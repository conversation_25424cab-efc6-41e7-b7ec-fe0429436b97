/**
 * Created by 侯杏哲 on 2017/4/22  实现财务修改记录页的功能 。
 */
$(function(){
    var processId = GetUrlQuery("id")
    getUpdateDetail(processId)
    sphdSocket.subscribe('approvalResults', function(data){
        loading.close()
        var content = JSON.parse(data)
        var state = content.state
        bounce.cancel()
        layer.msg(content.content)

        if (Number(state) !== 4) {
            window.location.reload()
        }
    })
});

/* creator：张旭博，2017-06-27 17:25:06，获取修改记录每一条数据的详细信息 */
function getUpdateDetail(processId) {
    var data = {
        approvalProcessId: processId,
        session: ''
    }
    $.ajax({
        url:"../update/getUpdateDetail.do" ,
        data : {
            json: JSON.stringify(data)
        },
        success:function(data) {
            var mid = GetUrlQuery("mid")
            if (mid) {
                sphdSocket.send('updateUserSuspendMsg', {
                    userId: sphdSocket.user.userID,
                    messageId: mid
                })
            }
            console.log('getUpdateDetail recieved OK:' + data);
            //在页面获取审批状态值
            data = data.data

            //获取数据
            var approvalProcess = data["approvalProcess"];
            var oldDetail = data["oldDetail"];
            var newDetail = data["newDetail"];
            var source = data["source"];

            var approveState = approvalProcess.approveStatus


            //定义字符串
            var detailStr = ""; //右侧详细内容字符串
            var footerStr = ""; //底部审批按钮字符串
            var approveStr = "";//左侧审批记录字符串

            $("#approveChange").data("source", source)

            var fileStr = ""; // 凭证照片
            let oldImgs = data.oldBillImages || []
            let newImgs = data.newBillImageHistories || []
            // 比较两组 图片 是否 完全一样
            let isSame = true
            if(oldImgs.length === newImgs.length){
                oldImgs.forEach(function (oldImg) {
                    let url_i = oldImg.uplaodPath
                    let same_i = false
                    newImgs.forEach(function (newImg) {
                        let url_j = newImg.uplaodPath
                        if(url_j === url_i){
                            same_i = true
                        }
                    })
                    if (!same_i){
                        isSame = false
                    }
                })
            } else {
                isSame = false
            }
            let oldImgsStr = '';
            oldImgs.forEach(function (oldImg) {
                let url_i = oldImg.uplaodPath
                let fullpath = $.fileUrl + url_i;
                oldImgsStr +=
                    '<div class="cp_img_box">' +
                    '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                    '       <a class="ww" path="'+ url_i +'" onclick="seeOnline($(this))">预览</a>' +
                    '   </div>'+
                    '</div>';
            })
            let newImgsStr = '';
            if (!isSame){
                newImgs.forEach(function (newImg) {
                    let url_i = newImg.uplaodPath
                    let fullpath = $.fileUrl + url_i;
                    newImgsStr +=
                        '<div class="cp_img_box">' +
                        '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                        '       <a class="ww" path="'+ url_i +'" onclick="seeOnline($(this))">预览</a>' +
                        '   </div>'+
                        '</div>';
                })
                newImgsStr = `<span class="ty-color-red colorChange"> → </span> <div class="fixedBox"> ${ newImgsStr } </div>`;
            }

            //遍历数据来源（5种数据来源）
            switch (Number(source)) {
                //  1-数据来源为纯现金/银行转账
                case 1:
                    var accountType = "";
                    var bankStr = "";

                    //method：判断来源为现金或者现金支票
                    if (oldDetail.method == 5) {
                        accountType = '银行转账';
                        if (oldDetail.debit && Number(oldDetail.debit) > 0) {
                            bankStr = '<p class="procon"><span>转账银行：</span><span>' + oldDetail.accountBank + '</span>' + compareNO(oldDetail.accountBank, newDetail.accountBank) + '</p>';
                            if(oldDetail.subType == 6) { // 汇划费
                                bankStr = '<p class="procon"><span>开户行：</span><span>' + oldDetail.accountBank + '</span>' + compareNO(oldDetail.accountBank, newDetail.accountBank) + '</p>';
                            }
                        } else {
                            bankStr = '<p class="procon"><span>到账时间：</span><span>' + formatTime(oldDetail.receiveAccountDate) + '</span>' + compareNO(formatTime(oldDetail.receiveAccountDate), formatTime(newDetail.receiveAccountDate)) + '</p>' +
                                '<p class="procon"><span>收款银行：</span><span>' + oldDetail.accountBank + '</span>' + compareNO(oldDetail.accountBank, newDetail.accountBank) + '</p>';
                        }
                    } else if (oldDetail.method == 1) {
                        accountType = '现金';
                        bankStr = "";
                    }
                    if (oldDetail.genre == 5) {
                        if (newDetail.genre == 5) {
                            var oldGenre = oldDetail.categoryDesc;
                            var newGenre = newDetail.categoryDesc;
                        } else {
                            var oldGenre = oldDetail.categoryDesc;
                            var newGenre = C_genre(newDetail.genre);
                        }
                    } else {
                        if (newDetail.genre == 5) {
                            var oldGenre = C_genre(oldDetail.genre)
                            var newGenre = newDetail.categoryDesc;
                        } else {
                            var oldGenre = C_genre(oldDetail.genre)
                            var newGenre = C_genre(newDetail.genre);
                        }
                    }

                    //根据支出或收入拼接显示字符串（ 收入为0 ，支出大于0）
                    if (oldDetail.debit && Number(oldDetail.debit) > 0) {
                        if(oldDetail.subType == 6){ // 汇划费
                            fileStr = '<p class="procon"><span>凭证照片：</span></p><div  class="fixedBox" >' + oldImgsStr + '</div>' + newImgsStr  ;
                            detailStr = '<p class="procon"><span>数据类别：</span><span>支出</span></p>' +
                                '<p class="procon"><span>支出类别：</span><span>' + fotmatExpType(oldDetail.subType) + '</span> </p>' +
                                '<p class="procon"><span>支出日期：</span><span>' + (new Date(oldDetail.factDate).format("yyyy-MM-dd"))  + '</span>' + compareNO((new Date(oldDetail.factDate).format("yyyy-MM-dd")) , (new Date(newDetail.factDate).format("yyyy-MM-dd")) ) + '</p>' +
                                '<p class="procon"><span>用途/摘要：</span><span>' + oldDetail.summary + '</span>' + compareNO(oldDetail.summary, newDetail.summary) + '</p>' +
                                '<p class="procon"><span>支出金额：</span><span>' + oldDetail.debit + '</span>' + compareNO(oldDetail.debit, newDetail.debit) + '</p>' +
                                bankStr +
                                fileStr +
                                '<p class="procon"><span>备注：</span><span>' + (oldDetail.memo||'') + '</span>' + compareNO(oldDetail.memo, newDetail.memo) + '</p>' +
                                '<p class="hd"><span class="processId">' + processId + '</span><span class="oldDetailId">' + oldDetail.id + '</span><span class="newDetailId">' + newDetail.id + '</span></p>';

                        }
                        else if(oldDetail.subType == "5"){ // 税款
                            let bankO = oldDetail.method == "1" ? "现金支付" : `转账支付${ oldDetail.accountBank }`
                            let bankN = newDetail.method == "1" ? "现金支付" : `转账支付${ newDetail.accountBank }`
                            fileStr = '<p class="procon"><span>缴税凭证照片：</span></p><div  class="fixedBox" >' + oldImgsStr + '</div>' + newImgsStr  ;
                            bankStr = `<p class="procon"><span>支出方式：</span><span>${ bankO }</span>${compareNO(bankO, bankN) }</p>`
                            detailStr = `
                                <p class="procon"><span>税种：</span><span>${oldDetail.taxName}</span>${compareNO(oldDetail.taxName, newDetail.taxName) }</p>
                                <p class="procon"><span>税款所属时期：</span>
                                    <span>${ oldDetail.businessPeriodBegin } - ${ oldDetail.businessPeriodEnd }</span>
                                    ${ compareNO(
                                        (oldDetail.businessPeriodBegin)+ " - " + (oldDetail.businessPeriodEnd), 
                                        (newDetail.businessPeriodBegin)+ " - " + (newDetail.businessPeriodEnd))
                                    }</p>
                                <p class="procon"><span>实际缴纳日期：</span> 
                                    <span>${ new Date(oldDetail.factDate).format("yyyy-MM-dd") }</span>
                                    ${compareNO(new Date(oldDetail.factDate).format("yyyy-MM-dd"), new Date(newDetail.factDate).format("yyyy-MM-dd")) }</p>
                                <p class="procon"><span>实缴金额：</span>
                                    <span>${ oldDetail.debit }</span>${ compareNO(oldDetail.debit, newDetail.debit ) }</p>
                                    ${bankStr}
                                    ${fileStr}
                                <p class="procon"><span>申报记录：</span>
                                    <span>需申报的起止日期为${ new Date(oldDetail.beginDate).format("yyyy-MM-dd") }至${ new Date(newDetail.endDate).format("yyyy-MM-dd") }的申报记录</span>
                                    ${ compareNO(
                                    '需申报的起止日期为' + (new Date(oldDetail.beginDate).format("yyyy-MM-dd") )+ "至" + (new Date(oldDetail.endDate).format("yyyy-MM-dd") ) + '的申报记录',
                                    '需申报的起止日期为' + (new Date(newDetail.beginDate).format("yyyy-MM-dd") )+ "至" + (new Date(newDetail.endDate).format("yyyy-MM-dd") ) + '的申报记录')
                                }</p>
                                <p class="procon"><span>备注：</span><span>${oldDetail.memo}</span>${compareNO(oldDetail.memo, newDetail.memo) }</p>
                                <p class="hd"><span class="processId">${processId}</span><span class="oldDetailId">${ oldDetail.id }</span><span class="newDetailId">${ newDetail.id }</span></p>
                            `;

                        }
                        else{
                            fileStr = '<p class="procon"><span>发票图片：</span></p><div  class="fixedBox">' + oldImgsStr + '</div>' + newImgsStr  ;
                            detailStr = '<p class="procon"><span>数据类别：</span><span>支出</span></p>' +
                                '<p class="procon"><span>支出类别：</span><span>' + fotmatExpType(oldDetail.subType) + '</span> </p>' +
                                '<p class="procon"><span>票据日期：</span><span>' + ( new Date(oldDetail.billDate).format("yyyy-MM-dd") )  + '</span>' + compareNO(( new Date(oldDetail.billDate).format("yyyy-MM-dd") ), new Date(newDetail.billDate).format("yyyy-MM-dd") ) + '</p>' +
                                '<p class="procon"><span>用途：</span><span>' + oldDetail.purpose + '</span>' + compareNO(oldDetail.purpose, newDetail.purpose) + '</p>' +
                                '<p class="procon"><span>摘要：</span><span>' + oldDetail.summary + '</span>' + compareNO(oldDetail.summary, newDetail.summary) + '</p>' +
                                '<p class="procon"><span>票据数量：</span><span>' + oldDetail.billQuantity + '</span>' + compareNO(oldDetail.billQuantity, newDetail.billQuantity) + '</p>' +
                                fileStr +
                                '<p class="procon"><span>票据金额合计：</span><span>' + oldDetail.billAmount + '</span>' + compareNO(oldDetail.billAmount, newDetail.billAmount) + '</p>' +
                                '<p class="procon"><span>实际金额合计：</span><span>' + oldDetail.debit + '</span>' + compareNO(oldDetail.debit, newDetail.debit) + '</p>' +
                                '<p class="procon"><span>实际付款日期：</span><span>' + (new Date(oldDetail.factDate).format("yyyy-MM-dd"))  + '</span>' + compareNO((new Date(oldDetail.factDate).format("yyyy-MM-dd")) , (new Date(newDetail.factDate).format("yyyy-MM-dd")) ) + '</p>' +
                                '<p class="procon"><span>经手人：</span><span>' + oldDetail.auditorName + '</span>' + compareNO(oldDetail.auditorName, newDetail.auditorName) + '</p>' +
                                '<p class="procon"><span>支出方式：</span>' + accountType + '</p>' +
                                `<p class="procon"><span>收款单位：</span><span>${ formatStakeholderCategory( oldDetail.stakeholderCategory ) } - ${ oldDetail.oppositeCorp }</span>
                            ${ compareNO(`${ formatStakeholderCategory( oldDetail.stakeholderCategory ) } - ${ oldDetail.oppositeCorp }`, `${ formatStakeholderCategory( newDetail.stakeholderCategory ) } - ${ newDetail.oppositeCorp }`) }</p>`+

                                bankStr +
                                '<p class="procon"><span>备注：</span><span>' + oldDetail.memo + '</span>' + compareNO(oldDetail.memo, newDetail.memo) + '</p>' +
                                '<p class="hd"><span class="processId">' + processId + '</span><span class="oldDetailId">' + oldDetail.id + '</span><span class="newDetailId">' + newDetail.id + '</span></p>';

                        }

                    }
                    else {
                        detailStr = '<p class="procon"><span>数据类别：</span><span>收入</span></p>' +
                            '<p class="procon"><span>类别：    </span><span>' + oldGenre + '</span>' + compareNO(oldGenre, newGenre) + '</p>' +
                            '<p class="procon"><span>摘要：    </span><span>' + oldDetail.summary + '</span>' + compareNO(oldDetail.summary, newDetail.summary) + '</p>' +
                            '<p class="procon"><span>实际金额：    </span><span>' + oldDetail.credit + '</span>' + compareNO(oldDetail.credit, newDetail.credit) + '</p>' +
                            '<p class="procon"><span>发票金额：    </span><span>' + (oldDetail.billAmount || '') + '</span>' + compareNO(oldDetail.billAmount, newDetail.billAmount) + '</p>' +
                            '<p class="procon"><span>用途：    </span><span>' + oldDetail.purpose + '</span>' + compareNO(oldDetail.purpose, newDetail.purpose) + '</p>' +
                            '<p class="procon"><span>经手人：  </span><span>' + oldDetail.auditorName + '</span>' + compareNO(oldDetail.auditorName, newDetail.auditorName) + '</p>' +
                            '<p class="procon"><span>收入方式： </span>' + accountType + '</p>' +
                            '<p class="procon"><span>付款单位： </span><span>' + oldDetail.oppositeCorp + '</span>' + compareNO(oldDetail.oppositeCorp, newDetail.oppositeCorp) + '</p>' +
                            bankStr +
                            '<p class="procon"><span>备注：    </span><span>' + oldDetail.memo + '</span>' + compareNO(oldDetail.memo, newDetail.memo) + '</p>' +
                            '<p class="hd"><span class="processId">' + processId + '</span><span class="oldDetailId">' + oldDetail.id + '</span><span class="newDetailId">' + newDetail.id + '</span></p>';
                    }
                    break;

                //  2-内部非支出性转账
                case 2:

                    var accountType = "";       //转账种类
                    var payAccount = "";        //付款账户
                    var receiveAccount = "";    //收款账户

                    //根据method获取转账种类
                    var method = Number(oldDetail.method);
                    var checkNoStr = "";
                    if (method === 6) {
                        accountType = "存现金";
                        checkNoStr = "";
                    } else if (method === 7) {
                        accountType = "取现金";
                        oldDetail.chequeNo ? (checkNoStr = '<p class="procon"><span>支票号：</span><span>' + oldDetail.chequeNo + '</span>' + compareNO(oldDetail.chequeNo, newDetail.chequeNo) + '</p>') : '';
                    } else if (method === 8) {
                        accountType = "其他内部转账";
                        checkNoStr = "";
                    }

                    //获取新旧金额
                    if (oldDetail.debit && Number(oldDetail.debit) > 0) {
                        var oldMoney = oldDetail.debit;
                        var newMoney = newDetail.debit;
                    } else {
                        oldMoney = oldDetail.credit;
                        newMoney = newDetail.credit;
                    }

                    //获取付款账户和收款账户
                    if (oldDetail.credit == 0 || oldDetail.credit == '' || oldDetail.credit == null) {
                        payAccount = oldDetail.accountBank;
                        receiveAccount = oldDetail.oppositeAccount;
                    } else {
                        payAccount = oldDetail.oppositeAccount;
                        receiveAccount = oldDetail.accountBank;
                    }

                    //拼接显示字符串
                    detailStr = '<p class="procon"><span>项目：</span><span>内部非支出性转账</span></p>' +
                        '<p class="procon"><span>转账种类：</span><span>' + accountType + '</span></p>' +
                        '<p class="procon"><span>付款账户：</span><span>' + payAccount + '</span></p>' +
                        '<p class="procon"><span>收款账户：</span><span>' + receiveAccount + '</span></p>' +
                        checkNoStr +
                        '<p class="procon"><span>转账金额：</span><span>' + oldMoney + '</span>' + compareNO(oldMoney, newMoney) + '</p>' +
                        '<p class="procon"><span>业务发生日期：</span><span>' + formatTime(oldDetail.auditDate) + '</span>' + compareNO(formatTime(oldDetail.auditDate), formatTime(newDetail.auditDate)) + '</p>' +
                        '<p class="procon"><span>备注：</span><span>' + oldDetail.memo + '</span>' + compareNO(oldDetail.memo, newDetail.memo) + '</p>' +
                        '<p class="hd"><span class="processId">' + processId + '</span><span class="oldDetailId">' + oldDetail.id + '</span><span class="newDetailId">' + newDetail.id + '</span></p>';
                    break;

                //  4-收入承兑汇票和转账支票中的外部支票
                case 4:
                    let editType = oldDetail.id == newDetail.returnBill  //true: 修改信息，false: 更换票据。 更换票据的你可以直接对比票据id
                    var type = oldDetail.type;
                    var typestring = ''
                    if (type == 1) {
                        typestring = '转账支票'
                    } else if (type == 2) {
                        typestring = '承兑汇票'
                    }
                    let depositInfo = {}
                    if (Number(approveState) === 1){// - 提交 2-批准
                        depositInfo = oldDetail
                    } else {
                        depositInfo = newDetail
                    }
                    editType ?  '': newDetail = data.financeReturnNew
                    //拼接显示字符串（主要区分为最后存入银行，那部分有改动）
                    detailStr = '<p class="procon"><span>项目：</span><span>收入</span></p>' +
                        '<p class="procon"><span>类别：</span><span>' + C_genre(oldDetail.category) + '</span></p>' +
                        '<p class="procon"><span>摘要：</span><span>' + oldDetail.summary + '</span></p>' +
                        '<p class="procon"><span>实际金额：</span><span>' + oldDetail.amount + '</span></p>' +
                        '<p class="procon"><span>发票金额：</span><span>' + (oldDetail.billAmount || '') + '</span></p>' +
                        '<p class="procon"><span>用途：</span><span>' + (oldDetail.purpose || '') + '</span></p>' +
                        '<p class="procon"><span>经手人：</span><span>' + oldDetail.operatorName + '</span></p>' +
                        '<p class="procon"><span>收入方式：</span><span>' + typestring + '</span></p>';
                    if (type == 1) {
                        detailStr +=
                            '<p class="procon"><span>收到支票日期：</span><span>' + formatTime(oldDetail.receiveDate) + '</span>'+ (editType ? '': compareNO(formatTime(oldDetail.receiveDate), formatTime(newDetail.receiveDate))) +'</p>' +
                            '<p class="procon"><span>付款单位：</span><span>' + oldDetail.payer + '</span>'+ (editType ? '': compareNO(oldDetail.payer, newDetail.payer)) +'</p>' +
                            '<p class="procon"><span>支票号：</span><span>' + oldDetail.returnNo + '</span>'+ (editType ? '': compareNO(oldDetail.returnNo, newDetail.returnNo)) +'</p>' +
                            '<p class="procon"><span>支票到期日：</span><span>' + formatTime(oldDetail.expireDate) + '</span>'+ (editType ? '': compareNO(formatTime(oldDetail.expireDate), formatTime(newDetail.expireDate))) +'</p>' +
                            '<p class="procon"><span>出具支票单位：</span><span>' + oldDetail.originalCorp + '</span>'+ (editType ? '': compareNO(oldDetail.originalCorp, newDetail.originalCorp)) +'</p>' +
                            '<p class="procon"><span>出具支票银行：</span><span>' + oldDetail.bankName + '</span>'+ (editType ? '': compareNO(oldDetail.bankName, newDetail.bankName)) +'</p>' +
                            '<p class="procon"><span>财务经手人：</span><span>' + oldDetail.createName + '</span>'+ (editType ? '': compareNO(oldDetail.createName, newDetail.createName)) +'</p>' +
                            '<p class="procon"><span>录入时间：</span><span>' + formatTime(oldDetail.createDate) + '</span>'+ (editType ? '': compareNO(formatTime(oldDetail.createDate), formatTime(newDetail.createDate))) +'</p>';
                    } else if (type == 2) {
                        detailStr +=
                            '<p class="procon"><span>收到汇票日期：</span><span>' + formatTime(oldDetail.receiveDate) + '</span>'+ (editType ? '': compareNO(formatTime(oldDetail.receiveDate), formatTime(newDetail.receiveDate))) +'</p>' +
                            '<p class="procon"><span>付款单位：</s' +
                            'pan><span>' + oldDetail.payer + '</span>'+ (editType ? '': compareNO(oldDetail.payer, newDetail.payer)) +'</p>' +
                            '<p class="procon"><span>汇票号：</span><span>' + oldDetail.returnNo + '</span>'+ (editType ? '': compareNO(oldDetail.returnNo, newDetail.returnNo)) +'</p>' +
                            '<p class="procon"><span>汇票到期日：</span><span>' + formatTime(oldDetail.expireDate) + '</span>'+ (editType ? '': compareNO(formatTime(oldDetail.expireDate), formatTime(newDetail.expireDate))) +'</p>' +
                            '<p class="procon"><span>原始出具汇票单位：</span><span>' + oldDetail.originalCorp + '</span>'+ (editType ? '': compareNO(oldDetail.originalCorp, newDetail.originalCorp)) +'</p>' +
                            '<p class="procon"><span>出具汇票银行：</span><span>' + oldDetail.bankName + '</span>'+ (editType ? '': compareNO(oldDetail.bankName, newDetail.bankName)) +'</p>' +
                            '<p class="procon"><span>录入时间：</span><span>' + formatTime(oldDetail.createDate) + '</span>'+ (editType ? '': compareNO(formatTime(oldDetail.createDate), formatTime(newDetail.createDate))) +'</p>';
                    }
                    var o_bankStr = oldDetail.saveBankName + ' ' + oldDetail.accout;
                    var n_bankStr = newDetail.saveBankName + ' ' + newDetail.accout;
                    //let able = $.trim(oldDetail.returnNo )==$.trim(newDetail.returnNo)
                   //detailStr +=
                   //    '<p class="procon"><span>备注：</span><span>' + (oldDetail.memo || '') + '</span></p>' +
                   //    '<p class="procon"><span>存入银行：</span><span>' + o_bankStr + '</span>' + (!able? '':compareNO(o_bankStr, n_bankStr)) + '</p>' +
                   //    '<p class="procon"><span>存入时间：</span><span>' + formatTime(oldDetail.depositDate) + '</span>' + (!able? '':compareNO(formatTime(oldDetail.depositDate), formatTime(newDetail.depositDate))) + '</p>' +
                   //    '<p class="procon"><span>存入经手人：</span><span>' + oldDetail.depositorName + '</span>' + (!able? '':compareNO(oldDetail.depositorName, newDetail.depositorName)) + '</p>' +
                   //    '<p class="procon"><span>到账时间：</span><span>' + formatTime(oldDetail.receiveAccountDate) + '</span>' + (!able? '':compareNO(formatTime(oldDetail.receiveAccountDate), formatTime(newDetail.receiveAccountDate))) + '</p>' +
                   //    '<p class="hd"><span class="processId">' + processId + '</span><span class="oldDetailId">' + oldDetail.id + '</span><span class="newDetailId">' + newDetail.id + '</span></p>';

                     detailStr +=
                        '<p class="procon"><span>备注：</span><span>' + (oldDetail.memo || '') + '</span></p>' +
                        '<p class="procon"><span>存入银行：</span><span>' + (Number(approveState) === 1 ?o_bankStr: n_bankStr) + '</span>' + (editType && Number(approveState) === 1 ? compareNO(o_bankStr, n_bankStr) : '') + '</p>' +
                        '<p class="procon"><span>存入时间：</span><span>' + formatTime(depositInfo.depositDate) + '</span>' + (editType && Number(approveState) === 1 ? compareNO(formatTime(oldDetail.depositDate), formatTime(newDetail.depositDate)) : '') + '</p>' +
                        '<p class="procon"><span>存入经手人：</span><span>' + depositInfo.depositorName + '</span>' + (editType && Number(approveState) === 1 ? compareNO(oldDetail.depositorName, newDetail.depositorName) : '') + '</p>' +
                        '<p class="procon"><span>到账时间：</span><span>' + formatTime(depositInfo.receiveAccountDate) + '</span>' + (editType && Number(approveState) === 1 ? compareNO(formatTime(oldDetail.receiveAccountDate), formatTime(newDetail.receiveAccountDate)) : '') + '</p>' +
                        '<p class="hd"><span class="processId">' + processId + '</span><span class="oldDetailId">' + oldDetail.id + '</span><span class="newDetailId">' + newDetail.id + '</span></p>';
                    break;

                //  5-数据来源为支出中的转账支票(内部的)
                case 5:
                    let oldAccountBill =  data["oldAccountBill"];
                    //拼接显示字符串
                    fileStr = '<p class="procon"><span>发票图片：</span></p><div class="fixedBox">' + oldImgsStr + '</div>' + newImgsStr ;
                    detailStr = '<p class="procon"><span>数据类别：</span><span>支出</span></p>' +
                        '<p class="procon"><span>支出类别：</span><span>' + fotmatExpType(oldAccountBill.subType) + '</span> </p>' +
                        '<p class="procon"><span>票据日期：</span><span>' + ( new Date(oldAccountBill.billDate).format("yyyy-MM-dd") )  + '</span>' + compareNO(( new Date(oldAccountBill.billDate).format("yyyy-MM-dd") ), new Date(newDetail.billDate).format("yyyy-MM-dd") ) + '</p>' +
                        '<p class="procon"><span>用途：</span><span>' + oldDetail.purpose + '</span>' + compareNO(oldDetail.purpose, newDetail.purpose) + '</p>' +
                        '<p class="procon"><span>摘要：</span><span>' + oldDetail.summary + '</span>' + compareNO(oldDetail.summary, newDetail.summary) + '</p>' +
                        '<p class="procon"><span>票据数量：</span><span>' + oldDetail.billQuantity + '</span>' + compareNO(oldDetail.billQuantity, newDetail.billQuantity) + '</p>' +
                        fileStr +
                        '<p class="procon"><span>票据金额合计：</span><span>' + oldDetail.billAmount + '</span>' + compareNO(oldDetail.billAmount, newDetail.billAmount) + '</p>' +
                        '<p class="procon"><span>实际金额合计：</span><span>' + oldDetail.amount + '</span>' + compareNO(oldDetail.amount, newDetail.amount) + '</p>' +
                        '<p class="procon"><span>实际付款日期：</span><span>' + (new Date(oldAccountBill.factDate).format("yyyy-MM-dd"))  + '</span>' + compareNO((new Date(oldAccountBill.factDate).format("yyyy-MM-dd")) , (new Date(newDetail.factDate).format("yyyy-MM-dd")) ) + '</p>' +
                        '<p class="procon"><span>经手人：</span><span>' + oldDetail.financialHandling + '</span>' + compareNO(oldDetail.financialHandling, newDetail.financialHandling) + '</p>' +
                        '<p class="procon"><span>支出方式：</span><span>' + C_payway(oldDetail.type) + '</span>' + compareNO(C_payway(oldDetail.type), C_payway(newDetail.type)) + '</p>';

                    //拼接显示字符串（根据type显示不同内容[1-转帐支票(内部的)，2-现金汇票 3-承兑汇票(仅在此处用) 4-外部的转账支票(仅在此处用)]）
                    var type = newDetail.type;
                    if (type == 1) {
                        var obank = oldDetail.bankName + oldDetail.account;
                        var nbank = newDetail.bankName + newDetail.accout;
                        detailStr +=
                            '<p class="procon"><span>银行账户：</span><span>' + obank + '</span>' + compareNO(obank, nbank) + '</p>' +
                            '<p class="procon"><span>支票号：</span><span>' + oldDetail.chequeNo + '</span>' + compareNO(oldDetail.chequeNo, newDetail.billNo) + '</p>' +
                            '<p class="procon"><span>支票到期日：</span><span>' + formatTime(oldDetail.expireDate) + '</span>' + compareNO(formatTime(oldDetail.expireDate), formatTime(newDetail.expireDate)) + '</p>' +
                            '<p class="procon"><span>接收日期：</span><span>' + formatTime(oldDetail.receiveDate) + '</span>' + compareNO(formatTime(oldDetail.receiveDate), formatTime(newDetail.receiveDate)) + '</p>' +
                            '<p class="procon"><span>接收经手人：</span><span>' + oldDetail.receiver + '</span>' + compareNO(oldDetail.receiver, newDetail.receiver) + '</p>' +
                            '<p class="procon"><span>支付经手人：</span><span>' + oldDetail.operator + '</span>' + compareNO(oldDetail.operator, newDetail.operator) + '</p>' +
                            `<p class="procon"><span>收款单位：</span><span>${ formatStakeholderCategory( oldAccountBill.stakeholderCategory ) } - ${ oldAccountBill.oppositeCorp }</span>
                            ${ compareNO(`${ formatStakeholderCategory( oldAccountBill.stakeholderCategory ) } - ${ oldAccountBill.oppositeCorp }`, `${ formatStakeholderCategory( newDetail.stakeholderCategory ) } - ${ newDetail.oppositeCorp }`) }</p>`;

                    } else {
                        var obank = oldDetail.bankName + oldDetail.account;
                        detailStr += '<p class="procon"><span class="ty-color-green">银行账户：</span><span class="ty-color-green">' + obank + '</span></p>';
                        if (type == 2) {

                        } else if (type == 3) {
                            detailStr +=
                                '<p class="procon"><span class="ty-color-green">支票号：</span><span class="ty-color-green">' + oldDetail.chequeNo + '</span><span class="ty-color-red"> 汇票号：<span>' + newDetail.billNo + '</span></span></p>' +
                                '<p class="procon"><span class="ty-color-green">支票到期日：</span><span class="ty-color-green">' + formatTime(oldDetail.expireDate) + '</span> </p>';

                        } else if (type == 4) {
                            detailStr +=
                                '<p class="procon"><span class="ty-color-green">支票号：</span><span class="ty-color-green">' + oldDetail.chequeNo + '</span><span class="ty-color-red"> 支票号：<span>' + newDetail.billNo + '</span></span></p>' +
                                '<p class="procon"><span class="ty-color-green">支票到期日：</span><span class="ty-color-green">' + formatTime(oldDetail.expireDate) + '</span></p>';
                        }
                        var oldBill = data["oldAccountBill"];
                        detailStr +=
                            '<p class="procon"><span class="ty-color-green">接收日期：</span><span class="ty-color-green">' + formatTime(oldDetail.receiveDate) + '</span></p>' +
                            '<p class="procon"><span class="ty-color-green">接收经手人：</span><span class="ty-color-green">' + oldDetail.receiver + '</span></p>' +
                            '<p class="procon"><span class="ty-color-green">支付经手人：</span><span class="ty-color-green">' + oldDetail.operator + '</span></p>' +
                            `<p class="procon"><span>收款单位：</span><span>${ formatStakeholderCategory( oldBill.stakeholderCategory ) } - ${ oldBill.oppositeCorp }</span>
                            ${ compareNO(`${ formatStakeholderCategory( oldBill.stakeholderCategory ) } - ${ oldBill.oppositeCorp }`, `${ formatStakeholderCategory( newDetail.stakeholderCategory ) } - ${ newDetail.oppositeCorp }`) }</p>`
                        ;
                    }


                    detailStr +=
                        '<p class="procon"><span>备注：</span><span>' + oldDetail.memo + '</span>' + compareNO(oldDetail.memo, newDetail.memo) + '</p>' +
                        '<p class="hd"><span class="processId">' + processId + '</span><span class="oldDetailId">' + oldDetail.id + '</span><span class="newDetailId">' + newDetail.id + '</span></p>';
                    break;
            }
            var process = [];
            process.push(approvalProcess)
            console.log(process)
            var nowProcessItem = process.find(value => value.toUser === sphdSocket.user.userID)
            //判断权限，只有超管能够审批
            if (nowProcessItem && nowProcessItem.approveStatus == 1) {
                footerStr = '<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-state="1" onclick="approvalChangeConfirm($(this))">批准</span> ' +
                    '<span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" data-state="0" onclick="approvalChangeConfirm($(this))">驳回</span>';
            } else {
                footerStr = '';
            }

            //将拼接的字符串填入对应容器
            $("#approveChange .description").html(approvalProcess.description);
            $("#approveChange .infoCon").html(detailStr);
            $("#approveChange .handleBtn").html(footerStr);

            //拼接修改说明字符串
            var instructionStr = "";
            $("#approveChange .colorChange").each(function () {
                var key = $(this).prev().prev().html();
                key = key.split("：")[0];
                var oldVal = $(this).prev().html();
                var newVal = $(this).html().slice(3);
                if(key.indexOf('凭证照片') >0 || key.indexOf('发票图片') >0 || key.indexOf('缴税凭证照片') >0){
                    newVal = $(this).next().html();
                }
                instructionStr += '<span>“' + key + '” 由原来的 <span class="ty-color-green">“' + oldVal + '”</span> 改为了 <span class="ty-color-red">“' + newVal + '”</span></span><br>';
            });
            instructionStr = '<p class="procon"><span>修改说明：</span>' + instructionStr + '</p>';

            //拼接审批流程字符串

            if (approveState == 1) {
                approveStr = '<div class="infoList ty-process-item">' +
                    '<p><span class="dot dot-wait"></span>处理人：' + approvalProcess.userName + ' </p>' +
                    '</div>';
            } else if (approveState == 2) {
                approveStr = '<div class="infoList ty-process-item">' +
                    '<p><span class="dot"></span>处理人：' + approvalProcess.userName + '  </p>' +
                    '<p>' + formatTime(approvalProcess.createDate, true) + '</p>' +
                    '</div>';
            } else if (approveState == 3) {
                approveStr = '<div class="infoList ty-process-item">' +
                    '<p><span class="dot dot-no"></span>处理人：' + approvalProcess.userName + '  </p>' +
                    '<p>' + formatTime(approvalProcess.createDate, true) + '</p>' +
                    (approvalProcess.reason ? '<p>驳回理由：' + approvalProcess.reason + '</p>' : '') +
                    '</div>';
            }

            //插入修改说明
            $("#approveChange .infoCon .hd").before(instructionStr);

            //插入审批流程
            $("#approveChange #process").html(approveStr);
        }
    });
}

function formatStakeholderCategory(cat) {
    cat = Number(cat);
    let str = ''
    switch (cat){
        case 1 : str = '供应商'; break;
        case 2 : str = '公司职工'; break;
        case 3 : str = '财务自行录入的收款单位（含个人）'; break;
    }
    return str
}
function fotmatExpType(type){
    type = Number(type)
    let str = ''
    switch (type){
        case 1:
            str = '本人的公务支出（财务费用除外、需入库物品的报销除外）'
            break;
        case 2:
            str = '其他同事的公务支出（财务费用除外，需入库物品的报销除外）'
            break;
        case 3:
            str = '需入库物品的报销'
            break;
        case 4:
            str = '社保/公积金'
            break;
        case 5:
            str = '税款'
            break;
        case 6:
            str = '汇划费'
            break;
        case 7:
            str = '其他财务费用的支出'
            break;
        case 8:
            str = '以上类别以外的支出'
            break;

    }
    return str
}
function approvalChangeConfirm(obj) {
    bounce.show($("#confirm"))
    var state = obj.data('state')
    $("#confirm").data('state', state)
    if (state === 1) {
        $("#confirm .agree").show().siblings().hide()
        $("#confirm .textMax").hide()
    } else {
        $("#confirm .reject").show().siblings().hide()
        $("#confirm .reject textarea").val("")
        $("#confirm .textMax").show()
        $("#confirm .textMax").html(50 + '/' +50)
    }
}

/* creator：张旭博，2017-06-27 20:39:14，审批修改数据（source：数据来源;state：1-批准 2-驳回） */
function approvalChange() {
    loading.open()
    var state = $("#confirm").data('state');
    var dataObj = $("#approveChange .infoCon>.hd");
    var approvalProcesId = dataObj.children(".processId").text();   //审批过程id
    var data = {
        'session':sphdSocket.sessionid,
        'userId': sphdSocket.user.userID,
        "state":state,                          //1-批准 0-驳回
        "approvalProcessId":approvalProcesId     //审批过程id
    };
    if (Number(state) === 0) {
        data.reason = $("#confirm .reject textarea").val()
    } else {
        data.reason = ''
    }
    var source =$("#approveChange").data("source")
    var url = ''
    //根据数据来源调取不同的接口
    switch (source){
        //1-数据来源为纯现金/银行转账
        case 1:
            url = "approvalCash";
            break;
        //2-内部非支出性转账
        case 2:
            url = "approvalTransferAccounts";
            break;
        //3-数据来源为报销
        case 3:
            url = "approvalPerReim";
            break;
        //4-承兑汇票和转账支票中的外部支票
        case 4:
            url = "approvalReturn";
            break;
        //5-数据来源为支出中的转账支票(内部的)
        case 5:
            url = "approvalBankTransfer";
            break;
    }
    console.log('param:' + JSON.stringify(data))
    sphdSocket.send(url,data);

    // $.ajax({
    //     url : url,
    //     data : data ,
    //     type:"post" ,
    //     dataType:"json" ,
    //     success:function(data){
    //         var status = data["status"];
    //         if(status == 1) {
    //             bounce.cancel();
    //             $("#tip #mt_tip_ms").html("操作成功！");
    //             bounce.show($("#tip"));
    //             $(".ty-secondTab li.ty-active").click();
    //         }else if(status == 2){
    //             bounce.cancel();
    //             $("#tip #mt_tip_ms").html("余额不足！");
    //             bounce.show($("#tip"));
    //         }else if(status == 0){
    //             bounce.cancel();
    //             $("#tip #mt_tip_ms").html("操作失败！");
    //             bounce.show($("#tip"));
    //         }
    //     },
    //     error:function(){
    //         $("#tip #mt_tip_ms").html("系统错误，请重试！");
    //         bounce.show($("#tip"));
    //     }
    // }) ;
}

/* creator：张旭博，2017-06-27 20:42:45，比较源数据和修改后的数据，相同显示旧数据，不同显示红箭头和新数据 */
function compareNO(o,n){
    if($.trim(o)==$.trim(n)){
        return '';
    }else{
        return '<span class="ty-color-red colorChange"> → '+n+'</span>';
    }
}

/* creator：张旭博，2017-06-27 20:43:42，转换票据所属月份 */
function C_billPeriod(billPeriod){
    if(billPeriod == 1){
        return '本月票据';
    }else if(billPeriod == 2){
        return '非本月票据';
    }
}

/* creator：张旭博，2017-06-27 20:44:00，转换票据类型 */
function C_genre(genre){
    switch(Number(genre)){
        case 1:
            return "货款";
            break;
        case 2:
            return "借款";
            break;
        case 3:
            return "投资款";
            break;
        case 4:
            return "废品";
            break;
        case 5:
            return "其他";
            break;
        default:
            return "";
            break;
    }
}

function C_payway(type) {
    switch(Number(type)){
        case 1:
            return "内部转账支票";
            break;
        case 2:
            return "现金汇票";
            break;
        case 3:
            return "承兑汇票";
            break;
        case 4:
            return "外部转账支票";
            break;
        default:
            return "";
            break;
    }
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.siblings(".textMax").text((max - curLength) +'/' + max );
}

// 时间控件的初始化
;!function(){
    laydate.render({elem: '#queryBeginTime'});
    laydate.render({elem: '#queryEndTime'});
}();


