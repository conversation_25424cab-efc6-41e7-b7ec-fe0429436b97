/**
 * Created by Administrator on 2017/11/13 0013.
 */
//creator:liyuting date:2018/1/25 保存是否有基本户
var operationState = ""; //1：有基本户 0：没有基本户
//update:liyuting date:2018/1/26  非基本户时，可否取现更改为只能选不可取现且不可修改
let matchType = ``, useInfo = ``;
$(function () {
    useInfo = auth.getOrg() //localStorage.getItem('org');
    $(".entrySect").hide();
    operationState = useInfo.fullName;
    getOrgSonOrgs();
    getfinanceAccounts();
    //creator:liyuting date:2018/11/29 对公户/非对公户按钮初始化
    $("#operationRadio .ty-radio").on("click",function () {
        if($(this).attr("value") === "对公户"){
            $.ajax({
                url:'../account/getBaseAccount.do',
                success:function (msg) {
                    var state = msg["status"];
                    var buildState = msg['res'];//建账完成状态
                    if(state=='0' && useInfo.orgType === 1){//没有基本户是4 就是子机构， 是1 就是总机构
                        $("#baseAccount").removeClass("radioDisabled");
                        $("#baseAccount").attr("onclick","publicOption($(this))");
                    }else{
                        $("#baseAccount").removeAttr("onclick");
                        $("#baseAccount").addClass("radioDisabled");
                    }
                    if(buildState != "4"){
                        $(".balance").prop("disabled",false);
                    }else{
                        if (useInfo.orgType === 1) {
                            $(".balance").val("0.00").prop("disabled",true);
                        } else {
                            $(".balance").val("").prop("disabled",true);
                        }
                    }
                }
            });
            $(".entrySect").hide();
            $("#baseAccountSet").removeClass("hd");
            $("#baseAccountSet .ty-radio").children("i").attr("class","fa fa-circle-o");
            $("#new-finance #new-isPublic").val("1");
            $(".isPublic").val("1");
        }else if($(this).attr("value") === "非对公户"){
            $(".balance").prop("disabled",false);
            $("#baseAccountSet").addClass("hd");
            $(".entrySect").show();
            $(".accountName").val("").removeAttr("disabled").removeClass("radioDisabled");
            $("#select2Radio .ty-radio").eq(0).children("i").attr("class","fa fa-dot-circle-o");
            $("#select2Radio .ty-radio").eq(1).children("i").attr("class","fa fa-circle-o");
            $("#select2Radio input").val("1");
            $("#new-finance #new-isPublic").val("0");
            $(".isPublic").val("0");
            $("#new-finance #new-isBasic").val("0");
        }
        $(this).siblings().children("i").attr("class","fa fa-circle-o");
        $(this).children("i").attr("class","fa fa-dot-circle-o");
    });
});
//creator:liyuting date:2018/11/29 基本户/非基本户按钮点击事件
function publicOption(obj){
    $(".entrySect").show();
    $(".accountName").val(operationState).attr("disabled","disabled").addClass("radioDisabled");
    if(obj.attr("value") == "基本户"){
        $("#select2Radio .ty-radio").eq(0).children("i").attr("class","fa fa-dot-circle-o");
        $("#select2Radio .ty-radio").eq(1).children("i").attr("class","fa fa-circle-o");
        $("#select2Radio input").val("1");
        $("#new-finance #new-isBasic").val("1");
    }else{
        $("#select2Radio .ty-radio").eq(1).children("i").attr("class","fa fa-dot-circle-o");
        $("#select2Radio .ty-radio").eq(0).children("i").attr("class","fa fa-circle-o");
        $("#select2Radio input").val("2");
        $("#new-finance #new-isBasic").val("0");
    }
    obj.siblings().children("i").attr("class","fa fa-circle-o");
    obj.children("i").attr("class","fa fa-dot-circle-o");
}
//creator:liyuting date:2018/11/29 获取主页账户列表
function getfinanceAccounts(){
    let orgNameStr = ``, oid = null;
    if (matchType) {$("#searchOrg").val() !== "" ? oid = $("#searchOrg").val(): "";}
    $.ajax({
        url:'../account/getAllAccounts.do',
        data: {"oid": oid},
        beforeSend:function(){ loading.open()},
        success:function (data) {
            var accountsList = data["financeAccountList"];
            var finishState = data["res"];
            let pubNum = 0, baseNum = 0;
            if(accountsList.length>0){
                var list = '';
                for(var i=0;i<accountsList.length;i++){
                    if (matchType) {orgNameStr = `<td> ${handleNull(accountsList[i].orgName)} </td>`;}
                    var operation = '--';
                    if (accountsList[i].accountType !== 1) {
                        if (accountsList[i].isPublic == "0") {
                            operation = '非对公户';
                            pubNum++;
                        } else {
                            if (accountsList[i].isBasic == "1") {
                                operation = '对公户 基本户';
                            } else {
                                operation = '对公户 非基本户';
                            }
                            baseNum++;
                        }
                    }
                    list +=
                        '<tr id="'+ accountsList[i].id +'">' +
                        '<td>'+ accountsList[i].name +'</td>' +
                        '<td>'+ (accountsList[i].name === "备用金/现金账户" ? '--': accountsList[i].bankName) +'</td>' +
                        '<td>'+ (accountsList[i].name === "备用金/现金账户" ? '--': accountsList[i].account) +'</td>' +
                        '<td>'+ operation +'</td>' + orgNameStr +
                        '<td>'+ formatMoney(accountsList[i].balance) +'</td>' +
                        '<td>'+ (new Date(accountsList[i].createDate).format('yyyy-MM-dd')) +'</td>' +
                        '<td>'+ (accountsList[i].name === "备用金/现金账户" ? '--': scanState(accountsList[i].accountStatus)) +'</td>' +
                        '<td>';
                    if (accountsList[i].accountType === 1){
                        accountsList.finishState = finishState
                        list += '<span class="ty-color-blue" onclick="initFundEditBtn(1,$(this))">初始资金管理</span>';
                        /*if(finishState == "4" || (useInfo.orgType === 1 && useInfo.name !== accountsList[i].orgName)){
                            list +=
                                '   <button class="ty-btn ty-btn-gray" disabled>修改</button>';
                        }else{
                            list +=
                                '   <button class="ty-btn ty-btn-blue ty-circle-3" onclick="accountUpdate(1,$(this))">修改</button>';
                        }
                        list +=
                            '   <button class="ty-btn ty-btn-blue ty-circle-3" onclick="recordDetail($(this),1)">修改记录</button>' +
                            '</td>' ;*/
                    } else {
                        list +=
                            '   <button class="ty-btn ty-btn-blue ty-circle-3" onclick="checkAccountInfoBtn($(this))">查看</button>';
                        if(accountsList[i].accountStatus === 1 && useInfo.name === accountsList[i].orgName){//开启
                            list += '   <button class="ty-btn ty-btn-blue ty-circle-3" onclick="accountUpdate(2,$(this))">修改</button>';
                        }else{
                            list += '   <button class="ty-btn ty-btn-blue ty-circle-3" disabled>修改</button>';
                        }
                        list +=
                            '   <button class="ty-btn ty-btn-blue ty-circle-3" onclick="recordDetail($(this),2)">修改记录</button>';
                        if(accountsList[i].isPublic == "1" && accountsList[i].isBasic == "1" || (useInfo.orgType === 1 && useInfo.name !== accountsList[i].orgName)){
                            list +='   <button class="ty-btn ty-btn-gray">关闭</button>';
                        }else{
                            if(accountsList[i].accountStatus){//可以关闭
                                list +=`   <button class="ty-btn ty-btn-blue ty-circle-3" onclick="closeAccount($(this),0)">关闭</button>`;
                            }else{//可以启用
                                list +=`   <button class="ty-btn ty-btn-blue ty-circle-3" onclick="closeAccount($(this),1)">启用</button>`;
                            }
                        }
                    }
                    list +=
                        '<div class="hd">' +JSON.stringify(accountsList[i])+
                        '</div>'+
                        '</td>' ;
                }
                $(".accountsList tbody").html(list);
            }
            $(".countStr").html(`下表的银行账户中，对公户共${baseNum}个，非对公户共${pubNum}个`);
        },
        complete:function(){ loading.close() ;  }
    });
}
//create:李玉婷 2024-08-26 08：38 初始资金管理
function initFundEditBtn(type, obj){
    obj.parent().parent().siblings("tr").removeClass("accountActive");
    obj.parent().parent().addClass("accountActive");
    let info = JSON.parse(obj.siblings(".hd").html());
    if (info.finishState == "4" || (useInfo.orgType === 1 && useInfo.name !== info.orgName)) {
        $("#initFundBtn").removeClass("ty-btn-blue").removeAttr("onclick");
    } else {
        $("#initFundBtn").addClass("ty-btn-blue").attr("onclick", 'accountUpdate(1)');
    }
    $("#initAmount").html(info.initialAmount);
    bounce.show($("#fundManagement"));
}
// var isHave="";
//update:liyuting date:2017/1/26T
function newBank() {
    if (chargeRole("超管")) {
        $("#mtTip #mt_tip_ms").html("您没有此权限！");
        bounce.show($("#mtTip"));
        return false;
    }
    //初始化
    $("#new-finance .ty-radio").find("i").attr("class", "fa fa-circle-o");
    $(".entrySect").find("input").val("");
    $(".entrySect").hide();
    $("#baseAccountSet").addClass("hd");
    bounce.show($('#new-finance'));
    bounce.everyTime('0.5s', 'newFinance', function () {
        if ($("#new-finance .bankName").val() == "" || $("#new-finance .account").val() == "" || $("#new-finance .accountName").val() == "" || $("#new-finance .balance").val() == "") {
            $(".newFinanceBtn").prop("disabled", true);
        } else {
            $(".newFinanceBtn").prop("disabled", false);
        }
    })
    /*$.ajax({
        url:"../business/getBusiness.do" ,
        data: {} ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ;  } ,
        success:function (data) {
            // status 0-失败， 1-成功，2-冻结，3-已生成
            var org  = data["org"];
            isHave=org.state;
            $("#new-finance .form-con").children("input").val("");
            if(org.name === undefined || org.name === null){
                $("#new-finance .accountName").val("");
            }else{
                $("#new-finance .accountName").val(org.name);
                accontNameFlag=org.name;
            }
            bounce.show($('#new-finance'));
            bounce.everyTime('0.5s','newFinance',function (){
                if($("#bankName").val() == "" || $(".account").val() == "" ||$(".accountName").val() ==""){
                    $(".newFinanceBtn").prop("disabled",true);
                }else{
                    $(".newFinanceBtn").prop("disabled",false);
                }
            })
        },
        error:function () {
            $("#mt_tip_ms").html("连接错误，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;}
    })*/
}
/*
function tijiao(yanzhengid){
    $.ajax({
        url:'${pageContext.request.contextPath}/user/judgeWhetherThereIsAChildAccount.do?id='+yanzhengid,
        dataType:'Text',
        success:function(course){
            if (course=="yes"){
                alert("此账户下还有账户明细，所以不可直接进行删除");
            }else if(course=="no"){
                openWindow('${pageContext.request.contextPath}/user/applicationForBankDelCashAccount.do?id='+yanzhengid,'1300','500');
            }
        }
    })
}
function quetijiao() {
    if(confirm("确认要提交解冻申请吗")){
        $("#yinchang").submit();
    }
}
*/
//update:liyuting date:2018/11/30 查看
function checkAccountInfoBtn(selector) {
    selector.parent().parent().siblings("tr").removeClass("accountActive");
    selector.parent().parent().addClass("accountActive");
    getAccountDetail();
    bounce.show($("#checkAccount"));
}
function getAccountDetail(){
    var accountId = $(".accountActive").attr("id");
    $(".checkRecord tbody").html("");
    $.ajax({
        url:"../account/getAccountDetail.do" ,
        data: { "accountId" : accountId } ,
        beforeSend:function(){ loading.open() ;  } ,
        success:function (data) {
            // status 0-失败， 1-成功，2-冻结，3-已生成
            var financeAccount = data["account"];
            if(financeAccount !== undefined){
                var hashSet = data["financeAccountRecords"];
                var option = '';
                if(financeAccount.isPublic == "0"){
                    option = '非对公户 可取现';
                }else{
                    if(financeAccount.isBasic == "0"){
                        option = '对公户 非基本户 不可取现';
                    }else{
                        option = '对公户 基本户 可取现';
                    }
                }
                $("#checkAccount .check_accountName").html(financeAccount.name);
                $("#checkAccount .check_bankName").html(financeAccount.bankName);
                $("#checkAccount .check_account").html(financeAccount.account);
                $("#checkAccount .check_balance").html(formatMoney(financeAccount.balance));
                $("#checkAccount .check_operation").html(option);
                $("#checkAccount .check_cashable").html(chargeCashable(financeAccount.cashable));
                $("#checkAccount .check_memo").html(financeAccount.memo);
                $(".check_nowState").html(scanState(financeAccount.accountStatus));
                $(".check_balanceInit").html(formatMoney(financeAccount.initialAmount));
                $(".check_createDate").html(financeAccount.createDate);
                $(".check_createName").html(financeAccount.createName);
                if(hashSet.length > 0){
                    var hashHtml = '';
                    for(var r =0;r<hashSet.length;r++){
                        hashHtml +=
                            '<tr>' +
                            '    <td class="form-title">'+ scanState(hashSet[r].state) +'时间：</td>' +
                            '    <td class="form-con">' +
                            '        <span class="check_offDate">'+ hashSet[r].updateDate + '</span>' +
                            '    </td>' +
                            '</tr>' +
                            '<tr>' +
                            '    <td class="form-title">操作者：</td>' +
                            '    <td class="form-con">' +
                            '        <span class="check_offPerson">'+ hashSet[r].updateName +'</span>' +
                            '    </td>' +
                            '</tr>';
                    }
                    $(".checkRecord tbody").html(hashHtml);
                }
            }
        },
        error:function () {
            $("#mt_tip_ms").html("连接错误，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    })
}
//update:liyuting date:2018/11/30 新建银行账户确定
function sureNewFinance() {
    var public = $("#new-finance #new-isPublic").val();
    var basic = $("#new-finance #new-isBasic").val();
    var accountName = $.trim($("#new-finance .accountName").val());
    var bankName = $.trim($("#new-finance .bankName").val());
    var account = $.trim($("#new-finance .account").val());
    var balance = $.trim($("#new-finance .balance").val());
    var cashable = $.trim($("#new-finance #cashable").val());
    var memo = $.trim($("#new-finance .memo").val());
    var pram = {
        "isPublic": public,
        "isBasic": basic,
        "name":accountName,
        "bankName":bankName,
        "account":account,
        "initialAmount":balance,
        "cashable":cashable,
        "memo":memo
    };
    if(bankName == ""){
        $("#tips .tipWord").html("请填写开户行!");
        bounce_Fixed.show($("#tips"));
    }else if(account == ""){
        $("#tips .tipWord").html("请填写账号!");
        bounce_Fixed.show($("#tips"));
    }else{
        $.ajax({
            url: '../account/addAccount.do',
            data: pram,
            beforeSend:function(){ loading.open()},
            success: function (data) {
                getfinanceAccounts();
                bounce.cancel();
            },
            complete:function(){ loading.close() ;  }
        });
    }
}
// creator:liyuting date:2018/11/29 银行账户列表修改
function accountUpdate(type, selector){
    if (chargeRole("超管")) {
        $("#mtTip #mt_tip_ms").html("您没有此权限！");
        bounce.show($("#mtTip"));
        return false;
    }
    if (type == 2) {
        selector.parent().parent().siblings("tr").removeClass("accountActive");
        selector.parent().parent().addClass("accountActive");
    }
    var accountId = $(".accountActive").attr("id");
    $.ajax({
        url: '../account/getAccountDetail.do',
        data: {"accountId":accountId},
        success: function (data) {
            var buildState = data["res"];
            if(type == 1) {
                if(buildState == "4"){
                    $("#mtTip #mt_tip_ms").html("建账完成不能修改余额。");
                    bounce.show($("#mtTip"));
                }else{
                    $("#updateBasic").val("");
                    bounce.show($('#basicAccountEdit'));
                }
            }else{
                var details = data["account"];
                var haveBase = data["status"];//判断是否有基本户
                if(details){
                    var isPublic = details["isPublic"];
                    var publicType = details["isBasic"];
                    var accountName = details["name"];
                    var bankName = details["bankName"];
                    var account = details["account"];
                    var balance = details["initialAmount"];
                    var memo = details["memo"];

                    $(".update_accountName").val(accountName);
                    $(".update_account").val(account);
                    $(".update_bankName").val(bankName);
                    $(".update_balance").val(balance);
                    $(".update_memo").val(memo);
                    if(isPublic == "1"){//对公户
                        $(".update_publicType span").eq(1).html("对公户");
                        $(".update_publicType input").val("1");
                        $("#operationBtn .ty-radio").on("click",function(){
                            $(this).siblings().children("i").attr("class","fa fa-circle-o");
                            $(this).children("i").attr("class","fa fa-dot-circle-o");
                            if($(this).attr("value") === "基本户"){
                                $("#editCaseRadio .ty-radio").eq(0).children("i").attr("class","fa fa-dot-circle-o");
                                $("#editCaseRadio .ty-radio").eq(1).children("i").attr("class","fa fa-circle-o");
                                $("#editCaseRadio input").val("1");
                                $("#operationBtn input").val("1");
                            }else{
                                $("#editCaseRadio .ty-radio").eq(1).children("i").attr("class","fa fa-dot-circle-o");
                                $("#editCaseRadio .ty-radio").eq(0).children("i").attr("class","fa fa-circle-o");
                                $("#editCaseRadio input").val("2");
                                $("#operationBtn input").val("0");
                            }
                        });
                        if(publicType == "1"){
                            $("#operationBtn").removeClass("radioDisabled");
                            $("#operationBtn .ty-radio").eq(0).click();
                        }else{
                            $("#operationBtn .ty-radio").eq(1).click();
                            if(haveBase){
                                $("#operationBtn").addClass("radioDisabled");
                                $("#operationBtn .ty-radio").off("click");
                            }
                        }
                        if(buildState == '4'){
                            $(".update_balance").prop("disabled",true);
                        }else{
                            $(".update_balance").prop("disabled",false);
                        }
                        $(".update_accountName").prop("disabled",true);
                        $("#public-select").show();
                    }else{
                        $(".update_balance").prop("disabled",false);
                        $(".update_publicType span").eq(1).html("非对公户");
                        $("#editCaseRadio .ty-radio").eq(0).children("i").attr("class","fa fa-dot-circle-o");
                        $("#editCaseRadio .ty-radio").eq(1).children("i").attr("class","fa fa-circle-o");
                        $(".update_accountName").prop("disabled",false);
                        $("#operationBtn input").val("0");
                        $("#public-select").hide();
                    }
                    bounce.show($("#accountEdit"));
                }
            }
        }
    })
    bounce.everyTime(0.5,"updateAccount",function(){
        if($(".update_accountName").val() == '' || $(".update_account").val() == '' ||$(".update_bankName").val() =='' ||$(".update_balance").val() == ''){
            $("#updateConfirm").prop("disabled",true);
        }else{
            $("#updateConfirm").prop("disabled",false);
        }
    })
}
// creator:liyuting date:2018/11/29  修改账户信息确定
function updateAccountSure(type){
    if (chargeRole("超管")) {
        $("#mtTip #mt_tip_ms").html("您没有此权限！");
        bounce.show($("#mtTip"));
        return false;
    }
    var selector = $(".accountActive").attr("id");
    var data = {};
    if(type == 1){
        var amount = $("#updateBasic").val();
        if(amount == ""){
            $("#tips .tipWord").html("请填写初始金额!");
            bounce_Fixed.show($("#tips"));
            return false;
        }
        data ={
            "accountId" : selector,
            "initialAmount":amount,
        }
    }else{
        var accountId = selector;
        var public = $("#update_publicType input").val();
        var basic = $("#operationBtn input").val();
        var name = $(".update_accountName").val();
        var bankName = $(".update_bankName").val();
        var account = $(".update_account").val();
        var initialAmount = $(".update_balance").val();
        var memo = $(".update_memo").val();
        data = {
            "accountId":accountId,
            "isPublic":public,
            "isBasic":basic,
            "name":name,
            "bankName":bankName,
            "account":account,
            "initialAmount":initialAmount,
            "memo":memo
        }
    }
    $.ajax({
        url: '../account/updateAccount.do',
        data: data,
        beforeSend:function(){ loading.open()},
        success: function (data) {
            var status = data["status"];
            if(status == "1"){
                bounce.cancel();
                getfinanceAccounts();
            }else if(status == "2"){
                $(".tipWord").html('账户中的余额比初始金额少，不够冲账.');
                bounce_Fixed.show($("#tips"));
            }
        },
        error: function () {
            $("#mtTip #mt_tip_ms").html("修改失败，请刷新重试！");
            bounce.show($("#mtTip"));
        }
    })
}
// creator:李玉婷 date:2024/10/10 银行账户-备用金-查看
function initFundRecordDetail(type){
    recordDetail(null,type)
}
// creator:liyuting date:2018/9/20 银行账户-非备用金修改记录列表-查看
function recordDetail(obj,type){
    var accountId = '';
    if (obj) {
        accountId = obj.parents("tr").attr("id");
    } else {
        accountId = $(".accountActive").attr("id");
    }
    $.ajax({
        url: "../account/getAccountHistories.do",
        data: {
            "accountId": accountId,
        },
        success: function (data) {
            var record = data["financeAccountHistoryDtos"];
            var htmlStr = '';
            if(record && record.length >0){
                if(type == "1"){
                    for(var a =0;a<record.length;a++){
                        htmlStr +=
                            '<tr>' +
                            '    <td>'+ new Date(record[a].updateDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                            '    <td>'+ formatMoney(record[a].revisedBeforeAmount) +'</td>' +
                            '    <td>'+ formatMoney(record[a].initialAmount) +'</td>' +
                            '    <td>'+ record[a].updateName +'</td>' +
                            '</tr>';
                    }
                    $("#seeAccountEditRecord tbody").html(htmlStr);
                    bounce.show($("#seeAccountEditRecord"));
                }else{
                    for(var a=0;a<record.length;a++){
                        htmlStr +=
                            '<tr>' +
                            '    <td>'+ new Date(record[a].updateDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                            '    <td>'+ record[a].updateName +'</td>' +
                            '    <td><span class="ty-color-blue" onclick="accountHistoryDetail('+ record[a].id +')">查看</span></td>' +
                            '</tr>';
                    }
                    $("#accountEditRecord tbody").html(htmlStr);
                    bounce.show($("#accountEditRecord"));
                }
            }
        }
    })
}
//creator:liyuting date:2018/12/05  非备用金修改记录查看
function accountHistoryDetail(recordId){
    $.ajax({
        url: "../account/getAccountHistoryDetail.do",
        data: {
            "accountHistoryDetailId": recordId,
        },
        success: function (data) {
            var newHistory = data["revisedAccountHistory"];
            var oldHistory = data["beforeAccountHistory"];
            var html =
                '<tr>' +
                '    <td class="line-title">账户类型</td>';
                if(newHistory.isPublic == "1"){
                    html +=
                        '    <td class="line-con">' +
                        '        <span>' +
                        '            对公户' +
                        '        </span>' +
                        '    </td>' +
                        '</tr>';
                    if(oldHistory.isBasic == newHistory.isBasic){
                        html +=
                            '<tr id="histroy-condation">' +
                            '    <td></td>' +
                            '    <td class="line-con">' +
                            '        <span>' + scanBasic(newHistory.isBasic) +
                            '        </span>' +
                            '    </td>'+
                            '</tr>';
                    }else{
                        html +=
                            '<tr><td></td><td class="line-con markRed">' +
                            '    <span>'+ scanBasic(oldHistory.isBasic) +'</span>' +
                            '    <i class="fa fa-long-arrow-right"></i>' +
                            '    <span>'+ scanBasic(newHistory.isBasic) +'</span>' +
                            '</td></tr>';
                    }
                }else{
                    html +=
                        '    <td class="line-con">' +
                        '        <span>' +
                        '            非对公户' +
                        '        </span>' +
                        '    </td>' +
                        '</tr>';
                }
                html +=
                    '<tr id="histroy-name">' +
                    '    <td class="line-title">账户名称</td>' + compareStr(oldHistory.name,newHistory.name) +
                    '</tr>'+
                    '<tr id="histroy-bankName">' +
                    '    <td class="line-title">开户行</td>' + compareStr(oldHistory.bankName,newHistory.bankName) +
                    '    </td>' +
                    '</tr>' +
                    '<tr id="histroy-account">' +
                    '    <td class="line-title">账号</td>' +compareStr(oldHistory.account,newHistory.account)+
                    '</tr>' +
                    '<tr id="histroy-amount">' +
                    '    <td class="line-title">初始资金</td>' +compareStr(formatMoney(oldHistory.initialAmount),formatMoney(newHistory.initialAmount)) +
                    '</tr>' +
                    '<tr id="histroy-memo">' +
                    '    <td class="line-title">备注</td>' +compareStr(oldHistory.memo,newHistory.memo) +
                    '</tr>';
                $("#editDetails tbody").html(html);
            bounce_Fixed.show($("#editDetails"));
        }
    })
}
//creator:liyuting date:2018/12/3 关闭账户
function closeAccount(selector,state){
    if (chargeRole("超管")) {
        $("#mtTip #mt_tip_ms").html("您没有此权限！");
        bounce.show($("#mtTip"));
        return false;
    }
    selector.parent().parent().siblings("tr").removeClass("accountActive");
    selector.parent().parent().addClass("accountActive");
    var org = $(".accountActive").attr("id");
    if(state =="1"){
        $("#close_tip_ms").html("您确定要开启该账户吗？");
        $(".canClose").removeClass("hd");
        $(".notClose").addClass("hd");
        $('.updateAccountState').attr("onclick","openOrCloseSure("+org+"," +state +")");
        bounce.show($("#closeTip"));
    }else{
        $.ajax({
            url: "../account/closingAccountJudge.do",
            data: {
                "accountId": org,
                "accountStatus": state       //accountStatus：账户状态（0-关闭 1-启用）；
            },
            success: function (data) {
                var accountStatus = data["status"];
                if(accountStatus == "0"){

                }else if(accountStatus == "1"){
                    $("#close_tip_ms").html("您确定要关闭该账户吗？");
                    $(".canClose").removeClass("hd");
                    $(".notClose").addClass("hd");
                    $('.updateAccountState').attr("onclick","openOrCloseSure("+org+"," +state +")");
                    bounce.show($("#closeTip"));
                }else if(accountStatus == "2"){
                    $("#close_tip_ms").html("本账户尚有余额，故不可关闭！");
                    $(".canClose").addClass("hd");
                    $(".notClose").removeClass("hd");
                    bounce.show($("#closeTip"));
                }else if(accountStatus == "3"){
                    $("#close_tip_ms").html("账户中有未完成审批的申请，故不可关闭！");
                    $(".canClose").addClass("hd");
                    $(".notClose").removeClass("hd");
                    bounce.show($("#closeTip"));
                }
            }
        })
    }
}

function openOrCloseSure(org,state){
    $.ajax({
        url: "../account/updateAccountStatus.do",
        data: {
            "accountId": org,
            "accountStatus": state       //accountStatus：账户状态（0-关闭 1-启用）；
        },
        success: function (data) {
            if(data.status == "1"){
                /*if(state == "0"){//关闭
                    $(".accountActive").children().eq(6).html("关闭");
                    $(".accountActive").children().eq(7).find("button").eq(1).prop("disabled",true);
                    $(".accountActive").children().eq(7).find("button").eq(3).attr("onclick","closeAccount($(this),1)").html("启用");
                }else{
                    $(".accountActive").children().eq(6).html("开启");
                    $(".accountActive").children().eq(7).find("button").eq(1).prop("disabled",false);
                    $(".accountActive").children().eq(7).find("button").eq(3).attr("onclick","closeAccount($(this),0)").html("关闭");
                }
                bounce.cancel();*/
                getfinanceAccounts();
                bounce.cancel();
            }
        }
    });
}
// -------------------多点点-分支机构-------------------
function screenOrgData(obj){
    getfinanceAccounts();
}


//creator:liyuting date:2022/08/12 可选择的机构列表
function getOrgSonOrgs(){
    $.ajax({
        async: false,
        url:'../sonOrg/getOrgSonOrgs.do',
        success:function(data){
            let list = data.data;
            let options = `<option value="">请选择机构</option>`;
            if (list && list.length > 0) {
                for(var i=0;i<list.length;i++) {
                    options += `<option value="${list[i].id}">${list[i].name}</option>`;
                }
            }
            if (useInfo.orgType === 1 && list.length > 1){//orgType 是4 就是子机构， 是1 就是总机构
                matchType = true;
                $(".screenBody").show();
                $(".belongOrg").show();
            } else {
                matchType = false;
                $(".screenBody").hide();
                $(".belongOrg").hide();
            }
            $("#searchOrg").html(options);
        }
    })
}
     //-----------辅助函数-------
// creator:liyuting date:2018/2/8 输入小数或整数
function clearNoNumO(obj){
    obj.value = obj.value.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符
    obj.value = obj.value.replace(/^0{2,}/g,"0");//只保留第一个0 清除多余的0
    //obj.value = obj.value.replace(/^0\d*/g,"");
    obj.value = obj.value.replace(/^\./g,"");  //验证第一个字符是数字而不是.
    obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的.
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    obj.value = obj.value.replace(/^0\d/g,"0");
}
//  输入整数
function clearNumO(obj){
    obj.value = obj.value.replace(/[^\d]/g,"");  //清除“数字”和“.”以外的字符
    obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的.
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
}
function scanState(val){
    if( val == true){ return "启用" };
    if( val == false){ return "关闭" };
    return "";
}
function scanBasic(val){
    if( val == 0 || val == "0"){ return "非基本户" };
    if( val == 1 || val == "1"){ return "基本户" };
    return "";
}
function chargeCashable(cashable) {
    if(cashable === 1){
        return "可取现";
    }else{
        return "不可取现";
    }
}
//
function compareStr(a,b){//a=新,b=旧
    var str ='';
    if(a == b){
        str =
            '    <td class="line-con">' +
            '        <span>' + b +
            '        </span>' +
            '    </td>';
    }else{
        str=
            '<td class="line-con markRed">' +
            '    <span>'+ a +'</span>' +
            '    <i class="fa fa-long-arrow-right"></i>' +
            '    <span>'+ b +'</span>' +
            '</td>';
    }
    return str;
}








































