/* creator: 张旭博，2019-04-22 10:21:32 */
$(function () {
	$("#newCostCategoryBtn").on("click", function () {
		bounce.show($("#newCostCategory"))
        $("#newCostCategory [name='costCategory']").val('')
        bounce.everyTime('0.5s','newCostCategory',function(){
            var state = 0;
            $("#newCostCategory").find("[require]:visible").each(function () {
                if($.trim($(this).val()) === ''){
                    state ++;
                }
            });

            if( state > 0 ){
                $("button[data-name='newCostCategory']").prop("disabled",true)
            }else {
                $("button[data-name='newCostCategory']").prop("disabled",false)
            }
        });
    })
    $(".bounce").on("click","button",function () {
        var name = $(this).data("name");
        switch (name) {
            // 新增费用类别 - 确定
            case 'newCostCategory':
                if (chargeRole('超管')) {
                	layer.msg("您没有此权限")
				} else {
                    var url = '', data = {}
                    var category = $("#newCostCategory [name='costCategory']").val()
                    var isMain = $(".main").is(":visible")
                    if(isMain){
                        url = '/reimburse/addCode.do'
                        data = { "name": category }
                    }else {
                        url = '/reimburseWindow/addSecondCode.do'
                        var pid = $(".second").data("pid")
                        data = {
                            "pid": pid,
                            "name": category
                        }
                    }

                    $.ajax({
                        url: url,
                        data: data,
                        success: function (data){
                            var status = data["status"];
                            if (status === 0) {
                                layer.msg("新增失败！");
                            }else if(status === 2){
                                layer.msg("费用类别重复，请更换类别名称！");
                            }else if(status === 1){
                                bounce.cancel();
                                layer.msg("新增成功！");
                                var code = data.code
                                if(isMain){
                                    var length = $(".main .fee tbody").find('tr').length
                                }else {
                                    var length = $(".second tbody").find('tr').length
                                }

                                var str = 	'<tr data-id="' + code.id + '">' +
                                    '<td>'+  (-(-length - 1)) +'</td>' +
                                    '<td class="categoryName">'+ code.name + '</td>' +
                                    '<td>' +
                                    '<span data-name="useState" type="1" class="ty-color-red btn">暂停使用</span>' +
                                    '</td>'+
                                    '</tr>';
                                if(isMain){
                                    $(".main .fee tbody").append(str);
                                }else {
                                    $(".second tbody").append(str);
                                }
                            }
                        }
                    })
				}
                break;
            case 'changeState':
                changeState()
                break;
        }
    })
	$(".ty-secondTab li").on("click", function () {
		var index = $(this).index()
		$(this).addClass('ty-active').siblings().removeClass('ty-active')
		$(".tblContainer").eq(index).show().siblings().hide()
		if(index === 0){
			getCostCategoryList()
            $(".costTip").show().siblings().hide();
            $("#newCostCategoryBtn").show()
		}else {
			getBillTypeList()
            $(".billTypeTip").show().siblings().hide();
            $("#newCostCategoryBtn").hide()
		}
    })

	$(".tblContainer").on("click", ".btn", function () {
        var name = $(this).data('name')
        var id = $(this).parents('tr').data('id')
        $(".tblContainer").data('id', id)
        switch (name) {
            case 'useState':
            	var type = $(this).attr("type")
				var categoryName = $(this).parents('td').siblings('.categoryName').html()
				$("#mtTip .tip").html('确定'+ (type === '0'? '恢复使用 ': '暂停使用 ') + '<b class="ty-color-blue">'+ categoryName+ '</b> 吗？')
				$("#mtTip").data('type', type)
				$("#mtTip").data('id', id)
				$("#mtTip").data('obj', $(this))
                bounce.show($("#mtTip"))
                break;
            case 'seeSecond':
                $(".second").show()
                $(".main").hide()
                var id = $(this).parents("tr").data("id")
                $(".second").data("pid",id)
                $.ajax({
                    url:'/reimburseWindow/getSecondCodeList.do',
                    data:{ id: id },
                    success:function(data){
                        var codeList = data
                        if(codeList){
                            var str = '';
                            for(var i in codeList){
                                let stateStr = ``;
                                var enabled = codeList[i].enabled
                                var cateStr =  enabled === 0 ? '<span data-name="useState" type="0" class="ty-color-blue btn">改变状态</span>' : '<span data-name="useState" type="1" class="ty-color-blue btn">改变状态</span>'
                                if (enabled === 1) {
                                    stateStr = `启用中`;
                                } else {
                                    stateStr = `已暂停使用`;
                                }
                                str += 	'<tr data-id="' + codeList[i].id + '">' +
                                    '<td>'+  (-(-i - 1)) +'</td>' +
                                    '<td class="categoryName">'+ codeList[i].name + '</td>' +
                                    '<td>' + cateStr + '</td>'+
                                    '</tr>';
                            }
                            $(".second tbody").html(str);
                        }
                    }
                })
                break


		}
    })
    getCostCategoryList()
})

// creator: 张旭博，2019-04-22 14:04:16，获取费用类别列表
function getCostCategoryList() {
    $.ajax({
        url:'../reimburse/toCode.do',
        data:{},
        success:function(data){
            var codeList = data.codeList
            if(codeList){
                var str = '';
                for(var i in codeList){
                    let stateStr = ``;
                    var orders = codeList[i].orders,
                        enabled = codeList[i].enabled
                    var cateStr = ''
                    if (orders === 10 || orders === 20) {
                        cateStr = '<span data-name="seeSecond" class="ty-color-blue btn">查看</span>'
                    } else {
                        cateStr =  enabled === 0 ? '<span data-name="useState" type="0" class="ty-color-blue btn">改变状态</span>' : '<span data-name="useState" type="1" class="ty-color-blue btn">改变状态</span>'
                    }
                    if (i < 2) {
                        stateStr = `--`;
                    } else {
                        if (enabled === 1) {
                            stateStr = `启用中`;
                        } else {
                            stateStr = `已暂停使用`;
                        }
                    }
                    str += 	'<tr data-id="' + codeList[i].id + '">' +
								'<td>'+  (-(-i - 1)) +'</td>' +
								'<td class="categoryName">'+ codeList[i].name + '</td>' +
								'<td>' + stateStr + '</td>'+
								'<td>' + cateStr + '</td>'+
							'</tr>';
                }
                $("#category").html(str);
            }
        }
    })
}

// creator: 张旭博，2019-04-22 14:12:10，获取票据种类列表
function getBillTypeList() {
    $.ajax({
        url:"../expense/queryCodeCategory.do",
        data:{},
        success:function(data) {
            var billCats = data.billCats
            var str = ''
            if (billCats) {
                for (var i in billCats) {
                    str += '<tr data-id="' + billCats[i].id + '">' +
                        '<td>' + (-(-i - 1)) + '</td>' +
                        '<td  class="billName">' + billCats[i].name + '</td>' +
                        '</tr>';
                }
                $("#billType").html(str);
            }
        }
    })
}

// creator: 张旭博，2019-04-23 09:40:22，恢复/暂停使用
function changeState() {
	var id = $("#mtTip").data('id')
	var type = parseInt($("#mtTip").data('type'))
	var obj = $("#mtTip").data('obj')
    $.ajax({
        url: "../reimburse/updateCode.do",
        data:{
        	id: id ,
            enabled: (type + 1)%2
		} ,
        success: function (data){
            var status = data.status;
            if (status && status !== 0) {
                layer.msg('操作成功！')
				if(type === 1){
                    obj.parent().html('<span data-name="useState" type="0" class="ty-color-green btn">恢复使用</span>')
				}else{
                    obj.parent().html('<span data-name="useState" type="1" class="ty-color-red btn">暂停使用</span>')
				}
                getCostCategoryList()
            }else{
                layer.msg('操作失败！')
			}
            bounce.cancel();

        },
        error:function (meg) {
            alert("连接错误，请稍后重试！");
        } ,
        complete:function(){ loading.close() ;  }
    })
}

function back() {
    $(".main").show()
    $(".second").hide()
}

