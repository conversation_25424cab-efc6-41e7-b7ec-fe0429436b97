
$(function(){
	bounce.cancel();
	$(".ty-secondTab li").on("click",function () {
		$(this).addClass("ty-active").siblings().removeClass("ty-active");
		var index = $(this).index();
		if(index === 0){
            getCategoryMes();//费用类别名称
            $("#category_addbtn").show();
		}else{
            getBillCatsMes();//票据种类
			$("#category_addbtn").hide();
		}
		$(".opinionCon").eq(index).show().siblings(".opinionCon").hide();
    })
	$(".ty-secondTab li").eq(0).click();

})
function getCategoryMes() {
	$.ajax({
		url:"../reimburse/toCode.do",
		data:{},
		type:"post",
		dataType:"json",
		beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
		success:function(data){
			var codeList=data["codeList"];
			if(codeList !=undefined && codeList.length > 0 ){
				var str = '';
				for(var i=0 ; i<codeList.length ; i++ ){
					var no = i+1;
					str += 	"<tr>" +
								"<td>"+  no +"</td>" +
								"<td>"+ codeList[i]["name"] + "</td>" +
								"<td class='cate_tab'>";
					 if(codeList[i]["enabled"] == 0){
						 str += "<span class='ty-color-blue' onclick='catagory_recover($(this))'>恢复使用</span>" ;
					 }else{
						 str +=	"<span class='ty-color-red' onclick='catagory_stop($(this))'>暂停使用</span>";
					 }
					str += 		"<div class='hd'>" +
									"<span class='cateroyId'>"+codeList[i]["id"]+"</span>" +
								"</div>" +
								"</td>"+
							"</tr>";
				}
                $("#category").html(str);
			}
		},
		error:function (meg) {
			alert("连接错误，请稍后重试！");
		} ,
		complete:function(){ chargeClose(1) ;  }
	})
}
function getBillCatsMes() {
	$.ajax({
		url:"../expense/queryCodeCategory.do",
		data:{},
		type:"post",
		dataType:"json",
		beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
		success:function(data){
			var billCats=data["billCats"];
			if(billCats !=undefined && billCats.length > 0 ){
				var str = '';
				for(var i=0 ; i<billCats.length ; i++ ){
					var no = i+1;
					str += "<tr>" +
						"<td>"+  no +"</td>" +
						"<td>"+ billCats[i]["name"] + "</td>" +
						"</tr>";
				}
                $("#billType").html(str);
			}
		},
		error:function (meg) {
			alert("连接错误，请稍后重试！");
		} ,
		complete:function(){ chargeClose( 2 ) ;  }
	})
}
//新增费用类别
function category_addbtn(){
    var userType = chargeRole("超管");
    if (userType) {
		$("#mt_tip_ms").html("您没有此操作权限！");
		bounce.show($("#mtTip"));
		return false ;
	}
	bounce.show($("#cata_addAccount"));
	$("#cateroy_name").val("");
}
//新增中的确定
/* updater：张旭博，2018-03-03 09:54:06，更改接口调用成功后的提示信息 */
function catagory_addsure() {
	var name = $("#cateroy_name").val();
	if( name==""){
		alert("类别名称不能为空！");
		return false;
	}
	$.ajax({
		url: "../reimburse/addCode.do",
		data:{ "name":name 	},
		type: "post",
		dataType: "json",
		beforeSend:function(){ loading.open() ;  } ,
		success: function (data){
			bounce.cancel();
			var status = data["status"];
			var code = data["code"];
			if (status === 0) {
				bounce.show($("#mtTip"));
				$("#mt_tip_ms").html("操作失败！");
			}else if(status === 2){
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("费用类别重复，请更换类别名称！");
			}else if(status === 1){
				layer.msg("新增成功！");
				$(".ty-secondTab li").eq(0).click();
			}
		},
		error:function (meg) {
			alert("连接错误，请稍后重试！");
		} ,
		complete:function(){ loading.close() ;  }
	}) ;
}
//新增类别中的关闭
function catagory_close(){
	bounce.cancel();
}
function catagory_cancel(){
	bounce.cancel();
}

//暂停使用按钮
var cateroyTrobj = null;
var catObj = null ; 
function catagory_stop(obj){
    catObj = obj;
    var userType = chargeRole("超管");
    if (userType) {
		$("#mt_tip_ms").html("您没有此操作权限！");
		bounce.show($("#mtTip"));
		return false ;   
	}
	var tr_obj = obj.parent().parent();
	cateroyTrobj = tr_obj ;
	var id = obj.siblings(".hd").children(".cateroyId").html();
	var name = obj.parent().prev().html();
	$("#mt_tip").html("确定 暂停使用 '"+ name +"' ?");
	$("#setType").html("0");
	$("#catID").html(id);
	bounce.show($("#confirm"));
}
//操作失败关闭
function tip_cancel() {
	bounce.cancel();
}
function catastop_close() {
	bounce.cancel();
}
function bounce_close() {
	bounce.cancel();
}
//恢复使用按钮
function catagory_recover(obj){
	catObj = obj ;
    var userType = chargeRole("超管");
    if (userType) {
		$("#mt_tip_ms").html("您没有此操作权限！");
		bounce.show($("#mtTip"));
		return false ;
	}
	var tr_obj = obj.parent().parent();
	cateroyTrobj = tr_obj ;
	var id = obj.siblings(".hd").children(".cateroyId").html();
	var name = obj.parent().prev().html();
	$("#mt_tip").html("确定 恢复使用 '"+ name +"' ?");
	$("#setType").html("1");
	$("#catID").html(id);
	bounce.show($("#confirm"));
	 
}
//删除提示
function catagory_del(obj){
	cateroyTrobj = obj.parent().parent() ;
	var id = obj.siblings(".hd").children(".cateroyId").html();
	if ( id == undefined || id == ""){  // 校验销售id 是否合法
		bounce.show($("#mtTip"));
		$("#mt_tip_ms").html("系统错误，刷新重试！"); // 不合法的提示信息赋值
		return false; // 结束
	}
	bounce.show($("#catagory_stopAccount"));
	var del_cateroyname = cateroyTrobj.children(":eq(1)").html();  // 获取类别名称
	var str = "确定删除费用类别名称为：" + del_cateroyname +" 的类别吗？";
	$("#cateroyDelTip").html(str); // 给提示信息位置赋值
	$("#del_cateroyID").html(id); //  给ID赋值

}
//删除中的确定
function catadel_sure(obj) {
	var id = $("#del_cateroyID").html();
	if ( id == undefined || id == ""){  // 校验id 是否合法
		bounce.show($("#mtTip"));
		$("#mt_tip_ms").html("系统错误，刷新重试！"); ///  不合法的提示信息赋值
		return false; // 结束
	}
	$.ajax({
		url:"",
		data:{ "id":id  },
		type:"post",
		dataType:"Json",
		beforeSend:function(){ loading.open() ;  } ,
		success:function (data) {
			var status = data["status"];
			if(status == 1 ){
				bounce.show($("#mtTip"));
				$("#mt_tip_ms").html("删除成功！");
				scanTrObj.remove();
			}else if( status == 0 ){
				bounce.show($("#mtTip"));
				$("#mt_tip_ms").html("删除失败！");
			}else{
				bounce.show($("#mtTip"));
				$("#mt_tip_ms").html("系统错误！");
			}
		},
		error: function (msg) {  // 失败以后的操作
			// console.log("连接错误，请稍后重试！");
			bounce.show($("#mtTip"));
			$("#mt_tip_ms").html("连接错误，请稍后重试！");
			return false;
		} ,
		complete:function(){ loading.close() ;  }
	});
}
//删除中的关闭
function catadel_close(){
	bounce.cancel();
}
function catadel_cancel(){
	bounce.cancel();
}
/* creator:侯杏哲，2017-2-16，确定修改费用类别状态  */
function setSatus(){
	var id = $("#catID").html() ;
	var type = $("#setType").html() ;
	$.ajax({
		url: "../reimburse/updateCode.do",
		data:{ "id":id , "enabled" : type } ,
		type: "post",
		dataType: "json",
		beforeSend:function(){ loading.open() ;  } ,
		success: function (data){
			var status = data["status"];
			if (status = 0 || status == "0") {
				bounce.show($("#mtTip"));
				$("#mt_tip_ms").html("操作失败！");
				return false;
			}
			bounce.cancel();
			if( type == 0){
				catObj.parent().html("<span class='ty-color-blue' onclick='catagory_recover($(this))'>恢复使用</span>	<div class='hd'>" +
					"<span class='cateroyId'>" + data["code"]["id"] + "</span>" +
					"</div>" );
			}else{
				catObj.parent().html( " <span class='ty-color-red' onclick='catagory_stop($(this))'>暂停使用</span><div class='hd'>" +
					"<span class='cateroyId'>" + data["code"]["id"] + "</span>" +
					"</div>"  );
			}
			
		},
		error:function (meg) {
			alert("连接错误，请稍后重试！");
		} ,
		complete:function(){ loading.close() ;  }
	})

}









