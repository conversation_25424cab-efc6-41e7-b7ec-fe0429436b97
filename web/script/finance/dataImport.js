/*
* creator 侯杏哲
* 创建完成  2016-01-12 
*/
var applyData = {} // 税款的日期区间信息

$(function () {
    $("#firstname").val('');
    $("#expend_oppcompany + .ty-optionCon").on("click",".ty-option",function () {
        $(this).parent().prev().val($(this).text());
        $(this).parent().prev().attr("id",$(this).attr("id"));
        $(this).parent().hide();
    })
    $("#expend_oppcompany").on("keyup",function () {
        var inputVal = $("#expend_oppcompany").val();
        var mtSuppliers = $("body").data("oppositeCorp");
        if(mtSuppliers.length > 0){
            $("#fixMsTips .msTip");
            var outerLiStr = "";
            if(inputVal !== ""){
                //输入值时将此值与所有数据比对，将含有此值的外部图号显示出来
                for(var j in mtSuppliers){
                    var mtSupplierName = mtSuppliers[j].name;
                    var choose = mtSupplierName.indexOf(inputVal);
                    if(choose != "-1"){
                        outerLiStr += '<li class="ty-option">'+mtSupplierName+'</li>';
                    }
                }

            }
            if(outerLiStr !== ""){
                $(".ty-optionCon").show();
                $(".ty-optionCon").html(outerLiStr);
                $(".ty-optionCon .ty-option").each(function () {
                    if($("#expend_oppcompany").val() == $(this).text()){
                        $(this).addClass("ty-optionActive");
                    }else{
                        $(this).removeClass("ty-optionActive");
                    }
                });
            }else{
                $(".ty-optionCon").hide();
            }
        }
    });
    $(".notTransfor").on("click",function () {
        $("#expend_oppcompany").next().hide();
    })
    $("#purpose").on("keyup",function () {
        $("#summaryBen2").val($(this).val());
    })
    setOppositeCorpCon();

    $("#stakeholderCategory option").click(function () {
		let val = $(this).val();
		if(val == 0){
			$("#oppositeCorp").val("");
			return false
		}
		bounce.show($("#oppositeCorpDiv"))
        $("#oppositeCorpDiv").data("type", val)
		let ttl = ""
		let list = ""
		let str = ""
		if(val == '1'){
			ttl = '选择供应商'
            $("#oppositeCorpDiv .bonceHead span").html(ttl)
            $.ajax({
				"url":"../po/suppliers?state=3&type=1",
				"data":{},
				success:function(res){
					list = res.data || [];
					str = ``
					list.forEach(function(item){
                        str += `
                        <li data-val="${item.id}">
                        	<i class="fa fa-circle-o"></i>
                        	<span>${item.full_name} ${ item.enabled ==0 ? '已暂停供货资格':'' }</span>
                        </li>
                        `
					})
					$("#chooseC").html(str);
				}
			})
		}else if(val == '2'){
            ttl = '选择公司职工'
            $("#oppositeCorpDiv .bonceHead span").html(ttl)
            $.ajax({
                "url":"../stakeholder/getUsers.do",
                success:function(res){
                    list = res.data.users || [];
                    str = ``
                    list.forEach(function(item){
                        str += `
                        <li data-val="${item.userID}">
                        	<i class="fa fa-circle-o"></i>
                        	<span>${item.userName}${ item.mobile } ${ item.isDuty ==2 ? '(已不在职)':'' }</span>
                        </li>
                        `
                    })
                    $("#chooseC").html(str);
                }
            })
		}else if(val == '3'){
            ttl = '选择其他单位或个人'
            $("#oppositeCorpDiv .bonceHead span").html(ttl)
            $.ajax({
                "url":"../stakeholder/getStakeholderList.do",
                success:function(res){
                    list = res.data.stakeholdersAvailable || [];
                    str = ``
                    list.forEach(function(item){
                        str += `
                        <li data-val="${item.id}">
                        	<i class="fa fa-circle-o"></i>
                        	<span>${item.name}</span>
                        </li>
                        `
                    })
                    $("#chooseC").html(str);
                }
            })
		}

    })
	$("#chooseC").on("click","li",function(){
        $("#chooseC i.fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).find("i").attr("class", "fa fa-dot-circle-o")
	})
})
// creator： 侯杏哲 2022-8-1 录入税款
function rateEntry() {
    applyData = {}
    $("#rateEntryFrm input").val("")
    $("#ratePic").html("")
    $("#rateApplyLog").html("")
    $("#rateEntryFrm select").val("0")
    $("#rateEntryFrm").data("type","add")
    bounce.show($("#rateEntryFrm"))
    getPatMethod($("#patMethod"))
    getRateList($("#rateCat"))
    uploadInit3();
    bounce.everyTime('0.2s',"getApplyTimer",function () {
        getRateApplyLog($("#rateApplyLog"))
    })
}
// creator： 侯杏哲 2022-8-1 录入 一条 确定
function rateAddtrok() {
    let rateCat = $("#rateCat").val()
    let rateCatName = $("#rateCat option:selected").html()
    let rateStartDate = $("#rateStartDate").val()
    let rateEndDate = $("#rateEndDate").val()
    let rateRealDate = $("#rateRealDate").val()
    let moneyReal = $("#moneyReal").val()
    let patMethod = $("#patMethod").val()
    let patMethodStr = $("#patMethod option:selected").html()
    let rateApplyLog = $("#rateApplyLog").val()
    let taxPeriodIdInsert =  $("#rateApplyLog option:selected").data("id")
    let startDate = $("#rateApplyLog option:selected").attr("startDate")
    let endDate = $("#rateApplyLog option:selected").attr("endDate")
    let rateApplyLogTxt = $("#rateApplyLog option:selected").html()
    let rateDateDurTxt = `${ (rateStartDate)}-${ (rateEndDate) }`
    let rateMemo = $("#rateMemo").val()
    let editType = $("#rateEntryFrm").data("type")

    if(rateCat== 0 || rateApplyLog== 0 || rateStartDate.length == 0 || rateEndDate.length == 0 || rateRealDate.length == 0 || moneyReal.length == 0 || patMethod.length == 0){
        layer.msg("请将必填项补充完整！");
        return false;
    }
    if( $("#taxTab tr:gt(0)").length > 0){
        let rateApplyLogSelected  = false
        if(editType == "add"){
            $("#taxTab tr:gt(0)").each(function () {
                let trInfo = $(this).find(".hd").html()
                trInfo = JSON.parse(trInfo)
                if(rateApplyLog == trInfo.report){
                    rateApplyLogSelected = true
                }
            })
        }else if(editType == "edit"){
            let editInfo = editObj.siblings(".hd").html();
            editInfo = JSON.parse(editInfo);
            $("#taxTab tr:gt(0)").each(function () {
                let trInfo = $(this).find(".hd").html()
                trInfo = JSON.parse(trInfo)
                if(editInfo.order != trInfo.order){ // 不是正在修改的那一行
                    if(rateApplyLog == trInfo.report){
                        rateApplyLogSelected = true
                    }
                }
            })
        }

        if(rateApplyLogSelected){
            layer.msg("该条申报记录已经选择过了，请不要重复选择！");
            return false;
        }
    }

    let imgPaths = ""
    let imgPathInfo = {}
    if($("#ratePic .cp_img_box").length > 0){
        $("#ratePic .cp_img_box").each(function () {
            let path = $(this).find(".ww").attr("path");
            let fileUid = $(this).find(".fileUid").html();
            imgPaths = path
            imgPathInfo = { path : path , fileUid: fileUid }
        })
    }
    let methodArr = patMethod.split("-")
    let method = methodArr[0]
    let financeAccountId = methodArr[1]

    let info = {
        "taxId": rateCat ,
        "taxCategoryName": rateCatName ,
        "businessPeriodBegin": rateStartDate,  //  税款所属时期开始时间(格式是yyyyMMdd)
        "businessPeriodEnd": rateEndDate ,  //  税款所属时期结束时间(格式是yyyyMMdd)
        "report": rateApplyLog ,         //    报表id
        "periodBegin": startDate ,         //    申报记录开始时间（yyyy-MM-dd）
        "periodEnd": endDate ,            //   申报记录结束时间（yyyy-MM-dd）
        "method": method ,               //    1-现金 5-银行转账
        "money": moneyReal ,
        "financeAccountId": financeAccountId ,     //     银行账户id
        "factDate": rateRealDate ,
        "imgPaths": imgPaths ,
        "memo": rateMemo ,
        "imgPathInfo": imgPathInfo ,
        "patMethodStr": patMethodStr ,
        "rateApplyLogTxt": rateApplyLogTxt ,
        "rateDateDurTxt": rateDateDurTxt ,
        "taxPeriodIdInsert":taxPeriodIdInsert
    }
    if(editType == "add"){
        info.order = $("#taxTab tr").length
    }else if(editType == "edit"){
        let editInfo = editObj.siblings(".hd").html();
        editInfo = JSON.parse(editInfo);
        info.order = editInfo.order
    }
    let trStr = `
        <td>${info.taxCategoryName}</td>
        <td>${info.rateDateDurTxt}</td>
        <td>${info.money}</td>
        <td>${ info.patMethodStr }</td>
        <td>
            <span class="ty-color-blue" onclick="editTax($(this))">修改</span>
            <span class="ty-color-red" onclick="delTax($(this))">删除</span>
            <span class="hd">${ JSON.stringify(info) }</span>
        </td>
    `
    if(editType == "add"){
        $("#taxTab tbody").append(`<tr>${ trStr }</tr>`);
    }else if(editType == "edit"){
        editObj.parents("tr").html(trStr)
    }
    $(".shuifee table").show()
    bounce.cancel();
}

// creator： 侯杏哲 2022-8-1 删除税款
function delTax(obj) {
    editObj = obj
    bounce.show($("#rateDel"))
}
function rateDelok() {
    editObj.parents("tr").remove();
    bounce.cancel()
}
// creator： 侯杏哲 2022-8-1 修改税款
var editObj = null
function editTax(obj) {
    editObj = obj
    applyData = {}
    $("#rateEntryFrm input").val("")
    $("#ratePic").html("")
    $("#rateEntryFrm select").val("0")
    $("#rateEntryFrm").data("type","edit")
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    getRateList($("#rateCat"), info.taxId);
    $("#rateStartDate").val(info.businessPeriodBegin)
    $("#rateEndDate").val(info.businessPeriodEnd)
    $("#rateRealDate").val(info.factDate)
    $("#moneyReal").val(info.money)
    getPatMethod($("#patMethod"), info.financeAccountId)
    let data = { "taxId":info.taxId , "startDate":info.businessPeriodBegin , "endDate":info.businessPeriodEnd  };
    getRateApplyLogAj(data, $("#rateApplyLog"), info.taxPeriodIdInsert)
    $("#rateMemo").val(info.memo)
    uploadInit3();
    if(info.imgPaths.length > 0){
        var fullpath = $.fileUrl + info.imgPaths ;
        var fileUid = info.imgPathInfo.fileUid ;
        var path = info.imgPaths ;
        var imgStr =
            '<div class="cp_img_box">' +
            '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
            '       <a class="ww" path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
            '       <span class="fa fa-times" onclick="delFile($(this))"></span>' +
            '       <a class="hd fileUid">'+ fileUid +'</a>' +
            '   </div>'+
            '</div>';
        $("#ratePic").html(imgStr)
    }
    bounce.show($("#rateEntryFrm"))
    bounce.everyTime('0.2s',"getApplyTimer",function () {
        getRateApplyLog($("#rateApplyLog"))
    })
}
// creator： 侯杏哲 2022-8-1 录入税款提交
function rateSubmit() {
    let trList = $("#taxTab tr:gt(0)")
    let taxCategorys = []
    if(trList.length > 0){
        trList.each(function () {
            let trInfo = $(this).find(".hd").html()
            trInfo = JSON.parse(trInfo)
            let imgPaths = trInfo.imgPaths? [trInfo.imgPaths] : []
            taxCategorys.push({
                "taxId": trInfo.taxId ,
                "taxCategoryName": trInfo.taxCategoryName ,
                "businessPeriodBegin": trInfo.businessPeriodBegin.replace(/-/g,""),  //  税款所属时期开始时间(格式是yyyyMMdd)
                "businessPeriodEnd": trInfo.businessPeriodEnd.replace(/-/g,"") ,  //  税款所属时期结束时间(格式是yyyyMMdd)
                "report": trInfo.report ,         //    报表id
                "periodBegin": trInfo.periodBegin ,         //    申报记录开始时间（yyyy-MM-dd）
                "periodEnd": trInfo.periodEnd ,            //   申报记录结束时间（yyyy-MM-dd）
                "method": trInfo.method ,               //    1-现金 5-银行转账
                "money": trInfo.money ,
                "financeAccountId": trInfo.financeAccountId ,     //     银行账户id
                "factDate": trInfo.factDate ,
                "imgPaths": imgPaths,
                "memo": trInfo.memo ,
                "taxPeriodIdInsert": trInfo.taxPeriodIdInsert ,

            })
        })
    }else{
        layer.msg("最少需录入一条数据！")
        return false
    }
    $.ajax({
        "url":"../data/debitTaxCategory.do",
        "data": { "taxCategorys": JSON.stringify(taxCategorys) },
        success:function(res){
            let data = res.data
            if(data){
                let status = data.status;
                let content = data.content;
                // status：1-操作成功 2-此账户已被冻结，不允许操作 4-余额不足
                // content：与status对应的描述

                layer.msg(content)
                $("#firstname").val("")
                $(".dataContainer").hide();
                $("#nav").children(".radioBtn").removeClass("radioActive");

            }else{
                let error = res.error
                layer.msg(error.message)
            }

        }
    })
}
// creator： 侯杏哲 2022-8-1 税种 更改判断
function rateCatChange(obj) {
    let rateCat = $("#rateCat").val();
    if(rateCat){
        let rateCatName = $("#rateCat option:selected").html()
        if(rateCatName == "个人所得税"){
            bounce_Fixed.show($("#changeTip"))
        }
    }
}

function changeTipCancel() {
    bounce_Fixed.cancel();$("#rateCat").val("0");
}

// creator： 侯杏哲 2022-8-1 获取支付方式
function getPatMethod(obj, selectID) {
    $.ajax({
        "url":"../account/getAccountKinds.do",
        "data":{ 'accountStatus': 1 },
        success:function (res) {
            let list = res.data && res.data.financeAccounts || [] ;
            let str = `<option value="0">请选择</option>`
            list.forEach(function (item) {
                let isSelect = ''
                if(selectID == item.id ){
                    isSelect = "selected"
                }
                if ( item.accountType == 1 ){
                    str += `<option value="1-${ item.id }" ${ isSelect }>现金支付</option>`
                }else{
                    let bankNameStr = item["name"] + ' ' + formatAccount(item["account"])+ ' ' + item["bankName"] ;
                    if(item.isPublic == 1){
                        bankNameStr = formatAccount(item["account"])+ ' ' + item["bankName"] ;
                    }
                    str += `<option ${ isSelect } value="5-${ item.id }">${ `转账支付 - ${ bankNameStr }` }</option>`
                }
            })

            obj.html(str)
        }
    })
}
// creator： 侯杏哲 2022-8-1 获取 税种 列表
function getRateList(obj, selectID) {
    $.ajax({
        "url":"../accountant/taxList.do",
        success:function (res) {
            let list = res.data || [] ;
            let str = `<option value="0">请选择</option>`
            list.forEach(function (item) {
                let isSelect = ''
                if(selectID == item.id ){
                    isSelect = "selected"
                }
                str += `<option ${ isSelect } value="${ item.id }">${ item.name }</option>`
                })

            obj.html(str)
        }
    })
}
// creator： 侯杏哲 2022-8-1 获取 申报记录
function getRateApplyLog(obj, selectID) {
    console.log("1")
    let rateCat = $("#rateCat").val()
    let rateStartDate = $("#rateStartDate").val()
    let rateEndDate = $("#rateEndDate").val()
    if(rateCat && rateCat.length > 0 && rateStartDate.length > 0 && rateEndDate.length > 0){
        let stDate = new Date(rateStartDate)
        let edDate = new Date(rateEndDate)
        if(stDate > edDate){
            layer.msg("税款所属时期开始日期应在截止日期之前！")
            $("#rateEndDate").val("")
            return false;
        }
        let data = { "taxId":rateCat, "startDate":rateStartDate, "endDate":rateEndDate };
        if(data.taxId == applyData.taxId && data.startDate == applyData.startDate && data.endDate == applyData.endDate ){
           return false
        }
        getRateApplyLogAj(data , obj, selectID)
    }
}
function getRateApplyLogAj(data, obj, selectID){
    if(!data.startDate || !data.endDate || Number(data.taxId) == 0 ){
        return false
    }
    applyData = JSON.parse(JSON.stringify(data))

    data.startDate = data.startDate.toString().split('');
    data.startDate.splice(4, 0, "-");
    data.startDate.splice(7, 0, "-");
    data.startDate = data.startDate.join("")

    data.endDate = data.endDate.toString().split('');
    data.endDate.splice(4, 0, "-");
    data.endDate.splice(7, 0, "-");
    data.endDate = data.endDate.join("")
    $.ajax({
        "url":"../accountant/getApplyTaxList.do",
        "data":data,
        beforeSend:function(){},
        success:function (res) {
            let list = res.data || [] ;
            let str = `<option value="0">请选择</option>`
            list.forEach(function (item) {
                let isSelect = ''
                if(selectID == item.id ){
                    isSelect = "selected"
                }
                str += `<option ${ isSelect } value="${ item.report }" data-id="${ item.id }" startDate="${ item.startDate }" endDate="${ item.endDate }">需申报的起止日期为${ new Date(item.startDate ).format("yyyy-MM-dd") }至${ new Date(item.endDate ).format("yyyy-MM-dd") }的申报记录 </option>`
            })
            obj.html(str)
        }
    })
}
// creator： 侯杏哲 2021-12-02 新增收款单位
function addOopBtn() {
	bounce.show($("#oppositeAddDiv"));
    $("#oppositeAddDiv input").val("");
    $("#oppositeAddDiv textarea").val("");
    $(".showNum").html(`0 / 50`)
}
function addOopOK() {
	let name = $("#oopName").val();
	if(name.length === 0){
		layer.msg("请输入收款单位名称")
		return false
	}
	let remark = $("#oopMemo").val();
	bounce.cancel()
	$.ajax({
		"url":"../stakeholder/addStakeholder.do",
		"data":{ 'name':name , 'remark': remark },
		success:function (res) {
			let status = res.data.status;
			if(status == 0){
				layer.msg("操作失败")
			}else if(status == 1){
                layer.msg("操作成功");
                let id= res.data.financeStakeholder.id
                $("#stakeholderCategoryText").html('财务自行录入的收款单位（含个人）').data("type",3);
                $("#oppositeCorp2").val(name).data("id",id);
                $(".oop2").show();
			}else if(status == 2){
                layer.msg("系统里已有该收款单位！")

            }
        }
	})
}
// creator： 侯杏哲 2021-12-02 选择 收款单位
function openOop(){
    let sghow = $("#stakeholderCategory").is(":visible")
    if(sghow){
        $("#stakeholderCategory").hide();
    }else{
        $("#stakeholderCategory").show();
    }
}
function chooseCOK() {
	let selectObj = $("#chooseC i.fa-dot-circle-o");
	if(selectObj.length > 0){
        let type = $("#oppositeCorpDiv").data("type")
		let typeStr = ''
		if(type == 1){
            typeStr = '供应商'
		}else if(type == 2){
            typeStr = '公司职工'
		}else if(type == 3){
            typeStr = '财务自行录入的收款单位（含个人）'
        }
        $("#stakeholderCategoryText").html(typeStr).data("type",type);

        let id = selectObj.parent().data('val');
        let name = selectObj.next("span").html();
        $("#oppositeCorp2").val(name).data("id",id);
        $(".oop2").show();
        bounce.cancel()
        $("#stakeholderCategory").hide()
	}else{
		layer.msg('请先选择收款对象')
	}
}
function setNum(obj) {
    let val = obj.val();
    if(val.length > 50){
        val = val.substr(0,50);
        obj.val(val);
	}
    obj.next(".showNum").html(`${ val.length } / 50`)
}
function setLastname0() {
    bounce.cancel();
    $('#lastname').val('0')
    $(".swi3").hide()
    $(".swi6").hide();
    $(".huihua").hide();
    $(".memotr").hide();
    $(".huihua").nextAll().hide()
    $("#submitCheck").hide();
}
// 类别种类
function expendMethod(thisObj) {
	var val = thisObj.val()
    if(val === ""){
        layer.msg("请选择支出类别！")
        return false
    }
    let vai2 = Number(val)
    $(".swi3").hide()
    $(".swi6").hide();
    $(".huihua").hide();
    $(".memotr").hide();
    $(".huihua").nextAll().hide()
    $("#submitCheck").hide();
	switch (vai2){
		case 0:
			$(".notTransfor>.itemTr").hide()
            $(".expend").hide(); // 显示支出
            $(".exCat").show()
            break;
		case 1: // 本人的公务支出（财务费用除外、需入库物品的报销除外）
			$("#reimburse").data("submitType",'myself')
			reimburseFF()
            break;
		case 2: // 其他同事的公务支出（财务费用除外，需入库物品的报销除外）
			$("#reimburse").data("submitType",'otherTog')
			$(".notTransfor").show().siblings().hide();
			$(".swi3").nextAll().hide()
			$(".swi3").show()
			$("#submitCheck").hide()
			getStaffList($("#partnerName"))

			break;
		case 3: // 需入库物品的报销
            $(".swi3").hide()
            $(".swi6").hide();
            $(".huihua").hide();
            $(".memotr").hide();
            $(".huihua").nextAll().hide()
            $("#submitCheck").hide();
			bounce.show( $("#tipClass3") )
            break;
		case 4: // 社保/公积金
            $(".swi3").hide()
            $(".swi6").hide();
            $(".huihua").hide();
            $(".memotr").hide();
            $(".huihua").nextAll().hide()
            $("#submitCheck").hide();
			bounce.show( $("#tipClass4") )
            break;
		case 9: // 款项借出
            $(".swi3").hide()
            $(".swi6").hide();
            $(".huihua").hide();
            $(".memotr").hide();
            $(".huihua").nextAll().hide()
            $("#submitCheck").hide();
			bounce.show( $("#tipClass5") )
            break;

		case 10: // 还款
            $(".swi3").hide()
            $(".swi6").hide();
            $(".huihua").hide();
            $(".memotr").hide();
            $(".huihua").nextAll().hide()
            $("#submitCheck").hide();
			bounce.show( $("#tipClass6") )
            break;


		case 5: // 税款
            $(".notTransfor").show().siblings().hide();
            $(".swi3").hide()
            $(".shuifee").nextAll().hide()
            $(".huihua").hide()
            $(".shuifee").show()
            $("#taxTab tr:gt(0)").remove()
            $("#taxTab").hide()
            $(".footer").hide();
            break;
		case 6: // 汇划费
            $(".notTransfor").show().siblings().hide();
            // $(".notTransfor .itemTr").hide() ;
            $(".swi3").hide()
            $(".huihua").nextAll().hide()
            $(".huihua").show()
            $(".huihua .itemTr").show()
            getAccountKinds($("#payBank"))
            uploadInit2()
            $(".footer").show();
            $("#summaryPurpose").val("汇划费");
            $("#submitCheck").show();
            break;
		case 7: // 其他财务费用的支出
            $(".huihua").hide()
            $(".swi3").hide()
            $("#stakeholderCategory").hide()
            $("#stakeholderCategoryText").html("").data("type","")

            $(".notTransfor>.itemTr").show()
            $(".dataMark").hide();	 // 隐藏遮罩层
            $(".notTransfor").show();  // 显示收入所在的转账
            $(".transfer").hide();// 隐藏非支出性转账
            $(".isGenre2").hide(); //
            $(".income").hide(); // 隐藏收入
            $(".expend").show(); // 显示支出
            $(".ex3").show(); // 显示支出
            $(".payTypeExpend").hide(); // 隐藏支出方式不是现金的各输入框
            $(".checkSame").hide(); // 隐藏支出方式不是现金的各支票选择
            $("#method").val("5");// 支出方式默认现金
            $("#billQuantity").removeAttr("disabled") ;
            changeAccountType($("#method"));
            $("#ticketType").val("0");
            $(".income7").show()
            $(".memotr").show()
            $(".swi6").show();
            $(".oop2").hide()
            $(".actual1").hide()
            $(".invoiceTtl").html("支出金额")
            $(".act2").html("支出日期")
            uploadInit()
            $(".footer").show();
            $("#submitCheck").show();


            break;
		case 8: // 以上类别以外的支出
			$("#stakeholderCategory").hide()
			$("#stakeholderCategoryText").html("").data("type","")

            $(".notTransfor>.itemTr").show()
            $(".dataMark").hide();	 // 隐藏遮罩层
            $(".notTransfor").show();  // 显示收入所在的转账
            $(".transfer").hide();// 隐藏非支出性转账
            $(".isGenre2").hide(); //
            $(".income").hide(); // 隐藏收入
            $(".expend").show(); // 显示支出
            $(".ex3").show(); // 显示支出
            $(".payTypeExpend").hide(); // 隐藏支出方式不是现金的各输入框
            $(".checkSame").hide(); // 隐藏支出方式不是现金的各支票选择
            $("#method").val("5");// 支出方式默认现金
            $("#billQuantity").removeAttr("disabled") ;
            changeAccountType($("#method"));
            $("#ticketType").val("0");
            $(".income7").show()
            $(".memotr").show()
            $(".oop2").hide()
            uploadInit()
            $(".footer").show();
			$("#submitCheck").show();
            $(".act2").html("实际付款日期")
            $(".invoiceTtl").html("票据金额合计")

            break;
		default:

	}
}
// creator:hxz 2022-03-15 获取 银行账户
function getAccountKinds(obj) {
    // Integer accountType：账户类型   1-现金，2-银行,3-其他
    // String isPublic：是否为对公帐号:1-是 0-否
    // Integer isBasic： 是否是基本户 1-是，0-否
    // Integer accountStatus：账户状态 0:关闭 1：正常

	$.ajax({
		"url":"../account/getAccountKinds.do",
        "data":{
		    "accountType":2,
            "accountStatus":1
        },
		success:function (res) {
			let list = res.data.financeAccounts || [] ;
			let str = `<option value="0"> -- 请选择 -- </option>`
			list.forEach(function (item) {
                let bankNameStr = item["name"] + ' ' + formatAccount(item["account"])+ ' ' + item["bankName"] ;
                if(item.isPublic == 1){
                    bankNameStr = formatAccount(item["account"])+ ' ' + item["bankName"] ;
                }
                str += "<option value="+ item["id"] +">"+ bankNameStr +"</option>"

            })
			obj.html(str)
		}
	})
}
// creator:hxz 2022-03-15 获取公司职工**
function getStaffList(obj) {
	$.ajax({
		"url":"../stakeholder/getUsers.do",
		success:function (res) {
			let list = res.data.users || [] ;
			let str = `<option value="0"> -- 请选择 -- </option>`
			list.forEach(function (item) {
				if(item.isDuty != 2){
					str += `<option value="${ item.userID }">${ item.userName } - ${ item.mobile }</option>`
				}
			})
			obj.html(str)
		}
	})
}
// creator:hxz 2022-03-15 下一步
function swi32Next() {
	let factUser = $("#partnerName").val()
	if(factUser == 0){
		layer.msg("请先选择同事姓名！")
		return false
	}
	reimburseFF()
}
function reimburseFF() {
	initForm('reimburse')
	$("#billCat").removeAttr("disabled");
	bounce.show($("#reimburse"))
	setEveryTime(bounce, 'reimburse')
	getFeeCats()
	getBillCats()
	bounce_Fixed.cancel()
	$("#submitCheck").hide();
}
// 选择数据录入的方式
var submitMethod = "";
function dataImportNav(obj){
	$("#genre option:first").prop("selected",true);
	changeCatergray($("#genre"));
	$(".radioBtn ").removeClass("radioActive");
    var userType = chargeRole("超管");
    if (userType) {
		$("#mt_tip_ms").html("您没有此操作权限！");
		bounce.show($("#mtTip"));
		return false ;
	}
	$(".dataContainer").show();
	$(".dataContainer input").val("");
	var val = (obj.val()) ;
	if(val === ""){
		layer.msg("请选择数据类别！")
        $(".dataContainer").hide();
		return false
	}
	var navVal = Number(val) ;
	submitMethod = navVal ;

    $(".swi3").hide()
    $(".huihua").hide()
    $(".shuifee").hide()
    $(".swi6").hide();

	switch(navVal){
		case 0 :  // 收入
            $(".notTransfor>.itemTr").show()
			$(".dataMark").show(); // 显示遮罩层
			$(".notTransfor").show();  // 显示收入所在的转账
			$(".transfer").hide();// 隐藏非支出性转账
			$(".income").show(); // 显示收入
			$(".expend").hide(); // 隐藏支出
			$(".isGenre2").hide(); // 隐藏支出
			$(".payTypeIncome").hide(); // 隐藏收入方式不是现金的备填框
			$("#incomeMethod").val("1"); // 收入方式默认现金
			$("#billQuantity").attr("disabled" , "true");
            $(".ex3").hide(); // 显示支出
			$(".invoiceTtl").html("所开具发票或收据的金额");
			$(".actualTtl").html("实际金额")
            $(".income7").hide()
            $(".memotr").show()
            $("#submitCheck").show()
            $(".footer").show();
            // 默认收入方式为承兑汇票
			$("#incomeMethod").val("4");
			changeAcceptType($("#incomeMethod"));
			break ;
		case 1 : // 支出
            $(".invoiceTtl").html("票据金额合计")
            $(".actualTtl").html("实际金额合计")

            $(".dataContainer>.itemTr").hide()
            $(".transfer").hide()
            $(".notTransfor").show()
            $(".expend").hide()
            $(".notTransfor>.itemTr").hide()
            $(".notTransfor>.income").hide()
            $(".exCat").show()
			$("#lastname").val("0")
            $(".memotr").hide()
			$("#submitCheck").hide();
            break ;
		case 2 :  // 非转账行支出
            $(".notTransfor>.itemTr").show()
			$("#method_transfer").val("6"); // 转账方式默认存现金
			$(".notTransfor").hide().siblings(".transfer").show(); // 显示转账
			$("#cun").show().siblings().hide();  // 显示默认的存现金输入项
			setTransMethod(6);
            $(".memotr").show()
            $("#submitCheck").show()
            $(".footer").show();
            break ;
		default:
			break ;
	}

}
/* =========================== Trans  =================================== */

// creater: hxz 初始化 上传插件
function uploadInit() {
    $("#imgList").html("")
    $("#uploadBtn").html("")
    $("#uploadBtn").Huploadify({
        auto: true,
        fileTypeExts: '*.gif,*.png;*.jpg;*.jpeg;',
        multi: true,
        buttonText: "上传发票图片",
        buttonNumber: 0,
        formData: {
            module: '财务管理',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: (100*1024),  // 100M = ( 100 * 1024 ) KB
        showUploadedPercent: true,
        showUploadedSize: true,
        removeTimeout: 99999999,
        // itemTemplate:itemTemp ,
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onUploadStart: function () {
            // deleObj.hide();
            // deleObj.siblings(".img").show();
        },
        onUploadComplete: function (file, data) {
        },
        onUploadError: function (file) {
            layer.msg('文件上传出错！');
            console.log('出错文件：', file)
        },
        onUploadSuccess: function (file, data) {
            data = JSON.parse(data)
            var fileUid = data.fileUid;
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                fullpath = $.fileUrl + data.filename,
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =
                '<div class="cp_img_box">' +
                '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                '       <a class="ww" path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                '       <span class="ww fa fa-times" onclick="delFile($(this))"></span>' +
                '       <a class="hd fileUid">'+ fileUid +'</a>' +
                '   </div>'+
                // '   <div class="cp_img_name">'+file.name+'</div>' +
                '</div>';
            $("#imgList").append(imgStr)
            $("#imgList .imgI:last-child").data('val', data)
        }
    });
}
function upBtn(){
    let num = $("#imgList .cp_img_box").length
    if(num >= 9){
        return false
    }
    $("#uploadBtn").find(".uploadify-button").click();
}

function uploadInit3() {
    $("#ratePic").html("")
    $("#uploadBtn3").html("")
    $("#uploadBtn3").Huploadify({
        auto: true,
        fileTypeExts: '*.gif,*.png;*.jpg;*.jpeg;',
        multi: true,
        buttonText: "上传发票图片",
        buttonNumber: 0,
        formData: {
            module: '财务管理',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: (100*1024),  // 100M = ( 100 * 1024 ) KB
        showUploadedPercent: true,
        showUploadedSize: true,
        removeTimeout: 99999999,
        // itemTemplate:itemTemp ,
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onUploadStart: function () {
            // deleObj.hide();
            // deleObj.siblings(".img").show();
        },
        onUploadComplete: function (file, data) {
        },
        onUploadError: function (file) {
            layer.msg('文件上传出错！');
            console.log('出错文件：', file)
        },
        onUploadSuccess: function (file, data) {
            data = JSON.parse(data)
            var fileUid = data.fileUid;
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                fullpath = $.fileUrl + data.filename,
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =
                '<div class="cp_img_box">' +
                '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                '       <a class="ww" path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                '       <span class="fa fa-times" onclick="delFile($(this))"></span>' +
                '       <a class="hd fileUid">'+ fileUid +'</a>' +
                '   </div>'+
                // '   <div class="cp_img_name">'+file.name+'</div>' +
                '</div>';
			$("#ratePic").append(imgStr)
			$("#ratePic .imgI:last-child").data('val', data)
        }
    });

}
function upBtn3(){
    let num = $("#ratePic .cp_img_box").length
    if(num >= 1){
        return false
    }
    $("#uploadBtn3").find(".uploadify-button").click();
}
function uploadInit2() {
    $("#imgList2").html("")
    $("#uploadBtn2").html("")
    $("#uploadBtn2").Huploadify({
        auto: true,
        fileTypeExts: '*.gif,*.png;*.jpg;*.jpeg;',
        multi: true,
        buttonText: "上传发票图片",
        buttonNumber: 0,
        formData: {
            module: '财务管理',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: (100*1024),  // 100M = ( 100 * 1024 ) KB
        showUploadedPercent: true,
        showUploadedSize: true,
        removeTimeout: 99999999,
        // itemTemplate:itemTemp ,
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onUploadStart: function () {
            // deleObj.hide();
            // deleObj.siblings(".img").show();
        },
        onUploadComplete: function (file, data) {
        },
        onUploadError: function (file) {
            layer.msg('文件上传出错！');
            console.log('出错文件：', file)
        },
        onUploadSuccess: function (file, data) {
            data = JSON.parse(data)
            var fileUid = data.fileUid;
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                fullpath = $.fileUrl + data.filename,
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =
                '<div class="cp_img_box">' +
                '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                '       <a class="ww" path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                '       <span class="fa fa-times" onclick="delFile($(this))"></span>' +
                '       <a class="hd fileUid">'+ fileUid +'</a>' +
                '   </div>'+
                // '   <div class="cp_img_name">'+file.name+'</div>' +
                '</div>';
			$("#imgList2").append(imgStr)
			$("#imgList2 .imgI:last-child").data('val', data)
        }
    });

}
function upBtn2(){
    let num = $("#imgList2 .cp_img_box").length
    if(num >= 9){
        return false
    }
    $("#uploadBtn2").find(".uploadify-button").click();
}

function delFile(thisObj){
    thisObj.parents('.cp_img_box').remove();
    let fileUid = thisObj.siblings(".hd").html()
    fileDelAjax({ 'type':'fileUid', 'fileUid': fileUid })
}


// 根据转账类型的不同，得到不同的收付款账户

function setTransMethod( type ){   //  1-存现金 2-取现金 3-其他银行转账
	var useType = 1;
	if( Number(type) == 6){ useType = 1; }else if( Number(type) == 7){ useType = 2;  }else if( Number(type) == 8){ useType = 3;  }
	$.ajax({
		url:"../data/chooseAccountByType.do" ,
		data:{ "type" : useType  } ,
		type:"post" ,
		dataType:"json" ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function (data) {
			var financeAccount1 = data["financeAccount1"];
			var financeAccount2 = data["financeAccount2"];
			switch( Number(type) ){
				case 6 :
					$("#cun").show().siblings().hide();
					$("#fromId_cun").val(financeAccount1["id"]);
					$("#toId_cun").html("");
                    var str = "" ;
                    var checkobj = "" ;
                    for( var i = 0 ; i < financeAccount2.length ; i++ ){
                        if( financeAccount2[i]["isBasic"] === 1){
                            str += "<option value='"+ financeAccount2[i]["id"] +"'>"+ financeAccount2[i]["bankName"] +"（基本户）"+ financeAccount2[i]["account"] +"</option>" ;
                        }else{
                            str += "<option value='"+ financeAccount2[i]["id"] +"'>"+ financeAccount2[i]["bankName"] +"  "+ financeAccount2[i]["account"] +"</option>" ;
						}
					}
					$("#toId_cun").html(str);
					break ;
				case 7 :
					$("#qu").show().siblings().hide();
					$("#toId_qu").val(financeAccount2["id"]);
					$("#fromId_qu").html("");
					var str = "" ;
                    $("#EnCrashMent span.radioBtn").removeClass("radioActive");
                    $('#checkId_qu').html("");
                    for( var i = 0 ; i < financeAccount1.length ; i++ ){
                        if( financeAccount1[i]["isBasic"] === 1){
                            if(i == 0){ checkobj = JSON.stringify(financeAccount1[i]) ;  }
                            str += "<option value='"+ JSON.stringify(financeAccount1[i]) +"'>"+ financeAccount1[i]["bankName"] +"（基本户）"+ financeAccount1[i]["account"] +"</option>" ;
                        }else{
							str += "<option value='"+ JSON.stringify(financeAccount1[i]) +"'>"+ financeAccount1[i]["bankName"] +"  "+ financeAccount1[i]["account"] +"</option>" ;
						}
					}
					$("#fromId_qu").html(str) ;   
					//if(checkobj){   getCheckList( checkobj );    }else{ $("#quCheckCon").hide() ;  }
					if(checkobj){   $("#EnCrashMent").show();    }else{ $("#EnCrashMent").hide();  }
					break ;
				case 8 :
					$("#tans").show().siblings().hide();
					$("#fromId_tans").html("");
                    $("#toId_tans").html("");
                    var str2 = "", str1 = "" ;
                    for( var i = 0 ; i < financeAccount1.length ; i++ ){
                        if( financeAccount1[i]["isBasic"] === 1){
                            str1 += "<option value='"+ financeAccount1[i]["id"] +"'>"+ financeAccount1[i]["bankName"] +"（基本户）"+ financeAccount1[i]["account"] +"</option>" ;
                        }else{
                            str1 += "<option value='"+ financeAccount1[i]["id"] +"'>"+ financeAccount1[i]["bankName"] +"  "+ financeAccount1[i]["account"] +"</option>" ;
                        }
                    }
                    for( var j = 0 ; j < financeAccount2.length ; j++ ){
                        if( financeAccount2[j]["isBasic"] === 1){
                            str2 += "<option value='"+ financeAccount2[j]["id"] +"'>"+ financeAccount2[j]["bankName"] +"（基本户）"+ financeAccount2[j]["account"] +"</option>" ;
                        }else{
                            str2 += "<option value='"+ financeAccount2[j]["id"] +"'>"+ financeAccount2[j]["bankName"] +"  "+ financeAccount2[j]["account"] +"</option>" ;
						}
					}
					$("#fromId_tans").html(str1);
					$("#toId_tans").html(str2);
					break ;
			}
		}, 
		error:function () {
			$("#mt_tip_ms").html("获得可用银行失败，请刷新重试！"); bounce.show($("#mtTip"));
		} ,
		complete:function(){ loading.close() ;  }
	});

}

/*function getCheckList(checkobj){
	var jsonCheck = JSON.parse(checkobj) ;
	if(jsonCheck["operation"] == "基本户"){
		var checkId = jsonCheck["id"] ;
		$.ajax({
			url : $.webRoot+"/data/getCashChequeByAccountId.do" ,
			data : { "financeAccountId" : checkId } ,
			type:"post" ,
			dataType:"json" ,
			beforeSend:function(){ loading.open() ;  } ,
			success:function (data) {
				var status = data["status"] ;
				if( status == 0 || status == 2 ){ // 当前账户没有可用的支票
					$("#quCheckCon").hide() ;
					$("#mt_tip_ms").html("当前账户没有可用的支票"); bounce.show($("#mtTip"));
					$("#checkId_qu").html("");
					$("#quCheckCon").show() ;
				}else{
					var list = data["chequeDetailList"] ;
					if(list){    
						var str = "" ;         
						for(var i=0 ; i<list.length ; i++ ){
							str += "<option value='"+ list[i]["id"] +"'>"+ list[i]["chequeNo"] +"</option>" ;
						}
						$("#checkId_qu").html(str);
						$("#quCheckCon").show() ;
					}else{
						$("#quCheckCon").hide() ;
						$("#mt_tip_ms").html("当前账户没有可用的支票"); bounce.show($("#mtTip"));
						$("#checkId_qu").html("");
						$("#quCheckCon").show() ;
					}
				}

			},
			error:function () {
				alert("获取支票号失败");
			} ,
			complete:function(){ loading.close() ;  }
		});
	}else{
		$("#quCheckCon").hide() ;
	}

}*/

function getCheckList(checkobj){
    //var jsonCheck = JSON.parse(checkobj) ;
    var jsonCheck = checkobj;
    if(jsonCheck["isBasic"] === 1){
        var checkId = jsonCheck["id"] ;
        $.ajax({
            url : $.webRoot+"/data/getCashChequeByAccountId.do" ,
			data : { "financeAccountId" : checkId } ,
			type:"post" ,
			dataType:"json" ,
			beforeSend:function(){ loading.open() ;  } ,
			success:function (data) {
				var status = data["status"] ;
				if( status == 0 || status == 2 ){ // 当前账户没有可用的支票
					$("#quCheckCon").hide() ;
					$("#mt_tip_ms").html("当前账户没有可用的支票"); bounce.show($("#mtTip"));
					$("#checkId_qu").html("");
					$("#quCheckCon").show() ;
				}else{
					var list = data["chequeDetailList"] ;
					if(list){
						var str = "" ;
						for(var i=0 ; i<list.length ; i++ ){
							str += "<option value='"+ list[i]["id"] +"'>"+ list[i]["chequeNo"] +"</option>" ;
						}
						$("#checkId_qu").html(str);
						//$("#quCheckCon").show() ;
					}else{
						$("#quCheckCon").hide() ;
						$("#mt_tip_ms").html("当前账户没有可用的支票"); bounce.show($("#mtTip"));
						$("#checkId_qu").html("");
						$("#quCheckCon").show() ;
					}
				}

			},
			error:function () {
				alert("获取支票号失败");
			} ,
			complete:function(){ loading.close() ;  }
		});
	}

}
/* =========================== Expend =================================== */
var billPeriodObj = null ;
function changeTicketMonth(obj){
	obj.addClass("radioActive");
	obj.siblings().removeClass("radioActive");
	billPeriodObj = JSON.parse( obj.children(".val").html()  ) ;
}

// 选择支出方式 
function changeAccountType( obj ){
	var val = Number(obj.val());
	switch(val){
		case 1 : // 现金
			$(".payTypeExpend").hide(); 
			$(".checkSame").hide();
			break ;
		case 3 : // 转账支票
			if( $("#ticketType").val() == 0 ){
				obj.val("1") ;
				$("#mt_tip_ms").html("请先选择票据类型！") ;
				bounce.show( $("#mtTip") ) ;
				return false ;
			}
			 
			$(".payTypeExpend").show().children(".transf").show().siblings().hide();
			$("#Inner").hide();$("#Outer").hide();
			$("#withinOrAbroad").val("0");
			$(".checkSame").hide();
			break ;
		case 4 : // 承兑汇票
			if( $("#ticketType").val() == 0  ){
				obj.val("1") ; 
				$("#mt_tip_ms").html("请先选择票据类型！") ;
				bounce.show( $("#mtTip") ) ;
				return false ; 
			}
			var billCatName = $("#ticketType option:selected").html() ;
			if( billCatName == "收据" ){ // 票据类型为收据不能选承兑汇票
				obj.val("1") ;
				$("#mt_tip_ms").html("票据类型为收据，您不能选择外部承兑汇票") ;
				bounce.show( $("#mtTip") ) ;
			}else{
				$(".payTypeExpend").show().children(".accept").show().siblings().hide();
				$(".checkSame").hide();
			}
			break ;
		case 5 : // 银行转账
			setExpendBank(4) ; // 设置银行转账的银行账户
			$(".payTypeExpend").show().children(".bank").show().siblings().hide();
			$(".checkSame").hide();
			break ;
		default:
			break ;
	}
}
// 支出方式为转账支票， 选择内部支票或外部支票   
function changeTypeOfTransform(obj){
	var val = Number(obj.val()) ;
	$("#Inner").hide();$("#Outer").hide();
	switch(val){
		case 1 :  // 内部支票
			bounce.show($("#checkInner")) ;
			setExpendBank(1) ; // 设置内部支票的银行账户
			break ;
		case 2 :  // 外部支票
            var billCatName = $("#ticketType option:selected").html() ;
			if( billCatName == "收据" ){ // 票据类型为收据不能选承兑汇票
				obj.val("0") ;
				$("#mt_tip_ms").html("票据类型为收据，您不能选择外部转账支票") ;
				bounce.show( $("#mtTip") ) ; 
			}else{
				bounce.show($("#checkOuter")) ;
				setExpendBank(2) ; // 设置外部支票的银行账户
			}
			break ;
		default:
			break ;
	}
}
// 设置 不同的支票信息
function setExpendBank( type ) {
	$.ajax({
		url:"../data/chooseCheque.do" ,
		data:{ "type" : type } ,
		type:"post" ,
		dataType:"json" ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function (data) {
			var content = data["content"] ;
			switch ( Number(type) ){  // 1-内部支票   2-外部支票    3-承兑汇票     4-银行转账
				case 1 :
					$("#financeAccountId_payout").html("") ;
					$("#financeAccountId_payout_bounce").html("") ;
					if( content && content.length > 0 ) {
						var str = "";
						for (var i = 0; i < content.length; i++) {
							str += "<option value='"+ content[i]["id"]  +"'>"+ content[i]["bankName"] + "  " + content[i]["account"] +"</option>"
						}
						$("#financeAccountId_payout").html(str) ;
						$("#financeAccountId_payout_bounce").html(str) ;
						setCheckNo( content[0]["id"] );
					}
					break ;
				case 2 :
					var str = "";
					$("#outerCheck").html("") ;
					if( content && content.length > 0 ) {
						var str = "";
						for (var i = 0; i < content.length; i++) {
							str += "<tr onclick='selectOuterCheck($(this))'>" +
								"<td>" +
								"<span class='radioBtn ' >" +
									"<span class='val'>{value:"+ content[i]["id"] +" , name:''}</span>" +
									"<span class='radioShow'></span>" +
								"</span>" +
								"</td>" +
								"<td>"+ content[i]["returnNo"] +"</td>" +
								"<td>"+ content[i]["amount"] +"</td>" +
								"<td>"+ content[i]["bankName"] +"</td>" +
								"<td>"+ new Date(content[i]["expireDate"]).format("yyyy-MM-dd") +"</td> " +
								"</tr>";
						}
						$("#outerCheck").html(str) ;
					}
					break ;
				case 3 :
					var str = "";
					$("#acceptCheck").html("") ;
					if( content && content.length > 0 ) {
						var str = "";
						for (var i = 0; i < content.length; i++) {
							str += "<tr onclick='acceptCheckSelect($(this))'>" +   
								"<td>" +  
									"<span class='radioBtn ' > " +    
										"<span class='val'>{ value : '"+ content[i]["id"] +"' }</span>" +   
										"<span class='radioShow'></span>"+   
									"</span>" +     
								"</td>" +   
								"<td>"+ new Date(content[i]["createDate"]).format("yyyy-MM-dd") +"</td>" +
								"<td>"+ content[i]["payer"] +"</td>" +
								"<td>"+ content[i]["amount"] +"</td>" +
								"<td>"+ content[i]["returnNo"] +"</td>" +
								"<td>"+ new Date(content[i]["expireDate"]).format("yyyy-MM-dd") +"</td>" +
								"<td>"+ content[i]["originalCorp"] +"</td>" +
								"<td>"+ content[i]["bankName"] +"</td>"+
								"</tr>";
						}
						$("#acceptCheck").html(str) ; 
					}
					break ; 
				case 4 :
					var str = "" ;  
					$("#financeAccountId_payTypeExpendBank").html("") ;
					if( content && content.length > 0 ) {
						var str = "<option value=''>选择转账银行</option>";
						for (var i = 0; i < content.length; i++) {
							let bankNameStr = content[i]["name"] + ' ' + formatAccount(content[i]["account"])+ ' ' + content[i]["bankName"] ;
							if(content[i].isPublic == 1){
								bankNameStr = formatAccount(content[i]["account"])+ ' ' + content[i]["bankName"] ;
							}
							str += "<option value='"+ content[i]["id"] +"'>"+ bankNameStr +"</option>";
						}
						$("#financeAccountId_payTypeExpendBank").html(str) ;
					}
					break ;
				default:
					break ;
			}

		},
		error:function () {
			$("#mt_tip_ms").html("获得可用银行失败，请刷新重试！"); bounce.show($("#mtTip"));
		} ,
		complete:function(){ loading.close() ;  }
	})
}
// 设置银行账户的支票号
function setCheckNo( accountId ){
	$("#checkNumber_payout_bounce").html("");
	$("#checkNumber_payout").html("");
	$.ajax({
		url:"../data/getChequeByAccountId.do" ,
		data:{ "accountId" : accountId } ,
		type:"post" ,
		dataType:"json" ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function (data) {
			var financeChequeDetails = data["financeChequeDetails"] ;
			if( financeChequeDetails && financeChequeDetails.length > 0 ) {
				var str = "<option value=''>支票账号</option>";
				for (var i = 0; i < financeChequeDetails.length; i++) {
					str += "<option value='"+ financeChequeDetails[i]["id"]  +"'>"+ financeChequeDetails[i]["bankName"] + "  " + financeChequeDetails[i]["chequeNo"] +"</option>"
				}
				$("#checkNumber_payout_bounce").html(str);
				$("#checkNumber_payout").html(str);
			}

		},
		error:function () {
			$("#mt_tip_ms").html("获得支票失败，请刷新重试！"); bounce.show($("#mtTip"));
		} ,
		complete:function(){ loading.close() ;  }
	})
}
// 选择好内部支票，点击确定
function showInner(){
	var id = $("#financeAccountId_payout_bounce").val() ;
	var no = $("#checkNumber_payout_bounce").val() ;
	// 做赋值操作
	if( id == "" || no == "" ){
		$("#innerTip").html("请选择有效的银行账号和支票账号！");
	}else{
		bounce.cancel() ;
		$("#Inner").show() ;
		$("#financeAccountId_payout").val(id) ;
		$("#checkNumber_payout").val(no);
	}

}
// 选择外部支票
function selectOuterCheck( obj ){
	obj.siblings().each(function(){
		$(this)	.children(":eq(0)").children(".radioBtn").removeClass("radioActive");
	});
	var radioBtn = obj.children(":eq(0)").children(".radioBtn");
	radioBtn.addClass("radioActive");
	var outerCheck =  eval('('+ radioBtn.children(".val").html()  +')'); 
	$("#outerCheckid").val(outerCheck["value"]);
	$("#outerCheckNo").val( obj.children(":eq(1)").html() );
}
// 选择好外部支票，点击确定
function showOuter(){
	bounce.cancel() ;
	if( $("#outerCheckid").val() == "" ){
		$("#withinOrAbroad").val("0");
		$("#Inner").hide();
		$("#Outer").hide();
	}else{
		$("#Outer").show() ;
	}
}
// 召唤出承兑汇票
var acceptCheckObj = null ; // 已经选择的承兑汇票信息
function selectAcceptBtn(){
	setExpendBank(3) ; // 设置承兑汇票的银行账户
	bounce.show( $('.acceptSelect') );
	acceptCheckObj = null;
}
// 选择承兑汇票
function acceptCheckSelect( obj ){
	console.log( obj.siblings().length );
	obj.siblings().each(function(){
		$(this).children(":eq(0)").children(".radioBtn").removeClass("radioActive");
	})
	var radioBtn = obj.children(":eq(0)").children(".radioBtn");
	radioBtn.addClass("radioActive");
	var val = radioBtn.children(".val").html();
	var acceptCheck = eval('('+ val +')');
	acceptCheckObj = { "iid": acceptCheck["value"] , "no": obj.children(":eq(4)").html() };
}
// 确定选择承兑汇票
function selectAcceptCheck(){
	if( acceptCheckObj ){
		$("#checkId_payTypeExpendAccept").val( acceptCheckObj["iid"]);
		$("#checkNo_payTypeExpendAccept").html( acceptCheckObj["no"] );
	}
	bounce.cancel();
}
// 取消选择各种支出方式的票据
function cancelPayMethod( num ){  // 1 转账支票  
	bounce.cancel(); 
	if( num == 1){
		$("#withinOrAbroad").val("0");
		$("#Inner").hide();
		$("#Outer").hide();
	}
}
/* ============================= Income =================================== */
// 选择收入方式 
function changeAcceptType( obj ){
	var val = Number(obj.val()); 
	switch(val){
		case 1 : // 现金
			$(".payTypeIncome").hide();$("#billMoneyCon").show();
			break ;
		case 3 : // 转账支票
			$(".payTypeIncome").show().children(".transf").show().siblings().hide();$("#billMoneyCon").hide();
			break ;
		case 4 : // 承兑汇票
			$(".payTypeIncome").show().children(".accept").show().siblings().hide();$("#billMoneyCon").hide();
			break ;
		case 5 : // 银行转账
			$(".payTypeIncome").show().children(".bank").show().siblings().hide();$("#billMoneyCon").show();
			setAcceptBank();
			break ;
		default:
			break ;
	}
}
// 变类别
function changeCatergray(obj){
	var num = obj.val();
	if( Number(num) == 5){ $(".isGenre").show();  }else { $(".isGenre").hide(); }

	if( Number(num) == 1){
	    layer.msg('录入客户回款，请到回款录入模块中操作！')
        obj.val("")
    }
	if( Number(num) == 2){
	    layer.msg('录入借来的款项，请到常规借款模块中操作！')
        obj.val("")
    }
	if( Number(num) == 6){
	    layer.msg('录入收回的借款，请到常规借款模块中操作！')
        obj.val("")
    }
}
// 设置收款银行
function setAcceptBank() {
	if(!bankInfo){  
		getBank( $("#financeAccountId") ); 	
	}else {
		var str = "<option value=''>选择收款银行</option>";
		for( var i=0 ; i<bankInfo.length ; i++ ){
			str += "<option value="+ bankInfo[i]["id"] +">"+ bankInfo[i]["bankName"] + " " + bankInfo[i]["account"] +"</option>"
		}
		$("#financeAccountId").html(str);
	}
}
/* =========================== submitCheck =================================== */
// 保存数据录入
function submitCheck(){
	if( submitMethod == 0 ){  // 收入
		var genre = $("#genre").val(); genre;//类别 1-货款，2-借款，3-投资款，4-废品，5-其他
		var categoryDesc = $("#categoryDesc").val(); // genre 为其他时候的的说明
		var summary = $("#summary").val();
		var money = ''
		var billMoney = parseFloat($("#billMoney").val());
		var purpose = $("#purpose").val();
		var auditorName = $("#auditorName").val();
		var oppositeCorp = $("#oppositeCorp").val();
		var memo = $("#memo").val();
		var method = $("#incomeMethod").val();  // 收入方式
		var isok = testNull(genre , "请选择合法的类别！"); if(!isok) return false ;
        if (Number(method) === 3){
            money = parseFloat($("#money3").val());
        } else if (Number(method) === 4) {
            money = parseFloat($("#money2").val());
        } else if (Number(method) !== 0) {
            money = parseFloat($("#money").val());
        }
		if( Number(genre) == 5 ){ isok = testNull(categoryDesc , "请对其他类别做简要说明！"); if(!isok) return false ;  }
		isok = testNull(summary , "摘要不能为空！"); if(!isok) return false ;
		isok = testNull(money , "实际金额不能为空！"); if(!isok) return false ;
		isok = testNumDataImport(money , "实际金额必须为正数！"); if(!isok) return false ;
        //isok = testNull(billMoney , "发票金额不能为空！"); if(!isok) return false ;
        isok = testNumDataImport(billMoney , "发票金额必须为正数！"); if(!isok) return false ;
		isok = testNull(purpose , "用途不能为空！"); if(!isok) return false ;
		isok = testNull(auditorName , "经手人不能为空！"); if(!isok) return false ;
		isok = testNull(oppositeCorp , "付款单位不能为空！"); if(!isok) return false ;
		isok = testNull(method , "收入方式不能为空！"); if(!isok) return false ;
		var data = {
			"genre": genre ,
			"categoryDesc": categoryDesc ,
			"summary": summary ,
			"money": money ,
			"billAmount": billMoney ,
			"purpose": purpose ,
			"memo": memo ,
			"method": method ,
			"auditorName": auditorName ,
			"oppositeCorp": oppositeCorp
		};
		if( Number(method) == 3){  // 转账支票
			var receiveDate = $("#receiveDate").val() ;
			var checkNumber = $("#checkNumber").val() ;
			var expireDate = $("#expireDate").val() ;
			var originalCorp = $("#originalCorp").val() ;
			var bankName = $("#bankName").val() ;
			isok = testNull(receiveDate , "收到票据日期不能为空！"); if(!isok) return false ;
			isok = testNull(checkNumber , "支票号不能为空！"); if(!isok) return false ;
			isok = testNull(expireDate , "到期日不能为空！"); if(!isok) return false ;
			isok = testNull(originalCorp , "出具的单位不能为空！"); if(!isok) return false ;
			isok = testNull(bankName , "出具的银行不能为空！"); if(!isok) return false ;
			data["receiveDate"] = receiveDate ;
			data["checkNumber"] = checkNumber ;
			data["expireDate"] = expireDate ;
			data["originalCorp"] = originalCorp ;
			data["bankName"] = bankName ;
		}else if( Number(method) == 4){  // 承兑支票
			var receiveDate = $("#receiveDate_4").val() ;
			var checkNumber = $("#checkNumber_4").val() ;
			var expireDate = $("#expireDate_4").val() ;
			var originalCorp = $("#originalCorp_4").val() ;
			var bankName = $("#bankName_4").val() ;
			isok = testNull(receiveDate , "收到票据日期不能为空！"); if(!isok) return false ;
			isok = testNull(checkNumber , "汇票号不能为空！"); if(!isok) return false ;
			isok = testNull(expireDate , "到期日不能为空！"); if(!isok) return false ;
			isok = testNull(originalCorp , "最初出具的单位不能为空！"); if(!isok) return false ;
			isok = testNull(bankName , "出具的银行不能为空！"); if(!isok) return false ;
			data["receiveDate"] = receiveDate ;
			data["checkNumber"] = checkNumber ;
			data["expireDate"] = expireDate ;
			data["originalCorp"] = originalCorp ;
			data["bankName"] = bankName ;
		}else if( Number(method) == 5 ){  // 银行转账
			var receiveAccountDate = $("#receiveAccountDate").val() ;
			var financeAccountId = $("#financeAccountId").val() ;
			isok = testNull(receiveAccountDate , "到账时间不能为空！"); if(!isok) return false ;
			isok = testNull(financeAccountId , "请选择收款银行！"); if(!isok) return false ;
			data["receiveAccountDate"] = receiveAccountDate ;
			data["financeAccountId"] = financeAccountId ;
		}
		$.ajax({
			url:"../data/credit.do" ,
			data: data ,
			type:"post" ,
			dataType:"json" ,
			beforeSend:function(){ loading.open() ;  } ,
			success:function (data) {
				// status 0-失败， 1-成功，2-冻结，3-已生成
				var status = data["status"];
				$(".dataContainer").hide();
				$("#nav").children(".radioBtn").removeClass("radioActive");
				$("#firstname").val("")
				if(Number(status) == 0){
					$("#mt_tip_ms").html("保存失败，请刷新重试！"); bounce.show($("#mtTip"));
				}else if(Number(status) == 1){
					$("#mt_tip_ms").html("保存成功！"); bounce.show($("#mtTip"));
				}else if(Number(status) == 2){
					$("#mt_tip_ms").html("账户已被冻结，不允许进行此操作！"); bounce.show($("#mtTip"));
				}else if(Number(status) == 3){
					$("#mt_tip_ms").html("已生成，无需重复录入"); bounce.show($("#mtTip"));
				}
			},
			error:function () {
				$("#mt_tip_ms").html("连接错误，请刷新重试！");
				bounce.show($("#mtTip"));
			} ,
			complete:function(){ loading.close() ;  }
		})
	}
	else if( submitMethod == 1 ){
	    // 支出
		// var genre = $("#genre").val(); genre;//类别 1-货款，2-借款，3-投资款，4-废品，5-其他
        var billCatName = $("#ticketType").val();
        var factDate = $("#actualDate").val();
		var subType = $("#lastname").val();
		var summary = $("#summaryBen2").val();
		var money = parseFloat($("#money").val() || 0);
		var billMoney = parseFloat($("#billMoney").val() || 0);
		var purpose = $("#purpose").val();
		var auditorName = $("#auditorName").val();
		var oppositeCorp = $("#oppositeCorp2").val();
		var memo = $("#memo").val();
		var method = $("#method").val();  // 支出方式
		var billDate = $("#categoryDate").val();  // 支出方式

		var billQuantity = $("#billQuantity").val() ;
		var isok = ''
        var data = {}
		// var isok = testNull(genre , "请选择合法的类别！"); if(!isok) return false ;
		// if( Number(genre) == 5 ){ isok = testNull(categoryDesc , "请对其他类别做简要说明！"); if(!isok) return false ;  }
        isok = testNull(subType , "支出类别不能为空！"); if(!isok) return false ;
        switch (Number(subType)) {
            case 7:
            case 8:
                isok = testNull(purpose , "用途不能为空！"); if(!isok) return false ;
                isok = testNull(billDate , "票据日期不能为空！"); if(!isok) return false ;
                isok = testNull(summary , "摘要不能为空！"); if(!isok) return false ;
                isok = testNull(billQuantity , "票据数量不能为空！"); if(!isok) return false ;
                isok = testNumDataImport(billQuantity , "票据数量必须为正数！"); if(!isok) return false ;
                isok = testNull(money , "实际金额不能为空！"); if(!isok) return false ;
                if(subType == 8){
                    isok = testNumDataImport(money , "实际金额必须为正数！"); if(!isok) return false ;
                }else{
                    money = billMoney ;
                }
                isok = testNull(billMoney , "发票金额不能为空！"); if(!isok) return false ;
                isok = testNumDataImport(billMoney , "发票金额必须为正数！"); if(!isok) return false ;

                isok = testNull(auditorName , "经手人不能为空！"); if(!isok) return false ;
                isok = testNull(oppositeCorp , "收款单位不能为空！"); if(!isok) return false ;
                isok = testNull(method , "支出方式不能为空！"); if(!isok) return false ;
                // isok = testNull(billCatName , "票据种类不能为空！"); if(!isok) return false ;
                isok = testNull(factDate , "实际付款日期不能为空！"); if(!isok) return false ;
                let stakeholderCategory = $("#stakeholderCategoryText").data("type")
                let stakeholder = $("#oppositeCorp2").data("id")
                var imgList = []
                $("#imgList .cp_img_box").each(function () {
                    var imginfo = $(this).find(".ww").attr("path")
                    imgList.push(imginfo)
                })

                data = {
                    "summary": summary ,
                    "subType": subType ,
                    "money": money ,
                    "billDate": billDate ,
                    "billAmount": billMoney ,
                    "purpose": purpose ,
                    "memo": memo ,
                    "method": method ,
                    "factDate": factDate,
                    "auditorName": auditorName ,
                    "oppositeCorp": oppositeCorp ,
                    "stakeholderCategory": stakeholderCategory ,
                    "stakeholder": stakeholder ,
                    "billCatName": billCatName ,
                    // "billPeriod": billPeriod ,
                    "billQuantity": billQuantity,
                    "imgPaths": JSON.stringify(imgList)
                };
                switch (Number( method )){
                    case 1 : // 现金
                        break ;
                    case 3 : // 转账支票
                        var withinOrAbroad = $("#withinOrAbroad").val() ;   //  1-内部 ， 2-外部
                        isok = testNull(withinOrAbroad , "请选择转账支票的类型！"); if(!isok) return false ;
                        data["withinOrAbroad"] = withinOrAbroad ;
                        if( Number(withinOrAbroad) == 1){
                            var financeAccountId = $("#financeAccountId_payout").val() ;
                            var checkId =  $("#checkNumber_payout").val() ;
                            var checkNumber = $("#checkNumber_payout").val() ;
                            var expireDate = $("#expireDate_payout").val() ;
                            var receiveDate = $("#receiveDate_payout").val() ;
                            var receiver = $("#receiver_payout").val() ;
                            var operator = $("#operator_payout").val() ;
                            var billQuantity = $("#billQuantity").val() ;
                            // var billPeriod = billPeriodObj["value"] ;
                            isok = testNull(financeAccountId , "请选择账户！"); if(!isok) return false ;
                            isok = testNull(checkId , "请选择支票！"); if(!isok) return false ;
                            isok = testNull(checkNumber , "支票号不能为空！"); if(!isok) return false ;
                            isok = testNull(expireDate , "支票到期日不能为空！"); if(!isok) return false ;
                            isok = testNull(receiveDate , "收到票据日期不能为空！"); if(!isok) return false ;
                            isok = testNull(receiver , "接收经手人不能为空！"); if(!isok) return false ;
                            isok = testNull(operator , "支付经手人不能为空！"); if(!isok) return false ;
                            isok = testInt(billQuantity , "票据数量必须为正整数！"); if(!isok) return false ;

                            // isok = testNull(billPeriod , "请选择是否为本月票据！"); if(!isok) return false ;
                            data["financeAccountId"] = financeAccountId ;
                            data["checkId"] = checkId ;
                            data["checkNumber"] = checkNumber ;
                            data["expireDate"] = expireDate ;
                            data["receiveDate"] = receiveDate ;
                            data["receiver"] = receiver ;
                            data["operator"] = operator ;
                            data["billQuantity"] = billQuantity ;
                            // data["billPeriod"] = billPeriod ;
                        }else if(  Number(withinOrAbroad) == 2  ){
                            var checkId = $("#outerCheckid").val();
                            isok = testNull(checkId , "请选择外部支票！"); if(!isok) return false ;
                            data["checkId"] = checkId ;
                        }
                        break ;
                    case 4 :  // 承兑汇票
                        var checkId = $("#checkId_payTypeExpendAccept").val();
                        isok = testNull(checkId , "请选择承兑汇票！"); if(!isok) return false ;
                        data["checkId"] = checkId ;
                        break ;
                    case 5 :  // 银行转账
                        var financeAccountId = $("#financeAccountId_payTypeExpendBank").val() ;
                        isok = testNull(financeAccountId , "请选择转账银行！"); if(!isok) return false ;
                        data["financeAccountId"] = financeAccountId ;
                        break ;
                    default :
                        break ;
                }

                break;
            case 6: // 汇划费
                var factDate = $("#paydate").val(); // 支出日期
                var summaryPurpose = $("#summaryPurpose").val(); // 用途/摘要
                var payAmount = $("#payAmount").val(); // 支出金额
                var payBank = $("#payBank").val(); // 开户行
                var payMemo = $("#payMemo").val(); // 备注
                var imgList = []; // 票据数量
                isok = testNull(factDate , "支出日期不能为空！"); if(!isok) return false ;
                isok = testNull(payAmount , "支出金额不能为空！"); if(!isok) return false ;
                if(Number(payBank) == 0){
                    layer.msg("开户行不能为空！")
                    return false ;
                }

                $("#imgList2 .cp_img_box").each(function () {
                    var imginfo = $(this).find(".ww").attr("path")
                    imgList.push(imginfo)
                })

                data = {
                    "summary": summaryPurpose ,
                    "subType": subType ,
                    "money": payAmount ,
                    "purpose": summaryPurpose ,
                    "memo": payMemo ,
                    "factDate": factDate,
                    "financeAccountId": payBank,
                    "method": 5,
                    "imgPaths": JSON.stringify(imgList)
                };

                break;
            default:
                console.log('未识别出来');
        }

		if(method == 1){
			data = JSON.stringify(data);
			$("#editType").attr("data",data);
			bounce.show($("#confirmOk"));
		}else{
			$.ajax({
			 url:"../data/debit.do" ,
			 data: data ,
			 success:function (res) {
			 	let data = res.data
                 //  status 0-失败，1-成功，2-冻结，3-已生成，4-余额不足
                 var status = data["status"];
                 $("#firstname").val("")
                 $(".dataContainer").hide();
                 $("#nav").children(".radioBtn").removeClass("radioActive");
                 if(Number(status) == 0){
                    $("#mt_tip_ms").html("保存失败，请刷新重试！"); bounce.show($("#mtTip"));
                 }else if(Number(status) == 1){
                    $("#mt_tip_ms").html("保存成功！"); bounce.show($("#mtTip"));
                 }else if(Number(status) == 2){
                    $("#mt_tip_ms").html("账户已被冻结，不允许进行此操作！"); bounce.show($("#mtTip"));
                 }else if(Number(status) == 3){
                    $("#mt_tip_ms").html("已生成，无需重复录入"); bounce.show($("#mtTip"));
                 }else if(Number(status) == 4){
                    $("#mt_tip_ms").html("余额不足，保存失败"); bounce.show($("#mtTip"));
                 }
			 }
			})
		}

	}
	else if( submitMethod == 2 ){  // 内部非支出性转账
		var money = $("#transMoeny").val();
		var occurrenceTime = $("#occurrenceTime").val();
		var memo = $("#memo").val();
		var method = $("#method_transfer").val();  // 
		var isok = testNull(money , "转账金额不能为空！"); if(!isok) return false ;
		var isok = testNumDataImport(money , "转账金额必须为正数！"); if(!isok) return false ;
		isok = testNull(occurrenceTime , "业务发生时间不能为空！"); if(!isok) return false ;
		isok = testNull(method , "请选择转账种类"); if(!isok) return false ;
		var data = {
			"occurrenceTime": occurrenceTime ,
			"money": money ,
			"memo": memo ,
			"method": method
		};
		var fromId = "" , toId = "", checkId = "" ;
		switch ( Number( method ) ){   ///  6-存现金，7-取现金，8-其他内部转账
			case 6:
				fromId = $("#fromId_cun").val();
				toId = $("#toId_cun").val();
				break ;
			case 7:
				//$("#quCheckCon").hide();
                var fromobj = JSON.parse( $("#fromId_qu").val() ) ;
                fromId = fromobj["id"] ;
                toId = $("#toId_qu").val();
                if(fromobj["isBasic"] === 1){
                    if($("#EnCrashMent .radioActive").length===0){
                        $("#mt_tip_ms").html("请选择是否使用现金支票！"); bounce.show($("#mtTip"));return false;
                    }else{
						var isCrash = JSON.parse($("#EnCrashMent .radioActive").children(".val").html());
						if(isCrash.value==1){
							checkId = $("#checkId_qu").val();
							isok = testNull(checkId , "请选择有效的支票号");
							if(!isok) return false ;
							data["chequeDetailId"] = checkId ;
						}
					}
				}
				break ;
			case 8:
				fromId = $("#fromId_tans").val();
				toId = $("#toId_tans").val();
				break ;
			default : break ;
		}
		data["fromId"] = fromId ;
		data["toId"] = toId ;
		isok = testNull(fromId , "请选择付款账户！"); if(!isok) return false ;
		isok = testNull(toId , "请选择收款账户"); if(!isok) return false ;
		$.ajax({
			url:"../data/transferAccounts.do" ,
			data: data ,
			type:"post" ,
			dataType:"json" ,
			beforeSend:function(){ loading.open() ;  } ,
			success:function (data) {
				//  status 0-失败， 1-成功，2-不能对自身转账，3-冻结，4-余额不足了
				var status = data["status"];
				$(".dataContainer").hide();
				$("#nav").children(".radioBtn").removeClass("radioActive");				
				if(Number(status) == 0){
					$("#mt_tip_ms").html("保存失败，请刷新重试！"); bounce.show($("#mtTip"));
				}else if(Number(status) == 1){
					$("#mt_tip_ms").html("保存成功！"); bounce.show($("#mtTip"));
				}else if(Number(status) == 2){
					$("#mt_tip_ms").html("账户不能对自身转账！"); bounce.show($("#mtTip"));
				}else if(Number(status) == 3){
					$("#mt_tip_ms").html("账户已冻结，不能进行转账操作！"); bounce.show($("#mtTip"));
				}else if(Number(status) == 4){
					$("#mt_tip_ms").html("余额不足，保存失败"); bounce.show($("#mtTip"));
				}
			},
			error:function () {
				$("#mt_tip_ms").html("连接错误，请刷新重试！");
				bounce.show($("#mtTip"));
			} ,
			complete:function(){ loading.close() ;  window.location.reload();}
		})
	}
}
function getToummary() {
    $("#summary").val($("#purpose").val());
}
function confirm(){
	var data = $("#editType").attr("data");
	data = JSON.parse(data);
    $("#firstname").val("0")

    $.ajax({
		url:"../data/debit.do" ,
		data: data ,
		type:"post" ,
		dataType:"json" ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function (res) {
            bounce.cancel()
			//  status 0-失败，1-成功，2-冻结，3-已生成，4-余额不足
			var status = res.data["status"];
			$(".dataContainer").hide();
			$("#nav").children(".radioBtn").removeClass("radioActive");
			if(Number(status) == 0){
				$("#mt_tip_ms").html("保存失败，请刷新重试！"); bounce.show($("#mtTip"));
			}else if(Number(status) == 1){
				$("#mt_tip_ms").html("保存成功！"); bounce.show($("#mtTip"));

            }else if(Number(status) == 2){
				$("#mt_tip_ms").html("账户已被冻结，不允许进行此操作！"); bounce.show($("#mtTip"));
			}else if(Number(status) == 3){
				$("#mt_tip_ms").html("已生成，无需重复录入"); bounce.show($("#mtTip"));
			}else if(Number(status) == 4){
				$("#mt_tip_ms").html("余额不足，保存失败"); bounce.show($("#mtTip"));
			}
		},
		error:function () {
			$("#mt_tip_ms").html("连接错误，请刷新重试！");
			bounce.show($("#mtTip"));
		} ,
		complete:function(){ loading.close() ;  }
	})
}
// 验证非空
function testNull( val , tip ){
	if( $.trim(val) == "" || val == undefined){
		$("#mt_tip_ms").html(tip);
		bounce.show($("#mtTip"));
		return false ;
	}
	return true ;
}
//验证为正实数
function testNumDataImport( val , tip ){
	var reg = /^\d+(?=\.{0,1}\d+$|$)/;
	if(reg.test(val)){
		return true;
	} else {
		$("#mt_tip_ms").html(tip);
		bounce.show($("#mtTip"));
		return false ;
	}
}
//验证为正整数
function testInt( val , tip ) {
	var reg = /^[0-9]*[1-9][0-9]*$/;
	if(reg.test(val)){
		return true;
	} else {
		$("#mt_tip_ms").html(tip);
		bounce.show($("#mtTip"));
		return false ;
	}
}
// create:hxz 2022-03-07 格式化 账号
function formatAccount(account){
	let accountStr = `****`
	if(account.length >3){
		accountStr += account.substr(account.length-4,4)
	}else{
		let n = 4-account.length ;
		for(let i = 0 ; i < n; i++){
			accountStr += '*'
		}
		accountStr += account
	}
	return accountStr
}
// 获得所有可用的银行(不含现金、备用金账户)
var bankInfo = null ;
function getBank( setObj ){
		$.ajax({
			url:"../cheque/getBankAccount.do" ,
			type:"post" ,
			dataType:"json" ,
			beforeSend:function(){ loading.open() ;  } ,
			success:function (data) {
				var financeAccounts = data["financeAccounts"] ;
                var str = "<option value=''>选择收款银行</option>";
                if( financeAccounts && financeAccounts.length > 0 ){
					bankInfo = financeAccounts ;
					for( var i=0 ; i<financeAccounts.length ; i++ ){
						let bankNameStr = financeAccounts[i]["name"] + ' ' + formatAccount(financeAccounts[i]["account"])+ ' ' + financeAccounts[i]["bankName"] ;
						if(financeAccounts[i].isPublic == 1){
							bankNameStr = formatAccount(financeAccounts[i]["account"])+ ' ' + financeAccounts[i]["bankName"] ;
						}
						str += "<option value="+ financeAccounts[i]["id"] +">"+ bankNameStr +"</option>"
					}
				}
                setObj.html(str);
            },
			error:function () {
				$("#mt_tip_ms").html("获得可用银行失败，请刷新重试！"); bounce.show($("#mtTip"));
			} ,
			complete:function(){ loading.close() ;  }
		})
		
	 // 这是一个
	
}
// 时间控件的初始化
;!function(){
    laydate.render({elem: '#receiveDate'});
    laydate.render({elem: '#expireDate'});
    laydate.render({elem: '#receiveDate_4'});
    laydate.render({elem: '#expireDate_4'});
    laydate.render({elem: '#receiveAccountDate'});
    laydate.render({elem: '#expireDate_payout'});
    laydate.render({elem: '#receiveDate_payout'});
    laydate.render({elem: '#occurrenceTime'});
    laydate.render({elem: '#categoryDate'});
    laydate.render({elem: '#actualDate'});
    laydate.render({elem: '#paydate'});

    laydate.render({elem: '#rateStartDate', format: 'yyyyMMdd' });
    laydate.render({elem: '#rateEndDate', format: 'yyyyMMdd' });
    laydate.render({elem: '#rateRealDate'});


}();

function setOriginalCorp( obj ) {
	var type = $("#incomeMethod").val() ; 
	if( type == 3){
		$("#originalCorp").val( obj.val() );
	}
}

function getAllMtSupplier() {
    $.ajax({
        url:"../material/getAllMtSupplier.do" ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ;  } ,
        success:function (data) {
            var mtSuppliers = data["mtSuppliers"] ;
            if( mtSuppliers && mtSuppliers.length > 0 ){
                var str = "<option value=''>选择收款银行</option>";
                for( var i=0 ; i<bankInfo.length ; i++ ){
                    str += "<option value="+ financeAccounts[i]["id"] +">"+ financeAccounts[i]["bankName"] + " " + financeAccounts[i]["account"] +"</option>"
                }
                setObj.html(str);
            }
        },
        error:function () {
            $("#mt_tip_ms").html("获得可用银行失败，请刷新重试！"); bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    })
}

/* creator：张旭博，2017-05-28 15:10:13，设置收款单位 ，获取供应商 */
function setOppositeCorpCon() {
    $.ajax({
        url: "../material/getAllMtSupplier.do",
        data: {} ,
        type: "post",
        dataType: "json",
        success: function (data){
        	$("body").data("oppositeCorp",data["mtSuppliers"]);
        },
        error: function () {
            alert("系统错误，请重试！")
        }
    })
}
//creator lyt date:2017/11/15 9:00
function changeEnCrashMent(obj){
	obj.addClass("radioActive");
	obj.siblings().removeClass("radioActive");
	var checkobj = JSON.parse( $("#fromId_qu").val()  ) ;
	var tip = JSON.parse( obj.children(".val").html()  ) ;

	if(tip.value == "1"){
		$("#quCheckCon").show() ;
		getCheckList(checkobj);
	}else{
		$("#quCheckCon").hide() ;
	}
}
function changeFromId(checkobj){
    var jsonCheck = JSON.parse(checkobj) ;
    if(jsonCheck["isBasic"] === 1){
        $("#quCheckCon").hide() ;
        $("#EnCrashMent").show() ;
    }else{
		$("#EnCrashMent").hide() ;
		$("#quCheckCon").hide() ;
		$("#EnCrashMent span.radioBtn").removeClass("radioActive");
		$("#checkId_qu").html("");
	}
}