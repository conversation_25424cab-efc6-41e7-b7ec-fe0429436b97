var bounce_Fixed2 = new Bounce(".bounce_Fixed2");

$(function(){
	// 获得基本信息列表
	$("#bill_bar").attr("class", "deal_active").siblings().attr("class","deal_Nav");
	accept();
	//点击事件-切换列表
	$(".ty-secondTab li").on("click",function () {
		var i=parseInt($(this).index());
		//  导航末尾添加状态名,设置切换状态cookie
		var name=$(this).text();
		$(".ty-header p span:last").html(name);
		//  设置切换
		$(".ty-secondTab li").removeClass("ty-active");
		$(this).addClass("ty-active") ;
		//  展示内容
		$(".ty-mainData .opinionCon").addClass("hd");
		$(".ty-mainData .opinionCon").eq(i).removeClass("hd");
	}) ;
	$(".clearInputVal").on({
		"mousedown": function () {
			$(this).prev().val("");
		},
		"mouseup": function () {
			$(this).prev().get(0).focus();
		}
	});
	showMainCon(1)
});

//点击转账支票台账
function bill(){
	kind = 1;
	$.ajax({
		url:"../return/getAllReturn.do",
		data:{ "type":1, 	"state":1 	},
		type:"post",
		dataType:"json",
		beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
		success:function(res){
			let data = res.data;
			loading.close();
			var amount = formatCurrency( data["amount"] );
			var total = data["total"];
			$("#total").html(total);
			$("#amount").html(amount);
			$("#bill_tbody").html("");
			var financeReturns =data["financeReturns"];
			if(financeReturns !=undefined && financeReturns.length > 0 ){
				for(var i=0 ; i<financeReturns.length ; i++ ){
					var str = "<tr>" +
						"<td>"+  financeReturns[i]["returnNumber"] +"</td>" +
						"<td>"+  new Date(financeReturns[i]["expireDate"]).format('yyyy-MM-dd') +"</td>" +
						"<td>"+ formatCurrency(financeReturns[i]["amount"]) + "</td>" +
						"<td>"+  financeReturns[i]["payer"] +"</td>" +
						"<td>"+ financeReturns[i]["bankName"] + "</td>" +
						"<td>"+ financeReturns[i]["createName"] + " " +  new Date(financeReturns[i]["createDate"]).format('yyyy-MM-dd hh:mm:ss') +"</td>" +
						"<td>"+//operation：操作/汇票是否修改过:1-增,2-删,3-修改(在存入银行之前的修改，3是修改，其他都是否)
						(Number(financeReturns[i]["operation"]) === 3? '是':'否')
						+"</td>" +
						"<td class='Table_butten'>"+
							"<span class='ty-color-blue' onclick='accept_see($(this))'>查看</span>"+
							//"<span class='ty-color-gray'>修改详情</span>"+
							"<span class='ty-color-blue' onclick='bill_bank($(this))'>存入银行</span>"+
							"<div class='hd'>" +
								"<span class='returnId'>"+financeReturns[i]["id"]+"</span>" +
							"</div>" +
						"</td>"+
						"</tr>";
					$("#bill_tbody").append(str);
				}
			}
		},
		error:function (meg) {
			loading.close();
			alert("连接错误，请稍后重试！");
		} ,
		complete:function(){ chargeClose( 2 ) ;  }
	}) ;
}
//点击承兑汇款台账
function accept(){
	kind = 2;
	$.ajax({
		url:"../return/getAllReturn.do",
		data:{ "type":2, "state":1 	},
		type:"post",
		dataType:"json",
		beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
		success:function(res){
			loading.close();
			let data = res.data;
			var amount = formatCurrency( data["amount"] );
			var total = data["total"];
			$("#total_bill").html(total);
			$("#amount_bill").html(amount);
			$("#accept_tbody").html("");
			var financeReturns =data["financeReturns"];
			if(financeReturns !=undefined && financeReturns.length > 0 ){
				for(var i=0 ; i<financeReturns.length ; i++ ){
					var str = "<tr>" +
						"<td>"+  financeReturns[i]["returnNumber"] +"</td>" +
						"<td>"+  new Date(financeReturns[i]["expireDate"]).format('yyyy-MM-dd') +"</td>" +
						"<td>"+ formatCurrency(financeReturns[i]["amount"]) + "</td>" +
						"<td>"+  financeReturns[i]["payer"] +"</td>" +
						"<td>"+ financeReturns[i]["bankName"] + "</td>" +
						"<td>"+ financeReturns[i]["createName"] + ' ' +  new Date(financeReturns[i]["createDate"]).format('yyyy-MM-dd hh:mm:ss') +"</td>" +
						"<td>"+ (financeReturns[i]["operation"] === 3 || financeReturns[i]["operation"] === '3'? '是':'否') + "</td>"+
						"<td class='Table_butten'>"+
						"<span class='ty-color-blue' onclick='accept_see($(this))'>查看</span>"+
						"<span class='ty-color-blue' onclick='accept_bank($(this))'>存入银行</span>"+
						"<div class='hd'>" +
						"<span class='returnId'>"+financeReturns[i]["id"]+"</span>" +
						"</div>" +
						"</td>"+
						"</tr>";
					$("#accept_tbody").append(str);
				}
			}
		},
		error:function (meg) {
			loading.close();
			alert("连接错误，请稍后重试！");
		} ,
		complete:function(){ chargeClose(1) ;  }
	})
}
//点击转账支票台账中的查看详情按钮显示弹框
var bill_seeTrObj = null ;
function bill_see(obj){
	var tr_obj = obj.parent().parent();
	bill_seeTrObj = tr_obj;
	var returnId = obj.siblings(".hd").children(".returnId").html();
	if ( returnId == undefined || returnId == ""){
		$("#mtTip").show().siblings().hide().parent().show();
		$("#mt_tip_ms").html("系统错误，刷新重试！");
		return false;
	}
	$(".collectBillDetail .subCon").html('');
	$.ajax({
		url : "../return/getReturn.do",
		data: { "returnId":returnId   },
		type : "post" , 
		dataType : "json" ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function(data){
			var financeReturn1 =data["financeReturn1"];
			if( financeReturn1 != undefined  ){ //source 1-数据录入、以及报销等,2-借款 3-销售回款
                var amount = financeReturn1["amount"];
                var receiveDate = financeReturn1["receiveDate"];
                var type = financeReturn1["type"];  //  收入方式 1-转帐支票，2-承兑汇票
                if( Number(type) == 1 ){  type = "转帐支票"; }
                if( Number(type) == 2 ){  type = "承兑汇票"; }
                if(data.source === '3') {
                	var chequeNo = financeReturn1.returnNo
                	var bankName = financeReturn1.bankName
                	var origin = financeReturn1.originalCorp
                	var expireDate = financeReturn1.expireDate
                	$("#collectCustomer").html(financeReturn1.customerName);
                	$("#collectAmount").html(formatMoney(amount));
                	$("#collectIncome").html(type);
                	$("#collectReceive").html(new Date(receiveDate).format('yyyy/MM/dd'));
                	$("#collectSn").html(chequeNo);
                	$("#collectDueDate").html(new Date(expireDate).format('yyyy/MM/dd'));
                	$("#collectUnit").html(origin);
                	$("#collectBank").html(bankName);
                    bounce.show($("#collectChequeSee"));
                }else{
                    bounce.show($("#bill_seeAccount"));
                    var approveItem = financeReturn1["approveItem"];
                    var category = chargeCateroy( financeReturn1["category"] );
                    var billType = financeReturn1["billType"]; //  项目 1-收入,2-支出
                    if( Number(billType) == 1 ){  billType = "收入"; }
                    if( Number(billType) == 2 ){  billType = "支出"; }
                    var billDate="本月票据";//票据所属月份
                    if(billDate==2){
                        billDate="非本月票据";
                    }

                    var summary = financeReturn1["summary"];
                    var accout = financeReturn1["accout"];
                    var billAmount = financeReturn1["billAmount"];
                    var purpose = financeReturn1["purpose"];
                    var operator = financeReturn1["operatorName"];
                    var operation = financeReturn1["operation"];
                    var payer = financeReturn1["payer"];
                    var returnNo = financeReturn1["returnNo"];
                    var expireDate = financeReturn1["expireDate"];
                    var originalCorp = financeReturn1["originalCorp"];
                    var bankName = financeReturn1["bankName"];
                    var depositor = financeReturn1["createName"];
                    var createDate = financeReturn1["createDate"];
                    var memo = financeReturn1["memo"];
                    $("#checkSee_income").val(type) ;
                    $("#checkSee_item").val(billType);
                    $("#checkSee_catagory").val(category);
                    $("#checkSee_billType").val("");
                    $("#checkSee_month").val("");
                    $("#checkSee_abstract").val(summary);
                    $("#checkSee_num").val(accout);
                    $("#checkSee_money").val(amount);
                    $("#checkSee_billMoney").val(billAmount);
                    $("#checkSee_use").val(purpose);
                    $("#checkSee_guy").val(operator);
                    $("#checkSee_getbilldate").val(new Date(receiveDate).format('yyyy-MM-dd'));
                    $("#checkSee_payer").val(payer);
                    $("#checkSee_billnumber").val(returnNo);
                    $("#checkSee_billoutdata").val(new Date(expireDate).format('yyyy-MM-dd'));
                    $("#checkSee_checkunit").val(originalCorp);
                    $("#checkSee_checkbank").val(bankName);
                    $("#checkSee_financeguy").val(depositor);
                    $("#checkSee_inputtime").val(new Date(createDate).format('yyyy-MM-dd'));
                    $("#checkSee_remark").val(memo);
				}
			}
		},
		error:function (meg) {
			alert("连接错误，请稍后重试！");
		} ,
		complete:function(){ loading.close() ;  }
	});

}
//温馨提示弹框的关闭
function bounce_cancel() {
	bounce.cancel();
}
function bounce_close(){
	bounce.cancel();
}
//关闭转账支票台账中的查看详情弹框
function bill_seeClose(){
	bounce.cancel();
}
function billSee_cancel(){
	bounce.cancel();
}
//点击转账支票台账中的修改详情按钮显示弹框
function bill_updata(obj){
	var userType = chargeRole("超管");
	if(userType){
		$("#mt_tip_ms").html("您没有此操作权限！");
		bounce.show($("#mtTip"));
		return false ;
	}
	bounce.show($("#bill_updataAccount"));
	var tr_obj = obj.parent().parent();
	bill_seeTrObj = tr_obj;
	var returnId = obj.siblings(".hd").children(".returnId").html();
	if ( returnId == undefined || returnId == ""){
		$("#mtTip").show().siblings().hide().parent().show();
		$("#mt_tip_ms").html("系统错误，刷新重试！");
		return false;
	}
	$.ajax({
		url : "../return/getReturn.do",
		data: { "returnId":returnId   },
		type : "post",
		dataType : "json" ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function(data){
			var financeReturn1 =data["financeReturn1"];
			if( financeReturn1 != undefined  ){
				var approveItem = financeReturn1["approveItem"];
				var category =  financeReturn1["category"] ;
				var billDate =  financeReturn1["billDate"] ;
				var billType = financeReturn1["billType"]; //  项目 1-收入,2-支出
				if( Number(billType) == 1 ){  billType = "收入"; }
				if( Number(billType) == 2 ){  billType = "支出"; }
				var type = financeReturn1["type"];  //  收入方式 1-转帐支票，2-承兑汇票
				if( Number(type) == 1 ){  type = "转帐支票"; }
				if( Number(type) == 2 ){  type = "承兑汇票"; }
				var summary = financeReturn1["summary"];
				var accout = financeReturn1["accout"];
				var amount = financeReturn1["amount"];
				var billAmount = financeReturn1["billAmount"];
				var purpose = financeReturn1["purpose"];
				var operator = financeReturn1["operatorName"];
				var operation = financeReturn1["operation"];
				var receiveDate = new Date(financeReturn1["receiveDate"]).format('yyyy-MM-dd');
				var payer = financeReturn1["payer"];
				var returnNo = financeReturn1["returnNo"];
				var expireDate = new Date(financeReturn1["expireDate"]).format('yyyy-MM-dd');
				var originalCorp = financeReturn1["originalCorp"];
				var bankName = financeReturn1["bankName"];
				var depositor = financeReturn1["createName"];
				var createDate = new Date(financeReturn1["createDate"]).format('yyyy-MM-dd');
				var memo = financeReturn1["memo"];

				$("#billUp_item").val(billType);
				$("#billUp_category").val(category);
				$("#billUp_abstract").val(summary);
				$("#billUp_num").val(accout); // 票据数量 
				$("#billUp_money").val(amount.toFixed(2));
				$("#billUp_billMoney").val(billAmount.toFixed(2));
				$("#billUp_use").val(purpose);
				$("#billUp_guy").val(operator);
				$("#billUp_income").val(type); // 收入方式
				$("#billUp_getbilldata").val(receiveDate);
				$("#billUp_payer").val(payer);
				$("#billUp_billnumber").val(returnNo);
				$("#billUp_billoutdata").val(expireDate);
				$("#billUp_checkunit").val(originalCorp);
				$("#billUp_checkbank").val(bankName);
				$("#billUp_financeguy").val(depositor);
				$("#billUp_inputtime").val(createDate);
				$("#billUp_remark").val(memo);
			}
		},
		error:function (meg) {
			alert("连接错误，请稍后重试！");
		} ,
		complete:function(){ loading.close() ;  }
	});

}
//点击转账支票台账中的确定
function billUpdata_sure(obj){
	var billnumber = $("#billUp_billnumber").val();
	var money = $("#billUp_money").val();
	var billMoney = $("#billUp_billMoney").val();
	var billoutdata = $("#billUp_billoutdata").val();
	var financeguy = $("#billUp_financeguy").val();
	var inputtime = $("#billUp_inputtime").val();

	bill_seeTrObj.children(":eq(0)").html(billnumber);
	bill_seeTrObj.children(":eq(1)").html(money);
	bill_seeTrObj.children(":eq(2)").html(billMoney);
	bill_seeTrObj.children(":eq(3)").html(billoutdata);
	bill_seeTrObj.children(":eq(4)").html(financeguy);
	bill_seeTrObj.children(":eq(5)").html(inputtime);

	bounce.cancel();

}
//关闭转账支票台账中的修改详情弹框
function bill_updataClose(){
	bounce.cancel();
}
function billUpdata_cancel(){
	bounce.cancel();
}
//点击转账支票台账中的存入银行按钮显示弹框
function bill_bank(obj){
	var userType = chargeRole("超管");
	if(userType){
		$("#mt_tip_ms").html("您没有此操作权限！");
		bounce.show($("#mtTip"));
		return false ;
	}
	bounce.show($("#bill_bankAccount"));
	getBank( $("#turnToBank_1") ) ;
	bill_seeTrObj = obj.parent().parent() ;
}
//点击转账支票台账中的存入银行弹框中的提交
function billBank_sure(obi) {
	var returnId = bill_seeTrObj.children(":last").children(".hd").children(".returnId").html();
	var bankInfo = $("#turnToBank_1").val();
	if(!bankInfo){  $("#turnToTip_1").html("请选择存入银行");   return false;  }
	bankInfo = eval('('+ bankInfo +')') ;
	var accountId = bankInfo["id"] ;
	var BankName = bankInfo["bankName"] ;
	var account =  bankInfo["account"] ;
	var depositDate = $("#bill_depositDate").val();
	var depositorName = $("#bill_depositorName").val();
	var receiveAccountDate = $("#billReceiveAccountDate").val();
	if(!returnId){  $("#turnToTip_1").html("获得支票信息失败请刷新重试");   return false;  }
	if(!accountId){  $("#turnToTip_1").html("获得账户ID失败请刷新重试");   return false;  }
	if(!BankName){  $("#turnToTip_1").html("获得银行名称失败请刷新重试");   return false;  }
	if(!account){  $("#turnToTip_1").html("获得银行账号失败请刷新重试");   return false;  }
	if(!depositDate){  $("#turnToTip_1").html("存入时间不能为空");   return false;  }
	//if(!depositorName){  $("#turnToTip_1").html("经手人不能为空");   return false;  }
	if(!receiveAccountDate){  $("#turnToTip_1").html("到账日期不能为空");   return false;  }
	$.ajax({
		url: $.webRoot + "/return/saveBank.do",
		data:{
			"id":returnId ,
			"returnId":returnId,
			"accountId":accountId,
			"bankName":BankName,
			"account":account,
			"depositDate": new Date(depositDate),
			"depositorName":depositorName,
			"receiveAccountDate": new Date(receiveAccountDate)
		},
		success:function(data){
			var status = data["status"];
			if( status = 0 || status == "0"){
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("操作失败！");
			} else {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("存入银行操作成功！");
                bill()
			}
		}
	})
}
//关闭转账支票台账中的存入银行弹框
function bill_bankClose(){
	bounce.cancel();
}
function billBank_cancel(){
	bounce.cancel();
}
//点击承兑汇票中的查看详情按钮显示弹框
	var accept_seeTrObj = null;
function accept_see(obj){
	var tr_obj = obj.parent().parent();
	accept_seeTrObj = tr_obj;  
	var returnId = obj.siblings(".hd").children(".returnId").html();
	if ( returnId == undefined || returnId == ""){
		$("#mtTip").show().siblings().hide().parent().show();
		$("#mt_tip_ms").html("系统错误，刷新重试！");
		return false;
	}
	let page = $("#showMainConNum").val() * 1;
	$(".seeAccountInfo").html('');
	$(".remarryTeam").hide();
	$.ajax({
		url : "../return/getReturnDetail.do",
		data: { "returnId" : returnId   },
		type : "post",
		dataType : "json" ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function(res){
			let data = res.data;
			//operation：操作/汇票是否修改过:1-增,2-删,3-修改(在存入银行之前的修改，3是修改，其他都是否)
			//source：数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69)
			//         5-票款处理(1.169采购的票款处理) 6-工资管理(1.206工资之支付) 7-预付款(1.229采购之预付款) 8报销时多付出去的款---需收回的款(1.231差额处理) 9多收来的款(1.233差额处理)
			//         10-多收来的款-需收回的款(1.233差额处理)
			let source = data.source;
			var financeReturn1 =data["financeReturn"];
			$(".seeAccountInfo").html(JSON.stringify(data));
			if( financeReturn1 != undefined  ) {
                var amount = financeReturn1["amount"];
                var type = financeReturn1["type"];  //  收入方式 1-转帐支票，2-承兑汇票
                $("#acceptCheckSee .createInfo").html(financeReturn1["createName"] + ' ' +  new Date(financeReturn1["createDate"]).format('yyyy-MM-dd hh:mm:ss'));
				var billNo = financeReturn1.returnNo
				var bankName = financeReturn1.bankName
				var origin = financeReturn1.originalCorp
				var expireDate = financeReturn1.expireDate
				var payer = financeReturn1["payer"];
				var billSource = '';
				var purpose = financeReturn1["purpose"];
				var summary = financeReturn1["summary"];
				var receiveDate = new Date(financeReturn1["receiveDate"]).format('yyyy-MM-dd');
				$("#billAmount").html(formatMoney(amount));
				$("#billSn").html(billNo);
				$("#billDueDate").html(new Date(expireDate).format('yyyy-MM-dd'));
				$("#billUnit").html(origin);
				$("#billBank").html(bankName);
				$("#acceptSee_use").html(purpose);
				$("#acceptSee_abstract").html(summary);
				$("#acceptSee_getbilldata").html(receiveDate);

				if(Number(type) === 1){
					$("#acceptCheckSee .bonceHead span").html("转账支票查看");
					$("#acceptCheckSee .snTtl").html("支票号");
					$("#acceptCheckSee .unitTtl").html("出具的单位");
				} else {
					$("#acceptCheckSee .bonceHead span").html("承兑汇票查看");
					$("#acceptCheckSee .snTtl").html("汇票号");
					$("#acceptCheckSee .unitTtl").html("最初出具的单位");
				}
				if(source === 3 || source === '3' || source === 'A'){
					billSource = source === 'A' ? '需回收的款': '回款录入';
					source === 'A' ? '': payer = financeReturn1["customerName"];
					$(".sourceIncome").hide();
					$("#acceptSee_payer").html(payer);
				}else{
                    var approveItem = financeReturn1["approveItem"];
                    var category = chargeCateroy( financeReturn1["category"] );
                    var billAmount = financeReturn1["billAmount"] || financeReturn1["amount"];
                    var operator = financeReturn1["operatorName"];
                    var memo = financeReturn1["memo"];
					billSource = '数据录入－收入－' + category;
                    $("#acceptSee_billMoney").html(billAmount.toFixed(2));
                    $("#acceptSee_guy").html(operator);
                    $("#acceptSee_remark").html(memo);
                    $(".sourceIncome").show();
					$("#acceptSee_payer").html(payer);
				}
				$("#acceptSee_item").html(billSource);
				if(page === 3 || page === 5) {
					if (financeReturn1.saveBankName && financeReturn1.accout) {
						$(".remarryTeam").show();
						$(".operateCon").html('存入银行的信息');
						$(".operateDate").html(financeReturn1.updateName + ' ' + new Date(financeReturn1.updateDate).format('yyyy-MM-dd hh:mm:ss'));
						$("#saveBank").html(financeReturn1.saveBankName + financeReturn1.accout);
						$("#saveTime").html(new Date(financeReturn1.depositDate).format('yyyy-MM-dd'));
						$("#saveHandler").html(financeReturn1.depositorName);
						$("#saveArriveDate").html(new Date(financeReturn1.receiveAccountDate).format('yyyy-MM-dd'));
						$(".saveStore2").show().siblings().hide();
					}
					if (financeReturn1.oppositeCorp && financeReturn1.factDate) {
						$(".remarryTeam").show();
						$(".operateCon").html('付款信息');
						$(".operateDate").html(financeReturn1.updateName + ' ' + new Date(financeReturn1.updateDate).format('yyyy-MM-dd hh:mm:ss'));
						$("#payeeName").html(financeReturn1.oppositeCorp || '');
						$("#payDate").html(new Date(financeReturn1.factDate).format('yyyy-MM-dd'));
						$(".saveStore4").show().siblings().hide();
					}
				}
				bounce.show($("#acceptCheckSee"));
			}
		},
		error:function (meg) {
			alert("连接错误，请稍后重试！");
		}
	});
	

}
//点击承兑汇票中修改中的确定
function acceptUpdata_sure(obj){
	let json = {
		id: $("#updateId").val(),
		categoryDesc: ''
	};
	$("#accept_updataAccount input:visible").each(function (){
		let val = $(this).val();
		let name = $(this).attr("name");
		if (name === 'receiveDate' || name === 'expireDate') {
			json[name] = new Date(val)
		} else if (name !== undefined) json[name] = val;
	})
	$("#accept_updataAccount select:visible").each(function (){
		let val = $(this).val();
		let name = $(this).attr("name");
		json[name] = val;
	})
	$.ajax({
		url:"../return/updateReturnDetail.do",
		data: json,
		success:function(data){
			var status = data.data["state"];
			bounce_Fixed.cancel();
			if( Number(status) === 1){
				layer.msg("修改成功！");
				updatePageData();
				bounce.cancel();
			} else if( status === 0 || status == "0"){
				bounce.show($("#mtTip"));
				$("#mt_tip_ms").html("操作失败！");
			} else {
				bounce.show($("#mtTip"));
				$("#mt_tip_ms").html(data.data.content);
			}
		}
	})
}
//creator: 李玉婷，2024-08-21 13:55:44， 修改后刷新页面
function updatePageData(){
	let num = $("#showMainConNum").val() * 1
	if (num === 1) {
		if (kind === 1) {
			bill()
		} else if (kind === 2) {
			accept()
		}
	} else if(num === 3) {
		periodScan(sort);
	} else if(num === 5) {
		countScan(scanType);
	}
}
//点击承兑汇款中的存入银行按钮显示弹框
function accept_bank(obj){
    var userType = chargeRole("超管");
    if(userType){
		$("#mt_tip_ms").html("您没有此操作权限！");
		bounce.show($("#mtTip"));
		return false ;
	}
	bounce.show($("#accept_bankAccount"));
	$("#accept_bankAccount input").val("")
	getBank( $("#acceptBankList") );
	accept_seeTrObj = obj.parent().parent() ; 
}
//点击转账支票台账中的存入银行弹框中的提交
function acceptBank_sure() {
	var bankInfo = $("#acceptBankList").val() ;
	if(!bankInfo){  $("#turnToTip_1").html("请选择存入银行");   return false;  }
	bankInfo = eval('('+ bankInfo +')');
	var accountId = bankInfo["id"] ;
	var BankName = bankInfo["bankName"] ;
	var account = bankInfo["account"] ;
	var returnId = accept_seeTrObj.children(":last").children(".hd").children(".returnId").html();
	var depositDate = $("#accept_depositDate").val();
	var depositorName = $("#accept_depositorName").val();
	var receiveAccountDate = $("#acceptReceiveAccountDate").val();
	if(!returnId){  $("#turnToTip_2").html("获得支票信息失败请刷新重试");   return false;  }
	if(!accountId){  $("#turnToTip_2").html("获得账户ID失败请刷新重试");   return false;  }
	if(!BankName){  $("#turnToTip_2").html("获得银行名称失败请刷新重试");   return false;  }
	if(!account){  $("#turnToTip_2").html("获得银行账号失败请刷新重试");   return false;  }
	if(!depositDate){  $("#turnToTip_2").html("存入时间不能为空");   return false;  }
	//if(!depositorName){  $("#turnToTip_2").html("经手人不能为空");   return false;  }
	if(!receiveAccountDate){  $("#turnToTip_2").html("到账日期不能为空");   return false;  }
	bounce.cancel();
	$.ajax({
		url: $.webRoot + "/return/saveBank.do",
		data:{
			"id" : returnId, 
			"returnId":returnId,
			"accountId":accountId,
			"bankName":BankName,
			"account":account,
			"depositDate": new Date(depositDate),
			"depositorName":depositorName,
			"receiveAccountDate": new Date(receiveAccountDate)
		},
		success:function(data){
            var status = data["status"];
            if( status === 0 || status == "0"){
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("操作失败！");
            } else {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("存入银行操作成功！");
				accept()
            }
		},
		error:function (meg) {
			alert("连接错误，请稍后重试！");
		} ,
		complete:function(){ loading.close() ;  }
	})
}
//关闭承兑汇票中的存入银行弹框
function accept_bankClose(){
	bounce.cancel();
}
function acceptBank_cancel(){
	bounce.cancel();
}
//点击其他
function bill_others( obj ){
	var val = obj.val() ;  
	if(Number(val) == 5){
		$(".check_SeeHide").show();
	}else{
		$(".check_SeeHide").hide();
	}
	
}
// 获得可用的银行
var bankInfo = null ;
function getBank( setObj ){
	$.ajax({
		url:"../cheque/getBankAccount.do" ,
		type:"post" ,
		dataType:"json" ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function (data) {
			var financeAccounts = data["financeAccounts"] ;
			var str = "<option value=''>选择收款银行</option>";
			if( financeAccounts && financeAccounts.length > 0 ){
				bankInfo = financeAccounts ;
				for( var i=0 ; i<bankInfo.length ; i++ ){
					str += "<option value="+ JSON.stringify({"id":financeAccounts[i]["id"] , "bankName":financeAccounts[i]["bankName"] , "account": financeAccounts[i]["account"] }) +">"+ financeAccounts[i]["bankName"] + " " + financeAccounts[i]["account"] +"</option>"
				}
			}
			setObj.html(str);
		},
		error:function () {
			$("#mt_tip_ms").html("获得可用银行失败，请刷新重试！"); bounce.show($("#mtTip"));
		} ,
		complete:function(){ loading.close() ;  }
	})



}
//creator:lyt 2024/4/22 14:13 财务管理-数据查看-存入银行的承兑汇票数据修改
function updateAcceptBill(obj){
	var userType = chargeRole("超管");
	if(userType){
		$("#mt_tip_ms").html("您没有此操作权限！");
		bounce.show($("#mtTip"));
		return false ;
	}
	let res = JSON.parse($(".seeAccountInfo").html());
	$("#accept_updataAccount input").val("");
	$("#accept_updataAccount select").val("");
	if ( res == undefined || res == ""){
		$("#mtTip").show().siblings().hide().parent().show();
		$("#mt_tip_ms").html("系统错误，刷新重试！");
		return false;
	}
	let page = $("#showMainConNum").val();
	let financeReturn = res.financeReturn;
	let ttl = kind === 1?'转账支票':'承兑汇票'
	let node = kind === 1?'支票号':'汇票号'
	let source = res.source
	$(".billCat").html(ttl);
	$(".modItemTtl").html(node);
	$(".corpTtl").html(kind === 1?'出具的单位':'最初出具的单位');
	if( financeReturn != undefined  ){
		$("#updateId").val(financeReturn.id);
		bounce_Fixed.show($("#accept_updataAccount"));
		$("#accept_updataAccount input").each(function (){
			let name = $(this).attr("name");
			if (financeReturn[name]) {
				if (name === 'receiveDate' || name === 'expireDate') {
					$(this).val(new Date(financeReturn[name]).format('yyyy-MM-dd'));
				} else if (name === 'billAmount' || name === 'amount') {
					$(this).val((financeReturn[name] || 0).toFixed(2));
				} else {
					$(this).val(financeReturn[name]);
				}
			}
		})
		//source：数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69)
		//         5-票款处理(1.169采购的票款处理) 6-工资管理(1.206工资之支付) 7-预付款(1.229采购之预付款) 8报销时多付出去的款---需收回的款(1.231差额处理) 9多收来的款(1.233差额处理)
		//         A-多收来的款-需收回的款(1.233差额处理)
		if(source === 3 || source === '3' || source === 'A') {
			let ttl = source === 3 || source === '3' ? `回款录入`:'需收回的款'
			let payer = source === 'A' ? financeReturn["payer"]: financeReturn["customerName"];
			$("#acceptUp_source").val('回款录入');
			$("#accept_income").hide();
			$("#accept_common").show();
			$("#acceptUp_payer").prop("disabled", true).html(payer);
			$("#acceptUp_money").prop("disabled", true);
		} else { //source = 1、2、8、5
			$("#acceptUp_item").val('数据录入－收入');
			$("#accept_income").show();
			$("#accept_common").hide();
			var category = financeReturn["category"] ;
			$("#acceptUp_category").val(category);
			$("#acceptUp_payer").prop("disabled", false).html(financeReturn["payer"]);
			if (Number(page) === 5 && scanType === 4) {//来源为“数据录入”的数据
				$("#acceptUp_money").prop("disabled", true);
			} else {
				$("#acceptUp_money").prop("disabled", false);
			}
		}
	}
}
function updateTransferCheck(obj){
	var userType = chargeRole("超管");
	if(userType){
		$("#mt_tip_ms").html("您没有此操作权限！");
		bounce.show($("#mtTip"));
		return false ;
	}
	bounce_Fixed.show($("#accept_updataAccount"));
	//var tr_obj = obj.parent().parent();
	//acceptObj = tr_obj;
	var returnId = obj.siblings(".hd").children(".returnId").html();
	if ( returnId == undefined || returnId == ""){
		$("#mtTip").show().siblings().hide().parent().show();
		$("#mt_tip_ms").html("系统错误，刷新重试！");
		return false;
	}
	let financeReturn = JSON.parse($(".seeAccountInfo").html());
	if( financeReturn != undefined  ){
		var billType = financeReturn["billType"] ;
		billType = Number(billType) == 1 ? "收入" : "支出" ;
		$("#acceptUp_item").val( billType );
		var category = financeReturn["category"] ;
		console.log( category ) ;
		$("#acceptUp_category").val( category );
		$("#acceptUp_abstract").val(financeReturn["summary"]);
		$("#acceptUp_num").val(financeReturn["accout"]);
		$("#acceptUp_money").val(financeReturn["amount"].toFixed(2));
		$("#acceptUp_billMoney").val(financeReturn["billAmount"].toFixed(2));
		$("#acceptUp_use").val(financeReturn["purpose"]);
		$("#acceptUp_guy").val(financeReturn["operatorName"]);
		$("#acceptUp_getbilldata").val(new Date(financeReturn["receiveDate"]).format('yyyy-MM-dd'));
		$("#acceptUp_payer").val(financeReturn["payer"]);
		$("#acceptUp_billnumber").val(financeReturn["returnNo"]);
		$("#acceptUp_billoutdata").val(new Date(financeReturn["expireDate"]).format('yyyy-MM-dd'));
		$("#acceptUp_checkunit").val(financeReturn["originalCorp"]);
		$("#acceptUp_checkbank").val(financeReturn["bankName"]);
		$("#acceptUp_financeguy").val(financeReturn["createName"]);
		$("#acceptUp_inputtime").val(new Date(financeReturn["createDate"]).format('yyyy-MM-dd'));
		$("#acceptUp_remark").val(financeReturn["memo"]);
	}
}
//creator:lyt 2024/6/11 修改记录
function baseRecord(id){
	let res = JSON.parse($(".seeAccountInfo").html());
	$("#recordList table tr:gt(0)").remove();
	$.ajax({
		"url": "../return/updateReturnRecords.do",
		"data": {
			"returnId": res.financeReturn.id
		},
		success: function (res) {
			let list = res.data.financeReturnHistories || [];
			let str = ``;
			for (let i = 0;i < list.length; i++) {
				let logItem = list[i]
				logItem.index = i
				str += `<tr>
                        <td>${ i === list.length -1 ? '原始信息' : `第${ list.length - 1 - i *1}次修改后` }</td>
                        <td class="ty-td-control"><span onclick="logDetails($(this))" class="ty-color-blue">查看</span><span class="hd">${ JSON.stringify(logItem) }</span></td>
                        <td>${ logItem.createName } ${ i === list.length -1 ? new Date(logItem.createDate ).format("yyyy-MM-dd hh:mm:ss"): new Date(logItem.updateDate ).format("yyyy-MM-dd hh:mm:ss")  } </td>
                    </tr>`;
			}
			$("#recordList table tbody").append(str);
			bounce_Fixed.show($("#recordList"))
		}
	})
}
//creator:lyt 2024/6/11
function logDetails(obj) {
	let res = JSON.parse($(".seeAccountInfo").html());
	let source = res.source;
	var financeReturn1 = JSON.parse(obj.siblings(".hd").html());
	if (financeReturn1 != undefined) {//source 1-数据录入、以及报销等,2-借款 3-销售回款
		var amount = financeReturn1["amount"];
		var type = financeReturn1["type"];  //  收入方式 1-转帐支票，2-承兑汇票
		//type = Number(type) == 1 ? "转帐支票" : "承兑汇票";
		var billSource = '';
		var billNo = financeReturn1.returnNo
		var bankName = financeReturn1.bankName
		var origin = financeReturn1.originalCorp
		var expireDate = financeReturn1.expireDate
		var payer = financeReturn1["payer"];
		var purpose = financeReturn1["purpose"];
		var summary = financeReturn1["summary"];
		var receiveDate = new Date(financeReturn1["receiveDate"]).format('yyyy-MM-dd');
		$("#record_billAmount").html(formatMoney(amount));
		$("#record_billSn").html(billNo);
		$("#record_billDueDate").html(new Date(expireDate).format('yyyy-MM-dd'));
		$("#record_billUnit").html(origin);
		$("#record_billBank").html(bankName);
		$("#record_acceptSee_payer").html(payer);
		$("#record_acceptSee_use").html(purpose);
		$("#record_acceptSee_abstract").html(summary);
		$("#record_acceptSee_getbilldata").html(receiveDate);

		if (Number(type) === 1) {
			$("#recordSee .bonceHead span").html("转账支票查看");
			$("#recordSee .record_snTtl").html("支票号");
			$("#recordSee .record_unitTtl").html("出具的单位");
		} else {
			$("#recordSee .bonceHead span").html("承兑汇票查看");
			$("#recordSee .record_snTtl").html("汇票号");
			$("#recordSee .record_unitTtl").html("最初出具的单位");
		}
		if (source === 3 || source === '3' || source === 'A') {
			billSource = source === 'A' ? '需回收的款': '回款录入';
			$(".record_sourceIncome").hide();
		} else {
			var billAmount = financeReturn1["billAmount"];
			var operator = financeReturn1["operatorName"];
			var memo = financeReturn1["memo"];
			var category = chargeCateroy( financeReturn1["category"] );
			billSource = '数据录入－收入－' + category;
			$("#record_acceptSee_billMoney").html(billAmount.toFixed(2));
			$("#record_acceptSee_guy").html(operator);
			$("#record_acceptSee_remark").html(memo);
			$(".record_sourceIncome").show();
		}
		$("#record_acceptSee_item").html(billSource);
	}
	bounce_Fixed2.show($("#recordSee"));
}
//creator:lyt 2024/5/17 更多数据
let kind = 1, period = ``;
function moreData(num){
//type：1-转帐支票，2-承兑汇票
	showMainCon(2)
	let ttl = kind === 1?'转账支票':'承兑汇票'
	$(".panelTtl").html(ttl);
	$("#moreYSearch").val('');
	$.ajax({
		"url":"../return/getReturnNum.do",
		"data":{ "type": num},
		success:function (res) {
			let data = res.data;
			if (data) {
				$("#thisYearNum").html((data.yearTime.num || 0) + '张');
				$("#thisYearAmount").html((data.yearTime.amount || 0).toFixed(2) + '元');
				$("#lastYearNum").html((data.lastYearTime.num || 0) + '张');
				$("#lastYearAmount").html((data.lastYearTime.amount || 0).toFixed(2) + '元');
				$("#agoYearNum").html((data.beforeYearTime.num || 0) + '张');
				$("#agoYearAmount").html((data.beforeYearTime.amount || 0).toFixed(2) + '元');
			}
		}
	})
}
//creator:lyt 2024/5/17 更多数据-查  看
let sort = 1;
function periodScan(num){
	sort = num;
	let today = new Date();
	let resDate = ``;
	showMainCon(3);
	if (num === 1) {
		resDate = today.format('yyyy-MM-dd');
	} else if (num === 2) {
		let time = new Date(today.getFullYear() - 1, 2, 1)
		resDate = time.format('yyyy-MM-dd');
	} else if (num === 3) {
		let time = new Date(today.getFullYear() - 2, 2, 1)
		resDate = time.format('yyyy-MM-dd');
	} else if (num === 4){
		let year = $("#moreYSearch").val()
		if (year === '') {
			layer.msg("请选择年份！");
			return false;
		}
		let time = year.slice(0, -1) + '-01-01';
		resDate = time;
	}
	period = resDate;
	$("#draftCountBtn").show();
	let ttl = kind === 1?'支票':'汇票'
	$(".cateTtl").html(ttl);
	$.ajax({
		"url":"../return/getReturnList.do",
		"data":{
			"type": kind, //type：1-转帐支票，2-承兑汇票
			//"state": 1, //state: 1-有效,2-存入银行,3-作废，4-已支出
			"beginTime": period
		},
		success:function (res) {
			let data = res.data;
			var str = "";
			var total = data["numAmount"];
			var financeReturns =data["financeReturns"];
			if(financeReturns !=undefined && financeReturns.length > 0 ){
				for(var i=0 ; i<financeReturns.length ; i++ ){
					str += "<tr>" +
					"<td>"+  financeReturns[i]["returnNumber"] +"</td>" +
					"<td>"+  new Date(financeReturns[i]["expireDate"]).format('yyyy-MM-dd') +"</td>" +
					"<td>"+ formatCurrency(financeReturns[i]["amount"]) + "</td>" +
					"<td>"+  (financeReturns[i]["payer"] || '') +"</td>" +
					"<td>"+ financeReturns[i]["bankName"] + "</td>" +
					"<td>"+ financeReturns[i]["createName"] + " " +  new Date(financeReturns[i]["createDate"]).format('yyyy-MM-dd hh:mm:ss') +"</td>" +
					"<td>"+//operation：操作/汇票是否修改过:1-增,2-删,3-修改(在存入银行之前的修改，3是修改，其他都是否)
						(financeReturns[i]["operation"] === 3 || financeReturns[i]["operation"] === '3'? '是':'否')
						+"</td>" +
						"<td class='Table_butten'>"+
						"<span class='ty-color-blue' onclick='accept_see($(this))'>查看</span>"+
						"<div class='hd'>" +
						"<span class='returnId'>"+financeReturns[i]["id"]+"</span>" +
						"</div>" +
						"</td>"+
						"</tr>";
				}
			}
			$("#billPeriod").html(str);
			$(".periodDetail").html(`${new Date(data.beginDate).format('yyyy-MM-dd')}至${new Date(data.endDate).format('yyyy-MM-dd')}期间收到的外部${kind === 1?'转帐支票':'承兑汇票'}共${total.num}张，${(total.amount || 0).toFixed(2)}元`);
		}
	})
}
//creator:lyt 2024/5/17 统  计
function draftCount(num){
//type：1-转帐支票，2-承兑汇票
	showMainCon(4);
	$.ajax({
		"url":"../return/getReturnStatisticState.do",
		"data":{
			"type": kind, //type：1-转帐支票，2-承兑汇票
			"beginTime": period
		},
		success:function (res) {
			let data = res.data;
			if (data) {
				$("#stillNum").html((data.available.num || 0) + '张');
				$("#stillAmount").html((data.available.amount || 0).toFixed(2) + '元');
				$("#bankNum").html((data.saveBanks.num || 0) + '张');
				$("#bankAmount").html((data.saveBanks.amount || 0).toFixed(2) + '元');
				$("#payNum").html((data.payReturns.num || 0) + '张');
				$("#payAmount").html((data.payReturns.amount || 0).toFixed(2) + '元');
			}
		}
	})
}
//creator:lyt 2024/5/17 统  计-查  看
let scanType = 1;
function countScan(type){
	scanType = type;
	showMainCon(5);
	let topTip = ``;
	if (type === 1) {
		$(".byCurrently").show().siblings().hide();
	} else if(type === 2){
		$(".byBank").show().siblings().hide();
	}  else if(type === 4){
		$(".byPayOut").show().siblings().hide();
	}
	$.ajax({
		"url":"../return/getReturnList.do",
		"data":{
			"type": kind, //type：1-转帐支票，2-承兑汇票
			"state": type, //state: 1-有效,2-存入银行,3-作废，4-已支出
			"beginTime": period
		},
		success:function (res) {
			let data = res.data;
			var str = "";
			var total = data["numAmount"];
			var financeReturns =data["financeReturns"] || [];
			if(financeReturns !=undefined && financeReturns.length > 0 ){
				for(var i=0 ; i<financeReturns.length ; i++ ){
					if (type === 1) {
						str += "<tr>" +
							"<td>"+  financeReturns[i]["returnNumber"] +"</td>" +
							"<td>"+ new Date(financeReturns[i]["expireDate"]).format('yyyy-MM-dd')  +"</td>" +
							"<td>"+ formatCurrency(financeReturns[i]["amount"]) + "</td>" +
							"<td>"+  financeReturns[i]["payer"] +"</td>" +
							"<td>"+ financeReturns[i]["bankName"] + "</td>" +
							"<td>"+ financeReturns[i]["createName"] + " " +  new Date(financeReturns[i]["createDate"]).format('yyyy-MM-dd hh:mm:ss') +"</td>" +
							"<td>"+//operation：操作/汇票是否修改过:1-增,2-删,3-修改(在存入银行之前的修改，3是修改，其他都是否)
							(financeReturns[i]["operation"] === 3 || financeReturns[i]["operation"] === '3'? '是':'否')
							+"</td>" +
							"<td class='Table_butten'>"+
							"<span class='ty-color-blue' onclick='accept_see($(this))'>查看</span>"+
							"<div class='hd'>" +
							"<span class='returnId'>"+financeReturns[i]["id"]+"</span>" +
							"</div>" +
							"</td>"+
							"</tr>";
					} else if(type === 2){
						str += "<tr>" +
							"<td>"+  financeReturns[i]["returnNumber"] +"</td>" +
							"<td>"+ formatCurrency(financeReturns[i]["amount"])  +"</td>" +
							"<td>"+ financeReturns[i]["payer"] + "</td>" +
							"<td>"+ new Date(financeReturns[i].expireDate).format('yyyy-MM-dd') + '/' + new Date(financeReturns[i].depositDate).format('yyyy-MM-dd') + '/' + new Date(financeReturns[i].receiveAccountDate).format('yyyy-MM-dd') +"</td>" +
							"<td>"+ financeReturns[i]["accout"] + "</td>" +
							"<td>"+ financeReturns[i]["updateName"] + " " +  new Date(financeReturns[i]["updateDate"]).format('yyyy-MM-dd hh:mm:ss') +"</td>" +
							"<td class='Table_butten'>"+
							"<span class='ty-color-blue' onclick='accept_see($(this))'>查看</span>"+
							"<div class='hd'>" +
							"<span class='returnId'>"+financeReturns[i]["id"]+"</span>" +
							"</div>" +
							"</td>"+
							"</tr>";
					}  else if(type === 4){
						str += "<tr>" +
							"<td>"+  financeReturns[i]["returnNumber"] +"</td>" +
							"<td>"+ new Date(financeReturns[i]["expireDate"]).format('yyyy-MM-dd')  +"</td>" +
							"<td>"+ formatCurrency(financeReturns[i]["amount"])  +"</td>" +
							"<td>"+ financeReturns[i]["payer"] + "</td>" +
							"<td>"+ (financeReturns[i]["oppositeCorp"] || '') + "</td>" +
							"<td>"+ new Date(financeReturns[i].receiveAccountDate).format('yyyy-MM-dd') +"</td>" +
							"<td>"+ financeReturns[i]["updateName"] + " " +  new Date(financeReturns[i]["updateDate"]).format('yyyy-MM-dd hh:mm:ss') +"</td>" +
							"<td class='Table_butten'>"+
							"<span class='ty-color-blue' onclick='accept_see($(this))'>查看</span>"+
							"<div class='hd'>" +
							"<span class='returnId'>"+financeReturns[i]["id"]+"</span>" +
							"</div>" +
							"</td>"+
							"</tr>";
					}

				}
			}
			if (type === 1) {
				$(".byCurrently tbody").html(str);
				topTip = `还在手中的共${financeReturns.length}张`;
			} else if(type === 2){
				$(".byBank tbody").html(str);
				topTip = `存入银行的共${financeReturns.length}张`;
			}  else if(type === 4){
				$(".byPayOut tbody").html(str);
				topTip = `又付出去的共${financeReturns.length}张`;
			}
			$(".byCountPeriod").html(`${new Date(data.beginDate).format('yyyy-MM-dd')}至${new Date(data.endDate).format('yyyy-MM-dd')}期间收到的外部${kind === 1?'转帐支票':'承兑汇票'}中，${topTip},${(total.amount || 0).toFixed(2)}元`);
		}
	})
}
function showMainCon(num){
	$("#showMainConNum").val(num)
	$(".mainCon" + num).show().siblings().hide();
}

// 时间控件的初始化
;!function(){
    laydate.render({elem: '#accept_depositDate'});
    laydate.render({elem: '#acceptReceiveAccountDate'});
    laydate.render({elem: '#bill_depositDate'});
    laydate.render({elem: '#billReceiveAccountDate'});
    laydate.render({elem: '#acceptUp_billoutdata'});
    laydate.render({elem: '#income_receiveDate'});
    laydate.render({elem: '#common_receiveDate'});
    laydate.render({elem: '#moreYSearch',type: 'year',format: 'yyyy年'});

}();

// 判断类别 
function chargeCateroy(num){
	switch (Number( num )){
		case 1 : return "货款" ; break ; 
		case 2 : return "借款" ; break ; 
		case 3 : return "投资款" ; break ; 
		case 4 : return "废品" ; break ; 
		case 5 : return "其他" ; break ; 
		default : break ; 
	}
}


