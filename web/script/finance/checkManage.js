let matchType = false, useInfo = null;
$(function(){
    useInfo = auth.getOrg();
    //点击事件-切换列表
    $(".ty-secondTab li").on("click",function () {
        var i=parseInt($(this).index());

        //导航末尾添加状态名,设置切换状态cookie
        var name=$(this).text();
        // $.cookie('state', i);
        $(".ty-header p span:last").html(name);

        //设置切换
        $(".ty-secondTab li").removeClass("ty-active");
        $(this).addClass("ty-active");
        //展示内容
        $(".ty-mainData .opinionCon").addClass("hd");
        $(".ty-mainData .opinionCon").eq(i).removeClass("hd");
        //弹框头部改变 /* creator：张旭博，2017-04-11 09:56:45， */
        $("#addContact .bonceHead>span").html(name+"录入<span style='display:none;'>"+(2-i)+"</span>");
    });
    getOrgSonOrgs();
    init();
});
/* creator：张旭博，2017-04-11 09:56:09，初始化 */
function init() {
    cash(1, 20);
    transfer(1, 20);
    if($.cookie("state")){
        $(".ty-secondTab li").eq($.cookie("state")).click();
    }else{
        $(".ty-secondTab li:first").click();
    }
}
// 点击支票录入按钮进入支票录入页面
/* updator : 侯杏哲，2017-02-15，添加清空输入框内容的代码 */
/* updator : 侯杏哲，2017-02-16，修改现金支票的银行账户添加  */
function addContact() {
    $("#addXtip").html("");
    $("#addContact").find("input").val("");
    if(chargeRole("超管")){
        $("#mt_tip_ms").html("您没有此操作权限！");
        bounce.show($("#mtTip"));
        return false
    }
    $("#addContact").show().parent().show();
    bounce.show($("#addContact"));
    if($(".ty-active").text()=="现金支票"){
        $(".shuju").next().html("现金备用金");
        $.ajax({
            url:"../account/getBaseAccount.do",
            beforeSend:function(){ loading.open() ;  } ,
            type:"post",
            dataType:"json",
            success:function(data){
                if(data.status==1){
                    var str="";
                    var str2="";
                    str += "<option value='0' title='" + data.financeAccountBasic.bankName + "(" + (data.financeAccountBasic.isBasic == 1?'基本户':'非基本户')+ ")" + data.financeAccountBasic.account + "'>" + data.financeAccountBasic.bankName + "(" + (data.financeAccountBasic.isBasic == 1?'基本户':'非基本户') + ")" + data.financeAccountBasic.account + "</option>";
                    str2 += "<span style='display:none' class='id0'>" + data.financeAccountBasic.id + "</span>" +
                        "<span style='display:none' class='account0'>" + data.financeAccountBasic.account + "</span>" +
                        "<span style='display:none' class='bankName0'>" + data.financeAccountBasic.bankName + "</span>";
                    $(".shuju").html(str2);
                    $(".bank").html(str);
                    $(".bank").attr("title",$(".bank option:selected").html());
                }
            },
            error:function(){
                // alert("错误")
            } ,
            complete:function(){ loading.close() ;  }
        });
    }else{
        $(".shuju").next().html("银行账户");
        //检索所有可用的银行账户
        $.ajax({
            url:"../cheque/getBankAccount.do",
            beforeSend:function(){ loading.open() ;  } ,
            type:"post",
            dataType:"json",
            success:function(data){
                var str="";
                var str2="";
                for(i=0;i<data.financeAccounts.length;i++){
                    if(data.financeAccounts[i]['isPublic'] == 1){
                        if( data.financeAccounts[i].isBasic == 1){
                            str+="<option value='"+i+"'>"+data.financeAccounts[i].bankName+"(基本户)"+data.financeAccounts[i].account+"</option>";
                        }else {
                            str+="<option value='"+i+"'>"+data.financeAccounts[i].bankName+data.financeAccounts[i].account+"</option>";
                        }
                    }

                    str2+="<span style='display:none' class='id"+[i]+"'>"+data.financeAccounts[i].id+"</span>"+
                        "<span style='display:none' class='account"+[i]+"'>"+data.financeAccounts[i].account+"</span>"+
                        "<span style='display:none' class='bankName"+[i]+"'>"+data.financeAccounts[i].bankName+"</span>"
                }
                $(".shuju").html(str2);
                $(".bank").html(str);

                $(".bank").change(function(){
                    $(".bank").attr("title",$(".bank option:selected").html());
                })
            },
            error:function(){
                // alert("错误")
            } ,
            complete:function(){ loading.close() ;  }
        });
    }

}
//点击现金支票和转账支票之后的table栏切换
//切换到现金支票
function cash( currentPageNo , pageSize  ) {
    $(".shuaxinXj").html("");
//获取现金支票
    loading.open();
    let orgNameStr = ``, oid = null;
    if (matchType) {
        $("#searchOrg").val() !== "" ? oid = $("#searchOrg").val(): "";
    }
    $.ajax({
        url:"../cheque/getCheque.do",
        data:{
            currentPageNo:currentPageNo ,
            pageSize: pageSize ,
            type: 2,
            oid: oid
        },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function(data){
            loading.close();
            console.log(data);
            var str="";
            let page =  data.data.pageInfo;
            let financeCheques = data.data.financeCheques;
            for(var i=0;i<financeCheques.length;i++){
                if (matchType) {orgNameStr = `<td> ${financeCheques[i].orgName} </td>`;}
                str+="<tr>" +
                        "<td style='cursor:pointer;color: #3096DA;' onclick='cash_cheque($(this));'>" +
                            "<span style='display:none;'>"+financeCheques[i].id+"</span>"+financeCheques[i].beginNo+"-"+financeCheques[i].endNo+"</td>" +
                        "<td>"+financeCheques[i].bankName+financeCheques[i].account+"</td>" + orgNameStr+
                        "<td>"+new Date(financeCheques[i].buyDate).format('yyyy-MM-dd')+"</td>" +
                        "<td>"+financeCheques[i].buyerName+"</td>" +
                        "<td>"+new Date(financeCheques[i].createDate).format('yyyy-MM-dd hh:mm:ss')+"</td>" +
                        "<td>"+financeCheques[i].createName+"</td>" +
                    "</tr>" ;
            }
            $(".cash_con").html("").append(str);

            var pageNumber = page["currentPageNo"];
            var totalPage = page["totalPage"];
            $("#ye-cash").html("");
            setPage( $("#ye-cash") , pageNumber , totalPage , "checkManage_2" , "" )
        },
        error:function(){
            loading.close();
            // alert("错误")
        } ,
        complete:function(){ chargeClose(1) ;  }
    });
}
//现金支票详情
function cash_cheque(obj){
    let orgAble = false;
    $(".shuaxinXj").html(obj.parent().index());
    var chequeReg=obj.children("span").html();
    // console.log(chequeReg);
    bounce.show( $("#cash") ) ;
    if (matchType && obj.parent("tr").children().eq(2).html() !== useInfo.name) {orgAble = true;}
    $.ajax({
        url:"../cheque/getCashChequeDetail.do",
        data:{
            chequeReg:chequeReg
        },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            console.log(data);
            var str="";
            for(var i=0;i<data.chequeDetails.length;i++){
                var str2='<span style="cursor:pointer;" class="ty-color-red" onclick="invalid($(this));" value="'+data.chequeDetails[i].id+'">作废</span>';
                if(data.chequeDetails[i].state==2||data.chequeDetails[i].state==3 || orgAble){
                    str2="<span disabled='disabled' class='color6'>作废</span>";
                }
               // if($("#userID").html()==1){
               //     str2="<span class='color6' disabled='disabled'>作废</span>";
               // }
                var str3="未使用";
                if(data.chequeDetails[i].state==2){
                    str3="已使用";
                }else if(data.chequeDetails[i].state==3){
                    str3="已作废";
                }

                if( str3 == "已使用" ){
                    str+="<tr><td>"+data.chequeDetails[i].chequeNo+"</td>" +
                        "<td>"+(data.chequeDetails[i].amount || '')+"</td>" +
                        "<td>"+new Date(data.chequeDetails[i].operateDate).format('yyyy-MM-dd')+"</td>" +
                        "<td>"+data.chequeDetails[i]["createName"]+"</td>" +
                        "<td>"+str3+"</td>" +
                        "<td>"+str2+"</td></tr>" ;
                } else{
                    str+="<tr><td>"+data.chequeDetails[i].chequeNo+"</td>" +
                        "<td>——</td>" +
                        "<td>——</td>" +
                        "<td>——</td>" +
                        "<td>"+str3+"</td>" +
                        "<td>"+str2+"</td></tr>" ;
                }

            }
            $(".msgXj").html("").append(str);
        },
        error:function(){
            alert("错误")
        } ,
        complete:function(){ loading.close() ;  }
    });
}
//切换到转账支票
function transfer( currentPageNo , pageSize  ) {
    $(".shuaxinXj").html("");
    $(".Con-pwd").on('click', '.tx2', function(event) {
        $(".transfer").siblings('.Con-pwd').children('.tx2').addClass('txn').siblings().removeClass('txn')
    });
    $("#addContact .bonceHead>span").html("转账支票录入<span style='display:none;'>1</span>");
    loading.open();
    let orgNameStr = ``, oid = null
    if (matchType) {$("#searchOrg").val() !== "" ? oid = $("#searchOrg").val(): "";}
//获取转账支票
    $.ajax({
        url:"../cheque/getCheque.do",
        data:{
            currentPageNo:currentPageNo ,
            pageSize: pageSize ,
            type:1,
            oid: oid
        },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;  ajaxArr[2] = 1 ;  } ,
        success:function(data){
            loading.close();
            var str="";
            let page =  data.data.pageInfo;
            let financeCheques = data.data.financeCheques;
            for(var i=0;i<financeCheques.length;i++){
                if (matchType) {orgNameStr = `<td> ${handleNull(financeCheques[i].orgName)} </td>`;}
                str+="<tr><td style='cursor:pointer;color: #3096DA;' onclick='transfer_cheque($(this));'><span style='display:none;'>"+financeCheques[i].id+"</span>"+financeCheques[i].beginNo+"-"+financeCheques[i].endNo+"</td>"+
                    "<td>"+financeCheques[i].bankName+financeCheques[i].account +orgNameStr+"</td><td>"+new Date(financeCheques[i].buyDate).format('yyyy-MM-dd')+"</td><td>"+financeCheques[i].buyerName+"</td><td>"+new Date(financeCheques[i].createDate).format('yyyy-MM-dd hh:mm:ss')+"</td><td>"+financeCheques[i].createName+"</td></tr>"
            }
            $(".transfer_con").html("").append(str);

            var pageNumber = page["currentPageNo"];
            var totalPage = page["totalPage"];
            $("#ye-transfer").html("");
            setPage( $("#ye-transfer") , pageNumber , totalPage , "checkManage_1" , "" )
        },
        error:function(){
            loading.close();
        } ,
        complete:function(){ chargeClose(2) ;  }
    });
}
//支票录入页面确定
/* updator : 侯杏哲，2017-02-15，改新增支票的传参  */
function saveContact(){
    var index=$(".bank").val();
    var accountId=$(".id"+index).html();//账户id
    var account=$(".account"+index).html();//账号
    var bankName=$(".bankName"+index).html();//银行名称
    var buyDate=$("#editcontact_mobile").val();//购买日期
    var buyerName=$("#editcontact_depart").val();//购买者
    var beginNo=$("#editcontact_zhi").val();//起始号码
    var endNo=$("#editcontact_email").val();//截止号码
    var type=$("#addContact .bonceHead>span>span").html();//支票类型
    if( $.trim(accountId)=="" || $.trim(account)==""  || $.trim(bankName)==""  || $.trim(buyDate)==""  || $.trim(buyerName)=="" || $.trim(beginNo)=="" || $.trim(endNo) == ""  ){
        $("#addXtip").html("以上各项均不能为空，请填写完整！");
    }else if( isNaN(Number(beginNo))|| isNaN(Number(endNo)) ) {
        $("#addXtip").html("起止号码格式不正确,请重新输入");
    }else if(parseInt(beginNo)<0||parseInt(beginNo)>**********){
        $("#addXtip").html("起始号码输入不规范，请重新输入");
    }else if(parseInt(endNo)<0||parseInt(endNo)>**********){
        $("#addXtip").html("截止号码输入不规范，请重新输入");
    }else if(parseInt(beginNo)>parseInt(endNo)){
        $("#addXtip").html("起始号码大于截止号码，请重新输入");
    }else{
        $.ajax({
            url:"../cheque/addCashCheque.do",
            data:{
                accountId : accountId ,
                account:account,
                bankName:bankName,
                buyDate1:buyDate,
                buyerName:buyerName,
                beginNo:beginNo,
                endNo:endNo,
                type:type
            },
            type:"post",
            dataType:"json",
            beforeSend:function(){ loading.open() ;  } ,
            success:function(data){
                if(data.status==1){
                    bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("录入成功");
                }else{
                    bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("录入失败");
                }
                bounce.cancel();
                if(type==2){
                    cash(1 , 20 );
                }else{
                    transfer(1 , 20 );
                }

            },
            error:function(){
                alert("错误")
            } ,
            complete:function(){ loading.close() ;  }
        });
    }
}
//转账支票详情
function transfer_cheque(obj) {
    $(".shuaxinZz").html(obj.parent().index());
    // $("#transfer").show().parent().show();
    bounce.show( $("#transfer") ) ;
    var chequeReg=obj.children("span").html();
    let orgAble = false;
    if (matchType && obj.parent("tr").children().eq(2).html() !== useInfo.name) {orgAble = true;}
    $.ajax({
        url:"../cheque/getCashChequeDetail.do",
        data:{  chequeReg:chequeReg   },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            console.log(data);
            var str="";
            for(let i=0;i<data.chequeDetails.length;i++){
                var str2='<span style="cursor:pointer;" class="ty-color-red" onclick="invalid($(this));" chequeNo="'+data.chequeDetails[i].chequeNo+'" value="'+data.chequeDetails[i].id+'">作废</span>';
                if(data.chequeDetails[i].state==2||data.chequeDetails[i].state==3 || orgAble){
                    str2="<span disabled='disabled' class='color6'>作废</span>" ;
                }
                var str3="未使用";
                if(data.chequeDetails[i].state==2){
                    str3="已使用";
                }else if(data.chequeDetails[i].state==3){
                    str3="已作废";
                }
                var amount = data.chequeDetails[i]["amount"] || '' ;
                if(amount == 0){ amount = "" ;  }
                str+="<tr><td>"+data.chequeDetails[i].chequeNo+"</td>" +
                    "<td>"+ amount +"</td><td>"+ new Date(data.chequeDetails[i].expireDate).format('yyyy-MM-dd')+"</td>" +
                    "<td>"+data.chequeDetails[i].receiveCorp+"</td>" +
                    "<td>"+new Date(data.chequeDetails[i].receiveDate).format('yyyy-MM-dd')+"</td>" +
                    "<td>"+data.chequeDetails[i].receiver+"</td>" +
                    "<td>"+data.chequeDetails[i].operator+"</td>" +
                    "<td>"+data.chequeDetails[i].financialHandling+"</td><td>"+str3+"</td><td>"+str2+"</td></tr>"
            }
            $(".msgZz").html("").append(str);
        },
        error:function(){
            alert("错误")
        } ,
        complete:function(){ loading.close() ;  }
    });
}
//详情页面的作废确定处理
function bounce_invalid(){
    var detailId=$("#invalid>span").html();
    $.ajax({
        url:"../cheque/updateState.do",
        data:{  detailId:detailId      },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            console.log(data);
           /* if(($(".shuaxinXj").html())!=""){
                $(".cash_con").children().eq( $(".shuaxinXj").html()).children().eq(0).click();
            }else{
                $(".transfer_con").children().eq( $(".shuaxinZz").html()).children().eq(0).click();
            }*/
            if(data.status==1){
                editSpan.attr("disabled","disabled").attr("class" , "color6").removeAttr("onclick") ;
                $("#altMS").html("操作成功") ; 
            }else{
                $("#altMS").html("操作失败")
            }
            bounce_Fixed.show( $("#alertMS") );

        },
        error:function(){
            alert("错误")
        } ,
        complete:function(){ loading.close() ;  }
    });
}
//二级弹框的取消按钮
function bounce_cancel1(){
    bounce_Fixed($("#invalid") );
}

// -------------------多地点-分支机构-------------------

function screenOrgData(){
    init();
}
//creator:liyuting date:2022/08/12 可选择的机构列表
function getOrgSonOrgs(){
    $.ajax({
        async: false,
        url:'../sonOrg/getOrgSonOrgs.do',
        success:function(data){
            let list = data.data;
            let options = `<option value="">请选择机构</option>`;
            if (list && list.length > 0) {
                for(var i=0;i<list.length;i++) {
                    options += `<option value="${list[i].id}">${list[i].name}</option>`;
                }
            }
            if (useInfo.orgType === 1 && list.length > 1){//orgType 是4 就是子机构， 是1 就是总机构
                matchType = true;
                $(".screenBody").show();
                $(".belongOrg").show();
            } else {
                matchType = false;
                $(".screenBody").hide();
                $(".belongOrg").hide();
            }
            $("#searchOrg").html(options);
        }
    })
}

//作废按钮
var editSpan = null ;
function invalid(obj) {
    editSpan = obj ;
    if(chargeRole("超管")){ // 0 是超管
        $("#altMS").html("您没有此操作权限！");
        bounce_Fixed.show($("#alertMS"));
    }else{
        $("#invalid>span").html(obj.attr("value"));
        bounce_Fixed.show($("#invalid")) ;
    }
    $("#invalid #mt_invalid1").html("支票号：" + obj.parent().siblings(":eq(0)").html() );
}
// 日期控件
laydate.render({
    elem: '#editcontact_mobile', //目标元素。由于laydate.js封装了一个轻量级的选择器引擎，因此elem还允许你传入class、tag但必须按照这种方式 '#id .class'
    event: 'focus' //响应事件。如果没有传入event，则按照默认的click
});