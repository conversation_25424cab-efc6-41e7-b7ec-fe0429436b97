$(function () {
    // 获取主列表
    getList() ;

});
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#startTip"));
bounce_Fixed2.cancel();
// creator : hxz 2018-07-20  发票设置列表
function getList() {
    $.ajax({
        "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
        success: function (res) {
            var list = res["financeInvoiceSettings"];
            var str = "";
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var enableTaxTate = list[i]["enableTaxTate"] || "— —" ;
                    if(enableTaxTate != "— —"){
                        enableTaxTate = enableTaxTate.replace(/,/g, "%, ") ; enableTaxTate += "% " ;
                    }
                    var cate = list[i]["category"] ;
                    var amountLimited = list[i]["amountLimited"] + "元" ;
                    if(cate == 1){
                        amountLimited = "不含税" + amountLimited   ;
                    }else if(cate == 2){
                        amountLimited = "含税" + amountLimited  ;
                    }
                    str += "<tr>" +
                        "    <td>" + category(list[i]["category"]) + "</td>" +
                        "    <td>" + (list[i]["hasAttachment"] ? "有" : "无") + "</td>" +
                        "    <td>" + (list[i]["linesLimited"]||"——") + "</td>" +
                        "    <td>" + enableTaxTate + "</td>" +
                        "    <td>" + amountLimited + "</td>" +
                        "    <td>" + new Date(list[i]["createDate"]).format('yyyy-MM-dd hh:mm:ss') + "</td>" +
                        "    <td>" + list[i]["createName"] + "</td>" +
                        "    <td>" +
                        "       <span class='ty-color-blue' onclick='editBtn($(this))'>修改</span>" +
                        "       <span class='ty-color-blue' onclick='editeLog($(this))'>修改记录查看</span>" +
                        "       <span class='hd'>" + list[i]["id"] + "</span>" +
                        "       <input type='hidden' value='"+ list[i]["category"] +"'>" +
                        "    </td>" +
                        "</tr>";
                }
                if(list.length != 3) {
                    $("#addBtn").attr("class","ty-btn ty-btn-green ty-btn-big ty-circle-5 ty-left").attr("onclick" , "addBtn()") ;
                }else{
                    $("#addBtn").attr("class","ty-btn ty-btn-gray ty-btn-big ty-circle-5 ty-left").removeAttr("onclick") ;
                }
            }else{
                $("#addBtn").attr("class","ty-btn ty-btn-green ty-btn-big ty-circle-5 ty-left").attr("onclick" , "addBtn()") ;
            }
            $("#mainList tbody").html(str);

        }
    });
}
// creator : hxz 2018-07-20  新增发票设置
var formValid = null;
function addBtn() {
    $("#type").val("add");
    $("#invoiceSetTtl").html("发票设置");
    bounce.show($("#invoiceSet"));
    $("#invoiceSet .bonceCon>div").hide();
    init($("#invoiceSet .bonceCon>div"));
    $("#invoiceSet .bonceCon>div:eq(0)").show();
    $("#stopLv").hide();
    $(".mask").hide();
    formValid = setInterval(function () {
        valid();
    }, 300);
    $("#mainList tbody").children().each(function(){
        var category = $(this).find("input").val() ;
        $("#setCatory" + category)&&$("#setCatory" + category).children(".mask").show();
    }) ;
    $("#setCatoryCon").children().show() ;
    $("#setCatoryCon").children("p").html("请选择发票种类") ;
}
// creator : hxz 2018-07-20  修改发票设置
function editBtn(thisObj) {
    var settingId = thisObj.siblings(".hd").html();
    $("#type").val("edit");
    $("#invoiceSetTtl").html("修改发票设置");
    bounce.show($("#invoiceSet"));
    $("#invoiceSet .bonceCon>div").hide();
    init($("#invoiceSet .bonceCon>div"));
    $("#invoiceSet .bonceCon>div:eq(0)").show();
    formValid = setInterval(function () {
        valid();
    }, 300);
    $("#stopLv").show();
    // 获取发票详情
    $.ajax({
        "url": "../invoiceSetting/getSettingDetail.do",
        "data": {"settingId": settingId},
        success: function (res) {
            var info = res["financeInvoiceSetting"];
            var type = info["category"];  // 发票种类 ， 需要返回的
            var fu = info["hasAttachment"] ? 1 : 0;  // 发票有无附页， 需要返回的 true-是/false-否；
            var amountNum = info["amountLimited"]; // 每张发票能开的金额上限；
            $("#id").val(info["id"]);
            setCatory($("#setCatory" + type), type, 1);
            setFu($("#setFu" + fu), fu);
            if (type == 1 || type == 2) {
                if (fu == 0) {
                    var hangNum = info["linesLimited"];  // 每张发票能开的行数上限；
                    $("#hangNum").val(hangNum);
                }
                var _setDiffLv = info["multipleRate"] ? 1 : 0; // 是否包含多税率,true-是/false-否；
                setDiffLv($("#setDiffLv" + _setDiffLv), _setDiffLv);
                var lvsList = info["enableTaxTate"].split(","), lvStr = "";
                for (var i = 0; i < lvsList.length; i++) {
                    var ifo = {"type": 0, "lv": lvsList[i]};
                    lvStr += "<span class='lv_item'>" +
                        "    <span> " + lvsList[i] + "% </span>" +
                        "    <span class='hd'>" + JSON.stringify(ifo) + "</span>" +
                        "    <a class='lv_control' onclick='changeLv($(this))'>停用</a>" +  // 启用 禁用的状态需要分情况判断
                        "</span>";
                }
                $("#lvs").html(lvStr);

            } else {
                var hangNum = info["linesLimited"];  // 每张发票能开的行数上限；
                $("#hangNum").val(hangNum)
            }
            $("#amountNum").val(amountNum);
            $("#disableTaxRate").val(info["disableTaxRate"]) ;
        }
    }) ;
    $("#setCatoryCon").children().hide() ;
    $("#setCatoryCon").children("p").html("请修改 "+  thisObj.parent().siblings(":eq(0)").html() +" 的发票设置").show() ;

}
// creator: hxz 2018-08-16 修改发票
function changeLv(obj) {
    var info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    var type = info["type"]; // 0 - 原来就有的 1 -新增的 2 - 启用的 3 - 停用的
    if (type == 0) { // 当前原有启用 - 改为 停用
        type = 3;
        obj.html("还原");
        obj.parent().addClass("lv_gray");
    } else if (type == 2) { // 原来停用的改为启用（当前）的后 - 再改为 停用
        obj.parent().remove();
    } else if (type == 3) { // 原来启用的改为停用（当前）后 - 再改为 启用
        type = 0;
        obj.html("停用");
        obj.parent().removeClass("lv_gray");
    }
    info["type"] = type;
    obj.siblings(".hd").html(JSON.stringify(info));
}
// creator: hxz 2018-07-20 确定 新增、修改 发票设置
function addInvoiceOk() {
    var type = $("#type").val();
    var catory = $("#setCatory").val(); // 发票类型
    var fu = Boolean(Number($("#setFu").val())); // 是否含有附件,true-是/false-否；
    var amountNum = $("#amountNum").val(); // 每张发票能开的金额上限
    var hangNum = $("#hangNum").val(); // 每张发票能开的行数上限
    var diffLv = Boolean(Number($("#setDiffLv").val()));
    var lvs = "";
    var addTaxRate = ""; // 新添加的税率
    var startTaxRate = ""; // 启用的税率
    var stopTaxRate = ""; // 要停用的税率
    $("#lvs").find(".lv_item").each(function () {
        var info = $(this).find("span.hd").html();
        info = JSON.parse(info);
        var lv = info["lv"];
        var tp = info["type"]; // 0 - 原来就有的 1 -新增的 2 - 启用的 3 - 停用的
        if (tp != 3) {
            lvs += lv + ",";
        }
        if (type == "edit") {
            if (tp == 1) {
                addTaxRate += lv + ",";
            } else if (tp == 2) {
                startTaxRate += lv + ",";
            } else if (tp == 3) {
                stopTaxRate += lv + ",";
            }
        }
    });
    lvs = lvs.substr(0, lvs.length - 1);
    stopTaxRate = stopTaxRate.substr(0, stopTaxRate.length - 1);
    addTaxRate = addTaxRate.substr(0, addTaxRate.length - 1);
    startTaxRate = startTaxRate.substr(0, startTaxRate.length - 1);

    var url = "../invoiceSetting/addInvoiceSetting.do";
    var data = {
        "linesLimited": hangNum,
        "amountLimited": amountNum,
        "category": catory,
        "hasAttachment": fu,
        "multipleRate": diffLv,
        "enableTaxTate": lvs
    };
    if (type == "edit") {
        var id = $("#id").val();
        url = "../invoiceSetting/updateInvoiceSetting.do";
        data = {
            "addTaxRate": addTaxRate, // 新添加的税率,多税率以,分隔;
            "stopTaxRate": stopTaxRate, // 要停用的税率,多税率以,分隔;
            "startTaxRate": startTaxRate, // 要启用的税率,多税率以,分隔；
            "linesLimited": hangNum, // 每张发票能开的行数上限；
            "amountLimited": amountNum, // 每张发票能开的金额上限；
            "category": catory, // 发票类型:1-增值税专用票,2-增值税普通票,3-其它普通票；
            "hasAttachment": fu, // 是否含有附件,true-是/false-否；
            "multipleRate": diffLv, // 是否包含多税率,true-是/false-否；
            "enableTaxTate": lvs, // 有效税率,多税率以,分隔；
            "id": id  // 发票设置id
        };
    }
    clearInterval(formValid);
    $.ajax({
        "url": url,
        "data": data,
        success: function (res) {
            bounce.cancel();
            var state = res["state"];
            if (type == "edit") {
                var state = res["status"];
                // 0-修改失败 1-系统中有相同的税率  2-修改成功
                if (state == 1) {
                    layer.msg("系统中有相同的税率");
                } else if (state == 2) {
                    layer.msg("修改成功");
                    getList();
                } else if (state == 0) {
                    layer.msg("操作失败");
                } else {
                    layer.msg("未知原因，操作失败！");
                }
            } else {
                // state： 0-添加失败  1-添加成功  2-系统中已存在此发票类型
                if (state == 1) {
                    layer.msg("操作成功");
                    getList();
                } else if (state == 2) {
                    layer.msg("系统中已存在此发票类型");
                } else if (state == 0) {
                    layer.msg("操作失败");
                } else {
                    layer.msg("未知原因，操作失败！");
                }
            }

        }
    });
}
// creator : hxz 2018-07-20  查看 停用的税率
function scanStopLvs(type) { // type =1 标识修改记录里面的
    var settingId = $("#id").val();
    var settingHistoryId = "";
    if(type == 1){
        settingId =  $("#logSetID").val() ;
        settingHistoryId = $("#logID").val() ;
    }
    $.ajax({
        "url": "../invoiceSetting/deactivatedTaxRate.do",
        "data": {"settingId": settingId , "settingHistoryId": settingHistoryId },
        success: function (res) {
            var list = res["rateHistories"];
            var str = "";
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var lv_item = list[i]["rate"];
                    var isEx = false;
                    if(type != 1){
                        $("#lvs").children(".lv_item").each(function () {
                            var info = $(this).find(".hd").html();
                            info = JSON.parse(info);
                            var lv_i = info["lv"];
                            if (lv_item == lv_i) {
                                isEx = true;
                            }
                        });
                    }
                    if (!isEx) {
                        var stopInfoList = list[i]["financeInvoiceRateHistorys"];
                        var startTimeStr = "", startNameStr = "", stopTimeStr = "", stopNameStr = "",
                            createTimeStr = "", createNameStr = "";
                        if (stopInfoList && stopInfoList.length > 0) {
                            for (var j = 0; j < stopInfoList.length; j++) {
                                var updateDate = new Date(stopInfoList[j]["updateDate"]).format('yyyy-MM-dd hh:mm:ss');
                                var updateName = stopInfoList[j]["updateName"];
                                var enabled = stopInfoList[j]["enabled"]; // 启用标志:true-启用 , false-停用
                                if (enabled) { // 启用
                                    var createDate = new Date(stopInfoList[j]["createDate"]).format('yyyy-MM-dd hh:mm:ss');
                                    var createName = stopInfoList[j]["createName"];
                                    if (createDate) { // 创建的
                                        stopTimeStr += "<div></div>";
                                        stopNameStr += "<div></div>";
                                        startTimeStr += "<div></div>";
                                        startNameStr += "<div></div>";
                                        createTimeStr += "<div>" + createDate + "</div>";
                                        createNameStr += "<div>" + createName + "</div>";
                                    } else { // 启用的
                                        stopTimeStr += "<div></div>";
                                        stopNameStr += "<div></div>";
                                        startTimeStr += "<div>" + updateDate + "</div>";
                                        startNameStr += "<div>" + updateName + "</div>";
                                        createTimeStr += "<div></div>";
                                        createNameStr += "<div></div>";
                                    }
                                } else {
                                    stopTimeStr += "<div>" + updateDate + "</div>";
                                    stopNameStr += "<div>" + updateName + "</div>";
                                    startTimeStr += "<div></div>";
                                    startNameStr += "<div></div>";
                                    createTimeStr += "<div></div>";
                                    createNameStr += "<div></div>";
                                }

                            }
                        }
                        str += "<tr>" +
                            "     <td>" + list[i]["rate"] + "%</td>" +
                            "     <td>" + stopTimeStr +
                            "     </td>" +
                            "     <td>" + stopNameStr +
                            "     </td>" +
                            "     <td>" + startTimeStr +
                            "     </td>" +
                            "     <td>" + startNameStr +
                            "     </td>" +
                            "     <td>" + createTimeStr +
                            "     </td>" +
                            "     <td>" + createNameStr +
                            "     </td>" ;
                        if(type != 1){
                            str +=  "<td><span class=\"ty-color-blue\" onclick=\"startBtn($(this))\">启用</span></td>";
                        }
                        str +=  "</tr>";
                    }
                }
            }
            if(type == 1){
                bounce_Fixed2.show($("#scanStopLvs2"));
                $("#scanStopLvs2 tbody").html(str);
            }else{
                bounce_Fixed.show($("#scanStopLvs"));
                $("#scanStopLvs tbody").html(str);
            }
        }
    })
}
// creator : hxz 2018-08-16  启用已停用的税率
var editTr = null;
function startBtn(obj) {
    editTr = obj.parents("tr");
    bounce_Fixed2.show($("#startTip"));
    $("#stLv").html(obj.parent().siblings(":eq(0)").html());
}
// creator : hxz 2018-08-16  确定启用税率
function startLv() {
    var lvStr = $("#stLv").html();
    var lv = lvStr.split("%")[0];
    var str = "<span class='lv_item'>" +
        "    <span> " + lvStr + " </span>" +
        "    <span class='hd'>" + JSON.stringify({"type": 2, "lv": lv}) + "</span>" +
        "    <a class='lv_control' onclick='changeLv($(this))'>还原</a>" +
        "</span>";
    $("#lvs").append(str);
    editTr.remove();
    bounce_Fixed2.cancel();
}
// creator : hxz 2018-07-20  查看 发票设置 修改记录
function editeLog(obj) {
    var id = obj.siblings(".hd").html();
    $("#logSetID").val(id) ;
    $.ajax({
        "url": "../invoiceSetting/getModifyRecord.do",
        "data": {"invoiceSettingId": id},
        success: function (res) {
            var status = res["status"];
            if (status == 1) {
                var list = res["invoiceSettingHistories"];
                bounce.show($("#editLog"));
                var str = "";
                $("#editLogTb").find("tr:gt(0)").remove();
                if (list && list.length > 0) {
                    for (var i = 0; i < list.length; i++) {
                        str += "<tr>" +
                            "    <td>" + (new Date(list[i]["updateDate"]).format('yyyy-MM-dd hh:mm:ss')) + "</td>" +
                            "    <td>" + list[i]["updateName"] + "</td>" +
                            "    <td class=\"ty-td-control\">" +
                            "        <span onclick=\"editeLogInfo($(this) , 0)\" class=\"ty-color-blue\">查看</span>" +
                            "    </td>" +
                            "    <td class=\"ty-td-control\">" +
                            "        <span onclick=\"editeLogInfo($(this) , 1)\" class=\"ty-color-blue\">查看</span>" +
                            "        <span class='hd'>" + JSON.stringify(list[i]) + "</span>" +
                            "    </td>" +
                            "</tr>";
                    }
                }
                $("#editLogTb").append(str);
            } else {
                layer.msg("获取修改记录失败");
            }
        }
    })
}
// creator : hxz 2018-07-20   查看 发票设置 修改记录详情
function editeLogInfo(thisObj, type) { // type: 0 - 修改前 ， 1 - 修改后
    bounce_Fixed.show($("#editeLogInfo"));
    var info = thisObj.parents("tr").children(":last").children(".hd").html();
    info = JSON.parse(info);
    var id = info["previousId"]; // 修改前
    $("#infoTtl").html("修改前详情") ;
    if (type == 1) { // 修改后
        id = info["id"] ;
        $("#infoTtl").html("修改后详情") ;
    }
    $("#logID").val(id) ;
    $.ajax({
        "url": "../invoiceSetting/getInvoiceSettingDetail.do",
        "data": {"invoiceSettingHistoryId": id},
        success: function (res) {
            var detail = res["financeInvoiceSettingHistory"];
            if(detail){
                $("#logCat").html( category(detail["category"]) ) ;
                $("#logFu").html( (detail["hasAttachment"]?"有":"无") ) ;
                $("#logAmount").html(detail["amountLimited"]);
                if(detail["category"] == 1){ // 增值税专用发票才有税率
                    $("#editeLogInfo .bonceCon").children("div:eq(3)").show() ;
                    $("#editeLogInfo .bonceCon").children("div:eq(4)").show() ;
                    $("#logMultipleRate").html( (detail["multipleRate"]?"是":"否") ) ;
                    var lvsList = detail["enableTaxTate"].split(","), lvStr = "";
                    for (var i = 0; i < lvsList.length; i++) {
                        var ifo = {"type": 0, "lv": lvsList[i]};
                        lvStr += "<span class='lv_item'>" +
                            "    <span> " + lvsList[i] + "% </span>" +
                            "</span>";
                    }
                    $("#lvs_info").html(lvStr);
                    $("#amountTtl2").html("每张发票可开的金额上限（不含税）");

                }else{
                    if(detail["category"] == 2){
                        $("#amountTtl2").html("每张发票可开的含税金额上限");
                    }else if(detail["category"] == 3){
                        $("#amountTtl2").html("每张发票可开的金额上限");
                    }
                    $("#editeLogInfo .bonceCon").children("div:eq(3)").hide() ;
                    $("#editeLogInfo .bonceCon").children("div:eq(4)").hide() ;
                }
                if(detail["hasAttachment"]){ // 有附件页
                    $("#infoHang").parent().hide() ;  }else{ $("#infoHang").parent().show() ; $("#logHang").html(detail["linesLimited"]) ;  }

            }else{
                layer.msg("获取数据失败") ;
            }


        }
    });
}
// creator : hxz 2018-07-20   发票设置 - 选择 发票种类
function setCatory(thisObj, type) { // type:发票类型  status：是否为修改（1-是）
    init(thisObj.parents(".item_i").parent().nextAll());
    $("#setCatory").val(type);
    thisObj.find("i.fa").attr("class", "fa fa-dot-circle-o");

    thisObj.parent().siblings(".item_i").each(function () {
        $(this).find("i.fa").attr("class", "fa fa-circle-o");
    }) ;

    // 显示 发票有无附页 ， 隐藏下面的
    $("#invoiceSet .bonceCon>div:gt(0)").show();
    $("#invoiceSet .bonceCon>div:eq(2)").hide();

    var amountStr = "每张发票可开的金额上限（不含税）<i>*</i>";
    if (type == 2) {
        amountStr = "每张发票可开的含税金额上限 <i>*</i>";
        $("#invoiceSet .bonceCon>div:eq(3)").hide();
        $("#invoiceSet .bonceCon>div:eq(4)").hide();
    } else if (type == 3) {
        amountStr = "每张发票可开的金额上限 <i>*</i>";
        $("#invoiceSet .bonceCon>div:eq(3)").hide();
        $("#invoiceSet .bonceCon>div:eq(4)").hide();
    }
    $("#amountTtl").html(amountStr);
}
// creator : hxz 2018-07-20   发票设置 - 选择 发票有无附页
function setFu(thisObj, type) {
    $("#setFu").val(type);
    thisObj.children("i.fa").attr("class", "fa fa-dot-circle-o");
    thisObj.siblings(".item_i").children("i.fa").attr("class", "fa fa-circle-o");
    $("#hangNum").val("");
    if (type == 0) {
        $("#invoiceSet .bonceCon>div:eq(2)").show();
    } else {
        $("#invoiceSet .bonceCon>div:eq(2)").hide();
    }

}
// creator : hxz 2018-07-20   发票设置 - 选择 发票有无附页
function setDiffLv(thisObj, type) {
    $("#setDiffLv").val(type);
    thisObj.children("i.fa").attr("class", "fa fa-dot-circle-o");
    thisObj.siblings(".item_i").children("i.fa").attr("class", "fa fa-circle-o");
}
// creator : hxz 2018-07-20   发票设置 - 增加可供选择的税率
function addLv() {
    bounce_Fixed.show($("#addLvCon"));
    $("#add_lv").val("");
}
function addLvOk() {
    var lv = $("#add_lv").val();
    if ($.trim(lv) == "") {
        layer.msg("请输入税率");
        return;
    }
    var lv = parseFloat(lv);
    if (lv > 100) {
        layer.msg("税率不合法");
        return;
    }
    var lvStr = lv + "%";
    var same = false;
    $("#lvs").children(".lv_item").each(function () {
        var ifo = $(this).find(".hd").html();
        ifo = JSON.parse(ifo);
        var i_lv = ifo["lv"];
        if (i_lv == lv) {
            same = true;
        }
    });
    if (same) {
        layer.msg("系统中已存在此税率，不能重复");
        return;
    }
    var disRates = $("#disableTaxRate").val() ;
    if(disRates.indexOf(lv) != -1){
        layer.msg("该税率已被停用。<br>您可在“已停用的税率”中将其重新启用");
        return;
    }

    var info = {
        "type": 1, // 0 - 原来就有的税率 1-新增的税率 ， 2 - 启用的税率
        "lv": lv
    };
    var str = "<span class=\"lv_item\">" +
        "    <span>" + lvStr + "</span>" +
        "    <span class='hd'>" + JSON.stringify(info) + "</span>" +
        "    <a class='lv_control' onclick='deleLv($(this))'>删除</a>" +
        "</span>";
    $("#lvs").append(str);
    bounce_Fixed.cancel();
}
function deleLv(obj) {
    obj.parent().remove();
}
// creator : hxz 2018-07-20  工具方法 ， 返回发票种类
function category(type) {
    if (type == 1) {
        return "增值税专用票";
    } else if (type == 2) {
        return "增值税普通票";
    } else if (type == 3) {
        return "其他普通票";
    } else {
        return "";
    }
}
// creator : hxz 2018-07-20  工具方法 ， 判断 确定 按钮能不能点
function valid() {
    var isGray = returnCharge();
    if (isGray) {
        $("#addInvoiceOk").attr("onclick", "addInvoiceOk()").attr("class", "ty-btn ty-btn-green ty-btn-big ty-circle-3");
    } else {
        $("#addInvoiceOk").removeAttr("onclick").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3");
    }
}
function returnCharge() {
    var type = $("#setCatory").val(); // 发票类型
    var fu = $("#setFu").val();
    var amountNum = $("#amountNum").val();
    if (type == "" || amountNum == "" || fu == "") {
        return false;
    }
    if (fu === "0") {
        var hangNum = $("#hangNum").val();
        if (hangNum == "") {
            return false;
        }
    }
    if (type == 1) {
        var lvLen = $("#lvs").children(".lv_item:not('.lv_gray')").length;
        if (lvLen == 0) {
            return false;
        }
    }
    return true;
}
// creator : hxz 2018-07-20   发票设置 - 重置数据
function init(selectors) {
    if (selectors && selectors.length > 0) {
        selectors.each(function () {
            $(this).find("i.fa").attr("class", "fa fa-circle-o");
            $(this).find("input").val("");
            $(this).find("div#lvs").html("");
        })
    }
}








