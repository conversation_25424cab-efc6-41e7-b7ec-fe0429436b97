var uploadCode = "" // 标记每次添加的代号
var uploadType = "" // 上传文件类型:1-照片,2-其他
$(function(){
    // 分工设置三种状态切换
    $(".divisionPage .ty-secondTab li").on("click", function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        //获取点击按钮的下标
        var index = $(this).index();
        $(".divisionPage .tblContainer").eq(index).show().siblings(".tblContainer").hide();
        getDivisionList();
    });
    // 给上传按钮绑定事件
    $(".fileCon").on("click",".uploadify-button", function() {
        uploadCode = 'f'+ Math.random().toString(36).substr(2);
        var id = $(this).attr("id")
        var num = id.split("_")[2];
        num = num.split("-")[0];
        if(num == 1 || num == 3){
            uploadType = 1 ;  //
        }else{
            uploadType = 2 ;
        }
    });
    // 根据是否核心人物，初始化新增投诉
    getCoreUser();
});
//----------------- 新增投诉 -------------------//
// creator：hxz，2019-05-23  初始化新增投诉需要的数据
var timerObj = { "timer":0 , "level1":0 , "he":0 } ; // timer-主页面的检验定时器，level1-是否开始了投诉表的填写，he-是不是核心人物
var cusList = [] ; chargerList = [] ; // 客户列表， 处理者列表
function getCoreUser() {
    $.ajax({
        "url":"../coreSetting/getCoreUser.do",
        success:function(data) {
            var coreUserID = data["coreUser"]["userID"] ;
            var curUserID = sphdSocket.user.userID ;
            if(coreUserID == curUserID){
                timerObj.he = 1 ; // 当前是核心人物
                // 订阅新增立案(核心人物)
                sphdSocket.subscribe('pendingComplaintEnd', addCallBack, function(){ console.log('新增立案失败！')},'user');
            }else {
                // 订阅新增立案(立案者)
                sphdSocket.subscribe('complaintEntry', addCallBack, function(){ console.log('新增立案失败！')},'user');
            }
            initAdd() ;
        },
        complete:function(){}
    });
}
function initAdd(cusID) {
    $(".main").find("input").val("");
    $(".main").find("textarea").val("");
    $(".main").find("select").val("");
    timerObj.level1 = 0 ;
    $(".yesPro").hide();
    $(".noPro").hide();
    $(".chargePro").hide();
    $(".blueTr").hide();
    if(timerObj.he){
        $(".notHe").hide();
        $(".isHe").show();
        // 获取处理者列表
        $.ajax({
            url:"../coreSetting/getThreeUser.do" ,
            data:{
                "coreCode":"handle"
            },
            success:function(data) {
                var list2 = data["codeUsers"], strCharger = "<option value=''>—— 请选择 ——</option>";
                if(list2 && list2.length>0){
                    for(var j = 0 ; j < list2.length ; j++){
                        strCharger +="<option value='"+ list2[j]["userID"] +"'>"+ list2[j]["userName"] +"</option>";
                    }
                }
                $("#charger").html(strCharger);
            }
        });
    }else{
        $(".notHe").show();
        $(".isHe").hide();
    }
    // 获取客户列表
    $.ajax({
        "url":"../invoice/getAllCustomer.do",
        success:function(data) {
            var list = data["Customers"], strCus = "<option value=''>—— 请选择客户 ——</option>";
            if(list && list.length>0){
                cusList = list ;
                for(var i = 0 ; i < list.length ; i++){
                    if(cusID && cusID ==list[i]["id"]){
                        $("#cusName").val(list[i]["fullName"]) ;
                        $("#cusCode").val(list[i]["code"]) ;
                        $("#cusIdOld").val(cusID) ;
                        strCus +="<option selected value='"+ list[i]["id"] +"'>"+ list[i]["fullName"] +"</option>";
                    }else{
                        strCus +="<option value='"+ list[i]["id"] +"'>"+ list[i]["fullName"] +"</option>";
                    }
                }
            }
            $("#cusID").html(strCus);
        },
        complete:function() {
            if(!timerObj.he){
                loading.close();
            }
        }
    });
    // 定时器，没计划关掉
    timerObj.timer = setInterval(function() {
        var charger =  $("#charger").val();
        var cusName =  $("#cusName").val();
        var bool = timerObj.he ? Boolean(cusName && charger) : Boolean(cusName) ;
        if(bool){
            if(timerObj.level1 != 1){
                $(".chargePro").show();
                $(".blueTr").show();
            }
        }else{
            if(timerObj.level1 != 1){
                $(".chargePro").hide();
                $(".blueTr").hide();
            }
        }
    }, 200)
}
// creator：hxz，2019-05-23  清空客户时给与提示
function chargeCus() {
    var cusID = $("#cusID").val();
    var cusIdOld = $("#cusIdOld").val();
    if(cusIdOld == ""){
        for(var i = 0 ; i < cusList.length ; i++){
                   if(cusList[i]["id"] == cusID){
                       $("#cusName").val(cusList[i]["name"]) ;
                       $("#cusCode").val(cusList[i]["code"]) ;
                       $("#cusIdOld").val(cusID) ;
                   }
               }
    }else{
        bounce.show($("#changeCus")) ;

    }
}
function emptyCus(type) {
    bounce.cancel();
    if(type){ // 清空
        var cusID = $("#cusID").val();
        initAdd(cusID);
    }else{ // 不清空
        $("#cusID").val($("#cusIdOld").val())
    }
}
// creator：hxz，2019-05-24 选择是否跟产品有关
function addBtn(type) {
    timerObj.level1 = 1 ;
    $(".chargePro").hide();
    var amount = $("#pro_amount").val();
    var percent = $("#pro_percent").val();
    if(amount && Number(amount)>999999999){
        layer.msg("问题产品数量请输入小于 999999999 的具体数字"); return false;
    }
    if(percent && (percent < 0 || percent > 100)){
        layer.msg("问题产品比例应介于0-100之间！"); return false;
    }
    if(type){ // 新增/修改问题产品
        $("#type").val(1);
        $(".yesPro").show();
        $(".noPro").hide();
        bounce.cancel();
        clearInterval(time2);
        var info = {
            'problem': $("#pro_problem").val() ,
            'consequence': $("#pro_consequence").val() ,
            'appeal': $("#pro_appeal").val() ,
            'amount': amount ,
            'percent': percent ,
            'customerProduct': $("#gsID").val() ,
            'memo': $("#pro_memo").val()
        }
        info["listAttachment"] = [] ;
        $("#selectGoods").find(".upItem").each(function(){
            var path = $(this).attr("path") ;
            var title = $(this).attr("title") ;
            var type = $(this).attr("type") ; // 类型:1-照片,2-其他
            info["listAttachment"].push({ "path": path, "title":title, "type":type })
        })
        var list = []
        var gouparr1 = $('#imgUploadProBtn').data("gouparr")||[];
        var gouparr2 = $('#fileUploadProBtn').data("gouparr")||[];
        list.push(...gouparr1, ...gouparr2);
        info['fileDelByGroup'] = list
        info["listProductOutput"] = [] ; // 商品详细签收时间列表
        $("#timeCon").children("span").each(function() {
            var outputStockSn = $(this).attr("timeSn");
            var outputTime  = "", timeList = outPutTim;
            for(var i = 0 ; i < timeList.length; i++){
                if(timeList[i]["outputStockSn"] == outputStockSn){
                    outputTime = (new Date(timeList[i]["outputTime"]).format("yyyy-MM-dd")) ;
                }
            }
            info["listProductOutput"].push({ "outputTime":outputTime, "outputStockSn":outputStockSn }) ;
        })

        var editGsType = $("#editGsType").val();
        if(editGsType == "update"){ // 修改情况
            editGs.siblings(".hd").html(JSON.stringify(info))
            var tdObj = editGs.parent();
            tdObj.siblings(":eq(0)").html($("#outSn").val());
            tdObj.siblings(":eq(1)").html($("#outName").val());
            tdObj.siblings(":eq(2)").html($("#inSn").val());
            tdObj.siblings(":eq(3)").html($("#inName").val());
        }else{ // 新增情况
            var strTr =
                "<tr>" +
                "    <td>"+ $("#outSn").val() +"</td>" +
                "    <td>"+ $("#outName").val() +"</td>" +
                "    <td>"+ $("#inSn").val() +"</td>" +
                "    <td>"+ $("#inName").val() +"</td>" +
                "    <td>" +
                "        <span class=\"ty-color-blue\" onclick='updateGs($(this))'>修改</span>" +
                "        <span class=\"ty-color-red\" onclick='delGs($(this))'>删除</span>" +
                "        <span class=\"hd\">"+ JSON.stringify(info) +"</span>" +
                "    </td>" +
                "</tr>";
            $("#goodsList").append(strTr) ;
        }

    }else{ // 跟产品无关
        $("#type").val(2);
        $(".yesPro").hide();
        $(".noPro").show();
        $('#imgUploadBtn').html("");
        $('#fileUploadBtn').html("");
        $('#imgUploadBtn').data("gouparr",[]);
        $('#fileUploadBtn').data("gouparr",[]);
        initUploadify($('#imgUploadBtn'), "img", 1);
        initUploadify($('#fileUploadBtn'), "file", 2);
    }
}
var time2 = 0; // 选择问题产品的必填检验定时器
var gsList = []; // 商品列表
var outPutTim = [] ; // 签收时间列表
// creator：hxz，2019-05-23  选择问题产品
function selectBtn(type) {
    bounce.show($("#selectGoods"));
    $("#selectGoods").find("input").val("");
    $("#selectGoods").find("textarea").val("");
    $("#selectGoods").find("select").val("");
    $('#timeCon').html("");
    $("#selectGoods .red").html($("#cusName").val());
    $('#imgUploadProBtn').html("");
    $('#fileUploadProBtn').html("");
    $('#imgUploadProBtn').data("gouparr",[]);
    $('#fileUploadProBtn').data("gouparr",[]);
    initUploadify($('#imgUploadProBtn'), "img", 3);
    initUploadify($('#fileUploadProBtn'), "file", 4);
    time2 = setInterval(function() {
        var outCode = $("#outSn").val();
        var outName = $("#outName").val();
        var inCode = $("#inSn").val();
        var inName = $("#inName").val();
        if(outCode && outName && inCode && inName){
            $("#addGoods").attr("class","ty-btn ty-btn-green ty-btn-big ty-circle-3").attr("onclick", "addBtn(1)");
        }else{
            $("#addGoods").attr("class","ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick");
        }
    },200);

    if(type){
        resetGsList();
    }else{
        $("#goodsList tbody").children("tr:gt(0)").remove();
        var userID = sphdSocket.user.userID ;
        $.ajax({
            "url": "../complaint/getCustomerProduntByComplaint.do",
            "data":{ "customer": $("#cusID").val(), "userID": userID },
            success:function(res) {
                var list = res["data"]["listProduct"] ;
                if(list && list.length>0){
                    gsList = list ;
                    resetGsList();
                }else{
                    layer.msg("当前没有可投诉的商品！")
                }
            }
        });
    }

}
// creator：hxz，2021-3-15  产品的取消
function cancelSubmitfilepro(){
    bounce.cancel();
    var list = []
    var gouparr1 = $('#imgUploadProBtn').data("gouparr")||[];
    var gouparr2 = $('#fileUploadProBtn').data("gouparr")||[];
    console.log('两个要取消的数组：')
    console.log(gouparr1)
    console.log(gouparr2)
    list.push(...gouparr1, ...gouparr2);
    // 删除已经上传的文件
    list.forEach(function (item) {
        fileDelAjax(item);
    })
}
// creator：hxz，2019-05-29 重置内商品代号、名称下拉框的连动
function resetGsList(type) {
    var curTypeVal = $("#"+type).val(),
        gs = gsList,
        outSnStr = "<option value=''>—— 请选择 ——</option>" ,
        inSnStr = "<option value=''>—— 请选择 ——</option>" ,
        outNameStr = "<option value=''>—— 请选择 ——</option>" ,
        inNameStr = "<option value=''>—— 请选择 ——</option>" ;
    if(curTypeVal == ""){
        type = "" ;
    }
    for(var i = 0 ; i < gs.length ; i++){
        var outerSn = gs[i]["outerSn"] ;
        var cInnerSn = gs[i]["innerSn"] ;
        var cInnerName = gs[i]["innerName"] ;
        var outerName = gs[i]["outerName"] ;
        var mtID = gs[i]["product"] ;
        var gsID = gs[i]["customerProduct"] ;
        switch (type){
            case "outSn":
                if(curTypeVal == outerSn){
                    inSnStr = "<option value='"+ cInnerSn +"'>"+ cInnerSn +"</option>" ;
                    outNameStr = "<option value='"+ outerName +"'>"+ outerName +"</option>" ;
                    inNameStr = "<option value='"+ cInnerName +"'>"+ cInnerName +"</option>" ;
                    $("#mtID").val(mtID);
                    $("#gsID").val(gsID);
                }
                break;
            case "outName":
                if(curTypeVal == outerName){
                    outSnStr = "<option value='"+ outerSn +"'>"+ outerSn +"</option>" ;
                    inSnStr = "<option value='"+ cInnerSn +"'>"+ cInnerSn +"</option>" ;
                    inNameStr = "<option value='"+ cInnerName +"'>"+ cInnerName +"</option>" ;
                    $("#mtID").val(mtID);
                    $("#gsID").val(gsID);
                }
                break;
            case "inSn":
                if(curTypeVal == cInnerSn){
                    outSnStr += "<option value='"+ outerSn +"'>"+ outerSn +"</option>" ;
                    outNameStr += "<option value='"+ outerName +"'>"+ outerName +"</option>" ;
                    inNameStr = "<option value='"+ cInnerName +"'>"+ cInnerName +"</option>" ;
                    $("#mtID").val("");
                    $("#gsID").val("");
                }
                break;
            case "inName":
                if(curTypeVal == cInnerName){
                    outSnStr += "<option value='"+ outerSn +"'>"+ outerSn +"</option>" ;
                    inSnStr = "<option value='"+ cInnerSn +"'>"+ cInnerSn +"</option>" ;
                    outNameStr += "<option value='"+ outerName +"'>"+ outerName +"</option>" ;
                    $("#mtID").val("");
                    $("#gsID").val("");
                }
                break;
            default:
                outSnStr += "<option value='"+ outerSn +"'>"+ outerSn +"</option>" ;
                inSnStr += "<option value='"+ cInnerSn +"'>"+ cInnerSn +"</option>" ;
                outNameStr += "<option value='"+ outerName +"'>"+ outerName +"</option>" ;
                inNameStr += "<option value='"+ cInnerName +"'>"+ cInnerName +"</option>" ;
                $("#mtID").val("")
                $("#gsID").val("");
                break;
        }

    }
    $(".outPutTime:gt(0)").remove();
    // creator：hxz，2019-05-29 获取签收时间列表
    $.ajax({
        "url":"../complaint/getOutPutTime.do",
        "data":{ "product": $("#gsID").val() },
        success:function(res) {
            var list = res["data"]["listProduct"], str = "<option value=''>—— 请选择 ——</option>";
            outPutTim = list ;
            if(list && list.length>0){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<option value='"+ list[i]["outputStockSn"] +"'>"+ (new Date(list[i]["outputTime"]).format("yyyy-MM-dd")) +"</option>"
                }
            }
            $("#outPutTime").html(str);
        }
    })
    switch (type){
        case "outSn":
            $("#outName").html(outNameStr);
            $("#inSn").html(inSnStr);
            $("#inName").html(inNameStr);
            break;
        case "outName":
            $("#outSn").html(outSnStr);
            $("#inSn").html(inSnStr);
            $("#inName").html(inNameStr);
            break;
        case "inSn":
            $("#outSn").html(outSnStr);
            $("#outName").html(outNameStr);
            $("#inName").html(inNameStr);
            break;
        case "inName":
            $("#outSn").html(outSnStr);
            $("#outName").html(outNameStr);
            $("#inSn").html(inSnStr);
            break;
        default:
            $("#outSn").html(outSnStr);
            $("#outName").html(outNameStr);
            $("#inSn").html(inSnStr);
            $("#inName").html(inNameStr);
            break;
    }
}
// creator：hxz，2019-05-29  删除选择的签收日期
function delTime(_this) {
    _this.parent().remove();
    setOutPutTime();
}
// creator：hxz，2019-05-29  选择签收日期
function setOutPutTime() {
    var timeSn = $("#outPutTime").val();
    var timeStr = "<span timeSn='"+ timeSn +"'>day<i class=\"fa fa-close red\" onclick=\"delTime($(this))\"></i></span>";
    var list = outPutTim,
        str = "<option value=''>—— 请选择 ——</option>",
        selectOutPut = [] ; // 已经选中的时间
    $("#timeCon").children("span").each(function(){
        selectOutPut.push($(this).attr("info"));
    });
    if(list && list.length>0){
        for(var i = 0 ; i < list.length ; i++){
            var d = list[i]["outputStockSn"]
            if(selectOutPut.indexOf(d) < 0){ // 过滤已经筛选过的日期
                if(timeSn == d){ // 当前选择的日期
                    timeStr = timeStr.replace("day", (new Date(list[i]["outputTime"]).format("yyyy-MM-dd")));
                    $("#timeCon").append(timeStr);
                }else{
                    str += "<option value='"+ list[i]["outputStockSn"] +"'>"+ (new Date(list[i]["outputTime"]).format("yyyy-MM-dd")) +"</option>"
                }
            }
        }
    }
    $("#outPutTime").html(str);
}
var editGs = null ; // 当前修改或删除的商品 span
// creator：hxz，2019-05-29  修改投诉的商品
function updateGs(_this) {
    editGs = _this ;
    $("#editGsType").val("update");
    $('#imgUploadProBtn').html("");
    $('#fileUploadProBtn').html("");
    $('#imgUploadProBtn').data("gouparr",[]);
    $('#fileUploadProBtn').data("gouparr",[]);
    initUploadify($('#imgUploadProBtn'), "img", 3);
    initUploadify($('#fileUploadProBtn'), "file", 4);
    time2 = setInterval(function() {
        var outCode = $("#outSn").val();
        var outName = $("#outName").val();
        var inCode = $("#inSn").val();
        var inName = $("#inName").val();
        if(outCode && outName && inCode && inName){
            $("#addGoods").attr("class","ty-btn ty-btn-green ty-btn-big ty-circle-3").attr("onclick", "addBtn(1)");
        }else{
            $("#addGoods").attr("class","ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick");
        }
    },200);
    var pObj = _this.parent()
    var outSn = pObj.siblings(":eq(0)").html();
    var outName = pObj.siblings(":eq(1)").html();
    var inSn = pObj.siblings(":eq(2)").html();
    var inName = pObj.siblings(":eq(3)").html();
    var info = JSON.parse(_this.siblings(".hd").html());
    $("#outSn").val(outSn)
    resetGsList("outSn");
    $("#pro_problem").val(info["problem"]) ;
    $("#pro_consequence").val(info["consequence"]) ;
    $("#pro_appeal").val(info["appeal"]) ;
    $("#pro_amount").val(info["amount"]) ;
    $("#pro_percent").val(info["percent"]) ;
    $("#mtID").val(info["customerProduct"]) ;
    $("#pro_memo").val(info["memo"]);

    var files = info["listAttachment"] ;
    for(var i = 0 ; i < files.length ; i++){
        var path = files[i]["path"];
        var title = files[i]["title"];
        var type = files[i]["type"];
        var fileType = title.split(".").pop() ;
        var fileName = title.replace("."+ fileType,"") ;
        var itemTemp =
            '<div title="'+ title +'" path="'+ path +'" type="'+ type +'" class="upItem uploadify-queue-item">' +
            '<i class="up_delBtn fa fa-close" onclick="deleteFile($(this))"></i>' +
            '<span class="up_fileType">'+ chargeFileType(fileType) +'</span>' +
            '<span class="up_name">'+ fileName +'</span>' +
            '</div>';
        if(type == 1){ // 上传文件类型:1-照片,2-其他
            $('#imgUploadProBtn').append(itemTemp) ;
        }else{
            $('#fileUploadProBtn').append(itemTemp) ;
        }
    }

    var outputTimes = info["listProductOutput"], timeStr="" ; // 商品详细签收时间列表
    if(outputTimes && outputTimes.length > 0){
        for(var q = 0 ; q < outputTimes.length ; q++){
            timeStr += "<span timeSn='"+ outputTimes[q]["outputStockSn"] +"'>"+ outputTimes[q]["outputTime"] +"<i class=\"fa fa-close red\" onclick=\"delTime($(this))\"></i></span>";
        }
    }
    $("#timeCon").html(timeStr);
    setOutPutTime();
    $(".up_fileType").show();
    bounce.show($("#selectGoods")) ;
}
// creator：hxz，2019-05-29  删除投诉的商品
function delGs(_this) {
    bounce.show($("#delGsTip"));
    editGs = _this ;
    var pObj = _this.parent()
    var outSn = pObj.siblings(":eq(0)").html();
    var outName = pObj.siblings(":eq(1)").html();
    var inSn = pObj.siblings(":eq(2)").html();
    var inName = pObj.siblings(":eq(3)").html();
    $("#delGsTip .tip").html("<p>商品代号："+ outSn +", 商品名称："+ outName +", 产品图号："+ inSn +",产品名称："+ inName +"</p><p>您确定要删除对该商品的投诉？</p>")
}
function delGsOk() {
    editGs.parent().parent().remove();
    bounce.cancel();
}
// creator：hxz，2019-05-29  提交新增投诉
function submitBtn(num) {
    var data = {} ; // 新增的传参
    if(num == 0 || num == 2){ // 取消
        $(".yesPro").hide();
        $(".noPro").hide();
        $(".chargePro").show();
        $(".blueTr").show();
        var list = [];
        if(num == 0){ // 无关取消
            var gouparr1 = $('#imgUploadBtn').data("gouparr")||[];
            var gouparr2 = $('#fileUploadBtn').data("gouparr")||[];
            console.log('两个要取消的数组：')
            console.log(gouparr1)
            console.log(gouparr2)
            list.push(...gouparr1, ...gouparr2);
            $(".noPro").find("input").val("");
            $(".noPro").find("textarea").val("");
        }else{ // 有关取消
            $("#goodsList tbody").children("tr:gt(0)").each(function(){
                var info = $(this).children("td:last").children(".hd").html();
                info = JSON.parse(info);
                list.push(...info.fileDelByGroup);
            })
            $("#goodsList tbody").children(":gt(0)").remove();

        }
        // 删除已经上传的文件
        list.forEach(function (item) {
            fileDelAjax(item);
        })
        return false;
    }else{
        data["module"] = '投诉' ;
        data["userID"] = sphdSocket.user.userID ;
        data["customer"] = $("#cusID").val() ;
        data["terminateState"] =0 ;
        data["contactName"] = $("#contactName").val() ;
        data["contactPhone"] = $("#contactPhone").val() ;
        data["contact"] = $("#contact").val() ;
        data["post"] = $("#post").val() ;
        data["type"] = $("#type").val() ;
        data["receiptTime"] = $("#receiptTime").val() ;
        data["state"] = timerObj.he ? 3 : 1 ; // 当登录人的身份是立案者时传1，当登录的人是核心人物时传3
        if(timerObj.he){
            data["processor"] = $("#charger").val() ;
            console.log("processor", data["processor"]);
            if(!data["processor"]){
                layer.msg("请选择投诉处理者");
                return false;
            }
        }
        data["listComplaintDetail"] = [] ;
        var list = [];
        if(num == 1){ // 无关 确定
            var info = {
                'problem': $("#problem").val() ,
                'consequence': $("#consequence").val() ,
                'appeal': $("#appeal").val() ,
                'memo': $("#memo").val()
            };
            var listAttachment = [] ;
            $("#nop").find(".upItem").each(function(){
                var path = $(this).attr("path") ;
                var title = $(this).attr("title") ;
                var type = $(this).attr("type") ; // 类型:1-照片,2-其他
                listAttachment.push({ "path": path, "title":title, "type":type })
            })
            if(listAttachment.length >0){
                info["listAttachment"] = listAttachment ;
            }
            // info = JSON.stringify(info);
            data["listComplaintDetail"].push(info) ;

            var gouparr1 = $('#imgUploadBtn').data("gouparr");
            var gouparr2 = $('#fileUploadBtn').data("gouparr");
            list.push(...gouparr1, ...gouparr2);

        }else if(num == 3){ // 有关 确定
            var infoArr = [] ;
            $("#goodsList tbody").children("tr:gt(0)").each(function(){
                var info = $(this).children("td:last").children(".hd").html();
                info = JSON.parse(info);
                list.push(...info.fileDelByGroup);
                delete info.fileDelByGroup
                infoArr.push(info);

            })
            // infoArr = JSON.stringify(infoArr);
            data["listComplaintDetail"] = infoArr ;

        }
        // 取消删除文件
        list.forEach(function (item) {
            cancelFileDel(item)
        })

        console.log('新增投诉的传参：', data)
        sphdSocket.send('saveComplaint', data);
        loading.open();
    }
}
//creator:hxz date:2019/05/29 新增投诉回调
function addCallBack(res) {
    res = JSON.parse(res) ;
    console.log('新增投诉回调', res) ;
    var complaintInfo = res["complaint"] ;
    var userID = sphdSocket.user.userID ;
    if(complaintInfo["creator"] == userID){
        loading.close();
        // layer.msg("新增投诉，操作成功！");
        initAdd() ;
    }
}
//creator:hxz date:2019/05/24 上传文件通用方法
function initUploadify(docObj, type, number) {
    var itemTemp =
        '<div id="${fileID}" title="${fileName}" size="${size}" code="${uploadCode}" class="upItem uploadify-queue-item">' +
        '<i class="hd up_delBtn fa fa-close" onclick="deleteFile($(this))"></i>' +
        '<span class="hd up_fileType">${fileType}</span>' +
        '<span class="hd delInfo"></span>' +
        '<span class="up_name">${fileName}</span>' +
        '<span class="uploadify-progress"><div class="uploadify-progress-bar"></div></span>' +
        '</div>';
    var fileTypeExt = '*.gif,*.png;*.jpg;*.jpeg;';
    if(type == 'file'){
        fileTypeExt = '.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.wps;*.et;*.md;';
    }
    docObj.Huploadify({
        auto: true,
        fileTypeExts: fileTypeExt,
        multi: true,
        buttonText: "添加",
        buttonNumber: number,
        formData:{
            module: '投诉',
            userId: sphdSocket.user.userID,
            groupUuid: sphdSocket.uuid()
        },
        fileSizeLimit: (100*1024),  // 100M = ( 100 * 1024 ) KB
        showUploadedPercent: true,
        showUploadedSize: true,
        removeTimeout: 99999999,
        itemTemplate:itemTemp ,
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onUploadStart: function () {
            $("#confirmReport").attr("class", "ty-btn ty-btn-big ty-btn-gray ty-circle-3").removeAttr("onclick");
        },
        onUploadComplete: function (file, data) {
            $("#confirmReport").attr("class", "ty-btn ty-btn-big ty-circle-3 ty-btn-green").attr("onclick", "confirmReport()");
        },
        onUploadError: function (file) {
            layer.msg('文件上传出错！');
            console.log('出错文件：', file)
        },
        onUploadSuccess: function (file, data) {
            data = JSON.parse(data);
            var filePath = data.filename;
            var fileName = file.name;
            var size = file.size;
            var name =data.filename;
            var fileUid =data.fileUid;
            var groupUuid =data.groupUuid;
            if(groupUuid){
                let gouparr = docObj.data("gouparr") || []
                let n = -1;
                gouparr.forEach(function (item, index) {
                    if('groupUuid' == item.type && groupUuid == item['groupUuid']){
                        n = index;
                    }
                });
                if(n === -1){
                    gouparr.push({"type":'groupUuid', 'groupUuid':groupUuid})
                    docObj.data("gouparr", gouparr);
                }
            }

            $(".upItem").each(function(){
                var txt = $(this).attr("title");
                var s = $(this).attr("size");
                var co = $(this).attr("code");
                if(fileName == txt && size == s && uploadCode == co){ // 名称/大小/批次 一样就默认是一个,即：每次不能穿两个名字、类型、大小完全相同的文件
                    $(this).children("i.up_delBtn").show();
                    $(this).children(".up_fileType").show();
                    $(this).children(".uploadify-progress").hide();
                    var type = $(this).children(".up_fileType").html();
                    $(this).children(".up_fileType").html(chargeFileType(type));
                    $(this).children(".up_name").html(fileName.replace("."+type , ""));
                    $(this).attr("path", filePath) ;
                    $(this).attr("type", uploadType) ;
                    $(this).find(".delInfo").html(JSON.stringify({"type":'fileId', 'fileId':fileUid}))
                }
            });
        }
    });
}
// 工具方法： 删除文件
function deleteFile(_this) {
    _this.parent().remove();
    let info = JSON.parse(_this.siblings(".delInfo").html())
    fileDelAjax(info)
}
// 工具方法： 返回文件类型的DOM串
function chargeFileType(fileType) {
    var midtermStr = "";
    switch (fileType) {
        case "doc":
        case "docx":
            midtermStr = '<div class="ty-fileType ty-file_doc"></div>';
            break;
        case "xls":
        case "xlsx":
        case "et":
            midtermStr = '<div class="ty-fileType ty-file_xls"></div>';
            break;
        case "ppt":
            midtermStr = '<div class="ty-fileType ty-file_ppt"></div>';
            break;
        case "rar":
        case "zip":
            midtermStr = '<div class="ty-fileType ty-file_rar"></div>';
            break;
        case "pdf":
            midtermStr = '<div class="ty-fileType ty-file_pdf"></div>';
            break;
        case "png":
        case "jpg":
        case "gif":
            midtermStr = '<div class="ty-fileType ty-file_jpg"></div>';
            break;
        default:
            midtermStr = '<div class="ty-fileType ty-file_other"></div>';
    }
    return midtermStr ;
}

//----------------- 分工设置 -------------------//
// creator：张旭博，2018-01-18 09:59:33，返回综合管理
function goBack(state) {
    if(state === 'division'){
        $(".mainPage").show();
        $(".divisionPage").hide();
    }else if(state === "passDetail"){
        $(".passedPage").show();
        $(".passedDetailPage").hide();
    }

}
//  creator：张旭博，2018-01-18 09:47:22，分工设置 - 按钮
function divisionSettingBtn() {
    $(".mainPage").hide();
    $(".divisionPage").show();
    $(".divisionPage .ty-secondTab li").eq(0).click();
}
// uodator：侯杏哲 2019-06-21 ，获取立案者、处理者
function getDivisionList() {
    var code = $(".divisionPage .ty-secondTab .ty-active").attr("code");
    $.ajax({
        url:"../coreSetting/getThreeUser.do" ,
        data:{
            "coreCode":code
        },
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                var listData = data["codeUsers"];
                var listStr = '';
                for(var i=0;i<listData.length;i++){
                    listStr +=  '<tr id="'+listData[i].userID+'">' +
                        '<td>'+ listData[i].userName +'</td>'+     //姓名
                        '<td>'+ chargeSex(listData[i].gender) +'</td>'+       //性别
                        '<td>'+ listData[i].mobile +'</td>'+       //手机号
                        '<td>'+ listData[i].departName +'</td>'+       //部门
                        '<td>'+ listData[i].postName +'</td>'+       //职位
                        '<td>'+ listData[i].createTime +'</td>'+       //职位
                        '<td>' +
                        '<span class="ty-color-red" onclick="stopRight($(this))">停权</span>'+
                        '</td>'+
                        '</tr>';
                }
                switch (code){
                    case "filing":
                        $(".divisionPage .tblContainer").eq(0).find("tbody").html(listStr);
                        break;
                    case "handle":
                        $(".divisionPage .tblContainer").eq(1).find("tbody").html(listStr);
                        break;
                }
            }else{
                $("#errorTip .tipWord").html("获取列表失败!") ;
                bounce.show($("#errorTip")) ;
            }
        }
    }) ;
}

// creator：hxz，2019-06-21 停权
var editTr = null ;
function stopRight(thisObj) {
    var code = $(".divisionPage .ty-secondTab .ty-active").attr("code");
    editTr = thisObj.parent().parent()
    bounce.show($("#stopRight"));
    var name = thisObj.parent().siblings(":eq(0)").html();
    var stopRightTip = "您点击确定后，"+ name +" 将不再负责投诉立案，在系统中将无法再见到投诉立案模块，也无法再见到自己的投诉立案。";
    if(code == "handle"){
        var stopRightTip = "您点击确定后，"+ name +" 将不再负责投诉的处理，在系统中将无法再见到投诉处理模块，也无法再见到自己处理过的投诉。";
    }
    $("#stopRightTip").html(stopRightTip) ;
}
function stopRightOk() {
    var code = $(".divisionPage .ty-secondTab .ty-active").attr("code");
    var userId = editTr.attr("id");

}

// creator：张旭博，2018-01-23 15:24:30，新增立案者/处理者/查看者
// updator：hxz，2019-05-23  修改选择人下拉框的赋值
function newDivisionBtn(){
    bounce.show( $("#newDivision") );
    var code = $(".divisionPage .ty-secondTab .ty-active").attr("code");
    $("#newDivision .bonceHead").attr("code",code);
    $("#newDivision ."+code).show().siblings().hide();
    $.ajax({
        url:"../complaint/getCoreSettingUserListByComplaint.do" ,
        data:{"coreCode":code },
        success:function( res ) {
            var success = res["success"] ;
            if(success != 1){
                bounce.show( $("#tip") ) ; $("#tipMess").html( "获取数据失败，请刷新重试！" ) ; return false ;
            }
            var data = res["data"], str="<option value=\"\">—— 请选择 ——</option>";
            if(data && data.length>0){
                var he = sphdSocket.user.userID;
                for(var i = 0 ; i < data.length ; i++){
                    if(he != data[i]["userID"]){
                        str += "<option value='"+ data[i]["userID"] +"'>"+ data[i]["userName"] +"</option>" ;
                    }
                }
            }
            $("#userList").html(str)
        }
    });
}
// creator：张旭博，2018-01-17 14:49:19，新增投诉立案者-确认
// updator：hxz，2019-05-23  修改选择人的值的读取
function sureNewDivision(){
    var coreCode = $("#newDivision .bonceHead").attr("code");
    var user = $("#userList").val();
    if(user == ""){
        layer.msg('请先选择员工，再确定'); return false;
    }
    var userId = [user];
    $.ajax({
        url:"../coreSetting/addManyUsers.do" ,
        data:{
            "userId":userId.toString(), //用户id
            "coreCode":coreCode         //filing-立案者 handle-处理者 see-查看者
        },
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                layer.msg("新增成功");
                bounce.cancel();
                $(".divisionPage .ty-secondTab .ty-active").click();
            }else{
                $("#errorTip .tipWord").html("新增失败!") ;
                bounce.show($("#errorTip")) ;
            }
        }
    }) ;
}
 // creator：张旭博，2018-01-23 16:27:12，删除立案者、处理者、查看者
function deleteDivision(selector) {
    var userId = selector.parent().parent().attr("id");
    var code = $(".divisionPage .ty-secondTab .ty-active").attr("code");
    $.ajax({
        url:"../coreSetting/deleteSpecialUser.do" ,
        data:{
            "userId":userId,
            "coreCode":code
        },
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                layer.msg("删除成功");
                $(".divisionPage .ty-secondTab .ty-active").click();
            }else{
                $("#errorTip .tipWord").html("删除失败!") ;
                bounce.show($("#errorTip")) ;
            }

        }
    }) ;
}
/* creator：张旭博，2018-01-23 16:22:45，转换性别字符串 */
function chargeSex(gender) {
    if(gender === "1"){
        return "男"
    }else if(gender === "0"){
        return "女"
    }else{
        return ""
    }
}

laydate.render({elem: '#receiptTime'});
