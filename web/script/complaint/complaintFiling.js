$(function () {
    // 三种状态切换（待处理、已批准、已驳回）
    $(".mainPage .ty-secondTab li").on("click", function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        //获取点击按钮的下标
        var index = $(this).index();
        // getComplaintByStatus(index+1);
        $(".mainPage .tblContainer").eq(index).show().siblings(".tblContainer").hide();
        if(index === 5){
            getPassedCase(1,20,1);
        }else{
            getComplaintByStatus(index+1);
        }
    });
    $(".divisionPage .ty-secondTab li").on("click", function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        //获取点击按钮的下标
        var index = $(this).index();
        $(".divisionPage .tblContainer").eq(index).show().siblings(".tblContainer").hide();
        getDivisionList();
    });
    $(".departTree").on("click" , "li>div" , function () {
        $(this).next().toggle();
    });
    $("#cp_customerName").on("change",function () {
        var code = $(this).find("option:selected").attr("code");
        if(code === "null"){
            $("#cp_customerCode").val("--");
        }else{
            $("#cp_customerCode").val(code);
        }
    });
    $("#newComplaint .cp_add_box .cp_customerName").on("change",function () {
        var customerCode = $("#newComplaint .cp_add_box .cp_customerName").find("option:selected").attr("code");
        $("#newComplaint .cp_add_box .cp_customerCode").val(customerCode);
    });


    // 单选按钮事件
    $(".ty-radio").on("click",function () {
        $(this).addClass("ty-radioActive").siblings().removeClass("ty-radioActive");
        $(this).siblings().children("i").attr("class","fa fa-circle-o")
        $(this).children("i").attr("class","fa fa-dot-circle-o")
        // $(this).siblings(".hd").val($(this).attr("value"));
    });
    //本年、去年、前年切换状态
    $("#changeState .ty-btn").on("click",function(){
        //样式切换
        $(this).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
        $("#loginQueryBtn").removeClass("ty-btn-blue");

        //清空自定义选择的时间
        $("#queryBeginTime").val("");
        $("#queryEndTime").val("");

        //获取对应数据
        var index = $(this).index();
        getPassedCase(1,20,index+1) ;
    });
    mainInit();
});

function mainInit() {
    var complaintID = getUrlParam("complaintID");
    var approvalStatus = getUrlParam("approvalStatus"); //0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
    if(complaintID){
        var bounceFootStr = '';
        if (approvalStatus === "3" || approvalStatus === "5"){
            bounceFootStr = '<button class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</button>' +
                '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeCaseBtn()">结案</button>';
        }

        $(".mainPage .ty-secondTab li").eq(approvalStatus-1).click();
        $("#seeComplaint .bonceFoot").html(bounceFootStr);
        $("#seeComplaint .bonceCon").attr("id",complaintID);
        getComplaintDetail("#seeComplaint",complaintID,approvalStatus,2);
        bounce.show($("#seeComplaint"));
    }else{
        $(".mainPage .ty-secondTab li").eq(2).click();
    }
}

//-----------------投诉流程-------------------//

/* creator：张旭博，2018-01-17 14:51:09，新增投诉 - 按钮 */
function newComplaintBtn(){
    bounce.show($("#newComplaint"));
    $('#newComplaint .cp_fileUpload').html("");
    $('#newComplaint .cp_imgUpload').html("");
    $('#newComplaint .cp_imgShow').html("");
    $('#newComplaint .cp_fileShow').html("");
    $("#newComplaint").find("input,textarea,select").val("");
    getCustomers();
    $('#newComplaint .cp_imgUpload').Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        formData:{
            module: '投诉',
            userId: sphdSocket.user.userID,
            groupUuid: sphdSocket.uuid()
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        uploader:$.webRoot + "/uploads/uploadfyByFile.do",
        onUploadStart:function(){},
        onInit:function(){},
        /* creator：张旭博，2017-05-06 15:15:49，选择文件后执行的方法 */
        onSelect:function (file) {},
        /* creator：张旭博，2017-05-06 15:15:37，上传文件完成执行的方法 */
        onUploadComplete:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传成功！").css("display","block");
        },
        /* creator：王静，2017-08-02 15:16:37，上传文件失败执行的方法 */
        onUploadError:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传失败！").css("display","block");
        },
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file,data){
            $(".uploadify-queue").html("");
            var res = JSON.parse(data);
            var groupUuid = res.groupUuid;
            if(groupUuid){
                let gouparr =  $('#newComplaint .cp_imgUpload').data("gouparr") || []
                gouparr.push({"type":'groupUuid', 'groupUuid':groupUuid})
                $('#newComplaint .cp_imgUpload').data("gouparr", gouparr);
            }
            //新上传的文件展示
            var str = getNewEnclosure(file,data);   //接口在complaintCommon.js中
            $("#newComplaint .cp_imgShow").append(str);
        },
        onCancel:function(file){
            // console.log(file.name);
        }
    });
    $('#newComplaint .cp_fileUpload').Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.wps;*.et;*.md;',
        multi:true,
        formData:{
            module: '投诉',
            userId: sphdSocket.user.userID,
            groupUuid: sphdSocket.uuid()
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        uploader:$.webRoot + "/uploads/uploadfyByFile.do",
        onUploadStart:function(){},
        onInit:function(){},
        /* creator：张旭博，2017-05-06 15:15:49，选择文件后执行的方法 */
        onSelect:function (file) {},
        /* creator：张旭博，2017-05-06 15:15:37，上传文件完成执行的方法 */
        onUploadComplete:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传成功！").css("display","block");
        },
        /* creator：王静，2017-08-02 15:16:37，上传文件失败执行的方法 */
        onUploadError:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传失败！").css("display","block");
        },
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file,data){
            $(".uploadify-queue").html("");
            var res = JSON.parse(data);
            var groupUuid = res.groupUuid;
            if(groupUuid){
                let gouparr = $('#newComplaint .cp_fileUpload').data("gouparr") || []
                gouparr.push({"type":'groupUuid', 'groupUuid':groupUuid})
                $('#newComplaint .cp_fileUpload').data("gouparr", gouparr);
            }
            //新上传的文件展示
            var str = getNewEnclosure(file,data);   //接口在complaintCommon.js中
            $("#newComplaint .cp_fileShow").append(str);
        },
        onCancel:function(file){
            // console.log(file.name);
        }
    });
    $('body').everyTime('0.5s','newComplaint',function(){
        var state = 1;
        $("#newComplaint .cp_add_box").find(".required").each(function () {
            if($.trim($(this).val()) === ''){
                state = 0;
            }
        });
        if( state === 0 ){
            $("#sureNewComplaintBtn").prop("disabled",true)
        }else {
            $("#sureNewComplaintBtn").prop("disabled",false)
        }
    });
}

/* creator：张旭博，2018-01-18 09:45:56，新增投诉 - 确定 */
function SureNewComplaint() {
    //准备ajax参数
    var customer    = $("#newComplaint .cp_add_box .cp_customerName").val();
    var contact     = $("#newComplaint .cp_add_box .cp_customerContact").val();
    var receiptTime = $("#newComplaint .cp_add_box .cp_acceptDate").val();
    var contactPhone= $("#newComplaint .cp_add_box .cp_mobile").val();
    var content     = $("#newComplaint .cp_add_box .cp_content").val();
    var memo        = $("#newComplaint .cp_add_box .cp_memo").val();
    var picArr = [];
    var attachArr = [];
    var picPatch    = {"picPatch":picArr};
    var attachPatch = {"attachPatch":attachArr};
    $("#newComplaint .cp_add_box .cp_imgShow .cp_img_box").each(function () {
        var path = $(this).attr("datasrc");
        var name = $(this).find(".cp_img_name").html();
        if(path){
            picArr.push({
                "title":name,
                "path":path
            });
        }
    });
    $("#newComplaint .cp_add_box .cp_fileShow .cp_img_box").each(function () {
        var path = $(this).attr("datasrc");
        var name = $(this).find(".cp_img_name").html();
        if(path){
            attachArr.push({
                "title":name,
                "path":path
            });
        }
    });
    var data= {
        "state":1,                                  //状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过,如果是核心人物新建的投诉状态是3立案通过，其他人新建后是1
        "customer":customer,                        //客户ID
        "contact":contact,                          //联系人
        "receiptTime":receiptTime,                  //接收投诉时间
        "contactPhone":contactPhone,                //联系电话
        "content":content,                          //投诉内容
        "memo":memo,                                //备注
        "picPatch":JSON.stringify(picPatch),        //图片路径数组
        "attachPatch":JSON.stringify(attachPatch),   //附件路径数组
        "module":'投诉',
        "coreCode" : 2
    };
    $.ajax({
        url:"../complaint/saveComplaint.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var status = data["status"];
            if(status === "1"){
                layer.msg("新增成功");
                bounce.cancel();
                $(".mainPage .ty-secondTab li").eq(0).click();
            }else{
                $("#errorTip .tipWord").html("新增失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-17 15:31:44，获取不同状态的投诉列表 */
function getComplaintByStatus(state) {
    $.ajax({
        url:"../complaint/getComplaintList.do",
        data:{
            "state":state,  //状态，1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
            "role":2        //1-核心人物 2-立案者 3-处理者 4-查看者
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ) {
            var status = data["status"];
            var listData = data["complaintList"];

            var listStr = '';
            if (listData.length > 0) {
                switch (state){
                    case 1:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +       //客户
                                '<td>' + listData[i].customCode + '</td>' +       //客户代号
                                '<td>' + listData[i].contact + '</td>' +       //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +       //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +       //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +       //录入者
                                '<td>' + listData[i].createTime + '</td>' +       //录入时间
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 2:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +       //客户
                                '<td>' + listData[i].customCode + '</td>' +       //客户代号
                                '<td>' + listData[i].contact + '</td>' +       //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +       //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +       //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +       //录入者
                                '<td>' + listData[i].createTime + '</td>' +       //录入时间
                                '<td>' + listData[i].registerApproveTime + '</td>' +       //审批时间
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 3:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +       //客户
                                '<td>' + listData[i].customCode + '</td>' +       //客户代号
                                '<td>' + listData[i].contact + '</td>' +       //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +       //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +       //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +       //录入者
                                '<td>' + listData[i].createTime + '</td>' +       //录入时间
                                '<td>' + listData[i].processorName + '</td>' +       //投诉处理人
                                '<td>' + listData[i].registerApproveTime+ '</td>' +       //审批时间
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '<span class="ty-color-blue" onclick="materialsBtn($(this))">补充材料</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 4:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +       //客户
                                '<td>' + listData[i].customCode + '</td>' +       //客户代号
                                '<td>' + listData[i].contact + '</td>' +       //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +       //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +       //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +       //录入者
                                '<td>' + listData[i].createTime + '</td>' +       //录入时间
                                '<td>' + listData[i].processorName + '</td>' +       //投诉处理人
                                '<td>' + listData[i].settleOpinion + '</td>' +       //结案说明
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 5:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +       //客户
                                '<td>' + listData[i].customCode + '</td>' +       //客户代号
                                '<td>' + listData[i].contact + '</td>' +       //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +       //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +       //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +       //录入者
                                '<td>' + listData[i].createTime+ '</td>' +       //录入时间
                                '<td>' + listData[i].processorName + '</td>' +       //投诉处理人
                                '<td>' + listData[i].settleApproveTime + '</td>' +       //审批时间
                                '<td>' + listData[i].settleOpinion + '</td>' +       //结案说明
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                }
                $(".mainPage .tblContainer").eq(state - 1).find("tbody").html(listStr);
            }else{
                $(".mainPage .tblContainer").eq(state - 1).find("tbody").html("");
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-18 15:26:31，投诉查看 - 按钮 */
function seeComplaintBtn(selector){
    bounce.show($("#seeComplaint"));
    var state = $(".mainPage .ty-secondTab li").index($(".mainPage .ty-secondTab li.ty-active"));
    var bonceFootStr = '';
    $("#cp_processor").show();
    switch (state){
        case 0:
        case 1:
            $("#cp_processor").hide();
            bonceFootStr =  '';
            break;
        case 2:
            bonceFootStr =  '<button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</button>' +
                '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="closeCaseBtn()">结案</button>';
            break;
        case 3:
            bonceFootStr =  '';
            break;
        case 4:
            bonceFootStr =  '<button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</button>' +
                '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="closeCaseBtn()">结案</button>';
            break;

    }
    $("#seeComplaint .bonceFoot").html(bonceFootStr);
    var complaintId = selector.parent().parent().attr("id");
    $("#seeComplaint .bonceCon").attr("id",complaintId);

    getComplaintDetail("#seeComplaint",complaintId,state,2);

}

/* creator：张旭博，2018-01-31 09:50:48，投诉查看 - 结案 - 按钮 */
function closeCaseBtn() {
    bounce_Fixed.show($("#closeCase")) ;
    $(".ty-radioActive").removeClass("ty-radioActive").children("i").attr("class","fa fa-circle-o");
    $("#settleOpinion").val("");
    //开启表单验证
    $('body').everyTime('0.5s','closeCase',function(){
        var caseState = $("#operationRadio .ty-radioActive").attr("value");

        if( caseState === undefined ){
            $("#sureCloseCaseBtn").prop("disabled",true)
        }else {
            $("#sureCloseCaseBtn").prop("disabled",false)
        }
    });
}

/* creator：张旭博，2018-01-31 09:59:04，投诉查看- 审批 - 确定（立案批准、立案驳回、结案批准、结案驳回、结案） */
function approveComplaint(state) {

    var id = $("#seeComplaint .bonceCon").attr("id");
    var customer = $("#seeComplaint .cp_scan_box .cp_customerName").attr("id");
    var creator = $("#seeComplaint .cp_scan_box .cp_customerName").attr("cid");

    var data = {
        "id":id,                //投诉id
        "state":state,          //状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
        "customer":customer,    //客户id
        "creator":creator,      //创建人id
        "coreCode":2            //1-核心人物
    };
    switch (state){
        //1-立案待审批
        case 1:
            break;
        //2-立案驳回
        case 2:
            var rejectReason = $("#tip #rejectReason").val();
            data["registerRejectReason"] = rejectReason;
            break;
        //3-立案通过
        case 3:
            var processor = $("#seeComplaint .cp_handlerName").val();
            data["processor"] = processor;
            break;
        //4-结案待审批
        case 4:
            var settleType = Number($("#operationRadio .ty-radioActive").attr("value"));
            var settleOpinion = $("#settleOpinion").val();
            data["settleType"] = settleType;
            data["settleOpinion"] = settleOpinion;
            break;
        //5-结案驳回
        case 5:
            break;
        //6-结案通过
        case 6:
            break;

    }
    console.log(data);
    $.ajax({
        url:"../complaint/approvalComplaint.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var status = data["status"];
            if(status === "1"){
                bounce.cancel();
                bounce_Fixed.cancel();
                switch (state){
                    //1-立案待审批
                    case 1:
                        break;
                    //2-立案驳回
                    case 2:
                        layer.msg("立案驳回成功");
                        break;
                    //3-立案通过
                    case 3:
                        layer.msg("立案批准成功");
                        break;
                    //4-结案待审批
                    case 4:
                        layer.msg("结案成功");
                        break;
                    //5-结案驳回
                    case 5:
                        layer.msg("结案驳回成功");
                        break;
                    //6-结案通过
                    case 6:
                        layer.msg("结案批准成功");
                        break;
                }
                $(".mainPage .ty-secondTab li.ty-active").click();
            }else{
                $("#errorTip .tipWord").html("提交失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-18 15:26:32，补充材料 - 按钮 */
function materialsBtn(selector){
    bounce.show($("#supplyComplaint"));
    $('#supplyComplaint .cp_fileUpload').html("");
    $('#supplyComplaint .cp_imgUpload').html("");
    $('#supplyComplaint .cp_imgShow').html("");
    $('#supplyComplaint .cp_fileShow').html("");
    $("#supplyComplaint").find("input").val("");
    $("#supplyComplaint").find("textarea").val("");
    var complaintId = selector.parent().parent().attr("id");
    $("#supplyComplaint .bonceCon").attr("id",complaintId);
    getComplaintDetail("#supplyComplaint",complaintId,0,2);
    $('#supplyComplaint .cp_imgUpload').Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        formData:{
            module: '投诉',
            userId: sphdSocket.user.userID,
            groupUuid: sphdSocket.uuid()
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        uploader:"../uploads/uploadfyByFile.do",
        onUploadStart:function(){},
        onInit:function(){},
        /* creator：张旭博，2017-05-06 15:15:49，选择文件后执行的方法 */
        onSelect:function (file) {},
        /* creator：张旭博，2017-05-06 15:15:37，上传文件完成执行的方法 */
        onUploadComplete:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传成功！").css("display","block");
        },
        /* creator：王静，2017-08-02 15:16:37，上传文件失败执行的方法 */
        onUploadError:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传失败！").css("display","block");
        },
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file,data){
            $("#supplyComplaint .cp_add_box .uploadify-queue").html("");
            var res = JSON.parse(data);
            var groupUuid = res.groupUuid;
            if(groupUuid){
                let gouparr = $('#supplyComplaint .cp_imgUpload').data("gouparr") || []
                gouparr.push({"type":'groupUuid', 'groupUuid':groupUuid})
                $('#supplyComplaint .cp_imgUpload').data("gouparr", gouparr);
            }
            //新上传的文件展示
            var str = getNewEnclosure(file,data);   //接口在complaintCommon.js中
            $("#supplyComplaint .cp_add_box .cp_imgShow").append(str);
        },
        onCancel:function(file){
            // console.log(file.name);
        }
    });
    $('#supplyComplaint .cp_fileUpload').Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.wps;*.et;*.md;',
        multi:true,
        formData:{
            module: '投诉',
            userId: sphdSocket.user.userID,
            groupUuid: sphdSocket.uuid()
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        uploader:"../uploads/uploadfyByFile.do",
        onUploadStart:function(){},
        onInit:function(){},
        /* creator：张旭博，2017-05-06 15:15:49，选择文件后执行的方法 */
        onSelect:function (file) {},
        /* creator：张旭博，2017-05-06 15:15:37，上传文件完成执行的方法 */
        onUploadComplete:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传成功！").css("display","block");
        },
        /* creator：王静，2017-08-02 15:16:37，上传文件失败执行的方法 */
        onUploadError:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传失败！").css("display","block");
        },
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file,data){
            $("#supplyComplaint .cp_add_box .uploadify-queue").html("");
            var res = JSON.parse(data);
            var groupUuid = res.groupUuid;
            if(groupUuid){
                let gouparr = $('#supplyComplaint .cp_fileUpload').data("gouparr") || []
                gouparr.push({"type":'groupUuid', 'groupUuid':groupUuid})
                $('#supplyComplaint .cp_fileUpload').data("gouparr", gouparr);
            }
            //新上传的文件展示
            var str = getNewEnclosure(file,data);   //接口在complaintCommon.js中
            $("#supplyComplaint .cp_add_box .cp_fileShow").append(str);
        },
        onCancel:function(file){
            // console.log(file.name);
        }
    });
    //开启表单验证
    $('body').everyTime('0.5s','supplyComplaint',function(){
        var cp_customerName = $("#supplyComplaint").find(".cp_add_box .cp_annex").val();
        if( cp_customerName === "" ){
            $("#sureSupplyComplaintBtn").prop("disabled",true)
        }else {
            $("#sureSupplyComplaintBtn").prop("disabled",false)
        }
    });
}

/* creator：张旭博，2018-01-29 13:32:22，补充材料 - 确定 */
function sureMaterials() {
    var complaintId = $("#supplyComplaint .bonceCon").attr("id");
    var description = $("#supplyComplaint .cp_add_box .cp_annex").val();
    var picArr = [];
    var attachArr = [];
    var picPatch = {"picPatch":picArr};
    var attachPatch = {"attachPatch":attachArr};
    $("#supplyComplaint .cp_add_box .cp_imgShow .cp_img_box").each(function () {
        var path = $(this).attr("datasrc");
        var name = $(this).find(".cp_img_name").html();
        if(path){
            picArr.push({
                "title":name,
                "path":path
            });
        }
    });
    $("#supplyComplaint .cp_add_box .cp_fileShow .cp_img_box").each(function () {
        var path = $(this).attr("datasrc");
        var name = $(this).find(".cp_img_name").html();
        if(path){
            attachArr.push({
                "title":name,
                "path":path
            });
        }
    });
    var data = {
        "complaint":complaintId,
        "module":'投诉',
        "description":description,
        "picPatch":JSON.stringify(picPatch),
        "attachPatch":JSON.stringify(attachPatch)
    };
    $.ajax({
        url:"../complaint/saveSupplement.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var status = data["status"];
            if(status === "1"){
                layer.msg("补充成功");
                bounce.cancel();
                $(".mainPage .ty-secondTab li.ty-active").click();
            }else{
                $("#errorTip .tipWord").html("补充失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

//-----------------结案通过（数据筛选）-------------------//

/* creator：张旭博，2018-02-01 14:18:36，获得结案通过的投诉 */
function getPassedCase(curr,totalPage,buttonType) {
    var data = {
        "buttenType":buttonType,//1代表获取本年的结案的case（即刚点进来时就要加载的方法，传2代表获取去年结案的case，传3代表获取前年结案的case，传4代表按某个特定的时间段获取结案的case
        "role":2,//身份信息 1是核心人物 2是立案者 3是处理者 4是查看者
        "currentPageNo":curr,
        "pageSize":totalPage
    };
    if(buttonType === 4){
        var startTime = $("#queryBeginTime").val()+" 00:00:00";
        var endTime = $("#queryEndTime").val()+" 23:59:59";
        data["startTime"] = startTime;//开始时间
        data["endTime"] = endTime;//结束时间
    }

    $.ajax({
        url:"../complaint/getPassedCase.do",
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var mainData = data["data"],
                pageInfo = data["page"];

            var complaintList   = mainData.complaintList,
                startDay        = formatTime(mainData.startDay,false),
                endDay          = formatTime(mainData.endDay,false),
                totalResult     = pageInfo.totalResult;

            var tipStr          = '<div class="ty-alert ty-alert-warning">自'+startDay+'~'+endDay+'，已结案的投诉共 <span class="ty-color-blue complaintResult">'+totalResult+'</span> 件</div>',
                complaintListStr= '';

            if(complaintList) {
                for (var i = 0; i < complaintList.length; i++) {
                    complaintListStr += '<tr id="' + complaintList[i].id + '">' +
                        '<td>' + complaintList[i].customName + '</td>' +       //客户
                        '<td>' + complaintList[i].customCode + '</td>' +       //客户代号
                        '<td>' + complaintList[i].contact + '</td>' +       //客户联系人
                        '<td>' + complaintList[i].contactPhone + '</td>' +       //联系方式
                        '<td>' + formatTime(complaintList[i].receiptTime) + '</td>' +       //接到投诉的日期
                        '<td>' + complaintList[i].createName + '</td>' +       //录入者
                        '<td>' + formatTime(complaintList[i].createTime) + '</td>' +       //录入时间
                        '<td>' + complaintList[i].processorName + '</td>' +       //投诉处理人
                        '<td>' + formatTime(complaintList[i].settleApproveTime) + '</td>' +       //审批时间
                        '<td>' + complaintList[i].settleOpinion + '</td>' +       //结案说明
                        '<td>' +
                        '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                        '</td>' +
                        '</tr>';
                }
                $(".mainPage .passedPage .tplContainer").eq(0).find(".tip").html(tipStr);
                $(".mainPage .passedPage .tplContainer").eq(0).find("tbody").html(complaintListStr);
            }else{
                $(".mainPage .tblContainer").eq(5).find("tbody").html("");
            }

        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        },
        complete:function(){ loading.close();}
    }) ;
}

//-----------------辅助方法-------------------//

/* creator：张旭博，2018-01-24 14:22:47，获取客户信息（从销售过来的数据） */
function getCustomers(){
    $.ajax({
        url:"/sales/getCustomerInfoByOid.do",
        data:{},
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var customerList = data["data"];
            var customerListStr = '<option value="">-----请选择客户-----</option>';
            for(var i in customerList){
                customerListStr += '<option value="'+customerList[i].id+'" code="'+chargeNull(customerList[i].code)+'">'+customerList[i].name+'</option>';
            }
            $("#newComplaint .cp_customerName").html(customerListStr);
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-19 09:41:00，设置不同分工者下拉选项*/
function setDivisionOption(selector,coreCode){
    $.ajax({
        url:"../coreSetting/getThreeUser.do" ,
        data:{
            "coreCode":coreCode
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                var listData = data["codeUsers"];

                var handlerStr = '<option value="">------请选择------</option>';
                for(var i in listData){
                    handlerStr += '<option value="'+listData[i].userID+'">'+listData[i].userName+'</option>';
                }
                selector.html(handlerStr);
            }else{
                $("#errorTip .tipWord").html("获取列表失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-25 15:01:43，删除图片或附件 */
function deleteImg(selector){
    selector.parent().remove();
    let info = JSON.parse(_this.siblings(".delInfo").html())
    fileDelAjax(info)
}

/* creator：张旭博，2018-01-18 09:59:33，返回综合管理 */
function goBack(state) {
    if(state === 'division'){
        $(".mainPage").show();
        $(".divisionPage").hide();
    }else if(state === "passDetail"){
        $(".passedPage").show();
        $(".passedDetailPage").hide();
    }

}

//-----------------选人方法（候）--------------------//

/* creator: 侯杏哲 2017-12-06 工具方法 拼接部门人员需要的字符串 */
function setStr( data , method , userArr ) { // data:该机构中人员的数据 , method:加或者减的方法
    var strAll = "" ;
    if(data && data.length > 0){
        for(var i in data){
            var hasKids = false ;
            var userList = data[i]["userList"]; // 人员列表
            var kidList = data[i]["subList"]; // 部门列表
            if( (userList && kidList.length > 0) || (userList && userList.length > 0) ){ // 有子级
                hasKids = true ;
                strAll += '<li><div lass="departid"  info="'+ data[i]["id"] +'"><i class="fa fa-angle-right"></i><span>'+ data[i]["name"] +'</span> <i class="fa fa-'+ method +'-square" onclick="'+ method +'($(this), event)"></i></div><ul>' ;
            }else{ // 无子级
                strAll += '<li><div lass="departid"  info="'+ data[i]["id"] +'"><i class="fa fa-angle-down"></i><span>'+ data[i]["name"] +'</span> <i class="fa fa-'+ method +'-square" onclick="'+ method +'($(this), event)"></i></div>' ;
            }
            if(kidList && kidList.length > 0){ // 遍历子级部门
                strAll += setStr( kidList , method  ) ;
            }
            if(userList && userList.length > 0){ // 遍历员工
                for(var j in userList){
                    strAll += '<li><input type="hidden" name="userID" value="'+ userList[j]["userID"] +'"><div lass="uid" info="'+ userList[j]["userID"] +'"><i class="fa fa-info"></i><span>'+ userList[j]["userName"] +'</span> <i class="fa fa-'+ method +'-square" onclick="' + method + '($(this), event)"></i></div></li>' ;
                    if(userArr){ // 需要获取所有用户
                        var jsonStr = { "userid" :  userList[j]["userID"]  } ;
                        userArr.push(jsonStr) ;
                    }
                }
            }
            if(hasKids){
                strAll += "</ul></li>" ;
            }else{
                strAll += "</li>" ;
            }
        }
    }
    return strAll ;
}

/* creator: 侯杏哲 2017-12-06 工具方法 - 设置右边的部门树  */
function setRightStr(right){
    if(right && right.length > 0){
        for(var i in right){
            var hasKids = false ;
            var userList = right[i]["userList"]; // 人员列表
            var kidList = right[i]["subList"]; // 部门列表
            if(kidList && kidList.length > 0){ // 遍历子级部门
                setRightStr( kidList ) ;
            }
            if(userList && userList.length > 0){ // 遍历员工
                for(var j in userList){
                    var userID = userList[j]["userID"] ;
                    var jsonStr = { "userid" :  userID } ;
                    userArr.push(jsonStr);
                    var isEObj = isExist( "uid" , userID , $("#allRight") ) ;
                    if(isEObj){
                        isEObj.children(".fa-plus-square").click() ;
                        isEObj.click() ;
                    }
                }
            }
        }
    }
}

/* creator: 侯杏哲 2017-12-06 工具方法 对比左右的字符串 */
function chargeBoth(leftObj , rightObj) {
    rightObj.find(".fa-info").each(function(){
        var _thisInfo = $(this) ;
        var userID = _thisInfo.parent().attr("info") ;
        var kls = _thisInfo.siblings(".fa-minus-square").hasClass("ty-gray") ; // 当前的这个是不是灰色的
        if(!kls){ // 当前是高亮的
            var isAllAct = true ; // 默认所有都是高亮
            var sib = _thisInfo.parent().parent().siblings("li") ;
            if(sib.length > 0){
                sib.each(function(){
                    var isAct = $(this).children("div").children(".fa-minus-square").hasClass("ty-gray") ;
                    if( isAct ){ // 不是高亮的
                        isAllAct = false ;
                    }
                })
            }
            if(isAllAct){ // 符合要求，父级设置为高亮
                var pDiv = _thisInfo.parent("div").parent("li").parent("ul").siblings("div") ;
                var lass = pDiv.attr("lass") ;
                var deparID = pDiv.attr("info") ;
                pDiv.children(".fa-minus-square").removeClass("ty-gray") ;
                var isE_DivObj = isExist( lass , deparID , $("#allRight") ) ;
                if(isE_DivObj){ isE_DivObj.children(".fa-plus-square").addClass("ty-gray") ;  }
            }
        }
    }) ;
    var bigAct = true ;
    rightObj.children("li:first").children("ul").children("li").each(function(){
        var isA = $(this).children("div").children(".fa-minus-square").hasClass("ty-gray") ;
        if(isA){ bigAct = false ;  }
    });
    if(bigAct){
        rightObj.children("li:first").children("div").children(".fa-minus-square")
    }
}

/* creator：侯杏哲，2017-12-07 13:46:44 权限设置 - 加号 */
function plus( obj , event) {
    event.stopPropagation() ;
    $("#sureChangeRight").attr("class" ,"ty-btn ty-btn-blue ty-btn-big ty-circle-5").attr("onclick" , "sureChangeRight()") ;
    var kls = obj.hasClass("ty-gray") ;
    if( kls ){
        console.log("不生效!");
    }else{
        var lass = obj.parent().attr("lass") , id = obj.parent().attr("info") ;
        var isE = isExist(lass , id , $("#nowRight")) ;
        if(isE){ // 右边已存在
            isE.children("i.fa-minus-square").removeClass("ty-gray") ; // 右边点亮
            var nUlObj = isE.next("ul") ;
            if(nUlObj){ nUlObj.find(".fa-minus-square").each(function(){ $(this).removeClass("ty-gray") ;  } ) }
            obj.addClass("ty-gray") ; // 左边熄灭
            var lUlObj = obj.parent().next("ul") ;
            if(lUlObj){ lUlObj.find(".fa-plus-square").each(function(){ $(this).addClass("ty-gray") ;  } ) }
        }else{
            // 以下是右边需要新增的情况
            var len = obj.parents("li").length ;
            if( len > 1){ // 不是一级文件夹
                var pDivObj = obj.parent("div") ;
                var pLiObj = pDivObj.parent(); // 点击的哪一级全部li
                var p2UlObj = pLiObj.parent("ul") ; // 点击的包含这一级的ul
                var kDivStr = pLiObj.html() ;
                pLiObj.html("REPLACE") ;
                var firstLiObj = p2UlObj.parents("li:last") ;
                var str = "<li>" + firstLiObj.html() + "</li>";
                var minusStr1 = str.replace( /fa-plus-square/g , "fa-minus-square ty-gray") ; // 上级的减号不能点
                minusStr1 = minusStr1.replace( /plus/g , "minus") ; // 将上级的方法替换
                var kidStr = kDivStr.replace( /plus/g , "minus") ; // 子级的加号替换为减号
                var minusStr = minusStr1.replace("REPLACE" , kidStr ) ; // 组成用来替换的字符串
                $("#nowRight").append( minusStr ) ;
                pLiObj.html(kDivStr) ;
                pLiObj.children().find("i.fa-plus-square").addClass("ty-gray") ;
            }else{ // 一级文件夹
                var liObj = obj.parents("li:last") ;
                var str = liObj.html() ;
                str = "<li>" + str + "</li>" ;
                var minusStr = str.replace( /plus/g , "minus") ;
                $("#nowRight").append( minusStr ) ;
                liObj.find("i.fa-plus-square").addClass("ty-gray") ;
            }
        }
        /* 补充显示 */
        chargeBoth( $("#allRight") , $("#nowRight") );
    }

}

/* creator：侯杏哲 2017-12-07 判断ulObj是否有选中的id , 有的话返回该div 对象 */
function isExist( lass , id , ulObj ) {
    var isE = false ;
    ulObj.children("li").each(function(){
        var _thisDiv = $(this).children("div") ;
        var _lass = _thisDiv.attr("lass") ;
        var _id = _thisDiv.attr("info") ;
        if(_lass == lass && _id == id){ // 右边有这个部门
            isE = _thisDiv ; return _thisDiv ;
        }
        if(!isE){
            var kUlObj = $(this).children("ul") ;
            if(kUlObj){ isE = isExist( lass , id , kUlObj ) }
        }
    }) ;
    return isE ;
}

/* creator：侯杏哲，2017-12-07 13:46:44 权限设置 - 减号 */
function minus( obj , event) {
    event.stopPropagation();
    $("#sureChangeRight").attr("class" ,"ty-btn ty-btn-blue ty-btn-big ty-circle-5").attr("onclick" , "sureChangeRight()") ;
    var kls = obj.hasClass("ty-gray") ;
    if( kls ){
        console.log("不生效!");
    }else{
        var pDivObj = obj.parent() ;
        var lass = pDivObj.attr("lass") , id = pDivObj.attr("info") ;
        var isE_DivObj = isExist( lass , id , $("#allRight") ) ;
        // 处理左边
        if(isE_DivObj){
            isE_DivObj.find("i.ty-gray").removeClass("ty-gray") ;
            var ulObj = isE_DivObj.next("ul");
            if(ulObj){ // 子级全部点亮
                ulObj.find("i.ty-gray").each(function () {
                    $(this).removeClass("ty-gray") ;
                })
            }
        }else {
            console.log("左面没找到!");
        }
        // 处理右边
        var pLiObj = pDivObj.parent() ;
        var ulID = pLiObj.parent().attr("id") ;
        if(ulID == "nowRight"){ // 是第一级的
            pLiObj.remove() ;
        }else{ //  不是第一级的
            obj.addClass("ty-gray") ;
            var sUlObj = pDivObj.next("ul") ;
            if(sUlObj){ // 处理子级 , 子级全部熄灭
                sUlObj.find(".fa-minus-square").each(function(){
                    $(this).addClass("ty-gray") ;
                })
            }
            // 判断整个大部门 有没有必要删除
            var count = 0 ;
            var firstLiObj = pDivObj.parents("li:last") ;
            var firstUlObj = firstLiObj.children("ul") ;
            if(firstUlObj){
                console.log(firstUlObj.find(".fa-minus-square").length) ;
                firstUlObj.find(".fa-minus-square").each(function(){
                    var ish =  $(this).hasClass("ty-gray") ;
                    if( !ish ){ count++ ; }

                });
            }
            if(count == 0){  // 没有选中的了,删除大部门
                var firDivObj = firstLiObj.children("div") ;
                var firLass = firDivObj.attr("lass") ;
                var firId = firDivObj.attr("info") ;
                var isEx = isExist( firLass , firId , $("#allRight") ) ;
                isEx.find(".fa-plus-square").removeClass("ty-gray") ;
                firstLiObj.remove() ;
            }
        }
        /* 补充显示 */
        chargeBoth( $("#allRight") , $("#nowRight") );
    }
}

laydate.render({elem: '#cp_acceptDate'});
laydate.render({elem: '#queryBeginTime'});
laydate.render({elem: '#queryEndTime'});