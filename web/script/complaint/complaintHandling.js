$(function () {
    // 三种状态切换（待处理、已批准、已驳回）
    $(".mainPage .ty-secondTab li").on("click", function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        //获取点击按钮的下标
        var index = $(this).index();
        // getComplaintByStatus(index+1);
        $(".mainPage .tblContainer").eq(index).show().siblings(".tblContainer").hide();
        if(index === 3){
            getPassedCase(1,20,1);
        }else{
            getComplaintByStatus(index+3);
        }
    });
    $(".divisionPage .ty-secondTab li").on("click", function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        //获取点击按钮的下标
        var index = $(this).index();
        $(".divisionPage .tblContainer").eq(index).show().siblings(".tblContainer").hide();
        getDivisionList();
    });
    $(".departTree").on("click" , "li>div" , function () {
        $(this).next().toggle();
    });
    $("#cp_customerName").on("change",function () {
        var code = $(this).find("option:selected").attr("code");
        if(code === "null"){
            $("#cp_customerCode").val("--");
        }else{
            $("#cp_customerCode").val(code);
        }
    });
    // 单选按钮事件
    $(".ty-radio").on("click",function () {
        $(this).addClass("ty-radioActive").siblings().removeClass("ty-radioActive");
        $(this).siblings().children("i").attr("class","fa fa-circle-o")
        $(this).children("i").attr("class","fa fa-dot-circle-o")
        // $(this).siblings(".hd").val($(this).attr("value"));
    });
    //本年、去年、前年切换状态
    $("#changeState .ty-btn").on("click",function(){
        //样式切换
        $(this).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
        $("#loginQueryBtn").removeClass("ty-btn-blue");

        //清空自定义选择的时间
        $("#queryBeginTime").val("");
        $("#queryEndTime").val("");

        //获取对应数据
        var index = $(this).index();
        getPassedCase(1,20,index+1) ;
    });
    mainInit();
});

function mainInit() {
    var complaintID = getUrlParam("complaintID");
    var approvalStatus = getUrlParam("approvalStatus"); //0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
    if(complaintID){
        var bounceFootStr = '';
        if (approvalStatus === "3" || approvalStatus === "5"){
            bounceFootStr = '<button class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</button>' +
                            '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeCaseBtn()">结案</button>';
        }
        $(".mainPage .ty-secondTab li").eq(approvalStatus-3).click();

        $("#seeComplaint .bonceFoot").html(bounceFootStr);
        $("#seeComplaint .bonceCon").attr("id",complaintID);
        getComplaintDetail("#seeComplaint",complaintID,approvalStatus,3);
        bounce.show($("#seeComplaint"));
    }else{
        $(".mainPage .ty-secondTab li").eq(0).click();
    }
}

//-----------------投诉流程-------------------//

/* creator：张旭博，2018-01-17 15:31:44，获取不同状态的投诉列表 */
function getComplaintByStatus(state) {
    $.ajax({
        url:"../complaint/getComplaintList.do",
        data:{
            "state":state,  //状态，1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
            "role":3        //1-核心人物 2-立案者 3-处理者 4-查看者
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ) {
            var status = data["status"];
            var listData = data["complaintList"];

            var listStr = '';
            if (listData.length > 0) {
                switch (state){
                    case 3:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +       //客户
                                '<td>' + listData[i].customCode + '</td>' +       //客户代号
                                '<td>' + listData[i].contact + '</td>' +       //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +       //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +       //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +       //录入者
                                '<td>' + listData[i].createTime.substring(0, 10) + '</td>' +       //录入时间
                                '<td>' + listData[i].processorName + '</td>' +       //投诉处理人
                                '<td>' + listData[i].registerApproveTime.substring(0,10) + '</td>' +       //审批时间
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '<span class="ty-color-blue" onclick="materialsBtn($(this))">补充材料</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 4:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +       //客户
                                '<td>' + listData[i].customCode + '</td>' +       //客户代号
                                '<td>' + listData[i].contact + '</td>' +       //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +       //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +       //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +       //录入者
                                '<td>' + listData[i].createTime.substring(0, 10) + '</td>' +       //录入时间
                                '<td>' + listData[i].processorName + '</td>' +       //投诉处理人
                                '<td>' + listData[i].settleOpinion + '</td>' +       //结案说明
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 5:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +       //客户
                                '<td>' + listData[i].customCode + '</td>' +       //客户代号
                                '<td>' + listData[i].contact + '</td>' +       //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +       //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +       //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +       //录入者
                                '<td>' + listData[i].createTime.substring(0, 10) + '</td>' +       //录入时间
                                '<td>' + listData[i].processorName + '</td>' +       //投诉处理人
                                '<td>' + listData[i].settleApproveTime.substring(0,10) + '</td>' +       //审批时间
                                '<td>' + listData[i].settleOpinion + '</td>' +       //结案说明
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 6:

                        break;
                }
                $(".mainPage .tblContainer").eq(state - 3).find("tbody").html(listStr);
            }else{
                $(".mainPage .tblContainer").eq(state - 3).find("tbody").html("");
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-18 15:26:31，投诉查看 - 按钮 */
function seeComplaintBtn(selector){
    bounce.show($("#seeComplaint"));
    var state = $(".mainPage .ty-secondTab li").index($(".mainPage .ty-secondTab li.ty-active"))+3;
    var bonceFootStr = '';
    switch (state){
        case 4:
        case 6:
            bonceFootStr =  '';
            break;
        case 3:
        case 5:
            bonceFootStr =  '<button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</button>' +
                '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="closeCaseBtn()">结案</button>';
            break;
    }
    $("#seeComplaint .bonceFoot").html(bonceFootStr);
    var complaintId = selector.parent().parent().attr("id");
    $("#seeComplaint .bonceCon").attr("id",complaintId);
    getComplaintDetail("#seeComplaint",complaintId,state,3);
}

/* creator：张旭博，2018-01-31 09:50:48，投诉查看 - 结案 - 按钮 */
function closeCaseBtn() {
    bounce_Fixed.show($("#closeCase")) ;
    $(".ty-radioActive").removeClass("ty-radioActive").children("i").attr("class","fa fa-circle-o");
    $("#settleOpinion").val("");
    //开启表单验证
    $('body').everyTime('0.5s','closeCase',function(){
        var caseState = $("#operationRadio .ty-radioActive").attr("value");

        if( caseState === undefined ){
            $("#sureCloseCaseBtn").prop("disabled",true)
        }else {
            $("#sureCloseCaseBtn").prop("disabled",false)
        }
    });
}

/* creator：张旭博，2018-01-31 09:59:04，投诉查看 - 审批 - 确认弹窗（立案批准、立案驳回、结案批准、结案驳回） */
function approveComplaintConfirm(type,state){
    if(type === 1){
        //批准
        $("#tip .tipWord").html("您确定批准吗？") ;
        $("#tip .bonceFoot").html('<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="approveComplaint('+state+')">确定</button>') ;
    }else{
        //批准
        $("#tip .tipWord").html('<p>请输入驳回理由：</p><textarea id="rejectReason" cols="30" rows="3" style="width: 300px"></textarea>') ;
        $("#tip .bonceFoot").html('<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="approveComplaint('+state+')">确定</button>') ;
    }
    bounce_Fixed.show($("#tip"));
}

/* creator：张旭博，2018-01-31 09:59:04，投诉查看- 审批 - 确定（立案批准、立案驳回、结案批准、结案驳回、结案） */
function approveComplaint(state) {

    var id = $("#seeComplaint .bonceCon").attr("id");
    var customer = $("#seeComplaint .cp_scan_box .cp_customerName").attr("id");
    var creator = $("#seeComplaint .cp_scan_box .cp_customerName").attr("cid");

    var data = {
        "id":id,                //投诉id
        "state":state,          //状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
        "customer":customer,    //客户id
        "creator":creator,      //创建人id
        "coreCode":3            //1-核心人物
    };
    switch (state){
        //1-立案待审批
        case 1:
            break;
        //2-立案驳回
        case 2:
            var rejectReason = $("#tip #rejectReason").val();
            data["registerRejectReason"] = rejectReason;
            break;
        //3-立案通过
        case 3:
            var processor = $("#seeComplaint .cp_handlerName").val();
            data["processor"] = processor;
            break;
        //4-结案待审批
        case 4:
            var settleType = Number($("#operationRadio .ty-radioActive").attr("value"));
            var settleOpinion = $("#settleOpinion").val();
            data["settleType"] = settleType;
            data["settleOpinion"] = settleOpinion;
            break;
        //5-结案驳回
        case 5:
            break;
        //6-结案通过
        case 6:
            break;

    }
    console.log(data);
    $.ajax({
        url:"../complaint/approvalComplaint.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var status = data["status"];
            if(status === "1"){
                bounce.cancel();
                bounce_Fixed.cancel();
                switch (state){
                    //1-立案待审批
                    case 1:
                        break;
                    //2-立案驳回
                    case 2:
                        layer.msg("立案驳回成功");
                        break;
                    //3-立案通过
                    case 3:
                        layer.msg("立案批准成功");
                        break;
                    //4-结案待审批
                    case 4:
                        layer.msg("结案成功");
                        break;
                    //5-结案驳回
                    case 5:
                        layer.msg("结案驳回成功");
                        break;
                    //6-结案通过
                    case 6:
                        layer.msg("结案批准成功");
                        break;
                }
                $(".mainPage .ty-secondTab li.ty-active").click();
            }else{
                $("#errorTip .tipWord").html("提交失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-18 15:26:32，补充材料 - 按钮 */
function materialsBtn(selector){
    bounce.show($("#supplyComplaint"));
    $('#supplyComplaint .cp_fileUpload').html("");
    $('#supplyComplaint .cp_imgUpload').html("");
    $('#supplyComplaint .cp_imgShow').html("");
    $('#supplyComplaint .cp_fileShow').html("");
    $("#supplyComplaint").find("textarea").val("");
    $("#supplyComplaint").find("input").val("");
    var complaintId = selector.parent().parent().attr("id");
    $("#supplyComplaint .bonceCon").attr("id",complaintId);
    getComplaintDetail("#supplyComplaint",complaintId,0,3);
    $('#supplyComplaint .cp_imgUpload').Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        formData:{
            module: '投诉',
            userId: sphdSocket.user.userID,
            groupUuid: sphdSocket.uuid()
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        uploader:$.webRoot +"/uploads/uploadfyByFile.do",
        onUploadStart:function(){},
        onInit:function(){},
        /* creator：张旭博，2017-05-06 15:15:49，选择文件后执行的方法 */
        onSelect:function (file) {},
        /* creator：张旭博，2017-05-06 15:15:37，上传文件完成执行的方法 */
        onUploadComplete:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传成功！").css("display","block");
        },
        /* creator：王静，2017-08-02 15:16:37，上传文件失败执行的方法 */
        onUploadError:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传失败！").css("display","block");
        },
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file,data){
            let res = JSON.parse(data);

            var groupUuid = res.groupUuid;
            if(groupUuid){
                let gouparr = $('#supplyComplaint .cp_imgUpload').data("gouparr") || []
                gouparr.push({"type":'groupUuid', 'groupUuid':groupUuid})
                $('#supplyComplaint .cp_imgUpload').data("gouparr", gouparr);
            }
            $("#supplyComplaint .cp_add_box .uploadify-queue").html("");

            //新上传的文件展示
            var str = getNewEnclosure(file,data);   //接口在complaintCommon.js中
            $("#supplyComplaint").find(".cp_imgShow").append(str);
        },
        onCancel:function(file){
            // console.log(file.name);
        }
    });
    $('#supplyComplaint .cp_fileUpload').Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.wps;*.et;*.md;',
        multi:true,
        formData:{
            module: '投诉',
            userId: sphdSocket.user.userID,
            groupUuid: sphdSocket.uuid()
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        uploader: $.webRoot +"/uploads/uploadfyByFile.do",
        onUploadStart:function(){},
        onInit:function(){},
        onSelect:function (file) {},
        onUploadComplete:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传成功！").css("display","block");
        },
        onUploadError:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传失败！").css("display","block");
        },
        onUploadSuccess:function(file,data){
            $("#supplyComplaint .cp_add_box .uploadify-queue").html("");
            var res = JSON.parse(data);
            var groupUuid = res.groupUuid;
            if(groupUuid){
                let gouparr = $('#supplyComplaint .cp_fileUpload').data("gouparr") || []
                gouparr.push({"type":'groupUuid', 'groupUuid':groupUuid})
                $('#supplyComplaint .cp_fileUpload').data("gouparr", gouparr);
            }
            //新上传的文件展示
            var str = getNewEnclosure(file,data);   //接口在complaintCommon.js中
            $("#supplyComplaint").find(".cp_fileShow").append(str);
        },
        onCancel:function(file){
            // console.log(file.name);
        }
    });
    //开启表单验证
    $('body').everyTime('0.5s','supplyComplaint',function(){
        var cp_customerName = $("#supplyComplaint").find(".cp_add_box .cp_annex").val();
        if( cp_customerName === "" ){
            $("#sureSupplyComplaintBtn").prop("disabled",true)
        }else {
            $("#sureSupplyComplaintBtn").prop("disabled",false)
        }
    });
}

/* creator：张旭博，2018-01-29 13:32:22，补充材料 - 确定 */
function sureMaterials(type) {
    var list = []
    var gouparr2 = $('#supplyComplaint .cp_fileUpload').data("gouparr");
    var gouparr1 = $('#supplyComplaint .cp_imgUpload').data("gouparr");
    list.push(...gouparr1, ...gouparr2);
    if(type === 'cancel'){
        list.forEach(function (item) {
            fileDelAjax(item);
        })
        bounce.cancel();
        return false;
    }else{
        list.forEach(function (item) {
            cancelFileDel(item);
        })
    }
    var complaintId = $("#supplyComplaint .bonceCon").attr("id");
    var description = $("#supplyComplaint .cp_add_box .cp_annex").val();
    var picArr = [];
    var attachArr = [];
    var picPatch = {"picPatch":picArr};
    var attachPatch = {"attachPatch":attachArr};
    $("#supplyComplaint .cp_add_box .cp_imgShow .cp_img_box").each(function () {
        var path = $(this).attr("datasrc");
        var name = $(this).find(".cp_img_name").html();
        if(path){
            picArr.push({
                "title":name,
                "path":path
            });
        }
    });
    $("#supplyComplaint .cp_add_box .cp_fileShow .cp_img_box").each(function () {
        var path = $(this).attr("datasrc");
        var name = $(this).find(".cp_img_name").html();
        if(path){
            attachArr.push({
                "title":name,
                "path":path
            });
        }
    });
    var data = {
        "complaint":complaintId,
        "module":'投诉',
        "description":description,
        "picPatch":JSON.stringify(picPatch),
        "attachPatch":JSON.stringify(attachPatch)
    };
    $.ajax({
        url:"../complaint/saveSupplement.do" ,
        data:data,
        success:function( data ){
            var status = data["status"];
            if(status === "1"){
                layer.msg("补充成功");
                bounce.cancel();
                $(".mainPage .ty-secondTab li.ty-active").click();
            }else{
                $("#errorTip .tipWord").html("补充失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

//-----------------结案通过（数据筛选）-------------------//

/* creator：张旭博，2018-02-01 14:18:36，获得结案通过的投诉 */
function getPassedCase(curr,totalPage,buttonType) {
    var data = {
        "buttenType":buttonType,//1代表获取本年的结案的case（即刚点进来时就要加载的方法，传2代表获取去年结案的case，传3代表获取前年结案的case，传4代表按某个特定的时间段获取结案的case
        "role":3,//身份信息 1是核心人物 2是立案者 3是处理者 4是查看者
        "currentPageNo":curr,
        "pageSize":totalPage
    };
    if(buttonType === 4){
        var startTime = $("#queryBeginTime").val()+" 00:00:00";
        var endTime = $("#queryEndTime").val()+" 23:59:59";
        data["startTime"] = startTime;//开始时间
        data["endTime"] = endTime;//结束时间
    }

    $.ajax({
        url:"../complaint/getPassedCase.do",
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var mainData = data["data"],
                pageInfo = data["page"];

            var complaintList   = mainData.complaintList,
                startDay        = formatTime(mainData.startDay,false),
                endDay          = formatTime(mainData.endDay,false),
                totalResult     = pageInfo.totalResult;

            var tipStr          = '<div class="ty-alert ty-alert-warning">自'+startDay+'~'+endDay+'，已结案的投诉共 <span class="ty-color-blue complaintResult">'+totalResult+'</span> 件</div>',
                complaintListStr= '';

            if(complaintList) {
                for (var i = 0; i < complaintList.length; i++) {
                    complaintListStr += '<tr id="' + complaintList[i].id + '">' +
                        '<td>' + complaintList[i].customName + '</td>' +       //客户
                        '<td>' + complaintList[i].customCode + '</td>' +       //客户代号
                        '<td>' + complaintList[i].contact + '</td>' +       //客户联系人
                        '<td>' + complaintList[i].contactPhone + '</td>' +       //联系方式
                        '<td>' + formatTime(complaintList[i].receiptTime) + '</td>' +       //接到投诉的日期
                        '<td>' + complaintList[i].createName + '</td>' +       //录入者
                        '<td>' + formatTime(complaintList[i].createTime) + '</td>' +       //录入时间
                        '<td>' + complaintList[i].processorName + '</td>' +       //投诉处理人
                        '<td>' + formatTime(complaintList[i].settleApproveTime) + '</td>' +       //审批时间
                        '<td>' + complaintList[i].settleOpinion + '</td>' +       //结案说明
                        '<td>' +
                        '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                        '</td>' +
                        '</tr>';
                }
                $(".mainPage .passedPage .tplContainer").eq(0).find(".tip").html(tipStr);
                $(".mainPage .passedPage .tplContainer").eq(0).find("tbody").html(complaintListStr);
            }else{
                $(".mainPage .tblContainer").eq(5).find("tbody").html("");
            }
            loading.close();

        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        },
        complete:function(){ }
    }) ;
}


//-----------------辅助方法-------------------//

/* creator：张旭博，2018-01-25 15:01:43，删除图片或附件 */
function deleteImg(selector){
    selector.parent().remove();
    let info = JSON.parse(_this.siblings(".delInfo").html())
    fileDelAjax(info)
}

/* creator：张旭博，2018-01-18 09:59:33，返回综合管理 */
function goBack(state) {
    if(state === 'division'){
        $(".mainPage").show();
        $(".divisionPage").hide();
    }else if(state === "passDetail"){
        $(".passedPage").show();
        $(".passedDetailPage").hide();
    }

}

laydate.render({elem: '#cp_acceptDate'});
laydate.render({elem: '#queryBeginTime'});
laydate.render({elem: '#queryEndTime'});