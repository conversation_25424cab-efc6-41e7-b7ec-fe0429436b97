/* 查看投诉详情的页面处理 */
var baseId = 0 ; // 投诉id
var processId = 0 ; //审批流程的id
var sendType = 0 ; // 根据角色展示不同的投诉详情页面
var complaintInfo = null ;
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#editContact"));
bounce_Fixed2.cancel();
$(function() {
    baseId = getUrlParam("id");
    sendType = Number(getUrlParam("t"));
    var titile = chargeTitle(sendType);
    $("#title").html(titile);
    getCompaint( baseId ) ;
    // 核心人物 订阅 停权  结果
    sphdSocket.subscribe('complaintMessage', complaintMessageCallback , function(){console.log('停权失败！')},'user');
    // 核心人物 订阅 立案审批/更换处理者  结果
    sphdSocket.subscribe('complaintMessage',complaintMessageCallback,null,'custom',baseId);
    // 给上传按钮绑定事件
    $(".fileCon").on("click",".uploadify-button", function() {
        uploadCode = 'f'+ Math.random().toString(36).substr(2);
        var id = $(this).attr("id")
        var num = id.split("_")[2];
        num = num.split("-")[0];
        if(num == 1){
            uploadType = 1 ;  // 上传文件类型:1-照片,2-其他文件
        }else{
            uploadType = 2 ;
        }
    });
    $("body").on("mouseover", ".upItem", function (item) {
        $(this).children(".fileScanMark").show();
    })
    $("body").on("mouseout", ".upItem", function (item) {
        $(this).children(".fileScanMark").hide();
    })
    $("body").on("click", ".fileScan", function (item) {
        // var path =  $.ow365url + $(this).parent().parent().attr("path");
        var path = $(this).parent().parent().attr("path");
        $(this).attr("path", path);
        window.parent.seeOnline($(this), "parent");
    })
    $("body").on("click", ".fileDown", function (item) {
        var path =  $(this).parent().parent().attr("path");
        $(this).attr("path", path);
        window.parent.getDownLoad($(this), "iframe");
    })
});
// creator : hxz 2019-06-04 获取投诉详情
function getCompaint( baseId ) {
    $.ajax({
        "url":"../complaint/complaintBaseMessage.do",
        "data": { "baseId": baseId },
        success:function(res) {
            complaintInfo = res ;
            var complaint = res["complaint"] ;
            var listApprovalProcess = res["listApprovalProcess"] ; // 审批流程
            var listDetail = res["listDetail"] ; // 投诉的详细信息
            var listSupplement = res["listSupplement"] ; //  补充材料的信息
            $("#cusName").html(complaint["customName"]);
            $("#cusCode").html(complaint["customCode"]);
            var state = complaint["state"]; // 0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
            var receiptTime = new Date(complaint["receiptTime"]).format("yyyy-MM-dd") ;
            var processorName = complaint["processorName"] ; // 处理者
            var processor = complaint["processor"] ; // 处理者
            // 通过 sendType 和 state 判断 展示的内容
            var isChargeNo = false, // 立案驳回结果
                isEnd = false, // 结案驳回结果
                isEndInfo = false, // 结案报告
                userRole = 0, //（更换处理者的按钮 / 投诉日期行 ）0-立案者 1-核心人物 2-处理者
                isComplaintInfo = false, // 展示投诉详情
                control_li = false, // 立案审批按钮
                control_applyEnd = false, // 提交结案申请按钮
                control_End = false, // 结案按钮
                control_endApplyBtn = false, // 最后一个结案申请按钮
                control_addMoreBtn = true, // 增加补充材料 按钮
                control_chargeApplyEnd = false; // 审批结案申请按钮
            switch (sendType){
                case 2: // 2- (核心人物：立案待审批 跳过来的详情页)
                    isComplaintInfo = true ;
                    control_li = true ;
                    break;
                case 1: // 1- (立案者：投诉录入 跳过来的详情页)
                    isComplaintInfo = true ;
                    break;
                case 3: // 3- 核心人物：待结案 跳过来的详情页
                    userRole = 1 ;
                    control_End = true ;
                    if(state == 5){
                        isEnd = true ;
                        isEndInfo = true ;
                    }else{
                        isComplaintInfo = true ;
                    }
                    if(listApprovalProcess && listApprovalProcess.length>1){
                        isEnd = true ;
                        isEndInfo = true ;
                    }else{
                        isComplaintInfo = true ;
                    }
                    break;
                case 4: // 4- 处理者(或者立案者)：待结案 跳过来的详情页
                    // if(state == 5){
                    if(listApprovalProcess && listApprovalProcess.length>1){
                        isEnd = true ;
                        isEndInfo = true ;
                    }else{
                        isComplaintInfo = true ;
                    }
                    control_applyEnd = true ;
                    userRole = 2 ;
                    break;
                case 5: // 5- 处理者：结案待审批 跳过来的详情页
                    userRole = 2 ;
                    isEndInfo = true ;
                    break;
                case 6: // 6- 核心人物：结案待审批 跳过来的详情页
                    userRole = 1 ;
                    isEndInfo = true ;
                    control_chargeApplyEnd = true ;
                    break;
                case 7: // 7- (立案者：立案驳回的消息 跳过来的详情页)
                    isChargeNo = true ;
                    control_addMoreBtn = false ;
                    break;
                case 8: // 8- (处理者：结案申请通过的消息 跳过来的详情页)
                    userRole = 2 ;
                    isComplaintInfo = true ;
                    control_addMoreBtn = false ;
                    break;
                case 9: // 立案者：查询已驳回的投诉
                    isChargeNo = true ;
                    control_addMoreBtn = false ;
                    break;
                case 10: // 处理者或者核心人物：查询已结案跳转过来的
                    userRole = 2 ;
                    isComplaintInfo = true ;
                    control_endApplyBtn = true ;
                    control_addMoreBtn = false ;
                    break;
                default:
                    return "";
            }
            if(isChargeNo){
                $(".chargeNo").show();
                var rejectReason = complaint["registerRejectReason"]; // 立案驳回原因
                $("#rejectReason").html(rejectReason);
                $("#receiptTime2").html(receiptTime);
            }
            if(isEnd){
                $(".chargeEndApoNo").show();
                var settle_reject_reason = complaint["settleRejectReason"];
                $("#chargeEndApoNoReason").html(settle_reject_reason);
            }
            if(isEndInfo){
                console.log('complaint["settleType"]  ')
                console.log(complaint["settleType"])
                $(".endComplaint").show();
                $("#endType").html(chargeSettleType(complaint["settleType"]));
                $("#endOpinion").html(complaint["settleOpinion"]);
            }
            if(userRole == 0){
                $(".apply").show();
                $("#receiptTime2").html(receiptTime);
            }else{
                $(".apprpve").show();
                $("#processorName").html(processorName);
                $("#processor").val(processor);
                $("#receiptTime").html(receiptTime);
                if(userRole == 1){
                    $(".chargeChange").show();
                }
            }
            if(isComplaintInfo){
                // 展示投诉详情
                var type = complaint["type"]; // 投诉类型:1-与产品有关,2-与产品无关
                if(type == 1){
                    $(".yesPro").show();
                    if(listDetail && listDetail.length>0){
                        var strPro = "" ;
                        for(var j = 0 ; j < listDetail.length ; j++){
                            strPro +=
                                "<tr>" +
                                "<td>"+ listDetail[j]["outersn"] +"</td>" +
                                "<td>"+ listDetail[j]["outerName"] +"</td>" +
                                "<td>"+ listDetail[j]["innerSn"] +"</td>" +
                                "<td>"+ listDetail[j]["innerName"] +"</td>" +
                                "<td class=\"ty-td-control\">" +
                                "<span class=\"ty-color-blue\" onclick='scanGoods($(this))'>查看</span>" +
                                "<span class='hd'>"+ JSON.stringify(listDetail[j]) +"</span>" +
                                "</td>" +
                                "</tr>";
                        }
                        $("#proList tbody").append(strPro) ;
                    }
                }else{
                    $(".noPro").show();
                    var info = listDetail[0] ;
                    $("#problem").html(info["problem"]);
                    $("#consequence").html(info["consequence"]);
                    $("#appeal").html(info["appeal"]);
                    $("#memo").html(info["memo"]);
                    var files = info["detailComplaintAttachmentHashSer"];
                    if(files && files.length>0){
                        for(var i = 0 ; i < files.length ; i++){
                            var type_f = files[i]["type"] ;
                            var title_f = files[i]["title"] ;
                            var path_f = files[i]["path"] ;
                            var fileType = title_f.split(".").pop() ;
                            var fileName = title_f.replace("."+ fileType,"") ;
                            var itemTemp =
                                '<div title="'+ title_f +'" path="'+ path_f +'" type="'+ type_f +'" class="upItem uploadify-queue-item"  >' +
                                    '<div class="fileScanMark">' +
                                        '<a class="fileScan">查看</a>'+
                                        '<a class="fileDown">下载</a>'+
                                    '</div>'+
                                    '<span class="up_fileType">'+ chargeFileType(fileType) +'</span>' +
                                    '<span class="up_name">'+ fileName +'</span>' +
                                '</div>';
                            if(type_f == 1){ // 上传文件类型:1-照片,2-其他
                                $('#pic').append(itemTemp) ;
                            }else{
                                $('#doc').append(itemTemp) ;
                            }
                            $(".up_fileType").show();
                        }
                    }
                }
            }
            if(control_li){
                $(".ctrl_li").show();
            }
            if(control_applyEnd){
                $(".ctrl_applyEnd").show();
            }
            if(control_End){
                $(".ctrl_End").show();
            }
            if(control_chargeApplyEnd){
                $(".ctrl_chargeApplyEnd").show();
            }
            // 展示审批流程
            if(listApprovalProcess && listApprovalProcess.length>0){
                var strCharge = "" ;
                var len = listApprovalProcess.length -1
                for(var q = 0 ; q < listApprovalProcess.length ; q++){
                    var name = q == 0 ? "原始投诉" : "结案申请" ;
                    var nameStr = "<td>"+ name +"</td>" ;
                    if(q == 0){
                        if(!isComplaintInfo){
                            nameStr = "<td class='ty-td-control'><span class='ty-color-blue' onclick='seeOral()'>"+ name +"</span>" +"</td>" ;
                        }
                    }else {
                        if(q == len){
                            if(control_endApplyBtn){// 最后一个是否可查看
                                nameStr = "<td class='ty-td-control'><span class='ty-color-blue' onclick='seeEndApply($(this))'>"+ name +"</span><span class='hd'>"+ JSON.stringify(listApprovalProcess[q]) +"</span></td>" ;
                            }
                        }else{
                            nameStr = "<td class='ty-td-control'><span class='ty-color-blue' onclick='seeEndApply($(this))'>"+ name +"</span><span class='hd'>"+ JSON.stringify(listApprovalProcess[q]) +"</span></td>" ;
                        }
                    }
                    var fromUserID = listApprovalProcess[q]["fromUser"] ;
                    var toUserID = listApprovalProcess[q]["toUser"] ;
                    var strFloat = "<p>"+ listApprovalProcess[q]["userName"] +" "+ (new Date(listApprovalProcess[q]["createDate"]).format('yyyy-MM-dd hh:mm:ss')) +"</p>" ;
                    // 核心人物 停权
                    strFloat = "<p>"+ listApprovalProcess[q]["userName"] +" "+ (new Date(listApprovalProcess[q]["createDate"]).format('yyyy-MM-dd hh:mm:ss')) ;
                    if(q == 0 && ([2,3,6].indexOf(Number(sendType))) > -1){ // 核心人物
                        if(fromUserID != toUserID){ // 不是自己的申请
                            if(complaint["terminateState"] == 0){
                                strFloat += " <span class='ty-color-blue' onclick='stopRight($(this))'>停权</span></p>" ;
                            }else{
                                strFloat += " <span class='ty-color-gray'>停权</span></p>" ;
                            }
                        }else{
                            strFloat += "</p>" ;
                        }
                    }else{
                        strFloat += "</p>" ;
                    }
                    if(fromUserID != toUserID){
                        var handleTime = listApprovalProcess[q]["handleTime"] ;
                        if(handleTime){
                            strFloat += "<p>"+ listApprovalProcess[q]["toUserName"] +" "+ (new Date(handleTime).format('yyyy-MM-dd hh:mm:ss')) +"</p>" ;
                        }
                    }
                    strCharge += "<tr>" + nameStr + "<td class='ty-td-control'>" + strFloat + "</td>" + "</tr>";
                }
                processId = listApprovalProcess[listApprovalProcess.length-1]["id"] ;
                $("#chargeFloat").append(strCharge);
            }
            // 展示补充材料
            if(!control_addMoreBtn){
                $("#addMoreBtn").hide()
            }
            setListSupplement(listSupplement, 0);
        }
    })
}

// creator:hxz 2020-01-14  查看文件
function fileScan(thisObj) {

}
// creator:hxz 2020-01-14  下载文件
function fileDown(thisObj) {

}
//creator:hxz 2019-06-20 工具方法： 返回结案类型
function chargeSettleType(type){
    switch (Number(type)){ // char(1)  comment '结案类型:1-真实投诉，现已处理完毕并获客户确认,2-不属我公司问题，且已得到客户确认,3-其他原因，不该算作投诉,4-与其他投诉重复
        case 1:
            return "真实投诉，现已处理完毕并获客户确认";
            break;
        case 2:
            return "不属我公司问题，且已得到客户确认";
            break;
        case 3:
            return "其他原因，不该算作投诉";
            break;
        case 4:
            return "与其他投诉重复";
            break;
        default:
            return ""
    }
}
// 工具方法： 返回文件类型的DOM串
function chargeFileType(fileType) {
    var midtermStr = "";
    switch (fileType) {
        case "doc":
        case "docx":
            midtermStr = '<div class="ty-fileType ty-file_doc"></div>';
            break;
        case "xls":
        case "xlsx":
        case "et":
            midtermStr = '<div class="ty-fileType ty-file_xls"></div>';
            break;
        case "ppt":
            midtermStr = '<div class="ty-fileType ty-file_ppt"></div>';
            break;
        case "rar":
        case "zip":
            midtermStr = '<div class="ty-fileType ty-file_rar"></div>';
            break;
        case "pdf":
            midtermStr = '<div class="ty-fileType ty-file_pdf"></div>';
            break;
        case "png":
        case "jpg":
        case "gif":
            midtermStr = '<div class="ty-fileType ty-file_jpg"></div>';
            break;
        default:
            midtermStr = '<div class="ty-fileType ty-file_other"></div>';
    }
    return midtermStr ;
}
// creator : hxz 2019-06-04 区别显示标题
function chargeTitle(type) {
    switch ((type)){
        case 2: // 2- (核心人物：立案待审批 跳过来的详情页)
        case 1: // 1- (立案者：投诉录入 跳过来的详情页)
        case 7: // 7- (立案者：立案驳回的消息 跳过来的详情页)
        case 9: // 9- (立案者：查询已驳回的投诉)
            return "客户投诉立案申请"; break;
        case 3: // 3- 核心人物：待结案 跳过来的详情页
        case 4: // 4- 处理者：待结案 跳过来的详情页
        case 8: // 8-处理者：结案申请通过的消息 跳过来的详情页
        case 10: // 10-处理者或者核心人物：查询已结案跳转过来的
            return "客户投诉"; break;
        case 5: // 5- 处理者：结案待审批 跳过来的详情页
        case 6: // 6- 核心人物：结案待审批 跳过来的详情页
            return "客户投诉结案申请"; break;
        default:
            return "";
    }
}
// creator hxz 2019-06-17 停权
var stopRightObj = null ;
function stopRight(thisObj) {
    stopRightObj = thisObj ;
    bounce.show($("#stopRight"));
    var name = thisObj.parent().html().split(" ")[0]
    $("#tipName").html("被停权后，"+ name +"在系统中将无法再见到本次投诉。");
}
// creator hxz 2019-06-17 停权
function stopRightOk() {
    var userID = sphdSocket.user.userID ;
    loading.open();
    console.log('停权传值：', {'userID':userID, 'baseId':baseId })
    sphdSocket.send('terminationAuthority',{'userID':userID, 'baseId':baseId });
}
// creator hxz 2019-06-17 查看原始投诉
function complaintMessageCallback(res) {
    var res = JSON.parse(res);
    var messageType = res["messageType"];
    loading.close();
    bounce.cancel();
    var complaint = res["complaint"] ;
    var listApprovalProcess = res["listApprovalProcess"] ; // 审批流程
    var listDetail = res["listDetail"] ; // 投诉的详细信息
    var listSupplement = res["listSupplement"] ; //  补充材料的信息
    var state = complaint["state"]; // 0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
    var fromUserID = listApprovalProcess[0]["fromUser"] ; // 立案者
    var toUserID = listApprovalProcess[0]["toUser"] ; // 核心人物
    var processorName = complaint["processorName"] ; // 处理者 名字
    var processor = complaint["processor"] ; // 处理者 ID
    var curUser = sphdSocket.user.userID ; // 当前登陆者
    var url = location.href ;
    switch(Number(messageType)){
        case 1:
        case 2:
            var typeStr = Number(messageType) == 1 ? "批准" : "驳回" ;
            layer.msg('该投诉立案已被'+ typeStr +'！');
            if(toUserID == curUser){ // 核心人物 审批完返回
                setTimeout(function(){ historyBack(); }, 1000) ;
            }else{ // 立案者
                // url = url.replace(sendType ,  7) ;
                if(Number(messageType) == 1 ){
                    setTimeout(function(){ historyBack(); }, 1000) ;
                }else{
                    url = "../../../complaint/complaintInfo.do?id="+ baseId +"&t=7"
                    setTimeout(function(){ location.href = url }, 1000) ;
                }

            }
            break;
        case 3:
            layer.msg('该投诉已申请结案,请在结案待审批中查看！');
            setTimeout(function(){ historyBack(); }, 1000) ;
            // location.href = "../../../complaint/complaintInfo.do?id="+ baseId +"&t=5"
            // $("#endComplaint4").hide();
            break;
        case 4:
            if(toUserID == curUser){ // 核心人物 审批完返回
                layer.msg('该投诉已结案！');
                $("#endComplaint3").hide();
                setTimeout(function(){ historyBack(); }, 1000) ;
            }else{
                layer.msg('该投诉已结案,请在查询中重新查看');
                setTimeout(function(){ historyBack(); }, 1000) ;
            }
            break;
        case 5:
            layer.msg('该投诉结案申请已被驳回！');
            if(toUserID == curUser){ // 核心人物 审批完返回
                setTimeout(function(){ historyBack(); }, 1000) ;
            }
            break;
        case 6:
            $("#processorName").html(processorName);
            if(processor == curUser){
                layer.msg('该投诉已更换处理者，您不再是该投诉的处理者！');
                loading.open();
                setTimeout(function(){ historyBack(); }, 1000) ;
            }else{
                layer.msg('该投诉已更换处理者！');
            }
            break;
        case 7:
            if(fromUserID == curUser){ // 立案者
                layer.msg("您已被停权！");
                setTimeout(function(){ historyBack(); }, 1000) ;
            }else {
                layer.msg("该投诉立案者已被停权！");
                if(toUserID == curUser){ // 核心人物 审批完返回
                    stopRightObj.attr("class", "ty-color-gray");
                }
            }
            break;
        case 8:
            // listSupplement
            layer.msg("该投诉新增加了补充材料！");
            console.log('增加了补充材料 返回值 ', res) ;
            setListSupplement(listSupplement , 1) ;
            break;
        default:
            break;
    }

}
// creator hxz 2019-06-06  展示补充材料
function setListSupplement(listSupplement , type) {
    if(listSupplement && listSupplement.length >0){
        $("#more").show();
        $("#more tbody").children(":gt(0)").remove();
        var strMore = "" ;
        for(var w = 0 ; w < listSupplement.length ; w++){
            strMore +=
                "<tr>" +
                "<td class='ty-td-control'><span class='ty-color-blue' onclick='seeMore($(this), "+ type +")'>补充材料</span><span class='hd'>"+ JSON.stringify(listSupplement[w]) +"</span>" +"</td>" +
                "<td>" + listSupplement[w]["createName"]+ " " + (new Date(listSupplement[w]["createTime"]).format("yyyy/MM/dd hh:mm:ss")) + "</td>" +
                "</tr>";
        }
        $("#more").append(strMore);
    }
}
// creator hxz 2019-06-06  核心人物审批结果
function historyBack() {
    window.parent.hideDetailsPage();
   /* var sid = $.cookie("sid");	//最后一级
    var isOk = 0 ;
    $(".menu_mid").each(function(){
        if ($(this).html() == sid) {
            setTimeout(function(){
                var herf = $(this).parent("a").attr("href");
                if(herf){
                    isOk = 1 ;
                    location.href = herf ;
                }
            }, 1000) ;
        }
    });
    if(isOk == 0){
        // location.href = "../sys/index.do" ;

    }*/
}
// creator hxz 2019-06-25 查看结案申请
function seeEndApply(thisObj) {
    bounce.show($("#seeEndApply"));
    var info = JSON.parse(thisObj.siblings(".hd").html()) ;
    var approveStatus = info["approveStatus"]; // 1是待审批 2批准 3是驳回
    var reason = info["reason"]; // 驳回的意见
    var description = info["description"]; // 结案意见
    var type  = info["type"];
    // 结案类型:6-真实投诉，现已处理完毕并获客户确认,7-不属我公司问题，且已得到客户确认,8-其他原因，不该算作投诉,9-与其他投诉重复
    // (不要和complaint中的settleType混淆，settleType的1-4不变)
    $("#settleTypeInfo").html( chargeType(type));
    $("#opinionInfo").html(description);
    if(approveStatus == 3){
        $("#reasonInfo").html(reason);
        $("#seeEndApply .bo").show();
    }else{
        $("#seeEndApply .bo").hide();
    }
}
// creator hxz 2019-06-26 工具方法 - 返回结案类型字符串
function chargeType(type) {
    switch (Number(type)){
        case 6:
            return "真实投诉，现已处理完毕并获客户确认" ; break;
        case 7:
            return "不属我公司问题，且已得到客户确认" ; break;
        case 8:
            return "其他原因，不该算作投诉" ; break;
        case 9:
            return "与其他投诉重复" ; break;
        default :
            return "未知类型"
    }
}
// creator hxz 2019-06-17 查看原始投诉
function seeOral() {
    bounce.show($("#seeOral"));
    var res = complaintInfo ;
    var complaint = res["complaint"] ;
    var listDetail = res["listDetail"] ; // 投诉的详细信息
    $("#cusName3").html(complaint["customName"]);
    $("#cusCode3").html(complaint["customCode"]);
    var receiptTime = new Date(complaint["receiptTime"]).format("yyyy-MM-dd") ;
    $("#receiptTime3").html(receiptTime);
    var type = complaint["type"]; // 投诉类型:1-与产品有关,2-与产品无关
    if(type == 1){
        $("#seeOral .yesPro").show();
        if(listDetail && listDetail.length>0){
            var strPro = "" ;
            for(var j = 0 ; j < listDetail.length ; j++){
                strPro +=
                    "<tr>" +
                    "<td>"+ listDetail[j]["outersn"] +"</td>" +
                    "<td>"+ listDetail[j]["outerName"] +"</td>" +
                    "<td>"+ listDetail[j]["innerSn"] +"</td>" +
                    "<td>"+ listDetail[j]["innerName"] +"</td>" +
                    "<td class=\"ty-td-control\">" +
                    "<span class=\"ty-color-blue\" onclick='scanGoods($(this))'>查看</span>" +
                    "<span class='hd'>"+ JSON.stringify(listDetail[j]) +"</span>" +
                    "</td>" +
                    "</tr>";
            }
            $("#proList3 tbody").children("tr:gt(0)").remove();
            $("#proList3 tbody").append(strPro) ;
        }
    }else{
        $("#seeOral .noPro").show();
        var info = listDetail[0] ;
        $("#problem3").html(info["problem"]);
        $("#consequence3").html(info["consequence"]);
        $("#appeal3").html(info["appeal"]);
        $("#memo3").html(info["memo"]);
        var files = info["detailComplaintAttachmentHashSer"];
        if(files && files.length>0){
            for(var i = 0 ; i < files.length ; i++){
                var type_f = files[i]["type"] ;
                var title_f = files[i]["title"] ;
                var path_f = files[i]["path"] ;
                var fileType = title_f.split(".").pop() ;
                var fileName = title_f.replace("."+ fileType,"") ;
                var itemTemp =
                    '<div title="'+ title_f +'" path="'+ path_f +'" type="'+ type_f +'" class="upItem uploadify-queue-item">' +
                    '<div class="fileScanMark">' +
                    '<a class="fileScan">查看</a>'+
                    '<a class="fileDown">下载</a>'+
                    '</div>'+
                    '<span class="up_fileType">'+ chargeFileType(fileType) +'</span>' +
                    '<span class="up_name">'+ fileName +'</span>' +
                    '</div>';
                if(type_f == 1){ // 上传文件类型:1-照片,2-其他
                    $('#pic3').append(itemTemp) ;
                }else{
                    $('#doc3').append(itemTemp) ;
                }
                $(".up_fileType").show();
            }
        }
    }

}
// creator hxz 2019-06-05 查看客户联系人
function scanContact() {
    $.ajax({
        "url":"../complaint/getConmplaintContact.do",
        "data":{ "baseId": baseId },
        success:function(res) {
            bounce_Fixed.show($("#scanContact"))
            var list = res["data"]["lsitContact"] , str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    str +=
                        "<div class=\"personItem\">" +
                            "<div class=\"ty-right\">" +
                                "<i class=\"fa fa-edit\" onclick='editContacter($(this))'></i>" +
                                "<i class=\"fa fa-file-text-o\" onclick='editContactLog($(this))'></i>" +
                                "<i class='hd'>"+ JSON.stringify(list[i]) +"</i>" +
                            "</div>" +
                            "<div>" +
                                "<h4>"+ list[i]["contactName"] +"</h4>" +
                                "<p>职位："+ (list[i]["post"] || "") +"</p>" +
                                "<p>电话："+ (list[i]["contactPhone"] || "") +"</p>" +
                            "</div>" +
                            "<div>" +  (list[i]["contact"] || "") + "</div>" +
                        "</div>"
                }
            }
            $("#contactCon").html(str);
            if(sendType == 9 || sendType == 10){
                $("#contactCon .fa-edit").hide();
                $("#addContacter").hide();
            }else{
                $("#addContacter").show();
            }
        }
    })
}
// creator hxz 2019-06-06 新增 客户联系人
function addContacter() {
    bounce_Fixed2.show($("#editContact"));
    $("#editContact").find("input").val("");
    $("#editContact").find("textarea").val("");
    $("#editContactTtl").html("增加新的客户联系人");
}
var editContact = null;
// creator hxz 2019-06-06 修改 客户联系人
function editContacter(_thisObj) {
    editContact = _thisObj ;
    var info = JSON.parse(_thisObj.siblings(".hd").html()) ;
    bounce_Fixed2.show($("#editContact"));
    $("#contactName_edit").val(info["contactName"]);
    $("#contactPhone_edit").val(info["contactPhone"]);
    $("#contactPost_edit").val(info["post"]);
    $("#contact_edit").val(info["contact"]);
    $("#editContactTtl").html("修改客户联系人信息");
}
// creator hxz 2019-06-06 确定 新增、修改 客户联系人
function editContactOk() {
    var type = $("#editContactTtl").html() ;
    var url = "../complaint/insertComplaintContact.do" ;
    var data = {
        "userID": sphdSocket.user.userID ,
        "complaintBase": baseId ,
        "contactName": $("#contactName_edit").val() ,
        "contact": $("#contact_edit").val() ,
        "contactPhone": $("#contactPhone_edit").val() ,
        "post":  $("#contactPost_edit").val()
    }
    if(type == "修改客户联系人信息"){ // 修改
        url = "../complaint/updateComplaintContact.do" ;
        var info = JSON.parse( editContact.siblings(".hd").html() );
        data["id"] = info["id"] ;
    }
    $.ajax({
        "url" : url ,
        "data": data ,
        success:function(res) {
            bounce_Fixed2.cancel();
            var data = res["data"] ;
            var info = data["complaintContact"] ;
            if(type == "修改客户联系人信息"){ // 修改
                editContact.siblings(".hd").html(JSON.stringify(info)) ;
                var pObj = editContact.parent().parent();
                pObj.children("div:eq(1)").children("h4").html(info["contactName"]) ;
                pObj.children("div:eq(1)").children("p:eq(0)").html("职位："+ info["post"]) ;
                pObj.children("div:eq(1)").children("p:eq(1)").html("电话："+ info["contactPhone"]) ;
                pObj.children("div:eq(2)").html(info["contact"]) ;
            }else{
                var str =
                    "<div class=\"personItem\">" +
                        "<div class=\"ty-right\">" +
                            "<i class=\"fa fa-edit\" onclick='editContacter($(this))'></i>" +
                            "<i class=\"fa fa-file-text-o\" onclick='editContactLog($(this))'></i>" +
                            "<i class='hd'>"+ JSON.stringify(info) +"</i>" +
                        "</div>" +
                        "<div>" +
                            "<h4>"+ info["contactName"] +"</h4>" +
                            "<p>职位："+ info["post"] +"</p>" +
                            "<p>电话："+ info["contactPhone"] +"</p>" +
                        "</div>" +
                        "<div>" + info["contact"] + "</div>" +
                    "</div>";
                $("#contactCon").append(str);
            }
        }
    })
}
// creator hxz 2019-06-06  客户联系人 修改记录
function editContactLog(_thisObj) {
    var info = JSON.parse( _thisObj.siblings(".hd").html() );
    $.ajax({
        "url" : "../complaint/getComplaintContactHistory.do" ,
        "data": { "contanctId": info["id"] } ,
        success:function(res) {
            bounce_Fixed2.show($("#editContactLog"));
            var list = res["data"]["lsitContactHistory"] ;
            var str = "" ;
            if(list && list.length>0){
                for(var i = 0 ; i < list.length ; i++){
                    var ttl = "原始信息", per = "创建者" ;
                    if(i > 0){ ttl = "第"+ i +"次修改后" ; per = "修改者" ; }
                    str +=
                        "<div class=\"logItem\">" +
                            "<p>"+ ttl +"  <span class=\"right\">"+ per +" "+ list[i]["createName"] +" "+ (new Date().format("yyyy/MM/dd hh:mm:ss")) +"</span></p>" +
                            "<table class=\"ty-table\">" +
                                "<tr>" +
                                    "<td width='12%'>姓名</td>" +
                                    "<td width='15%'>"+ (list[i]["contactName"] || "") +"</td>" +
                                    "<td width='12%'>联系方式</td>" +
                                    "<td width='18%'>"+ (list[i]["contactPhone"] || "") +"</td>" +
                                    "<td width='10%'>职位</td>" +
                                    "<td width='33%'>"+ (list[i]["post"] || "") +"</td>" +
                                "</tr>" +
                                "<tr>" +
                                    "<td>客户联系人说明</td>" +
                                    "<td colspan=\"5\">"+ (list[i]["contact"] || "") +"</td>" +
                                "</tr>" +
                            "</table>" +
                        "</div> " ;
                }
            }
            $("#editContactLog .bonceCon").html(str);
        }
    })
}
// creator hxz 2019-06-06 问题产品查看
function scanGoods(_thisObj) {
    var info = JSON.parse( _thisObj.siblings(".hd").html() );
    bounce_Fixed.show($("#scanGoods")) ;
    $("#outSn1").html(info["outersn"]) ;
    $("#outName1").html(info["outerName"]) ;
    $("#inSn1").html(info["innerSn"]) ;
    $("#inName1").html(info["innerName"]) ;
    $("#problem1").html(info["problem"]) ;
    $("#consequence1").html(info["consequence"]) ;
    $("#appeal1").html(info["appeal"]) ;
    $("#memo1").html(info["memo"]) ;
    var files = info["detailComplaintAttachmentHashSer"] ; // 附件的信息
    var signTimes = info["detailComplaintProductOutputHashSer"] ; // 签收时间
    var amount = info["amount"] ; // 问题产品数量
    var percent = info["percent"] ; // 问题产品比例
    var amountStr = amount ? "数量："+ amount + " " : "" ;
    if(percent){
        if(amountStr == ""){
            amountStr += "比例：" + percent + "% " ;
        }else{
            amountStr += " 或 比例：" + percent + "% " ;
        }
    }
    $("#amount1").html(amountStr) ;
    var signTimesStr = "" ;
    if(signTimes && signTimes.length){
        for(var i = 0 ; i < signTimes.length ; i++){
            signTimesStr += (new Date(signTimes[i]["outputTime"]).format("yyyy年MM月dd日")) + " " ;
        }
    }
    $("#signTimes1").html(signTimesStr) ;
    $('#pic1').html("") ;
    $('#doc1').html("") ;
    if(files && files.length>0){
        for(var i = 0 ; i < files.length ; i++){
            var type_f = files[i]["type"] ;
            var title_f = files[i]["title"] ;
            var path_f = files[i]["path"] ;
            var fileType = title_f.split(".").pop() ;
            var fileName = title_f.replace("."+ fileType,"") ;
            var itemTemp =
                '<div title="'+ title_f +'" path="'+ path_f +'" type="'+ type_f +'" class="upItem uploadify-queue-item">' +
                '<div class="fileScanMark">' +
                '<a class="fileScan">查看</a>'+
                '<a class="fileDown">下载</a>'+
                '</div>'+
                '<span class="up_fileType">'+ chargeFileType(fileType) +'</span>' +
                '<span class="up_name">'+ fileName +'</span>' +
                '</div>';
            if(type_f == 1){ // 上传文件类型:1-照片,2-其他
                $('#pic1').append(itemTemp) ;
            }else{
                $('#doc1').append(itemTemp) ;
            }
        }
    }
    $(".up_fileType").show();
}
// creator hxz 2019-06-06 核心人物 审批立案申请
function approveComplaint(state) {
    //  3是批准立案 2是驳回立案 1是更换处理者
    bounce.show($("#approveComplaint"));
    $("#state").val(state) ;
    $(".state" + state).show().siblings().hide();
    if(state == 3 || state == 1){
        $.ajax({
            url:"../coreSetting/getThreeUser.do" ,
            data:{  "coreCode":"handle" },
            success:function(data) {
                var list2 = data["codeUsers"], strCharger = "<option value=''>—— 请选择 ——</option>";
                if(list2 && list2.length>0){
                    for(var j = 0 ; j < list2.length ; j++){
                        strCharger +="<option value='"+ list2[j]["userID"] +"'>"+ list2[j]["userName"] +"</option>";
                    }
                }
                if(state == 3){
                    $("#charger").html(strCharger);
                }else{
                    $("#charger2").html(strCharger);
                    var name = $("#processorName").html();
                    $("#changeHandelerTip").html("当前本投诉处理者为"+ name +"。您可更换为其他人。");
                }

            }
        });
    }

}
function approveComplaintOk() {
    var state = $("#state").val() // 3是批准立案 2是驳回立案 1是更换处理者
    if(state == 1){
        var processor = $("#charger2").val();
        var oldProcessor = $("#processor").val();
        if(!processor){
            layer.msg("请选择新的处理者！");
            return false;
        }
        if(processor == oldProcessor){
            layer.msg("新的处理者与原处理者不能为同一人");
            return false;
        }
        $.ajax({
            "url":"../complaint/changeComplaintHandler.do",
            "data":{ "id": baseId , "processor": processor },
            success:function (res) {
                location.href = location.href ;
            }
        })
    }else{
        var data = {
            "userID": sphdSocket.user.userID ,
            "processId": processId ,
            "baseId": baseId,
            "state": state
        } ;
        if(state == 2){
            data["registerRejectReason"] = $("#reason").val();
        }else{
            data["processer"] = $("#charger").val();
            if(!data["processer"]){
                layer.msg("请指定本投诉的处理者！");
                return false ;
            }
        }
        console.log('审批传值：' ,data)
        loading.open();
        sphdSocket.send('approvePutOnRecordComplaint',data);
    }

}

// creator hxz 2019-06-06  增加补充材料
function addMore() {
    bounce.show($("#addMore"));
    $("#title2").val("");
    $('#imgUploadBtn').html("");
    $('#fileUploadBtn').html("");
    initUploadify($('#imgUploadBtn'), "img", 1);
    initUploadify($('#fileUploadBtn'), "file", 2);
}
// create : hxz 2019-06-18 确定补充材料
function addMoreOk(type) {
    var list = [];
    var gouparr1 = $('#imgUploadBtn').data("gouparr") || [];
    var gouparr2 = $('#fileUploadBtn').data("gouparr") || [];
    list.push(...gouparr1, ...gouparr2);
    if(type === 'cancel'){
        bounce.cancel();
        list.forEach(function (item) {
            fileDelAjax(item)
        })
        return false;
    }
    var title = $("#title2").val();
    var listAttachment = [] ;
    $("#addMore").find(".upItem").each(function(){
        var path = $(this).attr("path") ;
        var title = $(this).attr("title") ;
        var type = $(this).attr("type") ; // 类型:1-照片,2-其他
        listAttachment.push({ "path": path, "title":title, "type":type })
    })
    if(title == "" && listAttachment.length ==0){
        layer.msg("您没有填写任何内容，无法提交！");
        return false;
    }

    // 取消删除文件
    list.forEach(function (item) {
        cancelFileDel(item)
    })
    $.ajax({
        "url":"../complaint/saveSupplement.do",
        "data":{ "module":'投诉' ,  "userID":sphdSocket.user.userID , "complaint": baseId, "title": title , "listAttachment":JSON.stringify(listAttachment) } ,
        success:function (res) {
            // 处理在订阅的情况里面，此处不作处理
        }
    })
}
// creator: hxz 2019-06-18 查看补充材料
function seeMore(thisObj, type) {
    var info = JSON.parse(thisObj.next().html());
    bounce.show($("#seeMore"));
    // "<td>" + listSupplement[w]["createName"]+ " " + (new Date().format("yyyy/MM/dd hh:mm:ss")) + "</td>" +
    $("#seeMoreTip").html(info["createName"] + " " + (new Date(info["createTime"]).format("yyyy/MM/dd hh:mm:ss")) + " 录入的补充材料");
    $("#title3").html(info["title"]);
    $('#pic2').html("");
    $('#doc2').html("");
    var files = info["complaintAttachmentHashSet"] || info["listAttachment"]  ;
    // if(type == 1){
    //     files = JSON.parse( info["listAttachment"] ) ;
    // }
    if(files && files.length>0){
        for(var i = 0 ; i < files.length ; i++){
            var type_f = files[i]["type"] ;
            var title_f = files[i]["title"] ;
            var path_f = files[i]["path"] ;
            var fileType = title_f.split(".").pop() ;
            var fileName = title_f.replace("."+ fileType,"") ;
            var itemTemp =
                '<div title="'+ title_f +'" path="'+ path_f +'" type="'+ type_f +'" class="upItem uploadify-queue-item">' +
                '<div class="fileScanMark">' +
                '<a class="fileScan">查看</a>'+
                '<a class="fileDown">下载</a>'+
                '</div>'+
                '<span class="up_fileType">'+ chargeFileType(fileType) +'</span>' +
                '<span class="up_name">'+ fileName +'</span>' +
                '</div>';
            if(type_f == 1){ // 上传文件类型:1-照片,2-其他
                $('#pic2').append(itemTemp) ;
            }else{
                $('#doc2').append(itemTemp) ;
            }
            $(".up_fileType").show();
        }
    }

}
// creator hxz 2019-06-18  更换处理者记录
function changeHandlerLog() {
    bounce.show($("#changeHandlerLog"));
    $("#changeHandlerLogTab tbody").children(":gt(0)").remove();
    $.ajax({
        "url":"../complaint/getChangeComplaintHandler.do",
        "data":{ "baseId": baseId },
        success:function (res) {
            var list = res["data"]["lsitProcessor"] , str = "" ;
            for(var i = 0 ; i < list.length ; i++){
                var ttl = i == 0 ? "原始信息" : "第"+ i + "修改后" ;
                str +=
                    "<tr>" +
                        "<td style='color:#E46D0A;'>"+ ttl +"</td>" +
                        "<td>"+ list[i]["userName"] +"</td>" +
                        "<td>"+ (list[i]["postName"] || "") +"</td>" +
                        "<td>"+ list[i]["mobile"] +"</td>" +
                        "<td>"+ (new Date(list[i]["changeTime"]).format("yyyy/MM/dd hh:mm:ss")) +"</td>" +
                    "</tr>";
            }
            $("#changeHandlerLogTab").append(str);
        }
    })
}

//creator:hxz date:2019/05/24 上传文件通用方法
var uploadCode = "" // 标记每次添加的代号
var uploadType = "" // 上传文件类型:1-照片,2-其他
function initUploadify(docObj, type, number) {
    var itemTemp =
        '<div id="${fileID}" title="${fileName}" size="${size}" code="${uploadCode}" class="upItem uploadify-queue-item">' +
        '<div class="fileScanMark">' +
        '<a class="fileScan">查看</a>'+
        '<a class="fileDown">下载</a>'+
        '</div>'+
        '<i class="hd up_delBtn fa fa-close" onclick="deleteFile($(this))"></i>' +
        '<span class="hd up_fileType">${fileType}</span>' +
        '<span class="hd delInfo"></span>' +
        '<span class="up_name">${fileName}</span>' +
        '<span class="uploadify-progress"><div class="uploadify-progress-bar"></div></span>' +
        '</div>';
    var fileTypeExt = '*.gif,*.png;*.jpg;*.jpeg;';
    if(type == 'file'){
        fileTypeExt = '.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.wps;*.et;*.md;';
    }
    docObj.Huploadify({
        auto: true,
        fileTypeExts: fileTypeExt,
        multi: true,
        buttonText: "添加",
        buttonNumber: number,
        formData:{
            module: '投诉',
            userId: sphdSocket.user.userID,
            groupUuid: sphdSocket.uuid()
        },
        fileSizeLimit: (100*1024),  // 100M = ( 100 * 1024 ) KB
        showUploadedPercent: true,
        showUploadedSize: true,
        removeTimeout: 99999999,
        itemTemplate:itemTemp ,
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onUploadStart: function () {
            $("#confirmReport").attr("class", "ty-btn ty-btn-big ty-btn-gray ty-circle-3").removeAttr("onclick");
        },
        onUploadComplete: function (file, data) {
            $("#confirmReport").attr("class", "ty-btn ty-btn-big ty-circle-3 ty-btn-green").attr("onclick", "confirmReport()");
        },
        onUploadError: function (file) {
            layer.msg('文件上传出错！');
            console.log('出错文件：', file)
        },
        onUploadSuccess: function (file, data) {
            data = JSON.parse(data);
            var filePath = data.filename;
            var fileName = file.name;
            var size = file.size;
            var fileUid =data.fileUid;
            var groupUuid =data.groupUuid;
            if(groupUuid){
                let gouparr = docObj.data("gouparr") || []
                gouparr.push({"type":'groupUuid', 'groupUuid':groupUuid})
                docObj.data("gouparr", gouparr);
            }
            $(".upItem").each(function(){
                var txt = $(this).attr("title");
                var s = $(this).attr("size");
                var co = $(this).attr("code");
                if(fileName == txt && size == s && uploadCode == co){ // 名称/大小/批次 一样就默认是一个,即：每次不能穿两个名字、类型、大小完全相同的文件
                    $(this).children("i.up_delBtn").show();
                    $(this).children(".up_fileType").show();
                    $(this).children(".uploadify-progress").hide();
                    var type = $(this).children(".up_fileType").html();
                    $(this).children(".up_fileType").html(chargeFileType(type));
                    $(this).children(".up_name").html(fileName.replace("."+type , ""));
                    $(this).attr("path", filePath) ;
                    $(this).attr("type", uploadType) ;
                    $(this).find(".delInfo").html(JSON.stringify({"type":'fileId', 'fileId':fileUid}))
                }
            });
        }
    });
}
// create : hxz 2019-06-18 结案、结案申请
function endComplaint(num) {
    bounce.show($("#endComplaint"));
    $("#settleType").val("") ;
    $("#settleOpinion").val("") ;
    if(sendType == 3){ // 核心人物 传1
        $("#endComplaintTtl").html("结案");
    }else{
        $("#endComplaintTtl").html("结案申请");
    }
}
function endComplaintOk() {
    var ishe = 0 ; // 默认不是核心人物
    if(sendType == 3){ // 核心人物 传1
        ishe = 1 ;
    }
    var settleType = $("#settleType").val();
    if(settleType == ""){
        layer.msg("请选择 本投诉的类型");
        return false;
    }
    // <%--结案类型:1-真实投诉，现已处理完毕并获客户确认,2-不属我公司问题，且已得到客户确认,3-其他原因，不该算作投诉,4-与其他投诉重复--%>
    var data = {
        'userID':sphdSocket.user.userID,
        'processId':processId,
        'baseId':baseId,
        'settleType':settleType,
        'settleOpinion':$("#settleOpinion").val(),
        'role':ishe
    }
    console.log('结案传值：', data)
    loading.open();
    sphdSocket.send('applyFinishComplaint', data);
}
// create : hxz 2019-06-18 选择结案类型
function selectThis(thisObj , type) {
    $("#settleType").val(type);
    $("#endComplaint .fa").attr("class", "fa fa-circle-o");
    thisObj.children(".fa").attr("class", "fa fa-dot-circle-o")
}
// create : hxz 2019-06-20 核心人物审批结案
function chargeEndComplaint( type) { // 6-批准 5-驳回
    $("#endState").val(type);
    $("#state" + type).show().siblings().hide();
    bounce.show($("#chargeEndComplaint"));
    $("#chargeEndComplaint .state" + type).show().siblings().hide();
}
function chargeEndComplaintOk( ) { // 6-批准 5-驳回
    var type = $("#endState").val();
    var settleRejectReason = $("#endReason").val();
    bounce.cancel();
    var data = {
        'userID':sphdSocket.user.userID,
        'processId':processId,
        'baseId':baseId,
        'state':type,
        'settleRejectReason':settleRejectReason
    };
    loading.open();
    console.log("结案审批传值：", data) ;
    sphdSocket.send('approveComploaintEnd', data);
}
function cancelIframe() {
    window.parent.hideDetailsPage();
}


