// creator: 张旭博，2018-06-06 11:02:46，获取投诉详情
function getComplaintDetail(selector,complaintId,state,coreCode) {
    /*
     * selector : 选择器
     * complaintId : 投诉id
     * state : 审批状态
     * coreCode : 1-核心人物 else 其他人物
     */
    $.ajax({
        url:"../complaint/getComplaintDetail.do" ,
        data:{  "id":complaintId , "coreCode":coreCode  },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var complaintDetail = data["complaintDetail"];
            //查看页面通用信息赋值(包括附件)
            var customerId      = complaintDetail.customer;
            var creator         = complaintDetail.creator;
            var customName      = complaintDetail.customName;
            var customCode      = complaintDetail.customCode;
            var contact         = complaintDetail.contact;
            var receiptTime     = complaintDetail.receiptTime.substring(0,10);
            var contactPhone    = complaintDetail.contactPhone;
            var processor       = complaintDetail.processor;
            var processorName   = complaintDetail.processorName;
            var content         = complaintDetail.content;
            var memo            = complaintDetail.memo;
            var attachmentSet   = complaintDetail.attachmentSet;
            $(selector+" .cp_scan_box .cp_customerName").html(customName);
            $(selector+" .cp_scan_box .cp_customerName").attr("id",customerId);
            $(selector+" .cp_scan_box .cp_customerName").attr("cid",creator);
            $(selector+" .cp_scan_box .cp_customerCode").html(customCode);
            $(selector+" .cp_scan_box .cp_customerContact").html(contact);
            $(selector+" .cp_scan_box .cp_acceptDate").html(receiptTime);
            $(selector+" .cp_scan_box .cp_mobile").html(contactPhone);
            $(selector+" .cp_scan_box .cp_content").html(content);
            $(selector+" .cp_scan_box .cp_memo").html(memo);

            if($(selector+" .cp_scan_box .cp_handlerName").hasClass("cp_scan")){
                $(selector+" .cp_scan_box .cp_handlerName").html(processorName);
                $(selector+" .cp_scan_box .cp_handlerName").data("processor",processor);
            }
            //附件部分
            var fileInfoStr = '';
            var imageStr = getEnclosure(1,1,attachmentSet);
            var fileStr  = getEnclosure(1,2 ,attachmentSet);
            if(imageStr !== ''){
                fileInfoStr +=  '<div class="formItem">' +
                                '    <div class="formTitle"><span>照片</span></div>' +
                                '    <div class="formCon">' +
                                '        <div class="formConBg">' +
                                '            <div class="cp_imgShow clearfix">'+imageStr+'</div>' +
                                '        </div>' +
                                '    </div>' +
                                '</div>';
            }
            if(fileStr !== ''){
                fileInfoStr +=  '<div class="formItem">' +
                                '    <div class="formTitle"><span>附件</span></div>' +
                                '    <div class="formCon">' +
                                '        <div class="formConBg">' +
                                '            <div class="cp_fileShow clearfix">'+fileStr+'</div>' +
                                '        </div>' +
                                '    </div>' +
                                '</div>';
            }
            $(selector+" .cp_fileInfo").html(fileInfoStr);

            //补充材料部分
            var supplementSet    = complaintDetail.supplementSet;
            var annexStr = '<p class="ty-panelHead">补充材料</p>';//补充材料 拼接字符串
            if(supplementSet.length>0) {
                for (var i = 0; i < supplementSet.length; i++) {
                    var enclosureArr = supplementSet[i].complaintAttachmentHashSet;
                    var S_imageStr   = getEnclosure(2, 1, enclosureArr);
                    var S_fileStr    = getEnclosure(2, 2, enclosureArr);

                    annexStr    +=  '<div class="formItem">' +
                                    '    <div class="formTitle"><span>补充材料</span></div>' +
                                    '    <div class="formCon"><div class="cp_scan cp_annex" style="width: 780px">' + supplementSet[i].description + '</div></div>' +
                                    '</div>';
                    if(S_imageStr !== "") {
                        annexStr += '<div class="formItem">' +
                                    '    <div class="formTitle"><span>照片</span></div>' +
                                    '    <div class="formCon">' +
                                    '        <div class="formConBg">' +
                                    '            <div class="cp_imgShow clearfix">' + getEnclosure(2, 1, enclosureArr) + '</div>' +
                                    '        </div>' +
                                    '    </div>' +
                                    '</div>';
                    }
                    if(S_fileStr !== ""){
                        annexStr += '<div class="formItem">' +
                                    '    <div class="formTitle"><span>附件</span></div>' +
                                    '    <div class="formCon">' +
                                    '        <div class="formConBg">' +
                                    '            <div class="cp_fileShow clearfix">' + getEnclosure(2, 2, enclosureArr) + '</div>' +
                                    '        </div>' +
                                    '    </div>' +
                                    '</div>';
                    }
                }
            }else{
                annexStr = '';
            }
            $(selector+" .cp_scan_annex").html(annexStr);

            //立案驳回部分
            if(state === 1){
                var rejectStr = '<p class="ty-panelHead">立案驳回信息</p>'+
                    '<div class="formItem">' +
                    '    <div class="formTitle"><span>立案驳回时间</span></div>' +
                    '    <div class="formCon"><div class="cp_scan cp_caseType" style="width: 780px">'+complaintDetail.registerApproveTime+'</div></div>' +
                    '</div>' +
                    '<div class="formItem">' +
                    '    <div class="formTitle"><span>立案驳回理由</span></div>' +
                    '    <div class="formCon"><div class="cp_scan cp_caseDescription" style="width: 780px">'+complaintDetail.registerRejectReason +'</div></div>' +
                    '</div>';
                $(selector+" .cp_registerReject").html(rejectStr);
            }else {
                $(selector+" .cp_registerReject").html("");
            }

            //结案信息部分
            var approvalProcessList    = complaintDetail.approvalProcessList;
            var caseInfoStr = '<p class="ty-panelHead">结案记录</p>';//结案信息 拼接字符串
            var caseLength = approvalProcessList.length;
            if(caseLength>0){
                for(var j=0;j<caseLength;j++){
                    if(approvalProcessList[0].approveStatus === "4"){
                        if(j === 0){
                            caseInfoStr +=  '<div class="formItem">' +
                                '    <div class="formTitle"><span>投诉类型</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseType" style="width: 780px">'+chargeType(approvalProcessList[j].type)+'</div></div>' +
                                '</div>' +
                                '<div class="formItem">' +
                                '    <div class="formTitle"><span>结案意见</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseDescription" style="width: 780px">'+approvalProcessList[j].description +'</div></div>' +
                                '</div>' +
                                '<div class="formItem">' +
                                '    <div class="formTitle"><span>结案提交者</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseCreator">'+approvalProcessList[j].userName +'</div></div>' +
                                '    <div class="formTitle"><span>结案提交时间</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseCreateTime">'+approvalProcessList[j].createDate+'</div></div>'+
                                '</div>';
                        }else if(j % 2 === 1){
                            caseInfoStr +=  '<div class="formItem">' +
                                '    <div class="formTitle"><span>投诉类型</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseType" style="width: 780px">'+chargeType(approvalProcessList[j+1].type)+'</div></div>' +
                                '</div>' +
                                '<div class="formItem">' +
                                '    <div class="formTitle"><span>结案意见</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseDescription" style="width: 780px">'+approvalProcessList[j+1].description +'</div></div>' +
                                '</div>' +
                                '<div class="formItem">' +
                                '    <div class="formTitle"><span>结案提交者</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseCreator">'+approvalProcessList[j+1].userName +'</div></div>' +
                                '    <div class="formTitle"><span>结案提交时间</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseCreateTime">'+approvalProcessList[j+1].createDate+'</div></div>'+
                                '    <div class="formTitle"><span>结案驳回时间</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseApproveTime">'+approvalProcessList[j].handleTime+'</div></div>' +
                                '</div>';
                        }
                    }else{
                        if(j===0 && approvalProcessList[0].approveStatus === "6"){
                            caseInfoStr +=  '<div class="formItem">' +
                                '    <div class="formTitle"><span>投诉类型</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseType" style="width: 780px">'+chargeType(approvalProcessList[j].type)+'</div></div>' +
                                '</div>' +
                                '<div class="formItem">' +
                                '    <div class="formTitle"><span>结案意见</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseDescription" style="width: 780px">'+approvalProcessList[j].description +'</div></div>' +
                                '</div>' +
                                '<div class="formItem">' +
                                '    <div class="formTitle"><span>结案提交者</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseCreator">'+approvalProcessList[j].userName +'</div></div>' +
                                '    <div class="formTitle"><span>结案提交时间</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseCreateTime">'+approvalProcessList[j].createDate+'</div></div>'+
                                '    <div class="formTitle"><span>结案批准时间</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseApproveTime">'+approvalProcessList[j].handleTime+'</div></div>' +
                                '</div>';
                        }else if(j % 2 === 0){
                            caseInfoStr +=  '<div class="formItem">' +
                                '    <div class="formTitle"><span>投诉类型</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseType" style="width: 780px">'+chargeType(approvalProcessList[j+1].type)+'</div></div>' +
                                '</div>' +
                                '<div class="formItem">' +
                                '    <div class="formTitle"><span>结案意见</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseDescription" style="width: 780px">'+approvalProcessList[j+1].description +'</div></div>' +
                                '</div>' +
                                '<div class="formItem">' +
                                '    <div class="formTitle"><span>结案提交者</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseCreator">'+approvalProcessList[j+1].userName +'</div></div>' +
                                '    <div class="formTitle"><span>结案提交时间</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseCreateTime">'+approvalProcessList[j+1].createDate+'</div></div>'+
                                '    <div class="formTitle"><span>结案驳回时间</span></div>' +
                                '    <div class="formCon"><div class="cp_scan cp_caseApproveTime">'+approvalProcessList[j].handleTime+'</div></div>' +
                                '</div>';
                        }
                    }


                }
            }else{
                caseInfoStr = '';
            }
            $(selector+" .cp_caseInfo").html(caseInfoStr);
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

// creator: 张旭博，2018-06-06 11:02:32，获取展示时的附件的拼接字符串
function getEnclosure(type,annexType,enclosureArr) {
    //type 1-投诉附件 2-补充材料附件
    //annexType 1-图片 2-附件
    //因为返回值图片和附加在一块所以要这样区分（~_~)
    var complaintImgStr = '';
    var annexImgStr = '';
    var complaintFileStr = '';
    var annexFileStr = '';
    for(var i=0;i<enclosureArr.length;i++){
        // 新增投诉时的图片
        var path =  $.fileUrl + "/upload/"+enclosureArr[i].path;
        if(enclosureArr[i].type === "1"){
            var arr = enclosureArr[i].path.split(".");
            var fileType = arr[arr.length-1];
            var imgStr =    '<div class="cp_img_box">' +
                            '   <div class="fileType" style="background-image: url('+path+')">' +
                            '   <a path="'+ enclosureArr[i].path +'" onclick="seeOnline($(this))">预览</a>' +
                            '   <a path="'+ enclosureArr[i].path +'" onclick="return getDownLoad($(this));" download="'+ enclosureArr[i].title +'">下载</a>'+
                            '   </div>'+
                            '   <div class="cp_img_name">'+enclosureArr[i].title+'</div>' +
                            '</div>';
            if(enclosureArr[i].supplement === 0){
                complaintImgStr +=  imgStr;
            }else{
                annexImgStr += imgStr;
            }
        }else{
            var arr = enclosureArr[i].path.split(".");
            var fileType = arr[arr.length-1];
            var imageStr = '';
            if(fileType === 'jpg' || fileType === 'png' || fileType === 'jpeg' || fileType === 'gif'){
                imageStr = 'style="background-image: url('+path+')"';
            }else{
                imageStr = ''
            }
            var fileStr =   '<div class="cp_img_box">' +
                            '   <div class="fileType ty-file_'+fileType+'" '+imageStr+'>' +
                            '   <a path="'+ enclosureArr[i].path +'" onclick="seeOnline($(this))">预览</a>' +
                            '   <a path="'+ enclosureArr[i].path +'" onclick="return getDownLoad($(this));" download="'+ enclosureArr[i].title +'">下载</a>'+
                            '   </div>' +
                            '   <div class="cp_img_name">'+enclosureArr[i].title+'</div>' +
                            '</div>';
            if(enclosureArr[i].supplement === 0){
                complaintFileStr += fileStr;
            }else{
                annexFileStr += fileStr;
            }
        }
    }
    return type === 1?(annexType===1?complaintImgStr:complaintFileStr):(annexType===1?annexImgStr:annexFileStr);
}

// creator: 张旭博，2018-06-06 10:51:15，获取新增时的附件拼接字符串
function getNewEnclosure(file,data) {
    //file 文件上传返回的参数  data 接口返回的参数
    data = JSON.parse(data);
    var path =  $.fileUrl + data.data.filename,  //路径（包含文件类型）
        name = file.name,           //文件名称
        fileStr = '';

    //取到文件类型
    var arr = path.split(".");
    var fileType = arr[arr.length-1];
    var fileUid =data.fileUid;

    //图片类型展示图片缩略图，文件类型展示文件类型图标
    if (fileType === 'jpg' || fileType === 'png' || fileType === 'jpeg' || fileType === 'gif') {
        fileStr = '<div class="cp_img_box" datasrc="' + path + '">' +
            '   <i class="fa fa-times-circle" onclick="deleteImg($(this))"></i>' +
            '   <div class="hd delInfo">'+ JSON.stringify({"type":'fileId', 'fileId':fileUid}) +'</div>' +
            '    <img src="' + path + '" alt="' + name + '" class="cp_img_path">' +
            '    <div class="cp_img_name">' + name + '</div>' +
            '</div>';
    } else {
        fileStr = '<div class="cp_img_box" datasrc="' + path + '">' +
            '   <i class="fa fa-times-circle" onclick="deleteImg($(this))"></i>' +
            '   <div class="fileType ty-file_' + fileType + '"></div>' +
            '   <div class="hd delInfo">'+ JSON.stringify({"type":'fileId', 'fileId':fileUid}) +'</div>' +
            '   <div class="cp_img_name">' + name + '</div>' +
            '</div>';
    }
    return fileStr;
}

// creator: 张旭博，2018-06-06 11:02:23，转换结案类型
function chargeType(type) {
    switch (type){
        case 1:
            return '与其他投诉重复';
            break;
        case 2:
            return '客户误解，已与客户沟通并获得确认';
            break;
        case 3:
            return '其他原因，不该计入投诉';
            break;
        case 4:
            return '真实投诉，现已处理完毕并获客户确认';
            break;
        default:
            return '错误的返回值,请重试';
    }
}

function chargeNull(str){
    return str === "" || str === null?"--":str;
}

