$(function () {
    // 三种状态切换（待处理、已批准、已驳回）
    $(".mainPage .ty-secondTab li").on("click", function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        //获取点击按钮的下标
        var index = $(this).index();
        // getComplaintByStatus(index+1);
        $(".mainPage .tblContainer").eq(index).show().siblings(".tblContainer").hide();
        getComplaintByStatus(index+1);
    });
    $(".divisionPage .ty-secondTab li").on("click", function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        //获取点击按钮的下标
        var index = $(this).index();
        $(".divisionPage .tblContainer").eq(index).show().siblings(".tblContainer").hide();
        getDivisionList();
    });
    $(".departTree").on("click" , "li>div" , function () {
        $(this).next().toggle();
    });
    $("#cp_customerName").on("change",function () {
        var code = $(this).find("option:selected").attr("code");
        if(code === "null"){
            $("#cp_customerCode").val("--");
        }else{
            $("#cp_customerCode").val(code);
        }
    });
    // 单选按钮事件
    $(".ty-radio").on("click",function () {
        $(this).addClass("ty-radioActive").siblings().removeClass("ty-radioActive");
        $(this).siblings().children("i").attr("class","fa fa-circle-o")
        $(this).children("i").attr("class","fa fa-dot-circle-o")
        // $(this).siblings(".hd").val($(this).attr("value"));
    });
    //本年、去年、前年切换状态
    $("#changeState .ty-btn").on("click",function(){
        //样式切换
        $(this).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
        $("#loginQueryBtn").removeClass("ty-btn-blue");

        //清空自定义选择的时间
        $("#queryBeginTime").val("");
        $("#queryEndTime").val("");

        //获取对应数据
        var index = $(this).index();
        getPassedCase(1,20,index+1) ;
    });
    $(".mainPage .ty-secondTab li").eq(0).click();
});

//-----------------投诉流程-------------------//

/* creator：张旭博，2018-01-17 15:31:44，获取不同状态的投诉列表 */
function getComplaintByStatus(state) {
    $.ajax({
        url:"../complaint/getComplaintList.do",
        data:{
            "state":state,  //状态，1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
            "role":4        //1-核心人物 2-立案者 3-处理者 4-查看者
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ) {
            var status = data["status"];
            var listData = data["complaintList"];

            var listStr = '';
            if (listData.length > 0) {
                switch (state){
                    case 1:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +                     //客户
                                '<td>' + listData[i].customCode + '</td>' +                     //客户代号
                                '<td>' + listData[i].contact + '</td>' +                        //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +                   //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +    //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +                     //录入者
                                '<td>' + listData[i].createTime + '</td>' +    //录入时间
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 2:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +                     //客户
                                '<td>' + listData[i].customCode + '</td>' +                     //客户代号
                                '<td>' + listData[i].contact + '</td>' +                        //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +                   //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +    //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +                     //录入者
                                '<td>' + listData[i].createTime + '</td>' +    //录入时间
                                '<td>' + listData[i].registerApproveTime + '</td>' +    //审批时间
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 3:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +                     //客户
                                '<td>' + listData[i].customCode + '</td>' +                     //客户代号
                                '<td>' + listData[i].contact + '</td>' +                        //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +                   //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +    //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +                     //录入者
                                '<td>' + listData[i].createTime + '</td>' +    //录入时间
                                '<td>' + listData[i].processorName + '</td>' +                  //投诉处理人
                                '<td>' + listData[i].registerApproveTime + '</td>' +    //审批时间
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 4:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +                     //客户
                                '<td>' + listData[i].customCode + '</td>' +                     //客户代号
                                '<td>' + listData[i].contact + '</td>' +                        //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +                   //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +    //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +                     //录入者
                                '<td>' + listData[i].createTime + '</td>' +    //录入时间
                                '<td>' + listData[i].processorName + '</td>' +                  //投诉处理人
                                '<td>' + listData[i].settleOpinion + '</td>' +                  //结案说明
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 5:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].customName + '</td>' +                     //客户
                                '<td>' + listData[i].customCode + '</td>' +                     //客户代号
                                '<td>' + listData[i].contact + '</td>' +                        //客户联系人
                                '<td>' + listData[i].contactPhone + '</td>' +                   //联系方式
                                '<td>' + listData[i].receiptTime.substring(0,10) + '</td>' +    //接到投诉的日期
                                '<td>' + listData[i].createName + '</td>' +                     //录入者
                                '<td>' + listData[i].createTime + '</td>' +    //录入时间
                                '<td>' + listData[i].processorName + '</td>' +                  //投诉处理人
                                '<td>' + listData[i].settleApproveTime + '</td>' +      //审批时间
                                '<td>' + listData[i].settleOpinion + '</td>' +                  //结案说明
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 6:
                        getPassedCase(1,20,1);
                        break;
                }
                $(".mainPage .tblContainer").eq(state - 1).find("tbody").html(listStr);
            }else{
                $(".mainPage .tblContainer").eq(state - 1).find("tbody").html("");
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-18 15:26:31，投诉查看 - 按钮 */
function seeComplaintBtn(selector){
    bounce.show($("#seeComplaint"));
    $("#cp_processor").show();
    var state = $(".mainPage .ty-secondTab li").index($(".mainPage .ty-secondTab li.ty-active"));
    if(state === 0 || state === 1){
        $("#cp_processor").hide();
    }
    var complaintId = selector.parent().parent().attr("id");
    $("#seeComplaint .bonceCon").attr("id",complaintId);
    getComplaintDetail("#seeComplaint",complaintId);
}


//-----------------结案通过（数据筛选）-------------------//

/* creator：张旭博，2018-02-01 14:18:36，获得结案通过的投诉 */
function getPassedCase(curr,totalPage,buttonType) {
    var data = {
        "buttenType":buttonType,//1代表获取本年的结案的case（即刚点进来时就要加载的方法，传2代表获取去年结案的case，传3代表获取前年结案的case，传4代表按某个特定的时间段获取结案的case
        "role":4,//身份信息 1是核心人物 2是立案者 3是处理者 4是查看者
        "currentPageNo":curr,
        "pageSize":totalPage
    };
    if(buttonType === 4){
        var startTime = $("#queryBeginTime").val()+" 00:00:00";
        var endTime = $("#queryEndTime").val()+" 23:59:59";
        data["startTime"] = startTime;//开始时间
        data["endTime"] = endTime;//结束时间
    }

    $.ajax({
        url:"../complaint/getPassedCase.do",
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var mainData = data["data"],
                pageInfo = data["page"];

            var complaintList   = mainData.complaintList,
                startDay        = formatTime(mainData.startDay,false),
                endDay          = formatTime(mainData.endDay,false),
                totalResult     = pageInfo.totalResult;

            var tipStr          = '<div class="ty-alert ty-alert-warning">自'+startDay+'~'+endDay+'，已结案的投诉共 <span class="ty-color-blue complaintResult">'+totalResult+'</span> 件</div>',
                complaintListStr= '';

            if(complaintList) {
                for (var i = 0; i < complaintList.length; i++) {
                    complaintListStr += '<tr id="' + complaintList[i].id + '">' +
                        '<td>' + complaintList[i].customName + '</td>' +       //客户
                        '<td>' + complaintList[i].customCode + '</td>' +       //客户代号
                        '<td>' + complaintList[i].contact + '</td>' +       //客户联系人
                        '<td>' + complaintList[i].contactPhone + '</td>' +       //联系方式
                        '<td>' + formatTime(complaintList[i].receiptTime) + '</td>' +       //接到投诉的日期
                        '<td>' + complaintList[i].createName + '</td>' +       //录入者
                        '<td>' + formatTime(complaintList[i].createTime) + '</td>' +       //录入时间
                        '<td>' + complaintList[i].processorName + '</td>' +       //投诉处理人
                        '<td>' + formatTime(complaintList[i].settleApproveTime) + '</td>' +       //审批时间
                        '<td>' + complaintList[i].settleOpinion + '</td>' +       //结案说明
                        '<td>' +
                        '<span class="ty-color-blue" onclick="seeComplaintBtn($(this))">查看</span>' +
                        '</td>' +
                        '</tr>';
                }
                $(".mainPage .passedPage .tplContainer").eq(0).find(".tip").html(tipStr);
                $(".mainPage .passedPage .tplContainer").eq(0).find("tbody").html(complaintListStr);
            }else{
                $(".mainPage .tblContainer").eq(5).find("tbody").html("");
            }

        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        },
        complete:function(){ loading.close();}
    }) ;
}

//-----------------辅助方法-------------------//

/* creator：张旭博，2018-01-18 09:59:33，返回综合管理 */
function goBack(state) {
    if(state === 'division'){
        $(".mainPage").show();
        $(".divisionPage").hide();
    }else if(state === "passDetail"){
        $(".passedPage").show();
        $(".passedDetailPage").hide();
    }

}

laydate.render({elem: '#cp_acceptDate'});
laydate.render({elem: '#queryBeginTime'});
laydate.render({elem: '#queryEndTime'});