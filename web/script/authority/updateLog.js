$(function(){
    /*  creater  姚宗涛  2018/3/14  14:38:00    页面切换显示    */
    $(".ty-secondTab li").click(function(){
        $(this).addClass("ty-active").siblings().removeClass("ty-active") ;
        $(".ty-mainData table").eq($(this).index()).show().siblings().hide() ;
        logType = $(this).index()+1 ;
        getList(1 , 20  ) ;
    }) ;
    // 初始化获取列表
    getList(1 , 20  ) ;
}) ;

/* creator: 侯杏哲 2018-04-03 获取列表 */
var logType = 1; // 1-待处理，2-已批准，3-已驳回
function getList(cur , pageNum ){
    var pageInfo = {  } ;
    $.ajax({
        "url" : "../approval/editRecord.do" ,
        "data" : { "approvalStatus" :logType , "pageSize": pageNum , "currentPageNo" : cur  },
        beforeSend:function () { loading.open() ;  } ,
        success:function (res) {
            var list = res["approvalProcess"] ;
            var pageInfo = res["pageInfo"] ;
            var totalPage = pageInfo["totalPage"] ;
            setPage($("#ye") , cur , totalPage ,"updateLog" , "{}") ;
            var str = "" ;
            if(list && list.length > 0){
                for(var i = 0 ; i < list.length ; i++) {
                    var description = list[i]["description"]
                    if(description != "加班审批流程的修改申请"){
                        if(logType === 1){
                            str += "<tr>" +
                                "<td>"+ ( new Date(list[i]["createDate"]).format('yyyy-MM-dd')) +"</td>" +
                                "<td>"+ list[i]["askName"] +"</td>" +
                                "<td>"+ list[i]["description"] +"</td>" +
                                "<td>" +
                                "<span class='ty-color-blue' onclick=\"see("+ list[i]["id"] +" , $(this) , \'"+ list[i]["description"] +"\')\">查看</span>" +
                                "</td>" +
                                "</tr>" ;
                        }else{
                            str += "<tr>" +
                                "<td>"+ ( new Date(list[i]["createDate"]).format('yyyy-MM-dd'))  +"</td>" +
                                "<td>"+ list[i]["askName"] +"</td>" +
                                "<td>"+ list[i]["description"] +"</td>" +
                                "<td>"+ list[i]["toUserName"] +"</td>" +
                                "<td>"+ ( new Date(list[i]["handleTime"]).format('yyyy-MM-dd')) +"</td>" +
                                "<td>" +
                                "<span class='ty-color-blue' onclick=\"see("+  list[i]["id"]  +" , $(this) , \'"+ list[i]["description"] +"\')\">查看</span>" +
                                "</td>" +
                                "</tr>" ;
                        }
                    }

                }
            }
            if(logType === 1){
                $("#tbl1").html(str).parent().show().siblings("table").hide() ;
            }else{
                $("#tbl2").html(str).parent().show().siblings("table").hide() ;
            }
            $("#ye").show() ;
        } ,
        error:function () { } ,
        complete:function () {
            loading.close();
        }
    })
}
/*  updator  侯杏哲  2018/4/10      查看按钮   */
var editObj = {}  ;
function see(id ,_this , type ){
    editObj["obj_this"] = _this ;
    editObj["id"] = id ;
    editObj["type"] = _this.parent().siblings(":eq(2)").html() ;
    if( logType == 1 ){ //  待处理
        $("#look .bonceFoot").children(":eq(0)").show() ;
        $("#look .bonceFoot").children(":eq(1)").show() ;
        $("#look .bonceFoot").children(":eq(2)").hide() ;
    }else {   //  已批准、已驳回
        $("#look .bonceFoot").children(":eq(0)").hide() ;
        $("#look .bonceFoot").children(":eq(1)").hide() ;
        $("#look .bonceFoot").children(":eq(2)").show() ;
    }
    bounce.show($("#look")) ;
    $.ajax({
       "url" : "../approval/editInfo.do" ,
       "data":{ "id" : id  } ,
       "dataType" :"json" ,
       "type" :"post" ,
        beforeSend:function () { loading.open() ; } ,
        success:function (res) {
            var status  = res["status"] ;
            if(status != 1){
                bounce.show( $("#tip") ); $("#tip_ms").html("获取详情失败") ; return ;
            }
            var oldInfo = res["oldApprovalItem"] ;
            var newInfo = res["newApprovalItem"] ;
            var str_old = "" , str_new = "" ;
            if(oldInfo){
                $("#description").html(oldInfo["name"]) ;
                var lev = oldInfo["level"] ;
                if(lev == 0){
                    str_old = "<p>无需审批</p>" ;
                }else{
                    var levelList = oldInfo["approvalFlowHistories"] ;
                    str_old = "<p>"+ returnUpper(lev) +"级审批</p>" ;
                    for(var i = 0 ; i < lev; i++ ){ // 最高级审批者为最终审批者
                        var i_lev = levelList[i]["level"] ;
                        if( i_lev == lev ){
                            str_old += "<p>最终审批者："+ levelList[i]["toUser"]  +" / " +levelList[i]["userName"] +"</p>" ;
                        }else{
                            switch (oldInfo["name"]){
                                case "审批设置修改" :
                                    str_old += "<p>"+ returnUpper(levelList[i]["level"]) +"级审批者："+ levelList[i]["toUser"]  +" / " +levelList[i]["userName"] +"</p>" ;
                                    break ;
                                case "报销申请" :
                                    str_old += "<p>"+ returnUpper(levelList[i]["level"]) +"级审批者："+ levelList[i]["toUser"]  +" / " +levelList[i]["userName"] +"</p>" +
                                        "<p>金额限制： "+ levelList[i]["limitAmount"] +" 元</p>" ;
                                    break ;
                                case "请假申请" :
                                case "加班申请" :
                                    str_old += "<p>"+ returnUpper(levelList[i]["level"]) +"级审批者："+ levelList[i]["toUser"]  +" / " +levelList[i]["userName"] +"</p>"  +
                                        "<p>时长限制： "+ levelList[i]["limitAmount"] +"小时</p>" ;
                                    break ;
                                default : break
                            }
                        }
                    }
                }
            }
            if(newInfo){
                var lev2 =  newInfo["level"] ;
                var levelList2 = newInfo["approvalFlowHistories"] ;
                if(lev2 == 0){
                    str_new = "<p>无需审批</p>" ;
                }else{
                    str_new = "<p>"+ returnUpper(lev2) +"级审批</p>" ;
                    for(var j = 0 ; j < lev2; j++ ){ // 最高级审批者为最终审批者
                        var j_lev = levelList2[j]["level"] ;
                        if( j_lev == lev2 ){
                            str_new += "<p>最终审批者："+ levelList2[j]["toUser"]  +" / " +levelList2[j]["userName"] +"</p>" ;
                        }else{
                            switch (oldInfo["name"]){
                                case "修改审批设置" :
                                    str_new += "<p>"+ returnUpper(levelList2[j]["level"]) +"级审批者："+ levelList2[j]["toUser"]  +" / " +levelList2[j]["userName"] +"</p>" ;
                                    break ;
                                case "报销申请" :
                                    str_new += "<p>"+ returnUpper(levelList2[j]["level"]) +"级审批者："+ levelList2[j]["toUser"]  +" / " +levelList2[j]["userName"] +"</p>" +
                                        "<p>金额限制： "+ levelList2[j]["limitAmount"] +" 元</p>" ;
                                    break ;
                                case "请假申请" :
                                case "加班申请" :
                                    str_new += "<p>"+ returnUpper(levelList2[j]["level"]) +"级审批者："+ levelList2[j]["toUser"]  +" / " +levelList2[j]["userName"] +"</p>" +
                                        "<p>时长限制： "+ levelList2[j]["limitAmount"] +"小时</p>" ;
                                    break ;
                                default : break
                            }
                        }
                    }
                }

            }
            $("#old").html(str_old) ;   $("#new").html(str_new) ;

        } ,
        error:function () {
            bounce.show( $("#tip") ); $("#tip_ms").html("链接失败") ;
        },
        complete:function () {
            loading.close() ;
        }
    });

}
/*  creater  hxz  2018/3/14    查看弹框  批准按钮  */
function charge(type) { // 0 - 驳回 ； 1 - 批准
    $.ajax({
       "url" : "../approval/approvalApprovalItem.do" ,
       "data" : { "id" : editObj["id"] , "approvalStatus" : type  } ,
        beforeSend:function () { loading.open() ; } ,
        "type" :"post" ,
        "dataType" : "json" ,
        success:function (res) {
           var status = res["status"] ; //  1-成功，2-加班，3-请假，4-报销 有新申请，老流程没走完，不能批准。
           if(status == 1){
               var obj_this = editObj["obj_this"] ;
               obj_this.parent().parent().remove() ;
               bounce.show( $("#tip") ); $("#tip_ms").html("审批成功") ;
           }else if(status == 2){
               bounce.show( $("#tip") ); $("#tip_ms").html("有新加班申请，老流程没走完，不能批准") ;
           }else if(status == 3){
               bounce.show( $("#tip") ); $("#tip_ms").html("有新请假申请，老流程没走完，不能批准") ;
           }else if(status == 4){
               bounce.show( $("#tip") ); $("#tip_ms").html("有新报销申请，老流程没走完，不能批准") ;
           }else {
               bounce.show( $("#tip") ); $("#tip_ms").html("审批失败") ;
           }
        } ,
        error:function () {
            bounce.show( $("#tip") ) ; $("#tip_ms").html("链接失败！") ;
        } ,
        complete:function () {
            loading.close() ;
        }
    });
}
// creater  侯杏哲  2018/4/9  工具方法， 传入数字， 返回汉字
function returnUpper( num){
    switch (num){
        case 0:
        case "0": return "" ; break ;
        case 1:
        case "1": return "一" ; break ;
        case 2:
        case "2": return "二" ; break ;
        case 3:
        case "3": return "三" ; break ;
        case 4:
        case "4": return "四" ; break ;
        case 5:
        case "5": return "五" ; break ;
        case 6:
        case "6": return "六" ; break ;
        case 7:
        case "7": return "七" ; break ;
        case 8:
        case "8": return "八" ; break ;
        case 9:
        case "9": return "九" ; break ;
        default : break ;
    }
}