$(function () {
    getCurrentSetting();
    $(".ty-secondTab li").on("click",function () {
        $(this).addClass('ty-active').siblings().removeClass("ty-active")
        $(".tblContainer").eq($(this).index()).show().siblings().hide();
    })
});

/* creator：张旭博，2017-11-23 17:14:34，获取当前权限（已分配和未分配） */
function getCurrentSetting() {
    $.ajax({
        url:"../popedom/getOrgPopedomShow.do" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var unAssigned = data["data"].unAssigned;
            var assigned = data["data"].assigned;
            //未录入高管时显示重要提示
            if(assigned.length === 0){
                $(".ty-secondTab li").eq(1).hide();
                $("#assigned").hide();
                $(".importantTip").show();
            }else{
                $("#assigned").show();
                $(".importantTip").hide();
                //未分配没有结果时只显示已分配
                if(unAssigned === undefined){
                    $(".ty-secondTab li").eq(1).hide();
                    $("#assigned .ty-body").html(getListStr(assigned,1));
                }else{
                    $("#assigned .ty-body").html(getListStr(assigned,1));
                    $("#unassigned .ty-body").html(getListStr(unAssigned,0));
                }
            }
            $("[data-toggle='tooltip']").tooltip();
        }
    }) ;
}

/* creator：张旭博，2017-11-23 17:13:41，返回当前权限列表字符串 */
function getListStr(data,type) {
    var listStr = '';
    var dataList = data;
    var thirdUserStr = '';
    for (var i = 0; i < dataList.length; i++) {
        var subPopdoms2 = dataList[i].subPopdoms;
        //第一级列表
        listStr +=  '<ul>' +
                    '   <li style="width: 10%">' +
                    '       <span>' + dataList[i].name + '</span>' +
                    '   </li>' +
                    '   <li style="width: 90%"  level="1">' +
                    '       <div>';//嵌套一层table
        //第二层循环
        for (var j = 0; j < subPopdoms2.length; j++) {
            //第三层数据
            var subPopdoms3 = subPopdoms2[j].subPopdoms;
            //如果第三层数据不为空
            if(subPopdoms3.length !== 0){
                //第二级列表
                listStr +=  '        <ul>' +
                            '           <li style="width: 20%">' +
                            '              <span>' + subPopdoms2[j].name + '</span>' +
                            '           </li>' +
                            '           <li style="width: 80%"  level="2">' +
                            '               <div>';//嵌套一层table
                //如果是未分配
                if(type === 0){
                    for (var k = 0; k < subPopdoms3.length; k++) {
                        listStr +=  '                <ul>' +
                                    '                   <li style="width: 20%">' +
                                    '                       <span>' + subPopdoms3[k].name + '</span>' +
                                    '                   </li>' +
                                    '                   <li style="width: 80%">' +
                                    '                       <span>' + subPopdoms3[k].desc + '</span>' +
                                    '                   </li>' +
                                    '                </ul>';
                    }
                    //如果是已分配
                }else{
                    for (var k = 0; k < subPopdoms3.length; k++) {
                        listStr +=  '                <ul>' +
                                    '                   <li style="width: 20%">' +
                                    '                       <span>' + subPopdoms3[k].name + '</span>' +
                                    '                   </li>' +
                                    '                   <li style="width: 50%">' +
                                    '                       <span>' + subPopdoms3[k].desc + '</span>' +
                                    '                   </li>' +
                                    '                   <li style="width: 30%">' +
                                    '                       <span>' + getUserStr(subPopdoms3[k].users)+ '</span>' +
                                    '                   </li>' +
                                    '                </ul>';
                    }
                }
                listStr +=  '               </div>' +
                            '           </li>' +
                            '        </ul>';
                //如果第三层数据为空数组
            }else{
                //如果是未分配的第三层数据
                if(type === 0){
                    listStr +=  '        <ul>' +
                                '           <li style="width: 20%">' +
                                '              <span>' + subPopdoms2[j].name + '</span>' +
                                '           </li>' +
                                '           <li style="width: 80%">' +
                                '               <span>' + subPopdoms2[j].desc + '</span>' +
                                '           </li>' +
                                '        </ul>';
                    //如果是已分配的第三层数据
                }else{
                    //如果正好这一层的上级为个人中心或者关于（分配的人为默认字符非系统获取）
                    if(dataList[i].name === "个人中心" || dataList[i].name === "关于"){
                        if(subPopdoms2[j].name === "请求处理"){
                            thirdUserStr = '<span>全部非普通职工</span>';
                        }else{
                            thirdUserStr = '<span>全部职工</span>';
                        }
                        //否则正常获取人员
                    }else{
                        thirdUserStr = '<span>' +getUserStr(subPopdoms2[j].users)+ '</span>';
                    }
                    //填入后面的第二级名称（三级为空）、描述、以及已拥有权限者
                    listStr +=  '        <ul>' +
                                '           <li style="width: 20%">' +
                                '              <span>' + subPopdoms2[j].name + '</span>' +
                                '           </li>' +
                                '           <li style="width: 16%"></li>' +
                                '           <li style="width: 40%">' +
                                '               <span>' + subPopdoms2[j].desc + '</span>' +
                                '           </li>' +
                                '           <li style="width: 24%">' + thirdUserStr + '</li>' +
                                '        </ul>';
                }
            }
        }
        //补齐字符串
        listStr +=  '       </div>' +
                    '   </li>' +
                    '</ul>';
    }
    return listStr;
}

/* creator：张旭博，2017-11-23 17:13:06，获取人员列表 */
function getUserStr(userArr){
    var userStr = '';
    if(userArr){
        for(var i = 0 ;i<userArr.length;i++){
            var title= '部门：'+chargeNull(userArr[i].departName) + ' ； 职位：'+chargeNull(userArr[i].postName);
            if(i === 0){
                userStr += '<a class="tooltip-show" data-toggle="tooltip" title="' + title + '">' + userArr[i].userName + '</a>';
            }else{
                userStr += '、<a class="tooltip-show" data-toggle="tooltip" title="' + title + '">' + userArr[i].userName + '</a>';
            }

        }
        return userStr;
    }else{
        return '';
    }



}

/* creator：张旭博，2017-11-23 17:13:25，去空操作 */
function chargeNull(value) {
    return value === null?'--': value;
}