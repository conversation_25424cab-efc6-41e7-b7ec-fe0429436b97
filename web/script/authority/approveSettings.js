/**
 * Created by 侯杏哲 on 2017/2/10.
 */
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#limitTip"));
bounce_Fixed2.cancel();
var superList = [] ; // 存放超管和总务的基本信息， 第一个是超管，第二个是总务
var curEditType = {} ;  // 标识 当前操作的对象

$(function(){
    $("#userInput").click(function(){
        $("#selectU").show();
    }).on("keyup",function(){
        $("#userInput").data('uid', '')
        let txt = $(this).val();
        $("#selectU option").removeAttr("class");
        $("#selectU option").each(function(){
            let txto = $(this).html();
            if(txto.indexOf(txt) > -1){
                $(this).show();
                if(txto == txt){
                    match($(this))
                    $(this).attr("class", "active")
                }
            }else{
                $(this).hide();
            }
        })
    });
    $("#selectU").on("click","option", function(){
        match($(this));
        $("#selectU").hide();
    })
    getMainList();
    $(".sp>p").click(function(){
        $(this).attr("class","selectP").siblings().removeAttr("class").find(".fa").attr("class","fa fa-circle-o");
        $(this).find(".fa").attr("class","fa fa-dot-circle-o")
        showtime()
    })
    // creator : 侯杏哲 2017-05-15 权限分配 下拉框 赋默认值
    $("#chargeList").children().each(function(){ // tr 循环
        var spanList = $(this).children(".hd").children() ;
        var tr_this = $(this) ;
        var k = 3 ;
        var lastChargeName = "" ;
        spanList.each(function(){ // span 循环
            lastChargeName = $(this).html() ;
            if(k<=4){
                tr_this.children(":eq("+ k +")").html($(this).html()) ;
                k++ ;
            }
        });
        tr_this.children(":eq(5)").html(lastChargeName) ;
    }) ;
    // creator : 侯杏哲 2017-05-15 权限分配 下拉框 赋默认值
    tySelectSetInit() ;

    // 获取超管基本信息
    $.ajax({
        url : "../approval/getSuperAndGeneral.do",
        beforeSend: function () {},
        success:function (res) {
            var data = res["data"] ;
            if(data && data.length == 2){ superList.push(data[0]) ; superList.push(data[1]) ;}
        } ,
        complete:function () {}
    }) ;
    // 切换二级菜单
    $(".ty-secondTab li").click(function (index) {
        $(".ty-secondTab li").removeClass("ty-active") ;
        $(this).addClass("ty-active");
        var index = $(this).index();
        $(".ty-mainData .tab").addClass("hd");
        $(".ty-mainData .tab").eq(index).removeClass("hd") ;
    }) ;

    $("#amountInput").change(function(){
        $("#anyAmount").children("i").attr("class", "fa fa-circle-o");
        $("#anyAmount").data("anyAmount", 0) ;
    })
    // 订阅修改加班流程申请
    sphdSocket.subscribe('updateItemApply', function (data){
        console.log('申请返回值', data)
        var data = JSON.parse(data);
        var state = data["state"]; // state： 0-修改失败 1-修改成功 2-此修改申请正在申请中，不能再次提交！ 3-修改的不是加班
        var content = data["content"]; // state： 0-修改失败 1-修改成功 2-此修改申请正在申请中，不能再次提交！ 3-修改的不是加班
        loading.close();
        layer.msg(content);
    } , function(){console.log('修改加班流程申请 申请失败！')});

    // 订阅修改请假流程申请
    //sphdSocket.subscribe('updateItemApply', leaveCallBack , function(){console.log('修改请假流程申请 申请失败！')});
    $("#saleApprove_common").on("click", ".changeDot", function () {
        $(this).find("input:radio").prop("checked", true)
        $(this).addClass("select-active").siblings().removeClass("select-active");
        let currentState = $("#saleApprove_common").data("currentState");
        let inx = $(this).find("input:radio").attr("value")
        if (currentState != inx) {
            $("#startDateSale").show().find("input").val("");
        } else {
            $("#startDateSale").hide();
            $("#saleApprove_common .bonceFoot button").hide();
        }
    });
    $(".modeSect").on("click", ".ty-radio", function (et) {
        et.stopPropagation();
        let able = false;
        if (!$(this).hasClass("grayState")){
            if ($(this).find(".fa-circle-o").length >0) {
                able = true;
                $(this).find(".fa").removeClass("fa-circle-o").addClass("fa-circle");
                $(this).siblings().find(".fa").removeClass("fa-circle").addClass("fa-circle-o");
            } else {
                $(this).find(".fa").removeClass("fa-circle").addClass("fa-circle-o");
            }
            let level = $(this).parent().data("align");
            let val = $(this).find(".fa").data("val");
            if(level === 1) {
                if(able && val === 2) {
                    $("#cm_modlue1").removeClass("fa-circle").addClass("fa-circle-o").parent().addClass("grayState");
                    $("#cm_modlue2").hasClass("fa-circle")? $(".authorCare").hide(): $(".authorCare").show()
                } else {
                    $("#cm_modlue1").parent().removeClass("grayState");
                    $("#cm_modlue2").hasClass("fa-circle")? $(".authorCare").show(): $(".authorCare").hide()
                }
            } else if(level === 2) {
                if (able && val === 1){
                    $(".authorAble").hide();
                } else {
                    if (!$(".authorAble").is(":visible")) {
                        $(".authorAble").show();
                        $(".authorAble .fa").attr("fa fa-circle-o");
                    }
                    $("#zs_modlue2").hasClass("fa-circle")? $(".authorCare").hide(): $(".authorCare").show();
                }
            }
        }
    });
    $(".bounce").on("click", ".ty-btn", function () {//,.btnCat,.bonceHead>a
        var name = $(this).data("fun")
        switch (name) {
            case 'saleApprove': // 修改设置
                let flag = $("#sale_common").data("applyType");
                let currentState = $("#sale_common").data("currentState");
                $("#saleApprove_common").data("currentState", currentState);
                let num = 1 - Number(currentState);
                $("#startDateSale").hide().find("input").val("");
                $("#saleApprove_common .bonceFoot button").hide();
                $("#saleApprove_common .saleTip" + flag).show().siblings().hide();
                // $("#saleApprove_common .changeDot span").attr("class", "fa fa-circle-o");
                //$("#saleApprove_common .needStr").html(flag < 4?"需要": "手动关联")
                //$("#saleApprove_common .noNeedStr").html(flag < 4?"不需要": "自动关联")
                $("#saleApprove_common input:radio").eq(num).prop("checked", true).parents(".changeDot").addClass("select-active").siblings().removeClass("select-active")

                // if (flag == 1 || flag == 2 || flag == 3){
                //     $(".tick1").show().siblings().hide();
                //     $(".tick1 .changeDot").eq(num).addClass("select-active").siblings().removeClass("select-active");
                //     $(".tick1 .changeDot").eq(num).children("span").attr("class", "fa fa-dot-circle-o");
                // } else {
                //     $(".tick2").show().siblings().hide();
                //     $(".tick2 .changeDot").eq(num).addClass("select-active").siblings().removeClass("select-active");
                //     $(".tick2 .changeDot").eq(num).children("span").attr("class", "fa fa-dot-circle-o");
                // }
                startTimer(chargCommodityShow);
                bounce_Fixed.show($("#saleApprove_common"));
                break;
            case 'productSetting': // 修改设置
                $(".modeSect .fa").attr("class", "fa fa-circle-o");
                $("#ptCreationModeChange .ty-radio").removeClass("grayState");
                bounce_Fixed.show($("#ptCreationModeChange"));
                let data =  JSON.parse($("#productCreationMode .currentMode").html());
                let pdModelSettings = data.pdModelSettings;
                $(".modeSect:eq(0) .ty-radio").eq(pdModelSettings.dedicatedModel-1).click();
                $(".modeSect:eq(1) .ty-radio").eq(pdModelSettings.generalModel-1).click();
                if (pdModelSettings.generalModel !== 1) {
                    $(".modeSect:eq(2) .ty-radio").eq(pdModelSettings.reviseModel-1).click();
                }
                $("#pt_startDate").hide().find("input").val("");
                $("#ptCreationModeChange .bonceFoot button").hide();
                startTimer(ptStartDate);
                break;
        }
    });
    getHostTime(function (hosttime) { //与当前时间作比较
        console.log(hosttime)
        var dBroad = new Date(Number(new Date(hosttime).getTime()) + 86400000).format('yyyy-MM-dd');
        laydate.render({elem: '#startDateSale input' ,min: dBroad});
    });
    $("#chargeList").on("click", 'button', function (){
        var name = $(this).attr("name")
        var code = $(this).parents("tr").data("code")
        var id = $(this).parents("tr").data("id")
        switch (name) {
            // 主列表 - 查看
            case 'see':
                requestSet(code, id, $(this))
                break
            // 主列表 - 查看
            case 'change':
                requestSet(code, id, $(this))
                break
            // 主列表 - 修改记录
            case 'changeRecord':
                requestLog(code, id)
                break
        }
    })
    $("#anyTimeApply").on("click", function (){
        var isChecked = $(this).prop("checked")
        $("#changeFactApplyDurationRule [name='applyDuration']").prop("disabled", isChecked)
    })
}) ;
// creator：hxz 2019/11/27 匹配
function match(obj){
    let txt = obj.html()
    let id = obj.val()
    $("#userInput").data('uid', id).val(txt);
    showtime()
}

// creator：hxz 2019/11/27 获取主列表
// updatar: 张旭博，2022-10-12 10:41:06， 获取主列表
function getMainList(){
    var json = JSON.stringify({oid: sphdSocket.user.oid})
    $.ajax({
        url: "../popedom/approvalPowerSettingPage.do",
        data: { json: json },
        success:function (res) {
            var data = res.data
            var approvalItems = data.approvalItemPC
            var str = ''
            if (approvalItems && approvalItems.length > 0) {
                for (var i in approvalItems) {
                    var item = approvalItems[i]
                    let bg = ``
                    if (item.code === 'commodityProduct' || item.code === 'inStockCheckerSet') {
                        bg = ' class="txt"'
                    } else {
                        bg = ` class="label label-${(item.status === 1?'blue':'gray')}"`
                    }
                    str +=  '<tr data-code="'+item.code+'" data-id="'+item.id+'">' +
                            '   <td>'+item.name+'</td>'+
                            '   <td><span '+ bg +'>'+chargeSpecial(item).stateStr+'</span></td>'+
                            '   <td>'+(item.status === 1?item.firstUserName||'': '')+'</td>'+
                            '   <td>'+(item.status === 1?item.secondUserName||'': '')+'</td>'+
                            '   <td>'+(item.status === 1?item.finalUserName||'': '')+'</td>'+
                            '   <td>'+chargeSpecial(item).btnStr+'</td>'+
                            '</tr>'
                }
                $("#chargeList").html(str);
            }else{
                layer.msg("获取数据失败！")
            }

        }
    })
}

// creator: 张旭博，2022-10-12 10:41:43， 处理主列表特殊数据展示
function chargeSpecial(item) {
    var code = item.code
    var status = item.status
    var level = item.level
    var stateStr = ''
    var btnStr = ''
    var btnState = 'see'
    switch (code) {
        case 'overTimeApply':
            // 加班
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break
        case 'leaveApply':
            // 请假
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break
        case 'reimburseApply':
            // 报销
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break
        case 'paymentApproval':
            // 付款
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break
        case 'workAttendanceApply':
            // 修改考勤
            stateStr = status === 1?UpperLevel(level): '无需审批'
            btnState = 'none'
            break
        case 'archivesApply':
            // 修改职工档案
            stateStr = status === 1?UpperLevel(level): '无需审批'
            btnState = 'none'
            break
        case 'postApply':
            // 修改岗位设置
            stateStr = status === 1?UpperLevel(level): '无需审批'
            btnState = 'none'
            break
        case 'ordersReview':
            // 来自客户订单的评审
            stateStr = status === 1?'需要评审':'无需评审'
            break
        case 'materialInCheck':
            // 采购来材料的入库检验
            stateStr = status === 1?'需要检验':'无需检验'
            break
        case 'productInCheck':
            // 货物入成品库前的检验
            stateStr = status === 1?'需要检验':'无需检验'
            break
        case 'commodityProduct':
            // 商品与产品的关联
            stateStr = item.commodityProductDes
            break
        case 'stockModeChange':
            // 仓库的模式
            stateStr = status === 1?'智能仓库':'非智能仓库'
            break
        case 'itemApply':
            // 修改审批设置
            stateStr = status === 1?UpperLevel(level): '无需审批'
            btnState = 'change'
            break
        case 'purchaseApprovalSettings':
            // 采购审批设置
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break
        case 'productLogisticsCheck':
            // 成品出库时物流人员的复核
            stateStr = status === 1?'需物流复核':'无需物流复核'
            break
        case 'finishedProductCheck':
            // 成品库
            stateStr = status === 1?'已启用':'目前未启用'
            break
        case 'inStockCheckerSet':
            const stateArr = ['库管', '检验员', '库管与检验员', '无']
            stateStr = stateArr[status]
            break

    }
    if (btnState === 'see') {
        btnStr =    '<button class="link-blue" name="see">查看</button>' +
                    '<button class="link-blue" name="changeRecord">修改记录</button>'
    } else if (btnState === 'change') {
        btnStr =    '<button class="link-blue" name="change">修改</button>'
    }
    return {
        stateStr: stateStr,
        btnStr: btnStr
    }

}

// creator: 张旭博，2022-10-12 11:39:50， 各个审批设置查看
function requestSet( type , itemId , obj  ) {
    $("#beforeTip").html("") ;
    $(".ty-opItems").hide();
    curEditType["type"] = type ;
    curEditType["itemId"] = itemId ;
    curEditType["obj"] = obj ;

    var url = '../popedom/getItemDetail.do'
    var data = {
        json: JSON.stringify({ itemId: itemId })
    }
    if (type === 'itemApply') {
        url = '../approval/getLeaveOutTimeLevelList.do'
        data = { itemId : itemId  }
    }

    $.ajax({
        url: url ,
        data : data,
        success:function( res ){
            var data = res
            if (type !== 'itemApply') {
                data = res.data
            }
            var approvalItem = data.approvalItem;
            var approvalFlowList = data.approvalFlowList ;
            let sets = ``;
            switch (type ){
                // 加班
                case "overTimeApply" :
                    var approvalFlows = data.approvalFlows
                    var supplementary = data.supplementary // 加班/请假补报详情
                    var rule = data.rule // 加班/请假限制（提前量）详情
                    var submitRule = data.submitRule // 提交实际加班时限规则详情

                    var upperLimit = rule.upperLimit // 加班提前量
                    var state_makeAfterFactStr = chargeOpenState(supplementary.enabled?1:0) // “补报加班”的功能 是否开启
                    $("#overTime .upperLimit").html(upperLimit)
                    $("#overTime .upperLimit").attr('data-id', rule.id)
                    $("#overTime .state_makeAfterFact").html(state_makeAfterFactStr)
                    $("#overTime .state_factDurationRule").html(submitRule.upperLimit>0?submitRule.upperLimit:'--')
                    $("#overTime .state_factDurationRule").data('id', submitRule.id)
                    $("#overTime .state_makeAfterFact").attr('data-id', supplementary.id)
                    $("#overTime .state_makeAfterFact").attr('state', supplementary.enabled)

                    if( approvalItem.status === 1){ // 需要审批
                        var str = "" ;
                        if(approvalFlows && approvalFlows.length > 0){
                            $("#flowData").val(JSON.stringify(data));
                            for(var i = 0 ; i < approvalFlows.length ; i++){
                                var userName = approvalFlows[i]["userName"];
                                if(approvalFlows[i]["toUserId"] === 0){ userName = "直接上级"; }
                                var lev = returnUpper(i + 1) + "级审批";
                                str += "<div class='itemLe'><p>不高于"+ approvalFlows[i]["amountCeiling"] +"小时的加班，需"+ lev +"</p>"+
                                    "<p><span>"+ lev + "者</span><span>"+ userName +"</span><span>"+ (approvalFlows[i]["mobile"]||"") +"</span></p></div>";
                            }
                        } else {
                            str = "无需审批"
                        }
                        $("#approvList").html( str ) ;
                    } else{ // 不需要审批
                        $("#approvList").html( "不需要审批" ) ;
                    }

                    bounce_Fixed.show($("#overTime")) ;
                    $("#overTimeEditBtn").show();

                    break ;
                // 请假
                case "leaveApply" :
                    var approvalFlows = data.approvalFlows
                    var supplementary = data.supplementary // 加班/请假补报详情
                    var rule = data.rule // 加班/请假限制（提前量）详情

                    var upperLimit = rule.upperLimit // 请假提前量
                    var state_makeAfterFactStr = chargeOpenState(supplementary.enabled?1:0)// “补报请假”的功能 是否开启
                    $("#leave .upperLimit").html(upperLimit)
                    $("#leave .upperLimit").attr('data-id', rule.id)
                    $("#leave .state_makeAfterFact").html(state_makeAfterFactStr)
                    $("#leave .state_makeAfterFact").attr('data-id', supplementary.id)
                    $("#leave .state_makeAfterFact").attr('state', supplementary.enabled)

                    if( approvalItem.status === 1){ // 需要审批
                        var str = "" ;
                        if(approvalFlows && approvalFlows.length > 0){
                            str = leaveLevenStr(approvalFlows);
                        } else {
                            str = '无需审批'
                        }
                        $("#leaveApprovList").html( str ) ;
                    } else{ // 不需要审批
                        $("#leaveApprovList").html( "不需要审批" ) ;
                    }
                    bounce_Fixed.show($("#leave")) ;
                    $("#leaveEditBtn").show();
                    break ;
                // 报销
                case "reimburseApply" :
                    bounce.show($("#finananceEdit")) ;
                    $("#closeFin2").show().siblings().hide();
                    $("#editFlows").hide().siblings().show();
                    $("#finananceEdit .bonceHead span").html("查看报销审批的审批设置");

                    $("#editFlows").hide();
                    var approvalFlows = data.approvalFlows // 报销审批流程信息
                    var approvalProcessList = data.approvalProcessList // 修改申请的审批流程
                    var str = "", topAmount = 0 ;
                    for(var k = 0 ; k < approvalFlows.length ; k++){
                        var apitem = approvalFlows[k] , lev = returnUpper(apitem.level) ;
                        str += "<div class=\"apItem\">"
                        if(apitem['amountCeiling'] == "-1" && k == 0){
                            str += " <p>报销需"+ lev +"级审批</p>" ;
                        }else if(apitem['amountCeiling'] == "-1"){
                            str += " <p>高于"+ topAmount + "元的报销，需"+ lev +"级审批</p>" ;
                        }else{
                            topAmount = apitem['amountCeiling'] ;
                            str += " <p>不高于"+ apitem['amountCeiling'] +"元的报销，需"+ lev +"级审批</p>" ;
                        }
                        str +=    "     <p class=\"txtRight\">" +
                            "         <span>"+ lev +"级审批者</span>" +
                            "         <span>"+ (apitem['userName'] || apitem['toUser']) +"</span>" +
                            "         <span>"+ (apitem['mobile'] || "") +"</span>" +
                            "     </p>" +
                            "     <div class=\"clr\"></div>" +
                            " </div>"
                    }
                    $("#flowList3").html(str);
                    var len = approvalFlows.length ;
                    var lastAmountCeiling = approvalFlows[len-1 ]['amountCeiling'] ;
                    if(lastAmountCeiling != "-1"){
                        $("#flowList3").next().html(" 职工无法提交超过"+ topAmount +"元的报销申请。")
                    }
                    break ;
                // 付款
                case "paymentApproval":
                    var status = data.status || 0
                    if (status == 0){
                        layer.msg(data.content)
                        return false
                    }
                    bounce.show($("#paymentSet"));
                    approvalItem = data['approvalItem']; // 付款
                    var approvalItem1 = data['approvalItem1']; // 付款复核
                    var approvalFlows = data['approvalFlows'] && data['approvalFlows'][0];
                    var approvalProcessList = data['approvalProcessList'];
                    $("#paymentTtl").html("付款设置");
                    if(Number(approvalItem.status) === 0){
                        $("#cur1").html("无需审批");
                        $("#userInput").data('uid', 0).val(`无需审批`)
                    }else if(Number(approvalItem.status) === 1){
                        $("#cur1").html(`需经${ approvalFlows.userName } ${approvalFlows.mobile }审批`);
                        $("#cur1").data("userid", approvalFlows.toUserId)
                        $("#userInput").data('uid', approvalFlows.toUserId).val(`${ approvalFlows.userName } - ${approvalFlows.mobile }`)
                    }
                    $("#cur1").data("status", Number(approvalItem.status))
                    $("#cur2").data("status", Number(approvalItem1.status))
                    $("#cur2").html( Number(approvalItem1.status) === 1 ? `需经${ approvalItem1.approveUser } 复核` : "无需复核");
                    break;
                // 来自客户订单的评审
                case "ordersReview":
                    sets = approvalItem.status == 0? '无需评审':'需要评审';
                    $("#sale_common").data("applyType", 1);
                    $("#sale_common").data("currentState", approvalItem.status);
                    $("#sale_common .bonceHead span").html("来自客户订单的评审");
                    $("#msgLog").html("对于客户发来订单中的数量与交期，是否需公司各部门评审");
                    $("#funLog").html("系统默认需要评审。可修改为“无需评审”");
                    $("#currentSettings").html(sets);
                    bounce.show($("#sale_common"));
                    break;
                // 采购来材料的入库检验
                case "materialInCheck":
                    sets = approvalItem.status == 0? '无需检验':'需要检验';
                    $("#currentSettings").html(sets);
                    $("#sale_common").data("applyType", 2);
                    $("#sale_common").data("currentState", approvalItem.status);
                    $("#sale_common .bonceHead span").html("采购来材料的入库检验");
                    $("#msgLog").html("对于采购来的材料，入库前是否需要检验");
                    $("#funLog").html("系统默认需要检验。可修改为“无需检验”");
                    bounce.show($("#sale_common"));
                    break;
                // 货物入成品库前的检验
                case "productInCheck":
                    sets = approvalItem.status == 0? '无需检验':'需要检验';
                    $("#currentSettings").html(sets);
                    $("#sale_common").data("applyType", 3);
                    $("#sale_common").data("currentState", approvalItem.status);
                    $("#sale_common .bonceHead span").html("货物入成品库前的检验");
                    $("#msgLog").html("生产出的货物入成品库前，是否需要检验");
                    $("#funLog").html("系统默认需要检验。可修改为“无需检验”");
                    bounce.show($("#sale_common"));
                    break;
                // 产品基本信息的创建模式
                case "commodityProduct":
                    $("#zsModel").html(data.pdModelSettings.dedicatedModel);
                    $("#tyModel").html(data.pdModelSettings.generalModel);
                    $("#reviseModel").html(data.pdModelSettings.reviseModel === 1 ? '由有权限创建商品的职工修改':'由有产品操作权限的职工修改');
                    $("#productCreationMode .currentMode").html(JSON.stringify(data));
                    bounce.show($("#productCreationMode"));
                    break;
                case "itemApply":
                    var level = approvalItem.level
                    $("#itemApply").find("[name='level']").val(level).change()
                    if (level === 1) {
                        $("#itemApply").find("[name='lastApprover']").val(approvalFlowList[0].toUserId).change()
                    }
                    bounce.show($("#itemApply")) ;
                    break
                case "stockModeChange":
                    sets = approvalItem.status == 0? '非智能仓库':'智能仓库';
                    $("#stockMode .nowModeName").html(sets)
                    $("#stockMode").data('state', approvalItem.status == 0?1:0)
                    bounce.show($("#stockMode")) ;
                    if (approvalItem.status == 0) {
                        $("#stockMode .changeBtn").unbind().on("click", function () {
                            bounce_Fixed.show($("#stockModeChange_tip"))
                            $("#stockModeChange_tip .nextStep").unbind().on("click", function () {
                                bounce_Fixed.show($("#stockModeChange"))
                                $("#stockModeChange .newMode").html('智能仓库')
                                $("#stockModeChange input").val('')
                            })
                        })
                    } else {
                        $("#stockMode .changeBtn").unbind().on("click", function () {
                            bounce_Fixed.show($("#stockModeChange"))
                            $("#stockModeChange .newMode").html('非智能仓库')
                            $("#stockModeChange input").val('')
                        })
                    }
                    break
                // 采购审批设置
                case "purchaseApprovalSettings":
                    bounce.show($("#purchaseApprovalSettings"));
                    var approvalItem = data.approvalItem
                    var approvalFlows = data.approvalFlows

                    var level = approvalItem.level
                    var status = approvalItem.status
                    var openDate = moment(approvalItem.openDate).format("YYYY-MM-DD HH:mm:ss")
                    $("#purchaseApprovalSettings").find(".openDate").html(openDate)
                    if (status === 1) {
                        $("#purchaseApprovalSettings .btn_changeNoApprove").show()
                        $("#purchaseApprovalSettings .tips").html('目前，本公司采购需 <span class="level">'+NumberToChinese(level)+'</span> 级审批')
                    } else {
                        $("#purchaseApprovalSettings .btn_changeNoApprove").hide()
                        $("#purchaseApprovalSettings .tips").html("目前，本公司采购无需审批")
                    }
                    var approvalFlowsStr = ''
                    for (var i in approvalFlows) {
                        approvalFlowsStr += '<div class="item-flex">' +
                                            '    <div class="item-fix fix120">'+NumberToChinese(Number(i)+1)+'级审批者</div>' +
                                            '    <div class="item-auto text-right">'+approvalFlows[i].userName + ' ' + approvalFlows[i].mobile+'</div>' +
                                            '</div>'
                    }
                    $("#purchaseApprovalSettings").find(".approvalFlows").html(approvalFlowsStr)

                    break;
                // 成品出库时物流人员的复核
                case "productLogisticsCheck":
                    sets = approvalItem.status == 0? '无需物流复核':'需物流复核';
                    $("#productLogisticsCheck .currentSettings").html(sets);
                    bounce.show($("#productLogisticsCheck"));
                    break;
                // 成品库
                case "finishedProductCheck":
                    sets = approvalItem.status == 0? '目前未启用':'已启用';
                    $("#chengPinKuSet .currentSettings").html(sets);
                    bounce.show($("#chengPinKuSet"));
                    break;
                case 'inStockCheckerSet':
                    const stateArr = ['库管', '检验员', '库管与检验员', '无']
                    sets = stateArr[approvalItem.status]
                    $("#inStockCheckerSet .currentSettings").html(sets);
                    bounce.show($("#inStockCheckerSet"));
                    break
                default: bounce.show( $("#Tip") ) ; $("#tipMs").html("没有识别查询项！") ;
                    break;
            }
        }
    }) ;
}

//  creator : 侯杏哲，2021-10-28 修改付款设置
function showtime() {
    $("#paymentSetUpdate .times").show();
    $("#timesInput").val("");
}
//  creator : 侯杏哲，2021-10-28 修改付款设置
function submitcon() {
    console.log(auth)
    let selectO = $(".sp .fa-dot-circle-o");
    let selectU = $("#userInput").data('uid');
    let state = selectU > 0 ? 1 : 0; // 付款审批
    let approvalFlows = ''
    if(state > 0){
        approvalFlows = [
            {
                "toUserId": selectU,
                "amountCeiling":'-1',
                "level":'1'
            }
        ]

        // approvalFlows = JSON.stringify(approvalFlows)
    }
    let val = '' // 付款复核
    if(selectO.length > 0){
        val = selectO.data("val");
    }else{
        layer.msg("请选择是否需要财务负责人复核");
        return false;
    }
    let timesInput = $("#timesInput").val()
    if(timesInput.length > 0){
    }else{
        layer.msg("请选择生效时间");
        return false;
    }
    let sendData = {
        "itemId": curEditType["itemId"] ,
        "openDate": timesInput,
        "state": state , // 付款
        "approvalFlows": approvalFlows,
        "paymentAuditState": val
    }
    loading.open()
    console.log('sendData', sendData)
    sphdSocket.send("updateItemApply", sendData)
    bounce.cancel()
}

function updatePaySet() {
    $("#selectU").hide();
    bounce.show($("#paymentSetUpdate"))
    $("#paymentSetUpdate .times").hide()
    let userid = $("#cur1").data("userid")
    let status1 = $("#cur1").data("status")
    let status2 = $("#cur2").data("status")
    if(status2 == 0){
        $("#paymentSetUpdate .sp>p:nth-child(2)").click()
    }else if(status2 == 1){
        $("#paymentSetUpdate .sp>p:nth-child(1)").click()
    }

    $.ajax({
        "url": "../popedom/getOptionalUser.do" ,
        "data" : { "userId": "" } ,
        success:function( res ){
            var users = res["data"]["users"] || [];
            var str = `<option value="0" value='0'>无需审批</option>`;
            users.forEach(function(item){
                if(item.userID !=  0){
                    str += `<option value='${item.userID}'>${item.userName} - ${item.mobile}</option>` ;
                }
            })
            $("#selectU").html( str ) ;
        }
    }) ;
}
//  creator : 侯杏哲，2020/05/7 选择任意金额
function toggleThis(thisObj) {
    $("#amountInput").val("");
    var iObj = thisObj.children("i") ;
    var iObjGray = iObj.hasClass("fa-circle-o") ;
    if( iObjGray ){
        iObj.attr("class", "fa fa-dot-circle-o")
        thisObj.data("anyAmount", 1) ;
        $("#amount").val("");
    }else {
        iObj.attr("class", "fa fa-circle-o")
        thisObj.data("anyAmount", 0) ;
    }
}
//  creator : 侯杏哲，2020/05/07 修改报销审批流程  取消
function cacelFin() {
    bounce_Fixed.show($("#reimburseTip"));
}
function finCancelAll() {
    bounce_Fixed.cancel()
    bounce.cancel()
}
//  creator : 侯杏哲，2020/05/07 修改报销审批流程  下一步
function nextFin() {
    if($("#nextFin").hasClass("bounce-cancel")){
        layer.msg("有未填项");
        return false;
    }
    var anyAmount = $("#anyAmount").data("anyAmount");
    var pID = $("#finPerson").data("id");
    var amountInput = $("#amountInput").val();
    var pMobile = $("#finPerson").data("mobile");
    var pName = $("#finPerson").html();
    var len = $("#flowList4").children(".apItem").length;
    var lev = returnUpper(len + 1) ;
    var str = "<div class=\"apItem\" data-id='"+ pID +"'>";
    var info = {
        'pID':pID ,
        'amountInput': amountInput ,
        'pName': pName ,
        'lev': (len + 1) ,
        "pMobile":pMobile  }
    if(anyAmount == 1){
        if(len == 0){
            str += " <p>报销需"+ lev +"级审批</p>" ;
        }else{
            var lastInfo = JSON.parse( $("#flowList4").children(":last").find(".hd").html());
            var topAmount = lastInfo.amountInput ;
            str += " <p>高于"+ topAmount + "元的报销，需"+ lev +"级审批</p>" ;
        }
    }else{

        if(len > 0){
            // 如果数值小于之前的不能进行下一步
            var lastInfo = $("#flowList4").children(":last").find(".hd").html();
            var lastInfo = JSON.parse(lastInfo);
            var lastAmountInput = lastInfo['amountInput']
            if(Number(lastAmountInput) >= Number(amountInput)){
                layer.msg("金额限制不能小于" + lastAmountInput + "元");
                return false;
            }
        }
        str += " <p>不高于"+ amountInput +"元的报销，需"+ lev +"级审批</p>" ;
    }
    str +=    "     <p class=\"txtRight\">" +
        "         <span>"+ lev +"级审批者</span>" +
        "         <span>"+ pName +"</span>" +
        "         <span>"+ pMobile +"</span>" +
        "     </p>" +
        "     <div class='hd'>"+ JSON.stringify(info) +"</div> " +
        "    <div class=\"clr\"></div>" +
        " </div>";
    $("#flowList4").append(str) ;


    if(anyAmount == 1){
        $("#startDate3").show();
        $("#finananceEdit .editShow").hide();
        $("#endFinTip").html("")
        $("#nextFin").hide().siblings().hide();
        $("#applyFin").show();
        $("#cacelFin").show();

    }else{
        $("#endFinTip").html("职工无法提交超过"+ topAmount +"元的报销申请");
        bounce_Fixed.show($("#finNextLevel")) ;
        $("#finNextStep").find(".fa").attr("class", "fa fa-circle-o");
        $("#finNextLevel").data("forItem","finace") ;
    }
}
//  creator : 侯杏哲，2020/05/07 修改报销审批流程
function finananceEditBtn() {
    $("#editFlows").show().siblings().hide();
    $("#nextFin").show().siblings().hide();
    $(".editShow").show();
    $("#startDate3").hide();
    $("#anyAmount").data("anyAmount", 0);
    $("#finananceEdit .bonceHead span").html("修改报销审批的审批设置");
    $("#finPerson").html(" -- 请选择 -- ");
    $("#flowList4").html("");
    $("#finPerson").data("id","");
    $("#finPerson").data("mobile","");
    $("#amountInput").val("");
    $("#finStartDate").val("");
    $("#anyAmount").children("i").attr("class", "fa fa-circle-o");
    $("#anyAmount").data("anyAmount", 0);
    bounce.everyTime('0.5s','finananceEdit',function(){
        console.log("finananceEdit");
        var amountInput = $("#amountInput").val();
        var anyAmount = $("#anyAmount").data("anyAmount");
        var finPerson = $("#finPerson").html();
        var highLight = true, highLightApply = true ;
        if(finPerson == " -- 请选择 -- "){ highLight = false ; }
        else if(anyAmount == 0 && amountInput == ""){
            highLight = false ;
        }
        if(amountInput > 1000000){
            $("#finananceEdit .tp").html("请输入小于1000000的金额");
            highLight = false ;
        }else{
            $("#finananceEdit .tp").html(" ");
        }
        if(highLight){
            $("#nextFin").attr("class", "ty-btn bounce-ok ty-btn-big ty-circle-5 ");
        }else{
            $("#nextFin").attr("class", "ty-btn bounce-cancel ty-btn-big ty-circle-5 ");
        }
        var finStartDate = $("#finStartDate").val()
        if(finStartDate == ""){ highLightApply = false;  }
        var leng = 0 ;
        if($("#flowList4").children().length == 0){ highLightApply = false;  }
        if(highLightApply){
            $("#applyFin").attr("class", "ty-btn bounce-ok ty-btn-big ty-circle-5 ");
        }else{
            $("#applyFin").attr("class", "ty-btn bounce-cancel ty-btn-big ty-circle-5 ");
        }
    });
}
//  creator : 侯杏哲，2020/01/04 修改付款
function payEditBtn() {
    $("#paymentTtl").html("修改付款审批的审批设置");
    $("#scanU").addClass("border").attr("onclick", "chooseTip($(\"#scanU\"), \"payment\")");
    $("#payEditBtn").hide();
    $("#startDate2").hide();
    $("#savePaychargeNo").hide();
    endTimer();
}
function savePaycharge() {
    
}
//  creator : 侯杏哲，2019/11/21 修改加班
function overTimeEdit() {
    bounce_Fixed.cancel();  bounce.show($("#overTimeEdit"));
    $("#startDate").hide(); $(".btns").hide();
    var dataStr = $("#flowData").val()
    var data = JSON.parse(dataStr);
    var list = data["approvalFlows"] ;
    $("#flowList").html("");
    $("#nextStep").find(".fa").each(function(){
        $(this).attr("class" ,"fa fa-circle-o") ;
    }) ;
    if(list && list.length>0){
        for(var i = 0 ; i <list.length; i++){
            var toUserId = list[i]["toUserId"]; // 审批人id
            var userName = list[i]["userName"] || list[i]['toUser']; // 审批人
            var amountCeiling = list[i]["amountCeiling"]; // 时长
            var str = "<div class=\"apItem\"><p>" ;
            if(i === 0 && list.length == 1){
                str += "<span class=\"levelName\">审批者</span>" ;
            }else{
                str += "<span class=\"levelName\">"+ returnUpper(i+1) +"级审批者</span>" ;
            }
            var hourStr = "", hourArr = [0.5,1,2,4,8,16,24] ;
            if( i === 0){
                for(var q = 0; q<hourArr.length; q++ ){
                    if(amountCeiling == hourArr[q] ){
                        hourStr += "<option value='"+ hourArr[q] +"' selected >"+ hourArr[q] +"</option>";
                    } else {
                        hourStr += "<option value='"+ hourArr[q] +"' >"+ hourArr[q] +"</option>";
                    }

                }
            }else{
                var index_ = hourArr.indexOf(amountCeiling) ;
                if(index_ == -1){
                    hourStr += "<option value='' >"+amountCeiling+"</option>";
                    for(var q = 0; q<hourArr.length; q++ ){
                        hourStr += "<option value='"+ hourArr[q] +"' >"+ hourArr[q] +"</option>";
                    }
                }else{
                    for(var q = index_; q<hourArr.length; q++ ){
                        if(hourArr[q] == amountCeiling){
                            hourStr += "<option value='"+ hourArr[q] +"' selected >"+ hourArr[q] +"</option>";
                        }else{
                            hourStr += "<option value='"+ hourArr[q] +"' >"+ hourArr[q] +"</option>";
                        }
                    }
                }
            }
            str += "        <span class=\"chargeName\" onclick='chooseTip($(this), \"overTime\")'>"+ userName +"</span>" +
                "        <div class=\"clr hd\">"+ toUserId +"</div>" +
                "    </p>" +
                "    <p>" +
                "        该审批者有权批准不高于<select onchange='chargeEditOverShow($(this))' class=\"chargeHours\">"+ hourStr  +"</select>小时的加班" +
                "    </p>" +
                "</div>";
            // var listStr = appendCharge(i , list);
            $("#flowList").append(str);
            chargeEditOverShow();
        }
    }
}
// creator: hxz 2019/11/22 提交修改加班审批流程申请
function applySubmit() {
    var userId = sphdSocket.user.userID ;
    var itemId = curEditType["itemId"], approvalFlows = [], openDate = "" ;
    var data = {
        'session':sphdSocket.sessionid,
        'userId':userId,
        'state':1,
        'itemId':itemId
    };
    if(curEditType["type"] == "overTimeApply"){
        openDate = $("#startDate input").val();
        $("#flowList").children().each(function(index){
            var itm = {
                'toUserId':$(this).find(".hd").html(),
                'amountCeiling':$(this).find(".chargeHours").val(),
                'userName':$(this).find(".chargeName").html() ,
                'level':index+1
            };
            approvalFlows.push(itm);
        });

    }else if(curEditType["type"] == "paymentApproval"){
        openDate = $("#startDate2 input").val();
        var un = $("#scanU").html().split(" -- ")[0];
        var itm = {
            'toUserId':$("#paymentID").val(),
            'amountCeiling':"2",
            'userName':un ,
            'level':1
        };
        approvalFlows.push(itm);
    }else  if(curEditType["type"] == "leaveApply"){
        openDate = $("#leaveStartDate").val();
        $("#leaveFlowList").children().each(function(){
            var flows = $(this).data("flows");
            approvalFlows.push(flows);
        });
    }else  if(curEditType["type"] == "reimburseApply"){
        if($("#applyFin").hasClass("bounce-cancel")){
            return false;
        }
        openDate = $("#finStartDate").val();
        $("#flowList4").children().each(function(){
            var info = $(this).find(".hd").html();
            info = JSON.parse(info);
            approvalFlows.push({
                "toUserId": info['pID'] ,
                "amountCeiling": (info['amountInput'] || -1) ,
                "level": info['lev'] ,
                "userName": info['pName']
            });
        });
    }else  if(curEditType["type"] == "commodityProduct"){
        openDate = $("#pt_startDate input").val();
        data["generalModel"] = $(".modeSect:eq(1) .fa-circle").data("val") ;
        data["dedicatedModel"] = $(".modeSect:eq(0) .fa-circle").data("val") ;
        if ($(".modeSect:eq(2)").is(":visible")){data["reviseModel"] = $(".modeSect:eq(2) .fa-circle").data("val")}
        endTimer();
    } else  if(curEditType["type"] === "ordersReview" || curEditType["type"] === "materialInCheck" || curEditType["type"] === "productInCheck" || curEditType["type"] === "commodityProduct"){
        openDate = $("#startDateSale input").val();
        data["state"] = $("#saleApprove_common input:radio:checked").val();
    } else if (curEditType["type"] === "stockModeChange") {
        openDate = $("#stockModeChangeDate").val();
        data["state"] = $("#stockMode").data('state')
    } else if (curEditType["type"] === "productLogisticsCheck") {
        openDate = $("#productLogisticsCheck_set .effectTime input").val();
        data["state"] = $("#productLogisticsCheck_set input:radio:checked").val();
    } else if (curEditType["type"] === "finishedProductCheck") {
        openDate = $("#chengPinKu_set .effectTime input").val();
        data["state"] = $("#chengPinKu_set input:radio:checked").val();
    } else if (curEditType["type"] === "inStockCheckerSet") {
        openDate = $("#inStockChecker_set .effectTime input").val();
        data["state"] = $("#inStockChecker_set input:radio:checked").val();
    }
    data["openDate"] = openDate ;
    data["approvalFlows"] = JSON.stringify(approvalFlows) ;
    data["paymentAuditState"] = "" ;
    console.log("传参：" + JSON.stringify(data))
    getHostTime(function (hosttime) { //与当前时间作比较
        console.log(hosttime)
        openDate = openDate.replace(/-/g,"/");
        var d2 = new Date();//取今天的日期
        var selectDay = new Date(Date.parse(openDate));
        if(selectDay > hosttime){
            loading.open(); bounce.cancel();bounce_Fixed.cancel()
            sphdSocket.send('updateItemApply',data);
        }else{
            layer.msg('生效日期必须在当前日期之后！ 请重新选择')
        }
    });
}

// creator: 侯杏哲  2019/11/22 选择下一步操作
function toggleFa2( _this, num ) {
    var faObj = _this.find(".fa") ;
    if(faObj.hasClass("fa-circle-o")){
        $("#nextStep").find(".fa").each(function(){
            $(this).attr("class" ,"fa fa-circle-o") ;
        }) ;
        faObj.attr("class" , "fa fa-dot-circle-o") ;
        if(num == 1){ // 设置下一级
            $(".btns").show(); $("#applySubmit").hide();   $("#editOverTimeOk").show();
            $("#startDate").hide();
        }else{ // 不设置下一级，设置完毕
            $(".btns").hide();
            $("#startDate").show(); $("#startDate").find("input").val(""); startTimer(overTimeCharge);
        }
    }else{
        faObj.attr("class" , "fa fa-circle-o") ;
        $(".btns").hide();
        $("#startDate").hide();
    }

}
//  creator : 侯杏哲，2019/11/22 确定新增下一级
function editOverTimeOk() {
    var isOk = true ; // 默认合法
    $("#flowList").find(".chargeHours").each(function(){
        var h = $(this).val();
        if(!h){
            isOk = false ;
        }
    });
    if(isOk){
        appendItem();
        $(".btns").hide();
        $("#nextStep").hide();
    }else{
        layer.msg("请选择正确的批准时间限制");
    }

}
//  creator : 侯杏哲，2019/11/21
function appendItem() {
    var len = $("#flowList").children().length ;
    if(len == 1){
        $("#flowList").children(":first").find(".levelName").html("一级审批者");
    }
    var str = "<div class=\"apItem\"><p>"+
                 "<span class=\"levelName\">"+ returnUpper(len+1) +"级审批者</span>" ;
    var h = $("#flowList").children(":last").find("select").val();
    var hourStr = "", hourArr = [0.5,1,2,4,8,16,24] ;
    var index_ = hourArr.indexOf(Number(h)) ;
    for(var q = index_+1; q<hourArr.length; q++ ){
        hourStr += "<option value='"+ hourArr[q] +"' >"+ hourArr[q] +"</option>";
    }
    str += "        <span class=\"chargeName\" onclick='chooseTip($(this), \"overTime\")'> 请选择 </span>" +
        "        <div class=\"clr hd\"></div>" +
        "    </p>" +
        "    <p>" +
        "        该审批者有权批准不高于<select onchange='chargeEditOverShow($(this))' class=\"chargeHours\">"+ hourStr  +"</select>小时的加班" +
        "    </p>" +
        "</div>";
    $("#flowList").append(str);

}

//  creator : 侯杏哲，2019/11/21
function chargeEditOverShow(_this) {
    if(_this){
        var uId = _this.parent().parent().find(".hd").html();
        if(!uId){
            layer.msg("请选择审批人！"); return false;
        }
        // 清除之后的数据
        _this.parent().parent().nextAll().remove();
    }
    var h = $("#flowList").children(":last").find("select").val();
    var roleName = $("#flowList").children(":last").find(".chargeName").html();
    if( roleName == "董事长"){
        $("#startDate").show(); $("#startDate").find("input").val(""); startTimer(overTimeCharge);
        $("#nextStep").hide();
        $(".btns").hide();
    }else{
        if(h){
            if(h <24){
                $("#nextStep").show(); $("#nextStep").find(".fa").attr("class", "fa fa-circle-o");
                $("#startDate").hide();
                $("#startDate").find("input").val("");
                $(".btns").hide();
            }else if(h == 24 ){
                $("#startDate").show(); $("#startDate").find("input").val(""); startTimer(overTimeCharge);
                $("#nextStep").hide();
                $(".btns").hide();
            }
        }else{
            layer.msg("请确认时间限制均小于24小时！")
        }
    }

}
var chargeBtnsTimer = 0;
function startTimer(callback){
    if ( chargeBtnsTimer ) {
        endTimer();
    }
    chargeBtnsTimer = setInterval(function(){
        callback();
    }, 200);
}
function overTimeCharge(){
    var v = $("#startDate").find("input").val();
    if(v){
        $(".btns").show();
        $("#applySubmit").show();
        $("#editOverTimeOk").hide();
        endTimer();
    }
}
//  creator : 侯杏哲，2020/01/04
function chargPayShow() {
    var date = $("#startDate2 input").val();
    if(date){
        $("#savePaycharge").show();
        endTimer();
    }
}
function endTimer(){
    clearInterval(chargeBtnsTimer);
}
// creator: 李玉婷，2021-09-22 17:06:14，来自客户订单的评审修改设置
function chargCommodityShow() {
    var date = $("#startDateSale input").val();
    if(date){
        $("#saleApprove_common .bonceFoot button").show();
        endTimer();
    }
}
// creator: 李玉婷，2021-09-22 17:06:14，来自客户订单的评审修改设置
function ptStartDate() {
    var date = $("#pt_startDate input").val();
    let able = true;
    $(".modeSect:visible").each(function (){
        let val = $(this).find(".fa-circle").length;
        if (val === 0) {
            able = false;
        }
    })
    if(able){
        $("#pt_startDate").show();
    } else {
        $("#pt_startDate").hide();
    }
    if(able && date){
        $("#ptCreationModeChange .bonceFoot button").show();
    } else {
        $("#ptCreationModeChange .bonceFoot button").hide();
    }
}
//  creator : 侯杏哲，2019/11/21 修改记录
function requestLog(type, id){
    if(type === 'stockModeChange') {
        stockModeLog()
        return false
    }
    if(type === 'inStockCheckerSet') {
        inStockCheckerSetLog()
        return false
    }
    bounce.show($("#requestLog"));
    $("#requestLog").data("code", type)
    $("#requestLog tbody").html("")
    $.ajax({
        url :"../popedom/getRecordList.do",
        data : { 'json': JSON.stringify({ oid: sphdSocket.user.oid, code: type }) },
        success:function (res) {
            var data = res.data
            var list = data.approvalItems;
            var curInfo = data.approvalItem; // 当前生效的加班审批流程设置
            var num = data.num;

            $("#log13").hide();$("#log1").show();
            var createDate = new Date(curInfo["createDate"]).format('yyyy/MM/dd hh:mm:ss') ;
            var createName = curInfo["createName"];
            var typeName = "",typeCat = "";
            $("#log2").html("状态");
            if(type === 'overTimeApply'){
                typeName = "加班";
                typeCat = "流程";
                $("#log1").html("操作");
                $("#logType").html("加班审批的修改记录")
            }else if(type === 'paymentApproval'){
                typeName = "付款";
                typeCat = "人";
                $("#log1").html("付款审批者");
                $("#log13").show();
                $("#logType").html("付款设置的修改记录")
            }else if(type === 'leaveApply'){
                typeName = "请假";
                typeCat = "流程";
                $("#log1").html("操作");
                $("#logType").html("修改记录")
            }else if(type === 'reimburseApply'){
                typeName = "报销";
                typeCat = "流程";
                $("#log1").html("操作");
                $("#logType").html("报销审批的修改记录")
            }else if(type === 'ordersReview' || type === 'materialInCheck' || type === 'productInCheck'){
                typeName = curInfo.name;
                $("#log1").html("设置的结果");
                $("#log2").html("执行的状态");
                $("#logType").html("修改记录")
            } else if (type === 'purchaseApprovalSettings'){
                typeName = "采购";
                typeCat = "流程";
                $("#log1").html("操作");
                $("#log2").html("执行的状态");
                $("#logType").html("修改记录")
            } else if (type === 'commodityProduct'){
                typeName = "采购";
                typeCat = "流程";
                $("#log1").hide();
                $("#log2").html("设置的结果");
                $("#logType").html("修改记录")
            } else if (type === 'productLogisticsCheck'){
                typeName = "成品出库时物流人员的复核";
                typeCat = "流程";
                $("#log1").html("设置的结果");
                $("#log2").html("执行的状态");
                $("#logType").html("修改记录")
            } else if (type === 'finishedProductCheck') {
                typeName = "成品库";
                typeCat = "流程";
                $("#log1").html("设置的结果");
                $("#log2").html("执行的状态");
                $("#logType").html("修改记录")
            }
            if(num === 0){
                if(list && list.length > 0){
                    if(type === 'paymentApproval'){
                        var r = list[0]["approvalName"] ;
                        $("#summary").html( "当前"+ typeName +"审批"+ typeCat +"为"+ r +",为原始信息。<p style='text-align: right; '>创建人："+ createName + " "+ createDate +"</p>");
                    }else if(type === 'ordersReview'){
                        $("#summary").html("来自客户订单的评审是否需要评审事宜");
                    }else if(type === 'materialInCheck'){
                        $("#summary").html("采购来材料的入库前是否需要检验事宜");
                    }else if(type === 'productInCheck'){
                        $("#summary").html("货物入成品库前是否需要检验事宜");
                    }else if(type === 'commodityProduct'){
                        $("#summary").html("通用型商品与产品是否使用相同的代号与名称？");
                    }else if(type === 'productLogisticsCheck'){
                        $("#summary").html("商品出库时，仓库确认后是否需要物流人员再次确认。");
                    }else if(type === 'finishedProductCheck'){
                        $("#summary").html("销售系统中，是否启用成品库。");
                    }else{
                        $("#summary").html( "当前"+ typeName +"审批"+ typeCat +"为原始信息。<p style='text-align: right; '>创建人："+ createName + " "+ createDate +"</p>");
                    }
                }else{
                    $("#summary").html( typeName + "审批"+ typeCat +"尚未经修改。<span style='float: right; '>创建人："+ createName + " "+ createDate +"</span>");
                }
            } else {
                if (type === 'leaveApply' || type === 'reimburseApply'){
                    $("#summary").html("当前的"+ typeName +"审批"+ typeCat +"为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");
                }else if(type === 'ordersReview'){
                    $("#summary").html("来自客户订单的评审是否需要评审事宜");
                }else if(type === 'materialInCheck'){
                    $("#summary").html("采购来材料的入库前是否需要检验事宜");
                }else if(type === 'productInCheck'){
                    $("#summary").html("货物入成品库前是否需要检验事宜");
                }else if(type === 'commodityProduct'){
                    $("#summary").html("通用型商品与产品是否使用相同的代号与名称？");
                }else if(type === 'productLogisticsCheck'){
                    $("#summary").html("商品出库时，仓库确认后是否需要物流人员再次确认。");
                }else if(type === 'finishedProductCheck'){
                    $("#summary").html("销售系统中，是否启用成品库。");
                }else if(type === 'paymentApproval'){
                    if(curInfo.status === 1){
                        $("#summary").html("当前的"+ typeName +"审批"+ typeCat +"为"+  res["data"]["approvalNameCurrent"] + ",为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");
                    }else{
                        $("#summary").html("当前无"+ typeName +"审批"+ typeCat + ",为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");

                    }
                }else if(type === 'purchaseApprovalSettings'){
                    $("#summary").html("当前的"+ typeName +"审批"+ typeCat +"为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");
                }else{
                    $("#summary").html("当前的"+ typeName +"审批"+ typeCat +"为"+  res["data"]["approvalNameCurrent"] + ",为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");
                }
            }
            if(list && list.length > 0){
                var len = list.length , str = "";
                for(var i = 0 ; i < len ; i++){
                    var openDateStr = "无";
                    if(list[i]['openDate']){ openDateStr = new Date(list[i]['openDate']).format('yyyy-MM-dd') ;  }
                    str += "<tr>" +
                        "    <td>"+ list[i]["recordState"] +"</td>" ;
                    if(type === 'commodityProduct') {
                        str +="  <td class='ty-td-control'><span class='ty-color-blue' onclick='logInfo("+ list[i]['collectId'] +")'>查看</span></td>" ;
                    } else {
                        str += "    <td>"+ list[i]['state'] +"</td>";
                    }
                    str += "    <td>"+ openDateStr +"</td>" ;
                    if(type === 'overTimeApply'){
                        str +="  <td class='ty-td-control'><span class='ty-color-blue' onclick='logInfo("+ list[i]['collectId'] +")'>查看</span></td>" ;
                    }else if(type === 'paymentApproval'){
                        str +=`
                            <td>${ list[i]['statusResult']  === 0 ? '无需审批' : list[i]['approvalName'] }</td> 
                            <td>${ list[i]['paymentAuditStatus'] === 0 ? '无需复核':list[i]['paymentAuditName'] }</td>`;
                    }else if(type === 'leaveApply'){
                        str +="  <td class='ty-td-control'><span class='ty-color-blue' onclick='leaveUpRecord("+ list[i]['collectId'] +")'>查看</span></td>" ;
                    }else if(type === 'reimburseApply'){
                        str +="  <td class='ty-td-control'><span class='ty-color-blue' onclick='logInfo("+ list[i]['collectId'] +")'>查看</span></td>" ;
                    }else if(type === 'ordersReview'){
                        var con = list[i].statusResult===0 ? "无需评审":"需要评审"
                        str +="  <td>"+con+"</td>" ;
                    }else if(type === 'materialInCheck' || type === 'productInCheck'){
                        var con = list[i].statusResult===0 ? "无需检验":"需要检验"
                        str +="  <td>"+con+"</td>" ;
                    }else if(type === 'purchaseApprovalSettings'){
                        str +="  <td class='ty-td-control'><span class='ty-color-blue' onclick='logInfo("+ list[i]['collectId'] +")'>查看</span></td>" ;
                    }else if(type === 'productLogisticsCheck'){
                        var con = list[i].statusResult===0 ? "无需物流人员再次确认":"需要物流人员再次确认"
                        str +="  <td>"+con+"</td>" ;
                    }else if(type === 'finishedProductCheck'){
                        var con = list[i].statusResult===0 ? "未启用":"已启用"
                        str +="  <td>"+con+"</td>" ;
                    }
                    str +="  <td>"+ list[i]["createName"] + " " + new Date( list[i]["createDate"] ).format('yyyy-MM-dd hh:mm:ss')  +"</td>" +
                        "</tr>";
                }
                $("#requestLog table").show()
                $("#requestLog").find("tbody").html(str);
            }else{
                $("#requestLog table").hide()
            }
        }
    })
}

// creator: 张旭博，2024-02-05 11:31:31， 仓库修改记录
function stockModeLog(){
    $.ajax({
        url :"../popedom/getRecordList.do",
        data : { 'json': JSON.stringify({ oid: sphdSocket.user.oid, code: 'stockModeChange' }) },
        success:function (res) {
            bounce.show($("#stockModeLog"));
            let data = res.data
            let list = data.approvalItems
            let str = ''
            for(let item of list){
                let openDateStr = item.openDate? moment(item.openDate).format('YYYY-MM-DD') : '无';
                str += `<tr>
                            <td>${item.recordState}</td>
                            <td>${item.statusResult === 0?'非智能仓库':'智能仓库'}</td>
                            <td>${openDateStr}</td>
                            <td>${item.createName} ${moment(item.createDate).format('YYYY-MM-DD HH:mm:ss') }</td>
                        </tr>`
            }
            $("#stockModeLog tbody").html(str)
        }
    })
}

// creator: 张旭博，2025-02-27 03:26:53， 修改记录
function inStockCheckerSetLog() {
    $.ajax({
        url :"../popedom/getRecordList.do",
        data : { 'json': JSON.stringify({ oid: sphdSocket.user.oid, code: 'inStockCheckerSet' }) },
        success:function (res) {
            bounce.show($("#inStockCheckerSetLog"));
            let data = res.data
            let list = data.approvalItems
            let info = data.approvalItem
            let str = ''
            if (list.length === 0) {
                $("#inStockCheckerSetLog .state0").show().siblings().hide()
                $("#inStockCheckerSetLog .state0 .create").html(`创建人 ${info.createName} ${moment(info.createDate).format('YYYY-MM-DD HH:mm:ss') }`)
            } else {
                $("#inStockCheckerSetLog .state1").show().siblings().hide()
                for(let item of list){
                    let openDateStr = item.openDate? moment(item.openDate).format('YYYY-MM-DD') : '无';
                    str += `<tr>
                            <td>${item.recordState}</td>
                            <td>${item.state}</td>
                            <td>${openDateStr}</td>
                            <td>${['库管', '检验员', '库管与检验员', '无'][item.statusResult]}</td>
                            <td>${item.createName} ${moment(item.createDate).format('YYYY-MM-DD HH:mm:ss') }</td>
                        </tr>`
                }
                $("#inStockCheckerSetLog tbody").html(str)
                $("#inStockCheckerSetLog .num").html(list.length - 1)
            }

        }
    })
}
//  creator : 侯杏哲，2019/11/21 修改记录-查看
function logInfo(id) {
    var code = $("#requestLog").data("code")
    $.ajax({
        url:"../popedom/getItemDetail.do",
        data:{json:"{'itemId':"+ id +"}"},
        success:function (data) {
            var list = data["data"]["approvalFlows"] ;
            var approvalItem = data["data"]["approvalItem"] ;
            if($("#logType").html() == "加班审批的修改记录"){
                bounce_Fixed.show($("#overTime")) ;
                $("#overTimeEditBtn").hide();
                $("#scanTtl").html("目前本公司加班的审批流程如下：");
                if( approvalItem["status"] == 1){ // 需要审批
                    var str = "" ;
                    if(list && list.length > 0){
                        $("#flowData").val(JSON.stringify(data["data"]));
                        for(var i = 0 ; i < list.length ; i++){
                            var userName = list[i]["userName"];
                            if(list[i]["toUserId"] === 0){ userName = "直接上级"; }
                            var lev = returnUpper(i + 1) + "级审批";
                            str += "<div class='itemLe'><p>不高于"+ list[i]["amountCeiling"] +"小时的加班，需"+ lev +"</p>"+
                                "<p><span>"+ lev + "者</span><span>"+ userName +"</span><span>"+ (list[i]["mobile"]||"") +"</span></p></div>";
                        }

                    } else{
                        str = "无需审批" ;
                    }
                    $("#approvList").html( str ) ;
                } else{ // 不需要审批
                    $("#approvList").html( "不需要审批" ) ;
                }
            }else if($("#logType").html() == "报销审批的修改记录"){
                bounce_Fixed.show($("#reimburse")) ;
                var approvalFlows = list ;
                var str = "", topAmount = 0 ;
                for(var k = 0 ; k < approvalFlows.length ; k++){
                    var apitem = approvalFlows[k] , lev = returnUpper(apitem.level) ;
                    str += "<div class=\"apItem\">"
                    if(apitem['amountCeiling'] == "-1" && k == 0){
                        str += " <p>报销需"+ lev +"级审批</p>" ;
                    }else if(apitem['amountCeiling'] == "-1"){
                        str += " <p>高于"+ topAmount + "元的报销，需"+ lev +"级审批</p>" ;
                    }else{
                        topAmount = apitem['amountCeiling'] ;
                        str += " <p>不高于"+ apitem['amountCeiling'] +"元的报销，需"+ lev +"级审批</p>" ;
                    }
                    str +=    "     <p class=\"txtRight\">" +
                        "         <span>"+ lev +"级审批者</span>" +
                        "         <span>"+ (apitem['userName'] || apitem['toUser']) +"</span>" +
                        "         <span>"+ (apitem['mobile'] || "") +"</span>" +
                        "     </p>" +
                        "     <div class=\"clr\"></div>" +
                        " </div>"
                }
                $("#flowList31").html(str);
                $("#reimburse .applyTip").html(" <div>审批记录：</div>")
                var approvalProcessList = data['data']['approvalProcessList'] , processStr = "<p>申请人："+ approvalItem["createName"] +"  "+ (new Date(approvalItem["createDate"]).format('yyyy-MM-dd hh:mm:ss'))  +"</p>";
                for(var j = 0 ; j < approvalProcessList.length ; j++){
                    var item = approvalProcessList[j]
                    processStr += "<p>审批人："+ item["userName"] +"   "+  (new Date(item["handleTime"]).format('yyyy-MM-dd hh:mm:ss'))  +"</p>";
                }
                $("#reimburse .applyTip").append(processStr);
            } else if (code === 'purchaseApprovalSettings') {
                data = data.data
                bounce_Fixed.show($("#purchaseApprovalSettingsSee"));
                var approvalItem = data.approvalItem
                var approvalFlows = data.approvalFlows

                var level = approvalItem.level
                var status = approvalItem.status
                var openDate = moment(approvalItem.openDate).format("YYYY-MM-DD HH:mm:ss")
                $("#purchaseApprovalSettingsSee").find(".openDate").html(openDate)
                if (status === 1) {
                    $("#purchaseApprovalSettingsSee .btn_changeNoApprove").show()
                    $("#purchaseApprovalSettingsSee .tips").html('采购需 <span class="level">'+NumberToChinese(level)+'</span> 级审批')
                } else {
                    $("#purchaseApprovalSettingsSee .btn_changeNoApprove").hide()
                    $("#purchaseApprovalSettingsSee .tips").html("采购无需审批")
                }
                var approvalFlowsStr = ''
                for (var i in approvalFlows) {
                    approvalFlowsStr += '<div class="item-flex">' +
                        '    <div class="item-fix fix120">'+NumberToChinese(Number(i)+1)+'级审批者</div>' +
                        '    <div class="item-auto text-right">'+approvalFlows[i].userName + ' ' + approvalFlows[i].mobile+'</div>' +
                        '</div>'
                }
                $("#purchaseApprovalSettingsSee").find(".approvalFlows").html(approvalFlowsStr)
            } else if (code === 'commodityProduct') {

                data = data.data
                bounce_Fixed.show($("#ptModeSettingsSee"));

                var pdModelSetting = data.pdModelSettings
                let roleStr = ``,str = ``;
                $(".zs_modeType").html("模式" + pdModelSetting.dedicatedModel);
                $(".cm_modeType").html("模式" + pdModelSetting.generalModel);
                if (pdModelSetting.dedicatedModel === 1 && pdModelSetting.generalModel === 1) {
                    $(".role_modeType").hide();
                } else {
                    $(".role_modeType").show();
                    if (pdModelSetting.dedicatedModel !== 2 && pdModelSetting.generalModel === 2) roleStr = `通用型`;
                    $(".role_modeType span:eq(0)").html(roleStr);
                    $(".role_modeType span:eq(1)").html(pdModelSetting.reviseModel === 1 ? '由有权限创建商品的职工修改':'由有产品操作权限的职工修改');
                }
            }

        }
    })
}
//  creator: 侯杏哲 2018-04-08  审批设置修改 - 变更审批级别
function changeAuth() {
    let level = Number($("#itemApply [name='level']").val()) ;
    var str ='', str2= '' ;
    if(level === 1){
        $("#itemApply [name='firstApprover']").parents(".item-flex").hide()
        $("#itemApply [name='lastApprover']").prop("disabled", false)
        str =   '<option value="' + superList[0].userID + '">' + superList[0].userName + '--' + superList[0].mobile + '</option>' +
                '<option value="' + superList[1].userID + '">' + superList[1].userName + '--' + superList[1].mobile + '</option>'
        $("#itemApply [name='lastApprover']").html(str)
    }else if(level === 2){
        $("#itemApply [name='firstApprover']").prop("disabled", true).parents(".item-flex").show()
        $("#itemApply [name='lastApprover']").prop("disabled", true)
        str =   '<option value="' + superList[1].userID + '">' + superList[1].userName + '--' + superList[1].mobile + '</option>'
        str2 =  '<option value="' + superList[0].userID + '">' + superList[0].userName + '--' + superList[0].mobile + '</option>'
        $("#itemApply [name='firstApprover']").html(str)
        $("#itemApply [name='lastApprover']").html(str2)
    }
}
// creator： 侯杏哲 审批设置 - 保存
function saveAuthcharge() {
    var itemId = curEditType["itemId"] ;
    var level = Number($("#itemApply [name='level']").val()) ;
    var userId = $("#itemApply [name='lastApprover']").val() ;
    $.ajax({
        url : "../approval/approvalApprovalApply.do" ,
        data : {
            id: itemId, // 审批项id
            level: level, // 审批级别
            userId: userId // 审批者id,level 为 2 时后台不取此字段
        } ,
        success:function (res) {
            var status  = res["status"] ;
            if(status == 1){
                bounce.show($("#Tip")) ; $("#tipMs").html("申请设置提交成功！") ;
            }else  if(status == 2){
                bounce.show($("#Tip")) ; $("#tipMs").html("还有当前流程没处理完的申请，不能变更！") ;
            }else{
                bounce.show($("#Tip")) ; $("#tipMs").html("申请设置提交失败！") ;
            }
        }
    })
}

// creator : 侯杏哲，2017-04-24 08:43:22，获得可以审批的人员
function getPersonStr(data , num) { // num 用于区分是方式2 ， 还是方式3
    var status = data["status"] ;
    var userList = data["userList"] ;
    var str = "<option value='0'>直接上级</option>" ;
    if(num == 3){  str = "" ;  }
    if( Number(status) == 1 ){ // 除了直属上级，还有别人
        if(userList && userList.length > 0 ){
            for( var i=0 ; i < userList.length ; i++ ){
                str += "<option value='"+ userList[i]["userID"] +"'>"+ userList[i]["userName"] +"</option>" ;
            }
        }
    }
    return str ;
}

// updator : 侯杏哲 2019/11/22 修改加班申请人提示确定  "overTime"
function chooseTip(_this , type) {
    // type - 有值表示新生成的， 不给提示了
    curSelectObj = { "obj": _this , "type":type } ;

    if(type === "payment" || type === "leave" || type === "finace"){
        chooseTipOk();
    }else{
        bounce_Fixed.show($("#overTimeEditTip"));
    }
}

function chooseTipOk() {
    bounce_Fixed.show($("#chooseApproveP"));
    var thisTr = curSelectObj["obj"].parent().parent() , pid ="" ;

    if(curSelectObj["type"] == "overTime"){
        thisTr.nextAll().remove() ;
        $("#tp2").html("");
        $("#approveTree").html( "" ) ;
        var prevAll = thisTr.prevAll() ;
        var len = prevAll.length ;
        if(len>0){
            for(var j = 0 ; j < len ; j++){
                var tr = $("#flowList").children(".apItem:eq("+ j +")") ;
                var userID = tr.find(".hd").html() ;
                pid +=  userID + "," ;
            }
        }
        pid = pid.substr(0 , (pid.length -1)) ;
    }else if(curSelectObj["type"] == "payment"){
        pid = ""
    }else if(curSelectObj["type"] == "leave"){
        $("#leaveFlowList").children().each(function () {
            var info = $(this).data("flows");
            pid +=  info.toUserId + "," ;
        })
        pid = pid.substr(0 , (pid.length -1)) ;
    }else if(curSelectObj["type"] == "finace"){
        $("#flowList4").children().each(function () {
            var id = $(this).data("id");
            pid += id + "," ;
        })
        pid = pid.substr(0 , (pid.length -1)) ;
    }
    $.ajax({
        "url": "../approval/getOptionalUser.do" ,
        "data" : { "userId": pid } ,
        success:function( data ){
            var users = data["users"] ;
            var str = "" ;
            if(users) {
                for (var i = 0; i < users.length; i++) {
                    var setStr = true ;
                    if(curSelectObj["type"] == "payment" &&  users[i]["userName"] == "直接上级"){
                        setStr = false ;
                    }
                    if(setStr){
                        str += "<p onclick='toggleFa($(this))'><span class='fa fa-circle-o' roleName='" + users[i]["roleName"] + "' userid='" + users[i]["userID"] + "' userName='"+ users[i]["userName"] +"' data-mobile='"+ users[i]["mobile"] +"'></span> <span>" + users[i]["userName"] + " -- " + users[i]["mobile"] + "</span></p>";
                    }
                }
            }
            $("#approveTree").html( str ) ;
        }
    }) ;
}
var curSelectObj = null ; // 我的请求 - 当前正在选择审批人的对象
// updator : 侯杏哲 2017-05-13   请假加班 - 选择审批人
function chooseApproveP( obj ) {
    var thisTr = obj.parent().parent() ;
    var pid ="" ;
    thisTr.children(":gt(0)").remove();
    thisTr.nextAll().remove() ;
    var prevAll = thisTr.prevAll() ;
    var len = prevAll.length ;
    if(len>0){
        for(var j = 0 ; j < len ; j++){
            var tr = $("#approveList .trList:eq("+ j +")") ;
            var userID = tr.find(".hd").html() ;
            pid +=  userID + "," ;
        }
    }else{
        thisTr.children(".trItem2:eq(1)").remove() ;
    }
    pid = pid.substr(0 , (pid.length -1)) ;
    curSelectObj = obj ;
    $("#approveTree").html( "" ) ;
    $("#beforeTip").html( "" ) ;
    $("#tp2").html( "" ) ;
    bounce_Fixed.show($("#chooseApproveP"));
    $.ajax({
        url: "../approval/getOptionalUser.do" ,
        data : { "userId": pid } ,
        success:function( data ){
            var users = data["users"] ;
            var str = "" ;
            if(users) {
                for (var i = 0; i < users.length; i++) {
                    str += "<p onclick='toggleFa($(this))'><span class='fa fa-circle-o' roleName='" + users[i]["roleName"] + "' userid='" + users[i]["userID"] + "'></span> <span>" + users[i]["userName"] + " -- " + users[i]["mobile"] + "</span></p>";
                }
            }
            $("#approveTree").html( str ) ;
        }
    }) ;
}
// creator: 侯杏哲  2018-04-23
function toggleFa( _this ) {
    var faObj = _this.children(".fa") ;
    if(faObj.hasClass("fa-circle-o")){
        $(".userList").find(".fa").each(function(){
            $(this).attr("class" ,"fa fa-circle-o") ;
        }) ;
        faObj.attr("class" , "fa fa-dot-circle-o") ;
    }else{
        faObj.attr("class" , "fa fa-circle-o") ;
    }
}

// creator: 侯杏哲 2017-05-16 请求处理 - 选择审批人 - 点击部门名称展示部门下的人员
function showOrg( obj ){
    obj.children().prop("checked",true);
    obj.siblings("ul.level").toggleClass("hd");
    if( obj.find("i").hasClass("fa-angle-right") ){
        obj.find("i").removeClass("fa-angle-right") ;
        obj.find("i").addClass("fa-angle-down") ;
    }else{
        obj.find("i").removeClass("fa-angle-down") ;
        obj.find("i").addClass("fa-angle-right") ;
    }
}
// creator: 侯杏哲 2017-05-15 请求处理 - 选择审批人 - 点击部门名称 获取并展示 部门下的人员
function showPerson( obj , id){
    var departmentID = id ;
    $.ajax({
        url:"../lo/getOrgByPid.do" ,
        data:{ "department": departmentID  } ,
        type:"post" ,
        dataType:"json" ,
        success:function( data ){
            var orgList = data["orgList"] ;
            var users = data["users"] ;
            var str = "<ul class='level level3'>" ;
            for(var i = 0 ; i < users.length ; i++ ){
                str += "<li class='approvePItem' ><input type='radio' value='"+ users[i]["userID"] +"' name='approveP'/>"+ users[i]["userName"] +"</li>" ;
            }
            for(var j = 0 ; j < orgList.length ; j++ ){
                str += "<li>" +
                    "<div class='approvePItem' onclick='showPerson($(this) , "+ orgList[j]["id"] + ")'><i class='fa fa-angle-right'></i>"+ orgList[j]["name"] +"</div>" +
                    "</li>" ;
            }
            str += "</ul>" ;
            obj.after(str) ;
            obj.attr("onclick","showOrg( $(this) )")
        } ,
        error:function(){  }
    });
    showOrg( obj ) ;
}
// creator : 侯杏哲 2017-05-15 请假加班 - 修改 - 删除审批级别
function deleteItem(obj){
    var tr = obj.parent().parent() ;
    tr.nextAll().remove() ;
    tr.remove() ;
    var trLast = $("#approveList").children(":last-child") ;
    trLast.children("div:gt(0)").remove() ;
}

// creator : 侯杏哲 2017-05-16 请假加班，变更审批级别 - 确定选择审批人
function selectOK(){
    var personObj = $(".userList").find(".fa-dot-circle-o");
    var personID = personObj.attr("userid") ;
    var personName = personObj.next().text() ;
    var roleName = personObj.attr("roleName");
    var mobile = personObj.data("mobile");
    var isOk = 1 ; // 标记有没有相同
    var curSelectObjType = curSelectObj["type"] , curSelectObjThis = curSelectObj["obj"] ;
    if(curSelectObjType && curSelectObjType == "overTime"){
        $("#flowList").children().each(function(){
            var userID = $(this).find(".hd").html() ;
            if( (personID == userID) && userID != 0){  isOk = 0 ; return false ;    }
        }) ;
        if(isOk == 0 ){  $("#tp2").html("请不要选择重复的审批人") ;   return false ;  }
        personName = personObj.attr("userName");
        curSelectObjThis.html( personName ) ;
        curSelectObjThis.attr( "roleName", roleName) ;
        curSelectObjThis.parent().siblings(".hd").html( personID ) ;
        bounce_Fixed.cancel() ;
        chargeEditOverShow();

    }else if(curSelectObjType && curSelectObjType == "payment"){
        var userID = $("#paymentID").val() ;
        if(personID == userID){  $("#tp2").html("请不要选择重复的审批人") ;   return false ;   }
        // personName = personObj.attr("userName");
        curSelectObjThis.html( personName ) ;
        curSelectObjThis.siblings("input").val( personID ) ;
        bounce_Fixed.cancel() ;
        $("#startDate2 input").val("");
        $("#startDate2").show(); startTimer(chargPayShow);

    }else if(curSelectObjType && curSelectObjType == "leave"){
        $("#leaveFlowList").children().each(function(){
            var userID = $(this).find(".hd").html() ;
            if( (personID == userID) && userID != 0){  isOk = 0 ; return false ;    }
        }) ;
        if(isOk == 0 ){  $("#tp2").html("请不要选择重复的审批人") ;   return false ;  }
        personName = personObj.attr("userName");
        curSelectObjThis.html( personName ) ;
        curSelectObjThis.data( "mobile", mobile) ;
        curSelectObjThis.data( "id", personID) ;
        bounce_Fixed.cancel() ;
    }else if(curSelectObjType && curSelectObjType == "finace"){
        $("#flowList4").children().each(function(){
            var info = $(this).find(".hd").html() ;
            info = JSON.parse(info)
            var userID = info['pID'] ;
            if( (personID == userID) && userID != 0){  isOk = 0 ; return false ;    }
        }) ;
        if(isOk == 0 ){  $("#tp2").html("请不要选择重复的审批人") ;   return false ;  }
        personName = personObj.attr("userName");
        curSelectObjThis.html( personName ) ;
        curSelectObjThis.data( "mobile", mobile) ;
        curSelectObjThis.data( "id", personID) ;
        bounce_Fixed.cancel() ;
    }else{
        $("#approveList").children().each(function(){
            var userID = $(this).children(":eq(0)").children(".hd").html() ;
            if( (personID == userID) && userID != 0){  isOk = 0 ; return false ;    }
        }) ;
        if(isOk == 0 ){  $("#tp2").html("请不要选择重复的审批人") ;   return false ;  }
        curSelectObj.html( personName ) ;
        curSelectObj.siblings(".hd").html( personID ) ;
        bounce_Fixed.cancel() ;
        if (roleName == "总经理" || roleName == "董事长") { // 选到总经理之后不再显示添加审批级别的按钮
            $("#addItem2").hide() ;
        }else{
            $("#addItem2").show() ;
        }
    }
}
// updator: 侯杏哲 2018-03-28  请假、加班、报销 - 保存更改的审批权限
function changeMyApplyAuthz(){
    var chargeStatus = $("#isCharge").val() ; // 是否需要审批
    var chargeList = [] ; // 存选择的审批人和时长
    var postData = {} ;
    var isOk = 1 ;  // 标记数据是否合法  1：合法 ； 0 ：不合法
    var levelCount = $("#approveList").children().length ;
    $("#approveList").children().each(function(){
        var len = $(this).children().length ;
        var approveJson = {} ;
        var userID = $(this).children(":eq(0)").children(".hd").html() ;
        if( userID == "" ){
            $("#beforeTip").html("请选择第"+ NumberToChinese( $(this).index()+1 ) +"级审批人") ;
            isOk = 0 ; return false ;
        }
        approveJson["userId"] = userID ;
        var secdObj = $(this).children(":eq(1)") ;
        if( len == 2 ){ // 有时间的就读取
            var hours = secdObj.children("input.con2").val() ;
            if( isNaN( Number(hours) ) || Number(hours)==0 ){
                if(curEditType["type"] == "reimburseApply"){
                    $("#beforeTip").html("请填写第"+ NumberToChinese( $(this).index()+1 ) +"级的上限金额") ;
                }else{
                    $("#beforeTip").html("请填写第"+ NumberToChinese( $(this).index()+1 ) +"级的审批时长") ;
                }
                isOk = 0 ; return false ;
            }
            approveJson["amountCeiling"] = hours ;
        }
        chargeList.push(approveJson) ;
    }) ;
    if(chargeStatus == 1 && chargeList.length <1){
        isOk = 0 ;
    }
    if(isOk == 0){ return false ;   } // 校验数据不合法， 不能提交
    var type = curEditType["type"] , changeurl = ""  , itemId = curEditType["itemId"] ;
    postData["item"] = JSON.stringify(  { "itemId": itemId , "status": chargeStatus  } ) ;
    if(type == "leave" ){
        changeurl ="../approval/leaveApprovalApply.do" ;
        postData["levelList"] =  JSON.stringify( chargeList )  ;
    }else if( type == "overTime" ){
        changeurl ="../approval/outTimeApprovalApply.do" ;
        postData["outTimeList"] = JSON.stringify( chargeList ) ;
    }else if( type == "reimburseApply"){
        changeurl = "../approval/reimburseApprovalApply.do" ;
        postData["itemId"] = itemId ;
        postData["levelList"] = JSON.stringify( chargeList )  ;

    }
    $.ajax({
        url: changeurl ,
        data: postData ,
        type:"post" ,
        dataType:"json" ,
        success:function( data ){
            var status = data["status"] ;
            if( status == 0 ){
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("传值原因导致的变更失败！") ;
            }else if( status == 1 ){
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("提交 变更申请 成功！") ;
                var obj = curEditType["obj"] ;
                if(levelCount == 0){
                    obj.parent().prev().children(":eq(0)").html( "无需审批").attr("class" , "label label-gray") ;
                    obj.parent().prev().children(":eq(1)").html(levelCount) ;
                }else{
                    obj.parent().prev().children(":eq(0)").html( NumberToChinese(levelCount) + "级审批").attr("class" , "label label-blue") ;
                    obj.parent().prev().children(":eq(1)").html(levelCount) ;
                }
            }else if( status == 2 ){
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("还有没处理完的申请！") ;
            }else if( status == 3 ){
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("有重复的审批人！") ;
            }else if( status == 4 ){
                bounce.show( $("#Tip" ) ) ;
                $("#tipMs").html("此修改申请正在申请中，不能再次提交！") ;
            }else{
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("未知情况，提交失败 ！") ;
            }
        } ,
        error:function(){}
    });

}

/*--------------- 请假 -------------*/

// creator: 李玉婷，2020-04-17 15:55:00，修改请假
function leaveEdit() {
    $(".leaveHour").show();
    $(".leaveFlowTip").hide();
    $("#leaveFlowList").html("").data("hours","[0]");
    $("#leaveEdit input").val("");
    $("#addApprover select").val("");
    $("#addApprover .chargeName").html("");
    $("#addApprover .fa").attr("class","fa fa-circle-o");
    currDayHour();
    $("#addApprover").show().siblings().hide();
    $("#leaveApplySubmit").hide();$("#leaveApplyCancel").hide();$("#leaveNext").show();
    bounce_Fixed.cancel();  bounce.show($("#leaveEdit"));
}
// creator: 李玉婷，2020-04-20 21:25:38，三选一
var regZRS = /^(0|[1-9][0-9]*|-[1-9][0-9]*)$/;
function leaveTimeSt(obj){
    var type = obj.data("type");
    $("#addApprover [require]").each(function () {
        var temp = $(this).data("type");
        if(temp != type) {
            if (temp != '3'){
                $(this).val("");
            }else{
                $("#addApprover .fa").attr("class","fa fa-circle-o");
            }
        }
    });
    if (type == 2){
        var days = $("#leaveDays").val();
        if (days >= 150) {
            obj.val("");
            $("#requireTipMsg").html("请按公司规定录入正确的天数！");
            bounce_Fixed2.show($("#limitTip"));
        }else if (days != "" && !regZRS.test(days)) {
            obj.val(regZR(days));
            $("#requireTipMsg").html("请按公司规定录入正确的天数！");
            bounce_Fixed2.show($("#limitTip"));
        }
    }else if (type == 3) {
        obj.attr("class","fa fa-dot-circle-o");
    }
}
// creator: 李玉婷，2020-04-21 15:30:12，下一步
function nextStep() {
    var fillNum = 0, type = 0;
    $("#addApprover [require]").each(function () {
        var flag = $(this).data('type');
        if(flag == '3'){
            if($(this).hasClass('fa-dot-circle-o')) {
                fillNum++;
                type = flag;
            }
        }else{
            var val = $(this).val();
            if (val != '') {
                type = flag;
                fillNum++;
            }
        }
    });
    if ($("#addApprover .chargeName").html() != '' && fillNum > 0){
        if (type == '3') {
            leaveApplyItem(type);
            $("#addApprover").hide().siblings().show();
            $("#leaveApplySubmit").show();$("#leaveApplyCancel").show();$("#leaveNext").hide();
        } else{
            var hours = JSON.parse($("#leaveFlowList").data('hours'));
            var max = Math.max.apply(null,hours);
            $("#leaveNextStep .fa").attr("class" ,"fa fa-circle-o");
            if (type == '1') {
                $("#leaveNextLevel").data("type", type);
                bounce_Fixed.show($("#leaveNextLevel"));
            } else  if (type == '2') {
                var  hur = $("#leaveDays").val() * 24;
                if (max >= hur){
                    var limit = max / 24;
                    layer.msg('请输入高于'+ limit +'天的时长');
                }else{
                    $("#leaveNextLevel").data("type", type);
                    bounce_Fixed.show($("#leaveNextLevel"));
                }
            }
        }
    } else{
        $("#requireTipMsg").html("还有必填项尚未填写！");
        bounce_Fixed2.show($("#limitTip"));
    }
}
// creator: 李玉婷，2020-04-28 12:55:21，
function cancelSure() {
    bounce.cancel();
    bounce_Fixed2.cancel();
}
// creator: 李玉婷，2020-04-20 13:37:21，下一步勾选
function setNextLevel(obj) {
    obj.find(".fa").attr("class" ,"fa fa-dot-circle-o") ;
    obj.siblings().find(".fa").attr("class" ,"fa fa-circle-o") ;
}
// creator: hxz，2020-05-08 22:20:38，财务设置下一级 取消
function cancelNext() {
    bounce_Fixed.cancel();
    $("#flowList4").children(":last").remove();
}
// creator: hxz，2020-05-08 22:20:38，财务设置下一级或者设置完毕确定
function finNextOk(){
    var forItem = $("#finNextLevel").data("forItem");
    var type = $("#finNextLevel").data("type");
    var step = $("#finNextStep").find(".fa-dot-circle-o").data('step');
    if (step) {
        if(forItem == "finace"){ // 报销
            if (step == '1'){
                $("#finPerson").html(" -- 请选择 -- ");
                $("#finPerson").data("id","");
                $("#finPerson").data("mobile","");
                $("#amountInput").val("");
                $("#anyAmount").children("i").attr("class", "fa fa-circle-o");
                $("#anyAmount").data("anyAmount", 0);
            }else{
                $("#finananceEdit .editShow").hide();
                var anyAmount = $("#anyAmount").data("anyAmount");
                if(anyAmount == 0){
                    var lastInfo = JSON.parse( $("#flowList4").children(":last").find(".hd").html());
                    var topAmount = lastInfo.amountInput ;
                    $("#endFinTip").html("职工无法提交超过"+ topAmount +"元的报销申请");
                }else{
                    $("#endFinTip").html("")
                }
                $("#nextFin").hide().siblings().hide();
                $("#applyFin").show();
                $("#cacelFin").show();
                $("#startDate3").show();
            }
        }
        bounce_Fixed.cancel();
    }else{
        layer.msg("还有必填项尚未填写！");
    }
}
// creator: 李玉婷，2020-04-20 22:20:38，设置下一级或者设置完毕确定
function leaveNextOk(){
    var type = $("#leaveNextLevel").data("type");
    var step = $("#leaveNextStep").find(".fa-dot-circle-o").data('step');
    if (step && step != undefined) {
        leaveApplyItem(type);
        if (step == '1'){
            $("#addApprover .chargeName").html("");
            $("#addApprover input").val("");
            $("#addApprover select").val("");
            $("#addApprover .fa").attr("class","fa fa-circle-o");
        }else if (step == '2') {
            var hours = JSON.parse($("#leaveFlowList").data("hours"));
            var dur = Math.max.apply(null,hours);
            if(dur < 24) {
                $("#leaveFlowDur").html(dur + '小时');
            }else{
                dur = Number(dur) / 24;
                $("#leaveFlowDur").html(dur + '天');
            }
            $("#addApprover").hide().siblings().show();
            $("#leaveApplySubmit").show();$("#leaveApplyCancel").show();$("#leaveNext").hide();
            $(".leaveFlowTip").show();
        }

        bounce_Fixed.cancel();
    }else{
        $("#requireTipMsg").html("还有必填项尚未填写！");
        bounce_Fixed2.show($("#limitTip"));
    }
}
// creator: 李玉婷，2020-05-08 08:29:15，设置当日时长
function currDayHour() {
    var hours = JSON.parse($("#leaveFlowList").data("hours"));
    var max = Math.max.apply(null,hours);
    var hourStr = "<option value='' ></option>", hourArr = [0.5,1,2,4,8,12] ;
    var index_ = hourArr.indexOf(Number(max)) ;
    for(var q = index_ + 1; q<hourArr.length; q++ ){
        hourStr += "<option value='"+ hourArr[q] +"' >"+ hourArr[q] +"</option>";
    }
    $("#leaveHour").html(hourStr);
}
// creator: 李玉婷，2020-04-21 17:44:57，输出新设置的审批流程
function leaveApplyItem(type){
    var itemStr = '';
    var dur = '';
    var lev = $("#leaveFlowList").children().length + 1;
    var userName = $("#addApprover .chargeName").html();
    var mobile = $("#addApprover .chargeName").data("mobile");
    var personID = $("#addApprover .chargeName").data("id");
    var json = {
        'toUserId': personID,
        'amountCeiling': -1,
        'level': lev,
        'userName': userName
    }
    var hours = JSON.parse($("#leaveFlowList").data("hours"));
    if (type == 1){
        dur = Number($("#leaveHour").val());
        hours.push(dur);
        hours = JSON.stringify(hours);
        $("#leaveFlowList").data("hours",hours);
        json.amountCeiling = dur;
        json = JSON.stringify(json);
        currDayHour();
        itemStr =
            "<div class='itemLe' data-flows='"+ json +"'><p>当日不高于"+ dur +"小时的请假，需"+ returnUpper(lev) +"级审批</p>";
    }else if(type == 2){
        $(".leaveHour").hide();
        var durHour = Number($("#leaveDays").val()) * 24;
        dur = Number($("#leaveDays").val());
        hours.push(dur * 24);
        hours = JSON.stringify(hours);
        $("#leaveFlowList").data("hours",hours);
        json.amountCeiling = durHour;
        json = JSON.stringify(json);
        itemStr =
            "<div class='itemLe' data-flows='"+ json +"'><p>不高于"+ dur +"天的请假，需"+ returnUpper(lev) +"级审批</p>";
    }else{
        json = JSON.stringify(json);
        if (lev >= 2){
            var max = Math.max.apply(null,hours);
            if(max >= 24) {
                max = max/24 + '天';
            }else{
                max = max + '小时';
            }
            itemStr =
                "<div class='itemLe' data-flows='"+ json +"'><p>高于"+ max +"的请假，需"+ returnUpper(lev) +"级审批</p>";
        }else{
            itemStr =
                "<div class='itemLe' data-flows='"+ json +"'><p>请假需"+ returnUpper(lev) +"级审批</p>";
        }
    }
    itemStr +=
        "<p><span>"+ returnUpper(lev) +"级审批者</span><span>"+ userName +"</span><span>"+ mobile +"</span></p>" +
        "</div>";
    $("#leaveFlowList").append(itemStr);
}
// creator: 李玉婷，2020-04-23 15:57:34，修改记录查看
function leaveUpRecord(leaveId){
    $("#approvRecord").html("") ;
    $("#leaveSetList").html("") ;
    $.ajax({
        "url":"../popedom/getItemDetail.do",
        "data":{'json':"{'itemId':"+ leaveId +"}"},
        success:function (data) {
            bounce_Fixed.show($("#leaveRecordSee")) ;
            var list = data["data"]["approvalFlows"] ;
            var approvalItem = data["data"]["approvalItem"] ;
            var process = data["data"]["approvalProcessList"] ;
            var record =
                '<div>审批记录：</div>' +
                '<div>申请人: '+ approvalItem["createName"] + ' '+ new Date(approvalItem["createDate"]).format('yyyy-MM-dd hh:mm:ss') +'</div>';
            if (process && process.length > 0){
                for(var d=0;d<process.length;d++){
                    record +=
                        '<div>审批人: '+ process[d]["userName"] + ' '+new Date(process[d]["handleTime"]).format('yyyy-MM-dd hh:mm:ss') +'</div>';
                }
            }
            $("#approvRecord").html( record ) ;
            if( approvalItem["status"] == 1){ // 需要审批
                var str = "" ;
                if(list && list.length > 0){
                    str = leaveLevenStr(list);
                } else{
                    str = "无需审批" ;
                }
                $("#leaveSetList").html( str ) ;
            } else{ // 不需要审批
                $("#leaveSetList").html( "不需要审批" ) ;
            }
        }
    })
}
// creator: 李玉婷，2020-04-27 15:14:33，请假查看--审批流程输出
function leaveLevenStr(list) {
    var str = '', enable = 0;
    for(var i = 0 ; i < list.length ; i++){
        var userName = list[i]["userName"], amountCeiling = list[i]["amountCeiling"];
        if(list[i]["toUserId"] === 0){ userName = "直接上级"; }
        var lev = returnUpper(i + 1) + "级审批";
        if (amountCeiling > 0 && amountCeiling <24){
            str += "<div class='itemLe'><p>当日不高于"+ amountCeiling +"小时的请假，需"+ lev +"</p>";
        }else if(amountCeiling >= 24 && amountCeiling < 3600){
            amountCeiling = amountCeiling/24;
            str += "<div class='itemLe'><p>不高于"+ amountCeiling +"天的请假，需"+ lev +"</p>";
        }else{
            enable++;
            if (i==0 && amountCeiling == -1){
                str += "<div class='itemLe'><p>请假需"+ lev +"</p>";
            }else{
                amountCeiling = list[i-1]["amountCeiling"];
                if (amountCeiling < 24){
                    str += "<div class='itemLe'><p>高于"+ amountCeiling +"小时的请假，需"+ lev +"</p>";
                    $("#maxHur").html(amountCeiling +"小时");
                } else{
                    amountCeiling = amountCeiling/24;
                    str += "<div class='itemLe'><p>高于"+ amountCeiling +"天的请假，需"+ lev +"</p>";
                    $("#maxHur").html(amountCeiling +"天");
                }
            }
        }
        str += "<p><span>"+ lev + "者</span><span>"+ userName +"</span><span>"+ (list[i]["mobile"]||"") +"</span></p></div>";
    }
    if (enable > 0){
        $(".leaveSeeTip").hide();
    }else{
        var hurTemp = list[list.length-1]["amountCeiling"];
        if (hurTemp < 24){
            $(".maxHur").html(hurTemp +"小时");
        } else {
            hurTemp = Number(hurTemp)/24;
            $(".maxHur").html(hurTemp +"天");
        }
        $(".leaveSeeTip").show();
    }
    return str;
}
// creator: 李玉婷，2020-04-27 21:44:15，
function regZR(value) {
    var str = value;
    str = str.replace(/[^\d]/g,"");  //清除“数字”和“.”以外的字符
    str = str.replace(/^0/g,"");  //验证第一个字符不是零
    str = str.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的.
    str = str.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    return str;
}
// creater  侯杏哲  2018/4/9  工具方法， 传入数字， 返回汉字
function returnUpper( num){
    switch (num){
        case 1:
        case "1": return "一" ; break ;
        case 2:
        case "2": return "二" ; break ;
        case 3:
        case "3": return "三" ; break ;
        case 4:
        case "4": return "四" ; break ;
        case 5:
        case "5": return "五" ; break ;
        case 6:
        case "6": return "六" ; break ;
        case 7:
        case "7": return "七" ; break ;
        case 8:
        case "8": return "八" ; break ;
        case 9:
        case "9": return "九" ; break ;
        default : break ;
    }
}
// creator: 李玉婷，2021-10-09 09:26:44，弹窗名称
function returnHeadTtl( num){
    switch (num){
        case 1:
        case "1": return "一" ; break ;
        case 2:
        case "2": return "二" ; break ;
        case 3:
        case "3": return "三" ; break ;
        case 4:
        case "4": return "四" ; break ;
        case 5:
        case "5": return "五" ; break ;
        case 6:
        case "6": return "六" ; break ;
        case 7:
        case "7": return "七" ; break ;
        case 8:
        case "8": return "八" ; break ;
        case 9:
        case "9": return "九" ; break ;
        default : break ;
    }
}

// creator: 张旭博，2022/4/6 8:36，wonderss的加班功能 - 按钮
function wonderssOverTimeTips() {
    bounce_Fixed2.show($("#wonderssOverTimeTips"))
}

// creator: 张旭博，2022/4/6 8:36，wonderss的请假功能 - 按钮
function wonderssLeaveTips() {
    bounce_Fixed2.show($("#wonderssLeaveTips"))
}

// creator: 张旭博，2024-02-05 08:49:17， 仓库模式的说明
function stockModeDes() {
    bounce_Fixed2.show($("#stockModeDes"))
}

// creator: 张旭博，2022/4/15 10:05，修改补报加班功能状态 - 按钮
function changeOverTimeApplyAdvanceTime() {
    var id = $("#overTime .upperLimit").attr("data-id")
    var ruleTime = $("#overTime .upperLimit").html()
    bounce_Fixed2.show($("#overTimeApplyAdvanceTime"))
    $("#overTimeApplyAdvanceTime [name='ruleTime']").val(ruleTime)
    $("#overTimeApplyAdvanceTime .sureBtn").unbind().on("click", function(){
        updateRule('overTime', id)
    })
}

// creator: 张旭博，2022/4/15 10:05，修改事后补假功能状态 - 按钮
function changeLeaveApplyAdvanceTime() {
    var id = $("#leave .upperLimit").attr("data-id")
    var ruleTime = $("#leave .upperLimit").html()
    bounce_Fixed2.show($("#leaveApplyAdvanceTime"))
    $("#leaveApplyAdvanceTime [name='ruleTime']").val(ruleTime)
    $("#leaveApplyAdvanceTime .sureBtn").unbind().on("click", function(){
        updateRule('leave', id)
    })
}

// creator: 张旭博，2022/4/15 9:45，加班/请假时间限制（提前量）修改
function updateRule(type, id) {
    if (type === 'overTime') {
        var ruleTime = $("#overTimeApplyAdvanceTime [name='ruleTime']").val()
    } else if (type === 'leave') {
        var ruleTime = $("#leaveApplyAdvanceTime [name='ruleTime']").val()
    }

    $.ajax({
        url: '../popedom/updateRule.do',
        data: {
            itemId: id,
            ruleTime: ruleTime
        },
        success: function(res) {
            var data = res.data
            var status = data.status
            if (status === 1) {
                layer.msg("修改成功")
                bounce_Fixed2.cancel()
                $("#" + type).find(".upperLimit").html(ruleTime)
            } else if (status === 0) {
                layer.msg("修改失败")
            } else if (status === 2) {
                layer.msg("值未改变")
            }
        }
    })
}

// creator: 张旭博，2022/4/15 9:32，修改补报加班状态 - 按钮
function changeRepayOverTimeState() {
    var id = $("#overTime .state_makeAfterFact").attr("data-id")
    var state = $("#overTime .state_makeAfterFact").attr("state")
    var newState = state === 'true'?0:1
    var str =   ''
    if (newState === 1){
        str =   '<p>未提前申请的加班如确需计入考勤，可以去修改考勤。<br>' +
                '开启“补报加班”功能，可能带来管理方面的新问题。<br>' +
                '不开启该功能，需要时去修改考勤，同样可达到目的。</p>' +
                '<p class="text-center">确定开启该功能吗？</p>'
    } else {
        str = '<p class="text-center">确定关闭该功能吗？</p>'
    }
    bounce_Fixed2.show($("#bounceFixed2_tip"))
    $("#bounceFixed2_tip .tipCon").html(str)
    $("#bounceFixed2_tip .sureBtn").unbind().on("click", function() {
        updateSupplementary('overTime', id, newState)
    })
}

// creator: 张旭博，2023-04-03 11:49:24， 修改提交实际加班数据的规则
function changeFactApplyDurationRule() {
    bounce_Fixed2.show($("#changeFactApplyDurationRule"))
    $("#changeFactApplyDurationRule [name='applyDuration']").prop("disabled", false).val("3")
    $("#anyTimeApply").prop("checked",false)
}

// creator: 张旭博，2023-04-04 08:15:45， 修改提交实际加班数据的规则 - 确认
function sureChangeRule() {
    var id = $("#overTime .state_factDurationRule").data('id')
    var ruleTime = $("#changeFactApplyDurationRule [name='applyDuration']").val()
    var isChecked = $("#anyTimeApply").prop("checked")
    if (isChecked) {
        ruleTime = -1
    }
    $.ajax({
        url: $.webRoot+'/popedom/updateRule.do',
        data: {
            itemId: id, // 规则id，非加班审批事项id
            ruleTime: ruleTime
        }
    }).then(res => {
        var data = res.data
        var status = data.status
        if (status === 1) {
            layer.msg("操作成功")
            bounce_Fixed2.cancel()
            $("#overTime .state_factDurationRule").html(ruleTime===-1?'--':ruleTime)
        } else if (status === 2) {
            layer.msg("值未改动")
        } else {
            layer.msg("操作失败")
        }
    })
}

// creator: 张旭博，2022/4/15 9:32，修改事后补假状态 - 按钮
function changeRepayLeaveState() {
    var id = $("#leave .state_makeAfterFact").attr("data-id")
    var state = $("#leave .state_makeAfterFact").attr("state")
    var newState = state === 'true'?0:1

    var str = ''
    if (newState === 1){
        str =   '未提前申请的请假考勤中可能已被记为旷工。<br>' +
            '如需将该旷工改为请假，可以去修改考勤。<br>' +
            '开启“事后补假”功能，可能带来管理方面的新问题。<br>' +
            '不开启该功能，需要时去修改考勤，同样可达到目的。</p>' +
            '<p class="text-center">确定开启该功能吗？</p>'
    } else {
        str = '<p class="text-center">确定关闭该功能吗？</p>'
    }
    bounce_Fixed2.show($("#bounceFixed2_tip"))
    $("#bounceFixed2_tip .tipCon").html(str)
    $("#bounceFixed2_tip .sureBtn").unbind().on("click", function() {
        updateSupplementary('leave', id ,newState)
    })
}

// creator: 张旭博，2022/4/15 10:30，加班/请假补报修改
function updateSupplementary(type, id, newState){
    $.ajax({
        url: '../popedom/updateSupplementary.do',
        data: {
            itemId: id,
            enableType: newState
        },
        success: function(res) {
            var data = res.data
            var status = data.status
            if (status === 1) {
                layer.msg("修改成功")
                bounce_Fixed2.cancel()
                var str = chargeOpenState(newState) // “事后补假”的功能 是否开启
                $("#"+type).find(".state_makeAfterFact").html(str)
                $("#"+type).find(".state_makeAfterFact").attr("state", newState===1)
            } else if (status === 0) {
                layer.msg("修改失败")
            } else if (status === 2) {
                layer.msg("值未改变")
            }
        }
    })
}

// creator: 张旭博，2022-10-26 04:13:46， 采购审批设置 - 修改
function changeApprove(type) {
    bounce_Fixed.show($("#purchaseApprovalSettingsChange"))
    $("#purchaseApprovalSettingsChange").data('status', type)

    if(type === 1) {
        // 其他修改
        $(".page_needApprove").show().siblings().hide()
        $(".page_needApprove").find(".flows").html("")
        $("#purchaseApprovalSettingsChange").find("input").val("")
        $("#purchaseApprovalSettingsChange").find(".extraTip").hide()
        $.ajax({
            url: "../popedom/getOptionalUser.do" ,
            data: {userId: ''},
            success:function( res ){
                var data = res.data
                var users = data.users || []
                var userArr = users.filter(item => item.userID > 0)
                $("#purchaseApprovalSettingsChange").data("users", userArr)
                addOneLevel()
            }
        }) ;
    } else {
        $("#purchaseApprovalSettingsChange").find("input").val("")
        $("#purchaseApprovalSettingsChange").find(".extraTip").show()
        $(".page_noNeedApprove").show().siblings().hide()
    }
}

// creator: 张旭博，2022-11-22 09:45:55， 采购审批设置 - 提交
function sureChangeApprove() {
    var dialog = $("#purchaseApprovalSettingsChange")
    var status = Number(dialog.data('status'))
    var openDate =  dialog.find("[name='openDate']").val()

    if (status === 1) {
        var lastLevel =  dialog.find(".flows").children(":last-child").attr("level")
        var userList = []
        var state = 0
        dialog.find(".flows .item").each(function () {
            var userId = $(this).find("select").val()
            var level = $(this).attr("level")
            var users = $(this).find("select option:selected").html()
            var toUser = $(this).find("select option:selected").attr("name")
            var userArr = users.split(" ")
            var userName = userArr[0]
            var mobile = userArr[1]
            var userItem = {
                level: level,
                toUser: toUser,
                userName : userName ,
                mobile: mobile,
                toUserId: userId
            }
            userList.push(userItem)
            if (userId === '') {
                state ++
            }
        })
        var userIds = userList.map(item => item.toUserId)
        var userIdsNoRepeat = [...new Set(userIds)]
        if (state > 0) {
            layer.msg("还有审批者未选择")
            return false
        }
        if (userIds.length !== userIdsNoRepeat.length) {
            layer.msg("请勿重复选择")
            return false
        }
        var data = {
            id: curEditType["itemId"], // 项目id
            status: status, // 0-不需要审批,1-需要审批
            level: lastLevel, // 层级 审批总层级
            openDate: openDate, //生效时间
            userList: userList //审批人
        }
    } else {
        var data = {
            id: curEditType["itemId"], // 项目id
            status: status, // 0-不需要审批,1-需要审批
            level: '', // 层级 审批总层级
            openDate: openDate, //生效时间
            userList: [] //审批人
        }
    }
    if ($.trim(openDate) === '') {
        layer.msg("请选择本次修改的拟生效时间！")
        return false
    }
    $.ajax({
        url: '../purchaseApproval/purchaseApprovalApply.do',
        data: {
            json: JSON.stringify(data)
        },
        success: function(res) {
            var data = res.success
            var message = res.message
            if (data === 1) {
                layer.msg("操作成功")
                bounce.cancel()
                bounce_Fixed.cancel()
                getMainList()
            } else {
                if (res.error) {
                    if (res.error.message) {
                        layer.msg(res.error.message)
                    } else {
                        layer.msg("操作失败")
                    }
                } else {
                    layer.msg("操作失败")
                }

            }
        }
    })
}

// creator: 张旭博，2022-11-22 10:54:21， 新增一层
function addOneLevel(obj) {
    var selector, level = 0
    if (obj) {
        selector = obj.parents(".bonceContainer")
        var lastLevel = selector.find(".flows").children(":last-child").attr("level")
        level = Number(lastLevel) + 1
    } else {
        selector = $("#purchaseApprovalSettingsChange")
        level = 1
    }
    if (level > 9){
        layer.msg("已达到最大层数！")
        return false
    }

    if (obj) {
        selector.find(".flows").children(":last-child").find(".delBtn").remove()
    }


    var optionStr = '<option value="">请选择</option>'
    var delStr = '<button class="link-red ty-right delBtn" onclick="delThisLevel($(this))">删除本层审批</button>'
    var users = $("#purchaseApprovalSettingsChange").data("users")
    for (var i in users) {
        optionStr += '<option value="'+users[i].userID+'" name="'+users[i].roleName+'">'+users[i].userName+ ' ' + users[i].mobile+'</option>'
    }
    if (level === 1) {
        delStr = ''
    }
    var str =   '<div class="item" level="'+level+'">' +
                '    <div class="item-title">'+NumberToChinese(level)+'级审批者'+delStr+'</div>' +
                '    <select class="ty-inputSelect">'+optionStr+'</select>' +
                '</div>'
    selector.find(".flows").append(str)

}

// creator: 张旭博，2022-11-22 01:29:57， 删除本层审批
function delThisLevel(obj) {
    var selector = obj.parents(".item")
    var lastLevel = selector.attr("level")
    if (lastLevel > 2) {
        selector.prev(".item").find(".item-title").append('<button class="link-red ty-right delBtn" onclick="delThisLevel($(this))">删除本层审批</button>')
    }
    selector.remove()
}

// creator: 张旭博，2024-02-05 09:11:58， 仓库模式修改 - 确定
function sureChangeMode() {
    $.ajax({
        url: $.webRoot + '',
        data: {

        }
    }).then(res => {
        let state = res.data
        if (state === 1) {
            layer.msg("操作成功！")
        }
    })
}

function chengPinKu_set() {
    bounce_Fixed.show($("#chengPinKu_set"))
    $("#chengPinKu_set .effectTime input").val('')
    $("#chengPinKu_set .effectTime").hide()
    $("#chengPinKu_set .bonceFoot button").hide()
    $("#chengPinKu_set input:radio").prop("checked", false)
    $("#chengPinKu_set .changeDot").unbind().on('click', function () {
        let radio = $("#chengPinKu_set input:radio:checked").val()
        if (radio) {
            $("#chengPinKu_set .effectTime").show()
        } else {
            $("#chengPinKu_set .effectTime input").val('')
            $("#chengPinKu_set .effectTime").hide()
        }
    })
    $("#chengPinKu_set").everyTime('0.5s', 'footShow', function(){
        let effectTime = $("#chengPinKu_set .effectTime input").val()
        if (effectTime) {
            $("#chengPinKu_set .bonceFoot button").show()
        } else {
            $("#chengPinKu_set .bonceFoot button").hide()
        }
    })
}

function inStockChecker_set() {
    bounce_Fixed.show($("#inStockChecker_set"))
    $("#inStockChecker_set .effectTime input").val('')
    $("#inStockChecker_set .effectTime").hide()
    $("#inStockChecker_set .bonceFoot button").hide()
    $("#inStockChecker_set input:radio").prop("checked", false)
    $("#inStockChecker_set .changeDot").unbind().on('click', function () {
        let radio = $("#inStockChecker_set input:radio:checked").val()
        if (radio) {
            $("#inStockChecker_set .effectTime").show()
        } else {
            $("#inStockChecker_set .effectTime input").val('')
            $("#inStockChecker_set .effectTime").hide()
        }
    })
    $("#inStockChecker_set").everyTime('0.5s', 'footShow', function(){
        let effectTime = $("#inStockChecker_set .effectTime input").val()
        if (effectTime) {
            $("#inStockChecker_set .bonceFoot button").show()
        } else {
            $("#inStockChecker_set .bonceFoot button").hide()
        }
    })
}

function productLogisticsCheck_set() {
    bounce_Fixed.show($("#productLogisticsCheck_set"))
    $("#productLogisticsCheck_set .effectTime input").val('')
    $("#productLogisticsCheck_set .effectTime").hide()
    $("#productLogisticsCheck_set .bonceFoot button").hide()
    $("#productLogisticsCheck_set input:radio").prop("checked", false)
    $("#productLogisticsCheck_set .changeDot").unbind().on('click', function () {
        let radio = $("#productLogisticsCheck_set input:radio:checked").val()
        if (radio) {
            $("#productLogisticsCheck_set .effectTime").show()
        } else {
            $("#productLogisticsCheck_set .effectTime input").val('')
            $("#productLogisticsCheck_set .effectTime").hide()
        }
    })
    $("#productLogisticsCheck_set").everyTime('0.5s', 'footShow2', function(){
        let effectTime = $("#productLogisticsCheck_set .effectTime input").val()
        if (effectTime) {
            $("#productLogisticsCheck_set .bonceFoot button").show()
        } else {
            $("#productLogisticsCheck_set .bonceFoot button").hide()
        }
    })
}

//  creator : 张旭博  2017-05-15  我的请求工具方法 - 阿拉伯数字转中文数字
var chnNumChar = ["零","一","二","三","四","五","六","七","八","九"];  // 单个数字转换用数组实现
var chnUnitSection = ["","万","亿","万亿","亿亿"];                    // 节权位同样用数组实现
var chnUnitChar = ["","十","百","千"];                               // 节内权位同样用数组实现
// creator : 张旭博  2017-05-15  我的请求工具方法 - 节内转换算法：
function SectionToChinese(section){
    var strIns = '', chnStr = '';
    var unitPos = 0;
    var zero = true;
    while(section > 0){
        var v = section % 10;
        if(v === 0){
            if(!zero){
                zero = true;
                chnStr = chnNumChar[v] + chnStr;
            }
        }else{
            zero = false;
            strIns = chnNumChar[v];
            strIns += chnUnitChar[unitPos];
            chnStr = strIns + chnStr;
        }
        unitPos++;
        section = Math.floor(section / 10);
    }
    return chnStr;
}
//   creator : 张旭博  2017-05-15  我的请求工具方法 -   转换算法主函数
function NumberToChinese(num){
    var unitPos = 0;
    var strIns = '', chnStr = '';
    var needZero = false;
    if(num === 0){
        return chnNumChar[0];
    }
    while(num > 0){
        var section = num % 10000;
        if(needZero){
            chnStr = chnNumChar[0] + chnStr;
        }
        strIns = SectionToChinese(section);
        strIns += (section !== 0) ? chnUnitSection[unitPos] : chnUnitSection[0];
        chnStr = strIns + chnStr;
        needZero = (section < 1000) && (section > 0);
        num = Math.floor(num / 10000);
        unitPos++;
    }

    return chnStr;
}

function chargeOpenState(state) {
    return state === 1?'开启':'关闭'
}

// creator: 张旭博，2022-10-12 11:30:27，
function UpperLevel(level) {
    var sta = "";
    switch (Number(level)){
        case 1:
            sta = "需一级审批";break;
        case 2:
            sta = "需二级审批";break;
        case 3:
            sta = "需三级审批";break;
        case 4:
            sta = "需四级审批";break;
        case 5:
            sta = "需五级审批";break;
        case 6:
            sta = "需六级审批";break;
        case 7:
            sta = "需七级审批";break;
        case 8:
            sta = "需八级审批";break;
        default: sta = "无需审批";
    }
    return sta ;
}

// 时间控件的初始化
laydate.render({elem: '#timesInput'});

laydate.render({elem: '#startDate input'});
laydate.render({elem: '#startDate2 input' });
laydate.render({elem: '#leaveStartDate' });
laydate.render({elem: '#finStartDate' });
laydate.render({elem: '#openDate', min: 1 });
laydate.render({elem: '#stockModeChangeDate', min: 1 });
laydate.render({elem: '#pt_startDate input', min: 1 });
laydate.render({elem: '#effectTime_chengPinKu', min: 1});
laydate.render({elem: '#effectTime_inStockChecker', min: 1});
laydate.render({elem: '#effectTime_productLogisticsCheck', min: 1});
