$(function(){
    /* 筛选 */
    $("#Sizer").click(function(){
        getList() ;
        setBiao()
    }) ;
    /* 搜索 */
    $("#search").click(function(){
        getList() ;
        setBiao()
    }) ;
    /*  creater  姚宗涛  2018/3/15  10:15:07  */
    $(".ty-secondTab li").click(function(){
        $(this).addClass("ty-active").siblings().removeClass("ty-active") ;
        $(".ty-mainData").eq($(this).index()).removeClass("hd").siblings().addClass("hd") ;
        curType = $(this).index() ;
        getList() ;
        setBiao()
    }) ;
    getList(0) ;
    $(window).resize(function(){
        setBiao();
    });
}) ;
// creator : 侯杏哲 2018-06-07  设置表头大小
function setBiao() {
    setTimeout(function(){
        if(curType == 1){
            var w = $("#biao").width() +2 ;
            var h = $("#biao").height()+2 ;
            $(".biaotou").css({ "border-left": w + "px rgba(200,200,200,0.5) solid" , "width": w + "px" , "border-top": h + "px rgba(0,0,0,0) solid" }) ;
        }
    },500) ;

}
/*  updator hxz  2018-5-12  */
var popmList = [] ; // 存放全部的菜单
function look(type , userName , _this){
    var trObj = _this.parent().parent() ;
    var authList = trObj.attr("authinfo") ;
    authList = JSON.parse(authList) ;
    var str = "" ;
    $("#detailsName").html(userName) ;
    var pidInfo = getInfoByMid(type , popmList) ; // 一级菜单的信息
    $("#detailsModel").html(pidInfo["name"]) ;
    $("#ttl_popm").html(""); $("#check_popm").html("") ;
    var modelList = getModelpopmByPid(type , popmList) ; // 根据一级菜单找二级菜单
    if(modelList && modelList.length > 0){
        for(var i = 0 ; i < modelList.length ; i++){
            var mid = modelList[i]["mid"] ;
            var _bool = getInfoByMid(mid , authList) ;
            if(_bool){ // 有该权限
                var str1 = "<td>"+ modelList[i]["name"] +"</td>" ;
                var str2 = "<td><i class='fa fa-check'></i></td>" ;
                $("#ttl_popm").append(str1) ; $("#check_popm").append(str2) ;
            }

        }
    }
    bounce.show($("#details"));
}
/* creator : 侯杏哲 2018-04-04  获取列表 */
var curType = 0 ;
function getList() { // type : 1 - 审批设置查看 ; 0 - 当前权限查看
    var departName = $("#departName").val() ;
    var userName = $("#userName").val() ;
    if(curType == 0){
        $.ajax({
            "url": "../approval/userPopedom.do" ,
            "data" :{ "userName": userName , "departName": departName } ,
            success:function (res) {
                var userList = res["users"] ;
                popmList = res["popedoms"] ; // 该机构拥有的全部模块
                var colNum = popmList.length ; $("#colspan").attr("colspan" , colNum) ;
                var w = 100*colNum ; $("#auth_1_21").css("width" , w+"px") ;
                var bool_e = charFirstPopm("e" , popmList) ;   var str_e = "<td>权限管理</td>" ;if(!bool_e){ str_e = "" ; }
                var bool_k = charFirstPopm("k" , popmList) ;   var str_k = "<td>总务管理</td>" ;if(!bool_k){ str_k = "" ; }
                var bool_l = charFirstPopm("l" , popmList) ;   var str_l = "<td>财务管理</td>" ;if(!bool_l){ str_l = "" ; }
                var bool_q = charFirstPopm("q" , popmList) ;   var str_q = "<td>销售管理</td>" ;if(!bool_q){ str_q = "" ; }
                var bool_u = charFirstPopm("u" , popmList) ;   var str_u = "<td>生产管理</td>" ;if(!bool_u){ str_u = "" ; }
                var bool_p = charFirstPopm("p" , popmList) ;   var str_p = "<td>商品管理</td>" ;if(!bool_p){ str_p= "" ; }
                var bool_o = charFirstPopm("o" , popmList) ;   var str_o = "<td>物料管理</td>" ;if(!bool_o){ str_o = "" ; }
                var bool_t = charFirstPopm("t" , popmList) ;   var str_t = "<td>项目管理</td>" ;if(!bool_t){ str_t = "" ; }
                var bool_r = charFirstPopm("r" , popmList) ;   var str_r = "<td>文件与资料</td>" ;if(!bool_r){ str_r = "" ; }
                var bool_m = charFirstPopm("m" , popmList) ;   var str_m = "<td>个人中心</td>" ;if(!bool_m){ str_m = "" ; }
                var bool_h = charFirstPopm("h" , popmList) ;   var str_h = "<td>持续改进</td>" ;if(!bool_h){ str_h = "" ; }
                var bool_j = charFirstPopm("j" , popmList) ;   var str_j = "<td>备忘与日程</td>" ;if(!bool_j){ str_j = "" ; }
                var bool_mb = charFirstPopm("mb" , popmList, 1) ;   var str_mb = "<td>高管管理</td>" ;  if(!bool_mb){ str_mb = "" ; }
                var bool_db = charFirstPopm("db" , popmList, 1) ;   var str_db = "<td>日常事务</td>" ;  if(!bool_db){ str_db = "" ; }
                var bool_fc = charFirstPopm("fc" , popmList, 1) ;   var str_fc = "<td>投诉录入</td>" ;  if(!bool_fc){ str_fc = "" ; }
                var bool_gb = charFirstPopm("gb" , popmList, 1) ;   var str_gb = "<td>回款录入</td>" ;  if(!bool_gb){ str_gb = "" ; }
                var bool_lt = charFirstPopm("lt" , popmList, 1) ;   var str_lt = "<td>常规借款</td>" ;  if(!bool_lt){ str_lt = "" ; }
                var bool_n = charFirstPopm("n" , popmList) ;   var str_n = "<td>参考资料</td>" ;if(!bool_n){ str_n = "" ; }
                var bool_a = charFirstPopm("a" , popmList) ;   var str_a = "<td>薪资宝</td>" ;if(!bool_a){ str_a = "" ; }
                var bool_fb = charFirstPopm("fb" , popmList , 1) ;   var str_fb = "<td>投诉管理</td>" ;if(!bool_fb){ str_fb = "" ; }
                var bool_g = charFirstPopm("g" , popmList) ;   var str_g = "<td>汽车管理</td>" ;if(!bool_g){ str_g = "" ; }
                var bool_s = charFirstPopm("s" , popmList) ;   var str_s = "<td>会计管理</td>" ;if(!bool_s){ str_s = "" ; }
                var bool_v = charFirstPopm("v" , popmList) ;   var str_v = "<td>质量管理</td>" ;if(!bool_v){ str_v = "" ; }
                var bool_w = charFirstPopm("w" , popmList) ;   var str_w = "<td>技术管理</td>" ;if(!bool_w){ str_w = "" ; }
                var bool_x = charFirstPopm("x" , popmList) ;   var str_x = "<td>仓库管理</td>" ;if(!bool_x){ str_x = "" ; }
                var bool_y = charFirstPopm("y" , popmList) ;   var str_y = "<td>物流管理</td>" ;if(!bool_y){ str_y = "" ; }

                var modelStr = str_e + str_k + str_l + str_q + str_u + str_p + str_o + str_t + str_r + str_m +
                    str_h + str_j + str_mb + str_db + str_fc + str_gb + str_lt + str_n +
                    str_a + str_fb + str_g + str_s + str_v+ str_w+ str_x+str_y ;
                $("#modelTTl_2").html(modelStr) ;
                var str = "" , str1 = "";
                if(userList && userList.length > 0){
                    //  e-权限管理；k-总务管理；l-财务管理；q-销售管理；u-生产管理；p-商品管理；o-物料管理；t-项目管理；r-资源中心；m-个人中心；n-关于
                    for (var i = 0 ; i < userList.length ; i++){
                        var authList = userList[i]["userPopedomSet"] ;
                        var str_ea = "" ,  str_ka = "" , str_la = "" , str_qa = "" , str_ua = "" , str_pa = "" , str_oa = "" , str_ta = "" , str_ra = "" ,  str_ma = "" ,  str_na = "" ,
                            str_aa = "" , str_fb_ = "" ,str_ga = "" , str_sa = "" , str_va = "" , str_wa = "" , str_xa = "" , str_ya = ""
                            , str_ha = "" , str_ja = "" , str_mb_ = "" , str_db_ = "" , str_fc_ = "" , str_gb_ = "" , str_lt_ = "" ;
                        var bool_ea = charFirstPopm("e" , authList) ; if(bool_e){ if(bool_ea){ str_ea = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ea\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ea = "<td></td>" ; } }
                        var bool_ka = charFirstPopm("k" , authList) ; if(bool_k){ if(bool_ka){ str_ka = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ka\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ka = "<td></td>" ; } }
                        var bool_la = charFirstPopm("l" , authList) ; if(bool_l){ if(bool_la){ str_la = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"la\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_la = "<td></td>" ; } }
                        var bool_qa = charFirstPopm("q" , authList) ; if(bool_q){ if(bool_qa){ str_qa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"qa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_qa = "<td></td>" ; } }
                        var bool_ua = charFirstPopm("u" , authList) ; if(bool_u){ if(bool_ua){ str_ua = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ua\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ua = "<td></td>" ; } }
                        var bool_pa = charFirstPopm("p" , authList) ; if(bool_p){ if(bool_pa){ str_pa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"pa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_pa = "<td></td>" ; } }
                        var bool_oa = charFirstPopm("o" , authList) ; if(bool_o){ if(bool_oa){ str_oa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"oa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_oa = "<td></td>" ; } }
                        var bool_ta = charFirstPopm("t" , authList) ; if(bool_t){ if(bool_ta){ str_ta = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ta\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ta = "<td></td>" ; } }
                        var bool_ra = charFirstPopm("r" , authList) ; if(bool_r){ if(bool_ra){ str_ra = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ra\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ra = "<td></td>" ; } }
                        var bool_ma = charFirstPopm("m" , authList) ; if(bool_m){ if(bool_ma){ str_ma = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ma\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ma = "<td></td>" ; } }

                        var bool_ha = charFirstPopm("h" , authList) ; if(bool_h){ if(bool_ha){ str_ha = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ma\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ha = "<td></td>" ; } }
                        var bool_ja = charFirstPopm("j" , authList) ; if(bool_j){ if(bool_ja){ str_ja = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ma\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ja = "<td></td>" ; } }
                        var bool_mb_ = charFirstPopm("mb" , authList , 1) ; if(bool_mb){ if(bool_mb_){ str_mb_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_mb_ = "<td></td>" ; } }
                        var bool_db_ = charFirstPopm("db" , authList , 1) ; if(bool_db){ if(bool_db_){ str_db_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_db_ = "<td></td>" ; } }
                        var bool_fc_ = charFirstPopm("fc" , authList , 1) ; if(bool_fc){ if(bool_fc_){ str_fc_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_fc_ = "<td></td>" ; } }
                        var bool_gb_ = charFirstPopm("gb" , authList , 1) ; if(bool_gb){ if(bool_gb_){ str_gb_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_gb_ = "<td></td>" ; } }
                        var bool_lt_ = charFirstPopm("lt" , authList , 1) ; if(bool_lt){ if(bool_lt_){ str_lt_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_lt_ = "<td></td>" ; } }

                        var bool_na = charFirstPopm("n" , authList) ; if(bool_n){ if(bool_na){ str_na = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"na\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_na = "<td></td>" ; } }
                        var bool_aa = charFirstPopm("a" , authList) ; if(bool_a){ if(bool_aa){ str_aa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ar\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_aa = "<td></td>" ; } }
                        var bool_fb_ = charFirstPopm("fb" , authList , 1) ; if(bool_fb){ if(bool_fb_){ str_fb_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_fa = "<td></td>" ; } }
                        var bool_ga = charFirstPopm("g" , authList) ; if(bool_g){ if(bool_ga){ str_ga = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ga\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ga = "<td></td>" ; } }
                        var bool_sa = charFirstPopm("s" , authList) ; if(bool_s){ if(bool_sa){ str_sa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"sa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_sa = "<td></td>" ; } }
                        var bool_va = charFirstPopm("v" , authList) ; if(bool_v){ if(bool_va){ str_va = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"va\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_va = "<td></td>" ; } }
                        var bool_wa = charFirstPopm("w" , authList) ; if(bool_w){ if(bool_wa){ str_wa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"wa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_wa = "<td></td>" ; } }
                        var bool_xa = charFirstPopm("x" , authList) ; if(bool_x){ if(bool_xa){ str_xa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"xa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_xa = "<td></td>" ; } }
                        var bool_ya = charFirstPopm("y" , authList) ; if(bool_y){ if(bool_ya){ str_ya = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ya\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ya = "<td></td>" ; } }

                        str1 += " <tr>" +
                            "<td>"+ userList[i]["departName"]+"</td>" +
                            "<td>"+ userList[i]["userName"] +"</td>" +
                            "</tr>" ;
                        str += " <tr authinfo = '"+ JSON.stringify(authList) +"'>" +
                            str_ea + str_ka + str_la + str_qa + str_ua + str_pa + str_oa + str_ta + str_ra + str_ma +
                            str_ha + str_ja + str_mb_ + str_db_ + str_fc_ + str_gb_ + str_lt_ +
                            str_na + str_aa +str_fb_ +str_ga+ str_sa + str_va + str_wa +str_xa + str_ya +
                        "</tr>"
                    }
                }
                $("#authList_1_1").children(":gt(0)").remove(); $("#authList_1_1").append(str1) ;
                $("#authList_1_2").children(":gt(1)").remove(); $("#authList_1_2").append(str) ;
            }
        }) ;
    }else{
        $.ajax({
            "url": "../approval/userApproval.do" ,
            "data" :{ "userName": userName , "departName": departName } ,
            success:function (res) {
                var list = res["data"] ;
                var str = "" ;
                if(list && list.length > 0){
                    for(var i = 0 ; i < list.length ; i++){
                        var authInfo = list[i]["approvalMap"] ;
                        str += "<tr>" +
                            "<td>"+ (list[i]["departName"] || "") +"</td>" +
                            "<td>"+ list[i]["userName"] +"</td>" ;
                        if(authInfo["outTimeApproval"]){ str += "<td>"+ authInfo["outTimeApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 加班申请
                        if(authInfo["leaveApproval"]){ str += "<td>"+ authInfo["leaveApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 请假申请
                        if(authInfo["reimburseApproval"]){ str += "<td>"+ authInfo["reimburseApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 报销申请
                        str += "<td></td>" ; // 职工档案修改
                        str += "<td></td>" ; // 岗位设置修改
                        if(authInfo["approvalApproval"]){ str += "<td>"+ authInfo["approvalApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 审批设置修改
                        if(authInfo["projectApproval"]){ str += "<td>"+ authInfo["projectApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 新项目立项
                        if(authInfo["projectDevelopment"]){ str += "<td>"+ authInfo["projectDevelopment"] +"</td>" ; }else{ str += "<td></td>" ; } // 新项目开发
                        str += "</tr>" ;
                    }
                }
                $("#authList_2").children(":gt(0)").remove() ;
                $("#authList_2").append(str) ;
            }
        }) ;
    }

}
/* creator : 侯杏哲 2018-04-09 工具方法- 根据 pid 值的 获取该模块全部子菜单  */
function getModelpopmByPid(pid , popm){
    if(popm && popm.length > 0){
        for(var i= 0 ; i < popm.length ; i++){
            if(popm[i]["mid"] == pid){
                return popm[i]["subPopdoms"] ; // 只针对二级菜单
            }
        }
    }
}
/* creator : 侯杏哲 2018-04-09 工具方法- 根据 mid 值的 获取全部信息  */
function getInfoByMid( mid , arr ) {
    if( arr && arr.length > 0 ){
        for(var i = 0 ; i < arr.length ; i++){
            var _mid = arr[i]["mid"] ;
            if( _mid == mid ){
                return arr[i] ;
            }
        }
    }
    return false ;
}
/* creator :hxz 2018-05-11 工具方法 - 根据首字符判断 有没有一级菜单的权限 */
function charFirstPopm(type , arr , isAll){
    if( arr && arr.length > 0 ){
        for(var i = 0 ; i < arr.length ; i++){
            var _mid = arr[i]["mid"].substr(0,1) ; // 默认首字母
            if(isAll){ // 校验全部名称
                _mid = arr[i]["mid"] ;
            }
            if( _mid == type ){
                return true ;
            }
        }
    }
    return false ;
}
