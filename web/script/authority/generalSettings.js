$(function () {
    // updator:hxz 2018-05-10 权限设置的模块点击事件和勾选逻辑
    $("#generalAuthority").on("click",".ty-form-checkbox",function () {
        // 权限管理的特殊设置 ：
        // 1.勾选“权限设置eb”或“审批设置ec”，
        //       则 “权限设置eb”、“审批设置ec”、“当前权限ed”、“职工权限ef” 四项都必选 ，
        //       且“当前权限”、“职工权限” 不能勾掉 ;
        //      “审批查看ee” 必不勾选
        // 2.勾选“当前权限ed” ，则 “审批查看ee” 必勾选
        // 3.文件夹管理rb表格管理rd 不能单独选择， 必须选中权限设置eb
        // 4.内容管理-目录aca 不能单独选择， 必须选中权限设置eb,内容管理acb受联动影响，但是不需要勾选权限设置

        // 如果当前指针没有禁用
        if(!$(this).hasClass("ty-form-disabled")){
            var id = $(this).attr("id") ; var id0 = id.indexOf("e") ;
            // 如果当前指针已选中
            if($(this).hasClass("ty-form-checked")){
                // 处理权限的特殊情况
                if(id0 === 0){
                    if(id === "ea" ){
                        var b = $("#eb").hasClass("ty-form-checked") ;  var c = $("#ec").hasClass("ty-form-checked") ;
                        var d = $("#ed").hasClass("ty-form-checked") ;  var e = $("#ee").hasClass("ty-form-checked") ;
                        var f = $("#ef").hasClass("ty-form-checked") ;
                        if(!b && !c && !d && !e && !f){ // 子级没有选中的
                            $("#ea").removeClass("ty-form-checked");
                        }
                        return false ;
                    }else{
                        if(id ==="eb" || id ==="ec"){ // 第一条 联动的去掉 ,去掉小总务设置
                            $("#eb").removeClass("ty-form-checked");
                            $("#ec").removeClass("ty-form-checked");
                            $("#ed").removeClass("ty-form-checked");
                            $("#ef").removeClass("ty-form-checked");
                            $("#ra").removeClass("ty-form-checked");
                            $("#md").removeClass("ty-form-checked");
                            $("#rb").removeClass("ty-form-checked");
                            $("#rd").removeClass("ty-form-checked");
                            $("#ac").removeClass("ty-form-checked");
                            $("#aca").removeClass("ty-form-checked");
                            $("#acb").removeClass("ty-form-checked");
                            if($("#ed").hasClass("ty-form-disabled")){
                                $("#ed").removeClass("ty-form-disabled");
                            }
                        }else if(id === "ed" ){
                            if($("#eb").hasClass("ty-form-checked")){ // 第一条联动，不可去掉
                                layer.msg("不可取消") ;  return false ;
                            }else{// 第二条 联动去掉
                                $("#ed").removeClass("ty-form-checked");
                                $("#ee").removeClass("ty-form-checked");
                            }

                        }else if(id == "ee"){
                            if($("#ed").hasClass("ty-form-checked") && (!$("#ed").hasClass("ty-form-disabled"))){
                                layer.msg("不可去掉") ;  return false ;
                            }else {
                                $(this).removeClass("ty-form-checked") ;
                            }
                        }else if(id == "ef"){
                            if( $("#ed").hasClass("ty-form-checked") &&$("#eb").hasClass("ty-form-checked") &&$("#ec").hasClass("ty-form-checked") &&
                                (!$("#ed").hasClass("ty-form-disabled")) &&(!$("#eb").hasClass("ty-form-disabled")) &&(!$("#ec").hasClass("ty-form-disabled"))
                            ){
                                layer.msg("不可去掉") ; return false ;
                            }else{
                                $(this).removeClass("ty-form-checked") ;
                            }
                        }
                    }
                    return false ;
                }else{
                    if(id == "kb"){
                        $("#kj").removeClass("ty-form-checked");
                        $("#kk").removeClass("ty-form-checked");
                    }else if(id == "kj"){
                        $("#kb").removeClass("ty-form-checked");
                        $("#kk").removeClass("ty-form-checked");
                    }else if(id == "kk"){
                        $("#kb").removeClass("ty-form-checked");
                        $("#kj").removeClass("ty-form-checked");
                    }
                    //销售管理-权限设置
                    if(id == "qe" || id == "qb" ||id == "qc" || id == "qd" || id == "qg"){
                       /* $("#qa").removeClass("ty-form-checked");
                        $("#qb").removeClass("ty-form-checked");
                        $("#qc").removeClass("ty-form-checked");
                        $("#qd").removeClass("ty-form-checked");
                        $("#qg").removeClass("ty-form-checked");*/
                       $("#qa").click();
                    }
                    $(this).removeClass("ty-form-checked");
                    // 当前指针后面所有选择框全部置空（除了禁用）
                    $(this).parent("td").next("td").find(".ty-form-checkbox").not(".ty-form-disabled").removeClass("ty-form-checked");
                    // 如果当前指针最近的为二级并且它下面选中的只有一个
                    if($(this).closest("table").parent("td").attr("level") === "2" && $(this).closest("table").find(".ty-form-checked").not(".ty-form-disabled").length === 1){
                        //上级置空
                        $(this).parents("td[level='2']").prev().find(".ty-form-checkbox").removeClass("ty-form-checked");
                        //如果它的一级下的二级只有一个选中
                        if($(this).parents("td[level='1']").find(".ty-form-checked").not(".ty-form-disabled").length === 1){
                            //他的一级置空
                            $(this).parents("td[level='1']").prev().find(".ty-form-checkbox").removeClass("ty-form-checked");
                        }
                    }
                    // 如果它的直接上级为一级，并且它的下级只有一个选中
                    if($(this).closest("table").parent("td").attr("level") === "1" && $(this).closest("table").find(".ty-form-checked").not(".ty-form-disabled").length === 1){
                        //它的上级置空
                        $(this).parents("td[level='1']").prev().find(".ty-form-checkbox").removeClass("ty-form-checked");
                    }
                }
            // 如果当前指针未选中
            }else{
                if(id0 == 0){ // 权限管理的特殊设置 ：
                    $("#ea").addClass("ty-form-checked") ;
                    if(id === "ea" ){
                        return false ;
                    }else{
                        if(id ==="eb" || id ==="ec"){ // 第一条 联动
                            if($("#ee").hasClass("ty-form-checked")){ layer.msg("审批设置 与 审批查看 不能同时选中"); return false ; }
                            $("#eb").addClass("ty-form-checked"); $("#ec").addClass("ty-form-checked");
                            $("#ed").addClass("ty-form-checked"); $("#ef").addClass("ty-form-checked");
                            $("#ra").addClass("ty-form-checked"); $("#rb").addClass("ty-form-checked");$("#rd").addClass("ty-form-checked");
                            $("#md").addClass("ty-form-checked"); $("#ac").addClass("ty-form-checked");$("#aca").addClass("ty-form-checked"); $("#acb").addClass("ty-form-checked");
                        }else if(id === "ed" ){ // 第二条 联动

                            $("#ed").addClass("ty-form-checked"); $("#ee").addClass("ty-form-checked");
                        }else if(id === "ee"){
                            if($("#ec").hasClass("ty-form-checked")){
                                layer.msg("审批设置 与 审批查看 不能同时选中") ;  return false ;
                            }else {
                                $(this).addClass("ty-form-checked") ;
                            }
                        }else if(id == "ef"){
                            $(this).addClass("ty-form-checked") ;
                        }
                    }
                }else{
                    if(id == "rb" || id == "ra"|| id == "rd"){
                        // 文件夹管理rb选中 权限设置必须选中
                        if(!$("#eb").hasClass("ty-form-checked")){
                            layer.msg("先选择权限设置才能设置文件夹管理！");
                            return false
                        }
                    }
                    if (id == "ac" || id == "aca") {
                        if(!$("#eb").hasClass("ty-form-checked")){
                            layer.msg("先选择权限设置才能设置内容管理-目录！");
                            return false
                        }
                    }
                    if(id == "kb"){
                        $("#kj").addClass("ty-form-checked");
                        $("#kk").addClass("ty-form-checked");
                    }else if(id == "kj"){
                        $("#kb").addClass("ty-form-checked");
                        $("#kk").addClass("ty-form-checked");
                    }else if(id == "kk"){
                        $("#kj").addClass("ty-form-checked");
                        $("#kb").addClass("ty-form-checked");
                    }
                    //销售管理-权限设置
                    if(id == "qe" || id == "qb" || id == "qc" || id == "qd" || id == "qg"){
                        /*$("#qa").addClass("ty-form-checked");
                        $("#qb").addClass("ty-form-checked");
                        $("#qc").addClass("ty-form-checked");
                        $("#qd").addClass("ty-form-checked");*/
                        $("#qa").click();
                    }
                    $(this).addClass("ty-form-checked");
                    // 它的上下级都选中
                    $(this).parent("td").next("td").find(".ty-form-checkbox").not(".ty-form-disabled").addClass("ty-form-checked");
                    $(this).parents("td").prev("td").find(".ty-form-checkbox").not(".ty-form-disabled").addClass("ty-form-checked");
                }
            }
        }
    });
    $(".ty-secondTab li").on("click", function () {
        var index = $(this).index()
        $(this).addClass("ty-active").siblings().removeClass("ty-active")
        $(".tblContainer").eq(index).show().siblings(".tblContainer").hide()
        $(".tblContainer").eq(index).find("tbody").html("")
        getPopedomListByManager(index)
    })
    getGeneralAuthorityList();
});

function getGeneralAuthorityList() {
    $.ajax({
        url:"../popedom/getUserList.do" ,
        data:{
            // "pageSize":pageSize,// 每页长度
            // "currPage":currPage,// 当前页
        } ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){


            var userList = data["userList"];                          //获取列表json
            var generalListStr = "";                                           //承载列表字符串

            if(userList !== undefined) {
                //循环遍历json
                //value： [{userName 姓名，gender 1-男 0-女，mobile 手机号，departName 部门，postName 职位，leaderName 直接上级},{}]

                for (var i = 0; i < userList.length; i++) {
                    var roleCode = userList[i].roleCode
                    if (roleCode === 'agent') {
                        var handleStr = '<span class="ty-color-gray">权限分配</span>'
                    } else {
                        var handleStr = '<span class="ty-color-blue" onclick="allocateAuthorityBtn($(this))">权限分配</span>'
                    }


                    generalListStr +=   '<tr id="' + userList[i].userID + '">' +
                                            '<td>' + userList[i].userName + '</td>' +
                                            '<td>' + chargeSex(userList[i].gender)+ '</td>' +
                                            '<td>' + userList[i].mobile+ '</td>' +
                                            '<td>' + userList[i].departName+ '</td>' +
                                            '<td>' + userList[i].postName+ '</td>' +
                                            '<td>' + userList[i].leaderName + '</td>' +
                                            '<td>'+handleStr+'</td>' +
                                        '</tr>';
                }

                //遍历结束，将字符串写进对应表格中
                $(".tplContainer").eq(0).find("tbody").html(generalListStr);
            }
        },
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}


/* creator：张旭博，2017-09-05 15:00:12，权限分配 - 按钮 */
function allocateAuthorityBtn(selector) {
    selector.parents("tr").addClass("generalAuthorityActive").siblings().removeClass("generalAuthorityActive");
    bounce.show($("#generalAuthority"));
    getPopedomListByManager(1)
    $("#generalAuthority .ty-secondTab li").eq(0).click()
}

function sureAllocateAuthority(){
    var mid = '';
    $(".ty-form-checked").each(function (index) {
        if(index === 0){
            mid += $(this).attr("id");
        }else{
            mid += ","+$(this).attr("id");
        }
    });
    var userId = $(".generalAuthorityActive").attr("id");
    var data = {
        "userId":userId,
        "mid":$.trim(mid)
    };
    $.ajax({
        url:"../popedom/saveUserPopedom.do" ,
        data:data ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            var status = data["status"];
            if(status === 0){
                $("#errorTip .tipWord").html("保存失败，请重试！")
                bounce.show($("#errorTip")) ;
            }else if(status === 1){
                bounce.cancel();
                layer.msg("保存成功！")
            }
        },
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

function getPopedomListByManager(index) {
    var manageId = getUserInfo("userID");
    var userId = $(".generalAuthorityActive").attr("id");
    $(".userName").html($(".generalAuthorityActive").find("td").eq(0).html())
    if (index === 0) {
        var url = '../popedom/getExclusivePopedomsByManager.do'
    } else {
        var url = '../popedom/getPopedomListByManager.do'
    }
    $.ajax({
        url: url,
        data:{
            "manageId":manageId,// 分配人id
            "userId":userId     // 被分配人id
        } ,
        success:function( data ){
            var success = data["success"];
            var dataList = data["data"];

            var checked,disabled;
            var listStr = '';
            if(success === 1) {
                for (var i = 0; i < dataList.length; i++) {
                    checked = dataList[i].checked;
                    disabled = dataList[i].disabled;
                    var checkedClass, disabledClass;
                    if (checked) {
                        checkedClass = "ty-form-checked"
                    } else {
                        checkedClass = ""
                    }
                    if (disabled) {
                        disabledClass = "ty-form-disabled"
                    } else {
                        disabledClass = ""
                    }
                    var subPopdoms2 = dataList[i].subPopdoms;
                    var users2 = dataList[i].users;
                    listStr += '<tr>' +
                        '   <td style="width: 25%">' +
                        '       <div class="ty-form-checkbox ' + checkedClass + ' ' + disabledClass + '" skin="green" id="' + dataList[i].mid + '">' +
                        '           <span>' + dataList[i].name + '</span>' +
                        '           <i class="fa fa-check"></i>' +
                        '       </div>' +
                        '   </td>' +
                        '   <td style="width: 75%"  level="1">' +
                        '       <table class="ty-table" frame="void">';
                    //第二层循环
                    if (subPopdoms2.length > 0) {
                        for (var j = 0; j < subPopdoms2.length; j++) {
                            checked = subPopdoms2[j].checked;
                            disabled = subPopdoms2[j].disabled;
                            if (checked) {
                                checkedClass = "ty-form-checked"
                            } else {
                                checkedClass = ""
                            }
                            if (disabled) {
                                disabledClass = "ty-form-disabled"
                            } else {
                                disabledClass = ""
                            }
                            //第三层数据
                            var subPopdoms3 = subPopdoms2[j].subPopdoms;
                            var users3 = subPopdoms2[j].users;
                            var userNameArr3 = []
                            users3.forEach(function (item) {
                                userNameArr3.push(item.userName)
                            })
                            listStr +=  '        <tr>' +
                                        '           <td style="width: 35%">' +
                                        '              <div class="ty-form-checkbox ' + checkedClass + ' ' + disabledClass + '" skin="green" id="' + subPopdoms2[j].mid + '">' +
                                        '                  <span>' + subPopdoms2[j].name + '</span>' +
                                        '                  <i class="fa fa-check"></i>' +
                                        '              </div>' +
                                        '           </td>' +
                                        '           <td style="width: 30%"  level="2">' +
                                        '               <table class="ty-table" frame="void">';
                            for (var k = 0; k < subPopdoms3.length; k++) {
                                checked = subPopdoms3[k].checked;
                                disabled = subPopdoms3[k].disabled;
                                var users4 = subPopdoms3[k].users
                                var userNameArr4 = []
                                users4.forEach(function (item) {
                                    userNameArr4.push(item.userName)
                                })
                                if (checked) {
                                    checkedClass = "ty-form-checked"
                                } else {
                                    checkedClass = ""
                                }
                                if (disabled) {
                                    disabledClass = "ty-form-disabled"
                                } else {
                                    disabledClass = ""
                                }
                                listStr +=  '                <tr>' +
                                    '                   <td>' +
                                    '                      <div class="ty-form-checkbox ' + checkedClass + ' ' + disabledClass + '" skin="green" id="' + subPopdoms3[k].mid + '">' +
                                    '                          <span>' + subPopdoms3[k].name + '</span>' +
                                    '                          <i class="fa fa-check"></i>' +
                                    '                      </div>' +
                                    '                   </td>' +
                                    '                   <td><span class="text">' +(userNameArr4.length > 0 ? userNameArr4.join("、"):"暂无")+'</span></td>' +
                                    '                </tr>';
                            }
                            listStr +=  '               </table>' +
                                        '           </td>' +
                                        '           <td style="width: 40%"><span class="text">' +(userNameArr3.length > 0 ? userNameArr3.join("、"):"暂无")+'</span></td>' +
                                        '        </tr>';
                        }
                    } else {
                        var userNameArr2 = []
                        users2.forEach(function (item) {
                            userNameArr2.push(item.userName)
                        })
                            listStr +=  '<tr>'+
                                        '   <td style="width: 35%"></td>'+
                                        '   <td style="width: 30%"></td>'+
                                        '   <td style="width: 40%"><span class="text">'+(userNameArr2.length > 0 ? userNameArr2.join("、"):"暂无")+'</span></td>'+
                                        '</tr>'
                    }
                    listStr += '       </table>' +
                        '   </td>' +
                        '</tr>';
                }
                $("#generalAuthority .tblContainer").eq(index).find("tbody").html(listStr);
                // $("#generalAuthority .ty-form-disabled").each(function () {
                //     if($(this).hasClass("ty-form-checked")){
                //         $(this).parents("td[level='1']").prev().find(".ty-form-checkbox").addClass("ty-form-disabled");
                //     }
                // })
            }else{
                var error = data.error;
                if(error){
                    $("#errorTip .tipWord").html(error.message) ;
                    bounce.show($("#errorTip")) ;
                }else{
                    $("#errorTip .tipWord").html("系统错误，请重试!") ;
                    bounce.show($("#errorTip")) ;
                }
            }
        }
    }) ;
}

function getUserInfo(key) {
    var loginUser = $("#loginUser").html();
    var loginUserJson = JSON.parse(loginUser);
    switch (key){
        case "userID":
            return loginUserJson.userID;
            break;
    }
}
function chargeSex(sex) {
    switch (sex){
        case "0" :
            return "女";
            break;
        case "1" :
            return "男";
            break;
        default:
            return "";
    }
}
