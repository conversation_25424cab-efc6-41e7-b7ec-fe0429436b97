(function ajaxSetupInit(){
    $.ajaxSetup({
        "type" : "post" ,
        "dataType" : "json" ,
        beforeSend:function(){} ,
        error:function () {
            layer.msg("链接错误！");
        } ,
        complete:function () {}
    });
})() ;
$(function () {
    getMainUserList (1, 20)
    $(".ty-container").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'onlySeeFrozen':
                $(".frozenRecord").show().siblings().hide()
                getOnlyFrozenList()
                break
            case 'frozenRecord':
                bounce.show($("#frozenRecord"))
                getFrozenRecord(1, 20)
                break
            case 'goBack':
                $(".main").show().siblings().hide()
                getMainUserList (1, 20)
                break
            case 'frozenAccount':
                // 冻结账号 - 按钮
                loading.open()
                var userID = $(this).parents("tr").data("id")

                $("#frozenAccount").data("userID", userID)
                determineRoleCode(userID).then(function (res) {
                    // 返回值 status 1，可以冻结 ，0无权限冻结，-1代办着其他职工的工作
                    // 返回值 status为1时，返回身份列表
                    var status = res.status
                    var user = res.user
                    if (status === 1) {
                        bounce.show($("#frozenAccount"))
                        $("#frozenAccount").find('select').val("")
                        var manageList = res.manageList

                        // 主身份
                        var mainStr =   '<div class="identityItem"  data-id="'+user.userID+'">' +
                                        '   <div class="des">系统内工作的代办者</div>' +
                                        '   <select class="ty-inputSelect"></select>' +
                                        '</div>'
                        $("#frozenAccount .alertAccount").html('所选账号： ' + user.userName + ' ' + user.mobile)
                        $("#frozenAccount .mainIdentity").html(mainStr)

                        // 其他身份列表
                        var otherStr = ''
                        for (var i in manageList) {
                            otherStr += '<div class="identityItem" data-id="'+manageList[i].userID+'">' +
                                        '   <div class="des">该职工'+manageList[i].roleName+'的身份，需换为他人</div>' +
                                        '   <div class="des ty-color-blue">注：该职工账号解冻后，如需恢复此身份，需另行操作。</div>' +
                                        '   <select class="ty-inputSelect"></select>' +
                                        '</div>'
                        }
                        $("#frozenAccount .otherIdentity").html(otherStr)

                        // 填充可换代办人列表
                        agentList().then(function (res) {
                            var userList = res.userList
                            var userID = $("#frozenAccount").data("userID")
                            var userStr = '<option value="">----请选择----</option>'
                            for (var j in userList) {
                                if (userList[j].userID !== userID) {
                                    userStr += '<option value="'+userList[j].userID+'">'+userList[j].userName+'</option>'
                                }
                            }
                            $("#frozenAccount select").html(userStr)
                            loading.close()
                            bounce.everyTime('0.2s','frozenAccount',function() {
                                var state = 0
                                $("#frozenAccount select").each(function () {
                                    var a = $(this).val()
                                    if ($(this).val() === '') {
                                        state++
                                    }
                                })
                                $("#next").prop("disabled", state > 0)
                            })
                        })

                    } else if (status === 0) {
                        $("#tip .text-center").html('<p>您无权冻结该账号！</p>')
                        $("#tip .determine").html("确定").unbind('click').on("click", function () {
                            bounce.cancel()
                        })
                        bounce.show($("#tip"))
                        loading.close()
                    } else if (status === -1) {
                        $("#tip .text-center").html('<p>'+user.userName+'还代办着其他职工的工作。</p><p>请先更换相应职工的代办者。</p>')
                        $("#tip .determine").html("我知道了").unbind('click').on("click", function () {
                            bounce.cancel()
                        })
                        bounce.show($("#tip"))
                        loading.close()
                    } else {
                        layer.msg("系统错误！")
                    }
                })
                break

            case 'thawAccount':
                // 账号解冻
                loading.open()
                var userID = $(this).parents("tr").data("id")
                $("#confirm").data('userID', userID)
                selectUserLock(userID).then(function (res) {
                    var status = res.status // 1代表操作人一致，为0代表，操作人不一致,取UserlockCreator为冻结操作人
                    var userLockCreator = res.userlockCreator // 冻结操作人
                    if (status === 1) {
                        thawAccount()
                    } else if (status === 0) {
                        $("#confirm .text-center").html('<p>该账号系由' + userLockCreator + '冻结。</p><p>建议您充分确认冻结原因后再操作。</p>')
                        $("#confirm .iKnow").html("我知道了，继续操作").unbind('click').on("click", function () {
                            thawAccount()
                        })
                        bounce.show($("#confirm"))
                        loading.close()
                    }

                })
                break
            case 'changeAgency':
                // 换代办人
                loading.open()
                var userID = $(this).parents("tr").data("id")
                $("#confirm").data('userID', userID)
                selectUserLock(userID).then(function (res) {
                    var status = res.status // 1代表操作人一致，为0代表，操作人不一致,取UserlockCreator为冻结操作人
                    var userLockCreator = res.userlockCreator // 冻结操作人
                    var userLockList = res.userLockList // 冻结记录
                    $("#confirm").data('userLockList', userLockList)
                    if (status === 1) {
                        initChangeAgency()
                    } else if (status === 0) {
                        $("#confirm .text-center").html('<p>该账号系由' + userLockCreator + '冻结。</p><p>建议您充分确认冻结原因后再操作。</p>')
                        $("#confirm .iKnow").html("我知道了，继续操作").unbind('click').on("click", function () {
                            initChangeAgency()
                        })
                        bounce.show($("#confirm"))
                        loading.close()
                    }
                })
                break
        }
    })
    $(".bounce").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'next':
                $("#confirm_Fixed .text-center").html('<p>账号在被冻结期间无法登录系统。</p><p>确定冻结该账号吗？</p>')
                $("#confirm_Fixed .iKnow").html("确定").unbind('click').on("click", function () {
                    loading.open()
                    var userList = []
                    $("#frozenAccount .otherIdentity .identityItem").each(function () {
                        var manageId = $(this).data("id")
                        var passiveUserId = $(this).find('select').val()
                        userList.push({
                            manageId: manageId,
                            passiveUserId: passiveUserId
                        })
                    })
                    var data = {
                        userId: $("#frozenAccount .mainIdentity .identityItem").data("id"), // 被冻结人
                        substitute: $("#frozenAccount .mainIdentity .identityItem select").val(), // 代办人
                        userList: JSON.stringify(userList) // 被冻结人高管id,代办人 ,组成的json串
                    }
                    console.log('sureFrozenAccount', data)
                    sureFrozenAccount(data).then(function (res) {
                        loading.close()
                        bounce.cancel()
                        bounce_Fixed.cancel()
                        var status = res.status
                        if (status === 1) {
                            layer.msg("操作成功！")
                            if ($(".frozenRecord").is(":visible")) {
                                getOnlyFrozenList(1, 20)
                            } else {
                                getMainUserList(1, 20)
                            }
                        } else {
                            $("#tip_Fixed .text-center").html('<p>操作失败！</p>')
                            bounce_Fixed.show($("#tip_Fixed"))
                        }
                    })
                })
                bounce_Fixed.show($("#confirm_Fixed"))
                break;
            case 'sureChangeAgency':
                loading.open()
                var userId = $("#confirm").data("userID")
                var substitute = $("#changeAgency select").val()
                $.ajax({
                    url: '../userLock/updateSubstitute.do',
                    data: {
                        userId: userId, //被冻结人
                        substitute: substitute //代办人
                    },
                    success: function (res) {
                        loading.close()
                        bounce.cancel()
                        var status = res.status
                        if (status === 1) {
                            layer.msg("操作成功！")
                            if ($(".frozenRecord").is(":visible")) {
                                getOnlyFrozenList(1, 20)
                            } else {
                                getMainUserList(1, 20)
                            }
                        } else {
                            $("#tip_Fixed .text-center").html('<p>操作失败！</p>')
                            bounce_Fixed.show($("#tip_Fixed"))
                        }
                    }
                })
                break
        }

    })
})

// creator: 张旭博，2020-11-03 17:50:23，冻结账号首页获取人员列表
function getMainUserList(currentPageNo, pageSize) {
    $.ajax({
        url: '../userLock/userLockIndex.do',
        data: {
            currentPageNo: currentPageNo,
            pageSize: pageSize
        },
        success: function (res) {
            var userList = res.userList
            var pageInfo = res.pageInfo

            // 分页
            var totalPage = pageInfo.totalPage;
            var cur = pageInfo.currentPageNo;
            setPage( $("#ye_frozenMain") , cur ,  totalPage , "frozenMain");

            var str = ''
            for (var i in userList) {
                var sexStr = '',
                    degreeStr = '',
                    leaderStr = '',
                    handleStr = '';
                if (userList[i].submitState === '1') {
                    sexStr = chargeSex(userList[i].gender);
                    degreeStr = chargeDegree(userList[i].degree);
                }
                leaderStr = userList[i].roleCode === 'super' ? '':userList[i].leaderName;
                if (userList[i].roleCode !== 'agent') {
                    handleStr = '<span class="ty-btn ty-circle-2 ty-btn-blue" type="btn" data-name="frozenAccount">冻结账号</span>'
                } else {
                    // handleStr = '<span class="ty-btn ty-circle-2 ty-btn-orange" type="btn" data-name="thawAccount">账号解冻</span> '+
                    //             '<span class="ty-btn ty-circle-2 ty-btn-orange" type="btn" data-name="changeAgency">换代办人</span>'
                    handleStr = '<span class="ty-btn ty-circle-2 ty-btn-orange" type="btn" data-name="changeAgency">换代办人</span>'
                }
                str +=  '<tr data-id="'+userList[i].userID+'">' +
                        '    <td>' + userList[i].userName + '</td>' +
                        '    <td>' + sexStr + '</td>' +
                        '    <td>' + userList[i].mobile + '</td>' +
                        '    <td>' + (userList[i].departName || '') + '</td>' +
                        '    <td>' + (userList[i].postName || '') + '</td>' +
                        '    <td>' + leaderStr + '</td>' +
                        '    <td>' + degreeStr + '</td>' +
                        '    <td>' + handleStr + '<div class="hd">'+JSON.stringify(userList[i])+'</div></td>' +
                        '</tr>'
            }
            $(".main table tbody").html(str)
        }
    })
}

// creator: 张旭博，2020-11-03 17:53:46，冻结账号判断接口，以及获取身份列表
function determineRoleCode(userID) {
    var pm_determine = new Promise(function(resolve, reject) {
        $.ajax({
            url: '../userLock/determineRoleCode.do',
            data: {
                userId: userID
            },
            success: function (res) {
                resolve(res)
            },
            error: function (err) {
                reject(err)
            }
        })
    })
    return pm_determine
}

// creator: 张旭博，2020-11-03 17:53:46，解冻和更换代理人时调用，以及获取冻结记录
function selectUserLock(userID) {
    var pm_determine = new Promise(function(resolve, reject) {
        $.ajax({
            url: '../userLock/selectUserLock.do',
            data: {
                userId: userID
            },
            success: function (res) {
                resolve(res)
            },
            error: function (err) {
                reject(err)
            }
        })
    })
    return pm_determine
}

// creator: 张旭博，2020-11-05 08:37:43，冻结时调用，用来判断是否可以冻结
function thawAccount() {
    bounce.show($("#confirm"))
    loading.close()
    $("#confirm .text-center").html('<p>解冻后，该账号将恢复登录系统等功能。</p><p>确定解冻该账号吗？</p>')
    $("#confirm .iKnow").html("确定").unbind('click').on("click", function () {
        loading.open()
        var userId = $("#confirm").data('userID')
        $.ajax({
            url: '../userLock/clearUserLock.do',
            data: {
                userId: userId
            },
            success: function (res) {
                loading.close()
                var status = res.status
                if (status === 1) {
                    layer.msg("操作成功！")
                    if ($(".frozenRecord").is(":visible")) {
                        getOnlyFrozenList(1, 20)
                    } else {
                        getMainUserList(1, 20)
                    }
                    bounce.cancel()
                } else {
                    $("#tip_Fixed .text-center").html('<p>操作失败！</p>')
                    bounce_Fixed.show($("#tip_Fixed"))
                }
            }
        })

    })
}

// creator: 张旭博，2020-11-04 16:11:27，初始化换代办人
function initChangeAgency() {
    loading.open()
    bounce.show($("#changeAgency"))

    // 填充可换代办人列表
    agentList().then(function (res) {
        loading.close()
        var userList = res.userList
        var userLockList = $("#confirm").data('userLockList')

        // 代办人选择框赋值（前端去掉现任代办人）
        var userStr = '<option value="">----请选择----</option>'
        for (var j in userList) {
            if (userLockList[userLockList.length - 1].substitute !== userList[j].userID) {
                userStr += '<option value="'+userList[j].userID+'">'+userList[j].userName+'</option>'
            }
        }
        $("#changeAgency select").html(userStr)
        var str =  '<div class="agencyItem">' +
                   '   <div class="des">被冻结账号：'+userLockList[0].lockedName + ' ' +userLockList[0].lockedMobile+'</div>' +
                   '   <div class="ty-right">冻结者：'+userLockList[0].createName + ' ' + formatTime(userLockList[0].createTime, true) +'</div>' +
                   '</div>'
        for (var i = 0; i < userLockList.length; i++) {
            if (i === userLockList.length - 1) {
                str +=  '<div class="agencyItem">' +
                        '   <div class="des">系统内工作现由'+userLockList[i].substituteName+'在代办。</div>' +
                        '   <div class="ty-right">操作者：'+userLockList[i].createName + ' ' + formatTime(userLockList[i].createTime, true) +'</div>' +
                        '</div>'
            } else {
                str +=  '<div class="agencyItem">' +
                        '   <div class="des">系统内工作曾由'+userLockList[i].substituteName+'代办。</div>' +
                        '   <div class="ty-right">操作者：'+userLockList[i].createName + ' ' + formatTime(userLockList[i].createTime, true) +'</div>' +
                        '</div>'
            }
        }
        $("#changeAgency .agencyRecord").html(str)
        loading.close()
        bounce.everyTime('0.2s','changeAgency',function() {
            var state = 0
            $("#changeAgency select").each(function () {
                if ($(this).val() === '') {
                    state++
                }
            })
            $("#sureChangeAgencyBtn").prop("disabled", state > 0)
        })
    })
}

// creator: 张旭博，2020-11-03 17:53:46，可选代办人列表接口
function agentList() {
    return new Promise(function (resolve, reject) {
        $.ajax({
            url: '../userLock/substituteList.do',
            success: function (res) {
                resolve(res)
            },
            error: function (err) {
                reject(err)
            }
        })
    })
}

// creator: 张旭博，2020-11-04 10:28:35，冻结账号 - 确定
function sureFrozenAccount(data) {
    return new Promise(function (resolve, reject) {
        $.ajax({
            url: '../userLock/userLockEnter.do',
            data: data,
            success: function (res) {
                resolve(res)
            },
            error: function (err) {
                reject(err)
            }
        })
    })
}

// creator: 张旭博，2020-11-05 08:38:54，账号冻结记录
function getFrozenRecord(currentPageNo, pageSize) {
    $.ajax({
        url: '../userLock/userLockAll.do',
        data: {
            currentPageNo: currentPageNo,
            pageSize: pageSize
        },
        success: function (res) {
            var userList = res.userLockList
            var pageInfo = res.pageInfo;

            // 分页
            var totalPage = pageInfo.totalPage;
            var cur = pageInfo.currentPageNo;
            setPage( $("#ye_frozenRecord") , cur ,  totalPage , "frozenRecord");
            if (userList) {
                var str =''
                for (var i in userList) {
                    var sexStr = '',
                        handleStr = '',
                        substituteStr = '';
                    if (userList[i].submitState === '1') {
                        sexStr = chargeSex(userList[i].gender);
                    }
                    substituteStr = userList[i].substituteName + ' ' + userList[i].substituteMobile
                    var operation = Number(userList[i].operation) // 4-冻结账号,5-账号解冻,6-换代办人
                    if (operation === 4) {
                        handleStr = '冻结账号'
                    } else if (operation === 5) {
                        handleStr = '账号解冻'
                        substituteStr = '--'
                    } else if (operation === 6) {
                        handleStr = '换代办人'
                    }
                    str +=  '<tr>' +
                        '    <td>' + userList[i].lockedName + '</td>' +
                        '    <td>' + sexStr + '</td>' +
                        '    <td>' + userList[i].lockedMobile + '</td>' +
                        '    <td>' + (userList[i].departName || '') + '</td>' +
                        '    <td>' + (userList[i].postName || '') + '</td>' +
                        '    <td>' + handleStr + '</td>' +
                        '    <td>' + substituteStr + '</td>' +
                        '    <td>' + userList[i].createName + ' ' + formatTime(userList[i].createTime, true) + '</td>' +
                        '</tr>'
                }
                $("#frozenRecord table tbody").html(str)
            }

        }
    })
}

// creator: 张旭博，2020-11-05 08:43:01，获取只看账号被冻结的职工列表
function getOnlyFrozenList() {
    $.ajax({
        url: '../userLock/userLockCurrent.do',
        success: function (res) {
            var userList = res.userList
            if (userList) {
                var str =''
                for (var i in userList) {
                    var sexStr = '',
                        degreeStr = '',
                        leaderStr = '',
                        handleStr = '';
                    if (userList[i].submitState === '1') {
                        sexStr = chargeSex(userList[i].gender);
                        degreeStr = chargeDegree(userList[i].degree);
                    }
                    leaderStr = userList[i].roleCode === 'super' ? '':userList[i].leaderName;
                    if (userList[i].roleCode !== 'agent') {
                        handleStr = '<span class="ty-btn ty-circle-2 ty-btn-blue" type="btn" data-name="frozenAccount">冻结账号</span>'
                    } else {
                        // handleStr = '<span class="ty-btn ty-circle-2 ty-btn-orange" type="btn" data-name="thawAccount">账号解冻</span> '+
                        //     '<span class="ty-btn ty-circle-2 ty-btn-orange" type="btn" data-name="changeAgency">换代办人</span>'
                        handleStr = '<span class="ty-btn ty-circle-2 ty-btn-orange" type="btn" data-name="changeAgency">换代办人</span>'
                    }
                    str +=  '<tr data-id="'+userList[i].userID+'">' +
                            '    <td>' + userList[i].userName + '</td>' +
                            '    <td>' + sexStr + '</td>' +
                            '    <td>' + userList[i].mobile + '</td>' +
                            '    <td>' + (userList[i].departName || '') + '</td>' +
                            '    <td>' + (userList[i].postName || '') + '</td>' +
                            '    <td>' + leaderStr + '</td>' +
                            '    <td>' + degreeStr + '</td>' +
                            '    <td>' + handleStr + '<div class="hd">'+JSON.stringify(userList[i])+'</div></td>' +
                            '</tr>'
                }
                $(".frozenRecord table tbody").html(str)
            }

        }
    })
}


/* creator：张旭博，2017-11-06 09:23:16，转换男女 */
function chargeSex(sex) {
    switch (sex) {
        case "1":
            return "男";
            break;
        case "0":
            return "女";
            break;
        default:
            return '';
    }
}

// creator: 张旭博，2018-05-11 10:19:31，学历转换
function chargeDegree(degreeCode) {
    var degree = '';
    switch (degreeCode) {
        case 1:
            degree = "研究生";
            break;
        case 2:
            degree = "本科";
            break;
        case 3:
            degree = "大专";
            break;
        case 4:
            degree = "中专或高中";
            break;
        case 5:
            degree = "其它";
            break;
        default:
            degree = '';
            break;
    }
    return degree
}