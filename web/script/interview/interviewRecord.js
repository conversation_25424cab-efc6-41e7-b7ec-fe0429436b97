var perSize = 20 ;
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#contactSeeDetail"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#useDefinedLabel"));
bounce_Fixed3.cancel();
$(function () {
    let month = new Date().getMonth() + 1;
    if (month < 10) { month = '0' + month}
    $("#monthSearch").val(month);
    getAllCreator();
    initCardUpload($(".cardUploadBtn"));
    getCustomerList($("#customerSearch"));　// 客户列表
    $(".personCon").on("click", ".fa-close", function () {
        $(this).parent().remove();
    });
    $(".viewsList").on("click", '.fa', function () {
        if($(this).hasClass("fa-check-square-o")){
            $(this).attr("class","fa fa-square-o")
        }else{
            $(this).attr("class","fa fa-check-square-o");
        }
    })
    $("#customer").change(function() {
        $("#edit .personCon").html("");
    })
    $("#interviewList").on("click", "span.btn", function () {
        var type = $(this).data("type");
        editObj = $(this);
        switch (type){
            case "edit": // 修改
                var info = JSON.parse($(this).siblings(".hd").html());
                $.ajax({
                    "url":"../saleInterview/getWarrantCustomerList.do",
                    "data":{"userId":sphdSocket.user.masterUserID },
                    success:function (res) {
                        var list = res['data'] || [];
                        let index = list.findIndex(item => Number(item.customerId) === parseInt(info.customer))
                        if(index >= 0){
                            scan("edit");
                        } else {
                            layer.msg("您没有此权限");
                        }
                    }
                })
                break;
            case "hide": // 隐藏
                let able = $(this).data("abled");
                hideInterview(able);
                break;
            case "scan": // 查看
                scan();
                break;
            case "uploadLog": // 修改记录
                getRecordList($(this));
                break;
        }
        
    })
    $("body").on("click",".linkBtn,.redLinkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
    getHostTime(function (hosttime) {
        var year = new Date(hosttime).format("yyyy年");
        var dayStr = new Date(hosttime).format("yyyy年MM月dd日");
        var week =new Date(hosttime).getDay(), weekStr = "";
        if (week == 0) {
            weekStr = "星期日";
        } else if (week == 1) {
            weekStr = "星期一";
        } else if (week == 2) {
            weekStr = "星期二";
        } else if (week == 3) {
            weekStr = "星期三";
        } else if (week == 4) {
            weekStr = "星期四";
        } else if (week == 5) {
            weekStr = "星期五";
        } else if (week == 6) {
            weekStr = "星期六";
        }
        $("#todayData").html("今天是"+ dayStr + " " + weekStr);
        laydate.render({elem: '#yearSearch', type: 'year', format: 'yyyy年',value: year});
        getList(1 , perSize);
    });
});
// creator: hxz 2020-12-10 获取联系人列表
function getCusContactList(cusID) {
    $.ajax({
        "url":"../sales/getContactsList.do",
        "data":{ 'customerId': cusID },
        success:function (res) {
            if(res.status !== 1){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data'] || [], str="";
            let selected = $("#interviewer").siblings(".hd").html();
            let selectedList = [];
            if (selected !== "") {selectedList = JSON.parse(selected)}
            for(let i in list){
                let item = list[i];
                item.participator = item.id;
                item.memo = item.name;
                let faClass = `fa-square-o`;
                let icon = selectedList.findIndex(value => Number(value.id) === Number(item.id));
                icon > -1 ? faClass = `fa-check-square-o`: "";
                str += `<li>
                    <i class="fa ${faClass}"></i>
                    <span>${item.name}</span>
                    <span>${item.mobile}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                    <span class="linkBtn ty-right" data-type="contactSocialNew" data-id="${item.id}" onclick="cus_recordDetail($(this))">查看</span>
                </li>`;
            }
            $("#chooseCusContact .cusList").html(str);
        }
    });
}
function cus_recordDetail(obj){
    var source = obj.data('type');
    if(source == 'contactSocialNew') {
        let info = obj.siblings(".hd").html()
        info = JSON.parse(info)
        info['socialList'] = info.socialList ? JSON.parse(info.socialList):"";
        see_otherContactStr(info)
    } else {
        $.ajax({
            url : "../sales/getContactsSocial.do" ,
            data : {
                'contactId': obj.data('id')
            },
            success:function(data){
                var get = data['data'];
                see_otherContactStr(get)
            }
        });
    }
}
// creator: 李玉婷，2019-09-04 18:55:15，添加
function addMore (obj){
    obj.next("select").show();
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContact:visible li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li>' +
                '<span class="sale_ttl1">手机：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="1" data-name="手机" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact:visible").append(html);
            break;
        case '2':
            html +=
                '<li>' +
                '<span class="sale_ttl1">QQ：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact:visible").append(html);
            break;
        case '3':
            html +=
                '<li>' +
                '<span class="sale_ttl1">Email：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact:visible").append(html);
            break;
        case '4':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微信：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact:visible").append(html);
            break;
        case '5':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微博：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact:visible").append(html);
            break;
        case '9':
            $("#useDefinedLabel").data("source", 1);
            $("#useDefinedLabel input").val("");
            bounce_Fixed3.show($("#useDefinedLabel"));
            bounce_Fixed3.everyTime('0.5s', 'useDefinedLabel',function () {
                var name = $.trim($("#defLable").val());
                if (name == '' || !name) {
                    $("#addNewLableSure").prop('disabled', true);
                } else {
                    $("#addNewLableSure").prop('disabled', false);
                }
            })
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-19 09:37:24，清除自定义标签
function clearLableText(obj) {
    obj.siblings().val("");
}
// creator: 李玉婷，2019-09-19 10:14:23，自定义标签确定
function addNewLable(){
    let source = $("#useDefinedLabel").data("source");
    var val = $("#defLable").val();
    var html = ``;
    if (source === 1) {//新增联系人、新增访谈对象
        html =
            '<li>' +
            '<span class="sale_ttl1">' + val + '：</span>' +
            '<span class="gap"><input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" data-org="" require/></span>'+
            '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
            '</li>';
    } else {//录入同事外的人
        html =
            '<li>' +
            '<p>' + val + '</p>' +
            '<input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" data-org="" require/>'+
            '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
            '</li>';
    }
    $(".otherContact").append(html);
    bounce_Fixed3.cancel();
}
// creator: 李玉婷，2019-09-07 08:50:59，删除添加的联系方式
function removeAdd(obj) {
    obj.parent("li").remove();
}
function see_otherContactStr(get){
    var html = '',socialList = get['socialList'];
    $("#see_contactName").html(get.name);
    $("#see_position").html(get.post);
    $("#see_contactTag").html(get.tags);
    //$("#see_contactTag").html(get.visitCard);
    $(".see_otherContact").html("");
    $("#contactSeeDetail .see_createName").html(get.createName);
    $("#contactSeeDetail .see_createDate").html(new Date(get.createDate).format('yyyy-MM-dd hh:mm:ss'));
    if(socialList.length > 0){
        let sortList = [];
        for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
        for(var r in socialList){
            let item = socialList[r];
            let _index = Number(item.type);
            sortList[_index].push(item);
        }
        let sortAfter = [];
        for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
        let allStr = '';
        for(var t in sortAfter){
            let item = sortAfter[t];
            if(t%2===0){
                allStr += `<tr><td>${item.name}</td><td>${item.code}</td>`
            }else{
                allStr += `<td>${item.name}</td><td>${item.code}</td></tr>`
            }
        }
        if(sortAfter.length % 2 !== 0){
            allStr += `<td> </td><td> </td></tr>`
        }
        $(".see_otherContact").html(allStr);
    }
    bounce_Fixed2.show($("#contactSeeDetail"));
}
// creator: 李玉婷，2019-09-17 15:09:14，名片上传
function initCardUpload(obj){
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        formData:{
            module: '访谈记录',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        fileObjName:'file',
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var filePath = $.uploadUrl + data.filename;
            var fileUid =data.fileUid;
            var imgStr = `<div class="bussnessCard">
                            <div class="filePic" data-fileuid="${fileUid}" data-path="${data.filename}" style="background-image:url(${filePath})"></div>
                            <span class="ty-color-blue" onclick="cancleCard($(this))">删除</span>
                        </div>` ;
            if ($("#newContectInfo").is(":visible")) {
                $('#uploadCard').hide();
                $('#uploadCard').before(imgStr);
            } else if ($("#addOtherTogether").is(":visible")) {
                $('#addOtherTogether .businessCard').html(imgStr);
            }
        }
    });
}
// creator: 李玉婷，2019-09-04 19:31:34，删除名片
function cancleCard(obj) {
    let fileuid = obj.siblings(".filePic").data("fileuid")
    let op = {'type':'fileId', 'fileId':fileuid }
    cancelFileDel(op , true);
    obj.parent().html("");
    $('#uploadCard').show();
}
// creator: 李玉婷，2019-08-21 14:46:56，图片删除
function cancleThis(obj) {
    obj.parent(".imgsthumb").remove();
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.attr('path');
    $("#picShow img").attr('src', src);
    $("#picShow").fadeIn("fast");
}
// creator: hxz 2020-12-10 新增访谈对象
function addContactInfo() {
    let customer = $("#customer").val();
    if(!customer){
        layer.msg("请先选择客户名称！");
        return false
    }
    $("#newContectInfo .bonceHead span").html('新增客户联系人');
    $("#contactFlag").html( '访谈');
    $("#newContectInfo").data('type', 'new');
    $("#newContectInfo").data('source', 'receiveNew');
    document.getElementById('newContectData').reset();
    $(".otherContact").html("");
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    if($(".contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
    }
    $("#addMoreContact").hide();
    bounce_Fixed2.show($("#newContectInfo"));
    bounce_Fixed2.everyTime('0.5s','updateContact',function () {
        var state = 0, filledNum = 0,otherContactLen = 0;
        var contactsCard = $("#contactsCard").data('org');
        var imgCard = $("#contactsCard .filePic").data('path');
        var len = $(".otherContact").data('length');
        if ($(".otherContact:visible li").length > 0) {
            $(".otherContact:visible li").each(function () {
                var val = $(this).find("input").val();
                var type = $(this).find("input").data('type');
                if (type == '9' || type == '9') type = 6
                if (val == '') {
                    $("#addMoreContact option").eq(type).prop('disabled', true);
                }else {
                    $("#addMoreContact option").eq(type).prop('disabled', false);
                    otherContactLen++;
                }
            })
        }
        if (len !=  otherContactLen) state ++;
        $("#newContectData [require]:visible").each(function(){
            if ($(this).val() != '') filledNum++;
            if($(this).val() != $(this).data('org')){
                state ++;
            }
        });
        if(contactsCard != imgCard) state ++;
        if(filledNum > 0 && state > 0){
            $("#addContact").prop("disabled",false);
        }else{
            $("#addContact").prop("disabled",true);
        }
    });
}
// creator: hxz 2020-12-10 新增联系人
function addContact() {
    var data = {
        'id': Math.random() + '',
        'tags': $("#contactFlag").html(),
        'name': $("#contactName").val(),
        'post': $("#position").val(),
        'mobile': $("#contactNumber").val(),
        'visitCard': '',
        'socialList': '[]'
    };
    if($("#contactsCard .bussnessCard").length > 0){
        let path = $("#contactsCard .bussnessCard .filePic").data('path');
        //path = path.substr(9,path.length)
        data.visitCard = path ;
    }
    if($(".otherContact:visible li").length > 0){
        var arr = []
        $(".otherContact:visible li").each(function () {
            if ($(this).find('input').val() != '') {
                var json = {
                    'code': $(this).find("input").val(),
                    'type': $(this).find("input").data('type'),
                    'name': $(this).find("input").data('name')
                };
                arr.push(json);
            }
        })
        arr = JSON.stringify(arr);
        data.socialList = arr;
    }
    data.customer = $("#customer").val();
    $.ajax({
        url: '/sales/addCustomerContact.do',
        data: data,
        success: function (res) {
            var status = res.status;
            if (status == '1') {
                data.participator = res.id;
                data.memo = data.name;
                let html = `<li>
                    <i class="fa fa-square-o"></i>
                    <span>${data.name}</span>
                    <span>${data.mobile}</span>
                    <span class="hd info">${JSON.stringify(data)}</span>
                    <span class="linkBtn ty-right" data-type="contactSocial" data-id="${res.id}" onclick="cus_recordDetail($(this))">查看</span>
                   </li>`;
                $("#chooseCusContact .cusList").append(html);
                layer.msg('新增成功')
            } else {
                layer.msg("新增失败！");
            }
        }
    })
    bounce_Fixed2.cancel();
}
// create : hxz 2020-10-03 修改记录查看
function seeInterviewLogInfo(obj) {
    var info = JSON.parse(obj.siblings(".hd").html());
    var ttl = obj.parent().prev().html();
    $("#scan .bonceHead span").html(ttl);
    $("#scan .scan_interviewerContentItem").remove();
    $("#scan .scan_gainsItem").remove();
    $.ajax({
        "url":"../saleInterview/getRecordInterview.do",
        "data":{ 'id': info['id'], 'frontId': info['frontId'] },
        success:function (res) {
            var status = res['status'];
            var data = res['data']['now'];
            var front = res['data']['front'];
            if(status == 1){
                bounce_Fixed.show($("#scan"));
                for(var key in data){
                    if(key == "interviewDate"){
                        data[key] = new Date(data[key]).format("yyyy-MM-dd");
                        if(front && front[key]){
                            front[key] = new Date(front[key]).format("yyyy-MM-dd");
                        }
                    }
                    if(key === 'thyd' && data[key]) {
                        let html = ``
                        data[key].forEach(item => {
                            html  +=
                                `<tr class="scan_interviewerContentItem">
                                    <td>${front ? compareArr(front[key], item, 'memo') : item.memo}：</td>
                                    <td colspan="2">
                                        <div>${front ? compareArr(front[key], item, 'content') : item.content}</div>
                                    </td>
                                </tr>`;
                        })
                        $(".scan_interviewerContent").after(html);
                    } else if(key === 'cgContent' && data[key]) {
                        let html = ``
                        let cg = [], fCg = [];
                        if (data[key] !== '') {
                            cg = data[key].split("$a$");
                        }
                        if (front && front[key] && front[key] !== '') {
                            fCg = front[key].split("$a$");
                        }
                        cg.forEach(con => {
                            html +=
                                `<tr class="scan_gainsItem">
                                    <td></td>
                                    <td colspan="2">
                                        ${front ? compareCg(fCg, con) : con}
                                    </td>
                                </tr>`;
                        })
                        $(".scan_gains").after(html);
                    } else {
                        var str = data[key] || '' ;
                        if(front && data[key] != front[key]){
                            str = "<span class='red'>"+ str +"</span>";
                        }
                        $("#scan ." + key).html(str);
                    }
                }
                $("#scan .createName").html( data['createName'] + " " + new Date(data['createDate']).format("yyyy-MM-dd hh:mm:ss"));
            }else{
                layer.msg("获取数据失败！")
            }
        }
    })
    
}
/*creator:lyt 2023/4/3 0003 下午 9:41 修改记录查看标红*/
function compareArr(front, data, key) {
    var index = front.findIndex(value => value.participator === data.participator);
    let html = data[key] || '' ;
    if(front && (index === -1 || front[index][key] !== data[key])){
        html = "<span class='red'>"+ data[key] +"</span>";
    }
    return html;
}
/*creator:lyt 2023/4/3 0003 下午 9:41 修改记录查看标红*/
function compareCg(front, data) {
    var index = front.findIndex(value => value === data);
    let html = data ;
    if(front && (index === -1 || front[index] !== data)){
        html = "<span class='red'>"+ data +"</span>";
    }
    return html;
}

// create : hxz 2020-10-03 修改记录
function getRecordList() {
    var info = JSON.parse(editObj.siblings(".hd").html());
    var customerId = info['id'];
    bounce.show($("#seeInterviewInfo"));
    $(".recordTtl").html('访谈记录修改记录');
    $.ajax({
        url: '../sales/getRecordInterviewList.do',
        data: {
            'interviewId': customerId
        },
        success: function (res) {
            var status = res['status'];
            if (status == '1') {
                 let updateName = res['updateName']
                if(updateName){
                    var list = res['list']||[], str = "";
                    $("#interviewLog .noneUpdated").hide().siblings().show();
                    let updateDate = new Date(res['updateDate']).format("yyyy/MM/dd hh:mm:ss");
                    $("#interviewLog .eidtNum").html(list.length-1);
                    $("#interviewLog .editTime").html(updateDate);
                    $("#interviewLog tbody").children(":gt(0)").remove();
                    for(var i = 0 ; i <list.length ; i++){
                        var numStr = "原始信息";
                        if(i>0){ numStr = "第" + i + "次修改后";list[i]["frontId"] = list[i-1]['id']; }

                        str += "<tr>" +
                            "    <td>"+ numStr +"</td>" +
                            "    <td class='ty-td-control'>" +
                            "       <span class=\"ty-color-blue\" onclick='seeInterviewLogInfo($(this))'>查看</span>" +
                            "       <span class=\"hd\">"+ JSON.stringify(list[i]) +"</span>" +
                            "    </td>" +
                            "    <td>"+ list[i]['createName'] +" "+ (new Date(list[i]['updateDate'] || list[i]['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                            "</tr>";
                    }
                    $("#interviewLog tbody").append(str);
                }else{
                    $("#interviewLog .noneUpdated").show().siblings().hide();
                    $("#interviewLog .noneUpdated .create").html(res['createName'] + " " + new Date(res['createDate']).format('yyyy/MM/dd hh:mm:ss'));

                }
                bounce.show($("#interviewLog"));

            } else {
                layer.msg("查看失败！");
            }
        }
    })
}
// create : hxz 2020-10-03 编辑访谈记录确定
function setPerson() {
    var id = $("#userIdsSelect").val(), isOk = true;
    if(id == ""){ return false;}
    $(".personCon .person").each(function(){
        var uID = $(this).data("uid");
        if(uID == id){ isOk = false }
    })
    if(!isOk){ layer.msg("已有此人，无需再选！"); return false; }
    var name = $("#userIdsSelect option:selected").html();
    var str = "<span class=\"person\" data-uid=\""+ id +"\"><span >"+ name +"</span><i class=\"fa fa-close\"></i></span>"
    $("#edit .personCon").append(str);
}
// create : hxz 2020-10-03 修改查看访谈记录
function scan(type) {
    var info = JSON.parse(editObj.siblings(".hd").html());
    $.ajax({
        "url":"../saleInterview/getInterview.do",
        "data":{ "id":info['id']  },
        success:function (res) {
            var data = res['data'];
            if (data.interviewerList.length > 0) {
                let iNames = data.interviewerNames.split(",");
                for(let i = 0; i < iNames.length; i++) {
                    iNames[i] === "" ? iNames[i] = '--' : "";
                }
                data.interviewerNames = iNames.toString();
            }
            if(type == "edit"){ // 给修改赋值
                bounce.show($("#edit"));
                clearContact();
                $("#edit .bonceHead span").html("修改访谈记录");
                $("#editType").val("update");
                $("#customer").data("type", "old");
                getCustomerList($("#customer"),data['customer']);
                $("#edit [data-name='id']").val(data['id']);
                let html = ``, ydStr = ``;
                let cgContent = handleNull(data.cgContent) !== ""? data.cgContent.split("$a$"):[];
                for(let i =0; i < cgContent.length; i++) {
                    html += setGainStr(cgContent[i]);
                }
                for(let i =0; i < data.thyd.length; i++) {
                    ydStr += setTalksStr(data.thyd[i]);
                }
                if (data.interviewerList.length > 0) {
                    $("#edit #interviewer").html("共" + data.interviewerList.length + "人：" + data.interviewerNames).siblings(".hd").html(JSON.stringify(data.interviewerList));
                }
                if (data.tsList.length > 0) {
                    $("#edit #fellowTravelers").html("共" + data.tsList.length + "人：" + data.tsNames).siblings(".hd").html(JSON.stringify(data.tsList));
                }
                if (data.ftsList.length > 0) {
                    for(let i =0; i < data.ftsList.length; i++) {
                            data.ftsList[i].name = data.ftsList[i].memo;
                            data.ftsList[i].cardPath = data.ftsList[i].cardPath || '';
                            data.ftsList[i].socialList = data.ftsList[i].socialList || '[]';
                    }
                    $("#edit #fellowUnColleague").html("共" + data.ftsList.length + "人：" + data.ftxNames).siblings(".hd").html(JSON.stringify(data.ftsList));
                }
                if (data.users.length > 0) {
                    $("#edit .whoAbleData").html("共" + data.users.length + "人：" + data.userNames +`<span class="hd">${JSON.stringify(data.userIds)}</span>`).show().siblings().hide();
                }
                handleNull(data.mbContent) !== ""? $("#edit .purposeCon").show():"";
                $("#edit .purposeCon").html(data.mbContent);
                $("#edit .gains").after(html);
                $("#edit .interviewerContent").after(ydStr);
                $("#edit [data-name='interviewDate']").val(new Date(data['interviewDate']).format("yyyy年MM月dd日"));
            }else { // 查看
                bounce_Fixed.show($("#scan"));
                $("#scan .bonceHead span").html("访谈记录");
                data['interviewDate'] = new Date(data['interviewDate']).format("yyyy-MM-dd")
                $("#scan .scan_interviewerContentItem").remove();
                $("#scan .scan_gainsItem").remove();
                for(var key in data){
                    if (key === "thyd") {
                        let html  = ``;
                        data[key].forEach(item => {
                            html +=
                                `<tr class="scan_interviewerContentItem">
                                    <td>${item.memo || '--'}：</td>
                                    <td colspan="2">
                                        <div>${item.content}</div>
                                    </td>
                                </tr>`;
                        })
                        $(".scan_interviewerContent").after(html);
                    } else if(key === 'cgContent' && data[key]) {
                        let html  = ``;
                        let cgContent = data[key].split("$a$");
                        cgContent.forEach(con => {
                            html +=
                                `<tr class="scan_gainsItem">
                                    <td></td>
                                    <td colspan="2">
                                        ${con}
                                    </td>
                                </tr>`;
                        });
                        $(".scan_gains").after(html);
                    } else {
                        $("#scan ." + key).html(data[key]);
                    }
                }
                $("#scan .createName").append(new Date(data['createDate']).format("yyyy-MM-dd hh:mm:ss"));
            }
            
        }
    })
}
// create : hxz 2020-10-03 编辑访谈记录确定
function editOk() {
    var editType = $("#editType").val();
    var data = {}, url = "";
    $("#edit [need]").each(function () {
        var key = $(this).data("name");
        data[key] = $(this).val();
    })
    Object.keys(data).forEach((key)=>{ if(!data[key])  delete data[key] });
    if(!data['customer']){
        layer.msg("请先选择客户！");
        return false;
    }
    let interviewDate =  data["interviewDate"];
    if (interviewDate) {
        interviewDate = interviewDate.replace(/([^\u0000-\u00FF])/g,"-");
        interviewDate = interviewDate.substr(0, interviewDate.length-1);
        data["interviewDate"] = interviewDate
    }
    let arr1=[],arr2=[],arr3=[];
    let interviewer = $("#edit #interviewer").siblings(".hd").html();// 访谈对象
    let tsIds = $("#edit #fellowTravelers").siblings(".hd").html();// 同行者同事
    let ftxIds = $("#edit #fellowUnColleague").siblings(".hd").html();// 同行者非同事
    let userIds = $("#edit .whoAbleData").children(".hd").html();
    if (interviewer !== "") {
        interviewer = JSON.parse(interviewer);
        interviewer.forEach(function(item) {
            arr1.push(item.participator)
        })
    }
    if (tsIds !== "") {
        tsIds = JSON.parse(tsIds);
        tsIds.forEach(function(item) {
            arr2.push(item.participator)
        })
    }
    if (ftxIds !== "") {
        ftxIds = JSON.parse(ftxIds);
        ftxIds.forEach(function(item) {
            arr3.push(item.participator)
        })
    }
    // 谈话要点
    let talkingPoints = [], cgContent = ``;
    $("#edit .interviewerContentItem:visible").each(function () {
        let info = JSON.parse($(this).find(".hd").html());
        let val = $(this).find(".interviewerCon").html();
        let talk = {
            "type": info.type,//"参与者类型:1-访谈对象，2-同行者(同事)，3-同行者（非同事）"
            "content": val,
            "orders": $(this).index(),
            "participator": info.participator,
            "participatorName": info.memo,
        }
        talkingPoints.push(talk);
    })
    // 成果/结论
    $("#edit .gainsItem:visible").each(function () {
        let val = $(this).find(".gainsCon").html();
        if (val !== "") {
            cgContent += val + '$a$';
        }
        //cgContent.push(val);
    })
    //cgContent = cgContent.toString();
    if (cgContent !== "") {
        cgContent = cgContent.slice(0, cgContent.length-3);
    }
    //cgContent = cgContent.toString();
    data['cgContent']= cgContent;

    data['interviewer']= arr1.toString();// 访谈对象
    data['tsIds']= arr2.toString();// 同行者同事
    data['ftxIds']= arr3.toString();// 同行者非同事
    data['userIds'] = userIds; // 何人可见
    data['thydJson']= JSON.stringify(talkingPoints);
    data['mbContent']= $("#edit .purposeCon").html();// 目标内容
    data['ftsListJson']= $("#edit #fellowUnColleague").siblings(".hd").html();// 录入de同事外的人
    if(editType == "add"){
        url = "../saleInterview/addCustomerInterview.do";
    }else{
        url = "../saleInterview/updateCustomerInterview.do";
    }
    bounce.cancel();
    $.ajax({
        "url":url ,
        "data":data,
        success:function (res) {
            var code = res['code'];
            var msg = res['msg'];
            if(code == 200){
                layer.msg("操作成功");
                var cur = $("#yeCon1 .yecur").html();
                getList(cur, perSize);
            }else {
                layer.msg(msg);
            }
        }
    })
}
// create : hxz 2020-10-03 新增访谈记录
function addNew() {
    $("#edit .bonceHead span").html('新增访谈记录');
    bounce.show($("#edit"));
    $("#edit [need]").val("");
    $("#editType").val("add");
    $("#customer").data("type", "new");
    getCustomerList($("#customer"));　// 客户列表
    clearContact();
}
function changeCustomer(obj) {
    let type = obj.data("type");
    if (type === "new") {
        $("#customer").data("type", "old");
    }else if (type !== "new") {
        bounce_Fixed.show($("#changeCustomerTip"));
    }
}
function changeCustomerOk() {
    bounce_Fixed.cancel();
    clearContact()
}
// create : hxz 2021-1-22 清空已选联系人
function clearContact() {
    $(".textMax").html("0/200");
    $("#edit .gainsItem").remove();
    $("#edit .purposeCon").html("").hide();
    $("#edit .interviewerContentItem").remove();
    $("#edit .whoAbleData").html("").hide().siblings().show();
    $("#edit #interviewer").html("尚未选择").siblings(".hd").html("");
    $("#edit #fellowTravelers").html("尚未选择").siblings(".hd").html("");
    $("#edit #fellowUnColleague").html("尚未选择").siblings(".hd").html("");
    $("#chooseFellowUn .otherTogetherList").html("");
}
// create : hxz 2020-10-03 获取列表
function getList(cur , per) {
    let creator = $("#creatorSearch").val();
    let customerId = $("#customerSearch").val();
    let year = $("#yearSearch").val();
    let month = $("#monthSearch").val();
    let abled = $("#enableSearch").val();// 状态 0隐藏 1正常
    let count = 0, str = ``;
    year = year.replace("年","");
    let time = `${year}年${month}月1日至${new Date(year, month, 0).format('yyyy年MM月dd日')}`;
    $.ajax({
        "url":"../saleInterview/getInterviewList.do",
        "data":{ "currPage":cur,  "pageSize":per,  "creator":creator, "customerId":customerId, "year":year, "month":month, "enabled":abled },
        success:function (res) {
            var totalPage = res['totalPage'] ;
            setPage($("#yeCon1"), cur , totalPage, "getInterviewList", "{}");
            var list = res['data'] || [];
            if(res.code !== 400  && list.length > 0){
                count = list.length;
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    var editBtn = "";
                    let interviewers = handleNull(item['interviewerNames']) !== "" ? item['interviewerNames'].split(","):[];
                    for(let i = 0; i < interviewers.length; i++) {
                        interviewers[i] === "" ? interviewers[i] = '--' : "";
                    }
                    // view===0 显示，其他的隐藏
                    if(item['view'] == '0'){
                        editBtn ="<span class=\" btn ty-color-blue\" data-type=\"edit\">修改</span>" +
                            "<span class=\"btn ty-color-red\" data-type=\"hide\" data-abled='"+ (1^abled) +"'>" + (abled === '1' ? "隐藏" :"取消隐藏")+"</span>" ;
                    }
                    str += "<tr>" +
                        "<td>"+ item['customerName'] +"</td>" +
                        "<td title='"+interviewers.toString() +"'>共"+ interviewers.length + "人"+ (interviewers.length > 0? ","+interviewers.toString(): "") +"</td>" +
                        "<td>"+(new Date(item['interviewDate'] ).format("yyyy-MM-dd")) +"</td>" +
                        "<td>"+ item['createName'] + ' ' +(new Date(item['createDate'] ).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "<td>"+ handleNull(item['saleName']) +"</td>" +
                        "<td>" +
                        "    <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    <span class=\"btn ty-color-blue\" data-type=\"scan\">查看</span>" +
                        "    <span class=\"btn ty-color-blue\" data-type=\"uploadLog\">修改记录</span>" + editBtn +
                        "</td>" +
                        "</tr>";
                }
            }
            $("#interviewList").html(str);
            $(".mainCon1 .rowCount").html(count);
            $(".mainCon1 .timePeriod").html(time);
        }
    })
}
// create : hxz 2020-10-03 获取客户列表
function getCustomerList(obj,selectedVal) {
    obj.children(":gt(0)").remove();
    $.ajax({
        "url":"../saleInterview/getWarrantCustomerList.do",
        "data":{"userId":sphdSocket.user.masterUserID },
        success:function (res) {
            var list = res['data'] || [], str = "";
            if(list.length > 0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], sel = " ";
                    if(selectedVal == item['customerId']){ sel = " selected";  }
                    str += "<option"+ sel +" value='"+ item['customerId'] +"'>"+ item['fullName'] +"</option>";
                }
            }
            obj.append(str);
        }
    })
}
// create : hxz 2020-10-03 何人可见列表
function getStuffList(obj) {
    $.ajax({
        "url":"../saleInterview/getInterviewUserList.do",
        "data":{"code": "gd" },
        success:function (res) {
            let code = res['code'];
            if(code == 200){
                let list = res['data'] || [], str = ``;
                if(list.length > 0){
                    for(var i = 0 ; i < list.length ; i++){
                        var item = list[i] ;
                        str += ` <li>
                        <i class="fa fa-square-o"></i>
                        <span>${item['userName']}</span>
                        <span>${item['mobile']}</span>
                        <span class="hd info">${JSON.stringify(item)}</span> </li>`;
                    }
                }
                obj.html(str);
            }else{
                layer.msg("获取失败！");
            }
        }
    })
}
function hideInterview(able) {
    let info = JSON.parse(editObj.siblings(".hd").html());
    $.ajax({
        "url":"../saleInterview/stopOrStart.do",
        "data": { "id":info.id,"enabled":able },
        success:function (res) {
            let code = res['code'];
            if(code == 200){
                if(able === 0) {
                    editObj.parents("tr").remove();
                    layer.msg("<p>该条数据已隐藏！</p><p>可到“隐藏的访谈记录”中查看！</p>");
                } else {
                    layer.msg("<p>操作成功！</p><p>该条数据已取消隐藏！</p>");
                    getList(1, 20, "","","","",1);
                }
            }else{
                layer.msg("操作失败！");
            }
        }
    })
}
//creator:lyt 2023/01/30 下午 3:31 全部录入者
function getAllCreator() {
    $.ajax({
        "url":"../org/getPresentUsers.do",
        "data":{},
        success:function (res) {
            if(res.success !== 1){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data'] || [], str=`<option value="">全部录入者</option>`;
            for(let i in list){
                let item = list[i];
                str += `<option value="${item.userID}">${item.userName}</option>`;
            }
            $("#creatorSearch").html(str);
        }
    });
}
//creator:lyt 2022/11/25 下午 3:31 选择访谈对象
function interviewer(obj){
    let target = obj.data("target");
    var cusId = $("#customer").val();
    if(!cusId){
        layer.msg("请先选择客户！");
        return false;
    }
    $("#target").val(target);
    bounce_Fixed.show( $("#chooseCusContact") );
    getCusContactList(cusId);
}
// creator: hxz 2020-12-10 访谈对象新增
function chooseCusContactOk() {
    let selectObj = $("#chooseCusContact").find(".fa-check-square-o");
    if(selectObj.length > 0){
        let arr = [], name = [];
        selectObj.each(function () {
            let info = $(this).siblings("span.hd").html();
            let infoJson = JSON.parse(info);
            infoJson.type = 1;
            arr.push(infoJson);
            name.push(infoJson.name || '--');
        })
        name = name.join('、');
        $($("#target").val()).html("共" + selectObj.length + "人：" + name)
            .siblings(".hd").html(JSON.stringify(arr));
        bounce_Fixed.cancel();
        delTalkPoints();
    }else layer.msg('请先选择人员')
}
/*creator:lyt 2023/4/7 0007 下午 2:51 用户删除访谈对象，删除者如有“谈话要点”一并删除*/
function delTalkPoints() {
    let list = [];
    let arr1 = $("#interviewer").siblings(".hd").html();
    let arr2 = $("#fellowTravelers").siblings(".hd").html();
    if (arr1 !== "") {arr1 = JSON.parse(arr1);}
    if (arr2 !== "") {arr2 = JSON.parse(arr2);}
    list = [...arr1,...arr2];
    $(".interviewerContentItem").each(function () {
        let uid = $(this).data("uid");
        let index = list.findIndex(value => value.participator === uid)
        if (index === -1) {
            $(this).remove();
        }
    })
}
//creator:lyt 2022/11/25 0025 下午 7:27 同行者（同事）选择
function fellowTravelers(obj){
    let target = obj.data("target");
    $("#chooseFellow .target").val(target)
    bounce_Fixed.show($("#chooseFellow"));
    let selected = $("#fellowTravelers").siblings(".hd").html();
    let selectedList = [];
    if (selected !== "") {selectedList = JSON.parse(selected)}
    $.ajax({
        "url":"../org/getPresentUsers.do",
        "data":{},
        success:function (res) {
            if(res.success !== 1){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data'] || [], str="";
            for(let i in list){
                let item = list[i];
                item.type = 2;
                item.participator = item.userID;
                item.memo = item.userName;
                let faClass = `fa-square-o`;
                let icon = selectedList.findIndex(value => Number(value.userID) === Number(item.userID));
                icon > -1 ? faClass = `fa-check-square-o`: "";
                str += `<li>
                    <i class="fa ${faClass}"></i>
                    <span>${item.userName}</span>
                    <span>${item.mobile}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                </li>`;
            }
            $("#chooseFellow .fellowList").html(str);
        }
    });
}
function chooseFellowOk(){
    let selectObj = $("#chooseFellow").find(".fa-check-square-o");
    if(selectObj.length > 0){
        let arr = [], name = [];
        selectObj.each(function () {
            let info = $(this).siblings("span.hd").html();
            let infoJson = JSON.parse(info);
            arr.push(infoJson);
            name.push(infoJson.userName);
        })
        name = name.join('、');
        $($("#chooseFellow .target").val()).html("共" + selectObj.length + "人：" + name)
            .siblings(".hd").html(JSON.stringify(arr));
        delTalkPoints();
        bounce_Fixed.cancel();
    }else layer.msg('请先选择人员')
}
//creator:lyt 2022/11/26 0026 下午 9:55 同行者（非同事）选择
function fellowUnColleague(obj){
    let html = ``;
    let target = obj.data("target");
    $("#chooseFellowUn .target").val(target);
    let unFellow = $("#fellowUnColleague").siblings(".hd").html();
    if (unFellow !== "") {
        unFellow = JSON.parse(unFellow);
        unFellow.forEach((item)=> {
            html +=
            `<li>
                 <span>${item.name}</span>
                 <span>${item.mobile}</span>
                 <div class="ty-right">
                    <span class="linkBtn gapR" data-fun="updateOtherTogether">修改</span>
                    <span class="redLinkBtn" data-fun="delOtherTip" data-source="delOtherTip">删除</span>
                    <span class="hd">${JSON.stringify(item)}</span>
                </div>      
             </li>`;
        })
    }
    $("#chooseFellowUn .fellowList").html(html);
    bounce_Fixed.show($("#chooseFellowUn"));
}
function chooseFellowUnOk() {
    let selectObj = $("#chooseFellowUn .fellowList li");
    if(selectObj.length > 0){
        let arr = [], name = [];
        selectObj.each(function () {
            let info = $(this).find("span.hd").html();
            let infoJson = JSON.parse(info);
            arr.push(infoJson);
            name.push(infoJson.name);
        })
        name = name.join('、');
        $("#fellowUnColleague").html("共" + selectObj.length + "人：" + name)
            .siblings(".hd").html(JSON.stringify(arr));
        bounce_Fixed.cancel();
    }else layer.msg('请先选择人员');
}
//creator:lyt 2022/11/26 0026 下午 10:05 录入同事外的人
function addOtherTogether(){
    $("#addOtherTogether").data("source", "add").find("input").val("");
    $("#addOtherTogether .addMoreContact").hide();
    $("#addOtherTogether .otherContact").html("");
    $("#addOtherTogether .businessCard").html("");
    bounce_Fixed2.show($("#addOtherTogether"));
}
function updateOtherTogether(obj){
    let data = JSON.parse(obj.siblings(".hd").html());
    $("#addOtherTogether").data("obj", obj)
    $("#addOtherTogether").data("source", "update").find("input").val("");
    $("#addOtherTogether .addMoreContact").hide();
    $("#addOtherTogether .otherContact").html("");
    $("#addOtherTogether .businessCard").html("");
    $("#addOtherTogether input").each(function () {
        let name = $(this).attr("name");
        $(this).val(data[name]);
    })
    var socialList = typeof data.socialList === 'string' ?JSON.parse(data.socialList): data.socialList;
    if (data.cardPath && data.cardPath != '' && data.cardPath != 'undefined' && data.cardPath != 'null'){
        var path = data.cardPath;
        var imgStr =
            '	<div class="bussnessCard"><div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
            '   <span class="ty-color-blue" onclick="cancleCard($(this))">删除</span> </div>';
        $("#addOtherTogether .businessCard").html(imgStr);
    }
    var html = '';
    if(socialList.length > 0){
        for(var r in socialList){
            html +=
                '<li>' +
                '<p>'+ socialList[r].name +'</p>' +
                '<input type="text" value="'+ socialList[r].code +'" placeholder="请录入" data-type="'+ socialList[r].type +'" data-name="'+ socialList[r].name +'" data-org="'+ socialList[r].code +'" require/>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
        }
    }
    $("#addOtherTogether .otherContact").html(html).data('length', socialList.length);
    bounce_Fixed2.show($("#addOtherTogether"));
}
function addOtherTogetherSure(){
    var name = $("#otherTogetherName").val();
    if(name === ""){
        layer.msg("还有必填项尚未填写！");
        return false;
    }
    let source = $("#addOtherTogether").data("source");
    var data = {
        'id': Math.random() + '',
        'type': 3,//1-访谈对象，2-同行者(同事)，3-同行者（非同事）"
        'cardPath': '',
        'socialList': '[]'
    };
    $("#addOtherTogether input[require]").each(function () {
        let name = $(this).attr("name");
        data[name] = $(this).val();
    });
    if($("#addOtherTogether .businessCard").length > 0){
        let path = $("#addOtherTogether .businessCard .filePic").data('path');
        data.cardPath = path ;
    }
    if($("#addOtherTogether .otherContact li").length > 0){
        var arr = []
        $("#addOtherTogether .otherContact li").each(function () {
            if ($(this).find('input').val() != '') {
                var json = {
                    'code': $(this).find("input").val(),
                    'type': $(this).find("input").data('type'),
                    'name': $(this).find("input").data('name')
                };
                arr.push(json);
            }
        })
        arr = JSON.stringify(arr);
        data.socialList = arr;
    }
    data.participator = data.id
    data.memo = data.name
    let html = `<li>
                 <span>${data.name}</span>
                 <span>${data.mobile}</span>
                 <div class="ty-right">
                    <span class="linkBtn gapR" data-fun="updateOtherTogether">修改</span>
                    <span class="redLinkBtn" data-fun="delOtherTip" data-source="delOtherTip">删除</span>
                    <span class="hd">${JSON.stringify(data)}</span>
                </div>      
             </li>`;
    if (source === "add"){
        $("#chooseFellowUn .fellowList").append(html);
    } else {
        let obj = $("#addOtherTogether").data("obj");
        obj.parents("li").replaceWith(html);
    }
    bounce_Fixed2.cancel();
}
function delOtherTip(obj) {
    $("#delTip").data("obj",obj);
    $("#delTip").data("source", obj.data("source"));
    bounce_Fixed2.show($("#delTip"));
}
function delOtherOk(){
    let obj = $("#delTip").data("obj");
    let source = $("#delTip").data("source");
    if (source === 'delOtherTip'){
        bounce_Fixed2.cancel()
        obj.parents("li").remove();
    }
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreContact(obj){
    var val = obj.val();
    var html = '', type = ``;
    obj.val('0').hide();
    $(".otherContact:visible li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    if (val === '9'){
        $("#useDefinedLabel").data("source", 2);
        $("#useDefinedLabel input").val("");
        bounce_Fixed3.show($("#useDefinedLabel"));
        bounce_Fixed3.everyTime('0.5s', 'useDefinedLabel',function () {
            var name = $.trim($("#defLable").val());
            if (name == '' || !name) {
                $("#addNewLableSure").prop('disabled', true);
            } else {
                $("#addNewLableSure").prop('disabled', false);
            }
        })
    } else {
        switch (val) {
            case '1':
                type = '手机';
                break;
            case '2':
                type = 'QQ';
                break;
            case '3':
                type = 'Email';
                break;
            case '4':
                type = '微信';
                break;
            case '5':
                type = '微博';
                break;
            default:break;
        }
        html +=
            '<li>' +
            '<p>'+ type+'</p>' +
            '<input type="text" placeholder="请录入" data-type="'+ val +'" data-name="'+ type+'" require/>'+
            '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>' +
            '</li>';
        $(".otherContact:visible").append(html);
    }
}
//creator:lyt 2023/1/12 14:14 访谈目标编辑
function contentEntry(obj) {
    let icon = 0;
    let target = obj.data("target");
    let con = ``;
    if (target === 'purpose'){
        con = $("#edit .purposeCon").html();
    } else if (target === 'gains') {
        icon = 1;
        let source = obj.data("type");
        $("#storageData").data("obj", obj);
        $("#storageData").data("type", source);
        if (source === 'update') {
            con = obj.parent().siblings(".gainsCon").html();
        }
    }
    if (con === ""){
        $("#contentEntry .clearAreaBtn").hide();
    } else {
        $("#contentEntry .clearAreaBtn").show();
    }
    $("#contentEntry .target").val(target);
    $("#contentEntry textarea").val(con);
    $("#contentEntry .textMax").html(con.length + "/200");
    $(".con" + icon).show().siblings().hide();
    bounce_Fixed.show($("#contentEntry"));
}
function contentEntryOk() {
    let target = $("#contentEntry .target").val();
    let con = $("#contentEntry textarea").val();
    if (target === 'purpose'){
        $("#edit .purposeCon").show().html(con);
    } else if (target === 'gains') {//成果/结论编辑
        let obj = $("#storageData").data("obj");
        let source = $("#storageData").data("type");
        let out = ``;
        if (source === 'update') {
            out = $(".addInterview tr").index(obj.parents("tr"))
        }
        let html = setGainStr(con);
        if (source === 'add') {
            if ($("#edit .gainsItem").length > 0){
                $(".gainsItem:last").after(html);
            } else {
                $(".gains").after(html);
            }
        } else if(source === 'update') {
            $(".addInterview tr").eq(out).replaceWith(html);
        }
    }
    bounce_Fixed.cancel();
}
function setGainStr(con) {
    let html =
        `<tr class="gainsItem">
                    <td></td>
                    <td colspan="2">
                        <span class="gainsCon">${con}</span>
                        <div class="ty-right">
                        <span class="redLinkBtn gapR" data-fun="gainDel">删除</span>
                        <span class="linkBtn" data-fun="contentEntry" data-target="gains" data-type="update">编辑</span>
                        </div>
                    </td>
                </tr>`;
    return html;
}
function setTalksStr(infoJson) {
    let html  =
        `<tr class="interviewerContentItem" data-uid="${infoJson.participator}">
                    <td><span class="interviewerName">${infoJson.memo || '--'}</span>：</td>
                    <td colspan="2">
                        <span class="hd">${JSON.stringify(infoJson)}</span>
                        <div class="interviewerCon">${infoJson.content}</div>
                        <div class="ty-right">
                        <span class="redLinkBtn gapR" data-fun="gainDel">删除</span>
                        <span class="linkBtn" data-fun="addTalkingPoints" data-type="update">编辑</span>
                        </div>
                    </td>
                </tr>`;
    return html;
}
//creator:lyt 2023/1/12 14:24 访谈目标删除
function purposeDel() {
    $(".entryCon:visible").find("textarea").val("");
    $(".entryCon:visible").find(".textMax").html("0/200");
}
//creator:lyt 2023/1/12 14:24 成果/结论删除
function gainDel(obj) {
    obj.parents("tr").remove();
}
//creator:lyt 2023/1/28 10:04 谈话要点编辑
function addTalkingPoints(obj){
    let type = obj.data("type");
    let list = [], str="";
    let list1 = $("#interviewer").siblings(".hd").html();
    let list2 = $("#fellowTravelers").siblings(".hd").html();
    let list3 = $("#fellowUnColleague").siblings(".hd").html();
    if (list1 !== "") {
        list1 = JSON.parse(list1);
        list = [...list1];
    }
    if (list2 !== "") {
        list2 = JSON.parse(list2);
        list = [...list,...list2];
    }
    if (list3 !== "") {
        list3 = JSON.parse(list3);
        list = [...list,...list3];
    }
    for(let i in list){
        let item = list[i];
        str += `<option value='${JSON.stringify(item)}'>${item.memo || '--'}</option>`;
    }
    $("#addTalkingPoints").data("obj", obj);
    $("#addTalkingPoints #participant").html(str);
    bounce_Fixed.show($("#addTalkingPoints"));
    purposeDel();
    if (type === 'add'){
        $(".clearTalkArea").hide();
    } else if (type === 'update') {
        let info = JSON.parse(obj.parent().siblings(".hd").html());
        let name = info.memo;
        let con = obj.parent().siblings(".interviewerCon").html();
        $(".clearTalkArea").show();
        $("#participant option:contains("+ name +")").attr("selected",true);
        $(".entryCon:visible").find("textarea").val(con);
        $(".entryCon:visible").find(".textMax").html(con.length + "/200");
    }
}
function addTalkingPointsOk(){
    let info = $("#addTalkingPoints #participant").val();
    let isOk = true;
    let val = $(".entryCon:visible").find("textarea").val();
    let obj = $("#addTalkingPoints").data("obj");
    let type = obj.data("type")
    if( info !== ""){
        if (val === "") {
            layer.msg("请录入该发言者的谈话要点");
            return false;
        }
        let infoJson = JSON.parse(info);
        let curObj = $(".interviewerContentItem");
        let out = ``;
        if (type === 'update') {
            out = $(".addInterview tr").index(obj.parents("tr"))
        }
        curObj.each(function(){
            let idx = $(this).index();
            if (idx !== out) {
                var person = $(this).data("uid");
                if(infoJson.participator == person){ isOk = false }
            }
        })
        if(!isOk){ layer.msg("已有此人，无需再选！"); return false; }
        if (val !== "") {
            infoJson.content = val;
            let html = setTalksStr(infoJson);
            if (type === 'add') {
                if ($("#edit .interviewerContentItem").length > 0){
                    $(".interviewerContentItem:last").after(html);
                } else {
                    $(".interviewerContent").after(html);
                }
            } else if (type === 'update'){
                $(".addInterview tr").eq(out).replaceWith(html);
            }
            bounce_Fixed.cancel();
        } else  layer.msg('请录入该发言者的谈话要点');
    }else layer.msg('请先选择人员');
}
//creator:lyt 2023/1/12 8:24 何人可见
function whoAble() {
    $(".collList").html("");
    bounce_Fixed.show($("#whoAbleLog"));
    getStuffList($(".collList")); // 何人可见列表
}
//creator:lyt 2023/1/12 8:24 何人可见
function whoAbleLogSure() {
    if ($(".collList .fa-check-square-o").length > 0){
        let str = ``, idStr = [], names = [];
        $(".collList .fa-check-square-o").each(function () {
            let info = JSON.parse($(this).siblings(".hd").html());
            str += info.userName + '、';
            idStr.push(info.userID);
            names.push(info.userName);
        });
        if (idStr.length > 0){
            str = `共${names.length}人,` + names.join("、");
            let html = `<span class="hd">${idStr.toString()}</span>`
            $(".whoAbleData").html(str + html).show().siblings().hide();
        }
        bounce_Fixed.cancel()
    }else layer.msg('请先选择人员');
}
// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
        layer.msg("字数不能超过" + max + "个！");
    }
    obj.siblings(".textMax").text(curLength + '/' + max);
}
// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else {
        console.log("未定义的状态")
    }
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = []
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
}
laydate.render({elem: '#interviewDate', format: 'yyyy年MM月dd日'});

