/**
 * Created by Administrator on 2017/8/14 0014.
 */

let bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#knowTip"));
bounce_Fixed2.cancel();

let mtCategories = null; // 全部种类数据
let listData = { 'category':false , 'state':false , 'keyword':'' } // 列表的传值数据

$(function () {
    rendertest();
    // 查询筛选框显示

    //跳转
    $("#bocked").on('click','option',function(){
        $("#search_text").val($(this).text());
    })
    $(".next").on('click',function(){
        $("#two").show();
        $("#one").hide();
    })

    $("#back").on('click',function (){
        $("#one").show();
        $("#two").hide();
    })
});

function scanCtrlHide() {
    $("#scanCtrlCon").hide();
}

// creator: sy, 2022-2-21,原辅查询
function getMainList(pageNum){
    $("#boder").html("")
    $.ajax({
        "url":"../mt/list",
        "type":"post",
        "data":{"keyword":$("#search_text").val(),"pageNum":pageNum,"per":"20","state":"4"},
        success:function(data){
            let list = data.data || [];
            if(list.length>0){
                $("#stork tbody").html("");//避免重复遍历而使数据重复保存
                $.each(list,function(j,info){
                    $("#boder").append(
                        "<tr>" +
                        "<td>"+ info.name+"</td>" +
                        "<td>"+ info.code+"</td>" +
                        "<td>"+ info.model+"</td>" +
                        "<td>"+ info.specifications+"</td>" +
                        "<td>"+ info.create_name+"</td>" +
                        "<td>"+ info.unit+"</td>" +
                        "<td>"+ myFuncdtion(info.minimum_stock)+ "</td>" +
                        "<td>"+ info.current_stock+"</td>" +
                        "<td class='ty-td-control'>"+
                        "<span class='ty-color-blue data-type=\"initialStock\"'>"+
                        myFuncotion(info.initial_stock)+"</span>" +
                        "</td>" +
                        "<td class='ty-td-control'>" +
                        "<span class='ty-color-blue data-type=\"locationNumber\"' onclick='getpridt($(this))'>"+
                        myFunction(info.location_number)+"</span>" +"<span class='hd'>"+JSON.stringify(info)+"</span>"+
                        "</td>" +
                        "</tr>"
                    );
                });
            };

            let totalPage=data.totalCount;//总页数
            let cur = data.pageNum;//当前页数  pageNum是传什么返回什么
            let jsonStr = JSON.stringify(data);
            setPage($("#ye1"),cur,totalPage,"rawSearch", jsonStr);
            //rawSearch 是个字符串的值，根据所要实现的功能的含义进行命名
        }
    })
}

function myFunction(location_number){
    let a = "个";
    return (location_number == undefined || location_number == null)? 0+a:location_number+a;
}
function myFuncdtion(minimum_stock){
    return (minimum_stock == undefined || minimum_stock ==null)? 0:minimum_stock;
}
function myFuncotion(initial_stock){
    return (initial_stock == undefined || initial_stock ==null)? 0:initial_stock;
}

function getpridt(obj,location_number,current_stock){
    bounce.show($("#holdStockSee"));//通过bounce方法使弹窗显示
    console.log(obj);
    let b=obj.siblings().html();//获得obj兄弟下面的内容
    console.log(b);
    b = JSON.parse(b);//将字符串形态的b转换成json数据形态的b
    console.log(b);
    let c = b.location_number;
    $("#holdStockSee #holdStockInfo").html(//向表格中填入数据
        //将数据导入占用库位中弹出框表格中
        "<thead>"+
            "<tr>"+
                "<td width='15%'>"+"材料名称"+"</td>"+
                "<td width='15%'>"+"材料代号"+"</td>"+
                "<td width='10%'>"+"型号"+"</td>"+
                "<td width='10%'>"+"规格"+"</td>"+
                "<td width='20%'>"+"创建人"+"</td>"+
                "<td width='10%'>"+"计量单位"+"</td>"+
                "<td width='10%'>"+"当前库存"+"</td>"+
                "<td width='10%'>"+"占用库位"+"</td>"+
            "</tr>"+
        "</thead>"+
        "<tr>"+
            "<td>"+b.name+"</td>"+
            "<td>"+b.code+"</td>"+
            "<td>"+b.model+"</td>"+
            "<td>"+b.specifications+"</td>"+
            "<td>"+b.create_name+"</td>"+
            "<td>"+b.unit+"</td>"+
            "<td>"+b.current_stock+"</td>"+
            "<td>"+myFunction(b.location_number)+"</td>"+
        "</tr>"
    );
    if(c == null || c == undefined){
        $("#currentStation").hide();
        $("#onlyNumInfo").show();
    }else{
        $("#currentStation").show();
        $("#onlyNumInfo").hide();
    }
    $("#holdStockSee #currentStation").html(
        "<table class='ty-table ty-table-control gap'>"+
        "<tr>"+
            "<td class='td-orange' rowspan='2'>"+"现状"+"</td>"+
            "<td>"+"Y3-01"+"</td>"+
        "</tr>"+
        "<tr>"+
            "<td>"+b.current_stock+"</td>"+
        "</tr>"
    );
}


















// creator: sy, 2022-02-21 ,筛选弹出框显示
function AutoComplete(){
    // var autoNode=$("#"+auto);//缓存对象（弹出框）
    // var curvalue=$("#search_text").val();
    // var carlist=[];
    // for(var i=0;i<mylist.length;i++){
    //     if(mylist[i]["name"].indexOf(curvalue)>=0||mylist[i]["code"].indexOf(curvalue)>=0){
    //         carlist.push(mylist[i]);
    //     }
    // }
}
var test=[
    {"id":1,"code":"代号","name":"供应商的名字"},
    {"id":2,"code":"102","name":"小苏"},
    {"id":3,"code":"103","name":"小苏1"},
    {"id":4,"code":"104","name":"小苏2"},
    {"id":5,"code":"105","name":"小苏3"},
    {"id":6,"code":"106","name":"小苏4"},
    {"id":7,"code":"107","name":"小苏5"},
];
console.log(test);

function rendertest(){
    let str='';
    test.forEach(function(value, index){
        // console.log(value);
        // console.log(index);
        str +=`
            <div>
                <option>${value.name}></option>
            </div>
         
        `
    });
    return str;
}





















