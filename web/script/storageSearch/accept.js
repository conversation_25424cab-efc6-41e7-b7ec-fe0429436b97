
$(function () {
    showPageNum(1);
    $("body").on("click",".funBtn, .ty-btn, .linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
})

// creator:hxz 2022-02-28 显示页面
function showPageNum(num){
    $(".page>div").hide();
    num? $(".page" + num).show(): '' ;
}

// creator:hxz 2022-02-28 返回成品主页
function goMain() {
    showPageNum(1)
}
// creator:hxz 2022-02-28 搜索按钮
function searchBtn() {
    showPageNum(2)
    getList(1, $("#search_text").val());
}

function getList(cur,param) {
    param = param || ''
    $.ajax({
        "url":"../finished/search.do",
        "data":{
            "param": param,
            "currPage": cur,
            "pageSize": 20 ,
        },
        success:function(res){
            let list = res.data || [];
            let str = ``;
            $("#gsTb tr:gt(0)").remove()

            list.forEach(function (item) {
                str += `
                    <tr data-id="${ item.id }">
                        <td> ${item.innerSn} </td>
                        <td> ${item.name} </td>
                        <td> ${item.model} </td> 
                        <td> ${item.specifications} </td> 
                        <td> ${item.createName} ${new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss") }  </td> 
                        <td>计量单位</td>
                        <td> ${item.minimumiStock} </td> 
                        <td> ${item.currentStock} </td> 
                        <td> ${item.initialStock} </td> 
                        <td>
                            <span class="ty-color-blue funBtn" data-fun="stockHold">${ item.num || 0 }个</span>
                        </td>
                    </tr>
                `
            })
            $("#gsTb tbody").append(str)
            setPage($("#ye"), cur, res.totalPage, 'acceptStorageSearch', JSON.stringify({ 'param': param }));
        }
    })
}
// creator: 李玉婷，2020-01-21 11:02:34，占用库位
function stockHold(obj) {
    $("#holdStockInfo tbody").html("");
    var getId = obj.parents("tr").data("id");
    $("#holdStockSee").data("id", getId);
    setHoldData(getId);
}
// creator: 李玉婷，2020-02-24 21:49:33，获取库位数据
function setHoldData(itemId){
    $("#currentStation").html("");
    $.ajax({
        url:"../finished/getLocationListByProduct.do" ,
        data:{
            id: itemId
        },
        success:function(data){
            if (data.code == 200) {
                var info = data.data;
                var list = info.list;
                var goodsInfo =
                    '<tr>' +
                    '    <td>'+ handleNull(info.innerSn) +'</td>' +
                    '    <td>'+ handleNull(info.name) +'</td>' +
                    '    <td>'+ handleNull(info.model) +'</td>' +
                    '    <td>'+ handleNull(info.specifications) +'</td>' +
                    '    <td class="createInfo">'+ info.createName + '&nbsp;&nbsp;' + new Date(info.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                    '    <td>'+ info.unit +'</td>' +
                    '    <td>'+ info.currentStock +'</td>' +
                    '    <td>'+ info.num +'个</td>' +
                    '</tr>';
                $("#holdStockInfo tbody").html(goodsInfo);
                if (list.length > 0){
                    var len = Math.ceil(list.length/10);
                    var tabStr = '';
                    for(var m=0;m<len;m++){
                        tabStr +=
                            '<table class="ty-table ty-table-control gap">' +
                            '   <tr>' +
                            '       <td rowspan="2" class="td-orange">现况</td>';
                        var size = 8;
                        var render = list.length%8;
                        var locationCode = '<td width="60">库位</td>',locationNum ='<tr><td>数量</td>';
                        if (m==len-1 && render>0) {
                            size = render;
                        }
                        for(var n=0;n<size;n++){
                            var i = m*size + n;
                            locationCode += '<td>'+ handleNull(list[i].locationCode) +'</td>';
                            locationNum += '<td>'+ handleNull(list[i].amount) +'</td>';
                        }
                        locationNum += '</tr>'
                        tabStr += locationCode + locationNum +
                            '</tr></table>';
                    }
                    $("#onlyNumInfo").hide();
                    $("#currentStation").html(tabStr);
                    $("#holdStockSeeReset").html("重新选择库位");
                }else{
                    $("#onlyNumInfo").show();
                    $("#holdStockSeeReset").html("去选择库位");
                }
                bounce.show($("#holdStockSee"));
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}






