$(function () {
    console.log(formatJson({
        "data":null,
        "ba":null,
        'dsf':48
    }))
    getStockMode()
    getDeliveryList(1,20,0);
    // 三种状态切换（待处理、已批准、已驳回）
    $(".ty-secondTab li").on("click",function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        //获取点击按钮的下标
        var index = $(this).index();
        //展示对应的内容
        switch (index){
            case 0:
                getDeliveryList(1,20,0);
                break;
            case 1:
                getDeliveryList(1,20,2);
                break;
            case 2:
                getDeliveryList(1,20,3);
                break;
            case 3:
                getDeliveryList(1,20,8);
                break;
            default:
                getDeliveryList(1,20,0);
        }
        $(".tblContainer").eq(index).show().siblings(".tblContainer").hide();
    });
    $("#apply_chooseGood").on("click",".ty-form-checkbox",function () {
        bounce.cancel();
        bounce.show($("#outStorageApply_query"));
        var orderId = $(this).parents("tr").attr("oid");
        var customerName = $(this).parents("tr").find("td").eq(12).text();
        var addressId  = $(this).parents("tr").find("td").eq(13).attr("aid")
        outStorageApply_filter(orderId,customerName,addressId);
    })
    $(".transport").on("click",function () {
        var transport = $(this).text();
        var transInfoStr = '';
        switch (transport){
            case "快递":
                transInfoStr =  '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="A">快递</td>' +
                                '   <td>快递单号：</td>' +
                                '   <td><input type="text" class="sn"></td>' +
                                '   <td>快递公司：</td>' +
                                '   <td><input type="text" class="company"></td>' +
                                '   <td>交寄日期：</td>' +
                                '   <td><input type="text" class="time" id="arriveTime"></td>' +
                                '</tr>' ;
                break;
            case "随身携带":
                transInfoStr=   '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="B">随身携带</td>' +
                                '   <td>携带者：</td>' +
                                '   <td><input type="text" class="operator"></td>' +
                                '   <td>联系方式：</td>' +
                                '   <td><input type="text" class="telephone"></td>' +
                                '   <td>备注：</td>' +
                                '   <td><input type="text" class="memo"></td>' +
                                '</tr>' ;
                break;
            case "货运":
                transInfoStr =  '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="C">货运</td>' +
                                '   <td>联系人：</td>' +
                                '   <td><input type="text" class="operator"></td>' +
                                '   <td>联系电话：</td>' +
                                '   <td><input type="text" class="telephone"></td>' +
                                '   <td>牌照号码：</td>' +
                                '   <td><input type="text" class="licenseTag"></td>' +
                                '</tr>'+
                                '<tr>' +
                                '   <td></td>' +
                                '   <td></td>' +
                                '   <td>乘运公司：</td>' +
                                '   <td><input type="text" class="company"></td>' +
                                '   <td>货运单号：</td>' +
                                '   <td><input type="text" class="sn"></td>' +
                                '   <td>备注：</td>' +
                                '   <td><input type="text" class="memo"></td>' +
                                '</tr>';
                break;
        }
        var handleStr = '<span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>' +
                        '<span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="sureLogistic()">确定</span>';
        $("#logisticInfo .handle").html(handleStr);
        $("#logisticInfo .transInfo tbody").html(transInfoStr);
        if (transport === '快递') {
            laydate.render({elem: '#arriveTime', type: 'date'});
        }
        $("#logisticInfo .transInfo").show();
        $("#logisticInfo .transDetail").hide();
        $("#logisticInfo .chooseWay").hide();
    })
    $("#logisticInfo").on("click","#arriveTime",function () {
        laydate.render({elem: '#arriveTime', type: 'datetime'});
    })
    $("#apply_query").on("click",".ty-form-checkbox",function () {
        $(this).toggleClass("ty-form-checked");
    })
});

// creator: 张旭博，2025-05-12 10:15:15， 获取是否为简易模式
function getStockMode(code) {
    $.ajax({
        url: $.webRoot + '/skl/getStockMode?code=finishedProductCheck',
    }).then(res => {
        let data = res.status;
        $("body").data('mode', data)
    })
}

/* creator：张旭博，2017-11-15 16:50:37，点击出库申请按钮（展示出库申请-选择商品弹窗；获取待处理列表*/
function outStorageApply_chooseGoodBtn() {
    bounce.show($("#outStorageApply_chooseGood"));
    $.ajax({
        url:"../skl/deliveryList.do" ,
        data:{},
        success:function( data ){
            //设置分页
            // var mycurr      = data.currPage;
            // var ttlPage     = data.totalPage;

            var deliveryList    = data["data"];
            var deliveryListStr = '';
            if(deliveryList !== undefined && deliveryList !== null) {
                for (var i = 0; i < deliveryList.length; i++) {
                    deliveryListStr += '<tr id="'+deliveryList[i].itemId+'" oid="'+deliveryList[i].orderId+'">' +
                        '<td>' + deliveryList[i].outer_sn + '</td>' +        //商品代号
                        '<td>' + deliveryList[i].outer_name + '</td>' +      //商品名称
                        '<td>' + deliveryList[i].inner_sn + '</td>' +        //产品图号
                        '<td>' + deliveryList[i].NAME + '</td>' +            //产品名称
                        '<td>' + deliveryList[i].unit + '</td>' +            //单位
                        '<td>' + deliveryList[i].current_stock + '</td>' +   //当前库存
                        '<td>' +formatTime( deliveryList[i].delivery_date , false ) + '</td>' +   //要求到货日期
                        '<td>' + deliveryList[i].amount + '</td>' +          //要货数量
                        '<td>' + chargeNumb(deliveryList[i].available_stock) + '</td>' + //可选数量
                        '<td>' + chargeNumb(deliveryList[i].out_amount) + '</td>' +      //已出库总数
                        '<td>' + (deliveryList[i].sn || "") + '</td>' +              //订单号
                        '<td>' + formatTime( deliveryList[i].create_date , true ) + '</td>' +     //原始录入时间
                        '<td>' + deliveryList[i].customer_name + '</td>' +   //客户名称
                        '<td aid="'+deliveryList[i].address_id+'">' + deliveryList[i].delivery_address + '</td>' +         //收货地点
                        '<td>' + formatTime(deliveryList[i].update_date , true ) + '</td>' +     //订单修改时间
                        '<td>' +
                        '   <div class="ty-form-checkbox" skin="green">' +
                        '   <i class="fa fa-check"></i>' +
                        '   </div>' +
                        '</td>' +        //商品代号
                        '</tr>'
                }
                $("#apply_chooseGood tbody").html(deliveryListStr);
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-15 16:50:37，勾选某条待处理数据（展示出库申请-筛选弹窗；根据条件获取待处理列表*/
function outStorageApply_filter(orderId,customerName,addressId ) {
    $.ajax({
        url:"../skl/getItemListByCondition.do" ,
        data:{
            "orderId":orderId,
            "addressId":addressId ,
            "customerName":customerName,
            "pageNum":1,
            "per":20
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            //设置分页
            // var mycurr      = data.currPage;
            // var ttlPage     = data.totalPage;

            var deliveryList    = data["data"];
            var deliveryListStr = '';
            if(deliveryList !== undefined && deliveryList !== null) {
                for (var i = 0; i < deliveryList.length; i++) {
                    deliveryListStr += '<tr id="'+deliveryList[i].itemId+'" oid="'+orderId+'">' +
                        '<td>' + deliveryList[i].outer_sn + '</td>' +        //商品代号
                        '<td>' + deliveryList[i].outer_name + '</td>' +      //商品名称
                        '<td>' + deliveryList[i].inner_sn + '</td>' +        //产品图号
                        '<td>' + deliveryList[i].NAME + '</td>' +            //产品名称
                        '<td>' + deliveryList[i].unit + '</td>' +            //单位
                        '<td>' + deliveryList[i].current_stock + '</td>' +   //当前库存
                        '<td>' + formatTime( deliveryList[i].delivery_date , false ) + '</td>' +   //要求到货日期
                        '<td>' + deliveryList[i].amount + '</td>' +          //要货数量
                        '<td>' + chargeNumb(deliveryList[i].available_stock) + '</td>' + //可选数量
                        '<td>' + chargeNumb(deliveryList[i].out_amount) + '</td>' +      //已出库总数
                        '<td>' + chargeNull(deliveryList[i].sn) + '</td>' +              //订单号
                        '<td>' + formatTime( deliveryList[i].create_date , true ) + '</td>' +     //原始录入时间
                        '<td>' + deliveryList[i].customer_name + '</td>' +   //客户名称
                        '<td>' + deliveryList[i].delivery_address + '</td>' +         //收货地点
                        '<td>' + formatTime(deliveryList[i].update_date , true ) + '</td>' +     //订单修改时间
                        '<td>' +
                        '   <div class="ty-form-checkbox ty-form-checked" skin="green">' +
                        '   <i class="fa fa-check"></i>' +
                        '   </div>' +
                        '</td>' +        //商品代号
                        '<td class="data" style="display: none">' + JSON.stringify(deliveryList[i]) + '</td>' +   //客户名称
                        '<td class="base" style="display: none">' + JSON.stringify(data.base[0]) + '</td>' +   //客户名称
                        '</tr>'
                }
                $("#apply_query tbody").html(deliveryListStr);
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-15 16:58:50，出库申请 - 筛选后跳转的申请页面 */
function outStorageApplyBtn() {
    bounce_Fixed.show($("#outStorageApply"));
    $("#outStorageApply").find("input").val("");
    $("#deliveryWay").val("");


    var deliverySureStr = '';
    var base = JSON.parse($("#apply_query .base").eq(0).html());
    $("#outStorageApplyBase .customerName").html(chargeNull(base.name));
    $("#outStorageApplyBase .customerCode").html(chargeNull(base.code));
    $("#outStorageApplyBase .address").html(chargeNull(base.address));
    $("#outStorageApplyBase .contact").html(chargeNull(base.contact));
    $("#outStorageApplyBase .mobile").html(chargeNull(base.mobile));
    $("#outStorageApplyBase .sn").html(chargeNull(base.sn));
    $("#outStorageApplyBase .create_date").html(formatTime(base.create_date,true));
    $("#outStorageApplyBase .update_date").html(formatTime(base.update_date,true));

    $("#apply_query .ty-form-checked").each(function () {
        var deliveryItemData = JSON.parse($(this).parent().siblings(".data").html());
        deliverySureStr +=  '<tr id="'+deliveryItemData.itemId+'" oid="'+deliveryItemData.orderId+'" pack="'+deliveryItemData.pack+'">'+
                            '<td>' + chargeNull(deliveryItemData.outer_sn) + '</td>' +          //商品代号
                            '<td>' + chargeNull(deliveryItemData.outer_name)  + '</td>' +       //商品名称
                            '<td>' + chargeNull(deliveryItemData.inner_sn)  + '</td>' +         //产品图号
                            '<td>' + chargeNull(deliveryItemData.NAME)  + '</td>' +             //产品名称
                            '<td>' + chargeNull(deliveryItemData.unit)  + '</td>' +             //单位
                            '<td>' + chargeNumb(deliveryItemData.current_stock)  + '</td>' +    //当前库存
                            '<td>' + formatTime( deliveryItemData.delivery_date , false ) + '</td>' +   //要求到货日期
                            '<td class="amount">' + chargeNumb(deliveryItemData.amount)  + '</td>' +           //要货数量
                            '<td class="availableStock">' + chargeNumb(deliveryItemData.available_stock)  + '</td>' +  //可选数量
                            '<td class="outAmount">' + chargeNumb(deliveryItemData.out_amount)  + '</td>' +       //已出库总数
                            '<td><input type="text" class="outPlan" onkeyUp = "clearNoNum(this)"/></td>' +                   //计划出库数量
                            '<td class="goodNum_stock"></td>' +                                 //货物件数
                            '<td>' +
                                '<span class="ty-color-red" onclick="deleteGood($(this))">删除</span>'+
                            '</td>'+
                            '</tr>'
    });
    $("#apply-sure tbody").html("").append(deliverySureStr);
    $('body').everyTime('0.5s','pack',function(){
        var index = 1;
        var packs_amount = 0;
        var items_account = $("#apply-sure tbody tr").length;

        $("#apply-sure tbody tr").each(function () {
            //计算包装
            var pack = $(this).attr("pack");        //包装信息（格式：1,2,4）
            var packNum = 1;                        //包装数量（1x2x4=8）
            var pack_amount = 0;                    //货物件数

            var outPlan = $(this).find(".outPlan").val();

            // 计划出库数 < 要货数量 - 已出库总数 && 计划出库数 < 可选数量
            var amount = Number($(this).find(".amount").html());          //要货数量
            var availableStock = Number($(this).find(".availableStock").html());  //可选数量
            var outAmount = Number($(this).find(".outAmount").html());        //已出库总数

            //计算包装数量
            if(pack !== "null" && pack !== undefined && pack !== ""){
                var packObj = pack.split(",");
                for(var i=0;i<packObj.length;i++){
                    packNum  *= Number(packObj[i]);
                }
            }else{
                packNum = 1;
            }
            //计算货物件数
            if(outPlan !== "" ){
                pack_amount = outPlan/packNum;
                pack_amount = Number(Math.ceil(pack_amount))
            }else{
                pack_amount = 0;
            }
            var packNow = $(this).find(".goodNum_stock").html();
            if(pack_amount !== packNow){
                $(this).find(".goodNum_stock").html(pack_amount);
            }
            packs_amount += pack_amount;

            //输入的计划出库数量 逻辑判断
            if(outPlan !== ""){
                outPlan = Number(outPlan)
                var cel = amount - outAmount
                cel = Number(cel.toFixed(4))
                if(outPlan > cel || outPlan > availableStock){
                    index = 0;
                }
            }
            if(outPlan === ""){
                index = 0;
            }

        });

        //统计商品种类和件数
        figureGoodInfo($("#outStorageApply"));
        // $("#outStorageApply .countAll .item_amount").html(items_account);
        // $("#outStorageApply .countAll .pack_amount").html(packs_amount);

        //提交按钮判断
        let mode = $("body").data('mode')
        if (mode === 0) {
            // 简易模式
            if($("#deliveryDate").val() !== "" && items_account>0){
                $("#addGoodsBtn").prop("disabled",false);
            }else{
                $("#addGoodsBtn").prop("disabled",true);
            }
        } else {
            if($("#deliveryDate").val() !== "" && index === 1 && items_account>0){
                $("#addGoodsBtn").prop("disabled",false);
            }else{
                $("#addGoodsBtn").prop("disabled",true);
            }
        }
    });
}

/* creator：张旭博，2017-11-16 10:14:48，出库申请-提交 */
function outStorageApply(state) {
    // orderId  int  订单号
    // itemList String  包含所选订单明细的json串，
    // -传参示例：[{"itemId":1,"outPlan":"30"},{"itemId":1,"outPlan":"50"}]
    // -内部参数说明：itemId:订单明细id   outPlan:计划出库数量
    // deliveryDate  String  计划出库日期  yyyy-MM-dd
    // arriveDate    String  计划到达日期  yyyy-MM-dd
    // carrier   String   搬运负责人
    // deliveryWay  String  计划的发货方式
    var orderId = '',
        tpaId = '',
        deliveryDate = '',
        arriveDate = '',
        carrier = '',
        deliveryWay = '',
        itemList = [],
        paramData = {};

    if(state === 1){
        orderId         = $("#outStorageChange .tblList tbody tr").eq(0).attr("oid");
        deliveryDate    = $("#change_deliveryDate").val();    //计划出库日期
        arriveDate      = $("#change_arriveDate").val();       //计划到达日期
        carrier         = $("#change_carrier").val();        //搬运负责人
        deliveryWay     = $("#change_deliveryWay").val();      //计划到达日期
        $("#outStorageChange .tblList tbody tr").each(function () {
            itemList.push({
                "itemId":$(this).attr("id"),
                "inoutStockId":$(this).attr("tid"),
                "outPlan":$(this).find(".outPlan").val(),
                "packNum":$(this).find(".goodNum_stock").html()
            });
        });
    }else{
        orderId         = $("#apply_query tbody tr").eq(0).attr("oid");
        deliveryDate    = $("#deliveryDate").val();     //计划出库日期
        arriveDate      = $("#arriveDate").val();       //计划到达日期
        carrier         = $("#carrier").val();          //搬运负责人
        deliveryWay     = $("#deliveryWay").val();       //计划到达日期
        $("#apply-sure tbody tr").each(function () {
            itemList.push({
                "itemId":$(this).attr("id"),
                "outPlan":$(this).find(".outPlan").val(),
                "packNum":$(this).find(".goodNum_stock").html()
            });
        });
    }

    paramData = {
        "orderId":orderId,
        "itemList":JSON.stringify(itemList),
        "deliveryDate":deliveryDate,
        "arriveDate":arriveDate,
        "carrier":carrier,
        "deliveryWay":deliveryWay
    };

    if(state === 1){
        tpaId = $("#outStorageChange .bonceFoot").attr("tid");
        paramData["tpaId"] = tpaId;
    }
    $.ajax({
        url:"../skl/outApply.do" ,
        data:paramData,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var code = data["code"];
            bounce_Fixed.cancel();
            if(code === 400){
                $("#F_errorTip .tipWord").html(data["message"]) ;
                bounce_Fixed.show($("#F_errorTip")) ;
            }else if(code === 200){
                bounce.cancel();
                if(state === 1){
                    layer.msg("修改成功！")
                }else{
                    layer.msg("申请成功！")
                }
                $(".ty-secondTab .ty-active").click();
            }
        },
        error:function(){
            $("#F_errorTip .tipWord").html("系统错误，请重试!") ;
            bounce_Fixed.show($("#F_errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-21 09:27:40，已作废的发货通知 */
function invalidStorageApplyBtn() {
    bounce.show($("#invalidStorageApply"));
    $.ajax({
        url:"../skl/cancelOrder.do" ,
        data:{},
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var cancelList = data["data"],
                cancelListStr = '';
            if(cancelList !== null && typeof (cancelList) !== "undefined" && cancelList !== ''){
                for(var i=0;i<cancelList.length;i++){
                    cancelListStr +=  '<tr>'+
                        '<td>' + chargeNull(cancelList[i].outer_sn) + '</td>' +        //商品代号
                        '<td>' + chargeNull(cancelList[i].outer_name) + '</td>' +      //商品名称
                        '<td>' + chargeNull(cancelList[i].inner_sn) + '</td>' +        //产品图号
                        '<td>' + chargeNull(cancelList[i].NAME) + '</td>' +            //产品名称
                        '<td>' + chargeNull(cancelList[i].unit) + '</td>' +            //单位
                        '<td>' + chargeNull(cancelList[i].current_stock) + '</td>' +   //当前库存
                        '<td>' +formatTime( cancelList[i].delivery_date , false ) + '</td>' +   //要求到货日期
                        '<td class="amount">' + chargeNumb(cancelList[i].amount) + '</td>' +          //要货数量
                        '<td class="outAmount">' + chargeNumb(cancelList[i].out_amount) + '</td>' +      //已出库总数
                        '<td>' + cancelList[i].sn + '</td>' +              //订单号
                        '<td>' + formatTime( cancelList[i].create_date , true ) + '</td>' +     //原始录入时间
                        '<td>' + cancelList[i].customer_name + '</td>' +   //客户名称
                        '<td>' + cancelList[i].address + '</td>' +         //收货地点
                        '<td>' + formatTime(cancelList[i].update_date , true ) + '</td>' +     //订单修改时间
                        '</tr>'
                }
            }
            $("#invalidStorageApply .bonceCon tbody").html(cancelListStr);
        },
        error:function(){
            $("#F_errorTip .tipWord").html("系统错误，请重试!") ;
            bounce_Fixed.show($("#F_errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-21 09:29:41，出库申请-待出库-修改-按钮 */
function outStorageChangeBtn(selector) {
    bounce.show($("#outStorageChange"));
    var base = JSON.parse(selector.parent().siblings(".base").html()),
        deliveryItemData = base.dataList,
        deliveryStr = '';

    $("#outStorageChange .bonceFoot").attr("tid",selector.parent().parent().attr("id"));
    $("#outStorageChangeBase .customerName").html(base.customer_name);
    $("#outStorageChangeBase .customerCode").html(base.code);
    $("#outStorageChangeBase .address").html(base.delivery_address);
    $("#outStorageChangeBase .contact").html(base.contact);
    $("#outStorageChangeBase .mobile").html(base.mobile);
    $("#outStorageChangeBase .sn").html(base.sn);
    $("#outStorageChangeBase .create_date").html(formatTime(base.create_date,true));
    $("#outStorageChangeBase .update_date").html(formatTime(base.update_date,true));
    $("#outStorageChangeBase #change_deliveryDate").val(formatTime( base.tpa_delivery_date,false));
    $("#outStorageChangeBase #change_carrier").val(base.carrier);
    $("#outStorageChangeBase #change_arriveDate").val(formatTime( base.tpa_arrive_date,false));
    $("#outStorageChangeBase #change_deliveryWay").val(base.tpa_delivery_way);


    for(var i=0;i<deliveryItemData.length;i++){
        deliveryStr +=  '<tr id="'+deliveryItemData[i].itemId+'" oid="'+deliveryItemData[i].orderId+'" pack="'+deliveryItemData[i].pack+'" tid="'+deliveryItemData[i].tik_id+'">'+
                        '<td>' + chargeNull(deliveryItemData[i].outer_sn) + '</td>' +        //商品代号
                        '<td>' + chargeNull(deliveryItemData[i].outer_name) + '</td>' +      //商品名称
                        '<td>' + chargeNull(deliveryItemData[i].inner_sn) + '</td>' +        //产品图号
                        '<td>' + chargeNull(deliveryItemData[i].NAME) + '</td>' +            //产品名称
                        '<td>' + chargeNull(deliveryItemData[i].unit) + '</td>' +            //单位
                        '<td>' + chargeNumb(deliveryItemData[i].current_stock) + '</td>' +   //当前库存
                        '<td>' +formatTime( deliveryItemData[i].delivery_date , false ) + '</td>' +   //要求到货日期
                        '<td class="amount">' + chargeNumb(deliveryItemData[i].amount) + '</td>' +          //要货数量
                        '<td class="availableStock">' + chargeNumb(deliveryItemData[i].available_stock) + '</td>' + //可选数量
                        '<td class="outAmount">' + chargeNumb(deliveryItemData[i].out_amount) + '</td>' +      //已出库总数
                        '<td><input type="text" class="outPlan" value="'+deliveryItemData[i].out_plan+'" onkeyUp = "clearNoNum(this)"/></td>' +        //计划出库数量
                        '<td class="goodNum_stock">'+deliveryItemData[i].pack_num+'</td>' +        //货物件数
                        '</tr>'
    }
    $("#outStorageChange .tblList tbody").html(deliveryStr);

    $('body').everyTime('1s','change_pack',function(){
        var index = 1;
        var packs_amount = 0;
        var items_account = $("#outStorageChange tbody tr").length;

        $("#outStorageChange .tblList tbody tr").each(function () {
            //计算包装
            var pack = $(this).attr("pack");        //包装信息（格式：1,2,4）
            var packNum = 1;                        //包装数量（1x2x4=8）
            var pack_amount = 0;                    //货物件数

            var outPlan = $(this).find(".outPlan").val();


            // 计划出库数 < 要货数量 - 已出库总数 && 计划出库数 < 可选数量
            var amount = $(this).find(".amount").html();          //要货数量
            var availableStock = $(this).find(".availableStock").html();  //可选数量
            var outAmount = $(this).find(".outAmount").html();        //已出库总数

            //计算包装数量
            if(pack !== "null" && pack !== undefined && pack !== ""){
                var packObj = pack.split(",");
                for(var i=0;i<packObj.length;i++){
                    packNum  *= Number(packObj[i]);
                }
            }
            //计算货物件数
            if(outPlan !== ""){
                pack_amount = outPlan/packNum;
                pack_amount = Number(Math.ceil(pack_amount));
            }else{
                pack_amount = 0;
            }
            var packNow = $(this).find(".goodNum_stock").html();
            if(pack_amount !== packNow){
                $(this).find(".goodNum_stock").html(pack_amount);
            }

            packs_amount += pack_amount;

            //输入的计划出库数量 逻辑判断
            if(outPlan !== ""){
                if(outPlan > amount - outAmount || outPlan > availableStock){
                    index = 0;
                }
            }
            if(outPlan === ""){
                index = 0;
            }
        });

        //统计商品种类和件数
        figureGoodInfo($("#outStorageChange"));

        //提交按钮判断
        if($("#change_deliveryDate").val() !== "" && index === 1 && items_account>0){
            $("#changeGoodsBtn").prop("disabled",false);
        }else{
            $("#changeGoodsBtn").prop("disabled",true);
        }

    });

}

/* creator：张旭博，2017-12-01 09:33:27，出库申请-待出库-查看-按钮 */
function outStorageOrderBtn(selector) {
    bounce.show($("#outStorageOrder"));
    var base = JSON.parse(selector.parent().siblings(".base").html());
    $("#outStorageOrder .applyAll").html('申请人： <span class="ty-color-blue">'+base.tpa_create_name+'</span> 申请时间：<span class="ty-color-blue">'+formatTime(base.tpa_create_date,true) +'</span>');
    $("#outStorageOrderBase .customerName").html(base.customer_name);
    $("#outStorageOrderBase .customerCode").html(base.code);
    $("#outStorageOrderBase .sn").html(base.sn);
    $("#outStorageOrderBase .deliveryDate").html(formatTime( base.tpa_delivery_date,false));
    var deliveryItemData = base.dataList;
    var deliveryStr = '';
    for(var i=0;i<deliveryItemData.length;i++){
        deliveryStr +=  '<tr id="'+deliveryItemData[i].itemId+'" oid="'+deliveryItemData[i].orderId+'" pack="'+deliveryItemData[i].pack+'">'+
            '<td>' + deliveryItemData[i].outer_sn + '</td>' +        //商品代号
            '<td>' + deliveryItemData[i].outer_name + '</td>' +      //商品名称
            '<td>' + deliveryItemData[i].inner_sn + '</td>' +        //产品图号
            '<td>' + deliveryItemData[i].NAME + '</td>' +            //产品名称
            '<td>' + deliveryItemData[i].unit + '</td>' +            //单位
            '<td>' + deliveryItemData[i].current_stock + '</td>' +   //当前库存
            '<td>' + deliveryItemData[i].out_plan + '</td>' +        //计划出库数量
            '<td class="goodNum_stock">'+deliveryItemData[i].pack_num+'</td>' +        //货物件数
            '</tr>'
    }
    $("#outStorageOrder .tblList tbody").html(deliveryStr);

    //统计商品种类和件数
    figureGoodInfo($("#outStorageOrder"));
}

/* creator：张旭博，2017-11-21 09:29:41，出库申请-复核-按钮 */
function outStorageCheckBtn(selector) {
    bounce.show($("#outStorageCheck"));
    var base = JSON.parse(selector.parent().siblings(".base").html());
    $("#outStorageCheckBase .customerName").html(base.customer_name);
    $("#outStorageCheckBase .customerCode").html(base.code);
    $("#outStorageCheckBase .address").html(base.delivery_address);
    $("#outStorageCheckBase .contact").html(base.contact);
    $("#outStorageCheckBase .mobile").html(base.mobile);
    $("#outStorageCheckBase .sn").html(base.sn);
    $("#outStorageCheckBase .create_date").html(formatTime( base.create_date , true ));
    $("#outStorageCheckBase .update_date").html(formatTime( base.update_date , true ));
    $("#outStorageCheckBase .deliveryDate").html(formatTime( base.tpa_delivery_date , false ));
    $("#outStorageCheckBase .carrier").html(base.carrier);
    $("#outStorageCheckBase .arriveDate").html(formatTime( base.tpa_arrive_date , false ));
    $("#outStorageCheckBase .deliveryWay").html(base.tpa_delivery_way);
    var deliveryItemData = base.dataList;
    var deliveryStr = '';
    for(var i=0;i<deliveryItemData.length;i++){
        var operate = deliveryItemData[i].approver_operate,
            tik_state = deliveryItemData[i].tik_state;
        var handleStr = ''
        if(operate === "1"){
            if(tik_state === '8'){
                handleStr = '已复核无误';
            }else{
                handleStr = '<span class="ty-color-blue" onclick="outStorageCheck($(this),1)">复核无误</span>'+
                            '<span class="ty-color-blue" onclick="outStorageCheck($(this),2)">更改数量</span>';
            }
        }else{
            if(tik_state === "8"){
                handleStr = '已复核无误';
            }else{
                handleStr = '<span class="ty-color-blue" onclick="outStorageCheck($(this),2)">更改数量</span>';
            }
        }
        deliveryStr +=  '<tr id="'+deliveryItemData[i].tik_id+'" oid="'+deliveryItemData[i].orderId+'" pack="'+deliveryItemData[i].pack+'">'+
                            '<td>' + deliveryItemData[i].outer_sn + '</td>' +                               //商品代号
                            '<td>' + deliveryItemData[i].outer_name + '</td>' +                             //商品名称
                            '<td>' + deliveryItemData[i].inner_sn + '</td>' +                               //产品图号
                            '<td>' + deliveryItemData[i].NAME + '</td>' +                                   //产品名称
                            '<td>' + deliveryItemData[i].unit + '</td>' +                                   //单位
                            '<td>' + deliveryItemData[i].current_stock + '</td>' +                          //当前库存
                            '<td>' + formatTime( deliveryItemData[i].delivery_date , false ) + '</td>' +    //要求到货日期
                            '<td>' + deliveryItemData[i].amount + '</td>' +                                 //要货数量
                            '<td>' + chargeNumb(deliveryItemData[i].available_stock) + '</td>' +                    //可选数量
                            '<td>' + chargeNumb(deliveryItemData[i].out_amount) + '</td>' +                  //已出库总数
                            '<td>' + chargeNumb(deliveryItemData[i].out_plan) + '</td>' +                    //计划出库数量
                            '<td class="goodNum_stock">' + chargeNumb(deliveryItemData[i].pack_num) + '</td>' +                    //货物件数
                            '<td>' + chargeApproveState(deliveryItemData[i].approver_operate) + '</td>' +   //仓库审批结果
                            '<td>' + formatTime( deliveryItemData[i].approve_time,true) + '</td>' +         //仓库审批时间
                            '<td>' +handleStr+ '</td>'+
                            '</tr>'
    }
    $("#outStorageCheck .tblList tbody").html(deliveryStr);

    //统计商品种类和件数
    figureGoodInfo($("#outStorageCheck"));

    $('body').everyTime('0.5s','pack',function(){
        $("#apply-sure tbody tr").each(function () {
            var pack = $(this).attr("pack");
            var packObj=pack.split(",");
            var packNum = 1;
            for(var i=0;i<packObj.length;i++){
                packNum = packNum * packObj[i];
            }
            var outPlan = $(this).find(".outPlan").val();
            if(pack !== undefined && outPlan !== ""){
                var goodNum_stock = outPlan/packNum
                $(this).find(".goodNum_stock").html(Number(Math.ceil(goodNum_stock)));
            }
        });
    });

}

/* creator：张旭博，2017-11-24 15:24:08，出库申请-复核无误与更改数量 */
function outStorageCheck(selector,operate) {
    var id = selector.parent().parent().attr("id");
    var pack = selector.parent().parent().attr("pack");
    if(operate === 1){
        bounce_Fixed.show($("#outStorageCheckOk"));
        $("#outStorageCheckOk .bonceFoot").attr("id",id);
    }else{
        bounce_Fixed.show($("#outStorageCheckNo"));
        $("#outStorageCheckNo .bonceFoot").attr("id",id);
        $("#outStorageCheckNo .bonceFoot").attr("pack",pack);
    }

}

/* creator：张旭博，2017-11-24 19:22:12，确认复核 */
function sureOutStorageCheck(selector,operate) {
    var id = selector.parent().attr("id");
    var pack = selector.parent().attr("pack");
    var data= {
        "id":id,
        "reviewer_operate":operate
    };
    if(operate === 2){
        var packObj = [];
        var packNum = 1;
        if(pack !== "null" && pack !== undefined && pack !== ""){
            for(var i=0;i<packObj.length;i++){
                packNum  *= Number(packObj[i]);
            }
        }
        var outPlan = $("#outStorageCheckNo .amount").val();
        if(pack !== undefined && outPlan !== ""){
            data["amount"] = outPlan;
            var pack = outPlan/packNum
            data["packNum"] = Number(pack.toFixed(4));
        }
    }
    $.ajax({
        url:"../skl/outApplyStep.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            bounce_Fixed.cancel();
            if(data["code"] === 400){
                $("#F_errorTip .tipWord").html(data["message"]) ;
                bounce_Fixed.show($("#F_errorTip")) ;
            }else if(data["code"] === 200){
                bounce_Fixed.cancel();
                layer.msg(data["message"]);
                $("#outStorageCheck #"+id).find("td:last").html("已复核无误");
                $(".ty-secondTab .ty-active").click();
            }else{
                $("#F_errorTip .tipWord").html("错误的返回值！") ;
                bounce_Fixed.show($("#F_errorTip")) ;
            }
        },
        error:function(){
            $("#F_errorTip .tipWord").html("系统错误，请重试!") ;
            bounce_Fixed.show($("#F_errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-08 08:58:03，物流管理-》发货管理-》获取四个状态列表 */
function getDeliveryList(pageNum,per,state) {
    var data = {
        "pageNum":pageNum,
        "per":per
    };
    if(state !== 0){
        data["state"] = state
    }
    $.ajax({
        url:"../skl/deliveryList.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            var jsonStr = JSON.stringify({"state": state}) ;
            setPage( $("#ye_outStorage") , curr ,  totalPage , "outStorage", jsonStr );

            var deliveryList    = data["data"];
            var deliveryListStr = '';
            if(deliveryList !== undefined && deliveryList !== null) {
                for (var i = 0; i < deliveryList.length; i++) {

                    var deliveryItem        = formatJson(deliveryList[i]),

                        itemId              = deliveryItem.itemId,
                        orderId             = deliveryItem.orderId,
                        tpa_id              = deliveryItem.tpa_id,
                        outer_sn            = chargeNull(deliveryItem.outer_sn),
                        outer_name          = chargeNull(deliveryItem.outer_name),
                        inner_sn            = chargeNull(deliveryItem.inner_sn),
                        NAME                = chargeNull(deliveryItem.NAME),
                        unit                = chargeNull(deliveryItem.unit),
                        sn                  = chargeNull(deliveryItem.sn),
                        customer_name       = chargeNull(deliveryItem.customer_name),
                        delivery_address    = chargeNull(deliveryItem.delivery_address),
                        tpa_delivery_way    = chargeNull(deliveryItem.tpa_delivery_way),
                        carrier             = chargeNull(deliveryItem.carrier),
                        code                = chargeNull(deliveryItem.code),
                        tpa_create_name     = chargeNull(deliveryItem.tpa_create_name),

                        current_stock       = chargeNumb(deliveryItem.current_stock),
                        amount              = chargeNumb(deliveryItem.amount),
                        available_stock     = chargeNumb(deliveryItem.available_stock),
                        out_amount          = chargeNumb(deliveryItem.out_amount),
                        pack_amount         = chargeNumb(deliveryItem.pack_amount),
                        item_account        = chargeNumb(deliveryItem.item_account),

                        delivery_date       = formatTime(deliveryItem.delivery_date,false),
                        create_date         = formatTime(deliveryItem.create_date,false),
                        update_date         = formatTime(deliveryItem.update_date,false),
                        tpa_delivery_date   = formatTime(deliveryItem.tpa_delivery_date,false),
                        tpa_arrive_date   = formatTime(deliveryItem.tpa_arrive_date,false),
                        tpa_create_date     = formatTime(deliveryItem.tpa_create_date,true),
                        tpa_update_date     = formatTime(deliveryItem.tpa_update_date,true),
                        last_review_time    = formatTime(deliveryItem.last_review_time,true),
                        last_approve_time   = formatTime(deliveryItem.last_approve_time,true);

                    switch (state) {
                        case 0:
                            deliveryListStr += '<tr id="' + itemId + '" oid="' + orderId + '">' +
                                '<td>' + outer_sn + '</td>' +                       //商品代号
                                '<td>' + outer_name + '</td>' +                     //商品名称
                                '<td>' + inner_sn + '</td>' +                       //产品图号
                                '<td>' + NAME + '</td>' +                           //产品名称
                                '<td>' + unit + '</td>' +                           //单位
                                '<td>' + current_stock + '</td>' +                  //当前库存
                                '<td>' + delivery_date + '</td>' +                  //要求到货日期
                                '<td>' + amount + '</td>' +                         //要货数量
                                '<td>' + available_stock + '</td>' +                //可选数量
                                '<td>' + out_amount + '</td>' +                     //已出库总数
                                '<td>' + sn + '</td>' +                             //订单号
                                '<td>' + create_date + '</td>' +                    //原始录入时间
                                '<td>' + customer_name + '</td>' +                  //客户名称
                                '<td>' + delivery_address + '</td>' +               //收货地点
                                '<td>' + update_date + '</td>' +                    //订单修改时间
                                '</tr>';
                            break;
                        case 2:
                            deliveryListStr += '<tr id="' + tpa_id + '" oid="' + orderId + '">' +
                                '<td>' + tpa_delivery_date + '</td>' +          //计划出库日期
                                '<td>' + customer_name + '</td>' +              //客户名称
                                '<td>' + delivery_address + '</td>' +           //收货地址
                                '<td>' + tpa_delivery_way + '</td>' +           //计划的发货方式
                                '<td>' + sn + '</td>' +                         //订单号
                                '<td>' + item_account + '</td>' +               //商品类别总数
                                '<td>' + pack_amount + '</td>' +                //货物总件数
                                '<td>' + carrier + '</td>' +                    //搬运负责人
                                '<td>' + tpa_arrive_date + '</td>' +            //计划到达日期
                                '<td>' + tpa_create_date  + '</td>' +           //申请提交时间
                                '<td>' + tpa_update_date + '</td>' +            //申请最后修改时间
                                '<td>' + tpa_create_name + '</td>' +            //提交者
                                '<td class="base" style="display: none">'+JSON.stringify(deliveryItem)+'</td>'+
                                '<td>' +
                                '<span class="ty-color-blue" onclick="outStorageChangeBtn($(this))">修改</span>' +
                                '<span class="ty-color-blue" onclick="outStorageOrderBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                            break;
                        case 3:
                            deliveryListStr +=  '<tr>' +
                                '<td>' + tpa_delivery_date + '</td>' +          //计划出库日期
                                '<td>' + customer_name + '</td>' +              //客户名称
                                '<td>' + delivery_address + '</td>' +           //收货地址
                                '<td>' + tpa_delivery_way + '</td>' +           //计划的发货方式
                                '<td>' + sn + '</td>' +                         //订单号
                                '<td>' + item_account + '</td>' +               //商品类别总数
                                '<td>' + pack_amount + '</td>' +                //货物总件数
                                '<td>' + carrier + '</td>' +                    //搬运负责人
                                '<td>' + tpa_arrive_date + '</td>' +            //计划到达日期
                                '<td>' + tpa_create_date + '</td>' +            //申请提交时间
                                '<td>' + tpa_update_date + '</td>' +            //申请最后修改时间
                                '<td>' + tpa_create_name + '</td>' +            //提交者
                                '<td class="base" style="display: none">'+JSON.stringify(deliveryItem)+'</td>'+
                                '<td>' +
                                '<span class="ty-color-blue" onclick="outStorageCheckBtn($(this))">复核</span>' +
                                '</td>' +
                                '</tr>';
                            break;
                        case 8:
                            deliveryListStr +=  '<tr id="'+ tpa_id +'">' +
                                '<td>' + last_review_time + '</td>' +           //出库日期
                                '<td>' + customer_name + '</td>' +              //客户名称
                                '<td>' + code+ '</td>' +                        //客户代号
                                '<td>' + sn + '</td>' +                         //订单号
                                '<td>' + item_account + '</td>' +               //商品类别总数
                                '<td>' + pack_amount + '</td>' +                //货物总件数
                                '<td>' + tpa_arrive_date  + '</td>' +           //计划到达日期
                                '<td>' + tpa_create_date + '</td>' +            //申请提交时间
                                '<td>' + last_approve_time + '</td>' +          //仓库审批时间(最后)
                                '<td>' + last_review_time + '</td>' +           //复核时间(最后)
                                '<td>' + tpa_create_name + '</td>' +                        //提交者
                                '<td class="base" style="display: none">'+JSON.stringify(deliveryItem)+'</td>'+
                                '<td>' +
                                '<span class="ty-color-blue" onclick="outStorageOkBtn($(this))">查看</span>' +
                                '<span class="ty-color-blue" onclick="logisticInfoBtn($(this))">发运信息</span>' +
                                '</td>' +
                                '</tr>';
                            break;
                    }
                    switch (state) {
                        case 0:
                            $("#waitHandle tbody").html(deliveryListStr);
                            break;
                        case 2:
                            $("#waitOutStorage tbody").html(deliveryListStr);
                            break;
                        case 3:
                            $("#waitCheck tbody").html(deliveryListStr);
                            break;
                        case 8:
                            $("#alreadyOutStorage tbody").html(deliveryListStr);
                            break;
                    }
                }
            }else{
                $("#waitHandle tbody").html("");
                $("#waitOutStorage tbody").html("");
                $("#waitCheck tbody").html("");
                $("#alreadyOutStorage tbody").html("");
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-12-01 15:49:56，物流管理-》发货管理-》已出库-》查看-按钮  */
function outStorageOkBtn(selector){
    bounce.show($("#outStorageOk"));
    var base = JSON.parse(selector.parent().siblings(".base").html());
    $("#outStorageOkBase .customerName").html(base.customer_name);
    $("#outStorageOkBase .customerCode").html(base.code);
    $("#outStorageOkBase .address").html(base.delivery_address);
    $("#outStorageOkBase .contact").html(base.contact);
    $("#outStorageOkBase .mobile").html(base.mobile);
    $("#outStorageOkBase .sn").html(base.sn);
    $("#outStorageOkBase .create_date").html(formatTime( base.create_date , true ));
    $("#outStorageOkBase .update_date").html(formatTime( base.update_date , true ));
    $("#outStorageOkBase .approveTime").html(formatTime( base.last_review_time , true )); //出库日期
    $("#outStorageOkBase .carrier").html(base.carrier);
    $("#outStorageOkBase .arriveDate").html(formatTime( base.tpa_arrive_date , false ));
    $("#outStorageOkBase .deliveryWay").html(base.tpa_delivery_way);
    // $("#outStorageOk .countAll").html('本次计划出库共 <span class="ty-color-blue">'+base.item_account+'</span> 种商品，共 <span class="ty-color-blue">'+base.pack_amount+'</span> 件');
    $("#outStorageOk .applyAll").html(
        ' &nbsp;申请人： <span class="ty-color-blue">'+base.tpa_create_name+'</span> '+formatTime(base.tpa_create_date,true) +
        ' &nbsp;仓库： <span class="ty-color-blue">'+base.approver_name+'</span> '+formatTime( base.last_review_time , true ) +
        ' &nbsp;复核人： <span class="ty-color-blue">'+base.reviewer_name+'</span> '+formatTime(base.last_approve_time,true)

    );
    var deliveryItemData = base.dataList;
    var deliveryStr = '';
    for(var i=0;i<deliveryItemData.length;i++){
        deliveryStr +=  '<tr id="'+deliveryItemData[i].tik_id+'" oid="'+deliveryItemData[i].orderId+'" pack="'+deliveryItemData[i].pack+'">'+
            '<td>' + deliveryItemData[i].outer_sn + '</td>' +                           //商品代号
            '<td>' + deliveryItemData[i].outer_name + '</td>' +                         //商品名称
            '<td>' + deliveryItemData[i].inner_sn + '</td>' +                           //产品图号
            '<td>' + deliveryItemData[i].NAME + '</td>' +                               //产品名称
            '<td>' + deliveryItemData[i].unit + '</td>' +                               //单位
            '<td>' + formatTime( deliveryItemData[i].delivery_date , false ) + '</td>' +//要求到货日期
            '<td>' + deliveryItemData[i].out_plan + '</td>' +                           //出库数量
            '<td class="goodNum_stock">' + deliveryItemData[i].pack_num + '</td>' +                           //货物件数
            '<td>' + formatTime( deliveryItemData[i].approve_time,true) + '</td>' +     //仓库审批时间
            '<td>' + formatTime( deliveryItemData[i].review_time,true) + '</td>' +      //复核时间
            '</tr>'
    }
    $("#outStorageOk .tblList tbody").html(deliveryStr);

    //统计商品种类和件数
    figureGoodInfo($("#outStorageOk"))
}

/* creator：张旭博，2017-11-27 14:56:31，货运信息-按钮 */
function logisticInfoBtn(selector) {
    bounce.show($("#logisticInfo"));
    var outId = selector.parent().parent().attr("id");
    $("#logisticInfo .transInfo").attr("id",outId);
    getTransInfo();
}

/* creator：张旭博，2017-11-27 16:56:17，获取发运信息 */
function getTransInfo() {
    var outId   = $("#logisticInfo .transInfo").attr("id");
    $.ajax({
        url:"../skl/getShipmentState.do" ,
        data:{"outId":outId},
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            if(data.code && data.code === 400){
                $("#logisticInfo .transDetail").hide();
                $("#logisticInfo .chooseWay").show();
                $("#logisticInfo .transInfo").hide();
                $("#logisticInfo .handle").html("");
            }else {
                var transDetailStr = '';
                for (var i = 0; i < data.length; i++) {
                    var deliveryWay = data[i].deliveryWay;
                    var state = data[i].updateDate;
                    var dateStr = '';
                    if(state === ""){
                        dateStr = '录入日期';
                    }else{
                        dateStr = '修改日期';
                    }
                    switch (deliveryWay) {
                        case "A":
                            transDetailStr += '<tr>' +
                                '    <td>发运方式：</td>' +
                                '    <td>快递</td>' +
                                '    <td>快递单号：</td>' +
                                '    <td>' + data[i].sn + '</td>' +
                                '    <td>快递公司：</td>' +
                                '    <td>' + data[i].company + '</td>' +
                                '    <td>交寄日期：</td>' +
                                '    <td>' + (new Date(data[i].deliveryTime).format('yyyy-MM-dd'))  + '</td>' +
                                '    <td>'+dateStr+'</td>' +
                                '    <td>' + (new Date(data[i].createDate).format('yyyy-MM-dd')) + '</td>' +
                                '</tr>';
                            break;
                        case "B":
                            transDetailStr += '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="B">随身携带</td>' +
                                '   <td>携带者：</td>' +
                                '   <td>' + data[i].operator + '</td>' +
                                '   <td>联系方式：</td>' +
                                '   <td>' + data[i].telephone + '</td>' +
                                '   <td>备注：</td>' +
                                '   <td>' + data[i].memo + '</td>' +
                                '   <td>'+dateStr+'</td>' +
                                '   <td>' + (new Date(data[i].createDate).format('yyyy-MM-dd')) + '</td>' +
                                '</tr>';
                            break;
                        case "C":
                            transDetailStr += '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="C">货运</td>' +
                                '   <td>联系人：</td>' +
                                '   <td>' + data[i].operator + '</td>' +
                                '   <td>联系电话：</td>' +
                                '   <td>' + data[i].telephone + '</td>' +
                                '   <td>牌照号码：</td>' +
                                '   <td>' + data[i].licenseTag + '</td>' +
                                '</tr>' +
                                '<tr>' +
                                '   <td></td>' +
                                '   <td></td>' +
                                '   <td>乘运公司：</td>' +
                                '   <td>' + data[i].company + '</td>' +
                                '   <td>货运单号：</td>' +
                                '   <td>' + data[i].sn + '</td>' +
                                '</tr>'+
                                '<tr>' +
                                '   <td></td>' +
                                '   <td></td>' +
                                '   <td>备注：</td>' +
                                '   <td colspan="5">'+data[i].memo +'</td>' +
                                '   <td>'+dateStr+'</td>' +
                                '   <td>' + (new Date(data[i].createDate).format('yyyy-MM-dd')) + '</td>' +
                                '</tr>';
                            break;
                    }
                }
                var handelStr = '<span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="changeTransportation(0)">增加发运方式</span>'+' '+
                                '<span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="changeTransportation(1)">修改发运方式</span>';
                $("#logisticInfo .transDetail tbody").html(transDetailStr);
                $("#logisticInfo .transDetail").show();
                $("#logisticInfo .chooseWay").hide();
                $("#logisticInfo .transInfo").hide();
                $("#logisticInfo .handle").html(handelStr);
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-28 11:12:37，更改发运方式 */
function changeTransportation(state) {
    $("#logisticInfo .transDetail").hide();
    $("#logisticInfo .chooseWay").show();
    $("#logisticInfo .transInfo").hide();
    $("#logisticInfo .transInfo").attr("value",state);
    $("#logisticInfo .handle").html("");
}

/* creator：张旭博，2017-11-27 17:05:24，提交发运信息 */
function sureLogistic() {
    var deliveryWay = $("#logisticInfo .transInfo .deliveryWay").attr("value");
    var update = $("#logisticInfo .transInfo").attr("value");
    var outId   = parseInt($("#logisticInfo .transInfo").attr("id"));
    var data = {};
    switch(deliveryWay){
        case "A":
            var sn          = $("#logisticInfo .transInfo .sn").val();
            var company     = $("#logisticInfo .transInfo .company").val();
            var time        = $("#logisticInfo .transInfo .time").val();
            data = {
                "application_":outId,
                "deliveryWay":deliveryWay,
                "sn":sn,
                "company":company,
                "time":time
            };
            break;
        case "B":
            var operator    = $("#logisticInfo .transInfo .operator").val();
            var telephone   = $("#logisticInfo .transInfo .telephone").val();
            var memo        = $("#logisticInfo .transInfo .memo").val();
            data = {
                "application_":outId,
                "deliveryWay":deliveryWay,
                "operator":operator,
                "telephone":telephone,
                "memo":memo
            };
            break;
        case "C":
            var operator    = $("#logisticInfo .transInfo .operator").val();
            var telephone   = $("#logisticInfo .transInfo .telephone").val();
            var licenseTag  = $("#logisticInfo .transInfo .licenseTag").val();
            var company     = $("#logisticInfo .transInfo .company").val();
            var sn          = $("#logisticInfo .transInfo .sn").val();
            var memo        = $("#logisticInfo .transInfo .memo").val();
            data = {
                "application_":outId,
                "deliveryWay":deliveryWay,
                "operator":operator,
                "telephone":telephone,
                "licenseTag":licenseTag,
                "company":company,
                "sn":sn,
                "memo":memo
            };
            break;
    }
    if(update === "1"){
        data["update"] = 1;
    }
    $.ajax({
        url:"../skl/addOutShipment.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            if(data["code"] === 400){
                $("#F_errorTip .tipWord").html(data["message"]) ;
                bounce_Fixed.show($("#F_errorTip")) ;
            }else if(data["code"] === 200){
                bounce_Fixed.cancel();
                getTransInfo();
            }else{
                $("#F_errorTip .tipWord").html("错误的返回值！") ;
                bounce_Fixed.show($("#F_errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

// creator: 张旭博，2018-05-14 16:19:16，统计商品的类别数和件数
function figureGoodInfo(selector) {
    var goodIdArr = [],
        countAll = 0,
        pack_amount = 0;

    //遍历选择器下的所有商品id
    selector.find(".tblList tbody tr").each(function () {
        var innerSn = $(this).find("td").eq(2).text();
        goodIdArr.push(innerSn);
        pack_amount += Number($(this).find(".goodNum_stock").text());
    });

    //id排序
    goodIdArr.sort();

    //统计类别数
    for (var i = 0; i < goodIdArr.length;) {
        var count = 0;
        for (var j = i; j < goodIdArr.length; j++) {
            if (goodIdArr[i] === goodIdArr[j]) {
                count++;
            }
        }
        countAll++;
        i+=count;
    }
    var alertHtml = '本次计划出库共 <span class="ty-color-blue"> '+countAll+' </span> 种商品，共<span class="ty-color-blue"> '+parseInt(pack_amount)+' </span>件';
    selector.find(".countAll").alert('success',alertHtml);
}

/* creator：张旭博，2017-11-27 16:55:35，转换审批数字为字符串 */
function chargeApproveState(state) {
    if(state === "1"){
        return '同意出库'
    }else if(state === "2"){
        return '暂缓出库'
    }
}
// 为空赋值
function chargeNull(str){
    if(str === null  || str === undefined){
        return "--"
    }else{
        return str ;
    }
}

function chargeNumb(str) {
    if(str === null  || str === undefined || Number(str) < 0){
        return Number(0)
    }else{
        return Number(str) ;
    }
}

function formatJson(data) {
    for(var key in data){
        if(data[key] === null){
            data[key] = ""
        }
    }
    return data;
}

function deleteGood(selector) {
   selector.parent().parent().remove();
}


// 时间控件初始化
laydate.render({elem: '#deliveryDate'});
laydate.render({elem: '#arriveDate'});
laydate.render({elem: '#change_deliveryDate'});
laydate.render({elem: '#change_arriveDate'});