$(function(){
    getUnsignedList(1,20);
    // 两种状态切换（待签收、已签收）
    $(".ty-secondTab li").on("click",function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        //获取点击按钮的下标
        var index = $(this).index();
        if(index === 0){
            getUnsignedList(1,20);
        }else{
            getSignedList(1,20);
        }
        //展示对应的内容
        $(".tblContainer").eq(index).show().siblings(".tblContainer").hide();
    });

    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
})



/* creator：张旭博，2017-11-08 08:58:03，物流管理-》发货管理-》待签收-》待签收列表 */
function getUnsignedList(currPage,pageSize) {
    $.ajax({
        url:"../inOutStock/toBeSignedList.do" ,
        data:{
            "currPage":currPage,
            "pageSize":pageSize
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            setPage( $("#ye_signature") , curr ,  totalPage , "unSignature");

            var unsignList  = data["list"];
            var unsignListStr = '';
            for(var i=0;i<unsignList.length;i++){
                unsignListStr +=    '<tr id="'+unsignList[i].id+'">' +
                                        '<td>' + formatTime(unsignList[i].delivery_date,false) + '</td>'+     //出库日期
                                        '<td>' + unsignList[i].customer_name + '</td>'+     //客户名称
                                        '<td>' + handleNull(unsignList[i].customer_code) + '</td>'+     //客户代号
                                        '<td>' + handleNull(unsignList[i].sn) + '</td>'+     //订单号
                                        '<td>' + unsignList[i].gsum + '</td>'+     //商品类别总数
                                        '<td>' + unsignList[i].pack + '</td>'+     //货物总件数
                                        '<td>' + formatTime(unsignList[i].arrive_date,false) + '</td>'+     //计划到达日期
                                        '<td>' + formatTime(unsignList[i].apply_date,true) + '</td>'+     //申请提交时间
                                        '<td>' + formatTime(unsignList[i].approve_time,true) + '</td>'+     //仓库审批时间
                                        '<td>' + formatTime(unsignList[i].review_time,true) + '</td>'+     //复核时间
                                        '<td>' + unsignList[i].applicant_name + '</td>'+     //提交者
                                        '<td>' +
                                            '<span class="ty-color-blue" onclick="signInputBtn($(this))">签收录入</span>'+
                                            '<span class="ty-color-blue" onclick="transInfoBtn($(this))">发运信息</span>'+
                                            '<span class="ty-color-blue" onclick="logisticOrderBtn($(this))">出库单</span>'+
                                        '</td>'+     //操作
                                    '</tr>'
            }
            $("#unsigned tbody").html(unsignListStr);
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-29 16:12:17，签收录入-按钮 */
function signInputBtn(selector) {
    $("#signRecord .agreenTip").data("checked", "0");
    $("#signRecord .agreenTip i").attr("class", "fa fa-circle-o");
    bounce.show($("#signRecord"));
    var outid = selector.parent().parent().attr("id");
    getOutStorageDetail(outid)
}

/* creator：张旭博，2017-11-29 16:32:02，物流管理-》发货管理-》待签收-》签收录入 (查看，与签收录入共用) */
function getOutStorageDetail(outid) {
    $.ajax({
        url:"../inOutStock/signForEntry.do" ,
        data:{
            "outid":outid
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var base = data["data"];
            var deliveryItemData = base.list;
            $("#signRecordBase .customerName").html(base.customer_name);
            $("#signRecordBase .customerCode").html(base.customer_code);
            $("#signRecordBase .address").html(base.address);
            $("#signRecordBase .contact").html(base.consignee );
            $("#signRecordBase .mobile").html(base.telephone);
            $("#signRecordBase .sn").html(base.sn);
            $("#signRecordBase .create_date").html(formatTime( base.order_c , true ));
            $("#signRecordBase .update_date").html(formatTime( base.update_date , true ));
            $("#signRecordBase .deliveryDate").html(formatTime( base.delivery_date , false ));
            $("#signRecordBase .carrier").html(base.carrier);
            $("#signRecordBase .arriveDate").html(formatTime( base.arrive_date , false ));
            $("#signRecordBase .deliveryWay").html(base.delivery_way);
            $("#signRecordBase").attr("oid",base.outid);

            var deliveryStr = '';
            var changeState = 0;
            for(var i=0;i<deliveryItemData.length;i++){
                if(deliveryItemData[i].sign_amount !== null){
                    changeState ++;
                }
                deliveryStr +=  '<tr id="'+deliveryItemData[i].id+'" pack="'+deliveryItemData[i].pack+'">'+
                    '<td>' + deliveryItemData[i].outer_sn + '</td>' +                           //商品代号
                    '<td>' + deliveryItemData[i].outer_name + '</td>' +                         //商品名称
                    '<td>' + formatTime( deliveryItemData[i].delivery_date , false ) + '</td>' +//要求到货日期
                    '<td class="goodNum_stock">' + deliveryItemData[i].pack + '</td>' +                           //货物件数
                    '<td>' + formatTime( deliveryItemData[i].approve_time,true) + '</td>' +    //仓库审批时间
                    '<td>' + formatTime( deliveryItemData[i].review_time,true) + '</td>' +    //复核时间
                    '<td>' + deliveryItemData[i].unit + '</td>' +                               //单位
                    '<td>' + deliveryItemData[i].out_plan + '</td>' +                           //出库数量
                    //'<td>已修改</td>' +    //复核时间
                    //'<td class="change_amount">' + deliveryItemData[i].sign_amount+ '</td>' +    //修改数量
                    '<td class="change_memo" style="display: none">' + deliveryItemData[i].sign_record+ '</td>' +    //修改概况
                    '<td>' + (deliveryItemData[i].sign_amount || deliveryItemData[i].out_plan)+
                    //'<span class="ty-color-blue" onclick="signDetailCheckBtn($(this))">查看</span>'+
                    '<span class="ty-color-blue gapLt" onclick="signDetailChangeBtn($(this))">修改</span>'+
                    '</td>'+
                    '</tr>';
            }
            if(changeState > 0){
                $("#signSureBaseBtn").attr("data-state",2)
            }else{
               $("#signSureBaseBtn").attr("data-state",1)
            }
            $("#signRecord .tblList tbody").html(deliveryStr);

            //统计商品种类和件数
            figureGoodInfo($("#signRecord"));
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;

}

/* creator：张旭博，2017-11-29 16:56:31，货运信息-按钮 */
function transInfoBtn(selector) {
    bounce.show($("#logisticInfo"));
    var outId = selector.parent().parent().attr("id");
    $("#logisticInfo .transInfo").attr("id",outId);
    getTransInfo();
}

/* creator：张旭博，2017-11-29 16:56:17，获取发运信息 */
function getTransInfo() {
    var outId   = $("#logisticInfo .transInfo").attr("id");
    $.ajax({
        url:"../skl/getShipmentState.do" ,
        data:{"outId":outId},
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            if(data[0].code && data[0].code === 400){
                $("#logisticInfo .transDetail").hide();
                $("#logisticInfo .transInfo").show();
            }else {
                $("#logisticInfo .transDetail").show();
                $("#logisticInfo .transInfo").hide();
                var transDetailStr = '';
                for (var i = 0; i < data.length; i++) {
                    var deliveryWay = data[i].deliveryWay;
                    var state = data[i].updateDate;
                    var dateStr = '';
                    if(state === ""){
                        dateStr = '录入日期';
                    }else{
                        dateStr = '修改日期';
                    }
                    switch (deliveryWay) {
                        case "A":
                            transDetailStr += '<tr>' +
                                '    <td>发运方式：</td>' +
                                '    <td>快递</td>' +
                                '    <td>快递单号：</td>' +
                                '    <td>' + data[i].sn + '</td>' +
                                '    <td>快递公司：</td>' +
                                '    <td>' + data[i].company + '</td>' +
                                '    <td>交寄日期：</td>' +
                                '    <td>' + data[i].deliveryTime + '</td>' +
                                '    <td>'+dateStr+'</td>' +
                                '    <td>' + data[i].createDate + '</td>' +
                                '</tr>';
                            break;
                        case "B":
                            transDetailStr += '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="B">随身携带</td>' +
                                '   <td>携带者：</td>' +
                                '   <td>' + data[i].operator + '</td>' +
                                '   <td>联系方式：</td>' +
                                '   <td>' + data[i].telephone + '</td>' +
                                '   <td>备注：</td>' +
                                '   <td>' + data[i].memo + '</td>' +
                                '   <td>'+dateStr+'</td>' +
                                '   <td>' + data[i].createDate + '</td>' +
                                '</tr>';
                            break;
                        case "C":
                            transDetailStr += '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="C">货运</td>' +
                                '   <td>联系人：</td>' +
                                '   <td>' + data[i].operator + '</td>' +
                                '   <td>联系电话：</td>' +
                                '   <td>' + data[i].telephone + '</td>' +
                                '   <td>牌照号码：</td>' +
                                '   <td>' + data[i].licenseTag + '</td>' +
                                '</tr>' +
                                '<tr>' +
                                '   <td></td>' +
                                '   <td></td>' +
                                '   <td>乘运公司：</td>' +
                                '   <td>' + data[i].company + '</td>' +
                                '   <td>货运单号：</td>' +
                                '   <td>' + data[i].sn + '</td>' +
                                '</tr>'+
                                '<tr>' +
                                '   <td></td>' +
                                '   <td></td>' +
                                '   <td>备注：</td>' +
                                '   <td colspan="5">'+data[i].memo +'</td>' +
                                '   <td>'+dateStr+'</td>' +
                                '   <td>' + data[i].createDate + '</td>' +
                                '</tr>';
                            break;
                    }
                }
                $("#logisticInfo .transDetail tbody").html(transDetailStr);
                $("#logisticInfo .transDetail").show();
                $("#logisticInfo .chooseWay").hide();
                $("#logisticInfo .transInfo").hide();
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}


/* creator：张旭博，2017-11-30 08:30:58，物流管理-》发货管理-》已签收-》已签收列表-》签收记录-》修改确认操作 */
function signChange() {
    // Long num ;签收数量
    // Integer id //商品的id
    // Integer outid //出库单id
    // String message  //签收内容
    var outid = $("#signRecordBase").attr("oid");
    var num = $("#signDetailChange .actualDeliveryNum").val();
    var message = $("#signDetailChange .memo").val();
    var id = $("#signDetailChange .bonceCon").attr("id");

    $.ajax({
        url:"../inOutStock/numberOfChange.do" ,
        data:{
            "outid":outid,
            "id":id,
            "num":num,
            "message":message
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            bounce_Fixed.cancel();
            var status = data["status"];
            if(status === 1){
                layer.msg("修改成功！");
                getOutStorageDetail(outid)
            }else if(status === 0){
                $("#errorTip .tipWord").html("修改失败！") ;
                bounce.show($("#errorTip")) ;
            }else{
                $("#errorTip .tipWord").html("错误的返回值!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-30 08:30:58，物流管理-》发货管理-》已签收-》已签收列表-》签收记录-》确认无误按钮 */
function signSureBtn() {
    let len = $("#signRecord .agreenTip").find(".fa-dot-circle-o").length;
    if (len > 0) {
        $("#signSure").find("input,textarea").val("");
        $("#signSure").find("#signRecordUplaod").html("");
        $("#signSure").find(".signRecordImg").html("");
        if ($("#signRecordUplaod #file_upload_1-button").length <= 0) {
            initSignUpload($("#signRecordUplaod"));
        }
        bounce_Fixed.show($("#signSure"));
        $('body').everyTime('0.5s','signSure',function(){
            var arriveTimeFact = $("#signSure .arriveTimeFact").val();
            var signerName = $("#signSure .signerName").val();
            var signTime = $("#signSure .signTime").val();
            var signRecordInfo = $("#signSure .signRecordInfo").val();
            if($.trim(arriveTimeFact) === "" && $.trim(signerName) === "" && $.trim(signTime) === ""&& $.trim(signRecordInfo) === ""){
                $("#signSureBtn").prop("disabled",true);
            }else{
                $("#signSureBtn").prop("disabled",false);
            }
        })
    }
}

/* creator：张旭博，2017-11-30 08:30:58，物流管理-》发货管理-》已签收-》已签收列表-》签收记录-》确认无误操作 */
function signSure() {
    // Integer outid 出库单id 必
    // String time //日期  必
    // String sginer //签收人 必
    // String message  //签收内容
    var outId = $("#signRecordBase").attr("oid");
    var time = $("#signSure .signTime").val();
    var arriveTime = $("#signSure .arriveTimeFact").val();
    var signer = $("#signSure .signerName").val();
    var message = $("#signSure .signRecordInfo").val();
    var state = $("#signSureBaseBtn").attr("data-state");
    var picArr = '';
    if ($("#signSure .imgsthumb .filePic").length > 0) {
        $("#signSure .imgsthumb .filePic").each(function () {
            picArr += $(this).data("path") +',';
        })
        picArr = picArr.substr(0,picArr.length-1);
    }
    $.ajax({
        url:"../inOutStock/signFor.do" ,
        data:{
            "outid":outId,
            "time":time,
            "arriveTimeFact":arriveTime,
            "sginer":signer,
            "message":message,
            "state":state,
            "urls":picArr
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var status = data["status"];
            bounce_Fixed.cancel();
            bounce.cancel();
            if(status === 1){
                layer.msg("签收成功！");
                $(".ty-secondTab li").eq(0).click();
            }else if(status === 0){
                $("#errorTip .tipWord").html("签收失败！") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-29 16:13:51，物流管理-》发货管理-》出库单-按钮 */
function logisticOrderBtn(selector) {
    bounce.show($("#outStorageOrder"));
    var outid   = selector.parent().parent().attr("id");
    var data    = {"outid":outid,};
    var index   = $(".ty-secondTab li.ty-active").html();
    if(index === "待签收"){
        data["state"] = 8
    }else{
        data["state"] = 9
    }

    $.ajax({
        url:"../inOutStock/aTaoLook.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var base = data["data"];
            $("#outStorageOrder .applyAll").html(
                ' &nbsp;申请人： <span class="ty-color-blue">'+base.create_name+'</span> '+formatTime(base.apply_date,true) +
                ' &nbsp;仓库： <span class="ty-color-blue">'+base.approver_name+'</span> '+formatTime(base.approve_time , true ) +
                ' &nbsp;复核人： <span class="ty-color-blue">'+base.reviewer_name+'</span> '+formatTime(base.review_time,true)

            );
            $("#outStorageOrderBase .customerName").html(base.customer_name);
            $("#outStorageOrderBase .customerCode").html(base.customer_code);
            $("#outStorageOrderBase .sn").html(base.sn);
            $("#outStorageOrderBase .deliveryDate").html(formatTime( base.delivery_date,false));

            var deliveryItemData = base.list;
            var deliveryStr = '';
            for(var i=0;i<deliveryItemData.length;i++){
                deliveryStr +=  '<tr id="'+deliveryItemData[i].itemId+'" oid="'+deliveryItemData[i].orderId+'" pack="'+deliveryItemData[i].pack+'">'+
                    '<td>' + deliveryItemData[i].outer_sn + '</td>' +        //商品代号
                    '<td>' + deliveryItemData[i].outer_name + '</td>' +      //商品名称
                    '<td>' + deliveryItemData[i].inner_sn + '</td>' +        //产品图号
                    '<td>' + deliveryItemData[i].name + '</td>' +            //产品名称
                    '<td>' + deliveryItemData[i].unit + '</td>' +            //单位
                    '<td>' + deliveryItemData[i].out_plan + '</td>' +        //出库数量
                    '<td  class="goodNum_stock">' + deliveryItemData[i].pack + '</td>' +        //货物件数
                    '<td>' + formatTime( deliveryItemData[i].approve_time,true) + '</td>' +    //仓库审批时间
                    '<td>' + formatTime( deliveryItemData[i].review_time,true) + '</td>' +    //复核时间
                    '</tr>'
            }
            $("#outStorageOrder .tblList tbody").html(deliveryStr);

            //统计商品种类和件数
            figureGoodInfo($("#outStorageOrder"))
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
    // var base = JSON.parse(selector.parent().siblings(".base").html());
    // $("#outStorageOrder .countAll").html('本次计划出库共 <span class="ty-color-blue">'+base.item_account+'</span> 种商品，共 <span class="ty-color-blue">'+base.pack_amount+'</span> 件');
    // $("#outStorageOrder .applyAll").html(
    //     ' &nbsp;申请人： <span class="ty-color-blue">'+base.tpa_create_name+'</span> '+formatTime(base.tpa_create_date,false) +
    //     ' &nbsp;仓库： <span class="ty-color-blue">'+base.tpa_approve_name+'</span> '+formatTime( base.last_review_time , false ) +
    //     ' &nbsp;复核人： <span class="ty-color-blue">'+base.tpa_review_name+'</span> '+formatTime(base.last_approve_time,false)
    //
    // );
    // $("#outStorageOrderBase .customerName").html(base.customer_name);
    // $("#outStorageOrderBase .customerCode").html(base.customer_code);
    // $("#outStorageOrderBase .sn").html(base.sn);
    // $("#outStorageOrderBase .deliveryDate").html(formatTime( base.tpa_delivery_date,false));
    // var deliveryItemData = base.dataList;
    // var deliveryStr = '';
    // for(var i=0;i<deliveryItemData.length;i++){
    //     deliveryStr +=  '<tr id="'+deliveryItemData[i].itemId+'" oid="'+deliveryItemData[i].orderId+'" pack="'+deliveryItemData[i].pack+'">'+
    //         '<td>' + deliveryItemData[i].outer_sn + '</td>' +        //商品代号
    //         '<td>' + deliveryItemData[i].outer_name + '</td>' +      //商品名称
    //         '<td>' + deliveryItemData[i].inner_sn + '</td>' +        //产品图号
    //         '<td>' + deliveryItemData[i].NAME + '</td>' +            //产品名称
    //         '<td>' + deliveryItemData[i].unit + '</td>' +            //单位
    //         '<td>' + deliveryItemData[i].current_stock + '</td>' +   //当前库存
    //         '<td>' + deliveryItemData[i].out_plan + '</td>' +        //计划出库数量
    //         '<td class="goodNum_stock">'+deliveryItemData[i].pack_num+'</td>' +        //货物件数
    //         '</tr>'
    // }
    // $("#outStorageOrder .tblList tbody").html(deliveryStr);
}

/* creator：张旭博，2017-12-04 09:40:03，物流管理-》发货管理-》已签收-》签收查看-按钮  */
function signCheckBtn(selector) {
    bounce.show($("#signCheck"))
    var outid = selector.parent().parent().attr("id");
    $.ajax({
        url:"../inOutStock/aTaoLook.do" ,
        data:{
            "outid":outid,
            "state":9
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var base = data["data"];
            var deliveryItemData = base.list;
            var imghtml = '';
            $("#signCheckBase .customerName").html(base.customer_name);
            $("#signCheckBase .customerCode").html(base.customer_code);
            $("#signCheckBase .address").html(base.address);
            $("#signCheckBase .contact").html(base.telephone);
            $("#signCheckBase .mobile").html(base.consignee);
            $("#signCheckBase .sn").html(base.sn);
            $("#signCheckBase .create_date").html(formatTime( base.order_c , false ));
            $("#signCheckBase .update_date").html(formatTime( base.order_u , false ));
            $("#signCheckBase .deliveryDate").html(formatTime( base.delivery_date , false ));
            $("#signCheckBase .carrier").html(base.carrier);
            $("#signCheckBase .arriveDate").html(formatTime( base.arrive_date , false ));
            $("#signCheckBase .deliveryWay").html(base.delivery_way);

            $("#signInfoRecord .signDetail").html(base.sign_record);
            $("#signInfoRecord .signer").html(base.sginer);
            $("#signInfoRecord .signerFact").html(formatTime(base.arrive_time_fact , false ));
            $("#signInfoRecord .signTime").html(formatTime(base.sign_time,false));
            $("#signInfoRecord .recorder").html(base.sign_recorder_name);
            $("#signInfoRecord .recordTime").html(formatTime(base.sign_recorder_time,false));
            $("#signCheckBase").attr("oid",base.outid);
            $("#signCheck .applyAll").html(
                ' &nbsp;申请人： <span class="ty-color-blue">'+base.applicant_name+'</span> '+formatTime(base.apply_date,true) +
                ' &nbsp;仓库： <span class="ty-color-blue">'+base.approver_name+'</span> '+formatTime( base.approve_time , true ) +
                ' &nbsp;复核人： <span class="ty-color-blue">'+base.reviewer_name+'</span> '+formatTime(base.review_time,true)

            );
            if (base.imageUrls.length > 0){
                for(var t=0;t<base.imageUrls.length;t++){
                    imghtml += "<span><a path=\""+ base.imageUrls[t] +"\" onclick=\"fileSeeOnline($(this))\">照片"+ (t+1) + "</a></span>";
                }
            }
            $(".imgScanWall").html(imghtml);
            var deliveryStr = '';
            for(var i=0;i<deliveryItemData.length;i++){
                deliveryStr +=  '<tr id="'+deliveryItemData[i].id+'" pack="'+deliveryItemData[i].pack+'">'+
                    '<td>' + deliveryItemData[i].outer_sn + '</td>' +                           //商品代号
                    '<td>' + deliveryItemData[i].outer_name + '</td>' +                         //商品名称
                    '<td>' + formatTime( deliveryItemData[i].delivery_date , false ) + '</td>' +//要求到货日期
                    '<td class="goodNum_stock">' + deliveryItemData[i].pack + '</td>' +                           //货物件数
                    '<td>' + formatTime( deliveryItemData[i].approve_time,false) + '</td>' +    //仓库审批时间
                    '<td>' + formatTime( deliveryItemData[i].review_time,false) + '</td>' +    //复核时间
                    '<td>' + deliveryItemData[i].unit + '</td>' +                               //单位
                    '<td>' + deliveryItemData[i].out_plan + '</td>' +                               //应签收数量
                    '<td><span class="change_amount">' + deliveryItemData[i].sign_amount +'</span>'+
                    '<span class="ty-color-blue" onclick="signDetailCheckBtn($(this))">情况查看</span>'+
                    '</td>' +    //实际签收数量
                    '<td class="change_memo" style="display:none;">' + deliveryItemData[i].sign_record + '</td>' ;    //实际签收数量
                /*var sign_time = deliveryItemData[i].review_time;
                if(sign_time === null || sign_time === undefined || sign_time === ''){
                    deliveryStr +=  '<td></td><td></td></tr>'
                }else{
                    deliveryStr +=  '<td>' + formatTime( deliveryItemData[i].sgin_time,false) + '</td>' +    //录入时间
                                    '<td>' +
                                    '<span class="ty-color-blue" onclick="signDetailCheckBtn($(this))">查看</span>'+
                                    '</td>'+
                                    '</tr>';
                }*/
            }
            $("#signCheck .tblList tbody").html(deliveryStr);

            //统计商品种类和件数
            figureGoodInfo($("#signCheck"))
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-08 08:58:03，物流管理-》发货管理-》已签收-》已签收列表 */
function getSignedList(currPage,pageSize) {
    $.ajax({
        url:"../inOutStock/aTaoList.do" ,
        data:{
            "currPage":currPage,
            "pageSize":pageSize
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            setPage( $("#ye_signature") , curr ,  totalPage , "signature");

            var signedList  = data["list"];
            var signedListStr = '';
            for(var i=0;i<signedList.length;i++){
                signedListStr +=    '<tr  id="'+signedList[i].id+'">' +
                    '<td>' + formatTime(signedList[i].delivery_date,false) + '</td>'+     //出库日期
                    '<td>' + signedList[i].customer_name + '</td>'+     //客户名称
                    '<td>' + handleNull(signedList[i].customer_code) + '</td>'+     //客户代号
                    '<td>' + handleNull(signedList[i].sn) + '</td>'+     //订单号
                    '<td>' + signedList[i].gsum + '</td>'+     //商品类别总数
                    '<td>' + signedList[i].pack + '</td>'+     //货物总件数
                    '<td>' + formatTime(signedList[i].arrive_time_fact,false) + '</td>'+     //实际到达日期
                    '<td>' + signedList[i].sginer + '</td>'+     //签收人
                    '<td>' + formatTime(signedList[i].sign_time,false) + '</td>'+     //签收时间
                    '<td>' + signedList[i].reviewer_name + '&nbsp;&nbsp;' + formatTime(signedList[i].review_time,true) + '</td>'+     //复核时间
                    '<td>' +
                        '<span class="ty-color-blue" onclick="signCheckBtn($(this))">签收查看</span>'+
                        '<span class="ty-color-blue" onclick="transInfoBtn($(this))">发运信息</span>'+
                        '<span class="ty-color-blue" onclick="logisticOrderBtn($(this))">出库单</span>'+
                    '</td>'+     //操作
                    '</tr>'
            }
            $("#signed tbody").html(signedListStr);
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-30 08:30:58，物流管理-》发货管理-》已签收-》已签收列表-》签收记录-》修改按钮 */
function signDetailChangeBtn(selector) {
    bounce_Fixed.show($("#signDetailChange"));
    var id= selector.parent().parent().attr("id")
    $("#signDetailChange .bonceCon").attr("id",id);
    var sign_record = selector.parent().siblings(".change_memo").text();
    var sign_amount = selector.siblings(".change_amount").text();
    if(sign_record === undefined || sign_record === ""||sign_record === null||sign_record === "null"){
        $("#signDetailChange .actualDeliveryNum").val("");
        $("#signDetailChange .memo").val("");
    }else{
        $("#signDetailChange .actualDeliveryNum").val(sign_amount);
        $("#signDetailChange .memo").val(sign_record);
    }
    $('body').everyTime('0.5s','signDetailChange',function(){
        if($("#signDetailChange .actualDeliveryNum").val() === ""){
            $("#signDetailChangeBtn").prop("disabled",true);
        }else{
            $("#signDetailChangeBtn").prop("disabled",false);
        }
    })
}

/* creator：张旭博，2017-11-30 08:30:58，物流管理-》发货管理-》已签收-》已签收列表-》签收记录-》查看按钮 */
function signDetailCheckBtn(selector) {
    bounce_Fixed.show($("#signDetailCheck"));
    var sign_record = selector.parent().siblings(".change_memo").text();
    var sign_amount = selector.siblings(".change_amount").text();
    $("#signDetailCheck .actualDeliveryNum").val(sign_amount);
    $("#signDetailCheck .memo").val(sign_record);
}

// creator: 张旭博，2018-05-14 16:19:16，统计商品的类别数和件数
function figureGoodInfo(selector) {
    var goodIdArr = [],
        countAll = 0,
        pack_amount = 0;

    //遍历选择器下的所有商品id
    selector.find(".tblList tbody tr").each(function () {
        var innerSn = $(this).find("td").eq(2).text();
        goodIdArr.push(innerSn);
        console.log(innerSn)
        pack_amount += Number($(this).find(".goodNum_stock").text());
    });

    //id排序
    goodIdArr.sort();

    //统计类别数
    for (var i = 0; i < goodIdArr.length;) {
        var count = 0;
        for (var j = i; j < goodIdArr.length; j++) {
            if (goodIdArr[i] === goodIdArr[j]) {
                count++;
            }
        }
        countAll++;
        i+=count;
    }
    var alertHtml = '本次计划出库共 <span class="ty-color-blue"> '+countAll+' </span> 种商品，共<span class="ty-color-blue"> '+pack_amount+' </span>件';
    selector.find(".countAll").alert('success',alertHtml);
}
// creator: 李玉婷，2021-08-6 15:40:10，上传
function initSignUpload(obj){
    let multi = true
    let groupUuid = sphdSocket.uuid();
    let itemTemplate = ``
    obj.html("")
    obj.Huploadify({
        auto:true,
        fileTypeExts: '*.gif,*.png;*.jpg;*.jpeg;',
        itemTemplate: itemTemplate,
        multi: multi,
        buttonText:"上传",
        formData:{
            module: '物流管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data)
            let len =  obj.parent().siblings("div").find(".imgsthumb").length;
            if(len < 3){
                var path = data.filename //路径（包含文件类型）
                obj.parent().parent().attr("groupUuid", data.groupUuid )
                var imgStr =
                    '<div class="imgsthumb">' +
                    '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                    '    <span class="canclePic fa fa-close" fileUid="' + data.fileUid + '" onclick="cancleThis($(this))"></span> ' +
                    '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                    '</div>';
                obj.parent().siblings("div.signRecordImg").append(imgStr);
            }else{
                layer.msg('最多只能上传3个文件')
                let fileUid = data.fileUid
                cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
            }
        }
    })
}
// creator: 李玉婷，2021-10-26 09:39:47，勾选
function checkSignFinish (obj) {
    if(obj.data('checked') == '0'){
        obj.data('checked','1');
        obj.find('i').removeClass('fa-circle-o').addClass('fa-dot-circle-o');
    }else {
        obj.data('checked','0');
        obj.find('i').removeClass('fa-dot-circle-o').addClass('fa-circle-o');
    }
}
// creator: 李玉婷，2019-08-21 14:46:56，图片删除
function cancleThis(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent(".imgsthumb").remove();
}
// creator: 李玉婷，2021-10-27 13:51:10，图片预览
function fileSeeOnline(selector) {
    seeOnline(selector)
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.attr('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");
}
//处理null
function handleNull(str) {
    var result = str == null || str == undefined || str == 'null' ? '':str;
    return result;
}


// 时间控件初始化
laydate.render({elem: '#arriveTime'});
laydate.render({elem: '#signTime'});