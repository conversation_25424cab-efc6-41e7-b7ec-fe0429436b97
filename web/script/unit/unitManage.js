
/*
* 计量单位管理页面
* */
var listType = 1; // 查询的计量单位的状态 0-停用的，1-正在使用的
var editObj = null ;
$(function() {
    getList(1,20);

    $(".linkBtn").click(function() {
        var type = $(this).data("type");
        switch (type){
            case "addNew": // 新增计量单位
                bounce.show($("#addUnit"));
                $("#addUnit input").val("");
                break;
            case "seeStopList": // 查看已停用的计量单位
                $(".mainCon2").show().siblings().hide();
                listType = 0;
                getList(1,20);
                break;
            default:
        }
    });
    $(".ty-btn").click(function() {
        var type = $(this).data("type");
        switch (type){
            case "goPre": // 返回首页
                $(".mainCon1").show().siblings().hide();
                var cur = $("#ye .yecur").html();
                listType = 1;
                getList(cur,20);
                break;
            default:
        }
    })
    $("table").on("click", ".tyBtn", function(){
        var type = $(this).data("type");
        editObj = $(this)
        switch (type){
            case "stopBtn": // 停用
                var info = JSON.parse($(this).siblings(".hd").html());
                $.ajax({
                    "url":"../unit/stopUnitEnter.do",
                    "data":{ 'id': info.id },
                    success:function(res) {
                        var status = res['status'];
                        if(status == 1){ // 可以停
                            bounce.show($("#tip2"));
                            $("#tip2 ").data("type","0");
                            $("#tip2 .bonceCon").html("<p>确定后，在系统中将无法再选择该计量单位！</p><p>确定停用该计量单位吗？</p>");
                        }else { // 不能停用
                            var tipStr = '<p>操作失败！</p><p>因为该计量单位在系统中正在被使用！</p>' ;
                            $("#tip1 .bonceCon").html(tipStr);
                            bounce.show($("#tip1"));
                        }
                    }
                })
                break;
            case "startBtn": // 启用
                var info = JSON.parse($(this).siblings(".hd").html());
                bounce.show($("#tip2"));
                $("#tip2 ").data("type","1");
                $("#tip2 .bonceCon").html("<p>确定后，该计量单位在系统中又可重新被选择。</p><p>确定恢复使用该计量单位？</p>");
                break;
            default:
        }
    });
})
// creator: hxz，2020-08-27 14:51:41，确定停用
function editUnitOk() {
    var type = $("#tip2 ").data("type");
    var info = JSON.parse(editObj.siblings(".hd").html());
    var id = info.id
    $.ajax({
        "url":"../unit/updateUnit.do",
        "data":{ 'type': type, "id": id  },
        success:function(res) {
            bounce.cancel();
            var status = res['status'] ;
            if(status == 1){
                if(type == '1'){
                    layer.msg("启用成功！");
                    var cur = $("#ye2 .yecur").html();
                    getList(cur,20);
                }else{
                    layer.msg("停用成功！");
                    var cur = $("#ye1 .yecur").html();
                    getList(cur,20);
                }

            }else {
                layer.msg("停用失败，正在使用，无法停用！");
            }
        }
    })
}

// creator: hxz，2020-08-27 14:51:41，确定新增计量单位
function addUnitOk() {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": 0 },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                var cur = $("#ye"+listType).find(".yecur").html();
                getList(cur , 20);
                bounce.cancel();
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>您可将其恢复使用！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce.show($("#tip1"));
            }
        }
    })
}
// creator: hxz，2020-08-27 14:51:41，获取列表
function getList(cur, per) {
    $.ajax({
        "url":"../unit/getUnitList.do",
        "data":{ 'status': listType, "currentPageNo":cur , "pageSize":per },
        success:function(res) {
            var list = res['list'],  pageInfo = res['pageInfo'];
            var str = '', ctrlStr='<span class=\'tyBtn ty-color-red\' data-type=\'stopBtn\'>停用</span>',con = "1" ;
            if(listType == 0){
                ctrlStr='<span class=\'tyBtn ty-color-blue\' data-type=\'startBtn\'>恢复使用</span>';
                con = "2" ;
            }
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['name']+"</td>" +
                        "    <td>"+ item['createName']+ " " +(new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +  "</td>" +
                        "    <td>"+ (item['updateName'] || "- -") + (item['updateName']?(new Date(item['updateDate']).format("yyyy-MM-dd hh:mm:ss")):"")  +  "</td>" +
                        "    <td>" + ctrlStr +
                        "    <span class='hd'>"+ JSON.stringify(item) +"</span>  "+
                        "    </td>" +
                        "</tr>";
                }
            }

            $(".mainCon"+ con +" tbody").html(str);
            $(".sumHistory").html(res['sumHistory']);
            $(".totalNum"+ con ).html(pageInfo['totalResult']);
            setPage($("#ye" + con), pageInfo['currentPageNo'], pageInfo['totalPage'],'unitManage','{}');
        }
    })
}



