var user = null;
var contractDel = null;
var contractInfo = null;
var editContractObj = null;
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
//下面一行看到时候的情况进行修改
bounce_Fixed2.show($("#newMailInfo"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
//下面一行看到时候的情况进行修改
//bounce_Fixed3.show($("#lookpoot"));
bounce_Fixed3.cancel();

$(function(){
    //下面是用的范例
    getLogList(1,20,0);
    $("#checkInOutInfo .ty-secondTab li").attr("class","")
    $(".ty-secondTab").children().first().attr("class","ty-active");
    // 出入库统计
    $("#checkInOutInfo .ty-secondTab li").click(function(){
        let _index = $(this).index();
        getLogList(1,20,_index)
        $("#checkInOutInfo .ty-secondTab li").attr("class","")
        $(this).attr("class","ty-active")
    })
    //上面是范例区
})

// 下面是用的范例
// creator: hxz，2020-02-03 17:00:06，去处理
function stockJump(item) {
    $(".container_item").children("div").hide();
    $(".container_item").children("div").eq(item).show();
    if (item == '0' || item == 0) {
        $(".stockJump").hide();
    } else{
        $(".stockJump").show();
    }
    jumpTo(item, 1, 20);
}
function jumpTo(num, cur, pageTotal) {
    switch (num) {
        case 0:
            getIndexData();
            break;
        case 1:
            getWaitingForLocationList(cur, pageTotal);
            break;
        case 2:
            resetLocationList(cur, pageTotal);
            break;
        case 3:
            $(".curCat").html("");
            getPdByCategory('', 4, '', 1 , 20);
            break;
        case 4:
            getStorageAcceptList(cur, pageTotal,0);
            break;
        case 5:
            getOutStorageList(cur, pageTotal);
            break;
        case 6:
            $("#checkInOutInfo .ty-secondTab li").eq(0).click();
            break;
        case 7:
            var searchKey = $.trim($("#searchKey").val());
            keywordSearch(searchKey, cur, pageTotal);
            break;
    }
}
function getIndexData() {
    $(".dataFilter").hide()
    $("#singleHouse tbody").html('');
    $.ajax({
        url:"../mt/index" ,
        data:{ 'org': sphdSocket.org.id },
        success:function(data){
            var getData = data;
            getData['location_count_empty'] = Number(getData['location_count']) - Number(getData['location_count_null']);
            $("#finishedWarehouse [houseReq]").each(function(){
                var name = $(this).data('name')
                $(this).html((getData[name]));
            })
            $("#waitingFor").html(getData['waitingFor'])
            if (getData.warehouse && getData.warehouse.length > 0) {
                var ckList = getData.warehouse;
                var html = '';
                if(ckList && ckList.length >0){
                    for(var i=0;i<ckList.length;i++){
                        var cha = Number(ckList[i].location_num) - Number(ckList[i].location_count_null)
                        html +=
                            '<tr data-id="'+ ckList[i].id +'">' +
                            '    <td><div class="td-lightOrange">' +
                            '        <h5>'+ ckList[i].warehouse_name +'</h5>' +
                            '        <span class="jumpWarehouse">进入该仓库</span>' +
                            '    </div></td>' +
                            '    <td>' +
                            '        <p>区域数量</p>' +
                            '        <p>货架数量</p>' +
                            '        <p>库位总数/空库位数</p>' +
                            '    </td>' +
                            '    <td>' +
                            '        <p>'+ setZero(ckList[i].region_num) +'</p>' +
                            '        <p>'+ setZero(ckList[i].shelf_num) +'</p>' +
                            '        <p>'+ setZero(ckList[i].location_num) + '/' + cha +'</p>' +
                            '    </td>' +
                            '    <td><p class="bg-lightOrange">库内货物概览</p></td>' +
                            '    <td>' +
                            '        <p>种类总数</p>' +
                            '        <p>净重合计</p>' +
                            '        <p>总重合计</p></td>' +
                            '    <td>' +
                            // '        <p>'+ ckList[i].material_count +'种</p>' +
                            '        <p>- -</p>' +
                            '        <p>未知</p>' +
                            '        <p>未知</p>' +
                            '    </td>' +
                            '</tr>';
                    }
                }

                $("#singleHouse tbody").html(html);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// 出入库统计
function getLogList(cur,per,index){
    let url = "../inStock/inList"; //入库流水
    let data = { 'pageNum':cur , 'per':per }
    if(index == 1){
        url = "../picking/getPickingRecordList.do"
        data = { 'currPage':cur , 'pageSize':per }
    }
    $(`#checkInOutInfo .p${index}`).show().siblings().hide()
    $.ajax({
        "url":url,
        "data": data,
        success:function (res) {
            let info = { 'index': index }
            let str = ``
            let sunPage = 0
            if(index == 1){ // 领料
                let list1 = res.data || []
                sunPage = res.totalPage
                list1.forEach(function(item){
                    str += `
                     <tr>
                        <td>${ item.code }</td>
                        <td>${ item.name }</td>
                        <td>${ item.specifications }</td>
                        <td>${ item.model }</td>
                        <td>${ item.unit }</td>
                        <td>${ item.quantityFact }</td>
                        <td>${ item.departName }</td>
                        <td>${ item.createName }${ new Date(item.deliveryFact).format("yyyy-MM-dd hh:mm:ss") } </td>
                     </tr>
                `
                })
            }else{ // 入库
                let list0 = res.data || []
                sunPage = res.totalPage
                list0.forEach(function(item){
                    str += `
                 <tr>
                    <td>${ item.code }</td>
                    <td>${ item.name }</td>
                    <td>${ item.specifications }</td>
                    <td>${ item.model }</td>
                    <td>${ item.unit }</td>
                    <td>${ item.state == 8 ? item.quantity_fact :item.check_quantity  }</td>
                    <td>${ item.supplier_name }</td>
                    <td>${ item.check_name }${ new Date(item.check_date).format("yyyy-MM-dd hh:mm:ss") } </td>
                 </tr>
                `
                })
            }
            $(`#checkInOutInfo .p${index} tbody`).html(str)
            setPage( $(`#pageP${index}`), cur , sunPage , 'pickOrInList', JSON.stringify(info))

        }
    })
}
//上面是范例区