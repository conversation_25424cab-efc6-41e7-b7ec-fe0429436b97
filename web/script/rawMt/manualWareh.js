var user = null;
var contractDel = null;
var contractInfo = null;
var editContractObj = null;
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
//下面一行看到时候的情况进行修改
bounce_Fixed2.show($("#newMailInfo"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
//下面一行看到时候的情况进行修改
//bounce_Fixed3.show($("#lookpoot"));
bounce_Fixed3.cancel();

$(function(){
    $(".numbsersi").html(0);
    $("#addeqthng").val("");
    $("#addlistdate").val("");
    //对备注输入框中输入的字符进行统计
    $("#addeqthng").on('input focus keyup',function(){
        var str = $(this).val();
        var remain = str.length;
        $(this).parent().parent().children(":first-child").children(":first-child").next().html(remain);
        $(this).attr('maxlength',30);
    })
    //获取质检员数据
    fontchose($(this),'pect');
    //获取入库申请人数据
    fontchose($(this),'name');
    var alllank = [];
    $("#choseprojet").data("addlist",alllank);
})

// creator: sy 2023-12-28   点击增加按钮
function addMore(add,n){
    switch (add) {
        case 'stoadd':
            var protest2 = $(".protest2").val();
            var qualitydate1 = $(".qualitydate1").val();
            if(protest2 == "" || qualitydate1 == ""){
                layer.msg("操作失败！\n" + "当前这组数据录完后才能“增加”下一组！");
            }else{
                let html4 = ``;
                n = n + 1;
                let str4 = ``;
                str4 += `
                    <span class="ty-color-blue funBtn thin-btn addmore" onclick="addMore('stoadd',${n})" style="font-weight: bold;">增加</span>
                `;
                $(".addmoreb .addmore").replaceWith(str4);
                // str4 += `<button onclick="tryen(${n},$(this))">测试</button>`;
                // //现在需要将原有的tryen那行切换城上面的样子
                // $(".catsix button").replaceWith(str4);
                //$("#myDiv").replaceWith("<p>新内容</p>"); // 将<div>替换为<p>标签
                html4 += `
                    <div class="ston42" style="display: flex;justify-content: space-between;margin-top: 15px;">
                        <div>
                            <div class="citem gap">
                                <span class="ty-color-red">*</span>放置于该库位的材料数量
                            </div>
                            <div>
                                <input type="text" placeholder="请录入" class="protest2">
                            </div>
                        </div>
                        <div>
                            <div class="citem gap">
                                <span class="ty-color-red">*</span>A
                            </div>
                            <div>
                                <input id="qualitydatea${n}" class="ty-inputText ibige2 qualitydate1" value="" placeholder="请选择" style="width: 214px;"
                                           name="qualitydate" require/>
                                <span class="ty-color-red funBtn thin-btn pull-right" onclick="dettlelink($(this),4)" style="margin-left: 10px;
                                    font-weight: bold;">删除</span>
                            </div>
                        </div>
                    </div>`;
                // $(".chose4 .bottinpu").children(":first-child").next(".ston42").next().append(html4);
                $(".minge").append(html4);
                laydate.render({elem:'#qualitydatea'+n,format:'yyyy-MM-dd'});
                $(".dettenst").show();
            }
            break;
        case 'sto':
            var lank = $(".addmore2").data("link");
            var stoation  = $(".stoation ").val();
            var protest2 = $(".protest2").val();
            var qualitydate1 = $(".qualitydate1").val();
            if(stoation == "0" || protest2 == "" || qualitydate1 == ""){
                layer.msg("操作失败！\n" + "当前这组数据录完后才能“增加”下一组！");
            }else{
                let html3 = ``;
                n = n + 1 ;
                html3 += `
                    <div class="bottinpu">
                        <div class="ston4" style="display: flex;justify-content: space-between;">
                            <div>
                                <div class="citem gap">
                                     <span class="ty-color-red">*</span>库位
                                </div>
                                <div>
                                    <select class="stoation ibige2" onchange="locatioulication($(this))"></select>
                                </div>
                            </div>
                            <div style="padding-top: 35px;">
                                <span>增加一组“可使用的截止日期”？</span>
                                <span class="ty-color-blue funBtn thin-btn addmore" onclick="addMore('stoadd',1)" style="font-weight: bold;">增加</span>
                            </div>
                        </div>
                        <div class="minge">
                            <div class="ston42" style="display: flex;justify-content: space-between;margin-top: 15px;">
                                <div>
                                    <div class="citem gap">
                                        <span class="ty-color-red">*</span>放置于该库位的材料数量
                                    </div>
                                    <div>
                                        <input type="text" placeholder="请录入" class="protest2">
                                    </div>
                                </div>
                                <div>
                                    <div class="citem gap">
                                        <span class="ty-color-red">*</span>A
                                    </div>
                                    <div>
                                        <input id="qualitydatea${n}" class="ty-inputText ibige2 qualitydate1" value="" placeholder="请选择" style="width: 252px;"
                                               name="qualitydate" require/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span class="ty-color-red funBtn thin-btn" onclick="dettlelink($(this),3)">删除</span>
                    </div>`;
                // $(".chose4").children(":first-child").next().append(html3);
                $(".bingmin").append(html3);
                laydate.render({elem:'#qualitydatea'+n,format:'yyyy-MM-dd'});
                var optionStr = ``;
                optionStr += `<option value="0" style="color:grey;">请选择</option>`;
                lank.forEach(itemtion => {
                    optionStr += `<option value="${itemtion.id}">${itemtion.locationCode}</option>`;
                });
                $(".chose4 .bottinpu:last .ston4 .stoation").html(optionStr);
            }
            break;
        case 'onmig':
            var addlist= $(".ston3 .addlistnum").val();
            var onm = $(".qualitydate2").val();
            if(addlist == "" || onm == ""){
                layer.msg("操作失败！\n" + "当前这组数据录完后才能“增加”下一组！");
            }else{
                let html2 = ``;
                n = n + 2;
                html2 += `
                    <div class="ston3" style="display:flex;justify-content:space-between;margin-top: 20px;">
                        <div>
                            <span class="ty-color-blue funBtn thin-btn pull-right" type="hidden">  </span>
                            <div class="citem gap">
                                <span class="ty-color-red">*</span>入库数量
                            </div>
                            <div>
                                <input type="text" class="addlistnum" placeholder="请录入">
                            </div>
                        </div>
                        <div>
<!--                            <span class="ty-color-blue funBtn thin-btn pull-right" onclick="addMore('onmig')" style="font-width: bold;">增加</span>-->
                            <div class="citem gap">
                                <span class="ty-color-red">*</span>A
                            </div>
                            <div>
                                <input id="qualitydateb${n}" class="ty-inputText ibige2 qualitydate2" value="" placeholder="请选择" style="width: 211px;"
                                    name="qualitydate" require/>
                                <span class="ty-color-red funBtn thin-btn pull-right" onclick="dettlelink($(this),2)" style="margin-left: 10px;font-width: bold;">
                                    删除</span>
                            </div>
                        </div>
                    </div>`;
                $(".chose3").append(html2);
                laydate.render({elem:'#qualitydateb'+n,format:'yyyy-MM-dd'});
            }
            break;
        default:
            var lank = $(".addmore").data("link");
            var stoation = $(".stoation").val();
            var protest = $(".protest").val();
            if(stoation == "0" || protest == ""){
                layer.msg("操作失败！\n" + "当前这组数据录完后才能“增加”下一组！");
            }else{
                let html1 = ``;
                html1 += `
        <div class="flex-box ston2" style="display: flex;justify-content: space-between;margin-top: 14px;">
            <div>
                <div class="citem gap">
                    <span class="ty-color-red">*</span>库位
                </div>
                <div>
                    <select class="stoation ibige2" onchange="locatioulication($(this))" data-box="${lank}"></select>
                </div>
            </div>
            <div>
                <div class="citem gap">
                    <span class="ty-color-red">*</span>放置于该库位的材料数量
                </div>
                <div>
                    <input type="text" placeholder="请录入" class="protest">
                    <span class="ty-color-red funBtn thin-btn" onclick="dettlelink($(this),1)">删除</span>
                </div>
            </div></div>`;
                $(".chose2").append(html1);
                var optionStr = ``;
                optionStr += `<option value="0" style="color:grey;">请选择</option>`;
                lank.forEach(itemtion => {
                    optionStr += `<option value="${itemtion.id}">${itemtion.locationCode}</option>`;
                });
                $(".chose2 .ston2:last .stoation").html(optionStr);
            }
            break;
    }
}

// creator: sy 2023-12-14   点击‘选择材料’按钮展示选择材料弹窗
function chosepret(){
    bounce.show($("#selectmaterial"));
    bounce_Fixed.cancel($("#dettelpont2"));
    $("#havestoation1").attr("class","fa fa-circle-o");
    $("#havestoation2").attr("class","fa fa-circle-o");
    $(".material").attr("disabled",false);
    $(".materialname").attr("disabled",false);
    $(".suppliduct").attr("disabled",false);
    $(".material").removeClass("usunde");
    $(".materialname").removeClass("usunde");
    $(".suppliduct").removeClass("usunde");
    $("#closewinden").show();
    $("#nextture").show();
    $("#mahesure").hide();
    $(".protest").val("");
    $(".addlistnum").val("");
    $(".catfive p").show();
    $(".chose1").hide();
    $(".chose2").hide();
    $(".chose3").hide();
    $(".chose4").hide();
    $(".allprice").hide();
    $(".qualitydate2").val("");
    $(".qualitydate1").val("");
    $("#specifications").val("");
    $("#model").val("");
    $("#measurement").val("");
    $.ajax({
        url:"../picking/getAllMtList.do",
        data:{},
        success:function(res){
            var prolist = res.data || [];
            var list = $("#choseprojet").data("gethas");//现在页面中材料表格中已经有的材料数据
            if(list == undefined || list.length == 0){}else{
                list.forEach(items => {
                    var id = items.id;
                    id = Number(id);
                    prolist.forEach(function(itemp,index){
                        var pid = itemp.id;
                        switch (pid) {
                            case id:
                                prolist.splice(index,1);
                                break;
                        }
                    })
                })
            }
            console.log('删除后的prolist',prolist);
            var optionStr = ``;
            var optionStr2 = ``;
            optionStr += `<option value="0">请选择</option>`;
            optionStr2 += `<option value="0">请选择</option>`;
            prolist.forEach(itempro => {
                optionStr += `<option value="${itempro.id}">${itempro.code}</option>`;
                let expRequired = itempro.expRequired;
                switch (expRequired) {
                    case null:
                        var str = ``;
                        str += `
                            <input type="hidden" id="havestoation">
                            <span onclick="havestoation(1,$(this))" class="radioCon">
                                <i id="havestoation1" class="fa fa-circle-o"></i>需选择库位
                            </span>
                            <span onclick="havestoation(2,$(this))" class="radioCon kwal">
                                <i id="havestoation2" class="fa fa-circle-o"></i>仅录入入库数量，暂不选择库位
                            </span>`;
                        $(".stona").html(str);
                        break;
                    case 0:
                        var str = ``;
                        str += `
                            <input type="hidden" id="havestoation">
                            <span onclick="havestoation(1,$(this))" class="radioCon">
                                <i id="havestoation1" class="fa fa-circle-o"></i>需选择库位
                            </span>
                            <span onclick="havestoation(2,$(this))" class="radioCon kwal">
                                <i id="havestoation2" class="fa fa-circle-o"></i>仅录入入库数量，暂不选择库位
                            </span>`;
                        $(".stona").html(str);
                        break;
                    default:
                        var strq = ``;
                        strq += `
                            <input type="hidden" id="havestoation">
                            <span onclick="havestoation(1,$(this),'que')" class="radioCon">
                                <i id="havestoation1" class="fa fa-circle-o"></i>需选择库位
                            </span>
                            <span onclick="havestoation(2,$(this),'que')" class="radioCon kwal">
                                <i id="havestoation2" class="fa fa-circle-o"></i>仅录入入库数量，暂不选择库位
                            </span>`;
                        $(".stona").html(strq);
                        break;
                }
            });
            $(".material").html(optionStr);
            prolist.forEach(itemp2 => {
                optionStr2 += `<option value="${itemp2.id}">${itemp2.name}</option>`;
            });
            $(".materialname").html(optionStr2);
            $(".material").data("lank",prolist);
            var optionStr3 = ``;
            optionStr3 += `<option value="0">请选择</option>`;
            $(".suppliduct").html(optionStr3);
            var box = [];
            $(".material").data("box",box);
            var box2 = [];
            $(".materialname").data("box2",box2);
            var alllank = $("#choseprojet").data("addlist");
            $("#mahesure").data("addlist",alllank);
            $("#nextture").data("addlist",alllank);
        }
    })
    var nowlank = $("#choseprojet").data("prolink");
    $("#nextture").data("nowlank",nowlank);
    $("#mahesure").data("nowlank",nowlank);
}
// creator: sy 2024-01-02   关闭选择材料弹窗
function closen(tbog){
    switch (tbog) {
        case 1:
            bounce_Fixed.cancel($("#chosepropo"));
            bounce.show($('#selectmaterial'));
            break;
        // default:
        //     bounce.cancel($("#selectmaterial"));
        //     $(".material").attr("disabled",false);
        //     $(".materialname").attr("disabled",false);
        //     $(".suppliduct").attr("disabled",false);
        //     break;
    }
}

// creator: sy 2023-12-14   点击移除按钮给予移除弹窗
function detaddlink(dette){
    bounce.show($("#dettelpont1"));
    var upent = `<span>确定移除这条数据吗？</span>`;
    $("#dettelpont1 .bonceCon div p").html(upent);
    let linst = $(".detter").data("gethas");
    let chosen = dette.siblings(".hd").html();
    chosen = JSON.parse(chosen);
    $(".dettmd").data("gethas",linst);
    $(".dettmd").data("chosi",chosen);
    let topa = dette.parent().parent();
    $(".dettmd").data("kepd",topa);
}
// creator: sy 2023-12-14   筛选框中有数据，第二次点击展示下拉框时给予提示
function determine(check,objn){
    var str = ``;
    switch (check) {
        case 1:
            var box = $(".material").data("box");
            let materid = objn.val();
            materid = Number(materid);
            var list = $(".material").data("lank");
            var optionStr2 = ``;
            if(materid !== 0){
                optionStr2 += `<option value="0">请选择</option>`;
                list.forEach(itemls => {
                    if(itemls.id == materid){
                        optionStr2 += `<option value ="${itemls.id}">${itemls.name}</option>`;
                        $("#specifications").val(itemls.specifications);
                        $("#model").val(itemls.model);
                        $("#measurement").val(itemls.unit);
                    }
                })
                $(".materialname").html(optionStr2);
                getsuppliduct(materid);
                //getLocation(materid);
                //getstoration(materid);
            }else{
                optionStr2 += `<option value="0">请选择</option>`;
                list.forEach(itemls2 => {
                    optionStr2 += `<option value ="${itemls2.id}">${itemls2.name}</option>`;
                })
                $(".materialname").html(optionStr2);
                getsuppliduct(materid);
                //getLocation(materid);
                //getstoration(materid);
            }
            var pronum = $(".material").val();
            pronum = Number(pronum);
            box.push(pronum);
            var leaden = box.length;
            if(pronum !== 0){
                if(leaden >= 2){
                    bounce_Fixed.show($("#dettelpont2"));
                    str = `
                        <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontsure(1)">确定</span>
                    `;
                    $("#dettelpont2").children(".bonceFoot").html(str);
                }
            }
            //思路：监听鼠标点击筛选框，第一次不监听，当第二次点击筛选框的时候判断pronum是否有值，如果有值，出现弹窗
            //$(".material").click(function(){})
            $(".materialname").data("box2",[]);
            break;
        case 2://materialname
            var box2 = $(".materialname").data("box2");
            var proname = $(".materialname").val();
            proname = Number(proname);
            box2.push(proname);
            var leaden2 = box2.length;
            if(proname !== 0){
                if(leaden2 >= 2){
                    bounce_Fixed.show($("#dettelpont2"));
                    str = `
                        <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontsure(2)">确定</span>`;
                    $("#dettelpont2").children(".bonceFoot").html(str);
                }
            }
            //$(".materialname").click(function(){})
            break;
    }
}
// creator: sy 2023-12-28  删除添加的库位数据
function dettlelink(obj1,keept){
    switch (keept) {
        case 1:
            obj1.parent().parent().parent().remove();
            break;
        case 2:
            obj1.parent().parent().parent().remove();
            break;
        case 3:
            obj1.prev().remove();
            obj1.remove();
            break;
        case 4:
            obj1.parent().parent().parent().remove();
            //需要在只剩下一个的时候将删除按钮隐去
            if($(".minge").children().length === 0){//一个子级都没有
                $(".dettenst").hide();
            }else {}//有至少一个子级
            break;
    }
}
// creator: sy 2024-01-05   删除材料列表中数据
function dettmade(){
    let haslink = $(".dettmd").data("gethas");
    let choslist = $(".dettmd").data("chosi");
    //choslist = JSON.parse(choslist);
    let pasebook = $(".dettmd").data("kepd");
    if(haslink == undefined){//列表中原本没有数据
        pasebook.remove();
    }else{//列表中原来有数据
        haslink.forEach(function(items,index){
            let lid = items.id;
            let cid = choslist.id;
            if(lid == cid){
                haslink.splice(index,1);
            }
        })
        let str = ``;
        haslink.forEach(itemh => {
            str += `
                 <tr>
                    <td>${itemh.materialname}</td>
                    <td>${itemh.material}</td>
                    <td>${itemh.model}</td>
                    <td>${itemh.specifications}</td>
                    <td>${itemh.addlistnum}</td>
                    <td>${handleNull(itemh.measurement)}</td>
                    <td>${handleNull(itemh.stoation)}</td>
                    <td>${itemh.suppliduct}</td>
                    <td>
                        <span class="ty-color-red detter" onclick="detaddlink($(this))">移除</span>
                        <span class="hd">${JSON.stringify(itemh)}</span>
                    </td>
                </tr>`;
            $("#proallmsg").html(str);
        })
        $("#choseprojet").data("gethas",haslink);
        $(".detter").data("gethas",haslink);
    }
    bounce.cancel($("#dettelpont1"));
}

// creator: sy 2023-12-13
function fontchose(jsont,diffent){
    switch (diffent) {
        case 'name':
            $.ajax({
                url:"../picking/getPickingUserList.do",
                data:{
                    code:"pickingPerson"
                },
                success:function(res){
                    var list2 = res.data || [];//list2用于存储调用接口后获取的入库申请人数据
                    var optionStr = ``;
                    optionStr += `<option value="0" class="clolder">请选择</option>`;
                    list2.forEach(itemls2 => {
                        optionStr += `<option value="${itemls2.userID}">${itemls2.userName}</option>`;
                    });
                    $(".addlistname").html(optionStr);
                }
            })
            break;
        case 'pect':
            $.ajax({
                url:"../picking/getPickingUserList.do",
                data:{
                    code:"pickingPerson"
                },
                success:function(res){
                    var list3 = res.data || [];
                    var optionStr =   ``;
                    optionStr += `<option value="0" class="clolder">请选择</option>`;
                    list3.forEach(item => {
                        optionStr += `<option value="${item.userID}">${item.userName}</option>`;
                    })
                    $(".inspector").html(optionStr);
                }
            })
            break;
    }
}

// creator: sy 2023-12-28
function getfirst(objn){
    let mater = objn.val();
    $("#havestoation1").data("proid",mater);
}
//creator: sy 2023-12-27    获取库位数据
function getLocation(){
    var locaid = $(".stoation").data("proid");
    $.ajax({
        url:"../picking/getLwLocationList.do",
        data:{
            mtId:locaid
        },
        success:function(res){
            var list = res.data || [];
            var optionStr = ``;
            list.forEach(itemlik => {
                optionStr += `<option value="${itemlik.id}">${itemlik.locationCode}</option>`;
            });
            $(".stoation").html(optionStr);
        }
    })
}
function getMatername(){
    var namelist = [];
    var optionStr = ``;
    optionStr += `<option value="" style="color:grey;">请选择</option`;
    namelist.forEach(itemnam => {
        optionStr += `<option value="${itemnam.id}">${itemnam.name}</option>`;
    });
    $(".materialname").html(optionStr);
}
// creator: sy 2023-12-14   获取对应的材料代号数据
function getMaternumb(){
    // var numlist = [];//用于存储获取到的材料代号数据
    // var optionStr = ``;
    // optionStr += `<option value="" style="color:grey;">请选择</option`;
    // numlist.forEach(itemnum => {
    //     optionStr += `<option value="${itemnum.id}">${itemnum.name}</option>`;
    // });
    // $(".material").html(optionStr);
}
// creator: sy 2023-12-14   获取对应的库位数据
function getstoration(list){
    var storation = list;
    $(".addmore").data("link",storation);
    $(".addmore2").data("link",storation);
    var optionStr = ``;
    optionStr += `<option value="0" style="color:grey;">请选择</option>`;
    storation.forEach(itemtion => {
        optionStr += `<option value="${itemtion.id}">${itemtion.locationCode}</option>`;
    });
    $(".stoation").html(optionStr);
}
// creator: sy 2023-12-14   获取对应的供应商数据
function getsuppliduct(mage){
    console.log('mage的数据',mage);
    let numb = 1;
    numb = numb.toString();
    $.ajax({
        url:"../mt/suppliers",
        data:{
            id:mage,
            //id:材料id
            //enabled:固定参数1
            enabled:numb
        },
        success:function(res){
            var ductlist = res.data || [];
            var optionStr = ``;
            if(ductlist.length == 0){
                optionStr +=`<option value="9" class="clolder">本次入库不记录供应商</option>`;
            }else{
                optionStr += `<option value="0" class="clolder">请选择</option>`;
                ductlist.forEach(itemlist => {
                    optionStr += `<option value="${itemlist.supplier_id}">${itemlist.name}</option>`;
                });
            }
            $(".suppliduct").html(optionStr);
        }
    })
}

// creator: sy 2024-01-02   处理表格中没有数据的情况
function handleNull(str){
    var result = str == null || str == undefined || str == 'null' || str == '' || str == ""?'——':str;
    return result;
}
// creator: sy 2023-12-14   是否选择库位
function havestoation(type,thisObj,dife){
    setRadioSelectkw("havestoation",[1,2],type,thisObj);
    switch (dife) {
        case 'que':
            switch (type) {
                case 1://需要库位
                    $(".chose1").hide();
                    $(".chose2").hide();
                    $(".chose3").hide();
                    $(".chose4").show();
                    $(".allprice").hide();
                    $("#closewinden").show();
                    $("#nextture").show();
                    $("#mahesure").hide();
                    var proid = $("#havestoation1").data("proid");
                    $(".stoation").data("proid",proid);
                    $.ajax({
                        url:"../picking/getLwLocationList.do",
                        data:{
                            mtId:proid
                        },
                        success:function(res){
                            var list = res.data || [];
                            getstoration(list);
                        }
                    })
                    var parent = $(".chose4");
                    var allchidren = parent.children();
                    var unwantedChildren = allchidren.slice(2);
                    unwantedChildren.remove();
                    break;
                case 2://不需要库位
                    $(".chose1").hide();
                    $(".chose2").hide();
                    $(".chose3").show();
                    $(".chose4").hide();
                    $(".allprice").hide();
                    $("#closewinden").show();
                    $("#nextture").hide();
                    $("#mahesure").show();
                    break;
            }
            break;
        default:
            switch (type) {
                case 1://需要库位
                    $(".chose1").hide();
                    $(".chose2").show();
                    $(".allprice").hide();
                    $("#closewinden").show();
                    $("#nextture").show();
                    $("#mahesure").hide();
                    var proid = $("#havestoation1").data("proid");
                    $(".stoation").data("proid",proid);
                    $.ajax({
                        url:"../picking/getLwLocationList.do",
                        data:{
                            mtId:proid
                        },
                        success:function(res){
                            var list = res.data || [];
                            getstoration(list);
                        }
                    })
                    var parent = $(".chose2");//选中父级
                    var allchildren = parent.children();//选中父级下所有子级
                    var unwantedChildren = allchildren.slice(1);//选中除第一个子级以外的全部子级
                    unwantedChildren.remove();//移除除第一个子级外的全部子级
                    break;
                case 2://不选择库位
                    $(".chose1").show();
                    $(".chose2").hide();
                    $(".allprice").hide();
                    $("#closewinden").show();
                    $("#nextture").hide();
                    $("#mahesure").show();
                    break;
            }
            break;
    }
}

// creator: sy 2024-01-04   判断库位选择框选择的数据是否重复
function locatioulication(objo){
    let choseston = objo.val();
    let ablen = false;
    let ston2 = objo.parent().parent().parent(".ston2");
    ston2.siblings().each(function(){
        let item1 = $(this).find(".stoation").val();
        if(item1 == choseston){ ablen = true;}
    })
    switch (ablen) {
        case true:
            layer.msg("已选择了该库位，请重新选择！");
            objo.val(0);
            // var optionStr = ``;
            // optionStr += `<option value="0" style="color:grey;">请选择</option>`;
            // let lankbox = objo.data("box");
            // lankbox.forEach(itemtion => {
            //     optionStr += `<option value="${itemtion.id}">${itemtion.locationCode}</option>`;
            // });
            // $(".chose2 .ston2:last .stoation").html(optionStr);
            break;
        default:
            break;
    }

}

// creator: sy 2023-12-26   手动入库页面确定按钮
function madesure(){
    var userId = $("#addlistdate").val();
    var usename = $(".addlistname").find("option:selected").text();
    var inspector = $(".inspector").val();
    var inspectorName = $(".inspector").find("option:selected").text();
    var addlistdate = $("#addlistdate").val();
    var lank = $("#madesurend").data("alllink");
    var materialLocation = $("#madesurend").data("choslink");
    var prolist = [];
    var json1 = {
        id:"",
        supplierMaterial:"",
        isKw:"",
        numList:[],
        num:""
    };
    var json2 = {
        //materialWarehouse:"",
        materialLocation:"",
        num:"",
        //bzDate:""
    };
    lank.forEach(itemk => {
        json1.id = itemk.id;
        json1.supplierMaterial = itemk.supplierId;
        var chosen = itemk.havestoation;
        switch (chosen) {
            case 1:
                json1.isKw = 1;
                break;
            case 2:
                json1.isKw = 0;
        }
    })
    materialLocation.forEach(itema =>{

        // json2.materialLocation = itema.stoationid;
        // json2.num = itema.protest;
        json1.numList.push(json2);
    })
    prolist.push(json1);
    prolist = JSON.stringify(prolist);

    $.ajax({
        url:"../picking/manualWarehousing.do",
        data:{
            creator:userId,
            //creator:申请用户id
            //createName:申请用户名称
            inspector:inspector,
            //inspector:质检员ID
            inspectorName:inspectorName,
            //inspectorName:质检员姓名
            //mtList:入库信息 json字符串
            mtList:prolist,
            //mtList:{
            //      id:材料id
            //      supplierMaterial:供应商id
            //      isKw:是否选择库位 1是 0否
            //      numList:数量集合，如果材料没有保质期无需传该参数
            //      numList:{
            //          materialWarehouse:仓库id
            //          materialLocation:库位id
            //          num:数量
            //          bzDate:保质日期
            //      }
            //      num:入库总数量
            //}
            //arriveDate:入库时间
            arriveDate:addlistdate
        },
        success:function(res){

        }
    })
}
// creator: sy 2023-12-28   选择‘仅录入入库数量，暂不选择库位’后的确定按钮/选择'需选择库位‘后的确定按钮
function madesurene(keped){
    switch (keped) {
        case 1://选择的是需要库位按钮
            var allpro = $(".allpro").val();
            var stoation = $(".stoation").val();
            var protest = $(".protest").val();
            // var stoation2 = $(".stoation").find("option:selected").text();
            if(allpro == ""){
                layer.msg("操作失败！\n" + "还有必填项尚未填写！");
                return false;
            }
            break;
        case 2://不需要库位
            var addlistnum = $(".addlistnum").val();
            if(addlistnum == "") {
                layer.msg("操作失败！\n" + "还有必填项尚未填写！");
                return false;
            }
            break;
    }
    var suppliduct = $(".suppliduct").val();
    var material = $(".material").val();
    var materialname = $(".materialname").val();
    var specifications = $("#specifications").val();
    var model = $("#model").val();
    var measurement = $("#measurement").val();
    if(material == 0){
        layer.msg("操作失败！\n" + "还有必填项尚未填写！");
    }else if(materialname == 0){
        layer.msg("操作失败！\n" + "还有必填项尚未填写！");
    }else if(suppliduct == 0){
        layer.msg("操作失败！\n" + "还有必填项尚未填写！");
    }else{
        bounce.cancel($("#selectmaterial"));
        var jsonall = {
            id:"",
            supplierId:"",
            material:"",
            materialname:"",
            specifications:"",
            model:"",
            havestoation:"",
            measurement:"",
            suppliduct:"",
            addlistnum:"",
            stoation:"",
            protest:""
        };
        var havestoation = $("#havestoation").val();
        jsonall.havestoation = havestoation;
        jsonall.id = material;
        jsonall.supplierId = suppliduct;
        //jsonall.stoation = stoation2;
        var suppliduct2 = $(".suppliduct").find("option:selected").text();
        var material2= $(".material").find("option:selected").text();
        var materialname2 = $(".materialname").find("option:selected").text();
        jsonall.material = material2;
        jsonall.materialname = materialname2;
        jsonall.specifications = specifications;
        jsonall.model = model;
        jsonall.measurement = measurement;
        jsonall.suppliduct = suppliduct2;
        //将获取到的数据设置为小数点后两位
        switch (keped) {
            case 1:
                var lank = $("#mahesure").data("allbox");
                lank.push(jsonall);
                var new_arr = [];
                for(var i = 0; i< lank.length; i++){//所以说现在它又能成功去重了？？？
                    var items = lank[i];
                    if($.inArray(items,new_arr)==-1){
                        new_arr.push(items);
                    }
                }
                lank = new_arr;
                // var unique_arr = [];
                // $.each(lank, function(i, el){
                //     if($.inArray(el, unique_arr) === -1) unique_arr.push(el);
                // })
                // lank = unique_arr;
                // console.log('unique_arr',unique_arr);
                $("#madesurend").data("alllink",lank);
                var nowlank = $("#mahesure").data("nowlanke");
                if(nowlank == undefined || nowlank.length == 0){}else{
                    lank.forEach(function(itemk,index){
                        var lid = itemk.id;
                        nowlank.forEach(itemn => {
                            var nid = itemn.id;
                            if(lid == nid){
                                layer.msg("操作失败，选择材料重复!");
                                return false;
                                //lank.splice(index,1);
                            }
                        })
                    })
                }
                let sumall = $("#mahesure").data("sumall");
                allpro = Number(allpro);
                switch (allpro) {
                    case sumall:
                        var str = ``;
                        lank.forEach(itemall => {
                            str += `
                <tr>
                    <td>${itemall.materialname}</td>
                    <td>${itemall.material}</td>
                    <td>${itemall.model}</td>
                    <td>${itemall.specifications}</td>
                    <td>${itemall.addlistnum}</td>
                    <td>${handleNull(itemall.measurement)}</td>
                    <td>${handleNull(itemall.stoation)}</td>
                    <td>${itemall.suppliduct}</td>
                    <td>
                        <span class="ty-color-red detter" onclick="detaddlink($(this))">移除</span>
                        <span class="hd">${JSON.stringify(itemall)}</span>
                    </td>
                </tr>`;
                            $("#proallmsg").html(str);
                        })
                        $("#choseprojet").data("prolink",lank);
                        var mahesure = $("#mahesure").data("choslink");
                        allpro = Number(allpro);
                        mahesure.forEach(itema => {
                            itema.allprolink = allpro;
                        })
                        $("#madesurend").data("choslink",mahesure);
                        console.log('选择的材料数据1',lank);
                        $("#choseprojet").data("gethas",lank);
                        $(".detter").data("gethas",lank);
                        break;
                    default:
                        bounce_Fixed.show($("#chosepropo"));
                        //return  false;
                        break;
                }
                break;
            case 2:
                var result = parseFloat(addlistnum);
                if(isNaN(result)){
                    return false;
                }
                result = Math.round(addlistnum * 100) / 100;
                var s_x = result.toString();
                var pos_decimal = s_x.indexOf('.');
                if(pos_decimal < 0){
                    pos_decimal = s_x.length;
                    s_x += '.';
                }
                while (s_x.length <= pos_decimal +2 ){
                    s_x += '0';
                }
                addlistnum = s_x;
                jsonall.addlistnum = addlistnum;
                //应该在一开始选择之前定义一个数组
                var lank = $("#mahesure").data("addlist");
                lank.push(jsonall);
                $("#madesurend").data("alllink",lank);
                var nowlank = $("#mahesure").data("nowlank");
                if(nowlank == undefined || nowlank.length == 0){}else{
                    lank.forEach(function(itemk,index){
                        var lid = itemk.id;
                        nowlank.forEach(itemn => {
                            var nid = itemn.id;
                            if(lid == nid){
                                layer.msg("操作失败，选择材料重复!");
                                return false;
                                //lank.splice(index,1);
                            }
                        })
                    })
                }
                var str = ``;
                lank.forEach(itemall => {
                    str += `
                <tr>
                    <td>${itemall.materialname}</td>
                    <td>${itemall.material}</td>
                    <td>${itemall.model}</td>
                    <td>${itemall.specifications}</td>
                    <td>${itemall.addlistnum}</td>
                    <td>${handleNull(itemall.measurement)}</td>
                    <td>${handleNull(itemall.stoation)}</td>
                    <td>${itemall.suppliduct}</td>
                    <td>
                        <span class="ty-color-red detter" onclick="detaddlink($(this))">移除</span>
                        <span class="hd">${JSON.stringify(itemall)}</span>
                    </td>
                </tr>`;
                    $("#proallmsg").html(str);
                })
                $("#choseprojet").data("prolink",lank);
                break;
        }
    }
}

// creator: sy 2023-12-14   点击下一步展示总数输入框
function pontnext(){
    var material = $(".material").val();
    var materialname = $(".materialname").val();
    var suppliduct = $(".suppliduct").val();
    var stoation = $(".stoation").val();//在填写了两行库位数据后，怎么将两行的数据都存起来用呢
    let stoaall = [];
    $(".ston2").each(function(){
        let stonId = $(this).find(".stoation").val();
        let stonname = $(this).find(".stoation").find("option:selected").text();
        if(stonId && stonId !== ""){
            let index = stoaall.findIndex(value => Number(value.materialLocation) === Number(stonId));
            if(index === -1) stoaall.push({"materialLocation":stonId,"name":stonname});
        }
    })//stoaall现在是选择的所有库位数据
    //var numArr = []; // 定义一个空数组
    //         var txt = $('#box').find(':text'); // 获取所有文本框
    //         for (var i = 0; i < txt.length; i++) {
    //             numArr.push(txt.eq(i).val()); // 将文本框的值添加到数组中
    //         }
    //         console.info(numArr);
    let proall = [];
    let txt = $(".ston2").find(".protest");
    for(let i = 0; i < txt.length; i++){
        let txet = txt.eq(i).val();
        txet = Number(txet);
        proall.push(txet);
    }
    var protest = $(".protest").val();//应该也需要将手动填写的数量也存入数组中
    if(material == 0){
        //layer.msg("操作失败！\n" + "还有必填项尚未填写！");
    }else if(materialname == 0){
        //layer.msg("操作失败！\n" + "还有必填项尚未填写！");
    }else if(suppliduct == 0){
        //layer.msg("操作失败！\n" + "还有必填项尚未填写！");
    }else if(stoation == ""){
        //layer.msg("操作失败！\n" + "还有必填项尚未填写！");
    }else if(protest == ""){
        //layer.msg("操作失败！\n" + "还有必填项尚未填写！");
    }else{
        $(".material").attr("disabled",true);
        $(".materialname").attr("disabled",true);
        $(".suppliduct").attr("disabled",true);
        $(".material").addClass("usunde");
        $(".materialname").addClass("usunde");
        $(".suppliduct").addClass("usunde");
        $("#nextture").hide();
        $("#mahesure").show();
        var matenumb = $(".material").find("option:selected").text();
        var matername = $(".materialname").find("option:selected").text();
        //var stoation2 = $(".stoation").find("option:selected").text();
        var specifications = $("#specifications").val();
        var model = $("#model").val();
        var measurement = $("#measurement").val();
        var suppliduct = $(".suppliduct").find("option:selected").text();
        var allprolink = $(".allpro").val();
        //var stoation = $(".stoation").text();
        var prolank = [];
        var json = {
            matenumb:"",
            matername:"",
            specifications:"",
            model:"",
            measurement:"",
            suppliduct:"",
            stoation:[],
            //stoationid:"",
            protest:[],
            addlistnum:"",
            allprolink:""
        };
        json.matenumb = matenumb;
        json.matername = matername;
        json.specifications = specifications;
        json.model = model;
        json.measurement = measurement;
        json.suppliduct = suppliduct;
        json.stoation = stoaall;
        //json.stoation = stoation2;
        //json.stoationid = stoation;
        json.protest = proall;
        //json.protest = protest;
        json.allprolink = allprolink;
        prolank.push(json);//mahesure
        $(".catfive p").hide();
        $(".chose1").hide();
        $(".chose2").hide();
        $(".allprice").show();
        $("#nextture").hide();
        var bopx = $("#nextture").data("addlist");
        //$("#mahesure").data("alllink",prolank);
        var strboot = `
        <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel();" id="closewinden">取消</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontnext()" id="nextture">下一步</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="madesurene(1)" id="mahesure">确定</span>
    `;
        $("#choseprolite").html(strboot);
        $("#mahesure").data("choslink",prolank);
        $("#mahesure").data("allbox",bopx);
        $("#nextture").hide();
        $(".allpro").val("");
        var nowlank = $("#nextture").data("nowlank");
        $("#mahesure").data("nowlanke",nowlank);
        //            var sum = 0; // 初始化总和变量
        //
        //             $('.inputField').each(function(){ // 遍历所有具有inputField类名的元素
        //                 sum += parseFloat($(this).val()); // 将当前元素的值转换成float型并与sum相加
        //             });
        let sum = 0;
        $(".protest").each(function(){
            sum += parseFloat($(this).val());
        })
        console.log('sum是每行材料数量之和么',sum);
        $("#mahesure").data("sumall",sum);
    }
}
// creator: sy 2023-12-14   点击确定按钮清空筛选框中的数据
function pontsure(numb){
    switch (numb) {
        case 1:
            // var strm = `<option value="0">请选择</option>`;
            //$(".material").val("");
            // var str1 = `<option value="0" class="clolder">请选择</option>`;
            $(".materialname").val(0);
            let optionen1 = $(".suppliduct").val();
            if(optionen1 == "9" || optionen1 == 9){
            }else{
                optionen1 = 0;
            }
            $(".suppliduct").val(optionen1);
            //$(".suppliduct").val("");
            // var str12 = `<option value="" style="color:grey;">请选择</option>`;
            $(".stoation").val(0);
            //$(".stoation").val("");
            $(".protest").val("");
            $(".addlistnum").val("");
            bounce_Fixed.cancel($("#dettelpont2"));
            break;
        case 2:
            var strn =`<option value="0">请选择</option>`;
            $(".materialname").html(strn);
            let optionen2 = $(".suppliduct").val();
            if(optionen2 == "9" || optionen2 == 9){
            }else{
                optionen2 = 0;
            }
            $(".suppliduct").val(optionen2);
            var str22 = `<option value="" style="color:grey;">请选择</option>`;
            $(".stoation").html(str22);
            $(".protest").val("");
            $(".addlistnum").val("");
            bounce_Fixed.cancel($("#dettelpont2"));
            break;
    }
}

// creator: sy 2023-12-14   复选框选中
function setRadioSelectkw(str,arr,selectVal,thisObj){
    var isEdit = $("#isEdit").val();
    var idStr = "#";
    if(isEdit === "1"){
        idStr = "#edit_";
    }
    idStr += str ;
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $(idStr).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $(idStr + arr[i]).attr("class" , "fa fa-circle-o");
    }
    $(idStr + selectVal).attr("class" , "fa fa-dot-circle-o");
}

// creator: sy 尝试编写，成功后删除
function tryen(n,thisobj){
    let str = ``;
    n=n+1;
    str += `
        <input id="try${n}" class="tryt timeRender layui-input" value="" placeholder="请录入测试内容" />
    `;
    thisobj.next().next().append(str);
    laydate.render({elem: '#try'+n, format: 'yyyy-MM-dd'});
}

laydate.render({elem: '#addlistdate', format: 'yyyy-MM-dd'});
laydate.render({elem:'#try1',format:'yyyy-MM-dd'});
laydate.render({elem:'#qualitydateb2',format:'yyyy-MM-dd'});
laydate.render({elem:'#qualitydatea1',format:'yyyy-MM-dd'});