var user = null;
var contractDel = null;
var contractInfo = null;
var editContractObj = null;
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
//下面一行看到时候的情况进行修改
bounce_Fixed2.show($("#newMailInfo"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
//下面一行看到时候的情况进行修改
//bounce_Fixed3.show($("#lookpoot"));
bounce_Fixed3.cancel();

$(function(){
    $(".numbsersi").html(0);
    $("#addeqthngd").val("");
    $("#taklistdate").val("");
    $("#addeqthngd").on('input focus keyup',function(){
        var str = $(this).val();
        var remain = str.length;
        $(this).parent().parent().children(":first-child").children(":first-child").next().html(remain);
        $(this).attr('maxlength',30);
    })
    //获取领料人数据
    takwhopeop();
    $(".takuseplce").val("");
})

// creator: sy 2024-01-17   点击增加按钮
function addMore(keedif,b){
    switch (keedif) {
        case 1:
            let lank = $(".addmore").data("link");
            let stoation = $(".stoation ").val();
            let protest = $(".protest2").val();
            if(stoation == "0" || protest == ""){
                layer.msg("操作失败！\n" + "当前这组数据录完后才能“增加”下一组！");
            }else{
                let html1 = ``;
                html1 += `
            <div class="flex-box ston2" style="display:flex;justify-content:space-between;margin-top: 21px;">
                <div>
                    <div class="citem gap">
                        <span class="ty-color-red">*</span>库位
                    </div>
                    <div>
                        <select class="stoation ibige2" onchange="getstoration($(this))"></select>
                    </div>
                </div>
                <div>
                    <div class="citem gap">
                        <span class="ty-color-red">*</span>数量
                    </div>
                    <div>
                        <input type="text" placeholder="请录入" class="protest2">
                        <span class="ty-color-red funBtn thin-btn" onclick="dettlelink($(this),1)">删除</span>
<!--                        <span class="blueLinkBtn ty-color-blue funBtn thin-btn addmore" onclick="addMore()">新增</span>-->
                    </div>
                </div>
            </div>`;
                $(".stoboot").append(html1);
                let optionStr = ``;
                optionStr += `<option value="0" style="color:grey;">请选择</option>`;
                lank.forEach(itemk => {
                    optionStr += `<option value="${itemk.id}">${itemk.locationCode}</option>`;
                });
                $(".stoboot .flex-box:last .stoation").html(optionStr);
            }
            break;
        case 2:
            let stoation2 = $(".stoation").val();
            let protest2 = $(".protest2 ").val();
            if(stoation2 == "" || protest2 == ""){
                layer.msg("操作失败！\n" + "当前这组数据录完后才能“增加”下一组！");
            }else{
                let html2 = ``;
                b = b + 1;
                let str2 = ``;
                str2 +=`
                    <span class="blueLinkBtn ty-color-blue funBtn thin-btn addmore" onclick="addMore(2,${b})">新增</span>
                `;
                $(".addmore").replaceWith(str2);
                html2 += `
                    <div class="flex-box ston2" style="display:flex;justify-content:space-between;margin-top: 21px;">
                        <div>
                            <div class="citem gap">
                                <span class="ty-color-red">*</span>库位
                            </div>
                            <div>
                                <select class="stoation ibige2" onchange="getstoration($(this))"></select>
                            </div>
                        </div>
                        <div>
                            <div class="citem gap">
                                <span class="ty-color-red">*</span>数量
                            </div>
                            <div>
                                <input type="text" placeholder="请录入" class="protest2">
                                <span class="ty-color-red funBtn thin-btn" onclick="dettlelink($(this),1)">删除</span>
                            </div>
                        </div>
                    </div>
                `;
                $(".stoboot").append(html2);
                let lank = $(".addmore").data("link");
                let optionStr = ``;
                optionStr += `<option value="0" style="color:grey;">请选择</option>`;
                lank.forEach(itemk => {
                    optionStr += `<option value="${itemk.id}">${itemk.locationCode}</option>`;
                });
                $(".stoboot .flex-box:last .stoation").html(optionStr);
            }
            break;
    }
}

// creator: sy 2024-01-23   点击上一步按钮回到方才的位置
function backlast(){
    $("#closewinden").show();
    $("#nextture").show();
    $("#lastpont").hide();
    $("#mahesure").hide();
    $(".streatbox3").hide();
    $("#chosestoation1").attr("class","fa fa-circle-o");
    $("#chosestoation2").attr("class","fa fa-circle-o");
}

// creator: sy 2023-12-18   点击‘选择材料’按钮展示的选择材料弹窗
function chosepret(){
    bounce.show($("#taksectmral"));
    $("#nextture").show();
    $("#closewinden").show();
    $("#mahesure").hide();
    $("#lastpont").hide();
    $(".takfour p ").show();
    $(".streatbox2").hide();
    $(".streatbox3").hide();
    $("#chosestoation1").attr("class","fa fa-circle-o");
    $("#chosestoation2").attr("class","fa fa-circle-o");
    $("#specifications").val("");
    $("#model").val("");
    $(".protest2").val("");
    $("#measurement").val("");
    let useid = $(".takpicker").val();
    $.ajax({
        url:"../picking/getAllMtList.do",
        data:{
            //Integer userId  //领料人id
            userId:useid
        },
        success:function(res){
            let projlist = res.data || [];
            let lank = $("#choseprojet").data("cholist");//现在页面中材料表格中已经选择的材料数据
            if(lank == undefined || lank.length == 0){}else{
                lank.forEach(itemk => {
                    let id = itemk.id;
                    id = Number(id);
                    projlist.forEach(function(itemp,index){
                        let pid = itemp.id;
                        switch (pid) {
                            case id:
                                projlist.splice(index,1);
                        }
                    })
                })
            }
            console.log('删除后的projlist',projlist);
            let optionStr = ``;
            let optionStr2 = ``;
            optionStr += `<option value="0">请选择</option>`;
            optionStr2 += `<option value="0">请选择</option>`;
            projlist.forEach(itemp => {
                optionStr += `<option value="${itemp.id}">${itemp.code}</option>`;
            });
            $(".material").html(optionStr);
            projlist.forEach(itemp2 => {
                optionStr2 += `<option value="${itemp2.id}">${itemp2.name}</option>`;
            });
            $(".materialname").html(optionStr2);
            $(".material").data("lank",projlist);
            var box = [];
            $(".material").data("box",box);
            var box2 = [];
            $(".materialname").data("box2",box2);
        }
    })
}
// creator: sy 2023-12-18   是否选择库位
function chosestoation(type,thisObj){
    setRadioSelectkwx("chosestoation",[1,2],type,thisObj);
    switch (type) {
        case 1://从库位上选择材料
            $(".streatbox3").show();
            $(".streatbox2").hide();
            $(".protest2").val("");
            $(".protest1").val("");
            //这种情况时不仅要获取供应商数据，还要获取入库数据
            let material = $(".material").val();//获取材料id
            let materialname = $(".materialname").val();
            if(material == "0"){
                if(materialname == "0"){
                    let allstr = `<option value="0" class="clolder">请选择</option>`;
                    $(".suppliduct ").html(allstr);
                    $(".stoation ").html(allstr);
                }else{
                    materialname = Number(materialname);
                    $(".suppliduct").data("proid2",materialname);
                    getSuppleer(materialname);
                    getLocation(materialname);
                }
            }else{
                material = Number(material);
                $(".suppliduct").data("proid",material);
                getSuppleer(material);
                getLocation(material);
            }
            let box1 = [];
            $(".suppliduct").data("box1",box1);
            let strm = `
                <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel();" id="closewinden">取消</span>
                <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="backlast()" id="lastpont">上一步</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontnext()" id="nextture">下一步</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="madehsre(1)" id="mahesure">确定</span>
            `;
            $("#taksectmral .bonceFoot").html(strm);
            $("#closewinden").hide();
            $("#lastpont").show();
            $("#nextture").hide();
            $("#mahesure").show();
            break;
        case 2://仅录入入库数量，暂不选择库位
            $(".streatbox2").show();
            $(".streatbox3").hide();
            $(".protest2").val("");
            $(".protest1").val("");
            //这种情况只需要获取供应商数据就可以
            let material2 = $(".material").val();//获取材料id
            let materialname2 = $(".materialname").val();
            if(material2 == "0"){
                if(materialname2 == "0"){}else{
                    materialname2 = Number(materialname2);
                    getSuppleer(materialname2);
                }
            }else{
                material2 = Number(material2);
                getSuppleer(material2);
            }
            let str2 = `
                <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel();" id="closewinden">取消</span>
                <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="backlast()" id="lastpont">上一步</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontnext()" id="nextture">下一步</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="madehsre(2)" id="mahesure">确定</span>
            `;
            $("#taksectmral .bonceFoot").html(str2);
            let box2 = [];
            $(".suppliduct").data("box2",box2);
            $("#nextture").hide();
            $("#mahesure").show();
            $("#lastpont").hide();
            $("#closewinden").show();
            break;
    }
}

// creator: sy 2024-01-17   删除
function dettlelink(objd){
    objd.parent().parent().parent().remove();
}

// creator: sy 2024-01-16   获取库位数据
function getLocation(stonal){
    $.ajax({
        url:"../picking/getLwLocationList.do",
        data:{
            mtId:stonal
        },
        success:function(res){
            var list = res.data || [];
            var optionStr = ``;
            optionStr += `<option value="0">请选择</option>`;
            list.forEach(itemlik => {
                optionStr += `<option value="${itemlik.id}">${itemlik.locationCode}</option>`;
            });
            $(".stoation").html(optionStr);
            $(".addmore").data("link",list);
        }
    })
}
// creator: sy 2024-01-16   筛选框中有数据，第二次点击展示下拉框时给予提升
function getMaterm(thisobj,numb){
    let str =``;
    switch (numb) {
        case 1:
            let box = $(".material").data("box");
            let materid = thisobj.val();
            materid = Number(materid);
            let materialname = $(".materialname").val();
            materialname = Number(materialname);
            let list = $(".material").data("lank");
            let optionStr2 = ``;
            let optionStr3 = ``;
            if(materid !== 0){
                if(materialname == "0"){
                    optionStr3 = `<option value="0">请选择</option>`;
                    $(".suppliduct").html(optionStr3);
                }else{
                    getSuppleer(materialname);
                }
                optionStr2 += `<option value="0">请选择</option>`;
                list.forEach(itemt => {
                    if(itemt.id == materid){
                        optionStr2 += `<option value="${itemt.id}">${itemt.name}</option>`;
                        $("#specifications").val(itemt.specifications);
                        $("#model").val(itemt.model);
                        $("#measurement").val(itemt.unit);
                    }
                })
                $(".materialname").html(optionStr2);
            }else{
                optionStr2 += `<option value="0">请选择</option>`;
                list.forEach(itemls2 => {
                    optionStr2 += `<option value ="${itemls2.id}">${itemls2.name}</option>`;
                })
                $(".materialname").html(optionStr2);
            }

            let pronum = $(".material").val();
            pronum = Number(pronum);
            if(pronum == 0){
            }else{
                box.push(pronum);
                let leaden = box.length;
                if(pronum !== 0){
                    if(leaden >= 2){
                        bounce_Fixed.show($("#dettelpont2"));
                        str = `
                        <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontsure(1)">确定</span>
                    `;
                        $("#dettelpont2").children(".bonceFoot").html(str);
                    }
                }
            }
            $(".materialname").data("box2",[]);
            break;
        case 2://需要同选择前的样子进行比较，确认是否需要展示弹窗
            let box2 = $(".materialname").data("box2");
            let proname = $(".materialname").val();
            proname = Number(proname);
            if(proname == 0){
            }else{
                box2.push(proname);
                let leaden2 = box2.length;
                if(proname !== 0){
                    if(leaden2 >= 2){
                        bounce_Fixed.show($("#dettelpont2"));
                        str = `
                        <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontsure(2)">确定</span>`;
                        $("#dettelpont2").children(".bonceFoot").html(str);
                    }
                }
            }
            break;
    }
}
// creator: sy 2024-01-17   判断库位选择框选择的数据是否重复
function getstoration(objen){
    let chosen = objen.val();
    let ablen = false;
    let ston2 = objen.parent().parent().parent(".flex-box");
    ston2.siblings().each(function(){
        let item1 = $(this).find(".stoation").val();
        if(item1 == chosen){ ablen = true;}
    })
    switch (ablen) {
        case true:
            layer.msg("已选择了该库位，请重新选择！");
            objen.val(0);
            break;
    }
}
// creator: sy 2024-01-16   获取供应商数据
function getSuppleer(proid){
    // let str = ``;
    // //let  suppliduct = proid.val();
    // let suppliduct = proid;
    // let box1 = $(".suppliduct").data("box1");
    // if(box1 == undefined){
    //     box1 = $(".suppliduct").data("box2") || [];
    // }
    // suppliduct = Number(suppliduct) || null;
    // if(suppliduct == null){
    //     suppliduct = proid.val();
    //     suppliduct = Number(suppliduct);
    // }
    // if(suppliduct == 0){}else{
    //     box1.push(suppliduct);
    //     let lentd = box1.length;
    //     if(suppliduct !== 0){
    //         if(lentd >= 2){
    //             bounce_Fixed.show($("#dettelpont3"));
    //         }
    //     }
    // }
    // $(".suppliduct").data("box1",[]);
    let oid = $(".suppliduct").data("proid");
    proid = Number(proid);
    if(proid == 0){
        proid = oid;
    }else{
        let reg = /^[1-9][0-9]+$/gi;
        if(reg.test(proid)){
        }else{
            proid = oid;
        }
    }
    // let num2 = 1;
    // num2 = num2.toString();
    $.ajax({
        url:"../mt/suppliers",
        data:{
            id:proid,
            enabled:1
        },
        success:function(res){
            let ductlist = res.data || [];
            let optionStr = ``;
            if(ductlist.length == 0){
                optionStr +=`<option value="9" class="clolder">本次入库不记录供应商</option>`;
            }else{
                optionStr += `<option value="0" class="clolder">请选择</option>`;
                ductlist.forEach(itemlist => {
                    optionStr += `<option value="${itemlist.supplier_id}">${itemlist.name}</option>`;
                });
            }
            $(".suppliduct").html(optionStr);
            $(".addmore").data("linksup",ductlist);
        }
    })
}
// creator: sy 2024-01-23   改变供应商时给予提示
function getSuppmore(proid2){
    let str = ``;
    let suppliduct = proid2.val();
    let box1 = $(".suppliduct").data("box1");
    if(box1 == undefined){
        box1 = $(".suppliduct").data("box2") || [];
    }
    suppliduct = Number(suppliduct) || null;
    if(suppliduct == null){
        suppliduct = proid2.val();
        suppliduct = Number(suppliduct);
    }
    if(suppliduct == 0){}else{
        box1.push(suppliduct);
        let lentd = box1.length;
        if(suppliduct !== 0){
            if(lentd >= 2){
                bounce_Fixed.show($("#dettelpont3"));
            }
        }
    }
    $(".suppliduct").data("box1",[]);
    $(".suppliduct").data("box2",[]);
}

// creator: sy 2024-01-16   处理表格中没有数据的情况
function handleNull(str){
    let result = str == null || str == undefined || str == 'null' || str == '' || str == ""?'——':str;
    return result;
}

// creator: sy 2024-01-23   选择‘仅录入入库数量，暂不选择库位’/‘从库位上选择材料 ’的确定按钮
function madehsre(diffnum){
    switch (diffnum) {
        case 1://从库位上选择材料
            let suppliduct = $(".suppliduct").val();
            let stoation = $(".stoation").val();
            let protest2 = $(".protest2").val();
            if(suppliduct == 0){
                layer.msg("操作失败！\n" + "还有必填项尚未填写！");
                return false;
            }else if(stoation == ""){
                layer.msg("操作失败！\n" + "还有必填项尚未填写！");
                return false;
            }else if(protest2 == ""){
                layer.msg("操作失败！\n" + "还有必填项尚未填写！");
                return false;
            }
            $.ajax({
                url:"../picking/manualPicking.do",
                data:{

                },
                success:function(res){

                }
            })
            break;
        case 2://仅录入入库数量，暂不选择库位
            let suppliduct2 = $(".suppliduct").val();
            let protest1 = $(".protest1").val();
            if(suppliduct2 == 0){
                layer.msg("操作失败！\n" + "还有必填项尚未填写！");
                return false;
            }else if(protest2 == ""){
                layer.msg("操作失败！\n" + "还有必填项尚未填写！");
                return false;
            }
            break;
    }
}
// creator: sy 2024-01-24   手动领料数据提交
function madesure2(){
    let usid = $("#takpicker ").val();
    let username = $("#takpicker ").find("option:selected").text();
    let getprodate = $("#taklistdate").val();

}

// creatror: sy 2024-01-22  点击‘下一步’按钮进行跳转
function pontnext(){
    let material = $(".material ").val();
    let materialname = $(".materialname").val();
    let suppliduct  = $(".suppliduct").val();
    let protest1 = $(".protest1").val();
    let stoation = $(".stoation").val();
    let stoaall = [];
    $(".ston2").each(function(){
        let stonId = $(this).find(".stoation").val();
        let stonname = $(this).find(".stoation").find("option:selected").text();
        if(stonId && stonId !== ""){
            let index = stoaall.findIndex(value => Number(value.materialLocation) === Number(stonId));
            if(index === -1) stoaall.push({"materialLocation":stonId,"name":stonname});
        }
    })
    let proall2 = [];
    let txt = $(".ston2").find(".protest2");
    for(let i = 0; i < txt.length; i++){
        let txet = txt.eq(i).val();
        txet = Number(txet);
        proall2.push(txet);
    }
    var protest2 = $(".protest2").val();
    if(material == 0){}else if(materialname == 0){}else if(suppliduct == 0){}else if(protest1 == "" || protest2 == "")
    {}else if(stoation == ""){}else{

    }
}
// creator: sy 2024-01-17   点击确定按钮清空筛选框中的数据
function pontsure(numbn){
    switch (numbn) {
        case 1:
            //$(".material").val("");
            $(".materialname").val(0);
            $("#specifications").val("");
            $("#model").val("");
            $("#measurement").val("");
            let optionen1 = $(".suppliduct").val();
            if(optionen1 == "9" || optionen1 == 9){
            }else{
                optionen1 = 0;
            }
            $(".suppliduct").val(optionen1);
            //$(".suppliduct").val("");
            $(".protest1").val("");
            $(".protest2").val("");
            $(".stoation").val(0);
            bounce_Fixed.cancel($("#dettelpont2"));
            break;
        case 2:
            $(".materialname").val(0);
            let optionen2 = $(".suppliduct").val();
            if(optionen2 == "9" || optionen2 == 9){
            }else{
                optionen1 = 0;
            }
            $(".suppliduct").val(optionen2);
            $(".protest2").val("");
            $(".protest1").val("");
            $(".stoation").val(0);
            bounce_Fixed.cancel($("#dettelpont2"));
            break;
        case 3:
            $(".protest1").val("");
            $(".stoation").val(0);
            $(".protest2").val("");
            bounce_Fixed.cancel($("#dettelpont3"));
            break;
    }
}

// creator: sy 2023-12-18   调用接口获取领料人数据
function takwhopeop(jsonlyp){
    $.ajax({
        url:"../picking/getPickingUserList.do",
        data:{
            code:"pickingPerson"
        },
        success:function(res){
            var list2 = res.data || [];//list2用于存储调用接口后获取的入库申请人数据
            var optionStr = ``;
            optionStr += `<option value="" style="color:grey;">请选择</option>`;
            list2.forEach(itemls2 => {
                optionStr += `<option value="${itemls2.userID}">${itemls2.userName}</option>`;
            });
            $(".takpicker").html(optionStr);
        }
    })
}

// cerator: sy 2023-12-18   复选框选中
function setRadioSelectkwx(str,arr,selectVal,thisObj){
    var isEdit = $("#isEdit").val();
    var idStr = "#";
    if(isEdit === "1"){
        idStr = "#edit_";
    }
    idStr += str ;
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $(idStr).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $(idStr + arr[i]).attr("class" , "fa fa-circle-o");
    }
    $(idStr + selectVal).attr("class" , "fa fa-dot-circle-o");
}

laydate.render({elem: '#taklistdate', format: 'yyyy-MM-dd'});