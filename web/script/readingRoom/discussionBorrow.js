jQuery.fn.treeItemSlide = function (type) {
    return $(this).each(function(){
        var iconNode = $(this).children('i').eq(0)
        if (!iconNode.hasClass("ty-fa")) {
            if (type === 'up') {
                return iconNode.removeClass('fa-angle-down').addClass('fa-angle-right')
            } else if (type === 'down') {
                return iconNode.removeClass('fa-angle-right').addClass('fa-angle-down')
            } else if (type === 'toggle') {
                if (iconNode.hasClass("fa-angle-right")) {
                    return iconNode.eq(0).removeClass('fa-angle-right').addClass('fa-angle-down')
                } else {
                    return iconNode.eq(0).removeClass('fa-angle-down').addClass('fa-angle-right')
                }
            }
        }
    });
}
$(function () {
    initPage()
    $(".bounce").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'sureBorrowApply':
                var postId = $("#borrowApply").data("postId")
                var type = $("#borrowApply input:radio[name='isNeedOther']:checked").val()
                var data = {
                    userID: sphdSocket.user.userID, // 登录人id
                    postId: postId,                 // 借阅讨论的id
                    type: type,                      // 审批类型 1是中间审批 2是直接终审
                }
                if (Number(type) === 1) {
                    var auditor = $("#borrowApply .chooseApprover").val()
                    var auditorName = $("#borrowApply .chooseApprover option:selected").html()
                    data.auditor = auditor          // 审批人id 只有type为1的时候才传
                    data.auditorName = auditorName  // 审批人id 审批人名字 只有type为1的时候才传
                }
                $.ajax({
                    url: '../read/insertBorrowingForum.do',
                    data: data,
                    success: function (res) {
                        var data = res.data
                        if (data === '成功') {
                            layer.msg("操作成功！")
                            bounce.cancel()
                        } else {
                            $("#mt_tip_ms").html("操作失败！");
                            bounce.show($("#mtTip"));
                        }
                    }
                })
                break;
        }
    })
    $(".themeList").on("click keydown", "[type='btn']", function (event) {
        var name = $(this).data("name")
        switch (name) {
            case 'searchTheme':
                var searchInput = $(".ty-search").val()
                getThemeList(1, 20, searchInput)
                break;
        }
    })
    // × 点击事件
    $(".clearInput").on({
        "mousedown": function () {
            $(this).prev().val("");
            getThemeList(1, 20)
        },
        "mouseup": function () {
            $(this).prev().focus();

        }
    });

    $(".themeList").on("click", "[type='btn']", function () {
        var name=$(this).attr("name")
        switch (name) {
            case 'borrowApply':
                bounce.show($("#borrowApply"))
                var postId = $(this).parents(".themeItem").data("id");
                $("#borrowApply input:radio").eq(0).prop("checked", true);
                $("#borrowApply").data("postId", postId);
                getApprover()
                setEveryTime(bounce, 'borrowApply')
        }
    })


    $("input:radio[name='isNeedOther']").on('change', function () {
        if ($(this).val() === '0') {
            $(this).parents(".bonceContainer").find(".chooseApprover").val("")
        }
    })

    $(".chooseApprover").on("change", function () {
        if ($(this).val() !== '') {
            $(this).parents(".bonceContainer").find("input:radio[name='isNeedOther']").eq(0).prop("checked", true)
        }
    })
});

// creator: 张旭博, 2020-04-28 15:25:30, 初始化页面
function initPage() {
    getThemeList(1, 20) ;
}

// creator: 张旭博，2019-04-03 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 新增客户
            case 'borrowApply':
                var state = 0

                var isNeedOther = $("#borrowApply input:radio:checked").val()
                if (isNeedOther === '1' && $("#borrowApply .chooseApprover").val() === ''){
                    state++
                }

                if ( state > 0) {
                    $("#sureBorrowApplyBtn").prop("disabled",true)
                }else {
                    $("#sureBorrowApplyBtn").prop("disabled",false)
                }
                break;
        }

    });
}

// creator: 侯杏哲,2017-12-04 渲染获取的文件夹数据和页面 ( 适用于主页面文件夹树和移动文件弹框的文件夹树 )
function showTreeData(data , treeItemThis) {
    // data - 获取的数据 ， treeItemThis - 点击的那个文件夹对象 ， treeObj - 点击的树对象

    // 处理子文件夹数据
    var listNotice      = data["parentFolder"] || [];
    var listNoticeChild = data["childFolder"];
    var level = parseInt(treeItemThis.parent().parent().attr("level")) ;
    var nextLevel = level + 1 ;
    var levelStr = "";
    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">' ;
    for(var i in listNoticeChild){
        if(listNoticeChild[i]["childStatus"] === '1'){ // 1时代表此文件夹下有子文件夹
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"]+'</span>' +
                '</div></li>'
        }else{
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"] + '</span>' +
                '</div></li>'
        }
    }
    levelStr += '</div>';
    localStorage.setItem("noticemax",listNotice["childStatus"] ); // childStatus : 1 是有文件夹; 2 是有文件; null标识什么都没有

    treeItemThis.attr("child", listNoticeChild.length > 0)
    if (listNoticeChild.length > 0) {
        if (treeItemThis.next().length === 0) {
            treeItemThis.after(levelStr);
        }
    }

}

//------------------------- 文件查询 ---------------------------//

// creator: 张旭博，2018-04-20 19:47:19，普通搜索 - 搜索
function searchBtn() {
    $("#searchSort").show()
    $("#search_applier").html("")
    $("#searchSort li").eq(1).find(".sort").hide()
    $("#searchSort li").eq(1).find("i").removeClass("fa-long-arrow-up")
    getGeneralFile(1, 20);
}

// creator: 张旭博，2018-04-20 19:47:39，高级搜索 - 按钮
function advanceSearchBtn() {
    bounce.show($("#advancedSearch"))
    $("#advancedSearch").find("input,textarea,select").val("");
}

// creator: 张旭博，2018-04-20 14:49:37，根据名字和编号搜索文件(普通检索)
var sphdSocketArr = [];
function getGeneralFile(cur, pageSize){
    var name = $("#fileNameOrSn").val();
    var creator = $("#search_applier").val()
    var condition = {
        currentPageNo: cur,
        pageSize: pageSize,
        name: name,
        fileSn: name,
        type: 1,
        creator: creator
    }
    if ($("#searchSort li").eq(1).find(".sort").is(":visible")) {
        if($("#searchSort li").eq(1).find("i").hasClass('fa-long-arrow-up')){
            condition.type = 2
        }else{
            condition.type = 3
        }
    } else {
        condition.type = 2
    }
    $.ajax({
        url:"../res/generalFindFile.do",
        data: condition,
        success: function(data){
            data = data["data"];
            var fileInfo = data.listFile,
                pageInfo = data.pageInfo;
            var categoryInfo = data.listCategory;
            var creatorInfo = data.listCreator;
            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"]

            setPage($("#ye-search-file"), currentPageNo, totalPage, "generalFile") ;
            $("#btn-group").hide();
            $(".ty-searchContent").show().siblings().hide();
            $(".ty-searchContent .fileContent").show().siblings().hide();

            var fileListStr = '';
            if(fileInfo.length === 0){
                fileListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件</div>';
            }else{
                for(var t in categoryInfo){
                    sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                        console.log('普通搜索： ' + data)
                        var result = JSON.parse(data);
                        var fileResult = [];
                        fileResult.push(result.res);
                        var resultStr = getFileListStr(fileResult);
                        $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                        $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                    }, null, 'custom', categoryInfo[t]));
                }
                fileListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 '+pageInfo.totalResult+' 个文件符合查询结果</div>'
                    +   getFileListStr(fileInfo);
            }

            var applierListStr = '<option value="">--请选择--</option>'
            if ($("#search_applier").html() === '') {
                for (var i in creatorInfo) {
                    applierListStr += '<option value="'+creatorInfo[i].userID+'">'+creatorInfo[i].userName+'</option>'
                }
                $("#search_applier").html(applierListStr)
            }

            $(".ty-searchContent .fileContent .searchFile").html(fileListStr);
        }
    });
}

// creator: 张旭博，2018-04-20 19:53:29，高级检索 -- 文件检索
function advancedSearch_file(currentPageNo,pageSize) {
    var data = {
        "currentPageNo"     : currentPageNo,
        "pageSize"          : pageSize
    };
    $("#advancedSearch").find("input,select").each(function () {
        if($(this).val() !== ""){
            var key = $(this).attr("class");
            data[key] = $(this).val();
        }
    });
    $.ajax({
        url : '../res/advancedFindFile.do',
        data : data,
        success: function(data) {
            bounce.cancel();
            data = data["data"];
            var fileInfo = data.listFile,
                pageInfo = data.pageInfo;
            var catorageInfo = data.listCategory;
            // 设置分页信息
            var currentPageNo = pageInfo["currentPageNo"],
                totalPage = pageInfo["totalPage"];

            setPage($("#ye-search-file"), currentPageNo, totalPage, "advancedFindFile");
            $("#btn-group").hide();
            $(".ty-searchContent").show().siblings().hide();
            $(".ty-searchContent .fileContent").show().siblings().hide();

            // 展示查询结果（提示+文件列表）
            var fileListStr = '';
            if (fileInfo.length === 0) {
                fileListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件</div>';
            } else {
                fileListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 ' + pageInfo.totalResult + ' 个文件符合查询结果</div>'
                    + getFileListStr(fileInfo);
                for(var t in catorageInfo){
                    sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                        var result = JSON.parse(data);
                        var fileResult = [];
                        fileResult.push(result.res);
                        var resultStr = getFileListStr(fileResult);
                        $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                        $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                    }, null, 'custom', catorageInfo[t]));
                }
            }
            $(".ty-searchContent .fileContent .searchFile").html(fileListStr);
        }
    });
}

// creator: 张旭博，2018-04-20 19:53:29，高级检索 -- 文件类别检索
function advancedSearch_folder(currentPageNo,pageSize) {
    var folderName = $("#advancedSearch .folderName").val();
    $.ajax({
        url:"../res/findFolder.do",
        type:"post",
        data:{
            "currentPageNo"     : currentPageNo,
            "pageSize"          : pageSize,
            "name"              : folderName
        },
        success: function(data){
            bounce.cancel();
            data = data["data"];
            var folderInfo = data.listCategory,
                pageInfo = data.pageInfo;

            // 设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"];

            setPage( $("#ye-search-folder"), currentPageNo, totalPage, "findFolder") ;

            // 展示查询结果（提示+文件列表）
            var folderListStr = '';
            if(folderInfo.length === 0){
                folderListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件类别</div>';
                $("#ye-search-folder").hide();
            }else{
                folderListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 '+pageInfo.totalResult+' 个文件类别符合查询结果</div>'
                    + getFolderListStr(folderInfo);
                $("#ye-search-folder").show();
            }

            $("#btn-group").hide();
            $(".ty-searchContent").show().siblings().hide();
            $(".ty-searchContent .folderContent").show().siblings().hide();
            $(".ty-searchContent .searchFolderContent").html(folderListStr).attr("name",name);
        }
    });
}

// creator: 张旭博，2018-04-23 11:32:46，高级搜索 -- 按文件类别查询 -- 获取子级文件类别
function getChildFolder(folderId,parentFolder){
    var url = '';
    if(isGeneral || isSuper){
        url = '../res/getFolderAndChildFolderManager.do'
    }else{
        url = '../res/getFolderAndChildFolder.do'
    }
    $.ajax({
        url:url,
        type:"post",
        data:{
            "type"      : 1,
            "categoryId": folderId
        },
        success: function(data){
            data = data["data"];
            var childFolder  = data.childFolder;

            // 展示查询结果（提示+文件列表）
            if(childFolder.length === 0){
                getFile(1,20,folderId);
                $(".ty-searchContent .fileContent").show().siblings().hide();
                sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                    var result = JSON.parse(data);
                    var fileResult = [];
                    fileResult.push(result.res);
                    var resultStr = getFileListStr(fileResult);
                    $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                    $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                }, null, 'custom', folderId));
            }else{
                var folderListStr = getFolderListStr(childFolder,parentFolder);
                $(".ty-searchContent .folderContent").show().siblings().hide();
                $(".ty-searchContent .childFolder").children(":last").hide();
                $(".ty-searchContent .childFolder").append('<div class="childFolderContent">'+folderListStr+'</div>').attr("id",folderId).show().siblings().hide();
            }
        }
    });
}

// creator: 张旭博，2018-05-24 11:24:08，查询返回
function goBack(type) {
    switch (type) {
        case 'query':
            var fileContent = $(".ty-searchContent .fileContent"),
                folderContent = $(".ty-searchContent .folderContent");

            if(fileContent.find('.searchFile').html() !== ""){
                fileContent.find('.searchFile').html('');
                if(folderContent.find('.searchFolderContent').html() === ""){
                    $(".ty-fileContent").show().siblings().hide();
                    $("#fileNameOrSn").val("");
                    //清除搜索用到的订阅
                    for(var a=0; a< sphdSocketArr.length; a++){
                        sphdSocket.unsubscribe(sphdSocketArr[a]);
                    }
                }else{
                    fileContent.hide().siblings().show();
                }
            }else{
                if(folderContent.find('.childFolder').children().length > 1){
                    fileContent.find('.searchFile').html('');
                    folderContent.find('.childFolder').children(":last").remove();
                    folderContent.find('.childFolder').children(":last").show();
                }else if(folderContent.find('.childFolder').children().length === 1){
                    folderContent.find('.childFolder').html("");
                    folderContent.find('.searchFolder').show();

                }else{
                    folderContent.find('.searchFolderContent').html("");
                    $(".ty-fileContent").show().siblings().hide();
                    $("#advancedSearch").find("input,textarea,select").val("");
                    $("#fileNameOrSn").val("");
                    //清除搜索用到的订阅
                    for(var a=0; a< sphdSocketArr.length; a++){
                        sphdSocket.unsubscribe(sphdSocketArr[a]);
                    }
                }
            }
            break;
        case 'record':
            $("#main").show().siblings().hide();
            break;
    }
    if($(".ty-fileContent").is(":visible")) {
        $("#btn-group").show()
    } else {
        $("#btn-group").hide()
    }

}



//------------------------- 文件操作 ---------------------------//

// creator: 张旭博，2018-05-16 16:29:28，查看各种操作记录
function seeHandelRecordBtn(type){
    //打开弹窗
    bounce_Fixed.show($("#handleRecord"));

    //更改弹窗标题
    var recordTitleName = '';
    switch (type){
        case 1:
            recordTitleName = '浏览记录';
            break;
        case 2:
            recordTitleName = '下载记录';
            break;
        case 5:
            recordTitleName = '移动记录';
            break;
        case 6:
            recordTitleName = '文件名称修改记录';
            break;
        case 7:
            recordTitleName = '文件编号修改记录';
            break;
    }
    $("#handleRecord").find(".recordTitleName").html(recordTitleName);

    //渲染操作记录表格
    setHandelRecord(type);
}

// updater: 王静 2017-08-02 10:40:34 换版记录 - 按钮
function chargeRecord(obj){
    if(obj.hasClass("ty-disabled")){
        return false;
    }
    $("#resHistory").show().siblings().hide();
    $("#btn-group").hide()
    $("#resHistory").data("fileId", obj.parents(".ty-fileItem").attr("id"));
    getRecord(1, 20)
}

// creator: 张旭博，2019-06-19 14:02:04，基本信息 - 按钮
function basicMessage(obj, history){
    var pid=obj.parents(".ty-fileItem").attr("id");
    var loginId = sphdSocket.user.userID;
    $("#docInfoScan").data("currentFileId",pid);

    var url = '../res/getFileMessage.do'

    if (history === 1) {
        url = "../res/getUpFileMes.do"
    }
    $.ajax({
        url: url,
        data: {"id" :pid},
        success: function (res) {
            var state = res["success"] ;
            if(state !== 1){
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var list    = res["data"].resource, // 文件信息
                    arr     = res["data"].listUser, // 总务的列表
                    approvalProcess  = res["data"].listAp, // 审批流程
                    size        = list.size,
                    reason      = list.reason,
                    view_num    = list.viewNum,         // 浏览次数
                    download_num= list.downloadNum,     // 下载次数
                    move_num    = list.moveNum,         // 移动次数
                    change_num  = list.changeNum,       // 换版次数
                    upName      = res["data"].upName,   // 文件名称修改次数
                    upFileSn    = res["data"].upFileSn, // 文件编号修改次数
                    categoryName= res["data"].categoryName;

                typeof (reason) === "undefined" ? reason = "--":reason;
                size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size = parseFloat(size/1048576).toFixed(2) + 'MB';

                $("#info_title").html(list["name"]);

                // left
                $("#info_sn").html(list["fileSn"]);
                $("#info_category").html(categoryName);
                $("#info_size").html(size);

                // right
                $("#info_gn").html("G" + change_num);
                $("#info_createName").html(list["createName"]);
                $("#info_createDate").html(list["createDate"]);
                $("#info_version").html( ( list["version"] || "未知") );


                $("#info_content").html(list["content"]);

                var approveStr = '<div class="trItem"><span class="ttl">申请人：</span><span class="con" style="max-width: 280px"><span class="info_name">' + approvalProcess[0].userName + '</span><span>' + formatTime(approvalProcess[0].createDate, true) +'</span></div>'
                // 审批流程
                for (var i = 0; i < approvalProcess.length; i++) {
                    var name = ''
                    if ( i === approvalProcess.length - 1) {
                        name = '文 管'
                    } else {
                        name = '审批人'
                    }
                    approveStr += '<div class="trItem"><span class="ttl">' + name + '：</span><span class="con" style="max-width: 280px"><span class="info_name">' + approvalProcess[i].toUserName + '</span><span>' + formatTime(approvalProcess[i].handleTime, true) +'</span></div>'
                }
                $("#docInfoScan .processList").html(approveStr)
                if( change_num > 0){ // 换过版
                    $("#docInfoScan .info_content .ttl").html("换版原因：")
                }else{
                    $("#docInfoScan .info_content .ttl").html("说明：")
                }

                var userLimit = chargeRole('超管') || chargeRole('小超管') || chargeRole('总务') || chargeZW(loginId , arr)

                view_num !== 0 && userLimit      ? $("#viewNumBtn").prop("disabled",false)    :$("#viewNumBtn").prop("disabled",true);
                download_num !== 0 && userLimit  ? $("#downloadNumBtn").prop("disabled",false):$("#downloadNumBtn").prop("disabled",true);

                move_num === 0      ? $("#moveNumBtn").prop("disabled",true)        :$("#moveNumBtn").prop("disabled",false);
                upName === 0        ? $("#changeNameNumBtn").prop("disabled",true)  :$("#changeNameNumBtn").prop("disabled",false);
                upFileSn === 0      ? $("#changeNoNumBtn").prop("disabled",true)    :$("#changeNoNumBtn").prop("disabled",false);
                $("#docInfoScan .censusInfo .view_num").html(view_num);
                $("#docInfoScan .censusInfo .download_num").html(download_num);
                $("#docInfoScan .censusInfo .move_num").html(move_num);
                $("#docInfoScan .censusInfo .name_num").html(upName);
                $("#docInfoScan .censusInfo .no_num").html(upFileSn);

                if (history === 1) {
                    $("#docInfoScan .censusInfo").hide()
                } else {
                    $("#docInfoScan .censusInfo").show()
                }
                bounce.show($("#docInfoScan")) ;

            }
        }
    })
}

// creator: 张旭博，2020-10-22 10:30:17，文件借阅申请 - 按钮
function borrowApply(obj, idType) {
    bounce.show($("#borrowApply"))
    var fileId = obj.parents(".ty-fileItem").attr("id");
    $("#borrowApply input:radio").eq(0).prop("checked", true);
    $("#borrowApply").data("fileId", fileId);
    $("#borrowApply").data("idType", idType);
    getApprover()
    setEveryTime(bounce, 'borrowApply')
}

//------------------------- 获取数据 ---------------------------//

// creator: 张旭博, 2020-05-05 10:31:35, 获取审批人
function getApprover() {
    $.ajax({
        url: "../res/getAllOidUserByResource.do" ,
        data: { userID: sphdSocket.user.userID },
        success: function (data) {
            var approverList = data["data"]["list"];
            var optStr = '<option value="">选择审批人</option>';
            if (approverList && approverList.length >0){
                for(var i in approverList) {
                    optStr += '<option value="'+ approverList[i].userID +'">'+ approverList[i].userName +'</option>';
                }
            }
            $(".chooseApprover").html(optStr);
        }
    })
}

// creator: 张旭博，2018-05-16 16:46:20，获取各种操作记录
function setHandelRecord(type) {
    var currentFileId = $("#docInfoScan").data("currentFileId"),
        tableStr = '';
    switch (type){
        case 1:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>浏览人</td>'+
                '<td>浏览时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 2:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>下载人</td>'+
                '<td>下载时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 5:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>移动前</td>'+
                '<td>移动后</td>'+
                '<td>移动人</td>'+
                '<td>移动时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 6:
        case 7:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>修改前</td>'+
                '<td>修改后</td>'+
                '<td>修改人</td>'+
                '<td>修改时间</td>'+
                '</tr>'+
                '</thead>';
            break;
    }
    if(type === 1||type === 2){
        $.ajax({
            url:"../res/getRecordByShow.do",
            data:{
                "id"  : currentFileId,
                "type": type
            },
            success: function(data){
                if(data["success"] === 1){
                    data = data["data"];
                    tableStr += '<tbody>';
                    for(var i = 0; i < data.length; i++){
                        tableStr += '<tr>' +
                            '<td style="width: 10%">' + (i + 1) +'</td>'+
                            '<td style="width: 45%">' + data[i].createName + '</td>'+
                            '<td style="width: 45%">' + data[i].createDate.substring(0,19) + '</td>'+
                            '</tr>'
                    }
                    tableStr += '</tbody>';
                    $("#handleRecord .recordTable").html(tableStr)
                }else{
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }else{
        $.ajax({
            url:"../res/getRecordByUpAndMove.do",
            data:{
                "fileId"  : currentFileId,
                "operation": type
            },
            success: function(data){
                if(data["success"] === 1){
                    data = data["data"];
                    tableStr += '<tbody>';
                    for(var i = 0; i < data.length; i++){
                        tableStr += '<tr>' +
                            '<td style="width: 7%">' + (i + 1) +'</td>'+
                            '<td style="width: 30%">' + data[i].nameBefore + '</td>'+
                            '<td style="width: 30%">' + data[i].nameAfter + '</td>'+
                            '<td style="width: 15%">' + data[i].createName + '</td>'+
                            '<td style="width: 18%">' + data[i].createDate.substring(0,19) + '</td>'+
                            '</tr>'
                    }
                    tableStr += '</tbody>';
                    $("#handleRecord .recordTable").html(tableStr)
                }else{
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }
}

//updator:王静 2017-08-03 15:38:23 获取换版后的历史文件列表
function getRecord(currentPageNo, totalPage){
    //得到地址栏传过来的ID
    var fileId = $("#resHistory").data("fileId");
    var data = {
        "fileId": fileId,
        "currentPageNo": currentPageNo,
        "pageSize": totalPage
    }
    $.ajax({
        url: "../res/getUpdateFile.do",
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"],
                jsonStr = JSON.stringify( { "fileId" : fileId} ) ;

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo);
            setPage( $("#ye_record"), currentPageNo, totalPage, "changeRecord", jsonStr) ;
            $("#resHistory .fileList").html(fileListStr)
        }
    })
}

//------------------------- 公共方法 -----------------------//

// updater: 张旭博，2018-04-20 10:30:37，设置文件列表
function getFile(currentPageNo , pageSize , categoryId){
    var type = $("#fileSort").data('type')
    if(!type){
        type = 1;
    }
    // type - "1"代表获取的是按时间降序的列表，“2”代表获取的是时间升序的列表，“3”代表获取的是文件编号升序的列表，“4”代表的是获取文件编号降序的列表
    var data = {
        "categoryId" : categoryId,
        "currentPageNo" : currentPageNo,
        "pageSize" : pageSize,
        "type" : type
    }
    $.ajax({
        url: "../read/getFileForRead.do" ,
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"],
                jsonStr = JSON.stringify( { "categoryId" : categoryId} ) ;

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo);
            if($(".ty-searchContent").is(':visible')){
                setPage( $("#ye-search-file"), currentPageNo, totalPage, "fileMessage", jsonStr) ;
                $(".ty-searchContent .fileContent").find('.searchFile').html(fileListStr).attr("type","folder");
            }else{
                setPage( $("#ye_con"), currentPageNo, totalPage, "fileMessage", jsonStr) ;
                $(".ty-fileList").html(fileListStr);
                $(".ty-fileList").attr("type","folder");
            }
        }
    })
}

// creator: 张旭博，2018-04-24 10:17:16，根据文件数据返回文件列表字符串
function getFileListStr(fileInfo) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        $("#fileSort").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        if ($(".ty-fileContent").is(":visible")) {
            $("#ye_con").show() ;
            $("#fileSort").show() ;
        }
        for(var i = 0; i < fileInfo.length; i++){
            var id          = fileInfo[i].id,
                file    = fileInfo[i].file,
                category    = fileInfo[i].category,
                name        = fileInfo[i].name,
                size        = fileInfo[i].size,
                path        = fileInfo[i].path,
                fileSn      = fileInfo[i].fileSn,
                changeNum   = fileInfo[i].changeNum,
                fileType    = fileInfo[i].version,
                operation   = fileInfo[i].operation,
                updateName  = fileInfo[i].updateName,
                createName  = fileInfo[i].createName,
                updateDate  = fileInfo[i].updateDate,
                createDate  = fileInfo[i].createDate,
                updator     = fileInfo[i].updator,
                creator     = fileInfo[i].creator,
                loginUser   = $("#loginUser").html(),
                loginUserId = 0,
                applyDisabledClass = '',    //定义换版申请禁用class
                recordDisabledClass = '',   //定义换版记录禁用class
                handleStr = '',             //定义操作按钮部分字符串
                recycleId = Number($("#listDoc").data("recycleId")); //回收站id



            //获取当前userId
            loginUser   = JSON.parse(loginUser);
            loginUserId = loginUser.userID;

            //格式化数据
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';

            updateName  === null || updateName  == undefined? updateName = createName:updateName;   //更新人为空时 赋值 创建人，最后统一取更新时间 ， 下同
            updateDate  === null || updateDate  == undefined? updateDate = createDate:updateDate;
            if (!updator) {updator = creator}

            operation   === '3' ? applyDisabledClass  = 'ty-disabled': applyDisabledClass  = ''; //换版申请后的禁用（换版申请后  换版和移动按钮被禁用）
            changeNum   === 0   ? recordDisabledClass = 'ty-disabled': recordDisabledClass = ''; //换版记录的禁用 （换版为0 换版记录按钮禁用）

            if (file) {
                handleStr = '<a class="ty-left" onclick="borrowApply($(this), 2)">借阅申请</a>' +
                    '<a class="ty-left" onclick="basicMessage($(this), 1)">基本信息</a>';
            } else {
                handleStr = '<a class="ty-left" onclick="borrowApply($(this), 1)">借阅申请</a>' +
                    '<a class="ty-left" onclick="basicMessage($(this))">基本信息</a>'+
                    '<a class="ty-left" onclick="chargeRecord($(this))">换版记录</a>'
            }

            //单条文件代码字符串拼接
            fileListStr +=  '<div class="ty-fileItem" id="'+id+'" pid="'+category+'" data-id="'+id+'">'+
                '   <div class="ty-left ty-fileType ty-file_'+fileType+'"></div>'+
                '   <div class="ty-fileInfo ty-left">'+
                '       <div class="ty-fileName" title="'+name+ '-G'+changeNum+'">'+ name +'</div>'+
                '       <div class="ty-fileVersion" changeNum="'+ changeNum +'">'+'G'+changeNum+'</div>'+
                '       <div class="ty-fileNo"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                '       <div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + size + ' &nbsp;&nbsp; ' + updateDate +'</div>'+
                '   </div>'+
                '   <div class="ty-fileHandle ty-right">'+handleStr+'</div>'+
                '</div>';
        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2018-04-24 10:17:55，根据文件夹数据返回文件类别字符串
function getFolderListStr(folderInfo,parentFolder) {
    var folderListStr = '';
    if(folderInfo.length !== 0){
        for(var i = 0; i < folderInfo.length; i++) {
            var id = folderInfo[i].id,
                name = folderInfo[i].name,
                allParentCategory = folderInfo[i].allParentCategory,
                firstLevelClass = '';

            if (parentFolder) {
                allParentCategory = parentFolder;
            } else {
                if (allParentCategory === null) {
                    allParentCategory = '';
                    firstLevelClass = 'firstLevel'
                } else {
                    firstLevelClass = ''
                }
            }

            folderListStr += '<div class="ty-folderItem" id="' + id + '">' +
                '<div class="ty-left ty-folderType"><i class="fa fa-folder"></i></div>' +
                '<div class="ty-folderInfo ty-left">' +
                '<div class="ty-folderName ' + firstLevelClass + '">' + name + '</div>' +
                '<div class="ty-folderParent">' + allParentCategory + '</div>' +
                '</div>' +
                '</div>';
        }
    }
    return folderListStr;
}

// creator: 张旭博，2021-04-23 09:19:29，获取讨论组列表
function getThemeList(cur, pageSize, title){
    var data = {
        oid: sphdSocket.user.oid,
        currentPageNo: cur,
        pageSize: pageSize,
    }
    if (title) {
        data.title = title
    } else {
        data.title = ''
    }
    $.ajax({
        url: "../read/getOpenForumPost.do",
        data: data,
        success: function (res) {
            var data = res.data
            var listForumPost = data.openList
            var pageInfo = data.pageInfo

            var str = ''
            if (listForumPost.length > 0) {
                //设置分页信息
                var currentPageNo   = pageInfo.currentPageNo,
                    totalPage       = pageInfo.totalPage,
                    jsonStr = JSON.stringify( { "title" : title} ) ;

                //设置文件信息
                setPage( $("#ye_borrow"), currentPageNo, totalPage, "discussionBorrow", jsonStr) ;
                for (var i in listForumPost) {
                    var forumPost = listForumPost[i]
                    str +=  '<li class="themeItem" data-id="'+forumPost.id+'" data-category="'+forumPost.category+'" data-compere="'+forumPost.compere+'">' +
                            '   <div class="item_title">' +
                            '       <div class="title">' +
                            '          <span class="participantsNum" title="'+forumPost.listForumPostUser+'" data-num="'+forumPost.participantsNum+'">'+forumPost.participantsNum+'人</span>' +
                            '          <span title="'+forumPost.title+'">'+forumPost.title+'</span>' +
                            '       </div>' +
                            '   </div>' +
                            '   <div class="item_des">' +
                            '       <div class="lastReply">归档日期 ' + formatTime(forumPost.enabledTime, false) + '</div><a class="ty-color-blue" name="borrowApply" type="btn">阅览申请</a>'+
                            '   </div>'+
                            '</li>'
                }
            } else {
                str = '<div class="contentNull">暂无数据</div>'
            }
            $(".discussionList").html(str)
        }
    });
}

// creator : 侯杏哲 2017-12-16 判断当前是否为总务
function chargeZW(testID , arr) {
    var isZW = false ;
    if(arr && arr.length > 0){
        for(var i = 0 ; i < arr.length ; i++){
            var id = arr[i]["userID"] ;
            if( id == testID){ isZW = true ;  }
        }
    }
    return isZW ;
}

