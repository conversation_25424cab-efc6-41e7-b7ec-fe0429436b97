var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
let editObj = null
$(function (){
    jumpPage("main", function () {
        // 主页为静态资源
        getAllCollect()
    })
})

// creator: 张旭博，2023-12-26 08:47:31， 首页-原辅材料的初始库存-编辑
function editInitialInventory(selector) {
    $.ajax($.webRoot + '/initialize/mustAllocationList.do').then(res => {
        let data = res.data
        let purchaseData = data[0]
        let RAMtWhData = data[1]
        if (purchaseData.state === 1) {
            // 判断仓库模式
            $.ajax('../skl/getStockMode')
                .then(res => {
                    let status = res.status
                    if (status === 1) {
                        layer.msg('当前是智能库模式，需要到手机端操作')
                        return false
                    } else {
                        let stepId = selector.parents("tr").data("id")
                        $("#home").data('stepId', stepId) // 存储步骤id
                        $.ajax($.webRoot + '/mt/init/warehouse/collect')
                            .then(res => {
                                let initWarehouse = res.initWarehouse || [] // 新增的仓库
                                let changedWarehouse = res.changedWarehouse || [] // 布局有变化的仓库
                                if (changedWarehouse.length > 0 || initWarehouse.length > 0) {
                                    jumpPage('initialInventory_see', () => {
                                        $.ajax($.webRoot + '/mt/init/warehouse/collect')
                                            .then(res => {
                                                let initData = res.initData || [] // 初始库存有待编辑的材料
                                                let initedData = res.initedData || [] // 初始库存已编辑的材料
                                                let initWarehouse = res.initWarehouse || [] // 新增的仓库
                                                let changedWarehouse = res.changedWarehouse || [] // 布局有变化的仓库

                                                let thisPage =  $(".page:visible")

                                                // 如果更改了仓库，显示统计页面
                                                thisPage.find("input:radio").prop("checked", false)

                                                // 页面部分数据赋值
                                                if (res.lastEditDate) {
                                                    thisPage.find(".lastEdit").html(`<b>${res.lastEditName}</b> 对材料初始库存的最后编辑时间为 <b>${moment(res.lastEditDate).format("YYYY-MM-DD HH:mm:ss")}</b>。`)
                                                } else {
                                                    thisPage.find(".lastEdit").html(`尚未编辑`)
                                                }
                                                thisPage.find(".lastEditName").html(res.lastEditName)
                                                thisPage.find(".lastEditDate").html(moment(res.lastEditDate).format("YYYY-MM-DD HH:mm:ss"))
                                                thisPage.find(".toEditMtNum").html(initData.length)
                                                thisPage.find(".editedMtNum").html(initedData.length)

                                                if (changedWarehouse.length > 0) {
                                                    let changeWhStr = ''
                                                    $("section.changedWarehouse").show()
                                                    for (let item of changedWarehouse) {
                                                        if (item.pre) {
                                                            let mtBase = item.mtBases?(item.mtBases.data?item.mtBases.data:[]) : []
                                                            changeWhStr += `<div class="ty-alert ty-alert-info">
                                                ${item.warehouseName}仓库（原有库位<b>${item.pre.locationNum}</b>个；
                                                ${item.updateName}于${moment(item.updateDate).format("YYYY-MM-DD HH:mm:ss")}修改了布局，
                                                改后有库位<b>${item.locationNum}</b>个；放置有原辅材料<b class="oldMhMtNum">${mtBase.length}</b>条）
                                                <div class="btn-group">
                                                    <span class="link-blue" onclick="handleChangedMhMt($(this))">去处理</span><span class="hd">${JSON.stringify(item)}</span>
                                                </div>
                                            </div>`
                                                        }
                                                    }
                                                    $(".changedWarehouse_box").html(changeWhStr)
                                                } else {
                                                    $("section.changedWarehouse").hide()
                                                }
                                                if (initWarehouse.length > 0) {
                                                    let addWhStr = ''
                                                    $("section.addedWarehouse").show()
                                                    for (let item of initWarehouse) {
                                                        addWhStr += `<div class="ty-alert ty-alert-padding">
                                            <div class="LP_32">
                                                ${item.warehouseName}仓库：有库位<b>${item.locationNum}</b>个；
                                                新增的操作者及时间：${item.createName} ${moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}
                                            </div>
                                        </div>`
                                                    }
                                                    $(".addedWarehouse_box").html(addWhStr)
                                                    thisPage.find(".addedWhNum").html(initWarehouse.length)
                                                } else {
                                                    $("section.addedWarehouse").hide()
                                                }
                                            })

                                    })
                                } else {
                                    jumpPage('initialInventory_edit', () => {
                                        let thisPage =  $(".page:visible")
                                        thisPage.find("input:radio").prop("checked", false)
                                        thisPage.find(".hidePart").show()
                                        getWaitingForLocationList(1, 20)
                                    })
                                }
                            })
                    }
                })

        } else {

            bounce.show($("#bounce_iKnow"))
            let content = ` <p>上步初始化尚未完成，故本模块初始化暂无法开始！</p>
                            <p>上步初始化：${purchaseData.name}</p>
                            <p>负责人：${purchaseData.userName}</p>`
            $("#bounce_iKnow .iKnowWord").html(content)
        }
    })

}

// creator: 张旭博，2023-12-26 03:14:03， 初始库存统计页面-待处理-编辑
function toEditMt() {
    jumpPage('initialInventory_edit', () => {
        let thisPage =  $(".page:visible")
        thisPage.find("input:radio").prop("checked", false)
        thisPage.find(".hidePart").hide()
        getWaitingForLocationList(1, 20)
    })
}

// creator: 张旭博，2023-12-25 11:43:31， 获取首页统计
function getAllCollect() {
    // 此接口因为是手机端获取的数据单独写的接口，判断库位是否编辑过
    $.ajax($.webRoot + '/inv/invLocationsCount')
        .then(res => {
            let count = res
            $(".locationCount").html(count?'编辑尚未完成':'已编辑')
            // 李旭做的步骤列表
            $.ajax($.webRoot + '/initialize/getMaterialInitList.do')
                .then(res => {
                    let data = res.data
                    let str = ''
                    for (let i in data) {
                        let item = data[i]
                        let stateArr = ['编辑尚未完成', '已编辑']
                        if (i === '0') {
                            str += `<tr>
                                <td>设置原辅材料库的库位 <span class="ty-color-blue">(目前只能在手机端操作！)</span></td>
                                <td>否</td>
                                <td><span class="locationCount">${count?'已编辑':'编辑尚未完成'}</span></td>
                                <td>--</td>
                            </tr>`
                        } else {
                            let handleStr = ''
                            if (item.state === 1) {
                                handleStr = `<div class="link-gray"">编辑</div>`
                            } else {
                                handleStr = `<div class="link-blue" onclick="editInitialInventory($(this))">编辑</div>`
                            }
                            str += `<tr data-id="${item.id}">
                                <td>${item.editName}</td>
                                <td>${item.required?'是':'否'}</td>
                                <td>${stateArr[item.state]}</td>
                                <td>${handleStr}</td>
                            </tr>`
                        }
                    }
                    $(".tbl_main tbody").html(str)
                })
        })

}

// creator: 张旭博，2023-12-26 11:37:48， 仓库布局被修改后仓库内的材料 - 去处理
function handleChangedMhMt(selector) {
    jumpPage('initialInventory_changeWh_edit', ()=> {
        let data = selector.siblings(".hd").html()
        data = JSON.parse(data)
        let mts = data.mtBases.data || []
        let str = `${data.warehouseName}仓库原有库位<b>${data.pre.locationNum}</b>个；
                ${data.updateName}于${moment(data.updateDate).format("YYYY-MM-DD HH:mm:ss")}修改了布局，
                改后有库位<b>${data.locationNum}</b>个；放置的如下<b>${mts.length}</b>条原辅材料，需重新选择库位。`
        $(".changeWh_editTip").html(str)

        let tbodyStr = ''
        for (let item of mts) {
            tbodyStr += `<tr>
                        <td>${item.name}</td>
                        <td>${item.code}</td>
                        <td>${item.model}</td>
                        <td>${item.specifications}</td>
                        <td>${item.unit}</td>
                        <td><span class="initial_stock">${(item.initial_stock || 0)}</span> <span class="link-blue" onclick="initStockEditBtn($(this))">修改</span><span class="hd">${JSON.stringify(item)}</span></td>
                        <td>${(item.re_amount || 0)} <span class="link-blue" onclick="reSelectStock($(this))">重新选择库位</span><span class="hd">${JSON.stringify(item)}</span></td>
                        <td>${item.ml_update_date?'是':'否'}</td>
                    </tr>`
        }
        $(".tbl_initialInventoryChangeWhEdit tbody").html(tbodyStr)
    })

}

// creator: 张旭博，2023-09-08 09:20:54， 初始化 - 完成
function initComplete() {
    let isChecked = $(".checkRadio:visible").find("input:radio").prop("checked")
    if (!isChecked) {
        layer.msg("请勾选后再操作！")
        return false
    }
    let stepId = $("#home").data('stepId') // 步骤id
    let page = $(".page:visible").attr("page")
    if (page === 'initialInventory_edit') {
        if ($(".tbl_initialInventoryPending tbody tr").length > 0) {
            layer.msg("还有材料尚未录入初始库存！")
            return false
        }
    }
    if (page === 'initialInventory_see') {
        if ($(".page:visible .toEditMtNum").html() !== '0') {
            layer.msg("还有材料尚未录入初始库存！")
            return false
        }
        let state = 0
        $(".page:visible .changedWarehouse_box .ty-alert").each(function (){
            let a = $(this).find(".oldMhMtNum").html()
            if ($.trim($(this).find(".oldMhMtNum").html()) !== '0') {
                state++
            }
        })
        if (state > 0) {
            layer.msg("还有布局被修改的仓库内的材料尚未重新重新选择库位！")
            return false
        }
    }

    $.ajax({
        url: $.webRoot + '/initialize/completeAllot.do',
        data: {
            id: stepId
        }
    }).then(res => {
        let data = res.data
        if (data === 1) {
            layer.msg('操作成功！')
            setTimeout(()=>{
                window.location.href = '../sys/logout.do'
            }, 2000)
        }
    })
}

// ----------------------------- 原辅材料库 -------------------------------
// creator: hxz，2020-02-03 17:00:06，去处理
function stockJump(item) {
    $(".container_item").children("div").hide();
    $(".container_item").children("div").eq(item).show();
    if (item == '0' || item == 0) {
        $(".stockJump").hide();
    } else{
        $(".stockJump").show();
    }
    jumpTo(item, 1, 20);
}
function backIndex() {
    stockJump(0)
}
function jumpTo(num, cur, pageTotal) {
    switch (num) {
        case 0:
            getIndexData();
            break;
        case 1:
            getWaitingForLocationList(cur, pageTotal);
            break;
        case 2:
            resetLocationList(cur, pageTotal);
            break;
        case 3:
            $(".curCat").html("");
            getPdByCategory('', 4, '', 1 , 20);
            break;
        case 4:
            getStorageAcceptList(cur, pageTotal,0);
            break;
        case 5:
            getOutStorageList(cur, pageTotal);
            break;
        case 6:
            $("#checkInOutInfo .ty-secondTab li").eq(0).click();
            break;
        case 7:
            var searchKey = $.trim($("#searchKey").val());
            keywordSearch(searchKey, cur, pageTotal);
            break;
    }
}

// creator: hxz，2020-02-18 11:07:32，获取待录入初始库存列表
function getWaitingForLocationList(currPage, pageSize){
    let thisPage = $(".page:visible")
    thisPage.find(".tbl_initialInventoryPending tbody").html('');
    thisPage.find(".pendingNum").html(0);
    let data = {
        oid:sphdSocket.user.oid ,
        state:3,
        pageNum:currPage,
        per:pageSize
    };
    $.ajax({
        url: $.webRoot + "/mt/list" ,
        data:data ,
        success:function(res){
            let totalPage = Number(data["totalCount"]) > 0 ? data["totalCount"] : 1 ;//总页数

            // 分页
            setPage( $("#ye_initialInventoryPending") , currPage ,  totalPage , "initialInventoryPending");
            thisPage.find(".pendingNum").html(res.totalSize);
            thisPage.find(".certainNum").html(res.inited);            let list = res.data || [];
            if (list.length > 0) {
                let html = '';
                for(let item of list){
                    html +=
                        '<tr data-info=\''+ JSON.stringify(item) +'\'>' +
                        '    <td>'+ item.name +'</td>' +
                        '    <td>'+ item.code +'</td>' +
                        '    <td>'+ item.model +'</td>' +
                        '    <td>'+ item.specifications +'</td>' +
                        '    <td>'+ item.unit +'</td>' +
                        '    <td>'+ item.create_name + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss") + '</td>' +
                        '    <td>' +
                        '        <span class="link-blue" onclick="enterInitialStock($(this))">编辑</span>' +
                        '    </td>' +
                        '</tr>'
                }
                thisPage.find(".tbl_initialInventoryPending tbody").html(html);
            }
        }
    });
}

function seeHandledRAMtWh() {
    jumpPage('initialInventory_handled', ()=> {
        getHandledLocationList(1, 20)
    })
}

// creator: 张旭博，2023-12-21 01:10:50， 获取初始库存已确定的原辅材料
function getHandledLocationList(cur, pageSize) {
    let thisPage = $(".page[page='initialInventory_handled']")
    let data = {
        oid:sphdSocket.user.oid ,
        state:5,
        pageNum:cur,
        per:pageSize
    };
    $.ajax({
        url: $.webRoot + "/mt/list" ,
        data:data ,
        success:function(res){
            let totalPage = Number(data["totalCount"]) > 0 ? data["totalCount"] : 1 ;//总页数

            // 分页
            setPage( $("#ye_initialInventoryHandled") , cur ,  totalPage , "initialInventoryHandled");
            let list = res.data || [];
            let html = '';
            for(let item of list){
                html +=
                    '<tr>' +
                    '    <td>'+ item.name +'</td>' +
                    '    <td>'+ item.code +'</td>' +
                    '    <td>'+ item.model +'</td>' +
                    '    <td>'+ item.specifications +'</td>' +
                    '    <td>'+ item.unit +'</td>' +
                    '    <td>'+ (item.initial_stock || 0) +'</td>' +
                    '    <td>'+ item.si_create_name + ' ' +moment(item.si_create_date).format("YYYY-MM-DD HH:mm:ss") + '</td>' +
                    '    <td>' +
                    '        <span class="link-blue" onclick="initStockEditBtn($(this))">修改</span>' +
                    '        <span class="hd" >'+ JSON.stringify(item) +'</span>' +
                    '    </td>' +
                    '</tr>'
            }
            thisPage.find(".tbl_initialInventoryHandled tbody").html(html);
            thisPage.find(".certainNum").html(res.totalSize);
        }
    });
}

// creator: hxz，2020-01-20 09:38:33，录入初始库存数量/库位
function enterInitialStock(obj) {
    //初始化
    $("#initialStockList tbody").html('');
    $(".checkCondition .fa").removeClass('fa-check-square-o').addClass("fa-square-o");
    $(".storesAble").find("li").eq(0).siblings().remove();
    $("#enterInitialStock select").val(0);
    $("#enterInitialStock input").val("");
    var info = obj.parents('tr').data('info');
    $('#enterInitialStock').data("id",info.id);
    $('#enterInitialStock .kuList').show().siblings().hide();
    $("#option").val("");
    var goodsHtml =
        '<tr>' +
        '    <td>'+ info.name  + '</td>' +
        '    <td>'+ info.code  + '</td>' +
        '    <td>'+ info.model  + '</td>' +
        '    <td>'+ info.specifications  + '</td>' +
        '    <td class="createInfo">'+ info.create_name + '&nbsp;&nbsp;' + new Date(info.create_date).format('yyyy/MM/dd hh:mm:ss') + '</td>' +
        '    <td class="unit">'+ info.unit  + '</td>' +
        '    <td><span class="floatLocation">0</span></td>' +
        '    <td><span class="holdLocation">0</span>个</td>' +
        '</tr>';
    $("#initialStockList tbody").html(goodsHtml);
    $(".goodsUnit").html(info.unit);
    $.ajax({
        "url":"../mt/locations" ,
        "data": { 'org': sphdSocket.org.id },
        success:function(res){
            mtLocationList = res.data||[] ;
            // var options = '<option value="0"></option>';
            // if (mtLocationList.length > 0){
            //     for(var q = 0 ; q < mtLocationList.length ; q++){
            //         options += "<option value='"+ mtLocationList[q]["id"] +"'>"+ mtLocationList[q]["location_code"] +"</option>";
            //     }
            // }
            licationSelectArr = [] ; // 重置已选择的库位id
            // 获取供应商列表
            getSuppliers(info , mtLocationList)
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
    bounce_Fixed.show($("#enterInitialStock"));
    bounce_Fixed.everyTime('0.5s','enterStock',function(){
        if($(".checkCondition:visible .fa").hasClass('fa-square-o')){
            if ($(".notSelectStores:visible").length > 0){
                $(".notSelectStores").hide();
                $(".selectSect:visible .storesAble").show();
            }
            var fillNum = 0, storeNum = 0;
            $(".storesAble:visible input").each(function(){
                if ($(this).val() != '') fillNum += Number($(this).val());
            });
            $(".storesAble:visible select").each(function(){
                var tt = $(this).val();
                if ($(this).val() != null && $(this).val() !=0 && $(this).val() !="") {
                    storeNum++;
                }
            });
            $(".holdLocation").html(storeNum);
            $(".floatLocation").html(fillNum);
        } else if($(".checkCondition .fa").hasClass('fa-check-square-o')){
            if ($(".notSelectStores:visible").length <= 0){
                $(".notSelectStores").show();
                $(".selectSect:visible .storesAble").hide();
            }
            $(".holdLocation:visible").html('0');
            $(".floatLocation:visible").html(Number($(".onlyNumber:visible").val()));
        }
    });
}

// creater: hxz 2020-04-21 获取某物料的供应商列表
function getSuppliers(info , locationArr) {
    var mtId = info.id
    var unit = info.unit
    var data = { 'id': mtId  }
    $.ajax({
        "url":"../mt/suppliers",
        "data":data,
        success:function (res) {
            var list = res['data'] || [];
            var str = "" , localStr = "<option value='0'>请选择库位</option>";
            if(list.length>0){
                $("#initialStockList").data('supNum', 1);
                $(".hasSup").show().siblings().hide();
                for(var q = 0 ; q < locationArr.length ; q++){
                    localStr += "<option value='"+ locationArr[q]["id"] +"'>"+ locationArr[q]["location_code"] +"</option>";
                }
                $("#supNum").html(list.length)
                for(var i = 0 ; i <list.length ; i++){
                    var item = list[i];
                    str += "<li data-mtid='"+ mtId +"' data-mtsupid='"+ item['material_supplier_id'] +"' class=\"supKuItem\">" +
                        "    <div>" +
                        "        <div> <p>供应商： "+ item["full_name"] +"</p> <p>简称： "+ item["name"] +"　 代号："+ item["code_name"]  +"</p>  </div>" +
                        "        <div> <span class=\"addOneStore\" onclick=\"newStore($(this))\">增加新库位</span> </div>" +
                        "    </div>" +
                        "    <div class=\"kuItem\">" +
                        "        <div>" +
                        "            <span class=\"gapRt\">库位</span><select onchange=\"fillStore($(this))\" class=\"addInitStore\">"+ localStr +"</select>" +
                        "            <span class=\"ty-color-blue hd\" onclick=\"seeStoresDetail($(this))\">查看该库位情况</span>" +
                        "        </div>" +
                        "        <div>" +
                        "            <span class=\"gapRt\">数量</span> <input type=\"text\" onkeyup=\"clearNoNum(this)\"/><span class=\"goodsUnit\">"+ unit +"</span>" +
                        "        </div>" +
                        "    </div>" +
                        "</li>";
                }
                str += "<li data-mtid='"+ mtId +"' data-mtsupid='-1' class=\"supKuItem\">" +
                    "    <div>" +
                    "        <div> <p>难以区分供应商的本材料</p></div>" +
                    "        <div> <span class=\"addOneStore\" onclick=\"newStore($(this))\">增加新库位</span> </div>" +
                    "    </div>" +
                    "    <div class=\"kuItem\">" +
                    "        <div>" +
                    "            <span class=\"gapRt\">库位</span><select onchange=\"fillStore($(this))\" class=\"addInitStore\">"+ localStr +"</select>" +
                    "            <span class=\"ty-color-blue hd\" onclick=\"seeStoresDetail($(this))\">查看该库位情况</span>" +
                    "        </div>" +
                    "        <div>" +
                    "            <span class=\"gapRt\">数量</span> <input type=\"text\" onkeyup=\"clearNoNum(this)\"/><span class=\"goodsUnit\">"+ unit +"</span>" +
                    "        </div>" +
                    "    </div>" +
                    "</li>";
            }else{
                $(".noSup").show().siblings().hide();
                $("#initialStockList").data('supNum', 0);
                $(".toggleCheck0").show().siblings().hide();
                $("#toggleCheckNum").val(0);
                $("#toggleCheck").attr("class","fa fa-square-o");
                $(".toggleCheck0 .kuItem").remove();
                newStore($("#addOneStore3"));
            }
            $(".kuList").html(str);
        }
    })
}
// creater: hxz 2020-04-21 重新设置库位 - 获取某物料的供应商列表
function getResetSuppliers(info , locationArr) {
    var mtId = info.id
    var unit = info.unit
    var data = { 'id': mtId  }
    $.ajax({
        url:"../mt/suppliers",
        data: data,
        success:function (res) {
            var list = res['data'] || [];
            var str = "" , localStr = "<option value='0'>请选择库位</option>";
            if(list.length>0){
                for(var q = 0 ; q < locationArr.length ; q++){
                    localStr += "<option value='"+ locationArr[q]["id"] +"'>"+ locationArr[q]["location_code"] +"</option>";
                }
                for(var i = 0 ; i <list.length ; i++){
                    var item = list[i];
                    str += "<li data-mtid='"+ mtId +"' data-mtsupid='"+ item['material_supplier_id'] +"' class=\"supKuItem\">" +
                        "    <div>" +
                        "        <div> <p>供应商： "+ item["full_name"] +"</p> <p>简称： "+ item["name"] +"　 代号："+ item["code_name"]  +"</p>  </div>" +
                        "        <div> <span class=\"addOneStore\" onclick=\"newStore($(this))\">增加新库位</span> </div>" +
                        "    </div>" +
                        "    <div class=\"kuItem\">" +
                        "        <div>" +
                        "            <span class=\"gapRt\">库位</span><select onchange=\"fillStore($(this))\" class=\"addInitStore\">"+ localStr +"</select>" +
                        "            <span class=\"ty-color-blue hd\" onclick=\"seeStoresDetail($(this))\">查看该库位情况</span>" +
                        "        </div>" +
                        "        <div>" +
                        "            <span class=\"gapRt\">数量</span> <input type=\"text\" onkeyup=\"clearNoNum(this)\"/><span class=\"goodsUnit\">"+ unit +"</span>" +
                        "        </div>" +
                        "    </div>" +
                        "</li>";
                }
                str += "<li data-mtid='"+ mtId +"' data-mtsupid='-1' class=\"supKuItem\">" +
                    "    <div>" +
                    "        <div> <p>难以区分供应商的本材料</p></div>" +
                    "        <div> <span class=\"addOneStore\" onclick=\"newStore($(this))\">增加新库位</span> </div>" +
                    "    </div>" +
                    "    <div class=\"kuItem\">" +
                    "        <div>" +
                    "            <span class=\"gapRt\">库位</span><select onchange=\"fillStore($(this))\" class=\"addInitStore\">"+ localStr +"</select>" +
                    "            <span class=\"ty-color-blue hd\" onclick=\"seeStoresDetail($(this))\">查看该库位情况</span>" +
                    "        </div>" +
                    "        <div>" +
                    "            <span class=\"gapRt\">数量</span> <input type=\"text\" onkeyup=\"clearNoNum(this)\"/><span class=\"goodsUnit\">"+ unit +"</span>" +
                    "        </div>" +
                    "    </div>" +
                    "</li>";
                $("#hasSup").html(str);

            }else{
                $(".noSup").show().siblings().hide();
                $("#initialStockList").data('supNum', 0);
                $(".toggleCheck0").show().siblings().hide();
                $("#toggleCheckNum").val(0);
                $("#toggleCheck").attr("class","fa fa-square-o");
                $(".toggleCheck0 .kuItem").remove();
                newStore($("#addOneStore3"));
            }
        }
    })
}

// creator ：侯杏哲 2020-04-26  初始库存 修改
function initStockEditBtn(selector) {
    let data = selector.siblings(".hd").html()
    data = JSON.parse(data)
    $("#newStore").val('');
    bounce_Fixed.show($('#updateStores'));
    $("#updateStores .sureBtn").unbind().on("click", function (){
        let val = $("#newStore").val()
        if ($.trim(val) === '') {
            layer.msg('您还没输入具体数字呢！')
        } else if ($.trim(val) < 0) {
            layer.msg('此数字将导致实际库存小于零。<br>请输入正确的数字！')
        } else{
            $.ajax({
                url:"../skl/updateInitialStock.do" ,
                data:{
                    // id: 材料id   oldStock：当前库存  newStock：修改后的库存
                    id: data.id,
                    oldStock: data.initial_stock,
                    newStock: $.trim($("#newStore").val())
                }
            }).then(res => {
                bounce_Fixed.cancel();
                bounce.cancel();
                if (res == 0) {
                    layer.msg("修改失败!<br>已申请数量大于修改后库存");
                }else if(res == 1){
                    layer.msg("修改成功");
                    let page  = $(".page:visible").attr("page")
                    if (page === 'initialInventory_changeWh_edit') {
                        selector.siblings(".initial_stock").html($.trim($("#newStore").val()))
                    } else {
                        reloadPage()
                    }
                }else {
                    layer.msg("修改失败");
                }
            })
        }
    })
}

// creator: hxz，2020-02-25 08:15:05，修改初始库存
function updateStores() {
    $("#newStore").val('');
    bounce_Fixed.show($('#updateStores'));
}
// creator: hxz，2020-01-20 16:52:08，修改初始库存提交
function updateStoresSure() {
    var html = '';
    if ($.trim($("#newStore").val()) == '') {
        html = '您还没输入具体数字呢！';
        $("#knowTipMs").html(html);
        bounce_Fixed2.show($("#knowTip"));
    } else if ($.trim($("#newStore").val()) < 0) {
        html =
            '<p>此数字将导致实际库存小于零。</p>' +
            '<p>请输入正确的数字！</p>';
        $("#knowTipMs").html(html);
        bounce_Fixed2.show($("#knowTip"));
    } else{
        var itemId = $("#updateStores").data("id");
        var type = $("#updateStores").data("type");

        let curNum = $('#initStock').data('initialstock')
        $.ajax({
            url:"../skl/updateInitialStock.do" ,
            data:{
                // id: 材料id   oldStock：当前库存  newStock：修改后的库存
                id: itemId,
                oldStock: curNum,
                newStock: $.trim($("#newStore").val())
            },
            success:function(data){
                bounce_Fixed.cancel();
                bounce.cancel();
                if (data == 0) {
                    layer.msg("修改失败  已申请数量大于修改后库存");
                }else if(data == 1){
                    layer.msg("修改成功");
                    var aa = $("#ye1").find(".json").html()
                    var a = JSON.parse(aa);
                    var cur =  $("#ye1").find(".yecur").html();
                    getPdByCategory(a.category, a.state, a.keyword, cur , 20);
                }else {
                    layer.msg("修改失败");
                }
            },
            error:function(){
                $("#tip #tipMs").html("系统错误，请重试!") ;
                bounce_Fixed2.show($("#tip")) ;
            }
        });

    }
}

// creator: hxz，2020-02-27 09:09:06，获取库位列表
var mtLocationList = []
function getLocationList() {
    $.ajax({
        url:"../mt/locations" ,
        data: { 'org': sphdSocket.org.id },
        success:function(res){
            locationList = res.data ;

        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: hxz，2020-08-04 15:02:03，录入初始库存数量/库位确定
function addInitialStock(){
    var isOk = true ; // 库位数量同时为空可以； 单一缺少不能提交
    var supNum = $("#initialStockList").data('supNum');
    var mtID = $('#enterInitialStock').data("id");
    var data = {   "mtID":mtID   }
    if(supNum == 1){ // 有供应商
        var option = $("#option").val();
        if( option == "1"){
            data['initialStock'] = $("#initKu").val();
            data['operation'] = 7;

        }else if(option == "2"){
            var arr = [] ;
            $(".Check2 .kuItem").each(function(){
                var location = $(this).find("select").val() ;
                var amount = $(this).find("input").val()
                if(Number(amount)>0){
                } else{
                    isOk = false;
                }
                if(location>0){
                }else{
                    isOk = false;
                }
                if(location>0 && amount!=""){
                    arr.push({
                        "location": location,
                        "amount": amount ,
                        "unit": $(this).find(".goodsUnit").val(),
                        "supplierMaterial":0,
                        "material":mtID,
                        "createName":sphdSocket.user.userName,
                        "creator":sphdSocket.user.userID
                    })
                }

            })
            data['params'] = JSON.stringify(arr) ;
            data['operation'] = 8;

        }else{
            var arr = [] ;
            $(".kuList .supKuItem").each(function(){
                var supplierMaterial = $(this).data("mtsupid") ;
                $(this).find(".kuItem").each(function(){
                    var location = $(this).find("select").val() ;
                    var amount = $(this).find("input").val()
                    if(Number(amount)>0){
                    } else{
                        isOk = false;
                    }
                    if(location>0){
                    }else{
                        isOk = false;
                    }
                    if(location>0 && amount!="") {
                        arr.push({
                            "location": location,
                            "amount": amount ,
                            "unit": $(this).find(".goodsUnit").val(),
                            "supplierMaterial":supplierMaterial,
                            "material":mtID,
                            "createName":sphdSocket.user.userName,
                            "creator":sphdSocket.user.userID
                        })
                    }
                })
            });
            data['params'] = JSON.stringify(arr) ;
            data['operation'] = 8;
        }

    }else{ // 没有供应商
        var toggleCheckNum = $("#toggleCheckNum").val();
        data['operation'] = 8;
        if(toggleCheckNum == '1'){
            data['operation'] = 7 ;
            data['initialStock'] = $("#initKu0").val();

        }else {
            var arr = [] ;
            $(".toggleCheck0 .kuItem").each(function(){
                var location = $(this).find("select").val() ;
                var amount = $(this).find("input").val()
                if(Number(amount)>0){
                } else{
                    isOk = false;
                }
                if(location>0){
                }else{
                    isOk = false;
                }
                if(location>0 && amount!="") {
                    arr.push({
                        "location": location,
                        "amount": amount ,
                        "unit": $(this).find(".goodsUnit").val(),
                        "supplierMaterial":0,
                        "material":mtID,
                        "createName":sphdSocket.user.userName,
                        "creator":sphdSocket.user.userID
                    })
                }

            });
            data['params'] = JSON.stringify(arr) ;
        }
    }
    if(!isOk){
        layer.msg("还有必填项尚未填写！");
        return false;
    }
    $.ajax({
        url:"../mt/stockAndLocation" ,
        data:  data ,
        success:function( data ){
            if(data.code == 200){
                reloadPage()
                bounce_Fixed.cancel();
            }else{
                layer.msg("系统错误，请重试!") ;
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    })
}

var licationSelectArr = [] ;
function fillStore(obj) {
    var locationID = obj.val();
    var index_ = licationSelectArr.indexOf(locationID);
    if(index_ > -1){
        layer.msg('本次已选择了相同的库位！')
        obj.val('0')
    }else{
        licationSelectArr.push(locationID);
        if ( locationID!= 0){
            obj.next("span").show();
        } else{
            obj.next("span").hide();
        }
    }
}
// creator: hxz，2020-01-20 09:23:01，直接录入初始库存数量，暂不选择库位
function turnCheck (obj , num) {
    if (obj.hasClass('fa-square-o')) {
        $(".selectSect").find(".fa-check-square-o").removeClass('fa-check-square-o').addClass('fa-square-o');
        obj.removeClass('fa-square-o').addClass('fa-check-square-o');
        $(".Check" + num).show().siblings().hide();
        $("#option").val(num);
        if(num == 2){
            $(".Check2").find(".kuItem").remove();
            newStore($("#addOneStore2"))
        }
    }else{
        obj.removeClass('fa-check-square-o').addClass('fa-square-o');
        $(".selectSect .kuList").show().siblings().hide();
        $("#option").val("");
    }
}
// creator: hxz，2020-01-20 09:23:01，直接录入初始库存数量，暂不选择库位
function toggleCheck (obj ) {
    var num = 0
    if (obj.hasClass('fa-square-o')) {
        obj.removeClass('fa-square-o').addClass('fa-check-square-o');
        num = 1 ;
    }else{
        obj.removeClass('fa-check-square-o').addClass('fa-square-o');
        num = 0 ;
    }
    $("#toggleCheckNum").val(num);
    $(".toggleCheck" + num ).show().siblings().hide();
}
// creator: hxz，2020-09-298 09:23:01 重新选择库位
function reSetFa (obj,option) {
    // option - 0:不选择库位，1：不区分供应商
    var num = 2 ; // 0：不选库位 ， 1：不区分供应商，2：区分供应商
    if (obj.hasClass('fa-square-o')) {
        obj.removeClass('fa-square-o').addClass('fa-check-square-o');
        obj.parent().siblings().each(function () {
            $(this).find(".fa").attr("class", "fa fa-square-o");
        });
        if(option == 0){
            num = 0 ;
        } else if(option == 1){
            num = 1 ;
        }
    }else{
        obj.removeClass('fa-check-square-o').addClass('fa-square-o');
        num = 2 ;
    }
    $("#reSetFa").val(num);
    $(".storeList").children(":eq("+ num +")").show().siblings().hide();
}
// creator: hxz，2020-01-20 10:09:44，增加新库位
function newStore(thisObj) {
    var emty = 0;
    var pObj = thisObj.parents(".supKuItem") ;
    pObj.find("select").each(function(){
        if ($(this).val() == 0) emty++;
    });
    pObj.find("input:visible").each(function(){
        if($(this).val() == '') emty++;
    });
    if(emty > 0){
        $("#nullTip #nullTipMs").html('录完一个库位的数据后才能增加新库位!');
        bounce_Fixed2.show($("#nullTip"));
    } else{
        var options = '<option value="0"></option>';
        if (mtLocationList.length > 0){
            for(var q = 0 ; q < mtLocationList.length ; q++){
                options += "<option value='"+ mtLocationList[q]["id"] +"'>"+ mtLocationList[q]["location_code"] +"</option>";
            }
        }
        var unit = $('.unit:visible').html();
        var str =
            '<div class="kuItem">' +
            '    <div>' +
            '        <span class="gapRt">库位</span>' +
            '        <select onchange="fillStore($(this))">' + options +
            '        </select>' +
            '        <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>' +
            '    </div>' +
            '    <div>' +
            '        <span class="gapRt">数量</span>' +
            '        <input type="text" onkeyup="clearNoNum(this)" /><span class="gapLt">'+ unit +'</span>'+
            '    </div>'+
            '</div>';
        pObj.append(str);
    }
}
// creator: hxz，2020-01-20 10:22:39，查看该库位情况
function seeStoresDetail(obj) {
    $(".storesCreateInfo tbody").html('');
    $(".scienceCreateInfo tbody").html('');
    var id= obj.siblings("select").val();
    $.ajax({
        // url:"../finished/getLocationDetail.do" ,
        "url":"../mt/locations" ,
        "data":{ 'org': sphdSocket.org.id,  'id': id },
        success:function(res){
            bounce_Fixed2.show($("#seeStores"));
            var info = res.data[0];
            var part1 = '', part2 = '', list = res['mts'] || [];
            $("#seeStoresName").html( info.location_code);
            part1 +=
                '<tr>' +
                '    <td>'+ (info.warehouse_code || "")  +'</td>' +
                '    <td>'+ (info.region_code ||"") +'</td>' +
                '    <td>'+ (info.shelf_code || "") +'</td>' +
                '    <td>'+ (info.layer || "") +'</td>' +
                '    <td></td>' +
                '    <td class="createInfo">'+ (info.create_name || "") + '&nbsp;&nbsp;' + new Date(info.create_date).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                '</tr>';
            $(".storesCreateInfo tbody").html(part1);
            if (list.length > 0){
                for(var t=0;t<list.length;t++) {
                    part2 += '<tr>' +
                        '     <td>'+ list[t].name +'</td>' +
                        '     <td>'+ list[t].code +'</td>' +
                        '     <td>'+ list[t].model +'</td>' +
                        '     <td>'+ list[t].specifications +'</td>' +
                        '     <td>'+ list[t].unit +'</td>' +
                        '     <td>'+ list[t].amount +'</td>' +
                        ' </tr>';
                }
                $(".scienceCreateInfo tbody").html(part2);
            }
        }
    })
}

// creator: hxz，2020-02-04 16:21:35，重新选择库位初始化
function reSelectStock(obj, openType) {
    // type : 1 表示从重新选择库位进去的
    let info = JSON.parse(obj.siblings(".hd").html()) ;

    let goodsHtml =
        '<tr>' +
        '    <td>'+ info.name  + '</td>' +
        '    <td>'+ info.code  + '</td>' +
        '    <td>'+ info.model  + '</td>' +
        '    <td>'+ info.specifications  + '</td>' +
        '    <td class="createInfo">'+ info.create_name + '&nbsp;&nbsp;' + new Date(info.create_date).format('yyyy/MM/dd hh:mm:ss') + '</td>' +
        '    <td class="unit">'+ info.unit  + '</td>' +
        '    <td><span class="floatLocation">'+(info.initial_stock || 0)+'</span></td>' +
        '    <td><span class="holdLocation">'+(info.location_number || 0)+'</span>个</td>' +
        '    <td class="hd">'+JSON.stringify(info)+'</td>' +
        '</tr>';
    $("#reselectList tbody").html(goodsHtml);
    bounce_Fixed.show($('#reselectLocation'));
    $('#reselectLocation').data("selector", obj)
    $.ajax({
        url: '../mt/locations',
        data: { org: sphdSocket.org.id },
        success:function(res){
            let mtLocationList = res.data || [] ;
            let licationSelectArr = [] ; // 重置已选择的库位id
            // 初始化
            $("#hasSup").html("");
            $("#reselectLocation .fa").attr("class","fa fa-square-o");
            $("#noSup .kuItem").remove();
            newStore($("#addOneStore4"));
            $('#reselectLocation input').val("");
            $("#reselectTtl").html('重新选择库位');
            $(".noChoose").hide();$(".hasChoose").show();
            var type = $("#holdStockSeeReset").data("type" ) ; // -1 ：区分 ， 0：不区分供应商
            $(".hasChoose .case2").show();$(".hasChoose .case1").show();
            $("#hasSup").show().siblings().hide();
            getResetSuppliers(info , mtLocationList)
        }
    });
}
// creator: hxz，2020-02-24 16:02:15，重新选择库位提交
function updateLocation() {
    var isOk = true ; // 库位数量同时为空可以； 单一缺少不能提交
    var openType = $("#reselectLocation").data("openType");
    var info = JSON.parse($("#reselectList .hd").html())
    var mtID = info['id'];
    var createName = sphdSocket.user.userName;
    var creator = sphdSocket.user.userID;
    var data = { "mtID":mtID ,  "operation":8 ,  "re":1 }
    var num = $("#reSetFa").val();
    if(num === "0"){
        data['operation'] = 9 ;
        data['params'] = JSON.stringify([]) ;
    }else if(num === "1"){ // 不区分供应商
        var arr = [] ;
        $("#noSup .kuItem").each(function(){
            var location = $(this).find("select").val() ;
            var amount = $(this).find("input").val()
            if(location>0 && amount==""){
                isOk = false;
            }  if(location==0 && amount!=""){
                isOk = false;
            }
            if(location>0 && amount!=""){
                arr.push({
                    "location": location,
                    "amount": amount ,
                    "unit": $(this).find(".goodsUnit").val(),
                    "supplierMaterial":0,
                    "material":mtID,
                    "createName":createName,
                    "creator":creator
                })
            }
        })
        data['params'] = JSON.stringify(arr) ;
    }else{ // 有供应商
        var arr = [] ;
        $("#hasSup .supKuItem").each(function(){
            var supplierMaterial = $(this).data("mtsupid") ;
            $(this).find(".kuItem").each(function(){
                var location = $(this).find("select").val() ;
                var amount = $(this).find("input").val()
                if(location>0 && amount==""){
                    isOk = false;
                }  if(location==0 && amount!=""){
                    isOk = false;
                }
                if(location>0 && amount!="") {
                    arr.push({
                        "location": location,
                        "amount": amount ,
                        "unit": $(this).find(".goodsUnit").val(),
                        "supplierMaterial":supplierMaterial,
                        "material":mtID,
                        "createName":createName,
                        "creator":creator
                    })
                }
            })
        });
        data['params'] = JSON.stringify(arr) ;
    }

    if(!isOk){
        layer.msg("还有必填项尚未填写！");
        return false;
    }
    $.ajax({
        url:"../mt/stockAndLocation" ,
        data:  data ,
        success:function( data ){
            if(data.code == 200){
                bounce_Fixed.cancel();
                bounce.cancel();
                let page  = $(".page:visible").attr("page")
                if (page === 'initialInventory_changeWh_edit') {
                    let selector = $('#reselectLocation').data("selector")
                    selector.parents("tr").remove()
                } else {
                    reloadPage()
                }
            }else{
                layer.msg("系统错误，请重试!") ;
            }
        }
    })
}
// creator: hxz，2020-02-24 20:08:36，重新选择库位公用接口
function updateLocationCommon(itemId) {
    $.ajax({
        url:"../finished/getLocationListByProduct.do" ,
        data:{
            id: itemId
        },
        success:function(data){
            if (data.code == 200) {
                var info = data.data;
                var list = info.list;
                var goodsInfo =
                    '<tr data-id="'+ info.id +'">' +
                    '    <td>'+ info.name +'</td>' +
                    '    <td>'+ info.innerSn +'</td>' +
                    '    <td>'+ info.model +'</td>' +
                    '    <td>'+ info.specifications +'</td>' +
                    '    <td class="createInfo">'+ info.createName + '&nbsp;&nbsp;' + new Date(info.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                    '    <td>'+ info.unit +'</td>' +
                    '    <td>'+ info.currentStock +'</td>' +
                    '    <td>'+ info.num +'个</td>' +
                    '</tr>';
                $(".reUnit").html(info.unit);
                $("#reselectList tbody").html(goodsInfo);
                if (list.length > 0){
                    var len = Math.ceil(list.length/10);
                    $("#reselectTtl").html('重新选择库位');
                    for(var m=0;m<len;m++){
                        var tabStr =
                            '<table class="ty-table ty-table-control gap ">' +
                            '   <tr>' +
                            '       <td rowspan="2" class="td-orange">现况</td>';
                        var size = 10,locationCode = '',locationNum ='<tr>';
                        var render = list.length%10;
                        if (m==len-1 && render>0) {
                            size = render;
                        }
                        for(var n=0;n<size;n++){
                            var i = m*size + n;
                            locationCode += '<td>'+ list[i].locationCode +'</td>';
                            locationNum += '<td>'+ list[i].amount +'</td>';
                        }
                        tabStr += locationCode + locationNum +
                            '</tr></table>';
                    }
                    $("#reselectLocation .noStation").hide();
                    $("#reselectLocation .hasStation").show();
                    $("#resetCurrentStation").html(tabStr);
                }else{
                    $("#reselectLocation .noStation").show();
                    $("#reselectLocation .hasStation").hide();
                }
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}

//  create: hxz 2021-05-11 保留三位小数
function tofixed3(obj) {
    let v = obj.value
    v = v.replace(/[^\d.]/g, "");
    v = v.replace(/^\./g, "");
    v = v.replace(/\.{2,}/g, ".");
    v = v.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".")
    let vArr = v.split('.')
    if(vArr[0].length > 9){ // 最多9位
        vArr[0] = String(vArr[0]).substr(0,9);
    }
    v = vArr[0]
    if(vArr.length > 1){
        if(vArr[1].length > 4){
            vArr[1] = vArr[1].substr(0,4);
        }
        v += '.' + vArr[1];
    }
    obj.value = v
}