// creator: 张旭博，2023-02-24 15:39:49，返回上一步
function back(callback) {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr.pop()
    $("#home").data("pathArr", pathArr)
    isShowNav()
    var lastEle = pathArr[pathArr.length - 1]
    $(".page[page='"+lastEle+"']").show().siblings(".page").hide()
    if (lastEle === 'main') {
        $(".backBtn").hide()
    }
    if (methods[lastEle]) {
        methods[lastEle]()
    }
}

// creator: 张旭博，2023-02-24 15:40:51，回到主页
function backToMain() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr = [pathArr[0]]
    $("#home").data("pathArr", pathArr)
    isShowNav()
    $(".page[page='"+pathArr[0]+"']").show().siblings(".page").hide()
}

let methods = {}

// creator: 张旭博，2023-02-09 04:56:46， 跳转及退回
function jumpPage(page, callback) {
    var arr = $("#home").data("pathArr") || []
    arr.push(page)
    $("#home").data("pathArr", arr)
    isShowNav()
    $(".page[page='"+page+"']").show().siblings(".page").hide()
    if (page !== 'main') {
        $(".backBtn").show()
    }
    if (callback) {
        methods[page] = callback
        callback()
    } else {
        if (methods[page]) {
            methods[page]()
        }
    }
    console.log(methods)
}

function reloadPage() {
    var pathArr = $("#home").data("pathArr")
    var lastEle = pathArr[pathArr.length - 1]
    if (methods[lastEle]) {
        methods[lastEle]()
    }
}

// creator: 张旭博，2023-02-24 15:40:37，是否禁用导航
function isShowNav() {
    var pathArr = $("#home").data("pathArr")
    if (pathArr.length > 1) {
        $(".ty-page-header").show()
    } else {
        $(".ty-page-header").hide()
    }
}