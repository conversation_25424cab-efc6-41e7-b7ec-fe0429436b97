var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#wonderssOverTimeTips"))
bounce_Fixed3.show($("#knowTip"))
bounce_Fixed2.cancel()
bounce_Fixed3.cancel()
$(function (){
    jumpPage("main", function () {
        // 主页为静态资源
        initMainData()
    })
    $(".page[page='main']").on('click', '[type="btn"]', function () {
        var name = $(this).attr("name")
        var to = $(this).attr('to')
        var jumpName = to + '_' +name
        if (to) {
            jumpPage(jumpName, ()=> {
                switch (jumpName) {
                    case 'generalPower_set':
                        getGeneralAuthorityList()
                        break
                    case 'assignedPower_see':
                        getCurrentSetting('assigned')
                        break
                    case 'unassignedPower_see':
                        getCurrentSetting('unAssigned')
                        break
                    case 'workerAuthorityPower_see':
                        getWorkerSetting(0)
                        break
                    case 'approvePower_set':
                        // 内容太多，直接引入的js
                        break
                    case 'approvePower_see':
                        getWorkerSetting(1)
                        break
                    case 'employees_import':
                        defineGetList(1, 20, 1)
                        break
                }
            })
        }
    })
    // 审批设置部分
    $("#chargeList").on("click", 'button', function (){
        var name = $(this).attr("name")
        var code = $(this).parents("tr").data("code")
        var id = $(this).parents("tr").data("id")
        switch (name) {
            // 主列表 - 查看
            case 'see':
                requestSet(code, id, $(this))
                break
            // 主列表 - 查看
            case 'change':
                requestSet(code, id, $(this))
                break
            // 主列表 - 修改记录
            case 'changeRecord':
                requestLog(code, id)
                break
        }
    })
    // updator:hxz 2018-05-10 权限设置的模块点击事件和勾选逻辑
    $("#generalAuthority").on("click",".ty-form-checkbox",function () {
        // 权限管理的特殊设置 ：
        // 1.勾选“权限设置eb”或“审批设置ec”，
        //       则 “权限设置eb”、“审批设置ec”、“当前权限ed”、“职工权限ef” 四项都必选 ，
        //       且“当前权限”、“职工权限” 不能勾掉 ;
        //      “审批查看ee” 必不勾选
        // 2.勾选“当前权限ed” ，则 “审批查看ee” 必勾选
        // 3.文件夹管理rb表格管理rd 不能单独选择， 必须选中权限设置eb
        // 4.内容管理-目录aca 不能单独选择， 必须选中权限设置eb,内容管理acb受联动影响，但是不需要勾选权限设置

        // 如果当前指针没有禁用
        if(!$(this).hasClass("ty-form-disabled")){
            var id = $(this).attr("id") ; var id0 = id.indexOf("e") ;
            // 如果当前指针已选中
            if($(this).hasClass("ty-form-checked")){
                // 处理权限的特殊情况
                if(id0 === 0){
                    if(id === "ea" ){
                        var b = $("#eb").hasClass("ty-form-checked") ;  var c = $("#ec").hasClass("ty-form-checked") ;
                        var d = $("#ed").hasClass("ty-form-checked") ;  var e = $("#ee").hasClass("ty-form-checked") ;
                        var f = $("#ef").hasClass("ty-form-checked") ;
                        if(!b && !c && !d && !e && !f){ // 子级没有选中的
                            $("#ea").removeClass("ty-form-checked");
                        }
                        return false ;
                    }else{
                        if(id ==="eb" || id ==="ec"){ // 第一条 联动的去掉 ,去掉小总务设置
                            $("#eb").removeClass("ty-form-checked");
                            $("#ec").removeClass("ty-form-checked");
                            $("#ed").removeClass("ty-form-checked");
                            $("#ef").removeClass("ty-form-checked");
                            $("#ra").removeClass("ty-form-checked");
                            $("#md").removeClass("ty-form-checked");
                            $("#rb").removeClass("ty-form-checked");
                            $("#rd").removeClass("ty-form-checked");
                            $("#ac").removeClass("ty-form-checked");
                            $("#aca").removeClass("ty-form-checked");
                            $("#acb").removeClass("ty-form-checked");
                            if($("#ed").hasClass("ty-form-disabled")){
                                $("#ed").removeClass("ty-form-disabled");
                            }
                        }else if(id === "ed" ){
                            if($("#eb").hasClass("ty-form-checked")){ // 第一条联动，不可去掉
                                layer.msg("不可取消") ;  return false ;
                            }else{// 第二条 联动去掉
                                $("#ed").removeClass("ty-form-checked");
                                $("#ee").removeClass("ty-form-checked");
                            }

                        }else if(id == "ee"){
                            if($("#ed").hasClass("ty-form-checked") && (!$("#ed").hasClass("ty-form-disabled"))){
                                layer.msg("不可去掉") ;  return false ;
                            }else {
                                $(this).removeClass("ty-form-checked") ;
                            }
                        }else if(id == "ef"){
                            if( $("#ed").hasClass("ty-form-checked") &&$("#eb").hasClass("ty-form-checked") &&$("#ec").hasClass("ty-form-checked") &&
                                (!$("#ed").hasClass("ty-form-disabled")) &&(!$("#eb").hasClass("ty-form-disabled")) &&(!$("#ec").hasClass("ty-form-disabled"))
                            ){
                                layer.msg("不可去掉") ; return false ;
                            }else{
                                $(this).removeClass("ty-form-checked") ;
                            }
                        }
                    }
                    return false ;
                }else{
                    if(id == "kb"){
                        $("#kj").removeClass("ty-form-checked");
                        $("#kk").removeClass("ty-form-checked");
                    }else if(id == "kj"){
                        $("#kb").removeClass("ty-form-checked");
                        $("#kk").removeClass("ty-form-checked");
                    }else if(id == "kk"){
                        $("#kb").removeClass("ty-form-checked");
                        $("#kj").removeClass("ty-form-checked");
                    }
                    //销售管理-权限设置
                    if(id == "qe" || id == "qb" ||id == "qc" || id == "qd" || id == "qg"){
                        /* $("#qa").removeClass("ty-form-checked");
                         $("#qb").removeClass("ty-form-checked");
                         $("#qc").removeClass("ty-form-checked");
                         $("#qd").removeClass("ty-form-checked");
                         $("#qg").removeClass("ty-form-checked");*/
                        $("#qa").click();
                    }
                    $(this).removeClass("ty-form-checked");
                    // 当前指针后面所有选择框全部置空（除了禁用）
                    $(this).parent("td").next("td").find(".ty-form-checkbox").not(".ty-form-disabled").removeClass("ty-form-checked");
                    // 如果当前指针最近的为二级并且它下面选中的只有一个
                    if($(this).closest("table").parent("td").attr("level") === "2" && $(this).closest("table").find(".ty-form-checked").not(".ty-form-disabled").length === 1){
                        //上级置空
                        $(this).parents("td[level='2']").prev().find(".ty-form-checkbox").removeClass("ty-form-checked");
                        //如果它的一级下的二级只有一个选中
                        if($(this).parents("td[level='1']").find(".ty-form-checked").not(".ty-form-disabled").length === 1){
                            //他的一级置空
                            $(this).parents("td[level='1']").prev().find(".ty-form-checkbox").removeClass("ty-form-checked");
                        }
                    }
                    // 如果它的直接上级为一级，并且它的下级只有一个选中
                    if($(this).closest("table").parent("td").attr("level") === "1" && $(this).closest("table").find(".ty-form-checked").not(".ty-form-disabled").length === 1){
                        //它的上级置空
                        $(this).parents("td[level='1']").prev().find(".ty-form-checkbox").removeClass("ty-form-checked");
                    }
                }
                // 如果当前指针未选中
            }else{
                if(id0 == 0){ // 权限管理的特殊设置 ：
                    $("#ea").addClass("ty-form-checked") ;
                    if(id === "ea" ){
                        return false ;
                    }else{
                        if(id ==="eb" || id ==="ec"){ // 第一条 联动
                            if($("#ee").hasClass("ty-form-checked")){ layer.msg("审批设置 与 审批查看 不能同时选中"); return false ; }
                            $("#eb").addClass("ty-form-checked"); $("#ec").addClass("ty-form-checked");
                            $("#ed").addClass("ty-form-checked"); $("#ef").addClass("ty-form-checked");
                            $("#ra").addClass("ty-form-checked"); $("#rb").addClass("ty-form-checked");$("#rd").addClass("ty-form-checked");
                            $("#md").addClass("ty-form-checked"); $("#ac").addClass("ty-form-checked");$("#aca").addClass("ty-form-checked"); $("#acb").addClass("ty-form-checked");
                        }else if(id === "ed" ){ // 第二条 联动

                            $("#ed").addClass("ty-form-checked"); $("#ee").addClass("ty-form-checked");
                        }else if(id === "ee"){
                            if($("#ec").hasClass("ty-form-checked")){
                                layer.msg("审批设置 与 审批查看 不能同时选中") ;  return false ;
                            }else {
                                $(this).addClass("ty-form-checked") ;
                            }
                        }else if(id == "ef"){
                            $(this).addClass("ty-form-checked") ;
                        }
                    }
                }else{
                    if(id == "rb" || id == "ra"|| id == "rd"){
                        // 文件夹管理rb选中 权限设置必须选中
                        if(!$("#eb").hasClass("ty-form-checked")){
                            layer.msg("先选择权限设置才能设置文件夹管理！");
                            return false
                        }
                    }
                    if (id == "ac" || id == "aca") {
                        if(!$("#eb").hasClass("ty-form-checked")){
                            layer.msg("先选择权限设置才能设置内容管理-目录！");
                            return false
                        }
                    }
                    if(id == "kb"){
                        $("#kj").addClass("ty-form-checked");
                        $("#kk").addClass("ty-form-checked");
                    }else if(id == "kj"){
                        $("#kb").addClass("ty-form-checked");
                        $("#kk").addClass("ty-form-checked");
                    }else if(id == "kk"){
                        $("#kj").addClass("ty-form-checked");
                        $("#kb").addClass("ty-form-checked");
                    }
                    //销售管理-权限设置
                    if(id == "qe" || id == "qb" || id == "qc" || id == "qd" || id == "qg"){
                        /*$("#qa").addClass("ty-form-checked");
                        $("#qb").addClass("ty-form-checked");
                        $("#qc").addClass("ty-form-checked");
                        $("#qd").addClass("ty-form-checked");*/
                        $("#qa").click();
                    }
                    $(this).addClass("ty-form-checked");
                    // 它的上下级都选中
                    $(this).parent("td").next("td").find(".ty-form-checkbox").not(".ty-form-disabled").addClass("ty-form-checked");
                    $(this).parents("td").prev("td").find(".ty-form-checkbox").not(".ty-form-disabled").addClass("ty-form-checked");
                }
            }
        }
    });
    $(".ty-secondTab li").on("click", function () {
        var index = $(this).index()
        $(this).addClass("ty-active").siblings().removeClass("ty-active")
        $(".tblContainer").eq(index).show().siblings(".tblContainer").hide()
        $(".tblContainer").eq(index).find("tbody").html("")
        getPopedomListByManager(index)
    })
    $(".unfinishedForm").on('click', ".fa",function() {
        $(this).addClass("fa-circle").removeClass("fa-circle-o");
        $(this).parent().siblings(".changeDot").find("span").addClass("fa-circle-o").removeClass("fa-circle");
    });

})

// creator: 张旭博，2023-09-06 04:55:34， 初始化首页的数据
function initMainData() {
    // 加载必须分配的权限显示内容
    $.ajax($.webRoot + '/initialize/mustAllocationList.do').then(res => {
        let data = res.data
        let setData = []
        if (data) {
            setData = data.filter(item => item.userName)
        }
        $(".totalItem").html(data.length)
        $(".setItem").html(setData.length)

        if (data.length === setData.length) {
            $("#initCompleted").prop("disabled", false)
        } else {
            $("#initCompleted").prop("disabled", true)
        }

        // 直接在此处填充了弹窗的表格，避免多次调用
        let str = ''
        if (data) {
            for (let item of data) {
                str += `<tr data-code="${item.code}">
                            <td>${item.name}</td>
                            <td>${item.userName || '--'}</td>
                            <td><span class="link-blue" onclick="editRole($(this), 'haveToSetGeneralAuthority')">编辑</span></td>
                        </tr>`
            }
        }
        $("#haveToSetGeneralAuthorityBtn tbody").html(str)
        $("#haveToSetGeneralAuthorityBtn .num").html(data.length)
    })
}

// creator: 张旭博，2023-09-07 10:03:35， 初始化完成
function initCompleteBtn() {
    let checked = $("#initCompleted").prop("checked")
    if (!checked) {
        layer.msg("请勾选！")
        return false
    }
    $.ajax('../org/confirmOrg.do').then(res => {
        if(res === 1) {
            window.location.href = '../sys/logout.do'
        } else {
            layer.msg("操作失败")
        }
    })
}

// creator: 张旭博，2023-07-13 03:29:23， 必须设置的常规权限 - 按钮
function haveToSetGeneralAuthorityBtn () {
    bounce.show($("#haveToSetGeneralAuthorityBtn"))
}

// creator: 张旭博，2023-09-05 12:00:14， 选择人员
function editRole(selector, name) {
    bounce_Fixed2.show($("#editRole"))
    $.ajax('../popedom/getUserList.do').then(res => {
        let data = res.userList
        let optionStr = '<option value="">----- 请选择 -----</option>'
        for(let item of data) {
            optionStr += `<option value="${item.userID}">${item.userName} ${item.mobile}</option>`
        }
        $("#editRole [name='role']").html(optionStr)
    })
    $("#editRole .sureBtn").unbind().on('click', function (){
        let passiveUserId = $("#editRole [name='role']").val()
        let code = selector.parents("tr").data("code")
        if (passiveUserId === '') {
            layer.msg('请选择人员！')
            return false
        }
        $.ajax({
            url: '../initialize/saveMustAllocation.do',
            data: {
                code: code,
                passiveUserId: passiveUserId,
            }
        }).then(res => {
            let data = res.data
            if (data === 1) {
                layer.msg('操作成功！')
                bounce_Fixed2.cancel()
                initMainData()
            } else {
                layer.msg('操作失败')
            }
        })
    })
}
function getGeneralAuthorityList() {
    $.ajax({
        url:"../popedom/getUserList.do",
        success:function( data ){
            var userList = data["userList"];                          //获取列表json
            var generalListStr = "";                                           //承载列表字符串

            if(userList !== undefined) {
                //循环遍历json
                //value： [{userName 姓名，gender 1-男 0-女，mobile 手机号，departName 部门，postName 职位，leaderName 直接上级},{}]

                for (var i = 0; i < userList.length; i++) {
                    var roleCode = userList[i].roleCode
                    if (roleCode === 'agent') {
                        var handleStr = '<span class="ty-color-gray">权限分配</span>'
                    } else {
                        var handleStr = '<span class="ty-color-blue" onclick="allocateAuthorityBtn($(this))">权限分配</span>'
                    }


                    generalListStr +=   '<tr id="' + userList[i].userID + '">' +
                        '<td>' + userList[i].userName + '</td>' +
                        '<td>' + chargeSex(userList[i].gender)+ '</td>' +
                        '<td>' + userList[i].mobile+ '</td>' +
                        '<td>' + userList[i].departName+ '</td>' +
                        '<td>' + userList[i].postName+ '</td>' +
                        '<td>' + userList[i].leaderName + '</td>' +
                        '<td>'+handleStr+'</td>' +
                        '</tr>';
                }

                //遍历结束，将字符串写进对应表格中
                $(".tplContainer").eq(0).find("tbody").html(generalListStr);
            }
        }
    }) ;
}

// ------------- 权限设置部分接口----------------

// creator: 张旭博，2023-09-05 01:50:54， 获取可设置权限的职工列表
function getGeneralAuthorityList() {
    $.ajax({
        url:"../popedom/getUserList.do",
        success:function( data ){
            var userList = data["userList"];                          //获取列表json
            var generalListStr = "";                                           //承载列表字符串

            if(userList !== undefined) {
                //循环遍历json
                //value： [{userName 姓名，gender 1-男 0-女，mobile 手机号，departName 部门，postName 职位，leaderName 直接上级},{}]

                for (var i = 0; i < userList.length; i++) {
                    var roleCode = userList[i].roleCode
                    if (roleCode === 'agent') {
                        var handleStr = '<span class="link-gray">权限分配</span>'
                    } else {
                        var handleStr = '<span class="link-blue" onclick="allocateAuthorityBtn($(this))">权限分配</span>'
                    }

                    generalListStr +=   '<tr id="' + userList[i].userID + '">' +
                        '<td>' + userList[i].userName + '</td>' +
                        '<td>' + chargeSex(userList[i].gender)+ '</td>' +
                        '<td>' + userList[i].mobile+ '</td>' +
                        '<td>' + userList[i].departName+ '</td>' +
                        '<td>' + userList[i].postName+ '</td>' +
                        '<td>' + userList[i].leaderName + '</td>' +
                        '<td>'+handleStr+'</td>' +
                        '</tr>';
                }

                //遍历结束，将字符串写进对应表格中
                $(".tbl_generalPower_set tbody").html(generalListStr);
            }
        }
    }) ;
}

// creator: 张旭博，2023-09-05 01:48:10，权限分配 - 按钮
function allocateAuthorityBtn(selector) {
    selector.parents("tr").addClass("generalAuthorityActive").siblings().removeClass("generalAuthorityActive");
    bounce.show($("#generalAuthority"));
    getPopedomListByManager(1)
    $("#generalAuthority .ty-secondTab li").unbind().on("click", function () {
        var index = $(this).index()
        $(this).addClass("ty-active").siblings().removeClass("ty-active")
        $(".tblContainer").eq(index).show().siblings(".tblContainer").hide()
        $(".tblContainer").eq(index).find("tbody").html("")
        getPopedomListByManager(index)
    })
    $("#generalAuthority .ty-secondTab li").eq(0).click()
}

// creator: 张旭博，2023-09-05 01:47:52， 权限分配 - 确定
function sureAllocateAuthority(){
    var mid = '';
    $(".ty-form-checked").each(function (index) {
        if(index === 0){
            mid += $(this).attr("id");
        }else{
            mid += ","+$(this).attr("id");
        }
    });
    var userId = $(".generalAuthorityActive").attr("id");
    var data = {
        "userId":userId,
        "mid":$.trim(mid)
    };
    $.ajax({
        url:"../popedom/saveUserPopedom.do" ,
        data:data ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            var status = data["status"];
            if(status === 0){
                $("#errorTip .tipWord").html("保存失败，请重试！")
                bounce.show($("#errorTip")) ;
            }else if(status === 1){
                bounce.cancel();
                layer.msg("保存成功！")
            }
        },
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

// creator: 张旭博，2023-09-05 01:48:29， 获取模块
function getPopedomListByManager(index) {
    var userId = $(".generalAuthorityActive").attr("id");
    $(".userName").html($(".generalAuthorityActive").find("td").eq(0).html())
    if (index === 0) {
        var url = '../popedom/getExclusivePopedomsByManager.do'
    } else {
        var url = '../popedom/getPopedomListByManager.do'
    }
    $.ajax({
        url: url,
        data:{
            "manageId": sphdSocket.user.userID, // 分配人id
            "userId":userId     // 被分配人id
        } ,
        success:function( data ){
            var success = data["success"];
            var dataList = data["data"];

            var checked,disabled;
            var listStr = '';
            if(success === 1) {
                for (var i = 0; i < dataList.length; i++) {
                    checked = dataList[i].checked;
                    disabled = dataList[i].disabled;
                    var checkedClass, disabledClass;
                    if (checked) {
                        checkedClass = "ty-form-checked"
                    } else {
                        checkedClass = ""
                    }
                    if (disabled) {
                        disabledClass = "ty-form-disabled"
                    } else {
                        disabledClass = ""
                    }
                    var subPopdoms2 = dataList[i].subPopdoms;
                    var users2 = dataList[i].users;
                    listStr += '<tr>' +
                        '   <td style="width: 25%">' +
                        '       <div class="ty-form-checkbox ' + checkedClass + ' ' + disabledClass + '" skin="green" id="' + dataList[i].mid + '">' +
                        '           <span>' + dataList[i].name + '</span>' +
                        '           <i class="fa fa-check"></i>' +
                        '       </div>' +
                        '   </td>' +
                        '   <td style="width: 75%"  level="1">' +
                        '       <table class="ty-table" frame="void">';
                    //第二层循环
                    if (subPopdoms2.length > 0) {
                        for (var j = 0; j < subPopdoms2.length; j++) {
                            checked = subPopdoms2[j].checked;
                            disabled = subPopdoms2[j].disabled;
                            if (checked) {
                                checkedClass = "ty-form-checked"
                            } else {
                                checkedClass = ""
                            }
                            if (disabled) {
                                disabledClass = "ty-form-disabled"
                            } else {
                                disabledClass = ""
                            }
                            //第三层数据
                            var subPopdoms3 = subPopdoms2[j].subPopdoms;
                            var users3 = subPopdoms2[j].users;
                            var userNameArr3 = []
                            users3.forEach(function (item) {
                                userNameArr3.push(item.userName)
                            })
                            listStr +=  '        <tr>' +
                                '           <td style="width: 30%">' +
                                '              <div class="ty-form-checkbox ' + checkedClass + ' ' + disabledClass + '" skin="green" id="' + subPopdoms2[j].mid + '">' +
                                '                  <span>' + subPopdoms2[j].name + '</span>' +
                                '                  <i class="fa fa-check"></i>' +
                                '              </div>' +
                                '           </td>' +
                                '           <td style="width: 30%"  level="2">' +
                                '               <table class="ty-table" frame="void">';
                            for (var k = 0; k < subPopdoms3.length; k++) {
                                checked = subPopdoms3[k].checked;
                                disabled = subPopdoms3[k].disabled;
                                var users4 = subPopdoms3[k].users
                                var userNameArr4 = []
                                users4.forEach(function (item) {
                                    userNameArr4.push(item.userName)
                                })
                                if (checked) {
                                    checkedClass = "ty-form-checked"
                                } else {
                                    checkedClass = ""
                                }
                                if (disabled) {
                                    disabledClass = "ty-form-disabled"
                                } else {
                                    disabledClass = ""
                                }
                                listStr +=  '                <tr>' +
                                    '                   <td>' +
                                    '                      <div class="ty-form-checkbox ' + checkedClass + ' ' + disabledClass + '" skin="green" id="' + subPopdoms3[k].mid + '">' +
                                    '                          <span>' + subPopdoms3[k].name + '</span>' +
                                    '                          <i class="fa fa-check"></i>' +
                                    '                      </div>' +
                                    '                   </td>' +
                                    '                   <td><span class="text">' +(userNameArr4.length > 0 ? userNameArr4.join("、"):"暂无")+'</span></td>' +
                                    '                </tr>';
                            }
                            listStr +=  '               </table>' +
                                '           </td>' +
                                '           <td style="width: 40%"><span class="text">' +(userNameArr3.length > 0 ? userNameArr3.join("、"):"暂无")+'</span></td>' +
                                '        </tr>';
                        }
                    } else {
                        var userNameArr2 = []
                        users2.forEach(function (item) {
                            userNameArr2.push(item.userName)
                        })
                        listStr +=  '<tr>'+
                            '   <td style="width: 30%"></td>'+
                            '   <td style="width: 30%"></td>'+
                            '   <td style="width: 40%"><span class="text">'+(userNameArr2.length > 0 ? userNameArr2.join("、"):"暂无")+'</span></td>'+
                            '</tr>'
                    }
                    listStr += '       </table>' +
                        '   </td>' +
                        '</tr>';
                }
                $("#generalAuthority .tblContainer").eq(index).find("tbody").html(listStr);
                // $("#generalAuthority .ty-form-disabled").each(function () {
                //     if($(this).hasClass("ty-form-checked")){
                //         $(this).parents("td[level='1']").prev().find(".ty-form-checkbox").addClass("ty-form-disabled");
                //     }
                // })
            }else{
                var error = data.error;
                if(error){
                    $("#errorTip .tipWord").html(error.message) ;
                    bounce.show($("#errorTip")) ;
                }else{
                    $("#errorTip .tipWord").html("系统错误，请重试!") ;
                    bounce.show($("#errorTip")) ;
                }
            }
        }
    }) ;
}

// creator: 张旭博，2023-09-05 01:50:07， 格式化男女
function chargeSex(sex) {
    switch (sex){
        case "0" :
            return "女";
            break;
        case "1" :
            return "男";
            break;
        default:
            return "";
    }
}

// ------------- 当前权限部分接口（包括已分配和未分配）----------------
// creator: 张旭博，2023-09-05 04:35:21， 获取当前权限（已分配和未分配）
function getCurrentSetting(type) {
    $.ajax({
        url:"../popedom/getOrgPopedomShow.do" ,
        success:function( data ){
            var unAssigned = data["data"].unAssigned;
            var assigned = data["data"].assigned;
            //未录入高管时显示重要提示
            if(assigned.length === 0){
                $(".ty-secondTab li").eq(1).hide();
                $("#assigned").hide();
                $(".importantTip").show();
            }else{
                $("#assigned").show();
                $(".importantTip").hide();
                //未分配没有结果时只显示已分配
                if(type === 'assigned'){
                    $("#assigned .ty-body").html(getListStr(assigned,1));
                }else{
                    $("#unassigned .ty-body").html(getListStr(unAssigned,0));
                }
            }
            $("[data-toggle='tooltip']").tooltip();
        }
    }) ;
}

// creator: 张旭博，2023-09-05 04:35:31， 返回当前权限列表字符串
function getListStr(data,type) {
    var listStr = '';
    var dataList = data;
    var thirdUserStr = '';
    for (var i = 0; i < dataList.length; i++) {
        var subPopdoms2 = dataList[i].subPopdoms;
        //第一级列表
        listStr +=  '<ul>' +
            '   <li style="width: 10%">' +
            '       <span>' + dataList[i].name + '</span>' +
            '   </li>' +
            '   <li style="width: 90%"  level="1">' +
            '       <div>';//嵌套一层table
        //第二层循环
        for (var j = 0; j < subPopdoms2.length; j++) {
            //第三层数据
            var subPopdoms3 = subPopdoms2[j].subPopdoms;
            //如果第三层数据不为空
            if(subPopdoms3.length !== 0){
                //第二级列表
                listStr +=  '        <ul>' +
                    '           <li style="width: 20%">' +
                    '              <span>' + subPopdoms2[j].name + '</span>' +
                    '           </li>' +
                    '           <li style="width: 80%"  level="2">' +
                    '               <div>';//嵌套一层table
                //如果是未分配
                if(type === 0){
                    for (var k = 0; k < subPopdoms3.length; k++) {
                        listStr +=  '                <ul>' +
                            '                   <li style="width: 20%">' +
                            '                       <span>' + subPopdoms3[k].name + '</span>' +
                            '                   </li>' +
                            '                   <li style="width: 80%">' +
                            '                       <span>' + subPopdoms3[k].desc + '</span>' +
                            '                   </li>' +
                            '                </ul>';
                    }
                    //如果是已分配
                }else{
                    for (var k = 0; k < subPopdoms3.length; k++) {
                        listStr +=  '                <ul>' +
                            '                   <li style="width: 20%">' +
                            '                       <span>' + subPopdoms3[k].name + '</span>' +
                            '                   </li>' +
                            '                   <li style="width: 50%">' +
                            '                       <span>' + subPopdoms3[k].desc + '</span>' +
                            '                   </li>' +
                            '                   <li style="width: 30%">' +
                            '                       <span>' + getUserStr(subPopdoms3[k].users)+ '</span>' +
                            '                   </li>' +
                            '                </ul>';
                    }
                }
                listStr +=  '               </div>' +
                    '           </li>' +
                    '        </ul>';
                //如果第三层数据为空数组
            }else{
                //如果是未分配的第三层数据
                if(type === 0){
                    listStr +=  '        <ul>' +
                        '           <li style="width: 20%">' +
                        '              <span>' + subPopdoms2[j].name + '</span>' +
                        '           </li>' +
                        '           <li style="width: 80%">' +
                        '               <span>' + subPopdoms2[j].desc + '</span>' +
                        '           </li>' +
                        '        </ul>';
                    //如果是已分配的第三层数据
                }else{
                    //如果正好这一层的上级为个人中心或者关于（分配的人为默认字符非系统获取）
                    if(dataList[i].name === "个人中心" || dataList[i].name === "关于"){
                        if(subPopdoms2[j].name === "请求处理"){
                            thirdUserStr = '<span>全部非普通职工</span>';
                        }else{
                            thirdUserStr = '<span>全部职工</span>';
                        }
                        //否则正常获取人员
                    }else{
                        thirdUserStr = '<span>' +getUserStr(subPopdoms2[j].users)+ '</span>';
                    }
                    //填入后面的第二级名称（三级为空）、描述、以及已拥有权限者
                    listStr +=  '        <ul>' +
                        '           <li style="width: 20%">' +
                        '              <span>' + subPopdoms2[j].name + '</span>' +
                        '           </li>' +
                        '           <li style="width: 16%"></li>' +
                        '           <li style="width: 40%">' +
                        '               <span>' + subPopdoms2[j].desc + '</span>' +
                        '           </li>' +
                        '           <li style="width: 24%">' + thirdUserStr + '</li>' +
                        '        </ul>';
                }
            }
        }
        //补齐字符串
        listStr +=  '       </div>' +
            '   </li>' +
            '</ul>';
    }
    return listStr;
}

// creator: 张旭博，2023-09-05 04:35:36， 获取人员列表
function getUserStr(userArr){
    var userStr = '';
    if(userArr){
        for(var i = 0 ;i<userArr.length;i++){
            var title= '部门：'+chargeNull(userArr[i].departName) + ' ； 职位：'+chargeNull(userArr[i].postName);
            if(i === 0){
                userStr += '<a class="tooltip-show" data-toggle="tooltip" title="' + title + '">' + userArr[i].userName + '</a>';
            }else{
                userStr += '、<a class="tooltip-show" data-toggle="tooltip" title="' + title + '">' + userArr[i].userName + '</a>';
            }

        }
        return userStr;
    }else{
        return '';
    }



}

// creator: 张旭博，2023-09-05 04:35:49， 去空操作
function chargeNull(value) {
    return value === null?'--': value;
}

// ------------- 职工权限部分接口（包括当前权限查看和审批设置查看）----------------
// creator: 张旭博，2023-09-06 10:34:17， 获取职工权限列表（当前权限查看和审批设置查看）
function getWorkerSetting(curType) {
    var departName = $("#departName").val() ;
    var userName = $("#userName").val() ;
    if(curType == 0){
        $.ajax({
            "url": "../approval/userPopedom.do" ,
            "data" :{ "userName": userName , "departName": departName } ,
            success:function (res) {
                var userList = res["users"] ;
                popmList = res["popedoms"] ; // 该机构拥有的全部模块
                var colNum = popmList.length ; $("#colspan").attr("colspan" , colNum) ;
                var w = 100*colNum ; $("#auth_1_21").css("width" , w+"px") ;
                var bool_e = charFirstPopm("e" , popmList) ;   var str_e = "<td>权限管理</td>" ;if(!bool_e){ str_e = "" ; }
                var bool_k = charFirstPopm("k" , popmList) ;   var str_k = "<td>总务管理</td>" ;if(!bool_k){ str_k = "" ; }
                var bool_l = charFirstPopm("l" , popmList) ;   var str_l = "<td>财务管理</td>" ;if(!bool_l){ str_l = "" ; }
                var bool_q = charFirstPopm("q" , popmList) ;   var str_q = "<td>销售管理</td>" ;if(!bool_q){ str_q = "" ; }
                var bool_u = charFirstPopm("u" , popmList) ;   var str_u = "<td>生产管理</td>" ;if(!bool_u){ str_u = "" ; }
                var bool_p = charFirstPopm("p" , popmList) ;   var str_p = "<td>商品管理</td>" ;if(!bool_p){ str_p= "" ; }
                var bool_o = charFirstPopm("o" , popmList) ;   var str_o = "<td>物料管理</td>" ;if(!bool_o){ str_o = "" ; }
                var bool_t = charFirstPopm("t" , popmList) ;   var str_t = "<td>项目管理</td>" ;if(!bool_t){ str_t = "" ; }
                var bool_r = charFirstPopm("r" , popmList) ;   var str_r = "<td>文件与资料</td>" ;if(!bool_r){ str_r = "" ; }
                var bool_m = charFirstPopm("m" , popmList) ;   var str_m = "<td>个人中心</td>" ;if(!bool_m){ str_m = "" ; }
                var bool_h = charFirstPopm("h" , popmList) ;   var str_h = "<td>持续改进</td>" ;if(!bool_h){ str_h = "" ; }
                var bool_j = charFirstPopm("j" , popmList) ;   var str_j = "<td>备忘与日程</td>" ;if(!bool_j){ str_j = "" ; }
                var bool_mb = charFirstPopm("mb" , popmList, 1) ;   var str_mb = "<td>高管管理</td>" ;  if(!bool_mb){ str_mb = "" ; }
                var bool_db = charFirstPopm("db" , popmList, 1) ;   var str_db = "<td>日常事务</td>" ;  if(!bool_db){ str_db = "" ; }
                var bool_fc = charFirstPopm("fc" , popmList, 1) ;   var str_fc = "<td>投诉录入</td>" ;  if(!bool_fc){ str_fc = "" ; }
                var bool_gb = charFirstPopm("gb" , popmList, 1) ;   var str_gb = "<td>回款录入</td>" ;  if(!bool_gb){ str_gb = "" ; }
                var bool_lt = charFirstPopm("lt" , popmList, 1) ;   var str_lt = "<td>常规借款</td>" ;  if(!bool_lt){ str_lt = "" ; }
                var bool_n = charFirstPopm("n" , popmList) ;   var str_n = "<td>参考资料</td>" ;if(!bool_n){ str_n = "" ; }
                var bool_a = charFirstPopm("a" , popmList) ;   var str_a = "<td>薪资宝</td>" ;if(!bool_a){ str_a = "" ; }
                var bool_fb = charFirstPopm("fb" , popmList , 1) ;   var str_fb = "<td>投诉管理</td>" ;if(!bool_fb){ str_fb = "" ; }
                var bool_g = charFirstPopm("g" , popmList) ;   var str_g = "<td>汽车管理</td>" ;if(!bool_g){ str_g = "" ; }
                var bool_s = charFirstPopm("s" , popmList) ;   var str_s = "<td>会计管理</td>" ;if(!bool_s){ str_s = "" ; }
                var bool_v = charFirstPopm("v" , popmList) ;   var str_v = "<td>质量管理</td>" ;if(!bool_v){ str_v = "" ; }
                var bool_w = charFirstPopm("w" , popmList) ;   var str_w = "<td>技术管理</td>" ;if(!bool_w){ str_w = "" ; }
                var bool_x = charFirstPopm("x" , popmList) ;   var str_x = "<td>仓库管理</td>" ;if(!bool_x){ str_x = "" ; }
                var bool_y = charFirstPopm("y" , popmList) ;   var str_y = "<td>物流管理</td>" ;if(!bool_y){ str_y = "" ; }

                var modelStr = str_e + str_k + str_l + str_q + str_u + str_p + str_o + str_t + str_r + str_m +
                    str_h + str_j + str_mb + str_db + str_fc + str_gb + str_lt + str_n +
                    str_a + str_fb + str_g + str_s + str_v+ str_w+ str_x+str_y ;
                $("#modelTTl_2").html(modelStr) ;
                var str = "" , str1 = "";
                if(userList && userList.length > 0){
                    //  e-权限管理；k-总务管理；l-财务管理；q-销售管理；u-生产管理；p-商品管理；o-物料管理；t-项目管理；r-资源中心；m-个人中心；n-关于
                    for (var i = 0 ; i < userList.length ; i++){
                        var authList = userList[i]["userPopedomHashSet"] ;
                        var str_ea = "" ,  str_ka = "" , str_la = "" , str_qa = "" , str_ua = "" , str_pa = "" , str_oa = "" , str_ta = "" , str_ra = "" ,  str_ma = "" ,  str_na = "" ,
                            str_aa = "" , str_fb_ = "" ,str_ga = "" , str_sa = "" , str_va = "" , str_wa = "" , str_xa = "" , str_ya = ""
                            , str_ha = "" , str_ja = "" , str_mb_ = "" , str_db_ = "" , str_fc_ = "" , str_gb_ = "" , str_lt_ = "" ;
                        var bool_ea = charFirstPopm("e" , authList) ; if(bool_e){ if(bool_ea){ str_ea = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ea\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ea = "<td></td>" ; } }
                        var bool_ka = charFirstPopm("k" , authList) ; if(bool_k){ if(bool_ka){ str_ka = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ka\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ka = "<td></td>" ; } }
                        var bool_la = charFirstPopm("l" , authList) ; if(bool_l){ if(bool_la){ str_la = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"la\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_la = "<td></td>" ; } }
                        var bool_qa = charFirstPopm("q" , authList) ; if(bool_q){ if(bool_qa){ str_qa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"qa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_qa = "<td></td>" ; } }
                        var bool_ua = charFirstPopm("u" , authList) ; if(bool_u){ if(bool_ua){ str_ua = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ua\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ua = "<td></td>" ; } }
                        var bool_pa = charFirstPopm("p" , authList) ; if(bool_p){ if(bool_pa){ str_pa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"pa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_pa = "<td></td>" ; } }
                        var bool_oa = charFirstPopm("o" , authList) ; if(bool_o){ if(bool_oa){ str_oa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"oa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_oa = "<td></td>" ; } }
                        var bool_ta = charFirstPopm("t" , authList) ; if(bool_t){ if(bool_ta){ str_ta = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ta\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ta = "<td></td>" ; } }
                        var bool_ra = charFirstPopm("r" , authList) ; if(bool_r){ if(bool_ra){ str_ra = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ra\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ra = "<td></td>" ; } }
                        var bool_ma = charFirstPopm("m" , authList) ; if(bool_m){ if(bool_ma){ str_ma = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ma\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ma = "<td></td>" ; } }

                        var bool_ha = charFirstPopm("h" , authList) ; if(bool_h){ if(bool_ha){ str_ha = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ma\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ha = "<td></td>" ; } }
                        var bool_ja = charFirstPopm("j" , authList) ; if(bool_j){ if(bool_ja){ str_ja = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ma\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ja = "<td></td>" ; } }
                        var bool_mb_ = charFirstPopm("mb" , authList , 1) ; if(bool_mb){ if(bool_mb_){ str_mb_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_mb_ = "<td></td>" ; } }
                        var bool_db_ = charFirstPopm("db" , authList , 1) ; if(bool_db){ if(bool_db_){ str_db_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_db_ = "<td></td>" ; } }
                        var bool_fc_ = charFirstPopm("fc" , authList , 1) ; if(bool_fc){ if(bool_fc_){ str_fc_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_fc_ = "<td></td>" ; } }
                        var bool_gb_ = charFirstPopm("gb" , authList , 1) ; if(bool_gb){ if(bool_gb_){ str_gb_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_gb_ = "<td></td>" ; } }
                        var bool_lt_ = charFirstPopm("lt" , authList , 1) ; if(bool_lt){ if(bool_lt_){ str_lt_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_lt_ = "<td></td>" ; } }

                        var bool_na = charFirstPopm("n" , authList) ; if(bool_n){ if(bool_na){ str_na = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"na\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_na = "<td></td>" ; } }
                        var bool_aa = charFirstPopm("a" , authList) ; if(bool_a){ if(bool_aa){ str_aa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ar\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_aa = "<td></td>" ; } }
                        var bool_fb_ = charFirstPopm("fb" , authList , 1) ; if(bool_fb){ if(bool_fb_){ str_fb_ = "<td><i class='fa fa-check'></i></td>" ;}else{ str_fa = "<td></td>" ; } }
                        var bool_ga = charFirstPopm("g" , authList) ; if(bool_g){ if(bool_ga){ str_ga = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ga\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ga = "<td></td>" ; } }
                        var bool_sa = charFirstPopm("s" , authList) ; if(bool_s){ if(bool_sa){ str_sa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"sa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_sa = "<td></td>" ; } }
                        var bool_va = charFirstPopm("v" , authList) ; if(bool_v){ if(bool_va){ str_va = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"va\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_va = "<td></td>" ; } }
                        var bool_wa = charFirstPopm("w" , authList) ; if(bool_w){ if(bool_wa){ str_wa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"wa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_wa = "<td></td>" ; } }
                        var bool_xa = charFirstPopm("x" , authList) ; if(bool_x){ if(bool_xa){ str_xa = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"xa\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_xa = "<td></td>" ; } }
                        var bool_ya = charFirstPopm("y" , authList) ; if(bool_y){ if(bool_ya){ str_ya = "<td class='ty-td-control'><span class='ty-color-blue' onclick='look(\"ya\" ,\""+ userList[i]["userName"] +"\", $(this))'>查看</span></td>" ;}else{ str_ya = "<td></td>" ; } }

                        str1 += " <tr>" +
                            "<td>"+ userList[i]["departName"]+"</td>" +
                            "<td>"+ userList[i]["userName"] +"</td>" +
                            "</tr>" ;
                        str += " <tr authinfo = '"+ JSON.stringify(authList) +"'>" +
                            str_ea + str_ka + str_la + str_qa + str_ua + str_pa + str_oa + str_ta + str_ra + str_ma +
                            str_ha + str_ja + str_mb_ + str_db_ + str_fc_ + str_gb_ + str_lt_ +
                            str_na + str_aa +str_fb_ +str_ga+ str_sa + str_va + str_wa +str_xa + str_ya +
                            "</tr>"
                    }
                }
                $("#authList_1_1").children(":gt(0)").remove(); $("#authList_1_1").append(str1) ;
                $("#authList_1_2").children(":gt(1)").remove(); $("#authList_1_2").append(str) ;
            }
        }) ;
    }else{
        $.ajax({
            "url": "../approval/userApproval.do" ,
            "data" :{ "userName": userName , "departName": departName } ,
            success:function (res) {
                var list = res["data"] ;
                var str = "" ;
                if(list && list.length > 0){
                    for(var i = 0 ; i < list.length ; i++){
                        var authInfo = list[i]["approvalMap"] ;
                        str += "<tr>" +
                            "<td>"+ (list[i]["departName"] || "") +"</td>" +
                            "<td>"+ list[i]["userName"] +"</td>" ;
                        if(authInfo["outTimeApproval"]){ str += "<td>"+ authInfo["outTimeApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 加班申请
                        if(authInfo["leaveApproval"]){ str += "<td>"+ authInfo["leaveApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 请假申请
                        if(authInfo["reimburseApproval"]){ str += "<td>"+ authInfo["reimburseApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 报销申请
                        str += "<td></td>" ; // 职工档案修改
                        str += "<td></td>" ; // 岗位设置修改
                        if(authInfo["approvalApproval"]){ str += "<td>"+ authInfo["approvalApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 审批设置修改
                        if(authInfo["projectApproval"]){ str += "<td>"+ authInfo["projectApproval"] +"</td>" ; }else{ str += "<td></td>" ; } // 新项目立项
                        if(authInfo["projectDevelopment"]){ str += "<td>"+ authInfo["projectDevelopment"] +"</td>" ; }else{ str += "<td></td>" ; } // 新项目开发
                        str += "</tr>" ;
                    }
                }
                $("#authList_2").children(":gt(0)").remove() ;
                $("#authList_2").append(str) ;
            }
        }) ;
    }

}

/*  updator hxz  2018-5-12  */
var popmList = [] ; // 存放全部的菜单
function look(type , userName , _this){
    var trObj = _this.parent().parent() ;
    var authList = trObj.attr("authinfo") ;
    authList = JSON.parse(authList) ;
    var str = "" ;
    $("#detailsName").html(userName) ;
    var pidInfo = getInfoByMid(type , popmList) ; // 一级菜单的信息
    $("#detailsModel").html(pidInfo["name"]) ;
    $("#ttl_popm").html(""); $("#check_popm").html("") ;
    var modelList = getModelpopmByPid(type , popmList) ; // 根据一级菜单找二级菜单
    if(modelList && modelList.length > 0){
        for(var i = 0 ; i < modelList.length ; i++){
            var mid = modelList[i]["mid"] ;
            var _bool = getInfoByMid(mid , authList) ;
            if(_bool){ // 有该权限
                var str1 = "<td>"+ modelList[i]["name"] +"</td>" ;
                var str2 = "<td><i class='fa fa-check'></i></td>" ;
                $("#ttl_popm").append(str1) ; $("#check_popm").append(str2) ;
            }

        }
    }
    bounce.show($("#details"));
}

/* creator : 侯杏哲 2018-04-09 工具方法- 根据 pid 值的 获取该模块全部子菜单  */
function getModelpopmByPid(pid , popm){
    if(popm && popm.length > 0){
        for(var i= 0 ; i < popm.length ; i++){
            if(popm[i]["mid"] == pid){
                return popm[i]["subPopdoms"] ; // 只针对二级菜单
            }
        }
    }
}
/* creator : 侯杏哲 2018-04-09 工具方法- 根据 mid 值的 获取全部信息  */
function getInfoByMid( mid , arr ) {
    if( arr && arr.length > 0 ){
        for(var i = 0 ; i < arr.length ; i++){
            var _mid = arr[i]["mid"] ;
            if( _mid == mid ){
                return arr[i] ;
            }
        }
    }
    return false ;
}
/* creator :hxz 2018-05-11 工具方法 - 根据首字符判断 有没有一级菜单的权限 */
function charFirstPopm(type , arr , isAll){
    if( arr && arr.length > 0 ){
        for(var i = 0 ; i < arr.length ; i++){
            var _mid = arr[i]["mid"].substr(0,1) ; // 默认首字母
            if(isAll){ // 校验全部名称
                _mid = arr[i]["mid"] ;
            }
            if( _mid == type ){
                return true ;
            }
        }
    }
    return false ;
}

// creator : 侯杏哲 2018-06-07  设置表头大小
function setBiao() {
    setTimeout(function(){
        if(curType == 1){
            var w = $("#biao").width() +2 ;
            var h = $("#biao").height()+2 ;
            $(".biaotou").css({ "border-left": w + "px rgba(200,200,200,0.5) solid" , "width": w + "px" , "border-top": h + "px rgba(0,0,0,0) solid" }) ;
        }
    },500) ;

}

// ------------- 审批设置部分接口----------------

// ------------- 职工批量导入部分----------------

/* creator：张旭博，2017-11-02 17:26:51，筛选 */
function defineGetList(pageNumber,quantum,isDuty) {
    $.ajax({
        url:"../general/screen.do" ,//isDuty （1表示在职，2表示离职）、quantum （每页多少条）、pageNumber（当前页）
        data: {
            isDuty: isDuty,
            quantum: quantum,
            pageNumber: pageNumber
        },
        success:function( data ){
            var users = data["users"];
            var totalPage = data["totalPage"];//总页数
            var cur = data["pageNumber"];//当前页
            //设置分页
            $("#ye_employee").html("");
            var jsonStr = JSON.stringify({"isDuty":1}) ;
            setPage( $("#ye_employee") , cur ,  totalPage , "employee" ,jsonStr );
            var onPostListStr = ''
            for(var i=0;i<users.length;i++){
                var orgNameStr = `<td> ${handleNull(users[i].orgName)} </td>`;
                var sexStr = '',degreeStr = '', leaderStr = '';
                sexStr = chargeSex(users[i].gender);
                degreeStr = chargeDegree(users[i].degree);
                leaderStr = users[i].roleCode == 'super' ? '':users[i].leaderName;
                onPostListStr   +=  '<tr data-id="'+users[i].userID+'">'+
                    '   <td>'+users[i].userName+'</td>'+    //姓名
                    '   <td>'+sexStr+'</td>'+      //性别
                    '   <td>'+users[i].mobile+'</td>'+ orgNameStr+      //手机号
                    '   <td>'+users[i].departName+'</td>'+  //部门
                    '   <td>'+users[i].postName+'</td>'+    //职位
                    '   <td>'+leaderStr+'</td>'+  //直接上级
                    '   <td>'+degreeStr+'</td>'+      //最高学历
                    '</tr>';
            }
            $(".tbl_employeesList tbody").html(onPostListStr);
        }
    }) ;
}

// creator: 张旭博，2023-07-18 10:16:55， 转换性别
function chargeSex(sex) {
    switch(sex){
        case "1":
            return "男";
            break;
        case "0":
            return "女";
            break;
        default:
            return '';
    }
}
// creator: 李玉婷，2019-12-02 10:07:28，转换婚姻状况
function chargeMarry(marry) {
    switch(marry){
        case "1":
            return "未婚";
            break;
        case "0":
            return "已婚";
            break;
        default:
            return '';
    }
}
// creator: 张旭博，2018-05-11 10:19:31，学历转换
function chargeDegree(degreeCode) {
    var degree = '';
    switch (degreeCode){
        case 1:
            degree =  "研究生";
            break;
        case 2:
            degree =  "本科";
            break;
        case 3:
            degree =  "大专";
            break;
        case 4:
            degree =  "中专或高中";
            break;
        case 5:
            degree =  "其它";
            break;
    }
    return degree
}

// creator: 李玉婷，2020-11-02 10:15:50，工作特点转换
function changeWorkFuc(str) {
    var charact = '';
    switch (str) {
        case "general":
            charact = "不含销售工作与财会工作";
            break;
        case "finance":
            charact = "包含财务工作";
            break;
        case "sale":
            charact = "含有销售工作";
            break;
        case "accounting":
            charact = "包含会计工作";
            break;
    }
    return charact;
}

//------------------批量导入------------------
// creator: 张旭博，2024-01-22 04:28:45， 下一步
function sureStepNext() {
    let list = [];
    let userList = $(".importCon1").data("trueUserList");
    $(".importCon1 table tbody tr").each(function(){
        let item = JSON.parse($(this).find(".hd").html());
        list.push(item);
    });
    for (let item of userList) {
        list.push({
            userName: item.userName,
            mobile: item.mobile
        });
    }
    let sum = list.length;
    list = JSON.stringify(list);
    let json = {
        usersList: list,
        importSum: sum
    }
    saveImportList(json);
}

function stepNext() {
    let list = [];
    $(".importCon1 table tbody tr").each(function(){
        let item = JSON.parse($(this).find(".hd").html());
        list.push({item});
    });
    let sum = list.length;
    list = JSON.stringify(list);
    let param = {
        "usersList": list,
        "importSum": sum
    }
    // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
    $.ajax({
        url: "../userImport/allImportUserEnter.do",
        data: param,
        success: function (data) {
            var noSaveSum = data.falseImportSum;
            if (noSaveSum > 0) {
                $("#nextStep #noSaveMbSum").html(noSaveSum);
                $(".safeCondition").show();
            } else {
                $(".safeCondition").hide();
            }
            bounce.show($("#nextStep"));
        }
    });
}

// creator: 张旭博，2024-01-22 04:30:56， 批量导入尚未完成是否继续
function judgeImport() {
    var flag = $(".unfinishedForm .fa-circle").data("type")
    if (flag == '1') {
        jumpPage('importInfo', ()=> {
            $(".page[page='importInfo'] .importCon2").show().siblings().hide();
            bounce.cancel();
            $(".importOpertion").siblings("button.ty-btn").hide()
            var res = $("#importNotCompleted").data("userData");
            getImportUserList(res);
        })
    } else if (flag == '0') {
        bounce.cancel();
        turnCancel(1);
    } else {
        layer.msg("请选择上次的批量导入尚未完成是否继续！");
    }
}

// creator: 张旭博，2024-01-22 04:33:45， 点击批量导入-按钮(判断是否有未完成的导入)
function leadingStart(){
    var userType = chargeRole("超管")
    if(!userType){ // || hasAuthority(10) === true
        $.ajax({
            url:"../userImport/unfinishedImportUser.do",
            success:function (data) {
                var userList = data.userImportList;
                if (userList.length > 0) {
                    $('.unfinishedForm .fa').attr("class","fa fa-circle-o");
                    $('#importNotCompleted').data("userData",data);
                    bounce.show($('#importNotCompleted'));
                } else {
                    initLeading()
                    bounce.show($('#leading'));
                }
            }
        });
    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }
}


function initLeading() {
    $('#select_btn_1').val("");
    $('.userListUpload').remove();
    $('#leading .fileFullName').html("尚未选择文件");
    $('#sysUseUploadFile').html('')
    $('#sysUseUploadFile').Huploadify({
        auto:false ,
        fileTypeExts:'*.xls;*.xlsx;',
        multi:false,
        formData:{
            module: '职工导入',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:10240,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        itemTemplate: '<div id="${fileID}" class="uploadify-queue-item userListUpload" style="display: none">' +
            '<div class="uploadify_bottom"><div><a class="uploadbtn" href="javascript:void(0);"></a></div></div>' +
            '</div>',
        onUploadStart:function(){
        },
        onInit:function(){},
        onSelect:function (file) {
            $('.userListUpload:not(:last)').remove();
            $('#leading .fileFullName').html(file.name);
        },
        onUploadComplete:function(file,data){
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            var fileUid = data.fileUid
            let param =  {
                filePath: path
            };
            // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
            let storage = $(".part_own").data("storage")
            if (storage && storage.source === 1) {
                param.sonOid = storage.sonOid;
            }
            $.ajax({
                url: '../export/newImportUser.do',
                data: param,
                success: function (data) {
                    var status = data.status
                    let thisPage = $(".page[page='importInfo']")
                    if (status == '1') {
                        jumpPage('importInfo', ()=> {
                            thisPage.find("table tbody").html("");
                            // 取消删除程序
                            cancelFileDel({type: 'fileUid', fileUid: fileUid})
                            var userList = data.trueUserList;
                            var falseList = data.falseUserList;
                            $(".importCon1").data("trueUserList", userList);
                            bounce.cancel();
                            if (falseList && falseList.length > 0) {
                                loading.close() ;
                                var html = '';
                                for (let item of falseList) {
                                    let itemSimple = {
                                        userName: item.userName,
                                        mobile: item.mobile
                                    }
                                    html += `<tr data-info='${JSON.stringify(item)}'>
                                                <td>${handleNull(item.userName)}</td>
                                                <td>${handleNull(item.mobile)}</td>
                                                <td>
                                                    <span class="link-blue" onclick="initUpdate($(this))">修改</span>
                                                    <span class="link-red" onclick="initDel($(this))">删除</span>
                                                    <span class="hd">${JSON.stringify(itemSimple)}</span>
                                                </td>
                                            </tr>`
                                }

                                thisPage.find(".importCon1 table tbody").html(html);
                                thisPage.find(".importCon1 .initAll").html(data.importSum);
                                thisPage.find(".importCon1 .initWrong").html(data.falseImportSum);
                                thisPage.find(".importCon1").show().siblings().hide()

                            } else {
                                var importList = [];
                                for (let item of userList) {
                                    importList.push({
                                        userName: item.userName,
                                        mobile: item.mobile
                                    });
                                }
                                var json = {
                                    usersList: JSON.stringify(importList),
                                    importSum: data.importSum
                                }
                                saveImportList(json);
                            }
                        })
                    } else {
                        loading.close() ;
                        $('#select_btn_1').val("");
                        $('.userListUpload').remove();
                        $('#leading .fileFullName').html("尚未选择文件");
                        bounce_Fixed3.show($("#importantTip"));
                    }
                }
            })

        }
    });
}

// creator: 李玉婷，2020-08-28 08:55:00，导入确定
function sysUseImportOk() {
    if ($(".userListUpload").length <= 0) {
        layer.msg("您需选择一个文件后才能“导入”！")
    } else {
        loading.open();
        $(".userListUpload a").click();
    }
}

// creator: 张旭博，2024-01-23 08:50:14， 放弃
function clearNoSave() {
    $("#clearUser").data("type", 1);
    bounce.show($("#clearUser"));
}

// creator: 张旭博，2024-01-23 08:50:14， 放弃
function cancelSave() {
    $("#clearUser").data("type", 2);
    bounce.show($("#clearUser"));
}

// creator: 张旭博，2024-01-23 08:51:33， 修改
function initUpdate(selector) {
    var info = JSON.parse(selector.siblings(".hd").html());
    $("#updateUser").data("type", 1);
    $("#updateUser").data("obj", selector);
    $(".userForm .userName").val(info.userName);
    $(".userForm .userPhone").val(info.mobile);
    $(".userForm .userName").data("oldName",info.userName);
    $(".userForm .userPhone").data("oldMobile",info.mobile);
    $("#importUpdateUser").prop("disabled",true);
    bounce.show($("#updateUser"));
    setUpdateUserTimer();
}

// creator: 张旭博，2024-01-23 08:51:33， 删除
function initDel(selector) {
    $("#delUser").data("obj", selector);
    $("#delUser").data("type", '1');
    bounce.show($("#delUser"));
}

// creator: 张旭博，2024-01-23 08:51:33， 修改
function update(selector) {
    var id = selector.parents("tr").data("id");
    var userName = selector.parents("tr").find("td").eq(0).html();
    var mobile = selector.parents("tr").find("td").eq(1).html();
    $("#updateUser").data("obj", selector);
    $("#updateUser").data("id", id);
    $("#updateUser").data("type", 2);
    $(".userForm .userName").val(userName);
    $(".userForm .userPhone").val(mobile);
    $(".userForm .userName").data("oldName",userName);
    $(".userForm .userPhone").data("oldMobile",mobile);
    $("#importUpdateUser").prop("disabled",true);
    bounce.show($("#updateUser"));
    setUpdateUserTimer();
}

// creator: 张旭博，2024-01-23 08:51:33， 删除
function del(selector) {
    $("#delUser").data("obj", selector);
    $("#delUser").data("type", '2');
    bounce.show($("#delUser"));
}

// creator: 李玉婷，2020-08-30 07:17:44，删除职工
function delUserSure() {
    var obj = $("#delUser").data("obj");
    var type = $("#delUser").data("type");
    var trObj = obj.parents("tr");
    bounce.cancel();
    if (type == '1'){
        trObj.remove();
        var len = $(".importCon1 tbody tr").length;
        $(".importCon1 .initWrong").html(len);
    } else if(type == '2') {
        let param = {"id": trObj.data("id")};
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            "url": "../userImport/deleteImportUser.do",
            "data": param,
            success: function (res) {
                var status = res["status"];
                if (status == 1) {
                    resetImportUser();
                } else {
                    loading.close();
                    layer.msg("删除失败！");
                }
            },
            complete:function(){  }
        })
    }
}
// creator: 李玉婷，2020-11-12 09:27:09，修改材料定时器
function setUpdateUserTimer(){
    bounce.everyTime('0.5s','importUpdate',function(){
        var call = $("#updateUser .userPhone").val();
        var callOld = $("#updateUser .userPhone").data("oldMobile");
        var name = $("#updateUser .userName").val();
        var nameOld = $("#updateUser .userName").data("oldName");
        if(call != "" && name != "" && (call !== callOld || name !== nameOld)){
            $("#importUpdateUser").prop("disabled",false);
        }else{
            $("#importUpdateUser").prop("disabled",true);
        }
    });
}
// creator: 李玉婷，2020-08-27 09:58:54，修改职工信息
function updateUserInfo() {
    var call = $("#updateUser .userPhone").val();
    if (!testMobile(call)) {
        var str = '<p>您录入的手机号有误。</p><p>请确认！</p>';
        $("#iknowTip .iknowWord").html(str);
        bounce_Fixed3.show($("#iknowTip"))
    } else {
        var old = $("#updateUser .userPhone").data("oldMobile");
        var same = 0;
        var type = $("#updateUser").data("type");
        var name = '';
        if (type == 1) {
            $(".importCon1 table tbody tr").each(function () {
                var info = $(this).data("info");
                if (call == info.mobile && old != info.mobile){
                    same++;
                    name = info.userName;
                }
            });
        } else if (type == 2) {
            $(".importCon2 tbody tr").each(function () {
                var otherMb = $(this).find("td").eq(1).html();
                if (call == otherMb && old != otherMb){
                    same++;
                    name = $(this).find("td").eq(0).html();
                }
            });
        }
        if(same >0) {
            var str = '<p>您录入的手机号与本次导入的'+ name +'手机号相同。</p><p>请确认！</p>';
            $("#iknowTip .iknowWord").html(str);
            bounce_Fixed3.show($("#iknowTip"));
        }else {
            $("#checkTip").data("name", "updateImportStuff");
            mobileCheckImport(call, name);
        }
    }
}
// creator: 李玉婷，2020-10-21 11:21:59，修改职工信息确定
function updateImportUserSure(){
    var obj = $("#updateUser").data("obj");
    var type = $("#updateUser").data("type");
    var trObj = obj.parents("tr");
    var call = $("#updateUser .userPhone").val();
    var name = $("#updateUser .userName").val();
    if (type == 1) {
        var trData = JSON.parse(obj.siblings(".hd").html());
        trObj.find("td").eq(0).html(name);
        trObj.find("td").eq(1).html(call);
        trData.userName = name;
        trData.mobile = call;
        trObj.data("info", trData);
        bounce.cancel();
        common_bounce.cancel();
    } else if (type == 2) {
        let param = {
            "id": trObj.data("id"),
            "mobile": call,
            "userName": name
        };
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            "url": "../userImport/updateImportUserMobile.do",
            "data": param,
            success: function (res) {
                var status = res["status"];
                if (status == 1) {
                    bounce.cancel();
                    common_bounce.cancel();
                    trObj.find("td").eq(0).html(name);
                    trObj.find("td").eq(1).html(call);
                } else if (status == -1) {
                    layer.msg("和本次导入的其他手机号重复！");
                } else {
                    layer.msg("修改失败！");
                }
            }
        })
    }
}
// creator: 李玉婷，2020-05-28 11:59:40，查重后操作
function mobileCheckImport(phone, name) {
    mobileCheckRepeatImport(phone, name).then((data) => {
        var status = data["status"]; // state 1-查重通过  0-与在职人员相同  2- 与离职人员相同 3- 与历史记录相同 4-与冻结一致
        if (status === 1) {
            updateImportUserSure();
        } else if (status === 2) {
            var str =
                '<p>您录入的手机号与公司已离职者'+ data.name +'手机号相同。</p>' ;
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").hide();$("#tjSure").attr("onclick", "updateImportUserSure()");
            $(".canAllow").show();
        } else if (status === 3) {
            var str =
                '<p>您录入的手机号与公司'+ data.name +'曾用过的手机号相同。</p>' ;
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").hide();$("#tjSure").attr("onclick", "updateImportUserSure()");
            $(".canAllow").show();
        }else if(status === 0 || status === 4) {
            var str =
                '<p>您录入的手机号与公司'+ data.name +'手机号相同。</p>'+
                '<p>请确认！</p>';
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").show();$(".canAllow").hide();
        }
    }, (err) => {
    })
}
// creator: 李玉婷，2020-11-22 10:25:36，输入手机号 查重接口
function mobileCheckRepeatImport(phone, name){
    return new Promise( (resolve, reject) => {
        var param = {
            "mobile": phone,
            "userName": name
        };
        // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            "url": "../userImport/updateFalseUserEnter.do",
            "data": param,
            "async": false,
            success: function (res) {
                resolve(res);
            },
            error: function () {
                reject('')
            }
        })
    });
}

// creator: 李玉婷，2020-12-14 13:58:17，最后保存确定
function lastSave(){
    $.ajax({
        url: "../userImport/completeImportUser.do",
        success: function (data) {
            var state = data.status;
            if (state == 1) {
                bounce.cancel();
                back()
            } else {
                layer.msg("保存失败！");
            }
        }
    })
}
// creator: 李玉婷，2020-08-27 09:49:20，修改 - 一键清空
function clearTxt(obj) {
    obj.prev().val("");
}
// creator: 李玉婷，2020-10-21 16:42:51，下一步-数据保存
function saveImportList(userData) {
    // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {userData.sonOid = storage.sonOid;}
    $.ajax({
        url: "../userImport/saveImportUser.do",
        data: userData,
        success: function (data) {
            $(".page[page='importInfo'] .importCon2").show().siblings().hide();
            bounce.cancel();
            getImportUserList(data);
        }
    });
}

function clearUser() {
    var type = $("#clearUser").data("type");
    bounce.cancel();
    if (type == '1') {
        back()
    } else {
        turnCancel(2)
    }
}
// creator: 李玉婷，2020-10-18 14:06:42，放弃
function turnCancel(type){
    $.ajax({
        url: "../userImport/giveUpImportUser.do",
        success: function (data) {
            var state = data.status;
            if (state == '1')
                if (type == '1') {
                    initLeading()
                    bounce.show($('#leading'));
                }else{
                    back()
                }

        }
    })
}

// creator:hxz 2018-08-07 获取导入的员工列表
function getImportUserList(data) {
    var staffUserList = data["userImportList"];
    var manageList = data["manageList"];
    var buttonState = data["buttonState"]; // 确定按钮状态1- 变亮 0- 置灰
    if (buttonState == 1) {
        $("#ok").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "confirmOrgTip()");
    } else {
        $("#ok").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
    }
    $(".importCon2 .importSum").html(data.importSum || 0);
    $(".importCon2 .saveSum").html(data.tureImportSum || 0);
    var str = "";
    if (staffUserList && staffUserList.length > 0) {
        for (var i = 0; i < staffUserList.length; i++) {
            var isP = staffUserList[i]["ordinaryEmployees"]; // 是否普通员工
            var leader = handleNull(staffUserList[i]["leader"]); // 直接上级id
            var leaderName = handleNull(staffUserList[i]["leaderName"]); // 直接上级name
            var userID = staffUserList[i]["id"]; // 用户id
            var managerCode = staffUserList[i]["managerCode"];  // 所属高管
            var strP = setP(isP, userID);
            var strGroup = setGroup(managerCode, manageList, userID);
            str += "<tr data-id='"+ staffUserList[i]["id"] +"'>" +
                "    <td>" + staffUserList[i]["userName"] + "</td>" +
                "    <td>" + staffUserList[i]["mobile"] + "</td>" +
                "    <td>" + strP +
                "    </td>" +
                "    <td>" +
                "        <div class=\"z\" onclick='getOption($(this) ,1 , event)'> " +
                "           <input type='text' class='name' value='" + leaderName + "'>" +
                "           <input type='hidden' class='id' value='" + leader + "'>" +
                "           <input type='hidden' class='type' value='1'>" +
                "           <input type='hidden' class='userID' value='" + userID + "'>" +
                "           <div class='options'></div>" +
                "        </div>"+
                "    </td>" +
                "    <td>" + strGroup +
                "    </td>" +
                "    <td>" +
                "       <span class='link-blue' onclick='update($(this))'>修改</span>"+
                "       <span class='link-red' onclick='del($(this))'>删除</span>"+
                "    </td>" +
                "</tr>"
        }
    }
    $(".importCon2 tbody").html(str);
}
// creator:hxz 2018-08-13  生成是不是普通员工的下拉框
function setP(type, userID) {
    var na = "有", val = "0";
    if (type == 1) {
        na = "无";
        val = "1";
    }
    var str = "<div class=\"z\" onclick='getOption($(this) ,0 , event)'>" +
        "           <input type='text' class='name' value='" + na + "' readonly>" +
        "           <input type='hidden' class='id' value='" + val + "'>" +
        "           <input type='hidden' class='type' value='0'>" +
        "           <input type='hidden' class='userID' value='" + userID + "'>" +
        "           <div class='options'>" +
        "               <option onclick='selectThis($(this) , event)' value=\"1\" >无</option>" +
        "               <option onclick='selectThis($(this) , event)' value=\"0\">有</option>" +
        "           </div>" +
        "        </div>";
    return str;
}
// creator:hxz 2018-08-13  生成所属高管的下拉框
function setGroup(manageCode, manageList, userID) {
    var str = "<select class=\"s\" userID='" + userID + "' onchange='selectThis($(this))'><option value='0' >请选择</option>";
    if (manageList && manageList.length > 0) {
        for (var i = 0; i < manageList.length; i++) {
            var m = manageList[i]["userID"] + '-' + manageList[i]["roleCode"];
            var charact = changeWorkFuc(manageList[i].roleCode);
            if (manageCode == manageList[i]["roleCode"]) {
                str += "<option value='" + m + "' selected>" + charact + "</option>";
            } else {
                str += "<option value='" + m + "'>" + charact + "</option>";
            }
        }
    }
    str += "</select>";
    return str;
}
// creator:hxz 2018-08-13  获取直系上级的下拉列表
var timer = 0; // 直系上级 匹配的定时器
function getOption(obj, type, event) {
    event.stopPropagation();
    $(".options").hide();
    obj.find(".options").show();
    if (type == 0) { // 是否直接普通员工

    } else { // 选择直接上级的下拉框
        var userID = obj.children("input.userID").val();
        var name = obj.children(".name").val();
        let param = {"id": userID};
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            //"url": "../approval/getOptionalUser.do",
            "url": "../userImport/selectOptionalLeader.do",
            "data": param,
            success: function (res) {
                var userList = res;
                var str = "<option value='' style='display: none'></option>";
                if (userList && userList.length > 0) {
                    for (var i = 0; i < userList.length; i++) {
                        var _na = userList[i]["userName"] + " - - " + userList[i]["mobile"];
                        var index = _na.indexOf(name);
                        if (index != -1) {
                            obj.siblings(".id").val(userList[i]["id"]);
                            str += "<option onclick='selectThis($(this) , event)' value='" + userList[i]["id"] + "' data-flag='"+ userList[i]["status"] +"'>" + _na + "</option>";
                        } else {
                            str += "<option onclick='selectThis($(this) , event)' value='" + userList[i]["id"] + "' data-flag='"+ userList[i]["status"] +"' style='display: none'>" + _na + "</option>";
                        }
                    }
                }
                obj.find(".options").html(str);
            }
        });
        if (timer) {
            clearInterval(timer);
        }
        timer = setInterval(function () {
            match(obj.children(".name"))
        }, 300)
    }

}

// creator:hxz 2018-08-13  直系上级输入匹配
function match(obj) {
    var name = obj.val();
    if ($.trim(name) == "") {
        obj.siblings(".options").children().show();
    } else {
        obj.siblings(".options").children().each(function () {
            var _na = $(this).html();
            var index = _na.indexOf(name);
            if (index != -1) {
                $(this).show();
                if (_na == name) {
                    obj.siblings(".id").val($(this).val());
                }
            } else {
                $(this).hide();
            }
        })
    }
}
// creator:hxz 2018-08-13  页面中全部选择操作
function selectThis(obj, event) {
    var data = {};
    var userID = "";
    if (event) { // 设置普通员工和 直接上级
        event.stopPropagation();
        var val = obj.val();
        var name = obj.html();
        var ops = obj.parent(".options");
        var type = ops.siblings(".type").val(); // 0 - 是否普通员工 ， 1 - 直系上级
        userID = ops.siblings(".userID").val();
        data["leaderSorce"] = obj.data("flag");
        if (type == 1) {
            data["leaderId"] = val;
        } else {
            data["ordinaryEmployees"] = val;
        }
    } else { // 设置高管
        var val = obj.val();
        var arr = val.split('-');
        userID = obj.attr("userID");
        if (arr[0] != "null" && arr[0] != "") data["manageId"] = arr[0];
        data["manageCode"] = arr[1];
    }
    data["userId"] = userID;
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {data.sonOid = storage.sonOid;}
    $.ajax({
        "url": "../userImport/updateImportUser.do",
        "data": data,
        success: function (res) {
            var status = res["status"];
            var buttonState = res["buttonState"]; // 确定按钮状态1- 变亮 0- 置灰
            if (buttonState == 1) {
                $("#ok").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "confirmOrgTip()");
            } else {
                $("#ok").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
            }
            if (event) {
                if (status == 1) { // 设置没问题
                    var type = ops.siblings(".type").val();
                    ops.siblings(".id").val(val);
                    ops.siblings(".name").val(name);
                    if (type == 0) {
                        resetImportUser();
                    } else {
                        loading.close();
                    }
                } else if (status == 2) {
                    loading.close();
                    layer.msg("下级有员工，不能变成普通员工,设置失败！");
                } else {
                    loading.close();
                    layer.msg("设置失败！");
                }
                clearInterval(timer);
                ops.hide();
            } else {
                loading.close();
                if (status == 1) {
                } else {
                    layer.msg("设置所属高管失败！");
                }
            }
        },
        complete:function(){  }
    })
}
// creator: 李玉婷，2020-12-11 11:12:02，删除、是否有下属更改对直接上级影响
function resetImportUser() {
    // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
    let param = {};
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
    $.ajax({
        url:"../userImport/unfinishedImportUser.do",
        data: param,
        success:function (data) {
            getImportUserList(data);
        }
    });
}
function confirmOrgTip() {
    var sum = $(".importCon2 table tbody tr").length;
    var importNum = $(".importCon2 .importSum").html();
    $("#lastSave #saveSum").html(importNum);
    $("#lastSave #saveAble").html(sum);
    bounce.show($("#lastSave"));
}