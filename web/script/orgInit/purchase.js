var user = null;
var contractDel = null;
var contractInfo = null;
var editContractObj = null;
// 初始化 三级弹窗
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
var bounce_Fixed4 = new Bounce(".bounce_Fixed4");
var bounce_Fixed5 = new Bounce(".bounce_Fixed5");
$(function (){
    console.log('oid', sphdSocket.user.oid)
    // 供应关系部分逻辑（确定定点信息弹窗选择部分）
    $(".mtAndSupplier input:radio").on("click", function (){
        let thisName = $(this).attr("name")
        let thisValue = $(this).val()
        let thisChecked = $(this).is(":checked")
        if (thisName === 'hasContact' && thisValue === '1') {
            setContractOption()
        }
        $(".hidePart").hide()
        let radioData = {}
        $(".mtAndSupplier input:radio").each(function (){
            let name = $(this).attr('name')
            radioData[name] = $(".mtAndSupplier input:radio[name='"+name+"']:checked").val()
        })
        if (thisName === 'priceStable' || thisName === 'materialInvoicable') {
            $('.mtAndSupplier .part_voiceType input:radio').prop('checked', false)
            $('.mtAndSupplier .part_price input:radio').prop('checked', false)
            $('.mtAndSupplier .part_price input:not(:radio)').val('')
            $('.mtAndSupplier .part_freight input:radio').prop('checked', false)
            $('.mtAndSupplier .part_sendHomePlace').hide()
            $('.mtAndSupplier .part_sendLongPlace').hide()
        }
        if (radioData.hasContact === '1') {
            $(".mtAndSupplier .part_contract").show()
        }
        if (radioData.priceStable === '2') {
            $(".mtAndSupplier .uncertain").show()
        }
        if (radioData.priceStable !== undefined && radioData.materialInvoicable !== undefined) {
            if (radioData.materialInvoicable === '1') {
                $('.mtAndSupplier .part_voiceType').show()
                if (radioData.materialInvoiceCategory !== undefined) {
                    $('.mtAndSupplier .part_price').show()
                    $('.mtAndSupplier .part_freight').show()
                }
            } else if (radioData.materialInvoicable === '0' || radioData.materialInvoicable === '2') {
                $('.mtAndSupplier .part_price').show()
                $('.mtAndSupplier .part_freight').show()
            }
        }
        if (radioData.priceStable === '1') {
            // 价格相对稳定
            if (radioData.materialInvoicable === '1') {
                if (radioData.materialInvoiceCategory === '1') {
                    $(".price_des").html('已约定的单价')
                    $(".mtAndSupplier .part_isTax").show()
                    $(".mtAndSupplier .part_taxRate").show()
                } else if (radioData.materialInvoiceCategory === '2') {
                    $(".price_des").html('已约定的<span class="ty-color-orange">开票</span>单价')
                }
            } else if (radioData.materialInvoicable === '0') {
                $(".price_des").html('已约定的<span class="ty-color-orange">不开票</span>单价')
            }
        } else if (radioData.priceStable === '2') {
            // 价格变动频繁
            if (radioData.materialInvoicable === '1') {
                if (radioData.materialInvoiceCategory === '1') {
                    $(".mtAndSupplier .part_isTax").show()
                    $(".mtAndSupplier .part_taxRate").show()
                    $(".price_des").html('参考单价')
                } else if (radioData.materialInvoiceCategory === '2') {
                    $(".price_des").html('参考单价(<span class="ty-color-orange">开票价</span>)')
                }
            } else if (radioData.materialInvoicable === '0') {
                $(".price_des").html('已约定的<span class="ty-color-orange">不开票</span>单价')
                $(".price_des").html('参考单价(<span class="ty-color-orange">不开票价</span>)')
            } else if (radioData.materialInvoicable === '2') {
                $(".price_des").html('参考单价')
            }
        }
        if (radioData.inclusiveFreight === '1') {
            $(".mtAndSupplier .part_sendHomePlace").show()
        } else if (radioData.inclusiveFreight === '2') {
            $(".mtAndSupplier .part_sendLongPlace").show()
        }
    })

    $(".page[page='main']").on('click', '[type="btn"]', function () {
        let name = $(this).attr("name")
        let to = $(this).attr('to')
        let jumpName = to + '_' +name

        if (jumpName === 'RAAMtPoint_edit') {
            let state1 = $(".tbl_purchase_item_edit tbody tr").eq(0).data("state")
            let state3 = $(".tbl_purchase_item_edit tbody tr").eq(2).data("state")
            if (state1 === 0) {
                layer.msg("请先把“材料供应商清单”编辑完！")
                return false
            }
            if (state3 === 0) {
                layer.msg("请先把“其他原辅材料清单”编辑完！")
                return false
            }
        }

        // 跳转页面
        jumpPage(jumpName, ()=> {
            let stepId = $(this).parents("tr").data("id")
            $("#home").data('mainname', to) // 存储跳转的页面名称
            $("#home").data('stepId', stepId) // 存储步骤id

            // 区分初始和点击完成后的不同提示
            let initInfo = JSON.parse($(this).parents("tr").find(".hd").html())
            if (initInfo.state === 0) {
                $(".page:visible .tipInit").show().siblings().hide()
                $(".page:visible .topRightBtn").show()
            } else if (initInfo.state === 1) {
                $(".page:visible .tipDone").show().siblings().hide()
                $(".page:visible .tipDone .doneTime").html(moment(initInfo.updateDate).format("YYYY-MM-DD HH:mm:ss"))
                $(".page:visible .topRightBtn").hide()
            }

            // 重置表单
            $(".page:visible input:radio").prop("checked", false)
            $(".page:visible .ty-searchInput").val('')

            // 获取页面数据
            switch (jumpName) {
                case 'mtSupplierList_edit':
                    // 获得供应商列表
                    getContractMes(1,20);
                    break
                case 'equiptSupplierList_edit':
                    // 获得供应商列表
                    getContractMes(1,20);
                    break
                case 'RAAMtList_edit':
                    $(".searchInput:visible").val('')
                    $(".catAll").data("noHandle", 0)
                    getMtList(1, 20)
                    break
                case 'RAAMtPoint_edit':
                    if (initInfo.state === 0) {
                        $(".page:visible").find(".pagePart[part='edit']").show().siblings(".pagePart").hide()
                        $(".catAll").data("noHandle", 0)
                        getMtList(1, 20, 1);
                    } else if (initInfo.state > 0) {
                        $(".page:visible").find(".pagePart[part='editDone']").show().siblings(".pagePart").hide()
                        getCollect()
                    }
                    break

            }
        })

    })

    $.ajax('../skl/getStockMode')
        .then(res => {
            let status = res.status
            $("body").data('whmode', status) //0-非智能 1-智能
        })
})

function getCollect() {
    $.ajax($.webRoot + '/mt/init/collect')
        .then(res => {
            let data = res
            $(".page[page='RAAMtPoint_edit']").data('collect', JSON.stringify(data))
            updateCollectPage(data)
        })
}

function updateCollectPage(data) {
    let lastEditData = data.lastEditData?data.lastEditData[0]:null // 材料定点信息的最后编辑数据
    let mtList1 = data.mtList1 || [] // 待确认定点信息的材料
    let mtList4 = data.mtList4 || [] // 已确认定点信息可修改的材料
    let newMtBases = data.newMtBases || [] // 编辑之后又新增了的材料
    let changeMtBases = data.changeMtBases || [] // 已编辑数据基本信息发生变动的材料
    let suppliers = data.suppliers || [] // 编辑之后新增了的供应商
    let changeSuppliers = data.changeSuppliers || [] // 编辑之后信息发生变动的供应商
    let thisPage = $(".page:visible")
    let str = ''
    switch(thisPage.attr('page').slice(11)) {
        case 'edit':
            let page = $(".page[page='RAAMtPoint_edit']")
            let editTimeStr = lastEditData?`<b class="edited_createName">${lastEditData.create_name}</b> 对材料定点信息的最后编辑时间为 <b class="edited_createDate">${moment(lastEditData.create_date).format('YYYY-MM-DD HH:mm:ss')}</b>。`:''
            page.find('.edited_mtList1_num').html(mtList1.length + '<span class="hd">'+JSON.stringify(mtList1)+'</span>')
            page.find('.edited_mtList4_num').html(mtList4.length + '<span class="hd">'+JSON.stringify(mtList4)+'</span>')
            page.find('.edited_newMt_num').html(newMtBases.length + '<span class="hd">'+JSON.stringify(newMtBases)+'</span>')
            page.find('.edited_changeMt_num').html(changeMtBases.length + '<span class="hd">'+JSON.stringify(changeMtBases)+'</span>')
            page.find('.edited_newSupplier_num').html(suppliers.length + '<span class="hd">'+JSON.stringify(suppliers)+'</span>')
            page.find('.edited_changeSupplier_num').html(changeSuppliers.length + '<span class="hd">'+JSON.stringify(changeSuppliers)+'</span>')
            $(".mtPoint_last_editTime").html(editTimeStr)

            $(".display_mtList1").css("display", mtList1.length === 0?'none':'flex')
            $(".display_newMt").css("display", newMtBases.length === 0?'none':'flex')
            $(".display_changeMt").css("display", changeMtBases.length === 0?'none':'flex')
            $(".display_newSupplier").css("display", suppliers.length === 0?'none':'flex')
            $(".display_changeSupplier").css("display", changeSuppliers.length === 0?'none':'flex')
            $(".display_mtOrSupplierChange").css("display", newMtBases.length === 0 && changeMtBases.length === 0 && suppliers.length === 0 && changeSuppliers.length === 0?'none':'block')
            break
        case 'mtToHandle':
            for(let item of mtList1){
                str += `<tr>
                            <td>${item.name || ''}</td>
                            <td>${item.code || ''}</td>
                            <td>${item.model || ''}</td>
                            <td>${item.specifications || ''}</td>
                            <td>${item.unit || ''}</td>
                            <td>${item.create_name + ' ' + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>
                                <span class="hd">${JSON.stringify(item)}</span>
                                <span class="link-blue" onclick="determinePointInfo($(this))">确定定点信息</span>
                            </td>
                        </tr>`
            }
            thisPage.find('.tbl_edited_mtToHandle tbody').html(str)
            thisPage.find('.confirmNum').html(mtList1?mtList1.length:0)
            break
        case 'mtHandled':
            for(let item of mtList4){
                str +=  `<tr>
                            <td>${item.name || ''}</td>
                            <td>${item.code || ''}</td>
                            <td>${item.model || ''}</td>
                            <td>${item.specifications || ''}</td>
                            <td>${item.unit || ''}</td>
                            <td>${item.create_name + ' ' + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>
                                <span class="hd">${JSON.stringify(item)}</span>
                                <span class="link-blue" onclick="supplierOfMt($(this))">${item.supplier_number || 0}个</span>
                            </td>
                        </tr>`
            }
            thisPage.find('.tbl_edited_mtHandled tbody').html(str)
            thisPage.find('.confirmNum').html(mtList4?mtList4.length:0)
            break
        case 'mtAddHandle':
            for(let item of newMtBases){
                str +=  `<tr>
                            <td>${item.name || ''}</td>
                            <td>${item.code || ''}</td>
                            <td>${item.model || ''}</td>
                            <td>${item.specifications || ''}</td>
                            <td>${item.unit || ''}</td>
                            <td>${item.create_name + ' ' + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>
                                <span class="hd">${JSON.stringify(item)}</span>
                                <span class="link-blue" onclick="determinePointInfo($(this), 'collect')">确定定点信息</span>
                            </td>
                        </tr>`
            }
            thisPage.find('.tbl_edited_mtAddHandle tbody').html(str)
            thisPage.find('.confirmNum').html(newMtBases?newMtBases.length:0)
            thisPage.find('.determinedNum').html(mtList4?mtList4.length:0)
            break
        case 'mtChangeHandle':
            for(let item of changeMtBases){
                str +=   `<tr>
                            <td>修改前<br>修改后</td>
                            <td>${item.history.name || ''}<br>${item.name|| ''}</td>
                            <td>${item.history.code || ''}<br>${item.code|| ''}</td>
                            <td>${item.history.model || ''}<br>${item.model|| ''}</td>
                            <td>${item.history.specifications || ''}<br>${item.specifications|| ''}</td>
                            <td>${item.history.unit || ''}<br>${item.unit|| ''}</td>
                            <td>${item.history.createName + ' ' + moment(item.history.createDate).format("YYYY-MM-DD HH:mm:ss")}<br>${item.update_name + ' ' + moment(item.update_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>
                                <span class="hd">${JSON.stringify(item)}</span>
                                <span class="link-blue" onclick="mtChangeHandle($(this), 0)">无需处理</span>
                                <span class="link-blue" onclick="mtChangeHandle($(this), 11)">需处理</span> 
                            </td>
                        </tr>`
            }
            thisPage.find('.tbl_edited_mtChangeHandle tbody').html(str)
            thisPage.find('.changeNum').html(changeMtBases?changeMtBases.length:0)
            break
        case 'supplierAddHandle':
            for(let item of suppliers){
                str +=  `<tr>
                            <td>${item.fullName}</td>
                            <td>${item.name}</td>
                            <td>${item.codeName}</td>
                            <td>${item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>${chargeKey('invoicable', item.invoicable)}</td>
                            <td>${chargeKey('chargeAcceptable', item.chargeAcceptable)}</td>
                            <td>
                                <span class="hd">${JSON.stringify(item)}</span>
                                <span class="link-blue" onclick="supplierAddHandle($(this), 11)">有</span>
                                <span class="link-blue" onclick="supplierAddHandle($(this), 0)">没有</span>
                            </td>
                        </tr>`
            }
            thisPage.find('.tbl_edited_supplierAddHandle tbody').html(str)
            thisPage.find('.addNum').html(suppliers?suppliers.length:0)
            break
        case 'supplierChangeHandle':
            for(let item of changeSuppliers){
                let history = item.supplierHistory[item.supplierHistory.length - 2]
                if (!history) {
                    layer.msg("数据错误，请检查")
                    return false
                }
                str +=   `<tr>
                            <td>修改前<br>修改后</td>
                            <td>${history.fullName}<br>${item.fullName}</td>
                            <td>${history.name}<br>${item.name}</td>
                            <td>${history.codeName}<br>${item.codeName}</td>
                            <td>${history.createName + ' ' + moment(history.createDate).format("YYYY-MM-DD HH:mm:ss")}<br>${item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>${chargeKey('invoicable', history.invoicable)}<br>${chargeKey('invoicable', item.invoicable)}</td>
                            <td>${chargeKey('chargeAcceptable', history.chargeAcceptable)}<br>${chargeKey('chargeAcceptable', item.chargeAcceptable)}</td>
                            <td>
                                <span class="hd">${JSON.stringify(item)}</span>
                                <span class="link-blue" onclick="supplierChangeHandle($(this), 0)">无需处理</span>
                                <span class="link-blue" onclick="supplierChangeHandle($(this), 11)">需处理</span>
                            </td>
                        </tr>`
            }
            thisPage.find('.tbl_edited_supplierChangeHandle tbody').html(str)
            thisPage.find('.changeNum').html(changeSuppliers?changeSuppliers.length:0)
            break
    }

}
$(function (){
    // ---------------- 供应商 ---------------------
    $(".add_splName").on("blur", function(){
        var val = $(this).val();
        $(this).parents("section").find("[name='name']").val(val.substring(0,6));
    })
    $("#picShow").on("click",'',function(){//提示窗
        $("#picShow").fadeOut("fast");
    })
    $("#editMtSupplier input:radio").on("click", function (){
        $("#editMtSupplier .hidePart").hide()
        let radioData = {}
        $("#editMtSupplier input:radio").each(function (){
            let name = $(this).attr('name')
            radioData[name] = $("#editMtSupplier input:radio[name='"+name+"']:checked").val()
        })
        setRelation(radioData)
    })
})

function setRelation(radioData) {
    if (radioData.chargeAcceptable && radioData.chargeAcceptable == '1') {
        $("#editMtSupplier .part_isChargeBegin").css('display', 'flex')
        $("#editMtSupplier .part_chargePeriod").show()
    }
    if (radioData.isImprest && radioData.isImprest === '1') {
        $("#editMtSupplier .part_isImprestProportion").css('display', 'flex')
    }
    if (radioData.invoicable && radioData.invoicable === '1') {
        $("#editMtSupplier .part_isVatsPayable").css('display', 'flex')
        $("#editMtSupplier .part_isDraftAcceptable").css('display', 'flex')
        if (radioData.vatsPayable === '1') {
            $("#editMtSupplier .part_taxRate").show()
        }
    }
}
// 材料
$(function (){
    // 新增分类
    $('.addCat').click(function(){
        let page = $(".page:visible")
        var catLen = page.find(".catAll").children().length ;
        var mtLen = page.find(".tbl_RAAMt tbody tr").length ;
        if(catLen === 0 && mtLen > 0){
            layer.msg("当前分类下有材料，不能新增子级类别");return false;
        }
        var pid = $(".curCat").children(".go2Cat:last").children(".hd").html();
        var pName = $(".curCat").children(":last").children("span:eq(0)").html();
        if(pName == "待分类"){
            layer.msg("待分类下不能新增子类！");return false;
        }
        bounce.show($('#addCat'));
        $("#addkind_pid").val(pid);
        $("#catParents").html($(".curCat:visible").html());
        $("#newCatName").val("");

    });
    // 点击分类获取分类下的物料
    $('.catAll').on('click', 'li', function(){
        $(".searchInput:visible").val('')
        let id = $(this).data("id") ;
        getMtList(1, 20, '', id)
    });
    // 分类的编辑删除
    $('.catAll').on('click', '.ctrlBtn .fa', function(e){
        e.stopPropagation();
        let btnName = $(this).attr("name");
        let id = $(this).parent().parent().data("id") ;
        let that = $(this)
        if(btnName === "edit"){
            $("#editCat input").val("");
            bounce.show($("#editCat"));
            $("#editCat .sureBtn").unbind().on("click", function () {
                let name = $("#editCat input").val();
                $.ajax({
                    url:"../material/updateCategory.do",
                    data:{ id: id, name: name }
                }).then(res => {
                    var status = res.status;
                    if( status === 1 ){
                        layer.msg("操作成功！");
                        bounce.cancel()
                        that.parents("li").find(".catName").html(name);
                        // 修改列表
                        let mtCategories = $("#editRAAMt").data('categories')
                        mtCategories.forEach(element => {
                            if (element.id === id) {
                                element.name = name
                            }
                        })
                    }else{
                        layer.msg("编辑失败，请稍后重试！");
                    }
                })
            })
        }else if(btnName === "del"){
            bounce.show($("#bounce_tip"));
            $("#bounce_tip .tipMsg").html('确定删除该分类？')
            $("#bounce_tip .sureBtn").unbind().on("click", function () {
                $.ajax({
                    url:"../material/deleteCategory.do",
                    data:{ id: id  }
                }).then(res => {
                    bounce.cancel()
                    let status = res.status;
                    if( status === 1 ){
                        layer.msg("删除成功！");
                        let mtCategories = $("#editRAAMt").data('categories')
                        mtCategories.splice(mtCategories.findIndex(item => item.id === id), 1);
                        that.parents("li").remove()
                    }else if( status === 2 ){
                        layer.msg("当前分类下有子分类，不可以删除！");
                    }else if( status === 3 ){
                        layer.msg("当前分类的子分类下有物料，不可以删除！");
                    }else if( status === 4 ){
                        layer.msg("当前分类下有物料，不可以删除！");
                    }
                })
            })
        }
    });
    $(".page[page='importInfo']").on('click', '.btn', function(){
        let name = $(this).data("name");
        let thisObj = $(this);
        switch (name) {
            case 'clearNoSave':
                $("#importCancel").data("type",'1');
                bounce_Fixed.show($("#importCancel"));
                break;
            case 'stepNext':
                allImportMtEnter();
                break;
            case "cancelSave": // 放弃
                $("#importCancel").data("type", 2);
                bounce_Fixed.show($("#importCancel"));
                break;
            case "initUpdate": // 修改
                var info = thisObj.parents("tr").data("info");
                $("#updateImportMt").data("obj", thisObj);
                $("#updateImportMt").data("upType", 1);
                $("#updateImportMt input[name=code]").data("old", info["code"]);
                $("#updateImportMt [require]").each(function(){
                    var key = $(this).attr("name");
                    var val = handleNull(info[key]);
                    $(this).val(val);
                });
                $("#updateImportMt .textMax").html(handleNull(info["memo"]).length + '/100');
                bounce_Fixed.show($("#updateImportMt"));
                break;
            case "initDel": // 删除
                $("#importMtDel").data("obj", thisObj);
                $("#importMtDel").data("type", '1');
                bounce_Fixed.show($("#importMtDel"));
                break;
            case "update": // 修改
                var info = thisObj.parents("tr").data("info");
                $("#updateImportMt").data("obj", thisObj);
                $("#updateImportMt").data("upType", 2);
                $("#updateImportMt input[name=code]").data("old", info["code"]);
                $("#updateImportMt [require]").each(function(){
                    var key = $(this).attr("name");
                    var val = handleNull(info[key]);
                    $(this).val(val);
                });
                $("#updateImportMt .textMax").html(handleNull(info["memo"]).length + '/100');
                bounce_Fixed.show($("#updateImportMt"));
                break;
            case "del": // 删除
                $("#importMtDel").data("obj", thisObj);
                $("#importMtDel").data("type", '2');
                bounce_Fixed.show($("#importMtDel"));
                break;
        }
    });
    $(".bounce_Fixed").on('click', ".ty-btn",function() {
        var name = $(this).data('name');
        switch (name) {
            case "importCancelSure": // 放弃
                var type = $("#importCancel").data("type");
                // var enetryType = $("#importEntryType").data("type");
                var enetryType = 1;
                bounce_Fixed.cancel();
                if (type == '1') {
                    back()
                } else {
                    turnCancel(1, 2);
                }
                break;
            case "lastSaveSure": // 确定保存
                let cateId = $(".matCategory  .category:last").val();
                var isP = $("#importEntryType").data("type");
                $.ajax({
                    url: "../mtImport/finishImportMt.do",
                    data: {
                        "category": cateId,
                        "isPurchased": 1,
                        "type": 1
                    },
                    success: function (data) {
                        var state = data.status;
                        bounce_Fixed.cancel();
                        if (state == 1) {
                            layer.msg("保存成功")
                            back()
                        } else {
                            layer.msg("保存失败！");
                        }
                    }
                })
                break;
            case "judgeImport": // 批量导入尚未完成是否继续
                var flag = $(".unfinishedForm .fa-circle").data("type")
                if (flag == '1') {
                    jumpPage('importInfo')
                    $(".page[page='importInfo'] .importCon2").show().siblings().hide();
                    bounce.cancel(); bounce_Fixed.cancel();
                    getUnFinishImportList(1);
                } else if (flag == '0') {
                    turnCancel(1, 1);
                } else {
                    layer.msg("请选择上次的批量导入尚未完成是否继续！");
                }
                break;
            case "updateImportMt": // 修改确定
                var emptyNum = 0;
                var obj = $("#updateImportMt").data("obj");
                var trObj = obj.parents("tr");
                $("#updateImportMt [need]").each(function () {
                    var val = $(this).val();
                    if (val == '') emptyNum++;
                })
                if (emptyNum > 0) {
                    layer.msg("还有必填项尚未填写！");
                } else {
                    var codeMt = $("#updateImportMt input[name='code']").val();
                    var oldCode = $("#updateImportMt input[name='code']").data("old");
                    var same = 0, name= "";
                    var upType = $("#updateImportMt").data("upType");
                    if (upType == 1) {
                        $(".page[page='importInfo'] .importCon1 tbody tr").each(function () {
                            if ($(this) !== trObj) {
                                var info = $(this).data("info");
                                if (codeMt == info.code && info.code != oldCode){
                                    same++;
                                    name = info.name;
                                }
                            }
                        })
                    } else if (upType == 2) {
                        $(".page[page='importInfo'] .importCon2 tbody tr").each(function () {
                            if ($(this) !== trObj) {
                                var info = $(this).data("info");
                                if (codeMt == info.code && info.code != oldCode) {
                                    same++;
                                    name = info.name;
                                }
                            }
                        });
                    }
                    if(same > 0) {
                        var str = '<p>您录入的材料代号与本次导入的'+ name +'材料代号相同。</p><p>请确认！</p>';
                        $("#iknowTip .iknowWord").html(str);
                        bounce_Fixed2.show($("#iknowTip"));
                    }else {
                        var trData = trObj.data("info");
                        var trData1 = trData;
                        if (upType == 1) {
                            var unit = trObj.find("select").val();
                            $.ajax({
                                url: "../mtImport/updateFalseMtEnter.do",
                                data: {
                                    "code": codeMt,
                                    "unit": unit
                                },
                                success: function (data) {
                                    var status = data.status;
                                    if (status == '1') {
                                        $("#updateImportMt [require]").each(function () {
                                            var key = $(this).attr("name");
                                            var val = $(this).val();
                                            trData[key] = val;
                                        });
                                        bounce_Fixed.cancel();
                                        trObj.find(".sign").each(function () {
                                            var name = $(this).data("name");
                                            $(this).html($("#updateImportMt input[name=" +name +"]").val());
                                        });
                                        trObj.data("info", trData);
                                    } else if(status == '2'){
                                        var tip = '<p>您录入的材料代号与公司'+ data.name +'材料代号相同。</p>' +
                                            '<p>请确认！</p>';
                                        $("#iknowTip .iknowWord").html(tip);
                                        bounce_Fixed2.show($("#iknowTip"));
                                    }
                                }
                            });
                        } else if (upType == 2) {
                            var json = {
                                "id": trData.id,
                                "unitId": trData.unitId
                            }
                            $("#updateImportMt [require]").each(function () {
                                var key = $(this).attr("name");
                                var val = $(this).val();
                                json[key] = val;
                            });
                            console.log("tr的数据：" + JSON.stringify(trObj.data("info")));
                            updateMtInfo(json, 1);
                        }
                    }
                }
                break;
        }
    });
    $(".unfinishedForm").on('click', ".fa",function() {
        $(this).addClass("fa-circle").removeClass("fa-circle-o");
        $(this).parent().siblings(".changeDot").find("span").addClass("fa-circle-o").removeClass("fa-circle");
    });
})
$(function () {
    $('tbody').on('click', 'span', function(){
        let type = $(this).data('type');
        switch (type){
            // 表格 供应商数量 点击按钮
            case 'sup':
                // 跳转供应商列表
                editObj = $(this) ;
                var info = $(this).parent().next().children(".hd").html();
                info = JSON.parse(info)
                $(".mt41 .mt41_create").html(info['create_name'] + " " + (new Date(info['create_date']).format('yyyy-MM-dd hh:mm:ss')));
                for(var key in info){
                    if(key == 'location_number' ){
                        $(".mt41 .mt41_"+key).children("span").html(info[key] ? (info[key]+'个') : '0个');
                    }else if(key == 'initial_stock'){
                        $(".mt41 .mt41_"+key).children("span").html(info[key] ? (info[key]) : '0');
                    }else{
                        $(".mt41 .mt41_"+key).html(info[key] );
                    }
                }
                $(".mt42 tbody").html("");
                getSuppliers(info.id, 1, 'mt42');
                $(".stopSupListBtn").show();
                $(".addMtSupBtn").show();
                $(".addNewSupBtn").show();
                $(".noFixedPurchaseLogBtn").show();
                $(".noFixedPurchaseBtn").hide();
                $(".pauseSup").hide().siblings().show();

                $("#hidePause4").hide();
                toggleSuspend(4);
                break;
            // 获取某物料的供应商列表 - 查看
            case 'fixedScan':
                // 查看定点信息
                var mtInfo = {};
                if(editObj){
                    mtInfo = JSON.parse(editObj.parent().next().children(".hd").html());
                }else if(editObjFixedMassage){
                    mtInfo = JSON.parse(editObjFixedMassage.siblings(".hd").html());
                }else{
                    layer.msg("获取材料失败")
                    return false;
                }
                var mtID = mtInfo['id'];
                var supInfo = JSON.parse( $(this).siblings(".hd").html());
                $("#cgaddcontact3").data("box",supInfo);
                var supID = supInfo['supplier_id'];
                var suid = supInfo["id"];
                var relationID = supInfo['material_supplier_id'];
                fixedScanInfo(mtID, supID, relationID,supInfo);
                bounce.show($("#pointChange"));
                $("#pointChange .edit").hide();$("#pointChange .scan").show();
                let messge = $(this).siblings(".hd").html();
                $("#pointChange").data("mess",messge);    //用于获取供应商id
                break;
            case 'concase'://这里不知道是否需要用，可以加上-7.8
                //新增合同
                bounce.show($('#newcontract'));
                break;
            default:
        }

    })

    // 点击横向导航栏
    $('.curCat').on('click', '.go2Cat', function(){
        $(".searchInput:visible").val('')
        var cat = $(this).find(".hd").html();
        $(this).nextAll().remove();
        $(this).remove();
        getMtList(1, 20, '', cat)
    });
    //向本合同添加商品弹窗种的选择框
    $("#addcontract").on('click','.fa',function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    })


    // 上传的合同删除   //这里也是不确定需不需要用-7.8
    $("#newcontract").on("click", '.fa-times', function () {
        let info = JSON.parse($(this).siblings(".hd").html())
        let fileUid = info.fileUid
        cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
        $(this).parent(".fileIm").remove();
    });
    $(".bounce_Fixed").on("click",".ty-btn,.edit,[type='btn']",function(){
        var name=$(this).data('name');
        switch(name){
            //关闭弹窗
            case 'editContractOk':
                editContractOk(1)
                break;
        }
    });
    $("body").on("click",".funBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
    $(".chooseCusCon").click(function () {
        let target = $(this).data("target");
        $("#target").val(target)
        bounce_Fixed3.show( $("#chooseCusContact") );
        getCusContactList();
    });
    $("#chooseCusContact").on("click", '.fa', function () {
        $("#chooseCusContact").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).attr("class", "fa fa-dot-circle-o")
    })
    $(".placeList").on('click','.fa', function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    });
    $(document).bind("click",function(e){//点击空白处，设置的弹框消失
        var tag = $(".taxRateList:visible");
        var tag1 = $("#taxRate:visible");
        var tag2 = $("#edit_taxRate:visible");
        var target = $(e.target);
        if(target.closest(tag).length == 0 && target.closest(tag1).length == 0 && target.closest(tag2).length == 0){
            $(tag).hide();
        }
    });
})

// creator: 张旭博，2023-09-08 09:49:37， 初始化主页
function initMain() {
    $.ajax($.webRoot + '/initialize/getPurchaseInitList.do').then(res => {
        var data = res.data
        var overAllot = data.overAllot // 总进度
        var initAllotList = data.initAllotList // 各项进度
        $("#home").data('stepAllId', overAllot.id)
        if (overAllot.state === 1) {
            // 采购初始化已完成
            let completeTime = moment(overAllot.updateDate).format("YYYY-MM-DD HH:mm:ss")
            $(".purchaseCompleteTime").html(completeTime)
            $(".page[page='main'] .pagePart[part='see']").show().siblings(".pagePart").hide()
        } else {
            // 采购初始化开始
            $(".page[page='main'] .pagePart[part='edit']").show().siblings(".pagePart").hide()
            let str = ''
            var arr = ['编辑尚未完成', '已编辑完', '已编辑完']
            let toArr = ['mtSupplierList', 'equiptSupplierList', 'RAAMtList', 'RAAMtPoint']
            $.ajax($.webRoot + '/mt/init/collect')
                .then(res => {
                    let data = res
                    let newMtBases = data.newMtBases || [] // 编辑之后又新增了的材料
                    let changeMtBases = data.changeMtBases || [] // 已编辑数据基本信息发生变动的材料
                    let suppliers = data.suppliers || [] // 编辑之后新增了的供应商
                    let changeSuppliers = data.changeSuppliers || [] // 编辑之后信息发生变动的供应商
                    let stateStr = ''
                    let lastState = 0
                    if ((newMtBases.length > 0 || changeMtBases.length > 0) && (suppliers.length > 0 || changeSuppliers.length > 0)) {
                        stateStr = '已编辑完，但需重新编辑，因为材料与供应商的数据都有变化'
                        lastState = 2
                    } else {
                        if (newMtBases.length > 0 || changeMtBases.length > 0) {
                            stateStr = '已编辑完，但需重新编辑，因为材料的数据有变化'
                            lastState = 2
                        } else if (suppliers.length > 0 || changeSuppliers.length > 0) {
                            stateStr = '已编辑完，但需重新编辑，因为供应商的数据有变化'
                            lastState = 2
                        } else {
                            lastState = 1
                        }
                    }
                    if (stateStr) { arr[2] = stateStr }
                    for (let i in initAllotList) {
                        let item = initAllotList[i]
                        if (i == 3) {
                            if (item.state === 0) {
                                item.state = 0
                            } else {
                                item.state = lastState
                            }
                        }
                        str += ` <tr data-id="${item.id}" data-state="${item.state}">
                                    <td>${item.editName}</td>
                                    <td>${item.required?'是':'否'}</td>
                                    <td>${arr[item.state]}</td>
                                    <td><div class="link-blue" type="btn" name="edit" to="${toArr[i]}">编辑</div></td>
                                    <td class="hd">${JSON.stringify(item)}</td>
                                </tr>`
                    }
                    $(".tbl_purchase_item_edit tbody").html(str)
                })
        }
    })
}

// creator: 张旭博，2023-09-08 09:20:54， 初始化 - 完成
function initComplete() {
    let isChecked = $(".checkRadio:visible").find("input:radio").prop("checked")
    if (!isChecked) {
        layer.msg("请勾选后再操作！")
        return false
    }
    let stepId = $("#home").data('stepId') // 步骤id
    let page = $(".page:visible").attr("page")
    switch(page) {
        case 'main':
            stepId = $("#home").data('stepAllId')
            let state = 0
            $(".tbl_purchase_item_edit tbody tr").each(function (){
                if ($(this).data("state") !== 1) {
                    state++
                }
            })
            if (state > 0) {
                layer.msg("还有必须编辑的项尚未编辑！")
                return false
            }
            break
        case 'RAAMtList_edit':
            let num  = $(".mtNum").html()
            if (num === '0') {
                layer.msg("请录入贵公司需采购的材料！")
                return false
            }
            break
        case 'RAAMtPoint_edit':
            if ($(".tbl_undeterminedRAAMt tbody tr").length > 0) {
                layer.msg("还有材料的定点信息尚未确认！")
                return false
            }
            break
    }


    $.ajax({
        url: $.webRoot + '/initialize/completeAllot.do',
        data: {
            id: stepId
        }
    }).then(res => {
        let data = res.data
        if (data === 1) {
            layer.msg('操作成功！')
            if ($(".page[page='main']").is(":visible")) {
                reloadPage()
            } else {
                back()
            }
        }
    })
}


// creator: 张旭博，2023-09-12 03:55:12， 确定原辅材料的定点信息（完成态）- 各个去处理 - 按钮
function edited_handle(selector) {
    let to = selector.attr('to')
    jumpPage('RAAMtPoint_' + to, ()=> {
        getCollect()
    })
}

// creator: 张旭博，2023-07-17 08:41:31， 操作说明 - 录入采购材料
function des_inputMtBtn() {
    bounce.show($("#des_inputMt"))
}

// creator: 张旭博，2023-09-13 01:49:07， 材料改变 - 处理/无需处理
function mtChangeHandle(selector, initial) {
    if (initial !== 11) {
        let id = 0, mtInfo = ''
        if (initial === 0) {
            mtInfo = selector.parents("tr").find('.hd').html()
            mtInfo = JSON.parse(mtInfo)
            id = mtInfo.id
        } else {
            id = selector.parents(".page").find('.tbl_mtShow tbody tr').eq(0).data("id")
        }
        if (initial === 0) {
            bounce.show($("#bounce_tip"))
            $("#bounce_tip .tipMsg").html('确定无需处理吗？')
            $("#bounce_tip .sureBtn").unbind().on('click', function (){
                $.ajax({
                    url: $.webRoot + '/mt/init/mtBaseHandle',
                    data: {
                        id: id, // 材料id
                        initial: initial // 0-无需处理 1-需要处理
                    }
                }).then(res => {
                    if (res === 1) {
                        layer.msg('操作成功！')
                        bounce.cancel()
                        reloadPage()
                    }
                })
            })
        } else {
            $.ajax({
                url: $.webRoot + '/mt/init/mtBaseHandle',
                data: {
                    id: id, // 材料id
                    initial: initial // 0-无需处理 1-需要处理
                }
            }).then(res => {
                if (res === 1) {
                    layer.msg('操作成功')
                    back()
                }
            })
        }

    } else {
        jumpPage('determinePointInfo', function () {
            let info = JSON.parse(selector.siblings(".hd").html());
            let mtStr = `<tr data-id="${info.id}">
                            <td>修改前<br>修改后</td>
                            <td>${info.history.name || ''}<br>${info.name|| ''}</td>
                            <td>${info.history.code || ''}<br>${info.code|| ''}</td>
                            <td>${info.history.model || ''}<br>${info.model|| ''}</td>
                            <td>${info.history.specifications || ''}<br>${info.specifications|| ''}</td>
                            <td>${info.history.createName + ' ' + moment(info.history.createDate).format("YYYY-MM-DD HH:mm:ss")}<br>${info.update_name + ' ' + moment(info.update_date).format("YYYY-MM-DD HH:mm:ss")}</td> 
                            <td>${info.history.unit || ''}<br>${info.unit|| ''}</td>
                        </tr>`
            let mtTheadStr = `<tr>
                                <td>状态</td>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>创建/修改</td>
                                <td>计量单位</td>
                            </tr>`
            $("[page='determinePointInfo'] .topRightBtn").show()
            $("[page='determinePointInfo'] .tbl_mtShow tbody").html(mtStr)
            $("[page='determinePointInfo'] .tbl_mtShow thead").html(mtTheadStr)
            $("[page='determinePointInfo'] .tbl_mtHandle tbody").html('')
            getSuppliers(info.id, 1, 'tbl_mtHandle');
            $(".tbl_mtHandle").data('mtinfo', info)
        })
    }

}

// creator: 张旭博，2023-09-13 01:49:07， 供应商新增 - 有/没有 供应着的材料
function supplierAddHandle(selector, initial) {

    if (initial !== 11) {
        let id = 0, supplierInfo = ''
        if (initial === 0) {
            supplierInfo = selector.parents("tr").find('.hd').html()
        } else {
            supplierInfo = selector.parents(".page").find('.tbl_supplierShow .hd').html()
        }
        supplierInfo = JSON.parse(supplierInfo)
        id = supplierInfo.id
        if (initial === 0) {
            bounce.show($("#bounce_tip"))
            $("#bounce_tip .tipMsg").html('确定该供应商没有供应着的材料吗？')
            $("#bounce_tip .sureBtn").unbind().on('click', function (){
                $.ajax({
                    url: '/mt/init/supplierHandle',
                    data: {
                        id: id, // 材料id
                        initial: initial // 0-无需处理 1-需要处理
                    }
                }).then(res => {
                    if (res === 1) {
                        layer.msg('操作成功')
                        bounce.cancel()
                        reloadPage()
                    }
                })
            })
        } else {
            $.ajax({
                url: '/mt/init/supplierHandle',
                data: {
                    id: id, // 材料id
                    initial: initial // 0-无需处理 1-需要处理
                }
            }).then(res => {
                if (res === 1) {
                    layer.msg('操作成功')
                    back()
                }
            })
        }

    } else {
        jumpPage('determinePointInfo_editMt', function () {
            let info = JSON.parse(selector.siblings(".hd").html());
            let mtStr = `<tr>
                            <td>${info.fullName}</td>
                            <td>${info.name}</td>
                            <td>${info.codeName}</td>
                            <td>${chargeKey('invoicable', info.invoicable)}</td>
                            <td>${chargeKey('chargeAcceptable', info.chargeAcceptable)}</td>
                            <td>
                                <span class="hd">${JSON.stringify(info)}</span>
                                <span class="link-blue" onclick="havelooko($(this))">查看</span>
                            </td>
                        </tr>`
            $("[page='determinePointInfo_editMt'] .tbl_supplierShow tbody").html(mtStr)
            $("[page='determinePointInfo_editMt'] .tbl_supplierHandle tbody").html('')
            getMts(info.id, 1);
            $("[page='determinePointInfo_editMt'] .tbl_supplierHandle").data('supplierinfo', info)
        })
    }
}

// creator: 张旭博，2023-09-13 01:49:07， 供应商修改- 无需处理/需处理
function supplierChangeHandle(selector, initial) {

    if (initial !== 11) {
        let id = 0, supplierInfo = ''
        if (initial === 0) {
            supplierInfo = selector.parents("tr").find('.hd').html()
        } else {
            supplierInfo = selector.parents(".page").find('.tbl_supplierShow .hd').html()
        }
        supplierInfo = JSON.parse(supplierInfo)
        id = supplierInfo.id
        if (initial === 0) {
            bounce.show($("#bounce_tip"))
            $("#bounce_tip .tipMsg").html('确定无需处理吗？')
            $("#bounce_tip .sureBtn").unbind().on('click', function (){
                $.ajax({
                    url: '/mt/init/supplierHandle',
                    data: {
                        id: id, // 材料id
                        initial: initial // 0-无需处理 1-需要处理
                    }
                }).then(res => {
                    if (res === 1) {
                        layer.msg('操作成功')
                        bounce.cancel()
                        reloadPage()
                    }
                })
            })
        } else {
            $.ajax({
                url: '/mt/init/supplierHandle',
                data: {
                    id: id, // 材料id
                    initial: initial // 0-无需处理 1-需要处理
                }
            }).then(res => {
                if (res === 1) {
                    layer.msg('操作成功')
                    back()
                }
            })
        }

    } else {
        jumpPage('determinePointInfo_editMt', function () {
            let info = JSON.parse(selector.siblings(".hd").html());
            let mtStr = `<tr>
                            <td>${info.fullName}</td>
                            <td>${info.name}</td>
                            <td>${info.codeName}</td>
                            <td>${chargeKey('invoicable', info.invoicable)}</td>
                            <td>${chargeKey('chargeAcceptable', info.chargeAcceptable)}</td>
                            <td>
                                <span class="hd">${JSON.stringify(info)}</span>
                                <span class="link-blue" onclick="havelooko($(this))">查看</span>
                            </td>
                        </tr>`
            $("[page='determinePointInfo_editMt'] .tbl_supplierShow tbody").html(mtStr)
            $("[page='determinePointInfo_editMt'] .tbl_supplierHandle tbody").html('')
            getMts(info.id, 1);
            $("[page='determinePointInfo_editMt'] .tbl_supplierHandle").data('supplierinfo', info)
        })
    }
}

// creator: 张旭博，2023-09-14 02:23:46， 采购整个完成之后展示页面各项跳转
function seeListBtn(path) {
    // 跳转页面
    jumpPage(path, ()=> {

        // 重置表单
        $(".page:visible .ty-searchInput").val('')

        // 获取页面数据
        switch (path) {
            case 'mtSupplierList':
                // 获得供应商列表
                getContractMesSee(1,20);
                break
            case 'equiptSupplierList':
                // 获得供应商列表
                getContractMesSee(1,20);
                break
            case 'RAAMtList_edit':
                $(".searchInput:visible").val('')
                $(".catAll").data("noHandle", 1)
                getMtList(1, 20,)
                break
            case 'pointedRAAMt':
                $(".catAll").data("noHandle", 1)
                getMtList(1, 20, 4);
                break

        }
    })
}

// creator: 张旭博，2023-12-19 10:21:50， 材料搜索
function searchMtSupplier() {
    let keyword = $(".ty-searchInput:visible").val()
    if ($.trim(keyword) === '') {
        layer.msg('请输入供应商的代号或名称！')
        return false
    }
    jumpPage('mtSupplierSearch', ()=> {
        getSearchSuppliers(1, 20, keyword)
    })
}

function getSearchSuppliers(currentPageNo, pageSize, keyword) {
    let data = {
        currentPageNo: currentPageNo,
        pageSize: pageSize,
        keyword: keyword
    }
    let mainName = $("#home").data('mainname') // 当前编辑的4个模块是哪一个模块
    if (mainName === 'equiptSupplierList') {
        data.type = 2
    }
    $.ajax({
        url: $.webRoot + '/supplier/searchSupplier.do',
        data:data,
        success:function(res){
            let data = res.data;
            // 分页
            let pageInfo = data.pageInfo;
            let totalPage = pageInfo.totalPage;
            let jsonStr = JSON.stringify({keyword: keyword})
            setPage($("#ye_searchSupplier"), currentPageNo, totalPage, "searchSupplier", jsonStr);

            let result = pageInfo.totalResult
            $(".page[page='mtSupplierSearch'] .searchNum").html(result)

            let list = data.list || [];
            let tbodyStr = ''
            for (let item of list) {
                tbodyStr +=`<tr data-id="${item.id}">
                            <td>${item.fullName}</td>
                            <td>${item.name}</td>
                            <td>${item.codeName}</td>
                            <td>${item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>${chargeKey('invoicable', item.invoicable)}</td>
                            <td>${chargeKey('chargeAcceptable', item.chargeAcceptable)}</td>
                            <td>
                                <span class="link-blue" onclick="editMtSupplier($(this), 'update')">修改</span>
                                <span class="link-red" onclick="deleteSupplier($(this))">删除</span>
                                <span class="hd">${handleNull(item.id)}</span>
                                <span id="thingsbox" style="display: none">${JSON.stringify(item)}</span>
                            </td>
                        </tr>`
            }
            $(".tbl_supplierSearchList tbody").html(tbodyStr)
        }
    })
}

// ----------------------- 采购（供应商） --------------------------

// creator: 张旭博，2023-12-29 03:51:48， 获取供应商列表 (材料和设备共用一个接口）
function getContractMes(currentPageNo, pageSize){
    // currentPageNo——当前页 pageSize——每页记录数
    let paramData = {
        currentPageNo: currentPageNo,
        pageSize: pageSize
    }
    let mainName = $("#home").data('mainname') // 当前编辑的4个模块是哪一个模块
    if (mainName === 'equiptSupplierList') {
        paramData.type = 2
    }
    $.ajax({
        url: '../supplier/getSupplierList.do',
        data: paramData
    }).then(res => {
        let data = res.data
        let list = data.list
        let pageInfo = data.pageInfo
        let tbodyStr = ''

        let pageSelector = paramData.type === 2? $(".page[page='equiptSupplierList_edit']"): $(".page[page='mtSupplierList_edit']")
        setPage($("#ye_supplierList" + (paramData.type?2:'')), currentPageNo, pageInfo.totalPage, "supplierList");
        if (list && list.length > 0) {
            for (let item of list) {
                tbodyStr +=`<tr data-id="${item.id}">
                                <td>${item.fullName}</td>
                                <td>${item.name}</td>
                                <td>${item.codeName}</td>
                                <td>${item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                                <td>${chargeKey('invoicable', item.invoicable)}</td>
                                <td>${chargeKey('chargeAcceptable', item.chargeAcceptable)}</td>
                                <td>
                                    <span class="link-blue" onclick="editMtSupplier($(this), 'update')">修改</span>
                                    <span class="link-red" onclick="deleteSupplier($(this))">删除</span>
                                    <span class="hd">${handleNull(item.id)}</span>
                                    <span id="thingsbox" style="display: none">${JSON.stringify(item)}</span>
                                </td>
                            </tr>`
            }
            pageSelector.find(".pagePart[part='edit']").show().siblings(".pagePart").hide()
        } else {
            pageSelector.find(".pagePart[part='init']").show().siblings(".pagePart").hide()
        }
        pageSelector.find("tbody").html(tbodyStr)
    })
}

// creator: 张旭博，2023-12-29 03:51:48， 获取供应商列表（只查看）
function getContractMesSee(currentPageNo, pageSize){
    // currentPageNo——当前页 pageSize——每页记录数
    let paramData = {
        currentPageNo: currentPageNo,
        pageSize: pageSize
    }
    let page = $(".page:visible").attr("page") // 当前编辑的4个模块是哪一个模块
    if (page === 'equiptSupplierList') {
        paramData.type = 2
    }
    $.ajax({
        url: '../supplier/getSupplierList.do',
        data: paramData
    }).then(res => {
        var data = res.data
        var list = data.list || []
        let tbodyStr = ''
        $(".page:visible").find(".count").html(list.length)
        for (let item of list) {
            tbodyStr +=`<tr data-id="${item.id}">
                                <td>${item.fullName}</td>
                                <td>${item.name}</td>
                                <td>${item.codeName}</td>
                                <td>${item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                                <td>${chargeKey('invoicable', item.invoicable)}</td>
                                <td>${chargeKey('chargeAcceptable', item.chargeAcceptable)}</td>
                            </tr>`
        }
        $(".page:visible").find("tbody").html(tbodyStr)
    })
}

// creator: 张旭博，2023-08-10 10:31:30， 格式化数据（是否给开票、是否接收挂账）
function chargeKey(name, data) {
    let list = []
    if (data === null) {return  '--' } else {
        data = Number(data)
        switch (name) {
            case 'invoicable':
                list = ['不接受', '接受']
                return list[data]
                break
            case 'chargeAcceptable':
                list = ['否', '是']
                return list[data]
                break
        }
    }
}

// creator: 张旭博，2023-09-14 02:16:21， 新增/修改供应商
function editMtSupplier(selector, type){
    $("#editMtSupplier").data('selector', selector)
    $("#editMtSupplier").data('type', type)
    //初始化构件
    if (type === 'update') {
        var id = selector.parents("tr").data('id')
        $("#editMtSupplier").data('id', id);
        bounce_Fixed.show($("#changeMtSupplier"));

        $.ajax({
            url:"../supplier/getSrmSupplierOne.do",
            data:{
                id: id
            },
            success:function(resData){
                var res = resData.data;
                $("#changeMtSupplier .supplierName").html(res.fullName);
                $("#changeMtSupplier .insertItem").remove()
                let contactsList = res.contactsList || [] // 联系人
                let yjAddressList = res.yjAddressList || [] // 邮寄信息
                let contractBaseList = res.contractBaseList || [] // 合同

                $("#editMtSupplier").data('contactInfo', contactsList); // 联系人
                let contactStr = ``
                contactsList.forEach((contactItem)=>{
                    contactStr += `
                    <tr class="insertItem contactItem" data-id="${ contactItem.id }">
                        <td class="">${ contactItem.name } ${ contactItem.post } ${ contactItem.mobile }</td>
                        <td> 
                            <span class="link-blue" onclick="editContactBtn($(this), 'update')">修改</span>
                            <span class="link-red" onclick="editContactBtn($(this), 'del')" >删除</span>
                            <span class="hd">${ JSON.stringify(contactItem) }</span>
                        </td>
                    </tr>
                    
                    `
                })
                let yjAddressStr = ``
                yjAddressList.forEach((yjAddressItem)=>{
                    yjAddressStr += `
                    <tr class="insertItem mailItem" data-id="${ yjAddressItem.id }">
                        <td class="">${ yjAddressItem.address || ''}</td>
                        <td>
                            <span class="link-blue" onclick="editMailBtn($(this), 'update')">修改</span>
                            <span class="link-red" onclick="editMailBtn($(this), 'del')" >删除</span>
                            <span class="hd">${ JSON.stringify(yjAddressItem) }</span>
                        </td>
                    </tr>
                    `
                })
                let contractBaseStr = ``
                contractBaseList.forEach((contractBaseItem)=>{
                    contractBaseItem.signTime = formatDateTime(contractBaseItem.signTime)
                    contractBaseItem.validStart = formatDateTime(contractBaseItem.validStart)
                    contractBaseItem.validEnd = formatDateTime(contractBaseItem.validEnd)
                    contractBaseStr += `
                    <tr class="insertItem contractItem" data-id="${ contractBaseItem.id }">
                        <td class="">${ contractBaseItem.sn }（合同编号）</td>
                        <td> 
                            <span class="link-blue" onclick="editContractBtn($(this), 'update')" >修改</span>
                            <span class="link-red" onclick="editContractBtn($(this), 'del')" >删除</span>
                            <span class="hd">${ JSON.stringify(contractBaseItem) }</span>
                        </td>
                    </tr>
                    `
                })

                $("#changeMtSupplier .contract").after(contractBaseStr);
                $("#changeMtSupplier .mail").after(yjAddressStr);
                $("#changeMtSupplier .contact").after(contactStr);

            }
        })
    } else if (type === 'new') {
        bounce_Fixed2.show($("#editMtSupplier"));
        // 显示所有模块和名称（修改共用，会隐藏）
        $("#editMtSupplier section").show().find('.section_title').show()
        // 隐藏单选部分内容
        $("#editMtSupplier .hidePart").hide()
        // 重置表格
        $("#editMtSupplier tbody").html('')
        // 清空图片展示区
        $("#editMtSupplier .file_avatar").html('')
        // 重置表单
        $("#editMtSupplier input:radio").prop('checked', false)
        $("#editMtSupplier input:not(:radio)").val('')

        // 初始化全景照片上传
        initUploadSupplier($("#edit_panoramaBtn"));
        $("#editMtSupplier .bounce_title").html("新增供应商");
        bounce.everyTime('1.5s','addAccount',function(){
            var mailLength = $("#editMtSupplier .tbl_mail tbody tr").length;
            var contactLength = $("#editMtSupplier .tbl_contact tbody tr").length;
            var name=$("#editMtSupplier [name='name']").val();
            var fullName=$("#editMtSupplier [name='fullName']").val();
            var codeName=$("#editMtSupplier [name='codeName']").val();
            $("#sureEditMtSupplierBtn").prop("disabled", name === '' || fullName === '' || codeName === '')
            $("#newMailBtn").prop("disabled", mailLength > 8)
            $("#newContactBtn").prop("disabled", contactLength > 8)
        })
        $("#editMtSupplier").data('contactInfo',[]); // 联系人
        bounce_Fixed2.show($("#editMtSupplier"));
    }
}

// creator: 张旭博，2023-08-10 10:45:48， 新增供应商 - 提交
function sureEditMtSupplier(){
    let bounce_title = $.trim($("#editMtSupplier .bounce_title").html())
    //上传图片(获取图片的地址）
    let imgsQ = [];
    $("#editMtSupplier .file_avatar .imgsthumb").each(function() {
        let path = $(this).find(".filePic").data('path')
        imgsQ.push({
            normal: path
        });
    });
    let radioData = {}
    $("#editMtSupplier input:radio").each(function (){
        let name = $(this).attr('name')
        radioData[name] = $("#editMtSupplier input:radio[name='"+name+"']:checked").val()
    })

    // 基本信息数据获取
    let name = $("#editMtSupplier [name='name']").val();
    let fullName = $("#editMtSupplier [name='fullName']").val();
    let codeName = $("#editMtSupplier [name='codeName']").val();
    let chargePeriod = $("#editMtSupplier [name='chargePeriod']").val();
    let imprestProportion = $("#editMtSupplier [name='imprestProportion']").val();
    let baseData = {
        name: name,
        fullName: fullName,
        codeName: codeName,
        contractBaseImages:'',
        chargeAcceptable: radioData.chargeAcceptable || '', //是否接受挂账
        chargeBegin:radioData.chargeBegin || '',//从何时开始计算账期
        chargePeriod:chargePeriod,//请录入已约定的账期
        isImprest:radioData.isImprest || '',//是否需要预付款
        imprestProportion:imprestProportion,//预付款比例
        uncertainty:radioData.uncertainty || '',//比例不确定
        qImages: JSON.stringify(imgsQ)
    }
    if(baseData.uncertainty === "1" && baseData.imprestProportion === ""){
        baseData.imprestProportion = -1;
    }
    if(baseData.chargePeriod >0){
        baseData.chargePeriod = parseInt(baseData.chargePeriod);
    }
    // 基本信息验证
    if (bounce_title === '新增供应商' || bounce_title === '修改基本信息') {
        if( baseData.name.length === 0){
            layer.msg("请填写供应商名称");
            return false;
        }else if( baseData.fullName.length === 0){
            layer.msg("请填写供应商简称");
            return false;
        }else if( baseData.codeName.length === 0){
            layer.msg("请填写供应商代号");
            return false;
        }else if(baseData.isImprest === '1'){    //是否需要预付款       下面两个必须选一个（预付款比例和预付款比例不确定）
            if(baseData.imprestProportion === ''){ //预付款比例
                if(!baseData.uncertainty){ //预付款比例不确定
                    layer.msg("请填写预付款比例");
                    return false;
                }
            }
        }
    }

    // 下方为只修改基本信息提交方法
    if ($.trim($("#editMtSupplier .bounce_title").html()) === '修改基本信息') {
        baseData.id = $("#editMtSupplier").data("id")
        baseData.qImages = JSON.parse(baseData.qImages)
        $.ajax({
            url:"../supplier/updateSupplierBase.do",
            data: baseData
        }).then(res => {
            var status = res.status;
            if(status === 0){
                $("#tiphoto .shu1").html("操作失败，请稍后重试。");
                bounce_Fixed.show($("#tiphoto"));
                return false;
            }
            layer.msg("修改成功");
            getContractMes(1,20);
            bounce_Fixed2.cancel();
        })
        return false
    }
    // 开票信息数据获取
    let taxRate = $("#editMtSupplier [name='taxRate']").val();
    let invoiceData = {
        invoicable: radioData.invoicable,//能否开发票
        vatsPayable: radioData.vatsPayable || '',//能否开增值税发票
        taxRate: taxRate,//请录入税率
        draftAcceptable: radioData.draftAcceptable || '' //是否接受汇票
    }
    if(invoiceData.invoicable === "1"){
        if(!invoiceData.vatsPayable){
            invoiceData.vatsPayable = 1;
        }
    }
    // 开票信息验证
    if(!invoiceData.invoicable) {
        layer.msg("请选择该供应商是否能开发票");
        return false;
    }else if(invoiceData.invoicable === '1' && invoiceData.vatsPayable === '1'){
        if(invoiceData.taxRate === ''){
            layer.msg("请录入税率");
            return false;
        }
    }
    // 下方为只修改开票信息提交方法
    if ($.trim($("#editMtSupplier .bounce_title").html()) === '修改开票信息') {
        invoiceData.supplierId = $("#editMtSupplier").data("id")
        $.ajax({
            url: '../saleSupplier/editSupplierInvoice',
            data:invoiceData,
            success:function(data){
                // layer.msg("修改成功");
                getContractMes(1,20);
                bounce_Fixed2.cancel();
            }
        })
        return false
    }

    // 合同信息数据获取
    let contractArr = [];
    $("#editMtSupplier .tbl_contract tbody tr").each(function () {
        let info = JSON.parse($(this).find(".hd").html())
        if (info.contractBaseDocs.length > 0) {
            info.filePath = info.contractBaseDocs[0].filePath
            info.fileName = info.contractBaseDocs[0].title
        }
        contractArr.push(info)
    })

    // 邮寄信息数据获取
    let mailArr = [];
    $("#editMtSupplier .tbl_mail tbody tr").each(function () {
        let info = JSON.parse($(this).find(".hd").html())
        mailArr.push(info)
    })

    // 联系人信息数据获取
    let contactArr = []
    $("#editMtSupplier .tbl_contact tbody tr").each(function () {
        let info = JSON.parse($(this).find(".hd").html())
        contactArr.push(info)
    })

    let otherData = {
        contactsList:JSON.stringify(contactArr),//联系人列表
        yjAddressList :JSON.stringify(mailArr) ,//邮寄地址合计
        contractBaseList: JSON.stringify(contractArr)//合同集合
    }
    let dataFinal = {...baseData, ...invoiceData, ...otherData}
    let mainName = $("#home").data('mainname') // 当前编辑的4个模块是哪一个模块
    if (mainName === 'equiptSupplierList') {
        dataFinal.type = 2
    }
    console.log('dataFinal', dataFinal)
    $.ajax({
        url: '../supplier/addSupplier.do',
        data: dataFinal,
        success:function(data){
            console.log(data);
            var status = data.success;
            if(status == 0){
                layer.msg("该供应商名称已存在");
                return false;
            }else{
                layer.msg('操作成功')
                getContractMes(1,20);
                $(".page:visible .pagePart[part='edit']").show().siblings('.pagePart').hide()
                bounce_Fixed2.cancel();
            }
        }
    })
}

// creator: 张旭博，2023-12-29 03:44:43， 删除供应商 - 按钮
function deleteSupplier(selector){
    bounce_Fixed.show($("#bounceFixed_tip"));
    $("#bounceFixed_tip .tipMsg").html("确定删除吗?")
    $("#bounceFixed_tip .sureBtn").unbind().on("click", function () {
        let deleteId = selector.parents("tr").data("id")
        $.ajax({
            url:"../supplier/deleteSrmSupplier.do",
            data:{
                id: deleteId,
            },
            success:function(res){
                if (res.success === 1) {
                    layer.msg("操作成功");
                    bounce_Fixed.cancel();
                    reloadPage()
                } else {
                    layer.msg("操作失败！因为该材料下已有数据！");
                }
            },
        })
    })
}

// creator: 张旭博，2023-09-14 02:27:25， 新增/修改/删除 合同（适合多个场合）
function editContractBtn(selector, type){
    let supplierType = $("#editMtSupplier").data('type')
    let id = selector.parents("tr").data("id")
    let origin = selector.attr("origin")

    if (type === 'del') {
        if (supplierType === 'update') {
            // 修改供应商弹窗 删除合同 （需要调用接口删除）
            bounce_Fixed5.show($("#bounceFixed5_tip"))
            $("#bounceFixed5_tip .tipMsg").html("确定删除此合同信息吗")
            $("#bounceFixed5_tip .sureBtn").unbind().on("click", function (){
                $.ajax({
                    url: '../supplier/removeContract.do',
                    data: {
                        id: id
                    }
                }).then(res => {
                    let data = res.success
                    if (data === 1) {
                        layer.msg("操作成功！")
                        bounce_Fixed5.cancel()
                        selector.parents("tr").remove()
                    }
                })
            })
        } else {
            // 新增供应商弹窗 删除合同 直接删除
            selector.parents("tr").remove()
        }
        return false
    }
    $("#editContract").data("type", type);
    $("#editContract").data("selector", selector);
    $("#editContract").data("origin", origin);
    $("#editContract").data("img", "");
    $("#editContract input").val('');
    $("#editContract .file_avatar").html('');
    $("#editContract .clearAll").hide()
    initUploadContract($("#editContract_img"),'img');
    initUploadContract($("#editContract_doc"),'doc');
    if (type === 'update') {
        $("#editContract .bounce_title").html("修改合同");
        // 修改合同的时候需先获取数据填充
        if (supplierType === 'update') {
            // 供应商修改中的联系人修改需要调用接口
            $.ajax({
                url:"../supplier/getContractBase.do",
                data: {
                    id: id
                },
                success:function(res){
                    var thisInfo = res.data
                    thisInfo.signTime = moment(thisInfo.signTime).format("YYYY-MM-DD")
                    thisInfo.validStart = moment(thisInfo.validStart).format("YYYY-MM-DD")
                    thisInfo.validEnd = moment(thisInfo.validEnd).format("YYYY-MM-DD")
                    thisInfo.contractBaseDocs = thisInfo.filePath?[{
                        filePath: thisInfo.filePath,
                        order: 1,
                        type: 1,
                        title: thisInfo.fileName
                    }]:[]
                    fillContract(thisInfo)
                }
            })
        } else {
            // 供应商新增的时候 直接在页面拿（还未进入数据库，静态添加的）
            let thisInfo = JSON.parse(selector.parents("tr").find(".hd").html())
            fillContract(thisInfo)
        }
        
    } else if (type === 'new') {
        $("#editContract .bounce_title").html("新增合同");

    }
    bounce_Fixed3.show($("#editContract"));
}

// creator: 张旭博，2023-08-11 11:51:48， 填充合同弹窗数据
function fillContract(info) {
    $("#editContract input").each(function (){
        let name = $(this).attr("name")
        $(this).val(info[name])
    })
    let imgStr = ''
    if (info.contractBaseImages) {
        for(let it of info.contractBaseImages) {
            imgStr += `<div class="fileItem">
                        <i class="fa fa-file-image-o"></i>
                         <div class="fileName">${it.title}</div>
                         <i class="fa fa-times-circle"></i>
                         <span class="hd">${ JSON.stringify(it) }</span>
                    </div>`
        }
    }
    $("#editContract .imgCon").html(imgStr)
    let docStr = ''
    if (info.contractBaseDocs) {
        for(let item of info.contractBaseDocs) {
            docStr += `<div class="fileItem">
                        <i class="fa fa-file-word-o"></i>
                         <div class="fileName">${item.title}</div>
                         <i class="fa fa-times-circle"></i>
                         <span class="hd">${ JSON.stringify(item) }</span>
                    </div>`
        }
    }
    $("#editContract .docCon").html(docStr)
}

// creator: 张旭博，2023-08-08 09:15:48， 新增合同 - 确定
function sureEditContract() {
    let supplierType = $("#editMtSupplier").data('type')
    let supplierSelector = $("#editMtSupplier").data('selector')
    let type = $("#editContract").data('type')
    let selector = $("#editContract").data('selector')
    let origin = $("#editContract").data('origin')

    $("#editContract .bounce_title").html(type === 'new' ? '新增合同' : '修改合同')
    let data = {
        sn: '',
        signTime: '',
        validStart: '',
        validEnd: '',
        filePath: '',
        fileName: '',
        memo: '',
        contractBaseImages: [],
        contractBaseDocs: []
    }
    for (let key in data) {
        let value = $("#editContract [name='"+key+"']").val()
        if (value) {
            data[key] = value
        }
    }
    $("#editContract .imgCon .fileItem").each(function (index, item) {
        let info = JSON.parse($(this).find(".hd").html());
        data.contractBaseImages.push(info)
    })
    $("#editContract .docCon .fileItem").each(function (index, item) {
        let info = JSON.parse($(this).find(".hd").html());
        data.contractBaseDocs.push(info)
    })
    if (data.contractBaseDocs.length > 0) {
        data.filePath = data.contractBaseDocs[0].filePath
        data.fileName = data.contractBaseDocs[0].title
    }


    if(data.sn === ''){
        layer.msg('请录入合同编号');
        return false;
    }
    if(data.validStart === '' || data.validEnd === ''){
        layer.msg("请把有效期填补完整");
        return false;
    }
    // 不同的新增修改需要额外的传值
    if (supplierType === 'new') {
        if (type === 'new') {
            data.id = Math.random();
        } else {
            data.id = selector.parents("tr").data("id")
        }

    } else {
        let supplierId = $("#editMtSupplier").data("id")
        if (type === 'new') {
            if (origin === 'editPointInfo') {
                supplierId = $("body").data('supplierId')
            }
            data = {
                supplierId: supplierId,
                contractBase: JSON.stringify(data)
            }
        } else if (type === 'update') {
            data.id = selector.parents("tr").data("id")
            data = {
                supplierId: supplierId,
                contractBase: JSON.stringify(data)
            }
        }
    }

    if (supplierType === 'new') {
        let str =  `<tr data-id="${data.id}">
                        <td>${data.sn}</td>
                        <td>${data.signTime}</td>
                        <td>${data.validStart} 至 ${data.validEnd}</td>
                        <td>
                            <span class="link-blue" onclick="editContractBtn($(this), 'update')">修改</span>
                            <span class="link-red" onclick="$(this).parents('tr').remove()">删除</span>
                            <span class="hd" >${JSON.stringify(data)}</span>
                        </td>
                    </tr>`
        if (type === 'new') {
            $("#editMtSupplier .tbl_contract").append(str);
        }else if(type === 'update'){
            selector.parents("tr").replaceWith(str)
        }

    } else {
        if (type === 'new') {
            $.ajax({
                url:"../supplier/addContractBase.do",
                data: data,
                success:function(res){
                    var success = res.success;
                    if(success === 1){
                        layer.msg("操作成功");
                        bounce_Fixed3.cancel();
                        if (origin === 'editPointInfo') {
                            setContractOption()
                        } else {
                            editMtSupplier(supplierSelector, supplierType)
                        }
                    } else {
                        layer.msg("操作失败");
                    }
                }
            })
        } else if (type === 'update') {
            $.ajax({
                url: '../supplier/updateContractBase.do',
                data: data,
                success:function(res){
                    var success = res.success;
                    if(success === 1){
                        layer.msg("操作成功");
                        bounce_Fixed3.cancel()

                        editMtSupplier(supplierSelector, supplierType)
                    } else {
                        layer.msg("操作失败");
                    }
                }
            })
        }
    }
    bounce_Fixed3.cancel()
}

// creator: 张旭博，2023-08-23 01:36:31， 设置合同选项
function setContractOption() {
    let supplierId = $("body").data('supplierId')
    $.ajax({
        url:"../supplier/getContractList.do",
        data:{
            supplierId: supplierId
        },
        success:function(res){
            let str = '<option value="">-----请选择-----</option>';
            let data = res.data
            if(data) {
                for(let item of data){
                    str += `<option value="${item.id}" class='supItem'>${item.sn}(合同编号)</option>`;
                }
            }
            $(".mtAndSupplier [name='contractSn']").html(str);
        }
    })
}

// creator: 张旭博，2023-08-21 03:42:53， 选择联系人
function chooseContactInput() {
    bounce_Fixed3.show( $("#chooseCusContact") );
    $(".tbl_cusContactChoose tbody").html('')
    $.ajax('../dac/contact/list').then(res => {
        let list = res['data'] || [] ;
        let str = ''
        for (let item of list) {
            str += `<tr>
                        <td>
                            <div class="ty-radio">
                                <input type="radio" name="chooseContact" id="chooseCusContact_${item.id}">
                                <label for="chooseCusContact_${item.id}"></label>
                            </div>
                            ${item.name}
                        </td>
                        <td>${item.post && item.post.substr(0,8)}</td>
                        <td>${item.telephone}</td>
                        <td class="hd">${JSON.stringify(item)}</td>
                        <td><span class="link-blue" onclick="seeContactBtn($(this))">查看</span></td>
                    </tr>`
        }
        $(".tbl_cusContactChoose tbody").html(str)
    })
}

// creator: lyt 2022-11-29 收货人那里的新增收货人
function chooseCusContactOk() {
    let selectObj = $("#chooseCusContact").find("input:radio:checked");
    if(selectObj.length > 0){
        let strInfo = selectObj.parents('tr').find(".hd").html();
        let info = JSON.parse(strInfo);
        $("#editReceiveInfo .contactName").val(info.name)
        $("#editReceiveInfo .hd").html(strInfo)
        bounce_Fixed3.cancel();
    } else {
        layer.msg('请先选择人员')
    }
}

// creator: 张旭博，2023-08-21 04:53:59， 新增收货信息 - 确定
function sureEditReceiveInfo() {
    let type = $("#chooseDelivery").data('type')
    let address = $("#editReceiveInfo [name='address']").val()
    let contact = $("#editReceiveInfo [name='contact']").siblings(".hd").html()
    if (address === '') {
        layer.msg('请录入收获地址！')
        return false
    }
    if (contact === '') {
        layer.msg('请选择联系人！')
        return false
    } else {
        contact = JSON.parse(contact)
    }
    let data = {
        type: type,
        contact: contact.name ,
        telephone:contact.telephone,
        address: address
    }
    $.ajax({
        url: '../dac/add',
        data: data,
        beforeSend:function(){ loading.open() ; },
        success: function (data) {
            var status = data.success;
            if (status === 200) {
                bounce_Fixed2.cancel()
                getDeliveryList(type);
            } else {
                layer.msg("新增失败！");
            }
        }
    })
}

// creator: 张旭博，2023-08-08 09:16:07， 新增联系人/修改联系人
function editContactBtn(selector, type) {
    let supplierType = $("#editMtSupplier").data('type')
    let id = selector.parents("tr").data("id")
    let origin = selector.attr('origin')
    if (origin === 'pointInfo') {
        // 来源是定点信息
        $("#editContact").data('origin', 'pointInfo')
    }

    if (type === 'del') {
        if (supplierType === 'update') {
            bounce_Fixed5.show($("#bounceFixed5_tip"))
            $("#bounceFixed5_tip .tipMsg").html("确定删除此联系人信息吗")
            $("#bounceFixed5_tip .sureBtn").unbind().on("click", function (){
                $.ajax({
                    url: '../supplier/deleteCustomerContact.do',
                    data: {
                        contactId: id
                    }
                }).then(res => {
                    var data = res.success
                    if (data === 1) {
                        layer.msg("操作成功！")
                        bounce_Fixed5.cancel()
                        selector.parents("tr").remove()
                    }
                })
            })
        } else if (supplierType === 'new') {
            selector.parents("tr").remove()
        }
        return false
    }
    if($("#editContact .contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
        return false
    }
    bounce_Fixed4.show($("#editContact"))
    // 初始化
    $("#editContact input").val('');
    $("#editContact select").val('');
    $("#editContact .otherContact").html("")
    $("#editContact").data('type', type);
    $("#editContact").data('selector', selector);
    $('#uploadCard').show()
    $("#editContact .bussnessCard").remove();

    initUploadCard($("#uploadCard"));
    if (type === 'update') {
        $("#editContact .bounce_title").html("修改客户联系人");
        if (supplierType === 'update') {
            // 供应商修改中的联系人修改需要调用接口
            $.ajax({
                url:"../supplier/getContactsSocial.do",
                data: {
                    contactId: id
                },
                success:function(res){
                    var data = res.data
                    fillContact(data)
                }
            })
        } else {
            let thisInfo = JSON.parse(selector.parents("tr").find(".hd").html())
            fillContact(thisInfo)
        }
    } else {
        $("#editContact .bounce_title").html("新增客户联系人");
    }
    setTimer('updateContact');
}

// creator: 张旭博，2023-08-11 11:41:36， 填充联系人弹窗
function fillContact(thisInfo) {
    $("#contactFlag").html("其他");
    $("#editContact .bounce_title").html("修改联系人信息");
    $("#contactName").val(thisInfo.name);
    $("#position").val(thisInfo.post);
    $("#contactNumber").val(thisInfo.mobile);
    let cardPath = thisInfo.cardPath || "";       //这里的qImages就已经相当于是那里的normal了
    let imgStr =`<div class="bussnessCard">
                        <div class="filePic" data-path="${cardPath}" style="background-image: url('${$.uploadUrl + cardPath}')"></div>
                        <span fileUid="${thisInfo.fileUid}" class="link-red" onclick="cancleCard($(this))">删除</span>
                    </div>`

    if(cardPath.length > 0) {
        $('#uploadCard').before(imgStr)
        $('#uploadCard').hide()
    }else{
        $("#contactsCard .bussnessCard").remove()
    }
    var otherContactList = thisInfo.socialList;
    let str = ''
    for(let item of otherContactList){
        str += `<div class="item-flex" data-type="${item.type}" data-name="${item.name}">
                    <div class="item-title">${item.name}：</div>
                    <div class="item-content">
                        <input class="ty-inputText" type="text" placeholder="请录入" id="contactName" require value="${item.code}"/>
                        <span class="link-red" onclick="removeAdd($(this))">删除</span>
                    </div>
                </div>`
    }
    $('.otherContact').html(str)
}

// creator: 张旭博，2023-08-08 02:59:58， 新增联系人 - 确定(不同场景)
function sureEditContact() {
    let origin = $("#editContact").data('origin')
    let supplierType = $("#editMtSupplier").data('type') // 供应商状态 - 新增/修改 (new/update)
    let type = $("#editContact").data('type');  // 联系人状态 - 新增/修改 (new/update)
    let selector = $("#editContact").data('selector'); // 初始新增/修改按钮点击位置
    // 新增或者修改时候当时的弹窗名称（如果是新增邮寄信息下的新增联系人，需要将新增的联系人添加到选择列表中）
    let bounceName = selector.parents(".bonceContainer").attr("id")
    // let contactInfo = getContact(supplierType)
    // 基本表格信息
    let data = {
        tags: $("#contactFlag").html(),
        name: $("#contactName").val(),
        post: $("#position").val(),
        mobile: $(" #contactNumber").val(),
        cardPath: '',
        socialList: '[]'
    }
    if (data.name === '') {
        layer.msg("请录入联系人姓名！")
        return false
    }
    if($("#contactsCard .bussnessCard").length > 0){
        data.cardPath = $("#contactsCard .bussnessCard .filePic").data('path');
    }
    let socialList = []
    $("#editContact .otherContact .item-flex").each(function () {
        let val = $(this).find("input").val()
        if (val !== '') {
            socialList.push({
                code: val,
                type: $(this).data('type'),
                name: $(this).data('name')
            })
        }
    })
    data.socialList = JSON.stringify(socialList);

    // 不同的新增修改需要额外的传值
    if (supplierType === 'new') {
        if (type === 'new') {
            var groupUuid= $("#contactsCard").attr("groupUuid")
            data.id = Math.random();
            data.groupUuid = groupUuid;
        } else {
            data.id = selector.parents("tr").data("id")
        }

    } else {
        if (type === 'new') {
            data.supplierId = $("#editMtSupplier").data("id")
        } else if (type === 'update') {
            data.contactId = selector.parents("tr").data("id")
        }

    }
    // 不同场景下的逻辑
    var str = ''
    if (supplierType === 'update') {
        // 修改供应商  新增联系人/修改联系人 （需要调用接口）

        if (type === 'new') {
            $.ajax({
                url:"../supplier/addContact.do",
                data: data,
                success:function(res){
                    var success = res.success;
                    if(success === 1){
                        str = `<tr class="insertItem contactItem" data-id="${res.data}">
                                    <td>${data.name} ${data.post} ${data.mobile}</td>
                                    <td>
                                        <span class="link-blue" onclick="editContactBtn($(this), 'update')">修改</span>
                                        <span class="link-red" onclick="editContactBtn($(this), 'del')">删除</span>
                                        <span class="hd">${JSON.stringify(data)}</span>
                                    </td>
                                </tr>`
                        layer.msg("操作成功");
                        bounce_Fixed4.cancel()
                        $("#changeMtSupplier .contact").after(str)
                        // 从邮寄信息中新增的联系人，添加到联系人列表中
                        if (bounceName === 'editMail') {
                            let str = `<option value="${res.data}" cname="${data.name}" post="${data.post}" mobile="${data.mobile}">${data.name} ${data.post} ${data.mobile}</option>`
                            $("#editMail [name='supplierContact']").append(str)
                            $("#editMail [name='supplierContact']").val(res.data)
                        }
                    } else {
                        layer.msg("操作失败");
                    }
                }
            })
        } else if (type === 'update') {
            str = `<tr class="insertItem contactItem" data-id="${data.contactId}">
                        <td>${data.name} ${data.post} ${data.mobile}</td>
                        <td>
                            <span class="link-blue" onclick="editContactBtn($(this), 'update')">修改</span>
                            <span class="link-red" onclick="editContactBtn($(this), 'del')">删除</span>
                            <span class="hd">${JSON.stringify(data)}</span>
                        </td>
                    </tr>`

            $.ajax({
                url: '../supplier/updateContactsSocialAndCard.do',
                data: data,
                success:function(res){
                    var success = res.success;
                    if(success === 1){
                        layer.msg("操作成功");
                        bounce_Fixed4.cancel()
                        $("#changeMtSupplier .contactItem[data-id='"+data.contactId+"']").replaceWith(str)
                    } else {
                        layer.msg("操作失败");
                    }
                }
            })
        }
    } else if (supplierType === 'new') {
        // 新增供应商  新增联系人/修改联系人 （不调接口）
        str = `<tr data-id="${data.id}">
                    <td>${data.name}</td>
                    <td>${data.post}</td>
                    <td>
                        <span class="link-blue" onclick="editContactBtn($(this), 'update')">修改</span>
                        <span class="link-red" onclick="editContactBtn($(this), 'del')">删除</span>
                        <span class="hd">${JSON.stringify(data)}</span>
                    </td>
                </tr>`
        if (type === 'new') {
            // 从邮寄信息中新增的联系人，添加到联系人列表中
            if (bounceName === 'editMail') {
                let str = `<option value="${data.id}" cname="${data.name}" post="${data.post}" mobile="${data.mobile}">${data.name} ${data.post} ${data.mobile}</option>`
                $("#editMail [name='supplierContact']").append(str)
                $("#editMail [name='supplierContact']").val(data.id)
            }
            $("#editMtSupplier .tbl_contact").append(str);
        }else if(type === 'update'){
            selector.parents("tr").replaceWith(str)
        }
        bounce_Fixed4.cancel()
    }

    if (origin === 'pointInfo') {
        var arr = []
        data = {
            'name': $("#contactName").val(),
            'post': $("#position").val(),
            'telephone': $("#contactNumber").val(),
            'cardPath': ''
        };
        if($("#contactsCard .bussnessCard").length > 0){
            data.cardPath = $("#contactsCard .bussnessCard .filePic").data('path');
        }
        if($(".otherContact li").length > 0){
            $(".otherContact li").each(function () {
                if ($(this).find('input').val() != '') {
                    var json = {
                        'code': $(this).find("input").val(),
                        'type': $(this).find("input").data('type'),
                        'name': $(this).find("input").data('name')
                    };
                    arr.push(json);
                }
            })
        }
        let param = {
            "deliveryContact": data,
            "contactSocials": arr
        }
        $.ajax({
            url: '../dac/contact/add',
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            data: JSON.stringify(param),
            success: function (res) {
                data.id = res.data.deliveryContact.id;
                var status = res.success;
                if (status === '200' || status === 200) {
                    layer.msg('新增成功')
                    var groupUuidArr = []
                    $("#newContectInfo [groupUuid]").each(function () {
                        groupUuidArr.push({
                            type: 'groupUuid',
                            groupUuid: $(this).attr("groupUuid")
                        })
                    })
                    cancelFileDel(groupUuidArr)
                    // 给当前的赋值
                    data.contact = data.name
                    $("#editReceiveInfo .contactName")
                        .val(data.name).data('orgData', data.name)
                        .siblings(".hd").html(JSON.stringify(data));
                    bounce_Fixed4.cancel();
                } else {
                    layer.msg("新增失败！");
                }
            }
        })
    }
}

// creator: 张旭博，2023-08-11 08:32:13， 获取当前联系人数据
function getContact(supplierType) {
    let contactArr = []
    if (supplierType === 'new') {
        $("#editMtSupplier .tbl_contact tbody tr").each(function () {
            let info = $(this).find(".hd").html()
            contactArr.push(JSON.parse(info))
        })
    } else if (supplierType === 'update') {
        $("#changeMtSupplier .contactItem").each(function () {
            let info = $(this).find(".hd").html()
            contactArr.push(JSON.parse(info))
        })
    }
    return contactArr
}

// creator: 张旭博，2023-08-08 09:16:07， 新增邮寄信息 - 按钮
function editMailBtn(selector, type) {
    let supplierType = $("#editMtSupplier").data('type')
    let id = selector.parents("tr").data("id")

    if (type === 'del') {
        if (supplierType === 'update') {
            bounce_Fixed5.show($("#bounceFixed5_tip"))
            $("#bounceFixed5_tip .tipMsg").html("确定删除此邮寄信息吗")
            $("#bounceFixed5_tip .sureBtn").unbind().on("click", function (){
                $.ajax({
                    url: '../supplier/removeAddress.do',
                    data: {
                        addressId: id
                    }
                }).then(res => {
                    let data = res.success
                    if (data === 1) {
                        layer.msg("操作成功！")
                        bounce_Fixed5.cancel()
                        selector.parents("tr").remove()
                    }
                })
            })
        } else {
            selector.parents("tr").remove()
        }
        return false
    }
    bounce_Fixed3.show($("#editMail"))
    $("#editMail").data('type', type);
    $("#editMail").data('selector', selector);

    // 初始化
    $("#editMail input").val('');
    $("#editMail select").val('');
    let contactData = getContact(supplierType)
    let contactStr = '<option value="">----请选择----</option>'
    if (contactData) {
        for (let item of contactData) {
            contactStr += `<option value="${item.id}" cname="${item.name}" post="${item.post}" mobile="${item.mobile}">${item.name} ${item.post} ${item.mobile}</option>`
        }
    }
    $("#editMail [name='supplierContact']").html(contactStr)

    let title = ''
    if (type === 'update') {
        title = '修改邮寄信息'
        if (supplierType === 'update') {
            // 供应商修改中的联系人修改需要调用接口
            $.ajax({
                url:"../supplier/getAddressData.do",
                data: {
                    addressId: id
                },
                success:function(res){
                    var thisInfo = res.data
                    fillMail(thisInfo)
                }
            })
        } else {
            let thisInfo = JSON.parse(selector.parents("tr").find(".hd").html())
            fillMail(thisInfo)
        }
    } else {
        title = '新增邮寄信息'
    }
    $("#editMail .bounce_title").html(title)
    initUploadCard($("#uploadCard"));
    // setTimer('updateContact');
}

// creator: 张旭博，2023-08-11 11:46:14， 填充邮寄信息弹窗
function fillMail(thisInfo) {
    $("#editMail [name]").each(function () {
        let name = $(this).attr("name")
        $(this).val(thisInfo[name])
    })
}

// creator: 张旭博，2023-08-08 09:30:58， 新增邮寄信息 - 确定
function sureEditMail() {
    let supplierType = $("#editMtSupplier").data('type') // 供应商状态 - 新增/修改 (new/update)
    let type = $("#editMail").data('type')
    let selector = $("#editMail").data('selector')

    let address = $("#editMail [name='address']").val()
    let postcode = $("#editMail [name='postcode']").val()
    let contactName = $("#editMail [name='supplierContact'] option:selected").attr('cname')
    let contactMobile = $("#editMail [name='supplierContact'] option:selected").attr('mobile')
    let contactId = $("#editMail [name='supplierContact']").val()
    let data = {
        supplierContact: contactId,
        address: address,
        postcode: postcode,
        contact: contactName,
        mobile: contactMobile
    }
    // 不同的新增修改需要额外的传值
    if (supplierType === 'new') {
        if (type === 'new') {
            data.id = Math.random();
        } else {
            data.id = selector.parents("tr").data("id")
        }

    } else {
        if (type === 'new') {
            data = {
                supplierId: $("#editMtSupplier").data("id"),
                address: JSON.stringify(data)
            }
        } else if (type === 'update') {
            data.id = selector.parents("tr").data("id")
        }
    }

    // 不同场景下的逻辑
    var str = ''
    if (supplierType === 'update') {
        // 修改供应商  新增邮寄信息/修改邮寄信息 （需要调用接口）
        let str = `<tr class="insertItem mailItem" data-id="${data.id}">
                        <td>${address}</td>
                        <td>
                            <span class="link-blue" onclick="editMailBtn($(this), 'update')">修改</span>
                            <span class="link-red" onclick="editMailBtn($(this), 'del')">删除</span>
                            <span class="hd">${JSON.stringify(data)}</span>
                        </td>
                    </tr>`
        if (type === 'new') {
            $.ajax({
                url:"../supplier/addSupplierAddress.do",
                data: data,
                success:function(res){
                    var success = res.success;
                    if(success === 1){
                        layer.msg("操作成功");
                        bounce_Fixed3.cancel()
                        $("#changeMtSupplier .mail").after(str)
                    } else {
                        layer.msg("操作失败");
                    }
                }
            })
        } else if (type === 'update') {

            $.ajax({
                url: '../supplier/updateSupplierAddress.do',
                data: data,
                success:function(res){
                    var success = res.success;
                    if(success === 1){
                        layer.msg("操作成功");
                        bounce_Fixed3.cancel()
                        $("#changeMtSupplier .mailItem[data-id='"+data.id+"']").replaceWith(str)
                    } else {
                        layer.msg("操作失败");
                    }
                }
            })
        }
    } else if (supplierType === 'new') {
        // 新增供应商  新增邮寄信息/修改邮寄信息 （不调接口）
        let str = `<tr data-id="${data.id}">
                        <td>${data.address}</td>
                        <td>${data.contact}</td>
                        <td>${data.postcode}</td>
                        <td>${data.mobile}</td>
                        <td>
                            <span class="link-blue" onclick="editMailBtn($(this), 'update')">修改</span>
                            <span class="link-red" onclick="editMailBtn($(this), 'del')">删除</span>
                            <span class="hd">${JSON.stringify(data)}</span>
                        </td>
                    </tr>`
        if (type === 'new') {
            $("#editMtSupplier .tbl_mail").append(str);
        }else if(type === 'update'){
            selector.parents("tr").replaceWith(str)
        }
        bounce_Fixed3.cancel()
    }
}

// creator: 张旭博，2023-08-08 01:58:14， 联系人 - 查看
function seeContactBtn(selector) {
    let contactInfo = selector.parents("tr").find(".hd").html()
    let thisInfo = JSON.parse(contactInfo)
    for (let key in thisInfo) {
        $('#seeContact .see_' + key).html(thisInfo[key])
    }
    bounce_Fixed5.show($('#seeContact'));
}

// creator: sy 2022-05-13 邮寄信息列表
function setMailList(list){
    var type = $("#newMailInfo").data('type');
    if(list.length == 0){//条件语句
        $(".receiveList").html("");
    }else{
        for(var i=0;i < list.length;i++){
            var num = 1 + Number(i);
            var html1 = `
           
                <tr data-id="${list[i].customerContract}">
                    <td id="ads">${list[i].address}</td>
                    <td id="ctt">${list[i].contact}</td>
                    <td id="ptd">${list[i].postcode}</td>
                    <td id="mbe">${list[i].mobile}</td>
                    <td>
                        <span class="ty-color-blue node" data-type="update" data-source="addCustomer" data-name="mailInfo" onclick="updateen()">修改</span>
                        <span class="ty-color-red node dettle" data-type="delete" data-name="mailInfo" id="without" onclick="deletope($(this))">删除</span>
                        <span class="hd">${JSON.stringify(list[i])}</span>
                    </td>
                </tr>
            `;
            var html2 = `
                <td id="ads">${list[i].address}</td>
                <td id="ctt">${list[i].contact}</td>
                <td id="ptd">${list[i].postcode}</td>
                <td id="mbe">${list[i].mobile}</td>
                <td>
                    <span class="ty-color-blue node" data-type="update" data-source="addCustomer" data-name="mailInfo" onclick="updateen()">修改</span>
                    <span class="ty-color-red node dettle" data-type="delete" data-name="mailInfo" id="without" onclick="deletope($(this))">删除</span>
                    <span class="hd">${JSON.stringify(list[i])}</span>
                </td> `;
            if(type == "new"){
                $(".receiveList .mailPlaceList #clist2").append(`${html1}`);
            }else if(type == "update"){
                var uopon = $("#without").parent().parent();
                uopon.html(`${html2}`);
            }
        }
    }
    var mailInfo = list;
    mailInfo = JSON.stringify(mailInfo);
    $("#addAccount").data("mailInfo",mailInfo);
    $("#clist2").show();
}

// creator: sy 2022-07-05 新增联系人弹窗中添加更多联系方式
function addMore(obj){
    obj.parents('.item-flex').find("select").show();
}

// creator: sy 2022-07-05 添加更多联系人
function addMoreChange(selector){
    let value = selector.val()
    let content = selector.find("option:checked").html()
    if (value === '') {
        return false
    }
    let str = ''
    // 清空没有录入值的联系方式
    $(".otherContact .item-flex").each(function(){
        let value = $(this).find('input').val();
        if (value === '') $(this).remove();
    })
    if (value === '9') {
        // 自定义
        $("#useDefinedLabel input").val("");
        bounce_Fixed5.show($("#useDefinedLabel"));
        setTimer('useDefinedLabel');
    } else {
        str += `<div class="item-flex" data-type="${value}" data-name="${content}">
                    <div class="item-title">${content}：</div>
                    <div class="item-content">
                        <input class="ty-inputText" type="text" placeholder="请录入" id="contactName" require/>
                        <span class="link-red" onclick="removeAdd($(this))">删除</span>
                    </div>
                </div>`
    }

    $(".otherContact").append(str);
    $("#addMoreContact").val('').hide()
}

// creator: sy 2022-07-05 联系人自定义标签确定
function addNewContactLabel(){
    var value =$("#defLabel").val();
    let str = `<div class="item-flex" data-type="9" data-name="${value}">
                    <div class="item-title">${value}：</div>
                    <div class="item-content">
                        <input class="ty-inputText" type="text" placeholder="请录入" id="contactName" require/>
                        <span class="link-red" onclick="removeAdd($(this))">删除</span>
                    </div>
                </div>`
    $(".otherContact").append(str);
    bounce_Fixed5.cancel();
}

// creator: sy 2022-06-14 删除添加的联系方式
function removeAdd(obj){
    obj.parents(".item-flex").remove();
}

// creator: sy 2022-07-05 删除名片
function cancleCard(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    $('#uploadCard').show();
}


// creator: sy 2022-07-05 删除文件相关方法
function cancelFileDel(option, isDel){
    console.log(typeof option)
    let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]');
    // 传值为 all，赋值为存储的所有文件
    if (option === 'all') {option = fileDelArr}

    // 判断传值类型
    if (typeof option === 'object') {
        if (!isArrayFn(option)) { option = [option] } // 如果为对象转换为数组
        option.forEach(function (it) {
            let n = -1
            fileDelArr.forEach(function(item,index){
                if(it.type == item.type && it[it.type] == item[item.type]){
                    n = index;
                    fileDelArr.splice(index, 1);
                    // clearTimeout(item['fileDelTimer']);
                }
            })
            if(n === -1 ){
                console.log('可能面临风险');
                console.log('没匹配到：', option);
            }
            // 如果传了此字段为true，那么也同时会调用删除文件接口
            if (isDel) {
                let type = it.type
                let url = $.webRoot+'/uploads/removeByFile.do' ; // 默认按照文件删除
                let data = {
                    fileUid: it[type],
                    userId: sphdSocket.user.userID
                }
                if (type === 'groupUuid'){
                    url = $.webRoot+'/uploads/removeFilesByGroup.do' ;
                    data = {
                        groupUuid: it[type],
                        userId: sphdSocket.user.userID
                    }
                }
                $.ajax({url: url, data: data, beforeSend: function () {}, error: function () {}});
            }
        })
        window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
    } else {
        console.log('类型错误')
    }

}

// creator: sy 2022-07-05 图片删除
function cancelFile(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent(".imgsthumb").remove();
}

// creator: sy 2022-07-05 复选框相关方法
function chargeshow8(){
    // var bepont = $("#bepont").val();xytimeomre
    // var vapont = $("#vapont").val();
    // debugger;
    var haveInvoice = $("#haveInvoice").val();
    var invoiceCategory = $("#vatInvoice").val();
    if(haveInvoice === "1" && invoiceCategory === "1"){
        $(".hang8").show();
    }else{
        $(".hang8").hide();
    }
}

// creator: sy 2022-05 批量导入弹窗
function contractShow(){
    layer.msg("功能完善中，敬请期待。");
}

// creator: sy 2022-08-11 点击新增供应商弹窗中新增邮寄信息下的选择联系人弹窗中的“查看”按钮
function lxrlook(ogjc){
    let lxr = ogjc.siblings(".hd").html();
    lxr = JSON.parse(lxr);
    lxr.name.length>=0 ? $("#con-contactName").html(`${handleNull(lxr.name)}`):$("#con-contactName").html("");
    lxr.post.length >= 0 ? $("#con-contactpost").html(`${handleNull(lxr.post)}`):$("#con-contactpost").html("");
    lxr.tags == null || lxr.tags == 'null' ? $("#con-contacttag").html(""):$("#con-contacttag").html(`${handleNull(lxr.tags)}`);
    $("#look1").hide();
    $("#addmore").show();
    bounce_Fixed2.cancel($('#newMailInfo'));
    bounce_Fixed3.cancel($('#chooseCusContact'));
    bounce_Fixed.show($("#contacts"));
}

// creator: sy 2022-05 处理null
function handleNull(str){
    var result = str == null || str == undefined || str == 'null' || ''?'0':str;
    return result;
}


// creator: sy 2022-05-28 时间戳转换为日期
function formatDateTime(inputTime, type='month') {
    if (type === 'month') {
        return moment(inputTime).format("YYYY-MM-DD")
    } else if (type === 'dateTime') {
        return moment(inputTime).format("YYYY-MM-DD HH:mm:ss")
    } else {
        console.log('不支持的格式转换')
        return ''
    }
}

// creator: sy 2022-07-05 供应商查看
function havelooko(selector){
    $("#prient").hide();
    bounce_Fixed.show($("#havelook"));
    $(".addOtherInfo").show();
    var supplierInfo = JSON.parse(selector.parents("tr").find(".hd").html());
    var lookid = supplierInfo.id
    $("#havelook").data("id",lookid);
    if(lookid == undefined || lookid == ""){
        $("#tiphoto .shu1").html("加载错误，请稍后再试。");
        $("#beforeof").show();
        $("#overstop").hide();
        $("#maisure").show();
        $("#stopway").hide();
        bounce_Fixed.show($("#tiphoto"));
        return false;
    }
    $("#havelook").data("coid",lookid);
    $.ajax({
        url:"../supplier/getSrmSupplierOne.do",
        data:{
            id:lookid,
        },
        success:function(res){
            console.log(res);
            var tr_ser = selector.parent().parent();
            sui_seeTrser = tr_ser;
            var messge = res.data;
            var code = messge["codeName"];
            var oper = messge["operation"];
            var uaname = messge["name"];
            var uafullName = messge["fullName"];
            var createName = messge["createName"];
            var createDate = new Date(messge["createDate"]).format('yyyy-MM-dd hh:mm:ss');
            var qImages = messge["qImages"];
            var contactsList = messge["contactsList"] || [];//联系人
            var yjAddressList = messge["yjAddressList"] || [];//邮寄地址
            var contractBaseList = messge["contractBaseList"] || [];

            // var date = '';
            var strDate = '';


            var chargePeriod = messge["chargePeriod"] || 0;
            var taxRate = messge["taxRate"] || 0;
            $("#see_codde").html(code);
            $("#see_name").html(uafullName);
            $("#see_fullname").html(uaname);
            $("#see_createName").html(createName);
            $("#see_createDate").html(createDate);
            $("#qImages .imgsthumb").remove();
            $("#qImages").data('orgData',qImages);
            if(qImages.length > 0){
                var imgStr = '';
                for(var a=0;a<qImages.length;a++){
                    var path = qImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">'+
                        '   <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path +')"></div>'+
                        '   <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>'+
                        '</div>';
                }
                $("#overallImgUpload").html(imgStr);
            }else{
                $("#overallImgUpload").html("");
            }
            $("#overallImgUpload").html(imgStr);

            //账期
            var str1 = "";
            if(messge.chargeAcceptable == "1"){
                str1 +="<p style='margin:0 0 0 ;'>可接受挂账,";
                if(messge.chargePeriod){
                    str1 +="账期<span>"+messge.chargePeriod+"</span>天,</p>";
                }
                if(messge.chargeBegin == "1"){
                    str1 += "自货物入库之日起计算,</p>";
                }else if(messge.chargeBegin == "2"){
                    str1 += "自发票入账之日起计算,</p>";
                }
            }else{
                str1 += "<p style='margin:0 0 0 ;'>不接受挂账,";
            }
            if(messge.isImprest == "1"){
                str1 += "需要预付款.</p>";
            }else{
                str1 += "不需要预付款,</p>";
            }
            $("#gotpose").html(str1);

            //税率
            var str2 ="";//draftAcceptable
            let draftAcceptable = Number( messge.draftAcceptable );
            if(messge.invoicable == "1"){
                if(messge.vatsPayable == "1"){
                    if(messge.taxRate){
                        str2 += "<p style='margin: 0 0 0;'>该供应商能开税率为"+messge.taxRate+"%的增值税专用发票,</p>";
                    }else{
                        str2 += "<p style='margin:0 0 0 ;'>该供应商能开增值税专用发票,</p>";
                    }
                }else{
                    str2 += "<p style='margin:0 0 0 ;'>该供应商仅能开普通发票,</p>";
                }
                if(draftAcceptable === 0){
                    str2 += "不确定能接受承兑汇票.</span>";
                }else {
                    str2 += "可接收承兑汇票.</span>";
                }
            }else{
                str2 += "<p style='margin: 0 0 0 ;'>该供应商不能开发票,</p>";
            }
            $("#shuil").html(str2);


            if(contractBaseList.length == 0){
                $("#clist11").html("");
            }else{
                var str1 = ``;
                for(let n in contractBaseList){
                    let contrat = contractBaseList[n];
                    if(contrat.signTime == null || contrat.validStart == null || contrat.validEnd == null){
                        str1 +=`
                            <tr>
                                <td>${contrat.sn}</td>
                                <td></td>
                                <td></td>                 
                                <td>                            
                                    <span class='ty-color-blue node lookcon' onclick="lookcnent($(this))">查看</span>                            
                                    <span class="hd">${JSON.stringify(contrat)}</span>                        
                                </td> 
                            </tr>                   
                        `;
                        $("td:empty").html("");
                        $("#clist11").html(str1);
                    }else{
                        str1 +=`
                        <tr>                        
                            <td>${(contrat.sn)}</td>                        
                            <td>${formatDateTime(handleNull(contrat.signTime))}</td>                        
                            <td>${formatDateTime(handleNull(contrat.validStart))}至${formatDateTime(handleNull(contrat.validEnd))}</td>                                              
                            <td>                            
                                <span class='ty-color-blue node lookcon' onclick="lookcnent($(this))">查看</span>                            
                                <span class="hd">${JSON.stringify(contrat)}</span>                        
                            </td>                    
                        </tr>`;
                    }
                    $("#clist11").html(str1);
                }
            }
            if(yjAddressList.length == 0){
                $("#clist21").html("");
            }else{
                var str2 =``;
                for(let i in yjAddressList){
                    let yjaddre = yjAddressList[i];
                    str2 +=`
                    <tr>                        
                        <td>${handleNull(yjaddre.address)}</td>                        
                        <td>${handleNull(yjaddre.name)}</td>                        
                        <td>${handleNull(yjaddre.postcode)}</td>                        
                        <td>${handleNull(yjaddre.mobile)}</td>                                          
                    </tr>`;
                }
                $("#clist21").html(str2);
            }
            if(contactsList.length == 0){
                $("#clist31").html("");
            }else{
                var str3 = ``;
                var str4 = ``;
                for(let j in contactsList){
                    let contacts = contactsList[j];
                    str3 += `
                    <tr>                        
                        <td>${contacts.name}</td>                        
                        <td>${contacts.post}</td>                        
                        <td>${contacts.mobile}</td>                        
                        <td>                            
                            <span class='ty-color-blue node' onclick="contway($(this))">联系方式</span>                            
                            <span class='ty-color-blue node' onclick="lookpictue($(this))" id="postcord">查看名片</span>                            
                            <span class="hd">${JSON.stringify(contacts)}</span>                        
                        </td>                    
                    </tr>`;
                }
                $("#clist31").html(str3);
                str4 +=`${contactsList.length}`;
                $(".contactNum").html(str4);
            }
        }
    })
}

// creator: sy 2022-07-12 供应商查看窗口中“合同信息“里的查看
function lookcnent(lookli){
    bounce_Fixed3.show($("#lookpoot"));
    $("#conook").show();
    $("#uppook").hide();
    $("#chone").hide();
    $("#stoppen").hide();
    let into = lookli.siblings(".hd").html();
    into = JSON.parse(into);
    let contractId = into.id;
    $.ajax({
        "url":"../supplier/getContractBase.do",
        "data":{
            "id":contractId
        },
        success:function(res){
            let data = res.data;
            $("#contentmain").html(`${data.createName} ${ new Date(data.createDate).format("yyyy-MM-dd")  } `);
            $("#contentmainSn").html(data.sn);
            $("#contentSignDate").html( new Date(data.signTime).format("yyyy-MM-dd") );
            $("#contentValidDate").html(`${new Date(data.validStart).format("yyyy-MM-dd")} 至 ${ new Date(data.validEnd).format("yyyy-MM-dd")  } `);

            let imgList = data.contractBaseImages || [];
            let mtList = data.mtList || [];
            $("#looken").data("link",mtList);


            let imgStr = ``
            imgList.forEach((imgItem)=>{
                imgStr += `<span class="ty-color-blue node" onclick="seePic($(this))" path="${ imgItem.filePath }">${ imgItem.orders }</span>`
            });
            $("#contentImgs").html(imgStr)
            $("#contentTxt").attr('path', data.filePath)
            $("#contentMemo").html( data.memo)
            $("#goodsNum").html(`${ data.mtList.length }种`)

            let enabledList = data.enabledList || [];
            let enableStr ='';
            enabledList.forEach((enableditem)=>{
                enableStr +=`<p style="text-align: right;">${ enableditem.enabled == 1 ? '恢复履约/重启合作':'暂停履约/终止合作' }<span>${ enableditem.name} ${new Date(enableditem.enabledTime).format("yyyy-MM-dd")}</span></p>`
            })
            $("#contentEnable").html(enableStr);
        }
    })
}

// creator: sy 2022-09-15 供应商信息查看中查看名片弹窗
function lookpictue(bon){
    var pit = bon.siblings(".hd").html();
    pit = JSON.parse(pit);
    var unid = pit.id;
    $.ajax({
        url:"../supplier/getContactsSocial.do",
        data:{
            contactId:unid
        },
        success:function(res){
            var picture = res.data.cardPath;
            if(picture && picture != "" && picture != null){
                $('#poncune').html('<img style="width:300px;height: 300px" src="' + $.fileUrl + picture +'" />');
            }else{
                $('#poncune').html('');
            }
            bounce_Fixed2.show($("#phunte"));
        }
    })

}

// creator: sy 2022-05-20 图片预览
function imgViewer(obj) {
    //debugger;
    var src = obj.attr('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");//图片淡入淡出效果
}

// creator: 张旭博，2023-09-14 04:55:40， 图片上传(供应商全景照片）
function initUploadSupplier(obj){
    var groupUuid = sphdSocket.uuid();
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"上传",
        formData:{
            module: '供应商',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data);
            let len =  obj.siblings(".file_avatar").find('.imgsthumb').length;
            if(len < 9){
                var path = data.filename //路径（包含文件类型）
                obj.parent().attr("groupUuid", data.groupUuid )
                let imgStr = `<div class="imgsthumb">
                                    <div class="filePic" data-path="${path}" style="background-image: url('${$.uploadUrl + path}')"></div>
                                    <span fileUid="${data.fileUid}" onclick="cancelFile($(this))">删除</span>
                                    <a path="${path}" onclick="imgViewer($(this))">预览</a>
                                </div>`
                obj.siblings(".file_avatar").append(imgStr);
            }else{
                layer.msg('最多只能上传9张')
                let fileUid = data.fileUid
                cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
            }
        }
    });
}

// creator: 张旭博，2023-09-14 04:55:57， 图片上传(合同）
function initUploadContract(obj,type){
    let fileTypeExtsStr = ''
    let multi = true
    let groupUuid = sphdSocket.uuid();
    if(type === "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(type === "doc"){
        multi = false
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    }
    let itemTemplate = ``
    obj.html("")
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi: multi,
        buttonText:"上传",
        formData:{
            module: '供应商',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data);
            let saveData = {}
            if(type === "img"){
                let len =  obj.parents('.uploadCon').find('.file_avatar').find(".fileItem").length;
                if(len < 9){
                    saveData = {
                        orders: len + 1,
                        filePath: data.filename,
                        title: data.originalFilename,
                        type: 1
                    }
                    let imgStr1 = `<div class="fileItem">
                                       <i class="fa fa-file-image-o"></i>
                                       <div class="fileName">${saveData.title}</div>
                                       <i class="fa fa-times-circle ty-right"></i>
                                       <span class="hd">${ JSON.stringify(saveData) }</span>
                                  </div>`
                    obj.parents('.uploadCon').find('.file_avatar').append(imgStr1);
                }else{
                    layer.msg('最多只能上传9个文件')
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                }
            }else if(type === "doc"){
                let files = obj.siblings('.file_avatar').find(".fileIm");
                if(files.length > 0) {
                    cancelFileDel([{'type':'fileId', 'fileId':data.fileUid } ], true);
                    files.remove();
                }
                saveData = {
                    orders: 1,
                    filePath: data.filename,
                    title: data.originalFilename
                }
                let str2 = `<div class="fileItem">
                                <i class="fa fa-file-word-o"></i>
                                 <div class="fileName">${saveData.title}</div>
                                 <i class="fa fa-times-circle ty-right"></i>
                                 <span class="hd">${ JSON.stringify(saveData) }</span>
                            </div>`
                obj.siblings('.file_avatar').html(str2);
            }
        }
    })
}

// creator: 李玉婷，2019-09-17 15:09:14，名片上传
function initUploadCard(obj){
    obj.html('')
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        itemTemplate: '',
        formData:{
            module: '采购',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var path = data.filename //路径（包含文件类型）
            obj.parent().attr("groupUuid", data.groupUuid )
            var imgStr =
                '<div class="bussnessCard">' +
                '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '   <span fileUid="' + data.fileUid + '" class="link-red" onclick="cancleCard($(this))">删除</span> ' +
                '</div>';
            $('#uploadCard').hide();
            $('#uploadCard').before(imgStr);
        }
    });
}

// creator: sy 2022-05 新增验证
function is_postcode(postcode){
    if( postcode == ""){
        return true;
    }else{
        if(!/^[0-9][0-9]{5}$/.test(postcode)){
            return false;
        }
    }
    return true;
}

// creator: 张旭博，2023-08-10 08:35:47， 供应商修改 - 基本信息 - 修改
function mtSupplierPartEdit(selector, name) {
    $("#sureEditMtSupplierBtn").prop("disabled", false)
    let id = $("#editMtSupplier").data("id")
    let title = '修改基本信息'
    if (name === 'base') {
        title = '修改基本信息'
        $("#editMtSupplier section.base").show().siblings().hide()
        $("#editMtSupplier section.base .section_title").hide()
        initUploadSupplier($("#edit_panoramaBtn"));
        // 获取设置的信息
        $.ajax({
            url: '../supplier/getSupplierBase.do',
            data: {
                id: id,
            },
            success:function(res){
                console.log(res);
                let data = res.data
                let qImages = data.qImages;
                $("#editMtSupplier [name='fullName']").val(data.fullName)
                $("#editMtSupplier [name='name']").val(data.name)
                $("#editMtSupplier [name='codeName']").val(data.codeName)
                $("#editMtSupplier .file_avatar").html('');
                $("#edit_qImages").data('orgData',qImages);
                if(qImages.length > 0){
                    var html = setImgHtml(2, qImages);
                    $("#editMtSupplier .file_avatar").html(html);
                }
                // 渲染单选部分
                $("#editMtSupplier .hidePart").hide()
                $("#editMtSupplier input:radio").prop('checked', false)
                setRelation(data)

                for (let name in data) {
                    $("#editMtSupplier input[name='"+name+"'][value='"+data[name]+"']").prop("checked", true)
                    $("#editMtSupplier input:not(:radio)[name='"+name+"']").val(data[name])
                }
                // $("#editMtSupplier .hidePart").hide()
                // if (data.chargeAcceptable === '1') {
                //     $("#editMtSupplier .part_isChargeBegin").css('display', 'flex')
                //     $("#editMtSupplier .part_chargePeriod").show()
                // }
                // if (data.isImprest === '1') {
                //     $("#editMtSupplier .part_isImprestProportion").css('display', 'flex')
                // }
                // if (data.invoicable === '1') {
                //     $("#editMtSupplier .part_isVatsPayable").css('display', 'flex')
                //     $("#editMtSupplier .part_isDraftAcceptable").css('display', 'flex')
                //     if (data.vatsPayable === '1') {
                //         $("#editMtSupplier .part_taxRate").show()
                //     }
                // }
                // var chargeAcceptable = data.chargeAcceptable;
                // setHangAccount(chargeAcceptable);
                //
                // var chargeBegin = res.data.chargeBegin;
                // setStartDate(chargeBegin);
                //
                // var isImprest = res.data.isImprest;
                // setAdvanceFee(isImprest);

                // var imprestProportion = res.data.imprestProportion;
                // if(imprestProportion === null || imprestProportion === "" || imprestProportion === "null"){
                //     $("#e_gRate1").val("");
                // }else{
                //     $("#e_gRate1").val(imprestProportion);
                // }
                //
                // var chargePeridod = res.data.chargePeriod;
                // if(chargePeridod === null || chargePeridod === "" || chargePeridod === "null"){
                //     $("#spco、ntwek").val("");
                // }else{
                //     $("#spcontwek").val(res.data.chargePeriod);
                // }
                //
                // var imprestProportion = res.data.imprestProportion;
                // if(imprestProportion === -1){//当选定比例不确定时
                //     $("#uncertainty1").attr("class","fa fa-dot-circle-o");
                //     // $("#e_gRate1").attr("disabled","disabled");
                //     // $("#e_gRate1").css("background","rgb(221, 221, 221)");
                //     $("#e_gRate1").val("");
                // }else{
                //     // $("#e_gRate1").removeAttr("disabled");
                //     $("#e_gRate1").css("background","white");
                //     $("#uncertainty1").attr("class","fa fa-circle-o");
                //     $("#e_gRate1").val(imprestProportion);
                // }
            }
        })
    } else if (name === 'invoice') {
        title = '修改开票信息'
        $("#editMtSupplier section.invoice").show().siblings().hide()
        $("#editMtSupplier section.invoice .par_ttl").hide()
        $("#sureEditMtSupplierBtn").prop('disabled', false)

        $.ajax({
            url: '../saleSupplier/editSupplierInvoice',
            data:{ supplierId: id},//供应商id
            success:function(res){
                let data = res
                $("#editMtSupplier .hidePart").hide()
                $("#editMtSupplier input:radio").prop('checked', false)
                setRelation(data)

                for (let name in data) {
                    $("#editMtSupplier input[name='"+name+"'][value='"+data[name]+"']").prop("checked", true)
                    $("#editMtSupplier input:not(:radio)[name='"+name+"']").val(data[name])
                }
            }
        })
    }
    $("#editMtSupplier .bounce_title").html(title)
    bounce_Fixed2.show($("#editMtSupplier"))

}

// creator: sy 2022-05 是否能开发票(新增供应商弹窗中的)
function haveInvoice(type,thisObj){
    setRadioSelect("haveInvoice", [0,1],type,thisObj);
    var haveInvoice = $("#haveInvoice").val();
    if(haveInvoice === "1"){
        $("#vatInvoice1").attr("class","fa fa-dot-circle-o");
        $("#vatInvoice2").attr("class","fa fa-circle-o");
        setRadioSelect("vatInvoice",[1,2],type,thisObj);
        $(".godemo_1").show();
    }else{
        $("#vatInvoice1").attr("class","fa fa-circle-o");//左侧清除点
        $("#vatInvoice2").attr("class","fa fa-circle-o");//右侧清除点
    }
    if(type === 1 || type === "1"){
        $(".godemo").show();
        $(".hp").show();
        return false;
    }else{
        $(".godemo").hide();
        $(".hp").hide();
    }
    return false;
}

// creator: sy 2022-06-05 是否能开增值税发票
function vatInvoice(type, thisObj){
    setRadioSelect("vatInvoice",[0,1],type,thisObj);
    var vatInvoice = $("#vatInvoice").val();
    if(vatInvoice === "1"){
        $(".godemo_1").show();
        // $("#e_gRate0").val("");
        $("#vatInvoice2").attr("class","fa fa fa-circle-o");
    }else{
        $(".godemo_1").hide();
        $("#vatInvoice2").attr("class","fa fa-dot-circle-o");
        // $("#e_gRate0").val("");
    }
    if(type == 1 || type == "" || type == null){
        $(".godemo_1").show();
        return false;
    }else{
        $("#huip").hide();
        $(".godemo_1").hide();
    }
}

// creator: sy 2022-06-05 是否可接受汇票
function huip(type, thisObj){
    setRadioCSelect("huip",[1,0],type,thisObj);
}




// creator: sy 2022-08-18 设置输入框数字
function clearNoNum(input){
    // if (input.value < 0) input.value = 0;
    // if (input.value > 100) input.value = 100;
    input.value = input.value.replace(/[^\d.]/g,"");//清除“数字”和“.“以外的字符
    input.value = input.value.replace(/\.{2,}/g,".");//只保留第一个，清除多余的
    input.value=input.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    input.value=input.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');
    if(input.value.indexOf(".")<0&&input.value!=""){   //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于01、02
        input.value=parseFloat(input.value);
    }
    // input.value = input.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//最多能输入两位小数
    // input.value = input.value.replace(/\D/gi,"");//输入框只能输入数字
    $("#uncertainty1").attr("class","fa fa-circle-o");
}

// creator: sy 2022-12-21 数字设定
function clear0(input){
    var vane = input.val();
    if(vane == 0){
        input.val(0.01);
    }else if(vane == 100){
        input.val(99.99);
    }
}

// creator: sy 2022-06-27 修改基本信息弹窗中点击确定按钮
function sale_updatesure(){
    var id = $("#upcontent").data("id");
    $("#beforeof").show();
    $("#overstop").hide();
    $("#maisure").show();
    $("#stopaway").hide();
    if(id == undefined || id== ""){
        $("#tiphoto #prompttext").html("系统错误，请稍后刷新重试");
        bounce_Fixed.show($("#tiphoto"));
        return false;
    }
    var datao={
        'id':id,
        'chargeAcceptable':$("#setHangAccount").val(),//修改后的是否接受挂账
        'chargeBegin':$("#setStartDate").val(),//修改后的挂帐开始
        'chargePeriod':$("#spcontwek").val(),//修改后的账期
        'isImprest':$("#setAdvanceFee").val(),//修改后的是否需要预付款
        'imprestProportion':$("#e_gRate1").val(),//预付款比例
        'uncertainty':$("#uncertainty").val()
    };
    if(datao.chargeAcceptable == null || datao.chargeAcceptable == ""){
        // layer.msg("请选择是否接收挂账");
        // return false;
    }else if(datao.chargeAcceptable == "1"){
        if(datao.chargePeriod.length == 0){
            layer.msg("请录入账期");
            return false;
        }
    }else if(datao.chargeAcceptable == "0"){
        datao.chargePeriod = "";
    }
    // if(datao.chargeBegin == null || datao.chargeBegin == ""){
    //     layer.msg("请选择从何时开始计算账期");
    //     return false;
    // }else if(datao.isImprest == null || datao.isImprest == ""){
    //     layer.msg("请选择是否需要预付款");
    //     return false;
    // }
    if(datao.uncertainty === "1"){
        if(datao.imprestProportion === ""){
            datao.imprestProportion = -1;
        }else{
            datao.imprestProportion = datao.imprestProportion;
        }
        // $("#e_gRate1").attr("disabled","disabled");
        // $("#e_gRate1").css("background","rgb(221, 221, 221)");
    }else{
        datao.imprestProportion = datao.imprestProportion;
    }
    console.log(datao.chargeAcceptable);
    console.log(datao.chargeBegin);
    console.log(datao.chargePeriod);
    console.log(datao.isImprest);
    console.log(datao.imprestProportion);
    // console.log(datao.uncertainty);
    var name = $("#suppliername").val();
    var fullName = $("#supplieruname").val();
    var codeName = $("#suppliernumber").val();
    $("#addsupplier input[type='text']:visible").each(function(){
        var name = $(this).attr('name');
        datao[name]=$(this).val();
    })
    if($("#edit_qImages .file_avatar .imgsthumb").length >0){
        var imgArr = [];
        $("#edit_qImages .imgsthumb").each(function(){
            var path = $(this).find(".filePic").data('path');
            var json = {
                'normal':path
            };
            imgArr.push(json);
        })
        // imgArr = JSON.stringify(imgArr);
        // datao = JSON.stringify(datao);
        // imgArr =datao.qImages;
        //const text = "abc";
        // const chars = text.split('');
        // console.log(chars);
        // //['a', 'b', 'c']
    }
    if(datao.chargePeriod == ""){
        var chargePeriod = datao.chargePeriod;
    }else{
        var chargePeriod = parseInt(datao.chargePeriod);
    }
    chargePeriod = Number(chargePeriod);
    var imprestProportion = Number(datao.imprestProportion);
    $.ajax({
        url:"../supplier/updateSupplierBase.do",
        data:{
            id:datao.id,
            name:name,//供应商简称
            fullName:fullName, //供应商名称
            codeName:codeName,//供应商代号
            qImages:imgArr,
            chargeAcceptable:datao.chargeAcceptable,//是否接受挂账
            chargeBegin:datao.chargeBegin,//挂账开始
            chargePeriod:chargePeriod,//账期
            isImprest:datao.isImprest,//是否要预付款
            imprestProportion:datao.imprestProportion,//预付款比例
            // uncertainty:datao.uncertainty,//预付款不确定
        },
        success:function(data){
            var status = data['status'];
            $("#beforeof").show();
            $("#overstop").hide();
            $("#maisure").show();
            $("#stopaway").hide();
            if(status ==0 || status =="0"){
                $("#tiphoto .shu1").html("操作失败，请稍后重试。");
                bounce_Fixed.show($("#tiphoto"));
                return false;
            }
            getContractMes(1,20);
            bounce_Fixed2.cancel($("#addAccount"));
            bounce_Fixed.show($("#upcontent"));
        }
    })
}

// creator: sy 2022-06-20 是否可接受汇票
function hui(type , thisObj) {
    setRadioSelect("hui", [1,2], type, thisObj);
}

// creator: sy 2022-05-25 供应商查询
function searchcontract(){
    $("#home").hide();
    $("#searchdi").show();
    var keyword = $(".main #se0").val();
    getChateMessage(1,20,keyword);
}

// creator: sy 2022-05-20 是否需要预付款
function setAdvanceFee(type,thisObj){
    setRadioSelect("setAdvanceFee", [1,0,2], type, thisObj);
    if(type == 1){
        $("#paymore").show();
        $("#chooseunde").show();
        $("#uncertainty1").attr("class","fa fa fa-circle-o");
        // $("#e_gRate1").removeAttr("disabled");
        $("#e_gRate1").css("background","white");
    }else{
        $("#paymore").hide();
        $("#chooseunde").hide();
    }
}

// creator: sy 2022-08-15 比例不确定
function uncertainty(type,thisObj){
    setRadioSelect("uncertainty",[1,0],type,thisObj);
    if(type == 1){
        // $("#e_gRate1").attr("disabled","disabled");
        // $("#e_gRate1").css("background","rgb(221, 221, 221)");
        $("#e_gRate1").val("");
    }else{
        $("#contract").removeAttr("disabled");
        $("#e_gRate1").css("background","white");
        $("#uncertainty1").attr("class","fa fa fa-circle-o");
    }
}

// creator: sy 2022-05-06-30 联系人列表
function setContactList(list){
    if(list.length == 0){
        $(".contectList").html("");
    }
    var html = '',rhtml = '',slice = 0;
    if(list && list.length > 0){
        html =
            '<div class="dataList leftList"> <table class="ty-table ty-table-control">'+
            '<thead>'+
            '<tr>'+
            '<td>姓名</td>'+
            '<td>职位</td>'+
            '<td>操作</td>'+
            '</tr>'+
            '</thead><tbody>';
        if(list.length >=2) rhtml = html;
        for(var i=0;i<list.length;i++){
            list[i]['number'] = list[i].number || list[i].id;
            slice = i%2;
            if(slice > 0){
                rhtml +=
                    '<tr data-id="'+list[i].number+'" groupUuid="'+list[i].groupUuid+'">'+
                    '<td>'+list[i].name+'</td>'+
                    '<td>'+list[i].post+'</td>'+
                    '<td><span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer" onclick="upchange()">修改</span>' +
                    '<span class="ty-color-red node" data-type="delete" data-name="contactInfo" id="d1" onclick="dettle($(this))">删除</span></td>'+
                    '</tr>';
            }else{
                html +=
                    '<tr data-id="'+ list[i].number +'" groupUuid="'+list[i].groupUuid+'">'+
                    '<td>'+list[i].name+'</td>'+
                    '<td>'+list[i].post+'</td>'+
                    '<td><span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer" onclick="upchange()">修改</span>' +
                    '<span class="ty-color-red node" data-type="delete" data-name="contactInfo" id="d2" onclick="dettle($(this))">删除</span></td>'+
                    '</tr>';
            }
        }
        html += '</tbody></table></div>';
        if(list.length >=2) rhtml +='</tbody></table></div>';
    }
    var str = html +rhtml;
    var ts = JSON.stringify(list);
    $("#addAccount").data('conreceiveInfo',ts);
    return str;
}

// creator: sy 2022-07-05 选择联系人
function setCusListStr(list,source) {
    let str = "";
    var bont = $("#mailName").data("font");
    if (bont === "add") {//新增供应商弹窗
        // let bntn = $("#dettle").parent().parent().html();
        let bntn = $("#clist3-1").html();
        if(bntn == null ||bntn == undefined || bntn == "null"){
            $("#chooseCusContact .p0").show();
            $("#chooseCusContact .p1 p").hide();
            $(".cusList").hide();
        }else{
            // let lixir =$("#dettle").siblings(".hd").html();
            // list = JSON.parse(list);
            $("#chooseCusContact .p0").hide();
            $("#chooseCusContact .p1 p").show();
            $(".cusList").show();
            for(let i in list){
                let item = list[i];
                str += `<li>`+
                    `<i class="fa fa-circle-o"></i>`+
                    `<span style="margin-left: 10px;margin-right: 5px;">${item.name}</span>`+
                    `<span style="margin-left: 5px;margin-right: 5px;">${item.post && item.post.substr(0,8)} </span>`+
                    `<span style="margin-left: 5px;margin-right:10px;">${item.mobile}</span>`+
                    `<span class="hd info">${JSON.stringify(item)}</span>`+
                    `<span class="linkBtn ty-right" data-source="${source}" data-type="contactSocial" data-id="${item.id}" onclick="lxrlook($(this))">查看</span>`+
                    `</li>`;
            }
        }
    } else if(bont === "upen"){//供应商常规修改
        $("#chooseCusContact .p0").hide();
        $("#chooseCusContact .p1 p").show();
        $("#chooseCusContact .p1 .cusList").show();
        for(let i in list){
            let item = list[i];
            str += `<li>`+
                `<i class="fa fa-circle-o"></i>`+
                `<span style="margin-left: 10px;margin-right: 5px;">${item.name}</span>`+
                `<span style="margin-left: 5px;margin-right: 5px;">${item.post && item.post.substr(0,8)}</span>`+
                `<span style="margin-left: 5px;margin-right:10px;">${item.mobile}</span>`+
                `<span class="hd info">${JSON.stringify(item)}</span>`+
                `<span class="linkBtn ty-right" data-source="${source}" data-type="contactSocial" data-id="${item.id}" onclick="lxrlook($(this))">查看</span>`+
                `</li>`;
        }
        // }
    }
    $("#chooseCusContact .cusList").html(`${str}`);
    let box = $("#chooseCusContact .cusList").html();
    if(box.length<0 || box == undefined || box == null || box == "null" || box == ""){
        $("#chooseCusContact .p0").show();
        $("#chooseCusContact .p1 p").hide();
        $(".cusList").hide();
    }
}

// creator: sy 2022-05-14 是否接受挂账
function setHangAccount(type, thisObj){
    // $("#contdent").hide();
    // $("#spcontdent").hide();purchasedAny($(this))
    setRadioSelect("setHangAccount", [1,0], type, thisObj);
    $("#hangDays").val("") ;
    if(type == '1' || type == 1){
        $("#contdent").show(); $(".hang_7").show();$(".hang7_1").show();
    }else{
        $("#contdent").hide(); $(".hang_7").hide();$(".hang7_1").hide();
    }
}

// creator: 张旭博，2023-09-22 03:52:14， 获取供应商上传的图片渲染字符串
function setImgHtml(type, imgsArr) { // type: 1=查看 2= 修改
    let str = '';
    for(let item of imgsArr){
        let path = item.normal;
        let url = $.fileUrl + path
        let delStr = type === 2?`<span onclick="cancelFile($(this))">删除</span>`:''
        str += `<div class="imgsthumb">
                    <div class="filePic" data-path="${path}" style="background-image: url(${url})"></div>
                    ${delStr}
                    <a path="${path}" onclick="imgViewer($(this))">预览</a>
                </div>`
    }
    return str;
}

// creator: sy 2022-06-25 复选框选中（是否能开发票）
function setRadioSelect(str, arr, selectVal, thisObj){  //arr:[1,0]
    var isEdit = $("#isEdit").val();
    var idStr = "#";
    if(isEdit === "1"){
        idStr = "#edit_";
    }
    idStr += str ;
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $(idStr).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $(idStr + arr[i]).attr("class" , "fa fa-circle-o");
    }
    $(idStr + selectVal).attr("class" , "fa fa-dot-circle-o");

}

// creator: sy 2022-06-25 复选框选中（是否可接受汇票）
function setRadioCSelect(str,arr,selectVal,thisObj){
    var isEdit = $("#isEdit").val();
    var idStr = "#";
    if(isEdit === "1"){
        idStr = "#edit_";
    }
    idStr +=str;
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $(idStr).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $(idStr + arr[i]).attr("class" , "fa fa-circle-o");
    }
    $(idStr + selectVal).attr("class" , "fa fa-dot-circle-o");
}

// creator: sy 2022-06-25 复选框选中（从何时开始计算账期）
function setStartDate(type , thisObj) {
    setRadioSelect("setStartDate", [1,2], type, thisObj);
}

// creator: sy 2022-05-12 新增验证
function setTimer(timer){
    switch(timer){
        case 'updateReceive':
            bounce_Fixed.everyTime('0.5s','updateReceive',function () {
                var  filledNum = 0;
                $("#newReceiveInfo input").each(function(){
                    if ($(this).val() == '') filledNum++;

                });
                if(filledNum === 0 ){
                    $("#addReceive").prop("disabled",false);
                }else{
                    $("#addReceive").prop("disabled",true);
                }
            });
            break;
        case 'updateMail':
            bounce_Fixed.everyTime('0.5s','updateMail',function(){
                var state = 0,filledNum = 0;
                $("#newMailInfo input").each(function(){
                    if($(this).val() == '') filledNum++;
                });
                if(filledNum === 0){
                    if(!is_postcode($("newMailInfo #mailNumber").val())){
                        $("#mailNumError").hide();
                        $("#addMail").prop("disabled",false);
                    }else{
                        $("#mailNumError").show();
                        $("#addMail").prop("disabled",true);
                    }
                }else{
                    $("#addMail").prop("disabled",true);
                }
            });
            break;
        case 'updateContact':
            bounce_Fixed.everyTime('0.5s','updateContact',function(){
                var state = 0,filledNum = 0,otherContactLen = 0;
                var contactsCard = $("#contactsCard").data('org');
                var imgCard = $("#contactsCard .filePic").data('path');
                var len = $(".otherContact").data('length');
                if($(".otherContact li").length > 0){
                    $(".otherContact li").each(function (){
                        var val = $(this).find("input").val();
                        var type = $(this).find("input").data("type");
                        if(type == '0' || type == '0') type = 6
                        if(val == ''){
                            $("#addMoreContact option").eq(type).prop('disabled',false);
                        }else{
                            $("#addMoreContact option").eq(type).prop('disabled',false);
                            otherContactLen++;
                        }
                    })
                }
                if(len != otherContactLen) state ++;
                $("#newContectData [require]:visible").each(function(){
                    if($(this).val() != '') filledNum++;
                    if($(this).val() != $(this).data('org')){
                        state ++;
                    }
                });
                if(contactsCard != imgCard) state ++;
                if(filledNum > 0 && state >0){
                    $("#addContact").prop("disabled",false);
                }else{
                    $("#addContact").prop("disabled",false);
                }
            });
            bounce_Fixed.show("#newContectInfo");
            break;
        case 'useDefinedLabel':
            bounce_Fixed.everyTime('0.5s','useDefinedLabel',function(){
                var name = $.trim($("#defLable").val());
                if(name == '' || name){
                    $("#addNewLabelSure").prop('disabled',true);
                }else{
                    $("#addNewLableSure").prop('disabled',false);
                }
            })
            break;
    }
}
// 上传图片
var addCusContact = [];

laydate.render({elem:'.cSignDatecon',format:'yyyy-MM-dd'});
laydate.render({elem:'.cStartDatecon',format:'yyyy-MM-dd'});
laydate.render({elem:'.cEndDatecon',format:'yyyy-MM-dd'});
laydate.render({elem:'.cSignDate',format:'yyyy-MM-dd'});
laydate.render({elem:'.cStartDate',format:'yyyy-MM-dd'});
laydate.render({elem:'.cEndDate',format:'yyyy-MM-dd'});

// ----------------------------- 其他原辅材料 -------------------------------

// creator: 张旭博，2023-08-15 10:39:28， 录入其他原辅材料
function editRAAMt(selector, type) {
    $('#editRAAMt').data('type', type)
    $('#editRAAMt').data('selector', selector)
    $("#importBtn").show() // 修改时需要去掉
    let info = {}
    let data = {}
    if (type === 'del') {
        info = JSON.parse(selector.parents("tr").find(".hd").html())
        data = {
            operation: 2,
            userId: sphdSocket.user.userID,
            id: info.id
        }
        bounce.show($("#bounce_tip"))
        $("#bounce_tip .tipMsg").html('确定删除此材料？')
        $("#bounce_tip .sureBtn").unbind().on('click', function (){
            $.ajax({
                url: '../mt/operation',
                data: data ,
                success:function(res){
                    let code = res.code
                    $("#editMtBtn").data('info',JSON.stringify(res));
                    bounce.cancel();
                    if(code === 1){
                        layer.msg('删除成功！');
                        selector.parents("tr").remove();
                    }else if(code === 2){
                        layer.msg('系统不支持“删除”已有采购数据的材料，而此材料在系统中已有与采购有关的数据！')
                    }
                }
            })
        })
    }
    if (type === 'update') {
        info = JSON.parse(selector.parents("tr").find(".hd").html())
        data = {
            operation: 10,
            userId: sphdSocket.user.userID,
            id: info.id
        }
        // 数据填充
        $.ajax({
            url: '../mt/operation',
            data: data ,
            success:function(res){
                let code = res.code
                let data = res.data[0]
                let mode = $("body").data('whmode')
                $("#editMtBtn").data('info',JSON.stringify(res));
                $('#editRAAMt .bounce_title').html("材料修改")
                $("#editRAAMt .intelligentWh input:radio").prop("checked", false)
                $("#editRAAMt .intelligentWh .partHasExpRequired").hide()
                $('#editRAAMt .textMax').html(data.memo?data.memo.length:0 + "/100")
                $("#importBtn").hide()
                $("#editRAAMt .normal input").each(function () {
                    var name = $(this).attr("name")
                    $(this).val(data[name]);
                });

                if (mode !== 0) {
                    let expRequired = data.exp_required
                    let relatedItem = data.related_item
                    let sameExpiration = data.same_expiration
                    let openDuration = data.open_duration
                    let expirationDays = data.expiration_days
                    $("#editRAAMt [name='expRequired'][value='"+expRequired+"']").prop("checked", true)
                    if (expRequired === 1) {
                        $("#editRAAMt .partHasExpRequired").show()
                        $("#editRAAMt .partHasExpRequired [name='openDuration']").val(openDuration)
                        $("#editRAAMt [name='relatedItem'][value='"+relatedItem+"']").prop("checked", true)
                        if (relatedItem === 2) {
                            $("#editRAAMt .part_birthDate").show()
                            $("#editRAAMt [name='sameExpiration'][value='"+sameExpiration+"']").prop("checked", true)
                            if (sameExpiration === 1) {
                                $("#editRAAMt .partHasExpRequired [name='expirationDays']").val(expirationDays)
                            }
                        }
                    }

                }
                getUnitList($("#unitSelect"),7,data.unit_id);
                // 赋值种类
                var category_id = data.category_id;
                setCategory('', category_id)
                bounce.show($('#editRAAMt'));
            }
        })
    }
    if (type === 'new') {
        $('#editRAAMt .bounce_title').html("材料录入")
        $("#unitSelectBtn").show(); // 计量单位录入按钮显示
        $("#editRAAMt input:text").val("")
        $("#editRAAMt select").val("")
        setCategory()
        let mode = $("body").data('whmode')
        if (mode === 0) {
            $("#editRAAMt .intelligentWh").remove()
        } else {
            $("#editRAAMt .intelligentWh input:radio").prop("checked", false)
            $("#editRAAMt .intelligentWh .partHasExpRequired").hide()
        }
        bounce.show($('#editRAAMt'));
        getUnitList($("#unitSelect"),7);
        $("#editRAAMt .textMax").html("0/100");

    }
}

function changeExpRequired(select) {
    let parent = select.parents(".bonceContainer").attr('id')
    let selector = $("#" + parent)
    let value = selector.find("input[name='expRequired']:checked").val()
    if (value === '1') {
        let part = selector.find('.partHasExpRequired')
        part.show()
        part.find('input:radio').prop("checked", false)
        part.find('input:text').val('')
        part.find('.part_birthDate').hide()
    } else {
        selector.find('.partHasExpRequired').hide()
    }
}


function changeRelatedItem(select) {
    let parent = select.parents(".bonceContainer").attr('id')
    let selector = $("#" + parent)
    let value = selector.find("input[name='relatedItem']:checked").val()
    if (value === '2') {
        let part = selector.find('.part_birthDate')
        part.show()
        part.find('input:radio').prop("checked", false)
        part.find('input:text').val('')
        part.find('.part_saveExpiration').hide()
    } else {
        selector.find('.part_birthDate').hide()
    }
}

function changeSameExpiration(select) {
    let parent = select.parents(".bonceContainer").attr('id')
    let selector = $("#" + parent)
    let value = selector.find("input[name='sameExpiration']:checked").val()
    if (value === '1') {
        let part = selector.find('.part_saveExpiration')
        part.show()
        part.find('input:radio').prop("checked", false)
        part.find('input:text').val('')
    } else {
        selector.find('.part_saveExpiration').hide()
    }
}

// creator: 张旭博，2024-01-02 03:54:23， 物料搜索
function search() {
    getMtList(1 , 20, true);
}

// creator: 张旭博，2024-01-24 11:09:40， 定点信息已确定的原辅材料数据 - 物料搜索
function searchPointedRAAMt() {
    jumpPage('pointedRAAMt_search', ()=> {
        getSearchMtList(1, 20)
    })
}

function getSearchMtList(cur, per) {
    let keyword = $(".page[page='pointedRAAMt'] .ty-searchInput").val() || ''
    $(".page:visible .searchKey").html(keyword)
    // state:查询总列表时不传,  1-查询待确认定点信息时, 2-查询暂停采购的材料时, 3-查询待录入初始库存数量时 4-已确定定点信息
    // keyword : 查询的代号或名称
    let data = {
        oid: sphdSocket.user.oid,
        pageNum: cur,
        category: null,
        per: per,
        state: 4,
        keyword: keyword
    }
    $.ajax({
        url: '../mt/list',
        data: data,
        success:function (res) {
            // 分页处理
            let pageSize = res.totalCount; //总页数
            let jsonStr = JSON.stringify(data) ;
            setPage( $("#ye_mtSearch4"), cur, pageSize, "pointedRAAMt", jsonStr );

            let mtArr = res.data || []

            let strMt = "" ;
            if(mtArr){
                for(let item of mtArr){
                    // 已确认定点信息物料
                    strMt +=   `<tr>
                                        <td>${item.name || ''}</td>
                                        <td>${item.code || ''}</td>
                                        <td>${item.model || ''}</td>
                                        <td>${item.specifications || ''}</td>
                                        <td>${item.unit || ''}</td>
                                        <td>${item.create_name + ' ' + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                                        <td>
                                            <span class="hd">${JSON.stringify(item)}</span>
                                            <span class="link-blue" onclick="supplierOfMt($(this))">${item.supplier_number || 0}个</span>
                                        </td>
                                    </tr>`
                }
                $(".tbl_determinedRAAMt tbody").html(strMt)
            }
        }
    })
}

// created:hxz 2020-09/17 首页返回上一级/返回全部
function backPreCat(num) {
    var index = $('.curCat .go2Cat').length - 1; // 返回全部
    if(num === 1){ // 返回全部
        getMtList(1 , 20);
    }else{
        $('.curCat').children(":nth-child("+ index +")").click();
    }
}

// created:hxz 2020-03-11 新增分类
function addCatOk() {
    var pid =  $("#addkind_pid").val();
    var type =  1 ; // 0 新增同级 ； 1 新增子级 ； 2 修改分类
    var name = $("#newCatName").val();
    // 判断名字是否重复
    let mtCategories = $("#editRAAMt").data('categories')
    let isRepeat = mtCategories.findIndex(item => item.name === name) !== -1
    if (isRepeat) {
        layer.msg("已存在重复的同级类别名称！");
        return false;
    }
    if(name.length > 50){
        layer.msg("类别名称太长了！");
        return false;
    }
    if (!name) {
        layer.msg("请录入类别！");
        return false;
    }
    $.ajax({
        url:"../material/addMtCategoryByPid.do",
        data:{ pid:pid , type:type , name:name  },
        success:function (res) {
            var category = res.category
            var child = res.child
            layer.msg('新增成功');
            bounce.cancel();
            let thisCat = {
                id: category.id,
                name: category.name,
                parent: pid || null,
                count: 0,
                LEVEL: category.level
            }
            mtCategories.push(thisCat);
            $("#editRAAMt").data('categories', mtCategories)
            let str =  `<li data-id="${category.id}">
                            <span class="catName">${category.name}</span>
                            <span class="catNum">0</span>
                            <span class="ctrlBtn">
                                <i class="fa fa-pencil" type="btn" name="edit" title="修改分类"></i><i class="fa fa-trash" type="btn" name="del" title="删除分类"></i>
                            </span>
                        </li>`
            $(".catAll:visible").append(str)
        }
    })
}
// created:hxz 2020-03-11 物料查看与删除
function mtScanDel(type, num) {
    var info = editObj.siblings(".hd").html();
    info = JSON.parse(info);
    var data = { 'operation':10 , 'userId':sphdSocket.user.userID , 'id':info['id'] };
    if(type == 'mtDel' && num == 1){
        data['operation'] = 2 ;
    }
    $.ajax({
        "url":"../mt/operation",
        "data":data ,
        success:function(res){
            $("#editMtBtn").data('info',JSON.stringify(res));
            if(type == 'mtDel' && num == 1){ // 删除
                bounce.cancel();
                var code = res['code']
                if(code == 1){
                    layer.msg('删除成功！');
                    editObj.parent().parent().remove();
                }else if(code == 2){
                    $("#tip1 .bonceCon").html("系统不支持“删除”已有采购数据的材料，而此材料在系统中已有与采购有关的数据！")
                    bounce_Fixed.show($("#tip1"));
                }
            }else{ // 查看或者删除的前奏
                var data = res['data'][0] ;
                if(type == 'mtDel'){
                    if(data['origin'] == 3 || data['origin'] == 4 || data['origin'] == 5){
                        $("#tip1 .bonceCon").html("此种材料与产品有关，不可在此删除！")
                        bounce_Fixed.show($("#tip1"));
                    }else if(data['is_appointed'] == '0'){
                        $("#tip1 .bonceCon").html("系统不支持“删除”已有采购数据的材料，而此材料在系统中已有与采购有关的数据！")
                        bounce_Fixed.show($("#tip1"));
                    }else if(Number(info['supplier_number']) > 0){
                        $("#tip1 .bonceCon").html("系统不支持“删除”已有采购数据的材料，而此材料在系统中已有与采购有关的数据！")
                        bounce_Fixed.show($("#tip1"));
                    }else {
                        bounce.show($("#mtDel"));
                    }
                }else{
                    for(var key in data){
                        if(key == 'create_name' || key == 'create_date' ){

                        }else{
                            $(".scanMt_" + key).html(data[key]);
                        }
                    }
                    var str = "";
                    if(data['enabled'] == 0){ // 暂停采购了
                        $("#editMtBtn").hide();
                        str = data['update_name'] + "已于"+ (new Date(data['enabled_time']).format('yyyy-MM-dd hh:mm:ss')) +"将本材料“暂停采购”！。"
                    }else if(data['origin'] == 3 || data['origin'] == 4 || data['origin'] == 5){
                        str = "本材料来自于产品的拆分（包装）。"
                        $("#editMtBtn").show();
                    }else{
                        $("#editMtBtn").show();
                        switch (Number(data.is_purchased)){
                            case 1 :
                                str = "本材料录入时为“曾采购过的材料”。" ; break;
                            case 0 :
                                str = "本材料录入时为“未采购过的材料”。" ; break;
                        }
                    }
                    $(".scanMt_state").html(str)

                    $(".scanMt_cat").html(res['path']);
                    $(".scanMt_create").html(data['create_name'] + " " + (new Date(data['create_date']).format('yyyy-MM-dd hh:mm:ss')));
                }


            }
        }
    })
}
// created:hxz 2020-03-11 切换主页与暂停页
function toggleSuspend(num){
    $('.mainCon' + num).show().siblings().hide();
}


// creator: hxz 2020-04-07 查看暂停采购
function goPause(){
    listData = { 'category':false , 'state':2 , 'keyword':'' }
    getList('', 2, '', 1, 20);
    toggleSuspend(2)
}
// creator: hxz 2020-04-07 修改物料
function editMtBtn() {
    $("#importBtn").hide();
    bounce.show($("#mtEntry"));
    var res = JSON.parse($("#editMtBtn").data('info')) ;
    var order_count = res['order_count'];
    var data = res['data'][0];
    data['operation'] = 3 ;
    data['userId'] = sphdSocket.user.userID ;
    var purChaseStr = "本材料录入时为“未曾采购过的材料”。";
    var is_purchased = data['is_purchased'];
    var origin = data['origin'];
    if(origin == 3 ||origin == 4 ||origin == 5 ){
        purChaseStr = "本材料来自于产品的拆分（包装），<span class='red'>其一级类别不可修改，基本信息则不可在此修改。</span>";
        $("#mtEntry .editTip").hide();
        $("#unitSelectBtn").hide();
    }else {
        $("#unitSelectBtn").show();
        $("#mtEntry .editTip").show();
        if(is_purchased == "1"){
            purChaseStr = "本材料录入时为“曾采购过的材料”。";
        }
        if(order_count == 0){
            $("#mtEntry .orderCount0").hide();
        }else{
            $("#mtEntry .orderCount0").show();
            $("#mtEntry .order_count").html(order_count);
        }
    }
    $("#mtEntry .say").html(purChaseStr).show().siblings().hide();
    // 给修改页面赋值
    $("#mtEntry .bonceHead span").html("材料修改");

    $("#mtEntry input").each(function () {
        var name = $(this).attr("name")
        $(this).val(data[name]);
    });
    getUnitList($("#unitSelect"),7,data['unit_id']);
    // 赋值种类
    var category_id = data.category_id;
    var curCatObj = $("#catTree").find(".treeItem" + category_id);
    var pCatsArr = curCatObj.parents(".treeItem");
    var catsArr = [];
    pCatsArr.each(function(){
        var kls = $(this).attr("class");
        var catId = kls.substr(17, kls.length);
        catsArr.push(catId);
    })
    catsArr.reverse().push(category_id);
    for(var i = 0 ; i < catsArr.length ; i++){
        setCat(catsArr[i], $("#mtEntry .categorySelectList"), 1, origin);
    }
    if(origin == 3 ||origin == 4 ||origin == 5 ){
        $("#cats").children(".category:eq(0)").attr("disabled",true);
        $("#mtEntry [require]").attr("disabled", true);
    }else{
        $("#cats").children(".category:eq(0)").removeAttr("disabled");
        $("#mtEntry [require]").removeAttr("disabled");
    }
}

// creator: hxz 2020-04-07 保存物料
function mtEditOk() {
    var state = 0 ;
    var data = {
        userId: sphdSocket.user.userID,
        operation: 1,
        isPurchased: 1,
        enabled: 1,
        origin: '',
        id: '',
        category_: $("#editRAAMt .category:last").val()
    }

    $("#editRAAMt").find("[need]").each(function () {
        if($.trim($(this).val()) === ''){
            state ++;
        }
    });

    if(state>0 || data["category_"] == ""){
        layer.msg("还有必填项尚未填写！");
        return false;
    }
    $("#editRAAMt .normal [name]").each(function () {
        var name = $(this).attr("name")
        data[name] = $(this).val()
    });
    let mode = $("body").data('whmode') // 1智能库 0非智能库
    if (mode === 1) {
        let modeState = 0
        let expRequired = $("#editRAAMt [name='expRequired']:checked").val()
        let relatedItem = $("#editRAAMt [name='relatedItem']:checked").val()
        let sameExpiration = $("#editRAAMt [name='sameExpiration']:checked").val()
        let openDuration = $("#editRAAMt [name='openDuration']").val()
        let expirationDays = $("#editRAAMt [name='expirationDays']").val()
        data.expRequired = expRequired
        data.relatedItem = relatedItem
        data.sameExpiration = sameExpiration
        if (expRequired) {
            if (expRequired === '1') {
                data.openDuration = openDuration
                if (relatedItem) {
                    if (relatedItem === '2') {
                        if (sameExpiration) {
                            if (sameExpiration === '1') {
                                data.expirationDays = expirationDays
                                if (expirationDays === '') {
                                    modeState++
                                }
                            }
                        } else {
                            modeState++
                        }
                    }
                } else {
                    modeState++
                }
            }
        } else {
           modeState++
        }
        if (modeState > 0) {
            layer.msg("还有必填项尚未填写！");
            return false;
        }
    }
    let type = $("#editRAAMt").data('type')
    let selector = $("#editRAAMt").data('selector')
    if (type === 'update') {
        var info = JSON.parse(selector.parents('tr').find(".hd").html())
        data.operation = 3
        data.id = info.id
    }
    $.ajax({
        url: "../mt/operation",
        data: data,
        success:function(res){
            var code = res["code"]
            bounce.cancel();
            if(code === 1){
                layer.msg("保存成功");
                getMtList(1, 20)
            }else {
                var message = res['message'];
                layer.msg(message);
            }
        }
    })
}

// creator: 张旭博，2024-12-25 08:49:07， 设置类别选择框
function setCategory(obj, categoryId) {
    let mtCategories = $("#editRAAMt").data('categories')
    let val = obj?Number(obj.val()):null
    let disabledArr = ['构成商品的原辅材料', '外购成品', '商品的包装物'] // 这几个无法选择
    let data = mtCategories.filter(it => disabledArr.findIndex(i => i === it.name) === -1)
    let nextCategoryArr = data.filter(item => item.parent === val) // 下一级数据
    let optionStr = '<option value="">--- 请选择分类---</option>'
    for (let item of nextCategoryArr) {
        optionStr += `<option value="${item.id}">${item.name}</option>`
    }
    if (nextCategoryArr.length > 0) {
        let nextCategoryStr = `<select class="kj-input category" onchange="setCategory($(this))">${optionStr}</select>`
        if (obj) {
            obj.nextAll().remove()
            obj.after(nextCategoryStr)
        } else {
            $(".categorySelectList").html(nextCategoryStr)
        }
    } else {
        obj.nextAll().remove()
    }
    if (categoryId) {
        let str = getAllSelectCategoryStr(data, categoryId)
        $("#editRAAMt .categorySelectList").html(str)
    }
}

// creator: 张旭博，2024-01-03 03:38:35， 根据材料类别id获取存储路径
function getCategoryPath(categoryId) {
    let mtCategories = $("#editRAAMt").data('categories')
    let disabledArr = ['构成商品的原辅材料', '外购成品', '商品的包装物'] // 这几个无法选择
    let data = mtCategories.filter(it => disabledArr.findIndex(i => i === it.name) === -1)
    let str = getAllSelectCategoryStr(data, categoryId, 'path')
    return str
}

function getAllSelectCategoryStr(data, categoryId, type) {
    let item = data.find(it => it.id === categoryId)
    let parent = item.parent
    let thisLevelData = data.filter(item => item.parent === parent) // 下一级数据
    let parentItem = data.find(it => it.id === parent)
    let optionStr = '<option value="">--- 请选择分类---</option>'
    for (let item of thisLevelData) {
        let selectedStr = item.id === categoryId?'selected': ''
        optionStr += `<option value="${item.id}" ${selectedStr}>${item.name}</option>`
    }
    let thisLevelSelectStr = `<select class="kj-input category" onchange="setCategory($(this))">${optionStr}</select>`
    let pathStr = item.name
    if (parent !== null) {
        // 此处递归加上其他上级的下拉框（逆向，此处不能 +=）
        thisLevelSelectStr = getAllSelectCategoryStr(data, parent) + thisLevelSelectStr
        pathStr = getAllSelectCategoryStr(data, parent, 'path') + '>' + pathStr
    }
    if (type === 'path') {
        return pathStr
    } else {
        return thisLevelSelectStr
    }
}

//creator:hxz 2020-04-08 设置要选择的分类
function setCat(pid, obj, seleted, origin){
    // pid 点击的父级类id,
    // obj 点击的父级类dom
    // seleted 如果设置选中pid就是当前的(用在赋默认值)，不是父级的
    var categories = mtCategories;
    var str = "<option value=''>--- 请选择分类--- </option>";
    var len = str.length
    var objP = null;
    if (obj.hasClass("categorySelectList")) {
        objP = obj;
    } else {
        if(obj){
            // obj.html("")
            obj.nextAll().remove();
        }
        objP = obj.parent(".categorySelectList")
    }
    if(seleted == 1){
        var PrentID = 0;
        for(var i = 0 ; i < categories.length ; i++){
            var itemId = categories[i]['id'];
            if(itemId == pid){
                PrentID = categories[i]['parent'];
            }
        }
        for(var i = 0 ; i < categories.length ; i++){
            var itemId = categories[i]['id'];
            var itemName = categories[i]['name'];
            var itemPid = categories[i]['parent'];
            if(!itemPid){ // 一级
                var indexName = ['构成商品的原辅材料', '外购成品', '商品的包装物'].indexOf(itemName);
                if(!itemPid && indexName== -1 || ((origin == 3 ||origin == 4 ||origin == 5) &&  indexName> -1 )){
                    if(itemPid == PrentID){
                        if(itemId == pid){
                            str += "<option selected value='"+ categories[i]['id'] +"'>"+ categories[i]['name'] +"</option>"
                        }else{
                            str += "<option value='"+ itemId +"'>"+ categories[i]['name'] +"</option>"
                        }
                    }
                }
            }else {
                if(itemPid == PrentID){
                    if(itemId == pid){
                        str += "<option selected value='"+ categories[i]['id'] +"'>"+ categories[i]['name'] +"</option>"
                    }else{
                        str += "<option value='"+ itemId +"'>"+ categories[i]['name'] +"</option>"
                    }
                }
            }


        }

        if(str.length > len){
            if(!PrentID){
                objP.children("select").remove();
            }
            objP.append("<select class='category' onchange='setCat($(this).val(), $(this))'>"+ str +"</select>");
        }
        return false;
    }
    var selectLen = objP.find(".category").length ;
    if(!pid && selectLen<2){ // 一级
        for(var i = 0 ; i < categories.length ; i++){
            var pCat = categories[i]['parent'];
            var name = categories[i]['name'];
            var indexName = ['构成商品的原辅材料', '外购成品', '商品的包装物'].indexOf(name);
            if(!pCat && indexName== -1){
                str += "<option value='"+ categories[i]['id'] +"'>"+ categories[i]['name'] +"</option>";
            }
        }
        obj.html(str);
    }else{ // 非一级
        for(var i = 0 ; i < categories.length ; i++){
            var pCat = categories[i]['parent'];
            if(pCat == pid){
                str += "<option value='"+ categories[i]['id'] +"'>"+ categories[i]['name'] +"</option>"
            }
        }
        if(str.length > len){
            objP.append("<select class='category' onchange='setCat($(this).val(), $(this))'>"+ str +"</select>")
        }
    }

}
// creator:hxz 2020-04-09 创建种类树
function mtCategoriesTree(){
    var catArr = mtCategories ;
    $("#catTree").html("");
    for(var i = 0 ; i <catArr.length ; i++){
        var id = catArr[i]['id']
        var pid = catArr[i]['parent']
        if(pid){
            var str = "<span class='treeItem treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").find(".treeItem"+pid).append(str);

        }else{ // 一级类别
            var str = "<span class='treeItem treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").append(str);
        }
    }
}

// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectID) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">-----请选择-----</option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    if(selectID == item['id']){
                        str += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        str += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }
                }
            }
            obj.html(str);
        }
    })
}
// creator: 李玉婷，2021-07-21 08:23:44，返回列表
function getUnitListData(module) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    var list = [];
    $.ajax({
        "async": false,
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            list = res['list'] ;
        }
    })
    return list;
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit() {
    $("#addUnit").data("type", 1);
    bounce_Fixed.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed.cancel();
                var cur = $("#addUnit").data("type");
                if (cur == 1) {
                    getUnitList($("#unitSelect"), module);
                } else if (cur == 2) {
                    $(".page[page='importInfo'] .importCon2 tbody select").each(function(){
                        let option = `<option value="${res.id}">${res.name}</option>`;
                        $(this).append(option);
                    });
                } else if (cur == 3) {
                    $(".page[page='importInfo'] .importCon1 tbody select").each(function(){
                        var val = $(this).val();
                        getUnitList($(this), module, val);
                    });
                }
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = '<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>';
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed.show($("#tip1"));
            }
        }
    })
}


// ---------------------------- 导入材料 ------------------------------
// creator: 李玉婷，2020-10-12 11:23:38，批量导入-判断是否有未完成的导入材料
function leadingShow() {
    $.ajax({
        url:"../mtImport/whetherUnfinishedImport.do",
        data:{
            isPurchased: 1 // 曾采购过的材料
        },
        success:function (data) {
            let status = data.status;
            if (status === 1) {
                $('#select_btn_1').val("");
                $('.matListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                initLeading()
                bounce_Fixed.show($('#leading'));
            } else {
                $('.unfinishedForm .fa').attr("class","fa fa-circle-o");
                bounce_Fixed.show($('#importNotCompleted'));
            }
        }
    });

}

// creator: 张旭博，2023-11-10 02:19:49， 初始化
function initLeading() {
    $('#uploadFile').Huploadify({
        auto:false ,
        fileTypeExts:'*.xls;*.xlsx;',
        multi:false,
        formData:{
            module: '采购',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:40960,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onSelect:function (file) {
            $('#leading .uploadify-queue .uploadify-queue-item:not(:last)').remove();
            $('#leading .fileFullName').html(file.name);
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data ,pathArr){
            let fileData = JSON.parse(data);
            let filePath = fileData.filename;
            let fileUid = fileData.fileUid;
            $('#leading .fileFullName').data('fileid', fileUid);
            ImportMaterial(filePath)
        }
    });
}

// creator: 张旭博，2023-11-07 03:42:46， 浏览按钮 （触发选择文件按钮）
function chooseMtFile() {
    $("#uploadFile .uploadify-button").click()
}

// creator: 李玉婷，2020-09-03 17:12:49，导入验证
function importConfirm(type) {
    if(type === 'cancel'){
        let fileUid = $('#leading .fileFullName').data('fileid');
        let op = {"type":'fileId', 'fileId':fileUid}
        fileDelAjax(op);
        bounce_Fixed.cancel()
    }else{
        if ($("#leading .uploadify-queue-item").length <= 0) {
            layer.msg("您需选择一个文件后才能“导入”！")
        } else {
            loading.open() ;
            $("#leading .uploadify-queue-item .uploadbtn").click();
        }
    }
}

// created:hxz 2021-01-09 导入材料
function ImportMaterial(path) {
    $.ajax({
        url: "../export/ImportMaterial.do",
        data: { filePath: path },
        success:function (data) {
            var status = data.status;
            let thisPage = $(".page[page='importInfo']")
            if (status == '1') {
                jumpPage('importInfo', ()=> {
                    thisPage.find("table tbody").html("");
                    var mtBaseList = data.trueMtBaseList;
                    var mtFalseList = data.falseMtBaseList;
                    $(".importCon1").data("trueUserList", mtBaseList);
                    bounce.cancel();bounce_Fixed.cancel();
                    var unitList = getUnitListData(7);
                    if (mtFalseList && mtFalseList.length > 0) {
                        // 扫描的材料有错误
                        loading.close() ;
                        var html = '';
                        for (let item of mtFalseList) {
                            var option = '<option value="">-----请选择-----</option>';
                            if(unitList && unitList.length >0){
                                for(let it of unitList){
                                    if(item.unit == it.name){
                                        option += '<option selected value="'+ it.id +'">'+ it.name +'</option>';
                                    }else{
                                        option += '<option value="'+ it.id +'">'+ it.name+'</option>';
                                    }
                                }
                            }
                            html +=
                                '<tr data-info=\''+ JSON.stringify(item) +'\'>' +
                                '    <td class="sign" data-name="name">'+ handleNull(item.name) +'</td>' +
                                '    <td class="sign" data-name="code">'+ handleNull(item.code) +'</td>' +
                                '    <td class="sign" data-name="model">'+ handleNull(item.model) +'</td>' +
                                '    <td class="sign" data-name="specifications">'+ handleNull(item.specifications) +'</td>' +
                                '    <td class="sign" data-name="memo">'+ handleNull(item.memo) +'</td>' +
                                '    <td>' +
                                '        <select class="kj-select" type="text" name="unitId" require onchange="importMtUnitChange($(this))">'+ option +'</select>' +
                                '    </td>' +
                                '    <td>' +
                                '        <span class="link-blue btn" data-name="initUpdate">修改</span>' +
                                '        <span class="link-red btn" data-name="initDel">删除</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                        thisPage.find(".importCon1 table tbody").html(html);
                        thisPage.find(".importCon1 .initAll").html(data.importSum);
                        thisPage.find(".importCon1 .initWrong").html(data.falseImportSum);
                        thisPage.find(".importCon1").show().siblings().hide()
                    } else {
                        // 扫描的材料全部正确
                        // var purchasedType = $("#leading").data("type");
                        var json = {
                            mtBasesList: JSON.stringify(mtBaseList),
                            importSum: data.importSum,
                            isPurchased: 1
                        };
                        saveImportMtList(json);
                    }
                })
            } else {
                loading.close() ;
                $('#select_btn_1').val("");
                $('.matListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                bounce_Fixed2.show($("#importantTip"));
            }
        }
    })
}
// creator: 李玉婷，2020-10-13 15:35:58，下一步判断
function allImportMtEnter() {
    var sum = $(".page[page='importInfo'] .importCon1 tbody tr").length;
    var list = [];
    $(".page[page='importInfo'] .importCon1 tbody tr").each(function(){
        var item = $(this).data("info");
        if ($(this).find("select").val() !== '') {
            item.unitId = $(this).find("select").val();
            item.unit = $(this).find("select").find("option:selected").text();
        }
        list.push(item);
    });
    list = JSON.stringify(list);
    $.ajax({
        url:"../mtImport/allImportMtEnter.do",
        data:{
            "mtBasesList": list,
            "importSum": sum
        },
        success:function (data) {
            var errNum = data.falseImportSum;
            $("#importListTj #errNum").html(errNum);
            bounce_Fixed.show($("#importListTj"));
        }
    })
}
// creator: 李玉婷，2020-10-14 15:54:23，下一步确定
function importListTjSure() {
    var list = [];
    var trueList = $(".importCon1").data("trueUserList");
    $(".importCon1 table tbody tr").each(function(){
        var item = $(this).data("info");
        if ($(this).find("select").val() != "") {
            item.unitId = $(this).find("select").val();
            item.unit = $(this).find("select").find("option:selected").text();
        }
        list.push(item);
    });
    for (var i in trueList) {
        list.push(trueList[i]);
    }
    var sum = list.length;
    var isP = $("#importEntryType").data("type");
    list = JSON.stringify(list);
    var json = {
        "mtBasesList": list,
        "isPurchased": 1,
        "importSum": sum
    };
    saveImportMtList(json);
}
// creator: 李玉婷，2020-10-22 17:21:52，导入的材料数据都正确
function saveImportMtList(mtData) {
    $.ajax({
        url: "../mtImport/saveImportMt.do",
        data: mtData,
        success: function (data) {
            $(".page[page='importInfo'] .importCon2").show().siblings().hide();
            bounce_Fixed.cancel();
            getImportLastList(data);
        }
    });
}
// creator: 李玉婷，2020-10-22 21:24:16，输出数据（有计量单位页面）
function getImportLastList(data) {
    var importList = data["mtBaseList"];
    $(".page[page='importInfo'] .importCon2 .initAll").html(data.importSum);
    $(".page[page='importInfo'] .importCon2 .inabledSum").html(Number(data.tureImportSum));
    var str = "";
    var buttonState = data["buttonState"];
    $(".matCategory .category:gt(0)").remove();
    setCategory($("#matCategorySelect"))
    var unitList = getUnitListData(7);
    if (importList && importList.length > 0) {
        for (var i = 0; i < importList.length; i++) {
            var option = '<option value="">-----请选择-----</option>';
            if(unitList && unitList.length >0){
                for(var y = 0 ; y < unitList.length ; y++){
                    var item = unitList[y];
                    if(importList[i].unitId == item['id']){
                        option += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        option += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }
                }
            }
            str +=
                '<tr data-info=\''+ JSON.stringify(importList[i]) +'\'>' +
                '    <td class="sign" data-name="name">'+ handleNull(importList[i].name) +'</td>' +
                '    <td class="sign" data-name="code">'+ handleNull(importList[i].code) +'</td>' +
                '    <td class="sign" data-name="model">'+ handleNull(importList[i].model) +'</td>' +
                '    <td class="sign" data-name="specifications">'+ handleNull(importList[i].specifications) +'</td>' +
                '    <td class="sign" data-name="memo">'+ handleNull(importList[i].memo) +'</td>' +
                '    <td>' +
                '        <select class="kj-select" type="text" name="unitId" require onchange="importMtUnitChange($(this))">'+ option +'</select>' +
                '    </td>' +
                '    <td>' +
                '        <span class="link-blue btn" data-name="update">修改</span>' +
                '        <span class="link-red btn" data-name="del">删除</span>' +
                '    </td>' +
                '</tr>';
        }
    }
    $(".page[page='importInfo'] .importCon2 tbody").html(str);
    if (buttonState == 1) {
        $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImport()");
    } else {
        $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
    }
}
// creator: 李玉婷，2020-10-29 10:56:48，批量导入-新增计量单位
function addImportUnit() {
    $("#addUnit").data("type", 2);
    bounce_Fixed.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: 李玉婷，2021-7-16 10:56:48，批量导入-新增计量单位
function addUnitCommom(num) {
    $("#addUnit").data("type", num);
    bounce_Fixed.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: 李玉婷，2020-10-23 18:54:02，改变导入计量单位
var editUnitObj = null;
function importMtUnitChange(obj) {
    editUnitObj = obj;
    var val = obj.val();
    var info = obj.parents("tr").data("info");
    info.unitId = val;
    updateMtInfo(info, 2);
}
// creator: 李玉婷，2020-10-22 15:36:26，未完成批量导入列表获取
function getUnFinishImportList(type){
    $.ajax({
        url: "../mtImport/unfinishedImportMt.do",
        data: {"isPurchased": 1},
        success: function (data) {
            getImportLastList(data);
        }
    });
}
// creator: 李玉婷，2020-10-14 14:32:48，放弃
function turnCancel(importType, source){
    $.ajax({
        url: "../mtImport/finishImportMt.do",
        data: {
            "isPurchased": 1,
            "type": 0
        },
        success: function (data) {
            var state = data.status;
            if (state == '1') {
                bounce_Fixed.cancel();
                $(".mainCon1").show().siblings().hide();
                if (source == '1') {
                    $('#select_btn_1').val("");
                    $('.matListUpload').remove();
                    $("#leading").data("type", importType);
                    $('#leading .fileFullName').html("尚未选择文件");
                    if (importType == '1') {
                        $("#leading #mould1").attr("href", "../assets/oralResource/template/material_blank_sheet1.xls");
                    } else if (importType == '0'){
                        $("#leading #mould1").attr("href", "../assets/oralResource/template/material_blank_sheet.xls");
                    }
                    bounce_Fixed.show($('#leading'));
                }else if (source == '2') {
                    back()
                }
            } else {
                layer.msg("操作失败！")
            }
        }
    })
}
// creator: 李玉婷，2020-10-23 16:26:57，修改导入的材料
function updateMtInfo(json, editType) {
    // var isPurchased = $("#importEntryType").data("type");
    $.ajax({
        url: "../mtImport/updateImportMt.do",
        data: {
            "id": json.id,
            "code": json.code,
            "name": json.name,
            "specifications": json.specifications,
            "model": json.model,
            "memo": json.memo,
            "unitId": json.unitId,
            "isPurchased": 1
        },
        success: function (data) {
            var status = data.status;
            if (status == '1') {
                var buttonState = data["buttonState"];
                if (editType == '1') {
                    var obj = $("#updateImportMt").data("obj");
                    var trObj = obj.parents("tr");
                    var trData = trObj.data("info");
                    bounce_Fixed.cancel();
                    trObj.find(".sign").each(function () {
                        var name = $(this).data("name");
                        $(this).html($("#updateImportMt input[name=" +name +"]").val());
                    });
                    $("#updateImportMt [require]").each(function () {
                        var key = $(this).attr("name");
                        var val = $(this).val();
                        trData[key] = val;
                    });
                    trObj.data("info", trData);
                } else {
                    editUnitObj.parents("tr").data("info", json);
                }
                if (buttonState == 1) {
                    $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImport()");
                } else {
                    $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
                }
            } else if (status == '-1') {
                var tip = '<p>您录入的材料代号与公司'+ data.name +'材料代号相同。</p>' +
                    '<p>请确认！</p>';
                $("#iknowTip .iknowWord").html(tip);
                bounce_Fixed2.show($("#iknowTip"));
            } else {
                layer.msg('修改失败！');
            }
        }
    });
}
// creator: 李玉婷，2020-10-22 14:46:25，删除导入的材料
function importMtDel() {
    var type = $("#importMtDel").data("type");
    var curObj = $("#importMtDel").data("obj");
    bounce_Fixed.cancel();
    if (type == '1') {//initWrong
        curObj.parents("tr").remove();
        var len = $(".page[page='importInfo'] .importCon1 tbody tr").length;
        $(".page[page='importInfo'] .importCon1 .initWrong").html(len);
    } else {
        var info = curObj.parents("tr").data("info");
        // var isPurchased = $("#importEntryType").data("type");
        $.ajax({
            url:"../mtImport/deleteImportMt.do",
            data:{ "id": info.id , "isPurchased":1 },
            success:function (data) {
                var status = data["status"];
                var buttonState = data["buttonState"];
                if( status == 1 ){
                    layer.msg("操作成功！");
                    curObj.parents("tr").remove();
                    var len = $(".page[page='importInfo'] .importCon2 tbody tr").length;
                    $(".page[page='importInfo'] .importCon2 .inabledSum").html(len);
                    if (buttonState == 1) {
                        $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImport()");
                    } else {
                        $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
                    }
                }else{
                    layer.msg("编辑失败，请稍后重试！");
                }
            }
        })
    }
}
// creator: 李玉婷，2020-11-11 13:57:32，保存
function saveImport(){
    let empty = 0;
    $(".matCategory .category").each(function () {
        if ($(this).val() =="") {
            empty++;
        }
    })
    if (empty > 0) {
        var str = '还有必填项尚未填写！';
        $("#iknowTip .iknowWord").html(str);
        bounce_Fixed2.show($("#iknowTip"));
        return false;
    }
    // var isP = $("#importEntryType").data("type");
    var isP = 1;
    $.ajax({
        "url":"../mtImport/finishImportMtEnter.do",
        "data":{ 'isPurchased': isP},
        success:function(res) {
            var tip = "";
            if (isP == '1') {
                tip = "库管员将收到<span class='red'>须填写</span>初始库存的提示。";
            } else if (isP == '0') {
                tip = "库管员<span class='red'>无法填写</span>这些材料的当前库存数量。";
            }
            $("#importListSave .saveTip").html(tip);
            bounce_Fixed.show($("#importListSave"));
        }
    })
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
        layer.msg("字数不能超过" + max + "个！");
    }
    obj.siblings(".textMax").text(curLength + '/' + max);
}

// 编辑分类
function saveKindEdit(){
    var id = $("#kindEditID").val();
    var name = $("#kindEditName").val();
    bounce.cancel();
    $.ajax({
        url:"../material/updateCategory.do",
        data:{ "id": id , "name":name },
        success:function (data) {
            var status = data["status"];
            if( status == 1 ){
                layer.msg("操作成功！");
                var kindInfo = data["kindInfo"];
                editObj.parent().siblings(".name").html(name);
                // 修改列表
                for(let c of mtCategories){
                    if(c.id == id) {
                        c.name = name;
                    }
                }

            }else{
                layer.msg("编辑失败，请稍后重试！");
            }
        }
    })
}

// 确定删除分类
function okDeleteKind(){
    bounce.cancel();
    var id = $("#delCatTip").data("type");
    $.ajax({
        url:"../material/deleteCategory.do",
        data:{ "id": id  },
        success:function (data) {
            var status = data["status"];
            if( status == 1 ){
                layer.msg("删除成功！");
                editObj.parent().parent().remove();
                mtCategories.splice(mtCategories.findIndex(item => item.id === id), 1);

            }else if( status == 2 ){
                layer.msg("当前分类下有子分类，不可以删除！");
            }else if( status == 3 ){
                layer.msg("当前分类的子分类下有物料，不可以删除！");
            }else if( status == 4 ){
                layer.msg("当前分类下有物料，不可以删除！");
            }
        }
    })

}





// ----------------------------- 定点信息 -------------------------------
// created:hxz 2020-03-13 查看待确认列表
function goConfirm() {
    var count = $("#count").html();
    $(".confirmNum").html(count);
    toggleSuspend(2);

    $(".noFixedPurchaseLogBtn").hide();
    $(".noFixedPurchaseBtn").show().removeClass('ty-color-gray');
}

// creator: 张旭博，2023-08-11 04:23:19， 查看已确定的原辅材料
function seeDeterminedRAAMtList() {
    jumpPage('pointedRAAMt', function (){
        getMtList(1, 20, 4);
    })
}

// creator: 张旭博，2023-08-11 03:59:09， 获取材料列表 （state 此处只用到 1 4）
function getMtList(cur, per, state='', category= null){
    let noHandle = $(".catAll").data("noHandle")
    if (noHandle === 1) {
        $(".seeHide").hide()
    } else {
        $(".seeHide").show()
    }
    $(".searchTip").remove()
    let keyword = $(".ty-searchInput:visible").val() || ''
    // state:查询总列表时不传,  1-查询待确认定点信息时, 2-查询暂停采购的材料时, 3-查询待录入初始库存数量时 4-已确定定点信息
    // keyword : 查询的代号或名称
    if (category) {
        category = Number(category)
    }
    if (keyword) {
        $(".ty-searchInput:visible").val("")
        category = null
    }
    let data = {
        oid: sphdSocket.user.oid,
        pageNum: cur,
        per: per,
        category: category,
        state: state,
        keyword: keyword
    }
    $.ajax({
        url: '../mt/list',
        data: data,
        success:function (res) {
            // 分页处理
            let pageSize = res.totalCount; //总页数
            let jsonStr = JSON.stringify(data) ;
            setPage( $("#ye_mtList" + state), cur, pageSize, "materialEntry", jsonStr );


            var categories = res.categories
            var mtArr = res.data || []


            $("#editRAAMt").data('categories', categories)

            // 改为树形结构，给每一个分类加上children子分类
            let mtCategories = JSON.parse(JSON.stringify(categories))
            let AllCounts = 0
            for (let item of mtCategories) {
                if (!item.children) {
                    item.children = []
                    AllCounts += item.count
                }
                for (let it of mtCategories) {
                    if (it.parent === item.id) {

                        item.children.push(it)
                        item.count = item.count + it.count
                    }
                }
            }
            console.log('mtCategories', mtCategories)
            category = category || null


            let newPath = ``
            let menuStr = ''


            // 填充左侧分类列表 和 右侧分类导航

            let thisItem = mtCategories.find(item => item.id === category )
            let children = mtCategories.filter(item => item.parent === category )
            for (let child of children) {
                const sysCat = ["构成商品的原辅材料", "外购成品", "商品的包装物", "其他原辅材料"] , catName = child.name
                const ctrlStr = `<span class="ctrlBtn">
                                    <i class="fa fa-pencil" type="btn" name="edit" title="修改分类"></i><i class="fa fa-trash" type="btn" name="del" title="删除分类"></i>
                                </span>`
                const isShowCtrl = (category || sysCat.indexOf(catName)=== -1) && catName !== '待分类' && noHandle !== 1
                menuStr += `<li data-id="${child.id}">
                                <span class="catName">${child.name}</span>
                                <span class="catNum">${child.count}</span>
                                ${isShowCtrl ? ctrlStr : ''}
                            </li>`
            }

            if (category === null) {
                if (!keyword) {
                    newPath = `<span class='go2Cat'><span>全部</span><span class="hd"></span></span>`
                } else {
                    newPath = `<span class='go2Cat'><span>全部</span><span class="hd"></span></span><span class="searchTip"> > 查找代号或名称含“${keyword}”的原辅材料</span>`
                }
                $(".curCat").html(newPath);
                $(".rowTree_avatar .tree_footer .handleBtn").hide()
            } else {
                newPath = `<span class='go2Cat'> > <span>${thisItem.name}</span><span class="hd">${thisItem.id}</span></span>`

                $(".curCat").append(newPath);
                $(".rowTree_avatar .tree_footer .handleBtn").show()
            }
            $(".catAll:visible").html(menuStr);
            $(".mtNum").html(AllCounts);

            $("#bottom2").hide(); $("#bottom1").show();


            // 填充材料表格
            var strMt = ''
            if(mtArr && mtArr.length >0){
                for(let item of mtArr){
                    let handleStr = ''
                    if (noHandle !== 1) {
                        handleStr = `<td>
                                        <span class="link-blue" onclick="editRAAMt($(this), 'update')">修改</span>                                
                                        <span class="link-red"  onclick="editRAAMt($(this), 'del')">删除</span>                                
                                        <span class="hd">${JSON.stringify(item)}</span>                                
                                    </td>`
                    } else {
                        handleStr = `<td>
                                        <span class="link-blue" onclick="mtSee($(this))">查看</span>                                
                                        <span class="hd">${JSON.stringify(item)}</span>                                
                                    </td>`
                    }
                    strMt += `<tr>
                                    <td>${item.name}</td>
                                    <td>${item.code}</td>
                                    <td>${item.model || ''}</td>
                                    <td>${item.specifications || ''}</td>
                                    <td>${item.unit || ''}</td>
                                    <td>${item.create_name + ' ' + moment(item.create_date).format('YYYY-MM-DD HH:mm:ss')}</td>
                                    ${handleStr}
                                </tr>`
                }
            } else {
                strMt = `<tr><td colspan="10"><div class="ty-null"><img src="${$.webRoot}/css/resourceCenter/images/nodata.svg" alt=""><p>暂无数据</p></div></td></tr>` ;
            }
            $(".tbl_RAAMt tbody").html(strMt);


            var strMt = "" ;
            if(mtArr){
                for(let item of mtArr){
                    if (state === 1) {
                        // 待确认定点信息物料
                        strMt +=   `<tr>
                                        <td>${item.name || ''}</td>
                                        <td>${item.code || ''}</td>
                                        <td>${item.model || ''}</td>
                                        <td>${item.specifications || ''}</td>
                                        <td>${item.unit || ''}</td>
                                        <td>${item.create_name + ' ' + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                                        <td>
                                            <span class="hd">${JSON.stringify(item)}</span>
                                            <span class="link-blue" onclick="determinePointInfo($(this))">确定定点信息</span>
                                        </td>
                                    </tr>`

                    } else if (state === 4) {
                        // 已确认定点信息物料
                        strMt +=   `<tr>
                                        <td>${item.name || ''}</td>
                                        <td>${item.code || ''}</td>
                                        <td>${item.model || ''}</td>
                                        <td>${item.specifications || ''}</td>
                                        <td>${item.unit || ''}</td>
                                        <td>${item.create_name + ' ' + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                                        <td>
                                            <span class="hd">${JSON.stringify(item)}</span>
                                            <span class="link-blue" onclick="supplierOfMt($(this))">${item.supplier_number || 0}个</span>
                                        </td>
                                    </tr>`

                    }
                }
                if (state === 1) {
                    $(".tbl_undeterminedRAAMt tbody").html(strMt)
                    $(".confirmNum").html(mtArr?mtArr.length:0)
                    $(".determinedNum").html(res.yCount)
                } else {
                    $(".tbl_determinedRAAMt tbody").html(strMt)
                }
            }
        }
    })
}

// creator: 张旭博，2023-08-22 09:47:35， 已确定的定点信息页面 -> 材料列表 -> 供应商按钮点击
function supplierOfMt(selector) {
    jumpPage('determinePointInfo', function () {
        let noHandle = $(".catAll").data("noHandle")
        if (noHandle === 1) {
            $(".seeHide").hide()
        } else {
            $(".seeHide").show()
        }
        let info = JSON.parse(selector.siblings(".hd").html());
        let mtStr = `<tr data-id="${info.id}">
                        <td>${info.name || ''}</td>
                        <td>${info.code || ''}</td>
                        <td>${info.model || ''}</td>
                        <td>${info.specifications || ''}</td>
                        <td>${info.create_name + ' ' + moment(info.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                        <td>${info.unit || ''}</td>
                        <td>
                            <span class="hd">${JSON.stringify(info)}</span>
                            <span class="link-blue" onclick="mtSee($(this))">查看</span>
                        </td>
                    </tr>`
        let mtTheadStr =   `<tr>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>创建人</td>
                                <td>计量单位</td>
                                <td>操作</td>
                            </tr>`
        $("[page='determinePointInfo'] .topRightBtn").hide()
        $("[page='determinePointInfo'] .tbl_mtShow thead").html(mtTheadStr)
        $("[page='determinePointInfo'] .tbl_mtShow tbody").html(mtStr)
        $("[page='determinePointInfo'] .tbl_mtHandle tbody").html('')
        getSuppliers(info.id, 1, 'tbl_mtHandle');
        $(".tbl_mtHandle").data('mtinfo', info)
        $("#noFixedPurchaseBtn").hide()
        if (noHandle === 1) {
            $("#addPoint").hide()
        }
    })
}

// creator: 张旭博，2023-07-27 09:01:12， 待确定的材料列表 - 确定定点信息按钮
function determinePointInfo(selector, type) {
    jumpPage('determinePointInfo', function () {
        let info = JSON.parse(selector.siblings(".hd").html());
        let mtStr = `<tr data-id="${info.id}">
                        <td>${info.name || ''}</td>
                        <td>${info.code || ''}</td>
                        <td>${info.model || ''}</td>
                        <td>${info.specifications || ''}</td>
                        <td>${info.create_name + ' ' + moment(info.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                        <td>${info.unit || ''}</td>
                        <td>
                            <span class="hd">${JSON.stringify(info)}</span>
                            <span class="link-blue" onclick="mtSee($(this))">查看</span>
                        </td>
                    </tr>`
        let mtTheadStr = `<tr>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>创建人</td>
                                <td>计量单位</td>
                                <td>操作</td>
                            </tr>`
        if (type === 'collect') {
            $("[page='determinePointInfo'] .topRightBtn").show()
        } else {
            $("[page='determinePointInfo'] .topRightBtn").hide()
        }
        $("[page='determinePointInfo'] .tbl_mtShow thead").html(mtTheadStr)
        $("[page='determinePointInfo'] .tbl_mtShow tbody").html(mtStr)
        $("[page='determinePointInfo'] .tbl_mtHandle tbody").html('')
        getSuppliers(info.id, 1, 'tbl_mtHandle');
        $(".tbl_mtHandle").data('mtinfo', info)
        $("#noFixedPurchaseBtn").show()
        $("#addPoint").show()
    })

}

// creator: 张旭博，2023-07-28 08:57:59， 确定定点信息页面 -> 材料列表 -> 材料查看按钮
function mtSee(selector) {
    var info = $(".tbl_mtHandle").data('mtinfo')
    if (!info) {
        info = JSON.parse(selector.siblings(".hd").html())
    }
    var data = {
        operation: 10,
        userId: sphdSocket.user.userID,
        id: info.id
    }
    $.ajax({
        url: '../mt/operation',
        data: data
    }).then(res => {
        let data = res.data[0] ;
        for(let key in data){
            $(".scanMt_" + key).html(data[key]);
        }
        $(".scanMt_cat").html(res['path']);
        $(".scanMt_create").html(data['create_name'] + " " + (new Date(data['create_date']).format('yyyy-MM-dd hh:mm:ss')));
        let expStr = ``
        if(data.exp_required === 1){
            expStr = `有保质期方面的要求，`
            if(data.open_duration){
                expStr += `开瓶(开封)后可使用${ data.open_duration }日，`
            }
            // 相关的数据:1-截止日期,2-生产日期
            if(data.related_item === 1){
                expStr += `入库时,需录入可使用的截至日期。`
            }else if(data.related_item === 2){
                expStr += `入库时,需录入生产日期，`
                // 保质期是否相同 0-不相同 1-相同
                if(data.same_expiration === 1){
                    expStr += `不同供应商的供应该材料均为自生产日期之后${ data.expiration_days }日`
                }else{
                    expStr += `不同供应商的供应该材料的保质期不同`
                }
            }

        }else{
            expStr = `无保质期方面的要求`
        }
        $("#mtScan2 .scanMt_expStr").html(expStr)
        bounce.show($("#mtScan2"));

    })
}

// creator: 张旭博，2023-07-27 10:49:17， 确定定点信息页面 -> 中间 -> 添加材料的定点供应商按钮
function editPointInfoBtn(selector, type, pointType) {
    $("#editPointInfo").data("type", type)
    $("#editPointInfo").data("pointType", pointType)
    // 添加本材料的定点供应商
    bounce.show($('#editPointInfo'));
    let mtInfo = $(".tbl_mtHandle").data('mtinfo') // 一开始点进材料就存储的材料信息
    $("#editPointInfo section").hide()
    if (type === 'new') {
        if (pointType === 'mt') {
            $('#editPointInfo .bounce_title').html('添加材料的定点供应商')
            $('#editPointInfo section.part_addPointMt').show()
            $('#editPointInfo section.part_addPointMt input').val('')
            $('#editPointInfo section.part_addPointMt .supplierDes').html('').hide()
            $("#editPointInfo .purUnit").html(mtInfo['unit']);
            // 获取供应商列表
            $.ajax({
                url:"../material/retrievalSupplierByName.do",
                data:{
                    name: '',
                    enabled: 1
                }
            }).then(res => {
                let supplierList = res.supplierList
                // $("#addMtSup").data("choone",list)  //整个数据盒子
                let str = '<option value="" class="supItem">-----请选择-----</option>'
                if(supplierList && supplierList.length > 0){
                    for(let item of supplierList){
                        str += `<option value='${JSON.stringify(item)}' class='supItem'>${item.fullName}</option>`
                    }
                }
                $("#editPointInfo [name='fullName']").html(str);
            })
        } else if (pointType === 'supplier') {
            let thisSection = $('#editPointInfo section.part_addPointSupplier')
            // 获取材料列表
            $.ajax({
                url: $.webRoot + '/mt/list',
                data: { oid: sphdSocket.user.oid }
            }).then(res => {
                let data = res.data
                let list = data
                let str = '<option value="" class="supItem">-----请选择-----</option>'
                if(list && list.length > 0){
                    for(let item of list){
                        str += `<option value='${JSON.stringify(item)}' class='supItem'>${item.name}</option>`
                    }
                }
                $("#editPointInfo [name='mtname']").html(str);
            })

            $('#editPointInfo .bounce_title').html('添加本供应商供应的材料')
            $("#editPointInfo [name='mtcode']").val('')
            thisSection.show()
            // 供应商信息赋值
            let supplierInfo = JSON.parse($(".tbl_supplierShow .hd").html())
            $.ajax({
                url: $.webRoot + '/material/retrievalSupplierByName.do',
                data: { supplierId: supplierInfo.id, enabled: 1 }
            }).then(res => {
                let supplierInfo = res.supplierList[0]
                if (supplierInfo) {
                    let des = supplierInfo.ys
                    let supplierDes = ` <span class="ty-color-green">${des.kp ? des.kp + '，' : ''}</span>
                                <span class="ty-color-orange">${des.gz ? des.gz + '，' : ''}</span>
                                <span class="ty-color-blue">${des.hp}</span>`
                    thisSection.find("[name='fullName']").val(supplierInfo.fullName);
                    thisSection.find("[name='name']").val(supplierInfo.name);
                    thisSection.find("[name='codeName']").val(supplierInfo.codeName);
                    thisSection.find(".supplierDes").html(supplierDes);
                } else {
                    layer.msg('查询错误')
                }
            })
        }
    } else if (type === 'update') {
        // 获取供应商和供应关系信息
        let supInfo = JSON.parse(selector.siblings(".hd").html())
        $('#editPointInfo section.part_changePointFixed').show()
        $('#editPointInfo section.part_relation').show()
        $('#editPointInfo .bounce_title').html('修改定点信息')
        // 获取定点信息，包括材料信息和供应商信息，这里不显示已设置的定点关系信息，而是重置重新设置
        if(pointType === 'supplier') {
            mtInfo = supInfo
        }
        $.ajax({
            url: "../mt/location",
            data: { id: mtInfo.id, supplier: supInfo.supplier_id, relationId: supInfo.material_supplier_id },
            success:function (res) {
                $("#edit_supplier").val(JSON.stringify(res));
                let supplierInfo = res.supplier // 供应商信息
                let supMtInfo = res.mtSupplierMaterial // 供应关系(暂时不做，下方初始化供应关系）
                let orders = res.orders // 未完结的订单数
                let des = res['ys'];  //方框中文字信息

                // 初始化供应关系
                $(".part_relation input:radio").prop("checked", false)
                $(".part_relation .hidePart").hide()
                $(".part_addPointMt .supplierDes").show()
                $(".part_relation input:not(:radio)").val('')
                // 判断修改
                $(".ordersNum").html(orders);
                for (let key in mtInfo) {
                    $("#editPointInfo .mt_" + key).html(mtInfo[key])
                }
                for (let key2 in supplierInfo) {
                    $("#editPointInfo .sup_" + key2).html(supplierInfo[key2])
                }
                // 插入材料类别
                $("#editPointInfo .purUnit").html(mtInfo['unit']);

                if (mtInfo.category_id) {
                    let path = getCategoryPath(mtInfo.category_id)
                    $("#s_cat").html(path)
                }
                setSupInfo(res, $("#supInfo2"));
                let supplierDes = ` <span class="ty-color-green">${des.kp ? des.kp + '，' : ''}</span>
                                <span class="ty-color-orange">${des.gz ? des.gz + '，' : ''}</span>
                                <span class="ty-color-blue">${des.hp}</span>`
                $("#editPointInfo .supplierDes").html(supplierDes);
            }
        })
    }

}

// creator: 张旭博，2023-07-27 11:20:23， 确定定点信息页面 -> 中间 -> 本材料无需定点采购 - 按钮
function noFixedPurchaseBtn(selector) {
    let isHaveSupplier = selector.parents(".ty-alert").siblings("table").find("tbody tr").length > 0
    if (isHaveSupplier) {
        layer.msg("材料下有供应商！")
        return false
    }
    bounce.show($("#bounce_tip"))
    $("#bounce_tip .tipMsg").html("确定本材料无需定点采购？")
    $("#bounce_tip .sureBtn").unbind().on("click", function() {
        let info = $(".tbl_mtHandle").data('mtinfo')
        let data = {
            userId: sphdSocket.user.userID,
            id: info.id,
            is_appointed: 9
        }
        $.ajax({
            url: "../mt/operation",
            data: data ,

        }).then(res => {
            bounce.cancel();
            if(res.code === 1){
                layer.msg("操作成功");
                // 返回上一页，更新列表
                back(() => {
                    getMtList(1, 20, 1)
                })
            }else{
                layer.msg("操作失败")
            }
        })
    })
}

// creator: 张旭博，2024-01-05 02:26:44， 确定 材料供应商 定点关系 -> 确定按钮(添加、修改）
function editMtSupOk(type){
    let editType = $("#editPointInfo").data("type") // 新增还是修改
    let pointType = $("#editPointInfo").data("pointType") // 是材料选供应商还是供应商选材料

    let supplierInfo = {}, mtInfo = {}, supInfo = {}

    if (editType === 'new') {
        if (pointType === 'mt') {
            mtInfo = $("[page='determinePointInfo'] .tbl_mtHandle").data('mtinfo')
            supplierInfo = $(".part_addPointMt [name='fullName']").val() ;
            supplierInfo = JSON.parse(supplierInfo) ;
            if(!supplierInfo){
                layer.msg("请先选择供应商！");
                return false ;
            }
        } else {
            supplierInfo = $("[page='determinePointInfo_editMt'] .tbl_supplierHandle").data('supplierinfo')
            mtInfo = $(".part_addPointSupplier [name='mtname']").val() ;
            mtInfo = JSON.parse(mtInfo) ;
            if(!mtInfo){
                layer.msg("请先选择材料！");
                return false ;
            }
        }
    } else if (editType === 'update') {
        let res = JSON.parse($("#edit_supplier").val());
        supplierInfo = res['supplier'] ; // 供应商
        supInfo = res['mtSupplierMaterial'] ;
    }




    let data =  {
        org: sphdSocket.user.oid,
        supplierId: supplierInfo.id, // 供应商id
        hasContact: '', // 此材料是否包含于与供应商已签订的合同中？
        priceStable: '', // 该供应商供应的本材料价格是否稳定？
        materialInvoicable: '', // 购买本材料是否给开发票？
        materialInvoiceCategory: '', // 购买本材料给开何种发票？
        isTax: '', // 是否含税？
        inclusiveFreight: '', // 该价格是否含运费?
        packageMethod: '', // 所供应材料的包装方式
        unitPrice: null,
        expirationDays: null,
        perchaseCycle: null, // 采购周期
        minimumPurchase: null, // 最低采购量
        minimumStock: null, // 最低库存
        taxRate: null, // 税率
        supplierInvoice: null, // 税率id
        contractSn: null, // 合同 名字？
        deliveryAddress: null, // 收获地址
        init: 1, // 初始化需要传的
        rtl: '' // 初始化需要传的
    };
    if (editType === 'new') {
        data.operation = 1
        data.mtId = mtInfo.id
    } else if (editType === 'update') {
        data.supplierMaterialId = supInfo.id
        data.mtId = supInfo.material_
    }
    if (pointType === 'supplier') {
        data.rtl = 1
    }
    for (let key in data) {
        let selector = $(".mtAndSupplier [name='"+key+"']")
        let type = selector.attr('type')
        let value = ''
        if (selector.is(":visible")) {
            if (type === 'radio') {
                value = $(".mtAndSupplier input[name='"+key+"']:checked").val()
            } else {
                if (key === 'contractSn') {
                    value = selector.find('option:selected').html()
                    if (value.length > 6) {
                        value = value.slice(0, -6)
                    }
                } else {
                    value = selector.val()
                }

            }
            data[key] = value
        }
    }
    if(data.hasContact === undefined){
        layer.msg("请选中此材料是否包含于与供应商已签订的合同中");
        return false;
    }

    if(data.priceStable === undefined) {
        layer.msg("请选择该供应商供应的本材料价格是否稳定");
        return false;
    }
    if(data.inclusiveFreight === undefined){
        layer.msg("请选择该价格是否含运费");
        return false;
    }
    if(data.inclusiveFreight === "1" && $(".mtAndSupplier .manageAddressList").html() === ""){
        layer.msg("请选择收货地址");
        return false;
    }
    if(data.inclusiveFreight === "2" && $(".mtAndSupplier .manageAreaList").html() === ""){
        layer.msg("请选择到货区域");
        return false;
    }
    if(data.materialInvoicable === undefined){
        layer.msg("请选择购买本材料是否能开发票");
        return false;
    }
    if(data.isTax === undefined){
        layer.msg("请选择是否含税");
        return false;
    }
    if(data.materialInvoiceCategory === undefined){
        layer.msg("请选择购买本材料给开何种发票");
        return false;
    }
    if(data.supplierInvoice === ''){
        layer.msg("请选择税率");
        return false;
    }
    if(data.unitPrice === ''){
        layer.msg("请输入单价");
        return false;
    }
    if(data.packageMethod === undefined){
        layer.msg("请选择所供应材料的包装方式");
        return false;
    }
    if(data.perchaseCycle === ''){
        layer.msg("请输入采购周期");
        return false;
    }
    if(data.minimumPurchase === ''){
        layer.msg("请输入最低采购量");
        return false;
    }
    if(data.minimumStock === ''){
        layer.msg("请输入最低库存");
        return false;
    }
    if ($("#editPointInfo .part_exp").is(":visible")){
        if (data.expirationDays === '') {
            layer.msg("请输入保质期");
            return false;
        }
    }
    let addressIds = $(".mtAndSupplier .address_box:visible").siblings(".hd").html();
    data.deliveryAddress = addressIds
    if (data.materialTaxRate) {
        data.materialTaxRate = data.materialTaxRate.replace(/\%/, "");
    }
    if (data.taxRate) {
        data.taxRate = data.taxRate.slice(0, -1)
    }

    if(data.isTax !== "1"){
        data.unitPriceNotax = data.unitPrice;
    }
    console.log('data', data)
    $.ajax({
        url:"../material/locationSuppliers",
        data: data ,
        success:function(res){
            if(res == 1){
                layer.msg("操作成功");
                reloadPage()
            }else if(res == 2){
                layer.msg("该供应商已经存在，无需再次添加");
            }else if(res == 3) {
                layer.msg("有未完成的添加审核，不能操作");
            } else {
                layer.msg("操作失败");
            }
            // getSuppliers(mtInfo.id, 1, 'tbl_mtHandle');
            bounce.cancel();
        }
    })
}

// creator: sy 2022-07-20 从本合同种添加商品弹窗中的确定按钮
function submget(){
    let fun = $("#addcontract").data("fun");
    let listEditt = [];
    let chooseGood2 = $("#goodLust").data("chooseGood") || [];
    $("#addcontract .fa-check-square-o").each(function(i) {
        let data = JSON.parse($(this).parent().siblings().children(".controlTd").children(".hd").html());
        listEditt.push(data);
    });
    let newChooseGood = [...listEditt,...chooseGood2];//将原来的和新点击的数据合在一个数组里
    $("#goodLust").data("chooseGood",newChooseGood);
    let newChooseGoodName = newChooseGood.map((item,index) => {
        return item.name;
    });
    let conster = newChooseGood.length;
    $("#contdown").html(conster);
    let goodNameStr = newChooseGoodName.join("、");//在每个字符串之间加上符号
    $("#goodLust").html(goodNameStr);

    bounce_Fixed3.cancel();
}

// crearor:hxz 2020-09/08 查看定点信息
function fixedScanInfo(mtID, supID, relationID,supInfo) {
    $.ajax({
        url: "../mt/location",
        data: { id: mtID, supplier: supID, relationId: relationID },
        success:function (res) {
            $("#edit_supplier").val(JSON.stringify(res));
            var supplierInfo = res.supplier // 供应商信息
            var supMtInfo = res.mtSupplierMaterial // 供应关系
            var orders = res.orders // 未完结的订单数
            var des = res['ys'];  //方框中文字信息

            var enabled = supMtInfo['enabled'];
            // 判断修改
            $(".ordersNum").html(orders);
            $("#pointChange .fa").each(function(){
                $(this).attr("class", "fa fa-circle-o");
                $(this).removeAttr("disabled");
                $(this).parent().removeAttr("disabled");
            })
            chargeShowInvoiceEditSup();
            bounce.show($('#pointChange'));
            var info = $(".tbl_mtHandle").data('mtinfo')
            for (let key in info) {
                $("#pointChange .mt_" + key).html(info[key])
            }
            for (let key2 in supplierInfo) {
                $("#pointChange .sup_" + key2).html(supplierInfo[key2])
            }
            $("#s_cat").html(setCatStr(info['category_id']));
            setSupInfo(res, $("#supInfo2"));
            var priceInfo = chargePriceShow(supMtInfo);
            $("#priceTtl").html(priceInfo.ttl);
            $("#priceCon").html(priceInfo.info);
            let supplierDes = ` <span class="ty-color-green">${des.kp ? des.kp + '，' : ''}</span>
                                <span class="ty-color-orange">${des.gz ? des.gz + '，' : ''}</span>
                                <span class="ty-color-blue">${des.hp}</span>`
            $(".supplierDes").html(supplierDes);
            $("#mtTip_s").html(setSupMtInfo(des));
            $("#pointChange .purUnit").html(info['unit']);

        }
    })
}

function chargeShowInvoiceEditSup(){
    var isStable = $("#edit_isStable").val();
    var res = JSON.parse($("#edit_supplier").val());
    var supplier = res['mtSupplierMaterial']['supplier'] ; // 供应商
    var supInfo = res['mtSupplierMaterial'] ;
    var supplier1 = res['supplier'];
    console.log(supInfo);
    if(supInfo){
        // supplier1 = JSON.parse(supplier1) ;
        var supplierMaterial = supplier1['supplierMaterial'];
        for(var key in supplierMaterial){
            supplier1[key] = supplierMaterial[key]
        }
        var canInvoice = $("#edit_canInvoice").val() ;
        var incoiceTypeVal = $("#edit_incoiceType").val() ;
        if(ontractsigned === "1"){//选中是
            $(".contrInfo").show();
            $("#contract").removeAttr("disabled");
        }else if(ontractsigned ==="0"){//选中否
            $("#contract").attr("disabled","disabled");
            $(".contrInfo").hide();
        }
        if(isStable === "1"){ //稳定
            $(".priceInfo").show();
            $(".incoiceType4").hide();
            $(".canInvoice2").hide();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show();
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".priceInfo .type3").html("已约定的单价"); $("#edit_isParValue").val(1);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $("#edit_isParValue").val(1);
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("已约定的<span class='supOrange'>开票</span>单价");
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("已约定的<span class='supOrange'>不开票</span>单价");
                $("#edit_isParValue").val(0);
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
                $(".incoiceType").hide();$("#edit_isParValue").val("");
            }
        }else if(isStable === "2"){ // 变动频繁
            $(".priceInfo").show();
            $(".canInvoice2").show();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show();
                $(".incoiceType4").hide();
                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".type3").html("参考单价");
                    $("#edit_isParValue").val(0);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("参考单价(<span class='supOrange'>开票价</span>)");
                    $("#edit_isParValue").val(0);
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
                $("#edit_isParValue").val(0);
            }else if(canInvoice === "2"){ // 不确定
                if(supInfo["invoiceCategory"] === "2"){ // 供应商只能开普通票
                    $(".priceInfo .type1").html("参考单价");
                    $("#edit_isParValue").val("");
                }else{
                    $("#edit_isParValue").val(0);
                    $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
                }
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
            }
        }else {
            $(".incoiceType").hide();
        }
    }else{
        // 初始化
        $(".stable").hide();
    }
}
function chargeShowInvoice() {
    var isStable = $("#isStable").val();
    var supplier = $("#supplier").val() ;
    if(supplier){
        supplier = JSON.parse(supplier) ;
        var supplierMaterial = supplier['supplierMaterial'];
        for(var key in supplierMaterial){
            supplier[key] = supplierMaterial[key]
        }
        var canInvoice = $("#canInvoice").val() ;
        var incoiceTypeVal = $("#incoiceType").val() ;//如何让它变成1呢
        /*if(supplier["invoicable"] === "1"){
            $(".canInvoice").show();
            if(supplier["vatsPayable"] === "1"){ // 能开增值税专用发票 vatsPayable
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
            }else{ // 只能开普通票
                incoiceTypeVal = "2";  $("#incoiceType").val(incoiceTypeVal);
                $("#incoiceType1").attr("disabled", "disabled");
                $("#incoiceType1Con").attr("disabled", "disabled");
                $("#incoiceType0").attr("class", "fa fa-dot-circle-o");

            }
        } else{
            canInvoice = "0"; $("#canInvoice").val(canInvoice)
            //$(".canInvoice").hide();
            $(".incoiceType").hide();
        }*/
        if(ontractsigned === "1"){//是
            $(".contrInfo").show();
            $("#contract").removeAttr("disabled");
        }
        else if(ontractsigned ==="0"){//选中否
            $("#contract").attr("disabled","disabled");
            $(".contrInfo").hide();
        }
        if(isStable === "1"){ //稳定
            $(".priceInfo").show();
            $(".incoiceType4").hide();
            $(".canInvoice2").hide();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show();
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".priceInfo .type3").html("已约定的单价"); $("#isParValue").val(1);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $("#isParValue").val(1);
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("已约定的<span class='supOrange'>开票</span>单价");
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("已约定的<span class='supOrange'>不开票</span>单价");
                $("#isParValue").val(0);
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
                $(".incoiceType").hide();$("#isParValue").val("");
            }
        }else if(isStable === "2"){ // 变动频繁
            $(".priceInfo").show();
            $(".canInvoice2").show();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show()
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
                $(".incoiceType4").hide();
                $(".type1").hide();

                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".type3").html("参考单价");
                    $("#isParValue").val(0);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("参考单价(<span class='supOrange'>开票价</span>)");
                    $("#isParValue").val(0);
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
                $("#isParValue").val(0);
            }else if(canInvoice === "2"){ // 不确定
                //if(supplier["invoiceCategory"] === "2"){ // 供应商只能开普通票
                $(".priceInfo .type1").html("参考单价");
                $("#isParValue").val("");
                //}else{
                //    $("#isParValue").val(0);
                //    $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
                //}
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
            }
        }else {
            $(".incoiceType").hide();
        }
    }else{
        // 初始化
        $(".stable").hide();
    }
}
function chargePriceShow(supInfo) {
    var supplier = supInfo ;

    // var ttl = "", info = supplier["isTax"] === "1"?"含税":"不含税";
    var ttl = "", info = "";
    var isStable = supplier["priceStable"];
    var canInvoice = String(supplier["materialInvoicable"]);
    var incoiceTypeVal = supplier["materialInvoiceCategory"];

    if(isStable === "1"){ //稳定
        if(canInvoice === "1"){ // 能开票
            ttl = "已约定的单价";
            info += "开票价";
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "已约定的单价";
            info += "不开票价";
        }
    }else if(isStable === "2"){ // 变动频繁
        if(canInvoice === "1"){ // 能开票
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                ttl = "参考单价";
                info += "开票价";
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                ttl = "参考单价";
                info += "开票价";
            }
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "参考单价";
            info += "不开票价";

        }else if(canInvoice === "2"){ // 不确定
            ttl = "参考单价";
            info += "不开票价";
        }
    }
    var price = supplier["unitPriceNotax"]
    if(supplier["isTax"] == "1"){ // 含税
        price = supplier["unitPrice"];
    }
    info += price + "元";
    var infoall = ""
    switch (String(supplier["inclusiveFreight"])){
        case "1":
            infoall = "含运费的" + info ;
            break;
        case "2":
            infoall = "含运费的" + info + "，但材料到本市后需我司自提";
            break;
        case "3":
            infoall = "不含运费的" + info ;
            break;
        default:
            infoall = info ;
    }
    return { "ttl":ttl , "info":infoall }

}
// creator:hxz 2019-12-26 总结采购信息
function setSupMtInfo(bon) {
    console.log(bon);
    var str = '' ;
    str += `
        <span class='supBlue'>${bon.cght ? bon.cght + ',' : ''}</span>
        <span class='supOrange'>${bon.bz ? bon.bz + ',' : ''}</span>
        <span class='supGreen'>${bon.zdkc ? bon.zdkc + ',' : ''}</span>
        <span class='supOrange'>${bon.zdcg ? bon.zdcg + ',' : ''}</span>
        <span class='supGreen'>${bon.cgzq}</span><br/>
        <span class='supOrange'>${bon.jgwd ? bon.jgwd + ',' : ''}</span>
        <span class='supGreen'>${bon.kpys ? bon.kpys + ',' : ''}</span>
        <span class='supOrange'>${bon.yfk}</span>
    `;
    return str ;
}

// crearor:hxz 2020-04-24 无需定点采购
function noFixedPurchase() {
    let info = $(".tbl_mtHandle").data('mtinfo')
    var data = {
        userId:sphdSocket.user.userID,
        id:info.id,
        is_appointed:9
    }
    $.ajax({
        "url":"../mt/operation",
        "data":data ,
        success:function(res){
            bounce.cancel();
            if(res['code'] == 1){
                layer.msg("操作成功");
                goConfirm(2);
            }else{
                layer.msg("操作失败")
            }
        }
    })
}
// crearor:hxz 2020-04-24 返回上一页
function hidePause4() {
    $(".pauseSup").hide().siblings().show();
    $("#hidePause4").hide();
    var info = editMtSupplierObj.parent().next().children(".hd").html()
    info = JSON.parse(info)
    $(".mt42 tbody").html("");
    getSuppliers(info.id, 1, 'mt42');
}
// crearor:hxz 2019-12-25 保存编辑的供应商
function saveNewSupply(){
    var name = $("#e_gName").val();   // string    供应商名称
    var name2 = $("#e_gName2").val();   // string    供应商名称
    var gCode = $("#e_gCode").val() ; // 供应商代号
    gCode = $.trim(gCode) ;   name2 = $.trim(name2) ;     name = $.trim(name) ;
    // var hasContact = $("#haveContract").val() ;  //  boolean  '是否已与其签订采购合同;true-有,false-无',
    var contractSn = $("#e_gCompactNo").val() ;  // varchar(100)    '采购合同编号',
    var validDate = $("#e_gCompactExpire").val() ; //  date '有效期至',(yyyy-MM-dd)
    var signDate = $("#e_gCompactSignDay").val(); // date '签署日期',(yyyy-MM-dd)
    var hasContact = $("#contread").val();// boolean '此材料是否包含于与供应商已签订的合同中',
    //var invoicable = $("#haveInvoice").val()  ; // boolean  '该供应商是否能开发票::true-可开具,false-不能开',
    //var invoiceCategory = $("#vatInvoice").val()  ; // boolean  '是否能开增值税专用发票1:: 普通发票2
    //var taxRate = $("#e_gRate0").val() ; // 该合同开发票的税率
    //taxRate = $.trim(taxRate) ;
    var chargeAcceptable = $("#setHangAccount").val() ; // 是否接受挂账
    var chargePeriod = $("#hangDays").val() ; // 账期
    var chargeBegin = $("#setStartDate").val() ; // 1 - 自入库之日起 0 - 自发票提交之日起
    var isImprest = $("#setAdvanceFee").val() ; //  是否需要预付款 :0-不确定,1-需要,2-不需要
    var draftAcceptable = $("#hui").val() ; // 汇票 1接受 0不接受 2不确定
    var imprestProportion = $("#e_gRate1").val();//需预付的比例
    var proportion = $("#uncertainty").val();//比例不确定
    if(name === ""){
        // layer.msg("供应商名称不能为空！");
        bounce.show($("#tip1"));$("#contracto").hide();$("#mustchoose").show();return false ;
    }
    if(name2 === ""){
        // layer.msg("供应商简称不能为空！");
        bounce.show($("#tip1"));$("#contracto").hide();$("#mustchoose").show();return false ;
    }
    if(gCode === ""){
        // layer.msg("供应商代号不能为空！");
        bounce.show($("#tip1")); $("#contracto").hide();$("#mustchoose").show();return false ;
    }
    // if(hasContact === ""){ layer.msg("请选择“是否已与其签订采购合同”项目！");bounce.show($("#tip1")); return false ;  }
    // if(hasContact === "1"){
    //     if(validDate === ""){  layer.msg("合同有效期至不能为空！");bounce.show($("#tip1")); return false ;     }
    //     if(signDate === ""){  layer.msg("合同签署日期不能为空！");bounce.show($("#tip1")); return false ;     }
    //     if(contractSn === ""){  layer.msg("合同编号不能为空！");bounce.show($("#tip1")); return false ;     }
    // }
    // if(invoicable === ""){
    //     // layer.msg("请选择“该供应商是否能开发票”项目！");
    //     bounce.show($("#tip1")); $("#contracto").hide();$("#mustchoose").show();return false ;
    // }
    // if(invoicable === "1"){
    //     if(invoiceCategory === ""){
    //         // layer.msg("请选择“是否能开增值税专用发票”项目！");
    //         // bounce.show($("#tip1"));
    //         $("#contracto").hide();$("#mustchoose").show();return false ;
    //     }
    //     if(invoiceCategory === "1"){
    //         if(taxRate === ""){
    //             // layer.msg("税率不能为空！");
    //             bounce.show($("#tip1")); $("#contracto").hide();
    //             $("#mustchoose").show();return false ;
    //         }
    //         if(draftAcceptable === ""){
    //             // layer.msg("请选择“是否可接受汇票”项目！");
    //             bounce.show($("#tip1")); $("#contracto").hide();
    //             $("#mustchoose").show();return false ;
    //         }
    //     }
    // }
    if(chargeAcceptable === ""){
        // layer.msg("请选择“是否接受挂账”项目！");
        bounce.show($("#tip1")); $("#contracto").hide();$("#mustchoose").show();return false ;
    }
    if(chargeAcceptable === "1") {
        if (chargePeriod === "") {
            // layer.msg("请录入已约定的账期！");
            bounce.show($("#tip1"));
            $("#contracto").hide();
            $("#mustchoose").show();
            return false;
        }
        if (chargeBegin === "") {
            // layer.msg("请选择“请选择从何时开始计算账期”项目！");
            bounce.show($("#tip1"));
            $("#contracto").hide();
            $("#mustchoose").show();
            return false;
        }
    }
    if(isImprest === ""){
        // layer.msg("请选择“是否需要预付款”项目！");
        bounce.show($("#tip1"));$("#contracto").hide();$("#mustchoose").show(); return false ;
    }
    if(isImprest === "1"){
        if(proportion === "1"){
            imprestProportion = "-1";
        }
        if(imprestProportion === ""){
            // layer.msg("请录入需预付的比例");
            bounce.show($("#tip1"));
            $("#contracto").hide();
            $("#mustchoose").show();
            return false;
        }
    }
    if(imprestProportion == "0"){   //预付款比例
        imprestProportion = "0.01";
    }else if(imprestProportion == "100"){
        imprestProportion = "99.99";
    }

    var info = $(".tbl_mtHandle").data('mtinfo')


    var supItem = {
        "org": sphdSocket.user.oid,
        // "mtId": info.id ,
        "supplierId": "" , // 供应商id
        "fullName": name , // 供应全称
        "name": name2 , // 供应名称
        "codeName": gCode , // 代号
        // "hasContact": hasContact ,
        "contractSn": contractSn ,
        "validDate": validDate ,
        "signDate": signDate ,
        //"invoicable": invoicable ,
        //"invoiceCategory": invoiceCategory ,
        "draftAcceptable": draftAcceptable ,
        //"taxRate": taxRate ,
        "chargeAcceptable": chargeAcceptable ,
        "chargePeriod": chargePeriod ,
        "chargeBegin": chargeBegin ,
        "isImprest": isImprest,
        "imprestProportion":imprestProportion   //预付款比例
    };

    $.ajax({
        url: '../material/locationSuppliers',
        data: supItem ,
        success:function(res){
            bounce.cancel();
            if(res == 1){
                layer.msg("操作成功");
            }else  if(res == 2){
                layer.msg("该供应商已经添加过了，无需再次添加");
            }else  if(res == 3){
                layer.msg("有未完成的添加审核，不能操作");
            }else{
                layer.msg("操作失败");
            }
        }
    })
}
// creator: hxz 2020-04-23  所属种类序列
function setCatStr(category_id) {
    var curCatObj = $("#catTree").find(".treeItem" + category_id);
    var pCatsArr = curCatObj.parents("[class*='treeItem']");
    var catsArr = [];
    pCatsArr.each(function(){
        var kls = $(this).attr("class");
        var catId = kls.substr(8, kls.length);
        catsArr.push(catId);
    })
    catsArr.reverse().push(category_id);
    var str = "" ;
    let mtCategories = $("#editRAAMt").data('categories')
    for(var i = 0 ; i < catsArr.length ; i++){
        for(var j = 0 ; j < mtCategories.length ; j++){
            if(mtCategories[j]['id'] == catsArr[i]){
                str += mtCategories[j]['name'] + "> "
            }
        }
    }
    return str ;
}

// creater: hxz 2020-04-22 修改定点供应商确定
// function editMtSupOk(){
//     var res = JSON.parse($("#edit_supplier").val());
//     var supplier = res['supplier'] ; // 供应商
//     var supInfo = res['mtSupplierMaterial'] ;
//
//     var supID = supplier['id'];
//     var supplierMaterial = supplier['supplierMaterial'];
//     for(var key in supplierMaterial){
//         supplier[key] = supplierMaterial[key]
//     }
//     // supplier["isInclude"] = $("#edit_containThis").val(); // 合同是否包含本物料:1-包含,0—不包含
//     // if(supplier["hasContact"] === "1"){
//     //     if(supplier["isInclude"] !== "1" && supplier["isInclude"] !== "0"){
//     //         layer.msg("请选择采购合同是否包含本材料");
//     //         return false;
//     //     }
//     // }
//     // supplier["isInclude"]=$("#ontractsigned").val();//是否包含供应商已签订的合同中：1-是，0-否
//     supplier["hasContact"]=$("#edit_urgnistad").val();//是否包含供应商已签订的合同中：1-是，0-否
//     if(supplier["hasContact"] !=="1" && supplier["hasContact"] !== "0"){
//         layer.msg("请选中此材料是否包含于与供应商已签订的合同中");
//         return false;
//     }else if(supplier["hasContact"] === "1"){
//         var contact = $("#contract1").val();// 本材料在哪个合同中
//         contact = JSON.parse(contact);
//         supplier["contractSn"] = contact.sn;
//     }
//     supplier["priceStable"] = $("#edit_isStable").val();  //价格是否稳定:1-相对稳定,2-变动频繁
//     if(supplier["priceStable"] !== "1" && supplier["priceStable"] !== "2"){
//         layer.msg("请选择该供应商供应的本材料价格是否稳定");
//         return false;
//     }
//     supplier["isParValue"] = $("#edit_isParValue").val();  //是否为开票价格 1-是,0-否
//     supplier["inclusiveFreight"] = $("#edit_containYunFee").val();  //是否包含运费 是否包含运费:1-为送货上门价格,含所有运费/2-含长途运输费用,到本市后需我司自提/3-为离厂价格,不包含任何运费
//     if(supplier["inclusiveFreight"] !== "1" && supplier["inclusiveFreight"] !== "2" && supplier["inclusiveFreight"] !== "3"){
//         layer.msg("请选择该价格是否含运费");
//         return false;
//     } else {
//         if(supplier["inclusiveFreight"] === "1" && $("#pointChange .manageAddressList").html() === ""){
//             layer.msg("请选择收货地址");
//             return false;
//         } else {
//             supplier["deliveryAddress"] = $("#pointChange .manageAddressList").siblings(".hd").html();  //是否包含运费 是否包含运费:1-为送货上门价格,含所有运费/2-含长途运输费用,到本市后需我司自提/3-为离厂价格,不包含任何运费
//         }
//         if(supplier["inclusiveFreight"] === "2" && $("#pointChange .manageAreaList").html() === ""){
//             layer.msg("请选择到货区域");
//             return false;
//         }
//     }
//     supplier["atPar"] = $("#edit_price").val();  //已约定单价(参考价格)
//     supplier["materialInvoicable"] = $("#edit_canInvoice").val(); //本物料能否开发票  1可以 0不可以 2不确定
//     supplier[" "] = $("#edit_incoiceType").val();  //本物料发票类型:1-增值税专票,2-普通发票 4-不给开票
//     supplier["materialTaxRate"] =  "";  //本物料税率  普通票不传，开增值传前面设置的税率
//     supplier["isTax"] =  $("#edit_referPrice").val();  // 是否含税
//     supplier["materialTaxRate"] = $("#edit_taxRate").val().replace(/\%/, "");
//     supplier["supplierInvoice"] = $("#edit_taxRateId").val();
//
//     if(supplier["materialInvoicable"] === "1"){
//         if(supplier["materialInvoicable"] !== "1" && supplier["materialInvoicable"] !== "2" && supplier["materialInvoicable"] !== "0"){
//             layer.msg("请选择购买本材料是否能开发票");
//             return false;
//         }
//         if(supplier["materialInvoicable"] === "1"){
//             if(supplier["materialInvoiceCategory"] !== "1" && supplier["materialInvoiceCategory"] !== "2" && supplier["materialInvoiceCategory"] !== "4"){
//                 layer.msg("请选择购买本材料给开何种发票");
//                 return false;
//             }
//             if(supplier["materialInvoiceCategory"] === "1"){
//                 if(supplier["isTax"] !== "1" && supplier["isTax"] !== "0" ){
//                     layer.msg("请选择是否含税");
//                     return false;
//                 }
//                 if(supplier["supplierInvoice"] === "" ){
//                     layer.msg("请选择税率");
//                     return false;
//                 }
//             }
//         }
//     }
//     supplier["unitPrice"] =  $("#edit_price").val();
//     if(supplier["unitPrice"] === ""){
//         layer.msg("请输入单价");
//         return false;
//     }
//     supplier["packageMethod"] =  $("#edit_packgeType").val(); //包装方式:1-基本固定,2-型式不定
//     if(supplier["packageMethod"] !== "1" && supplier["packageMethod"] !== "2" ){
//         layer.msg("请选择所供应材料的包装方式");
//         return false;
//     }
//     supplier["perchaseCycle"] =  $("#edit_purTurn").val();  //采购周期
//     if(supplier["perchaseCycle"] === ""){
//         layer.msg("请输入采购周期");
//         return false;
//     }
//     supplier["minimumPurchase"] =  $("#edit_minPur").val();  //最低采购量
//     if(supplier["minimumPurchase"] === ""){
//         layer.msg("请输入最低采购量");
//         return false;
//     }
//     supplier["minimumStock"] =  $("#edit_minStorage").val(); //最低库存
//     if(supplier["minimumStock"] === ""){
//         layer.msg("请输入最低库存");
//         return false;
//     }
//     var info = $(".tbl_mtHandle").data('mtinfo')
//     var supItem = {
//         org: sphdSocket.user.oid,
//         mtId: info.id ,
//         supplierId: supID , // 供应商id
//         priceStable: supplier["priceStable"] ,
//         inclusiveFreight: supplier["inclusiveFreight"] ,
//         packageMethod: supplier["packageMethod"] ,
//         perchaseCycle: supplier["perchaseCycle"] ,
//         minimumPurchase: supplier["minimumPurchase"] ,
//         minimumStock: supplier["minimumStock"] ,
//         materialInvoicable: supplier["materialInvoicable"] ,
//         materialInvoiceCategory: supplier["materialInvoiceCategory"] ,
//         taxRate: supplier["materialTaxRate"] ,
//         supplierInvoice: supplier["supplierInvoice"] ,
//         supplierMaterialId: supInfo["id"] ,
//         isTax: supplier["isTax"] ,
//         isInclude: supplier["isInclude"],
//         hasContact:supplier["hasContact"],
//         contractSn:supplier["contractSn"],
//         deliveryAddress:supplier["deliveryAddress"]
//     };
//     if(supplier["isTax"] === "1"){
//         supItem["unitPrice"] =  supplier["unitPrice"];
//     }else{
//         supItem["unitPriceNotax"] =  supplier["unitPrice"];
//     }
//     $.ajax({
//         "url":"/material/locationSuppliers",
//         "data": supItem ,
//         success:function(res){
//             if(res == 1){
//                 layer.msg("操作成功");
//
//             }else{
//                 layer.msg("操作失败");
//             }
//             var tbid = $('#tdID').val( ) ;  //现在它没有值
//             getSuppliers(info.id, 1, 'tbl_mtHandle');
//             bounce.cancel();
//
//         }
//     })
// }
// created:hxz 2020-03-11 物料查看
function mtScan() {
    var info = $(".tbl_mtHandle").data('mtinfo')
    var data = { 'operation':10 , 'userId':sphdSocket.user.userID , 'id':info['id'] };
    $.ajax({
        "url":"../mt/operation",
        "data":data ,
        success:function(res){
            var data = res['data'][0] ;
            for(var key in data){
                if(key == 'create_name' || key == 'create_date' ){

                }else{
                    $(".scanMt_" + key).html(data[key]);
                }
            }
            var str = "本材料录入时为“曾采购过的材料”。"
            switch (Number(data.is_purchased)){
                case 1 :
                    str = "本材料录入时为“曾采购过的材料”。" ; break;
                case 0 :
                    str = "本材料录入时为“未采购过的材料”。" ; break;

            }
            $(".scanMt_state").html(str)

            $(".scanMt_cat").html(res['path']);
            $(".scanMt_create").html(data['create_name'] + " " + (new Date(data['create_date']).format('yyyy-MM-dd hh:mm:ss')));
            let mode = $("body").data('whmode')
            if (mode === 1) {
                let expStr = ``
                if(data.exp_required === 1){
                    expStr = `有保质期方面的要求，`
                    if(data.open_duration){
                        expStr += `开瓶(开封)后可使用${ data.open_duration }日，`
                    }
                    // 相关的数据:1-截止日期,2-生产日期
                    if(data.related_item === 1){
                        expStr += `入库时,需录入可使用的截至日期。`
                    }else if(data.related_item === 2){
                        expStr += `入库时,需录入生产日期，`
                        // 保质期是否相同 0-不相同 1-相同
                        if(data.same_expiration === 1){
                            expStr += `不同供应商的供应该材料均为自生产日期之后${ data.expiration_days }日`
                        }else{
                            expStr += `不同供应商的供应该材料的保质期不同`
                        }
                    }

                }else{
                    expStr = `无保质期方面的要求`
                }
                $("#mtScan .scanExpShow").show()
                $("#mtScan .scanMt_expStr").html(expStr)
            } else {
                $("#mtScan .scanExpShow").hide()
            }
            bounce.show($("#mtScan"));
        }
    })
}

// creater: hxz 2020-04-21 获取某物料的供应商列表
function getSuppliers(mtId, enabled, selector, hasHandle = true) {
    var data = {
        id: mtId,
        enabled: enabled
    }
    $.ajax({
        url: '../mt/suppliers',
        data: data,
        success:function (res) {
            let noHandle = $(".catAll").data("noHandle")
            var is_appointed = res['is_appointed']; // 9 表示无需定点采购
            var list = res['data'] || [];
            var str = "";
            if(is_appointed === '9' || is_appointed === '0'){
                $(".page:visible .pointSupplierTip").html("本材料由"+ res.appointed_user +"于"+ (new Date(res.appointed_time).format("yyyy-MM-dd hh:mm:ss")) +"确定为无需定点采购。");
                // $(".needAddMtSupplier").hide()
            }else{
                $(".page:visible .pointSupplierTip").html("本材料现有如下"+ list.length +"个定点供应商");
                // $(".needAddMtSupplier").show()
            }
            if(list && list.length>0){
                for(let item of list){
                    let mtInvoiceAble = "不确定";
                    let material_invoiceable =  Number(item['material_invoiceable'] );
                    if(material_invoiceable === 0){
                        mtInvoiceAble = "否";
                    }else if(material_invoiceable === 1){
                        mtInvoiceAble = "是";
                    }else if(material_invoiceable === 2){
                        mtInvoiceAble = "不确定";
                    }
                    let handleStr = ''
                    if (noHandle !== 1) {
                        handleStr = ` <td>
                                        <span class="link-blue" onclick="editPointInfoBtn($(this), 'update', 'mt')">修改</span>
                                        <span class="link-red" onclick="removePointSupplier($(this),'mt')">移除</span>
                                        <span class="hd">${JSON.stringify(item)}</span>
                                    </td>`
                    }
                    str += `<tr>
                                <td>${item.name}</td>
                                <td>${item.contract_sn || '无'}</td>
                                <td>${item.is_tax === '1'?item.unit_price : item.unit_price_notax}</td>
                                <td>${mtInvoiceAble}</td>
                                <td>${item.create_name + ' ' + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                                <td>${item.perchase_cycle}</td>
                                <td>${item.current_stock || 0}</td>
                                <td>${item.minimum_stock || 0}</td>
                                <td>${item.initial_stock || 0}</td>
                                <td>${item.location_number || 0}</td>
                                ${handleStr}
                            </tr>`
                }
            }
            if (noHandle !== 1) {
                $("."+ selector +" thead tr .handle").show();
            } else {
                $("."+ selector +" thead tr .handle").hide();
            }

            $("."+ selector +" tbody").html(str);
        }
    })
}

// creater: hxz 2020-04-21 获取供应商下的物料列表
function getMts(supplierId) {
    var data = {
        supplier: supplierId
    }
    $.ajax({
        url: '../mt/list',
        data: data,
        success:function (res) {
            var is_appointed = res['is_appointed']; // 9 表示无需定点采购
            var list = res['data'] || [];
            var str = "";
            var categories = res.categories

            $("#editRAAMt").data('categories', categories)
            if(list && list.length>0){
                for(let item of list){
                    let mtInvoiceAble = "不确定";
                    let material_invoiceable =  Number(item['material_invoiceable'] );
                    if(material_invoiceable === 0){
                        mtInvoiceAble = "否";
                    }else if(material_invoiceable === 1){
                        mtInvoiceAble = "是";
                    }else if(material_invoiceable === 2){
                        mtInvoiceAble = "不确定";
                    }
                    item.supplier_id = supplierId // 这两个字段修改的时候用（修改供应关系跟物料选供应商一个页面）
                    item.material_supplier_id = item.supplier_material_id
                    str += `<tr>
                                <td>${item.name}</td>
                                <td>${item.contract_sn || '无'}</td>
                                <td>${item.is_tax === '1'?item.unit_price : item.unit_price_notax}</td>
                                <td>${mtInvoiceAble}</td>
                                <td>${item.create_name + ' ' + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                                <td>${item.perchase_cycle}</td>
                                <td>${item.current_stock || 0}</td>
                                <td>${item.minimum_stock || 0}</td>
                                <td>${item.initial_stock || 0}</td>
                                <td>${item.location_number || 0}</td>
                                <td>
                                    <span class="link-blue" onclick="editPointInfoBtn($(this), 'update', 'supplier')">修改</span>
                                    <span class="link-red" onclick="removePointSupplier($(this),'supplier')">移除</span>
                                    <span class="hd">${JSON.stringify(item)}</span>
                                </td>
                            </tr>`
                }
            }
            $("[page='determinePointInfo_editMt'] .tbl_supplierHandle").find("tbody").html(str);
        }
    })
}

// creator: 张旭博，2023-08-17 04:42:11， 查看定点供应商信息
function editPointSupplier(selector) {
    var mtInfo = $(".tbl_mtHandle").data('mtinfo')
    var supplierInfo = JSON.parse(selector.siblings(".hd").html())
    var supInfo = JSON.parse(selector.siblings(".hd").html());
    $("#cgaddcontact3").data("box",supInfo);
    let messge = selector.siblings(".hd").html();
    $("#pointChange").data("mess",messge);    //用于获取供应商id
    fixedScanInfo(mtInfo.id, supplierInfo.supplier_id, supplierInfo.id, supInfo);
    bounce.show($("#pointChange"));
    $("#pointChange input:radio").prop("checked", false)
    $("#pointChange .hidePart").hide()
}

function removePointSupplier(selector, pointType) {
    var mtInfo = $(".tbl_mtHandle").data('mtinfo')
    var supplierInfo = JSON.parse(selector.siblings(".hd").html())
    bounce.show($("#bounce_tip"))
    let data = {
        operation: 4,
        enabled: 0,
        init: 1
    }
    if (pointType === 'mt') {
        $("#bounce_tip .tipMsg").html("确定移除该供应商吗？")
        data.product_ = supplierInfo.material_supplier_id
        data.id = mtInfo.id // 供应关系id
    } else {
        $("#bounce_tip .tipMsg").html("确定移除该材料吗？")
        data.product_ = supplierInfo.supplier_material_id
        data.id =  supplierInfo.id// 供应关系id
    }

    $("#bounce_tip .sureBtn").unbind().on("click", function () {
        $.ajax({
            url: '../mt/operation',
            data: data
        }).then(res => {
            let code = res.code
            if (code === 1) {
                layer.msg("操作成功！")
                bounce.cancel()
                // 刷新页面
                reloadPage()
            } else {
                layer.msg("操作失败！")
            }
        })
    })
}


// creator ：侯杏哲 2020-04-26  空赋值
function setVal(val) {
    return val?val:""
}


function getSupList(obj){
    var superList = [];
    $.ajax({
        url:"../material/retrievalSupplierByName.do",
        data:{
            "name" : "" ,
            "enabled": 1
        },
        success:function (data) {
            var list = data["supplierList"];
            $("#addMtSup").data("choone",list);//整个数据盒子
            setSupplier(list, obj);
        }
    }) ;
}

// creator : hxz 2019-12-23 供应商刷新
function setSupplier(list , obj, selectedSuplier){
    var name = "" ;
    if(selectedSuplier){
        name = selectedSuplier["fullName"]
    }
    var str = "<option value='' class='supItem'>-----请选择-----</option>" ;
    if(list && list.length > 0){
        for(var i=0 ; i < list.length ; i++){
            var info = list[i];
            if(name === info["fullName"]){
                str += "<option selected value='"+ JSON.stringify(info) +"' class='supItem'>"+ info["fullName"] +"</option>" ;
            }else{
                str += "<option value='"+ JSON.stringify(info) +"' class='supItem'>"+ info["fullName"] +"</option>" ;
            }
        }
    }
    obj.html(str);
}

// creator: 张旭博，2023-08-22 02:14:50， 匹配供应商
function matchSupper(selector){
    let supplierInfo = selector.val()

    if(supplierInfo === ''){
        $(".part_relation").hide()
        $(".part_addPointMt .supplierDes").hide()
        return false;
    }
    supplierInfo = JSON.parse(supplierInfo)
    console.log(supplierInfo);
    $("body").data('supplierId', supplierInfo.id)

    let mode = $("body").data('whmode') // 1智能库 0非智能库
    let mtInfo = $(".tbl_mtHandle").data('mtinfo')

    $(".part_relation").show()
    $(".part_relation input:radio").prop("checked", false)
    $(".part_relation .hidePart").hide()
    if (mode == 1 && mtInfo.exp_required == 1 && mtInfo.related_item ==2 && mtInfo.same_expiration == 0) {
        $(".part_exp").show()
    } else {
        $(".part_exp").hide()
    }
    $(".part_addPointMt .supplierDes").show()
    $(".part_relation input:not(:radio)").val('')
    $(".part_addPointMt [name='name']").val(supplierInfo.name);
    $(".part_addPointMt [name='codeName']").val(supplierInfo.codeName);
    let des = supplierInfo.ys
    let supplierDes = ` <span class="ty-color-green">${des.kp ? des.kp + '，' : ''}</span>
                        <span class="ty-color-orange">${des.gz ? des.gz + '，' : ''}</span>
                        <span class="ty-color-blue">${des.hp}</span>`
    $(".supplierDes").html(supplierDes);
    getSupplierTaxRate($("#editPointInfo .taxRateList"));
}

// creator: 张旭博，2023-08-22 02:14:50， 匹配材料
function matchMt(selector){
    let mtInfo = selector.val()
    if(mtInfo === ''){
        $(".part_relation").hide()
        return false;
    } else {
        mtInfo = JSON.parse(mtInfo)
        $(".part_relation").show()
        $(".part_relation input:radio").prop("checked", false)
        $(".part_relation .hidePart").hide()
        $(".part_relation input:not(:radio)").val('')
        $("#editPointInfo .purUnit").html(mtInfo['unit']); // 赋值单位
        $(".part_addPointSupplier [name='mtcode']").val(mtInfo.code)
    }
}

// creator:hxz 2019-12-26 总结供应商信息
function setSupInfo(res, obj) {
    var str2 = '';
    str2 += `
        <span class='supGreen'>${res.kp ? res.kp + '，' : ''}</span>
        <span class='supOrange'>${res.gz ? res.gz + '，' : ''}</span>
        <span class='supBlue'>${res.hp ? res.hp + '。' : ''} </span>
    `;
    obj.html(str2);
}

function chargeShowInvoice() {
    var isStable = $("#isStable").val();
    var supplier = $("#supplier").val() ;
    if(supplier){
        supplier = JSON.parse(supplier) ;
        var supplierMaterial = supplier['supplierMaterial'];
        for(var key in supplierMaterial){
            supplier[key] = supplierMaterial[key]
        }
        var canInvoice = $("#canInvoice").val() ;
        var incoiceTypeVal = $("#incoiceType").val() ;//如何让它变成1呢
        if(ontractsigned === "1"){//是
            $(".contrInfo").show();
            $("#contract").removeAttr("disabled");
        }
        else if(ontractsigned ==="0"){//选中否
            $("#contract").attr("disabled","disabled");
            $(".contrInfo").hide();
        }
        if(isStable === "1"){ //稳定
            $(".priceInfo").show();
            $(".incoiceType4").hide();
            $(".canInvoice2").hide();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show();
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".priceInfo .type3").html("已约定的单价"); $("#isParValue").val(1);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $("#isParValue").val(1);
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("已约定的<span class='supOrange'>开票</span>单价");
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("已约定的<span class='supOrange'>不开票</span>单价");
                $("#isParValue").val(0);
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
                $(".incoiceType").hide();$("#isParValue").val("");
            }
        }else if(isStable === "2"){ // 变动频繁
            $(".priceInfo").show();
            $(".canInvoice2").show();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show()
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
                $(".incoiceType4").hide();
                $(".type1").hide();

                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".type3").html("参考单价");
                    $("#isParValue").val(0);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("参考单价(<span class='supOrange'>开票价</span>)");
                    $("#isParValue").val(0);
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
                $("#isParValue").val(0);
            }else if(canInvoice === "2"){ // 不确定
                //if(supplier["invoiceCategory"] === "2"){ // 供应商只能开普通票
                $(".priceInfo .type1").html("参考单价");
                $("#isParValue").val("");
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
            }
        }else {
            $(".incoiceType").hide();
        }
    }else{
        // 初始化
        $(".stable").hide();
    }
}

// creator:lyt 2022-12-16 获取列表
function getSupplierTaxRate(obj){
    let supplierId = $("body").data('supplierId')
    obj.html("");
    $.ajax({
        url: '../supplier/getSupplierInvoices.do',
        data: { id: supplierId },
        success:function (res) {
            let list = res.data || [];
            let option = '';
            if (list.length > 0) {
                list.forEach(function (item) {
                    option += ` <span class="funBtn" data-val="${item.id}" data-fun="setTaxRateVal"><span>${item.taxRate}%</span><span onclick="delTaxRate($(this), event)">删除</span></span>`
                })
            } else {
                option = ''
            }

            obj.html(option);
            bounce_Fixed.cancel();
        }
    });
}

//  creator ：侯杏哲 2019/12/4 采购合同中包含本材料吗
function containThis(type , thisObj) {
    setRadioSelect("containThis", [1,0], type, thisObj);

}

// ----- 添加材料的定点供应上弹窗用到的方法

// creator: sy 2022-07-15 筛选框渲染
function choseothen(res,type){
    var bonk = res.data;
    var code = "";
    var str1 ="<option value='' class='supItem'></option>";
    if(bonk && bonk.length>0){
        for(var i=0;i<bonk.length;i++){
            var bont =bonk[i];
            if(code === bont.sn){
                str1 +="<option selected value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
            }else{
                str1 +="<option value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
            }
        }
    }
    // var str =``;
    // for(let i in bonk){
    //     str =`
    //         <option value=""></option>
    //         <option value="${JSON.stringify(bonk[i])}">${bonk[i].creator}(合同编号)</option>
    //     `;
    // }
    $("#contract").html(str1);
}

// creator: sy 2022-08-24 （查看定点）筛选框渲染
function choseothen1(res,type){
    var bonk = res.data;
    var code = "";
    var str1 ="<option value='' class='supItem'></option>";
    if(bonk && bonk.length>0){
        for(var i=0;i<bonk.length;i++){
            var bont =bonk[i];
            if(code === bont.sn){
                str1 +="<option selected value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
            }else{
                str1 +="<option value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
            }
        }
    }
    $("#contract1").html(str1);
}

// creator: sy 2022-08-23  查看定点信息中新增合同
function cgaddctact3(num){
    $("#newcontract").data("type","new");
    let type = $("#newcontract").data("type");
    if(num === 0) {//删除新增合同弹窗中上传的文件
        let fileImArr = $("#newcontract #fileCon1-1 .fileIm");
        if (fileImArr.length > 0) {
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid', groupUuid: groupUuid}, true);
        }
        let file2 = $("#newcontract #fileCon2-1 .fileIm");
        if (file2.length > 0) {
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid', groupUuid: groupUuid}, true);
        }
        bounce.cancel();
        bounce.show($('#pointChange'));
    }else{//点击确定按钮
        let infofi = {};
        infofi.cNo = $("#newcontract .cNo").val();
        if(infofi.cNo.length === 0){
            layer.msg("请录入合同编号");
            return false;
        }
        infofi.cSignDatecon = $("#newcontract .cSignDate").val();
        infofi.cStartDatecon = $("#newcontract .cStartDate").val();
        infofi.cEndDatecon = $("#newcontract .cEndDate").val();
        infofi.cMemo = $("#newcontract .cMemo").val();
        infofi.fileCon1 = [];
        $("#newcontract #fileCon1-1 .fileIm").each(function(){
            let itemf = $(this).find(".hd").html();
            infofi.fileCon1.push(JSON.parse(itemf));
        });
        infofi.fileCon2 = [];
        $("#newcontract #fileCon2-1 .fileIm").each(function(){
            let itemf = $(this).find(".hd").html();
            infofi.fileCon2.push(JSON.parse(itemf));
        });
        console.log(infofi);
        let url = "";
        var utbo = $("#cgaddcontact3").data("box") ;
        var utid = utbo.supplier_id; //供应商id
        let filePath = '';
        if(infofi.fileCon2.length >0){
            filePath = infofi.fileCon2[0].filename;
        };
        let imgs= [];
        if(infofi.fileCon1 && infofi.fileCon1.length>0){
            infofi.fileCon1.forEach(function(im,index){
                imgs.push({
                    "filePath":im.filename,
                    "order":index,
                    "type":"1",
                    "title":im.originalFilename
                })
            })
        }
        let contractBase = {
            "id":utid,
            "sn":infofi.cNo,
            "signTime":infofi.cSignDatecon,
            "validStart":infofi.cStartDatecon,
            "validEnd":infofi.cEndDatecon,
            "memo":infofi.cMemo,
            "filePath":filePath,
            "fileName":filePath,
            "contractBaseImages":imgs,
            "mtList":[]
        }
        var jhf = {
            'sn':contractBase.sn,
            'signTime':contractBase.signTime,
            'validStart':contractBase.validStart,
            'validEnd':contractBase.validEnd,
            'filePath':contractBase.filePath,
            'fileName':contractBase.fileName,
            'memo':contractBase.memo,
            'contractBaseImages':contractBase.contractBaseImages,
            'mtList':contractBase.mtList
        };
        jhf = JSON.stringify(jhf);
        if(type =="new"){
            url="../supplier/addContractBase.do";
            $.ajax({
                url:url,
                data:{
                    supplierId:utid,
                    contractBase:jhf
                },
                success:function(res){
                    let status = res.success;
                    if(status === "1"|| status === 1) {
                        layer.msg("新增成功");
                        bounce.cancel($("#newcontract"));
                        bounce.show($("#pointChange"));
                        var type = 1;
                        urgnistad(type ,editObj);
                    }
                }
            })
        }
    }
}

// creator:sy 2022-07-18 添加本材料的定点供应商弹窗中的新增合同
function cgaddctact(num){
    $("#newcontract").data("type","new");
    let type = $("#newcontract").data("type");
    if(num === 0){//删除新增合同弹窗中上传的文件
        let fileImArr = $("#newcontract #fileCon1-1 .fileIm");
        if(fileImArr.length>0){
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type:'groupUuid',groupUuid:groupUuid},true);
        }
        let file2 = $("#newcontract #fileCon2-1 .fileIm");
        if(file2.length>0){
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type:'groupUuid',groupUuid:groupUuid},true);
        }
        bounce.cancel();
        bounce.show($('#addMtSup'));
    }else{//点击确定按钮
        let infofi = {};
        infofi.cNo = $("#newcontract .cNo").val();
        if(infofi.cNo.length === 0){
            layer.msg("请录入合同编号");
            return false;
        }
        infofi.cSignDatecon = $("#newcontract .cSignDate").val();
        infofi.cStartDatecon = $("#newcontract .cStartDate").val();
        infofi.cEndDatecon = $("#newcontract .cEndDate").val();
        infofi.cMemo = $("#newcontract .cMemo").val();
        infofi.fileCon1 = [];
        $("#newcontract #fileCon1-1 .fileIm").each(function(){
            let itemf = $(this).find(".hd").html();
            infofi.fileCon1.push(JSON.parse(itemf));
        });
        infofi.fileCon2 = [];
        var box = $("#newcontract .fileCon2 .fileIm").html();
        if (box == undefined || box == null) {
            infofi.fileCon2 = [];
        }else{
            $("#newcontract #fileCon2-1 .fileIm").each(function(){
                let itemf = $(this).find(".hd").html();
                infofi.fileCon2.push(JSON.parse(itemf));
            });
        }
        let url = "";
        var utid = $("#newcontract").data("id");    //供应商id
        let filePath = '';
        if(infofi.fileCon2.length >0){
            filePath = infofi.fileCon2[0].filename;
        };
        let imgs= [];
        if(infofi.fileCon1 && infofi.fileCon1.length>0){
            infofi.fileCon1.forEach(function(im,index){
                imgs.push({
                    "filePath":im.filename,
                    "order":index,
                    "type":"1",
                    "title":im.originalFilename
                })
            })
        }
        let contractBase = {
            "id":utid,
            "sn":infofi.cNo,
            "signTime":infofi.cSignDatecon,
            "validStart":infofi.cStartDatecon,
            "validEnd":infofi.cEndDatecon,
            "memo":infofi.cMemo,
            "filePath":filePath,
            "fileName":filePath,
            "contractBaseImages":imgs,
            "mtList":[]
        }
        var jhf = {
            'sn':contractBase.sn,
            'signTime':contractBase.signTime,
            'validStart':contractBase.validStart,
            'validEnd':contractBase.validEnd,
            'filePath':contractBase.filePath,
            'fileName':contractBase.fileName,
            'memo':contractBase.memo,
            'contractBaseImages':contractBase.contractBaseImages,
            'mtList':contractBase.mtList
        };
        jhf = JSON.stringify(jhf);
        if(type =="new"){
            url="../supplier/addContractBase.do";
            $.ajax({
                url:url,
                data:{
                    supplierId:utid,
                    contractBase:jhf
                },
                success:function(res){
                    let status = res.success;
                    if(status === "1"|| status === 1) {
                        layer.msg("新增成功");
                        bounce.cancel($("#newcontract"));
                        bounce.show($("#addMtSup"));
                        var type = 1;
                        ontractsigned(type ,editObj);
                    }
                }
            })
        }
    }
}

// creator: sy 2022-07-18 关闭“本合同下的材料”弹窗
function closedown(){
    bounce.cancel($("#contactbox"));
    $("#newcontract").css("top","40px");
    bounce.show($("#newcontract"));
}

// creator: sy 2022-08-24 返回到合同页面
function backcontant(){
    $("#newcontract").css("top","40px");
    bounce.show($('#newcontract'));
}

// creator:sy 2022-08-30 设置输入框数字
function clearNoNum1(input){
    if(input.value != ""){
        input.value=input.value.replace(/[^\d]/g,'');   //只能输入整数
        input.value=input.value.substring(0,8);     //截取输入数字的前八位，使只能输入八位数字
    }
}

// creatory:sy 2022-11-11 设置输入框数字2
function clearNoNum2(obj){
    obj.value = obj.value.replace(/[^\d.]/g,"");//清除“数字”和“.”以外的字符
    obj.value=obj.value.replace(/\.{2,}/g,".");//只保留第一个.清除多余的
    obj.value=obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    obj.value=obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
    if(obj.value.indexOf(".")<0&&obj.value!=""){   //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于01、02
        obj.value=parseFloat(obj.value);
    }
}

// creator: 张旭博，2023-08-25 10:54:11， 新增税率
function addTaxRate(obj) {
    $("#taxRateVal").val("");
    $("#addTaxRate").data("obj", obj)
    bounce_Fixed.show($("#addTaxRate"));
}

// creator:lyt 2022-12-13 新增税率确定
function addTaxRatetOk(){
    let val = $("#taxRateVal").val();
    if (!val) {
        layer.msg("请录入税率！")
        return false
    }
    let supplierId = $("body").data('supplierId')
    $.ajax({
        url: '../supplier/addSupplierInvoice.do',
        data: { supplier: supplierId, invoiceCategory: 1, taxRate: val },
        success:function (res) {
            if (res.success === 1) {
                let data = res.data;
                let obj = $("#addTaxRate").data("obj")
                let option = ` <span class="funBtn" data-val="${data.id}" data-fun="setTaxRateVal"><span>${val}%</span><span onclick="delTaxRate($(this), event)">删除</span></span>`
                obj.siblings(".taxRateBody").find(".taxRateList").append(option);
                bounce_Fixed.cancel();
            }
        }
    });
}

function delTaxRate(obj, et){
    et.stopPropagation();
    let val = obj.parent().data("val");
    $.ajax({
        "url":"../supplier/deleteSupplierInvoice.do",
        "data":{ 'id': val },
        success:function (res) {
            let state = res.success;
            if (state === 1) {
                obj.parent().remove();
                layer.msg("删除成功");
            } else {
                layer.msg("不能删除已被使用的税率！");
            }
        }
    });
}
function taxRateFun(obj){
    obj.siblings(".taxRateList").toggle();
}
function setTaxRateVal(obj){
    let id = obj.data("val")
    let val = obj.find("span").eq(0).html();
    obj.parents(".taxRateList").toggle();
    obj.parents(".taxRateList").siblings("input[type='hidden']").val(id);
    obj.parents(".taxRateList").siblings("input[type='text']").val(val);
}
// creator:lyt 2022-12-13 管理交货地点
function manageDeliveryLocations(type){
    if (type === 1) {
        $("#chooseDelivery .btnName").html("新增收货地址");
    } else if (type === 2){
        $("#chooseDelivery .btnName").html("新增到货区域");
    }
    $("#chooseDelivery .care"+ type).show().siblings().hide();
    bounce_Fixed.show($("#chooseDelivery"));
    $("#chooseDelivery").data('type', type)
    getDeliveryList(type)

}

// creator: 张旭博，2023-08-21 03:32:12， 新增地址
function newAddress() {
    $("#editReceiveInfo").data("type", "new")
    bounce_Fixed2.show($("#editReceiveInfo"))
    $("#editReceiveInfo input").val('');
    $("#ReceiveName").val("").data('orgData',"").siblings(".hd").html("") ;
}

// creator: 张旭博，2023-08-22 04:40:16， 获取地址列表
function getDeliveryList(type) {
    $.ajax({
        url: '../dac/list',
        data: {
            type: type,
            enable: 1 // 0-停用状态 1-启用状态
        },
    }).then(res => {
        let list = res.data.data || [], str = ''
        list.forEach((item)=>{
            str +=`<tr>
                      <td>
                          <div class="ty-checkbox">
                               <input type="checkbox" id="address_${item.id}" value="${item.id}">
                               <label for="address_${item.id}"></label>
                          </div>
                          <span>${item.address}</span>
                          <span class="hd">${JSON.stringify(item)}</span>
                      </td>
                  </tr>`;
        })
        $(".tbl_chooseDelivery_address tbody").html(str);
    })
}
// creator: lyt 2022-12-15 管理交货地点勾选
function chooseDeliveryOk(){
    let type = $("#chooseDelivery").data("type")
    let addressInfoArr = []
    let addressStr = ''
    $("#chooseDelivery input:checkbox:checked").each(function (){
        let addressInfo = $(this).parents('tr').find(".hd").html()
        addressInfo = JSON.parse(addressInfo)
        addressInfoArr.push(addressInfo)
    })
    if (addressInfoArr.length === 0) {
        layer.msg('请至少选择一项！')
        return false
    }
    let addressArr = addressInfoArr.map(item => item.address)
    let addressIdsArr = addressInfoArr.map(item => item.id)
    $(".manageAddressList_" + type).html(addressArr.join('，')).siblings(".hd").html(addressIdsArr.join(','));
    bounce_Fixed.cancel()
}
/*creator:lyt 2022/11/14 0014 下午 4:46 收货信息*/
function addAddress(){
    $("#newReceiveInfo").data("type", "new")
    bounce_Fixed2.show($("#newReceiveInfo"))
    $("#newReceiveInfo input").val("");
    $("#ReceiveName")
        .val("").data('orgData',"")
        .siblings(".hd").html("") ;
}
/*creator:lyt 2022/11/14 0014 下午 4:54 收货信息确定*/
function addressAddSure(obj){
    let funType = $("#newReceiveInfo").data("type")
    let source = obj.data("source")
    let abled = true, url = '../dac/add';
    let curObj = $("#newReceiveInfo");
    let contactInfo = JSON.parse($("#ReceiveName").siblings(".hd").html());
    if (source === 2) {
        curObj = $("#newReceiveAreaInfo");
        contactInfo = JSON.parse($("#receiveAreaName").siblings(".hd").html());
    }
    curObj.find("input[require]").each(function (){
        let val = $(this).val();
        if (val === "") abled = false;
    })
    if (!abled) {
        layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
        return false;
    }
    var json = {
        'type': source === 1 ? 1: 2,
        'contact': contactInfo.contact ,
        'telephone':contactInfo.telephone
    }
    if (funType === 'update')  {
        url = '../dac/edit';
        json.id = curObj.data("id")
    }
    if (source === 1) {
        json.address = $("#ReceiveAddress").val()
    } else {
        json.address = $("#receiveArea").val()
        json.regionCode = $("#receiveAreaName").val()
        json.requirements = $("#receiveNotice").val()
    }
    $.ajax({
        url: url,
        data: json,
        beforeSend:function(){ loading.open() ; },
        success: function (data) {
            var status = data.success;
            if (status === 200) {
                bounce_Fixed2.cancel()
                getReceiveAddressList(source, 1);
            } else {
                layer.msg("新增失败！");
            }
        }
    })
}

// creator: sy 2023-01-12 鼠标获得焦点
function outover(obn){
    $("#uncertainty1").attr("class","fa fa fa-circle-o");
}
// creator: hxz 2020-12-10 新增联系人
function addContactInfo(num) {
    var type = "", source = "";
    if(num === 2){
        type = 'new';
        source = 'addAddress';
    }else if(num === 4){
        type = 'new';
        source = 'addArea';
    }
    //var customerId = $("#updateCustormPanel").data("id");
    //$("#newContectInfo").data('level',2);
    $("#newContectInfo").data('type',type);
    $("#newContectInfo").data('source', source);
    document.getElementById('newContectData').reset();
    $(".otherContact").html("");
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    if($(".contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
    }
    //$("#newContectInfo").data("id", customerId);
    $("#addMoreContact").hide();
    $("#uploadCard").html("")
    initUploadCard($("#uploadCard"));
    bounce_Fixed3.show($("#newContectInfo"));
    setTimer('updateContact');
}
// creator: lyt 2022-11-29 收货人那里的新增收货人
// function chooseCusContactOk() {
//     let selectObj = $("#chooseCusContact").find(".fa-dot-circle-o");
//     if(selectObj.length > 0){
//         let strInfo = selectObj.siblings("span.hd").html();
//         let info = JSON.parse(strInfo);
//         $($("#target").val())
//             .val(selectObj.next().html()).data('orgData',selectObj.next().html())
//             .siblings(".hd").html(strInfo) ;
//         bounce_Fixed3.cancel();
//     }else layer.msg('请先选择人员')
// }
// creator: hxz 2020-12-10 新增联系人确定
function addContactOk(){
    var arr = []
    var data = {
        'name': $("#contactName").val(),
        'post': $("#position").val(),
        'telephone': $("#contactNumber").val(),
        'cardPath': ''
    };
    if($("#contactsCard .bussnessCard").length > 0){
        data.cardPath = $("#contactsCard .bussnessCard .filePic").data('path');
    }
    if($(".otherContact li").length > 0){
        $(".otherContact li").each(function () {
            if ($(this).find('input').val() != '') {
                var json = {
                    'code': $(this).find("input").val(),
                    'type': $(this).find("input").data('type'),
                    'name': $(this).find("input").data('name')
                };
                arr.push(json);
            }
        })
    }
    let param = {
        "deliveryContact": data,
        "contactSocials": arr
    }
    $.ajax({
        url: '../dac/contact/add',
        "method": "POST",
        "timeout": 100000,
        "headers": {
            "Content-Type": "application/json"
        },
        data: JSON.stringify(param),
        success: function (res) {
            data.id = res.data.deliveryContact.id;
            var status = res.success;
            if (status === '200' || status === 200) {
                layer.msg('新增成功')
                var groupUuidArr = []
                $("#newContectInfo [groupUuid]").each(function () {
                    groupUuidArr.push({
                        type: 'groupUuid',
                        groupUuid: $(this).attr("groupUuid")
                    })
                })
                cancelFileDel(groupUuidArr)
                // 给当前的赋值
                data.contact = data.name
                if ($("#newReceiveInfo").is(":visible")) {
                    $("#ReceiveName")
                        .val(data.name).data('orgData', data.name)
                        .siblings(".hd").html(JSON.stringify(data));
                } else  if ($("#newReceiveAreaInfo").is(":visible")){
                    $("#areaName")
                        .val(data.name).data('orgData',data.name)
                        .siblings(".hd").html(JSON.stringify(data)) ;
                }
                bounce_Fixed3.cancel();
            } else {
                layer.msg("新增失败！");
            }
        }
    })
}

// creator: hxz 2020-12-10 获取联系人列表
function getCusContactList() {
    $.ajax({
        "url":"../dac/contact/list",
        success:function (res) {
            let list = res['data'] || [] ;
            setCusListStr(list, 'updateCustomer')
        }
    });
}

// creator: lyt 2022-11-30 联系人查看
function seeContactDetail(obj){
    $.ajax({
        url : "../dac/contact/detail" ,
        data : {
            'id': obj.data('id')
        },
        success:function(data){
            var info = data['data']['contact'];
            var socialList = data['data']['socials'] || [];
            let allStr = ``;
            $("#see_contactName").html(info.name);
            $("#see_position").html(info.post);
            if(socialList.length > 0){
                let sortList = [];
                for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
                for(var r in socialList){
                    let item = socialList[r];
                    let _index = Number(item.type);
                    sortList[_index].push(item);
                }
                let sortAfter = [];
                for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
                for(var t in sortAfter){
                    let item = sortAfter[t];
                    if(t%2===0){
                        allStr += `<tr><td>${item.name}</td><td>${item.code}</td>`
                    }else{
                        allStr += `<td>${item.name}</td><td>${item.code}</td></tr>`
                    }
                }
                if(sortAfter.length % 2 !== 0){
                    allStr += `<td> </td><td> </td></tr>`
                }
            }
            $(".see_otherContact").html(allStr);
            bounce_Fixed4.show($("#contactSeeDetail"));
        }
    });
}
// creator: 李玉婷，2019-09-16 16:26:52，定时器--新增验证
function setTimer (timer) {
    switch (timer) {
        case 'updateContact':
            bounce_Fixed3.everyTime('0.5s','updateContact',function () {
                let state = 0
                $("#editContact [require]:visible").each(function(){
                    if ($(this).val() === '') state++
                })
                $("#addContact").prop("disabled", state > 0);
            });
            break;
    }
}

// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    var parent4 = selector.parents(".bounce_Fixed4")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else if (parent4.length > 0) {
        bounce_Fixed4.cancel()
    } else {
        console.log("未定义的状态")
    }
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = []
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
}

laydate.render({elem: '#e_gCompactExpire',});
laydate.render({elem: '#e_gCompactSignDay',});
laydate.render({elem:'#editContract_signTime',format:'yyyy-MM-dd'});
laydate.render({elem:'#editContract_validStart',format:'yyyy-MM-dd'});
laydate.render({elem:'#editContract_validEnd',format:'yyyy-MM-dd'});

