var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
$(function (){
    //第一级目录加载
    jumpPage("main", function () {
        // 主页为静态资源
        firstLevelLoad(2);
    })

    // 每一级目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容）
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right") ;
            $(this).find("i").eq(0).addClass("fa-angle-down")
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down") ;
            $(this).find("i").eq(0).addClass("fa-angle-right") ;
        }

        //获取子级文件夹内容
        var categoryId = $(this).attr("id") ;
        var treeItemThis = $(this) ;
        $.ajax({
            url: "../post/toPostManagement.do",
            data: {
                "orgId":categoryId,
                "orgType":2,
                "userId": sphdSocket.user.userID
            },
            beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
            success: function (data) {
                //判断数据非空
                if(data === "" || data === undefined){
                    bounce.show( $("#tip") ) ; $("#tipMess").html( "系统错误，请重试！" ) ;
                    //正文
                }else{
                    var listNotice = data["data"]["organization"];          // 本级部门信息
                    var listNoticeChild = data["data"]["organizations"];    // 子级部门信息

                    //拼接子部门侧边菜单字符串
                    var level = parseInt(treeItemThis.parent().parent().attr("level")); //获取当前级别
                    var nextLevel = level + 1;                                          //下级级别

                    //拼接子级字符串
                    var levelStr = "";
                    //设置选中文件夹详细内容的头部名称
                    $(".nowFolder").children("h3").html(listNotice.name);
                    $(".ty-treeItemActive>span").html(listNotice.name);
                    //   updater，姚宗涛  2018/01/25  15:32:08  拼接左侧导航
                    levelStr += '<div style="margin-left:20px;" class="level'+nextLevel+'" level="'+nextLevel+'">';
                    for(var i in listNoticeChild){
                        // 判断子级有无子部门（1 - 有，其他值 - 没有）
                        if(listNoticeChild[i]["childNum"] > 0){
                            levelStr +=     '<li>' +
                                '<div class="ty-treeItem" id='+listNoticeChild[i].id+'>' +
                                '<i class="fa fa-angle-right"></i>' +
                                '<i class="fa fa-folder"></i>' +
                                '<span>'+listNoticeChild[i].name+'</span>' +
                                '</div>' +
                                '</li>'
                        }else{
                            levelStr +=     '<li>' +
                                '<div class="ty-treeItem" id='+listNoticeChild[i].id+'>' +
                                '<i class="ty-fa"></i>' +
                                '<i class="fa fa-folder"></i>' +
                                '<span>'+listNoticeChild[i].name+'</span>' +
                                '</div>' +
                                '</li>'
                        }
                    }
                    levelStr += '</div>';

                    //赋值
                    if(treeItemThis.children("i:first").hasClass("fa-angle-down")){
                        treeItemThis.next().remove();
                        treeItemThis.after(levelStr);
                    }else{
                        treeItemThis.next().remove();
                    }
                    //拼接本级部门信息列表字符串
                    var createDate  =   handleNull(listNotice["createName"]) + '&nbsp;&nbsp;' +new Date(listNotice["createDate"]).format('yyyy-MM-dd hh:mm:ss');
                    var flag  =   "";
                    if(listNotice["createName"] == '系统'){
                        flag = 'sys';
                    } else {
                        flag = 'nosys';
                    }

                    var otherStr = '';
                    if(listNoticeChild.length<1&&level<8){
                        otherStr =
                            '   <span class="link-blue" type="btn" data-name="newClass">新增子级部门</span>' +
                            '   <span class="link-blue" type="btn" data-name="suspend" data-type="0">停用</span>' +
                            '   <span class="link-red"  type="btn" data-name="delete">删除</span>';
                        $(".childFolder").parent().show();
                    }else if(level>7){
                        otherStr =  '   <span class="link-blue" type="btn"  data-name="suspend" data-type="0">停用</span>' +
                                    '   <span class="link-red"  type="btn"  data-name="delete">删除</span>';
                        $(".childFolder").parent().hide();
                    }else{
                        otherStr =
                            '   <span class="link-blue" type="btn" data-name="newClass">新增子级部门</span>' +
                            '   <span class="link-blue" type="btn" data-name="suspend" data-type="0">停用</span>'
                        $(".childFolder").parent().show();
                    }
                    var listNoticeStr =
                        '<tr data-flag="'+ flag +'">' +
                        '   <td>'+createDate+'</td>'+
                        '   <td><span class="'+(listNotice.allUserNum> 0?'link-blue':'link-gray')+'" type="btn" data-name="numSee" data-type="1">'+ listNotice.allUserNum +'</span></td>'+
                        '   <td>' +
                        '      <span class="link-blue" type="btn" data-name="changeWrong">改错字</span>' +
                        '      <span class="link-blue" type="btn" data-name="changeClass">部门改名</span>' +
                        '      <span class="link-blue" type="btn" data-name="record">修改记录</span>' +
                        '   </td>'+
                        '   <td>' + otherStr +'</td>'+
                        '</tr>';

                    //此部门信息列表字符串插入表格，此部门id插入表格
                    $(".CategoryMessage").find("tbody").html("").append(listNoticeStr);
                    $(".CategoryMessage").attr("id",categoryId);

                    //拼接子级部门列表字符串
                    if(listNoticeChild.length<1){
                        $(".childPanel").hide();
                    }else{
                        $(".childPanel").show();
                    }
                    var listNoticeChildStr="";
                    for(var j in listNoticeChild){
                        var cName            = listNoticeChild[j]["name"];
                        var cCreatDate       = listNoticeChild[j]["createName"] + '&nbsp;&nbsp' + new Date(listNoticeChild[j]["createDate"]).format('yyyy-MM-dd hh:mm:ss');
                        listNoticeChildStr +='<tr data-id="'+ listNoticeChild[j]["id"] +'"><td>'+cName+'</td>'+
                            '<td><span class="'+(listNoticeChild[j].allUserNum> 0?'link-blue':'link-gray')+'" type="btn" data-name="numSee" data-type="2">'+ listNoticeChild[j].allUserNum +'</span></td>'+
                            '<td>'+cCreatDate+'</td>'+
                            '</tr>';
                    }

                    //子类别信息列表字符串插入表格
                    $(".cCategoryMessage").find("tbody").html(listNoticeChildStr) ;
                }
            },
            complete:function(){ chargeClose( 2 ) ;  }
        })

    });

    $(".ty-secondTab li").click(function () {
        var index = $(this).index();
        $(this).addClass("ty-active").siblings().removeClass("ty-active");
        $(".ty-mainData .tplContainer").eq(index).show().siblings(".tplContainer").hide();
        if (index === 1) {
            getPostList()
        } else {
            firstLevelLoad(2)
        }
    }) ;
    $(".departMain").on("click","[type='btn']",function (){
        var name = $(this).data("name");
        var subThis = $(this);
        switch (name) {
            case 'newClass':
                //清空输入框
                $(".bounce-newClass .categoryName").val("");
                bounce.show($('.bounce-newClass'));
                break;
            case 'numSee':
                jumpPage("roleList", function (){
                    var type = subThis.data("type");
                    var categoryId = '';
                    var categoryName = ''
                    if (type == '1') {
                        categoryId = $.trim($(".CategoryMessage").attr("id"));
                        categoryName = $(".nowFolder").children("h3").html()
                    } else {
                        categoryId = subThis.parents("tr").data("id");
                        categoryName = subThis.parent().prev().html()
                    }
                    userNumSee(categoryId, categoryName);
                })

                break;
            case 'changeWrong':
                adjustAdvence(1, subThis);
                break;
            case 'changeClass':
                //清空输入框
                var title = $(".ty-treeItemActive>span").text();
                var flag = subThis.parents("tr").data("flag");
                $(".bounce-changeClass .categoryName").val(title);
                $(".bounce-changeClass .bonceHead>span").html("部门改名");
                $(".bounce-changeClass .categoryName").data("orgname",title);
                if (flag == 'sys') {
                    $(".saleChangeTip").show();
                } else {
                    $(".saleChangeTip").hide();
                }
                $("#changeEffect").show();
                $("#changeEffect input").val("");
                $(".bounce-changeClass .change").addClass("ty-btn-gray").removeClass("ty-btn-blue").attr("onclick","");
                bounce.show($('.bounce-changeClass'));
                break;
            case 'record':
                bounce.show($("#updateRecords"));
                changeRecord();
                break;
            case 'delete':
                var flag = subThis.parents("tr").data("flag");
                if (flag == "sys") {
                    bounce_Fixed.show( $("#knowTip") ) ; $("#knowTip .knowWord").html("对不起，为确保系统功能正常，此部门不可被删除！");
                } else {
                    adjustAdvence(3,"");
                }
                break;
            case 'suspend':
                var state = subThis.data("type");
                var flag = subThis.parents("tr").data("flag");
                $("#stopOrReset").data("state",state);
                if (flag == "sys") {
                    bounce_Fixed.show( $("#knowTip") ) ; $("#knowTip .knowWord").html("对不起，为确保系统功能正常，此部门不可被停用！");
                } else {
                    adjustAdvence(2,"");
                }
                break;
        }
    });

    $(".postMain, .page[page='stopPost']").on("click","[type='btn']",function (){
        var name = $(this).data("name");
        var id = $(this).parents("tr").data("id")
        $(".postMain").data("postId", id)
        switch (name) {
            case 'numSee':
                var postName = $(this).parents("tr").find("td").eq(0).html()
                jumpPage("roleList", function (){
                    var categoryName = $(this).parents("tr").find("td").eq(0).html()
                    $(".page[page='roleList'] .tip").html('');
                    $(".page[page='roleList'] tbody").html('');
                    $.ajax({
                        url: $.webRoot + '/post/getUserListByPostId.do',
                        data: {
                            postId: id
                        }
                    }).then(res => {
                        var usersList = res.data
                        var html = '';
                        for (var t=0;t<usersList.length;t++){
                            html += '<tr>' +
                                '<td>'+ handleNull(usersList[t].userName) +'</td>' +
                                '<td>'+ chargeSex(handleNull(usersList[t].gender)) +'</td>' +
                                '<td>'+ handleNull(usersList[t].mobile) +'</td>' +
                                '<td>'+ handleNull(usersList[t].departName) +'</td>' +
                                '<td>'+ handleNull(usersList[t].postName) +'</td>' +
                                '<td>'+ handleNull(usersList[t].leaderName) +'</td>' +
                                '</tr>';
                        }
                        $(".page[page='roleList'] .tip").html('当前'+postName+'职位共有职工'+usersList.length+'人');
                        $(".page[page='roleList'] tbody").html(html)
                    })
                })
                break;
            // 改错字
            case 'changeWord':
                var postName = $(this).parents("tr").find("td").eq(0).html()
                bounce.show($('#post_changeWord'));
                $('#post_changeWord input').val(postName)
                $("#post_changeWord .autoFill").each(function (){
                    var length = $(this).val().length
                    var max = $(this).attr("max")
                    $(this).siblings(".textMax").html(length + '/' + max)
                })
                break;
            // 岗位改名
            case 'changeName':
                var postName = $(this).parents("tr").find("td").eq(0).html()
                bounce.show($('#post_changeName'));
                $("#post_changeName input").val("")
                $("#post_changeName [name='name']").val(postName)
                $("#post_changeName .autoFill").each(function (){
                    var length = $(this).val().length
                    var max = $(this).attr("max")
                    $(this).siblings(".textMax").html(length + '/' + max)
                })
                break;
            // 查看
            case 'see':
                bounce_Fixed.show($('#post_see'));
                $.ajax({
                    url: $.webRoot + '/post/getPostInfo.do',
                    data: {
                        id: id
                    }
                }).then(res => {
                    var data = res.data
                    var duty = JSON.parse(data.duty || "[]")
                    var requirement = JSON.parse(data.requirement || "[]")
                    var synthesis = JSON.parse(data.synthesis || "[]")
                    var dutyStr = ''
                    for (var i = 0; i < duty.length; i++) {
                        dutyStr += '<div>'+(i+1) + '、' + duty[i]+'</div>'
                    }
                    var requirementStr = ''
                    for (var j = 0; j < requirement.length; j++) {
                        requirementStr += '<div>'+(j+1) + '、' + requirement[j]+'</div>'
                    }
                    $(".postSee_name").html(data.name)
                    $(".postSee_duty").html(dutyStr)
                    $(".postSee_requirement").html(requirementStr)
                    $(".postSee_synthesis").html(synthesis.join('、'))
                    $('#post_see').data('duty', duty)
                    $('#post_see').data('requirement', requirement)
                    $('#post_see').data('id', id)
                })
                break;
            case 'changedContent':
                bounce.show($('#post_changedContent'));
                $('#post_changedContent input').val("")
                $("#post_changedContent .input_choose .search").prevAll().remove()

                $.ajax({
                    url: $.webRoot + '/post/getPostInfo.do',
                    data: {
                        id: id
                    }
                }).then(res => {
                    var data = res.data
                    var duty = JSON.parse(data.duty || "['']")
                    var requirement = JSON.parse(data.requirement || "['']")
                    var synthesis = JSON.parse(data.synthesis || "['']")
                    $("#post_changedContent [name='name']").val(data.name)
                    renderInput($("#post_changedContent"),'duty', duty)
                    renderInput($("#post_changedContent"),'requirement', requirement)
                    $("#post_changedContent").data("duty", duty)
                    $("#post_changedContent").data("requirement", requirement)
                    $.ajax($.webRoot + '/post/getSynthesizes.do').then(res => {
                        var list = res.data
                        var str = ''
                        list.map(item => {
                            if (synthesis.findIndex(it => it === item.itemName) !== -1) {
                                item.select =  true
                                str +=  '<div class="selected_item" data-id="'+item.id+'">' +
                                        '    <span class="selected_name">'+item.itemName+'</span>' +
                                        '    <span class="delRole">+</span>' +
                                        '</div>'
                            }
                        })
                        var listStr = updateSynthesizeList(list)
                        $("#post_changedContent .input_choose_list ").html(listStr)
                        $("#post_changedContent .input_choose").data('list', list)
                        $("#post_changedContent .input_choose .search").before(str).val("")
                    })
                })
                break;
            case 'changedRecord':
                bounce.show($("#post_changedRecord"));
                getPostChangeRecord(id)
                break;
            case 'stop':
                $.ajax({
                    url: $.webRoot + '/post/deactivatePost.do',
                    data: {
                        id: id
                    }
                }).then( res=>{
                    var data = res.data
                    if (data === 1) {
                        layer.msg("该岗位已停用！")
                        bounce.cancel()
                        getPostList()
                    } else {
                        layer.msg("操作失败！<br>因为系统中该岗位下有职工。")
                    }
                })
                break;
            case 'restore':
                bounce.show($("#bounce_tip"))
                $("#bounce_tip .tipMsg").html("确定恢复使用该岗位吗？")
                $("#bounce_tip .sureBtn").unbind().on("click", function (){
                    $.ajax({
                        url: $.webRoot + '/post/restorePost.do',
                        data: {
                            id: id
                        }
                    }).then( res=>{
                        var data = res.data
                        if (data === 1) {
                            layer.msg("操作成功")
                            bounce.cancel()
                            getVoidPostList()
                            getPostList()
                        } else {
                            layer.msg("操作失败")
                        }
                    })
                })
                break;
            case 'delete':
                $.ajax({
                    url: $.webRoot + '/post/deletePost.do',
                    data: {
                        id: id
                    }
                }).then( res=>{
                    var data = res.data
                    if (data === 1) {
                        layer.msg("该岗位已删除！")
                        bounce.cancel()
                        var page = $(".page:visible").attr("page")
                        if (page === 'main') {
                            getPostList()
                        } else {
                            getVoidPostList()
                        }
                    } else {
                        layer.msg("操作失败！<br>因为该岗位下已有使用数据。")
                    }
                })
                break;
        }
    })

    $(".page[page='stopDepartment']").on("click","[type='btn']",function (){
        var name = $(this).data("name");
        var subThis = $(this);
        switch (name) {
            case 'reService':
                var state = subThis.data("type");
                var id = subThis.parents("tr").data("id");
                var tip =
                    '<p>确定后，该部门在系统中又可重新被选择。</p>' +
                    '<p>确定恢复使用该部门吗？</p>';
                $('#stopOrReset').data('id',id);
                $("#stopOrReset").data("state",state);
                $("#stopOrReset .msgTip").html(tip);
                bounce.show($("#stopOrReset"));
                break;
            case 'susDel':
                var folderId = subThis.parents("tr").data("id");
                var folderName = subThis.parents("tr").data("name");
                $('.bounce-deleteClass').data('delid',folderId);
                $('.bounce-deleteClass').data('obj',subThis);
                $('.bounce-deleteClass>.bonceHead>span').html("删除部门-"+folderName);
                bounce.show($('.bounce-deleteClass'));
                break;
        }
    });

    var sw = false
    $("body").on("input focus keyup", '.autoFill', function () {
        if (sw === false) {
            var max = $(this).attr("max")
            var curLength = $(this).val().length;
            if (curLength > max) {
                curLength = max
                var cutValue = $(this).val().substring(0,max);
                $(this).val(cutValue);
            }
            $(this).siblings(".textMax").text( curLength +'/' + max );
        }

    })
    $("body").on("compositionstart", '.autoFill', function () {
        sw = true
    })
    $("body").on("compositionend", '.autoFill', function () {
        sw = false
        var max = $(this).attr("max")
        var curLength = $(this).val().length;
        if (curLength > max) {
            curLength = max
            var cutValue = $(this).val().substring(0,max);
            $(this).val(cutValue);
        }
        $(this).siblings(".textMax").text( curLength +'/' + max );
    })
    $(document).click(function () {
        $(".input_choose_list:not(.dir)").slideUp('fast');
    });
    $(".custom_select .input_choose").on("click", function () {
        $(this).find('.search').focus()
        $(this).parents(".custom_select").find(".input_choose_list").slideDown('fast')
        return false
    })
    $(".custom_select .input_choose").on('input', '.search', function () {
        var customSelector =  $(this).parents(".custom_select")
        var list = customSelector.find(".input_choose").data('list')
        var val = $(this).val()
        var newList = list.filter(item => {
            return item.itemName.indexOf(val) !== -1
        })
        var listStr = updateSynthesizeList(newList)
        customSelector.find(".input_choose_list").html(listStr)
    })
    $(".custom_select .input_choose").on('click','.delRole', function () {
        var customSelector =  $(this).parents(".custom_select")
        var userID = $(this).parents('.selected_item').data('id')
        var list = customSelector.find(".input_choose").data('list')

        list.map(item => {
            if (item.id === userID) {
                item.select = false
            }
        })

        var listStr = updateSynthesizeList(list)
        customSelector.find(".input_choose_list").html(listStr)
        $(this).parents('.selected_item').remove()
    })
    $(".input_choose_list").on('click', '.input_choose_item', function () {
        var customSelector =  $(this).parents(".custom_select")
        if(!$(this).hasClass("selected")){
            $(this).addClass('selected')
            var role_name = $(this).find('.input_choose_item_name').html()
            var role_id = $(this).data('id')
            var list = customSelector.find(".input_choose").data('list')

            list.map(item => {
                if (item.id === role_id) {
                    item.select = true
                }
            })

            var listStr = updateSynthesizeList(list)
            customSelector.find(".input_choose_list ").html(listStr)
            var str =   '<div class="selected_item" data-id="'+role_id+'">' +
                        '    <span class="selected_name">'+role_name+'</span>' +
                        '    <span class="delRole">+</span>' +
                        '</div>'
            customSelector.find(".search").before(str).val("")
        }
        return false
    })
});
//   updater:  姚宗涛， 2018-3-8   13:33:08  弹框确定按钮的颜色变化
function btnColor(obj,funName){
    var len = obj.val() ;
    var org = obj.data("orgname") ;
    if( len == "" || len == org ){
        obj.parents(".bonceCon").next().children(":eq(1)").removeClass("ty-btn-blue").addClass("ty-btn-gray") ;
        obj.parents(".bonceCon").next().children(":eq(1)").attr("onclick","") ;
    } else {
        obj.parents(".bonceCon").next().children(":eq(1)").removeClass("ty-btn-gray").addClass("ty-btn-blue") ;
        obj.parents(".bonceCon").next().children(":eq(1)").attr("onclick",funName+"()") ;
    }
}

// creator: 张旭博，2023-05-09 11:11:33，
function stopDepartmentBtn() {
    jumpPage("stopDepartment")
    getSuspendList();
}

//------------------------- 部门列表 --------------------------//

// creator：张旭博，2017-12-04 11:45:19，获取一级所有部门
function firstLevelLoad(orgType) {
    $.ajax({
        url: "../post/toPostManagement.do",
        data: {
            "orgId": sphdSocket.user.oid,
            "orgType":orgType,   //1-公司，2-部门，3—岗位
            "userId": sphdSocket.user.userID
        },
        success: function (data) {
            // 存储第一级目录数组
            var listFirstCategory = data["data"]["organizations"]; //
            switch (orgType){
                case 1:
                    break;
                case 2:
                    if(listFirstCategory.length < 1){
                        $(".initialSect").show();
                        $(".ty-fileContent").hide();
                    }else{
                        $(".initialSect").hide();
                        $(".ty-fileContent").show();
                        // 构建文件夹菜单树
                        var firstLevelStr = "" ;
                        firstLevelStr += '<div class="level1" level="1">' ;
                        for(var i in listFirstCategory){
                            //判断每个文件夹是否有子级文件夹  是否来显示左侧箭头
                            if(listFirstCategory[i]["childNum"] > 0){  // childStatus: 1 - 下有子文件夹，没有值代表没有子文件夹
                                firstLevelStr +=    '<li>' +
                                    '<div class="ty-treeItem" id='+ listFirstCategory[i]["id"] +'>' +
                                    '<i class="fa fa-angle-right"></i>' +
                                    '<i class="fa fa-folder"></i>' +
                                    '<span>'+listFirstCategory[i]["name"]+'</span>' +
                                    '</div>' +
                                    '</li>'
                            }else{
                                firstLevelStr +=    '<li>' +
                                    '<div class="ty-treeItem" id='+ listFirstCategory[i]["id"] +'>' +
                                    '<i class="ty-fa"></i>' +
                                    '<i class="fa fa-folder"></i>' +
                                    '<span>'+listFirstCategory[i]["name"]+'</span>' +
                                    '</div>' +
                                    '</li>'
                            }
                        }
                        firstLevelStr += '</div>';
                        $(".ty-colFileTree").html(firstLevelStr) ;
                        $(".ty-colFileTree .ty-treeItem").eq(0).click();
                    }
                    break;
                case 3:
                    var str = '';
                    for(var j=0;j<listFirstCategory.length;j++){
                        str +=      '<tr>' +
                                    '    <td>'+listFirstCategory[j].name+'</td>' +
                                    '    <td><span class="link-blue" type="btn" data-name="numSee">'+listFirstCategory[j].userNum+'</span></td>' +
                                    '    <td>' +
                                    '        <span class="link-blue" type="btn" data-name="changeWord">改错字</span>' +
                                    '        <span class="link-blue" type="btn" data-name="changeName">岗位改名</span>' +
                                    '    </td>' +
                                    '    <td class="tdp">'+
                                    '        <span class="link-blue" type="btn" data-name="see">查看</span>' +
                                    '        <span class="link-blue" type="btn" data-name="changedContent">修改内容</span>' +
                                    '        <span class="link-blue" type="btn" data-name="changedRecord">修改记录</span>' +
                                    '        <span class="link-blue" type="btn" data-name="stop">停用</span>' +
                                    '        <span class="link-red" type="btn" data-name="delete">删除</span>' +
                                    '    </td>' +
                                    '</tr>'
                    }
                    $("#postList tbody").html(str);
                    break;
            }
        },
    })

}
// creator：张旭博，2017-05-04 13:16:41，修改部门弹窗-》确认
function sureChangeClass() {
    var categoryId = $(".CategoryMessage").attr("id");
    var categoryName = $.trim($(".bounce-changeClass .categoryName").val());
    if(categoryName === ""){
        bounce.show( $("#tip") ) ; $("#tipMess").html( "部门名称不能为空" ) ;
    }else{
        var json = {
            "deptId": categoryId,
            "departmentName": categoryName,
            "userId": sphdSocket.user.userID,
            "type": "1"
        };
        if ($("#effectiveDate:visible").length > 0) {
            var effectiveDate = $("#effectiveDate").val();
            if (effectiveDate == "") {
                bounce_Fixed.show( $("#knowTip") ) ;$("#knowTip .knowWord").html( "请选择本修改的生效日期" ) ;
                return false;
            } else {
                json.type = 2;
                json.enableTime = new Date(effectiveDate);
            }
        }
        $.ajax({
            url: "../post/updateNameWord.do",
            data: json,
            success: function (data) {
                var status = data["data"]["status"];
                bounce.cancel() ;
                if(status === 0) {
                    bounce.show( $("#tip") ) ; $("#tipMess").html("有重名的部门，操作失败！");
                }else if(status === 1) {
                    layer.msg("修改成功！");
                    $(".bounce-changeClass .categoryName").val("");
                    $(".ty-treeItemActive").click();
                } else {
                    layer.msg("系统错误，请重试！");
                }
            },
            error: function (err) {
                layer.msg("系统错误，请重试！");
            } ,
        })
    }
}
// updater：姚宗涛，2018-01-25 14:42:56，删除部门弹窗-》确认
function sureDeleteClass(){
    var categoryId = '', state = 1;
    if ($(".departMain:visible").length > 0) {
        categoryId = $.trim($(".CategoryMessage").attr("id"));
    } else {
        state = '2'
        categoryId = $('.bounce-deleteClass').data('delid');
    }
    $.ajax({
        url: "../post/deleteDepartment.do",
        data: {
            "orgId":categoryId,
            "oid":sphdSocket.user.oid
        },
        success: function (data) {
            var status = data["status"];
            bounce.cancel() ;
            if(data["data"]["status"] === 1){
                layer.msg("删除成功！");
                if (state == '1') {
                    if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                        firstLevelLoad(2);
                    } else {
                        if($(".ty-treeItemActive").parent().parent().children().length<2){
                            $(".ty-treeItemActive").parent().parent().prev().children().eq(0).attr("class","ty-fa");
                        }
                        $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                    }
                } else if (state == '2'){
                    getSuspendList();
                }
            }else{
                bounce_Fixed.show( $("#knowTip") ) ; $("#knowTip .knowWord").html("对不起，为确保系统功能正常，此部门不可被删除！");
            }
        }
    })
}
// creator：张旭博，2017-05-04 13:18:40，新增子级部门弹窗-》确认
function sureNewClass() {
    var categoryId = $(".CategoryMessage").attr("id");
    var categoryName = $.trim($(".bounce-newClass .categoryName").val());
    if(categoryId === ""){
        bounce.show( $("#tip") ) ; $("#tipMess").html("未获取到本部门！") ;
    }else{
        $.ajax({
            url: "../post/childrenSubmit.do",
            data: {
                "orgChildName" : categoryName,
                "orgId" : categoryId,
                "userId": sphdSocket.user.userID
            },
            success: function (data) {
                if(data == null || data == undefined){
                    bounce.show( $("#tip") ) ; $("#tipMess").html("返回值不存在！") ;
                }else{
                    var status = data["status"]; // 1 - 是新增成功，0 - 代表新增的文件夹名称重复
                    if(status === 1){
                        bounce.cancel();
                        layer.msg("新增成功！");
                        if($(".ty-treeItemActive").children().eq(0).hasClass("ty-fa")){
                            $(".ty-treeItemActive").children().eq(0).attr("class","fa fa-angle-right");
                            $(".ty-treeItemActive").click();
                        }else{
                            $(".ty-treeItemActive").click();
                            $(".ty-treeItemActive").click();
                        }

                    }else if(status == 0){
                        bounce.show( $("#tip") ) ; $("#tipMess").html("新增失败") ;
                        $(".bounce-newClass .categoryName").val("");
                    }else if(status == 2){
                        bounce.show( $("#tip") ) ; $("#tipMess").html("已经第八级了,不能再增加了") ;
                        $(".bounce-newClass .categoryName").val("");
                    }else if(status == 3){
                        bounce.show( $("#tip") ) ; $("#tipMess").html("有重名的部门，操作失败!") ;
                        $(".bounce-newClass .categoryName").val("");
                    }else{
                        bounce.show( $("#tip") ) ; $("#tipMess").html( "未知的返回值错误！" ) ;
                        $(".bounce-newSameClass .categoryName").val("");
                    }
                }
            }
        })
    }
}
// creator：张旭博，2017-05-09 10:13:27，点击部门-》新增同级部门
function newSameClass(state) {
    //清空输入框
    $(".bounce-newSameClass .categoryName").val("");
    if(state === 0){
        $(".bounce-newSameClass .bonceHead span").html("新增同级部门");
    }else{
        $(".bounce-newSameClass .bonceHead span").html("新增部门");
    }
    //显示弹窗
    bounce.show($(".bounce-newSameClass"));
}
// creator：张旭博，2017-05-09 10:14:11，新增同级部门弹窗-》确认
function sureNewSameClass() {
    //event.stopPropagation();
    var thisId = $(".ty-treeItemActive").attr("id");
    if(thisId === undefined){
        thisId = '';
    }
    var categoryName =$.trim($(".bounce-newSameClass .categoryName").val());
    if(categoryName === ""){
        bounce.show( $("#tip") ) ; $("#tipMess").html("部门名称不能为空！") ;
    }else{
        $.ajax({
            url: "../post/sameLevelSubmit.do",
            data: {"orgId":thisId,"orgName":categoryName, "userId":sphdSocket.user.userID},
            success: function (data) {
                var status = data["status"]; // 1是新增成功，0代表新增的文件夹名称重复
                if(status === 0){
                    bounce.show( $("#tip") ) ; $("#tipMess").html( "新增失败" ) ;
                    $(".bounce-newSameClass .categoryName").val("");
                }else if(status === 1){
                    if($(".ty-colFileTree").html() === ""){
                        window.location.reload();
                    }else{
                        if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                            firstLevelLoad(2);
                        }else{
                            $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                            $(".ty-treeItemActive").click();
                        }
                    }
                    bounce.cancel();
                    layer.msg("新增成功");
                }else if(status === 2){
                    bounce.show( $("#tip") ) ; $("#tipMess").html( "有重名的部门，操作失败！" ) ;
                    $(".bounce-newSameClass .categoryName").val("");
                }else{
                    bounce.show( $("#tip") ) ; $("#tipMess").html( "未知的返回值错误！" ) ;
                    $(".bounce-newSameClass .categoryName").val("");
                }
            },
            error:function (err) {
                bounce.show( $("#tip") ) ; $("#tipMess").html( "系统错误，请重试！" ) ;
            } ,
        })
    }

}
// creator: 李玉婷，2020-09-03 16:15:52，返回
function backMain() {
    $(".ty-secondTab").show();
    $(".departMain").show().siblings().hide();
}
// creator: 李玉婷，2020-09-22 20:59:07，停用/启用部门弹窗-》确认
function sureStopOrReset(){
    var state = $("#stopOrReset").data("state");
    var categoryId = "";
    if (state == '0') {
        categoryId = $.trim($(".CategoryMessage").attr("id"));
    } else {
        categoryId = $("#stopOrReset").data("id");
    }
    $.ajax({
        url: "../post/updateEnabled.do",
        data: {
            "deptId":categoryId,
            "userId":sphdSocket.user.userID,
            "enabled":state
        },
        success: function (data) {
            var status = data["data"]["statue"];
            bounce.cancel() ;
            if(status === 1){
                layer.msg("操作成功！");
                if (state == '0') {
                    if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                        firstLevelLoad(2);
                    } else {
                        if($(".ty-treeItemActive").parent().parent().children().length<2){
                            $(".ty-treeItemActive").parent().parent().prev().children().eq(0).attr("class","ty-fa");
                        }
                        $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                    }
                }else if (state == '1') {
                    firstLevelLoad(2);
                    getSuspendList();
                }

            }else if (status == '2') {
                bounce_Fixed.show( $("#knowTip") ) ; $("#knowTip .knowWord").html("对不起，为确保系统功能正常，此部门不可被停用！");
            }else if (status == '3') {
                var con =
                    '<p>操作失败！</p>' +
                    '<p>因为系统中该部门下有职工。</p>';
                bounce_Fixed.show( $("#knowTip") ) ; $("#knowTip .knowWord").html(con);
            }else if (status == '4') {
                var con =
                    '<p>操作失败！</p>' +
                    '<p>因为系统中该部门下有子级部门。</p>';
                bounce_Fixed.show( $("#knowTip") ) ; $("#knowTip .knowWord").html(con);
            } else {
                layer.msg(data["data"]["content"]);
            }
        }
    })
}
// creator: 李玉婷，2020-09-22 21:52:29，职工查看列表
function userNumSee (categoryId, categoryName) {
    $(".page[page='roleList'] .tip").html('');
    $(".page[page='roleList'] tbody").html('');
    $.ajax({
        url: "../post/getUsers.do",
        data: {
            "deptId":categoryId,
            "oid":sphdSocket.user.oid
        },
        success: function (data) {
            var usersList = data["data"]["users"];
            if (usersList && usersList.length > 0) {
                var html = '';
                for (var t=0;t<usersList.length;t++){
                    html += '<tr>' +
                        '<td>'+ handleNull(usersList[t].userName) +'</td>' +
                        '<td>'+ chargeSex(handleNull(usersList[t].gender)) +'</td>' +
                        '<td>'+ handleNull(usersList[t].mobile) +'</td>' +
                        '<td>'+ handleNull(usersList[t].departName) +'</td>' +
                        '<td>'+ handleNull(usersList[t].postName) +'</td>' +
                        '<td>'+ handleNull(usersList[t].leaderName) +'</td>' +
                        '</tr>';
                }
            }
            $(".page[page='roleList'] .tip").html('当前'+categoryName+'共有职工'+usersList.length+'人');
            $(".page[page='roleList'] tbody").html(html);
        }
    })
}
// creator: 李玉婷，2020-09-22 15:47:59，已停用的部门
function getSuspendList () {
    $(".inSuspendList tbody").html("");
    $.ajax({
        url: "../post/getDeptByEnable.do",
        data: {
            "oid":sphdSocket.user.oid
        },
        success: function (data) {
            var list = data["data"]["organizations"];
            if (list && list.length > 0) {
                var html = '';
                $("#inSuspendNum").html(list.length);
                for (var t=0;t<list.length;t++){
                    html += '<tr data-id="'+ list[t].id +'" data-name="'+ list[t].name +'">' +
                        '<td>'+ list[t].name +'</td>' +
                        '<td>'+ list[t].createName + '&nbsp;&nbsp;' + new Date(list[t].createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '<td>'+ list[t].updateName + '&nbsp;&nbsp;' + new Date(list[t].updateDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '<td>' +
                        '   <span class="link-blue" type="btn" data-name="reService" data-type="1">恢复使用</span> '+
                        '   <span class="link-red" type="btn" data-name="susDel">删除</span>' +
                        '</td>' +
                        '</tr>';
                }
            } else {
                $("#inSuspendNum").html("0");
            }
            $(".inSuspendList tbody").html(html);
        }
    })
}
// creator: 李玉婷，2020-09-23 8:47:59，修改记录
function changeRecord () {
    var categoryId = $(".CategoryMessage").attr("id");
    $(".changeRecord tbody").html("");
    $.ajax({
        url: "../post/getRecords.do",
        data: {
            "deptId":categoryId
        },
        success: function (data) {
            var list = data["data"]["listMap"];
            var editNum = data["data"]["num"];
            var otherInfo = data["data"]["organizationHistory"];
            if (editNum && editNum > 0) {
                var html = '';
                var title = $(".ty-treeItemActive>span").text();
                var creatInfo = otherInfo.createName + '&nbsp;&nbsp;' + new Date(otherInfo.createDate).format('yyyy-MM-dd hh:mm:ss');
                $(".changeRecord").show();
                $(".recordEffect").show().siblings().hide();
                $(".recordEffect .recordInfo").html(creatInfo);
                $(".recordEffect .record_postName").html(title);
                $(".recordEffect .record_num").html(editNum);
                for (var t=0;t<list.length;t++){
                    html += '<tr>' +
                        '<td>'+ list[t].recordState +'</td>' +
                        '<td>'+ list[t].departmentName +'</td>' +
                        '<td>'+ changeStr(list[t].operation) +'</td>' +
                        '<td>'+ list[t].state +'</td>' +
                        '<td>'+ changeGang(new Date(list[t].openDate).format('yyyy-MM-dd')) +'</td>' +
                        '<td>'+ list[t].createName + '&nbsp;&nbsp;' + new Date(list[t].createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '</tr>';
                }
                $(".changeRecord tbody").html(html);
            } else {
                var creatInfo = otherInfo.createName + '&nbsp;&nbsp;' + new Date(otherInfo.createDate).format('yyyy-MM-dd hh:mm:ss');
                if (list && list.length > 0) {
                    var html = '';
                    var title = $(".ty-treeItemActive>span").text();
                    $(".changeRecord").show();
                    $(".recordOrg").show().siblings().hide();
                    $(".recordOrg .recordInfo").html(creatInfo);
                    $(".recordOrg .record_postName").html(title);
                    for (var t=0;t<list.length;t++){
                        html += '<tr>' +
                            '<td>'+ list[t].recordState +'</td>' +
                            '<td>'+ list[t].departmentName +'</td>' +
                            '<td>'+ changeStr(list[t].operation) +'</td>' +
                            '<td>'+ list[t].state +'</td>' +
                            '<td>'+ changeGang(new Date(list[t].openDate).format('yyyy-MM-dd')) +'</td>' +
                            '<td>'+ list[t].createName + '&nbsp;&nbsp;' + new Date(list[t].createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                            '</tr>';
                    }
                    $(".changeRecord tbody").html(html);
                } else {
                    $(".changeRecord").hide();
                    $(".recordNone").show().siblings().hide();
                    $(".recordNone .recordInfo").html(creatInfo);
                }
            }
        }
    })
}
// creator: 李玉婷，2020-10-26 08:29:59，能不能修改判断
function adjustAdvence(type,obj) {
    var categoryId = $(".CategoryMessage").attr("id");
    $.ajax({
        url: "../post/getReturnTips.do",
        data: {
            "oid": sphdSocket.user.oid,
            "deptId": categoryId,
            "type": type
        },
        success: function (data) {
            var state = data.data.status;
            if (state == '1') {
                if (type == 1) {
                    var title = $(".ty-treeItemActive>span").text();
                    var flag = obj.parents("tr").data("flag");
                    $(".bounce-changeClass .categoryName").val(title);
                    $(".bounce-changeClass .categoryName").data("orgname", title);
                    $(".bounce-changeClass .bonceHead>span").html("改错字");
                    if (flag == 'sys') {
                        $(".saleChangeTip").show();
                    } else {
                        $(".saleChangeTip").hide();
                    }
                    $("#changeEffect").hide();
                    $("#changeEffect input").val("");
                    $(".bounce-changeClass .change").addClass("ty-btn-gray").removeClass("ty-btn-blue").attr("onclick", "");
                    //显示弹窗
                    bounce.show($('.bounce-changeClass'));
                } else if (type == 2) {
                    var tip =
                        '<p>确定后，在系统中将无法再选择该部门。</p>' +
                        '<p>确定停用该部门吗？</p>';
                    $("#stopOrReset .msgTip").html(tip);
                    bounce.show($("#stopOrReset"));
                } else if (type == 3) {
                    var folderName = $(".ty-treeItemActive span").text();
                    $('.bounce-deleteClass>.bonceHead>span').html("删除部门-" + folderName);
                    bounce.show($('.bounce-deleteClass'));
                }
            } else if (state == '3' || state == '4') {
            } else if (state == '2') {
                var tipStr =
                    "<p>操作失败！</p>" +
                    "<p>因为该部门新名字的生效日期尚未到来。</p>";
                bounce_Fixed.show($("#knowTip"));
                $("#knowTip .knowWord").html(tipStr);
                return false;
            } else if (state == '5') {
                var con =
                    '<p>操作失败！</p>' +
                    '<p>因为系统中该部门下有职工。</p>';
                bounce_Fixed.show($("#knowTip"));
                $("#knowTip .knowWord").html(con);
                return false;
            } else if (state == '6') {
                var con =
                    '<p>操作失败！</p>' +
                    '<p>因为系统中该部门下有子级部门。</p>';
                bounce_Fixed.show($("#knowTip"));
                $("#knowTip .knowWord").html(con);
                return false;
            } else {
                bounce.show($("#tip"));
                $("#tipMess").html("系统错误，请重试！");
                return false;
            }
        }
    });
}

// creator: 李玉婷，2020-09-25 12:34:18，转换
function changeStr(str) {
    var result = '';
    switch (str) {
        case '1':
            result = '--';
            break;
        case '4':
            result = '改错字';
            break;
        case '5':
            result = '部门改名';
            break;
    }
    return result;
}
//------------------------- 岗位列表 --------------------------//

// creator: 张旭博，2023-05-24 08:52:38, 获取岗位列表
function getPostList() {
    $.ajax($.webRoot + '/post/getPostList.do')
        .then(res => {
            var data = res.data
            var str = '';
            for(var j=0;j<data.length;j++){
                str +=  '<tr data-id="'+data[j].id+'">' +
                        '    <td>'+data[j].name+'</td>' +
                        '    <td><span class="link-blue" type="btn" data-name="numSee">'+data[j].userNums+'</span></td>' +
                        '    <td>' +
                        '        <span class="link-blue" type="btn" data-name="changeWord">改错字</span>' +
                        '        <span class="link-blue" type="btn" data-name="changeName">岗位改名</span>' +
                        '    </td>' +
                        '    <td class="tdp">'+
                        '        <span class="link-blue" type="btn" data-name="see">查看</span>' +
                        '        <span class="link-blue" type="btn" data-name="changedContent">修改内容</span>' +
                        '        <span class="link-blue" type="btn" data-name="changedRecord">修改记录</span>' +
                        '        <span class="link-blue" type="btn" data-name="stop">停用</span>' +
                        '        <span class="link-red" type="btn" data-name="delete">删除</span>' +
                        '    </td>' +
                        '</tr>'
            }
            $("#postList tbody").html(str);
        })
}

// creator：张旭博，2017-12-05 16:09:27，新增岗位
function addNewPosition(){
    bounce.show($("#addPost"));
    var dutyList = ['']
    var requirementList = ['']
    $("#addPost input").val("")
    $("#addPost .input_show .search").prevAll().remove()
    $('#addPost .textMax').each(function (){
        var curLength = $(this).parents(".item").find(".autoFill").val().length
        var max = $(this).parents(".item").find(".autoFill").attr("max")
        $(this).text( curLength +'/' + max );
    })

    $("#addPost").data("duty", dutyList)
    $("#addPost").data("requirement", requirementList)
    renderInput($("#addPost"),'duty', dutyList)
    renderInput($("#addPost"), 'requirement', requirementList)
    $.ajax($.webRoot + '/post/getSynthesizes.do').then(res => {
        var list = res.data
        $("#addPost .input_choose").data('list', list)
        var str = updateSynthesizeList(list)
        $("#addPost .input_choose_list").html(str)
    })
}

// creator: 张旭博，2023-05-10 11:45:44， 综合 - 增加
function addSynthesizeBtn(selector) {
    var bounce = selector.parents(".bonceContainer")
    bounce_Fixed.show($("#addSynthesize"))
    $("#addSynthesize input").val("")
    $("#addSynthesize .sureBtn").unbind().on("click", function (){
        var name = $("#addSynthesize input").val()
        if ($.trim(name) === '') {
            layer.msg("请输入内容！")
            return false
        }
        $.ajax({
            url: $.webRoot + '/post/addSynthesize.do',
            data: {
                name: name
            }
        }).then(res => {
            var data = res.data
            if (data === 1) {
                layer.msg("新增成功！")
                bounce_Fixed.cancel()
                $.ajax($.webRoot + '/post/getSynthesizes.do').then(res => {
                    var list = res.data
                    var str = updateSynthesizeList(list)
                    bounce.find(".input_choose_list").html(str)
                    bounce.find(".input_choose").data('list', list)
                })
            }
        })
    })
}

// creator: 张旭博，2019-07-02 12:07:26，更新综合列表
function updateSynthesizeList(list) {
    var str = ''
    for (var i in list) {
        str += '<div class="input_choose_item ' + (list[i].select ? 'selected' : '') + '" data-id="' + list[i].id + '">' +
            '    <div class="input_choose_item_info">' +
            '        <div class="input_choose_item_name text-center">' + list[i].itemName + '</div>' +
            '    </div>' +
            '</div>'
    }
    return str
}

// creator: 张旭博，2023-05-24 09:11:57, 增加一行
function addRow(selector, name) {
    var list = []
    selector.parents(".bonceContainer").find("." +name + "Panel .item-row").each(function (){
        var content = $(this).find("input").val()
        list.push(content)
    })
    list.push('')
    renderInput(selector.parents(".bonceContainer"), name, list)
    selector.parents(".bonceContainer").data(name, list)
}

function delRow(selector) {
    var name = selector.next("input").attr("name")
    var list = []
    var bounce = selector.parents(".bonceContainer")
    selector.parents(".item-row").remove()
    bounce.find("." +name + "Panel .item-row").each(function (){
        var content = $(this).find("input").val()
        list.push(content)
    })
    renderInput(bounce, name, list)
    bounce.data(name, list)
}

function upRow(selector) {
    if(!selector.hasClass("disabled")) {
        let prevVal = selector.parents(".item-row").prev().find('input').val()
        let thisVal = selector.siblings("input").val()
        selector.siblings("input").val(prevVal).siblings(".textMax").text( prevVal.length +'/' + 60 );
        selector.parents(".item-row").prev().find('input').val(thisVal).siblings(".textMax").text( thisVal.length +'/' + 60 );
    }
}

function downRow(selector) {
    if(!selector.hasClass("disabled")) {
        let nextVal = selector.parents(".item-row").next().find('input').val()
        let thisVal = selector.siblings("input").val()
        selector.siblings("input").val(nextVal).siblings(".textMax").text(nextVal.length + '/' + 60);
        selector.parents(".item-row").next().find('input').val(thisVal).siblings(".textMax").text(thisVal.length + '/' + 60);
    }
}

function sortPost(selector, name) {
    bounce_Fixed2.show($("#reorder"))
    $("#reorder .item_" + name).show().siblings().hide()
    let list = $('#post_see').data(name)
    renderInput($("#reorder"), name, list)
    $("#reorder").data('name', name)
}

function sureReorder() {
    let name = $("#reorder").data('name')
    let id = $('#post_see').data('id')
    if (name === 'duty') {
        let dutyArr = []
        $("#reorder .dutyPanel .item-row").each(function (){
            let content = $(this).find("input").val()
            dutyArr.push(content)
        })
        $.ajax({
            url: $.webRoot + '/post/orderPostDuty.do',
            data: {id: id, dutys: JSON.stringify(dutyArr)},
        }).then(res => {
            let success = res.success
            if (success === 1) {
                layer.msg('操作成功')
                bounce_Fixed2.cancel()
                bounce_Fixed.cancel()
            } else {
                layer.msg('操作失败')
            }
        })
    } else {
        let requirementArr = []
        $("#reorder .requirementPanel .item-row").each(function (){
            let content = $(this).find("input").val()
            requirementArr.push(content)
        })
        $.ajax({
            url: $.webRoot + '/post/orderPostRequirement.do',
            data: {id: id, requirements: JSON.stringify(requirementArr)},
        }).then(res => {
            let success = res.success
            if (success === 1) {
                layer.msg('操作成功')
                bounce_Fixed2.cancel()
                bounce_Fixed.cancel()
            } else {
                layer.msg('操作失败')
            }
        })
    }

    $("#addPost .requirementPanel .item-row").each(function (){
        var content = $(this).find("input").val()
        requirementArr.push(content)
    })
}

// creator: 张旭博，2019-07-02 12:07:26，渲染新增岗位的输入框
function renderInput(selector, name, inputList) {
    var str = ''
    if (inputList.length > 1) {
        for (var i=0; i<inputList.length; i++) {
            str +=  `<div class="item-row">
                        <label class="input_num">${i+1}</label>
                        <i class="fa fa-long-arrow-up input_up ${i===0?'disabled':''}" title="上移一行" onclick="upRow($(this))"></i>
                        <i class="fa fa-long-arrow-down input_down ${i===inputList.length-1?'disabled':''}" title="下移一行" onclick="downRow($(this))"></i>
                        ${i>0?'<i class="fa fa-minus-circle input_del" onclick="delRow($(this))"></i>':''}
                        <input class="kj-input autoFill" max="60" name="${name}" value="${inputList[i]}"/>
                        <div class="textMax text-right">${inputList[i].length}/60</div>
                    </div>`
        }
    } else {
        for (var i=0; i<inputList.length; i++) {
            str +=  '<div class="item-row">' +
                    '    <input class="kj-input autoFill" max="60" name="'+name+'" value="'+inputList[i]+'"/>' +
                    '    <div class="textMax text-right">'+inputList[i].length+'/60</div>' +
                    '</div>'
        }
    }
    selector.find("." +name + "Panel").html(str)
}

// updater：姚宗涛，2018-2-11 10:37:58，新增岗位-确认
function sureAddPost(){
    var name = $("#addPost [name='name']").val();
    if ($.trim(name) === '') {
        layer.msg("请填写必填项！")
        return false
    }
    var dutyArr = [], requirementArr = [], synthesisArr = []
    $("#addPost .dutyPanel .item-row").each(function (){
        var content = $(this).find("input").val()
        dutyArr.push(content)
    })
    $("#addPost .requirementPanel .item-row").each(function (){
        var content = $(this).find("input").val()
        requirementArr.push(content)
    })
    $("#addPost .synthesisPanel .input_show .selected_item").each(function(){
        var content= $(this).find('.selected_name').html();
        synthesisArr.push(content);
    });
    $.ajax({
        url: $.webRoot + '/post/addPost.do',
        data:{
            name: name,
            dutys: JSON.stringify(dutyArr),
            requirements: JSON.stringify(requirementArr),
            synthesis: JSON.stringify(synthesisArr)
        }
    }).then(res => {
        var data = res.data
        if (data === 1) {
            layer.msg("操作成功")
            bounce.cancel()
            var page = $(".page:visible").attr("page")
            if (page === 'main') {
                getPostList()
            } else {
                getVoidPostList()
            }
        } else {
            layer.msg("操作失败")
        }
    })
}

// creator: 张旭博，2023-05-24 11:13:18, 改错字
function updatePostCorrect() {
    var id = $(".postMain").data("postId")
    var name = $("#post_changeWord input").val()
    if ($.trim(name) === '') {
        layer.msg("请输入内容！")
        return false
    }
    $.ajax({
        url: $.webRoot + '/post/updatePostCorrect.do',
        data: {
            id: id,
            name: name
        }
    }).then(res => {
        var data = res.data
        if (data === 1) {
            layer.msg("操作成功")
            bounce.cancel()
            getPostList()
        } else if (data === 2) {
            layer.msg("操作失败！该岗位新名称的生效日期还没到呢！")
        } else {
            layer.msg("操作失败")
        }
    })
}

// creator: 张旭博，2023-05-24 11:13:18, 岗位改名
function updatePostName() {
    var id = $(".postMain").data("postId")
    var name = $("#post_changeName [name='name']").val()
    var beginTime = $("#post_changeNameDate").val()
    if ($.trim(name) === '') {
        layer.msg("请输入岗位名称！")
        return false
    }
    if ($.trim(beginTime) === '') {
        layer.msg("请选择本修改的生效日期！")
        return false
    }
    $.ajax({
        url: $.webRoot + '/post/updatePostName.do',
        data: {
            id: id,
            name: name,
            beginTime: beginTime
        }
    }).then(res => {
        var data = res.data
        if (data === 1) {
            layer.msg("操作成功")
            bounce.cancel()
            getPostList()
        } else if (data === 2) {
            layer.msg("操作失败！该岗位新名称的生效日期还没到呢！")
        } else {
            layer.msg("操作失败")
        }
    })
}

// creator: 张旭博，2023-05-24 05:32:16, 获取岗位修改记录
function getPostChangeRecord(id) {
    $("#post_changedRecord tbody").html("")
    $.ajax({
        url: $.webRoot + '/post/getPostHistories.do',
        data: {
            id: id
        }
    }).then( res=>{
        var data = res.data
        var list = data.historyList
        if (data.number === 0) {
            $("#post_changedRecord .tip").html('<div>本岗位信息尚未经修改。</div><div class="text-right">创建人：'+data.updateName + ' ' + moment(data.updateDate).format("YYYY-MM-DD HH:mm:ss") +'</div>')
        } else {
            $("#post_changedRecord .tip").html('<div>当前数据为第'+data.number+'次修改后的结果。</div><div class="text-right">修改人：'+data.updateName + ' ' + moment(data.updateDate).format("YYYY-MM-DD HH:mm:ss") +'</div>')
        }

        var str = ''
        for (var i=0; i<list.length; i++) {
            var nameStr = list[i].updateNature === '内容修改'?'<span class="hd">'+JSON.stringify(list[i].info)+'</span><span class="link-blue" onclick="seePostChangeRecord($(this))">查看</span>':list[i].name
            var beginDate = (i === 0 || list[i].updateNature === '改错字' || list[i].updateNature === '内容修改')?'--':moment(list[i].beginDate).format("YYYY-MM-DD")
            str +=  ' <tr>' +
                '    <td>'+list[i].dataState+'</td>' +
                '    <td>'+nameStr+'</td>' +
                '    <td>'+list[i].updateNature+'</td>' +
                '    <td>'+list[i].state+'</td>' +
                '    <td>'+beginDate+'</td>' +
                '    <td>'+list[i].updateName + ' ' +moment(list[i].updateDate).format("YYYY-MM-DD HH:mm:ss")+'</td>' +
                '</tr>'
        }
        $("#post_changedRecord tbody").html(str)
    })
}

// creator: 张旭博，2023-07-03 10:25:35， 获取岗位修改记录中的查看
function seePostChangeRecord(selector) {
    var info = selector.prev().html()
    if (info) {
        var data = JSON.parse(info)
        bounce_Fixed.show($('#post_see'));
        var duty = JSON.parse(data.duty || "[]")
        var requirement = JSON.parse(data.requirement || "[]")
        var synthesis = JSON.parse(data.synthesis || "[]")
        var dutyStr = ''
        for (var i = 0; i < duty.length; i++) {
            dutyStr += '<div>'+(i+1) + '、' + duty[i]+'</div>'
        }
        var requirementStr = ''
        for (var j = 0; j < requirement.length; j++) {
            requirementStr += '<div>'+(j+1) + '、' + requirement[j]+'</div>'
        }
        $(".postSee_name").html(data.name)
        $(".postSee_duty").html(dutyStr)
        $(".postSee_requirement").html(requirementStr)
        $(".postSee_synthesis").html(synthesis.join('、'))
    }
}

// creator: 张旭博，2023-05-24 11:13:18, 岗位 - 修改内容
function updatePostCont() {
    var id = $(".postMain").data("postId")
    var dutyArr = [], requirementArr = [], synthesisArr = []
    $("#post_changedContent .dutyPanel .item-row").each(function (){
        var content = $(this).find("input").val()
        dutyArr.push(content)
    })
    $("#post_changedContent .requirementPanel .item-row").each(function (){
        var content = $(this).find("input").val()
        requirementArr.push(content)
    })
    $("#post_changedContent .synthesisPanel .input_show .selected_item").each(function(){
        var content= $(this).find('.selected_name').html();
        synthesisArr.push(content);
    });

    $.ajax({
        url: $.webRoot + '/post/updatePostCont.do',
        data: {
            id: id,
            dutys: JSON.stringify(dutyArr),
            requirements: JSON.stringify(requirementArr),
            synthesis: JSON.stringify(synthesisArr)
        }
    }).then(res => {
        var data = res.data
        if (data === 1) {
            layer.msg("操作成功")
            bounce.cancel()
            getPostList()
        } else {
            layer.msg("操作失败")
        }
    })
}




// creator: 张旭博，2023-05-24 01:48:28, 已停用的岗位
function stopPostBtn() {
    jumpPage("stopPost", function (){
        getVoidPostList()
    })
}

function getVoidPostList() {
    $.ajax($.webRoot + '/post/deactivatePostList.do')
        .then(res => {
            var data = res.data
            var str = '';
            for(var j=0;j<data.length;j++){
                str +=  '<tr data-id="'+data[j].id+'">' +
                    '    <td>'+data[j].name+'</td>' +
                    '    <td>' +data[j].createName+ ' ' + moment(data[j].createDate).format("YYYY-MM-DD HH:mm:ss") + '</td>' +
                    '    <td>' +data[j].updateName+ ' ' + moment(data[j].updateDate).format("YYYY-MM-DD HH:mm:ss") + '</td>' +
                    '    <td class="tdp">'+
                    '        <span class="link-blue" type="btn" data-name="see">查看</span>' +
                    '        <span class="link-blue" type="btn" data-name="changedRecord">修改记录</span>' +
                    '        <span class="link-blue" type="btn" data-name="restore">恢复使用</span>' +
                    '        <span class="link-red" type="btn" data-name="delete">删除</span>' +
                    '    </td>' +
                    '</tr>'
            }
            $(".page[page='stopPost'] .stopPostNum").html(data.length);
            $(".page[page='stopPost'] tbody").html(str);
        })
}

// creator: 张旭博，2023-02-24 15:39:49，返回上一步
function back() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr.pop()
    $("#home").data("pathArr", pathArr)
    isShowNav()
    var lastEle = pathArr[pathArr.length - 1]
    $(".page[page='"+lastEle+"']").show().siblings(".page").hide()
    if (lastEle === 'main') {
        $(".backBtn").hide()
    }
}
// creator: 张旭博，2023-02-24 15:40:51，回到主页
function backToMain() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr = [pathArr[0]]
    $("#home").data("pathArr", pathArr)
    isShowNav()
    $(".page[page='"+pathArr[0]+"']").show().siblings(".page").hide()
}

// creator: 张旭博，2023-02-09 04:56:46， 跳转及退回
function jumpPage(page, callback) {
    var arr = $("#home").data("pathArr") || []
    arr.push(page)
    $("#home").data("pathArr", arr)
    isShowNav()
    $(".page[page='"+page+"']").show().siblings(".page").hide()
    if (page !== 'main') {
        $(".backBtn").show()
    }
    if (callback) { callback()}
}

// creator: 张旭博，2023-02-24 15:40:37，是否禁用导航
function isShowNav() {
    var pathArr = $("#home").data("pathArr")
    if (pathArr.length > 1) {
        $(".ty-page-header").show()
    } else {
        $(".ty-page-header").hide()
    }
}


// creator：张旭博，2017-11-06 09:23:16，转换男女
function chargeSex(sex) {
    switch(sex){
        case "1":
            return "男";
            break;
        case "0":
            return "女";
            break;
        default:
            return '';
    }
}
// creator: 李玉婷，2020-10-27 09:48:23，转换--
function changeGang(str) {
    if (str == '' || str == undefined ||  str == null) {
        return '--';
    } else {
        return str;
    }
}
var day1 = new Date();
var temorrow = day1.setDate(day1.getDate() + 1);
laydate.render({elem: '#effectiveDate',type: 'date',min: temorrow});
laydate.render({elem: '#post_changeNameDate',type: 'date',min: 1});