/**
 * Created by 侯杏哲 on 2017/2/10.
 */
$(function(){
    // creator : 侯杏哲 2017-05-15 权限分配 下拉框 赋默认值
    tySelectSetInit() ;
    // creator : 张旭博 2017-05-15 审批权限 - 项目管理 - 获取有审批权限的人
    $.ajax({
        url:"../approval/getOptionalUser.do" ,
        type:"post" ,
        dataType:"json" ,
        success:function (data) {
            chargePersons = data ;
        } ,
        error:function () {

        }
    });
    $.ajax({
        url:"../approval/getOptionalUser1.do" ,
        type:"post" ,
        dataType:"json" ,
        success:function (data) {
            chargePersons1 = data ;
        } ,
        error:function () {

        }
    });
    // 切换二级菜单   
    $(".ty-secondTab li").click(function (index) {
        $(".ty-secondTab li").removeClass("ty-active") ;
        $(this).addClass("ty-active");
        var index = $(this).index();
        $(".ty-mainData .tab").addClass("hd");
        $(".ty-mainData .tab").eq(index).removeClass("hd") ;
    }) ;
    // creator : 侯杏哲 2017-05-15  请求处理 新增审批人
    $("#addItem2").on("click",function () {
        var level = ++chargeLevel ;    // 审批级别新增
        if(level > 8){ 
            bounce_Fixed.show( $("#secdTip") ) ; $("#secdtipMs").html("最大审批级别为八级，不可再继续添加审批级别！") ;   return false ;
        }
        var str = "<div class='trList'>" +
            "<div class='trItem2' level='"+ level +"'>" +
                "<span class='ttl2'>第"+ NumberToChinese(level) +"级审批</span>" +
                "<span class='con2 add-person' onclick='chooseApproveP($(this))'>选择审批人</span>" +
                "<span class='deleteItem' onclick='deleteItem($(this))'>—</span>" +
                "<div class='hd'></div>" +
            "</div>" +
        "</div> " ;
        var dateStr = "<div class='trItem2' level='1'>" +
            "<span class='ttl2'>"+ NumberToChinese( level-1 ) +"级审批时长</span>" +
            "<input class='con2' placeholder='请输入审批时长' />" +
        "</div>" ;
        $("#approveList").children(".trList:last-child").append( dateStr ) ;
        $("#approveList").append(str) ;
        $(".deleteItem").attr("style" , "display:inline-block") ;
    }) ;

}) ;


//  creator: 侯杏哲 2017-05-10  常规权限 财务审批 - 更换审批方式
function changeMethod(obj){
    var val = obj.val() ;
    $("#myModal_finance").find("input").val("");
    switch (Number(val)){
        case 1 : 
            $("#method_1").show().siblings().hide();
            break ; 
        case 2 :
            $("#changeMethod2_person").html(getPersonStr(chargePersons , 2));
            $("#method_2").show().siblings().hide();
            break ; 
        case 3 :
            $("#changeMethod3_person2").html(getPersonStr(chargePersons , 3));
            $("#method_3").show().siblings().hide();
            break ;
        default :
    }
}
// creator: 张旭博 2017-05-10  更换 项目开发 - 审批方式
function levelChange(selector) {
    var level = selector.val() ;
    addLevelElement(level);
}
function addLevelElement(level,toUserId) {
    $(".bounce-changeState").find("input").val("");
    $(".bounce-changeState .approval").html("");
    var PersonStr2 = getPersonStr(chargePersons1 , 2);
    var PersonStr3 = getPersonStr(chargePersons1 , 3);
    var approveStr = "";
    switch (Number(level)){
        case 1 :
            approveStr +=   '<div class="trItem"><span class="ttl">审批者</span>'+
                '<span class="con">'+
                '<select class="conInput">'+
                PersonStr2+
                '</select>'+
                '</span></div>';
            $(".approval").append(approveStr);
            break ;
        case 2 :
            approveStr +=   '<div class="trItem"><span class="ttl">最终审批者</span>'+
                '<span class="con">'+
                '<select class="conInput">'+
                PersonStr3+
                '</select>'+
                '</span></div>'+
                '<div class="trItem"><span class="ttl">直接审批者</span>'+
                '<span class="con">'+
                '<span class="conInput" style="margin-left:-4px; ">直接上级</span>'+
                '</span></div>';
            $(".approval").append(approveStr);
            break ;
        default :
    }
    $(".approval option[value="+toUserId+"]").attr("selected", true);
}
// creator: 侯杏哲 2017-05-10 保存财务审批-审批方式 
function saveChargeMethod(){
    var level = $("#changeMethod").val() ;
    var data = null ;
    var formOK = 1 ;
    switch ( Number(level) ){
        case 1 :
            data = { "level":level } ;
            break ;
        case 2 :
            var userId = $("#changeMethod2_person").val();
            var amountCeiling = $("#amountCeiling").val();
            if($.trim(amountCeiling) == "" || userId == ""){ formOK = 0 ;   }
            data = { "level":level , "userId":userId , "amountCeiling": amountCeiling  } ;
            break ;
        case 3 :
            // userId 次级审批者（正常传被选择者的id）   amountCeiling 直接上级金额上限  secondaryAmountCeiling次级金额上限。
            var userId = $("#changeMethod3_person2").val();
            var amountCeiling = $("#amountCeiling2").val();
            var secondaryAmountCeiling = $("#secondaryAmountCeiling").val();
            if($.trim(amountCeiling) == "" || userId == "" || $.trim(secondaryAmountCeiling) == "" ){ formOK = 0 ;   }
            data = { "level":level , "userId":userId , "amountCeiling": amountCeiling , "secondaryAmountCeiling":secondaryAmountCeiling } ;
            break ;
    }
    if( formOK == 0 ){
        $("#tp").html("请将以上数据补充完整！");
        return false ;
    }
    $.ajax({
        url:"../approval/updateReimbursementApprovalProcess.do" ,
        data:data ,
        type:"post" ,
        dataType:"json" ,
        success:function (data) {
            var status = data["status"] ;
            if(status == 1){ bounce.show($("#bounce_success")) }
            else if(status == 2){ $("#tp").html("当前审批流程中，还有待处理的申请，不能变更审批流程！");   }
            else{  $("#tp").html("保存失败！");  }
        },
        error:function () {  }
    });
}
// creator : 张旭博，2017-04-24 08:43:22， 保存项目开发流程
var currentRequests = 0;
function saveProApprove(){
    currentRequests ++;
    var level = $(".bounce-changeState .level").val();
    var currentLevel = $(".bounce-changeState .approvalLevel").attr("currentLevel");
    var proType = $(".bounce-changeState .approvalLevel").attr("proType");
    var userId = $(".bounce-changeState .trItem:first").find(".conInput:first").val();
    var data = {
        "phrase":proType,
        "level":level,
        "currentLevel":currentLevel,
        "userId":userId
    };
    if(currentRequests == 1){
        $.ajax({
            url:"../approval/updateProjectApproval.do" ,
            data:data,
            type:"post" ,
            dataType:"json" ,
            success:function (data) {
                var status = data["status"] ;
                if(status == 1){ bounce.cancel();   bounce.show($("#bounce_success"));  }
                else if(status == 0){ $(".tp").html("当前审批流程中，还有待处理的申请，不能变更审批流程！");   }
                else{  $(".tp").html("保存失败！");  }
                currentRequests = 0
            },
            error:function () {
                currentRequests = 0
            }
        });
    }
    
}
/* creator：张旭博，2017-04-24 08:43:22，更改我的请求审批流程 */
/* updator：侯杏哲，2017-04-24 08:43:22，  */
var chargeLevel = 0 ; // 我的请求，当前已经选择的审批级别
var curEditType = {} ; // 我的请求， type 当前变更权限的类型："leave" : 请假 ， "overtime" : 加班
function requestSet( type , itemId , obj  ) {
    // 清除其他审批对请求申请的影响
    $("#myApplyCon").children(":gt(0)").remove() ; $(".tp").html("");
    // 进行逻辑处理
    $("#beforeTip").html("") ;
    var usertype = chargeRole("总务");
    if (!usertype) {
        $("#Tip #tipMs").html("您没有此权限！") ;
        bounce.show($("#Tip")); return false ;
    }
    curEditType["type"] = type ;
    curEditType["itemId"] = itemId ;
    curEditType["obj"] = obj ;
    bounce.show($("#myApply")) ;
    $.ajax({
        url: "../approval/getLeaveOutTimeLevelList.do" ,
        data : { "itemId" : itemId  } ,
        type:"post" ,
        dataType:"json" ,
        success:function( data ){
            var status = data["status"] ;
            if( status == 1){
                var approvalItem = data["approvalItem"] ; // status 1-需审批 ，2-不需审批
                if( approvalItem["status"] == 1){ // 需要审批
                    var approvalFlowList = data["approvalFlowList"] ;
                    var str = "" ;
                    chargeLevel = approvalFlowList.length ;
                    if(approvalFlowList && approvalFlowList.length > 0){
                        for(var i=0 ; i<approvalFlowList.length ; i++ ){
                            var level = i+1 ;
                            if( approvalFlowList[i]["toUserId"] == 0  ){
                                str += "<div class='trList'>" +
                                    "<div class='trItem2' level='"+ level +"'>" +
                                    "<span class='ttl2'>第"+ NumberToChinese(level) +"级审批</span>" +
                                    "<span class='con2 add-person' onclick='chooseApproveP($(this))'>"+ approvalFlowList[i]["toUser"] +"</span>" +
                                    "<span class='deleteItem' onclick='deleteItem($(this))'>—</span>" +
                                    "<div class='hd'>"+ approvalFlowList[i]["toUserId"] +"</div>" +
                                    "</div>" ;
                            }else{
                                str += "<div class='trList'>" +
                                    "<div class='trItem2' level='"+ level +"'>" +
                                    "<span class='ttl2'>第"+ NumberToChinese(level) +"级审批</span>" +
                                    "<span class='con2 add-person' onclick='chooseApproveP($(this))'>"+ approvalFlowList[i]["userName"] +"</span>" +
                                    "<span class='deleteItem' onclick='deleteItem($(this))'>—</span>" +
                                    "<div class='hd'>"+ approvalFlowList[i]["toUserId"] +"</div>" +
                                    "</div>" ;
                            }

                            if( i != (approvalFlowList.length-1) ){
                                str += "<div class='trItem2' level='1'>" +
                                    "<span class='ttl2'>"+ NumberToChinese( level  ) +"级审批时长</span>" + 
                                    "<input class='con2' placeholder='请输入审批时长' value='"+ approvalFlowList[i]["amountCeiling"] +"' />" +
                                    "</div>" ;
                            }
                            str += "</div> " ;
                        }
                    }else{
                        str += "<div class='trList'>" +
                            "<div class='trItem2' level='1'>" +
                            "<span class='ttl2'>第一级审批</span>" +
                            "<span class='con2 add-person' onclick='chooseApproveP($(this))'>选择审批人</span>" +
                            "<span class='deleteItem' onclick='deleteItem($(this))'>—</span>" +
                            "<div class='hd'></div>" +
                            "</div>" ;
                    }
                    $("#approveList").html( str ) ;
                    var newlen = $("#approveList").children().length ;
                    if( newlen > 1  ){
                        $(".deleteItem").attr("style","display:inline-block;");
                    }else{
                        $(".deleteItem").hide();
                    }
                    $("#myApplyCon").show() ;
                    // 给下拉框赋值 
                    $("#isCharge").val("1").html("需要审批") ;
                    $("#isCharge").parent().next().children().each(function(){
                        if( $(this).val() == "1" ){
                            $(this).addClass("ty-active").siblings().removeClass("ty-active") ;
                        }
                    })
                    
                }else{ // 不需要审批
                    $("#myApplyCon").hide() ;
                    $("#isCharge").html("不需要审批").val("0").parent().next().children(".ty-opItem").each(function(){
                        if( $(this).val() == "0" ){
                            $(this).addClass("ty-active").siblings().removeClass("ty-active") ;
                        }
                    }) ;

                }
            }else{ // 获取信息失败
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("获取信息失败！") ;
            }
        } ,
        error:function(){}
    }) ;
}
//  creator: 侯杏哲 2017-05-10  更改财务审批流程,   
var chargePersons = null ;
var chargePersons1 = null ;
function finananceSet() {
    var usertype = chargeRole("总务");
    if (!usertype) {
        $("#Tip #tipMs").html("您没有此权限！");
        bounce.show($("#Tip"));
    }else{
        // $("#myModal_finance").modal("show");
        bounce.show($("#myModal_finance"));
        $("#myModal_finance").find("input").val("");
        $("#tp").html("");
        // 修改的时候默认一级审批（超管）
        // $("#method_1").show().siblings().hide();  $("#changeMethod").val("1");
        // 获得当前的审批方式
        $.ajax({
            url:"../approval/getCurrentApproval.do",
            data:{} ,
            type:"post",
            dataType:"json",
            success:function (data) {
                var status = data["status"] ;
                if(status == 1){
                    var approvalFlowList = data["approvalFlowList"] ;
                    if( approvalFlowList ){
                        var level = approvalFlowList.length;
                        $("#changeMethod").val(level);
                        if(level == 1){ $("#method_1").show().siblings().hide(); }
                        if(level == 2){
                            $("#changeMethod2_person").html(getPersonStr(chargePersons , 2));
                            $("#method_2").show().siblings().hide(); $("#changeMethod").val("2").html("二级审批");
                            for(var i=0; i < approvalFlowList.length ; i++ ){
                                if( approvalFlowList[i]["level"] == 1  ){ // 直接审批人
                                    $("#changeMethod2_person").val( approvalFlowList[i]["toUserId"] ) ; // 直接审批人
                                    $("#amountCeiling").val( approvalFlowList[i]["amountCeiling"] ) ; // 直接审批人的金额上限
                                }
                            }
                        }
                        if(level == 3){
                            $("#changeMethod3_person2").html(getPersonStr(chargePersons , 3));
                            $("#method_3").show().siblings().hide(); $("#changeMethod").val("3").html("三级审批");
                            for(var i=0; i < approvalFlowList.length ; i++ ){
                                if( approvalFlowList[i]["level"] == 1  ){ // 直接审批人
                                    $("#amountCeiling2").val( approvalFlowList[i]["amountCeiling"] ) ; // 直接审批人的金额上限
                                }
                                else if( approvalFlowList[i]["level"] == 2  ){ // 次级审批人
                                    $("#changeMethod3_person2").val( approvalFlowList[i]["toUserId"] );  // 次级审批人呢以及金额上限
                                    $("#secondaryAmountCeiling").val( approvalFlowList[i]["amountCeiling"] );
                                }
                            }

                        }
                    }

                }else{
                    $("#changeMethod").val("1");
                    $("#method_1").show().siblings().hide();
                    $("#Tip #tipMs").html("获取当前审批详情失败！");
                    bounce.show($("#Tip"));
                }

            },
            error:function () {
                $("#Tip #tipMs").html("连接错误！");
                bounce.show($("#Tip"));
            }
        });
    }
    


}
//  creator : 张旭博，2017-04-24 08:43:22，更改项目立项审批流程,
function establishmentSet() {
    var usertype = chargeRole("总务");
    if (!usertype) {
        $("#Tip #tipMs").html("您没有此权限！") ;
        bounce.show($("#Tip"));
    }else{
        var proType = "项目立项";
        $.ajax({
            url:"../approval/getCurrentProject.do",
            data:{"project":proType} ,
            type:"post",
            dataType:"json",
            success:function (data) {
                var status = data["status"] ;
                if(status == 1){
                    var toUserId = data["approvalFlow"].toUserId ;
                    var currentLevel = data["approvalFlow"].level;
                    if(toUserId || toUserId==0){
                        bounce.show($(".bounce-changeState"));
                        $(".bounce-changeState").find("input").val("");
                        $(".bounce-changeState .approvalLevel").attr("currentLevel",currentLevel);
                        $(".bounce-changeState .approvalLevel").attr("proType",proType);
                        addLevelElement(currentLevel,toUserId);
                        $(".approvalLevel .level").val(currentLevel);
                        $(".tp").html("");
                    }
                }
            },
            error:function () {
                $("#Tip #tipMs").html("连接失败！")
                bounce.show($("#Tip"));
            }
        });
    }

    
    // 获得当前的审批方式


}
//  creator : 张旭博，2017-04-24 08:43:22，更改项目开发审批流程,
function developmentSet() {
    var usertype = chargeRole("总务");
    if (!usertype) {
        $("#Tip #tipMs").html("您没有此权限！")
        bounce.show($("#Tip"));
    }else{
        var proType = "项目开发";
        $.ajax({
            url:"../approval/getCurrentProject.do",
            data:{"project":proType} ,
            type:"post",
            dataType:"json",
            success:function (data) {
                var status = data["status"] ;
                if(status == 1){
                    var toUserId = data["approvalFlow"].toUserId ;
                    var currentLevel = data["approvalFlow"].level;
                    if(toUserId || toUserId==0){
                        bounce.show($(".bounce-changeState"));
                        $(".bounce-changeState").find("input").val("");
                        $(".bounce-changeState .approvalLevel").attr("currentLevel",currentLevel);
                        $(".bounce-changeState .approvalLevel").attr("proType",proType);
                        addLevelElement(currentLevel,toUserId);
                        $(".approvalLevel .level").val(currentLevel);
                        $(".tp").html("");
                    }
                }else{
                    $("#Tip #tipMs").html("连接失败！")
                    bounce.show($("#Tip"));
                }
            },
            error:function () {
                $("#Tip #tipMs").html("连接失败！")
                bounce.show($("#Tip"));
            }
        });
    }

}
 // creator : 侯杏哲，2017-04-24 08:43:22，获得可以审批的人员
function getPersonStr(data , num) { // num 用于区分是方式2 ， 还是方式3 
    var status = data["status"] ;
    var userList = data["userList"] ;
    var str = "<option value='0'>直接上级</option>" ;
    if(num == 3){  str = "" ;  }
    if( Number(status) == 1 ){ // 除了直属上级，还有别人
        if(userList && userList.length > 0 ){
            for( var i=0 ; i < userList.length ; i++ ){
                str += "<option value='"+ userList[i]["userID"] +"'>"+ userList[i]["userName"] +"</option>" ;
            }
        }
    }
    return str ; 
}
// creator ： 侯杏哲 2017-05-11  常规权限 - 移除角色
var editObj = {} ; // 常规权限 - 移除角色的对象
function deleteRole( roleID , roleName , obj ){
    var usertype = chargeRole("总务");
    if (!usertype) {
        $("#Tip #tipMs").html("您没有此权限！")
        bounce.show($("#Tip"));
    }else{
        if( 0 == roleID ){
            $("#tipMs").html("您尚未选择角色类型，无法进行删除操作！") ;  bounce.show( $("#Tip") ) ;
        }
        else{
            editObj["obj"] = obj ; editObj["roleID"] = roleID ; editObj["roleName"] = roleName ;
            $("#delTip").html("确定移除角色：" + roleName) ;  bounce.show( $("#delRole") ) ;
        }
    }
    
}
// creator ： 侯杏哲 2017-05-11 常规权限 - 确定移除角色
function delRoleSubmit(){
    $.ajax({
        url:"../sys/deleteRole.do" ,
        data: { "roleID" :  editObj["roleID"]  } ,
        type:"post" ,
        success:function( data ){
            if(parseInt(data) === 1){
                bounce.cancel() ;
                $("#tipMs").html(" 删除成功 ！") ;  bounce.show( $("#Tip") ) ;
                editObj["obj"].parent().parent().remove() ;
            }else if(parseInt(data) === 0){
                $("#tipMs").html(" 删除失败 ！") ;  bounce.show( $("#Tip") ) ;
            }
        } ,
        error:function(){
            $("#tipMs").html(" 网络连接错误 ！") ;  bounce.show( $("#Tip") ) ;
        }
    });
}
// creator ： 侯杏哲 2017-05-11 常规权限 - 新增角色
function addRoleBtn(){
    var usertype = chargeRole("总务");
    if (!usertype) {
        $("#Tip #tipMs").html("您没有此权限！")
        bounce.show($("#Tip"));
    }else {
        bounce.show($("#editRole"));
        $("#roleName").val("");
    }
}
// creator ： 侯杏哲 2017-05-11 常规权限 - 确定新增角色
function editRoleSubmit(){
    var roleName = $("#roleName").val() ;
    if( $.trim(roleName) == "" ){
        $("#editTip").html("角色名称不能够为空！") ; return false ;
    }
    $.ajax({
        url : "../sys/addRole.do"  ,
        data : { "roleName": roleName } ,
        type:"post" ,
        success:function(data){
            location.href="" ;
        } ,
        error:function(data){
            location.href="" ;
        }
    }) ;
}
// creator ： 侯杏哲 2017-05-11 权限分配 - 变更角色
var changeRoleObj = null ;
function changeRole( selectOption ){
    if(hasAuthority(0) === true){
        $("#Tip #tipMs").html("您没有此权限！");
        bounce.show($("#Tip"));
    }else{
        var valStr = selectOption.val() ;
        var valObj =  valStr.split(",") ;
        var id = valObj[0] ;
        var roleId = valObj[1] ;
        $("#confirmType").html("changeRole");
        $("#confirMs").html("确定变更该用户的角色？");
        bounce.show( $("#confirm") ) ;
        changeRoleObj = { "id":id , "roleId":roleId  } ;
    }
}
// creator ： 侯杏哲 2017-05-11 权限分配 - 确定变更角色
function changeRoleOk(){
    $.ajax({
        url:"../sys/changeRole.do",
        data:{ "roleID":changeRoleObj["roleId"] , "id":changeRoleObj["id"]    } ,
        success:function(){
            $("#tipMs").html("修改成功") ;
            bounce.show( $("#Tip") ) ;
        },
        error:function(){
            $("#tipMs").html("修改失败！") ;
            bounce.show( $("#Tip") ) ;
        }
    }).always(function(){
        location.href = "../sys/toApproveIndex.do" ;
    });
}
// creator ：侯杏哲 2017-05-11  confirm 框的取消
function confirCancel(){
    var confirmType = $("#confirmType").html() ;
    switch( confirmType){
        case "changeRole" :  // 权限分配 - 确定变更角色
            location.href = "../sys/toApproveIndex.do" ;  break ;
        default:
    }
}
// creator ：侯杏哲 2017-05-11  confirm 框的确定
function confirOK(){
    var confirmType = $("#confirmType").html() ;
    switch( confirmType){
        case "changeRole" :  // 权限分配 - 确定变更角色
            changeRoleOk() ; break ;
        default:
    }
}
// creator : 侯杏哲 2017-05-15  我的请求 - 请假和加班 切换需不需要审批 
function upChargeLevel(obj){
    var val = obj.val() ;
    if(val == 1){
        $("#myApplyCon").show();
        var str = "<div class='trList'>" +
            "<div class='trItem2' level='1'>" +
                "<span class='ttl2'>第一级审批</span>" +
                "<span class='con2 add-person' onclick='chooseApproveP($(this));' >选择审批人</span>" +
                "<span class='deleteItem' onclick='deleteItem($(this))'>—</span>" +
                "<div class='hd'></div>" +
            "</div>" +
        "</div>" ;
        $("#approveList").html( str ) ;
        $(".deleteItem").hide() ;
        chargeLevel = 1 ;
    }else{
        $("#myApplyCon").hide();
        $("#approveList").html("") ;
    }
}
// creator： 张旭博  2017-05-05  我的请求 - 选择审批人 
// updator : 侯杏哲 2017-05-13   添加交互处理 获取机构一级机构下的部门和人员
var curSelectObj = null ; // 我的请求 - 当前正在选择审批人的对象
function chooseApproveP( obj ) {
    curSelectObj = obj ;
    $("#approveTree").html( "" ) ;
    $("#beforeTip").html( "" ) ;
    bounce_Fixed.show($("#chooseApproveP"));
    $.ajax({
        url: "../lo/getOrgAndUser.do" ,
        type:"post" ,
        dataType:"json" ,
        success:function( data ){
            var org = data["org"] ;  //  机构信息
            var orgList = data["orgList"] ;  // 机构下的部门信息
            var users = data["users"] ;   //  机构下的用户信息
            var str = "" ;
            if(org){
                str += "<li> <div class='approvePItem' onclick='showOrg( $(this) )'><i class='fa fa-angle-right'></i>"+ org["name"] +"</div>" +
                            "<div class='hd'>"+ JSON.stringify(org) +"</div>" ;
                if( orgList && orgList.length > 0  ){
                    str += "<ul class='level level2 hd'>";
                   /* for(var j = 0 ; j < users.length ; j++ ){
                        str += "<li class='approvePItem'>" +
                            /!* "<div class='hd'>"+ JSON.stringify(users[j]) +"</div>" +   *!/
                            "<input type='radio' name='approveP' value='"+ users[j]["userID"] +"' />"+ users[j]["userName"] +"</li>"  ;
                    }*/
                    for(var i = 0 ; i < orgList.length ; i++ ){
                        str += "<li>" +
                            "<div class='approvePItem' onclick='showPerson($(this) , "+ orgList[i]["id"] +")'><i class='fa fa-angle-right'></i>"+ orgList[i]["name"] +"</div>" +
                            /*"<div class='hd'>"+ JSON.stringify(orgList[i]) +"</div>" +*/
                            "</li>" ;
                    }
                    str += "</li>" ;
                }
            }
            $("#approveTree").html( str ) ;
        } ,
        error:function(){
            $("#Tip #tipMs").html("连接失败！");
            bounce.show($("#Tip"));
        }
    }) ;
}
// creator: 侯杏哲 2017-05-16 请求处理 - 选择审批人 - 点击部门名称展示部门下的人员
function showOrg( obj ){
    obj.children().prop("checked",true);
    obj.siblings("ul.level").toggleClass("hd");
    if( obj.find("i").hasClass("fa-angle-right") ){
        obj.find("i").removeClass("fa-angle-right") ;
        obj.find("i").addClass("fa-angle-down") ;
    }else{
        obj.find("i").removeClass("fa-angle-down") ;
        obj.find("i").addClass("fa-angle-right") ;
    }
}
// creator: 侯杏哲 2017-05-15 请求处理 - 选择审批人 - 点击部门名称 获取并展示 部门下的人员
function showPerson( obj , id){
    var departmentID = id ;
    $.ajax({
        url:"../lo/getOrgByPid.do" ,
        data:{ "department": departmentID  } ,
        type:"post" ,
        dataType:"json" ,
        success:function( data ){
            var orgList = data["orgList"] ;
            var users = data["users"] ;
            var str = "<ul class='level level3'>" ;
            for(var i = 0 ; i < users.length ; i++ ){
                str += "<li class='approvePItem' ><input type='radio' value='"+ users[i]["userID"] +"' name='approveP'/>"+ users[i]["userName"] +"</li>" ;
            }
            for(var j = 0 ; j < orgList.length ; j++ ){
                str += "<li>" +
                    "<div class='approvePItem' onclick='showPerson($(this) , "+ orgList[j]["id"] + ")'><i class='fa fa-angle-right'></i>"+ orgList[j]["name"] +"</div>" +
                    "</li>" ;
            }
            str += "</ul>" ;
            obj.after(str) ;
            obj.attr("onclick","showOrg( $(this) )")
        } ,
        error:function(){  }
    });
    showOrg( obj ) ;
}
// creator : 侯杏哲 2017-05-15 我的请求 - 更改状态 - 删除审批级别7
function deleteItem(obj){
    --chargeLevel ; 
    var tr = obj.parent().parent().remove() ;
    var trArr = $("#approveList").children() ;
    trArr.each(function(){
        var _this = $(this) ;
        _this.children(":eq(0)").children(".ttl2").html("第"+ NumberToChinese(  _this.index()+1 ) +"级审批") ;
        if(_this.index() == (trArr.length -1) ){
            _this.children(":eq(1)").remove() ;
        }else{
            _this.children(":eq(1)").children(".ttl2").html(  NumberToChinese( _this.index()+1 ) +"级审批时长") ;
        }
    }) ;
/*    for(var i = 0 ; i < trArr.length ; i++ ){
        var _this = trArr[i] ;
        _this.children(":eq(0)").children(".ttl2").html("第"+ NumberToChinese( i+1 ) +"级审批") ;
        if(i == (trArr.length -1) ){
            _this.children(":eq(1)").remove() ;
        }else{
            _this.children(":eq(1)").children(".ttl2").html(  NumberToChinese( i+1 ) +"级审批时长") ;
        }
    }*/

}
//  creator : 张旭博  2017-05-15  我的请求工具方法 - 阿拉伯数字转中文数字
var chnNumChar = ["零","一","二","三","四","五","六","七","八","九"];  // 单个数字转换用数组实现
var chnUnitSection = ["","万","亿","万亿","亿亿"];                    // 节权位同样用数组实现
var chnUnitChar = ["","十","百","千"];                               // 节内权位同样用数组实现
// creator : 张旭博  2017-05-15  我的请求工具方法 - 节内转换算法：
function SectionToChinese(section){
    var strIns = '', chnStr = '';
    var unitPos = 0;
    var zero = true;
    while(section > 0){
        var v = section % 10;
        if(v === 0){
            if(!zero){
                zero = true;
                chnStr = chnNumChar[v] + chnStr;
            }
        }else{
            zero = false;
            strIns = chnNumChar[v];
            strIns += chnUnitChar[unitPos];
            chnStr = strIns + chnStr;
        }
        unitPos++;
        section = Math.floor(section / 10);
    }
    return chnStr;
}
//   creator : 张旭博  2017-05-15  我的请求工具方法 -   转换算法主函数
function NumberToChinese(num){
    var unitPos = 0;
    var strIns = '', chnStr = '';
    var needZero = false;
    if(num === 0){
        return chnNumChar[0];
    }
    while(num > 0){
        var section = num % 10000;
        if(needZero){
            chnStr = chnNumChar[0] + chnStr;
        }
        strIns = SectionToChinese(section);
        strIns += (section !== 0) ? chnUnitSection[unitPos] : chnUnitSection[0];
        chnStr = strIns + chnStr;
        needZero = (section < 1000) && (section > 0);
        num = Math.floor(num / 10000);
        unitPos++;
    }

    return chnStr;
}
// creator : 侯杏哲 2017-05-16 我的请求，变更审批级别 - 确定选择审批人
function selectOK(){
    var personObj = $("input[name='approveP']:checked") ;
    var personID = personObj.val() ;
    var personName = personObj.parent().text() ;
    var isOk = 1 ; // 标记有没有相同
    $("#approveList").children().each(function(){
        var userID = $(this).children(":eq(0)").children(".hd").html() ;
        if( personID == userID ){  isOk = 0 ; return false ;    }
    }) ;
    if(isOk == 0 ){  $("#tp2").html("请不要选择重复的审批人") ;   return false ;  }
    curSelectObj.html( personName ) ;
    curSelectObj.siblings(".hd").html( personID ) ;
    bounce_Fixed.cancel() ;
}
// creator: 侯杏哲 2017-05-17 我的请求 - 保存更改的审批权限
function changeMyApplyAuthz(){
    var chargeList = [] ; // 存选择的审批人和时长
    var postData = {} ;
    var isOk = 1 ;  // 标记数据是否合法  1：合法 ； 0 ：不合法
    var levelCount = $("#approveList").children().length ;
    // alert(levelCount) ;
    $("#approveList").children().each(function(){
        var len = $(this).children().length ;
        var approveJson  = {} ;
        var userID = $(this).children(":eq(0)").children(".hd").html() ;
        if( userID == "" ){
            $("#beforeTip").html("请选择第"+ NumberToChinese( $(this).index()+1 ) +"级审批人") ;
            isOk = 0 ; return false ;
        }
        approveJson["userId"] = userID ;
        var secdObj = $(this).children(":eq(1)") ;
        if( len == 2 ){ // 有时间的就读取
            var hours = secdObj.children("input.con2").val() ;
            if( isNaN( Number(hours) ) || Number(hours)==0 ){  $("#beforeTip").html("请填写第"+ NumberToChinese( $(this).index()+1 ) +"级的审批时长") ; isOk = 0 ; return false   }
            approveJson["amountCeiling"] = hours ;
        }
        chargeList.push(approveJson) ;
    }) ;
    if(isOk == 0){ return false ;   } // 校验数据不合法， 不能提交
    var type = curEditType["type"] , changeurl = ""  , itemId = curEditType["itemId"] ;
    var chargeStatus = $("#isCharge").val() ;
    postData["item"] = JSON.stringify(  { "itemId": itemId , "status": chargeStatus  } ) ;
    // alert( JSON.stringify( chargeList )  ) ;
    if(type == "leave" ){
        changeurl ="../approval/updateLeaveApprovalFlow.do" ;
        postData["levelList"] =  JSON.stringify( chargeList )  ;
    }else if( type = "overTime" ){
        changeurl ="../approval/updateOutTimeApprovalFlow.do" ;
        postData["outTimeList"] = JSON.stringify( chargeList ) ;
    }
    $.ajax({
        url:changeurl ,
        data:   postData    ,
        type:"post" ,
        dataType:"json" ,
        success:function( data ){
            var status = data["status"] ;
            if( status == 0 ){
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("传值原因导致的变更失败！") ;
            }else if( status == 1 ){
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("变更成功！") ;
                var obj = curEditType["obj"] ;
                if(levelCount == 0){
                    obj.parent().prev().children(":eq(0)").html( "无需审批").attr("class" , "label label-sm label-success") ;
                    obj.parent().prev().children(":eq(1)").html(levelCount) ;
                }else{
                    obj.parent().prev().children(":eq(0)").html( NumberToChinese(levelCount) + "级审批").attr("class" , "label label-sm label-danger") ;
                    obj.parent().prev().children(":eq(1)").html(levelCount) ;
                }
            }else if( status == 2 ){
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("还有没处理完的申请！") ;
            }else if( status == 3 ){
                bounce.show( $("#Tip") ) ;
                $("#tipMs").html("有重复的审批人！") ;
            }
        } ,
        error:function(){}
    });

}










