//creater:王静 2017-07-27 11:29:30 企业信息主页面
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#editRentPay"));
bounce_Fixed3.show($("#rentSchedule"));
bounce_Fixed2.cancel();
bounce_Fixed3.cancel();
$(function(){
    // getBusiness();
    startmess();
    initUpload($("#filePic"),'img');
    initUpload($("#filePic1"),'img');
    initUpload($("#filePic2"),'doc');
    // 右侧标签页切换点击事件
    $("body").on("click", '.thin-btn, .nodeBtn', function () {
        var name = $(this).data("fun");
        switch(name) {
            case 'updateName':
                // let fullName = $("#fullName").html();
                // let name = $("#name").html();
                // $("#changeBusinessName input").eq(0).val(fullName);
                // $("#changeBusinessName input").eq(1).val(name);
                bounce.show($("#changeBusinessName"));
                break;
            case 'setmamng':
                //console.log('16:31');
                let fullName = $("#fullName").html();
                let name = $("#name").html();
                $("#institmangent input").eq(0).val(fullName);
                $("#institmangent input").eq(1).val(name);
                bounce.show($("#institmangent"));
                break;
            case 'updateNameLog':
                updateNameLog(1, 20)
                break;
            case 'nameEditLogScan':
                updateNameLogScan($(this));
                break;
            case 'siteInfo':
                $("#Leader").html("编辑该场地的当前信息");
                $("#siteInfoder").html("机构地址");
                $(".jgone").show();
                $(".qtone").hide();
                $(".jgtwo").show();
                $(".qttwo").hide();
                $(".jgthree").show();
                $(".qtthree").hide();
                $("#stituress").show();
                $("#otheraddresssure").hide();
                $("#sysaddresssure").hide();
                $("#othersure").hide();
                $("#othernewsure").hide();
                siteInfo();
                break;
            case 'editSite':
                $("#jgby").hide();
                $("#qtby").hide();
                $("#editSite").data("source", 1);
                $("#Leader").html("编辑当前场地信息");
                let data = JSON.parse($("#siteStorage").html());
                let info = data.orgSite;
                $("#otherPayBox").html("");
                $(".categoryGroup").html("");
                $("#editSite .part").hide();
                $("#editSite input").val("");
                $("#editSite select").val("");
                $(".part0").removeClass("low-line");
                addTaxCategory();
                $("#editSite").find(".file-box").html('<span class="inTip">请上传</span>');
                if (!info) {
                    $("#siteConditions").prop("disabled", false);
                } else {
                    let rentImageList = data.rentImageList, conImageList = ``;
                    let rentInitialList = data.rentInitialList; //水电煤气的列表
                    let siteTaxList = data.siteTaxList, siteTaxStr = ``; //税费列表
                    let siteImageList = data.siteImageList;
                    let rentChargeList = data.rentChargeList;
                    let siteRent = data.siteRent;//租赁合同
                    let imageList = ``;
                    $("#editSite .part0 [require]").each(function (){
                        let name = $(this).attr("name");
                        if (name === 'totalArea' || name === 'buildingArea') {
                            $(this).val(Number(info[name]).toFixed(2));
                        } else {
                            $(this).val(info[name]);
                        }
                    });
                    for(let i=0; i<siteImageList.length;i++){
                        imageList +=`<div class="imgsthumb">
                                <a class="filePic" data-type="1" path="${siteImageList[i].uplaodPath}" onclick="seeOnline($(this))">${siteImageList[i].orders}</a>
                                <i class="fa fa-times" onclick="cancleThis($(this))"></i>
                            </div>`;
                    }
                    if (siteImageList.length > 0){
                        $("#imageList").siblings().find(".clearPicsBtn").show();
                    }
                    $("#imageList").html(imageList);
                    for(let i=0; i<siteTaxList.length;i++){
                        if(i !== 0) addTaxCategory();
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=category]").val(siteTaxList[i].category);
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=amount]").val(Number(siteTaxList[i].amount).toFixed(2));
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=payBeginTime]").data('val',new Date(siteTaxList[i].payBeginTime).format('yyyy-MM-dd'));
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=payBeginTime]").val(new Date(siteTaxList[i].payBeginTime).format('MM-dd'));
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=payEndTime]").val(new Date(siteTaxList[i].payEndTime).format('MM-dd'));
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=payEndTime]").data('val',new Date(siteTaxList[i].payEndTime).format('yyyy-MM-dd'));
                    }
                    if (info.ownership === "1") {
                        $("#editSite .part2").show();
                        $("#siteConditions").prop("disabled", true);
                        $(".part0").addClass("low-line");
                    } else if (info.ownership === "2") {
                        $("#editSite .part1").show();
                        $("#editSite .part3").show();
                        $(".part0,.part1,.part2").addClass("low-line");
                        $("#siteConditions").prop("disabled", true);
                        let imgs = ``, files = ``;
                        for(let i=0; i<rentImageList.length;i++){
                            if (rentImageList[i].type === '1') {
                                imgs +=`<div class="imgsthumb">
                                <a class="filePic" data-type="1" path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">${i+1}</a>
                                <i class="fa fa-times" onclick="cancleThis($(this))"></i>
                            </div>`;
                            } else {
                                rentImageList[i].filename = rentImageList[i].uplaodPath;
                                files +=`<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <i class="fa fa-times" onclick="cancleThis($(this))"></i>
                             <span class="hd">${ JSON.stringify(rentImageList[i]) }</span>
                        </span>`;
                            }
                        }
                        if (imgs !== ""){
                            $("#contractPic").siblings().find(".clearPicsBtn").show();
                        }
                        $("#contractPic").html(imgs);
                        $("#contractDoc").html(files);
                        if (siteRent) {
                            if (siteRent.hasTax === true) {$("#editSite .part2").show();}
                            $("#editSite .part1 [require]").each(function (){
                                let name = $(this).attr("name");
                                if (name === 'beginTime' || name === 'endTime') {
                                    $(this).val(new Date(siteRent[name]).format('yyyy-MM-dd'));
                                } else if (name === 'hasInvoice' || name === 'hasTax') {
                                    let lang = siteRent[name] === true? 1: 0;
                                    $(this).val(lang);
                                } else if (name === 'pledge' || name === 'amount') {
                                    $(this).val(Number(siteRent[name]).toFixed(2));
                                } else {
                                    $(this).val(siteRent[name]);
                                }
                            });
                        }
                        for(let i=0; i<rentInitialList.length;i++) {
                            if (rentInitialList[i].item === '水' || rentInitialList[i].item === '电' || rentInitialList[i].item === '煤气'){
                                $(".livingCost .costs:eq(0) input[name='amount']:eq("+ i +")").val(Number(rentInitialList[i].amount).toFixed(2));
                            } else {
                                let num = i - 3;
                                $(".livingCost .costs:eq(1) input[name='item']:eq("+ num +")").val(rentInitialList[i].item);
                                $(".livingCost .costs:eq(1) input[name='amount']:eq("+ num +")").val(Number(rentInitialList[i].amount).toFixed(2));
                            }
                        }
                        let rentChargeListData = [];
                        for(var i = 0; i<rentChargeList.length ;i++) {
                            let temp = {
                                item: rentChargeList[i].item,
                                amount: rentChargeList[i].amount,
                                deadline: rentChargeList[i].deadline
                            }
                            rentChargeListData.push(temp);
                        }
                        $("#otherPayBox").html(morePayStr(rentChargeListData));
                    }
                }
                $("#stituress").show();
                $("#otheraddresssure").hide();
                $("#sysaddresssure").hide();
                bounce_Fixed.show($('#editSite'));
                break;
            case 'editSiteq':
                $("#jgby").hide();
                $("#qtby").hide();
                $("#editSite").data("source", 5);
                $("#Leader").html("场地的当前信息");
                let dataq = JSON.parse($("#siteStorage").html());
                let infoq = dataq.orgSite;
                $("#otherPayBox").html("");
                $(".categoryGroup").html("");
                $("#editSite .part").hide();
                $("#editSite input").val("");
                $("#editSite select").val("");
                $(".part0").removeClass("low-line");
                addTaxCategory();
                $("#editSite").find(".file-box").html('<span class="inTip">请上传</span>');
                if (!infoq) {
                    $("#siteConditions").prop("disabled", false);
                }else{
                    let rentImageList = dataq.rentImageList, conImageList = ``;
                    let rentInitialList = dataq.rentInitialList; //水电煤气的列表
                    let siteTaxList = dataq.siteTaxList, siteTaxStr = ``; //税费列表
                    let siteImageList = dataq.siteImageList;
                    let rentChargeList = dataq.rentChargeList;
                    let siteRent = dataq.siteRent;//租赁合同
                    let imageList = ``;
                    $("#editSite .part0 [require]").each(function (){
                        let name = $(this).attr("name");
                        if (name === 'totalArea' || name === 'buildingArea') {
                            $(this).val(Number(infoq[name]).toFixed(2));
                        } else {
                            $(this).val(infoq[name]);
                        }
                    });
                    for(let i=0; i<siteImageList.length;i++){
                        imageList +=`<div class="imgsthumb">
                                <a class="filePic" data-type="1" path="${siteImageList[i].uplaodPath}" onclick="seeOnline($(this))">${siteImageList[i].orders}</a>
                                <i class="fa fa-times" onclick="cancleThis($(this))"></i>
                            </div>`;
                    }
                    if (siteImageList.length > 0){
                        $("#imageList").siblings().find(".clearPicsBtn").show();
                    }
                    $("#imageList").html(imageList);
                    for(let i=0; i<siteTaxList.length;i++){
                        if(i !== 0) addTaxCategory();
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=category]").val(siteTaxList[i].category);
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=amount]").val(Number(siteTaxList[i].amount).toFixed(2));
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=payBeginTime]").data('val',new Date(siteTaxList[i].payBeginTime).format('yyyy-MM-dd'));
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=payBeginTime]").val(new Date(siteTaxList[i].payBeginTime).format('MM-dd'));
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=payEndTime]").val(new Date(siteTaxList[i].payEndTime).format('MM-dd'));
                        $(".categoryGroup .categoryItem:eq("+ i +")").find("input[name=payEndTime]").data('val',new Date(siteTaxList[i].payEndTime).format('yyyy-MM-dd'));
                    }
                    if (infoq.ownership === "1") {
                        $("#editSite .part2").show();
                        $("#siteConditions").prop("disabled", true);
                        $(".part0").addClass("low-line");
                    }else if (infoq.ownership === "2") {
                        $("#editSite .part1").show();
                        $("#editSite .part3").show();
                        $(".part0,.part1,.part2").addClass("low-line");
                        $("#siteConditions").prop("disabled", true);
                        let imgs = ``, files = ``;
                        for(let i=0; i<rentImageList.length;i++){
                            if (rentImageList[i].type === '1') {
                                imgs +=`<div class="imgsthumb">
                                <a class="filePic" data-type="1" path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">${i+1}</a>
                                <i class="fa fa-times" onclick="cancleThis($(this))"></i>
                            </div>`;
                            }else{
                                rentImageList[i].filename = rentImageList[i].uplaodPath;
                                files +=`<span class="fileIm"  >
                                <span class="fa fa-file-word-o"></span>
                                <i class="fa fa-times" onclick="cancleThis($(this))"></i>
                                <span class="hd">${ JSON.stringify(rentImageList[i]) }</span>
                            </span>`;
                            }
                        }
                        if (imgs !== ""){
                            $("#contractPic").siblings().find(".clearPicsBtn").show();
                        }
                        $("#contractPic").html(imgs);
                        $("#contractDoc").html(files);
                        if (siteRent) {
                            if (siteRent.hasTax === true) {$("#editSite .part2").show();}
                            $("#editSite .part1 [require]").each(function (){
                                let name = $(this).attr("name");
                                if (name === 'beginTime' || name === 'endTime') {
                                    $(this).val(new Date(siteRent[name]).format('yyyy-MM-dd'));
                                } else if (name === 'hasInvoice' || name === 'hasTax') {
                                    let lang = siteRent[name] === true? 1: 0;
                                    $(this).val(lang);
                                } else if (name === 'pledge' || name === 'amount') {
                                    $(this).val(Number(siteRent[name]).toFixed(2));
                                } else {
                                    $(this).val(siteRent[name]);
                                }
                            });
                        }
                        for(let i=0; i<rentInitialList.length;i++) {
                            if (rentInitialList[i].item === '水' || rentInitialList[i].item === '电' || rentInitialList[i].item === '煤气'){
                                $(".livingCost .costs:eq(0) input[name='amount']:eq("+ i +")").val(Number(rentInitialList[i].amount).toFixed(2));
                            } else {
                                let num = i - 3;
                                $(".livingCost .costs:eq(1) input[name='item']:eq("+ num +")").val(rentInitialList[i].item);
                                $(".livingCost .costs:eq(1) input[name='amount']:eq("+ num +")").val(Number(rentInitialList[i].amount).toFixed(2));
                            }
                        }
                        let rentChargeListData = [];
                        for(var i = 0; i<rentChargeList.length ;i++) {
                            let temp = {
                                item: rentChargeList[i].item,
                                amount: rentChargeList[i].amount,
                                deadline: rentChargeList[i].deadline
                            }
                            rentChargeListData.push(temp);
                        }
                        $("#otherPayBox").html(morePayStr(rentChargeListData));
                    }
                }
                $("#stituress").hide();
                $("#otheraddresssure").hide();
                $("#sysaddresssure").hide();
                $("#othersure").show();
                $("#othernewsure").hide();
                bounce_Fixed.show($('#editSite'));
                break;
            case 'updateSite':
                $("#jgby").hide();
                $("#qtby").hide();
                let resq = JSON.parse($("#siteStorage").html());
                $(".part,.part0").removeClass("low-line");
                if (resq.orgSite) {
                    $("#editSite .part").hide();
                    $("#editSite").data("source", 2);
                    $("#otherPayBox").html("");
                    $(".categoryGroup").html("");
                    $("#editSite input").val("");
                    $("#editSite select").val("");
                    $("#siteConditions").prop("disabled", false);
                    $("#editSite").find(".file-box").html('<span class="inTip">请上传</span>');
                    $("#editSite").find(".file-box").siblings('.clearPicsBtn').hide();
                    addTaxCategory();
                    $("#stituress").show();
                    $("#otheraddresssure").hide();
                    $("#sysaddresssure").hide();
                    $("#othersure").hide();
                    $("#othernewsure").hide();
                    bounce_Fixed.show($('#editSite'));
                } else {
                    layer.msg("<p>操作失败！</p><p>因为系统里还没有场地信息！</p>");
                }
                break;
            case 'updateSiteq':
                $("#jgby").hide();
                $("#qtby").hide();
                let res = JSON.parse($("#siteStorage").html());
                $(".part,.part0").removeClass("low-line");
                if (res.orgSite) {
                    $("#editSite .part").hide();
                    $("#editSite").data("source", 6);
                    $("#Leader").html("场地的当前信息");
                    $("#otherPayBox").html("");
                    $(".categoryGroup").html("");
                    $("#editSite input").val("");
                    $("#editSite select").val("");
                    $("#siteConditions").prop("disabled", false);
                    $("#editSite").find(".file-box").html('<span class="inTip">请上传</span>');
                    $("#editSite").find(".file-box").siblings('.clearPicsBtn').hide();
                    addTaxCategory();
                    $("#stituress").hide();
                    $("#otheraddresssure").hide();
                    $("#sysaddresssure").hide();
                    $("#othersure").hide();
                    $("#othernewsure").show();
                    bounce_Fixed.show($('#editSite'));
                }else {
                    layer.msg("<p>操作失败！</p><p>因为系统里还没有场地信息！</p>");
                }
                break;
            case 'updateSiteLog':
                updateSiteLog(1, 20);
                break;
            case 'updateSiteLogScan':
                updateSiteLogScan($(this));
                break;
            // case 'updateSiteLogScanq':
            //     updateSiteLogScanq($(this));
            //     break;
            case 'editRentPay':
                $("#otherPay tbody tr:gt(0)").remove("");
                addLine();
                $("#editRentPay input").val("");
                $("#editRentPay select").val("");
                bounce_Fixed2.show($("#editRentPay"));
                break;
            // case 'addsyress':   //创建机构地址弹窗
            //     $("#jgby").show();
            //     $("#qtby").hide();
            //     $("#editSite").data("source",3);
            //     var orgSite =  $("#fixemal").data("orgSite2");
            //     if(orgSite !== null){
            //         layer.msg("机构地址已经创建了！");
            //         return false;
            //     }else{
            //         $("#Leader").html("创建机构地址");
            //         $("#otherPayBox").html("");
            //         $(".categoryGroup").html("");
            //         $("#editSite .part").hide();
            //         $("#editSite input").val("");
            //         $("#editSite select").val("");
            //         $(".part0").removeClass("low-line");
            //         addTaxCategory();
            //         $("#editSite").find(".file-box").html('<span class="inTip">请上传</span>');
            //         $("#stituress").hide();
            //         $("#otheraddresssure").hide();
            //         $("#sysaddresssure").show();
            //         $("#othersure").hide();
            //         $("#othernewsure").hide();
            //         bounce_Fixed.show($("#editSite"));
            //     }
            //     break;
            case 'addotheraddress': //创建其他地址弹窗
                $("#jgby").hide();
                $("#qtby").show();
                $("#editSite").data("source",4);
                var orgSite = $("#fixothermal").data("orgSite1");
                if(orgSite === null){
                    layer.msg("<p>机构地址尚未创建！</p>");
                    return false;
                }else{
                    $("#Leader").html("创建其他地址");
                    $("#otherPayBox").html("");
                    $(".categoryGroup").html("");
                    $("#editSite .part").hide();
                    $("#editSite input").val("");
                    $("#editSite select").val("");
                    $(".part0").removeClass("low-line");
                    addTaxCategory();
                    $("#editSite").find(".file-box").html('<span class="inTip">请上传</span>');
                    $("#stituress").hide();
                    $("#otheraddresssure").show();
                    $("#sysaddresssure").hide();
                    $("#othersure").hide();
                    $("#othernewsure").hide();
                    $("#siteConditions").prop("disabled", false);
                    bounce_Fixed.show($("#editSite"));
                }
                break;
        }
    })
    $(".clearInputVal").on({
        "mousedown": function () {
            $(this).prev().val("");
        },
        "mouseup": function () {
            if ($(this).siblings(".modItemTtl").find(".lenTip").length > 0) {
                $(this).siblings(".modItemTtl").find(".lenTip").html("0/100");
            }
            $(this).prev().get(0).focus();
        }
    });
    $("#up_cusName").on("blur", function () {
        var val = $(this).val()
        $(this).parents(".formContainer").find("input[name='name']").val(val.substring(0,6))
    })
    //获取企业信息地址数据
    getAddresLand(1,20);
});

//获取企业信息地址数据
function getAddresLand(currentPageNo,pageSize){}

function getBusiness() {
    $.ajax({
        url:"../business/getBusiness.do" ,
        data:{} ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            var res=data["org"];
            //获取后台信息
            var fullName=res["fullName"];
            var name=res["name"];
            var createDate=res["createDate"];
            var address=res["address"] === '' ? "信息尚未录入": res["address"];
            // var str = ``;
            // str = `<span>${createDate}</span>&nbsp;<span>创建</span>`;
            // $("#tidme").html(str);
            $("#fullName").html(fullName);
            $("#name").html(name);
            $("#comCreateDate").html(createDate);
            $("#address").html(address);
            $("#inforStorage").html(JSON.stringify(res));
        }
    }) ;
}

// creator: sy 2023-01-13 展示首页表格数据
function startmess(){
    $.ajax({
        url:"../site/getOrganizationInfo.do",
        data:{},
        type:"post",
        dataTye:"json",
        success:function(res){
            var lank = res.data ||  {};
            var fullName = lank.organization.fullName;  //机构名称
            var name = lank.organization.name;  //机构简称
            var createDate = lank.organization.createDate;  //  创建时间
            createDate = new Date(createDate).format("yyyy-MM-dd hh:mm:ss");
            var registeredAddress = lank.registeredAddress === "" || lank.registeredAddress === null ? "" : lank.registeredAddress;//注册地址
            var address = lank.organization.address === "" || lank.organization.address === null ? "信息尚未录入" : lank.organization.address;    //地址
            $("#fullName").html(fullName);
            $("#name").html(name);
            $("#comCreateDate").html(createDate);
            // var stroer = ``;
            // var orgSite = lank.orgSite;     //是否有数据
            // $("#fixothermal").data("orgSite1",orgSite);
            // $("#fixemal").data("orgSite2",orgSite);
            // if(orgSite === null){
            //     stroer += `
            //     <div class="ty-alert">
            //         <div class="com_address"><span>机构地址</span><span class="con-txt" id="address">${address}</span></div>
            //         <div>
            //             <span class="thin-btn" data-fun="siteInfo" style="display: block;margin-right: 0px;text-align: right;">管理</span>
            //             <div id="tidme"></div>
            //         </div>
            //     </div>  `;
            //     $("#tidme").hide();
            //     $("#otherbox").html(stroer);
            // }else{
            //     var str = ``;
            //     str = `<span>${new Date(orgSite.createDate).format("yyyy-MM-dd hh:mm:ss")}</span>&nbsp;<span>创建</span>`;
            //     $("#tidme").html(str);
            //     stroer += `
            //     <div class="ty-alert">
            //         <div class="com_address"><span>机构地址</span><span class="con-txt" id="address">${address}</span></div>
            //         <div>
            //             <span class="thin-btn" data-fun="siteInfo" style="display: block;margin-right: 0px;text-align: right;">管理</span>
            //             <div id="tidme">${str}</div>
            //         </div>
            //     </div>  `;
            //     var other = lank.otherSiteList; //其他地址数据
            //     for(var i = 0; i<other.length;i++){
            //         stroer += `
            //         <div class="ty-alert others">
            //             <div class="com_address">
            //             <span>其他地址</span>
            //             <span class="con-txt" id="addressoter">${other[i].address}</span>
            //         </div>
            //         <div id="gli">
            //             <span class="thin-btn" data-fun="siteInfo1" style="display: block;margin-right: 0px;text-align: right;" id="siteInfo1" onclick="siteInfo1($(this),0)">管理</span>
            //             <span class="hd">${handleNull(other[i].id)}</span>
            //             <div id="tidme1">
            //                 <span>${new Date(other[i].createDate).format("yyyy-MM-dd hh:mm:ss")}</span>&nbsp;<span>创建</span>
            //             </div>
            //         </div>
            //     </div>
            //     `;
            //     }
            //     $("#otherbox").html(stroer);
            // }
            $("#address_body").html("");
            var str = ``;
            var orgSite = lank.orgSite;     //是否有数据
            $("#fixothermal").data("orgSite1",orgSite);
            $("#fixemal").data("orgSite2",orgSite);
            if(orgSite === null){//没有经营地址数据
                str += `<tr>
                    <td>经营地址</td>
                    <td>${address || '——'}</td>
                    <td>${ownership || '——'}</td>
                    <td>——</td>
                    <td>——</td>
                    <td>${createDate || '——'}</td>
                    <td>
                        <span class="ty-color-blue">管理</span>
                    </td>
                </tr>`;
            }else{//有经营地址数据
                var ownership = orgSite.ownership;//场地情况
                if(ownership === '1'){ownership = '公司自有';}else if(ownership === '2'){ownership = '租赁或其他';}
                str += `<tr>
                    <td>经营地址</td>
                    <td>${address || '——'}</td>
                    <td>${ownership || '——'}</td>
                    <td>——</td>
                    <td>——</td>
                    <td>${createDate || '——'}</td>
                    <td><span class="ty-color-gray" data-fun="siteInfo" style="pointer-events:none;">管理</span></td>
                </tr>`;
            }
            str += `<tr>
                <td>注册地址</td>
                <td>——</td>
                <td>——</td>
                <td>——</td>
                <td>——</td>
                <td>——</td>
                <td><span class="ty-color-gray" style="pointer-events:none;">管理</span></td>
            </tr>`;
            var other = lank.otherSiteList; //其他地址数据
            for(var i =0;i<other.length;i++){
                var ownership = other[i].ownership;
                if(ownership === '1'){ownership = '公司自有';}else if(ownership === '2'){ownership = '租赁或其他';}
                str += `<tr>
                    <td>其他地址</td>
                    <td>${other[i].address}</td>
                    <td>${ownership || "——"}</td>
                    <td>——</td>
                    <td>——</td>
                    <td>${new Date(other[i].createDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                    <td>
                        <span class="ty-color-blue" onclick="siteInfo1($(this),0)">管理</span>
                        <span class="hd">${handleNull(other[i].id)}</span>
                    </td>
                </tr>`;
            }
            $("#address_body").append(str);
        }
    })
}

// creator: 李玉婷，2022-06-23 09:49:20，修改企业名称确定
function sureChangeBusinessName(){
    let full = $("#changeBusinessName input").eq(0).val();
    let name = $("#changeBusinessName input").eq(1).val();
    if (full !== "" && name !== "") {
        let data = {
            "name": name,
            "fullName": full
        }
        $.ajax({
            url: "../site/updateOrganizationInfo.do",
            data: data,
            success: function (res) {
                bounce.cancel();
                //getBusiness()
                $("#orgName").html(full);
                location.reload();
            }
        })
    }
}

// creator: sy 2025-2-21    机构名称的修改
function sureChangeBusinessName1(){
    //debugger;
    let full1 = $("#institmangent input").eq(0).val();
    let name1 = $("#institmangent input").eq(1).val();
    if(full1 !== "" && name1 !== ""){
        let data = {
            "name": name1,
            "fullName": full1
        };
        $.ajax({
            url: "../site/updateOrganizationInfo.do",
            data: data,
            success: function (res) {
                bounce.cancel();
                //getBusiness()
                //$("#orgName").html(full);
                location.reload();
            }
        })
    }
}

// creator: 李玉婷，2022-06-23 10:29:20，企业名称修改记录
function updateNameLog(currPage  , pageSize){
    $.ajax({
        "url": '../site/organizationInfoEditHistories.do',
        "data": {
            "currentPageNo":currPage,
            "pageSize":pageSize
        } ,
        success:function(res) {
            let data = res.data;
            let list = data.organizationHistoryList || [];
            let curStaStr = ``;
            var cur = data.pageInfo["currentPageNo"];
            var totalPage = data.pageInfo["totalPage"];
            bounce_Fixed.show($("#businessNameEditLog"));
            //debugger;
            if(data.number >0){
                let str = ``;
                $("#ye_log").show();
                setPage( $("#ye_log") , cur ,  totalPage , "businessNameEditLog" );
                debugger;
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    str += `<tr>
                             <td>${item.dataState}</td>
                             <td class="ty-td-control">
                                <span class="nodeBtn ty-color-blue" data-fun="nameEditLogScan">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                             <td>${handleNull(item.updateName)} ${new Date(item.updateTime).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                }
                $("#businessNameEditLog tbody").children(":gt(0)").remove();
                $("#businessNameEditLog tbody").append(str);
                let n = data.number * 1 - 1;
                curStaStr = `<p>当前的机构名称为第${n}次修改后的结果。</p><p>修改人：${handleNull(data.updateName) === ""? "系统": handleNull(data.updateName)} ${new Date(data.updateTime).format("yyyy-MM-dd hh:mm:ss")}</p> `
                $("#businessNameEditLog table").show();
            }else{
                $("#ye_log").hide();
                $("#businessNameEditLog table").hide();
                curStaStr = `<p>当前的机构名称尚未经修改。</p> `
            }
            $("#businessNameEditLog .curSta").html(curStaStr);
        }
    });
}

// creator: 李玉婷，2022-06-23 10:59:50，企业名称修改记录-查看
function updateNameLogScan(obj){
    var info = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        "url":"../site/getOrganizationInfoHistory.do",
        "data": { "id": info.id  } ,
        success:function(data) {
            var info = data.data;
            $('.log_fullName').html(handleNull(info.fullName));
            $('.log_name').html(handleNull(info.name));
            bounce_Fixed.show($('#businessNameEditLogScan'));
        }
    });
}

// creator: 李玉婷，2022-06-23 10:59:50，场地信息
function siteInfo(){
    $('#siteDetail tbody').html("");
    $("#siteTaxList tbody tr:gt(0)").remove();
    $("#siteTaxList").hide();
    $.ajax({
        "url":"../site/getOrgSiteInfo.do",
        "data": { } ,
        success:function(res) {
            let detail = res.data.orgSite;
            if(detail == null){
                layer.msg("操作失败！因为机构地址尚未录入！");
                return  false;
            }else{
                let rentImageList = res.data.rentImageList;
                let rentInitialList = res.data.rentInitialList; //水电煤气的列表
                let siteTaxList = res.data.siteTaxList, siteTaxStr = ``; //税费列表
                let siteImageList = res.data.siteImageList;
                let rentChargeList = res.data.rentChargeList;
                let siteRent = res.data.siteRent;
                let address = ``,ownership = `信息尚未录入`;
                let html = ``, sitePics = ``;
                $("#siteStorage").html(JSON.stringify(res.data));
                $('.siteInfoCare').show();
                $('#hydroGas').hide();
                if (detail) {
                    address = detail.address;
                    ownership = transformStr(detail.ownership, 'siteType');
                }
                html = `
                    <tr>
                            <td width="35%">具体地址</td>
                            <td width="65%" colspan="3">${address}</td>
                        </tr>
                        <tr>
                            <td>场地情况</td>
                            <td colspan="3">${ownership}</td>
                        </tr>
                    `;
                if (detail) {
                    for(let i=0;i<siteImageList.length;i++){
                        sitePics += `<a path="${siteImageList[i].uplaodPath}" onclick="seeOnline($(this))">${i+1}</a>`;
                    }
                    html += `<tr><td>场地照片</td><td colspan="3" class="comPics">${sitePics}</td></tr>
                         <tr>
                            <td>总面积</td>
                            <td>${handleNull(detail.totalArea).toFixed(2)}平方米</td>
                            <td>建筑面积</td>
                            <td>${handleNull(detail.buildingArea).toFixed(2)}平方米</td>
                        </tr>`;
                    if (handleNull(detail.ownership) !== "") {
                        $('.siteInfoCare').hide();
                        if (detail.ownership === "1") {

                        } else if (detail.ownership === "2") {
                            let pic = ``,file = ``;
                            for(let i=0;i<rentImageList.length;i++){
                                if (rentImageList[i].type === '1'){
                                    pic += `<a path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">${rentImageList[i].orders}</a>`;
                                } else {
                                    file += `<a path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">查看</a>`;
                                }
                            }
                            if (siteRent) {
                                html += `<tr><td>租赁合同的扫描件或照片</td><td colspan="3" class="comPics">${pic}</td></tr>
                           <tr>
                              <td>租赁合同的可编辑版本</td>
                              <td>${file}</td>
                              <td>场地押金</td>
                              <td>${handleNull(siteRent.pledge).toFixed(2)}元</td>
                          </tr>
                           <tr>
                              <td>合同开始日期</td>
                              <td>${new Date(siteRent.beginTime).format('yyyy-MM-dd')}</td>
                              <td>合同结束日期</td>
                              <td>${new Date(siteRent.endTime).format('yyyy-MM-dd')}</td>
                          </tr>
                           <tr>
                              <td>每年租金</td>
                              <td>${handleNull(siteRent.amount).toFixed(2)}元</td>
                              <td>房东联系方式</td>
                              <td>${handleNull(siteRent.landlord)}</td>
                          </tr>
                           <tr>
                              <td>租金需缴纳的次数与金额</td>
                              <td colspan="3">${morePayStr(rentChargeList)}</td>
                          </tr>
                           <tr>
                              <td>是否需额外缴纳税款</td>
                              <td>${transformStr(siteRent.hasTax, 'hasTax')}</td>
                              <td>租金是否含税</td>
                              <td>${transformStr(siteRent.hasInvoice , 'rentHasTax')}</td>
                          </tr>`;
                            }
                            if(rentInitialList && rentInitialList.length > 0) {
                                $('#hydroGas').show();
                                let combin = ``,tab1 = ``, tab2 = ``;
                                let water= ``, ele = ``, gas = ``, sNum = 0;
                                let idx1 = rentInitialList.findIndex(value => value.item === '水');
                                let idx2 = rentInitialList.findIndex(value => value.item === '电');
                                let idx3 = rentInitialList.findIndex(value => value.item === '煤气');
                                if (idx1 > -1) {
                                    sNum++;
                                    water = Number(rentInitialList[idx1].amount).toFixed(2);
                                }
                                if (idx2 > -1) {
                                    sNum++;
                                    ele = Number(rentInitialList[idx2].amount).toFixed(2);
                                }
                                if (idx3 > -1) {
                                    sNum++;
                                    gas = Number(rentInitialList[idx3].amount).toFixed(2);
                                }
                                tab1 = `<tr><td>水</td><td>电</td><td>煤气</td></tr>
                                    <tr><td>${water}</td><td>${ele}</td><td>${gas}</td></tr>`;
                                tab1 += `<tr>`;
                                tab2 += `<tr>`;
                                for (let b = 0; b < 6; b++) {
                                    if (b < rentInitialList.length) {
                                        if (rentInitialList[b].item !== "水" && rentInitialList[b].item !== "电" && rentInitialList[b].item !== "煤气") {
                                            tab1 += `<td>${rentInitialList[b].item}</td>`;
                                            tab2 += `<td>${(rentInitialList[b].amount).toFixed(2)}元</td>`;
                                        }
                                    } else if (b >= -(-6 + (3 - (rentInitialList.length - sNum)))){
                                        tab1 += `<td></td>`;
                                        tab2 += `<td></td>`;
                                    }
                                }
                                tab1 += `</tr>`;
                                tab2 += `</tr>`;
                                combin += tab1 + tab2;
                                $("#hydroGas tbody").html(combin);
                            }
                        }
                        if(siteTaxList && siteTaxList.length > 0) {
                            for(let a=0;a<siteTaxList.length;a++){
                                siteTaxStr += `<tr>
                                <td>${siteTaxList[a].category}</td>
                                <td>${(siteTaxList[a].amount).toFixed(2)}元</td>
                                <td>${new Date(siteTaxList[a].payBeginTime).format('MM-dd')} 至 ${new Date(siteTaxList[a].payEndTime).format('MM-dd')}</td>
                            </tr>`;
                            }
                            $("#siteTaxList tbody").append(siteTaxStr);
                            $("#siteTaxList").show();
                        }
                    }
                }
                $('#siteDetail tbody').html(html);
                bounce.show($('#siteInformation'));
            }
        }
    });
}

// creator: 李玉婷，2022-06-23 14:25:50，是否需额外缴纳税款
function changeHasTax(obj){
    let val = obj.val();
    if (val === '1') {
        $("#editSite .part2").show();
    } else if (val === '0') {
        $("#editSite .part2").hide();
    }
}

// creator: 李玉婷，2022-06-23 14:25:50，更换为新场地
function updateSiteSure(){
    let filled = 0;
    $("#editSite input:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    $("#editSite select:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    if ($("#editSite .imgsthumb").length > 0){filled++;}
    if ($("#editSite .fileIm").length > 0){filled++;}
    if (filled > 0) {
        if ($("#editSite input[name='totalArea']").val() === "") {
            layer.msg("请录入总面积");
            return false;
        }
        if ($("#editSite input[name='buildingArea']").val() === "") {
            layer.msg("请录入建筑面积");
            return false;
        }
        if ($("#editSite input[name='pledge']:visible").val() === "") {
            layer.msg("请录入场地押金");
            return false;
        }
        let num = 0,dateNum =0;
        $("#editSite input[need][name='amount']:visible").each(function (){
            if ($(this).val() === "") {
                num++;
            }
        })
        if (num > 0){
            layer.msg("请录入金额");
            return false;
        }
        $("#editSite input[name='payBeginTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        $("#editSite input[name='payEndTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        if (dateNum > 0){
            layer.msg("请录入应缴纳税款的时间区间");
            return false;
        }
        if ($("#editSite select[name='hasInvoice']:visible").val() === "") {
            layer.msg("请选择租金是否含税");
            return false;
        }
        if ($("#editSite select[name='hasTax']:visible").val() === "") {
            layer.msg("请选择是否需额外缴纳税款");
            return false;
        }
        if ($("#editSite #otherPayBox:visible").html() === "") {
            layer.msg("请编辑合同有效期内，租金需缴纳的次数与每次需缴纳的金额");
            return false;
        }
        let source = $("#editSite").data("source");
        let pics = [], taxCategory = [], url = ``;
        let part0 =  $("#editSite .part0");
        let param = {
            "ownership": part0.find("select:eq(0)").val(),
            "address": part0.find("input[name=address]").val(),
            "totalArea": part0.find("input[name=totalArea]").val(),
            "buildingArea": part0.find("input[name=buildingArea]").val()
        };
        if (source === 1) {
            url = `../site/updateOrgSiteInfo.do`;
        } else if (source === 2) {
            url = `../site/replaceNewOrgSite.do`;
        }
        $("#editSite .categoryItem:visible").each(function (){
            let item = {}, empty = 0;
            $(this).find("input").each(function () {
                if ($(this).val() === "") empty++;
                let name = $(this).attr("name");
                if (name === 'payBeginTime' || name === 'payEndTime') {
                    item[name] = $(this).data("val");
                } else {
                    item[name] = $(this).val();
                }
            })
            if (empty < 4) {
                taxCategory.push(item);
            }
        })
        $("#imageList .filePic:visible").each(function (){
            let path = $(this).attr("path");
            pics.push(path);
        })
        param.imageList = JSON.stringify(pics);
        param.siteTaxList = JSON.stringify(taxCategory);
        if (part0.find("select:eq(0)").val() === '2'){
            let contractPic = [], conDoc = [];
            $("#contractPic .filePic:visible").each(function (){
                let path = $(this).attr("path");
                contractPic.push(path);
            })
            $("#contractDoc .fileIm:visible").each(function (){
                let file = JSON.parse($(this).find(".hd").html());
                conDoc.push(file.filename);
            })
            param.contractImageList = JSON.stringify(contractPic);
            param.contractFileList = JSON.stringify(conDoc);
            param.rentChargeList = $("#otherPayBox").find(".hd").html() === ""?"[]":$("#otherPayBox").find(".hd").html();
            $(".part1").find("input[require]").each(function () {
                let name = $(this).attr("name");
                param[name] = $(this).val();
            })
            $(".part1").find("select[require]").each(function () {
                let name = $(this).attr("name");
                if ($(this).val() === '1'){
                    param[name] = true;
                } else if ($(this).val() === '0'){
                    param[name] = false;
                }
            });
            let rentInitialList = [];
            $(".edit_hydroGas").find(".modItem-s").each(function (){
                let jak = $(this).index();
                let item = {};
                let val = $(this).find("input").val();
                if (val !== "") {
                    if (jak === 0) {
                        item =  {"amount": val, "item": "水"}
                    } else if (jak === 1) {
                        item =  {"amount": val, "item": "电"}
                    } else if (jak === 2) {
                        item =  {"amount": val, "item": "煤气"}
                    }
                    rentInitialList.push(item);
                }
            })
            for (var t=0;t<3;t++) {
                let val = $(".part3 .costs:eq(1) input[name='item']:eq("+ t +")").val();
                let amount = $(".part3 .costs:eq(1) input[name='amount']:eq("+ t +")").val();
                if (val !== "" || amount  !== "") {
                    if (amount === ""){ amount = 0;}
                    let item = {"amount": amount, "item": val}
                    rentInitialList.push(item);
                }
            }
            param.rentInitialList = JSON.stringify(rentInitialList);
        }
        $.ajax({
            "url": url,
            "data": {'json': JSON.stringify(param)} ,
            success:function(data) {
                bounce_Fixed.cancel();
                siteInfo();
                // getBusiness();
                startmess();
            }
        });
    }
}

// creator: 李玉婷，2022-06-24 08:29:11，场地信息操作记录
function conditionsAssign(obj){
    let val = obj.val();
    $(".part").hide();
    $('.categoryGroup').html("");
    addTaxCategory();
    if (val === '1') {
        $(".part2").show();
        $(".part0").addClass("low-line").siblings().removeClass("low-line");
    } else if (val === '2') {
        $(".part1").show();
        $(".part3").show();
        $(".part0,.part1,.part2").addClass("low-line");
        if($(".part1 select[name='hasTax']").val() === '1')  $(".part2").show();
    } else {
        $(".part").hide();
        $(".part0,.part").removeClass("low-line");
    }
}

// creator: 李玉婷，2022-06-24 08:29:11，增加税种
function addTaxCategory(){
    let btn = ``;
    if ($('.categoryGroup .categoryItem').length >= 1) {
        btn = `<span class="ty-color-red delRow" onclick="delThisLine($(this))">删除本行</span>`;
    }
    let html = `
    <div class="categoryItem clear">
        <div class="modItem-m">
            <div class="ty-left">
                <p class="modItemTtl">
                    应缴税种名称
                </p>
                <input class="ty-inputText" value="" placeholder="请录入" name="category"/>
                <i class="fa fa-times clearInputVal"></i>
            </div>
            <div class="ty-right">
                <p class="modItemTtl">
                    应缴税款金额
                </p>
                <div class="input-group fitSize">
                    <input value="" placeholder="请录入" name="amount" need onkeyup="clearNoNumN(this, 2)"/>
                    <span class="input-group-btn">
                    <span class="unit-btn" type="button">元</span>
                </span>
                </div>
            </div>
        </div>
        <div class="modItem-m">
            <p class="modItemTtl">
                应缴纳税款的时间区间
            </p>
            <div class="modItem-flex">
                <div>
                    <input class="ty-inputText payBeginTime" value="" placeholder="请录入" name="payBeginTime"/>
                </div>
                <span class="ctrl">至</span>
                <div>
                    <input class="ty-inputText payEndTime" value="" placeholder="请录入" name="payEndTime"/>
                </div>
            </div>
        </div>
        ${btn}
    </div>`;
    $(".categoryGroup").append(html);
    let _input = $(".categoryGroup .categoryItem:last .payBeginTime");
    let _input1 = $(".categoryGroup .categoryItem:last .payEndTime");
    laydate.render({elem: _input[0], format: 'MM-dd',
        done: function (value, date, end){
            _input.data("val", new Date(date.year,date.month-1,date.date).format('yyyy-MM-dd'));
        }
    });
    laydate.render({elem: _input1[0], format: 'MM-dd',
        done: function (value, date, end){
            _input1.data("val", new Date(date.year,date.month-1,date.date).format('yyyy-MM-dd'));
        }
    });
}

function delThisLine(obj){
    obj.parent().remove();
}

// creator: 李玉婷，2022-06-24 08:29:11，场地信息操作记录
function updateSiteLog(currPage  , pageSize){
    $.ajax({
        "url": '../site/getSiteInfoEditHistories.do',
        "data": {
            "currentPageNo":currPage,
            "pageSize":pageSize
        } ,
        success:function(res) {
            let data = res.data;
            let list = data.orgSiteHistoryList || [];
            let curStaStr = ``;
            var cur = data.pageInfo["currentPageNo"];
            var totalPage = data.pageInfo["totalPage"];
            bounce_Fixed.show($("#siteEditLog"));
            if(data.number >0){
                let str = ``;
                setPage( $("#ye_siteLog") , cur ,  totalPage , "updateSiteLog" );
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    let cate = ``;//1-增,2-删,3-全改,4-更换新场地,5-启停用,6-仅改场地信息,7-改图片,8-改租赁信息,9-改租赁费用
                    if (list[i].operation === '1') {
                        cate = `录入原始信息`;
                    } else if (list[i].operation === '3') {
                        cate = `修改场地信息——修改既有场地的信息`;
                    } else if (list[i].operation === '4') {
                        cate = `修改场地信息——更换为新场地`;
                    }
                    str += `<tr>
                             <td>${item.dataState}</td>
                             <td class="ty-td-control">
                                <span class="nodeBtn ty-color-blue" data-fun="updateSiteLogScan">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                             <td>${cate}</td>
                             <td>${handleNull(item.updateName)} ${new Date(item.updateTime).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                }
                $("#siteEditLog tbody").children(":gt(0)").remove();
                $("#siteEditLog tbody").append(str);
                let n = data.number;
                curStaStr = `<p>当前场地信息为第${n}次修改后的结果。</p><p>修改人：${data.updateName} ${new Date(data.updateTime).format("yyyy-MM-dd hh:mm:ss")}</p> `
                $("#yecur").show();
                $("#siteEditLog table").show();
            }else{
                $("#yecur").hide();
                $("#siteEditLog table").hide();
                curStaStr = `<p>当前还没有与场地有关的操作记录。</p><p>创建人：${data.updateName === ""? '系统':data.updateName} ${new Date(data.updateTime).format("yyyy-MM-dd hh:mm:ss")}</p> `
            }
            $("#siteEditLog .curSta").html(curStaStr);
        }
    });
}

// creator : sy 2023-01-16 其它信息操作记录
function updateSiteLogq(){
    var unid = $(".qtthree").data("id");
    $.ajax({
        "url":"../site/getSiteInfoEditHistories.do",
        "data":{
            "siteId":unid,
            "currentPageNo":1,
            "pageSize":20
        },
        success:function(res){
            let data = res.data;
            let list = data.orgSiteHistoryList || [];
            let curStaStr = ``;
            var cur = data.pageInfo["currentPageNo"];
            var totalPage = data.pageInfo["totalPage"];
            bounce_Fixed.show($("#siteEditLog"));
            if(data.number > 0){
                let str = ``;
                setPage($("#ye_siteLog"),cur,totalPage,"updateSiteLog");
                for(let i = 0;i< list.length;i++){
                    let item = list[i];
                    let cate = ``;
                    if(list[i].operation === '1'){
                        cate = `录入原始信息`;
                    }else if(list[i].operation === '3'){
                        cate = `修改场地信息——修改既有场地的信息`;
                    }else if(list[i].operation === '4'){
                        cate = `修改场地信息——更换为新场地`;
                    }
                    str +=`
                        <tr>
                            <td>${item.dataState}</td>
                            <td class="ty-td-control">
                                <span class="nodeBtn ty-color-blue" id="havelook" onclick="updateSiteLogScanq($(this))">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                            </td>
                            <td>${cate}</td>
                            <td>${handleNull(item.updateName)} ${new Date(item.updateTime).format("yyyy-MM-dd hh:mm:ss")}</td>
                        </tr>`;
                }
                $("#siteEditLog tbody").children(":gt(0)").remove();
                $("#siteEditLog tbody").append(str);
                let n = data.number;
                curStaStr = `<p>当前场地信息为第${n}次修改后的结果。</p><p>修改人：${data.updateName} ${new Date(data.updateTime).format("yyyy-MM-dd hh:mm:ss")}</p>`;
                $("#yecur").show();
                $("#siteEditLog table").show();
            }else{
                $("#yecur").hide();
                $("#siteEditLog table").hide();
                curStaStr = `<p>当前还没有与场地有关的操作记录。</p><p>创建人:${data.updateName === ""? '系统':data.updateName} ${new Date(data.updateTime).format("yyyy-MM-dd hh:mm:ss")}</p>`;
            }
            $("#siteEditLog .curSta").html(curStaStr);
        }
    });
}

// creator: 李玉婷，2022-06-23 10:59:50，场地信息操作记录-查看
function updateSiteLogScan(obj){
    var info = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        "url":"../site/getOrgSiteHistoryInfo.do",
        "data": { "siteHistoryId": info.id  } ,
        success:function(res) {
            let detail = res.data.orgSite;
            let rentImageList = res.data.rentImageList;
            let rentInitialList = res.data.rentInitialList; //水电煤气的列表
            let siteTaxList = res.data.siteTaxList, siteTaxStr = ``; //税费列表
            let siteImageList = res.data.siteImageList;
            let rentChargeList = res.data.rentChargeList;
            let siteRent = res.data.siteRent;
            let address = ``,ownership = `信息尚未录入`;
            let html = ``, sitePics = ``;
            $("#siteStorage").html(JSON.stringify(res.data));
            $("#hydroGas_log").hide();
            $("#siteTaxList_log").hide();
            $("#siteTaxList_log tbody tr:gt(0)").remove();
            if (detail) {
                address = detail.address;
                ownership = transformStr(detail.ownership, 'siteType');
            }
            html = `
                    <tr>
                            <td width="30%">具体地址</td>
                            <td width="70%" colspan="3">${address}</td>
                        </tr>
                        <tr>
                            <td>场地情况</td>
                            <td colspan="3">${ownership}</td>
                        </tr>
                    `;
            if (detail) {
                for(let i=0;i<siteImageList.length;i++){
                    sitePics += `<a path="${siteImageList[i].uplaodPath}" onclick="seeOnline($(this))">${i+1}</a>`;
                }
                html += `<tr><td>场地照片</td><td colspan="3" class="comPics">${sitePics}</td></tr>
                         <tr>
                            <td>总面积</td>
                            <td>${handleNull(detail.totalArea).toFixed(2)}平方米</td>
                            <td>建筑面积</td>
                            <td>${handleNull(detail.buildingArea).toFixed(2)}平方米</td>
                        </tr>`;
                if (handleNull(detail.ownership) !== "") {
                    if (detail.ownership === "1") {

                    } else if (detail.ownership === "2") {
                        let pic = ``,file = ``;
                        for(let i=0;i<rentImageList.length;i++){
                            if (rentImageList[i].type === '1'){
                                pic += `<a path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">${rentImageList[i].orders}</a>`;
                            } else {
                                file += `<a path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">查看</a>`;
                            }
                        }
                        if (siteRent) {
                            html += `<tr><td>租赁合同的扫描件或照片</td><td colspan="3" class="comPics">${pic}</td></tr>
                           <tr>
                              <td>租赁合同的可编辑版本</td>
                              <td>${file}</td>
                              <td>场地押金</td>
                              <td>${handleNull(siteRent.pledge).toFixed(2)}元</td>
                          </tr>
                           <tr>
                              <td>合同开始日期</td>
                              <td>${new Date(siteRent.beginTime).format('yyyy-MM-dd')}</td>
                              <td>合同结束日期</td>
                              <td>${new Date(siteRent.endTime).format('yyyy-MM-dd')}</td>
                          </tr>
                           <tr>
                              <td>每年租金</td>
                              <td>${handleNull(siteRent.amount).toFixed(2)}元</td>
                              <td>房东联系方式</td>
                              <td>${handleNull(siteRent.landlord)}</td>
                          </tr>
                           <tr>
                              <td>租金需缴纳的次数与金额</td>
                                <td colspan="3">${morePayStr(rentChargeList)}</td>
                           </tr>
                           <tr>
                              <td>是否需额外缴纳税款</td>
                              <td>${transformStr(siteRent.hasTax, 'hasTax')}</td>
                              <td>租金是否含税</td>
                              <td>${transformStr(siteRent.hasInvoice , 'rentHasTax')}</td>
                          </tr>`;
                        }
                        if(rentInitialList && rentInitialList.length > 0) {
                            $('#hydroGas_log').show();
                            let combin = ``,tab1 = ``, tab2 = ``;
                            let water= ``, ele = ``, gas = ``, sNum = 0;
                            let idx1 = rentInitialList.findIndex(value => value.item === '水');
                            let idx2 = rentInitialList.findIndex(value => value.item === '电');
                            let idx3 = rentInitialList.findIndex(value => value.item === '煤气');
                            if (idx1 > -1) {
                                sNum++;
                                water = Number(rentInitialList[idx1].amount).toFixed(2);
                            }
                            if (idx2 > -1) {
                                sNum++;
                                ele = Number(rentInitialList[idx2].amount).toFixed(2);
                            }
                            if (idx3 > -1) {
                                sNum++;
                                gas = Number(rentInitialList[idx3].amount).toFixed(2);
                            }
                            tab1 = `<tr><td>水</td><td>电</td><td>煤气</td></tr>
                                    <tr><td>${water}</td><td>${ele}</td><td>${gas}</td></tr>`;
                            tab1 += `<tr>`;
                            tab2 += `<tr>`;
                            for (let b = 0; b < 6; b++) {
                                if (b < rentInitialList.length) {
                                    if (rentInitialList[b].item !== "水" && rentInitialList[b].item !== "电" && rentInitialList[b].item !== "煤气") {
                                        tab1 += `<td>${rentInitialList[b].item}</td>`;
                                        tab2 += `<td>${(rentInitialList[b].amount).toFixed(2)}元</td>`;
                                    }
                                } else if (b >= -(-6 + (3 - (rentInitialList.length - sNum)))){
                                    tab1 += `<td></td>`;
                                    tab2 += `<td></td>`;
                                }
                            }
                            tab1 += `</tr>`;
                            tab2 += `</tr>`;
                            combin += tab1 + tab2;
                            $("#hydroGas_log tbody").html(combin);
                        }
                    }
                    if(siteTaxList && siteTaxList.length > 0) {
                        for(let a=0;a<siteTaxList.length;a++){
                            siteTaxStr += `<tr>
                                <td>${siteTaxList[a].category}</td>
                                <td>${(siteTaxList[a].amount).toFixed(2)}元</td>
                                <td>${new Date(siteTaxList[a].payBeginTime).format('MM-dd')} 至 ${new Date(siteTaxList[a].payEndTime).format('MM-dd')}</td>
                            </tr>`;
                        }
                        $("#siteTaxList_log tbody").append(siteTaxStr);
                        $("#siteTaxList_log").show();
                    }
                }
            }
            $('#siteDetail_log tbody').html(html);
            bounce_Fixed2.show($('#updateSiteScan'));
        }
    });
}

// creator: sy 2023-01-17 其它地址信息操作记录-查看
function updateSiteLogScanq(obj){
    var lank = obj.siblings(".hd").html();
    lank = JSON.parse(lank);
    var unid = lank.id;
    $.ajax({
        "url": "../site/getOrgSiteHistoryInfo.do",
        "data":{
            siteHistoryId:unid
        },
        success:function(res){
            let detail = res.data.orgSite;
            let rentImageList = res.data.rentImageList;
            let rentInitialList = res.data.rentInitialList; //水电煤气的列表
            let siteTaxList = res.data.siteTaxList, siteTaxStr = ``; //税费列表
            let siteImageList = res.data.siteImageList;
            let rentChargeList = res.data.rentChargeList;
            let siteRent = res.data.siteRent;
            let address = ``,ownership = `信息尚未录入`;
            let html = ``, sitePics = ``;
            $("#siteStorage").html(JSON.stringify(res.data));
            $("#hydroGas_log").hide();
            $("#siteTaxList_log").hide();
            $("#siteTaxList_log tbody tr:gt(0)").remove();
            if (detail) {
                address = detail.address;
                ownership = transformStr(detail.ownership, 'siteType');
            }
            html = `
                    <tr>
                            <td width="30%">具体地址</td>
                            <td width="70%" colspan="3">${address}</td>
                        </tr>
                        <tr>
                            <td>场地情况</td>
                            <td colspan="3">${ownership}</td>
                        </tr>
                    `;
            if (detail) {
                for(let i=0;i<siteImageList.length;i++){
                    sitePics += `<a path="${siteImageList[i].uplaodPath}" onclick="seeOnline($(this))">${i+1}</a>`;
                }
                html += `<tr><td>场地照片</td><td colspan="3" class="comPics">${sitePics}</td></tr>
                         <tr>
                            <td>总面积</td>
                            <td>${handleNull(detail.totalArea).toFixed(2)}平方米</td>
                            <td>建筑面积</td>
                            <td>${handleNull(detail.buildingArea).toFixed(2)}平方米</td>
                        </tr>`;
                if (handleNull(detail.ownership) !== "") {
                    if (detail.ownership === "1") {
                    }else if (detail.ownership === "2") {
                        let pic = ``,file = ``;
                        for(let i=0;i<rentImageList.length;i++){
                            if (rentImageList[i].type === '1'){
                                pic += `<a path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">${rentImageList[i].orders}</a>`;
                            }else{
                                file += `<a path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">查看</a>`;
                            }
                        }
                        if (siteRent) {
                            html += `<tr><td>租赁合同的扫描件或照片</td><td colspan="3" class="comPics">${pic}</td></tr>
                                <tr>
                                    <td>租赁合同的可编辑版本</td>
                                    <td>${file}</td>
                                    <td>场地押金</td>
                                    <td>${handleNull(siteRent.pledge).toFixed(2)}元</td>
                                </tr>
                                 <tr>
                                    <td>合同开始日期</td>
                                    <td>${new Date(siteRent.beginTime).format('yyyy-MM-dd')}</td>
                                    <td>合同结束日期</td>
                                    <td>${new Date(siteRent.endTime).format('yyyy-MM-dd')}</td>
                                </tr>
                                  <tr>
                                    <td>每年租金</td>
                                    <td>${handleNull(siteRent.amount).toFixed(2)}元</td>
                                    <td>房东联系方式</td>
                                    <td>${handleNull(siteRent.landlord)}</td>
                                </tr>
                                 <tr>
                                    <td>租金需缴纳的次数与金额</td>
                                        <td colspan="3">${morePayStr(rentChargeList)}</td>
                                </tr>
                                <tr>
                                    <td>是否需额外缴纳税款</td>
                                    <td>${transformStr(siteRent.hasTax, 'hasTax')}</td>
                                    <td>租金是否含税</td>
                                    <td>${transformStr(siteRent.hasInvoice, 'rentHasTax')}</td>
                                </tr>`;
                        }
                        if(rentInitialList && rentInitialList.length > 0) {
                            $('#hydroGas_log').show();
                            let combin = ``,tab1 = ``, tab2 = ``;
                            let water= ``, ele = ``, gas = ``, sNum = 0;
                            let idx1 = rentInitialList.findIndex(value => value.item === '水');
                            let idx2 = rentInitialList.findIndex(value => value.item === '电');
                            let idx3 = rentInitialList.findIndex(value => value.item === '煤气');
                            if (idx1 > -1) {
                                sNum++;
                                water = Number(rentInitialList[idx1].amount).toFixed(2);
                            }
                            if (idx2 > -1) {
                                sNum++;
                                ele = Number(rentInitialList[idx2].amount).toFixed(2);
                            }
                            if (idx3 > -1) {
                                sNum++;
                                gas = Number(rentInitialList[idx3].amount).toFixed(2);
                            }
                            tab1 = `<tr><td>水</td><td>电</td><td>煤气</td></tr>
                                    <tr><td>${water}</td><td>${ele}</td><td>${gas}</td></tr>`;
                            tab1 += `<tr>`;
                            tab2 += `<tr>`;
                            for (let b = 0; b < 6; b++) {
                                if (b < rentInitialList.length) {
                                    if (rentInitialList[b].item !== "水" && rentInitialList[b].item !== "电" && rentInitialList[b].item !== "煤气") {
                                        tab1 += `<td>${rentInitialList[b].item}</td>`;
                                        tab2 += `<td>${(rentInitialList[b].amount).toFixed(2)}元</td>`;
                                    }
                                } else if (b >= -(-6 + (3 - (rentInitialList.length - sNum)))){
                                    tab1 += `<td></td>`;
                                    tab2 += `<td></td>`;
                                }
                            }
                            tab1 += `</tr>`;
                            tab2 += `</tr>`;
                            combin += tab1 + tab2;
                            $("#hydroGas_log tbody").html(combin);
                        }
                    }
                    if(siteTaxList && siteTaxList.length > 0) {
                        for(let a=0;a<siteTaxList.length;a++){
                            siteTaxStr += `<tr>
                                <td>${siteTaxList[a].category}</td>
                                <td>${(siteTaxList[a].amount).toFixed(2)}元</td>
                                <td>${new Date(siteTaxList[a].payBeginTime).format('MM-dd')} 至 ${new Date(siteTaxList[a].payEndTime).format('MM-dd')}</td>
                            </tr>`;
                        }
                        $("#siteTaxList_log tbody").append(siteTaxStr);
                        $("#siteTaxList_log").show();
                    }
                }
            }
            $('#siteDetail_log tbody').html(html);
            bounce_Fixed2.show($('#updateSiteScan'));
        }
    });
}



// creator: 李玉婷，2021-05-20 17:32:24，上传图片
function initUpload(obj, storg){
    let fileTypeExtsStr = ''
    let multi = true
    if(storg == "img" || storg == "entry"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(storg == "doc"){
        multi = false
        fileTypeExtsStr = '*.*;'
    } else if (storg == "vedio") {
        fileTypeExtsStr = '*.cda,*.wav;*.wmv;*.mp3;*.mp4;*.mpeg;;*.aiff;*.vqf;*.amr;'
    }
    let itemTemplate = ``
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi:multi,
        buttonText:"上传",
        formData:{
            module: '总务管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadStart:function(){
            loading.open();
        },
        onUploadSuccess:function(file, data){
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            //name = file.name,           //文件名称
            obj.parent().attr("groupUuid", data.groupUuid )
            loading.close();
            switch (storg) {
                case 'img':
                    var imgLen = obj.parent("p").siblings().find(".file-box").find(".imgsthumb").length;
                    if(imgLen < 9){
                        var imgStr1 =
                            `<div class="imgsthumb">
                                <a class="filePic" data-type="1" path="${path}" data-ttl="${file.name}" onclick="seeOnline($(this))">${imgLen + 1}</a>
                                <a class="hd fileUid">${data.fileUid}</a>
                                <i class="fa fa-times" fileUid="${data.fileUid}" onclick="cancleThis($(this))"></i>
                            </div>`;
                        obj.parent("p").siblings().find(".file-box").append(imgStr1);
                        if (imgLen < 1) {
                            obj.parent("p").siblings().find(".file-box").find(".inTip").remove();
                        }
                        obj.parent("p").siblings().find(".clearPicsBtn").show();
                    }else{
                        layer.msg('最多只能上传9个文件')
                        delC(data)
                    }
                    break;
                case 'doc':
                    let fileCon2Len =  obj.parent("p").siblings().find(".file-box").find(".fileIm").length;
                    if(fileCon2Len === 0){
                    }else{
                        let delO = $(`.fileCon2`).find(".fileIm")
                        let info = JSON.parse(delO.find(".hd").html())
                        delC(info)
                        delO.remove();
                    }
                    let str2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <i class="fa fa-times" fileUid="${data.fileUid}" onclick="cancleThis($(this))"></i>
                             <span class="hd">${ JSON.stringify(data) }</span>
                        </span>`
                    obj.parent("p").siblings().find(".file-box").html(str2);
                    break;
            }
        }
    });
}

// creator: 李玉婷，2021-05-27 16:51:45，编辑与缴纳租金有关的信息
function editRentPaySure(){
    let pays = [], str = ``, num = 0;
    $('#otherPay tbody tr:gt(0)').each(function (){
        let item = {};
        item.item = $(this).find("select").val();
        item.amount = $(this).find("input:eq(0)").val();
        item.deadline = $(this).find("input:eq(1)").val();
        if (item.item !== "" || item.amount !== "" || item.deadline !== ""){
            if (item.amount === ""){
                num++;
                return false;
            }
            pays.push(item);
        }
    });
    if (num > 0) {
        layer.msg("请录入金额");
    } else {
        if (pays.length > 0) {
            str = morePayStr(pays);
        }
        $('#otherPayBox').html(str);
        bounce_Fixed2.cancel();
    }
}

// creator: 李玉婷，2021-05-27 08:21:15，编辑与缴纳租金有关的信息
function morePayStr (pays) {
    let str = ``;
    if (pays.length > 0) {
        str = `<span class="limitEllipsis">录入${pays.length}条，金额依次为`;
        for(let t=0;t< pays.length;t++) {
            pays[t].deadline = new Date(pays[t].deadline).format('yyyy-MM-dd');
            if (t < 2) {
                str += `${Number(pays[t].amount).toFixed(2)}元，`;
            }
        }
        str = str.slice(0, -1);
        str += `</span>
        <span class="editPay" onclick="morePayData($(this))">更多<span class="hd">${JSON.stringify(pays)}</span></span>`;
    }
    return str;
}

// creator: 李玉婷，2021-05-27 08:21:15，增加行
function addLine(){
    let btn = ``;
    if ($('#otherPay tbody tr').length >= 2) {
        btn = `<span class="delLine ty-color-red" onclick="delLine($(this))">删除本行</span>`;
    }
    let html = `
            <tr>
                <td>
                    <select>
                        <option value=""></option>
                        <option value="场地租金">场地租金</option>
                        <option value="物业费">物业费</option>
                        <option value="取暖费">取暖费</option>
                        <option value="需额外缴纳的车位费">需额外缴纳的车位费</option>
                    </select>
                </td>
                <td><input value="" type="text" onkeyup="clearNoNumN(this, 2)" need/></td>
                <td><input value="" type="text" class="rent_date"/></td>
                <td>${btn}</td>
            </tr>
    `;
    $('#otherPay tbody').append(html);
    let _input = $("#otherPay tbody tr:last .rent_date")[0];
    laydate.render({elem: _input, format: 'yyyy-MM-dd'});
}

function delLine(obj){
    obj.parents("tr").remove();
}

// creator: 李玉婷，2021-05-25 10:21:45,更多
function morePayData(obj){
    let html = ``;
    let list = JSON.parse(obj.find(".hd").html());
    bounce_Fixed3.show($("#rentSchedule"));
    $('#rentSchedule tbody tr:gt(0)').remove();
    for(let t=0;t< list.length;t++) {
        addLine();
        html += `
            <tr>
                <td>${list[t].item}</td>
                <td>${Number(list[t].amount).toFixed(2)}</td>
                <td>${list[t].deadline}</td>
            </tr>`
    }
    $('#rentSchedule tbody').append(html);
}

// creator: 李玉婷，2021-05-25 10:21:45，转换字符串
function transformStr( val, type){
    let str = ``;
    switch (type) {
        case 'siteType': //场地情况
            if (val === '1') {
                str = `公司自有`;
            } else if (val === '2') {
                str = `租赁或其他`;
            } else {
                str = `信息尚未录入`;
            }
            break;
        case 'hasTax': //是否需额外缴纳税款
            if (val) {
                str = `需要`;
            } else if (val === false){
                str = `不需要`;
            }
            break;
        case 'rentHasTax': //租金是否含税
            if (val === true) {
                str = `含税`;
            } else if (val === false){
                str = `不含税`;
            }
            break;
    }
    return str;
}

// creator: 李玉婷，2021-05-24 16:51:45，清除
function clearPicsBtn(obj) {
    obj.siblings(".file-box").find(".imgsthumb").each(function () {
        var curObj = $(this).find(".fa");
        cancleThis(curObj);
    })
}

// creator: 李玉婷，2019-09-04 19:31:34，删除图片
function cancleThis(obj) {
    var parentObj = obj.parents(".file-box");
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    if (parentObj.find(".imgsthumb").length <= 0) {
        parentObj.siblings(".clearPicsBtn").hide();
        parentObj.html("<span class='inTip'>请上传</span>");
    } else {
        obj.parents(".file-box").find(".imgsthumb").each(function () {
            var idx = $(this).index()+ 1;
            $(this).find(".filePic").html(idx);
        })
    }
}

// creator: 李玉婷，2020-08-27 09:49:20，修改 - 一键清空
function clearTxt(obj) {
    obj.prev().val("");
}

//creator: sy 2023-01-13 创建其他地址确定按钮
function addotheress(){
    let filled = 0;
    $("#editSite input:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    $("#editSite select:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    if ($("#editSite .imgsthumb").length > 0){filled++;}
    if ($("#editSite .fileIm").length > 0){filled++;}
    if (filled > 0) {
        if ($("#editSite input[name='totalArea']").val() === "") {
            layer.msg("请录入总面积");
            return false;
        }
        if ($("#editSite input[name='buildingArea']").val() === "") {
            layer.msg("请录入建筑面积");
            return false;
        }
        if ($("#editSite input[name='pledge']:visible").val() === "") {
            layer.msg("请录入场地押金");
            return false;
        }
        let num = 0,dateNum =0;
        $("#editSite input[need][name='amount']:visible").each(function () {
            if ($(this).val() === "") {
                num++;
            }
        })
        if (num > 0){
            layer.msg("请录入金额");
            return false;
        }
        $("#editSite input[name='payBeginTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        $("#editSite input[name='payEndTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        if (dateNum > 0){
            layer.msg("请录入应缴纳税款的时间区间");
            return false;
        }
        if ($("#editSite select[name='hasInvoice']:visible").val() === "") {
            layer.msg("请选择租金是否含税");
            return false;
        }
        if ($("#editSite select[name='hasTax']:visible").val() === "") {
            layer.msg("请选择是否需额外缴纳税款");
            return false;
        }
        if ($("#editSite #otherPayBox:visible").html() === "") {
            layer.msg("请编辑合同有效期内，租金需缴纳的次数与每次需缴纳的金额");
            return false;
        }
        let source = $("#editSite").data("source");
        let pics = [], taxCategory = [], url = ``;
        let part0 =  $("#editSite .part0");
        let param = {
            "ownership": part0.find("select:eq(0)").val(),
            "address": part0.find("input[name=address]").val(),
            "totalArea": part0.find("input[name=totalArea]").val(),
            "buildingArea": part0.find("input[name=buildingArea]").val()
        };
        url=`../site/addOtherSiteInfo.do`;
        $("#editSite .categoryItem:visible").each(function (){
            let item = {}, empty = 0;
            $(this).find("input").each(function () {
                if ($(this).val() === "") empty++;
                let name = $(this).attr("name");
                if (name === 'payBeginTime' || name === 'payEndTime') {
                    item[name] = $(this).data("val");
                } else {
                    item[name] = $(this).val();
                }
            })
            if (empty < 4) {
                taxCategory.push(item);
            }
        })
        $("#imageList .filePic:visible").each(function (){
            let path = $(this).attr("path");
            pics.push(path);
        })
        param.imageList = JSON.stringify(pics);
        param.siteTaxList = JSON.stringify(taxCategory);
        if (part0.find("select:eq(0)").val() === '2'){
            let contractPic = [], conDoc = [];
            $("#contractPic .filePic:visible").each(function (){
                let path = $(this).attr("path");
                contractPic.push(path);
            })
            $("#contractDoc .fileIm:visible").each(function (){
                let file = JSON.parse($(this).find(".hd").html());
                conDoc.push(file.filename);
            })
            param.contractImageList = JSON.stringify(contractPic);
            param.contractFileList = JSON.stringify(conDoc);
            param.rentChargeList = $("#otherPayBox").find(".hd").html() === ""?"[]":$("#otherPayBox").find(".hd").html();
            $(".part1").find("input[require]").each(function () {
                let name = $(this).attr("name");
                param[name] = $(this).val();
            })
            $(".part1").find("select[require]").each(function () {
                let name = $(this).attr("name");
                if ($(this).val() === '1'){
                    param[name] = true;
                } else if ($(this).val() === '0'){
                    param[name] = false;
                }
            });
            let rentInitialList = [];
            $(".edit_hydroGas").find(".modItem-s").each(function (){
                let jak = $(this).index();
                let item = {};
                let val = $(this).find("input").val();
                if (val !== "") {
                    if (jak === 0) {
                        item =  {"amount": val, "item": "水"}
                    } else if (jak === 1) {
                        item =  {"amount": val, "item": "电"}
                    } else if (jak === 2) {
                        item =  {"amount": val, "item": "煤气"}
                    }
                    rentInitialList.push(item);
                }
            })
            for (var t=0;t<3;t++) {
                let val = $(".part3 .costs:eq(1) input[name='item']:eq("+ t +")").val();
                let amount = $(".part3 .costs:eq(1) input[name='amount']:eq("+ t +")").val();
                if (val !== "" || amount  !== "") {
                    if (amount === ""){ amount = 0;}
                    let item = {"amount": amount, "item": val}
                    rentInitialList.push(item);
                }
            }
            param.rentInitialList = JSON.stringify(rentInitialList);
        }
        $.ajax({
            "url": url,
            "data": {'json': JSON.stringify(param)} ,
            success:function(res) {
                bounce_Fixed.cancel();
                // getBusiness();
                startmess();
            }
        });
    }
}

//creator: sy 2023-01-13 创建机构地址确定按钮
function addsysress(){
    let filled = 0;
    $("#editSite input:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    $("#editSite select:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    if ($("#editSite .imgsthumb").length > 0){filled++;}
    if ($("#editSite .fileIm").length > 0){filled++;}
    if (filled > 0) {
        if ($("#editSite input[name='totalArea']").val() === "") {
            layer.msg("请录入总面积");
            return false;
        }
        if ($("#editSite input[name='buildingArea']").val() === "") {
            layer.msg("请录入建筑面积");
            return false;
        }
        if ($("#editSite input[name='pledge']:visible").val() === "") {
            layer.msg("请录入场地押金");
            return false;
        }
        let num = 0,dateNum =0;
        $("#editSite input[need][name='amount']:visible").each(function (){
            if ($(this).val() === "") {
                num++;
            }
        })
        if (num > 0){
            layer.msg("请录入金额");
            return false;
        }
        $("#editSite input[name='payBeginTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        $("#editSite input[name='payEndTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        if (dateNum > 0){
            layer.msg("请录入应缴纳税款的时间区间");
            return false;
        }
        if ($("#editSite select[name='hasInvoice']:visible").val() === "") {
            layer.msg("请选择租金是否含税");
            return false;
        }
        if ($("#editSite select[name='hasTax']:visible").val() === "") {
            layer.msg("请选择是否需额外缴纳税款");
            return false;
        }
        if ($("#editSite #otherPayBox:visible").html() === "") {
            layer.msg("请编辑合同有效期内，租金需缴纳的次数与每次需缴纳的金额");
            return false;
        }
        let source = $("#editSite").data("source");
        let pics = [], taxCategory = [], url = ``;
        let part0 =  $("#editSite .part0");
        let param = {
            "ownership": part0.find("select:eq(0)").val(),
            "address": part0.find("input[name=address]").val(),
            "totalArea": part0.find("input[name=totalArea]").val(),
            "buildingArea": part0.find("input[name=buildingArea]").val()
        };
        url=`../site/updateOrgSiteInfo.do`;
        $("#editSite .categoryItem:visible").each(function (){
            let item = {}, empty = 0;
            $(this).find("input").each(function () {
                if ($(this).val() === "") empty++;
                let name = $(this).attr("name");
                if (name === 'payBeginTime' || name === 'payEndTime') {
                    item[name] = $(this).data("val");
                } else {
                    item[name] = $(this).val();
                }
            })
            if (empty < 4) {
                taxCategory.push(item);
            }
        })
        $("#imageList .filePic:visible").each(function (){
            let path = $(this).attr("path");
            pics.push(path);
        })
        param.imageList = JSON.stringify(pics);
        param.siteTaxList = JSON.stringify(taxCategory);
        if (part0.find("select:eq(0)").val() === '2'){
            let contractPic = [], conDoc = [];
            $("#contractPic .filePic:visible").each(function (){
                let path = $(this).attr("path");
                contractPic.push(path);
            })
            $("#contractDoc .fileIm:visible").each(function (){
                let file = JSON.parse($(this).find(".hd").html());
                conDoc.push(file.filename);
            })
            param.contractImageList = JSON.stringify(contractPic);
            param.contractFileList = JSON.stringify(conDoc);
            param.rentChargeList = $("#otherPayBox").find(".hd").html() === ""?"[]":$("#otherPayBox").find(".hd").html();
            $(".part1").find("input[require]").each(function () {
                let name = $(this).attr("name");
                param[name] = $(this).val();
            })
            $(".part1").find("select[require]").each(function () {
                let name = $(this).attr("name");
                if ($(this).val() === '1'){
                    param[name] = true;
                } else if ($(this).val() === '0'){
                    param[name] = false;
                }
            });
            let rentInitialList = [];
            $(".edit_hydroGas").find(".modItem-s").each(function (){
                let jak = $(this).index();
                let item = {};
                let val = $(this).find("input").val();
                if (val !== "") {
                    if (jak === 0) {
                        item =  {"amount": val, "item": "水"}
                    } else if (jak === 1) {
                        item =  {"amount": val, "item": "电"}
                    } else if (jak === 2) {
                        item =  {"amount": val, "item": "煤气"}
                    }
                    rentInitialList.push(item);
                }
            })
            for (var t=0;t<3;t++) {
                let val = $(".part3 .costs:eq(1) input[name='item']:eq("+ t +")").val();
                let amount = $(".part3 .costs:eq(1) input[name='amount']:eq("+ t +")").val();
                if (val !== "" || amount  !== "") {
                    if (amount === ""){ amount = 0;}
                    let item = {"amount": amount, "item": val}
                    rentInitialList.push(item);
                }
            }
            param.rentInitialList = JSON.stringify(rentInitialList);
        }
        $.ajax({
            "url": url,
            "data": {'json': JSON.stringify(param)} ,
            success:function(res) {
                bounce_Fixed.cancel();
                // siteInfo();
                // getBusiness();
                startmess();
            }
        });
    }
}

// creator: sy 2023-01-16 其它地址信息
function siteInfo1(obj,chosse){
    $("#siteInfoder").html("其他地址");
    $("#siteDetail tbody").html("");
    $("#siteTaxList tbody tr:gt(0)").remove();
    $("#siteTaxList").hide();
    $(".jgone").hide();
    $(".qtone").show();
    $(".jgtwo").hide();
    $(".qttwo").show();
    $(".jgthree").hide();
    $(".qtthree").show();
    if(chosse == 1){
        var unid = obj;
    }else if(chosse == 0){
        var unid = obj.siblings(".hd").html();
        $(".qtthree").data("id",unid);
    }
    $.ajax({
        "url":"../site/getOrgSiteInfo.do",
        data:{
            "siteId":unid
        },
        success:function(res){
            let detail = res.data.orgSite;
            let rentImageList = res.data.rentImageList;
            let rentInitialList = res.data.rentInitialList;
            let siteTaxList = res.data.siteTaxList,siteTaxStr = ``;
            let siteImageList = res.data.siteImageList;
            let rentChargeList = res.data.rentChargeList;
            let siteRent = res.data.siteRent;
            let address = ``,ownership = `信息尚未录入`;
            let html = ``,sitePics = ``;
            $("#siteStorage").html(JSON.stringify(res.data));
            $(".siteInfoCare").show();
            $("#hydroGas").hide();
            if(detail){
                address = detail.address;
                ownership = transformStr(detail.ownership,'siteType');
            }
            html =`
                <tr>
                    <td width="35%">具体地址</td>
                    <td width="65%" colspan="3">${address}</td>
                </tr>
                <tr>
                    <td>场地情况</td>
                    <td colspan="3">${ownership}</td>
                </tr>
            `;
            if(detail){
                for(let i=0;i<siteImageList.length;i++){
                    sitePics += `<a path="${siteImageList[i].uplaodPath}" onclick="seeOnline($(this))">${i+1}</a>`;
                }
                html += `<tr><td>场地照片</td><td colspan="3" class="comPics">${sitePics}</td></tr>
                    <tr>
                        <td>总面积</td>
                        <td>${handleNull(detail.totalArea).toFixed(2)}平方米</td>
                        <td>建筑面积</td>
                        <td>${handleNull(detail.buildingArea).toFixed(2)}平方米</td>
                    </tr> `;
                if(handleNull(detail.ownership) !== ""){
                    $(".siteInfoCare").hide();
                    if(detail.ownership === "1"){}
                    else if(detail.ownership === "2"){
                        let pic = ``,file = ``;
                        for(let i =0;i<rentImageList.length;i++){
                            if(rentImageList[i].type === "1"){
                                pic += `<a path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">${rentImageList[i].orders}</a>`;
                            }else{
                                file +=`<a path="${rentImageList[i].uplaodPath}" onclick="seeOnline($(this))">查看</a>`;
                            }
                        }
                        if(siteRent){
                            html +=`<tr><td>租赁合同的扫描件或照片</td><td cospan="3" class="comPics">${pic}</td></tr>
                                    <tr>
                                        <td>租赁合同的可编辑版本</td>
                                        <td>${file}</td>
                                        <td>场地押金</td>
                                        <td>${handleNull(siteRent.pledge).toFixed(2)}元</td>
                                    </tr>
                                    <tr>
                                        <td>合同开始日期</td>
                                        <td>${new Date(siteRent.beginTime).format('yyyy-MM-dd')}</td>
                                        <td>合同结束日期</td>
                                        <td>${new Date(siteRent.endTime).format('yyyy-MM-dd')}</td>
                                    </tr>
                                    <tr>
                                        <td>每年租金</td>
                                        <td>${handleNull(siteRent.amount).toFixed(2)}元</td>
                                        <td>房东联系方式</td>
                                        <td>${handleNull(siteRent.landlord)}</td>
                                    </tr>
                                    <tr>
                                        <td>租金需缴纳的次数与金额</td>
                                        <td colspan="3">${morePayStr(rentChargeList)}</td>
                                    </tr>
                                    <tr>
                                        <td>是否需额外缴纳税款</td>
                                        <td>${transformStr(siteRent.hasTax, 'hasTax')}</td>
                                        <td>租金是否含税</td>
                                        <td>${transformStr(siteRent.hasInvoice , 'rentHasTax')}</td>
                                    </tr>`;
                        }
                        if(rentInitialList && rentInitialList.length >0){
                            $("#hydroGas").show();
                            let combin = ``,tab1 = ``,tab2 = ``;
                            let water = ``,ele= ``,gas = ``,sNum = 0;
                            let idx1 = rentInitialList.findIndex(value => value.item === '水');
                            let idx2 = rentInitialList.findIndex(value => value.item === '电');
                            let idx3 = rentInitialList.findIndex(value => value.item === '煤气');
                            if(idx1 >-1){
                                sNum++;
                                water = Number(rentInitialList[idx1].amount).toFixed(2);
                            }
                            if (idx2 > -1) {
                                sNum++;
                                ele = Number(rentInitialList[idx2].amount).toFixed(2);
                            }
                            if (idx3 > -1) {
                                sNum++;
                                gas = Number(rentInitialList[idx3].amount).toFixed(2);
                            }
                            tab1 = `<tr><td>水</td><td>电</td><td>煤气</td></tr>
                                    <tr><td>${water}</td><td>${ele}</td><td>${gas}</td></tr>`;
                            tab1 += `<tr>`;
                            tab2 += `<tr>`;
                            for (let b = 0; b < 6; b++) {
                                if (b < rentInitialList.length) {
                                    if (rentInitialList[b].item !== "水" && rentInitialList[b].item !== "电" && rentInitialList[b].item !== "煤气") {
                                        tab1 += `<td>${rentInitialList[b].item}</td>`;
                                        tab2 += `<td>${(rentInitialList[b].amount).toFixed(2)}元</td>`;
                                    }
                                } else if (b >= -(-6 + (3 - (rentInitialList.length - sNum)))){
                                    tab1 += `<td></td>`;
                                    tab2 += `<td></td>`;
                                }
                            }
                            tab1 += `</tr>`;
                            tab2 += `</tr>`;
                            combin += tab1 + tab2;
                            $("#hydroGas tbody").html(combin);
                        }
                    }
                    if(siteTaxList && siteTaxList.length > 0) {
                        for(let a=0;a<siteTaxList.length;a++){
                            siteTaxStr += `<tr>
                                <td>${siteTaxList[a].category}</td>
                                <td>${(siteTaxList[a].amount).toFixed(2)}元</td>
                                <td>${new Date(siteTaxList[a].payBeginTime).format('MM-dd')} 至 ${new Date(siteTaxList[a].payEndTime).format('MM-dd')}</td>
                            </tr>`;
                        }
                        $("#siteTaxList tbody").append(siteTaxStr);
                        $("#siteTaxList").show();
                    }
                }
            }
            $('#siteDetail tbody').html(html);
            bounce.show($('#siteInformation'));
        }
    })
}

// creator: sy 2023-01-17 其它地址编辑
function upotherSure(){
    let filled = 0;
    $("#editSite input:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    $("#editSite select:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    if ($("#editSite .imgsthumb").length > 0){filled++;}
    if ($("#editSite .fileIm").length > 0){filled++;}
    if (filled > 0) {
        if ($("#editSite input[name='totalArea']").val() === "") {
            layer.msg("请录入总面积");
            return false;
        }
        if ($("#editSite input[name='buildingArea']").val() === "") {
            layer.msg("请录入建筑面积");
            return false;
        }
        if ($("#editSite input[name='pledge']:visible").val() === "") {
            layer.msg("请录入场地押金");
            return false;
        }
        let num = 0,dateNum =0;
        $("#editSite input[need][name='amount']:visible").each(function (){
            if ($(this).val() === "") {
                num++;
            }
        })
        if (num > 0){
            layer.msg("请录入金额");
            return false;
        }
        $("#editSite input[name='payBeginTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        $("#editSite input[name='payEndTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        if (dateNum > 0){
            layer.msg("请录入应缴纳税款的时间区间");
            return false;
        }
        if ($("#editSite select[name='hasInvoice']:visible").val() === "") {
            layer.msg("请选择租金是否含税");
            return false;
        }
        if ($("#editSite select[name='hasTax']:visible").val() === "") {
            layer.msg("请选择是否需额外缴纳税款");
            return false;
        }
        if ($("#editSite #otherPayBox:visible").html() === "") {
            layer.msg("请编辑合同有效期内，租金需缴纳的次数与每次需缴纳的金额");
            return false;
        }
        let source = $("#editSite").data("source");
        let pics = [], taxCategory = [], url = ``;
        let part0 =  $("#editSite .part0");
        let param = {
            "ownership": part0.find("select:eq(0)").val(),
            "address": part0.find("input[name=address]").val(),
            "totalArea": part0.find("input[name=totalArea]").val(),
            "buildingArea": part0.find("input[name=buildingArea]").val()
        };
        url = `../site/updateOtherSiteInfo.do`;
        $("#editSite .categoryItem:visible").each(function (){
            let item = {}, empty = 0;
            $(this).find("input").each(function () {
                if ($(this).val() === "") empty++;
                let name = $(this).attr("name");
                if (name === 'payBeginTime' || name === 'payEndTime') {
                    item[name] = $(this).data("val");
                } else {
                    item[name] = $(this).val();
                }
            })
            if (empty < 4) {
                taxCategory.push(item);
            }
        })
        $("#imageList .filePic:visible").each(function (){
            let path = $(this).attr("path");
            pics.push(path);
        })
        param.imageList = JSON.stringify(pics);
        param.siteTaxList = JSON.stringify(taxCategory);
        if (part0.find("select:eq(0)").val() === '2'){
            let contractPic = [], conDoc = [];
            $("#contractPic .filePic:visible").each(function (){
                let path = $(this).attr("path");
                contractPic.push(path);
            })
            param.contractImageList = JSON.stringify(contractPic);
            param.contractFileList = JSON.stringify(conDoc);
            param.rentChargeList = $("#otherPayBox").find(".hd").html() === ""?"[]":$("#otherPayBox").find(".hd").html();
            $(".part1").find("input[require]").each(function () {
                let name = $(this).attr("name");
                param[name] = $(this).val();
            })
            $(".part1").find("select[require]").each(function () {
                let name = $(this).attr("name");
                if ($(this).val() === '1'){
                    param[name] = true;
                } else if ($(this).val() === '0'){
                    param[name] = false;
                }
            });
            let rentInitialList = [];
            $(".edit_hydroGas").find(".modItem-s").each(function (){
                let jak = $(this).index();
                let item = {};
                let val = $(this).find("input").val();
                if (val !== "") {
                    if (jak === 0) {
                        item =  {"amount": val, "item": "水"}
                    } else if (jak === 1) {
                        item =  {"amount": val, "item": "电"}
                    } else if (jak === 2) {
                        item =  {"amount": val, "item": "煤气"}
                    }
                    rentInitialList.push(item);
                }
            })
            for (var t=0;t<3;t++) {
                let val = $(".part3 .costs:eq(1) input[name='item']:eq("+ t +")").val();
                let amount = $(".part3 .costs:eq(1) input[name='amount']:eq("+ t +")").val();
                if (val !== "" || amount  !== "") {
                    if (amount === ""){ amount = 0;}
                    let item = {"amount": amount, "item": val}
                    rentInitialList.push(item);
                }
            }
            param.rentInitialList = JSON.stringify(rentInitialList);
        }
        var unid = $(".qtthree").data("id");
        $.ajax({
            "url":url,
            "data":{
                "siteId":unid,
                "json":JSON.stringify(param)
            },
            success:function(res){
                bounce_Fixed.cancel();
                // siteInfo();
                // startmess();
                var band = $(".qtthree").data("id");
                siteInfo1(band,1);
                startmess();
            }
        })
    }
}

// creator: sy 2023-01-17 其它地址更换新地址
function upnewotherSure(){
    let filled = 0;
    $("#editSite input:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    $("#editSite select:visible").each(function (){
        if ($(this).val() !== "") filled++;
    });
    if ($("#editSite .imgsthumb").length > 0){filled++;}
    if ($("#editSite .fileIm").length > 0){filled++;}
    if (filled > 0) {
        if ($("#editSite input[name='totalArea']").val() === "") {
            layer.msg("请录入总面积");
            return false;
        }
        if ($("#editSite input[name='buildingArea']").val() === "") {
            layer.msg("请录入建筑面积");
            return false;
        }
        if ($("#editSite input[name='pledge']:visible").val() === "") {
            layer.msg("请录入场地押金");
            return false;
        }
        let num = 0,dateNum =0;
        $("#editSite input[need][name='amount']:visible").each(function (){
            if ($(this).val() === "") {
                num++;
            }
        })
        if (num > 0){
            layer.msg("请录入金额");
            return false;
        }
        $("#editSite input[name='payBeginTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        $("#editSite input[name='payEndTime']:visible").each(function (){
            if ($(this).val() === "") {
                dateNum++;
            }
        })
        if (dateNum > 0){
            layer.msg("请录入应缴纳税款的时间区间");
            return false;
        }
        if ($("#editSite select[name='hasInvoice']:visible").val() === "") {
            layer.msg("请选择租金是否含税");
            return false;
        }
        if ($("#editSite select[name='hasTax']:visible").val() === "") {
            layer.msg("请选择是否需额外缴纳税款");
            return false;
        }
        if ($("#editSite #otherPayBox:visible").html() === "") {
            layer.msg("请编辑合同有效期内，租金需缴纳的次数与每次需缴纳的金额");
            return false;
        }
        let source = $("#editSite").data("source");
        let pics = [], taxCategory = [], url = ``;
        let part0 =  $("#editSite .part0");
        let param = {
            "ownership": part0.find("select:eq(0)").val(),
            "address": part0.find("input[name=address]").val(),
            "totalArea": part0.find("input[name=totalArea]").val(),
            "buildingArea": part0.find("input[name=buildingArea]").val()
        };
        url = `../site/otherSiteReplaceNewSite.do`;
        $("#editSite .categoryItem:visible").each(function (){
            let item = {}, empty = 0;
            $(this).find("input").each(function () {
                if ($(this).val() === "") empty++;
                let name = $(this).attr("name");
                if (name === 'payBeginTime' || name === 'payEndTime') {
                    item[name] = $(this).data("val");
                } else {
                    item[name] = $(this).val();
                }
            })
            if (empty < 4) {
                taxCategory.push(item);
            }
        })
        $("#imageList .filePic:visible").each(function (){
            let path = $(this).attr("path");
            pics.push(path);
        })
        param.imageList = JSON.stringify(pics);
        param.siteTaxList = JSON.stringify(taxCategory);
        if (part0.find("select:eq(0)").val() === '2'){
            let contractPic = [], conDoc = [];
            $("#contractPic .filePic:visible").each(function (){
                let path = $(this).attr("path");
                contractPic.push(path);
            })
            $("#contractDoc .fileIm:visible").each(function (){
                let file = JSON.parse($(this).find(".hd").html());
                conDoc.push(file.filename);
            })
            param.contractImageList = JSON.stringify(contractPic);
            param.contractFileList = JSON.stringify(conDoc);
            param.rentChargeList = $("#otherPayBox").find(".hd").html() === ""?"[]":$("#otherPayBox").find(".hd").html();
            $(".part1").find("input[require]").each(function () {
                let name = $(this).attr("name");
                param[name] = $(this).val();
            })
            $(".part1").find("select[require]").each(function () {
                let name = $(this).attr("name");
                if ($(this).val() === '1'){
                    param[name] = true;
                } else if ($(this).val() === '0'){
                    param[name] = false;
                }
            });
            let rentInitialList = [];
            $(".edit_hydroGas").find(".modItem-s").each(function (){
                let jak = $(this).index();
                let item = {};
                let val = $(this).find("input").val();
                if (val !== "") {
                    if (jak === 0) {
                        item =  {"amount": val, "item": "水"}
                    } else if (jak === 1) {
                        item =  {"amount": val, "item": "电"}
                    } else if (jak === 2) {
                        item =  {"amount": val, "item": "煤气"}
                    }
                    rentInitialList.push(item);
                }
            })
            for (var t=0;t<3;t++) {
                let val = $(".part3 .costs:eq(1) input[name='item']:eq("+ t +")").val();
                let amount = $(".part3 .costs:eq(1) input[name='amount']:eq("+ t +")").val();
                if (val !== "" || amount  !== "") {
                    if (amount === ""){ amount = 0;}
                    let item = {"amount": amount, "item": val}
                    rentInitialList.push(item);
                }
            }
            param.rentInitialList = JSON.stringify(rentInitialList);
        }
        var uaid = $(".qtthree").data("id");
        $.ajax({
            "url": url,
            "data": {
                'siteId':uaid,
                'json': JSON.stringify(param)
            },
            success: function (data) {
                bounce_Fixed.cancel();
                var band = $(".qtthree").data("id");
                siteInfo1(band,1);
                startmess();
            }
        });
    }
}

laydate.render({elem: '#contractBeginTime', format: 'yyyy-MM-dd'});
laydate.render({elem: '#contractEndTime', format: 'yyyy-MM-dd'});
