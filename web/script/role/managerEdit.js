/**
 * Created by Administrator on 2017/7/7 0007.
 */
$(function () {
    $(".ty-firstTab li").click(function (index) {
        $(".ty-firstTab li").removeClass("ty-active");
        $(this).addClass("ty-active");
        var index = $(this).index();
        $("#third_n").html($(this).html());
        $(".ty-mainData .tab").addClass("hd");
        $(".ty-mainData .tab").eq(index).removeClass("hd");
    })
});
/**
 *方法editIt的功能描述：编辑部门信息
 *<PRE>
 *<AUTHOR>
 *@role updator
 *@date 2017-03-09
 *<PRE>
 **/
function editIt(aa){
    if (chargeRole("超管")) {
        bounce.show( $("#mtTip") ); $("#mt_tip_ms").html("您没有此权限！");
        return false ;
    }
    var editItId=parseInt($("button.active").val());
//        var editItId = $(aa).attr("name");
    if(editItId==""){
        bounce.show( $("#mtTip") ); $("#mt_tip_ms").html("请点击本行左侧按钮后，再点击当前按钮 ");
    }else{
        openWindow('../general/editIt.do?id='+editItId,'700','400');
    }
}
/**
 *方法deleteIt的功能描述：删除部门
 *<PRE>
 *<AUTHOR>
 *@role updator
 *@date 2017-03-09
 *<PRE>
 **/
function deleteIt(aa){
    if (chargeRole("超管")) {
        bounce.show( $("#mtTip") ); $("#mt_tip_ms").html("您没有此权限！");
        return false ;
    }
    var deleteItId = parseInt($("button.active").val());
    if(deleteItId==""){
        bounce.show( $("#mtTip") ); $("#mt_tip_ms").html(" 请点击本行左侧按钮后，再点击当前按钮");
    }else{
        $.ajax({
            url:'../general/deleteJudge.do?id='+deleteItId,
            contentType: "charset=UTF-8",
            dataType:'Text',
            success:function(course){
                if (course=="0"){
                    bounce.show( $("#mtTip") ); $("#mt_tip_ms").html(" 对不起，该部门下面有子部门，不可删除");
                }else if(course=="2"){
                    bounce.show( $("#mtTip") ); $("#mt_tip_ms").html(" 对不起，该部门下有职员，不可删除");
                }
                else{
                    openWindow('../general/deleteIt.do?id='+deleteItId,'700','400');
                }
            }
        })
    }
}
/**
 *方法addSameLevel的功能描述：添加同级部门
 *<PRE>
 *<AUTHOR>
 *@role updator
 *@date 2017-03-09
 *<PRE>
 **/
function addSameLevel(aa){
    var sameLevelID = $(aa).parent().prev().children(":eq(0)").attr("value") ;
    if (chargeRole("超管")) {
        bounce.show( $("#mtTip") ); $("#mt_tip_ms").html("您没有此权限！");
        return false ;
    }
    if(sameLevelID == ""){
        bounce.show( $("#mtTip") ); $("#mt_tip_ms").html(" 请点击本行左侧按钮后，再点击当前按钮");
    }else{
        openWindow('../general/addSameLevel.do?id='+sameLevelID,'700','400');
    }

}
/*   updater:张旭博
 *   creatTime:2017-03-07 09:52:00
 *   删除必须点击左侧按钮才能点击的判断
 */
/**
 *方法addChildren的功能描述：添加子级部门
 *<PRE>
 *<AUTHOR>
 *@role updator
 *@date 2017-03-09
 *<PRE>
 **/
function addChildren(aa){

    if (chargeRole("超管")) {
        bounce.show( $("#mtTip") ); $("#mt_tip_ms").html("您没有此权限！");
        return false ;
    }
    var editItId=parseInt($("button.active").val());
    var addSameLevelId = $(aa).attr("name");
    var hang = $("#gid").children("div").length;
    var a = $(aa).parents(".parent1").attr("id");
    var b = $("#gid").children("div").eq(hang-1).attr("id");
    if((a==b)&&(hang>7)){
        bounce.show( $("#mtTip") ); $("#mt_tip_ms").html("不可以继续添加了！");
    }else{
        var idAndName = null;
        var yhzh = parseInt($(aa).parents('.parent1').find("button").length);
        var handleL=parseInt($(aa).parents('.parent1').find("button.handle").length);
        yhzh=yhzh-handleL;
        for(var i= 0;i<yhzh;i++){
            var tdId = $(aa).parents('.parent1').find("button").eq(i).attr("value");
            idAndName = idAndName +"+"+ tdId ;
            var tdName = $(aa).parents('.parent1').find("button").eq(i).text();
            idAndName = idAndName +"+"+ tdName;
        }
        openWindow('../general/addChildren.do?idAndName='+idAndName,'700','400');
    }
}
/**
 *方法yibu的功能描述：点击部门触发的事件
 *<PRE>
 *<AUTHOR>
 *@role updator
 *@date 2017-03-09
 *<PRE>
 **/

//anniu 部门按钮
function yibu(anniu,orgId){
    $("button").removeClass('active');
    $(anniu).addClass('active');
    $.ajax({
        url:'../general/querySubsystem.do?id='+orgId,
        contentType: "charset=UTF-8",
//            dataType:'text',
        success:function(course){
            console.log(course);
            var hang = $(".parent1").length;
            var hangNow=$(anniu).parent().parent().index();
            $("#gid").find('.edit').remove();
            $("#gid").find('.delete').remove();
            addHandle(hangNow);
            if (course=="cuowu"){
                var dq = $(anniu).parent().parent().attr("id").substring(4,5);
                $("#pdiv"+dq).nextAll().remove();
                return false ;
                kongAdd(anniu);
//                       //以下为了解决子级太多，再点击一个一级部门，会一级一级消失的问题
                var strList = course.split("+");
                sss(anniu,strList.length-1,strList);


            }else{
                var strList = course.split("+");
                sss(anniu,strList.length-1,strList);

                var ydivId = $(".parent1").last().find("div").eq(0).attr("id").substring(4,5);
                var zdivId = $(anniu).parent().parent().parent().find("div").last().eq(0).attr("id").substring(4,5);
                if(ydivId == zdivId){
                    $("#ydiv"+(zdivId-1)).find("button").eq(-4).attr("name",$(anniu).attr("value"));
                    $("#ydiv"+(zdivId-1)).find("button").eq(-3).attr("name",$(anniu).attr("value"));
                    $("#ydiv"+(zdivId-1)).find("button").eq(-2).attr("name",$(anniu).attr("value"));
                    $("#ydiv"+(zdivId-1)).find("button").eq(-1).attr("name",$(anniu).attr("value"));
                }
            }
        }
    })
}

function kongAdd(anniu){
    var bianliang = $(anniu).attr("value");
    var txt1="<button onclick='editIt(this)' class='btn blue' name="+bianliang+" style=\"width: 100px;margin-right: 10px;\">编辑</button>";
    var txt2="<button onclick='deleteIt(this)' class='btn blue' name="+bianliang+" style=\"width: 100px;margin-right: 10px;\">删除</button>";
    var ydivid = $(anniu).parents(".parent1").last().find("div").attr("id").substring(4,5);
    var td3=$("#ydiv"+ydivid).find("button").eq(-4).text();
    var td4=$("#ydiv"+ydivid).find("button").eq(-3).text();
    if(td3!='编辑'&&td4!='删除'){
        $("#ydiv"+ydivid).find("button").eq(-2).attr("name",bianliang);
        $("#ydiv"+ydivid).find("button").eq(-1).attr("name",bianliang);
        $("#ydiv"+ydivid).find("button").eq(-2).before(txt1,txt2);
    }else{
        $("#ydiv"+ydivid).find("button").eq(-4).attr("name",bianliang);
        $("#ydiv"+ydivid).find("button").eq(-3).attr("name",bianliang);
        $("#ydiv"+ydivid).find("button").eq(-2).attr("name",bianliang);
        $("#ydiv"+ydivid).find("button").eq(-1).attr("name",bianliang);
    }
}
/**
 *方法sss的功能描述：
 *<PRE>
 *<AUTHOR>
 *@role updator
 *@date 2017-03-09
 *<PRE>
 **/
//部门，次级部门的大小，次级部门内容
function sss(anniu,cishu,strList) {
    var hang = $(".parent1").length;//级数
    if (hang == 1) {
        //第一次进入的时候
        var bianliang = $(anniu).attr("value");
        var txt1 = "<button onclick='editIt(this)' class='btn blue' name=" + bianliang + " style=\"width: 100px;margin-right: 10px;\">编辑</button>";
        var txt2 = "<button onclick='deleteIt(this)' class='btn blue' name=" + bianliang + " style=\"width: 100px;margin-right: 10px;\">删除</button>";
        var ydivid = $(anniu).parents(".parent1").attr("id").substring(4,5);
        var td3=$("#ydiv"+ydivid).find("button").eq(-4).text();
        var td4=$("#ydiv"+ydivid).find("button").eq(-3).text();
        if(td3!='编辑'&&td4!='删除'){
            $("#ydiv"+ydivid).find("button").eq(-2).attr("name",bianliang);
            $("#ydiv"+ydivid).find("button").eq(-1).attr("name",bianliang);
            $("#ydiv"+ydivid).find("button").eq(-2).before(txt1,txt2);
        }else{
            $("#ydiv"+ydivid).find("button").eq(-4).attr("name",bianliang);
            $("#ydiv"+ydivid).find("button").eq(-3).attr("name",bianliang);
            $("#ydiv"+ydivid).find("button").eq(-2).attr("name",bianliang);
            $("#ydiv"+ydivid).find("button").eq(-1).attr("name",bianliang);
        }
        addElement(anniu, hang, cishu, strList);
    } else {
        //第一次之后进入的时候  (table中的表格第一行是零，每行的td是从1开始的)
        var dq = $(anniu).parents('.parent1').attr("id").substring(4, 5);
        var hang = parseInt($(".parent1").length, 10);
        var zh = $(".parent1").eq(hang - 1).attr("id").substring(2, 3);
        if (dq == zh) {
            addElement(anniu, hang, cishu, strList);
        } else {
            $("#pdiv"+dq).nextAll().remove();
            addElement(anniu, parseInt(dq) + 1, cishu, strList);
        }
    }
}
function addElement(anniu,hang,cishu,strList){
    var tr = null;

    var tr2 = null;
    var tr3 = null;
    for(var v=0;v<cishu;v++){
        t = v + 1;
        if(v==0){
            tr = "<button value="+strList[v]+" class='btn btn-sm green' style='margin-left: 10px;margin-top: 10px;' onclick='yibu(this,"+strList[v]+")'>"+strList[t]+"</button>";
        }else{
            tr = tr + "<button value="+strList[v]+" class='btn btn-sm green'  style='margin-left: 10px;margin-top: 10px;' onclick='yibu(this,"+strList[v]+")'>"+strList[t]+"</button>";
        }
        v = v + 1;
    }
    tr = "<div style=\"width: 700px;margin-top:20px;\" id="+"zdiv"+(hang)+">"+tr+"</div>";
    tr3 = "<button onclick='addSameLevel(this)' class='btn blue handle' name=\"\" style=\"width: 100px;margin-right: 4px;\">新增同级部门</button>";
    tr3 = tr3 + "<button onclick='addChildren(this)' class='btn blue handle' name=\"\"style=\"width: 100px;\">新增子级部门</button>";
    tr3 = "<div id="+"ydiv"+(hang)+" style=\"position: absolute;right:0px;margin-top:-45px;\">"+tr3+"</div>";
    tr2 = "<div id="+"pdiv"+hang+" class=\"parent1\" style='margin-top:20px;'>"+tr+tr3+"</div>";
    $(anniu).parents("#gid").append(tr2);
}
function addHandle(hang){
    var tr1 = null;
    tr1 = "<button onclick='editIt(this)' class='btn blue handle edit' name=\"\" style=\"width: 100px;margin-right: 10px;\">编辑</button>";
    tr1 = tr1 + "<button onclick='deleteIt(this)' class='btn blue handle delete' name=\"\" style=\"width: 100px;margin-right: 10px;\">删除</button>";
    $("#ydiv"+hang).prepend(tr1)
}
function secondEdit(aa){
    var usertype = chargeRole("超管");
    if (usertype) {
        $("#mtTip #mt_tip_ms").html("您没有此权限！");
        bounce.show($("#mtTip"));
    }else{
        var positionId = $(aa).parents(".tdp").find("input").eq(0).attr("value");
        bounce.show($("#positionEdit"));
        $("#editPositionId").val(positionId);
    }
}

function secondDelete(aa){
    var usertype = chargeRole("超管");
    if (usertype) {
        $("#mtTip #mt_tip_ms").html("您没有此权限！");
        bounce.show($("#mtTip"));
    }else{
        var positionId = $(aa).parents(".tdp").find("input").eq(0).attr("value");
        $("#positionId").val(positionId);
        $.ajax({
            url:'../general/checkPost.do?id='+positionId,
            contentType: "charset=UTF-8",
            dataType:'Text',
            success:function(course){
                if(course==0){
                    bounce.show( $("#mtTip") ); $("#mt_tip_ms").html(" 该职位下还有员工，不可删除");
                }else {
                    bounce.show($("#deletePosition"));
                }
            }
        });
    }

}

function addNewPosition(){
    var usertype = chargeRole("超管");
    if (usertype) {
        $("#mtTip #mt_tip_ms").html("您没有此权限！");
        bounce.show($("#mtTip"));
    }else{
        bounce.show($("#addPosition"));
    }

}

/**
 *方法setChoose的功能描述：设置选中状态
 *<PRE>
 *<AUTHOR>
 *@role updator
 *@date 2017-03-09
 *<PRE>
 **/
//  总务管理-岗位管理-职位列表 -删除
function sureDelete(){
    var positionId = $("#positionId").val();
    $.ajax({
        url:"../general/positionDeleteSubmit.do",
        data:{"positionId":positionId},
        type : "post" ,
        beforeSend:function(){ loading.open(); },
        success:function(data){
            console.log(data);
            location.href="/general/postManagement.do";
        },
        error:function(){
            alert("error");
        },
        complete:function(){ loading.close();  }
    });
}
//        总务管理-岗位管理-职位列表 -编辑
function sureEdit(){
    var positionId=$("#editPositionId").val();
    var positionName=$("#editPositionName").val();
    $.ajax({
        url:"../general/positionEditSubmit.do" ,
        type:"post",
        data:{"positionId":positionId, "positionName":positionName},
        beforeSend:function(){ loading.open();},
        success:function(data){
            console.log(data);
            location.href="/general/postManagement.do";
        },
        complete:function(){loading.close();}
    });
}
//   总务管理-岗位管理-职位列表 - 新增职位
function addPosition(){
    var positionName=$("#addPositionName").val();
    $.ajax({
        url:"../general/addNewPositionSubmit.do",
        type:"post",
        data:{"positionName":positionName},
        success:function(data){
            console.log(data);
            location.href="/general/postManagement.do";
        },
        error:function(){
            alert("error");
        }
    });
}