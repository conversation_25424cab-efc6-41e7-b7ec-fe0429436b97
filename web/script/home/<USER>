var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#workRecordSee"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#leaveRecord"));
bounce_Fixed3.cancel();

$(function (){
    // creator : hxz 2018-05-03 作息时间设置弹窗的 月份切换 - 适用于考勤作息设置与查看
    $(".monthsNav").on("click" , "li" , function () {
        var _index = $(this).index() ;
        $(this).attr("class","ty-active").siblings().removeClass("ty-active") ;
        $(this).parent("ul").siblings(".clendars").children("table:eq(" + _index + ")").show().siblings().hide();
    }) ;
    $(".autoFill").on("input focus keyup", function () {
        var max = $(this).attr("max")
        var curLength = $(this).val().length;
        if (curLength > max) {
            curLength = max
            var cutValue = $(this).val().substring(0,max);
            $(this).val(cutValue);
        }
        $(this).siblings(".textMax").text( curLength +'/' + max );
    })
})

// creator: 张旭博，2020-12-08 16:01:47，获取标红日期列表
function getRedListByMonth(noLoading) {
    var userId = $(".tbl_month").data("userId") // 总览、团队、工作记录点评模块中都存了此字段，代表查看的某职工的工作记录
    var month = $(".tbl_month").data("month")
    var noRed = $(".tbl_month").data("noRed")// 总览-职工-工作记录 中存了此数据，表示其日期上的评回状态都不需要展示红色（即是否查看）
    var isLeader = $(".tbl_month").data("isLeader") // 是不是直接上级或者自己

    var isShowRed = true // 存储是否显示红色
    if (!userId) {
        // 不存在userId的话，证明是在我的工作记录中，肯定有红色
        isShowRed = true
    } else {
        if (noRed) {
            // 只有总览存了此数据，肯定是不显示红色
            isShowRed = false
        } else {
            if (isLeader || (userId == sphdSocket.user.userID)) {
                // 剩下的就是另外连个模块，是直接上级就有红色
                isShowRed = true
            } else {
                isShowRed = false
            }
        }
    }

    var data = {
        ym: month
    }
    if (userId) {
        data.teamUserId = userId
    }
    $.ajax({
        url: '../mywork/getDailyStatus.do',
        data: data,
        beforeSend: function() {
            if (!noLoading) {
                loading.open()
            }
        },
        success: function (res) {
            var data = res.data
            data.forEach(function (item) {
                let chooseNode = $(".tbl_month td[day='"+item.dateNO+"']")
                // 给返回的所有日期加一个鼠标样式
                chooseNode.attr("class", "hover")
                // 设置右上方框状态（是否显示和颜色）
                chooseNode.children(".day_avatar").html(chargeState(item, isShowRed))
            })
            let nowDay = Number(moment(hostTime).format("DD"))
            // 日期为今天的特殊样式
            if(moment(hostTime).format("YYYY-MM") === $(".tbl_month").data("month")) {
                $(".tbl_month td[day='"+nowDay+"']").attr("class", "hover nowDay")
            }
        }
    })
}

// creator: 张旭博，2023-03-14 14:41:58，月报统计
function getMonthlyReportStat(month) {
    var teamUser = $("#monthReport").data('teamUser')

    if (!teamUser) {
        teamUser = {
            teamUserId: '',
            teamUserName: ''
        }
    }
    var data = {
        ym: month,
        userId: teamUser.teamUserId
    }
    $.ajax({
        url: $.webRoot + '/mywork/monthlyReportStat.do',
        data: data
    }).then(function (res) {
        var data = res.data
        var innerwork = data.innerwork
        var outterwork = data.outterwork
        var dailywork = data.dailywork

        var str = ''
        var innerCount = 0
        var dailyCount = 0
        if (innerwork.length > 0) {
            for (var i=0;i<innerwork.length;i++) {

                var fulfillment = innerwork[i].fulfillment
                var factIsGreaterThanPlan = innerwork[i].factIsGreaterThanPlan
                var startOnTime = innerwork[i].startOnTime

                var type = 0
                if (fulfillment === '1') {
                    if (factIsGreaterThanPlan) {
                        type = startOnTime?3:4
                    } else {
                        type = startOnTime?1:2
                    }
                } else if (fulfillment === '3') {
                    type = 5
                } else if (fulfillment === '5') {
                    type = 6
                }
                str += '<tr type="1">'
                if(i === 0) {
                    str += ' <td rowspan="'+innerwork.length+'">计划内工作<br>共 <b class="ty-color-blue innerCount"></b> 项</td>'
                }
                str +=  '    <td>'+chargeItem(type)+'</td>' +
                    '    <td>'+innerwork[i].count+'项</td>' +
                    '    <td>'+innerwork[i].ratio+'%</td>' +
                    '    <td><span class="link-blue" onclick="seeCountDetail($(this))">查看</span><span class="hd">'+JSON.stringify(innerwork[i])+'</span></td>' +
                    '</tr>'
                innerCount += Number(innerwork[i].count)
            }
        }
        if (dailywork.length > 0) {
            for(var j=0;j<dailywork.length;j++) {
                var fulfillment = dailywork[j].routineFulfillment
                var factIsGreaterThanPlan = dailywork[j].factIsGreaterThanPlan

                var type = 0
                if (fulfillment === '1') {
                    type = 7
                } else if (fulfillment > 1) {
                    type = 8
                }
                str += '<tr type="4" fulfillment="'+fulfillment+'" factIsGreaterThanPlan="'+(factIsGreaterThanPlan?1:0)+'">';
                if(j === 0) {
                    str +=  '    <td rowspan="'+dailywork.length+'">日常工作<br>共 <b class="ty-color-blue dailyCount"></b> 项</td>'
                }
                str +=      '    <td>'+chargeItem(type)+'</td>' +
                            '    <td>'+dailywork[j].count+'项</td>' +
                            '    <td>'+dailywork[j].ratio+'%</td>' +
                            '    <td><span class="link-blue" onclick="seeCountDetail($(this))">查看</span><span class="hd">'+JSON.stringify(dailywork[j])+'</span></td>' +
                            '</tr>'
                dailyCount += Number(dailywork[j].count)
            }
        }
        if (outterwork.length >0) {
            str +=  ' <tr type="2">' +
                '    <td>计划外工作</td>' +
                '    <td colspan="3">共完成 <b class="ty-color-blue outterwork">'+outterwork[0].count+' </b> 项</td>' +
                '    <td><span class="link-blue" onclick="seeCountDetail($(this))">查看</span></td>' +
                '</tr>'
        }
        $(".page[page='monthReport'] tbody").html(str)
        $(".page[page='monthReport'] .innerCount").html(innerCount)
        $(".page[page='monthReport'] .dailyCount").html(dailyCount)
        var thisName = teamUser.teamUserName || '我'
        var tips = month.length > 4 ?(thisName+'的工作月报——' + moment(month).format("YYYY年MM月")):(thisName+'的工作年报——' + moment(month).format("YYYY年"))
        $(".page[page='monthReport'] .tips").html(tips)
        $(".page[page='monthReport'] .seeAllWorkBtn").html(month.length > 4?'查看本月全部工作':'查看本年全部工作')
    })
}

function chargeItem(type) {
    var arr = [
        '已完成、实际耗时不大于计划耗时、按计划准时开始的',
        '已完成、实际耗时不大于计划耗时、未按计划准时开始的',
        '已完成、实际耗时大于计划耗时、按计划准时开始的',
        '已完成、实际耗时大于计划耗时、未按计划准时开始的',
        '未完成、做了一部分后关闭的',
        '未完成，未做即关闭的',
        '当日已完成',
        '当日未做或未做完的'
    ]
    return arr[Number(type)-1]
}

// creator: 张旭博，2023-03-15 10:38:28，查看月报全部
function seeMonthReportDetail() {
    jumpPage("monthReportDetail")
    var month = $("#monthReport").data("month")
    var teamUser = $("#monthReport").data('teamUser')
    if (!teamUser) {
        teamUser = {
            teamUserId: '',
            teamUserName: ''
        }
    }
    var data = {
        ym: month,
        userId: teamUser.teamUserId
    }
    $.ajax({
        url: $.webRoot + '/mywork/monthlyReportAllDetail.do',
        data: data
    }).then(function (res) {
        var data = res.data
        var str= ''
        if (data) {
            for (var i in data) {
                var item = data[i]
                var arr = ['计划内工作','计划外工作','','日常工作']
                var durationFact = -(-item.durationFactHour - (item.durationFactMinute / 60).toFixed(2))
                var durationPlan = -(-item.durationPlanHour - (item.durationPlanMinute / 60).toFixed(2))
                var type = item.type
                var sublist = item.subList || [item]

                if (type === '1') {
                    if (sublist[0].fulfillment === '5') {
                        str +=  '<tr>' +
                            '   <td>计划内工作</td>'+
                            '   <td>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                            '   <td>'+item.contentPlan+'</td>'+
                            '   <td>'+moment(item.timePlan).format("YYYY-MM-DD HH:mm")+'开始，计划耗时'+durationPlan+'小时</td>'+
                            '   <td>--</td>'+
                            '   <td>--</td>'+
                            '   <td>--</td>'+
                            '</tr>'
                    } else {
                        var substr = sublist.length > 1?' rowspan="'+(sublist.length)+'"':''
                        var durationFact0 = -(-sublist[0].durationFactHour - (sublist[0].durationFactMinute / 60).toFixed(2))
                        str +=  '<tr>' +
                                '   <td'+substr+'>'+arr[Number(item.type)-1]+'</td>'+
                                '   <td'+substr+'>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                                '   <td'+substr+'>'+item.contentPlan+'</td>'+
                                '   <td'+substr+'>'+moment(item.timePlan).format("YYYY-MM-DD HH:mm")+'开始，计划耗时'+durationPlan+'小时</td>'+
                                '   <td'+substr+'>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，共耗时'+durationFact+'小时</td>'+
                                '   <td>'+moment(sublist[0].timeFact).format("YYYY-MM-DD HH:mm")+'开始，耗时'+durationFact0+'小时</td>'+
                                '   <td>'+sublist[0].content+'</td>'+
                                '</tr>'
                        for (var j=1;j<sublist.length;j++) {
                            var durationFact = -(-sublist[j].durationFactHour - (sublist[j].durationFactMinute / 60).toFixed(2))
                            str +=  '<tr>' +
                                    '   <td>'+moment(sublist[j].timeFact).format("YYYY-MM-DD HH:mm")+'开始，耗时'+durationFact+'小时</td>'+
                                    '   <td>'+sublist[j].content+'</td>'+
                                    '</tr>'
                        }
                    }
                } else if (type === '2') {
                    str +=  '<tr>' +
                            '   <td>计划外工作</td>'+
                            '   <td>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                            '   <td>--</td>'+
                            '   <td>--</td>'+
                            '   <td>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，共耗时'+durationFact+'小时</td>'+
                            '   <td>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，耗时'+durationFact+'小时</td>'+
                            '   <td>'+item.content+'</td>'+
                            '</tr>'
                } else if (type === '4') {
                    if (item.routineFulfillment === '2') {
                        str +=  '<tr>' +
                                '   <td>'+arr[Number(item.type)-1]+'</td>'+
                                '   <td>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                                '   <td>'+item.contentPlan+'</td>'+
                                '   <td>'+moment(item.timePlan).format("YYYY-MM-DD HH:mm")+'开始'+'</td>'+
                                '   <td>--</td>'+
                                '   <td>--</td>'+
                                '   <td>--</td>'+
                                '</tr>'
                    } else {
                        str +=  '<tr>' +
                                '   <td>'+arr[Number(item.type)-1]+'</td>'+
                                '   <td>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                                '   <td>'+(type === '2'?'--':item.contentPlan)+'</td>'+
                                '   <td>'+moment(item.timePlan).format("YYYY-MM-DD HH:mm")+'开始</td>'+
                                '   <td>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，共耗时'+durationFact+'小时</td>'+
                                '   <td>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，耗时'+durationFact+'小时</td>'+
                                '   <td>'+item.content+'</td>'+
                                '</tr>'
                    }
                }
            }
            $(".page[page='monthReportDetail'] tbody").html(str)
            var thisName = teamUser.teamUserName || '我'
            var tips = month.length > 4 ?(thisName +'的工作月报——' + moment(month).format("YYYY年MM月")):(thisName +'的工作年报——' + moment(month).format("YYYY年"))
            $(".page[page='monthReportDetail'] .tips").html(tips)
        }

    })
}

// creator: 张旭博，2023-03-22 10:33:22，查看月报某一项详情
function seeCountDetail(selector) {
    jumpPage("monthReportDetail")
    $(".page[page='monthReportDetail'] tbody").html('')
    var month = $("#monthReport").data("month")
    var countData = selector.next(".hd").html()
    var type = selector.parents("tr").attr("type")
    var teamUser = $("#monthReport").data('teamUser')
    if (!teamUser) {
        teamUser = {
            teamUserId: '',
            teamUserName: ''
        }
    }

    var data = {
        ym: month,
        type: type,
        userId: teamUser.teamUserId
    }
    if (type==='1'){
        countData = JSON.parse(countData)
        data.fulfillment = countData.fulfillment
        data.successionIds = JSON.stringify(countData.successionIds)
    } else if (type === '4') {
        countData = JSON.parse(countData)
        data.routineFulfillment = countData.routineFulfillment
    }
    $.ajax({
        url: $.webRoot + '/mywork/monthlyReportDetail.do',
        data: data
    }).then(function (res) {
        var data = res.data
        var str= ''
        var planTimeStr = ''
        if (data) {
            for (var i in data) {
                var item = data[i]
                var arr = ['计划内工作','计划外工作','','日常工作']
                var durationFact = -(-item.durationFactHour - (item.durationFactMinute / 60).toFixed(2))
                var durationPlan = -(-item.durationPlanHour - (item.durationPlanMinute / 60).toFixed(2))
                var type = item.type
                var sublist = item.subList || [item]

                if (type === '1') {
                    if (sublist[0].fulfillment === '5') {
                        str +=  '<tr>' +
                            '   <td>计划内工作</td>'+
                            '   <td>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                            '   <td>'+item.contentPlan+'</td>'+
                            '   <td>'+moment(item.timePlan).format("YYYY-MM-DD HH:mm")+'开始，计划耗时'+durationPlan+'小时</td>'+
                            '   <td>--</td>'+
                            '   <td>--</td>'+
                            '   <td>--</td>'+
                            '</tr>'
                    } else {
                        var substr = sublist.length > 1?' rowspan="'+(sublist.length)+'"':''
                        var durationFact0 = -(-sublist[0].durationFactHour - (sublist[0].durationFactMinute / 60).toFixed(2))
                        str +=  '<tr>' +
                            '   <td'+substr+'>'+arr[Number(item.type)-1]+'</td>'+
                            '   <td'+substr+'>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                            '   <td'+substr+'>'+item.contentPlan+'</td>'+
                            '   <td'+substr+'>'+moment(item.timePlan).format("YYYY-MM-DD HH:mm")+'开始，计划耗时'+durationPlan+'小时</td>'+
                            '   <td'+substr+'>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，共耗时'+durationFact+'小时</td>'+
                            '   <td>'+moment(sublist[0].timeFact).format("YYYY-MM-DD HH:mm")+'开始，耗时'+durationFact0+'小时</td>'+
                            '   <td>'+sublist[0].content+'</td>'+
                            '</tr>'
                        for (var j=1;j<sublist.length;j++) {
                            var durationFact = -(-sublist[j].durationFactHour - (sublist[j].durationFactMinute / 60).toFixed(2))
                            str +=  '<tr>' +
                                '   <td>'+moment(sublist[j].timeFact).format("YYYY-MM-DD HH:mm")+'开始，耗时'+durationFact+'小时</td>'+
                                '   <td>'+sublist[j].content+'</td>'+
                                '</tr>'
                        }
                    }
                } else if (type === '2') {
                    str +=  '<tr>' +
                        '   <td>计划外工作</td>'+
                        '   <td>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                        '   <td>--</td>'+
                        '   <td>--</td>'+
                        '   <td>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，共耗时'+durationFact+'小时</td>'+
                        '   <td>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，耗时'+durationFact+'小时</td>'+
                        '   <td>'+item.content+'</td>'+
                        '</tr>'
                } else if (type === '4') {
                    let timePlanStr = item.timePlan?(moment(item.timePlan).format("YYYY-MM-DD HH:mm")+'开始'): '--'  // 日常事务开始日期不是必填的
                    if (item.routineFulfillment === '2') {
                        str +=  '<tr>' +
                            '   <td>'+arr[Number(item.type)-1]+'</td>'+
                            '   <td>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                            '   <td>'+item.contentPlan+'</td>'+
                            '   <td>'+timePlanStr+'</td>'+
                            '   <td>--</td>'+
                            '   <td>--</td>'+
                            '   <td>--</td>'+
                            '</tr>'
                    } else {
                        str +=  '<tr>' +
                            '   <td>'+arr[Number(item.type)-1]+'</td>'+
                            '   <td>'+moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>'+
                            '   <td>'+(type === '2'?'--':item.contentPlan)+'</td>'+
                            '   <td>'+timePlanStr+'</td>'+
                            '   <td>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，共耗时'+durationFact+'小时</td>'+
                            '   <td>'+moment(item.timeFact).format("YYYY-MM-DD HH:mm")+'开始，耗时'+durationFact+'小时</td>'+
                            '   <td>'+item.content+'</td>'+
                            '</tr>'
                    }
                }
            }
            $(".page[page='monthReportDetail'] tbody").html(str)
            var thisName = teamUser.teamUserName || '我'
            var tips = month.length > 4 ?(thisName +'的工作月报——' + moment(month).format("YYYY年MM月")):(thisName +'的工作年报——' + moment(month).format("YYYY年"))
            $(".page[page='monthReportDetail'] .tips").html(tips)
        }
    })
}

// creator: 张旭博，2022/2/8 10:39，某条记录的详情 - 请假内容处理
function handleLeaveDetail(leaveData, showDialog) {
    var leaveEntryData = leaveData.entryData
    var leaveApprovalData = leaveData.approvalData
    var leaveStr = ''
    if (leaveEntryData.length !== 0 || leaveApprovalData.length !== 0) {
        var leaveStr = '当日有请假：<div class="leaveList">'
        for (var i in leaveEntryData) {
            leaveStr += '<div>'+
                moment(leaveEntryData[i].beginTime).format("HH:mm") +' - '+ moment(leaveEntryData[i].endTime).format("HH:mm")+
                '   <button class="ty-btn ty-btn-blue ty-circle-2" name="seeLeave" source="1">查看</button>' +
                '   <div class="hd">'+JSON.stringify(leaveEntryData[i])+'</div>' +
                '</div>'
        }
        for (var j in leaveApprovalData) {
            leaveStr += '<div>'+
                moment(leaveApprovalData[j].personnelLeave.beginTime).format("HH:mm") +' - '+ moment(leaveApprovalData[j].personnelLeave.endTime).format("HH:mm")+
                '   <button class="ty-btn ty-btn-blue ty-circle-2" name="seeLeave" source="2">查看</button>' +
                '   <div class="hd">'+JSON.stringify(leaveApprovalData[j])+'</div>' +
                '</div>'
        }
        leaveStr += '</div>'
    }
    $("#" + showDialog).find(".leave").html(leaveStr)
}

// creator: 张旭博，2020-11-10 19:11:43，查看请假/加班记录
function seeLeaveRecord(selector) {

    bounce_Fixed3.show($("#leaveRecord"))

    // 从上个接口在页面绑定的值中取值
    var data = JSON.parse(selector.siblings('.hd').html())
    var source = Number(selector.attr("source"))


    if (source === 2) {
        var detail      = data.personnelLeave // 计划请假详情
        var processList = data.processList1 // 计划请假的审批流程
        var personnelLeaveItemList = data.personnelLeaveItems // 提前结束请假详情

        var str =   '<div class="bounceItem">' +
            '    <div class="bounceItem_title">职工姓名</div>' +
            '    <div class="bounceItem_content">' +
            '        <span class="name">'+detail.createName+'</span>' +
            '    </div>' +
            '</div>' +
            '<div class="ty-hr"></div>';
        var strLeave = ''
        if (personnelLeaveItemList) {
            for (var i in personnelLeaveItemList) {
                var process = personnelLeaveItemList[i].processList

                if (personnelLeaveItemList[i].approveStatus === '2') {
                    str +=  '<div class="kj-panel">' +
                        '    <div class="bounceItem">' +
                        '        <div class="bounceItem_title">提前结束请假</div>' +
                        '        <div class="bounceItem_content"></div>' +
                        '    </div>' +
                        '    <div class="bounceItem">' +
                        '        <div class="bounceItem_title">计划上班时间</div>' +
                        '        <div class="bounceItem_content">'+moment(personnelLeaveItemList[i].actualEndTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                        '    </div>' +
                        '    <div class="bounceItem">' +
                        '        <div class="bounceItem_title">说明</div>' +
                        '        <div class="bounceItem_content">'+personnelLeaveItemList[i].actualReason+'</div>' +
                        '    </div>' +
                        '</div>' ;
                    str +=  '<div class="process">' +
                        '    <div>申请人 <span class="processName">' + personnelLeaveItemList[i].createName + '</span> ' + formatTime(personnelLeaveItemList[i].createDate, true) + '</div>' ;
                    for (var k in process) {
                        if (process[k].approveStatus === '2' || process[k].approveStatus === '3') {
                            str += ' <div>审批人 <span class="processName">' + process[k].userName + '</span> ' + formatTime(process[k].handleTime, true) + '</div>'
                        }
                    }
                    str +=  '</div><div class="ty-hr"></div>'
                } else {
                    strLeave +=     '<div class="ty-hr"></div>' +
                        '<div class="kj-panel">' +
                        '    <div class="bounceItem">' +
                        '        <div class="bounceItem_title">提前结束请假</div>' +
                        '        <div class="bounceItem_content"></div>' +
                        '    </div>' +
                        '    <div class="bounceItem">' +
                        '        <div class="bounceItem_title">计划上班时间</div>' +
                        '        <div class="bounceItem_content">'+moment(personnelLeaveItemList[i].actualEndTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                        '    </div>' +
                        '    <div class="bounceItem">' +
                        '        <div class="bounceItem_title">说明</div>' +
                        '        <div class="bounceItem_content">'+personnelLeaveItemList[i].actualReason+'</div>' +
                        '    </div>';

                    for (var j in process) {
                        if (process[j].approveStatus === '3') {
                            strLeave +=     '    <div class="bounceItem ty-color-red">提前结束请假的申请被'+process[j].userName+'驳回！</div>' +
                                '    <div class="bounceItem ty-color-red">' +
                                '        <div class="bounceItem_title">驳回理由</div>' +
                                '        <div class="bounceItem_content">'+process[j].reason+'</div>' +
                                '    </div>' ;
                        }
                    }
                    strLeave +=     '</div>' +
                        '<div class="process">' +
                        '   <div>申请人 <span class="processName">' + personnelLeaveItemList[i].createName + '</span> ' + formatTime(personnelLeaveItemList[i].createDate, true) + '</div>' ;
                    for (var k in process) {
                        if (process[k].approveStatus === '2' || process[k].approveStatus === '3') {
                            strLeave += '<div>审批人 <span class="processName">' + process[k].userName + '</span> ' + formatTime(process[k].handleTime, true) + '</div>'
                        }

                    }
                    strLeave +=     '</div>' ;
                }
            }
        }
        str +=      '<div class="kj-panel">' +
            '    <div class="bounceItem">' +
            '        <div class="bounceItem_title">开始时间</div>' +
            '        <div class="bounceItem_content">'+moment(detail.beginTime).format('YYYY-MM-DD HH:mm')+'</div>' +
            '    </div>' +
            '    <div class="bounceItem">' +
            '        <div class="bounceItem_title">结束时间</div>' +
            '        <div class="bounceItem_content">'+moment(detail.endTime).format('YYYY-MM-DD HH:mm')+'</div>' +
            '    </div>' +
            '    <div class="bounceItem">' +
            '        <div class="bounceItem_title">请假类型</div>' +
            '        <div class="bounceItem_content">'+detail.leaveTypeName+'</div>' +
            '    </div>' +
            '    <div class="bounceItem">' +
            '        <div class="bounceItem_title">请假事由</div>' +
            '        <div class="bounceItem_content">'+detail.reason+'</div>' +
            '    </div>' +
            '</div>' +
            '<div class="process">' +
            '    <div>申请人 <span class="processName">' + detail.createName + '</span> ' +formatTime(detail.createDate, true) + '</div>';

        for (var m in processList) {
            if (processList[m].approveStatus === '2' || processList[m].approveStatus === '3') {
                str += ' <div>审批人 <span class="processName">' + processList[m].userName + '</span> ' + formatTime(processList[m].handleTime, true) + '</div>'
            }

        }
        str +=      '</div>' + strLeave;
    } else {
        var detail = data
        var str =   '<div class="bounceItem">' +
            '        <div class="bounceItem_title">职工姓名</div>' +
            '        <div class="bounceItem_content">' +
            '            <span class="name">'+detail.createName+'</span>' +
            '        </div>' +
            '    </div>' +
            '    <div class="ty-hr"></div>' +
            '    <div class="kj-panel">' +
            '        <div class="bounceItem">' +
            '            <div class="bounceItem_title">开始时间</div>' +
            '            <div class="bounceItem_content">'+moment(detail.beginTime).format('YYYY-MM-DD HH:mm')+'</div>' +
            '        </div>' +
            '        <div class="bounceItem">' +
            '            <div class="bounceItem_title">结束时间</div>' +
            '            <div class="bounceItem_content">'+moment(detail.endTime).format('YYYY-MM-DD HH:mm')+'</div>' +
            '        </div>' +
            '        <div class="bounceItem">' +
            '            <div class="bounceItem_title">请假类型</div>' +
            '            <div class="bounceItem_content">'+detail.leaveTypeName+'</div>' +
            '        </div>' +
            '        <div class="bounceItem">' +
            '            <div class="bounceItem_title">请假事由</div>' +
            '            <div class="bounceItem_content">'+detail.reason+'</div>' +
            '        </div>' +
            '    </div>';
    }
    // 赋值计划请假详情
    $("#leaveRecord .bounce_title").html('请假记录')
    $("#leaveRecord .detail").html(str)

}

// creator: 张旭博，2021-01-19 08:16:49，工作记录详情中的修改记录 - 按钮
function workRecordBtn() {
    bounce_Fixed.show($("#workRecord"))
    $.ajax({
        url: '../mywork/getHistoryList.do',
        data: {
            org: sphdSocket.user.userID,
            workId: $(".tbl_month").data("workId")
        },
        success: function (res) {
            var data = res.data
            var str = ''
            if (data.length === 0) {

            } else if (data.length === 1) {
                $("#workRecord .ty-alert").html('当前资料尚未经修改。<div class="btn-group text-right">创建人：'+data[0].createName+' '+moment(data[0].createDate).format('YYYY-MM-DD HH:mm:ss')+'</div>')
                $("#workRecord table").hide()
            } else {
                $("#workRecord .ty-alert").html('当前资料为第'+(data.length - 1)+'次修改后的结果。')
                $("#workRecord table").show()
                for (var i=0;i<data.length;i++) {
                    if (i===0) {
                        str +=  '<tr userWork="'+data[0].id+'" version="'+data[0].versionNo+'">'+
                            '   <td>原始信息</td>'+
                            '   <td><button class="ty-btn ty-btn-blue" onclick="workRecordSee($(this))">查看</button></td>'+
                            '   <td>'+data[i].createName+' '+moment(data[i].createDate).format('YYYY-MM-DD HH:mm:ss')+'</td>'
                        '</tr>'
                    } else {
                        str +=  '<tr userWork="'+data[i].id+'" version="'+data[i].versionNo+'">'+
                            '   <td>第'+i+'次修改后</td>'+
                            '   <td><button class="ty-btn ty-btn-blue" onclick="workRecordSee($(this))">查看</button></td>'+
                            '   <td>'+data[i].updateName+' '+moment(data[i].updateDate).format('YYYY-MM-DD HH:mm:ss')+'</td>'
                        '</tr>'
                    }
                }
            }
            $("#workRecord table tbody").html(str)
        }
    })
}

// creator: 张旭博，2021-01-19 09:12:54，XX修改记录 - 查看 - 按钮
function workRecordSee(selector) {
    var userWork = selector.parents("tr").attr("userWork")
    var version = selector.parents("tr").attr("version")
    getWorkOfRecord(userWork, version)
}

// creator: 张旭博，2021-01-19 10:03:44，获取修改记录某条记录的详情
function getWorkOfRecord(userWork, version) {
    $.ajax({
        url: '../mywork/getWorkDetailByWorkid.do',
        data: {
            org: sphdSocket.user.oid,
            userId: sphdSocket.user.userID,
            versionNo: version,
            workid: userWork // 返回值的ID值
        },
        success: function (res) {
            var data = res.data

            bounce_Fixed2.show($("#workRecordSee"))
            setEveryTime(bounce, 'workRecordSee')
            $("#workRecordSee .ty-panel").show()
            $("#workRecordSee .lastOperateTimeVisible").hide()
            if (data.recentList.length === 0) {
                $("#workRecordSee .recentWork").hide()
            }
            var showDialog = 'workRecordSee'

            // -----------------处理表头------------------ （申请 查看 修改 公用）
            var lastOperateTime = data.lastOperateTime ? moment(data.lastOperateTime).format("YYYY-MM-DD HH:mm:ss"): '暂无' // 最后的操作时间
            var leaveData = data.leaveData // 请假信息
            var isWorkDay = data.isWorkDay // true 正常上班 false 休息
            var hasExtraWork = data.hasExtraWork // true 有加班 false 无加班
            var timeSpentTotal = data.timeSpentTotal // 当日已录入内容合计耗时

            // 赋值当日应该有工作记录的原因
            $("#" + showDialog).find(".workReason").html(chargeWorkReason(isWorkDay, hasExtraWork))
            // 处理请假
            handleLeaveDetail(leaveData, showDialog)

            // 赋值当日已录入内容合计耗时
            $("#" + showDialog).find(".timeSpentTotal").html(timeSpentTotal)
            // 赋值最后的操作时间
            $("#" + showDialog).find(".lastOperateTime").html(lastOperateTime)

            // -----------------处理内容------------------

            // 获取日常工作
            var routineList = data.routineList // 日常工作内容
            var routineStr = ''
            for (var m = 0; m < routineList.length; m++) {
                var fulfillment = Number(routineList[m].routineFulfillment)

                var routineItem = {
                    id: routineList[m].id,
                    routineId: routineList[m].routineId,
                    contentPlan: routineList[m].contentPlan,
                    routineFulfillment: routineList[m].routineFulfillment || 0,
                    timePlan: routineList[m].timePlan?moment(routineList[m].timePlan).format("YYYY-MM-DD HH:mm:ss"):'',
                    timePlanDate: routineList[m].timePlan?moment(routineList[m].timePlan).format("YYYY-MM-DD"):'',
                    timePlanTime: routineList[m].timePlan?moment(routineList[m].timePlan).format("HH:mm"):'',

                }
                if (fulfillment === 1) {
                    routineItem.content = routineList[m].content
                    routineItem.timeFact = moment(routineList[m].timeFact).format("YYYY-MM-DD HH:mm:ss")
                    routineItem.timeFactDate = moment(routineList[m].timeFact).format("YYYY-MM-DD")
                    routineItem.timeFactTime = moment(routineList[m].timeFact).format("HH:mm")
                    routineItem.durationFactHour = routineList[m].durationFactHour
                    routineItem.durationFactMinute = routineList[m].durationFactMinute
                    routineItem.durationFact = -(-routineList[m].durationFactHour - (routineList[m].durationFactMinute/60).toFixed(2))  //实际耗时（小时，保存两位）
                }

                routineStr +=     '<tr>' +
                    '   <td>'+(routineItem.content?routineItem.contentPlan + '/<span class="ty-color-orange">'+routineItem.content+'</span>':routineItem.contentPlan)+'</td>'+
                    '   <td>'+(routineItem.timePlanTime || '--')+'</td>'+
                    '   <td>'+chargeRoutineFulfillment(routineItem)+'</td>'+
                    '   <td class="hd">'+JSON.stringify(routineItem)+'</td>'+
                    '</tr>'

            }

            $("#" + showDialog).find(".routineWork tbody").html(routineStr)

            var innerList = data.innerList

            var innerStr = ''
            for (var l = 0; l < innerList.length; l++) {
                var fulfillment = Number(innerList[l].fulfillment)
                if (fulfillment === 1) {
                    var innerItem = {
                        type: 1, //类型:1-计划内,2-计划外,3-近日计划
                        contentPlan: innerList[l].contentPlan,
                        content: innerList[l].content,
                        timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                        timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                        timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                        fulfillment: innerList[l].fulfillment,
                        timeFact: moment(innerList[l].timeFact).format("YYYY-MM-DD HH:mm:ss"),  //实际开始时间
                        timeFactDate: moment(innerList[l].timeFact).format("YYYY-MM-DD"),  //实际开始时间
                        timeFactTime: moment(innerList[l].timeFact).format("HH:mm"),  //实际开始时间
                        durationFact: -(-innerList[l].durationFactHour - (innerList[l].durationFactMinute/60).toFixed(2)),  //实际耗时（小时，保存两位）
                        durationFactMinute: innerList[l].durationFactMinute,                                                //实际耗时（分钟）
                        durationFactHour: innerList[l].durationFactHour,                                                    //实际耗时（小时）
                        durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                        durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                        durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                    }
                } else if (fulfillment === 2) {
                    var innerItem = {
                        type: 1, //类型:1-计划内,2-计划外,3-近日计划
                        contentPlan: innerList[l].contentPlan,
                        content: innerList[l].content,
                        timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                        timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                        timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                        fulfillment: innerList[l].fulfillment,
                        timeFact: moment(innerList[l].timeFact).format("YYYY-MM-DD HH:mm:ss"),  //实际开始时间
                        timeFactDate: moment(innerList[l].timeFact).format("YYYY-MM-DD"),  //实际开始时间
                        timeFactTime: moment(innerList[l].timeFact).format("HH:mm"),  //实际开始时间
                        durationFact: -(-innerList[l].durationFactHour - (innerList[l].durationFactMinute/60).toFixed(2)),  //实际耗时（小时，保存两位）
                        durationFactMinute: innerList[l].durationFactMinute,                                                //实际耗时（分钟）
                        durationFactHour: innerList[l].durationFactHour,                                                    //实际耗时（小时）
                        durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                        durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                        durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                        timeContinue: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD HH:mm:ss"):''),  //剩余部分计划开始时间
                        timeContinueDate: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD"):''),  //剩余部分计划开始日期
                        timeContinueTime: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("HH:mm"):''), //剩余部分计划开始分秒
                        continuePlan: -(-innerList[l].continuePlanHour - (innerList[l].continuePlanMinute/60).toFixed(2)),  //剩余部分计划耗时（小时，保存两位）
                        continuePlanMinute: (innerList[l].continuePlanMinute || 0),                                        //剩余部分计划耗时（分钟）
                        continuePlanHour: (innerList[l].continuePlanHour || 0),                                            //剩余部分计划耗时（小时）
                        reason: innerList[l].reason //关闭原因
                    }
                } else if (fulfillment === 3) {
                    var innerItem = {
                        type: 1, //类型:1-计划内,2-计划外,3-近日计划
                        contentPlan: innerList[l].contentPlan,
                        content: innerList[l].content,
                        timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                        timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                        timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                        fulfillment: innerList[l].fulfillment,
                        timeFact: moment(innerList[l].timeFact).format("YYYY-MM-DD HH:mm:ss"),  //实际开始时间
                        timeFactDate: moment(innerList[l].timeFact).format("YYYY-MM-DD"),  //实际开始时间
                        timeFactTime: moment(innerList[l].timeFact).format("HH:mm"),  //实际开始时间
                        durationFact: -(-innerList[l].durationFactHour - (innerList[l].durationFactMinute/60).toFixed(2)),  //实际耗时（小时，保存两位）
                        durationFactMinute: innerList[l].durationFactMinute,                                                //实际耗时（分钟）
                        durationFactHour: innerList[l].durationFactHour,                                                    //实际耗时（小时）
                        durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                        durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                        durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                        reason: innerList[l].reason //关闭原因
                    }
                } else if (fulfillment === 4) {
                    var innerItem = {
                        type: 1, //类型:1-计划内,2-计划外,3-近日计划
                        contentPlan: innerList[l].contentPlan,
                        timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                        timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                        timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                        fulfillment: innerList[l].fulfillment,
                        durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                        durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                        durationPlanHour: innerList[l].durationPlanHour,
                        timeContinue: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD HH:mm:ss"):''),  //剩余部分计划开始时间
                        timeContinueDate: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD"):''),  //剩余部分计划开始日期
                        timeContinueTime: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("HH:mm"):''), //剩余部分计划开始分秒//计划耗时（小时）
                        reason: innerList[l].reason //关闭原因
                    }
                } else {
                    var innerItem = {
                        type: 1, //类型:1-计划内,2-计划外,3-近日计划
                        contentPlan: innerList[l].contentPlan,
                        timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                        timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                        timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                        fulfillment: innerList[l].fulfillment,
                        durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                        durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                        durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                        reason: innerList[l].reason //关闭原因
                    }
                }
                innerStr +=     '<tr>' +
                    '   <td>'+(innerItem.content || innerItem.contentPlan)+'</td>'+
                    '   <td>'+moment(innerItem.timePlan).format("YYYY-MM-DD HH:mm")+'</td>'+
                    '   <td>'+innerItem.durationPlan+'小时</td>'+
                    '   <td>'+chargeFulfillment(innerItem)+'</td>'+
                    '</tr>'

            }
            $("#" + showDialog).find(".innerWork tbody").html(innerStr)

            // 获取当日所做的计划外工作
            var outterList = data.outterList
            var outterStr = ''
            for (var j = 0; j < outterList.length; j++) {
                var outterItem = {
                    type: 2,
                    content: outterList[j].content,
                    timeFact: moment(outterList[j].timeFact).format("YYYY-MM-DD HH:mm:ss"),
                    timeFactDate: moment(outterList[j].timeFact).format("YYYY-MM-DD"),
                    timeFactTime: moment(outterList[j].timeFact).format("HH:mm"),
                    durationFactHour: outterList[j].durationFactHour,
                    durationFactMinute: outterList[j].durationFactMinute,
                    durationFact: -(-outterList[j].durationFactHour - (outterList[j].durationFactMinute/60).toFixed(2))
                }
                outterStr +=    '<tr>' +
                    '   <td>'+outterItem.content+'</td>'+
                    '   <td>'+outterItem.timeFactDate+'</td>'+
                    '   <td>'+outterItem.timeFactTime+'</td>'+
                    '   <td><span class="durationFact">'+outterItem.durationFact+'</span>小时</td>'+
                    '</tr>'
            }
            $("#" + showDialog).find(".outterWork tbody").html(outterStr)
            // 获取近日的工作计划
            var recentList = data.recentList
            var recentStr = ''
            for (var k = 0; k < recentList.length; k++) {
                var recentItem = {
                    type: 3,
                    contentPlan: recentList[k].contentPlan,
                    timePlan: moment(recentList[k].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(recentList[k].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(recentList[k].timePlan).format("HH:mm"),
                    durationPlanHour: recentList[k].durationPlanHour,
                    durationPlanMinute: recentList[k].durationPlanMinute,
                    durationPlan: -(-recentList[k].durationPlanHour - (recentList[k].durationPlanMinute/60).toFixed(2)),
                    fulfillment: recentList[k].fulfillment
                }
                recentStr +=    '<tr>' +
                    '   <td>'+recentItem.contentPlan+'</td>'+
                    '   <td>'+moment(recentItem.timePlan).format("YYYY-MM-DD HH:mm")+'</td>'+
                    '   <td>'+recentItem.durationPlan+'小时</td>'+
                    '</tr>'
            }
            $("#" + showDialog).find(".recentWork tbody").html(recentStr)
        }
    })
}

// creator: 张旭博，2020-12-29 11:38:11，过滤字段 - 结果
function chargeFulfillment(item) {
    var str = ''
    var timeFact = item.timeFact?moment(item.timeFact).format('HH:mm'):''
    var durationFact = item.durationFact
    switch (item.fulfillment) {
        case '1':
            // 当日完成了该项工作
            str =   '实际于'+timeFact+'开始，耗时<span class="durationFact">'+durationFact+'</span>小时<br/>' +
                '已完成。'
            break
        case '2':
            str =   '实际于'+timeFact+'开始，耗时<span class="durationFact">'+durationFact+'</span>小时<br/>' +
                '未完成。计划再耗时'+item.continuePlan+'小时。<br/>' +
                '计划于<span class="timeContinue">'+moment(item.timeContinue).format('YYYY-MM-DD HH:mm')+'</span>继续进行。'
            break
        case '3':
            str =   '实际于'+timeFact+'开始，耗时<span class="durationFact">'+durationFact+'</span>小时<br/>' +
                '未完成。<br/>' +
                '关闭此项工作。'
            break
        case '4':
            str =   '此项工作当日未做。<br/>' +
                '推迟至'+moment(item.timeContinue).format('YYYY-MM-DD HH:mm')+'再做。<br/>'
            break
        case '5':
            str =   '此项工作当日未做。<br/>' +
                '关闭此项工作。'
            break
        case '6':
            str =   ''
            break
        default:
            str =   '尚未编辑'
    }
    return str
}

// creator: 张旭博，2020-12-29 11:38:11，过滤字段 - 结果
function chargeRoutineFulfillment(item) {
    var str = ''
    switch (item.routineFulfillment) {
        case '1':
            // 当日完成了该项工作
            str =   '实际于'+moment(item.timeFact).format('HH:mm')+'开始，耗时<span class="durationFact">'+item.durationFact+'</span>小时<br/>' +
                '已完成。'
            break
        case '2':
            str =   '此项工作当日未做,或未做完。'
            break
        default:
            str =   '尚未编辑'
    }
    return str
}

// creator: 张旭博，2020-12-23 12:41:01，判断当前日期是过去今天还是未来
function isTodayOrFuture(date) {
    var todayMoment = moment(moment(hostTime).format("YYYY-MM-DD"))
    var dateMoment = moment(date)
    var diff = dateMoment.diff(todayMoment)
    if (diff > 0) {
        diff = 1
    } else if (diff < 0) {
        diff = -1
    }
    return diff
}

// creator: 张旭博，2022/6/27 9:57，判断日期内额外显示的状态名称
function chargeState(data, isShowRed) {
    var dateNO = data.dateNO
    var markState = data.markState
    var commentState = Number(data.commentState)
    var replytState = Number(data.replytState)

    var str = '<div class="work_state">'

    var markStateStr = ''
    var dayNoStr = '<div class="work_dayNo">'+dateNO+'</div>'
    switch (markState) {
        case '1':
            markStateStr = '<div class="workState_item">稿</div>';
            break;
        case '2':
            dayNoStr = '<div class="work_dayNo red">'+dateNO+'</div>'
            break;
        case '3':
            markStateStr = '<div class="workState_item red">稿</div>';
            dayNoStr = '<div class="work_dayNo red">'+dateNO+'</div>'
            break;
        case '4':
            markStateStr = '<div class="workState_item">交</div>';
            break;
        case '5':
            dayNoStr = '<div class="work_dayNo black">'+dateNO+'</div>'
            break;
    }

    str += markStateStr

    if (commentState > 0) {
        str += '<div class="workState_item '+(commentState>1 || !isShowRed?'':'red')+'">评</div>'
    }
    if (replytState > 0) {
        str += '<div class="workState_item '+(replytState>1 || !isShowRed?'':'red')+'">回</div>'
    }
    str += '</div>' + dayNoStr
    return str
}

// creator: 张旭博，2021-01-27 09:32:26，验证小时和分钟不能超过最大限度
function validateTimeInput(selector) {
    var state = true
    selector.find("input._hour:visible").each(function () {
        if (Number($(this).val()) > 23) {
            layer.msg("时间单位为小时的时候，最多只能输入为23！")
            state = false
        }
    })
    if (state) {
        selector.find("input._minute:visible").each(function () {
            if (Number($(this).val()) > 59) {
                layer.msg("时间单位为分钟时候，最多只能输入为59！")
                state = false
            }
        })
    }
    return state
}

// updator：hxz 2018-06-13  考勤设置 - 作息时间查看
function timeTableSet(){
    $.ajax({
        "url":"../workAttendance/getTimeTable.do" ,
        "data" : {} ,
        success:function (res) {
            $("#today").val(res["today"]) ;
            var thisMonth = res["thisMonth"];
            var nextMonth = res["nextMonth"];
            var thisYear = thisMonth.substr(0,4);
            var nextYear = nextMonth.substr(0,4);
            var this_m = thisMonth.substr(5);
            var next_m = nextMonth.substr(5);
            var singleMon = thisYear==nextYear?next_m:nextMonth;
            var strNav = '<li class="ty-active">' + thisMonth + '</li><li>'+ nextMonth +'</li>' ;
            $("#monthsTip").html(thisMonth + '份与'+ singleMon +'份的作息计划如下。') ;
            $("#monthsScan").html(strNav) ;
            var nextInfoList = res["nextPersonnelAttendanceExceptions"] ;
            var thisInfoList = res["personnelAttendanceExceptions"] ;
            var monthInfo = [];
            if (thisInfoList && thisInfoList.length > 0) {
                for (var i = 0; i < thisInfoList.length; i++) {
                    var date = moment(thisInfoList[i]["exceptionDate"]).format("YYYY-MM-DD");
                    var type = thisInfoList[i]["type"];    // 1 - 假 ， 2 - 班
                    // var flag = 0; // flag=1:休息日  flag=2:节假日 flag=0:工作日
                    // if (type == 1) {
                    //     flag = 1;
                    // } else {
                    //     flag = 0;
                    // }
                    monthInfo.push({"date": date, "type": type});
                }
            }
            if (nextInfoList && nextInfoList.length > 0) {
                for (var i = 0; i < nextInfoList.length; i++) {
                    var date = moment(nextInfoList[i]["exceptionDate"]).format("YYYY-MM-DD");
                    var type = nextInfoList[i]["type"];    // 1 - 假 ， 2 - 班
                    // var flag = 0; // flag=1:休息日  flag=2:节假日 flag=0:工作日
                    // if (type == 1) {
                    //     flag = 1;
                    // } else {
                    //     flag = 0;
                    // }
                    monthInfo.push({"date": date, "type": type});
                }
            }
            //wyu：挪到上面赋值 20191106
            // var thisYear = thisMonth.substr(0,4) ;
            // var nextYear = nextMonth.substr(0,4) ;
            // var this_m = thisMonth.substr(5) ;
            // var next_m = nextMonth.substr(5) ;
            this_m = this_m.substr(0, this_m.length-1); if(Number(this_m)<10){ this_m = 0 + this_m; }
            next_m = next_m.substr(0, next_m.length-1); if(Number(next_m)<10){ next_m = 0 + next_m; }
            // var yearMonthArr = [] ;
            // if(thisYear == nextYear){ yearMonthArr = [ thisYear+this_m, thisYear+next_m ];   }else{  yearMonthArr = [thisYear+this_m, nextYear+next_m ]; }
            var yearMonthArr = [thisYear+this_m, nextYear+next_m ];
            datepicker.init($("#clendarsScan"), yearMonthArr, monthInfo, true);
            bounce.show($('#timetableSetting')) ;
        }
    }) ;
}

function closeAndFreshWork() {
    bounce.cancel()
    initMonth(true)
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(){
    $(".textMax:visible").each(function(){
        var max = $(this).prev(".autoFill").attr("max")
        $(this).text($(this).prev(".autoFill").val().length +'/' + max)
    })
}

// creator: 张旭博，2020-12-28 11:56:29，过滤字段 - 录入当日的工作内容
function chargeWorkReason(isWorkDay, hasExtraWork){
    var name = ''
    if (isWorkDay) {
        if (hasExtraWork) {
            name = '当日应该有工作记录的原因：正常工作日，且有加班'
        } else {
            name = '当日应该有工作记录的原因：正常工作日'
        }
    } else {
        if (hasExtraWork) {
            name = '当日应该有工作记录的原因：有加班'
        } else {
            name = '这天不是工作日，无需录入工作内容。'
        }
    }
    return name
}

// creator: 张旭博，2023-02-24 15:39:49，返回上一步
function back() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    var newArr = pathArr.slice(0,-1)
    $("#home").data("pathArr", newArr)
    $(".page[page='"+newArr.at(-1)+"']").show().siblings(".page").hide()
    if (newArr.at(-1) === 'main') {
        $(".backBtn").hide()
    }
    if (newArr.length < 3) {
        $("#backToMainBtn").hide()
    } else {
        $("#backToMainBtn").show()
    }
}

// creator: 张旭博，2023-02-24 15:40:51，回到主页
function backToMain() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr = [pathArr[0]]
    $("#home").data("pathArr", pathArr)
    $(".page[page='"+pathArr[0]+"']").show().siblings(".page").hide()
    $(".backBtn").hide()
}

// creator: 张旭博，2023-02-09 04:56:46， 跳转及退回
function jumpPage(page, callback) {
    var arr = $("#home").data("pathArr") || []
    arr.push(page)
    $("#home").data("pathArr", arr)
    $(".page[page='"+page+"']").show().siblings(".page").hide()
    if (page !== 'main') {
        $(".backBtn").show()
    }
    if (arr.length < 3) {
        $("#backToMainBtn").hide()
    } else {
        $("#backToMainBtn").show()
    }
    if (callback) { callback()}
}

// creator: 张旭博，2023-03-27 10:20:03， 查看全部点评 - 按钮
function seeAllReviewBtn(){
    var beCommentedId = $(".tbl_month").data("userId")
    bounce_Fixed.show($("#seeAllReview"))
    $.ajax({
        url: $.webRoot + '/mywork/getAllComments.do',
        data: {beCommentedId: beCommentedId}
    }).then(res => {
        var data = res.data
        var str = ''

        for (var k=0; k<data.length;k++) {
            var date = moment(data[k].date).format("YYYY-MM-DD")
            var commentList = data[k].commentList
            var nowUser = sphdSocket.user.userID
            var workUser = $(".tbl_month").data("userId")
            str += '<div class="panel-review-item" data-date="'+date+'"><div class="ty-alert">以下为对'+date+'工作记录的点评 <div class="btn-group text-right"><span class="link-blue" onclick="seeOnedayWorkBtn($(this))">查看这天的工作记录</span></div></div><div class="reviewpart">';
            for (var i in commentList) {
                var replyList = commentList[i].replyList
                var replyStr = ''
                if (replyList.length > 0) {
                    replyStr =  '<div class="reply-avatar">'
                    for (var j in replyList) {
                        replyStr += '<div class="review-item">' +
                            '    <div class="review-title">' + replyList[j].createName + ' ' + moment(replyList[j].createDate).format("YYYY-MM-DD HH:mm:ss") + '回复' +'</div>' +
                            '    <div class="review-content">'+replyList[j].content+'</div>' +
                            '</div>'
                    }
                    replyStr +=     '</div>'
                }

                var handleStr = nowUser !== workUser && commentList[i].creator === nowUser?'<button class="ty-btn ty-btn-red ty-circle-2" onclick="deleteReviewBtn($(this))">删除</button>': ''
                str +=  '<div class="review-item-avatar">' +
                    '    <div class="review-avatar" data-id="'+commentList[i].id+'" data-user="'+commentList[i].creator+'">' +
                    '        <div class="review-item">' +
                    '            <div class="review-title">' + commentList[i].createName + ' ' + moment(commentList[i].createDate).format("YYYY-MM-DD HH:mm:ss") + '点评' +
                    '                <div class="btn-group text-right">' +handleStr +'</div>' +
                    '            </div>' +
                    '            <div class="review-content">'+commentList[i].content+'</div>' +
                    '        </div>' +
                    '    </div>' + replyStr +
                    '</div>'
            }
            str += '</div></div>'
        }
        $("#seeAllReview .allReviewPart").html(str)
    })
}

// creator: 张旭博，2023-03-31 01:46:27， 查看某一天的全部点评 - 按钮
function seeOnedayWorkBtn(selector){
    bounce_Fixed.cancel()
    var date = selector.parents(".panel-review-item").data("date")
    getWorkOfSomeday(date)

}