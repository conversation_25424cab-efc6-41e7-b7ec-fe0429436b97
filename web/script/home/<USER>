/**
 * Created by lyt 2017/9/11.
 */

//定义全局变量，追踪状态
var loginHistory = [{
    "type" : 1, //1-第一层查询（及点击本日、本月、本年、自定义查询、按姓名、按时间后的查询）； 2-点击登录查询按钮后的查询
    "flag" : 0, //0-本日、1-本月、2-本年、3-自定义查询
    "sort" : 0  //0-按时间后的查询、1-按姓名
}];

//初始化
$(function(){
    //获取本日按时间查询的数据
    getLoginRecordList(1,20,0,0);

    //本年、本月、本日切换状态
    $(".flagTab .ty-btn-group .ty-btn").on("click",function(){
        //样式切换
        $(this).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
        $("#loginQueryBtn").removeClass("ty-btn-blue");

        //清空自定义选择的时间
        $("#queryBeginTime").val("");
        $("#queryEndTime").val("");

        //获取对应数据
        var thisFlag = $(this).index();
        var thisSort = 0;
        getLoginRecordList(1,20,thisFlag,thisSort) ;

        //设置状态追踪
        var json = {
            "type" : 1,
            "flag" : thisFlag,
            "sort" : thisSort
        };
        setLoginState(json);
    });

    //pc端、手机端切换
    $(".signTab .ty-btn-group .ty-btn").on("click",function(){
        //样式切换
        // $(this).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");

        //获取对应数据（参数从历史记录中获取）
        var param = loginHistory[loginHistory.length-1];
        param.sort = $(this).index();
        var paramStr = JSON.stringify(param);

        seeLoginRecordListDetail(1,20,paramStr);
    });

});

/* updater：张旭博，2017-09-20 13:42:23，获取登录记录列表 */
// wyu：有用 20180425
function getLoginRecordList(curr,totalPage,flag,sort) {
    var data = {
        "currPage":curr,
        "pageSize":totalPage,
        "flag":flag,
        "userId":sphdSocket.user.userID,
        "sort":0,
        "oid": sphdSocket.user.oid,
        "loginUserId" : sphdSocket.user.userID,
    };
    if(flag === 3){
        var beginTime = $("#queryBeginTime").val();
        var endTime = $("#queryEndTime").val();
        data["beginTime"] = beginTime;
        data["endTime"] = endTime;
    }
    if(flag === 4){
        // data["strk"] = beginTime;
        // data["strj"] = endTime;
    }
    $.ajax({
        url: "../loginrecord/loginRecords.do",
        data:data,
        success:function(data){
            var date = data.date;
            var mycurr=data.currPage;
            var ttlPage=data.totalPage;
            var userLogList = data.userLogList;
            var jsonStr = JSON.stringify({
                "flag": flag,
                "sort": sort
            });
            $("#nowDate").html(date);
            $(".dataNav").show();
            var listStr = "";
            //按时间排序
            if(sort===0){
                $(".flagTab").show();
                switch (flag){
                    //按本日时间排序 flag === 0 && sort === 0
                    case 0 :
                        $("#dayInfo").show().siblings().hide();
                        //表头展示
                        showLoginNav({"total":data.total,"date":date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});
                        for(var i=0;i<userLogList.length;i++){
                            listStr +=  '<tr id="'+userLogList[i].loginYear+'"><td>'+chargeNull(userLogList[i].userName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].departmentName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                                '<td>'+new Date(userLogList[i].operatetime).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                                '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                '<td>'+chargePort(userLogList[i].type)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].ip)+'</td></tr>';
                        }
                        $("#dayInfo tbody").html(listStr);
                        setPage($("#ye-dayInfo"),mycurr,ttlPage,"records",jsonStr);
                        break;

                    //按本月时间排序 flag === 1 && sort === 0
                    case 1 :
                        $("#monthInfo").show().siblings().hide();
                        //表头展示
                        showLoginNav({"total":data.total,"date":data.firstdate+'~'+date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});
                        for(var i=0;i<userLogList.length;i++){
                            var nextJson = {
                                "flag" : 2,
                                "sort" : -1,
                                "sign" : 0,
                                "mdate" : userLogList[i].loginYear,
                                "loginYear" : userLogList[i].loginYear
                            };
                            listStr +=  '<tr>' +
                                '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].loginYear) +'</td>'+
                                '<td>'+chargeNull(userLogList[i].sum)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this))">登录查询</span></td></tr>';
                        }
                        $("#monthInfo tbody").html(listStr);
                        setPage($("#ye-monthInfo"),mycurr,ttlPage,"records",jsonStr);
                        break;

                    //按本年时间排序 flag === 2 && sort === 0
                    case 2 :
                        $("#yearInfo").show().siblings().hide();
                        //表头展示
                        showLoginNav({"total":data.total,"date":data.firstdate+'~'+date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});
                        for(var i=0;i<userLogList.length;i++){
                            var nextJson = {
                                "flag" : 3,
                                "sort" : -1,
                                "sign" : 1,
                                "mdate" : userLogList[i].loginYear
                            };
                            listStr +=  '<tr>' +
                                '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                '<td>'+ chargeNull(userLogList[i].loginYear) + '</td>' +
                                '<td>'+chargeNull(userLogList[i].sum)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this))">登录查询</span></td></tr>';
                        }
                        $("#yearInfo tbody").html(listStr);
                        setPage($("#ye-yearInfo"),mycurr,ttlPage,"records",jsonStr);
                        break;

                    //按自定义时间排序 flag === 3 && sort === 0
                    case 3 :
                        $("#defineInfo").show().siblings().hide();
                        //表头展示
                        showLoginNav({"total":data.total,"date":data.beginTime+'~'+endTime,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});
                        for(var i=0;i<userLogList.length;i++){
                            var beginYear = beginTime.split("-")[0];
                            var beginMonth = beginTime.split("-")[1];
                            var endYear = endTime.split("-")[0];
                            var endMonth = endTime.split("-")[1];
                            var mdate;
                            var edate;
                            var nextJson = {};
                            if(beginYear !== endYear){//跨年
                                mdate = userLogList[i].loginYear+"-01-01";
                                if(mdate<data.beginTime)
                                    mdate=data.beginTime;
                                edate = userLogList[i].loginYear+"-12-31";
                                if(edate>endTime)
                                    edate=endTime;
                                $("#defineInfo thead td").eq(0).html("登录年份");
                                nextJson = {
                                    "flag" : 4,
                                    "sort" : -1,
                                    "sign" : 1,
                                    "mdate" : mdate,
                                    "edate" : edate,
                                    "loginYear" : userLogList[i].loginYear
                                };
                            }else if(beginMonth !== endMonth){//垮月
                                mdate = userLogList[i].loginYear+"-01";
                                if(mdate<data.beginTime)
                                    mdate=data.beginTime;
                                var thisDate= new Date(userLogList[i].loginYear.split("-")[0],userLogList[i].loginYear.split("-")[1],0);
                                var days  = thisDate.getDate();
                                edate = userLogList[i].loginYear+"-"+days;
                                if(edate>endTime)
                                    edate=endTime;
                                $("#defineInfo thead td").eq(0).html("登录月份");
                                nextJson = {
                                    "flag" : 3,
                                    "sort" : -1,
                                    "sign" : 1,
                                    "mdate" : mdate,
                                    "edate" : edate,
                                    "loginYear" : userLogList[i].loginYear
                                };
                            }else{
                                mdate = userLogList[i].loginYear;
                                edate = userLogList[i].loginYear;
                                $("#defineInfo thead td").eq(0).html("登录日期");
                                nextJson = {
                                    "flag" : 2,
                                    "sort" : -1,
                                    "sign" : 0,
                                    "mdate" : mdate,
                                    "edate" : edate,
                                    "loginYear" : userLogList[i].loginYear
                                };
                            }

                            listStr +=  '<tr>' +
                                '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].loginYear)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].sum)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this))">登录查询</span></td></tr>';
                        }

                        $("#defineInfo tbody").html(listStr);
                        setPage($("#ye-defineInfo"),mycurr,ttlPage,"records",jsonStr);
                        break;
                }
            }else if(sort===1){
                $(".flagTab").hide();
                switch (flag){
                    //按本日姓名排序 flag === 0 && sort === 1
                    case 0 :
                        $("#dayName").show().siblings().hide();
                        //表头展示
                        showLoginNav({"total":data.total,"date":date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});

                        for(var i=0;i<userLogList.length;i++){
                            var nextJson = {
                                "flag" : 1,
                                "sort" : 0,
                                "sign" : -1,
                                "userID" : userLogList[i].user_,
                                "mdate" : new Date(userLogList[i].operatetime).format('yyyy-MM-dd')
                            };
                            listStr +=  '<tr>' +
                                '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].userName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].departmentName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                '<td>'+new Date(userLogList[i].operatetime).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                                '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this))">登录查询</span></td></tr>';
                        }
                        $("#dayName tbody").html(listStr);
                        setPage($("#ye-dayName"),mycurr,ttlPage,"records",jsonStr);
                        break;

                    //按本月姓名排序 flag === 1 && sort === 1
                    case 1 :
                        $("#monthName").show().siblings().hide();
                        //表头展示
                        showLoginNav({"total":data.total,"date":data.firstdate+'~'+date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});
                        for(var i=0;i<userLogList.length;i++){
                            var nextJson = {
                                "flag" : 2,
                                "sort" : -1,
                                "sign" : 1,
                                "userID" : userLogList[i].user_,
                                "mdate" : data.firstdate,
                                "edate" : date
                            };
                            listStr +=  '<tr>' +
                                '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].userName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].departmentName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this))">登录查询</span></td></tr>';
                        }
                        $("#monthName tbody").html(listStr);
                        setPage($("#ye-monthName"),mycurr,ttlPage,"records",jsonStr);
                        break;

                    //按本年姓名排序 flag === 2 && sort === 1
                    case 2 :
                        $("#yearName").show().siblings().hide();
                        //表头展示
                        showLoginNav({"total":data.total,"date":data.firstdate+'~'+date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});
                        for(var i=0;i<userLogList.length;i++){
                            var nextJson = {
                                "flag" : 3,
                                "sort" : -1,
                                "sign" : 2,
                                "userID" : userLogList[i].user_,
                                "mdate" : data.firstdate,
                                "edate" : date
                            };
                            listStr +=  '<tr>' +
                                '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].userName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].departmentName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this))">登录查询</span></td></tr>';
                        }
                        $("#yearName tbody").html(listStr);
                        setPage($("#ye-yearName"),mycurr,ttlPage,"records",jsonStr);
                        break;

                    //按自定义姓名排序 flag === 3 && sort === 1
                    case 3 :
                        $("#defineName").show().siblings().hide();
                        //表头展示
                        showLoginNav({"total":data.total,"date":data.beginTime+'~'+data.endTime,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});
                        for(var i=0;i<userLogList.length;i++){
                            var nextJson = {
                                "flag" : 4,
                                "sort" : -1,
                                "sign" : 2,
                                "userID" : userLogList[i].user_,
                                "mdate" : data.beginTime,
                                "edate" : data.endTime
                            };
                            listStr +=  '<tr>' +
                                '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].userName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].departmentName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this))">登录查询</span></td></tr>';
                        }
                        $("#defineName tbody").html(listStr);
                        setPage($("#ye-defineName"),mycurr,ttlPage,"records",jsonStr);
                        break;
                }
            }
        }
    });
}



/* creator：张旭博，2017-10-12 15:32:53，自定义查询点击登录查询查询按钮 */
// wyu: Not used 20180425
function seeDefinedLoginRecordDetailBtn(selector) {
    var paramJson = selector.parents("tr").children().eq(0).html();
    seeDefinedLoginRecordListDetail(1,20,paramJson);
    setLoginState(JSON.parse(paramJson));
}

/* creator：张旭博，2017-10-12 15:33:24，自定义查询登录记录查看详情 */
// wyu: Not used 20180425
function seeDefinedLoginRecordListDetail(curr,totalPage,paramJson) {
    toggleButton("hide");
    var param = JSON.parse(paramJson);
    var data = {
        "pageNumber":curr,
        "quantum":totalPage,
        "type":param.sort,
        "beginTime":param.beginTime,
        "endTime":param.endTime,
        "userId":param.userId
    };
    $.ajax({
        url:"../loginrecord/getLogByCustom.do",
        data:data,
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;},
        success:function(data){
            loading.close();
            var mycurr=data.pageNumber ;
            var ttlPage=data.totalPage;
            var userLogList = data.totaluserLog;
            var typeNum     = parseInt(data.typeNum); //级数
            $(".dataNav").show();
            var listStr = "";
            //插入列表数据
            if(param.sort === 1){
                $("#defineInfo").show().siblings().hide();
                $("#ye-defineInfo").html("");

                //表头展示
                showLoginNav({"total":data.totalNumAll,"date":data.beginTime+'~'+data.endTime,"pcNum":data.pcNumAll,"mobileNum":data.phoneNumAll, "duration": data.duration});

                //设置分页
                setPage($("#ye-defineInfo"),mycurr,ttlPage,"definedRecordsDetail",paramJson);

                //设置表格表头的显示

                switch (typeNum){
                    case 1:
                        $("#defineInfo thead td").eq(0).html("登录年份");
                        break;
                    case 2:
                        $("#defineInfo thead td").eq(0).html("登录月份");
                        break;
                    case 3:
                        $("#defineInfo thead td").eq(0).html("登录日期");
                        break;
                }
                for(var i=0;i<userLogList.length;i++){
                    var beginTime   = userLogList[i].beginDate.substring(0,10);
                    var endTime     = userLogList[i].endDate.substring(0,10);
                    var periodTime  = beginTime;
                    switch (typeNum){
                        case 1:
                            periodTime  = beginTime.substring(0,4);
                            break;
                        case 2:
                            periodTime  = beginTime.substring(0,7);
                            break;
                    }

                    if(typeNum === 3){
                        var nextJson    = {
                            "flag" : 2,
                            "sort" : -1,
                            "sign" : 0,
                            "mdate" : beginTime,
                            "loginYear" : beginTime
                        };
                        listStr +=  '<tr>' +
                            '<td class="hd">'+ JSON.stringify(nextJson) +'</td>'+
                            '<td>' + periodTime + '</td>'+
                            '<td>' + chargeNull(userLogList[i].userNum) + '</td>'+
                            '<td>' + chargeNull(userLogList[i].durationTime)+'</td>'+
                            '<td>' + chargeNull(userLogList[i].totalNum) + '</td>'+
                            '<td>' + chargeNull(userLogList[i].pcNum) + '</td>'+
                            '<td>' + chargeNull(userLogList[i].phoneNum) + '</td>'+
                            '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this))">登录查询</span></td></tr>';
                    }else{
                        var nextJson    = {
                            "flag" : -1,
                            "sort" : param.sort,
                            "sign" : -1,
                            "beginTime" : beginTime,
                            "endTime" : endTime
                        };
                        listStr +=  '<tr>' +
                            '<td class="hd">'+ JSON.stringify(nextJson) +'</td>'+
                            '<td>' + periodTime + '</td>'+
                            '<td>' + chargeNull(userLogList[i].userNum) + '</td>'+
                            '<td>' + chargeNull(userLogList[i].durationTime)+'</td>'+
                            '<td>' + chargeNull(userLogList[i].totalNum) + '</td>'+
                            '<td>' + chargeNull(userLogList[i].pcNum) + '</td>'+
                            '<td>' + chargeNull(userLogList[i].phoneNum) + '</td>'+
                            '<td><span class="ty-color-blue" onclick="seeDefinedLoginRecordDetailBtn($(this))">登录查询</span></td></tr>';
                    }

                }

                $("#defineInfo tbody").html(listStr);
            }else{
                $("#defineDetail").show().siblings().hide();
                $("#ye-defineDetail").html("");

                //表头展示
                showLoginNav({"total":data.totalNumAll,"date":data.beginTime+'~'+data.endTime,"pcNum":data.pcNumAll,"mobileNum":data.phoneNumAll,"postName":userLogList[0].postName,"departmentName":userLogList[0].departName,"userName":userLogList[0].userName, "duration": data.duration});

                //设置分页
                setPage($("#ye-defineDetail"),mycurr,ttlPage,"definedRecordsDetail",paramJson);
                //设置表格表头的显示
                switch (typeNum){
                    case 1:
                        $("#defineDetail thead td").eq(3).html("登录年份");
                        break;
                    case 2:
                        $("#defineDetail thead td").eq(3).html("登录月份");
                        break;
                    case 3:
                        $("#defineDetail thead td").eq(3).html("登录日期");
                        break;
                }
                for(var i=0;i<userLogList.length;i++){

                    var beginTime   = userLogList[i].beginDate.substring(0,10);
                    var endTime     = userLogList[i].endDate.substring(0,10);
                    var periodTime  = beginTime;

                    switch (typeNum){
                        case 1:
                            periodTime  = beginTime.substring(0,4);
                            break;
                        case 2:
                            periodTime  = beginTime.substring(0,7);
                            break;
                    }

                    if(typeNum === 3){
                        var nextJson    = {
                            "flag" : 1,
                            "sort" : 0,
                            "sign" : -1,
                            "mdate" : beginTime,
                            "userID" : userLogList[i].userId
                        };
                        listStr +=     '<tr>' +
                            '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].userName)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].departName)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                            '<td>'+chargeNull(periodTime)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].totalNum)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].pcNum)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].phoneNum)+'</td>'+
                            '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this)) ">登录查询</span></td></tr>';
                    }else{
                        var nextJson    = {
                            "flag" : -1,
                            "sort" : param.sort,
                            "sign" : -1,
                            "beginTime" : beginTime,
                            "endTime" : endTime,
                            "userId" : userLogList[i].userId
                        };
                        listStr +=     '<tr>' +
                            '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].userName)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].departName)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                            '<td>'+chargeNull(periodTime)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].totalNum)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].pcNum)+'</td>'+
                            '<td>'+chargeNull(userLogList[i].phoneNum)+'</td>'+
                            '<td><span class="ty-color-blue" onclick="seeDefinedLoginRecordDetailBtn($(this)) ">登录查询</span></td></tr>';
                    }
                }
                $("#defineDetail tbody").html(listStr);
                // setPage($("#ye-defineDetail"),mycurr,ttlPage,"recordsDetail",jsonStr);



            }

        },
        error:function (meg) {
            loading.close();
            alert("连接错误，请稍后重试！");
        }
    })
}

/* creator：张旭博，2017-09-22 09:28:01，本日、本月、本年点击登录按钮 */
// wyu：点击列表中登录查询链接，包括本日、本月、本年和自定义查询结果列表中的“登录查询”
// wyu：有用 20180425
function seeLoginRecordListDetailBtn(selector) {
    var paramJson = selector.parents("tr").children().eq(0).html();
    seeLoginRecordListDetail(1,20,paramJson);
    setLoginState(JSON.parse(paramJson));
}

/* updater：张旭博，2017-09-20 13:43:08，本日、本月、本年登录记录查看详情 */
// wyu：点击列表中登录查询链接，包括本日、本月、本年和自定义查询结果列表中的“登录查询”
// wyu：有用 20180425
function seeLoginRecordListDetail(curr,totalPage,paramJson) {
    toggleButton("hide");
    var param = JSON.parse(paramJson);
    param["type"] = 2;

    var data = {
        "flag":param.flag,
        "currPage":curr,
        "pageSize":totalPage,
        "oid": sphdSocket.user.oid,
        "userID" : sphdSocket.user.userID
    };
    var flag = param.flag;
    var sort = param.sort;
    var sign = param.sign;
    var mdate = param.mdate;
    var edate = param.edate;

    var userID = param.userID;
    if(userID === undefined){
        userID = param.userId;
    }
    var loginYear = param.loginYear;
    if(mdate !== undefined && mdate !== -1){data["mdate"] = mdate;}
    if(edate !== undefined && edate !== -1){data["edate"] = edate;}
    if(userID !== undefined && userID !== -1){data["userID"] = userID;}
    if(sort !== undefined && sort !== -1){data["sort"] = sort;}
    if(sign !== undefined && sign !== -1){data["sign"] = sign;}
    if(loginYear !== undefined && loginYear !== -1){data["loginYear"] = loginYear;}
    $.ajax({
        // url:"../loginTeam/loginRecordPersonalTeam.do",
        url:"../loginrecord/loginRecordPersonal.do",
        data:data,
        success:function(data){
            loading.close();
            var mycurr=data.currPage;
            var ttlPage=data.totalPage;
            var userLogList = data.userLogList;
            var date = data.date;
            var jsonStr = JSON.stringify({
                "flag": flag,
                "sort": sort,
                "sign": sign
                , "duration": data.duration});
            $(".pcInfo table tbody").html("");
            $(".mbInfo table tbody").html("");
            $(".moreList").hide();
            $(".dataNav").show();

            var loginStr = '';
            switch (flag) {
                case 1:
                    //表头展示
                    showLoginNav({"total":data.total,"date":data.date,"pcNum":data.computer,"mobileNum":data.app,"postName":data.postName,"departmentName":data.departmentName,"userName":data.userName, "duration": data.duration});

                    $(".moreList").show();
                    $(".dataNav").hide();
                    for(var i=0;i<userLogList.length;i++){
                        loginStr += '<tr id="'+userLogList[i].user_+'"><td>'+new Date(userLogList[i].operatetime).format('yyyy-MM-dd')+'</td>'+
                            '<td>'+new Date(userLogList[i].operatetime).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                            '<td>'+userLogList[i].durationTime+'</td>'+
                            '<td>'+userLogList[i].ip+'</td></tr>';
                    }
                    switch (sort){
                        case 0:
                            //样式切换
                            $(".signTab").find("span").eq(0).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
                            $("#pcInfo").show().siblings().hide();
                            $("#pcInfo tbody").html(loginStr);
                            setPage($("#ye-pcInfo"),mycurr,ttlPage,"recordsDetail",paramJson);
                            break;
                        case 1:
                            $(".signTab").find("span").eq(1).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
                            $("#mbInfo").show().siblings().hide();
                            $("#mbInfo tbody").html(loginStr);
                            setPage($("#ye-mbInfo"),mycurr,ttlPage,"recordsDetail",paramJson);
                            break;
                    }
                    break;
                case 2:
                    switch (sign){
                        case 0:
                            $("#dayInfo").show().siblings().hide();
                            $("#ye-dayInfo").html("");

                            //表头展示
                            showLoginNav({"total":data.total,"date":date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});

                            for(var i=0;i<userLogList.length;i++){
                                loginStr +=     '<tr id="'+userLogList[i].loginYear+'" flag="2" sort="0"><td>'+userLogList[i].userName+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].departmentName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                                    '<td>'+new Date(userLogList[i].operatetime).format("yyyy-MM-dd hh:mm:ss")+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                    '<td>'+chargePort(userLogList[i].type)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].ip)+'</td></tr>';
                            }
                            $("#dayInfo tbody").html(loginStr);
                            setPage($("#ye-dayInfo"),mycurr,ttlPage,"recordsDetail",paramJson);
                            break;
                        case 1:
                            $("#monthDetail").show().siblings().hide();

                            //表头展示
                            showLoginNav({"total":data.total,"date":data.firstdate +'~' +date,"pcNum":data.computer,"mobileNum":data.app,"postName":userLogList[0].postName,"departmentName":userLogList[0].departmentName,"userName":userLogList[0].userName, "duration": data.duration});

                            for(var i=0;i<userLogList.length;i++){
                                var nextJson = {
                                    "flag" : 1,
                                    "sort" : 0,
                                    "sign" :-1,
                                    "userID" : userLogList[i].user_,
                                    "mdate" : userLogList[i].loginYear
                                };
                                loginStr +=     '<tr>' +
                                    '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].userName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].departmentName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].loginYear)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                    '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this)) ">登录查询</span></td></tr>';
                            }
                            $("#monthDetail tbody").html(loginStr);
                            setPage($("#ye-monthDetail"),mycurr,ttlPage,"recordsDetail",paramJson);
                            break;
                    }
                    break;
                case 3:
                    switch (sign){
                        case 1:
                            $("#yearInfoDetail").show().siblings().hide();
                            //表头展示
                            showLoginNav({"total":data.total,"date":date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});
                            $("#yearInfoDetail thead td").eq(0).html("登录日期");

                            for(var i=0;i<userLogList.length;i++){
                                var nextJson = {
                                    "flag" : 2,
                                    "sort" : -1,
                                    "sign" : 0,
                                    "mdate" : new Date(userLogList[i].operatetime).format('yyyy-MM-dd'),
                                    "loginYear" : new Date(userLogList[i].operatetime).format('yyyy-MM-dd')
                                };
                                loginStr +=     '<tr>' +
                                    '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                    '<td>'+ new Date(userLogList[i].operatetime).format('yyyy-MM-dd') + '</td>' +
                                    '<td>'+chargeNull(userLogList[i].sum)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                    '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this),0) ">登录查询</span></td></tr>';
                            }
                            $("#yearInfoDetail tbody").html(loginStr);
                            setPage($("#ye-yearInfoDetail"),mycurr,ttlPage,"recordsDetail",paramJson);
                            break;
                        case 2:
                            //表头展示
                            showLoginNav({"total":data.total,"date":data.firstdate +'~' +date,"pcNum":data.computer,"mobileNum":data.app,"postName":userLogList[0].postName,"departmentName":userLogList[0].departmentName,"userName":userLogList[0].userName, "duration": data.duration});
                            $("#yearDetail").show().siblings().hide();
                            $("#yearDetail thead td").eq(3).html("登录月份");

                            for(var i=0;i<userLogList.length;i++){
                                var thisDate= new Date(userLogList[i].loginYear.split("-")[0],userLogList[i].loginYear.split("-")[1],0);
                                var days  = thisDate.getDate();
                                var mdate = userLogList[i].loginYear+"-01";
                                if(mdate<data.firstdate)
                                    mdate=data.firstdate;
                                var edate = userLogList[i].loginYear+"-"+days;
                                if(edate>date)
                                    edate=date;
                                var nextJson = {
                                    "flag" : 2,
                                    "sort" : -1,
                                    "sign" : 1,
                                    "mdate" : mdate,
                                    "edate" : edate,
                                    "userID" : userLogList[i].user_
                                };
                                loginStr +=     '<tr>' +
                                    '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].userName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].departmentName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].loginYear)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                    '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this)) ">登录查询</span></td></tr>';
                            }
                            $("#yearDetail tbody").html(loginStr);
                            setPage($("#ye-yearDetail"),mycurr,ttlPage,"recordsDetail",paramJson);
                            break;
                    }
                    break;
                case 4:
                    switch (sign){
                        case 1:
                            $("#yearInfoDetail").show().siblings().hide();
                            //表头展示
                            showLoginNav({"total":data.total,"date":data.firstdate +'~' +date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration});
                            var beginYear = data.firstdate.split("-")[0];
                            var beginMonth = data.firstdate.split("-")[1];
                            var endYear = date.split("-")[0];
                            var endMonth = date.split("-")[1];
                            if(beginYear !== endYear){//跨年
                                $("#yearInfoDetail thead td").eq(0).html("登录年份");
                            }else if(beginMonth !== endMonth){//垮月
                                $("#yearInfoDetail thead td").eq(0).html("登录月份");
                            }else {
                                $("#yearInfoDetail thead td").eq(0).html("登录日期");
                            }

                            for(var i=0;i<userLogList.length;i++){
                                var arr=userLogList[i].loginYear.split('-');
                                flag=5-arr.length;
                                console.log(flag);
                                var mdate;
                                var edate;
                                var thisDate = new Date(arr[0],arr[1],0);
                                var days = thisDate.getDate();
                                mdate = arr[0]+"-"+arr[1]+"-01";
                                edate = arr[0]+"-"+arr[1]+"-"+days;
                                switch(flag){
                                    case 3:
                                        sign = 1;
                                        break;
                                    case 2:
                                        sign = 0;
                                        break;
                                    case 1:
                                        mdate = userLogList[i].loginYear;
                                        edate = userLogList[i].loginYear;
                                        break;
                                }
                                mdate=mdate>data.firstdate?mdate:data.firstdate;
                                edate=edate<date?edate:date;
                                var nextJson = {
                                    "flag" : flag,
                                    "sort" : -1,
                                    "sign" : sign,
                                    "mdate" : mdate,
                                    "edate" : edate,
                                    "loginYear" : userLogList[i].loginYear
                                };
                                loginStr +=     '<tr>' +
                                    '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                    '<td>'+ userLogList[i].loginYear + '</td>' +
                                    '<td>'+chargeNull(userLogList[i].sum)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                    '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this),0) ">登录查询</span></td></tr>';
                            }
                            $("#yearInfoDetail tbody").html(loginStr);
                            setPage($("#ye-yearInfoDetail"),mycurr,ttlPage,"recordsDetail",paramJson);
                            break;
                        case 2:
                            //表头展示
                            showLoginNav({"total":data.total,"date":data.firstdate +'~' +date,"pcNum":data.computer,"mobileNum":data.app,"postName":userLogList[0].postName,"departmentName":userLogList[0].departmentName,"userName":userLogList[0].userName, "duration": data.duration});
                            $("#yearDetail").show().siblings().hide();
                            var beginYear = data.firstdate.split("-")[0];
                            var beginMonth = data.firstdate.split("-")[1];
                            var endYear = date.split("-")[0];
                            var endMonth = date.split("-")[1];
                            if(beginYear !== endYear){//跨年
                                $("#yearDetail thead td").eq(3).html("登录年份");
                            }else if(beginMonth !== endMonth){//垮月
                                $("#yearDetail thead td").eq(3).html("登录月份");
                            }else {
                                $("#yearDetail thead td").eq(3).html("登录日期");
                            }

                            for(var i=0;i<userLogList.length;i++){
                                var arr=userLogList[i].loginYear.split('-');
                                flag=4-arr.length;
                                console.log(flag);
                                var mdate;
                                var edate;
                                switch(flag){
                                    case 3:
                                        mdate = arr[0]+"-01-01";
                                        edate = arr[0]+"-12-31";
                                        break;
                                    case 2:
                                        var thisDate = new Date(arr[0],arr[1],0);
                                        var days = thisDate.getDate();
                                        mdate = arr[0]+"-"+arr[1]+"-01";
                                        edate = arr[0]+"-"+arr[1]+"-"+days;
                                        sign = 1;
                                        break;
                                    case 1:
                                        mdate = userLogList[i].loginYear;
                                        edate = userLogList[i].loginYear;
                                        break;
                                }
                                mdate=mdate>data.firstdate?mdate:data.firstdate;
                                edate=edate<date?edate:date;
                                var nextJson = {
                                    "flag" : flag,
                                    "sort" : 0,
                                    "sign" : sign,
                                    "mdate" : mdate,
                                    "edate" : edate,
                                    "loginYear" : userLogList[i].loginYear,
                                    "userID" : userLogList[i].user_
                                };
                                loginStr +=     '<tr>' +
                                    '<td class="hd">'+JSON.stringify(nextJson)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].userName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].departmentName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].postName)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].loginYear)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].durationTime)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].count)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].com)+'</td>'+
                                    '<td>'+chargeNull(userLogList[i].app)+'</td>'+
                                    '<td><span class="ty-color-blue" onclick="seeLoginRecordListDetailBtn($(this)) ">登录查询</span></td></tr>';
                            }
                            $("#yearDetail tbody").html(loginStr);
                            setPage($("#ye-yearDetail"),mycurr,ttlPage,"recordsDetail",paramJson);
                            break;
                    }
                    break;
            }
        }
    })
}

/* updater：侯杏哲，2021-04-26 13:43:38，自定义查询 */
function sureLoginQuery(){
    //自定义查询设置选中，其他本月等按钮设置不选中
    $("#loginQueryBtn").addClass("ty-btn-blue");
    $(".flagTab .ty-btn-group .ty-btn").removeClass("ty-btn-active-blue");
    // var tnIndex = $(".sortTab .ty-btn-group span").index($(".sortTab .ty-btn-group span.ty-btn-active-blue"));
    var beginTime = $("#queryBeginTime").val() ;
    var endTime = $("#queryEndTime").val();
    getLoginRecordList(1,20,3,0)
    var json = {
        "type" : 1,
        "flag" : 3,
        "sort" :0,
        "beginTime" : beginTime,
        "endTime" : endTime
    };
    setLoginState(json);
}

/* creator：张旭博，2017-10-13 09:02:30，登录总显示 */
function showLoginNav(data){
    // if(data.userName === undefined){
    //     $(".mainDataNav .LPL_person").hide()
    // }else{
    //     $(".mainDataNav .LPL_person").show()
    // }
    /*if(typeof(data.userName)=='undefined') {
        $(".mainDataNav .LPI_time").html(chargeNull(data.date,''));
    } else {
        $(".mainDataNav .LPI_time").html(data.date + ' ' +data.userName);
    }*/
    $(".mainDataNav .LPI_time").html(chargeNull(data.date,''));
    $(".mainDataNav .LPI_name").html(chargeNull(data.userName,''));
    /*$(".mainDataNav .LPI_post").html(chargeNull(data.departmentName,''));
    $(".mainDataNav .LPI_org").html(chargeNull(data.postName,''));*/
    $(".mainDataNav .total_log").html(chargeNull(data.duration,''));
    $(".mainDataNav .loginSum").html(chargeNull(data.total,''));
    $(".mainDataNav .pcNum").html(chargeNull(data.pcNum,''));
    $(".mainDataNav .mobileNum").html(chargeNull(data.mobileNum,''));
    // if(data.sign !== undefined && data.flag === 1){
    //     if(data.userName === ""){
    //         data.userName = "--"
    //     }
    //
    //
    // }else{
    //     if(typeof (data.quantum) !== "string"){
    //         $(".mainDataNav .LPI_time").html(data.quantum.sTime + "~" + data.quantum.eTime);
    //     }else{
    //         $(".mainDataNav .LPI_time").html(data.quantum);
    //     }
    //     $(".mainDataNav .loginSum").html(data.total);
    //     $(".mainDataNav .pcNum").html(data.pnum);
    //     $(".mainDataNav .mobileNum").html(data.mnum);
    //
    // }

}

/* creator：张旭博，2017-09-20 13:43:54，设置分类标记 */
function setLoginState(json) {
    loginHistory.push(json);
}

/* creator：张旭博，2017-09-22 09:28:55，切换返回状态 */
function toggleButton(state) {
    if(state === "show"){
        $(".flagTab").show();
        $(".sortTab").show();
        $(".goBack").hide();
    }else{
        $(".flagTab").hide();
        $(".sortTab").hide();
        $(".goBack").show();
    }
}

/* creator：张旭博，2017-09-20 13:44:05，返回上一次状态 */
function goBack() {
    toggleButton("show");
    $(".moreList").hide();
    $(".dataNav").show();
    loginHistory.pop(); //删除最后一条记录
    // alert(loginHistory[loginHistory.length-1].sort);
    var param = loginHistory[loginHistory.length-1];
    var paramStr = JSON.stringify(param);
    var type = param.type;
    var flag = param.flag;
    var sort = param.sort;
    if(type === 1){
        if(flag === 3){
            var beginTime = param.beginTime;
            var endTime = param.endTime;
            getLoginRecordList(1,20,3,0)
        }else{
            if(flag === 0 && sort === 1){
                $(".signTab .ty-btn-group .ty-btn").eq(0).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
            }
            getLoginRecordList(1,20,flag,sort);
        }
    }else{
        if(flag==1||flag==2){
            if(sort==-1){
                $(".signTab .ty-btn-group .ty-btn").eq(0).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
            }
        }
        if(flag === -1){
            if(sort==2){ //creator:lyt 2017/12/14
                $(".signTab .ty-btn-group .ty-btn").eq(0).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
            }
            seeDefinedLoginRecordListDetail(1,20,paramStr)
        }else{
            seeLoginRecordListDetail(1,20,paramStr)
        }
    }

}

/* ------------------- 辅助方法 --------------------- */
/* creator：张旭博，2017-09-22 09:29:32，端口数组字符转换 */
function chargePort(port) {
    switch (port){
        case "1":
            return "手机端";
        case "3":
            return "电脑端"
    }
}
// 为空赋值
function chargeNull(str,nullvalue){
    if(nullvalue === null  || nullvalue === undefined)
        nullvalue = '--';
    if(str === null  || str === undefined){
        return nullvalue;
    }else{
        return str ;
    }
}

laydate.render({elem: '#searchStart'});
laydate.render({elem: '#searchEnd'});
laydate.render({elem: '#queryBeginTime'});
laydate.render({elem: '#queryEndTime'});





//----原来的方法---
function query(){
    $("#form1").submit();
}
function queryByMouth(aa){
    var uid = $(aa).attr("id");
    openWindow('${pageContext.request.contextPath}/general/LoginRecordByMouth.do?uid='+uid,'1100','700');
}