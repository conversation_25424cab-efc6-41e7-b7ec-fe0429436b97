/* =========  我的工作记录主页 ===========*/
$(function(){
    $(".workQuery").find('input,select,textarea').val("");

    // 查询按钮的点击事件
    $(".workQuery").on("click","button",function () {
        var name = $(this).data("name");
        switch (name) {
            case 'monthScreen':
                // 查看其他月份的工作记录
                $(".btn-back").show().siblings(".btn-input").hide();
                var month = $("#countKey").val()
                $(".tbl_month").data("month", month)
                initMonth()
                $("#countKey").val("")
                break;
            case 'back':
                $(".btn-back").hide().siblings(".btn-input").show();
                $(".tbl_month").data("month", '')
                initMonth()
                break
        }
    });
    // 日历某一天 - 点击
    $(".tbl_month").on("click", 'td', function () {
        var thisMonth = $(".tbl_month").data("month")
        var thisDate = thisMonth + '-' + ($(this).attr("day")>9?$(this).attr("day"):'0'+$(this).attr("day"))
        console.log(diff)
        // 初始化数据
        if ($(this).attr("day") !== '') {
            getWorkOfSomeday(thisDate)
        }
    })
    // 工作录入内的所有按钮 - 点击
    $("#workInput, #workChange, #workSee, #workRecordSee, #workFutureSee").on("click", 'button', function () {
        var name = $(this).attr("name")
        var type = $(this).attr("type")

        if (name === 'otherInnerWork' || name === 'editRoutineWork' || name === 'editInnerWork' || name === 'editOutterWork' || name === 'editRecentWork' || name === 'deleteWork') {
            var date = $(".tbl_month").data("date")
            var diff = moment(hostTime).diff(moment(date), 'days')
            if (diff > 40) {
                layer.msg("操作失败！公司规定40天前的工作日记不再允许修改。")
                return false
            }
        }

        switch (name) {
            case 'seeLeave':
                seeLeaveRecord($(this))
                break;
            case 'otherInnerWork':
                bounce_Fixed2.show($("#chooseOtherInnerWork"))
                var to_remove = []
                $("#workInput .innerWork tbody tr.add").each(function () {
                    var info = $(this).find("td.hd").html()
                    if (info) {info = JSON.parse(info)}
                    info.id = $(this).data("id")
                    to_remove.push(info)
                })
                var otherList = $("#workInput .innerWork").data("otherList")
                var newList = otherList.filter(arrItem => {
                    // 找到 被删除数组对象中 的主键 ，此处是 ID
                    const splitArrList = to_remove.map(splitArrItem => {
                        return splitArrItem['id']
                    })
                    return !splitArrList.includes(arrItem['id'])
                })
                var str = ''
                if (newList) {
                    for (var i = 0;i < newList.length; i++) {
                        var durationPlan = -(-newList[i].durationPlanHour - (newList[i].durationPlanMinute/60).toFixed(2))
                        var innerItem = {
                            id: newList[i].id,
                            previousRecord: newList[i].id,
                            contentPlan: newList[i].contentPlan,
                            timePlan: moment(newList[i].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                            timePlanDate: moment(newList[i].timePlan).format("YYYY-MM-DD"),
                            timePlanTime: moment(newList[i].timePlan).format("HH:mm"),
                            durationPlanMinute: newList[i].durationPlanMinute,
                            durationPlanHour: newList[i].durationPlanHour,
                            durationPlan: durationPlan,
                            fulfillment: newList[i].fulfillment,
                            type: 1
                        }
                        str +=  '<tr data-id="'+newList[i].id+'">' +
                            '   <td><div class="ty-checkbox"><input type="checkbox" id="innerCheckbox'+i+'"><label for="innerCheckbox'+i+'"></label></div></td>'+
                            '   <td>'+innerItem.contentPlan+'</td>'+
                            '   <td>'+innerItem.timePlanDate+'</td>'+
                            '   <td>'+innerItem.timePlanTime+'</td>'+
                            '   <td>'+innerItem.durationPlan+'小时</td>'+
                            '   <td class="hd">'+JSON.stringify(innerItem)+'</td>'+
                            '</tr>'
                    }
                }
                $("#chooseOtherInnerWork tbody").html(str)
                countWords()
                break
            case 'editRoutineWork':
                bounce_Fixed2.show($("#editRoutineWork"))
                $("#editRoutineWork").find(".repeatList").html("")
                setEveryTime(bounce, 'routineWorkInput')
                var info = $(this).parents("tr").find("td.hd").html()
                if (info) { info = JSON.parse(info) }
                info.timePlanTime = info.timePlanTime|| '--'
                $("#editRoutineWork .routineFulfillment").val(info.routineFulfillment).change()
                for (var key in info) {
                    $("#editRoutineWork .routine_"+key).html(info[key])
                    $("#editRoutineWork [name='"+key+"']:visible").val(info[key])
                }

                if ($("#workChange").is(":visible")) {
                    $("#editRoutineWork .controlBtn").hide()
                } else {
                    $("#editRoutineWork .controlBtn").show()
                }
                var $that = $(this).parents("tr")
                $("#editRoutineWork .sureBtn").unbind().on("click", function () {
                    sureEditRoutineWork($that)
                })
                countWords()
                break
            case 'editInnerWork':
                bounce_Fixed2.show($("#editInnerWork"))
                setEveryTime(bounce, 'innerWorkInput')
                var info = $(this).parents("tr").find("td.hd").html()
                if (info) { info = JSON.parse(info) }
                $("#editInnerWork").data("radio", {
                    content: info.contentPlan,
                    timeFactTime: info.timePlanTime,
                    durationFactHour: info.durationPlanHour,
                    durationFactMinute: info.durationPlanMinute
                })
                $("#editInnerWork .fulfillment").val(info.fulfillment).change()
                var isPlanContent = chargeEqual(info.contentPlan, info.content)
                var isPlanTime = chargeEqual(info.timePlanTime, info.timeFactTime)
                var isPlanHour = chargeEqual(info.durationPlan, info.durationFact)
                var choosePlanTime = $("#editInnerWork [name='isPlanTime'][value='"+isPlanTime+"']:visible")

                var choosePlanContent = $("#editInnerWork [name='isPlanContent'][value='"+isPlanContent+"']:visible")
                var choosePlanHour = $("#editInnerWork [name='isPlanHour'][value='"+isPlanHour+"']:visible")

                choosePlanTime.prop("checked", true)
                updateInputDialog(choosePlanTime)
                choosePlanContent.prop("checked", true)
                updateInputDialog(choosePlanContent)
                choosePlanHour.prop("checked", true)
                updateInputDialog(choosePlanHour)

                for (var key in info) {
                    $("#editInnerWork .inner_"+key).html(info[key])
                    $("#editInnerWork [name='"+key+"']:visible").val(info[key])
                }

                var $that = $(this).parents("tr")
                $("#editInnerWork .sureBtn").unbind().on("click", function () {
                    sureEditInnerWork($that)
                })
                countWords()
                break
            case 'editOutterWork':
                $("#editOutterWork .thisDayWeek").html(moment($(".tbl_month").data("date")).format("YYYY年MM月DD日 dddd"))
                setEveryTime(bounce, 'outterWorkInput')
                $("#editOutterWork .repeatList").html("")
                if ($(this).html() === '增加') {
                    $("#editOutterWork").find("input,select,textarea").val("")
                } else {
                    var info = $(this).parents("tr").find("td.hd").html()
                    if (info) { info = JSON.parse(info) }
                    for (var key in info) {
                        $("#editOutterWork [name='"+key+"']").val(info[key])
                    }
                }
                bounce_Fixed2.show($("#editOutterWork"))
                countWords()
                if ($("#workChange").is(":visible")) {
                    $("#editOutterWork .controlBtn").hide()
                } else {
                    $("#editOutterWork .controlBtn").show()
                }
                var $that = $(this).parents("tr")
                $("#editOutterWork .sureBtn").unbind().on("click", function () {
                    sureEditOutterWork($that)
                })

                break
            case 'editRecentWork':
                setEveryTime(bounce, 'recentWorkInput')
                bounce_Fixed2.show($("#editRecentWork"))

                if (type === 'add') {
                    $("#editRecentWork").find("input,select,textarea").val("")
                    var $that = $(this).parents("tr")
                    $("#editRecentWork .sureBtn").unbind().on("click", function () {
                        sureEditRecentWork()
                    })
                } else {
                    var info = $(this).parents("tr").find("td.hd").html()
                    if (info) { info = JSON.parse(info) }
                    for (var key in info) {
                        $("#editRecentWork [name='"+key+"']").val(info[key])
                    }
                    var $that = $(this).parents("tr")
                    $("#editRecentWork .sureBtn").unbind().on("click", function () {
                        sureEditRecentWork($that)
                    })
                }
                countWords()
                break
            case 'deleteWork':
                var info = $(this).parents("tr").find("td.hd").html()
                var isAdd = $(this).parents("tr").hasClass("add")
                if (isAdd) {
                    $(this).parents("tr").remove()
                } else {
                    let bounceName = $(this).parents(".bonceContainer").attr("id")
                    if (bounceName === 'workChange') {
                        let length = $(this).parents("tbody").find("tr").length
                        if (length === 1) {
                            layer.msg('您的工作记录至少要有一条！')
                            return false
                        }
                    }

                    if (info) { info = JSON.parse(info) }
                    bounce_Fixed2.show($("#confirm"))
                    $("#confirm .content").html("确定删除本行内容吗？")
                    var durationFact = $(this).parents('tr').find(".durationFact").html() || 0
                    var that = this


                    $("#confirm .confirmBtn").unbind().on("click", function () {
                        var id = info.id
                        var timeSpentTotal = 0
                        if (bounceName === 'workChange') {
                            timeSpentTotal = $("#workChange").find(".timeSpentTotal").html()
                        } else {
                            timeSpentTotal = $("#workInput").find(".timeSpentTotal").html()
                        }
                        timeSpentTotal = (timeSpentTotal - durationFact).toFixed(2)
                        if (id) {
                            $.ajax({
                                url: '../mywork/deleteDraftById.do',
                                data: { id: id, elapsedTime: timeSpentTotal },
                                success: function (res) {
                                    var success = res.success
                                    if (success === 1) {
                                        layer.msg("删除成功！")
                                        $(that).parents("tr").remove()
                                        bounce_Fixed2.cancel()
                                    } else {
                                        layer.msg("删除失败！")
                                    }
                                }
                            })
                        } else {
                            $(that).parents("tr").remove()
                            bounce_Fixed2.cancel()
                        }
                    })
                }
                break
        }

    })

    $(".autoFill").on("input focus keyup", function () {
        var max = $(this).attr("max")
        var curLength = $(this).val().length;
        if (curLength > max) {
            curLength = max
            var cutValue = $(this).val().substring(0,max);
            $(this).val(cutValue);
        }
        $(this).siblings(".textMax").text( curLength +'/' + max );
    })
    $("._hour, ._minute").on("input", function () {
        if ($(this).val() !== '' && $(this).siblings(".short").val() === '') {
            $(this).siblings(".short").val(0)
        }
    })
    $("._hour, ._minute").on("click", function () {
        if ($(this).val() === '0') {
            $(this).val("")
        }
    })
    $(".fulfillment,.routineFulfillment").on("change", function () {

        $(this).parents(".bonceCon").find($(".panel_"+$(this).val())).show().siblings('.kj-panel').hide()
        $(this).parents(".bonceCon").find($(".panel_"+$(this).val())).find("input:not(:radio),select,textarea").val("")
        $(this).parents(".bonceCon").find($(".panel_"+$(this).val())).find("input:radio").prop("checked", false)
        $(this).parents(".bonceCon").find($(".panel_"+$(this).val())).find(".extraInput").hide()
        $(this).parents(".bonceCon").find($(".panel_"+$(this).val())).find(".extraInput").find("input,select,textarea").prop("disabled", true)
        $(this).parents(".bonceCon").find($(".panel_"+$(this).val())).find(".repeatList").html("")
        countWords()
    })
    $("#editDailyWork [name='frequency']").on("change", function () {

        $(this).parents(".bonceCon").find($(".panel_"+$(this).val())).show().siblings('.kj-panel').hide()
        $(this).parents(".bonceCon").find($(".panel_"+$(this).val())).find("input,select,textarea").val("")
        countWords()
        $("#editDailyWork").find("[name='remindTimeStr']").val("08:00")
    })

    $(".halfTime").initHalfTime('00:00', 0)
    $("#editInnerWork").on("click", "input:radio", function (){
        updateInputDialog($(this))
    })
    jumpPage("main", function () {
        $(".backBtn").hide()
        initMonth()
    })
});
function updateInputDialog(selector) {
    var inputDialog = selector.parents(".chooseDo").next("div")
    inputDialog.find("input,select,textarea").val("")
    if (selector.prop("checked") && selector.val() == 0) {
        inputDialog.find("input,select,textarea").prop("disabled", false)
    } else {
        var info = $("#editInnerWork").data("radio")
        inputDialog.find("input,select,textarea").prop("disabled", true)
        inputDialog.find("input,select,textarea").each(function (){
            var name = $(this).attr("name")
            $(this).val(info[name])
        })

    }
    if (selector.parents(".chooseDo").find("input:radio:checked").length === 0) {
        inputDialog.hide()
    } else {
        inputDialog.show()
    }
    countWords()
}

// creator: 张旭博，2020-12-18 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 编辑日常工作
            case 'editDailyWork':
                var state = 0
                $("#editDailyWork").find("[require]:visible").each(function () {
                    if ($(this).val() === '') {
                        state++
                    }
                })
                if (state > 0) {
                    $("#sureEditDailyWorkBtn").prop("disabled", true)
                } else {
                    $("#sureEditDailyWorkBtn").prop("disabled", false)
                }
                break;
            // 录入当日的工作内容
            case 'workInput':
                var timeSpentTotal = 0.00
                $("#workInput .durationFact").each(function () {
                    timeSpentTotal += Number($(this).html())
                })
                $("#workInput tbody").each(function () {
                    if ($(this).find("tr").length === 0) {
                        $(this).parents("table").hide()
                    } else {
                        $(this).parents("table").show()
                    }
                })
                $("#workInput .timeSpentTotal").html(timeSpentTotal.toFixed(2))
                break;
            // 工作记录
            case 'workSee':
                $("#workSee tbody").each(function () {
                    if ($(this).find("tr").length === 0) {
                        $(this).parents("table").hide()
                    } else {
                        $(this).parents("table").show()
                    }
                })
                break;
            // 工作记录
            case 'workRecordSee':
                $("#workRecordSee tbody").each(function () {
                    if ($(this).find("tr").length === 0) {
                        $(this).parents("table").hide()
                    } else {
                        $(this).parents("table").show()
                    }
                })
                break;
            // 修改工作记录
            case 'workChange':
                var timeSpentTotal = Number($("#workChange").data('exceptOutterDuration'))
                $("#workChange .durationFact").each(function () {
                    timeSpentTotal += Number($(this).html())
                })
                $("#workChange .timeSpentTotal").html(timeSpentTotal.toFixed(2))
                break;
            case 'routineWorkInput':
                var state = 0
                $("#editRoutineWork [require]:visible").each(function () {
                    if ($(this).val() === '') {
                        state ++
                    }
                })
                if ($("#editRoutineWork [name='routineFulfillment']").val() === '0') {
                    state ++
                }
                if (state > 0) {
                    $("#sureEditRoutineWorkBtn").prop("disabled", true)
                } else {
                    $("#sureEditRoutineWorkBtn").prop("disabled", false)
                }
                break
            case 'innerWorkInput':
                var state = 0
                $("#editInnerWork [require]:visible").each(function () {
                    if ($(this).val() === '') {
                        state ++
                    }
                })
                $("#editInnerWork .chooseDo:visible").each(function () {
                    if (!$(this).find("input:radio:checked").val()) {
                        state ++
                    }
                })
                if ($("#editInnerWork [name='fulfillment']").val() === '0') {
                    state ++
                }
                if (state > 0) {
                    $("#sureEditInnerWorkBtn").prop("disabled", true)
                } else {
                    $("#sureEditInnerWorkBtn").prop("disabled", false)
                }
                break;
            case 'outterWorkInput':
                var state = 0
                $("#editOutterWork [require]").each(function () {
                    if ($(this).val() === '') {
                        state ++
                    }
                })
                if (state > 0) {
                    $("#sureEditOutterWorkBtn").prop("disabled", true)
                } else {
                    $("#sureEditOutterWorkBtn").prop("disabled", false)
                }
                break;
            case 'recentWorkInput':
                var state = 0
                $("#editRecentWork [require]").each(function () {
                    if ($(this).val() === '') {
                        state ++
                    }
                })
                if (state > 0) {
                    $("#sureEditRecentWorkBtn").prop("disabled", true)
                } else {
                    $("#sureEditRecentWorkBtn").prop("disabled", false)
                }
                break;

        }
    });
}

// creator: 张旭博，2020-12-18 10:13:22，初始化日表格
function initMonth(noloading) {
    var month = $(".tbl_month").data("month") || moment(hostTime).format("YYYY-MM")
    $(".tbl_month").data("month", month)
    $(".nowDayWeek").html(moment(hostTime).format("YYYY年MM月DD日 dddd"))
    $(".thisMonth").html(moment(month).format("YYYY年MM月"))
    // month = month || moment(hostTime).format("YYYY-MM")
    // $(".tbl_month").data("month", month)
    dateppicker.init($(".tbl_month"), month.substr(0,4), month.substr(5,2))
    getRedListByMonth(noloading)
    // getMonthCount()
    $.ajax({
        url: '../workAttendance/getAttendanceDays.do',
        data: {
            beginDate: month + '-01',
            onlyUser: sphdSocket.user.userID
        },
        success: function (res) {
            var data = res.data
            var userDaysDtos = data.userDaysDtos

            var str = ''
            if (userDaysDtos.length > 0) {
                for (var i in userDaysDtos) {
                    str +=  '<tr>' +
                        '   <td>'+userDaysDtos[i].workingDays+'天</td>' +
                        '   <td>'+userDaysDtos[i].outTimeDays+'天</td>' +
                        '   <td>'+userDaysDtos[i].submissionDays+'天</td>' +
                        '   <td>'+userDaysDtos[i].notSubmissionDays+'天</td>' +
                        '</tr>'
                }
            }
            $(".tbl_workCount tbody").html(str)
        }
    })
}

// creator: 张旭博，2020-11-08 19:11:43，录入今天的工作内容 - 按钮
function workInputBtn() {
    getWorkOfSomeday(moment(hostTime).format("YYYY-MM-DD"));
}

// creator: 张旭博，2021-11-22 15:25:23，日常工作设置
function dailyWorkBtn() {
    bounce_Fixed.show($("#dailyWorkSetting"))
    $("#dailyWorkSetting tbody").html("")
    // 获取当前工作设置
    getCurrentWorkSetting()
}

// creator: 张旭博，2021-11-24 11:46:54，获取当前工作设置
function getCurrentWorkSetting() {
    $.ajax({
        url: '../mywork/getDailyRoutineList.do',
        success: function (res) {
            console.log('res', res)
            let data = res.data
            var str = ''
            var weekString = '日一二三四五六'
            for(var i in data){
                str += '<tr>' +
                    '<td>'+data[i].content+'</td>' +
                    '<td>'+(data[i].frequency === '1'?'每日一次': '每周星期'+weekString.charAt(data[i].dayWeek)+'一次')+'</td>' +
                    '<td>'+(data[i].planTimeStr||'--')+'</td>' +
                    '<td>'+
                    '   <button class="ty-btn ty-btn-blue ty-circle-2" type="btn" onclick="editDailyWork($(this))">修改</button>'+
                    '</td>' +
                    '<td class="hd">'+JSON.stringify(data[i])+'</td>'+
                    '</tr>'
            }
            $("#dailyWorkSetting .table-dailyWork tbody").html(str)
        }
    })
}

// creator: 张旭博，2021-12-01 10:53:46，编辑日常工作
function editDailyWork(selector) {
    bounce_Fixed2.show($("#editDailyWork"))
    $("#editDailyWork").find("select,textarea,input").val("")
    $("#editDailyWork").find("[name='remindTimeStr']").val("08:00")
    $("#editDailyWork").find(".kj-panel").hide()
    $("#editDailyWork .bounce_title").html("新增日常工作")
    if (selector) {
        $("#editDailyWork .bounce_title").html("修改日常工作")
        var infoStr = selector.parents("tr").find(".hd").html()
        var info = JSON.parse(infoStr)
        $("#editDailyWork [name='frequency']").val(info.frequency).change()
        for (var key in info) {
            $("#editDailyWork [name='"+key+"']:visible").val(info[key])
        }
        $("#editDailyWork").data("id", info.id)
        var $that = selector
        $("#editDailyWork .sureBtn").unbind().on("click", function () {
            sureEditDailyWork($that)
        })
    } else {
        $("#editDailyWork .sureBtn").unbind().on("click", function () {
            sureEditDailyWork()
        })
    }
    setEveryTime(bounce_Fixed2, 'editDailyWork')
    countWords()
}

// creator: 张旭博，2021-12-01 11:05:46，编辑日常工作 - 确定
function sureEditDailyWork(selector) {
    var data = {}
    if (selector) {
        data.id = $("#editDailyWork").data("id")
        data.operation = 3 //操作:1-增,2-删,3-改,4-启停用
    } else {
        data.operation = 1 //操作:1-增,2-删,3-改,4-启停用
    }
    $("#form_editDailyWork").find("[name]:visible").each(function () {
        let name = $(this).attr("name")
        let value = $(this).val()
        data[name] = value
    })
    $.ajax({
        url: '../mywork/dailyRoutineOperation.do',
        data: data,
        success: function (res) {
            let success = res.success
            if (success === 1) {
                layer.msg("操作成功")
                bounce_Fixed2.cancel()
                getCurrentWorkSetting()
                initMonth()
            } else {
                layer.msg("新增失败")
            }
        }
    })
}

// creator: 张旭博，2021-01-15 08:17:44，工作记录弹窗 修改 - 按钮
function changeAttendanceBtn() {
    var date = $(".tbl_month").data("date")
    getWorkOfSomeday(date, true)
}

// creator: 张旭博，2020-12-18 15:04:12，其他日期计划内的工作(弹窗) - 确定
function sureChooseOtherInnerWork() {
    var otherList = []
    $("#chooseOtherInnerWork tbody input:checkbox:checked").each(function () {
        var info = $(this).parents("tr").find("td.hd").html()
        if (info) {info = JSON.parse(info)}
        otherList.push(info)
    })
    var str = ''
    if (otherList) {
        for (var i = 0;i < otherList.length; i++) {
            var durationPlan = -(-otherList[i].durationPlanHour - (otherList[i].durationPlanMinute/60).toFixed(2))
            str +=  '<tr class="add" data-id="'+otherList[i].id+'">' +
                '   <td>'+otherList[i].contentPlan+'</td>'+
                '   <td>'+moment(otherList[i].timePlan).format("YYYY-MM-DD HH:mm")+'</td>'+
                '   <td>'+durationPlan+'小时</td>'+
                '   <td>'+chargeFulfillment(otherList[i].fulfillment)+'</td>'+
                '   <td>'+
                '       <button class="ty-btn ty-btn-blue ty-circle-2" name="editInnerWork">编辑</button>'+
                '       <button class="ty-btn ty-btn-red ty-circle-2" name="deleteWork">删除</button>'+
                '   </td>'+
                '   <td class="hd">'+JSON.stringify(otherList[i])+'</td>'+
                '</tr>'
        }
    }
    $("#workInput .innerWork tbody").append(str)
    bounce_Fixed2.cancel()
}

// creator: 张旭博，2021-12-02 10:46:26，日常工作 - 确定
function sureEditRoutineWork(selector) {
    var workDate = $(".tbl_month").data("date")
    var orginData = JSON.parse(selector.find(".hd").html())
    var routineItem = orginData // 获取日常事务数据（包括计划内容，计划开始时间等内容）

    // 赋值一些传值需要的数据
    routineItem.id = orginData.id
    routineItem.type = 4
    routineItem.routineFulfillment = $("#editRoutineWork").find("[name='routineFulfillment']").val()

    if (routineItem.routineFulfillment === '0') {
        bounce_Fixed2.cancel()
        return false
    }

    var recordArray = []
    var countTime = 0
    if (routineItem.routineFulfillment === '1') {
        // 选择已完成时需要多传的数组
        $("#editRoutineWork .kj-panel:visible .repeatPart").each(function () {
            let recordItem = {}
            $(this).find('[name]').each(function () {
                var name = $(this).attr("name")
                recordItem[name] = $(this).val()
            })

            recordItem.durationFact = -(-recordItem.durationFactHour - (recordItem.durationFactMinute / 60).toFixed(2))
            recordItem.timeFact = workDate + ' ' + recordItem.timeFactTime + ':00'
            recordArray.push(recordItem)
            countTime += recordItem.durationFact
        })
    } else {
        routineItem.timeFact = routineItem.timePlan
        routineItem.timeFactTime = moment(routineItem.timeFact).format("HH:mm")
        routineItem.timeFactDate = moment(routineItem.timeFact).format("YYYY-MM-DD")
    }
    routineItem.elapsedTime = countTime
    routineItem.recordArray = recordArray

    if (!validateTimeInput($("#editRoutineWork"))){return false}
    if (!$("#workChange").is(":visible")) {
        saveDraft(routineItem, selector)
    } else {
        var str = ''
        if (routineItem.routineFulfillment === '1') {
            for (let i = 0; i<routineItem.recordArray.length; i++) {
                var recordItem = routineItem.recordArray[i]
                var newItem = {}
                Object.assign(newItem, routineItem, recordItem)
                var content = recordItem.content?(routineItem.contentPlan + '/<span class="ty-color-orange">'+recordItem.content):routineItem.contentPlan + '</span>'
                str +=  '<tr data-id="'+routineItem.previousRecord+'">' +
                    '    <td>' + content+'</td>' +
                    '    <td>' + selector.find("td").eq(1).html() + '</td>' +
                    '    <td>' + chargeRoutineFulfillment(newItem) + '</td>' +
                    '    <td>' + selector.find("td").eq(3).html() +'</td>'+
                    '    <td class="hd">' + JSON.stringify(newItem) +'</td>'+
                    '</tr>'
            }
        } else {
            // 当日未做
            str +=  '<tr data-id="'+routineItem.previousRecord+'">' +
                '    <td>' + routineItem.contentPlan+'</td>' +
                '    <td>' + selector.find("td").eq(1).html() + '</td>' +
                '    <td>' + chargeRoutineFulfillment(routineItem) + '</td>' +
                '    <td>' + selector.find("td").eq(3).html() +'</td>'+
                '    <td class="hd">' + JSON.stringify(routineItem) +'</td>'+
                '</tr>'
        }
        selector.replaceWith(str)
    }


    bounce_Fixed2.cancel()
    console.log(routineItem)
}

// creator: 张旭博，2020-12-18 11:14:51，当日所做的计划内工作 - 确定
function sureEditInnerWork(selector) {
    var innerJSON = JSON.parse(selector.find("td.hd").html())
    var thisDate = $(".tbl_month").data("date")
    var innerItem = innerJSON
    // 获取录入的数据
    $("#editInnerWork [name]:not(:radio):visible").each(function () {
        var name = $(this).attr("name")
        innerItem[name] = $(this).val()
    })
    // 录入的数据根据完成情况的不同需要进行数据处理
    if (innerItem.fulfillment < 4) {
        // fulfillment:1 当天完成了工作 2 做了一部分剩余改天再做 3做了一部分剩余不做
        var recordArray = []
        var countTime = 0
        // 获取所有分段时间数据
        $("#editInnerWork .kj-panel:visible .repeatPart").each(function () {
            let recordItem = {}
            $(this).find('[name]:not(:radio)').each(function () {
                var name = $(this).attr("name")
                recordItem[name] = $(this).val()
            })
            recordItem.content = recordItem.content || innerItem.contentPlan
            recordItem.durationFact = -(-recordItem.durationFactHour - (recordItem.durationFactMinute / 60).toFixed(2))
            if (recordItem.durationFact === 0) {
                recordItem.durationFact = innerItem.durationPlan
                recordItem.durationFactHour = innerItem.durationPlanHour
                recordItem.durationFactMinute = innerItem.durationPlanMinute
            }
            recordItem.timeFactTime = recordItem.timeFactTime || innerItem.timePlanTime
            recordItem.timeFact = moment(thisDate).format("YYYY-MM-DD") + ' ' + recordItem.timeFactTime + ':00'
            recordArray.push(recordItem)
            countTime += recordItem.durationFact
        })
        innerItem.elapsedTime = countTime
        innerItem.recordArray = recordArray
        if (innerItem.fulfillment === '2') {
            // 最复杂的一个，需要同时处理实际工作和增加未来计划（此条不同时间段共享未来计划）
            innerItem.timeContinue = innerItem.timeContinueDate + ' ' + innerItem.timeContinueTime + ':00'
            innerItem.continuePlan = -(-innerItem.continuePlanHour - (innerItem.continuePlanMinute/60).toFixed(2))
        }
    } else if (innerItem.fulfillment === '4') {
        // fulfillment:4 工作没做改天再做
        innerItem.timeContinue = innerItem.timeContinueDate + ' ' + innerItem.timeContinueTime + ':00'
        innerItem.timePlan = innerItem.timePlanDate + ' ' + innerItem.timePlanTime + ':00'
        innerItem.timeFact = moment(thisDate).format("YYYY-MM-DD HH:mm:ss")
    } else {
        innerItem.timeFact = moment(thisDate).format("YYYY-MM-DD HH:mm:ss")
    }
    if (!validateTimeInput($("#editInnerWork"))){return false}
    // 后台接口保存， 前端表格赋值
    saveDraft(innerItem, selector)

    bounce_Fixed2.cancel()
    console.log(innerItem)
}

// creator: 张旭博，2020-12-18 11:14:51，当日所做的计划外工作 - 确定
function sureEditOutterWork(selector) {
    var workDate = $(".tbl_month").data("date")
    if (!validateTimeInput($("#editOutterWork"))){return false}
    if (selector.length > 0) {
        var outterJSON = JSON.parse(selector.find("td.hd").html())
        var outterItem = outterJSON
    } else {
        var outterItem = {
            type: 2,
            id: null
        }
    }

    $("#editOutterWork [name]:visible").each(function () {
        var name = $(this).attr("name")
        outterItem[name] = $(this).val()
    })
    var recordArray = []
    var countTime = 0
    $("#editOutterWork .repeatPart").each(function () {
        let recordItem = {}
        $(this).find('[name]').each(function () {
            var name = $(this).attr("name")
            recordItem[name] = $(this).val()
        })

        recordItem.content = outterItem.content
        recordItem.durationFact = -(-recordItem.durationFactHour - (recordItem.durationFactMinute / 60).toFixed(2))
        recordItem.timeFact = workDate + ' ' + recordItem.timeFactTime + ':00'
        recordArray.push(recordItem)
        countTime += recordItem.durationFact
    })
    outterItem.elapsedTime = countTime
    outterItem.recordArray = recordArray

    if (!$("#workChange").is(":visible")) {
        saveDraft(outterItem, selector)
    } else {
        var str = ''
        for (let i = 0; i<outterItem.recordArray.length; i++) {
            let recordItem = outterItem.recordArray[i]
            var newItem = {}
            Object.assign(newItem, outterItem, recordItem)
            str +=   '<tr>' +
                '    <td>' + recordItem.content+'</span></td>' +
                '    <td>' + workDate + '</td>' +
                '    <td>' + recordItem.timeFactTime + '</td>' +
                '    <td><span class="durationFact">' + recordItem.durationFact + '</span>小时</td>'+
                '    <td>' +
                '        <button class="ty-btn ty-btn-blue ty-circle-2" type="change" name="editOutterWork">编辑</button>' +
                '        <button class="ty-btn ty-btn-red ty-circle-2" name="deleteWork">删除</button>' +
                '    </td>' +
                '    <td class="hd">' + JSON.stringify(newItem) +'</td>' +
                '</tr>'

        }
        if (selector.length > 0) {
            selector.replaceWith(str)
        } else {
            $("#workChange .outterWork tbody").append(str)
        }
    }

    bounce_Fixed2.cancel()
    console.log(outterItem)
}

// creator: 张旭博，2020-12-18 15:04:12，近日的工作计划(弹窗) - 确定
function sureEditRecentWork(selector) {
    if (!validateTimeInput($("#editRecentWork"))){return false}
    var recentItem = {
        type: 3
    }
    $("#editRecentWork [name]").each(function () {
        var name = $(this).attr("name")
        recentItem[name] = $(this).val()
    })
    if (selector) {
        recentItem.id = selector.data("id")
    } else {
        recentItem.id = null
    }
    recentItem.timePlan = recentItem.timePlanDate + ' '+recentItem.timePlanTime + ':00'
    recentItem.durationPlan = -(-recentItem.durationPlanHour - (recentItem.durationPlanMinute/60).toFixed(2))

    saveDraft(recentItem, selector)
    bounce_Fixed2.cancel()
    console.log(recentItem)
}

// creator: 张旭博，2021-12-02 11:40:04，编辑日常工作、计划内、计划外、今日计划，都会自动生成草稿(通用）
function saveDraft(item, selector) {
    var workDate = $(".tbl_month").data("date")
    item.date = workDate
    item.week = new Date(workDate).getDay() //星期几编号0-6：周日为0，周六为6
    $.ajax({
        url: '/mywork/saveDraft.do',
        data: {recordJson : JSON.stringify(item)},
        success: function (res) {
            var success = res.success
            var data = res.data
            var recordIds = data.recordIds
            $(".tbl_month").data("workId", data.workId)

            if (success === 1){
                layer.msg("保存成功")
                initMonth()
                if (item.type === 1) {
                    // 计划内的工作
                    var str = ''
                    if (item.fulfillment< 4) {
                        for (let i = 0; i<item.recordArray.length; i++) {
                            var recordItem = item.recordArray[i]
                            var newItem = {}
                            Object.assign(newItem, item, recordItem)
                            newItem.id = recordIds[i]
                            newItem.groupId = newItem.groupId || recordIds[0]
                            var content = newItem.content?(newItem.contentPlan + '/<span class="ty-color-orange">'+newItem.content):newItem.contentPlan
                            str +=   '<tr class="'+selector.attr("class")+'" data-id="'+newItem.previousRecord+'" data-common="'+newItem.groupId+'">' +
                                '    <td>' + content+'</td>' +
                                '    <td>' + selector.find("td").eq(1).html() + '</td>' +
                                '    <td>' + selector.find("td").eq(2).html() + '</td>' +
                                '    <td>' + chargeFulfillment(newItem) + '</td>' +
                                '    <td>' + selector.find("td").eq(4).html() + '</td>' +
                                '    <td class="hd">' + JSON.stringify(newItem) +'</td>' +
                                '</tr>'
                        }
                    } else {
                        item.id = recordIds[0]
                        str +=   '<tr class="'+selector.attr("class")+'" data-id="'+item.previousRecord+'">' +
                            '    <td>' + selector.find("td").eq(0).html()+'</td>' +
                            '    <td>' + selector.find("td").eq(1).html() + '</td>' +
                            '    <td>' + selector.find("td").eq(2).html() + '</td>' +
                            '    <td>' + chargeFulfillment(item) + '</td>' +
                            '    <td>' + selector.find("td").eq(4).html() + '</td>' +
                            '    <td class="hd">' + JSON.stringify(item) +'</td>' +
                            '</tr>'
                    }
                    if (selector) {
                        if (item.fulfillment< 4) {
                            var homologous = selector.siblings("tr[data-common='"+newItem.groupId+"']").find(".timeContinue")
                            homologous.each(function () {
                                var jsonStr = $(this).parents("tr").find(".hd").html()
                                var json = JSON.parse(jsonStr)
                                json.timeContinue = item.timeContinue
                                json.timeContinueDate = item.timeContinueDate
                                json.timeContinueTime = item.timeContinueTime
                                json.continuePlan = item.continuePlan
                                json.continuePlanMinute = item.continuePlanMinute
                                json.continuePlanHour = item.continuePlanHour
                                $(this).html(json.timeContinueDate + ' ' + json.timeContinueTime)
                                $(this).parents("tr").find(".hd").html(JSON.stringify(json))
                            })
                        }
                        selector.replaceWith(str)
                    } else {
                        $("#workInput .innerWork tbody").append(str)
                    }


                } else if (item.type === 2) {
                    // 计划外的工作
                    var str = ''
                    for (let i = 0; i<item.recordArray.length; i++) {
                        let recordItem = item.recordArray[i]
                        var newItem = {}
                        Object.assign(newItem, item, recordItem)
                        newItem.id = recordIds[i]
                        str +=   '<tr>' +
                            '    <td>' + recordItem.content+'</span></td>' +
                            '    <td>' + workDate + '</td>' +
                            '    <td>' + recordItem.timeFactTime + '</td>' +
                            '    <td><span class="durationFact">' + recordItem.durationFact + '</span>小时</td>'+
                            '    <td>' +
                            '        <button class="ty-btn ty-btn-blue ty-circle-2" type="change" name="editOutterWork">编辑</button>' +
                            '        <button class="ty-btn ty-btn-red ty-circle-2" name="deleteWork">删除</button>' +
                            '    </td>' +
                            '    <td class="hd">' + JSON.stringify(newItem) +'</td>' +
                            '</tr>'

                    }
                    if (selector.length > 0) {
                        selector.replaceWith(str)
                    } else {
                        $("#workInput .outterWork tbody").append(str)
                    }

                } else if (item.type === 3) {
                    // 今日计划
                    item.id = recordIds[0]
                    var str =   '<tr data-id="'+recordIds[0]+'">' +
                        '    <td>' + item.contentPlan + '</td>' +
                        '    <td>' + moment(item.timePlan).format("YYYY-MM-DD HH:mm") + '</td>' +
                        '    <td>' + item.durationPlan + '小时</td>' +
                        '    <td>' +
                        '        <button class="ty-btn ty-btn-blue ty-circle-2" type="change" name="editRecentWork">编辑</button>' +
                        '        <button class="ty-btn ty-btn-red ty-circle-2" name="deleteWork">删除</button>' +
                        '    </td>' +
                        '    <td class="hd">'+JSON.stringify(item)+'</td>' +
                        '</tr>'
                    if (selector) {
                        selector.replaceWith(str)
                    } else {
                        $("#workInput .recentWork tbody").append(str)
                    }
                } else {
                    var str = ''
                    if (item.routineFulfillment < 2) {
                        for (let i = 0; i<item.recordArray.length; i++) {
                            var recordItem = item.recordArray[i]
                            var newItem = {}
                            Object.assign(newItem, item, recordItem)
                            newItem.id = recordIds[i]
                            var content = recordItem.content?(item.contentPlan + '/<span class="ty-color-orange">'+recordItem.content):item.contentPlan + '</span>'
                            str +=  '<tr data-id="'+item.previousRecord+'">' +
                                    '    <td>' + content+'</td>' +
                                    '    <td>' + selector.find("td").eq(1).html() + '</td>' +
                                    '    <td>' + chargeRoutineFulfillment(newItem) + '</td>' +
                                    '    <td>' + selector.find("td").eq(3).html() +'</td>'+
                                    '    <td class="hd">' + JSON.stringify(newItem) +'</td>'+
                                    '</tr>'
                        }
                    } else {
                        item.id = recordIds[0]
                        item.timeFact = item.timePlan
                        // 当日未做
                        str +=  '<tr data-id="'+item.previousRecord+'">' +
                                '    <td>' + item.contentPlan+'</td>' +
                                '    <td>' + selector.find("td").eq(1).html() + '</td>' +
                                '    <td>' + chargeRoutineFulfillment(item) + '</td>' +
                                '    <td>' + selector.find("td").eq(3).html() +'</td>'+
                                '    <td class="hd">' + JSON.stringify(item) +'</td>'+
                                '</tr>'
                    }
                    selector.replaceWith(str)
                }
            } else {
                layer.msg("保存失败")
            }
        }
    })
}

// creator: 张旭博，2021-12-09 10:53:42，增加实际工作的重复内容
function addFactWorkForm(selector) {
    var cloneNode = selector.parents(".repeatPart").clone(true)
    var random = Math.round(Math.random()*10000)
    cloneNode.find(".controlBtn").replaceWith('<span class="ty-btn ty-btn-red ty-circle-2 controlBtn" onclick="deleteFactWorkForm($(this))">删除</span>')
    cloneNode.find("input:not(:radio)").val("")
    cloneNode.find(".extraInput").find(".extraInput").hide()
    cloneNode.find(".extraInput").find("input,select,textarea").prop("disabled", true)
    cloneNode.find("input:radio").each(function (){
        $(this).prop("checked", false)
        $(this).attr("id", $(this).attr("id") + random)
        $(this).attr("name", $(this).attr("name") + random)
        $(this).next("label").attr("for", $(this).next("label").attr("for") + random)
    })
    cloneNode.find("select").val("")
    cloneNode.find("textarea").val("")
    selector.parents(".repeatPart").siblings(".repeatList").append(cloneNode)
}

// creator: 张旭博，2021-12-09 10:53:42，删除实际工作的重复内容
function deleteFactWorkForm(selector) {
    selector.parents(".repeatPart").remove()
}

// creator: 张旭博，2021-01-06 13:46:13，新增某天的工作记录 - 确认弹窗
function saveWorkConfirm(state) {
    var date = $(".tbl_month").data("date")
    var diff = moment(hostTime).diff(moment(date), 'days')
    if (diff > 40) {
        layer.msg("操作失败！公司规定40天前的工作日记不再允许修改。")
        return false
    }
    var contentHtml = ''
    var noEditData = 0
    $("#workInput .innerWork tbody tr").each(function () {
        if ($.trim($(this).find("td").eq(3).text()) === '尚未编辑') {
            noEditData++
        }
    })
    if (noEditData > 0) {
        bounce_Fixed2.show($("#tipError"))
        $("#tipError .content").html("操作失败！<br>还有工作的结果尚未编辑。")
        return false
    }
    contentHtml = '正式提交后，与工作计划有关的内容将无法再更改。<br/>确定正式提交吗？'

    var that = this
    $("#confirm .confirmBtn").unbind().on("click", function () {
        bounce_Fixed2.cancel()
        saveWork(state)
    })
    $("#confirm .content").html(contentHtml)
    bounce_Fixed2.show($("#confirm"))
}

// creator: 张旭博，2021-01-06 13:46:13，修改某天的工作记录 - 确认弹窗
function changeWorkConfirm(state) {
    var contentHtml = ''
    if(state === 0) {
        // 保存草稿
        contentHtml = "取消后，您刚编辑的内容将丢失。"
        $("#confirm .confirmBtn").unbind().on("click", function () {
            bounce_Fixed.cancel()
            bounce_Fixed2.cancel()
        })
    } else {
        contentHtml = '确定后，当日的数据将保存为您刚刚编辑的内容。<br/>确定保存吗？'
        $("#confirm .confirmBtn").unbind().on("click", function () {
            bounce_Fixed2.cancel()
            changeWork()
        })
    }
    $("#confirm .content").html(contentHtml)
    bounce_Fixed2.show($("#confirm"))
}

// creator: 张旭博，2020-12-18 14:35:52，新增某天的工作记录
function saveWork(state) {
    var workDate = $(".tbl_month").data("date")
    var workId = $(".tbl_month").data("workId")
    if (!workId) {
        layer.msg("请至少录入或编辑一条数据！")
        return false
    }
    var data = {
        workId: workId
    }
    console.log(data)
    $.ajax({
        url: '../mywork/saveWork.do',
        data: data,
        success: function (res) {
            var success = res.success
            if (success === 1) {
                layer.msg("操作成功！")
                initMonth()
                bounce.cancel()
            } else {
                layer.msg("操作失败！")
            }
        }
    })
}

// creator: 张旭博，2021-01-15 09:30:35，修改某天的工作记录（只能修改正式提交的）
function changeWork() {
    var workDate = $(".tbl_month").data("date")
    var versionNo = $(".tbl_month").data("versionNo")
    var durationTotal = $("#workChange .timeSpentTotal").text()
    // 日常工作
    var routineList = []
    $("#workChange .routineWork tbody tr").each(function () {
        var outterItem = {}
        var data = $(this).find("td.hd").html()
        if (data) { outterItem = JSON.parse(data) }
        routineList.push(outterItem)
    })
    // 当日所做的计划外工作
    var outterList = []
    $("#workChange .outterWork tbody tr").each(function () {
        var outterItem = {}
        var data = $(this).find("td.hd").html()
        if (data) { outterItem = JSON.parse(data) }
        outterList.push(outterItem)
    })
    // 当日所做的计划外工作
    var data = {
        workId: $(".tbl_month").data("workId"),
        creator: sphdSocket.user.userID, //当前登录人ID
        org: sphdSocket.user.oid,
        workDay: new Date(workDate).getDay(), //星期几编号0-6：周日为0，周六为6
        userId: sphdSocket.user.userID,
        workDate: workDate,       //工作记录提交的日期
        outPlanCount: outterList.length, //计划外工作数量
        state: 2, //状态:1-草稿,2-正式提交
        createName: sphdSocket.user.userName,    //创建人姓名
        updateName: sphdSocket.user.userName,    //更新人姓名
        elapsedTime: durationTotal, //实际总耗时（=当天所有实际耗时之和）
        versionNo: versionNo, //版本号：首次传0，后每修改一次+1
        outterList: outterList,
        routineList: routineList
    }
    console.log(data)
    $.ajax({
        url: '../mywork/modifyWork.do',
        data: {
            workJson : JSON.stringify(data)
        },
        success: function (res) {
            var success = res.success
            if (success === 1) {
                layer.msg("操作成功！")
                bounce_Fixed.cancel()
                getWorkOfSomeday(workDate)
            } else {
                layer.msg("操作失败！")
            }
        }
    })
}

// creator: 张旭博，2021-01-29 10:13:07，初始化时间
function initLayerTime(date) {
    // 初始化时间
    var date1 = moment(date).add(1, 'd').format("YYYY-MM-DD")
    var data1Json = {
        date: new Date(date1).getDate(),
        hours: 0,
        minutes: 0,
        month: new Date(date1).getMonth(),
        seconds: 0,
        year: new Date(date1).getFullYear()
    }

    recent_timePlanDate.config.min = data1Json
    inner2_timeContinueDate.config.min = data1Json
    inner4_timeContinueDate.config.min = data1Json
}

// creator: 张旭博，2020-12-18 10:19:00，获取某天的工作记录详情
function getWorkOfSomeday(date, workChange) {
    var isToday = isTodayOrFuture(date) // -1: 过去 0: 现在 1: 未来
    $(".tbl_month").data("date", date)
    $(".currentDayWeek").html(moment(date).format("YYYY年MM月DD日 dddd"))
    var pm_workOfSomeDay = new Promise((resolve, reject) => {
        $.ajax({
            url: '../mywork/getWorkOfSomeday.do',
            data: {
                org: sphdSocket.user.oid,
                userId: sphdSocket.user.userID,
                date: date
            },
            success: function (res) {
                var success = res.success
                if (success === 1) {
                    resolve(res.data)
                } else {
                    reject()
                }
            }
        })
    })
    pm_workOfSomeDay.then(data => {
        $(".tbl_month").data("workId", data.workId || 0)
        $(".tbl_month").data("versionNo", data.versionNo)

        var state = Number(data.state) // 2-保存草稿 3-正式提交

        // -----------------初始化弹窗------------------
        var showDialog;
        if (isToday === 1) {
            showDialog = 'workFutureSee'
            bounce_Fixed2.show($("#workFutureSee"))
            setEveryTime(bounce_Fixed2, 'workFutureSee')
        } else {
            // 今天和过去
            // 如果还未正式提交
            if (state < 2) {
                showDialog = 'workInput'
                // 显示录入弹窗
                bounce.show($("#workInput"))
                setEveryTime(bounce, 'workInput')
                // 初始化输入框限制
                initLayerTime(date)
                // 初始化编辑计划外计划时默认的一个字段
                $("#editOutterWork .thisDayWeek").html(moment(date).format("YYYY年MM月DD日 dddd"))
                // 显示公共内容
                $("#workInput .ty-panel:not(.pastTip)").show()
                $("#workInput .timeSpentTotalVisible").show()
                $("#workInput .lastOperateTimeVisible").show()
                $(".pastTip").children().hide()
                if (isToday === -1) {
                    // 过去的特殊处理

                    // 1.如果之前正式提交近日的工作计划，那么以后都不会显示此部分内容
                    if (data.recentList.length === 0) {
                        $("#workInput .recentWork").hide()
                    }

                    // 2.过去未正式提交的提示（未录入显示提示0，草稿显示提示1）
                    $(".pastTip .tip0").show();
                    if (state === 1) {
                        $(".pastTip .tip2").show()
                    } else {
                        $(".pastTip .tip1").show()
                    }
                } else if(isToday === 0){
                    if (state === 1) {
                        $(".pastTip .tip2").show()
                    }
                }
            } else {
                // 已经正式提交 （显示查看弹窗）
                if (workChange) {
                    showDialog = 'workChange'
                    // 点击了修改按钮
                    bounce_Fixed.show($("#workChange"))
                    setEveryTime(bounce_Fixed, 'workChange')
                    var exceptOutterDuration = 0.00
                    $("#workSee .innerWork .durationFact").each(function () {
                        exceptOutterDuration += Number($(this).html())
                    })
                    $("#workChange ").data('exceptOutterDuration', exceptOutterDuration.toFixed(2))
                } else {
                    showDialog = 'workSee'
                    bounce.show($("#workSee"))
                    setEveryTime(bounce, 'workSee')
                    // 1.如果之前正式提交近日的工作计划，那么以后都不会显示此部分内容
                    if (data.recentList.length === 0) {
                        $("#workInput .recentWork").hide()
                    }
                }
            }
        }
        // -----------------处理表头------------------ （申请 查看 修改 公用）
        var lastOperateTime = data.lastOperateTime ? moment(data.lastOperateTime).format("YYYY-MM-DD HH:mm:ss"): '暂无' // 最后的操作时间
        var leaveData = data.leaveData // 请假信息
        var isWorkDay = data.isWorkDay // true 正常上班 false 休息
        var hasExtraWork = data.hasExtraWork // true 有加班 false 无加班
        var timeSpentTotal = data.timeSpentTotal // 当日已录入内容合计耗时
        var commentList = data.commentList // 评论

        // 赋值当日应该有工作记录的原因
        $("#" + showDialog).find(".workReason").html(chargeWorkReason(isWorkDay, hasExtraWork))
        // 处理请假
        handleLeaveDetail(leaveData, showDialog)
        // 处理评论
        handleComment(commentList, showDialog)
        // 赋值当日已录入内容合计耗时
        $("#" + showDialog).find(".timeSpentTotal").html(timeSpentTotal)
        // 赋值最后的操作时间
        $("#" + showDialog).find(".lastOperateTime").html(lastOperateTime)

        if (state > 1) {
            $(".lastOperateTimeVisible .workRecordBtn").show()
        } else {
            $(".lastOperateTimeVisible .workRecordBtn").hide()
        }

        // -----------------处理内容------------------

        var routineList = data.routineList // 日常工作内容
        var otherList = data.otherList
        var innerList = data.innerList

        // 获取日常工作
        var routineStr = ''
        for (var m = 0; m < routineList.length; m++) {
            var fulfillment = Number(routineList[m].routineFulfillment)

            var routineItem = {
                id: routineList[m].id,
                routineId: routineList[m].routineId,
                contentPlan: routineList[m].contentPlan,
                routineFulfillment: routineList[m].routineFulfillment || 0,
                timePlan: routineList[m].timePlan?(date + ' ' + moment(routineList[m].timePlan).format("HH:mm:ss")):'',
                timePlanDate: date,
                timePlanTime: routineList[m].timePlan?moment(routineList[m].timePlan).format("HH:mm"):'',

            }
            if (fulfillment === 1) {
                routineItem.content = routineList[m].content
                routineItem.timeFact = moment(routineList[m].timeFact).format("YYYY-MM-DD HH:mm:ss")
                routineItem.timeFactDate = moment(routineList[m].timeFact).format("YYYY-MM-DD")
                routineItem.timeFactTime = moment(routineList[m].timeFact).format("HH:mm")
                routineItem.durationFactHour = routineList[m].durationFactHour
                routineItem.durationFactMinute = routineList[m].durationFactMinute
                routineItem.durationFact = -(-routineList[m].durationFactHour - (routineList[m].durationFactMinute/60).toFixed(2))  //实际耗时（小时，保存两位）
            } else {
                routineItem.content = ''
                routineItem.timeFact = moment(routineList[m].contentPlan).format("YYYY-MM-DD HH:mm:ss") // 这个是假的，后台非得让传的
                routineItem.timeFactDate = moment(routineItem.timeFact).format("YYYY-MM-DD") // 这个是假的，后台非得让传的
                routineItem.timeFactTime = moment(routineItem.timeFact).format("HH:mm") // 这个是假的，后台非得让传的
                routineItem.durationFactHour = ''
                routineItem.durationFactMinute = ''
                routineItem.durationFact = ''
            }
            var handleStr = '   <td class="handle">'+
                '      <button class="ty-btn ty-btn-blue ty-circle-2" name="editRoutineWork">编辑</button>'+
                '   </td>';
            if (isToday === 1) {
                routineStr +=     '<tr>' +
                    '   <td>'+(routineItem.content?routineItem.contentPlan + '/<span class="ty-color-orange">'+routineItem.content+'</span>':routineItem.contentPlan)+'</td>'+
                    '   <td>'+routineItem.timePlanTime+'</td>'+
                    '</tr>'
            } else {
                routineStr +=     '<tr>' +
                    '   <td>'+(routineItem.content?routineItem.contentPlan + '/<span class="ty-color-orange">'+routineItem.content+'</span>':routineItem.contentPlan)+'</td>'+
                    '   <td>'+(routineItem.timePlanTime || '--')+'</td>'+
                    '   <td>'+chargeRoutineFulfillment(routineItem)+'</td>'+
                    handleStr +
                    '   <td class="hd">'+JSON.stringify(routineItem)+'</td>'+
                    '</tr>'
            }

        }

        $("#" + showDialog).find(".routineWork tbody").html(routineStr)


        // 获取当日所作的计划内工作
        $("#" + showDialog).find(".innerWork").data("otherList", otherList)
        var innerStr = ''
        for (var l = 0; l < innerList.length; l++) {
            var fulfillment = Number(innerList[l].fulfillment)
            if (fulfillment === 1) {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    content: innerList[l].content,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment,
                    timeFact: moment(innerList[l].timeFact).format("YYYY-MM-DD HH:mm:ss"),  //实际开始时间
                    timeFactDate: moment(innerList[l].timeFact).format("YYYY-MM-DD"),  //实际开始时间
                    timeFactTime: moment(innerList[l].timeFact).format("HH:mm"),  //实际开始时间
                    durationFact: -(-innerList[l].durationFactHour - (innerList[l].durationFactMinute/60).toFixed(2)),  //实际耗时（小时，保存两位）
                    durationFactMinute: innerList[l].durationFactMinute,                                                //实际耗时（分钟）
                    durationFactHour: innerList[l].durationFactHour,                                                    //实际耗时（小时）
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour                                                    //计划耗时（小时）
                }
            } else if (fulfillment === 2) {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    content: innerList[l].content,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment,
                    timeFact: moment(innerList[l].timeFact).format("YYYY-MM-DD HH:mm:ss"),  //实际开始时间
                    timeFactDate: moment(innerList[l].timeFact).format("YYYY-MM-DD"),  //实际开始时间
                    timeFactTime: moment(innerList[l].timeFact).format("HH:mm"),  //实际开始时间
                    durationFact: -(-innerList[l].durationFactHour - (innerList[l].durationFactMinute/60).toFixed(2)),  //实际耗时（小时，保存两位）
                    durationFactMinute: innerList[l].durationFactMinute,                                                //实际耗时（分钟）
                    durationFactHour: innerList[l].durationFactHour,                                                    //实际耗时（小时）
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                    timeContinue: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD HH:mm:ss"):''),  //剩余部分计划开始时间
                    timeContinueDate: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD"):''),  //剩余部分计划开始日期
                    timeContinueTime: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("HH:mm"):''), //剩余部分计划开始分秒
                    continuePlan: -(-innerList[l].continuePlanHour - (innerList[l].continuePlanMinute/60).toFixed(2)),  //剩余部分计划耗时（小时，保存两位）
                    continuePlanMinute: (innerList[l].continuePlanMinute || 0),                                        //剩余部分计划耗时（分钟）
                    continuePlanHour: (innerList[l].continuePlanHour || 0),                                            //剩余部分计划耗时（小时）
                    reason: innerList[l].reason //关闭原因
                }
            } else if (fulfillment === 3) {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    content: innerList[l].content,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment,
                    timeFact: moment(innerList[l].timeFact).format("YYYY-MM-DD HH:mm:ss"),  //实际开始时间
                    timeFactDate: moment(innerList[l].timeFact).format("YYYY-MM-DD"),  //实际开始时间
                    timeFactTime: moment(innerList[l].timeFact).format("HH:mm"),  //实际开始时间
                    durationFact: -(-innerList[l].durationFactHour - (innerList[l].durationFactMinute/60).toFixed(2)),  //实际耗时（小时，保存两位）
                    durationFactMinute: innerList[l].durationFactMinute,                                                //实际耗时（分钟）
                    durationFactHour: innerList[l].durationFactHour,                                                    //实际耗时（小时）
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                    reason: innerList[l].reason //关闭原因
                }
            } else if (fulfillment === 4) {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment,
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour,
                    timeContinue: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD HH:mm:ss"):''),  //剩余部分计划开始时间
                    timeContinueDate: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD"):''),  //剩余部分计划开始日期
                    timeContinueTime: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("HH:mm"):''), //剩余部分计划开始分秒//计划耗时（小时）
                    reason: innerList[l].reason //关闭原因
                }
            } else {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment || 0,
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                    reason: innerList[l].reason //关闭原因
                }
            }
            innerItem.id = innerList[l].id
            innerItem.groupId = innerList[l].groupId
            if (innerList[l].successionId) {
                innerItem.successionId = innerList[l].successionId
            }
            var previousId = innerList[l].previousRecord
            var groupId = innerList[l].groupId
            var addClass = ''
            var handleStr = ''
            if (previousId && innerItem.timePlanDate !== date) {
                handleStr = '   <td class="handle">'+
                    '      <button class="ty-btn ty-btn-blue ty-circle-2" name="editInnerWork">编辑</button>'+
                    '      <button class="ty-btn ty-btn-red ty-circle-2" name="deleteWork">删除</button>'+
                    '   </td>';
                addClass = 'add'
                innerItem.previousRecord = previousId

            } else {
                handleStr = '   <td class="handle">'+
                    '      <button class="ty-btn ty-btn-blue ty-circle-2" name="editInnerWork">编辑</button>'+
                    '   </td>';
                innerItem.previousRecord = 0
            }
            if (isToday === 1) {
                innerStr +=     '<tr>' +
                    '   <td>'+innerItem.contentPlan+'</td>'+
                    '   <td>'+moment(innerItem.timePlan).format("YYYY-MM-DD HH:mm")+'</td>'+
                    '   <td>'+innerItem.durationPlanHour+'小时</td>'+
                    '</tr>'
            } else {
                var content = chargeEqual(innerItem.contentPlan, innerItem.content)?innerItem.contentPlan:(innerItem.content?(innerItem.contentPlan + '/<span class="ty-color-orange">'+innerItem.content+'</span>'):innerItem.contentPlan)
                innerStr +=     '<tr class="'+addClass+'" data-id="'+previousId+'" data-common="'+groupId+'">' +
                    '   <td>'+content+'</td>'+
                    '   <td>'+moment(innerItem.timePlan).format("YYYY-MM-DD HH:mm")+'</td>'+
                    '   <td>'+innerItem.durationPlan+'小时</td>'+
                    '   <td>'+chargeFulfillment(innerItem)+'</td>'+
                    handleStr +
                    '   <td class="hd">'+JSON.stringify(innerItem)+'</td>'+
                    '</tr>'
            }
        }
        $("#" + showDialog).find(".innerWork tbody").html(innerStr)

        // 获取当日所做的计划外工作
        var outterList = data.outterList
        var outterStr = ''
        for (var j = 0; j < outterList.length; j++) {
            var outterItem = {
                type: 2,
                id: outterList[j].id,
                content: outterList[j].content,
                timeFact: moment(outterList[j].timeFact).format("YYYY-MM-DD HH:mm:ss"),
                timeFactDate: moment(outterList[j].timeFact).format("YYYY-MM-DD"),
                timeFactTime: moment(outterList[j].timeFact).format("HH:mm"),
                durationFactHour: outterList[j].durationFactHour,
                durationFactMinute: outterList[j].durationFactMinute,
                durationFact: -(-outterList[j].durationFactHour - (outterList[j].durationFactMinute/60).toFixed(2))
            }
            outterStr +=    '<tr data-id="'+outterItem.id+'">' +
                '   <td>'+outterItem.content+'</td>'+
                '   <td>'+outterItem.timeFactDate+'</td>'+
                '   <td>'+outterItem.timeFactTime+'</td>'+
                '   <td><span class="durationFact">'+outterItem.durationFact+'</span>小时</td>'+
                '   <td class="handle">'+
                '      <button class="ty-btn ty-btn-blue ty-circle-2" type="change" name="editOutterWork">编辑</button>'+
                '      <button class="ty-btn ty-btn-red ty-circle-2" name="deleteWork">删除</button>'+
                '   </td>'+
                '   <td class="hd">'+JSON.stringify(outterItem)+'</td>'+
                '</tr>'
        }
        $("#" + showDialog).find(".outterWork tbody").html(outterStr)
        // 获取近日的工作计划
        var recentList = data.recentList
        var recentStr = ''
        for (var k = 0; k < recentList.length; k++) {
            var recentItem = {
                type: 3,
                id: recentList[k].id,
                contentPlan: recentList[k].contentPlan,
                timePlan: moment(recentList[k].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                timePlanDate: moment(recentList[k].timePlan).format("YYYY-MM-DD"),
                timePlanTime: moment(recentList[k].timePlan).format("HH:mm"),
                durationPlanHour: recentList[k].durationPlanHour,
                durationPlanMinute: recentList[k].durationPlanMinute,
                durationPlan: -(-recentList[k].durationPlanHour - (recentList[k].durationPlanMinute/60).toFixed(2)),
                fulfillment: recentList[k].fulfillment
            }
            recentStr +=    '<tr data-id="'+recentItem.id+'">' +
                '   <td>'+recentItem.contentPlan+'</td>'+
                '   <td>'+moment(recentItem.timePlan).format("YYYY-MM-DD HH:mm")+'</td>'+
                '   <td>'+recentItem.durationPlan+'小时</td>'+
                '   <td class="handle">'+
                '      <button class="ty-btn ty-btn-blue ty-circle-2" type="change" name="editRecentWork">编辑</button>'+
                '      <button class="ty-btn ty-btn-red ty-circle-2" name="deleteWork">删除</button>'+
                '   </td>'+
                '   <td class="hd">'+JSON.stringify(recentItem)+'</td>'+
                '</tr>'
        }
        $("#" + showDialog).find(".recentWork tbody").html(recentStr)

        if (showDialog === 'workSee') {
            $("#" + showDialog).find(".handle").hide()
        }
    })
}

// creator: 张旭博，2022/6/27 17:10，某条记录的详情 - 评论处理
function handleComment(commentList, showDialog) {
    var str = ''
    for (var i in commentList) {
        var replyList = commentList[i].replyList
        var replyStr = ''
        if (replyList.length > 0) {
            replyStr =  '<div class="reply-avatar">'
            for (var j in replyList) {
                replyStr += '<div class="review-item" data-id="'+replyList[j].id+'">' +
                            '    <div class="review-title">' + replyList[j].createName + ' ' + moment(replyList[j].createDate).format("YYYY-MM-DD HH:mm:ss") + '回复' +
                            '        <div class="btn-group text-right">' +
                            '           <button class="ty-btn ty-btn-red ty-circle-2" onclick="deleteReplyBtn($(this))">删除</button>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="review-content">'+replyList[j].content+'</div>' +
                            '</div>'
            }
            replyStr +=     '</div>'
        }

        str +=  '<div class="review-item-avatar">' +
                '    <div class="review-avatar" data-id="'+commentList[i].id+'" data-user="'+commentList[i].creator+'">' +
                '        <div class="review-item">' +
                '            <div class="review-title">' + commentList[i].createName + ' ' + moment(commentList[i].createDate).format("YYYY-MM-DD HH:mm:ss") + '点评' +
                '                <div class="btn-group text-right">' +
                '                   <button class="ty-btn ty-btn-blue ty-circle-2" onclick="inputReplyBtn($(this))">回复</button>' +
                '                </div>' +
                '            </div>' +
                '            <div class="review-content">'+commentList[i].content+'</div>' +
                '        </div>' +
                '    </div>' + replyStr +
                '</div>'
    }
    $("#" + showDialog).find(".reviewPart").html(str)
    if (commentList.length === 0) {
        $("#" + showDialog).find(".panel_review").hide()
    } else {
        $("#" + showDialog).find(".panel_review").show()
    }
}

// creator: 张旭博，2022/6/27 10:40，录入点评回复 - 按钮
function inputReplyBtn(selector) {
    bounce_Fixed.show($("#inputReply"))
    var parent = selector.parents(".review-avatar").data("id")
    var beReplyId = selector.parents(".review-avatar").data("user")
    $("#inputReply textarea").val("")
    countWords()
    $("#inputReply .sureBtn").unbind().on("click", function () {
        var workDate    = $(".tbl_month").data("date")
        var userWork    = $(".tbl_month").data("workId")

        var content = $("#inputReply [name='content']").val()
        if ($.trim(content) === '') {
            layer.msg("请录入内容")
            return false
        }

        $.ajax({
            url: '../mywork/saveReply.do',
            data: {
                userWork: userWork,  // 用户工作ID
                content: content,  // 评论内容
                beReplyId: beReplyId,  // 被回复人ID
                parent: parent,  // 被回复的评论ID
            },
            success: function (res) {
                var data = res.data
                var success = res.success
                if (success === 1) {
                    layer.msg("操作成功")
                    bounce_Fixed.cancel()
                    getWorkOfSomeday(workDate)
                    initMonth()
                } else {
                    layer.msg(data)
                }
            }
        })
    })

}

// creator: 张旭博，2022/6/27 10:40，删除点评回复 - 按钮
function deleteReplyBtn(selector) {
    bounce_Fixed.show($("#bounceFixed_tip"))
    $("#bounceFixed_tip .tips").html("确定删除您的回复吗？")
    $("#bounceFixed_tip .sureBtn").unbind().on("click", function (){
        var id =  selector.parents(".review-item").data("id")
        var workDate  = $(".tbl_month").data("date")
        var data = {
            id: id, // 要删除的评回复ID
        }
        $.ajax({
            url: '../mywork/delReply.do',
            data: data,
            success: function (res) {
                var data = res.data
                var success = res.success
                if (success === 1) {
                    layer.msg("操作成功")
                    bounce_Fixed.cancel()
                    getWorkOfSomeday(workDate)
                } else {
                    layer.msg(data)
                }
            }
        })
    })
}

// creator: 张旭博，2023-01-28 02:59:57，比较内容

function chargeEqual(first, second) {
    return first === second?1:0
}


// 时间控件初始化
laydate.render({
    elem: '#otherMonthWork',
    type: 'month',
    showBottom: false,
    change: function (value, date, endDate) { //监听日期被切换
        lay('#otherMonthWork').val(value);
        $('.laydate-month-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
            // 查看其他月份的工作记录
            $(".btn-back").show().siblings(".btn-input").hide();
            $(".tbl_month").data("month", value)
            initMonth()
            $("#otherMonthWork").val("")
        })
    }
});
laydate.render({
    elem: '#yearReport',
    type: 'year',
    max: 0,
    showBottom: false,
    change: function (value) { //监听日期被切换
        $('#yearReport').val('');
        $('.laydate-year-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
            jumpPage("monthReport", function (){
                getMonthlyReportStat(value)
                $("#monthReport").data("month", value)
            })
        })
    }
});
laydate.render({
    elem: '#monthReport',
    type: 'month',
    max: 0,
    showBottom: false,
    change: function (value) { //监听日期被切换
        $('#monthReport').val('');
        $('.laydate-month-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
            jumpPage("monthReport", function (){
                getMonthlyReportStat(value)
                $("#monthReport").data("month", value)
            })
        })
    }
});
var recent_timePlanDate = laydate.render({elem: '#recent_timePlanDate', format: 'yyyy-MM-dd'});
var inner2_timeContinueDate = laydate.render({elem: '#inner2_timeContinueDate', format: 'yyyy-MM-dd'});
var inner4_timeContinueDate = laydate.render({elem: '#inner4_timeContinueDate', format: 'yyyy-MM-dd'});