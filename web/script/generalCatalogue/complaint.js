
$(function () {
    let info = {
        "oid":sphdSocket.org.id,    // oid Integer 机构id
        "type":'1',                     // type String 查询的排序方式 1是以录入时间为准 2是以接到投诉的时间为准
        "customer":'',               // customer Integer 客户的id 没选择时传空
        "year":'',                    // year String 年 例“2021” 没选择时传空
        "month":'',                    // month String 月 例“05” 没选择时传空
        "settleType":'',              // 结案类型:1-真实投诉，现已处理完毕并获客户确认,2-不属我公司问题，且已得到客户确认,3-其他原因，不该算作投诉,4-与其他投诉重复
        "sta":0,                      // 0 - 未完结 ， 1 - 已完结
        "pageSize":20                // pageSize 条数
    };
    $("#searchInfo0").html(JSON.stringify(info));
    $("#searchInfo1").html(JSON.stringify(info));
    $("#sta").val(0);
    getList(1);
    getCustomerList();
    // 初始化默认值
    initSearch();

    // 两个搜索
    $(".search").click(function () {
        let sta = $(this).data('sta');
        $("#sta").val(sta);
        let searchInfo = JSON.parse($("#searchInfo" + sta).html());
        searchInfo['sta'] = sta ;
        searchInfo['settleType'] = $(this).siblings('.settleType').val() ;
        searchInfo['month'] = $(this).siblings('.month').val() ;
        searchInfo['year'] = $(this).siblings('.year').val();
        searchInfo['type'] = $(this).siblings('.type').val() ;
        searchInfo['customer'] = $(this).siblings('.customer').val() ;
        $("#searchInfo" + sta).html(JSON.stringify(searchInfo));
        getList(1);
    });
    //  创造差异
    $("tbody").on('click','.funBtn',function () {
        let id = $(this).siblings(".hd").html();
        floatToPage('../complaint/complaintInfo.do', { 'id': id, 't': 10 })
    })
});
// create:hxz 2021-01-20 初始化
function initSearch(){
    $(".settleType").val("");
    $(".type").val("1");
    $(".customer").val("");
    $(".month").val("");
    let orgCreateTime = new Date(Number($("#orgCreateDate").html()));
    let serviceCurTime = new Date(Number($("#nowDate").html()));
    let dateStr = serviceCurTime.format("yyyy年MM月dd日");
    let weekStr = getWeek(serviceCurTime);
    $(".nowDay").html(dateStr + '&nbsp;&nbsp;' + weekStr);
    // 时间控件初始化
    let beginYear = orgCreateTime.getFullYear() + '-01-01';
    let endYear = serviceCurTime.format("yyyy-MM-dd");
    let curYear = serviceCurTime.getFullYear()+'年';
    let dateFormat = {format: 'yyyy年', type: 'year', min:beginYear , max:endYear,trigger: 'click',
        change: function(value, date, endDate){
            $(".laydate-btns-confirm").click();
        }};
    laydate.render({elem: '#year1',value:curYear, ...dateFormat});
    laydate.render({elem: '#year2',value:'', ...dateFormat});
}
// create:hxz 2021-01-16 校验选择时间的合法性
function chargeYear(thisObj) {
    let year = thisObj.siblings(".year").val();
    if(year.length === 0){
        layer.msg("请先选择年份！");
        thisObj.val("");
        return false
    }
    year = year.substr(0,4);
    let month = thisObj.val();
    let orgCreateTime = new Date(Number($("#orgCreateDate").html())).format('yyyy-MM');
    let serviceCurTime = new Date(Number($("#nowDate").html())).format('yyyy-MM');
    let beginYearMonth = (new Date(orgCreateTime)).valueOf() ;
    let endYearMonth = (new Date(serviceCurTime)).valueOf() ;
    let select = year + '-' + month;
    let selectYearMonth = (new Date(select)).valueOf() ;
    if(selectYearMonth < beginYearMonth){
        layer.msg('请选择'+ orgCreateTime + '到' + serviceCurTime + ' !');
        thisObj.val("");
    }
    if(selectYearMonth > endYearMonth){
        layer.msg('请选择'+ orgCreateTime + '到' + serviceCurTime + ' !');
        thisObj.val("");
    }

}
// create:hxz 2021-01-16 返回
function goBack() {
    $(".sta0").show().siblings().hide();
    $(".finished .settleType").val("");
    $(".finished .type").val("1");
    $(".finished .customer").val("");
    $(".finished .month").val("");
    let cur = ($(".nowDay").html()).substr(0,5)
    $("#year1").val(cur);
    $("#sta").val(0);
}
// create:hxz 2021-01-16 获取客户列表
function getList(cur){
    let sta = Number($("#sta").val());
    let searchInfo = JSON.parse($("#searchInfo" + sta).html());
    searchInfo['currentPageNo'] = cur
    searchInfo['year'] = searchInfo['year'].replace('年','') ;
    let url = "../complaint/unfinishedComplaintBase.do" ; // 未完结
    if(sta === 1){
        url = "../complaint/finishedComplaintBase.do" ;  // 已完结
    }else{
        delete searchInfo.settleType
    }
    delete searchInfo.sta
    $.ajax({
        "url": url ,
        "data":searchInfo,
        success:function(data) {
            try {
                var totalPage  = data["data"]['pageInfo']['totalPage'] || 1;
                var totalResult  = data["data"]['pageInfo']['totalResult'] || 0;
                var list  = data["data"]['overviewCompleteBase'] || [];
            }catch (err){
                layer.msg("获取数据失败，请重试！");
                return false
            }
            var str="";
            if(list && list.length>0){
                list.nullToStr()
                for(var i = 0 ; i < list.length ; i++){
                    let item = list[i];
                    str += `<tr>
                            <td>${ item.customName }</td>
                            <td>${ item.customCode }</td>
                            <td><div class="contact"><span>${ item.contactName }</span><span>${ item.contactPhone }</span></div></td>
                            <td>${ new Date(item.receiptTime ).format('yyyy-MM-dd')}</td>
                            <td><div class="contact"><span>${ item.createName }</span><span>${ new Date(item.createTime).format('yyyy-MM-dd hh:mm:ss')}</span></div></td>
                            <td>${ item.processorName }</td>
                            ${ sta === 0 ? `<td>${ formatSta(item.state) }</td>` : "" }
                            ${ sta === 1 ? `<td>${ new Date(item.settleApproveTime ).format('yyyy-MM-dd') }</td>` : "" }
                            ${ sta === 1 ? `<td>${ (item.settleOpinion ) }</td>` : "" }
                            <td>
                                <span class="ty-color-blue funBtn" data-type="scan">查看</span>
                                <span class="hd">${ item.id }</span>
                            </td>
                        </tr>`;
                }
            }
            $(".curList").html(totalResult);
            $(".sta" + sta + " tbody").html(str);
            setPage($("#page" + sta),cur,totalPage,'generalComplaint');
            $(".sta" + sta).show().siblings().hide();

        }
    });
}
// create:hxz 2021-01-19 周几
function getWeek(date) {
    var days =  new Date(date).getDay();
    switch(days) {
        case 1:
            days = '星期一';
            break;
        case 2:
            days = '星期二';
            break;
        case 3:
            days = '星期三';
            break;
        case 4:
            days = '星期四';
            break;
        case 5:
            days = '星期五';
            break;
        case 6:
            days = '星期六';
            break;
        case 0:
            days = '星期日';
            break;
        default: days = '';
            break;
    }
    return days;
}
// create:hxz 2021-01-16 状态
function formatSta(state) {
    switch (Number(state)){
        case 1:
            return '立案待审批';
            break;
        case 2:
            return '立案驳回';
            break;
        case 3:
            return '待结案';
            break;
        case 4:
            return '结案待审批';
            break;
        case 5:
            return '结案驳回';
            break;
        case 6:
            return '结案通过';
            break;
        default:
            return ''
    }
}
// create:hxz 2021-01-16 获取客户列表
function getCustomerList(){
    $.ajax({
        "url":"../invoice/getAllCustomer.do",
        success:function(data) {
            var list = data["Customers"], strCus = "<option value=''>—— 全部 ——</option>";
            if(list && list.length>0){
                for(var i = 0 ; i < list.length ; i++){
                    let item = list[i];
                    strCus += `<option value="${item.id}">${item.name} ${item.code}</option>`;
                }
            }
            $(".customer").html(strCus);
        }
    });
}








