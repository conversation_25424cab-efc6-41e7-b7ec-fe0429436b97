// creator: 张旭博，2018-12-20 15:55:09，常规借款js
var curMonth = ''
$(function () {
    $("input").each(function(){
        var key = $(this).attr("name")
        $(this).attr("autocomplete",'off')

    });
    $(".faXian").click(function () {
        let faO = $(this).find(".fa")
        if(faO.hasClass('fa-circle-o')){
            faO.attr("class", "fa fa-dot-circle-o")
        }else{
            faO.attr("class", "fa fa-circle-o")
        }
    })
    // 获取主列表
    getList(1,1,15,0)

    // 获取支票数据
    // getChequeInfo()
    // 两个主列表的操作按钮

    $(".orgName").html(sphdSocket.org.name);
    // 借款列表 按钮点击事件 1综合查看 2修改借款信息 3付款录入 4本笔借款不再付款 5本笔借款继续付款
    $(".list").on("click", '.btn', function () {
        var id = $(this).parents('tr').data("id")
        var amount = $(this).parents('tr').data("amount")
        $("#ordLoanAddEdit").data('id', id)
        $("#ordLoanAddEdit").data('amount',amount)
        // getOrdLoanAll($(this).parents("tr"), id)
        var btnName = $(this).data("type")
        switch (btnName) {
            // 未完结的常规借款 - 综合查看
            case 'generalScan':
                $("#generalScan .bonceHeadName").html('综合查看')
                $("#generalScan .handleBtn").hide()
                bounce.show($("#generalScan"));
                getOrdLoanDetail({'id': id})
                break;

            // 已完结的常规借款 - 综合查看
            case 'endGeneralScan':
                $("#generalScan .bonceHeadName").html('综合查看')
                $("#generalScan .handleBtn").hide()
                bounce.show($("#generalScan"));
                getOrdLoanDetail({'id': id}, true)
                break;

            // 修改借款信息
            case 'updateBorrowBtn':
                bounce.show($("#ordLoanAddEdit"));
                $("#ordLoanAddEdit .bonceHeadName").html('修改借款信息')
                $("#ordLoanAddEdit").data('type',2)
                setOrdLoanDetail(id)
                bounce.everyTime('0.5s','ordLoanAddEdit',function(){
                    var state = 0;
                    $("#ordLoanAddEdit").find("[require]:visible").each(function () {
                        if($.trim($(this).val()) === ''){
                            state ++;
                        }
                    });
                    if($("#ordLoanAddEdit [name='repaymentMethod']:checked").val() === '2' && $("#ordLoanAddEdit [name='repaymentDate']").val() === ''){
                        state ++
                    }
                    if($("#ordLoanAddEdit [name='repaymentMethod']:checked").length === 0 || $("#ordLoanAddEdit [name='interestMethod']:checked").length === 0){
                        state ++
                    }
                    if($("#ordLoanAddEdit [name='repaymentMethod']:checked").val() === '2') {
                        $("#repaymentDate").show();
                    }else{
                        $("#repaymentDate").hide();
                    }

                    if( state > 0 ){
                        $("#sureOrdLoanAdd").prop("disabled",true)
                    }else {
                        $("#sureOrdLoanAdd").prop("disabled",false)
                    }
                });
                break;

            // 付款录入/收款录入
            case 'ordRepaymentAddBtn':
                var json = $("#ye_ordLoan .json").html();
                json = JSON.parse(json);
                var index = Number(json.index) + 1 ; // 1-借来的（付款录入），2-借出（收款录入）
                bounce_Fixed.show($("#ordRepaymentAdd"));
                if(index == 1){
                    $("#ordRepaymentAdd .bonceHeadName").html('付款录入')
                    $("#paytitle").html("支付方式")
                    $("#paytitle2").html("付款日期")
                    $("#paytitle3").html("付款金额")
                }else{
                    $("#ordRepaymentAdd .bonceHeadName").html('收款录入')
                    $("#paytitle").html("收款方式")
                    $("#paytitle2").html("收款日期")
                    $("#paytitle3").html("收款金额")
                }
                $(".opt").hide().siblings().hide();

                $("#ordRepaymentAdd").data('borrowCommon', id)
                $("#ordRepaymentAdd input").val("")
                $("#ordRepaymentAdd select").val("")
                $("#ordRepaymentAdd .opt").hide()
                $("#ordRepaymentAdd").data("state",1)
                $("#repaymentMethod").prop("disabled",false)

                bounce.everyTime('0.5s','ordLoanAddEdit',function(){
                    var state = 0;
                    $("#ordRepaymentAdd").find("[name]:visible").each(function () {
                        if($.trim($(this).val()) === ''){
                            state ++;
                        }
                    });
                    if($('#ordRepaymentAdd .chooseCheque').val() !== ''&&$('#ordRepaymentAdd .chooseCheque').is(":visible")){
                        $("#ordRepaymentAdd [name='repaymentAmount']").prop("disabled",true)
                    }else {
                        $("#ordRepaymentAdd [name='repaymentAmount']").prop("disabled",false)
                    }
                    if($("#ordRepaymentAdd .bankAccount").val() === '') {
                        $("#ordRepaymentAdd .chequeNo").html('')
                    }
                    let repaymentMethod = $("#repaymentMethod").val()
                    if(repaymentMethod === '1'){
                        let isook = $("#ordRepaymentAdd .fa-dot-circle-o").length
                        if(isook === 0){
                            state ++;
                        }
                    }


                    if( state > 0){
                        $("#sureOrdRepaymentAdd").prop("disabled",true)
                    }else {
                        $("#sureOrdRepaymentAdd").prop("disabled",false)
                    }
                });
                break;

            // 本笔借款不再付款
            case 'endBtn':
                bounce.show($("#endPay"));
                $("#endPay").data('id', id)
                break;

            // 本笔借款继续付款
            case 'startBtn':
                bounce.show($("#startPay"));
                $("#startPay").data('id', id)
                break;
            default:
                window[btnName]($(this))

        }
    });

    // 主页面 按钮点击事件 新增借款
    $("#ordLoanAddBtn").on('click', function () {
        $("#ordLoanAddEdit [name='loanType']").removeAttr("disabled");
        $("#ordLoanAddEdit [name='lender']").removeAttr("disabled");
        $("#ordLoanAddEdit [name='borrower']").removeAttr("disabled");
        bounce.show($("#ordLoanAddEdit"))
        $("#ordLoanAddEdit").data('type',1)
        $('#ordLoanAddEdit input:not(":radio")').val("")
        $('#ordLoanAddEdit select').val("")
        $('#ordLoanAddEdit textarea').val("")
        $('#ordLoanAddEdit input:radio:checked').prop('checked',false)
        $('#ordLoanAddEdit #incomeMethod').prop('disabled',false)
        $('#ordLoanAddEdit #incomeMethod option:eq(3)').hide()
        $('#ordLoanAddEdit .trans1').show().siblings().hide();
        $('#ordLoanAddEdit .interest0').show().siblings().hide();
        $("#ordLoanAddEdit").attr("class", "bonceContainer bounce-green");
        $("#ordLoanAddEdit [name='principalAmount']").prop("disabled",false)
        $("#ordLoanAddEdit .bonceHead span").html("新增借款");
        $("#ordLoanAddEdit .tansCommon").hide();
        $("#sureOrdLoanAdd").prop("disabled",false)

        bounce.everyTime('0.5s','ordLoanAddEdit',function(){
            var state = 0;
            $("#ordLoanAddEdit").find("[require]:visible").each(function () {
                if($.trim($(this).val()) === ''){
                    state ++;
                }
            });
            var repaymentMethod = $("#ordLoanAddEdit [name='repaymentMethod']:checked").val() ;
            if( repaymentMethod === '2' && $("#ordLoanAddEdit [name='repaymentDate']").val() === ''){
                state ++
            }
            if($("#ordLoanAddEdit [name='repaymentMethod']:checked").length === 0 || $("#ordLoanAddEdit [name='interestMethod']:checked").length === 0){
                state ++
            }
            if($("#ordLoanAddEdit [name='repaymentMethod']:checked").val() === '2') {
                $("#repaymentDate").show();
            }else{
                $("#repaymentDate").hide();
            }
            if( state > 0 ){
                $("#sureOrdLoanAdd").prop("disabled",true)
            }else {
                $("#sureOrdLoanAdd").prop("disabled",false)
            }
        });
    });

    // 切换主列表
    $(".ty-secondTab").on('click','li', function () {
        var index = $(this).index();
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        var json = JSON.parse($("#ye_ordLoan .json").html());
        let state = ''
        switch(index){
            case 0:
            case 1:
                state = $(this).data('state')
                $('#endBtn').html('已完结的常规借款')
                // 获取主列表
                $(this).data('state', state)
                getList(state,1,15, index)
                $("#boxchoo").hide()
                break;
            case 2: // 付出去的采购预付款
                $('#endBtn').html('已完结的数据')
                state = $(this).data('state')
                var time = json["time"]
                if(state === 2){ // 未完结
                    time = ''
                    $("#boxchoo").hide()
                }else{ // 已完结
                    $("#boxchoo").show()
                }
                advancePaymentPayLoan(state, time, 1, index)
                break;
            case 3: // 报销时多付出去的款
                $('#endBtn').html('已完结的数据')
                state = $(this).data('state')
                if(state === 0){ // 未完结
                    time = ''
                    $("#boxchoo").hide()
                }else{ // 已完结
                    $("#boxchoo").show()
                }
                getMostDebitLoan(state, '', 1,index)
                break;
            case 4: // 报销时多付出去的款
                $('#endBtn').html('已完结的数据')
                state = $(this).data('state')
                if(state === 2){ // 未完结
                    time = ''
                    $("#boxchoo").hide()
                }else{ // 已完结
                    $("#boxchoo").show()
                }
                getLoanOverpaymentList(state, '', 1,index)
                break;
        }

    });

    // 新增借款弹窗 归还本金的约定切换事件
    $("#ordLoanAddEdit .repaymentMethod").on("change", function () {
        if($("[name='repaymentMethod']:checked").val() === '1') {
            $("#repaymentDate").val("");
        }
    })


    // 新增借款弹窗 本金类型切换事件
    $("#ordLoanAddEdit #incomeMethod").on("change", function () {
        incomeMethodChange(1);
    })


    $("#ordLoanAddEdit #accountId1").on("change", function () {
        accountIdChange($(this).val(), $("#billNo3"));
    })

    $("#withinOrAbroad").change(function(){
        withinOrAbroadChange();
    })

    // 新增借款弹窗 利息的支付方式切换事件
    $("#ordLoanAddEdit [name='interestMethod']").on("click", function () {

        $("#ordLoanAddEdit .interestCon input").val('')
        var val = '#ordLoanAddEdit .interest' + $(this).val()
        $(val).show().siblings().hide();
    })

    // 综合查看弹窗-》借款修改记录弹窗/还款记录修改弹窗 按钮点击事件 1修改前 2修改后
    $("#updateLog").on("click", ".btn", function () {
        var status = $("#updateLog").data('status')
        if(status === 1) {
            $("#borrowInfo .item-content").html('')
            seeChanges($(this))
        }else {
            getRepayHistoryDetail($(this))
        }
    })
    // 综合查看弹窗-》付款修改记录弹窗 按钮点击事件 1修改 2修改记录
    $("#generalScan .repaymentList").on("click", 'td>span', function () {
        var btnName = $(this).data("type")
        switch (btnName) {
            // 1查看
            case 'seeRepayBtn':
                seeRepayBtn($(this))
                break;

            // 1修改
            case 'changeRepayBtn':
                changeRepayBtn($(this))
                break;

            // 2修改记录
            case 'showHistory':
                showHistory(2, $(this))
                break;
        }
    })

    // 付款录入/修改付款弹窗 支付方式切换事件
    $("#repaymentMethod").on("change",function () {
        repaymentMethodChange();
    })

    // 付款录入/修改付款弹窗 - 切换内部支票和外部支票
    $("#billType").on("change",function () {
        $("#ordRepaymentAdd .opt input").val('')
        $("#ordRepaymentAdd .opt select").not('#billType').val('')
        var val = $(this).val();
        if(val === '1') {
            // 内部支票
            $("#ordRepaymentAdd .opt").children().hide();
            $("#ordRepaymentAdd .opt31").show();
            chooseChequeData(4 , $("#bankAccount"));
        } else if (val === '0') {
            // 外部支票
            $("#ordRepaymentAdd .opt").children().hide()
            $("#ordRepaymentAdd .opt32").show()
            $("#ordRepaymentAdd .chequeName").html('支票号')
            $("#ordRepaymentAdd .chooseCheque").attr('placeholder','请选择外部支票')
            chooseChequeData(2 ,  $("#financeReturns tbody"));
        }
    })


    // 付款录入/修改付款弹窗  - 根据银行获取支票号
    $("#ordRepaymentAdd .bankAccount").on("change", function () {
        var val = $(this).val()
        if (val !== '') {
            getChequeByAccountId(val)
        }else {
            $("#ordRepaymentAdd .chequeNo").html('')
        }
    })

    laydate.render({
        elem: '#repaymentDate'
        ,done: function(value, date, endDate){
            if (value !== '') {
                $("#ordLoanAddEdit [name='repaymentMethod'][value='2']").prop("checked", true)
            }
        }
    });

});
function repaymentMethodChange() {
    var type = $("#ordRepaymentAdd .bonceHeadName").html();
    $("#ordRepaymentAdd .optPay select").val('')
    $("#ordRepaymentAdd .optPay input").val('')
    var val =$("#repaymentMethod").val();
    if(type == "付款录入" || type == "修改付款"){
        $(".opt").show().siblings().hide();
        if (val === '1') {
            if(type == "付款录入"){
                $("#ordRepaymentAdd .opt").show()
                $("#ordRepaymentAdd .opt1").show().siblings().hide()
            }else{
                $("#ordRepaymentAdd .opt").hide()
            }
        } else {
            $("#ordRepaymentAdd .opt").show()
            if (val === '3') {
                //转账支票
                $("#ordRepaymentAdd .opt3").show().siblings().hide()
            } else if (val === '4') {
                //承兑汇票
                $("#ordRepaymentAdd .opt").children().hide()
                $("#ordRepaymentAdd .opt4").show()
                $("#ordRepaymentAdd .chequeName").html('汇票号')
                $("#ordRepaymentAdd .chooseCheque").attr('placeholder','请选择汇票号')
                chooseChequeData(3 , $("#acceptanceBills tbody"));
            } else if (val === '5') {
                //银行转账
                $("#ordRepaymentAdd .opt5").show().siblings().hide()
                chooseChequeData(4 , $("#bankAccount3"));

            }
        }
    }else{
        $(".opt").hide().siblings().show();
        $(".payx1 .faXian").show();
        $(".payx").children().hide(); $(".payx" + val).show();
        if(val == 4){
            $(".pay1").html("收到汇票日期")
            $(".pay2").html("汇票号")
            $(".pay3").html("汇票到期日")
            $(".pay4").html("原始出具汇票单位")
            $(".pay5").html("出具汇票银行")
        }else if(val == 3){
            $(".pay1").html("收到支票日期")
            $(".pay2").html("支票号")
            $(".pay3").html("支票到期日")
            $(".pay4").html("出具支票单位")
            $(".pay5").html("出具支票银行")
        }else if(val == 5){
            chooseChequeData(4 , $("#accountId4"));
        }else if(val == 1){
            if(type == "修改收款"){
                $(".payx1 .faXian").hide();
            }
        }
    }
}
function withinOrAbroadChange(bankID) {
    var loanType = $("#ordLoanAddEdit [name='loanType']:checked").val();
    var type = $("#withinOrAbroad").val();
    $(".tansCommon input").val('');
    $(".tansCommon").hide();
    $(".trans3").show();
    $('#ordLoanAddEdit .inOrOut' + type).show() ;
    if(type == '0'){
        chooseChequeData(2 , $("#outNo"));
    }else if(type == '1'){
        chooseChequeData(4 , $("#accountId1"), bankID)
    }
    chargeFu();
}
function incomeMethodChange(bool, Bankval) {
    var loanType = $("#ordLoanAddEdit [name='loanType']:checked").val();
    var type = $("#incomeMethod").val();
    if(Number(loanType) > 0){
        $("#ordLoanAddEdit .trans3").hide(); $("#ordLoanAddEdit .trans4").hide();
        var orgName = sphdSocket.org.name ;
        if(!bool){ // 假值 重置数据
            if(Number(loanType) == 1){ //收款方
                $("#ordLoanAddEdit [name='borrower']").attr("disabled", "disabled").val(orgName);
                $("#ordLoanAddEdit [name='lender']").val("").removeAttr("disabled");
                $("#incomeMethod option").eq(2).hide();

            }else{ // 2 借款方
                $("#ordLoanAddEdit [name='lender']").attr("disabled", "disabled").val(orgName);
                $("#ordLoanAddEdit [name='borrower']").val("").removeAttr("disabled");
                $("#incomeMethod option").eq(2).show();
            }
            $("#incomeMethod").val("");
            $("#incomeMethod option:eq(0)").attr("selected", "selected");
            type = "";
        }
        if(!type){
            $(".incomeMethodCon").hide();
        }else{
            if(Number(loanType) == 1){ //收款方
                if(type == 1){
                    charge1();
                }else{
                    if(type == 5){
                        chooseChequeData(4, $("#bankAccount2"));
                    }
                    charge2();
                }

            }else{ // 2 借款方
                charge1();
            }
        }



    }else{
        var orgName = $(".orgName").html();
        layer.msg("请先选择" + orgName + "为出资方或借款方");
        $("#incomeMethod").val("")
    }
}
// 处理新数据
function charge1(){
    var type = $("#incomeMethod").val();

    $("#ordLoanAddEdit .incomeMethodCon1").show();
    $("#ordLoanAddEdit .incomeMethodCon2").hide();
    $("#ordLoanAddEdit .incomeMethodCon1 .tansCommon input").val('');
    $("#ordLoanAddEdit .tansCommon").hide();

    if(type != ""){
        $('#ordLoanAddEdit .trans' + type).show() ;
        if(type == 4){
            chooseChequeData(3 , $("#acceptanceBills tbody"));
        }else if(type == 5){
            chooseChequeData(4 , $("#accountId2"));
        }else if(type == 3){
            $("#ordLoanAddEdit .transFu").hide();
        }
    }

    chargeFu();
}
// 处理老数据
function charge2(){
    var type = $("#incomeMethod").val();
    $(".incomeMethodCon2 input").val('')

    $("#ordLoanAddEdit .incomeMethodCon2").show();
    $("#ordLoanAddEdit .incomeMethodCon1").hide();
    var val = '#ordLoanAddEdit .incomeMethodCon2 .trans' + type
    $(val).show().siblings().hide();
}
// creator: hxz  2018-07-18  初始化 三级弹框 - fixed2
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
// bounce_Fixed2.show($("#inOrOut"));
bounce_Fixed2.show($("#borrowInfo"));
bounce_Fixed2.show($("#payInfo"));
bounce_Fixed2.cancel();

// creator: 张旭博，2018-12-28 09:02:24，新增借款 / 修改借款 - 确定
function sureOrdLoanAdd() {
    var type = $("#ordLoanAddEdit").data("type")
    var ordLoanAdd = {
        'lender': '',// 出资方
        'principalAmount': 0,// 本金
        'nominalRate': 0,// 名义利率
        'repaymentMethod': '',// 归还本金的约定:1-未约定具体日期,2-已约定归还的日期
        'repaymentDate': '',// 已约定归还的日期
        'incomeMethod': '',// 本金型式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
        'interestMethod': '',// 利息的支付方式：0-无利息,1-按日付,2-按月付,3-按年付
        'interestAccrualDate': '',// 开始计息日期
        'periodRepaymentDate': '',// 每期还款日
        'periodRepaymentAmount': 0,// 每期还款金额
        'billReceiveDate': '',// 收到支票或汇票日期
        'billNo': '',// 支票或汇票号码
        'billEndDate': '',// 支票或汇票到期日
        'billSource': '',// 出具支票或汇票单位
        'billBank': '',// 出具支票或汇票银行
        'arriveDate': '', // 转账到账日期
        'accountId': '', // 银行ID
        'memo': '', // 备注
        'loanType': '', // 借贷类型:1-借入(默认1),2-借出'
        // 'loaner': '', // 借出方
        'borrower': '', // 借入方
        'operatorName': '', // 经手人（如果是借入，此经手人为收款经手人；如果为借出，此经收人为付款经手人
        'partnerName': '', // 合作方经手人姓名（如果是借入，此经手人为付款经手人；如果为借出，此经收人为收款经手人）
        'withinOrAbroad': '', //  支出转账支票时使用  1-内部 0-外部
        'returnId': '', //  支出时的支票id
        'paymentDate': '', //  付款日期
        'oppositeBankno': '', //  开户行
        'oppositeBankcode': '', //  对方银行账号
        'oppositeAccount':''//  账户名称
    }
    $("#ordLoanAddEdit [name]:visible").each(function () {
        var name = $(this).attr("name")
        if ($(this).attr("type") === 'radio') {
            ordLoanAdd[name] = $("#ordLoanAddEdit [name='"+name+"']:checked").val()
        } else {
            ordLoanAdd[name] = $(this).val()
            if(name == 'billNo'){
                ordLoanAdd['returnId'] =$(this).data('id')
            }
        }
        if ($(this).attr("type") === 'num') {
            ordLoanAdd[name] = Number(ordLoanAdd[name])
        }
    })

    ordLoanAdd.nominalRate = ordLoanAdd.nominalRate / 100
    var url = ''
    if (type === 1) {
        // 新增借款
        url = '../loan/ordLoanAdd.do'
        $("#ordLoanAddEditTtl").html('新增借款')
    } else {
        // 修改借款
        ordLoanAdd.id = $("#ordLoanAddEdit").data("id")
        ordLoanAdd.isChange = Number($("#ordLoanAddEdit").data("amount")) === Number(ordLoanAdd.principalAmount)?0:1
        var lastId = $("#ordLoanAddEdit").data("lastId")
        if(lastId) {
            ordLoanAdd.previousId = lastId
            ordLoanAdd.updateFirst = 0
        } else {
            ordLoanAdd.updateFirst = 1
        }
        url = '../loan/ordLoanModify.do'
        $("#ordLoanAddEditTtl").html('修改借款信息')
    }
    if(Number(ordLoanAdd.principalAmount) === 0){
        layer.msg("本金金额不能为0!")
        return false
    }
    // 测试专用
    // ordLoanAdd = {
    //     lender:'',
    // principalAmount:'1',
    // nominalRate:'0.01',
    // repaymentMethod:'1',
    // repaymentDate:'',
    // incomeMethod:'1',
    // interestMethod:'0',
    // interestAccrualDate:'',
    // periodRepaymentDate:'',
    // periodRepaymentAmount:'0',
    // billReceiveDate:'',
    // billNo:'',
    // billEndDate:'',
    // billSource:'',
    // billBank:'',
    // arriveDate:'',
    // accountId:'',
    // memo:'的尽快哈地方',
    // loanType:1,
    // loaner:'出资方',
    // borrower:'借款方',
    // operatorName:'',
    // partnerName:'收款经手人',
    // withinOrAbroad:'',
    // returnId:'',
    // oppositeBankno:'',
    // oppositeBankcode:'',
    // oppositeAccount:'',
    // paymentDate:'2020-04-23'
    // }
    $.ajax({
        url: url,
        data: ordLoanAdd,
        success: function (data) {
            bounce.cancel();
            var data = data.data
            if (data) {
                if (data == 1) {
                    if (type === 1) {
                        layer.msg("新增成功")
                    } else {
                        layer.msg("修改成功")
                    }
                    // getList(state,1,15, index)
                    if(ordLoanAdd.loanType == 1){ //  'loanType': '', // 借贷类型:1-借入(默认1),2-借出'
                        $(".ty-secondTab li:eq(0)").click();
                    }else{
                        $(".ty-secondTab li:eq(1)").click();
                    }
                } else if (data == 0)  {
                    if (type === 1) {
                        layer.msg("新增失败")
                    } else {
                        layer.msg("修改失败")
                    }
                } else if (data == 2) {
                    layer.msg("选择的账户被冻结")
                } else if (data == 3) {
                    layer.msg("账户余额不够冲减")
                }
            }
        }
    })
}

// creator: hxz，2023-03-14 报销时多付出去的款
function getMostDebitLoan(state, yearMonth,cur,index) {
    if(state === 1){ // 已完结
        $(".more2").show().siblings().hide()
    }else{
        $(".more1").show().siblings().hide()
    }
    $.ajax({
        'url':'../purchaseInvoice/getMostDebitLoan.do',
        'data':{
            'finishType':state, // finishType:1-已完结的 其他的均为未完成
            'yearMonth':yearMonth, // String yearMonth：年月(yyyyMM)
            'pageSize':20,
            'currentPageNo':cur
        },
        success:function(res){
            let list = res.data.poLoans || []
            let pageInfo = res.page
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"] ;
            var jsonObj = JSON.stringify({"state": state , "index":index, 'time':yearMonth });
            setPage( $("#ye_ordLoan"), cur, total, "advancePaymentPayLoan", jsonObj) ;

            let str = ``
            let sum = 0
            list.forEach((item)=>{
                sum += Number(item.amount)
                str += `
                    <tr>
                        <td>${ item.supplierName || '' }</td>
                        <td>${ item.amount.toFixed(2) || ''  }</td>
                        <td>${ item.planAmount.toFixed(2) || ''  }</td>
                        <td>${ item.payNum || '0' }次， 计${ item.paidAmount.toFixed(2) || ''  }元</td>
                        <td>${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')  } ${ state === 1 ? `/ ${ new Date(item.finishTime).format('yyyy-MM-dd hh:mm:ss') }`:`` } </td>
                        <td>
                            <span class="ty-color-gray">订单</span>
                            <span class="ty-color-gray">付款记录</span>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </td>
                    </tr>
                `
            })
            $(".mainCon .list tbody").html(str);

            if(state === 1){
                $("#memo").html(`以下为采用承兑汇票等向供应商支付货款，票据金额大于应付款金额时，多支付的款项，已回收的款。此处数据仅供查看。`);

            }else{
                $("#memo").html(`以下为采用承兑汇票等向供应商支付货款，票据金额大于应付款金额时，多支付的款项，合计${ sum.toFixed(2) }元。<br>
                此处数据仅供查看。款项回收后，请及时在“处理-需收回的款”中操作，全部回收的数据即不再在此展示。`);
            }
        }
    })
}
// creator: hxz，2023-03-14 付出去的采购预付款
function advancePaymentPayLoan(status, yearMonth,cur, index) {
    $(".yfkCon1").show().siblings().hide()
    yearMonth = yearMonth ? yearMonth : null
    $.ajax({
        'url':'../purchaseInvoice/advancePaymentPayLoan.do',
        'data':{
            'prepaymentStatus': status,
            'yearMonth': yearMonth,
            'pageSize':20,
            'currentPageNo':cur
        }, // (格式 yyyyMM)
        success:function (res) {
            let listd = res.data.poOrders || [];
            let pageInfo = res.page
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"] ;
            var jsonObj = JSON.stringify({"state": status , "index":index, 'time':yearMonth });
            setPage( $("#ye_ordLoan"), cur, total, "advancePaymentPayLoan", jsonObj) ;
            var sum = 0;
            let listStr = ``
            for(var d in listd){
                let item = listd[d]
                listStr += `
                    <tr data-id="${ item.orderId }" data-amount="${ item.prepaymentAmount }">
                        <td>${ item.supplierName }</td>
                        <td>${ item.sn }</td>
                        <td>${ (item.amount && item.amount.toFixed(2)) || '' }</td>
                        <td>${ item.prepaymentProportion }%</td>
                        <td>${ item.prepaymentAmount }</td>
                        <td>${ new Date(item.factDate).format('yyyy-MM-dd')  }</td>
                        <td>
                            <span class="ty-color-blue btn" data-type="timelook">时间线查看</span>
                            <span class="hd">${ JSON.stringify(item.financePaymentId) }</span>
                        </td> 
                    </tr>  
                `
                sum += Number(item.prepaymentAmount);

            }
            $("#memo").html(`以下为发生了预付款且尚未完结的采购订单，预付款合计${ sum.toFixed(2) }元。此处数据仅供查看，且采购订单完结后，数据即不再在此展示。`);
            $(".mainCon .list tbody").html(listStr);
        }
    })
}
function comeback() {
    $(".timeLineCon").hide();
    $(".maincc").show();
}
// creator: sy 2022-11-07 时间线查看
function timelook(time){
    $(".timeLineCon").show();
    $(".maincc").hide();
    var abid = time.siblings(".hd").html();
    console.log('financePaymentId',abid);
    $.ajax({
        url:'../purchaseInvoice/orderTimeLine.do',
        data:{
            financePaymentId:abid ,
        },
        success:function(res){
            console.log(res);
            var approvalProcesses = res.data.approvalProcesses || [];
            var financePayment = res.data.financePayment;
            var str = ``
            approvalProcesses.forEach( (apItem, index) => {
                const indexNum = index + 1
                let fApItem = formatApItem(apItem)
                if(fApItem.ttl){
                    str += `<div class="apItem">
                            <div class="apTtl">
                                <span class="no">${indexNum}</span>
                                <span class="line">
                            </div>
                            <div class="apCon">
                                <p>${ fApItem.ttl }</p>
                                <p>${ fApItem.name }</p>
                                <p>${ fApItem.time }</p>
                            </div>
                        </div>`
                }

            })

            $("#approveItem").html(str)

        }
    })
}
// creator: hxz，2023-03-17 formatBusinessType
function formatApItem(apItem) {
    //         businessType：
//         23-采购订单审批 63-待付款审批(1.229采购的预付款中) 64-可付款(1.229采购的预付款中)
//         65-待复核(1.229采购的预付款中-待复核) 66-待付款(1.229采购的预付款中)   67-付款方式修改(1.229采购的预付款中)
//         45-待在线审批(采购的票款处理) 46-在线审核ok(采购的票款处理，即采购审批者的采购部门的票据审核) 47-待线下审核(采购的票款处理)
//         48-采购审批者的采购部门的付款(采购的票款处理)
//         49付款审批者-采购部门的付款(采购的票款处理,出纳的待付款审批) 50-可付款（采购的票款处理,出纳的可付款）
//         51-待复核（采购的票款处理,出纳与复核审批人-待复核） 52-待付款（采购的票款处理,出纳-待付款）
// 				53-付款方式修改（采购的票款处理,出纳-付款方式修改）
    let type = Number(apItem.businessType)
    let sta = Number(apItem.approveStatus)
    let jsonData = {
        ttl:'',
        name:'',
        time:''
    }
    switch (type){
        case 23:
            jsonData.ttl = '采购订单提交'
            jsonData.name = apItem.askName
            jsonData.time = new Date(apItem.createDate).format('yyyy-MM-dd hh:mm:ss')
            if(sta === 2){
                jsonData.ttl = '采购订单审批通过'
                jsonData.name = apItem.userName
                jsonData.time = new Date(apItem.handleTime).format('yyyy-MM-dd hh:mm:ss')
            }
            break;
        case 63:
            if(sta === 2){
                jsonData.ttl = '付款审批通过'
                jsonData.name = apItem.userName
                jsonData.time = new Date(apItem.handleTime).format('yyyy-MM-dd hh:mm:ss')
            }
            break;
        case 64:
            jsonData.ttl = '可付款提交'
            jsonData.name = apItem.askName
            jsonData.time = new Date(apItem.createDate).format('yyyy-MM-dd hh:mm:ss')
            if(sta === 2){
                jsonData.ttl = '可付款审批通过'
                jsonData.name = apItem.userName
                jsonData.time = new Date(apItem.handleTime).format('yyyy-MM-dd hh:mm:ss')
            }
            break;
        case 65:
            jsonData.ttl = '付款复核通过'
            jsonData.name = apItem.askName
            jsonData.time = new Date(apItem.createDate).format('yyyy-MM-dd hh:mm:ss')
            break;
        case 66:
            jsonData.ttl = '已报销'
            jsonData.name = '财务'
            jsonData.time = new Date(apItem.handleTime).format('yyyy-MM-dd hh:mm:ss')
            break;
        case 67:
            jsonData.ttl = '修改付款方式申请'
            jsonData.name = apItem.userName
            jsonData.time = new Date(apItem.handleTime).format('yyyy-MM-dd hh:mm:ss')
            break;
        case 45:
        case 46:
        case 47:
        case 48:
        case 49:
        case 50:
        case 51:
        case 52:
        case 53:
            break
        default:
    }
    return jsonData
}

// creator: 张旭博，2018-12-29 11:19:28，返回借款列表
function getList(state,cur,totalPage, index) {
    $(".mainCon .list tbody").html("")
    var  loanType = index+1 ;
    $.ajax({
        url: '../loan/ordLoanList.do',
        data: {
            state: state, // 1 -  未完结的借款列表  0 - 已完结的借款列表
            currentPageNo:cur,
            pageSize:totalPage,
            loanType:loanType
        },
        success: function (res) {
            var code = res["code"]
            if(code === '1') {
                var jsonObj = JSON.stringify({"state": state , "index":index});
                var data = res.data
                var list = data.list
                var principal = data.principal        // 本金
                var principalRepayment = data.principalRepayment        // 已还本金

                var pageInfo = data.pagebean
                //设置分页
                var cur     = pageInfo["currentPageNo"],
                    total   = pageInfo["totalPage"] ;
                var listStr =  '';
                if (state === 1) { // 已完结
                    $(".ttl0").show().siblings().hide()
                    if(index === 0){
                        $("#twoName").html("出资方");
                        $("#memo").html("以下各条数据为公司借来的款，本金合计"+ principal +"元，已付出合计"+ principalRepayment +"元。");
                    }else if(index === 1){
                        $("#twoName").html("收款方");
                        $("#memo").html("以下各条数据为公司借出去的款，本金合计"+ principal +"元，已收回合计"+ principalRepayment +"元。");
                    }
                    for (var i in list) {
                        listStr +=  '<tr data-id="'+ list[i].id +'" data-amount="'+list[i].principalAmount+'">' ;
                        if(index === 0){
                            listStr +=  '   <td>' + list[i].lender + '</td>' ;   //出资方
                        }else{
                            listStr +=  '   <td>' + list[i].borrower + '</td>' ;  // 收款方
                        }
                        listStr +=  '   <td>' + list[i].principalAmount + '</td>' +   //本金
                            '   <td>' + ( list[i].nominalRate * 100 ).toFixed(2) + ' %</td>' +   //名义利率
                            '   <td>' + chargeInterestMethod(list[i].interestMethod) + '</td>' +   //利息的支付方式
                            '   <td>' + chargeRepaymentDate(list[i].repaymentDate) + '</td>' +   //归还本金的日期
                            '   <td>' + list[i].createName + " " + formatTime(list[i].createDate, true) + '</td>' +   //创建时间 //创建人
                            '   <td>' +
                            '       <span data-type="generalScan" class="btn ty-color-blue">综合查看</span>' +
                            '       <span data-type="updateBorrowBtn" class="btn ty-color-blue">修改借款信息</span>';
                        if(index === 0){
                            listStr += '<span data-type="ordRepaymentAddBtn" class="btn ty-color-blue">付款录入</span>' +
                                '<span data-type="endBtn" class="btn ty-color-blue">本笔借款不再付款</span>' ;
                        }else {
                            listStr += '<span data-type="ordRepaymentAddBtn" class="btn ty-color-blue">收款录入</span>' +
                                '<span data-type="endBtn" class="btn ty-color-blue">本笔借款不再收款</span>';
                        }
                        listStr += '   </td>'+  '</tr>'
                    }

                } else if (state === 0) { // 未完结
                    $(".ttl1").show().siblings().hide();
                    if(index === 0){
                        $("#twoName2").html("出资方");
                        $("#memo").html("  以下为已完结的公司借来的款，本金合计"+ principal +"元，已付出合计"+ principalRepayment +"元。");
                    }else if(index === 1){
                        $("#twoName2").html("收款方");
                        $("#memo").html("以下为已完结的公司借出去的款，本金合计"+ principal +"元，已收回合计"+ principalRepayment +"元。");
                    }
                    for (var j in list) {
                        listStr +=  '<tr data-id="'+list[j].id+'">' ;
                        if(index === 0){
                            listStr +=  '   <td>' + list[j]['lender'] + '</td>' ;   //出资方
                        }else{
                            listStr +=  '   <td>' + list[j].borrower + '</td>' ;  // 收款方
                        }
                        listStr +=  '   <td>' + list[j].principalAmount + '</td>' +
                            '   <td>' + (list[j].amountRepayed || "")  + '</td>' +
                            '   <td>' +  list[j].endBorrower + " " + formatTime(list[j].endDate, true)  + '</td>' +
                            '   <td>' + list[j].createName + " " + formatTime(list[j].createDate, true) + '</td>' +
                            '   <td>' +
                            '       <span data-type="endGeneralScan" class="btn ty-color-blue">综合查看</span>' ;
                        if(index === 0){
                            listStr +=  '<span data-type="startBtn" class="btn ty-color-blue">本笔借款尚需继续付款</span>';
                        }else{
                            listStr +=  '<span data-type="startBtn" class="btn ty-color-blue">本笔借款尚需继续收款</span>';
                        }
                        listStr +=   '   </td>' +
                            '</tr>'
                    }
                }
                setPage( $("#ye_ordLoan"), cur, total, "ordLoan", jsonObj) ;
                $(".mainCon .list tbody").html(listStr)
            }
        },
        complete:function (xhr) {
            loading.close()
            curMonth = new Date(xhr.getResponseHeader('Date')).format('yyyy-MM');

        }
    })

}
// creator: hxz 2020-04-11 判断付款显示
function chargeFu() {
    var loanType = $("#ordLoanAddEdit [name='loanType']:checked").val();
    var incomeMethod = $("#incomeMethod").val();
    if(incomeMethod == 1 && loanType == 1){
        // var noLoanType = loanType == '1'? '2' : '1' ;
        $("#ordLoanAddEdit .fu2").hide();
        $("#ordLoanAddEdit .fu1").show();
    }else{
        $("#ordLoanAddEdit .fu1").hide();
        $("#ordLoanAddEdit .fu2").show();
    }

}

// updater hxz 2020-04-27 综合查看
function getOrdLoanDetail(param, isFinance) {
    $.ajax({
        url: '/loan/ordLoanDetail.do',
        data: param,
        success: function (data) {
            var data = data["data"]
            $(".incomeMethodCon").hide()
            if(data) {
                var borrorModList = data.borrorModList  // 借款修改列表，包含了修改前和修改后的数据，修改前的数据放在beforeInfo关键字里
                var loanDetail = data.loanDetail        // 借款详情，
                var repaymentList = data.repaymentList  // 付款列表，
                var recordList = data.recordList        // 借款启停用记录列表
                var repaymentListStr = ''
                $("#generalScan").data('id',loanDetail.id)
                $("#generalScan .showHistory").prop('disabled',borrorModList.length === 0)

                var incomeMethod = loanDetail.incomeMethod
                var loanType = loanDetail.loanType
                $(".tansCommon").hide();
                var loanTypeStr = "付款", loanTypeStr2 = "支付" ;
                if(loanType == 2 ) { // 借出
                    loanTypeStr = "收款" ;
                    loanTypeStr2 = "收款" ;

                }

                $("#logttl0").html("已"+ loanTypeStr +"金额");
                $("#logttl1").html(loanTypeStr + "日期");
                $("#logttl2").html(loanTypeStr + "金额");
                $("#logttl3").html(loanTypeStr2 + "方式");

                if(loanType == 2 || (loanType ==1 && incomeMethod == 1)){ // 借出的或者借入现金
                    $("#generalScan .tansCommon").hide();
                    $('#generalScan .trans' + loanDetail.incomeMethod).show() ;

                    $(".incomeMethodCon1").show()
                    if(incomeMethod == 1 && loanType == 1){
                        $(".fu2").hide();
                        $(".fu1").show();
                    }else{
                        $(".fu1").hide();
                        $(".fu2").show();
                        if(incomeMethod == 3){
                            $(".inOrOut" + loanDetail.withinOrAbroad).show();
                        }
                    }

                }else{ // 借入的
                    $(".incomeMethodCon2").show()
                    $('#generalScan .incomeMethodCon2 .trans' + loanDetail.incomeMethod).show();
                    $('#generalScan .incomeMethodCon2 .trans' + loanDetail.incomeMethod).siblings().hide();

                }
                $('#generalScan .interest' + loanDetail.interestMethod).show().siblings().hide();

                for(var key in loanDetail){
                    if(key === 'nominalRate'){loanDetail[key] = (loanDetail[key] * 100).toFixed(2) + '%'}
                    if(key === 'withinOrAbroad'){loanDetail[key] = chargeBillType(loanDetail[key])}
                    if(key === 'incomeMethod'){loanDetail[key] = chargePayMethod(loanDetail[key])}
                    if(key === 'interestMethod'){loanDetail[key] = chargeInterestMethod(loanDetail[key])}
                    if(key.substring(key.length-4, key.length) === 'Date' && key !== 'periodRepaymentDate'){loanDetail[key] = formatTime(loanDetail[key])}
                    if(key === 'repaymentDate' && loanDetail[key] === ''){loanDetail[key] ='未约定具体日期'}
                    var idStr = '#generalScan .' + key+ ''
                    $(idStr).html(loanDetail[key]);
                    // console.log('kkk:', $(idStr).html());

                }

                for(var i in repaymentList){
                    if(isFinance) {
                        var btnStr = '<span class="ty-color-blue" data-type="seeRepayBtn">查看</span>'
                    } else {
                        var btnStr = '<span class="ty-color-blue" data-type="changeRepayBtn">修改</span>'
                    }
                    repaymentListStr += '<tr data-id="'+repaymentList[i].id+'" data-method="'+repaymentList[i].repaymentMethod+'">' +
                        '<td>' + formatTime(repaymentList[i].repaymentTime) + '</td>' + // 付款日期
                        '<td>' + repaymentList[i].repaymentAmount + '</td>' + // 付款金额
                        '<td>' + chargePayMethod(repaymentList[i].repaymentMethod) + '</td>' + // 支付方式
                        '<td>' + repaymentList[i].createName + " " + formatTime(repaymentList[i].createDate, true) +'</td>' + // 录入者
                        '<td>' +  btnStr +
                        '<span class="ty-color-blue" data-type="showHistory">修改记录</span>'+
                        '</td>' + // 操作
                        '</tr>'
                }

                var recordListStr = '<div class="item">' +
                    '<div class="col-md-8"><span class="item-title">创建人</span><span style="width:300px; " class="item-content ">'+recordList[0].createName+ '  ' +formatTime(recordList[0].createDate, true) + '</span></div>' +
                    '<div class="col-md-4"><button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 showHistory" onclick="showHistory(1)" '+(borrorModList.length>0?'':'disabled')+'>借款信息的修改记录</button></div>' +
                    '</div>';
                for(var j=1; j<recordList.length; j++){
                    if (recordList[j].state) {
                        recordListStr +=   '<div class="item"><p style="padding-left:88px;">' + recordList[j].createName + " 于 "+ formatTime(recordList[j].createDate, true) +'将本笔借款由“已完结”恢复至“尚需继续'+ loanTypeStr +'”。</p></div>'


                    }else {
                        recordListStr +=   '<div class="item"><p style="padding-left:88px;">' + recordList[j].createName + " 于 "+ formatTime(recordList[j].createDate, true) +'将本笔借款操作为“已完结”。</p></div>'

                    }
                }
                $("#generalScan .repaymentList tbody").html(repaymentListStr)
                $("#generalScan .sumRepaymentAmount").html(data.sumRepaymentAmount)
                $("#generalScan .recordList").html(recordListStr)


            }
        }
    })
}

// creator: hxz 2023-03-15 修改查询时间
function chonseother(thisObj){
    let year = $("#pointchose").val();
    let month = $("#detailedKey").val();
    let yearMonth = year + month
    console.log('yearMonth=', yearMonth)
    var json = JSON.parse($("#ye_ordLoan .json").html());
    let index = json.index
    let state = json.state
    switch (index){
        case 0:
        case 1:
            break
        case 2:
            advancePaymentPayLoan(state, yearMonth, 1, index)
            break
        case 3:
            getMostDebitLoan(state, yearMonth, 1,index)
            break
        case 4:
            getLoanOverpaymentList(state, yearMonth, 1,index)
            break
    }
}
// creator: 张旭博，2019-01-11 16:49:47，修改借款中设置借款信息
function setOrdLoanDetail(id , repaymentID) {
    $.ajax({
        url: '/loan/ordLoanDetail.do',
        data: { 'id': id, 'repaymentID':repaymentID },
        success: function (data) {
            var data = data["data"]
            if(data) {
                var borrorModList = data.borrorModList  // 借款修改列表，包含了修改前和修改后的数据，修改前的数据放在beforeInfo关键字里
                var loanDetail = data.loanDetail        // 借款详情，
                var repaymentList = data.repaymentList  // 付款列表，
                var recordList = data.recordList        // 借款启停用记录列表

                var repaymentListStr = ''

                var loanType = loanDetail["loanType"] ;
                var incomeMethod = loanDetail["incomeMethod"] ;
                var interestMethod = loanDetail["interestMethod"] ;
                if(incomeMethod == 4) {
                    $('#ordLoanAddEdit #incomeMethod option:eq(3)').show()
                } else {
                    $('#ordLoanAddEdit #incomeMethod option:eq(3)').hide()
                }
                $(".interest" + interestMethod).show().siblings().hide();
                $("#ordLoanAddEdit [name='loanType']").prop("disabled", "disabled");
                $("#ordLoanAddEdit [name='loanType'][value='" + loanType + "']").prop('checked', true).prop("disabled");
                $("#incomeMethod").val(incomeMethod);
                $("#incomeMethod option[value='"+ incomeMethod +"']").prop("selected",true);

                if(loanType == 1){
                    if(incomeMethod == 1){
                        charge1();
                    }else{
                        if(incomeMethod == 5){
                            chooseChequeData(4, $("#bankAccount2"), loanDetail['accountId']);
                        }
                        charge2();
                    }
                    $("#ordLoanAddEdit [name='borrower']").prop("disabled", "disabled");
                    $("#ordLoanAddEdit [name='lender']").removeAttr("disabled");
                }else{ // 出资方
                    charge1();
                    $("#ordLoanAddEdit [name='lender']").prop("disabled", "disabled");
                    $("#ordLoanAddEdit [name='borrower']").removeAttr("disabled");
                    var returnId = loanDetail['returnId'];
                    var billNo = loanDetail['billNo'];
                    if(incomeMethod == "3"){
                        var withinOrAbroad = loanDetail["withinOrAbroad"] ;
                        $("#withinOrAbroad").val(withinOrAbroad);
                        $("#withinOrAbroad option[value='"+ withinOrAbroad +"']").prop("selected",true);

                        if(withinOrAbroad == 0){ // 外部
                            withinOrAbroadChange();
                            $("#ordLoanAddEdit [name='billNo']:visible").data('id', returnId) ;
                        }else if (withinOrAbroad == 1){ // 内部
                            var accountId = loanDetail['accountId'];
                            withinOrAbroadChange(accountId);
                            accountIdChange(accountId ,$("#billNo3"), returnId, billNo);
                        }
                    }else if(incomeMethod == "4"){
                        $("#ordLoanAddEdit [name='billNo']:visible").data('id', returnId) ;
                    }else if(incomeMethod == "5"){
                        var accountId = loanDetail['accountId'];
                        $("#accountId2").val(accountId);
                        $("#accountId2 option[value='"+ accountId +"']").prop("selected",true);
                        chooseChequeData(4 , $("#accountId2"), accountId);
                    }
                }
                $("#repaymentDate").show();
                for(var key in loanDetail){
                    // var name = $(this).attr("name");
                    var ignoreArr = ["loanType","incomeMethod"]
                    if(!ignoreArr.indexOf(key) != -1){
                        if(key === 'nominalRate'){loanDetail[key] = (loanDetail[key] * 100).toFixed(2)}
                        if(key === 'repaymentMethod' || key === 'interestMethod' || key === 'loanType'){$("#ordLoanAddEdit [name='" + key + "'][value='" + loanDetail[key] + "']").prop('checked', true)}
                        if(key.substring(key.length-4, key.length) === 'Date' && key !== 'periodRepaymentDate'){loanDetail[key] = formatTime(loanDetail[key])}
                        $("#ordLoanAddEdit [type!='radio'][name='" + key + "']:visible").val(loanDetail[key])
                    }
                }
                if(loanDetail.returnState && loanDetail.returnState > 1) {
                    $("#ordLoanAddEdit [name='principalAmount']:visible").prop("disabled",true)
                }else{
                    $("#ordLoanAddEdit [name='principalAmount']:visible").prop("disabled",false)
                }
                for(var i in repaymentList){
                    repaymentListStr += '<tr data-repay="'+repaymentList[i]+'" >' +
                        '<td>' + formatTime(repaymentList[i].repaymentTime) + '</td>' + // 付款日期
                        '<td>' + repaymentList[i].repaymentAmount + '</td>' + // 付款金额
                        '<td>' + chargePayMethod(repaymentList[i].repaymentMethod) + '</td>' + // 支付方式
                        '<td>' + formatTime(repaymentList[i].createDate, true) + '</td>' + // 录入时间
                        '<td>' + repaymentList[i].createName + '</td>' + // 录入者
                        '<td>' +
                        '<span class="ty-color-blue" onclick="">查看</span>'+
                        '<span class="ty-color-blue" onclick="repayHistory()">修改记录</span>'+
                        '</td>' + // 操作
                        '</tr>'
                }
                $("#ordLoanAddEdit .repaymentList tbody").html(repaymentListStr)
                if (borrorModList.length > 0) {
                    $("#ordLoanAddEdit").data("lastId", borrorModList[0].id)
                }else {
                    $("#ordLoanAddEdit").removeData("lastId")
                }
                $("#ordLoanAddEdit [name='incomeMethod']").prop('disabled',true)
            }
        }
    })
}

// creator: 张旭博，2019-01-26 11:04:54，修改付款 - 按钮
function changeRepayBtn(el) {
    var id = el.parents("tr").data("id");
    var method = el.parents("tr").data("method");
    bounce_Fixed.show($("#ordRepaymentAdd"));
    $("#ordRepaymentAdd input").val("")
    $("#ordRepaymentAdd select").val("")
    $("#ordRepaymentAdd").data("state",2)
    $("#ordRepaymentAdd").data("id",id)
    $("#ordRepaymentAdd").data("method",method)
    $("#ordRepaymentAdd [name='repaymentMethod']").prop("disabled",false)

    if($("#logttl1").html() == "收款日期"){
        $("#ordRepaymentAdd .bonceHeadName").html('修改收款')
        $("#paytitle").html("收款方式")
        $("#paytitle2").html("收款日期")
        $("#paytitle3").html("收款金额")
    }else{
        $("#ordRepaymentAdd .bonceHeadName").html('修改付款')
        $("#paytitle").html("支付方式")
        $("#paytitle2").html("付款日期")
        $("#paytitle3").html("付款金额")
    }
    setRepayDetail(id)
    bounce.everyTime('0.5s','ordLoanAddEdit',function(){
        var state = 0;
        $("#ordRepaymentAdd").find("[name]:visible").each(function () {
            if($.trim($(this).val()) === ''){
                state ++;
            }
        });
        if($('#ordRepaymentAdd .chooseCheque').val() !== ''&&$('#ordRepaymentAdd .chooseCheque').is(":visible")){
            $("#ordRepaymentAdd [name='repaymentAmount']").prop("disabled",true)
        }else {
            $("#ordRepaymentAdd [name='repaymentAmount']").prop("disabled",false)
        }

        if( state > 0){
            $("#sureOrdRepaymentAdd").prop("disabled",true)
        }else {
            $("#sureOrdRepaymentAdd").prop("disabled",false)
        }
    });
}

// creator: 张旭博，2019-02-22 16:43:07，付款查看 - 按钮
function seeRepayBtn(el) {
    // bounce_Fixed2.show($("#payInfo"))
    $("#payInfo .bonceHeadName").html('查看付款')
    var id = el.parents("tr").data("id")
    $.ajax({
        url: '/loan/ordRepaymentDetail.do',
        data: { id:id },
        success: function (data) {
            var repaymentData = data.data
            if (repaymentData) {
                setRepayInfo22(0 , repaymentData , [])

            }
        }
    })

}

// creator: 张旭博，2019-01-24 14:38:36，设置付款详情
function setRepayDetail(id) {
    $.ajax({
        url: '/loan/ordRepaymentDetail.do',
        data: {  id:id   },
        success: function (data) {
            var repaymentData = data.data
            if (repaymentData) {
                var repaymentMethod = repaymentData['repaymentMethod']
                $("#ordRepaymentAdd [name='repaymentMethod']").val(repaymentMethod).prop("disabled",true);
                var type = $("#ordRepaymentAdd .bonceHeadName").html();
                // repaymentMethodChange();
                var val = repaymentMethod ;
                repaymentData['repaymentTime'] = formatTime(repaymentData['repaymentTime'])
                repaymentData['billReceiveDate'] = formatTime(repaymentData['billReceiveDate'])
                repaymentData['billEndDate'] = formatTime(repaymentData['billEndDate'])
                repaymentData['arriveDate'] = formatTime(repaymentData['arriveDate'])

                if(type == "付款录入" || type == "修改付款"){
                    $(".opt").show().siblings().hide();
                    if (val === '1') {
                        $("#ordRepaymentAdd .opt").hide()
                    } else {
                        $("#ordRepaymentAdd .opt").show()
                        if (val === '3') {
                            //转账支票
                            $("#ordRepaymentAdd .opt3").show().siblings().hide()
                        } else if (val === '4') {
                            //承兑汇票
                            $("#ordRepaymentAdd .opt").children().hide()
                            $("#ordRepaymentAdd .opt4").show()
                            $("#ordRepaymentAdd .chequeName").html('汇票号')
                            $("#ordRepaymentAdd .chooseCheque").attr('placeholder','请选择汇票号')
                            chooseChequeData(3 , $("#acceptanceBills tbody"));
                        } else if (val === '5') {
                            //银行转账
                            $("#ordRepaymentAdd .opt5").show().siblings().hide()
                            // chooseChequeData(4 , $("#bankAccount3"));

                        }
                    }
                }

                if( type == "修改付款"){
                    if (val === '1') {
                        $("#ordRepaymentAdd .opt").hide()
                    } else {
                        $("#ordRepaymentAdd .opt").show()
                        if (val === '3') {
                            //转账支票
                            var billType = repaymentData.billType
                            $("#ordRepaymentAdd .opt3").show().siblings().hide()
                            if(billType === '1') {
                                // 内部支票
                                $("#ordRepaymentAdd .opt").children().hide()
                                $("#ordRepaymentAdd .opt31").show()

                                chooseChequeData(4 , $("#bankAccount"), repaymentData.accountId);
                                accountIdChange(repaymentData.accountId , $("#checkId2"), repaymentData.checkId, repaymentData.billNo)

                            } else if (billType === '0') {
                                // 外部支票
                                $("#ordRepaymentAdd .opt").children().hide()
                                $("#ordRepaymentAdd .opt32").show()
                                $("#ordRepaymentAdd .chequeName").html('支票号')
                                $("#ordRepaymentAdd .chooseCheque").html(repaymentData.billNo)
                                $("#ordRepaymentAdd .chooseCheque").data('id', repaymentData.checkId)
                            }
                        } else if (val === '4') {
                            //承兑汇票
                            $("#ordRepaymentAdd .opt").children().hide()
                            $("#ordRepaymentAdd .opt4").show()
                            $("#ordRepaymentAdd .chequeName").html('汇票号')
                            $("#ordRepaymentAdd .chooseCheque").html(repaymentData.billNo)
                            $("#ordRepaymentAdd .chooseCheque").data('id',repaymentData.checkId)
                        } else if (val === '5') {
                            //银行转账
                            $("#ordRepaymentAdd .opt5").show().siblings().hide()
                            $("#ordRepaymentAdd [name='repaymentMethod']").prop("disabled",true)
                            $("#ordRepaymentAdd .transBank").val(repaymentData.accountId)
                            chooseChequeData(4 , $("#bankAccount3"), repaymentData.accountId);
                        }
                    }
                }else if(type == "修改收款"){
                    repaymentMethodChange();
                    $(".payx").show().children().hide();
                    $(".payx" + val).show();
                    if(val == 5){
                        chooseChequeData(4 , $("#accountId4"), repaymentData.accountId);

                    }
                }

                for (var key in repaymentData) {
                    $("#ordRepaymentAdd [type!='radio'][name='" + key + "']:visible").val(repaymentData[key])
                }
            }
        }
    })

}

// creator: 张旭博，2019-01-08 11:30:39，付款录入 / 修改付款 - 确定
function sureOrdRepaymentAdd() {
    var type = $("#ordRepaymentAdd").data("state")
    var ordRepaymentAdd = {
        'loanType': 1, // 借贷类型:1-付款(默认1),2-收款
        'borrowCommon': 0, // (借款id)
        'repaymentAmount': 0, // (还款金额）
        'repaymentTime': '', // (付款日期)
        'repaymentMethod': '', // (还款方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐)
        'billType': '', // (支票类型，1-内部支票    0-外部支票)
        'accountId': '', // (银行账户ID)
        'checkId': '', // (支票或汇票ID)
        'billNo': '', // (支票或汇票号)
        'billEndDate': '', // (支票到期日)
        'billReceiveDate': '', // (接受日期)
        'receiveOperatorName': '', // (接收经手人)
        'paymentOperatorName': '', // (支付经手人)
    }
    var json = JSON.parse($("#ye_ordLoan .json").html());
    ordRepaymentAdd["loanType"] = Number(json["index"]) + 1
    $("#ordRepaymentAdd [name]:visible").each(function () {
        var name = $(this).attr("name")
        if ($(this).attr("type") === 'radio') {
            ordRepaymentAdd[name] = $("#ordRepaymentAdd [name='"+name+"']:checked").val()
        } else {
            ordRepaymentAdd[name] = $(this).val()
        }
        if ($(this).attr("type") === 'num') {
            ordRepaymentAdd[name] = Number(ordRepaymentAdd[name])
        }
    })
    if(ordRepaymentAdd["loanType"] == 1){

        var method = ordRepaymentAdd.repaymentMethod
        var billType = ordRepaymentAdd.billType
        ordRepaymentAdd.billNo = $("#ordRepaymentAdd .chequeNo option:selected").html()
        if (method === '3') {
            ordRepaymentAdd.accountId   = $("#ordRepaymentAdd .bankAccount").val()
            if (billType === '1') {
                // 内部支票
                ordRepaymentAdd.billNo = $("#ordRepaymentAdd .chequeNo option:selected").html()

            } else {
                ordRepaymentAdd.checkId = $("#ordRepaymentAdd .chooseCheque").data("id")
            }
        } else if (method === '4') {
            ordRepaymentAdd.checkId     = $("#ordRepaymentAdd .chooseCheque").data("id")
        } else if (method === '5') {
            ordRepaymentAdd.accountId   = $("#ordRepaymentAdd .transBank").val()
        }
    }



    var url = ''
    if(type === 1) {
        // 新增时传借款id
        url = '/loan/ordRepaymentAdd.do'
        var borrowCommon = $("#ordRepaymentAdd").data("borrowCommon")
        ordRepaymentAdd.borrowCommon = borrowCommon
    } else {
        // 修改时传付款id
        var url = '/loan/ordRepaymentModify.do'
        var id = $("#ordRepaymentAdd").data("id")
        var methodOld = $("#ordRepaymentAdd").data("method")
        ordRepaymentAdd.id = id
        ordRepaymentAdd.methodOld = methodOld
    }
    // if(Number(ordRepaymentAdd.repaymentAmount) === 0){
    //     layer.msg("付款金额不能为0!")
    //     return false
    // }

    $.ajax({
        url: url,
        data: ordRepaymentAdd,
        success: function (data) {
            var data = data.data
            if (data) {
                if (data == 1) {
                    bounce_Fixed.cancel();
                    if(type === 1) {
                        layer.msg("录入成功")
                    } else {
                        layer.msg("修改成功")
                        var id = $("#ordLoanAddEdit").data('id')
                        getOrdLoanDetail({'id': id})
                    }
                    // 获取支票数据
                    // getChequeInfo()
                } else if (data == 2) {
                    layer.msg("选择的账户被冻结")
                } else if (data == 3) {
                    layer.msg("账户余额不够冲减")
                }
            } else {
                layer.msg("您最多可以录入99999999.99元")
            }
        }
    })
}

// creator: 张旭博，2019-01-26 10:10:27，本笔借款不再付款
function changeRepayState(state) {
    if (state === 0) {
        var id = $("#endPay").data('id')
    } else {
        var id = $("#startPay").data('id')
    }
    $.ajax({
        url: '/loan/updateOrdLoanState.do',
        data: {
            id: id,
            state: state //新增借款时state是1，改成0的话就是不再付款的意思
        },
        success: function (data) {
            bounce.cancel();
            var data = data.data
            if (data) {
                if (data > 0) {
                    layer.msg("操作成功")
                    var jsonObj = $("#ye_ordLoan").children(".json").html();
                    var index = JSON.parse(jsonObj).index ;
                    getList(state,1,15, index)
                }else {
                    layer.msg("操作失败")
                }
            }

        }
    })
}

// creator : hxz 2018-11-30  已完结的常规借款列表
function endList() {
    $(".ty-secondTab li").each(function () {
        const indexNum = $(this).index()
        switch (indexNum){
            case 0:
            case 1:
                $(this).data('state',0)
                break
            case 2:
                $("#boxchoo").show()
                $(this).data('state','')
                break
            case 3:
                $("#boxchoo").show()
                $(this).data('state',1)
                break
            case 4:
                $("#boxchoo").show()
                $(this).data('state',1)
                break
        }
    })

    $(".backCtrl").show().siblings().hide()
    $(".ttl0").show().siblings().hide()
    var json = JSON.parse($("#ye_ordLoan .json").html());
    let index = json.index
    $("#ye_ordLoan .json").html(JSON.stringify(json))
    $(`.ty-secondTab li:eq(${index})`).click();
    let curMonthArr = curMonth.split('-');
    let year = curMonthArr[0]
    let month = curMonthArr[1]
    $("#pointchose").val(year)
    $("#detailedKey").val(month)
}

// creator : hxz 2018-11-30  返回未完结的常规借款列表
function goback() {
    $(".ty-secondTab li").each(function () {
        const indexNum = $(this).index()
        switch (indexNum){
            case 0:
            case 1:
                $(this).data('state',1)
                break
            case 2:
                $(this).data('state',2)
                break
            case 3:
                $(this).data('state',0)
                break
            case 4:
                $(this).data('state',2)
                break
        }
    })
    $(".mainCtrl").show().siblings().hide()
    $(".ttl0").show().siblings().hide()
    var json = JSON.parse($("#ye_ordLoan .json").html());
    let index = json.index
    $("#ye_ordLoan .json").html(JSON.stringify(json))
    $(`.ty-secondTab li:eq(${index})`).click();
}

// creator : hxz 2018-11-30  查看修改记录
function showHistory(status, el) {
    // status: 1- 借款信息修改 ； 2 - 付款记录修改记录
    bounce_Fixed.show($("#updateLog"));
    $("#updateLog").data('status',status)
    $("#logStatus").val(status);
    if (status === 1) {
        var id = $("#generalScan").data('id')
        $.ajax({
            url: '/loan/ordLoanRecordModList.do',
            data: { id: id },
            success: function (data) {
                var history = data.data
                if (history) {
                    var listStr = ''
                    for(var i in history) {
                        listStr +=  '<tr>' +
                            '   <td>'+formatTime(history[i].createDate, true)+'</td>'+
                            '   <td>'+history[i].createName+'</td>'+
                            '   <td class="ty-td-control">' +
                            '       <span class="hd">'+history[i].differents.join('-')+'</span>' +
                            '       <span class="ty-color-blue btn" data-type="1">查看</span>' +
                            '       <span class="hd">'+JSON.stringify(history[i].before)+'</span>' +
                            '   </td>'+ // 修改前
                            '   <td class="ty-td-control">' +
                            '       <span class="hd">'+history[i].differents.join('-')+'</span> ' +
                            '       <span class="ty-color-blue btn" data-type="2">查看</span>' +
                            '       <span class="hd">'+JSON.stringify(history[i].after)+'</span>' +
                            '   </td>'+ //修改后
                            '</tr>'
                    }
                }
                $("#updateLog tbody").html(listStr)
            }
        })
    } else {
        var id = el.parents("tr").data("id")
        $.ajax({
            url: '/loan/ordRecordModList.do',
            data: {
                id: id
            },
            success: function (data) {
                var history = data.data
                if (history) {
                    var listStr = ''
                    for(var i in history) {
                        listStr +=  '<tr>' +
                            '   <td>'+formatTime(history[i].createDate, true)+'</td>'+
                            '   <td>'+history[i].createName+'</td>'+
                            '   <td class="ty-td-control">' +
                            '       <span class="hd">'+history[i].differents.join('-')+'</span>' +
                            '       <span class="ty-color-blue btn" data-type="1">查看</span>' +
                            '       <span class="hd">'+JSON.stringify(history[i].before)+'</span>' +
                            '   </td>'+ // 修改前
                            '   <td class="ty-td-control">' +
                            '       <span class="hd">'+history[i].differents.join('-')+'</span> ' +
                            '       <span class="ty-color-blue btn" data-type="2">查看</span>' +
                            '       <span class="hd">'+JSON.stringify(history[i].after)+'</span>' +
                            '   </td>'+ //修改后
                            '</tr>'
                    }
                }
                $("#updateLog tbody").html(listStr)
            }
        })
    }
}

// creator: 张旭博，2019-01-28 10:23:57，查看借款 - 修改记录 - 按钮
function seeChanges(el) {
    // type 1:修改前 2:修改后
    var type = el.data('type')
    if(type === 1) {
        $("#borrowInfo .bonceHead span").html('修改前')
    } else {
        $("#borrowInfo .bonceHead span").html('修改后')
    }
    var loanDetail = JSON.parse(el.next().html());
    var differents = el.prev().html().split('-');
    setRepayInfo("#borrowInfo", loanDetail , differents);

}
function setRepayInfo(objStr , loanDetail , differents){
    bounce_Fixed2.show($(objStr));

    var incomeMethod = loanDetail.incomeMethod;
    var loanType = loanDetail.loanType
    $(objStr +" .tansCommon").hide();

    if(loanType == 2 || (loanType ==1 && incomeMethod == 1)){ // 借出的或者借入现金
        $(objStr + " .tansCommon").hide();
        $(objStr + ' .trans' + loanDetail.incomeMethod).show() ;

        $(objStr + ".incomeMethodCon1").show()
        if(incomeMethod == 1 && loanType == 1){
            $(objStr + " .fu2").hide();
            $(objStr + " .fu1").show();
        }else{
            if(incomeMethod == 3){
                $(objStr + " .inOrOut" + loanDetail.withinOrAbroad).show()
            }
            $(objStr + " .fu1").hide();
            $(objStr + " .fu2").show();
        }

    }else{ // 借入的
        $(objStr + ".incomeMethodCon2").show()
        $(objStr + ' .incomeMethodCon2 .trans' + loanDetail.incomeMethod).show();
        $(objStr + ' .incomeMethodCon2 .trans' + loanDetail.incomeMethod).siblings().hide();


    }
    $(objStr + ' .interest' + loanDetail.interestMethod).show().siblings().hide();
    for(var key in loanDetail){
        if(key === 'nominalRate'){loanDetail[key] = (loanDetail[key] * 100).toFixed(2) + '%'}
        if(key === 'withinOrAbroad'){loanDetail[key] = chargeBillType(loanDetail[key])}
        if(key === 'incomeMethod'){loanDetail[key] = chargePayMethod(loanDetail[key])}
        if(key === 'interestMethod'){loanDetail[key] = chargeInterestMethod(loanDetail[key])}
        if(key.substring(key.length-4, key.length) === 'Date' && key !== 'periodRepaymentDate'){loanDetail[key] = formatTime(loanDetail[key])}
        if(key === 'repaymentDate' && loanDetail[key] === ''){loanDetail[key] ='未约定具体日期'}
        $(objStr + " ."+ key + ":visible").html(loanDetail[key])
    }
    for (var key in differents) {
        if(differents[key] == "accountId"){
            differents[key] = "accountBank"
        }
        var idStr = objStr + " ."+differents[key]+ ":visible"
        var oral = $(idStr).html() ;
        $(idStr).html('<span class="ty-color-red">'+ oral +'</span>')
    }
}
// creator: 张旭博，2019-01-25 09:08:59，获得某条付款的修改详情
function getRepayHistoryDetail(el) {
    // type 1:修改前 2:修改后
    var type = el.data('type')
    var repaymentData = JSON.parse(el.next().html())
    var differents = el.prev().html().split('-')
    if(type === 1) {
        $("#payInfo2 .bonceHead span").html('修改前')
    } else {
        $("#payInfo2 .bonceHead span").html('修改后')
    }
    setRepayInfo22(1, repaymentData , differents)

}
function setRepayInfo22(seeType , repaymentData , differents){
    var seeCon = "",val = "",billType="";
    var sta = $("#logttl1").html();

    val = repaymentData['repaymentMethod'];
    if(seeType === 1){ // 修改记录的查看
        seeCon = "#payInfo2"
        billType = repaymentData.withinOrAbroad ;
        repaymentData['withinOrAbroad'] = chargeBillType(repaymentData['withinOrAbroad'])

    }else { // 查看收付款
        seeCon = "#payInfo"
        billType = repaymentData.billType ;
        repaymentData['billType'] = chargeBillType(repaymentData['billType'])
        if(sta == "收款日期") { // 收款
            $(seeCon + " .bonceHeadName").html("收款查看")
        }else{
            $(seeCon + " .bonceHeadName").html("付款查看")
        }
    }
    if(sta == "收款日期") { // 收款
        $(seeCon +" .payInfoTtl1").html("收款金额");
        $(seeCon +" .payInfoTtl2").html("收款日期");
        $(seeCon +" .payInfoTtl3").html("收款方式");
    }else{
        $(seeCon +" .payInfoTtl1").html("付款金额");
        $(seeCon +" .payInfoTtl2").html("付款日期");
        $(seeCon +" .payInfoTtl3").html("支付方式");
    }
    bounce_Fixed2.show($(seeCon));

    repaymentData['repaymentTime'] = formatTime(repaymentData['repaymentTime'])
    repaymentData['receiveDate'] = formatTime(repaymentData['receiveDate'])
    repaymentData['billEndDate'] = formatTime(repaymentData['billEndDate'])
    repaymentData['billReceiveDate'] = formatTime(repaymentData['billReceiveDate'])
    repaymentData['expireDate'] = formatTime(repaymentData['expireDate'])
    repaymentData['arriveDate'] = formatTime(repaymentData['arriveDate'])


    if(sta == "收款日期"){ // 收款
        $(seeCon +" .payx").show().siblings().hide();
        $(seeCon +" .payx" + val ).show().siblings().hide();
        if(val == 4){
            $(seeCon + " .pay1").html("收到汇票日期")
            $(seeCon + " .pay2").html("汇票号")
            $(seeCon + " .pay3").html("汇票到期日")
            $(seeCon + " .pay4").html("原始出具汇票单位")
            $(seeCon + " .pay5").html("出具汇票银行")
        }else if(val == 3){
            $(seeCon + " .pay1").html("收到支票日期")
            $(seeCon + " .pay2").html("支票号")
            $(seeCon + " .pay3").html("支票到期日")
            $(seeCon + " .pay4").html("出具支票单位")
            $(seeCon + " .pay5").html("出具支票银行")
        }
        for (var key in repaymentData) {
            if(key === 'repaymentMethod' ){
                repaymentData[key] = chargePayMethod(repaymentData[key])
            }
            $(seeCon +" ." +key + ":visible").html(repaymentData[key])
        }
        for (var key in differents) {
            if(differents[key]){
                $(seeCon +" ."+differents[key] + ":visible").html('<span class="ty-color-red">'+$(seeCon +" ."+differents[key]+ ":visible").html()+'</span>')
                if(differents[key] === 'receiveBank') {
                    $(seeCon +" .receiveBank:visible").html('<span class="ty-color-red">'+$(seeCon +" .receiveBank:visible").html()+'</span>')
                }
            }
        }
    }else{ // 付款
        loading.close();
        $(seeCon + " .payx").hide().siblings().show();
        if (val === '1') {
            $(seeCon + " .opt").hide()
        } else {
            $(seeCon + " .opt").show()
            if (val === '3') {
                //转账支票
                $(seeCon + " .opt3").show().siblings().hide()
                if(billType === '1' || billType === '内部支票') {
                    // 内部支票
                    $(seeCon + " .opt").children().hide()
                    $(seeCon + " .opt31").show()
                    $(seeCon + " .bankAccount").html((repaymentData.accountBank || repaymentData.billBank) + ' ' + repaymentData.account)
                } else if (billType === '0' || billType === '外部支票') {
                    // 外部支票
                    $(seeCon + " .opt").children().hide()
                    $(seeCon + " .opt32").show()
                    $(seeCon + " .chequeName").html('支票号')
                }
            } else if (val === '4') {
                //承兑汇票
                $(seeCon + " .opt").children().hide()
                $(seeCon + " .opt4").show()
                $(seeCon + " .chequeName").html('汇票号')
            } else if (val === '5') {
                //银行转账
                $(seeCon + " .opt5").show().siblings().hide()
                $(seeCon + " .transBank").html(repaymentData.receiveBank)
            }
        }
        repaymentData['repaymentMethod'] = chargePayMethod(repaymentData['repaymentMethod'])

        for (var key in repaymentData) {
            $(seeCon + " ." + key ).html(repaymentData[key])
        }
        for (var key in differents) {
            if(differents[key]){
                if(differents[key] == "accountId"){   differents[key] = "bankAccount";   }
                if(differents[key] == "withinOrAbroad"){
                    $(seeCon + " .expireDate:visible").html('<span class="ty-color-red">'+$(seeCon + " .expireDate").html()+'</span>')
                    $(seeCon + " .receiveDate:visible").html('<span class="ty-color-red">'+$(seeCon + " .receiveDate").html()+'</span>')

                }
                $(seeCon + " ."+ differents[key] + ":visible").html('<span class="ty-color-red">'+$(seeCon + " ."+differents[key]).html()+'</span>')

            }
        }
    }

}
// creator: 张旭博，2019-01-16 09:15:09，确定选择支票
function sureChooseCheque(el) {
    var dialog = el.parents('.bonceContainer')
    var selectRow = dialog.find('input[name]:checked').parents('tr')
    var id = selectRow.data("id")
    var returnNo = selectRow.data("no")
    var amount = selectRow.data("amount")
    zhiElem.children(".chooseCheque").val(returnNo)
    zhiElem.children(".chooseCheque").data('id', id)
    if($("#ordLoanAddEdit").is(":visible") ){
        $("#ordLoanAddEdit [name='principalAmount']").val(amount)
    }else{
        $("#ordRepaymentAdd [name='repaymentAmount']").val(amount)
    }
    bounce_Fixed2.cancel()
}

//----------------- 获取数据 ------------------//

// creator: 张旭博，2019-01-08 11:40:38，付款录入中获取相应的票据或账户信息
function getChequeInfo(type, addData) {
    /* $.ajax({
         url: '/loan/chooseChequeOrBank.do',
         data: { 'oid':sphdSocket.user.oid },
         success: function (data) {
             if (data) {
                 var financeReturns = data.financeReturns
                 var acceptanceBills = data.acceptanceBills
                 var financeAccounts = data.financeAccounts
                 var financeReturnsStr = '';
                 var acceptanceBillsStr = '';
                 var financeAccountsStr = '<option value="">---请选择---</option>';
                 //转账支票的外部支票列表
                 if(type === 'financeReturns') {
                     financeReturns.push(addData)
                 }else if (type === 'acceptanceBills'){
                     acceptanceBills.push(addData)
                 }else if(type === 'financeAccounts'){
                     financeAccounts.push(addData)
                 }
                 for (var i in financeReturns) {
                     financeReturnsStr +=    '<tr data-id="'+ financeReturns[i].id +'" data-no="'+ financeReturns[i].returnNo +'" data-amount="'+ financeReturns[i].amount +'">' +
                         '<td><input type="radio" name="financeReturns"></td>'+
                         '<td>'+ financeReturns[i].returnNo +'</td>'+
                         '<td>'+ financeReturns[i].amount +'</td>'+
                         '<td>'+ financeReturns[i].bankName +'</td>'+
                         '<td>'+ financeReturns[i].expireDate +'</td>'+
                         '</tr>'
                 }
                 //承兑汇票列表信息
                 for (var j in acceptanceBills) {
                     acceptanceBillsStr +=    '<tr data-id="'+ acceptanceBills[j].id +'" data-no="'+ acceptanceBills[j].returnNo +'" data-amount="'+ acceptanceBills[j].amount +'">' +
                         '<td><input type="radio" name="acceptanceBills"></td>'+
                         '<td>'+ acceptanceBills[j].receiveDate +'</td>'+ // 收到汇票日期
                         '<td>'+ acceptanceBills[j].payer	 +'</td>'+ //付款单位
                         '<td>'+ acceptanceBills[j].amount +'</td>'+ //金额
                         '<td>'+ acceptanceBills[j].returnNo +'</td>'+ //汇票号
                         '<td>'+ acceptanceBills[j].expireDate +'</td>'+ // 汇票到期日
                         '<td>'+ acceptanceBills[j].originalCorp +'</td>'+ // 原始出具汇票单位
                         '<td>'+ acceptanceBills[j].bankName +'</td>'+ // 汇票银行
                         '</tr>'
                 }
                 //账户信息列表
                 for (var k in financeAccounts) {
                     financeAccountsStr +=    '<option value="'+ financeAccounts[k].id +'">'+ financeAccounts[k].bankName + ' ' + financeAccounts[k].account +'</option>'

                 }
                 $("#financeReturns tbody").html(financeReturnsStr)
                 $("#acceptanceBills tbody").html(acceptanceBillsStr)
                 $(".bank").html(financeAccountsStr)
                 $("#accountId1").html(financeAccountsStr)
             }
         }
     })*/
}
// updator: hxz 2020-04-03 ，付款录入中获取相应的票据或账户信息
function chooseChequeData(type , thisObj , bankID) {
    //type： 1-转账支票的内部支票 2-转账支票的外部支票 3-承兑汇票 4-银行转账
    $.ajax({
        // url: '../data/chooseCheque.do',
        url: '../loan/chooseChequeOrBank.do' ,
        data: { type : type,  'oid':sphdSocket.user.oid  },
        success: function (data) {
            if(type == 2){ //  2-转账支票的外部支票
                var financeReturns = data['financeReturns'], financeReturnsStr = "";
                for (var i in financeReturns) {
                    financeReturnsStr +=    '<tr data-id="'+ financeReturns[i].id +'" data-no="'+ financeReturns[i].returnNo +'" data-amount="'+ financeReturns[i].amount +'">' +
                        '<td><input type="radio" name="financeReturns"></td>'+
                        '<td>'+ financeReturns[i].returnNo +'</td>'+
                        '<td>'+ financeReturns[i].amount +'</td>'+
                        '<td>'+ financeReturns[i].bankName +'</td>'+
                        '<td>'+ new Date(financeReturns[i].expireDate).format('yyyy-MM-dd') +'</td>'+
                        '</tr>'
                }
                $("#financeReturns tbody").html(financeReturnsStr);
                return false;
            }
            if(type == 3){ // 3-承兑汇票
                var acceptanceBills = data['acceptanceBills'], acceptanceBillsStr = "";
                for (var j in acceptanceBills) {
                    acceptanceBillsStr +=    '<tr data-id="'+ acceptanceBills[j].id +'" data-no="'+ acceptanceBills[j].returnNo +'" data-amount="'+ acceptanceBills[j].amount +'">' +
                        '<td><input type="radio" name="acceptanceBills"></td>'+
                        '<td>'+ new Date(acceptanceBills[j].receiveDate).format('yyyy-MM-dd') +'</td>'+ // 收到汇票日期
                        '<td>'+ acceptanceBills[j].payer	 +'</td>'+ //付款单位
                        '<td>'+ acceptanceBills[j].amount +'</td>'+ //金额
                        '<td>'+ acceptanceBills[j].returnNo +'</td>'+ //汇票号
                        '<td>'+ new Date(acceptanceBills[j].expireDate).format('yyyy-MM-dd') +'</td>'+ // 汇票到期日
                        '<td>'+ acceptanceBills[j].originalCorp +'</td>'+ // 原始出具汇票单位
                        '<td>'+ acceptanceBills[j].bankName +'</td>'+ // 汇票银行
                        '</tr>'
                }
                $("#acceptanceBills tbody").html(acceptanceBillsStr)

                return false;
            }
            if(type == 4){
                var list = data['financeAccounts'];
                var str = "<option value=''> ---- 请选择 ---</option>" ;
                if(list && list.length>0){
                    for(var i = 0 ; i < list.length ; i++){
                        let bankNameStr = list[i]["name"] + ' ' + formatAccount(list[i]["account"])+ ' ' + list[i]["bankName"] ;
                        if(list[i].isPublic == 1){
                            bankNameStr = formatAccount(list[i]["account"])+ ' ' + list[i]["bankName"] ;
                        }

                        if(bankID == list[i]['id']){
                            str += "<option selected value='"+ list[i]['id'] +"'>"+ bankNameStr +"</option>" ;
                        }else{
                            str += "<option value='"+ list[i]['id'] +"'>"+ bankNameStr +"</option>" ;
                        }
                    }
                }
                thisObj.html(str);
            }
        }
    })
}
// create:hxz 2022-03-07 格式化 账号
function formatAccount(account){
    let accountStr = `****`
    if(account.length >3){
        accountStr += account.substr(account.length-4,4)
    }else{
        let n = 4-account.length ;
        for(let i = 0 ; i < n; n++){
            accountStr += '*'
        }
        accountStr += account
    }
    return accountStr
}

function accountIdChange(accountId , thisObj, selectedID, billNo) {
    //type： 1-转账支票的内部支票 2-转账支票的外部支票 3-承兑汇票 4-银行转账
    $.ajax({
        url: '../data/getChequeByAccountId.do',
        data: { 'accountId' : accountId },
        success: function (data) {
            var list = data['financeChequeDetails'];
            var str = "<option> ---- 请选择 ---</option>" ;
            if(selectedID){
                str += "<option selected value='"+ selectedID +"'>"+ billNo +"</option>" ;
            }
            if(list && list.length>0){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<option value='"+ list[i]['id'] +"'>"+ list[i]['chequeNo'] +"</option>" ;
                }
            }
            thisObj.html(str);
        }
    })
}
var zhiElem = null ;
function chooseCheque(el) {
    zhiElem = el ;
    if(el.prev().html() === '支票号') {
        bounce_Fixed2.show($("#financeReturns"))
    } else {
        bounce_Fixed2.show($("#acceptanceBills"))
    }
}

// creator: 张旭博，2019-01-08 15:48:24，获取内部支票信息
function getChequeByAccountId(accountId, addArr) {
    $.ajax({
        url: '/data/getChequeByAccountId.do',
        data: {
            accountId: accountId //账户id
        },
        success: function (data) {
            // financeChequeDetails（返回值格式为List<FinanceChequeDetail>）：内部支票信息{id：支票号/汇票号returnNo：支票号码/汇票号码}
            var financeChequeDetails = data["financeChequeDetails"]

            if (financeChequeDetails) {
                var listStr = addArr?'<option value="'+ addArr.id +'">'+ addArr.chequeNo +'</option>':''
                for (var i in financeChequeDetails) {
                    listStr +=  '<option value="'+ financeChequeDetails[i].id +'">'+ financeChequeDetails[i].chequeNo +'</option>'
                }
            }
            $("#ordRepaymentAdd .chequeNo").html(listStr)
        }
    })
}

//creator:lyt 2023/6/29  上午 8:08 收款时多收来的款
function getLoanOverpaymentList(state, yearMonth,cur,index){
    if(state === 1){ // 已完结
        $(".more4").show().siblings().hide()
    }else{
        $(".more3").show().siblings().hide()
    }
    $.ajax({
        'url':'../overpayment/getLoanOverpaymentList.do',
        'data':{
            'finishType':state, // 完成状态 1-完结 2-未完结
            'yearMonth':yearMonth, // String yearMonth：年月(yyyyMM)
            'pageSize':20,
            'currentPageNo':cur
        },
        success:function(res){
            let list = res.data.loanBizList || []
            let pageInfo = res.data.pageInfo
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"] ;
            var jsonObj = JSON.stringify({"state": state , "index":index, 'time':yearMonth });
            setPage( $("#ye_ordLoan"), cur, total, "overpaymentPayLoan", jsonObj) ;

            let str = ``
            let sum = 0
            list.forEach((item)=>{
                sum += Number(item.amount)
                str += `
                    <tr>
                        <td>${ item.supplierName || '' }</td>
                        <td>${ item.amount.toFixed(2) || ''  }</td>
                        <td>${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')  } ${ state === 1 ? `/ ${ new Date(item.finishTime).format('yyyy-MM-dd hh:mm:ss') }`:`` } </td>
                        <td>
                            <span class="ty-color-gray">查看详情</span>
                            <span class="ty-color-gray">付款记录</span>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </td>
                    </tr>
                `
            })
            $(".mainCon .list tbody").html(str);

            if(state === 1){
                $("#memo").html(`收到承兑汇票或转账支票时，有时会多收。多收的款项需处理。<br>以下为已完结的数据，此处数据仅供查看。`);

            }else{
                $("#memo").html(`以下为收到承兑汇票或转账支票时我公司多收来的款，合计${ sum.toFixed(2) }元。<br>
                此处数据仅供查看。某笔多收来的款实际处理完成后，请及时在系统的“处理-多收来的款”中操作，该条数据即不再在此展示。`);
            }
        }
    })
}
// creator: 张旭博，2019-01-10 09:44:49，转换 支付方式
function chargePayMethod(type) {
    // 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
    switch (type) {
        case '1':
            return '现金';
            break;
        case '2':
            return '现金支票';
            break;
        case '3':
            return '转账支票';
            break;
        case '4':
            return '承兑汇票';
            break;
        case '5':
            return '银行转账';
            break;
    }
}

function chargeInterestMethod (type) {
    //利息的支付方式：0-无利息,1-按日付,2-按月付,3-按年付
    switch (type) {
        case '0':
            return '无利息';
            break;
        case '1':
            return '按日付';
            break;
        case '2':
            return '按月付';
            break;
        case '3':
            return '按年付';
            break;
    }
}

function chargeBillType(type) {
    // 1-内部支票 0-外部支票
    switch (type) {
        case '1':
            return '内部支票';
            break;
        case '0':
            return '外部支票';
            break;
    }
}

function chargeRepaymentDate(date) {
    // 1-内部支票 0-外部支票
    if (date === null) {
        return '未约定具体日期'
    } else {
        return formatTime(date)
    }
}



laydate.render({elem: '#billReceiveDate', festival: true});
laydate.render({elem: '#billEndDate', festival: true});
laydate.render({elem: '#arriveDate', festival: true});
laydate.render({elem: '#interestAccrualDate', festival: true});
laydate.render({elem: '#repaymentTime', festival: true});
laydate.render({elem: '#billEndDate', festival: true});
laydate.render({elem: '#billEndDate2', festival: true});
laydate.render({elem: '#billReceiveDate', festival: true});
laydate.render({elem: '#billReceiveDate2', festival: true});
laydate.render({elem: '#paymentDate', festival: true});
laydate.render({elem: '#paymentDate2', festival: true});
laydate.render({elem: '#paymentDate6', festival: true});
laydate.render({elem: '#paymentDate5', festival: true});
laydate.render({elem: '#paymentDate3', festival: true});
laydate.render({elem: '#billEndDate3', festival: true});
laydate.render({elem: '#day1', festival: true});
laydate.render({elem: '#day2', festival: true});
laydate.render({elem: '#day3', festival: true});
laydate.render({elem: '#day4', festival: true});