$(function (){
    getOrgSonOrgs();
})
/* creator：李玉婷，2022-08-17 09:22:11，主页列表 */
function getOrgSonOrgs(){
    $.ajax({
        url:"../sonOrg/getSeeUsersByOid.do",
        success:function(data){
            let list = data.data;
            let html = ``;
            for(let y=0;y<list.length;y++){
                html += `<tr><td>${list[y].oidName}</td>
                    <td><span class="ty-color-blue" onclick="changeManager(${list[y].userID})">进入</span></td></tr>`;
            }
            $(".mainCon1 table tbody").html(html);
        }
    })
}