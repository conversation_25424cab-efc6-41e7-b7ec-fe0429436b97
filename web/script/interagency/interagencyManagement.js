$(function () {
    showMainCon(1);
    getIndexInteraGency(1, 20);
    $('body').on('click', '.funBtn', function(){
        var type = $(this).data('type');
        let info = ``;
        let _this = $(this);
        switch (type) {
            case 'guide' :
                bounce.show($("#moreGuide"));
                break;
            case 'operationLog' : //
                showMainCon(2);
                orgsOptionHistories();
                break;
            case 'backMain' : //
                let road = $(this).data("road");
                showMainCon(road);
                break;
        }
    })
    $("table").on('click', 'span[type="btn"]', function(){
        var type = $(this).data('type');
        let info = ``;
        let _this = $(this);
        switch (type) {
            case 'modAccessibleOrgan' :
                bounce.show($("#modAccessibleOrgan"));
                break;
            case 'belongOrgan' :
                showMainCon(2);
                break;
            case 'dayLog' :
                $("#dayLog_ye").data("obj", $(this))
                orgsOptionDayHistories(1, 20);
                break;
            case 'dayLogScanEB' :
                scanDayHistories($(this),$(this).data("align"), 1);
                break;
            case 'dayLogScanEA' ://after
                scanDayHistories($(this),$(this).data("align"), 2);
                break;
            case 'editAddressable' :
                info = JSON.parse($(this).siblings(".hd").html());
                $("#modAccessibleOrgan .employeeInfo").html(info.userName+ ' ' + info.mobile);
                seeOrgs(info.userID);
                break;
        }
    });
    $("#modAccessibleOrgan .organs").on('click', 'li', function(){
        if ($(this).find(".fa-circle-o").is(":visible")){
            $(this).find(".fa").removeClass("fa-circle-o").addClass("fa-circle");
        } else {
            $(this).find(".fa").removeClass("fa-circle").addClass("fa-circle-o");
        }
    });
})
/* creator：李玉婷，2022-08-02 17:26:51，跨机构管理主页面数据 */
function getIndexInteraGency(currPage, pageSize) {
    $(".mainCon1 tbody tr:gt(0)").remove();
    $(".mainCon1 tbody tr:eq(0) td:gt(0)").remove();
    let headStr = `<td>修改所属机构</td><td>修改可访问机构</td>`;
    $.ajax({
        url:"../sonOrg/spanOrgManage.do",
        data:{
            "currentPageNo":currPage,
            "pageSize":pageSize
        },
        success:function(data){
            if (data.data) {
                let html = ``, orgName = ``;
                let list = data.data.userList;
                let orgList = data.data.organizationList;
                var cur = data.data.pageInfo["currentPageNo"];
                var totalPage = data.data.pageInfo["totalPage"];
                $("#ye_inter").show();
                setPage( $("#ye_inter") , cur ,  totalPage , "orgManage" );
                for(let a = 0; a < orgList.length; a++) {
                    orgName += `<td>${orgList[a].name}</td>`;
                }
                headStr = orgName + `<td>修改所属机构</td><td>修改可访问机构</td>`;
                for(let i = 0; i < list.length; i++) {
                    html +=
                        `<tr><td>${list[i].userName} ${list[i].mobile}</td>`;
                    let belong = list[i].seeOrgsList;
                    for(let y=0;y<orgList.length;y++){
                        let flag = ``, gray = ``;
                        let index = belong.findIndex(item => item.orgName === orgList[y].name);
                        if (index !== -1) {
                            if (belong[index].from) gray = `gray`;
                            if (belong[index].select) flag = `√`;
                        }
                        html +=`<td class="${gray}">${flag}</td>`;
                    }
                    html += `<td><span class="ty-color-gray">修改</span></td>
                         <td><span class="ty-color-blue" type="btn" data-type="editAddressable">修改</span><span class="hd">${JSON.stringify(list[i])}</span></td></tr>`;
                }
                $(".mainCon1 tbody").append(html);
            }
            $(".mainCon1 tbody tr:eq(0)").append(headStr);
        }
    })
}
/* creator：李玉婷，2022-08-02 09:22:11，查看某个职工可访问的子机构列表 */
function seeOrgs(userId) {
    $("#modAccessibleOrgan").data("passiveUserId", userId);
    $.ajax({
        url:"../sonOrg/getUserSeeOrgs.do",
        data:{
            "passiveUserId":userId
        },
        success:function(data){
            let list = data.data;
            let html = ``;
            for(let y=0;y<list.length;y++){
                list[y].userID = userId;
                html += `<li> <i class="fa ${list[y].select? 'fa-circle':'fa-circle-o'}" data-id="${list[y].oid}"></i>${list[y].orgName}</li>`;
            }
            $("#modAccessibleOrgan .organs").html(html);
            bounce.show($("#modAccessibleOrgan"));
        }
    })
}
function modAccessibleOrganSure(){
    let  oids = []
    $("#modAccessibleOrgan .organs .fa-circle").each(function(){
        oids.push($(this).data("id"));
    })
    oids = oids.join(',');
    $.ajax({
        url:"../sonOrg/updateUserSeeOrgs.do",
        data:{
            "oids": oids,
            "passiveUserId": $("#modAccessibleOrgan").data("passiveUserId")
        },
        success:function(data){
            getIndexInteraGency(1, 20);
            bounce.cancel();
        }
    })
}
/* creator：李玉婷，2022-08-04 09:22:11，操作日志 */
function orgsOptionHistories(month) {
    $("#opertionLog tbody tr:gt(0)").remove();
    $.ajax({
        url:"../sonOrg/getSeeOrgMonthHistories.do",
        data:{
            "yearMonth": month
        },
        success:function(data, status, xhr){
            let list = data.data;
            let firstDay = new Date(list[0].day);  //获得month的1号
            let firstDayWeekday = firstDay.getDay();
            let html = ``;
            var todaySys = new Date(xhr.getResponseHeader('Date'));
            let todayWeekDay = todaySys.getDay()-1;
            var weekDay = ["一", "二", "三", "四", "五", "六","日"];
            let before = firstDayWeekday-1;
            let end = list.length + firstDayWeekday -1;
            $("#queryMonth").html(new Date(list[0].day).format('yyyy年MM月'));
            $("#todayCon").html("今天是" + todaySys.format('yyyy年MM月dd日') + "星期" + weekDay[todayWeekDay]);
            for(let i = 0; i < 5; i++){
                html += `<tr>`;
                for(let g = i * 7; g < 7*(i+1); g++){
                    if (g < before || g >= end) {
                        html += `<td></td>`;
                    } else {
                        let mark = g-firstDayWeekday + 1;
                        let str = mark +1;
                        if(list[mark].number > 0) str = `<span class="red" type="btn" data-type="dayLog" data-date="${list[mark].day}"> ${mark+1}</span>`;
                        html += `<td>${str}</td>`;
                    }
                }
                html += `</tr>`;
            }
            $("#opertionLog tbody").append(html);
        }
    })
}
/* creator：李玉婷，2022-08-04 09:52:01，操作日志-查询 */
function getSearchKey(obj) {
    let month = obj.prev("input").val();
    orgsOptionHistories(month);
}
/* creator：李玉婷，2022-08-04 10:22:14，点击查看日历某日的操作历史记录接口 */
function orgsOptionDayHistories(currPage  , pageSize) {
    let obj = $("#dayLog_ye").data("obj");
    showMainCon(3);
    let day = new Date(obj.data("date"));
    $(".mainCon3 .dateInfo").html(day.format('yyyy年MM月dd日'));
    $("#opertionDayLog tbody tr:gt(0)").remove();
    $.ajax({
        url:"../sonOrg/getSeeOrgDayHistories.do",
        data:{
            "yearMonthDay": day.format('yyyy-MM-dd'),
            "currentPageNo":currPage,
            "pageSize":pageSize
        },
        success:function(data){
            let list = data.data.userOrgLogList;
            let html = ``;
            //分页
            let pageInfo = data.data.pageInfo;
            var totalPage = pageInfo["totalPage"];
            setPage( $("#dayLog_ye") , currPage ,  totalPage , "opertionDayLog" );
            for(let i = 0; i < list.length; i++){
                html += `<tr>
                            <td>${list[i].userName} ${list[i].mobile}</td>
                            <td>${list[i].type === '1'? '修改所属机构':'修改可访问机构'}</td>
                            <td>${list[i].createName} ${new Date(list[i].createTime).format('yyyy-MM-dd hh:mm:ss')}</td>
                            <td class="ty-td-control"><span class="ty-color-blue" type="btn" data-type="dayLogScanEB" data-align="${list[i].type}">查看</span><span class="hd">${list[i].beforeJson}</span></td>
                            <td><span class="ty-color-blue" type="btn" data-type="dayLogScanEA" data-align="${list[i].type}">查看</span><span class="hd">${list[i].afterJson}</span></td>
                        </tr>`;
            }
            $("#opertionDayLog tbody").append(html);
        }
    })
}
/* creator：李玉婷，2022-08-02 09:22:11，查看某个职工可访问的子机构列表 */
function scanDayHistories(obj, type, source) {
    let list = JSON.parse(obj.siblings(".hd").html());
    let html = ``;
    let detail = obj.parents("tr").children().eq(0).html();
    if(type === 1) {//修改所属机构
        if (source === 1) {//修改前
        } else{

        }
        $("#modbelongOrganScan .employeeInfo").html(detail);
        bounce.show($("#modbelongOrganScan"));
    } else {
        if (source === 1) {//修改前
            $("#modAccessOrganScan .scanType").html("修改前");
        } else{
            $("#modAccessOrganScan .scanType").html("修改后");
        }
        for(let i = 0; i < list.length; i++){
            html += `<li> <i class="fa ${list[i].select? 'fa-circle': 'fa-circle-o'}"></i>${list[i].orgName}</li>`;
        }
        $("#modAccessOrganScan .employeeInfo").html(detail);
        $("#modAccessOrganScan .organs").html(html);
        bounce.show($("#modAccessOrganScan"));
    }
}






// create :hxz 2021-1-11  显示页面
function showMainCon(num){
    $("#showMainConNum").val(num)
    $(".mainCon" + num).show().siblings().hide();
}



laydate.render({elem: '#otherMonth', type: 'month'});
