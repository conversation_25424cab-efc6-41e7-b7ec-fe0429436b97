var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#unfilledTip"));
bounce_Fixed2.cancel();
var gblObj = null;
$(function () {
    //initKendoUpload();
    $(".clearInputVal").on({
        "mousedown": function () {
            $(this).prev().val("");
        },
        "mouseup": function () {
            if ($(this).siblings(".modItemTtl").find(".lenTip").length > 0) {
                $(this).siblings(".modItemTtl").find(".lenTip").html("0/100");
            }
            $(this).prev().get(0).focus();
        }
    });
    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
    $("#video-box").on("click", '', function () {
        $("#video-box video")[0].pause();
        $("#video-box").fadeOut("fast");
    });
    $(".invoice1").on("input", 'input', function () {
        var val = Number($(this).val());
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val) && typeof val === 'number' ) {
            var attrName = $(this).attr("name");
            var rate = $(this).parents(".invoice1").find("select").val();
            if (attrName == "unitPriceNotax") {
                if (rate != "" && $(this).val() != "") {
                    var haveTax = val * (1 + rate/100);
                    $(this).parents(".invoice1").find("input").eq(1).prop("disabled", true);
                    $(this).parents(".invoice1").find("input").eq(1).val(parseFloat(Number(haveTax).toFixed(10)));
                } else {
                    if ($(this).val() == "") {
                        $(this).parents(".invoice1").find("input").eq(1).prop("disabled", false);
                        $(this).parents(".invoice1").find("input").eq(1).val("");
                    }
                }
            } else if (attrName == "unitPrice"){
                if (rate != "" && $(this).val() != "") {
                    var noTax = val / (1 + rate/100);
                    $(this).parents(".invoice1").find("input").eq(0).prop("disabled", true);
                    $(this).parents(".invoice1").find("input").eq(0).val(parseFloat(Number(noTax).toFixed(10)));
                } else {
                    if ($(this).val() == "") {
                        $(this).parents(".invoice1").find("input").eq(0).prop("disabled", false);
                        $(this).parents(".invoice1").find("input").eq(0).val("");
                    }
                }
            }
        }
    });
    $(".invoice1").on("change", 'select', function () {
        let rate = $(this).val(), price = 0,noprice = 0;
        let nopriceOne =  $(this).parents(".invoice1").find("input").eq(0);
        let priceOne =  $(this).parents(".invoice1").find("input").eq(1);
        if(priceOne.attr("disabled")){ // 按照bu含税算
            if(rate > 0 && nopriceOne.val() > 0){
                price = nopriceOne.val() * (rate/100 + 1) ;
                priceOne.val(parseFloat(Number(price).toFixed(10)));
            } else {
                priceOne.val("");
            }
        }else if(nopriceOne.attr("disabled")){ // 按照含税算
            if(rate > 0 && priceOne.val() > 0){
                noprice = priceOne.val() / (rate/100 + 1) ;
                nopriceOne.val(parseFloat(noprice.toFixed(10)));
            } else {
                nopriceOne.val("");
            }
        }
    });
    $("body").on('click', '.nodeBtn,.linkBtn', function () {
        var that = $(this)
        var name = that.data("fun");
        switch (name) { //新增计量单位
            case 'addUnit':
                addUnit('unitSelect');
                break;
            case 'baseRecord':
                baseRecord();
                break;
            case 'newContract':
                var customer_id = "";
                if ($("#addCommodity:visible").length > 0) {
                    customer_id = $("#curID span:last").data("id");
                } else if ($('#editCommodityOther:visible').length > 0) {
                    var info = $("#seeCommodity").data("info");
                    customer_id = info.customer_;
                }
                bounce_Fixed2.show($("#newContractInfo"));
                $("#newContractInfo input").val("")
                $("#newContractInfo .fileCon>div").html("")
                if ($("#cUpload1 #select_btn_1").length <= 0) {
                    initUpload($("#cUpload1"),'img');
                }
                if ($("#cUpload2 #select_btn_1").length <= 0) {
                    initUpload($("#cUpload2"), 'doc');
                }
                $("#newContractInfo .scanGs").data('gsArr',[]).data('gsArrNo',[]).html("0") ;
                $.ajax({
                    "url":"../sales/getContractCommodityList.do",
                    "data":{ "customerId": customer_id },
                    success:function (res) {
                        let list = res.productList || []
                        $("#newContractInfo .scanGs").data('gsArrNo',list) ;
                    }
                });
                break;
            case 'addGs' :
                let fun = that.data("fun");
                $("#tipcontractGoods").data("fun", fun);
                $("#tipcontractGoods .bonceHead span").html('向本合同添加商品');
                $(".addOrCancel").show(); $(".cScanc").hide();
                let gsArrNo = $("#newContractInfo .scanGs").data('gsArrNo')
                setContactGS(gsArrNo , true);
                break;
            case 'removeGs' :
                let fun1 = that.data("fun");
                $("#tipcontractGoods").data("fun", fun1);
                $("#tipcontractGoods .bonceHead span").html('从本合同移出商品');
                $(".addOrCancel").show(); $(".cScanc").hide();
                var list = $("#newContractInfo .scanGs").data('gsArr')
                setContactGS(list, true);
                break;
            case 'contractScan':
                var id = $("#contractScan").data("id");
                contractScan(id);
                break;
        }
    });
    $(".bounce_Fixed2").on('click','.fileImScan,.node', function(){
        var name = $(this).data('fun');
        switch (name) {
            case 'imgScan': // 合同的picture
                imgViewer($(this));
                break;
            case 'cWord': // 合同的可编辑版
                // let path = $("#cScan .cWord").attr('path');
                seeOnline($("#cScan .cWord a"))
                break;
            case 'gNum': //本合同下的商品
                $("#tipcontractGoods .bonceHead span").html('本合同下的商品');
                $(".addOrCancel").hide(); $(".cScanc").show();
                let productList = $("#cScan .gNum").data('list');
                setContactGS(productList , false);
                break;
            case 'cEditLog': // 本版本合同的修改记录

                break;
            case 'cRenewalLog': // 本合同的续约记录

                break;
        }
    });
})
// creator: 李玉婷，2020-03-30 09:21:28，获取设置的发票
function invoiceSettings(curObj,selectedVal,catVal,source){
    let options = `<option value="">请选择</option>`;
    let cat1 = false,cat2 = false;
    $.ajax({
        "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
        async: false,
        success: function (res) {
            var list = res["financeInvoiceSettings"];
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    if (list[i]["category"] == '1'){
                        var rate = list[i]["enableTaxTate"];
                        var rates = rate.split(",");
                        cat1 = true;
                        if (rates && rates.length >0){
                            var str = '<option value=""></option>';
                            for (var r = 0; r < rates.length; r++) {
                                var selectedStr = "";
                                if (selectedVal == rates[r]) {
                                    selectedStr = " selected='true' ";
                                }
                                str += "<option " + selectedStr + " value='" + rates[r] + "'>" + rates[r] + "%</option>";
                            }
                            curObj.html(str);
                        }
                    } else if (list[i]["category"] == '2' || list[i]["category"] == '3'){
                        cat2 = true;
                    }
                }
                if (cat1) {options += `<option value="1">开具增值税专用发票</option>`;}
                if (cat2) {options += `<option value="2">开具其他发票</option>`;}
            }
            options += `<option value="4">不开发票</option>`;
            if (source == 1) {
                if ($("#addCommodity:visible").length > 0) {
                    cat1? $("#addCommodity .invoice1").show():$("#addCommodity .invoice1").hide();
                    cat2? $("#addCommodity .invoice2").show():$("#addCommodity .invoice2").hide();
                } else if ($("#editCommodityOther:visible").length > 0) {
                    cat1? $("#editCommodityOther .invoice1").show():$("#editCommodityOther .invoice1").hide();
                    cat2? $("#editCommodityOther .invoice2").show():$("#editCommodityOther .invoice2").hide();
                }
            } else if (source == 2) {
                if ($("#addCommodity:visible").length > 0) {
                    $("#addCommodity [name='invoiceCategory']").html(options);
                } else if ($("#editCommodityOther:visible").length > 0) {
                    $("#editCommodityOther [name='invoiceCategory']").html(options);
                    $("#editCommodityOther [name='invoiceCategory']").val(catVal);
                }
            }
        }
    });
}
// creator: 李玉婷，2020-03-17 15:37:18，商品录入,source:1=通用型，2=专属
function newSalesCommodity(isSaled,source) {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else {
        $("#addUnitSee").html("");
        $("#addCommodity").data('isSaled', isSaled);
        $("#addCommodity input").val("");
        $("#addCommodity select").val("");
        $("#addCommodity .invoice1 input").prop("disabled", false);
        $("#addCommodity .lenTip").html("0/100");
        $("#addCommodity .filePicBox").html("<span class='inTip'>请上传</span>");
        $("#addCommodity .fileVedioBox").html("<span class='inTip'>请上传</span>");
        $("#addCommodity .clearPicsBtn").hide();
        if (isSaled == 1){
            $(".sold").show();$(".noSold").hide();
            $("#addCommodity .bonceHead span").html("录入已销售过的商品");
        }else{
            $(".sold").hide();$(".noSold").show();
            $("#addCommodity .bonceHead span").html("录入未销售过的商品");
        }
        if (source == 2){
            var categoryName = $("#firstLevelName").html();
            if (categoryName == '' || categoryName=='全部') categoryName = '待分类';
            $("#categoryType").html(categoryName);
            $("#addCommodity .contractList").prop("disabled",true);
            $("#addCommodity .priceType").hide();
            $("#addCommodity [data-fun='newContract']").hide();
            let obj = $('#addCommodity [name="contractId"]');
            let param = {
                obj: obj,
                customer: $("#curID span:last").data("id"),
                contract: ""
            }
            getContractBaseList(param);
        }
        if ($("#filePic #select_btn_1").length <= 0) {
            initUpload($("#filePic"),'entry');
        }
        if ($("#fileVedio .uploadify-button").length <= 0) {
            initViewUpload($("#fileVedio"));
        }
        bounce.show($("#addCommodity"));
        invoiceSettings($("#addInvoiceSpecial"),"","",source);
        // 计量单位列表、
        getUnitList($("#unitSelect"), source);
    }
}
// creator: 李玉婷，2020-03-31 12:13:08，录入- 计量单位设置
function unitAssign(obj) {
    var val = obj.val();
    var unitName = obj.children("option:selected").html();
    $("#addUnitSee").html(unitName);
    obj.siblings("input").val(unitName)
}
// creator: 李玉婷，2020-03-18 11:06:46，查看商品,source:1=通用，2=专属
function seeCommodityDetails(obj, source,state) {
    var info = obj.parents("tr").data("info");
    $('#seeCommodity').data("id", info.id);
    $("#seeCommodity .editBtn").prop('disabled',state);
    if (state){
        $('#stopDetails').show();
        $("#seeCommodity .editBtn").hide();
    }else{
        $('#stopDetails').hide();
        if (source == 2 && info.type == 1){
            $("#seeCommodity .editBtn").hide();
        }else{
            $("#seeCommodity .editBtn").show();
        }
    }
    getCommodityDetails(info.id,source);
}
function getCommodityDetails(productId, source){
    $.ajax({
        url: "../product/getProductOne.do",
        data: {
            productId: productId
        },
        success: function (data) {
            if (data.code == 200){
                var info = data.data;
                var mediaList = info.fileList;
                var operList = info.startAndStopList, operStr = ``;
                var creatorStr = ``;
                var updateDate = new Date(info.updateDate).format('yyyy-MM-dd hh:mm:ss');
                $("#seeCommodity").data("info", info);
                $("#seeCommodity [need]").each(function () {
                    var name = $(this).data("name");
                    $(this).html(handleNull(info[name]));
                    if(name == 'unit'){
                        $(this).data('unitid', info['unitId'])
                    } else if (name == 'innerSn' && handleNull(info[name]) == "") {
                        $(this).html("暂无");
                    } else if (name == 'contractNumber' && handleNull(info[name]) == ""){
                        $(this).html("尚未填写");
                    } else if(name == 'minimumStock'){
                        $(this).html(parseFloat(Number(info[name] || "0").toFixed(4)));
                    }
                });
                let picStr = ``, vedio=``;
                if (mediaList && mediaList.length > 0) {
                    for(var t=0;t<mediaList.length;t++) {
                        if (mediaList[t].type == '1') {
                            picStr += `<span data-path="${mediaList[t].uplaodPath}" onclick="imgViewer($(this))">${t+1}</span>`;
                        } else {
                            vedio += `<span data-path="${mediaList[t].uplaodPath}" onclick="vedioPlay($(this))">${t+1}</span>`;
                        }
                    }
                }
                creatorStr += `<span class="oprationName">${info.createName}</span>${new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss')}`;
                $("#seeCommodity .comPics").html(picStr);
                $("#seeCommodity .comVideo").html(vedio);
                $("#seeCreator").html(creatorStr);
                $('#stopName').html(info.updateName);
                $('#stopDate').html(updateDate);
                $('#seeCommodity').data("source",source);
                $('#seeCommodity #seeInvoice' + source).show().siblings().hide();
                if (operList && operList.length > 0) {
                    for(var r=0;r<operList.length;r++) {
                        operStr +=
                            `<div><span class="oper">${operList[r].enabled == 0? '暂停销售':'恢复销售'}</span> <span class="oprationName">${operList[r].updateName}</span>${new Date(operList[r].updateDate).format('yyyy-MM-dd hh:mm:ss')}</div>`;
                    }
                }
                $("#seeCommodity .reLog").html(operStr);
                var unitPrice = handleNull(info.unitPrice)==''? '0.00': info.unitPrice;
                var unitPriceNotax = handleNull(info.unitPriceNotax)==''? '0.00': info.unitPriceNotax;
                var unitPriceInvoice = handleNull(info.unitPriceInvoice)==''? '0.00': info.unitPriceInvoice;
                var unitPriceNoinvoice = handleNull(info.unitPriceNoinvoice)==''? '0.00': info.unitPriceNoinvoice;
                if(source == 1) {
                    var unitPriceReference = handleNull(info.unitPriceReference)==''? '0.00': info.unitPriceReference;
                    var specialDes = handleNull(info.taxRate)=='' && handleNull(info.unitPrice)=='' && handleNull(info.unitPriceNotax)==''? '暂无':'税率'+handleNull(info.taxRate)+'%，含税单价'+unitPrice+'元，不含税价'+unitPriceNotax+'元';
                    let generalDes = `${handleNull(info.unitPriceInvoice)==''?'暂无':'开票单价' +unitPriceInvoice+'元'}`;
                    let noDes = `${handleNull(info.unitPriceNoinvoice)==''?'暂无':'不开票价'+unitPriceNoinvoice+ '元'}`;
                    let referenceDes = `${handleNull(info.unitPriceReference)==''?'暂无': '参考单价'+ unitPriceReference+'元'}`;
                    $("#seeCommodity [data-name='specialDes']").html(specialDes);
                    $("#seeCommodity [data-name='generalDes']").html(generalDes);
                    $("#seeCommodity [data-name='noDes']").html(noDes);
                    $("#seeCommodity [data-name='referenceDes']").html(referenceDes);
                }else if(source == 2){
                    // 不含税单价:unitPriceNotax 含税单价unitPrice 开普票时的开票单价unitPriceInvoice 不开发票时的单价unitPriceNoinvoice
                    let invoiceCategoryStr = ``, priceStr = ``;
                    if (info["invoiceCategory"] == 1) {
                        invoiceCategoryStr = `该商品需开税率为${info.taxRate}%的增值税专用发票`;
                        priceStr =  `含税单价${unitPrice}元，不含税价${unitPriceNotax}元`;
                    } else if (info["invoiceCategory"] == 2) {
                        invoiceCategoryStr = `该商品需开增值税专用发票以外的发票`;
                        priceStr =  `开普通发票的开票单价${unitPriceInvoice}元`;
                    } else if (info["invoiceCategory"] == 4) {
                        invoiceCategoryStr = `该商品不开发票`;
                        priceStr = `不开票单价${unitPriceNoinvoice}元`;
                    }
                    if (handleNull(info.contractNumber) != '') {
                        $("#contractScan").show();
                    } else {
                        $("#contractScan").hide();
                    }
                    $("#contractScan").data("id",info.contractId);
                    $("#seeCommodity [data-name='invoiceCategory']").html(invoiceCategoryStr);
                    $("#seeCommodity [data-name='price']").html(priceStr);
                }
                bounce.show($('#seeCommodity'));
            }
        }
    })
}
// creator : hxz 2018-07-23  获取商品 当前订购信息
function curOrders(thisObj) {
    var info = thisObj.parents("tr").data("info");
    var outerSn = info["outerSn"];
    $("#curOrders tbody").children("tr:gt(0)").remove();
    $.ajax({
        "url": "../sale/getOccOrder.do",
        "data": {"outerSn": outerSn, "orderId": ""},
        success: function (res) {
            var list = res["data"], str = "";
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var n = i + 1;
                    str += "<tr>" +
                        "     <td>" + n + "</td>" +
                        "     <td>"+ (list[i]["sn"] || "") +"</td>" +
                        "     <td>"+ list[i]["create_name"] +"</td>" +
                        "     <td>"+ list[i]["t_amount"] +"</td>" +
                        "     <td>"+ formatTime(list[i]["earliest_delivery_date"])  +"</td>" +
                        "     <td>"+ list[i]["customer_name"] +"</td>" +
                        " </tr>";
                }
            }
            $("#curOrders tbody").append(str);
            bounce.show($("#curOrders"));
        }
    });
}
// creator: 李玉婷，2020-03-19 09:28:36，暂停或恢复商品销售,1恢复 0暂停
function suspendSale(obj, state) {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else{
        var info = obj.parents("tr").data("info");
        var tips = '';
        var data = {
            'productId': info.id,
            'state': state
        };
        var param = JSON.stringify(data);
        $("#pauseSales").data('data', param);
        $("#pauseSales").data('state', state);
        if (state == 1){
            tips =
                '<p class="ty-center">操作成功后，再下订单时此商品又可被选择了！</p>'+
                '<p class="ty-center">确定“恢复销售”此商品？</p>';
            $("#pauseSalesTip").html(tips);
            bounce.show($("#pauseSales"));
        }else{
            $.ajax({
                "url": "../product/suspendProductJudge.do",
                "data": data,
                success: function (res) {
                    if (res.code == '1'){
                        tips = '<p class="ty-center">操作成功后，再下订单时将无法选到此商品。</p>'+
                            '<p class="ty-center">确定“暂停销售”此商品？</p>';
                    }else {
                        tips =
                            '<p class="gapLeft">1、此商品尚有库存！</p>' +
                            '<p class="gapLeft">2、操作成功后，再下订单时将无法选到此商品。</p>'+
                            '<p class="ty-center">确定“暂停销售”此商品？</p>';
                    }
                    $("#pauseSalesTip").html(tips);
                    bounce.show($("#pauseSales"));
                }
            });
        }
    }
}
// creator: 李玉婷，2020-03-19 10:31:42，暂停或恢复商品销售确定
function suspendSaleSure() {
    var json = JSON.parse($("#pauseSales").data('data'));
    var state = $("#pauseSales").data('state');
    $.ajax({
        "url": "../product/suspendProduct.do",
        "data": json,
        success: function (res) {
            if (res.code == 200){
                var keyword = $("#searchKeyBase").val();
                bounce.cancel();
                if (state == 1){
                    getSuspendList(1,20,keyword);
                }else{
                    var name = $("#firstLevelName").html();
                    var id = $("#firstLevelName").data("categoryid");
                    getCommodityList(1, 20,id,name,keyword);
                }
            }
        }
    });
}
// creator: 李玉婷，2020-03-19 09:30:28，删除商品
function deleteCommodity(obj, state) {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else {
        gblObj = obj;
        var info = obj.parents("tr").data("info");
        var productId = info.id;
        $('#deleteCommodity').data('id', productId);
        $('#deleteCommodity').data('state', state);
        bounce.show($('#deleteCommodity'));
        $.ajax({
            "url": "../product/deleteProductJudge.do",
            "data": {
                'productId': productId
            },
            success: function (res) {
                if (res.code == '1'){
                    $("#deleteTip").html('<p class="ty-center">确定删除此商品？</p>');
                    $(".deleteCan").show();
                    $(".delRefuse").hide();
                } else{
                    $("#deleteTip").html('<p class="ty-indent">系统不支持“删除”有关联、包装或订购信息的商品，而此商品在系统中有关联、包装或订购信息！</p>');
                    $(".deleteCan").hide();
                    $(".delRefuse").show();
                }
            }
        });
    }
}
// creator: 李玉婷，2020-03-19 11:38:49，删除商品确定
function deleteCommoditySure(){
    var id = $('#deleteCommodity').data('id');
    var state = $('#deleteCommodity').data('state');
    $.ajax({
        "url": "../product/deleteProduct.do",
        "data": {
            'productId': id
        },
        success: function (res) {
            if (res.code == 200){
                bounce.cancel();
                var keyword = $("#searchKeyBase").val();
                if (state == 1){
                    getSuspendList(1,20,keyword);
                }else{
                    var name = $("#firstLevelName").html();
                    var id = $("#firstLevelName").data("categoryid");
                    getCommodityList(1, 20,id,name,keyword);
                }
            }
        }
    });
}
// creator: 李玉婷，2020-03-19 14:29:01，修改基本信息
function updateCommodityBase(model){  // 1 - 通用型商品 2- 专属商品
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else{
        var info = $("#seeCommodity").data("info");
        var mediaList = info.fileList;
        if ($("#filePic_edit #select_btn_1").length <= 0) {
            initUpload($("#filePic_edit"), 'entry');
        }
        if ($("#editCommodityBase .k-upload").length <= 0) {
            initViewUpload($("#fileVedio_edit"));
        }
        $('#orderUnNum').html(info.orderNum);
        $("#editCommodityBase .lenTip").html(info.memo.length + "/100");
        $("#updateCommodityBase [need]").each(function(){
            var name = $(this).attr('name');
            $(this).val(info[name]);
            if(name == 'unit'){
                var unitId = info.unitId;
                getUnitList($("#update_unitSelect"), model, unitId);
            }
        });
        let picStr = ``, vedio=``;
        if (mediaList && mediaList.length > 0) {
            for(var t=0;t<mediaList.length;t++) {
                if (mediaList[t].type == '1') {
                    picStr =
                        `<div class="imgsthumb">
                            <span class="filePic" data-path="${mediaList[t].uplaodPath}" data-ttl="${mediaList[t].title}" onclick="imgViewer($(this))">${t+1}</span>
                    <i class="fa fa-times" fileUid="${mediaList[t].fileUid}" onclick="cancleThis($(this))"></i>
                    </div>`;
                } else {
                    vedio +=
                        `<div class="imgsthumb">
                            <span class="filePic" data-path="${mediaList[t].uplaodPath}" data-ttl="${mediaList[t].title}" onclick="imgViewer($(this))">${t+1}</span>
                    <i class="fa fa-times" fileUid="${mediaList[t].fileUid}" onclick="cancleThis($(this))"></i>
                    </div>`;
                }
            }
            $('#editCommodityBase .filePicBox').html(picStr);
            $('#editCommodityBase .fileVedioBox').html(vedio);
            if ($('#editCommodityBase .filePicBox .imgsthumb').length > 0){
                $('#editCommodityBase .clearPicsBtn').show();
            } else {
                $('#editCommodityBase .clearPicsBtn').hide();
            }
        }
        bounce_Fixed.show($('#editCommodityBase'));
    }
}
function updateCommodityBaseSure(){
    var emptyNum = 0;
    $("#updateCommodityBase [require]:visible").each(function(){
        if($(this).val() == ""){ emptyNum++; }
    });
    if (emptyNum > 0){
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }else{
        var id = $('#seeCommodity').data('id');
        var params = {
            id: id
        };
        $("#updateCommodityBase [need]").each(function(){
            var nameData = $(this).attr('name');
            var html = $(this).val();
            params[nameData] = html;
        });
        //图片 视频
        var media = [];
        $("#updateCommodityBase .filePicBox .imgsthumb").each(function () {
            var cur = $(this).find(".filePic");
            var item = {
                'uplaodPath': cur.data('path'),
                'title':cur.data('ttl'),
                'type': 1,
                'orders': 1
            }
            media.push(item);
        });
        params.commodityMediaList = JSON.stringify(media);
        $.ajax({
            "url": "../product/updateProductBase.do",
            "data": params,
            success: function (res) {
                if (res.code == 'success'){
                    bounce_Fixed.cancel();
                    var source = $('#seeCommodity').data("source");
                    getCommodityDetails(id, source);
                    getCommodityList(1,20,"","全部",'');
                }else {
                    var msg = res.msg;
                    $("#unfilledTip_ms").html(msg);
                    bounce_Fixed2.show($("#unfilledTip"));
                }
            }
        });
    }
}
// creator: 李玉婷，2020-03-20 10:43:13，修改商品价格
function updateOtherData(){
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else {
        var info = $("#seeCommodity").data("info");
        var source = $('#seeCommodity').data("source");
        $('#orderUnNumOther').html(info.orderNum);
        $("#editCommodityOther .lenTip").html(info.priceDesc.length + "/100");
        $("#editCommodityOther [need]").each(function(){
            var nameData = $(this).attr('name');
            if (nameData == 'outerSn' || nameData == 'outerName') {
                $(this).html(info[nameData]);
            } else {
                $(this).val(info[nameData])
            }
        });
        $("#editCommodityOther .invoice1 input").prop("disabled", false);
        bounce_Fixed.show($('#editCommodityOther'));
        invoiceSettings($("#updateInvoiceSpecial"),info.taxRate,info.invoiceCategory,source);
        if (source == '2') {
            $("#editCommodityOther .priceType").hide();
            $('#editCommodityOther [name="contractType"]').val("");
            let obj = $('#editCommodityOther [name="contractId"]');
            let param = {
                obj: obj,
                customer: info.customer_,
                contract: info.contractId
            }
            getContractBaseList(param);
            if (handleNull(info.contractId) != "") {
                $('#editCommodityOther [name="contractType"]').val(1);
                $('#editCommodityOther [name="contractId"]').prop("disabled", false);
                $("#addCommodity [data-fun='newContract']").show();
            } else {
                $("#addCommodity [data-fun='newContract']").hide();
                $('#editCommodityOther [name="contractId"]').prop("disabled", true);
            }
            if (info.invoiceCategory && info.invoiceCategory != "") {
                $("#editCommodityOther .invoice" + info.invoiceCategory).show();
            }
        }
    }
}
// creator: 李玉婷，2020-03-20 11:15:01，修改商品价格信息确定
function updateOtherSure() {
    var reqTrue = 0; //invoiceCategory  '发票类型:1-增值税专用票,2-增值税普通票,3-其他票,4-不开票'
    $("#editOtherInvoice [require]:visible").each(function () {
        if ($(this).val() == "") reqTrue++;
    });
    if (reqTrue > 0) {
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }
    var id = $('#seeCommodity').data('id');
    var source = $('#seeCommodity').data("source");
    var params = {
        id: id
    };
    $("#editOtherInvoice input[need]:visible").each(function(){
        var nameData = $(this).attr('name');
        if(nameData == 'unitPriceNotax' || nameData == 'unitPrice') {
            params[nameData] = Number($(this).val());
        } else {
            params[nameData] = $(this).val();
        }
    });
    $("#editOtherInvoice select[need]:visible").each(function(){
        var nameData = $(this).attr('name');
        var html = $(this).val();
        params[nameData] = html;
    });
    if($("#editOtherInvoice [name='invoiceCategory']").val() == ""){
        delete params["invoiceCategory"];
    }
    $.ajax({
        "url": "../product/updateProductOtherBase.do",
        "data": params,
        success: function (res) {
            if (res.code == 'success'){
                bounce_Fixed.cancel();
                getCommodityDetails(id,source);
                getCommodityList(1,20,"","全部",'');
            }
        }
    });
}
// creator: 李玉婷，2020-03-27 17:42:06，分类
function kindBtn(obj) {
    var json = obj.siblings("div").children(".kindId").html();
    json = JSON.parse(json);
    var keyword = $("#searchKeyBase").val();
    var strson = '<span onclick="showkindNav($(this))" data-id="'+ json.id +'" data-name="'+ json.fullName +'"> >'+ json.fullName +'</span>';
    $("#curID").append(strson);
    getCommodityList(1,20,json.id,json.fullName,keyword);
    var stop = obj.data("stop")
    if (stop === 1){
        $(".indexInput .faceul .faceul1").addClass("disabled");
        $(".indexInput .faceul button").prop("disabled",true);
    }else{
        $(".indexInput .faceul button").prop("disabled",false);
        $(".indexInput .faceul .faceul1").removeClass("disabled");
    }
}
// creator: 李玉婷，2020-03-27 14:28:26，搜索
function searchKeyBase() {
    var keyword = $("#searchKeyBase").val();
    var categoryName = $("#firstLevelName").html();
    var category = '';
    if (categoryName != '全部') {
        category = $("#firstLevelName").data("categoryid");
    }
    if ($(".inSalesList:visible").length > 0){
        getCommodityList(1,20,category,categoryName,keyword);
    } else if ($(".inSuspendList:visible").length > 0){
        getSuspendList(1,20,keyword);
    }
}
// creator: 李玉婷，2020-03-30 15:48:25，暂停销售的商品
function suspendCommodyList(obj){
    $(".inSuspend").show();
    $(".inSales").hide();
    $(".indexInput").hide();
    $(".suspendBtn").hide();
    $(".left-bottom").show();
    $(".between").height(0);
    $("#kindsTree").html('');
    $("#firstLevelAmount").html('0');
    $("#firstLevelName").html(obj.html());
    var strson = '<span onclick="showkindNav($(this))" data-id="" data-name="'+ obj.html() +'"> >'+ obj.html() +'</span>';
    $("#curID").append(strson);
    getSuspendList(1,20,"");
}
// creator: 李玉婷，2020-04-03 10:28:28，描述、说明字数限制
function limitWord(obj, num) {
    var val = obj.val();
    var length = val.length;
    if (length <= num){
        obj.siblings("p").find(".lenTip").html(length + '/' + num);
    }else{
        var str = val.slice(0,num);
        obj.val(str);
        obj.siblings("p").find(".lenTip").html(str.length + '/' + num);
    }
}
// creator: 李玉婷，2020-04-03 13:46:22，当前分类
function showkindNav(obj){
    var id = obj.data('id');
    var name = obj.data('name');
    obj.nextAll().remove();
    if (id == "" || $(".inSalesList:visible").length > 0){
        getCommodityList(1,20,id,name,"");
    } else if ($(".inSuspendList:visible").length > 0){
        getSuspendList(1,20,"");
    }
}
//  返回上一级
function gobackLstLevel(type){
    if (type == 1){
        var  n = $("#curID").children().length-1 ;
        var kindId = "",kindName = "";
        var m = n - 1 ;
        if( m >= 0 ){
            $("#curID").children(":eq("+ n +")").remove() ;
            kindId = $("#curID").children(":eq("+ m +")").data("id");
            kindName = $("#curID").children(":eq("+ m +")").data("name");
            getCommodityList(1,20,kindId,kindName,"" );
        }
    }else{
        $("#curID").children(":eq(0)").nextAll().remove() ;
        getCommodityList(1,20,"","","" );
    }
}

// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectedID) {
    // 　1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['name'] +'</option>';
                }
            }
            obj.html(str);
        }
    })
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit(idStr) {
    bounce_Fixed2.show($("#addUnit"));
    $("#addUnit input").val("");
    $("#updateType").val(idStr);
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed2.cancel();
                var idStr = $("#updateType").val();
                getUnitList($("#" + idStr), module);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed2.show($("#tip1"));
            }
        }
    })
}

// creator: 李玉婷，2021-05-20 17:32:24，上传图片
function initUpload(obj, storg){
    let fileTypeExtsStr = ''
    let multi = true
    if(storg == "img" || storg == "entry"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(storg == "doc"){
        multi = false
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    } else if (storg == "vedio") {
        fileTypeExtsStr = '*.cda,*.wav;*.wmv;*.mp3;*.mp4;*.mpeg;;*.aiff;*.vqf;*.amr;'
    }
    let itemTemplate = ``
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi:multi,
        buttonText:"上传",
        formData:{
            module: '销售管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            //name = file.name,           //文件名称
            obj.parent().attr("groupUuid", data.groupUuid )
            switch (storg) {
                case 'entry':
                    var imgLen = obj.parent("p").siblings().find(".filePicBox").find(".imgsthumb").length;
                    if(imgLen < 9){
                        var imgStr1 =
                            `<div class="imgsthumb">
                                <span class="filePic" data-path="${path}" data-ttl="${file.name}" onclick="imgViewer($(this))">${imgLen + 1}</span>
                                <i class="fa fa-times" fileUid="${data.fileUid}" onclick="cancleThis($(this))"></i>
                            </div>`;
                        obj.parent("p").siblings().find(".filePicBox").append(imgStr1);
                        if (imgLen < 1) {
                            obj.parent("p").siblings().find(".filePicBox").find(".inTip").remove();
                        }
                        obj.parent("p").siblings().find(".clearPicsBtn").show();
                    }else{
                        layer.msg('最多只能上传9个文件')
                        delC(data)
                    }
                    break;
                case 'img':
                    let len =  $(`.fileCon1`).find(".fileIm").length;
                    if(len < 9){
                        data.orders = len + 1
                        data.filePath = data.filename
                        data.title = data.originalFilename
                        let imgStr1 = `<span class="fileIm"  >
                                 <span>${ 1 + len }</span>
                                 <span class="fa fa-times"></span>
                                 <span class="hd">${ JSON.stringify(data) }</span>
                            </span>`
                        $(`.fileCon1`).append(imgStr1);
                    }else{
                        layer.msg('最多只能上传9个文件')
                        delC(data);
                    }
                    break;
                case 'doc':
                    let fileCon2Len =  $(`.fileCon2`).find(".fileIm").length;
                    if(fileCon2Len === 0){
                    }else{
                        let delO = $(`.fileCon2`).find(".fileIm")
                        let info = JSON.parse(delO.find(".hd").html())
                        delC(info)
                        delO.remove();
                    }
                    let str2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data) }</span>
                        </span>`
                    $(`.fileCon2`).html(str2);
                    break;
            }
        }
    });
}
function initViewUpload(obj) {
    // 单文件上传不需要groupUuid
    obj.Huploadify({
        auto: false,
        fileTypeExts: '*.mp4',
        formData:{
            module: '销售管理',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: 1000960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedSize: true,
        removeTimeout: 99999999,
        buttonText: "上传",
        queueID: 'fileQueue',
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        multi:false,
        onSelect: function () {
            $(".fileUpload .uploadify-button").hide();
            // 获取文件url，创建一个新的音频元素
            let audioElement = new Audio(URL.createObjectURL(obj.children("input")[0].files[0]));
            // 绑定事件获取时长
            audioElement.addEventListener("loadedmetadata", (_event)=> {
                let duration = parseInt(audioElement.duration);
                console.log("duration")
                // 大小 时长
                if (duration > 15) {
                    //超过需求限制给的提示，你这里自己修改
                    layer.msg("视频时长不能超过15s")
                    // 清除上传队列
                    obj.find(".uploadify-queue").html('')
                } else {
                    // 符合条件时上传，改为手动上传后不会自动上传，在这个事件回调判断结束后再触发上传按钮
                    obj.find(".uploadbtn").click()
                    // 因为不显示过程我在这里加了一个loading
                    loading.open()
                }
            });
            audioElement.load()
        },
        onUploadError: function () {
            obj.find(".uploadify_state").html("上传失败！")
        },
        onUploadSuccess: function (file, json) {
            var data = JSON.parse(json)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            // 这部分自己修改
            console.log('file', file)
            console.log('json', json)
             if(json.length>0) {
                 // 结束loading
                 loading.close()
                 var mLen = obj.parents("p").siblings(".fileVedioBox ").find(".imgsthumb").length;
                 if(mLen < 1) {
                     var mStr1 =
                         '<div class="imgsthumb">' +
                         '    <a class="filePic" data-path="' + path + '" data-ttl="' + file.name + '" onclick="vedioPlay($(this))">' + (mLen + 1) + '</a>' +
                         '    <i class="fa fa-times" fileUid="' + data.fileUid + '" onclick="cancleThis($(this))"></i> ' +
                         '</div>';
                     obj.parents("p").siblings(".fileVedioBox").html(mStr1);
                 } else {
                     layer.msg('最多只能上传1个视频文件');
                     delC(json)
                 }
             }
            obj.find(".uploadify-queue").html('')
        },
        onQueueComplete:function() {
            obj.find(".uploadify-queue").html('')
        }
    })
}
// creator：lyt，2021-08-12 16:39:08，中断上传，关闭弹窗
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else {
        console.log("未定义的状态")
    }
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = []
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
}
// creator: hxz 2021-05-21 删除已经上传的合同文件
function delC (delInfo) {
    var option = {type: 'fileUid', fileUid: delInfo.fileUid}
    fileDelAjax(option)
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.data('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");
}
// creator: 李玉婷，2021-05-25 15:43:51，视频播放
function vedioPlay(obj) {
    var src = obj.data("path"),
        sourceDom = "<source src=\""+ $.fileUrl + src +"\">";
    $("#video-box video").html(sourceDom);
    $("#video-box").show();
    // 自动播放
    $("#video-box video")[0].play()
}
// creator: 李玉婷，2021-05-24 16:51:45，清除
function clearPicsBtn(obj) {
    obj.siblings(".filePicBox").find(".imgsthumb").each(function () {
        var curObj = $(this).find(".fa");
        cancleThis(curObj);
    })
}
// creator: 李玉婷，2019-09-04 19:31:34，删除图片
function cancleThis(obj) {
    var parentObj = obj.parents(".filePicBox");
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    if (parentObj.find(".imgsthumb").length <= 0) {
        parentObj.siblings(".clearPicsBtn").hide();
        parentObj.html("<span class='inTip'>请上传</span>");
    } else {
        obj.parents(".filePicBox").find(".imgsthumb").each(function () {
            var idx = $(this).index()+ 1;
            $(this).find(".filePic").html(idx);
        })
    }
}
// creator: 李玉婷，2021-05-22 09:05:59，修改记录
function baseRecord() {

    /*let info = JSON.parse($("#page5").find(".json").html());
    let id = info.id;
    $.ajax({
        "url":"../trainManage/selectTrainBankHistoryList.do",
        "data": { "id": id  } ,
        success:function(res) {
            let list = res.trainingQuestionBankHistoryList || [];
            let info = res.trainingQuestionBank ;
            bounce.show($("#bankEditLog"));
            let curStaStr = ``;
            if(list.length >0){
                let str = ``;
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    str += `<tr>
                             <td>${i===0 ? "原始信息":"第"+ i +"次修改后"}</td>
                             <td class=" ty-table-control">
                                <span class="funbtn ty-color-blue" data-fun="bankInfoLogScan">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                                <td>${item.updateName} ${new Date(item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                }
                $("#bankEditLog tbody").children(":gt(0)").remove();
                $("#bankEditLog tbody").append(str);
                let n = list.length
                let last = list[n-1]
                curStaStr = `<span style="float: right;">修改人：${last.updateName} ${new Date(last.updateDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料为第${n}次修改后的结果。`
            }else{
                $("#bankEditLog table").hide();
                curStaStr = `<span style="float: right;">创建人：${info.createName} ${new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料尚未经修改。`
            }
            $("#bankEditLog .curSta").html(curStaStr);
        }
    });*/
}
// creator: 李玉婷，2020-09-10 11:59:43，一键清空
function clearPre(obj) {
obj.prev("input").val("");
}
// create：hxz 2021-1-21 字数超出限制
function setWordsNum(thisObj , maxLen){
    let curtxt = thisObj.val()
    if(curtxt.length > maxLen){
        thisObj.val(curtxt.substr(0,maxLen));
        layer.msg(`最多录入${maxLen}个字符`);
    }
    thisObj.parent().find(".lenTip").html( thisObj.val().length + " / " + maxLen);
}
// creator: 李玉婷，2021-06-29 11:44:53，控制输入3位小数
// creator: 李玉婷，2021-06-29 13:54:49，保留3位小数
function testNumSize3(obj){
    obj.value = obj.value.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符
    obj.value = obj.value.replace(/^\./g,"");  //验证第一个字符是数字而不是.
    obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/,'$1$2.$3');//只能输入两个小数
    if(obj.value.indexOf(".")< 0 && obj.value !=""){//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        obj.value= parseFloat(obj.value);
    }
}