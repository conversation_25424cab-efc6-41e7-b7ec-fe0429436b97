// updator：王静，2017-08-22 09:11:09
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#tipcontractGoods"));
bounce_Fixed3.cancel();
$(function(){
    $("#tipcontractGoods").on('click','.fa', function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    });
	// 获得基本信息列表
    getCommodityList(1, 20,"","全部","");
});
// creator: 李玉婷，2020-03-18 14:51:41，获取专属商品列表
function getCommodityList(currPage,pageSize,category,categoryName,keyword){
    $("#ye").html("");
    $(".indexInput").show();
    $(".between").height(0);
    $("#kindsTree li").remove();
    $("#suspendCommodyNum").html("0");
    $("#classifiedGoods tbody").html("");
    $(".inSales").show().siblings().hide();
    if (categoryName == "" || categoryName == "全部"){
        $(".suspendBtn").show();
        $(".left-bottom").hide();
        $("#firstLevelName").html("全部").data("categoryid",category);
    } else{
        $(".suspendBtn").hide();
        $(".left-bottom").show();
        $("#firstLevelName").html(categoryName).data("categoryid",category);
    }
    $(".indexInput .faceul").show();
    if (categoryName == "" || categoryName == "全部" || categoryName == "待分类"){
        $(".indexInput .faceul .faceul1").addClass("disabled");
        $(".indexInput .faceul button").prop("disabled",true);
        if (categoryName == "待分类" && category== 0) {
            $(".indexInput .faceul").hide();
        }
    }else{
        $(".indexInput .faceul button").prop("disabled",false);
        $(".indexInput .faceul .faceul1").removeClass("disabled");
    }
    $.ajax({
        url: "../product/getAllProductList.do",
        data: {
            "pageSize":pageSize,
            "currPage":currPage,
            "customerId": category,
            "param": keyword
        },
        success: function (data) {
            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            var count = 0;
            var jsonStr = {
                "category": category,
                "categoryName": categoryName,
                "param": keyword,
            } ;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#ye") , curr ,  totalPage , "commodity", jsonStr );
            var list = data.data;
            var customers = data.customers;
            if(categoryName == "" || categoryName == '全部'){
                $("#suspendCommodyNum").html(data.suspendProductNum);
            }
            if (customers && customers.length > 0){
                var menu = '';
                for(var m=0;m<customers.length;m++){
                    count += customers[m].num;
                    if (categoryName != '待分类'){
                        menu +=
                            '<li class="faceul1">' +
                            '    <a>' +
                            '        <span data-stop="'+customers[m].isSuspend+'" onclick="kindBtn($(this))">'+ customers[m].fullName +'（'+ customers[m].num +'种）</span>' +
                            '        <div class="hd">' +
                            '            <span class="kindId">'+ JSON.stringify(customers[m])+'</span>' +
                            '        </div>' +
                            '    </a>' +
                            '</li>';
                    }
                }
                $("#kindsTree").html(menu);
            }
            $("#firstLevelAmount").html(count);
            if (list && list.length > 0){
                var html = '';
                for(var t=0;t<list.length;t++){
                    var info = JSON.stringify(list[t]);
                    var operaBtn = '';
                    if (list[t].type == 1){
                        operaBtn =
                            '         <span class="ty-color-blue tb-btn-sm" onclick="seeCommodityDetails($(this),1, false)">查看</span>' +
                            '         <span class="ty-color-blue tb-btn-sm" onclick="curOrders($(this))">当前订购信息</span>';
                    } else if (list[t].type == 2){
                        operaBtn +=
                            '         <span class="ty-color-blue tb-btn-sm" onclick="seeCommodityDetails($(this),2, false)">查看</span>' +
                            '         <span class="ty-color-blue tb-btn-sm" onclick="curOrders($(this))">当前订购信息</span>'+
                            '         <span class="ty-color-blue tb-btn-sm" onclick="suspendSale($(this),0)">暂停销售</span>' +
                            '         <span class="ty-color-red tb-btn-sm" onclick="deleteCommodity($(this),0)">删除</span>';
                    }
                    html+=
                        ' <tr data-info=\''+ info +'\'>' +
                        '     <td>'+ list[t].outerSn +'</td>' +
                        '     <td>'+ list[t].outerName +'</td>' +
                        '     <td>'+ list[t].model +'</td>' +
                        '     <td>'+ list[t].specifications +'</td>' +
                        '     <td>'+ list[t].unit +'</td>' +
                        '     <td>'+ parseFloat(Number(list[t].minimumStock || "0").toFixed(4)) +'</td>' +
                        '     <td>'+ parseFloat(Number(list[t].currentStock || "0").toFixed(4)) +'</td>' +
                        '     <td class="createInfo">'+ list[t].createName + ' &nbsp; ' + new Date(list[t].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '     <td>' + operaBtn +
                        '     </td>' +
                        ' </tr>';
                }
                $("#classifiedGoods tbody").html(html);
            }

            var conHeight = $(".bigContainer").height() ;
            conHeight++;
            $(".between").height(conHeight);
        }
    })
}
// creator: 李玉婷，2020-03-17 16:05:56，专属商品录入确定
function addZsCommoditySure() {
    var reqTrue = 0; //invoiceCategory  '发票类型:1-增值税专用票,2-增值税普通票,3-其他票,4-不开票'
    $("#addCommodity input[require]:visible").each(function () {
        if ($(this).val() == "") reqTrue++;
    });
    $("#addCommodity select[require]:visible").each(function () {
        if ($(this).val() == "") reqTrue++;
    });
    if (reqTrue > 0) {
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    } else {
        var isSaled = $("#addCommodity").data('isSaled');
        var goodsData = {
            'customer.id': $("#firstLevelName").data("categoryid"),
            'isSaled': isSaled
        };
        $("#addCommodity input:visible").each(function () {
            var name = $(this).attr('name');
            if (name) {
                goodsData[name] = $(this).val();
            }
        });
        $("#addCommodity select:visible").each(function () {
            var name = $(this).attr('name');
            if (name) {
                if(name == 'unitPriceNotax' || name == 'unitPrice') {
                    goodsData[name] = Number($(this).val());
                } else {
                    goodsData[name] = $(this).val();
                }
            }
        });
        if($("#addCommodity input[name='contractId']").val() == ""){
            delete goodsData["contractId"];
        }
        if($("#addCommodity [name='invoiceCategory']").val() == ""){
            delete goodsData["invoiceCategory"];
        }
        goodsData['unit'] = $("#add_unitName").val();
        //图片 视频
        var media = [];
        $("#addCommodity .filePicBox .imgsthumb").each(function () {
            var cur = $(this).find(".filePic");
            var item = {
                'uplaodPath': cur.data('path'),
                'title':cur.data('ttl'),
                'type': 1,
                'orders': 1
            }
            media.push(item);
        });
        goodsData.commodityMediaList = JSON.stringify(media);
        $.ajax({
            url: "../product/addZSProduct.do",
            data: goodsData,
            success: function (data) {
                if (data.code == 200) {
                    bounce.cancel();
                    var catId = $("#firstLevelName").data("categoryid");
                    var catName = $("#firstLevelName").html();
                    var key = $("#searchKeyBase").val();
                    getCommodityList(1, 20,catId,catName,key);
                } else {
                    var msg = data.msg;
                    $("#unfilledTip_ms").html(msg);
                    bounce_Fixed2.show($("#unfilledTip"));
                }
            }
        })
    }
}
// creator: 李玉婷，2020-04-01 15:18:20，获取暂停销售的商品数据
function getSuspendList(currPage,pageSize,keyword) {
    $("#ye").html('');
    $("#firstLevelAmount").html('0');
    $("#suspendZSList tbody").html('');
    $.ajax({
        "url": "../product/suspendAllProductList.do",
        "data": {
            "pageSize":pageSize,
            "currPage":currPage,
            "param": keyword
        },
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = {
                "param": keyword,
            } ;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#ye") , curr ,  totalPage , "inSuspendTY", jsonStr );
            $("#firstLevelAmount").html(res.suspendProductNum);
            var spList = res.data;
            if (spList && spList.length > 0){
                var html = '';
                for(var t=0;t<spList.length;t++){
                    var info = JSON.stringify(spList[t]);
                    html+=
                        ' <tr data-info=\''+ info +'\'>' +
                        '     <td>'+ spList[t].outerSn +'</td>' +
                        '     <td>'+ spList[t].outerName +'</td>' +
                        '     <td>'+ spList[t].model +'</td>' +
                        '     <td>'+ spList[t].specifications +'</td>' +
                        '     <td>'+ spList[t].unit +'</td>' +
                        '     <td>'+ parseFloat(Number(spList[t].minimumStock || "0").toFixed(4)) +'</td>' +
                        '     <td>'+ parseFloat(Number(spList[t].currentStock || "0").toFixed(4)) +'</td>' +
                        '     <td class="createInfo">'+ spList[t].updateName + ' &nbsp; ' + new Date(spList[t].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '     <td>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="seeCommodityDetails($(this),2, true)">查看</span>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="curOrders($(this))">当前订购信息</span>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="suspendSale($(this),1)">恢复销售</span>' +
                        '         <span class="ty-color-red tb-btn-sm" onclick="deleteCommodity($(this),1)">删除</span>' +
                        '     </td>' +
                        ' </tr>';
                }
                $("#suspendZSList tbody").html(html);
            }
            var conHeight = $(".bigContainer").height() ;
            conHeight++;
            $(".between").height(conHeight);
        }
    });
}

// creator: 李玉婷，2021-05-21 10:46:35，此商品是否包含于与客户已签订的合同中
function contractSelect(obj) {
    var val = obj.val();
    if (val == 1) {
        obj.parent().next().find("button").show();
        obj.parents(".modItem-m").next().find("select").prop("disabled",false);
    } else {
        obj.parent().next().find("button").hide();
        obj.parents(".modItem-m").next().find("select").val("").prop("disabled",true);
    }
}
// creator: 李玉婷，2021-05-21 11:08:35，发票选择
function invoiceTypeSelect(obj) {
    var val = obj.val();
    obj.parents(".modItem-m").siblings(".priceType").hide();
    obj.parents(".modItem-m").siblings(".invoice" + val).show();
}
// creator: 李玉婷，2021-05-26 21:46:29，获取合同列表
function getContractBaseList(param){
    $.ajax({
        "url": "../product/getContractBaseListByCustomer.do",
        "data": {
            "customerId": param.customer
        },
        success: function (res) {
            let contracts = res.contractBaseList;
            let options = `<option value="">请选择</option>`;
            for (var i=0;i<contracts.length; i++) {
                options += `<option value="${contracts[i].id}">${contracts[i].sn}</option>`;
            }
            param["obj"].html(options);
            param["obj"].val(param.contract);
        }
    });
}
// creater : 2021-5-24 hxz 渲染合同里商品的列表
function setContactGS(list, boolSet) {
    let str = '';
    let cstr = '';
    if(boolSet){
        cstr = '<span class="fa fa-square-o"></span>';
    }
    list = list || [];
    list.forEach(function (im) {
        str += `
          <tr>
              <td class="controlTd">${cstr}${im.sn}<span class="hd">${JSON.stringify(im)}</span></td>
              <td>${im.name}</td>
              <td>${im.model}</td>
              <td>${im.specifications}</td>
              <td>${im.unit}</td>
          </tr>`
    })
    $("#tipcontractGoods table tr:gt(0)").remove();
    $("#tipcontractGoods table").append(str);
    bounce_Fixed3.show($("#tipcontractGoods"));
}

// creater : 2021-5-24 hxz 合同商品 操作 确定
function addOrCancelOk() {
    let fun = $("#tipcontractGoods").data("fun");
    let listEdit = []
    $("#tipcontractGoods .fa-check-square-o").each(function(){
        let info =  JSON.parse($(this).siblings(".hd").html())
        let hasSame = false
        listEdit.push(info);
        $("#newContractInfo .goodList .gsIm").each(function () {
            let itemg = JSON.parse( $(this).find(".hd").html() );
            if(itemg.id === info.id){
                if(fun === "addGs"){
                    hasSame = true
                }else if(fun === "removeGs"){
                    $(this).remove();
                }
            }
        })
        if(!hasSame && fun === "addGs"){
            $(".goodList").append(`<span class="gsIm">${ info.name }、<span class="hd">${ JSON.stringify(info) }</span></span>`);
        }
    })
    bounce_Fixed3.cancel()
    // 更新数据存储
    let listYes =  $("#newContractInfo .scanGs").data('gsArr');
    let listNo =  $("#newContractInfo .scanGs").data('gsArrNo') || [];
    if(fun === "addGs"){
        listYes.push(...listEdit)// 将新增的添加到 选中的
        let listNew = []
        listNo.forEach(function(noLm, index){ // 从未选中中 去掉 新增的
            let has = false
            listEdit.forEach(function(noLm2){
                if(noLm.id == noLm2.id){
                    has = true
                }
            })
            if(!has){
                listNew.push(noLm)
            }
        })
    } else if(fun === "removeGs"){
        listNo.push(...listEdit)
        let listNew = []
        listYes.forEach(function(noLm, index){ // 从未选中中 去掉 新增的
            let has = false
            listEdit.forEach(function(noLm2){
                if(noLm.id == noLm2.id){
                    has = true
                }
            })
            if(!has){
                listNew.push(noLm)
            }
        })
        listYes = listNew
    }
    $("#newContractInfo .scanGs").data('gsArr', listYes).data('gsArrNo', listNo).html(listYes.length);
}

// creator: hxz 2021-05-21 编辑合同确定
function editContractOk() {
     // 确定
    let info = {};
    info.cNo = $("#newContractInfo .cNo").val()
    if(info.cNo.length === 0){
        layer.msg('请录入合同编号');
        return false
    }
    info.cSignDate = $("#newContractInfo .cSignDate").val()
    info.cStartDate = $("#newContractInfo .cStartDate").val()
    info.cEndDate = $("#newContractInfo .cEndDate").val()
    info.cMemo = $("#newContractInfo .cMemo").val()
    info.fileCon1 = [];
    let param = {}, customer = ``, curObj= ``;
    if ($("#addCommodity:visible").length > 0) {
        customer = $("#curID span:last").data("id")
        curObj = $('#addCommodity [name="contractId"]');
    } else if ($('#editCommodityOther:visible').length > 0) {
        var result = $("#seeCommodity").data("info");
        customer= result.customer_
        curObj = $('#editCommodityOther [name="contractId"]');
    }
    param = {
        obj: curObj,
        customer: customer,
        contract: curObj.val()
    }
    $("#newContractInfo .fileCon1 .fileIm").each(function () {
        let itemf = $(this).find(".hd").html();
        info.fileCon1.push(JSON.parse(itemf))
    })
    info.fileCon2 = [];
    $("#newContractInfo .fileCon2 .fileIm").each(function () {
        let itemf = $(this).find(".hd").html();
        info.fileCon2.push(JSON.parse(itemf))
    })
    info.goodList = [];
    $("#newContractInfo .goodList .gsIm").each(function () {
        let itemg = $(this).find(".hd").html();
        info.goodList.push(JSON.parse(itemg))
    })
            let data = { 'customerId': customer }
            let filePath = '',fileName = '';
            if( info.fileCon2.length > 0  ){
                filePath = info.fileCon2[0].filename
                fileName = info.fileCon2[0].originalFilename
            }
            let goods = []
            if(info.goodList && info.goodList.length > 0){
                info.goodList.forEach(function(ig, index){
                    goods.push({
                        "productId":ig.id,
                        "order":index
                    })
                })
            }
            let imgs = []
            if(info.fileCon1 && info.fileCon1.length > 0){
                info.fileCon1.forEach(function(im, index){
                    imgs.push({
                        "filePath": im.filePath,
                        "order":index ,
                        "type":"1",
                        "title": im.title
                    })
                })
            }
            data.contractBase = {
                "sn": info.cNo,
                "signTime": info.cSignDate,
                "validStart": info.cStartDate,
                "validEnd": info.cEndDate,
                "filePath": filePath,
                "fileName": fileName,
                "memo": info.cMemo,
                "contractBaseImages": imgs,
                "productList": goods,
            }
            data.contractBase = JSON.stringify( data.contractBase )
            $.ajax({
                "url": '../sales/addContractBase.do',
                "data": data ,
                success:function (res) {
                    let status = res.status
                    if(status === 1){
                        getContractBaseList(param);
                        bounce_Fixed2.cancel();
                    }else{
                        layer.msg("操作失败！");
                    }
                },
                complete:function(){  }
            })
}
// creator: 李玉婷，2021-05-27 9:47:12，合同查看
function contractScan(cid) {
    $.ajax({
        'url' : "../sales/getContractBase.do",
        "data":{ "id": cid },
        success:function (res) {
            var newcInfo = res.data
            var status = res.status
            if(status != 1){
                layer.msg("获取合同信息失败！");
                return false;
            }
            bounce_Fixed2.show($("#cScan"))
            $("#cScan").data("cid",cid)
            $("#cScan .cNos").html(newcInfo.sn)
            $("#cScan .create").html("&nbsp;&nbsp;" +newcInfo.createName + "&nbsp;&nbsp;" +new Date(newcInfo.createDate).format("yyyy-MM-dd hh:mm:ss"))
            $("#cScan .cSignDates").html(new Date(newcInfo.signTime).format("yyyy-MM-dd"))
            $("#cScan .cvalidDates").html(new Date(newcInfo.validStart).format("yyyy-MM-dd") + '至' + (new Date(newcInfo.validEnd).format("yyyy-MM-dd")))
            $("#cScan .cMemos").html(newcInfo.memo)
            let imgStr1 = ``,fileSect = ``;
                newcInfo.contractBaseImages.forEach(function(bsIm){
                imgStr1 += `<span class="fileImScan" data-fun="imgScan" data-path="${bsIm.filePath}">
                                  <span>${bsIm.orders + 1 }</span>
                                  <span class="hd">${JSON.stringify(bsIm) }</span>
                             </span>`
            })
            $("#cScan .cImgs").html(imgStr1)
            if (newcInfo.filePath && handleNull(newcInfo.filePath) != "") {
                fileSect = `<a class="ty-color-blue node" data-fun="cWord" data-path="${newcInfo.filePath}" path="${newcInfo.filePath}">查看</a>`;
            }
            $("#cScan .cWord").html(fileSect)
            let productList = newcInfo.productList || []
            $("#cScan .gNum").data('list', productList).html(productList.length)
            let enabledList = newcInfo.enabledList || [] , enableStr= ``;
            enabledList.forEach(function (enIm) {
                enableStr += `
                <p>
                    <span>${ enIm.enabled === 1 ? "恢复履约/重启合作" : "暂停履约/终止合作" }</span>
                    <span class="enName">${ enIm.updateName }</span>
                    <span>${ new Date(enIm.enabledTime).format("yyyy-MM-dd hh:mm:ss") }</span>
                </p>`;
            })
            $("#cScan .enabledList").html(enableStr)
        }
    })
}
laydate.render({elem: '.cSignDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cStartDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cEndDate', format: 'yyyy-MM-dd'});




