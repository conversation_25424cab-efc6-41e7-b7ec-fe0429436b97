/**
 * Created by Administrator on 2017/8/14 0014.
 */

var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#knowTip"));
bounce_Fixed2.cancel();
$(function () {
    getIndexData();

    $("#checkInOutInfo .ty-secondTab li").on("click",function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        var index = $(this).index();
        if(index === 0){
            getStorageAcceptList(1,20,1);
            $("#inStorageInfo").show().siblings().hide();
        }else{
            getDeliveryList(1,20,8)
            $("#outStorageInfo").show().siblings().hide();
        }
    });
    // 单选按钮事件
    $(".ty-radio").on("click",function () {
        $(this).addClass("ty-radioActive").siblings().removeClass("ty-radioActive");
        $(this).siblings().children("i").attr("class","fa fa-circle-o")
        $(this).children("i").attr("class","fa fa-dot-circle-o")
        $(this).siblings(".judgmentQuantity").val($(this).attr("value"));
        if($(this).attr("value") === "1"){
            $(this).siblings("#productNum").show()
            $(this).siblings("#productNum").children("input").focus();
        }else{
            $(this).siblings("#productNum").hide()
        }
    });
    // 点击body隐藏更多
    $("body").on("click",function () {
        $(".hel").hide()
    });
    $(".seemore").on("click",".cz_handle",function (e) {
        $(".hel").hide()
        $(this).find(".hel").show();
        e.stopPropagation()
    });
});

// ====================== 仓库管理-成品库 ========================//
function getIndexData() {
    $("#singleHouse tbody").html('');
    $.ajax({
        url:"../finished/getHomeData.do" ,
        data:'',
        success:function(data){
            var getData = data;
            $("#waitingFor").html(getData.waitingFor);
            $("#alreadySet").html(getData.alreadySet);
            $("#dairuku").html(getData.dairuku);
            $("#daichuku").html(getData.daichuku);
            $("#finishedWarehouse [houseReq]").each(function(){
                var name = $(this).data('name');
                if (name == 'kuwei') {
                    $(this).html(chargeNull(getData["kuwei"])+'/'+chargeNull(getData["kongwei"]));
                }else if (name == 'zl') {
                    $(this).html(chargeNull(getData[name])+'种');
                }else{
                    $(this).html(chargeNull(getData[name]));
                }
            })
            if (getData.ckList.length > 0) {
                var ckList = getData.ckList;
                var html = '';
                for(var i=0;i<ckList.length;i++){
                    html +=
                        '<tr data-id="'+ ckList[i].id +'">' +
                        '    <td class="td-lightOrange">' +
                        '        <h5>'+ ckList[i].warehouseName +'</h5>' +
                        '        <span class="jumpWarehouse">进入该仓库</span>' +
                        '    </td>' +
                        '    <td>' +
                        '        <p>区域数量</p>' +
                        '        <p>货架数量</p>' +
                        '        <p>库位总数/空库位数</p>' +
                        '    </td>' +
                        '    <td>' +
                        '        <p>'+ ckList[i].quyu +'</p>' +
                        '        <p>'+ ckList[i].huojia +'</p>' +
                        '        <p>'+ ckList[i].kuwei + '/' + ckList[i].kongwei +'</p>' +
                        '    </td>' +
                        '    <td><p class="bg-lightOrange">库内货物概览</p></td>' +
                        '    <td>' +
                        '        <p>种类总数</p>' +
                        '        <p>净重合计</p>' +
                        '        <p>总重合计</p></td>' +
                        '    <td>' +
                        '        <p>'+ ckList[i].zl +'种</p>' +
                        '        <p>未知</p>' +
                        '        <p>未知</p>' +
                        '    </td>' +
                        '</tr>';
                }
                $("#singleHouse tbody").html(html);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-02-03 17:00:06，去处理
function stockJump(item) {
    $(".container_item").children("div").hide();
    $(".container_item").children("div").eq(item).show();
    if (item == '0' || item == 0) {
        $("#ye_accept").hide();
        $(".stockJump").hide();
    } else{
        $("#ye_accept").show();
        $(".stockJump").show();
    }
    let json = {"num": item}
    if (item == 3 || item == 7) {json.inSuspend = 0;}
    jumpTo(json, 1, 20);
}
function jumpTo(param, cur, pageTotal) {
    let num = param.num;
    switch (num) {
        case 1:
            getWaitingForLocationList(cur, pageTotal);
            break;
        case 2:
            resetLocationList(cur, pageTotal);
            break;
        case 3:
            getPdByCategory(param.inSuspend,'',"",cur, pageTotal);
            break;
        case 4:
            getStorageAcceptList(cur, pageTotal,0);
            break;
        case 5:
            getOutStorageList(cur, pageTotal);
            break;
        case 6:
            getStorageAcceptList(cur,pageTotal,1);
            $(".ty-secondTab li").eq(0).addClass("ty-active").siblings("li").removeClass("ty-active");
            $("#inStorageInfo").show().siblings().hide();
            break;
        case 7:
            var searchKey = $.trim($("#searchKey").val());
            keywordSearch(searchKey, cur, pageTotal);
            break;
    }
}
/*-----获取列表-----*/
// creator: 李玉婷，2020-02-18 11:07:32，获取待录入初始库存列表
function getWaitingForLocationList(currPage,pageSize){
    $("#waitingForEntry tbody").html('');
    $("#ye_accept").html("");
    $("#waitingTotle").html("0");
    $.ajax({
        url:"../finished/getWaitingForLocation.do" ,
        data:{
            "currPage": currPage,
            "pageSize": pageSize
        },
        success:function(resData){
            //设置分页
            var totalPage = resData["totalPage"];//总页数
            var curr = resData["currPage"];//当前页
            var totalRows = resData["totalRows"];//当前页
            var partOne = $(".container_item>div:visible").data('part');
            var jsonStr = JSON.stringify({"num": partOne}) ;
            $("#waitingTotle").html(totalRows);

            setPage( $("#ye_accept") , curr ,  totalPage , "accept", jsonStr );
            var list = resData.data;
            if (list && list.length > 0) {
                var html = '';
                for(var a=0;a<list.length;a++){
                    html +=
                        '<tr data-info=\''+ JSON.stringify(list[a]) +'\'>' +
                        '    <td>'+ handleNull(list[a].innerSn) +'</td>' +
                        '    <td>'+ handleNull(list[a].name) +'</td>' +
                        '    <td>'+ handleNull(list[a].model) +'</td>' +
                        '    <td>'+ handleNull(list[a].specifications) +'</td>' +
                        '    <td class="createInfo">'+ list[a].createName + '  ' + new Date(list[a].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ list[a].unit +'</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue" onclick="enterInitialStock($(this))">录入初始库存数量/库位</span>' +
                        '    </td>' +
                        '</tr>'
                }
                $("#waitingForEntry tbody").html(html);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-02-19 11:13:11，重新选择库位
function resetLocationList(currPage,pageSize){
    $("#resetStock tbody").html('');
    $("#ye_accept").html("");
    $.ajax({
        url:"../finished/getPdProductList.do" ,
        data:{
            currPage: currPage,
            pageSize: pageSize,
            type: ''
        },
        success:function(data){
            if (data.code == '200') {
                //设置分页
                var totalPage = data["totalPage"];//总页数
                var curr = data["currPage"];//当前页
                var partOne = $(".container_item>div:visible").data('part');
                var jsonStr = JSON.stringify({"num": partOne}) ;
                setPage( $("#ye_accept") , curr ,  totalPage , "accept", jsonStr );

                var html = '';
                var list = data.data;
                for (var a=0;a<list.length;a++) {
                    html +=
                        ' <tr data-id="'+ list[a].id +'">' +
                        '    <td>'+ handleNull(list[a].innerSn) +'</td>' +
                        '    <td>'+ handleNull(list[a].name) +'</td>' +
                        '    <td>'+ handleNull(list[a].model) +'</td>' +
                        '    <td>'+ handleNull(list[a].specifications) +'</td>' +
                        '    <td class="createInfo">'+ list[a].createName +'&nbsp;&nbsp;'+ new Date(list[a].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ list[a].unit +'</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue" onclick="reSelectStock($(this))">重新选择库位</span>' +
                        '    </td>' +
                        '</tr>';
                }
                $("#resetStock tbody").html(html);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-02-19 11:13:11，按类别查看成品列表
function getPdByCategory(inSuspend,kindId,kindType,currPage,pageSize){
    let url = ``;
    $("#byCategory").data('inSuspend',inSuspend);
    $("#byCategoryCon").html('');
    $("#kindsTree").html("") ;
    $("#ye_accept").html("");
    $("#allCount").html('0');
    if ((kindId == "" || kindId =='全部') && inSuspend == 0){
        $(".suspendBtn").show();
        $(".left-bottom").hide();
    } else{
        $(".suspendBtn").hide();
        $(".left-bottom").show();
    }
    if (inSuspend == 1) {
        url = `../finished/getSuspendPdProductList.do  `;
    } else if (inSuspend ==0){
        url = `../finished/getPdProductList.do`;
    }
    $.ajax({
        url:url ,
        data:{
            currPage: currPage,
            pageSize: pageSize,
            id: kindId,
            type: kindType
        },
        success:function(data){

            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            var partOne = $(".container_item>div:visible").data('part');
            var jsonStr = JSON.stringify({"num": partOne, "inSuspend":inSuspend}) ;
            setPage( $("#ye_accept") , curr ,  totalPage , "accept", jsonStr );

            var html = '', tree = '';
            var list = data.data;
            var mtCategories = data.mtCategories;
            $(".between").height(0);
            if (mtCategories && mtCategories.length > 0){
                for(var i=0; i<mtCategories.length; i++) {
                    tree +=
                        "<li class='faceul1'>" +
                        "   <a data-stop='"+ inSuspend +"' onclick='showkindBtn($(this))'><span>" + mtCategories[i].name + "</span>" +
                        "   <span>（"+ mtCategories[i].num +"种）</span>" +
                        "   <div class='hd'>" +
                        "       <span class='kindId' data-type='"+  mtCategories[i].type +"'>" + mtCategories[i].id + "</span>" +
                        "   </div>" +
                        " </a> </li>";
                }
                $("#kindsTree").html(tree) ;
            }
            if ((kindId == '' || kindId =='全部') && inSuspend ==0){
                var strson_1 = "<span class='go2Cat' data-stop='"+ inSuspend +"' onclick='showkindNav($(this))'>"+
                    "<span>全部成品</span>"+
                    "<span class='hd' data-type=''>"+ "" +"</span>"+
                    "</span>";
                $("#categoryName").html('全部成品');
                $("#curID").html(strson_1);
                $("#suspendCommodyNum").html(data.suspendNum);
            }
            $("#allCount").html(data.totalRows);
            if (list && list.length >0){
                for (var a=0;a<list.length;a++) {
                    html +=
                        ' <tr data-id="'+ list[a].id +'">' +
                        '    <td>'+ handleNull(list[a].innerSn) +'</td>' +
                        '    <td>'+ handleNull(list[a].name) +'</td>' +
                        '    <td>'+ handleNull(list[a].model) +'</td>' +
                        '    <td>'+ handleNull(list[a].specifications) +'</td>' +
                        '    <td class="createInfo">'+ list[a].createName +'&nbsp; '+ new Date(list[a].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ list[a].unit +'</td>' +
                        '    <td>'+ parseFloat(Number(list[a].minimumiStock || "0").toFixed(4)) +'</td>' +
                        '    <td>'+ list[a].currentStock +'</td>' +
                        '    <td><span class="ty-color-blue" onclick="stockInitial($(this))">' + list[a].initialStock + '</span></td>' +
                        '    <td><span class="ty-color-blue" onclick="stockHold($(this))">' + list[a].num + '个</span></td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue cz_handle">查看' +
                        '           <ul class="hel" style="display: none">' +
                        '            <li class="ulli ty-disabled"><span>盘点记录</span></li>' +
                        '            <li class="ulli ty-disabled"><span>出入库记录</span></li>' +
                        '            <li class="ulli" onclick="initStockUpdateRecord($(this))"><span>初始库存修改记录</span></li>' +
                        '            <li class="ulli" onclick="safeStockUpdateRecord($(this))"><span>最低库存修改记录</span></li>' +
                        '        </ul>' +
                        '        </span>' +
                        '    </td>' +
                        '</tr>';
                }
                $("#byCategoryCon").html(html);
            }
            var conHeight = $(".bigContainer").height() ;
            conHeight++;
            $(".between").height(conHeight);
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-02-25 09:37:54，搜索
function keywordSearch(keyword,currPage,pageSize) {
    $("#resultList tbody").html('');
    $("#ye_accept").html("");
    $.ajax({
        url:"../finished/search.do" ,
        data:{
            currPage: currPage,
            pageSize: pageSize,
            param: keyword
        },
        success:function(data){
            if (data.code == '200') {
                //设置分页
                var totalPage = data["totalPage"];//总页数
                var curr = data["currPage"];//当前页
                var partOne = $(".container_item>div:visible").data('part');
                var jsonStr = JSON.stringify({"num": partOne}) ;
                setPage( $("#ye_accept") , curr ,  totalPage , "accept", jsonStr );

                var html = '';
                var list = data.data;
                for (var a=0;a<list.length;a++) {
                    html +=
                        ' <tr data-id="'+ list[a].id +'">' +
                        '    <td>'+ handleNull(list[a].innerSn) +'</td>' +
                        '    <td>'+ handleNull(list[a].name) +'</td>' +
                        '    <td>'+ handleNull(list[a].model) +'</td>' +
                        '    <td>'+ handleNull(list[a].specifications) +'</td>' +
                        '    <td class="createInfo">'+ list[a].createName + '  '+ new Date(list[a].createDate).format('yyyy/MM/dd hh:mm:ss')  +'</td>' +
                        '    <td>'+ list[a].unit +'</td>' +
                        '    <td>'+ parseFloat(Number(list[a].minimumiStock || "0").toFixed(4)) +'</td>' +
                        '    <td>'+ parseFloat(Number(list[a].currentStock || "0").toFixed(4)) +'</td>' +
                        '    <td><span class="ty-color-blue" onclick="stockInitial($(this))">' + parseFloat(Number(list[a].initialStock || "0").toFixed(4)) + '</span></td>' +
                        '    <td><span class="ty-color-blue" onclick="stockHold($(this))">' + list[a].num + '个</span></td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue cz_handle">查看' +
                        '           <ul class="hel" style="display: none">' +
                        '            <li class="ulli ty-disabled"><span>盘点记录</span></li>' +
                        '            <li class="ulli ty-disabled"><span>出入库记录</span></li>' +
                        '            <li class="ulli" onclick="initStockUpdateRecord($(this))"><span>初始库存修改记录</span></li>' +
                        '            <li class="ulli" onclick="safeStockUpdateRecord($(this))"><span>最低库存修改记录</span></li>' +
                        '        </ul>' +
                        '        </span>' +
                        '    </td>' +
                        '</tr>';
                }
                $("#resultList tbody").html(html);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-01-20 09:38:33，录入初始库存数量/库位
function enterInitialStock(obj) {
    //初始化
    $("#initialStockList tbody").html('');
    $(".checkCondition .fa").removeClass('fa-check-square-o').addClass("fa-square-o");
    $(".storesAble").find("li").eq(0).siblings().remove();
    $("#enterInitialStock select").val(0);
    $("#enterInitialStock input").val("");
    $("#enterInitialStock select").siblings(".ty-color-blue").hide();
    var info = obj.parents('tr').data('info');
    $('#enterInitialStock').data("id",info.id);
    var goodsHtml =
        '<tr>' +
        '    <td>'+ handleNull(info.innerSn) + '</td>' +
        '    <td>'+ handleNull(info.name) + '</td>' +
        '    <td>'+ handleNull(info.model) + '</td>' +
        '    <td>'+ handleNull(info.specifications) + '</td>' +
        '    <td class="createInfo">'+ info.createName + '&nbsp;&nbsp;' + new Date(info.createDate).format('yyyy/MM/dd hh:mm:ss') + '</td>' +
        '    <td>'+ info.unit  + '</td>' +
        '    <td><span class="floatLocation">0</span></td>' +
        '    <td><span class="holdLocation">0</span>个</td>' +
        '</tr>';
    $("#initialStockList tbody").html(goodsHtml);
    $(".goodsUnit").html(info.unit);
    getLocationList(1);

    bounce_Fixed.show($("#enterInitialStock"));
    bounce_Fixed.everyTime('0.5s','enterStock',function(){
        if($(".checkCondition:visible .fa").hasClass('fa-square-o')){
            if ($(".notSelectStores:visible").length > 0){
                $(".notSelectStores").hide();
                $(".selectSect:visible .storesAble").show();
            }
            var fillNum = 0, storeNum = 0;
            $(".storesAble:visible input").each(function(){
                if ($(this).val() != '') fillNum += Number($(this).val());
            });
            $(".storesAble:visible select").each(function(){
                var tt = $(this).val();
                if ($(this).val() != null && $(this).val() !=0 && $(this).val() !="") {
                    storeNum++;
                }
            });
            $(".holdLocation").html(storeNum);
            $(".floatLocation").html(parseFloat(Number(fillNum || "0").toFixed(4)));
        } else if($(".checkCondition .fa").hasClass('fa-check-square-o')){
            if ($(".notSelectStores:visible").length <= 0){
                $(".notSelectStores").show();
                $(".selectSect:visible .storesAble").hide();
            }
            var temp = parseFloat(Number($(".onlyNumber:visible").val() || "0").toFixed(4));
            $(".holdLocation:visible").html('0');
            $(".floatLocation:visible").html(temp);
        }
    });
}
// creator: 李玉婷，2020-02-27 09:09:06，获取库位列表
function getLocationList(type) {
    if (type == 1){
        $("#addInitStore").html("");
    }else{
        $("#resetInitStore").html("");
    }
    $.ajax({
        url:"../finished/getLocationList.do" ,
        data: '',
        success:function(data){
            var list = data.data;
            var options = '<option value="0"></option>';
            if (list && list.length > 0){
                for (var z=0;z<list.length;z++){
                    options += '<option value="'+ list[z].locationId +'">'+ list[z].locationCode +'</option>'
                }
            }
            if (type == 1){
                $("#addInitStore").html(options);
            }else{
                $("#resetInitStore").html(options);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-02-18 15:02:03，录入初始库存数量/库位确定
function addInitialStock(){
    var locationList = [], json = {};
    var id = $('#enterInitialStock').data("id");
    if ($(".checkCondition:visible").find('.fa-check-square-o').length > 0) {
        if ($(".onlyNumber:visible").val() != ''){
            json = {
                'locationId': '',
                'amount': $(".onlyNumber:visible").val()
            };
            locationList.push(json);
        }else{
            $("#nullTip #nullTipMs").html('还有必填项尚未填写!');
            bounce_Fixed2.show($("#nullTip"));
            return false;
        }
    }else {
        var emty = 0;
        $(".storesFill:visible input").each(function(){
            if($(this).val() == '') emty++;
        });
        $(".storesFill:visible select").each(function(){
            if($(this).val() == 0 || $(this).val() == '') emty++;
        });
        if(emty > 0){
            $("#nullTip #nullTipMs").html('还有必填项尚未填写!');
            bounce_Fixed2.show($("#nullTip"));
            return false;
        }else{
            $(".storesAble:visible li").each(function () {
                json = {
                    'locationId': $(this).find('select').val(),
                    'amount': $(this).find('input').val()
                };
                locationList.push(json);
            });
        }
    }
    locationList = JSON.stringify(locationList);
    $.ajax({
        url:"../finished/addInitialStock.do" ,
        data:{
            "id": id,
            "locationList": locationList
        } ,
        success:function( data ){
            if(data.code == 200){
                bounce_Fixed.cancel();
                var partOne = $(".container_item>div:visible").data('part');
                let pram = {'num': partOne}
                jumpTo( pram, 1,20);
                getIndexData();
            }else{
                $("#tip #tipMs").html("系统错误，请重试!") ;
                bounce_Fixed2.show($("#tip")) ;
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    })
}
// creator: 李玉婷，2020-02-18 16:13:33，库位选择事件
function fillStore(obj) {
    if (obj.val() != 0){
        obj.next("span").show();
    } else{
        obj.next("span").hide();
    }
}
// creator: 李玉婷，2020-01-20 09:23:01，直接录入初始库存数量，暂不选择库位
function turnCheck (obj) {
    if (obj.hasClass('fa-square-o')) {
        obj.removeClass('fa-square-o').addClass('fa-check-square-o');
    }else{
        obj.removeClass('fa-check-square-o').addClass('fa-square-o');
    }
}
// creator: 李玉婷，2020-01-20 10:09:44，增加新库位
function newStore() {
    var emty = 0;
    $(".storesAble:visible select").each(function(){
        if ($(this).val() == 0) emty++;
    });
    $(".storesFill:visible input").each(function(){
        if($(this).val() == '') emty++;
    });
    if(emty > 0){
        $("#nullTip #nullTipMs").html('录完一个库位的数据后才能增加新库位!');
        bounce_Fixed2.show($("#nullTip"));
    } else{
        $.ajax({
            url:"../finished/getLocationList.do" ,
            data: '',
            success:function(data){
                var list = data.data;
                var options = '<option value="0"></option>';
                if (list && list.length > 0){
                    for(var a=0;a<list.length;a++){
                        options += '<option value="'+ list[a].locationId +'">'+ list[a].locationCode +'</option>'
                    }
                }
                var unit = $('.bg-yellow:visible tbody tr').find("td").eq(5).html();
                var str =
                    '<li>' +
                    '    <div>' +
                    '        <span class="gapRt">库位</span>' +
                    '        <select onchange="fillStore($(this))">' + options +
                    '        </select>' +
                    '        <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>' +
                    '    </div>' +
                    '    <div>' +
                    '        <span class="gapRt">数量</span>' +
                    '        <input type="text" onkeyup="clearNoNum(this)" /><span class="gapLt">'+ unit +'</span>'+
                    '    </div>'+
                    '</li>';
                $(".storesFill:visible").append(str);
            },
            error:function(){
                $("#tip #tipMs").html("系统错误，请重试!") ;
                bounce_Fixed2.show($("#tip")) ;
            }
        });
    }
}
// creator: 李玉婷，2020-01-20 10:22:39，查看该库位情况
function seeStoresDetail(obj) {
    $(".storesCreateInfo tbody").html('');
    $(".scienceCreateInfo tbody").html('');
    var id= obj.siblings("select").val();
    $.ajax({
        url:"../finished/getLocationDetail.do" ,
        data:{
            'locationId': id
        },
        success:function(data){
            var detail = data.data, part1 = '', part2 = '', list = detail.list;
            $("#seeStoresName").html(detail.warehouseCode + '-' + detail.locationCode);
            part1 +=
                '<tr>' +
                '    <td>'+ handleNull(detail.warehouseCode ) +'</td>' +
                '    <td>'+ handleNull(detail.regionCode) +'</td>' +
                '    <td>'+ handleNull(detail.shelfCode) +'</td>' +
                '    <td>'+ handleNull(detail.layer) +'</td>' +
                '    <td></td>' +
                '    <td class="createInfo">'+ handleNull(detail.createName) + '&nbsp;&nbsp;' + new Date(detail.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                '</tr>';
            $(".storesCreateInfo tbody").html(part1);
            if (list && list.length > 0){
                for(var t=0;t<list.length;t++) {
                    part2 += '<tr>' +
                        '     <td>'+ handleNull(list[t].innerSn) +'</td>' +
                        '     <td>'+ handleNull(list[t].name) +'</td>' +
                        '     <td>'+ handleNull(list[t].model) +'</td>' +
                        '     <td>'+ handleNull(list[t].specifications) +'</td>' +
                        '     <td>'+ list[t].unit +'</td>' +
                        '     <td>'+ parseFloat(Number(list[t].amount || "0").toFixed(4)) +'</td>' +
                        ' </tr>';
                }
                $(".scienceCreateInfo tbody").html(part2);
            }
            bounce_Fixed2.show($("#seeStores"));
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-02-05 15:11:28，初始库存-修改记录
function initStockUpdateRecord(obj) {
    $("#initUpdateRecord tbody").html('');
    var itemId = obj.parents("tr").data("id");
    stockRecordCommon(itemId, 2);
}
// creator: 李玉婷，2020-02-05 15:11:28，最低库存-修改记录
function safeStockUpdateRecord(obj) {
    $("#safeRecord tbody").html("");
    var itemId = obj.parents("tr").data("id");
    var html='';
    $.ajax({
        url:"../finished/getMinimumStockRecord.do" ,
        data:{
            id: itemId
        },
        success:function(data){
            if (data.list.length > 0) {
                var list = data.list;
                for (var i=0; i<list.length;i++){
                    html +=
                        '<tr>' +
                        '    <td>'+ list[i].updateName +'</td>' +
                        '    <td>'+ new Date(list[i].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ list[i].customerName +'</td>' +
                        '    <td>'+ list[i].before +'</td>' +
                        '    <td>'+ list[i].after +'</td>' +
                        '</tr>';
                }
                $("#safeRecord tbody").html(html);
            }
            bounce.show($("#safeStockUpdateRecord"));
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-02-04 16:21:35，重新选择库位初始化
function reSelectStock(obj) {
    //初始化
    $("#reselectLocation tbody").html('');
    $(".storesAble").find("li:not(:first)").remove();
    $(".checkCondition .fa").removeClass('fa-check-square-o').addClass("fa-square-o");
    $("#reselectLocation select").val(0);
    $("#reselectLocation input").val("");
    $("#reselectLocation select").siblings(".ty-color-blue").hide();
    $("#resetCurrentStation").html('');
    var getId = '';
    if (obj == null) {
        getId = $("#holdStockSee").data("id");
    } else {
        getId = obj.parents("tr").data("id");
    }
    getLocationList(2);
    updateLocationCommon(getId);
    bounce_Fixed.show($('#reselectLocation'));
    bounce_Fixed.everyTime('0.5s','reselectStock',function(){
        if($(".checkCondition:visible .fa").hasClass('fa-square-o')){
            if ($(".notSelectStores:visible").length > 0){
                $(".notSelectStores").hide();
                $(".selectSect:visible .storesAble").show();
            }
        } else if($(".checkCondition:visible .fa").hasClass('fa-check-square-o')){
            if ($(".notSelectStores:visible").length <= 0){
                $(".notSelectStores").show();
                $(".selectSect:visible .storesAble").hide();
            }
        }
    });
}
// creator: 李玉婷，2020-02-24 16:02:15，重新选择库位提交
function updateLocation(type) {
    var updateId = $("#reselectList tbody tr").data('id');
    var locationList = [], json = {};
    if ($(".checkCondition:visible").find('.fa-check-square-o').length > 0) {
        if ($(".onlyNumber:visible").val() != ''){
            json = {
                'locationId': '',
                'amount': $(".onlyNumber:visible").val()
            };
            locationList.push(json);
        }else{
            $("#nullTip #nullTipMs").html('还有必填项尚未填写!');
            bounce_Fixed2.show($("#nullTip"));
            return false;
        }
    }else {
        var emty = 0;
        $(".storesFill:visible input").each(function(){
            if($(this).val() == '') emty++;
        });
        $(".storesFill:visible select").each(function(){
            if($(this).val() == null || $(this).val() == 0) emty++;
        });
        if(emty > 0){
            $("#nullTip #nullTipMs").html('还有必填项尚未填写!');
            bounce_Fixed2.show($("#nullTip"));
            return false;
        }else{
            $(".storesAble:visible li").each(function () {
                json = {
                    'locationId': $(this).find('select').val(),
                    'amount': $(this).find('input').val()
                };
                locationList.push(json);
            });
        }
    }
    locationList = JSON.stringify(locationList);
    $.ajax({
        url:"../finished/updateLocation.do" ,
        data:{
            "id": updateId,
            "locationList": locationList
        } ,
        success:function( data ){
            if(data.code == 200){
                bounce_Fixed.cancel();
                var partOne = $(".container_item>div:visible").data('part');
                let pram = {'num': partOne}
                jumpTo( pram, 1,20);
                if (partOne == 3 || partOne == 7) {
                    setHoldData(updateId);
                }
            }else{
                $("#tip #tipMs").html("系统错误，请重试!") ;
                bounce_Fixed2.show($("#tip")) ;
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    })
}
// creator: 李玉婷，2020-02-24 20:08:36，重新选择库位公用接口
function updateLocationCommon(itemId) {
    $.ajax({
        url:"../finished/getLocationListByProduct.do" ,
        data:{
            id: itemId
        },
        success:function(data){
            if (data.code == 200) {
                var info = data.data;
                var list = info.list;
                var goodsInfo =
                    '<tr data-id="'+ info.id +'">' +
                    '    <td>'+ handleNull(info.innerSn) +'</td>' +
                    '    <td>'+ handleNull(info.name) +'</td>' +
                    '    <td>'+ handleNull(info.model) +'</td>' +
                    '    <td>'+ handleNull(info.specifications) +'</td>' +
                    '    <td class="createInfo">'+ info.createName + '&nbsp;&nbsp;' + new Date(info.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                    '    <td>'+ info.unit +'</td>' +
                    '    <td>'+ parseFloat(Number(info.currentStock || "0").toFixed(4)) +'</td>' +
                    '    <td>'+ info.num +'个</td>' +
                    '</tr>';
                $(".reUnit").html(info.unit);
                $("#reselectList tbody").html(goodsInfo);
                if (list.length > 0){
                    var len = Math.ceil(list.length/10);
                    var tabStr = '';
                    for(var m=0;m<len;m++){
                        tabStr +=
                            '<table class="ty-table ty-table-control gap ">' +
                            '   <tr>' +
                            '       <td rowspan="2" class="td-orange">现况</td>';
                        var size = 8;
                        var render = list.length%8;
                        var locationCode = '<td width="60">库位</td>',locationNum ='<tr><td>数量</td>';
                        if (m==len-1 && render>0) {
                            size = render;
                        }
                        for(var n=0;n<size;n++){
                            var i = m*size + n;
                            locationCode += '<td>'+ list[i].locationCode +'</td>';
                            locationNum += '<td>'+ parseFloat(Number(list[i].amount || "0").toFixed(4)) +'</td>';
                        }
                        tabStr += locationCode + locationNum +
                            '</tr></table>';
                    }
                    $(".noStation").hide();
                    $(".hasStation").show();
                    $("#resetCurrentStation").html(tabStr);
                }else{
                    $(".noStation").show();
                    $(".hasStation").hide();
                }
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-01-20 11:22:34，去盘点
function takeInventory() {
    $("#tip #tipMs").html("请在手机端进行盘点！") ;
    bounce_Fixed2.show($("#tip")) ;
}
// creator: 李玉婷，2020-01-20 14:59:46，初始库存列表
function stockInitial(obj) {
    // var num = obj.html();
    // var itemId = obj.parents("tr").data("id");
    // $("#initialStockRecord tbody").html("");
    // stockRecordCommon(itemId, 1);
    // $("#updateStores").data("id", itemId);
    // if (num && Number(num) > 0) {
    //     $("#updateStoresBtn").attr("class", "ty-btn ty-btn-gray ty-btn-big").removeAttr("onclick");
    // } else {
    //     $("#updateStoresBtn").attr({"class": "ty-btn ty-btn-blue ty-btn-big", "onclick": "updateStores()"});
    // }
}
// creator: 李玉婷，2020-02-24 17:14:59，初始库存查看-公用 ,type:1=初始库存查看,2= 初始库存-修改记录
function stockRecordCommon(itemId,type) {
    var html='';
    $.ajax({
        url:"../finished/getInitialStockRecord.do" ,
        data:{
            id: itemId
        },
        success:function(data){
            if (data.list.length > 0) {
                var list = data.list;
                for (var i=0; i<list.length;i++){
                    html +=
                        '<tr>' +
                        '    <td>'+ list[i].updateName +'</td>' +
                        '    <td>'+ new Date(list[i].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ list[i].customerName +'</td>' +
                        '    <td>'+ list[i].before +'</td>' +
                        '    <td>'+ list[i].after +'</td>' +
                        '</tr>';
                }
            }
            if (type == 1){
                var stock = data.initialStock;
                var unit = data.unit;
                $("#stockAmount").html(stock + '&nbsp;&nbsp;' + unit);
                $("#initialStockRecord tbody").html(html);
                bounce.show($("#initialStockSee"));
            }else{
                $("#initUpdateRecord tbody").html(html);
                bounce.show($("#initStockUpdateRecord"));
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: 李玉婷，2020-02-25 08:15:05，修改初始库存
function updateStores() {
    $("#newStore").val('');
    bounce_Fixed.show($('#updateStores'));
}
// creator: 李玉婷，2020-01-20 16:52:08，修改初始库存提交
function updateStoresSure() {
    var html = '';
    if ($.trim($("#newStore").val()) == '') {
        html = '您还没输入具体数字呢！';
        $("#knowTipMs").html(html);
        bounce_Fixed2.show($("#knowTip"));
    } else if ($.trim($("#newStore").val()) < 0) {
        html =
            '<p>此数字将导致实际库存小于零。</p>' +
            '<p>请输入正确的数字！</p>';
        $("#knowTipMs").html(html);
        bounce_Fixed2.show($("#knowTip"));
    } else{
        var itemId = $("#updateStores").data("id");
        $.ajax({
            url:"../finished/updateInitialStock.do" ,
            data:{
                "id": itemId,
                "num": $.trim($("#newStore").val())
            },
            success:function(data){
                if (data.code == '200') {
                    bounce_Fixed.cancel();
                    $("#initialStockRecord tbody").html('');
                    var ye = $("#ye_accept .yecur").html();
                    var partOne = $(".container_item>div:visible").data('part');
                    stockRecordCommon(itemId, 1);
                    let inSuspend = $("#byCategory").data('inSuspend');
                    let pram = {'num': partOne,'inSuspend': inSuspend};
                    jumpTo(pram, ye,20);
                } else {
                    html =
                        `<p>${data.msg}</p>`;
                    $("#knowTipMs").html(html);
                    bounce_Fixed2.show($("#knowTip"));
                }
            },
            error:function(){
                $("#tip #tipMs").html("系统错误，请重试!") ;
                bounce_Fixed2.show($("#tip")) ;
            }
        });
    }
}
// creator: 李玉婷，2020-01-21 11:02:34，占用库位
function stockHold(obj) {
    $("#holdStockInfo tbody").html("");
    var getId = obj.parents("tr").data("id");
    $("#holdStockSee").data("id", getId);
    setHoldData(getId);
}
// creator: 李玉婷，2020-02-24 21:49:33，获取库位数据
function setHoldData(itemId){
    $("#currentStation").html("");
    $.ajax({
        url:"../finished/getLocationListByProduct.do" ,
        data:{
            id: itemId
        },
        success:function(data){
            if (data.code == 200) {
                var info = data.data;
                var list = info.list;
                var goodsInfo =
                    '<tr>' +
                    '    <td>'+ handleNull(info.innerSn) +'</td>' +
                    '    <td>'+ handleNull(info.name) +'</td>' +
                    '    <td>'+ handleNull(info.model) +'</td>' +
                    '    <td>'+ handleNull(info.specifications) +'</td>' +
                    '    <td class="createInfo">'+ info.createName + '&nbsp;&nbsp;' + new Date(info.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                    '    <td>'+ info.unit +'</td>' +
                    '    <td>'+ info.currentStock +'</td>' +
                    '    <td>'+ info.num +'个</td>' +
                    '</tr>';
                $("#holdStockInfo tbody").html(goodsInfo);
                if (list.length > 0){
                    var len = Math.ceil(list.length/10);
                    var tabStr = '';
                    for(var m=0;m<len;m++){
                        tabStr +=
                            '<table class="ty-table ty-table-control gap">' +
                            '   <tr>' +
                            '       <td rowspan="2" class="td-orange">现况</td>';
                        var size = 8;
                        var render = list.length%8;
                        var locationCode = '<td width="60">库位</td>',locationNum ='<tr><td>数量</td>';
                        if (m==len-1 && render>0) {
                            size = render;
                        }
                        for(var n=0;n<size;n++){
                            var i = m*size + n;
                            locationCode += '<td>'+ handleNull(list[i].locationCode) +'</td>';
                            locationNum += '<td>'+ handleNull(list[i].amount) +'</td>';
                        }
                        locationNum += '</tr>'
                        tabStr += locationCode + locationNum +
                            '</tr></table>';
                    }
                    $("#onlyNumInfo").hide();
                    $("#currentStation").html(tabStr);
                    $("#holdStockSeeReset").html("重新选择库位");
                }else{
                    $("#onlyNumInfo").show();
                    $("#holdStockSeeReset").html("去选择库位");
                }
                bounce.show($("#holdStockSee"));
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
//  返回上一级
function gobackLstLevel(num) {
    var index = $('#curID .go2Cat').length - 2; // 返回全部
    if(num === 1){ index = 0;   }
    $('#curID .go2Cat:eq('+ index +')').click();
}
// 点击选择分类
function showkindBtn(obj){
    isall = false ;
    var name = obj.children("span").eq(0).html();
    var id = obj.children("div.hd").children(".kindId").html();
    var type = obj.children("div.hd").children(".kindId").data("type");
    let stop = obj.data("stop");
    if(id != undefined || id != null){
        var strson_1 = "<span class='go2Cat' data-stop='"+stop+"' onclick='showkindNav($(this))'> < "+
            "<span>"+name+"</span>"+
            "<span class='hd' data-type='"+ type +"'>"+ id +"</span>"+
            "</span>";
        $("#curID").append(strson_1);
        $("#categoryName").html(name);
        getPdByCategory(stop, id , type, 1 , 20 );
    }else{
        $("#mt_tip_ms").html("系统错误，请刷新请刷新重试！");
        $("#mtTip").show().parent().show();
    }
}
// 点击头部导航
function showkindNav( obj ){
    let stop = obj.data("stop");
    var id = obj.children(".hd").html() ;
    var type = obj.children(".hd").data("type");
    var name = obj.children("span").eq('0').html();
    if(id != undefined  ){
        $("#categoryName").html(name);
        obj.nextAll().remove();
        getPdByCategory(stop, id, type , 1 , 20 );
    }else{
        $("#mt_tip_ms").html("系统错误，请刷新请刷新重试！");
        $("#mtTip").show().parent().show();
    }
}
// ====================== 仓库管理-入库受理 ========================//

/* creator：张旭博，2017-08-22 16:03:05，获取成品入库申请列表 */
function getStorageAcceptList(currPage,pageSize,flag) {
    $("#ye_accept").html("");
    $.ajax({
        url:"../inOutStock/StockApply.do" ,
        data:{
            "pageSize":pageSize,
            "currPage":currPage,
            "flag":flag
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){

            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            var partOne = $(".container_item>div:visible").data('part');
            var jsonStr = JSON.stringify({"num": partOne}) ;
            setPage( $("#ye_accept") , curr ,  totalPage , "accept", jsonStr );

            var mtStockList = data["mtStockList"];
            var storageAcceptListStr = "";
            for(var j = 0 ;j < mtStockList.length ; j++){
                var approcessList = mtStockList[j].approcessList;
                var processDetailStr = getProcessStr(approcessList);
                if(flag === 0){
                    var inFactStr = '';
                    var handelStr = '<td><span class="ty-color-blue" onclick="storageAcceptHandleBtn($(this))">操作</span></td>';
                }else if(flag ===1){
                    var inFactStr = '<td>'+parseFloat(Number(mtStockList[j].inFact || "0").toFixed(4))+'</td>';
                    var handelStr = '';
                }
                storageAcceptListStr += '<tr id="'+mtStockList[j].id+'">' +
                                            '<td>'+(j+1)+'</td>'+ // 序号
                                            '<td>'+mtStockList[j].createDate.substring(0,10)+'</td>'+ //申请时间
                                            '<td>'+mtStockList[j].outerName+'</td>'+    //商品代号
                                            '<td>'+mtStockList[j].outerSn+'</td>'+    //商品名称
                                            '<td>'+mtStockList[j].innerSn+'</td>'+    //产品图号
                                            '<td>'+mtStockList[j].innerSnName+'</td>'+    //产品名称
                                            '<td>'+mtStockList[j].unit+'</td>'+    //单位
                                            '<td>'+parseFloat(Number(mtStockList[j].inPlan || "0").toFixed(4))+'</td>'+    //申请入库数量
                                            inFactStr+                              //实际入库数量
                                            '<td>'+mtStockList[j].manufactureDate.substring(0,10)+'</td>'+    //生产日期
                                            '<td>'+mtStockList[j]["invalidDate"].substring(0,10)+'</td>'+    //产品到期日
                                            processDetailStr+
                                            handelStr+  //操作
                                        '</tr>';
            }
            if(flag === 0){
                $("#inStorage tbody").html(storageAcceptListStr);
            }else if(flag ===1){
                $("#inStorageInfo tbody").html(storageAcceptListStr);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        },
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-06 14:21:43，获取成品出库申请列表 */
function getOutStorageList(currPage,pageSize) {
    $.ajax({
        url:"../inOutStock/OutputList.do" ,
        data:{
            "pageSize":pageSize,
            "currPage":currPage
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){

            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            $("#ye_accept").html("");
            var partOne = $(".container_item>div:visible").data('part');
            var jsonStr = JSON.stringify({"num": partOne}) ;
            setPage( $("#ye_accept") , curr ,  totalPage , "accept", jsonStr );

            var outStockList = data["list"];
            var outStorageListStr = "";
            for(var j = 0 ;j < outStockList.length ; j++){
                outStorageListStr +=    '<tr id="'+outStockList[j].id+'">' +
                                            '<td>'+formatTime(outStockList[j].delivery_date,false)+'</td>'+   //计划出库日期
                                            '<td>'+outStockList[j].customer_name+'</td>'+   //客户名称
                                            '<td>'+handleNull(outStockList[j].address)+'</td>'+         //收货地址
                                            '<td>'+outStockList[j].delivery_way+'</td>'+    //计划的发货方式
                                            '<td>'+handleNull(outStockList[j].sn)+'</td>'+          //订单号
                                            '<td>'+outStockList[j].gsum+'</td>'+            //商品类别总数
                                            '<td>'+outStockList[j].pack+'</td>'+            //货物总件数
                                            '<td>'+outStockList[j].carrier+'</td>'+         //搬运负责人
                                            '<td>'+formatTime(outStockList[j].arrive_date,false)+'</td>'+     //计划到达日期
                                            '<td>'+formatTime(outStockList[j].apply_date,true)+'</td>'+      //申请提交时间
                                            '<td>'+formatTime(outStockList[j].update_date,true)+'</td>'+     //申请最后修改时间
                                            '<td>'+outStockList[j].applicant_name+'</td>'+  //提交者
                                            '<td>'+                                         //操作
                                                '<span class="ty-color-blue" onclick="approveOutStorageBtn($(this))">审批</span>'+
                                                '<span class="ty-color-blue" onclick="checkOutStorageBtn($(this))">查看</span>'+
                                            '</td>'+
                                        '</tr>';
            }

            $("#outStorage").find("tbody").html(outStorageListStr);

        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-18 09:46:38，出库评审弹窗赋值 */
function approveOutStorageBtn(selector){
    bounce.show($("#outStorageApply"));
    var outid = selector.parent().parent().attr("id");


    $.ajax({
        url:"../inOutStock/lookOutput.do" ,
        data:{
            "outid":outid
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            //设置分页
            // var mycurr      = data.currPage;
            // var ttlPage     = data.totalPage;
            var base = data["data"];
            var deliveryList    = base.list;
            var deliveryListStr = '';
            $("#outStorageApply .applyAll").html('申请人： <span class="ty-color-blue">'+base.applicant_name+'</span> 申请时间：<span class="ty-color-blue">'+formatTime(base.apply_date,false) +'</span>');
            $("#outStorageApplyBase").attr("oid",outid);
            $("#outStorageApplyBase .customerName").html(base.customer_name);
            $("#outStorageApplyBase .customerCode").html(base.customer_code);
            $("#outStorageApplyBase .deliveryDate").html(formatTime(base.delivery_date,false));
            $("#outStorageApplyBase .sn").html(base.sn);

            if(deliveryList !== undefined && deliveryList !== null) {
                for (var i = 0; i < deliveryList.length; i++) {
                    var state = deliveryList[i].approver_operate;
                    var handleStr = '';
                    if(state === null || state === ""){
                        handleStr = '<span class="ty-color-blue" onclick="approveOutStorage($(this),3)">同意出库</span>' +
                                    '<span class="ty-color-red" onclick="approveOutStorage($(this),2)">暂缓出库</span>' ;
                    }else if(state === "1"){
                        handleStr = '<span class="ty-color-gray"">已同意出库</span>'
                    }else if(state === "2"){
                        handleStr = '<span class="ty-color-gray"">已暂缓出库</span>'
                    }
                    deliveryListStr += '<tr id="'+deliveryList[i].id+'">' +
                        '<td>' + deliveryList[i].outer_sn + '</td>' +       //商品代号
                        '<td>' + deliveryList[i].outer_name + '</td>' +     //商品名称
                        '<td>' + handleNull(deliveryList[i].inner_sn) + '</td>' +       //产品图号
                        '<td>' + deliveryList[i].name + '</td>' +           //产品名称
                        '<td>' + deliveryList[i].unit + '</td>' +           //单位
                        '<td>' + parseFloat(Number(deliveryList[i].out_plan || "0").toFixed(4)) + '</td>' +       //计划出库数量
                        '<td class="goodNum_stock">' + deliveryList[i].pack + '</td>' +           //货物件数
                        '<td>' + handleStr + '</td>' +
                        '</tr>'
                }
                $("#goodList tbody").html(deliveryListStr);
            }
            //统计商品种类和件数
            figureGoodInfo($("#outStorageApply"))
        },
        error:function(){
            $("#tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-18 09:46:38，出库评审(确认出库和暂缓出库) */
function approveOutStorage(selector,state){
    bounce.show($("#outStorageApply"));
    var outid   = $("#outStorageApplyBase").attr("oid");
    var id      = selector.parent().parent().attr("id");

    // Integer outid //出库单id 必
    // Integer id  //商品序号id 必
    // Integer state //3.同意 2暂缓 必
    $.ajax({
        url:"../inOutStock/outboundApproval.do" ,
        data:{
            "outid":outid,
            "id":id,
            "state":state
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                if(state === 3){
                    selector.parent().html("已同意出库")
                }else{
                    selector.parent().html("已暂缓出库")
                }
                getOutStorageList(1,20);
                getIndexData();
            }
        },
        error:function(){
            $("#tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-12-07 16:00:43，成品出库申请-查看（出库申请单） */
function checkOutStorageBtn(selector) {

    bounce.show($("#outStorageOrder"));
    var outid = selector.parent().parent().attr("id");
    $.ajax({
        url:"../inOutStock/lookOutput.do" ,
        data:{
            "outid":outid
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            //设置分页
            // var mycurr      = data.currPage;
            // var ttlPage     = data.totalPage;
            var base = data["data"];
            var deliveryList    = base.list;
            var deliveryListStr = '';
            $("#outStorageOrder .applyAll").html('申请人： <span class="ty-color-blue">'+base.applicant_name+'</span> 申请时间：<span class="ty-color-blue">'+formatTime(base.apply_date,false) +'</span>');
            $("#outStorageOrderBase").attr("oid",outid);
            $("#outStorageOrderBase .customerName").html(base.customer_name);
            $("#outStorageOrderBase .customerCode").html(base.customer_code);
            $("#outStorageOrderBase .deliveryDate").html(formatTime(base.delivery_date,false));
            $("#outStorageOrderBase .sn").html(base.sn);

            if(deliveryList !== undefined && deliveryList !== null) {
                for (var i = 0; i < deliveryList.length; i++) {
                    deliveryListStr += '<tr id="'+deliveryList[i].id+'">' +
                        '<td>' + deliveryList[i].outer_sn + '</td>' +       //商品代号
                        '<td>' + deliveryList[i].outer_name + '</td>' +     //商品名称
                        '<td>' + deliveryList[i].inner_sn + '</td>' +       //产品图号
                        '<td>' + deliveryList[i].name + '</td>' +           //产品名称
                        '<td>' + deliveryList[i].unit + '</td>' +           //单位
                        '<td>' + parseFloat(Number(deliveryList[i].current_stock || "0").toFixed(4)) + '</td>' +  //当前库存
                        '<td>' + parseFloat(Number(deliveryList[i].out_plan || "0").toFixed(4)) + '</td>' +       //计划出库数量
                        '<td class="goodNum_stock">' + deliveryList[i].pack + '</td>' +           //货物件数
                        '</tr>'
                }
                $("#outStorageOrder .tblList tbody").html(deliveryListStr);
            }

            //统计商品种类和件数
            figureGoodInfo($("#outStorageOrder"))

        },
        error:function(){
            // $("#tipMs").html("系统错误，请重试!") ;
            // bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

function storageAcceptHandleBtn(selector) {
    // if(hasAuthority(0)){
    //     layer.msg("您没有此权限！")
    //     return false;
    // }
    bounce.show($("#storageAcceptHandle"));
    selector.parent().parent().siblings().removeClass("storageAcceptItemActive");
    selector.parent().parent().addClass("storageAcceptItemActive");
    $(".ty-radioActive").removeClass("ty-radioActive");
    $(".ty-radio i").removeClass("fa-dot-circle-o").addClass("fa-circle-o");
    $(".productNum").val("");
    $(".judgmentQuantity").val("");
    $("#productNum").hide();
    $('body').everyTime('0.5s','storageAcceptHandle',function(){
        var val = $(".ty-radioActive").attr("value");
        if(val === "0"||(val === "1"&&$(".productNum").val()!=="")){
            $("#storageAcceptHandleBtn").prop("disabled",false);
        }else{
            $("#storageAcceptHandleBtn").prop("disabled",true);
        }
    });
}

function sureStorageAcceptHandle() {
    var judgmentQuantity    = $("#storageAcceptHandle .judgmentQuantity").val();
    var productNum          = $("#storageAcceptHandle .productNum").val();
    var ID                  = $(".storageAcceptItemActive").attr("id");

    var data = {
        "flag" : judgmentQuantity,  //Integer flag数量无误传0,需修改数量传1；
        "ID"   : ID                 //Integer ID出入库表ID
    };

    if( Number(judgmentQuantity) === 1 ){
        data["InFact"] = productNum;//Integer InFact修改数量
    }

    $.ajax({
        url:"../mtStock/updateMtStockCount.do" ,
        data:data ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            loading.close() ;
            bounce.cancel();
            var status = data["status"];
            if(status != 1){
                $("#tip #tipMs").html("入库失败!") ;
                bounce_Fixed2.show($("#tip")) ;
            } else{
                var partOne = $(".container_item>div:visible").data('part');
                let pram = {'num': partOne}
                jumpTo(pram, 1,20);
                getIndexData();
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    }) ;

}

function goback() {
    $("#checkInOutInfo").hide().siblings(".ty-container").show();
    $("#queryBtn").show();
    var text = $("#inOutApply .ty-secondTab .ty-active").html();
    $("#curN").html(text)
}

/* creator：张旭博，2017-08-29 10:25:20，获取审批流程字符串（<td>...</td>） */
function getProcessStr(approcessList) {

    //每一条审批流程组成的字符串
    var processStr = "";
    for (var k = 0; k < approcessList.length; k++) {
        var approveStatus = approcessList[k].approveStatus;
        if(Number(approveStatus) === 4){
            processStr +=   '<div class="infoList ty-process-item ">' +
                '<p><i class="dot-no"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                '<p>' + approcessList[k].createDate + '</p>' +
                '</div>';
        }else{
            processStr +=   '<div class="infoList ty-process-item ">' +
                '<p><i class="dot"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                '<p>' + approcessList[k].createDate + '</p>' +
                '</div>';
        }
    }

    //审批流程字符串
    var processDetailStr  = '<td class="hoverDetail"><span>查看入库流程</span> '+
                                '<div style="position: relative;">' +
                                    '<div class="hoverDetailCon">' +
                                        '<div class="ty-panel ty-process" >'+
                                            '<div class="infoHead ty-process-ttl"><b class="ty-btn ty-btn-green ty-circle-3">入库流程</b></div>'+
                                            '<div class="conInfo ty-process-container" id="process">'+
                                            processStr+
                                            '</div>'+
                                        '</div>'+
                                    '</div>' +
                                '</div>' +
                            '</td>';    //入库流程
    return processDetailStr;
}

/* creator：张旭博，2017-11-08 08:58:03，物流管理-》发货管理-》获取四个状态列表 */
function getDeliveryList(pageNum,per,state) {
    $("#ye_accept").html("");
    var data = {
        "pageNum":pageNum,
        "per":per
    };
    if(state !== 0){
        data["state"] = state
    }
    $.ajax({
        url:"../skl/deliveryList.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            //设置分页
            // var mycurr      = data.currPage;
            // var ttlPage     = data.totalPage;
            var curr = data["currPage"];//当前页
            var totalPage = data["totalPage"];//当前页
            var jsonStr = JSON.stringify({"state": state}) ;

            setPage( $("#ye_accept") , pageNum ,  totalPage , "goodsDelivery", jsonStr );

            var deliveryList    = data["data"];
            var deliveryListStr = '';
            if(deliveryList !== undefined && deliveryList !== null) {
                for (var i = 0; i < deliveryList.length; i++) {
                    deliveryListStr +=  '<tr id="'+deliveryList[i].tpa_id+'">' +
                        '<td>' + formatTime( deliveryList[i].last_review_time  , false)+ '</td>' +  //出库日期
                        '<td>' + deliveryList[i].customer_name + '</td>' +                          //客户名称
                        '<td>' + deliveryList[i].code + '</td>' +                                   //客户代号
                        '<td>' + handleNull(deliveryList[i].sn) + '</td>' +                                     //订单号
                        '<td>' + deliveryList[i].item_account + '</td>' +                           //商品类别总数
                        '<td>' + deliveryList[i].pack_amount + '</td>' +                            //货物总件数
                        '<td>' + formatTime( deliveryList[i].tpa_create_date , true) + '</td>' +    //申请提交时间
                        '<td>' + formatTime( deliveryList[i].last_approve_time , true) + '</td>' +  //仓库审批时间(最后)
                        '<td>' + formatTime( deliveryList[i].last_review_time , true) + '</td>' +   //复核时间(最后)
                        '<td>' + deliveryList[i].tpa_create_name + '</td>' +                        //提交者
                        '<td class="base" style="display: none">'+JSON.stringify(deliveryList[i])+'</td>'+
                        '<td>' +
                        '<span class="ty-color-blue" onclick="signCheckBtn($(this))">查看</span>' +
                        '<span class="ty-color-blue" onclick="transInfoBtn($(this))">发运信息</span>' +
                        '</td>' +
                        '</tr>';
                }
                $("#outStorageInfo tbody").html(deliveryListStr);
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-12-04 09:40:03，物流管理-》发货管理-》已签收-》签收查看-按钮  */
function signCheckBtn(selector) {
    bounce.show($("#signCheck"))
    var outid = selector.parent().parent().attr("id");
    $.ajax({
        url:"../inOutStock/aTaoLook.do" ,
        data:{
            "outid":outid,
            "state":10
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var base = data["data"];
            var deliveryItemData = base.list;
            $("#signCheckBase .customerName").html(base.customer_name);
            $("#signCheckBase .customerCode").html(base.customer_code);
            $("#signCheckBase .address").html(base.address);
            $("#signCheckBase .contact").html(base.telephone);
            $("#signCheckBase .mobile").html(base.consignee);
            $("#signCheckBase .sn").html(base.sn);
            $("#signCheckBase .create_date").html(formatTime( base.order_c , false ));
            $("#signCheckBase .update_date").html(formatTime( base.order_u , false ));
            $("#signCheckBase .deliveryDate").html(formatTime( base.delivery_date , false ));
            $("#signCheckBase .carrier").html(base.carrier);
            $("#signCheckBase .arriveDate").html(formatTime( base.arrive_date , false ));
            $("#signCheckBase .deliveryWay").html(base.delivery_way);

            $("#signInfoRecord .signDetail").html(base.sign_record);
            $("#signInfoRecord .signer").html(base.sginer);
            $("#signInfoRecord .signTime").html(formatTime(base.sign_time,false));
            $("#signInfoRecord .recorder").html(base.sign_recorder_name);
            $("#signInfoRecord .recordTime").html(formatTime(base.sign_recorder_time,false));
            $("#signCheckBase").attr("oid",base.outid);
            $("#signCheck .applyAll").html(
                ' &nbsp;申请人： <span class="ty-color-blue">'+base.applicant_name+'</span> '+formatTime(base.apply_date,true) +
                ' &nbsp;仓库： <span class="ty-color-blue">'+base.approver_name+'</span> '+formatTime( base.approve_time , true ) +
                ' &nbsp;复核人： <span class="ty-color-blue">'+base.reviewer_name+'</span> '+formatTime(base.review_time,true)

            );

            var deliveryStr = '';
            for(var i=0;i<deliveryItemData.length;i++){
                deliveryStr +=  '<tr id="'+deliveryItemData[i].id+'" pack="'+deliveryItemData[i].pack+'">'+
                    '<td>' + deliveryItemData[i].outer_sn + '</td>' +                           //商品代号
                    '<td>' + deliveryItemData[i].outer_name + '</td>' +                         //商品名称
                    '<td>' + deliveryItemData[i].inner_sn + '</td>' +                           //产品图号
                    '<td>' + deliveryItemData[i].name + '</td>' +                               //产品名称
                    '<td>' + deliveryItemData[i].unit + '</td>' +                               //单位
                    '<td>' + formatTime( deliveryItemData[i].delivery_date , false ) + '</td>' +//要求到货日期
                    '<td>' + parseFloat(Number(deliveryItemData[i].out_plan || "0").toFixed(4)) + '</td>' +                           //出库数量
                    '<td class="goodNum_stock">' + deliveryItemData[i].pack + '</td>' +                           //货物件数
                    '<td>' + formatTime( deliveryItemData[i].approve_time,false) + '</td>' +    //仓库审批时间
                    '<td>' + formatTime( deliveryItemData[i].review_time,false) + '</td>' +    //复核时间
                    '<td class="change_amount">' + chargeNull(deliveryItemData[i].sign_amount) + '</td>' +    //实际签收数量
                    '<td class="change_memo" style="display:none;">' + parseFloat(Number(deliveryItemData[i].sign_record || "0").toFixed(4)) + '</td>' ;    //情况记录
                var sign_time = deliveryItemData[i].review_time;
                if(sign_time === null || sign_time === undefined || sign_time === ''){
                    deliveryStr +=  '<td></td><td></td></tr>'
                }else{
                    deliveryStr +=  '<td>' + formatTime( deliveryItemData[i].sgin_time,false) + '</td>' +    //录入时间
                        '<td>' +
                        '<span class="ty-color-blue" onclick="signDetailCheckBtn($(this))">查看</span>'+
                        '</td>'+
                        '</tr>';
                }
            }
            $("#signCheck .tblList tbody").html(deliveryStr);

            //统计商品种类和件数
            figureGoodInfo($("#signCheck"));
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-29 16:56:31，货运信息-按钮 */
function transInfoBtn(selector) {
    bounce.show($("#logisticInfo"));
    var outId = selector.parent().parent().attr("id");
    $("#logisticInfo .transInfo").attr("id",outId);
    getTransInfo();
}

/* creator：张旭博，2017-11-29 16:56:17，获取发运信息 */
function getTransInfo() {
    var outId   = $("#logisticInfo .transInfo").attr("id");
    $.ajax({
        url:"../skl/getShipmentState.do" ,
        data:{"outId":outId},
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            if(data[0].code && data[0].code === 400){
                $("#logisticInfo .transDetail").hide();
                $("#logisticInfo .transInfo").show();
            }else {
                $("#logisticInfo .transDetail").show();
                $("#logisticInfo .transInfo").hide();
                var transDetailStr = '';
                for (var i = 0; i < data.length; i++) {
                    var deliveryWay = data[i].deliveryWay;
                    var state = data[i].updateDate;
                    var dateStr = '';
                    if(state === ""){
                        dateStr = '录入日期';
                    }else{
                        dateStr = '修改日期';
                    }
                    switch (deliveryWay) {
                        case "A":
                            transDetailStr += '<tr>' +
                                '    <td>发运方式：</td>' +
                                '    <td>快递</td>' +
                                '    <td>快递单号：</td>' +
                                '    <td>' + data[i].sn + '</td>' +
                                '    <td>快递公司：</td>' +
                                '    <td>' + data[i].company + '</td>' +
                                '    <td>交寄日期：</td>' +
                                '    <td>' + data[i].deliveryTime + '</td>' +
                                '    <td>'+dateStr+'</td>' +
                                '    <td>' + data[i].createDate + '</td>' +
                                '</tr>';
                            break;
                        case "B":
                            transDetailStr += '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="B">随身携带</td>' +
                                '   <td>携带者：</td>' +
                                '   <td>' + data[i].operator + '</td>' +
                                '   <td>联系方式：</td>' +
                                '   <td>' + data[i].telephone + '</td>' +
                                '   <td>备注：</td>' +
                                '   <td>' + data[i].memo + '</td>' +
                                '   <td>'+dateStr+'</td>' +
                                '   <td>' + data[i].createDate + '</td>' +
                                '</tr>';
                            break;
                        case "C":
                            transDetailStr += '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="C">货运</td>' +
                                '   <td>联系人：</td>' +
                                '   <td>' + data[i].operator + '</td>' +
                                '   <td>联系电话：</td>' +
                                '   <td>' + data[i].telephone + '</td>' +
                                '   <td>牌照号码：</td>' +
                                '   <td>' + data[i].licenseTag + '</td>' +
                                '</tr>' +
                                '<tr>' +
                                '   <td></td>' +
                                '   <td></td>' +
                                '   <td>乘运公司：</td>' +
                                '   <td>' + data[i].company + '</td>' +
                                '   <td>货运单号：</td>' +
                                '   <td>' + data[i].sn + '</td>' +
                                '</tr>'+
                                '<tr>' +
                                '   <td></td>' +
                                '   <td></td>' +
                                '   <td>备注：</td>' +
                                '   <td colspan="5">'+data[i].memo +'</td>' +
                                '   <td>'+dateStr+'</td>' +
                                '   <td>' + data[i].createDate + '</td>' +
                                '</tr>';
                            break;
                    }
                }
                $("#logisticInfo .transDetail tbody").html(transDetailStr);
                $("#logisticInfo .transDetail").show();
                $("#logisticInfo .chooseWay").hide();
                $("#logisticInfo .transInfo").hide();
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

// creator: 张旭博，2018-05-14 16:19:16，统计商品的类别数和件数
function figureGoodInfo(selector) {
    var goodIdArr = [],
        countAll = 0,
        pack_amount = 0;

    //遍历选择器下的所有商品id
    selector.find(".tblList tbody tr").each(function () {
        var innerSn = $(this).find("td").eq(2).text();
        goodIdArr.push(innerSn);
        console.log(innerSn)
        pack_amount += Number($(this).find(".goodNum_stock").text());
    });

    //id排序
    goodIdArr.sort();

    //统计类别数
    for (var i = 0; i < goodIdArr.length;) {
        var count = 0;
        for (var j = i; j < goodIdArr.length; j++) {
            if (goodIdArr[i] === goodIdArr[j]) {
                count++;
            }
        }
        countAll++;
        i+=count;
    }
    var alertHtml = '本次计划出库共 <span class="ty-color-blue"> '+countAll+' </span> 种商品，共<span class="ty-color-blue"> '+pack_amount+' </span>件';
    selector.find(".countAll").alert('success',alertHtml);
}
/* creator：张旭博，2017-11-30 08:30:58，物流管理-》发货管理-》已签收-》已签收列表-》签收记录-》查看按钮 */
function signDetailCheckBtn(selector) {
    bounce_Fixed.show($("#signDetailCheck"));
    var sign_record = selector.parent().siblings(".change_memo").text();
    var sign_amount = selector.parent().siblings(".change_amount").text();
    $("#signDetailCheck .actualDeliveryNum").val(sign_amount);
    $("#signDetailCheck .memo").val(sign_record);
}
// creator: 李玉婷，2021-09-01 16:18:35，暂停商品
function suspendCommodyList(obj){
    getPdByCategory(1, '', 2 , 1 , 20 );
}

// creator: 李玉婷，2020-04-01 15:18:20，获取暂停销售的商品数据
function getSuspendList(currPage,pageSize,keyword) {
    $("#ye").html('');
    $("#firstLevelAmount").html('0');
    $("#suspendZSList tbody").html('');
    $.ajax({
        "url": "../product/suspendZSProductList.do",
        "data": {
            "pageSize":pageSize,
            "currPage":currPage,
            "param": keyword
        },
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = {
                "param": keyword,
            } ;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#ye") , curr ,  totalPage , "inSuspendTY", jsonStr );
            $("#firstLevelAmount").html(res.suspendProductNum);
            var spList = res.data;
            if (spList && spList.length > 0){
                var html = '';
                for(var t=0;t<spList.length;t++){
                    var info = JSON.stringify(spList[t]);
                    html+=
                        ' <tr data-info=\''+ info +'\'>' +
                        '     <td>'+ handleNull(spList[t].outerSn) +'</td>' +
                        '     <td>'+ handleNull(spList[t].outerName) +'</td>' +
                        '     <td>'+ handleNull(spList[t].model) +'</td>' +
                        '     <td>'+ handleNull(spList[t].specifications) +'</td>' +
                        '     <td>'+ spList[t].unit +'</td>' +
                        '     <td>'+ parseFloat(Number(spList[t].minimumStock || "0").toFixed(4)) +'</td>' +
                        '     <td>'+ parseFloat(Number(spList[t].currentStock || "0").toFixed(4)) +'</td>' +
                        '     <td class="createInfo">'+ spList[t].updateName + ' &nbsp; ' + new Date(spList[t].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '     <td>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="seeCommodityDetails($(this),2, true)">查看</span>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="curOrders($(this))">当前订购信息</span>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="suspendSale($(this),1)">恢复销售</span>' +
                        '         <span class="ty-color-red tb-btn-sm" onclick="deleteCommodity($(this),1)">删除</span>' +
                        '     </td>' +
                        ' </tr>';
                }
                $("#suspendZSList tbody").html(html);
            }
            var conHeight = $(".bigContainer").height() ;
            conHeight++;
            $(".between").height(conHeight);
        }
    });
}
// 为空赋值
function chargeNull(str){
    if(str === null || str==0 || str === undefined){
        return "--"
    }else{
        return str ;
    }
}
// creator: 李玉婷，2021-06-29 11:44:53，控制输入3位小数
function limitSize(obj) {
    clearNoNum(obj);
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/,'$1$2.$3');
}








