var user = null;
// creator: hxz  2018-07-18  初始化 三级弹框 - fixed2
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#tip3"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#contactSeeDetail"));
bounce_Fixed3.cancel();
var editContractObj = null
var contractDel = null
$(function(){
//     console.log('fileUrl', $.fileUrl)
//     console.log('uploadUrl', $.uploadUrl)
    if (sphdSocket.user.oid === 0) {
        $(".specialOrgPart").show()
        getModule()
        $("#addAccountBtn").hide().siblings().show()
    } else {
        $(".specialOrgPart").hide()
        $("#addAccountBtn").show().siblings().hide()
    }

	// 获得基本信息列表
    let u = auth.getUser()
	var userName = u.userName
	var userID = u.userID
    user = {"userName": userName, "userID": userID};
    if (chargeRole("超管")) { // 不是超管，都显示新增按钮
		$("#addCus").remove();
	}
    getCustomerMes(1 , 20, '');


	$(".chooseCusCon").click(function () {
	    let target = $(this).data("target");
	    let str = "其他";
        var type = "" , source = "" ;
	    if(target == "#ReceiveName"){
            str = "收货人";
            type = $("#newReceiveInfo").data('type');
            source = $("#newReceiveInfo").data('source');
        } else if(target == "#mailName"){
            str = "发票接收人";
            type = $("#newMailInfo").data('type');
            source = $("#newMailInfo").data('source');
        }
	    $("#target").val(target)
        bounce_Fixed2.show( $("#chooseCusContact") );

        if(source == 'addCustomer') {
            var contactInfo = addCusContact;
            setCusListStr(contactInfo,'addCustomer')
        }else{
            var cusId = $("#updateCustormPanel").data("id");
            getCusContactList(cusId);
        }
    });
    $(".main").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        var id = $(this).siblings(".hd").find(".cusProId").text()
        switch (name) {
            case 'orgManage':
                var cusInfo = JSON.parse($(this).next(".hd").find(".cusInfo").html())
                $(".orgManage").show().siblings().hide()
                $(".orgManage").data("cusInfo", cusInfo)
                $(".orgManage .orgName").html(cusInfo.name)
                getOrganizationList(cusInfo.id)
                break;
        }
    })
    $(".add_cusName").on("blur", function () {
        var val = $(this).val()
        $(this).parents("form").find("[name='name']").val(val.substring(0,6))
    })
    $(".orgManage").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'goBack':
                $(".main").show().siblings().hide()
                break;
            // 新增机构 - 按钮
            case 'newOrg':
                $("#selectModule .ty-color-red").show()
                $("#selectModule .module_avatar").show()
                $("#selectModule .bounce_title").html("新增机构")
                $("#selectModule input").val("").prop("disabled", false)
                $("#selectModule select").val("").prop("disabled", false)
                $("#selectModule input:checkbox").prop("checked", false)
                $("#selectModule .newModuleBtn").show().siblings().hide()
                var cusInfo = $(".orgManage").data("cusInfo")
                $("#selectModule input[name='supervisorName']").val(cusInfo.supervisorName)
                $("#selectModule input[name='supervisorMobile']").val(cusInfo.supervisorMobile)
                bounce_Fixed.show($("#selectModule"))
                setEveryTime(bounce_Fixed, 'newOrg')
                break;
            // 机构信息 - 按钮
            case 'orgInfo':
                var orgId = $(this).parents("tr").data("id")
                getOrgInfo(orgId)
                $("#selectModule .ty-color-red").hide()
                $("#selectModule .module_avatar").hide()
                $("#selectModule .bounce_title").html("机构信息查看")
                $("#selectModule [name]").prop("disabled", true)
                $("#selectModule .seeModuleBtn").show().siblings().hide()
                bounce_Fixed.show($("#selectModule"))
                $("#selectModule").data("orgId", orgId)
                setEveryTime(bounce_Fixed, 'newOrg')
                break;
            // 已选模块 - 按钮
            case 'selectedModule':
                var orgId = $(this).parents("tr").data("id")
                getOrgInfo(orgId)
                $("#selectModule .ty-color-red").hide()
                $("#selectModule .module_avatar").show()
                $("#selectModule .bounce_title").html("已选模块查看")
                $("#selectModule input").prop("disabled", true)
                $("#selectModule select").prop("disabled", true)
                $("#selectModule input:checkbox").prop("checked", false)
                $("#selectModule .seeModuleBtn").show().siblings().hide()
                bounce_Fixed.show($("#selectModule"))
                $("#selectModule").data("orgId", orgId)
                setEveryTime(bounce_Fixed, 'newOrg')
                break;
            case 'userLoginRecord':
                var orgId = $(this).parents("tr").data("id");
                $(".dateType").html("登录日期");
                $(".loginRecord").data("orgId", orgId);
                $(".loginRecord").show().siblings().hide();
                getCusLoginRecordList(orgId, 1, '', '') ;
                break;
            case 'resouceManage':
                getResouceInfo($(this))
                break;
        }
    })
    $(".loginRecord").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'goBack':
                $(".orgManage").show().siblings().hide()
                break;
        }
    })
    //本年、本月切换状态
    $(".flagTab .ty-btn-groupCom .ty-btn").on("click",function(){
        //样式切换
        $(this).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
        $("#loginQueryBtn").removeClass("ty-btn-blue");

        //清空自定义选择的时间
        $("#queryBeginTime").val("");
        $("#queryEndTime").val("");

        //获取对应数据
        var thisFlag = Number($(this).index()) + 1;
        var orgId = $(".loginRecord").data("orgId");
        if (thisFlag == 1) {
            $(".dateType").html("登录日期");
        }else if (thisFlag == 2) {
            $(".dateType").html("登录月份");
        }
        getCusLoginRecordList(orgId, thisFlag, '', '') ;
    });
    $(".bounce_Fixed").on('click','.ty-btn,.edit2', function(){
        var name = $(this).data('name');
        switch (name) {
            case 'cScan': // 已到期的查看
                var cid = $(this).siblings(".hd").html() ;
                cScan(cid)
                break;
            case 'cRestart': // 恢复履约/重启合作
                var cid = $(this).siblings(".hd").html() ;
                $("#cEndTip").data("cid",cid).data("enabled", 1)
                $("#tipsc").html("<p>确定后，需重新选定属于本合同的商品。</p> " +
                    " <p>确定进行本操作吗？</p>");
                bounce_Fixed3.show($("#cEndTip"));
                break;
            case 'cRenewal': // 续约
                contractInfoEdit('cRenewalHistory', 'updateCustomer', $(this));
                break
            case 'editContractOk': // 编辑合同确定
                editContractOk(1)
                break;
            case 'addReceive':
                var type = $("#newReceiveInfo").data('type');
                var source = $("#newReceiveInfo").data('source');
                if(source == 'addCustomer'){
                    var receiveData = $("#addAccount").data('receiveInfo');
                    receiveData = JSON.parse(receiveData);
                    let contactInfo = JSON.parse($("#ReceiveName").siblings(".hd").html());
                    if (type == 'new'){
                        var data = {
                            'number': Math.random(),
                            'address': $("#ReceiveAddress").val(),
                            'contact':  contactInfo.name ,
                            'mobile': contactInfo.mobile,
                            'contactInfoNumber': contactInfo.number
                        };
                        receiveData.push(data);
                    }else if(type == 'update'){
                        var id = $("#newReceiveInfo").data('id');
                        var index = receiveData.findIndex(value=>value.number === id);
                        var data = {
                            'number':  contactInfo.number,
                            'address': $("#ReceiveAddress").val(),
                            'contact':  contactInfo.name ,
                            'mobile': contactInfo.mobile,
                            'contactInfoNumber': contactInfo.number
                        }
                        receiveData[index] = data;
                    }
                    setShList(receiveData);
                }else{
                    var id = $("#newReceiveInfo").data('id');
                    let contactInfo = JSON.parse($("#ReceiveName").siblings(".hd").html());
                    if (type == 'new') {
                        var json = {
                            'address': $("#ReceiveAddress").val(),
                            'contact': contactInfo.name ,
                            'customerContact': contactInfo.id ,
                            'mobile':contactInfo.mobile
                        }
                        json = JSON.stringify(json);
                        var params = {
                            'customerId': id,
                            'shAddress': json
                        }
                        $.ajax({
                            url: '/sales/addCustomerAddress.do',
                            data: params,
                            success: function (data) {
                                var status = data.status;
                                if (status === '1' || status === 1) {
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }else if(type == 'update'){
                        var params = {
                            'id': id,
                            'address': $("#ReceiveAddress").val(),
                            'contact':  contactInfo.name ,
                            'mobile': contactInfo.mobile
                        }
                        $.ajax({
                            url: '/sales/updateCustomerAddress.do',
                            data: params,
                            success: function (data) {
                                var status = data.status;
                                if (status === '1' || status === 1) {
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }
                }
                bounce_Fixed.cancel();
                break;
            case 'addMail':
                var source = $("#newMailInfo").data('source');
                var type = $("#newMailInfo").data('type');
                if(source == 'addCustomer'){
                    var mailData = $("#addAccount").data('mailInfo');
                    mailData = JSON.parse(mailData);
                    if (type == 'new'){
                        let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var data = {
                            'customerContact': Math.random(),//
                            'address': $("#mailAddress").val(),
                            'contact':  contactInfo.name,
                            'postcode': $("#mailNumber").val(),
                            'mobile': contactInfo.mobile,
                            'contactInfoNumber': contactInfo.number
                        }
                        mailData.push(data);
                    }else if(type == 'update'){
                        var id = $("#newMailInfo").data('id');
                        var index = mailData.findIndex(value=>value.number === id);
                        let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var data = {
                            'number': id,
                            'address': $("#newMailInfo #mailAddress").val(),
                            'contact': contactInfo.name,
                            'postcode': $("#newMailInfo #mailNumber").val(),
                            'mobile': contactInfo.mobile,
                            'contactInfoNumber': contactInfo.number
                        }
                        mailData[index] = data;
                    }
                    setMailList(mailData);
                }else{
                    var id = $("#newMailInfo").data('id');
                    if (type == 'new') {
                        let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var json = {
                            'address': $("#mailAddress").val(),
                            'contact': contactInfo.name,
                            'postcode': $("#mailNumber").val(),
                            'mobile': contactInfo.mobile
                        }
                        json = JSON.stringify(json);
                        var params = {
                            'customerId': id,
                            'fpAddress': json
                        }
                        $.ajax({
                            url: '/sales/addCustomerAddress.do',
                            data: params,
                            success: function (data) {
                                var status = data.status;
                                if (status === '1' || status === 1) {
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("新增失败！");
                                }
                            }
                        })
                    }else if(type == 'update'){
                        let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var params = {
                            'id': id ,
                            'address': $("#mailAddress").val(),
                            'contact': contactInfo.name,
                            'postcode': $("#mailNumber").val(),
                            'mobile': contactInfo.mobile
                        }
                        $.ajax({
                            url: '/sales/updateCustomerAddress.do',
                            data: params,
                            success: function (data) {
                                var status = data.status;
                                if (status === '1' || status === 1) {
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }
                }
                bounce_Fixed.cancel();
                break;
            case 'addContact':
                var level = $("#newContectInfo").data('level');
                var type = $("#newContectInfo").data('type');
                var id = $("#newContectInfo").data('id');
                var source = $("#newContectInfo").data('source');
                if(source == 'addCustomer'){
                    var contactInfo = $("#addAccount").data('contactInfo');
                    contactInfo = JSON.parse(contactInfo);
                    var data = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $(" #contactNumber").val(),
                        'visitCard': '',
                        'socialList': '[]'
                    }
                    if($("#contactsCard .bussnessCard").length > 0){
                        data.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if($(".otherContact li").length > 0){
                        var arr = []
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json ={
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        data.socialList = arr;
                    }
                    var groupUuid= $("#contactsCard").attr("groupUuid")
                    if (type == 'new'){
                        data.id = Math.random();
                        data.groupUuid = groupUuid;
                        contactInfo.push(data);
                        addCusContact.push(data);

                    }else if(type == 'update') {
                        var id = $("#newContectInfo").data('id');
                        var index = contactInfo.findIndex(value=>value.id === id);
                        data.number = id;
                        data.groupUuid = groupUuid;
                        contactInfo[index] = data;
                        addCusContact.forEach(function (item, index) {
                            if(data.number == item.number){
                                addCusContact[index] = data ;
                                $(".mailList tbody tr").each(function(){
                                    let thisContactID = $(this).find(".hd").html()
                                    if(thisContactID == data.number){
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(4)").html(data.mobile);
                                    }
                                })
                                $(".receiveList tbody tr").each(function(){
                                    let thisContactID = $(this).find(".hd").html()
                                    if(thisContactID  == data.number){
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(3)").html(data.mobile);
                                    }
                                })
                            }
                        })
                    }
                    var html = setContactList(contactInfo);
                    $(".contectList").html(html);
                    // 给当前的赋值
                    if(level == 2){
                        var tags = $("#contactFlag").html();
                        if(tags == "收货人"){
                            bounce_Fixed.show($("#newReceiveInfo"));
                            $("#ReceiveName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else if(tags== "发票接收人"){
                            bounce_Fixed.show($("#newMailInfo"));
                            $("#mailName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else { //  新增联系人
                            bounce_Fixed.cancel();
                        }
                    }else{
                        bounce_Fixed.cancel();
                    }
                }else{
                    var data = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $("#contactNumber").val(),
                        'visitCard': '',
                        'socialList': '[]'
                    };
                    if($("#contactsCard .bussnessCard").length > 0){
                        data.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if($(".otherContact li").length > 0){
                        var arr = []
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json = {
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        data.socialList = arr;
                    }
                    if (type == 'new') {
                        data.customer = id;
                        $.ajax({
                            url: '/sales/addCustomerContact.do',
                            data: data,
                            success: function (res) {
                                data.id = res.id;
                                var status = res.status;
                                if (status === '1' || status === 1) {
                                    layer.msg('新增成功')
                                    var groupUuidArr = []
                                    $("#newContectInfo [groupUuid]").each(function () {
                                        groupUuidArr.push({
                                            type: 'groupUuid',
                                            groupUuid: $(this).attr("groupUuid")
                                        })
                                    })
                                    cancelFileDel(groupUuidArr)
                                    // 给当前的赋值
                                    var tags = $("#contactFlag").html();
                                    if(tags == "收货人"){
                                        bounce_Fixed.show($("#newReceiveInfo"));
                                        setTimer('updateReceive');
                                        $("#ReceiveName")
                                            .val(data.name).data('orgData',data.name)
                                            .siblings(".hd").html(JSON.stringify(data)) ;
                                    }else if(tags== "发票接收人"){
                                        bounce_Fixed.show($("#newMailInfo"));
                                        setTimer('updateMail');
                                        $("#mailName")
                                            .val(data.name).data('orgData',data.name)
                                            .siblings(".hd").html(JSON.stringify(data)) ;
                                    }else { //  新增联系人
                                        bounce_Fixed.cancel();
                                    }
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }else if(type == 'update'){
                        data.contactId = id;
                        $.ajax({
                            url: '/sales/updateContactsSocialAndCard.do',
                            data: data,
                            success: function (res) {
                                var status = res.status;
                                if (status === '1' || status === 1) {
                                    var groupUuidArr = []
                                    $("#newContectInfo [groupUuid]").each(function () {
                                        groupUuidArr.push({
                                            type: 'groupUuid',
                                            groupUuid: $(this).attr("groupUuid")
                                        })
                                    })
                                    cancelFileDel(groupUuidArr)
                                    indexUpdateList(cus_seeTrObj);
                                    // 给当前的赋值
                                    var tags = $("#contactFlag").html();
                                    if(tags == "收货人"){
                                        bounce_Fixed.show($("#newReceiveInfo"));
                                        $("#ReceiveName")
                                            .val(data.name).data('orgData',data.name)
                                            .siblings(".hd").html(JSON.stringify(data)) ;
                                        setTimer('updateReceive');
                                    }else if(tags== "发票接收人"){
                                        bounce_Fixed.show($("#newMailInfo"));
                                        setTimer('updateMail');
                                        $("#mailName")
                                            .val(data.name).data('orgData',data.name)
                                            .siblings(".hd").html(JSON.stringify(data)) ;
                                    }else { //  新增联系人
                                        bounce_Fixed.cancel();
                                    }
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }
                    bounce_Fixed.cancel();
                }
                break;
            case 'enableTurn':
                var addressId = $(this).data('id');
                var val = $(this).data('val');
                $.ajax({
                    url: '/sales/startOrStopAddress.do',
                    data: {
                        'addressId': addressId,
                        'enabled': val
                    },
                    success: function (data) {
                        var status = data.status;
                        if (status === '1' || status === 1) {
                            indexUpdateList(cus_seeTrObj);
                            bounce_Fixed.cancel();
                        } else {
                            layer.msg("停止失败！");
                        }
                    }
                })
                break;
            case 'deleteContact':
                var contactId = $(this).data('id');
                $.ajax({
                    url: '/sales/deleteCustomerContact.do',
                    data: {
                        'contactId': contactId
                    },
                    success: function (data) {
                        var status = data.status;
                        if (status === '1' || status === 1) {
                            indexUpdateList(cus_seeTrObj);
                            bounce_Fixed.cancel();
                        } else {
                            layer.msg("停止失败！");
                        }
                    }
                })
            // 机构信息查看弹窗/已选模块查看弹窗 - 修改 按钮
            case 'changeOrg':
                $("#selectModule .ty-color-red").show()
                var title = $("#selectModule .bounce_title").html()
                if (title === '机构信息查看') {
                    $("#selectModule table [name]").prop("disabled", false)
                    $("#selectModule table input[name='supervisorMobile']").prop("disabled", true)
                    $("#selectModule .bounce_title").html("机构信息修改")
                } else {
                    $("#selectModule .module_avatar input").prop("disabled", false)
                    $("#selectModule .bounce_title").html("已选模块修改")
                }
                $("#selectModule .changeModuleBtn").show().siblings().hide()
                break;
            // 已选模块修改弹窗 - 提交 按钮
            case 'sureChangeOrg':
                var orgId = $("#selectModule").data("orgId")
                var data = {}
                var url = ''
                if ($("#selectModule .module_avatar").is(":visible")) {
                    var orgArr = []
                    $(".module_avatar .module_body input:checkbox:checked").each(function (index) {
                        orgArr.push($(this).attr("mid"))
                    })
                    data.appObject = orgArr.join(",")
                    data.customerOrganizationId = orgId
                    url = '../special/updateOrgPopedoms.do'
                } else {
                    $("#selectModule table [name]").each(function () {
                        var key = $(this).attr("name")
                        data[key] = $(this).val()
                    })
                    data.id = orgId
                    url = '../special/updateCustomerOrganization.do'
                }
                $.ajax({
                    url: url,
                    data: data,
                    success: function (res) {
                        var data = res.data
                        if (data && data === 1) {
                            if ($("#selectModule .module_avatar").is(":visible")) {
                                layer.msg("修改申请已提交")
                                var key = $(".main #se0").val();
                                getCustomerMes(1 , 20, key)
                            }else {
                                layer.msg("修改成功")
                            }

                            bounce_Fixed.cancel()
                        } else {
                            layer.msg("修改失败")
                        }
                    }
                })
                break;
            // 暂不勾选模块 - confirm 弹窗
            case 'notSelectModule':
                sale_addsure()
                break;
            // 勾选模块 - 提交 - 按钮
            case 'submitModule':
                if (!testMobile($.trim($("#selectModule input[name='supervisorMobile']").val()))) {
                    layer.msg("请输入正确的手机号")
                    return false
                }
                newOrganization()
                break;
        }
    });
    $(".bounce_Fixed3").on('click','.ty-btn ', function(){
        var name = $(this).data('name');
        switch (name) {
            case 'cEndOk': // 暂停履约/终止合同 或者 回复履约的确定
                cEndOk()
                break;
        }
    });
    $(".bounce_Fixed2").on('click','.fileImScan,.node', function(){
        var name = $(this).data('fun');
        switch (name) {
            case 'imgScan': // 合同的picture
                imgViewer($(this));
                break;
            case 'cWord': // 合同的可编辑版
                // let path = $("#cScan .cWord").attr('path');
                seeOnline($("#cScan .cWord"))
                break;
            case 'gNum': //本合同下的商品
                $("#tipcontractGoods .bonceHead span").html('本合同下的商品');
                $(".addOrCancel").hide(); $(".cScanc").show();
                let productList = $("#cScan .gNum").data('list');
                setContactGS(productList , false);
                $("#cGTip").html(`本合同下的商品共有以下${ productList.length }种`)
                break;
            case 'cEditLog': // 本版本合同的修改记录

                break;
            case 'cRenewalLog': // 本合同的续约记录

                break;
        }
    });
    $(".bounce_Fixed").on('click','.linkBtn', function () {
        let fun = $(this).data("fun");
        $("#tipcontractGoods").data("fun", fun);
        switch (fun){
            case 'scanGs' :
                $("#tipcontractGoods .bonceHead span").html('本合同下的商品');
                $(".addOrCancel").hide(); $(".cScanc").show();
                var list = $("#newContractInfo .scanGs").data('gsArr')
                setContactGS(list , false);
                $("#cGTip").html(`本合同下的商品共有以下${ list.length }种`)
                break;
            case 'removeGs' :
                $("#tipcontractGoods .bonceHead span").html('从本合同移出商品');
                $(".addOrCancel").show(); $(".cScanc").hide();
                var list = $("#newContractInfo .scanGs").data('gsArr')
                setContactGS(list, true);
                $("#cGTip").html(`可供选择的商品共有以下${ list.length }种`)
                break;
            case 'addGs' :
                $("#tipcontractGoods .bonceHead span").html('向本合同添加商品');
                $(".addOrCancel").show(); $(".cScanc").hide();
                let gsArrNo = $("#newContractInfo .scanGs").data('gsArrNo')
                setContactGS(gsArrNo , true);
                $("#cGTip").html(`可供选择的商品共有以下${ gsArrNo.length }种`)
                break;
        }
    })
    $("#tipcontractGoods").on('click','.fa', function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    });
    $(".bounce").on('click','.node', function(){
        var name = $(this).data('name');
        var type = $(this).data('type');
        var source = $(this).data('source');
        switch (name) {
            case 'contractDel': // 客户查看 - 合同 删除合同
                contractDel = $(this) ;
                bounce_Fixed3.show( $("#contractDelTip") )

                break;
            case 'scanGs': // 客户查看 - 合同 本合同下的商品
                var cid = $(this).parents(".contractItem").data("id") ;
                cScanScanGs(cid);
                break;
            case 'cScan': // 已到期的查看
                var cid = $(this).parents(".contractItem").data("id") ;
                cScan(cid);
                break;
            case 'contractStopData': // 已暂停/终止的合同
                var id = $("#updateCustormPanel").data("id")
                contractStopData(id)
                break;
            case 'contractEndData': // 已到期的合同
                var id = $("#updateCustormPanel").data("id")
                contractEndData(id)
                break;
            case 'cEnd': // 暂停履约/终止合同
                var cid = $(this).parents(".contractItem").data("id") ;
                $("#cEndTip").data("cid",cid).data("enabled", 0)
                $("#tipsc").html("<p>确定后，相关商品将不再属于本合同。</p> " +
                             " <p>确定进行本操作吗？</p>")
                bounce_Fixed3.show($("#cEndTip"));
                break;
            case 'contractInfo': // 包含了新增和修改客户的
                contractInfoEdit(type, source, $(this));
			    break;
			case 'receiveInfo':
                var id = $(this).parents("tr").data('id');
                $("#newReceiveInfo").data('id', id);
                $("#newReceiveInfo").data('type', type);
                $("#newReceiveInfo").data('source', source);
               if(type == 'delete') {
                   var receiveData = $("#addAccount").data('receiveInfo');
                   receiveData = JSON.parse(receiveData);
                   var index = receiveData.findIndex(value=>value.number === id);
                   receiveData.splice(index, 1);
                   setShList(receiveData);
               }else{
                   if(type == 'new'){
                       $("#newReceiveInfo input").val("");
                   }else if(type == 'update'){
                       $("#ReceiveAddress").val($(this).parents("tr").find("td").eq(1).html());
                       $("#ReceiveName").val($(this).parents("tr").find("td").eq(2).html());
                       $("#ReceiveCallNo").val($(this).parents("tr").find("td").eq(3).html());
                   }
                   bounce_Fixed.show($("#newReceiveInfo"));
                   setTimer('updateReceive');
               }
                break;
            case 'mailInfo':
                var id = $(this).parents("tr").data('id');
                $("#newMailInfo").data('type', type);
                $("#newMailInfo").data('id', id);
                $("#newMailInfo").data('source', source);
                if(type == 'delete') {
                    var mailData = $("#addAccount").data('mailInfo');
                    mailData = JSON.parse(mailData);
                    var index = mailData.findIndex(value=>value.number === id);
                    mailData.splice(index, 1);
                    setMailList(mailData);
                }else{
                    if(type == 'new'){
                        $("#newMailInfo input").val("");
                    }else if(type == 'update'){
                        $("#mailAddress").val($(this).parents("tr").find("td").eq(1).html());
                        $("#mailName").val($(this).parents("tr").find("td").eq(2).html());
                        $("#mailNumber").val($(this).parents("tr").find("td").eq(3).html());
                        $("#mailContact").val($(this).parents("tr").find("td").eq(4).html());
                    }
                    bounce_Fixed.show($("#newMailInfo"));
                    setTimer('updateMail');
                }
                break;
            case 'contactInfo':
                var id = $(this).parents("tr").data('id');
                $("#newContectInfo").data('type', type);
                $("#newContectInfo").data('id', id);
                $("#newContectInfo").data('source', source);
                $("#newContectInfo").data('level',1);
                if(type == 'delete') {
                    var contactData = $("#addAccount").data('contactInfo');
                    contactData = JSON.parse(contactData);
                    var index = contactData.findIndex(value=>value.number === id || value.id === id);
                    contactData.splice(index, 1);
                    var html = setContactList(contactData);
                    $(".contectList").html(html);
                }else{
                    $("#uploadCard").html("")
                    initCardUpload($("#uploadCard"));
                    document.getElementById('newContectData').reset();
                    $(".otherContact").html("");
                    $("#contactFlag").html("其他");
                    $("#newContectInfo .bonceHead span").html('联系人')
                    $("#contactsCard .bussnessCard").remove();
                    if(type == 'new'){
                        $('#uploadCard').show();
                        $("#addMoreContact").hide();
                    }else if(type == 'update'){
                        var getData = $("#addAccount").data('contactInfo');
                        getData = JSON.parse(getData);
                        var flag = getData.find(value=>value.id === id || value.number === id);
                        if(flag){
                            flag.socialList = JSON.parse(flag.socialList);
                            setContact(flag);
                        }
                    }
                    $("#addMoreContact").hide();
                    bounce_Fixed.show($("#newContectInfo"));
                    setTimer('updateContact');
                }
                break;
            // 新增客户弹窗 - 暂不勾选模块 - 按钮
            case 'notSelectModule':
                if (!testMobile($.trim($("#addAccount input[name='supervisorMobile']").val()))) {
                    layer.msg("请输入正确的手机号")
                    return false
                }
                $("#mtTip .tip").html("确定暂不勾选模块吗？")
                bounce_Fixed.show($("#mtTip"))
                $("#mtTip .bonceFoot span").attr("data-name", 'notSelectModule')
                break;
            // 新增客户弹窗 - 录入完毕，勾选模块 - 按钮 （此操作会新增客户然后打开勾选模块弹窗）
            case 'selectModule':
                if (!testMobile($.trim($("#addAccount input[name='supervisorMobile']").val()))) {
                    layer.msg("请输入正确的手机号")
                    return false
                }
                var shList =[];
                var imgsQ = [], imgsP = [] ;
                var data = {
                    qImages: '',
                    pImages: '',
                    shAddressList: '',
                    fpAddressList: '',
                    contactsList: ''
                };
                $('#addpayDetails input[type="text"]').each(function(){
                    var name = $(this).attr('name');
                    data[name] = $(this).val();
                })
                var buyCase = $("#addpayDetails input[name='buyCase']:checked").val() == 1;
                if(buyCase){
                    var initialType = $("#addpayDetails input[name='firstTime']:checked").val();
                    if(initialType == undefined){
                        layer.msg('请选择首次购买时间');
                        return false;
                    }else{
                        data['initialType'] = initialType;
                        var date = '', month = '';
                        var year = new Date().getFullYear();
                        if(initialType == 1){
                            month = $("#addpayDetails #buyMonth").val();
                            if(month == "") {
                                layer.msg('请选择月份');
                                return false;
                            }else{
                                var arr = month.split('月');
                                if(arr[0] < 10){
                                    date = year + '0' + arr[0];
                                }else{
                                    date = year + arr[0];
                                }
                            }
                        }else if(initialType == 2){
                            year = Number(year) - 1;
                            date = year + '01';
                        }else if(initialType == 3){
                            year = Number(year) - 2;
                            date = year + '01';
                        }
                        data['initialPeriod'] = date;
                    }
                }
                $("#qImages .imgsthumb").each(function () {
                    var path = {
                        'normal': $(this).find(".filePic").data('path')
                    };
                    imgsQ.push(path);
                })
                $("#pImages .imgsthumb").each(function () {
                    var path = {
                        'normal': $(this).find(".filePic").data('path')
                    };
                    imgsP.push(path);
                })
                imgsQ = JSON.stringify(imgsQ);
                imgsP = JSON.stringify(imgsP);
                data.qImages = imgsQ;
                data.pImages = imgsP;
                if($("#firstContactTime").val() != ''){
                    data.firstContactTime = $("#firstContactTime").val();
                }
                if($("#addpayDetails .receiveList tbody tr").length>0){
                    $("#addpayDetails .receiveList tbody tr").each(function () {
                        var json = {
                            'address': $(this).children().eq(1).html(),
                            'contact': $(this).children().eq(2).html(),
                            'mobile': $(this).children().eq(3).html()
                        }
                        shList.push(json);
                    })
                    shList = JSON.stringify(shList);
                    data.shAddressList= shList;
                }
                data.fpAddressList= $("#addAccount").data('mailInfo');
                data.contactsList= $("#addAccount").data('contactInfo');
                $.ajax({
                    url: '/special/getSaleCtrlsByMobile.do',
                    data: {mobile: data.supervisorMobile},
                    success: function (res) {
                        var state = res.data
                        if (state === 2) {
                            $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                            bounce_Fixed.show($("#tip"))
                            loading.close()
                        } else if (state === 1) {
                            $.ajax({
                                url: "../sales/addPdCustomer.do",
                                data: data,
                                success: function (res) {
                                    var status = res.status
                                    if (status === 1) {
                                        layer.msg("新增成功");
                                        var key = $(".main #se0").val();
                                        getCustomerMes(1 , 20, key);

                                        $("#newReceiveInfo").data('id', '');
                                        $("#newReceiveInfo").data('type', '');
                                        $("#newReceiveInfo").data('source', '');
                                        $("#newMailInfo").data('type', '');
                                        $("#newMailInfo").data('id', '');
                                        $("#newMailInfo").data('source', '');
                                        $("#newContectInfo").data('type', '');
                                        $("#newContectInfo").data('id', '');
                                        $("#newContectInfo").data('source', '');

                                        $(".orgManage").data("cusInfo", res)
                                        $("#selectModule .ty-color-red").show()
                                        $("#selectModule .module_avatar").show()
                                        $("#selectModule input").val("").prop("disabled", false)
                                        $("#selectModule select").val("").prop("disabled", false)
                                        $("#selectModule input:checkbox").prop("checked", false)
                                        $("#selectModule .bounce_title").html("勾选模块")
                                        $("#selectModule .newModuleBtn").show().siblings().hide()
                                        $("#selectModule input[name='supervisorName']").val(res.supervisorName)
                                        $("#selectModule input[name='supervisorMobile']").val(res.supervisorMobile)
                                        $("#selectModule input[name='fullName']").val(res.name)
                                        $("#selectModule input[name='name']").val(res.name.slice(0,6))
                                        bounce_Fixed.show($("#selectModule"))
                                        setEveryTime(bounce_Fixed, 'newOrg')
                                    } else {
                                        $("#tip .tipMs").html(res.msg);
                                        bounce_Fixed.show($("#tip"));
                                    }
                                }
                            })
                        } else {
                            layer.msg("系统错误！")
                        }
                    },
                    complete: function () {

                    }
                })
                break;
		}
    })
    $("#invoiceSet .setItem").click(function(){
        $("#invoiceSet .fa").attr("class","fa fa-circle-o");
        $(this).children(".fa").attr("class","fa fa-dot-circle-o");
    })
    $(".module_avatar").on("click", "input:checkbox", function () {
        var isChecked = $(this).prop("checked")
        $(this).closest(".module_row").children(".module_list").find("input:checkbox").prop("checked", isChecked)
        $(this).parents(".module_row").children(".module_item").find("input:checkbox").prop("checked", isChecked)
        if (!$(this).prop("checked") && ($(this).closest(".module_list").children().children(".module_item").find("input:checkbox:checked").length > 0)) {
            $(this).closest(".module_row").parents(".module_row").children(".module_item").find("input:checkbox").prop("checked", true)
        }
    })
    $("input[name='checkAll']").click(function () {
        $(".module_avatar input:checkbox").prop("checked", $(this).prop("checked"))
    })
    $("#updateCustormPanel").on('click','.edit', function(){
        var name = $(this).data('name');
        var type = $(this).data('type');
        var source = $(this).data('source');
        switch (name) {
            case 'receiveStopData':
            case 'mailStopData':
                let sendData = {
                    "customerId": $("#updateCustormPanel").data("id"),
                    "type": type
                }
                $("#stopDelContact tbody").children(":gt(0)").remove();
                let ttl = type =='1' ? "已被停用的收货信息" : "已被停用的发票邮寄信息" ;
                $("#stopDelContact .bonceHead span").html(ttl);
                $.ajax({
                    "url":"../sales/getSuspendAddress.do",
                    "data": sendData,
                    success:function (res) {
                        let list = res['addressList']|| [], status = res['status'],str='';
                        bounce_Fixed.show($("#stopDelContact"));
                        let ttltype = type =='1' ? "shAddressRecords" : "fpAddressRecords" ;
                        list.nullToStr();
                        for(let i in list){
                            let item = list[i];
                            str += `<tr data-id="${item.id}" data-isstop="ok">
                                    <td>${item.contact} <span class="ty-right">${item.mobile}</span></td>
                                    <td>
                                        <span class="edit2 ty-color-red" data-id="${item.id}" data-name="enableTurn" data-val="1">启用</span>
                                        <span class="ty-color-blue" data-type="${ttltype}" onclick="getRecordList($(this))">修改记录</span>
                                    </td>
                                </tr>`;
                        }
                        $("#stopDelContact tbody").append(str);
                    }
                });
                break;
            case 'contactDelData':
                $("#stopDelContact tbody").children(":gt(0)").remove();
                $("#stopDelContact .bonceHead span").html("已被删除的联系人");
                $.ajax({
                    "url":"../sales/getDeleteContactsList.do",
                    "data": { "customerId": $("#updateCustormPanel").data("id") },
                    success:function (res) {
                        let list = res['data']|| [], status = res['status'],str='';
                        bounce_Fixed.show($("#stopDelContact"));
                        list.nullToStr();
                        for(let i in list){
                            let item = list[i];
                            str += `<tr data-id="${item.id}" data-isstop="ok">
                                    <td>${item.name} <span class="ty-right">${item.post && item.post.substr(0,8) }</span></td>
                                    <td>
                                        <span class="edit2 ty-color-blue" onclick="getRecordList($(this))" data-obj="update" data-type="contactRecords">修改记录</span>
                                    </td>
                                </tr>`;
                        }
                        $("#stopDelContact tbody").append(str);
                    }
                });
                break;
            case 'receiveInfo':
                var customerId = $("#updateCustormPanel").data("id");
                $("#newReceiveInfo").data('type', type);
                $("#newReceiveInfo").data('source', source);
                if(type == 'new'){
                    $("#newReceiveInfo .bonceHead span").html('新增收货信息');
                    if($(".shAddressItem").length >= 10){
                        layer.msg('最多可录入10条收货信息。');
                        break;
                    }
                    $("#newReceiveInfo").data('id', customerId);
                    $("#newReceiveInfo input").val("");
                }else if(type == 'update'){
                    $("#newReceiveInfo .bonceHead span").html('修改收货信息');
                    var id = $(this).parents("tr").data('id');
                    $("#newReceiveInfo").data('id', id);
                    var data = {
                        addressId: id,
                        type: '1'
                    }
                    setAddress(1, data);
                }
                bounce_Fixed.show($("#newReceiveInfo"));
                setTimer('updateReceive');
                break;
            case 'mailInfo':
                var customerId = $("#updateCustormPanel").data("id");
                $("#newMailInfo").data('type', type);
                $("#newMailInfo").data('source', source);
                if(type == 'new'){
                    $("#newMailInfo .bonceHead span").html('新增发票邮寄信息');
                    if($(".mailAddressItem").length >= 10){
                        layer.msg('最多可录入10条发票邮寄信息。');
                        break;
                    }
                    $("#newMailInfo").data('id', customerId);
                    $("#newMailInfo input").val("");
                }else if(type == 'update'){
                    $("#newMailInfo .bonceHead span").html('修改发票邮寄信息');
                    var id = $(this).parents("tr").data('id');
                    $("#newMailInfo").data('id', id);
                    var data = {
                        addressId: id,
                        type: '2'
                    }
                    setAddress(2, data);
                }
                bounce_Fixed.show($("#newMailInfo"));
                setTimer('updateMail');
                break;
            case 'contactInfo':
                var customerId = $("#updateCustormPanel").data("id");
                $("#newContectInfo").data('type', type);
                $("#newContectInfo").data('source', source);
                document.getElementById('newContectData').reset();
                $(".otherContact").html("");
                $('#uploadCard').show();
                $("#contactsCard .bussnessCard").remove();
                $("#uploadCard").html("")
                initCardUpload($("#uploadCard"));
                if(type == 'new'){
                    $("#newContectInfo .bonceHead span").html('新增客户联系人');
                    $("#contactFlag").html('其他');
                    if($(".contactItem").length >= 50){
                        layer.msg('最多可录入50条联系人。');
                        break;
                    }
                    $("#newContectInfo").data("id", customerId);
                    $("#addMoreContact").hide();
                }else if(type == 'update'){
                    $("#newContectInfo .bonceHead span").html('修改客户联系人');
                    var contactId = $(this).parents("tr").data('id');
                    $("#newContectInfo").data('id', contactId);
                    $.ajax({
                        url: '/sales/getContactsSocial.do',
                        data: {
                            'contactId': contactId
                        },
                        success: function (data) {
                            var status = data.status;
                            if (status === '1' || status === 1) {
                                var getData = data.data;
                                setContact(getData);
                            } else {
                                layer.msg("停止失败！");
                            }
                        }
                    })
                }
                bounce_Fixed.show($("#newContectInfo"));
                setTimer('updateContact');
                break;
        }
    })
    $("#chooseCusContact").on("click", '.fa', function () {
        $("#chooseCusContact").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).attr("class", "fa fa-dot-circle-o")
    })
    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
    $("input[name='fullName']").on('blur', function () {
        var name = $(this).val()
        name = name.slice(0,6)
        $(this).parents("table").find("input[name='name']").val(name)
    })
    // 上传的合同删除
    $("#newContractInfo").on("click", '.fa-times', function () {
        let info = JSON.parse($(this).siblings(".hd").html())
        let fileUid = info.fileUid
        cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
        $(this).parent(".fileIm").remove();
    })

    $('#uploadFile').Huploadify({
        auto:false ,
        fileTypeExts:'*.xls;*.xlsx;',
        multi:false,
        formData:{
            module: '销售管理',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:40960,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader: "../uploads/uploadfyByFile.do",
        itemTemplate: '<div id="${fileID}" class="uploadify-queue-item matListUpload" style="display: none">' +
        '<div class="uploadify_bottom"><div><a class="uploadbtn" href="javascript:void(0);"></a></div></div>' +
        '</div>',
        onUploadStart:function(){
        },
        onInit:function(){},
        onSelect:function (file) {
            $('.matListUpload:not(:last)').remove();
            $('#custormerLeading .fileFullName').html(file.name);
        },
        onUploadComplete:function(file,data){
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data);
            var filePath = data.filename;
            var fileUid =data.fileUid;
            $('#custormerLeading .fileFullName').data('fileid', fileUid);
            importCustomer(filePath);
        } ,
        onCancel:function(file){
        }
    });
}) ;

// create:hxz 2021-06-01 合同 删除合同 确定
function contractDelTipOk() {
    let cInfo = JSON.parse( contractDel.siblings(".hd").html() );
    if(cInfo.fileCon1.length > 0){
        let groupUuid =cInfo.fileCon1[0].groupUuid;
        cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
    }
    if(cInfo.fileCon2.length > 0){
        let groupUuid =cInfo.fileCon2[0].groupUuid;
        cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
    }
    contractDel.parent().parent().remove();
    bounce_Fixed3.cancel()
}
// creater : 2021-5-24 hxz  合同的商品列表
function cScanScanGs(cid) {
    $.ajax({
        'url' : "../sales/getContractBase.do",
        "data":{ "id": cid },
        success:function (res) {
            var newcInfo = res.data
            var status = res.status
            if(status != 1){
                layer.msg("获取合同信息失败！");
                return false;
            }
            $("#tipcontractGoods .bonceHead span").html('本合同下的商品');
            $(".addOrCancel").hide(); $(".cScanc").show();
            let productList = newcInfo.productList || []
            setContactGS(productList , false);
            $("#cGTip").html(`本合同下的商品共有以下${ productList.length }种`)

        }
    })
}

// creater : 2021-5-24 hxz  合同详情
function cScan(cid) {
    $.ajax({
        'url' : "../sales/getContractBase.do",
        "data":{ "id": cid },
        success:function (res) {
            var newcInfo = res.data
            var status = res.status
            if(status != 1){
                layer.msg("获取合同信息失败！");
                return false;
            }
            bounce_Fixed2.show($("#cScan"))
            $("#cScan").data("cid",cid)
            $("#cScan .cNos").html(newcInfo.sn)
            $("#cScan .cSignDates").html(new Date(newcInfo.signTime).format("yyyy-MM-dd"))
            $("#cScan .cvalidDates").html(new Date(newcInfo.validStart).format("yyyy-MM-dd") + '至' + (new Date(newcInfo.validEnd).format("yyyy-MM-dd")))
            $("#cScan .cMemos").html(newcInfo.memo)
            let imgStr1 = ``
            newcInfo.contractBaseImages.forEach(function(bsIm){
                imgStr1 += `<span class="fileImScan" data-fun="imgScan" path="${bsIm.filePath}">
                                  <span>${bsIm.orders + 1 }</span>
                                  <span class="hd">${JSON.stringify(bsIm) }</span>
                             </span>`
            })
            $("#cScan .cImgs").html(imgStr1)
            $("#cScan .create").html(`${newcInfo.createName} ${ new Date(newcInfo.createDate).format("yyyy-MM-dd hh:mm:ss")  } `)
            if(newcInfo.filePath){
                $("#cScan .cWord").data('path', newcInfo.filePath).attr('path', newcInfo.filePath).show()
            }else{
                $("#cScan .cWord").hide();
            }
            let productList = newcInfo.productList || []
            $("#cScan .gNum").data('list', productList).html(productList.length)
            let enabledList = newcInfo.enabledList || [] , enableStr= ``;
            enabledList.forEach(function (enIm) {
                enableStr += `
                <p>
                    <span>${ enIm.enabled === 1 ? "恢复履约/重启合作" : "暂停履约/终止合作" }</span>
                    <span class="enName">${ enIm.updateName }</span>
                    <span>${ new Date(enIm.enabledTime).format("yyyy-MM-dd hh:mm:ss") }</span>
                </p>`;
            })
            $("#cScan .enabledList").html(enableStr)
        }
    })
}
// creater : 2021-5-24 hxz 已暂停/终止的合同
function contractInfoEdit(type, source, thisObj) {
    // type =new , source=addCustomer
    $("#newContractInfo").data('type', type).data('source', source);
    bounce_Fixed.show($("#newContractInfo"));
    $("#newContractInfo input").val("")
    $("#newContractInfo .fileCon>div").html("")
    initCUpload2($("#cUpload1"),'img');
    initCUpload2($("#cUpload2"),'doc');
    $(".cGS").hide()
    $(".cRenewalTip").hide();
    $("#newContractInfo .scanGs").data('gsArr',[]).data('gsArrNo',[]).html("0") ;

    if(source === 'updateCustomer'){
        $(".cGS").show()
        $.ajax({
            "url":"../sales/getContractCommodityList.do",
            "data":{ "customerId": $("#updateCustormPanel").data("id") },
            success:function (res) {
                let success = res.success
                let list = res.productList || []
                $("#newContractInfo .scanGs").data('gsArrNo',list) ;
            }
        });
    }
    if(type === 'new'){
        $("#newContractInfo .bonceHead span").html("新增合同")
    } else if(type === 'update'){
        $("#newContractInfo .bonceHead span").html("修改合同")
    }else if(type === 'cRenewal' ||type === 'cRenewalHistory'){
        $("#newContractInfo .bonceHead span").html("合同续约")
        $(".cRenewalTip").show();
    }

    if(type === 'update' || type === 'cRenewal' || type === 'cRenewalHistory'){
        editContractObj = thisObj
        if(source === 'addCustomer'){
            var newcInfo = JSON.parse(thisObj.siblings(".hd").html());
            $("#newContractInfo .cNo").val(newcInfo.cNo)
            $("#newContractInfo .cSignDate").val(newcInfo.cSignDate)
            $("#newContractInfo .cStartDate").val(newcInfo.cStartDate)
            $("#newContractInfo .cEndDate").val(newcInfo.cEndDate)
            $("#newContractInfo .cMemo").val(newcInfo.cMemo)
            let imgStr1 = ``
            newcInfo.fileCon1.forEach(function(data){
                imgStr1 = `<span class="fileIm" >
                                <span>${data.orders}</span>
                                <span class="fa fa-times"></span>
                                <span class="hd">${JSON.stringify(data) }</span>
                            </span>`
            })
            $("#newContractInfo .fileCon1").append(imgStr1)
            let imgStr2 = ``
            newcInfo.fileCon2.forEach(function(data){
                imgStr2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data)}</span>
                         </span>`
            })
            $("#newContractInfo .fileCon2").html(imgStr2)

        } else {
            // 接口获取合同详情
            let cid = thisObj.parents(".contractItem").data("id") ;
            $("#newContractInfo").data("cid",cid)
            $.ajax({
                'url' : "../sales/getContractBase.do",
                "data":{ "id": cid },
                success:function (res) {
                    var newcInfo = res.data
                    var status = res.status
                    if(status != 1){
                        layer.msg("获取原来的合同信息失败！");
                        return false;
                    }
                    $("#newContractInfo .cNo").val(newcInfo.sn)
                    $("#newContractInfo .cSignDate").val(new Date(newcInfo.signTime).format("yyyy-MM-dd") )
                    let start = new Date(newcInfo.validStart).format("yyyy-MM-dd")
                    let end = new Date(newcInfo.validEnd).format("yyyy-MM-dd")
                    $("#newContractInfo .cStartDate").val( start ).data("old", start);
                    $("#newContractInfo .cEndDate").val( end ).data("old", end);
                    $("#newContractInfo .cMemo").val(newcInfo.memo);
                    let imgStr1 = ``
                    newcInfo.contractBaseImages.forEach(function(bsIm){
                        imgStr1 += `<span class="fileIm"  >
                                                   <span>${bsIm.orders + 1}</span>
                                                   <span class="fa fa-times"></span>
                                                   <span class="hd">${JSON.stringify(bsIm) }</span>
                                              </span>`
                    })
                    $("#newContractInfo .fileCon1").append(imgStr1)
                    let imgStr2 = ``
                    if( newcInfo.filePath &&  newcInfo.filePath.length > 0){
                        imgStr2 = `<span class="fileIm"  >
                                                   <span class="fa fa-file-word-o"></span>
                                                   <span class="fa fa-times"></span>
                                                   <span class="hd">${ JSON.stringify({ "filename" : newcInfo.filePath , "originalFilename": newcInfo.fileName })  }</span>
                                              </span>`
                    }
                    $("#newContractInfo .fileCon2").html(imgStr2)
                    newcInfo.productList.forEach(function (info) {
                        $(".goodList").append(`<span class="gsIm">${ info.name }、<span class="hd">${ JSON.stringify(info) }</span></span>`);
                    })
                    $("#newContractInfo .scanGs").html(newcInfo.productList.length).data('gsArr', newcInfo.productList).data("addGsClickNum","0");

                }
            })
        }
    }
}

 // creater : 2021-5-24 hxz 已暂停/终止的合同
 function contractStopData(id) {
     $.ajax({
         "url":"../sales/getSuspendContractBaseList.do",
         "data":{ "customerId": id  },
         success:function (res) {
             let list = res.data || []
             let str = ``;
             list.forEach(function (item) {
                 str += `
                  <tr class="contractItem" data-id="${item.id}">
                    <td>${ item.sn }</td>
                    <td>${ new Date(item.enabledTime).format("yyyy-MM-dd hh:mm:ss")  }</td>
                    <td>
                        <span class="ty-color-blue edit2" data-name="cScan">查看</span>
                        <span class="ty-color-blue edit2" data-name="cRestart"> 恢复履约/重启合作</span>
                        <span class="hd">${ item.id }</span>
                    </td>
                </tr>
                 `;
             })
             $("#tb2 tr:gt(0)").remove();
             $("#tb2").append(str);
             bounce_Fixed.show($("#contractStopData"));
         }
     })
 }
 // creater : 2021-5-24 hxz 已到期的合同
 function contractEndData() {
     $.ajax({
         "url":"../sales/getContractBaseEndList.do",
         "data":{ "customerId":$("#updateCustormPanel").data("id")  },
         success:function (res) {
            let list = res.data || []
             let str = ``;
             list.forEach(function (item) {
                 str += `
                  <tr class="contractItem" data-id="${item.id}">
                    <td>${ item.sn }</td>
                    <td>${ new Date(item.validEnd).format("yyyy-MM-dd")  }</td>
                    <td>
                        <span class="ty-color-blue edit2" data-name="cScan">查看</span>
                        <span class="ty-color-blue edit2" data-name="cRenewal">续约</span>  
                        <span class="hd">${ item.id }</span>
                    </td>
                </tr>
                 `;
             })
             $("#tb1 tr:gt(0)").remove()
             $("#tb1").append(str)
             bounce_Fixed.show($("#contractEndData"))
         }
     })
 }
 // creater : 2021-5-24 hxz 暂停履约/终止合同
 function cEndOk() {
     let id = $("#cEndTip").data("cid")
     let enabled = $("#cEndTip").data("enabled")
     $.ajax({
         "url":"../sales/suspendOrStartContractBase.do",
         "data":{ "id":id , "enabled":enabled },
         success:function (res) {
             if(res.status === 1){
                 layer.msg("操作成功")
                 var cusid = $("#updateCustormPanel").data("id")
                 if(enabled == 1){
                     bounce_Fixed.cancel()
                     // contractStopData(cusid)
                 }
                 indexUpdateList(cus_seeTrObj);

             }else{
                 let con = res.data || "操作失败"
                 layer.msg(con)
             }
             bounce_Fixed3.cancel();
         }
     })
 }
 // creater : 2021-5-24 hxz 合同商品 操作 确定
 function addOrCancelOk() {
     let fun = $("#tipcontractGoods").data("fun");
     let listEdit = []
     $("#tipcontractGoods .fa-check-square-o").each(function(){
         let info =  JSON.parse($(this).siblings(".hd").html())
         let hasSame = false
         listEdit.push(info);
         $("#newContractInfo .goodList .gsIm").each(function () {
             let itemg = JSON.parse( $(this).find(".hd").html() );
             if(itemg.id === info.id){
                 if(fun === "addGs"){
                     hasSame = true
                 }else if(fun === "removeGs"){
                     $(this).remove();
                 }
             }
         })
         if(!hasSame && fun === "addGs"){
             $(".goodList").append(`<span class="gsIm">${ info.name }、<span class="hd">${ JSON.stringify(info) }</span></span>`);
         }
     })
     bounce_Fixed3.cancel()
     // 更新数据存储
     let listYes =  $("#newContractInfo .scanGs").data('gsArr');
     let listNo =  $("#newContractInfo .scanGs").data('gsArrNo') || [];
     if(fun === "addGs"){
         listYes.push(...listEdit)// 将新增的添加到 选中的
         let listNew = []
         listNo.forEach(function(noLm, index){ // 从未选中中 去掉 新增的
             let has = false
             listEdit.forEach(function(noLm2){
                if(noLm.id == noLm2.id){
                    has = true
                }
             })
             if(!has){
                 listNew.push(noLm)
             }
         })
         listNo = listNew
     } else if(fun === "removeGs"){
         listNo.push(...listEdit)
         let listNew = []
         listYes.forEach(function(noLm, index){ // 从未选中中 去掉 新增的
             let has = false
             listEdit.forEach(function(noLm2){
                 if(noLm.id == noLm2.id){
                     has = true
                 }
             })
             if(!has){
                 listNew.push(noLm)
             }
         })
         listYes = listNew
     }
     $("#newContractInfo .scanGs").data('gsArr', listYes).data('gsArrNo', listNo).html(listYes.length);
 }
 // creater : 2021-5-24 hxz 渲染合同里商品的列表
 function setContactGS(list, boolSet) {
     let str = '';
     let cstr = '';
     if(boolSet){
         cstr = '<span class="fa fa-square-o"></span>';
     }
     list = list || [];
     list.forEach(function (im) {
         str += `
          <tr>
              <td style="overflow: inherit;">
                <div class="controlTd">${cstr}${im.sn}<span class="hd">${JSON.stringify(im)}</span></div>
                </td>
              <td>${im.name}</td>
              <td>${im.model}</td>
              <td>${im.specifications}</td>
              <td>${im.unit}</td>
          </tr>`
     })
     $("#tipcontractGoods table tr:gt(0)").remove();
     $("#tipcontractGoods table").append(str);
     bounce_Fixed3.show($("#tipcontractGoods"));
 }
// creator: hxz 2021-05-21 操作上传的合同
function fileControl(obj){
    let funName = obj.data("fun");
    let fInfo = obj.siblings(".fInfo").html();
    fInfo = JSON.parse(fInfo)

}
// creator: hxz 2021-05-21 编辑合同 上传合同文件
function initCUpload2(obj,type){
    let fileTypeExtsStr = ''
    let multi = true
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(type == "doc"){
        multi = false
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    }
    let itemTemplate = ``
    obj.html("")
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi: multi,
        buttonText:"上传",
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data)
            if(type == "img"){
                let len =  $(`.fileCon1`).find(".fileIm").length;
                if(len < 9){
                    data.orders = len + 1
                    data.filePath = data.filename
                    data.title = data.originalFilename
                    let imgStr1 = `<span class="fileIm"  >
                                 <span>${ 1 + len }</span>
                                 <span class="fa fa-times"></span>
                                 <span class="hd">${ JSON.stringify(data) }</span>
                            </span>`
                    $(`.fileCon1`).append(imgStr1);
                }else{
                    layer.msg('最多只能上传9个文件')
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                }
            }else if(type == "doc"){
                let len =  $(`.fileCon2`).find(".fileIm").length;
                if(len === 0){
                }else{
                    let delO = $(`.fileCon2`).find(".fileIm")
                    let info = JSON.parse(delO.find(".hd").html())
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                    delO.remove();
                }
                let str2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data) }</span>
                        </span>`
                $(`.fileCon2`).html(str2);
            }
        }
    })
}

// creator: hxz 2021-05-21 编辑合同确定
function editContractOk(num) {
    let type = $("#newContractInfo").data('type'); // new新增 update修改 cRenewal 续约
    let source = $("#newContractInfo").data('source');
    if(num === 0){ // 取消, 删除上传的文件
        let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
        if(fileImArr.length > 0){
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }
        let file2= $("#newContractInfo .fileCon2 .fileIm")
        if(file2.length > 0){
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }

    }else{ // 确定
        let info = {}
        info.cNo = $("#newContractInfo .cNo").val()
        if(info.cNo.length === 0){
            layer.msg('请录入合同编号');
            return false
        }
        info.cSignDate = $("#newContractInfo .cSignDate").val()
        info.cStartDate = $("#newContractInfo .cStartDate").val()
        info.cEndDate = $("#newContractInfo .cEndDate").val()
        info.cMemo = $("#newContractInfo .cMemo").val()
        if(type === "cRenewal" || type === "cRenewalHistory"){
            let cStartDateOld = $("#newContractInfo .cStartDate").data("old");
            let ccEndDateOld = $("#newContractInfo .cEndDate").data("old");
            let endDateOld = ccEndDateOld || cStartDateOld
            let endOld = Date.parse(new Date(ccEndDateOld));
            if(endDateOld){
                if(info.cStartDate){
                    let start = Date.parse(new Date( info.cStartDate));
                    if(endOld >= start){
                        layer.msg(`合同有效期开始时间应晚于${ccEndDateOld}`);
                        return false
                    }
                }else{
                    layer.msg(`请录入晚于${ccEndDateOld}的合同有效期开始时间`)
                    return false
                }
            }
        }
        if(info.cStartDate && info.cEndDate){
            let start = Date.parse(new Date( info.cStartDate));
            let end = Date.parse(new Date( info.cEndDate));
            if(start > end){
                layer.msg('合同有效期开始时间应早于结束时间');
                return false
            }
        }

        info.fileCon1 = [];
        $("#newContractInfo .fileCon1 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon1.push(JSON.parse(itemf))
        })
        info.fileCon2 = [];
        $("#newContractInfo .fileCon2 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon2.push(JSON.parse(itemf))
        })
        info.goodList = [];
        $("#newContractInfo .goodList .gsIm").each(function () {
            let itemg = $(this).find(".hd").html();
            info.goodList.push(JSON.parse(itemg))
        })
        if(source == "addCustomer"){
            let str = `
                <td>${info.cNo}</td>
                <td>${info.cSignDate}</td>
                <td>${info.cStartDate} 至 ${info.cEndDate}</td>
                <td>
                    <span class="ty-color-blue node" data-name="contractInfo" data-type="update" data-source="addCustomer">修改</span>
                    <span class="ty-color-red node" data-name="contractDel">删除</span>
                    <span class="hd" >${ JSON.stringify(info) }</span>
                </td>
                `
            if(type == "new"){
                $("#clist").append(`<tr>${str}</tr>`);
                $(".contractList").show();

            } else if(type == "update"){
                var trObj = editContractObj.parent().parent();
                trObj.html(str)
            }
        }else  if (source == "updateCustomer"){
            let url = '', data = { 'customerId': $("#updateCustormPanel").data("id") }
            let filePath = '',fileName = '';
            if( info.fileCon2.length > 0  ){
                filePath = info.fileCon2[0].filename
                fileName = info.fileCon2[0].originalFilename
            }
            let goods = []
            if(info.goodList && info.goodList.length > 0){
                info.goodList.forEach(function(ig, index){
                    goods.push({
                        "productId":ig.id,
                        "order":index
                    })
                })
            }
            let imgs = []
            if(info.fileCon1 && info.fileCon1.length > 0){
                info.fileCon1.forEach(function(im, index){
                    imgs.push({
                        "filePath": im.filePath,
                        "order":index ,
                        "type":"1",
                        "title": im.title
                    })
                })
            }
            data.contractBase = {
                "sn": info.cNo,
                "signTime": info.cSignDate,
                "validStart": info.cStartDate,
                "validEnd": info.cEndDate,
                "filePath": filePath,
                "fileName": fileName,
                "memo": info.cMemo,
                "contractBaseImages": imgs,
                "productList": goods,
            }
            if(type == "new"){
                url = '../sales/addContractBase.do'
            } else if(type == "update"){
                url = '../sales/updateContractBase.do'
                data.contractBase.id = $("#newContractInfo").data("cid")
            } else if(type == "cRenewal" || type == "cRenewalHistory"){
                url = '../sales/renewalContractBase.do'
                data.contractBase.id = $("#newContractInfo").data("cid")
            }
            data.contractBase = JSON.stringify( data.contractBase )
            $.ajax({
                "url": url,
                "data": data ,
                success:function (res) {
                    let status = res.status
                    if(status === 1){
                        let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
                        if(fileImArr.length > 0){
                            let info = JSON.parse(fileImArr.find(".hd").html());
                            let groupUuid = info.groupUuid;
                            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} )
                        }
                        let file2= $("#newContractInfo .fileCon2 .fileIm")
                        if(file2.length > 0){
                            let info = JSON.parse(file2.find(".hd").html());
                            let groupUuid = info.groupUuid;
                            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid})
                        }
                        indexUpdateList(cus_seeTrObj, type);

                    }else{
                        if(type == "cRenewal" || type == "cRenewalHistory"){
                            layer.msg(res.msg);

                        }else {
                            layer.msg("操作失败！");
                        }
                    }

                }
            })
        }


    }
    bounce_Fixed.cancel()
}

// creator: hxz 2021-05-06 客户对发票方面的要求
function goset(thisObj) {
    let type = thisObj.data("type")
    $("#invoiceSet").data("type" , type).find(".fa-dot-circle-o").attr("class", "fa fa-circle-o")
    bounce_Fixed2.show($("#invoiceSet"))
}
function invoiceSetOk() {
    var selectP = $("#invoiceSet .fa-dot-circle-o").parent()
    let type = selectP.data("type");
    let txt = '尚未设置'
    if(Number(type) > 0  && Number(type) !== 4){
        txt = selectP.children("span").html();
    }
    $(".goset_" + $("#invoiceSet").data("type")).data("invoice", type).prev().html(txt);
    bounce_Fixed2.cancel();
}
// creator: hxz 2020-12-10 收货人那里的新增收货人
function chooseCusContactOk() {
    let selectObj = $("#chooseCusContact").find(".fa-dot-circle-o");
    if(selectObj.length > 0){
        let strInfo = selectObj.siblings("span.hd").html();
        let info = JSON.parse(strInfo);
        $($("#target").val())
            .val(selectObj.next().html()).data('orgData',selectObj.next().html())
            .siblings(".hd").html(strInfo) ;
        bounce_Fixed2.cancel();
        var source = $("#newReceiveInfo").data('source');
        if(source == 'addCustomer') {
            let numberSelect = info.number ;
            for(let q = 0 ; q < addCusContact.length ; q++){
                let contactItem = addCusContact[q];
                if(numberSelect == contactItem.number){
                    contactItem["select"] = true;
                }
            }
        }
    }else layer.msg('请先选择人员')
}
// creator: hxz 2020-12-10 新增联系人
function addContactInfo(num) {
    var type = "", source = "";
    if(num === 2){
        $("#newContectInfo .bonceHead span").html('新增客户联系人');
        $("#contactFlag").html('收货人');
        type = $("#newReceiveInfo").data('type');
        source = $("#newReceiveInfo").data('source');
    }else if(num === 3){
        $("#newContectInfo .bonceHead span").html('新增客户联系人');
        $("#contactFlag").html('发票接收人');
        type = $("#newMailInfo").data('type');
        source = $("#newMailInfo").data('source');
    }
    var customerId = $("#updateCustormPanel").data("id");

    // add receiveInfo/updateCustomer
    $("#newContectInfo").data('level',2);
    $("#newContectInfo").data('type',type);
    $("#newContectInfo").data('source', source);
    document.getElementById('newContectData').reset();
    $(".otherContact").html("");
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    if($(".contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
    }
    $("#newContectInfo").data("id", customerId);
    $("#addMoreContact").hide();
    $("#uploadCard").html("")
    initCardUpload($("#uploadCard"));
    bounce_Fixed.show($("#newContectInfo"));
    setTimer('updateContact');
}
// creator: hxz 2020-12-10 获取联系人列表
function getCusContactList(cusID) {
    $.ajax({
        "url":"../sales/getContactsList.do",
        "data":{ 'customerId': cusID },
        success:function (res) {
            if(res.status !== 1){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data'] || [] ;
            setCusListStr(list, 'updateCustomer')
        }
    });
}
function setCusListStr(list, source) {
    let str="";
    if(list.length === 0){
        $("#chooseCusContact .p0").show()
        $("#chooseCusContact .p1").hide()
        return false
    }
    $("#chooseCusContact .p0").hide()
    $("#chooseCusContact .p1").show()
    for(let i in list){
        let item = list[i];
        str += `<li>
                    <i class="fa fa-circle-o"></i>
                    <span>${item.name}</span>
                    <span>${item.post && item.post.substr(0,8)}</span>
                    <span>${item.mobile}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                    <span class="linkBtn ty-right" data-source="${source}" data-type="contactSocial" data-id="${item.id}" onclick="cus_recordDetail($(this))">查看</span>
                </li>`;
    }
    $("#chooseCusContact .cusList").html(str);
}
// creator: 张旭博，2019-04-03 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 新增客户
            case 'newTenant':
                var state = 0
                $(".newCustomer input[require]").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })
                if( state > 0){
                    $("#notSelectModuleBtn").prop("disabled",true)
                    $("#selectModuleBtn").prop("disabled",true)
                }else {
                    $("#notSelectModuleBtn").prop("disabled",false)
                    $("#selectModuleBtn").prop("disabled",false)
                }
                break;
            // 我要报销 - 货物录入
            case 'newOrg':
                var state = 0
                $("#selectModule .orgBase [require]").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })

                if ($("#submitModuleBtn").is(":visible")) {
                    if( state > 0 || $("#selectModule input:checkbox:checked").length === 0){
                        $("#submitModuleBtn").prop("disabled",true)
                    }else {
                        $("#submitModuleBtn").prop("disabled",false)
                    }
                } else {
                    var isModule = $(".module_avatar").is(":visible")
                    if (isModule && $("#selectModule input:checkbox:checked").length > 0 || !isModule && state === 0) {
                        $("#sureChangeOrgBtn").prop("disabled",false)
                    } else {
                        $("#sureChangeOrgBtn").prop("disabled",true)
                    }
                }
                break;
        }

    });
}

// creator: 李玉婷，2019-09-16 16:26:52，定时器--新增验证
function setTimer (timer) {
    switch (timer) {
        case 'updateReceive':
            bounce_Fixed.everyTime('0.5s','updateReceive',function () {
                var  filledNum = 0;
                $("#newReceiveInfo input").each(function(){
                    if ($(this).val() == '') filledNum++;

                });
                if(filledNum === 0 ){
                    $("#addReceive").prop("disabled",false);
                }else{
                    $("#addReceive").prop("disabled",true);
                }
            });
            break;
        case 'updateMail':
            bounce_Fixed.everyTime('0.5s','updateMail',function () {
                var state = 0, filledNum = 0;
                $("#newMailInfo input").each(function(){
                    if ($(this).val() == '') filledNum++;

                });
                if(filledNum === 0  ){
                    if (!is_postcode($("#newMailInfo #mailNumber").val())){
                        $("#mailNumError").show();
                        $("#addMail").prop("disabled",true);
                    }else{
                        $("#mailNumError").hide();
                        $("#addMail").prop("disabled",false);
                    }
                }else{
                    $("#addMail").prop("disabled",true);
                }
            });
            break;
        case 'updateContact':
            bounce_Fixed.everyTime('0.5s','updateContact',function () {
                var state = 0, filledNum = 0,otherContactLen = 0;
                var contactsCard = $("#contactsCard").data('org');
                var imgCard = $("#contactsCard .filePic").data('path');
                var len = $(".otherContact").data('length');
                if ($(".otherContact li").length > 0) {
                    $(".otherContact li").each(function () {
                        var val = $(this).find("input").val();
                        var type = $(this).find("input").data('type');
                        if (type == '9' || type == '9') type = 6
                        if (val == '') {
                            $("#addMoreContact option").eq(type).prop('disabled', true);
                        }else {
                            $("#addMoreContact option").eq(type).prop('disabled', false);
                            otherContactLen++;
                        }
                    })
                }
                if (len !=  otherContactLen) state ++;
                $("#newContectData [require]:visible").each(function(){
                    if ($(this).val() != '') filledNum++;
                    if($(this).val() != $(this).data('org')){
                        state ++;
                    }
                });
                if(contactsCard != imgCard) state ++;
                if(filledNum > 0 && state > 0){
                    $("#addContact").prop("disabled",false);
                }else{
                    $("#addContact").prop("disabled",true);
                }
            });
            break;
        case 'useDefinedLabel':
            bounce_Fixed2.everyTime('0.5s', 'useDefinedLabel',function () {
                var name = $.trim($("#defLable").val());
                if (name == '' || !name) {
                    $("#addNewLableSure").prop('disabled', true);
                } else {
                    $("#addNewLableSure").prop('disabled', false);
                }
            })
            break;
        case 'newInterview':
            bounce_Fixed.everyTime('0.5s', 'newInterview',function () {
                var state = 0;
                $("#newInterview").find("[require]:visible:not([disabled])").each(function(){
                    if($.trim($(this).val()) != ""){
                        state ++;
                    }
                })
                if (state != 0) {
                    $("#newInterviewSure").prop('disabled', false);
                } else {
                    $("#newInterviewSure").prop('disabled', true);
                }
            })
            break;
        case 'updateInterview':
            bounce_Fixed.everyTime('0.5s','updateInterview',function () {
                var state = 0, needNum = 0;
                $("#newInterview [require]:visible").each(function(){
                    if ($(this).val() != ''){
                        needNum++;
                    }
                    if($(this).val() != $(this).data('org')){
                        state ++;
                    }
                });
                if(needNum > 0 && state > 0){
                    $("#newInterviewSure").prop("disabled",false);
                }else{
                    $("#newInterviewSure").prop("disabled",true);
                }
            });
            break;
    }
}
// creator: 李玉婷，2021-08-16 08:20:02，客户查询
function searchCustomer() {
    var key = $(".main #se0").val();
    getCustomerMes(1 , 20, key);
}
// 获取商品基本信息列表
function getCustomerMes(currPage  , pageSize ,keyWord ){
	// currPage   当前页  pageSize    每页条数

	$.ajax({
		url:"../sales/getAllPdCustomerList.do",
		data:{
			"keyword":keyWord,
			"currPage":currPage,
			"pageSize":pageSize
		},
		success:function(data){
			var totalPage = data["totalPage"];
			var totalRows = data["totalRows"];
			var orderCustomerNum = data["orderCustomerNum"];
			$(".curNum").html(totalRows)
			$(".saleNum").html(orderCustomerNum)
			var cur = data["currPage"];
			var pdCustomers = data["pdCustomers"];
			$("#ye_customerManage").html("");
			var jsonStr = JSON.stringify({"keyWord": keyWord}) ;
			setPage( $("#ye_customerManage") , cur ,  totalPage , "customerManage" ,jsonStr );
			$("#cusManage_body").html("");
			if(pdCustomers !=undefined && pdCustomers.length > 0 ){
				var number = 0;
				for(var i=0 ; i<pdCustomers.length ; i++ ){
					number = 1 +i;
					var str = "<tr>" +
						"<td>"+ number + "</td>" +
						"<td>"+ handleNull(pdCustomers[i]["code"]) + "</td>" +
						"<td>"+ handleNull(pdCustomers[i]["fullName"]) + "</td>" +
						"<td>"+ handleNull(pdCustomers[i]["createName"]) + '&nbsp;'+ new Date(pdCustomers[i]["createDate"]).format('yyyy-MM-dd hh:mm:ss') + "</td>" +
						"<td>"+ handleNull(pdCustomers[i]["principalName"]) + "</td>" +
						"<td>" +
						"<span class='ty-color-blue' onclick='sale_seebtn($(this))'>查看</span>" ;
					str +="<div class='hd'>" +
						"<span class='productId'>"+ pdCustomers[i]["product_"]  +"</span>" +
						"<span class='cusProId'>"+ pdCustomers[i]["id"]  +"</span>" +
						"<span class='cusInfo'>"+ JSON.stringify(pdCustomers[i])  +"</span>" +
						"</div>" +
						"</td>"+
						"</tr>";
					$(".sale_Tbody").append(str);
				}
			}
		}
	})
}
// 新增按钮
var addCusContact = [];
function sale_addbtn(){
    if (chargeRole("超管")) { // 0 表示超管
        $("#mtTip .tip").html("您没有此权限？")
        bounce_Fixed.show($("#mtTip"))
		return false ;
	}
    addCusContact = [];

    $("#addAccount").data('receiveInfo', '[]');
    $("#addAccount").data('mailInfo', '[]');
    $("#addAccount").data('contactInfo', '[]');
    $("#addAccount").data('contractInfo', '[]');
    $("#addAccount .textMax").html('0/255');
    $(".receiveList").html('');
    $(".mailList").html('');
    $(".goset_add").data("invoice", 0).prev().html('尚未设置');
    $(".contectList").html('');
    $("#clist").html('');
    $("#qImages .imgsthumb").remove();
    $("#pImages .imgsthumb").remove();
    $(".purchased").hide();
    $("#addAccount .hasContract").hide();
    $("#addAccount .contractList").hide();
    $("#buyMonth").hide();
    document.getElementById('addpayDetails').reset();
    $("#panoramaBtn").html("")
    $("#productPicsBtn").html("")
    initUpload($("#panoramaBtn"));
    initUpload($("#productPicsBtn"));
    bounce.show($("#addAccount"));
    bounce.everyTime('0.5s','addAccount',function () {
        var state = 0
        $("#addAccount [require]:visible").each(function () {
            if ($(this).val() === '') {
                state ++
            }
        })
		var placeLength = $("#addAccount .receiveList tbody tr").length;
		var mailLength = $("#addAccount .mailList tbody tr").length;
		var contactLength = $("#addAccount .contectList tbody tr").length;
        if(placeLength >= 10){
        	$("#addAccount #newAddress").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
		}else{
            $("#addAccount #newAddress").data("name","receiveInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }
        if(mailLength >= 10){
            $("#addAccount #newMail").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
        }else{
            $("#addAccount #newMail").data("name","mailInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }
        if(contactLength >= 50){
            $("#addAccount #newContact").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
        }else{
            $("#addAccount #newContact").data("name","contactInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }
        if(state === 0){
            $("#addAccountBtn").prop("disabled",false);
            $("#notSelectModuleBtn").prop("disabled",false);
            $("#selectModuleBtn").prop("disabled",false);
        }else{
            $("#addAccountBtn").prop("disabled",true);
            $("#notSelectModuleBtn").prop("disabled",true);
            $("#selectModuleBtn").prop("disabled",true);
        }
	});
}
//updater:孟闯闯，2017-3-02  15:00:00，点击导入，弹出导入页面
function leading(){
	bounce.show($("#leading"));
	$("#appendix").val("");
}
//updater:孟闯闯，2017-3-02  15:00:00，增加导入确定功能
function leadingIn(num){
	var filepath="D:\\Test\\文本1.txt";
	var fso = new ActiveXObject("Scripting.FileSystemObject");
	var file = fso.GetFile(filepath);
	var file=$("#appendix").val();
	$.ajax({
		url:"../export/importCustomer.do",
		type:"post",
		data:{
			file:file,
		},
		dataType:"json",
		multipart:true,
		success:function(data){
			console.log(data)
		},
		error:function(){
		}
	})
}

// creator 侯杏哲 2017-03-07 处理导入客户后的反馈
var importTimer = 0 ; 
function reloadPage( ){
	loading.open();
	bounce.cancel() ;
	var t = 1 ;
	importTimer = setInterval(function(){
		t += 1 ; 
		var data = $("#importCon").contents().find("body").html() ;
		if(data == 0 || data == 1 ){
			loading.close() ; 
			if(data == 1 ){
				location.href="../../sales/customerManage.do" ;
			}else{
                $("#mtTip .tip").html("文件导入失败，请重新导入！");
                bounce_Fixed.show($("#mtTip"));
				clearInterval( importTimer ) ;
				$("#importCon").attr( "src" , "../../script/sales/cusImport.jsp") ;
			}
		}else{
			loading.close() ;
			if( t >= 20 ){
                $("#mtTip .tip").html("文件导入失败，请重新导入！");
                bounce_Fixed.show($("#mtTip"));
				clearInterval( importTimer ) ;
				$("#importCon").attr( "src" , "../../script/sales/cusImport.jsp") ;
			}
		}
	} , 500 ) ;
}

// 新增中的保存
function sale_addsure(){
    var shList =[];
    var imgsQ = [], imgsP = [] ;
    var data = {
        qImages: '',
        pImages: '',
        contractBaseList: '',
        shAddressList: '',
        fpAddressList: '',
        contactsList: ''
	};
	$('#addpayDetails input[type="text"]:visible').each(function(){
        var name = $(this).attr('name');
        data[name] = $(this).val();
    })
    if (sphdSocket.user.oid === 0) {
        if (!testMobile($.trim($("#addpayDetails input[name='supervisorMobile']").val()))) {
            layer.msg("请输入正确的手机号")
            return false
        }
    }
    var hasBuyCase = $("#addpayDetails input[name='buyCase']:checked").val();
    if (hasBuyCase === '1') {
        // 购买过产品
        var initialType = $("#addpayDetails input[name='firstTime']:checked").val();
        if(typeof initialType === 'undefined'){
            layer.msg('请选择首次购买时间');
            return false;
        }else{

            data.initialType = initialType;
            var date = '', month = '';
            var year = new Date().getFullYear();
            if(initialType === '1'){
                month = $("#addpayDetails #buyMonth").val();
                if(month === "") {
                    layer.msg('请选择月份');
                    return false;
                }else{
                    var arr = month.split('月');
                    if(arr[0] < 10){
                        date = year + '0' + arr[0];
                    }else{
                        date = year + arr[0];
                    }
                }
            }else if(initialType === '2'){
                year = Number(year) - 1;
                date = year + '01';
            }else if(initialType === '3'){
                year = Number(year) - 2;
                date = year + '01';
            }
            data.initialPeriod = date;
        }
    }
    $("#qImages .imgsthumb").each(function () {
		var path = {
            'normal': $(this).find(".filePic").data('path')
        };
		imgsQ.push(path);
    })
    $("#pImages .imgsthumb").each(function () {
        var path = {
            'normal': $(this).find(".filePic").data('path')
        };
        imgsP.push(path);
    })
	imgsQ = JSON.stringify(imgsQ);
	imgsP = JSON.stringify(imgsP);
	data.qImages = imgsQ;
	data.pImages = imgsP;
	if($("#firstContactTime").val() != ''){
        data.firstContactTime = $("#firstContactTime").val();
    }
	if($("#addpayDetails .receiveList tbody tr").length>0){
        $("#addpayDetails .receiveList tbody tr").each(function () {
            var json = {
                'customerContact': $(this).data("id"),
                'address': $(this).children().eq(1).html(),
                'contact': $(this).children().eq(2).html(),
                'mobile': $(this).children().eq(3).html()
            }
            shList.push(json);
        })
        shList = JSON.stringify(shList);
        data.shAddressList= shList;
	}
	let contractList = [];
	$("#clist tr").each(function(){
	    let infoc = JSON.parse($(this).find(".hd").html())
        let filePath = ''
        if( infoc.fileCon2.length > 0  ){
            filePath = infoc.fileCon2[0].filename
        }
        let imgs = []
        if(infoc.fileCon1 && infoc.fileCon1.length > 0){
            infoc.fileCon1.forEach(function(im, index){
                imgs.push({
                    "filePath": im.filename,
                    "order":index ,
                    "type":"1",
                    "title": im.originalFilename
                })
            })
        }
        contractList.push({
            "sn": infoc.cNo,
            "signTime": infoc.cSignDate,
            "validStart":infoc.cStartDate,
            "validEnd":infoc.cEndDate,
            "filePath": filePath,
            "memo": infoc.cMemo,
            "contractBaseImages": imgs
        })
    })
    data.contractBaseList= JSON.stringify(contractList);
    data.fpAddressList= $("#addAccount").data('mailInfo');
    data.contactsList= $("#addAccount").data('contactInfo');
    data.invoiceRequire = $(".goset_add").data("invoice") || 0
    if (sphdSocket.user.oid === 0) {
        $.ajax({
            url: '/special/getSaleCtrlsByMobile.do',
            data: {mobile: data.supervisorMobile},
            success: function (res) {
                var state = res.data
                if (state === 2) {
                    $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                    bounce_Fixed.show($("#tip"))
                    loading.close()
                } else if (state === 1) {
                    $.ajax({
                        url: "../sales/addPdCustomer.do",
                        data: data,
                        success: function (res) {
                            var status = res.status;
                            if (status === 1) {
                                layer.msg("新增成功");
                                var key = $(".main #se0").val();
                                getCustomerMes(1 , 20, key);
                                bounce.cancel();
                                bounce_Fixed.cancel();
                                var groupUuidArr = []
                                $("#addAccount [groupUuid]").each(function () {
                                    groupUuidArr.push({
                                        type: 'groupUuid',
                                        groupUuid: $(this).attr("groupUuid")
                                    })
                                })
                                cancelFileDel(groupUuidArr)

                            } else if (status === 2) {
                                $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                                bounce_Fixed.show($("#tip"))
                            } else {
                                $("#tip .tipMs").html("操作失败！");
                                bounce_Fixed.show($("#tip"));
                            };

                            $("#newReceiveInfo").data('id', '');
                            $("#newReceiveInfo").data('type', '');
                            $("#newReceiveInfo").data('source', '');
                            $("#newMailInfo").data('type', '');
                            $("#newMailInfo").data('id', '');
                            $("#newMailInfo").data('source', '');
                            $("#newContectInfo").data('type', '');
                            $("#newContectInfo").data('id', '');
                            $("#newContectInfo").data('source', '');
                        }
                    })
                } else {
                    layer.msg("系统错误！")
                }
            },
            complete: function () {

            }
        })
    } else {
        $.ajax({
            url: "../sales/addPdCustomer.do",
            data: data,
            success: function (res) {
                var status = res.status;
                if (status === 1) {
                    var key = $(".main #se0").val();
                    layer.msg("新增成功");
                    getCustomerMes(1 , 20, key);
                    bounce.cancel();
                    bounce_Fixed.cancel();
                } else if (status === 2) {
                    $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                    bounce_Fixed.show($("#tip"))
                } else {
                    $("#tip .tipMs").html("操作失败！");
                    bounce_Fixed.show($("#tip"));
                }
            }
        })
    }


}
function formatInviceRequire(type) {
    let str = ''
    switch (Number(type)){
        case 0 :    str = '尚未设置'; break
        case 1 :    str = '需要主要为增值税专用发票'; break
        case 2 :    str = '需要主要为增值税专用发票以外的发票'; break
        case 3 :    str = '基本不需要开发票'; break
        case 4 :    str = '尚未设置'; break
        default: str = '尚未设置'
    }
    return str
}
// 查看按钮
var cus_seeTrObj = null ;
function sale_seebtn(obj){
    $("#newReceiveInfo").data('id', '');
    $("#newReceiveInfo").data('type', '');
    $("#newReceiveInfo").data('source', '');
	bounce.show($("#seeAccount"));
	var id = obj.siblings(".hd").children(".cusProId").html();
	if ( id == undefined || id == ""){
        $("#mtTip .tip").html("系统错误，刷新重试！");
        bounce_Fixed.show($("#mtTip"));
		return false;
	}
    $("#seeAccount").data("id", id);
    $.ajax({
		url : "../sales/getPdCustomerOne.do" ,
		data : { "id":id },
		success:function(data){
			var tr_obj = obj.parent().parent();
			cus_seeTrObj = tr_obj;
			var pdCustomer = data["data"];
            var cuscoding = pdCustomer["code"];
            var invoiceRequire = pdCustomer["invoiceRequire"];
            var cusname = pdCustomer["name"];
            var cusfullName = pdCustomer["fullName"];
            var firstBuyTime = pdCustomer["firstBuyTime"];
            var address = pdCustomer["address"];
            var invoiceName = pdCustomer["invoiceName"];
            var invoiceAddress = pdCustomer["invoiceAddress"];
            var phone = pdCustomer["telephone"];
            var bank = pdCustomer["bankName"];
            var accountnum = pdCustomer["bank_no"];
            var taxpayerID = pdCustomer["taxpayerID"];
            var firstContactAddress = pdCustomer["firstContactAddress"];
            var infoSource = pdCustomer["infoSource"];
            var memo = pdCustomer["memo"];
            var createName = pdCustomer["createName"];
            var supervisorMobile = pdCustomer["supervisorMobile"];
            var supervisorName = pdCustomer["supervisorName"];
            var firstContactTime = new Date(pdCustomer["firstContactTime"]).format('yyyy-MM-dd');
            var createDate = new Date(pdCustomer["createDate"]).format('yyyy-MM-dd hh:mm:ss');
            var qImages = pdCustomer["qImages"];
            var pImages = pdCustomer["pImages"];
            var contactList = pdCustomer["contactsList"];
            var contactBaseList = pdCustomer["contactBaseList"] || [] ;
            var shAddressList = pdCustomer["shAddressList"];
            var fpAddressList = pdCustomer["fpAddressList"];
            var initialPeriod = pdCustomer["initialPeriod"];
            var date = '';
            $("#see_cuscoding").html(cuscoding);
            $("#see_cusname").html( cusfullName);
            $("#see_cusfullName").html(cusname);
            $("#firstBuyTime").html(firstBuyTime);
            $("#see_address").html(address);
            $("#see_phone").html(phone);
            $("#see_bank").html(bank);
            $("#see_invoiceRequire").html( formatInviceRequire(invoiceRequire));
            $("#see_bankNo").html(accountnum);
            $("#taxpayerID").html(taxpayerID);
            $("#see_firstContactTime").html(firstContactTime);
            $("#firstContactAddress").html(firstContactAddress);
            $("#see_createName").html(createName);
            $("#see_createDate").html(createDate);
            $("#see_supervisorName").html(supervisorName);
            $("#see_supervisorMobile").html(supervisorMobile);
            $("#infoSource").html(infoSource);
            $("#see_memo").html(memo);
            $("#see_invoiceName").html(invoiceName);
            $("#see_invoiceAddress").html(invoiceAddress);
            $("#telephone").html(invoiceAddress);
            if(qImages.length > 0){
                var imgStr = '';
                for(var a=0;a<qImages.length;a++){
                    var path = qImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">' +
                        '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                        '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                        '</div>';
                }
                $("#overallImgUpload").html(imgStr);
            }else{
                $("#overallImgUpload").html("");
            }
            if(pImages.length > 0){
                var imgStr = '';
                for(var a=0;a<pImages.length;a++){
                    var path = pImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">' +
                        '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                        '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                        '</div>';
                }
                $("#productImgUpload").html(imgStr);
            }else{
                $("#productImgUpload").html('');
            }
            // 处理是否购买过本公司的产品
            var hasBuyStr = ''
            var initialType = pdCustomer.initialType;
            if (initialType === null) {
                hasBuyStr = '<div class="ty-alert ty-alert-info">'+createDate + '之前该客户尚未购买过本公司的商品</div>'
            } else {
                if(initialType === '1'){
                    var month = initialPeriod.substr(4,2);
                    if(month.slice(0,1) == '0'){
                        month = month.substr(1,1);
                    }
                    date = initialPeriod.substr(0,4) + '年'  + month  + '月';
                    hasBuyStr = '<div class="ty-alert ty-alert-info">'+date+'该客户首次购买过本公司的商品</div>'
                }else if(initialType === '2'){
                    hasBuyStr = '<div class="ty-alert ty-alert-info">去年该客户首次购买过本公司的商品</div>'
                }else{
                    hasBuyStr = '<div class="ty-alert ty-alert-info">去年之前该客户首次购买过本公司的商品</div>'
                }

            }
            $("#seeAccount .firstBuyTime").html(hasBuyStr)

            $(".see_contactList").html('');
            $(".recivePlaceList tbody").html('');
            $(".mailPlaceList tbody").html('');
            $(".contactNum").html(contactList.length);
            var htmlContract =  ''
            contactBaseList.forEach(function (cIm) {
                htmlContract += `
                <tr data-id="${ cIm.id }" class="contractItem">
                    <td>${ cIm.sn }</td>
                    <td>${ new Date(cIm.signTime).format("yyyy-MM-dd")}</td>
                    <td>${new Date(cIm.validStart).format("yyyy-MM-dd")}  至  ${new Date(cIm.validEnd).format("yyyy-MM-dd")} </td>
                    <td> <span class="linkBtn node" data-name="scanGs">${ cIm.num }</span>种</td>
                    <td><span class="ty-color-blue node" data-name="cScan">查看</span></td>
                </tr>
                `
            })
            $(".contractPlaceList tbody").html(htmlContract);

            if(contactList.length > 0){
                var rhtml = '';
                var html = '<div class="leftList"><table class="ty-table ty-table-control">' +
                    '<thead>' +
                    '<tr>' +
                    '    <td>姓名</td>' +
                    '    <td>职位</td>' +
                    '    <td>操作</td>' +
                    '</tr>' +
                    '</thead><tbody>';
                if(contactList.length >= 2){rhtml = html;}
                for(var b in contactList){
                    var slice = b % 2;
                    if(slice > 0){
                        rhtml +=
                            '<tr class="'+(contactList[b].enabled == 1?'':'disable')+'" data-id="' + contactList[b].id + '">' +
                            '    <td>' + contactList[b].name + '</td>' +
                            '    <td>' + contactList[b].post + '</td>' +
                            '    <td>' +
                            '<span class="ty-color-blue" data-id="' + contactList[b].id + '" data-type="contactSocial" onclick="cus_recordDetail($(this))">联系方式</span>' +
                            '<span class="ty-color-blue" data-id="' + contactList[b].id + '" data-type="visitCard" onclick="cus_recordDetail($(this))">查看名片</span>' +
                            '<span class="ty-color-blue" data-type="contactRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                            '</tr>';
                    }else{
                        html +=
                            '<tr class="'+(contactList[b].enabled == 1?'':'disable')+'" data-id="' + contactList[b].id + '">' +
                            '    <td>' + contactList[b].name + '</td>' +
                            '    <td>' + contactList[b].post + '</td>' +
                            '    <td>' +
                            '<span class="ty-color-blue" data-id="' + contactList[b].id + '" data-type="contactSocial" onclick="cus_recordDetail($(this))">联系方式</span>' +
                            '<span class="ty-color-blue" data-id="' + contactList[b].id + '" data-type="visitCard" onclick="cus_recordDetail($(this))">查看名片</span>' +
                            '<span class="ty-color-blue" data-type="contactRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                            '</tr>';
                    }
                }
                html += '</tbody></table></div>';
                if(contactList.length >= 2){rhtml += '</tbody></table></div>';}
                var result = html + rhtml;
                    $(".see_contactList").html(result);
            }
            if(shAddressList.length > 0){
                var html = '';
                for(var b in shAddressList){
                    var num = 1 + Number(b);
                    html +=
                        '<tr class="'+(shAddressList[b].enabled == 1?'':'disable')+'" data-id="' + shAddressList[b].id + '">' +
                        '    <td>' + num + '</td>' +
                        '    <td>' + shAddressList[b].address + '</td>' +
                        '    <td>' + shAddressList[b].contact + '</td>' +
                        '    <td>' + shAddressList[b].mobile + '</td>' +
                        '    <td><span class="ty-color-blue" data-type="shAddressRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                        '</tr>';
                }
                $(".recivePlaceList tbody").html(html);
            }
            if(fpAddressList.length > 0){
                var html = '';
                for(var b in fpAddressList){
                    var num = 1 + Number(b);
                    html +=
                        '<tr class="'+(fpAddressList[b].enabled == 1?'':'disable')+'" data-id="' + fpAddressList[b].id + '">' +
                        '    <td>' + num + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].address) + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].contact) + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].postcode) + '</td>' +
                        '    <td><span class="ty-color-blue" data-type="fpAddressRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                        '</tr>';
                }
                $(".mailPlaceList tbody").html(html);
            }
		}
	})
}
// 基本信息、开票信息修改按钮
function sale_updatabtn(obj){
    if (chargeRole("超管")) { // 0 表示超管
        $("#mtTip .tip").html("您没有此权限！");
        bounce_Fixed.show($("#mtTip"));
		return false ;
	}
    var objName = obj.data('name');
    var id =     $("#updateCustormPanel").data("id");
	if ( id == undefined || id == ""){
        $("#mtTip .tip").html("系统错误，刷新重试！");
        bounce_Fixed.show($("#mtTip"));
		return false;
	}
    document.getElementById('sale_updataBase').reset();
	$("#initialPeriod").hide();
	$("#sale_updataBase .hasContract").hide();
    $("#updataccount").data("changed", false);
    $.ajax({
		url : "getPdCustomerOne.do" ,
		data : { "id":id},
		success:function(data){
            var getData = data.data;
		    if (objName == 'updateBase'){
                var pImages = getData["pImages"];
                var qImages = getData["qImages"];
                var firstContactTime = new Date(getData['firstContactTime']).format('yyyy/MM/dd');
                $("#sale_updataBase input[type='text']").each(function(){
                    var name = $(this).attr('name');
                    $(this).val(getData[name]);
                    $(this).data('orgData',getData[name]);
                });
                $("#edit_firstTime").val(firstContactTime).data('orgData',firstContactTime);
                $("#edit_qImages .imgsthumb").remove();
                $("#edit_pImages .imgsthumb").remove();
                $("#edit_qImages").data('orgData',qImages);
                $("#edit_pImages").data('orgData',pImages);
                if(pImages.length > 0){
                    var html = setImgHtml(2, pImages);
                    $("#edit_pImages .imgWall").html(html);
                }
                if(qImages.length > 0){
                    var html = setImgHtml(2, qImages);
                    $("#edit_qImages .imgWall").html(html);
                }
                //是否购买过
                $("#sale_updataBase [name='buyCase']").data('orgData','');
                $(".purchased [type='radio']").data('orgData','');
                $("#initialPeriod").data('orgData','');
                if (getData.initialType == null) {
                    $("#sale_updataBase [name='buyCase'][value='0']").data('orgData','0').click();
                }else {
                    $("#sale_updataBase [name='buyCase'][value='1']").data('orgData','1').click();
                    $(".purchased [type='radio'][value='"+ getData.initialType +"']").data('orgData',getData.initialType).click();
                    if (getData.initialType == '1'){
                        if (getData.initialPeriod != null) {
                            var month = getData.initialPeriod;
                            month = month.substr(4,2);
                            if(month.substring(0,1) == '0') {
                                month = month.substr(1,1) + '月';
                            } else{
                                month = month + '月';
                            }
                            $("#initialPeriod").data('orgData',month).val(month);
                        }
                    }
                    if (getData.hasContract === '1') {
                        $("#sale_updataBase [name='hasContract'][value='1']").data('orgData','1').click();
                        var contractSn = getData.contractSn || ''
                        var expiresTime = getData.expiresTime?new Date(getData.expiresTime).format('yyyy/MM/dd') : ''
                        var contractTime = getData.contractTime?new Date(getData.contractTime).format('yyyy/MM/dd') : ''
                        $("#sale_updataBase [name='contractSn']").data('orgData',contractSn).val(contractSn);
                        $("#sale_updataBase [name='expiresTime']").data('orgData',expiresTime).val(expiresTime);
                        $("#sale_updataBase [name='contractTime']").data('orgData',contractTime).val(contractTime);
                    } else {
                        $("#sale_updataBase [name='hasContract'][value='0']").data('orgData','0').click();
                    }
                }
                $("#edit_panoramaBtn").html('')
                $("#edit_productPicsBtn").html('')
                initUpload($("#edit_panoramaBtn"));
                initUpload($("#edit_productPicsBtn"));

                bounce_Fixed.show($("#updataccount"));
                bounce_Fixed.everyTime('0.5s','updateAccount',function () {
                    var state = 0;
                    var status = 0
                     var buyCase = $("#sale_updataBase [name='buyCase']:checked");
                    var firstTime = $("#sale_updataBase [name='firstTime']:checked");
                    var qjPicArr = $("#edit_qImages").data('orgData');
                    var cpPicArr = $("#edit_pImages").data('orgData');
                    var qjPicLen = $("#edit_qImages .filePic").length;
                    var cpPicLen = $("#edit_pImages .filePic").length;
                    $("#sale_updataBase [require]:visible").each(function(){
                        if ($(this).val() === '') {
                            status++
                        }
                    });
                    if(buyCase.val() != buyCase.data('orgData')) state ++;
                    if(firstTime.val() != firstTime.data('orgData')) state ++;
                    if(qjPicLen == qjPicArr.length && qjPicLen != 0){
                        $("#edit_qImages .filePic").each(function(){
                            var elem = qjPicArr.find(value => value.normal === $(this).data('path'));
                            if(elem == undefined) state ++;
                        })
                    }else{
                        if(qjPicLen > 0 || qjPicArr.length > 0) state ++;
                    }
                    if(cpPicLen == cpPicArr.length && cpPicLen != 0){
                        $("#edit_pImages .filePic").each(function(){
                            var elem = cpPicArr.find(value=>value.normal ===$(this).data('path'));
                            if(elem == undefined) state ++;
                        })
                    }else{
                        if(cpPicLen > 0 || cpPicArr.length > 0) state ++;
                    }
                    if(status === 0){
                        $("#updateAccountBtn").prop("disabled",false);
                    }else{
                        $("#updateAccountBtn").prop("disabled",true);
                    }
                });
            }else{
                document.getElementById('sale_updataInvoice').reset();
                $("#sale_updataInvoice input").each(function(){
                    var name = $(this).data('name');
                    $(this).val(getData[name]);
                    $(this).data('orgData', handleNull(getData[name]));
                })
                $(".goset_update").data("invoice", getData.invoiceRequire ).data("orgData", getData.invoiceRequire )
                    .prev().html(formatInviceRequire(getData.invoiceRequire));
                bounce_Fixed.show($("#updateInvoice"));
                bounce_Fixed.everyTime('0.5s','updataInvoice',function () {
                    var state = 0,filledNum = 0;
                    $("#sale_updataInvoice [require]:visible").each(function(){
                        if ($(this).val() != '') filledNum++;
                        if($(this).val() != $(this).data('orgData')){
                            state ++;
                        }
                    });
                    if($(".goset_update").data("invoice") != $(".goset_update").data("orgData") ){
                        state ++;
                    }
                    if(filledNum>0 && state > 0){
                        $("#updataInvoiceSure").prop("disabled",false);
                    }else{
                        $("#updataInvoiceSure").prop("disabled",true);
                    }
                });
            }
		}
	})
}
// 修改中基本信息修改确定
function updata_sure(){
    var id = $("#updateCustormPanel").data("id");
	if ( id == undefined || id == ""){
        $("#mtTip .tip").html("系统错误，刷新重试！");
        bounce_Fixed.show($("#mtTip"));
		return false;
	}
    if (sphdSocket.user.oid === 0) {
        if (!testMobile($.trim($("#updataccount input[name='supervisorMobile']").val()))) {
            layer.msg("请输入正确的手机号")
            return false
        }
    }

	var data = {
	    'id': id,
	    'qImages': '[]',
        'pImages': '[]'
    };
	$("#sale_updataBase input[type='text']:visible").each(function(){
	    var name = $(this).attr('name');
        data[name] = $(this).val();
    })
    if($("#edit_firstTime").val() != ""){
	    data.firstContactTime = $("#edit_firstTime").val();
    }

    var hasBuyCase = $("#sale_updataBase input[name='buyCase']:checked").val();
    if (hasBuyCase === '1') {
        // 购买过产品
        var initialType = $("#sale_updataBase input[name='firstTime']:checked").val();
        if(typeof initialType === 'undefined'){
            layer.msg('请选择首次购买时间');
            return false;
        }else{
            data.initialType = initialType;
            var date = '', month = '';
            var year = new Date().getFullYear();
            if(initialType === '1'){
                month = $("#sale_updataBase [name='initialPeriod']").val();
                if(month === "") {
                    layer.msg('请选择月份');
                    return false;
                }else{
                    var arr = month.split('月');
                    if(arr[0] < 10){
                        date = year + '0' + arr[0];
                    }else{
                        date = year + arr[0];
                    }
                }
            }else if(initialType === '2'){
                year = Number(year) - 1;
                date = year + '01';
            }else if(initialType === '3'){
                year = Number(year) - 2;
                date = year + '01';
            }
            data.initialPeriod = date;
        }
    } else {
        // 选择否的时候什么字段都不需要传
    }

    if($("#edit_qImages .imgsthumb").length > 0){
        var imgArr = [];
        $("#edit_qImages .imgsthumb").each(function(){
            var path = $(this).find(".filePic").data('path');
            var json = {
                'normal': path
            };
            imgArr.push(json);
        })
        imgArr = JSON.stringify(imgArr);
        data.qImages = imgArr;
    }
    if($("#edit_pImages .imgsthumb").length > 0){
        var imgArr = [];
        $("#edit_pImages .imgsthumb").each(function(){
            var path = $(this).find(".filePic").data('path');
            var json = {
                'normal': path
            };
            imgArr.push(json);
        })
        imgArr = JSON.stringify(imgArr);
        data.pImages = imgArr;
    }
    data.invoiceRequire = $(".goset_update").data("invoice") || 0
    $.ajax({
        url: '/special/getSaleCtrlsByMobile.do',
        data: {mobile: data.supervisorMobile,id: data.id},
        success: function (res) {
            var state = res.data
            if (state === 2) {
                $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                bounce_Fixed.show($("#tip"))
                loading.close()
            } else if (state === 1) {
                $.ajax({
                    url : "/sales/updatePdCustomerBase.do" ,
                    data : data,
                    success:function(data){
                        var status = data["status"];
                        if( status == 0 || status == "0"){
                            $("#mtTip .tip").html("操作失败！");
                            bounce_Fixed.show($("#mtTip"));
                            return false;
                        }
                        // 取消已提交文件的删除
                        var groupUuidArr = []
                        var key = $(".main #se0").val();
                        $("#addAccount [groupUuid]").each(function () {
                            groupUuidArr.push({
                                type: 'groupUuid',
                                groupUuid: $(this).attr("groupUuid")
                            })
                        })
                        cancelFileDel(groupUuidArr)

                        indexUpdateList(cus_seeTrObj);
                        getCustomerMes(1 , 20, key);
                        bounce_Fixed.cancel();
                    }
                })
            } else {
                layer.msg("系统错误！")
            }
        }
    })
	
}
// creator: 李玉婷，2019-09-07 11:36:29，修改开票信息确定
function updataInvoice_sure(){
    var id =     $("#updateCustormPanel").data("id");
    var data = {
        'id': id
    };
    $("#sale_updataInvoice input").each(function(){
        var name = $(this).data('name');
        data[name] = $(this).val();
    })
    data['invoiceRequire'] =  $(".goset_update").data("invoice")
    $.ajax({
        url: "/sales/updatePdCustomerInvoice.do",
        data: data,
        type: "post",
        dataType: "json",
        success: function (data) {
            var status = data.status;
            if(status == '1'){
                bounce_Fixed.cancel();
                indexUpdateList(cus_seeTrObj);
            }
        }
    })
}
// 删除按钮
function sale_delbtn(obj){
    if (chargeRole("超管")) { // 0 表示超管
        layer.msg("您没有此权限！")
	}
	cus_seeTrObj = obj.parent().parent();
	var id = obj.siblings(".hd").children(".cusProId").html();
	if ( id == undefined || id == ""){  // 校验销售id 是否合法
        layer.msg("系统错误，刷新重试！")
		return false; // 结束
	}
	bounce.show($("#deleteAccount"));
	var del_cuscode = cus_seeTrObj.children(":eq(1)").html();  // 获取客户代号，客户名称
	var del_cusName = cus_seeTrObj.children(":eq(2)").html();
	var str = "确定删除客户代号为：" + del_cuscode + " , 客户名称为：" + del_cusName + " 的客户吗？";
	$("#customerDelTip").html(str); // 给提示信息位置赋值
	$("#del_saleID").html(id); //  给销售ID赋值
}
// 删除中的确定按钮
function sale_delsure(){
	var id = $("#del_saleID").html();
	if ( id == undefined || id == ""){  // 校验销售id 是否合法
        $("#mtTip .tip").html("系统错误，刷新重试！"); //  不合法的提示信息赋值
        bounce_Fixed.show($("#mtTip"));
		return false; // 结束
	}
	$.ajax({
		url:"deletePdCustomerOne.do",
		data:{
			"id":id
		},
		success:function (data){
            bounce.cancel();
            var status = data["status"];
			if(status == 1 ){
				layer.msg("删除成功！")
				cus_seeTrObj.remove();
			}else if( status == 0 ){
				$("#mtTip .tip").html("该客户下已有数据，不可删除！");
                bounce_Fixed.show($("#mtTip"));
			}else{
                $("#mtTip .tip").html("系统错误！");
                bounce_Fixed.show($("#mtTip"));
			}
		}
	})
}
//creator:lyt Date:2018/11/19 修改-删除按钮
function deleteContact(obj){
    var id = obj.parents("tr").data('id');
    $("#turnTipMs").html('删除后，本联系人依旧显示在查看中，但仅可查看。');
    $("#oprationSure").data('name','deleteContact');
    $("#oprationSure").data('id', id);
    bounce_Fixed.show($("#turnStateTip"));
}
//creator:lyt Date:2018/11/19 修改-停用按钮
function turnBtn(obj){
    var id = obj.parents("tr").data('id');
    $("#turnTipMs").html('确定'+ obj.html() +'吗？');
    $("#oprationSure").data('val',obj.data("able"));
    $("#oprationSure").data('name','enableTurn');
    $("#oprationSure").data('id', id);
    bounce_Fixed.show($("#turnStateTip"));
}

// creator: 张旭博，2020-10-27 08:25:22，暂停合作 - 按钮（打开确认弹窗）
function suspendCooperationBtn(selector) {

    var customerId = selector.siblings(".hd").find('.cusProId').html()
    bounce.show($("#confirmSuspendCooperation"))
    $("#confirmSuspendCooperation").data("customerId", customerId)
    if ($(".suspendedCustomer").is(":visible")) {
        $("#confirmSuspendCooperation .start").show().siblings().hide()
    } else {
        $("#confirmSuspendCooperation .stop").show().siblings().hide()
    }

}

// creator: 张旭博，2020-10-27 08:41:28，暂停合作 - 确认
function sureSuspendCooperation() {
    var customerId = $("#confirmSuspendCooperation").data("customerId")
    var state = $(".suspendedCustomer").is(":visible")?0:1
    $.ajax({
        url: '../sales/suspendCooperation.do',
        data: {
            customerId: customerId,
            state:state
        },
        success: function (res) {
            var status = res.status
            if (status === 1) {
                layer.msg("操作成功")
                if ($(".suspendedCustomer").is(":visible")) {
                    getSuspendCustomer(1, 20)
                } else {
                    var key = $(".main #se0").val();
                    getCustomerMes(1 , 20, key)
                }
                bounce.cancel()
            } else {
                layer.msg("操作失败")
            }
        }
    })
}

// creator: 张旭博，2020-10-27 08:51:47，已暂停合作的客户 - 按钮
function suspendedCustomer() {
    $(".suspendedCustomer").show().siblings().hide()
    getSuspendCustomer(1, 20)
}

// creator: 张旭博，2020-10-27 09:47:30，获取暂停/恢复合作历史记录列表
function getSuspendRecordList() {
    bounce_Fixed.show($("#suspendRecord"))
    var customerId = $("#seeAccount").data("id")
    $.ajax({
        url: '../sales/getSuspendOperationList.do',
        data: {
            customerId: customerId
        },
        success: function (res) {
            var data = res.data
            var str = ''
            for(var i in data) {
                str +=  '<tr>' +
                            '<td>' + (data[i].isSuspend === '1'?'暂停合作':'恢复合作') +'</td>' +
                            '<td>' + data[i].updateName + ' ' + formatTime(data[i].updateDate, true) + '</td>' +
                        '</tr>'
            }
            $("#suspendRecord tbody").html(str)
        }
    })
}

function goBack() {
    var key = $(".main #se0").val();
    $(".main").show().siblings().hide()
    getCustomerMes(1, 20 ,key)
}

// creator: 张旭博，2020-10-27 08:50:54，获取已暂停合作的客户
function getSuspendCustomer(currPage, pageSize) {
    $.ajax({
        url: '../sales/getAllSuspendCustomerList.do',
        data: {
            currPage:currPage,
            pageSize:pageSize
        },
        success: function (res) {
            var totalPage = res.totalPage
            var currPage = res.currPage
            var pdCustomers = res.pdCustomers
            setPage( $("#ye_suspendCustomer") , currPage ,  totalPage , "suspendCustomer");

            var str = ''
            pdCustomers.nullToStr();
            for (var i = 0; i < pdCustomers.length; i++) {
                str += '<tr>' +
                    '<td>'+ (Number(i) + 1)+'</td>'+
                    '<td>'+pdCustomers[i].code+'</td>'+
                    '<td>'+pdCustomers[i].fullName+'</td>'+
                    '<td>'+pdCustomers[i].createName + ' ' + formatTime(pdCustomers[i].createDate, true)+'</td>'+
                    '<td>'+pdCustomers[i].updateName + ' ' + formatTime(pdCustomers[i].updateDate, true)+'</td>'+
                    '<td>'+
                    '   <span class="ty-color-blue" onclick="sale_seebtn($(this))">查看</span>'+
                    '   <div class="hd"><span class="cusProId">'+pdCustomers[i].id+'</span></div>'+
                    '</td>'+
                    '</tr>'
            }
            $(".suspendedCustomer tbody").html(str)
        }
    })
}
// creator: 李玉婷，2019-09-04 10:20:07，初始化上传图片
function initUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"上传",
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            var data = JSON.parse(data)
            let len =  obj.siblings("div.imgWall").find(".imgsthumb").length;
            if(len < 9){
                $(".uploadify-queue").html("");
                //file 文件上传返回的参数  data 接口返回的参数
                var path = data.filename //路径（包含文件类型）
                //name = file.name,           //文件名称
                obj.parent().attr("groupUuid", data.groupUuid )
                var imgStr =
                    '<div class="imgsthumb">' +
                    '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                    '    <span fileUid="' + data.fileUid + '" onclick="cancleThis($(this))">删除</span> ' +
                    '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                    '</div>';
                obj.siblings("div.imgWall").append(imgStr);
            }else{
                layer.msg('最多只能上传9张')
                let fileUid = data.fileUid
                cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
            }
        }
    });
}
// creator: 李玉婷，2019-09-17 15:09:14，名片上传
function initCardUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        itemTemplate: '',
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var path = data.filename //路径（包含文件类型）
            obj.parent().attr("groupUuid", data.groupUuid )
            var imgStr =
                '<div class="bussnessCard">' +
                '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '   <span fileUid="' + data.fileUid + '" class="ty-color-blue" onclick="cancleCard($(this))">删除</span> ' +
                '</div>';
            $('#uploadCard').hide();
            $('#uploadCard').before(imgStr);
        }
    });
}
// creator: 李玉婷，2019-09-04 19:31:34，删除名片
function cancleCard(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
	obj.parent().remove();
    $('#uploadCard').show();
}
// creator: 李玉婷，2019-08-21 14:46:56，图片删除
function cancleThis(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent(".imgsthumb").remove();
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.attr('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");
}
// creator: 李玉婷，2019-09-17 16:44:02，是否购买商品点击事件
function purchasedAny (obj){
    if(obj.val() == '1' || obj.val() == 1){
        obj.parents('form').find(".purchased").show();
    }else{
        obj.parents('form').find(".purchased").hide();
        obj.parents('form').find(".purchased").find("input[name=initialPeriod]").val('').hide();
        obj.parents('form').find(".purchased").find("input[type=radio]").prop('checked',false);
        obj.parents('form').find(".hasContract").hide();
        obj.parents('form').find(".hasContract input").val('')
    }
}
// creator: 张旭博，2020-10-13 09:52:57，是否签订合同
function isContract (obj){
    if(obj.val() == '1' || obj.val() == 1){
        obj.parents('form').find(".hasContract").show();
    }else{
        obj.parents('form').find(".hasContract").hide();

    }
}
// creator: 李玉婷，2019-09-04 14:17:55，选择今年
function checkMonth(obj) {
	if(obj.val() == '1' || obj.val() == 1){
        obj.parent().siblings("input").show();
	}else{
        obj.parent().siblings("input").hide().val("");
    }
}
// creator: 李玉婷，2019-09-04 18:55:15，添加
function addMore (obj){
	obj.next("select").show();
}
function setShList(data){
    var html =
		'<table class="ty-table ty-table-control">' +
		'<thead> ' +
        '     <tr>' +
        '         <td>序号</td>' +
        '         <td>收货地址</td>' +
        '         <td>收货人</td>' +
        '         <td>收货电话</td>' +
        '         <td>操作</td>' +
        '     </tr>' +
		'</thead>' +
		'<tbody>';
    for (var i in data) {
    	var num  = 1 + Number(i);
        html +=
            '<tr data-id="' + data[i].number + '">' +
            '    <td>' + num + '</td>' +
            '    <td>' + data[i].address + '</td>' +
            '    <td>' + data[i].contact + '</td>' +
            '    <td>' + data[i].mobile + '</td>' +
            '    <td>' +
            '       <span class="ty-color-blue node" data-type="update" data-name="receiveInfo" data-source="addCustomer">修改</span>' +
            '       <span class="ty-color-red node" data-type="delete" data-name="receiveInfo">删除</span>' +
            '       <span class="hd">'+ data[i].contactInfoNumber +'</span>' +
            '   </td>' +
            '</tr>';
    }
    html +=
        '</tbody></table>';
    $(".receiveList").html(html);
	var listData = JSON.stringify(data);
    $("#addAccount").data('receiveInfo', listData);
}
// creator: 李玉婷，2019-09-05 10:43:09，发票邮寄列表
function setMailList(list) {
    var html =
		'<table class="ty-table ty-table-control">' +
        '<thead> ' +
        '<tr>' +
        '    <td>序号</td>' +
        '    <td>邮寄地址</td>' +
        '    <td>发票接收人</td>' +
        '    <td>邮政编码</td>' +
        '    <td>联系电话</td>' +
        '    <td>操作</td>' +
        '</tr>' +
		'</thead><tbody>';
    for (var i in list) {
    	var num = 1 + Number(i);
        html +=
            '<tr data-id="' + list[i].number + '">' +
            '    <td>' + num + '</td>' +
            '    <td>' + list[i].address + '</td>' +
            '    <td>' + list[i].contact + '</td>' +
            '    <td>' + list[i].postcode + '</td>' +
            '    <td>' + list[i].mobile + '</td>' +
            '    <td>' +
            '       <span class="ty-color-blue node" data-type="update" data-source="addCustomer" data-name="mailInfo">修改</span>' +
            '       <span class="ty-color-red node" data-type="delete" data-name="mailInfo">删除</span>' +
            '       <span class="hd">'+ list[i].contactInfoNumber +'</span>' +
            '   </td>' +
            '</tr>';
    }
    html += '</tbody></table>';
        $(".mailList").html(html);
    var mailInfo = list;
    mailInfo = JSON.stringify(mailInfo)
    $("#addAccount").data('mailInfo', mailInfo);
}
// creator: 李玉婷，2019-09-07 10:16:22，输出联系人列表
function setContactList(list) {
    var html = '', rhtml = '',slice = 0;
    if(list && list.length > 0){
        html =
            '<div class="leftList"> <table class="ty-table ty-table-control">' +
            '<thead> ' +
            '<tr>' +
            '    <td>姓名</td>' +
            '    <td>职位</td>' +
            '    <td>操作</td>' +
            '</tr>' +
            '</thead><tbody>';
        if(list.length >= 2) rhtml = html;
        for (var i=0;i < list.length; i++) {
            list[i]['number'] = list[i].number || list[i].id
            slice = i %2 ;
            if(slice > 0){
                rhtml +=
                    '<tr data-id="'+ list[i].number +'" groupUuid="'+list[i].groupUuid+'">' +
                    '    <td>' + list[i].name + '</td>' +
                    '    <td>' + list[i].post + '</td>' +
                    '    <td><span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer">修改</span><span class="ty-color-red node" data-type="delete" data-name="contactInfo">删除</span></td>' +
                    '</tr>';
            }else{
                html +=
                    '<tr data-id="'+ list[i].number +'" groupUuid="'+list[i].groupUuid+'">' +
                    '    <td>' + list[i].name + '</td>' +
                    '    <td>' + list[i].post + '</td>' +
                    '    <td><span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer">修改</span><span class="ty-color-red node" data-type="delete" data-name="contactInfo">删除</span></td>' +
                    '</tr>';
            }
        }
        html += '</tbody></table></div>';
        if(list.length >= 2) rhtml += '</tbody></table></div>';
    }
    var str = html + rhtml;
    var tt = JSON.stringify(list);
    $("#addAccount").data('contactInfo', tt);
    return str;
}



// creator: 李玉婷，2019-09-06 11:12:09，主页对客户列表的修改
function indexUpdateList(obj, cRenewalType){
    if (chargeRole("超管")) { // 0 表示超管
        $("#mtTip .tip").html("您没有此权限！");
        bounce_Fixed.show($("#mtTip"));
        return false ;
    }
    cus_seeTrObj = obj;
    var nodeId = obj.siblings(".hd").find(".cusProId").html();
    $("#updateCustormPanel").data("id", nodeId);
    var custonerName = $.trim(obj.parents("tr").children().eq(2).html());
    $("#customer_main").html(custonerName);
    $.ajax({
        url: '/sales/getUpdatePageData.do',
        data: {
            'customerId': nodeId
        },
        success: function (data) {
            var status = data.status;
            if (status === '1' || status === 1) {
                var contactsList = data.contactsList;
                var fpAddressList = data.fpAddressList;
                var shAddressList = data.shAddressList;
                var contractBaseList = data.contractBaseList;
                $(".contractItem").remove();
                $(".shAddressItem").remove();
                $(".mailAddressItem").remove();
                $(".contactItem").remove();
                if(shAddressList.length > 0){
                    var html = '';
                    for(var d in shAddressList){
                        if (shAddressList[d].enabled){
                            html +=
                                '<tr class="shAddressItem" data-id="'+ shAddressList[d].id +'">' +
                                '    <td><div class="sign">'+ shAddressList[d].address +'</div></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue edit" data-name="receiveInfo" data-type="update" data-source="updateCustomer">修改</span>' +
                                '        <span class="ty-color-blue" data-type="shAddressRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '        <span class="ty-color-red" data-able="0" onclick="turnBtn($(this))">停用</span>' +
                                '    </td>' +
                                '</tr>';
                        }else{
                            html +=
                                '<tr class="shAddressItem" data-id="'+ shAddressList[d].id +'">' +
                                '    <td><div class="signed">'+ shAddressList[d].address +'</div></td>' +
                                '    <td>' +
                                '        <span class="ty-color-red" data-able="1" onclick="turnBtn($(this))">启用</span>' +
                                '        <span class="ty-color-blue" data-type="shAddressRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                    }
                    $("#receivePanel").after(html);
                }
                if(contractBaseList.length > 0){
                    var html = '';
                    contractBaseList.forEach(function(cIm){
                        html += `
                            <tr class="contractItem" data-id="${cIm.id}">
                                <td><div class="sign">${cIm.sn}</div></td>
                                <td>
                                    <span class="ty-color-blue node" data-name="cScan" >查看</span>
                                    <span class="ty-color-blue node" data-name="contractInfo" data-type="update" data-source="updateCustomer">修改</span>
                                    <span class="ty-color-blue node" data-name="contractInfo" data-type="cRenewal" data-source="updateCustomer" >续约</span>
                                    <span class="ty-color-red node" data-name="cEnd">暂停履约/终止合同</span>
                                </td>
                            </tr>
                        `
                    });
                    $("#contractPanel").after(html);
                }
                if(fpAddressList.length > 0){
                    var html = '';
                    for(var d in fpAddressList){
                        if (fpAddressList[d].enabled){
                            html +=
                                '<tr class="mailAddressItem" data-id="'+ fpAddressList[d].id +'">' +
                                '    <td><div class="sign">'+ fpAddressList[d].address +'</div></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue edit" data-name="mailInfo" data-type="update" data-source="updateCustomer">修改</span>' +
                                '        <span class="ty-color-blue" data-type="fpAddressRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '        <span class="ty-color-red" data-able="0" onclick="turnBtn($(this))">停用</span>' +
                                '    </td>' +
                                '</tr>';
                        }else{
                            html +=
                                '<tr class="mailAddressItem" data-id="'+ fpAddressList[d].id +'">' +
                                '    <td><div class="signed">'+ fpAddressList[d].address +'</div></td>' +
                                '    <td>' +
                                '        <span class="ty-color-red" data-able="1" onclick="turnBtn($(this))">启用</span>' +
                                '        <span class="ty-color-blue" data-type="fpAddressRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                    }
                    $("#mailPanel").after(html);
                }
                if(contactsList.length > 0){
                    var html = '';
                    for(var d in contactsList){
                        if (contactsList[d].enabled){
                            html +=
                                '<tr class="contactItem" data-id="'+ contactsList[d].id +'">' +
                                '    <td><div class="sign">' +
                                '           <span>'+ contactsList[d].name +'</span>' +
                                '           <span>'+ contactsList[d].post +'</span>' +
                                '           <span>'+ contactsList[d].mobile +'</span>'+ '</div></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue edit" data-name="contactInfo" data-type="update" data-source="updateCustomer">修改</span>' +
                                '        <span class="ty-color-blue" data-type="contactRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '        <span class="ty-color-red" onclick="deleteContact($(this))">删除</span>' +
                                '    </td>' +
                                '</tr>';
                        }else{
                            html +=
                                '<tr class="contactItem" data-id="'+ contactsList[d].id +'">' +
                                '    <td><div class="signed">' + contactsList[d].name +'</div></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue" data-type="contactRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                    }
                    $("#contactPanel").after(html);
                }
                bounce.show($("#updateCustormPanel"));
                if(cRenewalType == "cRenewalHistory"){
                    var id = $("#updateCustormPanel").data("id")
                    contractEndData(id )
                }
            } else {
                layer.msg("查看失败！");
            }
        }
    })
}
// creator: 李玉婷，2019-09-06 14:26:59，图片字符串输出
function setImgHtml(type,imgs) { // type: 1=查看 2= 修改
    var html = '';
    for(var e in imgs){
        var path = imgs[e].normal;
        html +=
            '<div class="imgsthumb">' +
            '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>';
        if(type == '2'){
            html += '    <span onclick="cancleThis($(this))">删除</span> ';
        }
        html +=
        '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a></div>';
    }
    return html;
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContact li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li>' +
                '<span class="sale_ttl1">手机：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="1" data-name="手机" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '2':
            html +=
                '<li>' +
                '<span class="sale_ttl1">QQ：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '3':
            html +=
                '<li>' +
                '<span class="sale_ttl1">Email：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '4':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微信：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '5':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微博：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '9':
            $("#useDefinedLabel input").val("");
            bounce_Fixed2.show($("#useDefinedLabel"));
            setTimer('useDefinedLabel');
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-07 08:50:59，删除添加的联系方式
function removeAdd(obj) {
    obj.parent("li").remove();
    if ($(".otherContact li").length > 0) {
        $(".otherContact li").each(function () {
            var val = $(this).find("input").val();
            var type = $(this).find("input").data('type');
            if (type == '9' || type == '9') type = 6
            if (val == '') {
                $("#addMoreContact option").eq(type).prop('disabled', true);
            }else {
                $("#addMoreContact option").eq(type).prop('disabled', false);
            }
        })
    }else{
        $("#addMoreContact option").prop('disabled', false);
    }
}
// creator: 李玉婷，2019-09-07 16:09:24，获取客户收货，发票地址
function setAddress(type, json){
    $.ajax({
        url: '/sales/getAddressData.do',
        data: json,
        success: function (data) {
            var status = data.status;
            if (status === '1' || status === 1) {
                var getData = data.data;
                var res = {
                    "address" : getData.address,
                    "type" : getData.type,
                    "name" : getData.contact,
                    "post" : getData.postcode,
                    "mobile" : getData.mobile,
                    "linkId" : getData.id,
                    "id" : getData.customerContact,
                }
                if(type == '1'){
                    $("#ReceiveName")
                        .val(res.name)
                        .siblings(".hd").html(JSON.stringify(res)) ;
                    $("#newReceiveInfo").data('id', res.linkId);
                    $("#ReceiveAddress").val(res.address) ;
                }else if(type == '2'){
                    $("#newMailInfo").data('id',  res.linkId);
                    $("#mailName")
                        .val(res.name)
                        .siblings(".hd").html(JSON.stringify(res)) ;
                    $("#mailAddress").val(res.address) ;
                }
            } else {
                layer.msg("查看失败！");
            }
        }
    })
}
// creator: 李玉婷，2019-09-09 17:19:22，修改联系人-相同输出部分字符串拼接
function setContact(data) {
    var socialList = data.socialList;
    $("#contactFlag").html(data.tags ||'其他');
    $("#contactName").val(data.name).data('org',data.name);
    $("#position").val(data.post).data('org',data.post);
    $("#contactNumber").val(data.mobile).data('org',data.mobile);
    $("#contactsCard").data('org',data.visitCard);
    $("#addMoreContact").hide();
    if (data.visitCard && data.visitCard != '' && data.visitCard != 'undefined' && data.visitCard != 'null'){
        var path = data.visitCard;
        var imgStr =
            '<div class="bussnessCard">' +
            '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
            '   <span class="ty-color-blue" onclick="cancleCard($(this))">删除</span> ' +
            '</div>';
        $("#uploadCard").hide();
        $("#uploadCard").before(imgStr);
    }else{
        $("#uploadCard").show();
    }
    if(socialList.length > 0){
        var html = '';
        for(var r in socialList){
            html +=
                '<li>' +
                '<span class="sale_ttl1">'+ socialList[r].name +'：</span>' +
                '<span class="gap"><input type="text" value="'+ socialList[r].code +'" placeholder="请录入" data-type="'+ socialList[r].type +'" data-name="'+ socialList[r].name +'" data-org="'+ socialList[r].code +'" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
        }
        $(".otherContact").html(html).data('length', socialList.length);
    }
}
function bounceCancel() {
    let isStop = $("#from").val();
    if(isStop == 'ok'){
        bounce.show($("#stopDelContact"))
    }
    bounce_Fixed2.cancel()
}
// creator: 李玉婷，2019-09-09 19:52:16，修改记录列表获取公用方法
function getRecordList(obj) {
    var type = obj.data('type');
    var getObj = obj.data('obj');
    var customerId = '';
    if(getObj == 'see'){
        customerId = $("#seeAccount").data("id");
    }else if(getObj == 'update'){
        customerId = $("#updateCustormPanel").data("id");
    }else if(getObj == 'interview'){
        customerId = obj.parents("tr").data("id");
    }
    switch (type) {
        case 'baseRecords':
            $(".recordTtl").html('基本信息修改记录');
            $.ajax({
                url: '/sales/getRecordBaseList.do',
                data: {
                    'customerId': customerId
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
        case 'invoiceRecords':
            $(".recordTtl").html('开票信息修改记录');
            $.ajax({
                url: '/sales/getRecordInvoiceList.do',
                data: {
                    'customerId': customerId
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
        case 'contactRecords':
            $(".recordTtl").html('联系人修改记录');
            $.ajax({
                url: '/sales/getRecordContactList.do',
                data: {
                    'contactId': obj.parents('tr').data('id')
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
        case 'shAddressRecords':
        case 'fpAddressRecords':
            var addressId = obj.parents('tr').data('id');
            var isStop = obj.parents('tr').data('isstop');
            $("#from").val(isStop)
            $.ajax({
                url: '/sales/getRecordAddressList.do',
                data: {
                    'addressId': addressId
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        if(type == 'shAddressRecords'){
                            $(".recordTtl").html('收货信息修改记录');
                        }else{
                            $(".recordTtl").html('发票邮寄信息修改记录');
                        }
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
    }
}
/*修改记录前、后查看*/
function cus_recordDetail(obj){
    $(".initValue").html("");
    var json ={
        'id': obj.data('id'),
        'frontId': obj.data('frontid')
    };
    var seeType = obj.data('type');
    switch(seeType){
        case 'baseRecords':
            $.ajax({
                url : "../sales/getRecordBaseDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    var name = nowData.fullName;
                    var fullName = nowData.name;
                    var code = nowData.code;
                    var address = nowData.address;
                    var supervisorName = nowData.supervisorName;
                    var supervisorMobile = nowData.supervisorMobile;
                    var memo = nowData.memo;
                    var infoSource = nowData.infoSource;
                    var initialPeriod = nowData.initialPeriod;
                    var firstContactTime = new Date(nowData.firstContactTime).format('yyyy-MM-dd');
                    var createDate = new Date(nowData.createDate).format('yyyy-MM-dd hh:mm:ss')
                    var firstContactAddress = nowData.firstContactAddress;
                    var qImages = nowData.qImages;
                    var pImages = nowData.pImages;
                    var date = '';
                    if (qImages.length > 0){
                        var html = setImgHtml(1,qImages);
                        $("#base_qImgUpload").html(html);
                    }
                    if (pImages.length > 0){
                        var html = setImgHtml(1,pImages);
                        $("#base_pImgUpload").html(html);
                    }


                    // 处理是否购买过本公司的产品
                    var hasBuyStr = ''
                    var initialType = nowData.initialType;
                    if (initialType === null) {
                        hasBuyStr = '<div class="ty-alert ty-alert-info">'+createDate + '之前该客户尚未购买过本公司的商品</div>'
                    } else {
                        if(initialType === '1'){
                            var month = initialPeriod.substr(4,2);
                            if(month.slice(0,1) == '0'){
                                month = month.substr(1,1);
                            }
                            date = initialPeriod.substr(0,4) + '年'  + month  + '月';
                            hasBuyStr = '<div class="ty-alert ty-alert-info">'+date+'该客户首次购买过本公司的商品</div>'
                        }else if(initialType === '2'){
                            hasBuyStr = '<div class="ty-alert ty-alert-info">去年该客户首次购买过本公司的商品</div>'
                        }else{
                            hasBuyStr = '<div class="ty-alert ty-alert-info">去年之前该客户首次购买过本公司的商品</div>'
                        }
                        // 处理是否签合同
                        var hasContract = nowData.hasContract
                        if (hasContract === '1') {
                            var sn = []
                            var contractSn = nowData.contractSn
                            var expiresTime = nowData.expiresTime
                            var contractTime = nowData.contractTime

                            if (contractTime) sn.push('签署日期为'+new Date(contractTime).format("yyyy年MM月dd日"))
                            if (expiresTime) sn.push('有效期至'+new Date(expiresTime).format("yyyy年MM月dd日"))
                            if (contractSn) sn.push('合同编号为'+contractSn)
                            if (sn.length === 0) {
                                hasBuyStr += '<div class="ty-alert ty-alert-info">与该客户有处于有效期内的合同</div>'
                            } else {
                                hasBuyStr += '<div class="ty-alert ty-alert-info">与该客户有'+sn.join("、")+'的合同</div>'
                            }
                        } else {
                            hasBuyStr += '<div class="ty-alert ty-alert-info">目前与该客户无处于有效期内的合同</div>'
                        }
                    }
                    $("#BaseRecordsDetail .firstBuyTime").html(hasBuyStr)

                    if(frontData == null){
                        $("#BaseRecordsDetail .detaileTtl").html('原始信息');
                        $("#base_cusname").html(name);
                        $("#base_cusfullName").html(fullName);
                        $("#base_cuscoding").html(code);
                        $("#base_address").html(address);
                        $("#base_supervisorName").html(supervisorName);
                        $("#base_supervisorMobile").html(supervisorMobile);
                        $("#base_firstContactTime").html(firstContactTime);
                        $("#base_firstAddress").html(firstContactAddress);
                        $("#base_infoSource").html(infoSource);
                        $("#base_memo").html(memo);
                    }else{
                        $("#BaseRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#base_cusname").html(compareD(frontData.name,nowData.name));
                        $("#base_cusfullName").html(compareD(frontData.fullName,nowData.fullName));
                        $("#base_cuscoding").html(compareD(frontData.code,nowData.code));
                        $("#base_address").html(compareD(frontData.address,nowData.address));
                        $("#base_supervisorName").html(compareD(frontData.supervisorName, nowData.supervisorName));
                        $("#base_supervisorMobile").html(compareD(frontData.supervisorMobile, nowData.supervisorMobile));
                        $("#base_firstContactTime").html(compareD(new Date(frontData.firstContactTime).format('yyyy-MM-dd'),new Date(nowData.firstContactTime).format('yyyy-MM-dd')));
                        $("#base_firstAddress").html(compareD(frontData.firstContactAddress,nowData.firstContactAddress));
                        $("#base_infoSource").html(compareD(frontData.infoSource,nowData.infoSource));
                        $("#base_memo").html(compareD(frontData.memo,nowData.memo));
                    }
                    bounce_Fixed3.show($("#BaseRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'invoiceRecords':
            $.ajax({
                url : "../sales/getRecordInvoiceDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    if(frontData == null){
                        $("#invoiceRecordsDetail .detaileTtl").html('原始信息');
                        $("#inv_invoiceName").html(nowData.invoiceName);
                        $("#inv_phone").html(nowData.telephone);
                        $("#inv_invoiceAddress").html(nowData.invoiceAddress);
                        $("#inv_bank").html(nowData.bankName);
                        $("#inv_bankNo").html(nowData.bankNo);
                        $("#inv_taxpayerID").html(nowData.taxpayerID);
                    }else{
                        $("#invoiceRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#inv_invoiceName").html(compareD(frontData.invoiceName,nowData.invoiceName));
                        $("#inv_phone").html(compareD(frontData.telephone,nowData.telephone));
                        $("#inv_invoiceAddress").html(compareD(frontData.invoiceAddress,nowData.invoiceAddress));
                        $("#inv_bank").html(compareD(frontData.bankName,nowData.bankName));
                        $("#inv_bankNo").html(compareD(frontData.bankNo,nowData.bankNo));
                        $("#inv_taxpayerID").html(compareD(frontData.taxpayerID,nowData.taxpayerID));
                    }
                    bounce_Fixed3.show($("#invoiceRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'shAddressRecords':
            $.ajax({
                url : "../sales/getRecordShAddressDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    if(nowData.operation == '2' || frontData == null){
                        if(nowData.operation == '2'){
                            var tip = nowData.enabled == '1'? '本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+ nowData.updateName +'启用。':'本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+nowData.updateName+'停用。'
                            $('.shDisUse').show().html(tip);
                        }else{
                            $('.shDisUse').hide();
                        }
                        $("#shRecordsDetail .detaileTtl").html('原始信息');
                        $("#shAddress").html(handleNull(nowData.address));
                        $("#shName").html(handleNull(nowData.contact));
                        $("#shNumber").html(handleNull(nowData.mobile));
                    }else{
                        $('.shDisUse').hide().html('');
                        $("#shRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#shAddress").html(compareD(frontData.address,nowData.address));
                        $("#shName").html(compareD(frontData.contact,nowData.contact));
                        $("#shNumber").html(compareD(frontData.mobile,nowData.mobile));
                    }
                    bounce_Fixed3.show($("#shRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'fpAddressRecords':
            $.ajax({
                url : "../sales/getRecordFpAddressDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    if(nowData.operation == '2' || frontData == null){
                        if(nowData.operation == '2'){
                            var tip = nowData.enabled == '1'? '本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+nowData.updateName+'启用。':'本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+nowData.updateName+'停用。'
                            $('.fpDisUse').show().html(tip);
                        }else{
                            $('.fpDisUse').hide();
                        }
                        $("#fpRecordsDetail .detaileTtl").html('原始信息');
                        $("#fpAddress").html(handleNull(nowData.address));
                        $("#fpName").html(handleNull(nowData.contact));
                        $("#fpNumber").html(handleNull(nowData.postcode));
                        $("#fpMobile").html(handleNull(nowData.mobile));
                    }else{
                        $('.fpDisUse').html('').hide();
                        $("#fpRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#fpAddress").html(compareD(frontData.address,nowData.address));
                        $("#fpName").html(compareD(frontData.contact,nowData.contact));
                        $("#fpNumber").html(compareD(frontData.postcode,nowData.postcode));
                        $("#fpMobile").html(compareD(frontData.mobile,nowData.mobile));
                    }
                    bounce_Fixed3.show($("#fpRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'contactRecords':
            $.ajax({
                url : "../sales/getRecordContactDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front']||{};
                    var beforeSocialList = frontData ? frontData.socialList :[];
                    var socialList = nowData.socialList;
                    var frontSocialList = [];
                    if(frontData == null){
                        $("#contactRecordsDetail .detaileTtl").html('原始信息');
                        $("#record_contactName").html(nowData.name);
                        $("#record_position").html(nowData.post);
                    }else{
                        $("#contactRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#record_contactName").html(compareD(frontData.name,nowData.name));
                        $("#record_position").html(compareD(frontData.post,nowData.post));
                    }
                    $("#record_contactsCard").html("");
                    if (nowData.visitCard != '' && nowData.visitCard != undefined && nowData.visitCard != null){
                        var path = nowData.visitCard;
                        var imgStr =
                            '<div class="bussnessCard">' +
                            '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                            '</div>';
                        $("#record_contactsCard").html(imgStr);
                    }

                    for(let q in socialList){
                        let qItem = socialList[q], getSame = false;
                        for(let w in beforeSocialList){
                            let wItem = beforeSocialList[w];
                            if(qItem['contactSocial'] == wItem['contactSocial']){
                                getSame = true
                                if(qItem['name'] != wItem['name']){
                                    qItem['nameChange'] = true;
                                }
                                if(qItem['code'] != wItem['code']){
                                    qItem['codeChange'] = true;
                                }
                            }
                        }
                        if(!getSame){
                            qItem['nameChange'] = true;
                            qItem['codeChange'] = true;
                        }
                    }
                    socialList.push({ name: "手机", code:nowData.mobile, codeChange:(nowData.mobile != frontData.mobile),type: "1"})
                    //
                    let sortList = [];
                    for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
                    for(var r in socialList){
                        let item = socialList[r];
                        let _index = Number(item.type);
                        sortList[_index].push(item);
                    }
                    let sortAfter = [];
                    for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
                    let allStr = '';
                    for(var t in sortAfter){
                        let item = sortAfter[t];
                        if(t%2===0){
                            allStr += `<tr><td class="${item.nameChange?'ty-color-red':''}">${item.name}</td><td class="${item.codeChange?'ty-color-red':''}">${item.code}</td>`
                        }else{
                            allStr += `<td class="${item.nameChange?'ty-color-red':''}">${item.name}</td><td class="${item.codeChange?'ty-color-red':''}">${item.code}</td></tr>`
                        }
                    }
                    if(sortAfter.length % 2 !== 0){
                        allStr += `<td> </td><td> </td></tr>`
                    }
                    $(".record_otherContact").html(allStr);
                    bounce_Fixed3.show($("#contactRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'contactSocial':
            var source = obj.data('source');
            if(source == 'addCustomer') {
                let info = obj.siblings(".hd").html()
                info = JSON.parse(info)
                info['socialList'] = JSON.parse(info.socialList)
                see_otherContactStr(info)
            }else{
                $.ajax({
                    url : "../sales/getContactsSocial.do" ,
                    data : {
                        'contactId': obj.data('id')
                    },
                    success:function(data){
                        var get = data['data'];
                        see_otherContactStr(get)
                    },
                    error: function (msg) {
                        layer.msg("连接错误，请稍后重试！");
                        return false;
                    }
                });
            }

            break;
        case 'visitCard':
            $.ajax({
                url : "../sales/getContactCard.do" ,
                data : {
                    'contactId': obj.data('id')
                },
                success:function(data){
                    var status = data.status;
                    if (status == '1') {
                        if(data.visitCard && data.visitCard != "" && data.visitCard != null){
                            $('#see_contactsCard').html('<img src="' + $.fileUrl + data.visitCard +'" />');
                        }else{
                            $('#see_contactsCard').html('');
                        }
                    }
                    bounce_Fixed.show($("#visitCardDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'interview':
            $.ajax({
                url : "../sales/getRecordInterviewDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    $("#inputInfo").html('录入者：' + ' &nbsp; ' + nowData.createName + ' &nbsp; ' + new Date(nowData.createDate).format('yyyy-MM-dd hh:mm:ss'));
                    if(frontData == null){
                        $("#seeInterviewInfo .detaileTtl").html('原始信息');
                        $("#see_interviewee").html(nowData.interviewer + '&nbsp;&nbsp;' + nowData.post);
                        $("#see_interviewContent").html(nowData.content);
                        $("#see_interviewDate").html(new Date(nowData.interviewDate).format('yyyy年MM月dd日'));
                    }else{
                        $("#seeInterviewInfo .detaileTtl").html(obj.parent().prev().html());
                        $("#see_interviewee").html(compareD(frontData.interviewer,nowData.interviewer) + '&nbsp;&nbsp;' + compareD(frontData.post,nowData.post));
                        $("#see_interviewContent").html(compareD(frontData.content,nowData.content));
                        $("#see_interviewDate").html(compareD(new Date(frontData.interviewDate).format('yyyy年MM月dd日'),new Date(nowData.interviewDate).format('yyyy年MM月dd日')));
                    }
                    bounce_Fixed2.show($("#seeInterviewInfo"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
    }
}
function see_otherContactStr(get){
    var html = '',socialList = get['socialList'];
    $("#see_contactName").html(get.name);
    $("#see_position").html(get.post);
    $("#see_contactTag").html(get.tags);
    $(".see_otherContact").html("");
    $("#contactSeeDetail .see_createName").html(get.createName);
    $("#contactSeeDetail .see_createDate").html(new Date(get.createDate).format('yyyy-MM-dd hh:mm:ss'));
    if(socialList.length > 0){
        let sortList = [];
        for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
        for(var r in socialList){
            let item = socialList[r];
            let _index = Number(item.type);
            sortList[_index].push(item);
        }
        let sortAfter = [];
        for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
        let allStr = '';
        for(var t in sortAfter){
            let item = sortAfter[t];
            if(t%2===0){
                allStr += `<tr><td>${item.name}</td><td>${item.code}</td>`
            }else{
                allStr += `<td>${item.name}</td><td>${item.code}</td></tr>`
            }
        }
        if(sortAfter.length % 2 !== 0){
            allStr += `<td> </td><td> </td></tr>`
        }
        $(".see_otherContact").html(allStr);
    }
    bounce_Fixed3.show($("#contactSeeDetail"));
}
// creator: 李玉婷，2019-09-10 16:43:23，基本信息、发票信息修改记录列表数据获取
function getRecordsList(data,type){
    var getList = data.list;
    if(getList.length > 0) {
        var str = '';
        var eidtNumber = getList.length - 1;
        $(".createRecord .recordTip").html('当前数据为第' + eidtNumber + '次修改后的结果。');
        $(".createRecord .recordEditer").html('修改时间：' + new Date(data.updateDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").show();
        for (let r in getList) {
            if (r == '0') {
                str +=
                    '<tr>' +
                    '   <td>原始信息</td>' +
                    '   <td><span class="ty-color-blue" data-id="' + getList[r].id + '" data-frontid="0" data-type="'+ type +'" onclick="cus_recordDetail($(this))">查看</span></td>' +
                    '   <td>' + getList[r].createName + ' &nbsp; ' + new Date(getList[r].createDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                    '</tr>';
            } else {
                var front = Number(r) - 1;
                str +=
                    '<tr>' +
                    '   <td>第' + r + '次修改后</td>' +
                    '   <td><span class="ty-color-blue" data-id="' + getList[r].id + '" data-frontid="' + getList[front].id + '" data-type="'+ type +'" onclick="cus_recordDetail($(this))">查看</span></td>' +
                    '   <td>' + getList[r].updateName + ' &nbsp; ' + new Date(getList[r].updateDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                    '</tr>';
            }
        }
        $(".changeRecord tbody").html(str);
    }else {
        $(".createRecord .recordTip").html('当前资料未经修改');
        $(".createRecord .recordEditer").html('创建人：'+ data.createName + '&nbsp;&nbsp;' + new Date(data.createDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").hide();
    }
    bounce_Fixed2.show($("#updateRecords"));
}
//creator:lyt date:2018/11/28 比较修改记录修改前后不同
function compareD(front,now){
    if(front == now){
        return '<span>' + handleNull(now) +' </span>'
    }else{
        return '<span class="redFlag">' + handleNull(now) + '</span>'
    }
}

// creator: 李玉婷，2019-09-19 09:37:24，清除自定义标签
function clearLableText(obj) {
    obj.siblings().val("");
}
// creator: 李玉婷，2019-09-19 10:14:23，自定义标签确定
function addNewLable(){
    var val = $("#defLable").val();
    var html =
        '<li>' +
        '<span class="sale_ttl1">' + val + '：</span>' +
        '<span class="gap"><input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" data-org="" require/></span>'+
        '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
        '</li>';
    $(".otherContact").append(html);
    bounce_Fixed2.cancel();
}

// creator: 张旭博，2020-04-02 14:24:21,获取机构列表
function getOrganizationList(customerId) {
    $.ajax({
        url: '../special/getCustomerOrganizationsByCustomerId.do',
        data: { customerId: customerId },
        success: function (res) {
            var data = res.data
            if (data) {
                var str = ''
                for (var i in data) {
                    str +=  '<tr data-id="'+data[i].id+'">' +
                        '    <td>' + (Number(i) + 1) + '</td>' +
                        '    <td>' + data[i].supervisorMobile + '</td>' +
                        '    <td>' + data[i].supervisorName + '</td>' +
                        '    <td>' + data[i].fullName + '</td>' +
                        '    <td>' + data[i].createName + ' ' + formatTime(data[i].createDate, true) + '</td>' +
                        '    <td>' +
                        '        <span type="btn" data-name="orgInfo" class="ty-color-blue">机构信息</span>' +
                        '        <span type="btn" data-name="selectedModule" class="ty-color-blue">已选模块</span>' +
                        '        <span type="btn" data-name="userLoginRecord" class="ty-color-blue">登录记录</span>' +
                        '        <span type="btn" data-name="resouceManage" class="ty-color-blue">空间与流量</span>' +
                        '    </td>' +
                        '</tr>'
                }
                $(".orgList tbody").html(str)
            } else {
                $(".orgList tbody").html("")
            }

        }
    })
}

// creator: hxz 2021-04-08 获取空间与资源使用情况
function getResouceInfo(thisObj) {
    let id = thisObj.parent().parent().data("id")
    $.ajax({
        "url": "../st/getSpaceTrafficInfo.do",
        "data": { "id": id },
        success:function (data) {
            let res = data.data
            if(res){
                bounce.show($("#resourceManage"))
                $(`#resourceManage .orgfullname`).html(res.fullName || ' --')
                $(`#resourceManage .orgname`).html(res.name || '--')
                $(`#resourceManage .zoneTop`).html(res.ratedSpace ?  res.ratedSpace :  ' --')
                $(`#resourceManage .usedZone`).html( res.usedSpace ?  res.usedSpace :  ' --')
                $(`#resourceManage .zoneduraing`).html(`${res.beginDate ? new Date(res.beginDate).format("yyyy年MM月dd日") : ' -- ' }- ${ res.endDate ? new Date(res.endDate).format("yyyy年MM月dd日") : ' --' }`)
                $(`#resourceManage .flowAll`).html( res.ratedTraffic ?  res.ratedTraffic :  ' --')
                $(`#resourceManage .flowUsed`).html( res.usedTraffic ?  res.usedTraffic :  ' --')
            }else{
                layer.msg("未获取有效数据");
            }
        }
    })

}
// creator: 张旭博，2020-04-02 11:21:33,获取模块
function getModule() {
    $.ajax({
        url:"../special/getSelectPopedoms.do" ,
        success:function ( data ) {
            var success = data.success
            var list = data.data
            if (success !== 1) {
                // layer.msg("获取权限列表失败，请刷新重试！")
            } else {
                var str = '<div class="module_list">'
                for (var i in list) {
                    var subModule = list[i].subPopdoms
                    str +=  '<div class="module_row" level="1">'+
                        '   <div class="module_item">' +
                        '       <div class="ty-checkbox">' +
                        '           <input type="checkbox" id="'+list[i].pid + '-' + list[i].mid +'" mid="'+ list[i].mid +'">' +
                        '           <label for="'+list[i].pid + '-' + list[i].mid +'"></label> ' + list[i].name +
                        '       </div>' +
                        '   </div>'

                    str += '<div class="module_list">'
                    for (var j in subModule) {
                        var thirdModule = subModule[j].subPopdoms
                        str +=  '<div class="module_row" level="2">'+
                            '   <div class="module_item">' +
                            '       <div class="ty-checkbox">' +
                            '           <input type="checkbox" id="'+subModule[j].pid + '-' + subModule[j].mid +'" mid="'+ subModule[j].mid +'">' +
                            '           <label for="'+subModule[j].pid + '-' + subModule[j].mid +'"></label> ' + subModule[j].name +
                            '       </div>' +
                            '   </div>'
                        str += '<div class="module_list">'
                        for (var k in thirdModule) {
                            str +=  '<div class="module_row" level="3">'+
                                '   <div class="module_item">' +
                                '       <div class="ty-checkbox">' +
                                '           <input type="checkbox" id="'+thirdModule[k].pid + '-' + thirdModule[k].mid +'" mid="'+ thirdModule[k].mid +'">' +
                                '           <label for="'+thirdModule[k].pid + '-' + thirdModule[k].mid +'"></label> ' + thirdModule[k].name +
                                '       </div>' +
                                '   </div>'+
                                '</div>'
                        }
                        str += '</div>'
                        str += '</div>'
                    }
                    str += '</div>'
                    str += '</div>'
                }
                console.log(str)
                $(".module_avatar .module_body").html(str)
            }
        }
    })
}

// creator: 张旭博，2020-04-03 11:55:23,新增机构
function newOrganization() {
    var data = {}
    $("#selectModule table [name]").each(function () {
        var key = $(this).attr("name")
        data[key] = $(this).val()
    })
    var cusInfo = $(".orgManage").data("cusInfo")
    data.customerId = cusInfo.id
    var orgArr = []
    $(".module_avatar .module_body input:checkbox:checked").each(function () {
        orgArr.push($(this).attr("mid"))
    })
    data.appObject = orgArr.join((","))
    $.ajax({
        url: '../special/addOrgApply.do',
        data: data,
        success: function (res) {
            var data = res.data
            bounce.cancel()
            if (data === 1) {
                bounce_Fixed.cancel()
                layer.msg("已提交申请！")
            } else {
                $("#mtTip .tip").html("操作失败！")
                bounce_Fixed.show($("#mtTip"))
            }
        }
    })
}

// creator: 张旭博，2020-04-07 11:49:02,获取机构详情
function getOrgInfo(orgId) {
    $.ajax({
        url: '../special/getCustomerOrganizationInfo.do',
        data: {  id: orgId },
        success: function (res) {
            var data = res.data
            if (data) {
                for (var key in data) {
                    $("#selectModule [name='" + key + "']").val(data[key])
                    $("#orgInfo .org_" + key).html(data[key])
                }
                // 处理模块
                var moduleStr = data.appObject
                var moduleArr = moduleStr.split(",")
                for (var i in moduleArr) {
                    $(".module_avatar input[mid='"+moduleArr[i]+"']").prop("checked", true)
                    $(".module_avatar input[mid='"+moduleArr[i]+"']").parents(".module_row").children(".module_item").find("input:checkbox").prop("checked", true)
                }
            }
        }
    })
}

/*--------------- 登录记录 -----------------*/
// creator: 李玉婷，2020-08-20 14:43:26，按月、年查询
function getCusLoginRecordList(id, type, beginTime,endTime) {
    $("#userLoginRecord tbody").html('');
    var defType = 0;
    if (type == 3) {
        var beginYear = beginTime.split("-")[0];
        var beginMonth = beginTime.split("-")[1];
        var endYear = endTime.split("-")[0];
        var endMonth = endTime.split("-")[1];
        if(beginYear !== endYear){//跨年
            defType = 3;
            $(".dateType").html("登录年份");
        }else if(beginMonth !== endMonth){//垮月
            defType = 2;
            $(".dateType").html("登录月份");
        }else{
            defType = 1;
            $(".dateType").html("登录日期");
        }
    }
    $.ajax({
        url: '../special/getOrganizationLogins.do',
        data: {
            id: id,
            type: type,
            beginDate: beginTime,
            endDate: endTime
        },
        success: function (res) {
            var data = res.data;
            var userLogList = data.statisticsList;
            var listStr = '';
            if (data) {
                var dateFormat = '';
                var createInfo = data.createName + '&nbsp;' +new Date(Number(data['createDate'])).format('yyyy-MM-dd hh:mm:ss');
                $("#sysOrgName").html(data.name);
                $("#orgCreateName").html(createInfo);
                for(var i=0;i<userLogList.length;i++){
                    if (type == 1 || (type == 3 && defType == 1)) {
                        dateFormat = new Date(userLogList[i].loginDate).format('yyyy-MM-dd');
                    } else if (type == 2 || (type == 3 && defType == 2)) {
                        dateFormat = new Date(userLogList[i].loginDate).format('yyyy-MM');
                    } else {
                        dateFormat = new Date(userLogList[i].loginDate).format('yyyy');
                    }
                    listStr +=  '<tr>' +
                        '<td>'+ dateFormat +'</td>'+
                        '<td>'+handleNull(userLogList[i].sumUser)+'</td>'+
                        '<td>'+handleNull(userLogList[i].sumDuration)+'</td>'+
                        '<td>'+handleNull(userLogList[i].sumLogin)+'</td>'+
                        '<td>'+handleNull(userLogList[i].sumPc)+'</td>'+
                        '<td>'+handleNull(userLogList[i].sumApp)+'</td>'+
                        '</tr>';
                }
                $("#userLoginRecord tbody").html(listStr);
            }
        }
    })
}
// creator: 李玉婷，2020-08-17 15:56:58，登录记录-自定义查询
function sureLoginQuery(){
    //自定义查询设置选中，其他本月等按钮设置不选中
    $("#loginQueryBtn").addClass("ty-btn-blue");
    $(".flagTab .ty-btn-groupCom .ty-btn").removeClass("ty-btn-active-blue");
    var orgId = $(".loginRecord").data("orgId");
    var beginTime = $("#queryBeginTime").val() ;
    var endTime = $("#queryEndTime").val();
    getCusLoginRecordList(orgId, 3, beginTime,endTime);
}
// creator: 李玉婷，2021-07-27 15:49:23，批量导入
function leadingShow() {
    $('.matListUpload').remove();
    $('#custormerLeading .fileFullName').html("尚未选择文件");
    bounce.show($('#custormerLeading'));

}
// creator: 李玉婷，2020-09-03 17:12:49，导入验证
function matImportOk(type) {
    if(type === 'cancel'){
        let fileUid = $('#custormerLeading .fileFullName').data('fileid');
        let op = {"type":'fileId', 'fileId':fileUid}
        fileDelAjax(op);
        bounce.cancel()
    }else{
        if ($(".matListUpload").length <= 0) {
            $("#knowTip .knowWord").html('您需选择一个文件后才能“导入”！');
            bounce_Fixed2.show($("#knowTip"))
        } else {
            loading.open() ;
            $(".matListUpload a").click();
        }
    }
}
// creator: 李玉婷，2021-08-04 17:31:30，
function importCustomer(path) {
    $.ajax({
        "url": "../export/importCustomer.do",
        "data": {'file': '/' +path},
        success: function (data) {
            var status = data;
            if (status == '1' ||status == 1) {
                bounce.cancel()
                var key = $(".main #se0").val();
                getCustomerMes(1 , 20, key);
            } else {
                loading.close() ;
                $('#select_btn_1').val("");
                $('.matListUpload').remove();
                $('#custormerLeading .fileFullName').html("尚未选择文件");
                bounce_Fixed2.show($("#importantTip"));
            }
        }
    });
}
//--------------- 辅助方法 -----------------
function changeState(val){
    if( val == 0 || val == "0"){ return "停用" };
    if( val == 1 || val == "1"){ return "启用" };
    return "";
}
//处理null
function handleNull(str) {
    var result = str == null || str == undefined || str == 'null' || '' ? '--':str;
    return result;
}
//邮编正则
function is_postcode(postcode) {
    if ( postcode == "") {
        return true;
    } else {
        if (! /^[0-9][0-9]{5}$/.test(postcode)) {
            return false;
        }
    }
    return true;
}
// 现在是所有电话，座机和手机
function isTelNum(str){
    if(str == ""){
        return true;
    }else{
        var pattern = /(^((\(\d{2,3}\))|(\d{3}\-))?(\(0\d{2,3}\)|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$)|(^(0|86|17951)?(13[0-9]|15[012356789]|17[678]|18[0-9]|14[57])[0-9]{8}$)/;
        if (pattern.exec(str) !== null) {
            return true;
        }
    }
    return false;
}
// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.parent().siblings(".textMax").text( curLength +'/' + max );
    obj.siblings(".textMax").text( curLength +'/' + max );
}
// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else {
        console.log("未定义的状态")
    }
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = []
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
}
laydate.render({elem: '#edit_firstTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#firstContactTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#interviewDate', format: 'yyyy年MM月dd日'});
laydate.render({elem: '#initialPeriod', type: 'month', format: 'M月',min: new Date().format('yyyy-01-01'),max: new Date().format('yyyy-12-31')});
laydate.render({elem: '#buyMonth', type: 'month', format: 'M月',min: new Date().format('yyyy-01-01'),max: new Date().format('yyyy-12-31')});
laydate.render({elem: '#queryBeginTime'});
laydate.render({elem: '#queryEndTime'});
laydate.render({elem: '#expiresTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#contractTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#update_expiresTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#update_contractTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '.cSignDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cStartDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cEndDate', format: 'yyyy-MM-dd'});