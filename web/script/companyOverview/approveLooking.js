/**
 * Created by 侯杏哲 on 2017/2/10.
 */
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#wonderssLeaveTips"));
bounce_Fixed2.cancel();
$(function(){
    // getMainList();
    // 获取超管和总务信息
    getSuper() ;
    // creator : 侯杏哲 2017-05-15 权限分配 下拉框 赋默认值
    $("#chargeList").children().each(function(){ // tr 循环
        var spanList = $(this).children(".hd").children() ;
        var tr_this = $(this) ;
        var k = 3 ;
        var lastChargeName = "";
        spanList.each(function(){ // span 循环
            lastChargeName = $(this).html() ;
            if(k<=4){
                tr_this.children(":eq("+ k +")").html($(this).html()) ;
                k++ ;
            }

        });
        tr_this.children(":eq(5)").html( lastChargeName ) ;
    }) ;
    $("#chargeList").on("click", 'button', function (){
        var name = $(this).attr("name")
        var code = $(this).parents("tr").data("code")
        var id = $(this).parents("tr").data("id")
        switch (name) {
            // 主列表 - 查看
            case 'see':
                requestSet(code, id, $(this))
                break
            // 主列表 - 修改记录
            case 'changeRecord':
                requestLog(code, id)
                break
        }
    })
}) ;
// creator：hxz 2019/11/27 获取主列表
function getMainList(){
    var oid = sphdSocket.user.oid ;
    $.ajax({
        "url":"../popedom/approvalPowerSettingPage.do",
        "data":{'json':"{'oid':"+ oid +"}"},
        success:function (res) {
            var data = res.data
            var approvalItems = data.approvalItemPC
            var str = ''
            if (approvalItems && approvalItems.length > 0) {
                for (var i in approvalItems) {
                    var item = approvalItems[i]
                    str += '<tr data-code="' + item.code + '" data-id="' + item.id + '">' +
                        '   <td>' + item.name + '</td>' +
                        '   <td><span class="label label-' + (item.status === 1 ? 'blue' : 'gray') + '">' + chargeSpecial(item).stateStr + '</span></td>' +
                        '   <td>' + (item.status === 1 ? item.firstUserName || '' : '') + '</td>' +
                        '   <td>' + (item.status === 1 ? item.secondUserName || '' : '') + '</td>' +
                        '   <td>' + (item.status === 1 ? item.finalUserName || '' : '') + '</td>' +
                        '   <td>' + chargeSpecial(item).btnStr + '</td>' +
                        '</tr>'
                }
                $("#chargeList").html(str);
            }else{
                layer.msg("获取数据失败！")
            }
            // var approvalItemPC = res["data"]["approvalItemPC"], str="";
            // if(approvalItemPC && approvalItemPC.length > 0){
            //     for(var i = 0 ; i < approvalItemPC.length ; i++){
            //         var item = approvalItemPC[i];
            //         var name = item["name"], level = item["level"], status = item["status"];
            //         var stateStr = "", flowStr = "<td></td><td></td><td></td>", btnsStr="";
            //         if(status == 1){
            //             if (name == "来自客户订单的评审") {
            //                 stateStr = "<span class=\"label label-sm label-danger\">需要评审</span>";
            //             } else if (name == "采购来材料的入库检验" || name == "货物入成品库前的检验") {
            //                 stateStr = "<span class=\"label label-sm label-danger\">需要检验</span>";
            //             } else if (name == "商品与产品的关联") {
            //                 stateStr = "<span class=\"label label-sm label-danger\">手动关联</span>";
            //             }else {
            //                 stateStr = "<span class=\"label label-sm label-danger\">"+ UpperLevel(level) +"</span>";
            //                 flowStr = "<td>"+ item['firstUserName'] +"</td><td>"+ (item['secondUserName'] || "") +"</td><td>"+ item['finalUserName'] +"</td>";
            //             }
            //         }else{
            //             if (name == "来自客户订单的评审") {
            //                 stateStr = "<span class=\"label label-sm label-success\">无需评审</span>";
            //             } else if (name == "采购来材料的入库检验" || name == "货物入成品库前的检验") {
            //                 stateStr = "<span class=\"label label-sm label-success\">无需检验</span>";
            //             } else if (name == "商品与产品的关联") {
            //                 stateStr = "<span class=\"label label-sm label-success\">自动关联</span>";
            //             }else {
            //                 stateStr = "<span class=\"label label-sm label-success\">"+ "无需审批" +"</span>";
            //                 flowStr = "<td></td><td></td><td></td>";
            //             }
            //         }
            //         if(name == "加班"){
            //             btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('overTime', "+ item["id"] +" , $(this) )\">查看</span>" +
            //                 "<span class=\"ty-color-blue\" onclick=\"requestLog('overTimeApply', "+ item["id"] +", $(this) )\" >修改记录</span>"+
            //                 "<input type='hidden' class='createDate' value='"+ item['createDate'] + "'>" +
            //                 "<input type='hidden' class='createName' value='"+ item['createName'] + "'>" ;
            //         }else if(name == "付款"){
            //             btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('paymentApproval', "+ item["id"] +" , $(this) )\">查看</span>" +
            //                 "<span class=\"ty-color-blue\" onclick=\"requestLog('paymentApproval', "+ item["id"] +", $(this) )\" >修改记录</span>"+
            //                 "<input type='hidden' class='createDate' value='"+ item['createDate'] + "'>" +
            //                 "<input type='hidden' class='createName' value='"+ item['createName'] + "'>" ;
            //         }else if(name == "请假"){
            //             btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('leave',"+ item["id"] +" , $(this))\">查看</span>"+
            //                 "<span class=\"ty-color-blue\" onclick=\"requestLog('leaveApply', "+ item["id"] +", $(this) )\" >修改记录</span>";
            //         }else if(name == "报销"){
            //             btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('reimburseApply', "+ item["id"] +" , $(this) )\">查看</span>" +
            //                 "<span class=\"ty-color-blue\" onclick=\"requestLog('reimburseApply', "+ item['id'] +", $(this) )\">修改记录</span>";
            //
            //             // btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('finananceSet',"+ item["id"] +" , $(this))\">查看</span>"
            //
            //         }else if(name == "新项目立项"){
            //             // btnsStr = "<span class=\"ty-color-blue\" onclick=\"establishmentSet("+ item["id"] +" , $(this))\">修改</span>"
            //             btnsStr = "";
            //         }else if(name == "新项目开发"){
            //             // btnsStr = "<span class=\"ty-color-blue\" onclick=\"establishmentSet("+ item["id"] +" , $(this))\">修改</span>"
            //             btnsStr = "";
            //         }else if(name == "修改审批设置"){
            //             btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('authritySet',"+ item["id"] +" , $(this))\">查看</span>"
            //
            //         }else if(name == "来自客户订单的评审"){
            //             btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('ordersReview', "+ item["id"] +" , $(this) )\">查看</span>" +
            //                 "<span class=\"ty-color-blue\" onclick=\"requestLog('ordersReview', "+ item['id'] +", $(this) )\">修改记录</span>";
            //         }else if(name == "采购来材料的入库检验"){
            //             btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('materialInCheck', "+ item["id"] +" , $(this) )\">查看</span>" +
            //                 "<span class=\"ty-color-blue\" onclick=\"requestLog('materialInCheck', "+ item['id'] +", $(this) )\">修改记录</span>";
            //         }else if(name == "货物入成品库前的检验"){
            //             btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('productInCheck', "+ item["id"] +" , $(this) )\">查看</span>" +
            //                 "<span class=\"ty-color-blue\" onclick=\"requestLog('productInCheck', "+ item['id'] +", $(this) )\">修改记录</span>";
            //         }else if(name == "商品与产品的关联"){
            //             btnsStr = "<span class=\"ty-color-blue\" onclick=\"requestSet('commodityProduct', "+ item["id"] +" , $(this) )\">查看</span>" +
            //                 "<span class=\"ty-color-blue\" onclick=\"requestLog('commodityProduct', "+ item['id'] +", $(this) )\">修改记录</span>";
            //         }else{
            //             btnsStr = "";
            //         }
            //         str += "<tr>" +
            //             "    <td>"+ name +"</td>" +
            //             "    <td>"+ stateStr +"</td>" + flowStr +
            //             "    <td>" + btnsStr +"</td>" +
            //             "</tr>";
            //
            //     }
            //     $("#chargeList").html(str);
            // }else{
            //     layer.msg("获取数据失败！")
            // }

        }
    })
}
//  creator : 侯杏哲，2019/11/21 修改记录
function requestLog(type, id, _thisObj){
    bounce.show($("#requestLog"));
    $("#log").find("tr:gt(0)").remove();
    $("#requestLog").data("code", type)
    $.ajax({
        url:"../popedom/getRecordList.do",
        data:{ 'json': JSON.stringify({ oid: sphdSocket.user.oid, code: type }) },
        success:function (res) {
            var list = res["data"]["approvalItems"];
            var curInfo = res["data"]["approvalItem"]; // 当前生效的加班审批流程设置
            var num = res["data"]["num"];
            $("#log13").hide();
            var createDate = new Date(curInfo["createDate"]).format('yyyy/MM/dd hh:mm:ss') ;
            var createName = curInfo["createName"];
            var typeName = "",typeCat = "";
            if(type == 'overTimeApply'){
                typeName = "加班";
                typeCat = "流程";
                $("#log1").html("操作");
                $("#logType").html("加班审批的修改记录")
            }else if(type == 'paymentApproval'){
                typeName = "付款";
                typeCat = "人";
                $("#log1").html("付款审批者");
                $("#log13").show();
                $("#logType").html("付款设置的修改记录")
            }else if(type == 'leaveApply'){
                typeName = "请假";
                typeCat = "流程";
                $("#log1").html("操作");
                $("#logType").html("修改记录")
            }else if(type == 'reimburseApply'){
                typeName = "报销";
                typeCat = "流程";
                $("#log1").html("操作");
                $("#logType").html("报销审批的修改记录")
            }else if(type == 'ordersReview' || type == 'materialInCheck' || type == 'productInCheck' || type == 'commodityProduct'){
                typeName = curInfo.name;
                $("#log1").html("设置的结果");
                $("#log2").html("执行的状态");
                $("#logType").html("修改记录")
            } else if (type === 'purchaseApprovalSettings'){
                typeName = "采购";
                typeCat = "流程";
                $("#log1").html("操作");
                $("#log2").html("执行的状态");
                $("#logType").html("修改记录")
            }
            if(num == 0){
                if(list && list.length > 0){
                    if(type == 'paymentApproval'){
                        var r = list[0]["approvalName"] ;
                        $("#summary").html( "当前"+ typeName +"审批"+ typeCat +"为"+ r +",为原始信息。<p style='text-align: right; '>创建人："+ createName + " "+ createDate +"</p>");
                    }else if(type == 'ordersReview'){
                        $("#summary").html("来自客户订单的评审是否需要评审事宜");
                    }else if(type == 'materialInCheck'){
                        $("#summary").html("采购来材料的入库前是否需要检验事宜");
                    }else if(type == 'productInCheck'){
                        $("#summary").html("货物入成品库前是否需要检验事宜");
                    }else if(type == 'commodityProduct'){
                        $("#summary").html("商品与产品的关联方式事宜");
                    }else{
                        $("#summary").html( "当前"+ typeName +"审批"+ typeCat +"为原始信息。<p style='text-align: right; '>创建人："+ createName + " "+ createDate +"</p>");
                    }
                }else{
                    $("#summary").html( typeName + "审批"+ typeCat +"尚未经修改。<span style='float: right; '>创建人："+ createName + " "+ createDate +"</span>");
                }
            } else {
                if (type == 'leaveApply' || type == 'reimburseApply'){
                    $("#summary").html("当前的"+ typeName +"审批"+ typeCat +"为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");
                }else if(type == 'ordersReview'){
                    $("#summary").html("来自客户订单的评审是否需要评审事宜");
                }else if(type == 'materialInCheck'){
                    $("#summary").html("采购来材料的入库前是否需要检验事宜");
                }else if(type == 'productInCheck'){
                    $("#summary").html("货物入成品库前是否需要检验事宜");
                }else if(type == 'commodityProduct'){
                    $("#summary").html("商品与产品的关联方式事宜");
                }else if(type == 'paymentApproval'){
                    if(curInfo.status == 1){
                        $("#summary").html("当前的"+ typeName +"审批"+ typeCat +"为"+  res["data"]["approvalNameCurrent"] + ",为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");
                    }else{
                        $("#summary").html("当前无"+ typeName +"审批"+ typeCat + ",为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");

                    }
                }else if(type === 'purchaseApprovalSettings'){
                    $("#summary").html("当前的"+ typeName +"审批"+ typeCat +"为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");
                }else{
                    $("#summary").html("当前的"+ typeName +"审批"+ typeCat +"为"+  res["data"]["approvalNameCurrent"] + ",为第"+ num +"次修改后的结果 <p style='text-align: right; '>修改人："+ createName + " "+ createDate +"</p>");
                }
            }
            if(list && list.length > 0){
                var len = list.length , str = "";
                for(var i = 0 ; i < len ; i++){
                    var openDateStr = "无";
                    if(list[i]['openDate']){ openDateStr = new Date(list[i]['openDate']).format('yyyy-MM-dd') ;  }
                    str += "<tr>" +
                        "    <td>"+ list[i]["recordState"] +"</td>" +
                        "    <td>"+ list[i]['state'] +"</td>" +
                        "    <td>"+ openDateStr +"</td>" ;
                    if(type == 'overTimeApply'){
                        str +="  <td class='ty-td-control'><span class='ty-color-blue' onclick='logInfo("+ list[i]['collectId'] +")'>查看</span></td>" ;
                    }else if(type == 'paymentApproval'){
                        str +=`
                            <td>${ list[i]['statusResult']  === 0 ? '无需审批' : list[i]['approvalName'] }</td> 
                            <td>${ list[i]['paymentAuditStatus'] === 0 ? '无需复核':list[i]['paymentAuditName'] }</td>`;
                        // str +="  <td>"+ list[i]['approvalName'] +"</td>" ;
                    }else if(type == 'leaveApply'){
                        str +="  <td class='ty-td-control'><span class='ty-color-blue' onclick='leaveUpRecord("+ list[i]['collectId'] +")'>查看</span></td>" ;
                    }else if(type == 'reimburseApply'){
                        str +="  <td class='ty-td-control'><span class='ty-color-blue' onclick='logInfo("+ list[i]['collectId'] +")'>查看</span></td>" ;
                    }else if(type == 'ordersReview'){
                        var con = list[i].statusResult==0 ? "无需评审":"需要评审"
                        str +="  <td>"+con+"</td>" ;
                    }else if(type == 'materialInCheck' || type == 'productInCheck'){
                        var con = list[i].statusResult==0 ? "无需检验":"需要检验"
                        str +="  <td>"+con+"</td>" ;
                    }else if(type == 'commodityProduct'){
                        var con = list[i].statusResult==0 ? '自动关联':'手动关联'
                        str +="  <td>"+con+"</td>" ;
                    }else if(type === 'purchaseApprovalSettings'){
                        str +="  <td class='ty-td-control'><span class='ty-color-blue' onclick='logInfo("+ list[i]['collectId'] +")'>查看</span></td>" ;
                    }
                    str +="  <td>"+ list[i]["createName"] + " " + new Date( list[i]["createDate"] ).format('yyyy-MM-dd hh:mm:ss')  +"</td>" +
                        "</tr>";
                }
                $("#log").append(str).show();
            }else{
                $("#log").hide();
            }
        }
    })
}
//  creator : 侯杏哲，2019/11/21 修改记录-查看
function logInfo(id) {
    var code = $("#requestLog").data("code")
    $.ajax({
        "url":"../popedom/getItemDetail.do",
        "data":{'json':"{'itemId':"+ id +"}"},
        success:function (data) {
            var list = data["data"]["approvalFlows"] ;
            var approvalItem = data["data"]["approvalItem"] ;
            if($("#logType").html() == "加班审批的修改记录"){
                bounce_Fixed.show($("#overTime")) ;
                $("#overTimeEditBtn").hide();
                $("#scanTtl").html("目前本公司加班的审批流程如下：");
                if( approvalItem["status"] == 1){ // 需要审批
                    var str = "" ;
                    if(list && list.length > 0){
                        $("#flowData").val(JSON.stringify(data["data"]));
                        for(var i = 0 ; i < list.length ; i++){
                            var userName = list[i]["userName"];
                            if(list[i]["toUserId"] === 0){ userName = "直接上级"; }
                            var lev = returnUpper(i + 1) + "级审批";
                            str += "<div class='itemLe'><p>不高于"+ list[i]["amountCeiling"] +"小时的加班，需"+ lev +"</p>"+
                                "<p><span>"+ lev + "者</span><span>"+ userName +"</span><span>"+ (list[i]["mobile"]||"") +"</span></p></div>";
                        }

                    } else{
                        str = "无需审批" ;
                    }
                    $("#approvList").html( str ) ;
                } else{ // 不需要审批
                    $("#approvList").html( "不需要审批" ) ;
                }
            }else if($("#logType").html() == "报销审批的修改记录"){
                bounce_Fixed.show($("#reimburse")) ;
                var approvalFlows = list ;
                var str = "", topAmount = 0 ;
                for(var k = 0 ; k < approvalFlows.length ; k++){
                    var apitem = approvalFlows[k] , lev = returnUpper(apitem.level) ;
                    str += "<div class=\"apItem\">"
                    if(apitem['amountCeiling'] == "-1" && k == 0){
                        str += " <p>报销需"+ lev +"级审批</p>" ;
                    }else if(apitem['amountCeiling'] == "-1"){
                        str += " <p>高于"+ topAmount + "元的报销，需"+ lev +"级审批</p>" ;
                    }else{
                        topAmount = apitem['amountCeiling'] ;
                        str += " <p>不高于"+ apitem['amountCeiling'] +"元的报销，需"+ lev +"级审批</p>" ;
                    }
                    str +=    "     <p class=\"txtRight\">" +
                        "         <span>"+ lev +"级审批者</span>" +
                        "         <span>"+ (apitem['userName'] || apitem['toUser']) +"</span>" +
                        "         <span>"+ (apitem['mobile'] || "") +"</span>" +
                        "     </p>" +
                        "     <div class=\"clr\"></div>" +
                        " </div>"
                }
                $("#flowList31").html(str);
                $("#reimburse .applyTip").html(" <div>审批记录：</div>")
                var approvalProcessList = data['data']['approvalProcessList'] , processStr = "<p>申请人："+ approvalItem["createName"] +"  "+ (new Date(approvalItem["createDate"]).format('yyyy-MM-dd hh:mm:ss'))  +"</p>";
                for(var j = 0 ; j < approvalProcessList.length ; j++){
                    var item = approvalProcessList[j]
                    processStr += "<p>审批人："+ item["userName"] +"   "+  (new Date(item["handleTime"]).format('yyyy-MM-dd hh:mm:ss'))  +"</p>";
                }
                $("#reimburse .applyTip").append(processStr);
            } else if (code === 'purchaseApprovalSettings') {
                data = data.data
                bounce_Fixed.show($("#purchaseApprovalSettingsSee"));
                var approvalItem = data.approvalItem
                var approvalFlows = data.approvalFlows

                var level = approvalItem.level
                var status = approvalItem.status
                var openDate = moment(approvalItem.openDate).format("YYYY-MM-DD HH:mm:ss")
                $("#purchaseApprovalSettingsSee").find(".openDate").html(openDate)
                if (status === 1) {
                    $("#purchaseApprovalSettingsSee .btn_changeNoApprove").show()
                    $("#purchaseApprovalSettingsSee .tips").html('采购需 <span class="level">'+NumberToChinese(level)+'</span> 级审批')
                } else {
                    $("#purchaseApprovalSettingsSee .btn_changeNoApprove").hide()
                    $("#purchaseApprovalSettingsSee .tips").html("采购无需审批")
                }
                var approvalFlowsStr = ''
                for (var i in approvalFlows) {
                    approvalFlowsStr += '<div class="item-flex">' +
                        '    <div class="item-fix fix120">'+NumberToChinese(Number(i)+1)+'级审批者</div>' +
                        '    <div class="item-auto text-right">'+approvalFlows[i].userName + ' ' + approvalFlows[i].mobile+'</div>' +
                        '</div>'
                }
                $("#purchaseApprovalSettingsSee").find(".approvalFlows").html(approvalFlowsStr)
            }

        }
    })
}
/* creator：侯杏哲，2018-04-09 ，获取超管和总务的基本信息 */
var superList = [] ; // 存放超管和总务的基本信息， 第一个是超管，第二个是总务
function getSuper(){
    $.ajax({
        "url" : "../approval/getSuperAndGeneral.do" ,
        "type" : "post" ,
        "dataType" : "json" ,
        success:function (res) {
            var data = res["data"] ;
            if(data && data.length>0){
                if(data[0]["userName"] == "总经理"||data[0]["userName"] == "董事长"){
                    superList.push(data[0]) ; superList.push(data[1]) ;
                }else{
                    superList.push(data[1]) ; superList.push(data[0]) ;
                }
            }
        } ,
        error:function () {

        }
    }) ;
}
var chargeLevel = 0 ;
var curEditType = {} ;
// creator：侯杏哲，2018/04/11  请假、加班、审批设置 - 审批详情
// type 当前变更权限的类型："leave" : 请假 ， "overtime" : 加班 , "authritySet": 审批设置
function requestSet( type , itemId , obj  ) {
    $("#beforeTip").html("") ;
    $(".ty-opItems").hide();
    curEditType["type"] = type ;
    curEditType["itemId"] = itemId ;
    curEditType["obj"] = obj ;

    var url = '../popedom/getItemDetail.do'
    var data = {
        json: JSON.stringify({ itemId: itemId })
    }
    if (type === 'itemApply') {
        url = '../approval/getLeaveOutTimeLevelList.do'
        data = { itemId : itemId  }
    }

    $.ajax({
        url: url ,
        data : data,
        success:function( res ){
            var data = res
            if (type !== 'itemApply') {
                data = res.data
            }
            var approvalItem = data.approvalItem;
            var approvalFlowList = data.approvalFlowList ;
            let sets = ``;
            switch (type ){
                // 加班
                case "overTimeApply" :
                    var approvalFlows = data.approvalFlows
                    var supplementary = data.supplementary // 加班/请假补报详情
                    var rule = data.rule // 加班/请假限制（提前量）详情

                    var upperLimit = rule.upperLimit // 加班提前量
                    var state_makeAfterFactStr = chargeOpenState(supplementary.enabled?1:0) // “补报加班”的功能 是否开启
                    $("#overTime .upperLimit").html(upperLimit)
                    $("#overTime .upperLimit").attr('data-id', rule.id)
                    $("#overTime .state_makeAfterFact").html(state_makeAfterFactStr)
                    $("#overTime .state_makeAfterFact").attr('data-id', supplementary.id)
                    $("#overTime .state_makeAfterFact").attr('state', supplementary.enabled)

                    if( approvalItem.status === 1){ // 需要审批
                        var str = "" ;
                        if(approvalFlows && approvalFlows.length > 0){
                            $("#flowData").val(JSON.stringify(data));
                            for(var i = 0 ; i < approvalFlows.length ; i++){
                                var userName = approvalFlows[i]["userName"];
                                if(approvalFlows[i]["toUserId"] === 0){ userName = "直接上级"; }
                                var lev = returnUpper(i + 1) + "级审批";
                                str += "<div class='itemLe'><p>不高于"+ approvalFlows[i]["amountCeiling"] +"小时的加班，需"+ lev +"</p>"+
                                    "<p><span>"+ lev + "者</span><span>"+ userName +"</span><span>"+ (approvalFlows[i]["mobile"]||"") +"</span></p></div>";
                            }
                        } else {
                            str = "无需审批"
                        }
                        $("#approvList").html( str ) ;
                    } else{ // 不需要审批
                        $("#approvList").html( "不需要审批" ) ;
                    }

                    bounce_Fixed.show($("#overTime")) ;
                    $("#overTimeEditBtn").show();

                    break ;
                // 请假
                case "leaveApply" :
                    var approvalFlows = data.approvalFlows
                    var supplementary = data.supplementary // 加班/请假补报详情
                    var rule = data.rule // 加班/请假限制（提前量）详情

                    var upperLimit = rule.upperLimit // 请假提前量
                    var state_makeAfterFactStr = chargeOpenState(supplementary.enabled?1:0)// “补报请假”的功能 是否开启
                    $("#leave .upperLimit").html(upperLimit)
                    $("#leave .upperLimit").attr('data-id', rule.id)
                    $("#leave .state_makeAfterFact").html(state_makeAfterFactStr)
                    $("#leave .state_makeAfterFact").attr('data-id', supplementary.id)
                    $("#leave .state_makeAfterFact").attr('state', supplementary.enabled)

                    if( approvalItem.status === 1){ // 需要审批
                        var str = "" ;
                        if(approvalFlows && approvalFlows.length > 0){
                            str = leaveLevenStr(approvalFlows);
                        } else {
                            str = '无需审批'
                        }
                        $("#leaveApprovList").html( str ) ;
                    } else{ // 不需要审批
                        $("#leaveApprovList").html( "不需要审批" ) ;
                    }
                    bounce_Fixed.show($("#leave")) ;
                    $("#leaveEditBtn").show();
                    break ;
                // 报销
                case "reimburseApply" :
                    bounce.show($("#finananceEdit")) ;
                    $("#closeFin2").show().siblings().hide();
                    $("#editFlows").hide().siblings().show();
                    $("#finananceEdit .bonceHead span").html("查看报销审批的审批设置");

                    $("#editFlows").hide();
                    var approvalFlows = data.approvalFlows // 报销审批流程信息
                    var approvalProcessList = data.approvalProcessList // 修改申请的审批流程
                    var str = "", topAmount = 0 ;
                    for(var k = 0 ; k < approvalFlows.length ; k++){
                        var apitem = approvalFlows[k] , lev = returnUpper(apitem.level) ;
                        str += "<div class=\"apItem\">"
                        if(apitem['amountCeiling'] == "-1" && k == 0){
                            str += " <p>报销需"+ lev +"级审批</p>" ;
                        }else if(apitem['amountCeiling'] == "-1"){
                            str += " <p>高于"+ topAmount + "元的报销，需"+ lev +"级审批</p>" ;
                        }else{
                            topAmount = apitem['amountCeiling'] ;
                            str += " <p>不高于"+ apitem['amountCeiling'] +"元的报销，需"+ lev +"级审批</p>" ;
                        }
                        str +=    "     <p class=\"txtRight\">" +
                            "         <span>"+ lev +"级审批者</span>" +
                            "         <span>"+ (apitem['userName'] || apitem['toUser']) +"</span>" +
                            "         <span>"+ (apitem['mobile'] || "") +"</span>" +
                            "     </p>" +
                            "     <div class=\"clr\"></div>" +
                            " </div>"
                    }
                    $("#flowList3").html(str);
                    var len = approvalFlows.length ;
                    var lastAmountCeiling = approvalFlows[len-1 ]['amountCeiling'] ;
                    if(lastAmountCeiling != "-1"){
                        $("#flowList3").next().html(" 职工无法提交超过"+ topAmount +"元的报销申请。")
                    }
                    break ;
                // 付款
                case "paymentApproval":
                    var status = data.status || 0
                    if (status == 0){
                        layer.msg(data.content)
                        return false
                    }
                    bounce.show($("#paymentSet"));
                    approvalItem = data['approvalItem']; // 付款
                    var approvalItem1 = data['approvalItem1']; // 付款复核
                    var approvalFlows = data['approvalFlows'] && data['approvalFlows'][0];
                    var approvalProcessList = data['approvalProcessList'];
                    $("#paymentTtl").html("付款设置");
                    if(Number(approvalItem.status) === 0){
                        $("#cur1").html("无需审批");
                        $("#userInput").data('uid', 0).val(`无需审批`)
                    }else if(Number(approvalItem.status) === 1){
                        $("#cur1").html(`需经${ approvalFlows.userName } ${approvalFlows.mobile }审批`);
                        $("#cur1").data("userid", approvalFlows.toUserId)
                        $("#userInput").data('uid', approvalFlows.toUserId).val(`${ approvalFlows.userName } - ${approvalFlows.mobile }`)
                    }
                    $("#cur1").data("status", Number(approvalItem.status))
                    $("#cur2").data("status", Number(approvalItem1.status))
                    $("#cur2").html( Number(approvalItem1.status) === 1 ? `需经${ approvalItem1.approveUser } 复核` : "无需复核");
                    break;
                // 来自客户订单的评审
                case "ordersReview":
                    sets = approvalItem.status == 0? '无需评审':'需要评审';
                    $("#sale_common").data("applyType", 1);
                    $("#sale_common").data("currentState", approvalItem.status);
                    $("#sale_common .bonceHead span").html("来自客户订单的评审");
                    $("#msgLog").html("对于客户发来订单中的数量与交期，是否需公司各部门评审");
                    $("#funLog").html("系统默认需要评审。可修改为“无需评审”");
                    $("#currentSettings").html(sets);
                    bounce.show($("#sale_common"));
                    break;
                // 采购来材料的入库检验
                case "materialInCheck":
                    sets = approvalItem.status == 0? '无需检验':'需要检验';
                    $("#currentSettings").html(sets);
                    $("#sale_common").data("applyType", 2);
                    $("#sale_common").data("currentState", approvalItem.status);
                    $("#sale_common .bonceHead span").html("采购来材料的入库检验");
                    $("#msgLog").html("对于采购来的材料，入库前是否需要检验");
                    $("#funLog").html("系统默认需要检验。可修改为“无需检验”");
                    bounce.show($("#sale_common"));
                    break;
                // 货物入成品库前的检验
                case "productInCheck":
                    sets = approvalItem.status == 0? '无需检验':'需要检验';
                    $("#currentSettings").html(sets);
                    $("#sale_common").data("applyType", 3);
                    $("#sale_common").data("currentState", approvalItem.status);
                    $("#sale_common .bonceHead span").html("货物入成品库前的检验");
                    $("#msgLog").html("生产出的货物入成品库前，是否需要检验");
                    $("#funLog").html("系统默认需要检验。可修改为“无需检验”");
                    bounce.show($("#sale_common"));
                    break;
                // 商品与产品的关联
                case "commodityProduct":
                    sets = approvalItem.status == 0? '自动关联':'手动关联';
                    $("#currentSettings").html(sets);
                    $("#sale_common").data("applyType", 4);
                    $("#sale_common").data("currentState", approvalItem.status);
                    $("#sale_common .bonceHead span").html("商品与产品的关联");
                    $("#msgLog").html("系统内，商品与产品关联关系的建立，需由手动进行，还是由系统自动关联");
                    $("#funLog").html("系统默认为需手动关联。可修改为“自动关联”");
                    bounce.show($("#sale_common"));
                    break;
                case "itemApply":
                    var level = approvalItem.level
                    $("#itemApply").find("[name='level']").val(level).change()
                    if (level === 1) {
                        $("#itemApply").find("[name='lastApprover']").val(approvalFlowList[0].toUserId).change()
                    }
                    bounce.show($("#itemApply")) ;
                    break
                // 采购审批设置
                case "purchaseApprovalSettings":
                    bounce.show($("#purchaseApprovalSettings"));
                    var approvalItem = data.approvalItem
                    var approvalFlows = data.approvalFlows

                    var level = approvalItem.level
                    var status = approvalItem.status
                    var openDate = moment(approvalItem.openDate).format("YYYY-MM-DD HH:mm:ss")
                    $("#purchaseApprovalSettings").find(".openDate").html(openDate)
                    if (status === 1) {
                        $("#purchaseApprovalSettings .btn_changeNoApprove").show()
                        $("#purchaseApprovalSettings .tips").html('目前，本公司采购需 <span class="level">'+NumberToChinese(level)+'</span> 级审批')
                    } else {
                        $("#purchaseApprovalSettings .btn_changeNoApprove").hide()
                        $("#purchaseApprovalSettings .tips").html("目前，本公司采购无需审批")
                    }
                    var approvalFlowsStr = ''
                    for (var i in approvalFlows) {
                        approvalFlowsStr += '<div class="item-flex">' +
                            '    <div class="item-fix fix120">'+NumberToChinese(Number(i)+1)+'级审批者</div>' +
                            '    <div class="item-auto text-right">'+approvalFlows[i].userName + ' ' + approvalFlows[i].mobile+'</div>' +
                            '</div>'
                    }
                    $("#purchaseApprovalSettings").find(".approvalFlows").html(approvalFlowsStr)

                    break;
                default: bounce.show( $("#Tip") ) ; $("#tipMs").html("没有识别查询项！") ;
                    break;
            }
        }
    }) ;
}

//  creator : 侯杏哲，2018/04/11   新项目立项/开发 - 查看审批流程
function project( ID , type  ) {
    $.ajax({
        url:"../approval/getCurrentProject.do",
        data:{"project": type} ,
        type:"post",
        dataType:"json",
        success:function (data) {
            var status = data["status"] ;
            if(status == 1){
                bounce.show( $("#project") ) ;
                $("#proLevel").html() ;
            } else{
                $("#tipMs").html("获取详情失败！") ;  bounce.show($("#Tip"));
            }
        },
        error:function () {
            $("#tipMs").html("连接失败！") ;  bounce.show($("#Tip"));
        }
    });
}

// creator : 侯杏哲，2017-04-24 08:43:22，获得可以审批的人员
function getPersonStr(data , num) { // num 用于区分是方式2 ， 还是方式3
    var status = data["status"] ;
    var userList = data["userList"] ;
    var str = "<option value='0'>直接上级</option>" ;
    if(num == 3){  str = "" ;  }
    if( Number(status) == 1 ){ // 除了直属上级，还有别人
        if(userList && userList.length > 0 ){
            for( var i=0 ; i < userList.length ; i++ ){
                str += "<option value='"+ userList[i]["userID"] +"'>"+ userList[i]["userName"] +"</option>" ;
            }
        }
    }
    return str ;
}
// creator ： 侯杏哲 2017-05-11  常规权限 - 移除角色
var editObj = {} ; // 常规权限 - 移除角色的对象
function deleteRole( roleID , roleName , obj ){
    var usertype = chargeRole("超管");
    if (usertype) {
        $("#Tip #tipMs").html("您没有此权限！");
        bounce.show($("#Tip"));
        return false;
    }
    if (0 == roleID) {
        $("#tipMs").html("您尚未选择角色类型，无法进行删除操作！");
        bounce.show($("#Tip"));
    }
    else {
        editObj["obj"] = obj;
        editObj["roleID"] = roleID;
        editObj["roleName"] = roleName;
        $("#delTip").html("确定移除角色：" + roleName);
        bounce.show($("#delRole"));
    }

}
// creator ： 侯杏哲 2017-05-11 常规权限 - 确定移除角色
function delRoleSubmit(){
    $.ajax({
        url:"../sys/deleteRole.do" ,
        data: { "roleID" :  editObj["roleID"]  } ,
        type:"post" ,
        success:function( data ){
            if(parseInt(data) === 1){
                bounce.cancel() ;
                $("#tipMs").html(" 删除成功 ！") ;  bounce.show( $("#Tip") ) ;
                editObj["obj"].parent().parent().remove() ;
            }else if(parseInt(data) === 0){
                $("#tipMs").html(" 删除失败 ！") ;  bounce.show( $("#Tip") ) ;
            }
        } ,
        error:function(){
            $("#tipMs").html(" 网络连接错误 ！") ;  bounce.show( $("#Tip") ) ;
        }
    });
}
// creator ： 侯杏哲 2017-05-11 常规权限 - 新增角色
function addRoleBtn(){
    var usertype = chargeRole("超管");
    if (usertype) {
        $("#Tip #tipMs").html("您没有此权限！")
        bounce.show($("#Tip"));
    }else {
        bounce.show($("#editRole"));
        $("#roleName").val("");
    }
}
// creator ： 侯杏哲 2017-05-11 常规权限 - 确定新增角色
function editRoleSubmit(){
    var roleName = $("#roleName").val() ;
    if( $.trim(roleName) == "" ){
        $("#editTip").html("角色名称不能够为空！") ; return false ;
    }
    $.ajax({
        url : "../sys/addRole.do"  ,
        data : { "roleName": roleName } ,
        type:"post" ,
        success:function(data){
            location.href="" ;
        } ,
        error:function(data){
            location.href="" ;
        }
    }) ;
}
// creator ： 侯杏哲 2017-05-11 权限分配 - 变更角色
var changeRoleObj = null ;
function changeRole( selectOption ){
    if(hasAuthority(0) === true){
        $("#Tip #tipMs").html("您没有此权限！");
        bounce.show($("#Tip"));
    }else{
        var valStr = selectOption.val() ;
        var valObj =  valStr.split(",") ;
        var id = valObj[0] ;
        var roleId = valObj[1] ;
        $("#confirmType").html("changeRole");
        $("#confirMs").html("确定变更该用户的角色？");
        bounce.show( $("#confirm") ) ;
        changeRoleObj = { "id":id , "roleId":roleId  } ;
    }
}
// creator ： 侯杏哲 2017-05-11 权限分配 - 确定变更角色
function changeRoleOk(){
    $.ajax({
        url:"../sys/changeRole.do",
        data:{ "roleID":changeRoleObj["roleId"] , "id":changeRoleObj["id"]    } ,
        success:function(){
            $("#tipMs").html("修改成功") ;
            bounce.show( $("#Tip") ) ;
        },
        error:function(){
            $("#tipMs").html("修改失败！") ;
            bounce.show( $("#Tip") ) ;
        }
    }).always(function(){
        location.href = "../sys/toApproveIndex.do" ;
    });
}
// creator ：侯杏哲 2017-05-11  confirm 框的取消
function confirCancel(){
    var confirmType = $("#confirmType").html() ;
    switch( confirmType){
        case "changeRole" :  // 权限分配 - 确定变更角色
            location.href = "../sys/toApproveIndex.do" ;  break ;
        default:
    }
}
// creator ：侯杏哲 2017-05-11  confirm 框的确定
function confirOK(){
    var confirmType = $("#confirmType").html() ;
    switch( confirmType){
        case "changeRole" :  // 权限分配 - 确定变更角色
            changeRoleOk() ; break ;
        default:
    }
}
// creator : 侯杏哲 2017-05-15  我的请求 - 请假和加班 切换需不需要审批
function upChargeLevel(obj){
    var val = obj.val() ;
    if(val == 1){
        $("#myApplyCon").show();
        var str = "<div class='trList'>" +
            "<div class='trItem2' level='1'>" +
            "<span class='ttl2'>第一级审批</span>" +
            "<span class='con2 add-person' onclick='chooseApproveP($(this));' >选择审批人</span>" +
            "<span class='deleteItem' onclick='deleteItem($(this))'>—</span>" +
            "<div class='hd'></div>" +
            "</div>" +
            "</div>" ;
        $("#approveList").html( str ) ;
        $(".deleteItem").hide() ;
        chargeLevel = 1 ;
    }else{
        $("#myApplyCon").hide();
        $("#approveList").html("") ;
    }
}
// creator： 张旭博  2017-05-05  我的请求 - 选择审批人
// updator : 侯杏哲 2017-05-13   添加交互处理 获取机构一级机构下的部门和人员
var curSelectObj = null ; // 我的请求 - 当前正在选择审批人的对象
function chooseApproveP( obj ) {
    curSelectObj = obj ;
    $("#approveTree").html( "" ) ;
    $("#beforeTip").html( "" ) ;
    bounce_Fixed.show($("#chooseApproveP"));
    $.ajax({
        url: "../lo/getOrgAndUser.do" ,
        type:"post" ,
        dataType:"json" ,
        success:function( data ){
            var org = data["org"] ;  //  机构信息
            var orgList = data["orgList"] ;  // 机构下的部门信息
            var users = data["users"] ;   //  机构下的用户信息
            var str = "" ;
            if(org){
                str += "<li> <div class='approvePItem' onclick='showOrg( $(this) )'><i class='fa fa-angle-right'></i>"+ org["name"] +"</div>" +
                    "<div class='hd'>"+ JSON.stringify(org) +"</div>" ;
                if( orgList && orgList.length > 0  ){
                    str += "<ul class='level level2 hd'>";
                    /* for(var j = 0 ; j < users.length ; j++ ){
                         str += "<li class='approvePItem'>" +
                             /!* "<div class='hd'>"+ JSON.stringify(users[j]) +"</div>" +   *!/
                             "<input type='radio' name='approveP' value='"+ users[j]["userID"] +"' />"+ users[j]["userName"] +"</li>"  ;
                     }*/
                    for(var i = 0 ; i < orgList.length ; i++ ){
                        str += "<li>" +
                            "<div class='approvePItem' onclick='showPerson($(this) , "+ orgList[i]["id"] +")'><i class='fa fa-angle-right'></i>"+ orgList[i]["name"] +"</div>" +
                            /*"<div class='hd'>"+ JSON.stringify(orgList[i]) +"</div>" +*/
                            "</li>" ;
                    }
                    str += "</li>" ;
                }
            }
            $("#approveTree").html( str ) ;
        } ,
        error:function(){
            $("#Tip #tipMs").html("连接失败！");
            bounce.show($("#Tip"));
        }
    }) ;
}
// creator: 侯杏哲 2017-05-16 请求处理 - 选择审批人 - 点击部门名称展示部门下的人员
function showOrg( obj ){
    obj.children().prop("checked",true);
    obj.siblings("ul.level").toggleClass("hd");
    if( obj.find("i").hasClass("fa-angle-right") ){
        obj.find("i").removeClass("fa-angle-right") ;
        obj.find("i").addClass("fa-angle-down") ;
    }else{
        obj.find("i").removeClass("fa-angle-down") ;
        obj.find("i").addClass("fa-angle-right") ;
    }
}
// creator: 侯杏哲 2017-05-15 请求处理 - 选择审批人 - 点击部门名称 获取并展示 部门下的人员
function showPerson( obj , id){
    var departmentID = id ;
    $.ajax({
        url:"../lo/getOrgByPid.do" ,
        data:{ "department": departmentID  } ,
        type:"post" ,
        dataType:"json" ,
        success:function( data ){
            var orgList = data["orgList"] ;
            var users = data["users"] ;
            var str = "<ul class='level level3'>" ;
            for(var i = 0 ; i < users.length ; i++ ){
                str += "<li class='approvePItem' ><input type='radio' value='"+ users[i]["userID"] +"' name='approveP'/>"+ users[i]["userName"] +"</li>" ;
            }
            for(var j = 0 ; j < orgList.length ; j++ ){
                str += "<li>" +
                    "<div class='approvePItem' onclick='showPerson($(this) , "+ orgList[j]["id"] + ")'><i class='fa fa-angle-right'></i>"+ orgList[j]["name"] +"</div>" +
                    "</li>" ;
            }
            str += "</ul>" ;
            obj.after(str) ;
            obj.attr("onclick","showOrg( $(this) )")
        } ,
        error:function(){  }
    });
    showOrg( obj ) ;
}
// creator : 侯杏哲 2017-05-15 我的请求 - 更改状态 - 删除审批级别7
function deleteItem(obj){
    --chargeLevel ;
    var tr = obj.parent().parent().remove() ;
    var trArr = $("#approveList").children() ;
    trArr.each(function(){
        var _this = $(this) ;
        _this.children(":eq(0)").children(".ttl2").html("第"+ NumberToChinese(  _this.index()+1 ) +"级审批") ;
        if(_this.index() == (trArr.length -1) ){
            _this.children(":eq(1)").remove() ;
        }else{
            _this.children(":eq(1)").children(".ttl2").html(  NumberToChinese( _this.index()+1 ) +"级审批时长") ;
        }
    }) ;
    /*    for(var i = 0 ; i < trArr.length ; i++ ){
            var _this = trArr[i] ;
            _this.children(":eq(0)").children(".ttl2").html("第"+ NumberToChinese( i+1 ) +"级审批") ;
            if(i == (trArr.length -1) ){
                _this.children(":eq(1)").remove() ;
            }else{
                _this.children(":eq(1)").children(".ttl2").html(  NumberToChinese( i+1 ) +"级审批时长") ;
            }
        }*/

}
//  creator : 张旭博  2017-05-15  我的请求工具方法 - 阿拉伯数字转中文数字
var chnNumChar = ["零","一","二","三","四","五","六","七","八","九"];  // 单个数字转换用数组实现
var chnUnitSection = ["","万","亿","万亿","亿亿"];                    // 节权位同样用数组实现
var chnUnitChar = ["","十","百","千"];                               // 节内权位同样用数组实现
// creator : 张旭博  2017-05-15  我的请求工具方法 - 节内转换算法：
function SectionToChinese(section){
    var strIns = '', chnStr = '';
    var unitPos = 0;
    var zero = true;
    while(section > 0){
        var v = section % 10;
        if(v === 0){
            if(!zero){
                zero = true;
                chnStr = chnNumChar[v] + chnStr;
            }
        }else{
            zero = false;
            strIns = chnNumChar[v];
            strIns += chnUnitChar[unitPos];
            chnStr = strIns + chnStr;
        }
        unitPos++;
        section = Math.floor(section / 10);
    }
    return chnStr;
}
//   creator : 张旭博  2017-05-15  我的请求工具方法 -   转换算法主函数
function NumberToChinese(num){
    var unitPos = 0;
    var strIns = '', chnStr = '';
    var needZero = false;
    if(num === 0){
        return chnNumChar[0];
    }
    while(num > 0){
        var section = num % 10000;
        if(needZero){
            chnStr = chnNumChar[0] + chnStr;
        }
        strIns = SectionToChinese(section);
        strIns += (section !== 0) ? chnUnitSection[unitPos] : chnUnitSection[0];
        chnStr = strIns + chnStr;
        needZero = (section < 1000) && (section > 0);
        num = Math.floor(num / 10000);
        unitPos++;
    }

    return chnStr;
}
// creator : 侯杏哲 2017-05-16 我的请求，变更审批级别 - 确定选择审批人
function selectOK(){
    var personObj = $("input[name='approveP']:checked") ;
    var personID = personObj.val() ;
    var personName = personObj.parent().text() ;
    var isOk = 1 ; // 标记有没有相同
    $("#approveList").children().each(function(){
        var userID = $(this).children(":eq(0)").children(".hd").html() ;
        if( personID == userID ){  isOk = 0 ; return false ;    }
    }) ;
    if(isOk == 0 ){  $("#tp2").html("请不要选择重复的审批人") ;   return false ;  }
    curSelectObj.html( personName ) ;
    curSelectObj.siblings(".hd").html( personID ) ;
    bounce_Fixed.cancel() ;
}

// creator: 李玉婷，2020-04-27 15:14:33，请假查看--审批流程输出
function leaveLevenStr(list) {
    var str = '',enable =0;
    for(var i = 0 ; i < list.length ; i++){
        var userName = list[i]["userName"], amountCeiling = list[i]["amountCeiling"];
        if(list[i]["toUserId"] === 0){ userName = "直接上级"; }
        var lev = returnUpper(i + 1) + "级审批";
        if (amountCeiling > 0 && amountCeiling <24){
            str += "<div class='itemLe'><p>当日不高于"+ amountCeiling +"小时的请假，需"+ lev +"</p>";
        }else if(amountCeiling >= 24 && amountCeiling < 3600){
            amountCeiling = amountCeiling/24;
            str += "<div class='itemLe'><p>不高于"+ amountCeiling +"天的请假，需"+ lev +"</p>";
        }else{
            enable++;
            if (i==0 && amountCeiling == -1){
                str += "<div class='itemLe'><p>请假需"+ lev +"</p>";
            }else{
                amountCeiling = list[i-1]["amountCeiling"];
                if (amountCeiling < 24){
                    str += "<div class='itemLe'><p>高于"+ amountCeiling +"小时的请假，需"+ lev +"</p>";
                } else{
                    amountCeiling = amountCeiling/24;
                    str += "<div class='itemLe'><p>高于"+ amountCeiling +"天的请假，需"+ lev +"</p>";
                }
            }
        }
        str += "<p><span>"+ lev + "者</span><span>"+ userName +"</span><span>"+ (list[i]["mobile"]||"") +"</span></p></div>";
    }
    if (enable > 0){
        $(".leaveSeeTip").hide();
    }else{
        var hurTemp = list[list.length-1]["amountCeiling"];
        if (hurTemp < 24){
            $(".maxHur").html(hurTemp +"小时");
        } else {
            hurTemp = Number(hurTemp)/24;
            $(".maxHur").html(hurTemp +"天");
        }
        $(".leaveSeeTip").show();
    }
    return str;
}
// creator: 李玉婷，2020-04-23 15:57:34，修改记录查看
function leaveUpRecord(leaveId){
    $("#approvRecord").html("") ;
    $("#leaveSetList").html("") ;
    $.ajax({
        "url":"../popedom/getItemDetail.do",
        "data":{'json':"{'itemId':"+ leaveId +"}"},
        success:function (data) {
            bounce_Fixed.show($("#leaveRecordSee")) ;
            var list = data["data"]["approvalFlows"] ;
            var approvalItem = data["data"]["approvalItem"] ;
            var process = data["data"]["approvalProcessList"] ;
            if (process && process.length > 0){
                var record =
                    '<div>审批记录：</div>' +
                    '<div>申请人: '+ approvalItem["createName"] + ' '+ new Date(approvalItem["createDate"]).format('yyyy-MM-dd hh:mm:ss') +'</div>';
                for(var d=0;d<process.length;d++){
                    record +=
                        '<div>审批人: '+ process[d]["userName"] + ' '+new Date(process[d]["handleTime"]).format('yyyy-MM-dd hh:mm:ss') +'</div>';
                }
                $("#approvRecord").html( record ) ;
            }
            if( approvalItem["status"] == 1){ // 需要审批
                var str = "" ;
                if(list && list.length > 0){
                    str = leaveLevenStr(list);
                } else{
                    str = "无需审批" ;
                }
                $("#leaveSetList").html( str ) ;
            } else{ // 不需要审批
                $("#leaveSetList").html( "不需要审批" ) ;
            }
        }
    })
}

// creator: 张旭博，2022/4/6 8:36，wonderss的加班功能 - 按钮
function wonderssOverTimeTips() {
    bounce_Fixed2.show($("#wonderssOverTimeTips"))
}

// creator: 张旭博，2022/4/6 8:36，wonderss的请假功能 - 按钮
function wonderssLeaveTips() {
    bounce_Fixed2.show($("#wonderssLeaveTips"))
}

function chargeOpenState(state) {
    return state === 1?'开启':'关闭'
}

// creator: 张旭博，2022-10-12 10:41:43， 处理主列表特殊数据展示
function chargeSpecial(item) {
    var code = item.code
    var status = item.status
    var level = item.level
    var stateStr = ''
    var btnStr = ''
    var btnState = 'see'
    switch (code) {
        case 'overTimeApply':
            // 加班
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break
        case 'leaveApply':
            // 请假
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break
        case 'reimburseApply':
            // 报销
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break
        case 'paymentApproval':
            // 付款
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break
        case 'workAttendanceApply':
            // 修改考勤
            stateStr = status === 1?UpperLevel(level): '无需审批'
            btnState = 'none'
            break
        case 'archivesApply':
            // 修改职工档案
            stateStr = status === 1?UpperLevel(level): '无需审批'
            btnState = 'none'
            break
        case 'postApply':
            // 修改岗位设置
            stateStr = status === 1?UpperLevel(level): '无需审批'
            btnState = 'none'
            break
        case 'ordersReview':
            // 来自客户订单的评审
            stateStr = status === 1?'需要评审':'无需评审'
            break
        case 'materialInCheck':
            // 采购来材料的入库检验
            stateStr = status === 1?'需要检验':'无需检验'
            break
        case 'productInCheck':
            // 货物入成品库前的检验
            stateStr = status === 1?'需要检验':'无需检验'
            break
        case 'commodityProduct':
            // 商品与产品的关联
            stateStr = status === 1?'手动关联':'自动关联'
            break
        case 'itemApply':
            // 修改审批设置
            stateStr = status === 1?UpperLevel(level): '无需审批'
            btnState = 'seeOnly'
            break
        case 'purchaseApprovalSettings':
            // 修改审批设置
            stateStr = status === 1?UpperLevel(level): '无需审批'
            break

    }
    if (btnState === 'see') {
        btnStr =    '<button class="link-blue" name="see">查看</button>' +
            '<button class="link-blue" name="changeRecord">修改记录</button>'
    } else if (btnState === 'seeOnly') {
        btnStr =    '<button class="link-blue" name="see">查看</button>'
    }
    return {
        stateStr: stateStr,
        btnStr: btnStr
    }

}

// creater  侯杏哲  2018/4/9  工具方法， 传入数字， 返回汉字
function returnUpper( num){
    switch (num){
        case 1:
        case "1": return "一" ; break ;
        case 2:
        case "2": return "二" ; break ;
        case 3:
        case "3": return "三" ; break ;
        case 4:
        case "4": return "四" ; break ;
        case 5:
        case "5": return "五" ; break ;
        case 6:
        case "6": return "六" ; break ;
        case 7:
        case "7": return "七" ; break ;
        case 8:
        case "8": return "八" ; break ;
        case 9:
        case "9": return "九" ; break ;
        default : break ;
    }
}

function UpperLevel(level) {
    var sta = "";
    switch (Number(level)){
        case 1:
            sta = "需一级审批";break;
        case 2:
            sta = "需二级审批";break;
        case 3:
            sta = "需三级审批";break;
        case 4:
            sta = "需四级审批";break;
        case 5:
            sta = "需五级审批";break;
        case 6:
            sta = "需六级审批";break;
        case 7:
            sta = "需七级审批";break;
        case 8:
            sta = "需八级审批";break;
        default: sta = "无需审批";
    }
    return sta ;
}

//  creator: 侯杏哲 2018-04-08  审批设置修改 - 变更审批级别
function changeAuth() {
    let level = Number($("#itemApply [name='level']").val()) ;
    var str ='', str2= '' ;
    if(level === 1){
        $("#itemApply [name='firstApprover']").parents(".item-flex").hide()
        str =   '<option value="' + superList[0].userID + '">' + superList[0].userName + '--' + superList[0].mobile + '</option>' +
            '<option value="' + superList[1].userID + '">' + superList[1].userName + '--' + superList[1].mobile + '</option>'
        $("#itemApply [name='lastApprover']").html(str)
    }else if(level === 2){
        $("#itemApply [name='firstApprover']").parents(".item-flex").show()
        str =   '<option value="' + superList[1].userID + '">' + superList[1].userName + '--' + superList[1].mobile + '</option>'
        str2 =  '<option value="' + superList[0].userID + '">' + superList[0].userName + '--' + superList[0].mobile + '</option>'
        $("#itemApply [name='firstApprover']").html(str)
        $("#itemApply [name='lastApprover']").html(str2)
    }
}



