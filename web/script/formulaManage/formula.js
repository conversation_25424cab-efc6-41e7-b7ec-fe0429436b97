var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#functionSee"));
bounce_Fixed2.cancel();
bounce_Fixed3.show($("#giveTip"));
bounce_Fixed3.cancel();
var GSList = [] ; // 待选择的产品列表
var selectGS = [],notSelectGS = [] ;
var pageSize = 20 ; // 分页的每页条数
var matchObj = null ; // 原料代号、原料名称匹配
$(function(){
    $("input[name='mainConShow']").val("1");
    $(".btnDo").click(function(){
        var name = $(this).data('name');
        switch (name){
            case "goSelect": // 去处理
                goMainCon(2);
                getPdMtBaseList(1);
                break;
            case "goStopMt": // 去查看已停用材料
                goMainCon(5);
                getStopForulaList(1);
                break;
            case "addMtBtn": // 新增配方
                $("#selectFS").remove();
                $("#formulaSn").removeAttr("onclick");
                $("#mtEntry").data("operat", 'add');
                $("#mtEntry .entry").removeAttr("disabled");
                $("#entryMainMatBtn").prop("disabled",false);
                $("#entryFormulaMatBtn").prop("disabled",false);
                bounce_Fixed.show($("#mtEntry"));
                $("#mtEntry input").val("");
                $("#mainMaterial tbody").html("");
                $("#subMaterial tbody").html("");
                $("#mtEntry .formulaTtl").html("新增配方");
                $("#mtEntry input[name='id']").val("null");
                $("#mtEntry input[name='product']").val("null");
                setInter('newFormula');
                break;
            case "searchBtn": // 搜索材料
                var key = $("#searchKeyBase").val();
                getForulaList(key, 1);
                break;
            case "chooseByMt": // 去试试
                goMainCon(3);
                getFormulaList(1);
                break;
            case "selectMtOkBtn": // 挑选完毕，确定
                var num = $(".mainCon4 tbody:eq(1) tr").length;
                if (num > 1) {
                    num = num - 1;
                    $("#selectMtNum").html(num);
                    bounce.show($("#selectTip"));
                } else {
                    var str =  "请至少选择一项！" ;
                    $("#iknow .msg").html(str);
                    bounce.show( $("#iknow"))
                }
                break;
            case "fmUesdGs": // 曾使用过该配方的产品及零件
                goMainCon(8)
                var str = $(".mainCon6 tbody:eq(0)").children("tr:eq(1)").html();
                var id = $(".mainCon6 tbody:eq(0)").data('mfID');
                $(".mainCon8 tbody:eq(0)").children("tr:eq(1)").html(str);
                $(".mainCon8 tbody:eq(0)").data('mfID', id);
                $("#goback2").data("num", "6");
                getFmBeforeBindingPdList(1)
                break;
            case "functionSee":
                bounce_Fixed2.show($('#functionSee'));
                break;
            case "entryFormulaMat":
                var type = $(this).data("type");
                matchObj = null;
                partEntryBtn();
                $("#currentName").html($("#formulaName").val());
                $("#entryFormulaMat td").removeAttr("disabled");
                $("#entryFormulaMat input").val("").prop("disabled", false);
                $("#entryFormulaMat textarea").val("").prop("disabled", false);
                $("#entryFormulaMat select").val("").removeAttr("disabled");
                $("#entryFormulaMat .textMax").html("0/100");
                $("#entryFormulaMat input[name='isCurrent']").val("0");
                $('#entryFormulaMat').data("type", type);
                $("#addUnitBtn").addClass("addUnitActive").attr("onclick","addUnit()");
                bounce_Fixed2.show($('#entryFormulaMat'));
                getUnitList($("#unitSelect"), 5, "")
                setInter('matEntery');
                break;
        }
    });
    $(".isCurrentStock ").on('click', ".fa", function () {
        $(this).attr("class", "fa fa-circle").parent().siblings().children().attr("class","fa fa-circle-o");
        var num = $(this).data("num");
        $("input[name='isCurrent']").val(num);
    });
    $("tbody").on("click" , "span.btn" , function () {
        var type = $(this).data("type") ;
        var thisObj = $(this);
        switch (type){
            case "fmScan": // 查看
                bounce.show($("#mtScan"));
                var info = JSON.parse($(this).siblings(".hd").html());
                var num = $("input[name='mainConShow']").val();
                if(num == 1 || num == ""){
                    $("#editMtBtn").show();
                    $("#stopIntro").html("").hide();
                }else if (num == 5) {
                    $("#editMtBtn").hide();
                    $("#stopIntro").show();
                } else {
                    $("#editMtBtn").hide();
                    $("#stopIntro").hide();
                }
                getScanMatData(info.id);
                break;
            case "fmStop": // 暂停使用
                bounce.show($("#stopTip"));
                var info = JSON.parse($(this).siblings(".hd").html());
                $("#stopTip .msg").html("<p>操作成功后，该配方在系统中将无法被选择。</p><p>确定停用该配方？</p>");
                $("#stopTip").data("mfID", info.id);
                $("#stopTip").data("state", 0);
                break;
            case "fmStart": // 恢复使用
                var info = JSON.parse($(this).siblings(".hd").html());
                bounce.show($("#stopTip"));
                $("#stopTip .msg").html("<p>操作成功后，该配方在系统中又可重新被选择。</p><p>确定恢复使用该配方？</p>");
                $("#stopTip").data("mfID", info.id);
                $("#stopTip").data("state", 1);
                break;
            case "fmDel":  // 删除
                var info = JSON.parse($(this).siblings(".hd").html());
                bounce.show($("#delTip"));
                $("#delTip").data("mfID", info.id);
                $("#delTip").data("obj", thisObj);
                break;
            case "gsScan":  // 产品查看
                bounce.show($("#scanGS"));
                var info = JSON.parse($(this).siblings(".hd").html());
                for(var key in info){
                    if(key == "phrase"){
                        $("#scanGS ." + key).html(charge('phrase', info[key]));
                    }else{
                        $("#scanGS ." + key).html(info[key]);
                    }
                }
                $("#scanGS .create").html(info['createName'] + (new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss"))) ;
                break;
            case "gsSelect":  // 产品去处理
                var info = JSON.parse($(this).siblings(".hd").html());
                $("#selectFS").remove();
                $("#mtEntry input").val("");
                $("#mainMaterial tbody").html("");
                $("#subMaterial tbody").html("");
                $("#mtEntry input[name='id']").val("null");
                $("#mtEntry input[name='product']").val(info.id);
                $("#mtEntry .formulaTtl").html("选择/录入配方");
                $("#mtEntry").data("operat", 'relate');
                var selectStr = '<div id="selectFS"></div>';
                $("#mtEntry #formulaSn").data("old", "").after(selectStr);
                selectFSOption();
                $("#mtEntry .entry").removeAttr("disabled");
                $("#entryMainMatBtn").prop("disabled",false);
                $("#entryFormulaMatBtn").prop("disabled",false);
                bounce_Fixed.show($("#mtEntry"));
                setInter('newFormula');
                break;
            case "changeFm":  // 更换配方
                var info = JSON.parse($(this).siblings(".hd").html());
                $("#selectFS").remove();
                $("#mtEntry input").val("");
                $("#mainMaterial tbody").html("");
                $("#subMaterial tbody").html("");
                $("#mtEntry input[name='id']").val("null");
                $("#mtEntry input[name='product']").val(info.product);
                $("#mtEntry").data("operat", 'change');
                $("#mtEntry .formulaTtl").html("更换配方");
                var selectStr = '<div id="selectFS"></div>';
                $("#mtEntry #formulaSn").data("old", "").after(selectStr);
                selectFSOption();
                $("#mtEntry .entry").removeAttr("disabled");
                $("#entryMainMatBtn").prop("disabled",false);
                $("#entryFormulaMatBtn").prop("disabled",false);
                bounce_Fixed.show($("#mtEntry"));
                setInter('newFormula');
                break;
            case "changeFmLog":  // 配方更换记录
                var num = $("input[name='mainConShow']").val();
                goMainCon(7);
                $("#goPre69").data("num", num);
                var item = JSON.parse($(this).siblings(".hd").html());
                var str =   "    <td>"+ item['innerSn'] +"</td>" +
                    "    <td>"+ item['name'] +"</td>" +
                    "    <td>"+ item['model'] +"</td>" +
                    "    <td>"+ item['specifications'] +"</td>" +
                    "    <td>"+ item['unit'] +"</td>" +
                    "    <td>"+ (item['createName']) + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                    "    <td>" +
                    "       <span data-type=\"gsScan\" class=\"btn ty-color-blue\">查看</span>" +
                    "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                    "    </td>" ;
                $(".mainCon7 tbody:eq(0)").children("tr:eq(1)").html(str);
                getChangeRecord(1);
                break;
            case "fmUesdGs":  // 已停用的材料 - 曾使用过该配方的产品及零件
                goMainCon(8);
                var item = JSON.parse($(this).parent().next().children(".hd").html());
                var str =
                    "    <td>"+ item['code'] +"</td>" +
                    "    <td>"+ item['name'] +"</td>" +
                    "    <td>"+ (item['majorIngredient'] || 0) +"种</td>" +
                    "    <td>"+ (item['minorIngredient'] || 0) +"种</td>" +
                    "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                $(".mainCon8 tbody:eq(0)").children("tr:eq(1)").html(str);
                $(".mainCon8 tbody:eq(0)").data('mfID', item['id']);
                $("#goback2").data("num", "5");
                getFmBeforeBindingPdList(1);
                break;
            case "fmUsingGs":  // 使用该配方的产品及零件
                goMainCon(6);
                var item = JSON.parse($(this).parent().next().children(".hd").html());
                var str =
                    "    <td>"+ item['code'] +"</td>" +
                    "    <td>"+ item['name'] +"</td>" +
                    "    <td>"+ (item['majorIngredient'] || 0) +"种</td>" +
                    "    <td>"+ (item['minorIngredient'] || 0) +"种</td>" +
                    "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                $(".mainCon6 tbody:eq(0)").children("tr:eq(1)").html(str);
                $(".mainCon6 tbody:eq(0)").data('mfID', item['id']);
                getFmBindingPdList(1);
                break;
        }
    });
    $(".ty-btn").click(function(){
        var type = $(this).data('type');
        switch (type){
            case "goMain": // 返回材料首页
                goMainCon(1);
                var cur = $("#yeCon1 .yecur").html();
                var key = $("#searchKeyBase").val();
                getForulaList(key, cur);
                break;
            case "gopre2": // 返回上一页
                goMainCon(2);
                break;
            case "goPre3": // 返回上一页
                goMainCon(3);
            case "goPre69": // 返回上一页
                var num = $(this).data("num");
                goMainCon(num);
            case "goback2": // 返回上一页
                var num = $(this).data("num");
                goMainCon(num);
                break;
        }
    });
    $(".mainCon3 tbody").on("click",".fa", function () {
        var hasOk = $(this).hasClass("fa-dot-circle-o");
        if(hasOk){
            $(this).attr("class","fa fa-circle-o");
        }else{
            $(".mainCon3 tbody .fa").attr("class","fa fa-circle-o") ;
            $(this).attr("class","fa fa-dot-circle-o");
            getPdMtBaseListNoPage();
            goMainCon(4);
            var item = JSON.parse($(this).parent().siblings(":last").children(".hd").html()); ;
            var str = "<tr>" +
                "    <td>"+ item['code'] +"</td>" +
                "    <td>"+ item['name'] +"</td>" +
                "    <td>"+ (item['majorIngredient'] || 0) +"种</td>" +
                "    <td>"+ (item['minorIngredient'] || 0) +"种</td>" +
                "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                "    <td>" +
                "        <span data-type=\"fmScan\" class=\"btn ty-color-blue \">查看</span>" +
                '        <span class="hd">'+ JSON.stringify(item) +"</span>" +
                "    </td>" +
                "</tr>" ;
            $(".mainCon4 tbody:eq(0)").children(":gt(0)").remove();
            $(".mainCon4 tbody:eq(0)").append(str);
        }
    });
    // mainCon4 二级导航
    $(".mainCon4 .ty-secondTab").children().click(function () {
        toggleSelect($(this).index()) ;
    })
    $(".mainCon4 table").on('click', '.fa', function(){
        var klass = $(this).hasClass('fa-check-square-o');
        var item = $(this).parent().siblings(":last").find(".hd").html();
        item = JSON.parse(item) ;
        if(klass){
            $(this).attr("class" , "fa  fa-square-o");
            setKeyByID(item.id ,"selected", 0);
        }else{
            $(this).attr("class" , "fa  fa-check-square-o");
            setKeyByID(item.id ,"selected", 1);
        }
        countGsList();
        $(this).parent().parent().remove();

    });
    // 账号输入事件，搜索功能
    $("#entryFormulaMat").on('input click', '#innerSn,#innerName', function (e) {
        var matchStr = $(this).attr("name"),hasMatch= 0;
        if (matchStr == 'code') {
            var innerEntry = $("#innerSn").val();
            var old = $("#innerSn").data("old");
            $("#selecGS option").hide();
            $("#selecGS option").each(function () {
                var i_innerSn = $(this).html();
                if(i_innerSn.indexOf(innerEntry) != -1){
                    $(this).show();
                    hasMatch++;
                }
            });
            var evtType = e.type;
            if (evtType == 'click') {
                $("#selecGS").slideDown('fast')
            } else {
                var num = $("#entryFormulaMat table [disabled]").length;
                if(num > 0 && old !== innerEntry){
                    $("#addUnitBtn").addClass("addUnitActive").attr("onclick","addUnit()");
                    $("#entryFormulaMat td").removeAttr("disabled");
                    $("#entryFormulaMat input").not("#innerSn").val("").removeAttr("disabled");
                    $("#entryFormulaMat select").val("").removeAttr("disabled");
                    $("#entryFormulaMat textarea").val("").removeAttr("disabled");
                    $("#entryFormulaMat .textMax").html("0/100");
                }
            }
            if(hasMatch == 0){
                $("#selecGS").slideUp('fast')
            } else {
                $("#selecGS").slideDown('fast')
            }
        } else if (matchStr == 'name') {
            var nameEntry = $("#entryFormulaMat [name='name']").val();
            var old = $("#entryFormulaMat [name='name']").data("old");
            $("#selecGN option").hide();
            $("#selecGN option").each(function () {
                var i_innerNm = $(this).html();
                if(i_innerNm.indexOf(nameEntry) != -1){
                    $(this).show();
                    hasMatch++;
                }
            });
            var evtType = e.type;
            if (evtType == 'click') {
                $("#selecGN").slideDown('fast')
            } else {
                var num = $("#entryFormulaMat table [disabled]").length;
                if (num > 0 && old !== nameEntry) {
                    $("#addUnitBtn").addClass("addUnitActive").attr("onclick", "addUnit()");
                    $("#entryFormulaMat td").removeAttr("disabled");
                    $("#entryFormulaMat input:not([name='name'])").val("").removeAttr("disabled");
                    $("#entryFormulaMat select").val("").removeAttr("disabled");
                    $("#entryFormulaMat textarea").val("").removeAttr("disabled");
                    $("#entryFormulaMat .textMax").html("0/100");
                }
            }
            if(hasMatch == 0){
                $("#selecGN").slideUp('fast')
            } else {
                $("#selecGN").slideDown('fast')
            }
        }
    })
    // 材料录入 选中
    $("#mtEntry").on('input click', '#formulaSn', function(e){
        var hasMatch= 0;
        var nameEntry = $(this).val();
        var old = $(this).data("old");
        $("#selectFS option").hide();
        $("#selectFS option").each(function () {
            var i_innerNm = $(this).html();
            if(i_innerNm.indexOf(nameEntry) != -1){
                $(this).show();
                hasMatch++;
            }
        });
        var evtType = e.type;
        if (evtType == 'click') {
            $("#selectFS").slideDown('fast')
        } else {
            var num = $("#mtEntry [disabled]").length;
            if (num > 0 && old !== nameEntry) {
                $("#mtEntry input:not(#formulaSn, #productId)").val("").removeAttr("disabled");
                $("#mainMaterial tbody").html("");
                $("#subMaterial tbody").html("");
                $("#entryMainMatBtn").prop("disabled", false);
                $("#entryFormulaMatBtn").prop("disabled", false);
            }
        }
        if (hasMatch == 0) {
            $("#selectFS").slideUp('fast')
        } else {
            $("#selectFS").slideDown('fast')
        }
    });
    // 材料录入 选中
    $("#selecGS").on('click', 'option', function(){
        setSelectOption($(this));
        $("#selecGS").slideUp('fast')
    });
    $("#selecGN").on('click', 'option', function(){
        setSelectOption($(this));
        $("#selecGN").slideUp('fast')
    });
    $(document).on("click", function (e) {
        if ($("#mtEntry:visible").length > 0) {
            var targetFsn = $(e.target).parents(".col-formulaSn").length === 0
            if (targetFsn) {
                $("#selectFS").slideUp('fast')
            }
        }
        if($("#entryFormulaMat:visible").length > 0) {
            var targetSn = $(e.target).parents(".innerSn-col").length === 0
            var targetNm = $(e.target).parents(".innerName-col").length === 0
            if (targetSn && targetNm) {
                $("#selecGS").slideUp('fast')
                $("#selecGN").slideUp('fast')
            } else if (targetSn) {
                $("#selecGS").slideUp('fast')
            } else if (targetNm) {
                $("#selecGN").slideUp('fast')
            }
        }
    })
    getForulaList("", 1);
});
// creator: 李玉婷，2020-08-07 16:59:09，定时器
function setInter(name) {
    switch (name) {
        case 'newFormula':
            $(".bounce_Fixed").everyTime('0.5s',"newFormula",function(){
                $("#mainMatNum").html($("#mainMaterial tbody tr").length);
                $("#subMatNum").html($("#subMaterial tbody tr").length);
            });
            break;
        case 'matEntery':
            $(".bounce_Fixed2").everyTime('0.5s','partEntry',function(){
                var isNull = false;
                $("#entryFormulaMat [require]").each(function () {
                    var val = $(this).val();
                    if(!val){
                        isNull = true
                    }
                })
                var amount = $("#amount").val();
                if(isNull || amount == 0){
                    $("#partEntryOK").prop("disabled", true);
                }else{
                    $("#partEntryOK").prop("disabled", false);
                }
            });
            break;
    }
}
// creator: 李玉婷，2020-07-10 16:04:32，切换页面
function goMainCon(num) {
    $("input[name='mainConShow']").val(num);
    $(".mainCon" + num).show().siblings().hide();
}
// creator: 李玉婷，2020-07-8 8:10:45，切换选中未选择
function toggleSelect(state) {
    $(".mainCon4 .ty-secondTab").children().removeClass('ty-active');
    $(".mainCon4 .ty-secondTab").children(":eq("+ state +")").addClass('ty-active');
    $(".mainCon4 .select"+state).show().siblings().hide();
   setPageData(1, pageSize );
}
// creator: 李玉婷，2020-07-29 20:28:11，获取配方列表
function getForulaList(keyword, currPage) {
    $("#forulaList tbody").html("");
    $.ajax({
        "url" : "../formula/getHomeData.do" ,
        "data" :{
            "keyword": keyword,
            "currPage": currPage,
            "pageSize": pageSize
        } ,
        "success":function(data){
            var list = data.data;
            var totalPage = data['totalPage'];
            $("#confirmNum").html(data.pdNum);
            $("#currentForula").html(data.totalRows || 0);
            $("#currentStopForula").html(data.stopNum);
            var json = JSON.stringify({"keyword": keyword}) ;
            setPage($("#yeCon1") , currPage , totalPage, "forulaList", json);
            if (list && list.length > 0) {
                var html = "";
                for (var i=0;i<list.length;i++) {
                    html +=
                        '<tr>' +
                        '    <td>'+ list[i].code +'</td>' +
                        '    <td>'+ list[i].name +'</td>' +
                        '    <td>'+ handleNull(list[i].majorIngredient || 0) +'种</td>' +
                        '    <td>'+ handleNull(list[i].minorIngredient || 0) +'种</td>' +
                        '    <td>'+ list[i].createName +'&nbsp;&nbsp;'+ new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '    <td class="ty-td-control">'+ list[i].num +'种    <span class="ty-color-blue btn" data-type="fmUsingGs">去查看</span></td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue btn" data-type="fmScan">查看</span>' +
                        '        <span class="ty-color-blue btn" data-type="fmStop">停用</span>' +
                        '        <span class="hd">'+ JSON.stringify(list[i]) +"</span>" +
                        '        <span class="ty-color-red btn" data-type="fmDel">删除</span>' +
                        '    </td>' +
                        '</tr>';
                }
                $("#forulaList tbody").html(html);
            }
        }
    })
}
// creator: 李玉婷，2020-08-07 13:46:49，获取获取已停用配方列表
function getStopForulaList(currPage){
    $.ajax({
        "url":"../formula/getStopFormulaList.do",
        "data":{ "currPage":currPage , "pageSize":pageSize },
        success:function (res) {
            var totalRows = res['totalRows'];
            var totalPage = res['totalPage'];
            $(".mainCon5 .totalRows").html(totalRows);
            $(".mainCon5 tbody").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ (item['majorIngredient'] || 0) +"种</td>" +
                        "    <td>"+ (item['minorIngredient'] || 0) +"种</td>" +
                        "    <td>"+ item['updateName'] + " " + (new Date(item['updateDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td class=\"ty-td-control\">"+ item['num'] +"种<span data-type=\"fmUesdGs\" class=\"btn ty-color-blue \">去查看</span></td>" +
                        "    <td>" +
                        "       <span data-type=\"fmScan\" class=\"btn ty-color-blue \">查看</span>" +
                        "        <span data-type=\"fmStart\" class=\"btn ty-color-blue \">恢复使用</span>" +
                        "        <span data-type=\"fmDel\" class=\"btn ty-color-red \">删除</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon5 tbody").append(str);
            setPage($("#yeCon5") , currPage , totalPage, "stopForulaList");
        }
    })
}
function setSelectOption(thisObj) {
    var i_item = thisObj.val();
    var parentObj = thisObj.parent().attr("id");
    i_item = JSON.parse(i_item);
    $("#id").val(i_item['id']);
    var memoLen = i_item["memo"].length;
    for(var key in i_item){
        if ('name' != key && 'code' != key) {
            $("#entryFormulaMat .entry[name=" + key+ "]").val(i_item[key]).attr("disabled", "true");
        }
    }
    $("#entryFormulaMat .entry[name='code']").val(i_item["code"]).data("old",i_item["code"]);
    $("#entryFormulaMat .entry[name='name']").val(i_item["name"]).data("old",i_item["name"]);
    $("#addUnitBtn").removeClass("addUnitActive").removeAttr("onclick");
    $("#entryFormulaMat table").next("p.textMax").html(memoLen + "/100");
    $("#entryFormulaMat .entry[name='amount']").removeAttr("disabled");
}
// create:hxz 2020-06-25 录入零部件 - 获得可选的图号
function partEntryBtn(){
    $("#selecGS").html("");
    $("#selecGN").html("");
    $.ajax({
        "url": "../formula/selectMtBaseList.do",
        success:function (res) {
            var list = res['list'], str = "", html = "" ;
            if(list){
                selectMatList = list;
                for(var i = 0 ; i < list.length ; i++){
                    str += "<option value='"+ JSON.stringify(list[i]) +"'>"+ list[i]['code'] +"</option>"
                    html += "<option value='"+ JSON.stringify(list[i]) +"'>"+ list[i]['name'] +"</option>"
                }
            }
            $("#selecGS").html(str);
            $("#selecGN").html(html);
        }
    });
}

function selectFSOption() {
    $.ajax({
        "url": "../formula/getFormulaListNoPage.do",
        success:function (res) {
            var list = res['list'], str = "" ;
            if(list){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<option value='"+ JSON.stringify(list[i]) +"' onclick='setFormulaData($(this))'>"+ list[i]['code'] +"</option>"
                }
            }
            $("#selectFS").html(str);
        }
    });
}
// creator: 李玉婷，2020-10-15 15:28:55，选定配方后赋值
function setFormulaData(thisObj) {
    $("#selectFS").slideUp('fast')
    $(".bounce_Fixed").stopTime("gsMatch");
    $("#entryMainMatBtn").prop("disabled",true);
    $("#entryFormulaMatBtn").prop("disabled",true);
    var i_item = thisObj.val();
    i_item = JSON.parse(i_item);
    $.ajax({
        "url" : "../formula/getFormulaDetails.do" ,
        "data" :{
            "id": i_item.id
        } ,
        "success":function(data){
            var detail = data.data;
            var zlList = detail.zlList;
            var flList = detail.flList;
            $("#mainMatNum").html(zlList.length);
            $("#subMatNum").html(flList.length);
            $("#mtEntry .entry").each(function () {
                var key = $(this).attr("name");
                if (key != 'product' && key != 'code') {
                    $(this).val(detail[key]).attr("disabled", "true");
                }
            });
            $("#formulaSn").val(detail['code']).data("old", detail['code']);
            if (zlList && zlList.length) {
                var list1 = '';
                for(var z=0;z<zlList.length;z++) {
                    list1 +=
                        '<tr data-info=\''+ JSON.stringify(zlList[z]) +'\'>' +
                        '    <td>'+ zlList[z].code +'</td>' +
                        '    <td>'+ zlList[z].name +'</td>' +
                        '    <td>'+ zlList[z].model +'</td>' +
                        '    <td>'+ zlList[z].specifications +'</td>' +
                        '    <td>'+ zlList[z].unit +'</td>' +
                        '    <td>'+ zlList[z].amount +'</td>' +
                        '    <td>--</td>' +
                        '</tr>';
                }
                $("#mainMaterial tbody").html(list1);
            }
            if (flList && flList.length) {
                var list2 = '';
                for(var t=0;t<flList.length;t++) {
                    list2 +=
                        '<tr data-info=\''+ JSON.stringify(flList[t]) +'\'>' +
                        '    <td>'+ flList[t].code +'</td>' +
                        '    <td>'+ flList[t].name +'</td>' +
                        '    <td>'+ flList[t].model +'</td>' +
                        '    <td>'+ flList[t].specifications +'</td>' +
                        '    <td>'+ flList[t].unit +'</td>' +
                        '    <td>'+ flList[t].amount +'</td>' +
                        '    <td>--</td>' +
                        '</tr>';
                }
                $("#subMaterial tbody").html(list2);
            }
        }
    })
}
// creator: 李玉婷，2020-08-03 15:20:35，录入主/辅料完毕
function finishEnter() {
    var amount = $("#amount").val();
    if (amount != 0) {
        var empty = 0;
        $("#entryFormulaMat [require]").each(function () {
            var val = $(this).val();
            if (val == "") {
                empty++;
            }
        });
        if (empty > 0) {
            bounce_Fixed3.show($("#giveTip"));
            return false;
        }
        var id = $("#entryFormulaMat [name='id']").val();
        if (!id) {
            $(".isCurrentStock .fa").removeClass("fa-circle").addClass("fa-circle-o");
            bounce_Fixed3.show($("#entryTip"));
            bounce_Fixed3.everyTime('0.5s', 'entryTip', function () {
                var len = $(".isCurrentStock .fa-circle").length;
                if (len > 0) {
                    $("#finishEnterBtn").prop("disabled", false);
                } else {
                    $("#finishEnterBtn").prop("disabled", true);
                }
            })
        } else {
            finishEnterSure();
        }
    }
}
// creator: 李玉婷，2020-08-03 17:08:56，录入主/辅料完毕-是否需要加入新材料
function finishEnterSure() {
    var json = {};
    $("#entryFormulaMat .entry").each(function(){
        var key = $(this).attr("name");
        var val = $(this).val();
        json[key] = val;
    });
    var type = $('#entryFormulaMat').data("type");
    var operType = $("#mtEntry").data("operat");
    bounce_Fixed3.cancel();
    bounce_Fixed2.cancel();
    var btnName = '修改';
    if (operType == 'update') btnName='修改份数';
    var html =
        '<tr data-info=\''+ JSON.stringify(json) +'\'>' +
        '   <td>' + handleNull(json['code']) +'</td>' +
        '   <td>' + handleNull(json['name']) +'</td>' +
        '   <td>' + handleNull(json['model']) +'</td>' +
        '   <td>' + handleNull(json['specifications']) +'</td>' +
        '   <td>' + handleNull(json['unit']) +'</td>' +
        '   <td>' + handleNull(json['amount']) +'</td>' +
        '   <td>' +
        '       <span class="ty-color-blue" onclick="numEdit($(this))">'+ btnName +'</span>' +
        '       <span class="ty-color-red" onclick="commonDel($(this))">删除</span>' +
        '   </td>' +
        '</tr>';
    if (type == 1) {
        $("#mainMaterial tbody").append(html);
    } else {
        $("#subMaterial tbody").append(html);
    }
}
// creator: 李玉婷，2020-08-07 09:38:05，配方录入- 删除
function commonDel(obj){
    var delObj = obj.parents("tr");
    $("#delObjTip #msgTip").html("确定删除该行原料吗？");
    $("#delObjTip").data("operObj", delObj);
    bounce_Fixed3.show($("#delObjTip"));
}
// creator: 李玉婷，2020-08-07 20:33:59，配方修改-确定
function updateDel(obj) {
    var delObj = obj.parents("tr");
    var str =
        '<p>操作成功后，该配方中将不再包含该原料。</p>' +
        '<p>确定从该配方中删除该材料？</p>';
    $("#delObjTip #msgTip").html(str);
    $("#delObjTip").data("operObj", delObj);
    bounce_Fixed3.show($("#delObjTip"));
}
// creator: 李玉婷，2020-08-07 10:16:38，删除确定
function commonDelSure() {
    var delObj = $("#delObjTip").data("operObj");
    delObj.remove();
    bounce_Fixed3.cancel();
}
// creator: 李玉婷，2020-07-30 14:27:03，新增或配置配方
function addOrUpdateFormula() {
    var tjType = $("#mtEntry").data("operat");
    var isNull = $("#formulaSn").val();
    var len1 = $("#mainMaterial tbody tr").length;
    var len2 = $("#subMaterial tbody tr").length;
    if(isNull == "" && (len1 == 0 && len2 == 0)){
        bounce_Fixed3.show($("#giveTip"));
        return false;
    }
    if (tjType == "change") {
        bounce_Fixed3.show($("#changeFormula"));
    } else if (tjType == "update") {
        addOrUpdateFormulaSure();
    } else {
        bounce_Fixed3.show($("#addFormulaTip"));
    }
}
// creator: 李玉婷，2020-08-11 09:34:32，新增或配置配方确定
function addOrUpdateFormulaSure(){
    var  tjType = $("#mtEntry").data("operat");
    var data= {} ;
    var zlList = [],flList = [];
    $("#mtEntry .entry").each(function(){
        var key = $(this).attr("name");
        var val = $(this).val();
        data[key] = val ;
    });
    $("#mainMaterial tbody tr").each(function(){
        var mat = $(this).data("info");
        zlList.push(mat);
    });
    $("#subMaterial tbody tr").each(function(){
        var mat = $(this).data("info");
        flList.push(mat);
    });
    data.zlList = zlList;
    data.flList = flList;
    $.ajax({
        "url":"../formula/addOrUpdateFormula.do",
        "data": {
            "formula": JSON.stringify(data)
        } ,
        success:function (res) {
            bounce_Fixed3.cancel();
            var code = res['code'];
            if(code == "200"){
                bounce_Fixed.cancel();
                if (tjType == 'add') {
                    var yecur = $("#yeCon1 .yecur").html();
                    var key = $("#searchKeyBase").val();
                    getForulaList(key, yecur);
                } else if (tjType == 'update') {
                    var yecur1 = $("#yeCon1 .yecur").html();
                    var scanId = $("#mtEntry input[name='id']").val();
                    var key = $("#searchKeyBase").val();
                    getScanMatData(scanId);
                    getForulaList(key, yecur1);
                } else if (tjType == 'change') {
                    var yecur = $("#yeCon6 .yecur").html();
                    getFmBindingPdList(yecur);
                } else if (tjType == 'relate') {
                    var yecur = $("#yeCon2 .yecur").html();
                    getPdMtBaseList(yecur);
                }
            }else{
                layer.msg("操作失败:" + res.msg);
            }
        }
    })
}
// creator: 李玉婷，2020-08-24 18:46:36，
function mtEntryCancel() {
    bounce_Fixed.cancel();
    bounce_Fixed3.cancel();
}
// creator: 李玉婷，2020-08-06 12:32:58，配方查看
function getScanMatData(id){
    $("#scanMainMat tbody").html("");
    $("#scanSubMat tbody").html("");
    $.ajax({
        "url" : "../formula/getFormulaDetails.do" ,
        "data" :{
            "id": id
        } ,
        "success":function(data){
            var detail = data.data;
            var zlList = detail.zlList;
            var flList = detail.flList;
            $("#scanZlNum").html(zlList.length);
            $("#scanFlNum").html(flList.length);
            $("#mtScan .scanMt").each(function () {
                var key = $(this).data("name");
                $(this).html(detail[key]);
            });
            var num = $("input[name='mainConShow']").val();
            if(num != 1 && num != "") {
                $("#stopIntro").html(detail.updateName + "已于"+ new Date(detail.updateDate).format('yyyy-MM-dd hh:mm:ss')+"将本配方“停用”！");
            }
            if (zlList && zlList.length) {
                var list1 = '';
                for(var z=0;z<zlList.length;z++) {
                    list1 +=
                        '<tr data-info=\''+ JSON.stringify(zlList[z]) +'\'>' +
                        '    <td>'+ zlList[z].code +'</td>' +
                        '    <td>'+ zlList[z].name +'</td>' +
                        '    <td>'+ zlList[z].model +'</td>' +
                        '    <td>'+ zlList[z].specifications +'</td>' +
                        '    <td>'+ zlList[z].unit +'</td>' +
                        '    <td>'+ zlList[z].amount +'</td>' +
                        '</tr>';
                }
                $("#scanMainMat tbody").html(list1);
            }
            if (flList && flList.length) {
                var list2 = '';
                for(var t=0;t<flList.length;t++) {
                    list2 +=
                        '<tr data-info=\''+ JSON.stringify(flList[t]) +'\'>' +
                        '    <td>'+ flList[t].code +'</td>' +
                        '    <td>'+ flList[t].name +'</td>' +
                        '    <td>'+ flList[t].model +'</td>' +
                        '    <td>'+ flList[t].specifications +'</td>' +
                        '    <td>'+ flList[t].unit +'</td>' +
                        '    <td>'+ flList[t].amount +'</td>' +
                        '</tr>';
                }
                $("#scanSubMat tbody").html(list2);
            }
        }
    })
}
// creator: 李玉婷，2020-08-07 14:10:06，修改配方
function updateFormula() {
    $("#mainMaterial tbody").html("");
    $("#subMaterial tbody").html("");
    $("#mtEntry .formulaTtl").html("修改配方");
    $("#mtEntry .entry").each(function () {
        var key = $(this).attr("name");
        var val = $("#mtScan .scanMt[data-name="+key+"]").html();
        $(this).val(val);
    });
    var list1 = '',list2 = '';
    $("#scanMainMat tbody tr").each(function () {
        var trData = $(this).data("info");
        list1 +=
            '<tr data-info=\''+ JSON.stringify(trData) +'\'>' +
            '    <td>'+ trData.code +'</td>' +
            '    <td>'+ trData.name +'</td>' +
            '    <td>'+ trData.model +'</td>' +
            '    <td>'+ trData.specifications +'</td>' +
            '    <td>'+ trData.unit +'</td>' +
            '    <td>'+ trData.amount +'</td>' +
            '    <td>' +
            '       <span class="ty-color-blue" onclick="numEdit($(this))">修改份数</span>' +
            '       <span class="ty-color-red" onclick="updateDel($(this))">删除</span>' +
            '    </td>' +
            '</tr>';
    })
    $("#mainMaterial tbody").html(list1);
    $("#scanSubMat tbody tr").each(function () {
        var trData = $(this).data("info");
        list2 +=
            '<tr data-info=\''+ JSON.stringify(trData) +'\'>' +
            '    <td>'+ trData.code +'</td>' +
            '    <td>'+ trData.name +'</td>' +
            '    <td>'+ trData.model +'</td>' +
            '    <td>'+ trData.specifications +'</td>' +
            '    <td>'+ trData.unit +'</td>' +
            '    <td>'+ trData.amount +'</td>' +
            '    <td>' +
            '       <span class="ty-color-blue" onclick="numEdit($(this))">修改份数</span>' +
            '       <span class="ty-color-red" onclick="updateDel($(this))">删除</span>' +
            '    </td>' +
            '</tr>';
    })
    $("#subMaterial tbody").html(list2);
    $("#mtEntry").data("operat", 'update');
    $("#mtEntry .entry").removeAttr("disabled");
    $("#selectFS").remove();
    $("#formulaSn").removeAttr("onclick");
    $("#entryMainMatBtn").prop("disabled",false);
    $("#entryFormulaMatBtn").prop("disabled",false);
    bounce_Fixed.show($("#mtEntry"));
    setInter('newFormula');
}
// creator: 李玉婷，2020-08-07 15:39:10，修改份数
function numEdit(obj) {
    bounce_Fixed2.show($("#editNum"));
    var info = obj.parents("tr").data("info");
    $("#editNum").data("editObj", obj);
    for(var key in info){
        $("#editNum ." + key).html(info[key]);
    }
    $("#editNum input").val(info["amount"]);
    bounce_Fixed2.everyTime('0.5s','editNum',function(){
        var val = $("#editNum input[name=amount]").val();
        if(val == '' || val == 0){
            $("#finishNumEdit").prop("disabled", true);
        }else{
            $("#finishNumEdit").attr("disabled", false);
        }
    })
}
// creator: 李玉婷，2020-08-07 18:35:19，修改份数确定
function finishNumEdit() {
    bounce_Fixed2.cancel();
    var obj = $("#editNum").data("editObj");
    var amount = $("#editNum input").val();
    var infoData = obj.parents("tr").data("info");
    infoData.amount = amount;
    obj.parents("tr").data("info", infoData);
    obj.parents("tr").children().eq(5).html(amount);
}
// creater :hxz 2020-06-05 停启用材料
function mtStopOk() {
    var mfID = $("#stopTip").data("mfID");
    var state = $("#stopTip").data("state");
    $.ajax({
        "url":"../formula/startStopFormula.do",
        "data":{  "id": mfID , "state":state },
        success:function (res) {
            bounce.cancel();
            if(res['code'] == 200){
                layer.msg("操作成功");
                if(state == 0){ // 停用的
                    var curPage = $("#yeCon1 .yecur").html();
                    var key = $("#searchKeyBase").val();
                    getForulaList(key, curPage);
                }else{
                    var curPage = $("#yeCon5 .yecur").html();
                    getStopForulaList(curPage);
                }
            }else{
                var str =  "操作失败，因为还有使用该配方的产品或零件！" ;
                $("#iknow .msg").html(str);
                bounce.show( $("#iknow"))
            }
        }
    })
}
// creater :hxz 2020-06-05 删除材料
function mtDelOk() {
    var mfID = $("#delTip").data("mfID");
    var objCur = $("#delTip").data("obj");
    $.ajax({
        "url":"../formula/deleteFormula.do",
        "data":{  "id": mfID },
        success:function (res) {
            bounce.cancel();
            if(res['code'] == 200){
                layer.msg("操作成功");
                objCur.parents("tr").remove();
                if ($(".mainCon1:visible").length > 0) {
                    var curPage = $("#yeCon1 .yecur").html();
                    var key = $("#searchKeyBase").val();
                    getForulaList(key, curPage);
                } else if ($(".mainCon5:visible").length > 0) {
                    var curPage = $("#yeCon5 .yecur").html();
                    getStopForulaList(curPage);
                }
            }else{
                var str = "删除失败，因为此配方曾被某产品或零件使用过！" ;
                if(false){
                    str =  "操作失败，因为还有使用该配方的产品或零件！" ;
                }
                $("#iknow .msg").html(res.msg);
                bounce.show( $("#iknow"));
            }
        }
    })
}
// creator: 李玉婷，2020-07-29 22:21:39，去处理 - 待选择配方产品
function getPdMtBaseList(currPage) {
    $("#unChooseMat tbody").html("");
    $.ajax({
        "url" : "../formula/getPdFoBaseList.do" ,
        "data" :{
            "currPage": currPage,
            "pageSize": pageSize
        } ,
        "success":function(data){
            var list = data.data;
            var totalPage = data['totalPage'];
            $("#unChooseNum").html(data.totalRows);
            if (list && list.length > 0) {
                var html = "";
                for (var i=0;i<list.length;i++) {
                    html +=
                        '<tr>' +
                        '    <td>'+ list[i].innerSn +'</td>' +
                        '    <td>'+ list[i].name +'</td>' +
                        '    <td>'+ list[i].model +'</td>' +
                        '    <td>'+ list[i].specifications +'</td>' +
                        '    <td>'+ handleNull(list[i].unit) +'</td>' +
                        '    <td>'+ handleNull(list[i].createName) +'&nbsp;&nbsp;'+ new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue btn" data-type="gsScan">查看</span>' +
                        '        <span class="ty-color-blue btn" data-type="gsSelect">去处理</span>' +
                        '        <span class="hd">'+ JSON.stringify(list[i]) +'</span>' +
                        '    </td>' +
                        '</tr>';
                }
                $("#unChooseMat tbody").html(html);
            }
            setPage($("#yeCon2") , currPage , totalPage, "goHandle", "");
        }
    })
}
// creator: 李玉婷，2020-08-04 15:01:46，去试试 - 获取配方列表
function getFormulaList (cur) {
    $("#selectFormulaList tbody").html("");
    $.ajax({
        "url" : "../formula/getFormulaList.do" ,
        "data" :{
            "currPage": cur,
            "pageSize": pageSize
        } ,
        "success":function(data){
            var list = data.data;
            var totalPage = data['totalPage'];
            if (list && list.length > 0) {
                var html = "";
                for (var i=0;i<list.length;i++) {
                    html +=
                        '<tr>' +
                        '    <td><i class="fa fa-circle-o"></i></td>' +
                        '    <td>'+ list[i].code +'</td>' +
                        '    <td>'+ list[i].name +'</td>' +
                        '    <td>'+ (list[i].majorIngredient || 0) +'种</td>' +
                        '    <td>'+ (list[i].minorIngredient || 0) +'种</td>' +
                        '    <td>'+ list[i].createName +'&nbsp;&nbsp;'+ new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '    <td>' +
                        '        <span data-type="fmScan" class="btn ty-color-blue">查看</span>' +
                        '        <span class="hd">'+ JSON.stringify(list[i]) +'</span>' +
                        '    </td>' +
                        '</tr>';
                }
                $("#selectFormulaList tbody").html(html);
            }
            setPage($("#yeCon3") , cur , totalPage, "goSelectTry", "");
        }
    })
}
// creator: 李玉婷，2020-08-04 16:45:55，待选择列表
function getPdMtBaseListNoPage(){
    $.ajax({
        "url":"../formula/getPdFoBaseListNoPage.do",
        success:function (res) {
            var list = res['data'], str = "" ;
            GSList = list ;
            countGsList();
            $(".mainCon4 .ty-secondTab li:eq(0)").click();
        }
    })
}
// create:hxz 2020-06-22  计算选中未选中的数据
function countGsList(){
    var list = [] , list2 = [] ;
    for(var j = 0 ; j < GSList.length ; j++){
        if(GSList[j]["selected"] == 1){
            list.push(GSList[j]);
        }else{
            list2.push(GSList[j]);
        }
    }
    selectGS = list ;
    notSelectGS = list2 ;
    $(".mainCon4 .ty-secondTab").children("li:eq(0)").find("span").html(notSelectGS.length);
    $(".mainCon4 .ty-secondTab").children("li:eq(1)").find("span").html(selectGS.length);
}
// create:hxz 2020-07-22  maincon4 渲染表格数据
function setPageData(cur, per) {
    var list = [] , faStr = "" ;
    $(".mainCon4 tbody:eq(1)").children(":gt(0)").remove();
    var navIndex = $(".mainCon4 .ty-secondTab li").index($(".mainCon4 .ty-secondTab .ty-active"));
    if(navIndex == 0){
        faStr = "<td><span class=\"fa fa-square-o\"></span></td>";
        list = notSelectGS ;
    }else if(navIndex == 1){
        faStr = "<td><span class=\"fa fa-check-square-o\"></span></td>";
        list = selectGS ;
    }
    var str = "" ;
    var start = (cur-1)*per ;
    var end = start + per ;
    if(list){
        for(var i = start ; i < end ; i++){
            var item = list[i];
            if(item){
                str +=" <tr>" + faStr +
                    "    <td>"+ item['innerSn'] +"</td>" +
                    "    <td>"+ item['name'] +"</td>" +
                    "    <td>"+ item['model'] +"</td>" +
                    "    <td>"+ item['specifications'] +"</td>" +
                    "    <td>"+ handleNull(item['unit']) +"</td>" +
                    "    <td>"+ handleNull(item['createName']) + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                    "    <td>" +
                    "        <span class=\"btn ty-color-blue\" data-type=\"gsScan\">查看</span>" +
                    "       <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                    "    </td>" +
                    "</tr>";
            }
        }
    }
    $(".mainCon4 tbody:eq(1)").append(str);
}
// create:hxz 2020-06-21 设置商品键值对
function setKeyByID(id , key , val) {
    var list = GSList ;
    for(var i = 0 ; i < list.length ; i++){
        if(list[i]['id'] == id){
            list[i][key] = val ;
        }
    }
}
// creater :hxz 2020-06-05 确定选择好产品
function selectMt() {
    var item = JSON.parse( $(".mainCon4 tbody:eq(0) tr:last").find(".hd").html() ) ;
    var mtId = item.id , baseList = [];
    var list = selectGS ;
    for(var i = 0 ; i < list.length ; i++){
        baseList.push(list[i]["id"]);
    }
    baseList = baseList.toString();
    $.ajax({
        "url":"../formula/bindOperation.do",
        "data":{ "baseList":baseList, "formula":mtId },
        success:function (res) {
            bounce.cancel();
            if(res.code == 200){
                layer.msg("操作成功");
                goMainCon(2);
                getPdMtBaseList(1);
            }else{
                layer.msg("操作失败");
            }
        }
    })
}
// creator: 李玉婷，2020-08-10 8:59:58，根据配方获取已绑产品列表
function getFmBindingPdList(cur) {
    var id = $(".mainCon6 tbody:eq(0)").data('mfID');
    $.ajax({
        "url":"../formula/getBindingPdList.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "id":id },
        success:function (res) {
            var totalRows = res['totalRows'];      //
            var totalPage = res['totalPage'];      //
            var usedNum = res['beforeNum'];      //

            $(".mainCon6 .usedNum").html(usedNum);
            $(".mainCon6 .totalRows").html(totalRows);
            $(".mainCon6 tbody:eq(1)").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['innerSn'] +"</td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ (item['updateName'] || item['createName']) + " " + (new Date(item['updateDate'] || item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" +
                        "       <span data-type=\"changeFm\" class=\"btn ty-color-blue\">更换配方</span>" +
                        "       <span data-type=\"changeFmLog\" class=\"btn ty-color-blue\">配方更换记录</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon6 tbody:eq(1)").append(str);
            setPage($("#yeCon6") , cur , totalPage, "fmBindingPdList", "");
        }
    })
}
// creator: 李玉婷，2020-08-10 11:15:05，
function getChangeRecord(cur) {
    var info = JSON.parse($(".mainCon7 tbody:eq(0)").children("tr:eq(1)").children(":last").find(".hd").html());
    var id = info['product'];
    $.ajax({
        "url":"../formula/getFormulaListByPdBase.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "id": id },
        success:function (res) {
            var totalPage = res['totalPage'];      //

            $(".mainCon7 tbody:eq(1)").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ (item['majorIngredient'] || 0) +"种</td>" +
                        "    <td>"+ (item['minorIngredient'] || 0) +"种</td>" +
                        "    <td>"+ (item['updateName'] || item['createName']) + " " + (new Date(item['updateDate'] || item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" + charge("operation", item['operation'])+ "</td>" +
                        "</tr>";
                }
            }
            $(".mainCon7 tbody:eq(1)").append(str);
            setPage($("#yeCon7") , cur , totalPage, "fmUpdateRecord", "");
        }
    })
}
// creator: 李玉婷，2020-08-10 13:18:35，变更材料
function charge(type , val) {
    var str = "";
    switch (type){
        case "operation": //1选的 3变更
            if (val == 1){
                str = "选定配方";
            }else if(val == 3){
                str = "更换配方";
            }
            break;
        case 'phrase':
            if (val == 1){
                str = "开发中";
            }else if(val == 2){
                str = "开发完成";
            }
            break
    }
    return str;
}
// creator: 李玉婷，2020-08-10 14:07:21，曾使用过该材配方产品及零件
function getFmBeforeBindingPdList(cur){
    var id = $(".mainCon8 tbody:eq(0)").data('mfID');
    $.ajax({
        "url":"../formula/getBeforeBindingPdList.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "id": id },
        success:function (res) {
            var totalRows = res['totalRows'];      //X种“由所购买的单一材料直接加工而成”的产品或零件有待选择材料
            var totalPage = res['totalPage'];      //

            $(".mainCon8 .totalRows").html(totalRows);
            $(".mainCon8 tbody:eq(1)").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['innerSn'] +"</td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ (item['updateName'] || item['createName']) + " " + (new Date(item['updateDate'] || item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" +
                        "       <span data-type=\"changeFmLog\" class=\"btn ty-color-blue\">配方更换记录</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon8 tbody:eq(1)").append(str);
            setPage($("#yeCon8") , cur , totalPage, "fmBeforeBindingPdList", "");
        }
    })
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.parents("table").siblings(".textMax").text(curLength + '/' + max);
}

// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectID) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    if(selectID == item['id']){
                        str += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        str += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }
                }
            }
            obj.html(str);
        }
    })
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit() {
    bounce_Fixed3.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed3.cancel();
                getUnitList($("#unitSelect"), module, "");
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed3.show($("#tip1"));
            }
        }
    })
}

// creator: hxz，2020-09-02 14:51:41   计量单位设置
function unitAssign(obj) {
    var unitName = obj.children("option:selected").html();
    obj.siblings("input").val(unitName)
}
// creator: 李玉婷，2020-09-10 11:59:43，一键清空
function clearPre(obj) {
    obj.prev("input").val("");
}






