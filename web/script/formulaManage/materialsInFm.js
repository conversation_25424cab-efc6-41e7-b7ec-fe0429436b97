var pageSize = 20 ; // 分页的每页条数
$(function () {
    $("input[name='mainConShow']").val("1");
    //获取主页数据
    getMtListForFormula(1, '');
    $(".ty-btn").click(function(){
        var type = $(this).data('type');
        switch (type){
            case "goMain": // 返回材料首页
                goMainCon(1);
                var cur = $("#yeCon1 .yecur").html();
                var key = $(".mainCon1 .search input").val();
                getMtListForFormula(cur, key);
                break;
            case "goback2": // 返回上一页
                goMainCon(3);
                break;
        }
    });
    $(".btnCat").click(function(){
        var type = $(this).data('type');
        switch (type){
            case "goStopMt": // 去查看已停用材料
                goMainCon(3);
                getStopMtForFormula(1);
                break;
            case "searchBtn": // 搜索材料
                var key = $(".mainCon1 .search input").val();
                getMtListForFormula(1, key);
                break;
            case "searchBtnStop": // 已停用 - 搜索材料
                var key = $(".mainCon3 .search input").val();
                getStopMtForFormula(1, key);
                break;
        }
    });
    $("tbody").on("click" , "span.btn" , function () {
        var type = $(this).data("type") ;
        switch (type){
            case "mtScan": // 查看
                bounce.show($("#mtScan"));
                var info = JSON.parse($(this).siblings(".hd").html());
                for(var key in info){
                    if(key == 'create_name' || key == 'create_date' ){

                    }else{
                        $("#mtScan .scanMt_" + key).html(info[key]);
                    }
                }
                $("#mtScan .scanMt_create").html(info['createName'] + " " + (new Date(info['createDate']).format('yyyy-MM-dd hh:mm:ss')));
                $("#editMtBtn").data("info", JSON.stringify(info));
                break;
            case "mtScanStop": // 停用 - 查看
                bounce.show($("#mtScanStop"));
                var info = JSON.parse($(this).siblings(".hd").html());
                for(var key in info){
                    if(key == 'create_name' || key == 'create_date' ){

                    }else{
                        $("#mtScanStop .scanMt_" + key).html(info[key]);
                    }
                }
                $("#mtScanStop .scanMt_create").html(info['createName'] + " " + (new Date(info['createDate']).format('yyyy-MM-dd hh:mm:ss')));
                $("#stopIntro").html(info['updateName'] + "已于" + (new Date(info['updateDate']).format('yyyy-MM-dd hh:mm:ss')) + "将本材料“停用”！");
                break;
            case "mtStop": // 暂停使用
                var info = JSON.parse($(this).siblings(".hd").html());
                bounce.show($("#stopTip"));
                $("#stopTip .msg").html("<p>操作成功后，该材料在系统中将无法被选择。</p><p>确定停用该材料？</p>");
                $("#stopTip").data("mtID", info.id);
                $("#stopTip").data("state", 0);
                break;
            case "mtStart": // 恢复使用
                var info = JSON.parse($(this).siblings(".hd").html());
                bounce.show($("#stopTip"));
                $("#stopTip .msg").html("<p>操作成功后，再下采购订单时将可选择该材料。</p><p>确定恢复使用该材料？</p>");
                $("#stopTip").data("mtID", info.id);
                $("#stopTip").data("state", 1);
                break;
            case "mtUesdGs":  // 已停用的材料 - 曾使用过该材料的配方
                $("#goback2").show();
                goMainCon(2);
                var item = JSON.parse($(this).parent().next().children(".hd").html());
                var str =  "    <td>"+ item['name'] +"</td>" +
                    "    <td>"+ item['code'] +"</td>" +
                    "    <td>"+ item['model'] +"</td>" +
                    "    <td>"+ item['specifications'] +"</td>" +
                    "    <td>"+ item['unit'] +"</td>" +
                    "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                $(".mainCon2 tbody:eq(0)").children("tr:eq(1)").html(str);
                $(".mainCon2 tbody:eq(0)").data('mtID', item['id']);
                getBeforeBdFormulaList(1);
                break;
            case "mtUsingGs":  // 使用该材料的产品及零件
                $("#goback2").hide();
                goMainCon(2);
                var item = JSON.parse($(this).parent().next().children(".hd").html());
                var str =  "    <td>"+ item['name'] +"</td>" +
                    "    <td>"+ item['code'] +"</td>" +
                    "    <td>"+ item['model'] +"</td>" +
                    "    <td>"+ item['specifications'] +"</td>" +
                    "    <td>"+ item['unit'] +"</td>" +
                    "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                $(".mainCon2 tbody:eq(0)").children("tr:eq(1)").html(str);
                $(".mainCon2 tbody:eq(0)").data('mtID', item['id']);
                getBdFormulaList(1);
                break;
        }
    })
    $(".terminateOrders").on('click', ".fa", function () {
        $(this).attr("class", "fa fa-dot-circle-o").siblings().attr("class","fa fa-circle-o");
        var num = $(this).data("num");
        $("input[name='terminateOrders']").val(num);
    });
    $("#editMtBtn").click(function () {
        editMtBtn();
    })
    // 材料录入 选中
    $("#selectMt").on('click', 'option', function(){
        setSelectOption($(this));
        $("#selectMt").hide();
        $(".bounce").stopTime("mtMatch")
    });
});
// creator: 李玉婷，2020-08-11 14:34:21，获取材料列表
function getMtListForFormula(cur, keyword) {
    $.ajax({
        "url":"../formula/getMtListForFormula.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "keyword":keyword },
        success:function (res) {
            var stopNum   = res['stopNum']      //已停用的材料有X种
            var totalRows = res['totalRows']      //如下X种材料与产品或零件有关
            var totalPage = res['totalPage']      //总页数
            $(".mainCon1 .stopNum").html(stopNum);
            $(".mainCon1 .totalRows").html(totalRows);
            $(".mainCon1 tbody").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td class=\"ty-td-control\">"+ item['num'] +"种<span data-type=\"mtUsingGs\" class=\"btn ty-color-blue \">去查看</span></td>" +
                        "    <td>" +
                        "        <span data-type=\"mtScan\" class=\"btn ty-color-blue \">查看</span>" +
                        "        <span data-type=\"mtStop\" class=\"btn ty-color-blue \">停用</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon1 tbody").append(str);
            var json = JSON.stringify({"keyword": keyword}) ;
            setPage($("#yeCon1") , cur , totalPage, "mtForFormula", json);
        }
    })
}
//creator: 李玉婷，2020-08-11 15:14:12， 获取已停用材料列表
function getStopMtForFormula(cur, keyword){
    $.ajax({
        "url":"../formula/getStopMtBaseListForFormula.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "keyword": keyword },
        success:function (res) {
            var totalRows = res['totalRows'];      //X
            var totalPage = res['totalPage'];      //

            $(".mainCon3 .totalRows").html(totalRows);
            $(".mainCon3 tbody").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ item['updateName'] + " " + (new Date(item['updateDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td class=\"ty-td-control\">"+ item['num'] +"种<span data-type=\"mtUesdGs\" class=\"btn ty-color-blue \">去查看</span></td>" +
                        "    <td>" +
                        "       <span data-type=\"mtScanStop\" class=\"btn ty-color-blue \">查看</span>\n" +
                        "        <span data-type=\"mtStart\" class=\"btn ty-color-blue \">恢复使用</span>\n" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon3 tbody").append(str);
            var json = JSON.stringify({"keyword": keyword}) ;
            setPage($("#yeCon3") , cur , totalPage, "stopMtForFormula", json);
        }
    })
}
// creator: 李玉婷，2020-08-11 21:40:15，使用该材料的配方 - 去查看
function getBdFormulaList(cur) {
    var id = $(".mainCon2 tbody:eq(0)").data('mtID');
    $.ajax({
        "url":"../formula/getBindingFormulaList.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "id":id },
        success:function (res) {
            var totalRows = res['totalRows'];      //
            var totalPage = res['totalPage'];      //

            $(".mainCon2 .usedNum").html(totalRows);
            $(".mainCon2 tbody:eq(1)").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['majorIngredient'] +"</td>" +
                        "    <td>"+ item['minorIngredient'] +"</td>" +
                        "    <td>"+ (item['updateName'] || item['createName']) + " " + (new Date(item['updateDate'] || item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" +
                        "       <span class=\"ty-color-gray\">修改记录</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon2 tbody:eq(1)").append(str);
            setPage($("#yeCon2") , cur , totalPage, "mtBdFormula", "");
        }
    })
}
// creator: 李玉婷，2020-08-11 16:36:51，
function getBeforeBdFormulaList(cur) {
    var id = $(".mainCon2 tbody:eq(0)").data('mtID');
    $.ajax({
        "url":"../formula/getBeforeBindingFormulaList.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "id":id },
        success:function (res) {
            var totalRows = res['totalRows'];      //
            var totalPage = res['totalPage'];      //

            $(".mainCon2 .usedNum").html(totalRows);
            $(".mainCon2 tbody:eq(1)").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['majorIngredient'] +"</td>" +
                        "    <td>"+ item['minorIngredient'] +"</td>" +
                        "    <td>"+ (item['updateName'] || item['createName']) + " " + (new Date(item['updateDate'] || item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" +
                        "       <span class=\"ty-color-gray\">修改记录</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon2 tbody:eq(1)").append(str);
            setPage($("#yeCon2") , cur , totalPage, "mtBeforeBdFormula", "");
        }
    })
}
// creator: 李玉婷，2020-08-12 10:43:20， 修改材料的基本信息
function editMtBtn() {
    bounce.show($("#mtEntry"));
    $("#mtEntry .bonceHead span").html("材料修改");
    $("#mtEntry input[name='name']").attr("onclick","");
    $("#mtEntry input[name='terminateOrders']").val("");
    $("#mtEntry .terminateOrders .fa").addClass("fa-circle-o").removeClass("fa-circle");
    var info = JSON.parse($("#editMtBtn").data("info"));
    getUnitList($("#unitSelect"), 6, info['unit']);
    for(var key in info){
        if(key == 'create_name' || key == 'create_date' ){

        }else{
            //$(".scanMt_" + key).html(info[key]);
            $("#mtEntry [name='"+ key +"']").val(info[key]);
        }
    }
    $.ajax({
        "url":"../productMaterial/getOrderNum.do",
        "data" :{ "id": info['id'] },
        success:function (res) {
            var orderNum = res['data'];
            $("#mtEntry .orderNum").html(orderNum);
        }
    })
}
// creater :hxz 2020-06-05 切换页面
function goMainCon(num) {
    $("input[name='mainConShow']").val(num);
    $(".mainCon" + num).show().siblings().hide();
}
// creater :hxz 2020-06-05 停启用材料
function mtStopOk() {
    var mtID = $("#stopTip").data("mtID");
    var state = $("#stopTip").data("state");
    $.ajax({
        "url":"../productMaterial/startStopMt.do",
        "data":{  "id": mtID , "state":state },
        success:function (res) {
            bounce.cancel();

            if(res['code'] == 200){
                layer.msg("操作成功");
                if(state == 0){ // 停用的
                    var curPage = $("#yeCon1 .yecur").html();
                    var key = $(".mainCon1 .search input").val();
                    getMtListForFormula(curPage, key);
                }else{
                    var key = $(".mainCon3 .search input").val();
                    var curPage = $("#yeCon3 .yecur").html();
                    getStopMtForFormula(curPage, key );
                }
            }else{
                var str =  "操作失败，因为还有使用该材料的产品、零件或配方！" ;
                $("#iknow .msg").html(str);
                bounce.show( $("#iknow"))
            }
        }
    })
}
// creator: 李玉婷，2020-08-11 16:46:54，材料修改
function mtEditOk() {
    var data = {};
    $("#mtEntry [need]").each(function(){
        var key = $(this).attr("name");
        data[key] = $(this).val();
    });
    if(!data['name']){ layer.msg("材料名称为必填项！"); return false; }
    if(!data['code']){ layer.msg("材料代号为必填项！"); return false; }
    if(!data['unitId']){ layer.msg("计量单位为必填项！"); return false; }
    data['unit'] = $("#unitSelect option:selected").html();
    var url = "../productMaterial/updateMaterial.do" ;
    if (!data['terminateOrders']){ layer.msg("请确认采购人员需要终止未完结的采购订单！"); return false; }
    $.ajax({
        "url": url ,
        "data":data,
        success:function (res) {
            bounce.cancel();
            if(res['code'] == 200){
                layer.msg("操作成功");
                var cur = $(".mainCon1 .yecur").html();
                var key = $(".mainCon1 .search input").val();
                getMtListForFormula(cur,key)
            }else{
                layer.msg(res['msg']);
            }
        }
    })
}

// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectID) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    if(selectID == item['name']){
                        str += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        str += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }
                }
            }
            obj.html(str);
        }
    })
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit() {
    bounce_Fixed.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed.cancel();
                getUnitList($("#unitSelect"), module);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed.show($("#tip1"));
            }
        }
    })
}

// creator: hxz，2020-09-02 14:51:41   计量单位设置
function unitAssign(obj) {
    var unitName = obj.children("option:selected").html();
    obj.siblings("input").val(unitName)
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.parent().siblings(".textMax").text( curLength +'/' + max );
    obj.siblings(".textMax").text( curLength +'/' + max );
}








