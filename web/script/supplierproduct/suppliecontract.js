var user = null;
var contractDel = null;
var contractInfo = null;
var editContractObj = null;
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
var bounce_Fixed4 = new Bounce(".bounce_Fixed4");
$(function(){
    getContractRemind();
    $("#tipcontractGoods").on("change", '.ty-checkbox input', function () {
        var bounceCon = $(this).parents(".bonceCon")
        var length = bounceCon.find('input:checked').length
        bounceCon.find(".count").html(length)
    })
    $(".bounce").on("click",".ty-btn,.edit,[type='btn']", function (){
        var name = $(this).data('name');
        var source = $(this).data('source');
        switch (name){
            case 'conreceiveInfo'://联系人新增
                var type = $(this).data('type');
                var id = $(this).parents("tr").data("id");
                $("#newContectInfo").data("type",type);
                $("#newContectInfo").data("id",id);
                $("#newContectInfo").data("source",source);
                //console.log('4.23-8:36',source);
                $("#newContectInfo").data("level",1);
                if(type == "delete"){
                    var milecData = $("#addAccount").data("conrecfeiveInfo");
                    milecData = JSON.parse(milecData);
                    var index = milecData.findIndex(value=>value.number === id || value.id === id);
                    milecData.splice(index,1);
                    var html = setContactList(milecData);
                    $(".contectList").html(html);
                }else{
                    $("#uploadCard").html("");
                    initCardUpload($("#uploadCard"));
                    document.getElementById('newContectData').reset();
                    $(".otherContact").html("");
                    $("#contactFlag").html("其他");
                    $("#newContectInfo .bonceHead span").html('联系人')
                    $("#contactsCard .bussnessCard").remove();
                    if(type == 'new'){
                        $('#uploadCard').show();
                        $("#addMoreContact").hide();
                    }else if(type == 'update'){
                        var getData = $("#addAccount").data('conreceiveInfo');
                        getData = JSON.parse(getData);
                        var flag = getData.find(value=>value.id === id || value.number === id);
                        if(flag){
                            flag.socialList = JSON.parse(flag.socialList);
                            setContact(flag);
                        }
                    }
                    $("#addMoreContact").hide();
                    bounce_Fixed2.show($("#newContectInfo"));
                    setTimer('updateContact');
                }
                break;
            case 'mailInfom'://新增联系人（常规修改）
                var type = $(this).data('type');
                var milId = $("#updateSupPanel").data("id");
                $("#newContectInfo").data("type",type);
                $("#newContectInfo").data("source",source);
                if(type == 'new'){
                    $("#newContectInfo .bonceHead span").html("新增联系人");
                    $("#contactFlag").html("其它");
                    if($(".contactItem").length >= 50){
                        layer.msg('最多可录入50条联系人。');
                        break;
                    }
                    $("#newContectInfo").data("id",milId);
                    $("#addMoreContact").hide();
                }else if(type == 'update'){
                    $("#newContectInfo .bonceHead span").html("修改联系人");
                    var milucId = $(this).parents("tr").data("id");
                    $("#newContectInfo").data("id",milucId);
                }

                break;
        }

    });
    // 客户修改弹窗按钮交互
    $(".bounce").on('click','.node', function(){
        var name = $(this).data('name');
        var type = $(this).data('type');
        var source = $(this).data('source');
        let info = ``
        switch (name) {
            case 'contractDel': // 客户查看 - 合同 删除合同
                contractDel = $(this) ;
                bounce_Fixed.show( $("#contractDelTip") )

                break;
            case 'scanGs': // 客户查看 - 合同 本合同下的材料
                var cid = $(this).parents(".contractItem").data("id") ;
                cScanScanGs(cid);
                break;
            case 'cScan': // 已到期的查看
                var contractId = $(this).parents(".contractItem").data("id") ;
                contractBaseScan(contractId);
                break;
            case 'contractStopData': // 已暂停/终止的合同
                var id = $("#updateSupPanel").data("id")
                contractStopData(id)
                break;
            case 'contractEndData': // 已到期的合同
                var id = $("#updateSupPanel").data("id")
                contractEndData(id)
                break;
            case 'cEnd': // 暂停履约/合同终止
                let cInfo = JSON.parse($(this).siblings(".hd").html())
                if (Number(cInfo.type) === 2) {
                    layer.msg("<p>在此不可对服务合同进行编辑！</p><p>如需要，请到“服务合同”模块中操作。</p>")
                    return false;
                }
                var cid = $(this).parents(".contractItem").data("id") ;
                $("#cEndTip").data("cid",cid).data("enabled", 0)
                $("#tipsc").html("<p>点击“确定”后，本合同将进入“已暂停/终止的合同”。</p> " +
                    "<p>确定进行本操作吗？</p>")
                bounce_Fixed3.show($("#cEndTip"));
                break;
            case 'contractInfo': // 包含了新增和修改客户的
                contractInfoEdit(type, source, $(this));
                break;
            case 'contractChangeLog': // 包含了新增和修改客户的
                info = JSON.parse($(this).siblings(".hd").html())
                getContractChangeLog(info.id)
                break;
            case 'contractRenewLog':
                info = JSON.parse($(this).siblings(".hd").html())
                getContractRenewLog(info.primaryCont)
                break
            // 新增客户弹窗 - 录入完毕，勾选模块 - 按钮 （此操作会新增客户然后打开勾选模块弹窗）
            case 'selectModule':
                if (!testMobile($.trim($("#addAccount input[name='supervisorMobile']").val()))) {
                    layer.msg("请输入正确的手机号")
                    return false
                }
                var shList =[];
                var imgsQ = [], imgsP = [] ;
                var data = {
                    qImages: '',
                    pImages: '',
                    shAddressList: '',
                    fpAddressList: '',
                    contactsList: ''
                };
                $('#addpayDetails input[type="text"]').each(function(){
                    var name = $(this).attr('name');
                    data[name] = $(this).val();
                })
                var buyCase = $("#addpayDetails input[name='buyCase']:checked").val() == 1;
                if(buyCase){
                    var initialType = $("#addpayDetails input[name='firstTime']:checked").val();
                    if(initialType == undefined){
                        layer.msg('请选择首次购买时间');
                        return false;
                    }else{
                        data['initialType'] = initialType;
                        var date = '', month = '';
                        var year = new Date().getFullYear();
                        if(initialType == 1){
                            month = $("#addpayDetails #buyMonth").val();
                            if(month == "") {
                                layer.msg('请选择月份');
                                return false;
                            }else{
                                var arr = month.split('月');
                                if(arr[0] < 10){
                                    date = year + '0' + arr[0];
                                }else{
                                    date = year + arr[0];
                                }
                            }
                        }else if(initialType == 2){
                            year = Number(year) - 1;
                            date = year + '01';
                        }else if(initialType == 3){
                            year = Number(year) - 2;
                            date = year + '01';
                        }
                        data['initialPeriod'] = date;
                    }
                }
                $("#qImages .imgsthumb").each(function () {
                    var path = {
                        'normal': $(this).find(".filePic").data('path')
                    };
                    imgsQ.push(path);
                })
                $("#pImages .imgsthumb").each(function () {
                    var path = {
                        'normal': $(this).find(".filePic").data('path')
                    };
                    imgsP.push(path);
                })
                imgsQ = JSON.stringify(imgsQ);
                imgsP = JSON.stringify(imgsP);
                data.qImages = imgsQ;
                data.pImages = imgsP;
                if($("#firstContactTime").val() != ''){
                    data.firstContactTime = $("#firstContactTime").val();
                }
                if($("#addpayDetails .receiveList tbody tr").length>0){
                    $("#addpayDetails .receiveList tbody tr").each(function () {
                        var json = {
                            'address': $(this).children().eq(1).html(),
                            'contact': $(this).children().eq(2).html(),
                            'mobile': $(this).children().eq(3).html()
                        }
                        shList.push(json);
                    })
                    shList = JSON.stringify(shList);
                    data.shAddressList= shList;
                }
                data.fpAddressList= $("#addAccount").data('mailInfo');
                data.contactsList= $("#addAccount").data('contactInfo');
                $.ajax({
                    url: '../special/getSaleCtrlsByMobile.do',
                    data: {mobile: data.supervisorMobile},
                    success: function (res) {
                        var state = res.data
                        if (state === 2) {
                            $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                            bounce_Fixed.show($("#tip"))
                            loading.close()
                        } else if (state === 1) {
                            $.ajax({
                                url: "../sales/addPdCustomer.do",
                                data: data,
                                success: function (res) {
                                    var status = res.status
                                    if (status === 1) {
                                        layer.msg("新增成功");
                                        var key = $(".main #se0").val();
                                        getCustomerMes(1 , 20, key);

                                        $("#newReceiveAddressInfo").data('id', '');
                                        $("#newReceiveAddressInfo").data('type', '');
                                        $("#newReceiveAddressInfo").data('source', '');
                                        $("#newMailInfo").data('type', '');
                                        $("#newMailInfo").data('id', '');
                                        $("#newMailInfo").data('source', '');
                                        $("#newContectInfo").data('type', '');
                                        $("#newContectInfo").data('id', '');
                                        $("#newContectInfo").data('source', '');

                                        $(".orgManage").data("cusInfo", res)
                                        $("#newCusOrg select, #newCusOrg input").val("").prop("disabled", false)
                                        $("#newCusOrg input[name='supervisorName']").val(res.supervisorName)
                                        $("#newCusOrg input[name='supervisorMobile']").val(res.supervisorMobile)
                                        $("#newCusOrg input[name='fullName']").val(res.fullName)
                                        $("#newCusOrg input[name='name']").val(res.name)

                                        setEveryTime(bounce_Fixed, 'newCusOrg')
                                        getProductOption().then(str => {
                                            $("#newCusOrg .productSelect").html(str)
                                            bounce_Fixed.show($("#newCusOrg"))
                                        })
                                    } else {
                                        $("#tip .tipMs").html("操作失败！");
                                        bounce_Fixed.show($("#tip"));
                                    }
                                }
                            })
                        } else {
                            layer.msg("系统错误！")
                        }
                    },
                    complete: function () {

                    }
                })
                break;
            case 'contactInfo':
                var id = $(this).parents("tr").data('id');
                var type = $("#newContectInfo").data('type');
                $("#newContectInfo").data('id', id);
                var source = $("#newContectInfo").data("source");
                $("#newContectInfo").data('level',1);
                if(type == 'delete') {
                    var contactData = $("#addAccount").data('contactInfo');
                    contactData = JSON.parse(contactData);
                    var index = contactData.findIndex(value=>value.number === id || value.id === id);
                    contactData.splice(index, 1);//进行数据删除
                    var html =setContactList(contactData);
                    $(".contectList").html(html);
                }else{
                    $("#uploadCard").html("")
                    initCardUpload($("#uploadCard"));
                    document.getElementById('newContectData').reset();
                    $(".otherContact").html("");
                    $("#contactFlag").html("其他");
                    $("#newContectInfo .bonceHead span").html('联系人')
                    $("#contactsCard .bussnessCard").remove();
                    if(type == 'new'){
                        $('#uploadCard').show();
                        $("#addMoreContact").hide();
                    }else if(type == 'update'){
                        var getData = $("#addAccount").data('contactInfo');
                        getData = JSON.parse(getData);
                        var flag = getData.find(value=>value.id === id || value.number === id);
                        if(flag){
                            flag.socialList = JSON.parse(flag.socialList);
                            setContact(flag);
                        }
                    }
                    $("#addMoreContact").hide();
                    bounce_Fixed2.show($("#newContectInfo"));
                    setTimer('updateContact');
                }
                break;
            case 'mailInfo':
                var id = $(this).parents("tr").data('id');
                var type = $("#newMailInfo").data("type");
                $("#newMailInfo").data('type',type);
                $("#newMailInfo").data('id', id);
                $("#newMailInfo").data('source', source);
                if(type == 'delete') {
                    var mailData = $("#addAccount").data('mailInfo');
                    mailData = JSON.parse(mailData);
                    var index = mailData.findIndex(value=>value.customerContract === id);//获取索引值
                    mailData.splice(index, 1);//进行数据的删除
                    setMailList(mailData);
                }else{
                    if(type == 'new'){
                        $("#newMailInfo input").val("");
                    }else if(type == 'update'){
                        // $("#mailAddress").val($(this).parents("tr").find("td").eq(1).html());
                        $("#mailName").val($(this).parents("tr").find("td").eq(1).html());
                        // $("#mailNumber").val($(this).parents("tr").find("td").eq(3).html());
                        $("#mailContact").val($(this).parents("tr").find("td").eq(3).html());

                    }
                    bounce_Fixed2.show($("#newMailInfo"));
                    setTimer('updateMail');
                }
                break;
        }
    })
    $(".bounce_Fixed").on("click",".ty-btn,.edit,[type='btn']", function(){
        var name = $(this).data('name');
        let info = ``;
        switch (name){
            case 'spupdateInvoice'://开票信息修改
                suptendInvoice(type,source,$(this));
                break;
            case 'deleteData'://已被删除的数据
                $("#deletepo tbody").children(":gt(0)").remove();
                $("#deletepo .bonceHead span").html("已被删除的联系人");
                $.ajax({
                    url:"../supplier/getContactsList.do",
                    data:{
                        id:$("#updateSupPanel").data("id")
                    },
                    success:function(res){
                        var state = res.success;
                        // if(state === "1" || state === 1){
                        //     layer.msg("查询成功");
                        // }
                        let lins = res['data'] || [],status = res['status'],str='';
                        bounce_Fixed2.show($("#deletepeo"));
                    }
                })
                break;
            case 'contractyEndData'://已被停用的数据
                var sendData = {
                    "sondId":$("#updateSupPanel").data("id"),
                    "type":type
                }
                $("#stopsendms tbody").children(":gt(0)").remove();
                $("#stopsendms .bonceHead span").html("已被停用的邮寄信息");
                $.ajax({
                    url:"",
                    data:sendData,
                    success:function(res){
                        var link = res['addressList']|| [], status = res['status'],str='';
                        bounce_Fixed2.show($("#stopsendms"));
                        let ttltype = fpAddressRecords;
                        for(let i in link){
                            let item = link[i];
                            str += `<tr data-id="${item.id}" data-isstop="ok">
                                        <td>${item.contact}<span class="ty-right">${item.mobile}</span></td>
                                        <td>
                                            <span class="edit2 ty-color-red" data-id="${item.id}" data-name="enableTurn" data-val="1" onclick="beganup($(this))">启用</span>
                                            <span class="ty-color-blue jilu" data-type="${ttltype}" onclick="yjAddressUpdateLogu($(this))">修改记录</span>
                                            <span class="hd">${item.id}</span>
                                        </td>
                                    </tr>`;
                        }
                        $("#stopsendms tbody").append(str);
                    }
                })
                break;
            case 'maisure'://提示弹窗确定按钮
                maisure();
                break;
            case 'spcontStop1'://暂停履约/终止合同
                // var spid = $(this).parents(".contractItem").data("id");
                // $("#tiphoto").data("spid",spid).data("enabled",0);
                // $("#prompttext").html("<span>确定后，相关材料将不再属于本合同。</span>" +
                //     "                    <span>确定进行本操作吗？</span>");
                bounce_Fixed.show($("#tiphoto"));
                break;
            case 'conreceiveInfo'://新增联系人（新增供应商）
                var id = $(this).parents("tr").data("id");
                $("#newContectInfo").data("type",type);
                $("#newContectInfo").data("id",id);
                $("#newContectInfo").data("source","addCustomer");
                $("#newContectInfo").data("level",1);
                if(type == "delete"){
                    var milecData = $("#addAccount").data("conreceiveInfo");
                    milecData = JSON.parse(milecData);
                    var index = milecData.findIndex(value=>value.number === id || value.id === id);
                    milecData.splice(index,1);
                    var html = setContactList(milecData);
                    $(".contectList").html(html);
                }else{
                    $("#uploadCard").html("");
                    initCardUpload($("#uploadCard"));
                    document.getElementById('newContectData').reset();
                    $(".otherContact").html("");
                    $("#contactFlag").html("其他");
                    $("#newContectInfo .bonceHead span").html('联系人')
                    $("#contactsCard .bussnessCard").remove();
                    if(type == 'new'){
                        $('#uploadCard').show();
                        $("#addMoreContact").hide();
                    }else if(type == 'update'){
                        var getData = $("#addAccount").data('conreceiveInfo');
                        getData = JSON.parse(getData);
                        var flag = getData.find(value=>value.id === id || value.number === id);
                        if(flag){
                            flag.socialList = JSON.parse(flag.socialList);
                            setContact(flag);
                        }
                    }
                    $("#addMoreContact").hide();
                    bounce_Fixed2.show($("#newContectInfo"));
                    setTimer('updateContact');
                }
                break;
            case 'addMailm'://邮寄信息修改
                var type = $("#newMailInfo").data('type');
                var source = $("#newMailInfo").data("source");
                if(source == "addCustomer"){
                    var mailData = $("#addAccount").data("receiveInfo");
                    mailData = JSON.parse(mailData);
                    console.log(mailData);
                    if(type == "new"){
                        let contractInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var data ={
                            'customerContract':Math.random(),
                            'address':$("#mailAddress").val(),
                            'contact':contractInfo.name,
                            'postcode':$("#mailNumber").val(),
                            'mobile':contractInfo.mobile,
                            'contactInfoNumber':contractInfo.number,
                        }
                        mailData.push(data);
                    }else if(type =="update"){
                        var id= $("#newMailInfo").data("id");
                        var index = mailData.findIndex(value=>value.number === id);
                        let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var data = {
                            "number":id,
                            "address":$("#newMailInfo #mailAddress").val(),
                            "contact":contactInfo.name,
                            "postcode":$("#newMailInfo #mailNumber").val(),
                            "mobile":contactInfo.mobile,
                            "contactInfoNumber":contactInfo.number
                        }
                        mailData[index] = data;
                    }
                    setMailListm(mailData);
                }
                break;
            case 'mailInfom'://新增邮寄信息
                var id =$(this).parents("tr").data("id");
                $("#newMailInfo").data('type',type);
                $("#newMailInfo").data('id',id);
                $("#newMailInfo").data('source',source);
                if(type == 'delete'){
                    var mailData = $("#addAccount").data('receiveInfo');
                    mailData = JSON.parse(mailData);
                    var index = mailData.findIndex(value=>value.number === id);
                    mailData.splice(index,1);
                    setMailList(mailData);
                }else{
                    if(type == 'new'){
                        $("#newMailInfo input").val("");
                    }else if(type == 'update'){
                        $("#mailAddress").val($(this).parents("tr").find("td").eq(1).html());
                        $("#mailNumber").val($(this).parents("tr").find("td").eq(2).html());
                        $("#mailName").val($(this).parents("tr").find("td").eq(3).html());
                    }
                }
                break;
            case 'addContactxg'://修改
                var level = $("#newContectInfo").data('level');
                var type = $("#newContectInfo").data('type');
                var source = $("#newContectInfo").data('source');
                if(source == 'addCustomer') {
                    var contactInfo = $("#addAccount").data('contactInfo');
                    contactInfo = JSON.parse(contactInfo);
                    var data = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $(" #contactNumber").val(),
                        'visitCard': '',
                        'socialList': '[]'
                    }
                    if ($("#contactsCard .bussnessCard").length > 0) {
                        data.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if ($(".otherContact li").length > 0) {
                        var arr = []
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json = {
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        data.socialList = arr;
                    }
                    var groupUuid = $("#contactsCard").attr("groupUuid")
                    if (type == 'new') {
                        data.id = Math.random();
                        data.groupUuid = groupUuid;
                        contactInfo.push(data);
                        addCusContact.push(data);

                    } else if (type == 'update') {
                        var id = $("#newContectInfo").data('id');
                        var index = contactInfo.findIndex(value => value.id === id);
                        data.number = id;
                        data.groupUuid = groupUuid;
                        contactInfo[index] = data;
                        addCusContact.forEach(function (item, index) {
                            if (data.number == item.number) {
                                addCusContact[index] = data;
                                $(".mailList tbody tr").each(function () {
                                    let thisContactID = $(this).find(".hd").html()
                                    if (thisContactID == data.number) {
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(4)").html(data.mobile);
                                    }
                                })
                                $(".receiveList tbody tr").each(function () {
                                    let thisContactID = $(this).find(".hd").html()
                                    if (thisContactID == data.number) {
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(3)").html(data.mobile);
                                    }
                                })
                            }
                        })
                    }

                    var uxli = $(".node2 ").siblings(".hd").html();
                    uxli = JSON.parse(uxli);
                    var uxid = uxli.id;
                    var lixim ={
                        id:uxid,
                        socialList:'[]',
                        cardPath:'',
                        mobile:$("#contactNumber").val(),
                        post:$("#position").val(),
                        name:$("#contactName").val(),
                        tags:$("#contactFlag").val()
                    }
                    var soc = JSON.stringify(lixim.socialList);
                    var crd = JSON.stringify(lixim.cardPath);
                    var mob = JSON.stringify(lixim.mobile);
                    var po = JSON.stringify(lixim.post);
                    var na = JSON.stringify(lixim.name);
                    var tg = JSON.stringify(lixim.tags);
                    $.ajax({
                        url:"../supplier/updateContactsSocialAndCard.do",
                        data:{
                            contactId:lixim.id,
                            socialList:soc,
                            cardPath:crd,
                            mobile:mob,
                            post:po,
                            name:na,
                            tags:tg
                        },
                        success:function(res){
                            var state = res.success;
                            if(state === "1"|| state ===1){
                                // layer.msg("修改成功");
                                supplierInfoEdit(editObj);
                            }
                        }
                    })

                }
                break;
            case 'receiveInfo'://新增邮寄信息
                var id = $(this).parents("tr").data("id");
                $("#newMailInfo").data('type',type);
                $("#newMailInfo").data('id',id);
                $("#newMailInfo").data('source',source);
                if(type == 'delete'){
                    var mailData = $("#addAccount").data('receiveInfo');
                    mailData = JSON.parse(mailData);
                    var index = mailData.findIndex(value=>value.number === id);
                    mailData.splice(index,1);
                    setMailList(mailData);
                }else{
                    if(type == 'new'){
                        $("#newMailInfo input").val("");
                    }else if(type == 'update'){
                        $("#mailAddress").val($(this).parents("tr").find("td").eq(1).html());
                        $("#mailNumber").val($(this).parents("tr").find("td").eq(2).html());
                        $("#mailName").val($(this).parents("tr").find("td").eq(3).html());
                    }
                }
                break;
            case 'mailInfoi':
                var id = $(this).parents("tr").data('id');
                $("#newMailInfo").data("type",type);
                $("#newMailInfo").data("id",id);
                $("#newMailInfo").data("source",source);
                if(type == 'delete') {
                    var mailData = $("#addAccount").data('mailInfo');
                    mailData = JSON.parse(mailData);
                    var index = mailData.findIndex(value=>value.number === id);
                    mailData.splice(index, 1);
                    setMailList(mailData);
                }
                else{
                    if(type == 'new'){
                        $("#newMailInfo input").val("");
                    }else if(type == 'update'){
                        $("#mailAddress").val($(this).parents("tr").find("td").eq(1).html());
                        $("#mailName").val($(this).parents("tr").find("td").eq(2).html());
                        $("#mailNumber").val($(this).parents("tr").find("td").eq(3).html());
                        $("#mailContact").val($(this).parents("tr").find("td").eq(4).html());
                    }
                    bounce_Fixed2.show($("#newMailInfo"));
                    setTimer('updateMail');
                }
                break;
            case 'cScan': // 已到期的查看
                info = JSON.parse($(this).siblings(".hd").html())
                var cid  =  info.id;
                contractBaseScan(cid)
                break;
            case 'cRestart': // 恢复履约/重启合作
                info = JSON.parse($(this).siblings(".hd").html())
                $("#cEndTip").data("cid", info.id).data("enabled", 1)
                // 启用合同时判断合同的状态
                $.ajax({
                    url: $.webRoot + '/supplier/checkReStartPoContractState.do',
                    data: { id: info.id }
                }).then(res => {
                    let state = res.data.state // state 0-不要重复启用 1-没有过期直接恢复 2-已经过期了
                    let vaildEnd = res.data.vaildEnd // 到期日期
                    if (state === 0) {
                        layer.msg("请勿重复操作！")
                    } else if (state === 2) {
                        bounce_Fixed3.show($("#cEndTip2"));
                        $("#cEndTip2 input:radio").prop("checked", false)
                        $("#cEndTip2 .vaildEnd").html(moment(vaildEnd).format("YYYY-MM-DD"))
                    } else if (state === 1) {
                        bounce_Fixed3.show($("#cEndTip"));
                        $("#tipsc").html("<p>点击“确定”后，本合同将回到有效合同列表。</p> " +
                            " <p>确定进行本操作吗？</p>");
                    } else {
                        layer.msg("操作失败！")
                    }
                })
                break;
            case 'cRenewal': // 续约
                info = JSON.parse($(this).siblings(".hd").html())
                contractInfoEdit('cRenewalHistory', 'updateCustomer', $(this));
                break
            case 'editContractOk': // 编辑合同确定
                editContractOk(1)
                break;
            case 'seeContractChangeHisDetail': // 客户查看 - 合同 本合同下的商品
                let contractId = $(this).parents("tr").data("id") ;
                seeContractHisDetail(contractId, 'change');
                break;
            case 'seeContractRenewHisDetail': // 客户查看 - 合同 本合同下的商品
                let contractHisId = $(this).parents("tr").data("id") ;
                seeContractHisDetail(contractHisId, 'renew');
                break;
        }
    });
    $(".bounce_Fixed2").on("click",".ty-btn,.edit,[type='btn']", function(){
        var name = $(this).data('name');
        switch(name){
            case 'addContactm'://常规修改中新增邮寄信息中的新增联系人
                var level = $("#newContectInfo").data('level');
                var type = $("#newContectInfo").data('type');
                var id = $("#newContectInfo").data('id');
                var source = $("#newContectInfo").data('source');
                if(source == 'addCustomer') {
                    var contactInfo = $("#addAccount").data('contactInfo');
                    contactInfo = JSON.parse(contactInfo);
                    var data = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $(" #contactNumber").val(),
                        'cardPath': '',
                        'socialList': '[]'
                    }
                    if ($("#contactsCard .bussnessCard").length > 0) {
                        data.cardPath = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if ($(".otherContact li").length > 0) {
                        var arr = []
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json = {
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        data.socialList = arr;
                    }
                    var groupUuid = $("#contactsCard").attr("groupUuid")
                    if (type == 'new') {
                        data.id = Math.random();
                        data.groupUuid = groupUuid;
                        contactInfo.push(data);
                        addCusContact.push(data);

                    } else if (type == 'update') {
                        var id = $("#newContectInfo").data('id');
                        var index = contactInfo.findIndex(value => value.id === id);
                        data.number = id;
                        data.groupUuid = groupUuid;
                        contactInfo[index] = data;
                        addCusContact.forEach(function (item, index) {
                            if (data.number == item.number) {
                                addCusContact[index] = data;
                                $(".mailList tbody tr").each(function () {
                                    let thisContactID = $(this).find(".hd").html()
                                    if (thisContactID == data.number) {
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(4)").html(data.mobile);
                                    }
                                })
                                $(".receiveList tbody tr").each(function () {
                                    let thisContactID = $(this).find(".hd").html()
                                    if (thisContactID == data.number) {
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(3)").html(data.mobile);
                                    }
                                })
                            }
                        })
                    }
                    // getmiltruList(contactInfo);
                    if(level == 2){
                        var tags = $("#contactFlag").html();
                        if(tags == "收货人"){
                            bounce_Fixed.show($("#newReceiveInfo"));
                            $("#ReceiveName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else if(tags== "联系人"){
                            bounce_Fixed2.show($("#newMailInfo"));
                            $("#mailName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else { //  新增联系人
                            bounce_Fixed2.cancel($("#newContectInfo"));
                        }
                    }else{
                        bounce_Fixed2.cancel($("#newContectInfo"));
                    }


                    var ten = $("#mailName").siblings(".hd").html();
                    ten = JSON.parse(ten);
                    let udid = $("#updateSupPanel").data("id");
                    // var sc = JSON.stringify(ten.socialList);
                    // var vc = JSON.stringify(ten.visitCard);
                    // var md = JSON.stringify(ten.mobile);
                    // var ps = JSON.stringify(ten.post);
                    // var na = JSON.stringify(ten.name);
                    // var tg = JSON.stringify(ten.tags);
                    var name = $("#contactName").val();
                    var psont = $("#position").val();
                    // if(psont.length == 0){
                    //     layer.msg("请填写联系人职位");
                    //     return false;
                    // }else if(name.length == 0){
                    //     layer.msg("请填写联系人姓名");
                    //     return false;
                    // }
                    $.ajax({
                        url:"../supplier/addContact.do",
                        data:{//supplierId:供应商id  socialList:社交号  cardPath：名片地址  mobile：手机号
                            //post：职位  name：名称  tags：标签
                            supplierId:udid,
                            socialList:ten.socialList,
                            cardPath:ten.cardPath,
                            mobile:ten.mobile,
                            post:ten.post,
                            name:ten.name,
                            tags:ten.tags
                        },
                        success:function(res){
                            var state = res.success;
                            if(state === "1"|| state === 1){
                                // layer.msg("新增成功");
                                getmiltruList(contactInfo);
                                if(level == 2){
                                    var tags = $("#contactFlag").html();
                                    if(tags == "收货人"){
                                        bounce_Fixed.show($("#newReceiveInfo"));
                                        $("#ReceiveName")
                                            .val(data.name).data('orgData',data.name)
                                            .siblings(".hd").html(JSON.stringify(data)) ;
                                    }else if(tags== "联系人"){
                                        bounce_Fixed2.show($("#newMailInfo"));
                                        $("#mailName")
                                            .val(data.name).data('orgData',data.name)
                                            .siblings(".hd").html(JSON.stringify(data)) ;
                                    }else { //  新增联系人
                                        bounce_Fixed.cancel($("newContectInfo"));
                                    }
                                }else{
                                    bounce_Fixed2.cancel($("#newContectInfo"));
                                }
                            }
                        }
                    })

                }
                break;
            case 'addMailml': //邮寄信息新增
                var level = $("#newContectInfo").data('level');
                // $("#newContectInfo").data('type','new');
                var type = $("#newMailInfo").data('type');
                var id = $("#newContectInfo").data('id');
                // $("#newContectInfo").data("source","addCustomer");
                var source = $("#newMailInfo").data('source');
                if(source == 'addCustomer') {
                    var mailData = $("#addAccount").data("receiveInfo");
                    mailData = JSON.parse(mailData);
                    console.log(mailData);
                    let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                    if (type == "new") {
                        // let contractInfo = $("#mailName").data("contractInfo");
                        // contractInfo = JSON.parse(contractInfo);
                        let contractInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var data = {
                            'customerContract': Math.random(),
                            'address': $("#mailAddress").val(),
                            'contact': contractInfo.name,
                            'postcode': $("#mailNumber").val(),
                            'mobile': contractInfo.mobile,
                            'contactInfoNumber': contractInfo.number,
                        }
                        mailData.push(data);
                    } else if (type == "update") {
                        var id = $("#newMailInfo").data("id");
                        var index = mailData.findIndex(value => value.number === id);
                        var data = {
                            "number": id,
                            "address": $("#newMailInfo #mailAddress").val(),
                            "contact": contactInfo.name,
                            "postcode": $("#newMailInfo #mailNumber").val(),
                            "mobile": contactInfo.mobile,
                            "contactInfoNumber": contactInfo.number
                        }
                        mailData[index] = data;
                    }
                    setMailListm(mailData);

                    var id = $("#updateSupPanel").data("id");
                    var lxi = $("#empty").siblings(".hd").html();
                    lxi = JSON.parse(lxi);
                    let lxid = lxi.id;
                    let contactInfo1 = JSON.parse($(".node1").siblings(".hd").html());
                    if (type == 'new') {
                        var addu = {
                            'supplierContact':lxid,
                            'address': contactInfo1.address,
                            'contact': contactInfo1.contact,
                            'postcode': contactInfo1.postcode,
                            'mobile': contactInfo1.mobile
                        }
                        addu = JSON.stringify(addu);
                        $.ajax({
                            url: "../supplier/addSupplierAddress.do",
                            data: {//supplierId:供应商id   address
                                supplierId: id,
                                address: addu
                            },
                            success: function (res) {
                                var status = res.success;
                                if(status === "1" || status === 1){
                                    // layer.msg("新增成功");
                                    supplierInfoEdit(editObj);//刷新函数方法
                                }
                            }
                        })
                    }
                }
                break;
            case 'addContactmw'://常规修改中联系人单独添加
                var level = $("#newContectInfo").data('level');
                var type = $("#newContectInfo").data('type');
                var id = $("#newContectInfo").data('id');
                var source = $("#newContectInfo").data('source');
                //console.log('4.23-8:27',source);updateSupplie
                if(source == 'addCustomer') {
                    var contactInfo = $("#addAccount").data('contactInfo');
                    contactInfo = JSON.parse(contactInfo);
                    var data = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $(" #contactNumber").val(),
                        'cardPath': '',
                        'socialList': '[]'
                    }
                    if ($("#contactsCard .bussnessCard").length > 0) {
                        data.cardPath = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if ($(".otherContact li").length > 0) {
                        var arr = []
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json = {
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        data.socialList = arr;
                    }
                    var groupUuid = $("#contactsCard").attr("groupUuid")
                    if (type == 'new') {
                        data.id = Math.random();
                        data.groupUuid = groupUuid;
                        contactInfo.push(data);
                        addCusContact.push(data);

                    } else if (type == 'update') {
                        var id = $("#newContectInfo").data('id');
                        var index = contactInfo.findIndex(value => value.id === id);
                        data.number = id;
                        data.groupUuid = groupUuid;
                        contactInfo[index] = data;
                        addCusContact.forEach(function (item, index) {
                            if (data.number == item.number) {
                                addCusContact[index] = data;
                                $(".mailList tbody tr").each(function () {
                                    let thisContactID = $(this).find(".hd").html()
                                    if (thisContactID == data.number) {
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(4)").html(data.mobile);
                                    }
                                })
                                $(".receiveList tbody tr").each(function () {
                                    let thisContactID = $(this).find(".hd").html()
                                    if (thisContactID == data.number) {
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(3)").html(data.mobile);
                                    }
                                })
                            }
                        })
                    }

                    if(level == 2){
                        var tags = $("#contactFlag").html();
                        if(tags == "收货人"){
                            bounce_Fixed.show($("#newReceiveInfo"));
                            $("#ReceiveName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else if(tags== "联系人"){
                            bounce_Fixed2.show($("#newMailInfo"));
                            $("#mailName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else { //  新增联系人
                            bounce_Fixed.cancel($("newContectInfo"));
                        }
                    }else{
                        bounce_Fixed2.cancel($("#newContectInfo"));
                    }

                    // var ten = $(".node2 ").siblings(".hd").html();
                    var ten = data;
                    // ten = JSON.parse(ten);
                    let udid = $("#updateSupPanel").data("id");
                    // var sc = JSON.stringify(ten.socialList);
                    // var vc = JSON.stringify(ten.visitCard);
                    // var md = JSON.stringify(ten.mobile);
                    // var ps = JSON.stringify(ten.post);
                    // var na = JSON.stringify(ten.name);
                    // var tg = JSON.stringify(ten.tags);
                    var psont = $("#contactName").val();
                    var name = $("#position").val();
                    // if(psont.length == 0){
                    //     layer.msg("请填写联系人姓名");
                    //     return false;
                    // }else if(name.length == 0){
                    //     layer.msg("请填写联系人的职位");
                    //     return false;
                    // }
                    $.ajax({
                        url:"../supplier/addContact.do",
                        data:{//supplierId:供应商id  socialList:社交号  cardPath：名片地址  mobile：手机号
                            //post：职位  name：名称  tags：标签
                            supplierId:udid,
                            socialList:ten.socialList,
                            cardPath:ten.cardPath,
                            mobile:ten.mobile,
                            post:ten.post,
                            name:ten.name,
                            tags:ten.tags
                        },
                        success:function(res){
                            var state = res.success;
                            if(state === "1"|| state === 1){
                                // layer.msg("新增成功");
                                getmiltruList(contactInfo);
                                supplierInfoEdit(editObj);
                            }
                        }
                    })

                }else if(source === 'updateSupplie'){
                    var contactInfo = $("#addAccount").data('contactInfo');
                    contactInfo = JSON.parse(contactInfo);
                    var data = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $(" #contactNumber").val(),
                        'cardPath': '',
                        'socialList': '[]'
                    }
                    if ($("#contactsCard .bussnessCard").length > 0) {
                        data.cardPath = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if ($(".otherContact li").length > 0) {
                        var arr = []
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json = {
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        data.socialList = arr;
                    }
                    var groupUuid = $("#contactsCard").attr("groupUuid")
                    if (type == 'new') {
                        data.id = Math.random();
                        data.groupUuid = groupUuid;
                        contactInfo.push(data);
                        addCusContact.push(data);

                    } else if (type == 'update') {
                        var id = $("#newContectInfo").data('id');
                        var index = contactInfo.findIndex(value => value.id === id);
                        data.number = id;
                        data.groupUuid = groupUuid;
                        contactInfo[index] = data;
                        addCusContact.forEach(function (item, index) {
                            if (data.number == item.number) {
                                addCusContact[index] = data;
                                $(".mailList tbody tr").each(function () {
                                    let thisContactID = $(this).find(".hd").html()
                                    if (thisContactID == data.number) {
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(4)").html(data.mobile);
                                    }
                                })
                                $(".receiveList tbody tr").each(function () {
                                    let thisContactID = $(this).find(".hd").html()
                                    if (thisContactID == data.number) {
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(3)").html(data.mobile);
                                    }
                                })
                            }
                        })
                    }
                    if(level == 2){
                        var tags = $("#contactFlag").html();
                        if(tags == "收货人"){
                            bounce_Fixed.show($("#newReceiveInfo"));
                            $("#ReceiveName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else if(tags== "联系人"){
                            bounce_Fixed2.show($("#newMailInfo"));
                            $("#mailName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else { //  新增联系人
                            bounce_Fixed.cancel($("newContectInfo"));
                        }
                    }else{
                        bounce_Fixed2.cancel($("#newContectInfo"));
                    }
                    var ten = data;
                    let udid = $("#updateSupPanel").data("id");
                    var psont = $("#contactName").val();
                    var name = $("#position").val();
                    $.ajax({
                        url:"../supplier/addContact.do",
                        data:{//supplierId:供应商id  socialList:社交号  cardPath：名片地址  mobile：手机号
                            //post：职位  name：名称  tags：标签
                            supplierId:udid,
                            socialList:ten.socialList,
                            cardPath:ten.cardPath,
                            mobile:ten.mobile,
                            post:ten.post,
                            name:ten.name,
                            tags:ten.tags
                        },
                        success:function(res){
                            var state = res.success;
                            if(state === "1"|| state === 1){
                                // layer.msg("新增成功");
                                getmiltruList(contactInfo);
                                supplierInfoEdit(editObj);
                            }
                        }
                    })
                }
                break;
            case 'contractDel'://删除合同
                contractDel = $(this);
                bounce_Fixed.show($("#contractDelTip"));
                break;
            case 'addContact'://新增供应商中新增联系人
                var level = $("#newContectInfo").data('level');//为什么不是2呢？？？？？？
                var type = $("#newContectInfo").data('type');
                var id = $("#newContectInfo").data('id');
                var source = $("#newContectInfo").data('source');
                if(source == 'addCustomer'){
                    var contactInfo = $("#addAccount").data('contactInfo');
                    contactInfo = JSON.parse(contactInfo);
                    var data = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $(" #contactNumber").val(),
                        'visitCard': '',
                        'socialList': '[]'
                    }
                    if($("#contactsCard .bussnessCard").length > 0){
                        data.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if($(".otherContact li").length > 0){
                        var arr = [];
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json ={
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        data.socialList = arr;
                    }
                    var groupUuid= $("#contactsCard").attr("groupUuid")
                    if (type == 'new'){
                        data.id = Math.random();
                        data.groupUuid = groupUuid;
                        contactInfo.push(data);
                        addCusContact.push(data);

                    }else if(type == 'update') {
                        var id = $("#newContectInfo").data('id');
                        var index = contactInfo.findIndex(value=>value.id === id);
                        data.number = id;
                        data.groupUuid = groupUuid;
                        contactInfo[index] = data;
                        addCusContact.forEach(function (item, index) {
                            if(data.number == item.number){
                                addCusContact[index] = data ;
                                $(".mailList tbody tr").each(function(){
                                    let thisContactID = $(this).find(".hd").html()
                                    if(thisContactID == data.number){
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(4)").html(data.mobile);
                                    }
                                })
                                $(".receiveList tbody tr").each(function(){
                                    let thisContactID = $(this).find(".hd").html()
                                    if(thisContactID  == data.number){
                                        $(this).children(":eq(2)").html(data.name);
                                        $(this).children(":eq(3)").html(data.mobile);
                                    }
                                })
                            }
                        })
                    }
                    var html = getContractList(contactInfo);
                    $(".contectList").html(html);
                    // 给当前的赋值
                    if(level == 2){
                        var tags = $("#contactFlag").html();
                        if(tags == "收货人"){
                            bounce_Fixed.show($("#newReceiveInfo"));
                            $("#ReceiveName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else if(tags== "联系人"){
                            bounce_Fixed2.show($("#newMailInfo"));
                            $("#mailName")
                                .val(data.name).data('orgData',data.name)
                                .siblings(".hd").html(JSON.stringify(data)) ;
                        }else { //  新增联系人
                            bounce_Fixed2.show($("#newMailInfo"));
                        }
                    }else{
                        bounce.show($("#addAccount"));
                        bounce_Fixed2.cancel();
                    }
                    var peop =$("#mailName").val();
                    if(peop.length == 0){
                        $("#addMail").prop("disabled",true);
                    }else{
                        $("#addMail").prop("disabled",false);
                    }
                }else{
                    var data = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $("#contactNumber").val(),
                        'visitCard': '',
                        'socialList': '[]'
                    };
                    if($("#contactsCard .bussnessCard").length > 0){
                        data.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if($(".otherContact li").length > 0){
                        var arr = []
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json = {
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        data.socialList = arr;
                    }
                    // if (type == 'new') {
                    //     data.customer = id;
                    //     $.ajax({
                    //         url: '/sales/addCustomerContact.do',
                    //         data: data,
                    //         success: function (res) {
                    //             data.id = res.id;
                    //             var status = res.status;
                    //             if (status === '1' || status === 1) {
                    //                 layer.msg('新增成功')
                    //                 var groupUuidArr = []
                    //                 $("#newContectInfo [groupUuid]").each(function () {
                    //                     groupUuidArr.push({
                    //                         type: 'groupUuid',
                    //                         groupUuid: $(this).attr("groupUuid")
                    //                     })
                    //                 })
                    //                 cancelFileDel(groupUuidArr)
                    //                 // 给当前的赋值
                    //                 var tags = $("#contactFlag").html();
                    //                 if(tags == "收货人"){
                    //                     bounce_Fixed.show($("#newReceiveInfo"));
                    //                     setTimer('updateReceive');
                    //                     $("#ReceiveName")
                    //                         .val(data.name).data('orgData',data.name)
                    //                         .siblings(".hd").html(JSON.stringify(data)) ;
                    //                 }else if(tags== "发票接收人"){
                    //                     bounce_Fixed.show($("#newMailInfo"));
                    //                     setTimer('updateMail');
                    //                     $("#mailName")
                    //                         .val(data.name).data('orgData',data.name)
                    //                         .siblings(".hd").html(JSON.stringify(data)) ;
                    //                 }else { //  新增联系人
                    //                     bounce_Fixed.cancel();
                    //                 }
                    //                 supplierInfoEdit(cus_seeTrObj);
                    //             } else {
                    //                 layer.msg("查看失败！");
                    //             }
                    //         }
                    //     })
                    // }else if(type == 'update'){
                    //     data.contactId = id;
                    //     $.ajax({
                    //         url: '/sales/updateContactsSocialAndCard.do',
                    //         data: data,
                    //         success: function (res) {
                    //             var status = res.status;
                    //             if (status === '1' || status === 1) {
                    //                 var groupUuidArr = []
                    //                 $("#newContectInfo [groupUuid]").each(function () {
                    //                     groupUuidArr.push({
                    //                         type: 'groupUuid',
                    //                         groupUuid: $(this).attr("groupUuid")
                    //                     })
                    //                 })
                    //                 cancelFileDel(groupUuidArr)
                    //                 supplierInfoEdit(cus_seeTrObj);
                    //                 // 给当前的赋值
                    //                 var tags = $("#contactFlag").html();
                    //                 if(tags == "收货人"){
                    //                     bounce_Fixed.show($("#newReceiveInfo"));
                    //                     $("#ReceiveName")
                    //                         .val(data.name).data('orgData',data.name)
                    //                         .siblings(".hd").html(JSON.stringify(data)) ;
                    //                     setTimer('updateReceive');
                    //                 }else if(tags== "联系人"){
                    //                     bounce_Fixed.show($("#newMailInfo"));
                    //                     setTimer('updateMail');
                    //                     $("#mailName")
                    //                         .val(data.name).data('orgData',data.name)
                    //                         .siblings(".hd").html(JSON.stringify(data)) ;
                    //                 }else { //  新增联系人
                    //                     bounce_Fixed.cancel();
                    //                 }
                    //             } else {
                    //                 layer.msg("查看失败！");
                    //             }
                    //         }
                    //     })
                    // }
                    bounce.show($("#addAccount"));
                }
                break;
            case 'addMail'://新增邮寄地址
                var level = $("#newContectInfo").data('level');
                var type = $("#newContectInfo").data('type');
                var id = $("#newContectInfo").data('id');
                var source = $("#newContectInfo").data('source');
                if(source == 'addCustomer') {
                    var mailData = $("#addAccount").data("receiveInfo");
                    mailData = JSON.parse(mailData);
                    console.log(mailData);
                    let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                    if (type == "new") {
                        // let contractInfo = $("#mailName").data("contractInfo");
                        // contractInfo = JSON.parse(contractInfo);
                        let contractInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var data = {
                            'customerContract': Math.random(),
                            'address': $("#mailAddress").val(),
                            'contact': contractInfo.name,
                            'postcode': $("#mailNumber").val(),
                            'mobile': contractInfo.mobile,
                            'contactInfoNumber': contractInfo.number,
                        }
                        mailData.push(data);
                    } else if (type == "update") {
                        var id = $("#newMailInfo").data("id");
                        var index = mailData.findIndex(value => value.number === id);
                        var data = {
                            "number": id,
                            "address": $("#newMailInfo #mailAddress").val(),
                            "contact": contactInfo.name,
                            "postcode": $("#newMailInfo #mailNumber").val(),
                            "mobile": contactInfo.mobile,
                            "contactInfoNumber": contactInfo.number
                        }
                        mailData[index] = data;
                    }
                    setMailList(mailData);
                }
                // }else{
                //     var id = $("#newMailInfo").data('id');
                //     if(type == 'new'){
                //         let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                //         var json ={
                //             'address':$("#mailAddress").val(),
                //             'contact':contactInfo.name,
                //             'postcode':$("#mailName").val(),
                //             'mobile':contactInfo.mobile
                //         }
                //         json = JSON.stringify(json);
                //         var params = {
                //             'customerId':id,
                //             'fpAddress':json
                //         }
                //         $.ajax({
                //             url:"../supplier/addSupplierAddress.do",
                //             data:params,
                //             success:function(res){
                //                 var status = res.status;
                //                 if(status === "1" || status === 1){
                //                     layer.msg("新增成功");
                //                 }
                //             }
                //         })
                //     }else if(type == 'update'){
                //         let mavenInfo = JSON.parse($("#mailName").siblings(".hd").html());
                //         var panus = {
                //             id: id,
                //             address:$("#mailAddress").val(),
                //             contact:mavenInfo.name,
                //             postcode:$("#mailName").val()
                //         }
                //         $.ajax({
                //             url:"../supplier/updateSupplierAddress.do",
                //             data:panus,
                //             success:function(res){
                //                 var status = res.status;
                //                 if(status === "1" || status === 1){
                //                     layer.msg("修改成功");
                //                 }
                //             }
                //         })
                //     }
                // }

                bounce_Fixed2.cancel();
                break;
        }
    });
 /*   $(".bounce_Fixed").on("click",".linkBtn",function(){
        let fun = $(this).data("fun");
        $("#removeout").data("fun",fun);
    })*/
    $(".bounce_Fixed2, .bounce_Fixed3").on('click','.ty-btn,.fileImScan,.node', function(){
        var name = $(this).data('fun');
        let contractInfo = $("#cScan").data("contractInfo")
        let contractBase = {}
        let listTY = []
        if (contractInfo) {
            contractBase = contractInfo.contractBase
            listTY = contractInfo.listMt || []
        }
        switch (name) {
            case 'imgScan': // 合同的picture
                imgViewer($(this));
                break;
            case 'cWord': // 合同的可编辑版
                // let path = $("#cScan .cWord").attr('path');
                seeOnline($(this))
                break;
            case 'tyNum': //本合同下的商品
                let spInfo = JSON.parse(editObj.siblings(".thingsbox").html());
                setContactGS(listTY , false);
                let supplierName = spInfo.codeName + spInfo.name;
                $("#tipcontractGoods .bonceHead span").html('本合同下的材料');
                $("#tipcontractGoods .tip").html(listTY.length);
                $("#tipcontractGoods .supplerName").html(supplierName);
                break;
            case 'cEditLog': // 本版本合同的修改记录
                getContractChangeLog(contractBase.id)
                break;
            case 'cRenewalLog': // 本合同的续约记录
                let primaryId = contractBase.primaryCont
                getContractRenewLog(primaryId)
                break;
        }
    });
    $(".bounce_Fixed3").on('click','.ty-btn', function(){
        var name = $(this).data('name');
        switch (name) {
            case 'cEndOk': // 暂停履约/合同终止 或者 回复履约的确定
                cEndOk(1)
                break;
            case 'cEndOk2':
                let radio = $("#cEndTip2 [name='expire']:checked").val()
                if (radio === '1') {
                    // 不再执行，转入“已到期的合同
                    cEndOk(2)
                } else if (radio === '2') {
                    // 续约
                    bounce_Fixed3.cancel()
                    contractInfoEdit('cRenewalStop', 'updateCustomer');
                } else {
                    layer.msg("请勾选！")
                }
                break;
        }
    });
    $(".add_splName").on("blur", function(){
        var val = $(this).val();
        $(this).parents("form").find("[name='name']").val(val.substring(0,6));
    })
    $("#picShow").on("click",'',function(){//提示窗
        $("#picShow").fadeOut("fast");
    })
    $("#newContractInfo").on("click",".fa-times",function(){//新增合同   设置在点击图片文件清除按钮后的效果
        let info = JSON.parse($(this).siblings(".hd").html())
        let fileUid = info.fileUid
        cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
        if ($(this).parents(".fileCon1").length > 0 && info.id) {
            let fileItem = $(this).parent(".fileIm").html()
            $("#newContractInfo .deleteFile").append( `<span class="fileItem">${fileItem}</span>`)
        }
        $(this).parent(".fileIm").remove();
    })

    $("#newContractInfo").on('click','[type="btn"]', function () {
        let fun = $(this).data("fun");
        let tips = '', title = '', list = [];
        let productListTY = $("#newContractInfo").data('productListTY') || []
        let editProductTY = productListTY.filter(item => item.isChecked)
        let source = $("#newContractInfo").data('source');
        let supplierName = ``;
        switch (fun){
            case 'scanTyGs' :
                title = '本合同下的材料'
                list = editProductTY
                break;
            case 'removeTyGs' :
                title = '从本合同移出材料'
                list = editProductTY
                break
            case 'addTyGs' :
                title = '向本合同添加材料'
                list = productListTY
                break;
        }
        if (source === 'addCustomer') {
            supplierName = $("#suppliernumber").val() + $("#suppliername").val();
        } else {
            let spInfo = $("#updateSupPanel").data("cusInfo");
            supplierName = spInfo.codeName + spInfo.name;
        }
        $("#tipcontractGoods .bonceHead span").html(title);
        $("#tipcontractGoods .tip").html(list.length);
        $("#tipcontractGoods .supplerName").html(supplierName);
        let count = 0
        if (fun === 'addTyGs') {
            count = list.filter(item => item.isChecked).length
        }
        $("#tipcontractGoods .count").html(count);
        $("#tipcontractGoods").data('origin', fun)
        if (fun === 'scanZsGs' || fun === 'scanTyGs') {
            $(".addOrCancel").hide(); $(".cScanc").show();$(".countStr").hide()
            setContactGS(list , false);
        } else if (fun === 'addTyGs'){
            $(".addOrCancel").show(); $(".cScanc").hide();$(".countStr").show()
            setContactGS(list, true, true);
        } else {
            $(".addOrCancel").show(); $(".cScanc").hide();$(".countStr").show()
            setContactGS(list, true);
        }
    })
    $(".chooseCusCon").click(function(){//新增合同
        let target = $(this).data("target");
        let str ="其它";
        var type = "", source="";
        if(target == "#ReceiveName"){
            str ="收货人";
            type = $("#newReceiveInfo").data("type");
            source = $("#newReceiveInfo").data("source");
        }else if(target == "#mailName"){
            str = "联系人";
            type = $("#newMailInfo").data("type");
            source = $("#newMailInfo").data("source");
        }
        $(".p0").hide();
        $("#target").val(target);
        var bont = $("#mailName").data("font");
        var id = $("#updateSupPanel").data("id");
        if(bont === "upen"){
            $.ajax({
                url:"../supplier/getContactsList.do ",
                data:{
                    supplierId:id
                },
                success:function(res){
                    var statue = res.success;
                    if(statue === "1" || statue === 1){
                        // layer.msg("查询成功");
                        var list = res.data;
                        setCusListStr(list);
                    }
                }
            })
        }
        // var bont = $("#mailName").data("font");
        // if(bont ==="add"){
        //     $("#chooseCusContact .p0").show();
        // }else{
        //     $("#chooseCusContact .p0").hide();
        //     $("#chooseCusContact .p1").show();
        // }
        bounce_Fixed3.show($("#chooseCusContact"));
        if(source == 'addCustomer'){
            var contactInfo = addCusContact;
            setCusListStr(contactInfo,'addCustomer');
        }else{
            var cusId = $("#updateCustormPanel").data("id");
            getContractList(cusId);
        }
    })
    $("#chooseCusContact").on("click", '.fa', function () {//选择复选框
        $("#chooseCusContact").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).attr("class", "fa fa-dot-circle-o")
    })
    $("#chagebasm").on("click",".fa",function(){//复选框点击
        $("#chagebasm").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).attr("class", "fa fa-dot-circle-o");
    })
    $("#suppliername").on("blur",function(){ //基本信息获取
        var val = $(this).val();
        $(this).parents("form").find("[name='spluName']").val(val.substring(0,6));
    })

    $("#addCot").on("click",function(){
        // $("#addAccount").css("top","18.5px");//新增供应商
    })
    $(".jiben").on("click",function(){//基本信息弹窗
        // $("#addAccount").css("top","18.5px");
    })
    // $(".kp").on("click",function(){//新增供应商弹窗大小   //大神改的位置
    //     $("#addAccount").css("width","859px");
    // })




    // 获得供应商列表
    getContractMes(1,20);
    $("#userName").on('keydown',function(){
        $("#cloone").show();
    })
    $("#cloone").on("click",function(){
        $("#userName").val("");
        $("#cloone").hide();
    })
    $(document).click(function(e){
        var divTop = $('#cloone');   // 设置目标区域
        if(!divTop.is(e.target) && divTop.has(e.target).length === 0){
            divTop.hide();
        }
    })
    $("#name").on('keydown',function(){
        $("#cloone1").show();
    })
    $("#cloone1").on("click",function(){
        $("#name").val("");
        $("#cloone1").hide();
    })
    $(document).click(function(e){
        var divTop = $('#cloone1');   // 设置目标区域
        if(!divTop.is(e.target) && divTop.has(e.target).length === 0){
            divTop.hide();
        }
    })
    $("#coden").on('keydown',function(){
        $("#cloone2").show();
    })
    $("#cloone2").on("click",function(){
        $("#coden").val("");
        $("#cloone2").hide();
    })
    $(document).click(function(e){
        var divTop = $('#cloone2');   // 设置目标区域
        if(!divTop.is(e.target) && divTop.has(e.target).length === 0){
            divTop.hide();
        }
    })
})

// creator: sy 2024-01-25   放弃录入的数据
function abandonn(differ){
    bounce.show($("#giveupend"));
    let str1 = ``;
    switch (differ) {
        case 2:
            str1 += `
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="giveupsure(3)">确定</span>
            `;
            $(".getoffer").html(str1);
            break;
        default:
            str1 += `
                 <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="giveupsure(2)">确定</span>
            `;
            $(".getoffer").html(str1);
            break;
    }
}
// creator : 2021-5-24 hxz 编辑合同（新增、修改、续约）
function contractInfoEdit(type, source, thisObj) {
    $("#newContractInfo").data('type', type).data('source', source);
    bounce_Fixed.show($("#newContractInfo"));
    let cusInfo  = {}

    $("#newContractInfo input").val("")
    $("#newContractInfo .fileCon>div").html("")
    $("#newContractInfo .deleteFile").html('')
    $("#newContractInfo .tyGoodNumber").html(0)
    initCUpload2($("#cUpload1"),'img');
    initCUpload2($("#cUpload2"),'doc');

    $(".cRenewalTip").hide();
    //$("#newContractInfo .scanGs").data('gsArr',[]).data('gsArrNo',[]).html("0") ;

    if(source === 'updateCustomer'){
        cusInfo  = $("#updateSupPanel").data("cusInfo")
        $(".cusItem").show()
        $("#newContractInfo [name='customerName']").val(cusInfo.fullName)
    } else {
        $(".cusItem").hide()
    }
    if(type === 'new'){
        let supId = ``
        $("#newContractInfo .bonceHead span").html("新增合同")
        if (source === 'addCustomer') {
            $("#newContractInfo").data('productListTY', []) ;
        } else {
            getContractByCustomer(cusInfo.id)
                .then(data => {
                    $("#newContractInfo").data('productListTY',data.mtList) ;
                    renderEditProductTy()
                })
        }
    } else if(type === 'update'){
        $("#newContractInfo .bonceHead span").html("修改合同")
    }else if(type === 'cRenewal' ||type === 'cRenewalHistory'||type === 'cRenewalStop'){
        $("#newContractInfo .bonceHead span").html("合同续约")
        $(".cRenewalTip").show();
    }

    if(type === 'update' || type === 'cRenewal' || type === 'cRenewalHistory'|| type === 'cRenewalStop'){
        editContractObj = thisObj
        if(source === 'addCustomer'){
            var newcInfo = JSON.parse(thisObj.siblings(".hd").html());
            $("#newContractInfo .cNo").val(newcInfo.cNo)
            $("#newContractInfo .cSignDate").val(newcInfo.cSignDate)
            $("#newContractInfo .cStartDate").val(newcInfo.cStartDate)
            $("#newContractInfo .cEndDate").val(newcInfo.cEndDate)
            $("#newContractInfo .cMemo").val(newcInfo.cMemo)
            let imgStr1 = ``
            newcInfo.fileCon1.forEach(function(data){
                imgStr1 += `<span class="fileIm" >
                                <span>${data.orders}</span>
                                <span class="fa fa-times"></span>
                                <span class="hd">${JSON.stringify(data) }</span>
                            </span>`
            })
            $("#newContractInfo .fileCon1").append(imgStr1)
            let imgStr2 = ``
            newcInfo.fileCon2.forEach(function(data){
                imgStr2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data)}</span>
                         </span>`
            })
            $("#newContractInfo .fileCon2").html(imgStr2)


        } else {
            // 接口获取合同详情
            let cid = 0
            if (!thisObj) {
                cid = $("#cEndTip").data("cid")
            } else {
                cid = thisObj.parents(".contractItem").data("id") ;
            }
            $("#newContractInfo").data("cid",cid)
            $.ajax({
                url : $.webRoot + "/supplier/poContractBaseMes.do",
                data: { id: cid },
                success:function (res) {
                    let data = res.data
                    let contractBase = data.contractBase
                    let listImage = data.listImage
                    let listTY = data.listMt || []
                    var success = res.success
                    if(success !== 1){
                        layer.msg("获取原来的合同信息失败！");
                        return false;
                    }
                    $("#newContractInfo .cNo").val(contractBase.sn)
                    $("#newContractInfo .cSignDate").val(contractBase.signTime?(new Date(contractBase.signTime).format("yyyy-MM-dd")):'' )
                    let start = new Date(contractBase.validStart).format("yyyy-MM-dd")
                    let end = new Date(contractBase.validEnd).format("yyyy-MM-dd")
                    $("#newContractInfo .cStartDate").val( start ).data("old", start);
                    $("#newContractInfo .cEndDate").val( end ).data("old", end);
                    $("#newContractInfo .cMemo").val(contractBase.memo);
                    let imgStr1 = ``
                    for(let i in listImage) {
                        imgStr1 += `<span class="fileIm"  >
                                       <span>${Number(i) + 1}</span>
                                       <span class="fa fa-times"></span>
                                       <span class="hd">${JSON.stringify(listImage[i]) }</span>
                                  </span>`
                    }
                    $("#newContractInfo .fileCon1").append(imgStr1)
                    let imgStr2 = ``
                    if( contractBase.filePath &&  contractBase.filePath.length > 0){ // 下面的（id: 1）是用来区分是否修改用的
                        imgStr2 = `<span class="fileIm"  >
                                       <span class="fa fa-file-word-o"></span>
                                       <span class="fa fa-times"></span>
                                       <span class="hd">${ JSON.stringify({ id: 1, filename : contractBase.filePath , originalFilename: contractBase.fileName })  }</span>
                                  </span>`
                    }
                    $("#newContractInfo .fileCon2").html(imgStr2)
                    getContractByCustomer(cusInfo.id)
                        .then(data => {
                            let productListTY = data.mtList || []
                            productListTY.map(item => {
                                item.isChecked = false
                                listTY.map(val => {
                                    if (item.id === val.id) {
                                        item.isChecked = true
                                    }
                                })
                            })
                            $("#newContractInfo").data('productListTY',data.mtList) ;
                            renderEditProductTy()
                        })
                }
            })
        }
    }
}
function getContractByCustomer(supplier) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: $.webRoot + '/supplier/getContractMt.do',
            data: {
                type: 1, // 1-添加材料 2-移除材料
                supplier: supplier
            },
        }).then(res => {
            let data = res.data
            resolve(data)
        })
    })
}
function renderEditProductTy() {
    let productListTY = $("#newContractInfo").data('productListTY') || []
    let editProductTY = productListTY.filter(item => item.isChecked)
    let nameArr = editProductTY.map(item => item.name)
    $("#newContractInfo .tyGoodNumber").html(nameArr.length)
    $("#newContractInfo .tyGoodList").html(nameArr.join("、"))
}
// creator: sy 2022-05 （新增供应商）新增联系人（后面部分）
function addContactInfo(num) {
    $("#addContact").prop("disabled",false);
    var type = "", source = "";
    if(num === 2){
        $("#newContectInfo .bonceHead span").html('新增联系人');
        // $("#contactFlag").html('收货人');
        type = $("#newReceiveInfo").data('type');
        source = $("#newReceiveInfo").data('source');
        $("#yjtc").hide();
        $("#lxtc").show();
    }else if(num === 3){
        $("#newContectInfo .bonceHead span").html('新增联系人');
        $(".otherContact").html("");
        // $("#contactFlag").html('发票接收人');
        $("#contactFlag").html('联系人');
        type = $("#newMailInfo").data('type');
        source = $("#newMailInfo").data('source');
        $("#yjtc").show();
        $(".qu1").hide();
        $("#lxtc").hide();
    }
    $("#newContectInfo").data('type',"new");
    $("#newContectInfo").data('source', "addCustomer");
    document.getElementById('newContectData').reset();
    var add = $("#newMailInfo").data("add");
    if(add === 0){
        $("#addContact2").show();
        $("#addContact1").hide()
        $("#addContact").hide();
        $("#addContact3").hide();
    }else if(add === 1){
        $("#addContact2").hide();
        $("#addone").hide();
        $("#addtwo").hide();
        $("#addthree").hide();
        $("#addfour").hide();
        $("#addfive").hide();
        $("#addContact").show();
        $("#addContact1").hide();
        $("#addContact3").hide();
        $(".qu1").hide();
        // $(".qu2").show();
    }
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    if($(".contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
    }
    $("#newContectInfo").data("id", "customerId");
    $("#addMoreContact").hide();
    $("#uploadCard").html("")
    initCardUpload($("#uploadCard"));
    bounce_Fixed2.show($("#newContectInfo"));
    setTimer('updateContact');
}
// creator: sy 2022-07-05 （新增供应商）新增联系人（前面部分）
function addcustom(){
    $("#newContectInfo").data('level',1);
    $("#newMailInfo").data("add",1);
    addContactInfo(2);
}
// creator: sy 2022-07-18 供应商常规信息修改弹窗中新增合同弹窗中合同下的商品后面的“添加材料”按钮
/*function addinside(){
    $("#removeout .bonceHead span").html("向本合同添加材料");
    $("#removeout").data("fun","addGs");
    $(".getout").show();$(".makesure").show();
    $(".cloose").hide();$(".fxclo").hide();$("#mtSeeBtn").hide();
    var addid = $("#updateSupPanel").data("id");

    $.ajax({
        url:"../supplier/getContractMt.do",//"../supplier/getContractMaterialList.do",
        data:{
            type: 1, // 1-添加材料 2-移除材料
        },
        success:function(res){
            //res.data.mtList;
            let data = {
                data: res.data.mtList
            }
            setContactGS(data,true);
            $("#updunt").hide();
            $("#addmor").show();
            bounce_Fixed3.cancel($("#newcontractpro"));
            bounce_Fixed3.show($("#removeout"));
        }
    })
}*/
// creator: sy 2022-07-05 新增联系人弹窗中添加更多联系方式
function addMore(obj){
    obj.next("select").show();
}
// creator: sy 2022-07-05 添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = "";
    obj.val('0').hide();
    $(".otherContact li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val){
        case '1':
            html +=
                '<li id="addone" class="addone">'+
                '<span class="sale_titl1">手机:</span>'+
                '<span class="gap gap-1"><input id="contractphone" type="text" placeholder="请录入" data-type="1" data-name="手机" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '2':
            html +=
                '<li class="addtwo" id="addtwo">'+
                '<span class="sale_ttl1">QQ:</span>'+
                '<span class="gap"><input id="contractqq" type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '3':
            html +=
                '<li class="addthree"  id="addthree">'+
                '<span class="sale_ttl1">Email:</span>'+
                '<span class="gap"><input id="contractemal" type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '4':
            html +=
                '<li class="addthree" id="addfour">'+
                '<span class="sale_ttl1">微信</span>'+
                '<span class="gap"><input id="contractweixi" type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '5':
            html +=
                '<li class="addfive" id="addfive">'+
                '<span class="sale_ttl1">微博:</span>'+
                '<span class="gap"><input id="contractwebo" type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '9':
            $("#useDefinedLabel input").val("");
            bounce_Fixed2.cancel($("#newContectInfo"));
            bounce_Fixed.show($("#useDefinedLabel"));
            setTimer('useDefinedLabel');
            break;
        default:break;
    }
}
// creator: sy 2022-07-05 联系人自定义标签确定
function addNewLable(){
    var val=$("#defLable").val();
    var html =
        '<li>' +
        '<span class="sale_ttl1 salet1">' + val + '：</span>' +
        '<span class="gap"><input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" data-org="" require/></span>'+
        '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
        '</li>';
    $(".otherContact").append(html);
    bounce_Fixed.cancel();
    bounce_Fixed2.show($("#newContectInfo"));
}
// creator: sy 2022-07-19 从本合同中添加商品弹窗中的确定按钮
function addOrCancelOk(){
    let origin = $("#tipcontractGoods").data("origin");
    let productListTY = $("#newContractInfo").data('productListTY') || []
    if (origin === 'addTyGs') {
        productListTY.map(item => item.isChecked = false)
    }
    $("#tipcontractGoods input:checkbox:checked").each(function(){
        let id = $(this).parents("tr").data("id")
        if (origin === 'addTyGs') {
            productListTY.map(item => {
                if (item.id === id) {
                    item.isChecked = true
                }
            })
        }
        if (origin === 'removeTyGs') {
            productListTY.map(item => {
                if (item.id === id) {
                    item.isChecked = false
                }
            })
        }
    })
    if (origin === 'addTyGs' || origin === 'removeTyGs') {
        renderEditProductTy()
    }
    bounce_Fixed3.cancel()
}
// creator: sy 2022-06-20 新增联系人
function addpeo(){
    $("#contactName").val("");
    $("#position").val("");
    $("#contactNumber").val("");
    $("#newContectInfo").data("type","new");
    bounce_Fixed2.show($("#newContectInfo"));
}
// creator: sy 2022-07-02 新增供应商弹窗中新增合同弹窗中本合同下的商品后面的“添加材料”按钮
function addzi(){
    $("#removeout .bonceHead span").html("向本合同添加材料");
    $(".getout").show();$(".makesure").show();
    $(".cloose").hide();$(".fxclo").hide();$("#mtSeeBtn").hide();
    // let addid = $("#updateSupPanel").data("id");
    // $.ajax({
    //     url:"../supplier/getContractMaterialList.do",
    //     data:{
    //         id:addid
    //     },
    //     success:function(res){
    //         setContactGS(res,true);
    //     }
    // })
    var gsAunk = $("#newcontractpro .scanGs").data("gsArrNo");
    setContactGS(gsAunk,true);
    $("#updunt").hide();
    $("#cGTp").html(0);
}
// creator: sy 2022-08-02 点击“该供应商供应的材料”后面的“0种”中的0
function allcountent(){
    bounce.cancel();
    bounce_Fixed.show($("#cmaterials"));
    $(".recordTtl").html("材料清单");
    var allid = $("#updateSupPanel").data("id");
    var name = $("#supplie_main").html();
    $.ajax({
        type:"get",
        url:"../material/getSupplierMaterials",
        data:{
            supplierId:allid,
            state:1,
            enabled:1
        },
        success:function(res){
            var listo = res.data || [];
            var str12 = ``;
            var st21 = ``;
            if(listo.length>0){
                str12 +=`<span>${name}</span>供应的材料共<span>${handleNull(listo.length)}</span>种`;
                for(let i =0;i<listo.length;i++){
                    var indecs = listo.length-1;
                    st21 +=`
                       <tr>
                            <td class="centten">${handleNull(listo[i]['name'])}</td>
                            <td class="centten">${handleNull(listo[i]['code'])}</td>
                            <td class="centten">${handleNull(listo[i]['model'])}</td>
                            <td class="centten">${handleNull(listo[i]['specifications'])}</td>
                            <td class="centten">${handleNull(listo[i]['unit'])}</td>
                        </tr>`;
                }
                $("#contactlist .recordTip").html(`${str12}`);
                $("#coactbank #bothon").html(`${st21}`);
            }else{
                str12 = `<span>该供应商下没有材料</span>`;
                $("#contactlist .recordTip").html(`${str12}`);
                $("#coactbank #bothon").html("");
            }
        }
    })
}

// creator: sy 2022-08-10 邮寄和联系人弹窗点击取消
function bckoff(type,oter){
    if(type === "close"){
        if(oter == 1){
            bounce_Fixed2.cancel($('#newContectInfo'));
            bounce_Fixed2.show($('#newMailInfo'));
        }else if(oter == 2){
            bounce_Fixed2.cancel($('#newMailInfo'));
            bounce.show($('#addAccount'));
        }
    }else if(type === "opon"){
        bounce_Fixed2.cancel($('#newContectInfo'));
        bounce.show($("#addAccount"));
    }
    // $("#addAccount").css("top","13px");
}
// creator: sy 2022-07-13 已被停用的邮寄信息中”启用“
function beganup(opend){
    var qidonid = $(".jilu").siblings(".hd").html();
    var enabled = 1;
    $.ajax({
        url:"../supplier/startOrStopAddress.do",
        data:{
            addressId:qidonid,
            enabled:enabled
        },
        success:function(res){
            var state = res.success;
            if(state === "1" || state === 1){
                // layer.msg("启动成功");
                bounce_Fixed2.cancel($("#stopsendms"));
                supplierInfoEdit(editObj);
            }
        }
    })
}
// creator: sy 2022-07-05 点击“暂停采购”按钮后点击提示弹窗中的确定按钮进行采购暂停
function beginstop(){
    var supplesureId = $("#suspend").data("supplieId");
    $.ajax({
        url:"../supplier/stopSupplier.do",
        data:{
            id:supplesureId,
        },
        success:function(res){
            console.log(res);
            var status = res.success;
            if( status === 1){
                // layer.msg("暂停成功");
                bounce_Fixed.cancel($("#suspend"));
                getContractMes(1,20);
            }else{
                layer.msg("该供应已被使用，不能暂停");
            }
        }
    })
}

// creator: sy 2022-07-05 删除名片
function cancleCard(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    $('#uploadCard').show();
}
// creator: sy 2022-07-05 删除文件相关方法
function cancelFileDel(option, isDel){
    console.log(typeof option)
    let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]');
    // 传值为 all，赋值为存储的所有文件
    if (option === 'all') {option = fileDelArr}

    // 判断传值类型
    if (typeof option === 'object') {
        if (!isArrayFn(option)) { option = [option] } // 如果为对象转换为数组
        option.forEach(function (it) {
            let n = -1
            fileDelArr.forEach(function(item,index){
                if(it.type == item.type && it[it.type] == item[item.type]){
                    n = index;
                    fileDelArr.splice(index, 1);
                    // clearTimeout(item['fileDelTimer']);
                }
            })
            if(n === -1 ){
                console.log('可能面临风险');
                console.log('没匹配到：', option);
            }
            // 如果传了此字段为true，那么也同时会调用删除文件接口
            if (isDel) {
                let type = it.type
                let url = $.webRoot+'/uploads/removeByFile.do' ; // 默认按照文件删除
                let data = {
                    fileUid: it[type],
                    userId: sphdSocket.user.userID
                }
                if (type === 'groupUuid'){
                    url = $.webRoot+'/uploads/removeFilesByGroup.do' ;
                    data = {
                        groupUuid: it[type],
                        userId: sphdSocket.user.userID
                    }
                }
                $.ajax({url: url, data: data, beforeSend: function () {}, error: function () {}});
            }
        })
        window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
    } else {
        console.log('类型错误')
    }

}
// creator: sy 2022-07-05 图片删除
function cancleThis(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent(".imgsthumb").remove();
}
// creator: sy 2022-06-20 修改联系人
function chageUp(){
    $("#newContectInfo").data("type","update");
}
// creator: sy 2022-04 开票配置
function chargeHangType(){
    // 考虑公司不能开发票，则从入库之日起，且不可编辑
    var haveInvoice = $("#haveInvoice").val() ;
    var setHangAccount = $("#setHangAccount").val() ;
    if(setHangAccount === "0" || setHangAccount === ""){
        $("#setStartDate").val("") ;
        return false;
    }
    if(haveInvoice === "1"){ // 能开票
        $("#setStartDate1").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate2").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate2Con").removeAttr("disabled") ;
        $("#setStartDate").val("") ;
    }else if(haveInvoice === "0"){
        $("#setStartDate1").attr("class" ,"fa fa-dot-circle-o ccc").attr("disabled" , "disabled") ;
        $("#setStartDate2").attr("class" ,"fa fa-circle-o ccc").attr("disabled" , "disabled") ;
        $("#setStartDate2Con").attr("disabled" , "disabled") ;
        $("#setStartDate").val(1) ;
    }else{
        $("#setStartDate1").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate2").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate").val("") ;
    }
}
// creator: sy 2022-07-05 复选框相关方法
function chargeshow8(){
    // var bepont = $("#bepont").val();xytimeomre
    // var vapont = $("#vapont").val();
    // debugger;
    var haveInvoice = $("#haveInvoice").val();
    var invoiceCategory = $("#vatInvoice").val();
    if(haveInvoice === "1" && invoiceCategory === "1"){
        $(".hang8").show();
    }else{
        $(".hang8").hide();
    }
}
// creator: sy 2022-07-05 选择联系人
function chooseCusContactOk(){
    let selectObj = $("#chooseCusContact").find(".fa-dot-circle-o");
    var peop =$("#mailNumber").val();
    if(peop.length == 0){
        $("#addMail").prop("disabled",true);
    }else{
        $("#addMail").prop("disabled",false);
    }
    if(selectObj.length > 0){
        let strInfo = selectObj.siblings("span.hd").html();
        let info = JSON.parse(strInfo);
        $($("#target").val()).val(selectObj.next().html()).data('orgData',selectObj.next().html()).siblings(".hd")
            .html(strInfo);
        bounce_Fixed3.cancel();
        var source = $("#newReceiveInfo").data('source');
        if(source == 'addCustomer'){
            let numberSelect = info.number;
            for(let q = 0; q < addCusContact.length ; q++){
                let contactItem = addCusContact[q];
                if(numberSelect == contactItem.number){
                    contactItem["select"] = true;
                }
            }
            var peop =$("#mailName").val();
            if(peop.length == 0){
                $("#addMail").prop("disabled",true);
            }else{
                $("#addMail").prop("disabled",false);
            }
        }
    }else layer.msg('请先选择人员');
}
// creator: sy 2022-08-18 设置输入框数字
function clearNoNum(input){
    // if (input.value < 0) input.value = 0;
    // if (input.value > 100) input.value = 100;
    input.value = input.value.replace(/[^\d.]/g,"");//清除“数字”和“.“以外的字符
    input.value = input.value.replace(/\.{2,}/g,".");//只保留第一个，清除多余的
    input.value=input.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    input.value=input.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');
    if(input.value.indexOf(".")<0&&obj.value!=""){   //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于01、02
        input.value=parseFloat(input.value);
    }
    // input.value = input.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//最多能输入两位小数
    // input.value = input.value.replace(/\D/gi,"");//输入框只能输入数字
    $("#uncertainty1").attr("class","fa fa-circle-o");
}
// creator: sy 2022-12-21 数字设定
function clear0(input){
    var vane = input.val();
    if(vane == 0){
        input.val(0.01);
    }else if(vane == 100){
        input.val(99.99);
    }
}
// creator: sy 2022-07-29 点击“材料清单”弹窗中的“关闭”按钮
function cloose(){
    bounce_Fixed.cancel($("#cmaterials"));
    bounce.show($("#addAccount"));
    // $("#addAccount").css("top","40px");  //给弹窗设置顶部距离
}
// creator: sy 2022-08-10 点击新增供应商弹窗的取消
function cloosne(){
    $("#clist2").html("");
    $("#clist3-1").html("");
    $("#clist1").html("");
    bounce.cancel();
}
// creator: sy 2022-07-05 返回常规信息主页
function comebck1(){
    $("#materials").hide();
    $("#exclusive").hide();
    $("#nolonger").hide();
    $("#procurement").hide();
    $("#searchdi").hide();
    $("#home").show();
    getContractMes(1,20);
}
// creator: sy 2022-07-08 点击“本版本合同的修改记录”后的弹窗显示
/*function conlook(obig){
    let contractId = $("#lookpoot").data("contractId");
    $("#contractList").hide();
    bounce_Fixed2.show($("#modification"))
    bounce_Fixed3.cancel()
    let str = ``;
    $.ajax({
        url:"../supplier/poContractBaseHistory.do",
        data:{id : contractId},
        success:function(res){
            let list = res.data.list || [];
            if (list.length > 0) {
                var eidtNumber = list.length -1;
                $("#modRecord .recordTip").html('当前数据为第'+ eidtNumber + '次修改后的结果');
                $("#modRecord .recordEditer").html('修改时间:'+list[eidtNumber].updateName + ' ' +new Date(list[eidtNumber].updateDate).format('yyyy-MM-dd hh:mm:ss'));
                $("#contractList").show();
                for (let r in list){
                    if(r == '0'){
                        str +=
                            '<tr>'+
                            '<td id="tatle">本版本合同的原始信息</td>'+
                            '<td><span class="ty-color-blue" data-id="'+list[r].id+'" data-frontid="0" onclick="contractRecordScan($(this), \'change\')">查看</span>' +
                            '<span class="hd">'+ JSON.stringify(list[r]) +'</span>' +
                            '</td>'+
                            '<td>'+list[r].createName+'&nbsp;'+new Date(list[r].createDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                            '</tr>';
                    }else{
                        var front = Number(r) -1;
                        str +=
                            '<tr>'+
                            '<td id="tatle">第'+r+'次修改后</td>'+
                            '<td><span class="ty-color-blue" data-id="'+list[r].id+'" data-frontid="'+list[front].id+'" onclick="contractRecordScan($(this), \'change\')">查看</span> ' +
                            '<span class="hd">'+ JSON.stringify(list[r]) +'</span>' +
                            '</td>'+
                            '<td>'+list[r].updateName+'&nbsp;'+new Date(list[r].updateDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                            '</tr>';
                    }
                }
            } else {
                $("#modRecord .recordTip").html('当前资料尚未经修改。');
                $("#modRecord .recordEditer").html('');
            }
            $("#contractList tbody").html(str);
        }
    })
}*/
// creator: sy 2022-07-10 比较修改记录中前后修改的不同
function compareD(front,now){
    if(front == now){
        return '<span>' + handleNull(now) +' </span>'
    }else{
        return '<span class="redFlag">' + handleNull(now) + '</span>'
    }
}
// creator: sy 2022-07-05 供应商常规信息修改-联系人信息删除按钮
function contactDel(thisObj){
    var mavnil = thisObj.siblings(".hd").html();
    mavnil = JSON.parse(mavnil);
    // $("#upbookmess").data("liximess",mavnil);
    var id =mavnil.id;
    $.ajax({
        url:"../supplier/deleteCustomerContact.do ",
        data:{
            contactId:id
        },
        success:function(res){
            var status = res.success;
            if(status === "1"|| status === 1){
                // layer.msg("删除成功");
                supplierInfoEdit(editObj);//刷新函数方法
            }
        }
    })
}
// creator: sy 2022-07-07 联系人信息修改 （常规信息修改弹窗）
function contactUpdate(objc){
    var gyid = $("#updateSupPanel").data("id");
    var lixi = objc.parents("td").find(".hd").html();
    lixi = JSON.parse(lixi);
    $("#contactFlag").html("其他");
    var lixiid = lixi.id;
    $("#newContectInfo").data("cyid",lixiid);
    $("#mailName").data("font","upen");
    $("#newContectInfo").data("type","update");
    $("#newContectInfo").data("source","updateCustomer");
    $("#newContectInfo").data("add",0);
    $("#newContectInfo .bonceHead span").html("修改联系人信息");
    $(".qu1").show();
    $(".qu2").hide();
    $("#addContact1").hide();
    $("#addContact2").hide();
    $("#addContact").hide();
    $("#addContact3").show();
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    $("#uploadCard").html("");
    // $("#uploadCard .bussnessCard").show();
    $("#newContectData .otherContact #addone").hide();
    $("#newContectData .otherContact #addtwo").hide();
    $("#newContectData .otherContact #addthree").hide();
    $("#newContectData .otherContact #addfour").hide();
    $("#newContectData .otherContact #addfive").hide();
    initCardUpload($("#uploadCard"));
    $.ajax({
        url:"../supplier/getContactsSocial.do",
        data:{
            contactId:lixiid
        },
        success:function(res){
            let state = res.success;
            if(state === "1"|| state === 1){
                let data = res.data;
                let dunt = data;
                $("#contactName").val(`${dunt.name}`);
                $("#position").val(`${dunt.post}`);
                $("#contactNumber").val(`${dunt.mobile}`);
                var qImages =dunt.cardPath || "";       //这里的qImages就已经相当于是那里的normal了
                var imgStr = "";
                imgStr +=
                    '<div class="bussnessCard">' +
                    '	<div class="filePic" data-path="' + qImages + '" style="background-image: url(' + $.uploadUrl + qImages + ')"></div>' +
                    '   <span fileUid="' + data.fileUid + '" class="ty-color-red" onclick="cancleCard($(this))">删除</span> ' +
                    '</div>';
                if(qImages.length>0){
                    $('#uploadCard').hide();
                    $('#uploadCard').before(imgStr);
                }else{
                    $(".bussnessCard").html("");
                }
                var middle = dunt.socialList;
                for(var i =0;i<middle.length;i++){
                    let last1 = middle[i]['type'];
                    let answer = middle[i]['code'];
                    if(last1 == "1"){
                        $(".otherContact #addone #contractphone").html("");
                        $("#contractphone").val(`${answer}`);
                        $("#newContectData .otherContact #addone").show();
                    }else if(last1 == "2"){
                        $(".otherContact #addtwo #contractqq").html("");
                        $("#contractqq").val(`${answer}`);
                        $("#newContectData .otherContact #addtwo").show();
                    }else if(last1 == "3"){
                        $(".otherContact #addthree #contractemal").html("");
                        $("#contractemal").val(`${answer}`);
                        $("#newContectData .otherContact #addthree").show();
                    }else if(last1 == "4"){
                        $(".otherContact #addfour #contractweixi").html("");
                        $("#contractweixi").val(`${answer}`);
                        $("#newContectData .otherContact #addfour").show();
                    }else if(last1 == "5"){
                        $(".otherContact #addfive #contractwebo").html("");
                        $("#contractwebo").val(`${answer}`);
                        $("#newContectData .otherContact #addfive").show();
                    }
                }
            }
        }
    })

    bounce_Fixed2.show($("#newContectInfo"));
}
// creator: sy 2022-07-07 联系人信息修改记录（常规信息修改弹窗）
function contactUpdateLog(oblc){
    var type = 1;
    $(".recordTtl").html('联系人修改记录');
    var lin = oblc;
    lin= JSON.parse(lin);
    var liid = lin.id;
    $.ajax({
        url:'../supplier/getRecordContactList.do',
        data:{
            id:liid
        },
        success:function(res){
            var state = res.success;
            if(state === "1" || state === 1){
                getRecordslixi(res,type);
                // layer.msg("查询成功");
            }else{
                layer.msg("查询失败");
            }
        }
    })
}
// creator: sy 2022-07-12 点击供应商常规修改弹窗中的联系人信息里的修改记录
function contactUpdateLogi(lxjil){
    bounce_Fixed2.cancel($("#deletepeo"));
    $("#pacelook").hide();
    $("#upcane").show();
    var pank = lxjil.parent("td").find(".hd").html();
    contactUpdateLog(pank);
}
// creator: 施阳 2022-07-04 修改供应商 - 合同信息 查看
function contractBaseScan(contractId){
    $.ajax({
        url: $.webRoot + '/supplier/poContractBaseMes.do',
        data:{ id: contractId }
    }).then(res => {
        let data = res.data
        let contractBase = data.contractBase // 合同信息（大多数据都从这里取出，尤其是合同id，要用这个关键字下的）
        let listImage = data.listImage || [] // 合同的扫描件或照片
        let listTY = data.listMt || [] // 材料
        let listHis = data.listHis || [] // 暂停恢复记录
        bounce_Fixed2.show($("#cScan"))
        let imageStr = ''
        for (let i in listImage) {
            imageStr += `<span class="link-blue fileImScan" data-fun="imgScan" path="${listImage[i].uplaodPath}">${Number(i) + 1 } <i class="hd">${JSON.stringify(listImage[i])}</i></span>`
        }
        let fileStr = ''
        if (contractBase.filePath) {
            fileStr = ` <a class="link-blue cWord node" data-fun="cWord" path="${contractBase.filePath}">查看</a>`
        }
        let showData = {
            sn: contractBase.sn,
            signTime: contractBase.signTime?moment(contractBase.signTime).format("YYYY-MM-DD"):'',
            validTime: moment(contractBase.validStart).format("YYYY-MM-DD") + ' 至 ' + moment(contractBase.validEnd).format("YYYY-MM-DD"),
            memo: contractBase.memo,
            image: imageStr,
            file: fileStr,
            tyNum: listTY.length,
            create: contractBase.createName + ' ' + moment(contractBase.createDate).format("YYYY-MM-DD HH:mm:ss")
        }
        for (let key in showData) {
            $("#cScan .contract_see_" + key).html(showData[key])
        }
        $("#cScan").data("contractInfo", data)
        let suspendStr = ''
        listHis.forEach(function (enIm) {
            suspendStr += `
                <p>
                    <span>${ enIm.suspend ? "暂停履约/终止合作" : "恢复履约/重启合作" }</span>
                    <span class="enName">${ enIm.createName }</span>
                    <span>${ moment(enIm.suspendTime).format("YYYY-MM-DD HH:mm:ss") }</span>
                </p>`;
        })
        $("#cScan .enabledList").html(suspendStr)
    })
}
// creator:sy 2022-07-4 显示“暂停履约/终止合同”弹窗
function contractBaseStop(selector){
    var lank = selector.siblings(".hd").html();
    lank = JSON.parse(lank);//将字符串进行解析，方便获取值
    var spid = lank.id;
    $("#beforeof").show();
    $("#overstop").hide();
    $("#maisure").show();
    $("#stopaway").hide();
    // $("#newcontractpro").data("id",spid);
    $("#tiphoto").data("spid",spid).data("enabled",0);
    $("#prompttext").html("<span>确定后，相关材料将不再属于本合同。</span>" +
        "                    <span>确定进行本操作吗？</span>");
    bounce_Fixed.show($("#tiphoto"));
}
// creator: sy 2022-07-05 供应商常规信息修改-合同信息修改按钮
/*function contractBaseUpdate(thisObj){
    initCUpload2($("#cUpload2-1"),'img');
    initCUpload2($("#cUpload2-2"),'doc');
    let info = thisObj.siblings(".hd").html();
    info = JSON.parse(info);//解析数据
    let contractId = info.id;
    $("#newcontractpro .deleteFile").html('')
    $("#newcontractpro").data("id",contractId);
    $("#newcontractpro").data("type","update");//设置
    $("#newcontractpro").data("source","addCustomer");
    $("#newcontractpro .scanGs").data('gsArr',[]).data("gsArrNo",[]).html("0");
    $("#newcontractpro .bonceHead span").html("修改合同");
    $("#addpost1").hide();//添加材料
    $("#addpost2").show();//添加材料
    $("#movepost1").hide();//移出材料
    $("#movepost2").show();//移出材料
    $(".cRenewalTip").hide();//请录入续约合同信息
    $("#q1").show();//取消按钮
    $("#q2").hide();//取消按钮
    $("#q3").hide();//取消按钮
    $("#editContractOk1").hide();//提交按钮
    $("#updatebont").show();//提交按钮
    $("#newcontractpro .bounce_close").attr("onclick","updatebont(0)");

    $.ajax({
        "url": "../supplier/poContractBaseMes.do",
        "data": {'id': contractId},
        "cache":false,
        success:function(res){
            let data = res.data.contractBase;
            $("#connum").val(`${data.sn}`);
            $("#wrtdate").val(new Date(data.signTime).format("yyyy-MM-dd"));
            $(".cStartDatecon").val(`${new Date(data.validStart).format("yyyy-MM-dd")}`);
            $(".cEndDatecon").val(`${ new Date(data.validEnd).format("yyyy-MM-dd")  }`);
            let imgList = res.data.listImage || [];    //图片数据
            $("#newcontractpro").data("img",imgList);
            let mtList = res.data.listMt || []; //材料数据
            let wordlist = data.filePath;
            // let wordlist = data.enabledList || [];  //文件数据
            $("#newcontractpro").data("mtlist",mtList);
            $("#addpost2").data("add",mtList);
            var remove = $("#newcontractpro").data("mtlist");
            $("#goodLust").data("chooseGood",remove);
            $("#contdown").html(`${mtList.length}`);
            if(mtList.length == 0){
                $("#goodLust").html("");
            }else{
                let str1 = ``;
                for(let i in mtList){
                    let item = mtList[i];
                    str1 += `
                        <span id="gusn" class="gsIm">${item.name}、
                            <span class="hd">${JSON.stringify(item)}</span>
                        </span> `;
                }
                $("#goodLust").html(`${str1}`);
            }
            if(imgList.length == 0){
                $("#fileCon1-1").html("");
            }else{
                let imgStr = ``;
                for(let i in imgList){
                    imgStr +=`<span class="fileIm">
                    <span>${i*1 + 1}</span>
                    <span class="fa fa-times" ></span>
                    <span class="hd">${JSON.stringify(imgList[i])}</span>
                    </span>`;
                }
                $("#fileCon1-1").html(`${imgStr}`);
            }
            if(wordlist == null || wordlist == undefined || wordlist == ""){
                $("#fileCon2-1").html("");
            }else{
                let word1 = ``;
                word1 +=`<span class="fileIm"  >
                         <span class="fa fa-file-word-o"></span>
                         <span class="fa fa-times" onclick="empty($(this))"></span>
                         <span class="hd">${wordlist}</span>
                        </span>`;
                $("#fileCon2-1").html(`${word1}`);
            }
            $(".scanGs").html(`${mtList.length}种`);
            $(".cMemo").val(data.memo);
        }

    })
    bounce_Fixed3.show($("#newcontractpro"));
}*/
// creator: sy 2022-07-13 供应商常规信息修改-合同信息续约按钮
/*function contractBasexiu(thisObj){
    initCUpload2($("#cUpload2-1"),'img');
    initCUpload2($("#cUpload2-2"),'doc');
    let info = thisObj.siblings(".hd").html();
    info = JSON.parse(info);//解析数据
    let contractId = info.id;
    $("#newcontractpro").data("id",contractId);
    $("#addpost1").hide();
    $("#addpost2").show();
    $("#movepost1").hide();
    $("#movepost2").show();
    $("#updatebont").data("typeu",0);
    $("#q1").show();
    $("#q2").hide();
    $("#q3").hide();
    $("#file_upload_1-button").show();
    $("#editContractOk1").hide();
    $("#updatebont").show();
    $("#square").show();
    $("#newcontractpro .bounce_close").attr("onclick","updatebont(0)");
    contractBaseRenew(thisObj);
}*/
// creator:sy 2022-05 删除合同确定按钮
function contractDelTipOk(){
    let cInfop = JSON.parse(contractDel.siblings(".hd").html());
    if(cInfop.fileCon1.length > 0){
        let groupUuid = cInfop.fileCon1[0].groupUuid;
        cancelFileDel({type:'groupUuid',groupUuid:groupUuid},true);
    }
    if(cInfop.fileCon2.length >0){
        let groupUuid = cInfop.fileCon2[0].groupUuid;
        cancelFileDel({type:'groupUuid',groupUuid:groupUuid},true);
    }
    // var orgn = $("#updatchang").parent().parent();
    // orgn.html("");
    // $(".contractList table #clist1").html("");
    contractDel.parents("tr").remove();//删除数据内容
    bounce_Fixed.cancel();
}
// creator: sy 2022-05 批量导入弹窗
function contractShow(){
    //layer.msg("功能完善中，敬请期待。");
    $.ajax({
        url:"../supplier/import/getImportData",
        data:{},
        success:function(res){
            let exImports = res.exImports || [];//-无法保存的供应商
            let okImports = res.okImports || [];//-可保存的供应商
            let delImports = res.delImports || 0;//-已放弃的数量
            if(exImports.length > 0 || okImports.length > 0){
                //有exImports和okImports是[]，delImports=1的时候，delImports的情况先放着
                bounce.show($("#batchimport"));
            }else{
                $('#select_btn_1').val("");
                $('.matListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                importUploadFile();
                bounce.show($("#batimptble"));
            }
        }
    })
}
// creator: sy 2022-05-12 点击联系方式后的弹窗
function contway(obej){
    let lxn = obej.siblings(".hd").html();
    lxn = JSON.parse(lxn);
    // let lxx = lxn[0];
    let lid = lxn.id;
    $.ajax({
        url:"../supplier/getContactsSocial.do",
        data:{
            contactId:lid
        },
        success:function(res){
            var state = res.success;
            if(state === "1"|| state === 1){
                // layer.msg("查询成功");
                let date = res.data;
                date.name.length >= 0 ? $("#con-contactName").html(`${handleNull(date.name)}`):$("#con-contactName").html("");
                date.post.length >= 0 ? $("#con-contactpost").html(`${handleNull(date.post)}`):$("#con-contactpost").html("");
                date.tags == null || date.tags == 'null' ? $("#con-contacttag").html(""):$("#con-contacttag").html(`${handleNull(date.tags)}`);
                var str = ``;
                var lank = date.socialList || [];
                for(let i in lank){
                    str += `
                            <td>${lank[i].name}</td>
                            <td>${lank[i].code}</td>`;
                }
                $(".see-otherContact tbody tr").html(str);

            }
        }
    })
    $("#look1").show();
    $("#addmore").hide();
    bounce_Fixed.show($("#contacts"));
}
// creator: sy 2022-07-11 修改记录列表弹窗中“查看”按钮点击（联系人信息）
function cus_lxraddress(olbc){
    $(".initValue").html("");
    var json ={
        'id': olbc.data('id'),
        'frontId': olbc.data('frontid'),
    };
    $.ajax({
        url:"../supplier/getRecordContactDetails.do",
        data:{
            id:json.id,
            front:json.frontId
        },
        success:function(res){
            var status = res.success;
            if(status === "1"|| status === 1){
                // layer.msg("查询成功");
                let lanklx = res['data']['now'];//修改后的数组
                var frontData = res['data']['front']||[];   //修改前的数据
                let beforeSocialList = frontData?frontData.socialList:[];
                var socialfist = lanklx.socialList;
                var frontSocialList = [];
                if(frontData == null){
                    $("#lixilook .detaileTtl").html("原始信息");
                    $("#record_contactName").html(lanklx.name);
                    $("#record_position").html(lanklx.post);
                }else{
                    $("#lixilook .detaileTtl").html(olbc.siblings(".hd").html());
                    $("#record_contactName").html(compareD(frontData.name,lanklx.name));
                    $("#record_position").html(compareD(frontData.post,lanklx.post));
                }
                //var cardn = lanklx['cardPath'];
                $("#record_contactsCard").html("");
                if(lanklx.cardPath!=''&&lanklx.cardPath!=undefined&&lanklx.cardPath!=null){
                    var path = lanklx.cardPath;
                    $("#havelook").data("imgs",path);
                    var imgStr =
                        '<div class="bussnessCard">'+
                        '<div class="filePic" data-path="'+path+'"style="background-image: url('+$.fileUrl+path+')"></div>'+
                        '</div>';
                    $("#record_contactsCard").html(imgStr);
                }
                for(let q in socialfist){
                    let qItem = socialfist[q],getSname = false;
                    for(let w in beforeSocialList){
                        let wItem = beforeSocialList[w];
                        if(qItem['contactSocial'] == wItem['contactSocial']){
                            getSname = true;
                            if(qItem['name']!=wItem['name']){
                                qItem['nameChange']=true;
                            }
                            if(qItem['code']!=wItem['code']){
                                qItem['codeChange']=true;
                            }
                        }
                    }
                    if(!getSname){
                        qItem['nameChange'] = true;
                        qItem['codeChange'] = true;
                    }
                }
                socialfist.push({name:'手机',code:lanklx.mobile,codeChange:(lanklx.mobile!=frontData.mobile),type:"1"});
                let sortList = [];
                for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
                for(var r in socialfist){
                    let item = socialfist[r];
                    let _index = Number(item.type);
                    sortList[_index].push(item);
                }
                let sortAfter = [];
                for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
                let allStr = '';
                for(var t in sortAfter){
                    let item = sortAfter[t];
                    if(t%2===0){
                        allStr += `<tr><td class="${item.nameChange?'ty-color-red':''}">${item.name}</td><td class="${item.codeChange?'ty-color-red':''}">${item.code}</td>`
                    }else{
                        allStr += `<td class="${item.nameChange?'ty-color-red':''}">${item.name}</td><td class="${item.codeChange?'ty-color-red':''}">${item.code}</td></tr>`
                    }
                }
                if(sortAfter.length % 2 !== 0){
                    allStr += `<td> </td><td> </td></tr>`
                }
                $(".record_otherContact").html(allStr);
            }
            bounce_Fixed2.show($("#lixilook"));
        },
        error:function(msg){
            layer.msg("连接错误，请稍后重试!");
            return false;
        }
    })
}
// creator: sy 2022-07-05 修改记录列表弹窗中“查看”按钮点击(基本信息)
function cus_recordDetail(obj){
    $(".initValue").html("");
    var json ={
        'id': obj.data('id'),
        'frontId': obj.data('frontid')
    };
    $.ajax({
        url:"../supplier/getRecordBaseDetails.do",
        data:{
            id:json.id,
            front:json.frontId
        },
        success:function(res){
            var status = res.success;
            if(status === "1"|| status === 1){
                // layer.msg("查询成功");
                let lankj = res['data']['now'];//修改后的数组
                var fullname = lankj["fullName"];
                var name = lankj["name"];
                var code = lankj["codeName"];
                var qImages = lankj["qImages"];
                var weel = lankj["chargePeriod"];//账期多少天
                var pzy = lankj["chargeAcceptable"];//是否可接受挂账
                var begin = lankj["chargeBegin"];//账期开始日
                var imgse = lankj["isImprest"];//是否需要预付款
                var str = '';
                if(pzy == "1"){
                    str += '<p>可接受挂账,';
                    if(weel){
                        str += '账期<span>'+weel+'</span>天,';
                    }if(begin == "1"){
                        str += '自货物入库之日起计算,';
                    }else if(begin == "2"){
                        str += '自发票入账之日起计算,';
                    }
                }else{
                    str += '不接受挂账,';
                }if(imgse == "1"){
                    str += '需要预付款</p>'
                }else if(imgse == "2"){
                    str += '不确定需要预付款</p>'
                }else{
                    str += '不需要预付款</p>';
                }
                $("#jbsun_zweek").html(str);
                $("#jbxilook .detailTal").html("基本信息查看");
                $("#jbsun_fullname").html(fullname);
                $("#jbsun_name").html(name);
                $("#jbsun_node").html(code);
                $("#qImages .imgsthumb").remove();
                $("#qImages").data('orgData',qImages);
                if(qImages.length>0) {
                    var imgStr1 = '';
                    for (var a = 0; a < qImages.length; a++) {
                        var path = qImages[a].normal;
                        imgStr1 +=
                            '<div class="imgsthumb">' +
                            '   <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                            '   <a path="' + path + '" onclick="imgViewer($(this))">预览</a>' +
                            '</div>';
                    }
                    $("#jbsun_pitc").html(imgStr1);
                }else{
                    $("#jbsun_pitc").html("");
                }

            }
            bounce_Fixed2.show($("#jbxilook"));
        },
        error:function(msg){
            layer.msg("连接错误，请稍后重试!");
            return false;
        }
    })
}
// creator: sy 2022-07-09 修改记录列表弹窗中“查看”按钮点击（开票信息）
function cus_undefTailn(ojbc){
    var jsond={
        'id':ojbc.data("id"),
        'frontId':ojbc.data("frontid")
    };
    $.ajax({
        url:"../supplier/getRecordInvoiceDetails.do",
        data:{
            id:jsond.id,
            front:jsond.frontId
        },
        success:function(res){
            var state = res.success;
            if(state === "1"|| state ===1){
                // layer.msg("查询成功");
                let lankk = res['data']['now'];//修改后的数组
                var fullname = lankk["fullName"];
                var name = lankk["name"];
                var code = lankk["codeName"];
                var weel = lankk["taxRate"];
                var invoicable= lankk["invoicable"];
                var vatsPayable = lankk["vatsPayable"];
                $("#kpxilook .detailTal").html("开票信息查看");
                $("#kpsun_fullname").html(fullname);
                $("#kpsun_name").html(name);
                $("#kpsun_node").html(code);
                $(".uabe").html(weel);
                let choase = lankk["draftAcceptable"];
                let str1 = ``;
                if(invoicable == 0){   //不能开发票
                    str1 = `<p>该供应商不能开发票</p>`;
                    $(".kpsun_creat").html(`${str1}`);;
                } else if(invoicable != 0 && vatsPayable == "1" &&  choase == "1"){ //能开发票，能开增值税发票，能开汇票
                    str1 = `<p>该供应商能开税率为<span class="uabe">${weel}</span>%的增值税专用发票，可接受承兑汇票</p>`;
                    $(".kpsun_creat").html(`${str1}`);
                }else if(invoicable != 0 && vatsPayable == "1" && choase == "2"){ //能开发票，能开增值税发票，不能开汇票
                    str1 = `<p>该供应商能开税率为<span class="uabe">${weel}</span>%的增值税专用发票，不确定能接受承兑汇票</p>`;
                    $(".kpsun_creat").html(`${str1}`);
                } else if(invoicable != 0 && vatsPayable == "2" &&  choase == "1" ){   //能开发票，不能开增值税发票，能开汇票
                    str1 = `<p>该供应商不能开增值税专用发票，可接受承兑汇票</p>`;
                    $(".kpsun_creat").html(`${str1}`);
                }else if(invoicable != 0 && vatsPayable == "2" && choase == "2"){ //能开发票，不能开增值税发票，不能开汇票
                    str1 = `<p>该供应商不能开增值税专用发票，不确定能接受承兑汇票</p>`;
                    $(".kpsun_creat").html(`${str1}`);
                }
                // if(weel == null){
                //     str1 = `<p>该供应商不能开增值税专用发票，`;
                //     $(".kpsun_creat").html(`${str1}`);
                // }else{
                //     str1 =`<p>该供应商能开税率为<span class="uabe">${weel}</span>%的增值税专用发票，`
                //     $(".kpsun_creat").html(`${str1}`);
                // }
                // if(choase == "1"){
                //     str1 = `可接收承兑汇票</p>`;
                //     $(".kpsun_creat").html(`${str1}`);
                // }else if(choase == "0"){
                //     str1 = `<p>该供应商能开税率为<span class="uabe">${weel}</span>%的增值税专用发票，不可接收承兑汇票</p>`;
                //     $(".kpsun_creat").html(`${str1}`);
                // }
            }
            bounce_Fixed2.show($("#kpxilook"));
        },
        error:function(msg){
            layer.msg("连接错误，请稍后重试!");
            return false;
        }
    })
}
// creator: sy 2022-07-11 修改记录列表弹窗中“查看”按钮点击（邮寄信息）
function cus_yjaddress(odbc){
    $(".initValue").html("");
    var json ={
        'id': odbc.data('id'),
        'frontId': odbc.data('frontid')
    };
    $.ajax({
        url:"../supplier/getRecordShAddressDetails.do",
        data:{
            id:json.id,
            front:json.frontId
        },
        success:function(res){
            var status = res.success;
            if(status === "1"|| status === 1){
                // layer.msg("查询成功");
                let lankj = res['data']['now'];//修改后的数组
                let beforen = res['data']['front'];//修改前的数据
                var address = lankj['address'];
                var name = lankj['contact'];
                var phone = lankj['mobile'];
                var postcode = lankj['postcode'];
                if(beforen == null){
                    $("#yjdzlook .detailTal").html("原始信息");
                    $("#fpAddress").html(address);
                    $("#fpName").html(name);
                    $("#fpMobile").html(phone);
                    $("#fpNumber").html(postcode);
                }else{
                    $("#yjdzlook .detailTal").html(odbc.parent().parent().children("#tatle").html());
                    $("#fpAddress").html(compareD(beforen.address,lankj.address));
                    $("#fpName").html(compareD(beforen.contact,lankj.contact));
                    $("#fpMobile").html(compareD(beforen.mobile,lankj.mobile));
                    $("#fpNumber").html(compareD(beforen.postcode,lankj.postcode));

                }
            }
            bounce_Fixed2.show($("#yjdzlook"));
        },
        error:function(msg){
            layer.msg("连接错误，请稍后重试!");
            return false;
        }
    })
}

// creator: sy 2022-07-05 供应商常规信息修改-邮寄信息已被停用的数据按钮
function deactivatemess(){//已被停用的数据
    $("#stopsendms").data("type",2);
    var type = $("#stopsendms").data("type");
    var sondId = $("#updateSupPanel").data("id");
    $("#stopsendms tbody").children(":gt(0)").remove();
    $("#stopsendms .bonceHead span").html("已被停用的邮寄信息");
    $.ajax({
        url:"../supplier/getSuspendAddress.do",
        data:{
            supplierId:sondId
        },
        success:function(res){
            var status = res.success;
            if(status === "1"|| status === 1){
                var link = res['data']|| [], status = res['success'],str='';
                bounce_Fixed2.show($("#stopsendms"));
                for(let i in link){
                    let item = link[i];
                    str += `<tr data-id="${item.id}" data-isstop="ok">
                                <td>${item.address}<span class="ty-right"></span></td>
                                <td>
                                    <span class="edit2 ty-color-red" data-id="${item.id}" data-name="enableTurn" data-val="1" onclick="beganup($(this))">启用</span>
                                    <span class="ty-color-blue jilu" onclick="yjAddressback($(this))">修改记录</span>
                                    <span class="hd">${item.id}</span>
                                </td>
                            </tr>`;
                }
                $("#stopsendms tbody").append(str);
            }
        }
    })
}
// creator: sy 2022-07-13 已暂停供应商列表中点击“恢复采购”按钮后出现的弹窗中点击确定
function becameback(obje){
    let fhid = $("#qualification").data("hid");
    $.ajax({
        url:"../supplier/recoverySrmSupplier.do",
        data:{
            id:fhid
        },
        success:function(res){
            if(res.success === 1){
                // layer.msg("操作成功");
                bounce_Fixed.cancel($("#qualification"));
                stopcontent(1,20);
            }
        }
    })
}
// creator: sy 2022-05 点击删除后的提示
function deleteprompt(selector){
    bounce_Fixed.show($("#deletesure"));
    var deleteptid = selector.parents("tr").find(".hd").text();
    $("#deletesure").data("deleteptid",deleteptid);
    var thingo = selector.parents("tr").find(".thingsbox").html();
    $("#deletesure").data("box",thingo);
}
// creator: sy 2022-07-05 邮寄信息列表中“删除”按钮点击
function deletope(dent){
    var dee = dent.parent().parent();
    dee.remove();
}
// creator: sy 2022-05 点击删除弹窗中的确定按钮后删除供应商
function deletylst(){
    var depetentid = $("#deletesure").data("deleteptid");
    var deteid = parseInt(depetentid);
    deteid = Number(deteid);
    let box = $(".thingsbox").html();
    console.log('box的值',box);
    box = JSON.parse(box);
    $.ajax({
        url:"../supplier/deleteSrmSupplier.do",
        data:{
            id:deteid,
        },
        dataType:"text",
        success:function(res){
            // var status = res.success;
            console.log(res);
            res = JSON.parse(res);
            if(res.success === "1" || res.success === 1){
                // layer.msg("删除成功");
                bounce_Fixed.cancel($("#deletesure"));
                getContractMes(1,20);
            }else{//三秒提示
                layer.msg("操作失败，因为此供应商下已有材料数据！");
            }
        },
    })
}
// creator: sy 2022-09-02 删除
function detment(dont){
    var den = dont.parent().parent();
    den.html("");
}
// creator: sy 2024-01-26   点击确定按钮调用删除接口删除选中的错误数据
function dettend(checktem){
    let list = $("#dettend").data("link");
    let chosid = list.id;
    $.ajax({
        url:"../supplier/import/delImports",
        data:{
            id:chosid,
            operation:1
        },
        success:function(res){
            getimportment();
            switch (checktem) {
                case 2:
                    $("#truemess").show();
                    $("#rongmess").hide();
                    break;
                default:
                    $("#truemess").hide();
                    $("#rongmess").show();
                    break;
            }
            bounce.cancel($("#dettlepont"));
        }
    })
}
// creator: sy 2024-01-26   点击删除按钮展示删除提示弹窗
function dettenpo(keepd,numb){
    switch (numb) {
        case 2:
            let str1 = ``;
            str1 += `
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="dettend(2)" id="dettend">确定</span>
            `;
            $("#dettlepont .bonceFoot").html(str1);
            break;
        default:
            let str2 = ``;
            str2 += `
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="dettend()" id="dettend">确定</span>
            `;
            $("#dettlepont .bonceFoot").html(str2);
            break;
    }
    let json2 = keepd.next(".hd").html();
    json2 = JSON.parse(json2);
    $("#dettend").data("link",json2);
    bounce.show($("#dettlepont"));
}
// creator: sy 2022-07-05 联系人列表中点击“删除”按钮
function dettle(dotn){
    var ong = dotn.parent().parent().parent();
    ong.html("");
}

// creator: 李玉婷 2024-08-20 编辑合同确定
function editContractOk(num) {
    let type = $("#newContractInfo").data('type'); // new新增 update修改 cRenewal 续约
    let source = $("#newContractInfo").data('source');
    if(num === 0){ // 取消, 删除上传的文件
        bounce_Fixed.cancel()
        let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
        if(fileImArr.length > 0){
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }
        let file2= $("#newContractInfo .fileCon2 .fileIm")
        if(file2.length > 0){
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }

    }else{ // 确定
        let info = {}
        info.cNo = $("#newContractInfo .cNo").val()

        info.cSignDate = $("#newContractInfo .cSignDate").val()
        info.cStartDate = $("#newContractInfo .cStartDate").val()
        info.cEndDate = $("#newContractInfo .cEndDate").val()

        if(info.cNo.length === 0){
            layer.msg('请录入合同编号！');
            return false
        }
        if(!info.cStartDate || !info.cEndDate) {
            layer.msg('请选择合同的有效期！');
            return false
        }
        info.cMemo = $("#newContractInfo .cMemo").val()
        if(type === "cRenewal" || type === "cRenewalHistory" || type === 'cRenewalStop'){
            let cStartDateOld = $("#newContractInfo .cStartDate").data("old");
            let ccEndDateOld = $("#newContractInfo .cEndDate").data("old");
            let endDateOld = ccEndDateOld || cStartDateOld
            let endOld = Date.parse(new Date(ccEndDateOld));
            if(endDateOld){
                if(info.cStartDate){
                    let start = Date.parse(new Date( info.cStartDate));
                    if(endOld >= start){
                        layer.msg(`合同有效期开始时间应晚于${ccEndDateOld}`);
                        return false
                    }
                }else{
                    layer.msg(`请录入晚于${ccEndDateOld}的合同有效期开始时间`)
                    return false
                }
            }
        }
        if(info.cStartDate && info.cEndDate){
            let start = Date.parse(new Date( info.cStartDate));
            let end = Date.parse(new Date( info.cEndDate));
            if(start > end){
                layer.msg('合同有效期开始时间应早于结束时间');
                return false
            }
        }

        info.fileCon1 = [];
        $("#newContractInfo .fileCon1 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon1.push(JSON.parse(itemf))
        })
        info.fileCon2 = [];
        $("#newContractInfo .fileCon2 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon2.push(JSON.parse(itemf))
        })
        info.goodList = [];
        $("#newContractInfo .goodList .gsIm").each(function () {
            let itemg = $(this).find(".hd").html();
            info.goodList.push(JSON.parse(itemg))
        })
        let productListTY = $("#newContractInfo").data('productListTY') || []
        let editProductTY = productListTY.filter(item => item.isChecked)
        let tyGoods = editProductTY.map(item => {return {'material': item.id}})
        info.tyGoods = tyGoods
        if(source == "addCustomer"){
            let str = `
                <td>${info.cNo}</td>
                <td>${info.cSignDate}</td>
                <td>${info.cStartDate} 至 ${info.cEndDate}</td>
                <td>
                    <span class="link-blue node" data-name="contractInfo" data-type="update" data-source="addCustomer">修改</span>
                    <span class="link-red node" data-name="contractDel">删除</span>
                    <span class="hd" >${ JSON.stringify(info) }</span>
                </td>
                `
            if(type == "new"){
                $("#clist").append(`<tr>${str}</tr>`);
                $(".contractList").show();

            } else if(type == "update"){
                var trObj = editContractObj.parent().parent();
                trObj.html(str)
            }
            bounce_Fixed.cancel()
        }else  if (source == "updateCustomer"){
            let url = '', data = { 'customerId': $("#updateSupPanel").data("id") }
            let filePath = '',fileName = '';
            if( info.fileCon2.length > 0  ){
                filePath = info.fileCon2[0].filename
                fileName = info.fileCon2[0].originalFilename
            }
            let imgs = []
            if(info.fileCon1 && info.fileCon1.length > 0){
                info.fileCon1.forEach(function(im, index){
                    let imgItem = {
                        uplaodPath: im.filePath || im.uplaodPath,
                        type: 1, // 类型:1-图片,2-视频,3-文档
                        title: im.title
                    }
                    if (type === 'update') {
                        if (im.id) {
                            imgItem.id = im.id
                            imgItem.operation = 4 // 标签 1 用于表示新增 4 表示没动 2表示删除  ， 此处只有修改的时候需要传变化（即operation不是1的时候都需要传id）
                        } else {
                            imgItem.operation = 1
                        }
                    } else {
                        imgItem.operation = 1
                    }
                    imgs.push(imgItem)
                })
            }
            if (type === 'update') {
                $("#newContractInfo .deleteFile .fileItem").each(function () {
                    let info = JSON.parse($(this).find(".hd").html());
                    imgs.push({
                        uplaodPath: info.filePath || info.uplaodPath,
                        type: 1, // 类型:1-图片,2-视频,3-文档
                        title: info.title,
                        operation: 2, // 标签 4表示删除的文件
                        id: info.id
                    })
                })
            }

            let cusInfo  = $("#updateSupPanel").data("cusInfo")
            let productListTY = $("#newContractInfo").data('productListTY') || []
            let editProductTY = productListTY.filter(item => item.isChecked)
            let tyGoods = editProductTY.map(item => {return {'material': item.id}})
            data = {
                supplier: cusInfo.id,
                sn: info.cNo,
                contractSignTime: info.cSignDate,
                contractStartTime: info.cStartDate,
                contractEndTime: info.cEndDate,
                type: 1, // 1-商品合同
                memo: info.cMemo,
                contractBaseImages: JSON.stringify(imgs),
                mtList: JSON.stringify(tyGoods)
            }
            if( info.fileCon2.length > 0  ){
                data.filePath = filePath
                data.fileName = fileName
            }
            switch(type) {
                case 'cRenewal':
                    data.type = 1
                    break
                case 'cRenewalHistory':
                    data.type = 3
                    break
                case 'cRenewalStop':
                    data.type = 2
                    break
            }
            if(type == "new"){
                url = '../supplier/insertSupplierContract.do' // 修改过
            } else if(type == "update"){
                url = '../supplier/updateSupplierContract.do' // 修改过
                data.id = $("#newContractInfo").data("cid")
            } else if(type == "cRenewal" || type == "cRenewalHistory"|| type === 'cRenewalStop'){
                url = '../supplier/renewalSupplierContract.do'
                data.id = $("#newContractInfo").data("cid")
            }
            $.ajax({
                url: url,
                data: data ,
                success:function (res) {
                    let state = res.data.state
                    if(state === 1){
                        bounce_Fixed.cancel()
                        layer.msg("操作成功！")
                        let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
                        if(fileImArr.length > 0){
                            let info = JSON.parse(fileImArr.find(".hd").html())  ;
                            let groupUuid = info.groupUuid;
                            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} )
                        }
                        let file2= $("#newContractInfo .fileCon2 .fileIm")
                        if(file2.length > 0){
                            let info = JSON.parse(file2.find(".hd").html());
                            let groupUuid = info.groupUuid;
                            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid})
                        }
                        supplierInfoEdit(editObj, type);
                    } else if (state === 2) {
                        layer.msg("已续约不可修改!")
                    } else if (state === 3) {
                        layer.msg("修改的日期不能在上一个合同结束日之前!")
                    } else {
                        layer.msg("操作失败")
                    }
                }
            })
        }
    }
}
// creator: sy 2022-08-17 清除合同模板
function empty(){
    $("#newconbox .citem2 .fileCon #fileCon2-1").html("");
}

// creator: sy 2022-07-05 （常规信息修改弹窗）新增联系人
function famnew(){
    var type = "", source = "";
    $("#addAccount").data("contactInfo","[]");
    var milfuId = $("#updateSupPanel").data("id");
    $("#newContectInfo").data("type","new");
    $("#newContectInfo").data("source","addCustomer");
    type = $("#newContectInfo").data("type");
    source = $("#newContectInfo").data("source");
    document.getElementById('newContectData').reset();
    $("#addContact1").show();
    $("#addContact2").hide();
    $("#addContact3").hide();
    $("#addContact").hide();
    $(".qu1").show();
    $(".qu2").hide();
    $(".otherContact").html("");
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    $("#uploadCard").html("")
    initCardUpload($("#uploadCard"));
    if(type == "new"){
        $("#newContectInfo .bonceHead span").html('新增联系人');
        $("#contactFlag").html('其他');
        if($(".contactItem").length >= 50){
            layer.msg('最多可录入50条联系人。');
        }
        $("#newContectInfo").data("id", milfuId);
        $("#addMoreContact").hide();
    }
    bounce_Fixed2.show($("#newContectInfo"));
    setTimer('updateContact');
}
// creator: sy 2022-05-28 时间戳转换为日期
function formatDateTime(inputTime) {
    var date = new Date(inputTime);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? "0" + m : m;
    var d = date.getDate();
    d = d < 10 ? "0" + d : d;
    // var h = date.getHours();
    // h = h < 10 ? "0" + h : h;
    // var minute = date.getMinutes();
    // var second = date.getSeconds();
    // minute = minute < 10 ? "0" + minute : minute;
    // second = second < 10 ? "0" + second : second;
    // return y + "-" + m + "-" + d + " " + h + ":" + minute + ":" + second;
    return y + "-" + m + "-" + d;
}

// creator: sy 2022-08-10 联系人弹窗点击取消
function getallbc(lank){
    if(lank == 1){
        $("#newContectInfo").data("back","close");
    }else if(lank == 0){
        $("#newContectInfo").data("back","opon");
        bounce.show($('#addAccount'));
    }
    let type = $("#newContectInfo").data("back");
    bckoff(type,1);
}
// creator: sy 2022-08-10 邮寄信息弹窗点击取消
function getback(){
    $("#newMailInfo").data("back","close");
    let type=$("#newMailInfo").data("back");
    bounce.show($('#addAccount'));
    bckoff(type,2);
}
// creator: sy 2022-05-09 供应商搜索查询列表
function getChateMessage(currentPageNo,pageSize,keyword){
    $.ajax({
        url:"../supplier/searchSupplier.do",
        data:{
            "currentPageNo":currentPageNo,
            "pageSize":pageSize,
            "keyword":keyword
        },
        // dataType:"json",
        success:function(res){
            loading.close();
            console.log(res);
            $("#ye5").html("");
            var lest = res.data;
            var pagec = lest.pageInfo;
            var totalPage = pagec["totalPage"];
            var cur = pagec["currentPageNo"];
            var jsonStr = JSON.stringify({"keyword":keyword});
            setPage($("#ye_suppliecontract"),cur,totalPage,"cur",jsonStr);
            let lonbox = res.data.list || [];
            console.log(lonbox);
            if(lonbox!=undefined && lonbox.length>0){
                var number =0;
                for (var i=0;i<lonbox.length;i++){
                    number =1+i;
                    var inde=lonbox.length-1;
                    var str1 = "<span>"+lonbox.length+"</span>";
                    var str = "<tr>"+
                        '<td>'+handleNull(lonbox[i]["fullName"])+'/'+handleNull(lonbox[i]["codeName"])+'</td>'+
                        '<td>'+handleNull(lonbox[i]["createName"]) +'&nbsp;'+new Date(handleNull(lonbox[i]["createDate"])).format('yyyy-MM-dd hh:mm:ss') +'</td>'+
                        '<td>'+
                        '<span class="ty-color-blue" onclick="goone($(this))">' +
                        handleNull(lonbox[i]["supplyCount"])+'种</span>'+
                        '</td>'+
                        '<td>'+
                        '<span class="ty-color-blue" onclick="gotwo($(this))">' +
                        handleNull(lonbox[i]["exclusiveCount"])+'种</span>'+
                        '</td>'+
                        '<td>'+
                        '<span class="ty-color-blue" onclick="gothree($(this))">' +
                        handleNull(lonbox[i]["cutCount"]) +'种</span>'+
                        '</td>'+
                        '<td>'+
                        '<span class="ty-color-blue" onclick="havelooko($(this))">查看</span>'+
                        '<span class="ty-color-blue" onclick="supplierInfoEdit($(this))">修改</span>'+
                        '<span class="ty-color-red" onclick="deleteprompt($(this))">删除</span>'+
                        '<span class="ty-color-blue" data-name="stopbuy" onclick="stopgone($(this))">暂停采购</span>'+
                        '<span class="hd">'+handleNull(lonbox[i]["id"])+'</span>'+
                        '<span class="thingsbox" style="display: none;">'+handleNull(JSON.stringify(lonbox[i]))+'</span>'+
                        '</td>'+
                        "</tr>";
                    $("#ye5").append(str);
                    $("#chose .title span").html(str1);
                    $("#se0").val("");
                }
            }
        }
    })
}
// creator: sy 2022-05-02 获取联系人列表
function getContractList(list){
    var html = "", rhtml = "",slice =0;
    if(list && list.length >0){
        html =
            '<div class="leftList"> <table class="ty-table ty-table-control">'+
            '<thead style="background:none">'+
            '<tr>'+
            '<td>姓名</td>'+
            '<td>职位</td>'+
            '<td>操作</td>'+
            '</tr>'+
            '</thead><tbody id="clist3-1">';
        if(list.length >= 2) rhtml = html;
        for (var i=0;i < list.length; i++) {
            list[i]['number'] = list[i].number || list[i].id
            slice = i %2 ;
            if(slice > 0){
                rhtml += `
                    <tr data-id="${list[i].id}" groupUuid="${list[i].groupUuid}">
                        <td>${list[i].name}</td>
                        <td>${list[i].post}</td>
                        <td>
                            <span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer" onclick="upchange()">修改</span>
                            <span class="ty-color-red onel node" data-type="delete" data-name="contactInfo" id="deoter" onclick="dettle($(this))">删除</span>
                            <span class="hd">${JSON.stringify(list[i])}</span>
                        </td>
                    </tr>`
                // '<tr data-id="'+ list[i].number +'" groupUuid="'+list[i].groupUuid+'">' +
                // '    <td>' + list[i].name + '</td>' +
                // '    <td>' + list[i].post + '</td>' +
                // '    <td><span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer" onclick="upchange()">修改</span><span class="ty-color-red node" data-type="delete" data-name="contactInfo" onclick="dettle()">删除</span></td>' +
                // '</tr>';
            }else{
                html += `
                    <tr data-id="${list[i].number}" groupUuid="${list[i].groupUuid}">
                        <td>${list[i].name}</td>
                        <td>${list[i].post}</td>
                        <td>
                            <span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer" onclick="upchange()">修改</span>
                            <span class="ty-color-red node" data-type="delete" data-name="contactInfo" id="dettle" onclick="dettle($(this))">删除</span>
                            <span class="hd">${JSON.stringify(list[i])}</span>
                        </td>
                    </tr>`
                // '<tr data-id="'+ list[i].number +'" groupUuid="'+list[i].groupUuid+'">' +
                // '    <td>' + list[i].name + '</td>' +
                // '    <td>' + list[i].post + '</td>' +
                // '    <td>'+
                // '    <span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer" onclick="upchange()">修改</span><span class="ty-color-red node" data-type="delete" data-name="contactInfo" onclick="dettle()">删除</span>' +
                // '    <span class="hd">'+list[i].id+'</span>'+
                // '    </td>' +
                // '</tr>';
            }
        }
        html += '</tbody></table></div>';
        if(list.length >= 2) rhtml += '</tbody></table></div>';
    }
    var str = html + rhtml;
    var tt = JSON.stringify(list);
    $("#addAccount").data('contactInfo', tt);
    return str;
}
function addSupplierBtn(){
    if (remindState === 1){
        bounce.show($("#confirm_tip"));
    } else if (remindState === 2){
        addSupplierInfo()
    }
}
function noMoreTip(obj){
    obj.toggleClass("fa-circle");
}
function confirm_tipOk(obj){
    if ($("#confirm_tip .fa-circle").is(":visible")) {
        setNoRemind();
    }
    addSupplierInfo()
}
let remindState = 0;
function getContractRemind(){
    $.ajax({
        url:"../supplier/getContractRemind.do",
        data:{
            type: 1 //type 1-采购 2-销售
        },
        success:function(data){
            remindState = data.data.state    //1-需要提醒 2-不需提醒
        }
    })
}
function setNoRemind(){
    $.ajax({
        url:"../supplier/updateContractRemind.do",
        data:{
            type: 1 //type 1-采购 2-销售
        },
        success:function(data){
            remindState = 2
        }
    })

}

// creator: sy 2022-05-02 获得供应商基本信息列表
let supplierList = []
function getContractMes(currentPageNo,pageSize,keyword){
    bounce.cancel()
    // currentPageNo——当前页 pageSize——每页记录数
    $.ajax({
        url:"../supplier/getSupplierList.do",
        data:{
            "currentPageNo":currentPageNo,
            "pageSize":pageSize,
        },
        success:function(res){
            loading.close();
            console.log(res);
            var lest = res.data;
            console.log(lest);
            var pagec = lest.pageInfo;
            console.log(pagec);
            var number = pagec["totalResult"];
            var str1 = "<span>"+number+"</span>";
            $(".opinionCon .word span").html(str1);
            var totalPage = pagec["totalPage"];
            var cur = pagec["currentPageNo"];
            $("#ye_suppliecontract").html("");
            var jsonStr = JSON.stringify({"keyword":keyword});
            setPage($("#ye_suppliecontract"),cur,totalPage,"currentPageNo",jsonStr);
            $("#cotManage_body").html("");
            var lin = lest["list"] || [];
            supplierList = lin;
            $("#contractImport").data("sysall",lin);
            if(lin !=undefined && lin.length > 0){
                var number =0;
                for(var i=0;i<lin.length;i++){
                    number = 1+i;
                    var iden=lin.length-1;
                    let one = lin[i].supplyCount;
                    var str  = "";
                    str += `<tr>
                        <td id="otd" title="${lin[i].fullName}">
                        <span id="up">${handleNull(lin[i].name)+"/"+handleNull(lin[i].codeName)}</span>
                        <span id="two" style="display: none">${handleNull(lin[i].fullName)}</span>
                        <span id="three" style="display: none">${handleNull(lin[i].name)}</span>
                        </td>
                        <td>${handleNull(lin[i].createName)} &nbsp;${new Date(handleNull(lin[i].createDate)).format('yyyy-MM-dd hh:mm:ss')}</td>
                        <td>
                        <span class="ty-color-blue" onclick="goone($(this))" id="insider">
                        ${handleNull(lin[i]["supplyCount"])}种</span>
                        </td>
                        <td>
                        <span class="ty-color-blue" onclick="gotwo($(this))" id="fstinsider">
                        ${handleNull(lin[i]["exclusiveCount"])}种</span>
                        </td>
                        <td>
                        <span class="ty-color-blue" onclick="gothree($(this))" id="noidnst">
                        ${handleNull(lin[i]["cutCount"])}种</span>
                        </td>
                        <td>
                        <span class="ty-color-blue" onclick="havelooko($(this))">查看</span>
                        <span class="ty-color-blue" onclick="supplierInfoEdit($(this))">管理</span>
                        <span class="ty-color-red" onclick="deleteprompt($(this))">删除</span>
                        <span class="ty-color-blue" onclick="stopgone($(this))" id="stoppen">暂停采购</span>
                        <span class="hd">${handleNull(lin[i]["id"])}</span>
                        <span class="thingsbox" style="display: none;">${JSON.stringify(lin[i])}</span>
                        </td>
                        </tr>`;
                    $("#cotManage_body").append(str);
                }
            }
            $("#se0").val("");
        },
        error:function (meg) {
            loading.close();
            alert("连接错误，请稍后重试！");
        }
    })
    $(".dian").mouseover(function(){
        $("#cotManage_body tr #otd #up").hide();
        $("#cotManage_body tr #otd #two").show();
    });
    $(".dian").mouseout(function(){
        $("#cotManage_body tr #otd #two").hide();
        $("#cotManage_body tr #otd #up").show();
    });
}
// creator: sy 2024-01-26   获取错误的数据
function getimportment(uplink,diffent){
    $.ajax({
        url: "../supplier/import/getImportData",
        data:{},
        success:function(res){
            let exImports = res.exImports || [];//-无法保存的供应商
            let okImports = res.okImports || [];//-可保存的供应商
            let delImports = res.delImports || 0;//-已放弃的数量
            let exnumb = exImports.length;
            let oknumb = okImports.length;
            let delnumb = delImports;//delImports
            let sum = exnumb + oknumb;
            if(diffent == "updent"){
                if(exImports.length == 0){
                    $(".wrong").html(0);
                }
            }
            if(exImports.length > 0) {
                $("#roye1").html("");
                $(".allate").html(sum);
                $(".wrong").html(exnumb);
                $(".dittem").html(delnumb);
                let strex = ``;
                exImports.forEach(itemex => {
                    let name = itemex.name;
                    switch (name) {
                        case "":
                            name = itemex.fullName.substring(0,6);
                            break;
                        default:
                            name = name.substring(0,6);
                            break;
                    }
                    strex += `
                                <tr>
                                    <td class="userName">${handleNull(itemex.fullName)}</td>
                                    <td>${handleNull(name)}</td>
                                    <td>${handleNull(itemex.codeName)}</td>
                                    <td>
                                        <span class="ty-color-blue updentepo" data-type="update" data-name="upen" onclick="updentepo($(this))">修改</span>
                                        <span class="ty-color-red dettenpo" data-type="delete" data-name="deet" onclick="dettenpo($(this))">删除</span>
                                        <span class="hd">${JSON.stringify(itemex)}</span>
                                    </td>
                                </tr>
                            `;
                    $("#roye1").html(strex);
                })
            }else{
                $("#roye1").html("");
                $("#roye2").html("");
                $(".allmes").html(oknumb);
                $(".lastem").html(oknumb);
                $(".dittem").html(delnumb);
                console.log('uplink现在是什么格式',uplink);
                $("#rongmess").hide();
                $("#truemess").show();
                if(okImports.length == 0){}else{
                    okImports.forEach(item1 => {
                        let okid = item1.id;
                        if(uplink == undefined || uplink.length == 0){
                        }else{
                            uplink.forEach(itemup => {
                                let upid = itemup.id;
                                if(okid == upid){
                                    item1.fullName = itemup.fullName;
                                    item1.name = itemup.name;
                                    item1.codeName = itemup.codeName;
                                    item1.chargeAcceptable = itemup.chargeAcceptable;
                                    item1.isImprest = itemup.isImprest;
                                }
                            })
                        }
                    })
                    let strex2 = ``;
                    //思路：是对select进行监听，每次选择之后都将选择的数据存入那一行最后存储的json数据里？然后点击‘保存’的时候集体获取？
                    okImports.forEach(itemk => {
                        let name = itemk.name;
                        switch (name) {
                            case "":
                                name = itemk.fullName.substring(0,6);
                                break;
                            default:
                                name = name.substring(0,6);
                                break;
                        }
                        strex2 += `
                            <tr>
                                    <td>${handleNull(itemk.fullName)}</td>
                                    <td>${handleNull(name)}</td>
                                    <td>${handleNull(itemk.codeName)}</td>`;
                        let chopos = itemk.chargeAcceptable;
                        switch (chopos) {
                            case 1://接受
                                strex2 +=`
                                <td>
                                        <select class="chosgetpos" onchange="zetgetchon('geta',$(this))" value="0" style="text-align: center;"
                                            data-id="${itemk.id}">
                                            <option value="0">请选择</option>
                                            <option value="1" selected = "selected">接受</option>
                                            <option value="2">不接受</option>
                                        </select>
                                    </td>
                            `;
                                break;
                            case 0://不接受
                                strex2 +=`
                                <td>
                                        <select class="chosgetpos" onchange="zetgetchon('geta',$(this))" value="0" style="text-align: center;"
                                            data-id="${itemk.id}">
                                            <option value="0">请选择</option>
                                            <option value="1">接受</option>
                                            <option value="2" selected = "selected">不接受</option>
                                        </select>
                                    </td>
                            `;
                                break;
                            default:
                                strex2 +=`
                                <td>
                                    <select class="chosgetpos" onchange="zetgetchon('geta',$(this))" value="0" style="text-align: center;"
                                        data-id="${itemk.id}">
                                        <option value="0">请选择</option>
                                        <option value="1">接受</option>
                                        <option value="2">不接受</option>
                                    </select>
                                </td>
                            `;
                                break;
                        }
                        let chopaypos = itemk.isImprest;
                        switch (chopaypos) {
                            case 1://需要
                                strex2 +=`
                                <td>
                                    <select class="chospaypos" onchange="zetgetchon('pay',$(this))" value="0" style="text-align:center;"
                                        data-id="${itemk.id}">
                                        <option value="0">请选择</option>
                                        <option value="1" selected = "selected">需要</option>
                                        <option value="2">不需要</option>
                                        <option value="3">不确定</option>
                                    </select>
                                </td>
                            `;
                                break;
                            case 0://不需要
                                strex2 +=`
                                <td>
                                    <select class="chospaypos" onchange="zetgetchon('pay',$(this))" value="0" style="text-align:center;"
                                        data-id="${itemk.id}">
                                        <option value="0">请选择</option>
                                        <option value="1">需要</option>
                                        <option value="2" selected = "selected">不需要</option>
                                        <option value="3">不确定</option>
                                    </select>
                                </td>
                            `;
                                break;
                            case 2://不确定
                                strex2 +=`
                                <td>
                                    <select class="chospaypos" onchange="zetgetchon('pay',$(this))" value="0" style="text-align:center;"
                                        data-id="${itemk.id}">
                                        <option value="0">请选择</option>
                                        <option value="1">需要</option>
                                        <option value="2">不需要</option>
                                        <option value="3" selected = "selected">不确定</option>
                                    </select>
                                </td>
                            `;
                                break;
                            default:
                                strex2 +=`
                                <td>
                                    <select class="chospaypos" onchange="zetgetchon('pay',$(this))" value="0" style="text-align:center;"
                                        data-id="${itemk.id}">
                                        <option value="0">请选择</option>
                                        <option value="1">需要</option>
                                        <option value="2">不需要</option>
                                        <option value="3">不确定</option>
                                    </select>
                                </td>
                            `;
                                break;
                        }
                        strex2 +=`
                        <td>
                            <span class="ty-color-blue updentepo" data-type="update" data-name="upen" onclick="updentepo($(this),2)">修改</span>
                            <span class="ty-color-red dettenpo" data-type="delete" data-name="deet" onclick="dettenpo($(this),2)">删除</span>
                            <span class="hd">${JSON.stringify(itemk)}</span>
                        </td>
                    </tr>`;
                        $("#roye2").html(strex2);
                    })
                }
            }
            $("#nextstart").data("mslink",exImports);
            $("#nextstart").data("oklink",okImports);
            $("#nextstart2").data("lins",okImports);
            $(".chosgetpos").data("lins",okImports);
            $("#nextstart").data("nofolink",delImports);
        }
    })
}
// creator: sy 2022-07-12 点击基本信息后面的“修改记录”按钮
function getjbxxme(onr){
    $("#pacelook").hide();
    $("#upcane").show();
    getRecordList(onr);
}
// creator: sy 2022-07-12 点击开票信息后面的“修改记录”按钮
function getkpcord(kpn){
    $("#pacelook").hide();
    $("#upcane").show();
    getRecordList(kpn);
}
// creator: sy 2022-07-12 点击供应商查看弹窗中位于上面的“修改记录”按钮
function getlookup(unt){
    $("#pacelook").show();
    $("#upcane").hide();
    bounce_Fixed2.show($("#upbookmess"));
    let onk = $("#updateSupPanel").data("id");
    $("#havelook").data("id",onk);
    getRecordList(unt);
}
// creator: sy 2022-07-28 点击“修改开票信息”弹窗中的其中后面的数字(给增值税开专用发票）
function getMaterial(one){
    let unk = $("#addAccount .onez").html();
    // if(unk == 0){
    //     $("#addAccount .onez").attr("disabled",true);
    //     return false;
    // }
    bounce_Fixed.show($("#cmaterials"));
    $(".recordTtl").html("材料清单");
    var ktid = $("#updateSupPanel").data("id");
    var invoiceType = 1;
    var name = $("#supplie_main_dy").html();
    openmaterilist(name,ktid,invoiceType,1);    //supplie_main_dy
}
// creator: sy 2022-07-12 点击供应商查看弹窗中位于中间的“修改记录”按钮
function getmiddcord(mddle){
    $("#pacelook").show();
    $("#upcane").hide();
    bounce.show($("#havelook"));
    bounce_Fixed2.show($("#upbookmess"));
    let onk = $("#havelook").data("id");
    getRecordList(mddle);
}
// creator: sy 2022-07-05 新增联系人部分渲染
function getmiltruList(list){
    var html = "", rhtml = "",slice =0;
    if(list.length ==0){}else {
        for (var i = 0; i < list.length; i++) {
            html = `
            <tr id="famier" class="hd">
                <td>
                    <span>${list[i].name}</span>
                    <span>${list[i].post}</span>
                    <span>${list[i].mobile}</span>
                </td>
                <td>
                    <span class="ty-color-blue dfg" data-name="" onclick="" >修改
                    </span>
                    <span class="ty-color-blue dfg" data-type="contactRecords" data-obj="updatepeo"
                                  onclick="">修改记录
                    </span>
                    <span class="ty-color-red dfg" data-name="spStop2">删除
                    </span>
                </td>
            </tr>
            <tr class="insertItem">
                    <td class="">${ list[i].name } ${ list[i].post } ${ list[i].mobile }</td>
                    <td> 
                        <span class="ty-color-blue dfg" onclick="contactUpdate($(this))" >修改
                        </span>
                        <span class="ty-color-blue node dfg" onclick="contactUpdateLog($(this))" >修改记录</span>
                        <span id="contctdetion" class="ty-color-red node2 dfg " onclick="contactDel($(this))" >删除</span>
                        <span class="hd">${ JSON.stringify(list[i]) }</span>
                    </td>
                </tr>
                `
            $("#family").after(`${html}`);
        }
    }
    bounce_Fixed2.cancel($("#newContectInfo"));
}
// creator: sy 2022-07-05 渲染（常规修改）弹窗中合同信息新增
/*function gotNewList(list){
    if(list.length == 0){
        $(".insertItem").html("");
    }else{
        var html2=`
               <tr class="insertItem">
                    <td class="">${new Date(list.validEnd).format('yyyy-MM-dd') }到期/${list.sn}</td>
                    <td> 
                        <span class="ty-color-blue dfg" onclick="contractBaseScan($(this))" >查看</span>
                        <span class="ty-color-blue node dfg" data-name="contractInfo" data-type="update" data-source="updateCustomer">修改合同信息</span>
                        <span class="ty-color-blue dfg " onclick="contractBasexiu($(this))" >续约</span>
                        <span class="ty-color-red nodeon dfg " id="stopone" onclick="contractBaseStop($(this))" >暂停履约/合同终止</span>
                        <span class="hd">${ JSON.stringify(list) }</span>
                    </td>
                </tr>
            `
        $("#cotatinfomaton").after(`${html2}`);
    }
    var mailInfo = list;
    mailInfo = JSON.stringify(mailInfo);
    $("#addAccount").data("mailInfo",mailInfo);
    bounce_Fixed3.cancel($("#newcontractpro"));
    bounce_Fixed.show($("#updateSupPanel"));
}
// creator: sy 2022-07-05 渲染（常规修改）弹窗中合同信息修改
function gotNewList1(list){
    if(list.length == 0){
        $(".insertItem").html("");
    }else{
        var html3=`
               <tr class="insertItem">
                    <td class="">${ new Date(list.validEnd).format('yyyy-MM-dd') }/${list.sn}</td>
                    <td> 
                        <span class="ty-color-blue dfg" onclick="contractBaseScan($(this))" >查看</span>
                        <span class="ty-color-blue node dfg" data-name="contractInfo" data-type="update" data-source="updateCustomer">修改合同信息</span>
                        <span class="ty-color-blue dfg " onclick="contractBasexiu($(this))" >续约</span>
                        <span class="ty-color-red nodeon dfg " onclick="contractBaseStop($(this))" >暂停履约/终止合同</span>
                        <span class="hd">${ JSON.stringify(list) }</span>
                    </td>
                </tr>
            `
        $(".insertItem").html(`${html3}`);
    }
    var mailInfo = list;
    mailInfo = JSON.stringify(mailInfo);
    $("#addAccount").data("mailInfo",mailInfo);
    bounce_Fixed3.cancel($("#newcontractpro"));
}*/
// creator: sy 2022-07-28 点击“修改开票信息”弹窗中的其中后面的第二行数字（给开其它发票）
function getoterinvoices(two){
    let unk = $("#addAccount .twoz").html();
    // if(unk == 0){
    //     $("#addAccount .twoz").attr("disabled",true);
    //     return false;
    // }
    bounce_Fixed.show($("#cmaterials"));
    $(".recordTtl").html("材料清单");
    var ktid = $("#updateSupPanel").data("id");
    var invoiceType = 2;
    var name = $("#supplie_main_dy").html();
    openmaterilist(name,ktid,invoiceType,1);
}
// creator: sy 2022-07-03 修改记录
function getRecordList(oble){
    var type = oble.data('type');
    var getObj = oble.data('obj');
    var upsulId = "";
    if(getObj == 'see'){
        upsulId = $("#havelook").data("id");
    }else if(getObj == 'update'){
        upsulId = $("#updateSupPanel").data("id");
    }else if(getObj == 'interview'){
        upsulId = oble.parents("tr").data("id");
    }
    switch(type){
        case 'spbaserecord':
            $(".recordTtl").html("基本信息修改记录");
            $.ajax({
                url:"../supplier/getRecordBaseList.do",
                data:{
                    'id':upsulId
                },
                success:function(res){
                    var status = res.success;
                    if(status === '1' || status === 1){
                        getRecordsList(res,type);
                    }else{
                        layer.msg("查看失败");
                    }
                }
            })
            break;
        case 'spinvoiceRecords':
            $(".recordTtl").html('开票信息修改记录');
            $.ajax({
                url:'../supplier/getRecordInvoiceList.do',
                data:{
                    'id':upsulId
                },
                success:function(res){
                    var status = res.success;
                    if(status === '1' || status ===1){
                        getRecordsListq(res,type);
                    }else{
                        layer.msg("查看失败");
                    }
                }
            })
            break;
        case 'contactRecords':
            $(".recordTtl").html('联系人修改记录');
            $.ajax({
                url:'',
                data:{
                    'upsulId':obj.parents('tr').data('id')
                },
                success:function(data){
                    var status = data.status;
                    if(status === '1' || status === 1){
                        getRecordsList(data, type);
                    }else{
                        layer.msg("查看失败");
                    }
                }
            })
            break;
    }
}
// creator: sy 2022-07-05 修改记录列表数据获取(基本信息）
function getRecordsList(res,type){
    var getMess = res.data.list;
    if(getMess.length >0){
        var str = '';
        var eidtNumber = getMess.length -1;
        $(".createRecord .recordTip").html('当前数据为第'+ eidtNumber + '次修改后的结果');
        $(".createRecord .recordEditer").html('修改时间:'+new Date(res.data.updateDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").show();
        for (let r in getMess){
            if(r == '0'){
                str +=
                    '<tr>'+
                    '<td>原始信息</td>'+
                    '<td><span class="ty-color-blue" data-id="'+getMess[r].id+'" data-frontid="0" data-type="'+type+'" onclick="cus_recordDetail($(this))">查看</span></td>'+
                    '<td>'+getMess[r].createName+'&nbsp;'+new Date(getMess[r].createDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                    '</tr>';
            }else{
                var front = Number(r) -1;
                str +=
                    '<tr>'+
                    '<td>第'+r+'次修改后</td>'+
                    '<td><span class="ty-color-blue" data-id="'+getMess[r].id+'" data-frontid="'+getMess[front].id+'" data-type="'+type+'" onclick="cus_recordDetail($(this))">查看</span> </td>'+
                    '<td>'+getMess[r].updateName+'&nbsp;'+new Date(getMess[r].updateDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                    '</tr>';
            }
        }
        $("#upbookmess .changeRecord tbody").html(str);
    }else {
        $("#upbookmess .createRecord .recordTip").html('当前资料未经修改');
        // $("#upbookmess .createRecord .recordEditer").html("");
        $("#upbookmess .createRecord .recordEditer").html('创建人：' + res.data.createName + '&nbsp;&nbsp;' + new Date(res.data.createDate).format('yyyy-MM-dd hh:mm:ss'));
        $("#upbookmess .changeRecord").hide();
    }
    bounce_Fixed2.show($("#upbookmess"));
}
// creator: sy 2022-06-09 修改记录列表数据获取（开票信息）
function getRecordsListq(res,type){
    var getMess = res.data.list;
    if(getMess.length >0){
        var str = '';
        var eidtNumber = getMess.length -1;
        $(".createRecord .recordTip").html('当前数据为第'+ eidtNumber + '次修改后的结果');
        $(".createRecord .recordEditer").html('修改时间:'+new Date(res.data.updateDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").show();
        for (let r in getMess){
            if(r == '0'){
                str +=
                    '<tr>'+
                    '<td>原始信息</td>'+
                    '<td><span class="ty-color-blue" data-id="'+getMess[r].id+'" data-frontid="0" data-type="'+type+'" onclick="cus_undefTailn($(this))">查看</span></td>'+
                    '<td>'+getMess[r].createName+'&nbsp;'+new Date(getMess[r].createDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                    '</tr>';
            }else{
                var front = Number(r) -1;
                str +=
                    '<tr>'+
                    '<td>第'+r+'次修改后</td>'+
                    '<td><span class="ty-color-blue" data-id="'+getMess[r].id+'" data-frontid="'+getMess[front].id+'" data-type="'+type+'" onclick="cus_undefTailn($(this))">查看</span> </td>'+
                    '<td>'+getMess[r].updateName+'&nbsp;'+new Date(getMess[r].updateDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                    '</tr>';
            }
        }
        $(".changeRecord tbody").html(str);
    }else{
        $(".createRecord .recordTip").html('当前资料未经修改');
        // $(".createRecord .recordEditer").html("");
        $(".createRecord .recordEditer").html('创建人：'+ res.data.createName + '&nbsp;&nbsp;' + new Date(res.data.createDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").hide();
    }
    bounce_Fixed2.show($("#upbookmess"));
}
// creator: sy 2022-07-07 修改记录列表数据获取（联系人信息）
function getRecordslixi(res,type){
    var linxiMess = res.data.list;
    if(linxiMess.length >0){
        var str = '';
        var eidtNumber = linxiMess.length -1;
        $(".createRecord .recordTip").html('当前数据为第'+ eidtNumber + '次修改后的结果');
        $(".createRecord .recordEditer").html('修改时间:'+new Date(res.data.updateDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").show();
        for (let r in linxiMess){
            if(r == '0'){
                str +=
                    `<tr>
                    <td>原始信息</td>
                    <td>
                        <span class="ty-color-blue" data-id="${linxiMess[r].id}" data-frontid="0" data-type="${type}" onclick="cus_lxraddress($(this))">查看</span>
                         <span class="hd">原始信息</span>
                    </td>
                    <td>${linxiMess[r].createName}&nbsp;${new Date(res.data.createDate).format('yyyy-MM-dd hh:mm:ss')}</td>
                    </tr>`;
            }else{
                var front = Number(r) -1;
                str +=
                    `<tr>
                    <td>第${r}次修改后</td>
                    <td>
                            <span class="ty-color-blue" data-id="${linxiMess[r].id}" data-frontid="${linxiMess[front].id}" data-type="${type}" onclick="cus_lxraddress($(this))">查看</span>
                            <span class="hd">第${r}次修改后</span> 
                    </td>
                    <td>${linxiMess[r].updateName}&nbsp;${new Date(res.data.updateDate).format('yyyy-MM-dd hh:mm:ss')}</td>
                    </tr>`;
            }
        }
        $(".changeRecord tbody").html(str);
    }else{
        $(".createRecord .recordTip").html('当前资料未经修改');
        $(".createRecord .recordEditer").html("");
        //$(".createRecord .recordEditer").html('创建人：'+ res.data.createName + '&nbsp;&nbsp;' + new Date(res.data.createDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").hide();
    }
    bounce_Fixed2.show($("#upbookmess"));
}
// creator: sy 2022-07-07 修改记录列表数据获取 （邮寄地址）
function getRecordsyj(res,type){
    var yjxxMess = res.data.list;
    if(yjxxMess.length >0){
        var str = '';
        var eidtNumber = yjxxMess.length -1;
        $(".createRecord .recordTip").html('当前数据为第'+ eidtNumber + '次修改后的结果');
        $(".createRecord .recordEditer").html('修改时间:'+new Date(res.data.updateDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").show();
        for (let r in yjxxMess){
            if(r == '0'){
                str +=
                    '<tr>'+
                    '<td id="tatle">原始信息</td>'+
                    '<td><span class="ty-color-blue" data-id="'+yjxxMess[r].id+'" data-frontid="0" data-type="'+type+'" onclick="cus_yjaddress($(this))">查看</span></td>'+
                    '<td>'+res.data.createName+'&nbsp;'+new Date(res.data.createDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                    '</tr>';
            }else{
                var front = Number(r) -1;
                str +=
                    '<tr>'+
                    '<td id="tatle">第'+r+'次修改后</td>'+
                    '<td><span class="ty-color-blue" data-id="'+yjxxMess[r].id+'" data-frontid="'+yjxxMess[front].id+'" data-type="'+type+'" onclick="cus_yjaddress($(this))">查看</span> </td>'+
                    '<td>'+res.data.updateName+'&nbsp;'+new Date(res.data.updateDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                    '</tr>';
            }
        }
        $(".changeRecord tbody").html(str);
    }else{
        $(".createRecord .recordTip").html('当前资料未经修改');
        let bone = "";
        if(res.data.createName == null && res.data.createDate == null){
            $(".createRecord .recordEditer").html(bone);
        }else if(res.data.createName != null && res.data.createDate == null){
            $(".createRecord .recordEditer").html(bone);
        }else if(res.data.createName == null && res.data.createDate != null){
            $(".createRecord .recordEditer").html(bone);
        }else{
            $(".createRecord .recordEditer").html('创建人：'+ res.data.createName + '&nbsp;&nbsp;' + new Date(res.data.createDate).format('yyyy-MM-dd hh:mm:ss'));
        }
        $(".changeRecord").hide();
    }
    bounce_Fixed2.show($("#upbookmess"));
}
// creator: sy 2022-07-05 供应商查看弹窗中点击“暂停采购/恢复采购的操作记录”按钮
function getSuspendRecordList() {
    bounce_Fixed2.show($("#operation"));
    var customerId = $("#havelook").data("id");
    $.ajax({
        url:"../supplier/getStartAndStopList.do ",
        data:{
            id:customerId
        },
        success: function (res) {
            var data = res.data
            var str = ''
            for(var i in data) {

                str +=  '<tr>' +
                    '<td>' + (data[i].enabled == '1'?'暂停合作':'恢复合作') +'</td>' +
                    '<td>' + data[i].updateName + ' ' + formatTime(data[i].updateDate, true) + '</td>' +
                    '</tr>'
            }
            $("#suspendRecord tbody").html(str);
        }
    })
}
// creator: sy 2024-01-26   点击‘确定’按钮调用放弃功能接口
function giveupsure(keed){
    $.ajax({
        url:"../supplier/import/delImports",
        data:{
            operation:keed
        },
        success:function(res){
            bounce.cancel($("#giveupend"));
            $("#rongmess").hide();
            $("#truemess").hide();
            $("#home").show();
        }
    })
}
// creator: sy 2022-07-06 供应中的材料
function goone(oneder){
    //debugger;
    let lank = oneder.parents("tr").find(".thingsbox").html();
    lank = JSON.parse(lank);
    let messg = lank;
    if(messg.supplyCount == 0 || messg.supplyCount == null || messg.supplyCount == "null" || messg.supplyCount == undefined){
        $("#insider").attr("disabled",true);
        return false;
    }
    $("#home").hide();
    $("#searchdi").hide();
    $("#materials").show();
    var supplieId = oneder.parents("tr").find(".hd").text();
    var smallname = oneder.parents("tr").find("#three").text();
    materialsuppe(smallname,supplieId,1,1,20,1);

}
// creator: sy 2022-07-06 不再供应的材料
function gothree(threeder){
    let lank = threeder.parents("tr").find(".thingsbox").html();
    lank = JSON.parse(lank);
    let messg = lank;
    if(messg.cutCount == 0 || messg.cutCount == null || messg.cutCount == "null" || messg.cutCount == undefined){
        $("#insider").attr("disabled",true);
        return false;
    }
    $("#home").hide();
    $("#searchdi").hide();
    $("#nolonger").show();
    var supplieId = threeder.parents("tr").find(".hd").text();
    var smallname = threeder.parents("tr").find("#three").text();
    materialsuppe(smallname,supplieId,3,1,20,0);
}
// creator: sy 2022-07-06 独家供应的材料
function gotwo(twoder){
    let lank = twoder.parents("tr").find(".thingsbox").html();
    lank = JSON.parse(lank);
    let messg = lank;
    if(messg.exclusiveCount == 0 || messg.exclusiveCount == null || messg.exclusiveCount == "null" || messg.exclusiveCount == undefined){
        $("#insider").attr("disabled",true);
        return false;
    }
    $("#home").hide();
    $("#searchdi").hide();
    $("#exclusive").show();
    var supplieId = twoder.parents("tr").find(".hd").text();
    var smallname = twoder.parents("tr").find("#three").text();
    materialsuppe(smallname,supplieId,2,1,20,1);
}

// creator: sy 2022-05 处理null
function handleNull(str){
    // if(diffen == "middle"){
    //     let userNamelink = objen.next().next().children(".hd").html();//文字数据  该怎么获取到对应的供应商名称呢？
    //     userNamelink = JSON.parse(userNamelink);
    //     let userName = userNamelink.fullName;
    //     if(str == ""){
    //         str = userName.substring(0,6);//用于取一个文字数据的前六位
    //     }else{
    //         str = str.substring(0,6);
    //     }
    //     let result = str;
    //     return result;
    //     //var str = "这是一个字符串";
    //     // var newStr = str.substring(0, 6);
    //     // console.log(newStr);
    // }else{
    var result = str == null || str == undefined || str == 'null' || ''?'0':str;
    return result;
    // }
}
// creator: sy 2022-05 是否能开发票(新增供应商弹窗中的)
function haveInvoice(type,thisObj){
    setRadioSelect("haveInvoice", [0,1],type,thisObj);
    var haveInvoice = $("#haveInvoice").val();
    if(haveInvoice === "1"){
        $("#vatInvoice1").attr("class","fa fa-dot-circle-o");
        $("#vatInvoice2").attr("class","fa fa-circle-o");
        setRadioSelect("vatInvoice",[1,2],type,thisObj);
        var vatInvoice = $("#vatInvoice").val();
        $(".godemo_1").show();
        // $("#e_gRate0").val("");
    }else{
        $("#vatInvoice1").attr("class","fa fa-circle-o");//左侧清除点
        $("#vatInvoice2").attr("class","fa fa-circle-o");//右侧清除点
        // $("#e_gRate0").val("");//输入框清除填写
    }
    if(type === 1 || type === "1"){
        $(".godemo").show();
        $(".hp").show();
        // vatInvoice(1,thisObj);
        return false;
    }else{
        $(".godemo").hide();
        $(".hp").hide();
        // var chone = $("#addAccount").data("chose");
        // if(chone === "upode"){
        //     var two = $(".twoz").html();
        //     var three = $(".threez").html();
        //     if(two.length>0 ||  three.length>0){
        //         layer.msg("操作失败！\n" +
        //             "因为该供应商供应有开具发票的材料。\n" +
        //             "请先修改这些材料的价格！");
        //     }
        // }
    }
    return false;
}
// creator: sy 2022-07-05 点击列表中查看按钮
function havelooko(selector){
    editObj = selector ;
    $("#prient").hide();
    $("#isEdit").val(1);
    bounce.show($("#havelook"));
    $(".addOtherInfo").show();
    var lookid = selector.parents("tr").find(".hd").text();
    $("#havelook").data("id",lookid);
    if(lookid == undefined || lookid == ""){
        $("#tiphoto .shu1").html("加载错误，请稍后再试。");
        $("#beforeof").show();
        $("#overstop").hide();
        $("#maisure").show();
        $("#stopway").hide();
        bounce_Fixed.show($("#tiphoto"));
        return false;
    }
    $("#havelook").data("coid",lookid);
    getContract(1, 'see')
    $.ajax({
        url:"../supplier/getSrmSupplierOne.do",
        data:{
            id:lookid,
        },
        success:function(res){
            console.log(res);
            var tr_ser = selector.parent().parent();
            sui_seeTrser = tr_ser;
            var messge = res.data;
            var code = messge["codeName"];
            var oper = messge["operation"];
            var uaname = messge["name"];
            var uafullName = messge["fullName"];
            var createName = messge["createName"];
            var createDate = new Date(messge["createDate"]).format('yyyy-MM-dd hh:mm:ss');
            var qImages = messge["qImages"];
            var contactsList = messge["contactsList"] || [];//联系人
            var yjAddressList = messge["yjAddressList"] || [];//邮寄地址
            var contractBaseList = messge["contractBaseList"] || [];

            // var date = '';
            var strDate = '';


            var chargePeriod = messge["chargePeriod"] || 0;
            var taxRate = messge["taxRate"] || 0;
            $("#see_codde").html(code);
            $("#see_name").html(uafullName);
            $("#see_fullname").html(uaname);
            $("#see_createName").html(createName);
            $("#see_createDate").html(createDate);
            $("#qImages .imgsthumb").remove();
            $("#qImages").data('orgData',qImages);
            if(qImages.length > 0){
                var imgStr = '';
                for(var a=0;a<qImages.length;a++){
                    var path = qImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">'+
                        '   <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path +')"></div>'+
                        '   <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>'+
                        '</div>';
                }
                $("#overallImgUpload").html(imgStr);
            }else{
                $("#overallImgUpload").html("");
            }
            $("#overallImgUpload").html(imgStr);

            //账期
            var str1 = "";
            if(messge.chargeAcceptable == "1"){
                str1 +="可接受挂账,";
                if(messge.chargePeriod){
                    str1 +="账期<span>"+messge.chargePeriod+"</span>天,";
                }
                if(messge.chargeBegin == "1"){
                    str1 += "自货物入库之日起计算,";
                }else if(messge.chargeBegin == "2"){
                    str1 += "自发票入账之日起计算,";
                }
            }else{
                str1 += "不接受挂账,";
            }
            if(messge.isImprest == "1"){
                str1 += "需要预付款.";
            }else{
                str1 += "不需要预付款.";
            }
            $("#gotpose p").html(str1);

            //税率
            var str2 ="", str3 = '';
            let draftAcceptable = messge.draftAcceptable;
            if(messge.invoicable == "1"){
                if(messge.vatsPayable == "1"){
                    if(messge.taxRate){
                        str2 += "该供应商能开税率为"+messge.taxRate+"%的增值税专用发票,";
                    }else{
                        str2 += "该供应商能开增值税专用发票,";
                    }
                }else{
                    str2 += "该供应商仅能开普通发票,";
                }
            }else{
                str2 += "该供应商不能开发票.";
            }
            if(Number(draftAcceptable) === 2){
                str3 += "不确定能接受承兑汇票.";
            }else if(Number(draftAcceptable) === 1){
                str3 += "可接受承兑汇票.";
            }else if(Number(draftAcceptable) === 0){
                str3 += "不接受承兑汇票.";
            }
            $("#shuil p").html(str2 + str3);
            if(yjAddressList.length == 0){
                $("#clist21").html("");
            }else{
                var str2 =``;
                for(let i in yjAddressList){
                    let yjaddre = yjAddressList[i];
                    str2 +=`
                    <tr>                        
                        <td>${handleNull(yjaddre.address)}</td>                        
                        <td>${handleNull(yjaddre.name)}</td>                        
                        <td>${handleNull(yjaddre.postcode)}</td>                        
                        <td>${handleNull(yjaddre.mobile)}</td>                        
                        <td>                            
                            <span class='ty-color-blue node4' data-Obj="" onclick="yjccent($(this))">修改记录</span>                            
                            <span class="hd">${JSON.stringify(yjaddre.id)}</span>                        
                        </td>                    
                    </tr>`;
                }
                $("#clist21").html(str2);
            }
            if(contactsList.length == 0){
                $("#clist31").html("");
            }else{
                var str3 = ``;
                var str4 = ``;
                for(let j in contactsList){
                    let contacts = contactsList[j];
                    str3 += `
                    <tr>                        
                        <td>${contacts.name}</td>                        
                        <td>${contacts.post}</td>                        
                        <td>${contacts.mobile}</td>                        
                        <td>                            
                            <span class='ty-color-blue node' onclick="contway($(this))">联系方式</span>                            
                            <span class='ty-color-blue node' onclick="lookpictue($(this))" id="postcord">查看名片</span>                            
                            <span class='ty-color-blue nine node2' onclick="lxxgjl($(this))">修改记录</span>                            
                            <span class="hd">${JSON.stringify(contacts)}</span>                        
                        </td>                    
                    </tr>`;
                }
                $("#clist31").html(str3);
                str4 +=`${contactsList.length}`;
                $(".contactNum").html(str4);
            }
        }
    })
}
// creator: sy 2022-06-20 是否可接受汇票
function hui(type , thisObj) {
    setRadioSelect("hui", [1,2], type, thisObj);
}
// creator: sy 2022-06-05 是否可接受汇票
function huip(type, thisObj){
    setRadioCSelect("huip",[1,0],type,thisObj);
}

// creator: sy 2022-05-20 图片预览
function imgViewer(obj) {
    //debugger;
    var src = obj.attr('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");//图片淡入淡出效果
}
// creator: sy 2024-01-24   导入功能
function importOk(keyd){
    switch (keyd) {
        case 'cancel':
            bounce.cancel();
            break;
        default:
            let testlenth = $(".matListUpload").length;
            if(testlenth <= 0){
                layer.msg("选择一个文件后才能“导入”！");
            }else{
                let file = $("#leading .fileFullName").data('file');
                $.ajax({
                    url:"../supplier/import/import",
                    data:{
                        //filePath
                        filePath:file.path
                    },
                    success:function(res){
                        let code = res.code;
                        code = Number(code);
                        switch (code) {
                            case 2://导入失败
                                bounce_Fixed.show($("#Importfailed"));
                                break;
                            case 3://导入成功
                                wronmes('diffkep');
                                bounce.cancel($("#batimptble"));
                                break;
                        }
                    }
                })
            }
            break;
    }
}
// creator: sy 2024-01-24   文件预览
function importUploadFile(){
    $("#importUploadFile").html("");
    $("#importUploadFile").Huploadify({
        auto:true,
        fileTypeExts:'*.*;',
        multi:false,
        formData:{
            module: '供应商',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:10240,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader: "../uploads/uploadfyByFile.do",
        itemTemplate: '<div id="${fileID}" class="uploadify-queue-item matListUpload" style="display: none">' +
            '<div class="uploadify_bottom"><div><a class="uploadbtn" href="javascript:void(0);"></a></div></div>' +
            '</div>',
        onUploadStart:function(){},
        onInit:function(){},
        onSelect:function (file) {
            $('.matListUpload:not(:last)').remove();
            $('#leading .fileFullName').html(file.name);
        },
        onUploadComplete:function(file,data){},
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data);
            var filePath = data.filename;
            var fileUid =data.fileUid;
            let json = {
                "fileUid": fileUid,
                "path": filePath
            };
            $("#leading .fileFullName").data('file', json);
        },
        onCancel:function(file){}
    })
}
// creator: sy 2022-05-30 名片上传
function initCardUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        itemTemplate: '',
        formData:{
            module: '供应商',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",

        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var path = data.filename //路径（包含文件类型）
            obj.parent().attr("groupUuid", data.groupUuid )
            var imgStr =
                '<div class="bussnessCard">' +
                '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '   <span fileUid="' + data.fileUid + '" class="ty-color-red" onclick="cancleCard($(this))">删除</span> ' +
                '</div>';
            $('#uploadCard').hide();
            $('#uploadCard').before(imgStr);
        }
    });
}
// creator: sy 2022-08-27 图片上传
function initCUpload2(obj,type){
    let fileTypeExtsStr = ''
    let multi = true
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(type == "doc"){
        multi = false
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    }
    let itemTemplate = ``
    obj.html("")
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi: multi,
        buttonText:"上传",
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data)
            if(type == "img"){
                let len =  $(`.fileCon1`).find(".fileIm").length;
                if(len < 9){
                    data.orders = len + 1
                    data.filePath = data.filename
                    data.title = data.originalFilename
                    let imgStr1 = `<span class="fileIm"  >
                                 <span>${ 1 + len }</span>
                                 <span class="fa fa-times"></span>
                                 <span class="hd">${ JSON.stringify(data) }</span>
                            </span>`
                    $(`.fileCon1`).append(imgStr1);
                }else{
                    layer.msg('最多只能上传9个文件')
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                }
            }else if(type == "doc"){
                let len =  $(`.fileCon2`).find(".fileIm").length;
                if(len === 0){
                }else{
                    let delO = $(`.fileCon2`).find(".fileIm")
                    let info = JSON.parse(delO.find(".hd").html())
                    let fileUid = info.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                    delO.remove();
                }
                let str2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data) }</span>
                        </span>`
                $(`.fileCon2`).html(str2);
            }
        }
    })
}
// creator: sy 2022-06-10 图片上传
function initUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"上 传",
        formData:{
            module: '供应商',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",

        onUploadSuccess:function(file, data){
            var data = JSON.parse(data);
            let len =  obj.siblings("div.imgWall").find(".imgsthumb").length;
            if(len < 9){
                // $(".uploadify-queue").html("");
                //file 文件上传返回的参数  data 接口返回的参数
                var path = data.filename //路径（包含文件类型）
                //name = file.name,           //文件名称
                obj.parent().attr("groupUuid", data.groupUuid )
                var imgStr =
                    '<div class="imgsthumb">' +
                    '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                    '    <span fileUid="' + data.fileUid + '" onclick="cancleThis($(this))">删除</span> ' +
                    '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                    '</div>';
                obj.siblings("div.imgWall").append(imgStr);
            }else{
                layer.msg('最多只能上传9张')
                let fileUid = data.fileUid
                cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
            }
        }
    });
}
// creator: sy 2022-05 新增合同总图片文档上传
function initUpload1(obj,type){
    let fileTypeExtsStr = ''
    let multi = true
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(type == "doc"){
        multi = false
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    }
    let itemTemplate = ``
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi: multi,
        buttonText:"上传",
        formData:{
            module: '供应商',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",

        onUploadSuccess:function(file, data){
            data = JSON.parse(data)
            if(type == "img"){
                let len =  $(`.fileCon1`).find(".fileIm").length;
                if(len < 9){
                    data.orders = len + 1
                    data.filePath = data.filename
                    data.title = data.originalFilename

                    let ong = {
                        "filePath":data.filePath,
                        "orders":data.orders,
                        "title":data.title,
                        "type":1
                    };
                    let bonk = [];
                    bonk.push(ong);
                    $("#newcontractpro").data("new",bonk);

                    let imgStr1 = `<span class="fileIm"  >
                                 <span>${ 1 + len }</span>
                                 <span class="fa fa-times"></span>
                                 <span class="hd">${ JSON.stringify(data) }</span>
                            </span>`
                    $(`.fileCon1`).append(imgStr1);
                }else{
                    layer.msg('最多只能上传9个文件')
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                }
            }else if(type == "doc"){
                let len =  $(`.fileCon2`).find(".fileIm").length;
                if(len === 0){
                }else{
                    let delO = $(`.fileCon2`).find(".fileIm")
                    let info = JSON.parse(delO.find(".hd").html())
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                    delO.remove();
                }
                let str2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data) }</span>
                        </span>`
                $(`.fileCon2`).html(str2);
            }
        }
    })
}
// creator: sy 2022-06-30 点击“供应商常规信息的修改”弹窗中的开票信息后的“修改”按钮
function invoinfo(obj){
    bounce.show($("#addAccount"));
    var kpid = $("#updateSupPanel").data("id");//id
    $("#isEdit").val("1");
    $("#addAccount .bonceHead span").html("修改开票信息");
    // $("#addAccount").attr("class","bounce-blue");
    $("#addAccount").attr("class","bonceContainer bounce-blue");    //大神改的位置

    $("#addAccount").css("top","502px");
    // $("#addAccount .bonceCon").css("margin-left","0");
    // $("#addAccount .bonceCon #addsupplier #havenoce").css({"margin-bottom":"10px","margin-top":"20px"});
    // $("#havenoce .radioCon").css("margin-left","94px");
    // $("#contemo").css({"margin-top":"20px","margin-bottom":"10px"});
    // $("#contemo .radioCon").css("margin-left","80px");
    // $("#addAccount").css("top","0px");
    $("#addAccount").data("chose","upode");//用于进行开票点击后的提示的判断
    var trya = $("#addAccount").data("chose");
    console.log(trya);

    $(".one").hide();
    $("#paymore").hide();
    $(".upsth").hide();
    $("#getCredit").hide();
    $("#contdent").hide();
    $(".hang16").hide();
    $(".two").hide();
    $("#contemo").show();
    $(".addOtherInfo").hide();
    $("#addSuppliBtn").hide();
    $("#upSuppliBtn").hide();
    $(".hp").hide();
    $("#upinvoBtn").show();
    $("#havenoce").show();
    $("#contemo").show();
    $(".godemo_1").show();
    $(".order").show();
    $(".offerb").show();
    $("#ansow").attr("onclick","bounce.cancel()");
    $.ajax({
        url:"../saleSupplier/editSupplierInvoice",      //http://127.0.0.1/saleSupplier/editSupplierInvoice
        data:{ supplierId:kpid},//供应商id
        success:function(res){
            console.log(res);
            //var invoicable = res.invoicable;//是否能开发票
            //haveInvoice(invoicable);
            //var vatsPayable = res.vatsPayable;//是否能开增值税发票
            //vatInvoice(vatsPayable);
            //$("#e_gRate0").val(res.taxRate);//录入税率
            //var draftAcceptable = res.draftAcceptable;//是否可接受汇票
            //huip(draftAcceptable);

            var lank = res.vatsCount;//增专发票个数
            var one =lank == null || lank == undefined || lank == 'null' || ''?'0':lank;
            $("#addAccount .onez").html(one);

            var lonk = res.otherInvoiceCount;//其它发票个数
            var two = lonk == null || lonk == undefined || lonk == 'null' || ''?'0':lonk;
            $("#addAccount .twoz").html(two);

            var lbnk = res.notInvoiceCount;//不给开票的个数
            var three = lbnk == null || lbnk == undefined || lbnk == 'null' || ''?'0':lbnk;
            $("#addAccount .threez").html(three);

            var all = (Number(one)+ Number(two)+Number(three));
            $(".conone").html(all);
        }
    })
    bounce.show($("#addAccount"));
}
// creator: sy 2022-05 新增验证
function is_postcode(postcode){
    if( postcode == ""){
        return true;
    }else{
        if(!/^[0-9][0-9]{5}$/.test(postcode)){
            return false;
        }
    }
    return true;
}

// creator: sy 2024-01-26   点击‘确定’跳转对应页面
function keepsure(){
    let keep = $("#keepstre").data("keep");
    switch (keep) {
        case 1://继续上次的操作
            $.ajax({
                url:"../supplier/import/getImportData",
                data:{},
                success:function(res){
                    let exImports = res.exImports || [];//-无法保存的供应商
                    let okImports = res.okImports || [];//-可保存的供应商
                    let delImports = res.delImports || 0;//-已放弃的数量
                    let exnumb = exImports.length;
                    let oknumb = okImports.length;
                    let delnumb = delImports;
                    let sum = exnumb + oknumb;
                    if(exImports.length > 0){
                        $("#roye1").html("");
                        $(".allate").html(sum);
                        $(".wrong").html(exnumb);
                        let strex = ``;
                        exImports.forEach(itemex => {
                            let name = itemex.name;
                            switch (name) {
                                case "":
                                    name = itemex.fullName.substring(0,6);
                                    break;
                                default:
                                    name = name.substring(0,6);
                                    break;
                            }
                            strex += `
                                <tr>
                                    <td class="userName">${handleNull(itemex.fullName)}</td>
                                    <td>${handleNull(name)}</td>
                                    <td>${handleNull(itemex.codeName)}</td>
                                    <td>
                                        <span class="ty-color-blue updentepo" data-type="update" data-name="upen" onclick="updentepo($(this))">修改</span>
                                        <span class="ty-color-red dettenpo" data-type="delete" data-name="deet" onclick="dettenpo($(this))">删除</span>
                                        <span class="hd">${JSON.stringify(itemex)}</span>
                                    </td>
                                </tr>
                            `;
                            $("#roye1").html(strex);
                        })
                        $("#nextstart").data("mslink",exImports);
                        $("#truemess").hide();
                        $("#rongmess").show();
                        $("#home").hide();
                        bounce.cancel($("#batchimport"));
                    }else if(exImports.length == 0){
                        if(okImports.length > 0){
                            $("#roye2").html("");
                            $(".allmes").html(oknumb);
                            $(".lastem").html(oknumb);
                            $(".dittem").html(delnumb);
                            let strex2 = ``;
                            okImports.forEach(itemex2 => {
                                let name = itemex2.name;
                                switch (name) {
                                    case "":
                                        name = itemex2.fullName.substring(0,6);
                                        break;
                                    default:
                                        name = name.substring(0,6);
                                        break;
                                }
                                strex2 += `
                                <tr>
                                    <td>${handleNull(itemex2.fullName)}</td>
                                    <td>${handleNull(name)}</td>
                                    <td>${handleNull(itemex2.codeName)}</td>
                                    <td>
                                        <select class="chosgetpos" onchange="zetgetchon('geta',$(this))" value="0" style="text-align: center;">
                                            <option value="0">请选择</option>
                                            <option value="1">接受</option>
                                            <option value="2">不接受</option>
                                        </select>
                                    </td>
                                    <td>
                                        <select class="chospaypos" onchange="zetgetchon('pay',$(this))" value="0" style="text-align:center;">
                                            <option value="0">请选择</option>
                                            <option value="1">需要</option>
                                            <option value="2">不需要</option>
                                            <option value="3">不确定</option>
                                        </select>
                                    </td>
                                    <td>
                                        <span class="ty-color-blue updentepo" data-type="update" data-name="upen" onclick="updentepo($(this),2)">修改</span>
                                        <span class="ty-color-red dettenpo" data-type="delete" data-name="deet" onclick="dettenpo($(this),2)">删除</span>
                                        <span class="hd">${JSON.stringify(itemex2)}</span>
                                    </td>
                                </tr>`;
                                $("#roye2").html(strex2);
                            })
                            $("#truemess").show();
                            $("#home").hide();
                            $("#rongmess").hide();
                            bounce.cancel($("#batchimport"));
                            $(".chosgetpos").data("lins",okImports);
                            $(".chospaypos").data("lins",okImports);
                            $(".updentepo").data("lins",okImports);
                            $(".dettenpo").data("lins",okImports);
                            $("#nextstart2").data("lins",okImports);
                        }
                    }
                    $("#nextstart").data("mslink",exImports);
                    $("#nextstart").data("oklink",okImports);
                    $("#nextstart2").data("lins",okImports);
                    $(".chosgetpos").data("lins",okImports);
                    $("#nextstart").data("nofolink",delImports);
                    let iminlank = [];
                    iminlank = exImports.concat(okImports);//现在是单次导入的全部数据了
                    console.log('到底有没有值',iminlank);
                    $(".updentepo").data("inlinks",iminlank);//iminlank是包含正确和错误的所有数据
                }
            })
            break;
        case 2://放弃上次的操作，重新批量导入
            $.ajax({
                url:"../supplier/import/delImports",
                data:{
                    operation:3
                },
                success:function(res){
                    importUploadFile();
                    bounce.show($("#batimptble"));
                }
            })
            break;
    }
}

// creator: sy 2022-07-12 供应商查看窗口中“合同信息“里的查看
/*function lookcnent(lookli){
    bounce_Fixed.cancel();
    bounce_Fixed3.show($("#lookpoot"));
    $("#conook").show();
    $("#uppook").hide();
    $("#chone").hide();
    $("#stoppen").hide();
    let into = lookli.siblings(".hd").html();
    into = JSON.parse(into);
    let contractId = into.id;
    $.ajax({
        "url":"../supplier/poContractBaseMes.do",
        "data":{
            "id":contractId
        },
        success:function(res){
            let data = res.data.contractBase;
            $("#contentmain").html(`${data.createName} ${ new Date(data.createDate).format("yyyy-MM-dd")  } `);
            $("#contentmainSn").html(data.sn);
            $("#contentSignDate").html( new Date(data.signTime).format("yyyy-MM-dd") );
            $("#contentValidDate").html(`${new Date(data.validStart).format("yyyy-MM-dd")} 至 ${ new Date(data.validEnd).format("yyyy-MM-dd")  } `);

            let imgList = res.data.listImage || [];
            let mtList = res.data.listMt || [];
            $("#looken").data("link",mtList);


            let imgStr = ``, fileStr = `
            `
            imgList.forEach((imgItem,index)=>{
                imgStr += `<span class="ty-color-blue node" onclick="seePic($(this))" path="${ imgItem.uplaodPath }">${ index*1 +1 }</span>`
            });
            if (data.filePath) {
                fileStr = ` <a class="link-blue cWord node" data-fun="cWord" path="${data.filePath}" onclick="seeOnline($(this))">查看</a>`
            }
            $("#contentTxt").html(fileStr)
            $("#contentImgs").html(imgStr)
            $("#contentMemo").html( data.memo)
            $("#goodsNum").html(`${ mtList.length }种`)

            let enabledList = res.data.listHis || [];
            let enableStr ='';
            enabledList.forEach(function (enIm) {
                enableStr += `
                <p>
                    <span>${ enIm.suspend ? "暂停履约/终止合作" : "恢复履约/重启合作" }</span>
                    <span class="enName">${ enIm.createName }</span>
                    <span>${ moment(enIm.suspendTime).format("YYYY-MM-DD HH:mm:ss") }</span>
                </p>`;
            })
            $("#contentEnable").html(enableStr);
        }
    })
}*/
// creator: sy 2022-07-13 已暂停履约/终止的合同弹窗中“查看“按钮
function looknuc(seen){
    bounce_Fixed.cancel();
    bounce_Fixed3.show($("#lookpoot"));
    $("#conook").hide();
    $("#uppook").hide();
    $("#chone").hide();
    $("#stoppen").show();
    let seeid = seen.siblings(".hd").html();
    $.ajax({
        "url":"../supplier/poContractBaseMes.do",
        "data": { 'id': seeid },
        success:function(res){
            let data = res.data.contractBase;
            $("#contentmain").html(`${data.createName} ${ new Date(data.createDate).format("yyyy-MM-dd hh:mm:ss")  } `)
            $("#contentmainSn").html(data.sn)
            $("#contentSignDate").html( new Date(data.signTime).format("yyyy-MM-dd") )
            $("#contentValidDate").html(`${new Date(data.validStart).format("yyyy-MM-dd")} 至 ${ new Date(data.validEnd).format("yyyy-MM-dd")  } `)

            let imgList = res.data.listImage || [];
            let mtList = res.data.listMt || [];
            let imgStr = ``, fileStr = ``
            imgList.forEach((imgItem, index)=>{
                imgStr += `<span class="ty-color-blue node" onclick="seePic" path="${ imgItem.uplaodPath }">${ index*1 +1 }</span>`
            })
            $("#contentImgs").html(imgStr)
            $("#contentMemo").html( data.memo)
            $("#goodsNum").html(`${ mtList.length }种`);
            if (data.filePath) {
                fileStr = ` <a class="link-blue cWord node" data-fun="cWord" path="${data.filePath}" onclick="seeOnline($(this))">查看</a>`
            }
            $("#contentTxt").html(fileStr)
            let enabledList = res.data.listHis || [];
            let enableStr ='';
            enabledList.forEach(function (enIm) {
                enableStr += `
                <p>
                    <span>${ enIm.suspend ? "暂停履约/终止合作" : "恢复履约/重启合作" }</span>
                    <span class="enName">${ enIm.createName }</span>
                    <span>${ moment(enIm.suspendTime).format("YYYY-MM-DD HH:mm:ss") }</span>
                </p>`;
            })
            $("#contentEnable").html(enableStr);
        }
    })
}
// creator: sy 2022-09-15 供应商信息查看中查看名片弹窗
function lookpictue(bon){
    var pit = bon.siblings(".hd").html();
    pit = JSON.parse(pit);
    var unid = pit.id;
    $.ajax({
        url:"../supplier/getContactsSocial.do",
        data:{
            contactId:unid
        },
        success:function(res){
            var picture = res.data.cardPath;
            if(picture && picture != "" && picture != null){
                $('#poncune').html('<img style="width:300px;height: 300px" src="' + $.fileUrl + picture +'" />');
            }else{
                $('#poncune').html('');
            }
            bounce_Fixed2.show($("#phunte"));
        }
    })

}
// creator: sy 2022-08-11 点击新增供应商弹窗中新增邮寄信息下的选择联系人弹窗中的“查看”按钮
function lxrlook(ogjc){
    let lxr = ogjc.siblings(".hd").html();
    lxr = JSON.parse(lxr);
    lxr.name.length>=0 ? $("#con-contactName").html(`${handleNull(lxr.name)}`):$("#con-contactName").html("");
    lxr.post.length >= 0 ? $("#con-contactpost").html(`${handleNull(lxr.post)}`):$("#con-contactpost").html("");
    lxr.tags == null || lxr.tags == 'null' ? $("#con-contacttag").html(""):$("#con-contacttag").html(`${handleNull(lxr.tags)}`);
    $("#look1").hide();
    $("#addmore").show();
    bounce_Fixed2.cancel($('#newMailInfo'));
    bounce_Fixed3.cancel($('#chooseCusContact'));
    bounce_Fixed.show($("#contacts"));
}
// creator: sy 2022-07-12 点击供应商查看弹窗中的联系人信息中的“修改记录”
function lxxgjl(lxjli){
    let messg = lxjli.siblings(".hd").html();
    $("#pacelook").show();
    $("#upcane").hide();
    contactUpdateLog(messg);
}

//creator: sy 2022-07-02 邮寄信息修改（常规信息修改弹窗）
function mailupdent(){
    var mailId = $("#updateSupPanel").data("id");
    $("#addAccount").data("contactInfo","[]");
    $("#addAccount").data("receiveInfo","[]");
    $("#mailName").data("contractInfo","[]");
    $("#newMailInfo").data("type","update");
    $("#newMailInfo").data("source","updateCustomer");
    $("#newMailInfo").data("add",0);
    $("#newMailInfo .bonceHead span").html("修改邮寄信息");
    $("#before").hide();
    $("#after").show();
    $("#addMail").hide();
    $("#addMailml").show();
    var datao = {
        type:"2"
    }
    setAddress(2,datao);
    bounce_Fixed2.show($("#newMailInfo"));
}
// creator: sy 2022-06-31 暂停履约/终止合同
function maisure(){
    $("#beforeof").show();
    $("#overstop").hide();
    $("#maisure").show();
    $("#stopaway").hide();
    let id = $("#tiphoto").data("spid");
    let enabled = $("#tiphoto").data("enabled");
    $.ajax({
        url:"../supplier/terminateContract.do",
        data:{
            id:id,
            //enabled:enabled
        },
        success:function(res){
            if(res.success === 1){
                // layer.msg("操作成功");
                supplierInfoEdit(editObj);
            }
            bounce_Fixed.cancel($("#tiphoto"));
        }

    })
}
// creator: sy 2022-07-21 获取供应的材料列表
function materialsuppe(sname,id,state,currentPageNo,pageSize,enabled){
    var donnc = {
        "supplierId":id,
        "state":state,
        "enabled":enabled
    };
    $.ajax({
        type:"get",
        url:"../material/getSupplierMaterials",
        data:donnc,
        success:function(res){
            console.log(res);
            var list = res.data || [];
            if(list!=undefined && list.length>0){
                var number = 0;
                if(state === 1){
                    $("#boder1").html("");
                }else if(state === 2){
                    $("#boder2").html("");
                }else if(state === 3){
                    $("#boder3").html("");
                }
                for(var i=0;i<list.length;i++){
                    number =1+i;
                    var inde=list.length-1;
                    var str1 = '';
                    if(state === 1){
                        str1 += `<span>${sname}</span>供应中的材料共<span>${handleNull(list.length)}</span>种,具体如下:`;
                    }else if(state === 2){
                        str1 += `<span>${sname}</span>独家供应的材料共<span>${handleNull(list.length)}</span>种,具体如下:`;
                    }else if(state === 3){
                        str1 += `<span>${sname}</span>供应过但不再供应的材料共<span>${handleNull(list.length)}</span>种,具体如下:`;
                    }
                    var str2 = '';
                    str2 +=`
                        <tr>
                            <td style="text-align: center;">${handleNull(list[i]['name'])}</td>
                            <td style="text-align: center;">${handleNull(list[i]['code'])}</td>
                            <td style="text-align: center;">${handleNull(list[i]['model'])}</td>
                            <td style="text-align: center;">${handleNull(list[i]['specifications'])}</td>
                            <td style="text-align: center;">${handleNull(list[i]['unit'])}</td>
                            <td style="text-align: center;">${handleNull(list[i]['create_name'])} &nbsp;${new Date(handleNull(list[i]['create_date'])).format('yyyy-MM-dd hh:mm:ss')}</td>
                        </tr>`;
                    if(state === 1){
                        $("#titale1").html(str1);
                        $("#boder1").append(`${str2}`);
                    }else if(state === 2){
                        $("#titale2").html(str1);
                        $("#boder2").append(`${str2}`);
                    }else if(state === 3){
                        $("#titale3").html(str1);
                        $("#boder3").append(`${str2}`);
                    }
                }
            }else{
                var str1 = '';
                if(state === 1){
                    str1 += `<span>${sname}</span>供应中的材料共<span>0</span>种,具体如下:`;
                }else if(state === 2){
                    str1 += `<span>${sname}</span>独家供应的材料共<span>0</span>种,具体如下:`;
                }else if(state === 3){
                    str1 += `<span>${sname}</span>供应过但不再供应的材料共<span>0</span>种,具体如下:`;
                }
                if(state === 1){
                    $("#titale1").html(str1);
                }else if(state === 2){
                    $("#titale2").html(str1);
                }else if(state === 3){
                    $("#titale3").html(str1);
                }
            }
        },
        error:function (msg){
            layer.msg("连接错误，请重试");
        }
    })
}

// creator: sy 2022-09-21 查看供应商信息弹窗中合同信息表格中的“本合同下的材料”数字
/*function naumber(link){
    let inid = link.siblings("#hd2").html();
    inid = JSON.parse(inid);
    let contractId = inid.id;
    $.ajax({
        "url":"../supplier/poContractBaseMes.do",
        "data":{
            "id":contractId
        },
        success:function(res){
            let data = res.data;
            let mtList = res.data.listMt || [];
            setContractBN(mtList,false);
            $("#addmor").hide();
            $("#updunt").hide();
            $(".cloose").hide();
            $(".getout").hide();$("#mtSeeBtn").hide();
            $(".fxclo").show();
        }
    })
}*/
// creator: sy 2024-01-26   点击‘下一步’展示弹窗
function nextstart(diffen){
    let allink = $("#nextstart2").data("lins");
    let standup = [];
    if(allink == undefined){}else{
        allink.forEach(itema => {
            let jsons = {};
            jsons.id = itema.id;
            jsons.name = itema.name;
            jsons.fullName = itema.fullName;
            jsons.codeName = itema.codeName;
            if(itema.chargeAcceptable !== null){
                jsons.chargeAcceptable = itema.chargeAcceptable;
            }else{
                jsons.chargeAcceptable = "";
            }
            if(itema.isImprest !== null){
                jsons.isImprest = itema.isImprest;
            }else{
                jsons.isImprest = "";
            }
            standup.push(jsons);
        })
        standup = JSON.stringify(standup);
    }
    switch (diffen) {
        case 2:
            $.ajax({
                url:"../supplier/import/importSave",
                type:"POST",
                contentType:"application/json",
                dataType:"json",
                data:standup,
                // data:{
                //     //id:'',
                //     //id:link.id,
                //     //name:'',
                //     //name:link.name,
                //     //fullName:'',
                //     //fullName:link.fullName,
                //     //codeName:'',
                //     //codeName:link.codeName,
                //     //chargeAcceptable:'',
                //     //chargeAcceptable:numa,
                //     //isImprest:'',当选择‘不确定’的时候传2
                //     //isImprest:payk
                // },
                success:function(res){
                    if(res == 1){//保存成功
                        getContractMes(1,20);
                        $("#truemess").hide();
                        $("#home").show();
                    }
                }
            })
            break;
        default:
            let link = $("#nextstart").data("mslink");
            let oklink = $("#nextstart").data("oklink");
            let dellist = $("#nextstart").data("nofolink");
            let linknum = link.length || 0;
            let oknum = oklink.length || 0;
            let sum = linknum + oknum;
            if(link == undefined || link.length == 0) {//已经没有错误的数据了
                $("#roye2").html("");
                $(".allmes").html(oknum);
                $(".lastem").html(oknum);
                $(".dittem").html(dellist);
                let strex2 = ``;
                oklink.forEach(itemex2 => {
                    let name = itemex2.name;
                    switch (name) {
                        case "":
                            name = itemex2.fullName.substring(0,6);
                            break;
                        default:
                            name = name.substring(0,6);
                            break;
                    }
                    strex2 += `
                                <tr>
                                    <td>${handleNull(itemex2.fullName)}</td>
                                    <td>${handleNull(name)}</td>
                                    <td>${handleNull(itemex2.codeName)}</td>
                                    <td>
                                        <select class="chosgetpos centten" onchange="zetgetchon('geta',$(this))" value="0">
                                            <option value="0">请选择</option>
                                            <option value="1">接受</option>
                                            <option value="2">不接受</option>
                                        </select>
                                    </td>
                                    <td>
                                        <select class="chospaypos centten" onchange="zetgetchon('pay',$(this))" value="0">
                                            <option value="0">请选择</option>
                                            <option value="1">需要</option>
                                            <option value="2">不需要</option>
                                            <option value="3">不确定</option>
                                        </select>
                                    </td>
                                    <td>
                                        <span class="ty-color-blue updentepo" data-type="update" data-name="upen" onclick="updentepo($(this),2)">修改</span>
                                        <span class="ty-color-red dettenpo" data-type="delete" data-name="deet" onclick="dettenpo($(this),2)">删除</span>
                                        <span class="hd">${JSON.stringify(itemex2)}</span>
                                    </td>
                                </tr>`;
                    $("#roye2").html(strex2);
                })
                $(".chosgetpos").data("lins",oklink);
                $("#truemess").show();
                $("#home").hide();
                $("#rongmess").hide();
            }else{//还有错误的数据
                let alllis = link.length;
                $(".numer").html(alllis);
                bounce.show($("#nextpont"));
            }
            break;
    }
}
// creator: sy 2022-07-28 点击“修改开票信息”弹窗中的其中后面的第三行数字（不给开票）
function noinvoicingend(third){
    bounce_Fixed.show($("#cmaterials"));
    $(".recordTtl").html("材料清单");
    var ktid = $("#updateSupPanel").data("id");
    var invoiceType = 3;
    var name = $("#supplie_main_dy").html();
    openmaterilist(name,ktid,invoiceType,1);
}

// creator: sy 2022-06-26 点击“供应商常规信息的修改”弹窗中的基本信息后的“修改”按钮
function openbasic(obj){
    var objName = obj.data('name');
    var ueid = $("#updateSupPanel").data("id");
    $("#isEdit").val("1");
    $("#addAccount .bonceHead span").html("修改基本信息");
    initUpload($("#edit_panoramaBtn"));
    $("#addAccount").attr("class","bonceContainer bounce-blue");
    // $("#addAccount .bonceCon").css("margin-left","0");
    $("#addAccount").css("top","502px");//502+210
    // $("#addAccount").css("bottom","-10px");
    $("#beforeof").show();
    $("#overstop").hide();
    $("#maisure").show();
    $("#stopaway").hide();
    if(ueid == undefined || ueid == ""){
        $("#tiphoto #prompttext").html("系统错误，请稍后刷新重试");
        bounce_Fixed.show($("#tiphoto"));
        return false;
    }
    document.getElementById('splibox').reset();
    $(".upsth").show();
    $("#getCredit").show();
    $("#contdent").show();
    $(".hang16").show();
    $("#havenoce").hide();
    $("#contemo").hide();
    $(".addOtherInfo").hide();
    $(".one").hide();
    $(".two").hide();
    $(".order ").hide();
    $("#addSuppliBtn").removeClass("disabled");
    $("#addSuppliBtn").hide();
    $("#upSuppliBtn").show();
    $("#upinvoBtn").hide();
    $(".offerb").hide();
    $("#ansow").attr("onclick","bounce.cancel()");
    var name = $("#suppliername").val();
    var tname = $("#supplieruname").val();
    var node = $("#suppliernumber").val();
    $.ajax({
        url:"../supplier/getSupplierBase.do",
        data:{
            id:ueid,
        },
        success:function(res){
            console.log(res);
            var qImages = res.data.qImages;
            $("#addsupplier input[type='text']").each(function(){
                var name = $(this).attr('name');
                $(this).val(res.data.name);
                $(this).data('orgData',res.data.name);
            });
            $("#addsupplier input[type='nobe']").each(function(){
                var codeName  = $(this).attr('codeName');
                $(this).val(res.data.codeName);
                $(this).data('orgData',res.data.codeName);
            });
            $("#addsupplier input[type='tename']").each(function(){
                var fullName = $(this).attr('fullName');
                $(this).val(res.data.fullName);
                $(this).data('orgData',res.data.fullName);
            });
            $("#edit_qImages .imgsthumb").remove();
            $("#edit_qImages").data('orgData',qImages);
            if(qImages.length > 0){
                var html = setImgHtml(2, qImages);
                $("#edit_qImages .imgWall").html(html);
            }

            var chargeAcceptable = res.data.chargeAcceptable;
            setHangAccount(chargeAcceptable);

            var chargeBegin = res.data.chargeBegin;
            setStartDate(chargeBegin);

            var isImprest = res.data.isImprest;
            setAdvanceFee(isImprest);

            var imprestProportion = res.data.imprestProportion;
            if(imprestProportion === null || imprestProportion === "" || imprestProportion === "null"){
                $("#e_gRate1").val("");
            }else{
                $("#e_gRate1").val(imprestProportion);
            }

            var chargePeridod = res.data.chargePeriod;
            if(chargePeridod === null || chargePeridod === "" || chargePeridod === "null"){
                $("#spcontwek").val("");
            }else{
                $("#spcontwek").val(res.data.chargePeriod);
            }

            var imprestProportion = res.data.imprestProportion;
            if(imprestProportion === -1){//当选定比例不确定时
                $("#uncertainty1").attr("class","fa fa-dot-circle-o");
                // $("#e_gRate1").attr("disabled","disabled");
                // $("#e_gRate1").css("background","rgb(221, 221, 221)");
                $("#e_gRate1").val("");
            }else{
                // $("#e_gRate1").removeAttr("disabled");
                $("#e_gRate1").css("background","white");
                $("#uncertainty1").attr("class","fa fa-circle-o");
                $("#e_gRate1").val(imprestProportion);
            }
        }
    })
    bounce.show($("#addAccount"));
}
// creator: sy 2022-07-28 展示材料清单弹窗
function openmaterilist(name,supplierId,invoiceType,enabled){
    supplierId = Number(supplierId);
    invoiceType = Number(invoiceType);
    enabled = Number(enabled);
    $("#contactlist .recordTip span").html();
    $.ajax({
        url:'../material/getSupplierMaterials',
        data:{
            supplierId:supplierId,
            invoiceType:invoiceType,
            enabled:enabled
        },
        success:function(res){
            console.log(res);
            var list = res.data || [];
            var str1 = ``;
            var str2 = ``;
            if(list.length>0){
                if(invoiceType === 1){
                    str1 += `<span class="manayge">${name}</span>供应的材料中，给开增值税专用发票的共<span>${handleNull(list.length)}</span>种`;
                }else if(invoiceType === 2){
                    str1 += `<span class="manayge">${name}</span>供应的材料中，给开其他发票的共<span>${handleNull(list.length)}</span>种`;
                }else if(invoiceType === 3){
                    str1 += `<span class="manayge">${name}</span>供应的材料中，不给开票或不确定是否开票的共<span>${handleNull(list.length)}</span>种`;
                }
                for(var i =0;i<list.length;i++){
                    var indes = list.length -1;
                    str2 +=`
                        <tr>
                            <td class="centten">${handleNull(list[i]['name'])}</td>
                            <td class="centten">${handleNull(list[i]['code'])}</td>
                            <td class="centten">${handleNull(list[i]['model'])}</td>
                            <td class="centten">${handleNull(list[i]['specifications'])}</td>
                            <td class="centten">${handleNull(list[i]['unit'])}</td>
                        </tr>`;
                    //<td style="text-align: center;">${handleNull(list[i]['create_name'])} &nbsp;${new Date(handleNull(list[i]['create_date'])).format('yyyy-MM-dd hh:mm:ss')}</td>
                }
                if(invoiceType === 1){
                    $("#contactlist .recordTip").html(`${str1}`);
                    $("#coactbank #bothon").html(`${str2}`);
                }else if(invoiceType === 2){
                    $("#contactlist .recordTip").html(`${str1}`);
                    $("#coactbank #bothon").html(`${str2}`);
                }else if(invoiceType === 3){
                    $("#contactlist .recordTip").html(`${str1}`);
                    $("#coactbank #bothon").html(`${str2}`);
                }
            }else if(list==undefined || list.length == 0 || list!= [] || list != null){
                $("#contactlist .recordTip").html();
                var str3 = ``;
                if(invoiceType === 1){
                    str3 += `<span>供应的材料中，给开增值税专用发票的没有材料</span>`;
                }else if(invoiceType === 2){
                    str3 += `<span>供应的材料中，给开其他发票的没有材料</span>`;
                }else if(invoiceType === 3){
                    str3 += `<span>供应的材料中，不给开票或不确定是否开票的没有材料</span>`;
                }else{
                    str3 = `<span>该供应商下没有材料</span>`;
                }
                $("#contactlist .recordTip").html(`${str3}`);
                var str4 = `<tr></tr>`;
                $("#bothon").html(`${str4}`);
                return false;
            }
        }
    })
}
// creator: sy 2024-01-25   是否继续上次的操作
function operateport(type,thisObj){
    setRadioorhas("operateport",[1,2],type,thisObj);
    switch (type) {
        case 1:
            $("#keepstre").data("keep",1);
            break;
        case 2:
            $("#keepstre").data("keep",2);
            break;
    }
}
// creator: sy 2022-07-13 返回
function othersba(enave){
    bounce_Fixed3.cancel($("#newcontractpro"));
    bounce_Fixed2.show($("#overpoot"));
}
// creator: sy 2022-07-07 （常规信息修改弹窗）已被删除的数据
function overdelete(){
    $("#deletepo tbody").children(":gt(0)").remove();
    $("#deletepeo .bonceHead span").html("已被删除的联系人");
    $.ajax({
        url:"../supplier/getDeleteContactsList.do",
        data:{
            id:$("#updateSupPanel").data("id")
        },
        success:function(res){
            var state = res.success;
            if(state === "1" || state === 1){
            }
            let lins = res['data'] || [],status = res['status'],str='';
            bounce_Fixed2.show($("#deletepeo"));
            for(let i in lins){
                let item = lins[i];
                str += `<tr data-id="${item.id}" data-isstop="ok">
                                <td>${item.name}<span class="ty-right">${item.mobile}</span></td>
                                <td>
                                    <span class="ty-color-blue jilu" data-type="" onclick="contactUpdateLogi($(this))">修改记录</span>
                                    <span class="hd">${JSON.stringify(item)}</span>
                                </td>
                            </tr>`;
            }
            $("#deletepeo tbody").html(str);
        }
    })
}
// creator: sy 2022-07-01 供应商常规信息修改-合同信息已到期的合同按钮
/*function overfoot(){
    // bounce_Fixed.cancel();
    // $("#newcontractpro").data("img","");
    // $("#newcontractpro input").val("");
    var cid =$("#updateSupPanel").data("id");
    $.ajax({
        url:"../supplier/listContractBase.do",
        data:{
            id:cid,
            type: 3  //1-正常 2-终止 3-到期
        },
        success:function(res){
            var state = res.success;
            if(state === "1"|| state === 1){
                let doqi = res.data.contractBaseList || [];
                let strlixi = ``;
                for( let i in doqi){
                    let itam = doqi[i];
                    strlixi +=`
                        <tr data-id="${itam.id}" data-isstop="ok">
                            <td>${itam.sn}</td>
                            <td>${new Date (itam.validEnd).format("yyyy-MM-dd")}</td>
                            <td>
                                <span class="ty-color-blue" onclick="contractBaseScan($(this))">查看</span>
                                <span class="ty-color-blue" onclick="xytimeomre($(this))">续约</span>
                                <span class="hd">${JSON.stringify(itam)}</span>
                            </td>
                        </tr>`;
                }
                $("#timeover tr:gt(0)").remove();
                $("#timeover").append(strlixi);
                bounce_Fixed2.show($("#overpoot"));
            }
        }
    })
}*/

// creator: sy 2024-01-30   点击下一步提示弹窗中的确定按钮
function pacesure(){
    $.ajax({
        url:"../supplier/import/delImports",
        data:{
            operation:2
        },
        success:function(res){
            wronmes('true');
            bounce.cancel($("#nextpont"));
            $("#rongmess").hide();
            $("#truemess").show();
        }
    })
}
// creator: sy 2022-07-04 修改合同
function postupdate(){//修改合同
    $("#newcontractpro").data("type","update");
    $("#newcontractpro").data("source","updateCustomer");
    var type = $("#newcontractpro").data("type");
    var source = $("#newcontractpro").data("source");
    suptendInvoice(type,source,$(this));
}
// creator: sy 2022-06-12 已暂停采购的供应商列表中点击“0查看”
function prentmy(){
    bounce.show($("#havelook"));
}
// creator: sy 2022-06-15 已暂停采购的供应商列表中点击“恢复采购”按钮
function printen(selector){
    var upid = selector.parents("tr").find(".hd").text();
    $("#qualification").data("hid",upid);
    bounce_Fixed.show($("#qualification"));
}
// creator: sy 2022-07-01 供应商常规信息修改-合同信息新增按钮（前面部分）
/*function proaddnew(){
    // history.go(0);//清理缓存
    $("#newcontractpro").data("img","");
    $("#newcontractpro input").val("");
    // $("#newcontractpro").data("word",[]);
    initCUpload2($("#cUpload2-1"),'img');
    initCUpload2($("#cUpload2-2"),'doc');
    $(".cRenewalTip").hide();
    $("#addpost1").hide();
    $("#addpost2").show();
    $("#movepost1").hide();
    $("#movepost2").show();
    var ban = $("#goodLust").data("chooseGood");
    ban =[];
    $("#fileCon1-1").html("");
    $("#fileCon2-1").html("");
    $(".cNo").val("");
    $(".goodList").val("");
    $("#shiyan").html("");
    $('#wrtdate').val('');
    $('.cStartDatecon').val('');
    $(".cEndDatecon").val('');
    $(".fileCon").val('');
    $('.cMemo').val('');
    $(".goodList").html("");
    $("#newcontractpro").data("type","new");//设置
    $("#newcontractpro .scanGs").data('gsArr',[]).data("gsArrNo",[]).html("0");
    $("#newcontractpro").data("source","addCustomer");
    $("#newcontractpro .bonceHead span").html("新增合同");
    $("#newcontractpro .fileCon .fileCon1").html("");
    $("#q1").show();
    $("#q2").hide();
    $("#q3").hide();
    $("#square").hide();
    $("#editContractOk1").hide();
    $("#updatebont").show();
    $("#newcontractpro .bounce_close").attr("onclick","updatebont(0)");
    bounce_Fixed3.show($("#newcontractpro"));
}*/

// creator: sy 2022-06-14 删除添加的联系方式
function removeAdd(obj){
    obj.parent("li").remove();
    if($(".otherContact li").length > 0){
        $(".otherContact li").each(function(){
            var val = $(this).find("input").val();
            var type = $(this).find("input").data("type");
            if(type == '9' || type == '9') type = 6     //不是太明白，好好看看研究研究
            if(val == ''){
                $("#addMoreContact option").eq(type).prop('disabled',false);
            }else{
                $("#addMoreContact option").eq(type).prop('disabled',false);
            }
        })
    }else{
        $("#addMoreContact option").prop("disabled",false);
    }
}
// creator: sy 2022-07-04 续约合同
function renewconct(){//续约合同
    $("#newcontractpro").data("type","cRenewal");
    $("#newcontractpro").data("source","updateCustomer");
    var type = $("#newcontractpro").data("type");
    var source = $("#newcontractpro").data("source");
    suptendInvoice(type,source,$(this));
}

// creator: sy 2022-05 新增供应商
function addSupplierInfo(){
    //初始化构件
    $("#addSuppliBtn").prop("disabled",true);
    initUpload($("#edit_panoramaBtn"));
    $("#isEdit").val("0");
    $("#addAccount .bonceHead span").html("新增供应商");
    $("#addSuppliBtn").html("提交");
    $("#addAccount").attr("class","bonceContainer bounce-green");
    $("#havenoce").show();
    $("#addAccount").data("chose","add");//用于进行开票点击后的提示的判断
    $("#contemo").show();
    $(".offerb").hide();
    $(".addOtherInfo").show();
    $("#paymore").hide();
    $("#e_gRate1").val("");
    $("#clist").html("");
    $("#clist2").html("");
    $("#clist3-1").html("");
    $(".one").show();
    $("#addAccount  [type='hidden']").val('');
    $(".sale_c").show();
    $("#getCredit").show();
    $(".hang16").show();
    $(".two").show();
    $(".imgWall").html("");
    $("#e_gRate0").val("");
    $("#suppliername").val("");
    $("#supplieruname").val("");
    $("#suppliernumber").val("");
    $(".hang_7").hide();
    $("#spcontwek").val("");
    $(".imgsthumb").val("");
    $("#contdent").hide();
    $("#contemo").hide();
    $("#ansow").attr("onclick","bounce.cancel()");
    $("#addSuppliBtn").show();
    $("#upSuppliBtn").hide();
    $("#upinvoBtn").hide();
    $(".offerb").hide();
    $("#setHangAccount1").attr("class","fa fa fa-circle-o");
    $("#setHangAccount0").attr("class","fa fa fa-circle-o");
    $("#setStartDate2").attr("class","fa fa fa-circle-o");
    $("#setStartDate1").attr("class","fa fa fa-circle-o");
    $("#setAdvanceFee1").attr("class","fa fa fa-circle-o");
    $("#setAdvanceFee2").attr("class","fa fa fa-circle-o");
    $("#setAdvanceFee0").attr("class","fa fa fa-circle-o");
    $(".hp").hide();
    $(".supplerCon").hide();
    bounce.show($("#addAccount"));
    bounce.everyTime('1.5s','addAccount',function(){
        // var state =0;
        var mailLength = $("#addsupplier .receiveList tbody tr").length;
        var contactLength = $("#addsupplier .contectList tbody tr").length;
        var name=$("#supplieruname").val();
        var fullName = $("#suppliername").val();
        var codeName = $("#suppliernumber").val();
        if( name.length>0 && fullName.length>0 && codeName.length>0){
            $("#addSuppliBtn").prop("disabled",false);
        }
        if(mailLength >= 10){
            $("#addsupplier #newAddress").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
        }else{
            $("#addsupplier #newAddress").data("name","receiveInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }
        if(contactLength >=10){
            $("#addsupplier #newcontacts").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
        }else{
            $("#addsupplier #newcontacts").data("name","conreceiveInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }
    })
    $("#addAccount").data("contactInfo","[]");
    $("#addAccount").data("receiveInfo","[]");
}
// creator: sy 2022-05-31 点击提交进行新增供应商数据
// var unl="http://w4628873y7.qicp.vip";
// console.log(unl);
function sale_addsure(){
    var imgsQ = [];
    var dataou = {
        qImages:'',
        contactsList:'',//联系人列表
        yjAddressList :'',//邮寄地址合计
        contractBaseList:'',//合同集合
        contractBaseImages:'',
        chargeAcceptable:$("#setHangAccount").val(),//是否接受挂账
        chargeBegin:$("#setStartDate").val(),//从何时开始计算账期
        chargePeriod:$("#spcontwek").val(),//请录入已约定的账期
        isImprest:$("#setAdvanceFee").val(),//是否需要预付款
        imprestProportion:$("#e_gRate1").val(),//预付款比例
        uncertainty:$("#uncertainty").val()//比例不确定
    };
    if(dataou.uncertainty === "1"){
        if(dataou.imprestProportion === ""){
            dataou.imprestProportion = -1;
        }else{
            dataou.imprestProportion = dataou.imprestProportion;
        }
    }else{
        dataou.imprestProportion = dataou.imprestProportion;
    }

    var name = $("#supplieruname").val();
    var fullName = $("#suppliername").val();
    var codeName = $("#suppliernumber").val();
    //对必填选项进行监控
    if( name.length == 0){
        layer.msg("请填写供应商名称");
        return false;
    }else if( fullName.length == 0){
        layer.msg("请填写供应商简称");
        return false;
    }else if( codeName.length == 0){
        layer.msg("请填写供应商代号");
        return false;
    }else if(dataou.isImprest == 1){    //是否需要预付款       下面两个必须选一个（预付款比例和预付款比例不确定）
        if(dataou.imprestProportion == null || dataou.imprestProportion == ""){ //预付款比例
            if(dataou.uncertainty == null || dataou.uncertainty == ""){ //预付款比例不确定
                layer.msg("请填写预付款比例");
                return false;
            }
        }
    }
    //上传图片(获取图片的地址）
    var pitcure = [];
    $("#edit_qImages .imgWall .imgsthumb").each(function() {
        var path = {
            'normal':$(this).find(".filePic").data('path')
        };
        imgsQ.push(path);
    });
    imgsQ = JSON.stringify(imgsQ);
    dataou.qImages = imgsQ;
    let contractList = [];
    $("#clist tr").each(function(){
        let infoc = JSON.parse($(this).find(".hd").html())
        let filePath = ''
        let fileName = ''
        if( infoc.fileCon2.length > 0  ){
            filePath = infoc.fileCon2[0].filename
            fileName = infoc.fileCon2[0].originalFilename
        }
        let imgs = []
        if(infoc.fileCon1 && infoc.fileCon1.length > 0){
            infoc.fileCon1.forEach(function(im, index){
                imgs.push({
                    uplaodPath: im.filename,
                    order: index ,
                    type: 1,
                    title: im.originalFilename,
                    operation: 1
                })
            })
        }
        contractList.push({
            sn: infoc.cNo,
            contractSignTime: infoc.cSignDate,
            contractStartTime:infoc.cStartDate,
            contractEndTime:infoc.cEndDate,
            fileName: fileName,
            filePath: filePath,
            memo: infoc.cMemo,
            contractBaseImages: JSON.stringify(imgs),
            mtList: '[]'
        })
    })
    dataou.contractBaseList=JSON.stringify(contractList);

    var lonk = $("#clist3-1").children().children().find(".hd").html();
    if(lonk == undefined){
        lonk = [];
    }else{
        lonk = JSON.parse(lonk);
    }
    let yjAddressList  = [];
    $("#clist2 tr").each(function(){
        let infoc2 = JSON.parse($(this).find(".hd").html());
        console.log(infoc2);
        var contact = parseInt(infoc2.contact);
        var postcode = parseInt(infoc2.postcode);
        // infoc2 = [{}]
        // console.log(infoc2[0]);
        // var infoc28 = infoc2; infoc28应该是对象 infoc2是数组
        yjAddressList.push({
            "supplierContact":lonk.id,
            "name":infoc2.name,
            "address":infoc2.address,
            "mobile":infoc2.mobile,
            "contact":contact,
            "postcode":postcode
        })
    })
    dataou.yjAddressList=JSON.stringify(yjAddressList);

    let contactsList = [];
    $("#clist3-1 tr").each(function(){
        let infoc3 = JSON.parse($(this).find(".hd").html());
        console.log(infoc3);
        var id = lonk.id;
        contactsList.push({
            "id":id,
            "name":infoc3.name,
            "post":infoc3.post,
            "mobile":infoc3.mobile,
            "cardPath":infoc3.visitCard,
            "memo":infoc3.tags,
            "socialList":infoc3.socialList
        })
    })
    contactsList = JSON.stringify(contactsList);
    dataou.contactsList=contactsList;

    var chargeAcceptable = parseInt(dataou.chargeAcceptable);
    var chargeBegin = parseInt(dataou.chargeBegin);
    var chargePeriod = 0;
    if(dataou.chargePeriod >0){
        chargePeriod = parseInt(dataou.chargePeriod);
    }
    // var url="../supplier/addSupplier.do";
    // console.log(url);
    $.ajax({
        url:"../supplier/addSupplier.do",
        data:{
            name:name,//供应商简称
            fullName:fullName, //供应商名称
            codeName:codeName,//供应商代号
            chargeAcceptable:dataou.chargeAcceptable,//是否接受挂账
            chargeBegin:dataou.chargeBegin,//从何时开始计算账期
            chargePeriod:chargePeriod,//请录入已约定的账期
            isImprest:dataou.isImprest,//是否需要预付款
            imprestProportion:dataou.imprestProportion,//预付款的比例

            qImages:dataou.qImages,//图片
            contactsList:dataou.contactsList,//联系人列表
            yjAddressList :dataou.yjAddressList ,//邮寄地址合计
            contractBaseList: dataou.contractBaseList,//合同集合
        },
        success:function(data){
            console.log(data);
            // var pagenumber = $("#yecur").val();
            var status = data.success;
            if(status == 0){
                layer.msg("该供应商名称已存在");
                return false;
            }else{
                getContractMes(1,20);
                $("#clist1").html("");
                $("#clist2").html("");
                $(".leftList").html("");
                $("#fileCon1-1").html("");
                bounce.cancel();
            }
        },
        error:function(data){
            layer.msg("该供应商名称已存在");
            // alert("添加失败，请重试!");
        }
    })
    $(".imgWall").val("");
}
// creator: sy 2022-06-27 修改基本信息弹窗中点击确定按钮
function sale_updatesure(){
    var id = $("#updateSupPanel").data("id");
    $("#beforeof").show();
    $("#overstop").hide();
    $("#maisure").show();
    $("#stopaway").hide();
    if(id == undefined || id== ""){
        $("#tiphoto #prompttext").html("系统错误，请稍后刷新重试");
        bounce_Fixed.show($("#tiphoto"));
        return false;
    }
    var datao={
        'id':id,
        'chargeAcceptable':$("#setHangAccount").val(),//修改后的是否接受挂账
        'chargeBegin':$("#setStartDate").val(),//修改后的挂帐开始
        'chargePeriod':$("#spcontwek").val(),//修改后的账期
        'isImprest':$("#setAdvanceFee").val(),//修改后的是否需要预付款
        'imprestProportion':$("#e_gRate1").val(),//预付款比例
        'uncertainty':$("#uncertainty").val()
    };
    if(datao.chargeAcceptable == null || datao.chargeAcceptable == ""){
        // layer.msg("请选择是否接收挂账");
        // return false;
    }else if(datao.chargeAcceptable == "1"){
        if(datao.chargePeriod.length == 0){
            layer.msg("请录入账期");
            return false;
        }
    }else if(datao.chargeAcceptable == "0"){
        datao.chargePeriod = "";
    }
    // if(datao.chargeBegin == null || datao.chargeBegin == ""){
    //     layer.msg("请选择从何时开始计算账期");
    //     return false;
    // }else if(datao.isImprest == null || datao.isImprest == ""){
    //     layer.msg("请选择是否需要预付款");
    //     return false;
    // }
    if(datao.uncertainty === "1"){
        if(datao.imprestProportion === ""){
            datao.imprestProportion = -1;
        }else{
            datao.imprestProportion = datao.imprestProportion;
        }
        // $("#e_gRate1").attr("disabled","disabled");
        // $("#e_gRate1").css("background","rgb(221, 221, 221)");
    }else{
        datao.imprestProportion = datao.imprestProportion;
    }
    var name = $("#suppliername").val();
    var fullName = $("#supplieruname").val();
    var codeName = $("#suppliernumber").val();
    $("#addsupplier input[type='text']:visible").each(function(){
        var name = $(this).attr('name');
        datao[name]=$(this).val();
    })
    if($("#edit_qImages .imgWall .imgsthumb").length >0){
        var imgArr = [];
        $("#edit_qImages .imgsthumb").each(function(){
            var path = $(this).find(".filePic").data('path');
            var json = {
                'normal':path
            };
            imgArr.push(json);
        })
        // imgArr = JSON.stringify(imgArr);
        // datao = JSON.stringify(datao);
        // imgArr =datao.qImages;
        //const text = "abc";
        // const chars = text.split('');
        // console.log(chars);
        // //['a', 'b', 'c']
    }
    if(datao.chargePeriod == ""){
        var chargePeriod = datao.chargePeriod;
    }else{
        var chargePeriod = parseInt(datao.chargePeriod);
    }
    chargePeriod = Number(chargePeriod);
    var imprestProportion = Number(datao.imprestProportion);
    $.ajax({
        url:"../supplier/updateSupplierBase.do",
        data:{
            id:datao.id,
            name,//供应商简称
            fullName, //供应商名称
            codeName,//供应商代号
            qImages:imgArr,
            chargeAcceptable:datao.chargeAcceptable,//是否接受挂账
            chargeBegin:datao.chargeBegin,//挂账开始
            chargePeriod:chargePeriod,//账期
            isImprest:datao.isImprest,//是否要预付款
            imprestProportion:datao.imprestProportion,//预付款比例
            // uncertainty:datao.uncertainty,//预付款不确定
        },
        success:function(data){
            var status = data['status'];
            $("#beforeof").show();
            $("#overstop").hide();
            $("#maisure").show();
            $("#stopaway").hide();
            if(status ==0 || status =="0"){
                $("#tiphoto .shu1").html("操作失败，请稍后重试。");
                bounce_Fixed.show($("#tiphoto"));
                return false;
            }
            // layer.msg("修改成功");
            getContractMes(1,20);
            bounce.show($("#updateSupPanel"));
        }
    })
}
// creator: sy 2022-06-31 点击“修改开票信息”弹窗中的“确定”按钮
function sale_upinvosure(){
    var id = $("#updateSupPanel").data("id");
    var datar = {
        id:id,
        //invoicable:$("#haveInvoice").val(),//能否开发票
        //vatsPayable:$("#vatInvoice").val(),//能否开增值税发票
        //draftAcceptable:$("#huip").val(),//可否接受汇票
        //taxRate:$("#e_gRate0").val(),//税率
    };
    var orng = $("#e_gRate0").val();
    if(orng === "" || orng === null){
        datar.taxRate = 0;
    }
    if(datar.invoicable === "1"){
        if(datar.vatsPayable !== "1"){ //vatsPayable=2
            $(".godemo_1").hide();
            var ond = $("#addAccount").data("chose");
            if(ond === "upode"){
                var one = $(".onez").html(); //one对应的值是“是否能开增值税发票”
                // var two = $(".twoz").html();
                if(one != "0"){
                    layer.msg("操作失败！\n" +
                        "因为该供应商供应有开具增值税专用发票的材料。\n" +
                        "请先修改这些材料的价格！");
                    return false;
                }
            }
        }else if(datar.vatsPayable == "1"){
            $(".godemo_1").show();
            var bend = $("#addAccount").data("chose");
            // if(bend === "upode"){
            //     var one = $(".onez").html();
            //     var two = $(".twoz").html();
            //     var three = $(".threez").html();
            //     if(one != "0" || two != "0" ||  three != "0"){
            //         layer.msg("操作失败！\n" +
            //             "因为该供应商供应有开具发票的材料。\n" +
            //             "请先修改这些材料的价格！");
            //         return false;
            //     }
            // }
        }
    }else{
        $(".godemo").hide();
        $(".hp").hide();
        var chone = $("#addAccount").data("chose");
        if(chone === "upode"){
            var one = $(".onez").html();
            var two = $(".twoz").html();
            if(one != "0" || two != "0"){
                layer.msg("操作失败！\n" +
                    "因为该供应商供应有开具发票的材料。\n" +
                    "请先修改这些材料的价格！");
                return false;
            }
        }
    }
    $.ajax({
        url:"../saleSupplier/editSupplierInvoice",
        data:{
            supplierId:datar.id,
            invoicable:datar.invoicable,
            vatsPayable:datar.vatsPayable,
            draftAcceptable:datar.draftAcceptable,
            taxRate:datar.taxRate
        },
        success:function(data){
            // layer.msg("修改成功");
            getContractMes(1,20);
            bounce.show($("#updateSupPanel"));
        }
    })
}
// creator: sy 2022-06-27 单选框选中
function saveNewSupply(){
    // ‘该供应商是否能开发票’
    // var inbepon = $("#bepont").val();
    // '是否能开增值税专用发票'
    // var vapont = $("#vapont").val()  ;
    var invoiceCategory = $("#vatInvoice").val();
    // '该合同开发票的税率‘
    var taxRate = $("#Rate0").val() ;
    //'是否接受挂账‘
    var chargeAcceptable = $("#setHangAccount").val();
    //'账期'
    var chargePeriod = $("#hangDays").val();
    //'自入库之日起，自发票提交之日起'
    var chargeBegin = $("#setStartDate").val();
}
// creator: sy 2022-07-14 防止提示错误
function scanCtrlHide(){}
// creator: sy 2022-05-25 供应商查询
function searchcontract(){
    $("#home").hide();
    $("#searchdi").show();
    var keyword = $(".main #se0").val();
    getChateMessage(1,20,keyword);
}
// creator: sy 2022-08-17 图片展示
function seePic(imgn){
    imgViewer(imgn);
}
// creator: sy 2022-07-05 邮寄信息修改(常规修改）
function setAddress(type,json){
    var uin = $(".node4 ").siblings(".hd").html();
    uin =JSON.parse(uin);
    var uid = uin.id;
    var lixr = $(".node2 ").siblings(".hd").html();
    lixr = JSON.parse(lixr);
    var lid = lixr.id;
    var add = $("#mailAddress").val();
    var conte = $("#mailName").val();
    var numb = $("#mailNumber").val();
    var bil = lixr.mobile;
    $.ajax({
        url:"../supplier/updateSupplierAddress.do",
        data:{
            id:uid,
            supplierContact:lid,
            address:add,
            contact:conte,
            postcode:numb,
            mobile:bil
        },
        success:function(res){
            var status = res.success;
            if(status === '1'|| status === 1){
                // layer.msg("修改成功");
                supplierInfoEdit(editObj);
            }else{
                layer.msg("修改失败");
            }
        }
    })
    bounce_Fixed2.cancel($("#newMailInfo"));
}
// creator: sy 2022-05-20 是否需要预付款
function setAdvanceFee(type,thisObj){
    setRadioSelect("setAdvanceFee", [1,0,2], type, thisObj);
    if(type == 1){
        $("#paymore").show();
        $("#chooseunde").show();
        $("#uncertainty1").attr("class","fa fa fa-circle-o");
        // $("#e_gRate1").removeAttr("disabled");
        $("#e_gRate1").css("background","white");
    }else{
        $("#paymore").hide();
        $("#chooseunde").hide();
    }
}
// creator: sy 2022-06-21 新增联系人
function setContact(data) {
    var socialList = data.socialList;
    $("#contactFlag").html(data.tags ||'其他');
    $("#contactName").val(data.name).data('org',data.name);
    $("#position").val(data.post).data('org',data.post);
    $("#contactNumber").val(data.mobile).data('org',data.mobile);
    $("#contactsCard").data('org',data.visitCard);
    $("#addMoreContact").hide();
    if (data.visitCard && data.visitCard != '' && data.visitCard != 'undefined' && data.visitCard != 'null'){
        var path = data.visitCard;
        var imgStr =
            '<div class="bussnessCard">' +
            '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
            '   <span class="ty-color-blue" onclick="cancleCard($(this))">删除</span> ' +
            '</div>';
        $("#uploadCard").hide();
        $("#uploadCard").before(imgStr);
    }else{
        $("#uploadCard").show();
    }
    if(socialList.length > 0){
        var html = '';
        for(var r in socialList){
            html +=
                '<li class="addfour">' +
                '<span class="sale_ttl1">'+ socialList[r].name +'：</span>' +
                '<span class="gap"><input type="text" value="'+ socialList[r].code +'" placeholder="请录入" data-type="'+ socialList[r].type +'" data-name="'+ socialList[r].name +'" data-org="'+ socialList[r].code +'" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
        }
        $(".otherContact").html(html).data('length', socialList.length);
    }
}
// creator : sy 2022-08-23 渲染本合同下的商品内容
function setContractBN(late,boolSet){
    console.log(late);
    let str = '';
    late = late || [];
    if(late.length == 0){
        $("#cGTp").html(0);
        bounce_Fixed3.show($("#removeout"));
        $("#removematerial").children("thead").siblings("tr").html("");
        return false;
    }
    var item1 = [];
    if( late.data == undefined){
        item1 = late;
    }else{
        item1 = late.data;
    }
    let answ = item1.name;
    let sn = ``, name =``, mt
    let isEdit = $("#isEdit").val();
    if(isEdit === 0) {
        sn = $("#suppliername").val();
        name = $("#suppliernumber").text();
    } else {
        let supplierData = JSON.parse(editObj.siblings(".thingsbox").html());
        sn = supplierData.codeName
        name = supplierData.name
    }
    $("#supplierInfo").html(sn+ name);
    $("#removematerial .before").hide();
    $("#removeout .countStr").hide();
    if(answ != undefined){
        var str2 = ``;
        var band = 1;
        $("#cGTp").html(band);
        str +=`
            <tr>
                <td class="befone">
                    <div class="controlTd" id="dettlebeffoe">${handleNull(item1.code)}<span class="hd">${JSON.stringify(item1)}</span></div>
                </td>
                <td>${handleNull(item1.name)}</td>
                <td>${handleNull(item1.model)}</td>
                <td>${handleNull(item1.specifications)}</td>
                <td>${handleNull(item1.unit)}</td>
                <td>
                    <span class="ty-color-blue" onclick="includeGoodContract($(this))">${item1.contractNum || 0}个</span>
                    <span class="hd">${JSON.stringify(item1)}</span>
                </td>
            </tr>`
    }else{
        for(let i in item1){
            let atmo = item1[i];
            var str1 =``;
            var band = item1.length;
            str1 +=`可供选择的材料共有以下<span>${band}种</span>`;
            $("#cGTp").html(band);
            str +=`
            <tr>
                <td class="befone">
                    <div class="controlTd" id="dettlebeffoe">${handleNull(atmo.code)}<span class="hd">${JSON.stringify(atmo)}</span></div>
                </td>
                <td>${handleNull(atmo.name)}</td>
                <td>${handleNull(atmo.model)}</td>
                <td>${handleNull(atmo.specifications)}</td>
                <td>${handleNull(atmo.unit)}</td>
                <td>
                    <span class="ty-color-blue" onclick="includeGoodContract($(this))">${atmo.contractNum || 0}个</span>
                    <span class="hd">${JSON.stringify(atmo)}</span>
                </td>
            </tr>`
        }
    }
    $("#removeout table tr:gt(0)").remove();
    $("#removeout table").append(str);
    bounce_Fixed3.cancel($("#newcontractpro"));
    bounce_Fixed3.show($("#removeout"));
}

// creator: 张旭博，2024-06-28 01:28:46， 包含某商品的合同
function includeGoodContract(selector) {
    let mtInfo = JSON.parse(selector.siblings(".hd").html());
    let cusInfo = JSON.parse(editObj.siblings(".thingsbox").html());
    let cusArr = [cusInfo.codeName, cusInfo.fullName]
    $("#includeGoodContract .cusInfo").html([...cusArr].filter(item => item || item === 0).join(" / "));
    let listPoContract = mtInfo.listPoContract || [];
    let str = ''
    for (let item of listPoContract) {
        str += `<tr>
                    <td>${item.sn}</td>
                    <td>${item.materialCount || 0}种</td>
                    <td>${moment(item.validStart).format("YYYY-MM-DD")}至${moment(item.validEnd).format("YYYY-MM-DD")}</td>
                    <td>${item.signTime?(moment(item.signTime).format("YYYY-MM-DD")):''}</td>
                    <td>${item.createName} ${moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                </tr>`
    }
    $("#includeGoodContract tbody").html(str)
    bounce_Fixed4.show($("#includeGoodContract"));
}
// creator: 李玉婷 2024-08-20 渲染合同里商品的列表
function setContactGS(list, boolSet, isHasCheck) {
    let str = '';
    let cstr = '';
    if(boolSet){
        $("#tipcontractGoods .selectTd").show()
        $("#tipcontractGoods .addOrCancel").show()
        $("#tipcontractGoods .cScanc").hide()
        $("#tipcontractGoods .countStr").show()
    } else {
        $("#tipcontractGoods .selectTd").hide()
        $("#tipcontractGoods .addOrCancel").hide()
        $("#tipcontractGoods .cScanc").show()
        $("#tipcontractGoods .countStr").hide()
    }
    list = list || [];
    list.forEach(function (im) {
        let isCheckedStr = im.isChecked && isHasCheck?'checked':''
        let handleStr = boolSet?`<td><div class="ty-checkbox"><input type="checkbox" name="goods" id="good_${im.id}" ${isCheckedStr}/><label for="good_${im.id}"></label></div></td>`:''
        str += `<tr data-id="${im.id}">
                    ${handleStr}
                    <td>${im.code}</td>
                    <td>${im.name}</td>
                    <td>${im.model}</td>
                    <td>${im.specifications}</td>
                    <td>${im.unit}</td>
                    <td>
                        <span class="link-blue" onclick="includeGoodContract($(this))">${im.contractNum || 0}个</span>
                        <span class="hd">${JSON.stringify(im)}</span>
                    </td>
                </tr>`
    })
    $("#tipcontractGoods table tbody").html(str);
    bounce_Fixed3.show($("#tipcontractGoods"));
}
// creator: sy 2022-05-06-30 联系人列表
function setContactList(list){
    if(list.length == 0){
        $(".contectList").html("");
    }
    var html = '',rhtml = '',slice = 0;
    if(list && list.length > 0){
        html =
            '<div class="dataList leftList"> <table class="ty-table ty-table-control">'+
            '<thead>'+
            '<tr>'+
            '<td>姓名</td>'+
            '<td>职位</td>'+
            '<td>操作</td>'+
            '</tr>'+
            '</thead><tbody>';
        if(list.length >=2) rhtml = html;
        for(var i=0;i<list.length;i++){
            list[i]['number'] = list[i].number || list[i].id;
            slice = i%2;
            if(slice > 0){
                rhtml +=
                    '<tr data-id="'+list[i].number+'" groupUuid="'+list[i].groupUuid+'">'+
                    '<td>'+list[i].name+'</td>'+
                    '<td>'+list[i].post+'</td>'+
                    '<td><span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer" onclick="upchange()">修改</span>' +
                    '<span class="ty-color-red node" data-type="delete" data-name="contactInfo" id="d1" onclick="dettle($(this))">删除</span></td>'+
                    '</tr>';
            }else{
                html +=
                    '<tr data-id="'+ list[i].number +'" groupUuid="'+list[i].groupUuid+'">'+
                    '<td>'+list[i].name+'</td>'+
                    '<td>'+list[i].post+'</td>'+
                    '<td><span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer" onclick="upchange()">修改</span>' +
                    '<span class="ty-color-red node" data-type="delete" data-name="contactInfo" id="d2" onclick="dettle($(this))">删除</span></td>'+
                    '</tr>';
            }
        }
        html += '</tbody></table></div>';
        if(list.length >=2) rhtml +='</tbody></table></div>';
    }
    var str = html +rhtml;
    var ts = JSON.stringify(list);
    $("#addAccount").data('conreceiveInfo',ts);
    return str;
}
// creator: sy 2022-07-05 选择联系人
function setCusListStr(list,source) {
    let str = "";
    var bont = $("#mailName").data("font");
    if (bont === "add") {//新增供应商弹窗
        // let bntn = $("#dettle").parent().parent().html();
        let bntn = $("#clist3-1").html();
        if(bntn == null ||bntn == undefined || bntn == "null"){
            $("#chooseCusContact .p0").show();
            $("#chooseCusContact .p1 p").hide();
            $(".cusList").hide();
        }else{
            // let lixir =$("#dettle").siblings(".hd").html();
            // list = JSON.parse(list);
            $("#chooseCusContact .p0").hide();
            $("#chooseCusContact .p1 p").show();
            $(".cusList").show();
            for(let i in list){
                let item = list[i];
                str += `<li>`+
                    `<i class="fa fa-circle-o"></i>`+
                    `<span style="margin-left: 10px;margin-right: 5px;">${item.name}</span>`+
                    `<span style="margin-left: 5px;margin-right: 5px;">${item.post && item.post.substr(0,8)} </span>`+
                    `<span style="margin-left: 5px;margin-right:10px;">${item.mobile}</span>`+
                    `<span class="hd info">${JSON.stringify(item)}</span>`+
                    `<span class="linkBtn ty-right" data-source="${source}" data-type="contactSocial" data-id="${item.id}" onclick="lxrlook($(this))">查看</span>`+
                    `</li>`;
            }
        }
    } else if(bont === "upen"){//供应商常规修改
        $("#chooseCusContact .p0").hide();
        $("#chooseCusContact .p1 p").show();
        $("#chooseCusContact .p1 .cusList").show();
        for(let i in list){
            let item = list[i];
            str += `<li>`+
                `<i class="fa fa-circle-o"></i>`+
                `<span style="margin-left: 10px;margin-right: 5px;">${item.name}</span>`+
                `<span style="margin-left: 5px;margin-right: 5px;">${item.post && item.post.substr(0,8)}</span>`+
                `<span style="margin-left: 5px;margin-right:10px;">${item.mobile}</span>`+
                `<span class="hd info">${JSON.stringify(item)}</span>`+
                `<span class="linkBtn ty-right" data-source="${source}" data-type="contactSocial" data-id="${item.id}" onclick="lxrlook($(this))">查看</span>`+
                `</li>`;
        }
        // }
    }
    $("#chooseCusContact .cusList").html(`${str}`);
    let box = $("#chooseCusContact .cusList").html();
    if(box.length<0 || box == undefined || box == null || box == "null" || box == ""){
        $("#chooseCusContact .p0").show();
        $("#chooseCusContact .p1 p").hide();
        $(".cusList").hide();
    }
}
// creator: sy 2022-08-23 渲染移除从本合同中移除材料
function setDettleCont(late,boolSet) {
    console.log(late);
    let str = '';
    let ctsa = '';
    $("#selectedCount").html(0);
    if(boolSet){
        ctsa = '<span class="fa fa-square-o chose"></span>';
        $("#removematerial .before").show();
        $("#removeout .countStr").show();
    } else {
        $("#removematerial .before").hide();
        $("#removeout .countStr").hide();
    }
    late = late || [];
    if(late.length == 0){
        $("#cGTp").html(0);
        bounce_Fixed3.show($("#removeout"));
        $("#removematerial").children("thead").siblings("tr").html("");
        return false;
    }
    var item1 = [];
    if( late.data == undefined){
        item1 = late;
    }else{
        item1 = late.data;
    }
    var unal = item1.name;
    if(unal != undefined){
        var str2 = ``;
        var band = 1;
        str2 += `可供选择的材料共有以下<span>${band}种</span>`;
        $("#cGTp").html(band);
        str +=`
            <tr>
                <td class="before">${ctsa}</td>
                <td class="befone">
                    <div class="controlTd" id="dettlebeffoe">${handleNull(item1.code)}<span class="hd">${JSON.stringify(item1)}</span></div>
                </td>
                <td>${handleNull(item1.name)}</td>
                <td>${handleNull(item1.model)}</td>
                <td>${handleNull(item1.specifications)}</td>
                <td>${handleNull(item1.unit)}</td>
                <td>
                    <span class="ty-color-blue" onclick="includeGoodContract($(this))">${item1.contractNum || 0}个</span>
                    <span class="hd">${JSON.stringify(item1)}</span>
                </td>
            </tr>`
    }else{
        for(let i in item1){
            let atmo = item1[i];
            var str1 =``;
            var band = item1.length;
            str1 +=`可供选择的材料共有以下<span>${band}种</span>`;
            $("#cGTp").html(band);
            str +=`
            <tr>
                <td class="before">${ctsa}</td>
                <td class="befone">
                    <div class="controlTd" id="dettlebeffoe">${handleNull(atmo.code)}<span class="hd">${JSON.stringify(atmo)}</span></div>
                </td>
                <td>${handleNull(atmo.name)}</td>
                <td>${handleNull(atmo.model)}</td>
                <td>${handleNull(atmo.specifications)}</td>
                <td>${handleNull(atmo.unit)}</td>
                <td>
                    <span class="ty-color-blue" onclick="includeGoodContract($(this))">${item1.contractNum || 0}个</span>
                    <span class="hd">${JSON.stringify(item1)}</span>
                </td>
                </tr>`
        }
    }
    $("#removeout table tr:gt(0)").remove();
    $("#removeout table").append(str);
    bounce_Fixed3.show($("#removeout"));
}
// creator: sy 2022-05-14 是否接受挂账
function setHangAccount(type, thisObj){
    // $("#contdent").hide();
    // $("#spcontdent").hide();purchasedAny($(this))
    setRadioSelect("setHangAccount", [1,0], type, thisObj);
    $("#hangDays").val("") ;
    if(type == '1' || type == 1){
        $("#contdent").show(); $(".hang_7").show();$(".hang7_1").show();
    }else{
        $("#contdent").hide(); $(".hang_7").hide();$(".hang7_1").hide();
    }
}
// creator: sy 2022-05-05 图片字符串输出
function setImgHtml(type,imgs) { // type: 1=查看 2= 修改
    var html = '';
    for(var e in imgs){
        var path = imgs[e].normal;
        html +=
            '<div class="imgsthumb">' +
            '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>';
        if(type == '2'){
            html += '    <span onclick="cancleThis($(this))">删除</span> ';
        }
        html +=
            '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a></div>';
    }
    return html;
}
// creator: sy 2022-05-13 邮寄信息列表
function setMailList(list){
    var type = $("#newMailInfo").data('type');
    if(list.length == 0){//条件语句
        $(".receiveList").html("");
    }else{
        for(var i=0;i < list.length;i++){
            var num = 1 + Number(i);
            var html1 = `
           
                <tr data-id="${list[i].customerContract}">
                    <td id="ads">${list[i].address}</td>
                    <td id="ctt">${list[i].contact}</td>
                    <td id="ptd">${list[i].postcode}</td>
                    <td id="mbe">${list[i].mobile}</td>
                    <td>
                        <span class="ty-color-blue node" data-type="update" data-source="addCustomer" data-name="mailInfo" onclick="updateen()">修改</span>
                        <span class="ty-color-red node dettle" data-type="delete" data-name="mailInfo" id="without" onclick="deletope($(this))">删除</span>
                        <span class="hd">${JSON.stringify(list[i])}</span>
                    </td>
                </tr>
            `;
            var html2 = `
                <td id="ads">${list[i].address}</td>
                <td id="ctt">${list[i].contact}</td>
                <td id="ptd">${list[i].postcode}</td>
                <td id="mbe">${list[i].mobile}</td>
                <td>
                    <span class="ty-color-blue node" data-type="update" data-source="addCustomer" data-name="mailInfo" onclick="updateen()">修改</span>
                    <span class="ty-color-red node dettle" data-type="delete" data-name="mailInfo" id="without" onclick="deletope($(this))">删除</span>
                    <span class="hd">${JSON.stringify(list[i])}</span>
                </td> `;
            if(type == "new"){
                $(".receiveList .mailPlaceList #clist2").append(`${html1}`);
            }else if(type == "update"){
                var uopon = $("#without").parent().parent();
                uopon.html(`${html2}`);
            }
        }
    }
    var mailInfo = list;
    mailInfo = JSON.stringify(mailInfo);
    $("#addAccount").data("mailInfo",mailInfo);
    $("#clist2").show();
}
// creator: sy 2022-07-06 新增邮寄信息部分渲染
function setMailListm(list){
    if(list.length == 0){
        $(".insertItem").html("");
    }else{
        for(var i=0;i<list.length;i++){
            var html1=`
                <tr class="insertItem">
                    <td class="">${list[i].contact || ''}</td>
                    <td> 
                        <span class="ty-color-blue dfg" onclick="yjAddressUpdate($(this))" >修改
                        </span>
                        <span class="ty-color-blue node dfg" onclick="yjAddressUpdateLog($(this))" >修改记录</span>
                        <span class="ty-color-red dfg node1" onclick="yjAddressDel($(this))" >停用</span>
                        <span class="hd">${ JSON.stringify(list[i]) }</span>
                    </td>
                </tr>
                    `
            $("#ujnation").after(`${html1}`);
        }
    }
    var mailInfo = list;
    mailInfo = JSON.stringify(mailInfo);
    $("#addAccount").data("mailInfo",mailInfo);
    bounce_Fixed2.cancel($("#newMailInfo"));
    $("#ujnrion").show();
}
// creator: sy 2024-01-25   是否继续上次的操作
function setRadioorhas(str,arr,selectVal,thisObj){
    var isEdit = $("#isEdit").val();
    var idStr = "#";
    if(isEdit === "1"){
        idStr = "#edit_";
    }
    idStr += str ;
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $(idStr).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $(idStr + arr[i]).attr("class" , "fa fa-circle-o");
    }
    $(idStr + selectVal).attr("class" , "fa fa-dot-circle-o");
}
// creator: sy 2022-06-25 复选框选中（是否能开发票）
function setRadioSelect(str, arr, selectVal, thisObj){  //arr:[1,0]
    var isEdit = $("#isEdit").val();
    var idStr = "#";
    if(isEdit === "1"){
        idStr = "#edit_";
    }
    idStr += str ;
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $(idStr).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $(idStr + arr[i]).attr("class" , "fa fa-circle-o");
    }
    $(idStr + selectVal).attr("class" , "fa fa-dot-circle-o");

}
// creator: sy 2022-06-25 复选框选中（是否可接受汇票）
function setRadioCSelect(str,arr,selectVal,thisObj){
    var isEdit = $("#isEdit").val();
    var idStr = "#";
    if(isEdit === "1"){
        idStr = "#edit_";
    }
    idStr +=str;
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $(idStr).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $(idStr + arr[i]).attr("class" , "fa fa-circle-o");
    }
    $(idStr + selectVal).attr("class" , "fa fa-dot-circle-o");
}
// creator: sy 2022-06-25 复选框选中（从何时开始计算账期）
function setStartDate(type , thisObj) {
    setRadioSelect("setStartDate", [1,2], type, thisObj);
}
// creator: sy 2022-05-12 新增验证
function setTimer(timer){
    switch(timer){
        case 'updateReceive':
            bounce_Fixed.everyTime('0.5s','updateReceive',function () {
                var  filledNum = 0;
                $("#newReceiveInfo input").each(function(){
                    if ($(this).val() == '') filledNum++;

                });
                if(filledNum === 0 ){
                    $("#addReceive").prop("disabled",false);
                }else{
                    $("#addReceive").prop("disabled",true);
                }
            });
            break;
        case 'updateMail':
            bounce_Fixed.everyTime('0.5s','updateMail',function(){
                var state = 0,filledNum = 0;
                $("#newMailInfo input").each(function(){
                    if($(this).val() == '') filledNum++;
                });
                if(filledNum === 0){
                    if(!is_postcode($("newMailInfo #mailNumber").val())){
                        $("#mailNumError").hide();
                        $("#addMail").prop("disabled",false);
                    }else{
                        $("#mailNumError").show();
                        $("#addMail").prop("disabled",true);
                    }
                }else{
                    $("#addMail").prop("disabled",true);
                }
            });
            break;
        case 'updateContact':
            bounce_Fixed.everyTime('0.5s','updateContact',function(){
                var state = 0,filledNum = 0,otherContactLen = 0;
                var contactsCard = $("#contactsCard").data('org');
                var imgCard = $("#contactsCard .filePic").data('path');
                var len = $(".otherContact").data('length');
                if($(".otherContact li").length > 0){
                    $(".otherContact li").each(function (){
                        var val = $(this).find("input").val();
                        var type = $(this).find("input").data("type");
                        if(type == '0' || type == '0') type = 6
                        if(val == ''){
                            $("#addMoreContact option").eq(type).prop('disabled',false);
                        }else{
                            $("#addMoreContact option").eq(type).prop('disabled',false);
                            otherContactLen++;
                        }
                    })
                }
                if(len != otherContactLen) state ++;
                $("#newContectData [require]:visible").each(function(){
                    if($(this).val() != '') filledNum++;
                    if($(this).val() != $(this).data('org')){
                        state ++;
                    }
                });
                if(contactsCard != imgCard) state ++;
                if(filledNum > 0 && state >0){
                    $("#addContact").prop("disabled",false);
                }else{
                    $("#addContact").prop("disabled",false);
                }
            });
            bounce_Fixed.show("#newContectInfo");
            break;
        case 'useDefinedLabel':
            bounce_Fixed.everyTime('0.5s','useDefinedLabel',function(){
                var name = $.trim($("#defLable").val());
                if(name == '' || name){
                    $("#addNewLabelSure").prop('disabled',true);
                }else{
                    $("#addNewLableSure").prop('disabled',false);
                }
            })
            break;
    }
}
// creator: sy 2022-4-28 日历显示
function signingtime(){
    console.log("可以么");
    // $("#layui-laydate12").show();
}
// creator: sy 2022-05 已暂停的供应商列表
function stopcontent(currentPageNo,pageSize){
    $.ajax({
        url:"../supplier/getSuspendSupplierList.do",
        data:{
            "currentPageNo":currentPageNo,
            "pageSize":pageSize,
        },
        success:function(res){
            console.log(res);
            var totalPage = res.data.pageInfo.totalPage;
            var currentPageNo = res.data.pageInfo.currentPageNo;
            setPage($("#ye_stopen"),currentPageNo,totalPage,"currentPageNo");
            $("#ye4").html("");
            var leson = res.data.list;
            if(leson.length == 0){
                $(".initBody .title span").html(0);
            }
            for(var i=leson.length-1;i>=0;i--){
                var st1 = "<span>"+leson.length+"</span>";
                var str =
                    "<tr>"+
                    "<td>"+handleNull(leson[i]["fullName"])+"</td>"+
                    "<td>"+handleNull(leson[i]["codeName"])+"</td>"+
                    "<td>"+handleNull(leson[i]["createName"])+"&nbsp;"+new Date(handleNull(leson[i]['createDate'])).format('yyyy-MM-dd hh:mm:ss')+
                    "</td>"+
                    "<td>"+handleNull(leson[i]["createName"])+"&nbsp;"+new Date(handleNull(leson[i]['createDate'])).format('yyyy-MM-dd hh:mm:ss')+
                    "</td>"+
                    "<td>"+
                    "<span class='ty-color-blue' onclick='havelooko($(this))'>"+ handleNull(leson[i]["exclusive_count"])+"种</span>"+
                    "</td>"+
                    "<td>"+
                    "<span class='ty-color-blue' onclick='havelooko($(this))'>查看</span>"+
                    "<span class='ty-color-blue' id='recovery' onclick='printen($(this))'>恢复采购</span>"+
                    "<span class='hd'>"+handleNull(leson[i]['id'])+"</span>"+
                    "<span class='thingsbox' style='display: none'>"+JSON.stringify(leson[i])+"</span>"+
                "</td>"+
                    "</tr>";
                $(".initBody .title span").html(st1);
                $("#ye4").append(str);
            }
        }
    })
}
// creator: sy 2022-07-01 已暂停/终止的合同
/*function stopdown(){
    $("#newcontractpro .scanGs").data('gsArr',[]);
    var stid = $("#updateSupPanel").data("id");
    $.ajax({
        url:"../supplier/listContractBase.do",
        data:{
            supplierId:stid,
            type: 2
        },
        success:function(res){
            var state = res.success;
            if(state === "1" || state === 1){
                // layer.msg("查询成功");
                let cont = res.data.contractBaseList || [];
                let str1 = ``;
                cont.forEach(function(item){
                    str1 += `
                        <tr class="contractItem" data-id="${item.id}">
                            <td>${item.sn}</td>
                            <td>${new Date (item.suspendTime).format("yyyy-MM-dd hh:mm:ss")}</td>
                            <td>
                                <span class="ty-color-blue edit2" data-name="cScan" onclick="looknuc($(this))">查看</span>
                                <span class="ty-color-blue edit2 cRback" data-name="cRestart" onclick="tsoipen($(this))">恢复履约/重启合作</span>
                                <span class="hd">${item.id}</span>
                            </td>
                        </tr>
                        `;
                })
                $("#tb2 tr:gt(0)").remove();
                $("#tb2").append(str1);
                bounce_Fixed2.show($("#stopboot"));
            }
        }
    })
}
// creator: sy 2022-07-11 点击“恢复履约/重启合同”后显示的弹窗后在出现的弹窗中点击确定按钮
function stopdowno(dnnm){
    let qidid = $("#tiphoto").data("qid");
    $("#stopboot").data("enabled","1");
    let qienabled = $("#stopboot").data("enabled");
    let url = ``;
    $.ajax({
        url:"../supplier/checkReStartPoContractState.do",
        data:{
            id: qidid
        },
        success:function(data){
            let res = data.data;
            if(res.state === 1){ //启用合同时判断合同的状态：1 state 0-不要重复启用 1-没有过期直接恢复 2-已经过期了
                reStoreSure(qidid, 1);
            }else if(res.state === 0){
                layer.msg("不要重复启用");
                return false;
            }else if(res.state === 2){
                layer.msg("已经过期了");
                return false;
            }
        }
    })

}*/
// creator: 李玉婷 2024/07/31
function reStoreSure(id, type){
    $.ajax({
        url:"../supplier/reStartPoContract.do",
        data:{
            id: id,
            type: type // String  1-正常恢复合同 2-恢复合同到已到期中
        },
        success:function(res){
            if(res.success === 1){
                supplierInfoEdit(editObj);
                //stopdown();
            }else if(res.success === 0){
                layer.msg("该合同下已有材料被其他合同使用，不能启用!");
                return false;
            }
            bounce_Fixed.cancel($("#tiphoto"));
            bounce_Fixed2.show($("#stopboot"));
        }
    })
}

// creator: sy 2022-05 点击“暂停采购”按钮显示的弹窗
function stopgone(selector){
    bounce_Fixed.show($("#suspend"));
    var supplieId =selector.parents("tr").find(".hd").text();
    $("#suspend").data("supplieId",supplieId);
}
// creator: sy 2022-07-05 暂停履约/终止合同
function stoprook(){
    var spid = $(this).parents(".contractItem").data("id");
    $("#beforeof").show();
    $("#overstop").hide();
    $("#maisure").show();
    $("#stopaway").hide();
    $("#tiphoto").data("spid",spid).data("enabled",0);
    $("#prompttext").html("<span>确定后，相关材料将不再属于本合同。</span>" +
        "                    <span>确定进行本操作吗？</span>");
    bounce_Fixed.show($("#tiphoto"));
}
// creaotr: sy 2022-07-01 （常规信息修改弹窗中）邮寄信息数据中停用按钮
function stouse(boj){
    var id = obj.parents("tr").data("id");
}
// creator: sy 2022-07-05 开票信息修改(常规修改）
function suptendInvoice(type,source,thisObj){
    // $("#newcontractpro").data("type",type).data("source",source);
    bounce_Fixed3.show($("#newcontractpro"));
    if(type === 'new'){
        $("#newcontractpro .bonceHead span").html("新增合同");
        $(".cRenewalTip").hide();
    }else if(type === 'update'){
        $("#newcontractpro .bonceHead span").html("修改合同");
        $(".cRenewalTip").hide();
    }else if(type === 'cRenewal' || type === 'cRenewalHistory'){
        $("#newcontractpro .bonceHead span").html("合同续约");
        $(".cRenewalTip").show();
    }

    $("#newcontractpro .fileCon .fileCon1").html("");
    initCUpload2($("#cUpload2-1"),'img');
    initCUpload2($("#cUpload2-2"),'doc');
    $(".cGs").hide();
    $("#newcontractpro .scanGs").data("gsArr",[]).data("gsArrNo",[]).html("0");

}
// creator: sy 2022-06-10 点击“已暂停采购的供应商”按钮
function suspendedContract(){
    $("#home").hide();
    $("#procurement").show();
    stopcontent(1,20);//已暂停的供应商列表
}

// creator: sy 2022-09-21 查看合同弹窗中的查看材料
function tacklook(){
    let lank = $("#looken").data("link");
    $("#removeout .bonceHead span").html("本合同下的材料");
    setContractBN(lank,false);
    $("#addmor").hide();
    $("#updunt").hide();
    $(".cloose").show();
    $(".getout").hide();
    $(".fxclo").hide();$("#mtSeeBtn").hide();
}
// creator: sy 2022-07-11 恢复履约/重启合作按钮点击后的弹窗
function tsoipen(tboc){
    var qid = tboc.siblings(".hd").html();
    $("#prompttext .sureno").hide();
    $("#prompttext .ttwo").show();
    $("#beforeof").hide();
    $("#overstop").show();
    $("#maisure").hide();
    $("#stopaway").show();
    $("#prompttext .sureno").hide();
    bounce_Fixed2.cancel($("#stopboot"));
    $("#tiphoto").data("qid",qid);
    bounce_Fixed.show($("#tiphoto"));
}

// creator: sy 2022-07-05 (新增供应商）新增邮寄信息（前面部分）
function uiaddmail(){
    $("#mailAddress").val("");
    $("#mailNumber").val("");
    $("#mailName").val("");
    $("#newContectInfo").data('level',2);
    var level = $("#newContectInfo").data("level");
    $("#newMailInfo").data("type","new");
    var type = $("#newMailInfo").data("type");
    $("#newMailInfo").data("source","addCustomer");
    $("#mailName").data("font","add");
    $("#newMailInfo").data("add",1);
    $("#newMailInfo .bonceHead span").html("新增邮寄信息");
    $("#before").show();
    $("#after").hide();
    $("#addMail").show();
    $("#addMailml").hide();
    $("#addMailmn").hide();
    var peop =$("#mailNumber").val();
    if(peop.length == 0){
        $("#addMail").prop("disabled",true);
    }else{
        $("#addMail").prop("disabled",false);
    }
    var id = $(this).parents("tr").data('id');
    // $("#newMailInfo").data('type', type);
    // $("#newMailInfo").data('id', id);
    // $("#newMailInfo").data('source', source);
    if(type == 'delete') {
        var mailData = $("#addAccount").data('mailInfo');
        mailData = JSON.parse(mailData);
        var index = mailData.findIndex(value=>value.number === id);
        mailData.splice(index, 1);
        setMailList(mailData);
    }else{
        if(type == 'new'){
            $("#newMailInfo input").val("");
        }else if(type == 'update'){
            $("#mailAddress").val($(this).parents("tr").find("td").eq(1).html());
            $("#mailName").val($(this).parents("tr").find("td").eq(2).html());
            $("#mailNumber").val($(this).parents("tr").find("td").eq(3).html());
            $("#mailContact").val($(this).parents("tr").find("td").eq(4).html());
        }
        bounce_Fixed2.show($("#newMailInfo"));
        setTimer('updateMail');
    }
    bounce_Fixed2.show($("#newMailInfo"));
}
// creator: sy 2022-07-01 （供应商常规信息修改）新增邮寄信息（前面部分）
function ujmess(){
    var id = $("#updateSupPanel").data("id");
    $("#addAccount").data("contactInfo","[]");
    $("#addAccount").data("receiveInfo","[]");
    $("#mailName").data("contractInfo","[]");
    $("#mailAddress").val("");
    $("#mailNumber").val("");
    $("#mailName").val("");
    $("#newContectInfo").data('level',2);
    $("#newMailInfo").data("type","new");
    $("#newMailInfo").data("source","addCustomer");
    $("#newMailInfo").data("add",0);
    $("#mailName").data("font","upen");
    $("#newMailInfo .bonceHead span").html("新增邮寄信息");
    $("#before").hide();
    $("#after").show();
    $("#addMail").hide();
    $("#addMailml").show();
    $("#addMailmn").hide();

    bounce_Fixed2.show($("#newMailInfo"));
}
// creator: sy 2022-08-15 比例不确定
function uncertainty(type,thisObj){
    setRadioSelect("uncertainty",[1,0],type,thisObj);
    if(type == 1){
        // $("#e_gRate1").attr("disabled","disabled");
        // $("#e_gRate1").css("background","rgb(221, 221, 221)");
        $("#e_gRate1").val("");
    }else{
        $("#contract").removeAttr("disabled");
        $("#e_gRate1").css("background","white");
        $("#uncertainty1").attr("class","fa fa fa-circle-o");
    }
}
// creator: sy 2022-06-25 联系人列表中点击“修改按钮“
function upchange(){
    $("#newContectInfo").data("type","update");
    $("#newContectInfo").data("source","addCustomer");
    $("#newContectInfo .bonceHead span").html("修改联系人信息");
    bounce_Fixed2.show($("#newContectInfo"));
}
// creator: sy 2022-07-05 修改中基本信息修改确定
function updata_sure(){
    var id = $("#updateCustormPanel").data("id");
    if ( id == undefined || id == ""){
        $("#mtTip .tip").html("系统错误，刷新重试！");
        bounce_Fixed.show($("#mtTip"));
        return false;
    }
    if (sphdSocket.user.oid === 0) {
        if (!testMobile($.trim($("#updataccount input[name='supervisorMobile']").val()))) {
            layer.msg("请输入正确的手机号")
            return false
        }
    }

    var data = {
        'id': id,
        'qImages': '[]',
    };
    $("#sale_updataBase input[type='text']:visible").each(function(){
        var name = $(this).attr('name');
        data[name] = $(this).val();
    })
    if($("#edit_firstTime").val() != ""){
        data.firstContactTime = $("#edit_firstTime").val();
    }

    var hasBuyCase = $("#sale_updataBase input[name='buyCase']:checked").val();
    if (hasBuyCase === '1') {
        // 购买过产品
        var initialType = $("#sale_updataBase input[name='firstTime']:checked").val();
        if(typeof initialType === 'undefined'){
            layer.msg('请选择首次购买时间');
            return false;
        }else{
            data.initialType = initialType;
            var date = '', month = '';
            var year = new Date().getFullYear();
            if(initialType === '1'){
                month = $("#sale_updataBase [name='initialPeriod']").val();
                if(month === "") {
                    layer.msg('请选择月份');
                    return false;
                }else{
                    var arr = month.split('月');
                    if(arr[0] < 10){
                        date = year + '0' + arr[0];
                    }else{
                        date = year + arr[0];
                    }
                }
            }else if(initialType === '2'){
                year = Number(year) - 1;
                date = year + '01';
            }else if(initialType === '3'){
                year = Number(year) - 2;
                date = year + '01';
            }
            data.initialPeriod = date;
        }
    } else {
        // 选择否的时候什么字段都不需要传
    }

    if($("#edit_qImages .imgsthumb").length > 0){
        var imgArr = [];
        $("#edit_qImages .imgsthumb").each(function(){
            var path = $(this).find(".filePic").data('path');
            var json = {
                'normal': path
            };
            imgArr.push(json);
        })
        imgArr = JSON.stringify(imgArr);
        data.qImages = imgArr;
    }
    if($("#qImages .imgsthumb").length > 0){
        var imgArr = [];
        $("#qImages .imgsthumb").each(function(){
            var path = $(this).find(".filePic").data('path');
            var json = {
                'normal': path
            };
            imgArr.push(json);
        })
        imgArr = JSON.stringify(imgArr);
        data.qImages = imgArr;
    }
    if($("#edit_pImages .imgsthumb").length > 0){
        var imgArr = [];
        $("#edit_pImages .imgsthumb").each(function(){
            var path = $(this).find(".filePic").data('path');
            var json = {
                'normal': path
            };
            imgArr.push(json);
        })
        imgArr = JSON.stringify(imgArr);
        data.pImages = imgArr;
    }
    data.invoiceRequire = $(".goset_update").data("invoice") || 0
    $.ajax({
        url: '/special/getSaleCtrlsByMobile.do',
        data: {mobile: data.supervisorMobile,id: data.id},
        success: function (res) {
            var state = res.data
            if (state === 2) {
                $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                bounce_Fixed.show($("#tip"))
                loading.close()
            } else if (state === 1) {
                $.ajax({
                    url : "/sales/updatePdCustomerBase.do" ,
                    data : data,
                    success:function(data){
                        var status = data["status"];
                        if( status == 0 || status == "0"){
                            $("#mtTip .tip").html("操作失败！");
                            bounce_Fixed.show($("#mtTip"));
                            return false;
                        }
                        // 取消已提交文件的删除
                        var groupUuidArr = []
                        var key = $(".main #se0").val();
                        $("#addAccount [groupUuid]").each(function () {
                            groupUuidArr.push({
                                type: 'groupUuid',
                                groupUuid: $(this).attr("groupUuid")
                            })
                        })
                        cancelFileDel(groupUuidArr)

                        supplierInfoEdit(editObj);
                        getCustomerMes(1 , 20, key);
                        bounce_Fixed.cancel();
                    }
                })
            } else {
                layer.msg("系统错误！")
            }
        }
    })

}
// creator: sy 2022-07-05 修改邮寄信息弹窗展示
function updateen(){
    $("#newMailInfo").data("type","update");
    $("#newMailInfo").data("source","addCustomer");
    $("#newMailInfo .bonceHead span").html("修改邮寄信息");
    bounce_Fixed2.show($("#newMailInfo"));
}
// creator:  施阳 2022-07-04 点击供应商列表中“修改”按钮
var editObj = null; // 首页 点击 的供应商
function supplierInfoEdit(selector){
    var cusInfo = selector.siblings(".thingsbox").html();
    editObj = selector ;
    $("#isEdit").val("1");
    var updId = selector.siblings(".hd").html()
    $("#updateSupPanel").data("id",updId);
    $("#updateSupPanel .insertItem").remove()
    $("#updateSupPanel .contractItem").remove()
    $("#updateSupPanel").data("cusInfo", JSON.parse(cusInfo));
    let optionStr = `<option value="">请选择</option>`
    for(let t=0;t<supplierList.length;t++){
        optionStr += `<option value="${supplierList[t].id}" ${(Number(updId) === Number(supplierList[t].id)?'selected':"")}>${supplierList[t].name}</option>`
    }
    $("#supplier").html(optionStr);
    $(".supplerCon").show();
    getContract(1, 'edit');
    $.ajax({
        url:"../supplier/getSrmSupplierOne.do",
        data:{
            id:updId,
        },
        success:function(resData){
            var res = resData.data;
            var mes1 = "<p id='supplie_main_dy'>"+res.fullName+"</p>";
            $("#supplie_main").html(mes1);

            let contactsList = res.contactsList || [] // 联系人
            let yjAddressList = res.yjAddressList || [] // 邮寄信息
            let contactStr = ``
            contactsList.forEach((contavtItem)=>{
                contactStr += `
                <tr class="insertItem">
                    <td class="">${ contavtItem.name } ${ contavtItem.post } ${ contavtItem.mobile }</td>
                    <td> 
                        <span class="ty-color-blue dfg" onclick="contactUpdate($(this))" >修改</span>
                        <span class="ty-color-blue node dfg" onclick="contactUpdateLogi($(this))" >修改记录</span>
                        <span class="ty-color-red node2 dfg " id="empty" onclick="contactDel($(this))" >删除</span>
                        <span class="hd">${ JSON.stringify(contavtItem) }</span>
                    </td>
                </tr> `
            })
            let yjAddressStr = ``
            yjAddressList.forEach((yjAddressItem)=>{
                yjAddressStr += `
                <tr class="insertItem">
                    <td class="">${ yjAddressItem.address || ''}</td>
                    <td>
                        <span class="ty-color-blue dfg" onclick="yjAddressUpdate($(this))" >修改
                        </span>
                        <span class="ty-color-blue node dfg" onclick="yjAddressUpdateLogu($(this))" >修改记录</span>
                        <span class="ty-color-red node4 dfg " onclick="yjAddressDel($(this))" >停用</span>
                        <span class="hd">${ JSON.stringify(yjAddressItem) }</span>
                    </td>
                </tr>
                `
            })

            $("#ujnation").after(yjAddressStr);
            $("#family").after(contactStr);

            $("#updateSupPanel").data("mailInfom","[]");
            $("#updateSupPanel").data("type","new");
            bounce.show($("#updateSupPanel"));

        }
    })

}
// creator: sy 2022-05 更新图片内容
function updateImgs(arr) {
    var str = ''
    for (var i=0; i< arr.length; i++) {
        str +=  `<span class="fileIm"  >
                     <span>${ 1 + Number(i) }</span>
                     <span class="fa fa-times"></span>
                     <span class="hd">${ JSON.stringify(arr[i]) }</span>
                </span>`
    }
    $(`#fileCon1-1`).html(str)
}
// creator: sy 2022-07-08 供应商常规信息修改-联系人信息修改
function updatelix(){
    var level = $("#newContectInfo").data('level');
    var type = $("#newContectInfo").data('type');
    var source = $("#newContectInfo").data('source');
    var uxid = $("#newContectInfo").data("cyid");
    var lixim ={
        id:uxid,
        socialList:'[]',
        cardPath:'',
        mobile:$("#contactNumber").val(),
        post:$("#position").val(),
        name:$("#contactName").val(),
        tags:$("#contactFlag").html()
    }
    if ($("#contactsCard .bussnessCard").length > 0) {
        lixim.cardPath = $("#contactsCard .bussnessCard .filePic").data('path');
    }
    if ($(".otherContact li").length > 0) {
        var arr = []
        $(".otherContact li").each(function () {
            if ($(this).find('input').val() != '') {
                var json = {
                    'code': $(this).find("input").val(),
                    'type': $(this).find("input").data('type'),
                    'name': $(this).find("input").data('name')
                };
                arr.push(json);
            }
        })
        arr = JSON.stringify(arr);
        lixim.socialList = arr;
    }
    // var soc = JSON.stringify(lixim.socialList);
    // var crd = JSON.stringify(lixim.cardPath);
    // var mob = JSON.stringify(lixim.mobile);
    // var po = JSON.stringify(lixim.post);
    // var na = JSON.stringify(lixim.name);
    // var tg = JSON.stringify(lixim.tags);
    $.ajax({
        url:"../supplier/updateContactsSocialAndCard.do",
        data:{
            contactId:lixim.id,
            socialList:lixim.socialList,
            cardPath:lixim.cardPath,
            mobile:lixim.mobile,
            post:lixim.post,
            name:lixim.name,
            tags:lixim.tags
        },
        success:function(res){
            var state = res.success;
            if(state === "1"|| state ===1){
                // layer.msg("修改成功");
                bounce_Fixed2.cancel($("#newContectInfo"));
                supplierInfoEdit(editObj);
            }
        }
    })
}
// creator: sy 2024-01-29   修改供应弹窗点击确定按钮
function updatensure(){
    let userName = $("#userName").val();//输入框中此时的名称
    let name = $("#name").val();//输入框中此时的简称
    let code = $("#coden").val();//输入框中此时的代号
    //所以说需要手动同系统中的数据和单次导入的数据比对
    let linken = $("#updatensure").data("links");//修改前的数据       修改前点击的‘修改’按钮所在的那行数据
    let getinlink = $("#updatensure").data("inlnks");//单次导入的所有数据        getinlink原来就是本次导入的全部数据
    let allink = $("#updatensure").data("allink");//修改前点击的‘修改’按钮所在的那行数据
    console.log('linken的值',linken);//修改前的数据
    let list = $("#updatensure").data("list");
    console.log('list的值',list);//包含筛选框内容的数据
    if(list == undefined){
        list = {};
        list.chargeAcceptable = null;
        list.isImprest = null;
    }
    if(list.chargeAcceptable !== null || list.chargeAcceptable !== undefined){
        linken.chargeAcceptable = list.chargeAcceptable;
    }
    if(list.isImprest !== null || list.isImprest !== undefined){
        linken.isImprest = list.isImprest;
    }
    console.log('linken现在是包含筛选框选中的选项的数据',linken);
    //判断输入框中此时的内容是否重复，是将输入框中现有内容同列表中相应对应字段的数据进行比较
    let unid = linken.id;
    let json = {};//用于存储需要传值的数据
    //思路，故在调用接口前需要手动判断修改后的数据是否同本次导入的数据出现重复
    for(var g = 0;g<getinlink.length;g++){
        if(getinlink[g].fullName == userName){
            layer.msg("您录入的数据与本次导入的"+getinlink[g].fullName+"供应商名称相同。\n"+"请录入其他数据！");
            return false;
        }
        if(getinlink[g].name == name){
            layer.msg("您录入的数据与本次导入的"+getinlink[g].fullName+"供应商简称相同。\n"+"请录入其他数据！");
            return false;
        }
        if(getinlink[g].codeName == code){
            layer.msg("您录入的数据与本次导入的"+getinlink[g].fullName+"供应商代号相同。\n"+"请录入其他数据！");
            return false;
        }
    }
    linken.fullName = userName;
    linken.name = name;
    linken.codeName = code;
    console.log('现在的linken是修改后的',linken);
    if(linken.fullName == ""){
        layer.msg("请填写供应商名称");
        return false;
    }
    if(linken.name == ""){
        layer.msg("请填写供应商简称");
        return false;
    }
    if(linken.codeName == ""){
        layer.msg("请填写供应商代号");
        return false;
    }
    let imdiff = [];
    let linfullname = linken.fullName;//供应商名称
    let liname = linken.name;//供应商简称
    let licode = linken.codeName;//供应商代号
    getinlink.forEach(itemge => {
        let jsond = {};
        jsond.fullName = itemge.fullName;
        jsond.name = itemge.name;
        jsond.codeName = itemge.codeName;
        imdiff.push(jsond);
    })
    json.fullName = linken.fullName;
    json.name = linken.name;
    json.code = linken.codeName;
    // for(let i = 0;i < imdiff.length;i++){
    //     if(imdiff[i].fullName == linfullname){
    //         layer.msg("您录入的数据与本次导入的"+imdiff[i].fullName+"供应商名称相同。\n"+"请录入其他数据！");
    //         return false;
    //     }else if(imdiff[i].name == liname){
    //         layer.msg("您录入的数据与本次导入的"+imdiff[i].name+"供应商简称相同。\n"+"请录入其他数据！");
    //         return false;
    //     }else if(imdiff[i].codeName == licode){
    //         layer.msg("您录入的数据与本次导入的"+imdiff[i].codeName+"供应商代号相同。\n"+"请录入其他数据！");
    //         return false;
    //     }
    // }
    $.ajax({
        url:"../supplier/import/editImports",
        data:{
            id:unid,
            fullName:json.fullName,
            name:json.name,
            codeName:json.code
        },
        success:function(res){
            let codew = res.code;
            let namew = res.name;
            switch (codew) {
                case 1://修改成功
                    bounce.cancel($("#updatesuplist"));
                    getimportment(allink,"updent");
                    if(allink == undefined || allink.length == 0){
                    }else{
                        allink.forEach(itemall => {
                            let id = linken.id;
                            if(id == itemall.id){
                                if(linken.chargeAcceptable == null){
                                    if(linken.isImprest == null){}else{
                                        itemall.isImprest = linken.isImprest;
                                    }
                                }else{
                                    itemall.chargeAcceptable = linken.chargeAcceptable;
                                    if(linken.isImprest == null){}else{
                                        itemall.isImprest = linken.isImprest;
                                    }
                                }
                                itemall.fullName = linken.fullName;
                                itemall.name = linken.name;
                                itemall.codeName = linken.codeName;
                            }
                        })
                        console.log('现在的allink是包含了修改后的新数据',allink);
                        $(".chosgetpos").data("lins",allink);
                        $(".chospaypos").data("lins",allink);
                        $(".updentepo").data("lins",allink);
                        $(".dettenpo").data("lins",allink);
                        $("#nextstart2").data("lins",allink);
                    }
                    break;
                case 2://与已有的重复
                    layer.msg("您录入的数据与"+namew+"供应商代号相同。\n"+"请录入其他数据！");
                    $(".chosgetpos").data("lins",allink);
                    $(".chospaypos").data("lins",allink);
                    $(".updentepo").data("lins",allink);
                    $(".dettenpo").data("lins",allink);
                    $("#nextstart2").data("lins",allink);
                    break;
                case 3://与导入的重复
                    layer.msg("您录入的数据与本次导入的"+namew+"供应商代号相同。\n"+"请录入其他数据！");
                    $(".chosgetpos").data("lins",allink);
                    $(".chospaypos").data("lins",allink);
                    $(".updentepo").data("lins",allink);
                    $(".dettenpo").data("lins",allink);
                    $("#nextstart2").data("lins",allink);
                    break;
            }
        }
    })
}
// creator: sy 2022-06-02 新增供应商弹窗中合同修改
function updateth(){
    $("#newcontractpro").data("type","update");
    $("#newcontractpro").data("source","addCustomer");
    $("#square").show();
    $("#addpost1").show();
    $("#addpost2").hide();
    $("#movepost1").show();
    $("#movepost2").hide();
    $("#newcontractpro .scanGs").data('gsArr',[]).data("gsArrNo",[]).html("0");
    $("#newcontractpro .bonceHead span").html("修改合同");
    bounce_Fixed3.show($("#newcontractpro"));
}
// creator: sy 2024-01-29   调用接口获取数据展示需要修改的供应商数据
function updentepo(upden){
    let list = $(".updentepo").data("link");
    $("#updatensure").data("list",list);//筛选框选择了的数据
    let allink = $(".updentepo").data("lins");
    $("#updatensure").data("allink",allink);
    let importlink = $(".updentepo").data("inlinks");
    $("#updatensure").data("inlnks",importlink);
    let link = upden.next().next().html();//获取到的数据
    link = JSON.parse(link);
    let fullName = link.fullName;
    let name = link.name;
    if(name == ""){
        name = fullName.substring(0,6);
    }else{
        name = name.substring(0,6);
    }
    let codeName = link.codeName;
    $("#updatensure").data("links",link);
    $("#userName").val(fullName);
    $("#name").val(name);
    $("#coden").val(codeName);
    $("#cloone").hide();
    bounce.show($("#updatesuplist"));
}
// creator: sy 2022-08-22 从本合同中移出材料弹窗中的确定按钮
function updunteContent(){
    let fun  = $("#removeout").data("fun");

    let listEdit = [];
    let chooseGood2 = $("#goodLust").data("chooseGood");    //之前所选择的数据
    $("#removeout .fa-check-square-o").each(function(i){
        var data2 = JSON.parse($(this).parent().siblings().children(".controlTd").children(".hd").html());
        listEdit.push(data2);
    });
    for(let a in listEdit){
        let listid = listEdit[a].id;
        for(let b in chooseGood2){
            let chooid = chooseGood2[b].id;
            if(listid === chooid){
                chooseGood2.splice(b,1);
            }
        }
    }
    console.log(chooseGood2);
    let getoname = chooseGood2.map((item,index) =>{
        return item.name;
    });
    let constleng = getoname.length;
    $("#contdown").html(constleng);
    let namestr = getoname.join("、");
    $("#goodLust").html(namestr);

    bounce_Fixed3.cancel($("#removeout"));
    bounce_Fixed3.show($("#newcontractpro"));
}

// creator: sy 2022-06-05 是否能开增值税发票
function vatInvoice(type, thisObj){
    setRadioSelect("vatInvoice",[0,1],type,thisObj);
    var vatInvoice = $("#vatInvoice").val();
    if(vatInvoice === "1"){
        $(".godemo_1").show();
        // $("#e_gRate0").val("");
        $("#vatInvoice2").attr("class","fa fa fa-circle-o");
    }else{
        $(".godemo_1").hide();
        $("#vatInvoice2").attr("class","fa fa-dot-circle-o");
        // $("#e_gRate0").val("");
    }
    if(type == 1 || type == "" || type == null){
        $(".godemo_1").show();
        return false;
    }else{
        $("#huip").hide();
        $(".godemo_1").hide();
        // $("#e_gRate0").val("");
        // var ond = $("#addAccount").data("chose");
        // if(ond === "upode"){
        //     var two = $(".twoz").html();
        //     if(two.length>0){
        //         layer.msg("操作失败！\n" +
        //             "因为该供应商供应有开具增值税专用发票的材料。\n" +
        //             "请先修改这些材料的价格！");
        //     }
        // }
    }
    // chargeshow8();
}

// creator: sy 2024-01-25   展示错误数据
function wronmes(diffkeo){
    $.ajax({
        url:"../supplier/import/getImportData",
        data:{},
        success:function(res){
            let exImports = res.exImports || [];//-无法保存的供应商
            let okImports = res.okImports || [];//-可保存的供应商
            let delImports = res.delImports || 0;//-已放弃的数量
            let allank = [];
            let exnumb = exImports.length;
            let oknumb = okImports.length;
            let delnumb = delImports;
            allank = exImports.concat(okImports);
            let sum = exnumb + oknumb;
            let distinguish = "";
            if(exnumb > 0){
                distinguish = 'rong';
            }else{
                distinguish = 'true';
            }
            switch (distinguish) {
                case 'rong'://代表错误数据
                    //setPage($("#ye_stopen1"),)
                    $("#roye1").html("");
                    $(".allate").html(sum);
                    $(".wrong").html(exnumb);
                    let strex = ``;
                    if(exnumb == 0){}else{
                        exImports.forEach(itemex => {
                            let name = itemex.name;
                            switch (name) {
                                case "":
                                    name = itemex.fullName.substring(0,6);
                                    break;
                                default:
                                    name = name.substring(0,6);
                                    break;
                            }
                            strex += `
                                <tr>
                                    <td class="userName">${handleNull(itemex.fullName)}</td>
                                    <td>${handleNull(name)}</td>
                                    <td>${handleNull(itemex.codeName)}</td>
                                    <td>
                                        <span class="ty-color-blue updentepo" data-type="update" data-name="upen" onclick="updentepo($(this))">修改</span>
                                        <span class="ty-color-red dettenpo" data-type="delete" data-name="deet" onclick="dettenpo($(this))">删除</span>
                                        <span class="hd">${JSON.stringify(itemex)}</span>
                                    </td>
                                </tr>
                            `;
                            $("#roye1").html(strex);
                        })
                        $("#nextstart").data("mslink",exImports);
                    }
                    diffkeo = 1;
                    $("#home").hide();
                    $("#rongmess").show();
                    $(".updentepo").data("inlinks",allank);
                    $(".updentepo").data("lins",exImports);
                    break;
                case 'true':
                    $("#roye2").html("");
                    $(".allmes").html(oknumb);
                    $(".lastem").html(oknumb);
                    $(".dittem").html(delnumb);
                    let strex2 = ``;
                    okImports.forEach(itemk => {
                        let name = itemk.name;
                        switch (name) {
                            case "":
                                name = itemk.fullName.substring(0,6);
                                break;
                            default:
                                name = name.substring(0,6);
                                break;
                        }
                        strex2 += `
                            <tr>
                                    <td>${handleNull(itemk.fullName)}</td>
                                    <td>${handleNull(name)}</td>
                                    <td>${handleNull(itemk.codeName)}</td>
                                    <td>
                                        <select class="chosgetpos centten" onchange="zetgetchon('geta',$(this))" value="0">
                                            <option value="0">请选择</option>
                                            <option value="1">接受</option>
                                            <option value="2">不接受</option>
                                        </select>
                                    </td>
                                    <td>
                                        <select class="chospaypos centten" onchange="zetgetchon('pay',$(this))" value="0">
                                            <option value="0">请选择</option>
                                            <option value="1">需要</option>
                                            <option value="2">不需要</option>
                                            <option value="3">不确定</option>
                                        </select>
                                    </td>
                                    <td>
                                        <span class="ty-color-blue updentepo" data-type="update" data-name="upen" onclick="updentepo($(this),2)">修改</span>
                                        <span class="ty-color-red dettenpo" data-type="delete" data-name="deet" onclick="dettenpo($(this),2)">删除</span>
                                        <span class="hd">${JSON.stringify(itemk)}</span>
                                    </td>
                                </tr>`;
                        $("#roye2").html(strex2);
                    })
                    diffkeo = 2;
                    $("#home").hide();
                    $("#truemess").show();
                    $(".updentepo").data("inlinks",allank);
                    $(".chosgetpos").data("lins",okImports);
                    $(".chospaypos").data("lins",okImports);
                    break;
            }
            $(".chosgetpos").data("lins",okImports);
            $("#nextstart2").data("lins",okImports);
        }
    })
}

// creator: sy 2022-07-08 点击“本合同的续约记录”后的弹窗显示
/*function xylook(){
    bounce_Fixed2.show($("#renewRecord"))
    bounce_Fixed3.cancel()
    let str = ``;
    let primaryId = $("#lookpoot").data("primaryId");
    $("#contractList tbody").html(str);
    $.ajax({
        url:"../supplier/poContractSignRecord.do",
        data:{primaryId: primaryId},
        success:function(res){
            let list = res.data.list || [];
            if (list.length > 0) {
                for (let r in list){
                    if(r == '0'){
                        str +=
                            '<tr>'+
                            '<td id="tatle">第1版（原始版本）</td>'+
                            '<td><span class="ty-color-blue" data-id="'+list[r].id+'" data-frontid="0" onclick="contractRecordScan($(this), \'renew\')">查看</span>' +
                            '<span class="hd">'+ JSON.stringify(list[r]) +'</span>' +
                            '</td>'+
                            '<td>'+list[r].createName+'&nbsp;'+new Date(list[r].createDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                            '</tr>';
                    }else{
                        var front = Number(r) -1;
                        str +=
                            '<tr>'+
                            '<td id="tatle">第'+r+'版（第r-1次续约后）</td>'+
                            '<td><span class="ty-color-blue" data-id="'+list[r].id+'" data-frontid="'+list[front].id+'" onclick="contractRecordScan($(this), \'renew\')">查看</span> ' +
                            '<span class="hd">'+ JSON.stringify(list[r]) +'</span>' +
                            '</td>'+
                            '<td>'+list[r].updateName+'&nbsp;'+new Date(list[r].updateDate).format('yyyy-MM-dd hh:mm:ss')+'</td>'+
                            '</tr>';
                    }
                }
            }
            $("#renewRecordList tbody").html(str);
        }
    })
}*/
// creator: sy 2022-07-12 “已到期的合同”弹窗中的“续约”按钮
/*function xytimeomre(senve){
    // $("#newcontractpro").data("img","");
    // $("#newcontractpro input").val("");
    initCUpload2($("#cUpload2-1"),'img');
    initCUpload2($("#cUpload2-2"),'doc');
    $("#addpost1").hide();
    $("#addpost2").show();
    $("#movepost1").hide();
    $("#movepost2").show();
    $("#updatebont").data("typeu",1);
    $("#q3").show();
    $("#q1").hide();
    $("#q2").hide();
    $("#editContractOk1").hide();
    $("#updatebont").show();
    $("#newcontractpro .bounce_close").attr("onclick","othersba($(this))");
    contractBaseRenew(senve);
}*/

// creator: sy 2022-07-13  已被停用的邮寄信息中的修改记录
function yjAddressback(denu){
    bounce_Fixed2.cancel($("#stopsendms"));
    yjAddressUpdateLogu(denu);
}
// creator: sy 2022-07-08 邮寄信息停用（常规信息修改弹窗）
function yjAddressDel(thisObj){
    var addss = thisObj.siblings(".hd").html();
    addss = JSON.parse(addss);
    var addsid = addss.id;
    var enabled = 0;
    $.ajax({
        url:"../supplier/startOrStopAddress.do",
        data:{
            addressId:addsid,
            enabled:enabled
        },
        success:function(res){
            var state = res.success;
            if(state === "1"|| state === 1){
                // layer.msg("停用成功");
                supplierInfoEdit(editObj);
            }
        }
    })
}
// creator: sy 2022-07-07 邮寄信息修改（常规信息修改弹窗）
function yjAddressUpdate(thisObj){
    var mailId = $("#updateSupPanel").data("id");
    $("#addAccount").data("contactInfo","[]");
    $("#addAccount").data("receiveInfo","[]");
    $("#mailName").data("contractInfo","[]");
    $("#mailName").data("font","upen");
    $("#newMailInfo").data("type","update");
    $("#newMailInfo").data("source","updateCustomer");
    $("#newMailInfo").data("add",0);
    $("#newMailInfo .bonceHead span").html("修改邮寄信息");
    $("#before").hide();
    $("#after").show();
    $("#addMail").hide();
    $("#addMailml").hide();
    $("#addMailmn").show();
    var datao = {
        type:"2"
    }
    var yjin = thisObj.siblings(".hd").html();
    yjin = JSON.parse(yjin);
    var yjid = yjin.id;
    $.ajax({
        url:"../supplier/getAddressData.do",
        data:{
            addressId:yjid,
        },
        success:function(res){
            console.log(res);
            var messge = res.data;
            $("#mailAddress").val(messge.address);
            $("#mailNumber").val(messge.postcode);
            $("#mailName").val(messge.contact);
        }
    })
    // setAddress(datao.type,yjin);
    // $("#mailAddress").val(yjin.address);
    // $("#mailNumber").val(yjin.postcode);
    // $("#mailName").val(yjin.name);
    bounce_Fixed2.show($("#newMailInfo"));
}
// creator: sy 2022-07-07 邮寄信息修改记录（常规信息修改弹窗）
function yjAddressUpdateLog(oble){
    var type = oble.data('type');
    var getObj = oble.data('obj');
    $(".recordTtl").html("邮寄地址修改记录");
    var yji = oble.parent("td").children(".hd").html();
    // yji = JSON.parse(yji);
    var yjid = yji;
    $.ajax({
        url:'../supplier/getRecordAddressList.do',
        data:{
            id:yjid
        },
        success:function(res){
            var state = res.success;
            if(state === "1" || state ===1){
                getRecordsyj(res,type);
                // layer.msg("查询成功");
            }else{
                layer.msg("查询失败");
            }
        }
    })

}
// creator: sy 2022-07-12 供应商常规修改弹窗中邮寄信息中修改记录
function yjAddressUpdateLogu(yjon){
    $("#pacelook").hide();
    $("#upcane").show();
    yjAddressUpdateLog(yjon);
}
// creator: sy 2022-07-12 点击供应商查看弹窗中位于“邮寄信息”中的“修改记录”按钮
function yjccent(yjxg){
    $("#pacelook").show();
    $("#upcane").hide();
    let onk = $("#havelook").data("id");
    yjAddressUpdateLog(yjxg);
}

// creator: sy 2024-02-02   监听筛选框的选择
function zetgetchon(difen,jsonk){
    let one = $(".chosgetpos").data("link") || "";
    let two = $(".chospaypos").data("link") || "";
    let numa = "";
    let payk = "";
    let link = {};
    let link2 = {};
    switch (difen) {
        case 'geta':
            link = jsonk.parent().next().next().children(".hd").html();
            if(link == {}){
                link = jsonk.parent().next().children(".hd").html();
            }
            link = JSON.parse(link);
            let chosen = jsonk.children("option:selected").text();
            switch (chosen) {
                case '接受':
                    numa = 1;
                    break;
                case '不接受':
                    numa = 0;
                    break;
            }
            if(one == "" && two == ""){
                link.chargeAcceptable = numa;
            }else {
                if(one == ""){
                    if(two.id == link.id){
                        two.chargeAcceptable = numa;
                        link = two;
                    }else{
                        link.chargeAcceptable = numa;
                    }
                }else{
                    if(one.id == link.id){
                        one.chargeAcceptable = numa;
                        link = one;
                    }else{
                        link.chargeAcceptable = numa;
                    }
                }
            }
            $(".chosgetpos").data("link",link);
            break;
        case 'pay':
            link2 = jsonk.parent().next().children(".hd").html();
            if(link2 == {}){
                link2 = jsonk.parent().next().next().children(".hd").html();
            }
            link2 = JSON.parse(link2);
            let chosen2 = jsonk.children("option:selected").text();
            switch (chosen2) {
                case '需要':
                    payk = 1;
                    break;
                case '不需要':
                    payk = 0;
                    break;
                case '不确定':
                    payk = 2;
                    break;
            }
            if(two == "" && one == ""){
                link2.isImprest = payk;
            }else{
                if(two == ""){
                    if(one.id == link2.id){
                        one.isImprest = payk;
                        link2 = one;
                    }else{
                        link2.isImprest = payk;
                    }
                }else{
                    if(two.id == link2.id){
                        two.isImprest = payk;
                        link2 = two;
                    }else{
                        link2.isImprest = payk;
                    }
                }
            }
            link = link2;
            $(".chospaypos").data("link",link);
            break;
    }
    console.log('link现在的数据',link);//link中是有选择的数据
    let allink = $(".chosgetpos").data("lins") || [];
    let lentd = allink.length;
    if(lentd == 0){
        allink = $(".chospaypos").data("lins") || [];
        if(lentd == 0){
            allink.push(link);
        }else{
            allink.forEach(itemall => {
                let id = link.id;
                if(id == itemall.id){
                    if(link.chargeAcceptable == null){
                        itemall.isImprest = link.isImprest;
                    }else if(link.isImprest == null){
                        itemall.chargeAcceptable = link.chargeAcceptable;
                    }else{
                        itemall.chargeAcceptable = link.chargeAcceptable;
                        itemall.isImprest = link.isImprest;
                    }
                }
            })
        }
    }else{
        allink.forEach(itemall => {
            let id = link.id;
            if(id == itemall.id){
                if(link.chargeAcceptable == null){
                    itemall.isImprest = link.isImprest;
                }else if(link.isImprest == null){
                    itemall.chargeAcceptable = link.chargeAcceptable;
                }else{
                    itemall.chargeAcceptable = link.chargeAcceptable;
                    itemall.isImprest = link.isImprest;
                }
            }
        })
    }
    console.log('lins',allink);//allink没有包含之前的数据
    $(".chosgetpos").data("lins",allink);
    $(".chospaypos").data("lins",allink);
    $(".updentepo").data("lins",allink);
    $(".dettenpo").data("lins",allink);
    $("#nextstart2").data("lins",allink);
    $(".updentepo").data("link",link);
    $(".dettenpo").data("link",link);
}
function getContract(type, source){
    var cid = '';
    if (source === 'see') {
        cid = $("#havelook").data("id");
    } else if (source === 'edit'){
        cid =$("#updateSupPanel").data("id");
    }
    $.ajax({
        url:"../supplier/listContractBase.do",
        data:{
            supplierId: cid, //供应商id
            type: type  //1-正常 2-终止 3-到期
        },
        success:function(res){
            var state = res.success;
            if(state === "1"|| state === 1){
                let contractBaseList = res.data.contractBaseList || [] // 合同
                let contractBaseStr = ``
                if (type === 1){
                    if (source === 'edit') {
                        contractBaseList.forEach((contractBaseItem)=>{
                            contractBaseStr += `
                                <tr class="contractItem" data-id="${contractBaseItem.id}">
                                 <td class="">${ new Date(contractBaseItem.validEnd).format('yyyy-MM-dd') }到期/ ${contractBaseItem.sn}</td>
                                 <td> 
                                    <span class="ty-color-blue node dfg" data-name="cScan">查看</span>
                                    <span class="ty-color-blue node dfg" data-name="contractInfo" data-type="update" data-source="updateCustomer">修改合同信息</span>
                                    <span class="ty-color-blue node dfg" data-name="contractInfo" data-type="cRenewal" data-source="updateCustomer">续约</span>
                                    <span class="ty-color-red  node dfg" data-name="cEnd">暂停履约/合同终止</span>
                                    <span class="hd">${ JSON.stringify(contractBaseItem) }</span>
                                  </td>
                               </tr>`
                        })
                        $("#contractPanel").after(contractBaseStr);
                    } else if(source === 'see') {
                        if(contractBaseList.length == 0){
                            $(".contractPlaceList tbody").html("");
                        }else{
                            var str = ``;
                            contractBaseList.forEach(function (item) {
                                str += `
                                <tr class="contractItem" data-id="${item.id}">
                                    <td>${item.sn}</td>
                                    <td>${formatTime(item.signTime)}</td>
                                    <td>${formatTime(item.validStart)} 至 ${formatTime(item.validEnd)}</td>
                                    <td><span class="ty-color-blue node" data-name="scanGs">${ item.materialCount || 0 }</span>种</td>
                                    <td>
                                        <span class="ty-color-blue node" data-name="cScan" >查看</span>
                                    </td>
                                </tr>`;
                            })
                            $(".contractPlaceList tbody").html(str);
                        }
                    }

                }
            }
        }
    })
}
// creator : 2024-8-21 李玉婷  合同的商品列表
function cScanScanGs(cid) {
    $.ajax({
        'url' : "../sales/getContractBase.do",
        "data":{ "id": cid },
        success:function (res) {
            var newcInfo = res.data
            var status = res.status
            if(status != 1){
                layer.msg("获取合同信息失败！");
                return false;
            }
            $("#tipcontractGoods .bonceHead span").html('本合同下的材料');
            $(".addOrCancel").hide(); $(".cScanc").show();
            let productList = newcInfo.productList || []
            setContactGS(productList , false);
            $("#cGTip").html(`本合同下的材料共有以下${ productList.length }种`)

        }
    })
}
function getContractChangeLog(id) {
    $.ajax({
        url: $.webRoot + '/supplier/poContractBaseHistory.do',
        data: {
            id: id
        }
    }).then(res => {
        let list = res.data.list || []
        let str = ''
        for (let i in list) {
            let item = list[i]
            let create = ''
            if (i === '0') {
                create = item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")
            } else {
                create = item.updateName + ' ' + moment(item.updateDate).format("YYYY-MM-DD HH:mm:ss")
            }
            str += `<tr data-id="${item.id}">
                        <td>${i==='0'?'本版本合同的原始信息':'第' + i + '次修改后'}</td>
                        <td>
                            <span class="link-blue" data-name="seeContractChangeHisDetail" type="btn">查看</span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                        <td>${create}</td>
                    </tr>`
        }
        let lastItem = list[list.length-1]
        $("#contractChangeLog tbody").html(str)
        let tips = ''
        if (list.length === 1) {
            tips = '当前数据尚未修改'
        } else {
            tips = ` 当前数据为本版本合同第${list.length-1}次修改后的结果。
                    <div class="ty-right">
                        修改时间：${lastItem.updateName + ' ' + moment(lastItem.updateDate).format("YYYY-MM-DD HH:mm:ss")}
                    </div>`
        }
        $("#contractChangeLog .tips").html(tips)
        bounce_Fixed.show($("#contractChangeLog"))
        bounce_Fixed2.cancel()
    })
}

// creator: 张旭博，2024-07-03 03:16:34， 合同续约记录
function getContractRenewLog(id) {
    $.ajax({
        url: $.webRoot + '/supplier/poContractSignRecord.do',
        data: {
            primaryId: id
        }
    }).then(res => {
        let list = res.data.list || []
        let str = ''
        for (let i in list) {
            let item = list[i]
            let create = item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")
            str += `<tr data-id="${item.id}">
                        <td>${i==='0'?'第1版（原始版本）':'第' + (Number(i) + 1) + '版（第' + i + '次续约后）'}</td>
                        <td>
                          <span class="link-blue" data-name="seeContractRenewHisDetail" type="btn">查看</span>
                          <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                        <td>${create}</td>
                    </tr>`
        }
        let lastItem = list[list.length-1]
        $("#contractRenewLog tbody").html(str)
        bounce_Fixed.show($("#contractRenewLog"))
        bounce_Fixed2.cancel()
    })
}

// creator: 张旭博，2024-07-09 04:08:40， 查看修改记录或者续约记录的合同详情
function seeContractHisDetail(contractHisId, type) {
    let url = '', data = {}
    if (type === 'change') {
        url = $.webRoot + '/supplier/poContractBaseHisMes.do'
        data = { contractHisId: contractHisId }
    } else {
        url = $.webRoot + '/supplier/poContractBaseMes.do'
        data = { id: contractHisId }
    }
    $.ajax({
        url: url,
        data: data
    }).then(res => {
        let data = res.data
        let contractBase = {} // 合同信息（大多数据都从这里取出，尤其是合同id，要用这个关键字下的）
        let listImage = [] // 合同的扫描件或照片
        let listTY = [] // 通用型商品
        if (type === 'change') {
            data.contractBase = data.contracthistory
            data.listImage = data.listHisImage || []
            data.listMt = data.listMtHis
        }
        contractBase = data.contractBase
        listImage = data.listImage || []
        listTY = data.listMt || []
        let imageStr = ''
        for (let i in listImage) {
            imageStr += `<span class="link-blue fileImScan" data-fun="imgScan" path="${listImage[i].uplaodPath}">${Number(i) + 1 } <i class="hd">${JSON.stringify(listImage[i])}</i></span>`
        }
        let fileStr = ''
        if (contractBase.filePath) {
            fileStr = ` <a class="link-blue cWord node" data-fun="cWord" path="${contractBase.filePath}">查看</a>`
        }
        let showData = {
            sn: contractBase.sn,
            signTime: contractBase.signTime?moment(contractBase.signTime).format("YYYY-MM-DD"):'',
            validTime: moment(contractBase.validStart).format("YYYY-MM-DD") + ' 至 ' + moment(contractBase.validEnd).format("YYYY-MM-DD"),
            memo: contractBase.memo,
            image: imageStr,
            file: fileStr,
            tyNum: listTY.length
        }
        if (type === 'change') {
            bounce_Fixed3.show($("#cScanHis"))

            for (let key in showData) {
                $("#cScanHis .contract_see_" + key).html(showData[key])
            }
        } else {
            bounce_Fixed2.show($("#cScan"))
            for (let key in showData) {
                $("#cScan .contract_see_" + key).html(showData[key])
            }
        }
        $("#cScan").data("contractInfo", data)
    })
}
// creator : 2021-5-24 hxz 已暂停/终止的合同
function contractStopData(customer) {
    $.ajax({
        url: $.webRoot + "/supplier/listContractBase.do",
        data: { supplierId: customer, type: 2 },
        success:function (res) {
            let data = res.data || []
            let list = data.contractBaseList
            let str = ``;
            list.forEach(function (item) {
                str += `
                  <tr class="contractItem" data-id="${item.id}">
                    <td>${ item.sn }</td>
                    <td>${ new Date(item.suspendTime).format("yyyy-MM-dd hh:mm:ss")  }</td>
                    <td>
                        <span class="link-blue edit2" type="btn" data-name="cScan">查看</span>
                        <span class="link-blue edit2" type="btn" data-name="cRestart"> 恢复履约/重启合作</span>
                        <span class="hd">${ JSON.stringify(item)}</span>
                    </td>
                </tr>
                 `;
            })
            $("#contractStopData tbody").html(str)
            bounce_Fixed.show($("#contractStopData"));
        }
    })
}
// creator : 2021-5-24 hxz 已到期的合同
function contractEndData() {
    let customer = $("#updateSupPanel").data("id")
    $.ajax({
        url: $.webRoot + "/supplier/listContractBase.do",
        data: { supplierId: customer, type: 3 },//type String 1-正常 2-终止 3-到期
    }).then(res => {
        let data = res.data || []
        let list = data.contractBaseList
        let str = ``;
        list.forEach(function (item) {
            str += `
                  <tr class="contractItem" data-id="${item.id}">
                    <td>${ item.sn }</td>
                    <td>${ moment(item.validEnd).format("YYYY-MM-DD")  }</td>
                    <td>
                        <span class="link-blue edit2" type='btn' data-name="cScan">查看</span>
                        <span class="link-blue edit2" type='btn' data-name="cRenewal">续约</span>  
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
                 `;
        })
        $("#contractEndData tbody").html(str)
        bounce_Fixed.show($("#contractEndData"))
    })
}
// creator : 2021-5-24 hxz 暂停履约/合同终止
function cEndOk(type) {
    let id = $("#cEndTip").data("cid")
    let enabled = $("#cEndTip").data("enabled")
    if (enabled === 1) {
        $.ajax({
            url: $.webRoot + '/supplier/reStartPoContract.do',
            data: { id: id, type: type  }
        }).then(res => {
            let data = res.data
            let state = data.state
            if(state === 1){
                layer.msg("操作成功！")
                var cusid = $("#updateSupPanel").data("id")
                if(enabled == 1){
                    bounce_Fixed.cancel()
                    // contractStopData(cusid)
                }
                supplierInfoEdit(editObj);

            } else if (state === 0) {
                layer.msg('请勿重复操作！')
            } else {
                layer.msg('操作失败！')
            }
            bounce_Fixed3.cancel();
        })
    } else {
        $.ajax({
            url: $.webRoot + '/supplier/terminateContract.do',
            data: { id: id  }
        }).then(res => {
            let data = res.data
            let state = data.state
            if(state === 1){
                layer.msg("操作成功！")
                var cusid = $("#updateSupPanel").data("id")
                if(enabled == 1){
                    bounce_Fixed.cancel()
                    // contractStopData(cusid)
                }
                supplierInfoEdit(editObj);

            } else if (state === 0) {
                layer.msg('请勿重复操作！')
            } else {
                layer.msg('操作失败！')
            }
            bounce_Fixed3.cancel();
        })
    }

}
// 上传图片
var addCusContact = [];

laydate.render({elem:'.cSignDatecon',format:'yyyy-MM-dd'});
laydate.render({elem:'.cStartDatecon',format:'yyyy-MM-dd'});
laydate.render({elem:'.cEndDatecon',format:'yyyy-MM-dd'});
laydate.render({elem:'.cSignDate',format:'yyyy-MM-dd'});
laydate.render({elem:'.cStartDate',format:'yyyy-MM-dd'});
laydate.render({elem:'.cEndDate',format:'yyyy-MM-dd'});

