var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#customParameters"));
bounce_Fixed2.cancel();
$(function (){
    // MCI Material材料 Caigou采购 Index指数 （材料采购指数）
    jumpPage("main", function () {
        $(".backBtn").hide()
        getMCIList(2)
    })
    $("body").on("keyup", ".inputNum", function () {
        if ($(this).hasClass("link")) {
            clearNum(this)
        } else {
            clearNum0(this)
        }

    })
    $("body").on("keyup", ".inputFloat", function () {
        clearNoNumN(this, 2)
    })
    $("#home").on("click", "button", function () {
        var name = $(this).attr("name")
        let id = $(this).parents("tr").data("id")

        switch(name) {
            // 算法管理 - 按钮
            case 'algorithmManage':
                let no = $(this).parents("tr").data("no")

                if (no === '2-16') {
                    jumpPage("2-16")
                    getMCIList(1)
                } else {
                    var title = $(this).parents("tr").find("td").eq(0).html()
                    $("#algorithmManage").data("eisIndex", id)
                    getAecByAlgorithmCat().then(res => {
                        bounce.show($("#algorithmManage"))
                    })
                    $("#algorithmManage .indexName").html(title)
                }
                break
            case 'sureAdjustWeight':
                // 权重调整 - 确定
                var weights = []
                var count = 0
                $(".page_weightAdjustment tbody tr").each(function (){
                    var id = $(this).data("id")
                    var weight = $(this).find("input").val()
                    weights.push({
                        id:id,
                        weight: Number(weight)
                    })
                    count += Number(weight)
                })
                if (Number(count.toFixed()) !== 100) {
                    layer.msg("请检查数据，各指标所占权重之和需等于100%！")
                    return false
                }
                $.ajax({
                    url: "../eis/edit",
                    data: {
                        eisIndicesStr: JSON.stringify(weights)
                    },
                    success: function (res) {
                        var code = res.code
                        if (code === 0) {
                            layer.msg(res.msg)
                            back()
                            var page = $(".page:visible").attr("page")
                            getMCIList(page === 'main'?2:1)
                        } else {
                            layer.msg("操作失败")
                        }
                    }
                })
                break
            case 'seeRecord':
                var versionNo = $(this).parents("tr").data("version")
                var category = $(".page_weightAdjustmentRecord").data("category")
                jumpPage("weightAdjustmentRecordSee")
                $.ajax({
                    url: '../eis/history/list',
                    data: {
                        versionNo: versionNo,
                        category: category
                    },
                    success: function (res) {
                        var list = res.result
                        if (list && list.length > 0) {
                            list.sort((a,b) => {
                                return a.module.slice(2, a.module.length) - b.module.slice(2, b.module.length)
                            })
                            var str = ''

                            for (var i in list) {

                                str +=  '<tr>' +
                                    '    <td>'+list[i].indexName+'</td>' +
                                    '    <td>'+list[i].weight+'%</td>' +
                                    '</tr>'
                            }

                            $(".page_record_weightAdjustmentRecordSee").find("tbody").html(str)
                        } else {
                            layer.msg("数据错误")
                        }
                    }
                })
                break
        }
    })
    $("table.table_customParam").on("click", "button", function () {
        var name = $(this).attr("name")
        switch(name) {
            case 'addOneRow':
                var cloneObj = $("#customParameters").data("addRow")
                var addLength =  $(this).parents("table").find("tbody tr").length
                if (addLength > 8) {
                    layer.msg("已达到最大新增行")
                    return false
                }
                $(this).parents("table").find("tbody tr").eq(-1).before('<tr>'+cloneObj+'</tr>')
                $(this).parents("table").find("tbody tr").eq(-2).find(".inputNum").blur()
                $(this).parents("table").find("tbody tr").eq(-3).find(".inputNum").blur()
                break
            case 'delRow':
                var prev = $(this).parents("tr").prev("tr").find(".inputNum").eq(0)
                $(this).parents("tr").remove()
                prev.blur()
                break
        }
    })
    $("table.table_customParam").on("input blur", "input.link", function () {
        var val = $(this).val()
        var no = $("#algorithmManage").data("no")
        if (val === '') {
            $(this).parents("tr").next("tr").find(".minNum").html('')
        } else {
            if (no === '2-4' || no === '2-6' || no === '2-8') {
                $(this).parents("tr").next("tr").find(".minNum").html(val)
            } else {
                $(this).parents("tr").next("tr").find(".minNum").html(parseInt(Number(val) + 1))
            }
        }
    })
    $(".bounce").on("click", 'button', function () {
        var name = $(this).attr("name")
        switch (name) {
            case 'customParam':
                // 自定义参数
                var no = $("#algorithmManage").data("no")
                initCustomParam(no)
                break
            case 'changePriceCoefficient':
                // 修改价格补偿系数
                bounce_Fixed2.show($("#changePriceCoefficient"))
                // 修改价格补偿系数 - 绑定确定事件
                $("#changePriceCoefficient .sureBtn").unbind().on("click", function (){
                    var eisIndex = $("#algorithmManage").data("eisIndex")
                    var data = []
                    var isNull = validateNull($("#changePriceCoefficient .table_customParam"))
                    if (!isNull) {return false}
                    $("#changePriceCoefficient .table_customParam tbody tr").each(function () {
                        var item = {
                            eisIndex: eisIndex,
                            id: $(this).attr("data-id"),
                            value: $(this).find("td").eq(1).find(".inputFloat").val() || $(this).find("td").eq(1).html()
                        }
                        data.push(item)
                    })
                    var validate = true
                    for (var i=0; i<data.length; i++) {
                        var item = data[i]
                        var value = item.value
                        if (i > 0) {
                            if (value < 1 || value == 1) {
                                validate = false
                            }
                        }
                    }
                    if (!validate) {
                        layer.msg("数据验证失败！请检查录入数据")
                        return false
                    }
                    $.ajax({
                        url: `../eis/param/edit?eisIndex=${eisIndex}`,
                        data: JSON.stringify(data),
                        contentType: 'application/json',
                        success: function (res){
                            var state = res.code
                            if (state === 0) {
                                layer.msg("操作成功")
                                bounce_Fixed2.cancel()
                                getAecByAlgorithmCat()
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
                break
            case 'changeAlgorithm':
                // 修改算法
                bounce_Fixed.show($("#changeAlgorithm"))
                $("#changeAlgorithm").removeData('custom')
                $("#changeAlgorithm input:radio").prop("checked", false)
                var algorithmCat = $("#algorithmManage").data("algorithmCat")
                if (algorithmCat === 2) {
                    // 当前是步进改为区间，显示区间弹窗
                    $("#changeAlgorithm .sectionChange").show().siblings(".stepwiseChange").hide()
                } else {
                    // 当前是区间改为步进，显示步进弹窗
                    $("#changeAlgorithm .stepwiseChange").show().siblings(".sectionChange").hide()
                }
                $("#changeAlgorithm .sureBtn").unbind().on("click", function (){
                    var custom = $("#changeAlgorithm").data('custom') || []
                    var eisIndex = $("#algorithmManage").data("eisIndex")
                    var checked = $("#changeAlgorithm input:radio").prop("checked")
                    if (!checked) {
                        layer.msg("请勾选对应内容！")
                        return false
                    }
                    if (algorithmCat === 2) {
                        var url = `../eis/changeAlgorithmCatToSection?id=${eisIndex}&algorithmCat=4`
                        $.ajax({
                            url: url,
                            data: JSON.stringify(custom.data),
                            contentType: 'application/json',
                            success: function (res) {
                                var code = res.code
                                if (code === 0) {
                                    layer.msg("操作成功")
                                    bounce_Fixed.cancel()
                                    getAecByAlgorithmCat()
                                } else {
                                    layer.msg("操作失败")
                                }
                            }
                        })
                    } else {
                        var url = `../eis/changeAlgorithmCatToStep`
                        $.ajax({
                            url: url,
                            data: {
                                id:eisIndex,
                                upperLimit:custom.upperLimit || 1,
                                algorithmCat:2
                            },
                            success: function (res) {
                                var code = res.code
                                if (code === 0) {
                                    layer.msg("操作成功")
                                    bounce_Fixed.cancel()
                                    getAecByAlgorithmCat()
                                } else {
                                    layer.msg("操作失败")
                                }
                                console.log('change', res)
                            }
                        })
                    }
                })
                break
            case 'changeRecord':
                var eisIndex = $("#algorithmManage").data("eisIndex")
                var indexName = $("#algorithmManage").data("indexName")
                bounce_Fixed.show($("#changeRecord"))
                $("#changeRecord").find(".indexName").html(indexName)
                $.ajax({
                    url: `../eis/alHistory?eisIndex=${eisIndex}`,
                    type: 'GET',
                    success: function (res) {
                        var sectionHistory = res
                        // var sectionHistory = data.sectionHistory
                        // var stepHistory = data.stepHistory
                        var str = ''

                        for (var i = 0; i < sectionHistory.length; i++) {
                            if (i === 0) {
                                str +=  '<tr data-version="'+sectionHistory[i].versionNo+'" data-algorithmcat="'+sectionHistory[i].algorithmCat+'">' +
                                    '    <td>初始算法</td>' +
                                    '    <td>创建时间：'+sectionHistory[i].createDate+'</td>' +
                                    '    <td><button class="link-blue" type="btn" name="seeAlgorithmRecord">查看</button></td>' +
                                    '</tr>'
                            } else {
                                str +=  '<tr data-version="'+sectionHistory[i].versionNo+'" data-algorithmcat="'+sectionHistory[i].algorithmCat+'">' +
                                    '    <td>第'+i+'次修改后</td>' +
                                    '    <td>修改时间：'+sectionHistory[i].createDate+'</td>' +
                                    '    <td><button class="link-blue" type="btn" name="seeAlgorithmRecord">查看</button></td>' +
                                    '</tr>'
                            }

                        }
                        var length = sectionHistory.length
                        $("#changeRecord .changeNum").html(length > 0?(length - 1): 0)
                        $("#changeRecord tbody").html(str)
                    }
                })
                break
            case 'priceChangeRecord':
                var eisIndex = $("#algorithmManage").data("eisIndex")
                var indexName = $("#algorithmManage").data("indexName")
                bounce_Fixed.show($("#changeRecord"))
                $("#changeRecord").find(".indexName").html('价格补偿系数')
                $.ajax({
                    url: `../eis/param/epHistory?id=${eisIndex}`,
                    success: function (res) {
                        var sectionHistory = res
                        // var sectionHistory = data.sectionHistory
                        // var stepHistory = data.stepHistory
                        var str = ''
                        var changeNum = sectionHistory.length

                        for (var i = 0; i < sectionHistory.length; i++) {
                            if (i === 0) {
                                str +=  '<tr data-version="'+sectionHistory[i].versionNo+'" data-algorithmcat="'+sectionHistory[i].algorithmCat+'">' +
                                    '    <td>初始算法</td>' +
                                    '    <td>创建时间：'+sectionHistory[i].createDate+'</td>' +
                                    '    <td><button class="link-blue" type="btn" name="seePriceAlgorithmRecord">查看</button></td>' +
                                    '</tr>'
                            } else {
                                str +=  '<tr data-version="'+sectionHistory[i].versionNo+'" data-algorithmcat="'+sectionHistory[i].algorithmCat+'">' +
                                    '    <td>第'+i+'次修改后</td>' +
                                    '    <td>修改时间：'+sectionHistory[i].createDate+'</td>' +
                                    '    <td><button class="link-blue" type="btn" name="seePriceAlgorithmRecord">查看</button></td>' +
                                    '</tr>'
                            }

                        }
                        $("#changeRecord .changeNum").html(changeNum?(changeNum-1):0)
                        $("#changeRecord tbody").html(str)
                    }
                })
                break

        }
    })
    $(".bounce_Fixed").on("click", 'button', function () {
        var name = $(this).attr("name")
        switch (name) {
            case 'seeAlgorithmRecord':
                var indexName = $("#algorithmManage").data("indexName")
                var eisIndex = $("#algorithmManage").data("eisIndex")
                var eisNo = $("#algorithmManage").data("no")
                var algorithmCat =  $(this).parents("tr").data("algorithmcat")
                var version = $(this).parents("tr").data("version")
                bounce_Fixed2.show($("#changeRecordSee"))
                $("#changeRecordSee").find(".indexName").html(indexName)
                $.ajax({
                    url: `../eis/historyDetail?eisIndex=${eisIndex}&versionNo=${version}&algorithmCat=${algorithmCat}`,
                    method: 'GET',
                    success: function (res) {
                        var list = res.sectionHistory // 算法
                        if (res) {
                            var tbodyStr = ''
                            if (algorithmCat === 1) {
                                var enumerate = res.enumerateHistory
                                if (eisNo === '2-9') {
                                    var enumerateStr = ''
                                    for (var i in enumerate) {
                                        enumerateStr += '<tr><td>'+enumerate[i].value+'</td>' + '<td><span class="mark">'+enumerate[i].mark+'</span></td></tr>'
                                    }
                                    $("#changeRecordSee .algorithm2-9 tbody").html(enumerateStr)
                                    $("#changeRecordSee .algorithm2-9").show().siblings().hide()
                                } else {
                                    for (var i in enumerate) {
                                        tbodyStr += '<tr><td>'+enumerate[i].value+'</td>' + '<td><span class="mark">'+enumerate[i].mark+'</span></td></tr>'
                                    }
                                    $("#changeRecordSee .algorithm1-1").show().siblings().hide()
                                    $("#changeRecordSee .algorithm1-1 tbody").html(tbodyStr)
                                }

                            } else if (algorithmCat === 2) {
                                if (eisNo === '1-5') {
                                    var stepHistory = res.stepHistory
                                    $("#changeRecordSee .stepwise1-5").show().siblings().hide()
                                    $("#changeRecordSee .algorithm1-5").show().siblings().hide()
                                    $("#changeRecordSee .algorithm1-5 .step").html(stepHistory[0].upperLimit)
                                }

                            } else if (algorithmCat === 4) {

                                tbodyStr = updateParamTbody(eisNo, list)
                                if (eisNo === '2-4' || eisNo === '2-6' || eisNo === '2-8' ) {
                                    eisNo = '2-4'
                                }
                                $("#changeRecordSee .algorithm" + eisNo).show().siblings().hide()
                                $("#changeRecordSee .algorithm" + eisNo + " tbody").html(tbodyStr)
                                if (eisNo === '1-5') {
                                    $("#changeRecordSee .section1-5").show().siblings().hide()
                                }
                            }
                        } else {
                            $("#changeRecordSee .algorithm1").show().siblings().hide()
                            $("#changeRecordSee .algorithm1 tbody").html('<tr><td colspan="2" class="text-center">暂无数据</td></tr>')
                            layer.msg("系统错误")
                        }
                    }
                })
                break
            case 'seePriceAlgorithmRecord':
                var indexName = $("#algorithmManage").data("indexName")
                var eisIndex = $("#algorithmManage").data("eisIndex")
                var eisNo = $("#algorithmManage").data("no")
                var algorithmCat =  $(this).parents("tr").data("algorithmcat")
                var version = $(this).parents("tr").data("version")
                bounce_Fixed2.show($("#changeRecordSee"))
                $("#changeRecordSee").find(".indexName").html(indexName)
                $.ajax({
                    url: `../eis/param/epHistoryDetail?id=${eisIndex}&versionNo=${version}`,
                    success: function (res) {
                        var param = res
                        var paramStr = ''
                        for (var j in param) {
                            paramStr += '<tr><td>'+param[j].name+'</td>' + '<td><span class="mark">'+param[j].value+'</span></td></tr>'
                        }
                        $("#changeRecordSee .algorithm2-9Price tbody").html(paramStr)
                        $("#changeRecordSee .algorithm2-9Price").show().siblings().hide()
                    }
                })
                break
            case 'customParamFake':
                var no = $("#algorithmManage").data("no")
                initCustomParam(no, true)
                break
        }
    })
})

// creator: 张旭博，2022-09-30 10:44:50， 获取指标列表
function getMCIList(category) {
    $.ajax({
        url: '../eis/list',
        data: {
            category: category
        },
        success: function (res) {
            var list = res
            if (list && list.length > 0) {
                list.sort((a,b) => {
                    return a.module.slice(2, a.module.length) - b.module.slice(2, b.module.length)
                })
                var str = ''
                for (var i in list) {

                    str +=  '<tr data-id="'+list[i].id+'" data-no="'+list[i].module+'">' +
                        '    <td>'+list[i].indexName+'</td>' +
                        '    <td>'+list[i].fullMark+'</td>' +
                        '    <td>'+list[i].weight+'%</td>' +
                        '    <td>' +
                        '        <button class="link-blue" name="algorithmManage">算法及管理</button>' +
                        '    </td>' +
                        '</tr>'
                }

                $(".page_" + (category === 2?'main': '2-16')).find("tbody").html(str)
            } else {
                layer.msg("数据错误")
            }
        }
    })
}

// creator: 张旭博，2022-09-30 10:51:45， 权重调整
function weightAdjustmentBtn () {
    var category = $(".page:visible").attr("page") === 'main'?2:1
    jumpPage("weightAdjustment")
    $.ajax({
        url: '../eis/list',
        data: {
            category: category
        },
        success: function (res) {
            var list = res
            list.sort((a,b) => {
                return a.module.slice(2, a.module.length) - b.module.slice(2, b.module.length)
            })

            var str = ''

            for (var i in list) {
                str +=  '<tr data-id="'+list[i].id+'" data-no="'+list[i].module+'">' +
                    '    <td>'+list[i].indexName+'</td>' +
                    '    <td>'+list[i].weight+'</td>' +
                    '    <td><input type="text" placeholder="请录入" class="inputNum" value="'+list[i].weight+'"> %</td>' +
                    '</tr>'
            }

            $(".page_weightAdjustment").find("tbody").html(str)
        }
    })
}

// creator: 张旭博，2022-12-26 11:23:53， 根据算法类型获取算法
function getAecByAlgorithmCat() {
    var eisIndex = $("#algorithmManage").data("eisIndex")
    return $.ajax({
        url: '../eis/aec/list',
        data: {
            eisIndex: eisIndex
        },
        success: function(res) {
            var eisInfo = res.eis // 算法信息
            var eisNo = eisInfo.module
            var algorithmCat = eisInfo.algorithmCat
            var indexName = eisInfo.indexName

            $("#algorithmManage").data("no", eisNo)
            $("#algorithmManage").data("algorithmCat", algorithmCat)
            $("#algorithmManage").data("indexName", indexName)

            var list = res.data // 算法
            if (res) {
                var tbodyStr = ''
                if (algorithmCat === 1) {
                    var param = res.param
                    var enumerate = res.data
                    if (eisNo === '2-9') {

                        var paramStr = ''
                        var enumerateStr = ''
                        for (var i in enumerate) {
                            enumerateStr += '<tr><td>'+enumerate[i].value+'</td>' + '<td><span class="mark">'+enumerate[i].mark+'</span></td></tr>'
                        }
                        $("#algorithmManage .algorithm2-9 .enumerate tbody").html(enumerateStr)
                        $("#algorithmManage .algorithm2-9 .enumerate").next("section").find(".algBeginTime").html(enumerate[0].enabledTime)
                        var inputStr = ''
                        for (var j=0; j<param.length;j++) {
                            paramStr += '<tr><td>'+param[j].name+'</td>' + '<td><span class="mark">'+param[j].value+'</></td></tr>'
                            if (j === 0) {
                                inputStr += '<tr data-id="'+param[j].id+'"><td>'+param[j].name+'</td>' + '<td>'+param[j].value+'</td></tr>'
                            } else {
                                inputStr += '<tr data-id="'+param[j].id+'"><td>'+param[j].name+'</td>' + '<td><input type="text" placeholder="请录入" class="inputFloat"></td></tr>'
                            }
                        }
                        $("#algorithmManage .algorithm2-9 .param tbody").html(paramStr)
                        $("#changePriceCoefficient tbody").html(inputStr)
                        $("#algorithmManage .algorithm2-9").show().siblings().hide()
                        $("#algorithmManage .algorithm2-9 .param").next("section").find(".algBeginTime").html(param[0].enabledTime)
                    } else {
                        for (var i in enumerate) {
                            tbodyStr += '<tr><td>'+enumerate[i].value+'</td>' + '<td><span class="mark">'+enumerate[i].mark+'</span></td></tr>'
                        }
                        $("#algorithmManage .algorithm1-1").show().siblings().hide()
                        $("#algorithmManage .algorithm1-1 tbody").html(tbodyStr)
                        $("#algorithmManage .algorithm1-1 .algBeginTime ").html(enumerate[0].enabledTime)
                    }

                } else if (algorithmCat === 2) {
                    if (eisNo === '1-5') {
                        $(".stepwise1-5").show().siblings().hide()
                        $("#algorithmManage .algorithm1-5").show().siblings().hide()
                        $("#algorithmManage .algorithm1-5 .step").html(list[0].upperLimit)
                        $("#algorithmManage .algorithm1-5 .algBeginTime").html(list[0].enabledTime)
                    }

                } else if (algorithmCat === 4) {

                    tbodyStr = updateParamTbody(eisNo, list)
                    if (eisNo === '2-4' || eisNo === '2-6' || eisNo === '2-8' ) {
                        eisNo = '2-4'
                    }
                    $("#algorithmManage .algorithm" + eisNo).show().siblings().hide()
                    $("#algorithmManage .algorithm" + eisNo + " tbody").html(tbodyStr)
                    $("#algorithmManage .algorithm" + eisNo + " .algBeginTime ").html(list[0].enabledTime)
                    if (eisNo === '1-5') {
                        $(".section1-5").show().siblings().hide()
                    }
                }
            } else {
                $("#algorithmManage .algorithm1").show().siblings().hide()
                $("#algorithmManage .algorithm1 tbody").html('<tr><td colspan="2" class="text-center">暂无数据</td></tr>')
                layer.msg("系统错误")
            }
        }
    })
}

// creator: 张旭博，2022-09-29 11:41:16， 权重调整记录
function weightAdjustmentRecordBtn () {
    var category = $(".page_main").is(":visible")?2:1
    $(".page_weightAdjustmentRecord").data("category", category)
    jumpPage("weightAdjustmentRecord")
    $.ajax({
        url: `../eis/history/histories?category=${category}`,
        type: 'get',
        success: function (res) {
            var data = res
            if (data) {
                var str = ''
                if (data.length < 2) {
                    $(".page_weightAdjustmentRecord .tips").html("权重尚未经修改")
                    $(".page_weightAdjustmentRecord tbody").html("")
                } else {
                    for (var i = 0; i < data.length; i++) {
                        if (i === 0) {
                            str +=  '<tr data-version="'+data[i].versionNo+'">' +
                                '    <td>初始权重</td>' +
                                '    <td>创建时间：'+data[i].createDate+'</td>' +
                                '    <td><button class="link-blue" type="btn" name="seeRecord">查看</button></td>' +
                                '</tr>'
                        } else {
                            str +=  '<tr data-version="'+data[i].versionNo+'">' +
                                '    <td>第'+i+'次修改后</td>' +
                                '    <td>修改时间：'+data[i].updateDate+'</td>' +
                                '    <td><button class="link-blue" type="btn" name="seeRecord">查看</button></td>' +
                                '</tr>'
                        }

                    }
                    $(".page_weightAdjustmentRecord .tips").html("当前数据系第" + (data.length - 1) + "次修改后的结果")
                    $(".page_weightAdjustmentRecord tbody").html(str)
                }
            }
        }
    })
}

// creator: 张旭博，2022-10-11 02:52:25， 初始化自定义弹窗
function initCustomParam(no, isFake) {
    var eisIndex = $("#algorithmManage").data("eisIndex")
    if (no === '1-5') {
        var algorithmCat = $("#algorithmManage").data("algorithmCat")
        if (isFake) {
            // 此处isFake的作用是指1-5挂账指标2算法（更换算法同时自定义参数）时使用,此处并不会真正调用接口而是存在页面中，所以用假来命名
            if (algorithmCat === 4) {
                // 特指 1-5，当前是区间型，所以是要改为步进的自定义
                bounce_Fixed2.show($("#customParameters_step"))
                $("#customParameters_step").find("input").val("")
                $("#customParameters_step .sureBtn").unbind().on("click",function (){
                    var step = $("#customParameters_step .step").val()
                    if (step === '') {
                        layer.msg("请输入步长")
                        return false
                    }
                    step = Number(step)
                    if (step === 0 || step > 5) {
                        layer.msg("请输入1至5的自然数")
                        return false
                    }
                    $("#changeAlgorithm").data('custom', {
                        upperLimit: step
                    })
                    bounce_Fixed2.cancel()
                })
            }else {
                // 特指 1-5，当前是步进型，所以是要改为区间型的自定义
                updateCustomParam (no)
                bounce_Fixed2.show($("#customParameters"))

                $("#customParameters .sureBtn ").unbind().on("click",function (){
                    var data = []
                    var isNull = validateNull($("#customParameters .table_customParam"))
                    if (!isNull) {return false}
                    $("#customParameters .table_customParam tbody tr").each(function () {
                        var item = {
                            eisIndex: $("#algorithmManage").data("eisIndex"),
                            modifiable: 0,
                            lowerLimit: $(this).find("td").eq(0).find(".minNum").html() || 0,
                            upperLimit: $(this).find("td").eq(0).find(".inputNum").val() || 0,
                            mark: $(this).find("td").eq(1).find(".inputNum").val() || $(this).find("td").eq(1).html()
                        }
                        data.push(item)
                    })
                    var isData = validateData(data, no)
                    if (!isData) {
                        layer.msg("数据验证失败！请检查录入数据")
                        return false
                    }
                    $("#changeAlgorithm").data('custom', {
                        data: data
                    })
                    bounce_Fixed2.cancel()
                })
            }
        } else {
            // 1-5 算法管理 直接自定义参数
            if (algorithmCat === 2) {
                // 当前是步进型，直接自定义
                bounce_Fixed2.show($("#customParameters_step"))
                $("#customParameters_step").find("input").val("")
                $("#customParameters_step .sureBtn").unbind().on("click",function (){
                    var step = $("#customParameters_step .step").val()
                    if (step === '') {
                        layer.msg("请输入步长")
                        return false
                    }
                    step = Number(step)
                    if (step === 0 || step > 5) {
                        layer.msg("请输入1至5的自然数")
                        return false
                    }
                    var url = `../eis/changeAlgorithmCatToStep`
                    $.ajax({
                        url: url,
                        data: {
                            id: eisIndex,
                            upperLimit:step || 1
                        },
                        success: function (res) {
                            var code = res.code
                            if (code === 0) {
                                layer.msg("操作成功")
                                bounce_Fixed2.cancel()
                                getAecByAlgorithmCat()
                            } else {
                                layer.msg("操作失败")
                            }
                            console.log('change', res)
                        }
                    })
                })
            }else {
                // 当前是区间型，直接自定义
                updateCustomParam (no)
                bounce_Fixed2.show($("#customParameters"))
                $("#customParameters .sureBtn").unbind().on("click",function (){
                    var data = []
                    // 判断非空
                    var isNull = validateNull($("#customParameters .table_customParam"))
                    if (!isNull) {return false}
                    $("#customParameters .table_customParam tbody tr").each(function () {
                        var item = {
                            eisIndex: eisIndex,
                            modifiable: 0,
                            lowerLimit: $(this).find("td").eq(0).find(".minNum").html() || 0,
                            upperLimit: $(this).find("td").eq(0).find(".inputNum").val() || 0,
                            mark: $(this).find("td").eq(1).find(".inputNum").val() || $(this).find("td").eq(1).html()
                        }
                        data.push(item)
                    })
                    var isData = validateData(data, no)
                    if (!isData) {
                        layer.msg("数据验证失败！请检查录入数据")
                        return false
                    }
                    var url = `../eis/changeAlgorithmCatToSection?id=${eisIndex}`
                    $.ajax({
                        url: url,
                        data: JSON.stringify(data),
                        contentType: 'application/json',
                        success: function (res) {
                            var code = res.code
                            if (code === 0) {
                                layer.msg("操作成功")
                                bounce_Fixed2.cancel()
                                getAecByAlgorithmCat()
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
            }
        }

    } else {
        updateCustomParam (no)
        bounce_Fixed2.show($("#customParameters"))
        $("#customParameters .sureBtn").unbind().on("click",function (){
            var data = []
            var eisIndex = $("#algorithmManage").data("eisIndex")
            if (no !== '2-9') {
                // 正常自定义弹窗

                // 判断非空
                var isNull = validateNull($("#customParameters .table_customParam"))
                if (!isNull) {return false}
                $("#customParameters .table_customParam tbody tr").each(function () {
                    var markSelector = $(this).find("td").eq(1).find(".inputNum")
                    var item = {
                        eisIndex: eisIndex,
                        modifiable: 1,
                        lowerLimit: $(this).find("td").eq(0).find(".minNum").html() || 0,
                        upperLimit: $(this).find("td").eq(0).find(".inputNum").val() || 0,
                        mark: markSelector.length > 0 ?markSelector.val():$(this).find("td").eq(1).html()
                    }
                    data.push(item)
                })
                var isData = validateData(data, no)
                if (!isData) {
                    layer.msg("数据验证失败！请检查录入数据")
                    return false
                }

                console.log(eisIndex)
                console.log(data)
                $.ajax({
                    url: `../eis/changeAlgorithmCatToSection?id=${eisIndex}`,
                    data: JSON.stringify(data),
                    contentType: 'application/json',
                    success: function (res){
                        var state = res.code
                        if (state === 0) {
                            layer.msg("操作成功")
                            bounce_Fixed2.cancel()
                            getAecByAlgorithmCat()
                        } else {
                            layer.msg("操作失败")
                        }
                    }
                })
            } else {
                var isNull = validateNull($("#customParameters .table_customParam"))
                if (!isNull) {return false}
                $("#customParameters .table_customParam tbody tr").each(function () {
                    var item = {
                        module: '2-9',
                        eisIndex: eisIndex,
                        value: $(this).find("td").eq(0).html(),
                        mark: $(this).find("td").eq(1).find(".inputNum").val() || $(this).find("td").eq(1).html()
                    }
                    data.push(item)
                })
                var isData = validateData(data, no)
                if (!isData) {
                    layer.msg("数据验证失败！请检查录入数据")
                    return false
                }
                $.ajax({
                    url: `../eis/aec/editEnumerate?id=${eisIndex}`,
                    data: JSON.stringify(data),
                    contentType: 'application/json',
                    success: function (res){
                        var state = res.code
                        if (state === 0) {
                            layer.msg("操作成功")
                            bounce_Fixed2.cancel()
                            getAecByAlgorithmCat()
                        } else {
                            layer.msg("操作失败")
                        }
                    }
                })
            }

        })
    }
}

function updateCustomParam (no) {
    var thead = '', tbody = ''
    if (no === '2-9') {
        thead = '<tr>' +
            '    <td>实际情况</td>' +
            '    <td>本项指标实际得分</td>' +
            '</tr>'
    } else if (no === '2-14') {
        thead = '<tr>' +
            '    <td>采购周期的天数</td>' +
            '    <td>本项指标实际得分</td>' +
            '    <td><button class="link-blue" name="addOneRow">增加</button></td>' +
            '</tr>'
    } else if (no === '2-15') {
        thead = '<tr>' +
            '    <td>最低采购量的数值<br>（计量单位无法示出）</td>' +
            '    <td>本项指标实际得分</td>' +
            '    <td><button class="link-blue" name="addOneRow">增加</button></td>' +
            '</tr>'
    } else {
        thead = '<tr>' +
            '    <td>实际情况</td>' +
            '    <td>本项指标实际得分</td>' +
            '    <td><button class="link-blue" name="addOneRow">增加</button></td>' +
            '</tr>'
    }
    tbody = updateCustomParamTbody(no)
    $("#customParameters thead").html(thead)
    $("#customParameters tbody").html(tbody)
    $("#customParameters").data("addRow", $("#customParameters tbody tr").eq(1).html())
}

// creator: 张旭博，2023-01-31 04:55:27， 更新自定义参数表格
function updateCustomParamTbody (no) {
    var data = []
    switch (no) {
        case '1-5':
            data = [{
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: 100
            }]
            break
        case '2-4':
        case '2-6':
        case '2-8':
            data = [{
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: 100
            }]
            break
        case '2-14':
        case '2-15':
            data = [{
                lowerLimit: 0,
                upperLimit: 0,
                mark: 100
            }, {
                lowerLimit: 1,
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: ''
            }, {
                lowerLimit: '',
                upperLimit: '',
                mark: 0
            }]
            break
    }

    var tbody = ''
    for (var i = 0; i < data.length; i++) {
        if (i === 0) {
            switch (no) {
                case '1-5':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title">挂账天数低于</div> <input type="text" placeholder="请录入" class="inputNum link" value="'+data[i].upperLimit+'"> 天</td>' +
                        '   <td><input type="text" placeholder="请录入" class="inputNum" value="'+data[i].mark+'"></td>' +
                        '   <td></td>' +
                        '</tr>'
                    break;
                case '2-4':
                case '2-6':
                case '2-8':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title">跌幅不足 </div> <input type="text" placeholder="请录入" class="inputNum link" value="'+data[i].upperLimit+'"> %</td>' +
                        '   <td><input type="text" placeholder="请录入" class="inputNum" value="'+data[i].mark+'"></td>' +
                        '   <td></td>' +
                        '</tr>'
                    break;
                case '2-14':
                    tbody +=  '<tr>' +
                        '   <td><input type="text" placeholder="请录入" class="inputNum link" value="'+data[i].upperLimit+'" disabled> 天</td>' +
                        '   <td>100</td>' +
                        '   <td></td>' +
                        '</tr>'
                    break;
                case '2-15':
                    tbody +=  '<tr>' +
                        '   <td><input type="text" placeholder="请录入" class="inputNum link" value="'+data[i].upperLimit+'" disabled></td>' +
                        '   <td>100</td>' +
                        '   <td></td>' +
                        '</tr>'
                    break;
            }

        } else if (i === data.length-1) {
            switch (no) {
                case '1-5':
                    tbody +=  '<tr>' +
                        '    <td><div class="item_left_title">挂账天数多于</div><span class="minNum">'+data[i].lowerLimit+'</span> 天</td>' +
                        '    <td>'+data[i].mark+'</td>' +
                        '    <td></td>' +
                        '</tr>'
                    break;
                case '2-4':
                case '2-6':
                case '2-8':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title">跌幅达到</div><span class="minNum">'+data[i].lowerLimit+'</span>% 或更多</td>' +
                        '   <td>100</td>' +
                        '   <td></td>' +
                        '</tr>'
                    break;
                case '2-14':
                    tbody +=  '<tr>' +
                        '   <td><span class="minNum">'+data[i].lowerLimit+'</span>天以上</td>' +
                        '   <td>0</td>' +
                        '   <td></td>' +
                        '</tr>'
                    break;
                case '2-15':
                    tbody +=  '<tr>' +
                        '   <td><span class="minNum">'+data[i].lowerLimit+'</span>以上</td>' +
                        '   <td>0</td>' +
                        '   <td></td>' +
                        '</tr>'
                    break;
            }


        } else {
            switch (no) {
                case '1-5':
                    tbody +=    '<tr>' +
                                '   <td><div class="item_left_title">挂账天数为<span class="minNum">'+data[i].lowerLimit+'</span></div>天至<input type="text" placeholder="请录入" class="inputNum link" value="'+data[i].upperLimit+'"> 天之间</td>' +
                                '   <td><input type="text" placeholder="请录入" class="inputNum" value="'+data[i].mark+'"></td>' +
                                '   <td><button class="link-red" name="delRow">删除</button></td>'+
                                '</tr>'
                    break;
                case '2-4':
                case '2-6':
                case '2-8':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title">跌幅达到<span class="minNum">'+data[i].lowerLimit+'</span>% 不足</div> <input type="text" placeholder="请录入" class="inputNum link" value="'+data[i].upperLimit+'"> %</td>' +
                        '   <td><input type="text" placeholder="请录入" class="inputNum" value="'+data[i].mark+'"></td>' +
                        '   <td><button class="link-red" name="delRow">删除</button></td>'+
                        '</tr>'
                    break;
                case '2-14':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title"><span class="minNum">'+data[i].lowerLimit+'</span>天至</div> <input type="text" placeholder="请录入" class="inputNum link" value="'+data[i].upperLimit+'"> 天</td>' +
                        '   <td><input type="text" placeholder="请录入" class="inputNum" value="'+data[i].mark+'"></td>' +
                        '   <td><button class="link-red" name="delRow">删除</button></td>'+
                        '</tr>'
                    break;
                case '2-15':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title"><span class="minNum">'+data[i].lowerLimit+'</span>至</div> <input type="text" placeholder="请录入" class="inputNum link" value="'+data[i].upperLimit+'"></td>' +
                        '   <td><input type="text" placeholder="请录入" class="inputNum" value="'+data[i].mark+'"></td>' +
                        '   <td><button class="link-red" name="delRow">删除</button></td>'+
                        '</tr>'
                    break;
            }

        }
    }
    if (no === '2-9') {
        tbody = '<tr><td>价格最高的供应商</td><td>0</td></tr>' +
            '<tr><td>价格次高的供应商</td><td><input type="text" placeholder="请录入" class="inputNum"></td></tr>' +
            '<tr><td>中间价格的供应商</td><td><input type="text" placeholder="请录入" class="inputNum"></td></tr>' +
            '<tr><td>价格次低的供应商</td><td><input type="text" placeholder="请录入" class="inputNum"></td></tr>' +
            '<tr><td>价格最低的供应商</td><td>100</td></tr>'
    }
    return tbody
}

// creator: 张旭博，2023-01-31 04:55:54， 更新算法及管理弹窗表格
function updateParamTbody (no, data) {
    var tbody = ''
    for (var i = 0; i < data.length; i++) {
        if (i === 0) {
            switch (no) {
                case '1-5':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title">挂账天数低于</div><span class="minNum">'+data[i].upperLimit+'</span>天</td>' +
                        '   <td><span class="mark">'+data[i].mark+'</span></td>' +
                        '</tr>'
                    break;
                case '2-4':
                case '2-6':
                case '2-8':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title">跌幅不足</div><span class="minNum">'+data[i].upperLimit+'</span>%</td>' +
                        '   <td><span class="mark">'+data[i].mark+'</span></td>' +
                        '</tr>'
                    break;
                case '2-14':
                    tbody +=  '<tr>' +
                        '   <td><span class="minNum">0</span>天</td>' +
                        '   <td><span class="mark">100</span></td>' +
                        '</tr>'
                    break;
                case '2-15':
                    tbody +=  '<tr>' +
                        '   <td><span class="minNum">0</span></td>' +
                        '   <td><span class="mark">100</span></td>' +
                        '</tr>'
                    break;
            }

        } else if (i === data.length-1) {
            switch (no) {
                case '1-5':
                    tbody +=  '<tr>' +
                        '    <td><div class="item_left_title">挂账天数多于</div><span class="minNum">'+data[i].lowerLimit+'</span>天</td>' +
                        '    <td><span class="mark">'+data[i].mark+'</span></td>' +
                        '</tr>'
                    break;
                case '2-4':
                case '2-6':
                case '2-8':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title">跌幅达到</div><span class="minNum">'+data[i].lowerLimit+'</span>% 或更多</td>' +
                        '   <td><span class="mark">100</span></td>' +
                        '</tr>'
                    break;
                case '2-14':
                    tbody +=  '<tr>' +
                        '   <td><span class="minNum">'+data[i].lowerLimit+'</span>天以上</td>' +
                        '   <td><span class="mark">0</span></td>' +
                        '</tr>'
                    break;
                case '2-15':
                    tbody +=  '<tr>' +
                        '   <td><span class="minNum">'+data[i].lowerLimit+'</span>以上</td>' +
                        '   <td><span class="mark">0</span></td>' +
                        '</tr>'
                    break;
            }


        } else {
            switch (no) {
                case '1-5':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title">挂账天数为</div><span class="minNum">'+data[i].lowerLimit+'</span>天至<span class="maxNum">'+data[i].upperLimit+'</span>天之间</td>' +
                        '   <td><span class="mark">'+data[i].mark+'</span></td>' +
                        '</tr>'
                    break;
                case '2-4':
                case '2-6':
                case '2-8':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title">跌幅达到<span class="minNum">'+data[i].lowerLimit+'</span>% 不足</div><span class="maxNum">'+data[i].upperLimit+'</span>%' +
                        '   <td><span class="mark">'+data[i].mark+'</span></td>' +
                        '</tr>'
                    break;
                case '2-14':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title"><span class="minNum">'+data[i].lowerLimit+'</span>天 -</div><span class="maxNum">'+data[i].upperLimit+'</span>天</td>' +
                        '   <td><span class="mark">'+data[i].mark+'</span></td>' +
                        '</tr>'
                    break;
                case '2-15':
                    tbody +=  '<tr>' +
                        '   <td><div class="item_left_title"><span class="minNum">'+data[i].lowerLimit+'</span>-</div><span class="maxNum">'+data[i].upperLimit+'</span></td>' +
                        '   <td><span class="mark">'+data[i].mark+'</span></td>' +
                        '</tr>'
                    break;
            }

        }
    }
    return tbody
}

// creator: 张旭博，2022-09-30 03:32:49， 自定义算法
function customParametersBtn() {
    bounce_Fixed2.show($("#customParameters"))
}

// creator: 张旭博，2023-02-24 15:39:49，返回上一步
function back() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr.pop()
    $("#home").data("pathArr", pathArr)
    isShowNav()
    var lastEle = pathArr[pathArr.length - 1]
    $(".page[page='"+lastEle+"']").show().siblings(".page").hide()
    if (lastEle === 'main') {
        $(".backBtn").hide()
    }
}

// creator: 张旭博，2023-02-24 15:40:51，回到主页
function backToMain() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr = [pathArr[0]]
    $("#home").data("pathArr", pathArr)
    isShowNav()
    $(".page[page='"+pathArr[0]+"']").show().siblings(".page").hide()
}

// creator: 张旭博，2023-02-09 04:56:46， 跳转及退回
function jumpPage(page, callback) {
    var arr = $("#home").data("pathArr") || []
    arr.push(page)
    $("#home").data("pathArr", arr)
    isShowNav()
    $(".page[page='"+page+"']").show().siblings(".page").hide()
    if (page !== 'main') {
        $(".backBtn").show()
    }
    if (callback) { callback()}
}

// creator: 张旭博，2023-02-24 15:40:37，是否禁用导航
function isShowNav() {
    var pathArr = $("#home").data("pathArr")
    if (pathArr.length > 1) {
        $(".ty-page-header").removeAttr("disabled")
    } else {
        $(".ty-page-header").attr("disabled", "disabled")
    }
}

// creator: 张旭博，2023-03-05 10:35:26， 数据规则验证
function validateData(data, no) {
    var validate = true
    for (var i=0; i<data.length; i++) {
        var item = data[i]
        var min = item.lowerLimit
        var max = item.upperLimit
        if (no === '2-14' || no === '2-15' ){
            if (i > 0) {
                // 横向比较，需要是递进的数字
                if (i < data.length - 1) {
                    if ((max-min) < 1) {
                        validate = false
                    }
                }
                // 竖向比较，逆序
                if ((data[i].mark - data[i-1].mark) > -1) {
                    validate = false
                }
            }
        } else if (no === '2-9') {
            if (i > 0) {
                if ((data[i].mark - data[i-1].mark) < 1) {
                    validate = false
                }
            }
        } else {
            if (i <data.length - 1) {
                if ((max-min) < 1) {
                    validate = false
                }
            }
            if (i > 0) {
                if ((data[i].mark - data[i-1].mark) < 1) {
                    validate = false
                }
            }
        }
    }
    return validate
}

// creator: 张旭博，2023-03-07 15:35:42，非空验证
function validateNull(selector) {
    var state = 0
    selector.find("input").each(function (){
        if ($(this).val() === '') {
            state++
        }
    })
    if (state > 0) {
        layer.msg("您有数据未输入！")
    }
    return state === 0
}