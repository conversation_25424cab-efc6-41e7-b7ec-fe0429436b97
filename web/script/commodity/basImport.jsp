<%--Creator : 孟闯闯    2017/3/6 15:32 实现客户导入的异步实现--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>

<style type="text/css">
    /*导入页面*/
    .mould{
        text-align: left;
        margin: 10px 0 5% 30%;
        display: block;
    }
    .btnbd{
        width:88px;
        height:36px;
    }
    .operateIn{
        margin: -3% 0 0 17%;
    }
    .btn{ display:inline-block; border:1px solid #ccc;  line-height:40px; text-align: center; cursor:pointer;
        -moz-border-radius:4px;
        -webkit-border-radius:4px;
        border-radius:4px;
    }
    .blue{ background: #00a2d4; color: #fff;   }

</style>

<script src="${pageContext.request.contextPath }/assets/global/plugins/jquery.min.js" type="text/javascript"></script>
<div class="bonceCon">
    <form action="${pageContext.request.contextPath }/export/importProduct.do" id="cusImport" method="post" enctype="multipart/form-data">
        <div class="sale_c">
            <div style="margin-left:20%;text-align:left;"><span>第一步：请点击下面的链接下载模板，并填写客户信息。</span></div>
            <div class="mould"><div><a href="${pageContext.request.contextPath}/upload/template/spmb.xls" id="mould1">下载模板</a></div></div>
            <div style="margin-left:20%;text-align:left;"><span>第二步：导入完成的Excel文件。</span></div>
            <div class="mould"><input style="border:0;" type="file" id="appendix" name="file" ></div>
        </div>
    </form>
</div>
<div class="operateIn">
    <span class="btn btnbd blue" onclick="$('#cusImport').submit(); parent.reloadPage(); ">导入</span>
    <span class="btn btnbd" onclick="parent.bounce.cancel()">取消</span>
    <div class="sale_c" style="margin:4% 0 0 0;line-height:22px;">
        <div style="text-align:left;color:red;"><span>提示：</span></div>
        <div style="text-align:left;"><span>请在下载模板前，先维护好当前数据，并按照相应的格式在Excel表中填写相应信息。</span></div>
        <div style="text-align:left;color:red;margin-top:2%"><span>注意：只有严格按照模板的格式要求填写，表格信息才能正常导入：</span></div>
        <div style="text-align:left;">
            <span>1.日期格式为“20xx-xx-xx”，其它格式或空值无法导入。</span></br>
        </div>
    </div>
</div>