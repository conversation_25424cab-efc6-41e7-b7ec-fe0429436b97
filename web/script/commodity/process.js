
$(function () {
	bounce.cancel();
	App.init();
//	获取信息列表
	getProcess( "" ,1 , 10);


})
//获取商品工序信息列表
function getProcess(innerSn ,currPage , pageSize ) {
	// alert(innerSn);
	// innerSn  内部图号
	// currPage  当前页
	// pageSize   每页条数
	$.ajax({
		url:"getProcessList.do",
		data:{"innerSn":innerSn , "pageNumber": currPage , "quantum":pageSize },
		type:"post",
		dataType:"Json",
		beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
		success:function (data) {
			var cur = data["pageNumber"];
			var total = data["totalPage"];
			var list = data["pdProcessListList"];
			$("#ye_goodsprocess").html("");
			var jsonStr = JSON.stringify({"innerSn":innerSn ,  }) ;
			setPage( $("#ye_goodsprocess") , cur , total , "goodsprocess" , jsonStr );
			$("#process_tbl").html("");
			if(list !=undefined && list.length > 0 ){
				for(var i=0 ; i<list.length ; i++ ){
					var processid = 0, name="" , cavityPerModel="", unitConsumption="", materiaUtilization="", lossQuato="", rejectionRateQuato="", memo="" ;
					if(list[i]["TPdProcessPdBaseViaProduct"][0] != undefined ){
						name=list[i]["TPdProcessPdBaseViaProduct"][0]["name"]  ;
						cavityPerModel=list[i]["TPdProcessPdBaseViaProduct"][0]["cavityPerModel"] ;
						unitConsumption=list[i]["TPdProcessPdBaseViaProduct"][0]["unitConsumption"] ;
						materiaUtilization= list[i]["TPdProcessPdBaseViaProduct"][0]["materiaUtilization"]  ;
						lossQuato= list[i]["TPdProcessPdBaseViaProduct"][0]["lossQuato"]  ;
						rejectionRateQuato= list[i]["TPdProcessPdBaseViaProduct"][0]["rejectionRateQuato"]  ;
						memo= list[i]["TPdProcessPdBaseViaProduct"][0]["memo"]  ;
						processid= list[i]["TPdProcessPdBaseViaProduct"][0]["id"]  ;
					}
					var str = "<tr>" +
						"<td>"+ list[i]["innerSn"] + "</td>" +     // 内部图号
						"<td>"+ list[i]["name"] + "</td>" +          // 内部名称
						"<td>"+ chargeComposition( list[i]["composition"] ) + "</td>" + 		    // 构成
						"<td>"+ name + "</td>" + 			// 工序名称
						"<td>"+ chargeNetWeight(list[i]["netWeight"]) + "</td>" + 			// 产品重量
						"<td>"+ cavityPerModel + "</td>" +  			// 每模腔数
						"<td>"+ unitConsumption + "</td>" + 			// 单耗
						"<td>"+ materiaUtilization + "</td>" + 			// 材料利用率
						"<td>"+ lossQuato + "</td>" + 			// 损耗定额
						"<td>"+ rejectionRateQuato + "</td>" + 			// 废品率定额
						"<td>"+ memo + "</td>" + 			// 备注
						"<td>" +
						"<span class='ty-color-blue' onclick='BJBtnAccount( $(this) )'>编辑</span>" +
						"<div class='hd'>" +
						"<span class='productID'>"+ list[i]["id"]  +"</span>" +
						"<span class='processID'>"+processid +"</span>" +
						"</div>" +
						"</td>" +
						"</tr>";

					$("#process_tbl").append( str );

				}
			}
		},
		error:function(msg ){  // 失败以后的操作
			console.log("连接错误，请稍后重试！");
		} ,
		complete:function(){ chargeClose(1) ;  }
	})

}
function chargeNetWeight( val ){
	if( Number(val) > 0 ){
		return val ;
	}
	return "--"
}
//温馨提示的确定
function processcancel() {
	bounce.cancel($("#mtTip"));
	// $("#mtTip").hide().siblings().show().parent().hide();
}

// 工序信息
// 编辑查看
var BJTrobj = null ;
function BJBtnAccount(obj){
    if (chargeRole("超管")) { // 0 表示超管
		bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
	}
	$("#BJAccount").show().parent().show();
	$(".pro_hide").hide();
	$("#bounce_OK").hide();
	$("#bounce_BJ").show();
	$("#bounce_over").show();
	$("#bounce_over1").hide();
	var tr_obj = obj.parent().parent();
	BJTrobj = tr_obj;

	var productID = obj.siblings(".hd").children(".productID").html();
	var processID = obj.siblings(".hd").children(".processID").html();
	if ( productID == undefined || productID == "" ){
		bounce.show($("#mtTip"));
		// $("#mtTip").show().siblings().hide().parent().show();
		$("#mt_tip_ms").html("系统错误，刷新重试！");
		return false;
	}

	$.ajax({
		url: "getPartProcess.do",
		data: {"id": processID ,"productID": productID  },
		type: "post",
		dataType: "Json",
		beforeSend:function(){ loading.open() ;  } ,
		success: function (data) {
			var pdProcessDetail = data["pdProcessDetail"];
			var pdBases = data["pdBases"];
			var innerSn = pdBases["innerSn"] ;
			var name = pdBases["name"] ;
			var composition = pdBases["composition"] ;
			var netWeight = pdBases["netWeight"];
			var proname="" , procount = "" , take = "" , clmake = "" , sunhao = "" , rubbish = "" , remark = "" , arts = "" , work = "" , pack = "" ;
			if(pdProcessDetail.length > 0){
				proname = pdProcessDetail[0]["name"];
				procount = pdProcessDetail[0]["cavityPerModel"];
				take = pdProcessDetail[0]["unitConsumption"];
				clmake = pdProcessDetail[0]["materiaUtilization"];
				sunhao = pdProcessDetail[0]["lossQuato"];
				rubbish = pdProcessDetail[0]["rejectionRateQuato"] ;
				remark = pdProcessDetail[0]["memo"];
				arts = pdProcessDetail[0]["craftInstructor"];
				work = pdProcessDetail[0]["processInstructor"];
				pack = pdProcessDetail[0]["packagingInstructor"];
			}

			$("#sh_proinnertu").html(innerSn);  // 内部图号
			$("#sh_proinnername").html(name); // 内部名称
			$("#sh_pro").html(chargeComposition(composition) ); // 构成
			$("#sh_proname").html(proname);	//  工序名称
			$("#sh_weight").html(netWeight);	// 产品重量
			$("#sh_procount").html(procount);  //  每腔模数
			$("#sh_take").html(take); 		//  单耗
			$("#sh_clmake").html(clmake);		// 材料利用率
			$("#sh_sunhao").html(sunhao);		// 损耗定额
			$("#sh_rubbish").html(rubbish);	//	废品率定额
			$("#sh_remark").html(remark);	// 备注
			$("#sh_arts").html(arts);	//  工艺参数指导书编号
			$("#sh_work").html(work);///  加工作业指导书编号
			$("#sh_pack").html(pack);///  包装指导书编号
		},
		error: function (msg) {  // 失败以后的操作
			// console.log("连接错误，请稍后重试！");
			bounce.show($("#mtTip"));
			// $("#mtTip").show().siblings().hide().parent().show();
			$("#mt_tip_ms").html("连接错误，请稍后重试！");
			return false;
		} ,
		complete:function(){ loading.close() ;  }
	})

}
function chargeComposition( num ){
	if( num == "1" || num == 1 ){
		return "购买" ;
	}else if( num == "2" || num == 2 ){
		return "制造" ;
	}else if( num == "3" || num == 3 ){
		return "装配" ;
	}else{
		return "" ;
	}
}
// 编辑按钮
function bounce_BJ(){
	$(".pro_hide").show().siblings().hide().parent().show();
	$("#bounce_OK").show();
	$("#bounce_BJ").hide();
	$("#bounce_over").hide();
	$("#bounce_over1").show();
	var proinnertu = $("#sh_proinnertu").html();
	var proinnername = $("#sh_proinnername").html();
	var pro = $("#sh_pro").html();
	var proname = $("#sh_proname").html();
	var proweight = $("#sh_weight").html();
	var procount = $("#sh_procount").html();
	var take = $("#sh_take").html();
	var clmake = $("#sh_clmake").html();
	var sunhao = $("#sh_sunhao").html();
	var rubbish = $("#sh_rubbish").html();
	var remark = $("#sh_remark").html();
	var arts = $("#sh_arts").html();
	var work = $("#sh_work").html();
	var pack = $("#sh_pack").html();

	$("#pro_innertu").val(proinnertu);
	$("#pro_innername").val(proinnername);
	$("#pro_pro").val(pro);
	$("#pro_proname").val(proname);
	$("#pro_weight").val(proweight);
	$("#pro_count").val(procount);
	$("#pro_take").val(take);
	$("#pro_clmake").val(clmake);
	$("#pro_sunhao").val(sunhao);
	$("#pro_rubbish").val(rubbish);
	$("#pro_remark").val(remark);
	$("#pro_arts").val(arts);
	$("#pro_work").val(work);
	$("#pro_pack").val(pack);

}
// 保存按钮
function bounce_OK(){
	$(".pro_hide").hide().prev().show();
	$("#bounce_OK").hide();
	$("#bounce_BJ").show();
	$("#bounce_over").show();
	$("#bounce_over1").hide();
	var id = BJTrobj.children(":eq(11)").children(".hd").children(".processID").html();  // 工序id
	var pdBaseId = BJTrobj.children(":eq(11)").children(".hd").children(".productID").html();  // 商品id
	var name = $("#pro_proname").val();
	var cavityPerModel  = $("#pro_count").val(); // 每腔模数
	var unitConsuption  = $("#pro_take").val(); // 单耗
	var materiaUtilization  = $("#pro_clmake").val();  // 材料利用率
	var lossQuato  = $("#pro_sunhao").val();  // 损耗定额
	var rejectionRateQuato  = $("#pro_rubbish").val();  // 废品率定额
	var craftInstructor  = $("#pro_arts").val();  // 工艺参数指导书编号
	var processInstructor  = $("#pro_work").val();  // 加工作业指导书编号
	var packagingInstructor  = $("#pro_pack").val();  // 包装指导书编号
	var memo  = $("#pro_remark").val();  // 备注

	$.ajax({
		url: "updatePartProcess.do",
		data: {
			"id": id  ,
			"pdBaseId": pdBaseId  ,
			"name": name  ,
			"memo": memo  ,
			"cavityPerModel": cavityPerModel  ,
			"unitConsumption": unitConsuption  ,
			"materiaUtilization": materiaUtilization  ,
			"lossQuato": lossQuato  ,
			"rejectionRateQuato": rejectionRateQuato  ,
			"craftInstructor": craftInstructor  ,
			"processInstructor": processInstructor  ,
			"packagingInstructor": packagingInstructor
		},
		type: "post",
		dataType: "Json",
		beforeSend:function(){ loading.open() ;  } ,
		success: function (data) {
			var status =data["status"];
			if(Number(status) == 1){
				var Process = data["Process"];
				//获取后台数据
				var _name = Process["name"];
				var _cavityPerModel = Process["cavityPerModel"];
				var _unitConsuption  = Process["unitConsumption"]; // 单耗
				var _materiaUtilization  = Process["materiaUtilization"];  // 材料利用率
				var _lossQuato  = Process["lossQuato"];  // 损耗定额
				var _rejectionRateQuato  = Process["rejectionRateQuato"];  // 废品率定额
				var _craftInstructor  = Process["craftInstructor"];  // 工艺参数指导书编号
				var _processInstructor  = Process["processInstructor"];  // 加工作业指导书编号
				var _packagingInstructor  = Process["packagingInstructor"];  // 包装指导书编号
				var _proweight = $("#pro_weight").val();
				var _proinnertu = $("#pro_innertu").val();
				var _proinnername = $("#pro_innername").val();
				var _pro = $("#pro_pro").val();
				var _remark = Process["memo"];

				$("#sh_proinnertu").html(_proinnertu);
				$("#sh_proinnername").html(_proinnername);
				$("#sh_pro").html(_pro);
				$("#sh_proname").html(_name);
				$("#sh_weight").html(_proweight);
				$("#sh_procount").html(_cavityPerModel);
				$("#sh_take").html(_unitConsuption);
				$("#sh_clmake").html(_materiaUtilization);
				$("#sh_sunhao").html(_lossQuato);
				$("#sh_rubbish").html(_rejectionRateQuato);
				$("#sh_remark").html(_remark);
				$("#sh_arts").html(_craftInstructor);
				$("#sh_work").html(_processInstructor);
				$("#sh_pack").html(_packagingInstructor);
			}
			else{
				alert("修改失败！");
			}
		},
		error: function (msg) {  // 失败以后的操作
			// console.log("连接错误，请稍后重试！");
			bounce.show($("#mtTip"));
			// $("#mtTip").show().siblings().hide().parent().show();
			$("#mt_tip_ms").html("连接错误，请稍后重试！");
			return false;
		} ,
		complete:function(){ loading.close() ;  }
	})



}

function bounce_over(){
	$(".bounce").hide().children().hide();
	var proinnertu = $("#sh_proinnertu").html();
	var proinnername = $("#sh_proinnername").html();
	var pro = $("#sh_pro").html();
	var proname = $("#sh_proname").html();
	var proweight = $("#sh_weight").html();
	var procount = $("#sh_procount").html();
	var take = $("#sh_take").html();
	var clmake = $("#sh_clmake").html();
	var sunhao = $("#sh_sunhao").html();
	var rubbish = $("#sh_rubbish").html();
	var remark = $("#sh_remark").html();
	var arts = $("#sh_arts").html();
	var work = $("#sh_work").html();
	var pack = $("#sh_pack").html();

	if (BJTrobj != null ) {
		if(proweight==0){ proweight = "--" ;  }
		BJTrobj.children(":eq(0)").html(proinnertu);
		BJTrobj.children(":eq(1)").html(proinnername);
		BJTrobj.children(":eq(2)").html(pro);
		BJTrobj.children(":eq(3)").html(proname);
		BJTrobj.children(":eq(4)").html(proweight);
		BJTrobj.children(":eq(5)").html(procount);
		BJTrobj.children(":eq(6)").html(take);
		BJTrobj.children(":eq(7)").html(clmake);
		BJTrobj.children(":eq(8)").html(sunhao);
		BJTrobj.children(":eq(9)").html(rubbish);
		BJTrobj.children(":eq(10)").html(remark);

		BJTrobj = null ;
	}

}

$(".bounce_close").click(function(){
	$(".bounce").hide().children().hide();
	var proinnertu = $("#sh_proinnertu").html();
	var proinnername = $("#sh_proinnername").html();
	var pro = $("#sh_pro").html();
	var proname = $("#sh_proname").html();
	var proweight = $("#sh_weight").html();
	var procount = $("#sh_procount").html();
	var take = $("#sh_take").html();
	var clmake = $("#sh_clmake").html();
	var sunhao = $("#sh_sunhao").html();
	var rubbish = $("#sh_rubbish").html();
	var remark = $("#sh_remark").html();
	var arts = $("#sh_arts").html();
	var work = $("#sh_work").html();
	var pack = $("#sh_pack").html();

	if (BJTrobj != null ) {
		BJTrobj.children(":eq(0)").html(proinnertu);
		BJTrobj.children(":eq(1)").html(proinnername);
		BJTrobj.children(":eq(2)").html(pro);
		BJTrobj.children(":eq(3)").html(proname);
		BJTrobj.children(":eq(4)").html(proweight);
		BJTrobj.children(":eq(5)").html(procount);
		BJTrobj.children(":eq(6)").html(take);
		BJTrobj.children(":eq(7)").html(clmake);
		BJTrobj.children(":eq(8)").html(sunhao);
		BJTrobj.children(":eq(9)").html(rubbish);
		BJTrobj.children(":eq(10)").html(remark);

		BJTrobj = null ;
	}
})

function bounce_over1(){
	$(".pro_hide").hide().prev().show();
	$("#bounce_OK").hide();
	$("#bounce_BJ").show();
	$("#bounce_over").show();
	$("#bounce_over1").hide();


}
//查询
function searchProcess() {
	var inner = $("#search_innerSn").val();
	getProcess(inner,1,10);

}
































