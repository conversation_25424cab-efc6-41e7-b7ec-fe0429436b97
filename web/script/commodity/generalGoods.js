$(function() {
    getCommodityList(1,20,"","全部","");
});
// creator: 李玉婷，2020-03-18 10:17:06，获取通用型商品列表
function getCommodityList(currPage,pageSize,category,categoryName,keyword){
    $("#ye").html("");
    $(".indexInput").show();
    $(".between").height(0);
    $("#materialList").html("");
    $("#suspendCommodyNum").html("0");
    $("#firstLevelAmount").html("0");
    $("#kindsTree li").remove();
    $(".inSales").show();
    $(".inSuspend").hide();
    $(".mainCon1").show().siblings().hide();
    if (categoryName == "" || categoryName == "全部"){
        $(".suspendBtn").show();
        $(".left-bottom").hide();
        $("#firstLevelName").html("全部");
    } else{
        $(".suspendBtn").hide();
        $(".left-bottom").show();
        $("#firstLevelName").html(categoryName).data("categoryid",category);
    }
    $.ajax({
        url: "../product/getTYProductListByCategory.do",
        data: {
            "pageSize":pageSize,
            "currPage":currPage,
            "category": category,
            "param": keyword
        },
        success: function (data) {
            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            var count = 0;
            var jsonStr = {
                "category": category,
                "categoryName": categoryName,
                "param": keyword
            } ;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#ye") , curr ,  totalPage , "commodity", jsonStr );
            var list = data.data;
            var category = data.mtCategories;
            if(categoryName == '全部' || categoryName == ''){
                $("#suspendCommodyNum").html(data.suspendProductNum);
            }
            if (category && category.length > 0){
                var menu = '';
                for(var m=0;m<category.length;m++){
                    count += category[m].num;
                    if (categoryName == '全部' && category[m].name == '待分类') {
                        $("#firstLevelName").data("categoryid",category[m].id);
                    }
                    if (categoryName != '待分类'){
                        menu +=
                            '<li class="faceul1">' +
                            '    <a>' +
                            '        <span onclick="kindBtn($(this))" data-source="1">'+ category[m].name +'（'+ category[m].num +'种）</span>' +
                            '        <div class="hd">' +
                            '            <span class="kindId">'+ JSON.stringify(category[m])+'</span>' +
                            '        </div>' +
                            '    </a>' +
                            '</li>';
                    }
                }
                $("#kindsTree").html(menu);
            }
            $("#firstLevelAmount").html(count);
            if (list && list.length > 0){
                let html = ``;
                for(var t=0;t<list.length;t++){
                    var info = JSON.stringify(list[t]);
                    html+= `
                    <tr>
                      <td>${handleNull(list[t].outerSn)}</td>
                            <td>${handleNull(list[t].outerName)}</td>
                            <td>${handleNull(list[t].model)}</td>
                            <td>${ handleNull(list[t].specifications)}</td>
                            <td>${ handleNull(list[t].unit)}</td>
                            <td>${ parseFloat(Number(list[t].minimumStock || "0").toFixed(4))}</td>
                            <td>${ parseFloat(Number(list[t].currentStock || "0").toFixed(4))}</td>
                            <td class="createInfo">${ list[t].createName}&nbsp;${ new Date(list[t].createDate).format('yyyy/MM/dd hh:mm:ss') }</td>
                            <td>
                                <span class="ty-color-blue tb-btn-sm" onclick="seeCommodityDetails($(this),1, false, true)">查看</span>
                                <span class="ty-color-blue tb-btn-sm" onclick="curOrders($(this))">当前订购信息</span>
                                <span class="ty-color-blue tb-btn-sm" onclick="suspendSale($(this),0)">暂停销售</span>
                                <span class="ty-color-red tb-btn-sm" onclick="deleteCommodity($(this),0)">删除</span>
                                <span class="hd">${info}</span>
                            </td>
                        </tr>`;
                }
                $("#materialList").html(html);
            }
            var conHeight = $(".bigContainer").height() ;
            conHeight++;
            $(".between").height(conHeight);
        }
    })
}
// creator: 李玉婷，2020-03-17 16:05:56，商品录入确定
function addCommoditySure() {
    var reqTrue = 0;
    let addRate = $("#addInvoiceSpecial").val()
    let unitPriceNotax = $("#unitPriceNotax_add").val()
    let unitPrice_add = $("#unitPrice_add").val()
    if(addRate || unitPriceNotax || unitPrice_add){ // 有一个填了
        if(addRate){
            if(unitPriceNotax == "" || unitPrice_add == ''){ reqTrue++; layer.msg('请录入含税单价或者不含税单价') }
        }
        if(unitPriceNotax){
            if(addRate == "" || unitPrice_add == ''){ reqTrue++; layer.msg('请录入税率') }
        }
        if(unitPrice_add){
            if(addRate == "" || unitPriceNotax == ''){ reqTrue++; layer.msg('请录入税率') }
        }
        if (reqTrue > 0) {
            return false;
        }
    }

    $("#addCommodity [require]:visible").each(function () {
        if ($(this).val() == "") reqTrue++;
    });

    if (reqTrue > 0) {
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }
    else{
        var isSaled = $("#addCommodity").data('isSaled');
        var categoryid = $("#firstLevelName").data("categoryid");
        var goodsData = {
            'isSaled': isSaled,
            'category': categoryid
        };
        $("#addCommodity input:visible").each(function () {
            var name = $(this).attr('name');if (name) {
                if(name == 'unitPriceNotax' || name == 'unitPrice') {
                    goodsData[name] = Number($(this).val());
                } else {
                    goodsData[name] = $(this).val();
                }
            }
        });
        $("#addCommodity select:visible").each(function () {
            var name = $(this).attr('name');
            goodsData[name] = $(this).val();
        });
        goodsData['unit'] = $("#add_unitName").val();
        //图片 视频
        var media = [];
        $("#addCommodity .imgsthumb").each(function () {
            var cur = $(this).find(".filePic");
            var item = {
                'uplaodPath': cur.data('path'),
                'title':cur.data('ttl'),
                'type': cur.data('type')
            }
            media.push(item);
        });
        goodsData.commodityMediaList = JSON.stringify(media);

        goodsData.exclusiveTime = $("#firstTimeCC").data('type')
        goodsData.exclusiveMonth = $("#firMonth").val() || (new Date().format('yyyy'))

        $.ajax({
            url: "../product/addTYProduct.do",
            data: goodsData,
            success: function (data) {
                if (data.code == 200){
                    bounce.cancel();
                    getCommodityList(1,20,"","全部","");
                } else {
                    var msg = data.msg;
                    $("#unfilledTip_ms").html(msg);
                    bounce_Fixed2.show($("#unfilledTip"));
                }
            }
        })
    }
}
// creator: 李玉婷，2020-03-31 11:21:01，获取暂停销售的商品数据
function getSuspendList(currPage,pageSize,keyword) {
    $("#ye").html('');
    $("#firstLevelAmount").html('0');
    $("#suspendTYList tbody").html('');
    $.ajax({
        "url": "../product/suspendTYProductList.do",
        "data": {
            "pageSize":pageSize,
            "currPage":currPage,
            "param": keyword
        },
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = {
                "param": keyword,
            } ;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#ye") , curr ,  totalPage , "inSuspendTY", jsonStr );
            $("#firstLevelAmount").html(res.suspendProductNum);

            var spList = res.data;
            if (spList && spList.length > 0){
                var html = '';
                for(var t=0;t<spList.length;t++){
                    var info = JSON.stringify(spList[t]);
                    html+=
                        ' <tr>' +
                        '     <td>'+ spList[t].outerSn +'</td>' +
                        '     <td>'+ spList[t].outerName +'</td>' +
                        '     <td>'+ spList[t].model +'</td>' +
                        '     <td>'+ spList[t].specifications +'</td>' +
                        '     <td>'+ spList[t].unit +'</td>' +
                        '     <td>'+ parseFloat(Number(spList[t].minimumStock || "0").toFixed(4)) +'</td>' +
                        '     <td>'+ parseFloat(Number(spList[t].currentStock || "0").toFixed(4)) +'</td>' +
                        '     <td class="createInfo">'+ spList[t].updateName + ' &nbsp; ' + new Date(spList[t].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '     <td>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="seeCommodityDetails($(this),1,true,false)">查看</span>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="curOrders($(this))">当前订购信息</span>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="suspendSale($(this),1)">恢复销售</span>' +
                        '         <span class="ty-color-red tb-btn-sm" onclick="deleteCommodity($(this),1)">删除</span>' +
                        '         <span class="hd">'+ info +'</span>' +
                        '     </td>' +
                        ' </tr>';
                }
                $("#suspendTYList tbody").html(html);
            }
            var conHeight = $(".bigContainer").height() ;
            conHeight++;
            $(".between").height(conHeight);
        }
    });
}

// updator: hxz，2021-10-27 21:24:16，输出数据
function getImportLastList(data) {
    var importList = data["pdProductImportList"];
    var source = $("#importEnteryType").data("source");
    var caseVal = Number(data.importOption);
    var hasRate = Number(data.taxInclusive);
    $(".mainCon3 .importCon2 .initAll").html(data.importSum);
    $(".mainCon3 .importCon2 .inabledSum").html(Number(data.tureImportSum));
    let str = ``, trObj = ``;
    var buttonState = data["buttonState"];
    if (importList && importList.length > 0) {
        if(caseVal == 1){
            let cat1 = false,cat2 = false;
            $.ajax({
                "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
                success: function (res) {
                    var list = res["financeInvoiceSettings"];
                    let rateList_ = []
                    let invoiceCatList_ = []
                    if (list && list.length > 0) {
                        for (var i = 0; i < list.length; i++) {
                            if (list[i]["category"] == '1'){
                                var rate = list[i]["enableTaxTate"];
                                var rates = rate.split(",");
                                cat1 = true;
                                if (rates && rates.length >0){
                                    for (var r = 0; r < rates.length; r++) {
                                        rateList_.push(rates[r])
                                    }
                                }
                            } else if (list[i]["category"] == '2' || list[i]["category"] == '3'){
                                cat2 = true;
                            }
                        }
                        if (cat1) {
                            invoiceCatList_.push({ 'val':1, "name":"开具增值税专用发票" })
                        }
                        if (cat2) {
                            invoiceCatList_.push({ 'val':2, "name":"开具其他发票" })
                        }
                    }
                    invoiceCatList_.push({ 'val':4, "name":"不开发票" })

                    for (var i = 0; i < importList.length; i++) {
                        str +=
                            `<tr>
                                <td>${importList[i].code} &nbsp;&nbsp; ${handleNull(importList[i].name)} ${ handleNull(importList[i].specifications)} &nbsp;&nbsp; ${handleNull(importList[i].model) } &nbsp;&nbsp; ${ handleNull(importList[i].unit)}</td>
                                <!--<td><select name="invoiceType" onchange="changeInvoiceCat($(this))"></select></td>-->
                                <td class="excInvoice po-invoice1"><select class="sign2" name="taxRate" onchange="changeInvoiceRate($(this))" disabled="disabled">${ setinitRate(rateList_,importList[i].taxRate ) }</select></td>
                                <td class="excInvoice po-invoice1"><input class="sign2" value="${handleNull(importList[i].unitPrice)}" onkeyup="changeExcInvoice($(this))" onblur="importPriceChange($(this))" name="unitPrice" disabled="disabled"/></td>
                                <td class="excInvoice po-invoice1"><input class="sign2" value="${handleNull(importList[i].unitPriceNotax)}" onkeyup="changeExcInvoice($(this))" onblur="importPriceChange($(this))" name="unitPriceNotax" disabled="disabled"/></td>
                                <td class="po-invoice2"><input class="sign2" value="${handleNull(importList[i].unitPriceInvoice)}" name="unitPriceInvoice" onblur="importPriceChange($(this))" disabled="disabled"/></td>
                                <td><input class="sign2" value="${handleNull(importList[i].unitPriceNoinvoice)}" name="unitPriceNoinvoice" onblur="importPriceChange($(this))" disabled="disabled"/></td>
                                <td><input class="sign2" value="${handleNull(importList[i].unitPriceReference)}" name="unitPriceReference" onblur="importPriceChange($(this))" disabled="disabled"/></td>
                                <td>
                                    <span class="ty-color-blue btn" data-name="update">修改</span>
                                    <span class="ty-color-red btn" data-name="del">删除</span>
                                    <span class="hd">${JSON.stringify(importList[i])}</span>
                                </td>
                             </tr>`;
                    }
                    $(".mainCon3 .importCon2 .normal2").show();
                    $(".mainCon3 .importCon2 .normal2 tbody").html(str);
                    $(".mainCon3 .importCon2 .normal1").hide()
                }
            });

        }else{
            for (var i = 0; i < importList.length; i++) {
                str +=
                    `<tr>
                    <td>${importList[i].code} &nbsp;&nbsp; ${handleNull(importList[i].name)} ${ handleNull(importList[i].specifications)} &nbsp;&nbsp; ${handleNull(importList[i].model) } &nbsp;&nbsp; ${ handleNull(importList[i].unit)}</td>
                    <td class="sign" data-name="miniStock">${ handleNull(importList[i].miniStock) }</td>' +
                    <td class="sign" data-name="desc">${ handleNull(importList[i].desc)}</td>' +
                    <td>
                        <span class="ty-color-blue btn" data-name="update">修改</span>
                        <span class="ty-color-red btn" data-name="del">删除</span>
                        <span class="hd">${JSON.stringify(importList[i])}</span>
                    </td>
                 </tr>`;
            }
            $(".mainCon3 .importCon2 .normal1").show();
            $(".mainCon3 .importCon2 .normal1 tbody").html(str);
            $(".mainCon3 .importCon2 .normal2").hide()
            // for (var i = 0; i < importList.length; i++) {
            //     trObj = $(".mainCon3 .importCon2 .normal1 tbody tr:eq("+ i+")");
            //     invoiceSettings(trObj.find("select[name='taxRate']"),importList[i].taxRate,"",source);
            // }
        }
    }

    if (buttonState == 1) {
        $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImportJudge()");
    } else {
        $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
    }
}

function setinitRate(rateList, selectV) {
    let str = `<option value=""> 请选择</option>`
    rateList.forEach(function(item){
        str += `<option ${ selectV == item ? 'selected' : ''  } value="${ item }"> ${ item }%</option>`
    })
    return str
}