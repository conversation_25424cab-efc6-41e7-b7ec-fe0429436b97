var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#unfilledTip"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#relateRecord"));
bounce_Fixed3.cancel();
var gblObj = null;
let editType = 1;
$(function () {
    // 实例化复制按钮
    new ClipboardJS('.copyFun');

    $(".fap").click(function () {
        let faObj = $(this).find(".fa");
        $("#leadingBefore .fap").each(function(){
            $(this).find(".fa").attr("class","fa fa-circle-o")
        })
        faObj.attr("class","fa fa-dot-circle-o")
    })
    $(".faI").click(function () {
        let faObj = $(this).find(".fa");
        $("#editPrice .fa").attr("class","fa fa-circle-o")
        faObj.attr("class","fa fa-dot-circle-o")
        let val = faObj.data("type")
        if(val === 1){
            $("#date1").hide()
        }else{
            $("#date1").show().val('')
        }

    })

    $(".fap2").click(function () {
        let faObj = $(this).find(".fa");
        $(this).siblings(".fap2").find(".fa").attr("class","fa fa-circle-o")
        faObj.attr("class","fa fa-dot-circle-o")
    })

    $(".clearInputVal").on({
        "mousedown": function () {
            $(this).prev().val("");
        },
        "mouseup": function () {
            if ($(this).siblings(".modItemTtl").find(".lenTip").length > 0) {
                $(this).siblings(".modItemTtl").find(".lenTip").html("0/100");
            }
            $(this).prev().get(0).focus();
        }
    });
    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
    $("#video-box").on("click", '', function () {
        $("#video-box video")[0].pause();
        $("#video-box").fadeOut("fast");
    });
    $("body").on('click', '.nodeBtn,.linkBtn,.funBtn, .blueBtn, .lineBtn', function () {
        var that = $(this)
        var name = that.data("fun");
        switch (name) { //新增计量单位
            case 'addUnit':
                addUnit('unitSelect');
                break;
            case 'addUnitImport':
                addUnit('import_update_unitSelect');
                break;
            case 'baseRecord':
                let cat = that.data("cat")
                $("#commEditLog").data("cat", cat);
                baseRecord(cat);
                break;
            case 'baseEditRecordScan':
                baseEditRecordScan(that);
                break;
            case 'otherEditRecordScan':
                otherEditRecordScan(that);
                break;
            case 'newContract':
                let cusInfo = {
                    id: $("#curID span:last").data("id"),
                    fullName: $("#curID span:last").data("name"),
                    code: $("#curID span:last").data("code")
                }
                $("#newContractInfo").data("cusInfo", cusInfo)
                bounce_Fixed2.show($("#newContractInfo"));
                $("#newContractInfo input").val("")
                $("#newContractInfo .fileCon>div").html("")
                $("#newContractInfo .deleteFile").html('')
                initUpload($("#cUpload1"),'img');
                initUpload($("#cUpload2"),'doc');
                $("#newContractInfo [name='customerName']").val(cusInfo.fullName)
                $("#newContractInfo .scanGs").data('gsArr',[]).data('gsArrNo',[]).html("0") ;
                getContractByCustomer(cusInfo.id)
                    .then(data => {
                        $("#newContractInfo").data('productListZS',data.productListZS) ;
                        $("#newContractInfo").data('productListTY',data.productListTY) ;
                        renderEditProductZS()
                        renderEditProductTy()
                    })
                break;
            case 'addGs' :
                let fun = that.data("fun");
                $("#tipcontractGoods").data("fun", fun);
                $("#tipcontractGoods .bonceHead span").html('向本合同添加商品');
                $(".addOrCancel").show(); $(".cScanc").hide();
                let gsArrNo = $("#newContractInfo .scanGs").data('gsArrNo')
                setContactGS(gsArrNo , true);
                break;
            case 'removeGs' :
                let fun1 = that.data("fun");
                $("#tipcontractGoods").data("fun", fun1);
                $("#tipcontractGoods .bonceHead span").html('从本合同移出商品');
                $(".addOrCancel").show(); $(".cScanc").hide();
                var list = $("#newContractInfo .scanGs").data('gsArr')
                setContactGS(list, true);
                break;
            case 'contractScan':
                var id = $("#contractScan").data("id");
                contractScan(id);
                break;
            case 'importOpen':
                leadingShow();
                break;
            case 'relatedRecord':
                relatedRecord(that);
                break;
            case 'manageDeliveryPlace':
                let source = that.data("source");
                manageDeliveryPlace(source);
                break;
            case 'deliveryDescription':
                bounce_Fixed2.show($("#deliveryDescription"));
                break;
            default:
                window[name] && window[name]($(this), name);
        }
    });
    $("#newContractInfo").on('click','[type="btn"]', function () {
        let fun = $(this).data("fun");
        let cusInfo  = $("#newContractInfo").data("cusInfo")
        let tips = '', title = '', list = [], checkedLength = 0;
        let productListZS = $("#newContractInfo").data('productListZS') || []
        let productListTY = $("#newContractInfo").data('productListTY') || []
        let editProductZS = productListZS.filter(item => item.isChecked)
        let editProductTY = productListTY.filter(item => item.isChecked)

        switch (fun){
            case 'scanZsGs' :
                title = '本合同下的专属商品'
                list = editProductZS
                tips = `本合同下的专属商品共有以下${ list.length }种`
                break;
            case 'scanTyGs' :
                title = '本合同下的通用型商品'
                list = editProductTY
                tips = `本合同下的通用型商品共有以下${ list.length }种`
                break;
            case 'removeZsGs' :
                title = '从本合同移出专属商品'
                list = editProductZS
                tips = `可供选择的专属商品共有以下${ list.length }种`
                break;
            case 'addZsGs' :
                title = '向本合同添加专属商品'
                list = productListZS
                tips = `${cusInfo.code||''} ${cusInfo.fullName||''}的专属商品共有以下${ list.length }种`
                break;
            case 'removeTyGs' :
                title = '从本合同移出通用型商品'
                list = editProductTY
                tips = `可供选择的通用型商品共有以下${ list.length }种`
                break
            case 'addTyGs' :
                title = '向本合同添加通用型商品'
                list = productListTY
                tips = `系统内共有以下${ list.length }种通用型商品，均可选择`
                break;
        }
        $("#tipcontractGoods .bonceHead span").html(title);
        $("#tipcontractGoods .tip").html(tips);
        let count = 0
        if (fun === 'addZsGs' || fun === 'addTyGs') {
            count = list.filter(item => item.isChecked).length
        }
        $("#tipcontractGoods .count").html(count);
        $("#tipcontractGoods").data('origin', fun)
        if (fun === 'scanZsGs' || fun === 'scanTyGs') {
            $(".addOrCancel").hide(); $(".cScanc").show();$(".countStr").hide()
            setContactGS(list , false);
        } else if (fun === 'addZsGs' || fun === 'addTyGs'){
            $(".addOrCancel").show(); $(".cScanc").hide();$(".countStr").show()
            setContactGS(list, true, true);
        } else {
            $(".addOrCancel").show(); $(".cScanc").hide();$(".countStr").show()
            setContactGS(list, true);
        }
    })
    $(".bounce_Fixed2").on('click','.fileImScan,.node', function(){
        var name = $(this).data('fun');
        switch (name) {
            case 'imgScan': // 合同的picture
                imgViewer($(this));
                break;
            case 'cWord': // 合同的可编辑版
                // let path = $("#cScan .cWord").attr('path');
                seeOnline($("#cScan .cWord a"))
                break;
            case 'gNum': //本合同下的商品
                $("#tipcontractGoods .bonceHead span").html('本合同下的商品');
                $(".addOrCancel").hide(); $(".cScanc").show();
                let productList = $("#cScan .gNum").data('list');
                setContactGS(productList , false);
                break;
            case 'cEditLog': // 本版本合同的修改记录

                break;
            case 'cRenewalLog': // 本合同的续约记录

                break;
        }
    });
    $(".bounce_Fixed").on('click', ".ty-btn",function() {
        var name = $(this).data('name');
        switch (name) {
            case "importCancelSure": // 放弃
                var type = $("#importCancel").data("type");
                var enetryType = $("#importEnteryType").data("type");
                bounce_Fixed.cancel();
                if (type == '1') {
                    $(".mainCon1").show().siblings().hide();
                    if (enetryType == 1){
                        $('.indexInput li').eq(0).find("button").click();
                    } else {
                        $('.indexInput li').eq(1).find("button").click();
                    }
                } else {
                    saveImport(0, 2);
                }
                break;
            case "lastSaveSure": // 确定保存
                saveImport(1);
                break;
            case "judgeImport": // 批量导入尚未完成是否继续
                var flag = $(".unfinishedForm .fa-circle").data("type")
                var type = $("#importBtn").data("type");
                if (flag == '1') {
                    $(".mainCon3 .importCon2").show().siblings().hide();
                    $(".mainCon3").show().siblings().hide();
                    bounce.cancel(); bounce_Fixed.cancel();
                    getUnFinishImportList(type);
                } else if (flag == '0') {
                    saveImport(0, 1);
                } else {
                    layer.msg("请选择上次的批量导入尚未完成是否继续！");
                }
                break;
            case "updateImportMtBtn": // 修改确定
                var emptyNum = 0;
                var obj = $("#updateImportMt").data("obj");
                var trObj = obj.parents("tr");
                $("#updateImportMt input[require]:visible").each(function () {
                    var val = $(this).val();
                    if (val == '') {
                        emptyNum++;
                        console.log($(this))
                    }
                })
                if ($("#updateImportMt select").val() == '--'){ emptyNum++;  }
                if (emptyNum > 0) {
                    layer.msg("还有必填项尚未填写！");
                } else {
                    var codeMt = $("#updateImportMt input[name='code']").val();
                    var oldCode = $("#updateImportMt input[name='code']").data("old");
                    var same = 0, name= "";
                    var upType = $("#updateImportMt").data("upType");
                    if (upType == 1) {
                        $(".mainCon3 .importCon1 tbody tr").each(function () {
                            if ($(this) !== trObj) {
                                var info = JSON.parse($(this).children("td:last").find(".hd").html());
                                if (codeMt == info.code && info.code != oldCode){
                                    same++;
                                    name = info.name;
                                }
                            }
                        })
                    } else if (upType == 2) {
                        $(".mainCon3 .importCon2 tbody tr").each(function () {
                            if ($(this) !== trObj) {
                                var info = JSON.parse($(this).children("td:last").find(".hd").html());
                                if (codeMt == info.code && info.code != oldCode) {
                                    same++;
                                    name = info.name;
                                }
                            }
                        });
                    }
                    if(same > 0) {
                        var str = '<p>您录入的商品代号与公司'+ name +'商品代号相同。</p><p>请确认！</p>';
                        $("#iknowTip .iknowWord").html(str);
                        bounce_Fixed2.show($("#iknowTip"));
                    }else {
                        var trData =JSON.parse(obj.siblings(".hd").html());
                        if (upType == 1) {
                            $.ajax({
                                url: "../commodityImport/updateFalsePdEnter.do",
                                data: {
                                    "code": codeMt
                                },
                                success: function (data) {
                                    var status = data.status;
                                    if (status == '1') {
                                        $("#updateImportMt [need]").each(function () {
                                            var key = $(this).attr("name");
                                            var val = $(this).val();
                                            trData[key] = val;
                                            if (key == 'unitId') {
                                                trData['unit']=$("#import_update_unitSelect option:selected").text();
                                            }
                                        });
                                        var media = [];
                                        $("#updateImportMt .filePicBox .imgsthumb").each(function () {
                                            var cur = $(this).find(".filePic");
                                            var item = {
                                                'uplaodPath': cur.data('path'),
                                                'title':cur.data('ttl'),
                                                'type': 1,
                                                'orders': $(this).index()
                                            }
                                            media.push(item);
                                        });
                                        trData.pdCommodityImportAttachListJson = JSON.stringify(media);
                                        bounce_Fixed.cancel();
                                        trObj.find(".sign").each(function () {
                                            var name = $(this).data("name");
                                            if (name == 'unit') {
                                                $(this).html($("#import_update_unitSelect option:selected").text());
                                            } else {
                                                let iVal = $("#updateImportMt [name=" +name +"]").val();
                                                if(name == "unitPrice" || name == "unitPriceNotax" || name == 'unitPriceNoinvoice' ||  name == 'unitPriceInvoice' ){
                                                    iVal = Number(iVal).toFixed(2);
                                                }
                                                $(this).html(iVal);
                                            }
                                        });
                                        obj.siblings(".hd").html(JSON.stringify(trData));
                                    } else if(status == '2' || status == '4'){
                                        var tip = '<p>您录入的商品代号与公司'+ data.name +'商品代号相同。</p>' +
                                            '<p>请确认！</p>';
                                        $("#iknowTip .iknowWord").html(tip);
                                        bounce_Fixed2.show($("#iknowTip"));
                                    }
                                }
                            });
                        } else if (upType == 2) {
                           /* var json = {
                                "id": trData.id
                            }*/

                            // 重置 价格 开票种类
                            trData['unitPrice'] = ""
                            trData['unitPriceInvoice'] = ""
                            trData['unitPriceNoinvoice'] = ""
                            trData['unitPriceNotax'] = ""
                            trData['unitPriceReference'] = ""
                            trData['invoiceType'] = ""
                            trData['taxRate'] = ""

                            $("#updateImportMt [need]:visible").each(function () {
                                var key = $(this).attr("name");
                                var val = $(this).val();
                                trData[key] = val;
                                if (key == 'unitId') {
                                    trData['unit']= $(this).find("option:selected").text();
                                }
                                else if(val && Number(val) > 0){
                                    if(key == "unitPrice" || key == 'unitPriceNotax' || key == 'unitPriceNoinvoice' || key == 'unitPriceInvoice' || key == 'unitPriceReference' ){
                                        trData[key] = Number(val).toFixed(2);
                                    }
                                }
                            });
                            var media = [];
                            $("#updateImportMt .filePicBox .imgsthumb").each(function () {
                                var cur = $(this).find(".filePic");
                                var item = {
                                    'uplaodPath': cur.data('path'),
                                    'title':cur.data('ttl'),
                                    'type': 1,
                                    'orders': $(this).index()
                                }
                                media.push(item);
                            });
                            trData['pdCommodityImportAttachListJson'] = JSON.stringify(media);
                            updateMtInfo(trData, 1);
                        }
                    }
                }
                break;
        }
    });
    $(".unfinishedForm").on('click', ".fa",function() {
        $(this).addClass("fa-circle").removeClass("fa-circle-o");
        $(this).parent().siblings(".changeDot").find("span").addClass("fa-circle-o").removeClass("fa-circle");
    });
    $(".mainCon3 ").on('click', '.btn', function(){
        let name = $(this).data("name");
        let thisObj = $(this);
        switch (name) {
            case 'clearNoSave':
                $("#importCancel").data("type",'1');
                bounce_Fixed.show($("#importCancel"));
                break;
            case 'stepNext':
                allImportMtEnter();
                break;
            case "cancelSave": // 放弃
                $("#importCancel").data("type", 2);
                bounce_Fixed.show($("#importCancel"));
                break;
            case "initUpdate": // 修改
                $("#updateImportMt").data("upType", 1);
                initImportUpdateMt(thisObj);
                break;
            case "initDel": // 删除
                $("#importMtDel").data("obj", thisObj);
                $("#importMtDel").data("type", '1');
                bounce_Fixed.show($("#importMtDel"));
                break;
            case "update": // 修改
                $("#updateImportMt").data("upType", 2);
                initImportUpdateMt(thisObj);
                break;
            case "del": // 删除
                $("#importMtDel").data("obj", thisObj);
                $("#importMtDel").data("type", '2');
                bounce_Fixed.show($("#importMtDel"));
                break;
        }
    });
    getEditState();
    $("#editCommodityBase .fa-close").click(function () {
        let inputCObj = $(this).parent('.inputC')
        if(inputCObj.length > 0){
            inputCObj.find('input').val('')
        }
    })
    $(".faGroup1").click(function () {
        let faObj = $(this).find('.fa')
        $(this).parent().find('.fa').attr('class' , 'fa fa-circle-o')
        faObj.attr('class' , 'fa fa-dot-circle-o')
    })
})

// creator: 张旭博，2024-09-09 02:57:56， 获取商品
function getContractByCustomer(id) {
    let data = {}
    if (id) {
        data.customer = id
    }
    return new Promise((resolve, reject) => {
        $.ajax({
            url: $.webRoot + '/sales/insertCommodityByTypeForContract.do',
            data: data
        }).then(res => {
            let data = res.data
            resolve(data)
        })
    })
}

function renderEditProductZS() {
    let productListZS = $("#newContractInfo").data('productListZS') || []
    let editProductZS = productListZS.filter(item => item.isChecked)
    let nameArr = editProductZS.map(item => item.outerName)
    $("#newContractInfo .zsGoodNumber").html(nameArr.length)
    $("#newContractInfo .zsGoodList").html(nameArr.join("、"))
}
function renderEditProductTy() {
    let productListTY = $("#newContractInfo").data('productListTY') || []
    let editProductTY = productListTY.filter(item => item.isChecked)
    let nameArr = editProductTY.map(item => item.outerName)
    $("#newContractInfo .tyGoodNumber").html(nameArr.length)
    $("#newContractInfo .tyGoodList").html(nameArr.join("、"))
}

function clearPrice() {
    $("#price1").val("");
    $("#price2").val("");
}
function clearPrice2() {
    $("#price11").val("").removeAttr('disabled');
    $("#price21").val("").removeAttr('disabled');
}
// creator: 李玉婷，2020-03-30 09:21:28，获取设置的发票
 rateListStr = ""
function invoiceSettings2(container , dataList ,source){
    $.ajax({
        "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
        async: false,
        success: function (res) {
            var list = res["financeInvoiceSettings"];
            for (var i = 0; i < dataList.length; i++) {
                let selectedVal = dataList[i]["taxRate"];
                let curObj = container.children("tr:eq("+ i+")");
                let options = `<option value="">请选择</option>`;
                let cat1 = false,cat2 = false;
                if (list && list.length > 0) {
                    for (var i = 0; i < list.length; i++) {
                        if (list[i]["category"] == '1'){
                            var rate = list[i]["enableTaxTate"];
                            var rates = rate.split(",");
                            cat1 = true;
                            if (rates && rates.length >0){
                                var str = '<option value=""></option>';
                                for (var r = 0; r < rates.length; r++) {
                                    var selectedStr = "";
                                    if (selectedVal == rates[r]) {
                                        selectedStr = " selected='true' ";
                                    }
                                    str += "<option " + selectedStr + " value='" + rates[r] + "'>" + rates[r] + "%</option>";
                                }
                                curObj.html(str);
                                rateListStr = str
                            }
                        } else if (list[i]["category"] == '2' || list[i]["category"] == '3'){
                            cat2 = true;
                        }
                    }
                    if (cat1) {options += `<option value="1">开具增值税专用发票</option>`;}
                    if (cat2) {options += `<option value="2">开具其他发票</option>`;}
                }
                options += `<option value="4">不开发票</option>`;
                if (source == 1) {//1为通用商品 2为专属商品
                    if ($("#addCommodity:visible").length > 0) {
                        cat1? $("#addCommodity .invoice1").show():$("#addCommodity .invoice1").hide();
                        cat2? $("#addCommodity .invoice2").show():$("#addCommodity .invoice2").hide();
                    } else if ($("#editCommodityOther:visible").length > 0) {
                        cat1? $("#editCommodityOther .invoice1").show():$("#editCommodityOther .invoice1").hide();
                        cat2? $("#editCommodityOther .invoice2").show():$("#editCommodityOther .invoice2").hide();
                    } else if ($(".mainCon3 .importCon2:visible").length > 0) {
                        cat1? $(".mainCon3 .importCon2 .po-invoice1").show():$(".mainCon3 .importCon2 .po-invoice1").hide();
                        cat2? $(".mainCon3 .importCon2 .po-invoice2").show():$(".mainCon3 .importCon2 .po-invoice2").hide();
                    }
                } else if (source == 2) {
                    if ($("#addCommodity:visible").length > 0) {
                        $("#addCommodity [name='invoiceCategory']").html(options);
                    } else if ($("#editCommodityOther:visible").length > 0) {
                        $("#editCommodityOther [name='invoiceCategory']").html(options);
                        $("#editCommodityOther [name='invoiceCategory']").val(catVal);
                    } else if ($(".mainCon3 .importCon2:visible").length > 0) {
                        cat1? $(".mainCon3 .importCon2 .po-invoice1").show():$(".mainCon3 .importCon2 .po-invoice1").hide();
                        cat2? $(".mainCon3 .importCon2 .po-invoice2").show():$(".mainCon3 .importCon2 .po-invoice2").hide();
                        $(".mainCon3 .importCon2 tbody tr select[name='invoiceType']").html(options);
                        $(".mainCon3 .importCon2 tbody tr select[name='invoiceType']").val(catVal);
                    }
                }

            }

        }
    });
}
function invoiceSettings(curObj,selectedVal,catVal,source){
    let options = `<option value="">请选择</option>`;
    let cat1 = false,cat2 = false;
    $.ajax({
        "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
        async: false,
        success: function (res) {
            var list = res["financeInvoiceSettings"];
            $(".fap01").hide(); $(".fap02").hide();
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    if (list[i]["category"] == '1'){
                        var rate = list[i]["enableTaxTate"];
                        var rates = rate.split(",");
                        cat1 = true;
                        if (rates && rates.length >0){
                            var str = '<option value=""></option>';
                            for (var r = 0; r < rates.length; r++) {
                                var selectedStr = "";
                                if (selectedVal == rates[r]) {
                                    selectedStr = " selected='true' ";
                                }
                                str += "<option " + selectedStr + " value='" + rates[r] + "'>" + rates[r] + "%</option>";
                            }
                            curObj.html(str);
                            rateListStr = str
                        }
                    } else if (list[i]["category"] == '2' || list[i]["category"] == '3'){
                        cat2 = true;
                    }
                }
                if (cat1) {options += `<option value="1">开具增值税专用发票</option>`;  $(".fap01").show();   }
                if (cat2) {options += `<option value="2">开具其他发票</option>`; $(".fap02").show(); }
            }
            options += `<option value="4">不开发票</option>`;
            if (source == 1) {//1为通用商品 2为专属商品
                if ($("#addCommodity:visible").length > 0) {
                    cat1? $("#addCommodity .invoice1").show():$("#addCommodity .invoice1").hide();
                    cat2? $("#addCommodity .invoice2").show():$("#addCommodity .invoice2").hide();
                } else if ($("#editCommodityOther:visible").length > 0) {
                    cat1? $("#editCommodityOther .invoice1").show():$("#editCommodityOther .invoice1").hide();
                    cat2? $("#editCommodityOther .invoice2").show():$("#editCommodityOther .invoice2").hide();
                } else if ($(".mainCon3 .importCon2:visible").length > 0) {
                    cat1? $(".mainCon3 .importCon2 .po-invoice1").show():$(".mainCon3 .importCon2 .po-invoice1").hide();
                    cat2? $(".mainCon3 .importCon2 .po-invoice2").show():$(".mainCon3 .importCon2 .po-invoice2").hide();
                }
            } else if (source == 2) {
                if ($("#addCommodity:visible").length > 0) {
                    $("#addCommodity [name='invoiceCategory']").html(options);
                } else if ($("#editCommodityOther:visible").length > 0) {
                    $("#editCommodityOther [name='invoiceCategory']").html(options);
                    $("#editCommodityOther [name='invoiceCategory']").val(catVal);
                } else if ($(".mainCon3 .importCon2:visible").length > 0) {
                    cat1? $(".mainCon3 .importCon2 .po-invoice1").show():$(".mainCon3 .importCon2 .po-invoice1").hide();
                    cat2? $(".mainCon3 .importCon2 .po-invoice2").show():$(".mainCon3 .importCon2 .po-invoice2").hide();
                    $(".mainCon3 .importCon2 tbody tr select[name='invoiceType']").html(options);
                    $(".mainCon3 .importCon2 tbody tr select[name='invoiceType']").val(catVal);
                }
            }
        }
    });
}
function invoiceSet(catVal, rateVal){
    let options = `<option value="">请选择</option>`;
    let cat1 = false,cat2 = false;
    $.ajax({
        "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
        async: false,
        success: function (res) {
            var list = res["financeInvoiceSettings"];
            var rateListStr = '<option value=""></option>';
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    if (list[i]["category"] == '1'){
                        var rate = list[i]["enableTaxTate"];
                        var rates = rate.split(",");
                        cat1 = true;
                        if (rates && rates.length >0){
                            for (var r = 0; r < rates.length; r++) {
                                var selectedStr = "";
                                if (rateVal == rates[r]) {
                                    selectedStr = " selected='true' ";
                                }
                                rateListStr += `<option ${selectedStr} value="${ rates[r] }">${ rates[r] }%</option>`
                            }
                        }
                    } else if (list[i]["category"] == '2' || list[i]["category"] == '3'){
                        cat2 = true;
                    }
                }
                let se1 = catVal == '1' ? 'selected' :''
                let se2 = catVal == '2' ? 'selected' :''
                if (cat1) {options += `<option value="1" ${ se1 }>开具增值税专用发票</option>`;    }
                if (cat2) {options += `<option value="2" ${ se2 }>开具其他发票</option>`;  }
            }
            let se4 = catVal == '4' ? 'selected' :''
            options += `<option value="4" ${ se4 }>不开发票</option>`;
            $("#ep_cat").html(options)
            $("#addInv").html(rateListStr)
        }
    });
}
// creator: 李玉婷，2020-03-17 15:37:18，商品录入,source:1=通用型，2=专属
function newSalesCommodity(isSaled,source) {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }
    else {
        $("#addUnitSee").html("");
        $("#addCommodity").data('isSaled', isSaled);
        $("#addCommodity").data('source', source);
        $("#addCommodity input").val("");
        $("#addCommodity select").val("");
        $("#addCommodity .invoice1 input").prop("disabled", false);
        $("#addCommodity .lenTip").html("0/100");
        $("#addCommodity .filePicBox").html("<span class='inTip'>请上传</span>");
        $("#addCommodity .fileVedioBox").html("<span class='inTip'>请上传</span>");
        $("#addCommodity .clearPicsBtn").hide();
        $("#importBtn").data("type", isSaled);
        if ($("#importUploadFile #select_btn_1").length <= 0) {
            initImportUpload();
        }
        if (isSaled == 1){
            $(".sold").show();$(".noSold").hide();
            $("#addCommodity .bonceHead span").html("录入已销售过的商品");
        }else{
            $(".sold").hide();$(".noSold").show();
            $("#addCommodity .bonceHead span").html("录入未销售过的商品");
        }
        if (source == 2){
            let customerID = $("#firstLevelName").data("categoryid")
            if(!customerID){
                console.log('先别点，不合法')
                return false
            }
            var categoryName = $("#firstLevelName").html();
            if (categoryName == '' || categoryName=='全部') categoryName = '待分类';
            $("#categoryType").html(categoryName);
            $("#addCommodity .contractList").prop("disabled",true);
            $("#addCommodity .priceType").hide();
            $("#addCommodity [data-fun='newContract']").hide();
            $("#addCommodity").data("customer",$("#curID span:last").data("id"));
            let obj = $('#addCommodity [name="contractId"]');
            let param = {
                obj: obj,
                customer: $("#curID span:last").data("id"),
                contract: ""
            }
            $("#addCommodity .gray-box").html("").siblings(".hd").html("")
            getContractBaseList(param);
        }
        if ($("#filePic #select_btn_1").length <= 0) {
            initUpload($("#filePic"),'entry');
        }
        if ($("#fileVedio .uploadify-button").length <= 0) {
            initViewUpload($("#fileVedio"));
        }
        bounce.show($("#addCommodity"));
        invoiceSettings($("#addInvoiceSpecial"),"","",source);
        // 计量单位列表、
        getUnitList($("#unitSelect"), source);
        // 清空之前的数据
        $(".firstTimeCCon").find('.fa').attr('class', 'fa fa-circle-o')
        $("#addCommodity").find("input[name='minimumStock']").val("0"); // 最低库存默认0
        // 清空单选操作
        $("#firstTimeCC").data('type','0')
        $("#firMonth").val('')
        $(".firstTimeCCon").find('.fa').attr('class', 'fa fa-circle-o')


    }
}
// creator: 侯杏哲，2023-12-27 12:13:08，录入价格
function reSetPrice() {
    $("#unitPriceNotax_add").removeAttr('disabled')
    $("#unitPrice_add").removeAttr('disabled')
    $("#unitPriceNotax_add").val("")
    $("#unitPrice_add").val("")
}
function autoPrice(num) {
    let rate = $("#addInvoiceSpecial").val()
    let price11 = $("#unitPriceNotax_add").val() //不含税
    let price21 = $("#unitPrice_add").val() // 含税
    if(rate){
        price11 = Number(price11) ? Number(price11): ''
        price21 = Number(price21) ? Number(price21): ''

        if(num === 1){
            if(price11){
                $("#unitPrice_add").attr("disabled",'true')
                $("#unitPriceNotax_add").removeAttr('disabled')
                let price = Number(price11 * (1 + rate/100));
                $("#unitPrice_add").val(parseFloat(Number(price).toFixed(10)))
            }
        }
        if(num === 2){
            if(price21 ){
                $("#unitPriceNotax_add").attr("disabled",'true')
                $("#unitPrice_add").removeAttr('disabled')
                let price = Number(price21 / (1 + rate/100));
                $("#unitPriceNotax_add").val(parseFloat(Number(price).toFixed(10)))
            }
        }

        if(price11 ==='' && price21 === ''){
            $("#unitPriceNotax_add").removeAttr('disabled')
            $("#unitPrice_add").removeAttr('disabled')
        }
    }

}
function autoPrice2(num) {
    let rate = $("#addInv").val()
    let price11 = $("#price11").val() //不含税
    let price21 = $("#price21").val() // 含税
    if(rate){
        price11 = Number(price11) ? Number(price11): ''
        price21 = Number(price21) ? Number(price21): ''

        if(num === 1){
            if(price11){
                $("#price21").attr("disabled",'true')
                $("#price11").removeAttr('disabled')
                let price = Number(price11 * (1 + rate/100));
                $("#price21").val(parseFloat(Number(price).toFixed(10)))
            }
        }
        if(num === 2){
            if(price21 ){
                $("#price11").attr("disabled",'true')
                $("#price21").removeAttr('disabled')
                let price = Number(price21 / (1 + rate/100));
                $("#price11").val(parseFloat(Number(price).toFixed(10)))
            }
        }

        if(price11 ==='' && price21 === ''){
            $("#price11").removeAttr('disabled')
            $("#price21").removeAttr('disabled')
        }
    }

}

// creator: 李玉婷，2020-03-31 12:13:08，录入- 计量单位设置
function unitAssign(obj) {
    var val = obj.val();
    var unitName = obj.children("option:selected").html();
    $("#addUnitSee").html(unitName);
    obj.siblings("input").val(unitName)
}
// creator: 李玉婷，2020-03-18 11:06:46，查看商品,source:1=通用，2=专属
function seeCommodityDetails(obj, source,state,canEdit) {
    var info = JSON.parse(obj.siblings(".hd").html());
    $('#seeCommodity').data("id", info.id);
    //$("#seeCommodity .editBtn").prop('disabled',state);
    if (state){
        $('#stopDetails').show();
        $("#seeCommodity .editBtn").hide();
    }else{
        $('#stopDetails').hide();
        if (!canEdit){
            $("#seeCommodity .editBtn").hide();
        }else{
            $("#seeCommodity .editBtn").show();
        }
    }
    getCommodityDetails(info.id,info.type);
}
function getCommodityDetails(productId, source){
    $.ajax({
        url: "../product/getProductOne.do",
        data: {
            productId: productId
        },
        success: function (data) {
            if (data.code == 200){
                var info = data.data;
                $("#seeCommodity").data('info', info)
                var mediaList = info.fileList;
                var operList = info.startAndStopList, operStr = ``;
                var creatorStr = ``;
                var updateDate = new Date(info.updateDate).format('yyyy-MM-dd hh:mm:ss');
                $("#inforStorage").html(JSON.stringify(info));
                $("#seeCommodity [need]").each(function () {
                    var name = $(this).data("name");
                    $(this).html(handleNull(info[name]));
                    if(name == 'unit'){
                        $(this).data('unitid', info['unitId'])
                    } else if (name == 'innerSn' && handleNull(info[name]) == "") {
                        $(this).html("暂无");
                    } else if (name == 'contractNumber' && handleNull(info[name]) == ""){
                        $(this).html("尚未填写");
                    } else if(name == 'minimumStock'){
                        $(this).html(parseFloat(Number(info[name] || "0").toFixed(4)));
                    } else if(name == 'addressListString'){
                        let address = ``;
                        let arr = info.addressList || []
                        for(let i =0;i<arr.length ;i++){
                            address += arr[i].address + ',';
                        }
                        if (address !== "") {address = address.substring(0,address.length -1)}
                        $(this).html(address);
                    }
                });
                $("#nameInvoice").html( formatPatten(info.nameInvoice, info )|| '--')
                $("#glInvoice").html(formatPatten(info.glInvoice , info) || '--')
                let picStr = ``, vedio=``;
                if (mediaList && mediaList.length > 0) {
                    let picNum = 0;
                    for(var t=0;t<mediaList.length;t++) {
                        if (mediaList[t].type == '1') {
                            ++picNum;
                            picStr += `<span data-path="${mediaList[t].uplaodPath}" onclick="imgViewer($(this))">${picNum}</span>`;
                        } else {
                            vedio += `<span data-path="${mediaList[t].uplaodPath}" onclick="vedioPlay($(this))">1</span>`;
                        }
                    }
                }
                creatorStr += `<span class="oprationName">${info.createName}</span>${new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss')}`;
                $("#seeCommodity .comPics").html(picStr);
                $("#seeCommodity .comVideo").html(vedio);
                $("#seeCreator").html(creatorStr);
                $('#stopName').html(info.updateName);
                $('#stopDate').html(updateDate);
                $('#seeCommodity').data("source",source);
                $('#seeCommodity #seeInvoice' + source).show().siblings().hide();
                if (operList && operList.length > 0) {
                    for(var r=0;r<operList.length;r++) {
                        operStr +=
                            `<div><span class="oper">${operList[r].enabled == 0? '暂停销售':'恢复销售'}</span> <span class="oprationName">${operList[r].updateName}</span>${new Date(operList[r].updateDate).format('yyyy-MM-dd hh:mm:ss')}</div>`;
                    }
                }
                $("#seeCommodity .reLog").html(operStr);
                var unitPrice = handleNull(info.unitPrice)==''? '0.00': info.unitPrice;
                var unitPriceNotax = handleNull(info.unitPriceNotax)==''? '0.00': info.unitPriceNotax;
                var unitPriceInvoice = handleNull(info.unitPriceInvoice)==''? '0.00': info.unitPriceInvoice;
                var unitPriceNoinvoice = handleNull(info.unitPriceNoinvoice)==''? '0.00': info.unitPriceNoinvoice;
                if(source == 1) {
                    var unitPriceReference = handleNull(info.unitPriceReference)==''? '0.00': info.unitPriceReference;
                    var specialDes = handleNull(info.taxRate)=='' && handleNull(info.unitPrice)=='' && handleNull(info.unitPriceNotax)==''? '暂无':'税率'+handleNull(info.taxRate)+'%，含税单价'+unitPrice+'元，不含税价'+unitPriceNotax+'元';
                    let generalDes = `${handleNull(info.unitPriceInvoice)==''?'暂无':'开票单价' +unitPriceInvoice+'元'}`;
                    let noDes = `${handleNull(info.unitPriceNoinvoice)==''?'暂无':'不开票价'+unitPriceNoinvoice+ '元'}`;
                    let referenceDes = `${handleNull(info.unitPriceReference)==''?'暂无': '参考单价'+ unitPriceReference+'元'}`;
                    $("#seeCommodity [data-name='specialDes']").html(specialDes);
                    $("#seeCommodity [data-name='generalDes']").html(generalDes);
                    $("#seeCommodity [data-name='noDes']").html(noDes);
                    $("#seeCommodity [data-name='referenceDes']").html(referenceDes);
                }
                else if(source == 2){
                    // 不含税单价:unitPriceNotax 含税单价unitPrice 开普票时的开票单价unitPriceInvoice 不开发票时的单价unitPriceNoinvoice
                    let invoiceCategoryStr = ``, priceStr = ``;
                    if (info["invoiceCategory"] == 1) {
                        invoiceCategoryStr = `该商品需开税率为${info.taxRate}%的增值税专用发票`;
                        priceStr =  `含税单价${unitPrice}元，不含税价${unitPriceNotax}元`;
                    } else if (info["invoiceCategory"] == 2) {
                        invoiceCategoryStr = `该商品需开增值税专用发票以外的发票`;
                        priceStr =  `开普通发票的开票单价${unitPriceInvoice}元`;
                    } else if (info["invoiceCategory"] == 4) {
                        invoiceCategoryStr = `该商品不开发票`;
                        priceStr = `不开票单价${unitPriceNoinvoice}元`;
                    }
                    if (handleNull(info.contractNumber) != '') {
                        $("#contractScan").show();
                    } else {
                        $("#contractScan").hide();
                    }
                    $("#contractScan").data("id",info.contractId);
                    $("#seeCommodity [data-name='invoiceCategory']").html(invoiceCategoryStr);
                    $("#seeCommodity [data-name='price']").html(priceStr);
                }
                let exclusiveTxt = ''
                if(info.exclusiveTime == 1){
                    exclusiveTxt = '今年'
                    if(info.exclusiveMonth){
                        exclusiveTxt += Number(info.exclusiveMonth.substr(4,2)) + '月'
                    }
                }else if(info.exclusiveTime == 2){
                    exclusiveTxt = '去年'
                }else if(info.exclusiveTime == 3){
                    exclusiveTxt = '更久之前'
                }
                $("#exclusiveTimeC").html( exclusiveTxt)

                bounce.show($('#seeCommodity'));
            }
        }
    })
}
// creator : hxz 2018-07-23  获取商品 当前订购信息
function curOrders(thisObj) {
    var info = JSON.parse(thisObj.siblings(".hd").html());
    var outerSn = info["outerSn"];
    $("#curOrders tbody").children("tr:gt(0)").remove();
    $.ajax({
        "url": "../sale/getOccOrder.do",
        "data": {"outerSn": outerSn, "orderId": ""},
        success: function (res) {
            var list = res["data"], str = "";
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var n = i + 1;
                    str += "<tr>" +
                        "     <td>" + n + "</td>" +
                        "     <td>"+ (list[i]["sn"] || "") +"</td>" +
                        "     <td>"+ list[i]["create_name"] +"</td>" +
                        "     <td>"+ list[i]["t_amount"] +"</td>" +
                        "     <td>"+ formatTime(list[i]["earliest_delivery_date"])  +"</td>" +
                        "     <td>"+ list[i]["customer_name"] +"</td>" +
                        " </tr>";
                }
            }
            $("#curOrders tbody").append(str);
            bounce.show($("#curOrders"));
        }
    });
}
// creator: 李玉婷，2020-03-19 09:28:36，暂停或恢复商品销售,1恢复 0暂停
function suspendSale(obj, state) {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else{
        var info = JSON.parse(obj.siblings(".hd").html());
        var tips = '';
        var data = {
            'productId': info.id,
            'state': state
        };
        var param = JSON.stringify(data);
        $("#pauseSales").data('data', param);
        $("#pauseSales").data('state', state);
        if (state == 1){
            tips =
                '<p class="ty-center">操作成功后，再下订单时此商品又可被选择了！</p>'+
                '<p class="ty-center">确定“恢复销售”此商品？</p>';
            $("#pauseSalesTip").html(tips);
            bounce.show($("#pauseSales"));
        }else{
            $.ajax({
                "url": "../product/suspendProductJudge.do",
                "data": data,
                success: function (res) {
                    if (res.code == '1'){
                        tips = '<p class="ty-center">操作成功后，再下订单时将无法选到此商品。</p>'+
                            '<p class="ty-center">确定“暂停销售”此商品？</p>';
                    }else {
                        tips =
                            '<p class="gapLeft">1、此商品尚有库存！</p>' +
                            '<p class="gapLeft">2、操作成功后，再下订单时将无法选到此商品。</p>'+
                            '<p class="ty-center">确定“暂停销售”此商品？</p>';
                    }
                    $("#pauseSalesTip").html(tips);
                    bounce.show($("#pauseSales"));
                }
            });
        }
    }
}
// creator: 李玉婷，2020-03-19 10:31:42，暂停或恢复商品销售确定
function suspendSaleSure() {
    var json = JSON.parse($("#pauseSales").data('data'));
    var state = $("#pauseSales").data('state');
    $.ajax({
        "url": "../product/suspendProduct.do",
        "data": json,
        success: function (res) {
            if (res.code == 200){
                var keyword = $("#searchKeyBase").val();
                bounce.cancel();
                if (state == 1){
                    getSuspendList(1,20,keyword);
                }else{
                    var name = $("#firstLevelName").html();
                    var id = $("#firstLevelName").data("categoryid");
                    getCommodityList(1, 20,id,name,keyword);
                }
            }
        }
    });
}
// creator: 李玉婷，2020-03-19 09:30:28，删除商品
function deleteCommodity(obj, state) {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else {
        gblObj = obj;
        var info = JSON.parse(obj.siblings(".hd").html());
        var productId = info.id;
        $('#deleteCommodity').data('id', productId);
        $('#deleteCommodity').data('state', state);
        bounce.show($('#deleteCommodity'));
        $.ajax({
            "url": "../product/deleteProductJudge.do",
            "data": {
                'productId': productId
            },
            success: function (res) {
                if (res.code == '1'){
                    $("#deleteTip").html('<p class="ty-center">确定删除此商品？</p>');
                    $(".deleteCan").show();
                    $(".delRefuse").hide();
                } else{
                    $("#deleteTip").html('<p class="ty-indent">系统不支持“删除”有关联、包装或订购信息的商品，而此商品在系统中有关联、包装或订购信息！</p>');
                    $(".deleteCan").hide();
                    $(".delRefuse").show();
                }
            }
        });
    }
}
// creator: 李玉婷，2020-03-19 11:38:49，删除商品确定
function deleteCommoditySure(){
    var id = $('#deleteCommodity').data('id');
    var state = $('#deleteCommodity').data('state');
    $.ajax({
        "url": "../product/deleteProduct.do",
        "data": {
            'productId': id
        },
        success: function (res) {
            if (res.code == 200){
                bounce.cancel();
                var keyword = $("#searchKeyBase").val();
                if (state == 1){
                    getSuspendList(1,20,keyword);
                }else{
                    var name = $("#firstLevelName").html();
                    var id = $("#firstLevelName").data("categoryid");
                    getCommodityList(1, 20,id,name,keyword);
                }
            }
        }
    });
}
// creator: hxz 商品价格信息的修改记录
function epLog(obj) {
    let infoOral = $("#seeCommodity").data('info')
    let id = infoOral.id // 商品ID
    let source = infoOral.type;
    $.ajax({
        'url':'../product/getPdMerchandisePriceRecordList.do',
        'data': { id: id },
        success:function(res){
            let list = res.data.list || []
            if(list.length === 0){
                layer.msg('没有价格修改记录！')
                return false
            }
            bounce_Fixed.show($("#epPriceLog"))
            $("#epPriceLog table tr:gt(0)").remove();
            let str = ``
            var priceStrAll = ''
            list.forEach( info => {
                var unitPrice = handleNull(info.unitPrice)==''? '0.00': info.unitPrice;
                var unitPriceNotax = handleNull(info.unitPriceNotax)==''? '0.00': info.unitPriceNotax;
                var unitPriceInvoice = handleNull(info.unitPriceInvoice)==''? '0.00': info.unitPriceInvoice;
                var unitPriceNoinvoice = handleNull(info.unitPriceNoinvoice)==''? '0.00': info.unitPriceNoinvoice;
                if(source == 1){ // 通用
                    var unitPriceReference = handleNull(info.unitPriceReference)==''? '0.00': info.unitPriceReference;
                    var specialDes = handleNull(info.taxRate)=='' && handleNull(info.unitPrice)=='' && handleNull(info.unitPriceNotax)==''? '':'税率'+handleNull(info.taxRate)+'%，含税单价'+unitPrice+'元，不含税价'+unitPriceNotax+'元';
                    let generalDes = `${handleNull(info.unitPriceInvoice)==''?'':'开票单价' +unitPriceInvoice+'元'}`;
                    let noDes = `${handleNull(info.unitPriceNoinvoice)==''?'':'不开票价'+unitPriceNoinvoice+ '元'}`;
                    let referenceDes = `${handleNull(info.unitPriceReference)==''?'': '参考单价'+ unitPriceReference+'元'}`;
                    priceStrAll = `${ specialDes ? specialDes + ';<br/>' : '' } 
                                ${ generalDes ? generalDes + ';<br/>' : '' } 
                                ${ noDes ? noDes + ';<br/>' : '' } 
                                ${ referenceDes ? referenceDes + ';<br/>' : '' } `

                }
                else{ // 专属
                    let invoiceCategoryStr = ``, priceStr = ``;
                    if (info["invoiceCategory"] == 1) {
                        invoiceCategoryStr = `该商品需开税率为${info.taxRate}%的增值税专用发票`;
                        priceStr =  `含税单价${unitPrice}元，不含税价${unitPriceNotax}元`;
                    } else if (info["invoiceCategory"] == 2) {
                        invoiceCategoryStr = `该商品需开增值税专用发票以外的发票`;
                        priceStr =  `开普通发票的开票单价${unitPriceInvoice}元`;
                    } else if (info["invoiceCategory"] == 4) {
                        invoiceCategoryStr = `该商品不开发票`;
                        priceStr = `不开票单价${unitPriceNoinvoice}元`;
                    }
                    priceStrAll = `${ invoiceCategoryStr }<br/> ${ priceStr }`
                }

                str += `
                 <tr>
                    <td>${ priceStrAll }</td>
                    <td>${ new Date(info.effectiveDate).format('yyyy-MM-dd') } 至 ${ new Date(info.endDate).format('yyyy-MM-dd') } </td>
                    <td>${ info.createName || '' } ${ new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss') } <br>/<br>
                        ${ info.updateName || '' } ${ new Date(info.updateDate).format('yyyy-MM-dd hh:mm:ss') }</td>
                    <td>${ info.basicReason || '' }</td>
                    <td>${ info.financerName || '' } ${ new Date(info.financerTime).format('yyyy-MM-dd hh:mm:ss') }</td>
                </tr>
                `
            })
            $("#epPriceLogTab tr:gt(0)").remove();
            $("#epPriceLogTab").append(str);

            let lastInfo = list[list.length - 1]
            let infoStr = `当前资料为第${ list.length - 1 }次修改后的结果，修改人：${ lastInfo.updateName } ${ new Date(lastInfo.updateDate).format('yyyy-MM-dd hh:mm:ss') }`
            $("#epPriceLogInfo").html(infoStr);

        }
    })
}
// creator: hxz，2023-12-07  其他价格
function showOtherPrice(type) {  // 1 - 通用型商品 2- 专属商品
    let info = $("#seeCommodity").data('info')
    let productId = info.id
    $.ajax({
        "url":'../product/getOtherPrice.do',
        "data": { 'productId': productId },
        success: function(res){
            let resD = res.data
            let isShow = resD.isShow
            // isShow = true
            if(isShow){
                let info = resD.data
                $("#otherPrice .validateInfo").html(`${ new Date(info.effectiveDate).format('yyyy-MM-dd') }至${ '--' || new Date().format('yyyy-MM-dd') }`)
                var unitPrice = handleNull(info.unitPrice)==''? '0.00': info.unitPrice;
                var unitPriceNotax = handleNull(info.unitPriceNotax)==''? '0.00': info.unitPriceNotax;
                var unitPriceInvoice = handleNull(info.unitPriceInvoice)==''? '0.00': info.unitPriceInvoice;
                var unitPriceNoinvoice = handleNull(info.unitPriceNoinvoice)==''? '0.00': info.unitPriceNoinvoice;

                if(type === 1){ // 通用
                    var unitPriceReference = handleNull(info.unitPriceReference)==''? '0.00': info.unitPriceReference;
                    var specialDes = handleNull(info.taxRate)=='' && handleNull(info.unitPrice)=='' && handleNull(info.unitPriceNotax)==''? '':'税率'+handleNull(info.taxRate)+'%，含税单价'+unitPrice+'元，不含税价'+unitPriceNotax+'元';
                    let generalDes = `${handleNull(info.unitPriceInvoice)==''?'暂无':'开票单价' +unitPriceInvoice+'元'}`;
                    let noDes = `${handleNull(info.unitPriceNoinvoice)==''?'暂无':'不开票价'+unitPriceNoinvoice+ '元'}`;
                    let referenceDes = `${handleNull(info.unitPriceReference)==''?'暂无': '参考单价'+ unitPriceReference+'元'}`;

                    $("#otherPrice .priceCat1").html(specialDes)
                    $("#otherPrice .priceCat2").html(generalDes)
                    $("#otherPrice .priceCat3").html(noDes)
                    $("#otherPrice .priceCat4").html(referenceDes)

                }else{ // 专属
                    let invoiceCategoryStr = ``, priceStr = ``;
                    if (info["invoiceCategory"] == 1) {
                        invoiceCategoryStr = `该商品需开税率为${info.taxRate}%的增值税专用发票`;
                        priceStr =  `含税单价${unitPrice}元，不含税价${unitPriceNotax}元`;
                    } else if (info["invoiceCategory"] == 2) {
                        invoiceCategoryStr = `该商品需开增值税专用发票以外的发票`;
                        priceStr =  `开普通发票的开票单价${unitPriceInvoice}元`;
                    } else if (info["invoiceCategory"] == 4) {
                        invoiceCategoryStr = `该商品不开发票`;
                        priceStr = `不开票单价${unitPriceNoinvoice}元`;
                    }
                    $("#otherPrice .invoiceInfo").html(invoiceCategoryStr)
                    $("#otherPrice .priceInfo").html(priceStr)
                }


                bounce_Fixed.show($('#otherPrice'));

            }else{
                layer.msg('没有其他价格，当前价格长期有效！')

            }
        }
    })

}
// creator: 李玉婷，2020-03-19 14:29:01，修改 价格信息
function updatePrice(model) {  // 1 - 通用型商品 2- 专属商品
    bounce_Fixed.show($('#editPrice'));
    $('#editPrice input').val('')
    $("#price11").val("").removeAttr('disabled');
    $("#price21").val("").removeAttr('disabled');
    $('#editPrice .fa').attr('class','fa fa-circle-o')
    $('#editPrice').data('model', model)
    let info = $("#seeCommodity").data('info')
    $('#editPrice .gsInfo').html(`${ info.outerSn }/${ info.outerName }/${ info.specifications }/${ info.model } `)
    let catVal = info.invoiceCategory
    let rateVal = info.taxRate
    invoiceSet(catVal, rateVal)
    if(model === 1){
        $("#price11").val(info.unitPriceNotax)
        $("#price21").val(info.unitPrice)
        $("#price3").val(info.unitPriceInvoice) // 开普通发票时的开票单价
        $("#price4").val(info.unitPriceNoinvoice) // 不开发票时的单价
        $("#price5").val(info.unitPriceReference) // 不开发票时的单价
    }
    else{
        if(catVal){
            $(`.inv${ catVal }`).show().siblings().hide();
            if(catVal == 1){
                $("#price11").val(info.unitPriceNotax)
                $("#price21").val(info.unitPrice)
            }else if(catVal == 2){
                $("#price3").val(info.unitPriceInvoice) // 开普通发票时的开票单价
            }else if(catVal == 4){
                $("#price4").val(info.unitPriceNoinvoice) // 不开发票时的单价
            }
        }else{
            $(`.inv1`).hide().siblings().hide();
        }
    }
    $(`#editPrice .inputOk`).hide()
    $("#date1").hide()
    $(`#editPrice .orderUnNum22`).html(info.orderNum)
    bounce_Fixed.everyTime('0.5s','faITimer',function(){


        // 后续显示
        let priceOk = 0
        if(model == 2){ // 专属
            $("#editPrice [need]:visible").each(function(){
                let val = $(this).val()
                if(!val){
                    priceOk++;
                }
            })
        }else{

        }


        let typeObj = $("#editPrice .fa-dot-circle-o")
        if(typeObj.length > 0){
            let type = typeObj.data('type')
            if(type === 2){
                let date = $("#date1").val()
                if(!date){ priceOk++; }
            }
        } else {
            priceOk++;
        }
        if(priceOk === 0){
            $(".inputOk").show()
            let useDate = new Date().format('yyyy-MM-dd')
            if(typeObj.val() === '2'){
                useDate = $("#date1").val()
            }
            $("#editPrice .useDate").html(useDate);
            $("#editPriceOk").removeAttr("disabled")

        }else{
            $(".inputOk").hide()
            $("#editPriceOk").attr("disabled",true)

        }
    },500)
}
function editPriceOk(){
    let model = $('#editPrice').data('model')
    let info = $("#seeCommodity").data('info')
    let data = {
        'id': info.id
    }
    let faSelect = $('#editPrice .fa-dot-circle-o')
    data.basicReason = $("#ep_reason").val()
    data.effectiveOption = faSelect.length > 0 ? faSelect.data('type') : ''
    if(data.effectiveOption === 1){
        data.effectiveDate = (new Date().format('yyyy-MM-dd'))

    }else if(data.effectiveOption === 2){
        data.effectiveDate = $("#date1").val()
    }
    if(!data.effectiveDate){
        layer.msg('请选择生效日期'); return false
    }

    data.invoiceCategory = $("#ep_cat").val()
    if(model === 1){
        data.taxRate = $("#addInv").val()
        data.unitPrice = $("#price21").val()
        data.unitPriceNotax = $("#price11").val()
        data.unitPriceInvoice = $("#price3").val()
        data.unitPriceNoinvoice = $("#price4").val()
        data.unitPriceReference = $("#price5").val()
        if(data.taxRate || data.unitPriceNotax || data.unitPrice){
            if(!data.taxRate){ layer.msg('请录入税率'); return false }
            if(!data.unitPriceNotax){ layer.msg('请录入不含税单价'); return false }
            if(!data.unitPrice){ layer.msg('请录入含税单价'); return false }
        }

    }else{
        if(data.invoiceCategory === '1'){ // 增专
            data.taxRate = $("#addInv").val()
            data.unitPrice = $("#price21").val()
            data.unitPriceNotax = $("#price11").val()
            if(!data.taxRate){ layer.msg('请录入税率'); return false }
            if(!data.unitPriceNotax){ layer.msg('请录入不含税单价'); return false }
            if(!data.unitPrice){ layer.msg('请录入含税单价'); return false }
        }else if(data.invoiceCategory === '2'){ // 增通
            data.unitPriceInvoice = $("#price3").val()
            if(!data.unitPriceInvoice){ layer.msg('请录入开普通发票时的开票单价'); return false }

        }else if(data.invoiceCategory === '4'){ // 不开
            data.unitPriceNoinvoice = $("#price4").val()
            if(!data.unitPriceNoinvoice){ layer.msg('请录入不开发票时的单价'); return false }

        }
    }
    $.ajax({
        'url':'../product/updatePdMerchandisePrice.do',
        'data': data,
        success:function(res){
            let success = res.success
            if(success == 1){
                bounce_Fixed.cancel()
                var info = $("#seeCommodity").data('info')
                getCommodityDetails(data.id, info.type);
                let cur = $("#ye .yecur").html()
                let json = JSON.parse($("#ye .json").html()  )
                var key = json.param;
                var category = json.category;
                var categoryName = json.categoryName;
                getCommodityList(cur, 20, category, categoryName, key);
            }else{
                $("#unfilledTip_ms").html(res.errorMsg);
                bounce_Fixed2.show($("#unfilledTip"));
            }

        }
    })

}

function dataLog(thisObj,type) {
    let id = thisObj.parent().parent().data('info')
    bounce_Fixed2.show($("#ep_dataLog"))
    let typeObj =  $(`#ep_dataLog .type${ type }`)
    typeObj.show().siblings().hide();
    let ttl = type === 1 ? '发货记录' :  type === 2 ? '签收记录' :'开票记录'
    $("#ttlss").html(ttl)
    let data = {
        orderId: id
    }
    let url = '../sale/orderShipped.do' // 发货记录
    if( type === 2 ){
        url = '../sale//orderSigned.do' // 签收记录
    }
    else if( type === 3){
        url = '../sale/orderInvoiced.do' // 开票记录
    }
    $.ajax({
        'url':url,
        'data': data,
        success:function(res){
            let str = ''
            typeObj.children('table').find('tr:gt(0)').remove();
            typeObj.children('table').append(str);
        }
    })
}

// creator: 李玉婷，2020-03-19 14:29:01，修改基本信息
function updateCommodityBase(model){  // 1 - 通用型商品 2- 专属商品
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else{
        var info = JSON.parse($("#inforStorage").html());
        if (editType === 2 && info.addType !== null) {
            layer.msg("对不起，您无权进行此项修改！");
            return false;
        }

        $('#orderUnNum2').html(info.orderNum);
        // $("#editCommodityBase .lenTip").html(handleNull(info.memo).length + "/100");
        $("#editCommodityBase [need]").each(function(){
            var name = $(this).attr('name');
            $(this).val(info[name]);

        });
        $("#updateCommodityBase2 .fa-dot-circle-o").attr('class',"fa fa-circle-o")

        $('#editCommodityBase .lenTip').html('0/100')

        $("#editCommodityBase .cc" + info['exclusiveTime']).find('.fa').attr('class','fa fa-dot-circle-o')
        $("#firstTimeCC2").data('type', info['exclusiveTime'])
        if(info['exclusiveTime'] == 1){
            $(".firMonthcon").show()
            if(info['exclusiveMonth'].length == 6){
                $("#firMonth2").val(info['exclusiveMonth'])
                $(".ccN").find('.fa').attr('class', 'fa fa-circle-o')
            }else{
                $("#firMonth2").val("")
                $(".ccN").find('.fa').attr('class', 'fa fa-dot-circle-o')
            }

        }else{
            $(".firMonthcon").hide()
        }
        bounce_Fixed.show($('#editCommodityBase'));
    }
}
function updateCommodityBaseSure(){
    var emptyNum = 0;
    $("#updateCommodityBase2 [require]:visible").each(function(){
        if($(this).val() == ""){ emptyNum++; }
    });
    let nameLinkable = $("#updateCommodityBase2 .nameLinkable .fa-dot-circle-o")
    let modelLinkable = $("#updateCommodityBase2 .modelLinkable .fa-dot-circle-o")
    if(nameLinkable.length === 0 || modelLinkable.length === 0){
        emptyNum++;
    }
    if (emptyNum > 0){
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }else{
        var id = $('#seeCommodity').data('id');
        var params = {
            id: id
        };
        $("#updateCommodityBase2 [need]").each(function(){
            var nameData = $(this).attr('name');
            var html = $(this).val();
            params[nameData] = html;
        });
        // String basicReason;   //基本信息修改原因
        // String modelLinkable;  //规格型号是否需要修改  1是0否
        // String nameLinkable； //名称是否需要修改  1是0否
        params['modelLinkable'] = modelLinkable.data('sta')
        params['nameLinkable'] = nameLinkable.data('sta')
        params['exclusiveTime'] =  $("#firstTimeCC2").data('type')
        params['exclusiveMonth'] =  $("#firMonth2").val() || (new Date().format('yyyy'))

        $.ajax({
            "url": "../product/updateProductBase.do",
            "data": params,
            success: function (res) {
                if (res.code == 'success'){
                    bounce_Fixed.cancel();
                    var source = $('#seeCommodity').data("source");
                    getCommodityDetails(id, source);
                    getCommodityList(1,20,"","全部",'');
                }else {
                    var msg = res.msg;
                    $("#unfilledTip_ms").html(msg);
                    bounce_Fixed2.show($("#unfilledTip"));
                }
            }
        });
    }
}
// updator: hxz，2023-09-20  修改其他信息
function updateOtherData(){
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else {
        var info = JSON.parse($("#inforStorage").html());
        var source = $('#seeCommodity').data("source");
        $('#orderUnNumOther').html(info.orderNum);
        $("#editCommodityOther .priceDesc .lenTip").html(handleNull(info.priceDesc).length + "/100");
        $("#editCommodityOther .gsDesc .lenTip").html(handleNull(info.memo).length + "/100");
        $("#editCommodityOther .reason .lenTip").html("0/100");
        $("#editCommodityOther [need]").each(function(){
            var nameData = $(this).attr('name');
            if (nameData == 'outerSn' || nameData == 'outerName' || nameData == 'specifications' || nameData == 'model') {
                $(this).html(info[nameData]);
            } else {
                $(this).val(info[nameData])
            }
        });
        $("#editCommodityOther .invoice1 input").prop("disabled", false);
        bounce_Fixed.show($('#editCommodityOther'));
        // invoiceSettings($("#update_unitSelect"),info.taxRate,info.invoiceCategory,source);
        var unitList = getUnitListData(source);
        var option = '<option value="--">- - 请选择 - - </option>';
        if(unitList && unitList.length >0){
            for(var i = 0 ; i < unitList.length ; i++){
                var item = unitList[i];
                let selectStr = info.unitId === item['id'] ? 'selected' : ''
                option += `<option ${ selectStr } value="${ item['id'] }">${ item['name'] }</option>`
            }
        }
        $("#update_unitSelect").html(option);

        var mediaList = info.fileList;
        if ($("#filePic_edit #select_btn_1").length <= 0) {
            initUpload($("#filePic_edit"), 'entry');
        }
        if ($("#fileVedio_edit .uploadify-button").length <= 0) {
            initViewUpload($("#fileVedio_edit"));
        }
        let picStr = `<span class="inTip">请上传</span>`, vedio=`<span class="inTip">请上传</span>`;
        if (mediaList && mediaList.length > 0) {
            let picN = 0;
            picStr = ``;
            for(var t=0;t<mediaList.length;t++) {
                if (mediaList[t].type == '1') {
                    picN++;
                    picStr +=
                        `<div class="imgsthumb">
                            <span class="filePic" data-type="1" data-path="${mediaList[t].uplaodPath}" data-ttl="${mediaList[t].title}" onclick="imgViewer($(this))">${picN}</span>
                    <i class="fa fa-times" fileUid="${mediaList[t].fileUid}" onclick="cancleThis($(this))"></i>
                    </div>`;
                } else {
                    vedio =
                        `<div class="imgsthumb">
                            <span class="filePic" data-type="2" data-path="${mediaList[t].uplaodPath}" data-ttl="${mediaList[t].title}" onclick="imgViewer($(this))">1</span>
                    <i class="fa fa-times" fileUid="${mediaList[t].fileUid}" onclick="cancleThis($(this))"></i>
                    </div>`;
                }
            }
        }
        $('#editCommodityOther .filePicBox').html(picStr);
        $('#editCommodityOther .fileVedioBox').html(vedio);
        if ($('#editCommodityOther .filePicBox .imgsthumb').length > 0){
            $('#editCommodityOther .clearPicsBtn').show();
        } else {
            $('#editCommodityOther .clearPicsBtn').hide();
        }



        if (source == '2') {
            let addressList = info.addressList || [], addrListStr = ``;
            $("#editCommodityOther .priceType").hide();
            $('#editCommodityOther [name="contractType"]').val("");
            let obj = $('#editCommodityOther [name="contractId"]');
            let param = {
                obj: obj,
                customer: info.customer_,
                contract: info.contractId
            }
            getContractBaseList(param);
            if (handleNull(info.contractId) != "") {
                $('#editCommodityOther [name="contractType"]').val(1);
                $('#editCommodityOther [name="contractId"]').prop("disabled", false);
                $("#addCommodity [data-fun='newContract']").show();
            } else {
                $("#addCommodity [data-fun='newContract']").hide();
                $('#editCommodityOther [name="contractId"]').prop("disabled", true);
            }
            if (info.invoiceCategory && info.invoiceCategory != "") {
                $("#editCommodityOther .invoice" + info.invoiceCategory).show();
            }
            addressList.forEach((item)=>{
                addrListStr += item.address + ',';
            })
            if (addrListStr !== "") {addrListStr = addrListStr.substring(0, addrListStr.length-1)}
            $(".edit_deliveryPlaceData").html(addrListStr).siblings(".hd").html(JSON.stringify(addressList));
        }
    }
}
// creator: 李玉婷，2020-03-20 11:15:01，修改商品价格信息确定
function updateOtherSure() {
    var reqTrue = 0; //invoiceCategory  '发票类型:1-增值税专用票,2-增值税普通票,3-其他票,4-不开票'
    $("#editOtherInvoice [require]:visible").each(function () {
        if ($(this).val() == "") reqTrue++;
    });
    if (reqTrue > 0) {
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }
    var id = $('#seeCommodity').data('id');
    var source = $('#seeCommodity').data("source");
    var params = {
        id: id
    };
    $("#editOtherInvoice input[need]:visible").each(function(){
        var nameData = $(this).attr('name');

        if(nameData == 'unitPriceNotax' || nameData == 'unitPrice') {
            params[nameData] = Number($(this).val());
        } else {
            params[nameData] = $(this).val();

        }
    });
    params['unit'] = $('#update_unitName').val();

    $("#editOtherInvoice select[need]:visible").each(function(){
        var nameData = $(this).attr('name');
        var html = $(this).val();
        params[nameData] = html;
    });
    if($("#editOtherInvoice [name='invoiceCategory']").val() == ""){
        delete params["invoiceCategory"];
    }
    if (source === 2 || source === '2'){
        if ($(".edit_deliveryPlaceData").html() !== "") {
            params.addressListString = $(".edit_deliveryPlaceData").siblings(".hd").html()
        }
    }
    //图片 视频
    var media = [];
    $("#editCommodityOther .imgsthumb").each(function () {
        var cur = $(this).find(".filePic");
        var item = {
            'uplaodPath': cur.data('path'),
            'title':cur.data('ttl'),
            'type': cur.data('type')
        }
        media.push(item);
    });
    params.commodityMediaList = JSON.stringify(media);

    $.ajax({
        "url": "../product/updateProductOtherBase.do",
        "data": params,
        success: function (res) {
            if (res.code == 'success'){
                bounce_Fixed.cancel();
                getCommodityDetails(id,source);
                getCommodityList(1,20,"","全部",'');
            }
        }
    });
}
// creator: 李玉婷，2020-03-27 17:42:06，分类
function kindBtn(obj) {
    var source = obj.data("source");//source:1=通用，2=专属
    if (source === 2) {
        if (handleNull(obj.data("delivery")) === "" || obj.data("delivery") === 1 || obj.data("delivery") === '1') {
            layer.msg("不可选择该客户");
            return false;
        }
    }
    var json = obj.siblings("div").children(".kindId").html();
    json = JSON.parse(json);
    var keyword = $("#searchKeyBase").val();
    var strson = '<span onclick="showkindNav($(this))" data-id="'+ json.id +'" data-name="'+ json.fullName +'" data-code="'+ json.code +'"> >'+ json.fullName +'</span>';
    $("#curID").append(strson);
    getCommodityList(1,20,json.id,json.fullName,keyword);
    var stop = obj.data("stop")
    if (stop === 1){
        $(".indexInput .faceul .faceul1").addClass("disabled");
        $(".indexInput .faceul button").prop("disabled",true);
    }else{
        $(".indexInput .faceul button").prop("disabled",false);
        $(".indexInput .faceul .faceul1").removeClass("disabled");
    }
}
// creator: 李玉婷，2020-03-27 14:28:26，搜索
function searchKeyBase() {
    var keyword = $("#searchKeyBase").val();
    var categoryName = $("#firstLevelName").html();
    var category = '';
    if (categoryName != '全部') {
        category = $("#firstLevelName").data("categoryid");
    }
    if ($(".inSalesList:visible").length > 0){
        getCommodityList(1,20,category,categoryName,keyword);
    } else if ($(".inSuspendList:visible").length > 0){
        getSuspendList(1,20,keyword);
    }
}
// creator: 李玉婷，2020-03-30 15:48:25，暂停销售的商品
function suspendCommodyList(obj){
    $(".inSuspend").show();
    $(".inSales").hide();
    $(".indexInput").hide();
    $(".suspendBtn").hide();
    $(".left-bottom").show();
    $(".between").height(0);
    $("#kindsTree").html('');
    $("#firstLevelAmount").html('0');
    $("#firstLevelName").html(obj.html());
    var strson = '<span onclick="showkindNav($(this))" data-id="" data-name="'+ obj.html() +'"> >'+ obj.html() +'</span>';
    $("#curID").append(strson);
    getSuspendList(1,20,"");
}
// creator: 李玉婷，2020-04-03 10:28:28，描述、说明字数限制
function limitWord(obj, num) {
    var val = obj.val();
    var length = val.length;
    if (length <= num){
        obj.siblings("p").find(".lenTip").html(length + '/' + num);
    }else{
        var str = val.slice(0,num);
        obj.val(str);
        obj.siblings("p").find(".lenTip").html(str.length + '/' + num);
    }
}

// updator: hxz，2023-10-23  描述、说明字数限制
function limitWord2(obj, num) {
    var val = obj.val();
    var length = val.length;
    if (length <= num){
        obj.parent().siblings(".lenTip").html(length + '/' + num);
    }else{
        var str = val.slice(0,num);
        obj.val(str);
        obj.parent().siblings(".lenTip").html(str.length + '/' + num);
    }
}
// updator: hxz，2023-10-23  描述、说明字数限制
function limitWord3(obj, num) {
    var val = obj.val();
    var length = val.length;
    let tipObj =  obj.siblings("p").find('.ty-right')
    if (length <= num){
        tipObj.html(length + '/' + num);
    }else{
        var str = val.slice(0,num);
        obj.val(str);
        tipObj.html(str.length + '/' + num);
    }
}
// creator: 李玉婷，2020-04-03 13:46:22，当前分类
function showkindNav(obj){
    var id = obj.data('id');
    var name = obj.data('name');
    obj.nextAll().remove();
    if (name != "" && $(".inSuspendList:visible").length > 0){
        getSuspendList(1,20,"");
    } else {
        getCommodityList(1,20,id,name,"");
    }
}
//  返回上一级
function gobackLstLevel(type){
    if (type == 1){
        var  n = $("#curID").children().length-1 ;
        var kindId = "",kindName = "";
        var m = n - 1 ;
        if( m >= 0 ){
            $("#curID").children(":eq("+ n +")").remove() ;
            kindId = $("#curID").children(":eq("+ m +")").data("id");
            kindName = $("#curID").children(":eq("+ m +")").data("name");
            getCommodityList(1,20,kindId,kindName,"" );
        }
    }else{
        $("#curID").children(":eq(0)").nextAll().remove() ;
        getCommodityList(1,20,"","","" );
    }
}
// creator: 李玉婷，2021-07-21 08:23:44，返回列表
function getUnitListData(module) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    var list = [];
    $.ajax({
        "async": false,
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            list = res['list'] ;
        }
    })
    return list;
}
// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectedID) {
    // 　1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['name'] +'</option>';
                }
            }
            obj.html(str);
        }
    })
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit(idStr) {
    bounce_Fixed2.show($("#addUnit"));
    $("#addUnit input").val("");
    $("#updateType").val(idStr);
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed2.cancel();
                var idStr = $("#updateType").val();
                getUnitList($("#" + idStr), module);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed2.show($("#tip1"));
            }
        }
    })
}

// creator: 李玉婷，2021-05-20 17:32:24，上传图片
function initUpload(obj, storg){
    let fileTypeExtsStr = ''
    let multi = true
    if(storg == "img" || storg == "entry"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(storg == "doc"){
        multi = false
        fileTypeExtsStr = '*.*;'
    } else if (storg == "vedio") {
        fileTypeExtsStr = '*.cda,*.wav;*.wmv;*.mp3;*.mp4;*.mpeg;;*.aiff;*.vqf;*.amr;'
    }
    let itemTemplate = ``
    var groupUuid = sphdSocket.uuid();
    obj.html("")
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi:multi,
        buttonText:"上传",
        formData:{
            module: '销售管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadStart:function(){
            loading.open();
        },
        onUploadSuccess:function(file, data){
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            //name = file.name,           //文件名称
            obj.parent().attr("groupUuid", data.groupUuid )
            loading.close();
            switch (storg) {
                case 'entry':
                    let picContainer = obj.parents(".mediaBody").find(".filePicBox")
                    var imgLen = picContainer.find(".imgsthumb").length;
                    if(imgLen < 9){
                        var imgStr1 =
                            `<div class="imgsthumb">
                                <span class="filePic" data-type="1" data-path="${path}" data-ttl="${file.name}" onclick="imgViewer($(this))">${imgLen + 1}</span>
                                <i class="fa fa-times" fileUid="${data.fileUid}" onclick="cancleThis($(this))"></i>
                            </div>`;
                        picContainer.append(imgStr1);
                        if (imgLen < 1) {
                            picContainer.find(".inTip").remove();
                        }
                        obj.parents("td").find(".clearPicsBtn").show();
                    }else{
                        layer.msg('最多只能上传9个文件')
                        delC(data)
                    }
                    break;
                case 'img':
                    let len =  $(`.fileCon1`).find(".fileIm").length;
                    if(len < 9){
                        data.orders = len + 1
                        data.filePath = data.filename
                        data.title = data.originalFilename
                        let imgStr1 = `<span class="fileIm"  >
                                 <span>${ 1 + len }</span>
                                 <span class="fa fa-times"></span>
                                 <span class="hd">${ JSON.stringify(data) }</span>
                            </span>`
                        $(`.fileCon1`).append(imgStr1);
                    }else{
                        layer.msg('最多只能上传9个文件')
                        delC(data);
                    }
                    break;
                case 'doc':
                    let fileCon2Len =  $(`.fileCon2`).find(".fileIm").length;
                    if(fileCon2Len === 0){
                    }else{
                        let delO = $(`.fileCon2`).find(".fileIm")
                        let info = JSON.parse(delO.find(".hd").html())
                        delC(info)
                        delO.remove();
                    }
                    let str2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data) }</span>
                        </span>`
                    $(`.fileCon2`).html(str2);
                    break;
            }
        }
    });
}
function initViewUpload(obj) {
    // 单文件上传不需要groupUuid
    obj.Huploadify({
        auto: false,
        fileTypeExts: '*.mp4',
        formData:{
            module: '销售管理',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: 1000960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedSize: true,
        removeTimeout: 99999999,
        buttonText: "上传",
        queueID: 'fileQueue',
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        multi:false,
        onSelect: function () {
            $(".fileUpload .uploadify-button").hide();
            // 获取文件url，创建一个新的音频元素
            let audioElement = new Audio(URL.createObjectURL(obj.children("input")[0].files[0]));
            // 绑定事件获取时长
            audioElement.addEventListener("loadedmetadata", (_event)=> {
                let duration = parseInt(audioElement.duration);
                console.log("duration")
                // 大小 时长
                if (duration > 15) {
                    //超过需求限制给的提示，你这里自己修改
                    layer.msg("视频时长不能超过15s")
                    // 清除上传队列
                    obj.find(".uploadify-queue").html('')
                } else {
                    // 符合条件时上传，改为手动上传后不会自动上传，在这个事件回调判断结束后再触发上传按钮
                    obj.find(".uploadbtn").click()
                    // 因为不显示过程我在这里加了一个loading
                    loading.open()
                }
            });
            audioElement.load()
        },
        onUploadError: function () {
            obj.find(".uploadify_state").html("上传失败！")
        },
        onUploadStart:function(){
            loading.open();
        },
        onUploadSuccess: function (file, json) {
            var data = JSON.parse(json)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            // 这部分自己修改
            console.log('file', file)
            console.log('json', json)
             if(json.length>0) {
                 // 结束loading
                 loading.close()
                 var mLen = obj.parents("p").siblings(".fileVedioBox ").find(".imgsthumb").length;
                 if(mLen < 1) {
                     var mStr1 =
                         '<div class="imgsthumb">' +
                         '    <a class="filePic" data-type="2" data-path="' + path + '" data-ttl="' + file.name + '" onclick="vedioPlay($(this))">' + (mLen + 1) + '</a>' +
                         '    <i class="fa fa-times" fileUid="' + data.fileUid + '" onclick="cancleThis($(this))"></i> ' +
                         '</div>';
                     obj.parents("p").siblings(".fileVedioBox").html(mStr1);
                 } else {
                     layer.msg('最多只能上传1个视频文件');
                     delC(json)
                 }
             }
            obj.find(".uploadify-queue").html('')
        },
        onQueueComplete:function() {
            loading.close();
            obj.find(".uploadify-queue").html('')
        }
    })
}
// creator：lyt，2021-08-12 16:39:08，中断上传，关闭弹窗
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent4 = selector.parents(".bounce_Fixed4")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent4.length > 0) {
        bounce_Fixed4.cancel()
    } else {
        console.log("未定义的状态")
    }
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = []
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
}
// creator: hxz 2021-05-21 删除已经上传的合同文件
function delC (delInfo) {
    var option = {type: 'fileUid', fileUid: delInfo.fileUid}
    fileDelAjax(option)
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.data('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");
}
// creator: 李玉婷，2021-05-25 15:43:51，视频播放
function vedioPlay(obj) {
    var src = obj.data("path"),
        sourceDom = "<source src=\""+ $.fileUrl + src +"\">";
    $("#video-box video").html(sourceDom);
    $("#video-box").show();
    // 自动播放
    $("#video-box video")[0].play()
}
// creator: 李玉婷，2021-05-24 16:51:45，清除
function clearPicsBtn(obj) {
    obj.siblings(".filePicBox").find(".imgsthumb").each(function () {
        var curObj = $(this).find(".fa");
        cancleThis(curObj);
    })
}
// creator: 李玉婷，2019-09-04 19:31:34，删除图片
function cancleThis(obj) {
    var parentObj = obj.parents(".filePicBox");
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    if (parentObj.find(".imgsthumb").length <= 0) {
        parentObj.siblings(".clearPicsBtn").hide();
        parentObj.html("<span class='inTip'>请上传</span>");
    } else {
        obj.parents(".filePicBox").find(".imgsthumb").each(function () {
            var idx = $(this).index()+ 1;
            $(this).find(".filePic").html(idx);
        })
    }
}

// creator: 李玉婷，2021-05-22 09:05:59，修改记录
function baseRecord(type) {
    var url = '../product/getProductRecordBaseList.do';
    var info = JSON.parse($("#inforStorage").html());
    let id = info.id;
    type == 2? url = '../product/getProductRecordOtherList.do': '';
    if(type == 2){
        $("#commEditLog .cai").hide()
    }else{
        $("#commEditLog .cai").show()
    }
    $.ajax({
        "url": url,
        "data": { "id": id  } ,
        success:function(res) {
            let list = res.data.list || [];
            bounce_Fixed.show($("#commEditLog"));
            let curStaStr = ``;
            if(list.length >0){
                let str = ``;
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    str += `<tr>
                             <td class="ty-td-control">
                                ${i===0 ? "原始信息":"第"+ i +"次修改后"} 
                                <span class="nodeBtn ty-color-blue" data-fun="${type == 1? "baseEditRecordScan":"otherEditRecordScan"}">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                             <td>${i===0 ?item.createName:item.updateName} ${i===0 ?new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss"):new Date(item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                             <td>${ item.basicReason || '' }</td>
                             ${ type == 2 ? '' : `<td>${ item.financerName || '' } ${ new Date(item.financerTime).format("yyyy-MM-dd hh:mm:ss") }</td>` }
                           </tr>`
                }
                $("#commEditLog tbody").children(":gt(0)").remove();
                $("#commEditLog tbody").append(str);
                let n = list.length
                let last = list[n-1]
                curStaStr = `<span style="float: right;">修改人：${last.updateName} ${new Date(last.updateDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料为第${n-1}次修改后的结果。`
                $("#commEditLog table").show();
            }else{
                $("#commEditLog table").hide();
                curStaStr = `<span style="float: right;">创建人：${info.createName} ${new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料尚未经修改。`
            }
            $("#commEditLog .curSta").html(curStaStr);
            $("#commEditLog .bonceHead span").html(type == 2 ? '商品其他信息的修改记录' : '商品基本信息的修改记录');
        }
    });
}
// creator: 李玉婷，2022-02-22 14:51:24，基本信息修改记录查看
function baseEditRecordScan(obj) {
    var type = $("#commEditLog").data("cat");
    var record = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        "url":"../product/getProductRecordBaseDetails.do",
        "data": { "id": record.id  } ,
        success:function(data) {
            if (data.code == 200){
                var info = data.data;
                var mediaList = info.fileList;
                var operList = info.startAndStopList, operStr = ``;
                var creatorStr = ``;
                var updateDate = new Date(info.updateDate).format('yyyy-MM-dd hh:mm:ss');
                $("#commEditLogScan [need]").each(function () {
                    var name = $(this).data("name");
                    $(this).html(handleNull(info[name]));
                    if(name == 'unit'){
                        $(this).data('unitid', info['unitId'])
                    } else if(name == 'minimumStock'){
                        $(this).html(parseFloat(Number(info[name] || "0").toFixed(4)));
                    }
                });
                let picStr = ``, vedio=``;
                if (mediaList && mediaList.length > 0) {
                    let picNum = 0;
                    for(var t=0;t<mediaList.length;t++) {
                        if (mediaList[t].type == '1') {
                            picNum++;
                            picStr += `<span data-path="${mediaList[t].uplaodPath}" onclick="imgViewer($(this))">${picNum}</span>`;
                        } else {
                            vedio += `<span data-path="${mediaList[t].uplaodPath}" onclick="vedioPlay($(this))">${1}</span>`;
                        }
                    }
                }
                creatorStr += `<span class="oprationName">${info.createName}</span>${new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss')}`;
                $("#commEditLogScan .comPics").html(picStr);
                $("#commEditLogScan .comVideo").html(vedio);
                $("#seeCreator_log").html(creatorStr);
                if (operList && operList.length > 0) {
                    for(var r=0;r<operList.length;r++) {
                        operStr +=
                            `<div><span class="oper">${operList[r].enabled == 0? '暂停销售':'恢复销售'}</span> <span class="oprationName">${operList[r].updateName}</span>${new Date(operList[r].updateDate).format('yyyy-MM-dd hh:mm:ss')}</div>`;
                    }
                }
                $("#commEditLogScan .reLog").html(operStr);
                $(".logScan" + type).show().siblings().hide();
                bounce_Fixed2.show($('#commEditLogScan'));
            }
        }
    });
}
// creator: 李玉婷，2022-02-24 08:49:05，其他信息修改记录查看
function otherEditRecordScan(obj) {
    var type = $("#commEditLog").data("cat");
    var record = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        "url":"../product/getProductRecordBaseDetails.do",
        // "url":"../product/getProductRecordOtherDetails.do",
        "data": { "id": record.id  } ,
        success:function(data) {
            if (data.code == 200){
                var info = data.data;
                var operList = info.startAndStopList, operStr = ``;
                var creatorStr = ``,source = info.type;
                $("#commEditLogScan [need]").each(function () {
                    var name = $(this).data("name");
                    $(this).html(handleNull(info[name]));
                    if (name == 'innerSn' && handleNull(info[name]) == "") {
                        $(this).html("暂无");
                    } else if (name == 'contractNumber' && handleNull(info[name]) == ""){
                        $(this).html("尚未填写");
                    } else if(name == 'addressListString'){
                        let address = ``;
                        let arr = info.addressList || []
                        for(let i =0;i<arr.length ;i++){
                            address += arr[i].address + ',';
                        }
                        if (address !== "") {address = address.substring(0,address.length -1)}
                        $(this).html(address);
                    }
                });
                creatorStr += `<span class="oprationName">${info.createName}</span>${new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss')}`;
                $("#seeCreator_log").html(creatorStr);
                if(type === 2){ // 其他信息的
                    var mediaList = info.fileList;
                    let picStr = ``, vedio=``;
                    if (mediaList && mediaList.length > 0) {
                        let picNum = 0;
                        for(var t=0;t<mediaList.length;t++) {
                            if (mediaList[t].type == '1') {
                                ++picNum;
                                picStr += `<span data-path="${mediaList[t].uplaodPath}" onclick="imgViewer($(this))">${picNum}</span>`;
                            } else {
                                vedio += `<span data-path="${mediaList[t].uplaodPath}" onclick="vedioPlay($(this))">1</span>`;
                            }
                        }
                    }
                    creatorStr += `<span class="oprationName">${info.createName}</span>${new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss')}`;
                    $("#commEditLogScan .comPics").html(picStr);
                    $("#commEditLogScan .comVideo").html(vedio);


                }
                $('#commEditLogScan .seeInvoice' + source).show().siblings().hide();
                if (operList && operList.length > 0) {
                    for(var r=0;r<operList.length;r++) {
                        operStr +=
                            `<div><span class="oper">${operList[r].enabled == 0? '暂停销售':'恢复销售'}</span> <span class="oprationName">${operList[r].updateName}</span>${new Date(operList[r].updateDate).format('yyyy-MM-dd hh:mm:ss')}</div>`;
                    }
                }
                $("#commEditLogScan .reLog").html(operStr);

                var unitPrice = handleNull(info.unitPrice)==''? '0.00': info.unitPrice;
                var unitPriceNotax = handleNull(info.unitPriceNotax)==''? '0.00': info.unitPriceNotax;
                var unitPriceInvoice = handleNull(info.unitPriceInvoice)==''? '0.00': info.unitPriceInvoice;
                var unitPriceNoinvoice = handleNull(info.unitPriceNoinvoice)==''? '0.00': info.unitPriceNoinvoice;
                if(source == 1) {
                    var unitPriceReference = handleNull(info.unitPriceReference)==''? '0.00': info.unitPriceReference;
                    var specialDes = handleNull(info.taxRate)=='' && handleNull(info.unitPrice)=='' && handleNull(info.unitPriceNotax)==''? '暂无':'税率'+handleNull(info.taxRate)+'%，含税单价'+unitPrice+'元，不含税价'+unitPriceNotax+'元';
                    let generalDes = `${handleNull(info.unitPriceInvoice)==''?'暂无':'开票单价' +unitPriceInvoice+'元'}`;
                    let noDes = `${handleNull(info.unitPriceNoinvoice)==''?'暂无':'不开票价'+unitPriceNoinvoice+ '元'}`;
                    let referenceDes = `${handleNull(info.unitPriceReference)==''?'暂无': '参考单价'+ unitPriceReference+'元'}`;
                    $("#commEditLogScan [data-name='specialDes']").html(specialDes);
                    $("#commEditLogScan [data-name='generalDes']").html(generalDes);
                    $("#commEditLogScan [data-name='noDes']").html(noDes);
                    $("#commEditLogScan [data-name='referenceDes']").html(referenceDes);

                }else if(source == 2){
                    // 不含税单价:unitPriceNotax 含税单价unitPrice 开普票时的开票单价unitPriceInvoice 不开发票时的单价unitPriceNoinvoice
                    let invoiceCategoryStr = ``, priceStr = ``;
                    if (info["invoiceCategory"] == 1) {
                        invoiceCategoryStr = `该商品需开税率为${info.taxRate}%的增值税专用发票`;
                        priceStr =  `含税单价${unitPrice}元，不含税价${unitPriceNotax}元`;
                    } else if (info["invoiceCategory"] == 2) {
                        invoiceCategoryStr = `该商品需开增值税专用发票以外的发票`;
                        priceStr =  `开普通发票的开票单价${unitPriceInvoice}元`;
                    } else if (info["invoiceCategory"] == 4) {
                        invoiceCategoryStr = `该商品不开发票`;
                        priceStr = `不开票单价${unitPriceNoinvoice}元`;
                    }
                    if (handleNull(info.contractNumber) != '') {
                        $("#contractScan_log").show();
                    } else {
                        $("#contractScan_log").hide();
                    }
                    $("#contractScan_log").data("id",info.contractId);
                    $("#commEditLogScan [data-name='invoiceCategory']").html(invoiceCategoryStr);
                    $("#commEditLogScan [data-name='price']").html(priceStr);
                }
                $(".logScan" + type).show().siblings().hide();
                bounce_Fixed2.show($('#commEditLogScan'));
            }
        }
    });
}

// creator: 李玉婷，2021-08-24 08:35:09，
function initImportUpload() {
    $('#importUploadFile').Huploadify({
        auto:false ,
        fileTypeExts:'*.*;',
        //fileTypeExts:'*.xls;*.xlsx;',
        multi:false,
        formData:{
            module: '销售管理',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:10240,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader: "../uploads/uploadfyByFile.do",
        itemTemplate: '<div id="${fileID}" class="uploadify-queue-item matListUpload" style="display: none">' +
        '<div class="uploadify_bottom"><div><a class="uploadbtn" href="javascript:void(0);"></a></div></div>' +
        '</div>',
        onUploadStart:function(){
        },
        onInit:function(){},
        onSelect:function (file) {
            $('.matListUpload:not(:last)').remove();
            $('#leading .fileFullName').html(file.name);
        },
        onUploadComplete:function(file,data){
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data);
            var filePath = data.filename;
            var fileUid =data.fileUid;
            let json = {
                "fileUid": fileUid,
                "path": filePath
            }
            ImportMaterial(json)
        } ,
        onCancel:function(file){
        }
    });
}
// creator: 李玉婷，2021-08-24 08:19:48，批量导入-判断是否有未完成的导入材料
function leadingShow(){
    var type = $("#importBtn").data("type");
    let source = $("#addCommodity").data('source');
    $.ajax({
        url:"../commodityImport/whetherUnfinishedImport.do",
        data:{
            "isPurchased": type,
            "produdctCatetory": source
        },
        success:function (data) {
            var isHav = data.status;
            if (isHav == '1') {
                bounce_Fixed.show($("#leadingBefore"))
                $("#leadingBefore .cosName").html($("#categoryType").html())
                $("#imRate").html(rateListStr)
            } else {
                $('.unfinishedForm .fa').attr("class","fa fa-circle-o");
                bounce_Fixed.show($('#importNotCompleted'));

            }
        }
    });



}
function leadingShowNext() {
    var type = $("#importBtn").data("type");
    let source = $("#addCommodity").data('source');
    let caseValObj = $("#leadingBefore .fap .fa-dot-circle-o")
    let caseVal = ''
    let hasRateObj = $(".fap2 .fa-dot-circle-o");
    let hasRate = ''
    if(caseValObj.length > 0){
        caseVal = Number(caseValObj.data('val'));
        if(caseVal === 2){ // 增专
            if(hasRateObj.length > 0){
                hasRate = Number(hasRateObj.data('val'));
            }else{
                layer.msg('请将选项补充完整')
                return false
            }
            let taxRate = $("#imRate").val()
            if(taxRate <= 0){
                layer.msg('请将税率补充完整')
                return false
            }
        }
    }else{
        layer.msg('请将选项补充完整')
        return false
    }
    $("#importEnteryType").data("type",type); // 0-未销售过 1-销售过
    $("#importEnteryType").data("source",source); // 2- 专属 1- 通用
    if (source == 2) {
        let customer = $("#addCommodity").data("customer");
        $("#importEnteryType").data("customer", customer);
    }
    $('#select_btn_1').val("");
    $('.matListUpload').remove();
    $("#leading").data("type", type);
    $('#leading .fileFullName').html("尚未选择文件");
    let filepath = ''
    if (type == '1') { // 已销售
        if(source == '1'){ // 通用
            switch (caseVal){
                case 1:
                    filepath = '../assets/oralResource/template/commodity/commodity_saled_blank.xls'
                    break;
                case 2:
                    if(hasRate == 1){
                        filepath = '../assets/oralResource/template/commodity/commodity_saled_blank_sheet4.xls'
                    }else if(hasRate == 0){
                        filepath = '../assets/oralResource/template/commodity/commodity_saled_blank_sheet5.xls'
                    }
                    break;
                case 5:
                    filepath = '../assets/oralResource/template/commodity/commodity_saled_blank_sheet6.xls';
                    break;

            }
        }else if(source == '2'){ // 2- 专属
            switch (caseVal){
                case 1:
                    filepath = '../assets/oralResource/template/commodity/commodity_saled_blank.xls'
                    break;
                case 2:
                    if(hasRate == 1){
                        filepath = '../assets/oralResource/template/commodity/commodity_saled_blank_sheet.xls'
                    }else if(hasRate == 0){
                        filepath = '../assets/oralResource/template/commodity/commodity_saled_blank_sheet1.xls'
                    }
                    break;
                case 3:
                    filepath = '../assets/oralResource/template/commodity/commodity_saled_blank_sheet2.xls';
                    break;
                case 4:
                    filepath = '../assets/oralResource/template/commodity/commodity_saled_blank_sheet3.xls';
                    break;
            }
        }
    }
    else if (type == '0'){ // 未销售过
        if(source == '1'){ // 通用
            switch (caseVal){
                case 1:
                    filepath = '../assets/oralResource/template/commodity/commodity_blank.xls'
                    break;
                case 2:
                    if(hasRate == 1){
                        filepath = '../assets/oralResource/template/commodity/commodity_blank_sheet4.xls'
                    }else if(hasRate == 0){
                        filepath = '../assets/oralResource/template/commodity/commodity_blank_sheet5.xls'
                    }
                    break;
                case 5:
                    filepath = '../assets/oralResource/template/commodity/commodity_blank_sheet6.xls';
                    break;

            }
        }else if(source == '2'){ // 2- 专属
            switch (caseVal){
                case 1:
                    filepath = '../assets/oralResource/template/commodity/commodity_blank.xls'
                    break;
                case 2:
                    if(hasRate == 1){
                        filepath = '../assets/oralResource/template/commodity/commodity_blank_sheet.xls'
                    }else if(hasRate == 0){
                        filepath = '../assets/oralResource/template/commodity/commodity_blank_sheet1.xls'
                    }
                    break;
                case 3:
                    filepath = '../assets/oralResource/template/commodity/commodity_blank_sheet2.xls';
                    break;
                case 4:
                    filepath = '../assets/oralResource/template/commodity/commodity_blank_sheet3.xls';
                    break;
            }
        }
    }
    $("#mould1").attr("href",filepath).data('caseval', caseVal).data('hasrate', hasRate)
    bounce_Fixed.show($('#leading'));

}
// creator: 李玉婷，2020-09-03 17:12:49，导入验证
function matImportOk(type) {
    if(type === 'cancel'){
        bounce_Fixed.cancel()
    }else{
        if ($(".matListUpload").length <= 0) {
            $("#knowTip .knowWord").html('您需选择一个文件后才能“导入”！');
            bounce_Fixed2.show($("#knowTip"))
        } else {
            // loading.open() ;
            $(".matListUpload a").click();
        }
    }
}
// created:hxz 2021-01-09 导入商品
function ImportMaterial(param) {
    let source = $("#addCommodity").data('source');
    let caseValObj = $("#leadingBefore .fap .fa-dot-circle-o")
    let caseVal = ''
    if(caseValObj.length > 0){
        caseVal = Number(caseValObj.data('val'));
    }
    let taxRate = $("#imRate").val()
    let hasRateObj = $(".fap2 .fa-dot-circle-o");
    let hasRate = ''
    if(hasRateObj.length > 0){
        hasRate = Number(hasRateObj.data('val'));
    }
    let isSaled = $("#importEnteryType").data("type")
    if(caseVal && source && (Number(isSaled) === 0 || Number(isSaled) === 1)){
        if(caseVal == 2){
            if(taxRate && (Number(hasRate) === 0 || Number(hasRate) === 1)){
            }else{
                layer.msg("有必填项内容没填！")
                return false
            }
        }
    }else{
        layer.msg("有必填项内容没填！")
        return false
    }
    var customer = ''
    $.ajax({
        "url":"../export/newImportProduct.do",
        "data":{
            'filePath': param.path,
            'importOption': caseVal,
            'taxRate':taxRate,
            'taxInclusive': hasRate,
            'isSaled': isSaled,
            customer: $("#curID span:last").data("id"),
            'type': source
        },
        success:function (data) {
            var status = data.status;
            if (status == '1') {
                var purchasedType = $("#leading").data("type");
                var mtBaseList = data.truePdProductList || [];
                var mtFalseList = data.falsePdProducList || [];
                $("#importEnteryType").data("type",purchasedType);
                $("#importEnteryType").data("source",source);
                $(".importCon1").data("trueProductList", mtBaseList);
                bounce.cancel();bounce_Fixed.cancel();
                var html = '';
                let priceStr = ''
                if(source == 2){ // 专属
                    if(caseVal == 2 || caseVal == 1){ // 增专
                        if(Number(hasRate)>0){
                            priceStr = 'unitPrice'
                            $(".hasRatePrice").html("含税单价")
                        } else {
                            priceStr = 'unitPriceNotax'
                            $(".hasRatePrice").html("不含税单价")
                        }
                    }else if(caseVal == 3){ // 普通发票
                        priceStr = 'unitPriceInvoice'
                        $(".hasRatePrice").html("开普通发票时的开票单价")
                    }else if(caseVal == 4){ // 不开发票
                        priceStr = 'unitPriceNoinvoice'
                        $(".hasRatePrice").html("不开发票时的单价")
                    }
                }

                let isSuccess = mtFalseList.length > 0 ? false : true
                let showList = isSuccess ? mtBaseList : mtFalseList ;
                if (mtFalseList && mtFalseList.length > 0) {
                    for (var a=0; a<mtFalseList.length;a++) {
                        let unitStr = `<i class="darkGray"></i>`;
                        if (mtFalseList[a].unit && handleNull(mtFalseList[a].unit) != '') {
                            unitStr = mtFalseList[a].unit;
                        }
                        let pprice = mtFalseList[a][priceStr] || 0;
                        pprice = pprice && pprice.toFixed(2) || ''
                        html +=
                            `<tr>
                                <td class="sign" data-name="name">${ handleNull(mtFalseList[a].name) }</td>
                                <td class="sign" data-name="code">${ handleNull(mtFalseList[a].code) } </td>
                                <td class="sign" data-name="model">${ handleNull(mtFalseList[a].model) } </td>
                                <td class="sign" data-name="specifications">${ handleNull(mtFalseList[a].specifications) } </td>
                                <td class="sign" data-name="unit">${ unitStr } </td>
                                <td class="sign" data-name="miniStock">${ handleNull(mtFalseList[a].miniStock) } </td>
                                ${ source == 2 ? `<td class="sign" data-name="${ priceStr }">${ pprice } </td>` :`` }
                                <td class="sign" data-name="desc">${ handleNull(mtFalseList[a].desc)  } </td>
                                <td>
                                      <span class="ty-color-blue btn" data-name="initUpdate">修改</span>
                                      <span class="ty-color-red btn" data-name="initDel">删除</span>
                                      <span class="hd">${ JSON.stringify(mtFalseList[a]) } </span>
                                </td>
                            </tr>`;
                    }
                    $(".mainCon3 .importCon1 table tbody").html(html);
                    $(".mainCon3 .importCon1 .initAll").html(data.importSum);
                    $(".mainCon3 .importCon1 .initWrong").html(data.falseImportSum);
                    $(".mainCon3 .importCon1").show().siblings().hide();
                    $(".mainCon3").show().siblings().hide();
                } else {
                    let customer = $("#curID span:last").data("id")
                    var json = {
                        "pdProductImportListJson": JSON.stringify(mtBaseList),
                        "produdctCatetory": source,
                        "isPurchased": purchasedType,
                        "importSum": data.importSum,
                        "importOption": caseVal,
                        "taxRate": taxRate,
                        'taxInclusive': hasRate,
                        'isSaled': isSaled,
                        'customer': customer
                    };
                    if (source == 2) {
                        let customer = $("#addCommodity").data("customer");
                        json.customer = customer;
                    }
                    saveImportMtList(json);
                }
            } else {
                loading.close() ;
                $('#select_btn_1').val("");
                $('.matListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                bounce_Fixed2.show($("#importantTip"));
            }
            let fileUid = param.fileUid;
            let op = {"type":'fileUid', 'fileUid':fileUid}
            fileDelAjax(op);
        }
    })
}
// creator: 李玉婷，2020-10-13 15:35:58，下一步判断
function allImportMtEnter() {
    var sum = $(".mainCon3 .importCon1 .initAll").html();
    var list = [];
    $(".mainCon3 .importCon1 tbody tr").each(function(){
        var item = JSON.parse($(this).find(".hd").html());
        list.push(item);
    });
    let truePdProductList =$(".importCon1").data("trueProductList");
    list.push(...truePdProductList)
    list = JSON.stringify(list);
    let source = $("#addCommodity").data('source');
    let caseValObj = $("#leadingBefore .fap .fa-dot-circle-o")
    let caseVal = ''
    if(caseValObj.length > 0){
        caseVal = Number(caseValObj.data('val'));
    }
    let taxRate = $("#imRate").val()
    let hasRateObj = $(".fap2 .fa-dot-circle-o");
    let hasRate = ''
    if(hasRateObj.length > 0){
        hasRate = Number(hasRateObj.data('val'));
    }
    let isSaled = $("#importEnteryType").data("type")
    $.ajax({
        url:"../commodityImport/allImportPdEnter.do",
        data:{
            "pdProductImportListJson": list,
            "importSum": sum,
            "produdctCatetory": source,
            "importOption": caseVal,
            "taxRate": taxRate,
            "taxInclusive": hasRate,
            "isSaled": isSaled,
        },
        success:function (data) {
            var errNum = data.falseImportSum;
            $("#importListTj #errNum").html(errNum);
            bounce_Fixed.show($("#importListTj"));
        }
    })
}
// creator: 李玉婷，2020-10-14 15:54:23，下一步确定
function importListTjSure() {
    var list = [];
    var isP = $("#importEnteryType").data("type");
    // var source = $("#importEnteryType").data("source");
    var trueList = $(".importCon1").data("trueProductList");
    $(".importCon1 table tbody tr").each(function(){
        var item = JSON.parse($(this).find(".hd").html());
        list.push(item);
    });
    for (var i in trueList) {
        list.push(trueList[i]);
    }
    var sum = $(".importCon1 .initAll").html();
    list = JSON.stringify(list);
    let source = $("#addCommodity").data('source');
    let caseValObj = $("#leadingBefore .fap .fa-dot-circle-o")
    let caseVal = ''
    if(caseValObj.length > 0){
        caseVal = Number(caseValObj.data('val'));
    }
    let taxRate = $("#imRate").val()
    let hasRateObj = $(".fap2 .fa-dot-circle-o");
    let hasRate = ''
    if(hasRateObj.length > 0){
        hasRate = Number(hasRateObj.data('val'));
    }
    let isSaled = $("#importEnteryType").data("type")
    var json = {
        "pdProductImportListJson": list,
        "produdctCatetory": source,
        "isPurchased": isP,
        "importSum": sum,
        "importOption": caseVal,
        "taxRate": taxRate,
        'taxInclusive': hasRate,
        'isSaled': isSaled
    };
    if (source == 2) {
        json.customer = $("#importEnteryType").data("customer");
    }
    saveImportMtList(json);
}
// creator: 李玉婷，2020-10-22 17:21:52，导入的材料数据都正确
function saveImportMtList(mtData) {
    $.ajax({
        url: "../commodityImport/saveImportPd.do",
        data: mtData,
        success: function (data) {
            $(".mainCon3").show().siblings().hide();
            $(".mainCon3 .importCon2").show().siblings().hide();
            bounce_Fixed.cancel();
            getImportLastList(data);
        }
    });
}
// creator: 李玉婷，2021-09-07 16:23:38，税率切换
function changeInvoiceRate(obj){
    changeExcInvoice(obj); //更改税率后，单价自动换算
    importPriceChange(obj);//提交税率
}
// creator: 李玉婷，2021-09-01 13:29:15，专属发票自动赋值
function changeExcInvoice(obj) {
    let name = obj.attr("name");
    let val = obj.val();
    let price = 0;
    let assignObj = ``;
    if (name == 'taxRate') {

        if (obj.parents("tr").find("input[name='unitPrice']").val()!="" || obj.parents("tr").find("input[name='unitPriceNotax']").val()!="") {
            if (obj.parent("td").siblings(".excInvoice").find("input[name='unitPrice']").prop("disabled")) {
                assignObj = obj.parent("td").siblings(".excInvoice").find("input[name='unitPrice']");
                price = eval(obj.parent("td").siblings(".excInvoice").find("input[name='unitPriceNotax']").val() * (val / 100 + 1));
            } else if (obj.parent("td").siblings(".excInvoice").find("input[name='unitPriceNotax']").prop("disabled")) {
                assignObj = obj.parent("td").siblings(".excInvoice").find("input[name='unitPriceNotax']");
                price = eval(obj.parent("td").siblings(".excInvoice").find("input[name='unitPrice']").val() * (val / 100 + 1));
            }
            if (assignObj != ``) {
                assignObj.val(parseFloat(Number(price).toFixed(10)));
            }
        }
    } else {
        let rate = obj.parent("td").siblings(".excInvoice").find("select[name='taxRate']").val();
        if (name == 'unitPrice') {
            assignObj = obj.parent("td").siblings(".excInvoice").find("input[name='unitPriceNotax']");
            if (rate != "" && val != "") {
                var noTax = eval(val / (1 + rate/100));
                assignObj.prop("disabled", true);
                assignObj.val(parseFloat(Number(noTax).toFixed(10)));
            } else {
                if (val == "") {
                    assignObj.prop("disabled", false);
                    assignObj.val("");
                }
            }
        } else if (name == 'unitPriceNotax') {
            assignObj = obj.parent("td").siblings(".excInvoice").find("input[name='unitPrice']");
            if (rate != "" && val != "") {
                var noTax = eval(val * (1 + rate/100));
                assignObj.prop("disabled", true);
                assignObj.val(parseFloat(Number(noTax).toFixed(10)));
            } else {
                if (val == "") {
                    assignObj.prop("disabled", false);
                    assignObj.val("");
                }
            }
        }
    }
}
// creator: 李玉婷，2020-10-23 18:54:02，改变导入的商品价格
var editPriceObj = null;
function importPriceChange(obj) {
    let trObj = obj.parents("tr");
    var val = obj.val();
    var name = obj.attr('name');
    var info = JSON.parse(trObj.find(".hd").html());
    if (handleNull(info[name]) == val) {
        return false;
    }
    trObj.find("select").each(function () {
        var key = $(this).attr("name");
        info[key] = $(this).val();
    })
    trObj.find("input").each(function () {
        var key = $(this).attr("name");
        info[key] = $(this).val();
    })
    editPriceObj = obj;
    updateMtInfo(info, 2);
}
// creator: 李玉婷，2020-10-22 15:36:26，未完成批量导入列表获取
function getUnFinishImportList(type){
    let source = $("#addCommodity").data('source');
    $.ajax({
        url: "../commodityImport/unfinishedImportPd.do",
        data: {"isPurchased": type, "produdctCatetory": source},
        success: function (data) {
            $("#importEnteryType").data("source", source);
            getImportLastList(data);
        }
    });
}
// creator: 李玉婷，2021-09-07 10:35:32，修改导入的商品初始化
function initImportUpdateMt(thisObj) {
    let type = $("#importEnteryType").data("type"); // 0-未销售过 1-销售过
    let source = $("#importEnteryType").data("source"); // 2- 专属 1- 通用
    var info = JSON.parse(thisObj.siblings(".hd").html());
    if(type == 1){
        $("#updateImportMt .bonceHead span").html("录入已销售过的商品")
    }else{
        $("#updateImportMt .bonceHead span").html("录入已销售过的商品")
    }
    if(source == 2){
        $("#updateImportMt .priceType").hide();
        $("#haveContract").val()
        let param = {
            obj: $("#updateImportMt .contractList"),
            customer: info.customer,
            contract: ""
        }
        getContractBaseList(param);
    }
    $("#updateImportMt .invoiceCategory_i").html($("#addCommodity [name='invoiceCategory']").html());
    $("#updateImportMt .invoiceCategory_i").val(info.invoiceType)
    $(`#updateImportMt .invoice${ info.invoiceType }`).show()
    let caseVal = $("#mould1").data('caseval')
    let hasRate = $("#mould1").data('hasrate')
    if(caseVal == 1){ //
        $("#updateImportMt .invoiceCategory_i").removeAttr('disabled')
    }else{
        $("#updateImportMt .invoiceCategory_i").attr('disabled', 'disabled')
    }

    $("#addInvoiceSpecial2").html($("#addInvoiceSpecial").html());
    $("#addInvoiceSpecial2").val(info.taxRate)
    $("#updateImportMt .contractList").val(info.contractId)
    var unitList = getUnitListData(7);
    var option = '<option value="--">- - 请选择 - - </option>';
    var isPurchased = $("#importEnteryType").data("type");
    $("#updateImportMt").data("obj", thisObj);
    $("#updateImportMt input[name=code]").data("old", info["code"]);
    $("#updateImportMt input").each(function(){
        var key = $(this).attr("name");
        var val = handleNull(info[key]);
        if(val && Number(val) > 0){
            if(key == "unitPrice" || key == 'unitPriceNotax' || key == 'unitPriceNoinvoice' || key == 'unitPriceInvoice' || key == 'unitPriceReference' ){
                val = Number(val).toFixed(2);
            }
        }

        $(this).val(val);
    });
    if(unitList && unitList.length >0){
        for(var i = 0 ; i < unitList.length ; i++){
            var item = unitList[i];
            option += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
        }
    }
    $("#import_update_unitSelect").html(option);
    if(unitList && unitList.length >0){
        if (handleNull(info["unit"]) != '' && info["unit"] != 'null' ) {
            let find = unitList.findIndex(function (item){
                return item['name'] === info["unit"]//返回满足条件的第一个值所在的位置
            })
            if (find > -1) {
                $("#import_update_unitSelect").val(unitList[find]['id']);
            } else {
                let option = `<option value="" selected>${info["unit"]}</option>`;
                $("#import_update_unitSelect").append(option);
            }
        }
    }
    $("#updateImportMt .textMax").html(handleNull(info["desc"]).length + '/100');
    bounce_Fixed.show($("#updateImportMt"));
    $("#filePic2").html("")
    $("#updateImportMt .filePicBox").html(`<span class="inTip">请上传</span>`)
    initUpload($("#filePic2"),'entry');
    let picStr = ``;
    let picList = info.pdCommodityImportAttachList ||
        (info.pdCommodityImportAttachListJson && JSON.parse(info.pdCommodityImportAttachListJson)) ||
        (info.addPdCommodityImportAttachListJson && JSON.parse(info.addPdCommodityImportAttachListJson)) || [];
    if(picList.length > 0){
        picList.forEach(function (pic,index) {
            console.log(pic , index)
            picStr += `
            <div class="imgsthumb">
                <a class="filePic" data-path="${ pic.uplaodPath }" data-ttl="' + file.name + '" onclick="imgViewer($(this))">${ (index + 1) }</a>
                <i class="fa fa-times" fileUid="' + data.fileUid + '" onclick="cancleThis($(this))"></i>
            </div> 
            `
        })
    }

    $("#updateImportMt .filePicBox").html(picStr)
    $("#price1").removeAttr('disabled')
    $("#price2").removeAttr('disabled')
}

// creator: 李玉婷，2020-10-23 16:26:57，修改导入的材料
function updateMtInfo(json, editType) {
    var isPurchased = $("#importEnteryType").data("type");
    var source = $("#importEnteryType").data("source");
    let arr = ['unitPrice','unitPriceNotax','unitPriceInvoice','unitPriceNoinvoice']

    let prams = {
        "isPurchased": isPurchased,
        "produdctCatetory": source,
        "id": json.id,
        "code": json.code,
        "name": json.name,
        "specifications": json.specifications,
        "model": json.model,
        "unit": json.unit,
        "unitId": json.unitId,
        "miniStock": json.miniStock,
        "desc": json.desc,
        "priceDesc": json.priceDesc,
        "invoiceType": json.invoiceType,
        "taxRate": json.taxRate,
        "unitPrice": json.unitPrice,
        "unitPriceNotax": json.unitPriceNotax,
        "unitPriceInvoice": json.unitPriceInvoice,
        "unitPriceNoinvoice": json.unitPriceNoinvoice,
        "unitPriceReference": json.unitPriceReference,
        "addPdCommodityImportAttachListJson": json.pdCommodityImportAttachListJson,
    }
    for (var t=0;t<arr.length ;t++) {
        if(json[arr[t]] != '') {
            prams[arr[t]] = json[arr[t]];
        }
    }
    if (source == 1) {
        if(json.unitPriceReference != '') {
            prams.unitPriceReference = json.unitPriceReference;
        }
    }
    $.ajax({
        url: "../commodityImport/updateImportPd.do",
        data: prams,
        success: function (data) {
            var status = data.status;
            if (status == '1') {
                var buttonState = data["buttonState"];
                if (editType == '1') {
                    var obj = $("#updateImportMt").data("obj");
                    var trObj = obj.parents("tr");
                    // var trData = JSON.parse(obj.siblings(".hd").html());
                    var trData = prams;
                    bounce_Fixed.cancel();
                    trObj.find("td:eq(0)").each(function () {
                        $(this).html(json.code +' &nbsp;&nbsp;'+ handleNull(json.name) +' &nbsp;&nbsp;' + json.specifications +' &nbsp;&nbsp;' + json.model +' &nbsp;&nbsp;' + json.unit);
                    });
                    $("#updateImportMt [need]:visible").each(function () {
                        var key = $(this).attr("name");
                        var val = $(this).val();
                        trData[key] = val;
                        if (key == 'unitId') {
                            trData['unit']= $(this).find("option:selected").text();
                        }
                    });
                    obj.siblings(".hd").html(JSON.stringify(trData));
                    trObj.find("td.sign").each(function () {
                        var n = $(this).data("name")
                        let iVal = trData[n]
                        if(n == "unitPrice" || n == "unitPriceNotax"){
                            iVal = Number(iVal).toFixed(2);
                        }
                        $(this).html(iVal);

                    })
                    trObj.find(".sign2").each(function () {
                        var n = $(this).attr("name")
                        $(this).val(trData[n])
                    })

                } else {
                    editPriceObj.parents("tr").find(".hd").html(JSON.stringify(json));
                }
                if (buttonState == 1) {
                    $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImportJudge()");
                } else {
                    $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
                }
            } else if (status == '-1') {
                var tip = '<p>您录入的商品代号与公司'+ data.name +'商品代号相同。</p>' +
                    '<p>请确认！</p>';
                $("#iknowTip .iknowWord").html(tip);
                bounce_Fixed2.show($("#iknowTip"));
            } else {
                layer.msg('修改失败！');
            }
        }
    });
}
// creator: 李玉婷，2020-10-22 14:46:25，删除导入的材料
function importMtDel() {
    var type = $("#importMtDel").data("type");
    var curObj = $("#importMtDel").data("obj");
    bounce_Fixed.cancel();
    if (type == '1') {//initWrong
        curObj.parents("tr").remove();
        var len = $(".mainCon3 .importCon1 tbody tr").length;
        $(".mainCon3 .importCon1 .initWrong").html(len);
    } else {
        var info = JSON.parse(curObj.siblings(".hd").html());
        var source = $("#importEnteryType").data("source");
        var isPurchased = $("#importEnteryType").data("type");
        $.ajax({
            url:"../commodityImport/deleteImportPd.do",
            data:{ "id": info.id , "isPurchased":isPurchased, "produdctCatetory":source },
            success:function (data) {
                var status = data["status"];
                var buttonState = data["buttonState"];
                if( status == 1 ){
                    layer.msg("操作成功！");
                    curObj.parents("tr").remove();
                    var len = $(".mainCon3 .importCon2 tbody tr").length;
                    $(".mainCon3 .importCon2 .inabledSum").html(len);
                    if (buttonState == 1) {
                        $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImportJudge()");
                    } else {
                        $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
                    }
                }else{
                    layer.msg("编辑失败，请稍后重试！");
                }
            }
        })
    }
}
function saveImportJudge(){
    var isP = $("#importBtn").data("type");;
    let source = $("#addCommodity").data('source');
    if (source == 2) {
        let temp = 0;
        $(".mainCon3 .importCon2 tbody tr").each(function () {
            let invoiceType = $(this).find("select[name='invoiceType']").val();
            if (invoiceType == 1) {
                $(this).find("select[name='taxRate']").val() == ''? temp++: '';
                $(this).find("input[name='unitPrice']").val() == ''? temp++: '';
                $(this).find("input[name='unitPriceNotax']").val() == ''? temp++: '';
            } else if(invoiceType == 2){
                $(this).find("input[name='unitPriceInvoice']").val() == ''? temp++: '';
            } else if(invoiceType == 4){
                $(this).find("input[name='unitPriceNoinvoice']").val() == ''? temp++: '';
            }
        });
        if (temp > 0) {
            $("#unfilledTip_ms").html('还有必填项尚未填写!');
            bounce_Fixed2.show($("#unfilledTip"));
            return false;
        }
    }
    let prams = {
        'isPurchased': isP,//1是采购过 0是未采购过
        'produdctCatetory': source
    };
    $.ajax({
        "url":"../commodityImport/finishImportPdEnter.do",
        "data":prams,
        success:function(res) {
            var tip = "";
            var count = res.count;
            if (isP == '1') {
                if (count == 0) {
                    tip = "库管员将收到<span class='ty-color-red'>须填写</span>初始库存的提示。";
                } else {
                    tip = "<p>1、无法保存到系统的"+count+"行商品将被放弃。</p><p>2、库管员<span class='ty-color-red'>无法填写</span>这些商品的当前库存数量。</p>";
                }
            } else if (isP == '0') {
                if (count == 0) {
                    tip = "库管员无法填写这些商品的当前库存数量。";
                } else {
                    tip = "<p>1、无法保存到系统的"+count+"行商品将被放弃。</p><p>2、库管员无法填写这些商品的当前库存数量。</p>";
                }
            }
            $("#importListSave .saveTip").html(tip);
            bounce_Fixed.show($("#importListSave"));
        }
    })
}
// creator: 李玉婷，2020-11-11 13:57:32，保存
function saveImport(type, cancelSource){//1保存，0放弃
    var isP = $("#importBtn").data("type");;
    let source = $("#addCommodity").data('source');
    let customer = '';
    let prams = {
        'type': type,
        'isPurchased': isP,
        'produdctCatetory': source
    };
    if (source == 2) {
        customer = $("#importEnteryType").data("customer");
        prams.customer = customer;
    }
    $.ajax({
        "url":"../commodityImport/finishImportPd.do",
        "data":prams,
        success:function(res) {
            var state = res.status;
            if (state == '1') {
                if (type == 1 || type == '1') {
                    bounce_Fixed.cancel();
                    $("#curID span:last").click();
                } else {
                    bounce_Fixed.cancel();
                    $(".mainCon1").show().siblings().hide();
                    $("#importBtn").click();
                }
            } else {
                layer.msg("操作失败！")
            }
        }
    })
}


// creator: 李玉婷，2022-02-24 15:50:40，获取关联记录
function relatedRecord () {
    var info = JSON.parse($("#inforStorage").html());
    let id = info.id;
    $("#relateRecord tbody").children(":gt(0)").remove();
    $.ajax({
        "url": '../product/getProductGuanLianList.do',
        "data": { "id": id  } ,
        success:function(res) {
            let list = res.data || [];
            let curStaStr = ``;
            if(list && list.length >0){
                let str = ``;
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    str += `<tr>
                             <td>${i===0 ? "初次关联":"第"+ i +"次修改后"}</td>
                             <td>${item.name} ${item.innerSn}</td>
                             <td>${i===0 ?item.createName:item.correlaterName} ${i===0 ?new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss"):new Date(item.correlateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                }
                $("#relateRecord tbody").append(str);
                let n = list.length
                let last = list[n-1]
                curStaStr = `${n===1 ?last.createName:last.correlaterName} ${n===1 ?new Date(last.createDate).format("yyyy-MM-dd hh:mm:ss"):new Date(last.correlateDate).format("yyyy-MM-dd hh:mm:ss")}</span>`
            }
           $(".modPlace").html(handleNull(info.innerSn) + ' ' + handleNull(info.productName));
            $(".operationPlace").html(curStaStr);
            bounce_Fixed3.show($("#relateRecord"));
        }
    });
}
//1=由有权限创建商品的职工修改,2=由有产品操作权限的职工修改
function getEditState() {
    $.ajax({
        "url": "../popedom/getCurrentItem.do",
        "data": {"code": 'commodityProduct'},
        success: function (res) {
            let model = res.data.pdModelSettings;
            editType = model.reviseModel;
        }
    });
}
// creator: 李玉婷，2020-09-10 11:59:43，一键清空
function clearPre(obj) {
    obj.prev("input").val("");
}
// create：hxz 2021-1-21 字数超出限制
function setWordsNum(thisObj , maxLen){
    let curtxt = thisObj.val()
    if(curtxt.length > maxLen){
        thisObj.val(curtxt.substr(0,maxLen));
        layer.msg(`最多录入${maxLen}个字符`);
    }
    thisObj.parent().find(".lenTip").html( thisObj.val().length + " / " + maxLen);
}
// creator: 李玉婷，2021-06-29 11:44:53，控制输入3位小数
// creator: 李玉婷，2021-06-29 13:54:49，保留3位小数
function testNumSize3(obj){
    obj.value = obj.value.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符
    obj.value = obj.value.replace(/^\./g,"");  //验证第一个字符是数字而不是.
    obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/,'$1$2.$3');//只能输入两个小数
    if(obj.value.indexOf(".")< 0 && obj.value !=""){//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        obj.value= parseFloat(obj.value);
    }
}
// creator: 李玉婷，2019-09-19 10:14:23，自定义标签确定
function addNewLable(){
    var val = $("#defLable").val();
    var html =
        '<li>' +
        '<span class="sale_ttl1">' + val + '：</span>' +
        '<span class="gap"><input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" data-org="" require/></span>'+
        '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
        '</li>';
    $(".otherContact").append(html);
    bounce_Fixed5.cancel();
}
// create:hxz 2023-06-10 商品的两项开票资料 编辑
function twoEdit(thisObj) {
    bounce_Fixed.cancel()
    const typeA = thisObj.data('type'); // huo- 货物或应税劳务、服务名称 ; model - 规格型号
    const sta = thisObj.data('sta'); // editInfo- 修改基本信息 ; info - 查看详情的
    let type = typeA === 'huo' ? 1 : 2
    let info = $("#seeCommodity").data('info')
    info.typeA = typeA
    info.sta = sta
    let merchandise = info.id
    $("#twoEdit").data("info", info)
    $("#foo1").html(info.outerSn)
    $("#foo2").html(info.outerName)
    $("#foo3").html(info.specifications)
    $("#foo4").html(info.model)
    $.ajax({
        'url':'../product/getPdMerchandiseInvoice.do',
        'data':{ merchandise: merchandise, type: type },
        success:function(res){
            const data = res.data
            if(data && data.id){
                $(".yesEdit").show()
                $(".noEdit").hide()
                $(".huoVal").html($("#nameInvoice").html())
                $(".modelVal").html($("#glInvoice").html())
            }else{
                $(".yesEdit").hide()
                $(".noEdit").show()
            }
            bounce.show($("#twoEdit"))
            $(`#twoEdit .fa`).attr("class", "fa fa-circle-o");
            $(`#twoEdit input`).val("");
            $(`#twoEdit .${ typeA }`).show().siblings().hide()
        }
    })
}

function twoEditOk() {
    const info = $("#twoEdit").data("info")
    const selectFa = $(`#twoEdit .${ info.typeA }`).find(".fa-dot-circle-o")
    if(selectFa.length === 0){
        layer.msg('需将此数据修改为哪一项？请选择')
        return false
    }
    let userDefined = ''
    const funBtn = selectFa.parent(".funBtn")
    if(funBtn.data('type') === 'zi'){
        userDefined = funBtn.siblings('input').val()
    }
    let data = {
        merchandise: info.id,
        type: info.typeA === 'huo' ? 1 : 2,
        pattern: selectFa.data('val'),
        userDefined: userDefined ,
        memo:''
    }
    $.ajax({
        'url':'../product/editPdMerchandiseInvoice.do',
        "data": data,
        success:function(res){
            if(res.success === 1){
                layer.msg('操作成功')
                if(info.sta === 'editInfo'){
                    bounce.cancel()
                    bounce_Fixed.show($('#editCommodityBase'));
                    let idStr = info.typeA === 'huo' ? 'eName': 'eModel'
                    let selectObj = $(`#${ idStr }`)
                    selectObj.parent().find('.fa').attr('class' , 'fa fa-circle-o')
                    selectObj.find('.fa').attr('class' , 'fa fa-dot-circle-o')

                }else{
                    bounce.show($('#seeCommodity'));
                    let txt = formatPatten(data,info)
                    if(info.typeA === 'huo'){
                        $("#nameInvoice").html(txt || '--')
                    }else{
                        $("#glInvoice").html(txt || '--')
                    }
                }

            }else{
                layer.msg('操作失败')
            }
        }
    })

}
function twoEditCancel() {
    const info = $("#twoEdit").data("info")
    if(info.sta === 'editInfo'){
        bounce.cancel()
        bounce_Fixed.show($('#editCommodityBase'));
    }else{
        bounce.show($('#seeCommodity'));
    }
}
function formatPatten(data, info) {
    let str = ''
    if (!data){
        return false
    }
    if(data.type === 1){ // huowu
        switch (Number(data.pattern)){
            case 1: // 采用商品代号的当前数据
                str = info.outerSn
                break;
            case 2: // 采用商品名称的当前数据
                str = info.outerName
                break;
            case 3: // 上述代号与名称都使用，代号在前
                str = info.outerSn + ' ' + info.outerName
                break;
            case 4: // 上述代号与名称都使用，名称在前
                str = info.outerName + ' ' + info.outerSn
                break;
            case 5: // 自定义
                str = data.userDefined
                break;
        }
    }else{
        switch (Number(data.pattern)){
            case 1: // 发票上的“规格型号”为空，无需填写
                str = ''
                break;
            case 2: // 采用规格的当前数据
                str = info.specifications
                break;
            case 3: // 采用型号的当前数据
                str = info.model
                break;
            case 4: // 上述规格与型号都使用，规格在前
                str = info.specifications + ' ' + info.model
                break;
            case 5: // 上述规格与型号都使用，规格在前
                str = info.model + ' ' + info.specifications
                break;
            case 6: // 自定义
                str = data.userDefined
                break;
        }
    }
    return str
}
// create:hxz 2023-06-10 商品的两项开票资料 编辑记录
function twoEditLog(thisObj) {
    const typeA = thisObj.data('type'); // huo- 货物或应税劳务、服务名称 ; model - 规格型号
    let type = typeA === 'huo' ? 1 : 2
    $(".ttlType").html(typeA === 'huo' ? '货物或应税劳务、服务名称' : '规格型号')
    let info = $("#seeCommodity").data('info')
    info.typeA = typeA
    let merchandise = info.id
    $.ajax({
        "url":"../product/getPdMerchandiseInvoiceRecordList.do",
        "data": { merchandise: merchandise, type: type },
        success:function(res){
            bounce.show($("#twoEditLog"))
            const list = res.data.list || []
            if(list.length > 0){
                let str = ``
                let last = { 'numStr':'', 'timeStr':""}
                list.forEach( (item, index) => {
                    let numStr = index === 0 ? '首' : `第${ index+1 }`
                    let timeStr = index === 0 ?
                        `${ item.createName } ${ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")  }`
                        : `${ item.updateName } ${ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")}`
                    last.numStr = numStr
                    last.timeStr = timeStr
                    str += `
                <tr>
                <td>${ numStr }次编辑后</td>
                <td>${ formatPatten(item, info) }</td>
                <td> ${ timeStr } </td>
                </tr>
                `
                })
                $("#logTab").html(str)
                $("#twoEditLog .lastMsg").html(`当前资料为${ last.numStr }次编辑的结果，操作者：${ last.timeStr }`)
                $("#twoEditLog .noEdit").hide()
                $("#twoEditLog .havEdit").show()
            }else{
                $("#twoEditLog .noEdit").show()
                $("#twoEditLog .havEdit").hide()
            }



        }
    })

}
function toggleTwo(thisObj) {
    let type = thisObj.data("type")
    thisObj.parents(".typeCC").find(".fa").attr("class","fa fa-circle-o")
    thisObj.find('.fa').attr("class","fa fa-dot-circle-o")
    let inputObj = thisObj.parents(".selectItems").find("input")
    if(type === 'zi'){
        inputObj.removeAttr('disabled')
    }else{
        inputObj.attr('disabled','disabled')
        inputObj.val("")
    }
}
// create:hxz 2023-06-10  复制
function copyFun(thisObj) {
    // let txtObj = thisObj.parent().find(".codeTxt")
    // var str = txtObj.html()
    // $("#copyInput").val(str).select();
    // document.execCommand('copy');
    // document.execCommand("Copy");
    layer.msg('已复制到剪切板')
}
// create:hxz 2023-09-20 查看要货计划
function seeOrderCancel(thisObj){
    let type = $("#seeOrder").data('type')
    if(type === 'base'){
        bounce_Fixed.show($('#editCommodityBase'))
    }else if(type === 'price'){
        bounce_Fixed.show($('#editPrice'))
    }
}
function seeOrder(thisObj){
    let type = thisObj.data('type');
    let id = $('#seeCommodity').data('id');
    $("#seeOrder").data('type',type)
    $.ajax({
        'url':'../sale/occItems.do',
        'data':{ 'id': id },
        'method':'GET',
        success:function(res){
            const list = res.data || [];
            let str =  ``
            list.forEach(item => {
                str += `
                <tr>
                    <td>${ new Date(item.delivery_date).format('yyyy-MM-dd hh:mm:ss') }</td>
                    <td>${ item.unit }</td>
                    <td>${ item.amount }</td>
                    <td>${ item.out_fact || 0 }</td>
                    <td>${ item.sn }</td>
                    <td>${ new Date(item.create_date).format('yyyy-MM-dd ') }</td>
                    <td>${ item.customer_name }</td>
                </tr>
                `
            })
            $("#seeOrder table tr:gt(0)").remove()
            $("#seeOrder table").append(str)
            $("#seeOrder .num").append(list.length)
            bounce_Fixed.show($("#seeOrder"))
        }
    })
}

// create:hxz 2023-09-20 包含本商品的未完结订单
function getOccOrder(thisObj){
    let type = thisObj.data('type');
    let id = $('#seeCommodity').data('id');
    $("#seeOrder").data('type',type)
    $.ajax({
        'url':'../sale/getOccOrder.do',
        'data':{ 'id': id },
        success:function(res){
            const list = res.data || [];
            let str =  ``
            list.forEach(item => {
                str += `
                <tr>
                    <td>${ new Date(item.delivery_date).format('yyyy-MM-dd hh:mm:ss') }</td>
                    <td>${ item.unit || '' }</td>
                    <td>${ item.amount || 0 }</td>
                    <td>${ item.out_fact || 0 }</td>
                    <td>${ item.sn || '' }</td>
                    <td>${ new Date(item.sign_date).format('yyyy-MM-dd hh:mm:ss') }</td>
                    <td>${ item.customer_name || '' }</td>
                </tr>
                `
            })
            $("#seeOrder table tr:gt(0)").remove()
            $("#seeOrder table").append(str)
            $("#seeOrder .num").append(list.length)
            bounce_Fixed.show($("#seeOrder") )

        }
    })
}

function toggefirstTime(type, num) {
    $("#firstTimeCC" + num).data('type', type)
    $(".firstTimeCCon").find('.fa').attr('class', 'fa fa-circle-o')
    $(".cc" + type).find('.fa').attr('class', 'fa fa-dot-circle-o')
    if(type === 1){
        $(".firMonthcon").show()
        $('#firMonth'+ num).val('')
    }else{
        $(".firMonthcon").hide()
    }
}

function toggefirstTimeN(num) {
    $('#firMonth'+ num).val('')
    $(".ccN").find('.fa').attr('class', 'fa fa-dot-circle-o')
}
function nullNextN() {
    $(".ccN").find('.fa').attr('class', 'fa fa-circle-o')
}

laydate.render({elem: '#date1'});
laydate.render({elem: '#firMonth', type:'month', format:'yyyyMM'});
laydate.render({elem: '#firMonth2', type:'month', format:'yyyyMM'});

