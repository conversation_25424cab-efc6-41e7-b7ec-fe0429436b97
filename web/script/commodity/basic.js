var bounce_Fixed4 = new Bounce(".bounce_Fixed4");
var bounce_Fixed5 = new Bounce(".bounce_Fixed5");
bounce_Fixed4.show($("#chooseCusContact"));
bounce_Fixed4.cancel();
bounce_Fixed5.show($("#contactSeeDetail"));
bounce_Fixed5.cancel();
// updator：王静，2017-08-22 09:11:09
$(function(){
    $("#tipcontractGoods").on('click','.fa', function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    });
    $(".placeList").on('click','.fa', function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    });

	// 获得基本信息列表
    getCommodityList(1, 20,"","全部","");

    $(".chooseCusCon").click(function () {
        let target = $(this).data("target");
        $("#target").val(target)
        bounce_Fixed4.show( $("#chooseCusContact") );
        let source = $("#chooseDelivery").data("source");
        var cusId = ''
        if (source === 'new') {
            cusId = $("#firstLevelName").data("categoryid");
        } else {
            var info = JSON.parse($("#inforStorage").html());
            cusId = info.customer_;
        }
        getCusContactList(cusId);
    });
    $("#chooseCusContact").on("click", '.fa', function () {
        $("#chooseCusContact").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).attr("class", "fa fa-dot-circle-o")
    })
    // 上传的合同删除
    $("#newContractInfo").on("click", '.fa-times', function () {
        let info = JSON.parse($(this).siblings(".hd").html())
        let fileUid = info.fileUid
        cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
        if ($(this).parents(".fileCon1").length > 0 && info.id) {
            let fileItem = $(this).parent(".fileIm").html()
            $("#newContractInfo .deleteFile").append( `<span class="fileItem">${fileItem}</span>`)
        }
        $(this).parent(".fileIm").remove();
    })

});

// creator: hxz，2023-06-09  解封商品代号 toggle
function toggleRepeat(thisObj) {
    const faObj = thisObj.find(".fa")
    console.log('23')
    let isSelect = faObj.hasClass('fa-circle-o')
    if(isSelect){
        faObj.attr("class", "fa fa-dot-circle-o")
    }else{
        faObj.attr("class", "fa fa-circle-o")
    }
}

// creator: 李玉婷，2020-03-18 14:51:41，获取专属商品列表
function getCommodityList(currPage,pageSize,category,categoryName,keyword){
    $("#ye").html("");
    $(".indexInput").show();
    $(".between").height(0);
    $("#kindsTree li").remove();
    $("#suspendCommodyNum").html("0");
    $("#classifiedGoods tbody").html("");
    $(".inSales").show().siblings().hide();
    $(".mainCon1").show().siblings().hide();
    if (categoryName == "" || categoryName == "全部"){
        $(".suspendBtn").show();
        $(".left-bottom").hide();
        $("#firstLevelName").html("全部").data("categoryid",category);
    } else{
        $(".suspendBtn").hide();
        $(".left-bottom").show();
        $("#firstLevelName").html(categoryName).data("categoryid",category);
    }
    $(".indexInput .faceul").show();
    if (categoryName == "" || categoryName == "全部" || categoryName == "待分类"){
        $(".indexInput .faceul .faceul1").addClass("disabled");
        $(".indexInput .faceul button").prop("disabled",true);
        if (categoryName == "待分类" && category== 0) {
            $(".indexInput .faceul").hide();
        }
    }else{
        $(".indexInput .faceul button").prop("disabled",false);
        $(".indexInput .faceul .faceul1").removeClass("disabled");
    }
    $.ajax({
        url: "../product/getZSProductListByCategory.do",
        data: {
            "pageSize":pageSize,
            "currPage":currPage,
            "customerId": category,
            "param": keyword
        },
        success: function (data) {
            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            var count = 0;
            var jsonStr = {
                "category": category,
                "categoryName": categoryName,
                "param": keyword,
            } ;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#ye") , curr ,  totalPage , "commodity", jsonStr );
            var list = data.data;
            var customers = data.customers;
            if(categoryName == "" || categoryName == '全部'){
                $("#suspendCommodyNum").html(data.suspendProductNum);
            }
            if (customers && customers.length > 0){
                var menu = '';
                for(var m=0;m<customers.length;m++){
                    count += customers[m].num;
                    if (categoryName != '待分类'){
                        menu +=
                            '<li class="faceul1">' +
                            '    <a>' +
                            '        <span data-stop="'+customers[m].isSuspend+'" data-source="2" data-delivery="'+ customers[m].deliveryType +'" onclick="kindBtn($(this))">'+ customers[m].fullName +'（'+ customers[m].num +'种）</span>' +
                            '        <div class="hd">' +
                            '            <span class="kindId">'+ JSON.stringify(customers[m])+'</span>' +
                            '        </div>' +
                            '    </a>' +
                            '</li>';
                    }
                }
                $("#kindsTree").html(menu);
            }
            $("#firstLevelAmount").html(count);
            if (list && list.length > 0){
                var html = '';
                for(var t=0;t<list.length;t++){
                    var info = JSON.stringify(list[t]);
                    var operaBtn = '';
                    if (list[t].type == 1){
                        operaBtn =
                            '         <span class="ty-color-blue tb-btn-sm" onclick="seeCommodityDetails($(this),1, false, false)">查看</span>' +
                            '         <span class="ty-color-blue tb-btn-sm" onclick="curOrders($(this))">当前订购信息</span>';
                    } else if (list[t].type == 2){
                        operaBtn +=
                            '         <span class="ty-color-blue tb-btn-sm" onclick="seeCommodityDetails($(this),2, false,true)">查看</span>' +
                            '         <span class="ty-color-blue tb-btn-sm" onclick="curOrders($(this))">当前订购信息</span>'+
                            '         <span class="ty-color-blue tb-btn-sm" onclick="suspendSale($(this),0)">暂停销售</span>' +
                            '         <span class="ty-color-red tb-btn-sm" onclick="deleteCommodity($(this),0)">删除</span>';
                    }
                    html+=
                        ' <tr>' +
                        '     <td>'+ handleNull(list[t].outerSn)+'</td>' +
                        '     <td>'+ handleNull(list[t].outerName)+'</td>' +
                        '     <td>'+ handleNull(list[t].model)+'</td>' +
                        '     <td>'+ handleNull(list[t].specifications)+'</td>' +
                        '     <td>'+ handleNull(list[t].unit)+'</td>' +
                        `      <td>${ list[t].name || '' }</td>`+
                        '     <td>'+ parseFloat(Number(list[t].minimumStock || "0").toFixed(4)) +'</td>' +
                        '     <td>'+ parseFloat(Number(list[t].currentStock || "0").toFixed(4)) +'</td>' +
                        '     <td class="createInfo">'+ list[t].createName + '<br/>'+' &nbsp; ' + new Date(list[t].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '     <td>' + operaBtn +'<span class="hd">'+info+'</span>'+
                        '     </td>' +
                        ' </tr>';
                }
                $("#classifiedGoods tbody").html(html);
            }

            var conHeight = $(".bigContainer").height() ;
            conHeight++;
            $(".between").height(conHeight);
        }
    })
}

// creator: 李玉婷，2020-03-17 16:05:56，专属商品录入确定
function addZsCommoditySureBefore() {
    let code = $("#outerSnAdd").val()
    $.ajax({
        'url':'../product/getProductHistoryByCode.do',
        'data':{ code: code },
        success:function(res){
            let list = res.data.list || [] ; // 使用过该代号的商品
            let list2 = res.data.list || []; // 在用的列表
            if(list.length > 0 || list2.length > 0){
                if(list.length > 0){
                    $(".main80").show()
                    $(".main99").hide()
                    $(".main80 .fa").attr("class", "fa fa-circle-o")
                    $("#codeRepeatTip .repeatCode").html(code)
                    $("#codeRepeatTip").data('list', list)
                    bounce_Fixed.show($("#codeRepeatTip"));
                }else if(list2.length > 0){
                    layer.msg("该商品代号已存在！")
                }
            }else{
                addZsCommoditySure()
            }
        }
    })

}
// creator: 李玉婷，2020-03-17 16:05:56，专属商品录入确定
function addZsCommoditySure() {
    var reqTrue = 0; //invoiceCategory  '发票类型:1-增值税专用票,2-增值税普通票,3-其他票,4-不开票'
    $("#addCommodity input[require]:visible").each(function () {
        if ($(this).val() == "") reqTrue++;
    });
    $("#addCommodity select[require]:visible").each(function () {
        if ($(this).val() == "") reqTrue++;
    });
    if (reqTrue > 0) {
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }
    else {
        var isSaled = $("#addCommodity").data('isSaled');
        var goodsData = {
            'customer.id': $("#firstLevelName").data("categoryid"),
            'isSaled': isSaled
        };
        $("#addCommodity input:visible").each(function () {
            var name = $(this).attr('name');
            if (name) {
                goodsData[name] = $(this).val();
            }
        });
        $("#addCommodity select:visible").each(function () {
            var name = $(this).attr('name');
            if (name) {
                if(name == 'unitPriceNotax' || name == 'unitPrice') {
                    goodsData[name] = Number($(this).val());
                } else {
                    goodsData[name] = $(this).val();
                }
            }
        });
        if($("#addCommodity input[name='contractId']").val() == ""){
            delete goodsData["contractId"];
        }
        if($("#addCommodity [name='invoiceCategory']").val() == ""){
            delete goodsData["invoiceCategory"];
        }
        goodsData['unit'] = $("#add_unitName").val();
        //图片 视频
        var media = [];
        $("#addCommodity .imgsthumb").each(function () {
            var cur = $(this).find(".filePic");
            var item = {
                'uplaodPath': cur.data('path'),
                'title':cur.data('ttl'),
                'type': cur.data('type')
            }
            media.push(item);
        });
        goodsData.commodityMediaList = JSON.stringify(media);
        if ($(".deliveryPlaceData").html() !== "") {
            goodsData.addressListString = $(".deliveryPlaceData").siblings(".hd").html()
        }
        goodsData.exclusiveTime = $("#firstTimeCC").data('type')
        goodsData.exclusiveMonth = $("#firMonth").val() || (new Date().format('yyyy'))
        $.ajax({
            url: "../product/addZSProduct.do",
            data: goodsData,
            success: function (data) {
                if (data.code == 200) {
                    bounce.cancel();
                    var catId = $("#firstLevelName").data("categoryid");
                    var catName = $("#firstLevelName").html();
                    var key = $("#searchKeyBase").val();
                    getCommodityList(1, 20,catId,catName,key);
                } else {
                    var msg = data.msg;// 商品代号已存在
                    layer.msg(msg)
                }
            }
        })
    }
}

// creator: hxz 查看 使用过该代号的商品
function getOutSnRepeat(thisObj) {
    let list =  $("#codeRepeatTip").data('list')
    $(".main80").hide()
    $(".main99").show()
    let str = ``
    list.forEach(item => {
        str += `
                <tr>
                    <td>${ item.outerSn } / ${ item.outerName }</td>
                    <td>
                    ${item.createName || ''} ${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }
                                       -
                    ${item.updateName || ''} ${ new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss') }
                    </td>
                </tr>
                `
    })
    $("#repeat2").html(str);

}
function closeRepeat() {
    $(".main80").show()
    $(".main99").hide()
}
function repeatCodeOk() {
    let faObj = $("#codeRepeatTip .fa")
    let isSelect = faObj.hasClass('fa-dot-circle-o');
    if(isSelect){
        bounce_Fixed.cancel();
        addZsCommoditySure()
    }else{
        layer.msg(" 请先勾选\"解封商品代号\"")
    }


}
// creator: 李玉婷，2020-04-01 15:18:20，获取暂停销售的商品数据
function getSuspendList(currPage,pageSize,keyword) {
    $("#ye").html('');
    $("#firstLevelAmount").html('0');
    $("#suspendZSList tbody").html('');
    $.ajax({
        "url": "../product/suspendZSProductList.do",
        "data": {
            "pageSize":pageSize,
            "currPage":currPage,
            "param": keyword
        },
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = {
                "param": keyword,
            } ;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#ye") , curr ,  totalPage , "inSuspendTY", jsonStr );
            $("#firstLevelAmount").html(res.suspendProductNum);
            var spList = res.data;
            if (spList && spList.length > 0){
                var html = '';
                for(var t=0;t<spList.length;t++){
                    var info = JSON.stringify(spList[t]);
                    html+=
                        ' <tr>' +
                        '     <td>'+ handleNull(spList[t].outerSn) +'</td>' +
                        '     <td>'+ handleNull(spList[t].outerName) +'</td>' +
                        '     <td>'+ handleNull(spList[t].model) +'</td>' +
                        '     <td>'+ handleNull(spList[t].specifications) +'</td>' +
                        '     <td>'+ handleNull(spList[t].unit) +'</td>' +
                        '     <td>'+ parseFloat(Number(spList[t].minimumStock || "0").toFixed(4)) +'</td>' +
                        '     <td>'+ parseFloat(Number(spList[t].currentStock || "0").toFixed(4)) +'</td>' +
                        '     <td class="createInfo">'+ spList[t].updateName + ' &nbsp; ' + new Date(spList[t].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '     <td>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="seeCommodityDetails($(this),' + info.type + ', true, false)">查看</span>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="curOrders($(this))">当前订购信息</span>' +
                        '         <span class="ty-color-blue tb-btn-sm" onclick="suspendSale($(this),1)">恢复销售</span>' +
                        '         <span class="ty-color-red tb-btn-sm" onclick="deleteCommodity($(this),1)">删除</span>' +
                        '         <span class="hd">'+ info +'</span>' +
                        '     </td>' +
                        ' </tr>';
                }
                $("#suspendZSList tbody").html(html);
            }
            var conHeight = $(".bigContainer").height() ;
            conHeight++;
            $(".between").height(conHeight);
        }
    });
}

// creator: 李玉婷，2021-05-21 10:46:35，此商品是否包含于与客户已签订的合同中
function contractSelect(obj) {
    var val = obj.val();
    let contractList = obj.parents(".contractSelectC").find('.contractList')
    let nodeBtn = obj.parents(".contractSelectC").find('.nodeBtn')
    if (val == 1) {
        nodeBtn.show();
        contractList.prop("disabled",false);
    } else {
        nodeBtn.hide();
        contractList.val("").prop("disabled",true);
    }
}
// creator: 李玉婷，2021-05-21 11:08:35，发票选择
function invoiceTypeSelect(obj) {
    var val = obj.val();
    // obj.parents(".modItem-m").siblings(".priceType").hide();
    // obj.parents(".modItem-m").siblings(".invoice" + val).show();
    $(`.addPrice .priceType`).hide()
    $(`.addPrice .invoice${ val }`).show()
}
// creator: 李玉婷，2021-05-26 21:46:29，获取合同列表
function getContractBaseList(param){
    $.ajax({
        "url": "../sales/listContractBase.do",
        "data": {
            customer: param.customer,
            type: 1
        },
        success: function (res) {
            let contracts = res.data.contractBaseList || [];
            let options = `<option value="">请选择</option>`;
            for (let item of contracts) {
                options += `<option value="${item.id}">${item.sn}</option>`;
            }
            param["obj"].html(options);
            param["obj"].val(param.contract);
        }
    });
}
// create:hxz 2023-0920 是否包含合同
function contractSelect2(obj, contractId) {
    var val = obj.val();
    let contractList = $("#edit_contractList")
    let addBtn = $("#newContract")
    if (val == 1) {
        addBtn.show();
        contractList.prop("disabled",false);
    } else {
        addBtn.hide();
        contractList.val("").prop("disabled",true);
    }
}
// creater : 2021-5-24 hxz 渲染合同里商品的列表
function setContactGS(list, boolSet, isHasCheck) {
    let str = '';
    let cstr = '';
    if(boolSet){
        $("#tipcontractGoods .selectTd").show()
        $("#tipcontractGoods .addOrCancel").show()
        $("#tipcontractGoods .cScanc").hide()
        $("#tipcontractGoods .countStr").show()
    } else {
        $("#tipcontractGoods .selectTd").hide()
        $("#tipcontractGoods .addOrCancel").hide()
        $("#tipcontractGoods .cScanc").show()
        $("#tipcontractGoods .countStr").hide()
    }
    list = list || [];
    list.forEach(function (im) {
        let isCheckedStr = im.isChecked && isHasCheck?'checked':''
        let handleStr = boolSet?`<td><div class="ty-checkbox"><input type="checkbox" name="goods" id="good_${im.id}" ${isCheckedStr}/><label for="good_${im.id}"></label></div></td>`:''
        str += `<tr data-id="${im.id}">
                    ${handleStr}
                    <td>${im.outerSn}</td>
                    <td>${im.outerName}</td>
                    <td>${im.model}</td>
                    <td>${im.specifications}</td>
                    <td>${im.unit}</td>
                    <td>
                        <span class="link-blue" onclick="includeGoodContract($(this))">${im.contractNum || 0}个</span>
                        <span class="hd">${JSON.stringify(im)}</span>
                    </td>
                </tr>`
    })
    $("#tipcontractGoods table tbody").html(str);
    bounce_Fixed3.show($("#tipcontractGoods"));
}

// creator: 张旭博，2024-06-28 01:28:46， 包含某商品的合同
function includeGoodContract(selector) {
    let goodInfo = JSON.parse(selector.siblings(".hd").html())
    let cusInfo = $("#newContractInfo").data("cusInfo")
    let goodArr = [goodInfo.outerSn, goodInfo.outerName, goodInfo.model, goodInfo.specifications, goodInfo.unit]
    let cusArr = [cusInfo.code, cusInfo.fullName]
    $("#includeGoodContract .goodInfo").html([...goodArr].filter(item => item || item === 0).join(" / "));
    $("#includeGoodContract .cusInfo").html([...cusArr].filter(item => item || item === 0).join(" / "));
    bounce_Fixed4.show($("#includeGoodContract"));
    $.ajax({
        url: $.webRoot + '/sales/listContractByCommodity.do',
        data: {
            id: goodInfo.id
        }
    }).then(res => {
        let data = res.data
        let list = data.list
        let str = ''
        for (let item of list) {
            str += `<tr>
                    <td>${item.sn}</td>
                    <td>${item.commodityCount}</td>
                    <td>${moment(item.validStart).format("YYYY-MM-DD")}至${moment(item.validEnd).format("YYYY-MM-DD")}</td>
                    <td>${item.signTime?(moment(item.signTime).format("YYYY-MM-DD")):''}</td>
                    <td>${item.createName} ${moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                </tr>`
        }
        $("#includeGoodContract tbody").html(str)
    })
}

// creater : 2021-5-24 hxz 合同商品 操作 确定
function addOrCancelOk() {
    let origin = $("#tipcontractGoods").data("origin");
    let productListZS = $("#newContractInfo").data('productListZS') || []
    let productListTY = $("#newContractInfo").data('productListTY') || []
    if (origin === 'addZsGs') {
        productListZS.map(item => item.isChecked = false)
    }
    if (origin === 'addTyGs') {
        productListTY.map(item => item.isChecked = false)
    }
    let editProductZS = []
    let editProductTY = []
    let listEdit = []
    $("#tipcontractGoods input:checkbox:checked").each(function(){
        let id = $(this).parents("tr").data("id")
        if (origin === 'addZsGs') {
            productListZS.map(item => {
                if (item.id === id) {
                    item.isChecked = true
                }
            })
        }
        if (origin === 'addTyGs') {
            productListTY.map(item => {
                if (item.id === id) {
                    item.isChecked = true
                }
            })
        }
        if (origin === 'removeZsGs') {
            productListZS.map(item => {
                if (item.id === id) {
                    item.isChecked = false
                }
            })
        }
        if (origin === 'removeTyGs') {
            productListTY.map(item => {
                if (item.id === id) {
                    item.isChecked = false
                }
            })
        }
    })
    if (origin === 'addZsGs' || origin === 'removeZsGs') {
        renderEditProductZS()
    }
    if (origin === 'addTyGs' || origin === 'removeTyGs') {
        renderEditProductTy()
    }
    bounce_Fixed3.cancel()
    // // 更新数据存储
    // let listYes =  $("#newContractInfo .scanGs").data('gsArr');
    // let listNo =  $("#newContractInfo .scanGs").data('gsArrNo') || [];
    // if(fun === "addGs"){
    //     listYes.push(...listEdit)// 将新增的添加到 选中的
    //     let listNew = []
    //     listNo.forEach(function(noLm, index){ // 从未选中中 去掉 新增的
    //         let has = false
    //         listEdit.forEach(function(noLm2){
    //            if(noLm.id == noLm2.id){
    //                has = true
    //            }
    //         })
    //         if(!has){
    //             listNew.push(noLm)
    //         }
    //     })
    //     listNo = listNew
    // } else if(fun === "removeGs"){
    //     listNo.push(...listEdit)
    //     let listNew = []
    //     listYes.forEach(function(noLm, index){ // 从未选中中 去掉 新增的
    //         let has = false
    //         listEdit.forEach(function(noLm2){
    //             if(noLm.id == noLm2.id){
    //                 has = true
    //             }
    //         })
    //         if(!has){
    //             listNew.push(noLm)
    //         }
    //     })
    //     listYes = listNew
    // }
    // $("#newContractInfo .scanGs").data('gsArr', listYes).data('gsArrNo', listNo).html(listYes.length);
}


// creator: hxz 2021-05-21 编辑合同确定
function editContractOk(num) {
    if(num === 0){ // 取消, 删除上传的文件
        bounce_Fixed2.cancel()
        let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
        if(fileImArr.length > 0){
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }
        let file2= $("#newContractInfo .fileCon2 .fileIm")
        if(file2.length > 0){
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }

    }else{ // 确定

        let info = {}
        info.cNo = $("#newContractInfo .cNo").val()

        info.cSignDate = $("#newContractInfo .cSignDate").val()
        info.cStartDate = $("#newContractInfo .cStartDate").val()
        info.cEndDate = $("#newContractInfo .cEndDate").val()

        if(info.cNo.length === 0){
            layer.msg('请录入合同编号！');
            return false
        }
        if(!info.cStartDate || !info.cEndDate) {
            layer.msg('请选择合同的有效期！');
            return false
        }
        info.cMemo = $("#newContractInfo .cMemo").val()
        if(info.cStartDate && info.cEndDate){
            let start = Date.parse(new Date( info.cStartDate));
            let end = Date.parse(new Date( info.cEndDate));
            if(start > end){
                layer.msg('合同有效期开始时间应早于结束时间');
                return false
            }
        }

        info.fileCon1 = [];
        $("#newContractInfo .fileCon1 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon1.push(JSON.parse(itemf))
        })
        info.fileCon2 = [];
        $("#newContractInfo .fileCon2 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon2.push(JSON.parse(itemf))
        })
        info.goodList = [];
        $("#newContractInfo .goodList .gsIm").each(function () {
            let itemg = $(this).find(".hd").html();
            info.goodList.push(JSON.parse(itemg))
        })


        let url = '', data = { 'customerId': $("#updateCustomerPanel").data("id") }
        let filePath = '',fileName = '';
        if( info.fileCon2.length > 0  ){
            filePath = info.fileCon2[0].filename
            fileName = info.fileCon2[0].originalFilename
        }
        let imgs = []
        if(info.fileCon1 && info.fileCon1.length > 0){
            info.fileCon1.forEach(function(im, index){
                let imgItem = {
                    uplaodPath: im.filePath || im.uplaodPath,
                    type: 1, // 类型:1-图片,2-视频,3-文档
                    title: im.title,
                    operation: 1
                }
                imgs.push(imgItem)
            })
        }

        let cusInfo = $("#newContractInfo").data("cusInfo")
        let productListTY = $("#newContractInfo").data('productListTY') || []
        let editProductTY = productListTY.filter(item => item.isChecked)
        let productListZS = $("#newContractInfo").data('productListZS') || []
        let editProductZS = productListZS.filter(item => item.isChecked)
        let zsGoods = editProductZS.map(item => {return {commodity: item.id}})
        let tyGoods = editProductTY.map(item => {return {commodity: item.id}})
        console.log('zsGoods', zsGoods)
        console.log('tyGoods', tyGoods)
        data = {
            customer: cusInfo.id,
            sn: info.cNo,
            contractSignTime: info.cSignDate,
            contractStartTime: info.cStartDate,
            contractEndTime: info.cEndDate,
            type: 1, // 1-商品合同 1-商品合同
            memo: info.cMemo,
            contractBaseImages: JSON.stringify(imgs),
            productZSList: JSON.stringify(zsGoods),
            productTYList: JSON.stringify(tyGoods)
            // productList: goods,
        }
        if( info.fileCon2.length > 0  ){
            data.filePath = filePath
            data.fileName = fileName
        }
        $.ajax({
            url: '../sales/insertContractBase.do',
            data: data ,
            success:function (res) {
                let state = res.data.state
                let newContractId = res.data.contract.id
                if(state === 1){
                    bounce_Fixed2.cancel()
                    layer.msg("操作成功！")
                    let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
                    if(fileImArr.length > 0){
                        let info = JSON.parse(fileImArr.find(".hd").html())  ;
                        let groupUuid = info.groupUuid;
                        cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} )
                    }
                    let file2= $("#newContractInfo .fileCon2 .fileIm")
                    if(file2.length > 0){
                        let info = JSON.parse(file2.find(".hd").html());
                        let groupUuid = info.groupUuid;
                        cancelFileDel({type: 'groupUuid',groupUuid: groupUuid})
                    }
                    $.ajax({
                        "url": "../sales/listContractBase.do",
                        "data": {
                            customer: cusInfo.id,
                            type: 1
                        },
                        success: function (res) {
                            let contracts = res.data.contractBaseList || [];
                            let options = `<option value="">请选择</option>`;
                            let value = $(".contractList:visible").val();
                            for (let item of contracts) {
                                options += `<option value="${item.id}">${item.sn}</option>`;
                            }
                            $(".contractList:visible").html(options);
                            if (value) {
                                $(".contractList:visible").val(value);
                            }
                        }
                    });
                } else if (state === 2) {
                    layer.msg("已续约不可修改!")
                } else if (state === 3) {
                    layer.msg("修改的日期不能在上一个合同结束日之前!")
                } else {
                    layer.msg("操作失败")
                }
            }
        })
    }
}

// creator: 李玉婷，2021-05-27 9:47:12，合同查看
function contractScan(cid) {
    $.ajax({
        'url' : "../sales/getContractBase.do",
        "data":{ "id": cid },
        success:function (res) {
            var newcInfo = res.data
            var status = res.status
            if(status != 1){
                layer.msg("获取合同信息失败！");
                return false;
            }
            bounce_Fixed2.show($("#cScan"))
            $("#cScan").data("cid",cid)
            $("#cScan .cNos").html(newcInfo.sn)
            $("#cScan .create").html("&nbsp;&nbsp;" +newcInfo.createName + "&nbsp;&nbsp;" +new Date(newcInfo.createDate).format("yyyy-MM-dd hh:mm:ss"))
            $("#cScan .cSignDates").html(new Date(newcInfo.signTime).format("yyyy-MM-dd"))
            $("#cScan .cvalidDates").html(new Date(newcInfo.validStart).format("yyyy-MM-dd") + '至' + (new Date(newcInfo.validEnd).format("yyyy-MM-dd")))
            $("#cScan .cMemos").html(newcInfo.memo)
            let imgStr1 = ``,fileSect = ``;
                newcInfo.contractBaseImages.forEach(function(bsIm){
                imgStr1 += `<span class="fileImScan" data-fun="imgScan" data-path="${bsIm.filePath}">
                                  <span>${bsIm.orders + 1 }</span>
                                  <span class="hd">${JSON.stringify(bsIm) }</span>
                             </span>`
            })
            $("#cScan .cImgs").html(imgStr1)
            if (newcInfo.filePath && handleNull(newcInfo.filePath) != "") {
                fileSect = `<a class="ty-color-blue node" data-fun="cWord" data-path="${newcInfo.filePath}" path="${newcInfo.filePath}">查看</a>`;
            }
            $("#cScan .cWord").html(fileSect)
            let productList = newcInfo.productList || []
            $("#cScan .gNum").data('list', productList).html(productList.length)
            let enabledList = newcInfo.enabledList || [] , enableStr= ``;
            enabledList.forEach(function (enIm) {
                enableStr += `
                <p>
                    <span>${ enIm.enabled === 1 ? "恢复履约/重启合作" : "暂停履约/终止合作" }</span>
                    <span class="enName">${ enIm.updateName }</span>
                    <span>${ new Date(enIm.enabledTime).format("yyyy-MM-dd hh:mm:ss") }</span>
                </p>`;
            })
            $("#cScan .enabledList").html(enableStr)
        }
    })
}
// updator: hxz，2021-10-27 21:24:16，输出数据
function getImportLastList(data) {
    var importList = data["pdProductImportList"];
    var source = $("#importEnteryType").data("source");
    var caseVal = Number(data.importOption);
    var hasRate = Number(data.taxInclusive);
    $(".mainCon3 .importCon2 .initAll").html(data.importSum);
    $(".mainCon3 .importCon2 .inabledSum").html(Number(data.tureImportSum));
    let str = ``, trObj = ``;
    var buttonState = data["buttonState"];
    if (importList && importList.length > 0) {
        let priceStr = ''
        if(source == 2){ // 专属
            if(caseVal == 2){ // 增专
                if(Number(hasRate)>0){
                    priceStr = 'unitPrice'
                    $(".hasRatePrice").html("含税单价")
                } else {
                    priceStr = 'unitPriceNotax'
                    $(".hasRatePrice").html("不含税单价")
                }
            }else if(caseVal == 3){ // 普通发票
                priceStr = 'unitPriceInvoice'
                $(".hasRatePrice").html("开普通发票时的开票单价")
            }else if(caseVal == 4){ // 不开发票
                priceStr = 'unitPriceNoinvoice'
                $(".hasRatePrice").html("不开发票时的单价")
            }
        }
        if(caseVal == 1){
            let cat1 = false,cat2 = false;
            $.ajax({
                "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
                // async: false,
                success: function (res) {
                    var list = res["financeInvoiceSettings"];
                    let rateList_ = []
                    let invoiceCatList_ = []
                    if (list && list.length > 0) {
                        for (var i = 0; i < list.length; i++) {
                            if (list[i]["category"] == '1'){
                                var rate = list[i]["enableTaxTate"];
                                var rates = rate.split(",");
                                cat1 = true;
                                if (rates && rates.length >0){
                                    for (var r = 0; r < rates.length; r++) {
                                        rateList_.push(rates[r])
                                    }
                                }
                            } else if (list[i]["category"] == '2' || list[i]["category"] == '3'){
                                cat2 = true;
                            }
                        }
                        if (cat1) {
                            invoiceCatList_.push({ 'val':1, "name":"开具增值税专用发票" })
                        }
                        if (cat2) {
                            invoiceCatList_.push({ 'val':2, "name":"开具其他发票" })
                        }
                    }
                    invoiceCatList_.push({ 'val':4, "name":"不开发票" })

                    for (var i = 0; i < importList.length; i++) {
                            str +=
                                `<tr>
                                    <td>${importList[i].code} &nbsp;&nbsp; ${handleNull(importList[i].name)} ${ handleNull(importList[i].specifications)} &nbsp;&nbsp; ${handleNull(importList[i].model) } &nbsp;&nbsp; ${ handleNull(importList[i].unit)}</td>
                                    <td>
                                        <select name="invoiceType" class="sign2" onchange="changeInvoiceCat($(this))">${ setinitInvoiceType(invoiceCatList_,importList[i].invoiceType ) }</select>
                                    </td>
                                    <td class="excInvoice po-invoice1">
                                        <select name="taxRate" class="sign2" onchange="changeInvoiceRate($(this))" disabled="disabled">${ setinitRate(rateList_,importList[i].taxRate ) }</select>
                                    </td>
                                    <td class="excInvoice po-invoice1">
                                        <input class="sign2" value="${handleNull(importList[i].unitPrice)}" onkeyup="changeExcInvoice($(this))" onblur="importPriceChange($(this))" name="unitPrice" disabled="disabled"/>
                                    </td>
                                    <td class="excInvoice po-invoice1">
                                        <input class="sign2" value="${handleNull(importList[i].unitPriceNotax)}" onkeyup="changeExcInvoice($(this))" onblur="importPriceChange($(this))" name="unitPriceNotax" disabled="disabled"/>
                                    </td>
                                    <td class="po-invoice2">
                                        <input class="sign2" value="${handleNull(importList[i].unitPriceInvoice)}" name="unitPriceInvoice" onblur="importPriceChange($(this))" disabled="disabled"/>
                                    </td>
                                    <td><input class="sign2" value="${handleNull(importList[i].unitPriceNoinvoice)}" name="unitPriceNoinvoice" onblur="importPriceChange($(this))" disabled="disabled"/></td>
                                    <td>
                                        <span class="ty-color-blue btn" data-name="update">修改</span>
                                        <span class="ty-color-red btn" data-name="del">删除</span>
                                        <span class="hd">${JSON.stringify(importList[i])}</span>
                                    </td>
                                 </tr>`;
                    }
                    $(".mainCon3 .importCon2 .normal2").show();
                    $(".mainCon3 .importCon2 .normal2 tbody").html(str);
                    $(".mainCon3 .importCon2 .normal1").hide()
                }
            });
        }else{
            for (var i = 0; i < importList.length; i++) {
                let price34 = handleNull(importList[i][priceStr])
                price34 = price34 && price34.toFixed(2)
                    str +=
                        `<tr>
                    <td>${importList[i].code} &nbsp;&nbsp; ${handleNull(importList[i].name)} ${ handleNull(importList[i].specifications)} &nbsp;&nbsp; ${handleNull(importList[i].model) } &nbsp;&nbsp; ${ handleNull(importList[i].unit)}</td>
                    <td class="sign" data-name="miniStock">${ handleNull(importList[i].miniStock) }</td>' +
                    <td class="sign" data-name="${ priceStr }">${ price34 }</td>' +
                    <td class="sign" data-name="desc">${ handleNull(importList[i].desc)}</td>' +
                    <td>
                        <span class="ty-color-blue btn" data-name="update">修改</span>
                        <span class="ty-color-red btn" data-name="del">删除</span>
                        <span class="hd">${JSON.stringify(importList[i])}</span>
                    </td>
                 </tr>`;
            }
            $(".mainCon3 .importCon2 .normal1").show();
            $(".mainCon3 .importCon2 .normal1 tbody").html(str);
            $(".mainCon3 .importCon2 .normal2").hide()
        }
    }

    for (var i = 0; i < importList.length; i++) {
        trObj = $(".mainCon3 .importCon2 tbody tr:eq("+ i+")");
        trObj.find("select[name='invoiceType']").val(importList[i].invoiceType);
        trObj.find("select[name='taxRate']").val(importList[i].taxRate);
        if (importList[i].invoiceType == 1) {
            trObj.find("td:eq(2),td:eq(3),td:eq(4)").children().prop('disabled',false);
        } else if (importList[i].invoiceType == 2) {
            trObj.find("td:eq(5)").children().prop('disabled',false);
        } else if (importList[i].invoiceType == 4) {
            trObj.find("td:eq(6)").children().prop('disabled',false);
        }
    }
    if (buttonState == 1) {
        $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImportJudge()");
    } else {
        $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
    }
}
function setinitInvoiceType(catList, selectV) {
    let str = `<option value=""> 请选择</option>`
    catList.forEach(function(item){
        str += `<option ${ selectV == item.val ? 'selected' : ''  } value="${ item.val }"> ${ item.name }</option>`
    })
    return str
}
function setinitRate(rateList, selectV) {
    let str = `<option value=""> 请选择</option>`
    rateList.forEach(function(item){
        str += `<option ${ selectV == item ? 'selected' : ''  } value="${ item }"> ${ item }%</option>`
    })
    return str
}
// creator: 李玉婷，2021-08-31 17:42:14，发票切换
function changeInvoiceCat(obj) {
    var val = obj.val();
    obj.parents("tr").find("input,select[name='taxRate']").val('').prop('disabled',true);
    if (val == 1) {
        obj.parents("tr").find("td:eq(2),td:eq(3),td:eq(4)").children().prop('disabled',false);
    } else if (val == 2) {
        obj.parents("tr").find("td:eq(5)").children().prop('disabled',false);
    } else if (val == 4) {
        obj.parents("tr").find("td:eq(6)").children().prop('disabled',false);
    }
    importPriceChange(obj);
}

function changeInv(obj){
    var val = obj.val();
    $(`#editPrice .inv${ val }`).show().siblings().hide();
    $(`#editPrice input`).val('');
}

// creator: 李玉婷，2022-12-11 8:12:24，管理交货地点
function manageDeliveryPlace(source){
    $("#chooseDelivery").data("source", source)
    var customerID = '';
    if (source === "new") {
        customerID = $("#firstLevelName").data("categoryid");
    } else {
        var info = JSON.parse($("#inforStorage").html());
        customerID = info.customer_;
    }
    $.ajax({
        async:false,
        url:"../sales/getAddressListByCondition.do",
        data:{"cusId":customerID},
        success:function (data) {
            let addrList = data["data"] || [];
            let addrListStr = ``;
            if(data === null || handleNull(data.deliveryType) === '' || data.deliveryType === '1' || data.deliveryType === 1){
                layer.msg("不可选择该客户");
                return false;
            } else if (data.deliveryType === '2' || data.deliveryType === 2){
                if (addrList && addrList.length > 0) {
                    for (var i = 0; i < addrList.length; i++) {
                        addrListStr +=
                            ` <li>
                                   <i class="fa fa-square-o"></i>
                                   <span>${addrList[i].address}</span>
                                   <span class="hd">${JSON.stringify(addrList[i])}</span>
                               </li>`;
                    }
                }
                $(".placeList").html(addrListStr);
                $(".receiveAddress").html(JSON.stringify(addrList));
                bounce_Fixed.show($("#chooseDelivery"))
            }
        }
    })
}
// creator: 李玉婷，2022-12-11 8:12:24，管理交货地点确定
function chooseDeliveryOk(type){
    let source = $("#chooseDelivery").data("source")
    if (type === 0) {//关闭弹窗
        bounce_Fixed.cancel()
        if (source === "update") {
            bounce_Fixed.show($("#editCommodityOther"));
        }
    } else {
        let curObj = $(".deliveryPlaceData");
        bounce_Fixed.cancel()
        if (source === "update") {
            curObj = $(".edit_deliveryPlaceData");
            bounce_Fixed.show($("#editCommodityOther"));
        }
        let address = ``, data = []
        $(".placeList .fa-check-square-o").each(function (){
            let info = JSON.parse($(this).siblings(".hd").html())
            address += info.address + ',';
            data.push({"customerAddress": info.id})
        })
        if (address !== "") {address = address.substring(0, address.length-1)}
        curObj.html(address).siblings(".hd").html(JSON.stringify(data))
    }
}
// creator: lyt，2022-12-11 10:10:55，编辑收货信息
function receiveFirstPage(){
    let addrList = $(".receiveAddress").html();
    if (addrList.length > 0) {
        addrList = JSON.parse(addrList);
    }
    $(".receiveList").hide();
    $(".areaList").hide();
    $(".receiveList tbody tr:gt(0)").remove();
    $(".areaList tbody tr:gt(0)").remove();
    setShList(addrList);
    bounce_Fixed2.show($("#receiveFirstPage"))
}
// creator: 李玉婷，2019-09-04 18:55:15，添加
function addMore (obj){
    obj.next("select").show();
}
function setShList(data){
    let able1 = false, areaAble = false;
    let flag = ``, temp = ``, html = ``, str = ``;
    for (var i=0; i < data.length; i++) {
        if (data[i].type === '1') {
            able1 = true;
            flag = `addAddress`
        }else if (data[i].type === '3') {
            flag = `addArea`
            areaAble = true;
        }
        temp =
            '<tr>' +
            '    <td>' + data[i].address + '</td>'+
            '    <td>' + data[i].contact + '</td>' +
            '    <td>' + data[i].mobile + '</td>' +
            '    <td>' +
            '       <span class="ty-color-blue funBtn" data-type="update" data-fun="'+ flag +'" data-source="addCustomer">修改</span>' +
            '       <span class="ty-color-red funBtn" data-type="stop" data-fun="stopAddress">停用</span>' +
            '       <span class="hd">'+ JSON.stringify(data[i]) +'</span>' +
            '   </td>' +
            '</tr>';
        if (data[i].type === '1') {
            html += temp;
        }else if (data[i].type === '3') {
            str += temp;
        }
    }
    if (able1 && areaAble) {
        $(".receiveList").show().find("tbody").append(html);
        $(".areaList").show().find("tbody").append(str);
    } else {
        if (able1) {
            $(".areaList").hide();
            $(".receiveList").show().find("tbody").append(html);
        } else if (areaAble) {
            $(".receiveList").hide();
            $(".areaList").show().find("tbody").append(str);
        }
    }

}
// creator: lyt，2022-12-11 11:20:14，编辑收货信息
function receiveFirstPageSure(){
    let  id = ``;
    let source = $("#chooseDelivery").data("source")
    if (source === "new") {
        id = $("#firstLevelName").data("categoryid");
    } else {
        var info = JSON.parse($("#inforStorage").html());
        id = info.customer_;
    }
    let data = {
        "id": id,
        "selfState": $("#receiveFirstPage .selfStateTxt").html() === "开启"? 0:1
    }
    $.ajax({
        url: "../sales/editCustomerAddress.do",
        data: data,
        success: function (data) {
            var status = data.success;
            if(status == '1'){
                bounce_Fixed2.cancel();
            }
        }
    })
}
/*creator:lyt 2022/11/14 0014 下午 4:46 收货地址*/
function addAddress(obj){
    let type = obj.data('type')
    let  id =``;
    $("#newReceiveAddressInfo").data('type', type);
    bounce_Fixed3.show($("#newReceiveAddressInfo"))
    $("#newReceiveAddressInfo input").val("");
    $("#ReceiveName")
        .val("").data('orgData',"")
        .siblings(".hd").html("") ;
    if (type === "new"){
        let source = $("#chooseDelivery").data("source")
        if (source === "new") {
            id = $("#firstLevelName").data("categoryid");
        } else {
            var info = JSON.parse($("#inforStorage").html());
            id = info.customer_;
        }
    } else if (type === "update"){
        let trObj = obj.parents("tr")
        let info = JSON.parse(obj.siblings(".hd").html())
        id = info.id;
        $("#newReceiveAddressInfo").data('trObj', trObj);
        $("#ReceiveAddress").val(trObj.find("td").eq(0).html());
        $("#ReceiveName")
            .val(trObj.find("td").eq(1).html()).data('orgData',"")
            .siblings(".hd").html(JSON.stringify(info)) ;
    }
    $("#newReceiveAddressInfo").data('id', id);
    setTimer('updateReceive');
}
//info = {"name":data.name ,"contact":data.name , "mobile":data.mobile , "id":data.id, "post":data.post, "enabled":1};
function addAddressOk (obj){

    let shType = obj.data("type");
    var id   = '';
    var type = '';
    let contactInfo = ``;
    var json = {
        'type': obj.data("type")
    }
    if (shType === 1) {
        json.address = $("#ReceiveAddress").val();
        id   = $("#newReceiveAddressInfo").data('id');
        type = $("#newReceiveAddressInfo").data('type');
        contactInfo = JSON.parse($("#ReceiveName").siblings(".hd").html());
    } else if (shType === 3) {
        let regionCon = $("#regionCon").val();
        let contactName = $("#areaName").val();
        if (regionCon === "" || contactName === "") {
            layer.msg('还有必填项尚未填写!');
            return false;
        }
        let code = $("#regionCon").siblings(".hd").html();
        let path = code.split(",");
        json.address = regionCon;
        json.regionCode = path[path.length - 1];
        json.path = code;
        json.requirements = $("#requirements").val();
        id   = $("#newReceiveAreaInfo").data('id');
        type = $("#newReceiveAreaInfo").data('type');
        contactInfo = JSON.parse($("#areaName").siblings(".hd").html());
    }
    json.contact = contactInfo.contact;
    json.mobile = contactInfo.mobile;
    if (type == 'new') {
        json.customerContact = contactInfo.id
        json = JSON.stringify(json);
        var params = {
            'customerId': id,
            'shAddress': json
        }
        $.ajax({
            url: '../sales/addCustomerAddress.do',
            data: params,
            success: function (data) {
                var status = data.status;
                if (status === '1' || status === 1) {
                    let source = $("#chooseDelivery").data("source")
                    manageDeliveryPlace(source)
                    receiveFirstPage();
                } else {
                    layer.msg("新增收货地址失败！");
                }
            }
        })
    }else if(type == 'update'){
        json.id = id
        $.ajax({
            url: '../sales/updateCustomerAddress.do',
            data: json,
            success: function (data) {
                var status = data.status;
                if (status === '1' || status === 1) {
                    let source = $("#chooseDelivery").data("source")
                    manageDeliveryPlace(source)
                    receiveFirstPage();
                } else {
                    layer.msg("修改收货地址失败！");
                }
            }
        })
    }
    bounce_Fixed3.cancel();
}
/*creator:lyt 2022/11/15 0015 下午 12:32 停用收货地址*/
function stopAddress(obj){
    let info = JSON.parse(obj.siblings(".hd").html())
    bounce_Fixed3.show($("#turnStateTip"));
    $("#turnStateTip").data('id', info.id);
}
//creator:lyt 2023/7/25 0025 下午 7:37 新增到货区域
function addArea(obj){
    let  id =``;
    let type = obj.data('type')
    $("#newReceiveAreaInfo").data('type', type);
    bounce_Fixed3.show($("#newReceiveAreaInfo"))
    $("#newReceiveAreaInfo input").val("");
    $("#newReceiveAreaInfo .hd").html("");
    $("#areaName").data('orgData',"");
    $("#newReceiveAreaInfo .bonceHead span").html('新增到货区域');
    if (type === "new"){
        let source = $("#chooseDelivery").data("source")
        if (source === "new") {
            id = $("#firstLevelName").data("categoryid");
        } else {
            var info = JSON.parse($("#inforStorage").html());
            id = info.customer_;
        }
    } else if (type === "update"){
        let trObj = obj.parents("tr")
        let info = JSON.parse(obj.siblings(".hd").html());
        id = info.id;
        $("#newReceiveAreaInfo .bonceHead span").html('修改到货区域');
        $("#newReceiveAreaInfo").data('trObj', trObj);
        $("#regionCon").val(info.address);
        $("#regionCon").siblings(".hd").html(info.regionCode);
        $("#requirements").val(info.requirements);
        $("#areaName")
            .val(trObj.find("td").eq(1).html()).data('orgData',"")
            .siblings(".hd").html(JSON.stringify(info)) ;
    }
    $("#newReceiveAreaInfo").data('id', id);
}
//creator:lyt Date:2018/11/19 修改-停用按钮
function turnStatenSure(){
    var addressId = $("#turnStateTip").data('id');
    $.ajax({
        url: '../sales/startOrStopAddress.do',
        data: {
            'addressId': addressId,
            'enabled': 0
        },
        success: function (data) {
            var status = data.status;
            if (status === '1' || status === 1) {
                let source = $("#chooseDelivery").data("source")
                manageDeliveryPlace(source)
                receiveFirstPage();
                bounce_Fixed3.cancel();
            } else {
                layer.msg("停止失败！");
            }
        }
    })
}

//creator:lyt 2022/11/14 上门自提 -改变状态
function turnSelfState(obj){
    let str = $(".selfStateTxt").html();
    if (str === "开启") {
        $(".selfStateTxt").html("关闭");
    } else {
        $(".selfStateTxt").html("开启");
    }
}
// creator: hxz 2020-12-10 新增联系人
function addContactInfo() {
    document.getElementById('newContectData').reset();
    $(".otherContact").html("");
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    if($(".contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
    }
    $("#addMoreContact").hide();
    $("#uploadCard").html("")
    initCardUpload($("#uploadCard"));
    bounce_Fixed4.show($("#newContectInfo"));
    setTimer('updateContact');
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContact li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li>' +
                '<span class="sale_ttl1">手机：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="1" data-name="手机" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '2':
            html +=
                '<li>' +
                '<span class="sale_ttl1">QQ：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '3':
            html +=
                '<li>' +
                '<span class="sale_ttl1">Email：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '4':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微信：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '5':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微博：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '9':
            $("#useDefinedLabel input").val("");
            bounce_Fixed4.show($("#useDefinedLabel"));
            setTimer('useDefinedLabel');
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-07 08:50:59，删除添加的联系方式
function removeAdd(obj) {
    obj.parent("li").remove();
    if ($(".otherContact li").length > 0) {
        $(".otherContact li").each(function () {
            var val = $(this).find("input").val();
            var type = $(this).find("input").data('type');
            if (type == '9' || type == '9') type = 6
            if (val == '') {
                $("#addMoreContact option").eq(type).prop('disabled', true);
            }else {
                $("#addMoreContact option").eq(type).prop('disabled', false);
            }
        })
    }else{
        $("#addMoreContact option").prop('disabled', false);
    }
}
// creator: 李玉婷，2019-09-07 08:50:59，新增联系人确定
function addContactOk(){
    var id = ``;
    let source = $("#chooseDelivery").data("source")
    if (source === "new") {
        id = $("#firstLevelName").data("categoryid");
    } else {
        var info = JSON.parse($("#inforStorage").html());
        id = info.customer_;
    }
    var data = {
        'tags': $("#contactFlag").html(),
        'name': $("#contactName").val(),
        'post': $("#position").val(),
        'mobile': $("#contactNumber").val(),
        'customer': id,
        'visitCard': '',
        'socialList': '[]'
    };
    if($("#contactsCard .bussnessCard").length > 0){
        data.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
    }
    if($(".otherContact li").length > 0){
        var arr = []
        $(".otherContact li").each(function () {
            if ($(this).find('input').val() != '') {
                var json = {
                    'code': $(this).find("input").val(),
                    'type': $(this).find("input").data('type'),
                    'name': $(this).find("input").data('name')
                };
                arr.push(json);
            }
        })
        arr = JSON.stringify(arr);
        data.socialList = arr;
    }
    $.ajax({
        url: '../sales/addCustomerContact.do',
        data: data,
        success: function (res) {
            data.id = res.id;
            var status = res.status;
            if (status === '1' || status === 1) {
                layer.msg('新增成功')
                var groupUuidArr = []
                $("#newContectInfo [groupUuid]").each(function () {
                    groupUuidArr.push({
                        type: 'groupUuid',
                        groupUuid: $(this).attr("groupUuid")
                    })
                })
                cancelFileDel(groupUuidArr)
                // 给当前的赋值
                var tags = $("#contactFlag").html();
                if(tags == "收货人"){
                    data.contact = data.name
                    if ($("#newReceiveAddressInfo").is(":visible")) {
                        bounce_Fixed3.show($("#newReceiveAddressInfo"));
                        setTimer('updateReceive');
                        $("#ReceiveName")
                            .val(data.name).data('orgData', data.name)
                            .siblings(".hd").html(JSON.stringify(data));
                    } else  if ($("#newReceiveAreaInfo").is(":visible")){
                        $("#areaName")
                            .val(data.name).data('orgData',data.name)
                            .siblings(".hd").html(JSON.stringify(data)) ;
                    }
                }
                bounce_Fixed4.cancel();
            } else {
                layer.msg("查看失败！");
            }
        }
    })

}

// creator: hxz 2020-12-10 获取联系人列表
function getCusContactList(cusID) {
    $.ajax({
        "url":"../sales/getContactsList.do",
        "data":{ 'customerId': cusID },
        success:function (res) {
            if(res.status !== 1){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data'] || [] ;
            setCusListStr(list, 'updateCustomer')
        }
    });
}
function setCusListStr(list, source) {
    let str="";
    if(list.length === 0){
        $("#chooseCusContact .p0").show()
        $("#chooseCusContact .p1").hide()
        return false
    }
    $("#chooseCusContact .p0").hide()
    $("#chooseCusContact .p1").show()
    for(let i in list){
        let item = list[i];
        item.contact = item.name
        str += `<li>
                    <i class="fa fa-circle-o"></i>
                    <span>${item.name}</span>
                    <span>${item.post && item.post.substr(0,8)}</span>
                    <span>${item.mobile}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                    <span class="lineBtn ty-right" data-source="${source}" data-fun="contactSocial" data-id="${item.id}">查看</span>
                </li>`;
    }
    $("#chooseCusContact .cusList").html(str);
}
// creator: hxz 2020-12-10 收货人那里的新增收货人
function chooseCusContactOk() {
    let selectObj = $("#chooseCusContact").find(".fa-dot-circle-o");
    if(selectObj.length > 0){
        let strInfo = selectObj.siblings("span.hd").html();
        let info = JSON.parse(strInfo);
        $($("#target").val())
            .val(selectObj.next().html()).data('orgData',selectObj.next().html())
            .siblings(".hd").html(strInfo) ;
        bounce_Fixed4.cancel();
    }else layer.msg('请先选择人员')
}
// creator: lyt 2022-12-10 联系人查看
function contactSocial(obj){
    $.ajax({
        url : "../sales/getContactsSocial.do" ,
        data : {
            'contactId': obj.data('id')
        },
        success:function(data){
            var get = data['data'];
            see_otherContactStr(get)
        },
        error: function (msg) {
            layer.msg("连接错误，请稍后重试！");
            return false;
        }
    });
}
function see_otherContactStr(get){
    var html = '',socialList = get['socialList'];
    $("#see_contactName").html(get.name);
    $("#see_position").html(get.post);
    $("#see_contactTag").html(get.tags);
    $(".see_otherContact").html("");
    $("#contactSeeDetail .see_createName").html(get.createName);
    $("#contactSeeDetail .see_createDate").html(new Date(get.createDate).format('yyyy-MM-dd hh:mm:ss'));
    if(socialList.length > 0){
        let sortList = [];
        for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
        for(var r in socialList){
            let item = socialList[r];
            let _index = Number(item.type);
            sortList[_index].push(item);
        }
        let sortAfter = [];
        for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
        let allStr = '';
        for(var t in sortAfter){
            let item = sortAfter[t];
            if(t%2===0){
                allStr += `<tr><td>${item.name}</td><td>${item.code}</td>`
            }else{
                allStr += `<td>${item.name}</td><td>${item.code}</td></tr>`
            }
        }
        if(sortAfter.length % 2 !== 0){
            allStr += `<td> </td><td> </td></tr>`
        }
        $(".see_otherContact").html(allStr);
    }
    bounce_Fixed5.show($("#contactSeeDetail"));
}




// creator: 李玉婷，2019-09-16 16:26:52，定时器--新增验证
function setTimer (timer) {
    switch (timer) {
        case 'updateReceive':
            bounce_Fixed3.everyTime('0.5s','updateReceive',function () {
                var  filledNum = 0;
                $("#newReceiveAddressInfo input").each(function(){
                    if ($(this).val() == '') filledNum++;
                });
                if(filledNum === 0 ){
                    $("#addAddressOk").prop("disabled",false);
                }else{
                    $("#addAddressOk").prop("disabled",true);
                }
            });
            break;
        case 'updateContact':
            bounce_Fixed4.everyTime('0.5s','updateContact',function () {
                var state = 0, filledNum = 0,otherContactLen = 0;
                var contactsCard = $("#contactsCard").data('org');
                var imgCard = $("#contactsCard .filePic").data('path');
                var len = $(".otherContact").data('length');
                $("#addMoreContact option").prop('disabled', false);
                if ($(".otherContact li").length > 0) {
                    $(".otherContact li").each(function () {
                        var val = $(this).find("input").val();
                        var type = $(this).find("input").data('type');
                        if (type == '9' || type == '9') type = 6
                        if (val == '') {
                            $("#addMoreContact option").eq(type).prop('disabled', true);
                        }else {
                            otherContactLen++;
                        }
                    })
                }
                if (len !=  otherContactLen) state ++;
                $("#newContectData [require]:visible").each(function(){
                    if ($(this).val() != '') filledNum++;
                    if($(this).val() != $(this).data('org')){
                        state ++;
                    }
                });
                if(contactsCard != imgCard) state ++;
                if(filledNum > 0 && state > 0){
                    $("#addContact").prop("disabled",false);
                }else{
                    $("#addContact").prop("disabled",true);
                }
            });
            break;
        case 'useDefinedLabel':
            bounce_Fixed5.everyTime('0.5s', 'useDefinedLabel',function () {
                var name = $.trim($("#defLable").val());
                if (name == '' || !name) {
                    $("#addNewLableSure").prop('disabled', true);
                } else {
                    $("#addNewLableSure").prop('disabled', false);
                }
            })
            break;
    }
}
// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    var parent4 = selector.parents(".bounce_Fixed4")
    var parent5 = selector.parents(".bounce_Fixed5")
    if (parent5.length > 0) {
        bounce_Fixed5.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    }else if (parent4.length > 0) {
        bounce_Fixed4.cancel()
    } else {
        console.log("未定义的状态")
    }
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = []
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
}
// creator: 李玉婷，2019-09-17 15:09:14，名片上传
function initCardUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var path = data.filename //路径（包含文件类型）
            obj.parent().attr("groupUuid", data.groupUuid )
            var imgStr =
                '<div class="bussnessCard" style="width:70%; margin: 0 auto;">' +
                '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '   <span fileUid="' + data.fileUid + '" class="ty-color-blue" onclick="cancleCard($(this))">删除</span> ' +
                '</div>';
            $('#uploadCard').hide();
            $('#uploadCard').before(imgStr);
        }
    });
}
// creator: 李玉婷，2019-09-04 19:31:34，删除名片
function cancleCard(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    $('#uploadCard').show();
}
laydate.render({elem: '.cSignDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cStartDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cEndDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '#date1', format: 'yyyy-MM-dd'});




