// creator：王静，2017-08-01 09:56:09
$(function(){
    //获取包装信息主列表
    getbpMessage(1,10);
});
var m_current_level=1;
//updator:王静 2017-08-28 8:27:23  获取包装信息主列表
function getbpMessage(cur,per){
    $.ajax({
        url:$.webRoot+"/pack/getCustomerList.do",
        type:"post",
        data:{"pageNumber":cur,"quantum":per},
        success:function(res){
            $("#ye_goodsprocess").html("");
            $("#pack_tbl").html("");
            var cur=res["cur"];
            var count=res["countall"];
            setPage( $("#ye_goodsprocess"),cur,count,'backpmessage');
            if(res["status"]==1){
                var pdcus=res["pdCustomerProductList"];
                for(var i=0;i< pdcus.length; i++){
                    var no=i+1;
                    var str="<tr>" +
                        "<td>"+no+"</td>"+  //序号
                        "<td>"+ pdcus[i]["outerSn"]+"</td>"+ //商品代号
                        "<td>"+ pdcus[i]["outerName"]+"</td>"; //外部名称
                    var packList = pdcus[i]["pdPackHashSet"];
                    //对编辑的顺序进行排序 manner包装方式 amount--每包的数量
                    packList.sort(function(x,y){return  x.level<y.level? -1:1});
                    if(packList.length==1){
                        str = str + "<td>"+packList[0]["manner"]+"</td><td>"+packList[0]["amount"]+"</td>";
                        str = str + "<td></td><td></td>";
                        str = str + "<td></td><td></td>";
                    }else if(packList.length==2)
                    {
                        str = str + "<td>"+packList[0]["manner"]+"</td><td>"+packList[0]["amount"]+"</td>";
                        str = str + "<td>"+packList[1]["manner"]+"</td><td>"+packList[1]["amount"]+"</td>";
                        str = str + "<td></td><td></td>";
                    }else if (packList.length==3){
                        str = str + "<td>"+packList[0]["manner"]+"</td><td>"+packList[0]["amount"]+"</td>";
                        str = str + "<td>"+packList[1]["manner"]+"</td><td>"+packList[1]["amount"]+"</td>";
                        str = str + "<td>"+packList[2]["manner"]+"</td><td>"+packList[2]["amount"]+"</td>";
                    }else if(packList.length==4){
                        str = str + "<td>"+packList[0]["manner"]+"</td><td>"+packList[0]["amount"]+"</td>";
                        str = str + "<td>"+packList[1]["manner"]+"</td><td>"+packList[1]["amount"]+"</td>";
                        str = str + "<td>"+packList[2]["manner"]+"</td><td>"+packList[2]["amount"]+"</td>";
                        str = str + "<td class='hd'>"+packList[3]["manner"]+"</td><td class='hd'>"+packList[3]["amount"]+"</td>";
                    }else{
                        str = str + "<td></td><td></td>";
                        str = str + "<td></td><td></td>";
                        str = str + "<td></td><td></td>";
                    }
                    str = str +"<td>" +
                        "<span class='ty-color-blue' onclick='packedit( $(this) )'>编辑</span>" +
                        "<span class='ty-color-blue' onclick='packcheck( $(this) )'>查看</span>" +
                        "<div class='hd'>" +
                        "<span class='outerID'>"+pdcus[i]["id"]  +"</span>" +
                        "</div>" +
                        "</td>" +
                        "</tr>";
                    $("#pack_tbl").append(str);
                }
            }else{
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("获取失败!");
            }
        },
        error:function(msg ){  // 失败以后的操作
            bounce.show($("#mtTip"));
            $("#mt_tip_ms").html("连接错误，请刷新重试!");
        }
    })
}

var level,jweight,pdCustomerId;// level--包装的级别   jweight--产品净重
var Timer=null;//计算保重总重量的计时器
// creator：王静，2017-08-22 8:49 包装信息-编辑
function packedit(obj){
    if (chargeRole("超管")) {
        bounce.show($("#mtTip"));
        $("#mt_tip_ms").html("您没有此权限");
    }else{
        $(".bzBigCon input").removeAttr("disabled") ;
        var tr_obj=obj.parent().parent();
        pdCustomerId=obj.siblings(".hd").children(".outerID").html();
        bounce.show($("#bpcheck"));
        $("#bpmc").html("编辑包装信息");
        $(".check_hide").hide();
        $(".level_hide").hide();
        $(".rank_hide2").hide();
        $(".rank_hide3").hide();$(".rank_hide4").hide();
        $(".bp_hide").show();
        $("#addbp1").show();
        $("#addsub").show();
        $("#bounce_over1").show();
        $("#addbp1").removeClass("ty-btn-blue").addClass("ty-btn-gray");
        $("#addbp1").attr("disabled");
        $("#addsub").removeAttr("onclick").removeClass("ty-btn-blue").addClass("ty-btn-gray").attr("disabled");
        //再次点击编辑时是空白的
        for(var i=1;i<=4;i++){
            $("#bp_amount"+i).val("");
            $("#bp_method"+i).val("");
            $("#bp_cname"+i).val("");
            $("#bp_id"+i).val("");
            $("#bp_weight"+i).val("");
            $("#bp_toweight"+i).val("");
            $("#check_amount"+i).hide();
            $("#check_method"+i).hide();
            $("#check_cname"+i).hide();
            $("#check_id"+i).hide();
            $("#check_netweight"+i).hide();
            $("#check_taweight"+i).hide();
        }
        Timer = setInterval(function(){ totalweightnew(m_current_level); }, 500);//开启计算包装后每包总重量
            //编辑提交后，再次点击编辑时，存在上次提交的内容
        $.ajax({
            url:"../pack/getPdPackListByPdBaseId.do",
            data:{ "pdCustomerId": pdCustomerId},
            success:function(res){
                var pdBase=res["pdBase"];
                var pdlist=res["pdPackList"];
                $("#outerth").html(res["outerSn"]);
                $("#outermc").html(res["outerName"]);
                $("#innerth").html(pdBase["innerSn"]);
                $("#innermc").html(pdBase["name"]);
                $("#weight").html(pdBase["netWeight"]);
                $("#unit").html(pdBase["unit"]);
                $(".sameunit").html(pdBase["unit"]);
                $("#xh").html(pdBase["model"]);
                $("#specifications").html(pdBase["specifications"]);
                pdBase["minimumiStock"]==0 ? $("#stock").html("") :  $("#stock").html(pdBase["minimumiStock"]);
                jweight=pdBase['netWeight'];
                m_current_level=1;
                $(".rank_hide1").show();
                $(".rank_hide2").hide();
                $(".rank_hide3").hide();
                $(".rank_hide4").hide();
                for( var i=1;i<= pdlist.length;i++){
                    m_current_level=pdlist.length;
                    $(".rank_hide"+i).show();
                    $("#addbp1").removeClass("ty-btn-gray").addClass("ty-btn-blue");
                    $("#addsub").removeClass("ty-btn-gray").addClass("ty-btn-blue").attr("onclick" , "bounce_OK(this)");
                    $("#addbp"+(i-1)).hide() ;
                    $("#delbp"+(i-1)).hide() ;
                    $("#bp_amount"+i).val(pdlist[i-1].amount) ;
                    $("#bp_method"+i).val(pdlist[i-1].manner) ;
                    $("#bp_cname"+i).val(pdlist[i-1]["material"]["name"]).attr("disabled",true) ;
                    $("#bp_id"+i).val(pdlist[i-1]["material"]["code"]) ;
                    $("#bp_weight"+i).val(pdlist[i-1]["material"]["netWeight"]).attr("disabled",true) ;
                    $("#bp_toweight"+i).val(pdlist[i-1].totalWeight);
                    $("#check_id" + i ).html(pdlist[i-1]["material"]["id"]) ;
                }
            },
            error:function(){ //失败以后的操作
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("连接错误，请刷新重试!");
            }
        })
    }
}
//creator：王静，2017-08-22 8:49  包装后每包总重量的计算
function totalweightnew( ){
    for(var level =1 ; level < 5 ; level++ ){
        if(level==1 || level=="1"){
            var bp_amount=$("#bp_amount1").val();
            var bp_weight=$("#bp_weight1").val();
            if(bp_amount!=""){
                if(bp_weight!=""){
                    var zweight=parseFloat(bp_amount)*parseFloat(jweight)+parseFloat(bp_weight);
                    $("#bp_toweight1").val(zweight);
                }else{
                    var zweight=parseFloat(bp_amount)*parseFloat(jweight);
                    $("#bp_toweight1").val(zweight);
                }
            }else{
                $("#bp_toweight1").val("");
            }
        }else{
            // for(level==2;level<=4;level++){
                var bp_amount=$("#bp_amount"+level).val();
                var bp_weight=$("#bp_weight"+level).val();
                var z=$("#bp_toweight"+(level-1)).val();
                var zweight;
                if(bp_amount!=""){
                    if(bp_weight!=""){
                        zweight=parseFloat(bp_amount)*parseFloat(z)+parseFloat(bp_weight);
                    }else{
                        zweight=parseFloat(bp_amount)*parseFloat(z);
                    }
                    $("#bp_toweight"+level).val(zweight);
                }else{
                    $("#bp_toweight"+level).val("");
                }
            // }
        }
    }
    
}
//creator：王静，2017-08-22 8:49  包装信息-查看
function packcheck(obj){
    var tr_obj=obj.parent().parent();
    pdCustomerId=obj.siblings(".hd").children(".outerID").html();
    bounce.show($("#bpcheck"));
    $("#bpmc").html("包装信息详情");
    $(".bp_hide").hide();
    $("#del").show();
    $.ajax({
        url:"../pack/getPdPackListByPdBaseId.do",
        data:{ "pdCustomerId": pdCustomerId},
        success:function(res){
            var pdBase=res["pdBase"];
            var pdlist=res["pdPackList"];
            $("#outerth").html(res["outerSn"]);
            $("#outermc").html(res["outerName"]);
            $("#innerth").html(pdBase["innerSn"]);
            $("#innermc").html(pdBase["name"]);
            $("#weight").html(pdBase["netWeight"]);
            $("#unit").html(pdBase["unit"]);
            $(".sameunit").html(pdBase["unit"]);
            $("#xh").html(pdBase["model"]);
            $("#specifications").html(pdBase["specifications"]);
            pdBase["minimumiStock"]==0 ? $("#stock").html("") : $("#stock").html(pdBase["minimumiStock"]); //当最低库存为空时，查看中的商品基本信息最细库存显示空
            $(".rank_hide1").show();
            $(".rank_hide2").hide();
            $(".rank_hide3").hide();
            $(".rank_hide4").hide();
            $(".sameunit").hide();
            $("#check_amount1").html("");
            $("#check_method1").html("");
            $("#check_cname1").html("");
            $("#check_id1").html("");
            $("#check_netweight1").html("");
            $("#check_taweight1").html("");
            for(var i=1;i<= pdlist.length;i++){
                $(".rank_hide"+i).show();
                $(".sameunit").show();
                $("#check_amount"+i).html(pdlist[i-1].amount).show();
                $("#check_method"+i).html(pdlist[i-1].manner).show();
                $("#check_cname"+i).html(pdlist[i-1]["material"]["name"]).show();
                $("#check_id"+i).html(pdlist[i-1]["material"]["code"]).show();
                $("#check_netweight"+i).html(pdlist[i-1]["material"]["netWeight"]).show();
                $("#check_taweight"+i).html(pdlist[i-1].totalWeight).show();
            }
        },
        error:function(){ //失败以后的操作
            bounce.show($("#mtTip"));
            $("#mt_tip_ms").html("连接错误，请刷新重试!");
        }
    })

}
//creator：王静，2017-08-22 8:49:23 编辑弹框--增加下一级包装信息，显示对应的信息
function addrank(obj,num){
    if(num==1 || num=="1"){
        bounce.show($("#bpcheck"));
        var bp_amount=$("#bp_amount1").val();
        var bp_method=$("#bp_method1").val();
        if(bp_amount=="" || bp_method==""){
            $("#addbp1").attr("disabled");
        }else{
            m_current_level=2; //点击增加时，对应的级别加1
            $(".rank_hide1").show();
            $(".rank_hide2").show();
            $(".rank_hide3").hide();
            $(".rank_hide4").hide();
            $("#addbp1").hide();
            $("#addbp2").attr("disabled");
            $("#addbp2").removeClass("ty-btn-blue").addClass("ty-btn-gray");
            $("#addsub").removeClass("ty-btn-blue").addClass("ty-btn-gray").removeAttr("onclick") ;
        }
    }
    if(num==2||num=="2"){
        bounce.show($("#bpcheck"));
        var bp_amount=$("#bp_amount2").val();
        var bp_method=$("#bp_method2").val();
        if(bp_amount=="" || bp_method==""){
            $("#addbp2").removeClass("ty-btn-blue").addClass("ty-btn-gray").attr("disabled");
            $("#addsub").removeClass("ty-btn-blue").addClass("ty-btn-gray").attr("disabled");
        }else{
            m_current_level=3;
            $(".rank_hide2").show();
            $(".rank_hide1").show();
            $(".rank_hide3").show();
            $(".rank_hide4").hide();
            $("#addbp1").hide();
            $("#addbp2").hide();
            $("#delbp2").hide();
            $("#addbp3").removeClass("ty-btn-blue").addClass("ty-btn-gray").attr("disabled");
            $("#addsub").removeClass("ty-btn-blue").addClass("ty-btn-gray").removeAttr("onclick");
        }
    }
    if(num==3 || num=="3"){
        bounce.show($("#bpcheck"));
        var bp_amount=$("#bp_amount3").val();
        var bp_method=$("#bp_method3").val();
        if(bp_amount=="" || bp_method==""){
            $("#addbp3").attr("disabled");
        }else{
            m_current_level=4;
            $(".rank_hide1").show();
            $(".rank_hide2").show();
            $(".rank_hide3").show();
            $(".rank_hide4").show();
            $("#delbp2").hide(); $("#delbp3").hide();
            $("#addbp1").hide(); $("#addbp2").hide(); $("#addbp3").hide();
            $("#addbp3").removeClass("ty-btn-gray").addClass("ty-btn-blue").removeAttr("disabled");
            $("#addsub").removeClass("ty-btn-blue").addClass("ty-btn-gray").removeAttr("onclick");
        }
    }
}
//creator：王静，2017-08-22 8:49:56 编辑弹窗--包装信息中取消此级包装方式按钮
function dele(num){
    $("#addbp"+(num-1)).show();
    $(".rank_hide"+num).hide();
    m_current_level=num-1;
    $("#addbp1").removeClass("ty-btn-gray").addClass("ty-btn-blue").removeAttr("disabled");
    $("#addsub").removeClass("ty-btn-gray").addClass("ty-btn-blue").removeAttr("disabled").attr("onclick" , "bounce_OK(this)");
    $("#delbp"+(num-1)).show();
    $("#bp_amount"+num).val("");
    $("#bp_method"+num).val("");
    $("#bp_cname"+num).val("").attr("disabled",false);
    $("#bp_id"+num).val("");
    $("#bp_weight"+num).val("").attr("disabled",false);
    $("#bp_toweight"+num).val("");
}
// creator：王静，2017-08-22 8:50 编辑中必填项未填完，增加、提交不可点
function changeColor(num){
    var bp_amount=$("#bp_amount"+num).val();
    var bp_method=$("#bp_method"+num).val();
    var mtcode = $("#bp_id"+num).val() ; 
    var name = $("#bp_cname"+num).val() ;
    if($.trim(bp_amount)=="" || $.trim(bp_method)==""|| $.trim(mtcode)==""|| $.trim(name)==""){
        $("#addbp"+num).removeClass("ty-btn-blue").addClass("ty-btn-gray").attr("disabled");
        $("#addsub").removeAttr("onclick").removeClass("ty-btn-blue").addClass("ty-btn-gray").attr("disabled");
    }else{
        $("#addbp"+num).removeClass("ty-btn-gray").addClass("ty-btn-blue").removeAttr("disabled");
        $("#addsub").removeClass("ty-btn-gray").addClass("ty-btn-blue").removeAttr("disabled").attr("onclick" , "bounce_OK(this)");
    }
}

var bpTimer=null, matchBpTimer=null;
var orl_Mtval = "";  // 定时器中上次搜索代号的值
//creator：王静，2017-08-22 8:55 包装材料代号的搜索
function startBpmat(obj,event,num){
    stopIntervalbp() ;
    orl_Mtval = "";
    bpTimer = setInterval(function(){ searchbp(obj.val(),num); }, 500); //搜索物料代号的定时器
    matchBpTimer = setInterval(function () { matchbpCode(num); },200) ; //匹配物料代号的定时器
    event.stopPropagation();
}
//creator：王静，2017-08-22 8:59 搜索物料代号
function searchbp(val,num){
    if(orl_Mtval != val ) {
        orl_Mtval = val;
        //新增物料代号时，物料代号的下拉列表不显示
        if(val ==""){
            $("#bpCon"+num).hide();
            return false;
        }
        $.ajax({
            url: "../pack/getPackBaseByCode.do",
            type: "post",
            data: {"code": val},
            success: function (data) {
                $("#bpCon"+num).empty();
                var res = data["mtBaseList"];
                var str="";
                if (res && res.length > 0) {
                    $("#bpCon"+num).show();
                    for (var i = 0; i < res.length; i++) {
                         str += "<div class='item' onclick=\"selectThisBp($(this),"+num+")\" data-mtid=\""+res[i]["id"]+"\" ><span class='val' >" + JSON.stringify(res[i]) + "</span><span class='itemcode'>" + res[i]["code"] + "</span></div>";
                    }
                    $("#bpCon"+num).html(str);
                }else{
                    $("#bpCon"+num).hide();
                }
            },
            error: function () { //失败以后的操作
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("连接错误，请刷新重试!");
            }
        })
    }
}
//creator：王静，2017-08-22 9:00 匹配物料代号
function matchbpCode(num){
    var code=$("#bp_id"+num).val();
    var isHasMatch = false;
    $("#bpCon"+num).find(".item").each(function(){
        var item_code=$(this).children(".itemcode").html();
        if(item_code==code){
            isHasMatch=true;
            var val=$(this).children(".val").html();
            val = JSON.parse(val);//将字符串转换成对象
            $("#bp_cname"+num).val(val["name"]).attr("disabled",true);
            $("#bp_weight"+num).val(val["netWeight"]).attr("disabled",true);
            $("#check_id" + num).html(val["id"]) ; 
            return false;
        }
    }) ;  
    if(!isHasMatch){
        $("#bp_cname"+num).attr("disabled",false) ;
        $("#bp_weight"+num).attr("disabled",false) ;
        $("#check_id" + num).html("") ;
    }
}
//creator：王静，2017-08-22 9:05:43 用户选择包装材料代号
function selectThisBp(obj,num){
    var bpInfo = JSON.parse( obj.children(".val").html() ) ;
    stopIntervalbp( num ) ;
    $("#bp_id"+num).val(bpInfo["code"]) ;
    $("#check_id"+num).html(bpInfo["id"]) ;
    $("#bp_weight"+num).val(bpInfo["netWeight"]).attr("disabled","true") ;
    $("#bp_cname"+num).val(bpInfo["name"]).attr("disabled","true") ;
    $("#bpCon"+num).hide();
}
//creator：王静，2017-08-30 9:29:30 停止搜索以及匹配物料代号的定时器
function stopIntervalbp(){
    clearInterval(bpTimer) ;
    clearInterval(matchBpTimer) ;
    for(var i = 1 ; i < 5 ; i++){
        $("#bpCon"+ i ).hide() ;
    }

}
//creator：王静，2017-08-22 8:57 编辑--提交保存包装信息
function bounce_OK(obj){
    console.log(m_current_level) ;
    for(var i = 1 ; i <= m_current_level ; i++){
        var val = $("#bp_id" + i ).val() ;
        if($.trim(val) == ""){ return false ;  }
    }
    if($("#bp_amount"+m_current_level).val()=="" || $("#bp_method"+m_current_level).val()==""){
        return false;
    }else{
        clearInterval(Timer);
        /*var bp_amount1=$("#bp_amount1").val();
        var bp_method1=$("#bp_method1").val();
        var bp_amount2=$("#bp_amount2").val();
        var bp_method2=$("#bp_method2").val();
        var bp_amount3=$("#bp_amount3").val();
        var bp_method3=$("#bp_method3").val();
        var bp_amount4=$("#bp_amount4").val();
        var bp_method4=$("#bp_method4").val();*/ 
        var mtId; //mtId 获取的物料id
        var data = { "packList": [] ,"pdCustomerId":pdCustomerId};
        for(var i=1;i<=m_current_level;i++){
            var _mtId = $.trim( $("#check_id"+i).html() ) ;
            data.packList.push({
                "level": i ,
                "amount": $("#bp_amount"+i).val() ,
                "manner": $("#bp_method"+i).val() ,
                "mtId": _mtId , //如果是新增的包装材料代号，则物料代号为空
                "mtName": $("#bp_cname"+i).val() ,
                "mtCode": $("#bp_id"+i).val() ,
                "mtWeight": Number($("#bp_weight"+i).val() ) ,
                "totalWeight": Number( $("#bp_toweight"+i).val() )  
            });
        }
        $.ajax({
            url:"../pack/editPack.do",
            type:"POST",
            traditional:true,
            data:{"pdCustomerId":pdCustomerId,"packList":JSON.stringify(data.packList)},
            dataType:"json",
            success:function(data){
                if(data["status"] == 1){
                    location.reload(true);
                }else if(data["status"] == 2){
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("包装材料代号有重复，请修改重试!");
                }else{
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("链接错误，请刷新重试!");
                }
            },
            error:function(){
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("连接错误，请刷新重试!");
            }
        })
    }

}