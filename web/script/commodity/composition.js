
$(function(){
	//getComposionList( 1, 10 , "");
});
var clickStatus = "";  // 标记是编辑还是查看 ， 1:编辑 ； 0:查看 ；
var editTrObj_Array = [] ; // 存储 所有编辑操作的“行”对象
var curEditSelectObj = null ; // 标记当前操作的 来源或者构成的 select对象
var compotionContainer_Array = []; // 存储 所有的构成弹框对象，第一个在编辑里面（非弹框）
// 回到主页面 , 返回上一级
//updator: 王静 2017-08-23 9:34:12 弹框左侧箭头的返回
function showMain(){
    $(".bounce").hide();
    $(".bounceCompotion").remove();

}
// 获取构成主列表
//updator:王静 2017-08-24 9:02:45 获取构成主列表
function getComposionList(cur , per , innerSn){
	$.ajax({
		url:"getPdComposition.do",
		data:{ "innerSn": innerSn , "pageNumber":cur , "quantum":per },
		type:"post",
		dataType:"json" ,
		beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
		success:function(data){
			var pdBases = data["pdBases"];
			var cur = data["pageNumber"];
			var countAll = data["totalPage"];
			var json_innerSn = { "innerSn": innerSn  };
			$("#compotionList").html(""); //表格中列表的自动获取
			setPage($("#compositionYe") , cur , countAll , "composition" , JSON.stringify(json_innerSn) ) ; //设置页数
			if(pdBases.length > 0 ){
				for( var i = 0 ; i < pdBases.length; i++ ){
					var no = i + 1 ;
					var str = "<tr>" +
						"<td>"+ no +"</td>" +
						"<td>"+ pdBases[i]["innerSn"] +"</td>" +
						"<td>"+ pdBases[i]["name"] +"</td>" +
						"<td>"+ pdBases[i]["unit"] +"</td>" +
						"<td>" +
						"<span>"+ chargeSouce(pdBases[i]["source"]) +"</span>" + //来源
						"<span class='sourcevalue hd'>"+ pdBases[i]["source"] +"</span>" +
						"</td>" +
						"<td>" +
						"<span>"+ chargeComposition(pdBases[i]["composition"]) +"</span>" + //构成
						"<span class='compotionvalue hd'>"+ pdBases[i]["composition"] +"</span>" +
						"</td>" +
						"<td>"+ pdBases[i]["netWeight"] +"</td>" +
						"<td>"+ pdBases[i]["stuff"] +"</td>" +
						"<td>"+ pdBases[i]["memo"] +"</td>" +
						"<td>" +
						"<span class='ty-color-blue' onclick='compositeEditBtn($(this), 1 )'>编辑</span>" +
						"<span class='ty-color-blue' onclick='compositeEditBtn($(this) , 0 )'>查看</span>" +
						"<div class='hd'>" +
						"<span class='goodsID'>"+ pdBases[i]["id"] +"</span>" +
						"<span class=''></span>" +
						"</div>" +
						"</td>" +
						"</tr>";
					$("#compotionList").append(str);
				}
			}else{
				var str = "<tr ><td colspan='10'>暂无数据！</td></tr>";
				$("#compotionList").append(str);
			}
		},
		error:function(msg){
			$("#mt_tip_ms").html("链接错误，请刷新重试！");
			bounce.show($("#mtTip"));
		} ,
		complete:function(){ chargeClose(1) ;  }
	});
}
// 列表 编辑/查看 构成
//updator:王静 2017-08-24 9:00:15 商品构成列表 编辑/查看
function compositeEditBtn(obj , num) {
	if( num == 1){ //  超管不能编辑
        if (chargeRole("超管")) { // 0 表示超管
			bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
		}
	}
	//var editTrObj = obj.parent().parent();
	var id = '';
	if (obj != null){
        id = obj.data("id");
	}else{
        id = $('#seeProduct').data('id');
	}
	if(num == null || num == undefined){
		$("#mt_tip_ms").html("获取点击类型失败，请刷新重试！");
		bounce.show($("#mtTip"));
		return false;
	}else{
		clickStatus = num ;//编辑还是查看
	}
	if(id == null || id == undefined || id == ""){
		$("#mt_tip_ms").html("获取商品基本信息失败，请刷新重试！");
		bounce.show($("#mtTip"));
		return false ;
	}
	$.ajax({
		url:"getCompositionByPdId.do",
		data:{ "id": id },
		type:"post",
		dataType:"json" ,
		success:function( data ){
			console.log(data);
			var pdBase = data["pdBase"];
			var compositionList = data["compositionList"];
			//editTrObj_Array.push(editTrObj);   //在编辑的行对象中末尾添加新的编辑对象
			bounce.show($(".bounceCompotion"));
			CompotionDiv.setBaseContainer(clickStatus , pdBase["innerSn"] , pdBase["name"] , pdBase["unit"] , pdBase["source"] , pdBase["composition"] , pdBase["stuff"] , pdBase["memo"] , compositionList ,$(".bounce"), id , "gs" , "");

		},
		error:function(msg){
			$("#mt_tip_ms").html("连接失败，请稍后重试！");
			bounce.show($("#mtTip"))
		}
	});

}
// 生成构成的下拉列表
//updator:王静 2017-08-23 11:07:05 生成构成的下拉列表
var CompotionDiv = {
	"setBaseContainer":function( type ,  innerSn , name , unit , source , composition , material , memo , compotionListObj , containerObj , goodsID , goodsType , parentCompotionID ,cateroy ){
		// Param  type:0 查看 ； 1 编辑 ,
		// Param  内部图号 ， 内部名称 ， 单位 ， 来源 ， 构成 ， 材料 ， 备注 ,
		// Param  compotionListObj:构成列表
		// Param  containerObj  :容器对象
		// Param  goodsID : 编辑/查看 的 这个商品的id
		// Param  goodsType : 编辑/查看 的 这个商品的 类型 ："mt":表示这个商品是拆分时新增加的商品 ；"gs":表示这是一个真正的商品
		// Param  parentCompotionID  : 构成方式
		// Param  cateroy  : 商品所属的物料类型， 编辑商品（第一层）时不会显示，编辑半成品（2+ 层）时会显示
		//  TODO 搭建基本页面，初始化控件
		if( type == "1" || type == 1){ // 编辑
			var str_source = "<select class='sourceSelecter' onchange='changeSource( $(this) , 0 )'><option value='1' >外购</option><option value='2'>自制</option></select>" ;
			var str_composition = "<select class='compotionSelecter' onchange='chargeComposition( $(this) , 1  )'><option value='1' >购买</option></select>" ;

			if(source == 1 || source == "1"){ //来源 外购
				str_source =  "<select class='sourceSelecter' onchange='changeSource( $(this) , 0  )'><option value='1' selected='selected'>外购</option><option value='2'>自制</option></select>" ;
			}else if(source == 2 || source == "2"){ //来源 自制
				str_source =  "<select class='sourceSelecter' onchange='changeSource( $(this) , 0  )'><option value='1'>外购</option><option value='2' selected='selected'>自制</option></select>" ;
				str_composition =  "<select class='compotionSelecter' onchange='chargeComposition( $(this) , 1  )'><option value='2' >制造</option><option value='3'>装配</option></select>" ;
			}
			var str_cateroy = "";
			if(compotionContainer_Array.length >= 1){
				str_cateroy = "<div class='bounceH_4'><span class='bounceH_ttl2'>物料类型：</span>";
				if(cateroy == 3 || cateroy == "3"){
					str_cateroy += "<select class='cateroySelecter' onchange='changeSource( $(this) , 2 )'><option value='3' >半成品</option></select>" ;
				}
				else{
					str_cateroy += "<select class='cateroySelecter' onchange='changeSource( $(this) , 2 )'><option value='4' >外购成品</option></select>" ;
				}
				// else if(cateroy == 4 || cateroy == "4"){
				// 	str_cateroy += "<select class='cateroySelecter' onchange='changeSource( $(this) , 2 )'><option value='4' selected='selected'>外购成品</option><option value='5'>包装材料</option></select>" ;
				// }else if(cateroy == 5 || cateroy == "5"){
				// 	str_cateroy += "<select class='cateroySelecter' onchange='changeSource( $(this) , 2 )'><option value='4'>外购成品</option><option value='5' selected='selected'>包装材料</option></select>" ;
				// }else{
				// 	str_cateroy += "<select class='cateroySelecter' onchange='changeSource( $(this) , 2 )'><option value='4' selected='selected'>外购成品</option><option value='5'>包装材料</option></select>" ;
				// }
				str_cateroy += "<span class='hd cateroyVal'>"+ cateroy +"</span></div> " ;
			}

			if(composition == 2 || composition == "2"){
				str_composition =  "<select class='compotionSelecter' onchange='chargeComposition( $(this) , 1  )'><option value='2' selected='selected'>制造</option><option value='3'>装配</option></select>" ;
			}else if(composition == 3 || composition == "3"){
				str_composition =  "<select class='compotionSelecter' onchange='chargeComposition( $(this) , 1  )'><option value='2'>制造</option><option value='3' selected='selected'>装配</option></select>" ;
			}
			// 构成列表
			var str_compotionList = "<table class='ty-table ty-table-control'>" ;
			var matetialVal_isable = "";
			if(composition == 2 || composition == "2") { // 制造 , 物料
				matetialVal_isable = " disabled = 'true' ";
				str_compotionList += "<thead>" +
					"<td width='15%'>物料名称</td>" +
					"<td width='15%'>代号</td>" +
					"<td width='14%'>规格</td>" +
					"<td width='14%'>型号</td>" +
					"<td width='15%'>物料类型</td>" +
					"<td width='12%'>配比比例</td>" +
					"<td width='15%'>操作</td>" +
					"</thead><tbody class='compositionTbl'>";
			}else if(composition == 3 || composition == "3") { // 装配 ， 商品
				str_compotionList += "<thead>" +
					"<td width='10%'>代号</td>" +
					"<td width='10%'>名称</td>" +
					"<td width='10%'>单位</td>" +
					"<td width='10%'>装配数量</td>" +
					"<td width='10%'>规格</td>" +
					"<td width='10%'>型号</td>" +
					"<td width='5%'>来源</td>" +
					"<td width='5%'>构成</td>" +
					// "<td width='10%'>物料类型</td>" +
					"<td width='30%'>操作</td>" +
					"</thead><tbody class='compositionTbl'>";
			}
			if( compotionListObj != null && typeof(compotionListObj) == "object" && compotionListObj.length > 0 ){
				for( var i = 0 ; i < compotionListObj.length ; i++ ){
					var com = compotionListObj[i]["compotion"] ;
					var code = code = compotionListObj[i]["innerSn"] ;
					if( com == 2 || com == "2" || com == 3 || com == "3"|| com == 1 || com == "1"){  code = compotionListObj[i]["product"]["code"] ; }

					if(composition == 2 || composition == "2"){ // 制造 , 物料
						var str_item = "<tr>" +
							"<td>"+ compotionListObj[i]["name"]  +"</td>" +
							"<td>"+ code  +"</td>" +
							"<td>"+ compotionListObj[i]["specifications"]  +"</td>" +
							"<td>"+ compotionListObj[i]["model"]  +"</td>" +
							"<td>"+ compotionListObj[i]["categoryName"]  +"</td>" +
							"<td>"+ compotionListObj[i]["matching"]  +"</td>" +
							"<td> " +
							"<span class='ty-color-blue' onclick='composition_2_update($(this))'>修改</span>" +
							"<span class='ty-color-red' onclick='composition_2_del($(this))'>删除</span>" +
							"<div class='hd'>" +
							"<span class='mt_id'>"+ compotionListObj[i]["material_"]  +"</span> " +
							"<span class='compotion_id'>"+ compotionListObj[i]["id"] +"</span> " +
							"</div>" +
							"</td></tr>" ;
					}else if(composition == 3 || composition == "3"){ // 装配 ， 商品
						var charge = chargeGoodsType( compotionListObj[i]["product_"] ,  compotionListObj[i]["material_"]) ;
						var gs_id = charge.goodsID ;
						var gs_type = charge.goodsKind ;
						var gs_no = "";
						if( gs_type == "mt" ){ gs_no = compotionListObj[i]["innerSn"] ;    }
						else{ /*gs_no = compotionListObj[i]["product"]["code"] ;*/ gs_no = compotionListObj[i]["innerSn"] ;   }
						var str_item = "<tr>" +
							"<td>"+ code  +"</td>" + // 代号
							"<td>"+ compotionListObj[i]["name"]  +"</td>" +
							"<td>"+ compotionListObj[i]["unit"]  +"</td>" +
							"<td>"+ compotionListObj[i]["amount"]  +"</td>" +
							"<td>"+ compotionListObj[i]["specifications"]  +"</td>" +
							"<td>"+ compotionListObj[i]["model"]  +"</td>" +
							"<td>" +
							"<span>"+ chargeSouce(compotionListObj[i]["source"]) +"</span>" +
							"<span class='sourcevalue hd'>"+ compotionListObj[i]["source"] +"</span>" +
							"</td>" +
							"<td>" +
							"<span>"+ chargeComposition(compotionListObj[i]["composition"]) +"</span>" +
							"<span class='compotionvalue hd'>"+ compotionListObj[i]["composition"] +"</span>" +
							"</td>" +
							// "<td>"+ compotionListObj[i]["categoryName"] +"</td>"+
							"<td>" +
							"<span class='ty-color-blue' onclick='compotion_3_update($(this))'>修改</span>" +
							"<span class='ty-color-blue' onclick='compotion_3_edit($(this))'>编辑</span>" +
							"<span class='ty-color-red' onclick='compotion_3_del($(this))'>删除</span>" +
							"<div class='hd'>" +
							"<span class='gs_id'>"+ gs_id +"</span> " +
							"<span class='compotion_id'>"+ compotionListObj[i]["id"] +"</span> " +
							"<span class='gs_type'>"+ gs_type +"</span> " +
							"</div>" +
							"</td></tr>" ;
					}
					str_compotionList += str_item ;
				}
			}else {
				str_compotionList += "</tbody></table>";
			}
			var str_addBtn = "<div class='addBtnContainer'>";
			if( composition == 3 || composition == "3" ){ //装配
				str_addBtn += "<span onclick='addGoods($(this))'> 拆分 <span>" + name + "</span><span>" + innerSn + "</span> </span>";
			}else{
				str_addBtn += "<span onclick='addMaterial($(this))'> 录入 <span>" + name + "</span><span>" + innerSn + "</span> 原辅材料</span>";
			}
			str_addBtn +="</div>";
			var newLevel = editTrObj_Array.length ;
			var _length = compotionContainer_Array.length ;
			var isMove = "";
			var ttl_1 = "商品构成编辑";
			if( _length >= 1 ){
				ttl_1 = "半成品构成编辑";
			}
			if(newLevel >= 1){ isMove = " onmousedown='setMove_KeyDown($(this) , event )'" ; } //设置弹框的移动

			//构成编辑的头部信息  str_backContainer
			var str_backContainer = "<div class='bounceCompotion_titl' "+ isMove +" >" +
				"<span class='pic' onclick='showMain()'></span>" +
				"<span class='ttl'>"+ ttl_1 +"</span>" +
				"<a class='ty-btn ty-btn-big ty-btn-blue resourceAddBtn save-btn' onclick='saveBigCompotion($(this))'>保存</a>" +
				"<div class='clr'></div>" +
				"</div>" ;

			var _positionStr = "" ;
			if(_length > 1){
				var last = compotionContainer_Array[_length-1].offset();
				var _left = (_length-1)*15;
				var _top = (_length-1)*15;
				if(_length > 100){ _top = (_length - 100)*15; }
				_positionStr = "style = 'left:"+ _left +"; top:" + _top + ";'";
			}
			var str= "<div class='bounceCompotion' style='background-color:white;margin:0 auto;width:50%;margin-top:10%;'"+ _positionStr +"><div class='keep' onclick='giveTip($(this))'></div>"  + str_backContainer ;
			str +=	"<div class='bounceCompotion_head '>" +
				"<div>" +
				"<span class='bounceH_ttl'>内部图号：</span><span class='bounceH_con'>"+ innerSn +"</span>" +
				"<span class='bounceH_ttl'>内部名称：</span><span class='bounceH_con'>"+ name +"</span>" +
				"<span class='bounceH_ttl'>单位：</span><span class='bounceH_con'>"+ unit +"</span>" +
				"<div class='hd'>" +
				"<span class='goodsID'>"+ goodsID +"</span>" +
				"<span class='goodsType'>"+ goodsType +"</span>" +
				"<span class='parentCompotionID'>"+ parentCompotionID +"</span>" +
				"</div>" +
				"</div>" +
				"<div>" +
				"<div class='bounceH_4'>" +
				"<span class='bounceH_ttl2'>来源：</span>" + str_source + "<span class='hd sourceVal'>"+ source +"</span>" +
				"</div>" +
				"<div class='bounceH_4'>" +
				"<span class='bounceH_ttl2'>构成：</span>" + str_composition +"<span class='hd compotionVal'>"+ composition +"</span>" +
				"</div>" +

				"<div class='bounceH_4'>" +
				"<span class='bounceH_ttl2'>材料：</span><input type='text' "+ matetialVal_isable +" class='matetialVal' value='"+ material +"'>" +
				"</div>" +
				"<div class='bounceH_4'>" +
				"<span class='bounceH_ttl2'>备注：</span><input type='text' class='memoVal' value='"+ memo +"'>" +
				"</div>" + str_cateroy +
				"<div class='clr'></div>" +
				"</div>" +
				"</div>" ;
			if(source == 2 || source == "2"){ // 自制
				str += "<div class='bounceCompotion_con'>" + str_addBtn + str_compotionList +"</div>" +
					"</div>" ;
			}else{  // 外购
				str += "<div class='bounceCompotion_con hd'>" + str_addBtn + str_compotionList +"</div>" +
					"</div>" ;
			}
			containerObj.append(str);
			// compotionContainer_Array.pop();

		}else if( type == "0" || type == 0){  // 查看
			var str_source = "" ;
			if(source == 1 || source == "1"){
				str_source =  "外购" ;
			}else if(source == 2 || source == "2"){ str_source =  "自制" ;	}
			var str_composition = "" ;
			if(composition == 1 || composition == "1"){
				str_composition = "购买" ;
			}else if(composition == 2 || composition == "2"){ str_composition =  "制造" ; 	}else if(composition == 3 || composition == "3"){
				str_composition =  "装配" ;
			}
			var newLevel = editTrObj_Array.length ; //获取编辑行对象数组的长度
			var _length = compotionContainer_Array.length ; //获取弹框对象的长度
			var isMove = "" ;
			var _scanTtl = "商品构成查看" ;
			if(_length >0){ _scanTtl = "半成品构成查看" ;  }//若compotionContainer_Array的长度>0说明有弹出的对象
			if(newLevel >1){ isMove = " onmousedown='setMove_KeyDown($(this) , event )'" ; }

			var str_backContainer = "<div class='bounceCompotion_titl' "+ isMove +">" +
				"<span class='pic' onclick='showMain()'></span>" +
				"<span>"+ _scanTtl +"</span>" +
				"<div class='clr'></div>" +
				"</div>" ;
			var _positionStr = "" ;
			if(_length > 1){
				var last = compotionContainer_Array[_length-1].offset();
				var _left = (_length-1)*15;
				var _top = (_length-1)*15;
				if(_length >100){ _top = (_length - 100)*15; }
				_positionStr = "style = 'left:"+ _left +"; top:" + _top + ";'";
			}
			var str_compotionList = "<table class='ty-table ty-table-control'>" ;
			var matetialVal_isable = "" ;
			if(composition == 2 || composition == "2") { // 制造 , 物料
				matetialVal_isable = " disabled = 'true' ";
				str_compotionList += "<thead>" +
					"<td width='15%'>物料名称</td>" +
					"<td width='15%'>代号</td>" +
					"<td width='14%'>规格</td>" +
					"<td width='14%'>型号</td>" +
					"<td width='15%'>物料类型</td>" +
					"<td width='12%'>配比比例</td>" +
					"</thead><tbody class='compositionTbl'>";
			}else if(composition == 3 || composition == "3") { // 装配 ， 商品
				str_compotionList += "<thead>" +
					"<td width='10%'>代号</td>" +
					"<td width='10%'>名称</td>" +
					"<td width='10%'>单位</td>" +
					"<td width='10%'>装配数量</td>" +
					"<td width='10%'>规格</td>" +
					"<td width='10%'>型号</td>" +
					"<td width='5%'>来源</td>" +
					"<td width='5%'>构成</td>" +
					// "<td width='10%'>物料类型</td>" +
					"<td width='30%'>操作</td>" +
					"</thead><tbody class='compositionTbl'>";
			}
			if( compotionListObj != null && typeof(compotionListObj) == "object" && compotionListObj.length > 0 ){
				for( var i = 0 ; i < compotionListObj.length ; i++ ){
					var com = compotionListObj[i]["compotion"] ;
					var code = code = compotionListObj[i]["innerSn"] ;
					if( com == 2 || com == "2" || com == 3 || com == "3"|| com == 1 || com == "1"){  code = compotionListObj[i]["product"]["code"] ; }
					if(composition == 2 || composition == "2"){ // 制造 , 物料
						var str_item = "<tr>" +
							"<td>"+ compotionListObj[i]["name"]  +"</td>" +
							"<td>"+ code  +"</td>" +
							"<td>"+ compotionListObj[i]["specifications"]  +"</td>" +
							"<td>"+ compotionListObj[i]["model"]  +"</td>" +
							"<td>"+ compotionListObj[i]["categoryName"]  +"</td>" +
							"<td>"+ compotionListObj[i]["matching"]  +"</td></tr>" ;

					}else if(composition == 3 || composition == "3"){ // 装配 ， 商品
						var charge = chargeGoodsType( compotionListObj[i]["product_"] ,  compotionListObj[i]["material_"]) ;
						var gs_id = charge.goodsID ;
						var gs_type = charge.goodsKind ;
						var gs_no = "";
						if( gs_type == "mt" ){ gs_no = compotionListObj[i]["innerSn"] ;    }
						else{ gs_no = compotionListObj[i]["innerSn"] ;   }
						var strControl_see = "";
						if(Number(compotionListObj[i]["source"]) == 2){
							strControl_see = "<span class='ty-color-blue' onclick='compotion_3_edit($(this))'>查看</span>"  ;
						}
						var str_item = "<tr>" +
							"<td>"+ code  +"</td>" + // 代号
							"<td>"+ compotionListObj[i]["name"]  +"</td>" +
							"<td>"+ compotionListObj[i]["unit"]  +"</td>" +
							"<td>"+ compotionListObj[i]["amount"]  +"</td>" +
							"<td>"+ compotionListObj[i]["specifications"]  +"</td>" +
							"<td>"+ compotionListObj[i]["model"]  +"</td>" +
							"<td>" +
							"<span>"+ chargeSouce(compotionListObj[i]["source"]) +"</span>" +
							"<span class='sourcevalue hd'>"+ compotionListObj[i]["source"] +"</span>" +
							"</td>" +
							"<td>" +
							"<span>"+ chargeComposition(compotionListObj[i]["composition"]) +"</span>" +
							"<span class='compotionvalue hd'>"+ compotionListObj[i]["composition"] +"</span>" +
							"</td>" +
							// "<td>"+ compotionListObj[i]["categoryName"] +"</td>"+
							"<td>" + strControl_see +
							"<div class='hd'>" +
							"<span class='gs_id'>"+ gs_id +"</span> " +
							"<span class='compotion_id'>"+ compotionListObj[i]["id"] +"</span> " +
							"<span class='gs_type'>"+ gs_type +"</span> " +
							"</div>" +
							"</td></tr>" ;
					}
					str_compotionList += str_item ;
				}
			}else {
				str_compotionList += "</tbody></table>";
			}

			var str = "<div class='bounceCompotion' style='background-color:white;margin:0 auto;width:50%;margin-top:10%;'"+ _positionStr +"><div class='keep' onclick='giveTip($(this))'></div>" + str_backContainer +
				"<div class='bounceCompotion_head'>" +
				"<div>" +
				"<span class='bounceH_ttl'>内部图号：</span><span class='bounceH_con'>"+ innerSn +"</span>" +
				"<span class='bounceH_ttl'>内部名称：</span><span class='bounceH_con'>"+ name +"</span>" +
				"<span class='bounceH_ttl'>单位：</span><span class='bounceH_con'>"+ unit +"</span>" +
				"<div class='hd'>" +
				"<span class='goodsID'>"+ goodsID +"</span>" +
				"<span class='goodsType'>"+ goodsType +"</span>" +
				"<span class='parentCompotionID'>"+ parentCompotionID +"</span>" +
				"</div>" +
				"</div>" +
				"<div>" +
				"<span class='bounceH_ttl'>来源：</span><span class='bounceH_con'>"+ str_source +"</span>" +
				"<span class='bounceH_ttl'>构成：</span><span class='bounceH_con'>"+ str_composition +"</span>" +
				"<span class='bounceH_ttl'>材料：</span><span class='bounceH_con'>"+ material +"</span>" +
				"<span class='bounceH_ttl'>备注：</span><span class='bounceH_con'>"+ memo +"</span>" +
				"</div>" +
				"</div>" ;
			var _idshow = " hd";
			if(Number(source) == 2){ _idshow = "" ; }
			str += "<div class='bounceCompotion_con "+ _idshow +"'>" + str_compotionList +"</div>" +
				"</div>" ;
			containerObj.append(str);
		}
		if( compotionContainer_Array.length == 0){
			compotionContainer_Array.push(containerObj.children(":last"));
		}else{
			var curContainer = $("#compotionBounce").children(":last");
			compotionContainer_Array.push(curContainer); // 将构成弹框添加到存储数组
		}
	},
	//  构成列表的显示
	//updator:王静 2017-08-28 8:34:23 构成列表的显示
	"bounceCompotion_con_Show":function( compotion , source , cateroy , num ){
		// source:1 外购 ，2 自制 ;
		// composition ： 1 购买 ， 2 制造 ， 3 装配
		// cateroy : 3 半成品  4 外购成品 5 包装材料
		var bunceCur = compotionContainer_Array;

		// 设置三个select 后面的 selectVal 值
		var curSelectTr = curEditSelectObj.parent().parent() ;
		var souceSelectObj = curSelectTr.children(":eq(0)") ;
		var compotionSelectObj = curSelectTr.children(":eq(1)") ;
		var cateroySelectObj = curSelectTr.children(":eq(4)") ;
		souceSelectObj.children(".sourceVal").html(source);
		compotionSelectObj.children(".compotionVal").html(compotion);
		if( bunceCur.length>1){ cateroySelectObj.children(".cateroyVal").html(cateroy);}
		// 设置三个select的值
		switch (Number(num)){
			case 0: // 修改来源
				if(Number(source) == 1 ){
					var strnum_compotion = "<option>购买</option>" ;
					compotionSelectObj.children("select").html(strnum_compotion);
					var strnum_cateroy = "<select class='cateroySelecter' onchange='changeSource( $(this) , 2)'><option value='4'>外购成品</option></select>";
					if(bunceCur.length>1){
						cateroySelectObj.children("select").remove();
						cateroySelectObj.append(strnum_cateroy);
					}
					// cateroySelectObj.children("select").html(strnum_cateroy);
				}else if(Number(source) == 2 ){
					var strnum_compotion = "<select class='compotionSelecter' onchange='changeSource( $(this) , 1)'><option value='2' selected='selected'>制造</option><option value='3'>装配</option></select>" ;
					if(Number(compotion) == 3){
						strnum_compotion = "<select class='compotionSelecter' onchange='changeSource( $(this) , 1)'><option value='2'>制造</option><option value='3' selected='selected'>装配</option></select>" ;
					}
					compotionSelectObj.children("select").remove();
					compotionSelectObj.append(strnum_compotion);
					var strnum_cateroy="<select class='cateroySelecter' onchange='changeSource( $(this) , 2 )'><option value='3' >半成品</option></select>";
					if(bunceCur.length>1){
						cateroySelectObj.children("select").html(strnum_cateroy);
					}
				}
				break;
			case 1: // 修改构成

				break;
			case 2: // 修改 物料类型

				break;
			default:
				console.log(Number(num));
				console.log("未能识别");
				break;


		}

		var obj = bunceCur[ bunceCur.length - 1 ].children(".bounceCompotion_con");
		// 设置下面 table 构成列表
		if(Number(source) == 1 ){
			obj.hide().html("");
		}else{
			var name = obj.prev().children(":eq(0)").children(":eq(3)").html(); //内部名称
			var innerSn = obj.prev().children(":eq(0)").children(":eq(1)").html(); //内部图号
			var str_compotionList = "" ;
			if(Number(compotion) == 3 ) { // 装配 ， 商品
				var str_addBtn = "<div class='addBtnContainer'><span onclick='addGoods($(this))'> 拆分 <span>" + name + "</span><span>" + innerSn + "</span> </span></div>";
				str_compotionList += str_addBtn + "<table class='ty-table ty-table-control'><thead>" +
					"<td width='10%'>代号</td>" +
					"<td width='10%'>名称</td>" +
					"<td width='10%'>单位</td>" +
					"<td width='10%'>装配数量</td>" +
					"<td width='10%'>规格</td>" +
					"<td width='10%'>型号</td>" +
					"<td width='5%'>来源</td>" +
					"<td width='5%'>构成</td>" +
					// "<td width='10%'>物料类型</td>" +
					"<td width='20%'>操作</td>" +
					"</thead><tbody class='compositionTbl'>";
			}else{
				var str_addBtn = "<div class='addBtnContainer'><span onclick='addMaterial($(this))'> 录入 <span>" + name + "</span><span>" + innerSn + "</span> 原辅材料</span></div>";
				str_compotionList += str_addBtn + "<table class='ty-table ty-table-control'><thead>" +
					"<td width='15%'>物料名称</td>" +
					"<td width='15%'>代号</td>" +
					"<td width='14%'>规格</td>" +
					"<td width='14%'>型号</td>" +
					"<td width='15%'>物料类型</td>" +
					"<td width='12%'>配比比例</td>" +
					"<td width='15%'>操作</td>" +
					"</thead><tbody class='compositionTbl'>";
			}
			str_compotionList += "</tbody></table>";
			if( obj ){
				obj.html(str_compotionList).show();
			}else{ console.log("没有找到容器"); }

		}

		// 材料的动态显示
		if( Number(compotion) == 2 ){
			curSelectTr.children(":eq(2)").children("input").attr("disabled","disabled");
		}else{
			curSelectTr.children(":eq(2)").children("input").removeAttr("disabled");

		}
	}
};

//updator: 王静 2017-08-28 8:35:56 改变商品来源
function changeSource( obj , num ){ // num: 0 来源； 1 构成  ; 2 物料类型
	curEditSelectObj = obj ;
	var _goodsID = obj.parent().parent().prev().children(".hd").children(".goodsID").html();
	var _goodsName = obj.parent().parent().prev().children(":eq(3)").html();
	var _goodsType = obj.parent().parent().prev().children(".hd").children(".goodsType").html();
	var _parentCompotionID = obj.parent().parent().prev().children(".hd").children(".parentCompotionID").html();
	// 老数据
	var tr = obj.parent().parent() ;
	var orl_source = tr.children(":eq(0)").children(".sourceVal").html();
	var orl_compotion = tr.children(":eq(1)").children(".compotionVal").html();
	var orl_staff = tr.children(":eq(2)").children(".matetialVal").val();
	var orl_memo = tr.children(":eq(3)").children(".memoVal").val();
	var orl_cateroy = tr.children(":eq(4)").children(".cateroyVal").html();
	var new_source = "" , new_compotion="" , new_stuff = "" , new_memo = "" , new_cateroy = ""  ;
	var val = obj.val();
	var setChange = false ;
	console.log(compotionContainer_Array.length);
	if(num == 0 ){ //来源
		new_source = val ;
		if(new_source == 1){ new_compotion = 1 ; new_stuff = "" ; new_memo = "" ; new_cateroy = 4 ;   }
		else if(new_source == 2){ new_compotion = 2 ; new_stuff = "" ; new_memo = "" ; new_cateroy = 3 ;   }
		if(orl_source == ""){ // 原来为空，直接改成自制
			var data = {"pdBaseId" :_goodsID , "source":new_source , "composition": new_compotion , "type" :new_cateroy,
				"goodsType": changeSourceType(_goodsType) , "stuff":new_stuff , "memo": new_memo };
			if(compotionContainer_Array.length >1){
				data = { "source":new_source , "composition": new_compotion , "type" : new_cateroy ,"goodsType":"3" /*changeSourceType(_goodsType)*/ ,
					"compositionId" : _parentCompotionID , "stuff":new_stuff, "memo":new_memo };
			}
			$.ajax({
				url:"deleteCompositionByPdId.do",
				data: data ,
				type:"post",
				dataType:"json",
				success:function(data){
					var status = data["status"];
					if( status == "0" || status == 0){ // 修改失败
						var curSelect_parent = curEditSelectObj.parent();
						curEditSelectObj.remove();
						var str_source = "<select class='sourceSelecter' onchange='changeSource( $(this) , 0 )'><option value='1' >外购</option><option value='2'>自制</option></select>" ;
						curSelect_parent.append( str_source );
					}else{
						tr.children(":eq(2)").children(".matetialVal").val("");
						tr.children(":eq(3)").children(".memoVal").val("");
						CompotionDiv.bounceCompotion_con_Show(new_compotion , new_source ,new_cateroy , num ); // 显示下方 构成列表
					}
				},
				error:function(){
					$("#mt_tip_ms").html("修改失败，请重试！");
					bounce.show($("#mtTip"));
				}
			});
		}else{ setChange = true ;  }
	}else{
		setChange = true ;
		if(num == 1){ // 构成
			new_compotion = val ;
			if(new_compotion == 2 ){ new_source = 2 ; new_stuff = "" ; new_memo = "" ; new_cateroy = 3 ;    }
			else if(new_compotion == 3){ new_source = 2 ; new_stuff = "" ; new_memo = "" ; new_cateroy = 3 ;   }
		}else if(num == 2){ // 物料类型
			new_cateroy = val ;
			new_source = 1 ; new_compotion = 1; new_stuff = orl_staff ; new_memo = orl_memo ;
		}
	}
	if(setChange){
		var changeTp = "" , changeNm = "" ;
		if( num == 0 ){ changeTp = "来源"; if(new_source == 1){ changeNm = "外购"; }else{ changeNm = "自制";  }  }
		if( num == 1 ){ changeTp = "构成"; if(new_compotion == 2){ changeNm = "制造"; }else{ changeNm = "装配";  }  }
		if( num == 2 ){ changeTp = "物料类型"; if(new_cateroy == 4 ){ changeNm = "外购成品"; }else{ changeNm = "包装材料";  }  }
		$("#mt_confirm_ms").html("如果您将内部名称为" + _goodsName + "的 \""+ changeTp +"\" 修改为\"" + changeNm + "\"，以前设置的商品构成设置将被清空，确定进行此操作吗？");
		$("#mt_confirm_type").html("changeSource");
		$("#mt_confirm_id").html(num);
		$("#_goodsID").html(_goodsID);
		if(_goodsType == "mt" || _goodsType == 1){ _goodsType = 1 ; }else{  _goodsType = 2;  }
		$("#_goodsType").html(_goodsType);
		$("#_parentCompotion").html( _parentCompotionID );
		$("#mt_confirm_source").html( new_source );
		$("#mt_confirm_compotion").html( new_compotion );
		$("#mt_confirm_cateroy").html( new_cateroy );
		$("#mt_confirm_stuff").html( new_stuff );
		$("#mt_confirm_memo").html( new_memo );
		bounce.show($("#mtConfirm"));
	}
}

//updator:王静 2017-08-22 16:24 来源或构成修改后--确定
function okConfirm(){
	var type = $("#mt_confirm_type").html();
	var id = $("#mt_confirm_id").html();
	var _goodsID = $("#_goodsID").html();
	var source = $("#mt_confirm_source").html();
	var compotion = $("#mt_confirm_compotion").html();
	var cateroy = $("#mt_confirm_cateroy").html();
	var _goodsType = $("#_goodsType").html();
	var _parentCompotion = $("#_parentCompotion").html();
	var stuff = $("#mt_confirm_stuff").html();
	var memo = $("#mt_confirm_memo").html();
	console.log( compotionContainer_Array.length);
	switch (type){
		case "changeSource":  // 修改来源构成
			var num = id ;
			var data = {"pdBaseId" :_goodsID , "source":source , "composition": compotion ,"type":cateroy,"goodsType":changeSourceType(_goodsType) ,  "stuff":stuff, "memo":memo };
			if(compotionContainer_Array.length >1){
				data = {"source":source , "composition": compotion ,"type":cateroy, "goodsType":3/*changeSourceType(_goodsType)*/ , "compositionId":_parentCompotion , "stuff":stuff , "memo":memo };
			}
			$.ajax({
				url:"deleteCompositionByPdId.do",
				data: data ,
				type:"post",
				dataType:"json",
				success:function(data){
					var status = data["status"];
					if( status == "0" || status == 0){ // 修改失败
						var orl_val = curEditSelectObj.siblings("span.hd").html();
						if(orl_val == "" ){
							curEditSelectObj.children("option:eq(0)").attr("selected" , "selected").siblings().removeAttr("selected");
						}else{
							var curSelect_parent = curEditSelectObj.parent();
							curEditSelectObj.remove();
							var str = "";
							if(num == "0" || num == 0){ // 来源
								var str_source = "<select class='sourceSelecter' onchange='changeSource( $(this) , 0 )'><option value='1' >外购</option><option value='2'>自制</option></select>" ;
								if(orl_val == 1 || orl_val == "1"){ //外购
									str_source =  "<select class='sourceSelecter' onchange='changeSource( $(this) , 0  )'><option value='1' selected='selected'>外购</option><option value='2'>自制</option></select>" ;
								}else if(orl_val == 2 || orl_val == "2"){ //自制
									str_source =  "<select class='sourceSelecter' onchange='changeSource( $(this) , 0  )'><option value='1'>外购</option><option value='2' selected='selected'>自制</option></select>" ;
								}
								str = str_source ;

							}else if( num == "1" || num == 1 ){ // 构成
								var str_composition = "<select class='compotionSelecter' onchange='changeSource( $(this) , 1  )'><option value='1' >购买</option></select>" ;
								if(orl_val == 1 || orl_val == "1"){ //购买
								}else if(orl_val == 2 || orl_val == "2"){  //制造
									str_composition =  "<select class='compotionSelecter' onchange='changeSource( $(this) , 1  )'><option value='2' selected='selected'>制造</option><option value='3'>装配</option></select>" ;
								}else if(orl_val == 3 || orl_val == "3"){  //装配
									str_composition =  "<select class='compotionSelecter' onchange='changeSource( $(this) , 1  )'><option value='2'>制造</option><option value='3' selected='selected'>装配</option></select>" ;
								}
								str = str_composition ;
							}else if( num == "2" || num == 2 ){ // 物料类型
								var str_cateroy = "<select class='compotionSelecter' onchange='changeSource( $(this) , 2 )'><option value='4' >外购成品</option><option value='5' selected='selected'>包装材料</option></select>" ;
								if(orl_val == 4 || orl_val == "4"){
									str_cateroy = "<select class='compotionSelecter' onchange='changeSource( $(this) , 2 )'><option value='4' selected='selected' >外购成品</option><option value='5'>包装材料</option></select>" ;
								}
								str = str_cateroy ;
							}
							curSelect_parent.append( str );

						}
					}else{
						var pdBaseId = data["pdBaseId"];
						var mtBaseId = data["mtBaseId"];
						var updateType = "gs";
						var updateId = pdBaseId;
						if(mtBaseId != undefined){ updateType = "mt" ; updateId = mtBaseId ;  }
						curEditSelectObj.parent().parent().prev().children(".hd").children(".goodsID").html(updateId).siblings(".goodsType").html(updateType);
						curEditSelectObj.parent().parent().children(":eq(3)").children("input").val(memo);
						curEditSelectObj.parent().parent().children(":eq(2)").children("input").val(stuff);

						CompotionDiv.bounceCompotion_con_Show(compotion ,source , cateroy ,num ); // 显示下方 构成列表
					}
				},
				error:function(){
					$("#mt_tip_ms").html("修改失败，请重试！");
					bounce.show($("#mtTip"));
				}
			});
			break ;
		case "delMt": // 删除制造的物料
			$.ajax({
				url:"deleteRawMaterialById.do" ,
				data:{  "compositionId": id    },
				type:"post",
				dataType:"json",
				success:function(data){
					var status = data["status"];
					if(status == 1 || status == "1"){
						var tr = editTrObj_Array ;
						tr[tr.length - 1].remove();
						tr.pop();
					}else{
						$("#mt_tip_ms").html("删除失败，请刷新重试！");
						bounce.show($("#mtTip"));
					}
				},
				error:function(){
					$("#mt_tip_ms").html("连接失败，请刷新重试！");
					bounce.show($("#mtTip"));
				}
			});
			break ;
		case "delGs": // 删除装配的商品
			$.ajax({
				url:"deleteCompositionById.do" ,
				data:{  "id": id    },
				type:"post",
				dataType:"json",
				success:function(data){
					var status = data["status"];
					if(status == 1 || status == "1"){
						var tr = editTrObj_Array ;
						tr[tr.length - 1].remove();
						tr.pop();
						if($(".ttl").html()=="商品构成编辑"){
							$(".bounce").show();
						}else{
							$(".bounce").hide();
						}
					}else{
						$("#mt_tip_ms").html("删除失败，请刷新重试！");
						bounce.show($("#mtTip"));
					}
				},
				error:function(){
					$("#mt_tip_ms").html("连接失败，请刷新重试！");
					bounce.show($("#mtTip"));
				}
			});
			break ;

		default: break;
	}
	$("#mtConfirm").hide();
	//判断商品构成编辑/半成品构成编辑，改变来源/构成后，构成列表的显示
	if($(".ttl").html()=="商品构成编辑"){
		bounce.show($(".bounceCompotion"));
		CompotionDiv.bounceCompotion_con_Show(Number(compotion) , Number(source) , cateroy ,num);
		$(".clr").show().children().hide();
	}else{
		CompotionDiv.bounceCompotion_con_Show(Number(compotion) , Number(source) , cateroy ,num);
		$(".bounce").hide();
		$("#compotionBounce .bounceCompotion").show();
	}


}


//updator:王静 2017-08-29 9:04:04 点击取消返回上一张页面
function cancelConfirm(){
	var type = $("#mt_confirm_type").html();
	var id = $("#mt_confirm_id").html();
	var _goodsID = $("#_goodsID").html();
	var source = $("#mt_confirm_source").html();
	var compotion = $("#mt_confirm_compotion").html();
	var cateroy = $("#mt_confirm_cateroy").html();
	var _goodsType = $("#_goodsType").html();
	var _parentCompotion = $("#_parentCompotion").html();
	var stuff = $("#mt_confirm_stuff").html();
	var memo = $("#mt_confirm_memo").html();
	switch (type){
		case "changeSource":
			var num = id ;
			var orl_val = curEditSelectObj.siblings("span.hd").html();
			var selectTr = curEditSelectObj.parent().parent();
			if(Number(num) == 0){ // 来源
				var str = "<select class='sourceSelecter' onchange='changeSource( $(this) , 0 )'><option value='1' selected='selected'>外购</option><option value='2'>自制</option></selct>";
				if(Number(orl_val) == 2){
					str = "<select class='sourceSelecter' onchange='changeSource( $(this) , 0 )'><option value='1'>外购</option><option value='2' selected='selected'>自制</option></selct>";
				}
				selectTr.children(":eq(0)").children("select").remove(); selectTr.children(":eq(0)").append(str);
			}else if(Number(num) == 1){ // 构成
				var str = "<select class='compotionSelecter' ><option value='1' selected='selected'>购买</option></selct>" ;
				if(Number(orl_val) == 2){
					str = "<select class='compotionSelecter' onchange='changeSource( $(this) , 1 )'><option value='2' selected='selected'>制造</option><option value='3'>装配</option></selct>";
				}else if(Number(orl_val) == 3){
					str = "<select class='compotionSelecter' onchange='changeSource( $(this) , 1 )'><option value='2'>制造</option><option value='3' selected='selected'>装配</option></selct>";
				}
				selectTr.children(":eq(1)").children("select").remove(); selectTr.children(":eq(1)").append(str);

			}else if(Number(num) == 2){ // 物料类型
				var str = "<select class='cateroySelecter' onchange='changeSource( $(this) , 2 )' ><option value='4' selected='selected'>外购成品</option><option value='5'>包装材料</option></selct>";
				if(Number(num) == 5){
					str = "<select class='cateroySelecter' onchange='changeSource( $(this) , 2 )' ><option value='4'>外购成品</option><option value='5' selected='selected'>包装材料</option></selct>";
				}
				selectTr.children(":eq(4)").children("select").remove(); selectTr.children(":eq(4)").append(str);
			}
			break ;
		default: break;
	}
	bounce.cancel();
	if($(".ttl").html()=="商品构成编辑"){
		bounce.show($(".bounceCompotion"));
	}else{
		$(".bounce").hide();
		$("#compotionBounce .bounceCompotion").show();
	}


}
// 保存商品的构成
//updator:王静 2017-08-23 8:28:18 保存商品的构成
function saveBigCompotion(obj){
	var pObj = obj.parent().next() ;
	var goodsID = pObj.children(":eq(0)").children("div.hd").children(".goodsID").html() ;
	var compositionId = pObj.children(":eq(0)").children("div.hd").children(".parentCompotionID").html() ;

	var source = pObj.children(":eq(1)").children(":eq(0)").children("span.hd").html() ;
	var composition = pObj.children(":eq(1)").children(":eq(1)").children("span.hd").html()  ;
	var stuff = pObj.children(":eq(1)").children(":eq(2)").children(".matetialVal").val() ;
	var memo = pObj.children(":eq(1)").children(":eq(3)").children(".memoVal").val() ;
	var cateroy = pObj.children(":eq(1)").children(":eq(4)").children(".cateroyVal").html() ;
	if($.trim(source) == "" || $.trim(source) == 0 ){ source = 1 ; composition = 1; cateroy = 4; }
	if($.trim(cateroy) == "" || $.trim(cateroy) == 0){  if(Number(source) == 1){ cateroy=5;  }else{ cateroy=3; }  }
	var data = { "id":goodsID , "source":source , "composition":composition , "stuff":stuff , "memo":memo , "type":""  };
	var url = "updateOnePdBaseById.do" ;
	// console.log(compotionContainer_Array.length);
	if( compotionContainer_Array.length > 1 ){
		data = { "source":source , "composition":composition , "stuff":stuff , "memo":memo , "compositionId":compositionId , "type":cateroy  };
		url = "updatePdBaseById.do"
	}
	$.ajax({
		url: url ,
		data: data ,
		type:"post",
		dataType:"json",
		success:function(data){
			console.log(data);
			var status = data["status"];
			if(status == 1){
				var pdBase = data["pdBase"];
				var comA = compotionContainer_Array;
				var tr = editTrObj_Array ;
				if(comA.length == 1){
					pObj.children(":eq(1)").children(":eq(0)").children("span.hd").html(pdBase["source"]) ;
					pObj.children(":eq(1)").children(":eq(1)").children("span.hd").html( pdBase["composition"] )  ;
					pObj.children(":eq(1)").children(":eq(2)").children(".matetialVal").val( pdBase["stuff"] ) ;
					pObj.children(":eq(1)").children(":eq(3)").children(".memoVal").val(pdBase["memo"]) ;
				}else{ //半成品编辑-保存
					$("#compotionBounce .bounceCompotion :last").hide();
					pObj.children(":eq(1)").children(":eq(0)").children("span.hd").html(pdBase["source"]) ;
					pObj.children(":eq(1)").children(":eq(1)").children("span.hd").html( pdBase["composition"] )  ;
					pObj.children(":eq(1)").children(":eq(2)").children(".matetialVal").val( pdBase["stuff"] ) ;
					pObj.children(":eq(1)").children(":eq(3)").children(".memoVal").val(pdBase["memo"]) ;
					pObj.children(":eq(1)").children(":eq(4)").children(".cateroyVal").html(cateroy) ;
				}
				showMain();
			}else{
				$("#mt_tip_ms").html("保存失败，请刷新重试！");
				bounce.show($("#mtTip"));
			}
		},
		error:function(){
			$("#mt_tip_ms").html("连接失败，请刷新重试！");
			bounce.show($("#mtTip"));
		}
	});

}
// 录入材料
//updator: 王静 2017-08-23 9:36:12 商品构成编辑-录入材料
function addMaterial(obj){
	// $("#updateMaterial").children("div.bonceCon  input").val("");
	$("#upMt_type").val("");
	$("#upMt_mtID").val("");
	$("#upMt_compotionID").val("");
	$("#upMt_no").val("");
	$("#upMt_goodsID").val("");
	$("#mt_conainer").html("");
	$("#upMt_bi").val("");
	bounce.show($("#updateMaterial"));
	var goodsID = obj.parent().parent().siblings(".bounceCompotion_head").children(":eq(0)").children("div.hd").children(".goodsID").html() ;
	$("#upMt_goodsID").val(goodsID);
	$("#upMt_type").val("add");
	var ttl = obj.html();
	$("#typettl").html(ttl);
	$("#upMt_name").removeAttr("disabled").val("");
	$("#upMt_gui").removeAttr("disabled").val("");
	$("#upMt_size").removeAttr("disabled").val("");
	$("#upMt_no").removeAttr("disabled").val("");
	$("#upMt_kind").val("构成商品的原辅材料");
}
// 拆分 商品
//updator: 王静 2017-08-23 9:38:12 商品构成编辑-拆分 商品
function addGoods(obj){
	var bigId = obj.parent().parent().siblings(".bounceCompotion_head").children().children("div.hd").children(".goodsID").html();
	var type = "add";
	var ttl = obj.html();
	$("#typettl_gs").html(ttl);
	$("#upGs_type").val(type);
	$("#upGs_id").val("");
	$("#upGs_goodsType").val("");
	$("#upGs_compotionID").val("");
	$("#upGs_BigGsId").val(bigId);
	$("#upGs_no").val("").removeAttr("disabled");
	$("#upGs_name").val("").removeAttr("disabled");
	$("#upGs_gui").val("").removeAttr("disabled");
	$("#upGs_size").val("").removeAttr("disabled");
	$("#upGs_unit").val("").removeAttr("disabled");
	$("#upGs_num").val("");
	$("#upGs_id").val("") ;
	bounce.show($("#updateGoods"));

}
// 检索 物料代号和内部图号
var timer = null ; // 搜索的定时器
var orl_Mtval = "";  // 定时器中上次搜索代号的值
var matchTimer = null; // 匹配的定时器

//updator:王静 2017-08-28 8:37:45 录入原材料 启动搜索代号相关定时器
function searchCode(obj , event ){
	$("#mt_conainer").children().length ? $("#mt_conainer").show() : $("#mt_conainer").hide() ;
	orl_Mtval = "" ;
	timer = setInterval(function(){  searchUpMt(obj.val()); }, 500);
	matchTimer = setInterval(function () { matchCode(); },200) ;
	event.stopPropagation() ;
}

//updator:王静 2017-08-28 8:37:59 可拆分 中启动搜索代号相关定时器
function searchBoth(obj , event ){
	$("#gs_conainer").children().length ? $("#gs_conainer").show() : $("#gs_conainer").hide() ;
	orl_Mtval="" ;
    searchUpGs();
	matchTimer = setInterval(function () { matchGoodsCode(); },200) ;
	event.stopPropagation();
}
//updator:王静 2017-08-28 8:38:03  刷新代号列表
function searchUpMt( val ){
	if(orl_Mtval != val ){
		orl_Mtval = val ;
		$.ajax({
			url:"getMtBaseByCode.do",
			data:{ "code": val   },
			type:"post",
			dataType:"json",
			success:function(data){
				var mtBase = data["mtBases"];
				var str = "";
				if( mtBase != undefined && mtBase.length > 0 ){
					$("#mt_conainer").show();
					for( var i =0 ; i < mtBase.length ; i++ ){
						mtBase[i]["typeUpMt"] = "mt";
						var jsonStr = JSON.stringify(mtBase[i]);
						str += " <div class='chargeItemCode' onclick='mt_selectThis($(this))'><span class='val'>"+ jsonStr +"</span><span class='addCode'>"+ mtBase[i]["code"] +"</span></div>";
					}
				}else{
					$("#mt_conainer").hide();
				}
				$("#mt_conainer").html(str);
			},
			error:function(){
				$("#mt_tip_ms").html("连接失败，请刷新重试！");
				bounce.show($("#mtTip"));
			}
		});
	}
}

//updator:王静 2017-08-28 8:38:59 物料 匹配 代号
function matchCode() {
	var Code = $("#upMt_no").val();
	var isHasMatch = false;
	$("#mt_conainer").children(".chargeItemCode").each(function(){
		var item_code = $(this).children(".addCode").html();
		if( item_code == Code ){
			isHasMatch = true ;
			$(this).attr("style","background:#eaeaea;").siblings().attr("style","background:#fff;");
			var val = $(this).children(".val").html();
			val = JSON.parse(val);
			$("#upMt_no").val(val["code"]);
			$("#upMt_mtID").val(val["id"]);
			$("#upMt_name").val(val["name"]).attr("disabled","true");
			$("#upMt_gui").val(val["specifications"]).attr("disabled","true");
			$("#upMt_size").val(val["model"]).attr("disabled","true");
			return false;
		}else{
			$(this).attr("style","background:#fff;");
		}
	});

	if(!isHasMatch){
		$("#upMt_mtID").val("");
		$("#upMt_name").removeAttr("disabled").val("") ;
		$("#upMt_gui").removeAttr("disabled").val("") ;
		$("#upMt_size").removeAttr("disabled").val("") ;

	}


}

//updator:王静 2017-08-28 8:39:00 拆分商品 匹配 代号
function matchGoodsCode() {
	var Code = $("#upGs_no").val(); // 拆分输入的代号
	var isHasMatch = false;
	$("#gs_conainer").children(".chargeItem").each(function(){
		var item_code = $(this).children(".GoodsCode").html();
		var _index = item_code.indexOf(Code) ;
		if(_index == -1){
			$(this).hide() ;
		}else{
            $(this).show() ;
            if( item_code == Code ){
                isHasMatch = true ;
                var val = $(this).children(".val").html();
                val = JSON.parse(val);
                $(this).attr("style","background:#eaeaea;") ;

                $("#upGs_id").val(val["id"]) ;
                $("#upGs_name").val(val["name"]).attr("disabled","true");
                $("#upGs_gui").val(val["specifications"]).attr("disabled","true");
                $("#upGs_size").val(val["model"]).attr("disabled","true");
                $("#upGs_unit").val(val["unit"]).attr("disabled","true");
                $("#upGs_goodsType").val(val["typeUpGs"]) ;
				return false ;
            }else{
                $(this).attr("style","background:#fff;");
            }
		}


	});
    if(!isHasMatch){
    	if($("#upGs_id").val() != ""){
            $("#upGs_id").val("") ;
            $("#upGs_name").removeAttr("disabled").val("") ;
            $("#upGs_gui").removeAttr("disabled").val("") ;
            $("#upGs_size").removeAttr("disabled").val("") ;
            $("#upGs_unit").removeAttr("disabled").val("") ;
            $("#upGs_goodsType").val("") ;
		}
    }
}

//updator:王静 2017-08-28 8:40:00 刷新可拆分代号列表
function searchUpGs(val){
	$.ajax({
		url:"retrievalByInnerSnAndCode.do",
		data:{ "code": ""   },
		beforeSend:function(){},
		success:function(data){
			var mtBase = data["mtBase"];
			var pdBase = data["pdBase"];
			$("#gs_conainer").html("");
			if( (mtBase != undefined && mtBase.length > 0) || ( pdBase != undefined && pdBase.length > 0) ){
				$("#gs_conainer").show();
			}else{
				$("#gs_conainer").hide().attr("style","display:none");
			}
			if( mtBase != undefined && mtBase.length > 0 ){
				for( var i =0 ; i < mtBase.length ; i++ ){
					mtBase[i]["typeUpGs"] = "mt";
					var jsonStr = JSON.stringify(mtBase[i]);
					var code = mtBase[i]["code"] ;
					if(code == ""){  code = "&nbsp;";  }
					var str_i = " <div class='chargeItem' onclick='gs_selectThis($(this))'><span class='val'>"+ jsonStr +"</span><span class='GoodsCode'>"+ code +"</span></div>";
					$("#gs_conainer").append(str_i);
				}
			}
			if( pdBase != undefined && pdBase.length > 0 ){
				for( var i =0 ; i < pdBase.length ; i++ ){
					pdBase[i]["typeUpGs"] = "gs";
					var jsonStr = JSON.stringify(pdBase[i]);
					var code = pdBase[i]["innerSn"] ;
					if(code == ""){  code = "&nbsp;";  }
					var str_j = " <div class='chargeItem' onclick='gs_selectThis($(this))'><span class='val'>"+ jsonStr +"</span><span class='GoodsCode'>"+ code+"</span></div>";
					$("#gs_conainer").append(str_j);
				}
			}

		}
	});
}

//updator:王静 2017-08-28 8:40:10 停止定时器
function stopIntervalMt(  ){
	for(var j = matchTimer ; j >= 0 ; j-- ){
		clearInterval(j);
	}
	$("#mt_conainer").hide();
}
function stopIntervalGs(  ){
	for(var j = matchTimer ; j >= 0 ; j-- ){
		clearInterval(j);
	}
	$("#gs_conainer").hide();
    matchGoodsCode();
}

//updator:王静 2017-08-28 8:41:10  用户选择
function gs_selectThis(obj){
	var jsonStr = obj.children(".val").html();
	jsonStr = eval('('+ jsonStr +')');
	stopIntervalGs() ;
	$("#upGs_id").val(jsonStr["id"]);
	$("#upGs_goodsType").val(jsonStr["typeUpGs"]);
	var code = "";
	if( jsonStr["typeUpGs"] == "gs" ){ code = jsonStr["innerSn"]; }else{ code = jsonStr["code"];  }
	$("#upGs_no").val(code);
	$("#upGs_name").val(jsonStr["name"]).attr("disabled","disabled");
	$("#upGs_gui").val(jsonStr["specifications"]).attr("disabled","disabled");
	$("#upGs_size").val(jsonStr["model"]).attr("disabled","disabled");
	$("#upGs_unit").val(jsonStr["unit"]).attr("disabled","disabled");

	$(".mt_conainerGoods").hide();
}
//updator:王静 2017-08-28 8:42:10  点击代号列表获取信息
function mt_selectThis(obj){
	var jsonStr = obj.children(".val").html();
	jsonStr = eval('('+ jsonStr +')');
	stopIntervalMt();
	$("#upMt_mtID").val(jsonStr["id"]);
	$("#upMt_no").val(jsonStr["code"]);
	$("#upMt_name").val(jsonStr["name"]).attr("disabled","disabled");
	$("#upMt_gui").val(jsonStr["specifications"]).attr("disabled","disabled");
	$("#upMt_size").val(jsonStr["model"]).attr("disabled","disabled");
	$(".mt_conainer").hide();
}
/* 下面试 制造和装配 table 表格中的按钮  */
// 制造的修改
//updator:王静 2017-08-28 8:42:59 制造的修改
function composition_2_update(obj){
	editTrObj_Array.push(obj.parent().parent());
	var id = obj.siblings(".hd").children(".mt_id").html();
	var compotionId = obj.siblings(".hd").children(".compotion_id").html();
	var name = obj.parent().siblings(":eq(0)").html();
	var no = obj.parent().siblings(":eq(1)").html();
	var gui = obj.parent().siblings(":eq(2)").html();
	var size = obj.parent().siblings(":eq(3)").html();
	var kind = obj.parent().siblings(":eq(4)").html();
	var bi = obj.parent().siblings(":eq(5)").html();
	var goodsID = obj.parents(".bounceCompotion_con").siblings(".bounceCompotion_head").children(":eq(0)").children(".hd").children(".goodsID").html();
	$("#upMt_goodsID").val(goodsID);
	$("#upMt_mtID").val(id);
	$("#upMt_compotionID").val(compotionId);
	$("#upMt_no").val(no).attr("disabled" , "disabled" );
	$("#upMt_name").val(name).attr("disabled" , "disabled" );
	$("#upMt_gui").val(gui).attr("disabled" , "disabled" );
	$("#upMt_size").val(size).attr("disabled" , "disabled" );
	$("#upMt_bi").val(bi);
	$("#upMt_kind").val("原辅材料");
	$("#upMt_type").val("update");
	// bounce.show($(".goods_update"));
	bounce.show($("#updateMaterial"));
}
//updator:王静 2017-08-23 8:51:12 商品/半成品构成编辑--录入--修改-确定
function saveUpMtBtn(){
	var type = $("#upMt_type").val();
	var mtID = $("#upMt_mtID").val();
	var compotionID = $("#upMt_compotionID").val();
	var goodsID = $("#upMt_goodsID").val();
	var no = $("#upMt_no").val();
	var name = $("#upMt_name").val();
	var specifications = $("#upMt_gui").val();
	var model = $("#upMt_size").val();
	var match = $("#upMt_bi").val();
	if( match == "" ){ match = 0 ; }else if(Number(match) > 100 ){
		$("#mt_tip_ms").html("配比不合法，应小于100，请重试！");
		bounce.show($("#mtTip"));
		return false ; }
	var kind = $("#upMt_kind").val();
	// 保存修改(新增)的物料信息
	if(type != "update" && type != "add"){
		$("#mt_tip_ms").html("获取操作类型失败，请刷新重试！");
		bounce.show($("#mtTip"));
		return false ;
	}
	if(goodsID == "" ){
		$("#mt_tip_ms").html("获取当前操作构成商品失败，请刷新重试！");
		bounce.show($("#mtTip"));
		return false ;
	}
	if(name == "" ){
		$("#mt_tip_ms").html("您选择的物料名称为空，该物料为无意义数据，请重新选择！");
		bounce.show($("#mtTip"));
		return false ;
	}
	var url = "";
	var data = {};
	if(type == "update" ){
		url = "updateRawMaterial.do";
		data= {
			"compositionId": compotionID ,
			"name": name ,
			"specifications": specifications ,
			"model": model ,
			"matching": match
		};
	}else if(type == "add"){
		url = "addRawMaterial.do";
		data= {
			"ptBaseId": goodsID ,
			"categoryName": kind ,
			"code": no ,
			"id": mtID ,
			"name": name ,
			"specifications": specifications ,
			"model": model ,
			"unit": "" ,
			"matching": match ,
		};
	}
	var nowLen = editTrObj_Array.length ;
	var curTr = editTrObj_Array[ nowLen - 1] ;
	$.ajax({
		url:url ,
		data: data ,
		type:"post",
		dataType:"json",
		success:function(data){
			console.log(data);
			var status = data["status"];
			if(type == "update" ){ //修改
				if(status == 1){
					var compotion = data["composition"];
					var mtBase = data["mtBase"];
					// 此处进行修改
					var bi = compotion["matching"] ;
					curTr.children(":eq(5)").html(bi);
					editTrObj_Array.pop() ;
					$("#updateMaterial").hide();
					// $(".bounceCompotion").show();
					//半成品构成编辑--修改返回的页面
					$(".bounce").hide();
					if($(".ttl").html()=="商品构成编辑"){
						bounce.show($(".bounceCompotion"));
					}else{
						$("#compotionBounce .bounceCompotion").show();
					}
				}else {
					editTrObj_Array.pop() ;
					$("#mt_tip_ms").html("修改原辅材料失败，请稍后重试！");
					bounce.show($("#mtTip"));
				}
			}else if(type == "add") { //录入
				if(status == 1){
					var compotion = data["composition"];
					var mtBase = data["mtBase"];
					var len = compotionContainer_Array.length ;
					var curTbl = null ;
					var curCon = compotionContainer_Array[ len - 1];
					curTbl = curCon.children(".bounceCompotion_con").children("table");
					var trStr = "<tr>" +
						"<td>"+ mtBase["name"]  +"</td>" +
						"<td>"+ mtBase["code"]  +"</td>" +
						"<td>"+ mtBase["specifications"]  +"</td>" +
						"<td>"+ mtBase["model"]  +"</td>" +
						"<td>"+ mtBase["categoryName"]  +"</td>" +
						"<td>"+ compotion["matching"]  +"</td>" +
						"<td> " +
						"<span class='ty-color-blue' onclick='composition_2_update($(this))'>修改</span>" +
						"<span class='ty-color-red' onclick='composition_2_del($(this))'>删除</span>" +
						"<div class='hd'>" +
						"<span class='mt_id'>"+ mtBase["id"]  +"</span> " +
						"<span class='compotion_id'>"+ compotion["id"] +"</span> " +
						"</div>" +
						"</td></tr>" ;
					curTbl.append(trStr);
					// $(".compositionTbl").append(trStr);
					$("#updateMaterial").hide();
					if($(".ttl").html()=="商品构成编辑"){
						bounce.show($(".bounceCompotion"));
					}else{
						$(".bounce").hide();
						$("#compotionBounce .bounceCompotion").show();
					}
					// $(".bounceCompotion").show();
				}else if( Number(status) == 2 ){
					$("#mt_tip_ms").html("该物料代号或名称已存在，请重新录入");
					bounce.show($("#mtTip"));
				}else {
					$("#mt_tip_ms").html("新增原辅材料失败，请稍后重试！");
					bounce.show($("#mtTip"));
				}
			}
		},
		error:function(){
			$("#mt_tip_ms").html("链接失败，请稍后重试！");
			bounce.show($("#mtTip"));
			editTrObj_Array.pop();
		}
	});
}
//updator:王静 2017-08-29 11:44：43 构成编辑--录入弹框--取消
function cancelUpMtBtn(){
	var type = $("#upMt_type").val();
	if(type == "update" ){
		editTrObj_Array.pop();
	}
	$("#updateMaterial").hide();
	if($(".ttl").html()=="商品构成编辑"){
		bounce.show($(".bounceCompotion"));
	}else{
		$(".bounce").hide();
		$("#compotionBounce .bounceCompotion").show();
	}
}

//updator:王静 2017-08-28 8:43:56 制造的删除
function composition_2_del(obj){
	editTrObj_Array.push(obj.parent().parent());
	var id = obj.siblings(".hd").children(".gs_id").html();
	var compotion_id = obj.siblings(".hd").children(".compotion_id").html();
	var no = obj.parent().siblings(":eq(1)").html();
	var name = obj.parent().siblings(":eq(0)").html();
	$("#mt_confirm_ms").html("确定删除代号："+ no + "，物料名称：" + name + " 的装配材料？");
	$("#mt_confirm_type").html("delMt");
	$("#mt_confirm_id").html(compotion_id);
	bounce.show($("#mtConfirm"));
}

//updator:王静 2017-08-28 8:44:54 装配的修改
function compotion_3_update(obj){
	var trObj = obj.parent().parent() ;
	editTrObj_Array.push(trObj);
	var id = obj.siblings(".hd").children(".gs_id").html();
	var gs_type = obj.siblings(".hd").children(".gs_type").html();
	var compotionId = obj.siblings(".hd").children(".compotion_id").html();
	var no = obj.parent().siblings(":eq(0)").html();
	var name = obj.parent().siblings(":eq(1)").html();
	var unit = obj.parent().siblings(":eq(2)").html();
	var num = obj.parent().siblings(":eq(3)").html();
	var gui = obj.parent().siblings(":eq(4)").html();
	var size = obj.parent().siblings(":eq(5)").html();
	var bigID = trObj.parent().parent().parent().siblings(".bounceCompotion_head").children(":eq(0)").children("div.hd").children(".goodsID").html();
	$("#upGs_id").val(id);
	$("#upGs_goodsType").val(gs_type); // gsId 对应的是物料（"mt"）还是商品("gs")
	$("#upGs_type").val("update");
	$("#upGs_compotionID").val(compotionId);
	$("#upGs_BigGsId").val(bigID);
	$("#upGs_no").val(no).attr("disabled" , "disabled");
	$("#upGs_name").val(name).attr("disabled" , "disabled");
	$("#upGs_gui").val(gui).attr("disabled" , "disabled");
	$("#upGs_size").val(size).attr("disabled" , "disabled");
	$("#upGs_unit").val(unit).attr("disabled" , "disabled");
	$("#upGs_num").val(num);

	bounce.show($("#updateGoods"));
}
// 保存新增或修改的构成信息
//updator: 王静 2017-08-22 16:11:10 拆分--保存新增或修改的构成信息
function saveUpGsBtn(){
	var gsId = $("#upGs_id").val();
	var gs_type = $("#upGs_goodsType").val(); // gsId 对应的是物料（"mt"）还是商品("gs"),如果是新增都为空
	// gs_type = (gs_type == "gs" ? 2 : 1) ;
	var upGs_type = $("#upGs_type").val();
	var upGs_compotionID = $("#upGs_compotionID").val();
	var upGs_BigGsId = $("#upGs_BigGsId").val();
	var upGs_no = $("#upGs_no").val();
	var upGs_name = $("#upGs_name").val();
	var upGs_gui = $("#upGs_gui").val();
	var upGs_size = $("#upGs_size").val();
	var upGs_unit = $("#upGs_unit").val();
	var upGs_num = $("#upGs_num").val();
	if( isNaN(Number(upGs_num)) || upGs_num == ""){
		upGs_num = 0 ;
	}
	if( upGs_BigGsId == "" || upGs_no == "" || (upGs_type == "add" && upGs_type == "update") ){
		$("#mt_tip_ms").html("获取操作数据失败，请刷新重试！");
		bounce.show($("#mtTip"));
		return false;
	}
	if(upGs_type == "update" && (upGs_compotionID == "" || gsId == "" || upGs_no == "") ){
		$("#mt_tip_ms").html("获取操作数据失败，请刷新重试！");
		bounce.show($("#mtTip"));
		return false;
	}
	if( Number(upGs_num) > 99999999 ){
		$("#mt_tip_ms").html("装配数量录入不合法，请重试！");
		bounce.show($("#mtTip"));
		return false;
	}
	if( upGs_name == "" ){
		$("#mt_tip_ms").html("该商品名称为空，为无效商品，请重新选择！");
		bounce.show($("#mtTip"));
		return false;
	}
	var url = "";
	var data = {};
	if(upGs_type == "add"){
		url = "splitCommodity.do";
		data = {
			"pdBaseId": upGs_BigGsId ,
			"code": upGs_no ,
			"id": gsId ,
			"name": upGs_name ,
			"specifications": upGs_gui ,
			"model": upGs_size ,
			"unit": upGs_unit ,
			"type": changeSourceType(gs_type) ,
			"amount": upGs_num
		};
	}else if(upGs_type == "update"){
		url = "updateSplitCommodity.do";
		data = {
			"compositionId": upGs_compotionID ,
			"name": upGs_name ,
			"unit": upGs_unit ,
			"specifications": upGs_gui ,
			"model": upGs_size ,
			"amount": upGs_num
		};
	}
	$.ajax({
		url: url ,
		data: data ,
		type:"post" ,
		dataType:"json" ,
		success:function(data){
			var status = data["status"];
			if( status == 1 ){
				var composition = data["composition"];
				if(upGs_type == "update"){ //修改
					var amount = composition["amount"];
					editTrObj_Array[editTrObj_Array.length - 1].children(":eq(3)").html(amount);
					editTrObj_Array.pop();
				}else if(upGs_type == "add"){ //新增
					var base = null ;
					var code = "" ;
					var mtCategory = data["mtCategory"] ; // 拆的是已有的 材料
					if( (gs_type == "gs" && gsId)  ){ // 新增半成品 或 商品
						base = data["ppBase"];
						code = composition["innerSn"];
					}else{ // 物料
						base = data["mtBase"];
						code = composition["innerSn"];
						if(gs_type == "" ){
							mtCategory = base["categoryName"]; // 拆的是新增的物料
						}
					}
					var len = compotionContainer_Array.length ;
					var curTbl = null ;
					var curCon = compotionContainer_Array[ len - 1];
					curTbl = curCon.children(".bounceCompotion_con").children("table");
					var str_item = "<tr>" +
						"<td>"+ code +"</td>" +
						"<td>"+ composition["name"] +"</td>" +
						"<td>"+ composition["unit"] +"</td>" +
						"<td>"+ composition["amount"] +"</td>" +
						"<td>"+ composition["specifications"] +"</td>" +
						"<td>"+ composition["model"] +"</td>" +
						"<td>" +
						"<span>"+ chargeSouce(composition["source"]) +"</span>" +
						"<span class='sourcevalue hd'>"+ composition["source"] +"</span>" +
						"</td>" +
						"<td>" +
						"<span>"+ chargeComposition(composition["composition"]) +"</span>" +
						"<span class='compotionvalue hd'>"+ composition["composition"] +"</span>" +
						"</td>" +
						// "<td>"+ mtCategory +"</td>"+
						"<td>" +
						"<span class='ty-color-blue' onclick='compotion_3_update($(this))'>修改</span>" ;

					str_item += "<span class='ty-color-blue' onclick='compotion_3_edit($(this))'>编辑</span>" ;
					str_item += "<span class='ty-color-red' onclick='compotion_3_del($(this))'>删除</span>" +
						"<div class='hd'>" +
						"<span class='gs_id'>"+ base["id"] +"</span> " +
						"<span class='compotion_id'>"+ composition["id"] +"</span> " +
						"<span class='gs_type'>"+ gs_type +"</span> " +
						"</div>" +
						"</td></tr>" ;
					curTbl.append(str_item);
					// $(".compositionTbl").append(str_item);
				}
				$("#updateGoods").hide();
				if($(".ttl").html()=="半成品构成编辑"){
					$(".bounce").hide();
					$("#compotionBounce .bounceCompotion").show();
				}else if($(".ttl").html()=="商品构成编辑"){
					bounce.show($(".bounceCompotion"));
				}
			}else if(status == 2){
                layer.msg("代号或者名称重复，请重新录入！");

			}else{
                layer.msg("操作失败，请稍后重试！");
			}
		},
		error:function(){
			$("#mt_tip_ms").html("连接失败，请稍后重试！");
			bounce.show($("#mtTip"));
		},
	});
}
//updator:王静 2017-08-29 11:46:21 构成编辑--拆分弹框--取消
function cancelUpGsBtn(){
	var upGs_type = $("#upGs_type").val();
	if(upGs_type == "update"){
		editTrObj_Array.pop();
	}
	$("#updateGoods").hide();
	if($(".ttl").html()=="商品构成编辑"){
		bounce.show($(".bounceCompotion"));
	}else{
		$(".bounce").hide();
		$("#compotionBounce .bounceCompotion").show();
	}
}

//updator:王静 2017-08-28 8:44:59 装配的删除
function compotion_3_del(obj){
	editTrObj_Array.push(obj.parent().parent());
	var id = obj.siblings(".hd").children(".gs_id").html();
	var compotion_id = obj.siblings(".hd").children(".compotion_id").html();
	var no = obj.parent().siblings(":eq(0)").html();
	var name = obj.parent().siblings(":eq(1)").html();
	$("#mt_confirm_ms").html("确定删除代号："+ no + "，名称：" + name + " 的装配材料？");
	$("#mt_confirm_type").html("delGs");
	$("#mt_confirm_id").html(compotion_id);
	bounce.show($("#mtConfirm"));
}

//updator:王静 2017-08-28 8:45:54 装配的编辑
function compotion_3_edit(obj){
	$(".bounce").hide();
	var editTr = obj.parent().parent() ;
	var goodsType = obj.siblings(".hd").children(".gs_type").html();  // 要拆分的商品类型 ， 装配时新增的物料（mt）还是 商品/半成品
	var compotion_id = obj.siblings(".hd").children(".compotion_id").html();  // 此商品所属的这条构成关系
	var goodsID = obj.siblings(".hd").children(".gs_id").html();  // 此商品的id
	if(compotion_id == null || compotion_id == undefined || compotion_id == ""){
		$("#mt_tip_ms").html("获取商品基本信息失败，请刷新重试！");
		bounce.show($("#mtTip"));
		return false ;
	}
	var sourcevalue = obj.parent().siblings(":eq(6)").children(".sourcevalue").html(); //来源编号
	var compotionvalue = obj.parent().siblings(":eq(7)").children(".compotionvalue").html(); //构成编号
	$.ajax({
		url:"getSecondBaseByCompositionId.do",
		data:{ "compositionId": compotion_id },
		type:"post",
		dataType:"json" ,
		success:function( data ){
			editTrObj_Array.push( editTr );
			var status = data["status"];
			if( status == 1 || status == "1" ){
				var pdBase = data["pdBase"];   // goodsBaseType == 1
				var mtBase = data["mtBase"];  // goodsBaseType == 2
				var code = "";
				var cateroy = "";
				var source = "";
				var compotion = "";
				var goodsBase = null;
				if( pdBase == undefined || pdBase == null ){
					goodsBase = mtBase ;
					code = goodsBase["code"];
					source = 0;
					compotion = 0;
					cateroy = 0 ;
				}else{
					goodsBase = pdBase ;
					code = goodsBase["innerSn"];
					cateroy = goodsBase["type"];
					source = ifNull(goodsBase["source"]);
					compotion = ifNull(goodsBase["composition"]);
				}
				if(goodsBase["id"] == "" || goodsBase["id"] == undefined ){
					$("#mt_tip_ms").html("获取商品基本信息失败，请刷新重试！");
					bounce.show($("#mtTip"));
					return false ;
				}
				var compositionList = data["compositionList"];
				$("#compotionBounce").show().children(".bounceCompotion").children(".keep").show() ;
				CompotionDiv.setBaseContainer(clickStatus , ifNull(code) , ifNull(goodsBase["name"]) , ifNull(goodsBase["unit"]) , source , compotion , ifNull(goodsBase["stuff"]) , ifNull(goodsBase["memo"]) , compositionList , $("#compotionBounce") , goodsBase["id"] , goodsType , compotion_id , cateroy );
				$("#compotionBounce .bounceCompotion").css("margin-left","476px");
			}else{
				$("#mt_tip_ms").html("获取构成信息失败，请刷新重试！");
				bounce.show($("#mtTip"));
			}
		},
		error:function(msg){
			bounce.show($("#mtTip"));
			$("#mt_tip_ms").html("连接错误，请稍后重试!");
		}
	});
}

// charge undefned
function ifNull( str ){
	if( str == "undefined" || str == undefined){ return "";  }else{ return str; }
}

//updator:王静 2017-08-28 8:48:09 判断 返回的构成关系 是商品还是物料
function chargeGoodsType( product , material ){
	if( product == "" || product == null ){
		if( material == "" || material == null ){
			alert("物料两种类型都没有") ;
		}
		return { "goodsID" :material , "goodsKind" : "mt" } ;
	}else{
		return { "goodsID" :product , "goodsKind" : "gs" } ;
	}
}

//updator:王静 2017-08-28 8:48:19 设置弹框移动
var orl_rative = null ; // 一开始相对屏幕的距离
function setMove_KeyDown( obj , e){
	obj.parent().attr("onmousemove" , "setMove_Move($(this) , event)")
		.attr("onmouseup" , "setMove_KeyUp($(this) , event)")
		.attr("onmouseout" , "setMove_KeyUp($(this) , event)")
		.attr("onmouseleave" , "setMove_KeyUp($(this) , event)");
	var screenWidth = $(window).width() ;
	var screenHeight = $(window).height() ;
	var offsetObj = obj.parent().offset();
	var leftObj =offsetObj.left;
	var topObj = offsetObj.top ;
	var mouseX = e.pageX ;
	var mouseY = e.pageY ;
	var leftToObj = mouseX - leftObj ;
	var topToObj = mouseY - topObj ;
	orl_rative = { "left": leftToObj , "top":topToObj , "screenWidth" : screenWidth , "screenHeight" : screenHeight }
}
function setMove_Move( obj , event ){
	var objLeft = event.pageX  - orl_rative["left"];
	var objTop = event.pageY - orl_rative["top"];
	// 左
	if(objLeft <= 0 ){ objLeft = 0; }
	// 上
	if(objTop <= 50 ){ objTop = 50; }
	// 下
	var maxTop = orl_rative["screenHeight"] - 100;
	if(objTop >= maxTop ){ objTop = maxTop; }
	// 右
	var maxLeft = orl_rative["screenWidth"] - 100;
	if(objLeft >= maxLeft ){ objLeft = maxLeft; }
	obj.offset({ left:objLeft , top:objTop });
}
function setMove_KeyUp( obj , event ){
	obj.removeAttr("onmousemove")
		.removeAttr("onmouseup")
		.removeAttr("onmouseout")
		.removeAttr("onmouseleave");
}

//updator:王静 2017-08-28 8:48:29  当前弹框进行弹框提示
function giveTip( obj ){
	var len = compotionContainer_Array.length ;
	var curContainer = compotionContainer_Array[len - 1]  ;
	curContainer.animate({ left:"+=20px" },100).animate({ left:"-=20px" },100)
		.animate({ left:"+=15px" },80).animate({ left:"-=15px" },80)
		.animate({ left:"+=10px" },80).animate({ left:"-=10px" },80)
		.animate({ left:"+=5px" },80).animate({ left:"-=5px" },80)
		.animate({ left:"+=2px" },40).animate({ left:"-=2px" },40);
}

//updator:王静 2017-08-28 8:48:40 设置 changeSourceType
function changeSourceType(_goodsType){
	if(_goodsType == "mt" || _goodsType == 1 || _goodsType == "1"){ return 1 ;  } else{ return 2 ;  }

}

//updator:王静 2017-08-28 8:48:45   判断物料分类
function chargeCateroy(cateroy){
	if(Number(cateroy) == 3){ return "半成品";  }
	if(Number(cateroy) == 4){ return "外购成品";  }
	if(Number(cateroy) == 5){ return "包装材料";  }
	return "";
}

//updator:王静 2017-08-28 8:50:45  查询
function searchBtn(){
	var innerSn = $("#searchInnerSn").val();
	getComposionList( 1, 10 , innerSn);
}
//creator:王静 2017-08-29 16:19:23 拆分/录入--头部叉号返回对应页面
function rupdateGoods(){
	if($(".ttl").html()=="商品构成编辑"){
		bounce.cancel();
		bounce.show($(".bounceCompotion"));
	}else{
		bounce.cancel();
		$("#compotionBounce .bounceCompotion").show();
	}
}








