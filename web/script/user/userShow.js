$(function (){
    getAuthViewList (1, 20)
})
// creator: 张旭博，2021/12/28 11:33，用户展示列表
function getAuthViewList(currentPageNo, pageSize) {
    $.ajax({
        url: '../authView/getAuthViewList.do',
        data: {
            currentPageNo: currentPageNo,
            pageSize: pageSize
        },
        success: function (res) {
            var data = res.data
            var list = data.authAccList
            var pageInfo = data.pageInfo

            // 分页
            var totalPage = pageInfo.totalPage;
            var cur = pageInfo.currentPageNo;
            var totalResult = pageInfo.totalResult;
            setPage( $("#ye_authAcc") , cur ,  totalPage , "authAcc");

            $(".authAccNum").html(totalResult)

            var str = ''
            //list[i].activityLevel 活跃等级（暂时没值显示 --）
            for (var i in list) {
                str += `<tr>
                            <td>${list[i].name}</td>
                            <td>${list[i].mobile}</td>
                            <td>${chargeSource(list[i].source)}</td>
                            <td>${chargeNull(list[i].orgNum)}</td>
                            <td>${chargeNull(moment(list[i].createTime).format("YYYY-MM-DD hh:mm:ss"))}</td>
                            <td>${chargeNull(moment(list[i].lastLogon).format("YYYY-MM-DD hh:mm:ss"))}</td>
                            <td>--</td>
                             <td class="ty-td-control">
                                <span class="ty-color-blue funBtn" onclick="openspaceflow($(this))">空间与流量</span>
                            </td>
                            <td class="hd">
                                <span class="hd">${ JSON.stringify(list[i]) }</span>
                            </td>
                        </tr>`;
            }
            $("#mainList tbody").html(str)
        }
    })
}

// creator: 张旭博，2021/12/28 14:59，处理null
function chargeNull(val) {
    return val === null || val === 'Invalid date'? '': val
}

// creator: 张旭博，2021/12/28 14:59，来源过滤器
function chargeSource(val) {
    var text = ''
    switch (val) {
        case 1:
            text = '机构录入'
            break
        case 2:
            text = '自行注册'
            break
        case 3:
            text = 'XXX宝'
            break
        default:
            text = ''
    }
    return text
}

// creator: sy 2024-12-20 11:22    空间与流量
function openspaceflow(spue){
    bounce.show($("#spaceandflow"));
    let alist = spue.parent('td').siblings().find(".hd").text();
    console.log('a',alist);
    alist = JSON.parse(alist);
    console.log('b',alist.id);
    console.log('c',alist.name);
    $.ajax({
        url:"../st/getAccSpaceTrafficInfo.do",
        data: {
            id: alist.id,userName: alist.name
        },//id-列表id,userName-列表名称
        success:function(res){
            let ratedSpace = res.data.ratedSpace || "——";
            let usedSpace = res.data.usedSpace || "——";
            let statime = formatTimestamp(res.data.beginDate);
            let stotime = formatTimestamp(res.data.endDate);
            let ptime = statime+'-'+stotime;
            let ratedTraffic = res.data.ratedTraffic || "——";
            let usedTraffic = res.data.usedTraffic || "——";
            $(".privteprorty").html(res.data.fullName);
            $(".spueup").html(ratedSpace);
            $(".usespue").html(usedSpace);
            $(".pname").html(ptime);
            $(".trafficlimit").html(ratedTraffic);
            $(".usetramit").html(usedTraffic);
        }
    })
}

// creator: sy 2024.12.24 8:20  时间戳转换
function formatTimestamp(timestamp){
    // 创建一个 Date 对象
    const date = new Date(timestamp);
    // 获取年、月、日
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1，并补零
    const day = String(date.getDate()).padStart(2, '0'); // 补零
    // 格式化输出
    return `${year}年${month}月${day}日`;
}