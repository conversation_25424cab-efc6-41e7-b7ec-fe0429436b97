/**
 * Created by Administrator on 2017/9/7 0007.
 */
$(function () {
    getOrgs() ; // 获取机构信息
}) ;

var editObj = { } ; // 当前操作的公司信息 ：公司ID ， 是否默认
// Creator : 侯杏哲 2017-09-11 点击 复选框 切换默认机构
function showCongirm( orgId , curStatus){  // orgId - 设置的公司id ;  curStatus - 当前是否选中为默认 
    editObj = { "orgId" :orgId , "curStatus":curStatus } ;
    bounce.show( $("#controlManage") );
    if(curStatus){ // 当前默认选中，设置为不选中 ；
      $("#controlMess").html("取消默认机构，下次登录将会提示所有公司以供选择");
    }else{ // 当前默认不选中，设置为选中
        $("#controlMess").html("您再次登录系统时，将直接进入该公司");
    }
}

// Creator : 侯杏哲 2017-09-11 获取用户的机构信息
function getOrgs(){
    $.ajax({
        url:"getAllOrgsByUserId.do" ,
        data:{} ,
        type:"post" ,
        dataType : "json" ,
        beforeSend: function(){ loading.open() ;  } ,
        success:function (data) {
            var orgA = data["orgA"] ;
            var str = "" ;
            if( orgA && orgA.length > 0 ){
                let roleCode =  sphdSocket.user.roleCode // 4是浏览者，置灰不能操作
                for(var i=0 ; i<orgA.length ; i++){
                    str += `<tr >
                                <td>${ orgA[i]["name"] }</td>
                                <td class="${  roleCode === 'browse'? 'ty-gray':'' }" 
                                    ${ roleCode === 'browse'? '' :` onclick='showCongirm(${ orgA[i]["id"] }, ${ orgA[i]["defaulOrg"] == 2 ? true : false })'` }>
                                <i class='hd'></i>
                                <i class='fa ${ orgA[i]["defaulOrg"] == 2 ? 'fa-check-square-o':'fa fa-square-o' }  '></i> 
                                默认登录的企业
                                </td>
                            </tr>`

                }
            }
            $("#main_body").html( str ) ;
        },
        error:function () {
            bounce.show( $("#tip") ) ;
            $("#tipcon").html("链接错误请稍后重试！");
        } ,
        complete:function () {
            loading.close() ;
        }
    })
}

// Creator : 侯杏哲 2017-09-11 confirm 框 确定按钮
function sureSubmit() {
    bounce.cancel();
    var state = 0 ; // state（是否默认机构 1-否 2-是）
    if(editObj["curStatus"]){ // 当前选中，设置为不选中 ,
        state = 1 ;
    }else{ // 当前不选中 ， 设置为选中
        state = 2 ;
    }
    $.ajax({
        "url": "updateDefaulOrg.do" ,
        "data" : { "orgId" : editObj["orgId"] , "state" : state } ,
        "type":"post" ,
        "dataType" : "json" ,
        beforSend:function () {  loading.open() ;  },
        success:function (data) {
            var  state = data["state"] ;  // 1-成功 0-失败
            if(state == 1){
                location.reload()  ;
            }else{
                bounce.show($("#tip")) ;  $("#tipcon").html("设置失败");
            }
        } ,
        error:function () {
            bounce.show($("#tip")) ;  $("#tipcon").html("链接错误，请刷新重试！");
        },
        complete:function () {
            loading.close() ;
        }
    })
}