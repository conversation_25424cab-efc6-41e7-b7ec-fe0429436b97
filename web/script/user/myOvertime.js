/**
 * Created by Administrator on 2017/5/12.
 */
var isfromMessage = 0 ;
$(function(){
    $.fn.select2.defaults.set("theme", "default");
    // creator： 侯杏哲 2017-05-20  处理从消息跳转过来的页面
    var applyID = GetUrlQuery("applyID") ; // 审批过程id
    if( applyID != undefined ){
        getDetail( applyID , "---" ) ;
        isfromMessage = 1 ;
    }
    // 进入页面，获取列表 
    $(".ty-secondTab li").eq(0).click();
    $("#overtimeType")  .select2({placeholder: "请选择加班日期"});
    $("#beginTime1")    .select2({placeholder: "起时间", data: getData("0:00",0)});
    $("#endTime1")      .select2({placeholder: "止时间", data: getData("17:30",0)});
    $('#beginTime1').on('select2:select', function (evt) {
        $("#endTime1").html("");
        $("#endTime1").select2({
            placeholder: "止时间",
            data: getData($(this).val(),1)
        })
    });
    $('#overtimeType').on('select2:select', function (evt) {
        var val = Number($(this).val());
        $("#endTime1").html("<option></option>");
        switch (val){
            case 1:
                $("#beginTime1").select2().val("17:00").trigger("change");
                $("#endTime1").select2({data: getData("17:30")}).val("17:30").trigger("change");
                break;
            case 2:
            case 3:
                $("#beginTime1").select2().val("8:00").trigger("change");
                $("#endTime1").select2({data: getData("8:30")}).val("8:30").trigger("change");
                break;
            default:
                $("#beginTime1").select2().val("0:00").trigger("change");
                $("#endTime1").select2({data: getData("0:30")});
        }
    });
});
function getData(start,delay) {

    var data = [];
    var a = start.split(":")[0];
    var b = start.split(":")[1];

    for(var i=a;i<24;i++){
        data.push({"id" : i + ':' + '00', "text" : i+':'+'00'});
        data.push({"id" : i + ':' + '30', "text" : i+':'+'30'});
    }
    if(b === "30"){
        data.shift();
    }
    if(delay>0){
        for(var j=0;j<delay;j++){
            data.shift();
        }
    }
    return data;

}
function overtime_request(num ) {
    var val = num;
    var url = "";
    if(Number(val) == 1 ){
        url = "myOvertime.do"; // 加班
    }
    if(Number(val) == 2 ){
        url = "myLeave.do"; //请假
    }
    if(Number(val) == 3 ){
        url = "myApplication.do"; // 财务
    }
    if( url == ""){ return false; }
    location.href = url;
}
// 二级菜单切换
function leavShow( num , obj  ){ //  num :1 待处理 2 已批准  3 已驳回
    obj.addClass('ty-active').siblings().removeClass('ty-active');
    $('#for_n').html(obj.html()) ;
    $("#tab_15_"+num).show().siblings().hide();
    getApplyList(num , 1 , 15 ) ;
}
// 获得请假申请列表
function getApplyList( type , pageNumber , quantum  ){ // type:状态 ； pageNumber：每页条数 ； quantum：总页数
    var n = (pageNumber - 1) * quantum ;
    $.ajax({
        url:"../lo/myOutTimeListByLoginUser.do" ,
        data:{ "approvalStatus": type , "pageNumber":pageNumber , "quantum":quantum  } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            var list = data["personnelOvertimeList"] ;
            var str = "" ;
            if(list && list.length>0){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<tr>" +
                        "<td>"+ ++n +"</td>" +
                        "<td>"+ list[i]["createName"] +"</td>" +
                        "<td>"+ list[i]["beginTime"] +"</td>" +
                        "<td>"+ list[i]["endTime"] +"</td>" +
                        "<td>"+ list[i]["duration"] +"</td>" +
                        "<td>"+ chargeType( list[i]["type"] ) +"</td>" +
                        "<td>" +
                        "<span class='ty-color-blue' onclick='getDetail("+ list[i]["id"] +", "+ n +" )'>查看</span></td>" +
                        "</td>" +
                        "</tr>" ;
                }
            }
            $("#tbl"+type).html(str);
            var cur = data["pageNumber"] , total = data["totalPage"] ;
            var jsonStr = JSON.stringify({"type": type }) ;
            setPage( $("#ye" + type) , cur , total , "myApply_overtime" , jsonStr );
        },
        error:function(){
            $("#mt_tip_ms").html("获得列表失败") ;
            bounce.show($("#mtTip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    }) ;
}
// 判别请假类型
function chargeType( type ){
    if( type == "1" ){  return "工作日加班";  }
    if( type == "2" ){  return "周末假日加班";  }
    if( type == "3" ){  return "法定节假日加班";  }

    return "未识别" ;
}
// 获得某条详情  
function getDetail( id , no){
    if(id){
        $.ajax({
            url:"../lo/myOutTimeInfoById.do" ,
            data:{ "id" : id } ,
            type:"post" ,
            dataType:"json" ,
            beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
            success:function(data){
                var status = data["status"] ;
                if(status == 0 ){
                    $("#mt_tip_ms").html("获得该条记录详情失败") ;
                    bounce.show($("#mtTip")) ;
                }else{
                    var processList = data["processList"] ;
                    var info = data["personnelOvertime"] ;
                    bounce.show($("#detail")) ;     
                    var str = "" ;
                    if(processList && processList.length>0){
                        for(var i = 0 ; i < processList.length ; i++){
                            if( processList[i]["approveStatus"] == "1" ){ // 待审批
                                str += "<div class='ty-process-item ty-process-wait'>" +
                                    "<p><span class='dot-wait'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }else if( processList[i]["approveStatus"] == "3"){ // 已驳回
                                str += "<div class='ty-process-item ty-process-no '>" +
                                    "<p><span class='dot-no'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }else{  // 已批准
                                str += "<div class='ty-process-item'>" +
                                    "<p><span class='dot'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }
                        }
                    }
                    $("#process").html( str ) ;
                    if(info){
                        if( no == undefined){ no = "" ; }
                        $("#no").html( no ) ;
                        $("#createName").html(info["createName"]) ;
                        $("#createDate").html(info["createDate"]) ;
                        $("#beginTime").html(info["beginTime"]) ;
                        $("#endTime").html(info["endTime"]) ;
                        $("#type").html( chargeType(info["type"]) ) ;
                        $("#during").html(info["duration"] + " 小时") ;
                        $("#memo").html(info["reason"]) ;
                    }else{
                        $("#no").html( no ) ;
                        $("#createName").html("获取失败") ;
                        $("#beginTime").html("获取失败") ;
                        $("#endTime").html("获取失败") ;
                        $("#type").html("获取失败") ;
                        $("#during").html("获取失败") ;
                        $("#memo").html("获取失败") ;
                    }

                }
            } ,
            error:function(){
                $("#mt_tip_ms").html("获得该条记录详情失败,链接错误") ;
                bounce.show($("#mtTip")) ;
            } ,
            complete:function(){ chargeClose(1) ;  }
        });
    }else{
        $("#mt_tip_ms").html("获得该条ID失败") ;
        bounce.show($("#mtTip")) ;
    }
}
//点击新增加班申请
function overtimeApplyBtn() {
    $(".overtimeApply input").val("");
    $(".overtimeApply textarea").val("");
    $("#overtimeType").select2().val("1").trigger("change");
    $("#beginTime1").select2().val("17:00").trigger("change");
    $("#endTime1").html("") ;
    $("#endTime1")      .select2({placeholder: "止时间", data: getData("17:30",0)});
    $("#endTime1").select2().val("17:30").trigger("change");

    bounce.show($(".overtimeApply"));
}

//确定新增加班按钮
function sureOvertimeApply() {
    var overTimeDate = $("#beginDate1").val();

    var beginTime1  = chargeTime($("#beginTime1").select2("val"));
    var endTime1    = chargeTime($("#endTime1").select2("val"));
    var type        = $("#overtimeType").select2("val");
    var reason      = $(".overtimeApply .reason").val();

    var overtimeData = {
        "beginTime1": overTimeDate + " " + beginTime1 ,
        "endTime1"  : overTimeDate + " " + endTime1 ,
        "type"      : type ,
        "reason"    : reason
    };
    console.log(overtimeData);
    if($.trim(type) === ""){
        $("#mtTips #mt_tips_ms").html("请选择加班类型！");
        bounce_Fixed.show($("#mtTips"));
    }else if($.trim(overTimeDate) === ""){
        $("#mtTips #mt_tips_ms").html("请选择加班日期！");
        bounce_Fixed.show($("#mtTips"));
    }else if($.trim(beginTime1) === ""){
        $("#mtTips #mt_tips_ms").html("请选择加班开始时间！");
        bounce_Fixed.show($("#mtTips"));
    }else if($.trim(endTime1) === ""){
        $("#mtTips #mt_tips_ms").html("请选择加班结束时间！");
        bounce_Fixed.show($("#mtTips"));
    }else{
        $.ajax({   
            url : $.webRoot+"/lo/addOutTime.do",
            data : overtimeData,
            type : "post",
            dataType : "json",
            beforeSend:function(){ loading.open() ;  } ,
            success : function (data) {
                var status = data["status"];
                if(status === 1){
                    $("#mtTip #mt_tip_ms").html("申请成功！");
                    bounce.show($("#mtTip"));
                    $(".ty-secondTab li").eq(0).click();
                }else if(status === 0){
                    $("#mtTips #mt_tips_ms").html("请选择类型！");
                    bounce_Fixed.show($("#mtTips"));
                }else{
                    alert("系统错误，请重试！");
                }
            },
            error : function (err) {
                alert("系统错误！")
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
}

function chargeTime(time) {
    var a = time.split(":")[0];
    var b = time.split(":")[1];
    if(parseInt(a).length<10){
        a = "0" + a
    }
    time = a + ":" + b + ":" + "00";
    return time;
}

// creator :  侯杏哲 2017-05-20  判断若来自消息的，刷新消息的条数 
function chargeIsMs(){
    if( isfromMessage == 1 ){  isfromMessage = 0 ; msgNum();    } 
}

laydate.render({elem: '#beginDate1'});
