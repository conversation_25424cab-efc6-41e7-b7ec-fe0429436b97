/**
 * Created by Administrator on 2017/5/10.
 */


/*creator：李玉婷 date:2017/11/29 11:31 获取初始文件夹*/
$(function(){
    firstLevelLoad();
    $(".categoryName").on("input",function(evt){
        if($(this).val().trim().length){
            $(".submitBtn").removeAttr("disabled");
        }else{
            $(".submitBtn").prop("disabled","disabled");
        }
    });
    //每一级目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容）
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right") ;
            $(this).find("i").eq(0).addClass("fa-angle-down")
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down") ;
            $(this).find("i").eq(0).addClass("fa-angle-right") ;
        }
        //设置选中文件夹详细内容的头部名称
        $(".nowFolder").children("h3").html($(this).text());

        //获取子级文件夹内容
        var categoryId = $(this).attr("id") ;
        var treeItemThis = $(this) ;
        $.ajax({
            url: "../res/getFolderAndChildFolder.do",
            data: {"categoryId":categoryId , "type" :2 },
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
            success: function (data) {
                if(data == ""||data == undefined){
                    $("#mt_tip_ms").html("系统错误，请重试！");
                    bounce.show($("#mtTip"));
                }else{
                    var listNotice      = data.data["parentFolder"]; // 点击的那层文件夹的信息
                    var listNoticeChild = data.data["childFolder"]; // 点击打那层文件夹的子文件夹的信息
                    //拼接子类别侧边菜单字符串
                     var level = parseInt(treeItemThis.parent().parent().attr("level"));
                    var nextLevel = level + 1;
                    var levelStr = "";
                    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">';
                    for(var i in listNoticeChild){
                        if(listNoticeChild[i]["childStatus"] == 1){ // 1 - 有子文件夹，没有值代表没有子文件夹
                            levelStr +=     '<li>' +
                                '<div class="ty-treeItem" id='+listNoticeChild[i].id+'>' +
                                '<i class="fa fa-angle-right"></i>' +
                                '<i class="fa fa-folder"></i>' +
                                '<span>'+listNoticeChild[i].name+'</span>' +
                                '</div>' +
                                '</li>'
                        }else{
                            levelStr +=     '<li>' +
                                '<div class="ty-treeItem" id='+listNoticeChild[i].id+'>' +
                                '<i class="ty-fa"></i>' +
                                '<i class="fa fa-folder"></i>' +
                                '<span>'+listNoticeChild[i].name+'</span>' +
                                '</div>' +
                                '</li>'
                        }
                    }
                    levelStr += '</div>';
                    if(treeItemThis.children("i:first").hasClass("fa-angle-down")){
                        treeItemThis.next().remove();
                        treeItemThis.after(levelStr);
                    }else{
                        treeItemThis.next().remove();
                    }
                    //拼接此类别信息列表字符串
                    var createDate       = listNotice["createDate"].substring(0,10);
                    var updateDate      = "——";
                    if(listNotice["updateDate"]){
                        updateDate = listNotice["updateDate"].substring(0,10);
                    }
                    $(".newSameClassBtn").removeClass("hd");
                    if(listNoticeChild.length<1&&level<8){
                        var listNoticeStr      ='<tr><td>'+createDate+'</td>'+
                            '<td>'+updateDate+'</td>'+
                            '<td class="ty-td-control"><span class="ty-color-blue"  onclick="changeClass()">修改</span></td>'+
                            '<td class="ty-td-control"><span class="ty-color-red"   onclick="deleteClass()">删除</span></td>'+
                            '<td><span class="ty-color-green" onclick="newClass()"   >新增</span></td></tr>';
                        $(".childFolder").parent().show();
                    }else if(level>7){
                        var listNoticeStr      ='<tr><td>'+createDate+'</td>'+
                            '<td>'+updateDate+'</td>'+
                            '<td class="ty-td-control"><span class="ty-color-blue"  onclick="changeClass()">修改</span></td>'+
                            '<td class="ty-td-control"><span class="ty-color-red"   onclick="deleteClass()">删除</span></td>'+
                            '<td>--</td></tr>';
                        $(".childFolder").parent().hide();
                    }else{
                        var listNoticeStr      ='<tr><td>'+createDate+'</td>'+
                            '<td>'+updateDate+'</td>'+
                            '<td class="ty-td-control"><span class="ty-color-blue"  onclick="changeClass()">修改</span></td>'+
                            '<td>--</td>'+
                            '<td><span class="ty-color-green" onclick="newClass()"   >新增</span></td></tr>';
                        $(".childFolder").parent().show();
                    }

                    $(".childFolder").parent().show();


                    //此类别信息列表字符串插入表格，此类别id插入表格
                    $(".CategoryMessage").find("tbody").html("").append(listNoticeStr);
                    $(".CategoryMessage").attr("id",categoryId);


                    //alert(listNoticeChild.length);
                    //拼接子类别列表字符串
                    var listNoticeChildStr="";
                    for(var i in listNoticeChild){
                        var cName            = listNoticeChild[i].name;
                        var cCreatDate       = listNoticeChild[i].createDate.substring(0,10);
                        var cUpdateDate      = "";
                        if(listNoticeChild[i].updateDate){
                            cUpdateDate = listNoticeChild[i].updateDate.substring(0,10);
                        }else{
                            cUpdateDate = "--";
                        }

                        listNoticeChildStr +='<tr><td>'+cName+'</td>'+
                            '<td>'+cCreatDate+'</td>'+
                            '<td>'+cUpdateDate+'</td></tr>';
                    }

                    // 子类别信息列表字符串插入表格
                    $(".cCategoryMessage").find("tbody").html("").append(listNoticeChildStr);
                }
            },
            error:function (err) {
                //alert("系统错误，请重试！");
                $("#mt_tip_ms").html("系统错误，请重试！");
                bounce.show($("#mtTip"));
            } ,
            complete:function(){ chargeClose( 2 ) ;  }
        })

    });
});

function firstLevelLoad() {
    $.ajax({
        url: "../res/getInitialFolder.do",
        data: {"type": "2"},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success: function (data) {
            //没写success=1 and =0
            //存储第一级目录数组
            var listFirstFolder = data.data.listFirstFolder;
            if(listFirstFolder.length == 0){
                //该用户没有添加任何文件时
                $(".initialSect").show().siblings().hide();
                var urlParam = getUrlParam("state");
                if(urlParam == 0){
                    $(".newSameClassBtn").click();
                }
            }else {
                $(".initialSect").hide().siblings().show();
                $(".ty-fileContent").children().show();
                //拼接第一级文件夹字符串
                var firstLevelStr = "";
                firstLevelStr += '<div class="level1" level="1">';
                for (var i in listFirstFolder) {
                    //判断每个文件夹是否有子级文件夹  是否来显示左侧箭头
                    if (listFirstFolder[i].childStatus == "1") {
                        firstLevelStr += '<li>' +
                            '<div class="ty-treeItem" id=' + listFirstFolder[i].id + '>' +
                            '<i class="fa fa-angle-right"></i>' +
                            '<i class="fa fa-folder"></i>' +
                            '<span>' + listFirstFolder[i].name + '</span>' +
                            '</div>' +
                            '</li>'
                    } else {
                        firstLevelStr += '<li>' +
                            '<div class="ty-treeItem" id=' + listFirstFolder[i].id + '>' +
                            '<i class="ty-fa"></i>' +
                            '<i class="fa fa-folder"></i>' +
                            '<span>' + listFirstFolder[i].name + '</span>' +
                            '</div>' +
                            '</li>'
                    }
                }
                firstLevelStr += '</div>';

                //添加到父级容器
                $(".ty-colFileTree").html("").append(firstLevelStr);

                //设置样式使第一个文件夹点击并保持闭合状态
                $(".level1 li:first").children().click();
                $(".level1 li:first").children().click();
            }
        },
        error:function (err) { } ,
        complete:function(){ chargeClose(1) ;  }
    })
}
/* creator：张旭博，2017-05-09 10:13:27，点击新增同级类别按钮 */
/*update:李玉婷 date:2017/11/29 9:58*/
function newSameClass(num) { //num:1=该用户未建立任何文件夹 2=已建立文件夹

    //清空输入框
    $(".bounce-newSameClass .categoryName").val("");
    if(num == 1){
        $(".bounce-newSameClass .bonceHead span").html("新增类别");
        $(".status12").html("1");
    }else{
        $(".bounce-newSameClass .bonceHead span").html("新增同级类别");
        $(".status12").html("2");
    }
    //显示弹窗
    bounce.show($(".bounce-newSameClass"));
};

/* creator：张旭博，2017-05-09 10:14:11，点击新增同级类别 确认 按钮 */
/*update:李玉婷 date:2017/11/29 14:20*/
function sureNewSameClass() {
    var parentId = $(".ty-treeItemActive").parent().parent().prev().attr("id");
    if(parentId == undefined){
        parentId = null;
    }
    var categoryName =$.trim($(".bounce-newSameClass .categoryName").val());
    if(categoryName == ""){
        $("#mt_tip_ms").html("类别名称不能为空！");
        bounce.show($("#mtTip"));
    }else{
        $.ajax({
            url: "../res/AddSameFolderByData.do",
            data: {"parent":parentId,"categoryName":categoryName,"type":2},
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                //alert(data);
                var success = data["success"];
                if(success == 0){
                    $("#mt_tip_ms").html("文件夹名称重复！");
                    bounce.show($("#mtTip"));
                    $(".bounce-newSameClass .categoryName").val("");
                }else if(success == 1){
                    bounce.cancel();
                    if($(".ty-colFileTree").html() === ""){
                        window.location.reload();
                    }else{
                        if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                            firstLevelLoad();
                        }else{
                            $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                            $(".ty-treeItemActive").click();
                        }
                    }
                    bounce.show( $("#mtTip") ) ; $("#mt_tip_ms").html( "新增成功" ) ;
                }else{
                    $("#mt_tip_ms").html("未知的返回值错误！");
                    bounce.show($("#mtTip"));
                    $(".bounce-newSameClass .categoryName").val("");
                }
            },
            error:function (err) {
                $("#mt_tip_ms").html("系统错误，请重试！");
                bounce.show($("#mtTip"));
            } ,
            complete:function(){ loading.close() ;  }
        })
    }

};

/* creator：张旭博，2017-04-27 08:29:36，点击类别名称 删除 按钮 */
function deleteClass() {
    if(($(".ty-treeItemActive i").attr("class")=="fa fa-angle-down")||($(".ty-treeItemActive i").attr("class")=="fa fa-angle-right")){
        $("#mt_tip_ms").html("该文件夹不能删除!");
        bounce.show($("#mtTip"));
    }else{
        var categoryId = $(".CategoryMessage").attr("id");
        $.ajax({
            url: "../res/clickButtenJudgementFile.do",
            data: {"categoryId":categoryId},
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                if(data.success == 0){
                    $("#mt_tip_ms").html("该类别下有文件，不允许删除此类别！");
                    bounce.show($("#mtTip"));
                }else if(data.success == 1){
                    var folderName = $(".ty-treeItemActive span").text();
                    $('.bounce-deleteClass>.bonceHead>span').html("删除类别-"+folderName);
                    bounce.show($('.bounce-deleteClass'));
                }
            },
            error:function (err) {
                $("#mt_tip_ms").html("系统错误，请重试！");
                bounce.show($("#mtTip"));
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
};

/* creator：张旭博，2017-05-04 13:17:56，点击删除类别 确认 按钮 */
function sureDeleteClass() {
    var parentId = $(".ty-treeItemActive").parent().parent().prev().attr("id");
    if(parentId == undefined){
        parentId = null;
    }
    var categoryId = $(".CategoryMessage").attr("id");
    $.ajax({
        url: "../res/delFolder.do",
        data: {"categoryId":categoryId},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (data) {
            if(data.success == 0){
                $("#mt_tip_ms").html("该类别下有文件，不允许删除此类别！");
                bounce.show($("#mtTip"));
            }else if(data.success == 1){
                $("#mt_tip_ms").html("删除成功！");
                bounce.show($("#mtTip"));
                if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                    location.reload();
                }
                if($(".ty-treeItemActive").parent().parent().children().length<2){
                    $(".ty-treeItemActive").parent().parent().prev().children().eq(0).attr("class","ty-fa");
                }
                $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                $(".ty-treeItemActive").click();
            }else{
                $("#mt_tip_ms").html("删除失败！");
                bounce.show($("#mtTip"));
            }
        },
        error:function (err) {
            $("#mt_tip_ms").html("系统错误，请重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    })
};

/* creator：张旭博，2017-04-27 08:29:05，点击类别名称 修改 按钮 */
function changeClass() {
    //清空输入框
    var title = $(".ty-treeItemActive>span").text();
    $(".bounce-changeClass .categoryName").val(title);

    //显示弹窗
    bounce.show($('.bounce-changeClass'));
};

/* creator：张旭博，2017-05-04 13:16:41，点击修改类别 确认 按钮 */
function sureChangeClass() {
    var categoryId = $(".CategoryMessage").attr("id");
    var categoryName = $.trim($(".bounce-changeClass .categoryName").val());
    if(categoryName == ""){
        $("#mt_tip_ms").html("类别名称不能为空！");
        bounce.show($("#mtTip"));
    }else{
        $.ajax({
            url: "../res/UpdateFolderName.do",
            data: { "id" : categoryId,"name": categoryName,"type":2},
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                if (data.success == 1) {
                    $("#mt_tip_ms").html("更改成功！");
                    bounce.show($("#mtTip"));
                    $(".bounce-changeClass .categoryName").val("");
                    $(".ty-treeItemActive span").html(categoryName);
                    $(".ty-treeItemActive").click();
                } else if (data.success == 0) {
                    $("#mt_tip_ms").html("文件夹名称重复！");
                    bounce.show($("#mtTip"));
                } else {
                    $("#mt_tip_ms").html("更改错误！");
                    bounce.show($("#mtTip"));
                }
            },
            error: function (err) {
                $("#mt_tip_ms").html("系统错误，请重试！");
                bounce.show($("#mtTip"));
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
};
/* creator：张旭博，2017-04-27 08:29:49，点击新增子级类别按钮 */
function newClass() {
    //清空输入框
    $(".bounce-newClass .categoryName").val("");
    //判断该类别下是否有文件
    var categoryId = $(".CategoryMessage").attr("id");
    $.ajax({
        url: "../res/clickButtenJudgementFile.do",
        data: {"categoryId":categoryId},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (data) {
            if(data == "" || data == undefined){
                $("#mt_tip_ms").html("返回值不存在！");
                bounce.show($("#mtTip"));
            }else{
                var status = data["success"];
                if(status == 0){
                    $("#mt_tip_ms").html(data.data);
                    bounce.show($("#mtTip"));
                }else if(status == 1){
                    //显示弹窗
                    bounce.show($('.bounce-newClass'));
                }else{
                    $("#mt_tip_ms").html("未知的返回值错误！");
                    bounce.show($("#mtTip"));
                }
            }
        },
        error:function (err) {
            $("#mt_tip_ms").html("系统错误，请重试！404");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    })
};

/* creator：张旭博，2017-05-04 13:18:40，点击新增子级类别 确认 按钮 */
function sureNewClass() {
    var categoryId = $(".CategoryMessage").attr("id");
    var categoryName = $.trim($(".bounce-newClass .categoryName").val());
    var maxChildCategorys = $(".ty-treeItemActive").next().children("li").length;
    if(categoryName == ""){
        $("#mt_tip_ms").html("类别名称不能为空！");
        bounce.show($("#mtTip"));
    }else{
        $.ajax({
            url: "../res/AddSameFolderByData.do",
            data: {"parent":categoryId,"categoryName":categoryName,"type": 2,"maxChildCategorys":maxChildCategorys},
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                if(data == null || data == undefined){
                    $("#mt_tip_ms").html("返回值不存在！");
                    bounce.show($("#mtTip"));
                }else{
                    var status = data["success"];
                    if(status == 0){
                        $("#mt_tip_ms").html("文件夹名称重复！");
                        bounce.show($("#mtTip"));
                        $(".bounce-newClass .categoryName").val("");
                    }else if(status == 1){
                        bounce.show( $("#mtTip") ) ; $("#mt_tip_ms").html("新增成功") ;
                        if($(".ty-treeItemActive").children().eq(0).hasClass("ty-fa")){
                            $(".ty-treeItemActive").children().eq(0).attr("class","fa fa-angle-right");
                            $(".ty-treeItemActive").click();
                        }else{
                            $(".ty-treeItemActive").click();
                            $(".ty-treeItemActive").click();
                        }

                    }else{
                        $("#mt_tip_ms").html("未知的返回值错误！");
                        bounce.show($("#mtTip"));
                        $(".bounce-newClass .categoryName").val("");
                    }
                }
            },
            error:function (err) {
                $("#mt_tip_ms").html("系统错误，请重试！");
                bounce.show($("#mtTip"));
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
};













/* creator：张旭博，2017-04-27 08:28:29，页面初始化以及事件绑定 */
/*
$(function () {
    //第一级目录加载
    firstLevelLoad("load");
    //每一级目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容）
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right") ;
            $(this).find("i").eq(0).addClass("fa-angle-down")
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down") ;
            $(this).find("i").eq(0).addClass("fa-angle-right") ;
        }
        //设置选中文件夹详细内容的头部名称
        $(".nowFolder").children("h3").html($(this).text());

        //获取子级文件夹内容
        var categoryId = $(this).attr("id");
        var treeItemThis = $(this);
        $.ajax({
            url: "../res/getCategoryMessage.do",
            data: {"categoryId":categoryId},
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
            success: function (data) {
                if(data == ""||data == undefined){
                    alert("系统错误，请重试！")
                }else{
                    //存储返回的值（此类别的信息和子集类别的信息）
                    var listNotice      = data["listNotice"];
                    var listNoticeChild = data["listNoticeChild"];

                    //拼接子类别侧边菜单字符串
                    var level = parseInt(treeItemThis.parent().parent().attr("level"));
                    var nextLevel = level + 1;
                    var levelStr = "";
                    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">';
                    for(var i in listNoticeChild){
                        if(listNoticeChild[i].maxChildCategorys > 0){
                            levelStr +=     '<li>' +
                                '<div class="ty-treeItem" id='+listNoticeChild[i].id+'>' +
                                '<i class="fa fa-angle-right"></i>' +
                                '<i class="fa fa-folder"></i>' +
                                '<span>'+listNoticeChild[i].name+'</span>' +
                                '</div>' +
                                '</li>'
                        }else{
                            levelStr +=     '<li>' +
                                '<div class="ty-treeItem" id='+listNoticeChild[i].id+'>' +
                                '<i class="ty-fa"></i>' +
                                '<i class="fa fa-folder"></i>' +
                                '<span>'+listNoticeChild[i].name+'</span>' +
                                '</div>' +
                                '</li>'
                        }
                    }
                    levelStr += '</div>';
                    if(treeItemThis.children("i:first").hasClass("fa-angle-down")){
                        treeItemThis.next().remove();
                        treeItemThis.after(levelStr);
                    }else{
                        treeItemThis.next().remove();
                    }

                    //拼接此类别信息列表字符串
                    var createDate       = listNotice[0].createDate.substring(0,10);
                    var updateDate      = "";
                    if(listNotice[0].updateDate){
                        updateDate = listNotice[0].updateDate.substring(0,10);
                    }else{
                        updateDate = "--";
                    }

                    var userType = parseInt($("#loginUserType").text());
                    if(userType === ""||userType === undefined){
                        alert("未获取权限！")
                    }else if(userType == 3 || userType == 10){
                        $(".newSameClassBtn").removeClass("hd");
                        if(listNoticeChild.length<1&&level<5){
                            var listNoticeStr      ='<tr><td>'+createDate+'</td>'+
                                '<td>'+updateDate+'</td>'+
                                '<td><span class="ty-color-blue"  onclick="changeClass()">修改</span></td>'+
                                '<td><span class="ty-color-red"   onclick="deleteClass()">删除</span></td>'+
                                '<td><span class="ty-color-green" onclick="newClass()"   >新增</span></td></tr>';
                            $(".childFolder").parent().show();
                        }else if(level>4){
                            var listNoticeStr      ='<tr><td>'+createDate+'</td>'+
                                '<td>'+updateDate+'</td>'+
                                '<td><span class="ty-color-blue"  onclick="changeClass()">修改</span></td>'+
                                '<td><span class="ty-color-red"   onclick="deleteClass()">删除</span></td>'+
                                '<td>--</td></tr>';
                            $(".childFolder").parent().hide();
                        }else{
                            var listNoticeStr      ='<tr><td>'+createDate+'</td>'+
                                '<td>'+updateDate+'</td>'+
                                '<td><span class="ty-color-blue"  onclick="changeClass()">修改</span></td>'+
                                '<td>--</td>'+
                                '<td><span class="ty-color-green" onclick="newClass()"   >新增</span></td></tr>';
                            $(".childFolder").parent().show();
                        }
                    }else{
                        $(".newSameClassBtn").addClass("hd");
                        if(level>4){
                            var listNoticeStr      ='<tr><td>'+createDate+'</td>'+
                                '<td>'+updateDate+'</td>'+
                                '<td>--</td>'+
                                '<td>--</td>'+
                                '<td>--</td></tr>';
                            $(".childFolder").parent().hide();
                        }else{
                            var listNoticeStr      ='<tr><td>'+createDate+'</td>'+
                                '<td>'+updateDate+'</td>'+
                                '<td>--</td>'+
                                '<td>--</td>'+
                                '<td>--</td></tr>';
                            $(".childFolder").parent().show();
                        }
                    }


                    //此类别信息列表字符串插入表格，此类别id插入表格
                    $(".CategoryMessage").find("tbody").html("").append(listNoticeStr);
                    $(".CategoryMessage").attr("id",categoryId);


                    //拼接子类别列表字符串
                    var listNoticeChildStr="";
                    for(var i in listNoticeChild){
                        var cName            = listNoticeChild[i].name;
                        var cCreatDate       = listNoticeChild[i].createDate.substring(0,10);
                        var cUpdateDate      = "";
                        if(listNoticeChild[i].updateDate){
                            cUpdateDate = listNoticeChild[i].updateDate.substring(0,10);
                        }else{
                            cUpdateDate = "--";
                        }

                        listNoticeChildStr +='<tr><td>'+cName+'</td>'+
                            '<td>'+cCreatDate+'</td>'+
                            '<td>'+cUpdateDate+'</td></tr>';
                    }

                    //子类别信息列表字符串插入表格
                    $(".cCategoryMessage").find("tbody").html("").append(listNoticeChildStr);
                }
            },
            error:function (err) {
                alert("系统错误，请重试！")
            } ,
            complete:function(){ chargeClose( 2 ) ;  }
        })
    })
});
*/

/*
function successUserTypeInitPage(){
    var userType = parseInt($("#loginUserType").text());
    if(userType === 3|| userType === 10){
        $(".newSameClassBtn").removeClass("hd");
    }else{
        $(".newSameClassBtn").addClass("hd");
    }
}*/

/* creator：张旭博，2017-05-15 13:46:44，第一级类别加载 */
/*

function firstLevelLoad(isRefresh) {
    $.ajax({
        url: "../res/resCategoryType.do",
        data: {},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success: function (data) {
            //存储第一级目录数组
            var listFirstCategory = data.listFirstCategory;

            //拼接第一级文件夹字符串
            var firstLevelStr = "";
            firstLevelStr += '<div class="level1" level="1">';
            for(var i in listFirstCategory){
                //判断每个文件夹是否有子级文件夹  是否来显示左侧箭头
                if(listFirstCategory[i].maxChildCategorys > 0){
                    firstLevelStr +=    '<li>' +
                        '<div class="ty-treeItem" id='+listFirstCategory[i].id+'>' +
                        '<i class="fa fa-angle-right"></i>' +
                        '<i class="fa fa-folder"></i>' +
                        '<span>'+listFirstCategory[i].name+'</span>' +
                        '</div>' +
                        '</li>'
                }else{
                    firstLevelStr +=    '<li>' +
                        '<div class="ty-treeItem" id='+listFirstCategory[i].id+'>' +
                        '<i class="ty-fa"></i>' +
                        '<i class="fa fa-folder"></i>' +
                        '<span>'+listFirstCategory[i].name+'</span>' +
                        '</div>' +
                        '</li>'
                }
            }
            firstLevelStr += '</div>';

            //添加到父级容器
            $(".ty-colFileTree").html("").append(firstLevelStr);

            //设置样式使第一个文件夹点击并保持闭合状态
            if(isRefresh === "load"){
                $(".level1 li:first").children().click();
                $(".level1 li:first").children().click();
            }else if(isRefresh === "refresh"){
                $(".level1 li:last").children().click();
            }
        },
        error:function (err) { } ,
        complete:function(){ chargeClose(1) ;  }
    })
}
*/

/* creator：张旭博，2017-04-27 08:29:05，点击类别名称 修改 按钮 */
/*function changeClass() {
    //清空输入框
    var title = $(".ty-treeItemActive>span").text();
    $(".bounce-changeClass .categoryName").val(title);

    //显示弹窗
    bounce.show($('.bounce-changeClass'));
};*/

/* creator：张旭博，2017-05-04 13:16:41，点击修改类别 确认 按钮 */
/*function sureChangeClass() {
    var categoryId = $(".CategoryMessage").attr("id");
    var categoryName = $.trim($(".bounce-changeClass .categoryName").val());
    if(categoryName == ""){
        alert("类别名称不能为空！")
    }else{
        $.ajax({
            url: "../res/editCategoryName.do",
            data: {"categoryId": categoryId, "categoryName": categoryName},
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                if (data.code == 1) {
                    alert("更改成功！") ;
                    bounce.cancel();
                    $(".bounce-changeClass .categoryName").val("");
                    $(".ty-treeItemActive span").html(categoryName);
                    $(".ty-treeItemActive").click();
                    $(".ty-treeItemActive").click();
                } else if (data.code == 0) {
                    alert("类别名称重复，请更换类别名称！")
                } else {
                    alert("更改错误！")
                }
            },
            error: function (err) {
                alert("系统错误，请重试！")
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
};*/

/* creator：张旭博，2017-04-27 08:29:36，点击类别名称 删除 按钮 */
/*function deleteClass() {
    var folderName = $(".ty-treeItemActive span").text();
    $('.bounce-deleteClass>.bonceHead>span').html("删除类别-"+folderName);
    bounce.show($('.bounce-deleteClass'));
};*/

/* creator：张旭博，2017-05-04 13:17:56，点击删除类别 确认 按钮 */
/*function sureDeleteClass() {
    var parentId = $(".ty-treeItemActive").parent().parent().prev().attr("id");
    if(parentId == undefined){
        parentId = null;
    }
    var categoryId = $(".CategoryMessage").attr("id");
    $.ajax({
        url: "../res/deleteCategory.do",
        data: {"categoryId":categoryId,"parent":parentId},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (data) {
            if(data.status == 0){
                alert("该类别下有文件，不允许删除此类别！")
                bounce.cancel();
            }else if(data.status == 1){
                alert("删除成功！");
                bounce.cancel();
                if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                    location.reload();
                }
                if($(".ty-treeItemActive").parent().parent().children().length<2){
                    $(".ty-treeItemActive").parent().parent().prev().children().eq(0).attr("class","ty-fa");
                }
                $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                $(".ty-treeItemActive").click();
            }else{
                alert("删除失败！")
            }
        },
        error:function (err) {
            alert("系统错误，请重试！")
        } ,
        complete:function(){ loading.close() ;  }
    })
};*/

/* creator：张旭博，2017-04-27 08:29:49，点击新增子级类别按钮 */
/*function newClass() {
    //清空输入框
    $(".bounce-newClass .categoryName").val("");
    //判断该类别下是否有文件
    var categoryId = $(".CategoryMessage").attr("id");
    $.ajax({
        url: "../res/clickAddCategory.do",
        data: {"categoryId":categoryId},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (data) {
            if(data == "" || data == undefined){
                alert("返回值不存在！")
            }else{
                var status = data["status"];
                if(status == 0){
                    alert("该类别下有文件，不允许新增下级类别")
                }else if(status == 1){
                    //显示弹窗
                    bounce.show($('.bounce-newClass'));
                }else{
                    alert("未知的返回值错误！")
                }
            }
        },
        error:function (err) {
            alert("系统错误，请重试！404")
        } ,
        complete:function(){ loading.close() ;  }
    })
};*/

/* creator：张旭博，2017-05-04 13:18:40，点击新增子级类别 确认 按钮 */
/*function sureNewClass() {
    var categoryId = $(".CategoryMessage").attr("id");
    var categoryName = $.trim($(".bounce-newClass .categoryName").val());
    var maxChildCategorys = $(".ty-treeItemActive").next().children("li").length;
    if(categoryName == ""){
        alert("类别名称不能为空！")
    }else{
        $.ajax({
            url: "../res/addResourceCategory.do",
            data: {"categoryid":categoryId,"categoryName":categoryName,"maxChildCategorys":maxChildCategorys},
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                if(data == null || data == undefined){
                    alert("返回值不存在！")
                }else{
                    var status = data["status"];
                    if(status == 0){
                        alert("类别名称重复，请更换类别名称！");
                        $(".bounce-newClass .categoryName").val("");
                    }else if(status == 1){
                        alert("新增成功！");
                        bounce.cancel();
                        if($(".ty-treeItemActive").children().eq(0).hasClass("ty-fa")){
                            $(".ty-treeItemActive").children().eq(0).attr("class","fa fa-angle-right");
                            $(".ty-treeItemActive").click();
                        }else{
                            $(".ty-treeItemActive").click();
                            $(".ty-treeItemActive").click();
                        }

                    }else{
                        alert("未知的返回值错误！")
                        $(".bounce-newClass .categoryName").val("");
                    }
                }
            },
            error:function (err) {
                alert("系统错误，请重试！")
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
};*/

/* creator：张旭博，2017-05-09 10:13:27，点击新增同级类别按钮 */
/*function newSameClass() {
    //清空输入框
    $(".bounce-newSameClass .categoryName").val("");
    //显示弹窗
    bounce.show($(".bounce-newSameClass"));
};*/

/* creator：张旭博，2017-05-09 10:14:11，点击新增同级类别 确认 按钮 */
/*
function sureNewSameClass() {
    var parentId = $(".ty-treeItemActive").parent().parent().prev().attr("id");
    if(parentId == undefined){
        parentId = null;
    }
    var categoryName =$.trim($(".bounce-newSameClass .categoryName").val());
    if(categoryName == ""){
        alert("类别名称不能为空！")
    }else{
        $.ajax({
            url: "../res/addEqatuveCategory.do",
            data: {"parent":parentId,"categoryName":categoryName},
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                var status = data["status"];
                if(status == 0){
                    alert("类别名称重复，请更换类别名称！");
                    $(".bounce-newSameClass .categoryName").val("");
                }else if(status == 1){
                    bounce.cancel();
                    if($(".ty-colFileTree").html() === ""){
                        window.location.reload();
                    }else{
                        if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                            firstLevelLoad("refresh");
                        }else{
                            $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                            $(".ty-treeItemActive").click();
                        }
                    }
                    alert("新增成功");
                }else{
                    alert("未知的返回值错误！")
                    $(".bounce-newSameClass .categoryName").val("");
                }
            },
            error:function (err) {
                alert("系统错误，请重试！")
            } ,
            complete:function(){ loading.close() ;  }
        })
    }

};
*/

