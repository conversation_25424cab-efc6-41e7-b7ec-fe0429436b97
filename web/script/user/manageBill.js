/**
 * Created by Administrator on 2017/4/12.
 */
var num_id="";
// creator:孟闯闯，2017-4-12  12:00:00，超管审批批准按钮
function decide(num1,num2){
    $.ajax({
        url:"../expense/approvalPersonnelReimburse.do",
        data:{approvalProcessId :num2 ,approvalStatus:num1},
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            console.log(data);
            if(data.status==1){
                alert("操作成功");
                refreshOpener();
            }else{
                alert("操作失败")
            }
        },
        error:function(){ } ,
        complete:function(){ loading.close() ;  }
    })
}
// creator:孟闯闯，2017-4-12  12:00:00，超管审批驳回按钮
function supper_reject(num){
    num_id=num;
    bounce_Fixed.show($("#superAccount"));
}
// creator:孟闯闯，2017-4-12  12:00:00，超管审批驳回按钮确定
function super_delsure() {
    var reson = $("#super_reson").val();
    console.log(reson)
    if(reson==""){
        alert("请输入驳回原因");
        return false ;
    }
    $.ajax({
        url : "../reimburse/financeReceiveReimbursement.do" ,
        data: { "approvalProcessId":num_id ,"approvalStatus":0 , "approveMemo" : reson } ,
        type : "post",
        dataType : "json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            refreshOpener();
        },
        error:function (meg) {
            alert("连接错误，请稍后重试！");
        } ,
        complete:function(){ loading.close() ;  }
    })
}
// creator:孟闯闯，2017-4-12  12:00:00，财务审批批准按钮
function finance_agree(num1,num2){
    var data={};
    if(num1==1){
        data={"approvalProcessId":num2 ,"approvalStatus":num1 };
    }else if(num1==0){
        
        data={"approvalProcessId":num2 ,"approvalStatus":num1 , "approveMemo" : 11111};
    }
    $.ajax({
        url : "../reimburse/financeReceiveReimbursement.do" ,
        data: data ,
        type : "post",
        dataType : "json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            var status = data["status"];
            if(data["status"]==1){
                alert("成功");
                refreshOpener();
            }
        },
        error:function (meg) {
            alert("连接错误，请稍后重试！");
        } ,
        complete:function(){ loading.close() ;  }
    })
}
// creator:孟闯闯，2017-4-12  12:00:00，财务审批驳回按钮
function reject_btn(num) {
    num_id=num;
    bounce_Fixed.show($("#rejectAccount"));
}
// creator:孟闯闯，2017-4-12  12:00:00，财务审批驳回按钮确定
function reject_delsure() {
    var reson = $("#super_reson").val();
    if(reson==""){
        alert("请输入驳回原因");
        return false ;
    }
    $.ajax({
        url : "../reimburse/financeReceiveReimbursement.do" ,
        data: { "approvalProcessId":num_id ,"approvalStatus":0 , "approveMemo" : reson } ,
        type : "post",
        dataType : "json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            // var status = data["status"];
            // if( status = 0 || status == "0"){
            //     bounce.show($("#mtTip"));
            //     $("#mt_tip_ms").html("操作失败！");
            //     return false;
            // }
            // bounce.show($("#mtTip"));
            // $("#mt_tip_ms").html("驳回操作成功！");
            // $(".finance_hide").hide().siblings().show().parent().show();
            // $(".see").hide().siblings().show();
            refreshOpener();
        },
        error:function (meg) {
            alert("连接错误，请稍后重试！");
        } ,
        complete:function(){ loading.close() ;  }
    })
}
// creator:孟闯闯，2017-4-12  12:00:00，是否显示银行转账视图
function toggleTansBank(){
    var payMethod = $("#payMethod").val() ;
    if( payMethod == 3){
        getBankInfo();
        $("#tansBank").show();
    }else{
        $("#tansBank").hide();
    }
}
// creator:孟闯闯，2017-4-12  12:00:00，财务两讫批准按钮
function balance_dealBtn(num){
    num_id=num;
    bounce_Fixed.show( $("#balanceReceive") ) ;
    $("#tansBank").hide() ;
    $("#payMethod").val("1") ;
}
// creator:孟闯闯，2017-4-12  12:00:00，银行卡号获取
function getBankInfo(){
    $.ajax({
        url:"../data/chooseCheque.do" ,
        data:{ "type" : 4 } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ;  } ,
        success:function ( data ) {
            var content = data["content"] ;
            var str = "<option value='0'>选择转账银行</option>";
            $("#financeAccountId").html("") ;
            if( content && content.length > 0 ) {
                for (var i = 0; i < content.length; i++) {
                    str += "<option value='"+ content[i]["id"] +"'>"+ content[i]["bankName"] + "  " + content[i]["account"] +"</option>";
                }
            }
            $("#financeAccountId").html(str) ;
        },
        error:function () {
            $("#mt_tip_ms").html("获得可用银行失败，请刷新重试！"); bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    })

}
// creator:孟闯闯，2017-4-12  12:00:00，财务两讫批准确定
function balance_deal(){

    var payMethod = $("#payMethod").val() ;
    var financeAccountId = $("#financeAccountId").val() ;
    if( payMethod == 3){
        if( $.trim(financeAccountId) == "0" ){  bounce.show($("#mtTip"));  $("#mt_tip_ms").html("未选择转账银行，请重试！");  return false;  }
    }
    $.ajax({
        url : "../reimburse/financeAccountBalance.do" ,
        data: {
            "payMethod":payMethod ,
            "financeAccountId":financeAccountId ,
            "approvalProcessId":num_id,
            "approvalStatus":1
        } ,
        type : "post",
        dataType : "json" ,
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            var status = data["status"];
            if( status == 0 ){
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("操作失败！");
                return false;
            }else if( status == 2 ){
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("余额不足，请选择其他支付方式下账！");
                return false;
            }else{
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("两讫操作成功！");
                // $(".finance_hide").hide().siblings().show().parent().show();
                $(".see").hide().siblings().show();
            }
            refreshOpener();
        },
        error:function (meg) {
            alert("连接错误，请稍后重试！");
        } ,
        complete:function(){ loading.close() ;  }
    })
    
}
// creator:孟闯闯，2017-4-12  12:00:00，财务两讫驳回按钮
function reject_balancebtn(num) {
    num_id=num;
    bounce_Fixed.show($("#balancerejectAccount"));
}
// creator:孟闯闯，2017-4-12  12:00:00，财务两讫驳回确定
function balanceReject_delsure() {
    var reson = $("#reject_reson").val();
    if(reson==""){
        alert("请输入驳回原因");
        return false;
    }
    $.ajax({
        url : "../reimburse/financeAccountBalance.do" ,
        data: { "approvalProcessId":num_id ,"approvalStatus":0 , "approveMemo":reson  } ,
        type : "post",
        dataType : "json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            var status = data["status"];
            if( status == 0 || status == "0"){
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("操作失败！");
                return false;
            }
            bounce.show($("#mtTip"));
            $("#mt_tip_ms").html("驳回操作成功！");
            // $(".finance_hide").hide().siblings().show().parent().show();
            $(".see").hide().siblings().show();
            refreshOpener();
        },
        error:function (meg) {
            alert("连接错误，请稍后重试！");
        } ,
        complete:function(){ loading.close() ;  }
    })
}








// 鼠标进入图片
function imgEnter(obj) {
    var path = obj.attr("src") ;
    $("#bigImag").attr("src" , path);
    $("#bigImagcon").show();
}
// 鼠标离开图片
function imgOut(obj) {
    $("#bigImagcon").hide();
}

//关闭窗口
function reject_cancel(obj){

}