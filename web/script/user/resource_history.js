/**
 * Created by Administrator on 2017/5/10.
 */

$(function () {
    $(".ty-fileList").on("click",".ty-fileItem",function () {
        $(".ty-fileItem").removeClass("ty-fileItemActive") ;
        $(this).addClass("ty-fileItemActive") ;
    });
    getRecord( 1,20, "");
})

//updator:王静 2017-08-03 15:38:23 获取换版后的历史文件列表
function getRecord(curpage, pernum, fileId){
    //得到地址栏传过来的ID
    var fileId = getQueryString("id");
    //alert(fileId);
    $.ajax({
        url: "../res/getUpdateFile.do",
        data: {"fileId":fileId,"currentPageNo":curpage,"pageSize":pernum},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success: function (data) {
            var cur=data.data["pageInfo"].currentPageNo;
            var count=data.data["pageInfo"].totalPage;
            var jsonStr=JSON.stringify({"fileId":fileId});
            setPage( $("#ye_con") , cur , count , "changeRecord" , jsonStr);
            if(data == ""||data == undefined) {
                $("#mt_tip_ms").html("系统错误，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var list = data.data["list"];
                var itemStr = "";
                if(list.length>0){
                    for(var i in list){
                        var fileIconStr = "";
                        var fileType = list[i].version;
                        switch (fileType){
                            case "doc":case "docx":
                            fileIconStr = '<div class="ty-fileType ty-file_docx ty-left"></div>';
                            break;
                            case "xls":case "xlsx":case "et":
                            fileIconStr = '<div class="ty-fileType ty-file_xlsx ty-left"></div>';
                            break;
                            case "ppt":
                                fileIconStr = '<div class="ty-fileType ty-file_pptx ty-left"></div>';
                                break;
                            case "rar":case "zip":
                            fileIconStr = '<div class="ty-fileType ty-file_zip ty-left"></div>';
                            break;
                            case "pdf":
                                fileIconStr = '<div class="ty-fileType ty-file_pdf ty-left"></div>';
                                break;
                            case "png":case "jpg":case "gif":
                            fileIconStr = '<div class="ty-fileType ty-file_jpg ty-left"></div>';
                            break;
                            default:
                                fileIconStr = '<div class="ty-fileType ty-fileOther ty-left"></div>';
                        }
                        var size = list[i].size;
                        var sizeStr = "";
                        if(size<102400){
                            sizeStr = parseFloat(list[i].size/1024).toFixed(2) + 'KB';
                        }else{
                            sizeStr = parseFloat(list[i].size/1048576).toFixed(2) + 'MB';
                        }
                        itemStr +=  '<div class="ty-fileItem ty-table-control" style="margin-bottom:28px;" id="'+list[i].id+'">'+fileIconStr+
                            '<div class="ty-fileInfo ty-left">'+
                            '<div class="ty-fileName" title="'+list[i].name+ '-G'+list[i]["changeNum"]+'">'+list[i].name +'</div>'+
                            '<div class="ty-fileVersion">'+'G'+list[i]["changeNum"]+'</div>'+
                            '<div class="ty-fileNo">编号 ：'+list[i]["fileSn"]+'</div>'+
                            '<div class="ty-fileDetail">';
                        if(list[i].updateName==null || list[i].updateName==""){
                            itemStr+='<span>'+list[i].createName+'</span>';
                        }else{
                            itemStr+='<span>'+list[i].updateName+'</span>';
                        }
                        itemStr+='<span style="margin-left:15px;"></span>' +sizeStr+ ' -- ';
                        //alert(i+': '+list[i].updateDate);
                        if(list[i].updateDate==null || list[i].updateDate==""){
                            //alert(list[i].createDate);
                            itemStr+=(list[i].createDate).substring(0,19);
                        }else{
                            itemStr+=(list[i].updateDate).substring(0,19);
                        }
                        itemStr+='</div></div>'+
                            '<div class="ty-fileHandle ty-right">'+
                            '<a class="ty-left" path="'+ list[i].path +'" onclick="seeOnline($(this))">在线预览</a>' +
                            '<a class="ty-left" path="'+ list[i].path +'" onclick="getDownLoad($(this));" download="'+ list[i].name + '.' + fileType+'">下载</a>'+
                            '<a class="ty-left" onclick="basicMessage($(this))">基本信息</a>'+
                            '</div>'+
                            '</div>';
                    }
                    $(".ty-fileList").html("") ;
                    $(".ty-fileList").append(itemStr);
                }
            }
        },
        error:function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    })
}













function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}
/*$(function () {
    $(".ty-fileList").on("click",".ty-fileItem",function () {
        $(".ty-fileItem").removeClass("ty-fileItemActive") ;
        $(this).addClass("ty-fileItemActive") ;
    });
    getRecord( 1,20, "");
}) ;*/
//updator:王静 2017-08-03 15:38:23 获取换版后的历史文件列表
/*function getRecord(curpage, pernum, fileId){
    //得到地址栏传过来的ID
    var fileId = getQueryString("id");
    $.ajax({
        url: "../res/resourceHistory.do",
        data: {"fileId":fileId,"curpage":curpage,"pernum":pernum},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success: function (data) {
            var cur=data["curpage"];
            var count=data["countpage"];
            var jsonStr=JSON.stringify({"fileId":fileId});
            setPage( $("#ye_con") , cur , count , "changeRecord" , jsonStr);
            if(data == ""||data == undefined) {
                $("#mt_tip_ms").html("系统错误，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var list = data["list"];
                var itemStr = "";
                if(list.length>0){
                    for(var i in list){
                        var fileIconStr = "";
                        var fileType = list[i].type;
                        switch (fileType){
                            case "doc":case "docx":
                            fileIconStr = '<div class="ty-fileType ty-fileDoc ty-left"></div>';
                            break;
                            case "xls":case "xlsx":case "et":
                            fileIconStr = '<div class="ty-fileType ty-fileXls ty-left"></div>';
                            break;
                            case "ppt":
                                fileIconStr = '<div class="ty-fileType ty-filePpt ty-left"></div>';
                                break;
                            case "rar":case "zip":
                            fileIconStr = '<div class="ty-fileType ty-fileRar ty-left"></div>';
                            break;
                            case "pdf":
                                fileIconStr = '<div class="ty-fileType ty-filePdf ty-left"></div>';
                                break;
                            case "png":case "jpg":case "gif":
                            fileIconStr = '<div class="ty-fileType ty-fileJpg ty-left"></div>';
                            break;
                            default:
                                fileIconStr = '<div class="ty-fileType ty-fileOther ty-left"></div>';
                        }
                        var size = list[i].size;
                        var sizeStr = "";
                        if(size<102400){
                            sizeStr = parseFloat(list[i].size/1024).toFixed(2) + 'KB';
                        }else{
                            sizeStr = parseFloat(list[i].size/1048576).toFixed(2) + 'MB';
                        }
                        itemStr +=  '<div class="ty-fileItem ty-table-control" style="margin-bottom:28px;" id="'+list[i].id+'">'+fileIconStr+
                            '<div class="ty-fileInfo ty-left">'+
                            '<div class="ty-fileName" title="'+list[i].name+ '-G'+list[i]["changeNum"]+'">'+list[i].name +'</div>'+
                            '<div class="ty-fileVersion">'+'G'+list[i]["changeNum"]+'</div>'+
                            '<div class="ty-fileNo">编号 ：'+list[i]["fileSn"]+'</div>'+
                            '<div class="ty-fileDetail">';
                        if(list[i].updateName==null || list[i].updateName==""){
                            itemStr+='<span>'+list[i].createName+'</span>';
                        }else{
                            itemStr+='<span>'+list[i].updateName+'</span>';
                        }
                        itemStr+='<span style="margin-left:15px;"></span>' +sizeStr+ ' -- ';
                        if(list[i].updateDate==null || list[i].updateDate==""){
                            itemStr+=(list[i].createDate).substring(0,19);
                        }else{
                            itemStr+=(list[i].updateDate).substring(0,19);
                        }
                        itemStr+='</div>'+
                            '</div>'+
                            '<div class="ty-fileHandle ty-right">'+
                            '<span class="ty-color-blue ty-left cz_handle" onclick=\"yj($(this),'+i+',event)\" style="position: relative;">操作</span>'+
                            '<ul class="hel" id=\"handleul'+i+'\" style="display:none;">'+
                            '<li class="ty-left ulli"><a target="_blank" href="/upload/'+list[i].path+'" download="'+list[i].name+"-G"+list[i].changeNum+'.'+list[i].type+'">下载</a></li>'+
                            '<li class="ty-left ulli"><a path="'+ list[i].path+'" onclick="seeOnline($(this))">在线预览</a></li>'+
                            '</ul>'+
                            '<span class="ty-color-blue ty-left"  onclick="basicMessage($(this))">基本信息</span>'+
                            '</div>'+
                            '</div>';
                    }
                    $(".ty-fileList").html("") ;
                    $(".ty-fileList").append(itemStr);
                }
            }
        },
        error:function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ chargeClose(1) ;  }
    })
}*/
//updator:lyt 2017-12-05 15:01:23 点击基本信息
function basicMessage(obj){
    var hFileId=obj.parent().parent().attr("id");
    $.ajax({
        url: "../res/getUpFileMesByReference.do",
        data: {"id": hFileId},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (data) {
            if (data == "" || data == undefined) {
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            } else {
                var list = data["data"]["resource"];
                var operateDate = list.operateDate;
                // if(operateDate == "1900-01-01 00:00:00.0"){
                //     operateDate = "--";
                // }
                var verifyDate = list.verifyDate;
                // if(verifyDate == "1900-01-01 00:00:00.0"){
                //     verifyDate = "--";
                // }
                var approveDate = list.approveDate;
                // if(approveDate == "1900-01-01 00:00:00.0"){
                //     approveDate = "--";
                // }
                // var reason = list.reason;
                // if(reason == undefined){
                //     reason = "--";
                // }
                var size = list.size;
                var sizeStr = "";
                if(size<102400){
                    sizeStr = parseFloat(size/1024).toFixed(2) + 'KB';
                }else{
                    sizeStr = parseFloat(size/1048576).toFixed(2) + 'MB';
                }
                var detailStr =
                    '<div class="ty-json">'+
                    '<div class="ty-jsonHead">基本信息</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">文件编号</div>'+
                    '<div class="ty-val fileSn">'+list.fileSn+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">文件名称</div>'+
                    '<div class="ty-val fileName">'+list.name+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">原始上传人</div>'+
                    '<div class="ty-val fileName">'+list.createName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">原始上传时间</div>'+
                    '<div class="ty-val fileName">'+list.createDate.substring(0,19)+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">版本号</div>'+
                    '<div class="ty-val">G'+list.changeNum+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">当前版本上传人</div>';
                if(list.updateName==null || list.updateName==""){
                    detailStr += '<div class="ty-val fileName">'+list.createName+'</div>';
                }else{
                    detailStr += '<div class="ty-val fileName">'+list.updateName+'</div>';
                }
                detailStr += '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">当前版本上传时间</div>';
                if(list.updateDate==null || list.updateDate==""){
                    detailStr += '<div class="ty-val fileName">'+list.createDate.substring(0,19)+'</div>';
                }else{
                    detailStr += '<div class="ty-val fileName">'+list.updateDate.substring(0,19)+'</div>';
                }
                detailStr +=
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">大小</div>'+
                    '<div class="ty-val">'+sizeStr+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">类型</div>'+
                    '<div class="ty-val">'+list.version+'</div>'+
                    '</div>'+
                    '</div>';
                $(".ty-filePreview .ty-fileDetail").html("");
                $(".seeOnline").remove();
                $(".ty-filePreview .ty-fileDetail").append(detailStr);
                $(".ty-filePreview .seeOnline").attr("path",list.path);
            }

        },
        error: function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    }) ;
}

//updator:王静 2017-08-03 16:01:23 点击基本信息
/*function basicMessage(obj){
    var hFileId=obj.parent().parent().attr("id");
    $.ajax({
        url: "../res/resourceHistoryMessage.do",
        data: {"id": hFileId},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (data) {
            if (data == "" || data == undefined) {
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            } else {
                var list = data["data"];
                var operateDate = list.operateDate;
                if(operateDate == "1900-01-01 00:00:00.0"){
                    operateDate = "--";
                }
                var verifyDate = list.verifyDate;
                if(verifyDate == "1900-01-01 00:00:00.0"){
                    verifyDate = "--";
                }
                var approveDate = list.approveDate;
                if(approveDate == "1900-01-01 00:00:00.0"){
                    approveDate = "--";
                }
                var reason = list.reason;
                if(reason == undefined){
                    reason = "--";
                }
                var size = list.size;
                var sizeStr = "";
                if(size<102400){
                    sizeStr = parseFloat(size/1024).toFixed(2) + 'KB';
                }else{
                    sizeStr = parseFloat(size/1048576).toFixed(2) + 'MB';
                }
                var detailStr =
                    '<div class="ty-json">'+
                    '<div class="ty-jsonHead">基本信息</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">文件编号</div>'+
                    '<div class="ty-val fileSn">'+list.fileSn+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">文件名称</div>'+
                    '<div class="ty-val fileName">'+list.name+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">原始上传人</div>'+
                    '<div class="ty-val fileName">'+list.createName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">原始上传时间</div>'+
                    '<div class="ty-val fileName">'+list.createDate.substring(0,19)+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">版本号</div>'+
                    '<div class="ty-val">G'+list.changeNum+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">当前版本上传人</div>';
                if(list.updateName==null || list.updateName==""){
                    detailStr += '<div class="ty-val fileName">'+list.createName+'</div>';
                }else{
                    detailStr += '<div class="ty-val fileName">'+list.updateName+'</div>';
                }
                detailStr += '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">当前版本上传时间</div>';
                if(list.updateDate==null || list.updateDate==""){
                    detailStr += '<div class="ty-val fileName">'+list.createDate.substring(0,19)+'</div>';
                }else{
                    detailStr += '<div class="ty-val fileName">'+list.updateDate.substring(0,19)+'</div>';
                }
                detailStr +=
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">大小</div>'+
                    '<div class="ty-val">'+sizeStr+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">类型</div>'+
                    '<div class="ty-val">'+list.type+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">换版原因</div>'+
                    '<div class="ty-val">'+reason+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">说明</div>'+
                    '<div class="ty-val">'+list.content+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-jsonHead">制作与审批信息</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">制作人</div>'+
                    '<div class="ty-val">'+list.opertatorName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">制作时间</div>'+
                    '<div class="ty-val">'+operateDate.substring(0,19)+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">审批人</div>'+
                    '<div class="ty-val">'+list.verifierName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">审批时间</div>'+
                    '<div class="ty-val">'+verifyDate.substring(0,19)+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">批准人</div>'+
                    '<div class="ty-val">'+list.approverName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">批准时间</div>'+
                    '<div class="ty-val">'+approveDate.substring(0,19)+'</div>'+
                    '</div>'+
                    '</div>';
                $(".ty-filePreview .ty-fileDetail").html("");
                $(".seeOnline").remove();
                $(".ty-filePreview .ty-fileDetail").append(detailStr);
                $(".ty-filePreview .seeOnline").attr("path",list.path);
            }

        },
        error: function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    }) ;
}*/
// creator:王静 2017-08-03 16:11:23 点击操作按钮
function yj(obj,i,e){
    $("ul.hel").hide();
    $("#handleul"+i.toString()).show();
    $(obj).addClass("handleActive");
    e.stopPropagation();
}
$("*").not($('.cz_handle')).on("click",function(){
    $(".hel").hide();
    $(".cz_handle").removeClass("handleActive")
})
/* creator：张旭博，2017-05-09 10:37:54，解析地址栏地址得到参数值 */
/*
function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}*/
