// creater :侯杏哲 2017-03-27 处理三种请求的页面跳转
var isfromMessage = 0 ; // 标记是否来自于消息处理
$(function () {
    var curprocessID = GetUrlQuery("processID") ; // 审批过程id
    if(curprocessID != undefined){
        getDetail( {"approveID":curprocessID   }   ) ;
        isfromMessage = 1 ;
    }
    $(".ty-secondTab li").eq(0).click();
})
function ask_overtime(num) {
    var val =num;
    var url = "";
    if(Number(val) == 1 ){
        url = "../user/overtime.do"; // 加班
    }
    if(Number(val) == 2 ){
        url = "../user/leave.do"; //请假
    }
    if(Number(val) == 3 ){
        url = "../expense/chargeSubmitSale.do"; // 处理报销申请
    }
    if( url == ""){ return false; }
    location.href = url;
}
// creater :侯杏哲 2017-03-27 处理三种请求的页面跳转
function gotoTab2( obj , num){
    getList(num , 1 , 15) ;
    $('#for_n').html(obj.html()) ;
    obj.addClass('ty-active').siblings().removeClass('ty-active');
    $("#tab_15_"+num).show().siblings().hide();
}
// 获得列表
function getList( type , pageNumber , quantum ){
    $.ajax({
        url : "../lo/approvalOutTimeList.do" ,
        data:{ "approvalStatus": type , "pageNumber":pageNumber , "quantum": quantum } ,
        type:"post" ,
        dataType : "json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            var list = data["approvalProcessList"] ;
            var n = (pageNumber - 1) * quantum ;
            var str = "" ;
            if(list && list.length > 0){
                for(var i = 0 ; i < list.length ; i++){
                    var idObj = { "id": list[i]["personnelOvertime"].id , "approveID": list[i].id , "no" : ++n   } ;
                    str += "<tr>" +
                        "<td>"+ n +"</td>" +
                        "<td>"+ list[i]["personnelOvertime"].createName +"</td>" +
                        "<td>"+ list[i]["personnelOvertime"].beginTime +"</td>" +
                        "<td>"+ list[i]["personnelOvertime"].endTime +"</td>" +
                        "<td>"+ list[i]["personnelOvertime"].duration +"</td>" +
                        "<td>"+ chargeType(list[i]["personnelOvertime"]["type"]) +"</td>" +
                        "<td>" +
                        "<span class='ty-color-blue' onclick='getDetail("+  JSON.stringify( idObj) +" , $(this))'>查看</span>" +
                        // "<span class='ty-color-blue' onclick='getDetail("+ list[i]["personnelOvertime"]["id"] +", "+ n +")'>查看</span>" +
                        "</td>" +
                        "</tr>" ;
                }
                
            }
            $("#tpl" + type).html(str);
            var cur = data["pageNumber"] , total = data["totalPage"] ;
            var jsonStr = JSON.stringify({"type": type }) ;
            setPage( $("#ye" + type) , cur , total , "chargeApply_overtime" , jsonStr );
        },
        error:function(){
            $("#mt_tip_ms").html("获得列表失败，请刷新重试") ;
            bounce.show($("#mtTip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    });
}
// creator : 侯杏哲 2017-05-10  判别请假类型
function chargeType( type ){
    if( type == "1" ){  return "工作日加班";  }
    if( type == "2" ){  return "周末假日加班";  }
    if( type == "3" ){  return "法定节假日加班";  }
    return "未识别" ;
}
// creator : 侯杏哲 2017-05-10  查看详情
var editItem = {} ;
function getDetail( obj , editSppan ){
    var no =  obj["no"] ;
    var approveID = obj["approveID"] ;
    if(!approveID){ $("#mt_tip_ms").html("获得记录ID失败！") ;  bounce.show($("#mtTip")) ;     }
    editItem = obj ;
    if( editSppan ){  editItem["editTr"] = editSppan.parent().parent() ;  }
    $.ajax({
        url : "../lo/getOutTimeByApprovalProcessId.do" ,
        data:{ "approvalProcessId" : approveID  } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            var status = data["status"] ;
            if(status == 0 ){
                $("#mt_tip_ms").html("获得该条记录详情失败") ;
                bounce.show($("#mtTip")) ;
            }else{
                var processList = data["processList"] ;
                var info = data["personnelOvertime"] ;
                editItem["approveID"] = data["approvalProcessId"] ;

                bounce.show($("#detail")) ;
                var str = "" ;
                var nowUserId = JSON.parse($("#loginUser").text()).userID;
                var isAllowApprove= 0; //是否允许审批 0-允许 1-不允许
                if(processList && processList.length>0){
                    for(var i = 0 ; i < processList.length ; i++){
                        if( processList[i]["approveStatus"] == "1" ){ // 待审批
                            str += "<div class='ty-process-item ty-process-wait'>" +
                                "<p><span class='dot-wait'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                "<p>"+processList[i]["handleTime"]+"</p>" +
                                "</div>" ;
                        }else if( processList[i]["approveStatus"] == "3"){ // 已驳回
                            str += "<div class='ty-process-item ty-process-no '>" +
                                "<p><span class='dot-no'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                "<p>"+processList[i]["handleTime"]+"</p>" +
                                "</div>" ;
                        }else{  // 已批准
                            str += "<div class='ty-process-item'>" +
                                "<p><span class='dot'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                "<p>"+processList[i]["handleTime"]+"</p>" +
                                "</div>" ;
                        }
                        if(processList[i]["toUser"] == nowUserId && Number(processList[i].approveStatus) === 1){
                            isAllowApprove =1;
                        }
                    }
                }
                //判断是否拥有审批权限
                if(isAllowApprove === 1){
                    $("#detail .infoList").html(
                        '<span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="chargeBtn(0)">驳回</span>'+
                        '<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chargeBtn(1)">批准</span>'
                    );
                }else{
                    $("#detail .infoList").html("");
                }
                $("#process").html( str ) ;
                if(info){
                    editItem["id"] = info["id"] ;
                    $("#no").html( no ) ;
                    $("#createName").html(info["createName"]) ;
                    $("#createDate").html(info["createDate"]) ;
                    $("#beginTime").html(info["beginTime"]) ;
                    $("#endTime").html(info["endTime"]) ;
                    $("#type").html( chargeType(info["type"]) ) ;
                    $("#during").html(info["duration"] + "小时") ;
                    $("#memo").html(info["reason"]) ;

                }else{
                    $("#mt_tip_ms").html("获得信息失败，请刷新重试") ;
                    bounce.show($("#mtTip")) ;
                }
            }
        } ,
        error:function(){
            $("#mt_tip_ms").html("网络连接错误，请刷新重试") ;
            bounce.show($("#mtTip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }

    });


}
// creator : 侯杏哲 2017-05-10  审批请假申请按钮
function chargeBtn( type ){ // type : 0 - 驳回 ； 1 - 批准
    bounce.show( $("#confirm") ) ;
    editItem["type"] = type ;
    if(type == 1){
        $("#confirm").attr("class" , "bonceContainer bounce-green") ;
        $("#confirmCtrl").attr("class" , "ty-btn ty-btn-green ty-btn-big ty-circle-5").html("批准") ;
        $("#confirm_ms").html("是否批准此条请求？") ;
        $("#confirmTtl").html("批准") ;
    }else{
        $("#confirm").attr("class" , "bonceContainer bounce-red") ;
        $("#confirmCtrl").attr("class" , "ty-btn ty-btn-red ty-btn-big ty-circle-5").html("驳回") ;
        $("#confirm_ms").html("是否驳回此条请求？") ;
        $("#confirmTtl").html("驳回") ;
    }
}
// creator : 侯杏哲 2017-05-10  确定审批 / 驳回
function chargeApply(){
    if( !editItem ){ $("#mt_tip_ms").html("操作失败，请刷新重试") ;  bounce.show($("#mtTip")) ;  return false ;  }
    $.ajax({
        url : "../lo/progressiveApprovalOutTime.do" ,
        data:{ "outTimeId":editItem["id"]  , "approvalProcessId": editItem["approveID"]  , "approvalStatus": editItem["type"]  } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            var status = data["status"] ;
            if(status == 1){
                $("#mt_tip_ms").html("操作成功") ;
                bounce.show($("#mtTip")) ;
                if(editItem["editTr"] != undefined){ editItem["editTr"].remove() ; }
            }else if(status == 0){
                $("#mt_tip_ms").html("操作失败") ;
                bounce.show($("#mtTip")) ;
            }
        } ,
        error:function(){
            $("#mt_tip_ms").html("操作失败") ;
            bounce.show($("#mtTip")) ;
        } ,
        complete:function(){
            loading.close() ;
            if( isfromMessage == 1 ){  isfromMessage = 0 ; msgNum(); $(".ty-secondTab li").eq(0).click();   }
        }
    }) ;
}


