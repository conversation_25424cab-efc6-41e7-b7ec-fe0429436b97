/**
 * Created by rose on 2017/1/5.
 */
var isFromMessage = 0 ; // 标识是不是从消息跳转过来的 ： 0 不是 ； 1 是
$(function(){
    // creator：侯杏哲，2017-05-20 09:29:31，处理从消息跳转过来的页面
    var applyId = GetUrlQuery("applyID") ; // 审批过程id
    var approvalStatus = GetUrlQuery("approvalStatus") ; // 审批过程id
    if( applyId !== undefined ){
        switch (Number(approvalStatus)){
            case 1://待处理
            case 4://待两讫
            case 6://待接收
                $(".ty-secondTab li").eq(0).click();
                break;
            case 2://已批准
            case 5://已报销
            case 7://已接收
                $(".ty-secondTab li").eq(1).click();
                break;
            case 3://已驳回
            case 8://财务驳回
                $(".ty-secondTab li").eq(2).click();
                break;
            default:
                $(".ty-secondTab li").eq(0).click();
        }
        deal_see( applyId) ;
        isFromMessage = 1 ;
    }else{
        // 进入页面， 获取列表
        getApplicationList( 1 , 1 , 15 );
    }

   /* $(".ty-firstTab li").on("click",function(){
        var index = $(this).index();
        switch ( index ){
            case 0:
                location.href = "myOvertime.do"; // 加班
                break;
            case 1:
                location.href = "myLeave.do"; //请假
                break;
            case 2:
                location.href = "myApplication.do"; // 财务
                break;
        }
    });*/

    $(".ty-secondTab li").on("click",function(){
        $(this).addClass("ty-active").siblings().removeClass("ty-active");
        var index = $(this).index()+1;
        $('#for_n').html($(this).html()) ;
        $("#tab_15_"+index).show().siblings("table").hide();
        getApplicationList(index , 1 , 15 ) ;
    });


    //票据类型的切换事件
    $("#billCat").on("change",function () {
        $("#goodEntryTbl").hide();
        $("#goodEntryTbl tbody").html("");
        $("#accountAll .amountAll").html("");
        $("#accountAll .taxAll").html("");
        $("#goodEntryBtn").html("货物录入");
        $(".base input").val("");
        $(".base #billDate").val(1);
        $(".ty-form-checkbox").removeClass("ty-form-checked");
    });
    $(".ty-form-checkbox").on("click",function () {
        $(".ty-form-checkbox").removeClass("ty-form-checked");
        if(!$(this).hasClass("ty-form-checked")){
            $(this).addClass("ty-form-checked");
            $("#goodEntryTbl").hide();
            $("#goodEntryTbl tbody").html("");
            $("#accountAll .amountAll").html("");
            $("#accountAll .taxAll").html("");
            $("#goodEntryBtn").html("货物录入");
            $(".base input").val("");
            $(".base #billDate").val(1);
        }

    });

    $("#goodEntryBtn").on("click",function () {
        goodEntryBtn();
    });

    $('#ImportPicInput').on('change',function(){
        var fileList = document.getElementById("ImportPicInput").files;
        var fileNameStr = '';
        for(var i=0 ;i<fileList.length;i++){
            if(i === fileList.length - 1){
                fileNameStr += fileList[i].name;
            }else{
                fileNameStr += fileList[i].name + "，";
            }
        }
        $( "#importPicName").val(fileNameStr);
    })
});

/* updater：张旭博，2017-12-20 09:36:00，获取申请列表(待处理、已批准、已驳回三个部分) */
function getApplicationList( approveStatus , cur , per ){
    $.ajax({
        url:"../expense/toOnePersonnelReimburseMain.do",
        data:{
            "approveStatus": approveStatus, //1-带处理 2-已批准 3-已驳回
            "currPage":cur ,
            "pageSize": per
        },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function(data){
            var str = "" ;
            var cur = data["currPage"] ;
            var sumPage = data["totalPage"];
            var list = data["personnelReimbursesList"];
            var jsonStr = JSON.stringify( { "num" : approveStatus } );
            $("#page").html("");
            setPage( $("#page") , cur , sumPage , "myApply" , jsonStr  );
            if(list && list.length > 0){
                for( var i = 0; i < list.length ; i++ ){
                    var createDate = list[i]["createDate"].substr(0,10);//创建时间
                    var amount = list[i]["amount"];//实际金额
                    var billAmount = list[i]["billAmount"];//发票金额
                    var feeCatName = list[i]["feeCatName"];//费用类别
                    var billCatName = list[i]["billCatName"];//票据种类
                    str +="<tr>" +
                        "<td>"+ (i+1) +"</td>" +
                        "<td>"+ createDate +"</td>" +
                        "<td>"+ amount +"</td>" +
                        "<td>"+ billAmount +"</td>" +
                        "<td>"+ feeCatName +"</td>" +
                        "<td>"+ billCatName +"</td>" +
                        "<td>" +
                        "<span class='ty-color-blue' style='cursor:pointer;' onclick='deal_see("+  list[i]["id"] +")'>查看</span>" +
                        // "<span class='hd' >"+ JSON.stringify( list[i] ) +"</span>" +
                        "</td>" +
                        "</tr>"
                }
            }
            $("#tab_15_"+approveStatus+" tbody").html(str);
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    });
}

/* updater：张旭博，2017-12-20 09:36:31，点击查看详情 */
function deal_see(applyId){
    // $("#Details").show().siblings().hide();//显示详情处理页面
    bounce.show($("#contactDetail"));
    $(".bounce_Fixed").attr("onclick" , "imgOut()") ;
    $.ajax({
        url:"../expense/toOnePersonnelReimburseById.do",
        data:{  "id": applyId  },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            // 显示详情
            var applyInfo = data["personnelReimburse"] ;
            var status = applyInfo["approveStatus"];
            if(Number(status) == 1){
                $("#applyStatus").attr("class" , "dealSee_continer applyYes").children(".ttl").html("待处理") ;
                $("#info_summary").parent().hide();
            }else if( Number(status) == 2 ){
                $("#applyStatus").attr("class" , "dealSee_continer applyYes").children(".ttl").html("已批准") ;
                $("#info_summary").parent().show();
            }else if( Number(status) == 3 ){
                $("#info_summary").parent().hide();
                $("#applyStatus").attr("class" , "dealSee_continer applyNo").children(".ttl").html("已驳回") ;
            }
            $("#info_creator").html( applyInfo["createName"] ) ;
            $("#info_createDate").html( applyInfo["createDate"] ) ;
            $("#info_feeCatName").html( applyInfo["feeCatName"] ) ;
            $("#info_billCatName").html( applyInfo["billCatName"] ) ;
            $("#info_billDate").html( chargeBillDate( applyInfo["billDate"] ) ) ;
            $("#info_summary").html( applyInfo["summary"] ) ;
            $("#info_purpose").html( applyInfo["purpose"] ) ;
            $("#info_billQuantity").html( applyInfo["billQuantity"] ) ;
            $("#info_amount").html( applyInfo["amount"] ) ;
            $("#info_billAmount").html( applyInfo["billAmount"] ) ;
            $("#info_memo").html( applyInfo["memo"] ) ;
            // 展示审批流程
            var approvalProcess = data["approvalProcess"] ;
            var str2 = "" ;
            if( approvalProcess && approvalProcess.length > 0  ){
                for(var i = 0; i < approvalProcess.length ; i++ ) {
                    var prostatus = approvalProcess[i]["approveStatus"] ;
                    switch( Number(prostatus) ){
                        case 1 :  // 待处理
                        case 4 :  // 待两讫
                        case 6 :  // 待接收
                            str2 += '<div class="infoList ty-process-item">' +
                                '    <p><span class="dot-wait"></span>处理人：'+ approvalProcess[i]["toUserName"] + "/ " + approvalProcess[i]["userName"] + ' </p>' +
                                '    <p>' + approvalProcess[i]["handleTime"] + '</p>' +
                                '</div>';
                            break ;
                        case 2 :  // 已批准
                        case 5 :  // 已报销
                        case 7 :  // 已接收
                            str2 += '<div class="infoList ty-process-item">' +
                                '    <p><span class="dot"></span>处理人：'+ approvalProcess[i]["toUserName"] + "/ " + approvalProcess[i]["userName"] + ' </p>' +
                                '    <p>' + approvalProcess[i]["handleTime"] + '</p>' +
                                '</div>';
                            break ;
                        case 3 :  // 已驳回
                        case 8 :  // 财务驳回
                            str2 += '<div class="infoList ty-process-item">' +
                                '    <p><span class="dot-no"></span>处理人：'+ approvalProcess[i]["toUserName"] + "/ " + approvalProcess[i]["userName"] + ' </p>' +
                                '    <p>' + approvalProcess[i]["handleTime"] + '</p>' +
                                '</div>';
                            break ;
                        default:
                            break;
                    }
                }
            }
            $("#contactDetail #process").html(str2);
            // 展示图片
            var imgs = data["personnelReimbursetAttachment"] ;
            if( imgs && imgs.length > 0 ){
                var str = "" ;
                for(var j = 0 ; j < imgs.length ; j++ ){
                    var path = imgs[j]["path"] ;
                    if(path !== ""){
                        str += "<a href='../" + path+ "' target='_blank'><img src='../" + path+ "'/></a>" ;
                    }
                }
                $("#info_img").html( str );
            }
        },
        error:function(){  } ,
        complete:function(){ chargeClose(1) ;  }
    });
}

/* creator：张旭博，2017-12-13 14:37:56，我的请求-》报销申请-按钮 */
function expenseApplyBtn(){
    $(".bounce_Fixed").removeAttr("onclick") ;
    getFeeCats();
    getBillCats();
    $("#chargeGood").hide();
    $("#goodEntryBtn").hide();
    $("#goodEntryTbl").hide();
    $(".base").hide();
    bounce.everyTime('0.5s','expenseApply',function(){
        console.log("bounce.everyTime expenseApply Time:"+new Date().toLocaleTimeString())
        var billCat = $("#billCat").find("option:selected").html();
        switch (billCat){
            case "增值税专用发票":
                $("#chargeGood").hide();
                $("#goodEntryBtn").show();
                $("#goodEntryTbl").hide();
                $(".base").hide();
                if($("#goodEntryBtn").html() === "请录入下一种货物"){
                    $("#goodEntryTbl").show();
                    $(".base").show();
                }
                $("#billQuantity").prop("disabled",true);
                $("#billQuantity").val(1);
                $("#billDate").prop("disabled",true);
                break;
            case "增值税普通发票":
            case "其他普通发票":
                var isManyGood = $(".ty-form-checked").attr("value");   //是否包含多项货物 1-是 0-否
                var goodNum = $("#goodEntryTbl tbody tr").length;       //录入的货物数量
                if(isManyGood === "1"&&goodNum>1){
                    $("#billQuantity").prop("disabled",true);
                    $("#billQuantity").val(1);
                }else{
                    $("#billQuantity").prop("disabled",false);
                }
                $("#chargeGood").show();
                $("#goodEntryBtn").hide();
                $("#goodEntryTbl").hide();
                $(".base").hide();
                if($("#chargeGood .ty-form-checked").attr("value") === "1"){
                    $("#goodEntryBtn").show();
                    $("#billDate").prop("disabled",true)
                    if($("#goodEntryBtn").html() === "请录入下一种货物"){
                        $("#goodEntryTbl").show();
                        $(".base").show();
                    }
                }else if($("#chargeGood .ty-form-checked").attr("value") === "0"){
                    $(".base").show();
                    $("#billDate").prop("disabled",false)
                }

                break;
            case "收据":
                $("#chargeGood").hide();
                $("#goodEntryBtn").hide();
                $("#goodEntryTbl").hide();
                $(".base").show();
                $("#billQuantity").prop("disabled",false);
                $("#billDate").prop("disabled",false)
                break;
            default:
                $("#chargeGood").hide();
                $("#goodEntryBtn").hide();
                $("#goodEntryTbl").hide();
                $(".base").hide();
        }
        var feeCat = $("#feeCat").val();
        var billCatId = $("#billCat").val();
        var billDate = $("#billDate").val();
        var purpose = $("#purpose").val();
        var billQuantity = $("#billQuantity").val();
        var amount = $("#amount").val();
        var billAmount = $("#billAmount").val();
        if(billAmount !== ""){billAmount = parseFloat(billAmount);}

        var state = 0;

        var amountAll = 0;
        var taxAll = 0;
        var taxTotalAll = 0;
        $("#goodEntryTbl #goodList tr").each(function () {
            var goodAmount = $(this).children().eq(5).html();
            var tax = $(this).children().eq(7).html();
            amountAll += parseFloat(goodAmount);
            taxAll += parseFloat(tax);
        });
        taxTotalAll = -(-amountAll-taxAll);
        amountAll = parseFloat(amountAll.toFixed(2));
        taxAll = parseFloat(taxAll.toFixed(2));
        taxTotalAll = Number(taxTotalAll.toFixed(2));

        $("#accountAll .amountAll").html(amountAll);
        $("#accountAll .taxAll").html(taxAll);
        $("#accountAll .taxTotalAll").html(taxTotalAll);
        //如果必填输入框都不为空

        var isManyGood = $(".ty-form-checked").attr("value");   //是否包含多项货物 1-是 0-否
        switch (billCat){
            case "增值税专用发票":
                if(billAmount === taxTotalAll){
                    state = 1;
                }else{
                    state = 0;
                }
                break;
            case "增值税普通发票":
                if(isManyGood === "1"){
                    if(billAmount === taxTotalAll){
                        state = 1;
                    }else{
                        state = 0;
                    }
                }else{
                    state = 1;
                }
                break;
            case "其他普通发票":
                if(isManyGood === "1"){
                    if(billAmount === amountAll){
                        state = 1;
                    }else{
                        state = 0;
                    }
                }else{
                    state = 1;
                }
                break;
            case "收据":
                state = 1;
                break;
        }
        if(state === 1
            && feeCat       !== ""
            && billCatId    !== ""
            && billDate     !== ""
            && purpose      !== ""
            && billQuantity !== ""
            && amount       !== ""
            && billAmount   !== ""){
            $("#expenseApplyBtn").prop("disabled",false);
        }else{
            $("#expenseApplyBtn").prop("disabled",true);
        }

    });
    bounce.show($("#expenseApply"));
}

/* creator：张旭博，2018-03-03 13:29:45，计算金额合计 */
function figureOut() {
    var amountAll = 0;
    var taxAll = 0;
    var taxTotalAll = 0;
    $("#goodEntryTbl #goodList tr").each(function () {
        var amount = $(this).children().eq(5).html();
        var tax = $(this).children().eq(7).html();
        amountAll += Number(amount);
        taxAll += Number(tax);
    });
    taxTotalAll = -(-amountAll-taxAll);
    $("#accountAll .amountAll").html(amountAll);
    $("#accountAll .taxAll").html(taxAll);
    $("#accountAll .taxTotalAll").html(taxTotalAll);
    return taxTotalAll;
}


/* creator：张旭博，2017-12-14 16:24:47，报销申请-确认 */
function sureExpenseApply() {
    loading.open() ;
    $('body').stopTime('expenseApply');
    $("#expenseApplyBtn").prop("disabled",true);
    var feeCat=$("#feeCat").val();//费用类别
    var billCat=$("#billCat").val();//票据种类
    var billCatName=$("#billCat").find("option:selected").html();//票据种类
    var billAmount = $("#billAmount").val();
    var billDate=$("#billDate").val();//票据所属月份
    var purpose=$("#purpose").val();//用途
    var billQuantity=parseInt($("#billQuantity").val());//票据数量
    var allAmount= $("#amount").val();//合计金额（填数字）
    var memo= $("#memo").val();

    var amountAll = $("#accountAll .amountAll").html();
    var taxAll = $("#accountAll .taxAll").html();
    var issueDate = $("#expenseApply .billingDate").html();

    var billItems = [];
    var goodList = $("#goodEntryTbl tbody tr");
    if(goodList.length>0){
        for(var i=0;i<goodList.length;i++){

            var itemName    = goodList.eq(i).children().eq(0).html();     //货物名称
            var model       = goodList.eq(i).children().eq(1).html();     //规格型号
            var unit        = goodList.eq(i).children().eq(2).html();     //单位
            var itemQuantity= goodList.eq(i).children().eq(3).html();     //数量
            var uniPrice    = goodList.eq(i).children().eq(4).html();     //单价
            var price       = goodList.eq(i).children().eq(5).html();     //金额
            var taxRate     = goodList.eq(i).children().eq(6).attr("value");     //税率
            var tax         = goodList.eq(i).children().eq(7).html();     //税额
            var taxTotal    = goodList.eq(i).children().eq(8).html();     //含税合计
            if(billCatName === "其他普通发票"){
                billItems.push({
                    "itemName":itemName,        //货物名称
                    "model":model,              //规格型号
                    "unit":unit,                //单位
                    "itemQuantity":itemQuantity,//数量
                    "uniPrice":uniPrice,        //单价
                    "price":price,              //金额
                    "taxRate":"0",          //税率
                    "taxAmount":"0",            //税额
                    "amount":"0"           //含税合计
                })
            }else{
                billItems.push({
                    "itemName":itemName,        //货物名称
                    "model":model,              //规格型号
                    "unit":unit,                //单位
                    "itemQuantity":itemQuantity,//数量
                    "uniPrice":uniPrice,        //单价
                    "price":price,              //金额
                    "taxRate":taxRate,                //税率
                    "taxAmount":tax,              //税额
                    "amount":taxTotal                  //含税合计
                })
            }
        }
    }

    var reimburse = {
        "feeCat":feeCat,                //费用类别（字典表id）
        "billCat":billCat,              //票据类型（字典表id）
        "billAmount":billAmount,        //票面金额
        "purpose":purpose,              //用途
        "billQuantity":billQuantity,    //票据数量
        "billDate":billDate,            //发票所属月份(1-本月票据,2-非本月票据)
        "amount":allAmount,             //合计金额
        "memo":memo                     //备注
    };
    var bill = {
        "price":amountAll,              //税前金额
        "taxAmount":taxAll,             //税额
        "issueDate":issueDate           //开票日期
    };
    if(billCatName === "其他普通发票"){
        bill.taxAmount = "0";
    }
    var data = {
        'reimburse':JSON.stringify(reimburse),
        'bill':JSON.stringify(bill),
        'billItems':JSON.stringify(billItems)
    };
    $.ajax({
        url:"../expense/addReimburse.do",
        data:data,
        type:"post",
        dataType:"json",
        async:false,
        beforeSend:function(){ loading.open() },
        success:function(data){
            var status = data["status"];
            var personnelReimburseId = data["personnelReimburseId"];
            if(status === 1){
                if($.trim($("#importPicName").val()) !== ""){
                    $.ajaxFileUpload({
                        type: "POST",
                        url: "../expense/uploadImages.do",
                        data:{"personnelReimburseId":personnelReimburseId},
                        secureuri : false,//是否启用安全提交，默认为false
                        fileElementId:'ImportPicInput',//文件选择框的id属性
                        dataType: 'JSON',//服务器返回的格式
                        async : false,
                        success: function(data){
                            bounce.cancel();
                            loading.close();
                            layer.msg("申请成功！");
                            $(".ty-secondTab li").eq(0).click();
                            $('#ImportPicInput').unbind("change");
                            $('#ImportPicInput').on('change',function(){
                                var fileList = document.getElementById("ImportPicInput").files;
                                var fileNameStr = '';
                                for(var i=0 ;i<fileList.length;i++){
                                    if(i === fileList.length - 1){
                                        fileNameStr += fileList[i].name;
                                    }else{
                                        fileNameStr += fileList[i].name + "，";
                                    }
                                }
                                $( "#importPicName").val(fileNameStr);
                            })
                        },
                        error: function (data, status, e){
                            $("#errorTip .tipWord").html("附件上传失败！") ;
                            bounce.show($("#errorTip")) ;
                        }
                    });
                }else{
                    bounce.cancel();
                    loading.close();
                    layer.msg("申请成功！");
                    $(".ty-secondTab li").eq(0).click();
                    $('#ImportPicInput').unbind("change");
                    $('#ImportPicInput').on('change',function(){
                        var fileList = document.getElementById("ImportPicInput").files;
                        var fileNameStr = '';
                        for(var i=0 ;i<fileList.length;i++){
                            if(i === fileList.length - 1){
                                fileNameStr += fileList[i].name;
                            }else{
                                fileNameStr += fileList[i].name + "，";
                            }
                        }
                        $( "#importPicName").val(fileNameStr);
                    })
                }
            }else{
                $("#errorTip .tipWord").html("申请失败！") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("链接错误，请刷新重试！") ;
            bounce.show($("#errorTip")) ;
        },
        complete:function () {

        }
    });
}

/* creator：张旭博，2017-12-11 14:30:36，报销申请-》（票据种类：增值税专用发票）货物录入 */
function goodEntryBtn() {
    var billCat = $("#billCat").find("option:selected").html();
    if(billCat === "其他普通发票"){
        $(".taxInput").hide();
    }else{
        $(".taxInput").show();
    }
    bounce_Fixed.show($("#goodEntry"));
    $("#goodID").val("");
    $("#goodEntry input").not(".billingDate").val("");
    //打开货物录入定时器
    goodEntryTimer();
}

/* creator：张旭博，2017-12-11 14:30:36，报销申请-》货物录入-确定 */
function sureAddGood() {
    var billingDate         = $("#goodEntry .billingDate").val();
    var goodName            = $("#goodEntry .goodName").val();
    var specificationType   = $("#goodEntry .specificationType").val();
    var unit                = $("#goodEntry .unit").val();
    var goodNum             = $("#goodEntry .goodNum").val();
    var price               = $("#goodEntry .price").val();
    var amount              = $("#goodEntry .amount").val();
    var taxRatePercent      = $("#goodEntry .taxRate").val();
    var taxRate             = $("#goodEntry .taxRate").attr("value");
    var tax                 = $("#goodEntry .tax").val();
    var taxTotal            = $("#goodEntry .taxTotal").val();
    var lastNode            = $("#goodEntryTbl tbody tr").length;
    var index               = 1;
    var currentGoodId       = $("#goodID").val();
    var billCat             = $("#billCat").find("option:selected").html();
    var goodStr             = '';
    if(lastNode !== 0){
        index = $("#goodEntryTbl tbody tr:last").attr("id").charAt(4);
        index ++;
    }
    //其他普通发票和其他发票货物列表的区分
    if(billCat === "其他普通发票"){
        goodStr =   '<td>' + goodName + '</td>' +
                    '<td>' + specificationType + '</td>' +
                    '<td>' + unit + '</td>' +
                    '<td>' + goodNum + '</td>' +
                    '<td>' + price + '</td>' +
                    '<td>' + amount + '</td>' +
                    '<td>' +
                    '<span class="ty-color-blue" onclick="changeGoodBtn($(this))">修改</span>' +
                    '<span class="ty-color-red" onclick="deleteGoodBtn($(this))">删除</span>' +
                    '</td>';
        $(".recordHeader #taxAll").hide();
    }else{
        goodStr =   '<td>' + goodName + '</td>' +
                    '<td>' + specificationType + '</td>' +
                    '<td>' + unit + '</td>' +
                    '<td>' + goodNum + '</td>' +
                    '<td>' + price + '</td>' +
                    '<td>' + amount + '</td>' +
                    '<td value="'+taxRate+'">' + taxRatePercent + '</td>' +
                    '<td>' + tax + '</td>' +
                    '<td>' + taxTotal + '</td>' +
                    '<td>' +
                    '<span class="ty-color-blue" onclick="changeGoodBtn($(this))">修改</span>' +
                    '<span class="ty-color-red" onclick="deleteGoodBtn($(this))">删除</span>' +
                    '</td>';
        $(".recordHeader #taxAll").show();
    }
    //判断是新增还是修改
    if(currentGoodId === ""){
        $("#goodEntryTbl tbody").append('<tr id="good'+index+'">'+goodStr+'</tr>');
        $("#goodEntryBtn").html("请录入下一种货物");
    }else{
        $("#"+currentGoodId).html(goodStr);
    }
    //更新开票日期
    $("#goodEntryTbl .billingDate").html(billingDate);
    //根据录入的开票日期判断为本月票据还是非本月票据
    var nowDay = getCurDate();
    var nowMonth = nowDay.substring(0,7);
    var billingMonth = billingDate.substring(0,7);
    if(nowMonth === billingMonth){
        $("#billDate").val(1);
    }else{
        $("#billDate").val(2)
    }
    bounceFix_cancel();
}

/* creator：张旭博，2017-12-11 17:03:39，报销申请-》发票信息-修改-按钮 */
function changeGoodBtn(selector) {
    bounce_Fixed.show($("#goodEntry"));
    $("#goodID").val(selector.parents("tr").attr("id"));
    var itemName    = selector.parents("tr").children().eq(0).html();     //货物名称
    var model       = selector.parents("tr").children().eq(1).html();     //规格型号
    var unit        = selector.parents("tr").children().eq(2).html();     //单位
    var itemQuantity= selector.parents("tr").children().eq(3).html();     //数量
    var uniPrice    = selector.parents("tr").children().eq(4).html();     //单价
    var price       = selector.parents("tr").children().eq(5).html();     //金额
    var taxRate     = selector.parents("tr").children().eq(6).html()      //税额
    var taxAmount   = selector.parents("tr").children().eq(7).html()      //税额
    var amount      = selector.parents("tr").children().eq(8).html()      //含税合计
    $("#goodEntry .billingDate").val("");
    $("#goodEntry .goodName").val(itemName);
    $("#goodEntry .specificationType").val(model);
    $("#goodEntry .unit").val(unit);
    $("#goodEntry .goodNum").val(itemQuantity);
    $("#goodEntry .price").val(uniPrice);
    $("#goodEntry .amount").val(price);
    $("#goodEntry .taxRate").val(taxRate);
    $("#goodEntry .tax").val(taxAmount);
    $("#goodEntry .taxTotal").val(amount);
    $("#goodEntry .billingDate").val($("#goodEntryTbl .billingDate").html());
    figureOut();
    goodEntryTimer();
}

/* creator：张旭博，2017-12-11 17:03:39，报销申请-》发票信息-删除-按钮 */
function deleteGoodBtn(selector) {
    $("#deleteGood .tipWord").html("您确定要删除此条货物信息吗？");
    var goodId = selector.parent().parent().attr("id");
    $("#deleteGood .bonceCon").attr("gid",goodId);
    bounce_Fixed.show($("#deleteGood"));
}

/* creator：张旭博，2018-03-03 15:45:05，报销申请-》发票信息-删除-确定 */
function sureDeleteGood() {
    var goodId = $("#deleteGood .bonceCon").attr("gid");
    $("#"+goodId).remove();
    if($("#goodEntryTbl tbody").html() === ""){
        $("#goodEntryBtn").html("货物录入");
    }
    $(".base input").val("");
    $(".base #billDate").val(1);
    bounce_Fixed.cancel();
}

/* creator：张旭博，2017-12-11 15:59:29，查询全部费用类别 */
function getFeeCats() {
    $.ajax({
        url:"../expense/querySysCode.do",
        data:{},
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function(data){
            if(typeof(data.feeCats)==="undefined"){
                return false;
            }else{
                var str='<option value="">------请选择费用类别------</option>';
                for(var i=0;i<data.feeCats.length;i++){
                    str+='<option value='+data.feeCats[i].id+'>'+data.feeCats[i].name+'</option>'
                }
                $("#feeCat").html(str);
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    });
}

/* creator：张旭博，2017-12-11 15:59:17，查询全部票据种类 */
function getBillCats() {
    $.ajax({
        url:"../expense/queryCodeCategory.do",
        data:{},
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
        success:function(data){
            if(typeof(data.billCats)==="undefined"){
                return false;
            }else{
                var str='<option value="">------请选择票据种类------</option>';
                for(var i=0;i<data.billCats.length;i++){
                    str+='<option value='+data.billCats[i].id+'>'+data.billCats[i].name+'</option>'
                }
                $("#billCat").html("").append(str);
            }

        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ chargeClose(2) ;  }
    });
}

/* creator：张旭博，2017-12-11 17:05:19，一级弹窗-取消操作 */
function bounce_cancel() {
    bounce.cancel();
    // $('body').stopTime ('expenseApply');
}

/* creator：张旭博，2017-12-11 17:05:19，二级弹窗-取消操作 */
function bounceFix_cancel() {
    bounce_Fixed.cancel();
    // $('body').stopTime ('goodEntry');
}

// creator: 张旭博，2018-07-12 12:28:08，新增商品时的定时器
function goodEntryTimer() {
    bounce_Fixed.everyTime('0.5s','goodEntry',function(){
        console.log("bounce_Fixed.everyTime goodEntry Time:"+new Date().toLocaleTimeString())
        var goodNum             = $("#goodEntry .goodNum").val();
        var price               = $("#goodEntry .price").val();
        var amount              = $("#goodEntry .amount").val();
        var tax                 = $("#goodEntry .tax").val();
        var billingDate         = $("#goodEntry .billingDate").val();
        var goodName            = $("#goodEntry .goodName").val();
        var billCat             = $("#billCat").find("option:selected").html();
        if(goodNum!==''&& amount!== ''){
            var price = amount/goodNum;
            $("#goodEntry .price").val(price.toFixed(2));
            if(tax !== ''){
                var taxRate = tax/amount,
                    taxTotal = -(-amount-tax);
                if(taxRate){
                    var taxRatePercent = parseInt( taxRate * 100 )  + '%'
                }
                $("#goodEntry .taxRate").val(taxRatePercent);
                $("#goodEntry .taxRate").attr("value",taxRate.toFixed(2));
                $("#goodEntry .taxTotal").val(taxTotal.toFixed(2));
            }
        }
        if(billCat === "其他普通发票"){
            if(billingDate!==''&& goodName!==''&& goodNum!==''&& amount>0){
                $("#addGoodBtn").prop("disabled",false);
            }else{
                $("#addGoodBtn").prop("disabled",true);
            }
        }else{
            if(billingDate!==''&& goodName!==''&& goodNum!==''&& amount>0&& tax > 0){
                $("#addGoodBtn").prop("disabled",false);
            }else{
                $("#addGoodBtn").prop("disabled",true);
            }
        }

    });
}


/* updater：张旭博，2017-12-20 09:37:22，判别票据月份 */
function chargeBillDate( val ){
    if( Number(val) == 1 ){ return "本月票据" ;  }
    if( Number(val) == 2 ){ return "非本月票据" ;  }
    return "" ;
}

// 鼠标进入图片
function imgEnter(obj) {
    var path = obj.attr("src") ;
    $("#bigImag").attr("src" , path);
    bounce_Fixed.show($("#bigImgBnc")) ;
    var orl_width = obj.width() ;
    var orl_height = obj.height() ;
    var initWidth = 900 ;
    var initHeight = orl_height * initWidth / orl_width ;
    var screenHeight = $(window).height() ;
    if(screenHeight < initHeight){
        initHeight = screenHeight ;
        initWidth = initHeight * orl_width / orl_height ;
    }
    $("#bigImag").css({ "width": initWidth + "px" , "height":"auto"   }) ;
    var h =  $("#bigImag").height();
    $("#bigImgBnc").css({  "transform" : "rotate(0deg)", "-webkit-transform" : "rotate(0deg)" , "-moz-transform": "rotate(0deg)", "width":initWidth +"px" , "height": h +"px" , "margin":"0" }) ;
    var le = ($(window).width() - 400) / 2 ;
    $("#imgController").css({ "left" : le +"px" , "display":"block"  }) ;
    setCenter($("#bigImgBnc")) ;
    isLI = false ;
}
// 鼠标划过大图
function bigImagOver() {
    setTimeout(function(){ $("#bigImagcon p").show() ; } , 200) ;
}
// 鼠标划出大图
function bigImagOut() {
    setTimeout(function(){ $("#bigImagcon p").hide() ; } , 200) ;
}
// 鼠标离开图片
function imgOut(obj) {
    $("#bigImagcon").hide();
}
// 旋转图片
function rotateImg() {
    var deg = "90deg" ;
    var degStr = $("#bigImag").attr("style") ;
    if(degStr){
        if(degStr.indexOf("90") != -1){
            deg = "180deg" ;
        }else if(degStr.indexOf("180") != -1){
            deg = "270deg" ;
        }else if(degStr.indexOf("270") != -1){
            deg = "0deg" ;
        }
    }
    $("#bigImag").css({   "transform" : "rotate("+ deg +")" ,   "-webkit-transform" : "rotate("+ deg +")" ,  "-moz-transform": "rotate("+ deg +")" }) ;
    // console.log( deg ) ;
}

// 时间控件初始化
laydate.render({elem: '#billingDate'});




