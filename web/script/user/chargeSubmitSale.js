var isfromMessage = 0 ; // 标识是不是从消息跳转过来的 ： 0 不是 ； 1 是
$(function(){
    // creator： 侯杏哲 2017-05-20  处理从消息跳转过来的页面
    var processID = GetUrlQuery("processID") ; // 审批过程id
    if( processID != undefined ){
        deal_see( null ,  processID ) ; //approveStatus==1
        isfromMessage = 1 ;
    }
    // 进入页面， 获取列表
    pending( 1 , 1 , 15 ) ;
    
});
//获取待处理的数据
function pending( num , cur , per , obj){
    if(obj){
        $('#for_n').html(obj.html()) ;
        obj.addClass("ty-active").siblings().removeClass("ty-active");
    }
    $('#for_n').attr("onclick" , "pending( "+ num +" , "+ cur +" , 15 )");
    $("#tab_15_"+num).show().siblings().hide();
    $.ajax({
        url:"handlingClaims.do",
        data:{"approvalStatus":num , "pageNumber": cur , "quantum" : per  },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function(data){
            var approvalProcess = data["approvalProcess"] ;
            var pageNumber = data["pageNumber"] ;
            var quantum = data["quantum"] ;
            var totalPage = data["totalPage"] ;
            $("#ye").html("");
            var jsonStr = JSON.stringify( {"num": num } ) ;  
            setPage($("#ye") , pageNumber , totalPage , "chargeSubmitSale" , jsonStr);
            var str="";
            if( approvalProcess && approvalProcess.length > 0){
                for( i = 0 ; i < approvalProcess.length ; i++ ){
                    var info = approvalProcess[i]["reimburse"];
                    if ( info ) {
                        var createName=info["createName"];//申请人
                        var createDate=info["createDate"];//创建时间
                        var feeCatName=info["feeCatName"];//费用类别
                        var billCatName=info["billCatName"];//票据种类
                        var billDate="本月票据";//票据所属月份
                        if(Number(info["billDate"]) == 2 ){  billDate="非本月票据";       }
                        var purpose=info["purpose"];//用途
                        var billQuantity=info["billQuantity"];//票据数量 
                        var amount=info["amount"];//实际金额 
                        var billAmount=info["billAmount"];//实际金额
                        var memo=info["memo"];//备注
                        str += "<tr id='"+info["id"]+"'>" +
                            "<td>"+ (i+1) +"</td>" +
                            "<td>"+createName+"</td>" +
                            "<td>"+createDate+"</td>" +
                            "<td>"+amount+"</td>" +
                            "<td>"+billAmount+"</td>" +
                            "<td>"+feeCatName+"</td>" +
                            "<td>"+billCatName+"</td>" +
                            "<td>" +
                                "<span class='ty-color-blue' onclick='deal_see($(this) , "+  approvalProcess[i]["id"]  +" )' >查看</span>" +
                            "</td>" ;
                    }
                }
            }
            $("#tab_15_"+ num +" tbody").html(str);
            $("#infoCon").hide();  $("#tblList").show();
        },
        error:function(){ } ,
        complete:function(){ chargeClose(1) ;  }
    })
}
//点击查看，显示报销受理页面(待处理);
var processID = "" ;
var trObj = null ;
function deal_see( obj , processId  ){ 
    //  显示详细申请信息
    var processId = processId ;
    // info = JSON.parse( info ) ;
    if(obj != null){
        trObj = obj.parent().parent();
        trObj.siblings().removeClass("contactActive");
        trObj.addClass("contactActive");
    }

    //  显示详情处理页面
    // $("#infoCon").show();  $("#tblList").hide();
    processID = processId ; 
    $.ajax({
        url:"../expense/getPersonnelReimburseInfo.do",
        data:{ "approvalProcessId" : processId },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function(data){
            if( data["status"]==1 ){
                // 展示申请详情
                var approveStatus = "" ; 
                var info = data["personnelReimburse"] ;
                var str = "" ;
                if(info != undefined){
                    $("#info_creator").html( info["createName"] ) ;
                    $("#info_createDate").html( info["createDate"] ) ;
                    $("#info_feeCatName").html( info["feeCatName"] ) ;
                    $("#info_billCatName").html( info["billCatName"] ) ;
                    $("#info_billDate").html( chargeBillDate( info["billDate"] ) ) ;
                    $("#info_summary").html( info["summary"] ) ;
                    $("#info_purpose").html( info["purpose"] ) ;
                    $("#info_billQuantity").html( info["billQuantity"] ) ;
                    $("#info_amount").html( info["amount"] ) ;
                    $("#info_billAmount").html( info["billAmount"] ) ;
                    $("#info_memo").html( info["memo"] ) ;

                    // 处理流程
                    approveStatus = info["approveStatus"] ;
                }
                // 展示附件
                var strImg = "" ;
                var imgs = data["attachmentList"] ;
                for(var j = 0 ; j < imgs.length ; j++ ){
                    var path = imgs[j]["path"] ;
                    strImg += "<img onmouseenter='imgEnter($(this))'  src='../" + path+ " '/>   " ;
                }
                $("#info_img").html( strImg );
                //  展示审批流程
                $(".aboutbtn1 .id").html(info["id"] );
                $(".aboutbtn_reject .id").html(info["id"] );
                var approvalProcess = data["processList"] ;
                var str2="";
                if( approvalProcess && approvalProcess.length > 0  ){
                    for(var i = 0; i < approvalProcess.length ; i++ ) {
                        var prostatus = approvalProcess[i]["approveStatus"] ;
                        switch( Number(prostatus) ){
                            case 1 :  // 待处理
                            case 4 :  // 待两讫
                            case 6 :  // 待接收
                                str2 += '<div class="infoList ty-process-item">' +
                                    '    <p><span class="dot-wait"></span>处理人：'+ approvalProcess[i]["toUserName"] + "/ " + approvalProcess[i]["userName"] + ' </p>' +
                                    '    <p>' + approvalProcess[i]["handleTime"] + '</p>' +
                                    '</div>';
                                break ;
                            case 2 :  // 已批准
                            case 5 :  // 已报销
                            case 7 :  // 已接收
                                str2 += '<div class="infoList ty-process-item">' +
                                        '    <p><span class="dot"></span>处理人：'+ approvalProcess[i]["toUserName"] + "/ " + approvalProcess[i]["userName"] + ' </p>' +
                                        '    <p>' + approvalProcess[i]["handleTime"] + '</p>' +
                                        '</div>';
                                break ;
                            case 3 :  // 已驳回
                            case 8 :  // 财务驳回
                                str2 += '<div class="infoList ty-process-item">' +
                                        '    <p><span class="dot-no"></span>处理人：'+ approvalProcess[i]["toUserName"] + "/ " + approvalProcess[i]["userName"] + ' </p>' +
                                        '    <p>' + approvalProcess[i]["handleTime"] + '</p>' +
                                        '</div>';
                                break ;

                            default:
                                break;
                        }
                    }
                }
                var index = $(".ty-secondTab li").index($(".ty-secondTab .ty-active")); //获取状态下标 （0-待处理 1-待入库 2-已入库 3-已驳回）
                if(index === 0){
                    var str3 =  '<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reject_btn(1)">批准</span> ' +
                                '<span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="reject_btn(0)">驳回</span>';
                }else{
                    var str3 =  '<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>';
                }
                if(index === 1){
                    $("#contactDetail #info_summary").parent().show();
                }else{
                    $("#contactDetail #info_summary").parent().hide();
                }
                $("#contactDetail #process").html(str2);
                $("#contactDetail .bonceFoot").html(str3);
                bounce.show($("#contactDetail"))

                //  展示审批状态  
                // if( approvalProcess && approvalProcess.length > 0  ){
                //     if(approveStatus == 1){
                //         $("#infoContainer").attr( "class" , "applyYes dealSee_continer") ;
                //         $("#wipe_deal").html("待审批") ;
                //         var len = approvalProcess.length ;
                //         var loginUser = JSON.parse( $("#loginUser").html() ) ;
                //         var toUser = approvalProcess[len - 1]["toUser"] ;
                //         var curUser = loginUser["userID"] ;
                //         if( toUser == curUser ){
                //             $(".operate").show() ;
                //         }else{
                //             $(".operate").hide() ;
                //         }
                //     }else if(approveStatus == 2){
                //         $("#infoContainer").attr( "class" , "applyYes dealSee_continer") ;
                //         $("#wipe_deal").html("已批准") ;
                //         $(".operate").hide() ;
                //     }else if(approveStatus == 3){
                //         $("#infoContainer").attr( "class" , "applyNo dealSee_continer") ;
                //         $("#wipe_deal").html("已驳回") ;
                //         $(".operate").hide() ;
                //     }
                // }
            }else{
                bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("获取失败");
            }
        },
        error:function(){  } ,
        complete:function(){ chargeClose( 1 ) ;  }
    })
}
//进行报销受理
function reject_btn(status){
    var id = $("#tab_15_1 .contactActive").attr("id");
    var approvalStatus = status ;
    $.ajax({
        url:"../expense/approvalPersonnelReimburse.do",
        data:{
            approvalProcessId :processID ,  //审批id
            approvalStatus:approvalStatus   //审批类型   1-批准/0-驳回
        },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function(data){
            var status = data["status"];
            if(status == 1){
                bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("操作成功");
                $("#tab_15_1 .contactActive").remove();
                dealSee_pic();
            }else if(status == 3){
                bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("已批准，不能重复批准");
            }else{
                bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("操作失败");
            }
        } ,
        error:function(){  } ,
        complete:function(){ 
            loading.close() ;
            if( isfromMessage == 1 ){
                isfromMessage = 0 ;
                pending( 1 , 1 , 15 ) ;
            }
        }
    }) ; 
}
//点击箭头返回上一页
function dealSee_pic(){
    $("#infoCon").hide().siblings().show() ;   
}

// 阻止冒泡事件
function stopClick(e) {
    e.stopPropagation() ;
}
var isLI = true ; // 标识是不是竖直状态 ， true - 是 ， false - 不是
// 显示大图
function imgEnter(obj) {
    var path = obj.attr("src") ;
    $("#bigImag").attr("src" , path);
    bounce_Fixed.show($("#bigImgBnc")) ;
    var orl_width = obj.width() ;
    var orl_height = obj.height() ;
    var initWidth = 900 ;
    var initHeight = orl_height * initWidth / orl_width ;
    var screenHeight = $(window).height() ;
    if(screenHeight < initHeight){
        initHeight = screenHeight ;
        initWidth = initHeight * orl_width / orl_height ;
    }
    $("#bigImag").css({ "width": initWidth + "px" , "height":"auto"   }) ;
    var h =  $("#bigImag").height();
    $("#bigImgBnc").css({  "transform" : "rotate(0deg)", "-webkit-transform" : "rotate(0deg)" , "-moz-transform": "rotate(0deg)", "width":initWidth +"px" , "height": h +"px" , "margin":"0" }) ;
    var le = ($(window).width() - 400) / 2 ;
    $("#imgController").css({ "left" : le +"px" , "display":"block"  }) ;
    setCenter($("#bigImgBnc")) ;
    isLI = false ;
}
// 隐藏大图
function imgOut() {
    bounce_Fixed.cancel();
    $("#imgController").hide() ;
}
// 旋转图片
function rotateImg() {
    var deg = "90deg"  ;
    isLI = true ;
    var h = $("#bigImgBnc").height() ;
    var w = $("#bigImgBnc").width() ;
    var scH = $(window).height() ;
    var scW = $(window).width() ;
    var degStr = $("#bigImgBnc").attr("style") ;
    if(degStr){
        if(degStr.indexOf("90deg") != -1){
            deg = "180deg" ; isLI = false ;
        }else if(degStr.indexOf("180deg") != -1){
            deg = "270deg" ;
        }else if(degStr.indexOf("270deg") != -1){
            deg = "0deg" ; isLI = false ;
        }
    }
    var hNew = h , wNew = w ;
    if(isLI){ // 竖直状态
        if(wNew > scH){ wNew = scH ; hNew = h * wNew / w ;  }
        if(hNew > scW){ hNew = scW ; wNew = w * hNew / h ;  }
    }else{
        if(wNew > scW){ wNew = scW ; hNew = h * wNew / w ;  }
        if(hNew > scH){ hNew = scH ; wNew = w * hNew / h ;  }
    }
    $("#bigImgBnc").css({ "width": wNew + "px" , "height": hNew + "px"  }) ;
    $("#bigImgBnc").css({   "transform" : "rotate("+ deg +")" ,   "-webkit-transform" : "rotate("+ deg +")" ,  "-moz-transform": "rotate("+ deg +")" }) ;
    setCenter($("#bigImgBnc")) ;
}
// 缩小/放大 图片
function imgChange( type ) { // type : 1- expend , 0 - Compress
    var width = $("#bigImag").width() ;
    var height = $("#bigImag").height() ;
    var screenWidth = $(window).width()  ;
    var screenheight = $(window).height()  ;
    if(type == 1){
        if(isLI){
            if(height >= screenWidth){ layer.msg("不能再大啦！"); return false ; }
            if(width >= screenheight){ layer.msg("不能再大啦！");  return false ; }
        }else{
            if(height >= screenheight){ layer.msg("不能再大啦！"); return false ; }
            if(width >= screenWidth){ layer.msg("不能再大啦！");  return false ; }
        }

    }else{
        if(width <= 250){ layer.msg("不能再小啦！");  return false ; }
    }
    var widthNew = width - 100 ;
    if(type == 1){   widthNew = width + 100 ;  }
    var heightNew = widthNew * height / width ;
    if(isLI){ // 竖直状态
        if(heightNew > screenWidth){ heightNew == screenWidth ; widthNew = heightNew * width / height ;  }
        if(widthNew > screenheight){ widthNew == screenheight ; heightNew =  widthNew * height / width ;  }
    }else{
        if(heightNew > screenheight){ heightNew == screenheight ; widthNew = heightNew * width / height ;  }
        if(widthNew > screenWidth){ widthNew == screenWidth ; heightNew =  widthNew * height / width ;  }
    }
    if(widthNew < 250){ widthNew == 250 ; heightNew =  widthNew * height / width ;  }
    $("#bigImag").css({   "width" : widthNew +"px" , "height" : heightNew + "px" }) ;
    $("#bigImgBnc").css({   "width" : widthNew +"px" , "height" : heightNew + "px" }) ;
    setCenter($("#bigImgBnc")) ;

}
// creator : 侯杏哲 2018-04-26 工具方法 - 大图居中
function setCenter(obj) {
    var screenWidth = $(window).width()  ;
    var screenheight = $(window).height()  ;
    var w = obj.width() ;
    var h = obj.height() ;
    var left = (screenWidth - w)/2 ;
    var top = (screenheight - h)/2 ;
    obj.css({ "left": left + "px" , "top": top+"px" }) ;
}

// 判别票据所属月份
function chargeBillPeriod( val ){
    switch( Number(val) ){
        case 1 : return "本月票据" ; break ;
        case 2 : return "非本月票据" ; break ;
        default : return "";
    }
}

// 三个页面的跳转
function ask_leave(num) {
    var val =num;
    var url = "";
    if(Number(val) == 1 ){
        url = "../user/overtime.do"; // 加班
    }
    if(Number(val) == 2 ){
        url = "../user/leave.do"; //请假
    }
    if(Number(val) == 3 ){
        url = "../expense/chargeSubmitSale.do"; // 处理报销申请
    }
    if( url == ""){ return false; }
    location.href = url;
}

// 判别票据月份
function chargeBillDate( val ){
    if( Number(val) == 1 ){ return "本月票据" ;  }
    if( Number(val) == 2 ){ return "非本月票据" ;  }
    return "" ;
}