var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#alertMS"));
bounce_Fixed2.cancel();
// creator: 李玉婷，2019-11-14 08:54:04，验证手机号
jQuery.validator.addMethod("isMobile", function(value, element) {
    var length = value.length;
    var mobile = /^(13[0-9]{9})|(18[0-9]{9})|(14[0-9]{9})|(17[0-9]{9})|(15[0-9]{9})$/;
    return this.optional(element) || (length == 11 && mobile.test(value));
}, "请正确填写手机号码");
function isMobileMethod(mobile){
    var regPhone = /^(13[0-9]|15[012356789]|18[0123456789]|147|145|17[0-9])\d{8}$/;
    if(regPhone.test(mobile)){
        return true;
    }else{
        return false;
    }
}
// creator: 李玉婷，2019-11-06 11:26:32，个人信息弹窗
function getPersonalInfor() {
    $(".experience").remove();
    if (sphdSocket.user.roleCode === 'agent') {
        layer.msg("本账号已被冻结！")
        return false
    }
    $.ajax({
        "url": "/userInfo/userInfoPreview.do",
        "data": '',
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                var user = res['data']['user'];
                var checkState = user.submitState;
                var workList = res['data']['personnelOccupations'];
                var eduList = res['data']['personalEducations'];
                $("#personalInfor").data('state', checkState);
                if (checkState == '0') {
                    $("#backBtn").show();
                    bounce.everyTime('0.5s','checkState',function(){
                        var select = $(".agreenTip").data('checked');
                        if (select == '0') {
                            $("#checkInfoFinish").prop("disabled",true);
                        }else{
                            $("#checkInfoFinish").prop("disabled",false);
                        }
                    });
                }else{
                    $(".checkOperation").remove();
                    $("#backBtn").hide();
                    $("#closePersonalInfor").show();
                }
                if (eduList.length > 0) {
                    var educations = '';
                    for( var t = 0; t < eduList.length; t++) {
                        if (eduList[t].deleted == true) {
                            educations +=
                                '<tr class="experience">' +
                                '    <td><p style="text-indent: 2em;">'+ eduList[t].collegeName +'<span class="ty-color-red">(已删除)</span></p></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue" data-name="edu" data-id="'+ eduList[t].id +'" onclick="personUpdateRecord(\'edu\', '+ eduList[t].id + ', 1, 10)">修改记录</span>' +
                                '    </td>' +
                                '</tr>';
                        }else {
                            educations +=
                                '<tr class="experience">' +
                                '    <td><p style="text-indent: 2em;">'+ eduList[t].collegeName +'</p></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue" data-type="update" data-id="'+ eduList[t].id +'" onclick="eduExperience($(this))">修改</span>' +
                                '        <span class="ty-color-blue" data-name="edu" data-id="'+ eduList[t].id +'" onclick="personUpdateRecord(\'edu\', '+ eduList[t].id + ', 1, 10)">修改记录</span>' +
                                '        <span class="ty-color-blue" data-name="edu" data-id="'+ eduList[t].id +'" onclick="deleteExperience($(this))">删除</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                    }
                    $(".eduList").after(educations);
                }
                if (workList.length > 0) {
                    var  occupations = '';
                    for( var t = 0; t < workList.length; t++) {
                        if (workList[t].deleted == true) {
                            occupations +=
                                '<tr class="experience">' +
                                '    <td><p style="text-indent: 2em;">'+ workList[t].corpName +'<span class="ty-color-red">(已删除)</span></p></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue" data-name="work" data-id="'+ workList[t].id +'" onclick="personUpdateRecord(\'work\', '+ workList[t].id + ', 1, 10)">修改记录</span>' +
                                '    </td>' +
                                '</tr>';
                        }else {
                            occupations +=
                                '<tr class="experience">' +
                                '    <td><p style="text-indent: 2em;">'+ workList[t].corpName +'</p></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue" data-type="update" data-id="'+ workList[t].id +'" onclick="workExperience($(this))">修改</span>' +
                                '        <span class="ty-color-blue" data-name="work" data-id="'+ workList[t].id +'" onclick="personUpdateRecord(\'work\', '+ workList[t].id + ', 1, 10)">修改记录</span>' +
                                '        <span class="ty-color-blue" data-name="work" data-id="'+ workList[t].id +'" onclick="deleteExperience($(this))">删除</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                    }
                    $(".workList").after(occupations);
                }
                bounce.show($('#personalInfor'));
            } else{
                layer.msg('获取失败！');
            }
        },
        error:function(){
            $('#altMS').html('系统错误，请重试!');
            bounce_Fixed2.show($('#alertMS'));
        }
    })
}
// creator: 李玉婷，2019-11-07 16:55:30，预览
function personalInforView () {
    $('#baseInfoDetails .contectList') .html('');
    $('#eduHashMap') .html('');
    $('#workHashMap') .html('');
    $.ajax({
        "url": "/userInfo/userInfoPreview.do",
        "data":'',
        success: function (res) {
            var success = res['success'];
            if (success == '1') {
                var userInfo = res.data.user;
                var userContacts = res.data.userContacts;
                var eduList = res.data.personalEducations;
                var workList = res.data.personnelOccupations;
                $("#employeeUserName").html(userInfo.userName);
                $("#baseInfoDetails [require]").each(function () {
                    var name = $(this).data('name');
                    switch (name) {
                        case 'gender':
                            var str = chargeSex(handleNull(userInfo['gender']));
                            if (handleNull(userInfo['gender']) != ''){
                                str = chargeSex(userInfo['gender']) + '<i> / </i>';
                            }
                            $(this).html(str);
                            break;
                        case 'marry':
                            var str = "";
                            if (handleNull(userInfo['marry']) != ""){
                                str = chargeMarry(userInfo['marry']) + '<i> / </i>';
                            }
                            $(this).html(str);
                            break;
                        case 'degree':
                            if (chargeDegree(userInfo['degree']) != '') {
                                $(this).html(chargeDegree(userInfo['degree']) + '<i> / </i>');
                            } else{
                                $(this).html('');
                            }
                            break;
                        case 'birthday':
                            if (handleNull(userInfo[name]) != '') {
                                $(this).html(new Date(userInfo[name]).format('yyyy年MM月dd日生') + '<i> / </i>');
                            } else{
                                $(this).html('');
                            }
                            break;
                        default:
                            if (handleNull(userInfo[name]) != "") {
                                $(this).html(userInfo[name] + '<i> / </i>');
                            } else {
                                $(this).html('');
                            }
                            break;
                    }
                });
                var skills = 0;
                $("#baseInfoDetails [need]").each(function () {
                    var name = $(this).data('name');
                    if (handleNull(userInfo[name]) != "") {
                        skills++;
                        $(this).html(userInfo[name]);
                    } else {
                        $(this).html('');
                    }
                });
                if (skills > 0) {
                    $("#skills").show();
                } else {
                    $("#skills").hide();
                }
                var html = '';
                var contactList = {
                    'mobile': [{
                        name: 'mobile',
                        code: userInfo.mobile
                    }],
                    'qq': [],
                    'email': [],
                    'weixin': [],
                    'weibo': [],
                    'defined': []
                };
                var typeList = [
                    {'name': 'mobile'}, {'name': 'qq'}, {'name': 'email'}, {'name': 'weixin'}, {'name': 'weibo'}, {'name': 'defined'}
                ]
                for (var t = 0; t < userContacts.length; t++) {
                    var type = userContacts[t].type;
                    switch (type) {
                        case '1':
                            contactList['mobile'].push(userContacts[t]);
                            break;
                        case '2':
                            contactList['qq'].push(userContacts[t]);
                            break;
                        case '3':
                            contactList['email'].push(userContacts[t]);
                            break;
                        case '4':
                            contactList['weixin'].push(userContacts[t]);
                            break;
                        case '5':
                            contactList['weibo'].push(userContacts[t]);
                            break;
                        case '9':
                            contactList['defined'].push(userContacts[t]);
                            break;
                    }
                }
                for (var y = 0; y < typeList.length; y++) {
                    var name = typeList[y].name;
                    if (contactList[name] && contactList[name].length > 0) {
                        if (name == 'defined') {
                            for (var t=0; t<contactList[name].length;t++){
                                html +=
                                    ' <li>' +
                                    '    <div class="tctImg ">'+ contactList[name][t].name +':</div>' +
                                    '    <div class="ty-left">'+
                                    '    <p title="'+ contactList[name][t].code +'">'+ contactList[name][t].code +'</p>' +
                                    '</div></li>';
                            }
                        }else{
                            html +=
                                ' <li>' +
                                '    <div class="tctImg ' + name + '"></div>' +
                                '    <div class="ty-left">';
                            for (var t = 0; t < contactList[name].length; t++) {
                                html += '<p title="'+ contactList[name][t].code +'">' + contactList[name][t].code + '</p>';
                            }
                            html += '</div></li>';
                        }
                    }
                }
                $('#baseInfoDetails .contectList').html(html);
                if (eduList.length > 0){
                    var ehtml = '';
                    for(var e=0;e<eduList.length;e++){
                        if (eduList[e].deleted == false) {
                            ehtml +=
                                '<div class="areaBood">' +
                                '<div class="charact">' +
                                '    <div class="mmTtl">'+ new Date(eduList[e].beginTime).format('yyyy/MM') +'-'+ new Date(eduList[e].endTime).format('yyyy/MM') +' </div>' +
                                '    <div class="timeSlot">' +
                                '        <span>'+ handleNull(eduList[e].collegeName) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(eduList[e].departmentName) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(eduList[e].major) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(eduList[e].degreeDesc) +'</span>' +
                                '    </div>' +
                                '</div>' +
                                '<div class="charact">' +
                                '    <div class="mmTtl">专业描述：</div>' +
                                '    <div class="mmCon">' +
                                '        <p>'+ handleNull(eduList[e].majorDesc) +'</p>' +
                                '    </div>' +
                                '</div>' +
                                '<div class="charact">' +
                                '    <div class="mmTtl">补充说明：</div>' +
                                '    <div class="mmCon">' +
                                '        <p>'+ handleNull(eduList[e].memo) +'</p>' +
                                '    </div>' +
                                '</div></div> ';
                        }
                    }
                    $('#eduHashMap') .html(ehtml);
                }
                if (workList.length > 0){
                    var whtml = '';
                    for (var w=0; w<workList.length;w++) {
                        if (workList[w].deleted == false) {
                            whtml +=
                                '<div class="areaBood">' +
                                '<div class="charact">' +
                                '    <div class="mmTtl">'+ new Date(workList[w].beginTime).format('yyyy/MM') +'-'+ new Date(workList[w].endTime).format('yyyy/MM') +' </div>' +
                                '    <div class="timeSlot">' +
                                '        <span>'+ handleNull(workList[w].corpName) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(workList[w].corpNature) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(workList[w].corpSize) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(workList[w].corpDepartment) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(workList[w].post) +'</span>' +
                                '    </div>' +
                                '</div>' +
                                '<div class="charact">' +
                                '    <div class="mmTtl">工作描述：</div>' +
                                '    <div class="mmCon">' +
                                '        <p>'+ handleNull(workList[w].jobDesc) +'</p>' +
                                '    </div>' +
                                '</div>'+
                                '<div class="charact">' +
                                '    <div class="mmTtl">补充说明：</div>' +
                                '    <div class="mmCon">' +
                                '        <p>'+ handleNull(workList[w].memo) +'</p>' +
                                '    </div>' +
                                '</div></div>';
                        }
                    }
                    $('#workHashMap') .html(whtml);
                }
                bounce_Fixed.show($('#personalInforView'));
            } else{
                layer.msg('获取失败！');
            }
        },
        error:function(){
            $('#altMS').html('系统错误，请重试!');
            bounce_Fixed2.show($('#alertMS'));
        }
    });
}
// creator: 李玉婷，2019-11-07 16:32:35，修改基本信息修改详情获取
function updateBaseInfo () {
    $(".addMoreSelect").hide();
    $(".otherContactType").remove();
    $.ajax({
        "url": "/userInfo/userInfoPreview.do",
        "data":'',
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                var base = res['data']['user'];
                var userContacts = res['data']['userContacts'];
                var degree = handleNull(base.degree) == '' ? 0:base.degree;
                $("#updateBaseDetails input[name='sex']").prop('checked', false);
                $("#updateBaseName").html(base.userName);
                $("#personPhone").html(base.mobile);
                $("#updateBaseDetails input[name='sex'][value='" + handleNull(base.gender) + "']").prop('checked',true).data('org', handleNull(base.gender));
                $("#updateBaseDetails select[name='degree']").val(degree);
                $("#updateBaseDetails select[name='degree']").data('org', degree);
                $("#updateBaseDetails [require]").each(function () {
                    var name = $(this).attr('name');
                    if (name == 'birthday') {
                        var day = new Date(base[name]).format('yyyy/MM/dd');
                        if (day != ''){
                            $(this).val(day).data('org',day);
                        } else {
                            $(this).val(new Date().format('yyyy/MM/dd')).data('org',day);
                        }
                    } else if (name == 'marry') {
                        if (handleNull(base[name]) != ''){
                            $(this).val(handleNull(base[name])).data('org',handleNull(base[name]));
                        } else {
                            $(this).val('1').data('org',handleNull(base[name]));
                        }
                    } else {
                        $(this).val(handleNull(base[name])).data('org',handleNull(base[name]));
                    }
                });
                if (userContacts.length > 0) {
                    var html = '', name = ['','其他手机','Q Q','Email','微信','微博'], nameStr = '', isCan = '', definedName = '';
                    for( var t = 0; t < userContacts.length; t++) {
                        nameStr = userContacts[t].type == '9' ? userContacts[t].name:name[Number(userContacts[t].type)];
                        definedName = userContacts[t].type == '9' ? ' data-name="' + userContacts[t].name +'" ':'';
                        isCan = userContacts[t].open == true? '':'<span class="ty-color-blue cansee" onclick="removeCanSee($(this))">同事可见</span>';
                        html +=
                            '<li class="otherContactType" data-can="'+ userContacts[t].open +'" data-org="'+ userContacts[t].open +'">' +
                            '   <span class="one-ttl">'+ nameStr +'</span>' +
                            '   <input type="text" value="'+ userContacts[t].code +'" placeholder="请录入" ' + definedName + ' data-type="'+ userContacts[t].type +'" data-org="'+ userContacts[t].code +'" />' +
                            '   <a class="clearKey" onclick="clearKey($(this))">x</a>' +
                            '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+ isCan +
                            '</li>';
                    }
                    $("#updateBaseDetails").append(html);
                }
                $("#updateBaseDetails").data('userContacts',userContacts.length);
                bounce_Fixed.show($('#updatePerInfo'));
                bounce_Fixed.everyTime('0.5s','baseInfo',function(){
                    var state = 0;
                    $("#updateBaseDetails [require]").each(function () {
                        if($(this).data('org') != $(this).val()) state ++;
                    });
                    if($("#updateBaseDetails input[name='sex']:checked").length>0 && $("#updateBaseDetails input[name='sex']:checked").data('org') != $("#updateBaseDetails input[name='sex']:checked").val()){
                        state ++;
                    }
                    if($("#updateBaseDetails select[name='degree']").data('org') != $("#updateBaseDetails select[name='degree']").val()){
                        state ++;
                    }
                    if ($("#updateBaseDetails").data('userContacts') == $("#updateBaseDetails .otherContactType").length){
                        $("#updateBaseDetails .otherContactType").each(function(){
                            if($(this).find("input").val() != $(this).find("input").data('org')){
                                state ++;
                            }
                            if($(this).data('can') != $(this).data('org')){
                                state ++;
                            }
                        })
                    }else if ($("#updateBaseDetails").data('userContacts') < $("#updateBaseDetails .otherContactType").length){
                         $("#updateBaseDetails .otherContactType").each(function(){
                             if($(this).find("input").val() != $(this).find("input").data('org')){
                                 state ++;
                             }
                         })
                    }else{
                        state ++;
                    }
                    if(state != 0){
                        $("#updateBaseInfoSure").prop('disabled', false);
                    }else{
                        $("#updateBaseInfoSure").prop('disabled', true);
                    }
                    if ($("#updateBaseDetails .otherContactType").length > 0) {
                        $("#updateBaseDetails .otherContactType").each(function () {
                            var val = $(this).find("input").val();
                            var type = $(this).find("input").data('type');
                            if (type == '9' || type == '9') type = 6
                            if (val == '') {
                                $(".addMoreSelect option").eq(type).prop('disabled',true);
                            }else{
                                $(".addMoreSelect option").eq(type).prop('disabled',false);
                            }
                        })
                    } else {
                        $(".addMoreSelect option").prop('disabled', false);
                    }
                })
            } else{
                layer.msg('获取失败！');
            }
        },
        error:function(){
            $('#altMS').html('系统错误，请重试!');
            bounce_Fixed2.show($('#alertMS'));
        }
    });
}
// creator: 李玉婷，2019-11-07 16:32:35，修改基本信息修改详情提交
function updateBaseInfoSure() {
    var userInfo = {
        'passiveUserId': sphdSocket.user.userID,
        'gender': '',
        'degree': $("#updateBaseDetails select[name='degree']").val()
    }
    var contacts = [];
    if ($("#updateBaseDetails input[name='sex']:checked").length > 0) {
        userInfo.gender = $("#updateBaseDetails input[name='sex']:checked").val();
    }
    $("#updateBaseDetails [require]").each(function () {
        var name = $(this).attr('name');
        userInfo[name] = $(this).val();
    });
    $("#updateBaseDetails .otherContactType").each(function(){
        if ($(this).find('input').val() != ''){
            var type = $(this).find('input').data('type');
            var name = $(this).find('input').data('name');
            var isOpen = $(this).data('can');
            contacts.push({
                'type': type,
                'name': name,
                'code': $(this).find('input').val(),
                'isOpen': isOpen
            })
        }
    });
    userInfo = JSON.stringify(userInfo);
    contacts = JSON.stringify(contacts);
    var params = {
        'userInfo': userInfo,
        'contacts': contacts
    };
    $.ajax({
        "url": "/userInfo/updateUserBasicInfo.do",
        "data": params,
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                bounce_Fixed.cancel();
            } else{
                layer.msg('提交失败！');
            }
        },
        error:function(){
            $('#altMS').html('系统错误，请重试!');
            bounce_Fixed2.show($('#alertMS'));
        }
    });
}
// creator: 李玉婷，2019-11-05 17:12:04，添加更多联系方式
function addMoreContact (obj) {
    obj.next("select").show();
}
// creator: 李玉婷，2019-11-05 17:15:13，添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContactType").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li class="otherContactType" data-can="false">' +
                '   <span class="one-ttl">其他手机</span>' +
                '   <input type="text" placeholder="请录入" data-type="1" data-org="" onkeyup="clearKeyShow($(this))" onchange="addCanSee($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '   <span class="ty-color-blue cansee" style="display: none;" onclick="removeCanSee($(this))">同事可见</span>'+
                '</li>';
            $("#updateBaseDetails").append(html);
            break;
        case '2':
            html +=
                '<li class="otherContactType" data-can="false">' +
                '   <span class="one-ttl">Q Q</span>' +
                '   <input type="text" placeholder="请录入" data-type="2" data-org="" onkeyup="clearKeyShow($(this))" onchange="addCanSee($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '   <span class="ty-color-blue cansee" style="display: none;" onclick="removeCanSee($(this))">同事可见</span>'+
                '</li>';
            $("#updateBaseDetails").append(html);
            break;
        case '3':
            html +=
                '<li class="otherContactType" data-can="false">' +
                '   <span class="one-ttl">Email</span>' +
                '   <input type="text" placeholder="请录入" data-type="3" data-org="" onkeyup="clearKeyShow($(this))" onchange="addCanSee($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '   <span class="ty-color-blue cansee" style="display: none;" onclick="removeCanSee($(this))">同事可见</span>'+
                '</li>';
            $("#updateBaseDetails").append(html);
            break;
        case '4':
            html +=
                '<li class="otherContactType" data-can="false">' +
                '   <span class="one-ttl">微信</span>' +
                '   <input type="text" placeholder="请录入" data-type="4" data-org="" onkeyup="clearKeyShow($(this))" onchange="addCanSee($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '   <span class="ty-color-blue cansee" style="display: none;" onclick="removeCanSee($(this))">同事可见</span>'+
                '</li>';
            $("#updateBaseDetails").append(html);
            break;
        case '5':
            html +=
                '<li class="otherContactType" data-can="false">' +
                '   <span class="one-ttl">微博</span>' +
                '   <input type="text" placeholder="请录入" data-type="5" data-org="" onkeyup="clearKeyShow($(this))" onchange="addCanSee($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '   <span class="ty-color-blue cansee" style="display: none;" onclick="removeCanSee($(this))">同事可见</span>'+
                '</li>';
            $("#updateBaseDetails").append(html);
            break;
        case '9':
            $("#useDefinedLabel input").val("");
            bounce_Fixed2.show($("#useDefinedLabel"));
            bounce_Fixed2.everyTime('0.5s', 'useDefinedLabel', function () {
                if ($("#defLable").val() == '') {
                    $("#addNewLableSure").prop('disabled', true);
                } else{
                    $("#addNewLableSure").prop('disabled', false);
                }
            })
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-19 10:14:23，自定义标签确定
function addNewLable(){
    var val = $("#defLable").val();
    var html =
        '<li class="otherContactType" data-can="false">' +
        '   <span class="one-ttl">' + val + '：</span>' +
        '   <input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" onkeyup="clearKeyShow($(this))" onchange="addCanSee($(this))" data-org="" require/>'+
        '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
        '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
        '  <span class="ty-color-blue cansee" style="display: none;" onclick="removeCanSee($(this))">同事可见</span>'+
        '</li>';
    $("#updateBaseDetails").append(html);
    bounce_Fixed2.cancel();
}
// creator: 李玉婷，2019-11-05 19:27:59，同事可见
function removeCanSee (obj) {
    $("#canSeeMS").data('obj', obj);
    bounce_Fixed2.show($("#canSeeMS"));
}
// creator: 李玉婷，2019-11-14 13:58:48，同事可见出现
function addCanSee(obj) {
    if (obj.val() != ''){
        obj.siblings('.cansee').show();
    }
}
// creator: 李玉婷，2019-11-15 15:07:38，同事可见确定
function canSeeSure(){
    var obj = $("#canSeeMS").data('obj');
    obj.parent().data('can', true);
    obj.remove();
    bounce_Fixed2.cancel();
}
// creator: 李玉婷，2019-11-05 19:29:31，添加更多联系方式 - 删除
function removeAdd (obj) {
    obj.parent("li").remove();
}
// creator: 李玉婷，2019-11-15 10:04:14，一键清除
function clearKey(obj){
    obj.prev('input').val('')
}
// creator: 李玉婷，2019-11-15 10:06:49，一键清除出现
function clearKeyShow(obj){
    obj.next('a').show();
}
// creator: 李玉婷，2019-11-06 10:00:45，新增/修改教育经历
function eduExperience(obj) {
    document.getElementById("eduInfoForm").reset();
    var type = obj.data('type');
    $("#eduExperienceTj").data('type', type)
    if (type == 'new') {
        $("#eduInfoForm [require]").each(function () {
            $(this).data('org', '');
        })
    } else if (type == 'update') {
        var eduId = obj.data('id');
        $('#addEduInfo').data('eduId', eduId);
        $.ajax({
            "url": "/userInfo/userInfoPreview.do",
            "data": '',
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    var eduList = res['data']['personalEducations'];
                    var findItem = {};
                    findItem = eduList.find(item => item.id == eduId);
                    if(findItem != undefined) {
                        $("#eduInfoForm [require]").each(function () {
                            var name = $(this).attr('name');
                            if (name == 'beginTime' || name == 'endTime') {
                                $(this).val(new Date(findItem[name]).format('yyyy/MM')).data('org', new Date(findItem[name]).format('yyyy/MM'));
                            }else{
                                $(this).val(findItem[name]).data('org', findItem[name]);
                            }
                        })
                    }
                } else{
                    layer.msg('获取失败！');
                }
            },
            error:function(){
                $('#altMS').html('系统错误，请重试!');
                bounce_Fixed2.show($('#alertMS'));
            }
        })
    }
    bounce_Fixed.show($('#addEduInfo'));
    setEvery('educations');
}
// creator: 李玉婷，2019-11-06 10:33:18，新增教育经历提交
function eduExperienceTj () {
    var params = {}
    var tjType = $("#eduExperienceTj").data('type');
    $("#eduInfoForm [require]").each(function () {
        var name = $(this).attr('name');
        params[name] = $(this).val();
    })
    if (tjType == 'new') {
        $.ajax({
            "url": "/userInfo/addPersonalEducation.do",
            "data": params,
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    getPersonalInfor();
                    bounce_Fixed.cancel();
                } else{
                    layer.msg('提交失败！');
                }
            },
            error:function(){
                $('#altMS').html('系统错误，请重试!');
                bounce_Fixed2.show($('#alertMS'));
            }
        });
    } else {
        params.id = $('#addEduInfo').data('eduId');
        $.ajax({
            "url": "/userInfo/updatePersonalEducation.do",
            "data": params,
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    getPersonalInfor();
                    bounce_Fixed.cancel();
                } else{
                    layer.msg('提交失败！');
                }
            },
            error:function(){
                $('#altMS').html('系统错误，请重试!');
                bounce_Fixed2.show($('#alertMS'));
            }
        });
    }
}
// creator: 李玉婷，2019-11-06 10:00:45，新增/修改工作经历
function workExperience(obj) {
    document.getElementById("WorkInfoForm").reset();
    var type = obj.data('type');
    $("#eduExperienceTj").data('type', type);
    if (type == 'new') {
        $("#WorkInfoForm [require]").each(function () {
            $(this).data('org','');
        })
    } else if (type == 'update') {
        var eduId = obj.data('id');
        $('#addWorkInfo').data('workId', eduId);
        $.ajax({
            "url": "/userInfo/userInfoPreview.do",
            "data": '',
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    var workList = res['data']['personnelOccupations'];
                    var findItem = {};
                    findItem = workList.find(item => item.id == eduId);
                    if(findItem != undefined) {
                        $("#WorkInfoForm [require]").each(function () {
                            var name = $(this).attr('name');
                            if (name == 'beginTime' || name == 'endTime') {
                                $(this).val(new Date(findItem[name]).format('yyyy/MM')).data('org',new Date(findItem[name]).format('yyyy/MM'));
                            }else{
                                $(this).val(findItem[name]).data('org',findItem[name]);
                            }
                        })
                    }
                } else{
                    layer.msg('获取失败！');
                }
            },
            error:function(){
                $('#altMS').html('系统错误，请重试!');
                bounce_Fixed2.show($('#alertMS'));
            }
        })
    }
    bounce_Fixed.show($('#addWorkInfo'));
    setEvery('occupations');
}
// creator: 李玉婷，2019-11-06 13:02:49，新增工作经历提交
function workExperienceTj () {
    var params = {}
    var tjType = $("#eduExperienceTj").data('type');
    $("#WorkInfoForm [require]").each(function () {
        var name = $(this).attr('name');
        params[name] = $(this).val();
    })
    if (tjType == 'new') {
        $.ajax({
            "url": "/userInfo/addPersonnelOccupation.do",
            "data": params,
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    getPersonalInfor();
                    bounce_Fixed.cancel();
                } else{
                    layer.msg('提交失败！');
                }
            },
            error:function(){
                $('#altMS').html('系统错误，请重试!');
                bounce_Fixed2.show($('#alertMS'));
            }
        });
    } else {
        params.id = $('#addWorkInfo').data('workId');
        $.ajax({
            "url": "/userInfo/updatePersonnelOccupation.do",
            "data": params,
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    getPersonalInfor();
                    bounce_Fixed.cancel();
                } else{
                    layer.msg('提交失败！');
                }
            },
            error:function(){
                $('#altMS').html('系统错误，请重试!');
                bounce_Fixed2.show($('#alertMS'));
            }
        });
    }
}
// creator: 李玉婷，2019-11-13 14:43:19，设置定时器
function setEvery(name) {
    switch (name) {
        case 'educations':
            bounce_Fixed.everyTime('0.5s', 'educations', function(){
                var edu = 0, filedNum=0;
                $("#eduInfoForm [require]").each(function() {
                    if ($(this).val() != '') filedNum++;
                    if ($(this).data('org') != $(this).val()){
                        edu ++;
                    }
                })
                if (edu > 0 && filedNum > 0) {
                    $("#eduExperienceTj").prop('disabled', false);
                } else{
                    $("#eduExperienceTj").prop('disabled', true);
                }
            });
            break;
        case 'occupations':
            bounce_Fixed.everyTime('0.5s', 'occupations', function(){
                var work = 0, filedNum=0;
                $("#WorkInfoForm [require]").each(function() {
                    if ($(this).val() != '') filedNum++;
                    if ($(this).data('org') != $(this).val()){
                        work ++;
                    }
                })
                if (work > 0 && filedNum > 0) {
                    $("#workExperienceTj").prop('disabled', false);
                } else{
                    $("#workExperienceTj").prop('disabled', true);
                }
            });
            break;
    }
}
// creator: 李玉婷，2019-11-07 08:39:30，教育经历/工作经历删除
function deleteExperience (obj) {
    var state = $("#personalInfor").data('state');
    var name = obj.data('name');
    var delId = obj.data('id');
    var json ={
        'id': delId,
        'name': name
    }
    json = JSON.stringify(json);
    $("#deleteDetail").data('deleteParam', json);
    if (name == 'edu') {
        $(".deleteTtl").html('教育经历');
    } else {
        $(".deleteTtl").html('工作经历');
    }
    if(state == '0'){
        $(".tipMessage .deleteClear").show().siblings().hide();
    }else{
        $(".tipMessage .deleteClear").hide().siblings().show();
    }
    bounce_Fixed.show($('#deleteDetail'));
}
// creator: 李玉婷，2019-11-07 10:10:46，教育经历/工作经历删除确定
function deleteExperienceSure() {
    var deleteParam = JSON.parse($("#deleteDetail").data('deleteParam'));
    if (deleteParam.name == 'edu') {
        $.ajax({
            "url": "/userInfo/markDeleteEducation.do",
            "data": {
                id: deleteParam.id
            },
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    bounce_Fixed.cancel();
                    getPersonalInfor();
                } else{
                    layer.msg('删除失败！');
                }
            },
            error:function(){
                $('#altMS').html('系统错误，请重试!');
                bounce_Fixed2.show($('#alertMS'));
            }
        });
    } else {
        $.ajax({
            "url": "/userInfo/markDeleteOccupation.do",
            "data": {
                id: deleteParam.id
            },
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    bounce_Fixed.cancel();
                    getPersonalInfor();
                } else{
                    layer.msg('删除失败！');
                }
            },
            error:function(){
                $('#altMS').html('系统错误，请重试!');
                bounce_Fixed2.show($('#alertMS'));
            }
        });
    }
}
// creator: 李玉婷，2019-11-22 14:14:26，编辑紧急联系方式
function updateContactUpdate() {
    document.getElementById("emergencyForm").reset();
    $.ajax({
        "url": "/userInfo/userInfoPreview.do",
        "data": '',
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                var user = res['data']['user'];
                $("#emergencyForm [require]").each(function () {
                    var key = $(this).attr('name');
                    $(this).val(handleNull(user[key])).data('org', handleNull(user[key]));
                })
            }
        },
        error:function(){
            $('#altMS').html('系统错误，请重试!');
            bounce_Fixed2.show($('#alertMS'));
        }
    });
    bounce_Fixed.show($('#updateContact'));
    bounce_Fixed.everyTime('0.5s', 'updateContact', function () {
        var sum = 0, filledNum =0;
        $("#emergencyForm [require]").each(function(){
            if ($(this).val() != '') filledNum++;
            if($(this).val() != $(this).data('org')){
                sum++;
            }
        })
        if (sum > 0 && filledNum > 0){
            $("#updateEmergencyContact").prop('disabled', false);
        } else {
            $("#updateEmergencyContact").prop('disabled', true);
        }
    })
}
// creator: 李玉婷，2019-11-06 13:52:18，编辑紧急联系方式确定
function emergencyContact() {
    var params = {};
    $("#emergencyForm input[require]").each(function () {
        var name = $(this).attr('name');
        params[name] = $(this).val();
    })
    $.ajax({
        "url": "/userInfo/addEmergencyContact.do",
        "data": params,
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                bounce_Fixed.cancel();
            } else{
                layer.msg('提交失败！');
            }
        },
        error:function(){
            $('#altMS').html('系统错误，请重试!');
            bounce_Fixed2.show($('#alertMS'));
        }
    });
}
// creator: 李玉婷，2019-11-07 08:45:41，修改记录
function personUpdateRecord (name, id, currPage, pageSize) {
    var jsonStr = {
        'name': name,
        'id': id
    } ;
    jsonStr = JSON.stringify(jsonStr) ;
    switch (name) {
        case 'base':
            $.ajax({
                "url": "/userInfo/getUserBasicInfoHistory.do",
                "data": {
                    pageSize: pageSize,
                    currentPageNo: currPage
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var record = res['data']['userRoleHistoryList'];
                        $("#basePageInfo").html('');
                        if (Number(res['data'].number) > 0) {
                            var pageInfo = res['data']['pageInfo'];
                            var totalPage = pageInfo["totalPage"];
                            var cur = pageInfo["currentPageNo"];
                            setPage( $("#basePageInfo") , cur ,  totalPage , "personInfoRecord" ,jsonStr );
                            $('.baseUpdateNum').html(res['data'].number);
                            $('.baseUpdateInfo').html(handleNull(res['data'].updateName) + ' &nbsp;&nbsp; ' + new Date(res['data'].updateTime).format('yyyy/MM/dd hh:mm:ss'));
                            var str = recordList(record, 'base');
                            $(".updateBaseMessage").html(str);
                            $(".notEditBase").hide();
                            $(".recordMain").show();
                        } else {
                            $(".notEditBase").show();
                            $(".recordMain").hide();
                        }
                        bounce_Fixed.show($('#baseInfoRecord'));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $('#altMS').html('系统错误，请重试!');
                    bounce_Fixed2.show($('#alertMS'));
                }
            });
            break;
        case 'edu':
            $("#recordName").html('教育经历修改记录');
            $("#otherPageInfo").html('');
            $.ajax({
                "url": "/userInfo/personalEducationHistories.do",
                "data": {
                    id: id,
                    pageSize: pageSize,
                    currentPageNo: currPage
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var record = res['data']['personnelEducationHistoryList'];
                        var info = res['data']['personalEducation'];
                        if (Number(res['data'].number) > 0) {
                            var pageInfo = res['data']['pageInfo'];
                            var totalPage = pageInfo["totalPage"];
                            var cur = pageInfo["currentPageNo"];
                            setPage( $("#otherPageInfo") , cur ,  totalPage , "personInfoRecord" ,jsonStr );
                            if (info.deleted == true){
                                $('.othereUpdateNum').html('<span class="colorOrange">该条数据已被删除。</span>');
                                $('.deltedAfter').show();
                                $('.deltedTtl').html('毕业院校');
                                $('.deltedCon').html(info.collegeName);
                            }else{
                                $('.deltedAfter').hide();
                                $('.othereUpdateNum').html('当前数据为第' + res['data'].number + '次修改后的结果。');
                            }
                            $('.otherUpdateInfo').html(new Date(info.updateDate).format('yyyy/MM/dd hh:mm:ss'));
                            var str = recordList(record, 'edu');
                            $(".updateRecord").html(str);
                            $(".notEditOther").hide();
                            $(".recordMain").show();
                        } else {
                            $(".notEditOther").html('教育经历尚未修改过。').show();
                            $(".recordMain").hide();
                        }
                        bounce_Fixed.show($('#eduInfoRecord'));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $('#altMS').html('系统错误，请重试!');
                    bounce_Fixed2.show($('#alertMS'));
                }
            });
            break;
        case 'work':
            $("#recordName").html('工作经历修改记录');
            $("#otherPageInfo").html('');
            $.ajax({
                "url": "/userInfo/personnelOccupationHistories.do",
                "data": {
                    id: id,
                    pageSize: pageSize,
                    currentPageNo: currPage
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var record = res['data']['personnelOccupationHistoryList'];
                        var info = res['data']['personnelOccupation'];
                        if (Number(res['data'].number) > 0) {
                            var pageInfo = res['data']['pageInfo'];
                            var totalPage = pageInfo["totalPage"];
                            var cur = pageInfo["currentPageNo"];
                            setPage( $("#otherPageInfo") , cur ,  totalPage , "personInfoRecord" ,jsonStr );
                            if (info.deleted == true){
                                $('.othereUpdateNum').html('<span class="colorOrange">该条数据已被删除。</span>');
                                $('.deltedAfter').show();
                                $('.deltedTtl').html('公司名称');
                                $('.deltedCon').html(info.collegeName);
                            }else{
                                $('.deltedAfter').hide();
                                $('.othereUpdateNum').html('当前数据为第' + res['data'].number + '次修改后的结果。');
                            }
                            $('.otherUpdateInfo').html(new Date(info.updateDate).format('yyyy/MM/dd hh:mm:ss'));
                            var str = recordList(record, 'work');
                            $(".updateRecord").html(str);
                            $(".notEditOther").hide();
                            $(".recordMain").show();
                        } else {
                            $(".notEditOther").html('工作经历尚未修改过。').show();
                            $(".recordMain").hide();
                        }
                        bounce_Fixed.show($('#eduInfoRecord'));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $('#altMS').html('系统错误，请重试!');
                    bounce_Fixed2.show($('#alertMS'));
                }
            });
            break;
    }
}
// creator: 李玉婷，2019-11-07 13:39:28，修改记录列表设置
function recordList(record, name) {
    var html = '';
    if(name == 'base'){
        html =
            ' <tr>' +
            '     <td>记录</td>' +
            '     <td>操作</td>' +
            '     <td>创建者/修改者</td>' +
            ' </tr>' ;
    } else{
        html =
            ' <tr>' +
            '     <td>记录</td>' +
            '     <td>操作</td>' +
            '     <td>创建时间/修改时间</td>' +
            ' </tr>' ;
    }
    for (var a =0;a<record.length;a++) {
        var update = '';
        if(name == 'base') {
            update = handleNull(record[a].updateName) +' &nbsp;&nbsp; ' + new Date(record[a].updateTime).format('yyyy/MM/dd hh:mm:ss');
        } else{
            update = new Date(record[a].updateDate).format('yyyy/MM/dd hh:mm:ss');
        }
        if (record[a].dataState == '删除') {
            html +=
                ' <tr>' +
                '     <td><span class="colorOrange">'+ record[a].dataState +'</span></td>' +
                '     <td>--</td>' +
                '     <td>'+ update +'</td>' +
                ' </tr>';
        }else{
            html +=
                ' <tr>' +
                '     <td>'+ record[a].dataState +'</td>' +
                '     <td><span class="see-btn ty-color-blue" data-name="'+ name +'" data-id="'+ record[a].id +'" onclick="recordDetails($(this))">查看</span></td>' +
                '     <td>'+ update +'</td>' +
                ' </tr>';
        }
    }
    return html;
}
// creator: 李玉婷，2019-11-07 11:47:43，修改记录查看
function recordDetails (obj) {
    $(".seeMoreContact").remove();
    var name = obj.data('name');
    var id = obj.data('id');
    switch (name) {
        case 'base':
            $.ajax({
                "url": "/userInfo/getUserHistory.do",
                "data": {
                    'id': id
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var baseDetails = res['data']['user'];
                        var userContacts = res['data']['userContacts'];
                        $("#baseDetails [require]").each(function () {
                            var name = $(this).data('name');
                            switch (name) {
                                case 'gender':
                                    if (handleNull(baseDetails[name]) != '') {
                                        var value = chargeSex(baseDetails[name]);
                                        $(this).html(value);
                                    } else{
                                        $(this).html("");
                                    }
                                    break;
                                case 'marry':
                                    var value = chargeMarry(handleNull(baseDetails['marry']));
                                    $(this).html(value);
                                    break;
                                case 'degree':
                                    $(this).html(chargeDegree(baseDetails[name]));
                                    break;
                                case 'birthday':
                                    $(this).html(new Date(baseDetails[name]).format('yyyy/MM/dd'));
                                    break;
                                default:
                                    $(this).html(handleNull(baseDetails[name]));
                                    break;
                            }
                        });
                        $('#baseDetails .seeMoreContact').html('');
                        if (userContacts.length > 0) {
                            var html = '';
                            var typeArr = ['', '其它手机', 'QQ', 'Email', '微信', '微博'];
                            for (var t = 0; t < userContacts.length; t++) {
                                if (userContacts[t].type == '9') {
                                    html +=
                                        '<li class="seeMoreContact">' +
                                        '    <div class="ltTtl">' + userContacts[t].name + '</div>' +
                                        '    <span class="rtCon" title="' + userContacts[t].code + '">' + userContacts[t].code + '</span>' +
                                        '</li>';
                                } else {
                                    html +=
                                        '<li class="seeMoreContact">' +
                                        '    <div class="ltTtl">' + typeArr[Number(userContacts[t].type)] + '</div>' +
                                        '    <span class="rtCon" title="' + userContacts[t].code + '">' + userContacts[t].code + '</span>' +
                                        '</li>';
                                }
                            }
                            $('#baseDetails').append(html);
                        }
                        bounce_Fixed2.show($("#seeBaseInfor"));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $('#altMS').html('系统错误，请重试!');
                    bounce_Fixed2.show($('#alertMS'));
                }
            });
            break;
        case 'edu':
            $.ajax({
                "url": "/userInfo/getPersonalEducationHistory.do",
                "data": {
                    'id': id
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var details = res['data'];
                        $("#eduRecordSee [require]").each(function () {
                            var name = $(this).data('name');
                            switch (name) {
                                case 'beginTime':
                                case 'endTime':
                                    $(this).html(new Date(details[name]).format('yyyy/MM'));
                                    break;
                                default:
                                    $(this).html(handleNull(details[name]));
                                    break;
                            }
                        });
                        bounce_Fixed2.show($("#seeEduInfor"));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $('#altMS').html('系统错误，请重试!');
                    bounce_Fixed2.show($('#alertMS'));
                }
            });
            break;
        case 'work':
            $.ajax({
                "url": "/userInfo/getPersonnelOccupationHistory.do",
                "data": {
                    'id': id
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var details = res['data'];
                        $("#workRecordSee [require]").each(function () {
                            var name = $(this).data('name');
                            switch (name) {
                                case 'beginTime':
                                case 'endTime':
                                    $(this).html(new Date(details[name]).format('yyyy/MM'));
                                    break;
                                default:
                                    $(this).html(handleNull(details[name]));
                                    break;
                            }
                        });
                        bounce_Fixed2.show($("#seeWorkInfor"));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $('#altMS').html('系统错误，请重试!');
                    bounce_Fixed2.show($('#alertMS'));
                }
            });
            break;
    }
}
// creator: 李玉婷，2019-11-07 15:15:13，勾选
function checkInfoFinish (obj) {
    if(obj.data('checked') == '0'){
        obj.data('checked','1');
        obj.find('i').removeClass('fa-circle-o').addClass('fa-dot-circle-o');
    }else {
        obj.data('checked','0');
        obj.find('i').removeClass('fa-dot-circle-o').addClass('fa-circle-o');
    }
}
// creator: 李玉婷，2019-11-07 15:29:02，勾选确定
function checkFinishSure () {
    $.ajax({
        "url": "/userInfo/submitUserInfo.do",
        "data": {
            'id': sphdSocket.user.userID
        },
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                $(".checkOperation").remove();
                bounce.cancel();
            } else{
                layer.msg('获取失败！');
            }
        },
        error:function(){
            $('#altMS').html('系统错误，请重试!');
            bounce_Fixed2.show($('#alertMS'));
        }
    });
}
// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.parent().siblings(".textMax").text( curLength +'/' + max );
    obj.siblings(".textMax").text( curLength +'/' + max );
}
// creator: 李玉婷，2019-09-19 09:37:24，清除自定义标签
function clearLableText(obj) {
    obj.siblings().val("");
}

laydate.render({elem: '#personBirthday', format: 'yyyy/MM/dd'});
laydate.render({elem: '#workBegin', type: 'month', format: 'yyyy/MM'});
laydate.render({elem: '#workEnd', type: 'month', format: 'yyyy/MM'});
laydate.render({elem: '#studyBegin', type: 'month', format: 'yyyy/MM'});
laydate.render({elem: '#studyEnd', type: 'month', format: 'yyyy/MM'});
