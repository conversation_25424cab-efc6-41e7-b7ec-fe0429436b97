/* creator ： 侯杏哲 处理个人中心-我的请求-请假申请页面  */
var isfromMessage = 0 ;
$(function(){
    $.fn.select2.defaults.set("theme", "default");
    // creator： 侯杏哲 2017-05-20  处理从消息跳转过来的页面
    var applyID = GetUrlQuery("applyID") ; // 审批过程id
    if( applyID != undefined ){
        getDetail( applyID , "---" ) ;
        isfromMessage = 1 ;
    }
    // 进入页面，获取列表
    $(".ty-secondTab li").eq(0).click();
    $("#leaveType")     .select2({placeholder: "请选择请假类型",});
    $("#beginTime1")    .select2({placeholder: "起时间", data: getData("0:00",0)});
    $("#endTime1")      .select2({placeholder: "止时间"});
    $('#beginTime1').on('select2:select', function (evt) {
        if($("#beginDate1").val() === $("#endDate1").val()){
            $("#endTime1").html("<option></option>");
            $("#endTime1").select2({
                placeholder: "止时间",
                data: getData($(this).val(),1)
            })
        }else{
            $("#endTime1").html("<option></option>");
            $("#endTime1").select2({
                placeholder: "止时间",
                data: getData("0:00",1)
            })
        }
    });
});

function getData(start,delay) {

    var data = [];
    var a = start.split(":")[0];
    var b = start.split(":")[1];

    for(var i=a;i<24;i++){
        data.push({"id" : i + ':' + '00', "text" : i+':'+'00'});
        data.push({"id" : i + ':' + '30', "text" : i+':'+'30'});
    }
    if(b === "30"){
        data.shift();
    }
    if(delay>0){
        for(var j=0;j<delay;j++){
            data.shift();
        }
    }
    return data;

}
//  一级菜单切换
function overtime_request(num ) {
    var val = num;
    var url = "";
    if(Number(val) == 1 ){
        url = "myOvertime.do"; // 加班
    }
    if(Number(val) == 2 ){
        url = "myLeave.do"; //请假
    }
    if(Number(val) == 3 ){
        url = "myApplication.do"; // 财务
    }
    if( url == ""){ return false; }
    location.href = url;
}
// 二级菜单切换
function leavShow( num , obj  ){ //  num :1 待处理 2 已批准  3 已驳回
    obj.addClass('ty-active').siblings().removeClass('ty-active');
    $('#for_n').html(obj.html()) ;
    $("#tab_15_"+num).show().siblings().hide();
    getApplyList(num , 1 , 15 ) ;
}
// 获得请假申请列表
function getApplyList( type , pageNumber , quantum  ){ // type:状态 ； pageNumber：每页条数 ； quantum：总页数
    var n = (pageNumber - 1) * quantum ;
    $.ajax({
        url:"../lo/myLeaveListByLoginUser.do" ,
        data:{ "approvalStatus": type , "pageNumber":pageNumber , "quantum":quantum  } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            var list = data["personnelLeaveList"] ;
            var str = "" ;
            if(list && list.length>0){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<tr>" +
                        "<td>"+ ++n +"</td>" +
                        "<td>"+ list[i]["createName"] +"</td>" +
                        "<td>"+ list[i]["beginTime"] +"</td>" +
                        "<td>"+ list[i]["endTime"] +"</td>" +
                        "<td>"+ chargeType( list[i]["type"] ) +"</td>" +
                        "<td>" +
                        "<span class='ty-color-blue' onclick='getDetail("+ list[i]["id"] +", "+ n +" )'>查看</span></td>" +
                        "</td>" +
                        "</tr>" ;
                }
            }
            $("#tbl"+type).html(str);
            var cur = data["pageNumber"] , total = data["totalPage"] ;
            var jsonStr = JSON.stringify({"type": type }) ;
            setPage( $("#ye" + type) , cur , total , "myApply_leave" , jsonStr );
        },
        error:function(){
            $("#mt_tip_ms").html("获得列表失败") ;
            bounce.show($("#mtTip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    }) ;
}
// 判别请假类型
function chargeType( type ){
    if( type == "1" ){  return "事假";  }
    if( type == "2" ){  return "病假";  }
    if( type == "3" ){  return "年假";  }
    if( type == "4" ){  return "调休";  }
    if( type == "5" ){  return "婚假";  }
    if( type == "6" ){  return "产假";  }
    if( type == "7" ){  return "陪产假";  }
    if( type == "8" ){  return "路途假";  }
    if( type == "9" ){  return "其他";  }
    return "未识别" ;
}
// 获得某条详情
function getDetail( id , no){
    if(id){
        $.ajax({
            url:"../lo/myLeaveInfoById.do" ,
            data:{ "id" : id } ,
            type:"post" ,
            dataType:"json" ,
            beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
            success:function(data){
                var status = data["status"] ;
                if(status == 0 ){
                    $("#mt_tip_ms").html("获得该条记录详情失败") ;
                    bounce.show($("#mtTip")) ;
                }else{
                    var processList = data["processList"] ;
                    var info = data["personnelLeave"] ;
                    bounce.show($("#detail")) ;
                    var str = "" ;
                    if(processList && processList.length>0){
                        for(var i = 0 ; i < processList.length ; i++){
                            if( processList[i]["approveStatus"] == "1" ){ // 待审批
                                str += "<div class='ty-process-item ty-process-wait'>" +
                                    "<p><span class='dot-wait'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }else if( processList[i]["approveStatus"] == "3"){ // 已驳回
                                str += "<div class='ty-process-item ty-process-no '>" +
                                    "<p><span class='dot-no'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }else{  // 已批准
                                str += "<div class='ty-process-item'>" +
                                    "<p><span class='dot'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }
                        }
                    }
                    $("#process").html( str ) ;
                    if(info){
                        $("#no").html( no ) ;
                        $("#createName").html(info["createName"]) ;
                        $("#beginTime").html(info["beginTime"]) ;
                        $("#endTime").html(info["endTime"]) ;
                        $("#type").html( chargeType(info["type"]) ) ;
                        $("#memo").html(info["reason"]) ;
                    }else{
                        $("#no").html( no ) ;
                        $("#createName").html("获取失败") ;
                        $("#beginTime").html("获取失败") ;
                        $("#endTime").html("获取失败") ;
                        $("#type").html("获取失败") ;
                        $("#memo").html("获取失败") ;
                    }

                }
            } ,
            error:function(){
                $("#mt_tip_ms").html("获得该条记录详情失败,链接错误") ;
                bounce.show($("#mtTip")) ;
            } ,
            complete:function(){ chargeClose(1) ;  }
        });
    }else{
        $("#mt_tip_ms").html("获得该条ID失败") ;
        bounce.show($("#mtTip")) ;
    }
}
//点击新增请假按钮
function leaveApplyBtn() {
    $(".leaveApply input").val("");
    $(".leaveApply textarea").val("");
    $("#leaveType").select2().val("1").trigger("change");
    $("#beginTime1").select2().val("8:00").trigger("change");
    $(".endTime1").html("<option>止时间</option>");
    // $("#endTime1").select2({placeholder:"止时间",data: getData("8:30",0)});
    //$("#endTime1").select2().val("8:30").trigger("change");
    // $("#endTime1").select2().val("").trigger("change");
    bounce.show($(".leaveApply"))
}
//确定新增请假按钮
function sureLeaveApply() {
    start.max = '2099-06-16';
    end.max = '2099-06-16 23:59:59';
    end.min = ""; //开始日选好后，重置结束日的最小日期
    end.start = ""; //将结束日的初始值设定为开始日
    var beginDate1 = $("#beginDate1").val();
    var endDate1 = $("#endDate1").val();

    var beginTime1  = chargeTime($("#beginTime1").select2("val"));
    var endTime1    = chargeTime($("#endTime1").select2("val"));
    var type        = $("#leaveType").select2("val");
    var reason      = $(".leaveApply .reason").val();

    var leaveData = {
        "beginTime1": beginDate1 + " " + beginTime1 ,
        "endTime1"  : endDate1 + " " + endTime1 ,
        "type"      : type ,
        "reason"    : reason
    };
    if($.trim(type) === ""){
        $("#mtTips #mt_tips_ms").html("请选择请假类型");
        bounce_Fixed.show($("#mtTips"));
    }else if($.trim(beginDate1) === ""){
        $("#mtTips #mt_tips_ms").html("请选择计划开始日期");
        bounce_Fixed.show($("#mtTips"));
    }else if($.trim(beginTime1) === ""){
        $("#mtTips #mt_tips_ms").html("请选择计划开始时间");
        bounce_Fixed.show($("#mtTips"));
    }else if($.trim(endDate1) === ""){
        $("#mtTips #mt_tips_ms").html("请选择计划结束日期");
        bounce_Fixed.show($("#mtTips"));
    }else if($.trim(endTime1) === ""){
        $("#mtTips #mt_tips_ms").html("请选择计划结束时间");
        bounce_Fixed.show($("#mtTips"));
    }else {
        $.ajax({
            url: $.webRoot+"/lo/addLeave.do",
            data: leaveData,
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                var status = data["status"];
                if (status === 1) {
                    $("#mtTip #mt_tip_ms").html("申请成功！");
                    bounce.show($("#mtTip"));
                    $(".ty-secondTab li").eq(0).click();
                } else if (status === 0) {
                    $("#mtTips #mt_tips_ms").html("请选择类型！");
                    bounce_Fixed.show($("#mtTips"));
                } else {
                    alert("系统错误，请重试！");
                }
            },
            error: function (err) {
                alert("系统错误！")
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
}
// creator :  侯杏哲 2017-05-20  判断若来自消息的，刷新消息的条数
function chargeIsMs(){
    if( isfromMessage == 1 ){  isfromMessage = 0 ; msgNum();    }
}
function chargeTime(time) {
    var a = time.split(":")[0];
    var b = time.split(":")[1];
    if(parseInt(a).length<10){
        a = "0" + a
    }
    time = a + ":" + b + ":" + "00";
    return time;
}



var start = {
    elem: '#beginDate1', //选择ID为START的input
    format: 'yyyy-MM-dd', //自动生成的时间格式
    max: '2099-06-16', //最大日期
    istime: false, //必须填入时间
    istoday: true,  //是否是当天
    done: function(datas){
        end.min = datas; //开始日选好后，重置结束日的最小日期
        end.start = datas; //将结束日的初始值设定为开始日
        if($("#endDate1").val() === ""){
            $("#endTime1").prop("disabled", true);
        }else{
            $("#endTime1").prop("disabled", false);
            if($("#endDate1").val() === $("#beginDate1").val()){
                $("#endTime1").html("<option></option>");
                $("#endTime1").select2({
                    placeholder: "止时间",
                    data: getData($("#beginTime1").val(),1)
                })
            }else{
                $("#endTime1").html("<option></option>");
                $("#endTime1").select2({
                    placeholder: "止时间",
                    data: getData("0:00",0)
                })
            }
        }
    }/*,
    change: function(datas){
        end.min = datas; //开始日选好后，重置结束日的最小日期
        end.start = datas; //将结束日的初始值设定为开始日
        if($("#endDate1").val() === ""){
            alert(1)
            $("#endTime1").prop("disabled", true);
        }else{
            alert(2)
            $("#endTime1").prop("disabled", false);
            if($("#endDate1").val() === $("#beginDate1").val()){
                $("#endTime1").html("<option></option>");
                $("#endTime1").select2({
                    placeholder: "止时间",
                    data: getData($("#beginTime1").val(),1)
                })
            }else{
                $("#endTime1").html("<option></option>");
                $("#endTime1").select2({
                    placeholder: "止时间",
                    data: getData("0:00",0)
                })
            }
        }
    }*/
};

var end = {
    elem: '#endDate1',
    format: 'yyyy-MM-dd',
    max: '2099-06-16 23:59:59',
    istime: false,
    istoday: true,
    start: getCurDate(),
    done: function (value, datas) {
        start.max = datas; //结束日选好后，重置开始日的最大日期
        if ($("#beginDate1").val() === "") {
            $("#endTime1").prop("disabled", true);
        } else {
            $("#endTime1").prop("disabled", false);
            if ($("#endDate1").val() === $("#beginDate1").val()) {
                $("#endTime1").html("<option></option>");
                $("#endTime1").select2({
                    placeholder: "止时间",
                    data: getData($("#beginTime1").val(), 1)
                })
            } else {
                $("#endTime1").html("<option></option>");
                $("#endTime1").select2({
                    placeholder: "止时间",
                    data: getData("0:00", 0)
                })
            }
        }
    }/*,
    change: function (datas) {
        start.max = datas; //结束日选好后，重置开始日的最大日期
        // start.max = this.max;
        if ($("#beginDate1").val() === "") {
            alert(3)
            $("#endTime1").prop("disabled", true);
        } else {
            alert(4)
            $("#endTime1").prop("disabled", false);
            if ($("#endDate1").val() === $("#beginDate1").val()) {
                $("#endTime1").html("<option></option>");
                $("#endTime1").select2({
                    placeholder: "止时间",
                    data: getData($("#beginTime1").val(), 1)
                })
            } else {
                $("#endTime1").html("<option></option>");
                $("#endTime1").select2({
                    placeholder: "止时间",
                    data: getData("0:00", 0)
                })
            }
        }
    }*/
};

function goCancel(){
    start.max = '2099-06-16';
    end.max = '2099-06-16 23:59:59';
    end.min = ""; //开始日选好后，重置结束日的最小日期
    end.start = ""; //将结束日的初始值设定为开始日
    bounce.cancel();
}

laydate.render(start);
laydate.render(end);


