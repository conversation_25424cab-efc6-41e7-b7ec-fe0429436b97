/**
 * Created by Administrator on 2017/5/10.
 */
$(function () {
    /* creator：张旭博，2017-05-06 15:17:36，第一级目录加载 */
    //updator：王静，2017-08-02 15:17:36，第一级目录加载
    $.ajax({
        url: "../res/getInitialFolder.do",
        data: {"type": "2"},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success: function (data) {
            var listFirstCategory = data.data["listFirstFolder"];
            //var listNotice = data["listNotice"];
            //var listNoticeChild = data["listNoticeChild"];
            if(listFirstCategory.length == 0){
                $(".initialSect").show().siblings().hide();$(".uploadBtn").hide();
            }else{
                var firstLevelStr = "";
                firstLevelStr += '<div class="level1" level="1">';
                for(var i in listFirstCategory){
                    if(listFirstCategory[i].childStatus==1){
                        firstLevelStr += '<li><div class="ty-treeItem" id='+listFirstCategory[i].id+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listFirstCategory[i].name+'</span></div></li>'
                    }else{
                        firstLevelStr += '<li><div class="ty-treeItem" id='+listFirstCategory[i].id+'><i class="ty-fa"></i><i class="fa fa-folder"></i><span>'+listFirstCategory[i].name+'</span></div></li>'
                    }
                }
                firstLevelStr += '<p id="tstip"></p>'+
                    '</div>';
                $(".ty-colFileTree").append(firstLevelStr);
                //设置样式使第一个文件夹点击并保持闭合状态
                $(".ty-fileContent .ty-treeItem").eq(0).click();
                $(".ty-fileContent .ty-treeItem").eq(0).click();
            }
        },
        error:function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ chargeClose(1) ;  }
    });

    /* creator：张旭博，2017-05-06 15:16:54，每一集目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容） */
    /* updator：王静，2017-08-24 15:45::54，文件下含有子类别的，右侧页面(ty-fileContent) 为空*/
    var curid;
    $("#menuTree").on("click",".ty-treeItem",function(){
        //添加文件夹选中样式
        //添加文件夹选中样式
        $("#menuTree .ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //取消文件详细信息
        $(".ty-filePreview .ty-fileDetail").html("");
        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right");
            $(this).find("i").eq(0).addClass("fa-angle-down");
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down");
            $(this).find("i").eq(0).addClass("fa-angle-right");
        }
        //设置选中文件夹详细内容的头部名称
        $(".nowFolder").children("h3").html($(this).text());
        var categoryId = $(this).attr("id");
        curid=categoryId;
        var treeItemThis = $(this);
        $.ajax({
            url: "../res/getFolderAndChildFolder.do",
            data: {"categoryId":categoryId , "type" :2 },
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
            success: function (data) {
                if(data == ""||data == undefined){
                    $("#mt_tip_ms").html("连接失败，请刷新重试！");
                    bounce.show($("#mtTip"));
                }else{
                    //存储返回的值（此类别的信息和子集类别的信息）
                    var listNotice      = data.data["parentFolder"];
                    var listNoticeChild = data.data["childFolder"];
                    //var listResource    =data["listResource"];
                    // if(listNoticeChild.length > 0){
                    //     localStorage.setItem("noticemax",1);
                    // }else{
                    //     localStorage.setItem("noticemax",0);
                    // }
                    //拼接子类别侧边菜单字符串
                    var level = parseInt(treeItemThis.parent().parent().attr("level"));
                    var nextLevel = level + 1;
                    var levelStr = "";
                    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">';
                    for(var i in listNoticeChild){
                        //if(listNoticeChild[i]["childStatus"] == 1){
                        if(listNoticeChild[i]["childStatus"] == 1){
                            levelStr += '<li><div class="ty-treeItem" id='+listNoticeChild[i].id+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i].name+'</span>' +
                                '</div></li>'
                        }else{
                            levelStr += '<li><div class="ty-treeItem" id='+listNoticeChild[i].id+'><i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i].name+'</span>' +
                                '</div></li>'
                        }
                    }
                    levelStr += '</div>';
                    if(treeItemThis.children("i:first").hasClass("fa-angle-down")){
                        treeItemThis.next().remove();
                        treeItemThis.after(levelStr);
                    }else{
                        treeItemThis.next().remove();
                    }

                    //creator：王静 2017-08-25 8:33:34 移动弹框显示时，点击移动弹框中的文件，主页面不随着改变
                    if($(".bounce-RemoveFile").css("display")=="none"){
                        if(listNoticeChild.length>0){
                            $(".uploadBtn").addClass("hd");//上传文件按钮隐藏
                            $(".mar").addClass("hd");
                        }else{
                            $(".uploadBtn").removeClass("hd");//上传文件按钮显示
                            $(".mar").removeClass("hd");
                            getFile( 1,20, categoryId);
                        }
                        // if(listNoticeChild.length==0){
                        //     getFile( 1,20, categoryId);
                        //    // $(".ty-fileContent").removeClass("hd");
                        // }else{
                        //    // $(".ty-fileContent").addClass("hd");
                        // }
                    }else{
                        $("#tstip").html("");
                        $("#removeFilesure").removeClass("ty-btn-gray").addClass("ty-btn-blue").attr("disabled",false);
                    }
                }
            },
            error:function (err) {
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            } ,
            complete:function(){ chargeClose( 2 ) ;  }
        })
    });
    /*creator:lyt date:2017-12-14 9:30 移动弹窗中的文件树*/
    $("#removeTree").on("click",".ty-treeItem",function(){
        //添加文件夹选中样式
        $("#removeTree .ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //取消文件详细信息
        $("#menuTree .ty-fileDetail").html("");
        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right");
            $(this).find("i").eq(0).addClass("fa-angle-down");
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down");
            $(this).find("i").eq(0).addClass("fa-angle-right");
        }
        var categoryId = $(this).attr("id");
        //curid=categoryId;
        var treeItemThis = $(this);
        $.ajax({
            url: "../res/getFolderAndChildFolder.do",
            data: {"categoryId":categoryId , "type" :2 },
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
            success: function (data) {
                if(data == ""||data == undefined){
                    $("#mt_tip_ms").html("连接失败，请刷新重试！");
                    bounce.show($("#mtTip"));
                }else{
                    //存储返回的值（此类别的信息和子集类别的信息）
                    //var listNotice      = data.data["parentFolder"];
                    var listNoticeChild = data.data["childFolder"];
                    //var listResource    =data["listResource"];
                    if(listNoticeChild.length > 0){
                        localStorage.setItem("noticemax",1);
                    }else{
                        localStorage.setItem("noticemax",0);
                    }
                    //拼接子类别侧边菜单字符串
                    var level = parseInt(treeItemThis.parent().parent().attr("level"));
                    var nextLevel = level + 1;
                    var levelStr = "";
                    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">';
                    for(var i in listNoticeChild){
                        //if(listNoticeChild[i]["childStatus"] == 1){
                        if(listNoticeChild[i]["childStatus"] == 1){
                            levelStr += '<li><div class="ty-treeItem" id='+listNoticeChild[i].id+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i].name+'</span>' +
                                '</div></li>'
                        }else{
                            levelStr += '<li><div class="ty-treeItem" id='+listNoticeChild[i].id+'><i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i].name+'</span>' +
                                '</div></li>'
                        }
                    }
                    levelStr += '</div>';
                    if(treeItemThis.children("i:first").hasClass("fa-angle-down")){
                        treeItemThis.next().remove();
                        treeItemThis.after(levelStr);
                    }else{
                        treeItemThis.next().remove();
                    }
                    $("#tstip").html("");
                    $("#removeFilesure").removeClass("ty-btn-gray").addClass("ty-btn-blue").attr("disabled",false);

                }
            },
            error:function (err) {
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            } ,
            complete:function(){ chargeClose( 2 ) ;  }
        })
    });
    /* creator：张旭博，2017-05-06 15:17:01，每一个文件绑定事件（添加样式、获取文件详细信息、） */
    $(".ty-fileList").on("click",".ty-fileItem",function (){
        $(".ty-fileItem").removeClass("ty-fileItemActive");
        $(this).addClass("ty-fileItemActive");
        var fileId= $(this).attr("id");
        localStorage.setItem("fileid",fileId);
    });
    /* creator：张旭博，2017-05-06 15:19:56，上传删除按钮绑定事件 */
    $('.fileUpload').on('click','.delfilebtn',function(){
        $(".fileType").addClass("hd");
        $(".tips").show();
        $(".uploadify-button").show();
    });

});

//updator:王静 2017-08-02 10:00:00 获取文件夹下面的文件列表
function getFile(curpage,pernum,categoryId){
    $.ajax({
        url: "../res/getFile.do",
        data: {"categoryId":categoryId , "currentPageNo":curpage ,"pageSize":pernum,"type":5},
        type: "post",
        dataType: "json",
        success: function (data) {
            //设置分页
            var cur=data.data["pageInfo"].currentPageNo;
            var total=data.data["pageInfo"].totalPage;
            var jsonStr=JSON.stringify( { "categoryId" : categoryId});
            setPage( $("#ye_con"), cur, total, "fileMessage",jsonStr);

            var fileInfo = data.data["list"];
            if(fileInfo == ""||fileInfo == undefined){
                $(".uploadBtn").removeClass("hd");
                $("#ye_con").addClass("hd");
                $(".ty-fileList").html('<div class="ty-fileNull"><h3>暂无文件</h3></div>');
            }else{
                $("#ye_con").removeClass("hd");
                var itemStr = '';
                for(var i in fileInfo){
                    var id          = fileInfo[i].id,
                        name        = fileInfo[i].name,
                        size        = fileInfo[i].size,
                        path        = fileInfo[i].path,
                        fileSn      = fileInfo[i].fileSn,
                        changeNum   = fileInfo[i].changeNum,
                        fileType    = fileInfo[i].version,
                        updateName  = fileInfo[i].updateName,
                        createName  = fileInfo[i].createName,
                        updateDate  = fileInfo[i].updateDate,
                        createDate  = fileInfo[i].createDate,
                        updator     = fileInfo[i].updator,
                        creator     = fileInfo[i].creator,
                        loginUser   = $("#loginUser").html(),
                        recordDisabledClass = '';
                    size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size = parseFloat(size/1048576).toFixed(2) + 'MB';

                    updateName  === null? updateName = createName:updateName;
                    updateDate  === null? updateDate = createDate:updateDate;
                    updator     === null? updator    = creator:updateDate;

                    changeNum   === 0   ? recordDisabledClass = 'ty-disabled': recordDisabledClass = ''; //换版记录的禁用 （换版为0 换版记录按钮禁用）

                    var srr = '{"parentId":"'+fileInfo[i].category+'","fileName":"'+fileInfo[i].name+'","fileSn":"' + fileInfo[i]["fileSn"] + '","changeNum":"'+fileInfo[i]["changeNum"]+
                                '","fileId":"'+fileInfo[i].id +'"}';
                    localStorage.setItem("createname",createName);

                    itemStr +=  '<div class="ty-fileItem ty-table-control" id="' + id + '">'+
                                    '<div class="ty-left ty-fileType ty-file_'+fileType+'"></div>'+
                                    '<div class="rr hd">'+srr+'</div>'+
                                    '<div class="ty-fileInfo ty-left">'+
                                        '<div class="ty-fileName" title="' + name + '-G' + changeNum + '">' + name + '</div>'+
                                        '<div class="ty-fileVersion">G' + changeNum + '</div>'+
                                        '<div class="ty-fileNo">编号：' + fileSn + '</div>'+
                                        '<div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; '+ size + ' -- '+ updateDate+'</div>' +
                                    '</div>'+
                                    '<div class="ty-fileHandle ty-right">'+
                                        '<a class="ty-left" path="' + path + '" onclick="return getDownLoad($(this));" target="_blank" download="'+name+'.'+fileType+'">下载</a>' +
                                        '<a class="ty-left" onclick="changeVersion($(this))">换版</a>'+
                                        '<a class="ty-left" path="' + path + '" onclick="seeOnline($(this))">在线预览</a>'+
                                        '<a class="ty-left cz_handle" onclick="yj(this,'+i+',event)">更多'+
                                            '<ul class="hel" id="handleul'+i+'" style="display: none">'+
                                                '<li class="ulli '+recordDisabledClass+'" onclick="chargeRecord($(this))" ><span>换版记录</span></li>'+
                                                '<li class="ulli" onclick="removeFile($(this))"><span>移动</span></li>'+
                                                '<li class="ulli" onclick="deleteFile($(this))"><span>删除</span></li>'+
                                                '<li class="ulli" onclick="basicMessage($(this))"><span>基本信息</span></li>'+
                                            '</ul>'+
                                        '</a>'+
                                    '</div>'+
                                '</div>';
                }
                $(".ty-fileList").html(itemStr);
            }
        },
        error:function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        }
    })
}
/* creator：张旭博，2017-05-06 15:11:22，点击文件上传 按钮*/
/* updator：王静，2017-08-02 15:11:22，点击文件上传 按钮*/
function uploadNewFile() {
    var folderName = $("#menuTree .ty-treeItemActive>span").text();// 当前选中的类别名称
    $(".bounce-uploadFile input").val("");               // 清空所有输入框
    $(".bounce-uploadFile #folderName").val(folderName); // 在类别中填入当前类别名称
    $(".bounce-uploadFile ._path").html("");             // 清空文件路径内容
    $(".bounce-uploadFile ._size").html("");             // 清空文件大小内容
    $(".bounce-uploadFile ._type").html("");             // 清空文件类型内容
    $(".fileUpload").html('<div class="fileType ty-fileDoc hd"></div><span class="tips">请选择文件上传</span>');
    //初始化文件上传插件
    $('.fileUpload').Huploadify({
        auto:true,
        fileTypeExts:'*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.png;*.jpg;*.wps;*.et;*.md;',
        multi:false,
        fileSizeLimit:40960,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        formData:{
            module: '文件与资料',
            userId: sphdSocket.user.userID
            // groupUuid: sphdSocket.uuid()
        },
        uploader:"../uploads/uploadfyByFile.do",
        onUploadStart:function(file,data){
            // console.log('上传开始：')
            // console.log(file)
            // console.log(data)
        },
        onInit:function(){},
        /* creator：张旭博，2017-05-06 15:15:49，选择文件后执行的方法 */
        onSelect:function (file) {
            $(".fileType").removeClass("hd");
            $(".tips").hide();
            $(".uploadify-button").hide();
            var fileType = file.name.split(".");
            fileType = fileType[fileType.length-1];
            switch (fileType){
                //.ty-file_xls, .ty-file_xlsx, .ty-file_et
                case "doc":case "docx":
                $(".fileType").removeClass().addClass("fileType ty-file_doc");
                break;
                case "xls":case "xlsx":case "et":
                $(".fileType").removeClass().addClass("fileType ty-file_xls");
                break;
                case "ppt":
                    $(".fileType").removeClass().addClass("fileType ty-file_ppt");
                    break;
                case "rar":case "zip":
                $(".fileType").removeClass().addClass("fileType ty-file_rar");
                break;
                case "pdf":
                    $(".fileType").removeClass().addClass("fileType ty-file_xls");
                    break;
                case "png":case "jpg":case "gif":
                $(".fileType").removeClass().addClass("fileType ty-file_jpg");
                break;
                default:
                    $(".fileType").removeClass().addClass("fileType ty-file_other");
            }
        },
        /* creator：张旭博，2017-05-06 15:15:37，上传文件完成执行的方法 */
        onUploadComplete:function(file,data){
            $(".uploadbtn").html("上传成功！").css("display","block");

        },
        /* creator：王静，2017-08-02 15:15:37，上传文件失败执行的方法 */
        onUploadError:function(){
            $(".uploadbtn").html("上传失败！").css("display","block");
        },
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file,json){
            console.log("onUploadSuccess");
            $(".uploadbtn").html("上传成功！");
            var data = JSON.parse(json);
            var name =data.filename;
            var fileUid =data.fileUid;
            var groupUuid =data.groupUuid;
            if(groupUuid){
                $("#uploadFileBtn").data("info",{"type":'groupUuid', 'groupUuid':groupUuid})
            } else {
                $("#uploadFileBtn").data("info",{"type":'fileId', 'fileId':fileUid})
            }

            var type = data.lastName;
            var size = file.size;
            var str =   '<div class="hd">'+
                '<div class="_path">'+name+'</div>'+
                '<div class="_size">'+size+'</div>'+
                '<div class="_type">'+type+'</div>'+
                '</div>';
            $(".bounce-uploadFile>.bonceFoot").append(str);
        },
        onCancel:function(file){
        }
    });
    bounce.show($(".bounce-uploadFile"));                // 显示上传弹窗
    $("#filesure").removeClass("ty-btn-blue").addClass("ty-btn-gray");
    bounce.everyTime('0.2s','uploadNewFile',function(){
        var i = 0;
        if($.trim($("#fileName").val()) === "" ||$.trim( $(".bounce-uploadFile ._path").text()) === ""){i++}
        if(i === 0){
            $("#uploadFileBtn").removeAttr("disabled")
        }else{
            $("#uploadFileBtn").attr("disabled","disabled")
        }
    });
}
// /* creator：张旭博，2017-05-06 15:12:12，文件上传点击确定按钮 */
// /* updator：lyt，2017-11-30 15:35:12 */
function sureUploadNewFile(){
    //获取表单内容和接口需要的参数
    var id                  = parseInt($("#menuTree .ty-treeItemActive").attr("id"));
    var fileName            = $.trim($("#fileName").val());
    var fileNo              = $.trim($("#fileNo").val());
    var path                = $(".bounce-uploadFile ._path").text();
    var size                = parseInt($(".bounce-uploadFile ._size").text());
    var type                = $(".bounce-uploadFile ._type").text();
    // var fileMakeDate        = $.trim($("#fileMakeDate").val());
    //参数json
    var saveFileData = {
        "category":id,
        "name" : fileName,
        "fileSn" : fileNo,
        "version":type,
        "path" : path,
        "changeNum":0,
        "size":size,
        "module":'文件与资料',
        "approveStatus":2
    };
    //点击确定按钮之前的判断
    if(type=="" && size=="" || path==""){
        $("#mt_tip_ms").html("请先上传文件");
        bounce.show($("#mtTip"));
    }else if(fileName==""){
        // alert("文件名称不能为空！");
    } else{
        $.ajax({
            url: "../res/resManageAffridFile.do",
            data: saveFileData,
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                // option:格式{'type':'fileId', 'fileId':'sdfsdafsdfsdfsd' }
                // 或者 {'type':'groupUuid', 'groupUuid':  'ssdfsdfsadf'}
                var info = $("#uploadFileBtn").data("info")
                cancelFileDel(info)
                var status = data["success"];
                if(status == 0){
                    $("#mtTip #mt_tip_ms").html("文件编号重复，请重新填写文件编号!")
                    bounce.show($("#mtTip"));
                }else if(status == 1){
                    $("#mt_tip_ms").html("上传成功！");
                    bounce.show($("#mtTip"));
                    $(".Upload input").val("");
                    $("#menuTree .ty-treeItemActive").click();
                }else{
                    $("#mt_tip_ms").html("连接失败，请刷新重试！");
                    bounce.show($("#mtTip"));
                }
            },
            error:function (err) {
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
}

//update:李玉婷 date:2017/12/1 13:20
/* creator：张旭博，2017-05-06 15:10:52，点击换版按钮 */
function changeVersion(obj) {
    $(".bounce-CVuploadFile ._path").html("");             // 清空文件路径内容
    $(".bounce-CVuploadFile ._size").html("");             // 清空文件大小内容
    $(".bounce-CVuploadFile ._type").html("");             // 清空文件类型内容

    $(".bounce-CVuploadFile input").val("");
    $(".bounce-CVuploadFile textarea").val("");
    var category = $("#menuTree .ty-treeItemActive>span").text();
    var prama = obj.parent().siblings(".rr").text();
    $(".updateFile").html(prama);
    prama=JSON.parse(prama);
    var name = prama.fileName;
    var fileSn = prama.fileSn;
    $("#CV-folderName").val(category);
    $("#CV-fileName").val(name);
    $("#CV-fileNo").val(fileSn);
    $(".CV-fileUpload").html('<div class="fileType ty-fileDoc hd"></div><span class="tips">请选择文件上传</span>');
    /* creator：张旭博，2017-05-06 15:17:15，绑定换版文件上传插件 */
    /* updator：王静，2017-08-02 11:20:15，绑定换版文件上传插件 */
    $('.CV-fileUpload').Huploadify({
        auto:true,
        fileTypeExts:'*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.png;*.jpg;*.wps;*.et;*.md;',
        multi:false,
        formData:{
            module: '文件与资料',
            userId: sphdSocket.user.userID
            // groupUuid: sphdSocket.uuid()
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        uploader:"../uploads/uploadfyByFile.do",
        onUploadStart:function(){},
        onInit:function(){},
        /* creator：张旭博，2017-05-06 15:15:49，选择文件后执行的方法 */
        onSelect:function (file) {
            $(".CV-fileUpload .fileType").removeClass("hd");
            $(".CV-fileUpload .tips").hide();
            $(".CV-fileUpload .uploadify-button").hide();
            var fileType = file.name.split(".");
            fileType = fileType[fileType.length-1];
            switch (fileType){
                case "doc":case "docx":
                $(".CV-fileUpload .fileType").removeClass().addClass("fileType ty-fileDoc");
                break;
                case "xls":case "xlsx":case "et":
                $(".CV-fileUpload .fileType").removeClass().addClass("fileType ty-fileXls");
                break;
                case "ppt":
                    $(".CV-fileUpload .fileType").removeClass().addClass("fileType ty-filePpt");
                    break;
                case "rar":case "zip":
                $(".CV-fileUpload .fileType").removeClass().addClass("fileType ty-fileRar");
                break;
                case "pdf":
                    $(".CV-fileUpload .fileType").removeClass().addClass("fileType ty-fileXls");
                    break;
                case "png":case "jpg":case "gif":
                $(".CV-fileUpload .fileType").removeClass().addClass("fileType ty-fileJpg");
                break;
                default:
                    $(".CV-fileUpload .fileType").removeClass().addClass("fileType ty-fileOther");
            }
        },
        /* creator：张旭博，2017-05-06 15:15:37，上传文件完成执行的方法 */
        onUploadComplete:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传成功！").css("display","block");
        },
        /* creator：王静，2017-08-02 15:16:37，上传文件失败执行的方法 */
        onUploadError:function(file,data){
            $(".CV-fileUpload .uploadbtn").html("上传失败！").css("display","block");
        },
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file,json){
            console.log("onUploadSuccess");
            $(".CV-fileUpload .uploadbtn").html("上传成功！");
            var data = JSON.parse(json);
            var name =data.filename;
            var type = data.lastName;
            var size = file.size;
            var fileUid =data.fileUid;
            var groupUuid =data.groupUuid;
            if(groupUuid){
                $("#CVuploadFileBtn").data("info",{"type":'groupUuid', 'groupUuid':groupUuid})
            } else {
                $("#CVuploadFileBtn").data("info",{"type":'fileId', 'fileId':fileUid})
            }

            var str =   '<div class="hd">'+
                '<div class="_path">'+name+'</div>'+
                '<div class="_size">'+size+'</div>'+
                '<div class="_type">'+type+'</div>'+
                '</div>';
            $(".bounce-CVuploadFile>.bonceFoot").append(str);
        },
        onCancel:function(file){
            // console.log(file.name);
        }
    });
    bounce.show($(".bounce-CVuploadFile"));
    bounce.everyTime('0.2s','CVuploadFile',function(){
        var i = 0;
        if($.trim($(".bounce-CVuploadFile ._path").text()) === ""){i++}
        if(i === 0){
            $("#CVuploadFileBtn").removeAttr("disabled")
        }else{
            $("#CVuploadFileBtn").attr("disabled","disabled")
        }
    });
}
/* creator：张旭博，2017-05-06 15:11:35，换版弹窗点击确定按钮 */
function sureChangeVersion(){
    var rrJson = $(".updateFile").html();
    rrJson = JSON.parse(rrJson);
    var categoryId = rrJson.parentId;
    var fileId     = rrJson.fileId;
    //var content  = $.trim($("#CV-instruction").val());
    //var reason = $.trim($("#CV-reason").val());
    var path = $.trim($(".bounce-CVuploadFile ._path").text());
    var size = parseInt($(".bounce-CVuploadFile ._size").text());
    var type = $(".bounce-CVuploadFile ._type").text();

    var saveFileData = {
        "id":fileId,
        "category":categoryId,
        "path" : path,
        "size" : size,
        "version" : type,
        "module" : '文件与资料',
        "approveStatus":2
    };
    if(type == ""||path == ""||size == ""){
        // alert("请先上传文件！")
    }else{
        $.ajax({
            url: "../res/updateFileVersion.do",
            data: saveFileData,
            type: "post",
            dataType: "json",
            beforeSend:function(){ loading.open() ;  } ,
            success: function (data) {
                var status = data["success"];
                if(status == 1){
                    $("#mt_tip_ms").html("换版成功！");
                    bounce.show($("#mtTip"));
                    $("#menuTree .ty-treeItemActive").click();
                }else{
                    $("#mt_tip_ms").html("系统错误，请重试！");
                    bounce.show($("#mtTip"));
                }
            },
            error:function (err) {
                $("#mt_tip_ms").html("系统错误，请重试！");
                bounce.show($("#mtTip"));
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
}
//create:lyt date:2017/12/6 16:10 删除文件
function deleteFile(obj){
    var fileId = obj.parents(".ty-fileItem").attr("id");
     $("#deleteId").html(fileId);
    bounce.show($(".bounce-deleteFile"));
}

//create:lyt date:2017/12/6 16:10 删除文件确定
function deleteFileSure(obj){
    id = $("#deleteId").html();
    $.ajax({
        url: "../res/delFile.do",
        data: {"fileId":id},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (data) {
            var status = data["success"];
            if(status == 1){
                bounce.cancel();
                $("#menuTree .ty-treeItemActive").click();
                $("#mt_tip_ms").html("删除成功！");
                bounce.show($("#mtTip"));
            }else{
                $("#mt_tip_ms").html("系统错误，请重试！");
                bounce.show($("#mtTip"));
            }
        },
        error:function (err) {
            $("#mt_tip_ms").html("系统错误，请重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    })
}

// $(function () {
//     /* creator：张旭博，2017-05-06 15:17:36，第一级目录加载 */
//     //updator：王静，2017-08-02 15:17:36，第一级目录加载
//     $.ajax({
//         url: "../res/resCategoryType.do",
//         data: {},
//         type: "post",
//         dataType: "json",
//         beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
//         success: function (data) {
//             var listFirstCategory = data["listFirstCategory"];
//             var listNotice = data["listNotice"];
//             var listNoticeChild = data["listNoticeChild"];
//             var firstLevelStr = "";
//             firstLevelStr += '<div class="level1" level="1">';
//             for(var i in listFirstCategory){
//                 if(listFirstCategory[i].maxChildCategorys > 0){
//                     firstLevelStr += '<li><div class="ty-treeItem" id='+listFirstCategory[i].id+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listFirstCategory[i].name+'</span></div></li>'
//                 }else{
//                     firstLevelStr += '<li><div class="ty-treeItem" id='+listFirstCategory[i].id+'><i class="ty-fa"></i><i class="fa fa-folder"></i><span>'+listFirstCategory[i].name+'</span></div></li>'
//                 }
//             }
//             firstLevelStr += '<p id="tstip"></p>'+
//                 '</div>';
//             $(".ty-colFileTree").append(firstLevelStr);
//             //设置样式使第一个文件夹点击并保持闭合状态
//             $("#"+listFirstCategory[0].id).click();
//             $("#"+listFirstCategory[0].id).click();
//         },
//         error:function (err) {
//             $("#mt_tip_ms").html("连接失败，请刷新重试！");
//             bounce.show($("#mtTip"));
//         } ,
//         complete:function(){ chargeClose(1) ;  }
//     });
//
//     /* creator：张旭博，2017-05-06 15:16:54，每一集目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容） */
//     /* updator：王静，2017-08-24 15:45::54，文件下含有子类别的，右侧页面(ty-fileContent) 为空*/
//     var curid;
//     $(".ty-colFileTree").on("click",".ty-treeItem",function(){
//         //添加文件夹选中样式
//         $(".ty-treeItem").removeClass("ty-treeItemActive");
//         $(this).addClass("ty-treeItemActive");
//         //取消文件详细信息
//         $(".ty-filePreview .ty-fileDetail").html("");
//         //点击文件夹箭头方向的切换
//         if($(this).find("i").eq(0).hasClass("fa-angle-right")){
//             $(this).find("i").eq(0).removeClass("fa-angle-right");
//             $(this).find("i").eq(0).addClass("fa-angle-down");
//         }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
//             $(this).find("i").eq(0).removeClass("fa-angle-down");
//             $(this).find("i").eq(0).addClass("fa-angle-right");
//         }
//         //设置选中文件夹详细内容的头部名称
//         $(".nowFolder").children("h3").html($(this).text());
//         var categoryId = $(this).attr("id");
//         curid=categoryId;
//         var treeItemThis = $(this);
//         $.ajax({
//             url: "../res/getCategoryMessage.do",
//             data: {"categoryId":categoryId},
//             type: "post",
//             dataType: "json",
//             beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
//             success: function (data) {
//                 if(data == ""||data == undefined){
//                     $("#mt_tip_ms").html("连接失败，请刷新重试！");
//                     bounce.show($("#mtTip"));
//                 }else{
//                     //存储返回的值（此类别的信息和子集类别的信息）
//                     var listNotice      = data["listNotice"];
//                     var listNoticeChild = data["listNoticeChild"];
//                     var listResource    =data["listResource"];
//                     localStorage.setItem("noticemax",listNotice[0].maxChildCategorys);
//                     //拼接子类别侧边菜单字符串
//                     var level = parseInt(treeItemThis.parent().parent().attr("level"));
//                     var nextLevel = level + 1;
//                     var levelStr = "";
//                     levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">';
//                     for(var i in listNoticeChild){
//                         if(listNoticeChild[i].maxChildCategorys > 0){
//                             levelStr += '<li><div class="ty-treeItem" id='+listNoticeChild[i].id+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i].name+'</span>' +
//                                 '</div></li>'
//                         }else{
//                             levelStr += '<li><div class="ty-treeItem" id='+listNoticeChild[i].id+'><i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i].name+'</span>' +
//                                 '</div></li>'
//                         }
//                     }
//                     levelStr += '</div>';
//                     if(treeItemThis.children("i:first").hasClass("fa-angle-down")){
//                         treeItemThis.next().remove();
//                         treeItemThis.after(levelStr);
//                     }else{
//                         treeItemThis.next().remove();
//                     }
//
//                     //creator：王静 2017-08-25 8:33:34 移动弹框显示时，点击移动弹框中的文件，主页面不随着改变
//                     if($(".bounce-RemoveFile").css("display")=="none"){
//                         if(parseInt(listNotice[0].maxChildCategorys)>0){
//                             $(".uploadBtn").addClass("hd");//上传文件按钮隐藏
//                         }else{
//                             getFile( 1,20, categoryId);
//                         }
//                         if(listNoticeChild.length==0){
//                             getFile( 1,20, categoryId);
//                             $(".ty-fileContent").removeClass("hd");
//                         }else{
//                             $(".ty-fileContent").addClass("hd");
//                         }
//                     }else{
//                         $("#tstip").html("");
//                         $("#removeFilesure").removeClass("ty-btn-gray").addClass("ty-btn-blue").attr("disabled",false);
//                     }
//
//                 }
//             },
//             error:function (err) {
//                 $("#mt_tip_ms").html("连接失败，请刷新重试！");
//                 bounce.show($("#mtTip"));
//             } ,
//             complete:function(){ chargeClose( 2 ) ;  }
//         })
//     });
//
//     /* creator：张旭博，2017-05-06 15:17:01，每一个文件绑定事件（添加样式、获取文件详细信息、） */
//     $(".ty-fileList").on("click",".ty-fileItem",function (){
//         $(".ty-fileItem").removeClass("ty-fileItemActive");
//         $(this).addClass("ty-fileItemActive");
//         var fileId= $(this).attr("id");
//         localStorage.setItem("fileid",fileId);
//     });
//     /* creator：张旭博，2017-05-06 15:19:56，上传删除按钮绑定事件 */
//     $('.fileUpload').on('click','.delfilebtn',function(){
//         $(".fileType").addClass("hd");
//         $(".tips").show();
//         $(".uploadify-button").show();
//     });
//
// });
// //updator:王静 2017-08-02 10:00:00 获取文件夹下面的文件列表
// function getFile(curpage,pernum,categoryId){
//     $.ajax({
//         url: "../res/getResource.do",
//         data: {"categoryId":categoryId , "curpage":curpage ,"pernum":pernum},
//         type: "post",
//         dataType: "json",
//         success: function (data) {
//             $(".ty-fileList").html("");
//             var userType = parseInt($("#loginUserType").text());
//             var cur=data["curpage"];
//             var total=data["countpage"];
//             var jsonStr=JSON.stringify( { "categoryId" : categoryId});
//             setPage( $("#ye_con"), cur, total, "fileMessage",jsonStr);
//             if(userType == 3 || userType == 10){
//                 $(".uploadBtn").removeClass("hd");
//             }else{
//                 $(".uploadBtn").addClass("hd");
//             }
//             var list = data["list"];
//             var itemStr = "";
//             if(list == ""||list == undefined){
//                 itemStr =   '<div class="ty-fileNull">'+
//                     '<h3>暂无文件</h3>'+
//                     //                                                '<p>先上传一个新文件吧！</p>'+
//                     '</div>';
//                 $(".ty-fileList").append(itemStr);
//             }else{
//                 for(var i in list){
//                     var fileIconStr = "";
//                     var fileType = list[i].type;
//                     switch (fileType){
//                         case "doc":case "docx":
//                         fileIconStr = '<div class="ty-fileType ty-fileDoc ty-left"></div>';
//                         break;
//                         case "xls":case "xlsx":case "et":
//                         fileIconStr = '<div class="ty-fileType ty-fileXls ty-left"></div>';
//                         break;
//                         case "ppt":
//                             fileIconStr = '<div class="ty-fileType ty-filePpt ty-left"></div>';
//                             break;
//                         case "rar":case "zip":
//                         fileIconStr = '<div class="ty-fileType ty-fileRar ty-left"></div>';
//                         break;
//                         case "pdf":
//                             fileIconStr = '<div class="ty-fileType ty-filePdf ty-left"></div>';
//                             break;
//                         case "png":case "jpg":case "gif":
//                         fileIconStr = '<div class="ty-fileType ty-fileJpg ty-left"></div>';
//                         break;
//                         default:
//                             fileIconStr = '<div class="ty-fileType ty-fileOther ty-left"></div>';
//                     }
//                     var size = list[i].size;
//                     var sizeStr = "";
//                     if(size<102400){
//                         sizeStr = parseFloat(size/1024).toFixed(2) + 'KB';
//                     }else{
//                         sizeStr = parseFloat(size/1048576).toFixed(2) + 'MB';
//                     }
//                     itemStr +=  '<div class="ty-fileItem ty-table-control" id="'+list[i].id+'">'+fileIconStr+
//                         '<div class="ty-fileInfo ty-left">'+
//                         '<div class="ty-fileName" title="'+list[i].name+ '-G'+list[i]["changeNum"]+'">'+list[i].name +'</div>'+
//                         '<div class="ty-fileVersion">'+'G'+list[i]["changeNum"]+'</div>'+
//                         '<div class="ty-fileNo">编号 ：'+list[i]["fileSn"]+'</div>';
//                     if(list[i]["updateName"]==null || list[i]["updateName"]==""){
//                         itemStr += '<div class="ty-fileDetail">'+list[i]["createName"] ;
//                     }else{
//                         itemStr += '<div class="ty-fileDetail">'+list[i]["updateName"] ;
//                     }
//                     if(list[i]["updateDate"] === undefined || list[i]["updateDate"] === null){
//                         var dateStr = list[i].createDate.substring(0,19);
//                     }else{
//                         var dateStr = list[i].updateDate.substring(0,19);
//                     }
//                     localStorage.setItem("createname",list[i]["createName"]);
//                     itemStr += '<span style="margin-left:15px;"></span>'+
//                         sizeStr+' -- '+ dateStr+'</div>'+
//                         '</div>'+
//                         '<div class="ty-fileHandle ty-right">';
//                     itemStr +=
//                         '<span class="ty-color-blue ty-left cz_handle" onclick=\"yj(this,'+i+',event)\" style="position:relative;">操作</span>'+
//                         '<ul class="hel" id=\"handleul'+i+'\" style="display: none">'+
//                         '<li class="ty-left ulli"><a target="_blank" href="/upload/'+list[i].path+'" download="'+list[i].name+'.'+list[i].type+'">下载</a></li>'+
//                         '<li class="ty-left ulli" onclick="changeVersion($(this))"><span>换版</span></li>'+
//                         '<li class="ty-left ulli" onclick="removeFile($(this))"><span>移动</span></li>'+
//                         '<li class="ty-left ulli"><a path="'+ list[i].path+'" onclick="seeOnline($(this))">在线预览</a></li>'+
//                         '</ul>';
//                     if(list[i]["changeNum"]==0){
//                         itemStr+='<span class="ty-color-blue ty-left" style="disabled:disabled;background-color:#f4f5f9;color:#ccc;">换版记录</span>'
//                     }else{
//                         itemStr+='<span class="ty-color-blue ty-left"  onclick="chargeRecond($(this))">换版记录</span>'
//                     }
//                     itemStr=itemStr+ '<span class="ty-color-blue ty-left"  onclick="basicMessage($(this))">基本信息</span>'+
//                         '</div>'+
//                         '</div>';
//                 }
//                 $(".ty-fileList").append(itemStr);
//             }
//
//
//         },
//         error:function (err) {
//             $("#mt_tip_ms").html("连接失败，请刷新重试！");
//             bounce.show($("#mtTip"));
//         }
//     })
// }
//换班记录按钮
//updator:王静 2017-08-02 10:40:34 换班记录按钮
function chargeRecord(obj){
    if(obj.hasClass("ty-disabled")){
        return false;
    }else{
        var id=obj.parents(".ty-fileItem").attr("id");
        window.location.href="goResourceHistory.do?id="+id;
    }
}
//点击基本信息的按钮
//updator:王静 2017-08-02 10:45:34 点击基本信息的按钮
function basicMessage(obj){
    var pid=obj.parents(".ty-fileItem").attr("id");
    $.ajax({
        url: "../res/getFileMessageByReference.do",
        data: {"id" :pid},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (data) {
            if(data == "" || data == undefined){
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var list = data["data"]["resource"];
                var operateDate = list.operateDate;
                if(operateDate == "1900-01-01 00:00:00"){
                    operateDate = "--";
                }
                var verifyDate = list.verifyDate;
                if(verifyDate == "1900-01-01 00:00:00"){
                    verifyDate = "--";
                }
                var approveDate = list.approveDate;
                if(approveDate == "1900-01-01 00:00:00"){
                    approveDate = "--";
                }
                var reason = list.reason;
                if(reason == undefined){
                    reason = "--";
                }
                var size = list.size;
                var sizeStr = "";
                if(size<102400){
                    sizeStr = parseFloat(size/1024).toFixed(2) + 'KB';
                }else{
                    sizeStr = parseFloat(size/1048576).toFixed(2) + 'MB';
                }
                var detailStr =
                    '<div class="ty-json">'+
                    '<div class="ty-jsonHead">基本信息</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">文件编号</div>'+
                    '<div class="ty-val fileSn">'+list.fileSn+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">文件名称</div>'+
                    '<div class="ty-val fileName">'+list.name+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">原始上传人</div>'+
                    '<div class="ty-val fileName">'+list.createName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">原始上传时间</div>'+
                    '<div class="ty-val fileName">'+list.createDate.substring(0,19)+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">版本号</div>'+
                    '<div class="ty-val">G'+list.changeNum+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">当前版本上传人</div>';
                if(list.updateName==null || list.updateName==""){
                    detailStr+= '<div class="ty-val">'+list.createName+'</div>';
                }else{
                    detailStr+= '<div class="ty-val">'+list.updateName+'</div>';
                }
                detailStr+= '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">当前版本上传时间</div>';
                if(list.updateDate==null || list.updateDate==""){
                    detailStr+= '<div class="ty-val">'+list.createDate.substring(0,19)+'</div>';
                }else{
                    detailStr+= '<div class="ty-val">'+list.updateDate.substring(0,19)+'</div>';
                }
                detailStr+= '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">大小</div>'+
                    '<div class="ty-val">'+sizeStr+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">类型</div>'+
                    '<div class="ty-val">'+list.version+'</div>'+
                    '</div>'+
                    '</div>';
                $(".ty-filePreview .ty-fileDetail").html("");
                $(".seeOnline").remove();
                $(".ty-filePreview .ty-fileDetail").append(detailStr);
                $(".ty-filePreview .seeOnline").attr("path",list.path);
            }

        },
        error:function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    })
}
//creator: 王静 2017-08-02 11：00：23 点击操作出现的下拉列表
function yj(obj,i,e){
    $("ul.hel").hide();
    $("#handleul"+i.toString()).toggle();
    $(obj).addClass("handleActive");
    e.stopPropagation();
}
//creator: 王静 2017-08-02 11：10：23 点击body的其他部分，隐藏ul
$("*").on("click",function(){
    $(".hel").hide();
    $(".cz_handle").removeClass("handleActive")
})

/* creator：张旭博，2017-05-15 16:39:08，中断上传，关闭弹窗 */
function chargeXhr(str){
    if(curXhr){ curXhr.abort(); curXhr = null;   }
    bounce.cancel();
    var info = $("#" + str).data("info")
    fileDelAjax(info)
}

/* creator：王静，2017-08-03 1:10:23 移动弹框*/
var wjid=null;//当前文件的id
function removeFile(obj){
    $(".bounce-RemoveFile .ty-treeItem").eq(0).click();
    $(".bounce-RemoveFile .ty-treeItem").eq(0).click();
    bounce.show($(".bounce-RemoveFile"));
    $(".bonceFoot span").css("margin-top","20px");
    wjid=obj.parents(".ty-fileItem").attr("id");
    localStorage.setItem("fileid",wjid);
    $("#tstip").html("");
    // $("#removeFilesure").removeClass("ty-btn-gray").addClass("ty-btn-blue");
    // $("#removeFilesure").attr("disabled",false);
    // $(".ty-treeItem").removeClass("ty-treeItemActive");
    bounce.everyTime('0.2s','removeFile',function(){
        var i = 0;
        if(!$(".bounce-RemoveFile .ty-treeItemActive").children().eq(0).hasClass("ty-fa")){i++}
        if(i == 0){
            $("#removeFilesure").removeAttr("disabled")
        }else{
            $("#removeFilesure").attr("disabled","disabled")
        }
    });
}
// /* creator：王静，2017-08-03 1:10:23 移动弹框*/
// var wjid=null;//当前文件的id
// function removeFile(obj){
//     bounce.show($(".bounce-RemoveFile"));
//     $(".bonceFoot span").css("margin-top","20px");
//     wjid=obj.parents(".ty-fileItem").attr("id");
//     $("#tstip").html("");
//     // $("#removeFilesure").removeClass("ty-btn-gray").addClass("ty-btn-blue");
//     // $("#removeFilesure").attr("disabled",false);
//     // $(".ty-treeItem").removeClass("ty-treeItemActive");
//     bounce.everyTime('0.2s','removeFile',function(){
//         var i = 0;
//         if(!$(".bounce-RemoveFile .ty-treeItemActive").children().eq(0).hasClass("ty-fa")){i++}
//         if(i == 0){
//             $("#removeFilesure").removeAttr("disabled")
//         }else{
//             $("#removeFilesure").attr("disabled","disabled")
//         }
//     });
//
// }
//creator：王静，2017-08-03 1:30:23 移动弹框中的确定按钮
function removeSure(){
    if( localStorage.getItem("noticemax")>0) {
        $("#removeFilesure").removeClass("ty-btn-blue").addClass("ty-btn-gray");
        $("#removeFilesure").attr("disabled",true);
        $("#tstip").html("该类别下存在子类别，请选择该子类别或其他类别");
    }else{
        var oldId = parseInt($("#menuTree .ty-treeItemActive").attr("id"));
        var newId = parseInt($("#removeTree .ty-treeItemActive").attr("id"));
            $("#removeFilesure").removeClass("ty-btn-gray").addClass("ty-btn-blue");
        $("#removeFilesure").attr("disabled",false);
        $("#tstip").html("");
        if(oldId == newId){
            $("#mt_tip_ms").html("该文件不能移动至原文件夹下");
            bounce.show($("#mtTip"));
        }else{
            bounce.show($(".bounce-RemoveSure"));
            $("#removeTip").html("您确定要把本文件移动到" + $("#removeTree .ty-treeItemActive").find("span").html() + "下吗？");
        }
    }
}
//creator：王静，2017-08-03 2:00:23 移动弹窗-确定-确定
function bouncesure(){
    var categoryId = parseInt($("#removeTree .ty-treeItemActive").attr("id"));
    $.ajax({
        url:"../res/changeFilePlace.do",
        type:"post",
        data:{ "fileId":localStorage.getItem("fileid"), "categoryId" : categoryId},
        success: function(data){
            if(data["success"]=="1" || data["success"]==1){
                $("#menuTree .ty-treeItemActive").click();
                $("#mt_tip_ms").html("移动成功！");
                bounce.show($("#mtTip"));
            }else if(data["success"]=="0" || data["success"]==0){
                $("#mt_tip_ms").html("您没有移动该文件的权限！");
                bounce.show($("#mtTip"));
            }
        },
        error:function(msg){
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        }
    });
}
// //creator：王静，2017-08-03 1:30:23 移动弹框中的确定按钮
// function removeSure(){
//     if( localStorage.getItem("noticemax")>0) {
//         $("#removeFilesure").removeClass("ty-btn-blue").addClass("ty-btn-gray");
//         $("#removeFilesure").attr("disabled",true);
//         $("#tstip").html("该类别下存在子类别，请选择该子类别或其他类别");
//     }else{
//         $("#removeFilesure").removeClass("ty-btn-gray").addClass("ty-btn-blue");
//         $("#removeFilesure").attr("disabled",false);
//         $("#tstip").html("");
//         var categoryId          = parseInt($(".ty-treeItemActive").attr("id"));
//         $.ajax({
//             url:"../res/removeFileReplace.do",
//             type:"post",
//             data:{ "id":localStorage.getItem("fileid"), "categoryId" : categoryId},
//             success: function(data){
//                 if(data["status"]=="1" || data["status"]==1){
//                     bounce.show($(".bounce-RemoveSure"));
//                     $("#removeTip").html("您确定要把本文件移动到" + $(".ty-treeItemActive").find("span").html() + "下吗？");
//                 }else if(data["status"]=="0" || data["status"]==0){
//                     bounce.show($(".bounce-RemoveSure"));
//                     $("#removeTip").html("您没有移动该文件的权限");
//                 }else{
//                     bounce.show($(".bounce-RemoveSure"));
//                     $("#removeTip").html("该文件不能移动至原文件夹下");
//                 }
//             },
//             error:function(msg){
//                 $("#mt_tip_ms").html("连接失败，请刷新重试！");
//                 bounce.show($("#mtTip"));
//             }
//         });
//     }
// }

// /* creator：张旭博，2017-05-06 15:13:09，绑定选择时间插件 */
laydate.render({elem: '#CV-fileMakeDate'});
laydate.render({elem: '#CV-fileReviewDate'});
laydate.render({elem: '#CV-fileApproveDate'});
laydate.render({elem: '#fileMakeDate'});
laydate.render({elem: '#fileReviewDate'});
laydate.render({elem: '#fileApproveDate'});