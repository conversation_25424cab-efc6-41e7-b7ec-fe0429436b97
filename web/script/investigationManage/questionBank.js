// 调查管理
var pageSize = 20
$(function () {
    getList(1, 1 , '')
    // 问题类型 单选按钮
    $(".radioArea span").click(function(){
        let info = $("#editQuestion1").data("editobj")
        let val = Number($(this).data("val"));
        if(info.isShow == 1 && [1,5,51,52,53].indexOf(val) == -1){
            layer.msg("系统默认的题目，其类型不可以修改为此题型！")
            return false
        }
        if(val < 10){
            if(val === 6){
                return false
            }
            $(".typenum").hide();
            $(".radioArea .fa-dot-circle-o").attr("class" , "fa fa-circle-o");
            $(this).find(".fa").attr("class" , "fa fa-dot-circle-o");
            let idstr = "#editQuestion1 .qType" + val
            $(".qType>div").hide()
            $(idstr).show().siblings().hide();
            if(val === 3 || val === 4 ){
                $(".optionPannel .pannel:gt(1)").remove();
                $(".optionPannel .pannel textarea").val("");
                $(".optionPannel .pannel .lenTip").html("0/80");

            }else if(val === 2){
                $(".qType2").find("input:eq(0)").val("是");
                $(".qType2").find("input:eq(1)").val("否");
            }else if(val === 5 || val === 7 || val === 8){
                $(".typenum" + val).show();
            }
        }else{ // 二级选择
            $(this).siblings().find(".fa-dot-circle-o").attr("class" , "fa fa-circle-o");
            $(this).find(".fa").attr("class" , "fa fa-dot-circle-o");
        }
    });

    $("body").on("click",".funBtn, .ty-btn, .linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            case "back7": //
                back7()
                break
            case "addNewQustion":
                qAddPrev($(this), funCode)
                break;
            case "bankStop":
            case "bankStart":
                bankStartOrStop($(this), funCode)
                break
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })


})
// create : hxz 2021-09-15 预览
function scan() {
    let list = $("#qusList").data("source")
    let header = $("#bankTip").val()
    let title = $("#bankName").val()
    let tip = $("#preface").val()
    let subjectTagListJson = [];
    let desc = $(".mainCon3 input.desc1").val();
    let descNum = $(".mainCon3 select.desc1").val();
    if(desc.length > 0 && descNum.length > 0){
        if(descNum !=1){
            subjectTagListJson.push({
                "name": "",
                "beginOrder": 1,
            })
        }
        subjectTagListJson.push({
            "name": desc,
            "beginOrder": descNum,
        })
    }
    $(".mainCon3 .descriptionArea .descItem").each(function () {
        let descI = $(this).find("input").val()
        let descINum = $(this).find("select").val()
        if(desc.length > 0 && descNum.length > 0){
            subjectTagListJson.push({
                "name": descI,
                "beginOrder": descINum
            })
        }
    })
    let str = ``
    if(subjectTagListJson.length > 0){
        let subMaxIndex = subjectTagListJson.length - 1
        subjectTagListJson.forEach(function(subTag,index){
            let beginOrder = subTag.beginOrder - 1
            let quesStr = ``
            let nextBeginOrder = ''
            if(subMaxIndex >= index + 1){
                nextBeginOrder = subjectTagListJson[index + 1]['beginOrder'] - 1
            }else{
                nextBeginOrder = list.length
            }
            for(let runIndex = beginOrder; runIndex < nextBeginOrder; runIndex++){
                let q = list[runIndex];
                quesStr += `
                    <div class="ques">
                        <div>${ q.orders }、${ q.content  }</div>
                        <div class="ops">${ setOption(q) }</div>
                    </div>
                `
            }
            str += `
            <div class="cat">
                <h4>${ subTag.name}</h4>
                ${ quesStr }
            </div>
        `
        })
    }else{
        let quesStr = ``
        list.forEach(function(q){
            quesStr += `
                    <div class="ques">
                        <div>${ q.orders }、${ q.content  }</div>
                        <div class="ops">${ setOption(q) }</div>
                    </div>
                `
        })
        str += `
            <div class="cat">
                <h4></h4>
                ${ quesStr }
            </div>
        `
    }
    $(".mainCon71 .main").html(str);
    $(".mainCon71 .memo").html(header);
    $(".mainCon71 .ttl").html(title);
    $(".mainCon71 .tip").html(tip);
  $(".mainCon").hide();
    $(".mainCon71").show();
}
function back71(){
    $(".mainCon").hide();
    $(".mainCon3").show();
}
function tipCon(obj) {
    let val = obj.data('val')
    $("#tipImg").attr("src",`../assets/img/invest${val}.png`)
    bounce.show($("#tipConImg"))
}
function qMustToggle(obj) {
    let mustNum = Number($(".mainCon4 .mustNum").html());
    if(obj.hasClass("ty-color-blue")){
        obj.attr("class","ty-color-red funBtn").html("放弃")
        obj.parent().prev().html('已选中')
        mustNum++
    }else{
        obj.attr("class","ty-color-blue funBtn").html("选择")
        obj.parent().prev().html('- -')
        mustNum--
    }
    $(".mainCon4 .mustNum").html(mustNum);
}
// creater : hxz 2021-08-05 删除选项
function delOption(obj) {
    obj.parents('.pannel').remove()
}
// creater : hxz 2021-08-03 问卷停用启用
function addBank() {
    $(".mainCon").hide()
    $(".mainCon2").show()
    $(".mainCon2 input").val("")
    $(".mainCon2 .back").data("type",'addBank')
    $(".lenTip").html("0/18")
    $(".lenTip50").html("0/50")
    $("#qusList tr:gt(0)").remove()
    let list = [
        { 'isRequired':0, 'content':'填表人姓名', 'type':1 , 'orders': 1, 'isShow':1 , 'investigateQuestionKeyJson':[]},
        { 'isRequired':0, 'content':'联系方式', "isMultiple":0,"type":9,"specialModel":2,"orders":2,'isShow':1 ,"investigateQuestionKeyJson":[] },
    ]
    $("#qusList").data("source", list)
    setQusList()
}
function setQusList(){
    let list = $("#qusList").data("source")
    let str = ""
    list.forEach(function (item , index) {
        item.orders = index +1
        let conShow = item.content.length > 10 ?  item.content.substr(0,10) + '...'  : item.content
        str += `
                <tr>
                    <td>${ item.orders }</td>
                    <td>${ conShow }</td>
                    <td>${ formatType(item) }</td>
                    <td>
                        <span class="hd">${ JSON.stringify(item) }</span>
                        <span class="ty-color-blue funBtn" data-fun="qEidt">修改</span>
                        <span class="ty-color-blue funBtn" data-fun="qMove">移动</span>
                        <span class="ty-color-blue funBtn" data-fun="qAddPrev">在上方增加一道题</span>
                        ${ item.isShow !==1 ? `<span class="ty-color-red funBtn" data-fun="qDel">删除本题</span>` : `` }
                    </td>
                </tr>
                `
    })
    $("#qusList tr:gt(0)").remove()
    $("#qusList").append(str);
}
// creater : hxz 2021-08-03 问卷停用启用
function bankDel(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info)
    let id = info.id
    $.ajax({
        "url":"/investigationManage/deleteSubjectJudge.do",
        "data":{ "id":id },
        success:function(res){
            let status = res.status
            bounce.cancel()
            if(status == 1){
                $("#bankDel").data("id", id)
                bounce.show($("#bankDel"))
                $("#bankDel .tip").html('<p>确定删除本问卷吗？</p>')

            } else {
                bounce_Fixed.show($("#alertTip"))
                $("#alertTip .tip").html('<p>删除失败！</p><p>已使用过的问卷不可删除，仅可停用。</p>')
            }
        }
    })
}
// creater : hxz 2021-08-03 问卷删除
function bankDelOk() {
    let id = $("#bankDel").data("id");
    $.ajax({
        "url":"/investigationManage/deleteSubject.do",
        "data":{ "id":id },
        success:function(res){
            let status = res.status
            bounce.cancel()
            if(status == 1){
               layer.msg("删除成功！")
                let pageInfo = {}
                if($("#page1").is(":visible")){
                    pageInfo = $("#page1").find(".json").html()
                }else{
                    pageInfo = $("#page1Search").find(".json").html()
                }
                pageInfo = JSON.parse(pageInfo)
                getList(pageInfo.status, pageInfo.currentPageNo ,pageInfo.month)
            } else if(status == -1){
                layer.msg("已使用，不可以删除！")
            }else if(status == 0){
                bounce_Fixed.show($("#alertTip"))
                $("#alertTip .tip").html('<p>删除失败！</p><p>已使用过的问卷不可删除，仅可停用。</p>')
            }
        }
    })
}
// creater : hxz 2021-08-03 问卷停用启用
function bankStartOrStop(obj,code) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info)
    let id = info.id
    let str = ''
    if(code === "bankStart"){
        str = '<p>确定后，发起调查时将又可选择该问卷。</p><p>确定启用该问卷吗？</p>'
    }else if(code === "bankStop"){
        str = '<p>确定后，发起调查时将无法选择该问卷。</p><p>确定停用该问卷吗？</p>'
    }
    $("#bankStartOrStop .tip").html(str)
    bounce.show($("#bankStartOrStop"))
    let type = 1;
    if(code === "bankStop"){ type = 0; }
    $("#bankStartOrStop").data("type",type).data("id",id);
}
// creater : hxz 2021-08-03
function bankStartOrStopOk( ) {
    let type = $("#bankStartOrStop").data("type") ;
    let id = $("#bankStartOrStop").data("id");
    $.ajax({
        "url":"../investigationManage/updateEnabledSubject.do",
        "data":{ "id":id , "type":type },
        success:function(res){
            let status = res.status
            bounce.cancel()
            if(status == 1){
                layer.msg("操作成功！")
                let pageInfo = {}
                if(type === 1){ // 启用
                    pageInfo = $("#page6").find(".json").html()
                }else{ // 停用
                    if($("#page1").is(":visible")){
                        pageInfo = $("#page1").find(".json").html()
                    }else{
                        pageInfo = $("#page1Search").find(".json").html()
                    }
                }
                pageInfo = JSON.parse(pageInfo)
                getList(pageInfo.status, pageInfo.currentPageNo ,pageInfo.month)
            } else if(status == -1){
                layer.msg("操作失败！")
            }
        }
    })
}
// creater : hxz 2021-08-03 问卷查询
function bankScan(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info)
    let id = info.id
    let isStop = info.isStop
    $(".back7").data("isstop", isStop)
    $(".mainCon").hide();
    $(".mainCon7").show();
    if(isStop){
        $(".noStop").hide()
    }else{
        $(".noStop").show()
    }
    getBankInfo(id)
}
function getBankInfo(id) {
    $.ajax({
        "url":"../investigationManage/selectSubject.do",
        "data":{ "id" : id },
        success:function(res){
            let list = res.respInvestigateSubjectTagList || []
            let header = res.header || ""
            let title = res.title || ""
            let preface = res.preface || ""
            let str = ``
            let subjectTag = [] // 类别
            let questionList = [] // 问题
            list.forEach(function(cat){
                let quesStr = ``;
                let quess = cat.investigateQuestionList || []
                if(quess.length > 0){
                    let firstIndex = quess[0]['orders']
                    subjectTag.push({ 'name':cat.name , 'beginOrder': firstIndex})
                    quess.forEach(function(q){
                        questionList.push({
                            'id':q.id,
                            'type':q.type,
                            'orders':q.orders,
                            'content':q.content,
                            'isShow':q.isShow,
                            'isRequired':q.isRequired ,
                            'isMultiple':q.isMultiple ,
                            'specialModel':q.specialModel ,
                            'specialTab':q.specialTab ,
                            'investigateQuestionKeyJson':q.investigateQuestionKeyList ,
                        })
                        quesStr += `
                        <div class="ques">
                            <div>${ q.orders }、${ q.content  }</div>
                            <div class="ops">${ setOption(q) }</div>
                        </div>
                    `
                    })
                    str += `
                    <div class="cat">
                        <h4>${ cat.name || "" }</h4>
                        ${ quesStr }
                    </div>
                `
                }
            })
            $(".mainCon7 .updateNormal").data('source',subjectTag );
            $(".mainCon7 .update").data('source',questionList );
            $(".mainCon7 .main").html(str);
            $(".mainCon7 .memo").html(header);
            $(".mainCon7 .ttl").html(title);
            $(".mainCon7 .preface").html(preface);
            $(".mainCon7").data("id", id);
        }
    })
}
// creater : hxz 2021-08-06 复制，生成新问卷
function copyToNew() {
    $(".mainCon").hide()
    $(".mainCon2").show()
    $(".mainCon2 input").val("")
    $(".mainCon2 .back").data("type",'copyToNew')
    $(".lenTip").html("0/18")
    $("#qusList tr:gt(0)").remove()
    let bankName2 = $(".mainCon7 .ttl").html()
    let bankTip2 = $(".mainCon7 .memo").html()
    $("#bankName").val(bankName2)
    $("#bankName").parents(".pannel").find(".lenTip").html(bankName2.length + '/50')
    $("#bankTip").parents(".pannel").find(".lenTip").html(bankTip2.length + '/18')
    $("#bankTip").val(bankTip2)
    let list = $(".mainCon7 .update").data('source') || [];
    $("#qusList").data("source", list)
    setQusList()
}
// creater : hxz 2021-08-03 修改问题以外的内容
function updateNormal() {
    $(".mainCon").hide();
    $(".mainCon8").show();
    $(".update").show();
    $(".new").hide();
    $(".lenTip").html('0/18')
    $(".lenTip50").html('0/50')
    let id = $(".mainCon7").data("id");
    let bankName2 = $(".mainCon7 .ttl").html()
    let bankTip2 = $(".mainCon7 .memo").html()
    let preface2 = $(".mainCon7 .preface").html()
    $("#bankName2").val(bankName2)
    $(".mainCon8 .mustReSet").data("tagstatus",0);
    $("#bankName2").parents(".pannel").find(".lenTip").html(bankName2.length + '/50')
    $("#bankTip2").parents(".pannel").find(".lenTip").html(bankTip2.length + '/18')
    $("#preface2").parents(".pannel").find(".lenTip").html(preface2.length + '/1000')
    $("#bankTip2").val(bankTip2)
    $("#preface2").val(preface2)
    $(".mainCon8 .descriptionArea").html("")
    let subjectTag = $(".mainCon7 .updateNormal").data('source') || [];
    let questionList = $(".mainCon7 .update").data('source') || [];
    let firstBeforeTag = -1 ;
    $(".mainCon8 input.desc1").val("").parents(".pannel").find(".lenTip").html('0/50')
    $(".mainCon8 select.desc1").html(setNumStr("", questionList.length))
    subjectTag.forEach(function (tag,index) {
        if(tag.name && tag.name.length > 0){
        }else{
            firstBeforeTag = index
        }
    })
    let firstTag = firstBeforeTag + 1
    subjectTag.forEach(function(tag,index){
        if(index < firstTag){

        }else if(index == firstTag){
            if(tag.name){
                $(".mainCon8 input.desc1").val(tag.name)
                    .parents(".pannel").find(".lenTip").html(tag.name.length + " / 50" );
                $(".mainCon8 select.desc1").html(setNumStr(tag.beginOrder, questionList.length))
            }else{
                $(".mainCon8 input.desc1").val("")
                $(".mainCon8 select.desc1").html(setNumStr("", questionList.length))
            }
        }else{
            let tem = `
                 <div class="descItem pannel" >
                    <div>
                        <span class="lenTip ty-right">${tag.name.length}/50</span>
                    </div>
                    <input type="text" class="form-control" value="${tag.name}" placeholder="请录入问题的类别描述，字数上限为50个。" onchange="setWordsNum($(this),50)"/>
                    <p>
                        <span>请确定问题类别描述的位置：</span>
                        <span class="marLeft100">问题类别描述放到第<select class="form-control">${ setNumStr(tag.beginOrder, questionList.length)}</select>题之前</span>
                        <span class="red linkBtn ty-right" data-fun="descOptionDel">删除</span>
                    </p>
                </div>
                `
            $(".mainCon8 .descriptionArea").append(tem)
        }
    })
    let num = 0;
    questionList.forEach(function (q) {
        if(q.isRequired === 1){
            num++
        }
    })
    $(".mainCon8 .qMustNum").html(num);

}

function setNumStr(num,len) {
    let numStr = `<option value=""> -- </option>`
    for(let i = 1 ; i <= len ; i++){
        numStr += `<option ${ num === i ? 'selected':"" }>${ i }</option>`
    }
    return numStr
}
function setOption(q) {
    let str = ``
    if([2,3,4].indexOf(Number(q.type)) > -1){
        let ops = q.investigateQuestionKeyList || q.investigateQuestionKeyJson|| []
        ops.forEach(function(op, index){
            str += `
            <div>${index +1 }. ${ op.content }</div>
            `
        })
    }
    return str
}
// creater : hxz 2021-08-03 首页 查找
function searchBtn1(obj) {
    let searchKey = $("#searchKey").val();
    getListSearch(searchKey, 1 , "")
}
// creater : hxz 2021-08-03  月查询停用的列表
function monthScan(obj) {
    let monthInfo = obj.siblings(".hd").html();
    monthInfo = JSON.parse(monthInfo);
    let month = monthInfo.month
    getList(0, 1 , month)
    $(".mainCon").hide()
    $(".mainCon6").show()
}
// creater : hxz 2021-08-03 主列表 查询
function getListSearch(keyword, currentPageNo , month) {
    $.ajax({
        "url":"../investigationManage/selectSubjectByKeywordList.do",
        "data":{
            "keyword" : keyword,
            "currentPageNo" : currentPageNo,
            "month" : month ,
            "pageSize" : pageSize,
        },
        success:function(res){
            let list = res.investigateSubjectList || []
            let totalResult = res.totalResult || 0
            let sumPage = res.sumPage || 0
            let str = ``
            list.forEach(function(bank){
                bank.isStop = false
                str += `
                 <tr>
                    <td>${ bank.title }</td>
                    <td>${ bank.createName } ${ new Date(bank.createDate).format("yyyy-MM-dd hh:mm:ss")  }</td>
                    <td>
                        <span class="hd">${ JSON.stringify(bank) }</span>
                        <span class="ty-color-blue funBtn" data-fun="bankScan">查看</span>
                       <span class="ty-color-blue funBtn" data-fun="bankStop">停用</span>
                        <span class="ty-color-blue funBtn" data-fun="bankDel">删除</span>
                    </td>
                </tr>
                `
            })
            let info = JSON.stringify({
                "status" : status,
                "currentPageNo" : currentPageNo,
                "month" : month ,
                "keyword" : keyword ,
                "pageSize" : pageSize })
            $(".num1").html(totalResult)
            setPage($("#page1Search"),currentPageNo,sumPage,'investSearch',info)
            $("#page1").hide()
            $("#page1Search").show()
            $(".mainCon1 table tr:gt(0)").remove();
            $(".mainCon1 table").append(str);
        }
    })
}
// creater : hxz 2021-08-03 主列表
function getList(status, currentPageNo , month) {
    $.ajax({
        "url":"../investigationManage/selectSubjectList.do",
        "data":{
            "status" : status,
            "currentPageNo" : currentPageNo,
            "month" : month ,
            "pageSize" : pageSize,
        },
        success:function(res){
            let list = res.investigateSubjectList || []
            let sumPage = res.pageInfo.totalPage || 0
            let currentPageNo2 = res.pageInfo.currentPageNo || currentPageNo
            let sum = res.sum || 0
            let str = ``
            list.forEach(function(bank){
                bank.isStop = false
                if(status===0){
                    bank.isStop = true
                }
                str += `
                 <tr>
                    <td>${ bank.title }</td>
                    <td>${ bank.createName } ${ new Date(bank.createDate).format("yyyy-MM-dd hh:mm:ss")  }</td>
                    ${ status===0? `<td>${ bank.updateName } ${ new Date(bank.updateDate).format("yyyy-MM-dd hh:mm:ss")  }</td>` : ""                 }
                    <td>
                        <span class="hd">${ JSON.stringify(bank) }</span>
                        <span class="ty-color-blue funBtn" data-fun="bankScan">查看</span>
                        ${ status===0? `<span class="ty-color-blue funBtn" data-fun="bankStart">启用</span>` : `<span class="ty-color-blue funBtn" data-fun="bankStop">停用</span>`  }
                        
                        <span class="ty-color-blue funBtn" data-fun="bankDel">删除</span>
                    </td>
                </tr>
                `
            })

            let info = JSON.stringify({
                "status" : status,
                "currentPageNo" : currentPageNo,
                "month" : month ,
                "pageSize" : pageSize })
            if( status===0){
                $(".num6").html(sum)
                setPage($("#page6"),currentPageNo2,sumPage,'invest1',info)
                $(".mainCon6 table tr:gt(0)").remove();
                $(".mainCon6 table tbody").append(str);
            }else{
                $("#page1Search").hide()
                $("#page1").show()
                $(".num1").html(sum)
                setPage($("#page1"),currentPageNo2,sumPage,'invest1',info)
                $(".mainCon1 table tr:gt(0)").remove();
                $(".mainCon1 table tbody").append(str);
            }

        }
    })
}
function back51() {
    $(".mainCon").hide();
    $(".mainCon1").show();
}
// creater : hxz 2021-08-03 已停用的问卷
function goStop() {
    getEndList("")
    $(".mainCon").hide();
    $(".mainCon5").show();
}
function searchBtn2() {
    getEndList($("#endSearchYear").val())
}
function getEndList(year) {
    $.ajax({
        "url":"../investigationManage/selectSubjectStopByMonthList.do",
        "data":{ "year" : year },
        success:function(res){
            let list = res.respInvestigateStopByMonthList || []
            let sum = res.sum || 0
            let str = ``
            list.forEach(function(bank){
                str += `
                 <tr>
                    <td>${ bank.month }</td>
                    <td>
                        <span class="hd">${ JSON.stringify(bank) }</span>
                        <span>${ bank.sum } 份</span>
                        <span class="ty-color-blue funBtn" data-fun="monthScan">查看</span>
                    </td>
                </tr>
                `
            })
            $("#stopBankMonth tr:gt(0)").remove();
            $("#stopBankMonth").append(str);
            $(".mainCon5 .year").html(year || '今');
            $(".mainCon5 .num").html(sum);

        }
    })
}
// creater : hxz 2021-08-03 保存must
function saveMust() {
    let qlist = []
    let num = 0
    $("#mustTab tr:gt(0)").each(function(){
        let obj = $(this).find(".hd")
        let q = obj.html();
        q = JSON.parse(q);
        if(obj.siblings("span").hasClass("ty-color-red")){
            q.isRequired = 1
            num++
        }else{
            q.isRequired = 0
        }
        qlist.push(q)
    })
    let type = $(".mainCon4 .saveMust").data("type");
    if(type === "mustSet"){
        $("#qusList").data("source", qlist) ;
        $(".mainCon3 .qMustNum").html(num) ;
        back4()
    }else if(type === "mustReSet"){
        $(".mainCon7 .update").data('source',qlist)
        $(".mainCon8 .qMustNum").html(num);
        back4()
    }

}
function backAddBank(){
    bounce.show($("#backAddBank"))
}
function back(){
    let type = $(".mainCon2 .back").data("type")
    if(type === "addBank"){
        bounce.cancel()
        $(".mainCon").hide();
        $(".mainCon1").show();
        let pageInfo = {}
        if($("#page1").is(":visible")){
            pageInfo = $("#page1").find(".json").html()
        }else{
            pageInfo = $("#page1Search").find(".json").html()
        }
        pageInfo = JSON.parse(pageInfo)
        getList(pageInfo.status, pageInfo.currentPageNo ,pageInfo.month)
    }else{
        bounce.cancel()
        $(".mainCon").hide();
        $(".mainCon7").show();
        $(".update").show();
    }

}
function back7() {
    let isStop = $(".back7").data("isstop");
    if(isStop){
        $(".mainCon").hide();
        $(".mainCon6").show();
    }else{
        $(".mainCon").hide();
        $(".mainCon1").show();
    }
}
function back8() {
    $(".mainCon").hide();
    $(".mainCon7").show();
    // let id = $(".mainCon7").data("id");
    // getBankInfo(id)
}
function back5(){
    $(".mainCon").hide();
    $(".mainCon5").show();
    let year = $("#endSearchYear").val()
    getEndList(year);
}
function back4(){
    let type = $(".mainCon4 .saveMust").data("type");
    if(type === "mustSet"){
        $(".mainCon").hide();
        $(".mainCon3").show();
    }else if(type === "mustReSet"){
        $(".mainCon").hide();
        $(".mainCon8").show();
    }
}
function prevStep(){
    $(".mainCon").hide();
    $(".mainCon2").show();
}
function qDel(obj) {
    bounce.show($("#quesDel"));
    $("#quesDel").data('obj', obj)
}
function qDelOk() {
    let obj = $("#quesDel").data('obj')
    let info = obj.siblings(".hd").html()
    info = JSON.parse(info)
    let list = $("#qusList").data("source")
    let delIndex = 0
    list.forEach(function(qIm, index){
        if(qIm.orders === info.orders){
            delIndex = index
        }
    })
    list.splice(delIndex, 1);
    list.forEach(function(qIm, index){
        qIm.orders = index + 1
    })
    $("#qusList").data("source", list)
    setQusList();
    bounce.cancel()
}
// creater : hxz 2021-08-02  保存问题
function saveQuestion() {
    let ques = {}
    ques.isRequired = 0 // 默认不必填
    ques.content = $("#qContent").val();
    let valObj = $(".radioArea .fa-dot-circle-o")
    if(ques.content.length === 0){
        layer.msg("请录入问卷内容!");
        return false;
    }
    ques.isMultiple = 0
    if(valObj.length > 0){
        let val = Number(valObj.parent().data("val"));
        if(val > 4){ // 特殊题型
            if(valObj.length === 2){
                ques.type = 9
                if(val === 7){
                    ques.specialModel = 3
                }else if(val === 8){
                    ques.specialModel = 4
                }
                console.log(valObj)
                console.log(valObj[1])
                let val2 = Number(valObj.eq(1).parent().data("val"));
                if(val2 === 51){
                   ques.specialModel = 1
                }else if(val2 === 52){
                    ques.specialModel = 2
                }else if(val2 === 53){
                    ques.isMultiple = 8
                }else if(val2 >70 && val2 < 75){
                    ques.specialTab = val2 - 70
                }else if(val2 >80 && val2 < 84){
                    ques.specialTab = val2 - 80
                }
            } else {
                layer.msg("请选择具体的题型!");
                return false;
            }
        }else{ // 常规题
            ques.type = val
        }
    }else{
        layer.msg("请选择题型!");
        return false;
    }
    ques.orders = $("#qusList").length ;
    ques.investigateQuestionKeyJson = [];
    if([2,3,4].indexOf(Number(ques.type)) > -1){
        let isok = true
        let conAddress = $(".qType3 .pannel")
        if(Number(ques.type) === 2){
            conAddress =$(".qType2 .pannel")
        }
        conAddress.each(function(index){
            let option = {}
            option.content = $(this).find("input, textarea").val()
            if(option.content.length === 0){
                isok = false
            }
            option.orders = index + 1;
            console.log(option.orders)
            ques.investigateQuestionKeyJson.push(option)
        })
        if(!isok){
            layer.msg("选项不能为空！")
            return false;
        }
    }
    let isPrev = $("#editQuestion1").data("isPrev")
    let infoOld = $("#editQuestion1").data("editobj")
    let list = $("#qusList").data("source") || [] ;
    if(isPrev > 0){ // 在上方增加一道题
        list.splice(isPrev-1, 0, ques);
    }else if(isPrev <0){ // 修改
        ques.isShow = infoOld.isShow
        list.splice(-isPrev-1, 1, ques);
    }else{ // 新增
        list.push(ques)
    }
    $("#qusList").data("source", list)
    setQusList();
    bounce.cancel();
}
// creater : hxz 2021-08-02  编辑问卷 下一步
function addQuesNext() {
    let bankName = $("#bankName").val();
    if(bankName.length == 0){
        layer.msg('操作失败！请先将必填项填完！')
        return false
    }
    $(".mainCon").hide();
    $(".mainCon3").show();
    $(".update").hide();
    $(".new").show();
    $(".mainCon3 .descriptionArea").html("");
    let len = $("#qusList").data("source").length
    $(".qNum").html(len);
    let list = $("#qusList").data("source")
    let num = 0
    list.forEach(function (q) {
        if(q.isRequired === 1){
            num++
        }
    })
    $(".mainCon3 .lenTip").html("0 / 18");
    $(".mainCon3 .lenTip50").html("0 / 50");
    $(".mainCon3 .prefaceTip").html("0 / 1000");

    $(".mainCon3 .qMustNum").html(num);
    $(".mainCon3 input").val("");
    $(".mainCon3 textarea").val("");
    let numStr = `<option value=""> -- </option>`
    for(let i = 1 ; i <= len ; i++){
        numStr += `<option>${ i }</option>`
    }
    $("select.desc1").html(numStr)
    let type = $(".mainCon2 .back").data("type")
    if(type === "copyToNew" || type === "updateBank"){
        let preface2 = $(".mainCon7 .preface").html()
        $("#preface").val(preface2).parents(".pannel").find(".lenTip").html(preface2.length + '/1000');
        let subjectTag = $(".mainCon7 .updateNormal").data('source') || [];
        subjectTag.forEach(function(tag,index){
            if(index > 0){
                let tem = `
                 <div class="descItem pannel" >
                    <div>
                        <span class="lenTip ty-right">${tag.name.length}/50</span>
                    </div>
                    <input type="text" class="form-control" value="${tag.name}" placeholder="请录入问题的类别描述，字数上限为50个。" onchange="setWordsNum($(this),50)"/>
                    <p>
                        <span>请确定问题类别描述的位置：</span>
                        <span class="marLeft100">问题类别描述放到第<select class="form-control">${ setNumStr(tag.beginOrder, len)}</select>题之前</span>
                        <span class="red linkBtn ty-right" data-fun="descOptionDel">删除</span>
                    </p>
                </div>
                `
                $(".mainCon3 .descriptionArea").append(tem)
            }else{
                if(tag.name){
                    $(".mainCon3 input.desc1").val(tag.name)
                        .parents(".pannel").find(".lenTip").html(tag.name.length + " / 50" );
                    $(".mainCon3 select.desc1").html(setNumStr(tag.beginOrder, len))
                }else{
                    $(".mainCon3 input.desc1").val("")
                    $(".mainCon3 select.desc1").html(setNumStr("", questionList.length))
                }
            }
        })
    }
}
// creater : hxz 2021-08-02  新增选项
function addOption() {
    let temp = `
                <div class="pannel pannelAdd">
                   <div>
                       <span>选项</span>
                       <span class="lenTip ty-right">0/80</span>
                   </div>
                   <div class="clearInput">
                        <textarea class="form-control" placeholder="请录入，字数上限为80个。" onchange="setWordsNum($(this),80)"></textarea>
                        <i class="fa fa-times clearTextAreaVal" onmousedown="clearPrevVal($(this))"></i>
                   </div>
                   <span class=" linkBtn pannelDel red" data-fun="delOption">删除</span>
               </div>
                `
    $(".optionPannel").append(temp);
}
// creater : hxz 2021-08-02 增加问题类别描述
function bankDescAdd2(obj) {
    descAdd(obj,  $(".mainCon7 .update").data('source').length)
}
function bankDescAdd(obj) {
    descAdd(obj,  $("#qusList").data("source").length)
}
function descAdd(obj,len) {
    let mainCon = obj.parents(".mainCon")
    let descConList = []
    let descNumList = []
    let desc = mainCon.find("input.desc1").val();
    let descNum = mainCon.find("select.desc1").val();
    let isok = desc.length > 0 && descNum.length > 0
    if(!isok){
        layer.msg("请将第一条描述填写完整再新增！")
        return false
    }
    descConList.push(desc)
    descNumList.push(descNum)
    let descItem = mainCon.find(".descriptionArea .descItem")
    if(descItem.length > 0){
        let isok2 = 0
        descItem.each(function () {
            let desccon = $(this).find("input").val();
            let descnum = $(this).find("select").val();
            if(!( desccon.length > 0 && descnum.length > 0 )){
                isok2 = -1
            }else{
                if(descConList.indexOf(desccon) > -1 || descNumList.indexOf(descnum) > -1){
                    isok2 = -2
                }
                descConList.push(desccon)
                descNumList.push(descnum)
            }
        })
        if(isok2 === 0 && descNumList.length === len){
            isok2 = -3
        }
        if(isok2 === -1){
            layer.msg("请将以上每组描述填写完整再新增！")
            return false
        }else if(isok2 === -2){
            layer.msg("有重复的问题类别描述或者开始题号，<br>请先检查！")
            return false
        }else if(isok2 === -3){
            layer.msg("已经没有可选的题号了哦")
            return false
        }
    }
    let numStr = `<option value=""> -- </option>`
    for(let i = 1 ; i <= len ; i++){
        numStr += `<option>${ i }</option>`
    }
    let tem = `
                 <div class="descItem pannel" >
                    <div>
                        <span class="lenTip ty-right">0/50</span>
                    </div>
                    <input type="text" class="form-control" placeholder="请录入问题的类别描述，字数上限为50个。" onchange="setWordsNum($(this),50)"/>
                    <div>
                        <span>请确定问题类别描述的位置：</span>
                        <span class="marLeft100">问题类别描述放到第<select class="form-control">${numStr}</select>题之前</span>
                        <span class="red linkBtn ty-right" data-fun="descOptionDel">删除</span>
                    </div>
                </div>
                `
    mainCon.find(".descriptionArea").append(tem);
}
function testDesc(descItem, descConList, descNumList) {
    let isok2 = 0
    if(descItem.length > 0){
        descItem.each(function () {
            console.log($(this).html())
            let desccon = $(this).find("input").val();
            let descnum = $(this).find("select").val();
            if(!( desccon.length > 0 && descnum.length > 0 )){
                isok2 = -1
            }else{
                if(descConList.indexOf(desccon) > -1 || descNumList.indexOf(descnum) > -1){
                    isok2 = -2
                }
                descConList.push(desccon)
                descNumList.push(descnum)
            }
        })
        if(isok2 === -1){
            layer.msg("请将每组描述填写完整！")
        }else if(isok2 === -2){
            layer.msg("有重复的问题类别描述或者开始题号，<br>请先检查！")
        }
    }
    return isok2
}
function descOptionDel(obj) {
    obj.parents(".descItem").remove();
}
// creater : hxz 2021-08-02 移动问题
function qMove(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    bounce.show($("#moveQuestion"));
    $("#moveQuestion").data('info', info)
    $("#qNoList").val('')
    let list = $("#qusList").data("source")
    let optionStr = '<option value=""></option>'
    list.forEach(function (item , index) {
        item.orders = index +1
        optionStr += `<option value="${ item.orders }">${ item.orders }</option>`
    })
    $("#qNoList").html(optionStr)
    $("#qNoFa").attr('class', 'fa fa-circle-o')
}
function moveQuestionOK() {
    let hav = $("#qNoFa").hasClass('fa-dot-circle-o')
    let newOrder = 0
    let moveItem = $("#moveQuestion").data('info')
    let list = $("#qusList").data("source")
    let oldOrder = moveItem.orders - 1

    if(hav){
        console.log('oldOrder=', oldOrder)
        list.splice(oldOrder,1)
        list.push(moveItem)
    }else{
        let order = $("#qNoList").val()
        if(!order){
            layer.msg('请先选择！')
            return false
        }
        newOrder = order - 1
        if(newOrder > oldOrder){
            // 插到原来位置的后面 先插入 再删除
            list.splice(newOrder,0, moveItem)
            list.splice(oldOrder, 1)
        }else if(newOrder < oldOrder){
            // 插到原来位置的前面 先删除 再插入
            list.splice(oldOrder, 1)
            list.splice(newOrder,0, moveItem)
        }

    }
    $("#qusList").data("source", list)
    setQusList()
    bounce.cancel()
}
function changeOprion(type) {
    if(type === 1){
        $("#qNoFa").attr('class', 'fa fa-circle-o')
    }else {
        $("#qNoList").val('')
        $("#qNoFa").attr('class', 'fa fa-dot-circle-o')
    }
}
// creater : hxz 2021-08-02 修改问题
function qEidt(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    bounce.show($("#editQuestion1"));
    $(".qType>div").hide();
    $(".editQuestion1Ttl").html("修改问题");
    let order = info.orders
    $("#editQuestion1").data("isPrev", -order)
    $("#editQuestion1").data("editobj", info)
    $(".qContentLen").html(`${ info.content.length }/50`)
    $(".radioArea").find(".fa-dot-circle-o").attr("class" , "fa fa-circle-o");
    $("#qContent").val(info.content)
    $(".optionPannel .pannel:gt(1)").remove();
    $(".typenum").hide()
    info.type = Number(info.type)
    if(info.type == 1){
        $(".type1and2").children(":eq(0)").find(".fa").attr("class" , "fa fa-dot-circle-o");
    }else if([2,3,4].indexOf(info.type) > -1){
        if(info.type == 2){
            $(".type1and2").children(":eq(1)").find(".fa").attr("class" , "fa fa-dot-circle-o");
        } else if(info.type == 3){
            $(".type3and4").children(":eq(0)").find(".fa").attr("class" , "fa fa-dot-circle-o");
        } else if(info.type == 4){
            $(".type3and4").children(":eq(1)").find(".fa").attr("class" , "fa fa-dot-circle-o");
        }
        let idstr = "#editQuestion1 .qType" + info.type
        $(idstr).show().siblings().hide();
        info.investigateQuestionKeyJson.forEach(function (q,index) {
            if(info.type === 3 || info.type === 4 ){
                if(index>1){
                    addOption()
                }
                $(`.optionPannel .pannel:nth-child(${ index + 1 })`).find(".lenTip").html(q.content.length + ' / 80');
                $(`.optionPannel .pannel:nth-child(${ index + 1 })`).find("textarea").val(q.content);

            }else if(info.type === 2){
                $(".qType2").find(`input:eq(${index})`).val(q.content);
            }
        })
    }else if(info.type == 9){
        info.specialModel = Number(info.specialModel)
        if(info.specialModel === 3){
            $(".type7and8").children(":eq(0)").find(".fa").attr("class" , "fa fa-dot-circle-o");
            $(`.typenum7`).show()
                .find(`span`).eq(info.specialTab-1).find(".fa").attr("class" , "fa fa-dot-circle-o");
        }else if(info.specialModel === 4){
            $(".type7and8").children(":eq(1)").find(".fa").attr("class" , "fa fa-dot-circle-o");
            $(`.typenum8`).show()
                .find(`span`).eq(info.specialTab-1).find(".fa").attr("class" , "fa fa-dot-circle-o");
        }else if(info.specialModel === 1 || info.specialModel === 2 || info.isMultiple === 8){
            $(".type5and6").children(":eq(0)").find(".fa").attr("class" , "fa fa-dot-circle-o");
            $(`.typenum5`).show();
            if(info.isMultiple === 8){
                $(`.typenum5`).find(`span:nth-child(3)`).find(".fa").attr("class" , "fa fa-dot-circle-o");
            }else{
                $(`.typenum5`).find(`span:nth-child(${ info.specialModel})`).find(".fa").attr("class" , "fa fa-dot-circle-o");
            }
        }
    }
}
// creater : hxz 2021-08-02 在上面新增一行
function qAddPrev(obj, funCode) {
    bounce.show($("#editQuestion1"));
    // $("#qusList tr:gt(0)").remove()
    $(".qType>div").hide();
    $(".editQuestion1Ttl").html("新增问题");
    $("#editQuestion1").data("editobj", {})
    if( funCode == 'qAddPrev'){
        let order = JSON.parse(obj.siblings(".hd").html()).orders
        $("#editQuestion1").data("isPrev", order)
    }else{
        $("#editQuestion1").data("isPrev", 0)
    }
    $(".lenTip").html("0/80")
    $(`.typenum`).hide();
    $(`.radioArea span`).show();

    $(".radioArea").find(".fa-dot-circle-o").attr("class" , "fa fa-circle-o");
    $("#qContent").val("")
}
// creater : hxz 2021-08-02 必答题设置
function mustReSet() {
    $(".mainCon").hide();
    $(".mainCon4").show();
    $(".mainCon4 .saveMust").data("type","mustReSet");
    $(".mainCon8 .mustReSet").data("tagstatus",1);
    $("#mustTab tr:gt(0)").remove()
    let qList = $(".mainCon7 .update").data('source');
    setMustTabStr(qList)
}
// creater : hxz 2021-08-02 必答题设置
function mustSet() {
    $(".mainCon").hide();
    $(".mainCon4").show();
    $(".mainCon4 .saveMust").data("type","mustSet");
    $("#mustTab tr:gt(0)").remove()
    let qList = $("#qusList").data("source") ;
    setMustTabStr(qList)
}
function setMustTabStr(qList) {
    let str = ""
    let mustNum = 0
    qList.forEach(function(q){
        mustNum = q.isRequired ===1 ? ++mustNum : mustNum
        str += `
        <tr>
            <td>${ q.orders }</td>
            <td>${ q.content }</td>
            <td>${ formatType(q) }</td>
            <td>${ q.isRequired ===1 ? "已选中" :"- -" }</td>
            <td>
                <span class="${ q.isRequired ===1 ? "ty-color-red" :"ty-color-blue" } funBtn" data-fun="qMustToggle">${ q.isRequired ===1 ? "放弃" :"选择" }</span> 
                <span class="hd">${ JSON.stringify(q) }</span> 
            </td>
        </tr>
        `
    })
    $("#mustTab").append(str)
    $(".mainCon4 .allNum").html(qList.length);
    $(".mainCon4 .mustNum").html(mustNum);
}
// creater : hxz 2021-08-06 修改完毕，下一步
function editBankNext() {
    let title = $("#bankName2").val();
    let header = $("#bankTip2").val();
    let preface = $("#preface2").val();
    // 标签是否有修改，1修改，0未修改 为1时，所有subjectTag类别都传上来整体替换
    let tagStatus =  $(".mainCon8 .mustReSet").data("tagstatus");
    let subjectTagListJson = [];
    let questions = $(".mainCon7 .update").data('source') || [];
    let questionListJson = []
    questions.forEach(function (q) {
        questionListJson.push({ 'isRequired':q.isRequired , 'id':q.id })
    })
    if(true){
        let desc = $(".mainCon8 input.desc1").val();
        let descNum = $(".mainCon8 select.desc1").val();
        if(desc.length > 0 && descNum.length > 0){
            subjectTagListJson.push({
                "name": desc,
                "beginOrder": descNum,
            })
            let isok2 = testDesc($(".mainCon8 .descriptionArea .descItem"), [desc],[descNum]);
            if(isok2 < 0){
                return false
            }
        }else if(desc.length > 0 || descNum.length > 0){
            layer.msg("请将描述和题号补充完整")
            return false
        }

        let isDecOk = true
        $(".mainCon8 .descriptionArea .descItem").each(function () {
            let descI = $(this).find("input").val()
            let descINum = $(this).find("select").val()
            if(desc.length > 0 && descNum.length > 0){
                subjectTagListJson.push({
                    "name": descI,
                    "beginOrder": descINum
                })
            }else if(desc.length > 0 || descNum.length > 0){
                isDecOk = false
            }
        })
        if(!isDecOk){
            layer.msg("请将描述和题号补充完整")
            return false
        }
    }
    $.ajax({
        "url":"../investigationManage/updateSubjectOrTag.do",
        "data":{
            "id" : $(".mainCon7").data("id"),
            "title" : title,
            "header" : header,
            "preface" : preface,
            "tagStatus" : 1,
            "subjectTagListJson" : JSON.stringify(subjectTagListJson ),
            "questionListJson" : JSON.stringify( questionListJson ),
        },
        success:function(res){
            let status = res.status
            if(status === 1){
                layer.msg('操作成功')
                getList(1, 1 , '')
                $(".mainCon").hide();
                $(".mainCon1").show();
            }else{
                layer.msg('操作失败')
            }
        }
    })
}
function update() {
    $.ajax({
        "url":"../investigationManage/updateSubjectJudge.do",
        "data":{ "id": $(".mainCon7").data("id") },
        success:function(res){
            let status = res.status
            if(status == -1){
                bounce_Fixed.show($("#alertTip"))
                $("#alertTip .tip").html(`不可修改！<br>已参与过调查的问卷不可修改，仅可生成新问卷。`)
            }else{ // 走新增的流程
                $(".mainCon").hide()
                $(".mainCon2").show()
                $(".mainCon2 input").val("")
                $(".lenTip").html("0/18")
                $("#qusList tr:gt(0)").remove()
                let bankName2 = $(".mainCon7 .ttl").html()
                let bankTip2 = $(".mainCon7 .memo").html()
                let preface2 = $(".mainCon7 .preface").html()
                $("#bankName").val(bankName2)
                $("#bankName").parents(".pannel").find(".lenTip").html(bankName2.length + '/50')
                $("#bankTip").parents(".pannel").find(".lenTip").html(bankTip2.length + '/18')
                $("#bankTip").val(bankTip2)
                $("#preface").val(preface2)
                let questionList = $(".mainCon7 .update").data('source');
                $("#qusList").data("source", questionList)
                $(".mainCon2 .back").data("type", "updateBank")
                setQusList()
            }
        }
    })

}
// creater : hxz 2021-08-02 保存问卷
function saveBank() {
    let questions = $("#qusList").data("source")
    let title = $("#bankName").val()
    let header = $("#bankTip").val()
    let preface = $("#preface").val()
    let subjectTagListJson = [];
    let desc = $(".mainCon3 input.desc1").val();
    let descNum = $(".mainCon3 select.desc1").val();
    if(desc.length > 0 && descNum.length > 0){
        subjectTagListJson.push({
          "name": desc,
            "beginOrder": descNum,
        })
        let isok2 = testDesc($(".mainCon3 .descriptionArea .descItem"), [desc],[descNum]);
        if(isok2 < 0){
            return false
        }
    }else if(desc.length > 0 || descNum.length > 0){
        layer.msg("请将描述和题号补充完整")
        return false
    }
    let isDecOk = true
    $(".mainCon3 .descriptionArea .descItem").each(function () {
        let descI = $(this).find("input").val()
        let descINum = $(this).find("select").val()
        if(descI.length > 0 && descINum.length > 0){
            subjectTagListJson.push({
                "name": descI,
                "beginOrder": descINum
            })
        }else if(descI.length > 0 || descINum.length > 0){
            isDecOk = false
        }
    })
    if(!isDecOk){
        layer.msg("请将描述和题号补充完整")
        return false
    }
    let typeEdit = $(".mainCon2 .back").data("type"); // copyToNew , addBank , updateBank
    let url = "../investigationManage/addSubject.do"
    let data = {
        "title" : title,
        "header" : header,
        "preface" : preface,
        "subjectTagListJson" : JSON.stringify(subjectTagListJson ),
        "questionListJson" : JSON.stringify( questions ),
    }
    if(typeEdit === "updateBank"){
        url = "../investigationManage/updateSubject.do"
        data.id = $(".mainCon7").data("id");
    }
    $.ajax({
        "url": url,
        "data":data,
        success:function(res){
            let status = res.status
            if(status === 1){
                layer.msg('问卷已成功保存。请生成二维码，并发起调查吧！')
                getList(1, 1 , '')
                $(".mainCon").hide();
                $(".mainCon1").show();
            }else{
                layer.msg('操作失败')
            }
        }
    })



}
// creater : hxz 2021-08-02 计算字数
function setWordsNum(thisObj , maxLen){
    let curtxt = thisObj.val()
    if(curtxt.length > maxLen){
        thisObj.val(curtxt.substr(0,maxLen));
        layer.msg(`最多录入${maxLen}个字符`);
    }
    thisObj.parents(".pannel").find(".lenTip").html( thisObj.val().length + " / " + maxLen);
}
// creator: 李玉婷，2021-06-25 11:19:14，一键清除
function clearPrevVal(obj){
    obj.prev().val("");
    obj.prev().get(0).focus();
    obj.parents(".clearArea").find(".lenTip").html('0 / 80');
    obj.parents(".clearInput").find(".lenTip").html('0 / 15');
}

function formatType(info) {
    let type = info.type
    let str = '';
    switch (Number(type)){
        case 1:
            str = '常规问答题'; break;
        case 2:
            str = '正误判断题'; break;
        case 3:
            str = '常规单选题'; break;
        case 4:
            str = '常规多选题'; break;
        case 9:
            if(info.specialModel == 3){
                str = '国内省、市、地区或地址的题'; break;
            }else if(info.specialModel == 4){
                str = '年、月或日期的题'; break;
            }else if(info.specialModel == 1 || info.specialModel == 2 || info.isMultiple == 8){
                str = '几种特殊型式的问答题'; break;
            }
    }
    return str;
}



laydate.render({elem: '#endSearchYear', type: 'year'});
