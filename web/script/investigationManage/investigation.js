
var pageSize = 20
$(function () {
    getList(1, 1 , '')
    $(".qAnsList").on("click",".ty-color-blue", function(){
        let info = $(this).siblings('.hd').html()
        $(".qAnsList").data('info', info)
        $(".mainCon").hide();
        $(".mainCon1313").show();
        getYearAreaDetails(1)
    })
    $("body").on("click",".funBtn, .ty-btn, .linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
    $(".manageInvBC .fa").click(function () {
        let has = $(this).hasClass('fa-dot-circle-o')
        $(".manageInvBC .fa").attr('class','fa fa-circle-o')
        has ? $(this).attr('class','fa fa-circle-o') : $(this).attr('class','fa fa-dot-circle-o')
        let endDateFm = $(".endDateFm .fa-dot-circle-o")
        if(endDateFm.length > 0){ // 选择了  修改问卷提交的截止日期
            $("#manageInv .timCC").show()
        }
    })
})
function allDataBtn() {
    $(".qAnsList").data('info', '{}')
    $(".mainCon").hide();
    $(".mainCon1313").show();
    getYearAreaDetails(1)
}
function getYearAreaDetails(cur) {
    let info = $(".qAnsList").data('info')
    info = JSON.parse(info)

    let question = $("#analysisAns").data("question");
    let publish = $("#analysisAns").data("publish");
    let newType = $(".qAnsList").data('type')
    let content = info.contentStr
    console.log('info=', info)
    let data = {
        publish: publish,
        question:  question.id,
        type: newType,
        content: content,
        pageSize:20, currentPageNo:cur
    }
    console.log('data=', data)
    $.ajax({
        "url":"../investigationPublish/areaListByQuestion.do",
        "data": data ,
        success:function (res) {
            let tipStr
            if(data.content){

                $(".qInfo13").html('')
                tipStr = `该${ res.answerSum }位问卷的提交者信息如下：`
            }else{
                tipStr = `回收的问卷中，${ res.answerSum }份回答了本题，具体如下：`
                $(".qInfo13").html(question.orders + '、' + question.content)

            }
            $(".qwe13").html(tipStr)
            let ttlList = res.investigatePublishShowList || []
            let list = res.respInvestigateObjectList || []
            let ttlStr = '', ansStr = ''
            ttlList.forEach(ttl=>{
                ttlStr += `<td>${ ttl.content }</td>`
            })
            list.forEach(ii => {
                let tdList = ii.respInvestigateAnswerList
                if(tdList.length !== ttlList.length){
                    let newArr = []
                    for(let i=tdList.length; i<ttlList.length; i++){
                        newArr.push({ content: '' })
                    }
                    ii.respInvestigateAnswerList.push(...newArr)
                }
            })
            ansStr = handleList(list)
            let tabTtl = `<tr><td>提交时间</td>${ ttlStr }</tr>`
            let str = `
                <table class="ty-table">
                    ${ tabTtl }
                    ${ ansStr }
                </table>
                `;
            $(".mainCon1313 .qAnsList13").html(str);

            let pageInfo = res.pageInfo ;
            setPage($(".page13"), pageInfo.currentPageNo, pageInfo.totalPage, "getYearAreaDetails", "");


        }
    })
    
}

function notJiData(obj) {
    $(".notJiData").hide()
    $(".exportDataSet").hide()
    $(".back88").hide()
    $(".back881").show()
    let info = $(".mainCon88").data("source");
    getCompleteSubjectList(info.id , 1, '未纳入统计')
}
// creator:hxz 2024-07-24 返回 纳入统计的页面
function back881(obj) {
    $(".notJiData").show()
    $(".exportDataSet").show()
    $(".back88").show()
    $(".back881").hide()

    let info = $(".mainCon88").data("source");
    getCompleteSubjectList(info.id , 1 )

}
// creator:hxz 2024-07-24 不纳入统计
function notJiBtn(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    let type = obj.data('type')
    // id 应答对象id（object的id）
    // type 1 将状态修改为纳入统计，0 将状态修改为未纳入统计
    $.ajax({
        "url":"../investigationPublish/updateObjectSubsumable.do",
        "data":{ "id": info.id, type: type },
        success:function (res) {
            // status 1成功，0失败
            let status = res.status
            if(status == 1){
                let tip = '本条数据已进入“未纳入统计的数据”！'
                if(type == 1){
                    tip = '本条数据已恢复纳入统计！'
                }
                layer.msg(tip)
                let yeStr = $("#page88").find('.json').html()
                let yecur = Number($("#page88").find('.yecur').html())
                let yeJson = JSON.parse(yeStr)
                getCompleteSubjectList(yeJson.id , yecur, yeJson.typeStr)
            }else{
                layer.msg('操作')
            }

        }
    })
}

function share(obj) {
    layer.msg('友情提示：分享需使用手机端！')
    // /investigationPublish/getQRLink.do
    // 传值2个，id（调查表id） expireDate: time有效期，时间值.getTime()

    let info = obj.parent().next().find(".hd").html();
    info = JSON.parse(info)
    console.log('QR info=', info)
    $.ajax({
        "url":"../investigationPublish/getQRLink.do",
        "data":{ "expireDate": info.qrDeadline , 'id': info.id },
        success:function (res, status, xhr) {
            console.log('QR res=', res)
            console.log(res.data)

        }
    })
}
function searchBtn1() {
    let searchKey = $("#searchKey").val()
    getListBySearch(searchKey,1 )
}
function getListBySearch(searchKey,currentPageNo ) {
    $.ajax({
        "url":"../investigationPublish/selectPublishByKeywordList.do",
        "data":{
            "keyword" : searchKey,
            "currentPageNo" : currentPageNo,
            "pageSize" : pageSize,
        },
        success:function(res){
            let list = res.investigatePublishList || []
            let sumPage = res.pageInfo.totalPage || 0
            let currentPageNo2 = res.pageInfo.currentPageNo || currentPageNo
            let sum = res.sum || 0
            let str = ``
            list.forEach(function(invst){
                invst.isStop = false
                if(status===0){
                    invst.isStop = true
                }
                str += `
                 <tr>
                    <td>${ invst.name }</td>
                    <td>${ invst.createName } ${ new Date(invst.createDate ).format("yyyy-MM-dd hh:mm:ss")}</td>
                    <td>${ new Date(invst.endTime ).format("yyyy-MM-dd")}</td>
                    <td class="ty-td-control">
                        <span>${ invst.feedbackNum }份</span>
                        <span class="ty-color-blue funBtn" data-fun="scanAns">查看全部</span>
                        <span class="ty-color-blue funBtn" data-fun="analysisAns">调查分析</span>
                    </td>
                    <td>
                        <span class="ty-color-blue funBtn" data-fun="manageInv">管理 </span>
                        <span class="hd">${ JSON.stringify(invst) }</span>
                    </td>
                </tr>
                `
            })
            let info = JSON.stringify({
                "status" : status,
                "currentPageNo" : currentPageNo,
                "keyword": searchKey ,
                "pageSize" : pageSize });
            $("#page1Search").show()

            $(".mainCon22").show()
            $(".mainCon11").hide()
            $(".mainCon22 .num1").html(sum)
            setPage($("#page1Search"),currentPageNo2,sumPage,'investQRSearch',info)
            $(".mainCon22 table tr:gt(0)").remove();
            $(".mainCon22 table").append(str);
        }
    })
}
// creater : hxz 2021-08-07 主列表
function getList(status, currentPageNo , month) {
    $.ajax({
        "url":"../investigationPublish/selectPublishList.do",
        "data":{
            "status" : status,
            "currentPageNo" : currentPageNo,
            "month" : month ,
            "pageSize" : pageSize,
        },
        success:function(res){
            let list = res.investigatePublishList || []
            let sumPage = res.pageInfo.totalPage || 0
            let currentPageNo2 = res.pageInfo.currentPageNo || currentPageNo
            let sum = res.sum || 0
            let str = ``
            list.forEach(function(invst){
                invst.isStop = false
                invst.status = status
                if(status===0){
                    invst.isStop = true
                }
                str += `
                 <tr>
                    <td>${ invst.name }</td>
                    <td>${ invst.createName } ${ new Date(invst.createDate ).format("yyyy-MM-dd hh:mm:ss")}</td>
                    <td>${ new Date(invst.endTime ).format("yyyy-MM-dd")}</td>
                    <td class="ty-td-control">
                        <span>${ invst.feedbackNum }份</span>
                        <span class="ty-color-blue funBtn" data-fun="scanAns">查看全部</span>
                        <span class="ty-color-blue funBtn" data-fun="analysisAns">调查分析</span>
                        <span class="ty-color-blue funBtn" data-fun="share">分享</span>
                    </td>
                    <td>
                        <span class="ty-color-blue funBtn" data-fun="manageInv">管理 </span>
                        <span class="hd">${ JSON.stringify(invst) }</span>
                    </td>
                </tr>
                `
                //     <span class="ty-color-blue funBtn" data-fun="scanInv">查看问卷 </span>
                // <span class="ty-color-blue funBtn" data-fun="qrManage" data-sta="${status}">二维码管理</span>
                // ${ status===0 ? ``: `<span class="ty-color-red funBtn" data-fun="stopInv">终止本次调查</span>` }

            })
            let info = JSON.stringify({
                "status" : status,
                "currentPageNo" : currentPageNo,
                "month" : month ,
                "pageSize" : pageSize });
            if( status===0){
                $(".num6").html(sum)
                setPage($("#page66"),currentPageNo2,sumPage,'investQR',info)
                $(".mainCon66 table tr:gt(0)").remove();
                $(".mainCon66 table").append(str);
                $(".mainCon66 .month").html(month);
            }else{
                $(".num1").html(sum)
                setPage($("#page11"),currentPageNo2,sumPage,'investQR',info)
                $(".mainCon11 table tr:gt(0)").remove();
                $(".mainCon11 table").append(str);
            }
        }
    })
}
// creator:hxz 2023-11-15 管理
var editObj = null
function manageInvOk(obj) {
    let info =  $("#manageInv").data('info')
    let data = { id: info.id }
    data.type = 1  // type为1只修改允许修改时间，为2有截止日期修改，为3立即终止
    data.expireDate = ''   // 截止日期
    data.modifyLimit = $("#manage_upTim").val() // 允许修改时间
    let endDateFm = $(".endDateFm .fa-dot-circle-o")
    if(endDateFm.length > 0){ // 选择了  修改问卷提交的截止日期
        let d = $("#upEndDate").val()
        data.expireDate = new Date(d).getTime()
        data.type = 2
    }else{
        let endTestCC = $(".endTestCC .fa-dot-circle-o")
        if(endTestCC.length > 0){
            data.type = 3
        }
    }
    bounce.cancel()
    $.ajax({
        'url':"../investigationPublish/managePublish.do",
        'data':data,
        success:function(res){
            console.log('res=', res)
            if(res.status === 1){
                layer.msg('操作成功！')

                let yeConObj = editObj.parents(".mainCon").find('.yeCon')
                let type = yeConObj.siblings('.type').html()
                let pageInfo = yeConObj.siblings('.json').html()
                pageInfo = JSON.parse(pageInfo)
                if(type === 'investQRSearch'){
                    let searchKey = $("#searchKey").val()
                    getListBySearch(searchKey,pageInfo.currentPageNo )
                }else if(type === 'investQR'){
                    getList(pageInfo.status, pageInfo.currentPageNo ,pageInfo.month)
                }


            }else{
                layer.msg('操作失败！')
            }
        }
    })
}
// creator:hxz 2022-03-24 调查分析
function back99() {
    $(".mainCon").hide();
    $(".mainCon11").show();
}
// creator:hxz 2022-03-24 调查分析
function analysisAns(obj) {
    let info = obj.parent().next().find(".hd").html();
    info = JSON.parse(info)
    $(".mainCon").hide();
    $(".mainCon99").show();
    $("#analysisAns").data("publish", info.id);
    $(".mainCon99 .ansNum").html(info.feedbackNum);
    $.ajax({
        "url":"../investigationPublish/investigateAnswerStatistics.do",
        "data":{ "publish": info.id },
        success:function (res, status, xhr) {
            let timer = xhr.getResponseHeader('Date');
            $(".endTime").html(new Date(timer).format("yyyy-MM-dd hh:mm:ss"));
            $(".mainCon99 .ansNum").html(res.objectCount);
            $(".mainCon99 .q1Num").html(res.essayQuestionCount);
            $(".mainCon99 .q2Num").html(res.singleChoiceCount);
            $(".mainCon99 .q3Num").html(res.multipleChoiceCount);
            let qMun = Number(res.essayQuestionCount) + Number(res.singleChoiceCount) + Number(res.multipleChoiceCount) ;
            $(".mainCon99 .qMun").html(qMun);
        }
    })

}
// creator:hxz 2022-03-24 调查分析 -  查看题型
function getQAnsysList(obj) {
    let publish = $("#analysisAns").data("publish"); // 题库ID
    let type = obj.data("type");
    $(".mainCon").hide();
    $(".mainCon1010").show();
    $(".df1").html($(".df12").html());
    $(".type" + type).show().siblings().hide();
    $("#analysisAns").data("type", type);
    getgetQAnsysListByType(publish, type, 1)
}
// creator:hxz 2022-03-24 调查分析 -  查看题型 列表
function getgetQAnsysListByType(publish, type, cur){
    $.ajax({
        "url":"../investigationPublish/investigateQuestionStatistics.do",
        "data":{ "publish": publish, "type": type, "currentPageNo": cur, "pageSize": 20},
        success:function (res) {
            let list = res.respInvestigateQuestionList || [];
            $(".q1Num2").html(res.essayQuestionCount)
            $(".q2Num2").html(res.singleChoiceCount)
            $(".q3Num2").html(res.multipleChoiceCount)

            let str = ``
            list.forEach(function (item) {
                if(type == 1){
                    str += `
                        <div class="line">
                            <p>${ item.orders }、 ${ item.content }</p>
                            <p>
                                <span>回收的问卷中，${ item.answerSum }份回答了本题 </span>
                                <span class="linkBtn seeThisAnsBtn" data-fun="seeThisAns">查看本题的全部回答</span>
                                <span class="hd">${ JSON.stringify(item) }</span>
                            </p>
                        </div>
                        `
                }
                else if(type == 2 || type == 3){
                    let options = item.respInvestigateQuestionKeyList || [] ;
                    let optionStr = ``
                    options.forEach(function (option) {
                        optionStr += `<tr><td class="oneLine">${option.content}</td><td>${option.sum}</td><td>${ (option.percentage *100).toFixed(2) }%</td></tr>`
                    })
                    let contrlStr = ``;
                    if(type == 3){
                        contrlStr = `<span class="linkBtn" style="margin-left: 200px;" data-fun="changeToNext">切换至答卷者选择结果的统计</span>`
                    }
                    str += `
                        <div class="line">
                            <span class="hd">${ JSON.stringify(item) }</span>
                            <p>${ item.orders }、 ${ item.content }</p>
                            <p>回收的问卷中，${ item.answerSum }份回答了本题 ，情况如下：${ contrlStr }</p>
                            <table class="ty-table">
                                <tr><td class="oneLine">选项</td><td width="150">选择该项的份数</td><td width="150">占比</td></tr>
                                ${ optionStr }
                            </table>
                        </div>
                        `
                }
            })
            $(`.type${ type }List`).html(str);
            let jsonD = { "publish": publish,  "type": type }
            let pageInfo = res.pageInfo ;
            setPage($(".pagetype" + type), pageInfo.currentPageNo, pageInfo.totalPage, "getgetQAnsysListByType", JSON.stringify(jsonD));
        }
    })
}
function back1010() {
    $(".mainCon").hide();
    $(".mainCon99").show();
}
function back1111() {
    $(".mainCon").hide();
    $(".mainCon1010").show();

}
function back1313() {
    $(".mainCon").hide();
    $(".mainCon1111").show();

}

// creator:hxz 2022-03-25 切换至答卷者选择结果的统计报表
function changeToNext(obj) {
    $(".mainCon").hide();
    $(".mainCon1212").show();
    $(".mainCon1212 .chang1").html(obj.prev().html());
    let question = obj.parents(".line").find(".hd").html();
    question = JSON.parse(question)
    $("#analysisAns").data("question", question);
    let publish = $("#analysisAns").data("publish"); // 题库ID
    $(".mainCon1212 .questionInfo").html(`${ question.orders }、 ${ question.content }`);
    $.ajax({
        "url":"../investigationPublish/answerListByMultipleChoiceQuestion.do",
        "data":{ "question": question.id , "publish":publish },
        success:function (res) {
            let objectSum = res.objectSum ; // 总回收数
            let sum = res.sum  ; // 回答本题人数
            $(".mainCon1212 .pall").html(objectSum );
            $(".mainCon1212 .pans").html(sum );
            let ques = res.respInvestigateQuestion || [] ; // 题目详情
            let ops = ques.investigateQuestionKeyList || [] ; // options
            let ansList = res.respAnswerCountList || [] ; // ans
            let opStr = ``
            ops.forEach(function (op) {
                opStr += `<tr><td><span class="circleOrd">${ op.orders }</span> ${ op.content }</td></tr>`
            })
            $(".mainCon1212 .optionTab tbody tr:gt(0)").remove();
            $(".mainCon1212 .optionTab").append(opStr);
            let ansStr = ``
            ansList.forEach(function (ans) {
                ansStr += `<tr><td>${ formatOption(ans.answer, ops) }</td><td>${ ans.sum }</td><td>${ (ans.percentage *100).toFixed(2)  }%</td></tr>`
            })
            $(".mainCon1212 .ansTab tbody tr:gt(0)").remove();
            $(".mainCon1212 .ansTab").append(ansStr);

        }
    })

}
function formatOption(answer,options) {
    let answerArr = answer.split(',')
    let ansStr = ``
    answerArr.forEach(function (ansID) {
        options.forEach(function (op) {
            if(ansID == op.id){
                ansStr += `<span class="circleOrd">${ op.orders }</span>`
            }
        })
    })
    return ansStr
}
// creator:hxz 2022-03-25 导出
function exportBtn(obj) {

    let place = obj.data("place")
    let publish = $("#analysisAns").data("publish"); // 题库ID
    let question = $("#analysisAns").data("question"); // 题目详情
    let type = $("#analysisAns").data("type"); // 题库某类型的查看 1问答,2单选,3多选
    let data = { "publish": publish }
    let queryStr = `publish=${publish}`
    let url = ''
    if(place == 1){
        data['type'] = type
        url = '../investigationPublish/exportExcelQuestionStatistics.do'
        queryStr += `&type=${type}`

    }
    else if(place == 2){
        queryStr += `&question=${question.id}`
        data['question'] = question.id
        url = '../investigationPublish/exportExcelEssayQuestion.do'
        // publish 调查id
        // question 问题id
        // type 1普通问答题 3地址问答题，4 日期问答题
        // （此处的type是question的type为9时，special_model为3，是地址，为4，是日期，其他情况均目前传1）

        if(question.type == '1'){
            queryStr += `&type=1`
            data['type'] = 1
        }else if(question.type == '9'){
            if(question.isMultiple == 8){ // 可以多次输入的题
                data['type'] = 1
                queryStr += `&type=1`
            }else if(question.specialModel == '1'){ // 录入必须为电子邮箱格式的问答题
                data['type'] = 1
                queryStr += `&type=1`
            }else if(question.specialModel == '2'){ // 录入必须为11位的手机号的问答题
                data['type'] = 1
                queryStr += `&type=1`
            }else if(question.specialModel == '3'){ // 省、市、地区或地址的题-
                data['type'] = 3
                queryStr += `&type=3`
            }else if(question.specialModel == '4'){ // 年、月或日期的题
                data['type'] = 4
                queryStr += `&type=4`
            }
        }
    }
    else if(place == 3){
        queryStr += `&question=${question.id}`
        data['question'] = question.id
        url = '../investigationPublish/exportExcelMultipleChoiceQuestion.do'
    }
    else if(place == 4){
        queryStr += `&question=${question.id}`
        data['question'] = question.id
        url = '../investigationPublish/exportExcelAreaListByQuestion.do'
        // type 3地址问答题，4 日期问答题 special_model为3，是地址，为4，是日期，其他情况均目前传1）
        // content 值 （比如日期是查询2024年的，此处传‘2024年’，地址为天津市此处传天津市，如果地址为二级和平区，此处传 天津市 / 和平区）如果是查询全部，此处不传
        let info = $(".qAnsList").data('info')
        info = JSON.parse(info)
        let newType = $(".qAnsList").data('type')
        let content = info.contentStr
        if(content){
            queryStr += `&type=${ newType }&content=${ content }`
        }else{
            queryStr += `&type=${ newType }`
        }
        data.type = newType
        data.content = content
    }

    var oReq = new XMLHttpRequest();
    oReq.open("GET", `${ url }?${ queryStr }`, true);
    oReq.responseType = "blob";
    oReq.onload = function (oEvent) {
        var content = oReq.response;
        var elink = document.createElement('a');
        elink.download = `调查分析.xls`;
        elink.style.display = 'none';
        var blob = new Blob([content]);
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        document.body.removeChild(elink);
        loading.close();
    };
    oReq.send();
}
// creator:hxz 2022-03-24 调查分析 -  查看题型 问答题
function seeThisAns(obj) {
    $(".mainCon").hide();
    $(".mainCon1111").show();
    let info = obj.siblings(".hd").html();
    info = info && JSON.parse(info)
    let txt = obj.siblings("span").html();
    $(".mainCon1111 .qwe").html( txt + '，具体如下：');
    $(".mainCon1111 .qInfo").html(`${ info.orders }、 ${ info.content }`);
    $("#analysisAns").data("question", info); // 题目ID
    getQAnsList(1)
}
function getQAnsList(cur) {
    let question = $("#analysisAns").data("question");
    let publish = $("#analysisAns").data("publish");

    let newType = ''
    if(question.type === '1'){ // 填空
        newType = 1
    }else if(question.type === '9'){ // 特殊题型
        if(question.isMultiple == '8'){ newType = 1 }
        else if(question.specialModel === '1'){ newType = 1 }
        else if(question.specialModel === '2'){ newType = 1 }
        else if(question.specialModel === '3'){ newType = 3 }
        else if(question.specialModel === '4'){ newType = 4 }
    }
    $(".qAnsList").data('type', newType)
    if(newType === 1){
        $(".allDataBtn").hide()
    }else{
        $(".allDataBtn").show()
    }
    $.ajax({
        "url":"../investigationPublish/investigateAnswerListByQuestion.do",
        "data":{ "publish": publish, "question": question.id, "type": newType, "currentPageNo": cur, "pageSize": 40 },
        success:function (res) {
            let answerSum = res.answerSum || 0
            let objSum = res.objSum || 0
            let tipAtr = `${ objSum || '' }份问卷中，${ answerSum }份回答了本题，具体如下：`

            let ttlList = res.investigatePublishShowList || [];
            let ttlStr = ''
            ttlList.forEach(ttl=>{
                ttlStr += `<td>${ ttl.content }</td>`
            })

            let list = res.respInvestigateObjectList || [];
            let ansStr = ``;
            let tabTtl = ``;
            if (question.type == '3'|| question.type === '2') { // 单选， 判断

            }else if(question.type == '4'){ // 多选

            }else if(question.type == '1'){ // 填空
                tabTtl = `<tr><td>提交时间</td>${ ttlStr }</tr>`
                ansStr = handleList(list)
            }
            else if(question.type == '9') { // 特殊题型
                if (question.isMultiple == 8) { // 可以多次输入的题
                    tabTtl = `<tr><td>提交时间</td>${ ttlStr }</tr>`
                    ansStr = handleList(list)
                    $(".allDataBtn").hide()

                } else if (question.specialModel == '1') { // 录入必须为电子邮箱格式的问答题
                    tabTtl = `<tr><td>提交时间</td>${ ttlStr }</tr>`
                    ansStr = handleList(list)
                    $(".allDataBtn").hide()

                } else if (question.specialModel == '2') { // 录入必须为11位的手机号的问答题
                    tabTtl = `<tr><td>提交时间</td>${ ttlStr }</tr>`
                    ansStr = handleList(list)
                    $(".allDataBtn").hide()

                } else if (question.specialModel == '3') { // 省、市、地区或地址的题-
                    $(".allDataBtn").show()
                    tipAtr = `回收的问卷中，${ answerSum }份回答了本题，统计如下：`
                    list = res.investigateAreaList || []
                    if (question.specialTab === '4' || question.specialTab === '3') { //  4-加具体地址的， 3-省市区
                        tabTtl = `<tr><td>省</td><td>市</td><td>地区</td></tr>`
                        let str3 = ''
                        list.forEach(areaI =>{ // 省
                            areaI.contentStr = areaI.name
                            let cityList = areaI.investigateAreaList // 市
                            let provinceRowspanNum = 0
                            cityList.forEach((cityItem, cityIndex)=>{
                                cityItem.contentStr = areaI.name + ' / ' + cityItem.name
                                let quList = cityItem.investigateAreaList || []
                                let cityRowspanNum = quList.length || 1
                                provinceRowspanNum += Number(cityRowspanNum)
                                if(quList.length > 0){
                                    quList.forEach((quItem, quindex)=>{
                                        quItem.contentStr = cityItem.contentStr + ' / ' + quItem.name
                                        str3 += `<tr>
                                            ${( cityIndex === 0 && quindex === 0 ) ? `<td rowspan="provinceRowspanNum">${ areaI.name } <span class="ty-color-blue">${ areaI.sum }位</span><span class="hd">${ JSON.stringify(areaI) }</span></td>` :'' }
                                            ${ quindex === 0 ? `<td rowspan="${cityRowspanNum}">${ cityItem.name } <span class="ty-color-blue">${ cityItem.sum }位</span><span class="hd">${ JSON.stringify(cityItem) }</span></td>` :'' }
                                            <td>${ quItem.name } <span class="ty-color-blue">${ quItem.sum }位</span><span class="hd">${ JSON.stringify(quItem) }</span></td></td>
                                        </tr>`
                                    })
                                }else{
                                    str3 += `<tr>
                                            ${ cityIndex === 0 ? `<td rowspan="provinceRowspanNum">${ areaI.name } <span class="ty-color-blue">${ areaI.sum }位</span><span class="hd">${ JSON.stringify(areaI) }</span></td>` :'' }
                                            <td rowspan="${cityRowspanNum}">${ cityItem.name } <span class="ty-color-blue">${ cityItem.sum }位</span><span class="hd">${ JSON.stringify(cityItem) }</span></td>
                                            <td></td>
                                        </tr>`
                                }
                            })
                            let str3Arr = str3.split("provinceRowspanNum")
                            str3 = str3Arr[0] + provinceRowspanNum + str3Arr[1]
                        })
                        ansStr = str3
                    }
                    else if(question.specialTab ==='1'){ // 省
                        tabTtl = `<tr><td>省</td></tr>`
                        let str1 = ''
                        list.forEach(areaI =>{
                            areaI.contentStr = areaI.name
                            str1 += `<tr><td>${ areaI.name } <span class="ty-color-blue">${ areaI.sum }位</span><span class="hd">${ JSON.stringify(areaI) }</span></td></tr>`
                        })
                        ansStr = str1
                    }
                    else if(question.specialTab ==='2'){ // 市
                        tabTtl = `<tr><td>省</td><td>市</td></tr>`
                        let str2 = ''
                        list.forEach(areaI =>{
                            areaI.contentStr = areaI.name
                            let cityList = areaI.investigateAreaList || []
                            let rowspanStr = cityList.length > 1 ? `rowspan="${ cityList.length }"` : ''
                            cityList.forEach((cityItem, cityIndex)=>{
                                cityItem.contentStr = areaI.name + ' / ' + cityItem.name
                                str2 += `<tr>
                                    ${ cityIndex === 0 ? `<td ${rowspanStr}>${ areaI.name } <span class="ty-color-blue">${ areaI.sum }位</span><span class="hd">${ JSON.stringify(areaI) }</span></td>` : ''} 
                                    <td >${ cityItem.name } <span class="ty-color-blue">${ cityItem.sum }位</span><span class="hd">${ JSON.stringify(cityItem) }</span></td>
                                    </tr>`
                            })
                        })
                        ansStr = str2

                    }
                }
                else if (question.specialModel == '4') { // 年、月或日期的题
                    tipAtr = `回收的问卷中，${ answerSum }份回答了本题，统计如下： `
                    list = res.investigateAreaList || []
                    $(".allDataBtn").show()
                    if (question.specialTab == '1') { // 选项为年历
                        tabTtl = `<tr><td>年</td></tr>`
                        let str1 = ''
                        list.forEach(areaI =>{
                            areaI.contentStr = areaI.name
                            str1 += `<tr><td>${ areaI.name } <span class="ty-color-blue">${ areaI.sum }位</span><span class="hd">${ JSON.stringify(areaI) }</span></td></tr>`
                        })
                        ansStr = str1

                    }
                    else if (question.specialTab == '2') { // 选项为月历
                        tabTtl = `<tr><td>年</td><td>月</td></tr>`
                        let str2 = ''
                        list.forEach(areaI =>{
                            areaI.contentStr = areaI.name
                            let cityList = areaI.investigateAreaList || []
                            let rowspanStr = cityList.length > 1 ? `rowspan="${ cityList.length }"` : ''
                            cityList.forEach((cityItem, cityIndex)=>{
                                cityItem.contentStr = areaI.name + cityItem.name
                                str2 += `<tr>
                                    ${ cityIndex === 0 ? `<td ${rowspanStr}>${ areaI.name } <span class="ty-color-blue">${ areaI.sum }位</span><span class="hd">${ JSON.stringify(areaI) }</span></td>` : ''} 
                                    <td >${ cityItem.name } <span class="ty-color-blue">${ cityItem.sum }位</span><span class="hd">${ JSON.stringify(cityItem) }</span></td>
                                    </tr>`
                            })
                        })
                        ansStr = str2

                    }
                    else if (question.specialTab == '3') { // 选项为日历
                        tabTtl = `<tr><td>年</td><td>月</td><td>日</td></tr>`
                        let str3 = ''
                        list.forEach(areaI =>{ // 省
                            areaI.contentStr = areaI.name
                            let cityList = areaI.investigateAreaList // 市
                            let provinceRowspanNum = 0
                            cityList.forEach((cityItem, cityIndex)=>{
                                cityItem.contentStr = areaI.name + cityItem.name
                                let quList = cityItem.investigateAreaList || []
                                let cityRowspanNum = quList.length || 1
                                provinceRowspanNum += Number(cityRowspanNum)
                                if(quList.length > 0){
                                    quList.forEach((quItem, quindex)=>{
                                        quItem.contentStr = cityItem.contentStr + quItem.name
                                        str3 += `<tr>
                                            ${( cityIndex === 0 && quindex === 0 ) ? `<td rowspan="provinceRowspanNum">${ areaI.name } <span class="ty-color-blue">${ areaI.sum }位</span><span class="hd">${ JSON.stringify(areaI) }</span></td>` :'' }
                                            ${ quindex === 0 ? `<td rowspan="${cityRowspanNum}">${ cityItem.name } <span class="ty-color-blue">${ cityItem.sum }位</span><span class="hd">${ JSON.stringify(cityItem) }</span></td>` :'' }
                                            <td>${ quItem.name } <span class="ty-color-blue">${ quItem.sum }位</span><span class="hd">${ JSON.stringify(quItem) }</span></td></td>
                                        </tr>`
                                    })
                                }else{
                                    str3 += `<tr>
                                            ${ cityIndex === 0 ? `<td rowspan="provinceRowspanNum">${ areaI.name } <span class="ty-color-blue">${ areaI.sum }位</span><span class="hd">${ JSON.stringify(areaI) }</span></td>` :'' }
                                            <td rowspan="${cityRowspanNum}">${ cityItem.name } <span class="ty-color-blue">${ cityItem.sum }位</span><span class="hd">${ JSON.stringify(cityItem) }</span></td>
                                            <td></td>
                                        </tr>`
                                }
                            })
                            let str3Arr = str3.split("provinceRowspanNum")
                            str3 = str3Arr[0] + provinceRowspanNum + str3Arr[1]
                        })
                        ansStr = str3
                    }
                }
            }

            let str = `
                <table class="ty-table">
                    ${ tabTtl }
                    ${ ansStr }
                </table>
                `;
            $(".mainCon1111 .qAnsList").html(str);
            $(".mainCon1111 .qwe").html(tipAtr);

            let pageInfo = res.pageInfo ;
            setPage($(".page11"), pageInfo.currentPageNo, pageInfo.totalPage, "getgetQAnsysListById", "");

        }
    })

}
function handleList(list){
    let str = ''
    list = list || []
    list.forEach(item=>{
        str += `<tr>`
        str += `<td>${ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss") }</td>`
        let tdList = item.respInvestigateAnswerList
        tdList.forEach(ansI=>{
            str += `<td>${ ansI.content || '' }</td>`
        })
        str += `</tr>`
    })
    return str
}
// creator:hxz 2021-08-07 查看全部问卷
function scanAns(obj) {
    let info = obj.parent().next().find(".hd").html();
    info = JSON.parse(info)
    let isStop = info.isStop
    $(".back88").data("isstop", isStop)
    $(".mainCon").hide();
    $(".mainCon88").show();
    $(".mainCon88").data("source", info);

    $(".notJiData").show()
    $(".exportDataSet").show()
    $(".back88").show()
    $(".back881").hide()

    getCompleteSubjectList(info.id , 1)
}
function back88() {
    if($(".back88").data("isstop")){ // 停用的
        $(".mainCon").hide()
        $(".mainCon66" ).show()
    }else{ // 进行中的
        $(".mainCon").hide()
        $(".mainCon11" ).show()

    }

}
function back66() {
    $(".mainCon").hide()
    $(".mainCon55" ).show()
}
function getCompleteSubjectList(id , currentPageNo, typeStr){
    // typeStr: '未纳入统计'
    let type = typeStr ? 0 : 1 ; // type 为1时，纳入统计，为0时，未纳入统计
    // completeSubjectList.do 接口，查看全部接口，传值不变，
    // 返回值，取消原有 showvalue 传值，更换为
    // respInvestigateAnswerList[question问题id,content显示内容]
    // 当 question 等于 showlist 里面的 question 时，显示为该人回答该题内容

    $.ajax({
        "url":"../investigationPublish/completeSubjectList.do",
        "data":{ "id": id , "pageSize": pageSize , "currentPageNo": currentPageNo, type: type },
        success:function (res, status, xhr) {
            let tags = res.investigatePublishShowList || []
            let list = res.respInvestigateObjectList || []
            let totalPage = res.pageInfo.totalPage || 0
            let totalResult = res.pageInfo.totalResult || 0
            let currentPageNo2 = res.pageInfo.currentPageNo || currentPageNo
            let timer = xhr.getResponseHeader('Date');
            $(".mainCon88 .time").html(new Date(timer).format("yyyy-MM-dd hh:mm:ss"));
            $(".mainCon88 .num").html(totalResult);
            $(".mainCon88 table tr:gt(0)").remove();
            $(".mainCon88 table tr>td:gt(0)").remove();
            let tagStr = ``
            tags.forEach(function (item) {
                tagStr += `<td title="${ item.content  }"><span class="lineDot" >${ item.content }</span></td>`
            })
            tagStr += `<td>操作</td>`
            $(".mainCon88 table tr").append(tagStr);
            let noJistr = `<span class="ty-color-blue funBtn" data-type="0" data-fun="notJiBtn">不纳入统计</span>`
            if(typeStr === '未纳入统计'){
                noJistr = `<span class="ty-color-blue funBtn" data-type="1" data-fun="notJiBtn">恢复纳入统计</span>`
            }
            let str = ''
            list.forEach(function (item) {
                str += `
                <tr>
                    <td>${ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss") }</td>
                    ${ setTD(item, tags) }
                    <td>
                        <span class="ty-color-blue funBtn" data-fun="scanAnsInfo">查看</span>
                        ${ noJistr }
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
            `
            })
            $(".mainCon88 table").append(str);
            let len =
            $("#tab88").attr('style',`width:${  200*tags.length + 400 }px`);
            let info = {
                "id":id,
                "typeStr":typeStr,
            }
            setPage($("#page88"),currentPageNo2,totalPage,"completeSubject", JSON.stringify(info))

        }
    })
}
function scanAnsInfo(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    $.ajax({
        "url":"../investigationPublish/selectObjectById.do",
        "data":{ "id": info.id },
        success:function (res) {
            let list = res.respInvestigateSubjectTagList;
            let header = res.header || ""
            let title = res.title || ""
            let str = ``
            list.forEach(function(cat){
                let quesStr = ``;
                let quess = cat.investigateQuestionList
                let firstIndex = quess[0]['orders']
                quess.forEach(function(q){
                    quesStr += `
                        <div class="ques">
                            <div>${ q.orders }、${ q.content  }</div>
                            <div class="ops">${ setAnsOption(q) }</div>
                        </div>
                    `
                })
                str += `
                    <div class="cat">
                        <h4>${ cat.name || "" }</h4>
                        ${ quesStr }
                    </div>
                `
            })
            $(".mainCon77 .main").html(str);
            $(".mainCon77 .ttl").html(title);
            $(".mainCon77 .preface").html(header);
            $(".mainCon").hide()
            $(".mainCon77").show()
            $(".mainCon77 .back77").data('backto','88')

        }
    })
}
function setAnsOption(q) {
    let str = ``
    let ansArr = q.investigateAnswerList || []
    ansArr.forEach(function(ans){
        str += ans.content + "<br/>"
    })
    // if([2,3,4].indexOf(q.type) ){
    //     let ops = q.investigateQuestionKeyList || []
    //     ops.forEach(function(op, index){
    //         str += `
    //         <div>${index +1 }. ${ op.content }</div>
    //         `
    //     })
    // }else{
    //     str = q.investigateAnswerList[0]['content']
    // }
    return str
}
function setTD(item, tags){
    let respInvestigateAnswerList = item.respInvestigateAnswerList || []

    let str = ``
    respInvestigateAnswerList.forEach( QaA => {
        tags.forEach(tag => {
            if(QaA.question === tag.question){
                str += `<td style="width: 200px;">${ QaA.content || '- -' }</td>`
            }
        })
    })

    return str
}
// creator:hxz 2021-09-03 导出s设置
function exportDataSet(obj) {
    bounce.show($("#exportSet"))
    let info = $(".mainCon88").data("source");
    $.ajax({
        "url": "../investigationPublish/selectQuestionOptionsList.do",
        "data":{ 'id': info.id },
        success:function (res) {
            let list = res, str = ``,showSpan=``
            list.forEach(function (item, index) {
                if(item.isShow === 1){
                    showSpan  += index === 0 ? `“${ item.content }”` : `与“${ item.content }”`;
                }
                str += `
                <tr>
                    <td>${ item.orders }</td>
                    <td>${ item.content }</td>
                    <td>${ item.isShow === 1 ? '已选中' : '- -' }</td>
                    <td>
                        <span class="hd">${ JSON.stringify(item) }</span>
                        <span data-fun="toggleSelectExport" class="funBtn ${ item.isShow === 1 ? 'ty-color-red' : 'ty-color-blue' }">${ item.isShow === 1 ? '放弃' : '选择' }</span>
                    </td>
                </tr>
                `
            })
            $("#exportSet table tr:gt(0)").remove()
            $("#exportSet table").append(str)
            $("#exportSet .keyShow").html(showSpan)
        }
    })
}
// creator:hxz 2021-09-03 导出
function exportData(obj) {
    let len = $("#exportSet table .ty-color-red").length
    if(len === 0){
        layer.msg('请最少选择一项！')
        return false
    }
    let updateQuestionJson = []
    $("#exportSet table .ty-color-red").each(function(){
        let info2 = $(this).siblings(".hd").html();
        info2 = JSON.parse(info2)
        updateQuestionJson.push({'question': info2.id})
    })
    // /investigationPublish/exportExcel.do
    // 传值 id(publish)
    // 返回文件
    let info = $(".mainCon88").data("source");

    loading.open();


    var xhr = new XMLHttpRequest();
    var url = "../investigationPublish/exportExcel.do";
    var data = JSON.stringify({ id: info.id, updateQuestionJson: JSON.stringify(updateQuestionJson) }); // JSON数据

    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-Type", "application/json");
    xhr.responseType = "blob";
    xhr.onload = function (oEvent) {
        var content = xhr.response;
        var elink = document.createElement('a');
        elink.download = `${info.name}.xls`;
        elink.style.display = 'none';
        var blob = new Blob([content]);
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        document.body.removeChild(elink);
        loading.close();
        bounce.cancel()
    };

    xhr.send(data);

}
// creator:hxz 2024-06-20 导出设置
function toggleSelectExport(obj) {
    if(obj.hasClass("ty-color-red")){
        obj.html("选择").attr("class","funBtn ty-color-blue")
        obj.parent().prev().html( '- -')
    }else{
        obj.html("放弃").attr("class","funBtn ty-color-red")
        obj.parent().prev().html( '已选中')
    }
}
// creator:hxz 2024-08-27  全选
function allSelectBtn() {
    $("#showSet table .funBtn").each(function () {
        let obj = $(this)
        obj.html("放弃").attr("class","funBtn ty-color-red")
        obj.parent().prev().html( '已选中')
    })
}
function allSelectBtn2() {
    $("#exportSet table .funBtn").each(function () {
        let obj = $(this)
        obj.html("放弃").attr("class","funBtn ty-color-red")
        obj.parent().prev().html( '已选中')
    })
}
// creator:hxz 2024-08-27  全不选
function allNoSelectBtn() {
    $("#showSet table .funBtn").each(function () {
        let obj = $(this)
        obj.html("选择").attr("class","funBtn ty-color-blue")
        obj.parent().prev().html( '- -')
    })
}
function allNoSelectBtn2() {
    $("#exportSet table .funBtn").each(function () {
        let obj = $(this)
        obj.html("选择").attr("class","funBtn ty-color-blue")
        obj.parent().prev().html( '- -')
    })
}
// creator:hxz 2021-09-03 数据展示设置
function toggleSelect(obj) {

    if(obj.hasClass("ty-color-red")){
        obj.html("选择").attr("class","funBtn ty-color-blue")
        obj.parent().prev().html( '- -')
    }else{
        let len = $("#showSet table .ty-color-red").length
        obj.html("放弃").attr("class","funBtn ty-color-red")
        obj.parent().prev().html( '已选中')

    }
}
// creator:hxz 2021-09-03 数据展示设置保存
function updateShowQuestion() {
    let info = $(".mainCon88").data("source");
    let updateQuestionJson = []
    $("#showSet table .ty-color-red").each(function(){
        let info2 = $(this).siblings(".hd").html();
        info2 = JSON.parse(info2)
        updateQuestionJson.push({'question': info2.id})
    })
    $.ajax({
        "url": "../investigationPublish/updateShowQuestion.do",
        "data":{ 'id': info.id , 'updateQuestionJson': JSON.stringify(updateQuestionJson)},
        success:function (res) {
            if(res.status === 1){
                layer.msg("设置成功！")
                bounce.cancel();
                let info = $(".mainCon88").data("source");
                let yeStr = $("#page88").find('.json').html()
                let yeJson = JSON.parse(yeStr)
                getCompleteSubjectList(info.id , 1, yeJson.typeStr)
            }else{
                layer.msg("设置失败！")
            }
        }
    })
}
// creator:hxz 2021-09-03 数据展示设置
function dataShowSet() {
    bounce.show($("#showSet"))
    let info = $(".mainCon88").data("source");
    $.ajax({
        "url": "../investigationPublish/selectQuestionOptionsList.do",
        "data":{ 'id': info.id },
        success:function (res) {
            let list = res, str = ``,showSpan=``
            list.forEach(function (item, index) {
                if(item.isShow === 1){
                    showSpan  += index === 0 ? `“${ item.content }”` : `与“${ item.content }”`;
                }
                str += `
                <tr>
                    <td>${ item.orders }</td>
                    <td>${ item.content }</td>
                    <td>${ item.isShow === 1 ? '已选中' : '- -' }</td>
                    <td>
                        <span class="hd">${ JSON.stringify(item) }</span>
                        <span data-fun="toggleSelect" class="funBtn ${ item.isShow === 1 ? 'ty-color-red' : 'ty-color-blue' }">${ item.isShow === 1 ? '放弃' : '选择' }</span>
                    </td>
                </tr>
                `
            })
            $("#showSet table tr:gt(0)").remove()
            $("#showSet table").append(str)
            $("#showSet .keyShow").html(showSpan)
        }
    })
}
// creator:hxz 2021-08-07 管理问卷
function manageInv(obj) {
    editObj = obj
    let info = obj.siblings(".hd").html()
    info = JSON.parse(info)
    let endTime = new Date(info.endTime).format('yyyy-MM-dd')
    if(info.status===0){
        bounce.show($("#manageInv2"))
        $(".endtim").html( endTime )

    }else{
        $("#manageInv").data('info', info)
        bounce.show($("#manageInv"))
        $("#manage_upTim").val(info.modifyLimit)
        $("#upEndDate").val( endTime )
        $("#manageInv .timCC").hide()
    }
}
function endtimLog() {
    let info = editObj.siblings(".hd").html()
    info = JSON.parse(info)
    $.ajax({
        "url":"../investigationPublish/selectInvestigateQr.do",
        "data":{ 'id': info.id },
        success:function (res) {
            bounce_Fixed.show($("#endtimLog"))
            let endTime = res.endTime || "";
            endTime && $("#endtimLog .endTime").html(new Date(endTime).format("yyyy-MM-dd"))
            let list = res.investigateQrs || []
            let str = ``
            list.forEach( item => {
                str += `
                    <tr><td>${ new Date(item.qrDeadline ).format('yyyy-MM-dd') }</td><td>${ item.createName  } ${ new Date( item.createDate ).format('yyyy-MM-dd')  }</td></tr>
                `
            })
            $("#endtimLogTab tr:gt(0)").remove()
            $("#endtimLogTab").append(str)

        }
    })


}
// creator: hxz 2023-11-17 分享到微信后的效果
function shareView(thisObj) {
    let info = editObj.siblings(".hd").html()
    info = JSON.parse(info)
    console.log('info=', info)
    bounce_Fixed.show($("#shareView"))
    $("#shareView .ttl").html(info.name)
    let org = auth.getOrg()
    console.log('getO=', org)
    let orgName = org.name
    $("#shareView .txt").html(`问卷提交截止日期:${ new Date(info.endTime).format('yyyy-MM-dd') } <br> ${ orgName }`)

}


// creator:hxz 2021-08-07 已停用调查月列表接口
function searchBtn2() {
    let y = $("#endSearchYear").val()
    getStopByMonth(y)
}
function goStop() {
    $(".mainCon").hide()
    $(".mainCon55").show()
    getStopByMonth('')
}
function getStopByMonth(year){
    $.ajax({
        "url":"../investigationPublish/selectPublishStopByMonthList.do",
        "data":{ 'year':year },
        success:function (res) {
            let yearC= res.year
            let list = res.respInvestigateStopByMonthList || []
            $(".mainCon55 .year").html(yearC)
            $(".mainCon55 .num").html(res.sum)
            let str = ``
            list.forEach(function (item) {
                item.status = 0
                str += `
                     <tr>
                        <td>${ item.month  }</td>
                        <td>
                            <span>${ item.sum }份</span>
                            <span class="ty-color-blue funBtn" data-fun="invStopList">查看</span>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </td>
                    </tr>
                    `
            })
            $(".mainCon55 table tr:gt(0)").remove()
            $(".mainCon55 table").append(str)
        }
    })
}
// creator:hxz 2021-08-07 按月份查看问卷列表
function invStopList(obj) {
    let info = JSON.parse(obj.siblings(".hd").html())
    let month = info.month
    getList(0,1,month);
    $(".mainCon").hide();
    $(".mainCon66").show();
}
// creator:hxz 2021-08-07 预览查看问卷
function prevScan() {

    let id = $("#preID").val()
    if(id && Number(id) > 0){
        $.ajax({
            "url":"../investigationManage/selectSubject.do",
            "data":{ "id" : id },
            success:function(res){
                let list = res.respInvestigateSubjectTagList || []
                let header = res.header || ""
                let title = res.title || ""
                let preface = res.preface || ""
                let str = ``
                list.forEach(function(cat){
                    let quesStr = ``;
                    let quess = cat.investigateQuestionList
                    let firstIndex = quess[0]['orders']
                    quess.forEach(function(q){
                        quesStr += `
                        <div class="ques">
                            <div>${ q.orders }、${ q.content  }</div>
                            <div class="ops">${ setOption(q) }</div>
                        </div>
                    `
                    })
                    str += `
                    <div class="cat">
                        <h4>${ cat.name || "" }</h4>
                        ${ quesStr }
                    </div>
                `
                })

                $("#preView .main").html(str);
                $("#preView .memo").html(header);
                $("#preView .ttl").html(title);
                $("#preView .preface").html(preface);
                $("#preView").data("id", id);
                bounce_Fixed.show($("#preView"))
            }
        })
    }

}
function setOption(q) {
    let str = ``
    if([2,3,4].indexOf(q.type) ){
        let ops = q.investigateQuestionKeyList || []
        ops.forEach(function(op, index){
            str += `
            <div>${index +1 }. ${ op.content }</div>
            `
        })
    }
    return str
}
function back77() {
    let backto = $(".mainCon77 .back77").data('backto');
    $(".mainCon").hide()
    $(".mainCon" + backto).show()
}

// creator:hxz 2021-08-07
function back55(obj) {
    backTo11()
}
function backTo11(){
    $(".mainCon").hide()
    $(".mainCon11").show()
    let pageInfo = {}
    if($("#page11").is(":visible")){
        pageInfo = $("#page11").find(".json").html()
    }else{
        pageInfo = $("#page1Search").find(".json").html()
    }
    pageInfo = JSON.parse(pageInfo)
    getList(pageInfo.status, pageInfo.currentPageNo ,pageInfo.month)
}
function back44(obj) {
    backTo11()
}
// creator:hxz 2021-08-07
function back33() {
    $(".mainCon").hide()
    $(".mainCon22").show()
}
// creator:hxz 2021-08-07
function back22() {
    $(".mainCon").hide()
    $(".mainCon11").show()
}
// creator:hxz 2023-11-14  发起新的调查
function createInv(obj) {
    // 单文件上传不需要groupUuid
    var obj = $("#uploadCC")
    obj.html("")
    obj.Huploadify({
        auto: true,
        fileTypeExts: '*.gif,*.png;*.jpg;*.jpeg;',
        formData:{
            module: '调查管理',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: 1000960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedSize: true,
        removeTimeout: 99999999,
        buttonText: "上传",
        queueID: 'fileQueue',
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        multi:false,
        onSelect: function () {

        },
        onUploadError: function () {
            // obj.find(".uploadify_state").html("上传失败！")
        },
        onUploadStart:function(){
            loading.open();
        },
        onUploadSuccess: function (file, json) {
            var data = JSON.parse(json)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            var allpath = $.uploadUrl + path
            // 这部分自己修改
            console.log('file = ', file)
            console.log('json = ', data)
            if(json.length>0) {
                // 结束loading
                loading.close()
                var str = `
                        <div class="fImg">
                            <img path="${ path }" src="${ allpath }" alt="封面图片">
                            <span class="hd">${ JSON.stringify({ 'fileUid': data.fileUid , 'path': path  }) }</span>
                        </div>
                    `
                $(".fengm").html(str);
                $("#UploadFImg .imgCC").html(str)
            }
            obj.find(".uploadify-queue").html('')
        },
        onQueueComplete:function() {
            loading.close();
            obj.find(".uploadify-queue").html('')
        }
    })

    let path = 'assets/img/fimg.png'
    let allp = '../' + path
    let imgStr = `
        <div class="fImg">
            <img path="${ path }" src="${ allp }" alt="封面图片">
            <span class="hd">${ JSON.stringify({ 'path': path  }) }</span>
        </div>
    `
    $(".fengm").html(imgStr)

    $.ajax({
        "url":"../investigationPublish/selectSubjectOptionsList.do",
        success:function (res) {
            let list = res || []
            let opStr = `<option value="">请选择</option>`
            list.forEach(function (op) {
                opStr += `<option value="${ op.id }">${ op.title }</option>`
            })
            $(".bankList").html(opStr)
            bounce.show($("#createInv"))
            $("#createInv .marTp16px").hide()
        }
    })
}
function openUploadFImg() {
    $("#UploadFImg .imgCC").html($("#createInv .fengm").html())
    bounce_Fixed.show($("#UploadFImg"))
}
function uploadFImg() {
    $("#uploadCC .uploadify-button").click()
}
// creator:hxz 2021-08-07  发起新的调查 确定
function createInvOk() {
    let d = $("#endDate").val();
    if(d.length == 0){
        layer.msg("请录入有效期的到期日");
        return false;
    }
    let min = $("#add_min").val()
    if(min === ''){
        layer.msg("问卷提交后，多长时间内可修改 需要设置");
        return false;
    }
    let opID = $(".bankList").val()
    let coverImage = $(".fengm").find('img').attr('path');
    let data = {
        "expireDate": new Date(d).getTime() ,
        "id":opID ,
        "coverImage":coverImage ,
        "modifyLimit": min // 允许修改时间
    }
    $.ajax({
        url : "../investigationPublish/addPublish.do" ,
        data: data ,
        success:function( res ){
            let status = res.status
            bounce.cancel()
            if(status === 1){
                layer.msg("新的调查已生成")
                backTo11()
            }else{
                layer.msg("新的调查生成失败")
            }
        }
    })
}
// creator:hxz 2021-08-07  发起新的调查 选择好题目展示下面的
function showNext() {
    let it = $(".bankList").val()
    if(it && it > 0){
        $("#createInv .marTp16px").show()
        $("#add_min").val('10')

    }else{
        $("#createInv .marTp16px").hide()
    }
}


// creater : hxz 2021-08-07 二维码生成记录
function scanQRLog( ) {
    let list = $(".scanQRLogBtn").data("source") || []
    let str = ``
    list.forEach(function(invst){
        str += `
                 <tr>
                    <td>${ invst.createName } ${ new Date(invst.createDate ).format("yyyy-MM-dd hh:mm:ss")}</td>
                    <td>${ new Date(invst.qrDeadline ).format("yyyy-MM-dd")}</td>
                    <td>${ new Date(invst.qrTerminateTime ).format("yyyy-MM-dd")}</td>
                    <td>${ invst.updateName || '' } ${ new Date(invst.qrTerminateTime ).format("yyyy-MM-dd hh:mm:ss")}</td>
                </tr>
                `
    })
    $("#scanQRLog table tr:gt(0)").remove();
    $("#scanQRLog table").append(str);
    bounce.show($("#scanQRLog"))
}



laydate.render({elem: '#upEndDate' });
laydate.render({elem: '#add_date' });
laydate.render({elem: '#endDate' });
laydate.render({elem: '#endDate2' });
laydate.render({elem: '#endSearchYear', type: 'year'});