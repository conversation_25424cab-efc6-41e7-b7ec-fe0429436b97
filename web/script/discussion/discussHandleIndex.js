jQuery.fn.treeItemSlide = function (type) {
    return $(this).each(function(){
        var iconNode = $(this).children('i').eq(0)
        if (!iconNode.hasClass("ty-fa")) {
            if (type === 'up') {
                return iconNode.removeClass('fa-angle-down').addClass('fa-angle-right')
            } else if (type === 'down') {
                return iconNode.removeClass('fa-angle-right').addClass('fa-angle-down')
            } else if (type === 'toggle') {
                if (iconNode.hasClass("fa-angle-right")) {
                    return iconNode.eq(0).removeClass('fa-angle-right').addClass('fa-angle-down')
                } else {
                    return iconNode.eq(0).removeClass('fa-angle-down').addClass('fa-angle-right')
                }
            }
        }
    });
}
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#seeHistoryDetail"));
bounce_Fixed2.cancel();
$(function () {
    var id = getUrlParam("id")
    $(".mainChat").data("theme", {
        id: id
    })

    // 弹窗内的按钮点击事件
    $(".bounce,.bounce_Fixed,.bounce_Fixed2").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            // 讨论详情修改历史记录
            case 'discussDetailChangeHistory':
                bounce_Fixed.show($("#discussDetailChangeHistory"))
                getDiscussDetailChangeHistory()
                break
            // 查看历史详情
            case 'seeHistoryDetail':
                bounce_Fixed2.show($("#seeHistoryDetail"))
                var data = $(this).parents("tr").find(".hd").text()
                data = JSON.parse(data)
                var roleList = data.listForumPostUserHistory
                var roleArr = []
                for (var i in roleList) {
                    roleArr.push(roleList[i].userName)
                }
                $("#seeHistoryDetail .see_title").html(data.title)
                $("#seeHistoryDetail .see_des").html(data.content)
                $("#seeHistoryDetail .see_compere").html(data.compereName)
                $("#seeHistoryDetail .see_roleList").html(roleArr.join("，"))
                break
        }
    })

    // 右侧标签页切换点击事件
    $(".historyTab").on("click", 'li', function () {
        var to = $(this).attr("to")
        $("#" + to).show().siblings('.tabCon').hide()
        switch(to) {
            case 'roleList':
                getAllParticipants(1, 'rightRole')
                break;
            case 'allMessage':
                initAllMessage()
                break;
            case 'imageList':
                var arrowType = 1
                if($(this).hasClass('active')){
                    // 如果点击的是已经点击过的，升序改降序，降序改升序
                    $(this).find("i").toggleClass("fa-long-arrow-up");
                    arrowType = $(".history_avatar li.active .sort i").hasClass("fa-long-arrow-up") ? 1 : 2
                } else {
                    // 如果点击的是未被点击的，获取升序列表
                    $(this).find("i").addClass("fa-long-arrow-up");
                    arrowType = 1
                }
                getAllFile('image', arrowType)
                break;
            case 'fileList':
                if($(this).hasClass('active')){
                    // 如果点击的是已经点击过的，升序改降序，降序改升序
                    $(this).find("i").toggleClass("fa-long-arrow-up");
                    arrowType = $(".history_avatar li.active .sort i").hasClass("fa-long-arrow-up") ? 1 : 2
                } else {
                    // 如果点击的是未被点击的，获取升序列表
                    $(this).find("i").addClass("fa-long-arrow-up");
                    arrowType = 1
                }
                getAllFile('file', arrowType)

                break;
        }
        $(this).addClass("active").siblings().removeClass("active")
    })

    // 剩余页面按钮点击事件
    $(".app_avatar,.bounce").on("click keydown", "[type='btn']", function (event) {
        var name = $(this).data("name")
        switch (name) {
            case 'seeDetail':
                bounce.show($("#seeDetail"))
                getThemeDetail()
                break;
            case 'messageHistory':{
                $(".history_avatar").toggle()
                break;
            }
            case 'reloadMore':
                var lastTime = $(".themeList .themeItem").last().find(".lastReplyTime").data("time")
                getPostsList('', lastTime)
                break;
            case 'query':
                $(".queryCondition").show().siblings().hide()
                $(".queryCondition [name]").val("1")
                $(".queryCondition [name='findUser']").val("")
                getAllParticipants(1, 'query')
                break;
            case 'sureQuery':
                var data = {
                    postId: $(".mainChat").data("theme").id, // 讨论id
                    isQuery: true
                }
                $(".queryCondition [name]").each(function () {
                    var name = $(this).attr("name")
                    data[name] = $(this).val()
                })
                getAllReply(data)
                break;
            case 'unReadMsg':
                var unReadMsg = $(this).data("unReadMsg")
                var unreadNum = unReadMsg - $(".chat_main .msgItem").length
                if (unreadNum > 0) {
                    getPostMessage(3, unreadNum)
                } else {
                    $(".unReadMsg").hide()
                    var length = $(".chat_avatar .msgItem").eq(-unreadNum).offset().top - $(".chat_avatar .msgItem").eq(0).offset().top
                    $('.chat_avatar').scrollTop(length);
                }
                break;
            case 'seeOrigin':
                var parentReplyId = $(this).parents(".msgReply").data("origin")
                var lastReplyId = $(".chat_main .msgItem").first().data("id")
                if (parentReplyId < lastReplyId) {
                    getPostMessage(2, parentReplyId)
                } else {
                    var pTop = $(".chat_main").offset().top
                    var cTop = $(".chat_main .msgItem[data-id='"+parentReplyId+"']").offset().top
                    $('.chat_avatar').scrollTop(cTop-pTop);
                    $(".chat_main .msgItem[data-id='"+parentReplyId+"']").addClass("choosen")
                    setTimeout(function () {
                        $(".chat_main .msgItem[data-id='"+parentReplyId+"']").removeClass("choosen")
                    }, 3000 )
                }
                break
            case 'queryBack':
                initAllMessage()
                break
        }
    })


    // 初始化参与人员选择列表
    $(".departTree").on("click","li>div",function(){
        $(this).next().toggle();
    });
    $(document).click(function () {
        $(".input_choose_list:not(.dir)").slideUp('fast');
    });
    $(".input_choose").on("click", function () {
        $(this).find('.search').focus()
        $(".input_choose_list").slideDown('fast')
        return false
    })
    $(".input_choose").on('input', '.search', function () {
        var list = $("#newDiscussion").data('list')
        var newList = []
        var val = $(this).val()
        for (var i in list) {
            if (list[i].userName.indexOf(val) !== -1) {
                newList.push(list[i])
            }
        }
        updateRoleList('1vn', newList)
    })
    $(".input_choose").on('click','.delRole', function () {
        var userID = $(this).parents('.selected_item').data('id')
        var list = $("#newDiscussion").data('list')

        for (var i in list) {
            if (list[i].userID === userID) {
                list[i].select = false
            }
        }
        updateRoleList('1vn', list)
        $(this).parents('.selected_item').remove()
        var height = $(".input_choose").innerHeight()
        $(".input_choose_list").css('top',height)
    })
    $("body").on('click', '.delFile', function () {
        $(this).parents('.file_item').remove()
        var uid = $(this).attr("fileUid")
        if (uid) {
            var option = {type: 'fileUid', fileUid: uid}
            fileDelAjax(option)
        }
    })

    $(".input_choose_list").on('click', '.input_choose_item', function () {
        if(!$(this).hasClass("selected")){
            $(this).addClass('selected')
            var role_name = $(this).find('.input_choose_item_name').html()
            var role_id = $(this).data('id')
            var list = $("#newDiscussion").data('list')
            for (var i in list) {
                if (list[i].userID === role_id) {
                    list[i].select = true
                }
            }
            updateRoleList('1vn', list)
            var str =   '<div class="selected_item" data-id="'+role_id+'">' +
                '    <span class="selected_name">'+role_name+'</span>' +
                '    <span class="delRole">+</span>' +
                '</div>'
            $(".search").before(str)
            $(".search").val('')
            var height = $(".input_choose").innerHeight()
            $(".input_choose_list").css('top',height)
        }
        return false
    })

    $(".pageBtnGroup i").on("click", function () {
        var type = $(this).data("type")
        var pageInfo = $("#calendar").data("pageInfo")
        console.log(pageInfo)
        var currentPageNo = pageInfo.currentPageNo
        var totalPage = pageInfo.totalPage
        var pageNo = 0
        var isDisabled = $(this).hasClass("disabled")
        switch (type) {
            case 'first':
                pageNo = totalPage
                break
            case 'prev':
                pageNo = currentPageNo + 1
                break
            case 'next':
                pageNo = currentPageNo - 1
                break
            case 'last':
                pageNo = 0
                break
        }
        if (!isDisabled) {
            var param = {
                postId: $(".mainChat").data("theme").id, // 讨论id
                type: 1,   // 5中状态1是全部信息，2是近7日，3是近1个月，4是近3个月，5是近1年
                time: '',   // 查询全部信息时可以点开日历去查询某天的，这时要把时间传过来，当有此值时就不能传findUser和mesType，同理有这两个值时就不能传time
                findUser: '',   // 查找某人的信息时要把这个人id传过来
                mesType: '',    // 查找信息的类型1是全部的信息2是@我的信息3是回复我的信息
                currentPageNo:pageNo,
                pageSize:20,
                isQuery: false
            }
            getAllReply(param)
        }
    })
    $(".fa-calendar").click(function () {
        $("#calendar").focus()
    })

    // 显示主题和回复
    getPostMessage(0)

    var borrowId = getUrlParam("borrowId")
    if (borrowId) {
        $.ajax({
            url: '../read/getReadForumPostMessage.do',
            data: {
                borrowId: borrowId
            },
            beforeSend: function () {}
        })
    }

    // 默认显示参与人数据
    $(".history_avatar .historyTab li").eq(0).click()
});

// creator: 张旭博, 2020-07-15 23:24:33, 获取左侧主题列表字符串
function getThemeStr(forumPost, allComments) {
    var str = ''
    var corner          = forumPost.messageNum;
    var lastReplyTime   = customTime(forumPost.updateDate);
    var lastReply   = '';
    var atTag   = forumPost.atTag;
    var replyTag   = forumPost.replyTag;
    var isCorner        = !corner? '<span class="corner" style="display:none;">0</span>': '<span class="corner">' + corner + '</span>';
    var isAtTagShow     = atTag === 1? '':'style="display:none"';
    var isReplyTag    = replyTag === 1? '':'style="display:none"';

    if (allComments) {
        var msgType = allComments[0].msgType
        var file = allComments[0].forumReplyAttachmentHashSet

        // 转换最后一条回复中的文件和图片为汉字
        var fileStr = ''
        for (var i in file) {
            if (file[i].type === '1') {
                fileStr += '【图片】'
            } else {
                fileStr += '【文件】'
            }
        }
        if (msgType === '1') {
            lastReply = '<div class="lastReply">' + allComments[0].createName + '：' + fileStr + allComments[0].content.replace(/<[^>]+>/g,"") +'</div>'
        } else if (msgType === '2') {
            lastReply = '<div class="lastReply">' + allComments[0].memo + '</div>'
        } else {
            lastReply = '<div class="lastReply">' + allComments[0].content.replace(/<[^>]+>/g,"") + fileStr + '</div>'
        }
    }

    str +=  '<li class="themeItem active" data-id="'+forumPost.id+'" data-category="'+forumPost.category+'" data-compere="'+forumPost.compere+'">' +
        '   <div class="item_title">' +
        '       <div class="title" title="'+forumPost.title+'">' +
        '          <span class="participantsNum" data-num="'+forumPost.participantsNum+'">'+forumPost.participantsNum+'人</span>' +
        '          <span>'+forumPost.title+'</span>' +
        '       </div>' +
        '       <span class="lastReplyTime" data-time="'+formatTime(forumPost.updateDate, true)+'">'+lastReplyTime+'</span>' +
        '   </div>' +
        '   <div class="item_des">' +
        '       <b class="atTag ty-color-red" '+isAtTagShow+'>[有人@你]</b> <b class="replyTag ty-color-red" '+isReplyTag+'>[有人回复你]</b>' + lastReply + isCorner +
        '   </div>'+
        '</li>'
    return str
}

// creator: 张旭博, 2020-07-30 10:26:20, 更新未读按钮状态
function updateUnreadState() {
    var unReadMsg = $(".unReadMsg").data("unReadMsg")
    if (Number(unReadMsg) < $(".chat_main .msgItem").length || Number(unReadMsg) === $(".chat_main .msgItem").length) {
        $(".unReadMsg").hide()
    }
}

// creator: 张旭博，2019-10-29 16:04:48，获取所有参与人列表（已经选择过的参与人）
function getAllParticipants(type, name) {
    var data = {
        type: type, // 传1，2，3这三种状态 1代表获取当前讨论的参与人 2代表获取当前机构还不是参与人的人，用于新增参与人时使用，3代表获取除登录人外其它的参与人，用于新增@人员时选人，去除了自己做到不允许@自己
        userID: sphdSocket.user.userID
    }
    if (type !== 4) {
        data.postId = $(".mainChat").data("theme").id // 讨论的id
    }
    $.ajax({
        url: "getAllParticipants.do",
        data: data,
        success: function (res) {
            console.log(res)
            var roleList = res.listPostUser
            // 1: 'rightRole', 'query', 'seeCompere', 'changeCompere'
            // 2: 'continueAdd'
            // 3: 'at'
            // 4: '1v1', '1vn'
            if (roleList) {
                updateRoleList(name, roleList)
            }
        }
    })
}

// creator: 张旭博，2019-11-18 11:40:58，获取讨论主题的所有回复（包括文件和图片角标）(type:0 获取所有回复 type:其他 获取更多回复)
var xhr00 = null
function getPostMessage(type, num) {
    var param = {
        id : $(".mainChat").data("theme").id, // 讨论的id
        userID: sphdSocket.user.userID,
    }
    var url = ''

    if (type === 0) {
        url = 'getAllForumPostMessage.do'
    } else {
        url = 'forumPostLoadingMes.do'
        param.replyId = $(".chat_main .msgItem").first().data("id") // 最上面的消息
        param.type = type // 1是点击加载更多，2是定位原文，3是X条未读消息
        if (type === 2) {
            param.parentReplyId = num // 点击定位原文传父id
        }
        if (type === 3) {
            param.unreadNum = num // 点击M条消息时传的未读数
        }
    }
    if (xhr00) xhr00.abort()
    xhr00 = $.ajax({
        url: url,
        data: param,
        success: function (res) {
            var data = res.data
            var forumPost   = data.forumPost; // 讨论主题的信息
            var unreadNum  = data.unreadNum; // 未读跟帖的数目
            var replyAttPictureNum  = data.replyAttPictureNum; // 附件中图片的数目
            var replyAttFileNum  = data.replyAttfileNum; // 附件中文件的数目
            var allComments = data.listReplyPage;  // 跟帖的信息

            // 获取主题列表内容
            var themeListStr = getThemeStr(forumPost, allComments)
            $(".themeList .list").html(themeListStr)
            $(".mainChat .chatBar .title").html(forumPost.title)


            var cmtStr = ''
            if (type === 0) { // 处理回复区的所有回复信息
                $(".history_avatar li[to='imageList'] .corner-word").html(replyAttPictureNum > 0?'('+replyAttPictureNum+')':'')
                $(".history_avatar li[to='fileList'] .corner-word").html(replyAttFileNum > 0?'('+replyAttFileNum+')':'')
            }

            // 处理评论
            if (allComments) {
                cmtStr = updateMsg(allComments, type)
            }

            if (type === 0 || type === 1) {
                // 判断是否显示查看更多信息
                if (allComments.length === 20) {
                    $(".seeMore").show()
                } else {
                    $(".seeMore").hide()
                }
            }

            // 处理滚动条
            if (type === 0) {
                // 正常获取讨论，页面滚到最底部
                $(".chat_avatar .chat_main").html(cmtStr);
                $('.chat_avatar').scrollTop($(".chat_avatar").prop("scrollHeight"));
            } else if (type === 1) {
                // 查看更多消息，页面滚至看到的位置
                var prevHeight = $(".chat_avatar").prop("scrollHeight")
                $(".chat_avatar .chat_main").prepend(cmtStr);
                var nextHeight = $(".chat_avatar").prop("scrollHeight")
                $('.chat_avatar').scrollTop(nextHeight - prevHeight);
            }else if (type === 2) {
                // 查看更多消息，页面滚至看到的位置
                $(".chat_avatar .chat_main").prepend(cmtStr);
                $('.chat_avatar').scrollTop(0);
                $(".chat_main .msgItem").first().addClass("choosen")
                setTimeout(function () {
                    $(".chat_main .msgItem").first().removeClass("choosen")
                }, 3000 )
            } else if (type === 3) {
                // 查看未读消息
                $(".chat_avatar .chat_main").prepend(cmtStr);
                $('.chat_avatar').scrollTop(0);
            }

            if (type === 0) {
                // 处理未读消息
                $(".unReadMsg .num").html(unreadNum + '条消息未读')
                $(".unReadMsg").data("unReadMsg", unreadNum)
                if (unreadNum > 20) {
                    $(".unReadMsg").show()
                } else {
                    $(".unReadMsg").hide()
                }
            } else {
                updateUnreadState()
            }
        }
    });
}

// creator: 张旭博，2020-09-23 14:55:59，获取讨论详情（查看详情）
function getThemeDetail() {
    var id = $(".mainChat").data("theme").id
    $.ajax({
        url: 'getForumPostMessage.do',
        data: {
            id: id
        },
        success: function (res) {
            var data = res.data
            var forumPost   = data.forumPost; // 讨论主题的信息

            var compere = $(".mainChat").data("theme").compere
            var category = $(".mainChat").data("theme").category // 讨论类型（一对一、多对多）

            // 处理讨论主题的详情信息
            var allFile    = data.forumPostAttachmentHashSer;  //讨论的附件

            var fileStr = ''; // 附件列表
            // 处理附件
            if (allFile) {
                fileStr = updateFile(allFile)
            }
            // 处理参与人
            if (category === 1 || compere !== sphdSocket.user.userID) {
                $(".continueAdd").hide()
            } else {
                $(".continueAdd").show()
            }
            getAllParticipants(1, 'seeCompere')
            if (compere === sphdSocket.user.userID) {
                $("#discussDetailChangeBtn").show()
            } else {
                $("#discussDetailChangeBtn").hide()
            }

            $("#seeDetail .see_title").html(forumPost.title)
            $("#seeDetail .see_compere").html(forumPost.compereName)
            $("#seeDetail .see_des").html(forumPost.content)
            $("#seeDetail .see_processList").html('<div>发起：<span class="create">'+forumPost.createName + ' ' +formatTime(forumPost.createDate, true)+'</span></div><div>审批：' + forumPost.auditorName + ' ' +formatTime(forumPost.auditDate, true)+'</div>')
            $("#seeDetail .see_fileList").html(fileStr)
            $("#seeDetail").data('forumPost', forumPost)
        }
    })
}

// creator: 张旭博，2020-09-24 14:21:48，获取讨论组详情-修改记录
function getDiscussDetailChangeHistory() {
    var postId = $(".mainChat").data("theme").id
    $.ajax({
        url: 'getForumPostHis.do',
        data: {
            postId: postId
        },
        success: function (res) {
            var data = res.data
            var listForumPostHis = data.listForumPostHis
            var str = ''
            for (var i=0; i<listForumPostHis.length; i++) {
                if (i === 0) {
                    str += '<tr>' +
                        '<td>原始信息</td>' +
                        '<td><span class="ty-color-blue" type="btn" data-name="seeHistoryDetail">查看</span></td>' +
                        '<td>'+listForumPostHis[i].createName + ' ' + formatTime(listForumPostHis[i].createDate, true) +'</td>' +
                        '<td class="hd">'+JSON.stringify(listForumPostHis[i]) +'</td>' +
                        '</tr>'
                } else {
                    str += '<tr>' +
                        '<td>第'+i+'次修改后</td>' +
                        '<td><span class="ty-color-blue" type="btn" data-name="seeHistoryDetail">查看</span></td>' +
                        '<td>'+listForumPostHis[i].updateName + ' ' + formatTime(listForumPostHis[i].updateDate, true) +'</td>' +
                        '<td class="hd">'+JSON.stringify(listForumPostHis[i]) +'</td>' +
                        '</tr>'
                }

            }

            var n = listForumPostHis.length-1
            if (n > 0) {
                $("#discussDetailChangeHistory .changeTip").html('当前资料为第'+n+'次修改后的结果。<div class="text-right" style="flex: 1;">修改人：'+listForumPostHis[n].updateName + ' ' + formatTime(listForumPostHis[n].updateDate, true)+'</div>')
                $("#discussDetailChangeHistory table").show()
            } else {
                var createInfo  = $("#seeDetail .see_processList .create").html()
                $("#discussDetailChangeHistory .changeTip").html('当前资料尚未经修改。<div class="text-right" style="flex: 1;">发起人：'+createInfo+'</div>')
                $("#discussDetailChangeHistory table").hide()
            }

            $("#discussDetailChangeHistory tbody").html(str)
        }
    })
}

// creator: 张旭博，2019-10-29 16:05:38，获取所有附件
function getAllFile(fileType, arrowType) {
    $.ajax({
        url: "findSubsidiaryFile.do",
        data: {
            postId: $(".mainChat").data("theme").id, // 讨论的id
            type: arrowType,
            attType: (fileType === 'image'?1:2)
        },
        success: function (res) {
            console.log(res)
            var allFile = res.listReplyAtt
            var fileStr = ''
            if (allFile) {
                fileStr = updateFile(allFile)
                $(".history_avatar ."+fileType+"List").html(fileStr)
                if (allFile.length > 0) {
                    $(".history_avatar [to='"+fileType+"List'] .corner-word").html('(' + (allFile.length) + ')')
                } else {
                    $(".history_avatar [to='"+fileType+"List'] .corner-word").html('')
                }
            }
        }
    })
}

// creator: 张旭博，2019-10-29 16:24:03，获取讨论主题的历史所有回复外加各种查询条件
function getAllReply(param) {
    // var param = {
    //     postId: $(".mainChat").data("theme").id, // 讨论id
    //     type: 1,   // 5中状态1是全部信息，2是近7日，3是近1个月，4是近3个月，5是近1年
    //     time: '',   // 查询全部信息时可以点开日历去查询某天的，这时要把时间传过来，当有此值时就不能传findUser和mesType，同理有这两个值时就不能传time
    //     findUser: '',   // 查找某人的信息时要把这个人id传过来
    //     mesType: '',    // 查找信息的类型1是全部的信息2是@我的信息3是回复我的信息
    //     currentPageNo:1,
    //     pageSize:20
    // }
    param.userID = sphdSocket.user.userID
    $.ajax({
        url: "getAllReply.do",
        data: param,
        success: function (res) {
            var allReply = res.listReply
            var pageInfo = res.pageInfo
            var str = ''
            if (allReply) {
                for (var i = allReply.length - 1; i >= 0; i--) {
                    // 拼接回复
                    var replyStr = ''
                    if (allReply[i].parent) {
                        replyStr =  '<div class="msgReply">' +
                            '    <span>'+allReply[i].memo+'</span>' +
                            '</div>'
                    }
                    // 拼接附件信息
                    var fileStr = ''
                    var allFile = allReply[i].forumReplyAttachmentHashSet
                    if (allFile) {
                        fileStr = updateFile(allFile)
                    }
                    // 因为回调和接口返回的时间格式不一样，在此需要统一格式
                    var createDate = allReply[i].createDate
                    var dateObj = new Date(createDate)
                    createDate = formatTime(dateObj, true)

                    str +=  '<li class="replyItem">' +
                        '    <div class="'+(allReply[i].creator === sphdSocket.user.userID? 'ty-color-green':'ty-color-blue')+'">' + allReply[i].createName + ' ' + createDate+ '</div>' +
                        replyStr+
                        '    <div class="replyContent">' + fileStr + allReply[i].content + '</div>' +
                        '</li>'
                }
            }
            var isQuery = param.isQuery
            if (isQuery) {
                $(".queryAlert").show()
                $(".queryAlert .queryNum").html(allReply.length)
            } else {
                var currentPageNo = pageInfo.currentPageNo
                var totalPage = pageInfo.totalPage
                $(".pageBtnGroup i").removeClass('disabled')
                if (totalPage === 1){
                    $(".pageBtnGroup i").addClass('disabled')
                } else {
                    $(".pageBtnGroup i").removeClass('disabled')
                    if (currentPageNo === 1) {
                        $(".pageBtnGroup i").eq(2).addClass('disabled')
                    } else if (currentPageNo === totalPage) {
                        $(".pageBtnGroup i").eq(1).addClass('disabled')
                    }
                }
                $("#calendar").data('pageInfo', pageInfo)
            }
            $(".history_avatar .allMessage ul").html(str)
        }
    })

}

// creator: 张旭博，2019-12-26 16:34:55，初始化全部消息
function initAllMessage() {
    var data = {
        postId: $(".mainChat").data("theme").id, // 讨论id
        type: 1,   // 5中状态1是全部信息，2是近7日，3是近1个月，4是近3个月，5是近1年
        time: '',   // 查询全部信息时可以点开日历去查询某天的，这时要把时间传过来，当有此值时就不能传findUser和mesType，同理有这两个值时就不能传time
        findUser: '',   // 查找某人的信息时要把这个人id传过来
        mesType: '',    // 查找信息的类型1是全部的信息2是@我的信息3是回复我的信息
        currentPageNo:1,
        pageSize:20,
        isQuery: false
    }
    getAllReply(data)
    $(".queryAlert").hide()
    $(".dateChoose").show().siblings().hide()
    $(".dateChoose input").val("")
}

// creator: 张旭博，2019-07-02 12:07:26，更新参与人员列表
function updateRoleList(name, userList) {
    var str = ''
    switch (name) {
        // 右侧参与人列表
        case 'rightRole':
            str = ''
            var compere = $(".mainChat").data("theme").compere // 当前主持人
            var category = $(".mainChat").data("theme").category
            for (var i=0; i<userList.length;i++) {
                str +=  '<li class="roleItem" data-id="'+userList[i].userID+'">' +
                        '   <img class="avatar small" onerror="this.src=\'../../css/discussion/head_img/avatar.svg\' " src="../'+userList[i].imgPath+'">' +
                        '   <div class="name">' + userList[i].userName + ( compere === userList[i].userID ? '<i class="manager_s"></i>' : '' ) + '</div>' +
                        '   <div class="phone">' + userList[i].mobile + '</div>' +
                        '   <div class="post">' + (userList[i].departName || '--') + '</div>' +
                        ' </li>'
            }
            $(".history_avatar #roleList ul").html(str)
            $(".history_avatar #roleList ul").data('userList', userList)
            $(".history_avatar [to='roleList'] .corner-word").html(userList.length > 0?'('+userList.length+')':'')
            break
        // 右侧全部消息查询条件
        case 'query':
            str = '<option value="">----请选择----</option>'
            for (var i=0; i<userList.length;i++) {
                str += '<option value="'+userList[i].userID+'">'+userList[i].userName+'</option>'
            }
            $(".history_avatar .queryCondition [name='findUser']").html(str)
            $(".history_avatar .queryCondition [name='findUser']").data('userList', userList)
            break
        // 查看讨论详情参与人部分
        case 'seeCompere':
            var compere = $(".mainChat").data("theme").compere // 当前主持人
            for (var j=0; j<userList.length;j++) {
                var isShowDel = ''
                if (compere === sphdSocket.user.userID && userList[j].userID !== compere) {
                    isShowDel = '<i class="fa fa-minus-circle delUser" type="btn" data-name="delRole"></i>'
                }
                str +=  '<li class="see_roleList_item" data-id="'+userList[j].userID+'">' + userList[j].userName+ isShowDel + ' </li>'
            }
            if (compere === sphdSocket.user.userID) {
                str += '<span class="ty-btn ty-btn-green ty-circle-3" type="btn" data-name="addRole">+</span>'
            }
            $("#seeDetail .roleList").html(str)
            $("#seeDetail .roleList").data('userList', userList)
            break
    }
}

// creator: 张旭博，2019-12-17 10:37:39，更新附件信息
function updateFile(allFile) {
    var fileStr = '<div class="bubble_fileList">'
    for (var j in allFile) {
        var size = allFile[j].size
        var updateName = allFile[j].updateName || allFile[j].createName || ''
        var updateTime = allFile[j].updateTime || allFile[j].createTime ||''
        var filePathArr = allFile[j].path.split(".")
        var fileType = ''
        if (filePathArr) {
            fileType = filePathArr[1]
        } else {
            fileType = 'other'
        }
        size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size = parseFloat(size/1048576).toFixed(2) + 'MB';
        fileStr +=  '<div class="fileItem">' +
            '   <div class="fileType ty-file_'+fileType+'"></div>' +
            '   <div class="fileInfo">' +
            '       <div class="fileTitle"><span class="fileName" title="'+allFile[j].title+'">' + allFile[j].title + '</span> <span class="fileSize">('+size+')</span></div>' +
            '       <div class="fileDetail">' +
            '           <div class="fileDes">' + updateName + ' ' + formatTime(updateTime, true) + '</div>' +
            '           <div class="fileHandle">' +
            '               <a path="'+ allFile[j].path +'" onclick="seeOnline($(this))">预览</a>' +
            '               <a path="'+ allFile[j].path +'" onclick="getDownLoad($(this))" download="'+ allFile[j].title + '">下载</a>' +
            '           </div>' +
            '       </div>' +
            '   </div>'+
            '</div>'
    }
    fileStr += '</div>'
    return fileStr
}

// creator: 张旭博，2019-12-17 10:09:30，更新消息内容
function updateMsg(allComments, type) {
    var cmtStr = ''
    if (allComments.length === 0 && type === 0) {
        cmtStr = '<div class="text-center">本讨论主题尚无人发布消息</div>'
    } else {
        for (var w = 0; w < allComments.length; w++) {
            // 发起消息的人的信息
            // var postUserInfo = allComments[w].postUserMussage

            // 附件信息
            var allFile = allComments[w].forumReplyAttachmentHashSet
            var postUserMessage = allComments[w].postUserMussage
            var imgPath = '../' + postUserMessage.imgPath || ''

            // 判断发起人是不是当前人
            var isSelf = allComments[w].creator === sphdSocket.user.userID
            var length = $(".chat_main .msgItem").length
            var isReply = !isSelf && (length + w < 60 || type === 4)

            // 拼接附件信息
            var fileStr = ''
            if (allFile) {
                fileStr = updateFile(allFile)
            }

            // 判断是否需要添加撤回按钮
            var isCanDel = typeof allComments[w].delStatus === 'undefined' ? isSelf : (allComments[w].delStatus === 1 && isSelf)

            // 因为回调和接口返回的时间格式不一样，在此需要统一格式
            var createDate = allComments[w].createDate
            var dateObj = new Date(createDate)
            createDate = formatTime(dateObj, true)

            // 拼接回复
            var replyStr = ''
            if (allComments[w].parent) {
                replyStr =  '<div class="msgReply"  data-origin="'+allComments[w].parent+'">' +
                    '    <span>'+setNewMemo(allComments[w].memo)+'</span>' +
                    '</div>'
            }

            // 拼接主体消息
            var msgType = allComments[w].msgType // 用来判断是否为系统消息 1：非系统消息 其他：系统消息
            if (msgType === '1') {
                cmtStr =    '<div class="msgItem'+(isSelf?' selfMsg':'')+'" data-id="'+allComments[w].id+'">' +
                    '   <img class="avatar" onerror="this.src=\'../../css/discussion/head_img/avatar.svg\' " src="'+imgPath+'">' +
                    '   <div class="msgItemCon">' +
                    '      <div class="userInfo"><span class="userName">'+(isSelf?'我':allComments[w].createName)+'</span><span class="applyTime">'+createDate+'</span></div>' +
                    '      <div class="bubble_avatar">' + replyStr + fileStr + allComments[w].content + '</div>' +
                    (isReply?'<div class="reply" type="btn" data-name="reply">回复</div>':'')+
                    (isCanDel?'<div class="recall" type="btn" data-name="recall">撤回</div>':'')+
                    '   </div>' +
                    '</div>' + cmtStr
            } else if (msgType === '2') {
                cmtStr =    '<div class="msgItem system" data-id="'+allComments[w].id+'">' +
                    '   <div class="msgItemCon">' +
                    '      <div class="userInfo"><span class="applyTime">'+createDate+'</span></div>' +
                    '      <div class="bubble_avatar">' + allComments[w].memo + '</div>' +
                    '   </div>' +
                    '</div>' + cmtStr
            } else {
                cmtStr =    '<div class="msgItem system" data-id="'+allComments[w].id+'">' +
                    '   <div class="msgItemCon">' +
                    '      <div class="userInfo"><span class="applyTime">'+createDate+'</span></div>' +
                    '      <div class="bubble_avatar">' + allComments[w].content + '</div>' +
                    '   </div>' +
                    '</div>' + cmtStr
            }
        }
    }
    return cmtStr
}

//------------------------- 获取数据 ---------------------------//

// creator: 侯杏哲,2017-12-04 渲染获取的文件夹数据和页面 ( 适用于主页面文件夹树和移动文件弹框的文件夹树 )
function showTreeData(data , treeItemThis) {
    // data - 获取的数据 ， treeItemThis - 点击的那个文件夹对象 ， treeObj - 点击的树对象

    // 处理子文件夹数据
    var listNotice      = data["parentFolder"] || [];
    var listNoticeChild = data["childFolder"];
    var level = parseInt(treeItemThis.parent().parent().attr("level")) ;
    var nextLevel = level + 1 ;
    var levelStr = "";
    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">' ;
    for(var i in listNoticeChild){
        if(listNoticeChild[i]["childStatus"] === '1'){ // 1时代表此文件夹下有子文件夹
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"]+'</span>' +
                '</div></li>'
        }else{
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"] + '</span>' +
                '</div></li>'
        }
    }
    levelStr += '</div>';
    localStorage.setItem("noticemax",listNotice["childStatus"] ); // childStatus : 1 是有文件夹; 2 是有文件; null标识什么都没有

    treeItemThis.attr("child", listNoticeChild.length > 0)
    if (listNoticeChild.length > 0) {
        if (treeItemThis.next().length === 0) {
            treeItemThis.after(levelStr);
        }
    }

}

// creator: 张旭博，2018-04-24 10:17:16，根据文件数据返回文件列表字符串
function getFileListStr(fileInfo) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        $("#fileSort").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        if ($(".ty-fileContent").is(":visible")) {
            $("#ye_con").show() ;
            $("#fileSort").show() ;
        }
        for(var i = 0; i < fileInfo.length; i++){
            var id          = fileInfo[i].id,
                file        = fileInfo[i].file,
                category    = fileInfo[i].category,
                name        = fileInfo[i].name,
                size        = fileInfo[i].size,
                path        = fileInfo[i].path,
                fileSn      = fileInfo[i].fileSn,
                changeNum   = fileInfo[i].changeNum,
                fileType    = fileInfo[i].version,
                operation   = fileInfo[i].operation,
                updateName  = fileInfo[i].updateName,
                createName  = fileInfo[i].createName,
                updateDate  = fileInfo[i].updateDate,
                createDate  = fileInfo[i].createDate,
                updator     = fileInfo[i].updator,
                creator     = fileInfo[i].creator,
                version     = fileInfo[i].version.toLowerCase(),
                loginUser   = $("#loginUser").html(),
                loginUserId = 0,
                applyDisabledClass = '',    //定义换版申请禁用class
                recordDisabledClass = '',   //定义换版记录禁用class
                handleStr = '',             //定义操作按钮部分字符串
                recycleId = Number($("#listDoc").data("recycleId")); //回收站id



            //获取当前userId
            loginUser   = JSON.parse(loginUser);
            loginUserId = loginUser.userID;

            // 通用格式可以使用在线预览
            handleStr = customVersion.indexOf(version) !== -1?'<a class="ty-left" path="'+ path +'" onclick="fileSeeOnline($(this))">在线预览</a>': '<a class="ty-left ty-disabled">在线预览</a>'

            //格式化数据
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';

            updateName  === null || updateName  == undefined? updateName = createName:updateName;   //更新人为空时 赋值 创建人，最后统一取更新时间 ， 下同
            updateDate  === null || updateDate  == undefined? updateDate = createDate:updateDate;
            if (!updator) {updator = creator}

            operation   === '3' ? applyDisabledClass  = 'ty-disabled': applyDisabledClass  = ''; //换版申请后的禁用（换版申请后  换版和移动按钮被禁用）
            changeNum   === 0   ? recordDisabledClass = 'ty-disabled': recordDisabledClass = ''; //换版记录的禁用 （换版为0 换版记录按钮禁用）

            //单条文件代码字符串拼接
            fileListStr +=  '<div class="ty-fileItem fileItemRadioChoose" id="'+id+'" pid="'+category+'" data-id="'+id+'">'+
                '   <div class="ty-radio">' +
                '       <input type="radio" id="resource_choose_'+id+'" name="resource_choose">' +
                '       <label for="resource_choose_'+id+'"></label>' +
                '   </div>' +
                '   <div class="ty-left ty-fileType ty-file_'+fileType+'"></div>'+
                '   <div class="ty-fileInfo ty-left">'+
                '       <div class="ty-fileName" title="'+name+ '-G'+changeNum+'">'+ name +'</div>'+
                '       <div class="ty-fileVersion" changeNum="'+ changeNum +'">'+'G'+changeNum+'</div>'+
                '       <div class="ty-fileNo"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                '       <div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + size + ' &nbsp;&nbsp; ' + updateDate +'</div>'+
                '   </div>'+
                '   <div class="hd">'+JSON.stringify(fileInfo[i])+'</div>'+
                '</div>';
        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.siblings(".textMax").text((max - curLength) +'/' + max );
}

// creator: 张旭博，2019-11-18 11:38:02，layDate 插件设置可选择日期
function setCanChooseDate(date) {
    var start = new Date(date.year,date.month-1,1);
    var end = new Date(date.year,date.month,0);
    var startTime = formatTime(start, false)
    var endTime = formatTime(end, false)
    console.log(startTime)
    console.log(endTime)
    $.ajax({
        url:"../forum/monthReplyRecord.do",
        data:{
            id: $(".mainChat").data("theme").id,
            startTime: startTime,
            endTime: endTime,
        },
        success:function(data){
            var dataList= data.listMonthMessage
            if (dataList.length > 0) {
                for(var i in dataList) {
                    var time = start.getFullYear() + '-' + Number(start.getMonth() + 1) + '-' + dataList[i]
                    console.log(time)
                    $(".layui-laydate .layui-laydate-content").find("td[lay-ymd='"+time+"']").removeClass("laydate-disabled")
                }
            }
            console.log(data)
        }
    });
}

// creator: 张旭博，2019-12-17 10:10:48，验证聊天框输入信息
function formatTxt(txt) {
    var text = txt.replace(/<[^<>]+>/g,"");
    var isImg = txt.indexOf("img")
    var isFile = $(".imgUpload .file_item").length > 0
    var isNull = text || isImg !== -1 || isFile
    console.log(isNull)
    return isNull
}

// creator: 张旭博，2019-12-04 16:55:09，添加查看全文字段
function setNewMemo(memo) {
    var firstLetter = memo.indexOf('</small>') + 8
    console.log('firstLetter', firstLetter)
    return memo.slice(0,firstLetter) + ' <span class="seeOrigin" type="btn" data-name="seeOrigin">查看原文</span>' + memo.slice(firstLetter)
}

function customTime(date) {
    if (date) {
        var isToday = new Date().toDateString() === new Date(date).toDateString()
        if (isToday) {
            return new Date(date).toTimeString().substring(0, 8)
        } else {
            return new Date(date).toISOString().substring(0, 10)
        }
    } else {
        return ''
    }
}

laydate.render({
    elem: '#calendar',
    eventElem: '.fa-calendar',
    trigger: 'click',
    ready: function (date) {
        $(".layui-laydate .layui-laydate-content td").addClass("laydate-disabled")
        setCanChooseDate(date)
    },
    change: function(value, date){
        $(".layui-laydate .layui-laydate-content td").addClass("laydate-disabled")
        setCanChooseDate(date)
    },
    done: function (value, date) {
        var param = {
            postId: $(".mainChat").data("theme").id, // 讨论id
            type: 1,   // 5中状态1是全部信息，2是近7日，3是近1个月，4是近3个月，5是近1年
            time: value,   // 查询全部信息时可以点开日历去查询某天的，这时要把时间传过来，当有此值时就不能传findUser和mesType，同理有这两个值时就不能传time
            findUser: '',   // 查找某人的信息时要把这个人id传过来
            mesType: '',    // 查找信息的类型1是全部的信息2是@我的信息3是回复我的信息
            pageSize:20,
            isQuery: false
        }
        getAllReply(param)
    }
});

// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else {
        console.log("未定义的状态")
    }
    cancelFileDel('all', true)
}