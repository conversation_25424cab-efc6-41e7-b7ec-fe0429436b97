var uEditor = UE.getEditor('issueOpinionContainer', {
    autoHeightEnabled: false,
    zIndex: 996,
    initialFrameWidth: '100%',
    initialFrameHeight: 100,
    pasteplain: false,
    allHtmlEnabled: true,
    wordCountMsg: '{#count}/500',
    wordCount: true, //是否开启字数统计
    maximumWords: 500, //允许的最大字符数
    elementPathEnabled: false,
    enableAutoSave: false,
    toolbars: [['bold', 'italic', 'strikethrough', 'forecolor', 'backcolor', 'fontsize', 'emotion']],
    enterTag:'br'
});
jQuery.fn.treeItemSlide = function (type) {
    return $(this).each(function(){
        var iconNode = $(this).children('i').eq(0)
        if (!iconNode.hasClass("ty-fa")) {
            if (type === 'up') {
                return iconNode.removeClass('fa-angle-down').addClass('fa-angle-right')
            } else if (type === 'down') {
                return iconNode.removeClass('fa-angle-right').addClass('fa-angle-down')
            } else if (type === 'toggle') {
                if (iconNode.hasClass("fa-angle-right")) {
                    return iconNode.eq(0).removeClass('fa-angle-right').addClass('fa-angle-down')
                } else {
                    return iconNode.eq(0).removeClass('fa-angle-down').addClass('fa-angle-right')
                }
            }
        }
    });
}
var atUsers = []; // 记录被@的参与人列表（传值时使用）
// 初始化三级弹窗
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
$(function () {
    $('#changeVersion .fileUpload').Huploadify({
        auto: false,
        fileTypeExts: '*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.png;*.jpg;*.wps;*.et;*.md;*.html;',
        formData:{
            module: '事务讨论'
        },
        fileSizeLimit: 40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedSize: true,
        removeTimeout: 99999999,
        buttonText: "系统外文件",
        queueID: 'fileQueue',
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onSelect: function (file, json) {
            console.log('selectData', file)
            var name = file.name
            var newFileArr = name.split(".")
            var newFileType = newFileArr[newFileArr.length - 1]
            newFileType = newFileType.toLowerCase()
            var nowFileType = $("#changeVersion").data("nowFileType")
            if ((newFileType === 'png' || newFileType === 'jpg') && (nowFileType !== 'png' && nowFileType !== 'jpg')) {
                $("#fixed2_tip .text-center").html("您换版所选的文件是图片，但该文件之前不是图片。<br>确定该文件采用了新格式吗？")
                $("#fixed2_tip .sureBtn").unbind().on("click", function() {
                    $("#changeVersion .uploadbtn").click()
                    bounce_Fixed2.cancel()
                })
                $("#fixed2_tip .cancelBtn").unbind().on("click", function() {
                    bounce_Fixed2.cancel()
                    $("#changeVersion .uploadify-queue").html('')
                })
                bounce_Fixed2.show($("#fixed2_tip"))
            } else if ((newFileType !== 'png' && newFileType !== 'jpg') && (nowFileType === 'png' || nowFileType === 'jpg')) {
                $("#fixed2_tip .text-center").html("该文件之前为图片，您换版所选文件不是图片。<br>确定该文件采用了新格式吗？")
                $("#fixed2_tip .sureBtn").unbind().on("click", function() {
                    $("#changeVersion .uploadbtn").click()
                    bounce_Fixed2.cancel()
                })
                $("#fixed2_tip .cancelBtn").unbind().on("click", function() {
                    bounce_Fixed2.cancel()
                    $("#changeVersion .uploadify-queue").html('')
                })
                bounce_Fixed2.show($("#fixed2_tip"))
            } else {
                $("#changeVersion .uploadbtn").click()
            }
            console.log('selectData', json)
        },
        onUploadSuccess: function (file, json) {
            console.log(json)
            var data = JSON.parse(json),
                name = data.filename,
                type = data.lastName,
                size = file.size,
                fileName = file.name
            var replyId = $("#changeVersion").data('replyId')
            $.ajax({
                url: '../forum/updateReplyFileVersion.do',
                data: {
                    replyId: replyId, // 回复id
                    type: (type === 'jpg' || type === 'png' ? 1 : 2), // 1是图片 2是文件
                    path: name, // 文件地址
                    size: size, // 文件大小
                    module: '事务讨论' // 固定传值
                },
                beforeSend: function() {},
                success: function (res) {
                    var data = res.data
                    var state = data.state
                    if (state === 1) {
                        layer.msg("换版成功")
                        bounce.cancel()
                        bounce_Fixed.cancel()
                        $(".fileUpload_progress").next(".fileHandle").prepend("<div class='cvState'><i class=\"fa fa-check-circle-o\"></i> 换版成功</div>")
                        $(".fileUpload_progress").remove()
                    }
                }
            })
        },
        onQueueComplete:function() {
            $("#changeVersion .uploadify-queue").html('')
        }
    })
    subscribeAll() // 订阅四合一通道
    // 获取主题列表
    getThemeList()
    setUEditor() // 配置富文本编辑器相关事件
    $("#theme_search").val("")

    $("#fileSort li").on("click",function () {
        if($(this).hasClass('ty-active')){
            // 如果点击的是已经点击过的，改为升序，获取升序列表
            $(this).find("i").toggleClass("fa-long-arrow-up");
        } else {
            // 如果点击的是未被点击的，获取降序列表
            $(this).find('.sort').show()
            $(this).siblings().find('.sort').hide()
            $(this).find("i").removeClass("fa-long-arrow-up");
        }
        $(this).addClass("ty-active").siblings().removeClass("ty-active");
        var categoryId = $("#chooseSaveFolder .ty-treeItemActive").data("id");
        var type = $("#fileSort li.ty-active").attr("type");
        // "1"代表获取的是按时间降序的列表，“2”代表获取的是时间升序的列表，“3”代表获取的是文件编号升序的列表，“4”代表的是获取文件编号降序的列表
        if (type!=='1') {
            if(!$(this).find("i").hasClass('fa-long-arrow-up')){
                $("#fileSort").data('type', 1)
            }else{
                $("#fileSort").data('type', 2)
            }
        } else {
            if($(this).find("i").hasClass('fa-long-arrow-up')){
                $("#fileSort").data('type', 3)
            }else{
                $("#fileSort").data('type', 4)
            }
        }
        getFile( 1,20, categoryId);
    });
    $(".ty-colFileTree").on("click", ".ty-treeItem", function () {
        var treeItemThis = $(this)
        var categoryId = $(this).data("id")
        var avatarName = $(this).parents(".ty-colFileTree").data("name")
        var url = isGeneral || isSuper ? '../res/getFolderAndChildFolderManager.do' : '../res/getFolderAndChildFolder.do'

        // 处理逻辑
        treeItemThis.parents(".ty-colFileTree").find(".ty-treeItem").removeClass("ty-treeItemActive")   // 取消之前选中
        treeItemThis.addClass("ty-treeItemActive"); // 设置选中
        treeItemThis.treeItemSlide('toggle')        // icon切换（顶部jQuery方法拓展）

        $.ajax({
            url: url,
            data: {"categoryId": categoryId, "type": 1},
            success: function (res) {
                var data = res.data
                if (!data) {
                    $("#mt_tip_ms").html("连接失败，请刷新重试！");
                    bounce.show($("#mtTip"));
                } else {
                    // 渲染返回的信息
                    if (treeItemThis.children().eq(0).hasClass('fa-angle-right')) {
                        treeItemThis.next().remove()
                    } else {
                        showTreeData(data, treeItemThis);
                    }
                    var type = Number($("#chooseSaveFolder").data("type")) // 1-可以选择文件 0-只能选择文件夹
                    if (type === 1) {
                        $("#fileSort li").eq(0).addClass("ty-active").siblings().removeClass("ty-active");
                        $("#fileSort li").eq(0).find('.sort').show()
                        $("#fileSort li").eq(0).siblings().find('.sort').hide()
                        $("#fileSort li").eq(0).find("i").removeClass("fa-long-arrow-up");

                        var pageInfo = data.pageInfo,
                            fileInfo = data.list;

                        //设置分页信息
                        var currentPageNo = pageInfo["currentPageNo"],
                            totalPage = pageInfo["totalPage"],
                            jsonStr = JSON.stringify({"categoryId": categoryId});

                        //设置文件信息
                        var fileListStr = getFileListStr(fileInfo, true);
                        setPage($("#ye_con"), currentPageNo, totalPage, "fileMessage", jsonStr);
                        $(".ty-fileList").html(fileListStr);
                        $(".ty-fileList").attr("type", "folder");

                    }
                }
            }
        })
    })
    $("#chooseSaveFolder,#bubbleChangeVersionRecord").on("click",".ty-fileItem [type='btn']",function (){
        var name = $(this).attr("name")
        if ($(this).attr("disable") === 'true') {
            return false
        }
        switch (name) {
            case 'seeOnline': // 在线预览
                seeOnline($(this))
                break
            case 'download': // 下载
                getDownLoad($(this))
                break
        }
    });

    // 新增讨论 - 切换一人讨论和多人讨论
    $("#newDiscussion .ty-secondTab li").on("click", function () {
        $(this).addClass("ty-active").siblings().removeClass("ty-active")
        var index = $(this).index()
        $("#newDiscussion").find("form").eq(index).show().siblings("form").hide()
        $("#newDiscussion").data("type", index)

        if (index === 0) {
            $("#discussApplyBtn").html("确定")
            $("#newDiscussion select").val('')
            getAllParticipants(5, true).then(res => {
                updateRoleList('1v1', res)
            })
        } else {
            getApprover()
            $("#discussApplyBtn").html("提交申请")
            $("#newDiscussion [name]:not(input:radio)").val('')
            // 初始化
            $("#newDiscussion .input_show .selected_item").remove()
            $("#newDiscussion .input_choose_list").html('')

            $("#newDiscussion .fileUpload").html('')
            $("#newDiscussion .fileShowList").html('')
            $("#newDiscussion [name='isNeedApprove'][value='1']").prop("checked", true)

            $("#newDiscussion .textMax").each(function () {
                var max = $(this).attr('max')
                $(this).html(max + '/' + max)
            })
            var groupUuid = sphdSocket.uuid();
            $('#newDiscussion .fileUpload').Huploadify({
                auto: true,
                fileTypeExts: '*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.png;*.jpg;*.wps;*.et;*.md;*.html;',
                formData:{
                    module: '事务讨论',
                    userId: sphdSocket.user.userID,
                    groupUuid: groupUuid
                },
                fileSizeLimit: 40960,  // 300M = ( 300 * 1024 * 1024 ) B
                showUploadedSize: true,
                removeTimeout: 99999999,
                buttonText: "系统外文件",
                queueID: 'fileQueue',
                uploader:$.webRoot+"/uploads/uploadfyByFile.do",
                onSelect: function () {
                    // $(".fileUpload .uploadify-button").hide();
                },
                onUploadError: function () {
                    $(".fileUpload .uploadify_state").html("上传失败！")
                },
                onUploadSuccess: function (file, json) {
                    console.log(json)
                    var data = JSON.parse(json),
                        name = data.filename,
                        type = data.lastName,
                        size = file.size,
                        fileName = file.name,
                        fileInfo = {
                            path: name,
                            size: size,
                            title: fileName,
                            type: (type === 'jpg' || type === 'png' ? 1 : 2),
                            module: '事务讨论',
                            place: 1
                        };
                    $("#newDiscussion").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
                    $(".fileUpload").data("fileInfo", fileInfo);
                    var str = '<div class="file_item"><div class="hd">'+JSON.stringify(fileInfo)+'</div><div class="ty-fileType ty-file_'+type+'"></div><div class="file_name" title="'+fileName+'">'+fileName+'</div><div class="delFile" fileUid="'+data.fileUid+'">+</div></div>'
                    $("#newDiscussion .fileShowList").append(str);
                },
                onQueueComplete:function() {
                    $(".fileUpload .uploadify-button").show();
                    $(".fileUpload .uploadify-queue").html('')
                }
            })
        }
    })

    // 归档 - 切换图片中选择和非图片中选择
    $("#fileFile input[name='fileChoose']").on("change", function () {
        var value = $("#fileFile input[name='fileChoose']:checked").val()
        getFileList(1, 20, value)
    })

    // 归档 - 文件选中逻辑
    $("body").on("click", '.fileItemRadioChoose', function () {
        $(this).find("input").prop("checked", true)
        $(this).addClass("active").siblings(".fileItemRadioChoose").removeClass("active")
        // return false
    })

    // 弹窗内的按钮点击事件
    $(".bounce,.bounce_Fixed,.bounce_Fixed2").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            // 确定删除参与人
            case 'sureDelRole':
                var delUserDom = $("#delRole").data("delDom")
                var delUser= delUserDom.data("id")
                console.log('delUser',delUser)
                $.ajax({
                    url: "delPostUser.do",
                    data: {
                        postId: $(".mainChat").data("theme").id, // 讨论的id
                        delUser : delUser,
                        userID: sphdSocket.user.userID
                    },
                    success: function (res) {
                        bounce_Fixed.cancel()
                        var success = res.success
                        if (success !== 1) {
                            layer.msg('删除失败')
                        } else {
                            layer.msg('删除成功')
                        }
                    }
                })
                delUserDom.remove()
                break
            // 讨论详情修改
            case 'discussDetailChange':
                bounce_Fixed.show($("#discussDetailChange"))
                var forumPost = $("#seeDetail").data('forumPost')
                $("#discussDetailChange [name='title']").val(forumPost.title)
                $("#discussDetailChange [name='content']").val(forumPost.content)
                $("#discussDetailChange").data("forumPost", {
                    title: forumPost.title,
                    content: forumPost.content,
                    compere: forumPost.compere
                })
                $("#discussDetailChange .textMax").each(function () {
                    var max = $(this).attr('max')
                    $(this).html(max - $(this).prev().val().length + '/' + max)
                })
                $('.bounce_Fixed').everyTime('0.5s','discussDetailChange',function(){
                    var state = 0;
                    $("#discussDetailChange [name][require]").each(function () {
                        if ($.trim($(this).val()) === ''){
                            state++
                        }
                    })
                    if( state > 0){
                        $("#sureDiscussDetailChangeBtn").prop("disabled",true)
                    }else {
                        $("#sureDiscussDetailChangeBtn").prop("disabled",false)
                    }
                });
                $.ajax({
                    url: "getAllParticipants.do",
                    data: {
                        type: 1, // 传1，2，3这三种状态 1代表获取当前讨论的参与人 2代表获取当前机构还不是参与人的人，用于新增参与人时使用，3代表获取除登录人外其它的参与人，用于新增@人员时选人，去除了自己做到不允许@自己
                        userID: sphdSocket.user.userID,
                        postId: $(".mainChat").data("theme").id
                    },
                    success: function (res) {
                        console.log(res)
                        var roleList = res.listPostUser
                        if (roleList) {
                            updateRoleList('changeCompere', roleList)
                            $("#discussDetailChange [name='compere']").val(forumPost.compere)
                        }
                    }
                })
                break
            // 讨论详情修改历史记录
            case 'discussDetailChangeHistory':
                bounce_Fixed.show($("#discussDetailChangeHistory"))
                getDiscussDetailChangeHistory()
                break
            // 查看历史详情
            case 'seeHistoryDetail':
                bounce_Fixed2.show($("#seeHistoryDetail"))
                var data = $(this).parents("tr").find(".hd").text()
                data = JSON.parse(data)
                var roleList = data.listForumPostUserHistory
                var roleArr = []
                for (var i in roleList) {
                    roleArr.push(roleList[i].userName)
                }
                $("#seeHistoryDetail .see_title").html(data.title)
                $("#seeHistoryDetail .see_des").html(data.content)
                $("#seeHistoryDetail .see_compere").html(data.compereName)
                $("#seeHistoryDetail .see_roleList").html(roleArr.join("，"))
                break
            // 归档 - 选择保存位置
            case 'chooseSaveFolder':
                var name = $("#fileUpload").data("name")
                var fileFor = Number($("#fileFile input[name='fileFor']:checked").val())
                var type = fileFor === 1?0:1
                openResourceCenter(name, type)
                break
            // 归档 - 选择保存位置 - 确定
            case 'sureChooseFolder':
                var chooseFolderNode = $("#chooseSaveFolder .ty-treeItem.ty-treeItemActive")
                var chooseFileNode = $("#chooseSaveFolder .ty-fileItem.active")
                str = '<i class="fa fa-folder"></i>' + str
                var name = $("#chooseSaveFolder").data("name")
                var type = $("#chooseSaveFolder").data("type")
                if (type === 1) {
                    var json = chooseFileNode.find(".hd").html()
                    var data = JSON.parse(json),
                        path = data.path,
                        type = data.version,
                        size = data.size,
                        fileName = data.name,
                        fileInfo = {
                            type: 3, // (type === 'jpg' || type === 'png' ? 1 : 2) 改为 3
                            place: 2,
                            resourceId: data.id,
                            resourceVersion: data.changeNum
                        };
                }
                var folderNameObj = chooseFolderNode.parents("li").children(".ty-treeItem").children("span")
                var str = '<'
                folderNameObj.each(function (index) {
                    var name = $(this).text()
                    if(index === 0){
                        str = '<span class="ty-color-red">'+name+'</span>'
                    } else {
                        str = name + ' / ' + str
                    }
                })

                switch (name) {
                    case 'newDiscussion':
                        var str = '<div class="file_item"><div class="hd">'+JSON.stringify(fileInfo)+'</div><div class="ty-fileType ty-file_'+type+'"></div><div class="file_name" title="'+fileName+'">'+fileName+'</div><div class="delFile">+</div></div>'
                        $("#newDiscussion .fileShowList").append(str);
                        break;
                    case 'reply':
                        bounce.cancel()
                        var str = '<div class="file_item"><div class="hd">'+JSON.stringify(fileInfo)+'</div><div class="ty-fileType ty-file_'+type+'"></div><div class="file_name" title="'+fileName+'">'+fileName+'</div><div class="delFile">+</div></div>'
                        $(".sendBar .fileShowList").append(str);
                        break;
                    case 'fileUpload':
                        $("#fileUpload .savePlace").show().html(str)
                        $("#fileUpload .savePlace").data("categoryId", chooseFolderNode.data("id"))
                        break;
                    case 'fileChangeVersion':
                        $("#fileUpload .savePlace").show().html(str)
                        $("#fileUpload .savePlace").data("categoryId", chooseFolderNode.data("id"))
                        var chooseFileName = chooseFileNode.find(".ty-fileName").text()
                        var chooseFileSn = chooseFileNode.find(".ty-fileNo").attr("title")
                        $("#fileUpload [name='fileNo']").val(chooseFileSn);
                        $("#fileUpload [name='name']").val(chooseFileName);
                        break;
                }
                bounce_Fixed2.cancel()
                break
            case 'sureUploadNewFile':
                sureUploadNewFile()
                break
            case 'sureChangeVersion':
                sureChangeVersion()
                break;
            // 导入文件 - 选择换版文件
            case 'chooseChangeVersionFile':
                bounce_Fixed.show($("#chooseChangeVersionFile"))
                $("#chooseChangeVersionFile .fileList").html('')
                $.ajax({
                    url: "findSubsidiaryFile.do",
                    data: {
                        postId: $(".mainChat").data("theme").id, // 讨论的id
                        type: 1,
                        attType: 4
                    },
                    success: function (res) {
                        console.log(res)
                        var allFile = res.listReplyAtt || []
                        var fileStr = ''
                        if (allFile.length > 0) {
                            fileStr = updateFile(allFile, 'changeVersionDialog')
                            $("#chooseChangeVersionFile .fileList").html(fileStr)
                        } else {
                            $("#chooseChangeVersionFile .fileList").html('<div class="null"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无数据</p></div>')
                        }
                    }
                })

                break;
            // 导入文件 - 上传新文件
            case 'uploadNewFile':
                $(".imgUpload .uploadify-button").click()
                break;
            // 导入文件 - 选从文件与资料中选择
            case 'openResourceCenter':
                openResourceCenter('reply', 1)
                break;

        }
    })

    // 左侧主题列表点击事件
    $("#mainThemeList").on("click", 'li.themeItem', function () {

        // 主题内容变化
        $(this).addClass("active").siblings().removeClass("active")
        $(this).find(".corner").remove()
        $(this).find(".atTag").hide()
        $(this).find(".replyTag").hide()

        // 获取绑定数据
        var title = $(this).find(".title").attr('title')
        var id = $(this).data("id")
        var category = $(this).data("category")
        var compere = $(this).data("compere")

        // 为了后面方便使用绑定到主页面
        $(".mainChat").data("theme", {
            id: id,
            category: category, // 讨论类型（一对一、多对多）
            compere: Number(compere)    // 赋值主持人，后面参与人会用到
        })
        $('.imgUpload').html("")
        // 初始化回复区
        var groupUuid = sphdSocket.uuid();
        $('.imgUpload').Huploadify({
            auto: true,
            fileTypeExts: '*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.png;*.jpg;*.wps;*.et;*.md;*.html;',
            fileSizeLimit: 40960,  // 300M = ( 300 * 1024 * 1024 ) B
            showUploadedSize: true,
            removeTimeout: 99999999,
            buttonText: "上传新文件",
            queueID: 'fileQueue',
            formData:{
                module: '事务讨论',
                userId: sphdSocket.user.userID,
                groupUuid: groupUuid
            },
            uploader:$.webRoot+"/uploads/uploadfyByFile.do",
            onSelect: function () {
                $(".imgUpload .uploadify-button").hide();
            },
            onUploadError: function () {
                $(".imgUpload .uploadify_state").html("上传失败！")
            },
            onUploadSuccess: function (file, json) {
                var data = JSON.parse(json),
                    name = data.filename,
                    type = data.lastName,
                    size = file.size,
                    fileName = file.name,
                    fileInfo = {
                        path: name,
                        size: size,
                        title: fileName,
                        type: (type === 'jpg' || type === 'png' ? 1 : 2),
                        module: '事务讨论',
                        place: 1
                    };
                $(".messageInput").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
                $(".imgUpload").data("fileInfo", fileInfo);
                var str = '<div class="file_item"><div class="hd">'+JSON.stringify(fileInfo)+'</div><div class="ty-fileType ty-file_'+type+'"></div><div class="file_name" title="'+fileName+'">'+fileName+'</div><div class="delFile" fileUid="'+data.fileUid+'">+</div></div>'
                $(".messageInput .fileShowList").append(str);
                bounce.cancel()
            },
            onQueueComplete:function() {
                $(".imgUpload .uploadify-queue").html('')
            }
        })
        $(".mainChat .chatBar .title").html(title)
        $(".mainChat .chat_main").html("")
        $(".mainChat .unReadMsg").hide()
        $(".mainChat .seeMore").hide()
        $(".mainChat").show()
        $(".app_null").hide()

        // 显示回复数据
        getPostMessage(0)

        // 初始化富文本编辑器
        clearEditor()
        getAllParticipants(1, false).then(res => {
            updateRoleList('at', res)
            updateRoleList('rightRole', res)
        }) // 获取@人列表

        // 初始化右侧区域
        $(".history_avatar").show()
        if ($("#backToMainBtn").is(":visible") && compere === sphdSocket.user.userID) {
            $("#readingSettings").show()
            $("#delete").show()
        } else {
            $("#readingSettings").hide()
            $("#delete").hide()
        }

        // 默认显示参与人数据
        $("#roleList").show().siblings('.tabCon').hide()
        $(".history_avatar .historyTab li").eq(0).addClass("active").siblings().removeClass("active")

        // 一对一、多对多 不同显示（一对一 查看详情、继续添加按钮不显示）
        if (category === 1) {
            $(".m_btn").hide()
            $(".continueAdd").hide()
        } else {
            $(".m_btn").show()
            if ($("#backToMainBtn").is(":visible")) {
                $(".continueAdd").hide()
            } else {
                // 非当前主持人不显示继续添加
                if (compere === sphdSocket.user.userID) {
                    $(".continueAdd").show()
                } else {
                    $(".continueAdd").hide()
                }
            }

        }
    })
    $(".search_choose_list").on("click", 'li.themeItem', function (){
        var id = $(this).attr("data-id")
        var findSelector =  $("#mainThemeList .themeItem[data-id='"+id+"']")
        if (findSelector.length === 0) {
            var listForumPost = $(".search_choose_list_avatar").data('list')
            var formPost = listForumPost[listForumPost.findIndex(item=>item.id == id)]
            var str = getThemeStr(formPost, 'extra')
            $(".themeList_avatar").prepend(str)
            $(".search_choose_list").hide();
        }
        $("#mainThemeList .themeItem[data-id='"+id+"']").click()
        var length = $("#mainThemeList .themeItem[data-id='"+id+"']").offset().top - $("#mainThemeList").offset().top
        $('#mainThemeList').scrollTop(length);
    })

    // 右侧标签页切换点击事件
    $(".historyTab").on("click", 'li', function () {
        var to = $(this).attr("to")
        $("#" + to).show().siblings('.tabCon').hide()
        switch(to) {
            case 'roleList':
                getAllParticipants(1, true).then(res => {
                    updateRoleList('rightRole', res)
                })
                break;
            case 'allMessage':
                initAllMessage()
                break;
            case 'imageList':
                var arrowType = 1
                if($(this).hasClass('active')){
                    // 如果点击的是已经点击过的，升序改降序，降序改升序
                    $(this).find("i").toggleClass("fa-long-arrow-up");
                    arrowType = $(".history_avatar li.active .sort i").hasClass("fa-long-arrow-up") ? 1 : 3
                } else {
                    // 如果点击的是未被点击的，获取升序列表
                    $(this).find("i").addClass("fa-long-arrow-up");
                    arrowType = 1
                }
                getAllFile('image', arrowType)
                break;
            case 'fileList':
                if($(this).hasClass('active')){
                    // 如果点击的是已经点击过的，升序改降序，降序改升序
                    $(this).find("i").toggleClass("fa-long-arrow-up");
                    arrowType = $(".history_avatar li.active .sort i").hasClass("fa-long-arrow-up") ? 1 : 3
                } else {
                    // 如果点击的是未被点击的，获取升序列表
                    $(this).find("i").addClass("fa-long-arrow-up");
                    arrowType = 1
                }
                getAllFile('file', arrowType)

                break;
        }
        $(this).addClass("active").siblings().removeClass("active")
    })

    // 剩余页面按钮点击事件
    $(".app_avatar,.bounce").on("click", "[type='btn']", function (event) {
        var name = $(this).data("name")
        switch (name) {
            case 'importFile':
                importFileBtn()
                break;
            case 'send':
                addOneReply()
                break;
            case 'seeDetail':
                bounce.show($("#seeDetail"))
                getThemeDetail()
                break;
            case 'messageHistory':{
                $(".history_avatar").toggle()
                break;
            }
            case 'reply':
                var $replyItem = $(this).parents(".msgItem")
                var $replyCopy = $replyItem.clone()
                if ($replyCopy.find(".msgReply").length > 0) {
                    $replyCopy.find(".msgReply").remove()
                }
                $replyCopy.find(".fileItem").each(function () {
                    var name = $(this).find(".fileName").html()
                    $(this).replaceWith('<span class="file_replace">附件: '+name+'</span>')
                })
                var id = $replyItem.data("id")
                var userName = $replyItem.find(".userName").html()
                var applyTime = $replyItem.find(".applyTime").html()
                var replyText = $replyCopy.find(".bubble_avatar").text()
                var content = ''
                console.log($replyCopy)
                content = replyText
                if (replyText.length > 80) {
                    replyText = replyText.substring(0, 80)
                    content = replyText + '...'
                }

                $(".replyAt").html('<div class="replyAt_con">回复'+ '<span class="replyName">'+userName+'</span> ' + '<small>' +applyTime + '</small><div>' + content+'</div></div>')
                $(".replyAt").data("id", id)
                $('.replyAt').show()
                break;
            case 'recall':
                var $replyItem = $(this).parents(".msgItem")
                var data = {
                    userID: sphdSocket.user.userID,
                    replyId: $replyItem.data("id")
                }
                console.log(data)
                $.ajax({
                    url: "delOneReply.do",
                    data: data,
                    success: function (res) {
                        console.log(res)
                        var state = res.data
                        if (state === '1') {
                            layer.msg("您已经成功撤回消息")
                            $replyItem.remove()
                        } else if (state === '2') {
                            layer.msg("撤回已超时")
                        } else if (state === '3'){
                            layer.msg("此条下有回复，无法撤回")
                        } else {
                            layer.msg("撤回失败")
                        }
                    }
                })
                break;
            case 'reloadMore':
                var isSearch = $(this).attr("data-type") === 'search'
                var isForwardTo = $(this).attr("data-type") === 'forwardTo'
                var selector = ''
                var isDisabledThemeList = $('#searchBackToMainBtn').is(":visible")?0:1
                var searchInput = $(".ty-search").val()
                if (isSearch) {
                    selector = $(".search_choose_list_avatar")
                } else if (isForwardTo) {
                    selector = $(".themeList_avatar[data-name='forwardFrom']")
                } else {
                    selector = $("#mainThemeList")
                }
                var lastTime = selector.find(".themeItem").last().find(".lastReplyTime").data("time")

                getPostsList(searchInput, lastTime, isDisabledThemeList, !isSearch).then((res) => {
                    $(".searchBar .loading").hide()
                    // 先清空所有主题
                    var listForumPost = res.listForumPost; // 查找的主题列表 （暂未用到）

                    if (isSearch) {
                        var searchList = $(".search_choose_list_avatar").data('list')
                        $(".search_choose_list_avatar").data('list', [...searchList, ...listForumPost])
                    }

                    // 是否查询到主题
                    if (listForumPost.length === 0) {
                        // selector.html('<div class="null"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无数据</p></div>')
                    } else {
                        // 获取主题列表内容
                        var extra = $("#mainThemeList .themeItem.extra")
                        var extraIds = []
                        if (extra.length > 0) {
                            extra.each(function (index) {
                                extraIds.push($(this).attr("data-id"))
                            })
                        }

                        var themeListStr = ''
                        for (var i = 0; i < listForumPost.length; i++) {
                            if (isSearch) {
                                themeListStr += getThemeStr(listForumPost[i], 'search')
                            } else if (isForwardTo) {
                                themeListStr += getThemeStr(listForumPost[i], 'forwardFrom')
                            } else {
                                var isHasExtra = extraIds.findIndex(item => {return item == listForumPost[i].id})
                                if (isHasExtra  === -1) {
                                    themeListStr += getThemeStr(listForumPost[i])
                                }
                            }
                        }
                        if (isForwardTo) {
                            var leftList = $("#bubbleForward").data("leftList")
                            var newList = [...leftList, ...listForumPost]
                            $("#bubbleForward").data("leftList", newList)
                        }
                        selector.find(".refresh").before(themeListStr)

                        // 判断是否显示加载更多按钮
                        if (listForumPost.length < 15) {
                            $(this).hide()
                        } else {
                            $(this).show()
                        }
                    }
                })
                break;
            case 'addRole':
                bounce_Fixed.show($("#addRole"))
                $("#addRole .bounce_title").html("继续添加参与人")
                getAllParticipants(2, true).then(res => {
                    updateRoleList('continueAdd', res)
                })
                $("#addRole .sureBtn").unbind().on("click", function (){
                    var addUser = []
                    $("#addRole input:checkbox:checked").each(function() {
                        var user = {
                            userID: $(this).attr("value")
                        }
                        addUser.push(user)
                    })
                    if (addUser.length === 0) {
                        layer.msg("请选择参与人")
                        return false
                    }
                    console.log('addUser',addUser)
                    $.ajax({
                        url: "addPostUser.do",
                        data: {
                            postId: $(".mainChat").data("theme").id, // 讨论的id
                            listForumPostUser : JSON.stringify(addUser),
                            userID: sphdSocket.user.userID
                        },
                        success: function (res) {
                            bounce_Fixed.cancel()
                            var success = res.success
                            if (success !== 1) {
                                layer.msg('添加失败')
                            } else {
                                layer.msg('添加成功')
                            }
                        }
                    })
                })
                break;
            case 'delRole':
                $("#delRoleName").html($(this).parents("li").find(".name").text())
                bounce_Fixed.show($("#delRole"))
                $("#delRole").data("delDom", $(this).parents("li"))
                break;
            case 'query':
                $(".queryCondition").show().siblings().hide()
                $(".queryCondition [name]").val("1")
                $(".queryCondition [name='findUser']").val("")
                getAllParticipants(1, false).then(res => {
                    updateRoleList('query', res)
                })
                break;
            case 'sureQuery':
                var data = {
                    postId: $(".mainChat").data("theme").id, // 讨论id
                    isQuery: true
                }
                $(".queryCondition [name]").each(function () {
                    var name = $(this).attr("name")
                    data[name] = $(this).val()
                })
                getAllReply(data)
                break;
            case 'seeMore':
                getPostMessage(1)
                break
            case 'unReadMsg':
                var unReadMsg = $(this).data("unReadMsg")
                var unreadNum = unReadMsg - $(".chat_main .msgItem").length
                if (unreadNum > 0) {
                    getPostMessage(3, unreadNum)
                } else {
                    $(".unReadMsg").hide()
                    var length = $(".chat_avatar .msgItem").eq(-unreadNum).offset().top - $(".chat_avatar .msgItem").eq(0).offset().top
                    $('.chat_avatar').scrollTop(length);
                }
                break;
            case 'seeOrigin':
                var parentReplyId = $(this).parents(".msgReply").data("origin")
                var lastReplyId = $(".chat_main .msgItem").first().data("id")
                if (parentReplyId < lastReplyId) {
                    getPostMessage(2, parentReplyId)
                } else {
                    var pTop = $(".chat_main").offset().top
                    var cTop = $(".chat_main .msgItem[data-id='"+parentReplyId+"']").offset().top
                    $('.chat_avatar').scrollTop(cTop-pTop);
                    $(".chat_main .msgItem[data-id='"+parentReplyId+"']").addClass("choosen")
                    setTimeout(function () {
                        $(".chat_main .msgItem[data-id='"+parentReplyId+"']").removeClass("choosen")
                    }, 3000 )
                }
                break
            case 'queryBack':
                initAllMessage()
                break
        }
    })

    // 按 ctrl + 回车 提交
    $(window).keydown(function (event) {
        if (event.which === 13 && event.ctrlKey) {
            addOneReply()
        }
    })

    $(".w-e-text").keydown(function (event) {
        if (event.which === 8) {
            if (editor.txt.text() === ''){
                $('.replyAt').hide()
                $('.replyAt').html('')
                $('.replyAt').data('id', '')
            }
        }
    })


    // 初始化参与人员选择列表
    $(".departTree").on("click","li>div",function(){
        $(this).next().toggle();
    });
    $(".input_choose").on('click','.delRole', function () {
        $(this).parents('.selected_item').remove()
    })
    $("body").on('click', '.delFile', function () {
        $(this).parents('.file_item').remove()
        var uid = $(this).attr("fileUid")
        if (uid) {
            var option = {type: 'fileUid', fileUid: uid}
            fileDelAjax(option)
        }
    })
    $(".pageBtnGroup i").on("click", function () {
        var type = $(this).data("type")
        var pageInfo = $("#calendar").data("pageInfo")
        console.log(pageInfo)
        var currentPageNo = pageInfo.currentPageNo
        var totalPage = pageInfo.totalPage
        var pageNo = 0
        var isDisabled = $(this).hasClass("disabled")
        switch (type) {
            case 'first':
                pageNo = totalPage
                break
            case 'prev':
                pageNo = currentPageNo + 1
                break
            case 'next':
                pageNo = currentPageNo - 1
                break
            case 'last':
                pageNo = 0
                break
        }
        if (!isDisabled) {
            var param = {
                postId: $(".mainChat").data("theme").id, // 讨论id
                type: 1,   // 5中状态1是全部信息，2是近7日，3是近1个月，4是近3个月，5是近1年
                time: '',   // 查询全部信息时可以点开日历去查询某天的，这时要把时间传过来，当有此值时就不能传findUser和mesType，同理有这两个值时就不能传time
                findUser: '',   // 查找某人的信息时要把这个人id传过来
                mesType: '',    // 查找信息的类型1是全部的信息2是@我的信息3是回复我的信息
                currentPageNo:pageNo,
                pageSize:20,
                isQuery: false
            }
            getAllReply(param)
        }
    })
    $(".fa-calendar").click(function () {
        $("#calendar").focus()
    })
    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
    $(".app_avatar").on("click", 'img.avatar', function () {
        imgViewer($(this))
    })
    $("input:radio[name='isNeedApprove']").on('change', function () {
        if ($(this).val() === '0') {
            $(this).parents(".bonceContainer").find(".discussChooseApprover").val("")
        }
    })
    // 账号下拉列表点击其他地方收回
    $(document).click(function (e) {
        console.log(e.target)
        if(!$(".searchBar").is(e.target)&&$(".searchBar").has(e.target).length===0){
            $(".search_choose_list").hide();
            $("#theme_search").val('')
        }
    });

    var stateVersion = 0
    // 账号输入事件，搜索功能
    $(".searchInput").on('click input', 'input', function () {
        $(".search_choose_list_avatar").html("")
        $(".search_choose_list").show()
        var searchInput = $(".ty-search").val()
        var isDisabledThemeList = $('#searchBackToMainBtn').is(":visible")?0:1
        if (searchInput !== '') {
            stateVersion ++
            getPostsList(searchInput, '', isDisabledThemeList, false, stateVersion).then((res) => {
                // 先清空所有主题

                var stateVersion1 = res.stateVersion
                if (stateVersion1 === stateVersion) {
                    $(".searchBar .loading").hide()

                    var listForumPost = res.listForumPost; // 查找的主题列表
                    $(".search_choose_list_avatar").data('list', listForumPost)

                    // 是否查询到主题
                    if (listForumPost.length === 0) {
                        $(".search_choose_list_avatar").html('<div class="searchNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>搜索暂无数据</p></div>')
                    } else {
                        // 获取主题列表内容
                        var themeListStr = ''
                        for (var i = 0; i < listForumPost.length; i++) {
                            themeListStr += getThemeStr(listForumPost[i], 'search')
                        }
                        // 判断是否显示加载更多按钮
                        if (listForumPost.length === 15) {
                            themeListStr += ' <div class="refresh" type="btn" data-name="reloadMore" data-type="search">' +
                                            '    <i class="fa fa-refresh"></i>' +
                                            '</div>'
                        }
                        $(".search_choose_list_avatar").html(themeListStr)
                    }
                }
            })
        } else {
            $(".search_choose_list_avatar").html("")
        }
        return false
    })

    // 账号下拉列表点击事件，选择账号
    $(".search_choose_list").on('click', '.input_choose_item', function () {
        $("#phone input").val($(this).html())
        $(".search_choose_list").hide();
        return false
    })

    $(".chox").on("click", ".themeItem.forwardFrom", function () {
        // 交互
        var checkbox = $(this).find("input:checkbox")
        var isChecked = checkbox.prop("checked")
        checkbox.prop("checked", !isChecked)
        var id = $(this).data("id")
        var leftList = $("#bubbleForward").data("leftList")

        leftList[leftList.findIndex(item => item.id === id)].checked = !isChecked
        var newRightList = leftList.filter(item => item.checked)

        $("#bubbleForward").data("leftList", leftList)
        $("#bubbleForward").data("rightList", newRightList)

        renderThemeList(leftList, newRightList)
    })
    $(".chox").on("click", ".themeItem.forwardTo", function () {
        // 交互
        var id = $(this).data("id")
        var leftList = $("#bubbleForward").data("leftList")

        leftList[leftList.findIndex(item => item.id === id)].checked = false
        var newRightList = leftList.filter(item => item.checked)

        $("#bubbleForward").data("leftList", leftList)
        $("#bubbleForward").data("rightList", newRightList)

        renderThemeList(leftList, newRightList)
    })

    $("#chooseChangeVersionFile").on("click", ".fileItem", function (){
        bubbleChangeVersion($(this).find(".fileHandle"), 1)
    })
});

// creator: 张旭博，2022-11-15 11:43:53， 渲染转发内主题列表
function renderThemeList(left, right) {

        // 获取主题列表内容
    var leftStr = '', rightStr = ''
    for (var i = 0; i < left.length; i++) {
        leftStr += getThemeStr(left[i], 'forwardFrom')
    }

    // 判断是否显示加载更多按钮
    if ($(".refresh[data-type='forwardTo']").is(":visible")) {
        leftStr +=  ' <div class="refresh" type="btn" data-name="reloadMore" data-type="forwardTo">' +
            '    <i class="fa fa-refresh"></i>' +
            '</div>'
    }
    for (let j in right) {
        rightStr += getThemeStr(right[j], 'forwardTo')
    }
    $("#bubbleForward .themeList_avatar[data-name='forwardFrom']").html(leftStr)
    $("#bubbleForward .themeList_avatar[data-name='forwardTo']").html(rightStr)
}

// creator: 张旭博，2021-03-11 09:18:16，初始化富文本编辑器
function setUEditor() {
    // 监听富文本加载完成
    uEditor.ready(function (editor) {
        $(".edui-editor-messageholder.edui-default").css({"visibility": "hidden"});
        $("#addConf").attr("class", "ty-right ty-btn ty-btn-big ty-btn-green ty-circle-3").attr("onclick", "addConf2($(this))");
    });
    // 实现@功能
    var prevKey = "";
    uEditor.addListener("keyUp", function (type, event) {
        var focusNode = uEditor.selection.getStart();
        var txt = $(focusNode).text();
        var lastKey = txt.substr(txt.length - 1, 1);
        var domUtils = UE.dom.domUtils;
        // 监听@ 事件
        var nowKey = event["key"];
        // console.log("prevKey = " + prevKey + ", nowKey = " + nowKey);
        if (event["type"] == "keyup" && (( (prevKey == "@" || prevKey == "2") && nowKey == "Shift") || (prevKey == "Shift" && nowKey == 2) || nowKey == "@")) {
            $(".atContainer").show();
            //获取编辑器的坐标
            var $ueditor_offset = $("#ueditor_0").offset();
            //创建一个临时dom，用于获取当前光标的坐标
            var range = uEditor.selection.getRange();
            var bk_start = range.createBookmark().start;
            //设置临时dom不隐藏
            bk_start.style.display = '';
            //计算出x坐标
            var x = ($ueditor_offset.left + domUtils.getXY(bk_start).x);
            //计算出y坐标
            var y = ($ueditor_offset.top -bk_start.offsetHeight - domUtils.getXY(bk_start).y);
            //移除临时dom
            $(bk_start).remove();
            $(".atContainer").offset({top: y, left: x});
        } else {
            $(".atContainer").hide();
            // 设置被@的人不可编辑

            var valCusor = domUtils.getStyle(focusNode, 'cursor');
            if (valCusor == "pointer") {
                if (event["key"] == "Backspace" && event["keyCode"] == 8) { // 处理删除的情况
                    var title = focusNode.title
                    atUsers.splice(atUsers.findIndex(item => item === title), 1)
                    focusNode.remove();
                } else if (event["key"] == "ArrowLeft" && event["keyCode"] == 37) { // 处理左右键的问题
                    // focusNode.setCursor() ;
                    var range = uEditor.selection.getRange();
                    range.collapse();

                } else if (event["key"] == "ArrowRight" && event["keyCode"] == 39) {
                    // focusNode.setCursor(true) ;
                    var range = uEditor.selection.getRange();
                    range.collapse(true);

                } else { // @ 的人被编辑了就取消@特效改为普通文本
                    focusNode.remove();
                    txt = $.trim(txt);
                    uEditor.execCommand('inserthtml', txt);
                }
            }
        }
        prevKey = nowKey;
    });
    // 选择@ 的人
    $(".atContainer").on("click", ".atItem", function () {
        var name = $(this).children("span:eq(0)").html();
        var userID = $(this).attr("userID");
        var str =
            "<span class='atPerson' readonly='readonly' title='"+userID+"' style='padding:0 5px; border-radius:4px; color:#ca2839; background:#e1c2c5;cursor: pointer;text-decoration: none!important;  '>" +
            "<span>" + name + "</span>" +
            "</span>" +
            "<span>&nbsp;</span>";
        uEditor.execCommand('inserthtml', str);
        $(".atContainer").hide();
        atUsers.push(userID);
    });

    // × 点击事件
    $(".clearInput").on({
        "mousedown": function () {
            $(this).prev().val("");
        },
        "mouseup": function () {
            $(this).prev().focus();
            $(".search_choose_list").hide();
        }
    });
    $("#eidtor .clearInput").on("click", function () {
        $(".replyAt").html("").hide()
    })
}

// creator: 张旭博，2019-11-18 11:42:31，订阅
function subscribeAll() {
    // 四合一通道（新增删除回复，新增删除参与人）
    sphdSocket.subscribe('addOneReply', function (res) {
        console.log('addOneReplyResponse', JSON.parse(res))
        // 存储和这条讨论有关的数据
        res = JSON.parse(res)
        var forumPost = res.forumPost
        var msgPostId = forumPost.id // 消息影响的主题id
        var activeTheme = $(".mainChat").data("theme"); // 当前讨论的主题id
        if (activeTheme) {var activePostId = activeTheme.id} else {var activePostId = 0}

        var $msgTheme = $(".themeList [data-id='"+msgPostId+"']")

        // 新增一条消息时返回的数据
        if (res.addForumReply) {
            var forumReply = res.addForumReply // 返回的是个数组
            forumReply.sort((a,b) => {return b.id-a.id}) // 降序排序

            // 收到的消息属于当前选中的主题时
            if (msgPostId === activePostId) {
                // 讨论区内新增新消息
                var comments = forumReply

                var cmtStr = updateMsg(comments, 4)
                // 讨论区之前无消息时先清除提示
                if ($(".chat_main .msgItem").length < 1) {
                    $(".chat_main").html("")
                }
                // 追加消息，滚动条滚动到最底部
                $(".chat_avatar .chat_main").append(cmtStr)
                $('.chat_avatar').animate({scrollTop: $(".chat_avatar").prop("scrollHeight")}, 'normal', 'linear');
                $(".mainChat .chatBar .title").html(forumPost.title)
            }
            // 左侧主题变动
            forumPost.lastReply = forumReply[0]
            var str = getThemeStr(forumPost)

            if ($msgTheme.length > 0) {
                var atTag = forumPost.atTag
                var replyTag = forumPost.replyTag

                // 替换更新的主题信息（最后回复，最后回复时间）
                $msgTheme.replaceWith(str)

                // 恢复主题状态（选中状态，回复和@标记，角标变化）
                $msgTheme = $(".themeList [data-id='"+msgPostId+"']") // 更新选择器
                if (msgPostId === activePostId) {
                    $msgTheme.addClass("active")
                    $.ajax({
                        url: '../forum/removeLeftPostListNumber.do',
                        data: {
                            userID : sphdSocket.user.userID,
                            id: msgPostId
                        },
                        beforeSend: function () {},
                        complete: function () {}
                    })
                    $msgTheme.find(".corner").html("0").hide()
                    $msgTheme.find(".atTag").hide()
                    $msgTheme.find(".replyTag").hide()
                } else {
                    if (atTag === '1') {
                        $msgTheme.find(".atTag").show()
                    }
                    if (replyTag === '1') {
                        $msgTheme.find(".replyTag").show()
                    }
                }

                // 置顶
                $(".app_avatar #mainThemeList").prepend($(".themeList [data-id='"+msgPostId+"']"))
            } else {
                if ($(".themeList #mainThemeList .themeItem").length < 1) {
                    $(".themeList").show().siblings(":not('.app_null')").hide()
                    $(".app_null").show()
                }
                $(".app_avatar #mainThemeList").prepend(str)
            }
        }

        // 撤销一条消息时返回的数据
        if (res.delForumReply) {
            var forumReply = res.delForumReply
            var replyId = forumReply.id

            // 如果正好处在消息所在的讨论，直接删除对应消息
            if (msgPostId === activePostId) {
                // 讨论区内新增新消息
                var comments = []
                comments.push(forumReply)
                var cmtStr = updateMsg(comments)
                $(".chat_main .msgItem[data-id='"+replyId+"']").replaceWith(cmtStr)
            }

            // 左侧主题变动
            forumPost.lastReply = forumReply
            var str = getThemeStr(forumPost)

            if ($msgTheme.length > 0) {

                // 替换更新的主题信息（最后回复，最后回复时间）
                $msgTheme.replaceWith(str)

                // 恢复主题状态（选中状态，回复和@标记，角标变化）
                $msgTheme = $(".themeList [data-id='"+msgPostId+"']") // 更新选择器
                if (msgPostId === activePostId) {
                    $msgTheme.addClass("active")
                    $.ajax({
                        url: '/forum/removeLeftPostListNumber.do',
                        data: {
                            userID : sphdSocket.user.userID,
                            id: msgPostId
                        },
                        beforeSend: function () {},
                        complete: function () {}
                    })
                    $msgTheme.find(".corner").html("0").hide()
                    $msgTheme.find(".atTag").hide()
                    $msgTheme.find(".replyTag").hide()
                } else {
                    if (atTag === '1') {
                        $msgTheme.find(".atTag").show()
                    }
                    if (replyTag === '1') {
                        $msgTheme.find(".replyTag").show()
                    }
                }

                // 置顶
                $(".app_avatar #mainThemeList").prepend($(".themeList [data-id='"+msgPostId+"']"))
            } else {
                $(".app_avatar #mainThemeList").prepend(str)
            }
        }

        // 新增一个参与人通道
        if (res.newListUser) {
            var addUser  = res.newListUser
            if (msgPostId === activePostId) {
                var userList = $(".history_avatar #roleList ul").data('userList')
                var userLists = [...userList, ...addUser]
                updateRoleList('rightRole', userLists)
                if ($("#seeDetail").is(":visible")) {
                    if (activeTheme.compere === sphdSocket.user.userID) {
                        var userList2 = $("#seeDetail .roleList").data('userList')
                        var userLists = [...userList2, ...addUser]
                        updateRoleList('seeCompere', userLists)
                    }
                }
            }
        }

        // 删除一个参与人通道
        if (res.delUser) {
            var delUser  = res.delUser

            if (msgPostId === activePostId) {
                // 参与人列表内删除
                var userList = $(".history_avatar #roleList ul").data('userList')
                userList.splice(userList.findIndex(item => item.userID === delUser.userID), 1)
                updateRoleList('rightRole', userList)
                if ($("#seeDetail").is(":visible")) {
                    if (activeTheme.compere === sphdSocket.user.userID) {
                        var userList2 = $("#seeDetail .roleList").data('userList')
                        userList2.splice(userList2.findIndex(item => item.userID === delUser.userID), 1)
                        updateRoleList('seeCompere', userList2)
                    }
                }
            }
            var $msgTheme = $(".themeList .themeItem[data-id='" + msgPostId + "']")
            if (delUser.userID === sphdSocket.user.userID) {
                if ($msgTheme) {
                    $msgTheme.remove()
                    if (msgPostId === activePostId) {
                        $(".mainChat").removeData("theme")
                    }
                    if ($(".themeList .themeItem").length === 0) {
                        $(".app_null").show().siblings().hide()
                        $("#discuss_apply_s").show()
                    } else {
                        $(".themeList").show().siblings(":not('.app_null')").hide()
                        $(".app_null").show()
                    }
                }
            }
        }

        // 更换主持人
        if (res.newCompere) {
            var newCompere = res.newCompere
            if (msgPostId === activePostId) {
                var data = $(".mainChat").data("theme")
                if (data) {
                    data.compere = newCompere.userID
                    $(".mainChat").data("theme", data)
                    var userList = $(".history_avatar #roleList ul").data('userList')
                    updateRoleList('rightRole', userList)
                    if (newCompere.userID === sphdSocket.user.userID) {
                        $(".continueAdd").show()
                    } else {
                        $(".continueAdd").hide()
                    }
                }
            }
        }
    }, null, 'user');
}

// creator: 张旭博，2019-04-03 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 归档页面的验证
            case 'fileFile':
                if ($("#fileFile .active").length === 0 || $("#fileFile input:radio[name='fileFor']:checked").length === 0) {
                    $("#fileFileBtn").prop("disabled", true)
                } else {
                    $("#fileFileBtn").prop("disabled", false)
                }
                break;
            // 归档选择文件后文件上传部分的验证
            case 'fileUpload':

                var state = 0

                var isNeedOther = $("#fileUpload input:radio:checked").val()
                if (isNeedOther === '1' && $("#fileUpload .chooseApprover").val() === ''){
                    state++
                }
                $("#fileUpload input[require]").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })

                if ($("#fileUpload").data("fileInfo") === '') {
                    state ++
                }

                if ( state > 0 || $("#fileUpload .savePlace").html() === '') {
                    $("#sureUploadNewFileBtn").prop("disabled",true)
                }else {
                    $("#sureUploadNewFileBtn").prop("disabled",false)
                }
                break;
            // 归档选择文件后文件换版部分的验证
            case 'changeVersion':
                var state = 0
                var isNeedOther = $("#fileUpload input:radio:checked").val()
                if (isNeedOther === '1' && $("#fileUpload .chooseApprover").val() === ''){
                    state++
                }
                if ($("#fileUpload").data("fileInfo") === '') {
                    state ++
                }
                if ( state > 0 ) {
                    $("#sureUploadNewFileBtn").prop("disabled",true)
                }else {
                    $("#sureUploadNewFileBtn").prop("disabled",false)
                }
                break
            // 文件上传/文件换版 选择文件位置验证
            case 'chooseSaveFolder':
                let isDis = true
                if ($(".mar").is(":visible")) {
                    if($("input[name='resource_choose']:checked").length > 0 && $("#chooseSaveFolder .ty-colFileTree .ty-treeItemActive").length > 0){
                        isDis = false
                    }
                } else {
                    if($("#chooseSaveFolder .ty-colFileTree .ty-treeItemActive").length > 0){
                        isDis = false
                    }
                }
                $("#sureChooseFolderBtn").prop("disabled",isDis );

                break;
        }

    });
}

// creator: 张旭博，2019-11-02 15:07:02，新增一条讨论 - 确认
function sure_NewDiscuss(){
    var fileObjJson = [];
    var participant = [];

    var type = $("#newDiscussion").data("type") // 0 一人讨论 1 多人
    if (type === 0) {
        var userID  = $(".insertOneForum [name='forumPostUser']").val();
        var userName= $(".insertOneForum [name='forumPostUser']").find('option:selected').attr("name");
        participant.push({
            userID: userID,
            userName: userName
        });
        var data = {
            userID: sphdSocket.user.userID,
            category: 1,
            listForumPostUser: JSON.stringify(participant)
        };
    } else {
        var title = $(".insertForum [name='title']").val();
        var content = $(".insertForum [name='content']").val();
        $(".insertForum .fileShowList .file_item").each(function(){
            fileObjJson.push(JSON.parse($(this).find('.hd').html()));
        });
        $(".insertForum .input_show .selected_item").each(function(){
            var userID  = $(this).data("id");
            var userName= $(this).find('.selected_name').html();
            participant.push({
                userID: userID,
                userName: userName
            });
        });
        var data = {
            userID: sphdSocket.user.userID,
            title: title,
            content: content,
            valid: 1,
            listForumPostAttachment: JSON.stringify(fileObjJson),
            listForumPostUser: JSON.stringify(participant),
            participantsNum: participant.length,
        };
        data.isNeedOther = $(".insertForum input:radio:checked").val()
        if (data.isNeedOther === '1') {
            // 选择了审批人
            data.type = 2
            data.auditorName = $(".discussChooseApprover").find("option:selected").html()
            data.auditor = $(".discussChooseApprover").val()
        } else {
            // 由文管直接发布
            data.type = 1
        }
    }
    $.ajax({
        url:"../forum/insertOneForum.do",
        data:data,
        success:function(data){
            bounce.cancel()
            var state = data.success
            if (state === 1) {
                if (type === 0) {
                    layer.msg("新增成功")
                    // 取消删除程序
                    var groupUuidObj = $("#newDiscussion").data("groupUuidObj")
                    cancelFileDel(groupUuidObj)
                } else {
                    bounce.show($("#newDisTip"));
                }
            } else {
                layer.msg('发起失败！')
            }
        }
    });
}

function chooseRole() {
    bounce_Fixed.show($("#addRole"))
    $("#addRole .bounce_title").html("选择参与人")
    getAllParticipants(5, true).then(res => {
        updateRoleList('1vn', res)
        $(".input_choose .input_show .selected_item").each(function (){
            var id = $(this).data("id")
            $("#addRole input:checkbox[value='"+id+"']").prop("checked", true)
        })
    })
    $("#addRole .sureBtn").unbind().on("click", function (){
        var addUser = []
        var str = ''
        $("#addRole input:checkbox:checked").each(function() {
            var role_id = $(this).attr("value")
            var role_name = $(this).attr("name")
            str +=   '<div class="selected_item" data-id="'+role_id+'">' +
                '    <span class="selected_name">'+role_name+'</span>' +
                '    <span class="delRole">+</span>' +
                '</div>'
        })
        if (str === '') {
            layer.msg("请选择参与人")
            return false
        }
        console.log('addUser',addUser)
        $(".input_show").html(str)
        bounce_Fixed.cancel()
    })
}

// creator: 张旭博，2022-08-10 09:06:18， 设置主页面主题列表
function getThemeList(isDisabled) {
    getPostsList('','', isDisabled).then((res) => {
        $(".app_null").show()
        $(".themeList").show().siblings(":not('.app_null')").hide()
        $("#discuss_apply_s").hide()
        // 先清空所有主题
        $("#mainThemeList").html("")
        var listForumPost = res.listForumPost; // 主题列表

        // 是否查询到主题
        if (listForumPost.length === 0) {
            $("#mainThemeList").html('<div class="null"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无数据</p></div>')
        } else {
            // 获取主题列表内容
            var themeListStr = ''
            for (var i = 0; i < listForumPost.length; i++) {
                themeListStr += getThemeStr(listForumPost[i])
            }

            // 判断是否显示加载更多按钮
            if (listForumPost.length === 15) {
                themeListStr += ' <div class="refresh" type="btn" data-name="reloadMore" data-type="main">' +
                                '    <i class="fa fa-refresh"></i>' +
                                '</div>'
            }
            $("#mainThemeList").html(themeListStr)
        }
    })
}

// creator : hxz date:2018/10/31 获取讨论主题列表
function getPostsList(title = '', updateDate = '', isDisabled = 1, isLoading = true, stateVersion) {
    return new Promise((resolve, reject) => {
        var data = {
            userID: sphdSocket.user.userID,
            title: title,
            enabled: isDisabled,
            updateDate: updateDate,
            stateVersion: stateVersion
        }
        $.ajax({
            url : "getForumByAuth.do" ,
            data: data,
            beforeSend: function() {
                if (isLoading) {
                    loading.open()
                }
                if (title !== '') {
                    $(".searchBar .loading").show()
                }
            },
            success: function ( data ){
                var res = data.data
                if (res) {
                    resolve(res)
                } else {
                    reject()
                }
            }
        });
    })
}

// creator: 张旭博, 2020-07-15 23:24:33, 获取左侧主题列表字符串
function getThemeStr(forumPost, type) {
    var str = ''
    var corner          = forumPost.messageNum;
    var lastReplyTime   = customTime(forumPost.updateDate);
    var lastReply   = '';
    var atTag   = forumPost.atTag;
    var replyTag   = forumPost.replyTag;
    var isCorner        = corner && !type? '<span class="corner">' + corner + '</span>':'<span class="corner" style="display:none;">0</span>';
    var isAtTagShow     = atTag === 1? '':'style="display:none"';
    var isReplyTag    = replyTag === 1? '':'style="display:none"';
    var isJumpBtn    = type === 'search'? '<i class="fa fa-chevron-right"></i><span class="hd">'+JSON.stringify(forumPost)+'</span>':'';

    if (forumPost.lastReply) {
        var msgType = forumPost.lastReply.msgType
        var file = forumPost.lastReply.forumReplyAttachmentHashSet || []

        // 转换最后一条回复中的文件和图片为汉字
        var fileStr = ''
        for (var i in file) {
            if (file[i].type === '1') {
                fileStr += '【图片】'
            } else {
                fileStr += '【文件】'
            }
        }
        forumPost.lastReply.content = forumPost.lastReply.content|| ''
        if (msgType === '1') {
            lastReply = '<div class="lastReply">' + forumPost.lastReply.createName + '：' + fileStr + forumPost.lastReply.content.replace(/<[^>]+>/g,"") +'</div>'
        } else if (msgType === '2') {
            lastReply = '<div class="lastReply">' + forumPost.lastReply.content + '</div>'
        } else {
            lastReply = '<div class="lastReply">' + forumPost.lastReply.content.replace(/<[^>]+>/g,"") + fileStr + '</div>'
        }
    }
    var forwardFromStr = '', forwardToStr = ''
    if (type === 'forwardFrom') {
        forwardFromStr = '<div class="ty-checkbox"><input type="checkbox" name="" id="chox_'+forumPost.id+'" '+(forumPost.checked?'checked':'')+'><label for="chox_'+forumPost.id+'"></label></div>'
    }
    if (type === 'forwardTo') {
        forwardToStr = '<i class="fa fa-times-circle delTheme" type="btn" data-name="delTheme"></i>'
    }

    str +=  '<li class="themeItem '+(type || '')+'" data-id="'+forumPost.id+'" data-category="'+forumPost.category+'" data-compere="'+forumPost.compere+'">' + forwardFromStr +
            '   <div class="themeItem_avatar">' +
            '       <div class="item_title">' +
            '           <div class="title" title="'+forumPost.title+'">' +
            '              <span class="participantsNum" data-num="'+forumPost.participantsNum+'">'+forumPost.participantsNum+'人</span>' +
            '              <span>'+forumPost.title+'</span>' +
            '           </div>' +
            '           <span class="lastReplyTime" data-time="'+formatTime(forumPost.updateDate, true)+'">'+lastReplyTime+'</span>' +
            '       </div>' +
            '       <div class="item_des">' +
            '           <b class="atTag ty-color-red" '+isAtTagShow+'>[有人@你]</b> <b class="replyTag ty-color-red" '+isReplyTag+'>[有人回复你]</b>' + lastReply + isCorner + isJumpBtn +
            '       </div>'+
            '   </div>'+ forwardToStr +
            '</li>'
    return str
}

// creator: 张旭博，2019-12-11 14:31:25，新增一条讨论
function addOneReply() {
    var content = uEditor.getContent();
    var count = $("#eidtor").find(".edui-editor-wordcount").text();
    if (count.indexOf("拒绝保存") > 0) {
        layer.msg("文本字数超过限制，无法保存！");
        return false;
    }
    var postId = $(".mainChat").data("theme").id
    var isNotNull = formatTxt(content)

    var fileJson = []
    $(".messageInput .file_item").each(function () {
        var fileStr = $(this).find('.hd').html()
        fileJson.push(JSON.parse(fileStr));
    })
    if (!isNotNull && fileJson.length === 0) {
        layer.msg("请输入值再发送")
        return false
    }
    atUsers = [...new Set(atUsers)]

    if (content) {
        // 后台要求全部分开发送 且某个发送完再发送另一个，先发送内容再发送文件，多个文件再循环下一个文件
        var data = {
            userID: sphdSocket.user.userID,
            postId: postId,
            content: content,
            listUser: JSON.stringify(atUsers)
        }
        if ($(".replyAt").is(":visible")) {
            data.reply = $(".replyAt").data("id")
            data.parentContent = $(".replyAt_con").html()
        }
        $.ajax({
            url: "addOneReply.do",
            data: data,
            success: function (res) {
                console.log(res)
                var success = res.success
                if (success !== 1) {
                    layer.msg('提交失败，请重新提交')
                }
                // 看是否有文件需要发送
                if (fileJson.length > 0) {
                    sureAddOneReplay(fileJson, 0)
                }
            }
        })

    } else {
        if (fileJson.length > 0) {
            sureAddOneReplay(fileJson, 0)
        }
    }
    console.log('addOneReplyParam', data)
    clearEditor()
}

function sureAddOneReplay (fileJson, i) {
    var list = []
    list.push(fileJson[i])
    var postId = $(".mainChat").data("theme").id
    var data = {
        userID: sphdSocket.user.userID,
        postId: postId,
        listforumReplyAttachment: JSON.stringify(list),
        listUser: JSON.stringify(atUsers),
        content: ''
    }
    if ($(".replyAt").is(":visible")) {
        data.reply = $(".replyAt").data("id")
        data.parentContent = $(".replyAt_con").html()
    }
    var pm_addFile = new Promise((resolve, reject) => {

        $.ajax({
            url: "addOneReply.do",
            data: data,
            success: function (res) {
                console.log(res)
                var success = res.success
                if (success !== 1) {
                    layer.msg('提交失败，请重新提交')
                    reject("'提交失败，请重新提交'")
                } else {
                    resolve()
                }
                // 取消删除程序
                var groupUuidObj = $(".messageInput").data("groupUuidObj")
                cancelFileDel(groupUuidObj)
            }
        })
    })
    i++
    pm_addFile.then(() => {
        if (i < fileJson.length) {
            sureAddOneReplay(fileJson, i)
        } else {
            console.log("循环完毕")
        }
    })
}

// creator: 张旭博，2022-12-02 09:00:28，
function importFileBtn() {
    bounce.show($("#importFile"))
}

// creator: 张旭博，2021-06-21 09:24:12，使用说明 - 按钮
function instructionBtn() {
    bounce_Fixed.show($("#instruction"))
}

// creator: 张旭博，2020-01-06 14:01:52，清空输入框
function clearEditor() {
    atUsers = []
    uEditor.setContent("")
    $(".replyAt_con").html('')
    $(".replyAt").hide()
    $(".messageInput").find('.fileShowList').html("")
}

// creator: 张旭博, 2020-07-30 10:26:20, 更新未读按钮状态
function updateUnreadState() {
    var unReadMsg = $(".unReadMsg").data("unReadMsg")
    if (Number(unReadMsg) < $(".chat_main .msgItem").length || Number(unReadMsg) === $(".chat_main .msgItem").length) {
        $(".unReadMsg").hide()
    }
}

// creator: 张旭博，2019-10-29 16:04:48，获取所有参与人列表（已经选择过的参与人）
function getAllParticipants(type, isLoading) {
    var data = {
        type: type, // 传1，2，3这三种状态 1代表获取当前讨论的参与人 2代表获取当前机构还不是参与人的人，用于新增参与人时使用，3代表获取除登录人外其它的参与人，用于新增@人员时选人，去除了自己做到不允许@自己
        userID: sphdSocket.user.userID
    }
    if (type !== 4 && type !== 5) {
        data.postId = $(".mainChat").data("theme").id // 讨论的id
    }
    var pm_participant = new Promise((resolve, reject) => {
        $.ajax({
            url: "getAllParticipants.do",
            data: data,
            beforeSend: function () {
              if (isLoading) {
                  loading.open()
              }
            },
            success: function (res) {
                console.log(res)
                var roleList = res.listPostUser
                // 1: 'rightRole', 'query', 'seeCompere', 'changeCompere'
                // 2: 'continueAdd'
                // 3: 'at'
                // 5: '1v1', '1vn'
                if (roleList) {
                    resolve(roleList)
                } else {
                    reject('没有获取到人员列表')
                }
            },
            complete: function (){
                if (isLoading) {
                    loading.close()
                }
            }
        })
    })
    return pm_participant

}

// creator: 张旭博，2019-11-18 11:40:58，获取讨论主题的所有回复（包括文件和图片角标）(type:0 获取所有回复 type:其他 获取更多回复)
var xhr00 = null
function getPostMessage(type, num) {
    var param = {
        id : $(".mainChat").data("theme").id, // 讨论的id
        userID: sphdSocket.user.userID
    }
    var url = ''

    if (type === 0) {
        url = 'getAllForumPostMessage.do'
    } else {
        url = 'forumPostLoadingMes.do'
        param.replyId = $(".chat_main .msgItem").first().data("id") // 最上面的消息
        param.type = type // 1是点击加载更多，2是定位原文，3是X条未读消息
        if (type === 2) {
            param.parentReplyId = num // 点击定位原文传父id
        }
        if (type === 3) {
            param.unreadNum = num // 点击M条消息时传的未读数
        }
    }
    if (xhr00) xhr00.abort()
    xhr00 = $.ajax({
        url: url,
        data: param,
        success: function (res) {
            var data = res.data
            // var forumPost   = data.forumPost; // 讨论主题的信息
            var unreadNum  = data.unreadNum; // 未读跟帖的数目
            var replyAttPictureNum  = data.replyAttPictureNum; // 附件中图片的数目
            var replyAttFileNum  = data.replyAttfileNum; // 附件中文件的数目
            var allComments = data.listReplyPage;  // 跟帖的信息


            var cmtStr = ''
            if (type === 0) { // 处理回复区的所有回复信息
                $(".history_avatar li[to='imageList'] .corner-word").html(replyAttPictureNum > 0?'('+replyAttPictureNum+')':'')
                $(".history_avatar li[to='fileList'] .corner-word").html(replyAttFileNum > 0?'('+replyAttFileNum+')':'')
            }

            // 处理评论
            if (allComments) {
                cmtStr = updateMsg(allComments, type)
            }

            if (type === 0 || type === 1) {
                // 判断是否显示查看更多信息
                if (allComments.length === 20) {
                    $(".seeMore").show()
                } else {
                    $(".seeMore").hide()
                }
            }

            // 处理滚动条
            if (type === 0) {
                // 正常获取讨论，页面滚到最底部
                $(".chat_avatar .chat_main").html(cmtStr);
                $('.chat_avatar').scrollTop($(".chat_avatar").prop("scrollHeight"));
            } else if (type === 1) {
                // 查看更多消息，页面滚至看到的位置
                var prevHeight = $(".chat_avatar").prop("scrollHeight")
                $(".chat_avatar .chat_main").prepend(cmtStr);
                var nextHeight = $(".chat_avatar").prop("scrollHeight")
                $('.chat_avatar').scrollTop(nextHeight - prevHeight);
            }else if (type === 2) {
                // 查看更多消息，页面滚至看到的位置
                $(".chat_avatar .chat_main").prepend(cmtStr);
                $('.chat_avatar').scrollTop(0);
                $(".chat_main .msgItem").first().addClass("choosen")
                setTimeout(function () {
                    $(".chat_main .msgItem").first().removeClass("choosen")
                }, 3000 )
            } else if (type === 3) {
                // 查看未读消息
                $(".chat_avatar .chat_main").prepend(cmtStr);
                $('.chat_avatar').scrollTop(0);
            }

            if (type === 0) {
                // 处理未读消息
                $(".unReadMsg .num").html(unreadNum + '条消息未读')
                $(".unReadMsg").data("unReadMsg", unreadNum)
                if (unreadNum > 20) {
                    $(".unReadMsg").show()
                } else {
                    $(".unReadMsg").hide()
                }
            } else {
                updateUnreadState()
            }
        },
        error: function () {}
    });
}

// creator: 张旭博，2020-09-23 14:55:59，获取讨论详情（查看详情）
function getThemeDetail() {
    var id = $(".mainChat").data("theme").id
    $.ajax({
        url: 'getForumPostMessage.do',
        data: {
            id: id
        },
        success: function (res) {
            var data = res.data
            var forumPost   = data.forumPost; // 讨论主题的信息

            var compere = $(".mainChat").data("theme").compere
            var category = $(".mainChat").data("theme").category // 讨论类型（一对一、多对多）

            // 处理讨论主题的详情信息
            var allFile    = data.forumPostAttachmentHashSer || [];  //讨论的附件

            var fileStr = ''; // 附件列表
            // 处理附件
            if (allFile.length > 0) {
                fileStr = updateFile(allFile, 'seeDetail')
            }
            // 处理参与人
            if ($("#backToMainBtn").is(":visible") || category === 1 || compere !== sphdSocket.user.userID) {
                $(".continueAdd").hide()
            } else {
                $(".continueAdd").show()
            }
            getAllParticipants(1, true).then(res  => {
                updateRoleList('seeCompere', res)
            })
            if (compere === sphdSocket.user.userID && !$("#backToMainBtn").is(":visible")) {
                $("#discussDetailChangeBtn").show()
            } else {
                $("#discussDetailChangeBtn").hide()
            }

            $("#seeDetail .see_title").html(forumPost.title)
            $("#seeDetail .see_compere").html(forumPost.compereName)
            $("#seeDetail .see_des").html(forumPost.content)
            $("#seeDetail .see_processList").html('<div>发起：<span class="create">'+forumPost.createName + ' ' +formatTime(forumPost.createDate, true)+'</span></div><div>审批：' + forumPost.auditorName + ' ' +formatTime(forumPost.auditDate, true)+'</div>')
            $("#seeDetail .see_fileList").html(fileStr)
            $("#seeDetail").data('forumPost', forumPost)
        }
    })
}

// creator: 张旭博，2020-09-24 14:20:01，修改讨论组信息 - 确定
function sureChangeDiscussDetail() {
    var postId = $(".mainChat").data("theme").id

    var title = $("#discussDetailChange [name='title']").val();
    var content = $("#discussDetailChange [name='content']").val();
    var compere = $("#discussDetailChange [name='compere']").val();
    var compereName = $("#discussDetailChange [name='compere'] option:selected").attr('name');

    var data = {}
    var newForumPost = {
        title: title,
        content: content,
        compere: Number(compere)
    }
    var oldForumPost = $("#discussDetailChange").data("forumPost")
    for (var key in oldForumPost) {
        if (oldForumPost[key] !== newForumPost[key]) {
            data[key] = newForumPost[key]
            if (key === 'compere') {
                data.compereName = compereName
            }
        }
    }
    if ($.isEmptyObject(data)) {
        layer.msg("请修改内容后提交")
        return false
    }

    data.postId = postId
    data.userID = sphdSocket.user.userID
    $.ajax({
        url: 'updateForumPost.do',
        data: data,
        success: function (res) {
            bounce.cancel()
            bounce_Fixed.cancel()
            var success = res.success
            if (success === 1) {
                layer.msg("修改成功！")
                $(".mainChat").data("theme", {
                    id: postId,
                    category: 2, // 讨论类型（一对一、多对多）
                    compere: Number(compere)    // 赋值主持人，后面参与人会用到
                })
                $(".history_avatar .historyTab li.active").click()
            } else {
                layer.msg("修改失败！")
            }
        }
    })
}

// creator: 张旭博，2020-09-24 14:21:48，获取讨论组详情-修改记录
function getDiscussDetailChangeHistory() {
    var postId = $(".mainChat").data("theme").id
    $.ajax({
        url: 'getForumPostHis.do',
        data: {
            postId: postId
        },
        success: function (res) {
            var data = res.data
            var listForumPostHis = data.listForumPostHis
            var str = ''
            for (var i=0; i<listForumPostHis.length; i++) {
                if (i === 0) {
                    str += '<tr>' +
                        '<td>原始信息</td>' +
                        '<td><span class="ty-color-blue" type="btn" data-name="seeHistoryDetail">查看</span></td>' +
                        '<td>'+listForumPostHis[i].createName + ' ' + formatTime(listForumPostHis[i].createDate, true) +'</td>' +
                        '<td class="hd">'+JSON.stringify(listForumPostHis[i]) +'</td>' +
                        '</tr>'
                } else {
                    str += '<tr>' +
                        '<td>第'+i+'次修改后</td>' +
                        '<td><span class="ty-color-blue" type="btn" data-name="seeHistoryDetail">查看</span></td>' +
                        '<td>'+listForumPostHis[i].updateName + ' ' + formatTime(listForumPostHis[i].updateDate, true) +'</td>' +
                        '<td class="hd">'+JSON.stringify(listForumPostHis[i]) +'</td>' +
                        '</tr>'
                }

            }

            var n = listForumPostHis.length-1
            if (n > 0) {
                $("#discussDetailChangeHistory .changeTip").html('当前资料为第'+n+'次修改后的结果。<div class="text-right" style="flex: 1;">修改人：'+listForumPostHis[n].updateName + ' ' + formatTime(listForumPostHis[n].updateDate, true)+'</div>')
                $("#discussDetailChangeHistory table").show()
            } else {
                var createInfo  = $("#seeDetail .see_processList .create").html()
                $("#discussDetailChangeHistory .changeTip").html('当前资料尚未经修改。<div class="text-right" style="flex: 1;">发起人：'+createInfo+'</div>')
                $("#discussDetailChangeHistory table").hide()
            }

            $("#discussDetailChangeHistory tbody").html(str)
        }
    })
}

// creator: 张旭博，2019-10-29 16:05:38，获取所有附件
function getAllFile(fileType, arrowType) {
    $(".history_avatar ."+fileType+"List").html("")
    $.ajax({
        url: "findSubsidiaryFile.do",
        data: {
            postId: $(".mainChat").data("theme").id, // 讨论的id
            type: arrowType,
            attType: (fileType === 'image'?1:3)
        },
        success: function (res) {
            console.log(res)
            var allFile = res.listReplyAtt || []
            var fileStr = ''
            if (allFile.length > 0) {
                fileStr = updateFile(allFile)
                $(".history_avatar ."+fileType+"List").html(fileStr)
                $(".history_avatar [to='"+fileType+"List'] .corner-word").html('(' + (allFile.length) + ')')
            } else {
                $(".history_avatar [to='"+fileType+"List'] .corner-word").html('')
            }
        }
    })
}

// creator: 张旭博，2019-10-29 16:24:03，获取讨论主题的历史所有回复外加各种查询条件
function getAllReply(param) {
    // var param = {
    //     postId: $(".mainChat").data("theme").id, // 讨论id
    //     type: 1,   // 5中状态1是全部信息，2是近7日，3是近1个月，4是近3个月，5是近1年
    //     time: '',   // 查询全部信息时可以点开日历去查询某天的，这时要把时间传过来，当有此值时就不能传findUser和mesType，同理有这两个值时就不能传time
    //     findUser: '',   // 查找某人的信息时要把这个人id传过来
    //     mesType: '',    // 查找信息的类型1是全部的信息2是@我的信息3是回复我的信息
    //     currentPageNo:1,
    //     pageSize:20
    // }
    param.userID = sphdSocket.user.userID
    $.ajax({
        url: "getAllReply.do",
        data: param,
        success: function (res) {
            var allReply = res.listReply
            var pageInfo = res.pageInfo
            var str = ''
            if (allReply) {
                for (var i = allReply.length - 1; i >= 0; i--) {
                    // 拼接回复
                    var replyStr = ''
                    if (allReply[i].parent) {
                        replyStr =  '<div class="msgReply">' +
                                    '    <span>'+allReply[i].parentContent+'</span>' +
                                    '</div>'
                    }
                    // 拼接附件信息
                    var fileStr = ''
                    var allFile = allReply[i].forumReplyAttachmentHashSet || []
                    if (allFile.length > 0) {
                        allFile[0].replyId = allReply[i].id
                        allFile[0].isUploaded = allReply[i].isUploaded
                        fileStr = updateFile(allFile)
                    }
                    // 因为回调和接口返回的时间格式不一样，在此需要统一格式
                    var createDate = allReply[i].createDate
                    var dateObj = new Date(createDate)
                    createDate = formatTime(dateObj, true)

                    str +=  '<li class="replyItem">' +
                            '    <div class="'+(allReply[i].creator === sphdSocket.user.userID? 'ty-color-green':'ty-color-blue')+'">' + allReply[i].createName + ' ' + createDate+ '</div>' +
                            replyStr+
                            '    <div class="replyContent">' + fileStr + allReply[i].content + '</div>' +
                            '</li>'
                }
            }
            var isQuery = param.isQuery
            if (isQuery) {
                $(".queryAlert").show()
                $(".queryAlert .queryNum").html(allReply.length)
            } else {
                var currentPageNo = pageInfo.currentPageNo
                var totalPage = pageInfo.totalPage
                $(".pageBtnGroup i").removeClass('disabled')
                if (totalPage === 1){
                    $(".pageBtnGroup i").addClass('disabled')
                } else {
                    $(".pageBtnGroup i").removeClass('disabled')
                    if (currentPageNo === 1) {
                        $(".pageBtnGroup i").eq(2).addClass('disabled')
                    } else if (currentPageNo === totalPage) {
                        $(".pageBtnGroup i").eq(1).addClass('disabled')
                    }
                }
                $("#calendar").data('pageInfo', pageInfo)
            }
            $(".history_avatar .allMessage ul").html(str)
        }
    })
    
}

// creator: 张旭博，2019-12-26 16:34:55，初始化全部消息
function initAllMessage() {
    var data = {
        postId: $(".mainChat").data("theme").id, // 讨论id
        type: 1,   // 5中状态1是全部信息，2是近7日，3是近1个月，4是近3个月，5是近1年
        time: '',   // 查询全部信息时可以点开日历去查询某天的，这时要把时间传过来，当有此值时就不能传findUser和mesType，同理有这两个值时就不能传time
        findUser: '',   // 查找某人的信息时要把这个人id传过来
        mesType: '',    // 查找信息的类型1是全部的信息2是@我的信息3是回复我的信息
        currentPageNo:1,
        pageSize:20,
        isQuery: false
    }
    getAllReply(data)
    $(".queryAlert").hide()
    $(".dateChoose").show().siblings().hide()
    $(".dateChoose input").val("")
}

// creator: 张旭博，2019-07-02 12:07:26，更新参与人员列表
function updateRoleList(name, userList) {
    var str = ''
    switch (name) {
        // 新增讨论 - 一对一讨论参与人列表
        case '1v1':
            str = '<option value="">----请选择----</option>'
            for (var i in userList) {
                if (userList[i].userID !== sphdSocket.user.userID) {
                    str += '<option value="'+userList[i].userID+'" name="'+userList[i].userName+'">'+userList[i].userName+' '+(userList[i].departName||'--') + ' ' + userList[i].mobile+'</option>'
                }
            }
            $("#newDiscussion .roleList").html(str)
            $("#newDiscussion .roleList").data('userList', userList)
            break
        // at列表
        case 'at':
            if (userList && userList.length > 0) {
                for (var i = 0; i < userList.length; i++) {
                    if (userList[i].userID !== sphdSocket.user.userID) {
                        str += "<div userID='" + userList[i]["userID"] + "' class=\"atItem\"><span>" + userList[i]["userName"] + "</span> <span>(" + userList[i]["mobile"] + ")</span></div>";
                    }
                }
            }
            $(".atContainer").html(str);
            $(".atContainer").data('userList', userList)
            break
        // 右侧参与人列表
        case 'rightRole':
            str = ''
            var compere = $(".mainChat").data("theme").compere // 当前主持人
            var category = $(".mainChat").data("theme").category
            for (var i=0; i<userList.length;i++) {
                var isShowDel = ''
                if (compere === sphdSocket.user.userID && userList[i].userID !== compere && category !== 1 && !$("#backToMainBtn").is(":visible")) {
                    isShowDel = '<i class="fa fa-minus-circle delUser" type="btn" data-name="delRole"></i>'
                }
                var image = getAvatarPath(userList[i].gender, 'small')
                str +=  '<li class="roleItem" data-id="'+userList[i].userID+'">' +
                    '   <img class="avatar small" onerror="this.src=\''+image+'\' " src="'+$.fileUrl+userList[i].imgPath+'">' +
                    '   <div class="name">' + userList[i].userName + ( compere === userList[i].userID ? '<i class="manager_s"></i>' : '' ) + '</div>' +
                    '   <div class="phone">' + userList[i].mobile + '</div>' +
                    '   <div class="post">' + (userList[i].departName || '--') + '</div>' +
                    isShowDel +
                    ' </li>'
            }
            $(".history_avatar #roleList ul").html(str)
            $(".history_avatar #roleList ul").data('userList', userList)
            $(".history_avatar [to='roleList'] .corner-word").html(userList.length > 0?'('+userList.length+')':'')
            break
        // 右侧全部消息查询条件
        case 'query':
            str = '<option value="">----请选择----</option>'
            for (var i=0; i<userList.length;i++) {
                str += '<option value="'+userList[i].userID+'">'+userList[i].userName+'</option>'
            }
            $(".history_avatar .queryCondition [name='findUser']").html(str)
            $(".history_avatar .queryCondition [name='findUser']").data('userList', userList)
            break
        // 右侧参与人列表
        // 新增讨论 - 一对多讨论参与人列表
        case '1vn':
        case 'continueAdd':
            str = ''
            if (userList.length > 0) {
                for (var i in userList) {
                    var image = getAvatarPath(userList[i].gender, 'small')
                    str +=  '<div class="selectItem">' +
                            '   <div class="ty-checkbox"><input type="checkbox" id="continueAdd_'+userList[i].userID+'" value="'+userList[i].userID+'" name="'+userList[i].userName+'"><label for="continueAdd_'+userList[i].userID+'"></label></div>' +
                            '   <img class="avatar" onerror="this.src=\''+image+'\' " src="'+$.fileUrl+userList[i].imgPath+'">' +
                            '   <div class="right_info">' +
                            '       <div class="right_info_name"> ' + userList[i].userName + '</div>'+
                            '       <div class="right_info_des">' +
                            '           <div class="right_info_mobile">' + userList[i].mobile + '</div>'+
                            '           <div class="right_info_depart" title="'+userList[i].departName+'">' + userList[i].departName + '</div>'+
                            '       </div>'+
                            '   </div>'+
                            '</div>'
                }
            }
            $("#addRole .roleList").html(str)
            $("#addRole .roleList").data('userList', userList)
            break
        // 修改讨论主持人输入框
        case 'changeCompere':
            str = '<option value="">----请选择----</option>'
            for (var k in userList) {
                str += '<option value="'+userList[k].userID+'" name="'+userList[k].userName+'">'+userList[k].userName+' '+(userList[k].departName||'--') + ' ' + userList[k].mobile+'</option>'
            }
            $("#discussDetailChange .role_list").html(str)
            $("#discussDetailChange .role_list").data('userList', userList)
            break
        // 查看讨论详情参与人部分
        case 'seeCompere':
            var compere = $(".mainChat").data("theme").compere // 当前主持人
            for (var j=0; j<userList.length;j++) {
                var isShowDel = ''
                if (compere === sphdSocket.user.userID && userList[j].userID !== compere && !$("#backToMainBtn").is(":visible")) {
                    isShowDel = '<i class="fa fa-minus-circle delUser" type="btn" data-name="delRole"></i>'
                }
                str +=  '<li class="see_roleList_item" data-id="'+userList[j].userID+'">' + userList[j].userName+ isShowDel + ' </li>'
            }
            if (compere === sphdSocket.user.userID && !$("#backToMainBtn").is(":visible")) {
                str += '<span class="ty-btn ty-btn-green ty-circle-3" type="btn" data-name="addRole">+</span>'
            }
            $("#seeDetail .roleList").html(str)
            $("#seeDetail .roleList").data('userList', userList)
            break
    }
}

// creator: 张旭博，2019-12-17 10:37:39，更新附件信息
function updateFile(allFile, type) {
    // isDetail: 是不是讨论详情（弹窗），是的话只显示导入时版本，其他不显示）
    var fileStr = '<div class="bubble_fileList">'
    for (let j in allFile) {
        let sysOutItem  = allFile[j]
        let sysInItem   = allFile[j].resEntity
        let [size, path, fileType, fileName] = [0, '', '', '']
        let [newestVersion, newestName, newestDate] = [0, '', '非法日期']
        let [importVersion, importName, importDate] = [0, '', '非法日期']
        let [oldVersionStr, newVersionStr, startFileStr, handleStr] = ['', '', '', '']
        let [filePathArr, fileState] = [[], 1] // fileState: 1: 可以换版 2：转发文件不能换版 3：系统内文件不能换版
        let replyId = sysOutItem.replyId
        if (sysInItem) {
            filePathArr = sysInItem.path.split(".")
            size        = formatFileSize(sysInItem.size)
            path        = sysInItem.path
            fileType    = filePathArr [filePathArr.length - 1] || 'other'
            fileName    = sysInItem.name + '.' + sysInItem.version

            importVersion= sysOutItem.importVerson // 导入时版本
            importName  = sysOutItem.resourceImportName // 导入时名称
            importDate  = moment(sysOutItem.resourceImportDate).format("YYYY-MM-DD HH:mm:ss") // 导入时时间

            newestVersion   = sysInItem.changeNum  // 最新版本
            newestName  = sysInItem.updateName // 最新版本上传名称
            newestDate  = moment(sysInItem.updateDate).format("YYYY-MM-DD HH:mm:ss") // 最新版本上传时间

            fileState   = 3

            startFileStr  = '导入时版本G' + importVersion
            oldVersionStr = '导入时版本G' + importVersion + ': <span class="w50">' + importName + '</span> ' + importDate
            newVersionStr = importVersion == newestVersion?'':'<br><span class="ty-color-orange">最新版版本G' + newestVersion + ': <span class="w50">'+ newestName + '</span> ' + newestDate + '</span>'

            if (type==='seeDetail') {
                handleStr = '<a path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                            '<a path="'+ path +'" onclick="getDownLoad($(this))" download="'+ fileName + '">下载</a>'
            } else {
                handleStr = '<a path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                            '<a path="'+ path +'" onclick="getDownLoad($(this))" download="'+ fileName + '">下载</a>' +
                            '<a onclick="bubbleForward($(this))">转发</a>' +
                            '<a onclick="bubbleChangeVersion($(this), '+fileState+')">换版</a>' +
                            '<a onclick="bubbleChangeVersionRecord($(this))">历史版本</a>'
            }

            fileStr +=  '<div class="fileItem" type="sysIn" data-id="'+sysInItem.id+'" data-replyid="'+replyId+'">' +
                        '   <div class="fileInfo_avatar">'+
                        '       <div class="ty-fileType ty-file_'+fileType+'"></div>' +
                        '       <div class="fileInfo">' +
                        '           <div class="fileTitle"><span class="fileName" title="'+fileName+'">' + fileName + '</span> <span class="fileSize">('+size+')</span></div>' +
                        (type==='seeDetail'?'':'           <div class="fileDetail">' +
                        '               <div class="fileDes">' + (type === 'startFile'? startFileStr : (oldVersionStr + newVersionStr))  +'</div>' +
                        '           </div>') +
                        '       </div>'+
                        '   </div>'+
                        '   <div class="fileHandle text-right">' +handleStr + '</div>' +
                        '</div>'
        } else {
            let isUploaded = sysOutItem.isUploaded
            filePathArr = sysOutItem.path.split(".")
            size        = formatFileSize(sysOutItem.size)
            path        = sysOutItem.path
            fileType    = filePathArr[filePathArr.length - 1] || 'other'
            fileName    = sysOutItem.title.slice(0, sysOutItem.title.lastIndexOf(".") + 1) + fileType

            importVersion= sysOutItem.importVerson // 上传时版本
            importName  = sysOutItem.resourceImportName // 上传时名称
            importDate  = moment(allFile[j].resourceImportDate).format("YYYY-MM-DD HH:mm:ss") // 上传时时间

            newestVersion= sysOutItem.resourceVersion // 最新版本
            newestName  = sysOutItem.updateName || sysOutItem.createName || '' // 最新版本上传名称
            newestDate  = moment(sysOutItem.updateTime || sysOutItem.createTime || '').format("YYYY-MM-DD HH:mm:ss") // 最新版本上传时间

            fileState   = isUploaded === 1? 1: 2

            startFileStr  = '上传时版本G' + importVersion
            oldVersionStr = '上传时版本G' + importVersion + ': <span class="w50">' + importName + '</span> ' + importDate
            newVersionStr = newestVersion == importVersion?'':'<br><span class="ty-color-orange">最新版版本G' + newestVersion + ': <span class="w50">'+ newestName + '</span> ' + newestDate + '</span>'

            if (type === 'changeVersionDialog') {
                handleStr = ''
            } else if (type === 'seeDetail') {
                handleStr = ' <a path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                            ' <a path="'+ path +'" onclick="getDownLoad($(this))" download="'+ fileName + '">下载</a>'
            } else {
                handleStr = ' <a path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                    ' <a path="'+ path +'" onclick="getDownLoad($(this))" download="'+ fileName + '">下载</a>' +
                    ' <a onclick="bubbleForward($(this))">转发</a>' +
                    ' <a onclick="bubbleChangeVersion($(this), '+fileState+')">换版</a>' +
                    ' <a onclick="bubbleChangeVersionRecord($(this))">历史版本</a>'
            }
            fileStr +=  '<div class="fileItem" type="sysOut" data-id="'+sysOutItem.id+'" data-replyid="'+replyId+'">' +
                        '   <div class="fileInfo_avatar">'+
                        '       <div class="ty-fileType ty-file_'+fileType+'"></div>' +
                        '       <div class="fileInfo">' +
                        '           <div class="fileTitle"><span class="fileName" title="'+fileName+'">' + fileName + '</span> <span class="fileSize">('+size+')</span></div>' +
                        (type==='seeDetail'?'':'           <div class="fileDetail">' +
                        '               <div class="fileDes">' + (type === 'startFile'? startFileStr : (oldVersionStr + newVersionStr))  + '</div>' +
                        '           </div>') +
                        '       </div>'+
                        '   </div>'+
                        '   <div class="fileHandle text-right">' + handleStr + '</div>' +
                        '</div>'
        }

    }
    fileStr += '</div>'
    return fileStr
}

// creator: 张旭博，2019-12-17 10:09:30，更新消息内容
function updateMsg(allComments, type) {
    var cmtStr = ''
    if (allComments.length === 0 && type === 0) {
        cmtStr = '<div class="text-center">本讨论主题尚无人发布消息</div>'
    } else {
        for (var w = 0; w < allComments.length; w++) {
            // 发起消息的人的信息
            // var postUserInfo = allComments[w].postUserMussage

            // 附件信息
            var allFile = allComments[w].forumReplyAttachmentHashSet || []
            var postUserMessage = allComments[w].postUserMussage
            var imgPath = $.fileUrl + postUserMessage.imgPath || ''

            // 判断发起人是不是当前人
            var isSelf = allComments[w].creator === sphdSocket.user.userID
            var isRevised = allComments[w].isRevised //  换版的系统消息要显示到左侧，同上方一同判断消息显示左侧还是右侧
            var length = $(".chat_main .msgItem").length
            var isReply = !isSelf && (length + w < 60 || type === 4)

            // 拼接附件信息
            var fileStr = ''
            if (allFile.length > 0) {
                allFile[0].replyId = allComments[w].id
                allFile[0].isUploaded = allComments[w].isUploaded
                fileStr = updateFile(allFile)
            }

            var hasFile = allFile.length > 0

            // 判断是否需要添加撤回按钮
            var isCanDel = typeof allComments[w].delStatus === 'undefined' ? isSelf && !hasFile : (allComments[w].delStatus === 1 && isSelf && !hasFile)

            // 因为回调和接口返回的时间格式不一样，在此需要统一格式
            var createDate = allComments[w].createDate
            var dateObj = new Date(createDate)
            createDate = formatTime(dateObj, true)

            // 拼接回复
            var replyStr = ''
            if (allComments[w].parent) {
                replyStr =  '<div class="msgReply"  data-origin="'+allComments[w].parent+'">' +
                            '    <span>'+setNewMemo(allComments[w].parentContent)+'</span>' +
                            '</div>'
            }

            // 拼接主体消息
            var msgType = allComments[w].msgType // 用来判断是否为系统消息 1：非系统消息 其他：系统消息
            if (msgType === '1') {
                var image = getAvatarPath(postUserMessage.gender, 'small')
                cmtStr =    '<div class="msgItem'+(isSelf?' selfMsg':'')+(isRevised === 1?' reviseMsg':'')+'" data-id="'+allComments[w].id+'">' +
                            '   <img class="avatar" onerror="this.src=\''+image+'\' " src="'+imgPath+'">' +
                            '   <div class="msgItemCon">' +
                            '      <div class="userInfo"><span class="userName">'+(isSelf?'我':allComments[w].createName)+'</span><span class="applyTime">'+createDate+'</span></div>' +
                            '      <div class="bubble_avatar">' + replyStr + fileStr + allComments[w].content + '</div>' +
                            (isReply?'<div class="reply" type="btn" data-name="reply">回复</div>':'')+
                            (isCanDel && isRevised !== 1?'<div class="recall" type="btn" data-name="recall">撤回</div>':'')+
                            '   </div>' +
                            '</div>' + cmtStr
            } else if (msgType === '2') {
                cmtStr =    '<div class="msgItem system" data-id="'+allComments[w].id+'">' +
                            '   <div class="msgItemCon">' +
                            '      <div class="userInfo"><span class="applyTime">'+createDate+'</span></div>' +
                            '      <div class="bubble_avatar">' + allComments[w].content + '</div>' +
                            '   </div>' +
                            '</div>' + cmtStr
            } else {
                cmtStr =    '<div class="msgItem system" data-id="'+allComments[w].id+'">' +
                            '   <div class="msgItemCon">' +
                            '      <div class="userInfo"><span class="applyTime">'+createDate+'</span></div>' +
                            '      <div class="bubble_avatar">' + allComments[w].content + '</div>' +
                            '   </div>' +
                            '</div>' + cmtStr
            }
        }
    }
    return cmtStr
}

// creator: 张旭博，2019-07-01 08:50:49，发起讨论
function discussApply() {
    bounce.show($("#newDiscussion"))
    $("#newDiscussion .ty-secondTab li").eq(0).click()
    $('body').everyTime('0.5s','discussApply',function(){
        var state = 0;
        var type = $("#newDiscussion").data("type")

        if (type === 0) {
            if( $("#newDiscussion .roleList").val() === ''){
                $("#discussApplyBtn").prop("disabled",true)
            }else {
                $("#discussApplyBtn").prop("disabled",false)
            }
        } else {
            $(".insertForum [require]").each(function () {
                if($.trim($(this).val()) === ''){
                    state ++
                }
            });
            var isVal = $(".insertForum [name='isNeedApprove']:checked").val()
            if (isVal === '1' && $(".insertForum .discussChooseApprover").val() === '') {
                state++
            }
            if( state > 0 || $(".insertForum .input_show .selected_item").length === 0){
                $("#discussApplyBtn").prop("disabled",true)
            }else {
                $("#discussApplyBtn").prop("disabled",false)
            }
        }
    });

}

// creator: 张旭博，2021-04-09 11:58:27，新增公司的文件与资料 - 打开文件选择弹窗
function openResourceCenter(name, type) {
    switch (name) {
        case 'reply':
        case 'newDiscussion':
            $("#chooseSaveFolder .bounce_title").html("选择内部文件")
            break
        case 'fileUpload':
        case 'fileChangeVersion':
            $("#chooseSaveFolder .bounce_title").html("选择保存位置")
            break
    }
    // type:1-可以选择文件 0-只能选择文件夹
    if (type === 1) {
        $("#chooseSaveFolder").width(1050)
        $("#chooseSaveFolder .mar").show()
    } else {
        $("#chooseSaveFolder").width(600)
        $("#chooseSaveFolder .mar").hide()
    }
    $("#chooseSaveFolder").data("type", type)
    $("#chooseSaveFolder").data("name", name)
    bounce_Fixed2.show($("#chooseSaveFolder"));
    getFirstDoc()
    $("#fileSort").hide()
    $("#chooseSaveFolder .ty-fileList").html("")
    $("#ye_con").html("")
    setEveryTime(bounce_Fixed, 'chooseSaveFolder');
}

// creator: 张旭博，2021-01-21 11:41:24，已停用列表
function disabledDiscuss() {
    $(".app_avatar").data("isDisabledList", true)
    getThemeList(0)
    $("#searchBackToMainBtn").show().siblings().hide()
    $(".textInput_disabled").show().siblings(".textInput").hide()
    $("#theme_search").val("")
}

// creator: 张旭博，2021-01-21 11:46:52，返回主列表
function backToMain() {
    $(".app_avatar").data("isDisabledList", false)
    $("#searchBackToMainBtn").hide().siblings().show()
    getThemeList()
    $(".textInput_disabled").hide().siblings(".textInput").show()
}

// --------------- 归档 ------------------

// creator: 张旭博，2021-02-08 09:28:41，附件归档 - 按钮
function fileFileBtn() {
    bounce.show($("#fileFile"))
    $("#fileFile").find("input:radio:checked").prop("checked", false)
    setEveryTime(bounce, 'fileFile')
    getFileList(1,20)
}

// creator: 张旭博，2021-02-20 08:37:27，附件归档 - 确定
function sureFileFile() {
    var chooseFileSelector = $("#fileFile .fileItemRadioChoose.active").find(".fileItem")
    var fileInfo = $("#fileFile .fileItemRadioChoose.active").children(".hd").html()
    if (fileInfo) {fileInfo = JSON.parse(fileInfo)}
    $("#fileUpload .upload_avatar").html(chooseFileSelector.clone(true))

    $("#fileUpload").find("input.ty-inputText").val("")
    $("#needOther").prop("checked", true)
    $("#fileUpload .savePlace").hide().html("")
    $("#fileUpload .inputPart").show().siblings(".seePart").hide()

    $("#fileUpload").data("fileInfo", fileInfo)
    getApprover()
    setEveryTime(bounce, 'fileUpload')

    var fileFor = Number($("#fileFile input[name='fileFor']:checked").val())

    if (fileFor === 1) {
        $("#fileUpload").data("name", 'fileUpload')
        if(isGeneral){
            $("#fileUpload .bounce_title").html("上传文件")
        } else {
            $("#fileUpload .bounce_title").html("文件发布申请")
        }
        var fileName = chooseFileSelector.find(".fileName").html()
        $("#fileUpload input[name='fileNo']").val(fileName).prop("disabled", false);
        $("#fileUpload input[name='name']").val(fileName).prop("disabled", false);
        $("#sureUploadNewFileBtn").data("name", "sureUploadNewFile")
    } else {
        $("#fileUpload").data("name", 'fileChangeVersion')
        if(isGeneral){
            $("#fileUpload .bounce_title").html("文件换版")
        } else {
            $("#fileUpload .bounce_title").html("文件换版申请")
        }
        $("#fileUpload").prop("disabled", true)
        $("#fileUpload input[name='fileNo']").val('').prop("disabled", true);
        $("#fileUpload input[name='name']").val('').prop("disabled", true);
        $("#sureUploadNewFileBtn").data("name", "sureChangeVersion")
    }
    $("#fileUpload").data("fileInfo", JSON.parse($("#fileFile .fileItemRadioChoose.active").find(".hd").html()))

    bounce_Fixed.show($("#fileUpload"))

}

// creator: 张旭博，2021-02-08 17:05:44，附件归档获取图片或者非图片
function getFileList(currentPageNo,pageSize,attType) {
    var postId = $(".mainChat").data("theme").id;
    var data = {
        postId: postId,  // 讨论的id
        currentPageNo: currentPageNo,  // 分页条数和页数
        pageSize: pageSize  // 分页条数和页数
    }
    if (attType) {
        data.attType = attType  // 附件类型 1是文件2是图片
    }
    $.ajax({
        url: '../forum/getSmallArchiveFile.do',
        data: data,
        success: function (res) {
            var data = res.data
            var fileList = data.listReplyAtt
            var pageInfo = data.pageInfo

            //设置分页信息
            var currentPageNo = pageInfo["currentPageNo"],
                totalPage = pageInfo["totalPage"],
                jsonStr = JSON.stringify({attType: attType});

            setPage($("#ye_archiveFile"), currentPageNo, totalPage, "archiveFile", jsonStr);
            var str = ''
            if (fileList) {
                for (var i in fileList) {
                    var imgPath = fileList[i].path
                    var size = formatFileSize(fileList[i].size)
                    var filePathArr = fileList[i].path.split(".")
                    var fileType = filePathArr[filePathArr.length - 1] || 'other'
                    var newestName  = fileList[i].updateName || fileList[i].createName || ''
                    var newestDate  = moment(fileList[i].updateTime || fileList[i].createTime || '').format("YYYY-MM-DD HH:mm:ss")
                    fileList[i].version = fileType
                    var image = getAvatarPath(fileList[i].gender, 'small')
                    str +=  '<div class="fileRow fileItemRadioChoose">' +
                            '    <div class="fileRow_file">' +
                            '        <div class="msgItem selfMsg">' +
                            '            <img class="avatar" onerror="this.src=\''+image+'\' " src="'+$.fileUrl + fileList[i].imgPath+'">' +
                            '            <div class="msgItemCon">' +
                            '                <div class="userInfo">' +
                            '                    <span class="userName">'+newestName+'</span>' +
                            '                    <span class="applyTime">'+newestDate+'</span>' +
                            '                </div>' +
                            '                <div class="fileItem">' +
                            '                    <div class="fileInfo_avatar">' +
                            '                       <div class="ty-fileType ty-file_'+fileType+'"></div>' +
                            '                       <div class="fileInfo">' +
                            '                           <div class="fileTitle">' +
                            '                               <span class="fileName" title="'+fileList[i].title+'">'+fileList[i].title+'</span>' +
                            '                               <span class="fileSize">'+size+'</span>' +
                            '                           </div>' +
                            '                           <div class="fileDetail">' +
                            '                               <div class="fileDes">'+newestName + ' ' + newestDate +'</div>' +
                            '                           </div>' +
                            '                       </div>' +
                            '                    </div>' +
                            '                    <div class="fileHandle text-right">' +
                            '                        <a path="'+imgPath+'" onclick="seeOnline($(this))">预览</a>' +
                            '                        <a path="'+imgPath+'" onclick="getDownLoad($(this))" download="'+fileList[i].title+'">下载</a>'+
                            '                    </div>' +
                            '                </div>' +
                            '            </div>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="fileRow_radio">' +
                            '        <div class="ty-radio">' +
                            '            <input type="radio" id="file_choose_'+fileList[i].id+'" name="file_choose">' +
                            '            <label for="file_choose_'+fileList[i].id+'"></label>' +
                            '        </div>' +
                            '    </div>' +
                            '   <div class="hd">'+JSON.stringify(fileList[i])+'</div>'+
                            '</div>'
                }
                $("#fileFile .fileShow_avatar").html(str)
            }
        }
    })
}

// creator: 张旭博，2021-02-08 09:28:56，阅览设置 - 按钮
function readSettingBtn() {
    bounce.show($("#tip"))
    $('.bounce').everyTime('0.5s','readSetting',function(){
        var state = 0;
        if ($("#tip input[name='open']").is(":checked")) {
            $("#sureDiscussDetailChangeBtn").prop("disabled",false)
        }else {
            $("#sureDiscussDetailChangeBtn").prop("disabled",true)
        }
    });
    var id = $(".mainChat").data("theme").id
    $.ajax({
        url: 'getForumPostMessage.do',
        data: {
            id: id
        },
        success: function (res) {
            var data = res.data
            var forumPost   = data.forumPost; // 讨论主题的信息

            var html = ''
            var isOpen = forumPost.isOpen
            if (isOpen === 1) {
                html =  '处于“开放”状态的讨论组将显示于阅览室目录中。<br/>' +
                    '本讨论组目前处于“开放”状态。<br/>' +
                    '<div class="ty-radio">' +
                    '   <input type="radio" id="openDiscuss" name="open" value="1">' +
                    '   <label for="openDiscuss"></label>' +
                    '   <b>将本讨论组改为“未开放”状态！</b>' +
                    '</div> '
            } else {
                html =  '处于“开放”状态的讨论组将显示于阅览室目录中。<br/>' +
                    '本讨论组目前处于“未开放”状态。<br/>' +
                    '<div class="ty-radio">' +
                    '   <input type="radio" id="openDiscuss" name="open" value="0">' +
                    '   <label for="openDiscuss"></label>' +
                    '   <b>将本讨论组改为“开放”状态！</b>' +
                    '</div> '
            }
            $("#tip .text").html(html)
            $("#tip .sureBtn").unbind().on("click", function () {
                if ($("#tip input[name='open']").is(":checked")) {
                    var isOpen = $("#tip input[name='open']:checked").val() === '1' ? 0 : 1
                    var postId = $(".mainChat").data("theme").id;
                    $.ajax({
                        url: '../forum/openForumPost.do' ,
                        data: {
                            postId: postId, // 讨论id
                            isOpen: isOpen // 1是开放 0是不开放 传值时看下当前的状态当前讨论组是不开发状态时传1，开放状态时传0，默认是不开发状态
                        },
                        success: function (res) {
                            bounce.cancel()
                            var success = res.success
                            if (success === 1) {
                                layer.msg("操作成功！")
                            } else {
                                layer.msg("操作失败！")
                            }
                        }
                    });
                } else {
                    layer.msg("请勾选对应状态！")
                }
            })
        }
    })
}

// creator: 张旭博，2021-02-08 09:29:07，删除讨论 - 按钮
function delDiscussBtn() {

    bounce.show($("#fixed_confirm"))
    bounce.show($("#tip"))
    $("#tip .text").html('确定“删除”这个讨论组吗？')
    $("#tip .sureBtn").unbind().on("click", function () {
        var postId = $(".mainChat").data("theme").id;
        $.ajax({
            url: '../forum/delForumPost.do' ,
            data: {
                postId: postId, // Integer 要删除的讨论id
                userID: sphdSocket.user.userID // Integer 登录人id
            },
            success: function (res) {
                bounce.cancel()
                var data = res.data
                if (data) {
                    var status = data.state
                    var forumPost = data.forumPost
                    if (status === 1) {
                        layer.msg("讨论组已“删除”！之后3天，在“将被清除的讨论组”中您还能见到该讨论组！")
                    } else if (status === 2) {
                        $("#fixed_confirm .text").html('<p>删除失败！</p><p>讨论组停用5天后才可删除！</p>')
                        $("#fixed_confirm .iknowBtn").unbind().on("click", function () {
                            bounce_Fixed.cancel()
                        })
                        bounce_Fixed.show($("#fixed_confirm"))
                    } else if (status === 3) {
                        $("#fixed_confirm .text").html('<p>操作重复！本讨论组已于'+formatTime(forumPost.validTime, false)+'被'+forumPost.compereName+'进行了删除操作。</p>')
                        $("#fixed_confirm .iknowBtn").unbind().on("click", function () {
                            bounce_Fixed.cancel()
                        })
                        bounce_Fixed.show($("#fixed_confirm"))
                    } else {
                        layer.msg("操作失败！")
                    }
                } else {
                    layer.msg("操作失败！")
                }
            }
        });
    })
}

// -------------------讨论气泡 - 操作 ------------------------
// creator: 张旭博，2022-11-14 01:32:22， 气泡 - 转发
function bubbleForward(selector) {
    bounce.show($("#bubbleForward"))
    var msgId = selector.parents(".fileItem").data("replyid")
    $("#bubbleForward").data('msgId', msgId)
    $("#bubbleForward").find("input.forwardContent").val("")
    getPostsList().then((res) => {
        // 先清空所有主题
        $("#bubbleForward .themeList_avatar").html("")
        var listForumPost = res.listForumPost; // 主题列表 0

        // 是否查询到主题
        listForumPost.map(item => item.checked = false)
        if (listForumPost.length === 15) {
            var refresh =  ' <div class="refresh" type="btn" data-name="reloadMore" data-type="forwardTo">' +
                '    <i class="fa fa-refresh"></i>' +
                '</div>'
            $("#bubbleForward .themeList_avatar[data-name='forwardFrom']").html(refresh)
        } else {
            $("#bubbleForward .themeList_avatar[data-name='forwardFrom']").html('')
        }

        renderThemeList(listForumPost, [])
        $("#bubbleForward").data("leftList", listForumPost)
        $("#bubbleForward").data("rightList", [])
    })
}

// creator: 张旭博，2022-11-15 04:31:44， 转发 - 确定
function sureBubbleForward() {
    var msgId = $("#bubbleForward").data('msgId') // 消息id
    var content = $("#bubbleForward input.forwardContent").val()
    var forwardToList = $("#bubbleForward").data("rightList")
    var postIds = forwardToList.map(item => item.id)
    if (postIds.length === 0) {
        layer.msg("请选择转发的主题！")
        return false
    }
    $.ajax({
        url: '/forum/forwardingAttAndReply.do',
        data: {
            postIds: JSON.stringify(postIds), // 转发主题id拼接的字符串
            content: content, // 转发时带的消息
            replyIdByAtt: msgId, // json字符串里面的内容和发消息的时一样的
            listUser: JSON.stringify([]) // 同样和发消息时一样传值就行
        },
        success: function (res) {
            var data = res.data
            var state = data.state
            if (state === 1) {
                layer.msg("转发成功")
                bounce.cancel()
            } else {
                layer.msg("转发失败")
            }
        }
    })
}

// creator: 张旭博，2022-11-16 08:56:30， 气泡 - 换版
function bubbleChangeVersion(selector, fileState) {
    if (fileState === 1) {
        var replyId = selector.parents(".fileItem").data("replyid")
        $("#changeVersion").data('replyId', replyId)
        var fileHandle = selector.parents(".fileItem").find(".fileHandle")

        $(".fileUpload_progress").remove()
        var html = '<div class="fileUpload_progress"><div class="uploadify-progress-bar"></div></div>'
        fileHandle.before(html)
        $("#changeVersion .fileUpload .uploadify-button").click()
        var nowFileName = selector.parents(".fileItem").find(".fileName").attr("title")
        var nowFileArr = nowFileName.split(".")
        var nowFileType = nowFileArr[nowFileArr.length - 1]
        nowFileType = nowFileType.toLowerCase()
        $("#changeVersion").data("nowFileType", nowFileType)
        selector.siblings(".cvState").remove()
    } else if (fileState === 2) {
        layer.msg("这个文件不可以由您来换版")
    } else {
        layer.msg("请您在文件与资料内换版本文件")
    }
}

// creator: 张旭博，2022-11-16 08:56:30， 气泡 - 换版记录
function bubbleChangeVersionRecord(selector) {
    var fileId = selector.parents(".fileItem").data("id")
    var replyId = selector.parents(".fileItem").data("replyid")
    $("#bubbleChangeVersionRecord").data('fileId', fileId)
    $("#bubbleChangeVersionRecord").data('replyId', replyId)
    bounce.show($("#bubbleChangeVersionRecord"))
    var sys = selector.parents(".fileItem").attr("type")
    if (sys === 'sysIn') {
        getBubbleChangeVersionRecordSysIn(1, 20)
    } else {
        getBubbleChangeVersionRecordSysOut(1, 20)
    }
}

// creator: 张旭博，2022-11-16 03:15:25， 获取换版记录 - 内部文件
function getBubbleChangeVersionRecordSysIn(currentPageNo, pageSize) {
    var fileId = $("#bubbleChangeVersionRecord").data('fileId')
    var data = {
        fileId: fileId,
        currentPageNo: currentPageNo,
        pageSize: pageSize
    }
    $.ajax({
        url: "../res/getUpdateFile.do",
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"]

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo);
            setPage( $("#ye_bubbleHistoryRecord"), currentPageNo, totalPage, "bubbleInHistoryRecord") ;
            $("#bubbleChangeVersionRecord .fileList").html(fileListStr)
        }
    })
}

// creator: 张旭博，2022-11-16 03:15:25， 获取换版记录 - 外部文件
function getBubbleChangeVersionRecordSysOut(currentPageNo, pageSize) {
    var replyId = $("#bubbleChangeVersionRecord").data('replyId')
    var data = {
        replyId: replyId,
        currentPageNo: currentPageNo,
        pageSize: pageSize
    }
    $.ajax({
        url: "../forum/getForumReplyAttHis.do",
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"]

            //设置文件信息
            var fileListStr = getOutFileListStr(fileInfo);
            setPage( $("#ye_bubbleHistoryRecord"), currentPageNo, totalPage, "bubbleOutHistoryRecord") ;
            $("#bubbleChangeVersionRecord .fileList").html(fileListStr)
        }
    })
}

// ------------------------ 从文件与资料中引入 ---------------------//
// creator: 侯杏哲 2017-12-04  获取一级文件夹列表
function getFirstDoc(){
    var url = "../res/getInitialFolder.do" ;
    if( isGeneral || isSuper){ // 超管或总务
        url = "../res/getInitialFolderByManage.do" ;
    }
    $.ajax({
        url: url ,
        data: { "type": 1  },
        success: function (res) {
            var data = res["data"] ,
                listFirstCategory = data["listFirstFolder"],
                authFolder = data["authFolder"],    //  0是没有权限 1是有权限
                firstLevelStr = "";
            firstLevelStr += '<div class="level1" level="1">';
            if(listFirstCategory.length === 0){
                $("#fileUploadBtn").attr("class","ty-btn ty-btn-big ty-btn-gray ty-circle-3");
                $(".ty-colFileTree[data-name='main']").html('<div class="null"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无数据</p></div>');
                return false
            }
            for(var i in listFirstCategory){
                if(Number(i) === listFirstCategory.length - 1 && (isGeneral || isSuper)){
                    //最后一个值为回收站，有特殊图标(讨论区此处不能选择，所以不展示）
                    firstLevelStr += ''
                }else{
                    if(listFirstCategory[i]["childStatus"] === "1"){ //  1 - 下有子文件夹，没有值代表没有子文件夹
                        firstLevelStr += '<li><div class="ty-treeItem" title="'+ listFirstCategory[i]["name"] +'" data-id='+listFirstCategory[i]["id"]+'>' +
                            '<i class="fa fa-angle-right"></i>' +
                            '<i class="fa fa-folder"></i>' +
                            '<span>'+listFirstCategory[i]["name"]+'</span></div></li>'
                    }else{
                        firstLevelStr += '<li><div class="ty-treeItem" title="'+ listFirstCategory[i]["name"] +'" data-id='+listFirstCategory[i]["id"]+'>' +
                            '<i class="ty-fa"></i>' +
                            '<i class="fa fa-folder"></i>' +
                            '<span>'+ listFirstCategory[i]["name"] +'</span></div></li>'
                    }
                }
            }
            firstLevelStr += '<p id="tstip"></p>'+
                '</div>';
            $(".ty-colFileTree[data-name='main']").html(firstLevelStr);
        }
    });
}

// creator: 张旭博, 2020-04-27 08:58:11, 归档 - 文件发布申请 - 确定
function sureUploadNewFile(){
    //获取表单内容和接口需要的参数
    var fileInfo = $("#fileUpload").data("fileInfo");

    //参数json
    var data = {
        userID: sphdSocket.user.userID,
        category: $("#fileUpload .savePlace").data("categoryId"),
        path: fileInfo.path,
        size: fileInfo.size,
        version: fileInfo.version,
        changeNum: 0,
        isNeedOther: false,
        module: '文件与资料'
    }
    var files = [{
        name: $("#fileUpload [name='name']").val(),
        fileSn: $("#fileUpload [name='fileNo']").val(),
        path: fileInfo.path,
        size: fileInfo.size,
        version: fileInfo.version,
    }]
    data.files = JSON.stringify(files)
    data.isNeedOther = $("#fileUpload input:radio:checked").val()
    if (data.isNeedOther === '1') {
        // 选择了审批人
        data.type = 1
        data.auditName = $(".chooseApprover").find("option:selected").html()
        data.auditor = $(".chooseApprover").val()
    } else {
        // 由文管直接发布
        if (isGeneral) {
            data.type = 2
        } else {
            data.type = 1
        }
    }
    console.log(data)
    $.ajax({
        url: '../res/resCentrenAffirdFile.do',
        data: data,
        success: function (res) {
            var state = parseInt(res.data)
            if (state === 1) {
                layer.msg("操作成功")
                bounce_Fixed.cancel();
                bounce.cancel();
            } else if (state === 2) {
                layer.msg("文件编号重复，请重新填写文件编号!")
            } else if (state === 3) {
                layer.msg("文件夹不存在!")
            } else {
                $("#mtTip #mt_tip_ms").html("操作失败!") ;
            }
        }
    })
}

// creator：张旭博，2017-05-06 15:11:35，换版弹窗点击确定按钮
function sureChangeVersion(){
    //获取表单内容和接口需要的参数
    var fileInfo = $("#fileUpload").data("fileInfo");

    //参数json
    var data = {
        userID: sphdSocket.user.userID,
        file: $("#chooseSaveFolder .fileItemRadioChoose.active").data("id"),
        category: $("#fileUpload .savePlace").data("categoryId"),
        name:$("#fileUpload input[name='name']").val(),
        fileSn:$("#fileUpload input[name='fileNo']").val(),
        content:$("#fileUpload input[name='content']:visible").val(),
        path: fileInfo.path,
        size: fileInfo.size,
        version: fileInfo.version,
        module: '文件与资料',
        changeNum: $.trim($(".ty-fileItemActive .ty-fileVersion").attr("changeNum")) * 1 + 1,
        isNeedOther: false
    }
    data.isNeedOther = $("#fileUpload input:radio:checked").val()
    if (data.isNeedOther === '1') {
        // 选择了审批人
        data.type = 1
        data.approveName = $(".chooseApprover").find("option:selected").html()
        data.approveId = $(".chooseApprover").val()
    } else {
        // 由文管直接发布
        data.approveId = 0
        if (isGeneral) {
            data.type = 2
        } else {
            data.type = 1
        }
    }
    console.log(`data: ${JSON.stringify(data)}`)
    $.ajax({
        url: '../res/updateFileVersionByCentre.do',
        data: data,
        success: function (res) {
            var state = parseInt(res.data)
            if (state === 1) {
                layer.msg("操作成功")
                bounce.cancel();
                bounce_Fixed.cancel();
            } else {
                $("#mtTip #mt_tip_ms").html("操作失败!") ;
                bounce.show($("#mtTip"));
            }
        }
    })
}

//------------------------- 获取数据 ---------------------------//

// creator: 张旭博, 2020-05-05 10:31:35, 获取审批人
function getApprover() {
    $.ajax({
        url: "../res/getAllOidUserByResource.do" ,
        data: { userID: sphdSocket.user.userID },
        success: function (data) {
            var approverList = data["data"]["list"];
            var optStr = '<option value="">请选择本文件的审批人</option>';
            if (approverList && approverList.length >0){
                for(var i in approverList) {
                    optStr += '<option value="'+ approverList[i].userID +'">'+ approverList[i].userName +'</option>';
                }
            }
            $(".chooseApprover").html(optStr);
            $(".discussChooseApprover").html(optStr);
        }
    })
}

// creator: 侯杏哲,2017-12-04 渲染获取的文件夹数据和页面 ( 适用于主页面文件夹树和移动文件弹框的文件夹树 )
function showTreeData(data , treeItemThis) {
    // data - 获取的数据 ， treeItemThis - 点击的那个文件夹对象 ， treeObj - 点击的树对象

    // 处理子文件夹数据
    var listNotice      = data["parentFolder"] || [];
    var listNoticeChild = data["childFolder"];
    var level = parseInt(treeItemThis.parent().parent().attr("level")) ;
    var nextLevel = level + 1 ;
    var levelStr = "";
    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">' ;
    for(var i in listNoticeChild){
        if(listNoticeChild[i]["childStatus"] === '1'){ // 1时代表此文件夹下有子文件夹
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"]+'</span>' +
                '</div></li>'
        }else{
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"] + '</span>' +
                '</div></li>'
        }
    }
    levelStr += '</div>';
    localStorage.setItem("noticemax",listNotice["childStatus"] ); // childStatus : 1 是有文件夹; 2 是有文件; null标识什么都没有

    treeItemThis.attr("child", listNoticeChild.length > 0)
    if (listNoticeChild.length > 0) {
        if (treeItemThis.next().length === 0) {
            treeItemThis.after(levelStr);
        }
    }

}

// updater: 张旭博，2018-04-20 10:30:37，设置文件列表
function getFile(currentPageNo , pageSize , categoryId){
    var url = "../res/getFile.do" ;
    if(isGeneral || isSuper){
        url = "../res/getFileByManager.do" ;
    }
    var type = $("#fileSort").data('type')
    if(!type){
        type = 1;
    }
    // type - "1"代表获取的是按时间降序的列表，“2”代表获取的是时间升序的列表，“3”代表获取的是文件编号升序的列表，“4”代表的是获取文件编号降序的列表
    var data = {
        "categoryId" : categoryId,
        "currentPageNo" : currentPageNo,
        "pageSize" : pageSize,
        "type" : type
    }
    $.ajax({
        url: url ,
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"],
                jsonStr = JSON.stringify( { "categoryId" : categoryId} ) ;

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo, true);
            if($(".ty-searchContent").is(':visible')){
                setPage( $("#ye-search-file"), currentPageNo, totalPage, "fileMessage", jsonStr) ;
                $(".ty-searchContent .fileContent").find('.searchFile').html(fileListStr).attr("type","folder");
            }else{
                setPage( $("#ye_con"), currentPageNo, totalPage, "fileMessage", jsonStr) ;
                $(".ty-fileList").html(fileListStr);
                $(".ty-fileList").attr("type","folder");
            }
        }
    })
}

// creator: 张旭博，2018-04-24 10:17:16，根据文件数据返回文件列表字符串
function getFileListStr(fileInfo, hasChoose) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        $("#fileSort").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        if ($(".ty-fileContent").is(":visible")) {
            $("#ye_con").show() ;
            $("#fileSort").show() ;
        }
        for(var i = 0; i < fileInfo.length; i++){
            var id          = fileInfo[i].id,
                file        = fileInfo[i].file,
                category    = fileInfo[i].category,
                name        = fileInfo[i].name,
                size        = fileInfo[i].size,
                path        = fileInfo[i].path,
                fileSn      = fileInfo[i].fileSn,
                changeNum   = fileInfo[i].changeNum,
                fileType    = fileInfo[i].version,
                operation   = fileInfo[i].operation,
                updateName  = fileInfo[i].updateName,
                createName  = fileInfo[i].createName,
                updateDate  = fileInfo[i].updateDate,
                createDate  = fileInfo[i].createDate,
                updator     = fileInfo[i].updator,
                creator     = fileInfo[i].creator,
                version     = fileInfo[i].version.toLowerCase(),
                loginUser   = $("#loginUser").html(),
                loginUserId = 0,
                applyDisabledClass = '',    //定义换版申请禁用class
                recordDisabledClass = '',   //定义换版记录禁用class
                handleStr = '',             //定义操作按钮部分字符串
                recycleId = Number($("#listDoc").data("recycleId")); //回收站id



            //获取当前userId
            loginUser   = JSON.parse(loginUser);
            loginUserId = loginUser.userID;

            //格式化数据
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';

            updateName  === null || updateName  == undefined? updateName = createName:updateName;   //更新人为空时 赋值 创建人，最后统一取更新时间 ， 下同
            updateDate  === null || updateDate  == undefined? updateDate = createDate:updateDate;
            if (!updator) {updator = creator}

            operation   === '3' ? applyDisabledClass  = 'ty-disabled': applyDisabledClass  = ''; //换版申请后的禁用（换版申请后  换版和移动按钮被禁用）
            changeNum   === 0   ? recordDisabledClass = 'ty-disabled': recordDisabledClass = ''; //换版记录的禁用 （换版为0 换版记录按钮禁用）

            let funcName = {
                seeOnline:  '在线预览',
                download:   '下载',
                basicMsg:   '基本信息',
                recovery:   '还原',
                delete:     '删除',
                move:       '移动',
                more:       '更多',
                changeVersion:      '换版',
                changeVersionApply: '换版申请',
                changeVersionRecord:'换版记录',
                scanSet:            '使用权限设置',
                changeFileName:     '修改文件名称',
                changeFileNo:       '修改文件编号',
                disable:            '禁用',
                relateSheet:        '相关表格',
                sheetRelate:        '表格关联',
                sheetRelateHandleRecord: '表格关联的操作记录',
                sheetRelateRemove:  '解除关联'
            }

            let func = {}, moreFunc = {}

            func = {
                seeOnline: { path: path },
                download: { path: path, download: name + '.' + fileType },
            }
            for (let key in func) {
                handleStr += '<a class="aBtn" type="btn" name="'+key+'"'
                let attr = func[key]
                for (var item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</a>'
            }
            handleStr += '<ul class="hel" style="display: none">'
            for (let key in moreFunc) {
                handleStr += '<li class="liBtn" type="btn" name="'+key+'"'
                let attr = moreFunc[key]
                for (let item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</li>'
            }
            handleStr += '</ul>'

            var chooseStr = ''
            if (hasChoose) {
                chooseStr =     '   <div class="ty-radio">' +
                                '       <input type="radio" id="resource_choose_'+id+'" name="resource_choose">' +
                                '       <label for="resource_choose_'+id+'"></label>' +
                                '   </div>'
            }
            //单条文件代码字符串拼接
            fileListStr +=  '<div class="ty-fileItem fileItemRadioChoose" id="'+id+'" pid="'+category+'" data-id="'+id+'">'+ chooseStr +
                            '   <div class="ty-fileType ty-file_'+fileType+'"></div>'+
                            '   <div class="ty-fileInfo">'+
                            '      <div class="ty-fileRow">'+
                            '          <div class="ty-fileName" title="'+name+ '-G'+changeNum+'">'+ name +'</div>'+
                            '          <div class="ty-fileNo"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                            '          <div class="ty-fileVersion" changeNum="'+ changeNum +'">'+'G'+changeNum+'</div>'+
                            '      </div>'+
                            '      <div class="ty-fileRow">'+
                            '          <div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + size + ' &nbsp;&nbsp; ' + updateDate + '</div>'+
                            '          <div class="ty-fileHandle">'+ handleStr + '</div>'+
                            '      </div>'+
                            '   </div>'+
                            '   <div class="hd">'+JSON.stringify(fileInfo[i])+'</div>'+
                            '</div>';
        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2022-12-30 09:23:16， 根据外部文件数据返回字符串
function getOutFileListStr(fileInfo){
    var fileListStr = '';
    if(fileInfo.length === 0){
        fileListStr = '<div class="ty-fileNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        for(var i = 0; i < fileInfo.length; i++){
            let fileItem = fileInfo[i]
            let [id, size, path, pathArr, fileType, fileName] = [0, '', '', '']
            let [version, updateName, updateDate] = [0, '', '非法日期']
            let [handleStr] = ['']
            id          = fileItem.id
            size        = formatFileSize(fileItem.size)
            path        = fileItem.path
            version     = fileItem.resourceVersion
            fileType    = fileItem.version
            updateName  = fileItem.updateName || fileItem.createName || ''
            updateDate  = moment((fileItem.updateTime || fileItem.createTime || '')).format("YYYY-MM-DD HH:mm:ss")

            pathArr     = fileItem.path.split('.')
            fileType    = pathArr[pathArr.length - 1]
            fileType    = fileType.toLowerCase()
            fileName    = fileItem.title.slice(0, fileItem.title.lastIndexOf("."))

            let funcName = {
                seeOnline:  '在线预览',
                download:   '下载'
            }

            let func = {}, moreFunc = {}

            func = {
                seeOnline: { path: path },
                download: { path: path, download: fileName + '.' + fileType}
            }
            for (let key in func) {
                handleStr += '<a class="aBtn" type="btn" name="'+key+'"'
                let attr = func[key]
                for (var item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</a>'
            }
            handleStr += '<ul class="hel" style="display: none">'
            for (let key in moreFunc) {
                handleStr += '<li class="liBtn" type="btn" name="'+key+'"'
                let attr = moreFunc[key]
                for (let item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</li>'
            }
            handleStr += '</ul>'
            //单条文件代码字符串拼接
            fileListStr +=  '<div class="ty-fileItem fileItemRadioChoose" id="'+id+'" data-id="'+id+'">'+
                '   <div class="ty-fileType ty-file_'+fileType+'"></div>'+
                '   <div class="ty-fileInfo">'+
                '      <div class="ty-fileRow">'+
                '          <div class="ty-fileName" title="'+fileName+ '-G'+version+'">'+ fileName +'</div>'+
                '          <div class="ty-fileVersion">'+'G'+version+'</div>'+
                '      </div>'+
                '      <div class="ty-fileRow">'+
                '          <div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + size + ' &nbsp;&nbsp; ' + updateDate + '</div>'+
                '          <div class="ty-fileHandle">'+ handleStr + '</div>'+
                '      </div>'+
                '   </div>'+
                '   <div class="hd">'+JSON.stringify(fileItem)+'</div>'+
                '</div>';
        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.siblings(".textMax").text((max - curLength) +'/' + max );
}

// creator: 张旭博，2019-11-18 11:38:02，layDate 插件设置可选择日期
function setCanChooseDate(date) {
    var start = new Date(date.year,date.month-1,1);
    var end = new Date(date.year,date.month,0);
    var startTime = formatTime(start, false)
    var endTime = formatTime(end, false)
    console.log(startTime)
    console.log(endTime)
    $.ajax({
        url:"../forum/monthReplyRecord.do",
        data:{
            id: $(".mainChat").data("theme").id,
            startTime: startTime,
            endTime: endTime,
        },
        success:function(data){
            var dataList= data.listMonthMessage
            if (dataList.length > 0) {
                for(var i in dataList) {
                    var time = start.getFullYear() + '-' + Number(start.getMonth() + 1) + '-' + dataList[i]
                    console.log(time)
                    $(".layui-laydate .layui-laydate-content").find("td[lay-ymd='"+time+"']").removeClass("laydate-disabled")
                }
            }
            console.log(data)
        }
    });
}

// creator: 张旭博，2019-12-17 10:10:48，验证聊天框输入信息
function formatTxt(txt) {
    var text = txt.replace(/<[^<>]+>/g,"");
    var isImg = txt.indexOf("img")
    var isFile = $(".imgUpload .file_item").length > 0
    var isNull = text || isImg !== -1 || isFile
    console.log(isNull)
    return isNull
}

// creator: 张旭博，2019-12-04 16:55:09，添加查看全文字段
function setNewMemo(memo) {
    if (memo) {
        var firstLetter = memo.indexOf('</small>') + 8
        console.log('firstLetter', firstLetter)
        return memo.slice(0,firstLetter) + ' <span class="seeOrigin" type="btn" data-name="seeOrigin">查看原文</span>' + memo.slice(firstLetter)
    } else {
        return ''
    }
}

function customTime(date) {
    if (date) {
        var isToday = new Date().toDateString() === new Date(date).toDateString()
        if (isToday) {
            return new Date(date).toTimeString().substring(0, 8)
        } else {
            return new Date(date).toISOString().substring(0, 10)
        }
    } else {
        return ''
    }
}

// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    $("#picShow img").attr('src', obj.attr('src'));
    $("#picShow").fadeIn("fast");
}

laydate.render({
    elem: '#calendar',
    eventElem: '.fa-calendar',
    trigger: 'click',
    ready: function (date) {
        $(".layui-laydate .layui-laydate-content td").addClass("laydate-disabled")
        setCanChooseDate(date)
    },
    change: function(value, date){
        $(".layui-laydate .layui-laydate-content td").addClass("laydate-disabled")
        setCanChooseDate(date)
    },
    done: function (value, date) {
        var param = {
            postId: $(".mainChat").data("theme").id, // 讨论id
            type: 1,   // 5中状态1是全部信息，2是近7日，3是近1个月，4是近3个月，5是近1年
            time: value,   // 查询全部信息时可以点开日历去查询某天的，这时要把时间传过来，当有此值时就不能传findUser和mesType，同理有这两个值时就不能传time
            findUser: '',   // 查找某人的信息时要把这个人id传过来
            mesType: '',    // 查找信息的类型1是全部的信息2是@我的信息3是回复我的信息
            pageSize:20,
            isQuery: false
        }
        getAllReply(param)
    }
});

// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else {
        console.log("未定义的状态")
    }
    cancelFileDel('all', true)
}