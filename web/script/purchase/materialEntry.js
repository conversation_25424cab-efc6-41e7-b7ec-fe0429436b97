
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#knowTip"));
bounce_Fixed2.cancel();
var mtCategories = null; // 全部种类数据
var listData = { 'category':false , 'state':false , 'keyword':'' } // 列表的传值数据
var editObj = null; // 当前操作的对象
// created by hxz
$(function () {
    // 查看/删除/修改分类
    $('tbody').on('click', 'span', function(){
        var type = $(this).data('type');
        editObj = $(this);
        switch (type){
            case 'mtDel' :
                mtScanDel(type);
                break;
            case 'mtScan':
                bounce.show($("#"+type));
                mtScanDel(type);
                break;
        }
    });
    $.ajax('../skl/getStockMode')
        .then(res => {
            let status = res.status
            $("body").data('whmode', status) //0-非智能 1-智能
        })
    // 点击分类获取分类下的物料
    $('#catAll').on('click', 'li', function(){
        var index = $(this).index();
        var id = $(this).data("id") ;
        if(listData["keyword"] != ""){
            $('.curCat .ty-color-blue').remove();
        }
        listData["keyword"] = ""
        getList(id, "", "", 1 , 20)
    });
    // 分类的编辑删除
    $('#catAll').on('click', '.fa', function(e){
        e.stopPropagation();
        let type = $(this).data("type");
        var id = $(this).parent().parent().data("id") ;
        editObj = $(this) ;
        if(type == "edit"){
            $("#editCat input").val("");
            $("#kindEditID").val(id);
            bounce.show($("#editCat"));
        }else if(type == "del"){
            $("#delCatTip").data("type", id);
            bounce.show($("#delCatTip"));
        }
    });
    // 点击横向导航栏
    $('.curCat').on('click', '.go2Cat', function(){
        var cat = $(this).find(".hd").html();
        $(this).nextAll().remove();
        $(this).remove();
        getList(cat, listData['state'], listData['keyword'], 1 , 20);
    });
    $('.curCat').on('click', '.go2Search', function(){
        var cat = $(this).find(".hd").html();
        $(this).nextAll().remove();
        $(this).remove();
        getList(cat, listData['state'], listData['keyword'], 1 , 20);
    });
    // 获取全部分类/物料
    getList("","","", 1,20)
    $(".mainCon3 ").on('click', '.btn', function(){
        let name = $(this).data("name");
        let thisObj = $(this);
        switch (name) {
            case 'clearNoSave':
                $("#importCancel").data("type",'1');
                bounce_Fixed.show($("#importCancel"));
                break;
            case 'stepNext':
                allImportMtEnter();
                break;
            case "cancelSave": // 放弃
                $("#importCancel").data("type", 2);
                bounce_Fixed.show($("#importCancel"));
                break;
            case "initUpdate": // 修改
                editExpImport(thisObj,1)

                break;
            case "initDel": // 删除
                $("#importMtDel").data("obj", thisObj);
                $("#importMtDel").data("type", '1');
                bounce_Fixed.show($("#importMtDel"));
                break;
            case "update": // 修改
                $(".maskImport").show()
                $(".editInput input").removeAttr('disabled')
                $("#expCont p").hide()
                $("#expCont .bz").show()
                $("#expCont").find('.fa').attr('class','fa fa-circle-o')
                let iinfo = thisObj.parents('tr').find('.expInfo').find('.hd').html()
                if(iinfo){
                    iinfo = JSON.parse(iinfo)
                    if(iinfo.expRequired == 1){
                        $("#expCont .baozhi1").show()
                        $("#baozhi1_import").find('.fa').attr('class','fa fa-dot-circle-o')
                        $("#openDuration_import").val(iinfo.openDuration)
                        $("#len1_import").html(String(iinfo["openDuration"]).length + '/13');

                        if(iinfo.relatedItem == 1){
                            $("#baozhiInput1_import").find('.fa').attr('class','fa fa-dot-circle-o')
                        }else if(iinfo.relatedItem == 2){
                            $("#baozhiInput2_import").find('.fa').attr('class','fa fa-dot-circle-o')
                            $("#expCont .baozhiInput2").show()
                            if(iinfo.sameExpiration == 1){
                                $("#baozhiSame1_import").find('.fa').attr('class','fa fa-dot-circle-o')
                                $("#expCont .baozhiTime").show()
                            }else {
                                $("#baozhiSame0_import").find('.fa').attr('class','fa fa-dot-circle-o')
                            }
                        }

                    }else if(iinfo.expRequired == 0){
                        $("#baozhi0_import").find('.fa').attr('class','fa fa-dot-circle-o')
                    }
                }
                var info = thisObj.parents("tr").data("info");
                $("#updateImportMt").data("obj", thisObj);
                $("#updateImportMt").data("upType", 2);
                $("#updateImportMt input[name=code]").data("old", info["code"]);
                $("#updateImportMt [require]").each(function(){
                    var key = $(this).attr("name");
                    var val = handleNull(info[key]);
                    $(this).val(val);
                });
                $("#unitSelect2").val(info.unit)
                let catName = $("#mtEntryImportBefore").data('name')
                $("#category2").val( catName )
                $("#updateImportMt .memoLen").html(handleNull(info["memo"]).length + '/100');
                bounce_Fixed.show($("#updateImportMt"));
                break;
            case "del": // 删除
                $("#importMtDel").data("obj", thisObj);
                $("#importMtDel").data("type", '2');
                bounce_Fixed.show($("#importMtDel"));
                break;
        }
    });
    $(".bounce_Fixed").on('click', ".ty-btn",function() {
        var name = $(this).data('name');
        switch (name) {
            case "importCancelSure": // 放弃
                var type = $("#importCancel").data("type");
                var enetryType = $("#importEnteryType").data("type");
                bounce_Fixed.cancel();
                if (type == '1') {
                    $(".mainCon1").show().siblings().hide();
                    if (enetryType == 1){
                        $('.addMt li').eq(0).click();
                    } else {
                        $('.addMt li').eq(1).click();
                    }
                } else {
                    turnCancel(enetryType, 2);
                }
                break;
            case "lastSaveSure": // 确定保存
                let cateId = $(".matCategory  .category:last").val();
                var isP = $("#importEnteryType").data("type");
                $.ajax({
                    url: "../mtImport/finishImportMt.do",
                    data: {
                        "category": cateId,
                        "isPurchased": isP,
                        "type": 1
                    },
                    success: function (data) {
                        var state = data.status;
                        bounce_Fixed.cancel();
                        if (state == 1) {
                            toggleSuspend(1);
                            $(".curCat").children(":last").click();
                        } else {
                            layer.msg("保存失败！");
                        }
                    }
                })
                break;
            case "judgeImport": // 批量导入尚未完成是否继续
                var flag = $(".unfinishedForm .fa-circle").data("type")
                var type = $("#importBtn").data("type");
                if (flag == '1') {
                    $(".mainCon3 .importCon2").show().siblings().hide();
                    $(".mainCon3").show().siblings().hide();
                    bounce.cancel(); bounce_Fixed.cancel();
                    getUnFinishImportList(type);
                } else if (flag == '0') {
                    turnCancel(type, 1);
                } else {
                    layer.msg("请选择上次的批量导入尚未完成是否继续！");
                }
                break;
            case "updateImportMt": // 修改确定
                // type : 1-修改基本信息，3-修改保质期
                var upType = $("#updateImportMt").data("upType");
                var emptyNum = 0;
                var obj = $("#updateImportMt").data("obj");
                var trObj = obj.parents("tr");

                if(upType != 3){ // 修改基本信息的
                    $("#updateImportMt [need]").each(function () {
                        var val = $(this).val();
                        if (val == '') emptyNum++;
                    })
                }
                else{
                    // 修改保质期的
                    var expInfo = {}
                    let expRequiredObj = $("#updateImportMt .bz").find('.fa-dot-circle-o')
                    if(expRequiredObj.length === 0){
                        emptyNum++;
                    }else{
                        expInfo.expRequire = expRequiredObj.data('type')
                        if(expInfo.expRequire === 0 ){ // 不需要

                        } else {
                            expInfo.openDuration = $("#openDuration_import").val()
                            let relatedItemObj = $("#updateImportMt .baozhi1").find('.fa-dot-circle-o')
                            if(relatedItemObj.length === 0){
                                emptyNum++;
                            }else{
                                expInfo.relatedItem = relatedItemObj.data('type')
                                if(expInfo.relatedItem === 2){ // 生产日期的
                                    let sameExpirationObj = $("#updateImportMt .baozhiInput2").find('.fa-dot-circle-o')
                                    if(sameExpirationObj.length === 0){
                                        emptyNum++;
                                    }else{
                                        expInfo.sameExpiration = sameExpirationObj.data('type')
                                        if(expInfo.sameExpiration === 1){ // 相同保质期
                                            expInfo.expirationDays = $("#expirationDays_import").val()
                                            if(expInfo.expirationDays.length === 0){
                                                emptyNum++;
                                            }
                                        }

                                    }

                                }

                            }
                        }

                    }
                }

                if (emptyNum > 0) {
                    layer.msg("还有必填项尚未填写！");
                } else {
                    var codeMt = $("#updateImportMt input[name='code']").val();
                    var oldCode = $("#updateImportMt input[name='code']").data("old");
                    var same = 0, name= "";
                    if (upType == 1) {
                        $(".mainCon3 .importCon1 tbody tr").each(function () {
                            if ($(this) !== trObj) {
                                var info = $(this).data("info");
                                if (codeMt == info.code && info.code != oldCode){
                                    same++;
                                    name = info.name;
                                }
                            }
                        })
                    }
                    else if (upType == 2) {
                        $(".mainCon3 .importCon2 tbody tr").each(function () {
                            if ($(this) !== trObj) {
                                var info = $(this).data("info");
                                if (codeMt == info.code && info.code != oldCode) {
                                    same++;
                                    name = info.name;
                                }
                            }
                        });
                    }
                    if(same > 0) {
                        var str = '<p>您录入的材料代号与本次导入的'+ name +'材料代号相同。</p><p>请确认！</p>';
                        $("#iknowTip .iknowWord").html(str);
                        bounce_Fixed2.show($("#iknowTip"));
                    }
                    else {
                        var trData = trObj.data("info");
                        var trData1 = trData;
                        if (upType == 1) {
                            var unit = trObj.find("select").val();
                            $.ajax({
                                url: "../mtImport/updateFalseMtEnter.do",
                                data: {
                                    "code": codeMt,
                                    "unit": unit
                                },
                                success: function (data) {
                                    var status = data.status;
                                    if (status == '1') {
                                        $("#updateImportMt [require]").each(function () {
                                            var key = $(this).attr("name");
                                            var val = $(this).val();
                                            trData[key] = val;
                                        });
                                        bounce_Fixed.cancel();
                                        trObj.find(".sign").each(function () {
                                            var name = $(this).data("name");
                                            $(this).html($("#updateImportMt input[name=" +name +"]").val());
                                        });
                                        trObj.data("info", trData);
                                    } else if(status == '2'){
                                        var tip = '<p>您录入的材料代号与公司'+ data.name +'材料代号相同。</p>' +
                                            '<p>请确认！</p>';
                                        $("#iknowTip .iknowWord").html(tip);
                                        bounce_Fixed2.show($("#iknowTip"));
                                    }
                                }
                            });
                        }
                        else if (upType == 2) {
                            var json = {
                                "id": trData.id,
                            }
                            $("#updateImportMt [require]").each(function () {
                                var key = $(this).attr("name");
                                var val = $(this).val();
                                json[key] = val;
                            });
                            json.unitId = trData.unitId
                            // json.category_ = trData.category_

                            console.log("tr的数据：" + JSON.stringify(trObj.data("info")));
                            updateMtInfo(json, 1);
                        }
                        else if(upType == 3){
                            var data = {
                                "id": trData.id,
                            }
                            var state = 0 ;
                            let bz = $("#updateImportMt .bz").find('.fa-dot-circle-o')
                            if(bz.length > 0){
                                data.expRequired = bz.data('type')
                                if(data.expRequired=== 1){
                                    data.openDuration = $("#openDuration_import").val()
                                    let baozhi1 = $("#updateImportMt .baozhi1").find('.fa-dot-circle-o')
                                    if(baozhi1.length > 0){
                                        data.relatedItem = baozhi1.data('type')
                                        if(data.relatedItem === 2){
                                            let baozhiInput2 = $("#updateImportMt .baozhiInput2").find('.fa-dot-circle-o')
                                            if(baozhiInput2.length > 0){
                                                data.sameExpiration = baozhiInput2.data('type')
                                                if(data.sameExpiration === 1){
                                                    data.expirationDays = $("#expirationDays_import").val()
                                                    if($.trim(data.expirationDays ) === ''){
                                                        state ++;
                                                    }
                                                }

                                            }else{
                                                state ++;
                                            }
                                        }
                                    }else{
                                        state ++;
                                    }
                                }
                            }else {
                                state ++;
                            }
                            if(state>0){
                                layer.msg("还有必填项尚未填写！");
                                return false;
                            }else{
                                console.log("tr的数据：" + JSON.stringify(trObj.data("info")));
                                updateMtInfo3(data);
                            }
                        }
                    }
                }
                break;
        }
    });
    $(".unfinishedForm").on('click', ".fa",function() {
        $(this).addClass("fa-circle").removeClass("fa-circle-o");
        $(this).parent().siblings(".changeDot").find("span").addClass("fa-circle-o").removeClass("fa-circle");
    });
    $('#uploadFile').Huploadify({
        auto:false ,
        fileTypeExts:'*.xls;*.xlsx;',
        multi:false,
        formData:{
            module: '采购',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:40960,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader: "../uploads/uploadfyByFile.do",
        itemTemplate: '<div id="${fileID}" class="uploadify-queue-item matListUpload" style="display: none">' +
        '<div class="uploadify_bottom"><div><a class="uploadbtn" href="javascript:void(0);"></a></div></div>' +
        '</div>',
        onUploadStart:function(){
        },
        onInit:function(){},
        onSelect:function (file) {
            $('.matListUpload:not(:last)').remove();
            $('#leading .fileFullName').html(file.name);
        },
        onUploadComplete:function(file,data){
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data);
            var filePath = data.filename;
            var fileUid =data.fileUid;
            $('#leading .fileFullName').data('fileid', fileUid);
            ImportMaterial(filePath)
        } ,
        onCancel:function(file){
        }
    });
});

// create: hxz 2023-12-01  录入材料
function addMtBtn(num) {
    $("#importBtn").show();
    $("#mtEntry .bonceHead span").html("材料录入");
    var type = num
    $('.say' + type).show().siblings().hide();
    $("#mtEntry [require]").removeAttr("disabled");
    $(".category").removeAttr("disabled");
    bounce.show($('#mtEntry'));
    $("#importBtn").data("type", type);
    $("#mtEntry .editTip").hide();
    $("#unitSelectBtn").show();
    $("#mtEntry input").val("")
    $("#mtEntry select").val("")
    $("#mtEntry .category:gt(0)").remove();
    $("#mtEntry [name='id']").val("")
    $("#mtEntry [name='isPurchased']").val(type) // 是否采购过
    $("#mtEntry [name='enabled']").val(1) // 是否再购
    $("#mtEntry [name='operation']").val(1)
    $("#mtEntry [name='userId']").val(sphdSocket.user.userID);
    setCat("", $("#mtEntry .category:eq(0)"));
    getUnitList($("#unitSelect"),7);
    $("#mtEntry .textMax").html("0/100");
    $(".baozhi1").hide()
    $(".baozhiInput2").hide()
    $(".baozhiTime").hide()
    $("#mtEntry .fa").attr("class", 'fa fa-circle-o')
    let mode = $("body").data('whmode')
    if (mode === 0) {
        $("#mtEntry .intelligentWh").remove()
    }

}
// create: hxz 2-23-12-01  新增分类 (新增材料的时候)
function addCatBtn2() {
    addCatBtn(2)
}
//create: hxz 2-23-12-01  新增分类
function addCatBtn(num) {
    var catLen = $("#catAll").children().length ;
    var mtLen = $(".mainCon1 tbody").children().length ;
    if(catLen > 0 || (mtLen == 0)){
    }else{
        layer.msg("当前分类下有材料，不能新增子级类别");return false;
    }
    var pid = $(".curCat").children(".go2Cat:last").children(".hd").html();
    var pName = $(".curCat").children(":last").children("span:eq(0)").html();
    if(pName == "待分类"){
        layer.msg("待分类下不能新增子类！");return false;
    }
    $('#addCat').data('type', num)
    bounce.show($('#addCat'));
    $("#addkind_pid").val(pid);
    var txt = "";
    $(".curCat").children(".go2Cat").each(function(){
        txt += $(this).children(":eq(0)").html() ;
    })
    $("#catParents").html(txt);
    $("#newCatName").val("");
}
function baozhi(thisObj) {
    thisObj.parent().find('.fa').attr('class', 'fa fa-circle-o')
    thisObj.find('.fa').attr('class', 'fa fa-dot-circle-o')
    let type = thisObj.find('.fa').data('type')
    if(type === 1){
        $(".baozhi1").show()
        $(".baozhiInput2").hide()
        $(".baozhiTime").hide()
        $("#len1").html('0/13')
        $("#openDuration").val('')
        $("#expirationDays").val('')
        $(".baozhi1 .fa").attr('class','fa fa-circle-o')
        $(".baozhiInput2 .fa").attr('class','fa fa-circle-o')
    }else{
        $(".baozhi1").hide()
        $(".baozhiInput2").hide()
        $(".baozhiTime").hide()
    }
}
function baozhiInput(thisObj) {
    thisObj.parent().find('.fa').attr('class', 'fa fa-circle-o')
    thisObj.find('.fa').attr('class', 'fa fa-dot-circle-o')
    let type = thisObj.find('.fa').data('type')
    if(type === 2){
        $(".baozhiInput2").show()
        $(".baozhiTime").hide()
        $("#expirationDays").val('')
        $(".baozhiInput2 .fa").attr('class','fa fa-circle-o')
    }else{
        $(".baozhiInput2").hide()
        $(".baozhiTime").hide()
    }
}

function baozhiSame(thisObj) {
    thisObj.parent().find('.fa').attr('class', 'fa fa-circle-o')
    thisObj.find('.fa').attr('class', 'fa fa-dot-circle-o')
    let type = thisObj.find('.fa').data('type')
    if(type === 1){
        $(".baozhiTime").show()
        $("#expirationDays").val('')

    }else{
        $(".baozhiTime").hide()

    }

}



// created:hxz 2021-01-09 导入材料
function ImportMaterial(path) {
    $.ajax({
        "url":"../export/ImportMaterial.do",
        "data":{ 'filePath': path },
        success:function (data) {
            var status = data.status;
            if (status == '1') {
                $(".mainCon3 table tbody").html("");
                var mtBaseList = data.trueMtBaseList;
                var mtFalseList = data.falseMtBaseList;
                $(".importCon1").data("trueUserList", mtBaseList);
                bounce.cancel();bounce_Fixed.cancel();
                var unitList = getUnitListData(7);
                if (mtFalseList && mtFalseList.length > 0) {
                    loading.close() ;
                    var html = '';
                    for (var a=0; a<mtFalseList.length;a++) {
                        var item = {
                            "code": mtFalseList[a].code,
                            "name": mtFalseList[a].name,
                            "specifications": mtFalseList[a].specifications,
                            "model": mtFalseList[a].model,
                            "memo": mtFalseList[a].memo
                        };
                        html +=
                            '<tr data-info=\''+ JSON.stringify(mtFalseList[a]) +'\'>' +
                            '    <td class="sign" data-name="name">'+ handleNull(mtFalseList[a].name) +'</td>' +
                            '    <td class="sign" data-name="code">'+ handleNull(mtFalseList[a].code) +'</td>' +
                            '    <td class="sign" data-name="model">'+ handleNull(mtFalseList[a].model) +'</td>' +
                            '    <td class="sign" data-name="specifications">'+ handleNull(mtFalseList[a].specifications) +'</td>' +
                            '    <td>' +
                            '        <select type="text" name="unitId" require ></select>' +
                            '    </td>' +
                            '    <td class="sign" data-name="memo">'+ handleNull(mtFalseList[a].memo) +'</td>' +
                            '    <td>' +
                            '          <span class="ty-color-blue btn" data-name="initUpdate">修改</span>' +
                            '          <span class="ty-color-red btn" data-name="initDel">删除</span>' +
                            '    </td>' +
                            '</tr>';
                    }
                    $(".mainCon3 .importCon1 table tbody").html(html);
                    $(".mainCon3 .importCon1 .initAll").html(data.importSum);
                    $(".mainCon3 .importCon1 .initWrong").html(data.falseImportSum);
                    $(".main").hide();
                    $(".mainCon3 .importCon1").show().siblings().hide();
                    $(".mainCon3").show().siblings().hide();
                    var str = '<option value="">- - 请选择 - - </option>';
                    if(unitList && unitList.length >0){
                        for(var i = 0 ; i < unitList.length ; i++){
                            var item = unitList[i];
                            str += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                        }
                    }
                    $(".mainCon3 .importCon1 tbody select").html(str);
                    for (var z = 0; z < mtFalseList.length; z++) {
                        if (mtFalseList[z]["unit"] != "") {
                            let find = unitList.findIndex(function (item){
                                return item['name'] === mtFalseList[z]["unit"]//返回满足条件的第一个值所在的位置
                            })
                            if (find > -1) {
                                $(".mainCon3 .importCon1 tbody select").eq(z).val(unitList[find]['id']);
                            } else {
                                let option = `<option value="" selected>${mtFalseList[z]["unit"]}</option>`;
                                $(".mainCon3 .importCon1 tbody select").eq(z).append(option);
                            }
                        }
                    }
                } else {
                    var purchasedType = $("#leading").data("type");
                    var json = {
                        mtBasesList: JSON.stringify(mtBaseList),
                        importSum: data.importSum,
                        isPurchased: purchasedType
                    };
                    saveImportMtList(json);
                }
            } else {
                loading.close() ;
                $('#select_btn_1').val("");
                $('.matListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                bounce_Fixed2.show($("#importantTip"));
            }
        }
    })
}
// creator: hxz，2023-12-04 08:37:14 修改 导入 材料的 保质期情况
function editExpImport(thisObj,type) {
    // type : 1-修改基本信息，3-修改保质期
    var info = thisObj.parents("tr").data("info");
    $("#updateImportMt input").val("");
    $("#updateImportMt").data("obj", thisObj);
    $("#updateImportMt").data("upType", type);
    $("#updateImportMt input[name=code]").data("old", info["code"]);
    $("#updateImportMt [require]").each(function(){
        var key = $(this).attr("name");
        var val = handleNull(info[key]);
        $(this).val(val);
    });
    $("#unitSelect2").val(info.unit)
    let catName = $("#mtEntryImportBefore").data('name')
    $("#category2").val( catName )
    $("#len1_import").html('0/13')
    $("#updateImportMt .memoLen").html(handleNull(info["memo"]).length + '/100');
    // type:1- 修改基本信息， 3- 修改保质期情况
    if(type === 1){
        $("#expCont").hide()
        $(".maskImport").show()
        $(".editInput input").removeAttr('disabled')
    }else if(type === 3){
        $("#expCont").show()
        $("#updateImportMt").data("upType", 3);
        $(".maskImport").hide()
        $(".editInput input").attr('disabled','disabled')

        $("#expCont p").hide()
        $("#expCont .bz").show()
        $("#expCont").find('.fa').attr('class','fa fa-circle-o')
        var iinfo = info
        let iinfoww = thisObj.parents('tr').find('.expInfo').find('.hd').html()
        if(iinfoww){
            iinfo = JSON.parse(iinfoww)
        }
    console.log('可可 iinfo =', iinfo)
        if(iinfo){
            if(iinfo.expRequired == 1){
                $("#expCont .baozhi1").show()
                $("#baozhi1_import").find('.fa').attr('class','fa fa-dot-circle-o')
                $("#openDuration_import").val(iinfo.openDuration)
                $("#len1_import").html(String(iinfo.openDuration).length + '/13')
                if(iinfo.relatedItem == 1){
                    $("#baozhiInput1_import").find('.fa').attr('class','fa fa-dot-circle-o')
                }else if(iinfo.relatedItem == 2){
                    $("#baozhiInput2_import").find('.fa').attr('class','fa fa-dot-circle-o')
                    $("#expCont .baozhiInput2").show()
                    if(iinfo.sameExpiration == 1){
                        $("#baozhiSame1_import").find('.fa').attr('class','fa fa-dot-circle-o')
                        $("#expCont .baozhiTime").show()
                        $("#expirationDays_import").val(iinfo.expirationDays )

                    }else {
                        $("#baozhiSame0_import").find('.fa').attr('class','fa fa-dot-circle-o')
                    }
                }

            }else if(iinfo.expRequired == 0){
                $("#baozhi0_import").find('.fa').attr('class','fa fa-dot-circle-o')
            }
        }

    }

    bounce_Fixed.show($("#updateImportMt"));

}
// creator: 李玉婷，2021-07-21 08:37:14，修改计量单位、材料信息
function nextUnitChange(obj) {
    let trObj = obj.parents("tr");
    let unit = obj.val();
    var trData = trObj.data("info");
    $.ajax({
        url: "../mtImport/updateFalseMtEnter.do",
        data: {
            "code": trData.code,
            "unit": unit
        },
        success: function (data) {
            var status = data.status;
            if(status == '3'){
                var tip = '<p>您录入的计量单位已被禁用。</p>' +
                    '<p>请确认！</p>';
                $("#iknowTip .iknowWord").html(tip);
                bounce_Fixed2.show($("#iknowTip"));
            }
        }
    });
}
// created:hxz 2020-09/17 首页返回上一级/返回全部
function backPreCat(num) {
    var index = $('.curCat .go2Cat').length - 1; // 返回全部
    if(num === 1){ // 返回全部
        listData['state'] = ""
        // 获取全部分类/物料
        getList("","","", 1,20)
    }else{
        $('.curCat').children(":nth-child("+ index +")").click();
    }
}
// created:hxz 2020-03-11 搜索物料
function search(thisObj) {
    var keyword = thisObj.siblings('input').val();
    if(keyword == ""){
        layer.msg("请录入需要搜索的内容");
    }else {
        listData.keyword = keyword ;
        getList('', listData.state, keyword, 1 , 20);
    }
}
// created:hxz 2020-03-11 新增分类
function addCatCacel() {
    let num = $('#addCat').data('type')
    if(num === 2){
        bounce.show($('#mtEntry'));
        let typee = $("#importBtn").data("type");
        addMtBtn(typee)
    }else if(num === 3){
        mtEntryImportBefore()
    }else {
        bounce.cancel()
    }
}
function addCatOk() {
    var pid =  $("#addkind_pid").val();
    var type =  1 ; // 0 新增同级 ； 1 新增子级 ； 2 修改分类
    var name = $("#newCatName").val();
    // 判断名字是否重复
    var isRepeat = false;
    for(var i = 0 ; i < mtCategories.length ; i++){
        if(mtCategories[i]["name"] == name){
            isRepeat = true;
        }
    }
    if (isRepeat) {
        layer.msg("已存在重复的同级类别名称！");
        return false;
    }
    if(name.length > 50){
        layer.msg("类别名称太长了！");
        return false;
    }
    $.ajax({
        url:"../material/addMtCategoryByPid.do",
        data:{ pid:pid , type:type , name:name  },
        success:function (res) {
            var category = res['category']
            var child = res['child']
            layer.msg('新增成功');
            bounce.cancel();
            var catInfo = { 'id':category['id'] , 'name':category['name'] , 'parent':pid || null , 'count':0 }
            mtCategories.push(catInfo);
            if(child){
                var catChildInfo = { 'id':child['id'] , 'name':child['name'] , 'parent':category['id'] , 'count':0 }
                mtCategories.push(catChildInfo);
            }
            var ctrlStr = "<span class=\"catCtrl\">" +
                "   <i data-type='edit' class='fa fa-pencil'></i>"+
                "   <i data-type='del' class='fa fa-trash'></i>"+
                "</span>" ;
            var str = "<li class='catID"+ catInfo['id'] + "' data-id='"+ catInfo['id'] +"'><span class='name'>"+ catInfo['name'] +"</span><span class=\"catNum\">"+ catInfo['count'] +"</span>"+ ctrlStr + "</li>"
            $("#catAll").append(str)
            mtCategoriesTree();

            addCatCacel()

        }
    })
}
// created:hxz 2020-03-11 物料查看与删除
function mtScanDel(type, num) {
    var info = editObj.siblings(".hd").html();
    info = JSON.parse(info);
    var data = { 'operation':10 , 'userId':sphdSocket.user.userID , 'id':info['id'] };
    if(type == 'mtDel' && num == 1){
        data['operation'] = 2 ;
    }
    $.ajax({
        "url":"../mt/operation",
        "data":data ,
        success:function(res){
            $("#editMtBtn").data('info',JSON.stringify(res));
            if(type == 'mtDel' && num == 1){ // 删除
                bounce.cancel();
                var code = res['code']
                if(code == 1){
                    layer.msg('删除成功！');
                    editObj.parent().parent().remove();
                }else if(code == 2){
                    $("#tip1 .bonceCon").html("系统不支持“删除”已有采购数据的材料，而此材料在系统中已有与采购有关的数据！")
                    bounce_Fixed.show($("#tip1"));
                }
            }else{ // 查看或者删除的前奏
                var data = res['data'][0] ;
                if(type == 'mtDel'){
                    if(data['origin'] == 3 || data['origin'] == 4 || data['origin'] == 5){
                        $("#tip1 .bonceCon").html("此种材料与产品有关，不可在此删除！")
                        bounce_Fixed.show($("#tip1"));
                    }else if(data['is_appointed'] == '0'){
                        $("#tip1 .bonceCon").html("系统不支持“删除”已有采购数据的材料，而此材料在系统中已有与采购有关的数据！")
                        bounce_Fixed.show($("#tip1"));
                    }else if(Number(info['supplier_number']) > 0){
                        $("#tip1 .bonceCon").html("系统不支持“删除”已有采购数据的材料，而此材料在系统中已有与采购有关的数据！")
                        bounce_Fixed.show($("#tip1"));
                    }else {
                        bounce.show($("#mtDel"));
                    }
                }else{
                    for(var key in data){
                        if(key == 'create_name' || key == 'create_date' ){

                        }else{
                            $(".scanMt_" + key).html(data[key]);
                        }
                    }
                    let mode = $("body").data('whmode')
                    if (mode === 1) {
                        let expStr = ``
                        if(data.exp_required === 1){
                            expStr = `有保质期方面的要求，`
                            if(data.open_duration){
                                expStr += `开瓶(开封)后可使用${ data.open_duration }日，`
                            }
                            // 相关的数据:1-截止日期,2-生产日期
                            if(data.related_item === 1){
                                expStr += `入库时,需录入可使用的截至日期。`
                            }else if(data.related_item === 2){
                                expStr += `入库时,需录入生产日期，`
                                // 保质期是否相同 0-不相同 1-相同
                                if(data.same_expiration === 1){
                                    expStr += `不同供应商的供应该材料均为自生产日期之后${ data.expiration_days }日`
                                }else{
                                    expStr += `不同供应商的供应该材料的保质期不同`
                                }
                            }

                        }else{
                            expStr = `无保质期方面的要求`
                        }
                        $("#mtScan .scanExpShow").show()
                        $("#mtScan .scanMt_expStr").html(expStr)
                    } else {
                        $("#mtScan .scanExpShow").hide()
                    }
                    var str = "";
                    if(data['enabled'] == 0){ // 暂停采购了
                        $("#editMtBtn").hide();
                        str = data['update_name'] + "已于"+ (new Date(data['enabled_time']).format('yyyy-MM-dd hh:mm:ss')) +"将本材料“暂停采购”！。"
                    }else if(data['origin'] == 3 || data['origin'] == 4 || data['origin'] == 5){
                        str = "本材料来自于产品的拆分（包装）。"
                        $("#editMtBtn").show();
                    }else{
                        $("#editMtBtn").show();
                        switch (Number(data.is_purchased)){
                            case 1 :
                                str = "本材料录入时为“曾采购过的材料”。" ; break;
                            case 0 :
                                str = "本材料录入时为“未采购过的材料”。" ; break;
                        }
                    }
                    $(".scanMt_state").html(str)

                    $(".scanMt_cat").html(res['path']);
                    $(".scanMt_create").html(data['create_name'] + " " + (new Date(data['create_date']).format('yyyy-MM-dd hh:mm:ss')));
                }


            }
        }
    })
}
// created:hxz 2020-03-11 切换主页与暂停页
function toggleSuspend(num){
    $('.mainCon' + num).show().siblings().hide();
}
// create :hxz 2020-04-21 获取物料的列表
function getList(category, state, keyword, cur, per){
    // state:查询总列表时不传,  1-查询待确认定点信息时, 2-查询暂停采购的材料时, 3-查询待录入初始库存数量时
    // keyword : 查询的代号或名称
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "keyword":keyword, "pageNum":cur, "per":per };
    if (state){  data['state'] =  state  }
    if (category){  data['category'] =  category  } else {

    }
    $(".addCat").show();
    data['menu'] =  1
    $.ajax({
        "url":"../mt/list",
        "data":data,
        success:function (res) {
            //设置分页
            var totalPage = res["totalCount"];//总页数
            var curr = cur ;//当前页
            var jsonStr = JSON.stringify(data) ;
            if(state == 2) { // 暂停
                setPage( $("#ye2") , curr ,  totalPage , "materialEntery", jsonStr );
            }else{
                setPage( $("#ye1") , curr ,  totalPage , "materialEntery", jsonStr );
            }
            var categories = res['categories'];
            var count = res['count'];
            var mtArr = res['data'];
            var enabledCount = res['enabledCount'];
            $("#enabledCount").html(enabledCount);
            $(".catNumPause").html(enabledCount);

            var pid = null;
            var path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span>";
            $("#bottom2").hide(); $("#bottom1").show();
            if(category){  pid = category;  }
            if (!state){   mtCategories = categories; mtCategoriesTree();  }
            if(state != 2){
                $("#catAll").html("");
                for(var i = 0 ; i < categories.length ; i++){
                    var pCat = categories[i]['parent'];
                    if(pCat == pid){
                        var count = 0 ;
                        $("#catTree").find(".treeItem"+ categories[i]['id']).find(".countItem").each(function(){
                            count += Number($(this).html());
                        });
                        categories[i]['count'] = count ;
                        var ctrlStr = "<span class=\"catCtrl\">" +
                            "   <i data-type='edit' class='fa fa-pencil'></i>"+
                            "   <i data-type='del' class='fa fa-trash'></i>"+
                            "</span>" ;
                        var sysCat = ["构成商品的原辅材料", "外购成品", "商品的包装物", "其他原辅材料"] , catName = categories[i]['name'];
                        if(!category && sysCat.indexOf(catName)>-1){
                            ctrlStr = "";
                        }else if(catName == "待分类"){
                            ctrlStr = "";
                        }

                        var str = "<li class='catID"+ categories[i]['id'] + "' data-id='"+ categories[i]['id'] +"'>" +
                            "<span class='name'>"+ categories[i]['name'] +"</span>" +
                            "<span class=\"catNum\">"+ categories[i]['count'] +"</span>" + ctrlStr +
                            "</li>";
                        $("#catAll").append(str);
                    }
                    if(category == categories[i]['id']){
                        $("#bottom2").show(); $("#bottom1").hide();
                        path = "<span class='go2Cat'><span>"+ categories[i]['name'] +" > </span><span class='hd'>"+ categories[i]['id']  +"</span></span>"
                        if( categories[i]['name'] == "待分类"){
                            $(".addCat").hide();
                        }
                    }
                }
                var allCount = 0
                $("#catTree").find(".countItem").each(function(){
                    allCount += Number($(this).html());
                });
                $(".mtNum").html(allCount);
            }

            var strMt = "" ;
            if(mtArr && mtArr.length >0){
                for(var j = 0 ; j < mtArr.length ; j++){
                    var item = mtArr[j]
                    strMt += " <tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ (item['model'] || "") +"</td>" +
                        "    <td>"+ (item['specifications'] || "") +"</td>" +
                        "    <td>"+ (item['unit'] || "") +"</td>" +
                        "    <td>"+ item['create_name'] + (new Date(item['create_date']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                    if(state == 2) { // 暂停
                        strMt += " <td>"+ item['mb_update_name'] + (new Date(item['mb_update_date']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                    }
                    strMt += "    <td>" +
                        "        <span class=\"hd\">"+JSON.stringify(item)+"</span>" +
                        "        <span data-type=\"mtScan\" class=\"ty-color-blue \">查看</span>" +
                        "        <span data-type=\"mtDel\" class=\"ty-color-red \">删除</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            var conStr = ".mainCon1  tbody";
            if(state == 2){ // 暂停
                conStr = ".mainCon2  tbody";
                if(keyword){
                    path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span><span><span>查找代号或名称含“"+ keyword +"”的原辅材料 > </span></span>";
                    $(".curCat").append(path);
                }
            }else{
                if(keyword){
                    path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span><span class=' go2Search ty-color-blue'><span>查找代号或名称含“"+ keyword +"”的原辅材料 > </span></span>";
                    $(".curCat").html(path);
                }else{
                    if(path == "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span>"){
                        $(".curCat").html(path);
                    }else{
                        $(".curCat").append(path);
                    }
                }

            }
            $(conStr).html(strMt);

        }
    })
}
function getListByPage(category, state, keyword, cur, per){
    // state:查询总列表时不传,  1-查询待确认定点信息时, 2-查询暂停采购的材料时, 3-查询待录入初始库存数量时
    // keyword : 查询的代号或名称
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "keyword":keyword, "pageNum":cur, "per":per };
    if (state){  data['state'] =  state  }
    if (category){  data['category'] =  category  }
    data['menu'] =  1
    $.ajax({
        "url":"../mt/list",
        "data":data,
        success:function (res) {
            //设置分页
            var totalPage = res["totalCount"];//总页数
            var curr = cur ;//当前页
            var jsonStr = JSON.stringify(data) ;
            if(state == 2) { // 暂停
                setPage( $("#ye2") , curr ,  totalPage , "materialEntery", jsonStr );
            }else{
                setPage( $("#ye1") , curr ,  totalPage , "materialEntery", jsonStr );
            }
            var categories = res['categories'];
            var count = res['count'];
            var mtArr = res['data'];
            var enabledCount = res['enabledCount'];
            $("#enabledCount").html(enabledCount);
            var pid = null;
            $("#bottom2").hide(); $("#bottom1").show();
            if(category){  pid = category;  }
            if (!state){   mtCategories = categories; mtCategoriesTree();  }
            $("#catAll").html("");
            for(var i = 0 ; i < categories.length ; i++){
                var pCat = categories[i]['parent'];
                if(pCat == pid){
                    var count = 0 ;
                    $("#catTree").find(".treeItem"+ categories[i]['id']).find(".countItem").each(function(){
                        count += Number($(this).html());
                    });
                    categories[i]['count'] = count ;
                    var ctrlStr = "<span class=\"catCtrl\">" +
                        "   <i data-type='edit' class='fa fa-pencil'></i>"+
                        "   <i data-type='del' class='fa fa-trash'></i>"+
                        "</span>" ;
                    var sysCat = ["构成商品的原辅材料", "外购成品", "商品的包装物", "其他原辅材料"] , catName = categories[i]['name'];
                    if(!category && sysCat.indexOf(catName)>-1){
                        ctrlStr = "";
                    }else if(catName == "待分类"){
                        ctrlStr = "";
                    }
                    var str = "<li class='catID"+ categories[i]['id'] + "' data-id='"+ categories[i]['id'] +"'>" +
                        "<span class='name'>"+ categories[i]['name'] +"</span>" +
                        "<span class=\"catNum\">"+ categories[i]['count'] +"</span>" + ctrlStr +
                        "</li>";
                    $("#catAll").append(str);
                }
                if(category == categories[i]['id']){
                    $("#bottom2").show(); $("#bottom1").hide();
                }
            }
            var allCount = 0
            $("#catTree").find(".countItem").each(function(){
                console.log($(this).html());
                allCount += Number($(this).html());
            });
            $(".mtNum").html(allCount);
            var strMt = "" ;
            if(mtArr && mtArr.length >0){
                for(var j = 0 ; j < mtArr.length ; j++){
                    var item = mtArr[j]
                    strMt += " <tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ (item['model'] || "") +"</td>" +
                        "    <td>"+ (item['specifications'] || "") +"</td>" +
                        "    <td>"+ (item['unit'] || "") +"</td>" +
                        "    <td>"+ item['create_name'] + (new Date(item['create_date']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                    if(state == 2) { // 暂停
                        strMt += " <td>"+ item['update_name'] + (new Date(item['enabled_time']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                    }
                    strMt += "    <td>" +
                        "        <span class=\"hd\">"+JSON.stringify(item)+"</span>" +
                        "        <span data-type=\"mtScan\" class=\"ty-color-blue \">查看</span>" +
                        "        <span data-type=\"mtDel\" class=\"ty-color-red \">删除</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }

            var conStr = ".mainCon1  tbody";
            if(state == 2){ // 暂停
                conStr = ".mainCon2  tbody";
            }
            $(conStr).html(strMt);

        }
    })
}


// creator: hxz 2020-04-07 查看暂停采购
function goPause(){
    listData = { 'category':false , 'state':2 , 'keyword':'' }
    getList('', 2, '', 1, 20);
    toggleSuspend(2)
}
// creator: hxz 2020-04-07 修改物料
function editMtBtn() {
    $("#importBtn").hide();
    bounce.show($("#mtEntry"));
    var res = JSON.parse($("#editMtBtn").data('info')) ;
    var order_count = res['order_count'];
    var data = res['data'][0];
    data['operation'] = 3 ;
    data['userId'] = sphdSocket.user.userID ;
    var purChaseStr = "本材料录入时为“未曾采购过的材料”。";
    var is_purchased = data['is_purchased'];
    var origin =  res.origin ;
    if(origin == 3 ||origin == 4 ||origin == 5 ){
        purChaseStr = "本材料来自于产品的拆分（包装），<span class='red'>其一级类别不可修改，基本信息则不可在此修改。</span>";
        $("#mtEntry .editTip").hide();
        $("#unitSelectBtn").hide();
    }
    else {
        $("#unitSelectBtn").show();
        $("#mtEntry .editTip").show();
        if(is_purchased == "1"){
            purChaseStr = "本材料录入时为“曾采购过的材料”。";
        }
        if(order_count == 0){
            $("#mtEntry .orderCount0").hide();
        }else{
            $("#mtEntry .orderCount0").show();
            $("#mtEntry .order_count").html(order_count);
        }
    }
    $("#mtEntry .say").html(purChaseStr).show().siblings().hide();
    // 给修改页面赋值
    $("#mtEntry .bonceHead span").html("材料修改");
    $("#mtEntry input").each(function () {
        var name = $(this).attr("name")
        $(this).val(data[name]);
    });
    getUnitList($("#unitSelect"),7,data['unit_id']);
    // 赋值种类
    var category_id = data.category_id;
    var curCatObj = $("#catTree").find(".treeItem" + category_id);
    var pCatsArr = curCatObj.parents(".treeItem");
    var catsArr = [];
    pCatsArr.each(function(){
        var kls = $(this).attr("class");
        var catId = kls.substr(17, kls.length);
        catsArr.push(catId);
    })
    catsArr.reverse().push(category_id);
    for(var i = 0 ; i < catsArr.length ; i++){
        setCat(catsArr[i], $("#mtEntry .categorySelectList"), 1, origin);
    }
    if(origin == 3 ||origin == 4 ||origin == 5 ){
        $("#cats").children(".category:eq(0)").attr("disabled",true);
        $("#mtEntry [require]").attr("disabled", true);
    }else{
        $("#cats").children(".category:eq(0)").removeAttr("disabled");
        $("#mtEntry [require]").removeAttr("disabled");
    }

    data.memo = data.memo || ''
    $("#len2").html(data.memo.length + '/100')

    $(".baozhi1").hide()
    $(".baozhiInput2").hide()
    $(".baozhiTime").hide()

    baozhi($(`#baozhi${ data.exp_required }`))

    if(data.exp_required === 1){
        $("#openDuration").val(data.open_duration)
        if(data.open_duration){
            $("#len1").html(String(data.open_duration).length + '/13')
        }else{
            $("#len1").html('0/13')
        }

        // 相关的数据:1-截止日期,2-生产日期
        baozhiInput($(`#baozhiInput${ data.related_item }`))
        baozhiSame($(`#baozhiSame${ data.same_expiration }`))
        if(data.same_expiration === 1){
            $("#expirationDays").val(data.expiration_days )
        }

    }

}

// creator: hxz 2020-04-07 保存物料
function mtEditOk() {
    var state = 0 ;
    var data = {}
    data["category_"] = $("#mtEntry .category:last").val();

    $("#mtEntry").find("[need]").each(function () {
        if($.trim($(this).val()) === ''){
            state ++;
        }
    });
    let mode = $("body").data('whmode')
    if (mode !== 0) {
        let bz = $("#mtEntry .bz").find('.fa-dot-circle-o')
        if(bz.length > 0){
            data.expRequired = bz.data('type')
            if(data.expRequired=== 1){
                data.openDuration = $("#openDuration").val()
                let baozhi1 = $("#mtEntry .baozhi1").find('.fa-dot-circle-o')
                if(baozhi1.length > 0){
                    data.relatedItem = baozhi1.data('type')
                    if(data.relatedItem === 2){
                        let baozhiInput2 = $("#mtEntry .baozhiInput2").find('.fa-dot-circle-o')
                        if(baozhiInput2.length > 0){
                            data.sameExpiration = baozhiInput2.data('type')
                            if(data.sameExpiration === 1){
                                data.expirationDays = $("#expirationDays").val()
                                if($.trim(data.expirationDays ) === ''){
                                    state ++;
                                }
                            }

                        }else{
                            state ++;
                        }
                    }
                }else{
                    state ++;
                }
            }
        }else {
            state ++;
        }
    }

    if(state>0 || data["category_"] == ""){
        layer.msg("还有必填项尚未填写！");
        return false;
    }
    $("#mtEntry [require]").each(function () {
        var name = $(this).attr("name")
        data[name] = $(this).val()
    });
    console.log('打印传参', data)
    $.ajax({
        "url":"../mt/operation",
        "data":data,
        success:function(res){
            var code = res["code"]
            bounce.cancel();
            if(code == 1){
                layer.msg("保存成功");
                if(data.operation ==  1){ // 新增
                    var countObj = $("#catTree .treeItem" + data['category_']).children(".countItem") ;
                    countObj.html( Number(countObj.html()) + 1 );
                }else{

                }
                var curCatidArr = [];
                $(".curCat").children().each(function(){
                    var id = $(this).children(".hd").html();
                    curCatidArr.push(Number(id))
                })
                if(curCatidArr.indexOf(Number(data['category_']))){
                }
                $(".curCat").children(":last").click();

            }else {
                var message = res['message'];
                layer.msg(message);
            }
        }
    })
}
//creator:hxz 2020-04-08 设置要选择的分类
function setCat(pid, obj, seleted, origin){
    // pid 点击的父级类id,
    // obj 点击的父级类dom
    // seleted 如果设置选中pid就是当前的(用在赋默认值)，不是父级的
    var categories = mtCategories;
    var str = "<option value=''>--- 请选择分类--- </option>";
    var len = str.length
    var objP = null;
    if (obj.hasClass("categorySelectList")) {
        objP = obj;
    } else {
        if(obj){
            // obj.html("")
            obj.nextAll().remove();
        }
        objP = obj.parent(".categorySelectList")
    }
    if(seleted == 1){
        var PrentID = 0;
        for(var i = 0 ; i < categories.length ; i++){
            var itemId = categories[i]['id'];
            if(itemId == pid){
                PrentID = categories[i]['parent'];
            }
        }
        for(var i = 0 ; i < categories.length ; i++){
            var itemId = categories[i]['id'];
            var itemName = categories[i]['name'];
            var itemPid = categories[i]['parent'];
            if(!itemPid){ // 一级
                var indexName = ['构成商品的原辅材料', '外购成品', '商品的包装物'].indexOf(itemName);
                if(!itemPid && indexName== -1 || ((origin == 3 ||origin == 4 ||origin == 5) &&  indexName> -1 )){
                    if(itemPid == PrentID){
                        if(itemId == pid){
                            str += "<option selected value='"+ categories[i]['id'] +"'>"+ categories[i]['name'] +"</option>"
                        }else{
                            str += "<option value='"+ itemId +"'>"+ categories[i]['name'] +"</option>"
                        }
                    }
                }
            }else {
                if(itemPid == PrentID){
                    if(itemId == pid){
                        str += "<option selected value='"+ categories[i]['id'] +"'>"+ categories[i]['name'] +"</option>"
                    }else{
                        str += "<option value='"+ itemId +"'>"+ categories[i]['name'] +"</option>"
                    }
                }
            }
        }

        if(str.length > len){
            if(!PrentID){
                objP.children("select").remove();
            }
            objP.append("<select class='category' onchange='setCat($(this).val(), $(this))'>"+ str +"</select>");
        }
        return false;
    }
    var selectLen = objP.find(".category").length ;
    if(!pid && selectLen<2){ // 一级
        for(var i = 0 ; i < categories.length ; i++){
            var pCat = categories[i]['parent'];
            var name = categories[i]['name'];
            var indexName = ['构成商品的原辅材料', '外购成品', '商品的包装物'].indexOf(name);
            if(!pCat && indexName== -1){
                str += "<option value='"+ categories[i]['id'] +"'>"+ categories[i]['name'] +"</option>";
            }
        }
        obj.html(str);
    }else{ // 非一级
        for(var i = 0 ; i < categories.length ; i++){
            var pCat = categories[i]['parent'];
            if(pCat == pid){
                str += "<option value='"+ categories[i]['id'] +"'>"+ categories[i]['name'] +"</option>"
            }
        }
        if(str.length > len){
            objP.append("<select class='category' onchange='setCat($(this).val(), $(this))'>"+ str +"</select>")
        }
    }

}
// creator:hxz 2020-04-09 创建种类树
function mtCategoriesTree(){
    var catArr = mtCategories ;
    $("#catTree").html("");
    for(var i = 0 ; i <catArr.length ; i++){
        var id = catArr[i]['id']
        var pid = catArr[i]['parent']
        if(pid){
            var str = "<span class='treeItem treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").find(".treeItem"+pid).append(str);

        }else{ // 一级类别
            var str = "<span class='treeItem treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").append(str);
        }
    }
}

// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectID) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    if(selectID == item['id']){
                        str += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        str += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }
                }
            }
            obj.html(str);
        }
    })
}
// creator: 李玉婷，2021-07-21 08:23:44，返回列表
function getUnitListData(module) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    var list = [];
    $.ajax({
        "async": false,
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            list = res['list'] ;
        }
    })
    return list;
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit(addUnitNum) {
    $("#addUnit").data("type", 1);
    $("#addUnit").data('num', addUnitNum)
    bounce_Fixed.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed.cancel();
                var cur = $("#addUnit").data("type");
                if (cur == 1) {
                    getUnitList($("#unitSelect"), module);
                } else if (cur == 2) {
                    $(".mainCon3 .importCon2 tbody select").each(function(){
                        let option = `<option value="${res.id}">${res.name}</option>`;
                        $(this).append(option);
                    });
                } else if (cur == 3) {
                    $(".mainCon3 .importCon1 tbody select").each(function(){
                        var val = $(this).val();
                        getUnitList($(this), module, val);
                    });

                }
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed.show($("#tip1"));
            }
        }
    })
}
// creator: 李玉婷，2020-09-03 17:12:49，导入验证
function matImportOk(type) {
    if(type === 'cancel'){
        let fileUid = $('#leading .fileFullName').data('fileid');
        let op = {"type":'fileId', 'fileId':fileUid}
        fileDelAjax(op);
        bounce_Fixed.cancel()
    }else{
        if ($(".matListUpload").length <= 0) {
            $("#knowTip .knowWord").html('您需选择一个文件后才能“导入”！');
            bounce_Fixed2.show($("#knowTip"))
        } else {
            loading.open() ;
            $(".matListUpload a").click();
        }
    }
}
// creator: 李玉婷，2020-10-12 11:23:38，批量导入-判断是否有未完成的导入材料
function leadingShow() {
    var type = $("#importBtn").data("type");
    $("#importEnteryType").data("type",type);
    $.ajax({
        url:"../mtImport/whetherUnfinishedImport.do",
        data:{
            "isPurchased": type
        },
        success:function (data) {
            var isHav = data.status;
            if (isHav == '1') { // 没有 之前导入的
                mtEntryImportBefore()
            } else { // 之前导入未完成的
                $('.unfinishedForm .fa').attr("class","fa fa-circle-o");
                bounce_Fixed.show($('#importNotCompleted'));
            }
        }
    });
}
// creator: hxz，2023-12-04 15:35:58 导入前 选择种类
function mtEntryImportBefore() {
    bounce.show($("#mtEntryImportBefore"))
    setCat("", $("#catImport"));
}
function mtEntryImportBeforeOk() {
    let catName = ``, catID = ''
    $("#mtEntryImportBefore .categorySelectList select").each(function () {
        let name = $(this).find('option:selected').html()
        catName += `${name}/`
        catID = $(this).val()
    })
    catName = catName.substr(0, catName.length-1)
    console.log('catName=', catName)
    console.log('catID=', catID)
    if(catID === ''){
        layer.msg('请先选择分类！')
        return false
    }
    $("#curCatexp").html(catName)
    $("#mtEntryImportBefore").data('name', catName).data('id', catID)
    var type = $("#importBtn").data("type");
    $('#select_btn_1').val("");
    $('.matListUpload').remove();
    $("#leading").data("type", type);
    $('#leading .fileFullName').html("尚未选择文件");
    if (type == '1') {
        $("#leading #mould1").attr("href", "../assets/oralResource/template/material_blank_sheet1.xls");
    } else if (type == '0'){
        $("#leading #mould1").attr("href", "../assets/oralResource/template/material_blank_sheet.xls");
    }
    bounce_Fixed.show($('#leading'));
}

// creator: 李玉婷，2020-10-13 15:35:58，下一步判断
function allImportMtEnter() {
    var sum = $(".mainCon3 .importCon1 tbody tr").length;
    var list = [];
    let isOk = true
    $(".mainCon3 .importCon1 tbody tr").each(function(){
        var item = $(this).data("info");

        if ($(this).find("select").val() !== '') {
            item.unitId = $(this).find("select").val();
            item.unit = $(this).find("select").find("option:selected").text();
        }
        list.push(item);
    });
    if(!isOk){
        layer.msg('请将必填项补充完整！')
        return false
    }
    list = JSON.stringify(list);
    $.ajax({
        url:"../mtImport/allImportMtEnter.do",
        data:{
            "mtBasesList": list,
            "importSum": sum
        },
        success:function (data) {
            var errNum = data.falseImportSum;
            $("#importListTj #errNum").html(errNum);
            bounce_Fixed.show($("#importListTj"));
        }
    })
}
// creator: 李玉婷，2020-10-14 15:54:23，下一步确定
function importListTjSure() {
    var list = [];
    var trueList = $(".importCon1").data("trueUserList");
    $(".importCon1 table tbody tr").each(function(){
        var item = $(this).data("info");
        if ($(this).find("select").val() != "") {
            item.unitId = $(this).find("select").val();
            item.unit = $(this).find("select").find("option:selected").text();
        }
        list.push(item);
    });
    for (var i in trueList) {
        list.push(trueList[i]);
    }
    var sum = list.length;
    var isP = $("#importEnteryType").data("type");
    list = JSON.stringify(list);
    var json = {
        "mtBasesList": list,
        "isPurchased": isP,
        "importSum": sum
    };
    saveImportMtList(json);
}
// creator: 李玉婷，2020-10-22 17:21:52，导入的材料数据都正确
function saveImportMtList(mtData) {
    let catID = $("#mtEntryImportBefore").data('id')
    mtData.categoryId = catID
    $.ajax({
        url: "../mtImport/saveImportMt.do",
        data: mtData,
        success: function (data) {
            $(".mainCon3").show().siblings().hide();
            $(".mainCon3 .importCon2").show().siblings().hide();
            bounce_Fixed.cancel();
            getImportLastList(data);
        }
    });
}
// creator: 李玉婷，2020-10-22 21:24:16，输出数据（有计量单位页面）
function getImportLastList(data) {
    var importList = data["mtBaseList"];
    $(".mainCon3 .importCon2 .initAll").html(data.importSum);
    $(".mainCon3 .importCon2 .inabledSum").html(Number(data.tureImportSum));
    var str = "";
    var buttonState = data["buttonState"];
    $(".matCategory .category:gt(0)").remove();
    setCat( '', $(".matCategory #matCategorySelect"));
    var unitList = getUnitListData(7);
    if (importList && importList.length > 0) {
        for (var i = 0; i < importList.length; i++) {
            var option = '<option value="">- - 请选择 - - </option>';
            if(unitList && unitList.length >0){
                for(var y = 0 ; y < unitList.length ; y++){
                    var item = unitList[y];
                    if(importList[i].unitId == item['id']){
                        option += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        option += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }
                }
            }
            let iinfo = importList[i]
            let expInfo = formatExp(iinfo)
            str +=
                '<tr data-info=\''+ JSON.stringify(importList[i]) +'\'>' +
                '    <td class="sign" data-name="name">'+ handleNull(importList[i].name) +'</td>' +
                '    <td class="sign" data-name="code">'+ handleNull(importList[i].code) +'</td>' +
                '    <td class="sign" data-name="model">'+ handleNull(importList[i].model) +'</td>' +
                '    <td class="sign" data-name="specifications">'+ handleNull(importList[i].specifications) +'</td>' +
                '    <td class="sign ty-td-control expInfo" onclick="editExpImport($(this),3)"><span class="ty-color-blue lenBtn" title="'+ expInfo +'">'+ expInfo +'</span><span class="hd">'+ JSON.stringify(iinfo) +'</span></td>' +
                '    <td>' +
                '        <select type="text" name="unitId" require onchange="importMtUnitChange($(this))">'+ option +'</select>' +
                '    </td>' +
                '    <td class="sign" data-name="memo">'+ handleNull(importList[i].memo) +'</td>' +
                '    <td>' +
                '        <span class="ty-color-blue btn" data-name="update">修改</span>' +
                '        <span class="ty-color-red btn" data-name="del">删除</span>' +
                '    </td>' +
                '</tr>';
        }
    }
    $(".mainCon3 .importCon2 tbody").html(str);
    if (buttonState == 1) {
        $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImport()");
    } else {
        $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
    }
}
// creator: 李玉婷，2020-10-29 10:56:48，批量导入-新增计量单位
function addImportUnit() {
    $("#addUnit").data("type", 2);
    bounce_Fixed.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: 李玉婷，2021-7-16 10:56:48，批量导入-新增计量单位
function addUnitCommom(num) {
    $("#addUnit").data("type", num);
    bounce_Fixed.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: 李玉婷，2020-10-23 18:54:02，改变导入计量单位
var editUnitObj = null;
function importMtUnitChange(obj) {
    editUnitObj = obj;
    var val = obj.val();
    var info = obj.parents("tr").data("info");
    info.unitId = val;
    updateMtInfo(info, 2);
}
// creator: 李玉婷，2020-10-22 15:36:26，未完成批量导入列表获取
function getUnFinishImportList(type){
    $.ajax({
        url: "../mtImport/unfinishedImportMt.do",
        data: {"isPurchased": type},
        success: function (data) {
            $("#curCatexp").html(data.category)
            $("#mtEntryImportBefore").data('name', data.category)
            getImportLastList(data);
        }
    });
}
// creator: 李玉婷，2020-10-14 14:32:48，放弃
function turnCancel(importType, source){
    $.ajax({
        url: "../mtImport/finishImportMt.do",
        data: {
            "isPurchased": importType,
            "type": 0
        },
        success: function (data) {
            var state = data.status;
            if (state == '1') {
                bounce_Fixed.cancel();
                $(".mainCon1").show().siblings().hide();
                if (source == '1') {
                    $('#select_btn_1').val("");
                    $('.matListUpload').remove();
                    $("#leading").data("type", importType);
                    $('#leading .fileFullName').html("尚未选择文件");
                    if (importType == '1') {
                        $("#leading #mould1").attr("href", "../assets/oralResource/template/material_blank_sheet1.xls");
                    } else if (importType == '0'){
                        $("#leading #mould1").attr("href", "../assets/oralResource/template/material_blank_sheet.xls");
                    }
                    bounce_Fixed.show($('#leading'));
                }else if (source == '2') {
                    if (importType == 1){
                        $('.addMt li').eq(0).click();
                    } else {
                        $('.addMt li').eq(1).click();
                    }
                }
            } else {
                layer.msg("操作失败！")
            }
        }
    })
}
// creator: 李玉婷，2020-10-23 16:26:57，修改导入的材料
function updateMtInfo(json, editType) {
    var isPurchased = $("#importEnteryType").data("type");
    $.ajax({
        url: "../mtImport/updateImportMt.do",
        data: {
            "id": json.id,
            "code": json.code,
            "name": json.name,
            "specifications": json.specifications,
            "model": json.model,
            "memo": json.memo,
            "unitId": json.unitId,
            "isPurchased": isPurchased
        },
        success: function (data) {
            var status = data.status;
            if (status == '1') {
                var buttonState = data["buttonState"];
                if (editType == '1') {
                    var obj = $("#updateImportMt").data("obj");
                    var trObj = obj.parents("tr");
                    var trData = trObj.data("info");
                    bounce_Fixed.cancel();
                    trObj.find(".sign").each(function () {
                        var name = $(this).data("name");
                        $(this).html($("#updateImportMt input[name=" +name +"]").val());
                    });
                    $("#updateImportMt [require]").each(function () {
                        var key = $(this).attr("name");
                        var val = $(this).val();
                        trData[key] = val;
                    });
                    trObj.data("info", trData);
                } else {
                    editUnitObj.parents("tr").data("info", json);
                }
                if (buttonState == 1) {
                    $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImport()");
                } else {
                    $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
                }
            } else if (status == '-1') {
                var tip = '<p>您录入的材料代号与公司'+ data.name +'材料代号相同。</p>' +
                    '<p>请确认！</p>';
                $("#iknowTip .iknowWord").html(tip);
                bounce_Fixed2.show($("#iknowTip"));
            } else {
                layer.msg('修改失败！');
            }
        }
    });
}
function updateMtInfo3(json) {
    $.ajax({
        url: "../mtImport/updateImportMt.do",
        data: {
            "id": json.id,
            "expRequired": json.expRequired,
            "openDuration": json.openDuration,
            "relatedItem": json.relatedItem,
            "sameExpiration": json.sameExpiration,
            "expirationDays": json.expirationDays

        },
        success: function (res) {
            console.log(res)
            bounce_Fixed.cancel()
            let editObj = $("#updateImportMt").data("obj");
            let expInfoHdObj = editObj.parents('tr').find('.expInfo').find('.hd')
            let iinfoww = expInfoHdObj.html()
            iinfoww = JSON.parse(iinfoww)
            iinfoww.expRequired = json.expRequired
            iinfoww.openDuration = json.openDuration
            iinfoww.relatedItem = json.relatedItem
            iinfoww.sameExpiration = json.sameExpiration
            iinfoww.expirationDays = json.expirationDays

            expInfoHdObj.html(JSON.stringify(iinfoww))
            let str = formatExp(json)
            editObj.find('.lenBtn').html(str).attr('title', str)

        }
    });
}
function formatExp(iinfo) {
    let expInfo = ''
    if(iinfo.expRequired == 1){
        if(iinfo.relatedItem == 1){
            expInfo = '需录入“可使用的截止日期”'
        }else if(iinfo.relatedItem == 2){
            if(iinfo.sameExpiration == 1){
                expInfo = `需录入生产日期，本材料各供应商保质期均为生产日期后${ iinfo.expirationDays }天`
            }else {
                expInfo = `需录入生产日期，本材料各供应商对保质期的要求不同`
            }
        }

    }else if(iinfo.expRequired == 0){
        expInfo = '无要求'
    }else{
        expInfo = '尚未编辑'
    }
    return expInfo
}
// creator: 李玉婷，2020-10-22 14:46:25，删除导入的材料
function importMtDel() {
    var type = $("#importMtDel").data("type");
    var curObj = $("#importMtDel").data("obj");
    bounce_Fixed.cancel();
    if (type == '1') {//initWrong
        curObj.parents("tr").remove();
        var len = $(".mainCon3 .importCon1 tbody tr").length;
        $(".mainCon3 .importCon1 .initWrong").html(len);
    } else {
        var info = curObj.parents("tr").data("info");
        var isPurchased = $("#importEnteryType").data("type");
        $.ajax({
            url:"../mtImport/deleteImportMt.do",
            data:{ "id": info.id , "isPurchased":isPurchased },
            success:function (data) {
                var status = data["status"];
                var buttonState = data["buttonState"];
                if( status == 1 ){
                    layer.msg("操作成功！");
                    curObj.parents("tr").remove();
                    var len = $(".mainCon3 .importCon2 tbody tr").length;
                    $(".mainCon3 .importCon2 .inabledSum").html(len);
                    if (buttonState == 1) {
                        $("#save").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "saveImport()");
                    } else {
                        $("#save").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
                    }
                }else{
                    layer.msg("编辑失败，请稍后重试！");
                }
            }
        })
    }
}
// creator: 李玉婷，2020-11-11 13:57:32，保存
function saveImport(){
    let empty = 0;

    $(".importCon2 .expInfo").each(function () {
        let spanObj = $(this).children(":eq(0)")
        let txtSpan = spanObj.html()
        if(txtSpan === '尚未编辑'){
            empty++;
        }

    })

    if (empty > 0) {
        var str = '还有必填项尚未填写！';
        $("#iknowTip .iknowWord").html(str);
        bounce_Fixed2.show($("#iknowTip"));
        return false;
    }

    var isP = $("#importEnteryType").data("type");
    $.ajax({
        "url":"../mtImport/finishImportMtEnter.do",
        "data":{ 'isPurchased': isP},
        success:function(res) {
            var tip = "";
            if (isP == '1') {
                tip = "库管员将收到<span class='red'>须填写</span>初始库存的提示。";
            } else if (isP == '0') {
                tip = "库管员<span class='red'>无法填写</span>这些材料的当前库存数量。";
            }
            $("#importListSave .saveTip").html(tip);
            bounce_Fixed.show($("#importListSave"));
        }
    })
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
        layer.msg("字数不能超过" + max + "个！");
    }
    let letip = obj.siblings(".textMax")
    if(letip.length == 0){
        letip = obj.siblings(".textMax2")
    }
    letip.text(curLength + '/' + max);
    letip.text(curLength + '/' + max);
}

// 编辑分类
function saveKindEdit(){
    var id = $("#kindEditID").val();
    var name = $("#kindEditName").val();
    bounce.cancel();
    $.ajax({
        url:"../material/updateCategory.do",
        data:{ "id": id , "name":name },
        success:function (data) {
            var status = data["status"];
            if( status == 1 ){
                layer.msg("操作成功！");
                var kindInfo = data["kindInfo"];
                editObj.parent().siblings(".name").html(name);
                // 修改列表
                for(let c of mtCategories){
                    if(c.id == id) {
                        c.name = name;
                    }
                }

            }else{
                layer.msg("编辑失败，请稍后重试！");
            }
        }
    })
}

// 确定删除分类
function okDeleteKind(){
    bounce.cancel();
    var id = $("#delCatTip").data("type");
    $.ajax({
        url:"../material/deleteCategory.do",
        data:{ "id": id  },
        success:function (data) {
            var status = data["status"];
            if( status == 1 ){
                layer.msg("删除成功！");
                editObj.parent().parent().remove();
                mtCategories.splice(mtCategories.findIndex(item => item.id === id), 1);

            }else if( status == 2 ){
                layer.msg("当前分类下有子分类，不可以删除！");
            }else if( status == 3 ){
                layer.msg("当前分类的子分类下有物料，不可以删除！");
            }else if( status == 4 ){
                layer.msg("当前分类下有物料，不可以删除！");
            }
        }
    })

}


