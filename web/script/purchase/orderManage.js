var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#goodEntry"));
bounce_Fixed3.show($("#addMemo"));
bounce_Fixed2.cancel();
bounce_Fixed3.cancel();
var uploadCode = "" // 标记每次添加的代号

var mtCategories = null; // 全部种类数据
var listData = { 'category':false , 'state':false , 'keyword':'' } // 列表的传值数据
$(function () {
    setBackBtn(0);
    $(".mainArea").show().siblings().hide();
    // 点击分类获取分类下的物料
    $('#catAll').on('click', 'li', function(){
        var id = $(this).data("id") ;
        getReplenishList(id, listData['keyword'], 1, 20);
    });
    // 点击横向导航栏
    $('.curCat').on('click', '.go2Cat', function(){
        var cat = $(this).find(".hd").html();
        $(this).nextAll().remove();
        $(this).remove();
        getReplenishList(cat, listData['keyword'], 1, 20);
    });
    $(".nextBtn>span").on("click", function () {
        var child = $(this).children(".fa");
        if (child.hasClass("fa-circle-o")){
            child.addClass("fa-circle").removeClass("fa-circle-o");
            child.parents(".nextBtn").siblings("div").find(".fa").addClass("fa-circle-o").removeClass("fa-circle");
        }
    });
    $("#math").keydown(function(){
        $("#pofont").show();
    });
    $(document).on('keydown','#math1',function(){
        $("#pofont1").show();
        $("#cloone1").show();
    })
    $("#cloone").on("click",function(){
        $("#pofont").hide();
    })
    $(document).on('click','#cloone1',function(){
        $("#pofont1").hide();
        $("#math1").val("");
        $("#cloone1").hide();
    })
    $(".setAdvance .fa").on("click", function () {
        var key = $(this).data("type");
        var ynk = $("#needget").data("ron");
        // var bili = $("#needget").data("bili");
        //下面两行42-45行代码是有关按钮关闭不能点击的部分 （关闭按钮）
        // var disabled = $(this).attr("disabled");    //获取$(this)对应的按钮的disabled属性值
        // if(key == 2 && disabled){ //当满足左边条件的时候下面方法不执行
        //     return false;
        // }
        $(this).addClass("fa-dot-circle-o").removeClass("fa-circle-o");
        $(this).parent().siblings("div").find(".fa").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
        if (key == 2){
            $("#timege").val("");
            $("#picege").val("");
            var option = '';
            var num = 0;
            $("#matBuyInfo tbody tr").each(function(){
                    var incoiceable = $(this).data("incoiceable");
                    if (incoiceable == "0") num++;
                });
            // 正确的应该是1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            if (num > 0) { //1=购买不给开发票 2=不含
                option =
                    '<option value="" readonly=""></option>' +
                    '<option value="1">现金</option>' +
                    '<option value="6">非公户银行转账</option>';
            } else {
                option =
                    '<option value="" readonly=""></option>' +
                    '<option value="1">现金</option>' +
                    '<option value="5">银行转账</option>' +
                    '<option value="4">承兑汇票</option>' +
                    '<option value="3">转账支票</option>';
            }
            $(".advanceForm select").html(option);
            $(".advanceMain").show();
            if(ynk != "1"){
                $("#math").val("");
                $("#pofont").hide();
                $("#cloone").hide();
            }
                // if(ynk == "1"){ //预付款为1
                //     // if(bili == "1"){
                //     $("#messge").show();
                //     // }else if(bili == "2"){
                //     //     $("#messge").hide();
                //     //     // $("#pofont").html("");
                //     // }
                // }else{
                //     $("#messge").hide();
                //     // $("#pofont").html("");
                // }
            $(".advanceForm .advanceItem:eq(0)").nextAll().remove();
            $("#cloone").hide();
                // $("#pofont").hide();
            $("#cloone1").hide();
            $("#pofont1").hide();
                // var monny = $("#advanceChange").data("money");
                // var str = `${monny}`;
                // if(str == "undefined" || str == "null"){
                //     $("#math").val("");
                // }else{
                //     $("#math").val(str);
                // }
            var one = $("#math").val();
            if(one.length >0){
                    $("#pofont").show();
                }else{
                    $("#pofont").hide();
                }
            var allchage = $("#advanceCharge").data("total_price");
            var boni = $("#advanceCharge").data("boli");
        }else{
            $(".advanceMain").hide();
        };
    });
    getPurOrdersList("", 1);
    $("#invoiceHandleChoice p").click(function () {
        $("#invoiceHandleChoice .fa").attr("class","fa fa-circle-o");
        $(this).find(".fa").attr("class","fa fa-dot-circle-o");
    })
    $(".inv p").click(function () {
        $(".inv .fa").attr("class","fa fa-circle-o");
        $(".inv .reasonTip").hide();
        $(".inv .numTip").html("0/30")
        $("#reason").val("")
        $(this).find(".fa").attr("class","fa fa-dot-circle-o");
        $(this).find(".reasonTip").show();
    })
//预警材料下单
    $('table').on('click', 'span[type="btn"]', function(){
        let data = JSON.parse($(this).siblings(".hd").html());
        var type = $(this).data('fun');
        if(type == "warningNewOrder") {
            setBackBtn(2);
            data.method = '2';//method，1补货，2是预警的
            $(".repStepTwo").show().siblings().hide();
            $("#matDetails").html(JSON.stringify(data));
            getSupplierList(data.id, $("#matMatchSupplier"), 1);
        } else if(type == "newOrderFromSale") {
            setBackBtn(2);
            data.method = '6';//method，1补货，6是新销售订单
            $(".repStepTwo").show().siblings().hide();
            $("#matDetails").html(JSON.stringify(data));
            getSupplierList(data.id, $("#matMatchSupplier"), 1);
        }
    });

    // sy 进行input输入框清空按钮编写
    $("input").keydown(function(){
        $(this).parent().children(".input_clear").show();
    });
    $("input").keypress(function(){
        if($(this).val()==''){
            $(this).parent().children(".input_clear").hide();
        }
    });
    $(".input_clear").click(function(){
        $(this).parent().find("input").val('');
        $(this).hide();
    });
    $(".conditional").on('click','li', function(){
        if($(this).find(".fa").hasClass("fa-square-o")){
            $(this).find(".fa").attr("class","fa fa-check-square-o")
            $(this).siblings().find(".fa").attr("class","fa fa-square-o")
        }else{
            $(this).find(".fa").attr("class","fa fa-square-o")
        }
    });
    $(document).bind("click",function(e){//点击空白处，设置的弹框消失
        var tag = $(".taxRateList:visible");
        var tag1 = $("#order_taxRate:visible");
        var target = $(e.target);
        if(target.closest(tag).length == 0 && target.closest(tag1).length == 0){
            $(tag).hide();
        }
    });
    $("body").on("click",".funBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
});

function setNumberTip(obj){
    let val = obj.val()
    if(val.length > 30 ){
        val = val.substr(0,30)
    }
    obj.val(val)
    obj.siblings(".numTip").html(val.length + '/30');
}
// creator: hxz，2021-06-17  票款处理
function invoiceHandelBtn(thisObj) {
    //下面的150-153行代码是关于按钮关闭不能点击的部分   （关闭按钮）
    // var disabled = thisObj.attr("disabled");
    // if(disabled){
    //     return false;
    // }
    $("#invoiceHandleChoice").data("handelobj", thisObj);
    $("#invoiceHandleChoice .fa").attr("class","fa fa-circle-o");
    bounce.show($("#invoiceHandleChoice"))
}
// creator: hxz，2021-06-17  票款处理选择种类
function invoiceHandleChoiceOk() {
    let selectObj = $("#invoiceHandleChoice .fa-dot-circle-o")
    if( selectObj.length === 0 ){
        layer.msg("请选择您要办理的事务！");
        return false
    }
    let val = Number( selectObj.data("val") ) ;
    let ttl = "票款处理-仅提交票据"
    if(val === 2){
        ttl = "票款处理-录入票据并付款"
    }else if(val === 3){
        ttl = "票款处理-仅提交付款申请"
    }
    $(".inv").hide()
    $(".inv .reasonTip").hide()
    $(`.inv${val}`).show()
    $("#invoiceHandle .bonceHead span").html(ttl)
    $(".upperKey").html("").attr("title","")
    $(".inv .fa").attr("class","fa fa-circle-o");
    bounce.show($("#invoiceHandle"))
    // ============
    $(".inv1 .ty-color-blue").html(0)
    $("#invoiceList").html("")
    $("#amount2").val("")
    let thisObj = $("#invoiceHandleChoice").data("handelobj"); // 操作的按钮
    let orderID = thisObj.parents("tr").data("orderid") ;
    $.ajax({
        "url":"../po/poDetail",
        "data":{ 'orderId': orderID },
        success:function (res) {
            let data = res.data[0] || {}
            $("#invoiceHandle .orderSn").html(data.sn);
            $("#invoiceHandle .supName").html(data.supplier_name);
            $("#invoiceHandle .orderAmount").html(data.amount);
            $("#invoiceHandle .orderDate").html( new Date(data.create_date).format("yyyy-MM-dd"));
            $("#invoiceHandle .supCode").html(data.code_name);
            $("#invoiceHandle .rateCon>td:nth-child(1)").html(data.checked_amount || 0);
            let checked_percent = ((data.checked_percent || 0) * 100).toFixed(2) + '%'
            $("#invoiceHandle .rateCon>td:nth-child(2)").html(checked_percent);
            $("#invoiceHandle .rateCon>td:nth-child(3)").html(data.stored_amount || 0);
            let stored_percent = ((data.stored_percent || 0) * 100).toFixed(2) + '%'
            $("#invoiceHandle .rateCon>td:nth-child(4)").html(stored_percent);
            $("#invoiceHandle .rateCon>td:nth-child(5)").html(data.invoiced_amount || 0);
            let invoice_percent = ((data.invoice_percent || 0) * 100).toFixed(2) + '%'
            $("#invoiceHandle .rateCon>td:nth-child(6)").html(invoice_percent);
            $("#invoiceHandle .rateCon>td:nth-child(7)").html(data.payed_amount || 0);
            let pay_percent = ((data.pay_percent || 0) * 100).toFixed(2) + '%'
            $("#invoiceHandle .rateCon>td:nth-child(8)").html(pay_percent);
        }
    })
    $("#invoiceHandle").data("orderid", orderID).data("handlenum", val);
    if(val === 2 || val === 1){
        setEveryTime(bounce, 'reimburse')
        getFeeCats()
        getBillCats(val)
        initUpload();
        getMts()
        $("#reimburse .billCountAmount").html(0);
        $("#reimburse .countAmount").html(0);
    }
}
var mtsListStr = ''; //  货物列表 的字串
// creator: hxz 2021-07-02 获取货物列表
function getMts(){
    let orderId = $("#invoiceHandle").data("orderid")
    $.ajax({
        url: '../po/orderDetail',
        data: {
            "orderId": orderId
        },
        success: function (res) {
            var list = res.ordersItem || [] ;
            var html = `<option data-val=''${ JSON.stringify({}) }' value=''>请选择</option>`, amount = 0;
            list.forEach(function (item) {
                html += `<option data-val='${ JSON.stringify(item) }' value="${ item.item_id }">${ item.name  }</option>`
            })
            mtsListStr = html ;
            $("#itemName").html(html);
        }
    });
}
// creator: hxz 2021-07-02 根据获取 设置默认的 价格
function setMtPrice( thisObj ){
    let selectOption = $("#itemName").children("option:selected") ;
    if(selectOption.length > 0){
        let item = selectOption.data("val") ;
        console.log(item)
        var billCatName = $("#billCat").find("option:selected").html();
        if(billCatName === '增值税专用发票'){
            let is_tax = item.is_tax ;
            let rate = item.tax_rate/100
            let uniPrice = item.unit_price ;
            let unitPriceShow = uniPrice
            if(Number(is_tax) === 1){ // 含税
                unitPriceShow = uniPrice / (1 + rate)
            }else { // 不含税
            }
            $("#model").val( item.model);
            $("#unit").val( item.unit);
            $("#uniPrice").val( parseFloat(unitPriceShow.toFixed(4)) );
            $("#taxRate").val( item.tax_rate);
            let num = $("#itemQuantity").val() ;
            if(Number(num) > 0){
                let price = unitPriceShow * num ;
                let taxAmount = price * rate ;
                let amount = price + taxAmount ;
                $("#price").val(price.toFixed(2));
                $("#taxAmount").val(taxAmount.toFixed(2));
                $("#amount").val(amount.toFixed(2));
            }else{
                $("#price").val("");
                $("#taxAmount").val("");
                $("#amount").val("");
            }
        }
    }
}

//  create: hxz 2021-05-11 保留四位小数
function tofixed4(obj, len) {
    len = len || 4
    let v = obj.value
    v = v.replace(/[^\d.]/g, "");
    v = v.replace(/^\./g, "");
    v = v.replace(/\.{2,}/g, ".");
    v = v.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".")
    let vArr = v.split('.')
    if(vArr[0].length > 9){ // 最多9位
        vArr[0] = String(vArr[0]).substr(0,9);
    }
    v = vArr[0]
    if(vArr.length > 1){
        if(vArr[1].length > len){
            vArr[1] = vArr[1].substr(0,len);
        }
        v += '.' + vArr[1];
    }
    obj.value = v
}
// creator: hxz，2021-06-17  票款处理 确定
function invoiceHandleOk() {
    let orderAmount = Number($(".orderAmount").html());
    if(orderAmount === 0){
        layer.msg('该笔订单无需付款，不能进行票款处理！')
        return false
    }
    let data = {};
    let handlenum = Number($("#invoiceHandle").data("handlenum"));
    data.type = handlenum ;
    data.orders = $("#invoiceHandle").data("orderid");
    let amount2 = $("#amount2").val();
    if(handlenum != 1 &&amount2.length == 0){
        layer.msg("请输入此次申请付款的金额！");
        return false
    }
    if(handlenum === 2 || handlenum === 3){
        let reasonObj = $("#reasonCon .fa-dot-circle-o") ;
        if( reasonObj.length === 0 ){
            layer.msg("请选择提交此次付款申请的原因！");
            return false
        }
        data.applicationReason = reasonObj.data("val");
        if(data.applicationReason === 2){
            data.applicationDesc = $("#reason").val()
            if(data.applicationDesc.length === 0){
                layer.msg("请录入不符合合同约定的具体原因！");
                return false
            }
        }

    }
    data.purchaseInvoice = [] ;
    if(handlenum !== 3){
        let len =  $("#reimburse tbody tr").length ;
        if(len == 0){
            layer.msg("请录入票据！");
            return false
        }
        data.amountTotal = $("#reimburse .countAmount").html();
        if(handlenum === 2 ){
            data.amountTotal = $("#amount2").val();
        }
        data.billAmountTotal = $("#reimburse .billCountAmount").html();
        $("#reimburse tbody tr").each(function () {
            let pInvoiceItem = {} // 每行
            var commonBillItem = $(this).children('.billInfo').html()
            commonBillItem = JSON.parse(commonBillItem)
            let billCatName = commonBillItem.billCatName ;
            if(billCatName == "增值税专用发票"){
                pInvoiceItem.invoiceCategory = 1
            } else if(billCatName == "增值税普通发票"){
                pInvoiceItem.invoiceCategory = 2
            } else if(billCatName == "收据"){
                pInvoiceItem.invoiceCategory = 3
            }
            pInvoiceItem.poPaymentInvoices = []; // 票据详情
            let bills = commonBillItem.bills
            bills.forEach(function(bill){
                let path = bill.imgPaths[0]
                // bill.imgPaths[0] = path ? path.split('upload/')[1] : ''
                let fileArr = bill.fileArr
                delete bill.fileArr
                let po = {
                    // 'amount': bill.amount,
                    'amount': bill.billAmount,
                    'billAmount': bill.billAmount,
                    'billNos': bill.billNos,
                    'imgPaths': bill.imgPaths,
                    'relativeBillQuantity': bill.number,
                    'itemCount': commonBillItem.itemCount || 0,
                    'taxAmount': "",
                    "paymentInvoiceItems" : []
                }
                let billItems = bill.billItems
                let taxAmount = 0 ;
                billItems.forEach(function(billItem){
                    taxAmount += billItem.taxAmount ? Number(billItem.taxAmount) : 0 ;
                    po.paymentInvoiceItems.push({
                        'ordersItem': billItem.ordersItem , // 订单明细项ID
                        'itemName': billItem.itemName , // 明细项名称(货物或应税劳务、服务名称)
                        'model': billItem.model , // 规格型号
                        'itemQuantity': billItem.itemQuantity , // 数量
                        'unit': billItem.unit , // 单位
                        'unitPrice': billItem.uniPrice, // 单价
                        'price': billItem.price , // 价格金额
                        'taxRate': billItem.taxRate , // 税率
                        'taxAmount': billItem.taxAmount , // 税额
                        'amount': billItem.amount , // 税额
                    })
                })
                taxAmount = taxAmount ? taxAmount : ''
                po.taxAmount = taxAmount
                pInvoiceItem.poPaymentInvoices.push(po)
            })
            data.purchaseInvoice.push(pInvoiceItem)
        })
    }else{
        data.amountTotal = $("#amount2").val();
    }

    data.purchaseInvoice = JSON.stringify(data.purchaseInvoice);
    $.ajax({
        "url":"../purchaseInvoice/purchaseInvoiceApply.do",
        "data": data,
        success:function (res) {
            let content = res.data.content
            layer.msg(content)
            bounce.cancel()
            if ( content === '操作成功') {
                $("#reimburse").find(".billInfo").each(function() {
                    let data = JSON.parse($(this).html())
                    let bills = data.bills
                    bills.forEach(function(bill){
                        let fileArr = bill.fileArr
                        if(fileArr.length > 0){
                            let fileid = fileArr[0]
                            let op = {'type':'fileId', 'fileId':fileid }
                            cancelFileDel(op);
                        }
                    })
                })
            }
        }
    })
}

// creator: 李玉婷，2020-04-29 18:10:44，采购订单管理获取列表
function getPurOrdersList(keyword, cur){
    setBackBtn(0);
    $("#ordNum").html("0");
    $("#addOrderKind").val("");
    $("#phAllOrders tbody").html("");
    let options = ``;
    var oid = sphdSocket.user.oid;
    var data = {"oid":oid , "keyword":keyword, "pageNum": cur , "per": 20 };
    $.ajax({
        "url":"../po/list",
        "data":data,
        success:function (res) {
            var list = res.data;
            //设置分页
            var totalPage = res["totalCount"];//总页数
            var jsonStr = JSON.stringify({"keyword": keyword}) ;
            setPage( $("#ye_orderManage") , cur ,  totalPage , "orderManagePur", jsonStr );

            $("#ordNum").html(res.totalRows);
            if (list && list.length > 0){
                var html = "";
                for(var i=0; i<list.length; i++){
                    var checked = Number((list[i]["checked_quantity"] && list[i]["checked_quantity"] ) || "0") ;
                    var stored = Number( (list[i]["stored_quantity"] && list[i]["stored_quantity"] ) || "0" ) ;
                    var invoiced = Number( (list[i]["invoiced_quantity"] && list[i]["invoiced_quantity"] ) || "0" ) ;
                    var payed = Number( (list[i]["payed_quantity"] && list[i]["payed_quantity"] ) || "0" ) ;
                    if(checked > 100 ){ checked = 100 ; }
                    if(stored > 100 ){ stored = 100 ; }
                    if(invoiced > 100 ){ invoiced = 100 ; }
                    if(payed > 100 ){ payed = 100 ; }
                    if(checked == 0){ checked = "--"   ;   } else {checked = checked.toFixed(2) +'%'}
                    if(stored == 0){ stored = "--"   ;   } else {stored = stored.toFixed(2) +'%'}
                    if(invoiced == 0){ invoiced = "--"   ;   } else {invoiced = invoiced.toFixed(2) +'%'}
                    if(payed == 0){ payed = "--"   ;   } else {payed = payed.toFixed(2) +'%'}
                    html +=
                        '<tr data-orderid="'+ list[i].id +'">' +
                        '    <td>'+ handleNull(list[i].sn) +'</td>' +
                        '    <td>'+ handleNull(list[i]).supplier_name +'</td>' +
                        '    <td>'+ handleNull(list[i].create_name) +'&nbsp;&nbsp;' + new Date(list[i].create_date).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '    <td class="ty-td-control">' +
                        '        <span class="ty-color-blue" onclick="orderDetails($(this))">订单详情</span>' +
                        '        <span class="ty-color-blue" onclick="orderUpdate($(this))">订单修改</span>' +
                        '        <span class="ty-color-red">订单终止</span>' +
                       // '        <span class="ty-color-blue" onclick="invoiceHandelBtn($(this))" disabled id="pkcl" style="color: grey">票款处理</span>' +  //左侧disabled位置开始到grey的部分的有关按钮关闭的部分 关闭按钮
                        '        <span class="ty-color-blue" onclick="invoiceHandelBtn($(this))">票款处理</span>' +
                        '    </td>' +
                        '    <td>'+ checked +'</td>' +
                        '    <td>'+ stored +'</td>' +
                        '    <td>'+ invoiced +'</td>' +
                        '    <td>'+ payed +'</td>' +
                        '</tr>';
                }
                $("#phAllOrders tbody").html(html);
            }
            options = `<option value="">请选择</option>
                       <option value="1">新的销售订单所需的采购（${res.ordersPoNum}）</option>
                       <option value="2">库存预警的材料(${res.warningNum})</option>
                       <option disabled value="3">个人申购的新材料（0）</option>
                       <option value="4">补货</option>
                       <option disabled value="5">零星采购</option>`;
            $("#addOrderKind").html(options);
        }
    })
}
// creator: 李玉婷，2020-10-10 10:17:32，采购订单管理搜索
function orderSearchKey() {
    var key = $("#orderSearchKey").val();
    getPurOrdersList(key, 1);
}
// creator: 李玉婷，2020-04-29 18:32:08，补货列表获取
function getReplenishList(category, keyword, cur  , per){
    // state:查询总列表时不传,  1-查询待确认定点信息时, 2-查询暂停采购的材料时, 3-查询待录入初始库存数量时
    // keyword : 查询的代号或名称
    $("#replenishList tbody").html("");
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "keyword":keyword,"state": "4", "pageNum": cur,"per": per };
    if (category){
        data['category'] =  category
    } else {
        $(".curCat").html("");
    }
    if (keyword != ""){  data['type'] =  1  }
    $.ajax({
        "url":"../mt/list",
        "data":data,
        success:function (res) {//设置分页
            var totalPage = res["totalCount"];//总页数
            var curr = cur ;//当前页
            var jsonStr = JSON.stringify(data) ;
            setPage( $("#replenish_ye1") , curr ,  totalPage , "replenishIndex", jsonStr );

            //var totalCount = 0;
            var categories = res['categories'];
            var mtArr = res['data'];
            var enabledCount = res['enabledCount'];
            var pid = null;
            var path = "<span class='go2Cat'><span>全部 </span><span class='hd'></span></span>";
            if(category){  pid = category;  }
            mtCategories = categories; mtCategoriesTree();
            $("#catAll").html("");
            for(var i = 0 ; i < categories.length ; i++){
                var pCat = categories[i]['parent'];
                if(pCat == pid){
                    var count = 0 ;
                    $("#catTree").find(".treeItem"+ categories[i]['id']).find(".countItem").each(function(){
                        count += Number($(this).html());
                    });
                    categories[i]['count'] = count ;
                    var str = "<li class='catID"+ categories[i]['id'] + "' data-id='"+ categories[i]['id'] +"'><span class='name'>"+ categories[i]['name'] +"</span><span class=\"catNum\">"+ categories[i]['count'] +"</span></li>"
                    $("#catAll").append(str)
                }
                if(category == categories[i]['id']){
                    path = "<span class='go2Cat'><span> > "+ categories[i]['name'] +"</span><span class='hd'>"+ categories[i]['id']  +"</span></span>"
                }
            }
            var allCount = 0
            $("#catTree").find(".countItem").each(function(){
                allCount += Number($(this).html());
            });
            $("#catTotal").html(allCount);
            var strMt = "" ;
            if(mtArr && mtArr.length >0){
                for(var j = 0 ; j < mtArr.length ; j++){
                    var item = mtArr[j];
                    strMt += " <tr>" +
                        "    <td class=\"br-empty\" onclick='selectReplenishMat($(this))'><span class=\"fa fa-circle-o\"></span><span class=\"hd\">"+ JSON.stringify(item) +"</span></td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ handleNull(item['unit']) +"</td>" +
                        "    <td>"+ parseFloat(Number(item['current_stock'] || "0").toFixed(4)) +"</td>" +
                        "    <td>"+ parseFloat(Number(item['minimum_stock'] || "0").toFixed(4)) +"</td>" +
                        "    <td class='ty-td-control' onclick='getInTransit($(this))'><span class='ty-color-blue'>"+ (item['way_num'] || "0") +"</span></td>" +
                        "    <td>"+ (item['location_number'] || "0") +"个</td>" +
                        "    <td class='ty-td-control'><span data-type='sup'>"+ (item['supplier_number'] || "0") +"个</span></td>" +
                        "</tr>";
                }
            }
            if (!keyword || keyword == '') {
                $(".curCat").append(path);
            }
            $("#replenishList tbody").html(strMt);

        }
    })
}
// creator: 李玉婷，2020-10-10 10:37:33，采购订单管理搜索
function mtSearchSure() {
    var key = $("#mtSearch").val();
    var catId = $(".curCat .go2Cat:last").find(".hd").html();
    getReplenishList(catId, key, 1, 20);
}
// creator:hxz 2020-04-09 创建种类树
function mtCategoriesTree(){
    var catArr = mtCategories ;
    $("#catTree").html("");
    for(var i = 0 ; i <catArr.length ; i++){
        var id = catArr[i]['id']
        var pid = catArr[i]['parent']
        if(pid){
            var str = "<span class='treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").find(".treeItem"+pid).append(str);

        }else{ // 一级类别
            var str = "<span class='treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").append(str);
        }
    }
}
// creator: 李玉婷，2020-05-07 09:10:11，选择要补货的材料
var replenishMat = {};
function selectReplenishMat(obj) {
    var details = JSON.parse(obj.find(".hd").html());
    replenishMat = details;
    setBackBtn(2);
    details.method = '1';//method，1补货，2是预警的
    $("#matMatchSupplier").data("mtId",details.id);
    $("#matDetails").html(JSON.stringify(details));
    $(".repStepTwo").show().siblings().hide();
    getSupplierList(details.id, $("#matMatchSupplier"), 1);
}
// creator: 李玉婷，2020-05-07 14:39:18，获取供应商列表
function getSupplierList(mtId, curObj, type) {
    var oid = sphdSocket.user.oid;
    var data = { 'oid': oid, 'mtId': mtId, 'enabled': 1  };
    $.ajax({
        "url":"../po/suppliers",
        "data":data,
        success:function (res) {
            var list = res['data'];
            $("#makesure").data("mess",list);
            let tabs = ``;
            if(list){
                for(var i = 0 ; i <list.length ; i++){
                    var info = list[i];
                    tabs += `<div class="tabs__item" data-type="${type}" onclick="setSupplyInfo($(this))">供应商${(i+1)}<span class="hd">${JSON.stringify(info)}</span></div>`;
                }
            }
            curObj.find(".tabs__nav").html(tabs);
            curObj.find(".tabs__item").eq(0).click();
        }
    })
}
// creator: 李玉婷，2021-08-03 09:14:07，供应商赋值
function setSupplyInfo(obj) {
    let type = obj.data("type");
    let info = JSON.parse(obj.find(".hd").html());
    $("#makesure").data('gys',info);    //尝试
    let str =
        `<div class="supplierInfo"><span class="hd">${JSON.stringify(info)}</span>`;
    var cell1 = "", cell2 = "", cell3 = "", cell4 = "", cell5 = "", cell6 = "", cell7 = "", cell8 = "", cell9 = "";
    obj.addClass("is-active").siblings().removeClass("is-active");
    if (type == 1) {
        str +=
            '   <div class="clear"><div class="ty-left" onclick="getSupplyOrder($(this))">' +
            '       <span class="fa fa-circle-o"></span>' +
            '       <span class="spTl">供应商名称</span>' +
            '       <span class="spCd">'+ info.full_name +'</span>' +
            '   </div>';
    }else {
        str +=
            '   <div class="clear"><div class="ty-left">' +
            '       <span class="spTl">供应商名称</span>' +
            '       <span class="spCd">'+ info.full_name +'</span>' +
            '   </div>';
    }
    str +=
        '   <div class="ty-right">' +
        '       <span class="spTl">简称</span>' +
        '       <span class="spCd">'+ info.supplier_name +'</span>' +
        '       <span class="spTl">代号</span>' +
        '       <span>'+ info.code_name +'</span>' +
        '   </div>' +
        '</div></div>';
    if(info["has_contact"]){ // 签订采购合同
        cell1 = "采购合同已签订";
        if(info["contract_sn"]){ cell1 += "，合同编号"+ info["contract_sn"]; }
        if(info["valid_date"]){ cell1 += "，有效期至"+ new Date(info["valid_date"]).format('yyyy-MM-dd'); }
        if(info["sign_date"]){ cell1 += "，签署日期为"+ new Date(info["sign_date"]).format('yyyy-MM-dd') ; }
    }else{
        cell1 = "与该供应商暂无采购合同 ";
    }
    if(info["is_include"] === "1"){
        cell2 = "本材料已包含于采购合同中"
    }else if(info["is_include"] === "0"){
        cell2 = "本材料不包含于采购合同中"
    }
    if(info["package_method"] === "1"){
        cell3 += "该供应商供应的本材料包装方式基本固定"
    }else if(info["package_method"] === "2"){
        cell3 += "该供应商供应的本材料包装方式型式不定"
    }else {
        cell3 += "该供应商供应的本材料包装方式暂不清楚"
    }
    if(info["supplier_invoiceable"] == 1){//可否开具发票:0-不能开,1-可开具,2-不确定
        if(info["supplier_vat_payable"] == 1){//可否开具增值税专票:0-不能开,1-可开具,2-不确定
            if(info["supplier_tax_rate"]){
                cell4 = "该供应商能开税率为"+ info["supplier_tax_rate"] +"%的增值税专用发票"
            }else{
                cell4 = "该供应商能开增值税专用发票"
            }
        }else if(info["supplier_vat_payable"] == 2){
            cell4 = "该供应商不确定是否能开增税专用发票"
        }else{
            cell4 = "该供应商仅能开普通发票"
        }
    } else if(info["supplier_invoiceable"] == 2){
        cell4 = "该供应商不确定是否开票"
    }else{
        cell4 = "该供应商不能开发票"
    }
    if(info["charge_acceptable"]){
        cell5 += "可接受挂账";
        if(info["charge_period"]){
            cell5 += ",账期"+ info["charge_period"] +"天";
        }
        if(info["charge_begin"] == "1"){
            cell5 += ",自货物入库之日起计算";
        }else if(info["charge_begin"] == "2"){
            cell5 += ",自发票入账之日起计算";
        }
    }else{
        cell5 += "不接受挂账";
    }
    if(info["supplier_draft_acceptable"]){
        cell6 += "可接收承兑汇票";
    }else{
        cell6 += "不确定能接受承兑汇票";
    }
    if(info["price_stable"] === "1"){
        cell7 += "价格相对稳定";

    }else if(info["price_stable"] === "2"){
        cell7 += "价格变动较频繁";
    }
    if(String(info["material_invoiceable"]) === "1"){
        if(info["material_invoice_category"] === "1"){
            cell8 += "给开税率为"+ info["tax_rate"] +"%的增值税专用发票";
        }else if(info["material_invoice_category"] === "2"){
            cell8 += "给开其他发票";
        }
    }else if(String(info["material_invoiceable"]) === "0"){
        cell8 += "不给开发票";
    }
    if(info["supplier_is_imprest"] === "1"){ // 0-不确定,1-需要,2-不需要
        cell9 += "需要预付款";
    }else if(info["supplier_is_imprest"] === "2"){
        cell9 += "无需预付款";
    }else if(info["supplier_is_imprest"] === "0"){
        cell9 += "不确定是否需预付款";
    }
    var priceInfo = chargePriceShow(info);
    var priceElem = priceInfo.ttl + '&nbsp;&nbsp;' + priceInfo.info;
    str +=
        '<div class="npSupper"><table class="ty-table ty-table-control spTb">' +
        '    <tbody>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="9">'+ priceElem +'</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3">上次购买</td>' +
        '        <td colspan="3">本年购买</td>' +
        '        <td colspan="3">总计购买</td>' +
        '    </tr>' +
        '    <tr class="td-blue">' +
        '        <td>单价</td>' +
        '        <td>数量</td>' +
        '        <td>日期</td>' +
        '        <td>次数</td>' +
        '        <td>数量</td>' +
        '        <td>平均单价</td>' +
        '        <td>次数</td>' +
        '        <td>数量</td>' +
        '        <td>平均单价</td>' +
        '    </tr>' +
        '    <tr class="td-blue">' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td></td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '    </tr>' +
        '    <tr class="td-blue td-lf">' +
        '        <td colspan="9">备注：上次、本年及总计的购买，均指已完结的订单，且均以订单的创建日期为准，其中单价取实际支付金额所对应的单价。</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3" title="'+ cell1 +'">'+ cell1 +'</td>' +
        '        <td colspan="3" title="'+ cell2 +'">'+ cell2 +'</td>' +
        '        <td colspan="3" title="'+ cell3 +'">'+ cell3 +'</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3" title="'+ cell1 +'">'+ cell4 +'</td>' +
        '        <td colspan="3" title="'+ cell1 +'">'+ cell5 +'</td>' +
        '        <td colspan="3" title="'+ cell1 +'">'+ cell6 +'</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3" title="'+ cell1 +'">'+ cell7 +'</td>' +
        '        <td colspan="3" title="'+ cell1 +'">'+ cell8 +'</td>' +
        '        <td colspan="3" title="'+ cell1 +'">'+ cell9 +'</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3">采购周期 '+ (info.perchase_cycle || "0") +'天</td>' +
        '        <td colspan="3">最低采购量 '+ parseFloat(Number(info.minimum_purchase).toFixed(4)) +'</td>' +
        '        <td colspan="3">最低库存/当前库存 '+ parseFloat(Number(info.minimum_stock || "0").toFixed(4))+'/'+parseFloat(Number(info['current_stock'] || "0").toFixed(4)) +'</td>' +
        '    </tr>' +
        '    </tbody>' +
        '</table></div>';
    obj.parents(".supplierList").find(".tabs__content").html(str);
}

function chargePriceShow(supInfo) {
    var supplier = supInfo ;
    var ttl = "", info = "";
    var isStable = supplier["price_stable"];
    var canInvoice = String(supplier["material_invoiceable"]);
    var incoiceTypeVal = supplier["material_invoice_category"];

    if(isStable === "1"){ //稳定
        if(canInvoice === "1"){ // 能开票
            ttl = "已约定的单价";
            info += "开票价";
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "已约定的单价";
            info += "不开票价";
        }
    }else if(isStable === "2"){ // 变动频繁
        if(canInvoice === "1"){ // 能开票
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                ttl = "参考单价";
                info += "开票价";
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                ttl = "参考单价";
                info += "开票价";
            }
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "参考单价";
            info += "不开票价";

        }else if(canInvoice === "2"){ // 不确定
            ttl = "参考单价";
            info += "不开票价";
        }
    }
    var price = supplier["unit_price_notax"]
    if(supplier["is_tax"] === "1"){ // 含税
        price = supplier["unit_price"];
    }
    info += price + "元";
    var infoall = ""
    switch (String(supplier["inclusive_freight"])){
        case "1":
            infoall = "含运费的" + info ;
            break;
        case "2":
            infoall = "含运费的" + info + "，但材料到本市后需我司自提";
            break;
        case "3":
            infoall = "不含运费的" + info ;
            break;
        default:
            infoall = info ;
    }
    return { "ttl":ttl , "info":infoall }

}
// creator: 李玉婷，2020-05-07 14:05:00，材料选择供应商后-采购订单数据获取
function getSupplyOrder(obj){
    let source = $("#rderSource").html();
    $(".repStepThr").show().siblings().hide();
    $(".repStepThr").find("input,select").val("");
    $(".nextStep .fa").addClass("fa-circle-o").removeClass("fa-circle");
    $("#matBuyInfo thead tr td:eq(7) .xing").remove();
    $("#matBuyInfo thead tr td:eq(9) .xing").remove();
    var supplierDetails = JSON.parse(obj.parent().siblings(".hd").html());
    var matDetails = JSON.parse($("#matDetails").html());
    let limit = `2099-12-31`, addAble = false, mtPrice = ``, priceJson = {};
    $("#matchSupper [need]").each(function(){
        var attName = $(this).data("name");
        if (attName == "name") {
            $(this).html(supplierDetails["supplier_name"]);
        } else {
            $(this).html(supplierDetails[attName]);
        }
    });
    $("#matchSupperInfo").html(obj.parent().siblings(".hd").html());

    var priceMome = JSON.parse(priceStr(supplierDetails));
    $("#formSaleMat").show();
    $("#otherWarningMat").show();
    $(".orderAddress").hide();
    if (source == 1){
        limit = new Date(matDetails.latest_delivery_date).format('yyyy-MM-dd');
    } else if (source == 2) {
        addAble = true;
        $("#formSaleMat").hide();
        $(".orderAddress").show();
    }else {
        $("#formSaleMat").hide();
        $("#otherWarningMat").hide();
        if (source == 4){
            addAble = true;
            $(".orderAddress").show(); }
    }
    if (priceMome.flag == 2) {
        $("#matBuyInfo thead tr td:eq(7)").prepend('<span class="xing"></span>');
    } else if (priceMome.flag == 3) {
        $("#matBuyInfo thead tr td:eq(7)").prepend('<span class="xing"></span>');
        $("#matBuyInfo thead tr td:eq(9)").prepend('<span class="xing"></span>');
    }
    if (addAble) {
        //仅选择了一个交货地点，或选择了离厂价格的
        if (matDetails.inclusive_freight === "3"){
            getReceiveAddressList($("#deliveryAddress"),1,0);
        } else if (matDetails.inclusive_freight === "1" || matDetails.inclusive_freight === "2"){
            getMtAddressList(supplierDetails.id);
        }
        for(let key in priceMome) {
            if (key !== 'memo') {priceJson[key] = priceMome[key]}
        }
        priceJson.price_stable = supplierDetails.price_stable
    }
    if (supplierDetails.price_stable === '1') {
        mtPrice = priceMome.mtPriceVal
    } else if (supplierDetails.price_stable === '2'){
        mtPrice =
            '<div class="itemPrice">' +
            '<input value="' + priceMome.mtPriceVal + '" placeholder="请录入" onkeyup="setTotalPrice(this)"/>' +
            '<i onclick="clearCon($(this))">x</i>' +
            '</div>';
    }
    var tbHtml =
        '<tr data-incoiceable="'+ supplierDetails.material_invoiceable +'">' +
        '    <td>'+ matDetails.name +'</td>' +
        '    <td>'+ matDetails.code +'</td>' +
        '    <td>'+ matDetails.model +'</td>' +
        '    <td>'+ matDetails.specifications +'</td>' +
        '    <td>'+ matDetails.unit +'</td>' +
        '    <td><input value="" name="quantity" placeholder="请录入" onkeyup="testNumSize3(this)" onblur=\'quantityLimit($(this),'+  JSON.stringify(supplierDetails) +')\' /></td>' +
        '    <td><input class="arrivalDate" name="deliveryDate" value="" placeholder="请选择"/></td>' +
        '    <td data-tax="'+ priceMome.taxInclusive + '">' +
        '       <div>'+ mtPrice +'</div><span class="hd">'+ JSON.stringify(priceJson) +'</span></td>' +
        '    <td></td>' +
        '    <td class="notBreak">'+ priceMome.memo +'</td>' +
        '    <td><span class="ty-color-blue" onclick="detailsCompare($(this))" id="messgwe">信息对比</span><span class="hd">'+ JSON.stringify(matDetails) +'</span></td>' +
        '</tr>';
    $("#matBuyInfo tbody").html(tbHtml);
    var curObj = $("#matBuyInfo tbody tr:last").find("td").eq(6).children("input");
    laydate.render({
        elem: curObj[0],
        max: limit,
        type: "date",
        done: function(){
            deliveryDateLimit(curObj, JSON.stringify(supplierDetails));
        }
    });
    $(".repStepThr").everyTime('0.5s','amount',function(){
        if ($(".repStepThr").is(":visible")) {
            var orderTotle = 0;
            $("#matBuyInfo tbody tr").each(function(){
                if ($(this).children().eq(8).html() != "")
                    orderTotle += Number($(this).children().eq(8).html());
            });
            $("#orderTotleAmount").html(orderTotle.toFixed(2));
        } else{
            $(".repStepThr").stopTime('amount');
        }
    });
}
// creator: 李玉婷，2020-05-13 09:06:58，输出价格和备注
function priceStr(matInfo) {
    var json = {
        "mtPrice": 0,
        "mtPriceVal": 0,
        "memo": "",
        "flag": 1,
        "taxInclusive": 0
    };
    var isStable = matInfo["price_stable"]; //价格是否稳定:1-相对稳定,2-变动频繁
    var canInvoice = String(matInfo["material_invoiceable"]); //本物料可否开具发票:0-不能开,1-可开具,2-不确定
    var incoiceTypeVal = matInfo["material_invoice_category"]; //本物料发票类型:1-增值税专票,2-普通发票

    if(isStable === "1"){ //稳定
        if(canInvoice === "1"){ // 能开票
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                json.taxInclusive = 1;
                if (matInfo.is_tax == 0) {
                    json.mtPriceVal = json.mtPrice = (matInfo["unit_price_notax"] * (1 + matInfo["tax_rate"] / 100)).toFixed(2);
                } else {
                    json.mtPriceVal = json.mtPrice = (matInfo["unit_price"]).toFixed(2);
                }
                json.memo = "<span title='单价为开"+ matInfo["tax_rate"] + "%增值税专用发票的含税价'>单价为开"+ matInfo["tax_rate"] + "%增值税专用发票的含税价</span>";
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                json.mtPriceVal = json.mtPrice = (matInfo["unit_price_notax"]).toFixed(2);
                json.memo = "<span title='单价为开普票价'>单价为开普票价</span>";
            }
        }else if(canInvoice === "0"){ // 不能开票
            json.mtPriceVal = json.mtPrice = (matInfo["unit_price_notax"]).toFixed(2);
            json.memo = "<span title='购买不给开发票'>购买不给开发票</span>";
        }
    }else if(isStable === "2"){ // 变动频繁
        json.flag = "2";
        if(canInvoice === "1"){ // 能开票
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                var price = (matInfo["unit_price_notax"] * (1 + matInfo["tax_rate"] / 100)).toFixed(2);
                json.mtPriceVal = price;
                json.taxInclusive = 1;
                if (matInfo.is_tax == 1) {
                    price = (matInfo["unit_price"]).toFixed(2)
                    json.mtPriceVal = price;
                }
                json.memo = "<span title='单价为开"+ matInfo["tax_rate"] + "%增值税专用发票的含税价。参考单价为"+ price +"元'>单价为开"+ matInfo["tax_rate"] + "%增值税专用发票的含税价。参考单价为"+ price +"元</span>";
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                json.mtPriceVal = (matInfo["unit_price_notax"]).toFixed(2);
                json.memo = "<span title='单价为开普票的价格。参考单价为"+ (matInfo["unit_price_notax"]).toFixed(2) +"元'>单价为开普票的价格。参考单价为"+ (matInfo["unit_price_notax"]).toFixed(2) +"元</span>";
            }
        }else if(canInvoice === "0"){ // 不能开票
            json.mtPriceVal = (matInfo["unit_price_notax"]).toFixed(2);
            json.memo = "<span title='购买不给开发票。参考单价为"+ (matInfo["unit_price_notax"]).toFixed(2) +"元'>购买不给开发票。参考单价为"+ (matInfo["unit_price_notax"]).toFixed(2) +"元</span>";
        }else if(canInvoice === "2"){ // 不确定
            json.flag = "3";
            json.mtPriceVal = (matInfo["unit_price_notax"]).toFixed(2);
            json.memo = '<div class="memoArea" onclick="setMemoCon($(this))"></div>';
        }
    }
    var str = JSON.stringify(json);
    return str;
}
// creator: 李玉婷，2020-11-03 17:37:58，单价改变时总价赋值
function setTotalPrice(obj){
    testNum(obj);
    var price = $(obj).val();
    var quantity = $(obj).parents("tr").children().eq(5).find("input").val();
    var totalPrice = price * quantity;
    if (totalPrice && totalPrice > 0) $(obj).parents("tr").find("td").eq(8).html(totalPrice.toFixed(2));
}
// creator: 李玉婷，2020-05-19 14:24:23，一键清除
function clearCon(obj) {
    obj.prev().val("");
}
// creator: 李玉婷，2020-05-19 15:57:17，备注里的发票设置
function openNext(obj) {
    var open = obj.data("open");
    if (obj.find(".fa-dot-circle-o").length > 0){
        obj.find("span").attr("class","fa fa-circle-o");
        if (open != 0) {
            obj.parent().nextAll(".memoItem").hide();
        }
    } else {
        obj.find("span").attr("class","fa fa-dot-circle-o");
        obj.siblings("div:not('.taxWrap')").find("span").attr("class","fa fa-circle-o");
        if (open != 0) {
            obj.parent().next().show();
        } else {
            obj.parent().nextAll(".memoItem").hide();
        }
    }
}
// creator: 李玉婷，2020-11-03 20:25:42，不确定 - 备注设置
function setMemoCon(obj) {
    var trObj = obj.parents("tr");
    var trP = obj.parents("tbody");
    var trInx = trP.find("tr").index(trObj);
    var data = {};
    if (trInx == 0 && $(".repStepThr:visible").length > 0) {
        data = JSON.parse($("#matchSupperInfo").html());
    } else {
        data = JSON.parse(trObj.children("td:last").find(".hd").html());
    }
    $("#editUncertainMemo").data("obj",obj);
    $("#editUncertainMemo").data("info",data);
    $(".uncertainMemo .fa").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
    $(".uncertainMemo").children(".memoItem:eq(0)").show().siblings().hide();
    if (data["material_invoice_category"] == 2) {
        $(".uncertainMemo").children(".memoItem:eq(1)").find("div").eq(0).attr("disabled", "disabled");
    } else {
        $(".uncertainMemo").children(".memoItem:eq(1)").find("div").eq(0).removeAttr("disabled");
    }
    getSupplierTaxRate($(".taxRateList"));
    bounce.show($("#editUncertainMemo"));
}
// creator: 李玉婷，2020-07-27 11:13:20，备注转换
function memoTurn() {
    var count = 0;
    var obj = $("#editUncertainMemo").data("obj");
    var conf = $("#editUncertainMemo").data("info");
    $(".uncertainMemo").children(".memoItem:eq(0)").show();
    $(".uncertainMemo").children(".memoItem:visible").each(function(){
        var size = $(this).find(".fa-dot-circle-o").length;
        if (size <= 0) {
            count++;
        }
    });
    if ($("#order_taxRate").is(":visible") && $("#order_taxRate").val() === "") layer.msg("请选择税率！");
    if (count == 0) {
        $(".uncertainMemo").children(".memoItem:visible").each(function(){
            var name = $(this).find(".fa-dot-circle-o").data("name");
            var key = $(this).find(".fa-dot-circle-o").data("type");
            conf[name] = key;
        });
        conf.tax_rate = $("#order_taxRate").val().replace(/\%/, "");
        var str = uncertainMemo(conf);
        obj.html(str);
        obj.attr("title", str);
        obj.data("memo", conf);
        obj.parents("tr").data("incoiceable", $(".uncertainMemo .memoItem:eq(0) .fa-dot-circle-o").data("type"));
        bounce.cancel();
    }
}
// creator: 李玉婷，2020-07-27 16:18:25，输出备注
function uncertainMemo(json) {
    var html = '', priceStr='';
    if ($(".orderUpdate:visible").length > 0) {
        priceStr = 'unit_price';
    } else if ($(".repStepThr:visible").length > 0) {
        priceStr = 'unit_price_notax';
    }
    if(json.invoicable == "1"){ // 能开票
        if(json.invoiceCategory == "1"){ // 能开增值税专用发票
            var rate = json.tax_rate;
            if (json.taxInclusive == 0) {
                html = "单价为开"+ rate +"%增值税专用发票的不含税价。参考单价为"+ (json[priceStr]).toFixed(2) +"元";
            } else {
                html = "单价为开"+ rate +"%增值税专用发票的含税价。参考单价为"+ (json[priceStr]).toFixed(2) +"元";
            }
        }else if(json.invoiceCategory == "2"){ // 开普通发票
            html = "单价为开普票的价格。参考单价为"+ (json[priceStr]).toFixed(2) +"元";
        }
    }else if(json.invoicable == "0"){ // 不能开票unit_price_notax
        html = "购买不给开发票。参考单价为"+ (json[priceStr]).toFixed(2) +"元";
    }
    return html;
}

// creator:lyt 2022-12-11 新增税率
function addTaxRate(obj){
    $("#taxRateVal").val("");
    $("#addTaxRate").data("obj", obj)
    bounce_Fixed.show($("#addTaxRate"));
}
// creator:lyt 2022-12-13 新增税率确定
function addTaxRatetOk(){
    let val = $("#taxRateVal").val();
    if (val !== "") {
        var conf = ``;
        if ($(".repStepThr:visible").length > 0) {
            conf = JSON.parse($("#matchSupperInfo").html());
        } else {
            let obj = $("#editUncertainMemo").data("obj");
            conf = JSON.parse(obj.parents("tr").children("td:last").find(".hd").html());
        }
        let supplier = conf.supplier;
        $.ajax({
            "url":"../supplier/addSupplierInvoice.do",
            "data":{ 'supplier': supplier, 'invoiceCategory': 1, 'taxRate': val },
            success:function (res) {
                if (res.success === 1) {
                    let data = res.data;
                    let obj = $("#addTaxRate").data("obj")
                    let option = ` <span class="funBtn" data-val="${data.id}" data-fun="setTaxRateVal"><span>${val}%</span><span onclick="delTaxRate($(this), event)">删除</span></span>`
                    obj.siblings(".taxRateBody").find(".taxRateList").append(option);
                    bounce_Fixed.cancel();
                }
            }
        });
    }
}
// creator:lyt 2022-12-16 获取列表
function getSupplierTaxRate(obj){
    var conf = ``;
    if ($(".repStepThr:visible").length > 0) {
        conf = JSON.parse($("#matchSupperInfo").html());
    } else {
        let obj = $("#editUncertainMemo").data("obj");
        conf = JSON.parse(obj.parents("tr").children("td:last").find(".hd").html());
    }
    let supplier = conf.supplier;
    obj.html("");
    $.ajax({
        "url":"../supplier/getSupplierInvoices.do",
        "data":{ 'id': supplier },
        success:function (res) {
            let list = res.data || [];
            let option = `<span class="funBtn" data-val="" data-fun="setTaxRateVal"><span></span></span>`;
            list.forEach(function (item) {
                option += ` <span class="funBtn" data-val="${item.id}" data-fun="setTaxRateVal"><span>${item.taxRate}%</span><span onclick="delTaxRate($(this), event)">删除</span></span>`
            })
            obj.append(option);
            bounce_Fixed.cancel();
        }
    });
}
function delTaxRate(obj, et){
    et.stopPropagation();
    let val = obj.parent().data("val");
    $.ajax({
        "url":"../supplier/deleteSupplierInvoice.do",
        "data":{ 'id': val },
        success:function (res) {
            let state = res.success;
            if (state === 1) {
                obj.parent().remove();
                layer.msg("删除成功");
            } else {
                layer.msg("不能删除已被使用的税率！");
            }
        }
    });
}
function taxRateFun(obj){
    obj.siblings(".taxRateList").toggle();
}
function setTaxRateVal(obj){
    let id = obj.data("val")
    let val = obj.find("span").eq(0).html();
    obj.parents(".taxRateList").toggle();
    obj.parents(".taxRateList").siblings("input[type='hidden']").val(id);
    obj.parents(".taxRateList").siblings("input[type='text']").val(val);
}
// creator: 李玉婷，2022-04-15 12:16:50，修改订单中数量限制
function planLimit(obj, supplier) {
    let info = JSON.parse(obj.parents("tr").find("td:last").children(".hd").html());
    if (info.method == '6' && Number($.trim(obj.val())) > Number(info.lower_quantity)) {
        bounce.show($("#planTip"));
        $("#planTip").data("obj", obj);
        $("#planTip .info").html(JSON.stringify(supplier));
    } else {
        quantityLimit(obj, supplier);
    }
}
// creator: 李玉婷，2022-04-15 13:18:02，修改订单中数量限制提示确定
function planTipSure() {
    let obj = $("#planTip").data("obj");
    let supplier = JSON.parse($("#planTip .info").html());
    bounce.cancel();
    quantityLimit(obj, supplier);
}
// creator: 李玉婷，2020-05-20 08:15:47，数量提醒
function quantityLimit(obj, supplier) {
    var val = Number(obj.val());
    if (val != ""){
        var refer = Number(supplier.minimum_purchase);
        var totalPrice = 0;
        var priceObj = obj.parents("tr").find("td").eq(7);
        if (priceObj.find("input").length > 0){
            if(priceObj.find("input").val() != "") {
                totalPrice = val * priceObj.find("input").val();
            }
        }else{
            totalPrice = val * priceObj.find("div").html();
        }
        obj.parents("tr").find("td").eq(8).html(totalPrice.toFixed(2));
        if (refer && refer > val){
            $("#minimumPurchase").html(supplier.minimum_purchase + supplier.unit);
            bounce.show($("#purchaseTip"));
        }
    }
}
// creator: 李玉婷，2020-05-20 20:48:14，采购周期提示
function deliveryDateLimit(obj, supplier) {
    if (obj.val() != "") {
        supplier = JSON.parse(supplier);
        if (supplier.perchase_cycle && supplier.perchase_cycle != ""){
            var cycle = supplier.perchase_cycle * 3600 * 24000;
            var now = new Date().getTime();
            var arrive = obj.val() + ' 24:00:00';
            arrive = new Date(arrive).getTime();
            var diff = Math.abs(arrive - now);
            if (diff < cycle) {
                $("#perchaseCycle").html(supplier.perchase_cycle);
                bounce.show($("#deliveryDateLimit"));
            }
        }
    }
}
// creator: 李玉婷，2020-05-12 15:52:44，返回按钮设置
function setBackBtn(num){
    if (num == 1){
        $("#backInit").show().siblings().hide();
    }else if(num == 2){
        $("#backInit").show();
        $("#backPre").show();
    }else {
        $("#backInit").hide();
        $("#backPre").hide();
    }
}
// creator: 李玉婷，2020-04-13 10:01:53，新增订单-选项
function orderTypeChoose(obj){
    var val = obj.val();
    setBackBtn(1);
    $("#isEdit").val("1");
    $("#rderSource").html(val);
    $(".replenish").show().siblings().hide();
    if (val == 1) {//新的销售订单所需的采购
        $(".module" + val).show().siblings().hide();
        $(".repStepOne").show().siblings().hide();
        getNewSaleOrderList(1);
    } else if (val == 2) {//库存预警的材料
        $(".warningOrder").show().siblings().hide();
        $(".repStepOne").show().siblings().hide();
        getWarningList(1);
    } else if (val == 4) {//补货
        $(".supplyAgain").show().siblings().hide();
        $(".repStepOne").show().siblings().hide();
        setHeight([ $('.leCat'), $('.riMt')]);
        getReplenishList("", "", 1, 20);
    }
}
// created:hxz 2020-03-13 调整框高
function setHeight(objArr){
    // 设置框高
    let Hw = $(window).height()-120 , arr = [Hw];
    for(var i = 0 ; i < objArr.length; i++){
        arr.push(objArr[i].height());
    }
    let H = arr.sort()[arr.length-1];
    for(var i = 0 ; i < objArr.length; i++){
        objArr[i].css({'min-height':H +'px' });
    }
}
// creator: 李玉婷，2020-04-14 22:21:39，选择下一步
function nextStep(){
    $("#noneed").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
    $("#needget").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
    if ($(".nextStep .fa-circle").length == 0) return false;
    var step = $(".nextStep .fa-circle").data("type");
    if (step !== 2){
        $("#otherMaterialType").html(step);
        $("#otherMaterial tbody").html("");
        $(".nextStep .fa").addClass("fa-circle-o").removeClass("fa-circle");
        $(".repStepFor").show().siblings().hide();
        $(".repStepFor #checkedNum").html("0");
        var supplierInfo = JSON.parse($("#matchSupperInfo").html());
        var supplier = supplierInfo.supplier;
        $("#forSupplierInfo [need]").each(function () {
            var attName = $(this).data("name");
            $(this).html(supplierInfo[attName]);
        });
        otherMatList(supplier);
    }else{
        var reqNum = 0;
        $(".repStepThr [require]:visible").each(function () {
            if ($(this).val() === "") {
                reqNum++;
            }
        });
        $("#matBuyInfo tbody input").each(function () {
            if ($(this).val() == "") {
                reqNum++;
            }
        });
        $("#matBuyInfo tbody tr .memoArea").each(function () {
            if ($(this).html() == "") {
                reqNum++;
            }
        });
        if (reqNum > 0) {
            bounce_Fixed.show($("#filledTip"));
        }else{
            $(".setAdvance .fa").attr("class","fa fa-circle-o");
            // $(".advanceMain").hide();
            $(".advanceMain input").val("");
            $("#advanceCharge").data("total_price", $("#orderTotleAmount").html());

            var messg = $("#makesure").data("mess");    //疑问：这里是不是应该只获取一条数据？而不是两条？？

            console.log(messg); //供应商数据信息
            var two = $("#makesure").data('gys');
            console.log('包含供应商的名称和id么',two);
            var unid = two.supplier_id; //供应商id
            // var one = $("#messgwe").siblings(".hd").html();
            // console.log(one);
            // one = JSON.parse(one);
            // var unid = one.supplier_material_id;
            var info = "";
            for(var j in messg){
                info = messg[j];
                var yufu = info.supplier_is_imprest;    //预付款
                $("#advanceChange").data("yf",yufu);
                    //当预付款为1时
                if(yufu === "1"){
                        // $("#needget").attr("disabled",true);
                        $(".advanceMain").show();
                        var key = $(".setAdvance .fa").data("type");
                        $(this).addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                        $(this).parent().siblings("div").find(".fa").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                        if(key == 2){
                            $("#needget").data("ron",1);
                            var option = '';
                            var num = 0;
                            $("#matBuyInfo tbody tr").each(function(){
                                var incoiceable = $(this).data("incoiceable");
                                if (incoiceable == "0") num++;
                            });
                            // 正确的应该是1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
                            if (num > 0) { //1=购买不给开发票 2=不含
                                option =
                                    '<option value="" readonly=""></option>' +
                                    '<option value="1">现金</option>' +
                                    '<option value="6">非公户银行转账</option>';
                            } else {
                                option =
                                    '<option value="" readonly=""></option>' +
                                    '<option value="1">现金</option>' +
                                    '<option value="5">银行转账</option>' +
                                    '<option value="4">承兑汇票</option>' +
                                    '<option value="3">转账支票</option>';
                            }
                            $(".advanceForm select").html(option);
                            $(".advanceMain").show();
                            $(".advanceForm .advanceItem:eq(0)").nextAll().remove();
                            $("#cloone").hide();
                            $("#pofont").hide();
                            $("#pofont1").hide();
                            $("#cloone1").hide();
                            var monny = $("#advanceChange").data("money");
                            var str = `${monny}`;
                            if(str == "undefined" || str == "null"){
                                $("#math").val("");
                            }else{
                                $("#math").val(str);
                            }
                            var one = $("#math").val();
                            if(one.length >0){
                                $("#pofont").show();
                            }else{
                                $("#pofont").hide();
                            }
                        }else{
                            $(".advanceMain").hide();
                        }
                    }else{
                        $("#needget").data("ron",0);
                        $(".advanceMain").hide();
                    }
                var hp = info.supplier_draft_acceptable;    //接收汇票
                var bili = info.supplier_imprest_proportion;//预付款比例
                $("#advanceCharge").data("boli",bili);
                var allchage = $("#advanceCharge").data("total_price"); //订单金额
                var amount = bili*0.01*allchage;     //计划付款金额
                $("#mon1").html(amount);
                var tuny = $("#mon1").text()*1;//将左侧的数字值设置为保留小数点后两位
                $("#mon1").text(tuny.toFixed(2));
                var str = ``;
                str = `${amount}`;
                if(str == "undefined" || str == "null"){
                        $("#math").val("");
                    }else{
                        $("#math").val(str);
                    }
                var one = $("#math").val();
                if(one.length >0){
                        $("#pofont").show();
                    }else{
                        $("#pofont").hide();
                    }
                $("#mon2").html(allchage);
                if(bili == undefined){
                        amount == 0;
                        bili = 0;
                    }
                $("#mon3").html(bili);
                var tury = $("#mon3").text()*1;
                $("#mon3").text(tury.toFixed(2));
                $("#advanceCharge").data("money",amount);
                var str = ``;
                if(hp == false){
                        if(yufu == "0"){
                            str += `"不确定是否需预付款、不接收承兑汇票"。`;
                            $("#annotation").hide();
                            $("#needget").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                            $("#noneed").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                            $("#annotation").hide();
                            $("#messge").hide();
                            $("#math").val("");
                            $("#pofont").hide();
                        }else if(yufu == "1"){
                            if(bili == "-1"){
                                str += `"需预付款但预付比例不确定、不接收承兑汇票"。`;
                                $("#annotation").hide();
                                $("#needget").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                                $("#noneed").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                                $("#messge").hide();
                                $("#math").val("");
                                $("#pofont").hide();
                            }else{
                                str +=`"需预付款${bili.toFixed(2)}%、不接收承兑汇票"。`;
                                $("#annotation").hide();
                                $("#needget").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                                $("#noneed").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                                $("#messge").show();
                                $("#pofont").show();
                            }
                        }else if(yufu == "2"){
                            str += `"不需要预付款、不接收承兑汇票"。`;
                            $("#annotation").hide();
                            $("#noneed").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                            $("#needget").addClass("fa fa-circle-o").removeClass("fa-dot-circle-o");
                            $("#messge").hide();
                            $("#math").val("");
                            $("#pofont").hide();
                        }
                    }else if(hp == true){
                        if(yufu == "0"){
                            str += `"不确定是否需预付款、接收承兑汇票"。`;
                            $("#annotation").hide();
                            $("#needget").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                            $("#noneed").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                            $("#messge").hide();
                            $("#math").val("");
                            $("#pofont").hide();
                        }else if(yufu == "1"){  //bili还有值是null的时候
                            if(bili == "-1"){
                                str += `"需预付款但预付比例不确定、接收承兑汇票"。`;
                                $("#annotation").hide();
                                $("#needget").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                                $("#noneed").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                                $("#messge").hide();
                                $("#math").val("");
                                $("#pofont").hide();
                            }else if(bili == null){
                                str += `"需预付款但没有预付比例,接收承兑汇票"。`;
                                $("#annotation").hide();
                                $("#needget").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                                $("#noneed").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                                $("#messge").hide();
                                $("#math").val("");
                                $("#pofont").hide();
                            }else{
                                str +=`"需预付款${bili.toFixed(2)}%、接收承兑汇票"。`;
                                $("#annotation").hide();
                                $("#needget").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                                $("#noneed").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                                $("#messge").show();
                                $("#pofont").show();
                            }
                        }else if(yufu == "2"){
                            str += `"不需要预付款、接收承兑汇票"。`;
                            $("#annotation").show();
                            $("#noneed").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                            $("#needget").addClass("fa fa-circle-o").removeClass("fa-dot-circle-o");
                            $("#messge").hide();
                            $("#math").val("");
                            $("#pofont").hide();
                        }
                    }else if(hp == null){
                        if(yufu == "0"){
                            str += `"不确定是否需预付款、不接收承兑汇票"。`;
                            $("#annotation").hide();
                            $("#needget").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                            $("#noneed").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                            $("#messge").hide();
                            $("#math").val("");
                            $("#pofont").hide();
                        }else if(yufu == "1"){
                            if(bili == "-1"){
                                str += `"需预付款但预付比例不确定、接收承兑汇票"。`;
                                $("#annotation").hide();
                                $("#needget").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                                $("#noneed").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                                $("#messge").hide();
                                $("#math").val("");
                                $("#pofont").hide();
                            }else if(bili == null){
                                str += `"需预付款但没有预付比例,接收承兑汇票"。`;
                                $("#annotation").hide();
                                $("#needget").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                                $("#noneed").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                                $("#messge").hide();
                                $("#math").val("");
                                $("#pofont").hide();
                            }else{
                                str +=`"需预付款${bili.toFixed(2)}%、接收承兑汇票"。`;
                                $("#annotation").hide();
                                $("#needget").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                                $("#noneed").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                                $("#messge").show();
                                $("#pofont").show();
                            }
                        }else if(yufu == "2"){
                            str += `"不需要预付款、接收承兑汇票"。`;
                            $("#annotation").show();
                            $("#noneed").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                            $("#needget").addClass("fa fa-circle-o").removeClass("fa-dot-circle-o");
                            $("#messge").hide();
                            $("#math").val("");
                            $("#pofont").hide();
                        }
                    }
                if(info.supplier_id == unid) {
                    break;
                }
            }
            $("#messgble").html(str);
            bounce.show($("#advanceCharge"));
        }
    }
}
// creator: 李玉婷，2020-05-13 17:14:22，其他材料选购获取
function otherMatList(supplier, currPage  , pageSize) {
    var oid = sphdSocket.user.oid
    var step = $("#otherMaterialType").html();
    var mtArr = [];
    var url = "../mt/list";
    var data = {
        "oid":oid ,
        "keyword":"",
        "supplier": supplier,
        "state": "4",
        "pageNum": currPage,
        "per": pageSize
    };
    $("#otherMaterial .warningThd").hide();
    if (step == '0' || step == 0) {
        url = "../po/warningList"
        $("#otherMaterial .warningThd").show();
    } else if (step == 6) {
        url = "../po/slOrdersPo"
    }else {
    }
    $("#matBuyInfo tbody tr").each(function(){
        var mat = JSON.parse($(this).children("td:last").find(".hd").html());
        mtArr.push(mat.id);
    });
    $.ajax({
        url: url,
        data:  data ,
        success:function (data) {
            var str = '';
            var list = data.data;
            if (list){
                let shortNum = ``;
                let temp = null;
                for (var t=0;t<list.length;t++){
                    var indx = mtArr.findIndex((value) => {
                        return list[t].id == value
                    })
                    if (indx < 0) {
                        temp = list[t];
                        if (step === 0|| step === '0') {
                            temp.method = 2;
                            if (Number(list[t].current_stock || "0").toFixed(4) < Number(list[t].minimum_stock || "0").toFixed(4)) {
                                shortNum = `<td>${parseFloat((Number(list[t].minimum_stock || "0")-Number(list[t].current_stock || "0")).toFixed(4))}</td>`
                            } else {
                                shortNum = `<td>${"0"}</td>`
                            }
                        } else if (step === 1|| step === '1') {
                            temp.method = 1;
                        } else if (step === 6|| step === '6') {
                            temp.method = 6;
                        }
                        str +=
                            '<tr>' +
                            '    <td class="br-empty"><span class="fa fa-circle-o" onclick="checkState($(this))"></span></td>' +
                            '    <td>'+ list[t].name +'</td>' +
                            '    <td>'+ list[t].code +'</td>' +
                            '    <td>'+ handleNull(list[t].model) +'</td>' +
                            '    <td>'+ handleNull(list[t].specifications) +'</td>' +
                            '    <td>'+ handleNull(list[t].unit) +'</td>' +
                            '    <td>'+ parseFloat(Number(list[t].current_stock || "0").toFixed(4)) +'</td>' +
                            '    <td>'+ parseFloat(Number(list[t].minimum_stock || "0").toFixed(4)) +'</td>' + shortNum +
                            '    <td class="ty-td-control" onclick="getInTransit($(this))"><span class="ty-color-blue">'+ (list[t]['way_num'] || "0") +'</span></td>' +
                            '    <td>'+ handleNull((list[t]['supplier_number'] || "0")) +'个</td>' +
                            '    <td>' +
                            '       <span class="ty-color-blue" onclick="detailsCompare($(this))" id="messgwe">信息对比</span>' +
                            '       <span class="hd">'+ JSON.stringify(temp) +'</span>' +
                            '    </td>' +
                            '</tr>';
                    }
                }
                $("#otherMaterial tbody").html(str);
            }
        }
    })
}
// creator: 李玉婷，2020-04-14 19:11:00，选择完毕
function chooseOtherMat() {
    $(".repStepThr").show().siblings().hide();
    if($("#otherMaterial tbody .fa-dot-circle-o").length > 0){
        var html = '';
        let limit = `2099-12-31`;
        $("#otherMaterial tbody .fa-dot-circle-o").each(function () {
            var flag = JSON.parse($(this).parents("tr").children("td:last").find(".hd").html());
            let priceJson = {}, mtPrice = ``
            var priceMome = JSON.parse(priceStr(flag));
            if (flag.price_stable === '1') {
                mtPrice = priceMome.mtPriceVal
            } else if (flag.price_stable === '2'){
                mtPrice =
                    '<div class="itemPrice">' +
                    '<input value="' + priceMome.mtPriceVal + '" placeholder="请录入" onkeyup="setTotalPrice(this)"/>' +
                    '<i onclick="clearCon($(this))">x</i>' +
                    '</div>';
            }
            for(let key in priceMome) {
                if (key !== 'memo') {priceJson[key] = priceMome[key]}
            }
            priceJson.price_stable = flag.price_stable
            html +=
                '<tr data-mtid="'+ flag.id +'"  data-incoiceable="'+ flag.material_invoiceable +'">' +
                '    <td>'+ flag.name +'</td>' +
                '    <td>'+ flag.code +'</td>' +
                '    <td>'+ flag.model +'</td>' +
                '    <td>'+ flag.specifications +'</td>' +
                '    <td>'+ flag.unit +'</td>' +
                '    <td><input value="" placeholder="请录入" onkeyup="testNumSize3(this)" onblur=\'quantityLimit($(this),'+ JSON.stringify(flag) +')\' /></td>' +
                '    <td><input class="arrivalDate" value="" placeholder="请选择"/></td>' +
                '    <td data-tax="'+ priceMome.taxInclusive + '">' +
                '       <div>'+ mtPrice +'</div><span class="hd">'+ JSON.stringify(priceJson) +'</span></td>' +
                '    <td></td>' +
                '    <td class="notBreak">'+ priceMome.memo +'</td>' +
                '    <td>' +
                '       <span class="ty-color-blue tb-btn-sm" onclick="detailsCompare($(this))">信息对比</span>' +
                '       <span class="ty-color-red tb-btn-sm" onclick="deleteOther($(this), 1)">删除</span>' +
                '       <span class="hd">'+ JSON.stringify(flag) +'</span>' +
                '</td>' +
                '</tr>';
        });
        $("#matBuyInfo tbody").append(html);
        $("#matBuyInfo tbody tr").each(function () {
            var idx = $(this).index();
            if (idx !== 0) {
                var flag1 = $(this).children("td:last").find(".hd").html();
                var flag2 = JSON.parse(flag1);
                var curObj = $(this).find("td").eq("6").children("input");
                if (flag2.method == 6) {
                    limit = new Date(flag2.latest_delivery_date).format('yyyy-MM-dd');
                }
                laydate.render({
                    elem: curObj[0],
                    type: 'date',
                    max: limit,
                    done: function(){
                        deliveryDateLimit(curObj, flag1);
                    }
                });
            }
        });
        var step = $("#otherMaterialType").html();
        if (step !== 6) isIncludeAddress($("#deliveryAddress"),$("#otherMaterial tbody .fa-dot-circle-o").length);
        $(".repStepThr").everyTime('0.5s','amount',function(){
            var len = $(".repStepThr:visible").length;
            if (len > 0) {
                var orderTotle = 0;
                $("#matBuyInfo tbody tr").each(function(){
                    if ($(this).children().eq(8).html() != "")
                        orderTotle += Number($(this).children().eq(8).html());
                });
                $("#orderTotleAmount").html(orderTotle.toFixed(2));
            } else{
                $(".repStepThr").stopTime('amount');
            }
        });
    }
}
// creator: 李玉婷，2020-04-14 19:14:45，勾选材料
function checkState(obj) {
    if (obj.hasClass("fa-circle-o")){
        obj.addClass("fa-dot-circle-o").removeClass("fa-circle-o");
    }else{
        obj.addClass("fa-circle-o").removeClass("fa-dot-circle-o");
    }
    var totle = $("#otherMaterial .fa-dot-circle-o").length;
    $("#checkedNum").html(totle);
}
// creator: 李玉婷，2020-04-14 19:52:32，信息对比
function detailsCompare(obj) {
    var info = JSON.parse(obj.siblings(".hd").html());
    var html =
        '<tr>' +
        '    <td>' + handleNull(info.name) + '</td>' +
        '    <td>' + handleNull(info.code) + '</td>' +
        '    <td>' + handleNull(info.model) + '</td>' +
        '    <td>' + handleNull(info.specifications) + '</td>' +
        '    <td class="createInfo">' + handleNull(info.create_name) + '&nbsp;&nbsp;' + new Date(info.create_date).format('yyyy/MM/dd hh:mm:ss') + '</td>' +
        '    <td>' + handleNull(info.unit) + '</td>' +
        '    <td>' + parseFloat(Number(info.current_stock || "0").toFixed(4)) + '</td>' +
        '    <td>' + parseFloat(Number(info.minimum_stock || "0").toFixed(4)) + '</td>' +
        '    <td>' + handleNull(info['location_number'] || "0") + '个</td>' +
        '    <td>' + handleNull((info['supplier_number'] || "0")) + '个</td>' +
        '</tr>';
    $("#mtDetailsCompare tbody").html(html);
    bounce.show($("#detailsCompare"));
    getSupplierList(info.id || info.material_id, $("#compareSupplier"), 0);
}
// creator: 李玉婷，2020-05-19 11:08:54，删除其他材料
function deleteOther(obj, type) {
    $("#mtDel").data("obj", obj);
    $("#mtDel").data("type", type);
    bounce.show($("#mtDel"));
}
// creator: 李玉婷，2020-05-19 17:27:16，删除确定
function deleteOtherSure() {
    var obj = $("#mtDel").data("obj");
    var type = $("#mtDel").data("type");
    if (type == 2) {
        var items = $("#updateOrderList").data("dels");
        if (items == "") items = [];
        var trInfo = JSON.parse(obj.siblings(".hd").html());
        items.push(trInfo.item_id);
        $("#updateOrderList").data("dels", items);
    }
    bounce.cancel();
    obj.parents("tr").remove();
}
// creator: 李玉婷，2020-04-15 11:07:26，需多次预付款
function addMoreAdvance(){
    var option = '';
    var num = 0;
    $("#matBuyInfo tbody tr").each(function(){
        var incoiceable = $(this).data("incoiceable");
        if (incoiceable == "0") num++;
    });
    // 正确的应该是1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
    if (num > 0) { //1=购买不给开发票 2=不含
        option =
            '<option value="" readonly=""></option>' +
            '<option value="1">现金</option>' +
            '<option value="6">非公户银行转账</option>';
    } else {
        option =
            '<option value="" readonly=""></option>' +
            '<option value="1">现金</option>' +
            '<option value="5">银行转账</option>' +
            '<option value="4">承兑汇票</option>' +
            '<option value="3">转账支票</option>';
    }
    var html =
        '<div class="advanceItem">' +
        '    <div>' +
        '        <span class="xing">计划付款时间</span>' +
        '        <input class="planTime" type="text" value="" name="planDate"  />' +
        '        <span class="ty-btn ty-btn-red" onclick="delAdvanceItem($(this))">删除</span>'+
        '    </div>' +
        '    <div style="display: flex;align-items: center;">' +
        '        <span class="xing" style="margin-right: 24px;">计划付款金额</span>' +
        '        <div style="height: 39px">'+
        '               <input type="text" value="" id="math1" name="planAmount" oninput="testNum(this)" />' +
        '               <div style="font-size: 14px;margin-top: -25px;margin-left: 121px;display: none;width: 20px;" id="pofont1">元</div>'+
        '               <div style="top: -22px;cursor: pointer;display: none;right: 13px;position: relative;" class="input_clear" id="cloone1">' +
        '                       <button type="button" class="close" data-dismiss="modal" aria-hidden="true">x</button>'+
        '               </div>'+
        '        </div>'+
        '    </div>' +
        '    <div>' +
        '        <span class="xing">计划付款型式</span>' +
        '        <select name="planMethod">'+ option +'</select>' +
        '    </div>' +
        '</div>';
    $(".advanceForm").append(html);
    var curObj = $(".advanceForm .advanceItem:last").find(".planTime")[0];
    laydate.render({elem: curObj,type: 'date'});
}
// creator: 李玉婷，2020-10-27 16:46:51，删除
function delAdvanceItem(obj) {
    obj.parents(".advanceItem").remove();
}

// creator: 李玉婷，2020-04-15 12:04:26，预付款提交
function advanceTj() {
    var type = $(".setAdvance .fa-dot-circle-o").data("type");
    if (type == 1) {
        reissueOrderTj(type);
    }else if(type == 2) {
        var emptyNum = 0;
        $(".advanceForm input").each(function () {
            if ($(this).val() == "") {
                emptyNum++;
            }
        });
        $(".advanceForm select").each(function () {
            if ($(this).val() == "") {
                emptyNum++;
            }
        });
        if (emptyNum > 0) {
            bounce_Fixed.show($("#filledTip"));
        }else{
            var totalPrice = $("#advanceCharge").data("total_price");
            var planAmountToTal = 0;
            $(".advanceForm .advanceItem").each(function () {
                planAmountToTal += Number($(this).find("input").eq(1).val());
            });
            if (Number(totalPrice) < planAmountToTal) {
                //bounce.cancel($("#advanceCharge"));
                bounce_Fixed.show($("#adPlanTip"));
                return false;
            }else {
                reissueOrderTj(type);

            }
        }
    }else{
        bounce_Fixed.show($("#filledTip"));
    }
}
// creator: 李玉婷，2020-05-18 16:45:16，补发/预警订单提交
function reissueOrderTj(num) {
    var supplier = JSON.parse($("#matchSupperInfo").html());
    var type = $("#rderSource").html();
    var params = {
        "sn": $("#reissueOrderSn").val(),
        "supplier": supplier.supplier,
        "userId": sphdSocket.user.userID,
        "oid": sphdSocket.user.oid,
        "prepayment": 0,
        "method": type,
        "planOrder": ""
    };
    var poOrdersItems = [], items = [];
    $("#matBuyInfo tbody tr").each(function () {
        var order = {};
        var inx = $(this).index();
        var info = JSON.parse($(this).children("td:last").find(".hd").html());
        var len = $(this).find("td").eq(9).find(".memoArea").length;
        var price = 0;
        if ($(this).find("td").eq(7).find("input").length > 0) {
            price = $.trim($(this).find("td").eq(7).find("input").val());
        } else {
            price = $.trim($(this).find("td").eq(7).children("div").html());
        }
        order.unitPrice =  price;
        order.method =  info.method;
        order.quantity =  $.trim($(this).find("td").eq(5).children().val());
        order.deliveryDate  =  $.trim($(this).find("td").eq(6).children().val());
        if (info.method == 6) {
            order.itemId = info.item_id;
            order.material = info.id;
            order.soimId = info.soim_id;//统筹的明细id
            order.versionNo = info.version_no;
        }
        if (inx == 0) {
            params.planOrder = info.sl_orders;
            order.supplierMaterial = supplier.id;
            order.invoicable = handleNull(supplier.material_invoiceable);
            order.invoiceCategory = handleNull(supplier.material_invoice_category);
            order.taxInclusive = handleNull(supplier.is_tax);
        }else {
            order.supplierMaterial = info.supplier_material_id;
            order.invoicable = handleNull(info.material_invoiceable);
            order.invoiceCategory = handleNull(info.material_invoice_category);
            order.taxInclusive = handleNull(info.is_tax);
        }
        if (len > 0) {
            var memoJson = $(this).find("td").eq(9).find(".memoArea").data("memo");
            order.tax_rate = handleNull(memoJson.tax_rate);
            order.invoicable = handleNull(memoJson.invoicable);
            order.invoiceCategory = handleNull(memoJson.invoiceCategory);
            order.taxInclusive = handleNull(memoJson.taxInclusive);
            order.memo = $.trim($(this).find("td").eq(9).find(".memoArea").html());
        } else {
            order.memo = $.trim($(this).find("td").eq(9).children("span").html());
        }
        poOrdersItems.push(order);
    });
    poOrdersItems = JSON.stringify(poOrdersItems);
    params.poOrdersItems = poOrdersItems;
    if (num == 2) {
        params.prepayment =  $(".advanceForm .advanceItem").length;
        $(".advanceForm .advanceItem").each(function () {
            var item = {};
            item.planDate = $.trim($(this).find("input").eq(0).val());
            item.planAmount = $.trim($(this).find("input").eq(1).val());
            item.planMethod = $.trim($(this).find("select").val());
            items.push(item);
        });
    }
    items = JSON.stringify(items);
    params.prepayments = items;
    if (type === "2" || type === '4') {
        params.deliveryAddress = $("#deliveryAddress").val();
    }
    $.ajax({
        url: '../po/orders',
        data: params,
        success: function (res) {
            if (res.code == '9') {
                layer.msg(res.message);
            } else {
                layer.msg("提交成功");
                bounce.cancel();
                setBackBtn(1);
                $(".repStepOne").show().siblings().hide();
                if (type == '4') {
                    getReplenishList("",listData['keyword'],1,20);
                } else if (type == '2') {
                    getWarningList(1);
                } else if (type == '1') {
                    getNewSaleOrderList(1);
                }
            }
        }
    })
}
// creator: 李玉婷，2020-04-15 12:18:33，订单详情
function orderDetails(obj) {
    var orderId = obj.parents("tr").data("orderid");
    $("#advancePayList").html("");
    $("#orderDetailsList tbody").html("");
    $.ajax({
        url: '../po/orderDetail',
        data: {
            "orderId": orderId
        },
        success: function (res) {
            var orderInfo = res.orders[0];
            var advancePay = res.prepayments, payStr = "";
            var list = res.ordersItem, total = 0;
            $(".orderDetails [require]").each(function(){
                var name = $(this).data("name");
                $(this).html(orderInfo[name]);
            });
            if (advancePay && advancePay.length > 0) {
                for(var f=0;f<advancePay.length;f++){
                    payStr +=
                        '<div class="supplierInfo">' +
                        '    <div class="spNm">' +
                        '        <span class="spTl">计划预付款日期</span>' +
                        '        <span class="spCd">'+ new Date(advancePay[f].planDate).format('yyyy-MM-dd') +'</span>' +
                        '    </div>' +
                        '    <div>' +
                        '        <span class="spTl">计划预付款金额</span>' +
                        '        <span class="spCd">'+ advancePay[f].planAmount.toFixed(2) +'元</span>' +
                        '    </div>' +
                        '</div>'
                }
            }
            $("#advancePayList").html(payStr);
            if (list && list.length > 0) {
                var html = "", amount = 0;
                for (var t=0;t<list.length;t++) {
                    // let price = list[t].is_tax ? list[t].unit_price||0 : list[t].unit_price_notax||0;
                    let price = list[t].unit_price ;
                    // amount = list[t].quantity * price ;
                    let freight = (handleNull(list[t].inclusive_freight) == 1) || (handleNull(list[t].inclusive_freight) == 2) ? "是":"否";
                    amount = Number(list[t]['amount']) ;
                    total += amount;
                    html +=
                        '<tr>' +
                        '    <td title="'+ list[t].name +'">'+ list[t].name +'</td>' +
                        '    <td title="'+ list[t].code +'">'+ list[t].code +'</td>' +
                        '    <td title="'+ list[t].model +'">'+ list[t].model +'</td>' +
                        '    <td title="'+ list[t].specifications +'">'+ list[t].specifications +'</td>' +
                        '    <td title="'+ list[t].unit +'">'+ list[t].unit +'</td>' +
                        '    <td>'+ parseFloat((list[t].quantity).toFixed(4)) +'</td>' +
                        '    <td>'+ new Date(list[t].delivery_date).format('yyyy-MM-dd') +'</td>' +
                        '    <td>'+ (price.toFixed(2)) +'</td>' +
                        '    <td>'+ amount.toFixed(2) +'</td>' +
                        '    <td title="'+ list[t].memo +'" class="notBreak">'+ list[t].memo +'</td>' +
                        '    <td>'+ freight +'</td>' +
                        '</tr>';
                }
            }
            $("#orderTlAmount").html(Number(orderInfo.amount || 0).toFixed(2));
            $("#orderDetailsList tbody").html(html);
            setBackBtn(1);
            $(".orderDetails").show().siblings().hide();
        }
    });
}
// creator: 李玉婷，2020-05-13 09:06:58，订单修改 -- 输出价格和备注
function orderUpdatePriceStr(matInfo) {
    var json = {
        "mtPrice": 0,
        "mtPriceVal": 0,
        "memo": "",
        "flag": 1,
        "taxInclusive": 0
    };
    var isStable = matInfo["price_stable"]; //价格是否稳定:1-相对稳定,2-变动频繁
    var canInvoice = String(matInfo["material_invoiceable"]); //本物料可否开具发票:0-不能开,1-可开具,2-不确定
    var incoiceTypeVal = matInfo["material_invoice_category"]; //本物料发票类型:1-增值税专票,2-普通发票

    if(isStable === "1"){ //稳定
        if(canInvoice === "1"){ // 能开票
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                json.taxInclusive = 1;
                json.memo = "<span title='单价为开"+ matInfo["tax_rate"] + "%增值税专用发票的含税价'>单价为开"+ matInfo["tax_rate"] + "%增值税专用发票的含税价</span>";
                json.mtPriceVal = json.mtPrice = (matInfo["poi_unit_price"]).toFixed(2);
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                json.mtPriceVal = json.mtPrice = (matInfo["poi_unit_price"]).toFixed(2);
                json.memo = "<span title='单价为开普票价'>单价为开普票价</span>";
            }
        }else if(canInvoice === "0"){ // 不能开票
            json.mtPriceVal = json.mtPrice = (matInfo["poi_unit_price"]).toFixed(2);
            json.memo = "<span title='购买不给开发票'>购买不给开发票</span>";
        }
    }else if(isStable === "2"){ // 变动频繁
        json.flag = "2";
        if(canInvoice === "1"){ // 能开票
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                var price = (matInfo["poi_unit_price"]).toFixed(2)
                json.mtPriceVal = price;
                json.taxInclusive = 1;
                json.memo = "<span title='单价为开"+ matInfo["tax_rate"] + "%增值税专用发票的含税价。参考单价为"+ price +"元'>单价为开"+ matInfo["tax_rate"] + "%增值税专用发票的含税价。参考单价为"+ price +"元</span>";
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                json.mtPriceVal = (matInfo["poi_unit_price"]).toFixed(2);
                json.memo = "<span title='单价为开普票的价格。参考单价为"+ (matInfo["poi_unit_price"]).toFixed(2) +"元'>单价为开普票的价格。参考单价为"+ (matInfo["poi_unit_price"]).toFixed(2) +"元</span>";
            }
        }else if(canInvoice === "0"){ // 不能开票
            json.mtPriceVal = (matInfo["poi_unit_price"]).toFixed(2);
            json.memo = "<span title='购买不给开发票。参考单价为" + (matInfo["poi_unit_price"]).toFixed(2) + "元'>购买不给开发票。参考单价为"+ (matInfo["poi_unit_price"]).toFixed(2) +"元</span>";
        }else if(canInvoice === "2"){ // 不确定
            var dataMemo = {
                'invoicable': matInfo.invoicable,
                'invoiceCategory': matInfo.invoice_category,
                'taxInclusive': matInfo.tax_inclusive,
            };
            dataMemo = JSON.stringify(dataMemo);
            json.flag = "3";
            json.mtPriceVal = (matInfo["poi_unit_price"]).toFixed(2);
            json.memo = '<div class="memoArea" data-memo='+ dataMemo +' onclick="setMemoCon($(this))" title="' + matInfo.memo + '">' + matInfo.memo + '</div>';
        }
    }
    var str = JSON.stringify(json);
    return str;
}
// creator: 李玉婷，2020-04-15 12:18:41，订单修改
function orderUpdate(obj) {
    var orderId = obj.parents("tr").data("orderid");
    $("#isEdit").val("2");
    $("#updateOrderList tbody").html("");
    $("#updateOrderList").data("dels","");
    $.ajax({
        url: '../po/orderDetail',
        data: {
            "orderId": orderId
        },
        success: function (res) {
            var orderInfo = res.orders[0], total = 0;
            var list = res.ordersItem;
            $("#up_orderSn").val(orderInfo.sn);
            $("#up_supplier").html(JSON.stringify(orderInfo));
            $("#up_advancePayList").data("prepayments", res.prepayments);
            getReceiveAddressList($("#edit_deliveryAddress"),1, orderInfo.delivery_address)
            $("#up_supplierInfo [require]").each(function(){
                var name = $(this).data("name");
                $(this).html(orderInfo[name]);
            });
            $("#updateOrderList thead tr td:eq(7) .xing").remove();
            $("#updateOrderList thead tr td:eq(9) .xing").remove();
            if (list && list.length > 0) {
                var html = "";
                for (var t=0;t<list.length;t++){
                    list[t].poi_unit_price = list[t].unit_price;
                    var deleteBtn = "", amount = 0, mtPrice='', priceJson= {};
                    var priceMome = JSON.parse(orderUpdatePriceStr(list[t]));
                    amount = Number(list[t]['amount']) ;
                    total += amount;
                    if (t != 0){deleteBtn = '<span class="ty-color-red tb-btn-sm" onclick="deleteOther($(this), 2)">删除</span>' ;}
                    if (list[t].price_stable === '1') {
                        mtPrice = priceMome.mtPriceVal
                    } else if (list[t].price_stable === '2'){
                        mtPrice =
                            '<div class="itemPrice">' +
                            '<input value="' + priceMome.mtPriceVal + '" placeholder="请录入" onkeyup="setTotalPrice(this)"/>' +
                            '<i onclick="clearCon($(this))">x</i>' +
                            '</div>';
                    }
                    for(let key in priceMome) {
                        if (key !== 'memo') {priceJson[key] = priceMome[key]}
                    }
                    priceJson.price_stable = list[t].price_stable
                    list[t].supplier = orderInfo.supplier
                    if (list[t].inclusive_freight === '1' || list[t].inclusive_freight === '2'){
                        list[t].address = [{"id": orderInfo.delivery_address}];
                    }
                    html +=
                        '<tr>' +
                        '    <td title="'+ list[t].name + '">'+ list[t].name +'</td>' +
                        '    <td title="'+ list[t].code + '">'+ list[t].code +'</td>' +
                        '    <td title="'+ list[t].model + '">'+ list[t].model +'</td>' +
                        '    <td title="'+ list[t].specifications + '">'+ list[t].specifications +'</td>' +
                        '    <td title="'+ list[t].unit + '">'+ list[t].unit +'</td>' +
                        '    <td><input value="'+ list[t].quantity +'" onkeyup="testNumSize3(this)" onblur=\'planLimit($(this),'+ JSON.stringify(list[t]) +')\' /></td>' +
                        '    <td><input value="'+ new Date(list[t].delivery_date).format('yyyy-MM-dd') +'" /></td>' +
                        '    <td data-tax="'+ priceMome.taxInclusive +'">' +
                        '       <div>'+ mtPrice +'</div><span class="hd">'+ JSON.stringify(priceJson) +'</span>' +
                        '    </td>' +
                        '    <td>'+ (amount).toFixed(2) +'</td>' +
                        '    <td class="notBreak"> '+ priceMome.memo +'    </td>' +
                        '    <td>'+
                        '       <span class="ty-color-blue" onclick="detailsCompare($(this))">信息对比</span>' + deleteBtn +'<span class="hd">'+ JSON.stringify(list[t]) + '</span></td>' +
                        '</tr>';
                }
                //设置表头
                let limit =`2099-12-31`;
                var priceMome0 = JSON.parse(orderUpdatePriceStr(list[0]));
                if (priceMome0.flag == '2') {
                    $("#updateOrderList thead tr td:eq(7)").prepend('<span class="xing"></span>');
                } else if (priceMome0.flag == '3') {
                    $("#updateOrderList thead tr td:eq(7)").prepend('<span class="xing"></span>');
                    $("#updateOrderList thead tr td:eq(9)").prepend('<span class="xing"></span>');
                }
                $("#up_orderTlAmount").html(orderInfo.amount);
                $("#updateOrderList tbody").html(html);
                $("#updateOrderList tbody tr").each(function(){
                    var curObj = $(this).find("td").eq("6").children("input");
                    var item = $(this).children("td:last").find(".hd").html();
                    var flag2 = JSON.parse(item);
                    if (flag2.method == 6) {
                        limit = new Date(flag2.latest_delivery_date).format('yyyy-MM-dd');
                    }
                    laydate.render({
                        elem: curObj[0],
                        type: 'date',
                        max: limit,
                        done: function(){
                            deliveryDateLimit(curObj, JSON.stringify(item));
                        }
                    });
                });
            }
            setBackBtn(1);
            $(".orderUpdate").show().siblings().hide();
            $(".orderUpdate").everyTime('0.5s','orderTotle',function(){
                var len = $(".orderUpdate:visible").length;
                if (len > 0) {
                    var orderTotle = 0;
                    $("#updateOrderList tbody tr").each(function(){
                        if ($(this).children().eq(8).html() != "")
                            orderTotle += Number($(this).children().eq(8).html());
                    });
                    $("#up_orderTlAmount").html(orderInfo.amount);
                } else {
                    $(".orderUpdate").stopTime("orderTotle");
                }
            });
        }
    });
}
// creator: 李玉婷，2020-06-11 17:10:31，订单修改确定
function orderUpdateSure(){
    var reqNum = 0;
    $("#updateOrderList [require]").each(function () {
        if ($(this).val() == "") {
            reqNum++;
        }
    });
    $("#updateOrderList tbody input").each(function () {
        if ($(this).val() == "") {
            reqNum++;
        }
    });
    $("#updateOrderList tbody tr .memoArea").each(function () {
        if ($(this).html() == "") {
            reqNum++;
        }
    });
    if (reqNum > 0) {
        bounce_Fixed.show($("#filledTip"));
    }else{
        let unTj = 0;
        $("#updateOrderList tbody tr").each(function () {
            var info = JSON.parse($(this).children("td:last").find(".hd").html());
            if (info == '6' && Number($.trim($(this).find("td").eq(5).children().val())) > Number(info.lower_quantity)){
                unTj++;
            }
        });
        if (unTj > 0) {//修改购买数量，不能小于统筹数量（不少于）
            layer.msg("购买数量不能多于统筹订购数");
        } else {
            var order = JSON.parse($("#up_supplier").html());
            var params = {
                "sn": $("#up_orderSn").val(),
                "orderId": order.id,
                "method": order.method,
                "supplier": order.supplier,
                "userId": sphdSocket.user.userID,
                "oid": sphdSocket.user.oid,
                "deleteIds": ""
            };
            var poOrdersItems = [], delsStr = "";
            var dels = $("#updateOrderList").data("dels");
            $("#updateOrderList tbody tr").each(function () {
                var matList = {};
                var info = JSON.parse($(this).children("td:last").find(".hd").html());
                var len = $(this).find("td").eq(9).find(".memoArea").length;
                var price = 0;
                if ($(this).find("td").eq(7).find("input").length > 0) {
                    price = $.trim($(this).find("td").eq(7).find("input").val());
                } else {
                    price = $.trim($(this).find("td").eq(7).find("div").html());
                }
                matList.id = info.item_id  ;
                matList.method = info.method  ;
                matList.supplierMaterial = info.material_supplier_id;
                matList.quantity =  $.trim($(this).find("td").eq(5).children().val());
                matList.deliveryDate  =  $.trim($(this).find("td").eq(6).children().val());
                matList.unitPrice =  price;
                if (info.method == 6) {
                    matList.itemId = info.item_id;
                    matList.material = info.id;
                    matList.soimId = info.id;//统筹的明细id
                    matList.versionNo = info.version_no;
                }
                if (len > 0) {
                    var memoJson = $(this).find("td").eq(9).find(".memoArea").data("memo");
                    if (memoJson) {
                        matList.invoicable = handleNull(memoJson.invoicable);
                        matList.invoiceCategory = handleNull(memoJson.invoiceCategory);
                        matList.taxInclusive = handleNull(memoJson.taxInclusive);
                        matList.memo = $.trim($(this).find("td").eq(9).find(".memoArea").html());
                    }
                } else {
                    matList.invoicable = handleNull(info.invoicable);
                    matList.invoiceCategory = handleNull(info.invoice_category);
                    matList.taxInclusive = handleNull(info.tax_inclusive);
                    matList.memo = $.trim($(this).find("td").eq(9).children("span").html());
                }
                poOrdersItems.push(matList);
            });
            poOrdersItems = JSON.stringify(poOrdersItems);
            params.poOrdersItems = poOrdersItems;
            params.prepayments = $("#up_advancePayList").data("prepayments");
            params.deliveryAddress = $("#edit_deliveryAddress").val();
            if (dels.length > 0) {
                for (var i = 0; i < dels.length ;i++) {
                    delsStr += dels[i] + ',';
                }
                delsStr = delsStr.substr(0, delsStr.length-1);
                params.deleteIds = "(" + delsStr +")";
            }
            $.ajax({
                url: '../po/orders',
                data: params,
                success: function (res) {
                    if(res.code == 400){
                        layer.msg(res.message)
                    } else {
                        bounce.cancel();
                        var key = $("#orderSearchKey").val();
                        $(".mainArea").show().siblings().hide();
                        getPurOrdersList(key,1);
                    }
                }
            })
        }

    }
}
// creator: 李玉婷，2021-09-18 08:12:00，库存预警接口
function getWarningList(cur) {
    $.ajax({
        url: '../po/warningList',
        data: {"pageNum": cur,"per": 20},
        success: function (res) {
            var list = res.data;
            var html = '';
            var shortNum = 0;
            if (list && list.length > 0) {
                for(var a=0;a<list.length;a++){
                    shortNum = 0;
                    if (Number(list[a].current_stock || "0").toFixed(4) < Number(list[a].minimum_stock || "0").toFixed(4)) {
                        shortNum = '<span class="ty-red">'+parseFloat(((Number(list[a].minimum_stock || "0")-Number(list[a].current_stock || "0")).toFixed(4)) || "0")
                    }
                    html +=
                        '<tr>' +
                        '    <td>'+ handleNull(list[a].name) +'</td>' +
                        '    <td>'+ handleNull(list[a].code) +'</td>' +
                        '    <td>'+ handleNull(list[a].model) +'</td>' +
                        '    <td>'+ handleNull(list[a].specifications) +'</td>' +
                        '    <td>'+ handleNull(list[a].unit) +'</td>' +
                        '    <td>'+ parseFloat(Number(list[a].current_stock || "0").toFixed(4)) +'</td>' +
                        '    <td>'+ parseFloat(Number(list[a].minimum_stock || "0").toFixed(4)) +'</td>' +
                        '    <td class="warn-red">'+ shortNum +'</td>' +
                        '    <td class="ty-td-control" onclick="getInTransit($(this))"><span class="ty-color-blue">'+ Number(list[a]["way_num"] || "0") +'</span></td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue" type="btn" data-fun="warningNewOrder" id="downorder">下单</span>' +
                        '        <span class="hd">'+ JSON.stringify(list[a]) +'</span>' +
                        '    </td>' +
                        '</tr>'
                }
            }
            $("#warningList tbody").html(html);
        }
    })
}

// creator: 李玉婷，2021-07-28 09:15:12，获取在途数量
function getInTransit(obj) {
    var info = JSON.parse(obj.parents("tr").find(".hd").html());
    var key = '';
    $("#inTransit [need]").each(function(){
        key = $(this).data("name");
        if( key == 'way_num') {
            $(this).html(info[key] || '0');
        } else {
            $(this).html(info[key]);
        }
    });
    $.ajax({
        "url":"../po/transitList",
        "data":{"id": info.id},
        success:function (res) {
            var list = res['data'];
            let html = ``, total = ``;
            if(list){
                for(var i = 0 ; i <list.length ; i++){
                    total += Number(list[i].transit_quantity || 0);
                    html +=
                        `<tr>
                            <td>${formatType(list[i].type)}</td>
                            <td>${list[i].quantity || '0'}/${list[i].transit_quantity || '0'}</td>
                            <td>${new Date(list[i].delivery_date).format('yyyy-MM-dd')}</td>
                            <td>${orderProgress(list[i].progress)}</td>
                            <td>${list[i].sn}</td>
                            <td>${list[i].name}</td>
                            <td>${list[i].create_name} &nbsp;&nbsp; ${ new Date(list[i].create_date).format('yyyy-MM-dd hh:mm:ss')}</td>
                        </tr>`;
                }
            }
            $("#inTransit table tbody").html(html);
            bounce.show($("#inTransit"));

        }
    })
}
// creator: 李玉婷，2022-02-23 13:36:37，新的销售订单所需的采购列表获取
function getNewSaleOrderList(cur) {
    $.ajax({
        url: '../po/slOrdersPo',
        data: {},//{"pageNum": cur,"per": 20},
        success: function (res) {
            var list = res.data;
            var html = '';
            if (list && list.length > 0) {
                for(var a=0;a<list.length;a++){
                    html +=
                        '<tr>' +
                        '    <td>'+ handleNull(list[a].name) +'</td>' +
                        '    <td>'+ handleNull(list[a].code) +'</td>' +
                        '    <td>'+ handleNull(list[a].model) +'</td>' +
                        '    <td>'+ handleNull(list[a].specifications) +'</td>' +
                        '    <td>'+ handleNull(list[a].unit) +'</td>' +
                        '    <td>'+ new Date(list[a].latest_delivery_date).format("yyyy-MM-dd") +'</td>' +
                        '    <td>'+ handleNull(list[a].lower_quantity) +'</td>' +
                        '    <td><span>'+ Number(list[a]["order_quantity"] || "0") +'</span>/<span onclick="getInTransit($(this))">'+ Number(list[a]["way_num"] || "0") +'</span></td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue" type="btn" data-fun="newOrderFromSale">下单</span>' +
                        '        <span class="hd">'+ JSON.stringify(list[a]) +'</span>' +
                        '    </td>' +
                        '</tr>';
                }
            }
            $(".module1 tbody").html(html);
        }
    })
}
// creator: lyt 2022-11-29 收货地点列表
function getReceiveAddressList(obj, abled, val) {
    $.ajax({
        "url":"../dac/list",
        "data":{ 'enable': abled },
        success:function (res) {
            if(res.success !== 200){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data']['data'] || [], aStr=`<option value=""></option>`, bStr="";
            let cStr = `<option value="0">本公司到供应商处自提</option>`;
            list.forEach((item)=>{
                if (item.type === 1) {
                    aStr += `<option value="${ item.id }">${ item.address }</option>`;
                } else if (item.type === 2){
                    bStr += `<option value="${ item.id }">${ item.address }</option>`;
                }
            })
            obj.html(aStr+ bStr +cStr);
            if (list.length === 1) obj.val(list[0].id);
            if (val || val === 0) obj.val(val);
        }
    });
}
// creator: lyt 2022-12-27 收货地点列表
function getMtAddressList(id) {
    $.ajax({
        "url":"../supplier/materialAddress.do",
        "data":{ 'id': id },
        success:function (res) {
            if(res.success !== 1){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data']['addresses'] || [], val = ``;
            let info = JSON.parse($("#matBuyInfo tbody tr:eq(0) td:last").find(".hd").html());
            info.address = list;
            $("#matBuyInfo tbody tr:eq(0) td:last").find(".hd").html(JSON.stringify(info))
            if (list.length === 1) {
                val = list[0].id
            }
            getReceiveAddressList($("#deliveryAddress"),1, val)
        }
    });
}
// creator: lyt 2022-12-19 系统需查找运费元素是否包含该地点
function isIncludeAddress(obj, type) {
    let val = obj.val();
    let able = false, count = 0;
    let isEdit = $("#isEdit").val();
    let trArr = ``;
    if (isEdit === "1") {
        trArr = $("#matBuyInfo tbody tr");
        if (type && type !== "") {
            let len = $("#matBuyInfo tbody tr").length - type -1;
            trArr = $("#matBuyInfo tbody tr:gt("+ len +")");
        }
    } else if (isEdit === "2"){
        trArr = $("#updateOrderList tbody tr");
    }
    $(".conditional i").attr("class", "fa fa-square-o");
    trArr.each(function(){
        let info = JSON.parse($(this).children("td:last").find(".hd").html());
        let tdObj = $(this).children("td").eq(7);
        let priceJson = JSON.parse(tdObj.find(".hd").html());
        let flag = -1;
        if (val === "0" && $(".repStepThr").is(":visible")) {
            flag = info.inclusive_freight !== "3"? -1: 1;
        } else {
            let list = info.address || [];
            flag = list.findIndex((item)=>{return Number(item.id) === Number(val)})
        }
        if (flag === -1) {
            count++;
            able = true
        } else {
            if (tdObj.find(".modPrice").length > 0){
                let mtPrice = ``;
                if (priceJson.price_stable === '1') {
                    mtPrice = priceJson.mtPriceVal
                } else if (priceJson.price_stable === '2'){
                    mtPrice =
                        '<div class="itemPrice">' +
                        '<input value="' + priceJson.mtPriceVal + '" placeholder="请录入" onkeyup="setTotalPrice(this)"/>' +
                        '<i onclick="clearCon($(this))">x</i>' +
                        '</div>';
                }
                tdObj.children("div").html(mtPrice);
            }
        }
    })
    if (able) {
        if (trArr.length === 1) {
            $(".tip1").show().siblings().hide();
        } else {
            $(".tip3 .outNum").html(count);
            $(".tip3").show().siblings().hide();
        }
        if (type && type !== "") {
            $(".tip2 .outNum").html(count);
            $(".tip2").show().siblings().hide();
        }
        bounce.show($("#addressChangeTip"))
    }
}
function addressChangeOk(fun){
    let able = false;
    if (fun === 2) {
        if ($(".conditional .fa-check-square-o").length === 0) {
            return false;
        } else {
            if ($(".conditional .fa-check-square-o").data("val") === 2) {
                able = true;
            }
        }
    }
    bounce.cancel();
    let isEdit = $("#isEdit").val();
    let obj = ``, trArr = ``;
    if (isEdit === "1") {
        obj = $("#deliveryAddress");
        trArr = $("#matBuyInfo tbody tr");
    } else if (isEdit === "2"){
        obj = $("#edit_deliveryAddress");
        trArr = $("#updateOrderList tbody tr");
    }
    if (able) {
        let val = obj.val();
        trArr.each(function(){
            let info = JSON.parse($(this).children("td:last").find(".hd").html());
            let list = info.address || [];
            let flag = -1;
            if (val === 0) {
                flag = info.inclusive_freight !== "3"? -1: 1;
            } else {
                flag = list.findIndex((item)=>{return Number(item.id) === Number(val)})
            }
            let tdObj = $(this).children("td").eq(7);
            let priceJson = JSON.parse(tdObj.find(".hd").html());
            if (flag === -1) {
                if (tdObj.find(".modPrice").length <= 0) {
                    tdObj.children("div").html(`<input type="text" value='' placeholder='参考价格${priceJson.mtPriceVal}' class="modPrice" oninput="setTotalPrice(this)" />`);
                    tdObj.find("input").focus();
                }
            } else {
                let mtPrice = ``;
                if (priceJson.price_stable === '1') {
                    mtPrice = priceJson.mtPriceVal
                } else if (priceJson.price_stable === '2'){
                    mtPrice =
                        '<div class="itemPrice">' +
                        '<input value="' + priceJson.mtPriceVal + '" placeholder="请录入" onkeyup="setTotalPrice(this)"/>' +
                        '<i onclick="clearCon($(this))">x</i>' +
                        '</div>';
                }
                tdObj.children("div").html(mtPrice);
            }
        })
    } else {
        obj.val("");
    }
}
// creator: 李玉婷，2020-05-16 10:40:47，返回提示
function backJump(num) {
    if ($(".orderDetails").is(":visible")) {
        var key = $("#orderSearchKey").val();
        setBackBtn(0)
        getPurOrdersList(key, 1);
        $(".mainArea").show().siblings().hide();
    } else if ($(".repStepOne").is(":visible") || $(".repStepTwo").is(":visible")) {
        turnJump(num);
    }else {
        $("#backTip").data("type",num);
        bounce.show($("#backTip"));
    }
}
// creator: 李玉婷，2020-05-16 11:06:52，返回确定
function turnPage() {
    bounce.cancel();
    var type = $("#backTip").data("type");
    turnJump(type);
}
// creator: 李玉婷，2022-04-08 13:08:32，页面跳转
function turnJump(type) {
    if (type == 1 && !$(".repStepOne").is(":visible")){
        $(".replenish>div:visible").hide().prev().show();
        var isH = $(".replenish>div:visible").hasClass("repStepOne");
        if (isH){
            setBackBtn(1);
        }else{
            setBackBtn(2);
        }
    }else{
        var key = $("#orderSearchKey").val();
        setBackBtn(0)
        getPurOrdersList(key, 1);
        $(".mainArea").show().siblings().hide();
    }
}
// creator: hxz，2021-06-17  金额转大写
function turnUpperKey(obj){
    let str = `大写 ${ convertCurrency(obj.val()) }`
    $(".upperKey").html(str).attr("title",str);
}
// creator: hxz，2021-06-17  小写金额转大写金额
function convertCurrency(money) {
    //汉字的数字
    var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖');
    //基本单位
    var cnIntRadice = new Array('', '拾', '佰', '仟');
    //对应整数部分扩展单位
    var cnIntUnits = new Array('', '万', '亿', '兆');
    //对应小数部分单位
    var cnDecUnits = new Array('角', '分', '毫', '厘');
    //整数金额时后面跟的字符
    var cnInteger = '整';
    //整型完以后的单位
    var cnIntLast = '元';
    //最大处理的数字
    var maxNum = 999999999999999.9999;
    //金额整数部分
    var integerNum;
    //金额小数部分
    var decimalNum;
    //输出的中文金额字符串
    var chineseStr = '';
    //分离金额后用的数组，预定义
    var parts;
    if (money == '') { return ''; }
    money = parseFloat(money);
    if (money >= maxNum) {
        //超出最大处理数字
        return '';
    }
    if (money == 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger;
        return chineseStr;
    }
    //转换为字符串
    money = money.toString();
    if (money.indexOf('.') == -1) {
        integerNum = money;
        decimalNum = '';
    } else {
        parts = money.split('.');
        integerNum = parts[0];
        decimalNum = parts[1].substr(0, 4);
    }
    //获取整型部分转换
    if (parseInt(integerNum, 10) > 0) {
        var zeroCount = 0;
        var IntLen = integerNum.length;
        for (var i = 0; i < IntLen; i++) {
            var n = integerNum.substr(i, 1);
            var p = IntLen - i - 1;
            var q = p / 4;
            var m = p % 4;
            if (n == '0') {
                zeroCount++;
            } else {
                if (zeroCount > 0) {
                    chineseStr += cnNums[0];
                }
                //归零
                zeroCount = 0;
                chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
            }
            if (m == 0 && zeroCount < 4) {
                chineseStr += cnIntUnits[q];
            }
        }
        chineseStr += cnIntLast;
    }
    //小数部分
    if (decimalNum != '') {
        var decLen = decimalNum.length;
        for (var i = 0; i < decLen; i++) {
            var n = decimalNum.substr(i, 1);
            if (n != '0') {
                chineseStr += cnNums[Number(n)] + cnDecUnits[i];
            }
        }
    }
    if (chineseStr == '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger;
    } else if (decimalNum == '') {
        chineseStr += cnInteger;
    }
    return chineseStr;
}
// creator: 李玉婷，2021-10-29 8:11:15，购买理由
function formatType (type) {
    let str = ''
    switch (type) {
        case '2':
        case 2:
            str = '库存预警'
            break
        case '3':
        case 3:
            str = '个人申购的新材料'
            break
        case '1':
        case 1:
            str = '补货'
            break
        case '5':
        case 5:
            str = '零星采购'
            break
        case '6':
        case 6:
            str = '销售新订单所需的采购'
            break
        default:
    }
    return str
}
// creator: 李玉婷，2021-12-07 21:36:02，订单进度
function orderProgress(state){
    let str = ''
    switch (state) {
        case '0':
        case 0:
            str = '审批驳回'
            break
        case '1':
        case 1:
            str = '订单已提交，待审批'
            break
        case '2':
        case 2:
            str = '已下单，待到货'
            break
        case '3':
        case 3:
            str = '已到货，待检验'
            break
        case '4':
        case 4:
            str = '检验ok，待入库'
            break
        case '5':
        case 5:
            str = '检验不合格，待提交让步评审'
            break
        case '6':
        case 6:
            str = '检验不合格，待让步评审'
            break
        case '7':
        case 7:
            str = '让步评审未通过'
            break
        case 'Z':
            str = '完结'
            break
        default:
    }
    return str
}
// creator: 李玉婷，2021-06-29 13:54:49，保留3位小数
function testNumSize3(obj){
    obj.value = obj.value.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符
    obj.value = obj.value.replace(/^\./g,"");  //验证第一个字符是数字而不是.
    obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/,'$1$2.$3');//只能输入两个小数
    if(obj.value.indexOf(".")< 0 && obj.value !=""){//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        obj.value= parseFloat(obj.value);
    }
}
laydate.render({elem: '.planTime',type: 'date'});













