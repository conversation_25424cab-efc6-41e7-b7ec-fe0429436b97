//浮窗跳转查看订单详情
var approvalProcessId = 0; // 当前审批的流程
$(function(){
    // 获取详情
    var orderID = getUrlParam("id");
    var sendType = Number(getUrlParam("t")); // 0-申请人 ， 1-审批人，2-查询 ,3 -消息
    $(".ctrl_t" + sendType).show().siblings().hide();
    getDetails(orderID)
    var listener = [
        sphdSocket.subscribe('purchaseOrderMessage', function(data){
            console.log('purchaseOrderMessage recieved OK:'+data);
            var res = JSON.parse(data);
            var status = res['status'];
            var content = res['content'];
            loading.close();
            // alert(data);
            window.parent.layer.msg(content);
            window.parent.hideDetailsPage()

        }, function(){console.log('Socket check Error:')})
    ]

    // 驳回的理由选择
    $("#approveOrd3 p").click(function() {
        var faObj = $(this).find(".fa");
        if(faObj.length >0){
            $("#approveOrd3 .fa").attr("class", "fa fa-circle-o");
            faObj.attr("class", "fa fa-dot-circle-o");
            var rejectReasion =  faObj.data("type") ;
            if(rejectReasion == 8){
                $("#rejectReasionDesc").show();
                $(".txtMax").html("0/80").show();
            }else{
                $("#rejectReasionDesc").val("").hide();
                $(".txtMax").hide()

            }
        }
    })
})
function getDetails(orderID) {
    $(".orderInfo tbody").children("tr:gt(0)").remove();
    $.ajax({
        "url":"../purchaseOrderApproval/getPurchaseOrderDetail.do",
        "data":{"ordersId": orderID },
        success:function(data) {
            $(".ty-mainData").show();
            var res = data['data'];
            var ordersItem = res['ordersItem']; // 明细列表
            var orders = res['orders'] && res['orders'][0]; // 订单详情
            var prepayments = res['prepayments'] || []; // 信息列表
            var approvalProcess = res['approvalProcess'] || []; // 报销流程
            $(".show_supplierName").html(orders['supplier_name']);
            $(".show_amount").html(orders['amount']);
            $(".show_sn").html(orders['sn']);
            let approveStatus = orders['approveStatus']; // 1-刚提交， 2-审批通过 3-驳回 4撤销的
            var payStr = "", ordersItemStr = "", len = prepayments.length ;
            var approvalProcessStr = "<p><span>申请人：</span><span>"+ orders['create_name'] +"</span> <span>"+ (new Date(orders['create_date']).format("yyyy-MM-dd hh:mm:ss"))  +"</span></p>";

            if (approvalProcess && approvalProcess.length > 0) {
                approvalProcessId = approvalProcess[approvalProcess.length-1]['id'];
                for (var j = 0; j < approvalProcess.length; j++) {
                    var item = approvalProcess[j] ;
                    var status = item['approveStatus']
                    if( status== 1){
                        approvalProcessStr += "<p>等待 "+ item['userName'] +" 审批</p>";
                    }else if( status== 2 || status == 3){
                        approvalProcessStr += "<p><span>审批人：</span><span>"+ item['userName'] +"</span> <span>"+ (new Date(item['handleTime']).format("yyyy-MM-dd hh:mm:ss"))  +"</span></p>";
                    }
                    if(status == 3){
                        approvalProcessStr += "<div class='ty-color-red'>" +
                            "<p>驳回理由：</p>"+
                            "<span style='width:200px;word-break: break-all;'>"+ item['reason'] +"</span>"+
                            "</div>"
                    }
                }
            }
            payStr += `<p><span class="gapR">收货地址</span> ${orders.delivery_address === 0 ? '本公司到供应商处自提':handleNull(orders.address)}</p>`;
            if (prepayments && prepayments.length > 0) {
                for (var i = 0; i < prepayments.length; i++) {
                    payStr += `<p>
                                    <span class="t1">计划预付款日期${ new Date(prepayments[i]['planDate']).format("yyyy-MM-dd") }</span>
                                    <span>计划预付款金额${ prepayments[i]['planAmount'] }</span>
                                </p>`;
                }
            }
            payStr = `<tr><td style="vertical-align:top;">${ payStr }</td><td>${ approvalProcessStr }</td></tr>`
            $(".orderInfo tbody").append(payStr);
            if(ordersItem && ordersItem.length > 0){
                for (var k = 0 ; k < ordersItem.length ; k++) {
                    var itemO = ordersItem[k]
                    let inclusive_freight = Number( itemO['inclusive_freight'] );
                    let inclusive_freightStr = ''
                    if(inclusive_freight === 1 || inclusive_freight === 2){
                        inclusive_freightStr = '是';
                    }else if(inclusive_freight === 3){
                        inclusive_freightStr = '否';
                    }
                    var totalPrice = Number(itemO['unit_price'] || itemO['unit_price_notax']) * Number(itemO['quantity'])
                    ordersItemStr += "  <tr>" +
                        "    <td>"+ itemO['name'] +"</td>" +
                        "    <td>"+ itemO['code'] +"</td>" +
                        "    <td>"+ itemO['model'] +"</td>" +
                        "    <td>"+ itemO['specifications'] +"</td>" +
                        "    <td>"+ (itemO['unit']) +"</td>" +
                        "    <td>"+ (new Date(itemO['delivery_date']).format("yyyy-MM-dd"))  +"</td>" +
                        "    <td>"+ itemO['quantity'] +"</td>" +
                        "    <td>"+ (itemO['unit_price'] || itemO['unit_price_notax']) +"</td>" +
                        "    <td>"+ totalPrice.toFixed(2) +"</td>" +
                        "    <td>"+ itemO['memo'] +"</td>" +
                        "    <td>"+ inclusive_freightStr +"</td>" +
                        "    <td>"+
                        "       <span class='ty-color-blue' onclick=\"buyReason($(this))\">"+ changeBuy(itemO['type']) +"</span>" +
                        "    </td>" +
                        "    <td>"+
                        "       <span class='ty-color-blue' onclick=\"getInTransit($(this))\">"+ (itemO['way_num'] || '0') +"</span>" +
                        "    </td>" +
                        "    <td>" +
                        "<span class='ty-color-blue' onclick=\"detailsCompare($(this))\">查看</span>" +
                        "<span class='hd'>"+ JSON.stringify(itemO) +"</span>" +
                        "</td>" +
                        "</tr>";
                }
            }
            $(".ordersItem").html(ordersItemStr);
            $("#mainshow").show();
        }
    })
}
function chargePriceShow(supInfo) {
    var supplier = supInfo ;
    var ttl = "", info = "";
    var isStable = supplier["price_stable"];
    var canInvoice = String(supplier["material_invoiceable"]);
    var incoiceTypeVal = supplier["material_invoice_category"];

    if(isStable === "1"){ //稳定
        if(canInvoice === "1"){ // 能开票
            ttl = "已约定的单价";
            info += "开票价";
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "已约定的单价";
            info += "不开票价";
        }
    }else if(isStable === "2"){ // 变动频繁
        if(canInvoice === "1"){ // 能开票
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                ttl = "参考单价";
                info += "开票价";
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                ttl = "参考单价";
                info += "开票价";
            }
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "参考单价";
            info += "不开票价";

        }else if(canInvoice === "2"){ // 不确定
            ttl = "参考单价";
            info += "不开票价";
        }
    }
    var price = supplier["unit_price_notax"]
    if(supplier["is_tax"] === "1"){ // 含税
        price = supplier["unit_price"];
    }
    info += price + "元";
    var infoall = ""
    switch (String(supplier["inclusive_freight"])){
        case "1":
            infoall = "含运费的" + info ;
            break;
        case "2":
            infoall = "含运费的" + info + "，但材料到本市后需我司自提";
            break;
        case "3":
            infoall = "不含运费的" + info ;
            break;
        default:
            infoall = info ;
    }
    return { "ttl":ttl , "info":infoall }

}
function approveOrdBtn(type) {
    if(type == 3){
        bounce.show($("#approveOrd3"));
        $("#approveOrd3 .fa").attr("class", "fa fa-circle-o");
        $("#rejectReasionDesc").val("").hide();
        $(".txtMax").hide()
    }else{
        approveOrd(2)
    }
}
function approveOrd(type) {
    var json={
        'session':sphdSocket.sessionid,
        'approvalProcessId':approvalProcessId,
        'userId':sphdSocket.user.userID,
        'rejectReasion':'',
        'approvalStatus':type,
        'rejectReasionDesc':''
    }
    if(type == 3) {
        // alert('判断驳回')
        let rejectReasion =  $("#approveOrd3 .fa-dot-circle-o").data("type") ;
        console.log(rejectReasion);
        if(!rejectReasion){
            layer.msg("请选择驳回理由！");
            return false;
        }
        json['rejectReasion'] = rejectReasion
        json['rejectReasionDesc'] = $(".reasonCon i[data-type='"+ rejectReasion +"']").next().html();
        if(rejectReasion == 8){
            let rejectReasionDesc = $("#rejectReasionDesc").val();
            json['rejectReasionDesc'] += ":" + rejectReasionDesc
        }
    }
    console.log('审批传值：')
    console.log(json)
    loading.open();
    sphdSocket.send('purchaseOrderApproval',json);
    bounce.cancel();
}
function cancelIframe() {
    window.parent.hideDetailsPage();
    _this.sphdSocket.unsubscribe(item)
}
function wordsMaxTip() {
    let str = $("#rejectReasionDesc").val();
    if(str.length >80){
        layer.msg("理由字数不得超过80个字");
        str = str.substr(0,80)
        $("#rejectReasionDesc").val(str);
    }
    $(".txtMax").html(str.length + "/80" )
}

// creator: 李玉婷，2020-04-14 19:52:32，信息对比
function detailsCompare(obj) {
    var info = JSON.parse(obj.siblings(".hd").html());
    var html =
        '<tr>' +
        '    <td>' + handleNull(info.name) + '</td>' +
        '    <td>' + handleNull(info.code) + '</td>' +
        '    <td>' + handleNull(info.model) + '</td>' +
        '    <td>' + handleNull(info.specifications) + '</td>' +
        '    <td class="createInfo">' + (info.createName || "") + '&nbsp;&nbsp;' + new Date(info.createDate).format('yyyy/MM/dd hh:mm:ss') + '</td>' +
        '    <td>' + handleNull(info.unit) + '</td>' +
        '    <td>' + handleNull(info.current_stock) + '</td>' +
        '    <td>' + handleNull(info.minimum_stock) + '</td>' +
        '    <td>' + handleNull((info['location_number'] || "0")) + '个</td>' +
        '    <td>' + handleNull((info['supplier_number'] || "0")) + '个</td>' +
        '</tr>';
    $("#mtDetailsCompare tbody").html(html);
    bounce.show($("#detailsCompare"));
    getSupplierList(info.id || info.material_id, $("#compareSupplier"), 0);
}

// creator: 李玉婷，2020-05-07 14:39:18，获取供应商列表
function getSupplierList(mtId, curObj, type) {
    var oid = sphdSocket.user.oid;
    var data = { 'oid': oid, 'mtId': mtId, 'enabled': 1  };
    $.ajax({
        "url":"../po/suppliers",
        "data":data,
        success:function (res) {
            var list = res['data'];
            let tabs = ``;
            if(list){
                for(var i = 0 ; i <list.length ; i++){
                    var info = list[i];
                    tabs += `<div class="tabs__item" data-type="${type}" onclick="setSupplyInfo($(this))">供应商${(i+1)}<span class="hd">${JSON.stringify(info)}</span></div>`;
                }
            }
            curObj.find(".tabs__nav").html(tabs);
            curObj.find(".tabs__item").eq(0).click();
        }
    })
}
// creator: 李玉婷，2021-08-03 09:14:07，供应商赋值
function setSupplyInfo(obj) {
    let type = obj.data("type");
    let info = JSON.parse(obj.find(".hd").html());
    let str =
        `<div class="supplierInfo"><span class="hd">${JSON.stringify(info)}</span>`;
    var cell1 = "", cell2 = "", cell3 = "", cell4 = "", cell5 = "", cell6 = "", cell7 = "", cell8 = "", cell9 = "";
    obj.addClass("is-active").siblings().removeClass("is-active");
    if (type == 1) {
        str +=
            '   <div class="clear"><div class="ty-left" onclick="getSupplyOrder($(this))">' +
            '       <span class="fa fa-circle-o"></span>' +
            '       <span class="spTl">供应商名称</span>' +
            '       <span class="spCd">'+ info.full_name +'</span>' +
            '   </div>';
    }else {
        str +=
            '   <div class="clear"><div class="ty-left">' +
            '       <span class="spTl">供应商名称</span>' +
            '       <span class="spCd">'+ info.full_name +'</span>' +
            '   </div>';
    }
    str +=
        '   <div class="ty-right">' +
        '       <span class="spTl">简称</span>' +
        '       <span class="spCd">'+ info.supplier_name +'</span>' +
        '       <span class="spTl">代号</span>' +
        '       <span>'+ info.code_name +'</span>' +
        '   </div>' +
        '</div></div>';
    if(info["has_contact"]){ // 签订采购合同
        cell1 = "采购合同已签订";
        if(info["contract_sn"]){ cell1 += "，合同编号"+ info["contract_sn"]; }
        if(info["valid_date"]){ cell1 += "，有效期至"+ new Date(info["valid_date"]).format('yyyy-MM-dd'); }
        if(info["sign_date"]){ cell1 += "，签署日期为"+ new Date(info["sign_date"]).format('yyyy-MM-dd') ; }
    }else{
        cell1 = "与该供应商暂无采购合同 ";
    }
    if(info["is_include"] === "1"){
        cell2 = "本材料已包含于采购合同中"
    }else if(info["is_include"] === "0"){
        cell2 = "本材料不包含于采购合同中"
    }
    if(info["package_method"] === "1"){
        cell3 += "该供应商供应的本材料包装方式基本固定"
    }else if(info["package_method"] === "2"){
        cell3 += "该供应商供应的本材料包装方式型式不定"
    }else {
        cell3 += "该供应商供应的本材料包装方式暂不清楚"
    }
    if(info["invoice_able"] == 1){
        $(".incoiceType").show();
        if(info["invoice_category"] == 1){
            if(info["tax_rate"]){
                cell4 = "该供应商能开税率为"+ info["tax_rate"] +"%的增值税专用发票"
            }else{
                cell4 = "该供应商能开增值税专用发票"
            }
        }else{
            cell4 = "该供应商仅能开普通发票"
        }
    }else{
        cell4 = "该供应商不能开发票"
    }
    if(info["charge_acceptable"] == 1){
        cell5 += "可接受挂账";
        if(info["charge_period"]){
            cell5 += ",账期"+ info["charge_period"] +"天";
        }
        if(info["charge_begin"] == "1"){
            cell5 += ",自货物入库之日起计算";
        }else if(info["charge_begin"] == "2"){
            cell5 += ",自发票入账之日起计算";
        }
    }else{
        cell5 += "不接受挂账";
    }
    if(info["draft_acceptable"] == 1){
        cell6 += "可接收承兑汇票";
    }else if(info["draft_acceptable"] == 2){
        cell6 += "不确定能接受承兑汇票";
    }
    if(info["price_stable"] === "1"){
        cell7 += "价格相对稳定";

    }else if(info["price_stable"] === "2"){
        cell7 += "价格变动较频繁";
    }
    if(String(info["material_invoiceable"]) === "1"){
        if(info["material_invoice_category"] === "1"){
            cell8 += "给开税率为"+ info["tax_rate"] +"%的增值税专用发票";
        }else if(info["material_invoice_category"] === "2"){
            cell8 += "给开其他发票";
        }
    }else if(String(info["material_invoiceable"]) === "0"){
        cell8 += "不给开发票";
    }
    if(info["is_imprest"] === "1"){ // 0-不确定,1-需要,2-不需要
        cell9 += "需要预付款";
    }else if(info["is_imprest"] === "2"){
        cell9 += "无需预付款";
    }else if(info["is_imprest"] === "0"){
        cell9 += "不确定是否需预付款";
    }
    var priceInfo = chargePriceShow(info);
    var priceElem = priceInfo.ttl + '&nbsp;&nbsp;' + priceInfo.info;
    str +=
        '<div class="npSupper"><table class="ty-table ty-table-control spTb">' +
        '    <tbody>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="9">'+ priceElem +'</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3">上次购买</td>' +
        '        <td colspan="3">本年购买</td>' +
        '        <td colspan="3">总计购买</td>' +
        '    </tr>' +
        '    <tr class="td-blue">' +
        '        <td>单价</td>' +
        '        <td>数量</td>' +
        '        <td>日期</td>' +
        '        <td>次数</td>' +
        '        <td>数量</td>' +
        '        <td>平均单价</td>' +
        '        <td>次数</td>' +
        '        <td>数量</td>' +
        '        <td>平均单价</td>' +
        '    </tr>' +
        '    <tr class="td-blue">' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td></td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '        <td>0</td>' +
        '    </tr>' +
        '    <tr class="td-blue td-lf">' +
        '        <td colspan="9">备注：上次、本年及总计的购买，均指已完结的订单，且均以订单的创建日期为准，其中单价取实际支付金额所对应的单价。</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3">'+ cell1 +'</td>' +
        '        <td colspan="3">'+ cell2 +'</td>' +
        '        <td colspan="3">'+ cell3 +'</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3">'+ cell4 +'</td>' +
        '        <td colspan="3">'+ cell5 +'</td>' +
        '        <td colspan="3">'+ cell6 +'</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3">'+ cell7 +'</td>' +
        '        <td colspan="3">'+ cell8 +'</td>' +
        '        <td colspan="3">'+ cell9 +'</td>' +
        '    </tr>' +
        '    <tr class="td-yellow">' +
        '        <td colspan="3">采购周期 '+ (info.perchase_cycle || "0") +'天</td>' +
        '        <td colspan="3">最低采购量 '+ parseFloat(Number(info.minimum_purchase).toFixed(4)) +'</td>' +
        '        <td colspan="3">最低库存/当前库存 '+ parseFloat(Number(info.minimum_stock || "0").toFixed(4))+'/'+parseFloat(Number(info['current_stock'] || "0").toFixed(4)) +'</td>' +
        '    </tr>' +
        '    </tbody>' +
        '</table></div>';
    obj.parents(".supplierList").find(".tabs__content").html(str);
}
// creator: 李玉婷，2021-07-28 09:15:12，获取在途数量
function getInTransit(obj) {
    var info = JSON.parse(obj.parents("tr").find(".hd").html());
    var key = '';
    $("#inTransit [need]").each(function(){
        key = $(this).data("name");
        $(this).html(info[key]);
    });
    $.ajax({
        "url":"../po/transitList",
        "data":{"id": info.material_id},
        success:function (res) {
            var list = res['data'];
            let html = ``, total = ``;
            if(list){
                for(var i = 0 ; i <list.length ; i++){
                    total += Number(list[i].transit_quantity || 0);
                    html +=
                        `<tr>
                            <td>${formatType(list[i].type)}</td>
                            <td>${list[i].quantity}/${list[i].transit_quantity || '0'}</td>
                            <td>${new Date(list[i].delivery_date).format('yyyy-MM-dd')}</td>
                            <td>${orderProgress(list[i].progress)}</td>
                            <td>${list[i].sn}</td>
                            <td>${list[i].name}</td>
                            <td>${list[i].create_name} &nbsp;&nbsp; ${ new Date(list[i].create_date).format('yyyy-MM-dd hh:mm:ss')}</td>
                        </tr>`;
                }
            }
            $("#inTransit table tbody").html(html);
            bounce.show($("#inTransit"));

        }
    })
}
// creator: 李玉婷，2021-10-29 8:11:15，在途数量-购买理由
function formatType (type) {
    let str = ''
    switch (type) {
        case '2':
        case 2:
            str = '库存预警'
            break
        case '3':
        case 3:
            str = '个人申购的新材料'
            break
        case '1':
        case 1:
            str = '补货'
            break
        case '5':
        case 5:
            str = '零星采购'
            break
        case '6':
        case 6:
            str = '销售新订单所需的采购'
            break
        default:
    }
    return str
}
// creator: 李玉婷，2021-07-28 10:15:12，购买理由
function buyReason(obj) {
    var info = JSON.parse(obj.parents("tr").find(".hd").html());
    let ttl = ``;
    if (info.type == '1') {
        ttl = `购买理由-补货`;
    } else if (info.type == '2'){
        ttl = `购买理由-库存预警的材料`;
    } else if (info.type == '6'){
        ttl = `购买理由-新的销售订单所需的采购`;
    }
    $("#buyReason .bonceHead span").html(ttl);
    let tdStr = ``;
    if(info.type == '6') {
        tdStr =
            `<tr>
                <td>客户订购数</td>
                <td>${handleNull((info['quantity'] || "0"))}</td>
                <td></td>
            </tr>
            <tr>
                <td>统筹订购数</td>
                <td>${handleNull((info['lower_quantity'] || "0"))}</td>
                    <td></td>
            </tr>
            <tr>
                <td>已订购数</td>
                <td>${handleNull((info['order_quantity'] || "0"))}</td>
                <td><span class="ty-color-blue">相应的采购订单</span></td>
            </tr>`;
    } else {
        tdStr =
            `<tr>
                <td>当前库存</td>
                <td>${handleNull((info['current_stock'] || "0"))}</td>
            </tr>
            <tr>
                <td>最低库存</td>
                <td>${handleNull((info['minimum_stock'] || "0"))}</td>
            </tr>`;
    }

    $("#buyReason tbody").html(tdStr);
    bounce.show($("#buyReason"));
}
// creator: 李玉婷，2021-10-22 14:34:26，购买理由输出
function changeBuy(num) {
    var str = '';
    switch (num) {
        case 1:
        case '1':
            str = '补货';
        break;
        case 2:
        case '2': str = '预警';
            break;
        case '6':
        case 6: str = '新单';
            break;
        default: break;
    }
    return str;
}
// creator: 李玉婷，2021-12-07 21:36:02，订单进度
function orderProgress(state){
    let str = ''
    switch (state) {
        case '0':
        case 0:
            str = '审批驳回'
            break
        case '1':
        case 1:
            str = '订单已提交，待审批'
            break
        case '2':
        case 2:
            str = '已下单，待到货'
            break
        case '3':
        case 3:
            str = '已到货，待检验'
            break
        case '4':
        case 4:
            str = '检验ok，待入库'
            break
        case '5':
        case 5:
            str = '检验不合格，待提交让步评审'
            break
        case '6':
        case 6:
            str = '检验不合格，待让步评审'
            break
        case '7':
        case 7:
            str = '让步评审未通过'
            break
        case 'Z':
            str = '完结'
            break
        default:
    }
    return str
}







