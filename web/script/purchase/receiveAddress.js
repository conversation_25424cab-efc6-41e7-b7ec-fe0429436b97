var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#contactSeeDetail"));
bounce_Fixed2.cancel();
$(function () {
    getReceiveAddressList(1,1);
    getReceiveAddressList(2,1);
    $("body").on("click",".funBtn,.funbtn,.linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
    $(".chooseCusCon").click(function () {
        let target = $(this).data("target");
        $("#target").val(target)
        bounce_Fixed.show( $("#chooseCusContact") );
        getCusContactList();
    });
    $("#chooseCusContact").on("click", '.fa', function () {
        $("#chooseCusContact").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).attr("class", "fa fa-dot-circle-o")
    })
    $(".clearInputVal").on({
        "mousedown": function () {
            $(this).prev().val("");
        }
    });
})
// creator: lyt 2022-11-29 收货地点列表
function getReceiveAddressList(type, abled) {//0-停用状态 1-启用状态
    $.ajax({
        "url":"../dac/list",
        "data":{ 'type': type, 'enable': abled },
        success:function (res) {
            if(res.success !== 200){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data']['data'] || [], str=``, addressStr = ``, areaStr = ``;
            list.forEach((item)=>{
                if (item.enabled === '1') {
                    str += ` <tr>
                    <td>${ item.address }</td>
                    <td>${ item.contact || '' }</td>
                    <td>${ item.telephone }</td>
                    <td>
                        <span data-fun="receive_edit" class="ty-color-blue funbtn">修改</span>
                        <span data-fun="receive_record" class="ty-color-blue funbtn">修改记录</span>
                        <span data-fun="receive_stop" class="ty-color-blue funbtn">停用</span>
                        <span data-fun="receive_del" class="ty-color-red funbtn">删除</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr> `;
                    if (item.type === 1) {
                        addressStr += str;
                    } else if (item.type === 3) {
                        areaStr += str;
                    }
                } else {
                    str += `
                <tr>
                    <td>${ item.address }</td>
                    <td>${ handleNull(item.enabled_name)} ${new Date(item.enabled_time).format("yyyy-MM-dd hh:mm:ss") }</td>
                    <td>
                        <span data-fun="receive_restart" class="ty-color-blue funbtn">启用</span>
                        <span data-fun="receive_record" class="ty-color-blue funbtn">修改记录</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
                `
                }
            })
            if (abled === 1) {
                if (type === 1) {
                    $(".receiveAddressList tbody").html(str);
                } else {
                    $(".receiveAreaList tbody").html(str);
                }
            } else {
                $(".receiveStoppedList tbody tr:gt(0)").remove();
                $(".receiveStoppedList tbody").append(str);
                bounce.show($("#receiveStopped"))
            }
        }
    });
}
/*creator:lyt 2022/11/14 0014 下午 4:46 收货信息*/
function addAddress(){
    $("#newReceiveInfo").data("type", "new")
    bounce.show($("#newReceiveInfo"))
    $("#newReceiveInfo input").val("");
    $("#newReceiveInfo .bonceHead span").html("新增收货地址");
    $("#ReceiveName")
        .val("").data('orgData',"")
        .siblings(".hd").html("") ;
}
/*creator:lyt 2022/11/14 0014 下午 4:54 收货信息确定*/
function addressAddSure(obj){
    let funType = ``;
    let source = obj.data("source")
    let abled = true, url = '../dac/add';
    let curObj = $("#newReceiveInfo");
    let contactInfo = ``;
    if (source === 2) {
        curObj = $("#newReceiveAreaInfo");
    }
    curObj.find("input[require]").each(function (){
        let val = $(this).val();
        if (val === "") abled = false;
    })
    if (!abled) {
        layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
        return false;
    }
    if (source === 1) {
        funType = $("#newReceiveInfo").data("type")
        contactInfo = JSON.parse($("#ReceiveName").siblings(".hd").html());
    } else if (source === 2) {
        funType = $("#newReceiveAreaInfo").data("type")
        contactInfo = JSON.parse($("#areaName").siblings(".hd").html());
    }
    var json = {
        'type': source === 1 ? 1: 2,
        'contact': contactInfo.contact ,
        'telephone':contactInfo.telephone
    }
    if (funType === 'update')  {
        url = '../dac/edit';
        json.id = curObj.data("id")
    }
    if (source === 1) {
        json.address = $("#ReceiveAddress").val()
    } else {
        let code = $("#regionCon").siblings(".hd").html();
        code = code.split(',')
        json.address = $("#regionCon").val()
        json.regionCode = code[code.length - 1];
        json.requirements = $("#requirements").val()
    }
    $.ajax({
        url: url,
        data: json,
        beforeSend:function(){ loading.open() ; },
        success: function (data) {
            var status = data.success;
            if (status === 200) {
                bounce.cancel()
                getReceiveAddressList(source, 1);
            } else {
                layer.msg("新增失败！");
            }
        }
    })
}
/*creator:lyt 2022/11/14 0014 下午 4:46 新增到货区域*/
function addArea(){
    $("#newReceiveAreaInfo").data('type', "new");
    bounce.show($("#newReceiveAreaInfo"))
    $("#newReceiveAreaInfo input").val("");
    $("#newReceiveAreaInfo .hd").html("");
    $("#areaName").data('orgData',"");
    $("#newReceiveAreaInfo .bonceHead span").html('新增到货区域');
}
// creator: lyt 2022-11-30 已被停用的数据
function stoppedAddress(obj){
    let type = obj.data("type")
    getReceiveAddressList(type, 0);
}

// creator: lyt 2022-11-30 修改收货信息
let editObj = null;
function receive_edit(obj){
    editObj = obj;
    bounce.show($("#updateTip"))
}
// creator: lyt 2022-11-30 收货信息修改
function updateNext(){
    let info = JSON.parse(editObj.siblings(".hd").html());
    let source = info.type;
    if (source === 1) {
        $("#newReceiveInfo").data("id", info.id)
        $("#newReceiveInfo").data("type", "update")
        $("#newReceiveInfo .bonceHead span").html("修改收货地址");
        bounce.show($("#newReceiveInfo"))
        $("#newReceiveInfo input").val("");
        $("#ReceiveAddress").val(info.address)
        $("#ReceiveName")
            .val(info.contact).data('orgData',info.contact)
            .siblings(".hd").html(JSON.stringify(info)) ;
    } else {
        $("#newReceiveAreaInfo .bonceHead span").html('修改到货区域');
        $("#newReceiveAreaInfo").data("id", info.id)
        $("#newReceiveAreaInfo").data("type", "update")
        $("#regionCon").val(info.address);
        $("#regionCon").siblings(".hd").html(info.region_code);
        $("#requirements").val(info.requirements);
        $("#areaName")
            .val(editObj.parents("tr").find("td").eq(1).html()).data('orgData',"")
            .siblings(".hd").html(JSON.stringify(info)) ;
        bounce.show($("#newReceiveAreaInfo"));
    }
}
// creator: lyt 2022-11-30 收货信息修改记录
function receive_record(obj){
    let info = JSON.parse(obj.siblings(".hd").html());
    $("#receiveUpdateRecords tbody").children(":gt(0)").remove();
    $.ajax({
        url: '../dac/histories',
        data: {"id": info.id},
        success: function (res) {
            let list = res.data || [];
            let str = ``, curStaStr = ``;
            // var cur = data.pageInfo["currentPageNo"];
            // var totalPage = data.pageInfo["totalPage"];
            bounce_Fixed.show($("#receiveUpdateRecords"));
            if (list && list.length > 0) {
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    str += `<tr>
                             <td>${item.versionNo === 0? '原始信息':'第'+item.versionNo+'次修改后'}</td>
                             <td class="ty-td-control">
                                <span class="funBtn ty-color-blue" data-fun="updateLogScan">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                             <td>${item.versionNo === 0?handleNull(item.createName):handleNull(item.updateName)} 
                                 ${item.versionNo === 0?new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss"):new Date(item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                }
                let last = list[list.length-1]
                $("#receiveUpdateRecords table").show();
                curStaStr = `<p>当前数据为第${list.length-1}次修改后的结果。修改时间：${last.updateName} ${new Date(last.updateDate).format("yyyy-MM-dd hh:mm:ss")}</p> `
            }else{
                $("#receiveUpdateRecords table").hide();
                curStaStr = `<p>当前资料尚未经修改。</p> `
            }
            $("#receiveUpdateRecords .curSta").html(curStaStr);
            $("#receiveUpdateRecords tbody").append(str);
        }
    })
}

// creator: lyt 2022-11-30 收货信息修改记录查看
function updateLogScan(obj){
    let info = JSON.parse(obj.siblings(".hd").html());
    if (info.type === '1') {
        $("#recordScan .record_name").html("收货地址");
    } else {
        $("#recordScan .record_name").html("到货区域");
    }
    $("#recordScan .bonceHead>span").html(obj.parents("tr").children().eq(0).html());
    $("#recordScan .record_address").html(info.address);
    $("#recordScan .record_contact").html(info.contact);
    $("#recordScan .record_telephone").html(info.telephone);
    bounce_Fixed2.show($("#recordScan"))
}
// creator: lyt 2022-11-30 删除收货信息
function receive_del(obj){
    let info = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        url: '../dac/delete',
        data: {"id": info.id},
        beforeSend:function(){ loading.open() ; },
        success: function (data) {
            var status = data.success;
            if (status === 200) {
                bounce.cancel()
                obj.parents("tr").remove();
                layer.msg("该条数据已删除！");
            } else {
                layer.msg("<p>操作失败！</p><p>曾被使用过的数据不可删除！</p>");
            }
        }
    })
}
// creator: lyt 2022-11-30 停用收货信息
function receive_stop(obj){
    let info = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        url: '../dac/enabled',
        data: {"id": info.id, "enabled": 1^info.enabled},
        beforeSend:function(){ loading.open() ; },
        success: function (data) {
            var status = data.success;
            if (status === 1) {
                obj.parents("tr").remove();
                if (info.enabled === '1') {
                    bounce.cancel()
                    layer.msg("该条数据已停用！");
                } else {
                    layer.msg("该条数据已启用！");
                    bounce_Fixed2.cancel();
                    getReceiveAddressList(info.type, 1);
                }
            } else {
                layer.msg("<p>操作失败！</p><p>正被使用的数据不可停用！</p>");
            }
        }
    })
}
// creator: lyt 2022-11-30 启用收货信息
function receive_restart(obj){
    bounce_Fixed2.show($("#restartTip"));
    $("#restartTip").data("obj", obj);
}
function restartOk(){
    let obj = $("#restartTip").data("obj");
    receive_stop(obj);
}
// creator: hxz 2020-12-10 新增联系人
function addContactInfo(num) {
    var type = "", source = "";
    if(num === 2){
        type = 'new';
        source = 'addAddress';
    }else if(num === 4){
        type = 'new';
        source = 'addArea';
    }
    //var customerId = $("#updateCustormPanel").data("id");
    //$("#newContectInfo").data('level',2);
    $("#newContectInfo").data('type',type);
    $("#newContectInfo").data('source', source);
    document.getElementById('newContectData').reset();
    $(".otherContact").html("");
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    if($(".contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
    }
    //$("#newContectInfo").data("id", customerId);
    $("#addMoreContact").hide();
    $("#uploadCard").html("")
    initCardUpload($("#uploadCard"));
    bounce_Fixed.show($("#newContectInfo"));
    setTimer('updateContact');
}
// creator: lyt 2022-11-29 收货人那里的新增收货人
function chooseCusContactOk() {
    let selectObj = $("#chooseCusContact").find(".fa-dot-circle-o");
    if(selectObj.length > 0){
        let strInfo = selectObj.siblings("span.hd").html();
        let info = JSON.parse(strInfo);
        $($("#target").val())
            .val(selectObj.next().html()).data('orgData',selectObj.next().html())
            .siblings(".hd").html(strInfo) ;
        bounce_Fixed.cancel();
    }else layer.msg('请先选择人员')
}
// creator: hxz 2020-12-10 新增联系人确定
function addContact(){
    var arr = []
    var data = {
        'name': $("#contactName").val(),
        'post': $("#position").val(),
        'telephone': $("#contactNumber").val(),
        'cardPath': ''
    };
    if($("#contactsCard .bussnessCard").length > 0){
        data.cardPath = $("#contactsCard .bussnessCard .filePic").data('path');
    }
    if($(".otherContact li").length > 0){
        $(".otherContact li").each(function () {
            if ($(this).find('input').val() != '') {
                var json = {
                    'code': $(this).find("input").val(),
                    'type': $(this).find("input").data('type'),
                    'name': $(this).find("input").data('name')
                };
                arr.push(json);
            }
        })
    }
    let param = {
        "deliveryContact": data,
        "contactSocials": arr
    }
    $.ajax({
        url: '../dac/contact/add',
        "method": "POST",
        "timeout": 100000,
        "headers": {
            "Content-Type": "application/json"
        },
        data: JSON.stringify(param),
        success: function (res) {
            data.id = res.data.deliveryContact.id;
            var status = res.success;
            if (status === '200' || status === 200) {
                layer.msg('新增成功')
                var groupUuidArr = []
                $("#newContectInfo [groupUuid]").each(function () {
                    groupUuidArr.push({
                        type: 'groupUuid',
                        groupUuid: $(this).attr("groupUuid")
                    })
                })
                cancelFileDel(groupUuidArr)
                // 给当前的赋值
                data.contact = data.name
                if ($("#newReceiveInfo").is(":visible")) {
                    //bounce_Fixed.show($("#newReceiveInfo"));
                    $("#ReceiveName")
                        .val(data.name).data('orgData', data.name)
                        .siblings(".hd").html(JSON.stringify(data));
                } else  if ($("#newReceiveAreaInfo").is(":visible")){
                    $("#areaName")
                        .val(data.name).data('orgData',data.name)
                        .siblings(".hd").html(JSON.stringify(data)) ;
                }
                bounce_Fixed.cancel();
            } else {
                layer.msg("新增失败！");
            }
        }
    })
}
// creator: 李玉婷，2019-09-04 18:55:15，添加
function addMore (obj){
    obj.next("select").show();
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContact li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li>' +
                '<span class="sale_ttl1">手机：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="1" data-name="手机" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '2':
            html +=
                '<li>' +
                '<span class="sale_ttl1">QQ：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '3':
            html +=
                '<li>' +
                '<span class="sale_ttl1">Email：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '4':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微信：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '5':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微博：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '9':
            $("#useDefinedLabel input").val("");
            bounce_Fixed2.show($("#useDefinedLabel"));
            setTimer('useDefinedLabel');
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-07 08:50:59，删除添加的联系方式
function removeAdd(obj) {
    obj.parent("li").remove();
    if ($(".otherContact li").length > 0) {
        $(".otherContact li").each(function () {
            var val = $(this).find("input").val();
            var type = $(this).find("input").data('type');
            if (type == '9' || type == '9') type = 6
            if (val == '') {
                $("#addMoreContact option").eq(type).prop('disabled', true);
            }else {
                $("#addMoreContact option").eq(type).prop('disabled', false);
            }
        })
    }else{
        $("#addMoreContact option").prop('disabled', false);
    }
}
// creator: 李玉婷，2019-09-04 19:31:34，删除名片
function cancleCard(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    $('#uploadCard').show();
}
// creator: hxz 2020-12-10 获取联系人列表
function getCusContactList() {
    $.ajax({
        "url":"../dac/contact/list",
        success:function (res) {
            let list = res['data'] || [] ;
            setCusListStr(list, 'updateCustomer')
        }
    });
}
function setCusListStr(list) {
    let str="";
    if(list.length === 0){
        $("#chooseCusContact .p0").show()
        $("#chooseCusContact .p1").hide()
        return false
    }
    $("#chooseCusContact .p0").hide()
    $("#chooseCusContact .p1").show()
    for(let i in list){
        let item = list[i];
        item.contact = item.name
        str += `<li>
                    <i class="fa fa-circle-o"></i>
                    <span>${item.name}</span>
                    <span>${item.post && item.post.substr(0,8)}</span>
                    <span>${item.telephone}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                    <span class="linkBtn ty-right" data-id="${item.id}" onclick="seeContactDetail($(this))">查看</span>
                </li>`;
    }
    $("#chooseCusContact .cusList").html(str);
}
// creator: lyt 2022-11-30 联系人查看
function seeContactDetail(obj){
    $.ajax({
        url : "../dac/contact/detail" ,
        data : {
            'id': obj.data('id')
        },
        success:function(data){
            var info = data['data']['contact'];
            var socialList = data['data']['socials'] || [];
            let allStr = ``;
            $("#see_contactName").html(info.name);
            $("#see_position").html(info.post);
            if(socialList.length > 0){
                let sortList = [];
                for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
                for(var r in socialList){
                    let item = socialList[r];
                    let _index = Number(item.type);
                    sortList[_index].push(item);
                }
                let sortAfter = [];
                for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
                for(var t in sortAfter){
                    let item = sortAfter[t];
                    if(t%2===0){
                        allStr += `<tr><td>${item.name}</td><td>${item.code}</td>`
                    }else{
                        allStr += `<td>${item.name}</td><td>${item.code}</td></tr>`
                    }
                }
                if(sortAfter.length % 2 !== 0){
                    allStr += `<td> </td><td> </td></tr>`
                }
            }
            $(".see_otherContact").html(allStr);
            bounce_Fixed2.show($("#contactSeeDetail"));
        }
    });
}
// creator: 李玉婷，2019-09-16 16:26:52，定时器--新增验证
function setTimer (timer) {
    switch (timer) {
        case 'updateContact':
            bounce_Fixed.everyTime('0.5s','updateContact',function () {
                var state = 0, filledNum = 0,otherContactLen = 0;
                var contactsCard = $("#contactsCard").data('org');
                var imgCard = $("#contactsCard .filePic").data('path');
                var len = $(".otherContact").data('length');
                $("#addMoreContact option").prop('disabled', false);
                if ($(".otherContact li").length > 0) {
                    $(".otherContact li").each(function () {
                        var val = $(this).find("input").val();
                        var type = $(this).find("input").data('type');
                        if (type == '9' || type == '9') type = 6
                        if (val == '') {
                            $("#addMoreContact option").eq(type).prop('disabled', true);
                        }else {
                            otherContactLen++;
                        }
                    })
                }
                if (len !=  otherContactLen) state ++;
                $("#newContectData [require]:visible").each(function(){
                    if ($(this).val() != '') filledNum++;
                    if($(this).val() != $(this).data('org')){
                        state ++;
                    }
                });
                if(contactsCard != imgCard) state ++;
                if(filledNum > 0 && state > 0){
                    $("#addContact").prop("disabled",false);
                }else{
                    $("#addContact").prop("disabled",true);
                }
            });
            break;
    }
}
// creator: 李玉婷，2019-09-17 15:09:14，名片上传
function initCardUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        itemTemplate: '',
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var path = data.filename //路径（包含文件类型）
            obj.parent().attr("groupUuid", data.groupUuid )
            var imgStr =
                '<div class="bussnessCard">' +
                '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '   <span fileUid="' + data.fileUid + '" class="ty-color-blue" onclick="cancleCard($(this))">删除</span> ' +
                '</div>';
            $('#uploadCard').hide();
            $('#uploadCard').before(imgStr);
        }
    });
}
// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else {
        console.log("未定义的状态")
    }
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = []
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
}







