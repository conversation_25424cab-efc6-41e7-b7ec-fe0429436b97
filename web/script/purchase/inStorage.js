var selectGS = [],notSelectGS = [] ;
var editObj = null
var editObj_adjustMat = null
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#ordSelect"));
bounce_Fixed2.cancel();
$(function () {
    //获取供应商列表数据
    getSupplier()

    // 获取仓库的模式
    $.ajax('../skl/getStockMode')
        .then(res => {
            let status = res.status
            $("body").data('whmode', status) //0-非智能 1-智能
        })

    $(".searchInputText").on('click input', function () {
        let searchInput = $(this).val()
        let name = $(this).attr('name')
        let list = $("#chooseMateriel_baoZhiQi").data("materiel")
        let searchArr = []
        searchArr = list.filter(item => item[name].indexOf(searchInput) !== -1)
        var readonlyStr = $(this).attr("readonly");
        if (!readonlyStr) {
            let str = ''
            for (let item of searchArr) {
                str += `<div class="search_item" data-id="${item.id}">${item[name]}</div>`
            }
            $(".search_choose_list_avatar").html(str)
            $(".search_choose_list").hide()
            $(this).siblings(".search_choose_list").show()
        }
    })
    $(".search_choose_list").on('click', '.search_item', function () {
        let searchThis = $(this)
        let currentMt = $("#chooseMateriel_baoZhiQi").data("mt")
        let currentId = currentMt?currentMt.id:0
        let chooseId = searchThis.data("id")
        if (!currentId) {
            searchThis.parents('.search_choose_list').siblings('input').val(searchThis.text())
            $(".search_choose_list").hide();
            let list = $("#chooseMateriel_baoZhiQi").data("materiel") // 材料列表
            let data = list.find(item => item.id === searchThis.data('id'))
            setSelectMateriel(data)
            return false
        } else {
            if (currentId == chooseId) {
                layer.msg('选择的材料与当前材料相同！')
            } else {
                bounce_Fixed2.show($("#changeMtTip"))
                $("#changeMtTip .sureBtn").unbind().on("click", function () {
                    bounce_Fixed2.cancel()
                    searchThis.parents('.search_choose_list').siblings('input').val(searchThis.text())
                    $(".search_choose_list").hide();
                    let list = $("#chooseMateriel_baoZhiQi").data("materiel") // 材料列表
                    let data = list.find(item => item.id === searchThis.data('id'))
                    setSelectMateriel(data)
                    return false
                })
            }
        }
    })
    // 账号下拉列表点击其他地方收回
    $(document).click(function (e) {
        console.log(e.target)
        if(!$(".searchInputText").is(e.target)&&$(".searchInputText").has(e.target).length===0){
            $(".search_choose_list").hide();
            let mtInfo = $("#chooseMateriel_baoZhiQi").data('mt')
            if ($("#chooseMateriel_baoZhiQi").is(":visible")) {
                if (mtInfo) {
                    $("#chooseMateriel_baoZhiQi [name='code']").val(mtInfo.code)
                    $("#chooseMateriel_baoZhiQi [name='name']").val(mtInfo.name)
                }
            }
        }
    });
    setEveryTime($('body'), 'selectSupplier');
    $(".main").on("click", "[type='btn'],.btnDo ", function () {
        var name = $(this).data("name");
        var thisObj = $(this);
        switch (name) {
            case 'update':
                $("#chooseMateriel_baoZhiQi .bonceHead span").html("修改要入库的材料")
                chooseMateriel(thisObj);
                break;
            case 'add':
            case 'add2': // 上方新增一行
                $("#chooseMateriel_baoZhiQi .bonceHead span").html("选择要入库的材料")
                chooseMateriel(thisObj);
                break;
            case 'delete':
                $("#delTip").data("obj", thisObj);
                bounce.show($("#delTip"));
                break;
            case 'back':
                $(".guide_avatar").show().siblings().hide()
                break;
            case 'cancelApply':
                $("#tip .tipMs").html("确定放弃所录入的内容吗？")
                bounce.show($("#tip"))
                break;
            case 'inStorageApply': /// 提交入库申请
                inStorageApply()
                break;
        }
    })
    $(".bounce").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'editOk': // 编辑材料确定
                var result = {};
                var str = '';
                $("#chooseMateriel_baoZhiQi input").each(function () {
                    var key = $(this).attr("name");
                    result[key] = $(this).val();
                })
                var tt = $("#chooseMateriel_baoZhiQi select[name='unit_price']").data("tt");
                result["tt"] = tt;
                if(tt === 1){
                    $("#chooseMateriel_baoZhiQi select").each(function () {
                        var key = $(this).attr("name");
                        if(key == 'unit_price'){
                            result['']
                        }
                        result[key] = $(this).val();
                    })
                    let orderID = $("#chooseMateriel_baoZhiQi select[name='unit_price']").val();
                    let unit_price = $("#chooseMateriel_baoZhiQi select[name='unit_price'] option:selected").html();
                    let memo = $("#chooseMateriel_baoZhiQi select[name='memo'] option:selected").html();
                    result['orderID'] = orderID;
                    result['unit_price'] = unit_price;
                    result['memo'] = memo;
                    result['unitPriceList'] = $("#chooseMateriel_baoZhiQi select[name='unit_price']").html();
                }
                let memoInfo = $("#chooseMateriel_baoZhiQi input[name='memo']").data("memoinfo") || $("#chooseMateriel_baoZhiQi").data("rateinfo")

                result["canInvoice"] = memoInfo.canInvoice;
                result["incoiceType"] = memoInfo.incoiceType;
                result["hasTax"] = memoInfo.hasTax;
                var hasRepeat = false
                $(".main .addActive tbody tr").each(function(){
                    let trInfo = $(this).data("item");
                    if(trInfo.code == result.code && trInfo.name == result.name){
                        hasRepeat = true
                    }
                })
                str +=  '<tr data-item=\''+JSON.stringify(result)+'\'>' +
                            '<td>'+result.code+'</td>'+
                            '<td>'+result.name+'</td>'+
                            '<td>'+result.model+'</td>'+
                            '<td>'+result.specifications+'</td>'+
                            '<td>'+result.unit+'</td>'+
                            '<td>'+parseFloat(Number(result.quantity).toFixed(4))+'</td>'+
                            '<td>--</td>'+
                            '<td>'+
                            '<span class="link-blue" type="btn" data-name="update">修改</span>'+
                            '<span class="link-blue" type="btn" data-name="add2">在上方增加一行</span>'+
                            '<span class="link-red" type="btn" data-name="delete">删除本行</span>'+
                            '</td>'+
                        '</tr>';
                var type =  $("#chooseMateriel_baoZhiQi").data("type")
                var editTr = editObj.parents("tr");
                if(type.indexOf("add") > -1){ // 新增
                    if(hasRepeat){
                        // layer.msg("该材料已经选择过，请重新选择！")
                        // return false;
                    }
                    if(type == "add2"){ // 在上方新增
                        editTr.before(str)
                    }else{
                        $(".addActive table tbody").append(str);
                    }

                }else if(type == "update"){ // 修改
                    editTr.before(str);
                    editTr.remove();
                }
                bounce.cancel()
                break;
            case 'allocatedMat':
                let matDistributInfot = JSON.parse($("#matDistributList").html());
                let dbType = matDistributInfot.dbType ;
                let list = matDistributInfot.matDistributList;
                $("#matDistribut tbody").html("");
                let strScan = "";
                if(list && list.length > 0){
                    for(let i = 0 ; i < list.length ; i++){
                        let item = list[i];
                        strScan += `<tr>
                    <td>${ item.mb_name}</td>
                    <td>${ item.code}</td>
                    <td>${ item.model}</td>
                    <td>${ item.specifications}</td>
                    <td>${ item.unit}</td> 
                    <td>${ parseFloat(Number(item.quantity).toFixed(4))}</td> 
                    <td class="ty-td-control">${ item.sn}<span class="ty-color-gray">查看</span></td> 
                    <td>${ parseFloat(Number(item.quantity - item.stored_quantity - item.locked_quantity).toFixed(4)) }</td>  
                    <td>${ parseFloat(Number(item. dif_amount).toFixed(4))}</td>  
                    <td><input value="${ dbType == 2 ? parseFloat(Number(item.adjustAmount).toFixed(4)) : '' }" disabled="disabled" /><span class="hd">${ JSON.stringify(item) }</span></td>
                </tr>`;
                    }
                }
                $("#canAdjust").show();
                $("#matDistribut tbody").html(strScan);
                bounce_Fixed.show($("#matDistribution"));
                break;
            case 'adjustingMat':
                let payItems = JSON.parse($("#adjustingMatOralList").html());
                let adjustList = JSON.parse($("#adjustList").html());
                $("#adjustMat .select0 tbody").html("");
                $("#adjustMat .select1 tbody").html("");
                $("#adjustMat .ty-secondTab li:eq(0)").click();
                let str0 = "", str1 = "" ;
                if(payItems && payItems.length > 0) {
                    for(let j = 0; j < payItems.length ; j++){
                        let strSet = "";
                        let item = payItems[j];
                        let isMatch = chargeMatch(item['id'], adjustList);
                        let way = item.way;
                        let faStr = `<td class="faTd" data-type="0">
                                <span class="fa fa-circle-o"></span>
                                <span class="hd">${JSON.stringify(item)}</span>
                            </td>`;
                        let selectStr = ""
                        if(way > 0){ // 调整过
                            faStr = `<td class="faTd" data-type="1">
                                <span class="fa fa-dot-circle-o"></span>
                                <span class="hd" data-orderid="${item.orders_id}">${JSON.stringify(item)}</span>
                            </td>`;
                            selectStr = `<td><select>
                                    <option value="">-- 请选择 --</option>
                                    <option value="2" ${ way=='2'? "selected":"" } >增加到未完结的订单</option>
                                    <option value="1" ${ way=='1'? "selected":"" }>补发新的订单</option>
                                    </select></td>`;
                        }
                        strSet =`<tr> ${faStr }
                        <td>${ item.name}</td>
                        <td>${ item.code}</td>
                        <td>${ item.model}</td>
                        <td>${ item.specifications}</td>
                        <td>${ item.unit}</td> 
                        <td>${ parseFloat(Number(item.quantity).toFixed(4))}</td>
                        <td>${ parseFloat(Number(item.out).toFixed(4))}</td> 
                        ${ selectStr }
                    </tr>`;
                        if(way > 0){
                            str1 += strSet ;
                        }else{
                            str0 += strSet ;
                        }
                    }
                }
                $("#adjustMat .select0 tbody").html(str0);
                $("#adjustMat .select1 tbody").html(str1);
                $(".ty-secondTab li:eq(0) span").html( $(".select0 tbody tr").length );
                $(".ty-secondTab li:eq(1) span").html( $(".select1 tbody tr").length );
                bounce_Fixed.show($("#adjustMat"));
                break;
            case 'normal':
                var origin = $("#tip").data("origin")
                if (origin === 'cancelApply') {
                    $(".guide_avatar").show().siblings().hide()
                    $("#materiel tbody").html("")
                }
                bounce.cancel()
                break;
            case 'delSure':
                var curObj = $("#delTip").data("obj");
                curObj.parents("tr").remove();
                bounce.cancel();
                break;
            case 'inStorageApplyTipOk':
                inStorageApplyTipOk()
                break;
        }
    })
    $("#adjustMat").on("change","select", function(){
        let val = $(this).val();
        if(val == 2){
            editObj_adjustMat = $(this)
            bounce_Fixed2.show($("#ordSelect"));
            let info = JSON.parse($(this).parents("tr").find(".hd").html());
            $.ajax({
                "url":"../whs/getOrdersByMtId",
                "data":{ "mtId": info.id, "supplier": $(".main").data("chooseSupplier")},
                success:function (res) {
                    let list = res['data'], str='<option value="">-- 请选择 --</option>';
                    if(list && list.length > 0){
                        for(let i = 0 ; i < list.length; i++){
                            let item = list[i];
                            str += `<option value="${item.id}">订单号：${item.sn}</option>`;
                        }
                    }
                    $("#ordSelect select").html(str)
                }
            })
        }
    });
    $("#materielList").on("click", ".ty-form-checkbox", function () {
        $(this).toggleClass("ty-form-checked")
    });
    $("#adjustMat").on("click", ".faTd", function () {
        let item = JSON.parse( $(this).find(".hd").html() );
        let val = $(this).data("type");
        $(this).parent().remove();
        let selectStr = `<select>
                    <option value="">-- 请选择 --</option>
                    <option value="2">增加到未完结的订单</option>
                    <option value="1">补发新的订单</option>
                    </select>`;
        let innerHtml = `<tr>
                        <td class="faTd" data-type="1">
                            <span class="fa fa-dot-circle-o"></span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                        <td>${ item.name}</td>
                        <td>${ item.code}</td>
                        <td>${ item.model}</td>
                        <td>${ item.specifications}</td>
                        <td>${ item.unit}</td> 
                        <td>${ parseFloat(Number(item.quantity).toFixed(4))}</td>
                        <td>${ parseFloat(Number(item.out).toFixed(4))}</td>
                        <td>${selectStr}</td>
                    </tr>`;
        if(val == 1){
            innerHtml = `<tr>
                        <td class="faTd" data-type="0">
                            <span class="fa fa-circle-o"></span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                        <td>${ item.name}</td>
                        <td>${ item.code}</td>
                        <td>${ item.model}</td>
                        <td>${ item.specifications}</td>
                        <td>${ item.unit}</td> 
                        <td>${ parseFloat(Number(item.quantity).toFixed(4))}</td>
                        <td>${ parseFloat(Number(item.out).toFixed(4))}</td> 
                    </tr>`;
            $("#adjustMat .select0 tbody").append(innerHtml);
        }else{
            $("#adjustMat .select1 tbody").append(innerHtml);
        }
        $(".ty-secondTab li").each(function(index){
            $(this).children("span").html($(".select"+ index +" tbody tr").length)
        })

    });
    // 渲染供应商数据
    $("#supplier").on('input click', '', function () {
        var input = $(this).val()
        var list = querySupplier(input)
        $(this).find('input').focus()
        $(".input_choose_list").slideDown('fast')
        var str = ''
        console.log(list)
        if (list.length === 0) {
            str = '<div class="input_choose_item disabled">未搜索到结果</div>'
        } else {
            for (var i in list) {
                let item = list[i]
                let info = {"id":item.id, "full_name":item.full_name, "name":item.name, "code_name":item.code_name }
                str += '<div class="input_choose_item" data-id="'+list[i].id+'">'+list[i].full_name+'<span class="hd">'+ JSON.stringify(info) +'</span></div>'
            }
        }
        $(".input_choose_list").html(str)
    })
    // 账号下拉列表点击事件，选择账号
    $(".input_choose_list").on('click', '.input_choose_item', function () {
        if (!$(this).hasClass("disabled")) {
            $(".main").data("chooseSupplier", $(this).data("id"))
            $(".main .supplierName").html($(this).html()).next(".hd").html($(this).find('.hd').html())
            getMateriel()
        }
        return false
    })
    // creator: 李玉婷，2020-06-04 17:53:57，选中材料代号或名称
    $(".materielInputForm .selecGS").on('click', 'div', function () {
        setSelectMateriel($(this).data('val'));
        $(".selecGS").hide();
        $(".bounce").stopTime("gsMatch");
    });
    $("body").on("click",function (e) {
        var targetBtn = $(e.target).parents("#supplier").length === 0
        if (targetBtn) {
            $(".input_choose_list").slideUp('fast');
        }

        $(".selecGS").hide();
        $(".bounce").stopTime("gsMatch");
        $(".selecPs").hide();
        $(".bounce").stopTime("selecPs");

    })

    $(".ty-secondTab").children().click(function () {
        toggleSelect($(this).index()) ;
    })
})
// create: hxz 2021-1-14 材料分配 确定
function matDistributOk() {
    let matDistributInfot = JSON.parse($("#matDistributList").html());
    let dbType = matDistributInfot.dbType ; // 分配状态 1-按系统默认分配 2-手动分配
    if(dbType == 1){
        let dis = $("#matDistribution input").attr("disabled") ;
        if(dis == undefined ){
            dbType = 2 ;
        }
    }
    let canOk = true;
    let mtIDList = [], // 统计材料id
        mtDifAmountSum=[], // 统计对应每种 系统分配数量 总数
        mtNumSum=[]; // 统计对应每种 调整后 总数
    let matDistributList = [];
    if(dbType == 2 ){
        $("#matDistribut tbody tr").each(function () {
            let adjustAfterNum = Number( $(this).find("input").val() ); // 调整后总数
            let info = JSON.parse($(this).find(".hd").html());
            let dif_amount = Number(info.dif_amount) ;  // 系统分配数量
            let canNum = info.quantity - info.stored_quantity ; // 可分配数量
            let mtId = info.mb_id;
            let indexMt = mtIDList.indexOf(mtId);
            info['adjustAmount'] = adjustAfterNum ;  // 调整后数量
            matDistributList.push(info) ;
            if(indexMt>-1){
                mtNumSum[indexMt] += adjustAfterNum ;
                mtDifAmountSum[indexMt] += dif_amount ;
            }else{
                mtIDList.push(mtId)
                mtNumSum.push(adjustAfterNum);
                mtDifAmountSum.push(dif_amount);
            }

        });
        for(let g = 0 ; g < mtIDList.length; g++){
            let adjustAfterNumAll = mtNumSum[g];
            let difAmountAll = mtDifAmountSum[g];
            if(adjustAfterNumAll > difAmountAll){
                canOk = false ;
            }
        }

        if(!canOk){
            layer.msg('所分配数量有误！')
            return false
        }
        // 每种材料调整后总数 必须等于系统分配总数
       for(let q = 0 ; q < mtIDList.length ; q++){
            if(mtNumSum[q] !== mtDifAmountSum[q]){
                canOk = false;
            }
       }
        if(!canOk){
            layer.msg('所分配数量有误！')
            return false
        }
    }
    bounce_Fixed.cancel();
    $("#matDistributList").html(JSON.stringify({"dbType": dbType, "matDistributList": matDistributList }))
}
// create: hxz 2021-1-9 材料分配 取消
function matDistributCancel() {
    bounce_Fixed.cancel();
}
function ordSelectOk(type) {
    if(type == 'cancel'){
        editObj_adjustMat.val("")
    }else{
        let ordSelectVal = $("#ordSelect select").val();
        let hdObj = editObj_adjustMat.parents("tr").find(".hd");
        hdObj.data('orderid', ordSelectVal);
    }
    bounce_Fixed2.cancel();
}
// create:hxz 2021-1-9 调整分配数量
function canAdjust() {
    $("#canAdjust").hide();
    $("#matDistribution input").removeAttr("disabled");
}
// creator: 侯杏哲，2020-01-01 15:48:17 校验定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            case "selectSupplier":
                var sName = $("#supplier input").val();
                if(sName == ""){
                    $("#supplier .input_choose_list .input_choose_item").show();
                }else{
                    $("#supplier .input_choose_list .input_choose_item").each(function () {
                        var html = JSON.parse($(this).find(".hd").html()) ;
                        var name = html.name;
                        var index = name.indexOf(sName);
                        if(index>-1){
                            $(this).show();
                        }else{
                            $(this).hide();
                        }
                    });
                }
                break;
            case 'chooseMateriel': // 编辑材料弹窗校验
                var result = {};
                $("#chooseMateriel_baoZhiQi input").each(function () {
                    var key = $(this).attr("name");
                    result[key] = $(this).val();
                })
                var tt = $("#chooseMateriel_baoZhiQi select[name='unit_price']").data("tt");
                if(tt === 1){
                    $("#chooseMateriel_baoZhiQi select").each(function () {
                        var key = $(this).attr("name");
                        result[key] = $(this).val();
                    })
                }
                var canbeClick = true, reaquireKey=['name', 'code','quantity','unit_price','memo'];
                for(let i in reaquireKey){
                    let key = reaquireKey[i]
                    let val = result[key]
                    if(val == ""){
                        canbeClick = false
                    }
                }
                $("#chooseInMaterielBtn").prop("disabled",!canbeClick)


                break;
            case 'chooseMateriel_baoZhiQi': // 编辑材料弹窗校验
                let code = $("#chooseMateriel_baoZhiQi [name='code']").val()
                let canClick = true

                if (!code) canClick = false

                $("#chooseInMaterielZhinengBtn").prop("disabled",!canbeClick)

                if ($("#chooseMateriel_baoZhiQi .baoZhiQiPart").is(":visible")) {
                    let count = 0
                    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem").each(function () {
                        let deadline = $(this).find('[name="deadline"]').val()
                        let quantity = $(this).find('[name="quantity"]').val() || 0
                        let packs = $(this).find('.hd').html()
                        if (!deadline || !quantity || !packs) {
                            canClick = false
                        }
                        count += Number(quantity)
                    })
                    $("#chooseMateriel_baoZhiQi [name='count']").val(count)
                } else {
                    let quantity = $("#chooseMateriel_baoZhiQi [name='quantity']").val()
                    if (!quantity) canClick = false
                }
                $("#chooseInMaterielBaoZhiQiBtn").prop("disabled", !canClick)
                break;

            case 'inStorageApply':
                var state = 0
                $("#materiel tbody tr").each(function () {
                    var quantityPlan = $(this).find(".quantityPlan").val();     //--申请入库数量
                    var arriveDate = $(this).find(".arriveDate").val();  //--到货日期
                    if ($.trim(quantityPlan) === '' || $.trim(arriveDate) === '' ) {
                        state++
                    }
                })

                // if( state > 0 || $("#materiel tbody tr").length < 1){
                //     $("#inStorageApplyBtn").prop("disabled",true)
                // }else {
                //     $("#inStorageApplyBtn").prop("disabled",false)
                // }
                // if ($("#materiel tbody tr").length > 0) {
                //     $("button[data-name='cancelApply']").show()
                //     $("button[data-name='inStorageApply']").show()
                // } else {
                //     $("button[data-name='cancelApply']").hide()
                //     $("button[data-name='inStorageApply']").hide()
                // }
                break;
            case 'editBaoZhuang':
                let allCount = 0
                $("#editBaoZhuangInfo .smallCount").each(function () {
                    let inputVal = $(this).parents('tr').find("input").val() || 0
                    $(this).html(Number(inputVal).toFixed(2))
                    allCount += Number(inputVal)
                })
                $("#editBaoZhuangInfo .allCount").html(Number(allCount).toFixed(2))
                let inputAmount = Number($("#editBaoZhuangInfo .mtNum").text())
                if (inputAmount !== allCount) {
                    $("#editBaoZhuangInfoBtn").prop("disabled", true)
                } else {
                    $("#editBaoZhuangInfoBtn").prop("disabled", false)
                }
                break
        }

    });
}

// creator: 张旭博, 2020-06-08 20:24:25, 放弃本次申请
function giveUpApply() {
    $(".guide_avatar").show().siblings().hide()
    $("#materiel tbody").html("")
    bounce.cancel()
    setEveryTime($('body'), 'selectSupplier');
}

// creator: 张旭博，2019-10-22 08:47:25，获取供应商
function getSupplier() {
    $.ajax({
        url: '/inStock/supplier',
        data: {
            oid: sphdSocket.user.oid,
            name: ''
        },
        success: function (res) {
            var supplierList = res.data || []
            $("#supplier").data('supplier', supplierList);
        }
    })
}

// creator: 张旭博，2020-14-05 18:14:42,遍历供应商
function querySupplier(input) {
    var supplierList = $("#supplier").data('supplier')
    var queryList = []
    for (var i in supplierList) {
        var name = supplierList[i].name
        if (name.indexOf(input) !== -1) {
            queryList.push(supplierList[i])
        }
    }
    return queryList
}

// creator: 张旭博，2019-10-22 09:08:15，获取物料
function getMateriel() {
    $.ajax({
        url: '/inStock/mtBase',
        data: {
            oid: sphdSocket.user.oid,
            name: '',
            supplierId: $(".main").data("chooseSupplier")
        },
        success: function (res) {
            var list = res.data
            if (list && list.length > 0){
                $(".input_choose_list").slideUp('fast');
                $(".main").show().siblings().hide()
                newChooseMateriel(1)
                list.forEach(function(mt){
                    var item = (mt.sp && mt.sp[0]) || {}
                    var price = item.unit_price_notax || item.unit_price;
                    var str = ""
                    if(item["price_stable"] === "1"){ // 价格相对稳定
                        if(String(item["material_invoiceable"]) === "1"){ // 能开票
                            if(item["material_invoice_category"] === "1"){
                                str += `单价为${item.tax_rate}%增值税专用发票的含税价。  `
                            }else if(item["material_invoice_category"] === "2"){ // 给开其他发票
                                str = `单价为开普票价`
                            }
                        }else if(String(item["material_invoiceable"]) === "0"){ // 不给开发票
                            str += `购买不给开发票`
                        }else if(String(item["material_invoiceable"]) === "2"){ // 不确定是否开票
                            //没有这种情况
                        }
                    }else if(item["price_stable"] === "2"){ // 价格变动较频繁
                        if(String(item["material_invoiceable"]) === "1"){ // 能开票
                            if(item["material_invoice_category"] === "1"){
                                str += `单价为开${item.tax_rate}%增值税专用发票的含税价。参考单价${price}元`
                            }else if(item["material_invoice_category"] === "2"){ // 给开其他发票
                                str = `单价为开普票的价格。参考单价${price}元`
                            }
                        }else if(String(item["material_invoiceable"]) === "0"){ // 不给开发票
                            str += `购买不给开发票。参考单价${price}元`
                        }else if(String(item["material_invoiceable"]) === "2"){ // 不确定是否开票
                            //要选择的清空
                            str = "-- 请选择 --"
                        }
                    }
                    item.memo = str;
                })
                $("#chooseMateriel_baoZhiQi").data("materiel", list);
                $('body').stopTime("selectSupplier");
            } else {
                layer.msg('该供应商未供应任何材料');
            }
        }
    })
}
// creator: hxz，2020-01-01 10:57:16
function toggleFa(_thisObj) {
    if(_thisObj.hasClass("fa-check-square-o")){
        _thisObj.attr("class", "fa fa-square-o")
    }else {
        _thisObj.attr("class", "fa fa-check-square-o")
    }
}
// creator: hxz，2020-01-01 10:57:16，提交入库申请
function inStorageApply() {
    let isok = true
    $(".applyForm input").each(function () {
       let data = $(this).val();
       if(!data){
           isok=false
       }
    });
    if(!isok){
        layer.msg('请将到货日期补充完整！')
        return false
    }
    $(".applyForm .inStoItem").each(function(){
        let lenTr = $(this).find("tbody tr").length ;
        if(!(lenTr > 0)){
            isok = false;
        }
    });
    // 每个到货日期下都应该有材料
    // if(!isok){
    //     layer.msg('请将到货日期的菜')
    //     return false
    // }
    var itemList=[], supplierId =  $(".main").data("chooseSupplier"), oid = sphdSocket.org.id;
    var dateMt = [] ;
    $(".inStoItem").each(function () {
        let d = $(this).find('input').val();
        $(this).find("tbody tr").each(function(){
            var info = $(this).data("item"), has = false;
            itemList.forEach(function (item) {
                if(
                    item.mtId == info.id &&
                    item.invoiceable == info.canInvoice &&
                    item.invoiceCategory == info.incoiceType &&
                    item.taxInclusive == info.hasTax &&
                    item.memo == info.memo &&
                    item.unitPrice == info.unit_price
                ){
                    item.quantity += Number(info.quantity || info.amount);
                    has = true;

                }
            })
            if(!has){
                itemList.push({
                    "mtId": info.id,
                    "quantity": Number(info.quantity || info.amount),
                    "supplierId": supplierId,
                    "oid": oid,
                    "invoiceable":info.canInvoice,
                    "invoiceCategory":info.incoiceType,
                    "taxInclusive":info.hasTax,
                    "unitPrice":info.unit_price,
                    "memo":info.memo });
            }
            let ha2 = false;
            dateMt.forEach(function(i) {
                if(i.id == info.id){
                    ha2 = true
                    let d1 = new Date(i.d)
                    let d2 = new Date(d)
                    if(d2 > d1){
                        i.d = d
                    }
                }
            })
            if (!ha2){
                dateMt.push({ 'id':info.id, 'd':d })
            }

        })
    })
    if(itemList.length == 0 ){
        layer.msg('还有必填项尚未填写！')
        return false
    }
    window.dateMt = dateMt
    bounce.show($("#inStorageApplyTip"))
    $.ajax({
        // headers必须添加，否则会报415错误
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(itemList),
        url: '/whs/checks',
        success: function(data){
            //成功处理
            var noPayNum = data.no;
            var itemMap = data.itemMap;
            var payItems = data.payItems;
            var payNum = 0;
            var outNum = data.out;
            $("#amountOut").html(outNum);
            $("#amountOut2").html(outNum);
            $("#amountOut3").html(outNum);
            $("#paySum").html(payNum);
            $("#noPaySum").html(outNum - payNum);
            let noPaySum = outNum - payNum ;
            if(Number(outNum) > 0){   $(".line3").show(); $(".paySumTr").show(); }else{ $(".line3").hide(); $(".paySumTr").hide();  }
            if(Number(noPaySum) > 0){  $(".noPaySumTr").show(); }else{  $(".noPaySumTr").hide(); } 
            
            // 渲染去查看和去调整的数据
            // 去查看
            $("#matDistributList").html(JSON.stringify({"dbType":1, "matDistributList":itemMap}));

            let info = JSON.parse($(".main .supplierName").next('.hd').html())
            $("#matDistribution .supplerInfo .supFullname").html(info.full_name);
            $("#matDistribution .supplerInfo .supname").html(info.name);
            $("#matDistribution .supplerInfo .supcode").html(info.code_name);

             // 去调整
            // 给单价赋值，随便选的
            payItems.forEach(function (item1) {
                item1.way = 0; //默认没有调整
            });
            payItems.nullToStr(0)
            $("#adjustingMatOralList").html(JSON.stringify(payItems))
            $("#adjustList").html(JSON.stringify([]));

        }
    });

}
// creator: hxz，2020-01-01 10:57:16，提交入库申请 确定
function inStorageApplyTipOk() {
    let isOk = true;
    $("#inStorageApplyTip [data-val]").each(function(){
        let val = $(this).data("val");
        if(val == 1){
            if($(this).hasClass("fa-square-o")){
                layer.msg("请确认：对于待入库材料在采购订单中的分配情况是否有异议");
                isOk = false;
                return false
            }
        }else if(val == 2){
            if($("#noPaySum").html()>0){
                if($(this).hasClass("fa-square-o") && $("#noPaySum").html()>0){
                    layer.msg("请确认：无需付款的"+ $("#noPaySum").html() +"种材料将由系统补发订单");
                    isOk = false;
                    return false
                }
            }
        }
    })
    if(!isOk){
        return false
    }
    var dbType = 1 , // 分配状态 1-按系统默认分配 2-手动分配
        difItems = [], // 分配方式数组：id:材料id orderId: 所属订单id difAmount：分配数量 数据格式：[{"id":868,"orderId":45,"difAmoutnt":100}]
        waysItems = [], // 处理方式数组：id:材料id orderId:订单id amount数量；
        nosItms = [], // 补发订单数组：id:材料id amount：数量 数据格式：[{"id":868,"amoutnt":100}] 该数组下的元素为无需付款的材料 A-B
        oid = sphdSocket.org.id,
        items = []; //入库数组 [{"id":868, date":"2020-12-25"，”"amoutnt":1000}] 全部的入库数组 id:材料id date:到货日期 amount:入库数量

    let matDistributInfot = JSON.parse($("#matDistributList").html());
    dbType = matDistributInfot.dbType;
    let matDistributList = matDistributInfot.matDistributList;
    // 材料分配
    matDistributList.forEach(function (info) {
        info.difAmount = dbType ===1 ? info.dif_amount : info.adjustAmount
        difItems.push({"id":info.mb_id,"itemId":info.orders_id, "orderId":info.orders_id, "difAmount":info.difAmount}) ;
    });
    // 调整材料
    let adjustList = JSON.parse($("#adjustingMatOralList").html());
    adjustList.forEach(function (info) {
        let way = info.way ; // 1-补发新订单，2-增加到未完结订单
        let item = {"id":info.id,
            "amount":info.out,
            "unitPrice": info.unitPrice ,
            "invoiceable":info.invoiceAble,
            "invoiceCategory":info.invoiceCategory,
            "taxInclusive":info.isTax,
            "memo": info.memo }
        if(way == 1){
            item['pay'] = 1
            item['way'] = way
        }else if(way == 2){
            item['pay'] = 1
            item['way'] = way
            item['orderId'] = info.orders_id
            item['itemId'] = info.orders_id
        }else{
            item['pay'] = 0
        }
        window.dateMt.forEach(function(i){
            if(i.id == info.id){
                item.date = i.d
            }
        })

        waysItems.push(item);
    });
    $(".main .inStoItem").each(function(){
        let date = $(this).find("input").val();
        $(this).find("tbody tr").each(function(){
            let info = $(this).data("item");
            items.push({ id: info.id, date:date, rDate: info.date, amount: info.amount || info.quantity, packs: info.packs });
        })
    })
    let supplierInfo = JSON.parse($(".main .supplierName").next('.hd').html())
    $.ajax({
        "url":"../whs/instock",
        "data":{
            "dbType": dbType ,
            "difItems": JSON.stringify(difItems) ,
            "waysItems": JSON.stringify(waysItems) ,
            "oid": oid ,
            "supplierId": supplierInfo.id ,
            "items": JSON.stringify(items) ,
        },
        success:function (res) {
            var code = res['code'], msg = res['meg'];
            if(code == 200){
                layer.msg("操作成功！");
                $(".main").hide();
                $(".guide_avatar").show();
                bounce.cancel();
                setEveryTime($('body'), 'selectSupplier');
            }
        }
    })
}
// creator: 李玉婷，2020-05-18 11:25:12，选择要入库的材料
function chooseMateriel(obj) {
    let list = $("#chooseMateriel_baoZhiQi").data("materiel"), str = "", nameStr = "";
    editObj = obj ;
    var type = obj.data("name");
    $("#chooseMateriel_baoZhiQi").data("type", type)
    obj.parents(".inStoItem").addClass('addActive').siblings().removeClass('addActive');


    // 默认显示非保质期页面，且只能选择材料，其余不能编辑

    $("#chooseMateriel_baoZhiQi .defaultPart").show()
    $("#chooseMateriel_baoZhiQi").data('mt', '')
    $("#chooseMateriel_baoZhiQi .baoZhiQiPart").hide()
    $("#chooseMateriel_baoZhiQi input[name='unit_price']").show().siblings().hide();
    $("#chooseMateriel_baoZhiQi input[name='memo']").show().siblings().hide();
    $("#chooseMateriel_baoZhiQi .otherInput input").prop("disabled", true);
    $("#chooseMateriel_baoZhiQi .materielInputForm input").val("");
    $("#chooseMateriel_baoZhiQi .selecGS").hide();

    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem.clone").remove()
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem.fixed").find('input').val('')
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem.fixed").find('[name="editstate"]').val('尚未编辑')
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem.fixed").find('.hd').html('')



    // 获取材料列表
    if(list){
        for(var i = 0 ; i < list.length ; i++){
            str += "<div data-val='"+ JSON.stringify(list[i]) +"'>"+ list[i]['code'] +"</div>"
            nameStr += "<div data-val='"+ JSON.stringify(list[i]) +"'>"+ list[i]['name'] +"</div>"
        }
    }
    $("#chooseMateriel_baoZhiQi .materielInputForm .selecGS:eq(0)").html(str);
    $("#chooseMateriel_baoZhiQi .materielInputForm .selecGS:eq(1)").html(nameStr);
    bounce.show($("#chooseMateriel_baoZhiQi"));

    setEveryTime(bounce, 'chooseMateriel_baoZhiQi');
    if(type === "update"){
        let info = obj.parents("tr").data("item");
        setSelectMateriel(info.mtInfo)
        $("#chooseMateriel_baoZhiQi input[name='code']").prop("disabled", true)
        $("#chooseMateriel_baoZhiQi input[name='name']").prop("disabled", true)
        $("#chooseMateriel_baoZhiQi .groupItem.fixed input[name='deadline']").val(info.date)
        $("#chooseMateriel_baoZhiQi .groupItem.fixed input[name='quantity']").val(info.amount)
        $("#chooseMateriel_baoZhiQi .groupItem.fixed input[name='editstate']").val('已编辑')
        $("#chooseMateriel_baoZhiQi .groupItem.fixed .hd").html(JSON.stringify(info.packs))
    } else {
        $("#chooseMateriel_baoZhiQi input[name='code']").prop("disabled", false)
        $("#chooseMateriel_baoZhiQi input[name='name']").prop("disabled", false)
    }
}
function showInputOrSelect(num){
    let showStr = "input", hideStr = "select" ;
    if(num === 1){
        showStr = "select";
        hideStr = "input" ;
    }
    $("#chooseMateriel_baoZhiQi "+ showStr +"[name='unit_price']").show();
    $("#chooseMateriel_baoZhiQi "+ showStr +"[name='memo']").show();
    $("#chooseMateriel_baoZhiQi "+ hideStr + "[name='unit_price']").hide();
    $("#chooseMateriel_baoZhiQi "+ hideStr +"[name='memo']").hide();
}
// creator: 李玉婷，2020-06-04 18:06:27，选择要入库的材料后确定
function inputMateriel(e){
    var curObj = $(e.target);
    var readonlyStr = curObj.attr("readonly");
    if (readonlyStr === "readonly") {

    } else {
        e.stopPropagation();
        $(".bounce").stopTime("psMatch");
        $(".bounce").everyTime('0.5s',"gsMatch",function(){
            gsMatch(e);
        });
    }
    
}
// creator: 李玉婷，2020-06-05 17:32:40，与录入匹配
function gsMatch(obj) {
    var curObj = $(obj.target);
    var tagetName = curObj.attr("name");
    curObj.next(".selecGS").show();
    var innerEntry = curObj.val();
    var selecGSObj = curObj.next(".selecGS")
    var hasMatch = false;
    if (innerEntry != "") {
        selecGSObj.children().hide();
        selecGSObj.children().each(function () {
            var code = $(this).html();
            if(code.indexOf(innerEntry) != -1){
                $(this).show();
            }
            if(code === innerEntry){
                hasMatch = true;
                setSelectMateriel($(this).data('val'));
            }
        });
    }else{
        selecGSObj.children().show();
    }

    if(!hasMatch){
        $("#chooseMateriel_baoZhiQi input:not([name="+ tagetName +"])").val("");
        $("#chooseMateriel_baoZhiQi select[name='unit_price']").prop("disabled", true);
        $("#chooseMateriel_baoZhiQi select[name='memo']").prop("disabled", true);
    }else{
        $("#chooseMateriel_baoZhiQi select[name='unit_price']").prop("disabled", false);
        $("#chooseMateriel_baoZhiQi select[name='memo']").prop("disabled", false);
    }
}
// creator: hxz，2021-01-01 13:04:29 删除本组到货日期
function delChooseMateriel(delBtn) {
    delBtn.parents(".inStoItem").remove();
}
// creator: 李玉婷，2020-08-25 13:04:29，增加一组到货日期
function newChooseMateriel(type) {
    type = type || 2; // 1-新增第一组，2-删除本组
    var addOrDelBtn ='<span class="link-blue btnDo" onclick="newChooseMateriel()">增加一组到货日期</span>' ;
    if(type == 2){
        addOrDelBtn ='<span class="link-red btnDo" onclick="delChooseMateriel($(this))">删除本组到货日期</span>' ;
    }else{
        $(".main .applyForm").html("");
    }
    // obj.parents(".inStoItem").addClass('addActive').siblings().removeClass('addActive');
    var invoiceDateID = "I" + Math.random().toString(36).substr(2).substr(5,10) ;
    var html =
        '<div class="inStoItem">' +
        '    <div class="clear matItem">' +
        '        <div class="ty-left">' +
        '            <span>' +
        '                <span>到货日期</span>' +
        '                <input class="kj-input" id="'+ invoiceDateID +'" value="" placeholder="请选择"/>' +
        '            </span>' +
        '            <span class="link-blue btnDo chooseInSto" data-name="add">选择要入库的材料</span>' +
        '        </div>' +
        '        <div class="ty-right">' + addOrDelBtn +
        '        </div>' +
        '    </div>' +
        '    <table class="kj-table" class="materiel">' +
        '        <thead>' +
        '        <tr>' +
        '            <td width="15%">材料代号</td>' +
        '            <td width="18%">材料名称</td>' +
        '            <td width="10%">型号</td>' +
        '            <td width="10%">规格</td>' +
        '            <td width="6%">计量单位</td>' +
        '            <td width="8%">申请入库的数量</td>' +
        '            <td width="10%">保质期至</td>' +
        '            <td width="23%">操作</td>' +
        '        </tr>' +
        '        </thead>' +
        '        <tbody></tbody>' +
        '    </table>' +
        '</div>';
    $(".main .applyForm").append(html);
    laydate.render({elem: '#'+invoiceDateID, istoday: true, festival: true});
}
// creator: 李玉婷，2020-06-05 20:02:26，补全材料其他信息
function setSelectMateriel(i_item){

    // 清一下保质期内容
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem.clone").remove()
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem.fixed").find('input').val('')
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem.fixed").find('.dateInput').html(`<input class="ty-inputText" placeholder="请选择日期" name="deadline" style="width: 100%"/>`)
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem.fixed").find('[name="editstate"]').val('尚未编辑')
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem.fixed").find('.hd').html('')

    let formEle = $("#chooseMateriel_baoZhiQi")
    $("#chooseMateriel_baoZhiQi").data('mt', i_item)
    let mode = $("body").data('whmode')

    let data = i_item

    let baoZhuangInfo = {
        expRequired: data.exp_required, // 是否有保质期方面的要求
        openDuration: data.open_duration, // 开封/开瓶后可使用天数
        relatedItem: data.related_item, // 相关的数据:1-截止日期,2-生产日期
        sameExpiration: data.same_expiration, // 保质期是否相同
        expirationDays: data.expiration_days // 保质期天数
    }

    // 智能库且需要保质期的时候按新版弹窗展示
    if (mode === 1 && baoZhuangInfo.expRequired === 1) {
        $("#chooseMateriel_baoZhiQi .baoZhiQiPart").show()
        $("#chooseMateriel_baoZhiQi .defaultPart").hide()
        formEle.find(".name_deadline").html(baoZhuangInfo.relatedItem === 1?'可使用的截止日期':'生产日期')
        // 截止日期最小今天，生产日期最大今天
        if (baoZhuangInfo.relatedItem === 1) {
            formEle.find(".mtRowInputGroup input[name='deadline']").each(function () {
                laydate.render({
                    elem: this,
                    min: 0
                })
            })
        } else {
            formEle.find(".mtRowInputGroup input[name='deadline']").each(function () {
                laydate.render({
                    elem: this,
                    max: 0
                })
            })
        }
    } else {
        $("#chooseMateriel_baoZhiQi .baoZhiQiPart").hide()
        $("#chooseMateriel_baoZhiQi .defaultPart").show()
        $("#chooseMateriel_baoZhiQi [name='quantity']").prop("disabled", false).val('')
    }
    // 单价和备注展示
    for(let key in i_item){
        if(key != "unit_price" && key != "memo"){
            formEle.find("input[name=" + key +"]").val(i_item[key]);
        }
    }
    formEle.find(".unit").html(i_item.unit);
    formEle.find("input[name='unit_price']").prop("disabled", false)
    formEle.find("input[name='memo']").prop("disabled", false)
    let sp = i_item.sp[0];
    let list = i_item.details || [], str = "<option value=''>-- 请选择 --</option>", nameStr = "<option value=''>-- 请选择 --</option>";
    formEle.data('rateinfo', {
        canInvoice: sp.invoice_able,
        incoiceType: sp.invoice_category,
        hasTax: sp.is_tax
    })


    if(list.length > 0){
        let afterList = []
        list.forEach(function(i) {
            let h = false
            afterList.forEach(function(j) {
                if (i.unit_price == j.unit_price ){ h = true ; } ;
            })
            if(!h){
                afterList.push(i)
            }
        })
        let selectStr = '' ;
        if(afterList.length === 1){ selectStr = ' selected ' ; }
        for(let item of afterList){
            str += `<option ${selectStr} value='${item.id}'>${item.unit_price}</option>`
            nameStr += `<option ${selectStr} value='${item.id}'>${item.memo}</option>`
        }
        formEle.find("select[name='unit_price']").html(str).removeAttr("readonly").data("tt",1).show().siblings("input").hide();
        formEle.find("select[name='memo']").html(nameStr).removeAttr("readonly").show().siblings("input").hide();
    }else{
        formEle.find("select[name='unit_price']").hide().siblings("input").show().data("tt",0);
        formEle.find("select[name='memo']").hide().siblings("input").show();

        formEle.find("input[name='unit_price']").val(sp.unit_price_notax || sp.unit_price).attr("readonly",true);
        formEle.find("input[name='memo']").val(sp.memo).attr("readonly", true);

        if(String(sp.price_stable )=== "1"){
            formEle.find("input[name='memo']").removeAttr("onclick");
            // $(".materielInputForm input[name='memo']").attr("onclick","setInvoice()");
        }else{//可点击条件：变动频繁且价格选择了不确定，且该材料没有采购订单
            if(String(sp["material_invoiceable"]) === "2" && handleNull(list.orders) == ''){
                formEle.find("input[name='memo']").data("tax",sp.tax_rate).attr("onclick","setInvoice()");
            }  else {
                formEle.find("input[name='memo']").removeAttr("onclick");
            }
        }
    }



}
function setOtherVal(thisObj) {
    let val = thisObj.val();
    $("#chooseMateriel_baoZhiQi select").val(val);
}
function setInvoice() {
    bounce_Fixed.show($("#memoSelect"));
    $(".incoiceType").hide();
    $(".hasTax").hide();
    $("#memoSelect .fa").attr("class", "fa fa-circle-o");
    $("#memoSelect input").val("");
}

// creator: 张旭博，2024-12-30 04:24:50， 备注选择（补充creator）
function memoSelectOk() {
    let canInvoice = $("#edit_canInvoice").val(); // 1-是；0-否
    let incoiceType = $("#edit_incoiceType").val(); // 1-增专；2-其他
    let hasTax = $("#edit_hasTax").val(); // 1-是；2-否
    let iso = true
    if(canInvoice == ""){
        iso = false
    }else if(canInvoice == '1' && incoiceType == ''){
        iso = false
    }else if(canInvoice == '1' && incoiceType == '1' && hasTax == ""){
        iso = false
    }
    if(!iso){
        layer.msg("请选择完整！");
        return false
    }
    bounce_Fixed.cancel();
    var memoInfo = { "canInvoice":canInvoice,  "incoiceType":incoiceType,  "hasTax":hasTax }
    var str = ``;
    if(canInvoice == 1){
        if(incoiceType == 1){
            var tax_rate = $("#chooseMateriel_baoZhiQi input[name='memo']").data("tax")
            if(hasTax == 1){
                str = `单价为开${tax_rate? (tax_rate + '%'):''}增值税专用发票的含税价`;
            }else{
                str = `单价为开${tax_rate? (tax_rate + '%'):''}增值税专用发票的不含税价`;
            }
        }else{
            str = `单价为开普票的价格`;
        }
        $("#chooseMateriel_baoZhiQi input[name='memo']").data("memobefore",str);
        var unit_price = $("#chooseMateriel_baoZhiQi input[name='unit_price']").val()
        str += `参考单价为${unit_price}元`
    }else{
        str = `购买不给开发票`;
    }
    $("#chooseMateriel_baoZhiQi input[name='unit_price']").prop("disabled", false);
    $("#chooseMateriel_baoZhiQi input[name='memo']").data("memoinfo",memoInfo).val(str).attr("title", str) ;
}
function setMemo(){
    var str = $("#chooseMateriel_baoZhiQi input[name='memo']").data("memobefore");
    var unit_price = $("#chooseMateriel_baoZhiQi input[name='unit_price']").val();
    str += `参考单价为${unit_price}元`;
    $("#chooseMateriel_baoZhiQi input[name='memo']").val(str).attr("title", str) ;
}
//  creator ：侯杏哲 2019/12/4 购买本材料是否能开发票
function canInvoice(type , thisObj) {
    setRadioSelect("canInvoice", [1,0], type, thisObj);
    $(".incoiceType .fa").attr("class","fa fa-circle-o");
    $(".hasTax .fa").attr("class","fa fa-circle-o");
    $(".incoiceType input").val("");
    $(".hasTax input").val("");
    if(type === 1){
        $(".incoiceType").show();
    }else {
        $(".incoiceType").hide();
        $(".hasTax").hide();
    }
}
function incoiceType(type , thisObj) {
    setRadioSelect("incoiceType", [1,2], type, thisObj);
    $(".hasTax .fa").attr("class","fa fa-circle-o");
    $(".hasTax input").val("");
    if(type === 1){
        $(".hasTax").show();
    }else {
        $(".hasTax").hide();
    }
}
function hasTax(type , thisObj) {
    setRadioSelect("hasTax", [1,0], type, thisObj);
}
// creator：侯杏哲 2019/12/24 操作单选按钮
function setRadioSelect(str, arr, selectVal, thisObj){
    var idStr = "#edit_" + str ;
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $(idStr).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $(idStr + arr[i]).attr("class" , "fa fa-circle-o");
    }
    $(idStr + selectVal).attr("class" , "fa fa-dot-circle-o");
}
// creator: 李玉婷，2020-07-8 8:10:45，切换选中未选择
function toggleSelect(state) {
    $(".ty-secondTab").children().removeClass('ty-active');
    $(".ty-secondTab").children(":eq("+ state +")").addClass('ty-active');
    $("#adjustMat .select"+state).show().siblings().hide();
}
// creator: hxz 2021-1-14  调整材料 确定
function adjustMatOk() {
    let isok = true;
    let adjustList = [] ;
    let payItems = JSON.parse($("#adjustingMatOralList").html());
    $("#adjustMat .select0 .fa").each(function(){
        let hdObj = $(this).siblings(".hd");
        let info = JSON.parse(hdObj.html());
        delete info['way']
        delete info['orders_id']
        let index = payItems.findIndex((i)=>{ return i.id == info.id   })
        payItems[index] = info
    })
    $("#adjustMat .select1 select").each(function(){
        let val = Number($(this).val());
        if(!(val > 0)){
            isok = false;
        }else{
            let hdObj = $(this).parents("tr").find(".hd");
            let info = JSON.parse(hdObj.html());
            info['way'] = val ;
            if(val == 2){
                let orders_id = hdObj.data('orderid');
                info['orders_id'] = orders_id ;
            }
            let index = payItems.findIndex((i)=>{ return i.id == info.id   })
            payItems[index] = info
        }
    })
    if(!isok){
        bounce_Fixed2.show($("#adjustTip"));
        return false
    }else{
        // $("#adjustList").html(JSON.stringify(adjustList));
        $("#adjustingMatOralList").html(JSON.stringify(payItems))
        let unNum = $("#adjustMat .ty-secondTab li:eq(1) span").html();
        $("#paySum").html(unNum);
        let noPaySum = $("#amountOut").html() - unNum ;
        $("#noPaySum").html(noPaySum);
        if(noPaySum > 0){
            $(".noPaySumTr").show();
        }else{
            $(".noPaySumTr").hide();
        }
    }
    bounce_Fixed.cancel();


}
// create:hxz 2021-1-14 判断id 是否存在
function chargeMatch(id , adjustList){
    let _bool = false
    if(adjustList && adjustList.length > 0) {
        for(let i = 0 ; i < adjustList.length; i++){
            if(id = adjustList[i]['id']){
                _bool = adjustList[i]['way']
            }
        }
    }
    return _bool
}
// creator: 李玉婷，2021-06-29 11:44:53，控制输入3位小数
function limitSize3(obj) {
    clearNoNum(obj);
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/,'$1$2.$3');
}



// creator: 张旭博，2024-12-27 02:39:43， 选择要入库的材料（智能库） - 新增一组数据
function addOneGroup() {

    let state = 0
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem").each(function () {
        let date = $(this).find("[name='deadline']").val()
        let quantity = $(this).find("[name='quantity']").val()
        let editstate = $(this).find("[name='editstate']").val()
        if (date === '' || quantity === '' || editstate === '尚未编辑') {
            state++
        }
    })
    if (state > 0) {
        let str = '<p>操作失败！</p><p>因为还有没填写内容的数据！</p>'
        bounce_Fixed2.show($("#bounce_Fixed2Tip"))
        $("#bounce_Fixed2Tip .msg").html(str)
        return false
    }
    let groupItem = $("#chooseMateriel_baoZhiQi .hidePart .groupItem").clone(true)
    groupItem.addClass('clone')
    $("#chooseMateriel_baoZhiQi .mtRowInputGroup").append(groupItem)
    let name_deadline = groupItem.find(".name_deadline").html()
    if (name_deadline === '生产日期') {
        $("#chooseMateriel_baoZhiQi .mtRowInputGroup").find("input[name='deadline']").each(function () {
            if (!$(this).attr('lay-key')) {
                laydate.render({
                    elem: this,
                    max: 0
                })
            }

        })
    } else {
        $("#chooseMateriel_baoZhiQi .mtRowInputGroup").find("input[name='deadline']").each(function () {
            if (!$(this).attr('lay-key')) {
                laydate.render({
                    elem: this,
                    min: 0
                })
            }
        })
    }

}

// creator: 张旭博，2024-12-27 02:59:25， 删除
function delOneGroup(selector) {
    selector.parents(".groupItem").remove()
}

// creator: 张旭博，2024-12-27 03:29:56， 编辑包装信息
function editBaoZhuangInfo(selector) {
    let groupItem = selector.parents(".groupItem")
    let deadline = groupItem.find("[name='deadline']").val()
    let quantity = groupItem.find("[name='quantity']").val()
    let unit = groupItem.find(".unit").html()
    if (!deadline || !quantity) {
        let str = '<p>操作失败！</p><p>因为材料信息不全！</p>'
        bounce_Fixed2.show($("#bounce_Fixed2Tip"))
        $("#bounce_Fixed2Tip .msg").html(str)
        return false
    }
    bounce_Fixed.show($("#editBaoZhuangInfo"))
    $("#editBaoZhuangInfo .mtNum").html(quantity)
    $("#editBaoZhuangInfo .unit").html(unit)
    $("#editBaoZhuangInfo").data('edit', selector)
    $("#editBaoZhuangInfo tbody").html('')
    $("#editBaoZhuangInfo tfoot input").val('')
    setEveryTime($('body'), 'editBaoZhuang');
}

// creator: 张旭博，2024-12-27 03:50:11， 编辑包装信息 - 确定
function editBaoZhuangInfo_submit() {
    let packArr = []
    let noPackArr = [{
        id: 0,
        amount: $("#editBaoZhuangInfo tfoot input").val()
    }]
    let selector = $("#editBaoZhuangInfo").data('edit')

    bounce_Fixed2.show($("#editBaoZhuangTip"))
    $("#editBaoZhuangTip .sureBtn").unbind().on("click", function () {
        selector.parents(".form-item").find(".hd").html(JSON.stringify([...packArr, ...noPackArr]))
        selector.parents(".form-item").find("[name='editstate']").val('已编辑')
        bounce_Fixed2.cancel()
        bounce_Fixed.cancel()
    })

}

function judgeHasInputPack(selector) {
    let pack = selector.parents(".groupItem").find(".hd").html()
    if (pack) {
        bounce_Fixed2.show($("#changeQuantityTip"))
        $("#changeQuantityTip .sureBtn").unbind().on("click", function () {
            bounce_Fixed2.cancel()
            selector.parents(".groupItem").find(".hd").html('')
            selector.parents(".groupItem").find("[name='editstate']").val('尚未编辑')
        })
        $("#changeQuantityTip .cancelBtn").unbind().on("click", function () {
            bounce_Fixed2.cancel()

            let hd = JSON.parse(selector.parents(".groupItem").find(".hd").html())
            selector.parents(".groupItem").find("[name='quantity']").val(hd[0].amount)
        })
    }
}

// creator: 张旭博，2024-12-30 09:41:47， 确定
function chooseMateriel_baoZhiQi_submit() {
    let formEle = $("#chooseMateriel_baoZhiQi")
    let mtInfo = $("#chooseMateriel_baoZhiQi").data('mt')
    var result = {};
    result.mtInfo = mtInfo
    var str = '';
    $("#chooseMateriel_baoZhiQi .mtBase input").each(function () {
        var key = $(this).attr("name");
        result[key] = $(this).val();
    })

    var tt = formEle.find("select[name='unit_price']").data("tt");
    result["tt"] = tt;
    if(tt === 1){
        let orderID = $("#chooseMateriel_baoZhiQi .mtBase [name='unit_price']").val();
        let unit_price = $("#chooseMateriel_baoZhiQi .mtBase [name='unit_price'] option:selected").html();
        let memo = $("#chooseMateriel_baoZhiQi .mtBase [name='memo'] option:selected").html();
        result['orderID'] = orderID;
        result['unit_price'] = unit_price;
        result['memo'] = memo;
        result['unitPriceList'] = formEle.find("select[name='unit_price']").html();
    }
    let memoInfo = formEle.find("input[name='memo']").data("memoinfo")|| formEle.data("rateinfo")

    result["canInvoice"] = memoInfo.canInvoice;
    result["incoiceType"] = memoInfo.incoiceType;
    result["hasTax"] = memoInfo.hasTax;
    result.name = mtInfo.name
    result.code = mtInfo.code
    var hasRepeat = false
    $(".main .addActive tbody tr").each(function(){
        let trInfo = $(this).data("item");
        if(trInfo.code == result.code && trInfo.name == result.name){
            hasRepeat = true
        }
    })
    let hasExp = $("#chooseMateriel_baoZhiQi .baoZhiQiPart").is(":visible")
    if (hasExp) {
        let groupData = []
        $("#chooseMateriel_baoZhiQi .mtRowInputGroup .groupItem").each(function () {
            let packs = $(this).find(".hd").html()
            groupData.push({
                date: $(this).find("[name='deadline']").val(),
                amount: $(this).find("[name='quantity']").val(),
                packs: packs?JSON.parse(packs): ''
            })
        })
        for (let item of groupData) {
            let deadline = item.date
            if (mtInfo.exp_required === 1 && mtInfo.related_item === 2) {
                if (mtInfo.same_expiration === 1) {
                    // 不同供应商供应的本材料保质期是否相同？ 相同
                    deadline = moment(item.date).add(mtInfo.expiration_days || 0, 'days').format("YYYY-MM-DD")
                } else {
                    deadline = moment(item.date).add(mtInfo.msm_expiration_days || 0, 'days').format("YYYY-MM-DD")
                }
            }
            str += `<tr data-item='${JSON.stringify({...item, ...result})}'>
                    <td>${result.code}</td>
                    <td>${result.name}</td>
                    <td>${result.model}</td>
                    <td>${result.specifications}</td>
                    <td>${result.unit}</td>
                    <td>${item.amount}</td>
                    <td>${deadline}</td>
                    <td>
                        <span class="link-blue" type="btn" data-name="update">修改</span>
                        <span class="link-blue" type="btn" data-name="add2">在上方增加一行</span>
                        <span class="link-red" type="btn" data-name="delete">删除本行</span>
                    </td>
                </tr>`
        }
    } else {
        str = `<tr data-item='${JSON.stringify(result)}'>
                <td>${result.code}</td>
                <td>${result.name}</td>
                <td>${result.model}</td>
                <td>${result.specifications}</td>
                <td>${result.unit}</td>
                <td>${parseFloat(Number(result.quantity).toFixed(4))}</td>
                <td>--</td>
                <td>
                    <span class="link-blue" type="btn" data-name="update">修改</span>
                    <span class="link-blue" type="btn" data-name="add2">在上方增加一行</span>
                    <span class="link-red" type="btn" data-name="delete">删除本行</span>
                </td>
            </tr>`
    }


    let type =  $("#chooseMateriel_baoZhiQi").data("type")
    let editTr = editObj.parents("tr");
    if(type.indexOf("add") > -1){ // 新增
        if(hasRepeat){
            // layer.msg("该材料已经选择过，请重新选择！")
            // return false;
        }
        if(type == "add2"){ // 在上方新增
            editTr.before(str)
        }else{
            $(".addActive table tbody").append(str);
        }

    }else if(type == "update"){ // 修改
        editTr.before(str);
        editTr.remove();
    }
    bounce.cancel()
}
laydate.render({elem: '#arriveDate',type: 'date'});

