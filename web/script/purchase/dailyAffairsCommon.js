

$(function () {
    $(".handleBtn").on("click", '.ty-form-checkbox', function () {
        if ($(this).hasClass("ty-form-checked")){
            $(this).removeClass("ty-form-checked")
            $("#form_billEntry").hide()
            document.getElementById('form_billEntry').reset()
            $(".cp_imgShow").html("")
        } else {
            $(this).addClass("ty-form-checked")
            $("#form_billEntry").show()
            setAmount()
        }
    })
    $(".bounce").on("click", '[type="btn"]', function () {
        var name = $(this).data('name')
        switch (name) {
            // 票据录入
            case 'goodEntry':
                moveGoodEntry("600px");
                $(".ty-nextTip").hide();
                $("#form_billEntry").hide()
                $("#form_billEntry .repeatBill").hide()
                $("#form_billEntry .cp_imgShow").html("")
                $("#form_billEntry .repeatCon").html("")
                $("#form_billEntry input").val("")
                $(".VATInvoice").hide().siblings().hide();
                $('#form_goodEntry')[0].reset()
                $("#goodEntry .textMax").html("0/30");
                var firstLoadBill = $("#reimburse tbody tr").eq(0)
                if (firstLoadBill.length !== 0) {
                    var billCatName = firstLoadBill.children().eq(0).html() // 已经录入的票据类型
                    if (billCatName === '收据') {
                        $("#form_goodEntry [name='billCat'] option").eq(5).prop('selected', true)
                    } else {
                        $("#form_goodEntry [name='billCat']").val('')
                    }
                }
                $(".secondFeeCat").hide()
                bounce_Fixed2.show($("#goodEntry"))
                $("#goodEntry").data('type', 'new')
                setEveryTime(bounce_Fixed2, 'goodEntry')
                $("#onlyOneRow").val("");
                $("#goodEntry").find("i.fa-dot-circle-o").attr("class", "fa fa-circle-o");
                $("#goodEntry").data('type', 'new')


                break;
            // 报销申请-确定
            case 'reimburse':
                sureReimburse($(this))
                break;
            // 报销申请 - 修改
            case 'changeBill':
                changeBill($(this))
                break;
            // 报销申请 - 删除
            case 'delBill':
                var billInfo = $(this).parent().siblings(".billInfo").html();
                var data = JSON.parse(billInfo);
                var bills = data.bills
                bills.forEach(function(bill){
                    let fileArr = bill.fileArr
                    if(fileArr.length > 0){
                        let fileid = fileArr[0]
                        let op = {'type':'fileId', 'fileId':fileid }
                        cancelFileDel(op, true);
                    }
                })
                $(this).parents('tr').remove()
                break;
        }
    })
    $(".bounce_Fixed").on("click", '[type="btn"]', function () {
        var name = $(this).data('name')
        switch (name) {
            // 票据信息 - 获取列表 - 修改
            case 'changeGood':
                // 初始化
                document.getElementById('form_goodEntry').reset()
                bounce_Fixed2.show($("#goodEntry"))
                var data = $("#billEntry").data('state')
                $("#goodEntry [name='billCat']").val(data.billCat)
                $("#goodEntry .issueDate").val(data.issueDate)
                $("#goodEntry .billNo").val(data.billNo)
                // 开启验证
                setEveryTime(bounce_Fixed2, 'goodEntry')
                // 获取数据
                var goodInfo = $(this).parents('td').prev('.goodInfo').html()
                goodInfo = JSON.parse(goodInfo)
                // 赋值各个参数
                for(var key in goodInfo){
                    if(key == 'taxRate'){
                        goodInfo.taxRate = goodInfo.taxRate *100 ;
                    }
                    $("#goodEntry [name='"+key+"']").val(goodInfo[key])
                }
                setFeeCat(goodInfo)

                // 赋值代表是修改用的弹窗
                $("#goodEntry").data('type', 'change')
                $("#goodEntry").data('obj', $(this))

                break;
            // 票据信息 - 获取列表 - 删除
            case 'delGood':
                if ($(this).parents('tbody').find('tr').length === 1) {
                    var type = $("#billEntry").data('type')
                    var obj = $("#billEntry").data('obj')
                    bounce_Fixed.cancel()
                    if (type === 'change') {
                        obj.parents("tr").remove()
                    }
                }else {
                    $(this).parents("tr").remove()
                }
                break;
            // 票据信息 - 确认
            case 'billEntry':
                sureBillEntry($(this))
                break;
            // 票据信息- 录入下一种货物
            case 'goodEntryNext':
                document.getElementById('form_goodEntry').reset()
                $(".secondFeeCat").hide()
                bounce_Fixed2.show($("#goodEntry"))
                setEveryTime(bounce_Fixed2, 'goodEntry')
                // 初始化录入完毕下面内容
                $(".handleBtn .ty-form-checkbox").removeClass("ty-form-checked")
                $("#form_billEntry").hide()
                document.getElementById('form_billEntry').reset()
                $("#goodEntry").data('type', 'newNext')
                $(".ty-nextTip").hide();
                $("#billCat").attr("disabled","disabled");
                var data = $("#billEntry").data('state')
                $("#goodEntry [name='billCat']").val(data.billCat)
                $("#goodEntry .issueDate").val(data.issueDate)
                $("#goodEntry .billNo").val(data.billNo)
                $("#goodEntry .billNo").val(data.billNo)
                $("#goodEntry .billMemo").val(data.memo)
                break
        }
    })
    $(".bounce_Fixed2").on("click", '[type="btn"]', function (e) {
        var name = $(this).data('name')
        switch (name) {
            /* ------------- 货物录入弹窗 --------------- */
            // 货物录入确认
            case 'sureGoodEntry':
                sureGoodEntry($(this))
                break;
            // 获取录入 -新增行 （定额发票）
            case 'iconOneRow':
                var val_ = $(this).data('val');
                $("#onlyOneRow").val(val_);
                $(this).find("i.fa").attr("class", "fa fa-dot-circle-o");
                $(this).siblings().find("i.fa").attr("class", "fa fa-circle-o");
                if(val_ == '1'){
                    var billCatName = $("#billCat").find("option:selected").html();
                    $(".in4").html(billCatName);
                    if( billCatName === '增值税普通发票'){
                        $(".in2").show();
                        $(".in3").show();
                        $(".in1").hide();
                    }else{
                        $(".in2").hide();
                        $(".in3").hide();
                        $(".in1").show();

                    }
                    $(".oneRowTab").children("tbody").html("") ;
                    inputNextTr(1);
                    $("#summary").html("您已录入"+ billCatName +"共 0 张，发票金额总计 0 元，实际支出（即您将报销）总计 0 元。");
                }
                break;
            // 录入下一张只有一行内容的增值税普通发票
            case 'inputNextBtn':
                var isGray = $(this).hasClass("ty-btn-gray");
                if(!isGray){
                    inputNextTr(1);
                }
                break;
            // 单行增普上传图片
            case 'uploadImg':
                var deleObj = $(this) ;
                uploadCode = 'f'+ Math.random().toString(36).substr(2);
                var itemTemp =
                    '<div id="${fileID}" title="${fileName}" size="${size}" code="${uploadCode}" class="upItem uploadify-queue-item" onmouseout=\'hideIMG($(this))\' onmouseover=\'showIMGInfo($(this))\'>' +
                    '<i class="hd up_delBtn fa fa-close" onclick="deleteFile($(this))"></i>' +
                    '<span class="hd up_fileType">${fileType}</span>' +
                    '<span class="up_name">${fileName}</span>' +
                    '<span class="uploadify-progress"><div class="uploadify-progress-bar"></div></span>' +
                    '</div>';
                $(this).siblings(".img").Huploadify({
                    auto: true,
                    fileTypeExts: '*.gif,*.png;*.jpg;*.jpeg;',
                    multi: true,
                    buttonText: "",
                    buttonNumber: 0,
                    formData: {
                        module: '日常事务',
                        userId: sphdSocket.user.userID
                    },
                    fileSizeLimit: (100*1024),  // 100M = ( 100 * 1024 ) KB
                    showUploadedPercent: true,
                    showUploadedSize: true,
                    removeTimeout: 99999999,
                    itemTemplate:itemTemp ,
                    uploader: $.webRoot + "/uploads/uploadfyByFile.do",
                    onUploadStart: function () {
                        deleObj.hide();
                        deleObj.siblings(".img").show();
                    },
                    onUploadComplete: function (file, data) {
                    },
                    onUploadError: function (file) {
                        layer.msg('文件上传出错！');
                        console.log('出错文件：', file)
                    },
                    onUploadSuccess: function (file, data) {
                        data = JSON.parse(data);
                        // data = result.data;
                        var fileUid =data.fileUid;
                        var filePath = $.fileUrl + data.filename;
                        var fileName = file.name;
                        var size = file.size;
                        $(".upItem").each(function(){
                            var txt = $(this).attr("title");
                            var s = $(this).attr("size");
                            var co = $(this).attr("code");
                            if(fileName == txt && size == s && uploadCode == co){ // 名称/大小/批次 一样就默认是一个,即：每次不能穿两个名字、类型、大小完全相同的文件
                                var imgStr = "<img data-fileid='"+ fileUid +"' src='"+ filePath +"' data-filename='"+ fileName +"' />" ;
                                $(this).html(imgStr)
                            }
                        });
                    }
                });
                $(this).siblings(".img").find(".uploadify-button").click();
                break;
            // 单行增普删除附件
            case 'delImg':
                window.tipDelObj = { "obj":$(this), "type":"img"  } ;
                $(".deltip").html("确定要删除该附件 ?");
                bounce_Fixed3.show($("#tipDelTr"));
                break;
            // 清空本行
            case 'reset':
                var tr = $(this).parents("tr");
                tr.children(":eq(9)").children(".memoAcronym").html("");
                tr.children(":eq(9)").children(".memoAcronym").html("");
                tr.children(":eq(9)").children(".hd").html("");
                tr.find("input").val("");
                tr.find('.uploadImg').show()
                tr.find('.img').html("")
                break;
            // 删除本行
            case 'clearTr':
                window.tipDelObj = { "obj":$(this), "type":"tr"  } ;
                $(".deltip").html("确定要删除该行 ?");
                bounce_Fixed3.show($("#tipDelTr"));
                break;
            case 'newRow':
                var isGray = $(this).hasClass("ty-color-gray");
                if(!isGray){
                    inputNextTr(2);
                }
                break;
            // 获取录入 -删除行 （定额发票）
            case 'delRow':
                $(this).parents("tr").remove()
        }
    })
    $(".firstFeeCat select").on('input', function () {
        var feeCatName = $(this).find('option:selected').html()
        var feeCat = $(this).val()
        if(feeCatName === '交通费' || feeCatName === '车务支出') {
            $(".secondFeeCat").show()
            getSecondFeeCat(feeCat, function (err, data) {
                var str = '<option value="">---请选择二级费用类别---</option>'
                for (var i in data) {
                    if (Number(data[i].enabled) === 1) {
                        str += '<option value="' + data[i].id + '">' + data[i].name + '</option>'
                    }
                }
                $(".secondFeeCat select").html(str)
            })
        }else {
            $(".secondFeeCat").hide()
        }
    })
    $("#billCat").on("change", function () {
        $(".kindInvoice [name]").val('')
        $(".kindInvoice .cp_imgShow").html('')
        $(".addRow").remove()
        var firstLoadBill = $("#reimburse tbody tr").eq(0)
        var thisBill = $(this).find("option:selected").html(), isSelect = true;
        $("#onlyOneRow").val("")
        $(".ty-nextTip").find(".fa").attr("class", "fa fa-circle-o");
        if (firstLoadBill.length !== 0) {
            var billCatName = firstLoadBill.children().eq(0).html() // 已经录入的票据类型
            if (billCatName === '收据') {
                if (thisBill !== '收据') {
                    $("#tip .tip").html('您刚才已录入收据，发票需另外提出申请！');
                    bounce_Fixed3.show($("#tip"));
                    $(this).val('');
                    isSelect = false;
                }
            } else {
                if (thisBill === '收据') {
                    $("#tip .tip").html('您刚才已录入发票，收据需另外提出申请！');
                    bounce_Fixed3.show($("#tip"))
                    $(this).val('')
                    isSelect = false;
                }
            }
        }
        if(isSelect){
            if(thisBill === "收据" || thisBill === "其他普通发票"){
                $(".in2").show();$(".in1").hide();$(".in3").show();
            }else if(thisBill === "增值税普通发票"){
                $(".in1").show();$(".in2").hide();$(".in3").hide();
            }

            if (billCatName === '收据') {
                $(".VATInvoice .formItem_itemName .titleName").html('票据内容')
                $(".VATInvoice .formItem_itemQuantity .titleName").html('数量')
                $(".VATInvoice .formItem_price .titleName").html('金额')
            } else if (billCatName === '其他普通发票') {
                $(".VATInvoice .formItem_itemName .titleName").html('票据内容')
                $(".VATInvoice .formItem_itemQuantity .titleName").html('数量')
                $(".VATInvoice .formItem_price .titleName").html('金额')
            } else {
                $(".VATInvoice .formItem_itemName .titleName").html('货物或应税劳务、服务名称')
                $(".VATInvoice .formItem_itemQuantity .titleName").html('数量')
                $(".VATInvoice .formItem_price .titleName").html('金额')
            }
        }

        if (thisBill === '定额普通发票'|| thisBill ==='定额发票') {
            $(".quotaInvoice tbody").html("");
            $(".colNumTotal").html("0");
            $(".colPriceTotal").html("0");
            $(".colAmountTotal").html("0");
            inputNextTr(2);
        }


    });
});

// creator: hxz，2020/02/06 显示图片信息
function tipDelTrOk() {
    if(tipDelObj.type ==  "tr"){
        var imgObj = tipDelObj.obj.siblings(".img");
        let fileid = imgObj.find("img").data("fileid")
        let op = {'type':'fileId', 'fileId':fileid }
        cancelFileDel(op, true);
        window.tipDelObj.obj.parents("tr").remove();
    }else{
        var imgObj = tipDelObj.obj.parents(".img");
        let fileid = imgObj.find("img").data("fileid")
        let op = {'type':'fileId', 'fileId':fileid }
        cancelFileDel(op, true);
        imgObj.html("").hide().siblings(".uploadImg").show();
    }
    bounce_Fixed3.cancel();

}
// creator: hxz，2020/02/06 显示图片信息
var IMGObj = null;
function showIMGInfo(obj) {
    IMGObj = obj;
    var hav = obj.find(".IMGinfo");
    if(hav.length >0){
        obj.find(".IMGinfo").show();
    }else{
        var filename = obj.find("img").data("filename");
        var url = obj.find("img").attr("src");
        var left = obj.offset().left - 155;
        var top = obj.offset().top + 31;
        var str = "<div class='IMGinfo' style='left:"+ left +"px;top:"+ top +"px'>" +
                        "<p class=\"picName\">文件名："+ filename +"</p>" +
                        "<a href='"+ url +"' target='_blank' class=\"ty-color-blue\" data-name='scanImg'>查看大图</a>" +
                        "<span type='btn' class=\"ty-color-red\" data-name='delImg'>删除附件</span>" +
                    "</div>";
        obj.append(str);
        obj.find(".IMGinfo").show().hide();
    }


}
function hideIMG(obj) {
    obj.find(".IMGinfo").hide();
}
// creator: 张旭博，2019-07-15 09:33:15，初始化上传插件
function initUpload() {
    //初始化图片上传

    $('#form_billEntry').find('.cp_imgUpload').html("").Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        formData:{
            module: '日常事务',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        fileObjName:'file',
        uploader:$.webRoot + "/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var fileUid =data.fileUid;
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                fullPath = $.fileUrl + data.filename ;
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =    '<div class="cp_img_box">' +
                '   <div class="fileType" style="background-image: url('+ fullPath +')">' +
                '   <a path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                '   <a class="hd fileUid">'+ fileUid +'</a>' +
                '   </div>'+
                '   <div class="cp_img_name">'+file.name+'</div>' +
                '</div>';


            $('#form_billEntry').find(".cp_imgShow").html(imgStr);
        }
    });
    $('.quotaInvoice').find('.cp_imgUpload').html("").Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        formData:{
            module: '日常事务',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        fileObjName:'file',
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var fileUid =data.fileUid;
            //file 文件上传返回的参数  data 接口返回的参数
            var path = $.fileUrl + data.filename,  //路径（包含文件类型）
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =    '<div class="cp_img_box">' +
                '   <div class="fileType" style="background-image: url('+path+')">' +
                '   <a path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                '   <a class="hd fileUid">'+ fileUid +'</a>' +'   </div>'+
                '   <div class="cp_img_name">'+file.name+'</div>' +
                '</div>';
            $('.quotaInvoice').find(".cp_imgShow").html(imgStr);
        }
    });

}
// creator: 张旭博，2019-05-24 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 我要报销 - 主页面
            case 'reimburse':
                var billCount = 0
                var count = 0
                var num = 0
                var nowMonth = formatTime(new Date()).substring(0, 7)
                var monthList = []
                $("#reimburse tbody tr").each(function () {
                    var billAmount = $(this).find('.billAmount').html()
                    var numbers = Number($(this).find('.number').html())
                    var amount = $(this).find('.amount').html()
                    billCount += parseFloat(billAmount)
                    count += parseFloat(amount)
                    num += parseInt(numbers)
                    var month = $(this).data('month')
                    if (month) {
                        monthList.push(month === nowMonth)
                    }
                });
                var isTip = false
                if (monthList.length > 1) {
                    for (var i=0; i<monthList.length; i++){
                        if(i<monthList.length-1){
                            if(monthList[i] !== monthList[i+1]){
                                isTip = true
                            }
                        }
                    }
                }

                if (isTip) {
                    $(".isMonthTip").show()
                } else {
                    $(".isMonthTip").hide()
                }
                $(".billCountAmount").html(billCount.toFixed(2))
                $(".countAmount").html(count.toFixed(2))
                $(".billCountNumber").html(num)
                var state = 0
                // $("#form_reimburse").find("[require]:visible").each(function () {
                //     if($.trim($(this).val()) === ''){
                //         state++
                //     }
                // });
                // $("#form_reimburse .textMax").html($("#form_reimburse [name='purpose']").val().length+'/255')
                if(!$(".transactionType input:radio:checked").val()){state++}
                if (num === 0 || state > 0 || $(".isMonthTip").is(":visible")) {
                    $("#reimburseBtn").prop("disabled",true)
                } else {
                    $("#reimburseBtn").prop("disabled",false)
                }
                break;
            // 我要报销 - 货物录入
            case 'goodEntry':
                var state = 0
                var isChange = $("#goodEntry").data('type')
                // $("#goodEntry").data('type', 'newNext')
                // 不同票据类型 展示和隐藏
                var kindInvoice = false , // 显示哪种类型录入
                    billNo = false, // 显示发票号码
                    billNoRed = false, // 显示发票前的必填红星
                    itemQuantityRed = true, // 显示数量前的必填红星
                    itemNameRed = true, // 显示票据信息前的必填红星
                    memoItemShow = true, // 显示发票上的备注
                    moveWidth = "600px", //
                    nextTip = false; //  显示想要录入的票据上只有一行内容项

                if(isChange === "change" || isChange === 'newNext'){
                    $("#goodEntry [name='billCat']").prop("disabled",true)
                }else{
                    $("#goodEntry .bonceFoot").children().hide();
                    $("#goodEntry [name='billCat']").prop("disabled",false)
                }
                var billCatVal = $("#billCat").val()
                var onlyOneRow = $("#onlyOneRow").val()
                var billCatName = $("#billCat").find("option:selected").html();
                var hightLight = true, sumNumber = 0, sumAmount1 = 0, sumAmount2 = 0 ; // 默认高亮

                if(billCatName === '增值税专用发票'){
                    $("#model").prop("disabled" , true);
                    $("#unit").prop("disabled" , true);
                    $("#uniPrice").prop("disabled" , true);
                    $("#price").prop("disabled" , true);
                    $("#taxRate").prop("disabled" , true);
                    $("#taxAmount").prop("disabled" , true);
                    $("#amount").prop("disabled" , true);
                } else if(billCatName === '增值税普通发票'){
                    $("#model").prop("disabled" , false);
                    $("#unit").prop("disabled" , false);
                    $("#uniPrice").prop("disabled" , true);
                    $("#price").prop("disabled" , false);
                    $("#taxRate").prop("disabled" , true);
                    $("#taxAmount").prop("disabled" , false);
                    $("#amount").prop("disabled" , amount);
                } else {
                    $("#model").prop("disabled" , false);
                    $("#unit").prop("disabled" , false);
                    $("#uniPrice").prop("disabled" , true);
                    $("#price").prop("disabled" , false);
                    $("#taxRate").prop("disabled" , false);
                    $("#taxAmount").prop("disabled" , false);
                    $("#amount").prop("disabled" , false);
                }

                // 判断
                if(billCatVal !== ''){
                    if (billCatName === '增值税普通发票' || billCatName === '其他普通发票' || billCatName === '收据') {
                        if(isChange === "change" || isChange === 'newNext'){
                            nextTip = false ;
                        }else{
                            nextTip = true ;
                        }

                        if(onlyOneRow === '1'){
                            if( billCatName === '收据'){
                                $(".oneRowTab thead td:eq(1)").hide()
                                $(".oneRowTab thead td:eq(9)").hide()
                                $(".oneRowTab tbody tr").each(function(){
                                    $(this).children(":eq(1)").hide();
                                    $(this).children(":eq(9)").children("span").hide();
                                    $(this).children(":eq(9)").children("i").hide();
                                    $(this).children(":eq(9)").find("div.ctrlConBtn").css({ "right":'-120px' , "top":'-15px'});
                                    $(this).children(":eq(9)").css({ "border-color": '#F0F8FF', 'background':'#F0F8FF' });
                                })
                                $("#in4").html("票据金额");
                            }else{
                                $(".oneRowTab thead td:eq(1)").show()
                                $(".oneRowTab thead td:eq(9)").show().html("发票上的备注")
                                $(".oneRowTab tbody tr").each(function(){
                                    $(this).children(":eq(1)").show();
                                    // $(this).children(":eq(9)").show();
                                    $(this).children(":eq(9)").children("span:not(.hd)").show();
                                    $(this).children(":eq(9)").children("i").show();
                                    $(this).children(":eq(9)").children("div").css({ "right":'-10px' , "top":'-8px'});
                                    $(this).children(":eq(9)").css({ "border-color": '#d7d7d7', 'background':'#fff' });

                                })
                                $("#in4").html("发票金额");
                            }

                            kindInvoice = ".oneRowInvoice" ;
                            moveWidth = "1660px";

                            // 录入按钮
                            $("#inputNextBtn").html("录入下一张只有一行内容的" + billCatName)
                            $(".oneRowTab tbody").children("tr").each(function(index){
                                var feeCats = $(this).children(":eq(0)").children(".hd").html() ;
                                var no = $(this).children(":eq(1)").children("input").val();
                                var date = $(this).children(":eq(2)").children("input").val();
                                var sum = $(this).children(":eq(7)").children("input").val();
                                var sum2 = $(this).children(":eq(8)").children("input").val();
                                // if(Number(sum) > 0){ $(this).children(":eq(7)").children("input").val( Number(sum).toFixed(2));  }
                                // if(Number(sum2) > 0){ $(this).children(":eq(8)").children("input").val( Number(sum2).toFixed(2));  }
                                if(feeCats == "" || date == "" || sum == "" || sum2 == "" ){
                                    hightLight = false;
                                }else{
                                    if(billCatName !== '收据'&& billCatName !== '其他普通发票'){
                                        if(no == ""){
                                            hightLight = false;
                                        }else{
                                            sumNumber++;
                                            sumAmount1 += Number(sum) ;
                                            sumAmount2 += Number(sum2) ;
                                        }
                                    }else{
                                        sumNumber++;
                                        sumAmount1 += Number(sum) ;
                                        sumAmount2 += Number(sum2) ;
                                    }
                                }
                                if(index == 0){
                                    $(this).find(".clearTr").hide();
                                }
                            }) ;
                            sumAmount1 = sumAmount1.toFixed(2) ;
                            sumAmount2 = sumAmount2.toFixed(2) ;
                            $("#summary").html("您已录入"+ billCatName +"共 <span class='sumNumber'>"+ sumNumber +"</span> 张，发票金额总计 <span class='sumAmount1'>"+ sumAmount1 +"</span> 元，实际支出（即您将报销）总计 <span class='sumAmount2'>"+ sumAmount2 +"</span> 元。");

                            if(hightLight){
                                $("#goodEntryBtn").prop("disabled",false);
                                $("#inputNextBtn").attr("class", "ty-right ty-btn ty-btn-green ty-btn-big ty-circle-3").attr("disabled","true");
                            }else{
                                $("#goodEntryBtn").prop("disabled",true);
                                $("#inputNextBtn").attr("class", "ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("disabled");
                            }
                        }else if(onlyOneRow === '0'){
                            kindInvoice = ".VATInvoice" ;
                            moveWidth = "600px";
                            $(".VATInvoice>.ty-alert").hide();
                            if (billCatName !== '收据') {
                                billNo = true;
                                billNoRed = true;
                            }
                            if (billCatName == '其他普通发票') {
                                billNoRed = false;
                                itemNameRed = false;
                            }

                            if(billCatName !== '其他普通发票' && billCatName !== '收据'){
                                $(".t1").hide(); $(".t2").show();
                                $("#m1").html('货物或应税劳务、服务名称');
                                $("#m2").html('货物或应税劳务、服务名称');
                            }else{
                                $(".t2").hide(); $(".t1").show();
                                $("#m1").html('票据内容');
                                $("#m2").html('票据内容');
                                if(billCatName == '其他普通发票'){
                                    itemQuantityRed = false;
                                }else if(billCatName == '收据'){
                                    memoItemShow = false;
                                }
                            }
                        }else { // 可能还没选择
                            nextTip = true;
                            moveWidth = "600px";
                        }
                    }else {
                        $("#onlyOneRow").val("");
                        $("#goodEntry").find("i.fa-dot-circle-o").attr("class", "fa fa-circle-o");
                        if(billCatName === '定额发票'|| billCatName === '定额普通发票'){ // 所有定额的处理都在这里
                            kindInvoice = ".quotaInvoice" ;
                            moveWidth = "1660px";
                            var colNumTotal = 0, colPriceTotal = 0, colPriceTotal2 = 0 ;
                            $(".quotaInvoice tbody").children("tr").each(function (index) {
                                var feeCats = $(this).children(":eq(0)").children(".hd").html() ;
                                var rowSinglePrice = $(this).children(':eq(1)').children("input").val();
                                var rowNum = $(this).children(':eq(2)').children("input").val() ;
                                var practiceFee = $(this).children(':eq(4)').children("input").val()  ;
                                if(feeCats == "" || rowSinglePrice == "" || rowNum == "" || practiceFee == "" ){
                                    hightLight = false;
                                }
                                rowSinglePrice = rowSinglePrice || 0 ;
                                rowNum = rowNum || 0 ;
                                practiceFee = practiceFee || 0 ;
                                var rowAmountTotal = rowSinglePrice * rowNum;
                                $(this).children(':eq(3)').children("input").val(rowAmountTotal.toFixed(2));
                                colNumTotal += parseInt(rowNum);
                                colPriceTotal +=  parseFloat(rowAmountTotal)
                                colPriceTotal2 +=  parseFloat(practiceFee) ;

                                if(index == 0){
                                    $(this).find(".clearTr").hide();
                                }

                            })
                            // 备注剩余字符
                            $("#form_goodEntry .textMax").html($("#form_goodEntry [name='memo']").val().length+'/255');
                            if(hightLight){
                                colPriceTotal = colPriceTotal.toFixed(2);
                                colPriceTotal2 = colPriceTotal2.toFixed(2);
                                $("#goodEntryBtn").prop("disabled",false);
                                $("#summary2").html("您已录入"+ billCatName +"共 <span class='colNumTotal'> "+ colNumTotal +"</span> 张，发票金额总计 <span class='colPriceTotal'>"+ colPriceTotal +"</span> 元，实际支出（即您将报销）总计 <span class='colAmountTotal'>"+ colPriceTotal2 +"</span> 元。");
                                $("#inputNextBtn").attr("class", "ty-right ty-btn ty-btn-green ty-btn-big ty-circle-3").attr("disabled","true");
                                $("#newRow").attr("class", "ty-color-blue").attr("disabled","true");
                            }else{
                                $("#goodEntryBtn").prop("disabled",true);
                                $("#inputNextBtn").attr("class", "ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("disabled");
                                $("#newRow").attr("class", "ty-color-gray").attr("disabled","true");
                            }

                        }else if(billCatName === '增值税专用发票'){
                            kindInvoice = ".VATInvoice" ;
                            billNo = true;
                            billNoRed = true;
                            moveWidth = "600px";
                            $(".VATInvoice>.ty-alert").show();
                            $(".t1").hide(); $(".t2").show();
                            $("#goodEntry .bonceFoot").children().show();
                        }
                    }
                }

                // 实操
                if(kindInvoice){
                    $(kindInvoice).show().siblings().hide();
                    if(kindInvoice === ".VATInvoice"){ // VATInvoice 显示 用state判断按钮
                        $("#goodEntryBtn").html("确定")
                        if(billNo){
                            $(".VATInvoice .formItem_billNo").show()
                        }else{
                            $(".VATInvoice .formItem_billNo").hide()
                        }
                        if(billNoRed){
                            $(".formItem_billNo .ty-color-red").show();
                            $(".formItem_billNo input").attr('require', '')
                        }else {
                            $(".formItem_billNo .ty-color-red").hide();
                            $(".formItem_billNo input").removeAttr('require')
                        }
                        if(itemQuantityRed){
                            $(".formItem_itemQuantity .ty-color-red").show();
                            $(".formItem_itemQuantity input").attr('require', '')
                        }else {
                            $(".formItem_itemQuantity .ty-color-red").hide();
                            $(".formItem_itemQuantity input").removeAttr('require')
                        }
                        if(itemNameRed){
                            $(".formItem_itemName .ty-color-red").show();
                            $(".formItem_itemName input").attr('require', '')
                        }else {
                            $(".formItem_itemName .ty-color-red").hide();
                            $(".formItem_itemName input").removeAttr('require')
                        }
                        if(memoItemShow){
                            $(".formItem_itemMemo").show();
                            // $(".formItem_itemMemo input").attr('require', '')
                        }else {
                            $(".formItem_itemMemo").hide();
                            // $(".formItem_itemMemo input").removeAttr('require')
                        }

                        $(".tipTaxRate").hide();
                        switch (billCatName){
                            case "增值税专用发票":
                            case "增值税普通发票":
                                // 显示增值税发票表单
                                $(".taxInput").show();
                                break;
                            case "其他普通发票":
                            case "收据":
                                $(".taxInput").hide();
                                break;
                            default:
                        }
                        // 四个票据类型 计算单价或税额
                        var amount              = $("#goodEntry [name='price']").val();
                        if (billCatName === '增值税专用发票') {

                        }else if (billCatName === '增值税普通发票') {
                            var goodNum             = $("#goodEntry [name='itemQuantity']").val();
                            var tax                 = $("#goodEntry [name='taxAmount']").val();
                            if (goodNum && amount) {
                                var price = amount/goodNum;
                                $("#goodEntry [name='uniPrice']").val(price.toFixed(2));
                            } else {
                                $("#goodEntry [name='uniPrice']").val('');
                            }
                            if (amount && tax) {
                                var taxRate = tax/amount,
                                    taxTotal = -(-amount-tax);
                                var taxRatePercent = parseFloat( taxRate * 100 )
                                $("#goodEntry [name='taxRate']").val(taxRatePercent.toFixed(2));
                                $("#goodEntry [name='taxRate']").attr("value",taxRate.toFixed(2));
                                $("#goodEntry [name='amount']").val(taxTotal.toFixed(2));
                                if (taxRate > 1) {
                                    $(".tipTaxRate").show()
                                    state ++
                                } else {
                                    $(".tipTaxRate").hide()
                                }
                            } else {
                                $("#goodEntry [name='taxRate']").val('');
                                $("#goodEntry [name='taxRate']").attr("value",'');
                                $("#goodEntry [name='amount']").val('');
                            }
                        } else if ( billCatName === '其他普通发票' || billCatName === '收据'){
                            var goodNum             = $("#goodEntry [name='itemQuantity']").val();
                            if (goodNum && amount) {
                                var price = amount/goodNum;
                                $("#goodEntry [name='uniPrice']").val(price.toFixed(2));
                            } else {
                                $("#goodEntry [name='uniPrice']").val('');
                            }
                        } else {
                            var amount = $("#goodEntry .quotaInvoice [name='amount']").val();
                        }
                        if (amount == 0) {
                            state ++
                        }
                        $("#form_goodEntry").find("[require]:visible").each(function () {
                            if($.trim($(this).val()) === ''){
                                // if($(this).attr("name") === 'specialInterval' && !$("#newEvent [name='special']").prop("checked")){}
                                state++
                            }
                        });
                        if( state > 0){
                            $("#goodEntryBtn").prop("disabled",true)
                        }else {
                            $("#goodEntryBtn").prop("disabled",false)
                        }
                    }else{
                        $("#goodEntryBtn").html("录入完毕，下一步")
                    }
                }else{
                    $(".VATInvoice").hide().siblings().hide();
                }

                if(nextTip){
                    if(onlyOneRow === '1' || onlyOneRow === '0'){
                        $("#goodEntry .bonceFoot").children().show();
                    }else{
                        $("#goodEntry .bonceFoot").children().hide();
                    }

                    $(".ty-nextTip").show();
                }else {
                    if(billCatName === '定额发票'){
                        $("#goodEntry .bonceFoot").children().show();
                    }
                    $(".ty-nextTip").hide();
                }
                moveGoodEntry(moveWidth);
                break;
            // 我要报销 - 票据确认
            case 'billEntry':
                // 定时器
                var commonVal = $("#billEntry").data('state')
                countAllAmount()

                var number = parseInt($("#form_billEntry [name='number']").val())
                $("#billEntry .issueMonth").html(commonVal.issueDate.substring(0,4) + '年' + commonVal.issueDate.substring(5,7) + '月')
                // $("#billEntry .issueDate").html(commonVal.issueDate)
                $("#billEntry .thisBillNo").html(commonVal.billNo)
                $("#billEntry .thisBillNum").html(number - 1)
                var isEntry = $("#billEntry .handleBtn .ty-form-checkbox").hasClass('ty-form-checked')
                var state = 0
                $("#form_billEntry").find("[require]:visible").each(function () {
                    if($.trim($(this).val()) === ''){
                        state++;
                    }
                });
                if($(".isBillAmountTip").is(":visible")) {
                    state ++
                }
                if( state > 0 || !isEntry){
                    $("#billEntryBtn").prop("disabled",true)
                }else {
                    $("#billEntryBtn").prop("disabled",false)
                }
                break
        }

    });
}
// creator: 张旭博，2019-07-18 14:55:25，报销申请 - 修改
function changeBill(obj) {
    // 获取票据数据
    var billInfo = obj.parents('tr').find('.billInfo').html()
    if (billInfo) { billInfo = JSON.parse(billInfo) }
    var itemNum = billInfo.itemNum ;
    var billCatName = billInfo.billCatName
    var billCat = billInfo.billCat

    if(itemNum == 1){ // 单行的数据展示
        bounce_Fixed2.show($("#goodEntry"));
        $("#goodEntry").data('type', 'change')
        $("#goodEntry").data("obj", obj)
        $("#billCat").val(billCat).attr("disabled","disabled");
        $("#billCat option").each(function () {
            if($(this).html() === billCatName){
                $(this).attr("selected" , "selected");
            }else {
                $(this).removeAttr("selected");
            }
        });
        setEveryTime(bounce_Fixed, 'goodEntry');
        $(".ty-nextTip").hide();  $("#onlyOneRow").val(1);
        if (billCatName === '增值税普通发票' || billCatName === '其他普通发票' || billCatName === '收据') {
            $(".oneRowInvoice").show().siblings().hide();
            $(".oneRowInvoice tbody").html("");
            var bills = billInfo.bills ;
            for(var i = 0 ; i < bills.length ; i++){
                var str = "";
                var invoiceDateID = "I" + Math.random().toString(36).substr(2).substr(5,10) ;
                var good = bills[i]["billItems"][0], bill = bills[i];
                var feeJson = { "feeCat_1":good['feeCat'], "feeCat_2":good['secondFeeCat'],
                    "feeCatName_1":good['feeCatName'], "feeCatName_2":good['secondFeeCatName'] } ;
                var fee = good["feeCatName"] ;
                if(good['secondFeeCat']){
                    fee += " - "+ good["secondFeeCatName"] ;
                }
                str = "<tr>" +
                    "    <td class='ty-td-control'><span class='ty-color-blue FeeCats' onclick='oneSelect($(this))'>"+ fee +"</span><span class='hd'>"+ JSON.stringify(feeJson) +"</span></td>" +
                    "    <td><input type=\"text\" value='"+ bill.billNos[0] +"'></td>" +
                    "    <td><input id='"+ invoiceDateID +"' type=\"text\" class='big' value='"+ bill.issueDate  +"'></td>" +
                    "    <td><input type=\"text\" value='"+ good["itemName"] +"'></td>" +
                    "    <td><input type=\"text\" value='"+ good["model"] +"'></td>" +
                    "    <td><input type=\"text\" value='"+ good["unit"] +"'></td>" +
                    "    <td><input type=\"text\" onkeyup=\"clearNoNum(this)\" value='"+ good["itemQuantity"] +"'></td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["price"] +"'>元</td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["amount"] +"'>元</td>" +
                    "    <td>" +
                    "<span class='memoAcronym'>"+ good["memo"].substr(0,2) + "..." +"</span>" +
                    "<span class='hd'>"+ good["memo"] +"</span>" +
                    "<i class='fa fa-info addMemoBtn' title='添加备注' onclick='addMemo($(this))'></i>" +
                    "<div class='ctrlCon'>" +
                    "<div class='ctrlConBtn'>" +
                    "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                    "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>";
                if(bill.imgName){
                    let imgp = $.fileUrl + bill.imgPaths[0]
                    str +=  "<span type='btn' data-name='uploadImg' class='uploadImg' style='display: none' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                        "<span class='img' style='display:initial'>"+
                        "<div class='upItem' onmouseout='hideIMG($(this))' onmouseover='showIMGInfo($(this))'>" +
                        "<img src='"+ imgp +"' data-filename='"+ bill.imgName +"' />"+
                        "<div class='IMGinfo hd' >" +
                        "<p class='picName'>文件名："+ bill.imgName +"</p>"+
                        "<a target='_blank' class='ty-color-blue' data-type='scanImg' href='"+ bill.imgPaths[0] +"'>查看大图</a>"+
                        "<span type='btn' class='ty-color-red' data-type='delImg'>删除附件</span>"+
                        "</div>"
                    "</div>";
                }else{
                    str += "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                        "<span class='img'>";
                }

                str += "</span></div></div></td> </tr>";
                $(".oneRowTab").append(str) ;
                laydate.render({elem: '#'+invoiceDateID, istoday: true, festival: true});

            }
        }else {
            // 定额
            $(".quotaInvoice").show().siblings().hide();
            $(".quotaInvoice tbody").html("");
            var bills = billInfo.bills ;
            for(var i = 0 ; i < bills.length ; i++){
                var str = "";
                var invoiceDateID = "I" + Math.random().toString(36).substr(2).substr(5,10) ;
                var good = bills[i]["billItems"][0], bill = bills[i];
                var feeJson = { "feeCat_1":good['feeCat'], "feeCat_2":good['secondFeeCat'],
                    "feeCatName_1":good['feeCatName'], "feeCatName_2":good['secondFeeCatName'] } ;
                var fee = good["feeCatName"] ;
                if(good['secondFeeCat']){
                    fee += " - "+ good["secondFeeCatName"] ;
                }
                var str = "<tr>" +
                    "   <td class='ty-td-control'><span class='ty-color-blue FeeCats' onclick='oneSelect($(this))'>"+ fee +"</span><span class='hd'>"+ JSON.stringify(feeJson) +"</span></td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["uniPrice"] +"'>元</td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["itemQuantity"] +"'>张</td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["price"] +"'>元</td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["amount"] +"'>元" +
                    "<div class='ctrlCon'>" +
                    "<div class='ctrlConBtn'>" +
                    "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                    "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>";
                if(bill.imgName){
                    str +=  "<span type='btn' data-name='uploadImg' style='display:none;' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                        "<span class='img' style='display:initial'>"+
                        "<div class='upItem' onmouseout='hideIMG($(this))' onmouseover='showIMGInfo($(this))'>" +
                        "<img data-fileid='bill.fileArr[0]' src='"+ bill.imgPaths[0] +"' data-filename='"+ bill.imgName +"' />"+
                        "<div class='IMGinfo hd' >" +
                        "<p class='picName'>文件名："+ bill.imgName +"</p>"+
                        "<a target='_blank' class='ty-color-blue' data-type='scanImg' href='"+ bill.imgPaths[0] +"'>查看大图</a>"+
                        "<span  class='ty-color-red' data-type='delImg'>删除附件</span>"+
                        "</div>"+
                        "</div>";
                }else{
                    str += "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                        "<span class='img'>";
                }

                str +=  "</span></div></div></td> </tr>";

                $(".quotaInvoice tbody").append(str) ;
            }

        }

        return false
    }
    // 多行的数据显示
    $("#onlyOneRow").val(0);
    // 录入完毕的表单展示，勾选 ‘本张票录入完毕’按钮
    $("#form_billEntry").show()
    if (!$(".handleBtn .ty-form-checkbox").hasClass("ty-form-checked")) {
        $(".handleBtn .ty-form-checkbox").addClass("ty-form-checked")
    }

    // 获取货物数据
    var goods = billInfo.bills[0].billItems

    // 定额发票单独处理，因为是直接跳转到货物录入页面
    if (billCatName === '定额发票') {
        // 初始化
        document.getElementById('form_goodEntry').reset()
        $(".quotaInvoice tbody .addRow").remove()
        bounce_Fixed2.show($("#goodEntry"))
        // 开启验证
        setEveryTime(bounce_Fixed2, 'goodEntry')
        $(".quotaInvoice").show().siblings().hide()

        // 赋值表格
        var bills = billInfo['bills']
        var str = ''
        for(var j in bills){
            var good = bills[j]["billItems"][0]
            str += "<tr>" +
                "    <td><select onchange='setNextCat($(this))' class='feeCat_1'>"+ setFeeCats(good['feeCat'], feeCatsStr) +"</select></td>" +
                // "    <td><select class='feeCat_2'>"+ "" +"</select></td>" +
                "    <td><input type=\"text\" class='right' value='"+ good['uniPrice'] +"' onkeyup=\"clearNoNum(this)\">元</td>" +
                "    <td><input type=\"text\" class='right' value='"+ good['itemQuantity'] +"' onkeyup=\"clearNoNum(this)\">张</td>" +
                "    <td><input type=\"text\" class='right' value='"+ good['price'] +"' onkeyup=\"clearNoNum(this)\">元</td>" +
                "    <td><input type=\"text\" class='right' value='"+ good['amount'] +"' onkeyup=\"clearNoNum(this)\">元" +
                "<div class='ctrlCon'>" +
                "<div class='ctrlConBtn'>" +
                "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>"+
                "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                "<span class='img'></span>"+
                "</div>" +
                "</div>" +
                "</td>" +
                "</tr>";
            // str +=  '<tr class="addRow">' +
            //     '    <td><input type="text" class="rowSinglePrice" value="'+goods[j].uniPrice+'" require onkeyup="testNum(this)"></td>' +
            //     '    <td><input type="text" class="rowNum" style="width: 80px" value="'+goods[j].itemQuantity+'" require onkeyup="clearNum(this)">张</td>' +
            //     '    <td class="rowAmountTotal">'+goods[j].price+'</td>' +
            //     '    <td><span class="ty-color-red" type="btn" data-name="delRow">删除</span></td>' +
            //     '</tr>'
        }
        $(".quotaInvoice tbody").prepend(str)
        // 赋值一级费用类别和二级费用类别
        setFeeCat(billInfo)
        var imgPath = billInfo.imgPaths
        var imgStr = ''
        if (imgPath.length > 0) {
            imgStr =    '<div class="cp_img_box">' +
                '   <div class="fileType" style="background-image: url('+imgPath[0]+')">' +
                '   <a path="'+ imgPath[0] +'" onclick="seeOnline($(this))">预览</a>' +
                '   </div>'+
                '   <div class="cp_img_name">'+billInfo.imgName+'</div>' +
                '</div>';
        }
        $("#form_billEntry .textMax").html(billInfo.memo.length+'/255')
        $('#goodEntry').find(".cp_imgShow").html(imgStr);
        // 赋值代表是修改用的弹窗
        $("#goodEntry").data('type', 'change')
        $("#goodEntry").data('obj', obj)
        return false
    }
    // 其他发票在修改时的赋值，跳转到票据信息页面
    var str = ''
    // 最上方货物表格赋值
    for (var i in goods) {
        // 不同费用类别表格
        var feeCatName = goods[i].feeCatName
        var secondFeeCatName = goods[i].secondFeeCatName
        var catName = ''
        if (secondFeeCatName) {catName = feeCatName + '-' + secondFeeCatName} else {catName = feeCatName}
        if(billCatName === '其他普通发票') {
            str +=   '<tr>' +
                '<td>'+catName+'</td>'+
                '<td>'+goods[i].itemName+'</td>'+
                '<td>'+goods[i].model+'</td>'+
                '<td>'+goods[i].unit+'</td>'+
                '<td>'+goods[i].itemQuantity+'</td>'+
                '<td>'+goods[i].uniPrice+'</td>'+
                '<td>'+goods[i].price+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(goods[i])+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'

        } else if (billCatName === '收据') {
            str +=   '<tr>' +
                '<td>'+catName+'</td>'+
                '<td>'+goods[i].itemName+'</td>'+
                '<td>'+goods[i].model+'</td>'+
                '<td>'+goods[i].unit+'</td>'+
                '<td>'+goods[i].itemQuantity+'</td>'+
                '<td>'+goods[i].uniPrice+'</td>'+
                '<td>'+goods[i].price+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(goods[i])+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'
        } else {
            str +=   '<tr>' +
                '<td>'+catName+'</td>'+
                '<td>'+goods[i].itemName+'</td>'+
                '<td>'+goods[i].model+'</td>'+
                '<td>'+goods[i].unit+'</td>'+
                '<td>'+goods[i].itemQuantity+'</td>'+
                '<td>'+goods[i].uniPrice+'</td>'+
                '<td>'+goods[i].price+'</td>'+
                '<td>'+(goods[i].taxRate * 100).toFixed(2) +' %</td>'+
                '<td>'+goods[i].taxAmount+'</td>'+
                '<td>'+goods[i].amount+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(goods[i])+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'

        }
    }
    if (billCatName === '其他普通发票'){
        $("#billEntry .otherGood").show().siblings().hide()
        $("#billEntry .otherGood tbody").html(str)
    } else if (billCatName === '收据') {
        $("#billEntry .receipt").show().siblings().hide()
        $("#billEntry .receipt tbody").html(str)
    } else {
        $("#billEntry .VATGood").show().siblings().hide()
        $("#billEntry .VATGood tbody").html(str)
    }
    // 下方票据信息确认赋值
    var info = billInfo.bills[0]
    $("#form_billEntry [name='billAmount']").val(info['billAmount'])
    $("#form_billEntry [name='amount']").val(info['amount'])
    $("#form_billEntry [name='memo']").val(info['memo'])
    $("#form_billEntry [name='number']").val(billInfo['number'])

    // 票据数量单独处理，涉及到其他发票号码的显隐
    if(info.billNos.length > 1) {
        $("#form_billEntry .repeatBill").show()
        $("#form_billEntry .thisBillNo").html(info.billNos[0])
        var noStr = ''
        for (var k = 1 ;k < info.billNos.length; k++) {
            noStr += '<input type="text" class="billNoOther" value="'+ info.billNos[k]+'" require>'
        }
        $("#form_billEntry .repeatCon").html(noStr)
    } else {
        $("#form_billEntry .repeatBill").hide()
    }
    // 文件赋值
    var imgPath = billInfo.bills[0].imgPaths
    var fileUid = billInfo.bills[0].fileArr[0]
    var imgStr = ''
    if (imgPath.length > 0) {
        imgStr =    '<div class="cp_img_box">' +
            '   <div class="fileType" style="background-image: url('+ $.fileUrl + imgPath[0]+')">' +
            '   <a path="'+ imgPath[0] +'" onclick="seeOnline($(this))">预览</a>' +
            '   <a class="hd fileUid">'+ fileUid +'</a>' + '   </div>'+
            '   <div class="cp_img_name">'+billInfo.bills[0].imgName+'</div>' +
            '</div>';
    }
    $('#form_billEntry').find(".cp_imgShow").html(imgStr);

    // 展示票据信息页面
    bounce_Fixed.show($("#billEntry"))

    // 记录费用类别 和 开票日期 为后面 做判断(而且这些字段的值只有一个，录入多个货物按照最后录入的更新）
    var commonVal = {
        billCatName: billInfo.billCatName,
        billCat: billInfo.billCat,
        issueDate: info.issueDate,
        memo: info.memo,
        billNo: info.billNos[0] || ''
    }
    // console.log('每张票据的每次录入货物都会更新的子段（开票日期、票据类型、发票号码）：' + JSON.stringify(commonVal))
    $("#billEntry").data('state', commonVal)

    // 记录点击来源
    $("#billEntry").data('type', 'change')
    $("#billEntry").data('obj', obj)
    // 表单验证
    setEveryTime(bounce_Fixed, 'billEntry')
}
function setInput(){
    let isSet = true

    let info = $(".receipt .goodInfo").html()
    if(info){
        info = JSON.parse(info)
        let catName = info.billCatName
        if(catName == '收据'){
            isSet = false
        }
    }
   if(isSet){
       var number = parseInt($("#form_billEntry [name='number']").val())
       if(number - 1 >0){
           $("#billEntry .repeatBill").show()
           let str = ``;
           for(let i = 0; i < number - 1 ; i++){
               str += `<input type="text" class="billNoOther" require />`;
           }
           $(".repeatCon").html(str)
       }else{
           $("#billEntry .repeatBill").hide()
           $(".repeatCon").html('')
       }
   }

}
// creator: 张旭博，2019-07-15 11:36:58，票据信息 - 确定
function sureBillEntry(obj) {
    // 获取货物信息
    var bill = {}, commonBillItem = {}
    var goods = []
    $("#billEntry table:visible tbody tr").each(function () {
        var good = $(this).children('.goodInfo').html()
        good = JSON.parse(good)
        bill.memo = good['memo']
        goods.push(good)
    })
    $("#billEntry [name]:visible").each(function () {
        var name = $(this).attr("name")
        var type = $(this).data("type")
        if (type === 'num') {
            if(name == "number"){
                commonBillItem[name] =  Number($(this).val())
            }else{
                bill[name] =  Number($(this).val())
            }
        } else if (type === 'singleCheck') {
            bill[name] = $(this).is(':checked')
        } else {
            bill[name] = $(this).val()
        }
    })
    // 获取票据信息
    bill.billItems = goods
    bill.number = commonBillItem.number

    var state = $("#billEntry").data('state')
    // 这四个字段从state中去（因为是实时更新的）
    commonBillItem.itemCount =  0
    commonBillItem.billCat = state.billCat
    commonBillItem.billCatName = state.billCatName
    if (state.billNo) {
        bill.billNos = [state.billNo]
    } else {
        bill.billNos = []
    }
    $(".repeatCon input").each(function () {
        var val = $(this).val()
        bill.billNos.push(val)
    })
    bill.issueDate = state.issueDate
    var countAllAmount = $(".countAllAmount").html()
    bill.imgPaths = []
    bill.fileArr = []
    $("#form_billEntry .cp_imgShow .cp_img_box").each(function () {
        var path = $(this).find('a').attr('path')
        var fileUid = $(this).find('.hd.fileUid').html()
        var name = $(this).find('.cp_img_name').html()
        bill.imgPaths.push(path)
        bill.fileArr.push(fileUid)
        bill.imgName = name
    })
    bill.amount = bill.amount.toFixed(2);
    commonBillItem.bills = [bill];
    var str =   '<tr data-month="'+commonBillItem.bills[0].issueDate.substring(0, 7)+'">' +
        '<td>'+commonBillItem.billCatName+'</td>'+
        '<td>'+bill.billAmount+'</td>'+
        '<td class="number">'+commonBillItem.number+'</td>'+
        '<td class="billAmount">'+countAllAmount+'</td>'+
        '<td class="amount">'+bill.amount+'</td>'+
        '<td class="billInfo" style="display: none">'+JSON.stringify(commonBillItem)+'</td>'+
        '<td>'+
        '<span type="btn" data-name="changeBill" class="ty-color-blue">修改</span>'+
        '<span type="btn" data-name="delBill" class="ty-color-red">删除</span>'+
        '</td>'+
        '</tr>'
    // console.log(JSON.stringify(bill))
    var type = $("#billEntry").data('type')
    var obj = $("#billEntry").data('obj')
    if (type === 'change') {
        obj.parents('tr').replaceWith(str)
    } else {
        $("#reimburse table tbody").append(str)
    }
    bounce_Fixed.cancel()
    initForm('billEntry')
}

// creator: hxz 2021-0701 设置费用类别 - 订单管理
var feeCatsStr = ""; // 费用类别的字串
function getFeeCats() {
    var str='<option value="1">采购材料</option>';
    $("#feeCat").html(str).prop('disabled', true);
    feeCatsStr = str ;
}

// creator:hxz 2020/0205 设置而二级科目
function setNextCat(thisObj){
    var feeCatName = thisObj.find('option:selected').html()
    var feeCat = thisObj.val();
    if(feeCatName === '交通费' || feeCatName === '车务支出') {
        $("#pFeeCat2").show();
        getSecondFeeCat(feeCat, function (err, data) {
            var str = '<option value="">---请选择二级费用类别---</option>'
            for (var i in data) {
                if (Number(data[i].enabled) === 1) {
                    str += '<option value="' + data[i].id + '">' + data[i].name + '</option>'
                }
            }
            thisObj.parent().next().children("select").html(str)
        })
    }else {
        $("#pFeeCat2").hide();
        thisObj.parent().next().children("select").html("")
    }

}
// creator:hxz 2020/03/16 选择费用类别
var editTD = null; // 记录费用类别的操作td
function oneSelect(thisObj) {
    editTD = thisObj;
    $("#pFeeCat2").hide();
    $(".feeCat_1").html(feeCatsStr);
    bounce_Fixed3.show($("#selectFee"));
}
function selectFeeOk() {
    var feeCat_1 = $(".feeCat_1").val();
    if(feeCat_1 === ""){
        layer.msg("请选择费用类别！");
        return false;
    }
    var feeCatName_1 = $(".feeCat_1 option:selected").html();
    var feeJson = { "feeCat_1":feeCat_1, "feeCat_2":"", "feeCatName_1":feeCatName_1, "feeCatName_2":"" } ;
    var str = feeCatName_1 ;
    var v = $("#pFeeCat2").is(":visible")
    if(v){
        var feeCat_2 = $(".feeCat_2").val();
        if(feeCat_2 === ""){
            layer.msg("请选择二级类别");
            return false
        }
        var feeCatName_2 = $(".feeCat_2 option:selected").html();
        feeJson["feeCat_2"] = feeCat_2
        feeJson["feeCatName_2"] = feeCatName_2
        str += " - " + feeCatName_2 ;
    }
    editTD.html(str);
    editTD.siblings(".hd").html(JSON.stringify(feeJson));
    bounce_Fixed3.cancel();
}
// creator:hxz 2020/03/16 添加备注
function addMemo(thisObj) {
    editTD = thisObj;
    bounce_Fixed3.show($("#addMemo"));
    $("#memo3").val(thisObj.siblings(".hd").html());
}
function addMemoOk() {
    var memo3 = $("#memo3").val();
    editTD.siblings(".memoAcronym").html(memo3.substr(0,2) + "...");
    editTD.siblings(".hd").html(memo3);
    bounce_Fixed3.cancel()
}
// creator: 张旭博，2019-07-15 09:34:04，查询全部票据种类
function getBillCats(val) {
    $.ajax({
        url:"../expense/queryCodeCategory.do",
        data:{},
        success:function(data){
            if(typeof(data.billCats)==="undefined"){
                return false;
            }else{
                var str='<option value="">------请选择票据种类------</option>';
                for(var i=0;i<data.billCats.length;i++){
                    let name = data.billCats[i].name ;
                    if(val === 1 && (name == "增值税专用发票" || name == "增值税普通发票")){
                        str+='<option value='+data.billCats[i].id+'>'+data.billCats[i].name+'</option>'

                    } else if(val === 2 && (name == "增值税专用发票" || name == "增值税普通发票" || name == "收据")){
                        str+='<option value='+data.billCats[i].id+'>'+data.billCats[i].name+'</option>'

                    }
                }
                $("#billCat").html("").append(str);
            }
        }
    })
}
// creator: 张旭博，2019-07-15 11:32:57，货物录入 - 确定
function sureGoodEntry(obj) {
    // 搜集最后需要的货物详情参数
    // 定额发票传值和其他区别很大单独分开处理
    if($(".quotaInvoice").is(":visible")){
        var bills = [], commonBillItem = {};
        commonBillItem.billCat        = Number($("#billCat").val()) ;
        commonBillItem.billCatName    = $("#billCat").find("option:selected").html()
        commonBillItem.itemNum    = '1'
        commonBillItem.number = $(".colNumTotal").html()
        var billAmountOnly = 0;
        var allBillAmount = 0 , allAmount = 0;
        $(".quotaInvoice tbody tr").each(function () {
            var bill = {}
            var billItems = []
            var imgObj = $(this).find("img");
            // bill.billNos = []
            // bill.issueDate = ""
            if(imgObj.length == 0){
                bill.imgPaths      = []
                bill.fileArr      = []
            }else{
                let path = imgObj.attr('src')
                bill.imgPaths      = [path ? path.split('upload/')[1] : '']
                bill.fileArr      = [imgObj.data('fileid')]
            }
            // bill.imgPaths      = [imgObj.attr('src')]
            bill.imgName =  imgObj.data('filename')
            var feeCats = $(this).children(":eq(0)").children(".hd").html() ;
            feeCats = JSON.parse(feeCats) ;
            var good = {
                itemQuantity       : $(this).children(":eq(2)").children("input").val(),
                uniPrice           : $(this).children(":eq(1)").children("input").val(),
                price              :  $(this).children(":eq(3)").children("input").val(),
                feeCat             :  feeCats["feeCat_1"],
                feeCatName         :  feeCats["feeCatName_1"],
                secondFeeCat       : feeCats["feeCat_2"],
                memo                : "",
                secondFeeCatName   : feeCats["feeCatName_2"],
                amount   : $(this).children(":eq(4)").children("input").val()
            }
            bill.memo           =  good.memo
            bill.billAmount     = Number(good.price).toFixed(2)
            bill.number         = 1
            bill.amount         = Number(good.amount).toFixed(2)
            allBillAmount = (Number(bill.billAmount) + Number(allBillAmount)).toFixed(2)
            allAmount = (Number(bill.amount) + Number(allAmount)).toFixed(2)
            if(billAmountOnly != "--"){
                if(billAmountOnly == 0){
                    billAmountOnly = good.uniPrice
                }else{
                    if(billAmountOnly == good.uniPrice){}else{
                        billAmountOnly = "--"
                    }
                }
            }
            billItems.push(good);
            bill.billItems = billItems
            bills.push(bill);
        })
        commonBillItem.bills = bills;
        var str = '<tr>' +
            '<td>' + commonBillItem.billCatName + '</td>'+
            '<td>'+ billAmountOnly +'</td>'+
            '<td class="number">' + commonBillItem.number + '</td>'+
            '<td class="billAmount">' + allBillAmount + '</td>'+
            '<td class="amount">' + allAmount+ '</td>'+
            '<td class="billInfo" style="display: none">'+JSON.stringify(commonBillItem)+'</td>'+
            '<td>'+
            '<span type="btn" data-name="changeBill" class="ty-color-blue">修改</span>'+
            '<span type="btn" data-name="delBill" class="ty-color-red">删除</span>'+
            '</td>'+
            '</tr>';

        var type = $("#goodEntry").data("type")
        if(type === 'change') {
            var obj = $("#goodEntry").data("obj")
            obj.parents('tr').replaceWith(str)
        } else {
            $("#reimburse table tbody").append(str)
        }
        bounce_Fixed2.cancel()
        return false
    }
    /*--- 除开定额发票的其他发票处理 ----*/
    var display = $(".VATInvoice").css("display");
    if(display == "none"){ // 单行表格的情况
        var commonBillItem = {};
        commonBillItem.billCat        = $("#billCat").val()
        commonBillItem.itemCount        =  1
        commonBillItem.billCatName    = $("#billCat").find("option:selected").html()
        commonBillItem.number = 0
        commonBillItem.itemNum    = '1'
        var bills = [] ;
        // bill.imgPaths = [];
        var billAmountOnly = 0;
        var allBillAmount = 0 , allAmount = 0;
        $(".oneRowInvoice tbody tr").each(function () {
            commonBillItem.number++
            var bill = {};
            var billItems = []
            var imgObj = $(this).find("img");
            if(imgObj.length == 0){
                bill.imgPaths      = []
                bill.fileArr      = []
            }else{
                let path = imgObj.attr('src')
                bill.imgPaths  = [path ? path.split('upload/')[1] : '']
                bill.fileArr   = [imgObj.data('fileid')]
            }
            bill.imgName =  imgObj.data('filename')
            bill.issueDate = $(this).children(":eq(2)").children("input").val()
            bill.billNos      =  [ $(this).children(":eq(1)").children("input").val() ]; // 发票号
            var feeCats = $(this).children(":eq(0)").children(".hd").html() ;
            feeCats = JSON.parse(feeCats) ;
            var good = {
                unit         : $(this).children(":eq(5)").children("input").val(), // 单位
                model       : $(this).children(":eq(4)").children("input").val(), // 型号
                ordersItem       : $(this).children(":eq(3)").children("select").val(), // 货物或应税劳务、服务名称  票据内容
                itemName       : $(this).children(":eq(3)").children("select").children("option:selected").html(), // 货物或应税劳务、服务名称  票据内容
                itemQuantity       : $(this).children(":eq(6)").children("input").val(),
                uniPrice           : 0,
                price              :  $(this).children(":eq(7)").children("input").val(),
                taxRate            : '',
                taxAmount          : '',
                amount             : $(this).children(":eq(8)").children("input").val(), // 含税金额
                feeCat             :  feeCats["feeCat_1"],
                feeCatName         :  feeCats["feeCatName_1"],
                secondFeeCat       : feeCats["feeCat_2"],
                secondFeeCatName   : feeCats["feeCatName_2"] ,
                memo : $(this).children(":eq(9)").children(".hd").html()
            }
            bill.memo         = good.memo
            bill.billAmount     = good.price
            bill.amount         = good.amount

            allBillAmount = (Number(bill.billAmount) + Number(allBillAmount)).toFixed(2)
            allAmount = (Number(bill.amount) + Number(allAmount)).toFixed(2)

            bill.number         = 1
            if(billAmountOnly != "--"){
                if(billAmountOnly == 0){
                    billAmountOnly = good.amount
                }else{
                    if(billAmountOnly == good.amount){}else{
                        billAmountOnly = "--"
                    }
                }
            }
            billItems.push(good);
            bill.billItems = billItems ;
            bills.push(bill);
        })

        commonBillItem.bills = bills
        var str =   '<tr>' +
            '<td>'+commonBillItem.billCatName+'</td>'+
            '<td>'+ billAmountOnly +'</td>'+
            '<td class="number">'+commonBillItem.number+'</td>'+
            '<td class="billAmount">'+  allBillAmount +'</td>'+
            '<td class="amount">'+ allAmount +'</td>'+
            '<td class="billInfo" style="display: none">'+JSON.stringify(commonBillItem)+'</td>'+
            '<td>'+
            '<span type="btn" data-name="changeBill" class="ty-color-blue">修改</span>'+
            '<span type="btn" data-name="delBill" class="ty-color-red">删除</span>'+
            '</td>'+
            '</tr>'

        var type = $("#goodEntry").data('type')
        var obj = $("#goodEntry").data('obj')
        if (type === 'change') {
            obj.parents('tr').replaceWith(str)
        } else {
            $("#reimburse table tbody").append(str)
        }
        bounce_Fixed2.cancel()
        initForm('billEntry')


    }else{ // 不是单行的情况
        var good = {}
        $("#goodEntry [name]:visible").each(function () {
            var name = $(this).attr("name")
            var type = $(this).data("type")
            if(name == "ordersItem"){
                good['itemName'] = $("#itemName").children("option:selected").html();
            }
            if (type === 'num') {
                good[name] = Number($(this).val())
            }  else {
                good[name] = $(this).val()
            }
        })
        good.billCat            = $("#billCat").val()
        good.billCatName        = $("#billCat").find("option:selected").html()
        good.feeCatName         = $(".firstFeeCat select option:selected").html()
        good.feeCat             = $(".firstFeeCat select").val()
        good.secondFeeCatName   = $('.secondFeeCat').is(":visible")? $(".secondFeeCat select option:selected").html() : ''
        good.secondFeeCat       = $('.secondFeeCat').is(":visible")? $(".secondFeeCat select").val(): ''

        var type = $("#goodEntry").data('type')
        if(good.billCatName  == '其他普通发票' || good.billCatName  == '收据'){
            $("#m2").html('票据内容');
        }else{
            $("#m2").html('货物或应税劳务、服务名称');
        }
        // 在票据信息赋值来源为新增
        if(type === 'new') {
            // 如果货物录入是第一次新增的，那么票据信息页面设置为新
            $("#billEntry [name='number']").val(1)
            $("#billEntry").data('type', 'new')
            initForm('billEntry')

        }else{
            $("#billAmount").val("")
            let num = Number($("#number").val())
            if(num > 1){
                $(".repeatBill").show()
            }else{
                $(".repeatBill").hide()
            }
        }


        // 不同费用类别表格
        if(good.billCatName === '其他普通发票') {
            var str =   '<tr>' +
                '<td>'+good.feeCatName+(good.secondFeeCatName?'-'+good.secondFeeCatName : '')+'</td>'+
                '<td>'+good.itemName+'</td>'+
                '<td>'+good.model+'</td>'+
                '<td>'+good.unit+'</td>'+
                '<td>'+good.itemQuantity+'</td>'+
                '<td>'+good.uniPrice+'</td>'+
                '<td>'+good.price+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(good)+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'
            $("#billEntry .otherGood").show().siblings().hide()
            if (type === 'change') {
                var obj = $("#goodEntry").data('obj')
                obj.parents('tr').replaceWith(str)
            } else {
                $("#billEntry .otherGood tbody").append(str)
            }

        }
        else if (good.billCatName === '收据') {
            var str =   '<tr>' +
                '<td>'+good.feeCatName+(good.secondFeeCatName?'-'+good.secondFeeCatName : '')+'</td>'+
                '<td>'+good.itemName+'</td>'+
                '<td>'+good.model+'</td>'+
                '<td>'+good.unit+'</td>'+
                '<td>'+good.itemQuantity+'</td>'+
                '<td>'+good.uniPrice+'</td>'+
                '<td>'+good.price+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(good)+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'
            $("#billEntry .receipt").show().siblings().hide()
            if (type === 'change') {
                var obj = $("#goodEntry").data('obj')
                obj.parents('tr').replaceWith(str)
            } else {
                $("#billEntry .receipt tbody").append(str)
            }
        } else {
            good.taxRate /= 100
            var str =   '<tr>' +
                '<td>'+good.feeCatName+ (good.secondFeeCatName?'-'+good.secondFeeCatName : '')+'</td>'+
                '<td>'+good.itemName+'</td>'+
                '<td>'+good.model+'</td>'+
                '<td>'+good.unit+'</td>'+
                '<td>'+good.itemQuantity+'</td>'+
                '<td>'+good.uniPrice+'</td>'+
                '<td>'+good.price+'</td>'+
                '<td>'+(good.taxRate * 100).toFixed(2) +' %</td>'+
                '<td>'+good.taxAmount+'</td>'+
                '<td>'+good.amount+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(good)+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'
            $("#billEntry .VATGood").show().siblings().hide()
            if (type === 'change') {
                var obj = $("#goodEntry").data('obj')
                obj.parents('tr').replaceWith(str)
            } else {
                $("#billEntry .VATGood tbody").append(str)
            }
        }

        $("#billEntry .ty-alert .issueDate").html(good.issueDate)
        bounce_Fixed2.cancel()
        bounce_Fixed.show($("#billEntry"))

        // 记录费用类别 和 开票日期 为后面 做判断(而且这些字段的值只有一个，录入多个货物按照最后录入的更新）
        var commonVal = {
            billCatName: good.billCatName,
            billCat: good.billCat,
            issueDate: good.issueDate,
            memo: good.memo,
            billNo: $(".billNo").val()
        }
        $("#billEntry").data('state', commonVal)
        countAllAmount()
        $(".inputAmount").val($(".countAllAmount").html())
        setEveryTime(bounce_Fixed, 'billEntry')

    }


}
function cancelReimburseBtn(){
    bounce.cancel()
    $("#reimburse").find(".billInfo").each(function() {
        let data = JSON.parse($(this).html())
        let bills = data.bills
        bills.forEach(function(bill){
            let fileArr = bill.fileArr
            if(fileArr.length > 0){
                let fileid = fileArr[0]
                let op = {'type':'fileId', 'fileId':fileid }
                cancelFileDel(op, true);
            }
        })
    })
}
function setToFixed2(thisObj){
    thisObj.val(Number(thisObj.val()).toFixed(2));
}
function cancelfile() {
    $("#form_billEntry .cp_imgShow .cp_img_box").each(function () {
        var fileUid = $(this).find('.hd.fileUid').html()
        let op = {'type':'fileId', 'fileId':fileUid }
        cancelFileDel(op,true);
    })
    bounce_Fixed.cancel()
}
function setAmount() {
    var billAmount = $("#form_billEntry [name='billAmount']").val()
    var number = $("#form_billEntry [name='number']").val() || 1
    var allAmount = $("#billEntry").find(".countAllAmount").html()
    if(billAmount){
        allAmount = (billAmount * number).toFixed(2)
    }
    $("#billEntry").find(".inputAmount").val( allAmount)
}
// creator: 张旭博，2019-07-15 09:34:41，初始化各个表单
function initForm(name) {
    switch (name) {
        case 'reimburse':
            document.getElementById('form_reimburse').reset()
            $("#reimburse tbody").html('')
            break;
        case 'billEntry':
            $(".handleBtn .ty-form-checkbox").removeClass("ty-form-checked")
            $("#billEntry tbody").html('')
            // document.getElementById('form_billEntry').reset()
            // $("#billEntry .cp_imgShow").html('')
            // $("#billEntry .repeatCon").html('')
            // $(".repeatBill").hide()
            break;
    }
}
// creator:hxz 2020/0205 插入一行
function inputNextTr(type , isReplace){
    var invoiceDateID = "I" + Math.random().toString(36).substr(2).substr(5,10) ;
    switch (type){
        case 1:
            let feejson = {"feeCat_1":"","feeCat_2":"","feeCatName_1":"采购材料","feeCatName_2":""};
            var str = "<tr>" +
                // "    <td><select onchange='setNextCat($(this))' class='feeCat_1'>"+ feeCatsStr +"</select></td>" +
                // "    <td><select class='feeCat_2'></select></td>" +
                "    <td class='ty-td-control'><span class='ty-color-blue FeeCats' >采购材料</span><span class='hd'>"+ JSON.stringify(feejson) +"</span></td>" +
                "    <td><input type=\"text\" onchange='countWords($(this),8)'></td>" +
                "    <td><input id='"+ invoiceDateID +"' type=\"text\" class='big'></td>" +
                "    <td><select style='width:200px; ' class='litFont'>"+ mtsListStr +"</select></td>" +
                "    <td><input type=\"text\" onchange='countWords($(this),10)'></td>" +
                "    <td><input type=\"text\" onchange='countWords($(this),6)'></td>" +
                "    <td><input type=\"text\" onkeyup=\"clearNoNum(this);\" onchange='countWords($(this),10)'></td>" +
                "    <td><input type=\"text\" class='right' onchange='toFix2($(this))' onkeyup=\"clearNoNum(this);countWords($(this),8)\">元</td>" +
                "    <td><input type=\"text\" class='right' onchange='toFix2($(this))' onkeyup=\"clearNoNum(this);countWords($(this),8)\">元</td>" +
                "    <td>" +
                "<span class='memoAcronym'></span>" +
                "<span class='hd'></span>" +
                "<i class='fa fa-info addMemoBtn' title='添加备注' onclick='addMemo($(this))'></i>" +
                "<div class='ctrlCon'>" +
                "<div class='ctrlConBtn'>" +
                "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>"+
                "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                "<span class='img'></span>"+
                "</div>" +
                "</div>" +
                "</td>" +
                "</tr>";
            $(".oneRowTab").append(str) ;
            laydate.render({elem: '#'+invoiceDateID, istoday: true, festival: true});
            break;
        case 2:
            var str = "<tr>" +
                // "    <td><select onchange='setNextCat($(this))' class='feeCat_1'>"+ feeCatsStr +"</select></td>" +
                // "    <td><select class='feeCat_2'></select></td>" +
                "   <td class='ty-td-control'><span class='ty-color-blue FeeCats' onclick='oneSelect($(this))'>选择类别</span><span class='hd'></span></td>" +
                "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\">元</td>" +
                "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\">张</td>" +
                "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\">元</td>" +
                "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\">元" +
                "<div class='ctrlCon'>" +
                "<div class='ctrlConBtn'>" +
                "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>"+
                "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                "<span class='img'></span>"+
                "</div>" +
                "</div>" +
                "</td>" +
                "</tr>";

            $(".quotaInvoice tbody").append(str) ;
            break
    }


}
// creator: 张旭博，2019-07-15 09:33:46，根据一级费用类别获取二级费用类别
function getSecondFeeCat(feeCat, callback) {
    $.ajax({
        url:'/reimburseWindow/getSecondCodeList.do',
        data: {id: feeCat},
        success: function (data) {
            callback(null, data)
        }
    })
}
// creator: 张旭博，2019-07-12 10:29:03，计算票据信息页面 单张票据总金额
function countAllAmount() {
    var allAmount = 0
    var billAmount = $("#form_billEntry [name='billAmount']").val()
    var number = $("#form_billEntry [name='number']").val() || 1
    $("#billEntry table:visible tbody tr").each(function () {
        var goodInfo = $(this).find('.goodInfo').html()
        if (goodInfo) {goodInfo = JSON.parse(goodInfo)}
        var amount = goodInfo.amount || goodInfo.price
        allAmount += parseFloat(amount)
    })
    // 如果没有录入票据金额，那么默认给遍历货物的金额的票据金额合计，录入之后按输入与票据数量相乘得出的合计
    if (billAmount) {
        if (parseFloat(billAmount).toFixed(2) !== allAmount.toFixed(2)) {
            $(".isBillAmountTip").show()
        } else {
            $(".isBillAmountTip").hide()
        }
        allAmount = billAmount * number
    } else {
        $(".isBillAmountTip").hide()
    }
    $("#billEntry").find(".countAllAmount").html(allAmount.toFixed(2))
}
// creator:hxz 2020/05/26 两位数
function toFix2(thisObj) {
    thisObj.val(Number(thisObj.val()).toFixed(2));
}
// creator: 张旭博，2019-08-05 10:37:45，设置费用类别
function setFeeCat(billInfo) {
    $("#goodEntry .feeCat").val(billInfo.feeCat)
    if (billInfo.feeCatName === '车务支出' || billInfo.feeCatName === '交通费') {
        $("#goodEntry .secondFeeCat").show()
        getSecondFeeCat(billInfo.feeCat, function (err, data) {
            var str = '<option value="">---请选择二级费用类别---</option>'
            for (var i in data) {
                if (Number(data[i].enabled) === 1) {
                    str += '<option value="' + data[i].id + '">' + data[i].name + '</option>'
                }
            }
            $(".secondFeeCat select").html(str)
            $(".secondFeeCat select").val(billInfo.secondFeeCat)
        })
    } else {
        $("#goodEntry .secondFeeCat").hide()
    }
}
function cancelGoodsEntery() {
    bounce_Fixed2.cancel();
    $("#goodEntry").find(".img").each(function() {
        let fileid = $(this).find("img").data("fileid")
        let op = {'type':'fileId', 'fileId':fileid }
        cancelFileDel(op, true);
    })
}
// creator: hxz，2020/02/05 增普弹窗变化
function moveGoodEntry(targetWidth){
    var oldWidth = $("#goodEntry").css("min-width") ;
    if(oldWidth == targetWidth){
    }else{
        $("#goodEntry").css("min-width", targetWidth) ;
        bounce_Fixed2.show($("#goodEntry"));
    }
}
// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
        layer.msg("字数不能超过" + max + "个！");
    }
    obj.siblings(".textMax").text(curLength + '/' + max);
}



laydate.render({elem: '#issueDate', istoday: true, festival: true});



















