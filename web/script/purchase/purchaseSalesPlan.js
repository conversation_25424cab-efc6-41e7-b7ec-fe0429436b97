var pageSize = 20;
$(function() {
    showMainCon(1);
    $("#purchaseCompleted").hide();
    // mainCon1 二级导航
    $(".mainCon1 .ty-secondTab").children().click(function () {
        var index = $(this).index();
        index == 1? $("#purchaseCompleted").show():$("#purchaseCompleted").hide();
        $(".mainCon1 .ty-secondTab").children().removeClass('ty-active');
        $(".mainCon1 .ty-secondTab").children(":eq("+ index +")").addClass('ty-active');
        if (index == 0) {
            $(".mainCon1 .ttlStr").hide();
        } else {
            $(".mainCon1 .ttlStr").show();
        }
        getOrderList(1, index) ;
    });
    $(".mainCon1 .ty-secondTab").children(":eq(0)").click();
    // 所有table中的查看
    $("table").on('click', 'span[type="btn"]', function(){
        var type = $(this).data('type');
        var item = JSON.parse($(this).siblings(".hd").html());
        if(type == "orderDetail"){
            var num = $("#planConNum").html();
            $("#orderId").val(item.id);
            if (num == 0) {
                showMainCon(2);
            } else {
                showMainCon(5);
            }
            $(".arrowBtn").children("i").attr("class","fa fa-long-arrow-down");
            getOrderDetail(item.id, 'desc');
        } else if(type == "handle"){
            getMaterialDetail(item.id, $(this));
        } else if(type == "outstandingOrders"){
            getOutstandingOrders($(this));
        }
    });
    //无需订购勾选
    $(".nextStep").on('click', '.checkLable', function(){
        if (handleNull($(this).data("state")) == "") {
            var klass = $(this).children().hasClass('fa-dot-circle-o');
            if(klass){
                $(".mainCon3 input").prop("disabled", false);
                $(this).children().attr("class" , "fa fa-circle-o");
            }else{
                $(".mainCon3 input").val("").prop("disabled", true);
                $(this).children().attr("class" , "fa fa-dot-circle-o");
            }
        } else {
            layer.msg(`<p>操作失败！</p><p>因为改后的统筹数量不能小于采购已订购的数量！</p>`);
        }
    });
    $("body").on("click", ".ty-btn,.btnCat,.bonceHead>a", function () {
        var type = $(this).data('type');
        if(type == "orderCompleted"){
            showMainCon(4);
            getOrderList(1, 3);
        } else if(type == "goMain"){
            showMainCon(1);
        } else if(type == "goMainRush"){
            showMainCon(1);
            $(".mainCon1 .ty-secondTab .ty-active").click();
        } else if(type == "goPrev"){
            goPrev();
        } else if(type == "allFinished"){
            var state = 0;
            $(".mainCon2 table tbody tr").each(function () {
                $(this).find("td:eq(7)").html() == '未处理'? state++:'';
            });
            if (state == 0){
                var order = $("#orderId").val();
                allPurchaseFinished(order);
            } else {
                layer.msg("操作失败！尚有材料未处理完！");
            }
        } else if(type == "matPurchaseSure"){
            let qObj = $("#matsDetail tbody tr input:eq(0)");
            let tObj = $("#matsDetail tbody tr input:eq(1)");
            let tip = ``;
            if (Number(qObj.data("limit")) > Number(qObj.val())){
                tip = "<p>操作失败！</p><p>因为改后的统筹数量不能小于采购已订购的数量！</p>";
                layer.msg(tip);
            } else if (new Date(tObj.data("limit")) > new Date(tObj.val())){
                tip = "<p>操作失败！因为系统不支持统筹时间的提前。</p><p>如确需提前，请及时与采购线下联系！</p>";
                layer.msg(tip);
            } else {
                var empty = 0;
                $("#matsDetail tbody tr input").each(function () {
                    ($(this).val()).trim() == ""?empty++:'';
                });
                if(!$(".nextBtn .fa").hasClass("fa-dot-circle-o") && empty > 0){
                    layer.msg("还有必填项尚未填写！");
                    return false;
                }
                var info = JSON.parse($("#goodsDetail tbody tr").find(".hd").html());
                var matInfo = JSON.parse($("#matsDetail tbody tr").find(".hd").html());
                var unNum = $("#untreatedSize").val();
                var param = {
                    "itemId": info.id,
                    "material": "",
                    "versionNo": matInfo.versionNo || '1'
                }
                unNum <= 1? param.next =1: "";
                if($(".nextBtn .fa").hasClass("fa-dot-circle-o")){
                    param.needOrder = 0;
                } else {
                    param.needOrder = 1;
                    param.lowerQuantity = qObj.val();
                    param.latestDeliveryDate = tObj.val();
                }
                materialOrderSure(param);
            }
        }
    });
    $(document).click(function (e) {
        var targetM = $(e.target).hasClass("ssMonth");
        var targetY = $(e.target).hasClass("ssYear");
        if (!targetM) {
            if ($(".monthTb").is(":visible")) {
                $(".monthTb").slideUp('fast');
            }
        } else {
            $(".monthTb").slideDown('fast');
        }
        if (!targetY) {
            if ($(".yearTb").is(":visible")) {
                $(".yearTb").slideUp('fast');
            }
        } else {
            $(".yearTb").slideDown('fast');
        }
    });
    $(".timeBtn").on('click', '.monthTb>div,.yearTb>div', function(){
        getOrderList(1, 3, $(this)) ;
    });
    // 排列 升序降序
    $(".arrowBtn").click(function() {
        let o = $(this).children("i");
        let orderId = $("#orderId").val();
        let down = o.hasClass("fa-long-arrow-down");
        if(down){
            o.attr("class","fa fa-long-arrow-up");
        }else {
            o.attr("class","fa fa-long-arrow-down");
        }
        let typeNum = `` ;//asc 是升序  desc 倒序
        if(down){
            typeNum = `asc`;
        }else {
            typeNum = `desc` ;
        }
        getOrderDetail(orderId, typeNum);
    });
});
// create :hxz 2021-1-11  显示页面
function showMainCon(num){
    var page = $("#showMainConNum").html();
    if (num == 1 || page == "") {
        page = [num];
    } else {
        page = JSON.parse(page);
        page.push(num);
    }
    $("#showMainConNum").html(JSON.stringify(page));
    $(".mainCon" + num).show().siblings().hide();
}
// creator: 李玉婷，2022-01-13 16:27:51，返回上一页
function goPrev() {
    var page = JSON.parse($("#showMainConNum").html());
    if (page.length > 1){
        var num =page[page.length-2];
        page.splice(page.length-2,2);
        $("#showMainConNum").html(JSON.stringify(page));
        showMainCon(num);
    }
}
// creator: 李玉婷，2022-01-10 08:24:09，0-待统筹 1-已统筹，采购未下单 2-已统筹，采购未完成 3-已统筹，采购已完成
function getOrderList(cur, num, obj) {
    let param ={
        "currPage": cur,
        "pageSize": pageSize,
        "planState": num,
        "isScheduled": 2,
        "plan": "plan"
    }
    let pageObj = $(".mainCon1");
    let oper = `查看`;
    var jsonStr = {"num":num , "obj":obj };
    if (num == 0){
        oper = `处理`
        param.planState = `(${num})`;
    } else if(num == 1){
        param.planState = `(${num},2)`;
    } else if(num == 3){
        param.planState = `(${num})`;
        pageObj = $(".mainCon4");
        if(obj) {
            let time = obj.data("val");
            if (obj.parent().data("type") == 'month') {
                let date = new Date(time + '-1');
                param.start = time + '-1';
                param.end = new Date(date.getFullYear(), date.getMonth()+1, 0).format('yyyy-MM-dd');
            } else if (obj.parent().data("type") == 'year') {
                param.start = time + '-1-1';
                param.end = new Date(time, 12, 0).format('yyyy-MM-dd');
            }
        }
    }
    $("#planConNum").html(num);
    $.ajax({
        "url": "../sales/getSlOrders.do",
        "data": param,
        success: function (res) {
            var list = res.data;
            let html = ``;
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            if (list && list.length > 0) {
                let plTime = 0;
                for(var i=0;i<list.length;i++){
                    num !== 0 ? plTime = `<td>${list[i].si_create_name} ${new Date(list[i].si_create_date).format('yyyy-MM-dd hh:mm:ss')}</td>`: "";
                    html +=
                        `<tr>
                            <td>${list[i].sn}</td>
                            <td>${new Date(list[i].sign_date).format('yyyy-MM-dd')}</td>
                            <td>${list[i].customer_name}</td>
                            <td>${list[i].create_name} ${new Date(list[i].create_date).format('yyyy-MM-dd hh:mm:ss')}</td>
                            ${plTime}
                            <td>
                                <span class="btn ty-color-blue" type="btn" data-type="orderDetail">${oper}</span>
                                <span class="hd">${JSON.stringify(list[i])}</span>
                            </td>
                        </tr>`;
                }
            }
            if (num == 3) {
                let monthFirstDay = new Date(res.sysTime), monthEnd = new Date(res.monthEnd);
                let start = new Date(res.start || res.monthFirstDay).format('yyyy年M月d日');
                let end = new Date(res.end || res.monthEnd).format('yyyy年M月d日');
                pageObj.find(".duringTime").html(start+"-"+ end);
                pageObj.find(".saleOrderCount").html(res.totalRows);
                $(".monthTb").html(ssOption(monthFirstDay,monthEnd, 'month'));
                $(".yearTb").html(ssOption(monthFirstDay,monthEnd, 'year'));
                setPage( $("#yeCon1") , curr ,  totalPage , "planSlOrders", jsonStr );
            } else {
                setPage( $("#yeCon") , curr ,  totalPage , "planSlOrders", jsonStr );
            }
            pageObj.find("table tbody").html(html);
        }
    });
}
// creator: 李玉婷，2022-03-15 17:22:59，输出查询年月日期
function ssOption(start, end, type) {
    let result = ``;
    if (start.getFullYear() == end.getFullYear()) {
        if (type == 'year') {
            result = `<div data-val="${start.format('yyyy')}">${start.format('yyyy年')}</div>`;
        } else if (type == 'month') {
            if (start.getMonth()+1 == end.getMonth()+1) {
                result = `<div data-val="${start.format('yyyy-MM')}">${start.format('yyyy年MM月')}</div>`;
            } else {
                for (var a=start.getMonth();a<=end.getMonth();a++){
                    result += `<div data-val="${start.getFullYear()}-${a+1}">${start.getFullYear()}年${a+1}月</div>`;
                }
            }
        }
    } else {
        for (var a=start.getFullYear();a<end.getFullYear()-start.getFullYear();a++){
            if (type == 'year') {
                result += `<div data-val="${a}">${a}年}</div>`;
            } else {
                for (var i=0;i<end.getMonth()-start.getMonth();i++){
                    result += `<div data-val="${a}-${i+1}">${a}年${i+1}月</div>`;
                }
            }
        }
    }
    return result;
}
// creator: 李玉婷，2022-01-10 09:33:48，统筹处理
function getOrderDetail(id, state){
    var num = $("#planConNum").html();
    var pageObj = $(".mainCon2");
    if (num != 0) {
        pageObj = $(".mainCon5");
    }
    $.ajax({
        "url": "../sale/orderDetail.do",
        "data": {
            "orderId": id,
            "orderBy": state
        },
        success: function (res) {
            var list = res.items;//商品列表
            var orderDetails = res.orderDetails[0];
            let html = ``, key = ``, unNum = 0;
            pageObj.find("[need]").each(function(){
                key = $(this).data("name");
                if (key == 'sign_date'){
                    $(this).html(new Date(orderDetails[key]).format('yyyy-MM-dd'));
                }else if (key == 'orderCreate'){
                    $(this).html(orderDetails.create_name + ' ' + new Date(orderDetails.create_date).format('yyyy-MM-dd hh:mm:ss'));
                }else{
                    $(this).html(handleNull(orderDetails[key]));
                }
            });
            if (list && list.length > 0) {
                let extra = ``;
                for(var i=0;i<list.length;i++){
                    if (num == 0) {
                        extra = `
                        <td>${list[i].amount}</td>
                            <td>${new Date(list[i].delivery_date).format('yyyy-MM-dd')}</td>
                            <td>${changeState(handleNull(list[i].plan_state))}</td>
                            <td><span class="btn ty-color-blue" type="btn" data-type="handle">去处理</span>
                        `;
                    } else {
                        extra = `
                            <td>${list[i].amount} / ${Number(list[i].amount || 0) - Number(list[i].yet || 0)}</td>
                            <td>${list[i].lower_quantity || 0}</td>
                            <td>${list[i].po_item_amount || 0} / ${list[i].way_num || 0}</td>
                            <td>${handleNull(list[i].sn)}</td>
                            <td><span class="btn ty-color-blue" type="btn" data-type="handle">修改</span>
                        `;
                    }
                    handleNull(list[i].plan_state) == "" || handleNull(list[i].plan_state) == 0?unNum++ : "";
                    html +=
                        `<tr>
                            <td>${list[i].outer_sn}</td>
                            <td>${list[i].outer_name}</td>
                            <td>${list[i].out_model}</td>
                            <td>${list[i].out_specifications}</td>
                            <td>${list[i].unit}</td>
                            ${extra}
                                <span class="hd">${JSON.stringify(list[i])}</span>
                            </td>
                        </tr>`;
                }
                $("#signCreate span").html(list[0].si_create_name + ' ' + new Date(list[0].si_create_date).format('yyyy-MM-dd hh:mm:ss'));
            }
            pageObj.find("table tbody").html(html);
            if (num == 0) {$("#untreatedSize").val(unNum);}
        }
    });
}
// creator: 李玉婷，2022-01-17 14:48:13，待统筹-去处理-材料订购
function getMaterialDetail(id, obj){
    $.ajax({
        "url": "../po/pop",
        "data": {
            "itemId": id
        },
        success: function (res) {
            let html = ``, amountVal = ``, dateVal = ``;
            var list = res.data;//商品列表
            if ((handleNull(list[0].terminate_date) == "")  && list[0].yet != 0 ) {
                if (list && list[0].code == null){
                    layer.msg("商品构成不合适");
                } else {
                    var item = JSON.parse(obj.siblings(".hd").html());
                    showMainCon(3);
                    let matStr =
                        `<tr>
                    <td>${item.outer_sn}</td>
                    <td>${item.outer_name}</td>
                    <td>${item.out_model}</td>
                    <td>${item.out_specifications}</td>
                    <td>${item.unit}</td>
                    <td>${item.amount}</td>
                    <td>${new Date(item.delivery_date).format('yyyy-MM-dd')}</td>
                    <td><span class="btn ty-color-blue" type="btn" data-type="outstandingOrders">查看</span>
                        <span class="hd">${JSON.stringify(item)}</span>
                    </td>
                </tr>`;
                    $("#modName").html(item.outer_name);
                    $("#goodsDetail tbody").html(matStr);
                    if (list && list.length > 0) {
                        for(var i=0;i<list.length;i++){
                            amountVal = handleNull(list[i].lower_quantity);
                            dateVal = new Date(list[i].latest_delivery_date).format('yyyy-MM-dd');
                            html +=
                                `<tr data-id="${list[i].id}">
                            <td>${handleNull(list[i].code)}</td>
                            <td>${handleNull(list[i].name)}</td>
                            <td>${handleNull(list[i].model)}</td>
                            <td>${handleNull(list[i].specifications)}</td>
                            <td>${handleNull(list[i].unit)}</td>
                            <td>${list[i].amount || 0}</td>
                            <td>${list[i].current_stock || 0}</td>
                            <td class='ty-td-control' onclick="getInTransit($(this))"><span class='ty-color-blue'>${list[i].way_num || 0}</span></td>
                            <td><input type="text" data-orig="${amountVal}" data-limit="${handleNull(list[i].order_quantity)}" value="${amountVal}" name="amount" onblur="giveTip($(this))"/></td>
                            <td><input type="text" data-orig="${dateVal}" data-limit="${list[i].poi_delivery_date}" value="${dateVal}" class="lastArrivalTime" name="arrivalDate" />
                            <span class="hd">${JSON.stringify(list[i])}</span>
                            </td>
                        </tr>`;
                        }
                    }
                    $(".nextBtn .checkLable").data("state", list[0].order_quantity);
                    $("#matsDetail tbody").html(html);
                    if (list[0].need_order || list[0].need_order == null){
                        $(".nextBtn .fa").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
                    } else {
                        $(".nextBtn .fa").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
                        $("#matsDetail input").val("").prop("disabled", true);
                    }
                    laydate.render({
                        elem: '.lastArrivalTime',
                        format: 'yyyy-MM-dd',
                        done: function(){
                            giveTip($(".lastArrivalTime"));
                        }
                    });
                }

            } else {
                let pStr = `<p>操作失败！</p><p>因为这个销售订单已不再需要发货！</p>`;
                layer.msg(pStr);
            }
        }
    });
}
// creator: 李玉婷，2022-02-14 18:46:32，统筹数量/统筹时间判断
function giveTip(obj) {
    var num = $("#planConNum").html();
    if (num !== 0) {
        var name = obj.attr("name");
        var org = obj.data("orig");
        var val = obj.val();
        var tip = '';
        var limit = obj.data("limit");
        if (name == "amount") {
            if (Number(limit) > Number(val)){
                tip = "<p>操作失败！</p><p>因为改后的统筹数量不能小于采购已订购的数量！</p>";
                layer.msg(tip);
                obj.val(org);
            }
        } else if (name == "arrivalDate") {
            if (limit != null && new Date(limit) > new Date(val)){
                tip = "<p>操作失败！因为系统不支持统筹时间的提前。</p><p>如确需提前，请及时与采购线下联系！</p>";
                layer.msg(tip);
                obj.val(org);
            }
        }
    }
}
// creator: 李玉婷，2022-01-17 15:07:07，待统筹-去处理-材料订购确定
function materialOrderSure(param){
    let orderId = $("#orderId").val();
    param.material = $("#matsDetail tbody tr").data("id");
    $.ajax({
        "url": "../po/op",
        "data": param,
        success: function (res) {
            if (res.code === 2) {
                layer.msg(res.message)
            } else {
                var num = $("#planConNum").html();
                let typeNum = `` ;//asc 是升序  desc 倒序
                goPrev();
                loading.open();
                $(".arrowBtn").children("i").attr("class","fa fa-long-arrow-down");
                if (num == 3){
                    getOrderList(1,3)
                } else if (num == 0) {
                    let down = $(".arrowBtn").children("i").hasClass("fa-long-arrow-down");
                    if(down){
                        typeNum = `asc`;
                    }else {
                        typeNum = `desc` ;
                    }
                }
                getOrderDetail(orderId, typeNum);
            }
        }
    });
}
// creator: 李玉婷，2022-01-19 08:54:37，待统筹-去处理-均已处理，下一步
function allPurchaseFinished(id){
    $.ajax({
        "url": "../po/ops",
        "data": {"orderId":id},
        success: function (res) {
            if (res.code == 1) {
                loading.open();
                showMainCon(1);
                var navIndex = $(".mainCon1 .ty-secondTab li").index($(".mainCon1 .ty-secondTab .ty-active"));
                getOrderList(1, navIndex);
            } else {
                layer.msg("操作失败");
            }
        }
    });
}
// creator: 李玉婷，2022-02-23 14:49:31，未完成的销售订单
function getOutstandingOrders(obj) {
    var item = JSON.parse(obj.siblings(".hd").html());
    let key = ``;
    $("#outstandingOrders [need]").each(function () {
        key = $(this).data("name");
        $(this).html(handleNull(item[key]));
    });
    $.ajax({
        "url": "../po/occOrders",
        "data": {"id":item.id},
        success: function (res) {
            var list = res['data'];
            let html = ``, total = ``;
            if(list){
                for(var i = 0 ; i <list.length ; i++){
                    total += Number(list[i].transit_quantity || 0);
                    html +=
                        `<tr>
                            <td>${list[i].sn}</td>
                            <td>${new Date(list[i].sign_date).format('yyyy-MM-dd')}</td>
                            <td>${list[i].customer_name}</td>
                            <td>${list[i].so_amount || '0'}/${Number(list[i].so_amount || '0') - Number(list[i].yet_amount || '0')}</td>
                            <td>${list[i].order_quantity || '0'}}</td>
                            <td>${list[i].po_amount || '0'}/${list[i].transit_quantity || '0'}</td>
                            <td>${list[i].po_sn}</td>
                        </tr>`;
                }
            }
            $("#outstandingOrders table tbody").html(html);
            bounce.show($("#outstandingOrders"));
        }
    });
}
// creator: 李玉婷，2021-07-28 09:15:12，获取在途数量
function getInTransit(obj) {
    var info = JSON.parse(obj.parents("tr").find(".hd").html());
    var key = '';
    $("#inTransit [need]").each(function(){
        key = $(this).data("name");
        if( key == 'way_num') {
            $(this).html(info[key] || '0');
        } else {
            $(this).html(info[key]);
        }
    });
    $.ajax({
        "url":"../po/transitList",
        "data":{"id": info.id},
        success:function (res) {
            var list = res['data'];
            let html = ``, total = ``;
            if(list){
                for(var i = 0 ; i <list.length ; i++){
                    total += Number(list[i].transit_quantity || 0);
                    html +=
                        `<tr>
                            <td>${formatType(list[i].type)}</td>
                            <td>${list[i].quantity || '0'}/${list[i].transit_quantity || '0'}</td>
                            <td>${new Date(list[i].delivery_date).format('yyyy-MM-dd')}</td>
                            <td>${orderProgress(list[i].progress)}</td>
                            <td>${list[i].sn}</td>
                            <td>${list[i].name}</td>
                            <td>${list[i].create_name} &nbsp;&nbsp; ${ new Date(list[i].create_date).format('yyyy-MM-dd hh:mm:ss')}</td>
                        </tr>`;
                }
            }
            $("#inTransit table tbody").html(html);
            bounce.show($("#inTransit"));

        }
    })
}
// creator: 李玉婷，2022-01-17 19:22:21，状态输出
function changeState(type){
    if (type == "" || type == 0) {
        return "未处理";
    } else {
        return "已处理";
    }
}

// creator: 李玉婷，2021-10-29 8:11:15，购买理由
function formatType (type) {
    let str = ''
    switch (type) {
        case '2':
        case 2:
            str = '库存预警'
            break
        case '3':
        case 3:
            str = '个人申购的新材料'
            break
        case '1':
        case 1:
            str = '补货'
            break
        case '5':
        case 5:
            str = '零星采购'
            break
        case '6':
        case 6:
            str = '销售新订单所需的采购'
            break
        default:
    }
    return str
}
// creator: 李玉婷，2021-12-07 21:36:02，订单进度
function orderProgress(state){
    let str = ''
    switch (state) {
        case '0':
        case 0:
            str = '审批驳回'
            break
        case '1':
        case 1:
            str = '订单已提交，待审批'
            break
        case '2':
        case 2:
            str = '已下单，待到货'
            break
        case '3':
        case 3:
            str = '已到货，待检验'
            break
        case '4':
        case 4:
            str = '检验ok，待入库'
            break
        case '5':
        case 5:
            str = '检验不合格，待提交让步评审'
            break
        case '6':
        case 6:
            str = '检验不合格，待让步评审'
            break
        case '7':
        case 7:
            str = '让步评审未通过'
            break
        case 'Z':
            str = '完结'
            break
        default:
    }
    return str
}


