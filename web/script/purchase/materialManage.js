var mtCategories = null; // 全部种类数据
var listData = { 'category':false , 'state':false , 'keyword':'' } // 列表的传值数据
var editObj = null; // 当前操作的对象  请不要更改editObj的指向定义， 牵一发动全身
var editObjFixedMassage = null; // 确认定点信息当前操作的对象  请不要更改editObj的指向定义， 牵一发动全身
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
var bounce_Fixed4 = new Bounce(".bounce_Fixed4");
$(function () {
    $.ajax('../skl/getStockMode')
        .then(res => {
            let status = res.status
            $("body").data('whmode', status) //0-非智能 1-智能
        })
    $("#remoecontact").on('click','.fa',function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    })
    $('tbody').on('click', 'span', function(){
        let type = $(this).data('type');
        switch (type){
            case 'fixedMassage':
                editObjFixedMassage = $(this);
                var info = JSON.parse($(this).siblings(".hd").html());
                $(".mt31 .mt31_create").html(info['create_name'] + " " + (new Date(info['create_date']).format('yyyy-MM-dd hh:mm:ss')));
                for(var key in info){
                    $(".mt31 .mt31_"+key).html(info[key]);
                }
                $(".mt31 tbody tr").children(":last").find(".hd").html(JSON.stringify(info));
                $(".mt32 tbody").html("");
                getSuppliers(info.id, 1, 'mt32');
                toggleSuspend(3);
                break;
            case 'mtDel':
                bounce.show($('#delMT'));
                break;
            case 'sup':
                // 跳转供应商列表
                editObj = $(this) ;
                var info = $(this).parent().next().children(".hd").html();
                $(".mt31 tbody tr").children(":last").find(".hd").html(info)
                info = JSON.parse(info)
                $(".mt41 .mt41_create").html(info['create_name'] + " " + (new Date(info['create_date']).format('yyyy-MM-dd hh:mm:ss')));
                for(var key in info){
                    if(key == 'location_number' ){
                        $(".mt41 .mt41_"+key).children("span").html(info[key] ? (info[key]+'个') : '0个');
                    }else if(key == 'initial_stock'){
                        $(".mt41 .mt41_"+key).children("span").html(info[key] ? (info[key]) : '0');
                    }else{
                        $(".mt41 .mt41_"+key).html(info[key] );
                    }
                }
                $(".mt42 tbody").html("");
                getSuppliers(info.id, 1, 'mt42');
                $(".stopSupListBtn").show();
                $(".addMtSupBtn").show();
                $(".addNewSupBtn").show();
                $(".noFixedPurchaseLogBtn").show();
                $(".noFixedPurchaseBtn").hide();
                $(".pauseSup").hide().siblings().show();

                $("#hidePause4").hide();
                toggleSuspend(4);
                break;
            case 'initStock':
                // 初始库存
                showStorageHis();
                break;
            case 'initStockBySup':
                // 初始库存一个供应商的
                var sup = JSON.parse($(this).parent().siblings(":last").children(".hd").html());
                showStorageHis(sup);
                break;
            case 'storeHouse':
                // 占用库位
                getStoreHouse();

                break;
            case 'storeHouseBySup':
                // 占用库位
                bounce.show($('#storeHouse'));
                var sup = JSON.parse($(this).parent().next().children(".hd").html());
                var supId = sup['material_supplier_id'];
                getStoreHouse(supId);

                break;
            case 'fixedScan':
                // 查看定点信息
                var mtInfo = {};
                if(editObj){
                    mtInfo = JSON.parse(editObj.parent().next().children(".hd").html());
                }else if(editObjFixedMassage){
                    mtInfo = JSON.parse(editObjFixedMassage.siblings(".hd").html());
                }else{
                    layer.msg("获取材料失败")
                    return false;
                }
                var mtID = mtInfo['id'];
                var supInfo = JSON.parse( $(this).siblings(".hd").html());
                $("#cgaddcontact3").data("box",supInfo);
                var supID = supInfo['supplier_id'];
                var suid = supInfo["id"];
                var relationID = supInfo['material_supplier_id'];
                fixedScanInfo(mtID, supID, relationID,supInfo);
                bounce.show($("#fixedScan"));
                $("#fixedScan .edit").hide();$("#fixedScan .scan").show();
                let messge = $(this).siblings(".hd").html();
                $("#fixedScan").data("mess",messge);    //用于获取供应商id
                break;
            case 'stopPurchase':
                // 暂停采购
                bounce.show($('#stopPurchase'));
                var supInfo = JSON.parse($(this).siblings(".hd").html());
                let uid = supInfo.id;
                $("#fixedScan").data("id",uid);
                $('#stopPurchase').data("mt_sup" , supInfo['material_supplier_id'])
                break;
            case 'stopPurchase2':
                // 全部暂停采购
                editObj = $(this);
                bounce.show($('#stopPurchase2'));
                var mtInfo = $(this).siblings(".hd").html();
                mtInfo = JSON.parse(mtInfo);
                $('#stopPurchase2').data("mtId", mtInfo.id);
                break;
            case 'startPurchase':
                // 恢复采购
                bounce.show($('#startPurchase'));
                var mtInfo = $(this).siblings(".hd").html();
                mtInfo = JSON.parse(mtInfo);
                $('#startPurchase').data("mt_sup", mtInfo['material_supplier_id']);
                break;
            case 'startPurchase2':
                // 全部恢复采购
                editObj = $(this);
                bounce.show($('#startPurchase2'));
                var mtInfo = $(this).siblings(".hd").html();
                mtInfo = JSON.parse(mtInfo);
                $('#startPurchase2').data("mtId", mtInfo.id);
                break;
            case 'concase'://这里不知道是否需要用，可以加上-7.8
                //新增合同
                bounce.show($('#newcontract'));
                break;
            default:
        }

    })
    $("#newContractInfo").on('click','[type="btn"]', function () {
        let fun = $(this).data("fun");
        let tips = '', title = '', list = [];
        let productListTY = $("#newContractInfo").data('productListTY') || []
        let editProductTY = productListTY.filter(item => item.isChecked)
        let source = $("#newContractInfo").data('source');
        let supplierName = ``;
        switch (fun){
            case 'scanTyGs' :
                title = '本合同下的材料'
                list = editProductTY
                break;
            case 'removeTyGs' :
                title = '从本合同移出材料'
                list = editProductTY
                break
            case 'addTyGs' :
                title = '向本合同添加材料'
                list = productListTY
                break;
        }
        let spInfo = ''
        if (source === 'addFixed') {
            spInfo = JSON.parse($("#supplier").val())
        } else {
            let edit_supplier = JSON.parse($("#edit_supplier").val())
            spInfo = edit_supplier.supplier
        }
        supplierName = spInfo.codeName + spInfo.name;
        $("#tipcontractGoods .bonceHead span").html(title);
        $("#tipcontractGoods .tip").html(list.length);
        $("#tipcontractGoods .supplerName").html(supplierName);
        let count = 0
        if (fun === 'addTyGs') {
            count = list.filter(item => item.isChecked).length
        }
        $("#tipcontractGoods .count").html(count);
        $("#tipcontractGoods").data('origin', fun)
        if (fun === 'scanZsGs' || fun === 'scanTyGs') {
            $(".addOrCancel").hide(); $(".cScanc").show();$(".countStr").hide()
            setContactGS(list , false);
        } else if (fun === 'addTyGs'){
            $(".addOrCancel").show(); $(".cScanc").hide();$(".countStr").show()
            setContactGS(list, true, true);
        } else {
            $(".addOrCancel").show(); $(".cScanc").hide();$(".countStr").show()
            setContactGS(list, true);
        }
    })
    $('.ctrlGp').on('click', 'span.btnCat', function(){
        let type = $(this).data('type');
        switch (type){
            case 'showPauseSup':
                // 4页的查看暂停供应商
                var info = {};
                if(editObj){
                    info = JSON.parse(editObj.parent().next().children(".hd").html());
                }else if(editObjFixedMassage){
                    info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
                }else{
                    layer.msg("获取材料失败")
                    return false;
                }
                getSuppliers(info.id, 0, 'mt43');
                $(".pauseSup").show().siblings().hide();
                $("#hidePause4").show();

                break;
            case 'addMtSup':
                // 添加本材料的定点供应商
                $("#isEdit").val("0");
                bounce.show($('#addMtSup'));
                var tbid = $(this).data("tbid");
                $('#tdID').val( tbid) ;
                $(".supInfo").hide();
                $(".stable").hide();
                $("#e_gName1").val("");
                $("#contract").val("");
                $("#e_gCode1").val("");
                $(".manageAddress").hide();
                $(".manageAddress .hd").html("");
                $(".manageAddress .manageAreaList").html("");
                $(".manageAddress .manageAddressList").html("");
                $(".priceInfo .type2").hide();
                $("#addMtSup .bonceCon").children(".item").hide();
                $("#addMtSup .bonceCon .item:first").show();
                var info = {};
                if(editObj){
                    info = JSON.parse(editObj.parent().next().children(".hd").html());
                }else if(editObjFixedMassage){
                    info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
                }else{
                    layer.msg("获取材料失败");
                    return false;
                }
                $("#addMtSup .addMtSup_create").html(info['create_name'] + " " + (new Date(info['create_date']).format('yyyy-MM-dd hh:mm:ss')));
                for(var key in info){
                    $("#addMtSup .addMtSup_"+key).html(info[key]);
                }
                if(info['is_purchased'] == 1){
                    $("#addMtSup .say1").show().siblings().hide();
                }else{
                    $("#addMtSup .say0").show().siblings().hide();
                }
                $("#addMtSup .purUnit").html(info['unit']);
                $("#addMtSup .addMtSup_cat").html(setCatStr(info['category_id']));
                $(".expInfo").hide()
                // 获取供应商列表
                getSupList( $("#supplier"));

                break;
            case 'addNewSup':
                // 向系统添加新供应商
                $("#isEdit").val("0");
                bounce.show($('#addSupply'));
                // 初始化表单控件
                $("#supTip").html("");
                $("#addSupply input[type='text']").val("");
                $("#addSupply input[type='hidden']").val("");
                $("#addSupply .fa").each(function () {
                    $(this).attr("class" , "fa fa-circle-o").removeAttr("disabled") ;
                });
                $("#paymore").hide();
                var hideHang = [4,6,"_6","_7","7_1",8];
                for(var e=0; e<hideHang.length;e++){
                    $(".hang" + hideHang[e]).hide() ;
                }
                $("#e_gPerchaseCycle").val("");
                $("#e_gMinimumPurchase").val("0");
                $("#e_gMinimumStock").val("0");
                $("#e_gInitialStock").val("0");
                $(".isKai").show();
                // 赋值物料信息
                var trObj = $(".mt41 tbody").children("tr:eq(0)");
                /* $("#e_gMtName").val(trObj.children(":eq(0)").html()) ;
                 $("#e_gMtCode").val(trObj.children(":eq(1)").html()) ;
                 $("#e_gMtUnit").val(trObj.children(":eq(5)").html()) ;*/

                $("#sup_type").val("0");
                $("#supConTtl").html("新增供应商");
                break;
            case 'noFixedPurchase':
                if($(this).hasClass("ty-color-gray")){
                    console.log('灰色的')
                }else{
                    bounce.show($('#noFixedPurchase'));
                }
                break;
            case 'editSupMt':
                // 修改定点供应商的信息
                $("#fixedScan .edit").show();$("#fixedScan .scan").hide();
                $("#contants").hide();
                $("#isEdit").val("1");
                $(".canInvoice").show();
                $(".manageAddress").hide();
                var res = JSON.parse($("#edit_supplier").val());
                var supMt = res['mtSupplierMaterial'] ;
                $("#fixedScan").data("mt_sup",supMt['id']);
                var tbid = $(this).data("tbid");
                $('#tdID').val(tbid);
                $(".priceInfo .type2").hide();
                $(".edit .manageAddress .hd").html("");
                $(".edit .manageAddress .manageAreaList").html("");
                $(".edit .manageAddress .manageAddressList").html("");
                if (Number(res.supplier.draftAcceptable) === 1) {
                    $("#edit_acceptBills").next().click();
                } else if (Number(res.supplier.draftAcceptable) === 2){
                    $("#edit_acceptBills").next().next().click();
                }
                getSupplierTaxRate($("#fixedScan .taxRateList"));
                break;
            case 'stopPurchase':
                // 查看定点供应商处 暂停采购
                bounce.show($('#stopPurchase'));
                var res = JSON.parse($("#edit_supplier").val());
                var supMt = res['mtSupplierMaterial'] ;
                $('#stopPurchase').data("mt_sup" , supMt['id']);
                var tbid = $(this).data("tbid")
                $('#tdID').val( tbid) ;
                break;
            case 'startPurchase':
                // 查看定点供应商处 恢复采购
                bounce.show($('#startPurchase'));
                var res = JSON.parse($("#edit_supplier").val());
                var supMt = res['mtSupplierMaterial'] ;
                $('#startPurchase').data("mt_sup" , supMt['id']);
                break;
            case 'startPurchase2':
                // 暂停采购的材料里面的
                bounce.show($('#startPurchase2'));
                var mtInfo = editObj.parent().next().children(".hd").html();
                mtInfo = JSON.parse(mtInfo);
                $('#startPurchase2').data("mtId", mtInfo.id);
                break;
            default:
        }

    })
    // 点击分类获取分类下的物料
    $('#catAll').on('click', 'li', function(){
        let index = $(this).index();
        var id = $(this).data("id") ;
        if(listData["keyword"] != ""){
            $('.curCat .ty-color-blue').remove();
        }
        listData["keyword"] = "";
        getList(id, "", "", 1, 20)
    });
    // 点击横向导航栏
    $('.curCat').on('click', '.go2Cat', function(){
        var cat = $(this).find(".hd").html();
        $(this).nextAll().remove();
        $(this).remove();
        listData['state'] = "" ;
        getList(cat, listData['state'], listData['keyword'], 1 , 20);
    });
    //向本合同添加商品弹窗种的选择框
    $("#addcontract").on('click','.fa',function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    })

    // 获取全部分类/物料
    getList("","","", 1,20);
    setHeight([ $('.mainCon1 .leCat'), $('.mainCon1 .riMt')]);

    // 上传的合同删除   //这里也是不确定需不需要用-7.8
    $("#newContractInfo").on("click",".fa-times",function(){//新增合同   设置在点击图片文件清除按钮后的效果
        let info = JSON.parse($(this).siblings(".hd").html())
        let fileUid = info.fileUid
        cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
        if ($(this).parents(".fileCon1").length > 0 && info.id) {
            let fileItem = $(this).parent(".fileIm").html()
            $("#newContractInfo .deleteFile").append( `<span class="fileItem">${fileItem}</span>`)
        }
        $(this).parent(".fileIm").remove();
    })
    $(".bounce_Fixed").on("click",".ty-btn,.edit,[type='btn']",function(){
        var name=$(this).data('name');
        switch(name){
            //关闭弹窗
            case 'editContractOk':
                editContractOk(1)
                break;
        }
    });
    $("body").on("click",".funBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
    $(".chooseCusCon").click(function () {
        let target = $(this).data("target");
        $("#target").val(target)
        bounce_Fixed3.show( $("#chooseCusContact") );
        getCusContactList();
    });
    $("#chooseCusContact").on("click", '.fa', function () {
        $("#chooseCusContact").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).attr("class", "fa fa-dot-circle-o")
    })
    $(".placeList").on('click','.fa', function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    });
    $(document).bind("click",function(e){//点击空白处，设置的弹框消失
        var tag = $(".taxRateList:visible");
        var tag1 = $("#taxRate:visible");
        var tag2 = $("#edit_taxRate:visible");
        var target = $(e.target);
        if(target.closest(tag).length == 0 && target.closest(tag1).length == 0 && target.closest(tag2).length == 0){
            $(tag).hide();
        }
    });
});

// creator: sy 2022-07-20 从本合同种添加商品弹窗中的确定按钮
function submget(){
    $("#newcontract").css("top","40px");
    let fun = $("#addcontract").data("fun");
    let listEditt = [];
    let chooseGood2 = $("#goodLust").data("chooseGood") || [];
    $("#addcontract .fa-check-square-o").each(function(i) {
        let data = JSON.parse($(this).parent().siblings().children(".controlTd").children(".hd").html());
        listEditt.push(data);
    });
    let newChooseGood = [...listEditt,...chooseGood2];//将原来的和新点击的数据合在一个数组里
    $("#goodLust").data("chooseGood",newChooseGood);
    let newChooseGoodName = newChooseGood.map((item,index) => {
        return item.name;
    });
    let conster = newChooseGood.length;
    $("#contdown").html(conster);
    let goodNameStr = newChooseGoodName.join("、");//在每个字符串之间加上符号
    $("#goodLust").html(goodNameStr);



    $("#newcontract").css("top","-5.5px");
    bounce.cancel($("#addcontract"));
    bounce.show($('#newcontract'));
}
// created:hxz 2020-09/17 首页返回上一级/返回全部
function backPreCat(num) {
    var index = $('.curCat .go2Cat').length - 2; // 返回全部
    if(num === 1){ index = 0;   }
    $('.curCat .go2Cat:eq('+ index +')').click();
}
// crearor:hxz 2020-09/08 查看定点信息
function fixedScanInfo(mtID, supID, relationID,supInfo) {
    $.ajax({
        "url":"../mt/location",
        "data":{ 'id': mtID,  'supplier': supID,  'relationId': relationID  },
        success:function (res) {
            $("#edit_supplier").val(JSON.stringify(res));
            var ben = res.supplier;//
            var supplier = res['mtSupplierMaterial']['supplier'] ;
            var supMt = supplier['supplierMaterial'] ;
            var supInfo = res['mtSupplierMaterial'] ;//材料
            var orders = res['orders'] ; // 未完结的订单数
            var enabled = supInfo['enabled'];
            var wenzi = res['ys'];  //方框中文字信息

            // 判断修改
            $(".supInfo").show();
            $(".stable").hide();
            $(".ordersNum").html(orders);
            $(".supInfo input").val("");
            $(".stable input").val("");
            $("#fixedScan .fa").each(function(){
                $(this).attr("class", "fa fa-circle-o");
                $(this).removeAttr("disabled");
                $(this).parent().removeAttr("disabled");
            })
            chargeShowInvoiceEditSup();
            // 处理查看
            var chargeMtEnabled = $(".stopInfo").data("mtstop");
            if(chargeMtEnabled == "0"){
                $("#fixedScan .stop").hide(); // 物料已经暂停采购， 供应商不能恢复采购
            }else{
                $("#fixedScan .stop").show();
            }
            if(enabled === "1"){
                $("#fixedScan .stop").hide(); $("#fixedScan .start").show();
                $("#ceateInfo").html("本材料由 " + supInfo['createName'] + "于" + (new Date(supInfo['createDate']).format("yyyy-MM-dd hh:mm:ss")) + "确定可定点采购于以下的供应商。");
            }else{ // 暂停采购的
                $("#ceateInfo").html("本材料由" + supInfo['updateName'] + "于" + (new Date(supInfo['enabledTime']).format("yyyy-MM-dd hh:mm:ss")) + " 暂停采购于以下的供应商。");
                $("#fixedScan .stop").show(); $("#fixedScan .start").hide();
            }
            bounce.show($('#fixedScan'));
            var info = {};
            if(editObj){
                info = JSON.parse(editObj.parent().next().children(".hd").html());
            }else if(editObjFixedMassage){
                info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
            }else{
                layer.msg("获取材料失败")
                return false;
            }
            let messge = $("#fixedScan").data("mess");
            messge = JSON.parse(messge);
            $("#s_name").html(info['name']);
            $("#supplier2").html(messge['full_name']);
            $("#s_code").html(info['code']);
            $("#supplier3").html(messge['name']);
            $("#s_unit").html(info['unit']);
            $("#g_code").html(messge['code_name']);
            $("#s_specifications").html(info['specifications']);
            $("#s_model").html(info['model']);
            $("#s_memo").html(info['memo']);
            $("#s_create").html(info['create_name'] + " " + (new Date(info['create_date']).format('yyyy-MM-dd hh:mm:ss')));
            $("#s_cat").html(setCatStr(info['category_id']));

            let mode = $("body").data('whmode')
            let data = info
            if (mode === 1) {
                let expStr = ``
                if(data.exp_required === 1){
                    expStr = `有保质期方面的要求，`
                    if(data.open_duration){
                        expStr += `开瓶(开封)后可使用${ data.open_duration }日，`
                    }
                    // 相关的数据:1-截止日期,2-生产日期
                    if(data.related_item === 1){
                        expStr += `入库时,需录入可使用的截至日期。`
                    }else if(data.related_item === 2){
                        expStr += `入库时,需录入生产日期，`
                        // 保质期是否相同 0-不相同 1-相同
                        if(data.same_expiration === 1){
                            expStr += `不同供应商的供应该材料均为自生产日期之后${ data.expiration_days }日`
                        }else{
                            expStr += `不同供应商的供应该材料的保质期不同`
                        }
                    }

                }else{
                    expStr = `无保质期方面的要求`
                }
                $("#fixedScan .scanExpShow").show()
                $("#fixedScan .scanMt_expStr").html(expStr)
            } else {
                $("#fixedScan .scanExpShow").hide()
            }


            if(info['is_purchased'] == '1'){
                $("#fixedScan .say0").html("本材料录入时为“曾采购过的材料”。")
            }else{
                $("#fixedScan .say0").html("本材料录入时为“未采购过的材料”。")
            }
            setSupInfo(res, $("#supInfo2"));
            var priceInfo = chargePriceShow(supInfo);
            $("#priceTtl").html(priceInfo.ttl);
            $("#priceCon").html(priceInfo.info);
            // $("#supInfo2").html(setSupOnbe(ben));
            $("#supInfo2").html(setSupOnbe(wenzi));
            // $("#mtTip_s").html(setSupMtInfo(ben, supInfo));//ben是供应商的，supInfo是材料的
            $("#mtTip_s").html(setSupMtInfo(wenzi));
            $("#fixedScan .purUnit").html(info['unit']);

        }
    })
}

// creator: sy 2022-09-11 查看定点供应商数据
function setSupOnbe(link,obj){
    var stren = "";
    console.log(link);//这是文字数据，可直接调用

    var stren = '';
    stren += `
        <span class="supGreen">${link.kp ? link.kp : ''}</span>
        <span class="supOrange">${link.kp !== "" && link.gz !== "" ? ',' : ''}${link.gz ? link.gz : ''}</span>
        <span class="supBlue">${link.hp !== "" && link.gz !== "" ? ',' : ''}${link.hp || ''}</span>
    `;
    return stren;
}

// crearor:hxz 2020-04-24 无需定点采购
function noFixedPurchase() {
    var info = {};
    if(editObj){
        info = JSON.parse(editObj.parent().next().children(".hd").html());
    }else if(editObjFixedMassage){
        info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
    }else{
        layer.msg("获取材料失败")
        return false;
    }
    var data = {
        userId:sphdSocket.user.userID,
        id:info.id,
        is_appointed:9
    }
    $.ajax({
        "url":"../mt/operation",
        "data":data ,
        success:function(res){
            bounce.cancel();
            if(res['code'] == 1){
                layer.msg("操作成功");
                goConfirm(2);
            }else{
                layer.msg("操作失败")
            }
        }
    })
}
// crearor:hxz 2020-04-24 返回上一页
function hidePause4() {
    $(".pauseSup").hide().siblings().show();
    $("#hidePause4").hide();
    var info = editObj.parent().next().children(".hd").html()
    info = JSON.parse(info)
    $(".mt42 tbody").html("");
    getSuppliers(info.id, 1, 'mt42');
}
// crearor:hxz 2019-12-25 保存编辑的供应商
function saveNewSupply(){
    var name = $("#e_gName").val();   // string    供应商名称
    var name2 = $("#e_gName2").val();   // string    供应商名称
    var gCode = $("#e_gCode").val() ; // 供应商代号
    gCode = $.trim(gCode) ;   name2 = $.trim(name2) ;     name = $.trim(name) ;
    // var hasContact = $("#haveContract").val() ;  //  boolean  '是否已与其签订采购合同;true-有,false-无',
    var contractSn = $("#e_gCompactNo").val() ;  // varchar(100)    '采购合同编号',
    var validDate = $("#e_gCompactExpire").val() ; //  date '有效期至',(yyyy-MM-dd)
    var signDate = $("#e_gCompactSignDay").val(); // date '签署日期',(yyyy-MM-dd)
    var hasContact = $("#contread").val();// boolean '此材料是否包含于与供应商已签订的合同中',
    //var invoicable = $("#haveInvoice").val()  ; // boolean  '该供应商是否能开发票::true-可开具,false-不能开',
    //var invoiceCategory = $("#vatInvoice").val()  ; // boolean  '是否能开增值税专用发票1:: 普通发票2
    //var taxRate = $("#e_gRate0").val() ; // 该合同开发票的税率
    //taxRate = $.trim(taxRate) ;
    var chargeAcceptable = $("#setHangAccount").val() ; // 是否接受挂账
    var chargePeriod = $("#hangDays").val() ; // 账期
    var chargeBegin = $("#setStartDate").val() ; // 1 - 自入库之日起 0 - 自发票提交之日起
    var isImprest = $("#setAdvanceFee").val() ; //  是否需要预付款 :0-不确定,1-需要,2-不需要
    //var draftAcceptable = $("#hui").val() ; // 汇票 1接受 0不接受 2不确定
    var imprestProportion = $("#e_gRate1").val();//需预付的比例
    var proportion = $("#uncertainty").val();//比例不确定
    if(name === ""){
        // layer.msg("供应商名称不能为空！");
        bounce.show($("#tip1"));$("#contracto").hide();$("#mustchoose").show();return false ;
    }
    if(name2 === ""){
        // layer.msg("供应商简称不能为空！");
        bounce.show($("#tip1"));$("#contracto").hide();$("#mustchoose").show();return false ;
    }
    if(gCode === ""){
        // layer.msg("供应商代号不能为空！");
        bounce.show($("#tip1")); $("#contracto").hide();$("#mustchoose").show();return false ;
    }
    // if(hasContact === ""){ layer.msg("请选择“是否已与其签订采购合同”项目！");bounce.show($("#tip1")); return false ;  }
    // if(hasContact === "1"){
    //     if(validDate === ""){  layer.msg("合同有效期至不能为空！");bounce.show($("#tip1")); return false ;     }
    //     if(signDate === ""){  layer.msg("合同签署日期不能为空！");bounce.show($("#tip1")); return false ;     }
    //     if(contractSn === ""){  layer.msg("合同编号不能为空！");bounce.show($("#tip1")); return false ;     }
    // }
    // if(invoicable === ""){
    //     // layer.msg("请选择“该供应商是否能开发票”项目！");
    //     bounce.show($("#tip1")); $("#contracto").hide();$("#mustchoose").show();return false ;
    // }
    // if(invoicable === "1"){
    //     if(invoiceCategory === ""){
    //         // layer.msg("请选择“是否能开增值税专用发票”项目！");
    //         // bounce.show($("#tip1"));
    //         $("#contracto").hide();$("#mustchoose").show();return false ;
    //     }
    //     if(invoiceCategory === "1"){
    //         if(taxRate === ""){
    //             // layer.msg("税率不能为空！");
    //             bounce.show($("#tip1")); $("#contracto").hide();
    //             $("#mustchoose").show();return false ;
    //         }
    //         if(draftAcceptable === ""){
    //             // layer.msg("请选择“是否可接受汇票”项目！");
    //             bounce.show($("#tip1")); $("#contracto").hide();
    //             $("#mustchoose").show();return false ;
    //         }
    //     }
    // }
    if(chargeAcceptable === ""){
        // layer.msg("请选择“是否接受挂账”项目！");
        bounce.show($("#tip1")); $("#contracto").hide();$("#mustchoose").show();return false ;
    }
    if(chargeAcceptable === "1") {
        if (chargePeriod === "") {
            // layer.msg("请录入已约定的账期！");
            bounce.show($("#tip1"));
            $("#contracto").hide();
            $("#mustchoose").show();
            return false;
        }
        if (chargeBegin === "") {
            // layer.msg("请选择“请选择从何时开始计算账期”项目！");
            bounce.show($("#tip1"));
            $("#contracto").hide();
            $("#mustchoose").show();
            return false;
        }
    }
    if(isImprest === ""){
        // layer.msg("请选择“是否需要预付款”项目！");
        bounce.show($("#tip1"));$("#contracto").hide();$("#mustchoose").show(); return false ;
    }
    if(isImprest === "1"){
        if(proportion === "1"){
            imprestProportion = "-1";
        }
        if(imprestProportion === ""){
            // layer.msg("请录入需预付的比例");
            bounce.show($("#tip1"));
            $("#contracto").hide();
            $("#mustchoose").show();
            return false;
        }
    }
    if(imprestProportion == "0"){   //预付款比例
        imprestProportion = "0.01";
    }else if(imprestProportion == "100"){
        imprestProportion = "99.99";
    }

    var info = {};
    if(editObj){
        info = JSON.parse(editObj.parent().next().children(".hd").html());
    }else if(editObjFixedMassage){
        info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
    }else{
        layer.msg("获取材料失败")
        return false;
    }


    var supItem = {
        "org": sphdSocket.user.oid,
        // "mtId": info.id ,
        "supplierId": "" , // 供应商id
        "fullName": name , // 供应全称
        "name": name2 , // 供应名称
        "codeName": gCode , // 代号
        // "hasContact": hasContact ,
        "contractSn": contractSn ,
        "validDate": validDate ,
        "signDate": signDate ,
        //"invoicable": invoicable ,
        //"invoiceCategory": invoiceCategory ,
        //"draftAcceptable": draftAcceptable ,
        //"taxRate": taxRate ,
        "chargeAcceptable": chargeAcceptable ,
        "chargePeriod": chargePeriod ,
        "chargeBegin": chargeBegin ,
        "isImprest": isImprest,
        "imprestProportion":imprestProportion   //预付款比例
    };

    $.ajax({
        "url":"../material/locationSuppliers",
        "data": supItem ,
        success:function(res){
            bounce.cancel();
            if(res == 1){
                layer.msg("操作成功");
            }else  if(res == 2){
                layer.msg("该供应商已经添加过了，无需再次添加");
            }else  if(res == 3){
                layer.msg("有未完成的添加审核，不能操作");
            }else{
                layer.msg("操作失败");
            }
        }
    })
}
// creator: hxz 2020-04-23  所属种类序列
function setCatStr(category_id) {
    var curCatObj = $("#catTree").find(".treeItem" + category_id);
    var pCatsArr = curCatObj.parents("[class*='treeItem']");
    var catsArr = [];
    pCatsArr.each(function(){
        var kls = $(this).attr("class");
        var catId = kls.substr(8, kls.length);
        catsArr.push(catId);
    })
    catsArr.reverse().push(category_id);
    var str = "" ;
    for(var i = 0 ; i < catsArr.length ; i++){
        for(var j = 0 ; j < mtCategories.length ; j++){
            if(mtCategories[j]['id'] == catsArr[i]){
                str += mtCategories[j]['name'] + "> "
            }
        }
    }
    return str ;
}
// creater: hxz 2020-04-22 添加定点供应商确定
function addMtSupOk(){
    // var editType = $("#editType").val() ;
    var sup = $("#supplier").val() ;
    if(!sup){
        layer.msg("请先选择供应商！");
        return false ;
    }
    sup = JSON.parse(sup) ;
    var supplier = sup ;
    var supID = supplier['id'];
    var supplierMaterial = supplier['supplierMaterial'];
    for(var key in supplierMaterial){
        supplier[key] = supplierMaterial[key]
    }
    // 供应商唯一判断
    var fullName = supplier["fullName"], same = false;
    var tbid = $('#tdID').val( ) ;
    $(".mt"+ tbid +" tbody").children("tr").each(function () {
        var itemSup = $(this).children(":last").children(".hd").html();
        itemSup = JSON.parse(itemSup) ;
        if(itemSup["full_name"] == fullName){
            same = true ;
            return false
        }
    })

    if(same){
        layer.msg('该供应商已存在')
        return false;
    }
    // supplier["isInclude"] = $("#containThis").val(); // 合同是否包含本物料:1-包含,0—不包含
    // if(supplier["hasContact"] === "1"){
    //     if(supplier["isInclude"] !== "1" && supplier["isInclude"] !== "0"){
    //         layer.msg("请选择采购合同是否包含本材料");
    //         return false;
    //     }
    // }

    supplier["hasContact"]=$("#ontractsigned").val();//是否包含供应商已签订的合同中：1-是，0-否
    if(supplier["hasContact"] !=="1" && supplier["hasContact"] !== "0"){
        layer.msg("请选中此材料是否包含于与供应商已签订的合同中");
        return false;
    }else if(supplier["hasContact"] === "1"){
        var contact = $("#contract").val();// 本材料在哪个合同中
        contact = JSON.parse(contact);
        supplier["contractSn"] = contact.sn;
    }

    supplier["priceStable"] = $("#isStable").val();  //价格是否稳定:1-相对稳定,2-变动频繁
    if(supplier["priceStable"] !== "1" && supplier["priceStable"] !== "2"){
        layer.msg("请选择该供应商供应的本材料价格是否稳定");
        return false;
    }
    supplier["isParValue"] = $("#isParValue").val();  //是否为开票价格 1-是,0-否
    supplier["inclusiveFreight"] = $("#containYunFee").val();  //是否包含运费 是否包含运费:1-为送货上门价格,含所有运费/2-含长途运输费用,到本市后需我司自提/3-为离厂价格,不包含任何运费
    if(supplier["inclusiveFreight"] !== "1" && supplier["inclusiveFreight"] !== "2" && supplier["inclusiveFreight"] !== "3"){
        layer.msg("请选择该价格是否含运费");
        return false;
    } else {
        if(supplier["inclusiveFreight"] === "1" && $("#addMtSup .manageAddressList").html() === ""){
            layer.msg("还未选择收货地址");
            return false;
        }
        if(supplier["inclusiveFreight"] === "2" && $("#addMtSup .manageAreaList").html() === ""){
            layer.msg("还未选择到货区域");
            return false;
        }
        if(supplier["inclusiveFreight"] === "1"){
            supplier["deliveryAddress"] = $("#addMtSup .manageAddressList").siblings(".hd").html();  //是否包含运费 是否包含运费:1-为送货上门价格,含所有运费/2-含长途运输费用,到本市后需我司自提/3-为离厂价格,不包含任何运费
        }
        if(supplier["inclusiveFreight"] === "2"){
            supplier["deliveryAddress"] = $("#addMtSup .manageAreaList").siblings(".hd").html();  //是否包含运费 是否包含运费:1-为送货上门价格,含所有运费/2-含长途运输费用,到本市后需我司自提/3-为离厂价格,不包含任何运费
        }
    }
    supplier["atPar"] = $("#price").val();  //已约定单价(参考价格)
    supplier["invoicable"] = $("#canInvoice").val(); //本物料能否开发票  1可以 0不可以 2不确定
    supplier["materialInvoicable"] = $("#canInvoice").val(); //本物料能否开发票  1可以 0不可以 2不确定
    supplier["materialInvoiceCategory"] = $("#incoiceType").val();  //本物料发票类型:1-增值税专票,2-普通发票 4-不给开票
    supplier["materialTaxRate"] =  "";  //本物料税率  普通票不传，开增值传前面设置的税率
    supplier["isTax"] =  $("#referPrice").val();  // 是否含税
    supplier["materialTaxRate"] = $("#taxRate").val().replace(/\%/, "");
    supplier["supplierInvoice"] = $("#taxRateId").val();
    if(supplier["materialInvoicable"] === "1"){
        if(supplier["materialInvoicable"] !== "1" && supplier["materialInvoicable"] !== "2" && supplier["materialInvoicable"] !== "0"){
            layer.msg("请选择购买本材料是否能开发票");
            return false;
        }
        if(supplier["materialInvoicable"] === "1"){
            if(supplier["materialInvoiceCategory"] !== "1" && supplier["materialInvoiceCategory"] !== "2" && supplier["materialInvoiceCategory"] !== "4"){
                layer.msg("请选择购买本材料给开何种发票");
                return false;
            }
            if(supplier["materialInvoiceCategory"] === "1"){
                if(supplier["isTax"] !== "1" && supplier["isTax"] !== "0" ){
                    layer.msg("请选择是否含税");
                    return false;
                }
                if(supplier["supplierInvoice"] === "" ){
                    layer.msg("请选择税率");
                    return false;
                }
            }
        }
    }
    supplier["unitPrice"] =  $("#price").val();
    if(supplier["unitPrice"] === ""){
        layer.msg("请输入单价");
        return false;
    }
    supplier["packageMethod"] =  $("#packgeType").val(); //包装方式:1-基本固定,2-型式不定
    if(supplier["packageMethod"] !== "1" && supplier["packageMethod"] !== "2" ){
        layer.msg("请选择所供应材料的包装方式");
        return false;
    }
    let infoMt = ($(".mt31 tbody tr").children(":last").find(".hd").html()) ;
    infoMt = JSON.parse(infoMt) ;

    if(infoMt.exp_required == 1 && infoMt.related_item ==2 && infoMt.same_expiration == 0){
        supplier["expirationDays"] =  $("#expDate").val();  //保质期
        if(supplier["expirationDays"] === ""){
            layer.msg("请输入保质期");
            return false;
        }
    }


    supplier["perchaseCycle"] =  $("#purTurn").val();  //采购周期
    if(supplier["perchaseCycle"] === ""){
        layer.msg("请输入采购周期");
        return false;
    }
    supplier["minimumPurchase"] =  $("#minPur").val();  //最低采购量
    if(supplier["minimumPurchase"] === ""){
        layer.msg("请输入最低采购量");
        return false;
    }
    supplier["minimumStock"] =  $("#minStorage").val(); //最低库存
    if(supplier["minimumStock"] === ""){
        layer.msg("请输入最低库存");
        return false;
    }
    if ($("#addMtSup .acceptBills").is(":visible")) {
        supplier["draftAcceptable"] =  $("#acceptBills").val(); //供应商是否可接受汇票
        if(supplier["draftAcceptable"] === ""){
            layer.msg("请选择供应商是否可接受汇票");
            return false;
        }
    }

    var info = {};
    if(editObj){
        info = JSON.parse(editObj.parent().next().children(".hd").html());
    }else if(editObjFixedMassage){
        info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
    }else{
        layer.msg("获取材料失败")
        return false;
    }
    var supItem = {
        "org": sphdSocket.user.oid,
        "mtId": info.id ,
        "operation": 1 ,
        "supplierId": supID , // 供应商id
        "priceStable": supplier["priceStable"] ,
        "inclusiveFreight": supplier["inclusiveFreight"] ,
        "packageMethod": supplier["packageMethod"] ,
        "perchaseCycle": supplier["perchaseCycle"] ,
        "minimumPurchase": supplier["minimumPurchase"] ,
        "minimumStock": supplier["minimumStock"] ,
        "invoicable":supplier["invoicable"],
        "materialInvoicable": supplier["materialInvoicable"] ,
        "materialInvoiceCategory": supplier["materialInvoiceCategory"] ,
        "taxRate": supplier["materialTaxRate"] ,
        "supplierInvoice": supplier["supplierInvoice"] ,
        "isTax": supplier["isTax"] ,
        "isInclude": supplier["isInclude"],
        "hasContact":supplier["hasContact"],
        "contractSn":supplier["contractSn"],
        "expirationDays":supplier["expirationDays"],
        "deliveryAddress":supplier["deliveryAddress"]
    };
    if(supplier["isTax"] === "1"){
        supItem["unitPrice"] =  supplier["unitPrice"];
    }else{
        supItem["unitPriceNotax"] =  supplier["unitPrice"];
    }
    if ($("#addMtSup .acceptBills").is(":visible")) {
        supItem["draftAcceptable"] =  supplier["draftAcceptable"];
    }
    $.ajax({
        url:"../material/locationSuppliers",
        data: supItem ,
        success:function(res){
            if(res == 1){
                layer.msg("操作成功");
            }else if(res == 2){
                layer.msg("该供应商已经存在，无需再次添加");
            }else  if(res == 3) {
                layer.msg("有未完成的添加审核，不能操作");
            } else {
                layer.msg("操作失败");
            }
            var tbid = $('#tdID').val( ) ;
            getSuppliers(info.id, 1, 'mt'+ tbid);
            bounce.cancel();

        }
    })
}
// creater: hxz 2020-04-22 修改定点供应商确定
function editMtSupOk(){
    var res = JSON.parse($("#edit_supplier").val());
    var supplier = res['supplier'] ; // 供应商
    var supInfo = res['mtSupplierMaterial'] ;

    var supID = supplier['id'];
    var supplierMaterial = supplier['supplierMaterial'];
    for(var key in supplierMaterial){
        supplier[key] = supplierMaterial[key]
    }
    // supplier["isInclude"] = $("#edit_containThis").val(); // 合同是否包含本物料:1-包含,0—不包含
    // if(supplier["hasContact"] === "1"){
    //     if(supplier["isInclude"] !== "1" && supplier["isInclude"] !== "0"){
    //         layer.msg("请选择采购合同是否包含本材料");
    //         return false;
    //     }
    // }
    // supplier["isInclude"]=$("#ontractsigned").val();//是否包含供应商已签订的合同中：1-是，0-否
    supplier["hasContact"]=$("#edit_urgnistad").val();//是否包含供应商已签订的合同中：1-是，0-否
    if(supplier["hasContact"] !=="1" && supplier["hasContact"] !== "0"){
        layer.msg("请选中此材料是否包含于与供应商已签订的合同中");
        return false;
    }else if(supplier["hasContact"] === "1"){
        var contact = $("#contract1").val();// 本材料在哪个合同中
        contact = JSON.parse(contact);
        supplier["contractSn"] = contact.sn;
    }
    supplier["priceStable"] = $("#edit_isStable").val();  //价格是否稳定:1-相对稳定,2-变动频繁
    if(supplier["priceStable"] !== "1" && supplier["priceStable"] !== "2"){
        layer.msg("请选择该供应商供应的本材料价格是否稳定");
        return false;
    }
    supplier["isParValue"] = $("#edit_isParValue").val();  //是否为开票价格 1-是,0-否
    supplier["inclusiveFreight"] = $("#edit_containYunFee").val();  //是否包含运费 是否包含运费:1-为送货上门价格,含所有运费/2-含长途运输费用,到本市后需我司自提/3-为离厂价格,不包含任何运费
    if(supplier["inclusiveFreight"] !== "1" && supplier["inclusiveFreight"] !== "2" && supplier["inclusiveFreight"] !== "3"){
        layer.msg("请选择该价格是否含运费");
        return false;
    } else {
        if(supplier["inclusiveFreight"] === "1" && $("#fixedScan .manageAddressList").html() === ""){
            layer.msg("请选择收货地址");
            return false;
        }
        if(supplier["inclusiveFreight"] === "2" && $("#fixedScan .manageAreaList").html() === ""){
            layer.msg("请选择到货区域");
            return false;
        }
        if(supplier["inclusiveFreight"] === "1"){
            supplier["deliveryAddress"] = $("#fixedScan .manageAddressList").siblings(".hd").html();  //是否包含运费 是否包含运费:1-为送货上门价格,含所有运费/2-含长途运输费用,到本市后需我司自提/3-为离厂价格,不包含任何运费
        }
        if(supplier["inclusiveFreight"] === "2"){
            supplier["deliveryAddress"] = $("#fixedScan .manageAreaList").siblings(".hd").html();  //是否包含运费 是否包含运费:1-为送货上门价格,含所有运费/2-含长途运输费用,到本市后需我司自提/3-为离厂价格,不包含任何运费
        }
    }
    supplier["atPar"] = $("#edit_price").val();  //已约定单价(参考价格)
    supplier["materialInvoicable"] = $("#edit_canInvoice").val(); //本物料能否开发票  1可以 0不可以 2不确定
    supplier["materialInvoiceCategory"] = $("#edit_incoiceType").val();  //本物料发票类型:1-增值税专票,2-普通发票 4-不给开票
    supplier["materialTaxRate"] =  "";  //本物料税率  普通票不传，开增值传前面设置的税率
    supplier["isTax"] =  $("#edit_referPrice").val();  // 是否含税
    supplier["materialTaxRate"] = $("#edit_taxRate").val().replace(/\%/, "");
    supplier["supplierInvoice"] = $("#edit_taxRateId").val();

    if(supplier["materialInvoicable"] === "1"){
        if(supplier["materialInvoicable"] !== "1" && supplier["materialInvoicable"] !== "2" && supplier["materialInvoicable"] !== "0"){
            layer.msg("请选择购买本材料是否能开发票");
            return false;
        }
        if(supplier["materialInvoicable"] === "1"){
            if(supplier["materialInvoiceCategory"] !== "1" && supplier["materialInvoiceCategory"] !== "2" && supplier["materialInvoiceCategory"] !== "4"){
                layer.msg("请选择购买本材料给开何种发票");
                return false;
            }
            if(supplier["materialInvoiceCategory"] === "1"){
                if(supplier["isTax"] !== "1" && supplier["isTax"] !== "0" ){
                    layer.msg("请选择是否含税");
                    return false;
                }
                if(supplier["supplierInvoice"] === "" ){
                    layer.msg("请选择税率");
                    return false;
                }
            }
        }
    }
    supplier["unitPrice"] =  $("#edit_price").val();
    if(supplier["unitPrice"] === ""){
        layer.msg("请输入单价");
        return false;
    }
    supplier["packageMethod"] =  $("#edit_packgeType").val(); //包装方式:1-基本固定,2-型式不定
    if(supplier["packageMethod"] !== "1" && supplier["packageMethod"] !== "2" ){
        layer.msg("请选择所供应材料的包装方式");
        return false;
    }
    supplier["perchaseCycle"] =  $("#edit_purTurn").val();  //采购周期
    if(supplier["perchaseCycle"] === ""){
        layer.msg("请输入采购周期");
        return false;
    }
    supplier["minimumPurchase"] =  $("#edit_minPur").val();  //最低采购量
    if(supplier["minimumPurchase"] === ""){
        layer.msg("请输入最低采购量");
        return false;
    }
    supplier["minimumStock"] =  $("#edit_minStorage").val(); //最低库存
    if(supplier["minimumStock"] === ""){
        layer.msg("请输入最低库存");
        return false;
    }
    if ($("#fixedScan .edit_acceptBills").is(":visible")) {
        supplier["draftAcceptable"] =  $("#edit_acceptBills").val(); //供应商是否可接受汇票
        if(supplier["draftAcceptable"] === ""){
            layer.msg("请选择供应商是否可接受汇票");
            return false;
        }
    }
    var info = {};
    if(editObj){
        info = JSON.parse(editObj.parent().next().children(".hd").html());
    }else if(editObjFixedMassage){
        info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
    }else{
        layer.msg("获取材料失败")
        return false;
    }
    var supItem = {
        "org": sphdSocket.user.oid,
        "mtId": info.id ,
        "supplierId": supID , // 供应商id
        "priceStable": supplier["priceStable"] ,
        "inclusiveFreight": supplier["inclusiveFreight"] ,
        "packageMethod": supplier["packageMethod"] ,
        "perchaseCycle": supplier["perchaseCycle"] ,
        "minimumPurchase": supplier["minimumPurchase"] ,
        "minimumStock": supplier["minimumStock"] ,
        "materialInvoicable": supplier["materialInvoicable"] ,
        "materialInvoiceCategory": supplier["materialInvoiceCategory"] ,
        "taxRate": supplier["materialTaxRate"] ,
        "supplierInvoice": supplier["supplierInvoice"] ,
        "supplierMaterialId": supInfo["id"] ,
        "isTax": supplier["isTax"] ,
        "isInclude": supplier["isInclude"],
        "hasContact":supplier["hasContact"],
        "contractSn":supplier["contractSn"],
        "deliveryAddress":supplier["deliveryAddress"]
    };
    if(supplier["isTax"] === "1"){
        supItem["unitPrice"] =  supplier["unitPrice"];
    }else{
        supItem["unitPriceNotax"] =  supplier["unitPrice"];
    }
    if ($("#fixedScan .edit_acceptBills").is(":visible")) {
        supItem["draftAcceptable"] =  supplier["draftAcceptable"];
    }
    $.ajax({
        "url":"/material/locationSuppliers",
        "data": supItem ,
        success:function(res){
            if(res == 1){
                layer.msg("操作成功");

            }else{
                layer.msg("操作失败");
            }
            var tbid = $('#tdID').val( ) ;  //现在它没有值
            getSuppliers(info.id, 1, 'mt'+ tbid);
            bounce.cancel();

        }
    })
}
// created:hxz 2020-03-11 物料查看
function mtScan() {
    var info = {};
    if(editObj){
        info = JSON.parse(editObj.parent().next().children(".hd").html());
    }else if(editObjFixedMassage){
        info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
    }else{
        layer.msg("获取材料失败")
        return false;
    }
    var data = { 'operation':10 , 'userId':sphdSocket.user.userID , 'id':info['id'] };
    $.ajax({
        "url":"../mt/operation",
        "data":data ,
        success:function(res){
            var data = res['data'][0] ;
            for(var key in data){
                if(key == 'create_name' || key == 'create_date' ){

                }else{
                    $(".scanMt_" + key).html(data[key]);
                }
            }
            var str = "本材料录入时为“曾采购过的材料”。"
            switch (Number(data.is_purchased)){
                case 1 :
                    str = "本材料录入时为“曾采购过的材料”。" ; break;
                case 0 :
                    str = "本材料录入时为“未采购过的材料”。" ; break;

            }
            $(".scanMt_state").html(str)

            $(".scanMt_cat").html(res['path']);
            $(".scanMt_create").html(data['create_name'] + " " + (new Date(data['create_date']).format('yyyy-MM-dd hh:mm:ss')));
            let mode = $("body").data('whmode')
            if (mode === 1) {
                let expStr = ``
                if(data.exp_required === 1){
                    expStr = `有保质期方面的要求，`
                    if(data.open_duration){
                        expStr += `开瓶(开封)后可使用${ data.open_duration }日，`
                    }
                    // 相关的数据:1-截止日期,2-生产日期
                    if(data.related_item === 1){
                        expStr += `入库时,需录入可使用的截至日期。`
                    }else if(data.related_item === 2){
                        expStr += `入库时,需录入生产日期，`
                        // 保质期是否相同 0-不相同 1-相同
                        if(data.same_expiration === 1){
                            expStr += `不同供应商的供应该材料均为自生产日期之后${ data.expiration_days }日`
                        }else{
                            expStr += `不同供应商的供应该材料的保质期不同`
                        }
                    }

                }else{
                    expStr = `无保质期方面的要求`
                }
                $("#mtScan .scanExpShow").show()
                $("#mtScan .scanMt_expStr").html(expStr)
            } else {
                $("#mtScan .scanExpShow").hide()
            }

            bounce.show($("#mtScan"));
        }
    })
}
// creater: hxz 2020-04-21 暂停/恢复采购
function editPurchase(enabled, isAll) {
    var data = {
        userId : sphdSocket.user.userID,
        operation : 4,
        enabled : enabled
    };
    if(enabled === 0){ // 暂停
        if(isAll === 1){
            data['id'] = $('#stopPurchase2').data("mtId");
        }else{
            var info = {};
            if(editObj){
                info = JSON.parse(editObj.parent().next().children(".hd").html());
            }else if(editObjFixedMassage){
                info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
            }else{
                layer.msg("获取材料失败")
                return false;
            }
            data['product_'] = $('#stopPurchase').data("mt_sup") ;
            data['id'] = info['id'] ;
        }
    }else{ // 恢复
        if(isAll === 1){
            data['id'] = $('#startPurchase2').data("mtId");
        }else{
            var info = {};
            if(editObj){
                info = JSON.parse(editObj.parent().next().children(".hd").html());
            }else if(editObjFixedMassage){
                info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
            }else{
                layer.msg("获取材料失败")
                return false;
            }
            data['product_'] = $('#startPurchase').data("mt_sup") ;
            data['id'] = info['id'] ;
        }
    }

    // material_supplier_id  返回这个。传给operation的键是 product_

    $.ajax({
        "url":"../mt/operation",
        "data": {
            userId:data.userId,
            operation:data.operation,
            enabled:data.enabled,
            product_:data.product_,
            id:data.id
        },
        success:function(res){
            var code = res["code"]
            bounce.cancel();
            if(code == 1){
                layer.msg("操作成功");
                if(isAll === 1){
                    if(enabled === 0){
                        $(".curCat").children(":last").click();
                    }else{
                        goPause();
                    }
                }else{
                    if(enabled === 0){
                        if($(".mainCon4").is(":visible")){
                            getSuppliers(info.id, 1, 'mt42');
                        }else{
                            getSuppliers(info.id, 1, 'mt32');
                        }
                    }else{
                        $(".showPauseSup").click();
                    }
                }
            }else{
                layer.msg(res["message"]);
            }
        }
    })
}
// creater: hxz 2020-04-21 获取某物料的供应商列表
function getSuppliers(mtId, enabled, tblID) {
    var data = {
        'id': mtId,
        'enabled':enabled
    }
    $.ajax({
        "url":"../mt/suppliers",
        "data":data,
        success:function (res) {
            var is_appointed = res['is_appointed']; // 9 表示无需定点采购
            var chargeMtEnabled = res['enabled']; // 0-已暂停采购
            var list = res['data'] || [];
            var str = "";
            var ctrlStr =  "<span class=\"ty-color-blue\" data-type=\"stopPurchase\">暂停采购</span>" ;
            if(enabled == 0){ // 供应商暂停采购的
                ctrlStr =   "<span class=\"ty-color-blue\" data-type=\"startPurchase\">恢复采购</span>"
                $(".pauseSup").show()
                $(".pauseSup1").html(list.length);
            }
            $(".stopInfo").data("mtstop", chargeMtEnabled);
            if(chargeMtEnabled == "0"){
                $(".stopInfo").parent().show();
                $(".addMtSupBtn").hide();
                $(".addNewSupBtn").hide();
                $(".stopInfo").html( res['update_name'] +"已于"+ (new Date(res['enabled_time']).format("yyyy-MM-dd hh:mm:ss")) +"将本材料<span class='ty-color-orange'>“暂停采购”！</span>。");
                ctrlStr = "" ;
            }else{
                $(".stopInfo").parent().hide();
                $(".addMtSupBtn").show();
                $(".addNewSupBtn").show();
            }
            if(is_appointed === '9' || is_appointed === '0'){
                $(".noFixedPurchaseBtn").attr("class","btnCat noFixedPurchaseBtn ty-color-gray");

                $(".noPositionCreat").html("本材料由"+ res['appointed_user'] +"于"+ (new Date(res['appointed_time']).format("yyyy-MM-dd hh:mm:ss")) +"确定为无需定点采购。");

            }else{
                $(".noFixedPurchaseBtn").attr("class","btnCat noFixedPurchaseBtn");

                $(".noPositionCreat").html("本材料现有如下"+ list.length +"个定点供应商");
            }
            $(".showPauseSup").html(res['enabledCount'] + " 个"); // 暂停供应商
            $(".pauseSup1").html(list.length ); // 暂停供应商
            if(list && list.length>0){
                for(var i = 0 ; i <list.length ; i++){
                    var info = list[i];
                    var price = info['unit_price_notax'] ;
                    if(info['is_tax'] == "1"){
                        price = info['unit_price'] ;
                    }
                    let mtInvoiceAble = "不确定";
                    let material_invoiceable =  Number(info['material_invoiceable'] );
                    if(material_invoiceable === 0){
                        mtInvoiceAble = "否";
                    }else if(material_invoiceable === 1){
                        mtInvoiceAble = "是";
                    }else if(material_invoiceable === 2){
                        mtInvoiceAble = "不确定";
                    }
                    str += "<tr>" +
                        "    <td>"+ info['name'] +"</td>" +
                        "    <td>"+ (info['contract_sn'] || "无") +"</td>" +
                        "    <td>"+ price +"</td>" +
                        "    <td>"+ mtInvoiceAble +"</td>" +
                        "    <td>"+ info['create_name'] + (new Date(info['create_date']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>"+ info['perchase_cycle'] +"</td>" +
                        "    <td>"+ (info['current_stock'] || "0") +"</td>" +
                        "    <td>"+ (info['minimum_stock'] || "0") +"</td>" +
                        "    <td class=\"ty-td-control\"><span data-type=\"initStockBySup\" class=\"ty-color-blue\">"+ (info['initial_stock'] || "0") +"</span></td>" +
                        "    <td class=\"ty-td-control\"><span data-type=\"storeHouseBySup\" class=\"ty-color-blue\">"+ (info['location_number'] || "0") +"</span></td>" +
                        "    <td id='havesee'>" +
                        "        <span class='hd' >"+ JSON.stringify(info) +"</span>" +
                        "        <span class=\"ty-color-blue\" data-type=\"fixedScan\">查看</span>" + ctrlStr +
                        "    </td>" +
                        "</tr>";
                }
            }
            $("."+ tblID +" tbody").html(str);
        }
    })
}
// created:hxz 2020-03-11 搜索物料
function search(thisObj,type) {
    var keyword = thisObj.siblings('input').val();
    if(keyword == ""){
        layer.msg("请录入需要搜索的内容");
    }else {
        listData.keyword = keyword ;
        if(type == 1){
            getList('', listData.state, keyword, 1 ,20);
        }else if(type == 2){
            getList('', listData.state, keyword,1,20);
        }
    }
}
// create :hxz 2020-04-21 获取物料的列表
function getList(category, state, keyword, cur, per){
    // state:查询总列表时不传,  1-查询待确认定点信息时, 2-查询暂停采购的材料时, 3-查询待录入初始库存数量时
    // keyword : 查询的代号或名称
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "keyword":keyword,  "pageNum":cur, "per":per  };
    if (state){  data['state'] =  state  }
    if (category){  data['category'] =  category  }
    data['menu'] =  2
    $.ajax({
        "url":"../mt/list",
        "data":data,
        success:function (res) {
            var totalPage = res["totalCount"];//总页数
            var curr = cur ;//当前页
            var jsonStr = JSON.stringify(data) ;
            if(state == 2) { // 暂停
                setPage( $("#ye5") , curr ,  totalPage , "materialEntery", jsonStr );
            }else if(state == 1) { // 需确认定点信息
                setPage( $("#ye2") , curr ,  totalPage , "materialEntery", jsonStr );
            }else{
                setPage( $("#ye1") , curr ,  totalPage , "materialEntery", jsonStr );
            }

            var categories = res['categories'];
            var count = res['count'][0];
            var mtArr = res['data'];
            var enabledCount = res['enabledCount'];
            if(!state){
                $("#count").html(count);
                $(".stopNum").html(enabledCount) ;
                var pid = null;
                var path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span>";
                $("#bottom2").hide(); $("#bottom1").show();
                if(category){  pid = category;  }
                if (!state){   mtCategories = categories; mtCategoriesTree();  }
                $("#catAll").html("");
                for(var i = 0 ; i < categories.length ; i++){
                    var pCat = categories[i]['parent'];
                    if(pCat == pid){
                        var count = 0 ;
                        $("#catTree").find(".treeItem"+ categories[i]['id']).find(".countItem").each(function(){
                            count += Number($(this).html());
                        });
                        categories[i]['count'] = count ;
                        var str = "<li class='catID"+ categories[i]['id'] + "' data-id='"+ categories[i]['id'] +"'><span class='name'>"+ categories[i]['name'] +"</span><span class=\"catNum\">"+ categories[i]['count'] +"</span></li>"
                        $("#catAll").append(str)
                    }
                    if(category == categories[i]['id']){
                        $("#bottom2").show(); $("#bottom1").hide();
                        path = "<span class='go2Cat'><span>"+ categories[i]['name'] +" > </span><span class='hd'>"+ categories[i]['id']  +"</span></span>"
                    }
                }
                var allCount = 0
                $("#catTree").find(".countItem").each(function(){
                    allCount += Number($(this).html());
                });
                $(".mtNum").html(allCount);
            }
            var strMt = "" ;
            if(mtArr && mtArr.length >0){
                for(var j = 0 ; j < mtArr.length ; j++){
                    var item = mtArr[j]
                    if(state == 1){ // 待确认定点信息物料
                        strMt += " <tr>" +
                            "    <td>"+ setVal(item['name'])  +"</td>" +
                            "    <td>"+ setVal(item['code'])  +"</td>" +
                            "    <td>"+ setVal(item['model'])  +"</td>" +
                            "    <td>"+ setVal(item['specifications'] ) +"</td>" +
                            "    <td>"+ setVal(item['create_name']) + (new Date(item['create_date']).format("yyyy-MM-dd hh:mm:ss"))  +"</td>" +
                            "    <td>"+ setVal(item['unit'])  +"</td>" +
                            "    <td>" +
                            "        <span class=\"hd\">"+JSON.stringify(item)+"</span>" +
                            "        <span data-type=\"fixedMassage\" class=\"ty-color-blue \">确定定点信息</span>" +
                            "    </td>" +
                            "</tr>";
                    } else{
                        strMt += " <tr>" +
                            "    <td>"+ setVal(item['name'])  +"</td>" +
                            "    <td>"+ setVal(item['code'])  +"</td>" +
                            "    <td>"+ setVal(item['model'])  +"</td>" +
                            "    <td>"+ setVal(item['specifications'] ) +"</td>" ;
                        if(state == 2){
                            strMt +=  "<td>"+ (new Date(item['enabled_time']).format("yyyy-MM-dd hh:mm:ss")) +"</td>";
                        }else{
                            strMt +=  "<td>"+ setVal(item['create_name']) +  (new Date(item['create_date']).format("yyyy-MM-dd hh:mm:ss")) +"</td>";
                        }
                        let current_stock = ( item['current_stock'] && parseFloat(Number(item.current_stock).toFixed(4))) || 0 ;
                        let minimum_stock = ( item['minimum_stock'] && parseFloat(Number(item.minimum_stock).toFixed(4))) || 0 ;
                        strMt +=     "    <td>"+ setVal(item['unit']) +"</td>" +
                            "    <td>"+ current_stock +"</td>" +
                            "    <td>"+ minimum_stock +"</td>" +
                            "    <td>"+ setVal(item['location_number']) +"</td>" +
                            "    <td class='ty-td-control'><span data-type='sup' class='ty-color-blue'>"+ (item['supplier_number'] || "0") +"个</span></td>" +
                            "    <td>" +
                            "        <span class=\"hd\">"+JSON.stringify(item)+"</span>" ;
                        if(state == 2){
                            strMt +=   "<span data-type=\"startPurchase2\" class=\"ty-color-blue \">恢复采购</span></td></tr>";

                        }else{
                            strMt +=   "<span data-type=\"stopPurchase2\" class=\"ty-color-blue \">暂停采购</span></td></tr>";

                        }
                    }
                }
            }

            var conStr = ".mainCon1  tbody";
            if(state == 1){ // 待确认定点信息物料
                conStr = ".mainCon2  tbody";
                $(".confirmNum").html(mtArr?mtArr.length:0);

            }else{ // 全部物料查看
                $(".stopNum2").html(enabledCount + " 种") ;
                if(state == 2){
                    conStr = ".mainCon5  tbody";
                    if(keyword.length >0){
                        path = "<span class='go2Cat'><span>全部 > 暂停采购的材料: </span><span class='hd'></span></span><span class='ty-color-blue'><span>查找代号或名称含“"+ keyword +"”的原辅材料 > </span></span>";
                    }else{
                        path = "<span class='go2Cat'><span>全部 > 暂停采购的材料: </span><span class='hd'></span></span>";
                    }
                    $(".curCat2").html(path)

                }else{
                    if(keyword.length >0){
                        path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span><span class='ty-color-blue'><span>查找代号或名称含“"+ keyword +"”的原辅材料 > </span></span>";
                        $(".curCat").html(path);

                    }else{
                        $(".curCat").append(path);
                    }
                }
            }
            $(conStr).html(strMt);

        }
    })
}
function getListByPage(category, state, keyword, cur, per){
    // state:查询总列表时不传,  1-查询待确认定点信息时, 2-查询暂停采购的材料时, 3-查询待录入初始库存数量时
    // keyword : 查询的代号或名称
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "keyword":keyword,  "pageNum":cur, "per":per  };
    if (state){  data['state'] =  state  }
    if (category){  data['category'] =  category  }
    data['menu'] =  2
    $.ajax({
        "url":"../mt/list",
        "data":data,
        success:function (res) {
            var totalPage = res["totalCount"];//总页数
            var curr = cur ;//当前页
            var jsonStr = JSON.stringify(data) ;
            if(state == 2) { // 暂停
                setPage( $("#ye5") , curr ,  totalPage , "materialManage", jsonStr );
            }else if(state == 1) { // 待确认定点信息
                setPage( $("#ye2") , curr ,  totalPage , "materialManage", jsonStr );
            }else{
                setPage( $("#ye1") , curr ,  totalPage , "materialManage", jsonStr );
            }

            var categories = res['categories'];
            var count = res['count'][0];
            var mtArr = res['data'];
            var enabledCount = res['enabledCount'];
            if(!state){
                $("#count").html(count);
                $(".stopNum").html(enabledCount) ;
                var pid = null;
                $("#bottom2").hide(); $("#bottom1").show();
                if(category){  pid = category;  }
                if (!state){   mtCategories = categories; mtCategoriesTree();  }
                $("#catAll").html("");
                for(var i = 0 ; i < categories.length ; i++){
                    var pCat = categories[i]['parent'];
                    if(pCat == pid){
                        var count = 0 ;
                        $("#catTree").find(".treeItem"+ categories[i]['id']).find(".countItem").each(function(){
                            count += Number($(this).html());
                        });
                        categories[i]['count'] = count ;
                        var str = "<li class='catID"+ categories[i]['id'] + "' data-id='"+ categories[i]['id'] +"'><span class='name'>"+ categories[i]['name'] +"</span><span class=\"catNum\">"+ categories[i]['count'] +"</span></li>"
                        $("#catAll").append(str)
                    }
                    if(category == categories[i]['id']){
                        $("#bottom2").show(); $("#bottom1").hide();
                    }
                }
                var allCount = 0
                $("#catTree").find(".countItem").each(function(){
                    allCount += Number($(this).html());
                });
                $(".mtNum").html(allCount);
            }
            var strMt = "" ;
            if(mtArr && mtArr.length >0){
                for(var j = 0 ; j < mtArr.length ; j++){
                    var item = mtArr[j]
                    if(state == 1){ // 待确认定点信息物料
                        strMt += " <tr>" +
                            "    <td>"+ setVal(item['name'])  +"</td>" +
                            "    <td>"+ setVal(item['code'])  +"</td>" +
                            "    <td>"+ setVal(item['model'])  +"</td>" +
                            "    <td>"+ setVal(item['specifications'] ) +"</td>" +
                            "    <td>"+ setVal(item['create_name'] + (new Date(item['create_date']).format("yyyy-MM-dd hh:mm:ss")) )   +"</td>" +
                            "    <td>"+ setVal(item['unit'])  +"</td>" +
                            "    <td>" +
                            "        <span class=\"hd\">"+JSON.stringify(item)+"</span>" +
                            "        <span data-type=\"fixedMassage\" class=\"ty-color-blue \">确定定点信息</span>" +
                            "    </td>" +
                            "</tr>";
                    } else{
                        strMt += " <tr>" +
                            "    <td>"+ setVal(item['name'])  +"</td>" +
                            "    <td>"+ setVal(item['code'])  +"</td>" +
                            "    <td>"+ setVal(item['model'])  +"</td>" +
                            "    <td>"+ setVal(item['specifications'] ) +"</td>" ;
                        if(state == 2){
                            strMt +=  "<td>"+ (new Date(item['enabled_time']).format("yyyy-MM-dd hh:mm:ss")) +"</td>";
                        }else{
                            strMt +=  "<td>"+ setVal(item['create_name']) +  (new Date(item['create_date']).format("yyyy-MM-dd hh:mm:ss")) +"</td>";
                        }
                        let current_stock = ( item['current_stock'] && parseFloat(Number(item.current_stock).toFixed(4))) || 0 ;
                        let minimum_stock = ( item['minimum_stock'] && parseFloat(Number(item.minimum_stock).toFixed(4))) || 0 ;
                        strMt +=     "    <td>"+ setVal(item['unit']) +"</td>" +
                            "    <td>"+ current_stock +"</td>" +
                            "    <td>"+ minimum_stock +"</td>" +
                            "    <td>"+ setVal(item['location_number']) +"</td>" +
                            "    <td class='ty-td-control'><span data-type='sup' class='ty-color-blue'>"+ (item['supplier_number'] || "0") +"个</span></td>" +
                            "    <td>" +
                            "        <span class=\"hd\">"+JSON.stringify(item)+"</span>" ;
                        if(state == 2){
                            strMt +=   "<span data-type=\"startPurchase2\" class=\"ty-color-blue \">恢复采购</span></td></tr>";

                        }else{
                            strMt +=   "<span data-type=\"stopPurchase2\" class=\"ty-color-blue \">暂停采购</span></td></tr>";
                        }
                    }
                }
            }

            var conStr = ".mainCon1  tbody";
            if(state == 1){ // 待确认定点信息物料
                conStr = ".mainCon2  tbody";
                // $(".confirmNum").html(mtArr?mtArr.length:0);
                if(keyword){

                }
            }else{ // 全部物料查看（不包含暂停的）
                $(".stopNum2").html(enabledCount + " 种") ;
                if(state == 2){   conStr = ".mainCon5  tbody";   }
            }
            $(conStr).html(strMt);

        }
    })
}
// creator ：侯杏哲 2020-04-26  空赋值
function setVal(val) {
    return val?val:""
}
// creator ：侯杏哲 2020-04-26  占用库位
function getStoreHouse(supId) {
    var info = {};
    if(editObj){
        info = JSON.parse(editObj.parent().next().children(".hd").html());
    }else if(editObjFixedMassage){
        info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
    }else{
        layer.msg("获取材料失败")
        return false;
    }
    $("#currentStation").html("");
    bounce.show($("#holdStockSee"));
    var goodsInfo =
        '<tr>' +
        '    <td>'+ info.name +'</td>' +
        '    <td>'+ info.code +'</td>' +
        '    <td>'+ setVal(info.model) +'</td>' +
        '    <td>'+ setVal(info.specifications) +'</td>' +
        '    <td class="createInfo">'+ setVal(info.create_name) + '&nbsp;&nbsp;' + (info.createName? new Date(info.create_date).format('yyyy/MM/dd hh:mm:ss'): '') +'</td>' +
        '    <td>'+ setVal(info.unit) +'</td>' +
        '    <td>'+ (info.current_stock || 0) +'</td>' +
        '    <td>'+ (info.location_number || 0) +'个</td>' +
        '</tr>';
    $("#holdStockInfo tbody").html(goodsInfo);

    var mtid = info.id
    var data = { "id" : mtid }
    if(supId){
        data['supplierMaterialId'] = supId
    }
    $.ajax({
        "url":"../mt/mtLocation",
        "data": data ,
        success:function (res) {
            var code = res['code'];
            if(code ==400){ //  未选择库位
                $("#onlyNumInfo").show();
                $("#holdStockSeeReset").html("去选择库位");
            }else {
                $("#onlyNumInfo").hide();
                $("#holdStockSeeReset").html("重新选择库位");
                $("#currentStation").html("");
                var list0 = res['0'];  // key:  -1 ：难以区分 ， 0：不区分
                if(list0){
                    $("#holdStockSeeReset").data("type", "0")
                    var list = list0 ;
                    var len = Math.ceil(list.length/10);
                    for(var m=0;m<len;m++){
                        var tabStr =
                            '<table class="ty-table ty-table-control gap">' +
                            '   <tr>' +
                            '       <td rowspan="2" class="td-orange">现况</td>';
                        var size = 10,locationCode = '',locationNum ='<tr>';
                        var render = list.length%10;
                        if (m==len-1 && render>0) {
                            size = render;
                        }
                        for(var n=0;n<size;n++){
                            var i = m*size + n;
                            locationCode += '<td>'+ list[i].location_code +'</td>';
                            locationNum += '<td>'+ list[i].amount +'</td>';
                        }
                        tabStr += locationCode + locationNum +
                            '</tr></table>';
                        $("#currentStation").append(tabStr);

                    }

                }else{
                    $("#holdStockSeeReset").data("type", "-1")
                    var itemStr = "" ,itemStr_1 = "" ;
                    for(var key in res){
                        var list = res[key] || [];
                        if(list.length>0){
                            var info = { "fullName": list[0]['full_name'], 'name':list[0]['name'],  'code':list[0]['code_name'] }
                            var len = Math.ceil(list.length/10);
                            for(var m=0;m<len;m++){
                                var tabStr =
                                    '<table class="ty-table ty-table-control gap">' +
                                    '   <tr>' +
                                    '       <td rowspan="2" class="td-orange">现况</td>';
                                var size = 10,locationCode = '',locationNum ='<tr>';
                                var render = list.length%10;
                                if (m==len-1 && render>0) {
                                    size = render;
                                }
                                for(var n=0;n<size;n++){
                                    var i = m*size + n;
                                    locationCode += '<td>'+ list[i].location_code +'</td>';
                                    locationNum += '<td>'+ list[i].amount +'</td>';
                                }
                                tabStr += locationCode + locationNum +
                                    '</tr></table>';
                            }

                            if(key === '-1'){
                                itemStr_1 +=
                                    "<div class='supKuItem'>" +
                                    "   <div>" +
                                    "       <div> 难以区分供应商的本材料 </div>" +
                                    "   </div>" +
                                    "   <div>"+ tabStr +"</div>"
                                "</div>"
                            }else {
                                itemStr +=
                                    "<div class='supKuItem'>" +
                                    "   <div>" +
                                    "       <div>供应商："+ info['fullName'] +"</div>" +
                                    "      <div>简称："+ info['name'] +"</div>" +
                                    "      <div>代号："+ info['code'] +"</div>" +
                                    "   </div>" +
                                    "   <div>"+ tabStr +"</div>" +
                                    "</div>";
                            }
                        }

                    }
                    $("#currentStation").html(itemStr + itemStr_1);
                }
            }
        }
    })
}
// creator ：侯杏哲 2020-04-26  初始库存
function showStorageHis(sup) {
    var info = {};
    if(editObj){
        info = JSON.parse(editObj.parent().next().children(".hd").html());
    }else if(editObjFixedMassage){
        info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
    }else{
        layer.msg("获取材料失败")
        return false;
    }
    var mtid = info.id
    $("#curNum").html(" " + (info.initial_stock || 0) + " " + info.unit);
    var data = { "mtBaseId" : mtid }
    if(sup){
        var supId = sup.id;
        if(supId){
            data['supplier'] = supId
        }
    }
    bounce.show($('#initStock'));

    $.ajax({
        "url": "../material/modifyRecord.do" ,
        "data": data ,
        success: function (res) {
            var struts = res["struts"] ;
            var str = "" ;
            var initialList = res["initialList"] || [] ;
            var minlist = res["minlist"] ;
            var list = initialList;
            // $("#curNum").html(list.length + '个')
            if(list && list.length > 0){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<tr>" +
                        "<td width='20%'>"+ list[i]["createName"] +"</td>" +
                        "<td width='20%'>"+ (new Date(list[i]["createDate"]).format("yyyy-MM-dd hh:mm:ss"))  +"</td>" +
                        "<td width='20%'>"+ (list[i]["name"]||"") +"</td>" ;
                    str += "<td width=\"20%\">"+ (list[i]["initialStock"] || "") +"</td><td width=\"20%\">"+ list[i]["afterInitialStock"] +"</td></tr>" ;

                }
            }
            $("#initStock tbody").html(str);
        }
    })
}
// created:hxz 2020-03-13 查看待确认列表
function goConfirm() {
    editObj = null;
    editObjFixedMassage = null;
    var count = $("#count").html();
    $(".confirmNum").html(count);
    toggleSuspend(2);
    listData.state = 1
    getList('', 1, '', 1 , 20);
    $(".noFixedPurchaseLogBtn").hide();
    $(".noFixedPurchaseBtn").show().removeClass('ty-color-gray');
}
// created:hxz 2020-03-13 调整框高
function setHeight(objArr){
    // 设置框高
    /*  let Hw = Number($(window).height()-120) , arr = [Hw];
      for(var i = 0 ; i < objArr.length; i++){
          arr.push(Number(objArr[i].height()));
      }
      arr.sort()
      let H = arr[arr.length-1];
      console.log(arr, H)
      for(var i = 0 ; i < objArr.length; i++){
          objArr[i].css({'min-height':H +'px' });
      }*/
}
function goPause(){
    listData = { 'category':false , 'state':2 , 'keyword':'' }
    getList('', 2, '',1,20);
    toggleSuspend(5)
}

// created:hxz 2020-03-11 切换主页与暂停页
function goMainCon1() {
    editObj = null;
    editObjFixedMassage = null;
    toggleSuspend(1);
    $(".curCat .go2Cat:last").click();
}
// created:hxz 2020-03-11 切换主页与暂停页
function toggleSuspend(num){
    $('.mainCon' + num).show().siblings().hide();
    if(num === 1 || num === 5){
        setHeight([ $('.mainCon'+ num + ' .leCat'), $('.mainCon'+ num +' .riMt')]);
    }else{
        setHeight([$('.mainCon' + num + '>div')])
    }
}



function getSupList(obj){
    var superList = [];
    $.ajax({
        url:"../material/retrievalSupplierByName.do",
        data:{
            "name" : "" ,
            "enabled": 1
        },
        success:function (data) {
            var list = data["supplierList"];
            $("#addMtSup").data("choone",list);//整个数据盒子
            // for(let i in list){
            //     let chooae = list[i];
            //     $("#addMtSup").data("choone",chooae);//可以开增值税专用发票
            // }
            //superList = list ;
            // if(list && list.length > 0){
            //     for(var i=0 ; i < list.length ; i++){
            //         var supID = list[i]["id"] ;
            //         var infoList = list[i]["mtSupplierMaterialHashSet"] ;
            //         if( infoList.length > 0 ){
            //             var info = "" , goOn = true ; // goOn - 标识是否继续循环
            //             for(var j = 0 ; j < infoList.length  ; j++){
            //                 var _supId = infoList[j]["supplier_"] ;
            //                 if(_supId == supID){
            //                     info = infoList[j] ;
            //                     info["id"] = supID ;
            //                     info["sbId"] = "" ;
            //                     info["name"] = list[i]["name"] ;
            //                     info["fullName"] = list[i]["fullName"] ;
            //                     goOn = false ;
            //                     superList.push(info);
            //                 }
            //             }
            //         }
            //     }
            // }
            // var bank = list;
            // var str1 = ``;
            // var submig = [];
            // for(let i in bank){
            //     str1 = `${bank[i].id}`;
            //     submig.push(str1);
            // }
            // var box = [];
            // for(let j in submig){
            //     var contractid = 0;
            //     contractid = parseFloat(submig[j]);
            //     contractid = Number(contractid);
            //     box.push(contractid);
            // }
            // box = JSON.parse(box);
            // for(let a in box){
            //     var unid = box[a];
            //     $.ajax({
            //         url:"../supplier/getContractList.do",
            //         data:{
            //             id:unid
            //         },
            //         success:function(res){
            //             var lank = res.data;
            //
            //         }
            //     })
            // }

            // var onid =document.getElementById('supplier');
            // var bank = onid.value;
            // bank = JSON.parse(bank);
            // var type =0;
            // var contractid = bank.id;
            // $.ajax({
            //     url:"../supplier/getContractList.do",
            //     data:{
            //         id:contractid
            //     },
            //     success:function(res){
            //         choseothen(res,type);
            //     }
            // })
            // var bonk =$("option:selected").html();
            // var band = $("#supplier").val();
            setSupplier(list, obj);
        }
    }) ;
}


// creator: sy 2022-07-15 筛选框渲染
function choseothen(res,type){
    var bonk = res.data;
    var code = "";
    var str1 ="<option value='' class='supItem'></option>";
    if(bonk && bonk.length>0){
        for(var i=0;i<bonk.length;i++){
            var bont =bonk[i];
            if(code === bont.sn){
                str1 +="<option selected value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
            }else{
                str1 +="<option value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
            }
        }
    }
    // var str =``;
    // for(let i in bonk){
    //     str =`
    //         <option value=""></option>
    //         <option value="${JSON.stringify(bonk[i])}">${bonk[i].creator}(合同编号)</option>
    //     `;
    // }
    $("#contract").html(str1);
}

// creator: sy 2022-08-24 （查看定点）筛选框渲染
function choseothen1(res,type){
    var bonk = res.data;
    var code = "";
    var str1 ="<option value='' class='supItem'></option>";
    if(bonk && bonk.length>0){
        for(var i=0;i<bonk.length;i++){
            var bont =bonk[i];
            if(code === bont.sn){
                str1 +="<option selected value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
            }else{
                str1 +="<option value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
            }
        }
    }
    $("#contract1").html(str1);
}

// creator : hxz 2019-12-23 供应商刷新
function setSupplier(list , obj, selectedSuplier){
    var name = "" ;
    if(selectedSuplier){
        name = selectedSuplier["fullName"]
    }
    var str = "<option value='' class='supItem'> </option>" ;
    if(list && list.length > 0){
        for(var i=0 ; i < list.length ; i++){
            var info = list[i];
            if(name === info["fullName"]){
                str += "<option selected value='"+ JSON.stringify(info) +"' class='supItem'>"+ info["fullName"] +"</option>" ;
            }else{
                str += "<option value='"+ JSON.stringify(info) +"' class='supItem'>"+ info["fullName"] +"</option>" ;
            }
        }
    }
    obj.html(str);
}
// creator : 侯杏哲 2018-03-30 匹配供应商
function matchSupper(thisObj){
    var supplier = $("#supplier").val() ;
    if(supplier == ""){
        $(".supInfo input").val("");
        $(".supInfo .fa").attr("class", "fa fa-circle-o");
        $(".supInfo").hide();
        $(".stable").hide();
        $(".expInfo").hide()
        $("#e_gName1").val("")
        $("#e_gCode1").val("");
        return false;
    }
    supplier = JSON.parse(supplier) ;
    var supplierId = supplier['id'];
    var info = {};
    let draftAcceptable = supplier.draftAcceptable;
    if(editObj){
        info = JSON.parse(editObj.parent().next().children(".hd").html());
    }else if(editObjFixedMassage){
        info = JSON.parse(editObjFixedMassage.siblings(".hd").html());
    }else{
        layer.msg("获取材料失败")
        return false;
    }
    var mtId = info['id'];
    $.ajax({
        "url":"../mt/supplierState",
        "data":{ "supplierId":supplierId , "mtId": mtId },
        success:function (res) {
            if(res == 1){
                $("#e_gName1").val(supplier["name"]);
                $("#e_gCode1").val(supplier["codeName"]);
                $(".supInfo").show();
                $(".stable").hide();
                $(".supInfo input").val("");
                $(".stable input").val("");
                $("#contract").html("");
                $("#addMtSup .fa").each(function(){
                    $(this).attr("class", "fa fa-circle-o");
                    $(this).removeAttr("disabled");
                    $(this).parent().removeAttr("disabled");
                });
                console.log(supplier);
                var box = supplier['ys'];
                console.log(box);

                setSupInfo(box, $("#sup1"));
                chargeShowInvoice();
                getSupplierTaxRate($("#addMtSup .taxRateList"));
                $("#contread").show();
                $(".canInvoice").show();
                $(".contrInfo").show();
                $("#addcontrot").hide();
                $("#addMtSup .canInvoice2").hide();
                $(".contrInfo #contract").show();
                $("#contract").css({border:"1px solid #ddd",background:"#eee"});
                $("#contract").attr("disabled","disabled");
                if (Number(draftAcceptable) === 1) {
                    $("#addMtSup #acceptBills").next().click();
                    //$("#addMtSup .acceptBills i:eq(0)").attr("class", "fa fa-dot-circle-o");
                } else if (Number(draftAcceptable) === 2){
                    $("#addMtSup #acceptBills").next().next().click();
                }
                let mode = $("body").data('whmode')
                if(info.exp_required == 1 && info.related_item ==2 && info.same_expiration == 0 && mode === 1){
                    $(".expInfo").show()

                }else {
                    $(".expInfo").hide()
                }
            }else{
                bounce_Fixed.show($("#tip1"));
                $("#contracto").show();
                $("#mustchoose").hide();
            }
        }
    })

}

// // creator: sy 2022-07-15 匹配合同编号
// function matchContent(docnobj){
//     // var contract = $("#contract").val();
//     // contract = JSON.parse(contract);
//     // var contractid = contract['id'];
//     $.ajax({
//         url:"../supplier/getContractList.do",
//         data:{
//             id:contractid
//         },
//         success:function(res){
//             var state = res.success;
//             if(state === "1" || state === 1){
//
//             }
//         }
//     })
// }


function chargeShowInvoiceEditSup(){
    var isStable = $("#edit_isStable").val();
    var res = JSON.parse($("#edit_supplier").val());
    var supplier = res['mtSupplierMaterial']['supplier'] ; // 供应商
    var supInfo = res['mtSupplierMaterial'] ;
    var supplier1 = res['supplier'];
    console.log(supInfo);
    if(supInfo){
        // supplier1 = JSON.parse(supplier1) ;
        var supplierMaterial = supplier1['supplierMaterial'];
        for(var key in supplierMaterial){
            supplier1[key] = supplierMaterial[key]
        }
        var canInvoice = $("#edit_canInvoice").val() ;
        var incoiceTypeVal = $("#edit_incoiceType").val() ;
        /*if(supplier1["invoicable"] === "1"){
            $(".canInvoice").show();
            if(supplier1["vatsPayable"] === "1"){ // 能开增值税专用发票vatsPayable
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
            }else{ // 只能开普通票
                incoiceTypeVal = "2";  $("#edit_incoiceType").val(incoiceTypeVal);
                $("#edit_incoiceType1").attr("disabled", "disabled");
                $("#edit_incoiceType1Con").attr("disabled", "disabled");
                $("#edit_incoiceType0").attr("class", "fa fa-dot-circle-o");

            }
        } else{
            canInvoice = "0"; $("#edit_canInvoice").val(canInvoice)
            $(".canInvoice").hide(); $(".incoiceType").hide();
        }*/
        if(ontractsigned === "1"){//选中是
            $(".contrInfo").show();
            $("#contract").removeAttr("disabled");
        }else if(ontractsigned ==="0"){//选中否
            $("#contract").attr("disabled","disabled");
            $(".contrInfo").hide();
        }
        if(isStable === "1"){ //稳定
            $(".priceInfo").show();
            $(".incoiceType4").hide();
            $(".canInvoice2").hide();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show();
                $(".edit_acceptBills").show();
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".priceInfo .type3").html("已约定的单价"); $("#edit_isParValue").val(1);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $("#edit_isParValue").val(1);
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("已约定的<span class='supOrange'>开票</span>单价");
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".edit_acceptBills").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("已约定的<span class='supOrange'>不开票</span>单价");
                $("#edit_isParValue").val(0);
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
                $(".edit_acceptBills").hide();
                $(".incoiceType").hide();$("#edit_isParValue").val("");
            }
        }else if(isStable === "2"){ // 变动频繁
            $(".priceInfo").show();
            $(".canInvoice2").show();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show();
                $(".incoiceType4").hide();
                $(".edit_acceptBills").show();
                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".type3").html("参考单价");
                    $("#edit_isParValue").val(0);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("参考单价(<span class='supOrange'>开票价</span>)");
                    $("#edit_isParValue").val(0);
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".edit_acceptBills").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
                $("#edit_isParValue").val(0);
            }else if(canInvoice === "2"){ // 不确定
                if(supInfo["invoiceCategory"] === "2"){ // 供应商只能开普通票
                    $(".priceInfo .type1").html("参考单价");
                    $("#edit_isParValue").val("");
                }else{
                    $("#edit_isParValue").val(0);
                    $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
                }
                $(".incoiceType").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".edit_acceptBills").hide();
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
                $(".edit_acceptBills").hide();
            }
        }else {
            $(".incoiceType").hide();
            $(".edit_acceptBills").hide();
        }
    }else{
        // 初始化
        $(".stable").hide();
    }
}
function chargeShowInvoice() {
    var isStable = $("#isStable").val();
    var supplier = $("#supplier").val() ;
    if(supplier){
        supplier = JSON.parse(supplier) ;
        var supplierMaterial = supplier['supplierMaterial'];
        for(var key in supplierMaterial){
            supplier[key] = supplierMaterial[key]
        }
        var canInvoice = $("#canInvoice").val() ;
        var incoiceTypeVal = $("#incoiceType").val() ;//如何让它变成1呢
        /*if(supplier["invoicable"] === "1"){
            $(".canInvoice").show();
            if(supplier["vatsPayable"] === "1"){ // 能开增值税专用发票 vatsPayable
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
            }else{ // 只能开普通票
                incoiceTypeVal = "2";  $("#incoiceType").val(incoiceTypeVal);
                $("#incoiceType1").attr("disabled", "disabled");
                $("#incoiceType1Con").attr("disabled", "disabled");
                $("#incoiceType0").attr("class", "fa fa-dot-circle-o");

            }
        } else{
            canInvoice = "0"; $("#canInvoice").val(canInvoice)
            //$(".canInvoice").hide();
            $(".incoiceType").hide();
        }*/
        if(ontractsigned === "1"){//是
            $(".contrInfo").show();
            $("#contract").removeAttr("disabled");
        }
        else if(ontractsigned ==="0"){//选中否
            $("#contract").attr("disabled","disabled");
            $(".contrInfo").hide();
        }
        if(isStable === "1"){ //稳定
            $(".priceInfo").show();
            $(".incoiceType4").hide();
            $(".canInvoice2").hide();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show();
                $(".acceptBills").show();
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".priceInfo .type3").html("已约定的单价"); $("#isParValue").val(1);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $("#isParValue").val(1);
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("已约定的<span class='supOrange'>开票</span>单价");
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".acceptBills").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("已约定的<span class='supOrange'>不开票</span>单价");
                $("#isParValue").val(0);
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
                $(".acceptBills").hide();
                $(".incoiceType").hide();$("#isParValue").val("");
            }
        }else if(isStable === "2"){ // 变动频繁
            $(".priceInfo").show();
            $(".canInvoice2").show();
            if(canInvoice === "1"){ // 能开票
                $(".incoiceType").show()
                $(".acceptBills").show();
                let unit = $("#addMtSup").data("choone");
                if(unit == "1"){
                    $("#incoiceType1Con").removeAttr("disabled");
                    $("#incoiceType1").removeAttr("disabled");
                }
                $(".incoiceType4").hide();
                $(".type1").hide();

                if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                    $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                    $(".type3").html("参考单价");
                    $("#isParValue").val(0);
                }else if(incoiceTypeVal === "2"){ // 开普通发票
                    $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                    $(".priceInfo .type1").html("参考单价(<span class='supOrange'>开票价</span>)");
                    $("#isParValue").val(0);
                }
            }else if(canInvoice === "0"){ // 不能开票
                $(".incoiceType").hide();
                $(".acceptBills").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
                $("#isParValue").val(0);
            }else if(canInvoice === "2"){ // 不确定
                //if(supplier["invoiceCategory"] === "2"){ // 供应商只能开普通票
                    $(".priceInfo .type1").html("参考单价");
                    $("#isParValue").val("");
               //}else{
               //    $("#isParValue").val(0);
               //    $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
               //}
                $(".incoiceType").hide();
                $(".acceptBills").hide();
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
            }else{ // 还没选能否开票
                $(".priceInfo").hide();
                $(".acceptBills").hide();
            }
        }else {
            $(".incoiceType").hide();
            $(".acceptBills").hide();
        }
    }else{
        // 初始化
        $(".stable").hide();
    }
}
function chargeshow8() {
    var haveInvoice = $("#haveInvoice").val();
    var invoiceCategory = $("#vatInvoice").val();
    if(haveInvoice === "1" && invoiceCategory === "1"){
        $(".hang8").show();
    }else{
        $(".hang8").hide();
    }
}

//  creator ：侯杏哲 2019/12/4 采购合同中包含本材料吗
function containThis(type , thisObj) {
    setRadioSelect("containThis", [1,0], type, thisObj);

}

// creator：侯杏哲 2019/12/24 操作单选按钮
function setRadioSelect(str, arr, selectVal, thisObj){
    var isEdit = $("#isEdit").val()
    var idStr = "#"
    if(isEdit === "1"){
        idStr = "#edit_"
    }
    idStr += str ;
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $(idStr).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $(idStr + arr[i]).attr("class" , "fa fa-circle-o");
    }
    $(idStr + selectVal).attr("class" , "fa fa-dot-circle-o");
}

// creator: sy 2022-08-22 此材料是否包含于与供应商已签订的合同中（查看定点信息）
function urgnistad(type,thisObj){
    // editObj = lank;
    if(type == "1" || type ==1){
        $(".contrInfo").show();
        $("#addcontrot1").show();
        $("#contract1").removeAttr("disabled");
        $("#contract1").css("background","white");
        var utbo = $("#cgaddcontact3").data("box") ;
        let edit_supplier = JSON.parse($("#edit_supplier").val())
        let untad = edit_supplier.supplier.id;
        let type =1;
        $("#urgnistad1").attr("class","fa fa-dot-circle-o");
        $("#urgnistad0").attr("class","fa fa-circle-o");
        getContract(untad, 'updateFixed');
    }else{
        $("#contract1").attr("disabled","disabled");
        $("#addcontrot1").hide();
        $("#contract1").css("background","#eee");
        $("#urgnistad1").attr("class","fa fa-circle-o");
        $("#urgnistad0").attr("class","fa fa-dot-circle-o");
    }
    setRadioSelect("urgnistad", [1,0],type,thisObj);
    var isEdit = $("#isEdit").val();
    if(isEdit ==="1"){
        chargeShowInvoiceEditSup();
    }else{
        chargeShowInvoice();
    }
}
//  creator : sy 2022/4/25 此材料是否包含于与供应商已签订的合同中(添加定点供应商）
// var editObj = null;
function ontractsigned(type, thisObj){
    // editObj = band;
    if(type == "1" || type ==1){
        $("#addcontrot").show();
        $("#contract").removeAttr("disabled");
        $("#contract").css("background","white");
        var band = $("#supplier").val();
        band = JSON.parse(band);
        var untid = band.id;
        $("#newcontract").data("id",untid);
        var type =1;
        $("#ontractsigned1").attr("class","fa fa-dot-circle-o");
        $("#ontractsigned12").attr("class","fa fa-dot-circle-o");
        $("#ontractsigned02").attr("class","fa fa-dot-circle-o");
        getContract(untid, 'addFixed')
    }else{
        $("#contract").attr("disabled","disabled");
        $("#addcontrot").hide();
        $("#contract").css("background","#eee");
    }
    setRadioSelect("ontractsigned", [1,0],type,thisObj);
    var isEdit = $("#isEdit").val();
    if(isEdit ==="1"){
        chargeShowInvoiceEditSup();
    }else{
        chargeShowInvoice();
    }
}
//  creator ：侯杏哲 2019/12/4 该供应商供应的本材料价格是否稳定
function isStable(type , thisObj) {
    setRadioSelect("isStable", [1,2], type, thisObj);

    var isEdit = $("#isEdit").val();
    if(isEdit === "1"){
        chargeShowInvoiceEditSup();
    }else {
        chargeShowInvoice();
    }
}
//  creator ：侯杏哲 2019/12/4 购买本材料是否能开发票
function canInvoice(type , thisObj) {
    let box =$("#addMtSup").data("choone");
    let nid = $("#canInvoice1").data("unid");
    let box1 = $("#fixedScan").data("mess");
    console.log(box1);
    for(let i in box){
        let choose = box[i];
        let id = choose.id;
        if(id === nid){
            let unit = choose. vatsPayable;
            if(unit == "1"){
                $("#incoiceType1Con").removeAttr("disabled");
                $("#incoiceType1").removeAttr("disabled");
            }
        }
    }

    setRadioSelect("canInvoice", [1,2,0], type, thisObj);
    var isEdit = $("#isEdit").val();
    if(isEdit === "1"){
        chargeShowInvoiceEditSup();
    }else {
        chargeShowInvoice();
    }
}
//  creator ：侯杏哲 2019/12/4 购买本材料给开何种发票
function incoiceType(type , thisObj) {
    setRadioSelect("incoiceType", [1,2,4], type, thisObj);
    var isEdit = $("#isEdit").val();
    if(isEdit === "1"){
        chargeShowInvoiceEditSup();
    }else {
        chargeShowInvoice();
    }
}
//  creator ：侯杏哲 2019/12/4 参考单价
function referPrice(type , thisObj) {
    setRadioSelect("referPrice", [1,0], type, thisObj);
}
/*creator:lyt 2024/1/17 0017 下午 7:24 */
function acceptBills(type , thisObj){
    setRadioSelect("acceptBills", [1,2], type, thisObj);

}
//  creator ：侯杏哲 2019/12/4 该价格是否含运费
function containYunFee(type , thisObj) {
    $(".manageAddress").hide()
    if(type === 1 || type === 2){
        thisObj.parent().siblings(".manageAddress").show();
    }
    setRadioSelect("containYunFee", [1,2,3], type, thisObj);
}
//  creator ：侯杏哲 2019/12/4 所供应材料的包装方式
function packgeType(type , thisObj) {
    setRadioSelect("packgeType", [1,2], type, thisObj);

}

// creator:hxz 2020-04-09 创建种类树
function mtCategoriesTree(){
    var catArr = mtCategories ;
    $("#catTree").html("");
    for(var i = 0 ; i <catArr.length ; i++){
        var id = catArr[i]['id']
        var pid = catArr[i]['parent']
        if(pid){
            var str = "<span class='treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").find(".treeItem"+pid).append(str);

        }else{ // 一级类别
            var str = "<span class='treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").append(str);
        }
    }
}
// creator : 侯杏哲 2018-03-30 供应商简称
function setGname2(thisObj){
    var fullname = thisObj.val() ;
    $("#e_gName2").val(fullname.substr(0,6));
}
// creator ：侯杏哲 2018-02-12  第 3 行   是否有合同
function haveContract(type , thisObj){
    setRadioSelect("haveContract", [1,0], type, thisObj);

    if(type == 1){  // 有合同
        $(".hang4").show(500);
    }else if(type == 0){
        $(".hang4").hide(200);
        // 清空数据
        $("#e_gCompactNo").val("") ;
        $("#e_gCompactExpire").val("") ;
        $("#e_gCompactSignDay").val("") ;
    }
}
// creator ：侯杏哲 2018-02-12  第5行 能否开发票
function haveInvoice(type , thisObj){
    setRadioSelect("haveInvoice", [1,0], type, thisObj);
    var isEdit = $("#isEdit").val();
    if(isEdit ==="1"){
        chargeShowInvoiceEditSup();
    }else{
        chargeShowInvoice();
    }
    var haveInvoice = $("#haveInvoice").val();
    if(haveInvoice === "1"){
        $("#vatInvoice1").attr("class","fa fa-dot-circle-o");
        $("#vatInvoice2").attr("class","fa fa-circle-o");
        setRadioSelect("vatInvoice",[1,2],type,thisObj);
        var vatInvoice = $("#vatInvoice").val();
        if(vatInvoice == 1){
            $(".hang_6").show();
        }
    }else{
        $("#vatInvoice1").attr("class","fa fa-circle-o");//左侧清除点
        $("#vatInvoice2").attr("class","fa fa-circle-o");//右侧清除点
    }
    if(type === 1){
        $(".hang6").show()
        $(".hang8").show()
    }else{
        $(".hang6").hide();
        $(".hang8").hide();
    }
    // chargeshow8();
    chargeHangType();
}
// creator ：侯杏哲 2018-02-12  第6行 能否开增值税专用发票
function vatInvoice(type , thisObj){
    setRadioSelect("vatInvoice", [1,2], type, thisObj);
    if(type == 1){
        $(".hang_6").show();
    }else{
        $(".hang8").hide();
        $(".hang_6").hide();
        $("#e_gRate0").val("");
    }
    chargeshow8();
}

function chargeshow8() {
    var haveInvoice = $("#haveInvoice").val();
    var invoiceCategory = $("#vatInvoice").val();
    if(haveInvoice === "1" && invoiceCategory === "1"){
        $(".hang8").show();
    }else{
        $(".hang8").hide();
    }
}
// creator: hxz 2019-12-25 判断挂账的项目
function chargeHangType(){
    // 考虑公司不能开发票，则从入库之日起，且不可编辑
    var haveInvoice = $("#haveInvoice").val() ;
    var setHangAccount = $("#setHangAccount").val() ;
    if(setHangAccount === "0" || setHangAccount === ""){
        $("#setStartDate").val("") ;
        return false
    }
    if(haveInvoice === "1"){ // 能开票
        $("#setStartDate1").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate2").attr("class" ,"fa fa-circle-o ") .attr("disabled","disabled");
        // $("#setStartDate2Con").attr("disabled","disabled");
        $("#setStartDate2Con").removeAttr("disabled") ;
        $("#setStartDate2").removeAttr("disabled");
        $("#setStartDate").val("") ;
    }else if(haveInvoice === "0"){
        $("#setStartDate1").attr("class" ,"fa fa-dot-circle-o ccc").attr("disabled" , "disabled") ;
        $("#setStartDate2").attr("class" ,"fa fa-circle-o ccc").attr("disabled" , "disabled") ;
        $("#setStartDate2Con").attr("disabled" , "disabled") ;
        $("#setStartDate").val(1) ;
    }else{
        $("#setStartDate1").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate2").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate").val("") ;
    }
}
//  creator ：侯杏哲 2018-02-12  第13行 以上价格是否包含运费
function hui(type , thisObj) {
    setRadioSelect("hui", [1,2], type, thisObj);

}
//  creator ：侯杏哲 2018-02-12  第16行 是否需要预付款
function setAdvanceFee(type , thisObj) {
    setRadioSelect("setAdvanceFee", [1,0,2], type, thisObj);
    if(type == 1){
        $("#paymore").show();
        $("#chooseunde").show();
        $("#uncertainty1").attr("class","fa fa fa-circle-o");
        $("#e_gRate1").removeAttr("disabled");
        $("#e_gRate1").css("background","white");
    }else{
        $("#paymore").hide();
        $("#chooseunde").hide();
    }
}
//  creator ：侯杏哲 2018-02-12  第7行 是否接受挂账
function setHangAccount(type , thisObj) {
    setRadioSelect("setHangAccount", [1,0], type, thisObj);
    $("#hangDays").val("") ;
    // $("#setStartDate").val("") ;
    // $("#setStartDate1").attr("class" , "fa fa-circle-o") ;
    // $("#setStartDate0").attr("class" , "fa fa-circle-o").removeAttr("disabled") ;
    if(type == 1){
        $(".hang7_1").show();  $(".hang_7").show();
        // setStartDate(1, $("#setStartDate1Con"));
    }else{
        $(".hang7_1").hide(); $(".hang_7").hide();
    }
    // 设置挂账的选项
    chargeHangType();
}
//  creator ：侯杏哲 2018-02-12  第7行 自入库之日起 / 自发票提交之日起
function setStartDate(type , thisObj) {
    setRadioSelect("setStartDate", [1,2], type, thisObj);
}

// creator: hxz 2018-09-03 格式化发票种类
function catoryFormat( type ){
    if(type == 1){
        return "增值税专用发票" ;
    }else if(type == 2){
        return "其他发票" ;
    }else{
        return "" ;
    }
}
//
function chargePriceShow(supInfo) {
    var supplier = supInfo ;

    // var ttl = "", info = supplier["isTax"] === "1"?"含税":"不含税";
    var ttl = "", info = "";
    var isStable = supplier["priceStable"];
    var canInvoice = String(supplier["materialInvoicable"]);
    var incoiceTypeVal = supplier["materialInvoiceCategory"];

    if(isStable === "1"){ //稳定
        if(canInvoice === "1"){ // 能开票
            ttl = "已约定的单价";
            info += "开票价";
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "已约定的单价";
            info += "不开票价";
        }
    }else if(isStable === "2"){ // 变动频繁
        if(canInvoice === "1"){ // 能开票
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                ttl = "参考单价";
                info += "开票价";
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                ttl = "参考单价";
                info += "开票价";
            }
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "参考单价";
            info += "不开票价";

        }else if(canInvoice === "2"){ // 不确定
            ttl = "参考单价";
            info += "不开票价";
        }
    }
    var price = supplier["unitPriceNotax"]
    if(supplier["isTax"] == "1"){ // 含税
        price = supplier["unitPrice"];
    }
    info += price + "元";
    var infoall = ""
    switch (String(supplier["inclusiveFreight"])){
        case "1":
            infoall = "含运费的" + info ;
            break;
        case "2":
            infoall = "含运费的" + info + "，但材料到本市后需我司自提";
            break;
        case "3":
            infoall = "不含运费的" + info ;
            break;
        default:
            infoall = info ;
    }
    return { "ttl":ttl , "info":infoall }

}
// creator:hxz 2019-12-26 总结供应商信息
function setSupInfo(res, obj) {
    // var supplierMaterial = res['mtSupplierMaterial'];
    // for(var key in supplierMaterial){
    //     res[key] = supplierMaterial[key]
    // }
    // 供应商信息赋值
    var str2 = '';
    // if(supplier["hasContact"] == 1){ // 签订采购合同
    //     str1 = "采购合同已签订";
    //     if(supplier["contractSn"]){ str1 += "，合同编号"+ supplier["contractSn"].substr(0,10); }
    //     if(supplier["validDate"]){ str1 += "，有效期至"+ supplier["validDate"].substr(0,10); }
    //     if(supplier["signDate"]){ str1 += "，签署日期为"+ supplier["signDate"].substr(0,10) ; }
    // }else{
    //     str1 = "与该供应商暂无采购合同 ";
    //     $(".containThis").hide(); $("#containThis").val("");
    // }
    str2 += `
        <span class='supGreen'>${res.kp ? res.kp : ''}</span>
        <span class='supOrange'>${res.kp !== "" && res.gz !== "" ? ',' : ''}${res.gz ? res.gz : ''}</span>
        <span class='supBlue'>${res.hp !== "" && res.gz !== "" ? ',' : ''}${res.hp ? res.hp : ''} </span>
    `;
    // let inid = res.id;
    // $("#canInvoice1").data("unid",inid);
    // if(res.invoicable == "1"){
    //     $(".incoiceType").show();
    //     if(res["vatsPayable"] == "1"){
    //         if(res["taxRate"]){
    //             str2 = "<span class='supGreen'>该供应商能开税率为"+ res["taxRate"] +"%的增值税专用发票， </span>"
    //         }else{
    //             str2 = "<span class='supGreen'>该供应商能开增值税专用发票， </span>"
    //         }
    //     }else{
    //         str2 = "<span class='supGreen'>该供应商仅能开普通发票， </span>"
    //     }
    // }else{
    //     $(".incoiceType").hide();
    //     str2 = "<span class='supGreen'>该供应商不能开发票， </span>"
    // }
    // if(res["chargeAcceptable"] == 1){
    //     str2 += "<span class='supOrange'>可接受挂账，";
    //     if(res["chargePeriod"]){
    //         str2 += "账期"+ res["chargePeriod"] +"天，";
    //     }
    //     if(res["chargeBegin"] == "1"){
    //         str2 += "自货物入库之日起计算，</span>";
    //     }else if(res["chargeBegin"] == "2"){
    //         str2 += "自发票入账之日起计算，</span>";
    //     }
    // }else{
    //     str2 += "<span class='supOrange'>不接受挂账，</span>";
    // }
    // 4.1  可接收承兑汇票
    // 4.2  不确定能接受承兑汇票
    // let draftAcceptable = Number( res["draftAcceptable"] )
    // if(draftAcceptable === 2){
    //     str2 += "<span class='supBlue'>不确定能接受承兑汇票</span>";
    // }else {
    //     str2 += "<span class='supBlue'>可接收承兑汇票</span>";
    // }
    obj.html(str2);

}
// creator:hxz 2019-12-26 总结采购信息
function setSupMtInfo(bon) {
    console.log(bon);
    var str = '' ;
    str += `
        <span class='supBlue'>${bon.cght ? bon.cght + ',' : ''}</span>
        <span class='supOrange'>${bon.bz ? bon.bz + ',' : ''}</span>
        <span class='supGreen'>${bon.zdkc ? bon.zdkc + ',' : ''}</span>
        <span class='supOrange'>${bon.zdcg ? bon.zdcg + ',' : ''}</span>
        <span class='supGreen'>${bon.cgzq}</span><br/>
        <span class='supOrange'>${bon.jgwd ? bon.jgwd + ',' : ''}</span>
        <span class='supGreen'>${bon.kpys ? bon.kpys + ',' : ''}</span>
        <span class='supOrange'>${bon.yfk}</span>
    `;
    return str ;
}

// creator:sy 2022-4-25 新增合同
function newContract(){
    contractInfoEdit('new','addFixed')
}
// creator : 2024-9-25 李玉婷 编辑合同（新增、修改、续约）
function contractInfoEdit(type, source) {
    $("#newContractInfo").data('type', type).data('source', source);
    bounce_Fixed.show($("#newContractInfo"));
    let cusInfo  = {}
    $("#newContractInfo").data("source", source)
    $("#newContractInfo input").val("")
    $("#newContractInfo .fileCon>div").html("")
    $("#newContractInfo .deleteFile").html('')
    initCUpload2($("#cUpload1"),'img');
    initCUpload2($("#cUpload2"),'doc');
    if (source === 'addFixed') {
        cusInfo = JSON.parse($("#supplier").val())
    } else {
        let edit_supplier = JSON.parse($("#edit_supplier").val())
        cusInfo = edit_supplier.supplier
    }
    let supplier = cusInfo;
    $("#newContractInfo [name='customerName']").val(supplier.name)
    getContractByCustomer()
        .then(data => {
            $("#newContractInfo").data('productListTY',data.mtList) ;
            renderEditProductTy()
        })
    /*if(type === 'update' || type === 'cRenewal' || type === 'cRenewalHistory'|| type === 'cRenewalStop'){
        editContractObj = thisObj
        if(source === 'addMt'){
            var newcInfo = JSON.parse(thisObj.siblings(".hd").html());
            $("#newContractInfo .cNo").val(newcInfo.cNo)
            $("#newContractInfo .cSignDate").val(newcInfo.cSignDate)
            $("#newContractInfo .cStartDate").val(newcInfo.cStartDate)
            $("#newContractInfo .cEndDate").val(newcInfo.cEndDate)
            $("#newContractInfo .cMemo").val(newcInfo.cMemo)
            let imgStr1 = ``
            newcInfo.fileCon1.forEach(function(data){
                imgStr1 = `<span class="fileIm" >
                                <span>${data.orders}</span>
                                <span class="fa fa-times"></span>
                                <span class="hd">${JSON.stringify(data) }</span>
                            </span>`
            })
            $("#newContractInfo .fileCon1").append(imgStr1)
            let imgStr2 = ``
            newcInfo.fileCon2.forEach(function(data){
                imgStr2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data)}</span>
                         </span>`
            })
            $("#newContractInfo .fileCon2").html(imgStr2)


        } else {
            // 接口获取合同详情
            let cid = 0
            if (!thisObj) {
                cid = $("#cEndTip").data("cid")
            } else {
                cid = thisObj.parents(".contractItem").data("id") ;
            }
            $("#newContractInfo").data("cid",cid)
            $.ajax({
                url : $.webRoot + "/supplier/poContractBaseMes.do",
                data: { id: cid },
                success:function (res) {
                    let data = res.data
                    let contractBase = data.contractBase
                    let listImage = data.listImage
                    let listTY = data.listMt || []
                    var success = res.success
                    if(success !== 1){
                        layer.msg("获取原来的合同信息失败！");
                        return false;
                    }
                    $("#newContractInfo .cNo").val(contractBase.sn)
                    $("#newContractInfo .cSignDate").val(contractBase.signTime?(new Date(contractBase.signTime).format("yyyy-MM-dd")):'' )
                    let start = new Date(contractBase.validStart).format("yyyy-MM-dd")
                    let end = new Date(contractBase.validEnd).format("yyyy-MM-dd")
                    $("#newContractInfo .cStartDate").val( start ).data("old", start);
                    $("#newContractInfo .cEndDate").val( end ).data("old", end);
                    $("#newContractInfo .cMemo").val(contractBase.memo);
                    let imgStr1 = ``
                    for(let i in listImage) {
                        imgStr1 += `<span class="fileIm"  >
                                       <span>${Number(i) + 1}</span>
                                       <span class="fa fa-times"></span>
                                       <span class="hd">${JSON.stringify(listImage[i]) }</span>
                                  </span>`
                    }
                    $("#newContractInfo .fileCon1").append(imgStr1)
                    let imgStr2 = ``
                    if( contractBase.filePath &&  contractBase.filePath.length > 0){ // 下面的（id: 1）是用来区分是否修改用的
                        imgStr2 = `<span class="fileIm"  >
                                       <span class="fa fa-file-word-o"></span>
                                       <span class="fa fa-times"></span>
                                       <span class="hd">${ JSON.stringify({ id: 1, filename : contractBase.filePath , originalFilename: contractBase.fileName })  }</span>
                                  </span>`
                    }
                    $("#newContractInfo .fileCon2").html(imgStr2)
                    getContractByCustomer()
                        .then(data => {
                            let productListTY = data.mtList || []
                            productListTY.map(item => {
                                item.isChecked = false
                                listTY.map(val => {
                                    if (item.id === val.id) {
                                        item.isChecked = true
                                    }
                                })
                            })
                            $("#newContractInfo").data('productListTY',data.mtList) ;
                            renderEditProductTy()
                        })
                }
            })
        }
    }*/
}
function getContractByCustomer() {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: $.webRoot + '/supplier/getContractMt.do',
            data: {
                type: 1 // 1-添加材料 2-移除材料
            },
        }).then(res => {
            let data = res.data
            resolve(data)
        })
    })
}
function renderEditProductTy() {
    let productListTY = $("#newContractInfo").data('productListTY') || []
    let editProductTY = productListTY.filter(item => item.isChecked)
    let nameArr = editProductTY.map(item => item.name)
    $("#newContractInfo .tyGoodNumber").html(nameArr.length)
    $("#newContractInfo .tyGoodList").html(nameArr.join("、"))
}


// creator: sy 2022-08-23 新增合同
/*function newcontroat1(){
    $("#fixedScan").hide();
    $("#newcontract").show();
    $("#connum").val("");
    $(".goodList").html("");
    $("#newcontract").css("top","40px");
    $("#newcontract .scanGs").data('gsArr',[]).data("gsArrNo",[]).html("0");
    initCUpload2($("#cUpload1"),'img');
    initCUpload2($("#cUpload2"),'doc');
    $("#q2").hide();
    $("#q3").show();
    $("#cgaddcontact").hide();
    $("#cgaddcontact3").show();
}
*/

function initCUpload2(obj,type){
    let fileTypeExtsStr = ''
    let multi = true
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(type == "doc"){
        multi = false
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    }
    let itemTemplate = ``
    obj.html("")
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi: multi,
        buttonText:"上传",
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data)
            if(type == "img"){
                let len =  $(`.fileCon1`).find(".fileIm").length;
                if(len < 9){
                    data.orders = len + 1
                    data.filePath = data.filename
                    data.title = data.originalFilename
                    let imgStr1 = `<span class="fileIm"  >
                                 <span style="font-size: 17px;">${ 1 + len }</span>
                                 <span class="fa fa-times" style="font-size: 15px;"></span>
                                 <span class="hd">${ JSON.stringify(data) }</span>
                            </span>`
                    $(`.fileCon1`).append(imgStr1);
                }else{
                    layer.msg('最多只能上传9个文件')
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                }
            }else if(type == "doc"){
                let len =  $(`.fileCon2`).find(".fileIm").length;
                if(len === 0){
                }else{
                    let delO = $(`.fileCon2`).find(".fileIm")
                    let info = JSON.parse(delO.find(".hd").html())
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                    delO.remove();
                }
                let str2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o" style="font-size: 17px;"></span>
                             <span class="fa fa-times" style="font-size: 15px;"></span>
                             <span class="hd">${ JSON.stringify(data) }</span>
                        </span>`
                $(`.fileCon2`).html(str2);
            }
        }
    })
}

// creator:sy 2022-4-28 向合同中添加材料
function addmaterial(){
    $("#addcontract").data("fun","addGs");
    var addid = $("#newcontract").data("id");
    $.ajax({
        url:"../supplier/getContractMaterialList.do",
        data:{
            id:addid
        },
        success:function(res){
            setContactGS(res,true);
        }
    })
    // bounce.show($("#addcontract"));
}

// creator: sy 2022-07-20 渲染可选择的材料
function  setContactGS(list, boolSet, isHasCheck) {
    let str = '';
    let cstr = '';
    if(boolSet){
        $("#tipcontractGoods .selectTd").show()
        $("#tipcontractGoods .addOrCancel").show()
        $("#tipcontractGoods .cScanc").hide()
        $("#tipcontractGoods .countStr").show()
    } else {
        $("#tipcontractGoods .selectTd").hide()
        $("#tipcontractGoods .addOrCancel").hide()
        $("#tipcontractGoods .cScanc").show()
        $("#tipcontractGoods .countStr").hide()
    }
    list = list || [];
    list.forEach(function (im) {
        let isCheckedStr = im.isChecked && isHasCheck?'checked':''
        let handleStr = boolSet?`<td><div class="ty-checkbox"><input type="checkbox" name="goods" id="good_${im.id}" ${isCheckedStr}/><label for="good_${im.id}"></label></div></td>`:''
        str += `<tr data-id="${im.id}">
                    ${handleStr}
                    <td>${im.code}</td>
                    <td>${im.name}</td>
                    <td>${im.model}</td>
                    <td>${im.specifications}</td>
                    <td>${im.unit}</td>
                    <td>
                        <span class="link-blue" onclick="includeGoodContract($(this))">${im.contractNum || 0}个</span>
                        <span class="hd">${JSON.stringify(im)}</span>
                    </td>
                </tr>`
    })
    $("#tipcontractGoods table tbody").html(str);
    bounce_Fixed3.show($("#tipcontractGoods"));
}
// creator: 李玉婷，2024-09-25 14:28:46， 包含某商品的合同
function includeGoodContract(selector) {
    let mtInfo = JSON.parse(selector.siblings(".hd").html());
    let cusInfo = ''
    let source = $("#newContractInfo").data('source')
    if (source === 'addFixed') {
        cusInfo = JSON.parse($("#supplier").val())
    } else {
        let edit_supplier = JSON.parse($("#edit_supplier").val())
        cusInfo = edit_supplier.supplier
    }
    let cusArr = [cusInfo.codeName, cusInfo.fullName]
    $("#includeGoodContract .cusInfo").html([...cusArr].filter(item => item || item === 0).join(" / "));
    let listPoContract = mtInfo.listPoContract || [];
    let str = ''
    for (let item of listPoContract) {
        str += `<tr>
                    <td>${item.sn}</td>
                    <td>${item.materialCount || 0}种</td>
                    <td>${moment(item.validStart).format("YYYY-MM-DD")}至${moment(item.validEnd).format("YYYY-MM-DD")}</td>
                    <td>${item.signTime?(moment(item.signTime).format("YYYY-MM-DD")):''}</td>
                    <td>${item.createName} ${moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                </tr>`
    }
    $("#includeGoodContract tbody").html(str)
    bounce_Fixed4.show($("#includeGoodContract"));
}
// creator: sy 2022-07-19 从本合同中添加商品弹窗中的确定按钮
function addOrCancelOk(){
    let origin = $("#tipcontractGoods").data("origin");
    let productListTY = $("#newContractInfo").data('productListTY') || []
    if (origin === 'addTyGs') {
        productListTY.map(item => item.isChecked = false)
    }
    $("#tipcontractGoods input:checkbox:checked").each(function(){
        let id = $(this).parents("tr").data("id")
        if (origin === 'addTyGs') {
            productListTY.map(item => {
                if (item.id === id) {
                    item.isChecked = true
                }
            })
        }
        if (origin === 'removeTyGs') {
            productListTY.map(item => {
                if (item.id === id) {
                    item.isChecked = false
                }
            })
        }
    })
    if (origin === 'addTyGs' || origin === 'removeTyGs') {
        renderEditProductTy()
    }
    bounce_Fixed3.cancel()
}
// creator: sy 2022-08-23 渲染从本合同种移除材料
function setDettleCont(late,boolSet){
    console.log(late);
    let str = '';
    let ctsa = '';
    if(boolSet){
        ctsa = '<span class="fa fa-square-o chose"></span>';
    }
    late = late || [];
    if(late == []){
        $("#cGTpc").html("可供选择的材料共有以下<span>0种</span>");
        return false;
    }
    let item3 = [];
    if(late.data == undefined){
        item3 = late;
    }else{
        item3 = late.data;
    }
    for(let i in item3){
        let atmo = item3[i];
        let str1 = ``;
        let band = item3.length;
        str1 +=`可供选择的材料共有以下<span>${band}种</span>`;
        $("#cGTpc").html(str1);
        str +=`
            <tr>
                <td class="before">${ctsa}</td>
                <td style="overflow:inherit;">
                    <div class="controlTd" id="dettlebeffoe">${handleNull(atmo.code)}<span class="hd">${JSON.stringify(atmo)}</span></div>
                </td>
                <td>${handleNull(atmo.name)}</td>
                <td>${handleNull(atmo.model)}</td>
                <td>${handleNull(atmo.specifications)}</td>
                <td>${handleNull(atmo.unit)}</td>
        </tr>`;
    }
    $("#remoecontact table tr:gt(0)").remove();
    $("#remoecontact table").append(str);
    bounce.show($("#remoecontact"));
}

// creator:sy 2022-4-28 本合同下的材料
function contentall(){
    // let wanbox = [];
    let list = $("#goodLust").data("chooseGood");
    // list = JSON.parse(list);
    // setContactGS(list , false);
    setContractBN(list,false);
    // $("#contactbox .bonceCon div p span").html(0);
    $("#cGTip").html(`本合同下的商品共有以下${ list.length }种`)
    bounce.show($("#contactbox"));
}

// creator: sy 2022-08-23 渲染本合同下的商品内容
function setContractBN(late,boolSet){
    console.log(late);
    let str = "";
    late = late || [];
    if(late == []){
        $("#contben").html("可供选择的材料共有以下<span>0种</span>");
        return false;
    }
    let item1 = [];
    if(late.data == undefined){
        item1 = late;
    }else{
        item1 = late.data;
    }
    for(let i in item1){
        let atmo = item1 [i];
        var str1 = ``;
        var band = item1.length;
        str1 += `可供选择的材料共有以下<span>${band}种</span>`;
        $("#contben").html(str1);
        str +=`
            <tr>
                <td style="overflow:inherit">
                    <div class="controlTd" id="dettlebeffoe">${handleNull(atmo.code)}<span class="hd">${JSON.stringify(atmo)}</span></div>
                </td>
                <td>${handleNull(atmo.name)}</td>
                <td>${handleNull(atmo.model)}</td>
                <td>${handleNull(atmo.specifications)}</td>
                <td>${handleNull(atmo.unit)}</td>
        </tr>`
    }
    $("#contactbox table tr:gt(0)").remove();
    $("#contactbox table").append(str);
    bounce.show($("#contactbox"));
}

// creator:sy 2022-4-28 日历显示
function signingtime(){
    console.log("可以么");
}

// creator: 李玉婷 2024-08-20 编辑合同确定
function editContractOk(num) {
    let type = $("#newContractInfo").data('type'); // new新增 update修改 cRenewal 续约
    let source = $("#newContractInfo").data('source');
    if(num === 0){ // 取消, 删除上传的文件
        bounce_Fixed.cancel()
        let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
        if(fileImArr.length > 0){
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }
        let file2= $("#newContractInfo .fileCon2 .fileIm")
        if(file2.length > 0){
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }

    }else{ // 确定
        let info = {}
        info.cNo = $("#newContractInfo .cNo").val()

        info.cSignDate = $("#newContractInfo .cSignDate").val()
        info.cStartDate = $("#newContractInfo .cStartDate").val()
        info.cEndDate = $("#newContractInfo .cEndDate").val()

        if(info.cNo.length === 0){
            layer.msg('请录入合同编号！');
            return false
        }
        if(!info.cStartDate || !info.cEndDate) {
            layer.msg('请选择合同的有效期！');
            return false
        }
        info.cMemo = $("#newContractInfo .cMemo").val()
        if(info.cStartDate && info.cEndDate){
            let start = Date.parse(new Date( info.cStartDate));
            let end = Date.parse(new Date( info.cEndDate));
            if(start > end){
                layer.msg('合同有效期开始时间应早于结束时间');
                return false
            }
        }

        info.fileCon1 = [];
        $("#newContractInfo .fileCon1 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon1.push(JSON.parse(itemf))
        })
        info.fileCon2 = [];
        $("#newContractInfo .fileCon2 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon2.push(JSON.parse(itemf))
        })
        info.goodList = [];
        $("#newContractInfo .goodList .gsIm").each(function () {
            let itemg = $(this).find(".hd").html();
            info.goodList.push(JSON.parse(itemg))
        })
        let productListTY = $("#newContractInfo").data('productListTY') || []
        let editProductTY = productListTY.filter(item => item.isChecked)
        let tyGoods = editProductTY.map(item => {return {'material': item.id}})
        let url = '', data = { }
        let filePath = '',fileName = '';
        if( info.fileCon2.length > 0  ){
            filePath = info.fileCon2[0].filename
            fileName = info.fileCon2[0].originalFilename
        }
        let imgs = []
        if(info.fileCon1 && info.fileCon1.length > 0){
            info.fileCon1.forEach(function(im, index){
                let imgItem = {
                    uplaodPath: im.filePath || im.uplaodPath,
                    type: 1, // 类型:1-图片,2-视频,3-文档
                    title: im.title
                }
                if (type === 'update') {
                    if (im.id) {
                        imgItem.id = im.id
                        imgItem.operation = 4 // 标签 1 用于表示新增 4 表示没动 2表示删除  ， 此处只有修改的时候需要传变化（即operation不是1的时候都需要传id）
                    } else {
                        imgItem.operation = 1
                    }
                } else {
                    imgItem.operation = 1
                }
                imgs.push(imgItem)
            })
        }
        let cusInfo  = '', supplierId = ''
        if (source === 'addFixed') {
            cusInfo = JSON.parse($("#supplier").val())
        } else {
            let edit_supplier = JSON.parse($("#edit_supplier").val())
            cusInfo = edit_supplier.supplier
        }
        supplierId = cusInfo.id;
        data = {
            supplier: supplierId,
            sn: info.cNo,
            contractSignTime: info.cSignDate,
            contractStartTime: info.cStartDate,
            contractEndTime: info.cEndDate,
            type: 1, // 1-商品合同
            memo: info.cMemo,
            contractBaseImages: JSON.stringify(imgs),
            mtList: JSON.stringify(tyGoods)
        }
        if( info.fileCon2.length > 0  ){
            data.filePath = filePath
            data.fileName = fileName
        }
        url = '../supplier/insertSupplierContract.do' // 修改过
        $.ajax({
            url: url,
            data: data ,
            success:function (res) {
                let state = res.data.state
                if(state === 1){
                    bounce_Fixed.cancel()
                    layer.msg("操作成功！")
                    let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
                    if(fileImArr.length > 0){
                        let info = JSON.parse(fileImArr.find(".hd").html())  ;
                        let groupUuid = info.groupUuid;
                        cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} )
                    }
                    let file2= $("#newContractInfo .fileCon2 .fileIm")
                    if(file2.length > 0){
                        let info = JSON.parse(file2.find(".hd").html());
                        let groupUuid = info.groupUuid;
                        cancelFileDel({type: 'groupUuid',groupUuid: groupUuid})
                    }
                    getContract(supplierId, source);
                } else if (state === 2) {
                    layer.msg("已续约不可修改!")
                } else if (state === 3) {
                    layer.msg("修改的日期不能在上一个合同结束日之前!")
                } else {
                    layer.msg("操作失败")
                }
            }
        })
    }
}
// creator : 2024-9-25 李玉婷 获取三种合同：type String 1-正常 2-终止 3-到期
function getContract(customer, source) {
    $.ajax({
        url: $.webRoot + "/supplier/listContractBase.do",
        data: { supplierId: customer, type: 1 },
        success:function (res) {
            let data = res.data || []
            let list = data.contractBaseList
            let str = ``;
            var code = "";
            var str1 ="<option value='' class='supItem'></option>";
            if(list && list.length>0){
                for(var i=0;i<list.length;i++){
                    var bont =list[i];
                    if(code === bont.sn){
                        str1 +="<option selected value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
                    }else{
                        str1 +="<option value='"+JSON.stringify(bont)+"' class='supItem'>"+bont.sn+"(合同编号)</option>";
                    }
                }
            }
            if (source === 'addFixed') {
                $("#contract").html(str1);
            } else {
                $("#contract1").html(str1);
            }

        }
    })
}

// creator: sy 2022-08-24 从本合同中移出材料弹窗中的确定按钮
function updunteContent(){
    let listEdit = [];
    let chooseGood2 = $("#goodLust").data("chooseGood") || [];
    $("#remoecontact .fa-check-square-o").each(function(i) {
        let data3 = JSON.parse($(this).parent().siblings().children(".controlTd").children(".hd").html());
        listEdit.push(data3);
    });
    for(let c in listEdit){
        let listid = listEdit[c].id;
        for(let d in chooseGood2){
            let chooid = chooseGood2[d].id;
            if(listid === chooid){
                chooseGood2.splice(d,1);
            }
        }
    }
    console.log(chooseGood2);
    let getoname = chooseGood2.map((item,index) =>{
        return item.name;
    });
    let constleng = getoname.length;
    $("#contdown").html(constleng);
    let namestr = getoname.join("、");
    $("#goodLust").html(namestr);
    $("#newcontract").css("top","-5.5px");
    bounce.cancel($("#remoecontact"));
    bounce.show($('#newcontract'));
}

// creator: sy 2022-08-23  查看定点信息中新增合同
function cgaddctact3(num){
    $("#newcontract").data("type","new");
    let type = $("#newcontract").data("type");
    if(num === 0) {//删除新增合同弹窗中上传的文件
        let fileImArr = $("#newcontract #fileCon1-1 .fileIm");
        if (fileImArr.length > 0) {
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid', groupUuid: groupUuid}, true);
        }
        let file2 = $("#newcontract #fileCon2-1 .fileIm");
        if (file2.length > 0) {
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid', groupUuid: groupUuid}, true);
        }
        bounce.cancel();
        bounce.show($('#fixedScan'));
    }else{//点击确定按钮
        let infofi = {};
        infofi.cNo = $("#newcontract .cNo").val();
        if(infofi.cNo.length === 0){
            layer.msg("请录入合同编号");
            return false;
        }
        infofi.cSignDatecon = $("#newcontract .cSignDate").val();
        infofi.cStartDatecon = $("#newcontract .cStartDate").val();
        infofi.cEndDatecon = $("#newcontract .cEndDate").val();
        infofi.cMemo = $("#newcontract .cMemo").val();
        infofi.fileCon1 = [];
        $("#newcontract #fileCon1-1 .fileIm").each(function(){
            let itemf = $(this).find(".hd").html();
            infofi.fileCon1.push(JSON.parse(itemf));
        });
        infofi.fileCon2 = [];
        $("#newcontract #fileCon2-1 .fileIm").each(function(){
            let itemf = $(this).find(".hd").html();
            infofi.fileCon2.push(JSON.parse(itemf));
        });
        console.log(infofi);
        let url = "";
        var utbo = $("#cgaddcontact3").data("box") ;
        var utid = utbo.supplier_id; //供应商id
        let filePath = '';
        if(infofi.fileCon2.length >0){
            filePath = infofi.fileCon2[0].filename;
        };
        let imgs= [];
        if(infofi.fileCon1 && infofi.fileCon1.length>0){
            infofi.fileCon1.forEach(function(im,index){
                imgs.push({
                    "filePath":im.filename,
                    "order":index,
                    "type":"1",
                    "title":im.originalFilename
                })
            })
        }
        let contractBase = {
            "id":utid,
            "sn":infofi.cNo,
            "signTime":infofi.cSignDatecon,
            "validStart":infofi.cStartDatecon,
            "validEnd":infofi.cEndDatecon,
            "memo":infofi.cMemo,
            "filePath":filePath,
            "fileName":filePath,
            "contractBaseImages":imgs,
            "mtList":[]
        }
        var jhf = {
            'sn':contractBase.sn,
            'signTime':contractBase.signTime,
            'validStart':contractBase.validStart,
            'validEnd':contractBase.validEnd,
            'filePath':contractBase.filePath,
            'fileName':contractBase.fileName,
            'memo':contractBase.memo,
            'contractBaseImages':contractBase.contractBaseImages,
            'mtList':contractBase.mtList
        };
        jhf = JSON.stringify(jhf);
        if(type =="new"){
            url="../supplier/addContractBase.do";
            $.ajax({
                url:url,
                data:{
                    supplierId:utid,
                    contractBase:jhf
                },
                success:function(res){
                    let status = res.success;
                    if(status === "1"|| status === 1) {
                        layer.msg("新增成功");
                        bounce.cancel($("#newcontract"));
                        bounce.show($("#fixedScan"));
                        var type = 1;
                        urgnistad(type ,editObj);
                    }
                }
            })
        }
    }
}

// creator:sy 2022-07-18 添加本材料的定点供应商弹窗中的新增合同
function cgaddctact(num){
    $("#newcontract").data("type","new");
    let type = $("#newcontract").data("type");
    if(num === 0){//删除新增合同弹窗中上传的文件
        let fileImArr = $("#newcontract #fileCon1-1 .fileIm");
        if(fileImArr.length>0){
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type:'groupUuid',groupUuid:groupUuid},true);
        }
        let file2 = $("#newcontract #fileCon2-1 .fileIm");
        if(file2.length>0){
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type:'groupUuid',groupUuid:groupUuid},true);
        }
        bounce.cancel();
        bounce.show($('#addMtSup'));
    }else{//点击确定按钮
        let infofi = {};
        infofi.cNo = $("#newcontract .cNo").val();
        if(infofi.cNo.length === 0){
            layer.msg("请录入合同编号");
            return false;
        }
        infofi.cSignDatecon = $("#newcontract .cSignDate").val();
        infofi.cStartDatecon = $("#newcontract .cStartDate").val();
        infofi.cEndDatecon = $("#newcontract .cEndDate").val();
        infofi.cMemo = $("#newcontract .cMemo").val();
        infofi.fileCon1 = [];
        $("#newcontract #fileCon1-1 .fileIm").each(function(){
            let itemf = $(this).find(".hd").html();
            infofi.fileCon1.push(JSON.parse(itemf));
        });
        infofi.fileCon2 = [];
        var box = $("#newcontract .fileCon2 .fileIm").html();
        if (box == undefined || box == null) {
            infofi.fileCon2 = [];
        }else{
            $("#newcontract #fileCon2-1 .fileIm").each(function(){
                let itemf = $(this).find(".hd").html();
                infofi.fileCon2.push(JSON.parse(itemf));
            });
        }
        let url = "";
        var utid = $("#newcontract").data("id");    //供应商id
        let filePath = '';
        if(infofi.fileCon2.length >0){
            filePath = infofi.fileCon2[0].filename;
        };
        let imgs= [];
        if(infofi.fileCon1 && infofi.fileCon1.length>0){
            infofi.fileCon1.forEach(function(im,index){
                imgs.push({
                    "filePath":im.filename,
                    "order":index,
                    "type":"1",
                    "title":im.originalFilename
                })
            })
        }
        let contractBase = {
            "id":utid,
            "sn":infofi.cNo,
            "signTime":infofi.cSignDatecon,
            "validStart":infofi.cStartDatecon,
            "validEnd":infofi.cEndDatecon,
            "memo":infofi.cMemo,
            "filePath":filePath,
            "fileName":filePath,
            "contractBaseImages":imgs,
            "mtList":[]
        }
        var jhf = {
            'sn':contractBase.sn,
            'signTime':contractBase.signTime,
            'validStart':contractBase.validStart,
            'validEnd':contractBase.validEnd,
            'filePath':contractBase.filePath,
            'fileName':contractBase.fileName,
            'memo':contractBase.memo,
            'contractBaseImages':contractBase.contractBaseImages,
            'mtList':contractBase.mtList
        };
        jhf = JSON.stringify(jhf);
        if(type =="new"){
            url="../supplier/addContractBase.do";
            $.ajax({
                url:url,
                data:{
                    supplierId:utid,
                    contractBase:jhf
                },
                success:function(res){
                    let status = res.success;
                    if(status === "1"|| status === 1) {
                        layer.msg("新增成功");
                        bounce.cancel($("#newcontract"));
                        bounce.show($("#addMtSup"));
                        var type = 1;
                        ontractsigned(type ,editObj);
                    }
                }
            })
        }
    }
}

// creator: sy 2022-07-18 关闭“本合同下的材料”弹窗
function closedown(){
    bounce.cancel($("#contactbox"));
    $("#newcontract").css("top","40px");
    bounce.show($("#newcontract"));
}

// creator: sy 2022-08-24 返回到合同页面
function backcontant(){
    $("#newcontract").css("top","40px");
    bounce.show($('#newcontract'));
}

// creator: sy 2022-08-30 比例不确定
function uncertainty(type,thisObj){
    setRadioSelect("uncertainty",[1,0],type,thisObj);
    if(type == 1){
        // $("#e_gRate1").attr("disabled","disabled");
        // $("#e_gRate1").css("background","rgb(221, 221, 221)");
        $("#e_gRate1").val("");
    }else{
        $("#contract").removeAttr("disabled");
        $("#e_gRate1").css("background","white");
        $("#uncertainty1").attr("class","fa fa fa-circle-o");
    }
}

// creator:sy 2022-08-30 设置输入框数字
function clearNoNum1(input){
    if(input.value != ""){
        input.value=input.value.replace(/[^\d]/g,'');   //只能输入整数
        input.value=input.value.substring(0,8);     //截取输入数字的前八位，使只能输入八位数字
    }
}

// creatory:sy 2022-11-11 设置输入框数字2
function clearNoNum2(obj){
    obj.value = obj.value.replace(/[^\d.]/g,"");//清除“数字”和“.”以外的字符
    obj.value=obj.value.replace(/\.{2,}/g,".");//只保留第一个.清除多余的
    obj.value=obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    obj.value=obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
    if(obj.value.indexOf(".")<0&&obj.value!=""){   //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于01、02
        obj.value=parseFloat(obj.value);
    }
}

// creator: sy 2022-12-16 数字设定(鼠标失去焦点判定)
function clear0(obj){
    var vane = obj.val();   //输入框中的值
    if(vane == ""){
        obj.val(vane);
    }
    else if(vane == "0"){
        obj.val(0.01);
    }else if(vane > 100 || vane == 100){
        obj.val(99.99);
    }else{
        obj.val(vane);
    }
}
// creator:lyt 2022-12-11 新增税率
function addTaxRate(obj){
    $("#taxRateVal").val("");
    $("#addTaxRate").data("obj", obj)
    bounce_Fixed.show($("#addTaxRate"));
}
// creator:lyt 2022-12-13 新增税率确定
function addTaxRatetOk(){
    let val = $("#taxRateVal").val();
    if (val !== "") {
        var isEdit = $("#isEdit").val()
        let supplier = ``;
        if(isEdit === "1"){
            supplier = $("#fixedScan").data("mt_sup");
        } else {
            var band = $("#supplier").val();
            band = JSON.parse(band);
            supplier = band.id
        }
        $.ajax({
            "url":"../supplier/addSupplierInvoice.do",
            "data":{ 'supplier': supplier, 'invoiceCategory': 1, 'taxRate': val },
            success:function (res) {
                if (res.success === 1) {
                    let data = res.data;
                    let obj = $("#addTaxRate").data("obj")
                    let option = ` <span class="funBtn" data-val="${data.id}" data-fun="setTaxRateVal"><span>${val}%</span><span onclick="delTaxRate($(this), event)">删除</span></span>`
                    obj.siblings(".taxRateBody").find(".taxRateList").append(option);
                    bounce_Fixed.cancel();
                }
            }
        });
    }
}
// creator:lyt 2022-12-16 获取列表
function getSupplierTaxRate(obj){
    var isEdit = $("#isEdit").val()
    let supplier = ``;
    if(isEdit === "1"){
        let info = JSON.parse($("#edit_supplier").val());
        supplier = info.supplier.id;
    } else {
        var band = $("#supplier").val();
        band = JSON.parse(band);
        supplier = band.id
    }
    obj.html("");
    $.ajax({
        "url":"../supplier/getSupplierInvoices.do",
        "data":{ 'id': supplier },
        success:function (res) {
            let list = res.data || [];
            let option = `<span class="funBtn" data-val="" data-fun="setTaxRateVal"><span></span></span>`;
            list.forEach(function (item) {
                option += ` <span class="funBtn" data-val="${item.id}" data-fun="setTaxRateVal"><span>${item.taxRate}%</span><span onclick="delTaxRate($(this), event)">删除</span></span>`
            })
            obj.append(option);
            bounce_Fixed.cancel();
        }
    });
}
function delTaxRate(obj, et){
    et.stopPropagation();
    let val = obj.parent().data("val");
    $.ajax({
        "url":"../supplier/deleteSupplierInvoice.do",
        "data":{ 'id': val },
        success:function (res) {
            let state = res.success;
            if (state === 1) {
                obj.parent().remove();
                layer.msg("删除成功");
            } else {
                layer.msg("不能删除已被使用的税率！");
            }
        }
    });
}
function taxRateFun(obj){
    obj.siblings(".taxRateList").toggle();
}
function setTaxRateVal(obj){
    let id = obj.data("val")
    let val = obj.find("span").eq(0).html();
    obj.parents(".taxRateList").toggle();
    obj.parents(".taxRateList").siblings("input[type='hidden']").val(id);
    obj.parents(".taxRateList").siblings("input[type='text']").val(val);
}
// creator:lyt 2022-12-13 管理交货地点
function manageAddress(){
    var isEdit = $("#isEdit").val()
    var idStr = "#"
    if(isEdit === "1"){
        idStr = "#edit_"
    }
    idStr += 'containYunFee' ;
    let type = $(idStr).val()
    $("#chooseDelivery").data("type",type)
    $(".placeList").html("");
    if (type === '1') {
        getReceiveAddressList(1,1)
        $("#chooseDelivery .funTtl").html("新增收货地址");
    } else if (type === '2'){
        $("#chooseDelivery .funTtl").html("新增到货区域");
        getReceiveAddressList(2,1)
    }
    $("#chooseDelivery .care"+ type).show().siblings().hide();
    $("#chooseDelivery").data("type", type);
    bounce_Fixed.show($("#chooseDelivery"));
}

// creator: lyt 2022-11-29 收货地点列表
function getReceiveAddressList(type, abled) {//abled=0-停用状态 1-启用状态
    $.ajax({
        "url":"../dac/list",
        "data":{ 'type': type, 'enable': abled },
        success:function (res) {
            if(res.success !== 200){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data']['data'] || [], str="", areaStr = ``, temp = ``;
            list.forEach((item)=>{
                temp =  ` <li>
                                   <i class="fa fa-square-o"></i>
                                   <span>${item.address}</span>
                                   <span class="hd">${JSON.stringify(item)}</span>
                               </li>`;
                if (item.type === 1) {
                    str += temp;
                } else if (item.type === 2) {
                    areaStr += temp;
                }
            })
            $(".receiveAddress").html(JSON.stringify(list));
            if (type === 1) {
                $(".placeList").html(str);
            } else {
                $(".placeList").html(areaStr);
            }
        }
    });
}
// creator: lyt 2022-12-15 管理交货地点勾选
function chooseDeliveryOk(){
    var isEdit = $("#isEdit").val()
    var idStr = "#"
    if(isEdit === "1"){
        idStr = "#edit_"
    }
    idStr += 'containYunFee' ;
    let type = $(idStr).val()
    bounce_Fixed.cancel()
    let address = ``, data = [], ids = ``;
    $(".placeList .fa-check-square-o").each(function (){
        let info = JSON.parse($(this).siblings(".hd").html())
        address += info.address + ',';
        data.push(info.id)
    })
    if (address !== "") {
        address = address.substring(0, address.length-1);
        ids = data.join(",")
    }
    if (type === '1'){
        $(idStr).parents(".priceInfo").find(".manageAddressList").html(address).siblings(".hd").html(ids);
    } else if (type === '2') {
        $(idStr+ 2).parents(".priceInfo").find(".manageAreaList").html(address).siblings(".hd").html(ids);

    }
}
/*creator:lyt 2022/11/14 0014 下午 4:46 收货信息*/
function addAddress(){
    let type = $("#chooseDelivery").data("type");
    if (type === '1') {
        $("#newReceiveInfo").data("type", "new")
        bounce_Fixed2.show($("#newReceiveInfo"))
        $("#newReceiveInfo input").val("");
        $("#ReceiveName")
            .val("").data('orgData',"")
            .siblings(".hd").html("") ;
    } else if (type === '2') {
        let source = $("#receiveInfo").data('source')
        $("#newReceiveAreaInfo").data('type', "new");
        //$("#newReceiveAreaInfo").data('source', source);
        bounce_Fixed2.show($("#newReceiveAreaInfo"))
        $("#newReceiveAreaInfo input").val("");
        $("#newReceiveAreaInfo .hd").html("");
        $("#areaName").data('orgData',"");
    }
}
/*creator:lyt 2022/11/14 0014 下午 4:54 收货信息确定*/
function addressAddSure(obj){
    let funType = $("#newReceiveInfo").data("type")
    let source = obj.data("source")
    let abled = true, url = '../dac/add';
    let curObj = $("#newReceiveInfo");
    if (source === 2) {
        curObj = $("#newReceiveAreaInfo");
    }
    curObj.find("input[require]").each(function (){
        let val = $(this).val();
        if (val === "") abled = false;
    })
    if (!abled) {
        layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
        return false;
    }
    let contactInfo = ``;
    if (source === 1) {
        contactInfo = JSON.parse($("#ReceiveName").siblings(".hd").html());
    } else if (source === 2) {
        contactInfo = JSON.parse($("#areaName").siblings(".hd").html());
    }
    var json = {
        'type': source === 1 ? 1: 2,
        'contact': contactInfo.contact ,
        'telephone':contactInfo.telephone
    }
    if (funType === 'update')  {
        url = '../dac/edit';
        json.id = curObj.data("id")
    }
    if (source === 1) {
        json.address = $("#ReceiveAddress").val()
    } else {
        let code = $("#regionCon").siblings(".hd").html();
        code = code.split(',')
        json.address = $("#regionCon").val()
        json.regionCode =  code[code.length - 1];
        json.requirements = $("#requirements").val()
    }
    $.ajax({
        url: url,
        data: json,
        beforeSend:function(){ loading.open() ; },
        success: function (data) {
            var status = data.success;
            if (status === 200) {
                bounce_Fixed2.cancel()
                getReceiveAddressList(source, 1);
            } else {
                layer.msg("新增失败！");
            }
        }
    })
}

// creator: sy 2023-01-12 鼠标获得焦点
function outover(obn){
    $("#uncertainty1").attr("class","fa fa fa-circle-o");
}
// creator: hxz 2020-12-10 新增联系人
function addContactInfo(num) {
    var type = "", source = "";
    if(num === 2){
        type = 'new';
        source = 'addAddress';
    }else if(num === 4){
        type = 'new';
        source = 'addArea';
    }
    //var customerId = $("#updateCustormPanel").data("id");
    //$("#newContectInfo").data('level',2);
    $("#newContectInfo").data('type',type);
    $("#newContectInfo").data('source', source);
    document.getElementById('newContectData').reset();
    $(".otherContact").html("");
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    if($(".contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
    }
    //$("#newContectInfo").data("id", customerId);
    $("#addMoreContact").hide();
    $("#uploadCard").html("")
    initCardUpload($("#uploadCard"));
    bounce_Fixed3.show($("#newContectInfo"));
    setTimer('updateContact');
}
// creator: lyt 2022-11-29 收货人那里的新增收货人
function chooseCusContactOk() {
    let selectObj = $("#chooseCusContact").find(".fa-dot-circle-o");
    if(selectObj.length > 0){
        let strInfo = selectObj.siblings("span.hd").html();
        let info = JSON.parse(strInfo);
        $($("#target").val())
            .val(selectObj.next().html()).data('orgData',selectObj.next().html())
            .siblings(".hd").html(strInfo) ;
        bounce_Fixed3.cancel();
    }else layer.msg('请先选择人员')
}
// creator: hxz 2020-12-10 新增联系人确定
function addContactOk(){
    var arr = []
    var data = {
        'name': $("#contactName").val(),
        'post': $("#position").val(),
        'telephone': $("#contactNumber").val(),
        'cardPath': ''
    };
    if($("#contactsCard .bussnessCard").length > 0){
        data.cardPath = $("#contactsCard .bussnessCard .filePic").data('path');
    }
    if($(".otherContact li").length > 0){
        $(".otherContact li").each(function () {
            if ($(this).find('input').val() != '') {
                var json = {
                    'code': $(this).find("input").val(),
                    'type': $(this).find("input").data('type'),
                    'name': $(this).find("input").data('name')
                };
                arr.push(json);
            }
        })
    }
    let param = {
        "deliveryContact": data,
        "contactSocials": arr
    }
    $.ajax({
        url: '../dac/contact/add',
        "method": "POST",
        "timeout": 100000,
        "headers": {
            "Content-Type": "application/json"
        },
        data: JSON.stringify(param),
        success: function (res) {
            data.id = res.data.deliveryContact.id;
            var status = res.success;
            if (status === '200' || status === 200) {
                layer.msg('新增成功')
                var groupUuidArr = []
                $("#newContectInfo [groupUuid]").each(function () {
                    groupUuidArr.push({
                        type: 'groupUuid',
                        groupUuid: $(this).attr("groupUuid")
                    })
                })
                cancelFileDel(groupUuidArr)
                // 给当前的赋值
                data.contact = data.name
                if ($("#newReceiveInfo").is(":visible")) {
                    $("#ReceiveName")
                        .val(data.name).data('orgData', data.name)
                        .siblings(".hd").html(JSON.stringify(data));
                } else  if ($("#newReceiveAreaInfo").is(":visible")){
                    $("#areaName")
                        .val(data.name).data('orgData',data.name)
                        .siblings(".hd").html(JSON.stringify(data)) ;
                }
                bounce_Fixed3.cancel();
            } else {
                layer.msg("新增失败！");
            }
        }
    })
}
// creator: 李玉婷，2019-09-04 18:55:15，添加
function addMore (obj){
    obj.next("select").show();
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContact li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li>' +
                '<span class="sale_ttl1">手机：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="1" data-name="手机" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '2':
            html +=
                '<li>' +
                '<span class="sale_ttl1">QQ：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '3':
            html +=
                '<li>' +
                '<span class="sale_ttl1">Email：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '4':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微信：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '5':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微博：</span>' +
                '<span class="sale_gap"><input type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '9':
            $("#useDefinedLabel input").val("");
            bounce_Fixed4.show($("#useDefinedLabel"));
            setTimer('useDefinedLabel');
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-07 08:50:59，删除添加的联系方式
function removeAdd(obj) {
    obj.parent("li").remove();
    if ($(".otherContact li").length > 0) {
        $(".otherContact li").each(function () {
            var val = $(this).find("input").val();
            var type = $(this).find("input").data('type');
            if (type == '9' || type == '9') type = 6
            if (val == '') {
                $("#addMoreContact option").eq(type).prop('disabled', true);
            }else {
                $("#addMoreContact option").eq(type).prop('disabled', false);
            }
        })
    }else{
        $("#addMoreContact option").prop('disabled', false);
    }
}
// creator: 李玉婷，2019-09-04 19:31:34，删除名片
function cancleCard(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    $('#uploadCard').show();
}
// creator: hxz 2020-12-10 获取联系人列表
function getCusContactList() {
    $.ajax({
        "url":"../dac/contact/list",
        success:function (res) {
            let list = res['data'] || [] ;
            setCusListStr(list, 'updateCustomer')
        }
    });
}
function setCusListStr(list) {
    let str="";
    if(list.length === 0){
        $("#chooseCusContact .p0").show()
        $("#chooseCusContact .p1").hide()
        return false
    }
    $("#chooseCusContact .p0").hide()
    $("#chooseCusContact .p1").show()
    for(let i in list){
        let item = list[i];
        item.contact = item.name
        str += `<li>
                    <i class="fa fa-circle-o"></i>
                    <span>${item.name}</span>
                    <span>${item.post && item.post.substr(0,8)}</span>
                    <span>${item.telephone}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                    <span class="linkBtn ty-right" data-id="${item.id}" onclick="seeContactDetail($(this))">查看</span>
                </li>`;
    }
    $("#chooseCusContact .cusList").html(str);
}
// creator: lyt 2022-11-30 联系人查看
function seeContactDetail(obj){
    $.ajax({
        url : "../dac/contact/detail" ,
        data : {
            'id': obj.data('id')
        },
        success:function(data){
            var info = data['data']['contact'];
            var socialList = data['data']['socials'] || [];
            let allStr = ``;
            $("#see_contactName").html(info.name);
            $("#see_position").html(info.post);
            if(socialList.length > 0){
                let sortList = [];
                for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
                for(var r in socialList){
                    let item = socialList[r];
                    let _index = Number(item.type);
                    sortList[_index].push(item);
                }
                let sortAfter = [];
                for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
                for(var t in sortAfter){
                    let item = sortAfter[t];
                    if(t%2===0){
                        allStr += `<tr><td>${item.name}</td><td>${item.code}</td>`
                    }else{
                        allStr += `<td>${item.name}</td><td>${item.code}</td></tr>`
                    }
                }
                if(sortAfter.length % 2 !== 0){
                    allStr += `<td> </td><td> </td></tr>`
                }
            }
            $(".see_otherContact").html(allStr);
            bounce_Fixed4.show($("#contactSeeDetail"));
        }
    });
}
// creator: 李玉婷，2019-09-16 16:26:52，定时器--新增验证
function setTimer (timer) {
    switch (timer) {
        case 'updateContact':
            bounce_Fixed3.everyTime('0.5s','updateContact',function () {
                var state = 0, filledNum = 0,otherContactLen = 0;
                var contactsCard = $("#contactsCard").data('org');
                var imgCard = $("#contactsCard .filePic").data('path');
                var len = $(".otherContact").data('length');
                $("#addMoreContact option").prop('disabled', false);
                if ($(".otherContact li").length > 0) {
                    $(".otherContact li").each(function () {
                        var val = $(this).find("input").val();
                        var type = $(this).find("input").data('type');
                        if (type == '9' || type == '9') type = 6
                        if (val == '') {
                            $("#addMoreContact option").eq(type).prop('disabled', true);
                        }else {
                            otherContactLen++;
                        }
                    })
                }
                if (len !=  otherContactLen) state ++;
                $("#newContectData [require]:visible").each(function(){
                    if ($(this).val() != '') filledNum++;
                    if($(this).val() != $(this).data('org')){
                        state ++;
                    }
                });
                if(contactsCard != imgCard) state ++;
                if(filledNum > 0 && state > 0){
                    $("#addContact").prop("disabled",false);
                }else{
                    $("#addContact").prop("disabled",true);
                }
            });
            break;
    }
}
// creator: 李玉婷，2019-09-17 15:09:14，名片上传
function initCardUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        itemTemplate: '',
        formData:{
            module: '采购',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var path = data.filename //路径（包含文件类型）
            obj.parent().attr("groupUuid", data.groupUuid )
            var imgStr =
                '<div class="bussnessCard">' +
                '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '   <span fileUid="' + data.fileUid + '" class="ty-color-blue" onclick="cancleCard($(this))">删除</span> ' +
                '</div>';
            $('#uploadCard').hide();
            $('#uploadCard').before(imgStr);
        }
    });
}
// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else {
        console.log("未定义的状态")
    }
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = []
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
}

laydate.render({elem: '#e_gCompactExpire',});
laydate.render({elem: '#e_gCompactSignDay',});
laydate.render({elem:'.cSignDate',format:'yyyy-MM-dd'});
laydate.render({elem:'.cStartDate',format:'yyyy-MM-dd'});
laydate.render({elem:'.cEndDate',format:'yyyy-MM-dd'});
