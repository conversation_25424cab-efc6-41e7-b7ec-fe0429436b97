var specialIndex = 0;
$(function(){
    showMainCon(1);
    getMainData();
    // 保存现金流量option
    getCashFlowOption();
    $("body").on("click",".fun-btn",function(){
        var type = $(this).data("type");
        switch (type) {
            case 'subjectOperate': // 查看/选择科目
                showMainCon(6);
                $("#secondNav li:eq(0)").click();
                // 获取主列表
                getMainList();
                break;
            case 'noAccountingScan': // 尚未入会计帐的票据
                let icon = $(this).data("icon");
                if (icon === 1) {
                    showMainCon(2);
                    getNotAccounting(1, $(this));
                } else {
                    showMainCon(3);
                    getNotAccounting(2, $(this));
                }
                break;
            case 'markNoAccounting': // 已制作过凭证但选为“不予做账”的
                let iconM = $(this).data("icon");
                if (iconM === 1) {
                    showMainCon(2);
                    getMarkNotAccounting(1, $(this));
                } else {
                    showMainCon(3);
                    getMarkNotAccounting(2, $(this));
                }
                break;
            case 'getTickList':
                showMainCon(4);
                getTickList(1, $(this));
                break;
            case 'getMarkTickList':
                showMainCon(4);
                getTickList(2, $(this));
                break;
            case 'noVoucherCount':
                bounce.show($("#noaccount"));
                $("#noaccount .bothPriodInfo").html($(".lastToNow").html());
                getMarkNotAccountingCount(1);
                break;
            case 'manualsScan': //手动选择的凭证
                let month1 = $("#needMonth").data("month");
                getVoucherByMonth(0, month1);
                break;
            case 'osScan': //系统选择的凭证
                let month2 = $("#needMonth").data("month");
                getVoucherByMonth(2, month2);
                break;
            case 'accountantlsScan': //会计录入的凭证
                let month3 = $("#needMonth").data("month");
                getVoucherByMonth(1, month3);
                break;
            case 'chooseBack':
                showMainCon(1);
                getMainData();
                break;
        }
    });
    $("#secondNav").on("click","li",function(){
        $(this).addClass('ty-active').siblings().removeClass('ty-active');
        let index = $(this).index();
        $("#tblCon .tblCon" + index).show().siblings().hide();
    });
    //点击-新增借方
    $("#choose_addAccount").on("click",".newBorrow",function () {
        var borrowStr =     '<div class="borrowItem">'+
            '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="" class="subjectBorrow" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</span>'+
            '           <span class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>';
        $(this).parents(".borrowItem").after(borrowStr);
        //判断是不是第一组借方（第一组新增完之后第一组没有按钮，非第一组有取消按钮）
        if(!$(this).parents(".borrowItem").hasClass("fixed")){
            $(this).parent(".handle").html('<button class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</button>');
        }else{
            $(this).parent(".handle").html("");
        }

    });
    //取消借方的操作
    $("#choose_addAccount").on("click",".cancelBorrow",function () {
        //如果此取消按钮前面同级有新增按钮，取消后在他的前一个借方加入新增借方按钮
        if($(this).prev().hasClass("newBorrow")){
            $(this).parents(".borrowItem").prev().find(".handle").prepend('<button class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</button> ');
        }
        //指向此借方
        var subjectItem= $(this).parents(".borrowItem");
        //获取借方号
        var subjectNo= subjectItem.find(".bookIcon").next("input").attr("id").substring(0,4);
        //如果删除的这个贷方为1001或1002，则对应删除一个现金流量科目
        if(subjectNo === "1001" || subjectNo === "1002"){
            var specialId = subjectItem.attr("data-to");
            subjectInputObj.parents(".borrowMoreLoans").next(".special").find("."+specialId).remove();
        }
        //删除此借方
        subjectItem.remove();
    });
    //点击-新增贷方 （逻辑同借方，不再赘述）
    $("#choose_addAccount").on("click",".newLoan",function () {
        // language=HTML
        var loanStr =       '<div class="loanItem">'+
            '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="" class="subjectLoan" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span>'+
            '           <span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>';
        $(this).parents(".loanItem").after(loanStr);
        if(!$(this).parents(".loanItem").hasClass("fixed")) {
            $(this).parent(".handle").html('<button class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</button>');
        }else{
            $(this).parent(".handle").html("");
        }

    });
    //取消贷方的操作
    $("#choose_addAccount").on("click",".cancelLoan",function () {
        //如果取消按钮前面有新增按钮，取消后在他的前一个借方加入新增借方按钮
        if($(this).prev().hasClass("newLoan")){
            $(this).parents(".loanItem").prev().find(".handle").prepend('<button class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</button>');
        }
        //指向此贷方
        var subjectItem= $(this).parents(".loanItem");
        //获取贷方号
        var subjectNo= subjectItem.find(".bookIcon").next("input").attr("id").substring(0,4);
        //如果删除的这个贷方为1001或1002，则对应删除一个现金流量科目
        if(subjectNo === "1001" || subjectNo === "1002"){
            var specialId = subjectItem.attr("data-to");
            subjectInputObj.parents(".borrowMoreLoans").next(".special").find("."+specialId).remove();
        }
        //删除此借方
        $(this).parents(".loanItem").remove();
    });

    /* creator：张旭博，2017-05-06 15:16:54，每一集目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容） */
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");

        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right")
            $(this).find("i").eq(0).addClass("fa-angle-down")
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down")
            $(this).find("i").eq(0).addClass("fa-angle-right")
        }

        //点击文件夹后子列表的显隐
        $(this).next().toggle();

    });
    $(".acTable td").on("click",function () {
        var index = $(this).index()+1;
        setPartSubjects(index);
        $(this).siblings("td").removeClass("acActive");
        $(this).addClass("acActive");

    });
    $(".borrowMoreLoans").on("keyup",".mNumber input",function () {
        var price = $(this).find(".price").val();
        var quantity = $(this).siblings("input:not(:disabled)").val();
        var unitPrice = $(this).val();

        if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
        if(quantity === ""){quantity = 0}
        if(unitPrice === ""){unitPrice = 0}

        if(quantity !== 0 && unitPrice !== 0){
            price = parseFloat((quantity * unitPrice).toFixed(2))
            $(this).parent().prev().find(".price").val(price);
        }
    });
    $(".subjectChoose").on("click", "[type='btn']", function () {
        var name = $(this).data('name')
        $("#tblCon").data('selectObj', $(this).parents('tr'))
        // 此传入的kind并非数据来源，showAccountingDetail 方法内的接口返回的kind是真正的数据来源
        // 报销的数据只有货物科目可以修改，其他借贷方科目和金额都不能修改，此逻辑也写在showAccountingDetail内
        switch(name) {
            case 'subject_choose':
                // 科目选择
                bounce.show($("#choose_addAccount"))
                var kind = $(this).parents('tr').data('kind')
                var detailId = $(this).parents('tr').data("id");
                choose_subject()

                showAccountingDetail(detailId, kind);
                break;
        }
    })
    $("#choose_addAccount").on("click", "[type='btn']", function () {
        var name = $(this).data('name')
        switch(name) {
            case 'noAccounting':
                if (!accountantAuth.toChooseRefuse) {
                    $("#errorTip .tipWord").html("您无此操作权限！");
                    bounce.show($("#errorTip"));
                    return false;
                }
                // 不予下账
                var content =   '<div>是否确定对此条数据不予下账？</div>' +
                    '<textarea id="reason" rows="3" style="width: 300px" placeholder="请填写不予下账理由"></textarea></div>';
                bounce_confirm(content, function () {
                    subjectChooseSubmit(name)
                })
                break;
            case 'subjectSubmit':
                if (!accountantAuth.toChooseSubject) {
                    $("#errorTip .tipWord").html("您无此操作权限！");
                    bounce.show($("#errorTip"));
                    return false;
                }
                // 提交
                bounce_confirm('确定提交吗？', function () {
                    subjectChooseSubmit(name)
                })
                break;
        }
    })
    $("#reimburse").on('click', '[type="scan_btn"]', function () {
        var name = $(this).data('name')
        switch (name) {
            case 'billInfo':
                bounce_Fixed2.show($("#billInfo"))
                var reimburseBillId = $(this).parents("tr").attr("id")
                $.ajax({
                    url: "../reimburseWindow/getReimburseBill.do",
                    data: {reimburseBillId: reimburseBillId},
                    success: function (data) {
                        if (data) {
                            var quotaBill = data.financeReimburseBillList[0]
                            var goods= quotaBill.financeReimburseBillItemList
                            var billCatName = quotaBill.billCatName
                            $("#billInfo .billQuantity").hide()
                            $("#billInfo .singleAmount").show()
                            switch (billCatName) {
                                case '增值税专用发票':
                                case '增值税普通发票':
                                    $("#billInfo .VATGood").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice+'</td>' +
                                            '   <td>'+goods[i].price+'</td>' +
                                            '   <td>'+(goods[i].taxRate * 100).toFixed(2) +' %</td>' +
                                            '   <td>'+goods[i].taxAmount+'</td>' +
                                            '   <td>'+goods[i].amount+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .VATGood tbody").html(goodsStr)
                                    var issueDate = quotaBill.issueDate
                                    var quantity = quotaBill.relativeBillQuantity
                                    issueDate = issueDate.substring(0,4) +'年' + issueDate.substring(5,7) + '月'
                                    if (Number(quantity) > 1) {
                                        $("#billInfo .billQuantity").show().html('内容与本票据完全相同，且开票月份也为' + issueDate + '的票据总数量为' + quantity + '张')
                                    }
                                    break;
                                case '定额普通发票':
                                    var goodsStr = ''
                                    $("#billInfo .quotaGood").show().siblings().hide()
                                    $("#billInfo .quotaInvoice .billCatName").html('定额普通发票')
                                    $("#billInfo .quotaInvoice .feeCatName").html(quotaBill.feeCatName)
                                    var amount = 0
                                    var number = 0
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '    <td>'+goods[i].uniPrice+'</td>' +
                                            '    <td>'+goods[i].itemQuantity+'张</td>' +
                                            '    <td>'+goods[i].price+'</td>' +
                                            '</tr>'
                                        amount = parseFloat(amount) + parseFloat(goods[i].price)
                                        number = parseInt(number) + parseInt(goods[i].itemQuantity)
                                    }
                                    goodsStr += '<tr>' +
                                        '    <td>总计</td>' +
                                        '    <td>'+number+'张</td>' +
                                        '    <td>'+amount+'</td>' +
                                        '</tr>'
                                    $("#billInfo .quotaGood tbody").html(goodsStr)
                                    $("#billInfo .singleAmount").hide()
                                    break;
                                case '其他普通发票':
                                    $("#billInfo .otherGood").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice+'</td>' +
                                            '   <td>'+goods[i].price+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .otherGood tbody").html(goodsStr)
                                    var issueDate = quotaBill.issueDate
                                    var quantity = quotaBill.relativeBillQuantity
                                    issueDate = issueDate.substring(0,4) +'年' + issueDate.substring(5,7) + '月'
                                    if (Number(quantity) > 1) {
                                        $("#billInfo .billQuantity").show().html('内容与本票据完全相同，且开票月份也为' + issueDate + '的票据总数量为' + quantity + '张')
                                    }
                                    break;
                                case '收据':
                                    $("#billInfo .receipt").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice+'</td>' +
                                            '   <td>'+goods[i].price+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .receipt tbody").html(goodsStr)
                                    break;
                                default:
                                    console.log('不存在的值')
                            }
                            $("#billInfo .memo").html(quotaBill.memo)
                            $("#billInfo .amount").html(quotaBill.itemAmount)
                        }
                    }
                })
                break;
            case 'billPic':
                if ($(this).data("path") !== ""){
                    seePicture($(this).data("path"));
                } else {
                    layer.msg("无图片");
                }
                break;
        }
    })
});


// creator: 李玉婷，2022-04-28 12:13:38，新凭证管理首页数据
function getMainData() {
    $.ajax({
        url: '../accountant/getVoucherHomeData.do',
        data: {},
        success: function (res) {
            let countType = ``;
            let data = res.data;
            let settleMonth = data.settleMonth.replace('年', '-');
            settleMonth = settleMonth.replace('月', '');
            $("#needMonth").data("month", settleMonth).html(data.settleMonth);
            $(".mainCon1 .countNum").each(function () {
                countType = $(this).data("type");
                $(this).html(data[countType] || 0);
            })
        }
    });
}
// creator: 李玉婷，2022-05-10 08:24:56，尚未入会计帐的票据
function getNotAccounting(num, obj) {
    let title = ``, curObj = null;
    let param = {
        type: num,//1-按年查询  2-按年月查询
        yearDate: $("#needMonth").data("month").substr(0,4)
    }
    if (num === 2) {
        let info = JSON.parse(obj.siblings(".hd").html());
        param.yearDate = info.year.replace('年','')
    }
    $.ajax({
        url: '../data/notRecordedList.do',
        data: param,
        success: function (res) {
            let catStr = ``, icon = 2;
            let list = res.data.numList || [];
            let html = ``, count = 0;
            num === 2 ? catStr = `getTickList`: catStr = `noAccountingScan`;
            for (let i=0;i<list.length;i++){
                count += Number(list[i].num);
                if (num === 2) {
                    icon = list[i].year.replace('年', '-');
                    icon = icon.replace('月', '')
                }
                html +=
                    `<li>
                         <span class="count-it">票据日期为${list[i].year}的共${list[i].num}条</span>
                         <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3 fun-btn" data-type="${catStr}" data-icon="${icon}">查 看</span>
                         <span class="hd">${JSON.stringify(list[i])}</span>
                     </li>`;
            }
            if (num === 1) {
                curObj = $(".mainCon2");
                title = `目前，系统内尚未入会计帐的票据共${count}条，其中`;
            } else {
                let info = JSON.parse(obj.siblings(".hd").html());
                curObj = $(".mainCon3");
                title = `目前，系统内尚未入会计帐、票据日期为${info.year}的票据共${count}条，其中`;
            }
            curObj.find("ul").html(html);
            curObj.find(".tickDetails").html(title);
        }
    });
}
// creator: 李玉婷，2022-05-10 09:24:56，不予做账
function getMarkNotAccounting(num, obj) {
    let title = ``, curObj = null;
    let param = {
        type: num//1-按年查询  2-按年月查询
    }
    if (num === 2) {
        let info = JSON.parse(obj.siblings(".hd").html());
        param.beginYear = info.year.replace('年','')
    }
    $.ajax({
        url: '../data/noAccountingList.do',
        data: param,
        success: function (res) {
            let catStr = ``, icon = 2;
            let list = res.data.numList || [];
            let html = ``, count = 0;
            num === 2 ? catStr = `getMarkTickList`: catStr = `markNoAccounting`;
            for (var i=0;i<list.length;i++){
                count += Number(list[i].num);
                if (num === 2) {
                    icon = list[i].year.replace('年', '-');
                    icon = icon.replace('月', '')
                }
                html +=
                    `<li>
                         <span class="count-it">票据日期为${list[i].year}的共${list[i].num}条</span>
                         <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3 fun-btn" data-type="${catStr}" data-icon="${icon}">查 看</span>
                         <span class="hd">${JSON.stringify(list[i])}</span>
                     </li>`;
            }
            if (num === 1) {
                curObj = $(".mainCon2");
                title = `目前，系统内已选为“不予做账”的票据共${count}条，其中`;
            } else {
                let info = JSON.parse(obj.siblings(".hd").html());
                curObj = $(".mainCon3");
                title = `目前，系统内已选为“不予做账”、票据日期为${info.year}年的票据共${count}条，其中`;
            }
            curObj.find("ul").html(html);
            curObj.find(".tickDetails").html(title);
        }
    });
}
// creator: 李玉婷，2022-05-10 11:24:56，尚未入会计帐的票据详情列表
function getTickList(num, obj) {
    let title = ``, url = `../data/getNotRecordedDetailList.do`;
    var tableBodyStr = '';
    let info = JSON.parse(obj.siblings(".hd").html());
    num === 2? url = `../data/getNoAccountingDetailBillList.do`: "";
    let begin = obj.data("icon") + '-01';
    let end =  new Date(new Date(begin).getFullYear(), new Date(begin).getMonth()+1, 0).format('yyyy-MM-dd');
    $.ajax({
        url: url,
        data: {
            begin: begin,
            end: end
        },
        success: function (res) {
            var detail      = res.data.accountDetails // 会计详情列表
            var billList    = res.data.financeAccountBills // 财务详情列表
            let count       = 0;
            for (var i in detail) {
                count++;
                var billDate = new Date(detail[i].billDate).format("yyyy-MM-dd")//票据日期
                tableBodyStr +=  '  <tr data-id="' + detail[i].id + '" data-kind="1">' +
                    '<td>' + detail[i].summary + '</td>' +          // 摘要
                    '<td>' + (detail[i].credit ? detail[i].billAmount : '--') + '</td>' + // 收入
                    '<td>' + (detail[i].debit ? detail[i].billAmount : '--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (detail[i].billQuantity || '') + '</td>' + // 票据数量
                    '<td>' + billDate+ '</td>' +    // 票据日期
                    '<td>' + handleNull(detail[i].purpose) + '</td>' +  // 用途
                    '<td>' + detail[i].createName+ ' ' + new Date(detail[i].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td title="' + detail[i].memo + '" class="memoLimit">' + handleNull(detail[i].memo) + '</td>' + // 备注
                    '<td><span class="ty-color-blue" onclick="getAccountingDetail('+detail[i].id+', 1)">查看详情</span>' +
                    '</td>' +
                    '</tr>'
            }

            for (var j = 0; j < billList.length; j++) {
                count++;
                tableBodyStr +=     '<tr data-id="' + billList[j].id + '" data-kind="2">' +
                    '<td>' + billList[j].summary + '</td>' +          // 摘要
                    '<td class="income">' + (billList[j].type === '1'?billList[j].billAmount:'--') + '</td>' + // 收入 'type' : 1- 收入 2- 支出
                    '<td class="pay">' + (billList[j].type === '2'?billList[j].billAmount:'--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (billList[j].billQuantity || '--') + '</td>' + // 票据数量
                    '<td>' +  new Date(billList[j].billDate).format("yyyy-MM-dd")+ '</td>' +    // 票据日期
                    '<td>' + handleNull(billList[j].purpose) + '</td>' +  // 用途
                    '<td>' + billList[j].createName+ ' ' + new Date(billList[j].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td title="' + billList[j].memo + '" class="memoLimit">' + handleNull(billList[j].memo) + '</td>' + // 备注
                    '<td><span class="ty-color-blue" onclick="getAccountingDetail('+billList[j].id+', 2)">查看详情</span>' +
                    '</td>' +
                    '</tr>'

            }
            if (num === 1) {
                title = `目前，系统内尚未入会计帐、票据日期为${info.year}的票据共如下${count}条`;
            } else {
                title = `目前，系统内已选为“不予做账”、票据日期为${info.year}的票据共如下${count}条`;
            }
            $(".mainCon4 tbody").html(tableBodyStr);
            $(".mainCon4 .tickDetails").html(title);
        }
    });
}
// ==============科目选择============
// creator: 李玉婷，2022-05-10 13:24:56，获取各种状态的列表
function getMainList() {
    var tableBodyStr = '',updateList = '',noStr = '';
    let month = $("#needMonth").data("month");
    let arr = month.split("-");
    let beginDate = month + "-01";
    let endDate = new Date(arr[0],arr[1],0).format('yyyy-MM-dd');
    let last = Number(arr[0]) -1;
    let lastMonth = new Date(arr[0],Number(arr[1]) -1,0);
    let nearly = {
        "beginYear": last+'-01',
        "endYear": lastMonth.format('yyyy-MM')
    }
    $("#tblCon .nearlyTwoYears").html(JSON.stringify(nearly));
    $("#tblCon .tblCon2 .settleMonth").html($("#needMonth").html());
    $("#tblCon .lastToNow").html(last + '年1月至'+lastMonth.format('yyyy年M月'));
    $.ajax({
        url: '../data/voucherList.do',
        data: {
            beginDate: beginDate,
            endDate: endDate
        },
        success: function (data,status,xhr) {
            var billPeriod = ''
            var detail      = data.data.accountDetails // 会计详情列表
            var billList    = data.data.financeAccountBills // 财务详情列表
            var detailUpdate    = data.data.accountDetailsUpdate // 修改待选择的数据信息
            var billUpdate    = data.data.financeAccountBillsUpdate // 修改待选择的数据信息
            var accountDetailsNo    = data.data.accountDetailsNo // 不予下账的数据信息
            var fcBillsNo    = data.data.financeAccountBillsNo ;
            var nowMonth = new Date(xhr.getResponseHeader('Date')).format("yyyy-MM-dd")
            $("#secondNav li:eq(0) span").html(data.data.numNormal);
            $("#secondNav li:eq(1) span").html(data.data.numUpdate);
            $("#secondNav li:eq(2) span").html(data.data.numNo);
            // 待选择列表
            // 正常待选择
            let billMonthStr = ``, billMonth = ``;
            for (var i in detail) {
                billMonth = new Date(detail[i].billDate).format("yyyy-MM-dd")//票据日期
                billMonthStr = billMonth;
                if (billMonth !== '') {
                    billPeriod = billMonth === nowMonth ? '1' : '2'
                } else {
                    billPeriod = '2'
                }
                var billDetail = {
                    kind: 1,
                    operatorFinance: detail[i].createName, // 创建者
                    operatorName: detail[i].auditorName, // 经手人
                    pricetype: (detail[i].credit?0:1), // 收入或支出
                    summary: detail[i].summary, // 摘要
                    purpose: detail[i].purpose, // 用途
                    bill_quantity: detail[i].billQuantity || 0, // 票据数量
                    bill_peroid: billPeriod, // 票据所属月份
                    price: detail[i].billAmount, //
                    memo: detail[i].memo, //
                    detailId: detail[i].id,
                    billDate: billMonth
                }
                if (detail[i].billCatNameList && detail[i].billCatNameList.findIndex((v)=>{return v==='定额发票'}) !== -1) {
                    billMonthStr = '--';
                }
                tableBodyStr +=  '  <tr data-id="' + detail[i].id + '" data-kind="1">' +
                    '<td>' + detail[i].summary + '</td>' +          // 摘要
                    '<td>' + (detail[i].credit ? detail[i].billAmount : '--') + '</td>' + // 收入
                    '<td>' + (detail[i].debit ? detail[i].billAmount : '--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (detail[i].billQuantity || '') + '</td>' + // 票据数量
                    '<td class="billPeriod" data-time="'+billPeriod+'">' + billMonthStr+ '</td>' +    // 票据所属月份
                    '<td>' + handleNull(detail[i].purpose) + '</td>' +  // 用途
                    '<td>' + detail[i].createName+ ' ' + new Date(detail[i].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td class="memoLimit" title="' + handleNull(detail[i].memo) + '">' + handleNull(detail[i].memo) + '</td>' + // 备注
                    '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                    '<td>' +
                    '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                    '</td>' +
                    '</tr>'
            }
            for (var j = 0; j < billList.length; j++) {
                billMonth = new Date(billList[j].billDate).format("yyyy-MM-dd");
                if (billList[j].billCatNameList && billList[j].billCatNameList.findIndex((v)=>{return v==='定额发票'}) !== -1) {
                    billMonthStr = '--';
                }
                var billDetail = {
                    kind: 2,
                    operatorName: billList[j].createName,
                    pricetype: 0, // 收入或支出
                    summary: billList[j].summary, // 摘要
                    purpose: billList[j].purpose, // 用途
                    bill_quantity: billList[j].billQuantity || 0, // 票据数量
                    bill_peroid: billList[j].billPeriod || 1, // 票据所属月份
                    price: billList[j].billAmount, //
                    memo: billList[j].memo, //
                    bill_detail: billList[j].id, //
                    billDate: billMonth
                }
                tableBodyStr +=     '<tr data-id="' + billList[j].id + '" data-kind="2">' +
                    '<td>' + billList[j].summary + '</td>' +          // 摘要
                    '<td class="income">' + (billList[j].type === '1'?billList[j].billAmount:'--') + '</td>' + // 收入 'type' : 1- 收入 2- 支出
                    '<td class="pay">' + (billList[j].type === '2'?billList[j].billAmount:'--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (billList[j].billQuantity || '--') + '</td>' + // 票据数量
                    '<td class="billPeriod">' +  billMonth+ '</td>' +    // 票据所属月份
                    '<td>' + handleNull(billList[j].purpose) + '</td>' +  // 用途
                    '<td>' + billList[j].createName+ ' ' + new Date(billList[j].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td class="memoLimit" title="' + handleNull(billList[j].memo) + '">' + handleNull(billList[j].memo) + '</td>' + // 备注
                    '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                    '<td>' +
                    '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                    '</td>' +
                    '</tr>'
            }
            $("#tblCon .tblCon0 tbody").html(tableBodyStr);
            for(let a=0;a<detailUpdate.length ;a++){
                billMonth = new Date(detailUpdate[a].billDate).format("yyyy-MM-dd")//票据日期
                if (billMonth !== '') {
                    billPeriod = billMonth === nowMonth ? '1' : '2'
                } else {
                    billPeriod = '2'
                }
                var billDetail = {
                    kind: 1,
                    operatorFinance: detailUpdate[a].createName, // 创建者
                    operatorName: detailUpdate[a].auditorName, // 经手人
                    pricetype: (detailUpdate[a].credit?0:1), // 收入或支出
                    summary: detailUpdate[a].summary, // 摘要
                    purpose: detailUpdate[a].purpose, // 用途
                    bill_quantity: detailUpdate[a].billQuantity || 0, // 票据数量
                    bill_peroid: billPeriod, // 票据所属月份
                    price: detailUpdate[a].billAmount, //
                    memo: detailUpdate[a].memo, //
                    detailId: detailUpdate[a].id, //
                    billDate: billMonth
                }
                updateList +=  '  <tr data-id="' + detailUpdate[a].id + '" data-kind="1">' +
                    '<td>' + detailUpdate[a].summary + '</td>' +          // 摘要
                    '<td>' + (detailUpdate[a].credit ? detailUpdate[a].billAmount : '--') + '</td>' + // 收入
                    '<td>' + (detailUpdate[a].debit ? detailUpdate[a].billAmount : '--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (detailUpdate[a].billQuantity || '') + '</td>' + // 票据数量
                    '<td class="billPeriod" data-time="'+billPeriod+'">' + billMonth+ '</td>' +    // 票据所属月份
                    '<td>' + handleNull(detailUpdate[a].purpose) + '</td>' +  // 用途
                    '<td>' + detailUpdate[a].createName+ ' ' + new Date(detailUpdate[a].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td class="memoLimit" title="' + handleNull(detailUpdate[a].memo) + '">' + handleNull(detailUpdate[a].memo) + '</td>' + // 备注
                    '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                    '<td>' +
                    '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                    '</td>' +
                    '</tr>'
            }

            for (var b = 0; b < billUpdate.length; b++) {
                billMonth = new Date(billUpdate[b].billDate).format("yyyy-MM-dd");
                var billDetail = {
                    kind: 2,
                    operatorName: billUpdate[b].createName,
                    pricetype: 0, // 收入或支出
                    summary: billUpdate[b].summary, // 摘要
                    purpose: billUpdate[b].purpose, // 用途
                    bill_quantity: billUpdate[b].billQuantity || 0, // 票据数量
                    bill_peroid: billUpdate[b].billPeriod || 1, // 票据所属月份
                    price: billUpdate[b].billAmount, //
                    memo: billUpdate[b].memo, //
                    bill_detail: billUpdate[b].id, //
                    billDate: billMonth
                }
                tableBodyStr +=     '<tr data-id="' + billUpdate[b].id + '" data-kind="2">' +
                    '<td>' + billUpdate[b].summary + '</td>' +          // 摘要
                    '<td class="income">' + (billUpdate[b].type === '1'?billUpdate[b].billAmount:'--') + '</td>' + // 收入 'type' : 1- 收入 2- 支出
                    '<td class="pay">' + (billUpdate[b].type === '2'?billUpdate[b].billAmount:'--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (billUpdate[b].billQuantity || '--') + '</td>' + // 票据数量
                    '<td class="billPeriod">' + billMonth + '</td>' +    // 票据所属月份
                    '<td>' + handleNull(billUpdate[b].purpose) + '</td>' +  // 用途
                    '<td>' + billUpdate[b].createName+ ' ' + new Date(billUpdate[b].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td class="memoLimit" title="' + handleNull(billUpdate[b].memo) + '">' + handleNull(billUpdate[b].memo) + '</td>' + // 备注
                    '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                    '<td>' +
                    '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                    '</td>' +
                    '</tr>'
            }
            //去年与今年的“不予下账”
            for (let m=0;m<accountDetailsNo.length ;m++) {
                billMonth = new Date(accountDetailsNo[m].billDate).format("yyyy-MM-dd")//票据日期
                console.log("aa:"+billMonth)
                if (billMonth !== '') {
                    billPeriod = billMonth === nowMonth ? '1' : '2'
                } else {
                    billPeriod = '2'
                }
                var billDetail = {
                    kind: 1,
                    operatorFinance: accountDetailsNo[m].createName, // 创建者
                    operatorName: accountDetailsNo[m].auditorName, // 经手人
                    pricetype: (accountDetailsNo[m].credit?0:1), // 收入或支出
                    summary: accountDetailsNo[m].summary, // 摘要
                    purpose: accountDetailsNo[m].purpose, // 用途
                    bill_quantity: accountDetailsNo[m].billQuantity || 0, // 票据数量
                    bill_peroid: billPeriod, // 票据所属月份
                    price: accountDetailsNo[m].billAmount, //
                    memo: accountDetailsNo[m].memo, //
                    billDate: billMonth,
                    noAccount: true,
                    detailId: accountDetailsNo[m].id //
                }
                noStr +=  '  <tr data-id="' + accountDetailsNo[m].id + '" data-kind="1">' +
                    '<td>' + accountDetailsNo[m].summary + '</td>' +          // 摘要
                    '<td>' + (accountDetailsNo[m].credit ? accountDetailsNo[m].billAmount : '--') + '</td>' + // 收入
                    '<td>' + (accountDetailsNo[m].debit ? accountDetailsNo[m].billAmount : '--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (accountDetailsNo[m].billQuantity || '') + '</td>' + // 票据数量
                    '<td class="billPeriod" data-time="'+billPeriod+'">' + billMonth+ '</td>' +    // 票据所属月份
                    '<td>' + handleNull(accountDetailsNo[m].purpose) + '</td>' +  // 用途
                    '<td>' + accountDetailsNo[m].createName+ ' ' + new Date(accountDetailsNo[m].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td class="memoLimit" title="' + handleNull(accountDetailsNo[m].memo) + '">' + handleNull(accountDetailsNo[m].memo) + '</td>' + // 备注
                    '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                    '<td>' +
                    '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                    '</td>' +
                    '</tr>'
            }
            for (var n = 0; n < fcBillsNo.length; j++) {
                billMonth = new Date(fcBillsNo[n].billDate).format("yyyy-MM-dd");
                var billDetail = {
                    kind: 2,
                    operatorName: fcBillsNo[n].createName,
                    pricetype: 0, // 收入或支出
                    summary: fcBillsNo[n].summary, // 摘要
                    purpose: fcBillsNo[n].purpose, // 用途
                    bill_quantity: fcBillsNo[n].billQuantity || 0, // 票据数量
                    bill_peroid: fcBillsNo[n].billPeriod || 1, // 票据所属月份
                    price: fcBillsNo[n].billAmount, //
                    memo: fcBillsNo[n].memo, //
                    bill_detail: fcBillsNo[n].id, //
                    noAccount: true,
                    billDate: billMonth
                }
                noStr +=     '<tr data-id="' + fcBillsNo[n].id + '" data-kind="2">' +
                    '<td>' + fcBillsNo[n].summary + '</td>' +          // 摘要
                    '<td class="income">' + (fcBillsNo[n].type === '1'?fcBillsNo[n].billAmount:'--') + '</td>' + // 收入 'type' : 1- 收入 2- 支出
                    '<td class="pay">' + (fcBillsNo[n].type === '2'?fcBillsNo[n].billAmount:'--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (fcBillsNo[n].billQuantity || '--') + '</td>' + // 票据数量
                    '<td class="billPeriod">' + billMonth + '</td>' +    // 票据所属月份
                    '<td>' + handleNull(fcBillsNo[n].purpose) + '</td>' +  // 用途
                    '<td>' + fcBillsNo[n].createName+ ' ' + new Date(fcBillsNo[n].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td class="memoLimit" title="' + fcBillsNo[n].memo + '">' + handleNull(fcBillsNo[n].memo) + '</td>' + // 备注
                    '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                    '<td>' +
                    '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                    '</td>' +
                    '</tr>'
            }
            $("#tblCon .tblCon1 tbody").html(updateList);
            $("#tblCon .tblCon2 tbody").html(noStr);
            $("#tblCon .tblCon2 .settleMonthcount").html(data.data.numNoAccounting);
        }
    })
}

// creator: 李玉婷，2022-05-10 09:24:56，不予做账-票据日期在A-1年1月至A年B-1月之间的票据查看
function getMarkNotAccountingCount(num, obj) {
    let param = {
        type: num
    };
    let btnStr = ``, curObj = null;
    if (num === 1) {
        let data = JSON.parse($("#tblCon .nearlyTwoYears").html());
        param.beginYear = data.beginYear.replace('-','');
        param.endYear = data.endYear.replace('-','');
        btnStr = `<span class="ty-right ty-color-blue fun-btn" onclick="getMarkNotAccountingCount(2, $(this))">按月份展示</span>`
    } else {
        let info = JSON.parse(obj.siblings(".hd").html());
        let year = info.year;
        param.beginYear = year.replace('年','');
        param.endYear = year.replace('年','');
    }
    $.ajax({
        url: '../data/noAccountingList.do',
        data: param,
        success: function (res) {
            let list = res.data.numList || [];
            let html = ``, count = 0;
            for (var i=0;i<list.length;i++){
                count += Number(list[i].num);
                html +=
                    `<li>
                         <span class="count-it">${list[i].year}共${list[i].num}条</span>
                         ${btnStr}
                         <span class="ty-right ty-color-blue fun-btn" onclick="byBothPriod(${num},$(this))">查看全部票据</span>
                         <span class="hd">${JSON.stringify(list[i])}</span>
                     </li>`;
            }
            if (num === 1) {
                curObj = $("#noaccount .bothPriod");
                $("#noaccount .bothPriodCount").html(count);
            } else {
                $("#noaccountByMonth .bothPriodInfo").html($(".lastToNow").html());
                $("#noaccountByMonth .bothPriodCount").html(count);
                bounce.show($("#noaccountByMonth"));
                curObj = $(".bothPriodByMonth");
            }
            curObj.find("ul").html(html);
        }
    });
}
// creator: 李玉婷，2022-05-17 09:12:26，查看全部票据
function byBothPriod(num,obj) {
    showMainCon(7);
    var tableBodyStr = '';
    let info = JSON.parse(obj.siblings(".hd").html());
    let param = {
        begin: info.year.replace('年','-') + '01-01',
        end: info.year.replace('年','-') + '12-31'
    };
    if (num === 2) {
        let date = info.year.replace('年','-')
        param.begin = date.replace('月','-') + '01';
        let day = new Date(new Date(param.begin).getFullYear(), new Date(param.begin).getMonth()+1, 0).format('yyyy-MM-dd');
        param.end = day;
    }
    bounce.cancel();
    $.ajax({
        url: '../data/getNoAccountingDetailBillList.do',
        data: param,
        success: function (res,status,xhr) {
            var detail      = res.data.accountDetails // 会计详情列表
            var billList    = res.data.financeAccountBills // 财务详情列表
            var nowMonth = new Date(xhr.getResponseHeader('Date')).format("yyyy-MM-dd")
            let billPeriod = ``;
            for (var i in detail) {
                var billMonth = new Date(detail[i].billDate).format("yyyy-MM-dd")//票据日期
                if (billMonth !== '') {
                    billPeriod = billMonth === nowMonth ? '1' : '2'
                } else {
                    billPeriod = '2'
                }
                var billDetail = {
                    kind: 1,
                    operatorFinance: detail[i].createName, // 创建者
                    operatorName: detail[i].auditorName, // 经手人
                    pricetype: (detail[i].credit?0:1), // 收入或支出
                    summary: detail[i].summary, // 摘要
                    purpose: detail[i].purpose, // 用途
                    bill_quantity: detail[i].billQuantity || 0, // 票据数量
                    bill_peroid: billPeriod, // 票据所属月份
                    price: detail[i].billAmount, //
                    memo: detail[i].memo, //
                    bill_detail: null, //
                    noAccount: true,
                    detailId: detail[i].id //
                }
                tableBodyStr +=  '  <tr data-id="' + detail[i].id + '" data-kind="1">' +
                    '<td>' + detail[i].summary + '</td>' +          // 摘要
                    '<td>' + (detail[i].credit ? detail[i].billAmount : '--') + '</td>' + // 收入
                    '<td>' + (detail[i].debit ? detail[i].billAmount : '--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (detail[i].billQuantity || '') + '</td>' + // 票据数量
                    '<td class="billPeriod" data-time="'+billPeriod+'">' + billMonth+ '</td>' +    // 票据所属月份
                    '<td>' + handleNull(detail[i].purpose) + '</td>' +  // 用途
                    '<td>' + detail[i].createName+ ' ' + new Date(detail[i].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td class="memoLimit" title="' + detail[i].memo + '">' + detail[i].memo + '</td>' + // 备注
                    '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                    '<td>' +
                    '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                    '</td>' +
                    '</tr>'
            }
            for (var j = 0; j < billList.length; j++) {
                var billDetail = {
                    kind: 2,
                    operatorName: billList[j].createName,
                    pricetype: 0, // 收入或支出
                    summary: billList[j].summary, // 摘要
                    purpose: billList[j].purpose, // 用途
                    bill_quantity: billList[j].billQuantity || 0, // 票据数量
                    bill_peroid: billList[j].billPeriod || 1, // 票据所属月份
                    price: billList[j].billAmount, //
                    memo: billList[j].memo, //
                    bill_detail: billList[j].id, //
                    noAccount: true,
                    detailId: null //
                }
                tableBodyStr +=     '<tr data-id="' + billList[j].id + '" data-kind="2">' +
                    '<td>' + billList[j].summary + '</td>' +          // 摘要
                    '<td class="income">' + (billList[j].type === '1'?billList[j].billAmount:'--') + '</td>' + // 收入 'type' : 1- 收入 2- 支出
                    '<td class="pay">' + (billList[j].type === '2'?billList[j].billAmount:'--') + '</td>' +  // 支出
                    '<td class="billQuantity">' + (billList[j].billQuantity || '--') + '</td>' + // 票据数量
                    '<td class="billPeriod">' +  new Date(billList[j].billDate).format("yyyy-MM-dd")+ '</td>' +    // 票据所属月份
                    '<td>' + handleNull(billList[j].purpose) + '</td>' +  // 用途
                    '<td>' + billList[j].createName+ ' ' + new Date(billList[j].createDate).format("yyyy-MM-dd hh:mm:ss")+ '</td>' +  // 经手人
                    '<td class="memoLimit" title="' + billList[j].memo + '">' + billList[j].memo + '</td>' + // 备注
                    '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                    '<td>' +
                    '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                    '</td>' +
                    '</tr>'
            }
            title = `以下为票据日期为${info.year}、已选择为“不予下账”的票据，共${info.num}条`;
            $(".mainCon7 tbody").html(tableBodyStr);
            $(".mainCon7 .tickDetails").html(title);
            $(".mainCon7 .settleMonth").html($("#settleMonth").html());
        }
    });
}
