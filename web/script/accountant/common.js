/**
 * Created by 侯杏哲 on 2017/1/20.
 *  处理员工登陆各页面后的权限
 */

/* creator：侯杏哲 ，2017/1/20 ，用于识别会计人员的权限 */
var AccountantAuth = function (){
    var authStr = $("#auth").html();
    // console.log(authStr) ;
    if(authStr != ""){
        console.log("新增科目sj:" + authStr.indexOf("sj") );
        console.log("修改科目sk:" + authStr.indexOf("sk") );
        console.log("修改状态sz:" + authStr.indexOf("sz") );
        console.log("待选择 - 查看- sp:" + authStr.indexOf("sp") );
        console.log("待选择 - 选择科目 - sq:" + authStr.indexOf("sq") );

        // 科目设置
        this.addSubject = authStr.indexOf("sj") > 0 || authStr.indexOf("vi") > 0 ;        // 新增科目
        this.updateSubject = authStr.indexOf("sk") > 0  || authStr.indexOf("vi") > 0  ;    // 修改科目
        this.controlSubject = authStr.indexOf("sz") > 0 || authStr.indexOf("vi") > 0  ;   // 修改状态
        // 科目选择
        this.toChooseScan = authStr.indexOf("sp") > 0  ;      // 待选择 - 查看
        this.toChooseSubject = authStr.indexOf("sq") > 0  ;  // 待选择 - 选择科目
        this.toChooseRefuse = authStr.indexOf("so") > 0 ;   // 待选择 - 不予下账
        this.toChargeScan = authStr.indexOf("sr") > 0 ;     // 待审批 - 查看
        this.toChargeCharge = authStr.indexOf("ss") > 0 ;   // 待审批 - 审批
        this.isAgreeScan = authStr.indexOf("st") > 0 ;      // 已批准 - 查看
        this.isAgreeRefuse = authStr.indexOf("su") > 0 ;    // 已批准 - 不予下账
        this.isAgreeUpdate = authStr.indexOf("sv") > 0  ;    // 已批准 - 修改
        this.isRejectedScan = authStr.indexOf("sx") > 0 ;    // 已驳回 - 查看
        this.isRejectedRefuse = authStr.indexOf("sw") > 0 ;  // 已驳回 - 不予下账
        this.isRejectedUpdate = authStr.indexOf("sy") > 0 ;  // 已驳回 - 修改
        // 会计录入
        this.insertVoucher = authStr.indexOf("sl") > 0 ;     // 新增记账凭证
        this.updateVoucher = authStr.indexOf("sm") > 0 ;     // 修改记账凭证
        this.deleteVoucher = authStr.indexOf("sn") > 0 ;     // 删除记账凭证
        // 对公报表
        // this.trial = authStr.indexOf("sza") > 0 ;                       // 试算
        this.trial = 1 ;                       // 去掉原来的教研 直接赋值全部都有 这个
        this.lossAndGainBroughtForward = authStr.indexOf("szb") > 0 ; // 结转损益
        this.checkout = authStr.indexOf("szc") > 0 ;                    // 结账
        this.counterCheck = authStr.indexOf("szd") > 0 ;                // 反结账
    }
    
}; 
var accountantAuth = new AccountantAuth() ;      // 实例化


// creator: 李玉婷，2022-04-26 10:44:05，返回
function reback(num) {
    if (!num) {
        $(".mainCon").hide()
        $(".mainCon1").show()
    } else {
        $(".mainCon").hide()
        $(".mainCon" + num).show()
    }

}
// creator: 李玉婷，2022-04-12 08:57:55，显示页面
function showMainCon(num){
    $("#showMainConNum").val(num)
    $(".mainCon" + num).show().siblings().hide();
}






