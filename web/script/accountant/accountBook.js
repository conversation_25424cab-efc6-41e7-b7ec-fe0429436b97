/**
 * Created by Administrator on 2017/3/22.
 */
//实例化账簿管理目录树
var accountBook = new subjectTree("accountBook");
$(function () {
    //获取所有目录
    getAllSubject(accountBook);
    
    $(".ty-firstTab li").on("click",function () {
        $(".ty-firstTab li").removeClass("ty-active");
        $(this).addClass("ty-active");
    })
    // $("#actsTree2 li").on("click",function (event) {
    //     if(event.target == this){
    //         alert(123)
    //         var subjectId = $(this).find(".acNo").text();
    //         var flag = $(".ty-firstTab .ty-active").attr("flag");//获取flag 0：总账 1：明细账
    //         //获取科目详细信息
    //         getSubjectDetail(subjectId,flag);
    //     }
    // })
    $(".fast_change").on("click",".acInfo",function () {
        alert(123);
    })
})

// creator:孟闯闯，2017-3-22  11:00:00，一级导航栏
function BigNav(num, obj) {
    $("#for_n").html(obj.html())
    // $("#BigNav").children(":eq(" + num + ")").siblings().children("span").attr("class", "ty-active");
    // $("#BigNav").children(":eq(" + num + ")").children("span").attr("class", "ty-active");
    obj.addClass('ty-active').siblings().removeClass('ty-active');
    var str1 = "";
    var str2="";
    if (num == 0) {
        str1 =  '<thead>'+
                '<tr>'+
                '   <td style="display:inline-block;width:16.5%">凭证日期</td>' +
                '   <td style="display:inline-block;width:16.5%">摘要</td>' +
                '   <td style="display:inline-block;width:16.5%">借方金额</td>' +
                '   <td style="display:inline-block;width:16.5%">贷方金额</td>' +
                '   <td style="display:inline-block;width:16.5%">方向</td>' +
                '   <td style="display:inline-block;width:17.5%">余额</td>'+
                '</tr>'+
                '</thead>';
        for(var i=0;i<20;i++){
            str2+='<tr style="display:block">'+
                '<td style="width:16.5%" class="margin_r fontSize">凭证日期'+
                '</td><td style="width:16.5%" class="margin_r fontSize">摘要'+
                '</td><td style="width:16.5%" class="margin_r fontSize">借方金额'+
                '</td><td style="width:16.5%" class="margin_r fontSize">贷方金额'+
                '</td><td style="width:16.5%" class="margin_r fontSize">方向'+
                '</td><td style="width:17.5%" class="fontSize">余额</td>'+
                '</tr>'
        }

    } else if (num == 1) {
        str1 = '<tr  style="display:block;padding-right:6px;">'+
            '<td style="display:inline-block;width:14%">凭证日期'+
            '<td style="display:inline-block;width:14%">凭证号'+
            '</td><td style="display:inline-block;width:14%">摘要'+
            '</td><td style="display:inline-block;width:14%">借方金额'+
            '</td><td style="display:inline-block;width:14%">贷方金额'+
            '</td><td style="display:inline-block;width:14%">方向'+
            '</td><td style="display:inline-block;width:16%">余额</td>'+
            '</tr>';
        for(var i=0;i<20;i++){
            str2+='<tr style="display:block">'+
                '<td style="width:14%" class="margin_r fontSize">凭证日期'+
                '<td style="width:14%" class="margin_r fontSize">凭证号'+
                '</td><td style="width:14%" class="margin_r fontSize">摘要'+
                '</td><td style="width:14%" class="margin_r fontSize">借方金额'+
                '</td><td style="width:14%" class="margin_r fontSize">贷方金额'+
                '</td><td style="width:14%" class="margin_r fontSize">方向'+
                '</td><td style="width:16%" class="fontSize">余额</td>'+
                '</tr>'
        }

    }
    $("#theader").html(str1);
    $("#set_body").html(str2);
}

// 一个对齐的滚动条
function startRequest(){
    $("#set_body").prev().width($("#set_body").children("tr").eq(0)[0].clientWidth)
}

//获取所有目录
function getAllSubject(obj){
    $.ajax({
        url:"getAllSubjecs.do" ,//得到所有科目
        data: {} ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["code"] == 1){
                var list = data["data"];
                $("#actsTree2").html( obj.setActsTree(list , "getSubjectDetail") ); //将得到的目录html插入到对应容器
            }else if(data["code"] == 0){
                alert(data["msg"]);
            }else{
                alert("返回值错误！")
            }
        } ,
        error:function () {
            alert("系统错误，请重试！")
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    });
}

function changeRules(val,type) {
    val = parseInt(val);
    if(type == "balanceDirection"){
        switch(val){
            case 1:
                return "借";
                break;
            case 2:
                return "贷";
                break;
            case 3:
                return "平";
                break;
            default:
                return false;
        }
    }
}
function getSubjectDetail(){
    var subjectId = $(".acActive").find(".acNo").text();
    var flag = $(".ty-firstTab .ty-active").attr("flag");//获取flag 0：总账 1：明细账
    var acNO = $(".acActive .acNo").html();
    var acName = $(".acActive .acName").html()
    $("#tit_msg").html(acName+"-"+acNO);   //设置科目名称-科目编号
    $.ajax({
        url:"getSubjectLedger.do" ,//得到所有科目
        data: {"subject":subjectId,"flag":flag} ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["code"] == 1){
                //获取科目详细信息数据
                var subjectDetailData = data["data"];

                //拼接科目详细信息字符串
                var subjectDetailStr  = '<thead>'+
                                        '<tr>'+
                                        '   <td>凭证日期</td>' +
                                        '   <td>摘要</td>' +
                                        '   <td>借方金额</td>' +
                                        '   <td>贷方金额</td>' +
                                        '   <td>方向</td>' +
                                        '   <td>余额</td>'+
                                        '</tr>'+
                                        '</thead>';
                for(var i in subjectDetailData){
                    subjectDetailStr += '<tbody>'+
                                        '<tr>'+
                                        '   <td onclick="'+subjectDetailData[i].createDate+'">'+subjectDetailData[i].createDate+'</td>' +
                                        '   <td>'+subjectDetailData[i].summary+'</td>' +
                                        '   <td>'+chargeUndefined(subjectDetailData[i].credit)+'</td>' +
                                        '   <td>'+chargeUndefined(subjectDetailData[i].debit)+'</td>' +
                                        '   <td>'+changeRules(subjectDetailData[i].balanceDirection,"balanceDirection")+'</td>' +
                                        '   <td>'+chargeUndefined(subjectDetailData[i].balance)+'</td>';
                                        '</tr>'
                }
                $(".subjectDetail").html(subjectDetailStr);
            }else if(data["code"] == 0){
                alert(data["msg"]);
            }else{
                alert("返回值错误！")
            }
        } ,
        error:function () {
            alert("系统错误，请重试！")
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    });

}
// setInterval("startRequest()",10);