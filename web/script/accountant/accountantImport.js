// var getSubjects = new subjectTree("getSubjects");
var specialIndex = 0;
$(function(){
    getCashFlowOption();// 获取现金流量数据
    getAccountImportMes();// 获得基本信息列表
    accountantAuth.insertVoucher
    if(!accountantAuth.insertVoucher){
        $("#enter_addbtn").remove();
    }
    //点击-新增借方
    $("#enter_addAccount,#update_addAccount").on("click",".newBorrow",function () {
        var borrowStr =     '<div class="borrowItem">'+
            '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="" class="subjectBorrow" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</span>'+
            '           <span class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>';
        $(this).parents(".borrowItem").after(borrowStr);
        //判断是不是第一组借方（第一组新增完之后第一组没有按钮，非第一组有取消按钮）
        if(!$(this).parents(".borrowItem").hasClass("fixed")){
            $(this).parent(".handle").html('<button class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</button>');
        }else{
            $(this).parent(".handle").html("");
        }
    });
    //取消借方的操作
    $("#enter_addAccount,#update_addAccount").on("click",".cancelBorrow",function () {
        //如果此取消按钮前面同级有新增按钮，取消后在他的前一个借方加入新增借方按钮
        if($(this).prev().hasClass("newBorrow")){
            $(this).parents(".borrowItem").prev().find(".handle").prepend('<button class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</button> ');
        }
        //指向此借方
        var subjectItem= $(this).parents(".borrowItem");
        //获取借方号
        var subjectNo= subjectItem.find(".bookIcon").next("input").attr("id").substring(0,4);
        //如果删除的这个贷方为1001或1002，则对应删除一个现金流量科目
        if(subjectNo === "1001" || subjectNo === "1002"){
            var specialId = subjectItem.attr("data-to");
            subjectInputObj.parents(".borrowMoreLoans").next(".special").find("."+specialId).remove();
        }
        //删除此借方
        subjectItem.remove();
    });
    //点击-新增贷方 （逻辑同借方，不再赘述）
    $("#enter_addAccount,#update_addAccount").on("click",".newLoan",function () {
        // language=HTML
        var loanStr =       '<div class="loanItem">'+
            '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="" class="subjectLoan" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span>'+
            '           <span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>';
        $(this).parents(".loanItem").after(loanStr);
        if(!$(this).parents(".loanItem").hasClass("fixed")) {
            $(this).parent(".handle").html('<button class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</button>');
        }else{
            $(this).parent(".handle").html("");
        }

    });
    //取消贷方的操作
    $("#enter_addAccount,#update_addAccount").on("click",".cancelLoan",function () {
        //如果取消按钮前面有新增按钮，取消后在他的前一个借方加入新增借方按钮
        if($(this).prev().hasClass("newLoan")){
            $(this).parents(".loanItem").prev().find(".handle").prepend('<button class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</button>');
        }
        //指向此贷方
        var subjectItem= $(this).parents(".loanItem");
        //获取贷方号
        var subjectNo= subjectItem.find(".bookIcon").next("input").attr("id").substring(0,4);
        //如果删除的这个贷方为1001或1002，则对应删除一个现金流量科目
        if(subjectNo === "1001" || subjectNo === "1002"){
            var specialId = subjectItem.attr("data-to");
            subjectInputObj.parents(".borrowMoreLoans").next(".special").find("."+specialId).remove();
        }
        //删除此借方
        $(this).parents(".loanItem").remove();
    });
    /* creator：张旭博，2017-05-06 15:16:54，每一集目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容） */
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");

        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right")
            $(this).find("i").eq(0).addClass("fa-angle-down")
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down")
            $(this).find("i").eq(0).addClass("fa-angle-right")
        }

        //点击文件夹后子列表的显隐
        $(this).next().toggle();

    });

    $(".acTable td").on("click",function () {
        var index = $(this).index()+1;
        setPartSubjects(index);
        $(this).siblings("td").removeClass("acActive");
        $(this).addClass("acActive");

    }) ;
    $(".borrowMoreLoans").on("keyup",".mNumber input",function () {
        var price = $(this).find(".price").val();
        var quantity = $(this).siblings("input:not(:disabled)").val();
        var unitPrice = $(this).val();

        if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
        if(quantity === ""){quantity = 0}
        if(unitPrice === ""){unitPrice = 0}

        if(quantity !== 0 && unitPrice !== 0){
            price = parseFloat((quantity * unitPrice).toFixed(2))
            $(this).parent().prev().find(".price").val(price);
        }
    })
});

/* creator：张旭博，2017-07-25 21:01:42，获取所有会计录入的数据 */
function getAccountImportMes() {
    $.ajax({
        url:"../accountant/getAccountantInputs.do",
        data:{"oid":2},
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open(); },
        success:function(data){
            var accVoucher = data["accVoucher"];
            if(accVoucher !==undefined && accVoucher.length > 0 ){
                var str = "";
                for(var i=0 ; i<accVoucher.length ; i++ ){
                    var borinfo  	= accVoucher[i]["borinfo"];
                    var loaninfo  	= accVoucher[i]["loaninfo"];
                    var cashjson  	= accVoucher[i]["cashjson"];
                    var is_settled  = accVoucher[i]["is_settled"];
                    var type  		= accVoucher[i]["type"];
                    var borrowStr = '';
                    var loanStr = '';
                    var borrowPrice = '';
                    var loanPrice = '';
                    for(var j = 0; j<borinfo.length;j++){
                        borrowStr 	+= '<div>'+borinfo[j].borrowNames+'</div>';
                        loanStr 	+= '<div></div>';
                        borrowPrice += '<div>'+borinfo[j].borrowAmount+'</div>';
                        loanPrice 	+= '<div></div>'
                    }
                    for(var k = 0; k<loaninfo.length;k++){
                        borrowStr 	+= '<div></div>';
                        loanStr 	+= '<div>'+loaninfo[k].loanNames+'</div>';
                        borrowPrice += '<div></div>';
                        loanPrice 	+= '<div>'+loaninfo[k].loanAmount+'</div>'
                    }
                    if(type == 2){
                        str += 	'<tr id="'+accVoucher[i].id+'" class="trGray">' ;
                    }else{
                        str += 	'<tr id="'+accVoucher[i].id+'">' ;
                    }
                    str +=      '<td>' + testValue( accVoucher[i].create_date )	+'</td>' +
                        '<td>' + testValue( accVoucher[i].summary )		+'</td>' +
                        '<td>' + testValue( borrowStr )					+'</td>'+
                        '<td>' + testValue( borrowPrice )				+'</td>' +
                        '<td>' + testValue( loanStr )					+'</td>' +
                        '<td>' + testValue( loanPrice )					+'</td>' +
                        '<td class="ty-td-control">' ;
                    if(type == 2 ){
                        str +=	'<span class="ty-color-gray" >修改</span>';
                    }else{
                        str +=	'<span class="ty-color-blue" onclick="entering_upbtn($(this))">修改</span>';
                    }
                    /*  type=2表示冲红的凭证  isSettled=1表示结账过的凭证  */
                    if(type == 2 ||type == 3 || is_settled == 1 ){
                        // str += '<span class="ty-color-gray">删除</span>' ;
                    }else{
                        str += '<span class="ty-color-red" onclick="entering_delet($(this))">删除</span>' ;
                    }
                    str +=  '</td>'+
                        '<td class="hd borinfo">'+JSON.stringify(borinfo)+'</td>'+
                        '<td class="hd loaninfo">'+JSON.stringify(loaninfo)+'</td>'+
                        '<td class="hd cashjson">'+JSON.stringify(cashjson)+'</td>'+
                        '</tr>' ;
                }
            }else{
                str = '';
            }
            $("#set_body").html(str);
        },
        error:function (meg) {
            bounce.show($("#tip"));  $("#tip .tipWord").html("连接错误，请稍后重试！");
        },
        complete:function () { loading.close();   }
    })
}

//点击新增按钮
/* updater：张旭博，2017-08-02 11:23:36，点击新增按钮 */
function enter_add(){
    bounce.show($("#errorTip"));
    $("#errorTip .tipWord").html("您没有新增权限！");
    var headInfoStr  =      '<div class="normalItem">'+
        '   <span>摘要：</span>'+
        '   <input type="text" class="summary">'+
        '</div>';
    var borrowMoreLoanStr = showBorLoan(0,0);
    var specialStr          =       '';
    var chooseBtn           =       '<span class="ty-btn ty-btn-big ty-circle-5" onclick="entering_cancel()">取消</span>'+
        '<button class="ty-btn ty-btn-green ty-circle-5 ty-btn-big" id="entering_addsure" onclick="entering_addsure()">确定</button>';
    $("#enter_addAccount .headInfo").html(headInfoStr);
    $("#enter_addAccount .borrowMoreLoans").html(borrowMoreLoanStr);
    $("#enter_addAccount .special").html(specialStr);
    $("#enter_addAccount .chooseBtn").html(chooseBtn);
    bounce.show($("#enter_addAccount"));
    bounce.resize($("#enter_addAccount"));
    $('body').everyTime('0.5s','enter_addAccount',function(){
        var allBorrowPrice = 0;
        var allLoanPrice = 0;
        $("#enter_addAccount .borrowItem").each(function () {
            var price = $(this).find(".price").val();
            var creditQuantity = $(this).find(".creditQuantity").val();
            var debitQuantity = $(this).find(".debitQuantity").val();
            var unitPrice = $(this).find(".unitPrice").val();

            if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
            if(creditQuantity === ""){creditQuantity = 0}
            if(debitQuantity === ""){debitQuantity = 0}
            if(unitPrice === ""){unitPrice = 0}

            if($(this).find(".mNumber").is(':visible')){
                if(creditQuantity !== 0){
                    $(this).find(".debitQuantity").prop("disabled",true);
                    // price =  parseFloat((creditQuantity * unitPrice).toFixed(2));
                    // $(this).find(".price").val(price);
                }else{
                    $(this).find(".debitQuantity").prop("disabled",false);
                }
                if(debitQuantity !== 0){
                    $(this).find(".creditQuantity").prop("disabled",true);
                    // price =  parseFloat((debitQuantity * unitPrice).toFixed(2));
                    // $(this).find(".price").val(price);
                }else{
                    $(this).find(".creditQuantity").prop("disabled",false);
                }
            }
            allBorrowPrice += price;
        });
        $("#enter_addAccount .loanItem").each(function () {
            var price = $(this).find(".price").val();
            var creditQuantity = $(this).find(".creditQuantity").val();
            var debitQuantity = $(this).find(".debitQuantity").val();
            var unitPrice = $(this).find(".unitPrice").val();

            if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
            if(creditQuantity === ""){creditQuantity = 0}
            if(debitQuantity === ""){debitQuantity = 0}
            if(unitPrice === ""){unitPrice = 0}

            if($(this).find(".mNumber").is(':visible')){
                if(creditQuantity !== 0){
                    $(this).find(".debitQuantity").prop("disabled",true);
                    // price =  parseFloat((creditQuantity * unitPrice).toFixed(2));
                    // $(this).find(".price").val(price);
                }else{
                    $(this).find(".debitQuantity").prop("disabled",false);
                }
                if(debitQuantity !== 0){
                    $(this).find(".creditQuantity").prop("disabled",true);
                    // price =  parseFloat((debitQuantity * unitPrice).toFixed(2));
                    // $(this).find(".price").val(price);
                }else{
                    $(this).find(".creditQuantity").prop("disabled",false);
                }
            }
            allLoanPrice += price;
        });
        var i = 0;
        $("#enter_addAccount .borrowMoreLoans").find("input:not(:disabled)").each(function () {
            if($(this).val() == ""){
                i++;
            }
        });
        // $("#choose_addAccount .source span").html(i);
        if(parseFloat(allBorrowPrice.toFixed(2)) === parseFloat(allLoanPrice.toFixed(2)) && i===0){
            $("#entering_addsure").removeAttr("disabled")
        }else{
            $("#entering_addsure").attr("disabled","disabled")
        }
    });
}

// 召唤出科目树
var subjectInputObj = null ;
function getActsList( obj ){
    subjectInputObj = obj ;
    bounce_Fixed.show( $("#Accounting") );
    $("#Accounting table td").eq(0).click();
}

// 确定选择的科目
/* updater：张旭博，2017-08-02 11:25:52，确定选择的科目 */
function getAccount() {
    $(".acTable .acActive").click();

    var subjectNo,  //原始科目
        subjectId;  //账簿中当前选择的科目

    var treeItemActive = $("#Accounting").find(".ty-treeItemActive");
    var subjectUnit = treeItemActive.attr("title");
    var subjectName = treeItemActive.text().split(" ")[1];


    subjectInputObj.attr("id") === undefined?subjectNo = "":subjectNo = subjectInputObj.attr("id");
    subjectId = treeItemActive.attr("id");
    //判断是否显示单价数量
    if(treeItemActive.attr("cn") === "1"){
        subjectInputObj.siblings(".mNumber").show();
        subjectInputObj.siblings(".mNumber").find("input").prop("disabled",false);
    }else if(treeItemActive.attr("cn") === "0"){
        subjectInputObj.siblings(".mNumber").hide();
        subjectInputObj.siblings(".mNumber").find("input").prop("disabled",true);
    }
    if(treeItemActive.next().length<=0){
        var level1 = treeItemActive.parents(".level2").prev().text().split(" ")[1];
        var level2 = treeItemActive.parents(".level3").prev().text().split(" ")[1];
        if(level2 !== undefined){
            //这个是三级科目
            subjectName = level1 + '-' +  level2 + '-' + subjectName;

        }else if(level1 !== undefined){
            //这个是二级科目
            subjectName = level1 + '-' + subjectName;
        }
        subjectInputObj.val(subjectName);
        subjectInputObj.attr("id",subjectId);
        subjectInputObj.siblings(".mNumber").find(".measure_unit").html(subjectUnit);
        bounce_Fixed.cancel();
    }

    //判断 1001 和 1002 开头的科目  需要生成对应的现金流量下拉框
    if(subjectId.substring(0,4) === '1001' || subjectId.substring(0,4) === '1002' ){
        if(subjectNo === '1001' || subjectNo === '1002'){}else{
            if(subjectInputObj.parent().attr("data-to") === undefined){
                subjectInputObj.parent().attr("data-to","special"+specialIndex);
                var specialStr =        '<div class="cashFlowGroup special'+specialIndex+'">'+
                    '<div class="normalItem">'+
                    '<span>现金流量：</span>'+
                    '<select class="cash">'+
                    '<option value="1">-----请选择现金流量项目-----</option>'+
                    $(".cashFlowOption").html()+
                    '</select> '+
                    '<span>金额：</span>'+
                    '<input class="amount" type="text"/>'+
                    '</div>'+
                    '</div>';
                subjectInputObj.parents(".borrowMoreLoans").next(".special").append(specialStr);
                specialIndex ++;
            }
        }
    }else{
        if(subjectNo.substring(0,4) === '1001' || subjectNo.substring(0,4) === '1002'){
            // $("#enter_addAccount .special .cashFlowGroup").eq(-1).remove();
            // $("#update_addAccount .special .cashFlowGroup").eq(-1).remove();
            var specialId = subjectInputObj.parent().attr("data-to");
            subjectInputObj.parents(".borrowMoreLoans").next(".special").find("."+specialId).remove();
            subjectInputObj.parent().removeAttr("data-to");
        }
    }

    // 判断特殊科目,只在会计录入子模块中起作用。
    // 当选择科目时借方中如果存在生产成本（4001）或其子科目，同时贷方存在制造费用（4101）或其子科目的话需要访问该接口，然后把返回的所有科目显示在贷方
    // 当选择科目时借方中如果存在库存商品（1405）或其子科目，同时贷方存在生产成本（4001）或其子科目的话需要访问该接口，然后把返回的所有科目显示在贷方
    // 当选择科目时借方中如果存在主营业务成本（5401）或其子科目，同时贷方存在库存商品（1405）或其子科目的话需要访问该接口，然后把返回的所有科目显示在贷方

    var parentSubjectId = subjectId.substring(0,4);
    var specialState = 0;
    var loanId = 0;
    var $del;
    if(subjectInputObj.hasClass("subjectBorrow")){
        switch (parentSubjectId){
            case "4001":
                $(".subjectLoan").each(function () {
                    if($(this).attr("id").substring(0,4) === "4101"){
                        specialState = 1;
                        loanId = $(this).attr("id");
                        $del = $(this);
                    }
                });
                break;
            case "1405":
                $(".subjectLoan").each(function () {
                    if($(this).attr("id").substring(0,4) === "4001"){
                        specialState = 1;
                        loanId = $(this).attr("id");
                        $del = $(this);
                    }
                });
                break;
            case "5401":
                $(".subjectLoan").each(function () {
                    if($(this).attr("id").substring(0,4) === "1405"){
                        specialState = 1;
                        loanId = $(this).attr("id");
                        $del = $(this);
                    }
                });
                break;
        }
    }

    if(subjectInputObj.hasClass("subjectLoan")){
        loanId = subjectId;
        $del = subjectInputObj;
        switch (parentSubjectId){
            case "4101":
                $(".subjectBorrow").each(function () {
                    if($(this).attr("id").substring(0,4) === "4001"){
                        specialState = 1;
                    }
                });
                break;
            case "4001":
                $(".subjectBorrow").each(function () {
                    if($(this).attr("id").substring(0,4) === "1405"){
                        specialState = 1;
                    }
                });
                break;
            case "1405":
                $(".subjectBorrow").each(function () {
                    if($(this).attr("id").substring(0,4) === "5401"){
                        specialState = 1;
                    }
                });
                break;
        }
    }
    if(subjectId.substring(0,4) === subjectNo.substring(0,4)){
        specialState = 0;
    }
    if(specialState === 1){
        // console.log(subjectInputObj.parents(".loanItem").attr("data-is"));
        getSpecialSubjects(loanId,$del);
    }
}

//新增中的确定
/* updater：张旭博，2017-08-02 11:25:52，新增中的确定 */
function entering_addsure() {
    var subjectBorrow = [];
    var subjectLoan = [];
    $("#enter_addAccount .borrowItem").each(function () {
        var borrow = $(this).find(".subjectBorrow").attr("id");
        var subjectNames = $(this).find(".subjectBorrow").val();
        var price = $(this).find(".price").val();
        var creditQuantity  = $(this).find(".creditQuantity").val();if(creditQuantity === ""){creditQuantity = 0};
        var debitQuantity   = $(this).find(".debitQuantity").val();if(debitQuantity === ""){debitQuantity = 0};
        var unitPrice    = $(this).find(".unitPrice").val();if(unitPrice === ""){unitPrice = 0};
        subjectBorrow.push({
            "subject" : borrow ,
            "subjectNames":subjectNames ,
            "price" : price ,
            "creditQuantity" : creditQuantity,
            "debitQuantity" : debitQuantity,
            "unitPrice" : unitPrice
        });
    });
    $("#enter_addAccount .loanItem").each(function () {
        var loan = $(this).find(".subjectLoan").attr("id");
        var subjectNames = $(this).find(".subjectLoan").val();
        var price = $(this).find(".price").val();
        var creditQuantity  = $(this).find(".creditQuantity").val();if(creditQuantity === ""){creditQuantity = 0};
        var debitQuantity   = $(this).find(".debitQuantity").val();if(debitQuantity === ""){debitQuantity = 0};
        var unitPrice    = $(this).find(".unitPrice").val();if(unitPrice === ""){unitPrice = 0};
        subjectLoan.push({
            "subject" : loan ,
            "subjectNames":subjectNames ,
            "price" : price,
            "creditQuantity" : creditQuantity,
            "debitQuantity" : debitQuantity,
            "unitPrice" : unitPrice
        });
    });
    var summary = $("#enter_addAccount .summary").val();
    var data = {
        "summary":summary,
        "belong_peroid":1,//凭证所属月份:本月凭证
        "mode": 1 ,
        "category":1,
        "subjectBorrow":JSON.stringify(subjectBorrow),
        "subjectLoan":JSON.stringify(subjectLoan),
        "is_account":1,
        "bill_detail":0,
        "memo":"" ,
        "bill_period":1,//票据所属月份:本月票据
        "source":1,
        "oid":2
    }
    var cashjson = [];
    if($("#enter_addAccount .special").text() !== ""){
        $("#enter_addAccount .special .cashFlowGroup").each(function () {
            var cashitem = $(this).find(".cash").val();
            var cashes = $(this).find(".amount").val();
            if(cashitem !== 1 && cashes !== ""){
                cashjson.push({"cashitem":cashitem,"cashes":cashes})
            }
        }) ;
        data["cashjson"] = JSON.stringify(cashjson);
    }
    $.ajax({
        url:"../accountant/insertAccountantInputs.do",
        data: data,
        type: "post",
        dataType: "json",
        success: function (data){
            var state = data["state"];
            if (state === 0 || state === "0") {
                bounce.show($("#errorTip"));
                $("#errorTip .tipWord").html("新增失败！");
            }else if(state === 1){
                bounce.show($("#tip"));
                $("#tip .tipWord").html("新增成功！");
                getAccountImportMes();
            }else{
                bounce.show($("#errorTip"));
                $("#errorTip .tipWord").html(data.prompt);
            }
        },
        error: function (msg) {  // 失败以后的操作
            bounce.show($("#errorTip"));
            $("#errorTip .tipWord").html("系统错误，请重试！");
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })

}

//点击修改按钮
/* updater：张旭博，2017-08-02 11:25:52，点击修改按钮 */
function entering_upbtn(obj){
    if(!accountantAuth.updateVoucher){
        $("#errorTip .tipWord").html("您没有修改权限！");
        bounce.show($("#errorTip"));
        return false;
    }
    var id =  obj.parent().parent().attr("id");
    $.ajax({
        "url" : "judgeMinorSettle.do" ,
        "data" : { "id" : id , "voucher_id":0 } ,  // id 凭证id , voucher_id 历史表的id，存在则说明修改的是历史表的数据
        "type" : "post" ,
        beforeSend:function () { loading.open(); } ,
        success:function (data) {
            var res = data["res"] ;
            if(res){ // 已经小结账
                $("#errorTip .tipWord").html("该凭证已经记账，请在“对公报表 - 反结账”后在修改！");
                bounce.show($("#errorTip"));
                return false ;
            }

            bounce.show($("#update_addAccount"));
            obj.parent().parent().addClass("itemActive");
            obj.parent().parent().siblings().removeClass("itemActive");
            var borInfo  =  JSON.parse(obj.parent().parent("tr").children("td").eq(7).text());
            var loanInfo =  JSON.parse(obj.parent().parent("tr").children("td").eq(8).text());
            var cashjson =  JSON.parse(obj.parent().parent("tr").children("td").eq(9).text());
            var summary =  obj.parent().parent("tr").children("td").eq(1).text();
            var headInfo            =   '<div class="choose_Input">'+
                '<div class="normalItem">'+
                '<span>摘要：</span>'+
                '<input type="text" name="" class="summary"  value="'+summary+'">'+
                '</div>';
            //判端多借多贷的显示（只有一借的时候 显示新增借方，多借的时候 第一借没有按钮 最后一借有两个按钮 新增和取消  其他都是只有一个取消按钮
            var borrowMoreLoanStr   =       showBorLoan(borInfo,loanInfo);
            var cashFlowStr         =       showCashFlow(cashjson);

            $("#update_addAccount .headInfo").html(headInfo);
            $("#update_addAccount .borrowMoreLoans").html(borrowMoreLoanStr);
            $("#update_addAccount .special").html(cashFlowStr);
            bounce.resize($("#update_addAccount"))
            $('body').everyTime('0.5s','update_addAccount',function(){
                var allBorrowPrice = 0;
                var allLoanPrice = 0;
                $("#update_addAccount .borrowItem").each(function () {
                    var price = $(this).find(".price").val();
                    var creditQuantity = $(this).find(".creditQuantity").val();
                    var debitQuantity = $(this).find(".debitQuantity").val();
                    var unitPrice = $(this).find(".unitPrice").val();

                    if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
                    if(creditQuantity === ""){creditQuantity = 0}
                    if(debitQuantity === ""){debitQuantity = 0}
                    if(unitPrice === ""){unitPrice = 0}

                    if($(this).find(".mNumber").is(':visible')){
                        if(creditQuantity !== 0){
                            $(this).find(".debitQuantity").prop("disabled",true);
                            // price =  parseFloat((creditQuantity * unitPrice).toFixed(2));
                            // $(this).find(".price").val(price);
                        }else{
                            $(this).find(".debitQuantity").prop("disabled",false);
                        }
                        if(debitQuantity !== 0){
                            $(this).find(".creditQuantity").prop("disabled",true);
                            // price =  parseFloat((debitQuantity * unitPrice).toFixed(2));
                            // $(this).find(".price").val(price);
                        }else{
                            $(this).find(".creditQuantity").prop("disabled",false);
                        }
                    }
                    allBorrowPrice += price;
                });
                $("#update_addAccount .loanItem").each(function () {
                    var price = $(this).find(".price").val();
                    var creditQuantity = $(this).find(".creditQuantity").val();
                    var debitQuantity = $(this).find(".debitQuantity").val();
                    var unitPrice = $(this).find(".unitPrice").val();

                    if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
                    if(creditQuantity === ""){creditQuantity = 0}
                    if(debitQuantity === ""){debitQuantity = 0}
                    if(unitPrice === ""){unitPrice = 0}

                    if($(this).find(".mNumber").is(':visible')){
                        if(creditQuantity !== 0){
                            $(this).find(".debitQuantity").prop("disabled",true);
                            // price =  parseFloat((creditQuantity * unitPrice).toFixed(2));
                            // $(this).find(".price").val(price);
                        }else{
                            $(this).find(".debitQuantity").prop("disabled",false);
                        }
                        if(debitQuantity !== 0){
                            $(this).find(".creditQuantity").prop("disabled",true);
                            // price =  parseFloat((debitQuantity * unitPrice).toFixed(2));
                            // $(this).find(".price").val(price);
                        }else{
                            $(this).find(".creditQuantity").prop("disabled",false);
                        }
                    }
                    allLoanPrice += price;
                });
                var i = 0;
                $("#update_addAccount .borrowMoreLoans").find("input:not(:disabled)").each(function () {
                    if($(this).val() == ""){
                        i++;
                    }
                });
                // $("#choose_addAccount .source span").html(i);
                if(Number(allBorrowPrice) === Number(allLoanPrice) && i===0){
                    $("#entering_updatasure").removeAttr("disabled")
                }else{
                    $("#entering_updatasure").attr("disabled","disabled")
                }
            });
        },
        error:function () {

        },
        complete:function () {
            loading.close() ;
        }
    });


}

//修改中的确定
/* updater：张旭博，2017-08-02 11:25:52，修改中的确定 */
function entering_updatasure() {
    var id = $(".itemActive").attr("id");
    var subjectBorrow = [];
    var subjectLoan = [];
    var summary = $("#update_addAccount .summary").val();

    $("#update_addAccount .borrowItem").each(function () {
        var borrow = $(this).find(".subjectBorrow").attr("id");
        var subjectNames = $(this).find(".subjectBorrow").val();
        var price = $(this).find(".price").val();
        var creditQuantity  = $(this).find(".creditQuantity").val();if(creditQuantity === ""){creditQuantity = 0};
        var debitQuantity   = $(this).find(".debitQuantity").val();if(debitQuantity === ""){debitQuantity = 0};
        var unitPrice    = $(this).find(".unitPrice").val();if(unitPrice === ""|| unitPrice=="undefined"){unitPrice = 0};
        subjectBorrow.push({"subject" : borrow , "subjectNames":subjectNames , "price" : price , "creditQuantity" : creditQuantity, "debitQuantity" : debitQuantity , "unitPrice" : unitPrice });
    });
    $("#update_addAccount .loanItem").each(function () {
        var loan = $(this).find(".subjectLoan").attr("id");
        var subjectNames = $(this).find(".subjectLoan").val();
        var price = $(this).find(".price").val();
        var creditQuantity  = $(this).find(".creditQuantity").val();if(creditQuantity === ""){creditQuantity = 0};
        var debitQuantity   = $(this).find(".debitQuantity").val();if(debitQuantity === ""){debitQuantity = 0};
        var unitPrice    = $(this).find(".unitPrice").val();if(unitPrice === ""|| unitPrice=="undefined"){unitPrice = 0};
        subjectLoan.push({"subject" : loan , "subjectNames":subjectNames , "price" : price , "creditQuantity" : creditQuantity, "debitQuantity" : debitQuantity , "unitPrice" : unitPrice});
    });
    var data = {
        "id":id,
        "oid":2,
        "bill_detail":0,
        "summary":summary,
        "subjectBorrow":JSON.stringify(subjectBorrow),
        "subjectLoan":JSON.stringify(subjectLoan)
    } ;
    var cashjson = [];
    if($("#update_addAccount .special").text() !== ""){
        $("#update_addAccount .special .cashFlowGroup").each(function () {
            var cashitem = $(this).find(".cash").val();
            var cashes = $(this).find(".amount").val();
            if(cashitem !== 1 && cashes !== ""){
                cashjson.push({"cashitem":cashitem,"cashes":cashes})
            }
        }) ;
        data["cashjson"] = JSON.stringify(cashjson);
    }
    $.ajax({
        url:"../accountant/updateAccountantInputs.do",
        data: data,
        type: "post",
        dataType: "json",
        success: function (data) {
            bounce.show($("#tip"));
            $("#tip .tipWord").html("修改成功！");
            getAccountImportMes();
        },
        error: function (msg) {  // 失败以后的操作
            bounce.show($("#tip"));
            $("#tip .tipWord").html("连接错误，请稍后重试！");
            return false;
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })
}

//点击删除按钮
/* updater：张旭博，2017-08-02 11:25:52，点击删除按钮 */
function entering_delet(obj){
    if(!accountantAuth.deleteVoucher){
        bounce.show($("#errorTip"));
        $("#errorTip .tipWord").html("您没有删除权限！");
        return false;
    }
    // accountTrobj = obj.parent().parent();
    // var id = obj.siblings(".hd").children(".billID").html();
    // if ( id == undefined || id == ""){  // 校验销售id 是否合法
    // 	bounce.show($("#mtTip"));
    // 	$("#mt_tip_ms").html("系统错误，刷新重试！"); ///  不合法的提示信息赋值
    // 	return false; // 结束
    // }
    obj.parent().parent().addClass("itemActive");
    obj.parent().parent().siblings().removeClass("itemActive");
    bounce.show($("#enter_deletAccount"));
    // $("#del_billID").html(id);

}

//删除中的确定
/* updater：张旭博，2017-08-02 11:25:52，删除中的确定 */
function entering_deletsure() {
    var id = $(".itemActive").attr("id");
    if ( id == undefined || id == ""){  // 校验id 是否合法
        bounce.show($("#errorTip"));
        $("#errorTip .tipWord").html("系统错误，刷新重试！"); ///  不合法的提示信息赋值
        return false;
    }
    $.ajax({
        url:"../accountant/deleteAccountantInputs.do",
        data:{
            "id":id
        },
        type:"post",
        dataType:"Json",
        success:function (data){
            var res = data["res"];
            if(res === 1 ){
                bounce.show($("#tip"));
                $("#tip .tipWord").html("删除成功！");
                getAccountImportMes();
            }else if( res === 0 ){
                bounce.show($("#errorTip"));
                $("#errorTip .tipWord").html("该凭证已经结账或存在冲红的凭证，不能删除");
            }else{
                bounce.show($("#mtTip"));
                $("#errorTip .tipWord").html("系统错误！");
            }
        },
        error: function (msg) {  // 失败以后的操作
            bounce.show($("#errorTip"));
            $("#errorTip .tipWord").html("连接错误，请稍后重试！");
            return false ;
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })
}

/* creator：张旭博，2017-08-02 11:15:43，获取现金流量下拉框数据，并赋予到页面开始（.cashFlowOption） */
function getCashFlowOption() {
    $.ajax({
        url: "getCashItems.do",
        data: {},
        type: "post",
        dataType: "json",
        success: function (data) {
            var cashList = data["data"];
            var optionStr = "";
            for(var i=0;i<cashList.length;i++){
                optionStr +=               '<option value="'+cashList[i].cashFlowCode+'">'+cashList[i].cashFlowName+'</option>'
            }
            $(".cashFlowOption").html(optionStr);
        },
        error: function () {
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    });
}

/* creator：张旭博，2017-07-31 15:04:54，获取已经存在的借贷方拼接字符串*/
function showBorLoan(borInfo,loanInfo) {
    var borLoanStr = '';    //借贷字符串
    var handleStr = '';     //是否有取消和新增按钮
    var fixed = '';         //为第一组借方或贷方添加样式
    var specialStr = '';
    var specialIndexThis = 0;

    //判断是否为初始状态（传参为0则为初始状态，没有对输入框赋值）
    if(borInfo === 0 && loanInfo === 0){

        //静态页面，无数据

        borLoanStr =    '<div class="borrowItem fixed">'+
            '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="" class="subjectBorrow" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>'+
            '<div class="loanItem fixed">'+
            '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="" class="subjectLoan" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>';
    }else {
        for (var j = 0; j < borInfo.length; j++) {
            //判端多借多贷的显示（只有一借的时候 显示新增借方，多借的时候 第一借没有按钮 最后一借有两个按钮 新增和取消  其他都是只有一个取消按钮
            if (borInfo.length === 1) {
                handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</span>';
                fixed = 'fixed';
            } else {
                if (j === 0) {
                    handleStr = '';
                    fixed = 'fixed';
                } else if (j === borInfo.length - 1) {
                    handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</span>' +
                        '<span class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</span>';
                    fixed = '';
                } else {
                    handleStr = '<span class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</span>';
                    fixed = '';
                }
            }
            if(borInfo[j].quantityAssistingAccounting === "0"){
                var styleStr = 'display:none';
            }else{
                var styleStr = 'display:block';
            }
            if(borInfo[j].borrowSubject.substring(0,4) === '1001' || borInfo[j].borrowSubject.substring(0,4) === '1002'){
                specialStr = 'data-to="special'+specialIndexThis+'a"';
                specialIndexThis ++ ;
            }
            if(borInfo[j].creditQuantity  === 0){borInfo[j].creditQuantity  = '' ;var creditDisabled = 'disabled'}else{var creditDisabled = ""};
            if(borInfo[j].debitQuantity  === 0){borInfo[j].debitQuantity  = '' ;var debitDisabled = 'disabled'}else{var debitDisabled = ""};
            //拼接借方字符串
            borLoanStr += '<div class="borrowItem ' + fixed + '" '+specialStr +'>' +
                '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="' + borInfo[j].borrowSubject + '" class="subjectBorrow" type="text" onclick="getActsList($(this))" value="' + borInfo[j].borrowNames + '" id="' + borInfo[j].borrowSubject + '"/>' +
                '   <div class="handle">' + handleStr +
                '   </div>' +
                '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text" value="' + borInfo[j].borrowAmount + '"/></div>' +
                '   <div class="m5 mNumber" style="'+styleStr+'">' +
                '       <span>借数量：</span><input class="creditQuantity w65" type="text" value="' + borInfo[j].creditQuantity + '" '+creditDisabled+'/>' +
                '       <span>贷数量：</span><input class="debitQuantity w65" type="text" value="' + borInfo[j].debitQuantity + '" '+debitDisabled+'/>' +
                '       <span>单价：</span><input class="unitPrice w65" type="text" value="' + borInfo[j].unitPrice + '"/>' +
                '       <span>单位：</span><span class="measure_unit">' + borInfo[j].measureUnit + '</span>' +
                '   </div>' +
                '</div>';
        }
        for (var k = 0; k < loanInfo.length; k++) {
            //贷方逻辑同上
            if (loanInfo.length === 1) {
                handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span>';
                fixed = 'fixed';
            } else {
                if (k === 0) {
                    handleStr = '';
                    fixed = 'fixed';
                } else if (k === loanInfo.length - 1) {
                    handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span> ' +
                        '<span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>';
                    fixed = '';
                } else {
                    handleStr = '<span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>';
                    fixed = '';
                }
            }
            if(loanInfo[k].quantityAssistingAccounting === "0"){
                var styleStr = 'display:none';
            }else{
                var styleStr = 'display:block';
            }
            if(loanInfo[k].loanSubject.substring(0,4) === '1001' || loanInfo[k].loanSubject.substring(0,4) === '1002'){
                specialStr = 'data-to="special'+specialIndexThis+'a"';
                specialIndexThis ++ ;
            }
            if(loanInfo[k].creditQuantity  === 0){loanInfo[k].creditQuantity  = '' ;var creditDisabled = 'disabled'}else{var creditDisabled = ""};
            if(loanInfo[k].debitQuantity  === 0){loanInfo[k].debitQuantity  = '' ;var debitDisabled = 'disabled'}else{var debitDisabled = ""};
            //拼接贷方字符串
            borLoanStr += '<div class="loanItem ' + fixed + '" '+specialStr +'>' +
                '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="' + loanInfo[k].loanSubject + '" class="subjectLoan" type="text" onclick="getActsList($(this))" value="' + loanInfo[k].loanNames + '" id="' + loanInfo[k].loanSubject + '"/>' +
                '   <div class="handle">' + handleStr +
                '   </div>' +
                '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text" value="' + loanInfo[k].loanAmount + '"/></div>' +
                '   <div class="m5 mNumber" style="'+styleStr+'">' +
                '       <span>借数量：</span><input class="creditQuantity w65" type="text" value="' + loanInfo[k].creditQuantity + '" '+creditDisabled+'/>' +
                '       <span>贷数量：</span><input class="debitQuantity w65" type="text" value="' + loanInfo[k].debitQuantity + '" '+debitDisabled+'/>' +
                '       <span>单价：</span><input class="unitPrice w65" type="text" value="' + loanInfo[k].unitPrice + '"/>' +
                '       <span>单位：</span><span class="measure_unit">' + loanInfo[k].measureUnit + '</span>' +
                '   </div>' +
                '</div>';
        }
    }
    return borLoanStr;      //返回借贷字符串
}

/* creator：张旭博，2017-08-02 11:17:35，显示现金流量 */
function showCashFlow(cashjson){
    var str = '';
    for(var l=0;l<cashjson.length;l++){
        str  +=    '<div class="cashFlowGroup special'+l+'a">'+
            '<div class="normalItem">'+
            '<span>现金流量：</span>'+
            '<select class="cash" value="'+cashjson[l].cashitem+'">'+
            '<option value="'+cashjson[l].cashitem+'">'+cashjson[l].cashitemName+'</option>'+
            '<option value="1">-----请选择现金流量项目-----</option>'+
            $(".cashFlowOption").html()+
            '</select> '+
            '<span>金额：</span>'+
            '<input class="amount" type="text" value="'+cashjson[l].cash+'"/>'+
            '</div>'+
            '</div>';
    }
    return str;
}

// creator: 张旭博，2018-04-03 14:53:33，特殊需求（特定情况下访问的接口）
function getSpecialSubjects(loanSubject,selector) {
    return $.ajax({
        url: "../accountant/getSubjects.do",
        data: {"subject":loanSubject},
        type: "post",
        dataType: "json",
        success: function (data) {

            var subjectList = data["res"];

            var loanStr = '';
            var isLoanItem = $("#enter_addAccount .borrowMoreLoans .loanItem").length && $("#enter_addAccount .borrowMoreLoans .loanItem").length>0;
            if(subjectList && subjectList.length > 0){
                for(var i = 0 ;i<subjectList.length;i++){

                    var balance = subjectList[i].balance;
                    var id = subjectList[i].id;
                    var subject = subjectList[i].subject;
                    var name = subjectList[i].name;

                    var handleStr = '';
                    var fixed  = '';

                    console.log(isLoanItem);

                    if(isLoanItem){
                        if(i === subjectList.length-1){
                            handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span> ' +
                                '<span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>';
                        }else{
                            handleStr = '<span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>';
                        }
                    }else{
                        if(subjectList.length === 1){
                            handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span>';
                            fixed = 'fixed';
                        }else{
                            if(i === 0){
                                handleStr = '';
                                fixed = 'fixed';
                            }else if(i === subjectList.length-1){
                                handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span> ' +
                                    '<span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>';
                            }else{
                                handleStr = '<span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>';
                            }
                        }
                    }
                    loanStr +=  '<div class="loanItem '+fixed+'" data-is="1">' +
                                '   <div class="handle">' + handleStr + '</div>' +
                                '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="' + subject + '" class="subjectLoan" type="text" onclick="getActsList($(this))" value="' + name + '" id="' + subject + '"/>' +
                                '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text" value="' + balance + '"/></div>' +
                                '</div>';

                }
                selector.parents(".loanItem").remove();
                $("#enter_addAccount .borrowMoreLoans").append(loanStr);
            }

        // 1. balance（该科目的余额）
        // 2. id（该科目的id）
        // 3. subject（科目的编号全称）
        // 4. name(科目名称)
        },
        error: function () {
        },
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })
}


//-------------------- 辅助方法 --------------------

// 一个对齐的滚动条
function startRequest(){
    if( $("#set_body").children("tr").eq(0).length > 0  ){
        $("#set_body").prev().width($("#set_body").children("tr").eq(0)[0].clientWidth)
    }
}
setInterval("startRequest()",10);

/* creator：张旭博，2017-08-02 11:20:28，去null操作 */
function testValue(val) {
    if(val==undefined||val=="undefined"||val==null||val=="null"){
        val="";
        return val;
    }else {
        return val;
    }
}

//关闭弹框
function entering_cancel(){
    bounce.cancel();
}
function entering_close(){
    bounce.cancel();
}

//关闭提示
function bounce_cancel() {

    bounce.cancel();

}
function bounce_close() {
    bounce.cancel();
}


