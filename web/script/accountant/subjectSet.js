/*creator:孟闯闯 creaTime:2017-2-17    用于实现“会计=>科目选择”页面的功能*/
var assetArr =[],debtArr =[],ownerArr=[],costArr=[],profitArr =[];
$(function(){
    showMainCon(1);
    accounttingState();
	// 从消息跳过来的直接新增科目
	var messID = getUrlParam("id") ;
	if(messID){
		mtSubjectAdd( messID ) ;
	}
	// 获得基本信息列表
	category( $("#bar_asset") );
   // chargeOpen(); // 是否显示启用 科目名称关联功能 按钮

	$(".checkNum").on("click",function () {
        $(this).toggleClass("ty-form-checked");
        $(this).parent().next().toggle();
    }) ;

    $(".associate").on('change', 'input:radio', function () {
        if($(this).val() === '1'){
            $(this).parents('tr').find('select').prop('disabled',false)
        } else {
            $(this).parents('tr').find('select').prop('disabled',true)
            $(this).parents('tr').find('select').val('')
        }
    })
    $("body").on("click",".ty-btn,.fun-btn",function(){
        var type = $(this).data("type");
        switch (type) {
            case 'note': // 重要说明
                bounce.show($("#importantNote"));
                break;
            case 'changeRecord': // 科目变动记录
                showMainCon(3);
                getSubChangeRecord(1, 3);
                break;
            case 'purchaseAssociationSet': // 采购关联设置
                chargeOpen();
                break;
            case 'purchaseAssocial':
                purchaseAssocialSure();
                break;
            case 'functionIll': // 采购关联设置-功能说明
                bounce_Fixed.show($("#purchaseFunction"));
                break;
            case 'purchaseRecord': // 采购关联设置-采购关联开关记录
                purchaseRecordList();
                break;
        }
    });
    $("#subjectScan").on('click', 'li', function () {
        $(this).addClass('ty-active').siblings().removeClass('ty-active');
        let data = JSON.parse($("#subChangeParam").html());
        data.category = Number($(this).index())+1;
        getChangeSubjects(data);
    });
    $("table").on('click', '.funbtn', function () {
        let fun = $(this).data("fun");
        let info = ``;
        switch (fun) {
            case "changeContent": // 变动内容
				let type = $(this).data("type");
				info = JSON.parse($(this).parents("tr").find(".hd").html());
				if (type === 1) {
					let json = {
						"establish": info.establish,
						"changeDate": new Date(info.changeDate).format('yyyy-MM-dd')
					}
                    $("#re-changeDate").html(new Date(info.changeDate).format('yyyy年MM月dd日'));
                    $("#accountChangeRecord").data("establish", info.establish);
                    $("#accountChangeRecord tbody tr:gt(0)").remove();
                    changeContentScan(json);
                    bounce.show($("#accountChangeRecord"));
                } else if (type === 2) { //变动后的科目
                    afterSubjectChange(1, $(this));
                    showMainCon(4);
                }
                break;
            case "beforeUpdate": //修改前
                //1. id            int    查看修改后时传,否则不传
                //2. previousId    int    修改前时数据ID，否则不传
                subjectUpdateScan(1, $(this));
                break;
            case "afterUpdate": //修改后
                subjectUpdateScan(2, $(this));
                break;
            case "afterAccount": // 建账后的科目
                showMainCon(4);
                afterSubjectChange(0, $(this));
                break;
        }
    });
    $("#purchaseAssociationSet").on("click", ".changeDot", function () {
    	if ($(this).parents(".set-step1").is(":visible")){
            if (!$(this).hasClass("select-active") && $(this).find(".fa-dot-circle-o").length === 0) {
                if ($(this).index() === 0) {
                    $(".set-step2").show().siblings().hide();
                } else {
                    $(this).children("i").attr("class", "fa fa-dot-circle-o");
                }
            }
		} else if ($(this).parent(".set-step2").is(":visible")){
    		if ($(this).find(".fa-dot-circle-o").length > 0) {
    			$(this).children("i").attr("class", "fa fa-circle-o");
            } else {
                $(this).children("i").attr("class", "fa fa-dot-circle-o");
			}
        }
    });
}) ;
// creator : 侯杏哲 2018-04-14 处理新增物料的会计科目
var mtInfo = {} ;
function mtSubjectAdd(messID) {
	$.ajax({
		"url" : "../material/getMtAndSupplierByMid.do" ,
		"data" : { "messageId" : messID  } ,
		success:function(res){
			var mtBase = res["mtBase"] ;
			var mtSupplier = res["mtSupplier"] ;
			if(!mtBase || !mtSupplier){
                bounce.show($("#mtTip")) ; $("#mt_tip_ms").html("该条消息已过期！") ;
                return false ;
			}
            mtInfo = { "mtBase" : mtBase , "mtSupplier" : mtSupplier } ;
            subject_add() ;
        }
	})
}
//creator:孟闯闯，2017-2-17  12:00:00，点击菜单栏的切换并获取数据
//updater:孟闯闯，20174-16  12:00:00，table栏重构（div换成table、tr、td）
function category(obj){
	obj.addClass('ty-active').siblings().removeClass('ty-active');
	var category=obj.attr("value");
	$.ajax({
		url:"../accountant/getSubject.do",
		data:{ category:category  },
		success:function(data){
			if(data.status==1){
				var str="";
				for(var i=0;i<data.list.length;i++){
					var subject=data.list[i].subject;//科目编号
					var name=data.list[i].name;//科目名称
					var indent=(data.list[i].level-1)*2+"em";
					var categoryname=data.list[i].categoryname;//类别名称
					var balance_direction="借";//余额方向
					if(data.list[i].balanceDirection==2){
						balance_direction="贷"
					}
                    // 操作菜单
                    var operate = "<td class='setAddUp fl set_addup ttl_control'>";
					if(data.list[i].creator===0 || data.list[i].creator===undefined || data.list[i].creator===null){ // 基本菜单
						if(data.list[i].level!=4){ // 1 , 2 , 3级菜单
							if(accountantAuth.addSubject){
                                operate += "<a class='ty-color-green' onclick='subject_add($(this))'>新增</a></td>"
							}
						}
					}else{ // 非基本菜单
						if(subject.substr(0,4) == '1002'){
                            if(data.list[i].level =='2'){ // 2 级菜单
                                if(accountantAuth.addSubject){
                                    operate += "<a class='ty-color-green' onclick='subject_add($(this))'>新增</a>" ;
                                }
                                if(accountantAuth.updateSubject){
                                    if(data.list[i].useable == 1){
                                        operate += "<a style='margin-left:5px;' class='ty-color-blue' onclick='subject_update($(this))'>修改</a>" ;
                                    }
                                }
                            }
						}else{
                            if(data.list[i].level!=4){ // 1,2,3 级菜单
                                if(accountantAuth.addSubject){
                                    operate += "<a class='ty-color-green' onclick='subject_add($(this))'>新增</a>" ;
                                }
                                if(accountantAuth.updateSubject){
                                    if(data.list[i].useable == 1){
                                        operate += "<a style='margin-left:5px;' class='ty-color-blue' onclick='subject_update($(this))'>修改</a>" ;
                                    }
                                }
                            }else{ // 四级菜单
                                if(accountantAuth.updateSubject){
                                    if (data.list[i].useable == 1) {
                                        operate += "<a style='margin-left:5px;' class='ty-color-blue' onclick='subject_update($(this))'>修改</a>";
                                    }
                                }
                            }
						}
					}
                    operate += "</td>";

					var state=data.list[i].state;//状态
					if(accountantAuth.controlSubject){
						if(state){
							state="<span class='ty-color-blue' onclick='btnChange($(this))' value='0'>已启用</span>";
						}else{
							state="<span class='ty-color-gray' onclick='btnChange($(this))' value='1'>已禁用</span>";
                            // operate="<td class='setAddUp ttl_control' style='width:20%;'></td>";
						}
					}else{
						if(state){
							state="<span class='ty-color-blue' onclick='no_authorize()' value='0'>已启用</span>";
						}else{
							state="<span class='ty-color-gray' onclick='no_authorize()' value='1'>已禁用</span>";
						}
					}
                    let wid = ``;
                    if (i === 0) {
                        wid = `wids`;
                    }
					str+="<tr>" +
						"<td class='"+ wid +" fl'>"+subject+"</td>" +
						"<td class='fl fl1' style='text-indent:"+indent+";' title='"+name+"'>"+name+"</td>" +
						"<td class='"+ wid +" fl'>"+categoryname+"</td>" +
						"<td class='"+ wid +" fl'>"+balance_direction+"</td>" +
						"<td class='"+ wid +" fl ty-td-control'>"+state+"</td>"+
                        operate +
						// "<div class='clr'></div>" +
						"</tr>"
                    /*str+="<tr>" +
						"<td class='"+ wid +" fl'>"+subject+"</td>" +
						"<td class='fl fl1' style='text-indent:"+indent+";' title='"+name+"'>"+name+"</td>" +
						"<td class='"+ wid +" fl'>"+categoryname+"</td>" +
						"<td class='"+ wid +" fl'>"+balance_direction+"</td>" +
						"<td class='"+ wid +" fl ty-td-control'>"+state+"</td>"+
                        operate +
						// "<div class='clr'></div>" +
						"</tr>"*/
					/*str+="<tr>" +
						"<td class='fl' width='15%;'>"+subject+"</td>" +
						"<td class='fl fl1' width='20%' style='text-indent:"+indent+";'title='"+name+"'>"+name+"</td>" +
						"<td class='fl' width='15%'>"+categoryname+"</td>" +
						"<td class='fl' width='15%'>"+balance_direction+"</td>" +
						"<td class='fl ty-td-control' width='15%'>"+state+"</td>"+
                        operate +
						// "<div class='clr'></div>" +
						"</tr>"*/
				}
				$("#set_body").html(str);
				if(operate==""){
					$("#theader").children("div").children("div").eq(5).remove();
				}
			}else{
				alert("获取失败，请重试")
			}
		}
    });
}
//creator:孟闯闯，2017-2-17  12:00:00，禁用or启用按钮
function btnChange(obj){
	$("#mt_tip_ms2").attr("value",obj.attr("value")) ; // 禁用还是启用的传值
	var str="您确认启用吗？";
	if(obj.attr("value")==0){
		str="您确认禁用吗？" ;
	}
	$("#mt_tip_ms2").html(str);//温馨提示
	$("#mtTip2").attr("value",obj.parent().parent().children("td").eq(0).html());//科目号
	bounce.show($("#mtTip2"))
}
//creator:孟闯闯，2017-2-17  12:00:00，启用or禁用确定
function changeSure(){
	var subject=$("#mtTip2").attr("value");
	var state=$("#mt_tip_ms2").attr("value");
	$.ajax({
		url:"../accountant/changeSubjectState.do",
		data:{
			subject:subject,
			state:state
		},
		type:"post",
		dataType:"json",
        beforeSend:function(){
            loading.open()
        },
    	success:function(data){
			if(data.status==1){
				bounce.cancel();
				$("#mainTab .ty-active").click();
			}else if(data.status==0){
                $("#errorTip .tipWord").html("因为不知名原因，你修改失败了！");
                bounce.show($("#errorTip"));
			}else if(data.status==2){
                $("#errorTip .tipWord").html("此科目正在被使用,不能被禁用！");
                bounce.show($("#errorTip"));
			}

		},
		error:function(){
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
		},
        complete:function(){
            loading.close() ;
        },
	})
}
//creator:孟闯闯，2017-2-17  12:00:00，点击新增的弹框
//updator:lyt，2018-10-29  14:00:00
function subject_add(obj){
	$("#subject_addAccount .addpayDetails div").children("p").eq(1).children("input").val("");//新增科目名称
	var measure_unit=$("#subject_addAccount .addpayDetails div").children("p").eq(5).children("input").val("");//计量单位
	if(obj){ // 新增下级科目
        var subject=obj.parent().parent().children("td").eq(0).html();
        $.ajax({
            url:"../accountant/clickAdd.do",
            data: {subject: subject},
            success:function(data){
                if(data.status==1){
                    var newSubject=data.list[0].newSubject;		//新增子科目编号
                    var subject=data.list[0].subject;			//上级科目编号
                    var name=data.list[0].name;					//上级科目名称
                    var categoryname=data.list[0].categoryname;	//上级类别名称
                    var direction="借";							//余额方向
                    if(data.list[0].direction==2){
                        direction="贷"
                    };
                    $("#subject_addAccount .addpayDetails div").children("p").eq(0).children("input").val(newSubject);
                    $("#subject_addAccount .addpayDetails div").children("p").eq(2).children("input").val(subject+" "+name);
                    $("#subject_addAccount .addpayDetails div").children("p").eq(3).children("input").val(categoryname);
                    $("#subject_addAccount .addpayDetails div").children("p").eq(4).children("input").val(direction);
                    bounce.show($("#subject_addAccount"));
                }else if(data.status==2){
                    $("#errorTip .tipWord").html("请先到财务模块中新操作！");
                    bounce.show($("#errorTip"));
				}else{
                    $("#errorTip .tipWord").html("此科目的上限已达到最大！");
                    bounce.show($("#errorTip"));
                }
            },
            error:function(){
                $("#errorTip .tipWord").html("系统错误,请重试！");
                bounce.show($("#errorTip"));
            } ,
            beforeSend:function(){ loading.open(); },
            complete:function () {  loading.close();  }
        });
	}else{ // 从物料过来的
		$.ajax({
			"url" :"getSpecifySubjectInfo.do" ,
			success:function (res) {
				bounce.show( $("#mtSubject_addAccount") ) ;
                $("#checkNum").click();
				var subjectInfo = res["subjectInfo"] ;
				if(subjectInfo && subjectInfo.length == 2){
                    var name = mtInfo["mtSupplier"]["name"] ;
                    var unit = mtInfo["mtBase"]["unit"] ;
                    $("#mtSubjectName").val( name ) ;
                    $("#mtSubjectUnit").val( unit ) ;
					var memo =  subjectInfo[0]["memo"] ;  memo = JSON.parse(memo) ;
                    var yu = memo[0]["direction"] ;
					if(yu == 1){ yu = "借" ; }else{   yu = "贷" ;  }
                    $("#mtSubjectNo").val(memo[0]["newSubject"]) ;
                    $("#mtSubjectCat").val(memo[0]["categoryname"]) ;
                    $("#mtSubjectYu").val( yu ) ;
                    var subStr = "" ;
                    for(var i = 0 ; i < 2 ; i++){
                        subStr += "<option value='"+subjectInfo[i]["memo"] +"'>"+ subjectInfo[i]["name"] +"</option>" ;
					}
					$("#pSubjects").html( subStr ) ;
				}else {
                    bounce.show($("#mtTip")) ; $("#mt_tip_ms").html("获取数据不完整！") ;
				}
            }
		})
	}

}
//creator:侯杏哲，2018-04-19  确定新增科目 （从物料过来的）
function subject_mtAddSure() {
	var memo = $("#pSubjects").val() ; memo = JSON.parse(memo) ;
	var data = {
        "supplier" : mtInfo["mtSupplier"]["name"] ,
        "material" : mtInfo["mtBase"]["name"] + mtInfo["mtBase"]["code"] + mtInfo["mtSupplier"]["name"] ,
		"supplierSubject" :  $("#mtSubjectNo").val() ,
		"supplierParentSubject" : memo[0]["subject"] ,
        "relevanceSupplierType": 2 ,  //	供应商关联类型:1-商品，2-物料，3-供应商
    	"relevanceSupplierItem":  mtInfo["mtSupplier"]["id"] ,  //	供应商关联项的id,根据代码去不同的表中查,商品去找商品表找id，物料去找物料表id
    	"relevanceMaterialType" : 2 , //	物料关联类型:1-商品，2-物料，3-供应商
    	"relevanceMaterialItem" : mtInfo["mtBase"]["id"] ,	//  物料商关联项的id,根据代码去不同的表中查,商品去找商品表找id，物料去找物料表id
    	"measureUnit" : $("#mtSubjectUnit").val() ,  // 	物料单位
    	"messageid" : getUrlParam("id")  // 	消息 id
    } ;
	$.ajax({
		"url" : "../accountant/generateSubjects.do" ,
		"data" : data ,
		"type" : "post" ,
		"dataType" : "json" ,
		beforeSend : function () { loading.open() ; } ,
		success:function (data) {
			var  res = data["res"] ;  //  0-失败    1-成功    2-已存在改科目
			if(res == 1){
                bounce.show($("#mtTip")) ; $("#mt_tip_ms").html("创建科目成功！") ;
                setTimeout(function(){
                	location.href = "../accountant/subjectSet.do" ;
				} , 500)
			}else if(res == 2){
                bounce.show($("#mtTip")) ; $("#mt_tip_ms").html("已存在该科目！") ;
			}else{
                bounce.show($("#mtTip")) ; $("#mt_tip_ms").html("新增科目失败！") ;
			}
        } ,
		error:function () {
            bounce.show($("#mtTip")) ; $("#mt_tip_ms").html("连接失败！") ;
        },
		complete:function () {
			loading.close() ;
        }
	})
	
}
//creator:侯杏哲，2018-04-19   选择父级科目 - 从物料过来的
function setSelectVal(_thisObj ){
	var memo = _thisObj.val() ; memo = JSON.parse(memo) ;
    var yu = memo[0]["direction"] ;
    if(yu == 1){ yu = "借" ; }else{   yu = "贷" ;  }
    $("#mtSubjectNo").val(memo[0]["newSubject"]) ;
    $("#mtSubjectCat").val(memo[0]["categoryname"]) ;
    $("#mtSubjectYu").val( yu ) ;
}
//creator:孟闯闯，2017-2-17  12:00:00，新增弹框中的确定按钮
function subject_addsure(){
	var newsubject=$("#subject_addAccount .addpayDetails div").children("p").eq(0).children("input").val();//新增科目编号
	var parent=parseInt($("#subject_addAccount .addpayDetails div").children("p").eq(2).children("input").val());//上级科目编号
	var name=$("#subject_addAccount .addpayDetails div").children("p").eq(1).children("input").val();//新增科目名称
	var measure_unit=$("#subject_addAccount .addpayDetails div").children("p").eq(5).children("input").val();//计量单位
    var checkNum = $("#subject_addAccount .checkNum").hasClass("ty-form-checked");
	if(name==""){
        $("#mt_tip_ms").html("请填写科目名称！");
        bounce.show($("#mtTip"));
	}else{
	    var data={
            newsubject:newsubject,
            parent:parent,
            name:name
        };
	    if(checkNum){
	        data["quantityAssistingAccounting"] = "1";
	        data["measure_unit"] = measure_unit;
        }else{
            data["quantityAssistingAccounting"] = "0";
        }
		$.ajax({
			url:"../accountant/addSubject.do",
			data:data,
			type:"post",
			dataType:"json",
			success:function(data){
				if(data.status==1){
					bounce.cancel();
					$("#mainTab .ty-active").click();
				}else{
                    $("#errorTip .tipWord").html("新增失败！");
                    bounce.show($("#errorTip"));
				}
			},
			error:function(){
                $("#errorTip .tipWord").html("系统错误,请重试！");
                bounce.show($("#errorTip"));
			} ,
            beforeSend:function(){ loading.open(); },
            complete:function () {  loading.close();  }
		})
	}

}
//creator:孟闯闯，2017-2-17  12:00:00，点击修改的弹框
function subject_update(obj){
	var subject=obj.parent().parent().children("td").eq(0).html();
	$.ajax({
		url:"../accountant/clickUpdate.do",
		data:{
			subject:subject
		},
		type:"post",
		dataType:"json",
		success:function(data){
			if(data.status==1){
				bounce.show($("#subject_upAccount"))
				var subject=data.data.subject;//科目编号
				var name=data.data.name//科目名称
				var parent=data.data.parent//上级科目编号
				var parentName=data.parentName//上级科目名称
				var categoryname=data.data.categoryname//类别名称
				var direction="借";//余额方向
				var measure_unit=data.data.measureUnit;
                var quantityAssistingAccounting = data.data.quantityAssistingAccounting;
                var quantity = data.data.quantity;
				if(data.data.balanceDirection==2){
					direction="贷"
				} ;
				var measure_unit=data.data.measureUnit;//计量单位
				if (!measure_unit && typeof(measure_unit)!="undefined" && measure_unit!=0){
					measure_unit="";
				}
				let curObj = $("#subject_upAccount .addpayDetails div");
                curObj.children("p").eq(1).data("gqName", name);
                curObj.children("p").eq(1).data("level", data.data.level);
                curObj.children("p").eq(2).data("parent", parent);
                curObj.children("p").eq(4).data("direction", data.data.balanceDirection);
                curObj.children("p").eq(0).children("input").val(subject);
				curObj.children("p").eq(1).children("input").val(name);
				curObj.children("p").eq(2).children("input").val(parent+"    "+parentName);
				curObj.children("p").eq(3).children("input").val(categoryname);
				curObj.children("p").eq(4).children("input").val(direction);
				curObj.children("p").eq(5).children("input").val(measure_unit);
                if(quantityAssistingAccounting === "1"){
                    $(".checkNum").addClass("ty-form-checked");
                    $(".checkNum").parent().next().show();
                    $("#subject_addAccount .quantity").val(quantity);
                }else if(quantityAssistingAccounting === 0){
                    $(".checkNum").removeClass("ty-form-checked");
                    $(".checkNum").parent().next().hide();
                    $("#subject_addAccount .quantity").val("");
                }
			}else if(data.status==0){
                $("#errorTip .tipWord").html("此科目已经被选择，不能修改！");
                bounce.show($("#errorTip"));
			}else if(data.status==2){
                $("#errorTip .tipWord").html("请先到财务模块中新操作！");
                bounce.show($("#errorTip"));
            }else{
                $("#errorTip .tipWord").html("修改失败！");
                bounce.show($("#errorTip"));
			}
		},
		error:function(){
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
		} ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
	});
	// $("#subject_upAccount").show().siblings().hide().parent().show();
}
//creator:孟闯闯，2017-2-17  12:00:00，修改弹框中的确定按钮
//updater:张旭博，2017-3-03  09:27:00，删除计量单位输入判断、删除measure_unit获取的计量单位输入框的值
function subjectup_sure(){
    let curArr = $("#subject_upAccount .addpayDetails div p");
	var subject=curArr.eq(0).children("input").val();//科目编号
	var name=curArr.eq(1).children("input").val();//科目名称
	var gqName=curArr.eq(1).data("gqName");//修改前的科目名称
    let level =    curArr.eq(1).data("level");
    var parent=curArr.eq(2).data("parent")//上级科目
    var balanceDirection =curArr.eq(4).data("direction")//上级科目
	var measure_unit=curArr.eq(6).children("input").val();//计量单位
    var checkNum = $("#subject_upAccount .checkNum").hasClass("ty-form-checked");
    if(name==""){
        $("#mt_tip_ms").html("请输入科目名称！");
        bounce.show($("#mtTip"));
	}else{
        var param={
            level:level,
            gqName:gqName,
            parent:parent,
            subject:subject,
            balanceDirection:balanceDirection,
            name:name
        };
        if(checkNum){
            param["quantityAssistingAccounting"] = "1";
            param["measureUnit"] = measure_unit;
        }else{
            param["quantityAssistingAccounting"] = "0";
        }
		$.ajax({
			url:"../accountant/updateSubject.do",
			data:param,
			type:"post",
			dataType:"json",
			success:function(data){
				if(data.status==1){
					bounce.cancel();
					$("#mainTab .ty-active").click();
				}
			},
			error:function(){
                $("#errorTip .tipWord").html("系统错误,请重试！");
                bounce.show($("#errorTip"));
			} ,
            beforeSend:function(){ loading.open(); },
            complete:function () {  loading.close();  }
		})
	}

}
//creator:孟闯闯，2017-2-17  12:00:00，没有权限的提示
function no_authorize(){
    $("#errorTip .tipWord").html("抱歉，您没有该权限！");
    bounce.show($("#errorTip"));
}
// creator:孟闯闯，2017-4-17  12:00:00，一个对齐的滚动条
function startRequest(){
	if( $("#set_body").children("tr").eq(0).length > 0 ){
		$("#set_body").prev().width($("#set_body").children("tr").eq(0)[0].clientWidth);
	}
    if( $(".setAlign").children("tr").eq(0).length > 0 ){
        $(".setAlign").prev().width($(".setAlign").children("tr").eq(0)[0].clientWidth);
    }
}
//setInterval("startRequest()",10);
//creator:侯杏哲，2018-03-08  判断是否启用 科目名称关联功能
function chargeOpen(){
    $("#purchaseAssociationSet .set-step1").show().siblings().hide();
    $("#purchaseAssociationSet .changeDot").children("i").attr("class", "fa fa-circle-o");
    $.ajax({
        "url" : "../accountant/getRelation.do" ,
        success:function (data) {
            var res = data["res"] ;
            let status = ``, num = 0;
			if(res ==0){ // 开始
                num = 1;
                status = `关闭`;
			}else{
                status = `开启`;
			}
            $("#purchaseAssociationSet .set-step1 .changeDot").eq(num).addClass("select-active").siblings().removeClass("select-active") ;
            $("#recentStatus").html(status) ;
            bounce.show($("#purchaseAssociationSet"));
        }
    });
}
//creator:侯杏哲，2018-03-08  启用科目名称关联功能
function showOpen() {
	bounce.show($("#open")) ;
}
// creator:侯杏哲，2018-03-08  确定启用 物料与会计关联
function linkBtn() {
    bounce.cancel();
	$.ajax({
		"url" : "../accountant/setRelation.do" ,
		success:function (data) {
			var res = data["res"] ;
			if(res == 1){
                $("#openLink").hide() ;
                category($(".mainCon1 .ty-secondTab .ty-active")) ;
                bounce.show($("#mtTip")) ; $("#mt_tip_ms").html("启用科目名称关联功能成功") ;
            }else{
                bounce.show($("#mtTip")) ; $("#mt_tip_ms").html("启用科目名称关联功能失败") ;
			}
        }
	});
}
// creator:侯杏哲，2018-03-09 切换没用的显示
function faChange(num) {
	$("#fa"+ num).children(".fa").attr("class" , "fa fa-check-square-o") ;
	if(num == 1){
        $("#fa2").children(".fa").attr("class" , "fa fa-square-o") ;
	}else{
        $("#fa1").children(".fa").attr("class" , "fa fa-square-o") ;
	}
}
// creator:lyt，2018-06-21 科目管理 num==1：已建账；2：未建账
function setUpBill(num,str){
	//公共显示
    bounce.cancel();
	if(num == "1"){
		//已建账
        $(".main_sub").show();
        $(".main_input").hide();
        $(".settingTip").show();
        $(".subject-save").show();
        $(".setSubOverBtn").show().siblings().hide();
    }else if(num == "2"){
		//未建账
        $(".settingTip").show();
        $(".subject-save").show();
        $(".setOverBtn").show().siblings().hide();
    }else if(num == "3"){
        $(".editBtnGroup").show();
        $(".subject-save").hide();
        $(".main_sub").hide();
        getFinalLevelSubject();
        $(".main_input").show();
	}else if(num == "4"){
        //chargeOpen(); // 是否显示启用 科目名称关联功能 按钮
	}
/*	if(str == "first"){
        setBuildAccountState(num);
	}
	if(str == "return"){
        $(".today").html($(".timeLine").html());
	}*/
}

// creator:lyt，2018-06-21 科目管理 暂存/金额核查所需的参数
function getNeedParameter(){
    // var tab = $("#enterInitFinance").attr("catoType");
    var saveData={};
    var listSubjects = [];
    var cashFlow = [];
    var arr = [];
    var cashFlowNum = 0;
    $("#enterInitFinance input").each(function () {
        if($(this).attr("val-init") == "1"){
            var id = $(this).parents("tr").attr("id");
            /*if($(this).val() !== "0"){
                arr.push(id);
            }*/
            arr.push(id);
        }
    });
    for(var i = 0; i < arr.length; i++){
        if(i === 0 || arr[i] !== arr[i-1]){
            var subjectItem = {};
            var tr = $("#"+arr[i]).children();
            subjectItem.id = arr[i];
            var count = 0;
            if(tr.eq(4).find("input").length){
                if(tr.eq(4).find("input").attr("val-init") =="1"){
                    count++;
                    subjectItem.beginningQuantity= tr.eq(4).find("input").val();
                }
            }
            if(tr.eq(6).find("input").length){
                if(tr.eq(6).find("input").attr("val-init") =="1"){
                    count++;
                    subjectItem.creditAccumulativeQuantity= tr.eq(6).find("input").val();
                }
            }
            if(tr.eq(8).find("input").length){
                if(tr.eq(8).find("input").attr("val-init") =="1"){
                    count++;
                    subjectItem.debitAccumulativeQuantity= tr.eq(8).find("input").val();
                }
            }
            if(tr.eq(10).find("input").length){
                if(tr.eq(10).find("input").attr("val-init") =="1"){
                    count++;
                    subjectItem.quantity= tr.eq(10).find("input").val();
                }
            }
            if(tr.eq(5).find("input").attr("val-init") =="1"){
                count++;
                subjectItem.beginningBalance = tr.eq(5).find("input").val();
            }
            if(tr.eq(7).find("input").attr("val-init") =="1"){
                count++;
                subjectItem.creditAccumulative = tr.eq(7).find("input").val();
            }
            if(tr.eq(9).find("input").attr("val-init") =="1"){
                count++;
                subjectItem.debitAccumulative = tr.eq(9).find("input").val();
            }
            if(tr.eq(11).find("input").attr("val-init") =="1"){
                count++;
                subjectItem.balance = tr.eq(11).find("input").val();
            }
            if(count != 0){
                subjectItem.balanceDirection = $("#"+arr[i]).attr("direction");
                listSubjects.push(subjectItem);
            }
        }
    }


    /*$("#enterInitFinance tr").each(function(){
    	if($(this).attr("val-init") == 1){
            var subjectItem = {};
            var tr = $(this).children();
            subjectItem.beginningQuantity= tr.eq(4).find("input").val();
            subjectItem.creditAccumulativeQuantity= tr.eq(6).find("input").val();
            subjectItem.debitAccumulativeQuantity= tr.eq(8).find("input").val();
            subjectItem.quantity= tr.eq(10).find("input").val();

            if(tr.eq(5).find("input").length>0){
            	if(tr.eq(5).find("input").val()!="0")
                subjectItem.beginningBalance = tr.eq(5).find("input").val();
            }else{
                subjectItem.beginningBalance = 0;
            }
            if(tr.eq(7).find("input").length>0){
            	if(tr.eq(7).find("input").val()!="0")
                subjectItem.creditAccumulative = tr.eq(7).find("input").val();
            }else{
                subjectItem.creditAccumulative = 0;
            }
            if(tr.eq(9).find("input").length>0){
            	if(tr.eq(9).find("input").val()!="0")
                subjectItem.creditAccumulative = tr.eq(9).find("input").val();
            }else{
                subjectItem.creditAccumulative = 0;
            }
            if(tr.eq(11).find("input").length>0){
            	if(tr.eq(11).find("input").val()!="0")
                subjectItem.balance = tr.eq(11).find("input").val();
            }else{
                subjectItem.balance = 0;
            }
            subjectItem.balanceDirection = $(this).attr("direction");
            listSubjects.push(subjectItem);
		}
	});*/
    listSubjects = JSON.stringify(listSubjects);
    //cashFlow:录入的现金流量表的数据（每次都只穿有值的列，如果没有值被改变则不传）
    // cashFlowCode：项目编码
    // accumulative：本年累计

    $("#caseTable input").each(function(){
        if($(this).attr("val-init") == "1"){
            var rowNum = $(this).attr("row");
            var caseData = {};
            cashFlowNum++;
            caseData.cashFlowCode = "r"+rowNum+"c4";
            caseData.accumulative = $(this).val();
            cashFlow.push(caseData);
        }
    });
    cashFlow = JSON.stringify(cashFlow);
    saveData.cashFlow = cashFlow;
    saveData.listSubjects = listSubjects;
    return saveData;
}

// creator:lyt，2018-06-21 科目管理 暂存处理
function temporarySave(){
    var param = getNeedParameter();
    $.ajax({
        "url": "../accountant/saveBuildAccount.do",
        "data": param,
        success: function (data) {
            assetArr =[],debtArr =[],ownerArr=[],costArr=[],profitArr =[];
            getFinalLevelSubject();
        }
    });
}
// creator:lyt，2018-06-21 科目管理 余额录入
function amountTab(obj) {
	var html = '';
    obj.addClass('ty-active').siblings().removeClass('ty-active');
    $("#enterInitFinance").attr("catoType",obj.attr("value"));
    if(obj.attr("value")=="6"){
    	$("#caseStatetable").show().siblings().hide();
        $("#caseTable").prev().width($("#caseTable").children("tr").eq(0)[0].clientWidth);
	}else{
        if(obj.attr("value")=="1"){
            for(var b=0;b<assetArr.length;b++){
            	var aNum = assetArr[b].quantityAssistingAccounting;
                html +=
                    '<tr id="'+assetArr[b].id+'" direction="'+ assetArr[b].balanceDirection +'">' +
                    '    <td style="width: 10%">'+ assetArr[b].subject +'</td>' +
                    '    <td style="width: 10%">'+ assetArr[b].name +'</td>' +
                    '    <td style="width: 8%">'+ assetArr[b].categoryname +'</td>' +
                    '    <td style="width: 8%">'+ transformLoan(assetArr[b].balanceDirection) +'</td>' + //借、贷
                    '    <td style="width: 8%">'+ converFormate(aNum,assetArr[b].beginningQuantity) +'</td>' + //年初数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" style="width: 100%;" type="text" value="'+ formatMoney(assetArr[b].beginningBalance) + '" data-init="'+ formatMoney(assetArr[b].beginningBalance) + '" onkeyup="sub_clearNoNum(this)"/></td>' +
                    '    <td style="width: 8%">'+ converFormate(aNum,assetArr[b].creditAccumulativeQuantity ) +'</td>' + //本年累计借方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" style="width: 100%;" type="text" value="'+ formatMoney(assetArr[b].creditAccumulative) +'" data-init="'+ formatMoney(assetArr[b].creditAccumulative) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计借方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,assetArr[b].debitAccumulativeQuantity ) +'</td>' +//本年累计贷方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" style="width: 100%;" type="text" value="'+ formatMoney(assetArr[b].debitAccumulative) +'" data-init="'+ formatMoney(assetArr[b].debitAccumulative) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计贷方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,assetArr[b].quantity) +'</td>' +//期末数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" style="width: 100%;" type="text" value="'+ formatMoney(assetArr[b].balance) +'" data-init="'+ formatMoney(assetArr[b].balance) +'" onkeyup="sub_clearNoNum(this)"/></td>' +//期末余额
                    '</tr>';
            }
        }else if(obj.attr("value")=="2"){
            for(var t=0;t<debtArr.length;t++){
                var aNum = debtArr[t].quantityAssistingAccounting;
                html +=
                    '<tr id="'+debtArr[t].id+'" direction="'+ debtArr[t].balanceDirection +'">' +
                    '    <td style="width: 10%">'+ debtArr[t].subject +'</td>' +
                    '    <td style="width: 10%">'+ debtArr[t].name +'</td>' +
                    '    <td style="width: 8%">'+ debtArr[t].categoryname +'</td>' +
                    '    <td style="width: 8%">'+ transformLoan(debtArr[t].balanceDirection) +'</td>' + //借、贷
                    '    <td style="width: 8%">'+ converFormate(aNum,debtArr[t].beginningQuantity) +'</td>' + //年初数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(debtArr[t].beginningBalance) +'" data-init="'+formatMoney(debtArr[t].beginningBalance) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //年初余额
                    '    <td style="width: 8%">'+ converFormate(aNum,debtArr[t].creditAccumulativeQuantity ) +'</td>' + //本年累计借方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(debtArr[t].creditAccumulative) +'" data-init="'+formatMoney(debtArr[t].creditAccumulative) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计借方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,debtArr[t].debitAccumulativeQuantity ) +'</td>' +//本年累计贷方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(debtArr[t].debitAccumulative)  +'" data-init="'+formatMoney(debtArr[t].debitAccumulative)  +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计贷方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,debtArr[t].quantity) +'</td>' +//期末数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(debtArr[t].balance) +'" data-init="'+formatMoney(debtArr[t].balance) +'" onkeyup="sub_clearNoNum(this)"/></td>' +//期末余额
                    '</tr>';
            }
        }else if(obj.attr("value")=="3"){
            for(var e=0;e<ownerArr.length;e++){
                var aNum = ownerArr[e].quantityAssistingAccounting;
                html +=
                    '<tr id="'+ownerArr[e].id+'" direction="'+ ownerArr[e].balanceDirection +'">' +
                    '    <td style="width: 10%">'+ ownerArr[e].subject +'</td>' +
                    '    <td style="width: 10%">'+ ownerArr[e].name +'</td>' +
                    '    <td style="width: 8%">'+ ownerArr[e].categoryname +'</td>' +
                    '    <td style="width: 8%">'+ transformLoan(ownerArr[e].balanceDirection) +'</td>' + //借、贷
                    '    <td style="width: 8%">'+ converFormate(aNum,ownerArr[e].beginningQuantity) +'</td>' + //年初数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(ownerArr[e].beginningBalance) +'" data-init="'+formatMoney(ownerArr[e].beginningBalance) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //年初余额
                    '    <td style="width: 8%">'+ converFormate(aNum,ownerArr[e].creditAccumulativeQuantity ) +'</td>' + //本年累计借方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(ownerArr[e].creditAccumulative) +'" data-init="'+formatMoney(ownerArr[e].creditAccumulative) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计借方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,ownerArr[e].debitAccumulativeQuantity ) +'</td>' +//本年累计贷方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(ownerArr[e].debitAccumulative) +'" data-init="'+formatMoney(ownerArr[e].debitAccumulative) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计贷方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,ownerArr[e].quantity) +'</td>' +//期末数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(ownerArr[e].balance) +'" data-init="'+formatMoney(ownerArr[e].balance) +'" onkeyup="sub_clearNoNum(this)"/></td>' +//期末余额
                    '</tr>';
            }
        }else if(obj.attr("value")=="4"){
            for(var w=0;w<costArr.length;w++){
                var aNum = costArr[w].quantityAssistingAccounting;
                html +=
                    '<tr id="'+costArr[w].id+'" direction="'+ costArr[w].balanceDirection +'">' +
                    '    <td style="width: 10%">'+ costArr[w].subject +'</td>' +
                    '    <td style="width: 10%">'+ costArr[w].name +'</td>' +
                    '    <td style="width: 8%">'+ costArr[w].categoryname +'</td>' +
                    '    <td style="width: 8%">'+ transformLoan(costArr[w].balanceDirection) +'</td>' + //借、贷
                    '    <td style="width: 8%">'+ converFormate(aNum,costArr[w].beginningQuantity) +'</td>' + //年初数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(costArr[w].beginningBalance) +'" data-init="'+formatMoney(costArr[w].beginningBalance) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //年初余额
                    '    <td style="width: 8%">'+ converFormate(aNum,costArr[w].creditAccumulativeQuantity ) +'</td>' + //本年累计借方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(costArr[w].creditAccumulative) +'" data-init="'+formatMoney(costArr[w].creditAccumulative) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计借方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,costArr[w].debitAccumulativeQuantity ) +'</td>' +//本年累计贷方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(costArr[w].debitAccumulative) +'" data-init="'+formatMoney(costArr[w].debitAccumulative) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计贷方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,costArr[w].quantity) +'</td>' +//期末数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(costArr[w].balance) +'" data-init="'+formatMoney(costArr[w].balance) +'" onkeyup="sub_clearNoNum(this)"/></td>' +//期末余额
                    '</tr>';
            }
        }else if(obj.attr("value")=="5"){
            for(var r=0;r<profitArr.length;r++){
                var aNum = profitArr[r].quantityAssistingAccounting;
                html +=
                    '<tr id="'+profitArr[r].id+'" direction="'+ profitArr[r].balanceDirection +'">' +
                    '    <td style="width: 10%">'+ profitArr[r].subject +'</td>' +
                    '    <td style="width: 10%">'+ profitArr[r].name +'</td>' +
                    '    <td style="width: 8%">'+ profitArr[r].categoryname +'</td>' +
                    '    <td style="width: 8%">'+ transformLoan(profitArr[r].balanceDirection) +'</td>' + //借、贷
                    '    <td style="width: 8%">'+ converFormate(aNum,profitArr[r].beginningQuantity ) +'</td>' + //年初数量
                    '    <td style="width: 8%"><input val-init="0" data-init="'+formatMoney(profitArr[r].beginningBalance) +'" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(profitArr[r].beginningBalance) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //年初余额
                    '    <td style="width: 8%">'+ converFormate(aNum,profitArr[r].creditAccumulativeQuantity ) +'</td>' + //本年累计借方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+formatMoney(profitArr[r].creditAccumulative) +'"  data-init="'+formatMoney(profitArr[r].creditAccumulative) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计借方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,profitArr[r].debitAccumulativeQuantity ) +'</td>' +//本年累计贷方数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+ formatMoney(profitArr[r].debitAccumulative) +'" data-init="'+ formatMoney(profitArr[r].debitAccumulative) +'" onkeyup="sub_clearNoNum(this)"/></td>' + //本年累计贷方金额
                    '    <td style="width: 8%">'+ converFormate(aNum,profitArr[r].quantity) +'</td>' +//期末数量
                    '    <td style="width: 8%"><input val-init="0" onblur="newCompareOld($(this))" type="text" value="'+ formatMoney(profitArr[r].balance) +'" data-init="'+ formatMoney(profitArr[r].balance) +'" onkeyup="sub_clearNoNum(this)"/></td>' +//期末余额
                    '</tr>';
            }
        }
        $("#caseStatetable").hide().siblings().show();
        $("#enterInitFinance").html(html);
	}
}
// creator:lyt，2018-06-21 科目管理 金额核查
var assetCheck ="",debtCheck ="",ownerCheck="",costCheck="",profitCheck ="";
var balanceList = [];
function amountCheck(){
    assetCheck ="",debtCheck ="",ownerCheck="",costCheck="",profitCheck ="";
    var param = getNeedParameter();
    var period = "";
    $.ajax({
        "url": "../accountant/amountVerification.do",
        "data": param,
        success: function (res) {
            var listResult = res["data"]["listResult"];
            period = res["data"]["period"];
            balanceList = res["data"]["balanceComparison"];
            $(".cashSheetCheck").attr("paramDate",period);
            $(".needChangeNum").html(res["data"]["totalDifferent"]);
            if(res["data"]["totalDifferent"] >0){
                $("#buildOverBtn").attr({"class":"ty-btn ty-btn-gray ty-btn-big ty-circle-5","can":"off"}).removeAttr("onclick");
			}else{
            	$("#buildOverBtn").attr({"class":"ty-btn ty-btn-green ty-btn-big ty-circle-5","can":"ok","onclick":"bounce.show($('#finishTip'))"});
            }
            if(balanceList && balanceList.length>0){
            	var sysDate = new Date(period);
            	var thisMonth = sysDate.getMonth();
            	if(thisMonth == "11"){
                    thisMonth = 1;
				}else{
                    thisMonth = thisMonth + 2;
				}
                $(".balanceTip").show().attr({"onclick":"balancedetail()"});
                $("#balanceTip .period").html(sysDate.format("yyyy年MM月"));
                $("#balanceTip .sameMonth").html(thisMonth);
                $("#balanceTip .tb-period").html(sysDate.format("yyyy年MM月")+'余额');
            }else{
                $(".balanceTip").hide().removeAttr("onclick");
            }
            if(listResult && listResult.length>0){
                for(var y=0;y<listResult.length;y++){
                    var tempVarb = '<tr style="display:block;">' +
                        '<td style="display:inline-block;width:9%">'+ listResult[y].subject +'</td>' +
                        '<td class="ellipsisSet" style="display:inline-block;width:8%" title="'+ listResult[y].name +'">'+ listResult[y].name +'</td>' +
                        '<td style="display:inline-block;width:8%">'+ listResult[y].categoryname +'</td>' +
                        '<td style="display:inline-block;width:6%">'+transformLoan(listResult[y].balanceDirection)+'</td>' +
                        '<td style="display:inline-block;width:8%">'+ delNull(listResult[y].beginningQuantity) +'</td>' +
                        '<td style="display:inline-block;width:9%">'+ formatMoney(listResult[y].beginningBalance) +'</td>' +
                        '<td style="display:inline-block;width:8%">'+ delNull(listResult[y].creditAccumulativeQuantity) +'</td>' +
                        '<td style="display:inline-block;width:9%">'+ formatMoney(listResult[y].creditAccumulative) +'</td>' +
                        '<td style="display:inline-block;width:8%">'+ delNull(listResult[y].debitAccumulativeQuantity) +'</td>' +
                        '<td style="display:inline-block;width:9%">'+ formatMoney(listResult[y].debitAccumulative) +'</td>' ;
                    if(!listResult[y].quantityDifferent){
                        tempVarb +='<td style="display:inline-block;width:8%" class="warningErr">'+ delNull(listResult[y].quantity) +'</td>';
                    }else{
                        tempVarb +='<td style="display:inline-block;width:8%">'+ delNull(listResult[y].quantity) +'</td>';
                    }
                    if(!listResult[y].balanceDifferent){
                        tempVarb +='<td style="display:inline-block;width:9%" class="warningErr">'+ formatMoney(listResult[y].balance) +'</td>';
                    }else{
                        tempVarb +='<td style="display:inline-block;width:9%">'+ formatMoney(listResult[y].balance) +'</td>';
                    }
                    tempVarb +='</tr>';
                    if(listResult[y].category =="6" ||listResult[y].category =="7"){
                        assetCheck += tempVarb;
                    }else if(listResult[y].category =="8" ||listResult[y].category =="9") {
                        debtCheck += tempVarb;
                    }else if(listResult[y].category =="10"){
                        ownerCheck += tempVarb;
                    }else if(listResult[y].category =="11"){
                        costCheck += tempVarb;
                    }else if(listResult[y].category =="12"||listResult[y].category =="13"||listResult[y].category =="14"||listResult[y].category =="15"||listResult[y].category =="16"||listResult[y].category =="17"){
                        profitCheck += tempVarb;
                    }
                }
            }
            amountCheckTab($(".amount-body ul li").eq(0));
            bounce.show($('#amountCheck'));
            getFinalLevelSubject();
        }
    });
    // $("#checkTab2 thead").width($("#checkTab2").parent().eq(0)[0].clientWidth);
    var caseStr = initCashData();
    $("#caseListCheck").html(caseStr);
}
// creator:lyt，2018-06-21 科目管理 金额核查切换
function amountCheckTab(obj) {
	var val = obj.attr("value");
	if(val == "6"){
        $("#checkTab2").show().siblings().hide();
        var dateRes = $(".cashSheetCheck").attr("paramDate");
        if(dateRes == ""){
            dateRes = null;
        }
        $.ajax({
            url:"getCashFlowData.do" ,
            data:{ "period": dateRes } ,
            type:"post" ,//
            dataType:"json" ,
            success:function(data){
                if(data["code"] == 1){
                    var list = data["data"];
                    if(list.length>0){
                        for(var i = 0; i<list.length ;i++){
                            var cell = list[i].cellCode;
                            var accumulative = delNull(list[i].accumulative);
                            var amount = delNull(list[i].amount);
                            var row = cell.split("c")[0].split("r")[1];
                            var col = cell.split("c")[1];
                            if(col === "7"){col = 4}

                            $("#caseListCheck").find("tr").eq(row-1).children("td").eq(col-2).html(formatMoney(accumulative));
                            $("#caseListCheck").find("tr").eq(row-1).children("td").eq(col-1).html(formatMoney(amount));
                        }
                        // $(".tblContainer").eq(4).find("tbody tr").eq(33-1).children("td").eq(4-2).html(formatMoney(delNull(list[33].accumulative)));
                    }else{
                        $("#caseListCheck").find("tr").each(function () {
                            $(this).children("td").eq(2).html("");
                            $(this).children("td").eq(3).html("");
                        });
                    }
                }else{
                    $("#errorTip .tipWord").html("无效的返回值！");
                    bounce.show($("#errorTip"));
                }
            },
            error:function () {
                $("#errorTip .tipWord").html("系统错误，请重试！");
                bounce.show($("#errorTip"));
            }
        })
    }else{
        if(val == "1"){
            $("#amountCheckTab").html(assetCheck);
        }if(val == "2"){
            $("#amountCheckTab").html(debtCheck);
        }if(val == "3"){
            $("#amountCheckTab").html(ownerCheck);
        }if(val == "4"){
            $("#amountCheckTab").html(costCheck);
        }if(val == "5"){
            $("#amountCheckTab").html(profitCheck);
        }
        $("#checkTab1").show().siblings().hide();
	}
    obj.addClass('ty-active').siblings().removeClass('ty-active');
}
// creator:lyt，2018-06-21 科目管理 金额核查切换
function balancedetail(){
	if(balanceList.length>0){
		var html= '';
		var num = 0;
		for(var a=0;a<balanceList.length;a++){
			num = 1 +a;
			html +=
				'<tr>' +
                '    <td>'+ num +'</td>' +
                '    <td>'+ balanceList[a].bankName  +'</td>' +
                '    <td>'+ balanceList[a].accountNO +'</td>' +
                '    <td>'+ balanceList[a].accountantBalance +'</td>' +
                '    <td>'+ balanceList[a].financeBalance +'</td>' +
                '</tr>';
		}
        $(".balanceDetailList tbody").html(html);
        bounce_Fixed.show($("#balanceTip"));
    }else{
        $("#errorTip .tipWord").html("无效的返回值！");
        bounce.show($("#errorTip"));
	}
}
// creator:lyt，2018-07-10 科目管理 报表核查
var prevMonth = ""; //报表核查所需要的时间
function reportCheck(){
    //初始化所有表固定信息
    initAllThead();
    var caseFlowStr = initCashData();
    $(".tblContainer table>tbody").eq(4).html(caseFlowStr);
    $.ajax({
        "url": "../accountant/reportVerification.do",
        "data": '',
        success: function (data) {
        	if(data["code"]== "1"){
                prevMonth = data["data"]['period'];
                var periodStr = prevMonth.split("-")[0] + '年'+ prevMonth.split("-")[1]+'月';
                $(".dateSet").html(periodStr);
                $("#reportCheck .ty-secondTab li").eq(0).click();
                bounce.show($("#reportCheck"));
			}
        }
    });
}
/* creator：张旭博，2017-06-08 09:48:36，初始化后三张表固定信息 */
function initAllThead() {
    //----------利润表 BalanceSheet----------

    //项目
    var incomeProjectStr            = [{"item":"一、营业收入","level":"0"},{"item":"减：营业成本","level":"0"},{"item":"营业税金及附加","level":"1"},{"item":"其中：消费税","level":"2"},{"item":"营业税","level":"3"},{"item":"城市维护建设税","level":"3"},{"item":"资源税","level":"3"},{"item":"土地增值税","level":"3"},{"item":"城镇土地使用税、房产税、车船税、印花税","level":"3"},{"item":"教育费附加、矿产资源补偿费、排污费","level":"3"},{"item":"销售费用","level":"1"},{"item":"其中：商品维修费","level":"2"},{"item":"广告费和业务宣传费","level":"3"},{"item":"管理费用","level":"1"},{"item":"其中：开办费","level":"2"},{"item":"业务招待费","level":"3"},{"item":"研究费用","level":"3"},{"item":"财务费用","level":"1"},{"item":"其中：利息费用（收入以“-”号填列）","level":"2"},{"item":"加：投资收益（损失以“-”号填列）","level":"0"},{"item":"二、营业利润（亏损以“-”号填列）","level":"0"},{"item":"加：营业外收入","level":"0"},{"item":"其中：政府补助","level":"1"},{"item":"减：营业外支出","level":"0"},{"item":"其中：坏账损失","level":"1"},{"item":"无法收回的长期债券投资损失","level":"2"},{"item":"无法收回的长期股权投资损失","level":"2"},{"item":"自然灾害等不可抗力因素造成的损失","level":"2"},{"item":"税收滞纳金","level":"2"},{"item":"三、利润总额（亏损以“-”号填列）","level":"0"},{"item":"减：所得税费用","level":"0"},{"item":"四、净利润（净亏损以“-”号填列）","level":"0"}];
    //行次
    var incomeProjectNumStr         = ["1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32"];
    var incomeSheetStr = "" ;
    for(var j=0;j<incomeProjectNumStr.length;j++){
        incomeSheetStr +=       '<tr style="display:block">'+
            '<td style="display:inline-block;width: 40%"><span level="'+incomeProjectStr[j].level+'">'+incomeProjectStr[j].item+'</span></td>'+
            '<td style="display:inline-block;width: 20%">'+incomeProjectNumStr[j]+'</td>'+
            '<td style="display:inline-block;width: 20%"></td>'+
            '<td style="display:inline-block;width: 20%"></td>'+
            '</tr>';
    }
    $(".tblContainer table>tbody").eq(2).html(incomeSheetStr);

    //----------资产负债表 BalanceSheet----------

    //资产
    var assetStr            = [{"item":"流动资产：","level":"0"},{"item":"货币资金","level":"1"},{"item":"短期投资","level":"1"},{"item":"应收票据","level":"1"},{"item":"应收账款","level":"1"},{"item":"预付账款","level":"1"},{"item":"应收股利","level":"1"},{"item":"应收利息","level":"1"},{"item":"其他应收款","level":"1"},{"item":"存货","level":"1"},{"item":"其中：原材料","level":"2"},{"item":"在产品","level":"3"},{"item":"库存商品","level":"3"},{"item":"周转材料","level":"3"},{"item":"其他流动资产","level":"1"},{"item":"流动资产合计","level":"2"},{"item":"非流动资产：","level":"1"},{"item":"长期债券投资","level":"1"},{"item":"长期股权投资","level":"1"},{"item":"固定资产原价","level":"1"},{"item":"减：累计折旧","level":"1"},{"item":"固定资产账面价值","level":"1"},{"item":"在建工程","level":"1"},{"item":"工程物资","level":"1"},{"item":"固定资产清理","level":"1"},{"item":"生产性生物资产","level":"1"},{"item":"无形资产","level":"1"},{"item":"开发支出","level":"1"},{"item":"长期待摊费用","level":"1"},{"item":"其他非流动资产","level":"1"},{"item":"非流动资产合计","level":"2"},{"item":"资产总计","level":"3"}];

    //行次
    var assetNumStr         = ["","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30"];

    //负债
    var liabilitieStr       = [{"item":"流动负债：","level":"0"},{"item":"短期借款","level":"1"},{"item":"应付票据","level":"1"},{"item":"应付账款","level":"1"},{"item":"预收账款","level":"1"},{"item":"应付职工薪酬","level":"1"},{"item":"应交税费","level":"1"},{"item":"应付利息","level":"1"},{"item":"应付利润","level":"1"},{"item":"其他应付款","level":"1"},{"item":"其他流动负债","level":"1"},{"item":"流动负债合计","level":"2"},{"item":"非流动负债：","level":"0"},{"item":"长期借款","level":"1"},{"item":"长期应付款","level":"1"},{"item":"递延收益","level":"1"},{"item":"其他非流动负债","level":"1"},{"item":"非流动负债合计","level":"2"},{"item":"负债合计","level":"3"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"所有者权益（或股东权益）：","level":"0"},{"item":"实收资本（或股本）","level":"1"},{"item":"资本公积","level":"1"},{"item":"盈余公积","level":"1"},{"item":"未分配利润","level":"1"},{"item":"所有者权益（或股东权益）合计","level":"2"},{"item":"负债和所有者权益（或股东权益）总计","level":"0"}];

    //行次
    var liabilitieNumStr    = ["","31","32","33","34","35","36","37","38","39","40","41","","42","43","44","45","46","47","","","","","","","","48","49","50","51","52","53"];

    var BalanceSheetStr     = "" ;
    for(var i=0;i<assetNumStr.length;i++){
        BalanceSheetStr +=      '<tr style="display:block">'+
            '<td style="display:inline-block;width:20%"><span level="'+assetStr[i].level+'">'+assetStr[i].item+'</span></td>'+
            '<td style="display:inline-block;width:10%">'+assetNumStr[i]+'</td>'+
            '<td style="display:inline-block;width:10%"></td>'+
            '<td style="display:inline-block;width:10%"></td>'+
            '<td style="display:inline-block;width:20%"><span level="'+liabilitieStr[i].level+'">'+liabilitieStr[i].item+'</span></td>'+
            '<td style="display:inline-block;width:10%">'+liabilitieNumStr[i]+'</td>'+
            '<td style="display:inline-block;width:10%"></td>'+
            '<td style="display:inline-block;width:10%"></td>'+
            '</tr>';

    }
    $(".tblContainer table>tbody").eq(3).html(BalanceSheetStr);
}
//返回按钮
function backMain(){
	$(".main_sub").show();
	$(".main_input").hide();
}
//creator: lyt date: 2018/7/2 返回当前建账状态
function accounttingState() {
    $.ajax({
        "url": "../accountant/getBuildAccountState.do",
        "data": '',
        success: function (data) {
            var field = data["data"];
            var timeShow = field.time;
            var rebuildFlag = field.rebuildFlag;
            var tempTime = timeShow.substr(0,4) + '-'+ timeShow.substr(5,2) + '-' + timeShow.substr(8,2)+' 00:00:01';//2018年12月20日
            var today1 = new Date(tempTime);
            var yearSign = today1.getFullYear();
            var monthSign = today1.getMonth();
            var balanceYear = yearSign;
            var lastMonth = '';//上一月
            var nextMonthDate = '';
            var sign = 0;
            var nextNum = 0;
            if(monthSign == '11'){
                sign = yearSign +1;
                nextMonthDate = sign + '年1月';
            }else{
                nextNum = monthSign +2;
                nextMonthDate = yearSign + '年' + nextNum + '月';
            }
            if(monthSign == '0'){//上一年
                sign = yearSign - 1;
                lastMonth = sign + '年12月';
                balanceYear = sign;
            }else{
                lastMonth = yearSign + '年' + monthSign + '月';
            }
            if (field.res == "0") {
                bounce.show($("#buildBill"));
            } else if (field.res == "1") { //1  -  建账提示中选择了是,进入的科目设置
                $(".today").html(timeShow);
                setUpBill(1);
                // setUpBill(1,"");
            } else if (field.res == "2") {//2  -  建账提示中选择了否,进入的科目设置
                $(".today").html(timeShow);
                $(".generateBeginTime").html(nextMonthDate);
                setUpBill(2);
            } else if (field.res == "3") {
                bounce.cancel();
                $(".timeLine").html(timeShow);
                $(".lastMonth").html(lastMonth);
                $(".balanceYear").html(balanceYear);
                $(".balanceMonth").html(lastMonth);
                $(".generateBeginTime").html(nextMonthDate);
                setUpBill(3);
            } else if (field.res == "4") {
                setUpBill(4);
            }
        }
    });
}
//creator: lyt date: 2018/7/4 设置当前建账状态
function setBuildAccountState(num){
    $.ajax({
        "url": "../accountant/setBuildAccountState.do",
        "data": {"state":num},
        success: function (json) {
            var status = json.code;
            var setTime = json["data"].time;
            var standardTime = setTime.substr(0,4) + '-' + setTime.substr(5,2) + '-' + setTime.substr(8,2);
            var today1 = new Date(standardTime);
            var yearSign = today1.getFullYear();
            var monthSign = today1.getMonth();
            var balanceYear = yearSign;
            var lastMonth = '';//上一月
            var nextMonth = '';
            var sign = 0;
            var nextNum = 0;
            if(monthSign == '11'){ //下一年
                sign = yearSign +1;
                nextMonth = sign + '年1月';
			}else{
                nextNum = monthSign +2;
                nextMonth = yearSign + '年' + nextNum + '月';
            }
            if(monthSign == '0'){//上一年
                sign = yearSign - 1;
                lastMonth = sign + '年12月';
                balanceYear = sign;
			}else{
                lastMonth = yearSign + '年' + monthSign + '月';
            }
            if(status == "1"){
                if(num == "1"){
                    $(".today").html(setTime);
                    $(".generateBeginTime").html(nextMonth);
                    setUpBill(1);
                }else if(num =="2"){
                    setUpBill(2);
				}else if(num == "3"){
                    $(".timeLine").html(setTime);
                    $(".lastMonth").html(lastMonth);
                    $(".balanceYear").html(balanceYear);
                    $(".balanceMonth").html(lastMonth);
                    setUpBill(3);
                }
            }
        }
    });
}
//设置建账完成
function buildFinish(){
	$.ajax({
		"url": "../accountant/buildFinish.do",
		"data": {},
		success: function (json) {
			var code = json["code"];
			if(code == "1"){
                location.href = '/sys/logout.do';
			}else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
			}
		}
	})
}
//creator: lyt date: 2018/7/2 获取该机构所有的最后一级的科目，用于录入建账数据
function getFinalLevelSubject(){
    assetArr =[],debtArr =[],ownerArr=[],costArr=[],profitArr =[];
    $.ajax({
        "url": "../accountant/getBaseSubjectsByOrg.do",
        "data": "",
        success: function (json) {
        	var data = json["data"]["subjectList"];
        	var cashList = json["data"]["cashList"];

            // var arrTemp = {};
            if(data && data.length>0){
                for(var a=0 ;a<data.length;a++){
                    var baseData = {};
                    baseData.id =data[a].id;
                    baseData.subject =data[a].subject;
                    baseData.name =data[a].name;
                    baseData.category =data[a].category;
                    baseData.categoryname =data[a].categoryname;
                    baseData.beginningBalance =data[a].beginningBalance;
                    baseData.beginningQuantity =data[a].beginningQuantity;
                    baseData.balance =data[a].balance;
                    baseData.quantity =data[a].quantity;
                    baseData.creditAccumulative =data[a].creditAccumulative;
                    baseData.creditAccumulativeQuantity =data[a].creditAccumulativeQuantity;
                    baseData.debitAccumulative =data[a].debitAccumulative;
                    baseData.debitAccumulativeQuantity =data[a].debitAccumulativeQuantity;
                    baseData.balanceDirection =data[a].balanceDirection;
                    baseData.quantityAssistingAccounting =data[a].quantityAssistingAccounting;
                    // baseData.initialAmount =data[a].initialAmount;
                    // baseData.initialQuantity =data[a].initialQuantity;
                	if(data[a].category == "6"||data[a].category == "7"){
                        assetArr.push(baseData);
					}else if(data[a].category == "8" || data[a].category == "9"){
                        debtArr.push(baseData);
					}else if(data[a].category == "10"){
                        ownerArr.push(baseData);
					}else if(data[a].category == "11"){
                        costArr.push(baseData);
					}else if(data[a].category == "12"||data[a].category == "13"||data[a].category == "14"||data[a].category == "15"||data[a].category == "16"||data[a].category == "17"){
                        profitArr.push(baseData);
					}
                }
                var jump = $("#enterInitFinance").attr("catoType")-1;
                amountTab($("#amountTab li").eq(jump));
			}
			if(cashList && cashList.length>0){
                //----------现金流量表 cashFlowSheet----------

                var cashFlowStr      = [{"item":"一、经营活动产生的现金流量：","level":"0"},{"item":"销售商品、提供劳务收到的现金","level":"1"},{"item":"收到的税费返还","level":"1"},{"item":"收到其他与经营活动有关的现金","level":"1"},{"item":"经营活动现金流入小计","level":"1"},{"item":"购买商品、接受劳务支付的现金","level":"1"},{"item":"支付给职工以及为职工支付的现金","level":"1"},{"item":"支付的各项税费","level":"1"},{"item":"支付其他与经营活动有关的现金","level":"1"},{"item":"经营活动现金流出小计","level":"2"},{"item":"经营活动产生的现金流量净额","level":"2"},{"item":"二、投资活动产生的现金流量：","level":"0"},{"item":"收回投资收到的现金","level":"1"},{"item":"取得投资收益收到的现金","level":"1"},{"item":"处置固定资产、无形资产和其他长期资产收回的现金净额","level":"1"},{"item":"处置子公司及其他营业单位收到的现金净额","level":"1"},{"item":"收到其他与投资活动有关的现金","level":"1"},{"item":"投资活动现金流入小计","level":"2"},{"item":"购建固定资产、无形资产和其他长期资产支付的现金","level":"1"},{"item":"投资支付的现金","level":"1"},{"item":"取得子公司及其他营业单位支付的现金净额","level":"1"},{"item":"支付其他与投资活动有关的现金","level":"1"},{"item":"投资活动现金流出小计","level":"2"},{"item":"投资活动产生的现金流量净额","level":"2"},{"item":"三、筹资活动产生的现金流量：","level":"0"},{"item":"吸收投资收到的现金","level":"1"},{"item":"其中：子公司吸收少数股东投资收到的现金","level":"1"},{"item":"取得借款收到的现金","level":"1"},{"item":"收到其他与筹资活动有关的现金","level":"1"},{"item":"筹资活动现金流入小计","level":"2"},{"item":"偿还债务支付的现金","level":"1"},{"item":"分配股利、利润或偿付利息支付的现金","level":"1"},{"item":"其中：子公司支付给少数股东的股利、利润","level":"1"},{"item":"支付其他与筹资活动有关的现金","level":"1"},{"item":"筹资活动现金流出小计","level":"2"},{"item":"筹资活动产生的现金流量净额","level":"2"},{"item":"四、汇率变动对现金及现金等价物的影响","level":"0"},{"item":"五、现金及现金等价物净增加额","level":"0"},{"item":"加：期初现金及现金等价物的余额","level":"1"},{"item":"六、期末现金及现金等价物余额","level":"0"}];
                //项目
                var cashFlowNumStr   = ["","1","2","3","4","5","6","7","8","9","10","","11","12","13","14","15","16","17","18","19","20","21","22","","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37"];
                //行次
                var cashFlowSheetStr = "";
                for(var c=0;c<cashFlowNumStr.length;c++){
                    cashFlowSheetStr +='<tr style="display:block">'+
                        '<td style="display:inline-block;width:40%"><span level="'+ cashFlowStr[c].level+'">'+cashFlowStr[c].item+'</span></td>'+
                        '<td style="display:inline-block;width:20%">'+cashFlowNumStr[c]+'</td>';
						if(cashFlowNumStr[c] == ""){
                            cashFlowSheetStr +='<td style="display:inline-block;width:20%"></td>';
						}else{
                            cashFlowSheetStr +='<td style="display:inline-block;width:20%"><input value="0.00" val-init="0" data-init="0.00" onblur="newCompareOld($(this))" type="text" onkeyup="sub_clearNoNum(this)"/></td>';
						}
                        cashFlowSheetStr +='<td style="display:inline-block;width:20%">--</td>'+
                        '</tr>';
                }
                $("#caseStatetable tbody").html(cashFlowSheetStr);
            	for(var p=0;p<cashList.length;p++){
            		var cashFlowCode = cashList[p].cashFlowCode;
            		var accumulative = cashList[p].accumulative;
                    var row = cashFlowCode.split("c")[0].split("r")[1]-1;	//第几行
					var rowNum = cashFlowCode.split("c")[0].split("r")[1];
                    var col = cashFlowCode.split("c")[1]-2;	//第几列
					$("#caseStatetable tbody").find("tr").eq(row).children().eq(col).children().attr("row",rowNum).val(formatMoney(accumulative));
				}
			}
        }
    });
}

//现金流量表初始化 cashFlowSheet
function initCashData(){
	//----------现金流量表 cashFlowSheet----------

    var cashFlowStr      = [{"item":"一、经营活动产生的现金流量：","level":"0"},{"item":"销售商品、提供劳务收到的现金","level":"1"},{"item":"收到的税费返还","level":"1"},{"item":"收到其他与经营活动有关的现金","level":"1"},{"item":"经营活动现金流入小计","level":"1"},{"item":"购买商品、接受劳务支付的现金","level":"1"},{"item":"支付给职工以及为职工支付的现金","level":"1"},{"item":"支付的各项税费","level":"1"},{"item":"支付其他与经营活动有关的现金","level":"1"},{"item":"经营活动现金流出小计","level":"2"},{"item":"经营活动产生的现金流量净额","level":"2"},{"item":"二、投资活动产生的现金流量：","level":"0"},{"item":"收回投资收到的现金","level":"1"},{"item":"取得投资收益收到的现金","level":"1"},{"item":"处置固定资产、无形资产和其他长期资产收回的现金净额","level":"1"},{"item":"处置子公司及其他营业单位收到的现金净额","level":"1"},{"item":"收到其他与投资活动有关的现金","level":"1"},{"item":"投资活动现金流入小计","level":"2"},{"item":"购建固定资产、无形资产和其他长期资产支付的现金","level":"1"},{"item":"投资支付的现金","level":"1"},{"item":"取得子公司及其他营业单位支付的现金净额","level":"1"},{"item":"支付其他与投资活动有关的现金","level":"1"},{"item":"投资活动现金流出小计","level":"2"},{"item":"投资活动产生的现金流量净额","level":"2"},{"item":"三、筹资活动产生的现金流量：","level":"0"},{"item":"吸收投资收到的现金","level":"1"},{"item":"其中：子公司吸收少数股东投资收到的现金","level":"1"},{"item":"取得借款收到的现金","level":"1"},{"item":"收到其他与筹资活动有关的现金","level":"1"},{"item":"筹资活动现金流入小计","level":"2"},{"item":"偿还债务支付的现金","level":"1"},{"item":"分配股利、利润或偿付利息支付的现金","level":"1"},{"item":"其中：子公司支付给少数股东的股利、利润","level":"1"},{"item":"支付其他与筹资活动有关的现金","level":"1"},{"item":"筹资活动现金流出小计","level":"2"},{"item":"筹资活动产生的现金流量净额","level":"2"},{"item":"四、汇率变动对现金及现金等价物的影响","level":"0"},{"item":"五、现金及现金等价物净增加额","level":"0"},{"item":"加：期初现金及现金等价物的余额","level":"1"},{"item":"六、期末现金及现金等价物余额","level":"0"}];
    //项目
    var cashFlowNumStr   = ["","1","2","3","4","5","6","7","8","9","10","","11","12","13","14","15","16","17","18","19","20","21","22","","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37"];
    //行次
    var cashFlowSheetStr = "";
    for(var c=0;c<cashFlowNumStr.length;c++){
        cashFlowSheetStr +=       '<tr style="display:block;">'+
            '<td style="display:inline-block;width: 40%"><span level="'+cashFlowStr[c].level+'">'+cashFlowStr[c].item+'</span></td>'+
			'<td style="display:inline-block;width: 20%">'+cashFlowNumStr[c]+'</td>'+
			'<td style="display:inline-block;width: 20%"></td>'+
			'<td style="display:inline-block;width: 20%"></td>'+
			'</tr>';
    }
    return cashFlowSheetStr;
}
//现金流量表初始化 cashFlowSheet
function getCashFlowSheet(obj){
    obj.addClass('ty-active').siblings().removeClass('ty-active');
    $("#reportCheck .tblContainer").eq(4).find("thead").width($("#reportCheck .amount-body").eq(0)[0].clientWidth);
    $(".tblContainer").addClass("hd");
    $(".tblContainer").eq(obj.index()).removeClass("hd");
    //获取日期
    if(prevMonth == ""){
        prevMonth = null;
    }
    $.ajax({
        url:"getCashFlowData.do" ,
        data:{ "period": prevMonth } ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["code"] == 1){
                var list = data["data"];
                if(list.length>0){
                    for(var i = 0; i<list.length ;i++){
                        var cell = list[i].cellCode;
                        var accumulative = delNull(list[i].accumulative);
                        var amount = delNull(list[i].amount);
                        var row = cell.split("c")[0].split("r")[1];
                        var col = cell.split("c")[1];
                        if(col === "7"){col = 4}

                        $("#reportListCheck").find("tr").eq(row-1).children("td").eq(col-2).html(formatMoney(accumulative));
                        $("#reportListCheck").find("tr").eq(row-1).children("td").eq(col-1).html(formatMoney(amount));
                    }
                    // $(".tblContainer").eq(4).find("tbody tr").eq(33-1).children("td").eq(4-2).html(formatMoney(delNull(list[33].accumulative)));
                }else{
                    $("#reportListCheck").find("tr").each(function () {
                        $(this).children("td").eq(2).html("");
                        $(this).children("td").eq(3).html("");
                    });
                }
            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        }
    })
}
//获取试算平衡表信息 TrialBalance
function getTrialBalanceSheet(obj) {
	obj.addClass("ty-active").siblings().removeClass("ty-active");
    $(".tblContainer").addClass("hd");
    $(".tblContainer").eq(obj.index()).removeClass("hd");
    //获取日期
    if(prevMonth == null && prevMonth == ""){
        prevMonth = null;
    }
    $.ajax({
        url:"getTrialBalanceData.do" ,
        data:{ "period": prevMonth } ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["code"] == 0){
                $("#tip #tipMs").html(data["msg"]);
                bounce.show($("#tip"));
            }else if(data["code"] == 1){
                var list = data["data"];
                var trialBalanceStr =  "";
                if(list.length>0){
                    for(var i =0 ;i<list.length;i++){
                        trialBalanceStr += '<tr>'+
                            '<td>'+list[i].subject+'</td>'+
                            '<td>'+list[i].subName+'</td>'+
                            '<td>'+changeRules(list[i].subDirection)+'</td>'+//期初余额方向
                            '<td>'+formatMoney(delNull(list[i].previousBalance))+'</td>'+//期初余额
                            '<td>'+formatMoney(delNull(list[i].credit))+'</td>'+//本期发生借
                            '<td>'+formatMoney(delNull(list[i].debit))+'</td>'+//本期发生贷
                            '<td>'+changeRules(list[i].balanceDirection)+'</td>'+//期末余额方向
                            '<td>'+formatMoney(delNull(list[i].balance))+'</td>'+//期末余额
                            '</tr>';
                    }
                    //加入合计字符串
                    trialBalanceStr +=  '<tr>' +
                        '<td colspan="2" rowspan="2">合计</td>'+
                        '<td>借</td>'+
                        '<td>'+formatMoney(data["sumPreviousBalanceCredit"])+'</td>'+
                        '<td rowspan="2">'+formatMoney(data["sumCredit"])+'</td>'+
                        '<td rowspan="2">'+formatMoney(data["sumDebit"])+'</td>'+
                        '<td>借</td>'+
                        '<td>'+formatMoney(data["sumBalanceCredit"])+'</td>'+
                        '</tr>'+
                        '<tr>' +
                        '<td>贷</td>'+
                        '<td>'+formatMoney(data["sumPreviousBalanceDebit"])+'</td>'+
                        '<td>贷</td>'+
                        '<td>'+formatMoney(data["sumBalanceDebit"])+'</td>'+
                        '</tr>';
                    $(".tblContainer").eq(0).find("tbody").html(trialBalanceStr);
                }else{
                    $(".tblContainer").eq(0).find("tbody").html("");
                }

            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        }
    })
}

//获取科目余额表信息 AccountBalance
function getAccountBalanceSheet(obj) {
	obj.addClass("ty-active").siblings().removeClass("ty-active");
    // $("#reportCheck .tblContainer").eq(1).find("thead").width($("#reportCheck .amount-body").eq(0)[0].clientWidth);
    $(".tblContainer").addClass("hd");
    $(".tblContainer").eq(obj.index()).removeClass("hd");
    if(prevMonth == null && prevMonth == ""){
        prevMonth = null;
    }
    $.ajax({
        url:"getSubjectBalanceSheet.do" ,
        data:{ "period": prevMonth } ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["code"] == 0){
                $("#tip #tipMs").html(data["msg"]);
                bounce.show($("#tip"));
            }else if(data["code"] == 1){
                var list = data["data"];
                var accountBalanceStr =  "";
                if(list.length>0){
                    for(var i =0 ;i<list.length;i++){
                        accountBalanceStr += '<tr>'+
                            '<td>'+list[i].subject+'</td>'+
                            '<td>'+list[i].subName+'</td>'+
                            '<td>'+changeRules(list[i].beginningDirection)+'</td>'+//年初余额方向
                            '<td>'+formatMoney(delNull(list[i].beginningBalance))+'</td>'+//年初余额
                            '<td>'+changeRules(list[i].subDirection)+'</td>'+//期初余额方向
                            '<td>'+formatMoney(delNull(list[i].previousBalance))+'</td>'+//期初余额
                            '<td>'+formatMoney(delNull(list[i].credit))+'</td>'+//本期发生借
                            '<td>'+formatMoney(delNull(list[i].debit))+'</td>'+//本期发生贷
                            '<td>'+formatMoney(delNull(list[i].creditAccumulative))+'</td>'+//本年累计借
                            '<td>'+formatMoney(delNull(list[i].debitAccumulative))+'</td>'+//本年累计贷
                            '<td>'+changeRules(list[i].balanceDirection)+'</td>'+//期末余额方向
                            '<td>'+formatMoney(delNull(list[i].balance))+'</td>'+//期末余额
                            '</tr>';
                    }
                    //加入合计字符串
                    accountBalanceStr +=    '<tr>' +
                        '<td colspan="2" rowspan="2">合计</td>'+
                        '<td>借</td>'+
                        '<td>'+formatMoney(data["sumBeginningBalanceCredit"])+'</td>'+
                        '<td>借</td>'+
                        '<td>'+formatMoney(data["sumPreviousBalanceCredit"])+'</td>'+
                        '<td rowspan="2">'+formatMoney(data["sumCredit"])+'</td>'+
                        '<td rowspan="2">'+formatMoney(data["sumDebit"])+'</td>'+
                        '<td rowspan="2">'+formatMoney(data["sumAccumulativeCredit"])+'</td>'+
                        '<td rowspan="2">'+formatMoney(data["sumAccumulativeDebit"])+'</td>'+
                        '<td>借</td>'+
                        '<td>'+formatMoney(data["sumBalanceCredit"])+'</td>'+
                        '</tr>'+
                        '<tr>' +
                        '<td>贷</td>'+
                        '<td>'+formatMoney(data["sumBeginningBalanceDebit"])+'</td>'+
                        '<td>贷</td>'+
                        '<td>'+formatMoney(data["sumPreviousBalanceDebit"])+'</td>'+
                        '<td>贷</td>'+
                        '<td>'+formatMoney(data["sumBalanceDebit"])+'</td>'+
                        '</tr>';
                    $(".tblContainer table>tbody").eq(1).html(accountBalanceStr);
                }else{
                    $(".tblContainer").eq(1).find("tbody").html("");
                }

            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误请重试！");
            bounce.show($("#errorTip"));
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })
}

//获取利润表信息
function getIncomeSheet(obj) {
    //获取日期
    //资产负债和利润表共用一个借口，使用code区分
    obj.addClass("ty-active").siblings().removeClass("ty-active");
    $("#reportCheck .tblContainer").eq(2).find("thead").width($("#reportCheck .amount-body").eq(0)[0].clientWidth);
    $(".tblContainer").addClass("hd");
    $(".tblContainer").eq(obj.index()).removeClass("hd");
    var code = 2;
    if(prevMonth == ""){
        prevMonth = null;
    }
    $.ajax({
        url:"getSublectBalance.do" ,
        data:{ "nowDate": prevMonth , "code":code,"state":"4"} ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["status"] === 0){
                $("#errorTip .tipWord").html("本月未结账！");
                bounce.show($("#errorTip"));
            }else if(data["status"] === 1){
                var list = data["list"];
                if(list.length>0){
                    for(var i = 0; i<list.length ;i++){
                        var cell = list[i].cellCode;
                        var amount = list[i].amount;    //本月金额
                        var accumulative = list[i].accumulative;    //本年累计金额
                        var row = cell.split("c")[0].split("r")[1];

                        $(".tblContainer").eq(2).find("tbody tr").eq(row-1).children("td").eq(2).html(formatMoney(accumulative));
                        $(".tblContainer").eq(2).find("tbody tr").eq(row-1).children("td").eq(3).html(delNull(amount));
                    }
                }else{
                    $(".tblContainer").eq(2).find("tbody tr").each(function () {
                        $(this).children("td").eq(3).html("");
                    });
                }

            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })

}

//获取资产负债表信息
function getBalanceSheet(obj) {
    obj.addClass("ty-active").siblings().removeClass("ty-active");
    $(".tblContainer").addClass("hd");
    $(".tblContainer").eq(obj.index()).removeClass("hd");
    $("#reportCheck .tblContainer").eq(3).find("thead").width($("#reportCheck .amount-body").eq(0)[0].clientWidth);
    //获取日期
    //资产负债和利润表共用一个借口，使用code区分
    var code = 1;
    if(prevMonth == null && prevMonth == ""){
        prevMonth = null;
    }
    $.ajax({
        url:"getSublectBalance.do" ,
        data:{ "nowDate": prevMonth , "code":code,"state":"4"} ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["status"] == 0){
                $("#errorTip .tipWord").html("本月未结账！");
                bounce.show($("#errorTip"));
            }else if(data["status"] == 1){
                var list = data["list"];
                if(list.length>0){
                    for(var i = 0; i<list.length ;i++){
                        var cell = list[i].cellCode;
                        var balance = list[i].balance;
                        var beginningBalance = list[i].beginningBalance;
                        var row = cell.split("c")[0].split("r")[1];
                        var col = cell.split("c")[1];
                        $(".tblContainer").eq(3).find("tbody tr").eq(row-1).children("td").eq(col-1).html(formatMoney(balance));
                        $(".tblContainer").eq(3).find("tbody tr").eq(row-1).children("td").eq(col).html(formatMoney(beginningBalance));
                    }
                    //以下四个为固定值
                    $(".tblContainer").eq(3).find("tbody tr").eq(10).children("td").eq(6).html(formatMoney(0.00));
                    $(".tblContainer").eq(3).find("tbody tr").eq(16).children("td").eq(6).html(formatMoney(0.00));
                    $(".tblContainer").eq(3).find("tbody tr").eq(10).children("td").eq(7).html(formatMoney(0.00));
                    $(".tblContainer").eq(3).find("tbody tr").eq(16).children("td").eq(7).html(formatMoney(0.00));
                }else{
                    $(".tblContainer").eq(3).find("tbody tr").each(function () {
                        $(this).children("td").eq(2).html("");
                        $(this).children("td").eq(6).html("");
                    });
                }
            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })

}

// creator: 张旭博，2019-05-28 13:24:07，个人报销与科目关联管理
function associateBtn() {
	$(".mainCon2").show()
	$(".mainCon1").hide()
    getRelevances()
}

// creator: 张旭博，2019-05-28 13:37:45，返回报销关联科目数据
function getRelevances() {

    $.ajax({
        url:'../accountant/getRelevances.do',
        data:{},
        success:function(data){
            var data = data.data
			var categories = data.categories
			var selectedSubjects = data.selectedSubjects
			var subject5601 = data['5601']
			var subject5602 = data['5602']

			var categoryStr = ''
            var str5601 = '<option value="">--请选择科目--</option>'
            var str5602 = '<option value="">--请选择科目--</option>'
            for(var k in subject5601){
                str5601 += '<option value="'+subject5601[k].id+'">' + subject5601[k].subject + ' ' + subject5601[k].name + '</option>'
            }
            for(var l in subject5602){
                str5602 += '<option value="'+subject5602[l].id+'">' + subject5602[l].subject + ' ' + subject5602[l].name + '</option>'
            }
			for(var i in categories) {
                categoryStr += 	'<tr data-id="' + categories[i].id + '">' +
								'    <td>'+  (-(-i - 1)) +'</td>' +
								'    <td data-id = '+categories[i].id+' class="category">'+ categories[i].name + '</td>' +
								'    <td>不关联 <input type="radio" name="enabled'+i+'" value="0" checked> 关联 <input type="radio" name="enabled'+i+'" value="1"></td>' +
								'    <td><select name="" subject="5601" disabled>'+str5601+'</select></td>' +
								'    <td><select name="" subject="5602" disabled>'+str5602+'</select></td>' +
								'    <td>' +
								'        <span class="ty-color-blue" onclick="getCategoryRelevanceHistory(5601, $(this))">销售费用记录</span>' +
								'        <span class="ty-color-blue" onclick="getCategoryRelevanceHistory(5602, $(this))">管理费用记录</span>' +
								'    </td>' +
								'</tr>'
			}
            $(".associate tbody").html(categoryStr);
            for(var j in selectedSubjects) {
            	var categoryId = selectedSubjects[j].feeCat
                var subject = selectedSubjects[j].subject
            	var enabled = selectedSubjects[j].enabled
            	var name = selectedSubjects[j].name
            	var code = selectedSubjects[j].code
            	var subjectId = selectedSubjects[j].subjectId
				var $obj = $("[data-id='"+categoryId+"']")
                var oldData = {
                    feeCat: categoryId , //(类别id)
                    subject: subject ,//(关联的科目编号)
                    name:name , //(科目名称)
                    enabled:enabled , //(关联状态，有关联科目时为1，否则为0)
                    code:code,//(父科目编号，5601或5602)
                    subjectId:subjectId //(科目id，关联科目的id)
                }

                $obj.find('input:radio[value="'+enabled+'"]').prop("checked",true)
                $obj.find('select').prop("disabled",false)
				console.log(subject)
                $obj.find('[subject="'+code+'"]').val(subjectId)

                if (code === '5601') {
                    $obj.data("oldData5601",oldData)
                }else {
                    $obj.data("oldData5602",oldData)
                }

            }
        }
    })
}

// creator: 张旭博，2019-05-28 13:37:45，保存关联设置
function setReimburseRelevance() {
	var newData = []
    var oldData = $(".associate").data('oldData')
	var n = 0
    $(".associate tbody tr").each(function () {
        var enabled = Number($(this).find('input:radio:checked').val())
        var newSubjectId1 = Number($(this).find('[subject="5601"]').val())
        var newSubjectId2 = Number($(this).find('[subject="5602"]').val())
        var categoryName = $(this).find('.category').html()

        if(!newSubjectId1 && !newSubjectId2 && enabled === 1) {
            $("#errorTip .tipWord").html('<span class="ty-color-blue">' + categoryName+'</span> 未选择任何科目但选择了关联，请检查！');
            bounce.show($("#errorTip"));
            n ++
            return false
        }

        var oldData5601 = $(this).data('oldData5601')
        var oldData5602 = $(this).data('oldData5602')
        var oldEnabled, newEnabled
        if (!oldData5601 && !oldData5602) {
            oldEnabled = 0
        } else {
            oldEnabled = 1
        }
        newEnabled = parseInt($(this).find('input:radio:checked').val())

		if(oldData5601) {
            var oldSubjectId1 = oldData5601.subjectId
		} else {
            var oldSubjectId1 = 0
		}
        if(oldData5602) {
            var oldSubjectId2 = oldData5602.subjectId
        } else {
            var oldSubjectId2 = 0
		}

        var subject5601 = $(this).find('[subject="5601"] option:selected').html()
        var subject5602 = $(this).find('[subject="5602"] option:selected').html()
        var feeCat = $(this).attr('data-id')
        if (newSubjectId1) {
            var newData5601 = {
                feeCat: feeCat, //(类别id)
                subject: subject5601.split(' ')[0],//(关联的科目编号)
                name: subject5601.split(' ')[1], //(科目名称)
                enabled: 1, //(关联状态，有关联科目时为1，否则为0)
                code: 5601,//(父科目编号，5601或5602)
                subjectId: $(this).find('[subject="5601"]').val() //(科目id，关联科目的id)
            }
        } else {
            var newData5601 = {
                feeCat: feeCat, //(类别id)
                subject: '',//(关联的科目编号)
                name: '', //(科目名称)
                enabled: 0, //(关联状态，有关联科目时为1，否则为0)
                code: 5601,//(父科目编号，5601或5602)
                subjectId: '' //(科目id，关联科目的id)
            }
        }
        if (newSubjectId2) {
            var newData5602 = {
                feeCat: feeCat, //(类别id)
                subject: subject5602.split(' ')[0],//(关联的科目编号)
                name: subject5602.split(' ')[1], //(科目名称)
                enabled: 1, //(关联状态，有关联科目时为1，否则为0)
                code: 5602,//(父科目编号，5601或5602)
                subjectId: $(this).find('[subject="5602"]').val() //(科目id，关联科目的id)
            }
        } else {
            var newData5602 = {
                feeCat: feeCat, //(类别id)
                subject: '',//(关联的科目编号)
                name: '', //(科目名称)
                enabled: 0, //(关联状态，有关联科目时为1，否则为0)
                code: 5602,//(父科目编号，5601或5602)
                subjectId: '' //(科目id，关联科目的id)
            }
        }
        if(oldEnabled === 0 && newEnabled === 1){
            if (newSubjectId1) {
                newData.push(newData5601)
            }
            if (newSubjectId2) {
                newData.push(newData5602)
            }
        } else if (oldEnabled === 1 && newEnabled === 0) {
            if (oldSubjectId1) {
                newData.push(newData5601)
            }
            if (oldSubjectId2) {
                newData.push(newData5602)
            }

        } else if (oldEnabled === 1 && newEnabled === 1) {
            if (newSubjectId1 !== oldSubjectId1) {
                newData.push(newData5601)
            }
            if (newSubjectId2 !== oldSubjectId2) {
                newData.push(newData5602)
            }
        }
    })
	if(n > 0) return false
    console.log(JSON.stringify(newData))
    $.ajax({
        url:'../accountant/setReimburseRelevance.do',
        data:{list:JSON.stringify(newData)},
        success:function(data){
			var state = data.data
			if(state === 1){
				layer.msg("设置成功")
                getRelevances()
			}
        }
    })
}

// creator: 张旭博，2019-05-28 13:37:45，管理费用记录
function getCategoryRelevanceHistory(code, obj) {
    var subject = code === 5601?'销售费用':'管理费用'
    var categoryName = obj.parents('tr').find('.category').html()
    var categoryId = obj.parents('tr').find('.category').data('id')
    bounce.show($("#manageBillRecord"))
    $("#manageBillRecord .bonceHead span").html(subject + '操作记录')
    $("#manageBillRecord .feeCatName").html(categoryName)
    $.ajax({
        url: '../accountant/getCategoryRelevanceHistory.do',
        data: { code: code, feeCat: categoryId },
        success: function (data) {
            var history = data.data
            var str = '<tr><td>原始状态</td><td>与  <span class="ty-color-green">'+code + subject+'</span> 科目不关联</td><td>--</td></tr>'
            for(var i in history) {
                str += '<tr>' +
                        '    <td>第'+(-(-i-1))+'次修改后</td>' +
							(history[i].enabled === 0 ?
								'<td>修改为与  <span class="ty-color-green">'+ code + subject  +'</span> 科目不关联</td>':
								'<td>修改为与  <span class="ty-color-green">'+history[i].subject + ' ' + history[i].name +'</span> 科目相关联</td>'
							)+
                        '    <td>修改者 '+ history[i].updateName + ' ' + formatTime(history[i].updateDate, true)  +'</td>' +
                        '</tr>'
            }
            $("#manageBillRecord tbody").html(str)

        }
    })
}


// creator: 李玉婷，2022-04-27 08:48:13，采购关联设置确定
function purchaseAssocialSure() {
	if ($("#purchaseAssociationSet .fa-dot-circle-o").is(":visible")){
        let state = $("#purchaseAssociationSet .fa-dot-circle-o").data("type");
        $.ajax({
            url:"../accountant/setRelation.do" ,
			data: {"state": state},
            success:function ( data ) {
                let success = data.res;
                if (success === 1) {
                    bounce.cancel();
                    layer.msg("设置成功！")
                } else {
                    layer.msg("操作失败，请刷新重试！")
				}
            }
        })
    }
}
// creator: 李玉婷，2022-04-28 15:29:05，采购关联开关记录
function purchaseRecordList() {
    $("#purchaseRecord table tbody tr:gt(0)").remove();
    $.ajax({
        url:"../accountant/getRelationRecords.do" ,
        data: "",
        success:function ( res ) {
            let html = ``;
            let list = res.data;
            if (list && list.length > 0) {
                for(var i=0;i<list.length;i++){
                    html +=
                        `<tr>
                            <td>${list[i].createName} ${ new Date(list[i].createDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            <td>${transformStr(list[i].value_, 'associate')}</td>
                        </tr>`;
                }
                $("#purchaseRecord table tbody").append(html);
            }
            bounce_Fixed.show($("#purchaseRecord"));
        }
    })
}
// creator: 李玉婷，2022-05-05 10:04:30，取消
function purchaseCancel() {
    if ($("#purchaseAssociationSet .set-step1").is(":visible")){
        bounce.cancel();
    } else {
        $("#purchaseAssociationSet .set-step2").hide().siblings().show();
    }
}




//========辅助方法

//去null操作
function delNull(val){
    return null == val ? 0: val;
}
// 设置金额为默认格式（比如199,231,000.00)
function formatMoney(number, places, symbol, thousand, decimal) {
    number = number || 0;
    places = !isNaN(places = Math.abs(places)) ? places : 2;
    symbol = symbol !== undefined ? symbol : "";
    thousand = thousand || ",";
    decimal = decimal || ".";
    var negative = number < 0 ? "-" : "",
        i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
        j = (j = i.length) > 3 ? j % 3 : 0;
    return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
}
// creator: 李玉婷，2022-04-28 16:13:05，输出 
function transformStr(val, type) {
	let str = ``;
    switch(type){
        case 'associate'://Y-开启关联，N-关闭关联
        	if (handleNull(val) === 'Y') {
                str = `开启`;
			} else if (handleNull(val) === 'N') {
                str = `关闭`;
            }
            break;
    }
    return str;
}


//获得方向 1：借、2：贷、3：平
function changeRules(val) {
    if(val == null){
        val = 3
    }
    val = parseInt(val);
    switch(val){
        case 1:
            return "借";
            break;
        case 2:
            return "贷";
            break;
        case 3:
            return "平";
            break;
        default:
            return " ";
    }
}
//creator: lyt date: 2018/7/9 标记被修改的input表单
function newCompareOld(obj){
    let val = obj.val();
    if (val === ""){
        obj.val("0.00");
        val = 0.00;
    }
    let old = obj.data("init");
    if(Number(val) !== Number(old)){
        obj.attr("val-init","1");
        if($("#buildOverBtn").attr("can") == "ok"){
            $("#buildOverBtn").attr("can","off");
            $("#buildOverBtn").attr({"class":"ty-btn ty-btn-gray ty-btn-big ty-circle-5"}).removeAttr("onclick");
        }
    }
}
//creator: lyt date: 2018/7/2 默认--
function converFormate(str,init){
    if(str == "1"){
		var html = '<input val-init="0" data-init="'+ delNull(init)+'" type="text" value="'+ delNull(init)+'" onblur="newCompareOld($(this))" onkeyup="sub_clearNoNum(this)"/>';
        return html;
    }else{
        return "- -";
    }
}
//  输入小数或整数 或负数
function sub_clearNoNum(obj){
    obj.value = obj.value.replace(/[^(\d| \-).]/g,"");  //清除“数字”和“.”以外的字符

    obj.value = obj.value.replace(/^\./g,"");  //验证第一个字符是数字而不是.
    obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的.
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
}



