/* creator ： 侯杏哲 ， 2017-1-12
* 用于完成会计模块-权限设置 的各功能
* */
$(function(){
	getAccountList();
	$(".ty-form-checkbox").on("click",function () {
		var IDval = $(this).attr("id") ;
		if(IDval){
            var isAll = IDval.split("_").length === 3;  //ture -总模块  flase - 子模块
            var allObj = $(this).parents("tr");
            if($(this).parents("tr").hasClass("subjectChoose")){
                if(isAll){
                    if($(this).hasClass("ty-form-checked")){
                        $(".subjectChoose").find(".ty-form-checkbox").not("#auth_sc_all").removeClass("ty-form-checked");
                    }
                }else{
                    if(!$(".subjectChoose").find(".ty-form-checkbox").hasClass("ty-form-checked")){
                        $("#auth_sc_all").addClass("ty-form-checked");
                    }
                }
                $(this).toggleClass("ty-form-checked");
            }else if($(this).parents("tr").hasClass("subjectSet")){
                if($(this).hasClass("ty-form-checked")){
                    allObj.children("td").find(".ty-form-checkbox").removeClass("ty-form-checked");
                }else{
                    allObj.children("td").find(".ty-form-checkbox").addClass("ty-form-checked");
                }
            }else{
                if(isAll){
                    if($(this).hasClass("ty-form-checked")){
                        allObj.children("td:gt(0)").find(".ty-form-checkbox").removeClass("ty-form-checked");
                    }else{
                        allObj.children("td").eq(2).find(".ty-form-checkbox").addClass("ty-form-checked");
                    }
                }else{
                    if(!allObj.find(".ty-form-checkbox").hasClass("ty-form-checked")){
                        if(!$(this).hasClass("ty-form-select")){
                            allObj.children("td").eq(0).find(".ty-form-checkbox").addClass("ty-form-checked");
                        }
                    }
                    if($(this).hasClass("ty-form-checked") && allObj.find(".ty-form-checked").length === 2){
                        allObj.children("td").eq(0).find(".ty-form-checkbox").removeClass("ty-form-checked");
                    }
                }
                $(this).toggleClass("ty-form-checked");
            }
		}

    })
});
var editItem = {};
 /* creator: 侯杏哲，2017-1-12  调整权限  */
function set_authority( obj ){
    var userID =  obj.siblings(".hd").html() ;
    var loginUser = JSON.parse( $("#loginUser").html() ) ;
    var manageId = loginUser["userID"] ;
    if( userID == ""){
        $("#mt_tip_ms").html("获取员工信息失败！"); bounce.show( $("#mtTip") ) ; return false ;
    }
    var phone = obj.parent().prev().html();
    var ttlnav = "<span class='nav_'> / </span> <span class='navTxt'>"+ phone+" - 权限设置</span>" ;
    $("#authNav").html( ttlnav );
    editItem = { "manageId":manageId  , "userId" : userID  } ;
    $("#adjustAuthority .ty-form-checkbox").removeClass("ty-form-checked");
	$.ajax({
        url: "../sys/getAccountingPopedomList.do" ,
        data: editItem ,
		type:"post" ,
		dataType:"json" ,
		beforeSend:function(){ loading.open();    } , 
		success:function( data ){
            bounce.show($("#adjustAuthority"));
            var rolePopedom = data["popedomList"] ;
			if(rolePopedom && rolePopedom.length > 0 ){
				for( var i=0 ; i < rolePopedom.length ; i++ ){
                    var mid = rolePopedom[i]["mid"] ;
                    $("#auth_" + mid ).attr("class" , "ty-form-checkbox ty-form-checked") ;
				}
			}
			$("#adjustAuthority tbody tr").not(".subjectChoose").each(function () {
				var len = $(this).find(".ty-form-checked").length;
				if(len > 0){
					$(this).find(".ty-form-checkbox").eq(0).addClass("ty-form-checked");
				}else{
                    $(this).find(".ty-form-checkbox").eq(0).removeClass("ty-form-checked");
				}
            }) ;

		} , 
		error:function () {
			$("#mt_tip_ms").html("链接错误，请稍后重试！");
			bounce.show( $("#mtTip") ) ;
		}
	}).always(function(){  loading.close();   });
}
/* updator: 侯杏哲，2018-01-31 调整权限 - 确定按钮  */
function authority_surebtn(){
	$("#authNav span:gt(2)").remove();
	$(".authority_hd").hide().siblings().show().parent().show();
	var popdomStr = "sa" ;
	$(".ty-form-checked").each(function(){
        var isAll = $(this).attr("id").split("_").length === 3;
        if(!isAll){
            var mid = $(this).attr("id").split("_")["1"] ;
            popdomStr += "," +  mid ;
		}
	});
	 $(".subjectChoose").each(function(){
	     if($(this).find(".ty-form-checked").length >0   ){
             popdomStr += ",sc" ; return false ;
         }
     })  ;
	$.ajax({
		// url:"../popedom/saveUserPopedom.do" ,
		url:"../sys/saveAccountingPopedomList.do" ,
		data:{ "mid": popdomStr , "userId": editItem["userId"]  } ,
		type:"post" , 
		dataType:"json" , 
		beforeSend:function(){ loading.open();   } , 
		success:function(data){
			var status = data["status"] ;
			if( Number(status) == 1){
				$("#mt_tip_ms").html("修改权限成功！");
				bounce.show( $("#mtTip") ) ;
			}else{
				$("#mt_tip_ms").html("修改权限失败！");
				bounce.show( $("#mtTip") ) ;
			}
		},
		error:function () {
			$("#mt_tip_ms").html("修改权限失败！");
			bounce.show( $("#mtTip") ) ;    
		}
	}).always(function(){ loading.close();  });
}
/* creator: 侯杏哲，2017-1-12  新增会计  */ 
function addStaff(){
	bounce.show( $("#addStaff") );
	$("#Phone").val("");
}
/* creator: 侯杏哲，2017-1-12  新增会计 - 确定  */
function addStaffOk(){
    var phone = $("#Phone").val();
    var userName  = $("#Phone").val();
    if( !testMobile(phone) ){
        $(".tip").html("您输入的手机号不合法");
        return false;
    }
    $.ajax({
        url:"../sys/addSmallAccounting.do",
        data:{ "phone" :phone , "userName" : userName   } ,
        type : "post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open();   } ,
        success:function ( data ) {
            var status = data["status"]; // 2 手机号已存在   1 成功
            if( Number(status) == 1 ){
                var userNew = data["user"];
                var str = "<tr>" +
                    "<td>"+ userNew["mobile"] +"</td>" +
                    "<td class='setAddUp'>" +
                    "<a class='ty-color-blue' onclick='set_authority($(this))'>调整权限</a>" +
                    "<span class='hd userID'>"+ userNew["userID"]  +"</span>" +
                    "</td>" +
                    "</tr>";
                $("#staffCon").append( str ) ;
                bounce.cancel();
            }else{
                $("#mt_tip_ms").html("该手机号已存在， 无需再次新增");
                bounce.show( $("#mtTip") ) ;
            }
        } ,
        error:function(){
            $("#mt_tip_ms").html("连接错误，请稍后重试！");
            bounce.show( $("#mtTip") ) ;
        }
    }).always(function(){ loading.close();    }) ;
}
/* updator: 侯杏哲，2018-1-29   获得会计人员列表  */
function getAccountList(){
    $.ajax({
        url:"../sys/smallAccountingList.do" ,
        data:{    } ,
        type : "post" ,
        dataType:"json" ,
        beforeSend : function(){ loading.open() ;  } ,
        success:function ( data ) {
            var status  = data["status"] ;
            var list = data["users"] ;
            var str = " " ;
            if( list && list.length > 0 ){
                for( var i=0 ; i < list.length ; i++ ){
                    str += "<tr>" +
                        "<td>"+ list[i]["mobile"] +"</td>" +
                        "<td class='setAddUp'>" +
                        "<a class='ty-color-blue' onclick='set_authority($(this))'>调整权限</a>" +
                        "<span class='hd userID'>"+ list[i]["userID"]  +"</span>" +
                        "</td>" +
                        "</tr>";
                }
            }
            $("#staffCon").html( str ) ;
        } ,
        error:function(){
            $("#mt_tip_ms").html("连接错误，请稍后重试！");
            bounce.show( $("#mtTip") ) ;
        }
    }).always(function(){   loading.close();   })
}













