/**
 * Created by Administrator on 2017/2/8.
 */
// //creator:孟闯闯，2017-2-17  12:00:00，结账
// function settleAccounts() {
//     $.ajax({
//         url:"settleAccount.do" , 
//         type:"post" , 
//         dataType:"json"
//     }).success(function (data) {
//         console.log(data); 
//         var status = data["res"] ;
//         if( status == 0 ){
//             $("#tp").html("结账失败，请重试！"); 
//         }else{
//             $("#tp").html("结账成功！");
//             $("#settle").html("");
//         }
//     }).fail(function () {
//         $("#tp").html("链接错误，请重试！");
//     })
// }
// //creator:孟闯闯，2017-2-17  12:00:00，结账撤销
// function settleAccounts2(){
//     $.ajax({
//         url: "cancelSettleAccount.do",
//         type: "post",
//         data: {},
//         dataType:"json",
//         success:function(data){
//             console.log(data)
//             if(data.res==1){
//                 $("#tp").html("撤销成功！");
//                 $("#settle").html("");
//             }else{
//                 $("#tp").html("撤销失败，请重试！");
//             }
//         },
//         error:function(){
//             $("#tp").html("链接错误，请重试！");
//         }
//     });
// }
$(function () {
    showMainCon(1);
    getMainData();
    judgementState();
    $("#previewing").val(0); //mainCon6结账预览页面是否显示过
    $("body").on("click",".fun-btn",function(){
        var type = $(this).data("type");
        switch (type) {
            case 'subjectOperate': // 查看/选择科目
                showMainCon(6);
                $("#secondNav li:eq(0)").click();
                break;
            case 'noAccounting': // 尚未入会计帐的票据
                showMainCon(2);
                break;
            case 'markNoAccounting': // 已制作过凭证但选为“不予做账”的
                showMainCon(3);
                break;
            case 'noVoucherCount':
                bounce.show($("#noaccount"));
                break;
            case 'manualsScan': //手动选择的凭证
                let month1 = $("#settleMonth").data("month");
                getVoucherByMonth(0, month1);
                break;
            case 'osScan': //系统选择的凭证
                let month2 = $("#settleMonth").data("month");
                getVoucherByMonth(2, month2);
                break;
            case 'accountantlsScan': //会计录入的凭证
                let month3 = $("#settleMonth").data("month");
                getVoucherByMonth(1, month3);
                break;
            case 'settleRecord': //结账记录
                $("#previewing").val(2);
                $("#otherYear").val("");
                settleRecordList();
                break;
            case 'fcReportPreview': //财务报表
                fcReportPreview();
                break;
            case 'reback5':
                let state = $("#previewing").val();
                if (state === '1'){
                    showMainCon(6);
                } else if (state === "2"){
                    showMainCon(2);
                } else {
                    showMainCon(1);
                }
                break;
            case 'reback6':
                $("#previewing").val(0);
                reback();
                break;
            case 'bookPreview':
                let month = $("#settleMonth").data("month");
                $(".mainCon7 .bookDate").html($("#settleMonth").html()).data("month", month);
                showMainCon(7);
                setAllSubject($(".ty-colFileTree .treeBox"));
                break;
        }
    });
    $("table").on("click", "[type='btn']", function () {
        var name = $(this).data('name');
        let info = JSON.parse($(this).siblings(".hd").html());
        switch(name) {
            case 'fcReportByMonth':
                showMainCon(4);
                let str = info.period.replace('-', '年');
                $(".mainCon4 .reportDetail").show().html(str + "月的财务报表");
                $("#reportDateSelect").val(info.period);//来自凭证管理页面
                $(".ty-secondTab .ty-active").click();
                break;
            case 'accountantBook':
                showMainCon(7);
                setAllSubject($(".ty-colFileTree .treeBox"));
                break;
            case 'record_book':
                let month = info.period.replace('-', '年');
                $(".mainCon7 .bookDate").html(month+ '月').data("month", info.period);
                showMainCon(7);
                setAllSubject($(".ty-colFileTree .treeBox"));
                break;
            case 'monthlyVoucher':
                getVoucherByMonth("",info.period);
                break;
        }
    });
    $("#reimburse").on('click', '[type="scan_btn"]', function () {
        var name = $(this).data('name')
        switch (name) {
            case 'billInfo':
                bounce_Fixed2.show($("#billInfo"))
                var reimburseBillId = $(this).parents("tr").attr("id")
                $.ajax({
                    url: "../reimburseWindow/getReimburseBill.do",
                    data: {reimburseBillId: reimburseBillId},
                    success: function (data) {
                        if (data) {
                            var quotaBill = data.financeReimburseBillList[0]
                            var goods= quotaBill.financeReimburseBillItemList
                            var billCatName = quotaBill.billCatName
                            $("#billInfo .billQuantity").hide()
                            $("#billInfo .singleAmount").show()
                            switch (billCatName) {
                                case '增值税专用发票':
                                case '增值税普通发票':
                                    $("#billInfo .VATGood").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice+'</td>' +
                                            '   <td>'+goods[i].price+'</td>' +
                                            '   <td>'+(goods[i].taxRate * 100).toFixed(2) +' %</td>' +
                                            '   <td>'+goods[i].taxAmount+'</td>' +
                                            '   <td>'+goods[i].amount+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .VATGood tbody").html(goodsStr)
                                    var issueDate = quotaBill.issueDate
                                    var quantity = quotaBill.relativeBillQuantity
                                    issueDate = issueDate.substring(0,4) +'年' + issueDate.substring(5,7) + '月'
                                    if (Number(quantity) > 1) {
                                        $("#billInfo .billQuantity").show().html('内容与本票据完全相同，且开票月份也为' + issueDate + '的票据总数量为' + quantity + '张')
                                    }
                                    break;
                                case '定额普通发票':
                                    var goodsStr = ''
                                    $("#billInfo .quotaGood").show().siblings().hide()
                                    $("#billInfo .quotaInvoice .billCatName").html('定额普通发票')
                                    $("#billInfo .quotaInvoice .feeCatName").html(quotaBill.feeCatName)
                                    var amount = 0
                                    var number = 0
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '    <td>'+goods[i].uniPrice+'</td>' +
                                            '    <td>'+goods[i].itemQuantity+'张</td>' +
                                            '    <td>'+goods[i].price+'</td>' +
                                            '</tr>'
                                        amount = parseFloat(amount) + parseFloat(goods[i].price)
                                        number = parseInt(number) + parseInt(goods[i].itemQuantity)
                                    }
                                    goodsStr += '<tr>' +
                                        '    <td>总计</td>' +
                                        '    <td>'+number+'张</td>' +
                                        '    <td>'+amount+'</td>' +
                                        '</tr>'
                                    $("#billInfo .quotaGood tbody").html(goodsStr)
                                    $("#billInfo .singleAmount").hide()
                                    break;
                                case '其他普通发票':
                                    $("#billInfo .otherGood").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice+'</td>' +
                                            '   <td>'+goods[i].price+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .otherGood tbody").html(goodsStr)
                                    var issueDate = quotaBill.issueDate
                                    var quantity = quotaBill.relativeBillQuantity
                                    issueDate = issueDate.substring(0,4) +'年' + issueDate.substring(5,7) + '月'
                                    if (Number(quantity) > 1) {
                                        $("#billInfo .billQuantity").show().html('内容与本票据完全相同，且开票月份也为' + issueDate + '的票据总数量为' + quantity + '张')
                                    }
                                    break;
                                case '收据':
                                    $("#billInfo .receipt").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice+'</td>' +
                                            '   <td>'+goods[i].price+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .receipt tbody").html(goodsStr)
                                    break;
                                default:
                                    console.log('不存在的值')
                            }
                            $("#billInfo .memo").html(quotaBill.memo)
                            $("#billInfo .amount").html(quotaBill.itemAmount)
                        }
                    }
                })
                break;
            case 'billPic':
                if ($(this).data("path") !== ""){
                    seePicture($(this).data("path"));
                } else {
                    layer.msg("无图片");
                }
                break;
        }
    })
});
// creator: 李玉婷，2022-05-13 08:00:17，结账记录
function settleRecordList(val) {
    let param = {};
    if (val && val !== "") {param.year = val;}
    showMainCon(2);
    let year = $("#settleMonth").data("month").substring(0,4);
    $(".mainCon2 .annual").html(year);
    $.ajax({
        url:"../accountant/getSettleListByYear.do" ,
        data:param ,
        success:function(data){
            //"status": 此时状态，“2”代表小结账成功，“3”代表总结账成功。
            let html = ``;
            let list = data.data || [];
            for(let i=0;i<list.length;i++){
                html +=
                    `<tr><td>${list[i].period}</td>
                    <td>${list[i].createName} ${new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss')}</td>
                    <td>
                        <span class="ty-color-blue" type="btn" data-name="fcReportByMonth">结账后该月的财务报表</span>
                        <span class="ty-color-blue" type="btn" data-name="monthlyVoucher">该月凭证</span>
                        <span class="ty-color-blue" type="btn" data-name="record_book">该月结账后的账簿</span>
                        <span class="ty-color-blue">该月重要的会计操作</span>
                        <span class="hd">${JSON.stringify(list[i])}</span>
                    </td></tr>`;
            }
            $("#settleRecordList tbody").html(html);
        },
        error:function () {
            $("#tip #tipMs").html("系统错误，请重试！");
            bounce.show($("#tip"));
        }
    });
}
// creator: 李玉婷，2022-05-10 11:31:32，结账预览
function checkOutPreview() {
    $("#previewing").val(1);
    showMainCon(6);
}
// creator: 李玉婷，2022-05-11 14:20:54，财务报表
function fcReportPreview() {
    showMainCon(4);
    $("#reportDateSelect").val($("#settleMonth").data("month"));
    $(".mainCon4 .reportDetail").show().html($("#settleMonth").html() + "的财务报表");
    $(".ty-secondTab .ty-active").click();
}
/* creator：张旭博，2017-06-26 16:00:20，判断转状态 */
function judgementState(){
    var data = {};
    $.ajax({
        url:"../accountant/clickReportForm.do" ,
        data:data ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            var period = data["period"];
            var status = data["status"]; // status=2，也就是结账按钮是可点击状态下，需要把新加的prompt返回值显示在页面上
            var isDisplayTaxBtn = data["isDisplayTaxBtn"]; // 标识是否显示报税按钮 ：1-可以显示报税按钮         0-不显示
            if(isDisplayTaxBtn == 1){
                $("#tallageBtn").prop("disabled" , false) ;
            } else {
                $("#tallageBtn").prop("disabled" , true) ;
            }
            if(period == ""||period == undefined){
                $("#tip #tipMs").html("未返回正确时间！");
                bounce.show($("#tip"));
            }else{
                period = period.split("-")[0]+"年"+period.split("-")[1]+"月";
                $("#searchDate").val(period);
                $(".settleDate").html(period);
            }
            switch(Number(status)){
                case 0:
                    $("#checkOutBtn").prop("disabled",true);
                    $("#trialBtn").prop("disabled",false);
                    $("#checkOutPreview").prop("disabled",false);
                    $("#lossAndGainBroughtForwardBtn").prop("disabled",true);
                    $("#partCheckOutBtn").hide();
                    $("#counterCheckBtn").prop("disabled",true);
                    $("#counterCheckBtn").show();
                    break;
                case 1://1-代表已经进行过试算，此时试算，记账，和总结账按钮,都亮起。
                    $("#checkOutBtn").prop("disabled",false);
                    $("#trialBtn").prop("disabled",false);
                    $("#checkOutPreview").prop("disabled",false);
                    $("#lossAndGainBroughtForwardBtn").prop("disabled",false);
                    $("#partCheckOutBtn").prop("disabled",false);
                    $("#partCheckOutBtn").show();
                    $("#counterCheckBtn").hide();
                    break;
                case 3://3-代表已经结账，所有按钮都不亮
                    $("#checkOutBtn").prop("disabled",true);
                    $("#trialBtn").prop("disabled",true);
                    $("#checkOutPreview").prop("disabled",true);
                    $("#lossAndGainBroughtForwardBtn").prop("disabled",true);
                    $("#partCheckOutBtn").prop("disabled",true);
                    $("#partCheckOutBtn").show();
                    $("#counterCheckBtn").hide();
                    break;
                case 2://2-代表此时已经记账，此时以前显示记账的按钮亮起并显示成反记账，结账按钮亮起。
                    $("#trialBtn").prop("disabled",true);
                    $("#lossAndGainBroughtForwardBtn").prop("disabled",true);
                    $("#partCheckOutBtn").hide();
                    $("#checkOutPreview").prop("disabled",false);
                    $("#counterCheckBtn").prop("disabled",false);
                    $("#counterCheckBtn").show();
                    $("#checkOutBtn").prop("disabled",false);
                    break;
                default:
                    $("#tip #tipMs").html("错误的状态！");
                    bounce.show($("#tip"));

            }
        },
        error:function () {
            $("#tip #tipMs").html("系统错误，请重试！");
            bounce.show($("#tip"));
        },
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })
}
/* creator：张旭博，2017-06-26 17:48:34，点击结账按钮 */
function checkoutBtn() {
    $("#checkout").data("state", 3)
    bounce.show($("#checkout"));
}

/* creator：张旭博，2017-06-26 17:48:53，确定结账 */
function sureCheckout() {
    //state 状态2是小结账 3是总结账
    var data = {
        "state":$("#checkout").data("state")
    };
    bounce.cancel() ;
    $.ajax({
        url:"../accountant/closeAccounts.do" ,
        data:data ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function () {
          loading.open() ;
        },
        success:function(data){
            //"status": 此时状态，“2”代表小结账成功，“3”代表总结账成功。
            var status = data["status"];
            var flag = data["flag"];
            if(flag === 0){
                $("#tip #tipMs").html("请先试算再结账！");
                bounce.show($("#tip"));
            }else if(flag === -1){
                $("#tip #tipMs").html("操作失败！因为还有尚未处理的凭证！");
                bounce.show($("#tip"));
            } else{
                if(status === "" || status === undefined){
                    $("#tip #tipMs").html("未返回正确的试算状态");
                    bounce.show($("#tip"));
                }else if(status === "3"){
                    $("#tip #tipMs").html("结账成功！");
                    bounce.show($("#tip"));
                    judgementState();
                }if(status === "2"){
                    $("#tip #tipMs").html("结账成功！<p>记账后请勿忘记在本系统中“结账”！！</p>");
                    bounce.show($("#tip"));
                    judgementState();
                }
            }
        },
        error:function () {
            $("#tip #tipMs").html("系统错误，请重试！");
            bounce.show($("#tip"));
        },
        complete:function () {
            loading.close();
        }
    });
}


// creator: 李玉婷，2022-04-28 12:13:38，结账管理首页数据
function getMainData() {
    $.ajax({
        url: '../accountant/getSettleHomeData.do',
        data: {},
        success: function (res) {
            let countType = ``;
            let data = res.data;
            let settleMonth = data.settleMonth.replace('年', '-');
            settleMonth = settleMonth.replace('月', '');
            $("#settleMonth").data("month",settleMonth).html(data.settleMonth);
            $(".mainCon6 .settleDate").html(data.settleMonth);
            $(".mainCon1 .countNum").each(function () {
                countType = $(this).data("type");
                $(this).html(data[countType] || 0);
            })
            $(".mainCon6 .countNum").each(function () {
                countType = $(this).data("type");
                $(this).html(data[countType] || 0);
            })
        }
    });
}
//绑定时间插件
laydate.render({
    elem: '#otherYear',
    type: 'year',
    format: 'yyyy',
    done: function(value){
        if(value!== ''){
            settleRecordList(value)
        }
    }
}) ;