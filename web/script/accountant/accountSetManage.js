$(function () {
    showMainCon(1);
    getAccountSetRecord();
    judgeRebuildAccount();
    $("table").on('click', '.funbtn', function () {
        let fun = $(this).data("fun");
        let info = ``;
        switch (fun) {
            case "changeRecord": // 科目变动记录
                showMainCon(2);
                $("#changeContent_ye").data("obj", $(this));
                getSubChangeRecord(1, 2);
                break;
            case "changeContent": // 变动内容
                let type = $(this).data("type");
                info = JSON.parse($(this).parents("tr").find(".hd").html());
                if (type === 1) {
                    let json = {
                        "establish": info.id,
                        "changeDate": new Date(info.changeDate).format('yyyy-MM-dd')
                    }
                    $("#re-changeDate").html(new Date(info.changeDate).format('yyyy年MM月dd日'));
                    $("#accountChangeRecord").data("establish", info.establish);
                    $("#accountChangeRecord tbody tr:gt(0)").remove();
                    changeContentScan(json);
                    bounce.show($("#accountChangeRecord"));
                } else if (type === 2) {
                    afterSubjectChange(1, $(this));
                    showMainCon(3);
                }
                break;
            case "beforeUpdate": //修改前
                //1. id            int    查看修改后时传,否则不传
                //2. previousId    int    修改前时数据ID，否则不传
                subjectUpdateScan(1, $(this));
                break;
            case "afterUpdate": //修改后
                subjectUpdateScan(2, $(this));
                break;
            case "afterAccount": // 建账后的科目
                showMainCon(3);
                afterSubjectChange(0, $(this));
                break;
        }
    });
    $("#subjectScan").on('click', 'li', function () {
        $(this).addClass('ty-active').siblings().removeClass('ty-active');
        let data = JSON.parse($("#subChangeParam").html());
        data.category = Number($(this).index())+1;
        getChangeSubjects(data);
    });
});


//creator:lyt date:2018/7/13 重新建账判断
function judgeRebuildAccount(){
    $.ajax({
        url: "../accountant/checkRebuildFlag.do",
        data: "",
        success: function (data) {
            if(data["data"]=="1"){
                $("#rebuildAccount").addClass("ty-btn-green").removeClass("ty-btn-gray").attr("onclick","bounce.show($('#rebuild_tip'))");
            }else{
                $("#rebuildAccount").addClass("ty-btn-gray").removeClass("ty-btn-green").removeAttr("onclick");
            }
        }
    });
}
//creator:lyt date:2018/7/13 重新建账操作
function rebuildAccount(){
    $.ajax({
        url: "../accountant/rebuild.do",
        data: "",
        success: function (data) {
            if(data["code"] == "1"){
                location.href = '/sys/logout.do';
            }else if(data["code"] == "0"){
                layer.msg("重新建账失败！")
            }
        }
    });
}
// creator: 李玉婷，2022-04-28 15:29:05，建账记录
function getAccountSetRecord() {
    $.ajax({
        url:"../accountant/getEstablishRecords.do" ,
        data: "",
        success:function ( res ) {
            let html = ``;
            let list = res.data;
            if (list && list.length > 0) {
                for(var i=0;i<list.length;i++){
                    html +=
                        `<tr>
                            <td>${list[i].type === '1'? '初始化':'重新建账'} </td>
                            <td>${list[i].createName} </td>
                            <td>${ new Date(list[i].createDate).format("yyyy-MM-dd hh:mm:ss")} </td>
                            <td>
                            <span class="funbtn ty-color-blue" data-fun="changeRecord">查看</span>
                            <span class="hd">${JSON.stringify(list[i])}</span>
                            </td>
                        </tr>`;
                }
                $(".mainCon1 table tbody").html(html);
            }
        }
    })
}

// creator: 李玉婷，2022-04-26 10:44:05，返回
function reback(num) {
    if (!num) {
        $(".mainCon").hide()
        $(".mainCon1").show()
    } else {
        $(".mainCon").hide()
        $(".mainCon" + num).show()
    }

}
// creator: 李玉婷，2022-04-12 08:57:55，显示页面
function showMainCon(num){
    $("#showMainConNum").val(num)
    $(".mainCon" + num).show().siblings().hide();
}

