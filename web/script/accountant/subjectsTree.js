/**
 * Created by rose on 2017/1/13.
 */
// 单位科目表假数据
var subjects = [
    { "id":1 , "subject":1001 , "parent":0 , "name":"银行存款" , "category":1 , "level":1 ,"maxChildSubjects":4 },
    { "id":2 , "subject":100101 , "parent":1001 , "name":"银行存款1" , "category":6 , "level":2 ,"maxChildSubjects":2 },
    { "id":2 , "subject":100101001 , "parent":100101 , "name":"银行存款11" , "category":6 , "level":3 ,"maxChildSubjects":0 },
    { "id":2 , "subject":100101002 , "parent":100101 , "name":"银行存款12" , "category":6 , "level":3 ,"maxChildSubjects":0 },
    { "id":3 , "subject":100102 , "parent":1001 , "name":"银行存款2" , "category":7 , "level":2 ,"maxChildSubjects":0 },
    { "id":4 , "subject":100103 , "parent":1001 , "name":"银行存款3" , "category":7 , "level":2 ,"maxChildSubjects":2 },
    { "id":2 , "subject":100103001 , "parent":100103 , "name":"银行存款31" , "category":8 , "level":3 ,"maxChildSubjects":0 },
    { "id":2 , "subject":100103001 , "parent":100103 , "name":"银行存款32" , "category":8 , "level":3 ,"maxChildSubjects":0 },
    { "id":5 , "subject":100104 , "parent":1001 , "name":"银行存款4" , "category":6 , "level":2 ,"maxChildSubjects":0 },
    { "id":6 , "subject":1002 , "parent":0 , "name":"银行存款" , "category":1 , "level":1 ,"maxChildSubjects":0 },
    { "id":7 , "subject":1003 , "parent":0 , "name":"银行存款" , "category":1 , "level":1 ,"maxChildSubjects":0 },
    { "id":8 , "subject":1004 , "parent":0 , "name":"银行存款" , "category":1 , "level":1 ,"maxChildSubjects":0 },
    { "id":1 , "subject":1005 , "parent":0 , "name":"银行存款" , "category":1 , "level":1 ,"maxChildSubjects":4 },
    { "id":2 , "subject":100501 , "parent":1005 , "name":"银行存款5" , "category":6 , "level":2 ,"maxChildSubjects":2 },
    { "id":2 , "subject":100501001 , "parent":100501 , "name":"银行存款51" , "category":6 , "level":3 ,"maxChildSubjects":0 },
    { "id":2 , "subject":100501001 , "parent":100501 , "name":"银行存款52" , "category":6 , "level":3 ,"maxChildSubjects":0 },
    { "id":2 , "subject":100502 , "parent":1005 , "name":"银行存款5" , "category":6 , "level":2 ,"maxChildSubjects":2 },
    { "id":2 , "subject":100503 , "parent":1005 , "name":"银行存款5" , "category":6 , "level":2 ,"maxChildSubjects":2 },
    { "id":2 , "subject":100504 , "parent":1005 , "name":"银行存款5" , "category":6 , "level":2 ,"maxChildSubjects":2 },
    { "id":9 , "subject":1006 , "parent":0 , "name":"银行存款" , "category":1 , "level":1 ,"maxChildSubjects":0 },
    { "id":10 , "subject":1007 , "parent":0 , "name":"银行存款" , "category":1 , "level":1 ,"maxChildSubjects":0 },
    { "id":11 , "subject":1008 , "parent":0 , "name":"银行存款" , "category":1 , "level":1 ,"maxChildSubjects":0 }
] ;
/* creator：侯杏哲 ，2017/1/20
*  目前支持到三级会计科目
*  创建各种科目树的类
* */

function subjectTree(objName){

    this.objName = objName ; // 实例化的对象名  
    this.accountInfo = null ; 
    this.getAccountInfo = function () {
        return this.accountInfo ; 
    } ; 
    // 建树用到的字符串 无子级  
    this.noKidsStr = function(objData , fn ){  // objData 单条的数据  
        var kls = "" ;
        if(objData["state"] == 0){ kls = "hd" ; }
        var click = this.objName + '.selectSubject( $(this) , event , '+fn+' )';
        var str = "<li class='"+ kls +"' onclick='"+click+"'>" +
            "<span class='acInfo'>" +
            "<span class='ac-ye-Icon'></span>"+
            "<span class='acNo'>"+ objData["subject"] +"</span>"+
            "<span class='acName'>"+ objData["name"] +"</span>" +
            "<div class='hd'>"+ JSON.stringify(objData) +"</div> " +
            "</span>" +
            "</li>" ;
        return str ;
    };
    // 建树用到的字符串 有子级 
    this.hasKidsStr = function(objData  ){  // objData 单条的数据 ， objName 被实例化的对象名
        var kls = "" ; 
        if(objData["state"] == 0){ kls = "hd" ; }
        var str = "<li class='dropDown "+ kls +" ' onclick='"+ this.objName +".toggleSonActs( $(this) , event )'>" +
            "<span class='acInfo  '>" +
            "<span class='drop-acAdd'></span>" +
            "<span class='ac-folder-Icon'></span>" +
            "<span class='acNo'>"+ objData["subject"] +"</span>" +
            "<span class='acName'>"+ objData["name"] +"</span>" +
            "<div class='hd'>"+ JSON.stringify(objData) +"</div> " +
            "</span>" +
            "<ul class='ulObj_hide'>" ;
        return str ;
    };
    
    // 建立会计科目树
    this.setActsTree = function( subjects , fn ){
        var str = "" ;
        if (subjects && subjects.length > 0) {
            for (var i = 0; i < subjects.length; i++) {
                var level = subjects[i]["level"] ;
                var maxKid = subjects[i]["maxChildSubjects"] ;
                if(Number(level) == 1){ // 只看一级会计科目  
                    if (Number(maxKid) > 0) { // 一级会计科目有子级 
                        str += this.hasKidsStr(subjects[i]) ;
                        for (var j = 1; j <= Number(maxKid); j++) { // 二级会计科目
                            var _index = i+j ; // 当前遍历到的二级节点数
                            // console.log(subjects[_index]);
                            if( subjects[_index] ){
                                var level_2 = subjects[_index]["level"] ;
                                var maxKid_2 = subjects[_index]["maxChildSubjects"] ;
                                if ( Number(level_2) == 2 ) {
                                    if ( Number(maxKid_2) > 0 ) { // 二级会计科目有子级
                                        str += this.hasKidsStr(subjects[_index] ) ;
                                        for (var k = 1; k <= Number(maxKid_2); k++) { // 三级会计科目
                                            var _index3 = i+j+k ; // 当前遍历到的三级节点数
                                            if(subjects[_index3]){
                                                str += this.noKidsStr(subjects[_index3] ,fn) ;
                                                
                                            }
                                        }
                                        str += "</ul></li>" ; // 这个二级科目的时代过去了
                                    } else{ // 二级会计科目无子级
                                        str += this.noKidsStr(subjects[_index] ,fn) ;
                                    }
                                }
                            }
                        }
                        str += "</ul></li>" ; // 这个一级科目的时代过去了
                    } else{ // 一级会计科目无子级 
                        str += this.noKidsStr(subjects[i] , fn) ;
                    }
                }
            }
        }
        return str ;
    };

    // 点击召唤下级菜单
    this.toggleSonActs = function( obj ,e ){
        e.stopPropagation();
        this.accountInfo = null ; 
        var ulObj = obj.children("ul");
        var isBlock = ulObj.attr("class");
        $(".acInfo").removeClass("acActive");
        obj.children(".acInfo").addClass("acActive");
        if (isBlock == "ulObj_hide") {
            ulObj.attr("class" , "ulObj_show");
            obj.children(".acInfo").children("span:eq(0)").attr("class" , "drop-acMinus");
        } else{
            ulObj.attr("class" , "ulObj_hide"); 
            obj.children(".acInfo").children("span:eq(0)").attr("class" , "drop-acAdd");
        }
    };
    // 选中自己科目
    this.selectSubject = function( obj , e ,fn){
        var subjectInfo = obj.children(".acInfo").children(".hd").html();
        e.stopPropagation();
        $(".acInfo").removeClass("acActive");
        obj.children(".acInfo").addClass("acActive") ; 
        this.accountInfo = JSON.parse(subjectInfo) ;
        if(fn){fn()};
}

}
   



// getActs( 1 );
// 切换科目树大类 
function getActs( num  , obj ){
    obj && obj.addClass("acActive").siblings().removeClass("acActive");
    $.ajax({
        url:"getChoseSubject.do" ,
        data: { "category" : num  } ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            console.log(data)
            $("#actsTree").html( getSubjects.setActsTree(data.list) );
        } ,
        error:function () {

        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    });
}

// getActs2()
// 切换科目树大类
function getActs2( num  , obj ){
    obj && obj.addClass("acActive").siblings().removeClass("acActive");
    $.ajax({
        url:"getChoseSubject.do" ,
        data: { "category" : num  } ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            $("#actsTree2").html( accountBook.setActsTree(data.list) );
            $("#actsTree2").children("li").eq(0).children("span").eq(0).addClass("acActive");
            $("#tit_msg").html($(".acActive .acNo").html()+"&nbsp;&nbsp;"+$(".acActive .acName").html());
        } ,
        error:function () {

        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    });
}

// 账簿管理页面
// creator:孟闯闯，2017-3-27  09:40:00，选中科目的时候右侧显示的信息
// function getRight() {
//     console.log(1)
//     // var actInfo = accountBook.accountInfo;
//     // console.log(actInfo)
//     // $("#tit_msg").html(actInfo["subject"] + actInfo["name"])
//     $("#tit_msg").html($(".acActive .acNo").html()+"&nbsp;&nbsp;"+$(".acActive .acName").html());
// }
