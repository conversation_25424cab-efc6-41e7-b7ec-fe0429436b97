// creator: 张旭博，2019-06-28 10:03:51，正常待选择 - 选择科目
function choose_subject() {
    var $selectObj = $("#tblCon").data('selectObj')
    //var billPeriod = $selectObj.find('.billPeriod').data('time')
    var billDetail = $selectObj.find('.billDetail').html()
    $("#tblCon").data('billDetail', billDetail)

    // 获取借方贷方内容
    var borrowMoreLoanStr   =   showBorLoan(0,0);

    // if(billPeriod === 1){
    //     $("#choose_addAccount .voucher").val('1').prop('disabled',true)
    // }else{
    //     $("#choose_addAccount .voucher").val('2').prop('disabled',false)
    // }
    var chooseBtn = '';
    let json = JSON.parse(billDetail);
    if(!json.noAccount){
        chooseBtn = '<button class="ty-btn ty-btn-red ty-btn-big ty-circle-3" type="btn" data-name="noAccounting">不予下账</button> ';
    }
    chooseBtn    += '<button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" type="btn" data-name="subjectSubmit" id="subjectChooseBtn"  disabled="disabled">提交</button>'; //onclick="bounce_Fixed5(1,1,' + num + ',' + pricetype + ',' + bill_period + ',' + bill_quantity + ')"
    $("#choose_addAccount .borrowMoreLoans").html(borrowMoreLoanStr);
    $("#choose_addAccount .special").html("");
    $("#choose_addAccount .bonceFoot").html(chooseBtn);
    bounce.show($("#choose_addAccount"));

    $('body').everyTime('0.5s','choose_addAccount',function(){
        var allBorrowPrice = 0;
        var allLoanPrice = 0;
        var i = 0;
        $("#choose_addAccount .borrowItem").each(function () {
            var price = $(this).find(".price").val(); // 金额：
            var creditQuantity = $(this).find(".creditQuantity").val();  // 借数量
            var debitQuantity = $(this).find(".debitQuantity").val();  // 贷数量
            var unitPrice = $(this).find(".unitPrice").val(); // 单价：

            if(price === ""){price = 0}else{ price = parseFloat((price*1).toFixed(2)) }
            if(creditQuantity === ""){creditQuantity = 0}
            if(debitQuantity === ""){debitQuantity = 0}
            if(unitPrice === ""){unitPrice = 0}

            if($(this).find(".mNumber").is(':visible')){
                if(creditQuantity !== 0){
                    $(this).find(".debitQuantity").prop("disabled",true);
                }else{
                    $(this).find(".debitQuantity").prop("disabled",false);
                }
                if(debitQuantity !== 0){
                    $(this).find(".creditQuantity").prop("disabled",true);
                }else{
                    $(this).find(".creditQuantity").prop("disabled",false);
                }
            }
            allBorrowPrice += price;
        });
        $("#choose_addAccount .loanItem").each(function () {
            var price = $(this).find(".price").val();
            var creditQuantity = $(this).find(".creditQuantity").val();
            var debitQuantity = $(this).find(".debitQuantity").val();
            var unitPrice = $(this).find(".unitPrice").val();

            if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
            if(creditQuantity === ""){creditQuantity = 0}
            if(debitQuantity === ""){debitQuantity = 0}
            if(unitPrice === ""){unitPrice = 0}

            if($(this).find(".mNumber").is(':visible')){
                if(creditQuantity !== 0){
                    $(this).find(".debitQuantity").prop("disabled",true);
                }else{
                    $(this).find(".debitQuantity").prop("disabled",false);
                }
                if(debitQuantity !== 0){
                    $(this).find(".creditQuantity").prop("disabled",true);
                }else{
                    $(this).find(".creditQuantity").prop("disabled",false);
                }
            }
            allLoanPrice += price;
        });

        var allPrice = parseFloat($("#accountingDetail").find(".price").html());
        $("#choose_addAccount .borrowMoreLoans").find("input:not(:disabled)").each(function () {
            if($(this).val() == ""){
                i++;
            }
        });

        if(allPrice === allBorrowPrice && allPrice === allLoanPrice && i===0){
            $("#subjectChooseBtn").prop("disabled", false)
        }else{
            $("#subjectChooseBtn").attr("disabled", true)
        }
        console.log(allBorrowPrice +'-'+ allPrice + '-' + allLoanPrice)
    });
}

// creator: 张旭博，2019-06-28 10:00:45，选择科目页面 不予下账or提交
function subjectChooseSubmit(btnName) {
    var subjectBorrow = [];
    var subjectLoan = [];
    var cashJson = [];
    $("#choose_addAccount .borrowItem").each(function () {
        var borrow = $(this).find(".subjectBorrow").attr("id");
        var subjectNames = $(this).find(".subjectBorrow").val();
        var creditQuantity  = $(this).find(".creditQuantity").val();if(creditQuantity === ""){creditQuantity = 0};
        var debitQuantity   = $(this).find(".debitQuantity").val();if(debitQuantity === ""){debitQuantity = 0};
        var unitPrice    = $(this).find(".unitPrice").val();if(unitPrice === ""){unitPrice = 0};
        var price = $(this).find(".price").val();

        var productID = $(this).attr("pid");
        var qAA = $(this).find(".subjectBorrow").attr("cn");   //quantityAssistingAccounting 数量辅助核算
        if(productID){
            if(qAA === "0"){
                creditQuantity = 0;
                unitPrice = 0;
            }else if(qAA === "1"){
                creditQuantity = $(this).find(".creditQuantity").html();
                unitPrice = $(this).find(".unitPrice").html();
            }
            subjectBorrow.push({
                "subject" : borrow ,
                "subjectNames":subjectNames ,
                "price" : price ,
                "creditQuantity" : creditQuantity,
                "debitQuantity" : 0,
                "unitPrice" : unitPrice,
                "productID" : productID
            });
        }else{
            subjectBorrow.push({
                "subject" : borrow ,
                "subjectNames":subjectNames ,
                "price" : price ,
                "creditQuantity" : creditQuantity,
                "debitQuantity" : debitQuantity,
                "unitPrice" : unitPrice
            });
        }
    });
    $("#choose_addAccount .loanItem").each(function () {
        var loan = $(this).find(".subjectLoan").attr("id");
        var subjectNames = $(this).find(".subjectLoan").val();
        var creditQuantity  = $(this).find(".creditQuantity").val();if(creditQuantity === ""){creditQuantity = 0}
        var debitQuantity   = $(this).find(".debitQuantity").val();if(debitQuantity === ""){debitQuantity = 0}
        var unitPrice    = $(this).find(".unitPrice").val();if(unitPrice === ""){unitPrice = 0}
        var price = $(this).find(".price").val();
        subjectLoan.push({
            "subject" : loan ,
            "subjectNames":subjectNames ,
            "price" : price,
            "creditQuantity" : creditQuantity,
            "debitQuantity" : debitQuantity,
            "unitPrice" : unitPrice
        });
    });
    if($("#choose_addAccount .special").text() !== ""){
        $("#choose_addAccount .special .cashFlowGroup").each(function () {
            var cashitem = $(this).find(".cash").val();
            var cashes = $.trim($(this).find(".amount").val());
            if(cashitem !== 1 && cashes !== ""){
                cashJson.push({"cashitem":cashitem,"cashes":cashes})
            }
        })
    }
   // var belong_period = $(".voucher option:selected").val();  //凭证日期
    var data = {
        mode: 1, // 一借一贷（固定）
        category: 1, // 凭证字1（固定）
        belong_peroid: 2, // 凭证日期
        subjectBorrow: "", // 借方科目
        subjectLoan: "", // 贷方科目
        cashjson: "", // 现金流量
        approve_status: 2, // 审批状态 1 待处理 5不予下账 2(改)正常提交
        is_account: 1, // 1-下账 0不予下账
        reason: '' // 不予下账原因
    }
    var billDetail = JSON.parse($("#tblCon").data('billDetail')) // 主列表返回的财务数据需传给后台
    for (var key in billDetail) {
        if (key !== 'noAccount') {
            data[key] = billDetail[key]
        }
    }
    if (btnName === 'noAccounting') {
        // 不予下账需增加和改变的参数
        data.reason = $("#reason").val()
        data.is_account = 0
        data.approve_status = 9//9(改)是不与下账审批通过
    } else {
        data.subjectBorrow = JSON.stringify(subjectBorrow)
        data.subjectLoan = JSON.stringify(subjectLoan)
        data.cashjson = JSON.stringify(cashJson)
    }
    $.ajax({
        url: "../accountant/selectSubject.do",
        data: data,
        type: "post",
        dataType: "json",
        success: function (res) {
            // 一、科目选择 和 会计录入 最后点确定那个按钮时多增加了俩返回值
            // settleType 1-表示当前有记账状态不允许继续操作
            // prompt  这个返回值是当settleType =1时给用户的提示信息
            let data = res.data;
            bounce.cancel()
            bounce_Fixed.cancel()
            if (data["settleType"] == 1) {
                operate(data["prompt"]);
            } else if (data.isSelected == false) {
                operate("您选择的月份已经结账");
            } else {
                if (data.res == 1) {
                    layer.msg('操作成功！')
                    showMainCon(6);
                    getMainList();
                } else {
                    operate("操作失败");
                }
            }
        }
    })
}
// creator:孟闯闯，2017-2-17  12:00:00，召唤出科目树
var subjectInputObj = null;
function getActsList(obj) {
    subjectInputObj = obj;
    $(".acFoot").children("p").html("");
    bounce_Fixed.show($("#Accounting"));
    $("#Accounting table td").eq(0).click();
}

// creator:孟闯闯，2017-2-17  12:00:00，确定选择的科目
function getAccount() {
    if(subjectInputObj.attr("id") !== undefined){
        var subjectNo = subjectInputObj.attr("id");
    }else{
        var subjectNo = "";
    }
    var treeItemActive = $("#Accounting").find(".ty-treeItemActive");   //科目列表中当前选中的科目
    var subjectId = treeItemActive.attr("id");
    var subjectUnit = treeItemActive.attr("title");
    var subjectName = treeItemActive.text().split(" ")[1];
    var qAA = treeItemActive.attr("cn");    //quantityAssistingAccounting 数量辅助核算
    if(qAA === "1"){
        subjectInputObj.siblings(".mNumber").show();
        subjectInputObj.siblings(".mNumber").find("input").prop("disabled",false);
    }else if(qAA === "0"){
        subjectInputObj.siblings(".mNumber").hide();
        subjectInputObj.siblings(".mNumber").find("input").prop("disabled",true);
    }
    if(treeItemActive.next().length<=0){
        var level1 = treeItemActive.parents(".level2").prev().text().split(" ")[1];
        var level2 = treeItemActive.parents(".level3").prev().text().split(" ")[1];
        var level3 = treeItemActive.parents(".level4").prev().text().split(" ")[1];
        if(level3 !== undefined){
            //这个是四级科目
            subjectName = level1 + '-' +  level2 + '-' + level3 + '-' + subjectName;
        }else if(level2 !== undefined){
            //这个是三级科目
            subjectName = level1 + '-' +  level2 + '-' + subjectName;

        }else if(level1 !== undefined){
            //这个是二级科目
            subjectName = level1 + '-' + subjectName;
        }
        subjectInputObj.val(subjectName);
        subjectInputObj.attr("id",subjectId);
        subjectInputObj.attr("cn",qAA);   //数量辅助核算
        subjectInputObj.siblings(".mNumber").find(".measure_unit").html(subjectUnit);
        bounce_Fixed.cancel();
    }


    if(subjectId.substring(0,4) === '1001' || subjectId.substring(0,4) === '1002' ){
        if(subjectNo === '1001' || subjectNo === '1002'){}else{
            if(subjectInputObj.parent().attr("data-to") === undefined){
                subjectInputObj.parent().attr("data-to","special"+specialIndex);
                var specialStr =        '<div class="cashFlowGroup special'+specialIndex+'">'+
                    '<div class="normalItem">'+
                    '<span>现金流量：</span>'+
                    '<select class="cash">'+
                    '<option value="1">-----请选择现金流量项目-----</option>'+
                    $(".cashFlowOption").html()+
                    '</select> '+
                    '<span>金额：</span>'+
                    '<input class="amount" type="text"/>'+
                    '</div>'+
                    '</div>';
                subjectInputObj.parents(".borrowMoreLoans").next(".special").append(specialStr);
                specialIndex ++;
            }
        }
    }else{
        if(subjectNo.substring(0,4) === '1001' || subjectNo.substring(0,4) === '1002'){
            var specialId = subjectInputObj.parent().attr("data-to");
            subjectInputObj.parents(".borrowMoreLoans").next(".special").find("."+specialId).remove();
            subjectInputObj.parent().removeAttr("data-to");
        }
    }
}

// creator: 张旭博，2018-03-19 11:30:29，获取现金流量下拉列表内容
function getCashFlowOption() {
    $.ajax({
        url: "getCashItems.do",
        data: {},
        async: false,
        type: "post",
        dataType: "json",
        success: function (data) {
            var cashList = data["data"];
            var optionStr = "";
            for(var i=0;i<cashList.length;i++){
                optionStr +=               '<option value="'+cashList[i].cashFlowCode+'">'+cashList[i].cashFlowName+'</option>'
            }
            $(".cashFlowOption").html(optionStr);
        }
    });
}

// creator: 张旭博，2018-03-19 11:30:41，获取已经存在的借贷方拼接字符串
function showBorLoan(borInfo,loanInfo) {
    //num :
    // 1-正常待审批
    // 2-正常已批准
    // 3-正常已驳回
    // 4-修改待审批

    // 5-修改已批准
    // 6-修改已驳回

    // 7-不予下账待审批
    // 8-不予下账已批准

    // 9-不予下账已驳回
    var borLoanStr = '';    //借贷字符串
    var handleStr = '';     //是否有取消和新增按钮
    var fixed = '';         //为第一组借方或贷方添加样式
    var specialStr = '';
    var specialIndexThis = 0;
    //判断是否为初始状态（传参为0则为初始状态，没有对输入框赋值）
    if(borInfo === 0 && loanInfo === 0){

        //静态页面，无数据

        borLoanStr =    '<div class="borrowItem fixed">'+
            '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="" class="subjectBorrow" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>'+
            '<div class="loanItem fixed">'+
            '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="" class="subjectLoan" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>';
    }else {

        //动态页面，有数据
        if(borInfo[0].productID){
            var goodListStr = '';
            var borrowItemStr = '';
            for (var j = 0; j < borInfo.length; j++) {
                if(borInfo[j].productID){
                    goodListStr +=  '<tr class="borrowItem" id="special'+borInfo[j].productID+'" pid="'+borInfo[j].productID+'">' +
                        '<td><input placeholder="请点击选择科目" class="subjectBorrow" type="text" id="'+borInfo[j].borrowSubject+'" value="' + borInfo[j].borrowNames + '" cn="'+borInfo[j].quantityAssistingAccounting+'" onclick="getActsList($(this))" disabled style="width: 100%"></td>' +       //含税合计
                        '</tr>';
                }else{
                    borrowItemStr +=   '<div class="borrowItem">'+
                        '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="'+borInfo[j].borrowSubject+'" class="subjectBorrow" type="text" value="' + borInfo[j].borrowNames + '" disabled/>'+
                        '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text" value="'+borInfo[j].borrowAmount+'" disabled/></div>'+
                        '</div>';
                }
            }

            borLoanStr = '<div class="borrowItems">' +
                '<table class="ty-table specialInvoiceGoods">' +
                '<thead>' +
                '<tr>' +
                '<td>货物或应税劳务、服务名称</td>' +
                '<td>规格型号</td>' +
                '<td>单位</td>' +
                '<td>数量</td>' +
                '<td>单价</td>' +
                '<td>金额</td>' +
                '<td>所属发票</td>' +
                '<td>选择科目</td>' +
                '</tr>' +
                '</thead>' +
                '<tbody>'+goodListStr+'</tbody>'+
                '</table>'+
                '</div>' + borrowItemStr;
            for (var k = 0; k < loanInfo.length; k++) {
                //拼接贷方字符串
                borLoanStr +=   '<div class="loanItem ' + fixed + '" '+specialStr +'>' +
                    '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="' + loanInfo[k].loanSubject + '" class="subjectLoan" type="text" onclick="getActsList($(this))" value="' + loanInfo[k].loanNames + '" id="' + loanInfo[k].loanSubject + '" disabled/>' +
                    '   <div class="m5 mPrice" style="'+mPriceStyleStr+'"><span>金额：</span><input class="price" type="text" value="' + loanInfo[k].loanAmount + '" disabled/></div>' +
                    '</div>';
            }
        }else{
            for (var j = 0; j < borInfo.length; j++) {
                //判端多借多贷的显示（只有一借的时候 显示新增借方，多借的时候 第一借没有按钮 最后一借有两个按钮 新增和取消  其他都是只有一个取消按钮
                if (borInfo.length === 1) {
                    handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</span>';
                    fixed = 'fixed';
                } else {
                    if (j === 0) {
                        handleStr = '';
                        fixed = 'fixed';
                    } else if (j === borInfo.length - 1) {
                        handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</span>' +
                            '<span class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</span>';
                        fixed = '';
                    } else {
                        handleStr = '<span class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</span>';
                        fixed = '';
                    }
                }

                if(borInfo[j].quantityAssistingAccounting === "1"){
                    var mPriceStyleStr = '';
                    var mNumberStyleStr = '';
                }else if(borInfo[j].quantityAssistingAccounting === "0"){
                    var mPriceStyleStr = '';
                    var mNumberStyleStr = 'display:none';
                }

                if(borInfo[j].borrowSubject.substring(0,4) === '1001' || borInfo[j].borrowSubject.substring(0,4) === '1002'){
                    specialStr = 'data-to="special'+specialIndexThis+'a"';
                    specialIndexThis ++ ;
                }
                //拼接借方字符串
                borLoanStr +=   '<div class="borrowItem ' + fixed + '" '+specialStr +'>' +
                    '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="' + borInfo[j].borrowSubject + '" class="subjectBorrow" type="text" onclick="getActsList($(this))" value="' + borInfo[j].borrowNames + '" id="' + borInfo[j].borrowSubject + '"/>' +
                    '   <div class="handle">' + handleStr +
                    '   </div>' +
                    '   <div class="m5 mPrice" style="'+mPriceStyleStr+'"><span>金额：</span><input class="price" type="text" value="' + borInfo[j].borrowAmount + '"/></div>' +
                    '   <div class="m5 mNumber" style="'+mNumberStyleStr+'">' +
                    '       <span>借数量：</span><input class="creditQuantity w65" type="text" value="' + borInfo[j].creditQuantity + '"/>' +
                    '       <span>贷数量：</span><input class="debitQuantity w65" type="text" value="' + borInfo[j].debitQuantity + '"/>' +
                    '       <span>单价：</span><input class="unitPrice w65" type="text" value="' + borInfo[j].unitPrice + '"/>' +
                    '       <span>单位：</span><span class="measure_unit">' + borInfo[j].measureUnit + '</span>' +
                    '   </div>' +
                    '</div>';
            }
            for (var k = 0; k < loanInfo.length; k++) {
                //贷方逻辑同上
                if (loanInfo.length === 1) {
                    handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span>';
                    fixed = 'fixed';
                } else {
                    if (k === 0) {
                        handleStr = '';
                        fixed = 'fixed';
                    } else if (k === loanInfo.length - 1) {
                        handleStr = '<span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span>' +
                            '<span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>';
                        fixed = '';
                    } else {
                        handleStr = '<span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>';
                        fixed = '';
                    }
                }
                if(loanInfo[k].loanSubject.substring(0,4) === '1001' || loanInfo[k].loanSubject.substring(0,4) === '1002'){
                    specialStr = 'data-to="special'+specialIndexThis+'a"';
                    specialIndexThis ++ ;
                }
                if(loanInfo[k].quantityAssistingAccounting === "1"){
                    var mPriceStyleStr = '';
                    var mNumberStyleStr = '';
                }else if(loanInfo[k].quantityAssistingAccounting === "0"){
                    var mPriceStyleStr = '';
                    var mNumberStyleStr = 'display:none';
                }
                //拼接贷方字符串
                borLoanStr +=   '<div class="loanItem ' + fixed + '" '+specialStr +'>' +
                    '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="' + loanInfo[k].loanSubject + '" class="subjectLoan" type="text" onclick="getActsList($(this))" value="' + loanInfo[k].loanNames + '" id="' + loanInfo[k].loanSubject + '"/>' +
                    '   <div class="handle">' + handleStr +
                    '   </div>' +
                    '   <div class="m5 mPrice" style="'+mPriceStyleStr+'"><span>金额：</span><input class="price" type="text" value="' + loanInfo[k].loanAmount + '"/></div>' +
                    '   <div class="m5 mNumber" style="'+mNumberStyleStr+'">' +
                    '       <span>借数量：</span><input class="creditQuantity w65" type="text" value="' + loanInfo[k].creditQuantity + '"/>' +
                    '       <span>贷数量：</span><input class="debitQuantity w65" type="text" value="' + loanInfo[k].debitQuantity + '"/>' +
                    '       <span>单价：</span><input class="unitPrice w65" type="text" value="' + loanInfo[k].unitPrice + '"/>' +
                    '       <span>单位：</span><span class="measure_unit">' + loanInfo[k].measureUnit + '</span>' +
                    '   </div>' +
                    '</div>';
            }
        }
    }
    return borLoanStr;      //返回借贷字符串
}

// creator：张旭博，2017-09-13 10:00:48，返回现金流量部分字符串
function showCashFlow(cashjson){
    var str = '';
    for(var l=0;l<cashjson.length;l++){
        str  += '<div class="cashFlowGroup special'+l+'a">'+
            '<div class="normalItem">'+
            '<span>现金流量：</span>'+
            '<select class="cash" value="'+cashjson[l].cashitem+'">'+
            '<option value="'+cashjson[l].cashitem+'">'+cashjson[l].cashitemName+'</option>'+
            '<option value="1">-----请选择现金流量项目-----</option>'+
            $(".cashFlowOption").html()+
            '</select> '+
            '<span>金额：</span>'+
            '<input class="amount" type="text" value="'+cashjson[l].cash+'"/>'+
            '</div>'+
            '</div>';
    }
    return str;
}


// creator: 张旭博，2019-06-20 15:45:03，打开数据详情
function accountingDetailBtn() {
    var kind = $("#accountingDetail").data('kind')
    if (kind == 1) {
        bounce_Fixed.show($("#reimburse"))
    } else {
        bounce_Fixed.show($("#accountingDetail"))
    }

}
// creator: 张旭博，2017-09-13 15:30:05，插入凭证详情
function showAccountingDetail(detailId, kind) {
    $.ajax({
        url: "../data/getAccountingDetail.do",
        data: {
            detailId: detailId,
            kind: kind
        },
        success: function (data) {
            if(data == ""){
                return false ;
            }
            var content = data["content"];
            var kind = data["kind"];
            var debitSubject = data["debitSubject"]; // 增专科目关联数据

            var accountingDetailStr = "";
            var specialInvoiceGoodsStr = "";
            $("#accountingDetail").data('kind', kind)
            if(kind === 1){
                var borrowMoreLoanStr = '';
                var cashFlowStr = '';
                var approve_status = Number($(".ty-secondTab li.ty-active").attr('state'))
                var billList = content.financeReimburseBillHashSet

                if($("#pend_addAccount").is(':visible') && approve_status !== 6 && approve_status !== 9){
                    for (var i in billList) {
                        var goodList = billList[i].financeReimburseBillItemHashSet
                        var pictureInfo = billList[i].financeReimburseBillAttachmentHashSet
                        var billCatName   = billList[i].billCatName;  //票据类型
                        var certificationState = billList[i].certificationState

                        var picture = pictureInfo.length > 0 ? pictureInfo[0].path : ''

                        for (var j in goodList) {
                            var amount = '';
                            if(certificationState === "2" || certificationState === "3" || billCatName === "增值税普通发票"){
                                amount = goodList[j].amount;
                            }else{
                                amount = goodList[j].price;
                            }
                            var goodStr =   '<td>' + goodList[j].itemName + '</td>' +       //货物或应税劳务、服务名称
                                '<td>' + goodList[j].model + '</td>' +          //规格型号
                                '<td>' + goodList[j].unit + '</td>' +           //单位
                                '<td class="creditQuantity">' + (goodList[j].itemQuantity === 0? "": handleNull(goodList[j].itemQuantity)) + '</td>' +   //数量
                                '<td class="unitPrice">' + (goodList[j].uniPrice === 0? "": handleNull(goodList[j].uniPrice)) + '</td>' +       //单价
                                '<td><input class="price" value="' + amount + '" style="display: none">' + amount + '</td>' +         //金额
                                '<td><span class="ty-color-blue" data-path="'+picture+'" onclick="seePic($(this))">查看票据内容</span></td>';
                            $("#special"+goodList[j].id).prepend(goodStr);
                        }
                    }
                    $("#pend_addAccount").find('input,select').not('.voucher').prop('disabled', true)
                    if (approve_status === 1 || approve_status === 4 || approve_status === 5) {
                        $("#pend_addAccount .borrowItems").find('input,select').prop('disabled', true)
                    } else {
                        $("#pend_addAccount .borrowItems").find('input,select').prop('disabled', false)
                    }
                }else{
                    if (billList.length > 0) {
                        for (var i in billList) {
                            var goodList = billList[i].financeReimburseBillItemHashSet
                            var pictureInfo = billList[i].financeReimburseBillAttachmentHashSet
                            var billCatName   = billList[i].billCatName;  //票据类型
                            var certificationState = billList[i].certificationState

                            var picture = pictureInfo.length > 0 ? pictureInfo[0].path : ''
                            for (var j in goodList) {
                                var amount = '';

                                //认证通过后 金额为金额合计  无需认证和认证失败  金额为含税合计
                                if(certificationState === "2" || certificationState === "3" || billCatName === "增值税普通发票"){
                                    amount = goodList[j].amount;
                                }else{
                                    amount = goodList[j].price;
                                }
                                specialInvoiceGoodsStr += '<tr class="borrowItem borrow" pid="' + goodList[j].id + '">' +
                                    '<td>' + goodList[j].itemName + '</td>' +       //货物或应税劳务、服务名称
                                    '<td>' + goodList[j].model + '</td>' +          //规格型号
                                    '<td>' + goodList[j].unit + '</td>' +           //单位
                                    '<td class="creditQuantity">' + (goodList[j].itemQuantity === 0? '': handleNull(goodList[j].itemQuantity)) + '</td>' +   //数量
                                    '<td class="unitPrice">' + (goodList[j].uniPrice === 0? '': handleNull(goodList[j].uniPrice)) + '</td>' +       //单价
                                    '<td><input class="price" value="' + amount + '" style="display: none">' + amount + '</td>' +          //金额
                                    '<td><span class="ty-color-blue" data-path="'+picture+'" onclick="seePic($(this))">查看票据内容</span></td>' +          //金额
                                    '<td><input placeholder="请点击选择科目" class="subjectBorrow" type="text" id="' + goodList[j].subject + '"  value="'+goodList[j].subjectName+'" onclick="getActsList($(this))" style="width: 100%"></td>' +
                                    '</tr>';
                            }
                        }

                        borrowMoreLoanStr = '<div class="borrowItems">' +
                            '<table class="ty-table specialInvoiceGoods">' +
                            '<thead>' +
                            '<tr>' +
                            '<td>货物或应税劳务、服务名称</td>' +
                            '<td>规格型号</td>' +
                            '<td>单位</td>' +
                            '<td>数量</td>' +
                            '<td>单价</td>' +
                            '<td>金额</td>' +
                            '<td>所属发票</td>' +
                            '<td width="324">选择科目</td>' +
                            '</tr>' +
                            '</thead>' +
                            '<tbody>' + specialInvoiceGoodsStr + '</tbody>' +
                            '</table>' +
                            '</div>';
                        var borrow = JSON.parse(debitSubject.borrow)
                        var loan = JSON.parse(debitSubject.loan)
                        var cashjson = JSON.parse(debitSubject.cash)
                        for(var m in borrow){
                            borrowMoreLoanStr +=    '<div class="borrowItem borrow">'+
                                '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="' + borrow[m].subject + '" class="subjectBorrow" type="text" value="' + borrow[m].subjectNames + '" disabled/>'+
                                '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text" value="'+borrow[m].price+'" disabled/></div>'+
                                '</div>';
                        }
                        for(var n in loan) {
                            //拼接贷方字符串
                            borrowMoreLoanStr +=    '<div class="loanItem loan">' +
                                '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="' + loan[n].subject + '" class="subjectLoan" type="text" value="' + loan[n].subjectNames + '" disabled/>' +
                                '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text" value="' + loan[n].price + '" disabled/></div>' +
                                '</div>';
                        }
                        for(var l in cashjson) {
                            cashFlowStr  += '<div class="cashFlowGroup special'+l+'a">'+
                                '<div class="normalItem">'+
                                '<span>现金流量：</span>'+
                                '<select class="cash" value="'+cashjson[l].cashitem+'" disabled>'+
                                $(".cashFlowOption").html()+
                                '</select> '+
                                '<span>金额：</span>'+
                                '<input class="amount" type="text" value="'+cashjson[l].cashes+'" disabled/>'+
                                '</div>'+
                                '</div>';
                        }

                    }
                    var state = Number($(".ty-secondTab li.ty-active").attr('state'))
                    if (state) {
                        if (state === 6 || state === 9) {
                            $("#pend_addAccount .special").html(cashFlowStr);
                            $("#pend_addAccount .special .cash").each(function () {
                                $(this).val($(this).attr('value'))
                            });
                            $("#pend_addAccount .borrowMoreLoans").html(borrowMoreLoanStr);
                        }
                    } else {
                        $("#choose_addAccount .special").html(cashFlowStr);
                        $("#choose_addAccount .special .cash").each(function () {
                            $(this).val($(this).attr('value'))
                        });
                        $("#choose_addAccount .borrowMoreLoans").html(borrowMoreLoanStr);
                    }
                }
                $("#accountingDetail .detailed").html('<div class="price hd"></div>');
                getReimburseDetail(content.id)
                return false;
            }
            switch (kind){
                case 2:
                    if (content.type === "1") {
                        var type = "内部转账支票";
                    } else if (content.type === "2") {
                        var type = "现金支票";
                    }
                    accountingDetailStr     =   '<tr><td>项目</td><td>支出</td></tr>' +
                        '<tr><td>票据种类</td><td>' + content.billCat + '</td></tr>' +
                        '<tr><td>票据所属月份</td><td>' + chargeBillPeriod(content.billPeriod) + '</td></tr>' +
                        '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                        '<tr><td>票据数量</td><td>' + content.billQuantity + '</td></tr>' +
                        '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                        '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                        '<tr><td>经手人</td><td class="operatorName">' + content.financialHandling + '</td></tr>' +
                        '<tr><td>支出方式</td><td>' + type + '</td></tr>' +
                        '<tr><td>银行账户</td><td>' + content.bankName + ' ' + content.account + '</td></tr>' +
                        '<tr><td>支票号</td><td>' + content.chequeNo + '</td></tr>' +
                        '<tr><td>支票到期日</td><td>' + content.expireDate.substring(0,10) + '</td></tr>' +
                        '<tr><td>接收日期</td><td>' + content.receiveDate.substring(0,10) + '</td></tr>' +
                        '<tr><td>接收经手人</td><td>' + content.receiver + '</td></tr>' +
                        '<tr><td>支付经手人</td><td>' + content.operator + '</td></tr>' +
                        '<tr><td>收款单位</td><td>' + content.receiveCorp + '</td></tr>' +
                        '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>';
                    break;
                case 3:
                    $(".source").attr("tittle", 3);
                    if (content.billType === "1") {
                        var billType = "收入";
                    } else if (data.content.billType === "2") {
                        var billType = "支出";
                    }
                    //收入中的转账支票
                    if(content.billType === "1"){
                        accountingDetailStr     =   '<tr><td>项目</td><td>收入</td></tr>' +
                            '<tr><td>类别</td><td>' + chargeGenre(content.category) + '</td></tr>' +
                            '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                            '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                            '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                            '<tr><td>经手人</td><td class="operatorName">' + content.operatorName + '</td></tr>' +
                            '<tr><td>收入方式</td><td>' + C_payway(content.type) + '</td></tr>';
                        if(content.type === 1){
                            accountingDetailStr +=  '<tr><td>收到支票日期</td><td>' + content.receiveDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>付款单位</td><td>' + content.payer + '</td></tr>' +
                                '<tr><td>支票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>支票到期日</td><td>' + content.expireDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>出具支票单位</td><td>' + content.originalCorp + '</td></tr>' +
                                '<tr><td>出具支票银行</td><td>' + content.bankName + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }else if(content.type === 2){
                            accountingDetailStr +=  '<tr><td>收到汇票日期</td><td>' + content.receiveDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>付款单位</td><td>' + content.payer + '</td></tr>' +
                                '<tr><td>汇票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>汇票到期日</td><td>' + content.expireDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>原始出具汇票单位</td><td>' + content.originalCorp + '</td></tr>' +
                                '<tr><td>出具汇票银行</td><td>' + content.bankName + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }
                    }
                    //支出中的转账支票
                    else if(content.billType === "2"){
                        accountingDetailStr     =   '<tr><td>项目</td><td>' + billType + '</td></tr>' +
                            '<tr><td>票据种类</td><td>' + content.billCat + '</td></tr>' +
                            '<tr><td>票据所属月份</td><td>' + chargeBillPeriod(content.billPeriod) + '</td></tr>' +
                            '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                            '<tr><td>票据数量</td><td>' + content.billQuantity + '</td></tr>' +
                            '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                            '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                            '<tr><td>经手人</td><td class="operatorName">' + content.operatorName + '</td></tr>' +
                            '<tr><td>支出方式</td><td>' + C_payway(content.type) + '</td></tr>';
                        if(content.type === 1){
                            accountingDetailStr +=  '<tr><td>支票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>收款单位</td><td>' + content.oppositeCorp + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }else if(content.type === 2){
                            accountingDetailStr +=  '<tr><td>汇票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>收款单位</td><td>' + content.oppositeCorp + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }
                    }
                    break;
                case 4:
                    var method  =   "";             //支出方式
                    var bankStr =   "";             //转账银行
                    if (content.method === "1") {
                        method = "现金";
                    } else if (content.method === "5") {
                        method = "银行转账";
                        bankStr = '<tr><td>转账银行：</td><td>' + content.oppositeAccount + '</td></tr>';
                    }
                    accountingDetailStr     =   '<tr><td>项目   </td><td>支出</td></tr>' +
                        '<tr><td>票据种类</td><td>' + content.billCat + '</td></tr>' +
                        '<tr><td>票据所属月份</td><td>' + chargeBillPeriod(content.billPeriod) + '</td></tr>' +
                        '<tr><td>摘要   </td><td class="summary">' + content.summary + '</td></tr>' +
                        '<tr><td>票据数量</td><td>' + content.billQuantity + '</td></tr>' +
                        '<tr><td>金额   </td><td class="price">' + content.billAmount + '</td></tr>' +
                        '<tr><td>用途   </td><td class="purpose">' + content.purpose + '</td></tr>' +
                        '<tr><td>经手人 </td><td class="operatorName">' + content.operatorName + '</td></tr>' +
                        '<tr><td>支出方式</td><td>' + method + '</td></tr>' + bankStr +
                        '<tr><td>收款单位</td><td>' + content.oppositeCorp + '</td></tr>' +
                        '<tr><td>备注   </td><td class="memo">' + content.memo + '</td></tr>' ;
                    break;
                case 5:
                    $(".source").attr("tittle", 0);
                    var method  =   "";             //收入方式
                    var bankStr =   "";             //收款银行

                    if (content.method === "1") {
                        method  =   "现金";
                        bankStr =   '';
                    } else if (content.method === "5") {
                        method  =   "银行转账";
                        bankStr =   '<tr><td>收款银行</td><td>' + content.accountBank + '</td></tr>' +
                            '<tr><td>到账时间</td><td>' + content.receiveAccountDate.substring(0,10) + '</td></tr>';
                    }

                    accountingDetailStr     =   '<tr><td>项目</td><td>收入</td></tr>' +
                        '<tr><td>类别</td><td>' + chargeGenre(content.genre) + '</td></tr>' +
                        '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                        '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                        '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                        '<tr><td>经手人</td><td class="operatorName">' + content.auditorName + '</td></tr>' +
                        '<tr><td>收入方式</td><td>' + method + '</td></tr>' +
                        '<tr><td>付款单位</td><td>' + content.oppositeCorp + '</td></tr>' + bankStr +
                        '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>';
                    break;
                default:
                    accountingDetailStr     =   '';
            }
            $("#accountingDetail .detailed").html('<table class="ty-table"><body>' + accountingDetailStr + '</body></table>');

        }
    });
}
//creator:孟闯闯,2017-2-21 10:25:00 ，"待选择=》选择科目=》不予下账=》弹框"
function noAccounting(num) {

    if (num == 1) {
        if (!accountantAuth.toChooseRefuse) {
            $("#errorTip .tipWord").html("您无此操作权限！");
            bounce.show($("#errorTip"));
            return false;
        }
    }
    if (num == 2 || num == 7) {
        if (!accountantAuth.isAgreeRefuse) {
            $("#errorTip .tipWord").html("您无此操作权限！");
            bounce.show($("#errorTip"));
            return false;
        }
    }
    if (num == 3 || num == 8) {
        if (!accountantAuth.isRejectedRefuse) {
            $("#errorTip .tipWord").html("您无此操作权限！");
            bounce.show($("#errorTip"));
            return false;
        }
    }
    bounce_confirm('')
    var str = '<p id="mt_tip_ms1" style="text-align: center; padding:10px 0">是否确定对此条数据不予下账？</p>' +
        '<textarea id="reason" class="reason" rows="3" style="width: 300px" placeholder="请填写不予下账理由"></textarea>';
    $("#mtTip1 .shu1").html(str) ;
    bounce_Fixed.show($("#mtTip1")) ;
    $("#reason").css("color", "gray") ;
    $("#sure").show().next().html("取消").removeClass("blue");
}

function bounce_confirm(html, fuc) {
    bounce_Fixed.show($("#tips"))
    $("#tips .content").html(html)
    $("#tips .sureBtn").unbind().on('click', fuc)
}


// creator: 孟闯闯，2017-2-17  12:00:00，鼠标进入图片
function imgEnter(obj) {
    var path = obj.attr("src");
    $("#bigImag").attr("src", path);
    $("#bigImagcon").show().css("position", "absolute");
}

// creator: 孟闯闯，2017-2-17  12:00:00，鼠标离开图片
function imgOut(obj) {
    $("#bigImagcon").hide();
}
// creator: 孟闯闯，2017-2-19  14:00:00，温馨提示
function operate(tip) {
    $("#errorTip .tipWord").html(tip);
    bounce.show($("#errorTip"));
}

// creator: 孟闯闯,2017-2-21 10:30:00 ，"操作提示弹框"关闭
function bounce_cancel(obj) {
    obj.parent().parent().hide().parent().hide();
}
// creator: 张旭博，2019-08-16 15:48:27，查看票据内容
function seePic(obj) {
    var path = obj.data('path')
    if (path) {
        seePicture(path)
    } else {
        layer.msg('未上传图片')
    }
}

// 判别票据所属月份
function chargeBillPeriod( val ){
    switch( Number(val) ){
        case 1 : return "本月票据" ; break ;
        case 2 : return "非本月票据" ; break ;
        default : return "非本月票据";
    }
}

// creator: 张旭博，2018-03-19 11:36:41，转换支付方式
function C_payway(type) {
    switch(Number(type)){
        case 1:
            return "内部转账支票";
            break;
        case 2:
            return "现金汇票";
            break;
        case 3:
            return "承兑汇票";
            break;
        case 4:
            return "外部转账支票";
            break;
    }
}