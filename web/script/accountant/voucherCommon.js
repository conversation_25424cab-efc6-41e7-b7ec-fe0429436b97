// creator: 张旭博，2019-07-15 09:32:19，初始化三级和四级弹窗
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#billInfo"));
bounce_Fixed2.cancel();
// creator: 李玉婷，2022-04-29 09:19:54，指定月份不同类型的凭证
function getVoucherByMonth(type, month) {
    let obj = ``;
    let monthStr = month.replace("-","年");
    monthStr = monthStr + '月';
    //type 0-手动录入的凭证，1-会计录入的凭证，2-系统自动选择的凭证。不填为查询所有
    //month     String         选填，需结账的月份 yyyy-MM。不填为查询所有
    let param = {
        "type": type,
        "month": month
    }
    if (type === ""){
        obj =  $(".mainCon5 .tblVoucher1");
    } else {
        obj =  $(".mainCon5 .tblVoucher2");
    }
    obj.show().siblings().hide();
    $(".mainCon5 .voucherParam").html(JSON.stringify(param));
    $.ajax({
        url: '../accountant/getVoucherByMonth.do',
        data: param,
        success: function (res) {
            let list = res.data, count = 0;
            let detail = ``,html = ``;
            if (list && list.length > 0) {
                count = list.length;
                for(var i=0;i<list.length;i++){
                    var listBorrow  = list[i]["borrowSubject"] || [];
                    var listLoan  = list[i]["loanSubject"] || [];
                    var borrowStr = '';
                    var loanStr = '';
                    var borrowPrice = '';
                    var loanPrice = '';
                    for(var j = 0; j<listBorrow.length;j++){
                        borrowStr += '<div><div class="ellipsis" title="'+listBorrow[j].subjectName+'">'+listBorrow[j].subjectName+'</div></div>';
                        borrowPrice += '<div>'+listBorrow[j].amount+'</div>';
                        loanPrice += '<div></div>'
                    }
                    for(var k = 0; k<listLoan.length;k++){
                        borrowStr += '<div><div class="ellipsis" title="'+listLoan[k].subjectName+'">'+listLoan[k].subjectName+'</div></div>';
                        borrowPrice += '<div></div>';
                        loanPrice += '<div>'+listLoan[k].amount+'</div>'
                    }
                    if (type === "") {
                        html +=
                            `<tr>
                            <td>${new Date(list[i].createDate).format("yyyy-MM-dd")}</td>
                            <td>${list[i].summary}</td>
                            <td>${handleNull(borrowStr)} ${handleNull(loanStr)}</td>
                            <td>${handleNull(borrowPrice)}</td>
                            <td>${handleNull(loanPrice)}</td>
                            <td>${list[i].financeCreateName || '会计高管'} ${ new Date(list[i].financeCreateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            <td>${list[i].operatorName || '会计高管'} ${ new Date(list[i].addtime).format("yyyy-MM-dd hh:mm:ss")}</td>
                            <td>${list[i].auditorName} ${ new Date(list[i].auditDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                        </tr>`;
                    } else {
                        let operBtn = ``;
                        if (type === 1 || (type === 2 && list[i].source === '2')) {
                            operBtn =
                                `<td><span class="ty-color-gray">查看</span></td>`;
                        } else {
                            operBtn =
                                `<td>
                            <span class="funbtn ty-color-blue" onclick="getAccountingDetail(${list[i].detailId}, ${list[i].kind})" data-type="2">查看</span>
                            <span class="hd">${JSON.stringify(list[i])}</span>
                            </td>`;
                        }
                        html +=
                            `<tr>
                            <td>${new Date(list[i].createDate).format("yyyy-MM-dd")}</td>
                            <td>${new Date(list[i].billDate).format("yyyy-MM-dd")}</td>
                            <td>${list[i].summary}</td>
                            <td>${handleNull(borrowStr)} ${handleNull(loanStr)}</td>
                            <td>${handleNull(borrowPrice)}</td>
                            <td>${handleNull(loanPrice)}</td>
                            <td>${list[i].financeCreateName || '会计高管'} ${ new Date(list[i].financeCreateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            <td>${list[i].operatorName || '会计高管'} ${ new Date(list[i].addtime).format("yyyy-MM-dd hh:mm:ss")}</td>
                           ${operBtn}
                        </tr>`;
                    }
                }
            }
            obj.find("table tbody").html(html);
            if (type === 0) {
                detail = `<p>${monthStr}手动选择科目的凭证  共${count}张</p>`;
            } else if (type === 1) {
                detail = `<p>${monthStr}会计录入的凭证  共${count}张</p>`;
            } else if (type === 2) {
                detail = `<p>${monthStr}系统生成的凭证  共${count}张</p>`;
            } else {
                detail = `<p>${monthStr}的凭证  共${count}张</p>`;
            }
            obj.find(".voucherDetails").html(detail);
            showMainCon(5);
        }
    });
}
// creator: 李玉婷，2022-05-16 09:48:25，查凭证
function voucherSearch(obj) {
    let val = obj.prev().val();
    if (val !== "") {
        let param = JSON.parse($(".mainCon5 .voucherParam").html());
        $.ajax({
            url: '../accountant/searchVoucher.do',
            data: {
                "type": param.type,
                "settleMonth": param.month,
                "con": val,
            },
            success: function (res) {
                let list = res.data;
                let html = ``;
                if (list && list.length > 0) {
                    for(var i=0;i<list.length;i++){
                        var listBorrow  = list[i]["borrowSubject"] || [];
                        var listLoan  = list[i]["loanSubject"] || [];
                        var borrowStr = '';
                        var loanStr = '';
                        var borrowPrice = '';
                        var loanPrice = '';
                        for(var j = 0; j<listBorrow.length;j++){
                            borrowStr += '<div><div class="ellipsis" title="'+listBorrow[j].subjectName+'">'+listBorrow[j].subjectName+'</div></div>';
                            borrowPrice += '<div>'+listBorrow[j].amount+'</div>';
                            loanPrice += '<div></div>'
                        }
                        for(var k = 0; k<listLoan.length;k++){
                            borrowStr += '<div><div class="ellipsis" title="'+listLoan[k].subjectName+'">'+listLoan[k].subjectName+'</div></div>';
                            borrowPrice += '<div></div>';
                            loanPrice += '<div>'+listLoan[k].amount+'</div>'
                        }
                        if (param.type === "") {
                            html +=
                                `<tr>
                                    <td>${new Date(list[i].createDate).format("yyyy-MM-dd")}</td>
                                    <td>${list[i].summary}</td>
                                    <td>${handleNull(borrowStr)} ${handleNull(loanStr)}</td>
                                    <td>${handleNull(borrowPrice)}</td>
                                    <td>${handleNull(loanPrice)}</td>
                                    <td>${list[i].financeCreateName || '会计高管'} ${ new Date(list[i].financeCreateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                                    <td>${list[i].operatorName || '会计高管'} ${ new Date(list[i].addtime).format("yyyy-MM-dd hh:mm:ss")}</td>
                                    <td>${list[i].auditorName} ${ new Date(list[i].auditDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                                </tr>`;
                        } else {
                            html +=
                                `<tr>
                                    <td>${new Date(list[i].createDate).format("yyyy-MM-dd")}</td>
                                    <td>${new Date(list[i].billDate).format("yyyy-MM-dd")}</td>
                                    <td>${list[i].summary}</td>
                                    <td>${handleNull(borrowStr)} ${handleNull(loanStr)}</td>
                                    <td>${handleNull(borrowPrice)}</td>
                                    <td>${handleNull(loanPrice)}</td>
                                    <td>${list[i].financeCreateName || '会计高管'} ${ new Date(list[i].financeCreateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                                    <td>${list[i].operatorName || '会计高管'} ${ new Date(list[i].addtime).format("yyyy-MM-dd hh:mm:ss")}</td>
                                    <td>
                                    <span class="funbtn ty-color-blue" onclick="getAccountingDetail(${list[i].detailId}, ${list[i].kind})" data-type="2">查看</span>
                                    <span class="hd">${JSON.stringify(list[i])}</span>
                                    </td>
                                </tr>`;
                        }
                    }
                }
                if (param.type === "") {
                    $(".mainCon8 .scTab1 tbody").html(html);
                    $(".mainCon8 .scTab1").show().siblings().hide();
                } else {
                    $(".mainCon8 .scTab2 tbody").html(html);
                    $(".mainCon8 .scTab2").show().siblings().hide();
                }
                showMainCon(8);
            }
        });
    }
}

// creator: 张旭博，2017-09-13 15:30:05，插入凭证详情
function getAccountingDetail(detailId, kind) {
    $.ajax({
        url: "../data/getAccountingDetail.do",
        data: {
            detailId: detailId,
            kind: kind
        },
        success: function (data) {
            if(data == ""){
                return false ;
            }
            var content = data["content"];
            var resKind = data["kind"];
            var accountingDetailStr = "";
            if(resKind === 1){
                $("#accountingDetail .detailed").html('<div class="price hd"></div>');
                getReimburseDetail(content.id)
            }
            switch (resKind){
                case 2:
                    if (content.type === "1") {
                        var type = "内部转账支票";
                    } else if (content.type === "2") {
                        var type = "现金支票";
                    }
                    accountingDetailStr     =   '<tr><td>项目</td><td>支出</td></tr>' +
                        '<tr><td>票据种类</td><td>' + content.billCat + '</td></tr>' +
                        '<tr><td>票据所属月份</td><td>' + chargeBillPeriod(content.billPeriod) + '</td></tr>' +
                        '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                        '<tr><td>票据数量</td><td>' + content.billQuantity + '</td></tr>' +
                        '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                        '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                        '<tr><td>经手人</td><td class="operatorName">' + content.financialHandling + '</td></tr>' +
                        '<tr><td>支出方式</td><td>' + type + '</td></tr>' +
                        '<tr><td>银行账户</td><td>' + content.bankName + ' ' + content.account + '</td></tr>' +
                        '<tr><td>支票号</td><td>' + content.chequeNo + '</td></tr>' +
                        '<tr><td>支票到期日</td><td>' + content.expireDate.substring(0,10) + '</td></tr>' +
                        '<tr><td>接收日期</td><td>' + content.receiveDate.substring(0,10) + '</td></tr>' +
                        '<tr><td>接收经手人</td><td>' + content.receiver + '</td></tr>' +
                        '<tr><td>支付经手人</td><td>' + content.operator + '</td></tr>' +
                        '<tr><td>收款单位</td><td>' + content.receiveCorp + '</td></tr>' +
                        '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>';
                    break;
                case 3:
                    $(".source").attr("tittle", 3);
                    if (content.billType === "1") {
                        var billType = "收入";
                    } else if (data.content.billType === "2") {
                        var billType = "支出";
                    }
                    //收入中的转账支票
                    if(content.billType === "1"){
                        accountingDetailStr     =   '<tr><td>项目</td><td>收入</td></tr>' +
                            '<tr><td>类别</td><td>' + chargeGenre(content.category) + '</td></tr>' +
                            '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                            '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                            '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                            '<tr><td>经手人</td><td class="operatorName">' + content.operatorName + '</td></tr>' +
                            '<tr><td>收入方式</td><td>' + C_payway(content.type) + '</td></tr>';
                        if(content.type === 1){
                            accountingDetailStr +=  '<tr><td>收到支票日期</td><td>' + content.receiveDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>付款单位</td><td>' + content.payer + '</td></tr>' +
                                '<tr><td>支票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>支票到期日</td><td>' + content.expireDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>出具支票单位</td><td>' + content.originalCorp + '</td></tr>' +
                                '<tr><td>出具支票银行</td><td>' + content.bankName + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }else if(content.type === 2){
                            accountingDetailStr +=  '<tr><td>收到汇票日期</td><td>' + content.receiveDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>付款单位</td><td>' + content.payer + '</td></tr>' +
                                '<tr><td>汇票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>汇票到期日</td><td>' + content.expireDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>原始出具汇票单位</td><td>' + content.originalCorp + '</td></tr>' +
                                '<tr><td>出具汇票银行</td><td>' + content.bankName + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }
                    }
                    //支出中的转账支票
                    else if(content.billType === "2"){
                        accountingDetailStr     =   '<tr><td>项目</td><td>' + billType + '</td></tr>' +
                            '<tr><td>票据种类</td><td>' + content.billCat + '</td></tr>' +
                            '<tr><td>票据所属月份</td><td>' + chargeBillPeriod(content.billPeriod) + '</td></tr>' +
                            '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                            '<tr><td>票据数量</td><td>' + content.billQuantity + '</td></tr>' +
                            '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                            '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                            '<tr><td>经手人</td><td class="operatorName">' + content.operatorName + '</td></tr>' +
                            '<tr><td>支出方式</td><td>' + C_payway(content.type) + '</td></tr>';
                        if(content.type === 1){
                            accountingDetailStr +=  '<tr><td>支票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>收款单位</td><td>' + content.oppositeCorp + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }else if(content.type === 2){
                            accountingDetailStr +=  '<tr><td>汇票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>收款单位</td><td>' + content.oppositeCorp + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }
                    }
                    break;
                case 4:
                    var method  =   "";             //支出方式
                    var bankStr =   "";             //转账银行
                    if (content.method === "1") {
                        method = "现金";
                    } else if (content.method === "5") {
                        method = "银行转账";
                        bankStr = '<tr><td>转账银行：</td><td>' + content.oppositeAccount + '</td></tr>';
                    }
                    accountingDetailStr     =   '<tr><td>项目   </td><td>支出</td></tr>' +
                        '<tr><td>票据种类</td><td>' + content.billCat + '</td></tr>' +
                        '<tr><td>票据所属月份</td><td>' + chargeBillPeriod(content.billPeriod) + '</td></tr>' +
                        '<tr><td>摘要   </td><td class="summary">' + content.summary + '</td></tr>' +
                        '<tr><td>票据数量</td><td>' + content.billQuantity + '</td></tr>' +
                        '<tr><td>金额   </td><td class="price">' + content.billAmount + '</td></tr>' +
                        '<tr><td>用途   </td><td class="purpose">' + content.purpose + '</td></tr>' +
                        '<tr><td>经手人 </td><td class="operatorName">' + content.operatorName + '</td></tr>' +
                        '<tr><td>支出方式</td><td>' + method + '</td></tr>' + bankStr +
                        '<tr><td>收款单位</td><td>' + content.oppositeCorp + '</td></tr>' +
                        '<tr><td>备注   </td><td class="memo">' + content.memo + '</td></tr>' ;
                    break;
                case 5:
                    $(".source").attr("tittle", 0);
                    var method  =   "";             //收入方式
                    var bankStr =   "";             //收款银行

                    if (content.method === "1") {
                        method  =   "现金";
                        bankStr =   '';
                    } else if (content.method === "5") {
                        method  =   "银行转账";
                        bankStr =   '<tr><td>收款银行</td><td>' + content.accountBank + '</td></tr>' +
                            '<tr><td>到账时间</td><td>' + content.receiveAccountDate.substring(0,10) + '</td></tr>';
                    }

                    accountingDetailStr     =   '<tr><td>项目</td><td>收入</td></tr>' +
                        '<tr><td>类别</td><td>' + chargeGenre(content.genre) + '</td></tr>' +
                        '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                        '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                        '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                        '<tr><td>经手人</td><td class="operatorName">' + content.auditorName + '</td></tr>' +
                        '<tr><td>收入方式</td><td>' + method + '</td></tr>' +
                        '<tr><td>付款单位</td><td>' + content.oppositeCorp + '</td></tr>' + bankStr +
                        '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>';
                    break;
                default:
                    accountingDetailStr     =   '';
            }
            $("#accountingDetail .detailed").html('<table class="ty-table"><body>' + accountingDetailStr + '</body></table>');
            if (resKind == 1) {
                bounce_Fixed.show($("#reimburse"))
            } else {
                bounce_Fixed.show($("#accountingDetail"))
            }
        }
    });
}

// creator: 张旭博，2019-05-24 09:28:12，获取报销详情
function getReimburseDetail(reimburseId) {
    $.ajax({
        url:"../reimburseWindow/getReimburseInfo.do" ,
        data: {reimburseId : reimburseId },
        success:function (data) {
            var personnelReimburse = data.personnelReimburse
            var billList = data.billList

            var billStr = ''


            $("#reimburse .summary").html(personnelReimburse.summary)
            $("#reimburse .purpose").html(personnelReimburse.purpose)
            $("#accountingDetail .price").html(personnelReimburse.amount)

            var totalQuantity = 0
            var totalBillAmount = 0
            var totalAmount = 0
            for (var j in billList) {
                var path = billList[j].pictures && billList[j].pictures.length > 0? billList[j].pictures[0].path: "";
                billStr +=  '<tr id="'+billList[j].reimburseBillId+'">' +
                    '    <td>' + billList[j].billCatName + '</td>' +
                    '    <td>' + (billList[j].billCatName === '定额普通发票'? '--' : billList[j].billAmount) + '</td>' +
                    '    <td>' + billList[j].num + '</td>' +
                    '    <td>' + billList[j].billAmount + '</td>' +
                    '    <td>' + billList[j].amount + '</td>' +
                    '    <td>' +
                    '        <span class="ty-color-blue" data-name="billInfo" type="scan_btn">票据内容</span>' +
                    '        <span class="ty-color-blue" data-name="billPic" data-path="' + path + '" type="scan_btn">票据图片</span>' +
                    '    </td>' +
                    '</tr>'
                totalQuantity = -(-totalQuantity - billList[j].num)
                totalBillAmount = -(-totalBillAmount - billList[j].billAmount)
                totalAmount = -(-totalAmount - billList[j].amount)
            }
            if (billList.length > 1) {
                billStr +=  '<tr>' +
                    '    <td></td>' +
                    '    <td></td>' +
                    '    <td>' + totalQuantity + '</td>' +
                    '    <td>' + totalBillAmount.toFixed(2) + '</td>' +
                    '    <td>' + totalAmount.toFixed(2) + '</td>' +
                    '    <td></td>' +
                    '</tr>'
            }

            $("#reimburse .billDetail tbody").html(billStr)
        }
    })
}
// 判别 费用类别
function chargeGenre( val ){
    switch( Number(val) ){
        case 1 : return "贷款" ; break ;
        case 2 : return "借款" ; break ;
        case 3 : return "投资款" ; break ;
        case 4 : return "废品" ; break ;
        case 5 : return "其他" ; break ;
        default : return "";
    }
}
