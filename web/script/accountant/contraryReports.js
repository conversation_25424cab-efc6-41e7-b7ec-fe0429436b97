
/**
 * Created by Administrator on 2017/5/22.
 */
$(function(){

    //初始化所有表固定信息
    initAllThead();

    //一开始调的接口
    judgementState();

    //标签切换触发的事件（切换标签样式和内容、设置默认选中）
    $(".ty-secondTab li").on("click",function () {
        $(".ty-secondTab li").removeClass("ty-active");
        $(this).addClass("ty-active");
        $(".tblContainer").addClass("hd")
        $(".tblContainer").eq($(this).index()).removeClass("hd");
        var index  =$(this).index();
        switch (index){
            case 0:
                //获取试算平衡表表格
                getTrialBalanceSheet();
                break;
            case 1:
                //获取科目余额表表格
                getAccountBalanceSheet();
                break;
            case 2:
                //获取利润表表格
                getIncomeSheet();
                break;
            case 3:
                //获取资产负债表表格
                getBalanceSheet();
                break;
            case 4:
                //获取现金流量表表格
                getCashFlowSheet();
                break;
            default:
                alert("错误的标签页！");
        }
    });

});
/* creator :侯杏哲 2018-05-14 报税完毕按钮 */
function tallageBtn() {
    bounce.show($("#tallage")) ; $("#tallageDate").click(); $("#laydate_box").hide() ;
}
function showTB(){
    $("#laydate_table").children().show() ;$("#laydate_box").toggle() ;
}
/* creator :侯杏哲 2018-05-14 报税 */
function tallage() {
    var taxDate = $("#tallageDate").val() ;
    if($.trim(taxDate) == ""){
        layer.msg("请输入报税时间") ; return false ;
    }
    $("#laydate_table").children().hide() ;
    $.ajax({
        "url" :"../accountant/clickTaxBtn.do" ,
        "data" : { "taxDate" : taxDate  } ,
        success:function (res) {
            var code = res["code"] ;
            if(code == 1){
                bounce.cancel() ; layer.msg("录入成功！")
                judgementState();
                showMainCon(1);
                getMainData();
            }else{
                var msg = res["msg"] ;
                bounce.show($("#tip")) ; $("#tipMs").html(msg) ;
            }
        }
    })
}

/* creator：张旭博，2017-06-08 09:48:36，初始化后三张表固定信息 */
function initAllThead() {
    //----------利润表 BalanceSheet----------

    //项目
    var incomeProjectStr            = [{"item":"一、营业收入","level":"0"},{"item":"减：营业成本","level":"0"},{"item":"营业税金及附加","level":"1"},{"item":"其中：消费税","level":"2"},{"item":"营业税","level":"3"},{"item":"城市维护建设税","level":"3"},{"item":"资源税","level":"3"},{"item":"土地增值税","level":"3"},{"item":"城镇土地使用税、房产税、车船税、印花税","level":"3"},{"item":"教育费附加、矿产资源补偿费、排污费","level":"3"},{"item":"销售费用","level":"1"},{"item":"其中：商品维修费","level":"2"},{"item":"广告费和业务宣传费","level":"3"},{"item":"管理费用","level":"1"},{"item":"其中：开办费","level":"2"},{"item":"业务招待费","level":"3"},{"item":"研究费用","level":"3"},{"item":"财务费用","level":"1"},{"item":"其中：利息费用（收入以“-”号填列）","level":"2"},{"item":"加：投资收益（损失以“-”号填列）","level":"0"},{"item":"二、营业利润（亏损以“-”号填列）","level":"0"},{"item":"加：营业外收入","level":"0"},{"item":"其中：政府补助","level":"1"},{"item":"减：营业外支出","level":"0"},{"item":"其中：坏账损失","level":"1"},{"item":"无法收回的长期债券投资损失","level":"2"},{"item":"无法收回的长期股权投资损失","level":"2"},{"item":"自然灾害等不可抗力因素造成的损失","level":"2"},{"item":"税收滞纳金","level":"2"},{"item":"三、利润总额（亏损以“-”号填列）","level":"0"},{"item":"减：所得税费用","level":"0"},{"item":"四、净利润（净亏损以“-”号填列）","level":"0"}];
    //行次
    var incomeProjectNumStr         = ["1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32"];
    var incomeSheetStr = "" ;
    for(var j=0;j<incomeProjectNumStr.length;j++){
        incomeSheetStr +=       '<tr>'+
            '<td><span level="'+incomeProjectStr[j].level+'">'+incomeProjectStr[j].item+'</span></td>'+
            '<td>'+incomeProjectNumStr[j]+'</td>'+
            '<td></td>'+
            '<td></td>'+
            '</tr>';

    }
    $(".tblContainer table>tbody").eq(2).html(incomeSheetStr);

    //----------资产负债表 BalanceSheet----------

    //资产
    var assetStr            = [{"item":"流动资产：","level":"0"},{"item":"货币资金","level":"1"},{"item":"短期投资","level":"1"},{"item":"应收票据","level":"1"},{"item":"应收账款","level":"1"},{"item":"预付账款","level":"1"},{"item":"应收股利","level":"1"},{"item":"应收利息","level":"1"},{"item":"其他应收款","level":"1"},{"item":"存货","level":"1"},{"item":"其中：原材料","level":"2"},{"item":"在产品","level":"3"},{"item":"库存商品","level":"3"},{"item":"周转材料","level":"3"},{"item":"其他流动资产","level":"1"},{"item":"流动资产合计","level":"2"},{"item":"非流动资产：","level":"1"},{"item":"长期债券投资","level":"1"},{"item":"长期股权投资","level":"1"},{"item":"固定资产原价","level":"1"},{"item":"减：累计折旧","level":"1"},{"item":"固定资产账面价值","level":"1"},{"item":"在建工程","level":"1"},{"item":"工程物资","level":"1"},{"item":"固定资产清理","level":"1"},{"item":"生产性生物资产","level":"1"},{"item":"无形资产","level":"1"},{"item":"开发支出","level":"1"},{"item":"长期待摊费用","level":"1"},{"item":"其他非流动资产","level":"1"},{"item":"非流动资产合计","level":"2"},{"item":"资产总计","level":"3"}];

    //行次
    var assetNumStr         = ["","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30"];

    //负债
    var liabilitieStr       = [{"item":"流动负债：","level":"0"},{"item":"短期借款","level":"1"},{"item":"应付票据","level":"1"},{"item":"应付账款","level":"1"},{"item":"预收账款","level":"1"},{"item":"应付职工薪酬","level":"1"},{"item":"应交税费","level":"1"},{"item":"应付利息","level":"1"},{"item":"应付利润","level":"1"},{"item":"其他应付款","level":"1"},{"item":"其他流动负债","level":"1"},{"item":"流动负债合计","level":"2"},{"item":"非流动负债：","level":"0"},{"item":"长期借款","level":"1"},{"item":"长期应付款","level":"1"},{"item":"递延收益","level":"1"},{"item":"其他非流动负债","level":"1"},{"item":"非流动负债合计","level":"2"},{"item":"负债合计","level":"3"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"","level":"0"},{"item":"所有者权益（或股东权益）：","level":"0"},{"item":"实收资本（或股本）","level":"1"},{"item":"资本公积","level":"1"},{"item":"盈余公积","level":"1"},{"item":"未分配利润","level":"1"},{"item":"所有者权益（或股东权益）合计","level":"2"},{"item":"负债和所有者权益（或股东权益）总计","level":"0"}];

    //行次
    var liabilitieNumStr    = ["","31","32","33","34","35","36","37","38","39","40","41","","42","43","44","45","46","47","","","","","","","","48","49","50","51","52","53"];

    var BalanceSheetStr     = "" ;
    for(var i=0;i<assetNumStr.length;i++){
        BalanceSheetStr +=      '<tr>'+
            '<td><span level="'+assetStr[i].level+'">'+assetStr[i].item+'</span></td>'+
            '<td>'+assetNumStr[i]+'</td>'+
            '<td></td>'+
            '<td></td>'+
            '<td><span level="'+liabilitieStr[i].level+'">'+liabilitieStr[i].item+'</span></td>'+
            '<td>'+liabilitieNumStr[i]+'</td>'+
            '<td></td>'+
            '<td></td>'+
            '</tr>';

    }
    $(".tblContainer table>tbody").eq(3).html(BalanceSheetStr);



    //----------现金流量表 cashFlowSheet----------

    var cashFlowStr      = [{"item":"一、经营活动产生的现金流量：","level":"0"},{"item":"销售商品、提供劳务收到的现金","level":"1"},{"item":"收到的税费返还","level":"1"},{"item":"收到其他与经营活动有关的现金","level":"1"},{"item":"经营活动现金流入小计","level":"1"},{"item":"购买商品、接受劳务支付的现金","level":"1"},{"item":"支付给职工以及为职工支付的现金","level":"1"},{"item":"支付的各项税费","level":"1"},{"item":"支付其他与经营活动有关的现金","level":"1"},{"item":"经营活动现金流出小计","level":"2"},{"item":"经营活动产生的现金流量净额","level":"2"},{"item":"二、投资活动产生的现金流量：","level":"0"},{"item":"收回投资收到的现金","level":"1"},{"item":"取得投资收益收到的现金","level":"1"},{"item":"处置固定资产、无形资产和其他长期资产收回的现金净额","level":"1"},{"item":"处置子公司及其他营业单位收到的现金净额","level":"1"},{"item":"收到其他与投资活动有关的现金","level":"1"},{"item":"投资活动现金流入小计","level":"2"},{"item":"购建固定资产、无形资产和其他长期资产支付的现金","level":"1"},{"item":"投资支付的现金","level":"1"},{"item":"取得子公司及其他营业单位支付的现金净额","level":"1"},{"item":"支付其他与投资活动有关的现金","level":"1"},{"item":"投资活动现金流出小计","level":"2"},{"item":"投资活动产生的现金流量净额","level":"2"},{"item":"三、筹资活动产生的现金流量：","level":"0"},{"item":"吸收投资收到的现金","level":"1"},{"item":"其中：子公司吸收少数股东投资收到的现金","level":"1"},{"item":"取得借款收到的现金","level":"1"},{"item":"收到其他与筹资活动有关的现金","level":"1"},{"item":"筹资活动现金流入小计","level":"2"},{"item":"偿还债务支付的现金","level":"1"},{"item":"分配股利、利润或偿付利息支付的现金","level":"1"},{"item":"其中：子公司支付给少数股东的股利、利润","level":"1"},{"item":"支付其他与筹资活动有关的现金","level":"1"},{"item":"筹资活动现金流出小计","level":"2"},{"item":"筹资活动产生的现金流量净额","level":"2"},{"item":"四、汇率变动对现金及现金等价物的影响","level":"0"},{"item":"五、现金及现金等价物净增加额","level":"0"},{"item":"加：期初现金及现金等价物的余额","level":"1"},{"item":"六、期末现金及现金等价物余额","level":"0"}];
    //项目
    var cashFlowNumStr   = ["","1","2","3","4","5","6","7","8","9","10","","11","12","13","14","15","16","17","18","19","20","21","22","","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37"];
    //行次
    var cashFlowSheetStr = "";
    for(var c=0;c<cashFlowNumStr.length;c++){
        cashFlowSheetStr +=       '<tr>'+
            '<td><span level="'+cashFlowStr[c].level+'">'+cashFlowStr[c].item+'</span></td>'+
            '<td>'+cashFlowNumStr[c]+'</td>'+
            '<td></td>'+
            '<td></td>'+
            '</tr>';
    }
    $(".tblContainer table>tbody").eq(4).html(cashFlowSheetStr);

}

/* creator：张旭博，2017-06-26 16:00:20，判断转状态 */
/*function judgementState(){
    $.ajax({
        url:"../accountant/clickReportForm.do" ,
        success:function(data){
            var period = data["period"];
            var status = data["status"];
            var isDisplayTaxBtn = data["isDisplayTaxBtn"]; // 标识是否显示报税按钮 ：1-可以显示报税按钮         0-不显示
            if(isDisplayTaxBtn == 1){
                $("#tallageBtn").attr("onclick" , "tallageBtn()").attr("class" , "ty-btn ty-btn-blue ty-btn-big ty-circle-5") ;
            }
            if(period == ""||period == undefined){
                $("#errorTip .tipWord").html("未返回正确时间！");
                bounce.show($("#errorTip"));
            }else{
                if($("#searchDate").val() == ""){
                    period = period.split("-")[0]+"年"+period.split("-")[1]+"月";
                    $("#searchDate").val(period);
                }
            }
            switch(Number(status)){
                case 0:
                    $("#trialBtn").prop("disabled",false);
                    $("#lossAndGainBroughtForwardBtn").prop("disabled",true);
                    $("#partCheckOutBtn").hide();
                    $("#counterCheckBtn").prop("disabled",true);
                    $("#counterCheckBtn").show();

                    break;
                case 1:
                    $("#trialBtn").prop("disabled",false);
                    $("#lossAndGainBroughtForwardBtn").prop("disabled",false);
                    $("#partCheckOutBtn").prop("disabled",false);
                    $("#partCheckOutBtn").show();
                    $("#counterCheckBtn").hide();
                    break;
                case 2:
                    $("#trialBtn").prop("disabled",true);
                    $("#lossAndGainBroughtForwardBtn").prop("disabled",true);
                    $("#partCheckOutBtn").hide();
                    $("#counterCheckBtn").prop("disabled",false);
                    $("#counterCheckBtn").show();
                    break;
                case 3:
                    $("#trialBtn").prop("disabled",true);
                    $("#lossAndGainBroughtForwardBtn").prop("disabled",true);
                    $("#partCheckOutBtn").prop("disabled",true);
                    $("#partCheckOutBtn").show();
                    $("#counterCheckBtn").hide();
                    break;
                default:
                    $("#errorTip .tipWord").html("错误的状态！");
                    bounce.show($("#errorTip"));
            }
            $("#handleBtn").show();
            var $active = $(".ty-secondTab li.ty-active");
            if($active == undefined){
                $(".ty-secondTab li").eq(0).click();
            }else{
                $active.click();
            }

            //默认选中第一张表

        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
        },
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })
}*/

//获取试算平衡表信息 TrialBalance
function getTrialBalanceSheet() {
    //获取日期
    //var nowDate = $("#searchDate").val();
    var period = $("#reportDateSelect").val();    //来自结账管理页面
    /*if(nowDate != null && nowDate != ""){
            //格式化时间
            period = (nowDate.substring(0,4)).toString() +"-"+ (nowDate.substring(5,7)).toString();
        }else{
            period = null;
        }*/
    $.ajax({
        url:"getTrialBalanceData.do" ,
        data:{ "period": period } ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["code"] == 0){
                $("#tip #tipMs").html(data["msg"]);
                bounce.show($("#tip"));
            }else if(data["code"] == 1){
                var list = data["data"];
                var trialBalanceStr =  "";
                if(list.length>0){
                    for(var i =0 ;i<list.length;i++){
                        trialBalanceStr += '<tr>'+
                            '<td>'+list[i].subject+'</td>'+
                            '<td>'+list[i].subName+'</td>'+
                            '<td>'+changeRules(list[i].previousDirection)+'</td>'+//期初余额方向
                            '<td>'+formatMoney(delNull(list[i].previousBalance))+'</td>'+//期初余额
                            '<td>'+formatMoney(delNull(list[i].credit))+'</td>'+//本期发生借
                            '<td>'+formatMoney(delNull(list[i].debit))+'</td>'+//本期发生贷
                            '<td>'+changeRules(list[i].balanceDirection)+'</td>'+//期末余额方向
                            '<td>'+formatMoney(delNull(list[i].balance))+'</td>'+//期末余额
                            '</tr>';
                    }
                    //加入合计字符串
                    trialBalanceStr +=  '<tr>' +
                        '<td colspan="2" rowspan="2">合计</td>'+
                        '<td>借</td>'+
                        '<td>'+formatMoney(data["sumPreviousBalanceCredit"])+'</td>'+
                        '<td rowspan="2">'+formatMoney(data["sumCredit"])+'</td>'+
                        '<td rowspan="2">'+formatMoney(data["sumDebit"])+'</td>'+
                        '<td>借</td>'+
                        '<td>'+formatMoney(data["sumBalanceCredit"])+'</td>'+
                        '</tr>'+
                        '<tr>' +
                        '<td>贷</td>'+
                        '<td>'+formatMoney(data["sumPreviousBalanceDebit"])+'</td>'+
                        '<td>贷</td>'+
                        '<td>'+formatMoney(data["sumBalanceDebit"])+'</td>'+
                        '</tr>';
                    $(".tblContainer").eq(0).find("tbody").html(trialBalanceStr);
                }else{
                    $(".tblContainer").eq(0).find("tbody").html("");
                }


            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })

}

//获取科目余额表信息 AccountBalance
function getAccountBalanceSheet() {
    //var nowDate = $("#searchDate").val();
    var period = $("#reportDateSelect").val();    //来自结账管理页面
    /*if(nowDate != null && nowDate != ""){
            //格式化时间
            period = (nowDate.substring(0,4)).toString() +"-"+ (nowDate.substring(5,7)).toString();
        }else{
            period = null;
        }*/
    $.ajax({
        url:"getSubjectBalanceSheet.do" ,
        data:{ "period": period } ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["code"] == 0){
                $("#tip #tipMs").html(data["msg"]);
                bounce.show($("#tip"));
            }else if(data["code"] == 1){
                var list = data["data"];
                var accountBalanceStr =  "";
                if(list.length>0){
                    for(var i =0 ;i<list.length;i++){
                        accountBalanceStr += '<tr>'+
                            '<td>'+list[i].subject+'</td>'+
                            '<td>'+list[i].subName+'</td>'+
                            '<td>'+changeRules(list[i].beginningDirection)+'</td>'+//年初余额方向
                            '<td>'+formatMoney(delNull(list[i].beginningBalance))+'</td>'+//年初余额
                            '<td>'+changeRules(list[i].previousDirection)+'</td>'+//期初余额方向
                            '<td>'+formatMoney(delNull(list[i].previousBalance))+'</td>'+//期初余额
                            '<td>'+formatMoney(delNull(list[i].credit))+'</td>'+//本期发生借
                            '<td>'+formatMoney(delNull(list[i].debit))+'</td>'+//本期发生贷
                            '<td>'+formatMoney(delNull(list[i].creditAccumulative))+'</td>'+//本年累计借
                            '<td>'+formatMoney(delNull(list[i].debitAccumulative))+'</td>'+//本年累计贷
                            '<td>'+changeRules(list[i].balanceDirection)+'</td>'+//期末余额方向
                            '<td>'+formatMoney(delNull(list[i].balance))+'</td>'+//期末余额
                            '</tr>';
                    }
                    //加入合计字符串
                    accountBalanceStr +=    '<tr>' +
                                                '<td colspan="2" rowspan="2">合计</td>'+
                                                '<td>借</td>'+
                                                '<td>'+formatMoney(data["sumBeginningBalanceCredit"])+'</td>'+
                                                '<td>借</td>'+
                                                '<td>'+formatMoney(data["sumPreviousBalanceCredit"])+'</td>'+
                                                '<td rowspan="2">'+formatMoney(data["sumCredit"])+'</td>'+
                                                '<td rowspan="2">'+formatMoney(data["sumDebit"])+'</td>'+
                                                '<td rowspan="2">'+formatMoney(data["sumAccumulativeCredit"])+'</td>'+
                                                '<td rowspan="2">'+formatMoney(data["sumAccumulativeDebit"])+'</td>'+
                                                '<td>借</td>'+
                                                '<td>'+formatMoney(data["sumBalanceCredit"])+'</td>'+
                                            '</tr>'+
                                            '<tr>' +
                                                '<td>贷</td>'+
                                                '<td>'+formatMoney(data["sumBeginningBalanceDebit"])+'</td>'+
                                                '<td>贷</td>'+
                                                '<td>'+formatMoney(data["sumPreviousBalanceDebit"])+'</td>'+
                                                '<td>贷</td>'+
                                                '<td>'+formatMoney(data["sumBalanceDebit"])+'</td>'+
                                            '</tr>';
                    $(".tblContainer table>tbody").eq(1).html(accountBalanceStr);
                }else{
                    $(".tblContainer").eq(1).find("tbody").html("");
                }

            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误请重试！");
            bounce.show($("#errorTip"));
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })

}

//获取利润表信息
function getIncomeSheet() {
    //获取日期
    //var nowDate = $("#searchDate").val();
    //资产负债和利润表共用一个借口，使用code区分
    var code = 2;
    var period = $("#reportDateSelect").val();    //来自结账管理页面
    /*if(nowDate != null && nowDate != ""){
            //格式化时间
            period = (nowDate.substring(0,4)).toString() +"-"+ (nowDate.substring(5,7)).toString();
        }else{
            period = null;
        }*/
    $.ajax({
        url:"getSublectBalance.do" ,
        data:{ "nowDate": period , "code":code} ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["status"] === 0){
                $("#errorTip .tipWord").html("本月未结账！");
                bounce.show($("#errorTip"));
            }else if(data["status"] === 1){
                var list = data["list"];
                if(list.length>0){
                    for(var i = 0; i<list.length ;i++){
                        var cell = list[i].cellCode;
                        var amount = list[i].amount;    //本月金额
                        var accumulative = list[i].accumulative;    //本年累计金额
                        var row = cell.split("c")[0].split("r")[1];

                        $(".tblContainer").eq(2).find("tbody tr").eq(row-1).children("td").eq(2).html(formatMoney(accumulative));
                        $(".tblContainer").eq(2).find("tbody tr").eq(row-1).children("td").eq(3).html(formatMoney(amount));


                    }
                }else{
                    $(".tblContainer").eq(2).find("tbody tr").each(function () {
                        $(this).children("td").eq(3).html("");
                    });
                }

            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })

}

//获取资产负债表信息
function getBalanceSheet() {
    //获取日期
    //var nowDate = $("#searchDate").val();
//资产负债和利润表共用一个借口，使用code区分
    var code = 1;
    var period = $("#reportDateSelect").val();    //来自结账管理页面
    /*if(nowDate != null && nowDate != ""){
           //格式化时间
           period = (nowDate.substring(0,4)).toString() +"-"+  (nowDate.substring(5,7)).toString();
       }else{
           period = null;
       }*/
    $.ajax({
        url:"getSublectBalance.do" ,
        data:{ "nowDate": period , "code":code} ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["status"] == 0){
                $("#errorTip .tipWord").html("本月未结账！");
                bounce.show($("#errorTip"));
            }else if(data["status"] == 1){
                var list = data["list"];
                if(list.length>0){
                    for(var i = 0; i<list.length ;i++){
                        var cell = list[i].cellCode;
                        var balance = list[i].balance;
                        var beginningBalance = list[i].beginningBalance;
                        var row = cell.split("c")[0].split("r")[1];
                        var col = cell.split("c")[1];
                        $(".tblContainer").eq(3).find("tbody tr").eq(row-1).children("td").eq(col-1).html(formatMoney(balance));
                        $(".tblContainer").eq(3).find("tbody tr").eq(row-1).children("td").eq(col).html(formatMoney(beginningBalance));
                    }
                    //以下四个为固定值
                    $(".tblContainer").eq(3).find("tbody tr").eq(10).children("td").eq(6).html(formatMoney(0.00));
                    $(".tblContainer").eq(3).find("tbody tr").eq(16).children("td").eq(6).html(formatMoney(0.00));
                    $(".tblContainer").eq(3).find("tbody tr").eq(10).children("td").eq(7).html(formatMoney(0.00));
                    $(".tblContainer").eq(3).find("tbody tr").eq(16).children("td").eq(7).html(formatMoney(0.00));
                }else{
                    $(".tblContainer").eq(3).find("tbody tr").each(function () {
                        $(this).children("td").eq(2).html("");
                        $(this).children("td").eq(6).html("");
                    });
                }
            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })

}

//现金流量表 cashFlowSheet
function getCashFlowSheet(){
    //获取日期
    //var nowDate = $("#searchDate").val();
    var period = $("#reportDateSelect").val();    //来自结账管理页面
    /*if(nowDate != null && nowDate != ""){
        //格式化时间
        period = (nowDate.substring(0,4)).toString() +"-"+ (nowDate.substring(5,7)).toString();
    }else{
        period = null;
    }*/
    $.ajax({
        url:"getCashFlowData.do" ,
        data:{ "period": period } ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["code"] == 0){
                $("#tip #tipMs").html(data["msg"]);
                bounce.show($("#tip"));
            }else if(data["code"] == 1){
                var list = data["data"];
                if(list.length>0){
                    for(var i = 0; i<list.length ;i++){
                        var cell = list[i].cellCode;
                        var accumulative = delNull(list[i].accumulative);
                        var amount = delNull(list[i].amount);
                        var row = cell.split("c")[0].split("r")[1];
                        var col = cell.split("c")[1];
                        if(col === "7"){col = 4}

                        $(".tblContainer").eq(4).find("tbody tr").eq(row-1).children("td").eq(col-2).html(formatMoney(accumulative));
                        $(".tblContainer").eq(4).find("tbody tr").eq(row-1).children("td").eq(col-1).html(formatMoney(amount));
                    }
                    // $(".tblContainer").eq(4).find("tbody tr").eq(33-1).children("td").eq(4-2).html(formatMoney(delNull(list[33].accumulative)));
                }else{
                    $(".tblContainer").eq(4).find("tbody tr").each(function () {
                        $(this).children("td").eq(2).html("");
                        $(this).children("td").eq(3).html("");
                    });
                }

            }else{
                $("#errorTip .tipWord").html("无效的返回值！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误，请重试！");
            bounce.show($("#errorTip"));
        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    })
}

/* creator：张旭博，2017-07-31 09:23:17，试算-按钮 */
function trial() {

    $("#reportDateSelect").val($("#settleMonth").data("month"));//来自凭证管理页面
    $.ajax({
        url:"../accountant/tentativeConculation.do" ,
        data:{} ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open(); },
        success:function(data){
            var status = data["status"];
            if(status == 1){
                bounce.cancel();
                $("#tip #tipMs").html("试算成功！<p>报税后请勿忘记点击在本系统中“记账”与“结账”！！\n</p>");
                bounce.show($("#tip"));
                judgementState();
                //$(".ty-secondTab .ty-active").click();
            }else{
                $("#errorTip .tipWord").html("未返回正确的试算状态！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误，请重试！");
            bounce.show($("#errorTip"));
        },
        complete:function () {
            loading.close();
        }
    });

}

/* creator：张旭博，2017-07-31 09:47:25，结账-按钮 */
function partCheckoutBtn() {
   /* if(!accountantAuth.checkout){
        $("#errorTip .tipWord").html("您没有结账此权限！");
        bounce.show($("#errorTip"));
        return false;
    }*/
    $("#checkout").data("state", 2)
    bounce.show($("#checkout"));
}

/* creator：张旭博，2017-07-31 09:47:40，结账-确定 */
/*function sureCheckout() {
    //state 状态2是小结账 3是总结账
    var data = {
        "state":2
    };
    $.ajax({
        url:"../accountant/closeAccounts.do" ,
        data:data ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open(); },
        success:function(data){
            //"status": 此时状态，“2”代表小结账成功，“3”代表总结账成功。
            var status = data["status"];
            var flag = data["flag"];
            if(flag === 0){
                $("#errorTip .tipWord").html("请先试算再结账！");
                bounce.show($("#errorTip"));
            }else{
                if(status === "2"){
                    bounce.cancel();
                    $("#tip #tipMs").html("结账成功！<p>记账后请勿忘记在本系统中“结账”！！</p>");
                    bounce.show($("#tip"));
                    $("#trialBtn").prop("disabled",true);
                    $("#lossAndGainBroughtForwardBtn").prop("disabled",true);
                    $("#checkOutBtn").hide();
                    $("#counterCheckBtn").prop("disabled",false);
                    $("#counterCheckBtn").show();
                    $(".ty-secondTab .ty-active").click();
                }else{
                    $("#errorTip .tipWord").html("未成功结账！");
                    bounce.show($("#errorTip"));
                }
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误，请重试！");
            bounce.show($("#errorTip"));
        } ,
        complete:function () {  loading.close();  }
    });
}*/

/* creator：张旭博，2017-07-31 09:47:56，结转损益-按钮 */
function lossAndGainBroughtForwardBtn(){
    /*if(!accountantAuth.lossAndGainBroughtForward){
        $("#errorTip .tipWord").html("您没有结转损益权限！");
        bounce.show($("#errorTip"));
        return false;
    }*/
    $("#lossAndGainBroughtForward .summary").val("结转本期损益");
    bounce.show($("#lossAndGainBroughtForward"));
}

/* creator：张旭博，2017-07-31 09:47:56，结转损益-确定(生成凭证) */
function sureLossAndGainBroughtForward(){
    bounce.cancel();
    $.ajax({
        url:"/accountant/carryOverAccount.do" ,
        data:{},
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open(); },
        success:function(data){
            //"status": “0”代表清空了什么操作都没有
            var status = data["status"];
            if(status === 0){
                $("#errorTip .tipWord").html("结转失败！");
                bounce.show($("#errorTip"));
            }else if(status === 1){
                $("#tip #tipMs").html("结转成功！");
                bounce.show($("#tip"));
                judgementState();
            }else{
                $("#errorTip .tipWord").html("未知的返回值！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
        } ,
        complete:function(){ loading.close(); }
    });
}

/* creator：张旭博，2017-07-31 09:48:58，反结账-按钮 */
function counterCheckBtn() {
    /*if(!accountantAuth.counterCheck){
        $("#errorTip .tipWord").html("您没有反结账权限！");
        bounce.show($("#errorTip"));
        return false;
    }*/
    $.ajax({
        url:"/accountant/claseAccountsBack.do" ,
        data:{},
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open(); },
        success:function(data){
            //"status": “0”代表清空了什么操作都没有
            var status = data["status"];
            if(status == 0){
                bounce.cancel();
                $("#tip #tipMs").html("反结账成功！");
                bounce.show($("#tip"));
                judgementState();
                //$(".ty-secondTab .ty-active").click();
            }else{
                $("#errorTip .tipWord").html("未成功反结账！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
        },
        complete:function(){ loading.close(); }
    });
}

/* creator：张旭博，2017-07-31 09:49:08，反结账-确定 */
function sureCounterCheck(){
    $.ajax({
        url:"/accountant/claseAccountsBack.do" ,
        data:{},
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open(); },
        success:function(data){
            //"status": “0”代表清空了什么操作都没有
            var status = data["status"];
            if(status == 0){
                bounce.cancel();
                $("#tip #tipMs").html("反结账成功！");
                bounce.show($("#tip"));
                $("#trialBtn").prop("disabled",false);
                $("#lossAndGainBroughtForwardBtn").prop("disabled",true);
                $("#partCheckOutBtn").hide();
                $("#counterCheckBtn").prop("disabled",true);
                $("#counterCheckBtn").show();
                //$(".ty-secondTab .ty-active").click();
            }else{
                $("#errorTip .tipWord").html("未成功反结账！");
                bounce.show($("#errorTip"));
            }
        },
        error:function () {
            $("#errorTip .tipWord").html("系统错误,请重试！");
            bounce.show($("#errorTip"));
        } ,
        complete:function(){ loading.close(); }
    });

}

//----------辅助方法----------
//获得方向 1：借、2：贷、3：平
function changeRules(val) {
    if(val == null){
        val = 3
    }
    val = parseInt(val);
    switch(val){
        case 1:
            return "借";
            break;
        case 2:
            return "贷";
            break;
        case 3:
            return "平";
            break;
        default:
            return " ";
    }
}

//去null操作
function delNull(val){
    return null == val ? 0: val;
}

// 设置金额为默认格式（比如199,231,000.00)
function formatMoney(number, places, symbol, thousand, decimal) {
    number = number || 0;
    places = !isNaN(places = Math.abs(places)) ? places : 2;
    symbol = symbol !== undefined ? symbol : "";
    thousand = thousand || ",";
    decimal = decimal || ".";
    var negative = number < 0 ? "-" : "",
        i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
        j = (j = i.length) > 3 ? j % 3 : 0;
    return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
}

//绑定时间插件
laydate.render({
    elem: '#searchDate',
    type: 'month',
    format: 'yyyy年MM月',
    done: function(value){
        if(value!== ''){
            judgementState(value)
        }
    }
}) ;
laydate.render({elem: "#tallageDate"});


