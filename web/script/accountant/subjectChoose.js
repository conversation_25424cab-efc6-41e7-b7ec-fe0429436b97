/* creator：孟闯闯，2017-02-17 08:47:12，实现科目选择页面的功能 */
/* updater：孟闯闯，2017-02-24 12:00:00，修改默认显示的数据 */
/* creator：侯杏哲，2017-01-13 12:00:00，构建科目选择树 */
/* updater：张旭博，2017-05-26 12:00:00，修改科目选择树 */
// updater: 张旭博，2019-06-20 11:03:27，1.59科目关联会计部分改动(重写代码)
var specialIndex = 0;
// creator: 张旭博，2019-07-15 09:32:19，初始化三级和四级弹窗
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#billInfo"));
bounce_Fixed2.cancel();
$(function () {
    // 保存现金流量option
    getCashFlowOption();
    // 一级菜单点击事件
    $(".ty-firstTab li").on('click', function () {
        $(this).addClass('ty-active').siblings().removeClass('ty-active')
        var to = $(this).attr('to')
        $('#' + to).show().siblings().hide()
        $('#' + to).find('li').eq(0).click()
    })
    $(".ty-secondTab li").on('click', function () {
        $(".ty-secondTab li").removeClass('ty-active')
        $(this).addClass('ty-active')
        // 获取主列表
        getMainList()

        var firstName = $(".ty-firstTab li.ty-active").html()
        var secondName = $(this).html()
        $("#firstTag").html(firstName)
        $("#secondTag").html(secondName)
    })
    //点击-新增借方
    $("#choose_addAccount,#pend_addAccount").on("click",".newBorrow",function () {
        var borrowStr =     '<div class="borrowItem">'+
            '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="" class="subjectBorrow" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</span>'+
            '           <span class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>';
        $(this).parents(".borrowItem").after(borrowStr);
        //判断是不是第一组借方（第一组新增完之后第一组没有按钮，非第一组有取消按钮）
        if(!$(this).parents(".borrowItem").hasClass("fixed")){
            $(this).parent(".handle").html('<button class="ty-btn ty-btn-red ty-circle-3 cancelBorrow">取消借方</button>');
        }else{
            $(this).parent(".handle").html("");
        }

    });
    //取消借方的操作
    $("#choose_addAccount,#pend_addAccount").on("click",".cancelBorrow",function () {
        //如果此取消按钮前面同级有新增按钮，取消后在他的前一个借方加入新增借方按钮
        if($(this).prev().hasClass("newBorrow")){
            $(this).parents(".borrowItem").prev().find(".handle").prepend('<button class="ty-btn ty-btn-green ty-circle-3 newBorrow">新增借方</button> ');
        }
        //指向此借方
        var subjectItem= $(this).parents(".borrowItem");
        //获取借方号
        var subjectNo= subjectItem.find(".bookIcon").next("input").attr("id").substring(0,4);
        //如果删除的这个贷方为1001或1002，则对应删除一个现金流量科目
        if(subjectNo === "1001" || subjectNo === "1002"){
            var specialId = subjectItem.attr("data-to");
            subjectInputObj.parents(".borrowMoreLoans").next(".special").find("."+specialId).remove();
        }
        //删除此借方
        subjectItem.remove();
    });
    //点击-新增贷方 （逻辑同借方，不再赘述）
    $("#choose_addAccount,#pend_addAccount").on("click",".newLoan",function () {
        // language=HTML
        var loanStr =       '<div class="loanItem">'+
            '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="" class="subjectLoan" type="text" onclick="getActsList($(this))"/>'+
            '   <div class="handle">'+
            '           <span class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</span>'+
            '           <span class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</span>'+
            '   </div>'+
            '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text"/></div>'+
            '   <div class="m5 mNumber" style="display: none">'+
            '       <span>借数量：</span><input class="creditQuantity w65" type="text"/>'+
            '       <span>贷数量：</span><input class="debitQuantity w65" type="text"/>'+
            '       <span>单价：</span><input class="unitPrice w65" type="text"/>'+
            '       <span>单位：</span><span class="measure_unit"></span>'+
            '   </div>'+
            '</div>';
        $(this).parents(".loanItem").after(loanStr);
        if(!$(this).parents(".loanItem").hasClass("fixed")) {
            $(this).parent(".handle").html('<button class="ty-btn ty-btn-red ty-circle-3 cancelLoan">取消贷方</button>');
        }else{
            $(this).parent(".handle").html("");
        }

    });
    //取消贷方的操作
    $("#choose_addAccount,#pend_addAccount").on("click",".cancelLoan",function () {
        //如果取消按钮前面有新增按钮，取消后在他的前一个借方加入新增借方按钮
        if($(this).prev().hasClass("newLoan")){
            $(this).parents(".loanItem").prev().find(".handle").prepend('<button class="ty-btn ty-btn-green ty-circle-3 newLoan">新增贷方</button>');
        }
        //指向此贷方
        var subjectItem= $(this).parents(".loanItem");
        //获取贷方号
        var subjectNo= subjectItem.find(".bookIcon").next("input").attr("id").substring(0,4);
        //如果删除的这个贷方为1001或1002，则对应删除一个现金流量科目
        if(subjectNo === "1001" || subjectNo === "1002"){
            var specialId = subjectItem.attr("data-to");
            subjectInputObj.parents(".borrowMoreLoans").next(".special").find("."+specialId).remove();
        }
        //删除此借方
        $(this).parents(".loanItem").remove();
    });

    /* creator：张旭博，2017-05-06 15:16:54，每一集目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容） */
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");

        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right")
            $(this).find("i").eq(0).addClass("fa-angle-down")
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down")
            $(this).find("i").eq(0).addClass("fa-angle-right")
        }

        //点击文件夹后子列表的显隐
        $(this).next().toggle();

    });
    $(".acTable td").on("click",function () {
        var index = $(this).index()+1;
        setPartSubjects(index);
        $(this).siblings("td").removeClass("acActive");
        $(this).addClass("acActive");

    });
    $(".borrowMoreLoans").on("keyup",".mNumber input",function () {
        var price = $(this).find(".price").val();
        var quantity = $(this).siblings("input:not(:disabled)").val();
        var unitPrice = $(this).val();

        if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
        if(quantity === ""){quantity = 0}
        if(unitPrice === ""){unitPrice = 0}

        if(quantity !== 0 && unitPrice !== 0){
            price = parseFloat((quantity * unitPrice).toFixed(2))
            $(this).parent().prev().find(".price").val(price);
        }
    });
    $("#tblCon").on("click", "[type='btn']", function () {
        var name = $(this).data('name')
        $("#tblCon").data('selectObj', $(this).parents('tr'))
        // 此传入的kind并非数据来源，showAccountingDetail 方法内的接口返回的kind是真正的数据来源
        // 报销的数据只有货物科目可以修改，其他借贷方科目和金额都不能修改，此逻辑也写在showAccountingDetail内
        switch(name) {
            case 'subject_choose':
                // 科目选择
                bounce.show($("#choose_addAccount"))
                var kind = $(this).parents('tr').data('kind')
                var detailId = $(this).parents('tr').data("id");
                choose_subject()

                showAccountingDetail(detailId, kind);
                break;
            case 'subject_approve':
                bounce.show($("#pend_addAccount"))
                pend_subject($(this))
                // 科目审批
                break;
            case 'subject_change':
                // 科目修改
                bounce.show($("#pend_addAccount"))
                pend_subject($(this))
                break;
            case 'subject_reChoose':
                // 不予下账已批准和不予下账已驳回重新选择科目（逻辑同科目选择）
                // bounce.show($("#pend_addAccount"))
                // var kind = $(this).parents('tr').data('kind')
                // var detailId = $(this).parents('tr').data("id");
                // pend_subject()
                // showAccountingDetail(detailId, kind);
                bounce.show($("#pend_addAccount"))
                pend_subject($(this))
                break;
        }
    })
    $("#choose_addAccount").on("click", "[type='btn']", function () {
        var name = $(this).data('name')
        switch(name) {
            case 'noAccounting':
                if (!accountantAuth.toChooseRefuse) {
                    $("#errorTip .tipWord").html("您无此操作权限！");
                    bounce.show($("#errorTip"));
                    return false;
                }
                // 不予下账
                var content =   '<div>是否确定对此条数据不予下账？</div>' +
                    '<textarea id="reason" rows="3" style="width: 300px" placeholder="请填写不予下账理由"></textarea></div>';
                bounce_confirm(content, function () {
                    subjectChooseSubmit(name)
                })
                break;
            case 'subjectSubmit':
                if (!accountantAuth.toChooseSubject) {
                    $("#errorTip .tipWord").html("您无此操作权限！");
                    bounce.show($("#errorTip"));
                    return false;
                }
                // 提交
                bounce_confirm('确定提交吗？', function () {
                    subjectChooseSubmit(name)
                })
                break;
        }
    })
    $("#pend_addAccount").on("click", "[type='btn']", function () {
        var name = $(this).data('name')
        switch(name) {
            case 'noAccounting':
                if ($(".ty-firstTab .ty-active").attr("to") === 'tab3') {
                    if (!accountantAuth.isAgreeRefuse) {
                        $("#errorTip .tipWord").html("您无此操作权限！");
                        bounce.show($("#errorTip"));
                        return false;
                    }
                }
                if ($(".ty-firstTab .ty-active").attr("to") === 'tab4') {
                    if (!accountantAuth.isRejectedRefuse) {
                        $("#errorTip .tipWord").html("您无此操作权限！");
                        bounce.show($("#errorTip"));
                        return false;
                    }
                }

                // 不予下账
                var content =   '<div>是否确定对此条数据不予下账？</div>' +
                    '<textarea id="reason" rows="3" style="width: 300px" placeholder="请填写不予下账理由"></textarea></div>';
                bounce_confirm(content, function () {
                    subjectChooseChange(name)
                })
                break;
            case 'subjectAgree':
                if (!accountantAuth.toChargeCharge) {
                    $("#errorTip .tipWord").html("您无此操作权限！");
                    bounce.show($("#errorTip"));
                    return false;
                }
                // 批准
                var type = $(this).data('type')
                bounce_confirm('确定批准吗？', function () {
                    subjectChooseApprove(type)
                })
                break;
            case 'subjectReject':
                if (!accountantAuth.toChargeCharge) {
                    $("#errorTip .tipWord").html("您无此操作权限！");
                    bounce.show($("#errorTip"));
                    return false;
                }
                // 驳回
                var type = $(this).data('type')
                bounce_confirm('确定驳回吗？', function () {
                    subjectChooseApprove(type)
                })
                break;
            case 'subjectSubmit':
                if ($(".ty-firstTab .ty-active").attr("to") === 'tab3') {
                    if (!accountantAuth.isAgreeUpdate) {
                        $("#errorTip .tipWord").html("您无此操作权限！");
                        bounce.show($("#errorTip"));
                        return false;
                    }
                }
                if ($(".ty-firstTab .ty-active").attr("to") === 'tab4') {
                    if (!accountantAuth.isRejectedUpdate) {
                        $("#errorTip .tipWord").html("您无此操作权限！");
                        bounce.show($("#errorTip"));
                        return false;
                    }
                }
                // 提交
                bounce_confirm('确定提交吗？', function () {
                    subjectChooseChange(name)
                })
                break;
        }
    })
    $("#reimburse").on('click', '[type="btn"]', function () {
        var name = $(this).data('name')
        switch (name) {
            case 'billInfo':
                bounce_Fixed2.show($("#billInfo"))
                var reimburseBillId = $(this).parents("tr").attr("id")
                $.ajax({
                    url: "../reimburseWindow/getReimburseBill.do",
                    data: {reimburseBillId: reimburseBillId},
                    success: function (data) {
                        if (data) {
                            var quotaBill = data.financeReimburseBill
                            var goods= data.financeReimburseBillItemList
                            var billCatName = quotaBill.billCatName
                            $("#billInfo .billQuantity").hide()
                            $("#billInfo .singleAmount").show()
                            switch (billCatName) {
                                case '增值税专用发票':
                                case '增值税普通发票':
                                    $("#billInfo .VATGood").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice+'</td>' +
                                            '   <td>'+goods[i].price+'</td>' +
                                            '   <td>'+(goods[i].taxRate * 100).toFixed(2) +' %</td>' +
                                            '   <td>'+goods[i].taxAmount+'</td>' +
                                            '   <td>'+goods[i].amount+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .VATGood tbody").html(goodsStr)
                                    var issueDate = quotaBill.issueDate
                                    var quantity = quotaBill.relativeBillQuantity
                                    issueDate = issueDate.substring(0,4) +'年' + issueDate.substring(5,7) + '月'
                                    if (Number(quantity) > 1) {
                                        $("#billInfo .billQuantity").show().html('内容与本票据完全相同，且开票月份也为' + issueDate + '的票据总数量为' + quantity + '张')
                                    }
                                    break;
                                case '定额普通发票':
                                    var goodsStr = ''
                                    $("#billInfo .quotaGood").show().siblings().hide()
                                    $("#billInfo .quotaInvoice .billCatName").html('定额普通发票')
                                    $("#billInfo .quotaInvoice .feeCatName").html(quotaBill.feeCatName)
                                    var amount = 0
                                    var number = 0
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '    <td>'+goods[i].uniPrice+'</td>' +
                                            '    <td>'+goods[i].itemQuantity+'张</td>' +
                                            '    <td>'+goods[i].price+'</td>' +
                                            '</tr>'
                                        amount = parseFloat(amount) + parseFloat(goods[i].price)
                                        number = parseInt(number) + parseInt(goods[i].itemQuantity)
                                    }
                                    goodsStr += '<tr>' +
                                        '    <td>总计</td>' +
                                        '    <td>'+number+'张</td>' +
                                        '    <td>'+amount+'</td>' +
                                        '</tr>'
                                    $("#billInfo .quotaGood tbody").html(goodsStr)
                                    $("#billInfo .singleAmount").hide()
                                    break;
                                case '其他普通发票':
                                    $("#billInfo .otherGood").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice+'</td>' +
                                            '   <td>'+goods[i].price+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .otherGood tbody").html(goodsStr)
                                    var issueDate = quotaBill.issueDate
                                    var quantity = quotaBill.relativeBillQuantity
                                    issueDate = issueDate.substring(0,4) +'年' + issueDate.substring(5,7) + '月'
                                    if (Number(quantity) > 1) {
                                        $("#billInfo .billQuantity").show().html('内容与本票据完全相同，且开票月份也为' + issueDate + '的票据总数量为' + quantity + '张')
                                    }
                                    break;
                                case '收据':
                                    $("#billInfo .receipt").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice+'</td>' +
                                            '   <td>'+goods[i].price+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .receipt tbody").html(goodsStr)
                                    break;
                                default:
                                    console.log('不存在的值')
                            }
                            $("#billInfo .memo").html(quotaBill.memo)
                            $("#billInfo .amount").html(quotaBill.itemAmount)
                        }
                    }
                })
                break;
            case 'billPic':
                var reimburseBillId = $(this).parents("tr").attr("id")

                $.ajax({
                    url:"../reimburseWindow/getBillAttachmentList.do" ,
                    data: {reimburseBillId: reimburseBillId},
                    success:function (data) {
                        var list = data.billAttachmentList
                        if (list.length > 0) {
                            seePicture(list[0].path)
                        } else {
                            layer.msg('未上传图片')
                        }
                    }
                });
                break;
        }
    })
    initPage();
});
var del = "";//删了这条信息

// creator: 张旭博，2018-03-06，初始化
function initPage() {
    if (!accountantAuth.toChooseScan) $("#tabChoose").remove() // 没有待选择模块移除此标签，下同理
    if (!accountantAuth.toChargeScan) $("#tabApprove").remove()
    if (!accountantAuth.isAgreeScan) $("#tabAgree").remove()
    if (!accountantAuth.isRejectedScan) $("#tabReject").remove()
    $(".ty-firstTab li").eq(0).click()
}

// creator: 张旭博，2019-06-20 11:09:00，获取各种状态的列表
function getMainList() {
    var firstTab = Number($(".ty-firstTab .ty-active").attr('to').substring(3, 4)) //1待选择2待审批3已批准4已驳回
    var tableHeaderStr = ''
    var tableBodyStr = ''
    console.log(firstTab)
    if (firstTab === 1) {
        var secondTab = Number($(".ty-secondTab .ty-active").attr('type'))
        // 待选择列表
        if (secondTab === 1) {
            // 正常待选择
            $.ajax({
                url: '../data/getAccountingData.do',
                data: {},
                async: false,
                success: function (data,status,xhr) {
                    var detail      = data.accountDetailList // 会计详情列表
                    var billList    = data.financeAccountBillList // 财务详情列表
                    var nowMonth = formatTime(xhr.getResponseHeader('Date')).substring(0,7)
                    console.log(nowMonth)

                    tableHeaderStr =    '<tr>'+
                        '<td>序号</td>' +
                        '<td>摘要</td>' +
                        '<td>收入</td>' +
                        '<td>支出</td>' +
                        '<td>票据数量</td>' +
                        '<td>票据所属月份</td>' +
                        '<td>用途</td>' +
                        '<td>经手人</td>' +
                        '<td>备注</td>' +
                        '<td>操作</td>' +
                        '</tr>';
                    for (var i in detail) {
                        var billPeriod = ''
                        var billMonth = detail[i].beginDate.substring(0,7)
                        console.log(billMonth)
                        if (billMonth !== '') {
                            billPeriod = billMonth === nowMonth ? '1' : '2'
                        } else {
                            billPeriod = '2'
                        }
                        var billDetail = {
                            kind: 1,
                            operatorFinance: detail[i].createName, // 创建者
                            operatorName: detail[i].auditorName, // 经手人
                            pricetype: (detail[i].credit?0:1), // 收入或支出
                            summary: detail[i].summary, // 摘要
                            purpose: detail[i].purpose, // 用途
                            bill_quantity: detail[i].billQuantity, // 票据数量
                            bill_peroid: billPeriod, // 票据所属月份
                            price: detail[i].billAmount, //
                            memo: detail[i].memo, //
                            bill_detail: detail[i].id //
                        }
                        tableBodyStr +=  '  <tr data-id="' + detail[i].id + '" data-kind="1">' +
                            '<td>' + (-(-i-1))+'</td>' +
                            '<td>' + detail[i].summary + '</td>' +          // 摘要
                            '<td>' + (detail[i].credit ? detail[i].billAmount : '--') + '</td>' + // 收入
                            '<td>' + (detail[i].debit ? detail[i].billAmount : '--') + '</td>' +  // 支出
                            '<td class="billQuantity">' + (detail[i].billQuantity || '') + '</td>' + // 票据数量
                            '<td class="billPeriod" data-time="'+billPeriod+'">' + chargeBillPeriod(billPeriod)+ '</td>' +    // 票据所属月份
                            '<td>' + detail[i].purpose + '</td>' +  // 用途
                            '<td>' + detail[i].auditorName + '</td>' +  // 经手人
                            '<td>' + detail[i].memo + '</td>' + // 备注
                            '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                            '<td>' +
                            '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                            '</td>' +
                            '</tr>'
                    }

                    for (var j = 0; j < billList.length; j++) {
                        var billDetail = {
                            kind: 2,
                            operatorName: billList[j].createName,
                            pricetype: 0, // 收入或支出
                            summary: billList[j].summary, // 摘要
                            purpose: billList[j].purpose, // 用途
                            bill_quantity: billList[j].billQuantity, // 票据数量
                            bill_peroid: billList[j].billPeriod || 1, // 票据所属月份
                            price: billList[j].billAmount, //
                            memo: billList[j].memo, //
                            bill_detail: billList[j].id //
                        }
                        tableBodyStr +=     '<tr data-id="' + billList[j].id + '" data-kind="2">' +
                            '<td>' + (-(-j-1-detail.length))+'</td>' +
                            '<td>' + billList[j].summary + '</td>' +          // 摘要
                            '<td class="income">' + (billList[j].type === '1'?billList[j].billAmount:'--') + '</td>' + // 收入 'type' : 1- 收入 2- 支出
                            '<td class="pay">' + (billList[j].type === '2'?billList[j].billAmount:'--') + '</td>' +  // 支出
                            '<td class="billQuantity">' + (billList[j].billQuantity || '--') + '</td>' + // 票据数量
                            '<td class="billPeriod">' + chargeBillPeriod(billList[j].billPeriod)+ '</td>' +    // 票据所属月份
                            '<td>' + billList[j].purpose + '</td>' +  // 用途
                            '<td>' + billList[j].operatorName + '</td>' +  // 经手人
                            '<td>' + billList[j].memo + '</td>' + // 备注
                            '<td class="hd billDetail">' + JSON.stringify(billDetail) + '</td>' + // 提交时需要用到的数据
                            '<td>' +
                            '<span class="ty-color-blue" type="btn" data-name="subject_choose">选择科目</span>' +
                            '</td>' +
                            '</tr>'

                    }
                    $(".ty-mainData thead").html(tableHeaderStr);
                    $(".ty-mainData tbody").html(tableBodyStr);
                }
            })
        } else if (secondTab === 2) {
            // 修改待选择 （暂未开发）
            tableHeaderStr =    '<tr>'+
                '<td>序号</td>' +
                '<td>凭证日期</td>' +
                '<td>摘要</td>' +
                '<td>收入</td>' +
                '<td>支出</td>' +
                '<td>票据数量</td>' +
                '<td>票据所属月份</td>' +
                '<td>用途</td>' +
                '<td>经手人</td>' +
                '<td>借方科目</td>' +
                '<td>借方金额</td>' +
                '<td>贷方科目</td>' +
                '<td>贷方金额</td>' +
                '<td>备注</td>' +
                '<td>操作</td>' +
                '</tr>'
            $(".ty-mainData thead").html(tableHeaderStr);
            $(".ty-mainData tbody").html('');
        }
    } else {
        var approve_status = Number($(".ty-secondTab .ty-active").attr('state'))
        $.ajax({
            url: "../accountant/getApprove.do",
            data: {"approve_status": approve_status},
            success: function (data) {
                //获取数据-审批列表
                var approvalList = data["approveList"];

                //遍历数据
                if(approvalList.length > 0) {
                    for (var i in approvalList) {
                        //获取用到的字段
                        var id                  = approvalList[i].id;
                        var voucher_id          = approvalList[i].voucher_id;           //表示数据来自凭证表还是来自凭证历史表。 如果该值大于0表示来自历史表，如果后台没有传该字段到页面，则提交的时候传0

                        var type                = approvalList[i].type;
                        var create_date         = approvalList[i].create_date;          //凭证日期
                        var summary             = approvalList[i].summary;              //摘要
                        var memo                = approvalList[i].memo;                 //备注
                        var purpose             = approvalList[i].purpose;              //用途
                        var create_name         = approvalList[i].create_name;          //经手人

                        var priceType           = approvalList[i].pricetype;            //项目类型（1-收入 2-支出）
                        var amount              = approvalList[i].amount;               //金额
                        var billQuantity        = approvalList[i].bill_quantity;        //票据数量

                        var borrowInfo          = approvalList[i].borinfo;              //借方信息
                        var loanInfo            = approvalList[i].loaninfo;             //贷方信息
                        var cashJson            = approvalList[i].cashjson;             //现金流量

                        var cash                = approvalList[i].cash;
                        var cashitemName        = approvalList[i].cashitemName;
                        var cashitem            = approvalList[i].cashitem;

                        var kind                = approvalList[i].kind;
                        var bill_detail         = approvalList[i].bill_detail;
                        var bill_period         = approvalList[i].bill_period;          //票据所属月份
                        var belong_peroid       = approvalList[i].belong_peroid;        //凭证日期（1-本月凭证 2-上月凭证）

                        var addtime             = approvalList[i].addtime;              //科目选择时间
                        var voucherOperator     = approvalList[i].voucherOperator;      //科目选择者
                        var is_settled          = approvalList[i].is_settled;
                        var reason              = approvalList[i].reason;

                        cashJson        === undefined   ? cashJson      = "" : cashJson;
                        billQuantity    === 0           ? billQuantity  = "" : billQuantity;
                        voucher_id      === undefined   || voucher_id === "" ? voucher_id = 0 : voucher_id;

                        //定义各个字段字符串
                        var cashStr = '',
                            operateStr = '';       //弹窗底部按钮字符串

                        if (cash) {
                            cashStr =   '<td id="' + cashitem + '" class="hd cashitemName">' + cashitemName + '</td>' +
                                '<td class="hd cash">' + cash + '</td>';
                        }

                        //定义不同状态下的操作按钮以及点击事件
                        switch (approve_status) {
                            case 1:
                            case 4:
                            case 5:
                                operateStr = '<span class="ty-color-blue" type="btn" data-name="subject_approve">审批</span>';
                                break;
                            case 2:
                            case 3:
                            case 7:
                            case 8:
                                operateStr = '<span class="ty-color-blue" type="btn" data-name="subject_change">修改</span>';
                                break;
                            case 9:
                            case 6:
                                operateStr = '<span class="ty-color-blue" type="btn" data-name="subject_reChoose">修改</span>'; // 算是重新选择
                                break;
                        }

                        if (type === '2') {
                            operateStr = '<td class="changeColor ttl_control ty-td-control"></td>';
                        }

                        //展示多条借方贷方
                        var borrowStr = '';
                        var loanStr = '';
                        var borrowPrice = '';
                        var loanPrice = '';
                        if(borrowInfo){
                            for (var j = 0; j < borrowInfo.length; j++) {
                                borrowStr   += '<div>' + borrowInfo[j].borrowNames + '</div>';
                                loanStr     += '<div></div>';
                                borrowPrice += '<div>' + borrowInfo[j].borrowAmount + '</div>';
                                loanPrice   += '<div></div>'
                            }
                        }
                        if(loanInfo){
                            for (var k = 0; k < loanInfo.length; k++) {
                                borrowStr   += '<div></div>';
                                loanStr     += '<div>' + loanInfo[k].loanNames + '</div>';
                                borrowPrice += '<div></div>';
                                loanPrice   += '<div>' + loanInfo[k].loanAmount + '</div>'
                            }
                        }

                        //完成表格字符串
                        if (approve_status === 1 || approve_status === 2 || approve_status === 3 || approve_status === 4 || approve_status === 7 || approve_status === 8) {
                            tableHeaderStr =    '<tr>' +
                                '<td>序号</td>' +
                                '<td>凭证日期</td>' +
                                '<td>摘要</td>' +
                                '<td>收入</td>' +
                                '<td>支出</td>' +
                                '<td>票据数量</td>' +
                                '<td>票据所属月份</td>' +
                                '<td>用途</td>' +
                                '<td>经手人</td>' +
                                '<td>借方科目</td>' +
                                '<td>借方金额</td>' +
                                '<td>贷方科目</td>' +
                                '<td>贷方金额</td>' +
                                '<td>备注</td>' +
                                '<td class="ttl_control">操作</td>' +
                                '</tr>';
                            tableBodyStr +=     '<tr data-id="' + bill_detail +'" data-kind="' + kind + '">' +
                                '<td>' + (-(-i - 1)) + '</td>' +
                                '<td>' + create_date + '</td>' +
                                '<td>' + summary + '</td>' +
                                '<td>' + (priceType !== "1" ? amount : "--") + '</td>' +
                                '<td>' + (priceType === "1" ? amount : "--") + '</td>' +
                                '<td>' + billQuantity + '</td>' +
                                '<td>' + (bill_period === 1 ? "本月票据" : "非本月票据") + '</td>' +
                                '<td>' + purpose + '</td>' +
                                '<td>' + create_name + '</td>' +
                                '<td>' + borrowStr + '</td>' +
                                '<td>' + borrowPrice + '</td>' +
                                '<td>' + loanStr + '</td>' +
                                '<td>' + loanPrice + '</td>' +
                                '<td>' + memo + '</td>' +
                                + cashStr +
                                '<td class="hd data">' + JSON.stringify(approvalList[i]) + '</td>' +
                                '<td>' + operateStr + '</td>' +

                                '</tr>';
                        } else {
                            tableHeaderStr =    '<tr>' +
                                '<td>序号</td>' +
                                '<td>摘要</td>' +
                                '<td>收入</td>' +
                                '<td>支出</td>' +
                                '<td>票据数量</td>' +
                                '<td>票据所属月份</td>' +
                                '<td>用途</td>' +
                                '<td>经手人</td>' +
                                '<td>备注</td>' +
                                '<td>操作</td>' +
                                '</tr>';
                            tableBodyStr +=     '<tr  data-id="' + bill_detail +'" data-kind="' + kind + '">' +
                                '<td>' + (-(-i - 1)) + '</td>' +
                                '<td>' + summary + '</td>' +
                                '<td>' + (priceType !== "1" ? amount : "--") + '</td>' +
                                '<td>' + (priceType === "1" ? amount : "--") + '</td>' +
                                '<td>' + billQuantity + '</td>' +
                                '<td>' + (bill_period === 1 ? "本月票据" : "非本月票据") + '</td>' +
                                '<td>' + purpose + '</td>' +
                                '<td>' + create_name + '</td>' +
                                '<td>' + memo + '</td>' +
                                '<td class="hd data">' + JSON.stringify(approvalList[i]) + '</td>' +
                                '<td>' + operateStr + '</td>' +
                                '</tr>';
                        }
                    }
                    // serialNum();
                    $(".changeColor").parent().css("color", "#ccc");//已冲红的字体颜色
                }
                $(".ty-mainData thead").html(tableHeaderStr);
                $(".ty-mainData tbody").html(tableBodyStr);
            }
        });

    }
}

// creator: 张旭博，2019-06-26 10:26:57，主列表的审批、修改 - 按钮
function pend_subject(obj) {
    var $selectObj = $("#tblCon").data('selectObj')
    var voucherData = JSON.parse($selectObj.find('.data').html())
    var approve_status = Number($(".ty-secondTab li.ty-active").attr('state'))
    $.ajax({
        url: "judgeMinorSettle.do",
        data: {
            id: voucherData.id ,// id 凭证id
            voucher_id: voucherData.voucher_id // voucher_id 历史表的id，存在则说明修改的是历史表的数据
        },
        success: function (data) {

            var res = data.res ;
            var isPeriodChange = data.belong_peroid ; //  true可以改 、  false不能改(belong_peroid在此不表示凭证日期，只用来判断是否禁用操作)
            var belong_peroid = voucherData.belong_peroid ; //  true可以改 、  false不能改(belong_peroid在此不表示凭证日期，只用来判断是否禁用操作)
            var bill_period = Number(voucherData.bill_period) ; // 凭证日期（1-本月凭证 2-上月凭证）
            if(res){
                $("#errorTip .tipWord").html("该凭证已经记账，请在“对公报表 - 反结账”后在修改！") ;
                bounce.show($("#errorTip")) ;
                return false ;
            }

            var headInfoStr =   '<div class="normalItem">' +
                '    <span>凭证日期：</span>' +
                '    <select class="subjectInput voucher" name="bill_period">' +
                '        <option value="2">上月凭证</option>' +
                '        <option value="1">本月凭证</option>' +
                '    </select>' +
                '</div>',
                borrowMoreLoanStr = '',
                cashFlowStr = '',
                chooseBtn = '';
            if (approve_status === 1 || approve_status === 2 || approve_status === 3 || approve_status === 4 || approve_status === 7 || approve_status === 8) {
                //待审批 - 正常待审批 || 待审批-修改待审批
                if (approve_status === 1 || approve_status === 4) {
                    if (!$("#tbody").find(".subjectActive").children("td").eq(8).html()) {
                        $("#tbody").find(".subjectActive").children("td").eq(8).html("");
                    }
                    headInfoStr         =       '<div class="normalItem">'+
                        '   <span>科目选择者：</span>'+
                        '   <input value="' + voucherData.voucherOperator + '" type="text" name="" disabled style="width: 100px">'+
                        '   <span>科目选择时间：</span>'+
                        '   <input value="' + voucherData.addtime + '" type="text" name="" class="addTime" disabled style="width: 120px">'+
                        '   <span>凭证日期：</span>'+
                        '   <input value="' + voucherData.create_date + '" type="text" name="" disabled style="width: 120px">'+
                        '</div>';
                    borrowMoreLoanStr   =       showBorLoan(voucherData.borinfo, voucherData.loaninfo, approve_status);
                    cashFlowStr         =       showCashFlow(voucherData.cashjson);

                    if (approve_status === 1) {

                        chooseBtn = '<button class="ty-btn ty-btn-red ty-btn-big ty-circle-3" type="btn" data-name="subjectReject" data-type="3">驳回</button> ' +
                            '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="subjectAgree" data-type="2">批准</button>' ;
                    } else if (approve_status === 4) {
                        chooseBtn = '<button class="ty-btn ty-btn-red ty-btn-big ty-circle-3" type="btn" data-name="subjectReject" data-type="8">驳回</button> ' +
                            '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="subjectAgree" data-type="7">批准</button>' ;
                    }

                }else if( approve_status === 2 || approve_status === 3 || approve_status === 7 || approve_status === 8){
                    borrowMoreLoanStr   =       showBorLoan(voucherData.borinfo, voucherData.loaninfo);
                    cashFlowStr         =       showCashFlow(voucherData.cashjson);

                    // 根据是否有1001 1002增加现金流量选择框（特殊处理）
                    var getCashLength   =       voucherData.cashjson.length;
                    var nowCashLength   =       0;
                    $("#pend_addAccount .subjectBorrow").each(function () {
                        var subjectNo = $(this).attr("id").substring(0,4);
                        if(subjectNo == "1001" || subjectNo == "1002"){
                            nowCashLength++;
                        }
                    });
                    $("#pend_addAccount .subjectLoan").each(function () {
                        var subjectNo = $(this).attr("id").substring(0,4);
                        if(subjectNo == "1001" || subjectNo == "1002"){
                            nowCashLength++;
                        }
                    });
                    var EmptySpecialLength = nowCashLength-getCashLength;

                    if(EmptySpecialLength > 0){
                        for(var i = 0;i<EmptySpecialLength;i++){
                            cashFlowStr +=  '<div class="cashFlowGroup">'+
                                '<div class="normalItem">'+
                                '<span>现金流量：</span>'+
                                '<select class="cash">'+
                                '<option value="1">-----请选择现金流量项目-----</option>'+
                                $(".cashFlowOption").html()+
                                '</select> '+
                                '<span>金额：</span>'+
                                '<input class="amount" type="text"/>'+
                                '</div>'+
                                '</div>';
                        }
                    }

                    chooseBtn          =        '<button class="ty-btn ty-btn-red   ty-btn-big ty-circle-3"  type="btn" data-name="noAccounting">不予下账</button> ' +
                        '<button class="ty-btn ty-btn-green ty-btn-big ty-circle-3"  type="btn" data-name="subjectSubmit" id="pendSubmit">提交</button>';
                }
            }else if(approve_status === 5) {
                //不予下账待审批
                headInfoStr         =       '<div class="normalItem">'+
                    '   <span>科目选择者：</span>'+
                    '   <input value="' + voucherData.voucherOperator + '" type="text" name="" disabled style="width: 100px">'+
                    '   <span>科目选择时间：</span>'+
                    '   <input value="' + voucherData.addtime + '" type="text" name="" class="addTime" disabled style="width: 120px">'+
                    '   <span>不予下账：</span>'+
                    '   <input value="' + voucherData.reason + '" type="text" name="" disabled style="width: 120px">'+
                    '</div>';
                chooseBtn           =       '<button class="ty-btn ty-btn-red ty-btn-big ty-circle-3" type="btn" data-name="subjectReject" data-type="6">驳回</button>' +
                    '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="subjectAgree" data-type="9">批准</button>' ;

            }else if( approve_status === 6 || approve_status === 9 ){
                borrowMoreLoanStr   =       showBorLoan(0,0);
                chooseBtn           =       '<button class="ty-btn ty-btn-green ty-btn-big ty-circle-3"  type="btn" data-name="subjectSubmit" id="pendSubmit">提交</button>';
            }
            $("#pend_addAccount .headInfo").html(headInfoStr);
            $("#pend_addAccount .borrowMoreLoans").html(borrowMoreLoanStr);
            $("#pend_addAccount .special").html(cashFlowStr);
            $("#pend_addAccount .bonceFoot").html(chooseBtn);

            // 只能放在这，得等科目准备好之后再把前面的字段加到表格里面
            var kind = obj.parents('tr').data('kind')
            var detailId = obj.parents('tr').data("id");
            showAccountingDetail(detailId, kind);

            // 本月票据还是只能选本月凭证， 非本月票据按上次选的显示（getApprove中的belong_peroid）,
            // 另外（judgeMinorSettle返回的用来判断是否可以更改，true代表可以更改，false代表不能更改）
            console.log(bill_period,isPeriodChange,belong_peroid)
            if(bill_period === 1){
                $("#pend_addAccount .voucher").val(1).prop("disabled", true)
            }else{
                $("#pend_addAccount .voucher").val(belong_peroid).prop("disabled", !isPeriodChange)
            }
            if (approve_status === 1 || approve_status === 4) {
                // $("#pend_addAccount .borrowMoreLoans .handle").remove();
                $("#pend_addAccount input,#pend_addAccount select").prop("disabled",true);
            }
            $('body').everyTime('0.5s','pend_addAccount',function(){
                var allBorrowPrice = 0;
                var allLoanPrice = 0;
                var i = 0;
                $("#pend_addAccount .borrowItem").each(function () {
                    var price = $(this).find(".price").val();
                    var creditQuantity = $(this).find(".creditQuantity").val();
                    var debitQuantity = $(this).find(".debitQuantity").val();
                    var unitPrice = $(this).find(".unitPrice").val();

                    if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
                    if(creditQuantity === ""){creditQuantity = 0}
                    if(debitQuantity === ""){debitQuantity = 0}
                    if(unitPrice === ""){unitPrice = 0}

                    if($(this).find(".mNumber").is(':visible')){
                        if(creditQuantity != 0){
                            $(this).find(".debitQuantity").prop("disabled",true);
                            price =  parseFloat((creditQuantity * unitPrice).toFixed(2));
                            $(this).find(".price").val(price);
                        }else{
                            $(this).find(".debitQuantity").prop("disabled",false);
                        }
                        if(debitQuantity != 0){
                            $(this).find(".creditQuantity").prop("disabled",true);
                            price =  parseFloat((debitQuantity * unitPrice).toFixed(2));
                            $(this).find(".price").val(price);
                        }else{
                            $(this).find(".creditQuantity").prop("disabled",false);
                        }
                    }
                    allBorrowPrice += price;
                });
                $("#pend_addAccount .loanItem").each(function () {
                    var price = $(this).find(".price").val();
                    var creditQuantity = $(this).find(".creditQuantity").val();
                    var debitQuantity = $(this).find(".debitQuantity").val();
                    var unitPrice = $(this).find(".unitPrice").val();

                    if(price === ""){price = 0}else{price = parseFloat((price*1).toFixed(2))}
                    if(creditQuantity === ""){creditQuantity = 0}
                    if(debitQuantity === ""){debitQuantity = 0}
                    if(unitPrice === ""){unitPrice = 0}

                    if($(this).find(".mNumber").is(':visible')){
                        if(creditQuantity != 0){
                            $(this).find(".debitQuantity").prop("disabled",true);
                            price =  parseFloat((creditQuantity * unitPrice).toFixed(2));
                            $(this).find(".price").val(price);

                        }else{
                            $(this).find(".debitQuantity").prop("disabled",false);
                        }
                        if(debitQuantity != 0){
                            $(this).find(".creditQuantity").prop("disabled",true);
                            price =  parseFloat((debitQuantity * unitPrice).toFixed(2));
                            $(this).find(".price").val(price);
                        }else{
                            $(this).find(".creditQuantity").prop("disabled",false);
                        }
                    }
                    allLoanPrice += price;
                });
                var allPrice = parseFloat($("#accountingDetail").find(".price").html());
                $("#pend_addAccount .borrowMoreLoans").find("input:not(:disabled)").each(function () {
                    if($(this).val() == ""){
                        i++;
                    }
                });
                // $("#choose_addAccount .source span").html(i);
                if(allPrice === allBorrowPrice && allPrice === allLoanPrice && i===0){
                    $("#pendSubmit").removeAttr("disabled")
                }else{
                    $("#pendSubmit").attr("disabled","disabled")
                }
                console.log(allBorrowPrice +'-'+ allPrice + '-' + allLoanPrice)
            });

        }
    }) ;
}

// creator: 张旭博，2019-06-26 15:56:23，批准或者驳回
function subjectChooseApprove(btnType) {
    var $selectObj = $("#tblCon").data('selectObj')
    var voucherData = JSON.parse($selectObj.find('.data').html())
    var data = {
        id: voucherData.id,
        approve_status: btnType,
        voucher_id: voucherData.voucher_id
    };
    var cashjson = [];
    if($("#pend_addAccount .special").text() !== ""){
        $("#pend_addAccount .special .cashFlowGroup").each(function () {
            var cashitem = $(this).find(".cash").val();
            var cashes = $(this).find(".amount").val();
            if(cashitem !== 1 && cashes !== ""){
                cashjson.push({"cashitem":cashitem,"cashes":cashes})
            }
        });
        data["cashjson"] = JSON.stringify(cashjson);
    }
    console.log(data)
    $.ajax({
        url: "../accountant/setApprove.do",
        data: data,
        success: function (data) {
            bounce.cancel()
            bounce_Fixed.cancel()
            if (data.res > 0) {
                layer.msg("操作成功");
                getMainList()
            } else {
                operate("操作失败,请稍后再试");
            }
        }
    })
}

// creator: 张旭博，2019-06-26 16:18:53，修改提交 - 按钮
function subjectChooseChange(btnName) {
    var $selectObj = $("#tblCon").data('selectObj')
    var voucherData = JSON.parse($selectObj.find('.data').html())
    var approve_status = btnName === 'noAccounting'?5:4

    var subjectBorrow = [] ;
    var subjectLoan = [] ;
    $("#pend_addAccount .borrowItem").each(function () {
        var borrow = $(this).find(".subjectBorrow").attr("id");
        var subjectNames = $(this).find(".subjectBorrow").val();
        var price = $(this).find(".price").val();
        var creditQuantity  = $(this).find(".creditQuantity").val();if(creditQuantity === ""){creditQuantity = 0};
        var debitQuantity   = $(this).find(".debitQuantity").val();if(debitQuantity === ""){debitQuantity = 0};
        var unitPrice    = $(this).find(".unitPrice").val();if(unitPrice === ""){unitPrice = 0};
        var productID = $(this).attr("pid");
        var qAA = $(this).find(".subjectBorrow").attr("cn");   //quantityAssistingAccounting 数量辅助核算
        if(productID){
            if(qAA === "0"){
                creditQuantity = 0;
                unitPrice = 0;
            }else if(qAA === "1"){
                creditQuantity = $(this).find(".creditQuantity").html();
                unitPrice = $(this).find(".unitPrice").html();
            }
            subjectBorrow.push({
                "subject" : borrow ,
                "subjectNames":subjectNames ,
                "price" : price ,
                "creditQuantity" : creditQuantity,
                "debitQuantity" : 0,
                "unitPrice" : unitPrice,
                "productID" : productID
            });
        }else{
            subjectBorrow.push({
                "subject" : borrow ,
                "subjectNames":subjectNames ,
                "price" : price ,
                "creditQuantity" : creditQuantity,
                "debitQuantity" : debitQuantity,
                "unitPrice" : unitPrice
            });
        }
    });
    $("#pend_addAccount .loanItem").each(function () {
        var loan = $(this).find(".subjectLoan").attr("id");
        var subjectNames = $(this).find(".subjectLoan").val();
        var price = $(this).find(".price").val();
        var creditQuantity  = $(this).find(".creditQuantity").val();if(creditQuantity === ""){creditQuantity = 0};
        var debitQuantity   = $(this).find(".debitQuantity").val();if(debitQuantity === ""){debitQuantity = 0};
        var unitPrice    = $(this).find(".unitPrice").val();if(unitPrice === ""){unitPrice = 0};
        subjectLoan.push({
            "subject" : loan ,
            "subjectNames":subjectNames ,
            "price" : price,
            "creditQuantity" : creditQuantity,
            "debitQuantity" : debitQuantity,
            "unitPrice" : unitPrice
        });
    });

    var belong_peroid = $("#pend_addAccount .voucher").val() ;  //凭证日期12
    var data = {
        id: voucherData.id,
        voucher_id: voucherData.voucher_id,
        approve_status: approve_status,
        subjectBorrow: JSON.stringify(subjectBorrow),
        subjectLoan: JSON.stringify(subjectLoan),
        belong_peroid: belong_peroid,
        reason: '' ,
    };
    if (btnName === 'noAccounting') {
        data.reason = $("#reason").val();
    }
    var cashjson = [];
    if($("#pend_addAccount .special").text() !== ""){
        $("#pend_addAccount .special .cashFlowGroup").each(function () {
            var cashitem = $(this).find(".cash").val();
            var cashes = $(this).find(".amount").val();
            if(cashitem !== 1 && cashes !== ""){
                cashjson.push({"cashitem":cashitem,"cashes":cashes})
            }
        });
        data.cashjson = JSON.stringify(cashjson);
    }
    console.log(data)
    $.ajax({
        url: "../accountant/updateVoucher.do",
        data: data,
        type: "post",
        dataType: "json",
        success: function (data) {
            bounce.cancel()
            bounce_Fixed.cancel()
            if (data.res > 0) {
                layer.msg("操作成功")
                $(".ty-secondTab").find(".ty-active:visible").click()
            }else if(data.res === (-1)){
                operate("请正确填写科目！")
            }else if(data.res === (-2)){
                operate("请先反结账")
            }else if(data.res === (-3)){
                operate("该凭证已经结账，不能选择不予下账！！")
            }else if(data.res === 0){
                operate("请检查是否有未审批的凭证！")
            }else{
                operate("未知的返回值！");
            }
        }
    });
}

// creator: 张旭博，2019-06-20 15:45:03，打开数据详情
function accountingDetailBtn() {
    var kind = $("#accountingDetail").data('kind')
    if (kind == 1) {
        bounce_Fixed.show($("#reimburse"))
    } else {
        bounce_Fixed.show($("#accountingDetail"))
    }

}

// creator: 张旭博，2017-09-13 15:30:05，插入凭证详情
function showAccountingDetail(detailId, kind) {
    $.ajax({
        url: "../data/getAccountingDetail.do",
        data: {
            detailId: detailId,
            kind: kind
        },
        success: function (data) {
            if(data == ""){
                return false ;
            }
            var content = data["content"];
            var kind = data["kind"];
            var debitSubject = data["debitSubject"]; // 增专科目关联数据

            var accountingDetailStr = "";
            var specialInvoiceGoodsStr = "";
            $("#accountingDetail").data('kind', kind)
            if(kind === 1){
                var borrowMoreLoanStr = '';
                var cashFlowStr = '';
                var approve_status = Number($(".ty-secondTab li.ty-active").attr('state'))
                var billList = content.financeReimburseBillHashSet

                if($("#pend_addAccount").is(':visible') && approve_status !== 6 && approve_status !== 9){
                    for (var i in billList) {
                        var goodList = billList[i].financeReimburseBillItemHashSet
                        var pictureInfo = billList[i].financeReimburseBillAttachmentHashSet
                        var billCatName   = billList[i].billCatName;  //票据类型
                        var certificationState = billList[i].certificationState

                        var picture = pictureInfo.length > 0 ? pictureInfo[0].path : ''

                        for (var j in goodList) {
                            var amount = '';
                            if(certificationState === "2" || certificationState === "3" || billCatName === "增值税普通发票"){
                                amount = goodList[j].amount;
                            }else{
                                amount = goodList[j].price;
                            }
                            var goodStr =   '<td>' + goodList[j].itemName + '</td>' +       //货物或应税劳务、服务名称
                                '<td>' + goodList[j].model + '</td>' +          //规格型号
                                '<td>' + goodList[j].unit + '</td>' +           //单位
                                '<td class="creditQuantity">' + goodList[j].itemQuantity + '</td>' +   //数量
                                '<td class="unitPrice">' + goodList[j].uniPrice + '</td>' +       //单价
                                '<td><input class="price" value="' + amount + '" style="display: none">' + amount + '</td>' +         //金额
                                '<td><span class="ty-color-blue" data-path="'+picture+'" onclick="seePic($(this))">查看票据内容</span></td>';
                            $("#special"+goodList[j].id).prepend(goodStr);
                        }
                    }
                    $("#pend_addAccount").find('input,select').not('.voucher').prop('disabled', true)
                    if (approve_status === 1 || approve_status === 4 || approve_status === 5) {
                        $("#pend_addAccount .borrowItems").find('input,select').prop('disabled', true)
                    } else {
                        $("#pend_addAccount .borrowItems").find('input,select').prop('disabled', false)
                    }
                }else{
                    if (billList.length > 0) {
                        for (var i in billList) {
                            var goodList = billList[i].financeReimburseBillItemHashSet
                            var pictureInfo = billList[i].financeReimburseBillAttachmentHashSet
                            var billCatName   = billList[i].billCatName;  //票据类型
                            var certificationState = billList[i].certificationState

                            var picture = pictureInfo.length > 0 ? pictureInfo[0].path : ''
                            for (var j in goodList) {
                                var amount = '';

                                //认证通过后 金额为金额合计  无需认证和认证失败  金额为含税合计
                                if(certificationState === "2" || certificationState === "3" || billCatName === "增值税普通发票"){
                                    amount = goodList[j].amount;
                                }else{
                                    amount = goodList[j].price;
                                }

                                specialInvoiceGoodsStr += '<tr class="borrowItem borrow" pid="' + goodList[j].id + '">' +
                                    '<td>' + goodList[j].itemName + '</td>' +       //货物或应税劳务、服务名称
                                    '<td>' + goodList[j].model + '</td>' +          //规格型号
                                    '<td>' + goodList[j].unit + '</td>' +           //单位
                                    '<td class="creditQuantity">' + goodList[j].itemQuantity + '</td>' +   //数量
                                    '<td class="unitPrice">' + goodList[j].uniPrice + '</td>' +       //单价
                                    '<td><input class="price" value="' + amount + '" style="display: none">' + amount + '</td>' +          //金额
                                    '<td><span class="ty-color-blue" data-path="'+picture+'" onclick="seePic($(this))">查看票据内容</span></td>' +          //金额
                                    '<td><input placeholder="请点击选择科目" class="subjectBorrow" type="text" id="' + goodList[j].subject + '"  value="'+goodList[j].subjectName+'" onclick="getActsList($(this))" style="width: 100%"></td>' +
                                    '</tr>';
                            }
                        }

                        borrowMoreLoanStr = '<div class="borrowItems">' +
                            '<table class="ty-table specialInvoiceGoods">' +
                            '<thead>' +
                            '<tr>' +
                            '<td>货物或应税劳务、服务名称</td>' +
                            '<td>规格型号</td>' +
                            '<td>单位</td>' +
                            '<td>数量</td>' +
                            '<td>单价</td>' +
                            '<td>金额</td>' +
                            '<td>所属发票</td>' +
                            '<td width="324">选择科目</td>' +
                            '</tr>' +
                            '</thead>' +
                            '<tbody>' + specialInvoiceGoodsStr + '</tbody>' +
                            '</table>' +
                            '</div>';
                        var borrow = JSON.parse(debitSubject.borrow)
                        var loan = JSON.parse(debitSubject.loan)
                        var cashjson = JSON.parse(debitSubject.cash)
                        for(var m in borrow){
                            borrowMoreLoanStr +=    '<div class="borrowItem borrow">'+
                                '   <span class="subjectTitle bookIcon">借方科目：</span><input placeholder="请点击选择科目" id="' + borrow[m].subject + '" class="subjectBorrow" type="text" value="' + borrow[m].subjectNames + '" disabled/>'+
                                '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text" value="'+borrow[m].price+'" disabled/></div>'+
                                '</div>';
                        }
                        for(var n in loan) {
                            //拼接贷方字符串
                            borrowMoreLoanStr +=    '<div class="loanItem loan">' +
                                '   <span class="subjectTitle bookIcon">贷方科目：</span><input placeholder="请点击选择科目" id="' + loan[n].subject + '" class="subjectLoan" type="text" value="' + loan[n].subjectNames + '" disabled/>' +
                                '   <div class="m5 mPrice"><span>金额：</span><input class="price" type="text" value="' + loan[n].price + '" disabled/></div>' +
                                '</div>';
                        }
                        for(var l in cashjson) {
                            cashFlowStr  += '<div class="cashFlowGroup special'+l+'a">'+
                                '<div class="normalItem">'+
                                '<span>现金流量：</span>'+
                                '<select class="cash" value="'+cashjson[l].cashitem+'" disabled>'+
                                $(".cashFlowOption").html()+
                                '</select> '+
                                '<span>金额：</span>'+
                                '<input class="amount" type="text" value="'+cashjson[l].cashes+'" disabled/>'+
                                '</div>'+
                                '</div>';
                        }

                    }
                    var state = Number($(".ty-secondTab li.ty-active").attr('state'))
                    if (state) {
                        if (state === 6 || state === 9) {
                            $("#pend_addAccount .special").html(cashFlowStr);
                            $("#pend_addAccount .special .cash").each(function () {
                                $(this).val($(this).attr('value'))
                            });
                            $("#pend_addAccount .borrowMoreLoans").html(borrowMoreLoanStr);
                        }
                    } else {
                        $("#choose_addAccount .special").html(cashFlowStr);
                        $("#choose_addAccount .special .cash").each(function () {
                            $(this).val($(this).attr('value'))
                        });
                        $("#choose_addAccount .borrowMoreLoans").html(borrowMoreLoanStr);
                    }
                }
                $("#accountingDetail .detailed").html('<div class="price hd"></div>');
                getReimburseDetail(content.id)
                return false;
            }
            switch (kind){
                case 2:
                    if (content.type === "1") {
                        var type = "内部转账支票";
                    } else if (content.type === "2") {
                        var type = "现金支票";
                    }
                    accountingDetailStr     =   '<tr><td>项目</td><td>支出</td></tr>' +
                        '<tr><td>票据种类</td><td>' + content.billCat + '</td></tr>' +
                        '<tr><td>票据所属月份</td><td>' + chargeBillPeriod(content.billPeriod) + '</td></tr>' +
                        '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                        '<tr><td>票据数量</td><td>' + content.billQuantity + '</td></tr>' +
                        '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                        '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                        '<tr><td>经手人</td><td class="operatorName">' + content.financialHandling + '</td></tr>' +
                        '<tr><td>支出方式</td><td>' + type + '</td></tr>' +
                        '<tr><td>银行账户</td><td>' + content.bankName + ' ' + content.account + '</td></tr>' +
                        '<tr><td>支票号</td><td>' + content.chequeNo + '</td></tr>' +
                        '<tr><td>支票到期日</td><td>' + content.expireDate.substring(0,10) + '</td></tr>' +
                        '<tr><td>接收日期</td><td>' + content.receiveDate.substring(0,10) + '</td></tr>' +
                        '<tr><td>接收经手人</td><td>' + content.receiver + '</td></tr>' +
                        '<tr><td>支付经手人</td><td>' + content.operator + '</td></tr>' +
                        '<tr><td>收款单位</td><td>' + content.receiveCorp + '</td></tr>' +
                        '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>';
                    break;
                case 3:
                    $(".source").attr("tittle", 3);
                    if (content.billType === "1") {
                        var billType = "收入";
                    } else if (data.content.billType === "2") {
                        var billType = "支出";
                    }
                    //收入中的转账支票
                    if(content.billType === "1"){
                        accountingDetailStr     =   '<tr><td>项目</td><td>收入</td></tr>' +
                            '<tr><td>类别</td><td>' + chargeGenre(content.category) + '</td></tr>' +
                            '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                            '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                            '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                            '<tr><td>经手人</td><td class="operatorName">' + content.operatorName + '</td></tr>' +
                            '<tr><td>收入方式</td><td>' + C_payway(content.type) + '</td></tr>';
                        if(content.type === 1){
                            accountingDetailStr +=  '<tr><td>收到支票日期</td><td>' + content.receiveDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>付款单位</td><td>' + content.payer + '</td></tr>' +
                                '<tr><td>支票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>支票到期日</td><td>' + content.expireDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>出具支票单位</td><td>' + content.originalCorp + '</td></tr>' +
                                '<tr><td>出具支票银行</td><td>' + content.bankName + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }else if(content.type === 2){
                            accountingDetailStr +=  '<tr><td>收到汇票日期</td><td>' + content.receiveDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>付款单位</td><td>' + content.payer + '</td></tr>' +
                                '<tr><td>汇票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>汇票到期日</td><td>' + content.expireDate.substring(0,10) + '</td></tr>' +
                                '<tr><td>原始出具汇票单位</td><td>' + content.originalCorp + '</td></tr>' +
                                '<tr><td>出具汇票银行</td><td>' + content.bankName + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }
                    }
                    //支出中的转账支票
                    else if(content.billType === "2"){
                        accountingDetailStr     =   '<tr><td>项目</td><td>' + billType + '</td></tr>' +
                            '<tr><td>票据种类</td><td>' + content.billCat + '</td></tr>' +
                            '<tr><td>票据所属月份</td><td>' + chargeBillPeriod(content.billPeriod) + '</td></tr>' +
                            '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                            '<tr><td>票据数量</td><td>' + content.billQuantity + '</td></tr>' +
                            '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                            '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                            '<tr><td>经手人</td><td class="operatorName">' + content.operatorName + '</td></tr>' +
                            '<tr><td>支出方式</td><td>' + C_payway(content.type) + '</td></tr>';
                        if(content.type === 1){
                            accountingDetailStr +=  '<tr><td>支票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>收款单位</td><td>' + content.oppositeCorp + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }else if(content.type === 2){
                            accountingDetailStr +=  '<tr><td>汇票号</td><td>' + content.returnNo + '</td></tr>' +
                                '<tr><td>收款单位</td><td>' + content.oppositeCorp + '</td></tr>' +
                                '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>' ;
                        }
                    }
                    break;
                case 4:
                    var method  =   "";             //支出方式
                    var bankStr =   "";             //转账银行
                    if (content.method === "1") {
                        method = "现金";
                    } else if (content.method === "5") {
                        method = "银行转账";
                        bankStr = '<tr><td>转账银行：</td><td>' + content.oppositeAccount + '</td></tr>';
                    }
                    accountingDetailStr     =   '<tr><td>项目   </td><td>支出</td></tr>' +
                        '<tr><td>票据种类</td><td>' + content.billCat + '</td></tr>' +
                        '<tr><td>票据所属月份</td><td>' + chargeBillPeriod(content.billPeriod) + '</td></tr>' +
                        '<tr><td>摘要   </td><td class="summary">' + content.summary + '</td></tr>' +
                        '<tr><td>票据数量</td><td>' + content.billQuantity + '</td></tr>' +
                        '<tr><td>金额   </td><td class="price">' + content.billAmount + '</td></tr>' +
                        '<tr><td>用途   </td><td class="purpose">' + content.purpose + '</td></tr>' +
                        '<tr><td>经手人 </td><td class="operatorName">' + content.operatorName + '</td></tr>' +
                        '<tr><td>支出方式</td><td>' + method + '</td></tr>' + bankStr +
                        '<tr><td>收款单位</td><td>' + content.oppositeCorp + '</td></tr>' +
                        '<tr><td>备注   </td><td class="memo">' + content.memo + '</td></tr>' ;
                    break;
                case 5:
                    $(".source").attr("tittle", 0);
                    var method  =   "";             //收入方式
                    var bankStr =   "";             //收款银行

                    if (content.method === "1") {
                        method  =   "现金";
                        bankStr =   '';
                    } else if (content.method === "5") {
                        method  =   "银行转账";
                        bankStr =   '<tr><td>收款银行</td><td>' + content.accountBank + '</td></tr>' +
                            '<tr><td>到账时间</td><td>' + content.receiveAccountDate.substring(0,10) + '</td></tr>';
                    }

                    accountingDetailStr     =   '<tr><td>项目</td><td>收入</td></tr>' +
                        '<tr><td>类别</td><td>' + chargeGenre(content.genre) + '</td></tr>' +
                        '<tr><td>摘要</td><td class="summary">' + content.summary + '</td></tr>' +
                        '<tr><td>金额</td><td class="price">' + content.billAmount + '</td></tr>' +
                        '<tr><td>用途</td><td class="purpose">' + content.purpose + '</td></tr>' +
                        '<tr><td>经手人</td><td class="operatorName">' + content.auditorName + '</td></tr>' +
                        '<tr><td>收入方式</td><td>' + method + '</td></tr>' +
                        '<tr><td>付款单位</td><td>' + content.oppositeCorp + '</td></tr>' + bankStr +
                        '<tr><td>备注</td><td class="memo">' + content.memo + '</td></tr>';
                    break;
                default:
                    accountingDetailStr     =   '';
            }
            $("#accountingDetail .detailed").html('<table class="ty-table"><body>' + accountingDetailStr + '</body></table>');

        }
    });
}
// creator: 张旭博，2019-05-24 09:28:12，获取报销详情
function getReimburseDetail(reimburseId) {
    $.ajax({
        url:"../reimburseWindow/getReimburseInfo.do" ,
        data: {reimburseId : reimburseId },
        success:function (data) {
            var personnelReimburse = data.personnelReimburse
            var billList = data.billList

            var billStr = ''


            $("#reimburse .summary").html(personnelReimburse.summary)
            $("#reimburse .purpose").html(personnelReimburse.purpose)
            $("#accountingDetail .price").html(personnelReimburse.amount)

            var totalQuantity = 0
            var totalBillAmount = 0
            var totalAmount = 0
            for (var j in billList) {
                billStr +=  '<tr id="'+billList[j].id+'">' +
                    '    <td>' + billList[j].billCatName + '</td>' +
                    '    <td>' + (billList[j].billCatName === '定额普通发票'? '--' : billList[j].itemAmount) + '</td>' +
                    '    <td>' + billList[j].relativeBillQuantity + '</td>' +
                    '    <td>' + billList[j].billAmount + '</td>' +
                    '    <td>' + billList[j].amount + '</td>' +
                    '    <td>' +
                    '        <span class="ty-color-blue" data-name="billInfo" type="btn">票据内容</span>' +
                    '        <span class="ty-color-blue" data-name="billPic" type="btn">票据图片</span>' +
                    '    </td>' +
                    '</tr>'
                totalQuantity = -(-totalQuantity - billList[j].relativeBillQuantity)
                totalBillAmount = -(-totalBillAmount - billList[j].billAmount)
                totalAmount = -(-totalAmount - billList[j].amount)
            }
            if (billList.length > 1) {
                billStr +=  '<tr>' +
                    '    <td></td>' +
                    '    <td></td>' +
                    '    <td>' + totalQuantity + '</td>' +
                    '    <td>' + totalBillAmount.toFixed(2) + '</td>' +
                    '    <td>' + totalAmount.toFixed(2) + '</td>' +
                    '    <td></td>' +
                    '</tr>'
            }

            $("#reimburse .billDetail tbody").html(billStr)
        }
    })
}
// 判别转账方式
function chargeMethod( val ){
    switch( Number(val) ){
        case 1 : return "现金" ; break ;
        case 2 : return "现金支票" ; break ;
        case 3 : return "转账支票" ; break ;
        case 4 : return "承兑汇票" ; break ;
        case 5 : return "银行转账" ; break ;
        case 6 : return "存现金" ; break ;
        case 7 : return "取现金" ; break ;
        case 8 : return "其他内部转" ; break ;
        default : return "";
    }
}
// 判别 费用类别
function chargeGenre( val ){
    switch( Number(val) ){
        case 1 : return "贷款" ; break ;
        case 2 : return "借款" ; break ;
        case 3 : return "投资款" ; break ;
        case 4 : return "废品" ; break ;
        case 5 : return "其他" ; break ;
        default : return "";
    }
}


