/**
 * Created by Administrator on 2017/5/27.
 */

$(function(){
    //设置账簿
    //var searchDate = localStorage.getItem("searchDate");
    //$("#searchDate").val(searchDate);
    setAllSubject($(".ty-colFileTree .treeBox"));
    //菜单切换
    $(".ty-firstTab li").on("click",function () {
        $(".ty-firstTab li").removeClass("ty-active");
        $(this).addClass("ty-active");
        $(".ty-treeItemActive").click();
    });
    /* creator：张旭博，2017-05-06 15:16:54，每一集目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容） */
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");

        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right") ;
            $(this).find("i").eq(0).addClass("fa-angle-down")
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down") ;
            $(this).find("i").eq(0).addClass("fa-angle-right")
        }
        //点击文件夹后子列表的显隐
        $(this).next().toggle();
        //获得右侧信息
        var quantityAssistingAccounting = $(this).attr("cn");
        getSubjectDetail($(this),quantityAssistingAccounting);
    });
});
/* creator：张旭博，2017-08-07 16:35:36，设置账簿 */
function setAllSubject(selector) {
    $.ajax({
        url: "getAllSubjecs.do",
        data: {},
        type: "post",
        dataType: "json",
        beforeSend:function () { loading.open();  },
        success: function (data) {
            if(data["code"] === 1){
                getAllSubjectStr(data,selector);
            }
            $(".ty-colFileTree .ty-treeItem").eq(0).click();
        },
        error:function (err) {
        },
        complete:function(){ loading.close(); }
    });
}
/* creator：张旭博，2017-08-07 16:35:58，获取每个科目详细内容（右侧数据） */
function getSubjectDetail(selector , quantityAssistingAccounting,searchDate ){
    var subjectId = selector.attr("id");
    var subjectName = selector.text().split(" ")[1];
    var level1 = selector.parents(".level2").prev().text().split(" ")[1];
    var level2 = selector.parents(".level3").prev().text().split(" ")[1];
    //var searchDate = $("#searchDate").val();
    var searchDate = $(".mainCon7 .bookDate").data("month");
    if(level2 !== undefined){
        //这个是三级科目
        subjectName = level1 + ' - ' +  level2 + ' - ' + subjectName;
    }else if(level1 !== undefined){
        //这个是二级科目
        subjectName = level1 + ' - ' + subjectName;
    }
    var flag = $(".ty-firstTab .ty-active").attr("flag");//获取flag 0：总账 1：明细账
    $(".subjectInfo").html("项目名称："+subjectName);   //设置科目名称-科目编号
    var data = {
        "subject":subjectId,
        "flag":flag
    };
    if(searchDate !== ""){
        data["queryTime"] = searchDate + '-01';
    }
    $.ajax({
        url:"getSubjectLedger.do" ,//得到所有科目
        data: data ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            if(data["code"] === 1){
                //获取科目详细信息数据
                var subjectDetailData = data["data"];

                //拼接科目详细信息字符串
                var subjectTheadStr  = '';
                var subjectTbodyStr  = '';
                if (quantityAssistingAccounting === "1" && flag === "1") {
                    subjectTheadStr =   '<tr>' +
                                            '<td rowspan="2">凭证日期</td>' +
                                            '<td rowspan="2">摘要</td>' +
                                            '<td colspan="2">借方</td>' +
                                            '<td colspan="2">贷方</td>' +
                                            '<td rowspan="2">方向</td>' +
                                            '<td colspan="3">余额</td>'+
                                        '</tr>'+
                                        '<tr>' +
                                            '<td>数量</td>' +
                                            '<td>金额</td>' +
                                            '<td>数量</td>' +
                                            '<td>金额</td>' +
                                            '<td>单价</td>' +
                                            '<td>数量</td>'+
                                            '<td>金额</td>'+
                                        '</tr>';
                    for(var j in subjectDetailData){
                        subjectTbodyStr +=  '<tr>'+
                                            '   <td onclick="'+subjectDetailData[j].createDate+'">'+subjectDetailData[j].period+'</td>' +
                                            '   <td>'+subjectDetailData[j].summary+'</td>' +
                                            '   <td>'+chargeUndefined(subjectDetailData[j].creditQuantity)+'</td>' +
                                            '   <td>'+chargeUndefined(subjectDetailData[j].credit)+'</td>' +
                                            '   <td>'+chargeUndefined(subjectDetailData[j].debitQuantity)+'</td>' +
                                            '   <td>'+chargeUndefined(subjectDetailData[j].debit)+'</td>' +
                                            '   <td>'+changeRules(subjectDetailData[j].balanceDirection,"balanceDirection")+'</td>' +
                                            '   <td>'+chargeUndefined(subjectDetailData[j].unitPrice)+'</td>'+
                                            '   <td>'+chargeUndefined(subjectDetailData[j].quantity)+'</td>'+
                                            '   <td>'+chargeUndefined(subjectDetailData[j].balance)+'</td>'+
                                            '</tr>';
                    }
                }else{
                    subjectTheadStr =   '<tr>' +
                                            '<td>凭证日期</td>' +
                                            '<td>摘要</td>' +
                                            '<td>借方金额</td>' +
                                            '<td>贷方金额</td>' +
                                            '<td>方向</td>' +
                                            '<td>余额</td>'+
                                        '</tr>';
                    for(var i in subjectDetailData){
                        subjectTbodyStr += '<tr>'+
                                            '   <td onclick="'+subjectDetailData[i].createDate+'">'+subjectDetailData[i].period+'</td>' +
                                            '   <td>'+subjectDetailData[i].summary+'</td>' +
                                            '   <td>'+chargeUndefined(subjectDetailData[i].credit)+'</td>' +
                                            '   <td>'+chargeUndefined(subjectDetailData[i].debit)+'</td>' +
                                            '   <td>'+changeRules(subjectDetailData[i].balanceDirection,"balanceDirection")+'</td>' +
                                            '   <td>'+chargeUndefined(subjectDetailData[i].balance)+'</td>'+
                                            '</tr>';
                    }

                }
                $(".subjectDetail thead").html(subjectTheadStr);
                $(".subjectDetail tbody").html(subjectTbodyStr);
            }else if(data["code"] === 0){
                alert(data["msg"]);
            }else{
                alert("返回值错误！")
            }
        } ,
        error:function () {
            alert("系统错误，请重试！")
        },
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    });

}
// creator:侯杏哲 2017-10-26 点击放大镜搜索
function searchB(){
    localStorage.setItem("searchDate",$("#searchDate").val());
    $(".ty-treeItemActive").click();
}
//---------- 辅助方法 ----------//

// 改变方向（int -> string)
/*function changeRules(val,type) {
    val = parseInt(val);
    if(type === "balanceDirection"){
        switch(val){
            case 1:
                return "借";
                break;
            case 2:
                return "贷";
                break;
            case 3:
                return "平";
                break;
            default:
                return false;
        }
    }
}*/
// 非空方法
function chargeUndefined( val ){
    if(val === undefined || val === null || val == "0"){
        return "0.00";
    }
    return val ;
}

laydate.render({
    elem: '#searchDate',
    type: "month",
    showBottom:false,
    ready:function(date){
            $("#layui-laydate1").off('click').on('click','.laydate-month-list li',function(){
                $("#layui-laydate1").remove();
            });
        },
    change:function(value,dates,edate){
        $('#searchDate').val(value);
    }
}) ;
