
$(function(){
    getVouchermes();

});
function testValue(val) {
    if(val==undefined||val=="undefined"||val==null||val=="null"){
        val="";

        return val;
    }else {
        return val;
    }
}
function getVouchermes() {
    $.ajax({
        url:"../accountant/getVoucherManager.do",
        data:{},
        beforeSend:function(){
            loading.open()
        },
        type:"post",
        dataType:"json",
        success:function(data){
            console.log(data)
            var arrVoucherManager = data["arrVoucherManager"];
            var status = data["status"];
            if (status = 0 || status == "0") {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("操作失败！");
                return false;
            }
            $("#set_body").html("") ;
            var str = "" ;
            if(arrVoucherManager !=undefined && arrVoucherManager.length > 0 ){
                for(var i=0 ; i<arrVoucherManager.length ; i++ ){
                    var listBorrow  = arrVoucherManager[i]["listBorrow"];
                    var listLoan  = arrVoucherManager[i]["listLoan"];
                    var borrowStr = '';
                    var loanStr = '';
                    var borrowPrice = '';
                    var loanPrice = '';
                    for(var j = 0; j<listBorrow.length;j++){
                        borrowStr += '<div>'+listBorrow[j].subjectName+'</div>';
                        borrowPrice += '<div>'+listBorrow[j].amount+'</div>';
                        loanPrice += '<div></div>'
                    }
                    for(var k = 0; k<listLoan.length;k++){
                        borrowStr += '<div>'+listLoan[k].subjectName+'</div>';
                        borrowPrice += '<div></div>';
                        loanPrice += '<div>'+listLoan[k].amount+'</div>'
                    }

                    /* var str = "<div class='tr'>"+
                        "<div class='fl ttl  wt_left'>"+"<span type='checkbox' name='chkItem' class='check_false'onclick='voucherBtn($(this))'></span>"+"</div>"+
                        "<div class='fl ttl_two'>"+testValue(arrVoucherManager[i]["create_date"])+"</div>"+
                        "<div  class='manage_conon fl ttl_112 control_font'>"+"<div>"+testValue(arrVoucherManager[i]["summary"])+"</div>"+"<div>"+testValue(arrVoucherManager[i]["summary"])+"</div>"+"</div>"+
                        "<div  class='manage_conon fl ttl_112 control_font' '>"+"<div>"+testValue(arrVoucherManager[i]["borrowSubject"])+" "+testValue(arrVoucherManager[i]["borrowName"])+"</div>"+"<div>"+testValue(arrVoucherManager[i]["loanSubject"])+" "+testValue(arrVoucherManager[i]["loanName"])+"</div>"+"</div>"+
                        "<div  class='manage_conon fl ttl_1 control_font'>"+"<div>"+testValue(arrVoucherManager[i]["amount_borrow"])+"</div>"+"<div>"+testValue(arrVoucherManager[i][""])+"</div>"+"</div>"+
                        "<div  class='manage_conon fl ttl_1 control_font'>"+"<div>"+testValue(arrVoucherManager[i][""])+"</div>"+"<div>"+testValue(arrVoucherManager[i]["amount_loan"])+"</div>"+"</div>"+
                        "<div class='fl ttl_1 control_font'>"+testValue(  arrVoucherManager[i]["operator_finance"] ) +"</div>"+
                        "<div class='fl ttl_1 control_font'>"+ testValue(  arrVoucherManager[i]["operator_name"] ) +"</div>"+
                        "<div class='ttl_control wt_right'>"+ testValue(  arrVoucherManager[i]["update_name"] ) +"</div>"+
                        "</div>"; */

                    str += "<tr>" +
                        "<td><span type='checkbox' name='chkItem' class='check_false'onclick='voucherBtn($(this))'></span></td>"  +
                        "<td>" + testValue( arrVoucherManager[i]["create_date"])+"</td>" +
                        "<td>" + testValue( arrVoucherManager[i]["summary"])+"</td>" +
                        "<td>" + testValue( borrowStr)+testValue(loanStr)+"</div></td>" +
                        "<td>" + testValue( borrowPrice)+"</td>" +
                        "<td>" + testValue( loanPrice)	+"</td>" +
                        "<td>" + testValue( arrVoucherManager[i]["operator_finance"] ) +"</td>" +
                        "<td>" + testValue( arrVoucherManager[i]["operator_name"] ) +"</td>" +
                        "<td>" + testValue( arrVoucherManager[i]["update_name"] ) +"</td>" +
                        "</tr>" ;
                }
            }
            $("#set_body").append(str) ;
        },
        error:function (meg) {
            loading.close();
            // console.log("连接错误，请稍后重试！");
            bounce.show($("#mtTip"));
            $("#mt_tip_ms").html("连接错误，请稍后重试！");
            $("#set_body").html("");
        }
    }).always(function(){
        loading.close();
    })
}

setInterval( function(){ test(); } , 100) ;
function test(){
    var scrollBarWidth = getScrollBarWidth();
    var vertical = scrollBarWidth["vertical"];
    $(".tr").attr("style" , "margin-right:-" + vertical + "px") ;
}
// 获得系统默认浏览器的宽度
function getScrollBarWidth() {
    var __scrollBarWidth = null;
    if (__scrollBarWidth) return __scrollBarWidth;
    var scrollBarHelper = document.getElementById("set_body");
    if (scrollBarHelper) {
        __scrollBarWidth = {
            horizontal: scrollBarHelper.offsetHeight - scrollBarHelper.clientHeight,
            vertical: scrollBarHelper.offsetWidth - scrollBarHelper.clientWidth
        };
    }
    return __scrollBarWidth;
}
//点击多选框选中
function voucherBtn(obj , num) {
    var val = obj.attr("class") ;
    var checkNum = 0 ;
    var trObj = null ;
    var trObj_all = null;
    if( val == "check_false" || val == "check_true" ){
        trObj = obj.parent().parent().parent().children("tr");
        trObj_all = obj.parent().parent().parent().children();
    }else if( val == "check_all_false" || val == "check_all_true" ){
        trObj = obj.parent().parent().parent().next().children("tr");
        trObj_all = obj.parent().parent().siblings().children();
    }
    checkNum = trObj_all.find(".check_true").length ;
    switch (val){
        case "check_false" :
            obj.attr( "class" , "check_true" );
            checkNum++ ;
            if( checkNum == trObj_all.length ){ $("#checkList").attr( "class" , "check_all_true" );  }
            break ;
        case "check_true" :
            obj.attr( "class" , "check_false" );
            checkNum-- ;
            $("#checkList").attr("class" , "check_all_false");
            break ;
        case "check_all_false" :
            obj.attr( "class" , "check_all_true" );
            var trObj = obj.parent().parent().parent().siblings().children("tr");
            trObj.each(function(){
                $(this).find(".check_false").attr( "class" , "check_true" );
            });
            break ;
        case "check_all_true" :
            obj.attr( "class" , "check_all_false" );
            var trObj = obj.parent().parent().parent().siblings().children("tr");
            trObj.each(function(){
                $(this).find(".check_true").attr( "class" , "check_false" );
            });
            break ;
        default:
            obj.attr( "class" , "check_false" );
    }

}
//关闭提示
function bounce_cancel() {
    bounce.cancel();
}
function bounce_close() {
    bounce.cancel();
}

// creator : 侯杏哲 2018-03-21   打印的工具方法
function printVouncher() {
    preview(1 , "") ;
}
// creator : 侯杏哲 2018-03-21   打印的工具方法
function preview(oper,actionurl) {
    if (oper < 10){
        bdhtml=window.document.body.innerHTML;//获取当前页的html代码
        sprnstr="<!--startprint"+oper+"-->";//设置打印开始区域
        eprnstr="<!--endprint"+oper+"-->";//设置打印结束区域
        prnhtml=bdhtml.substring(bdhtml.indexOf(sprnstr)+18); //从开始代码向后取html
        prnhtml=prnhtml.substring(0,prnhtml.indexOf(eprnstr));//从结束代码向前取html
        window.document.body.innerHTML=prnhtml;
        location.href=actionurl;
        window.print();
        //window.document.body.innerHTML=bdhtml;
    } else{
        window.print();
    }
}

