
// creator: 李玉婷，2022-04-27 10:41:52，科目变动记录
let pageSize = 20;
function getSubChangeRecord(cur, type) {
    let curObj = $(".mainCon3"), info = ``;
    let param = {
        "pageSize": pageSize,
        "currentPageNo": cur
    };
    if (type === 2) {
        curObj = $(".mainCon2");
        info = JSON.parse($("#changeContent_ye").data("obj").parents("tr").find(".hd").html());
        param.establish = info.id;
    }
    curObj.find("table tbody tr:gt(1)").remove();
    $.ajax({
        url:"../accountant/getSubjectJournal.do" ,
        data: param,
        success:function ( res ) {
            let html = ``;
            let list = res.data.list;
            //分页
            let pageInfo = res.data.pageInfo;
            var totalPage = pageInfo["totalPage"];
            var jsonStr = JSON.stringify({"type":type}) ;
            setPage( $("#changeContent_ye") , cur ,  totalPage , "subChangeContent" ,jsonStr );
            if (list && list.length > 0) {
                for(var i=0;i<list.length;i++){
                    html +=
                        `<tr>
                            <td>${new Date(list[i].changeDate).format("yyyy-MM-dd")}</td>
                            <td>${list[i].subject1}</td>
                            <td>${list[i].subject2}</td>
                            <td>${list[i].subject3}</td>
                            <td>${list[i].subject4}</td>
                            <td><span class="funbtn ty-color-blue" data-fun="changeContent" data-type="1">查看</span></td>
                            <td>
                            <span class="funbtn ty-color-blue" data-fun="changeContent" data-type="2">查看</span>
                            <span class="hd">${JSON.stringify(list[i])}</span>
                            </td>
                        </tr>`;
                }
                curObj.find("table tbody").append(html);
            }
        }
    })
}
// creator: 李玉婷，2022-04-27 11:29:21，变动内容
function changeContentScan(param) {
    $.ajax({
        url:"../accountant/getSubjectRecords.do" ,
        data: param,
        success:function ( res ) {
            let html = ``;
            let list = res.data;
            if (list && list.length > 0) {
                let oper = ``, detail = ``;
                for(var i=0;i<list.length;i++){
                    oper = ``, detail = ``;
                    //操作:1-增,2-删,3-改,4-启用,5-停用,6-建账,7-重新建账
                    if (list[i].operation === "1") {
                        oper = `增加科目`;
                        detail = `增加的科目为“${list[i].name}+${list[i].subject}”`;
                    } else if (list[i].operation === "3") {
                        oper = `修改科目`;
                        detail = `<span class="funbtn ty-color-blue" data-fun="beforeUpdate" data-id="${list[i].previousId}">修改前</span>
                            <span class="funbtn ty-color-blue" data-fun="afterUpdate" data-id="${list[i].id}">修改后</span>`;
                    } else if (list[i].operation === "4") {
                        oper = `启用科目`;
                        detail = `启用的科目为“${list[i].name}+${list[i].subject}”`;
                    } else if (list[i].operation === "5") {
                        oper = `禁用科目`;
                        detail = `禁用的科目为“${list[i].name}+${list[i].subject}”`;
                    } else if (list[i].operation === "6") {
                        oper = `建帐`;
                        detail = `<span class="funbtn ty-color-blue" data-fun="afterAccount">建账后的科目</span>
                                    <span class="hd">${JSON.stringify(list[i])}</span>`;
                    } else if (list[i].operation === "7") {
                        oper = `重新建帐`;
                        detail = `<span class="funbtn ty-color-blue" data-fun="afterAccount">重新建账后的科目</span>
                                  <span class="hd">${JSON.stringify(list[i])}</span>`;
                    }
                    html +=
                        `<tr>
                            <td>${oper}</td>
                            <td>${list[i].createName} ${new Date(list[i].createDate).format("hh:mm:ss")}</td>
                            <td>${detail}</td>
                        </tr>`;
                }
            }
            $("#accountChangeRecord tbody").append(html);
        }
    })
}
// creator: 李玉婷，2022-04-28 20:22:48，某天的所有科目或建账后的科目
function afterSubjectChange(type, obj) {
    let info = JSON.parse(obj.siblings(".hd").html());
    let param = {
        "category": 1,
        "isChanged": type,
        "establish": info.establish
    }
    if (type === 1) {
        param.changeDate = new Date(info.changeDate).format("yyyy-MM-dd");
    } else {
        bounce.cancel();
    }
    $("#subChangeParam").html(JSON.stringify(param));
    $("#subjectScan li:eq(0)").click();
}
// creator: 李玉婷，2022-05-12 15:00:36，变动后的科目列表切换展示不同数据
function getChangeSubjects(param) {
    $.ajax({
        url:"../accountant/getChangeSubjects.do" ,
        data: param,
        success:function ( res ) {
            let list = res.data || [];
            let str=``;
            for(var i=0;i<list.length;i++){
                var subject=list[i].subject;//科目编号
                var name=list[i].name;//科目名称
                var indent=(list[i].level-1)*2+"em";
                var categoryname=list[i].categoryname;//类别名称
                var balance_direction="借";//余额方向
                if(list[i].balanceDirection==2){
                    balance_direction="贷"
                }

                var state=list[i].state;//状态
                if(accountantAuth.controlSubject){
                    if(state){
                        state="<span class='ty-color-blue'>已启用</span>";
                    }else{
                        state="<span class='ty-color-gray'>已禁用</span>";
                    }
                }else{
                    if(state){
                        state="<span class='ty-color-blue'>已启用</span>";
                    }else{
                        state="<span class='ty-color-gray'>已禁用</span>";
                    }
                }

                str+="<tr>" +
                    "<td width='25%' class='fl'>"+subject+"</td>" +
                    "<td width='25%' class='fl fl1' style=' text-indent:"+indent+";'title='"+name+"'>"+name+"</td>" +
                    "<td width='20%' class='fl'>"+categoryname+"</td>" +
                    "<td width='15%' class='fl'>"+balance_direction+"</td>" +
                    "<td width='15%' class='fl ty-td-control'>"+state+"</td>"+
                    "</tr>"
            }
            $("#subChange").html(str);
        }
    })
}

// creator: 李玉婷，2022-05-05 14:38:52，变动内容 - 修改前  修改后
function subjectUpdateScan(type, obj){
    let param = {}, title = ``;
    if (type === 1) {
        title = `修改前`;
        param.previousId = obj.data("id");
    } else {
        title = `修改后`;
        param.id = obj.data("id");
    }
    $("#subject_updateScan .bonceHead span").html(title);
    $.ajax({
        url:"../accountant/getSubjectModifyInfo.do" ,
        data: param,
        success:function ( res ) {
            let key = ``;
            let data = res.data;
            $(".su_pack .con_scan").each(function(){
                key = $(this).data("name");
                if (key === 'balanceDirection') {
                    $(this).html(transformLoan(data[key]));
                } else {
                    $(this).html(data[key]);
                }
            });
            if (data.quantityAssistingAccounting === 1) {
                $("#subject_updateScan .ty-form-checkbox").addClass("ty-form-checked");
            } else {
                $("#subject_updateScan .ty-form-checkbox").removeClass("ty-form-checked");
            }
            bounce_Fixed.show($("#subject_updateScan"));
        }
    })
}

//creator: lyt date: 2018/7/2 输出借、贷
function transformLoan(str){
    if(str == "1"){
        return "借";
    }else if(str == "2"){
        return "贷";
    }
}