/* creator：张旭博，2017-05-06 15:17:36，获取所有的科目目录字符串（data:科目数据） */
function getAllSubjectStr(data,selector) {
    var subjectList = data["list"];
    var firstLevelStr = "";
    firstLevelStr += '<div class="level1" level="1">';
    for(var i=0; i<subjectList.length;i++){
        if(subjectList[i].level == 1){
            if(i === subjectList.length - 1) {
                firstLevelStr += '<li><div class="ty-treeItem" cn="'+subjectList[i].quantityAssistingAccounting+'" title="'+subjectList[i].measureUnit+'" id='+subjectList[i].subject+'><i class="ty-fa"></i><i class="fa fa-book"></i><span>'+subjectList[i].subject+" "+subjectList[i].name+'</span></div></li>'
            }else {
                if(subjectList[i+1].level > 1){
                    firstLevelStr += '<li><div class="ty-treeItem" cn="'+subjectList[i].quantityAssistingAccounting+'" title="'+subjectList[i].measureUnit+'" id='+subjectList[i].subject+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+subjectList[i].subject+" "+subjectList[i].name+'</span></div><div class="level2" level="2" style="display: none"></div></li>'
                }else{
                    firstLevelStr += '<li><div class="ty-treeItem" cn="'+subjectList[i].quantityAssistingAccounting+'" title="'+subjectList[i].measureUnit+'" id='+subjectList[i].subject+'><i class="ty-fa"></i><i class="fa fa-book"></i><span>'+subjectList[i].subject+" "+subjectList[i].name+'</span></div></li>'
                }
            }
        }
    }
    firstLevelStr += '</div>';
    selector.html(firstLevelStr);
    var secondLevelStr = "";
    for(var j=0; j<subjectList.length;j++){
        if(subjectList[j].level === 2){
            if(j === subjectList.length - 1){
                secondLevelStr = '<li><div class="ty-treeItem" cn="'+subjectList[j].quantityAssistingAccounting+'" title="'+subjectList[j].measureUnit+'" id='+subjectList[j].subject+'><i class="ty-fa"></i><i class="fa fa-book"></i><span>'+subjectList[j].subject+" "+subjectList[j].name+'</span></div></li>'
            }else{
                if(subjectList[j+1].level > 2){
                    secondLevelStr = '<li><div class="ty-treeItem" cn="'+subjectList[j].quantityAssistingAccounting+'" title="'+subjectList[j].measureUnit+'" id='+subjectList[j].subject+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+subjectList[j].subject+" "+subjectList[j].name+'</span></div><div class="level3" level="3" style="display: none"></div></li>'
                }else{
                    secondLevelStr = '<li><div class="ty-treeItem" cn="'+subjectList[j].quantityAssistingAccounting+'" title="'+subjectList[j].measureUnit+'" id='+subjectList[j].subject+'><i class="ty-fa"></i><i class="fa fa-book"></i><span>'+subjectList[j].subject+" "+subjectList[j].name+'</span></div></li>'
                }
            }
            $("#"+subjectList[j].parent+"+.level2").append(secondLevelStr);
        }
    }
    var thirdLevelStr = "";
    for(var k=0; k<subjectList.length;k++){
        if(subjectList[k].level === 3){
            if(k === subjectList.length - 1){
                thirdLevelStr = '<li><div class="ty-treeItem" cn="'+subjectList[k].quantityAssistingAccounting+'" title="'+subjectList[k].measureUnit+'" id='+subjectList[k].subject+'><i class="ty-fa"></i><i class="fa fa-book"></i><span>'+subjectList[k].subject+" "+subjectList[k].name+'</span></div></li>'
            }else{
                if(subjectList[k+1].level > 3){
                    thirdLevelStr = '<li><div class="ty-treeItem" cn="'+subjectList[k].quantityAssistingAccounting+'" title="'+subjectList[k].measureUnit+'" id='+subjectList[k].subject+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+subjectList[k].subject+" "+subjectList[k].name+'</span></div><div class="level4" level="4" style="display: none"></div></li>'
                }else{
                    thirdLevelStr = '<li><div class="ty-treeItem" cn="'+subjectList[k].quantityAssistingAccounting+'" title="'+subjectList[k].measureUnit+'" id='+subjectList[k].subject+'><i class="ty-fa"></i><i class="fa fa-book"></i><span>'+subjectList[k].subject+" "+subjectList[k].name+'</span></div></li>'
                }
            }
            $("#"+subjectList[k].parent+"+.level3").append(thirdLevelStr);
        }
    }
    var fourthLevelStr = "";
    for(var l=0; l<subjectList.length;l++){
        if(subjectList[l].level === 4){
            fourthLevelStr = '<li><div class="ty-treeItem" cn="'+subjectList[l].quantityAssistingAccounting+'" title="'+subjectList[l].measureUnit+'" id='+subjectList[l].subject+'><i class="ty-fa"></i><i class="fa fa-book"></i><span>'+subjectList[l].subject+" "+subjectList[l].name+'</span></div></li>';
            $("#"+subjectList[l].parent+"+.level4").append(fourthLevelStr);
        }
    }
    bounce.resize(selector.parents(".bonceContainer"))
}

function setPartSubjects(category) {
    $.ajax({
        url: "getChoseSubject.do",
        data: { "category" : category },
        type: "post",
        dataType: "json",
        success: function (data) {
            if(data["status"] === 1){
                getAllSubjectStr(data,$(".ty-colFileTree"));
            }
        },
        error:function (err) {

        } ,
        beforeSend:function(){ loading.open(); },
        complete:function () {  loading.close();  }
    });
}