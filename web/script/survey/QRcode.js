/**
 * Created by hxz on 2021-3-8
 */
let alreadyID = "";
$(function () {

    // 获取当前二维码信息
    getCurQRInfo();
    //放大二维码
    $("#qrCode_small").on("click",function () {
        if($(this).html() == "暂无二维码"){
            return false;
        }
        bounce.show($("#QR_code"));
    });
    $(".ty-btn").click(function () {
        var fun = $(this).data("fun");
        switch (fun){
            case "createQRcode":
                bounce.show($("#createQRcode"));
                $("#endDate").val("");
                if(alreadyID){
                    $("#createTip").html('操作成功后，页面上原有的二维不再展示。<br>请选择二维码有效期的到期日')
                }else{
                    $("#createTip").html('请选择二维码有效期的到期日')
                }
                break;
            case "createQRcodeOK":
                createQRCodeUrl();
                break;
        }
    })

});

// creator:hxz，2021-3-9 08:44:53，获取当前二维码信息
function getCurQRInfo() {
    $.ajax({
        url : "../survey/selectSurveySubject.do" ,
        success:function( res ){
            let id = res.id;
            if(id){
                alreadyID = id;
                let endTime = res.endTime;
                let createDate = res.createDate;
                $("#validityDate").show().html(`有效期至${new Date(endTime).format("yyyy年MM月dd日")}`);
                $("#create").show().html(`生成时间：${new Date(createDate).format("yyyy-MM-dd hh:mm:ss")}`);
                getQRCodeUrl(id, endTime)
            }else{
                $("#create").hide();
                $("#validityDate").hide();
                $("#qrCode_small").html("暂无二维码");
            }
        }
    })
}
// creator:hxz，2021-3-9 08:44:53 获取二维码信息
function getQRCodeUrl(id, endTime) {
    let time = new Date(endTime).getTime() + 24*60*60*1000 ;
    $.ajax({
        url : "../survey/getQRLink.do" ,
        data:{ "expireDate": time, "id": id },
        success:function( res ){
            $("#qrCode_small").html("")
            bounce.cancel();
            let url = res.data;
            $("#qrCode_small").data("url","url");
            renderQRcode({ "obj":$("#qrCode_small"), "width": 226, "height":226, "url":url  });
            renderQRcode({ "obj":$("#qrCode"), "width": 400, "height":400, "url":url });
        }
    })
}
// creator:hxz，2021-3-9 08:44:53，创建二维码
function createQRCodeUrl() {
    let d = $("#endDate").val();
    if(d.length == 0){
        layer.msg("请录入有效期的到期日");
        return false;
    }
    $.ajax({
        url : "../survey/addSurveySubject.do" ,
        data:{ "expireDate": new Date(d).getTime() },
        success:function( res ){
            $("#qrCode_small").html("")
            bounce.cancel();
            let id = res.id;
            let endTime = res.endTime;
            let createDate = res.createDate;
            $("#validityDate").show().html(`有效期至${new Date(endTime).format("yyyy年MM月dd日")}`);
            $("#create").show().html(`生成时间：${new Date(createDate).format("yyyy-MM-dd hh:mm:ss")}`);
            if(id){
                getQRCodeUrl(id, endTime)
                if(alreadyID){
                    layer.msg('操作成功！<br>新二维码已经可以使用了！', { time: 3000 });
                }else{
                    layer.msg('操作成功！<br>所生成的二维码已经可以使用了！', { time: 3000 });
                }
            }else{
                $("#qrCode_small").html("暂无二维码");
            }
        }
    })
}
// create : 工具方法，渲染二维码
function renderQRcode(data){
    let obj = data.obj , wid = data.width, url = data.url, hei = data.height;
    obj.html("")
    obj.qrcode(
        {
            render : "canvas",    //设置渲染方式，有table和canvas，使用canvas方式渲染性能相对来说比较好
            text : url,    //扫描二维码后显示的内容,可以直接填一个网址，扫描二维码后自动跳向该链接
            width : wid,            // //二维码的宽度
            height : hei,              //二维码的高度
            background : "#ffffff",       //二维码的后景色
            foreground : "#333333"        //二维码的前景色
        }
    );
}
laydate.render({elem: '#endDate' });


