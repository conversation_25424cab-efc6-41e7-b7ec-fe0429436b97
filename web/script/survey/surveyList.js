$(function () {
    getList(1);
    // 页面中的按钮
    $(".ty-btn").click(function () {
        let fun = $(this).data("fun");
        switch (fun){
            case "back":
                showCon(1);
                break;
            default :
        }
    });
    // 表格中的按钮事件
    $("table").on("click", ".funBtn", function () {
        let fun = $(this).data("fun");
        switch (fun){
            case "scan":
                scan($(this));
                break;
            default:
        }
    })
});
// create :hxz 2021-3-9 答卷列表
function getList(cur) {
    $.ajax({
        "url":"../survey/surveyAnswerList.do",
        "data":{ "pageSize": 20, "currentPageNo": cur },
        success:function (res) {
            let pageInfo = res.pageInfo;
            let sum = pageInfo.totalPage;
            let list = res.surveyObjectList || [] , str=``;
            list.forEach(function (item) {
                str += `<tr>
                   <td>${item.corpName}</td>
                   <td>${item.contact}</td>
                   <td>${item.contactWay}</td>
                   <td>${new Date(item. createDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                   <td>
                       <span class="ty-color-blue funBtn" data-fun="scan">查看</span>
                       <span class="hd">${ JSON.stringify(item) }</span>
                   </td>
               </tr>`
            });
            $(".mainCon1 table tbody tr:gt(0)").remove();
            $(".mainCon1 table").append(str);
            setPage($("#yeCon1"),cur , sum, "surveyList");
            showCon(1)
        }
    })

}
// create :hxz 2021-3-9 查看答卷详情
function scan(thisObj) {
    let info = JSON.parse( thisObj.siblings(".hd").html() );
    $.ajax({
        "url":"../survey/surveyAnswerDetails.do",
        "data":{ "id": info.id },
        success:function (res) {
            showCon(2);
            let title = res.name;
            $(".surveyForm h3").html(title);
            let endTime = res.endTime;
            let list = res.respSurveySubjectTagList || [] , str=`` , count = 1;
            list.forEach(function (item,index) {
                let catName = item.name;
                let ques = item.respSurveyQuestionList ;
                str +=`<div class="survey">`
                        /*<h4>${UpNum(index)}、 ${catName}</h4>*/
                ques.forEach(function (qs,qindex) {
                    let ans = qs.surveyAnswerList;
                    str += `<div class="surveyItem">
                                <div class="titleCon">${qs.code || '&nbsp;&nbsp;&nbsp;&nbsp;'} ${qs.content}：</div> `;
                    ans.forEach(function (as,aindex) {
                        str += `<div class="textCon"> ${as.content || ""}</div> `
                    });
                    str += `</div>`;
                });
                str += `</div>`;
            });
            $(".surveyCon").html(str);
        }
    })

}
function showCon(num) {
    $(".mainCon" + num).show().siblings().hide();
}
// create :hxz 2021-3-9 数字转换为中文
function UpNum(num) {
    let list = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"];
    return list[num];
}