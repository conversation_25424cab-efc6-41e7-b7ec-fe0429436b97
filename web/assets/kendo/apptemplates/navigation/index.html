<!DOCTYPE html>
<html>
    <head>
        <title>Home Page - My Kendo UI Application</title>
        <link href="styles/site.css" rel="stylesheet" type="text/css" />
        <link rel="stylesheet" href="../../styles/kendo.common.min.css">
        <link rel="stylesheet" href="../../styles/kendo.rtl.min.css">
        <link rel="stylesheet" href="../../styles/kendo.default.min.css">
        <link rel="stylesheet" href="../../styles/kendo.mobile.all.min.css">

        <script src="../../js/jquery.min.js"></script>
        <script src="../../js/kendo.all.min.js"></script>
    </head>
    <body>
        <header>
        <div class="content-wrapper">
            <div class="float-left">
                <p class="site-title"><a href="/">your logo here</a></p>
            </div>
            <div class="float-right">
                <nav>
                <ul id="menu">
                    <li><a href="#">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
                </nav>
            </div>
        </div>
        </header>
        <div id="body">
            <div class="featured">
                <div class="content-wrapper">
                    <hgroup class="title">
                        <h1>Home Page.</h1>
                        <h2>Welcome to Kendo UI!</h2>
                    </hgroup>
                    <p>
                        To learn more about Kendo UI visit
                        <a href="http://docs.telerik.com/kendo-ui/introduction" title="Kendo UI Documentation">docs.telerik.com</a>.
                        The page features <mark>tutorials and samples</mark>
                        to help you get started.
                    </p>
                </div>
            </div>
            <section class="content-wrapper main-content clear-fix">
                <h3>Kendo UI PanelBar</h3>
                <br />

                <ul id="introPanelBar">
                    <li class="k-state-active">
                        <span class="k-link k-state-selected">Getting Started</span>
                        <div style="padding:1em">
                            Kendo UI is an HTML5 and jQuery-based comprehensive framework for building interactive and high-performance websites and applications for desktop and touchscreen devices by using the latest web standards. Basically, Kendo UI is a tool-set of web, mobile and data visualization UI scripts, styles, and images that gives you everything you need to get your project up and running. The application framework package comes with a templating library of 75+ UI widgets, an abundance of data-visualization gadgets, client-side data source, an MVVM (Model-View-ViewModel) design pattern, and an adaptive-rendering mobile tool-set that can be seamlessly run on your website. Kendo UI provides AngularJS and Bootstrap integration and is incorporated into products and frameworks such as Telerik Platform, Telerik Analytics, Sitefinity, TeamPulse, Test Studio, Google Chrome, Visual Studio, NuGet along with others.
                        </div>
                    </li>
                    <li>
                        <span class="k-link">Use Bower and jump-start your coding</span>
                        <div style="padding:1em">
                            Bower makes it easy to install and update front-end libraries.
                            <a href="http://docs.telerik.com/kendo-ui/intro/getting-started#install-kendo-ui-as-a-kendo-ui-core-or-kendo-ui-professional-bower-package">Learn more…</a>
                        </div>
                    </li>
                    <li>
                        <span class="k-link">Find More Samples</span>
                        <div style="padding:1em">
                            Kendo UI comes with a comprehensive suite of demos that showcase major features.
                            <a href="http://demos.telerik.com/kendo-ui/">Learn more…</a>
                        </div>
                    </li>
                </ul>
                <script>
                    $(function(){
                        $("#introPanelBar").kendoPanelBar({
                            expandMode: "multiple"
                        });
                    });
                </script>
            </section>
        </div>

        <footer>
        <div class="content-wrapper">
            <div class="float-left">
                <p>&copy; 2015 - Kendo UI Application</p>
            </div>
        </div>
        </footer>
    </body>
</html>