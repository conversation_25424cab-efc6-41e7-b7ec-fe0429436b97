{"version": 3, "sources": ["kendo.scheduler.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "undefined", "timezoneButtonText", "model", "message", "startTimezone", "endTimezone", "appendTimezoneAttr", "attrs", "timezone", "attr", "appendValidDateValidator", "validDateRule", "validationRules", "fields", "field", "validation", "validDateValidator", "isPlainObject", "appendDateCompareValidator", "dateCompareRule", "dateCompare", "wrapDataAccess", "originalFunction", "data", "convertData", "wrapDataSerialization", "toString", "call", "ObservableArray", "method", "removeUid", "event", "idx", "start", "convert", "end", "uid", "getOccurrenceByUid", "applyZone", "date", "fromZone", "toZone", "remove", "input", "picker", "filter", "widgetInstance", "ui", "parseDate", "val", "format", "dateCompareValidator", "container", "startInput", "endInput", "startPicker", "endPicker", "editable", "closest", "find", "expandAll", "events", "zone", "concat", "expand", "trimOptions", "name", "prefix", "edit", "add", "navigate", "createValidationAttributes", "ruleName", "rule", "modelField", "specialRules", "datatype", "inArray", "isFunction", "dropDownResourceEditor", "resource", "title", "ns", "appendTo", "kendoDropDownList", "dataTextField", "dataValueField", "dataSource", "valuePrimitive", "optionLabel", "template", "dataColorField", "dropDownResourceEditorMobile", "view", "getter", "descriptionEditor", "multiSelectResourceEditor", "kendoMultiSelect", "itemTemplate", "tagTemplate", "multiSelectResourceEditorMobile", "moveEventRange", "distance", "duration", "getTime", "Date", "setTime", "defaultCommands", "editors", "Editor", "MobileEditor", "PopupEditor", "Scheduler", "defaultViews", "SCHEDULER_EXPORT", "TimezoneEditor", "ZONETITLEOPTIONTEMPLATE", "ZONEOPTIONTEMPLATE", "MobileTimezoneEditor", "MS_PER_DAY", "getDate", "getMilliseconds", "recurrence", "keys", "F10", "Widget", "DataBoundWidget", "STRING", "Popup", "Calendar", "DataSource", "proxy", "Object", "prototype", "isArray", "NS", "CLICK", "MOUSEDOWN", "TOUCHSTART", "support", "pointers", "TOUCHMOVE", "TOUCHEND", "MOUSEMOVE", "mousemove", "CHANGE", "PROGRESS", "ERROR", "CANCEL", "REMOVE", "RESET", "SAVE", "ADD", "EDIT", "FOCUSEDSTATE", "EXPANDEDSTATE", "VIEWSSELECTOR", "INVERSECOLORCLASS", "valueStartEndBoundRegex", "TODAY", "EXCEPTION_SEPARATOR", "OLD_EXCEPTION_SEPARATOR_REGEXP", "RECURRENCE_EXCEPTION", "DELETECONFIRM", "DELETERECURRING", "EDITRECURRING", "DELETERECURRINGCONFIRM", "RESETSERIESCONFIRM", "DELETESERIESCONFIRM", "COMMANDBUTTONTMPL", "VIEWBUTTONTEMPLATE", "TOOLBARTEMPLATE", "MOBILETOOLBARTEMPLATE", "MOBILEDATERANGEEDITOR", "isAllDay", "dateTimeValidate", "dateValidate", "hide", "DATERANGEEDITOR", "RECURRENCEEDITOR", "kendoRecurrenceEditor", "messages", "MOBILERECURRENCEEDITOR", "kendoMobileRecurrenceEditor", "pane", "MOBILEISALLDAYEDITOR", "MOBILETIMEZONEPOPUP", "noTimezone", "click", "TIMEZONEPOPUP", "timezoneEditorButton", "MOBILETIMEZONEEDITOR", "kendoMobileTimezoneEditor", "TIMEZONEEDITOR", "visible", "toggle", "kendoTimezoneEditor", "SchedulerDataReader", "schema", "reader", "serialize", "errors", "parse", "total", "groups", "aggregates", "SchedulerEvent", "Model", "that", "fn", "_defaultId", "defaults", "idField", "_time", "fieldTime", "toUtcTime", "_date", "clone", "updateUid", "constructor", "toJSON", "offset", "getTimezoneOffset", "MS_PER_MINUTE", "update", "eventInfo", "set", "_startTime", "_endTime", "isMultiDay", "isException", "isNew", "recurrenceId", "isOccurrence", "isRecurring", "recurrenceRule", "isRecurrenceHead", "id", "toOccurrence", "recurrenceException", "obj", "shouldSerialize", "milliseconds", "type", "defaultValue", "required", "validDate", "description", "SchedulerDataSource", "modelBase", "endOffset", "apply", "logic", "filters", "operator", "Query", "toArray", "cancelChanges", "_removeExceptionDate", "insert", "index", "_createNewModel", "accept", "_pushCreated", "_addExceptionDate", "pushCreate", "items", "_removeExceptions", "slice", "item", "shift", "head", "replaceRegExp", "RegExp", "toExceptionString", "test", "newException", "create", "Error", "className", "canceledit", "destroy", "imageClass", "iconClass", "mobile", "date<PERSON><PERSON><PERSON>", "timezonePopUp", "multipleResources", "resources", "desktop", "Observable", "element", "createButton", "toggleDateValidationHandler", "_toggleDateValidation", "e", "isDateTimeInput", "shouldValidate", "bindAttribute", "each", "is", "resourceIndex", "preventDefault", "_initTimezoneEditor", "editor", "allDayEvent", "windows_zones", "repeat", "recurrenceEditor", "multiple", "_buildDesktopEditTemplate", "editable<PERSON><PERSON>s", "tmpl", "settings", "Template", "templateSettings", "paramName", "html", "separateTimezones", "expr", "_createEndTimezoneButton", "_buildMobileEditTemplate", "_buildEditTemplate", "isMobile", "unescape", "_revertTimezones", "_startTimezone", "_endTimezone", "arguments", "Pane", "wrap", "viewEngine", "viewOptions", "renderOnInit", "wrapInSections", "detachOnHide", "detachOnDestroy", "parent", "css", "animations", "left", "right", "close", "unbind", "timezoneView", "content", "kSwitch", "endTimezoneRow", "startTimezoneChange", "enable", "cancel", "timezoneTitle", "save", "append", "contentElement", "show", "on", "edit<PERSON>ie<PERSON>", "stopPropagation", "hasClass", "_editPane", "bind", "ev", "checked", "showDialog", "actions", "buttons", "button", "action", "primary", "kendoDialog", "modal", "preventScroll", "closable", "editEvent", "resetSeriesBtn", "deleteBtn", "updateText", "removeText", "cancelText", "titleText", "editor<PERSON><PERSON><PERSON>", "resetSeries", "kendoEditable", "clearContainer", "target", "validateOnBlur", "trigger", "_views", "roleSelector", "not", "views", "eq", "purge", "deleteText", "kendoWindow", "resizable", "draggable", "userTriggered", "center", "open", "cycleForm", "popup", "_timezonePopup", "buttonIndex", "wrapper", "currentTarget", "focus", "getKendoWindow", "activator", "wnd", "checkbox", "saveButton", "cancelButton", "timezonePopup", "prop", "timezoneEditorTitle", "_initModel", "_wrapper", "_toolbar", "_dataSource", "_resources", "_resizeHandler", "resize", "_resizable", "_movable", "_bindResize", "_selectable", "_touchHandlers", "_ariaId", "guid", "_createEditor", "_unbindResize", "off", "dataItems", "eventsCount", "sortedData", "_data", "eventsUids", "dict", "eventsUidsLength", "_isMobile", "mobileOS", "_isTouch", "originalEvent", "pointerType", "_isInverseColor", "eventElement", "_groupsByResource", "groupIndex", "groupsArray", "parentFieldValue", "parentField", "group", "prevIndex", "dataIndex", "fieldValue", "currentGroupIndex", "select", "selectedGroups", "selectedEvents", "slots", "fieldName", "groupsByResource", "rangeStart", "rangeEnd", "ranges", "selection", "_selection", "_selectedSlots", "_selectedEvents", "_resourceBySlot", "_old", "clearSelection", "splice", "groupedResources", "_selectEvents", "_select", "_startDate", "addDays", "_endDate", "timeSlotCollectionCount", "toLocalDate", "isDaySlot", "currentGroup", "daySlotCollectionCount", "collIdx", "dayCollIdx", "eventsLength", "isGrouped", "getTimeSlotCollection", "getDaySlotCollection", "_createSelection", "startX", "startY", "endX", "endY", "timeStamp", "touchMoveHandler", "_touchMove", "stop", "_touchPosX", "_tapPosition", "_touchPosY", "_userTouched", "_scrolling", "now", "delta", "amplitude", "_amplitude", "_dragging", "selectable", "Math", "abs", "_mouseDownSelection", "kineticScrollNeeded", "animate", "scrollTop", "mouseMoveHandler", "_tabindex", "_mouseMove", "which", "isRight", "_selectFirstSlot", "_ctrl<PERSON>ey", "_shift<PERSON>ey", "toolbar", "removeClass", "relatedTarget", "_keydown", "ctrl<PERSON>ey", "shift<PERSON>ey", "_activeElement", "focusElement", "currentUid", "labelFormat", "oldSelection", "oldEventsLength", "removeAttribute", "removeAttr", "ariaEventLabel", "ariaSlotLabel", "setAttribute", "uids", "occurrenceByUid", "coordinate", "changedTouches", "verticalScroll", "scrollHeight", "clientHeight", "horizontalScroll", "scrollWidth", "clientWidth", "round", "scrollLeft", "applyVerticalScroll", "applyhorizontalScroll", "clearTimeout", "_moveTimer", "setTimeout", "slot", "startDate", "endDate", "selectionByElement", "backward", "_viewByIndex", "selectedIndex", "itemToFocus", "focusedViewName", "viewByIndex", "keyCode", "prevSelection", "isModifier", "focusableToolBarSelector", "focusableItems", "viewsWrapper", "shouldNavigate", "focusedViewIndex", "children", "isRtl", "direction", "addClass", "TAB", "_<PERSON><PERSON>iew", "ENTER", "SPACEBAR", "activeElement", "blur", "altKey", "DOWN", "RIGHT", "LEFT", "UP", "_showCalendar", "ESC", "moveToEvent", "addEvent", "DELETE", "removeEvent", "move", "inRange", "_selected<PERSON>iew<PERSON>ame", "_adjustSelectedDate", "indexOf", "_updateSelection", "dataItem", "indexOfEvent", "_updateEventForSelection", "_timeSlotInterval", "autoBind", "snap", "allDaySlot", "min", "max", "workWeekStart", "workWeekEnd", "showWorkHours", "startTime", "endTime", "currentTimeMarker", "updateInterval", "useLocalTimezone", "footer", "today", "pdf", "deleteWindowTitle", "next", "previous", "day", "week", "workWeek", "agenda", "month", "timeline", "timelineWeek", "timelineWorkWeek", "timelineMonth", "recurrenceMessages", "resetSeriesWindowTitle", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "confirmation", "orientation", "resourceDS", "_refresh<PERSON><PERSON><PERSON>", "_progress<PERSON><PERSON><PERSON>", "_error<PERSON><PERSON><PERSON>", "_resourceRefreshHandler", "_resourceProgressHandler", "_resourceErrorHandler", "calendar", "_editor", "_moveDraggable", "_resizeDraggable", "_model", "setDataSource", "fetch", "_selected<PERSON><PERSON><PERSON>", "siblings", "startSlot", "endSlot", "startResources", "clonedEvent", "originSlot", "originStartTime", "clonedEvents", "cachedEvents", "movable", "android", "Draggable", "ignore", "holdToDrag", "autoScroll", "evtClone", "evtCloneElement", "is<PERSON><PERSON>ch", "_eventOptionsForMove", "inverseColor", "_slotByPosition", "x", "startLocation", "y", "startOffset", "range", "slotIndex", "targetSlotElement", "location", "top", "_updateMoveHint", "endResources", "prevented", "evt", "updatedEventOptions", "eventOptions", "_removeMoveHint", "stringify", "_isMultiDrag", "_updateEvent", "sync", "cancelHold", "handle", "directions", "k-resize-e", "k-resize-w", "k-resize-n", "k-resize-s", "dragstart", "dragHandle", "_updateEventForResize", "drag", "dir", "currentSlot", "originalStart", "originalEnd", "_updateResizeHint", "dragend", "_removeResizeHint", "setHours", "setMinutes", "dragcancel", "updateEvent", "callback", "_preventRefresh", "_convertDates", "recurrenceHead", "getByUid", "updateSeries", "updateOccurrence", "eventUid", "exception", "_showRecurringDialog", "editR<PERSON><PERSON>ring", "occurrenceText", "seriesText", "_modelForContainer", "_confirmation", "isResetSeries", "_isEditorOpened", "deleteRecurringConfirmation", "deleteSeriesConfirmation", "resetSeriesConfirmation", "cancelEvent", "_editEvent", "saveEvent", "dirty", "_editRecurringDialog", "_createPopupEditor", "editOccurrence", "editSeries", "editRecurringMode", "_createButton", "command", "commandName", "_set", "_attemptRefresh", "refresh", "_deleteRecurringDialog", "_removeEvent", "occurrence", "occurrencesInRange", "deleteOccurrenceConfirmation", "seriesCallback", "<PERSON><PERSON><PERSON><PERSON>", "currentModel", "deleteOccurrence", "deleteSeries", "deleteRecurring", "_unbind<PERSON>iew", "angular", "elements", "_bind<PERSON>iew", "_viewRemoveHandler", "_viewAddHandler", "_viewEditHandler", "_viewNavigateHandler", "switchWorkDay", "isWorkDay", "_workDayMode", "_select<PERSON><PERSON>w", "_viewActivateHandler", "constrainSelection", "viewButton", "firstButton", "viewButtons", "viewSelect", "_renderView", "_viewsCount", "replaceWith", "prepend", "toLowerCase", "rebind", "viewName", "_initializeView", "mouseAndTouchPresent", "dateForTitle", "shortDateForTitle", "mobileDateForTitle", "force", "getSize", "currentSize", "selectedDate", "isInDateRange", "setFullYear", "getFullYear", "getMonth", "isSettings", "defaultView", "selected", "hasType", "allDay", "time", "_requestStart", "_error", "_progress", "progress", "resourcePromises", "_resourceDataSource", "when", "then", "groupName", "promises", "dataSourceInstance", "_bindResourceEvents", "isResourceGrouped", "_refreshResource", "observable", "formattedDate", "formattedShortDate", "commands", "grep", "viewsCount", "saveAsPDF", "timezoneOffset", "li", "currentDate", "nextDate", "previousDate", "toggleClass", "hover", "targetElm", "anchor", "activate", "_toggleResize", "change", "copyAnchorStyles", "preventRefresh", "visibleEndDate", "refreshLayout", "render", "slotByPosition", "slotByElement", "resourcesBySlot", "plugin", "PDFMixin", "_drawPDF", "scheduler", "promise", "table", "styles", "Deferred", "_drawPDFShadow", "avoidLinks", "done", "args", "page", "pageNumber", "totalPages", "notify", "resolve", "fail", "err", "reject", "always", "zones", "zones_titles", "_zonesQuery", "_zoneTitleId", "_zoneTitlePicker", "_zonePicker", "_zoneTitle", "_zone", "_value", "zoneTitle", "DropDownList", "cascadeFrom", "dataBound", "other_zone", "_bindZones", "_filter", "_options", "_zonePickerLabel", "zoneSelect", "after", "zonePicker", "zone_value"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,mBACH,qBACA,iBACA,oBACA,eACA,uBACA,6BACA,uBACA,0BACA,6BACA,4BACA,+BACA,eACA,aACA,YACA,gBACDD,IACL,WAs1HE,MAlxHC,UAAUE,EAAGkF,GA8DV,QAASC,GAAmBC,EAAOC,GAQ/B,MAPAA,GAAUA,GAAW,GACjBD,EAAME,gBACND,EAAUD,EAAME,cACZF,EAAMG,cACNF,GAAW,MAAQD,EAAMG,cAG1BF,EAEX,QAASG,GAAmBC,EAAOhC,GAC/B,GAAIiC,GAAWjC,EAAQiC,QACnBA,KACAD,EAAMvD,EAAMyD,KAAK,aAAeD,GAGxC,QAASE,GAAyBH,EAAOhC,GAAzC,GAGYoC,GAFJC,EAAkBrC,EAAQ2B,MAAMW,OAAOtC,EAAQuC,OAAOC,UACtDH,KACID,EAAgBC,EAAgBI,mBAChCL,GAAiBM,EAAcN,IAAkBA,EAAcR,UAC/DI,EAAMvD,EAAMyD,KAAK,kBAAoBE,EAAcR,UAI/D,QAASe,GAA2BX,EAAOhC,GAA3C,GAGY4C,GAFJP,EAAkBrC,EAAQ2B,MAAMW,OAAOtC,EAAQuC,OAAOC,UACtDH,KACIO,EAAkBP,EAAgBQ,YAClCD,GAAmBF,EAAcE,IAAoBA,EAAgBhB,UACrEI,EAAMvD,EAAMyD,KAAK,oBAAsBU,EAAgBhB,UAInE,QAASkB,GAAeC,EAAkBd,GACtC,MAAO,UAAUe,GAGb,MAFAA,GAAOD,EAAiBC,GACxBC,EAAYD,EAAM,QAASf,GACpBe,OAGf,QAASE,GAAsBH,EAAkBd,GAC7C,MAAO,UAAUe,GAQb,MAPIA,KAC4B,mBAAxBG,EAASC,KAAKJ,IAAgCA,YAAgBvE,GAAMuE,KAAKK,kBACzEL,GAAQA,KAGhBC,EAAYD,EAAM,SAAUf,GAAU,GACtCe,EAAOD,EAAiBC,GACjBA,OAGf,QAASC,GAAYD,EAAMM,EAAQrB,EAAUsB,GACzC,GAAIC,GAAOC,EAAKhG,CAEhB,KADAuF,EAAOA,MACFS,EAAM,EAAGhG,EAASuF,EAAKvF,OAAQgG,EAAMhG,EAAQgG,IAC9CD,EAAQR,EAAKS,GACTF,EACIC,EAAM3B,eAAiB2B,EAAM1B,YACzBG,GACAuB,EAAME,MAAQjF,EAAMwD,SAAS0B,QAAQH,EAAME,MAAOF,EAAM3B,eAAiB2B,EAAM1B,YAAaG,GAC5FuB,EAAMI,IAAMnF,EAAMwD,SAAS0B,QAAQH,EAAMI,IAAKJ,EAAM1B,aAAe0B,EAAM3B,cAAeI,GACxFuB,EAAME,MAAQjF,EAAMwD,SAASqB,GAAQE,EAAME,MAAOzB,GAClDuB,EAAMI,IAAMnF,EAAMwD,SAASqB,GAAQE,EAAMI,IAAK3B,KAE9CuB,EAAME,MAAQjF,EAAMwD,SAASqB,GAAQE,EAAME,MAAOF,EAAM3B,eAAiB2B,EAAM1B,aAC/E0B,EAAMI,IAAMnF,EAAMwD,SAASqB,GAAQE,EAAMI,IAAKJ,EAAM1B,aAAe0B,EAAM3B,gBAEtEI,IACPuB,EAAME,MAAQjF,EAAMwD,SAASqB,GAAQE,EAAME,MAAOzB,GAClDuB,EAAMI,IAAMnF,EAAMwD,SAASqB,GAAQE,EAAMI,IAAK3B,IAG9CuB,EAAM3B,eAAiB2B,EAAM1B,aAC7B0B,EAAME,MAAQjF,EAAMwD,SAASqB,GAAQE,EAAME,MAAOF,EAAM3B,eAAiB2B,EAAM1B,aAC/E0B,EAAMI,IAAMnF,EAAMwD,SAASqB,GAAQE,EAAMI,IAAKJ,EAAM1B,aAAe0B,EAAM3B,eACrEI,IACAuB,EAAME,MAAQjF,EAAMwD,SAAS0B,QAAQH,EAAME,MAAOF,EAAM3B,eAAiB2B,EAAM1B,YAAaG,GAC5FuB,EAAMI,IAAMnF,EAAMwD,SAAS0B,QAAQH,EAAMI,IAAKJ,EAAM1B,aAAe0B,EAAM3B,cAAeI,KAErFA,IACPuB,EAAME,MAAQjF,EAAMwD,SAASqB,GAAQE,EAAME,MAAOzB,GAClDuB,EAAMI,IAAMnF,EAAMwD,SAASqB,GAAQE,EAAMI,IAAK3B,IAGlDsB,SACOC,GAAMK,GAGrB,OAAOb,GAEX,QAASc,GAAmBd,EAAMa,GAE9B,IADA,GAAmCL,GAA/B/F,EAASuF,EAAKvF,OAAQgG,EAAM,EACzBA,EAAMhG,EAAQgG,IAEjB,GADAD,EAAQR,EAAKS,GACTD,EAAMK,MAAQA,EACd,MAAOL,GAqCnB,QAASO,GAAUC,EAAMC,EAAUC,GAM/B,MAJIF,GADAE,EACOzF,EAAMwD,SAAS0B,QAAQK,EAAMC,EAAUC,GAEvCzF,EAAMwD,SAASkC,OAAOH,EAAMC,GAI3C,QAASxB,GAAmB2B,GAA5B,GAEYJ,GACAK,CAFR,SAAID,EAAME,OAAO,gBAAgB7G,QAAU2G,EAAME,OAAO,iBAAiB7G,QAAU2G,EAAME,OAAO,cAAc7G,QAAU2G,EAAME,OAAO,eAAe7G,UAE5I4G,EAAS5F,EAAM8F,eAAeH,EAAO3F,EAAM+F,IAC3CH,GACAL,EAAOvF,EAAMgG,UAAUL,EAAMM,MAAOL,EAAOrE,QAAQ2E,UAC1CX,GAAQK,EAAOjF,UAExB4E,EAAOvF,EAAMgG,UAAUL,EAAMM,SACpBV,IAKrB,QAASY,GAAqBR,GAA9B,GAEYS,GACAC,EACAC,EAEIrB,EAAOE,EACPoB,EACAC,EACAC,EACAvD,EAUQM,EACAJ,EACAC,CArBpB,SAAIsC,EAAME,OAAO,cAAc7G,SACvBoH,EAAYT,EAAMe,QAAQ,0BAC1BL,EAAaD,EAAUO,KAAK,wBAC5BL,EAAWF,EAAUO,KAAK,sBAC1BL,EAAS,IAAMD,EAAW,KAEtBE,EAAcvG,EAAM8F,eAAeO,EAAYrG,EAAM+F,IACrDS,EAAYxG,EAAM8F,eAAeQ,EAAUtG,EAAM+F,IACjDU,EAAWL,EAAU7B,KAAK,iBAC1BrB,EAAQuD,EAAWA,EAASlF,QAAQ2B,MAAQ,KAC5CqD,GAAeC,GACfvB,EAAQsB,EAAY5F,QACpBwE,EAAMqB,EAAU7F,UAEhBsE,EAAQjF,EAAMgG,UAAUK,EAAWJ,OACnCd,EAAMnF,EAAMgG,UAAUM,EAASL,QAE/BhB,GAASE,OACLjC,IACIM,EAAW6C,EAAW5C,KAAKzD,EAAMyD,KAAK,aACtCL,EAAgBF,EAAME,cACtBC,EAAcH,EAAMG,YACxBD,EAAgBA,GAAiBC,EACjCA,EAAcA,GAAeD,EACzBA,IACA6B,EAAQK,EAAUL,EAAO7B,EAAeI,GACxC2B,EAAMG,EAAUH,EAAK9B,EAAaG,KAGnCyB,GAASE,GA4RhC,QAASyB,GAAUC,EAAQ5B,EAAOE,EAAK2B,GAEnC,IADA,GAAI9H,GAAS6H,EAAO7H,OAAQuF,KAAWS,EAAM,EACtCA,EAAMhG,EAAQgG,IACjBT,EAAOA,EAAKwC,OAAOF,EAAO7B,GAAKgC,OAAO/B,EAAOE,EAAK2B,GAEtD,OAAOvC,GAkCX,QAAS0C,GAAY1F,GAOjB,aANOA,GAAQ2F,WACR3F,GAAQ4F,aACR5F,GAAQmE,aACRnE,GAAQ6F,WACR7F,GAAQ8F,UACR9F,GAAQ+F,SACR/F,EAEX,QAASgG,GAA2BrE,EAAOY,GAA3C,GAYQ0D,GACAC,EAZAC,GAAcxE,EAAMW,QAAUX,GAAOY,GACrC6D,GACA,MACA,QACA,SACA,OACA,WAEA5D,EAAa2D,EAAaA,EAAW3D,cACrC6D,EAAW5H,EAAMyD,KAAK,QACtBoE,EAAU/J,EAAE+J,QAGZpE,IACJ,KAAK+D,IAAYzD,GACb0D,EAAO1D,EAAWyD,GACdK,EAAQL,EAAUG,IAAiB,EACnClE,EAAKmE,GAAYJ,EACTxH,EAAM8H,WAAWL,KACzBhE,EAAK+D,GAAYvD,EAAcwD,GAAQA,EAAK9G,OAAS6G,EAAWC,GAEpEhE,EAAKzD,EAAMyD,KAAK+D,EAAW,SAAWC,EAAKtE,OAE/C,OAAOM,GAEX,QAASsE,GAAuBC,EAAU9E,GACtC,GAAIO,GAAO8D,EAA2BrE,EAAO8E,EAASlE,MACtD,OAAO,UAAUsC,GACbtI,EAAEkC,EAAMkG,OAAO,2CAA6ChD,EAAM+E,MAAQ,KAAMjI,EAAMkI,GAAIF,EAASlE,QAAQqE,SAAS/B,GAAW3C,KAAKA,GAAM2E,mBACtIC,cAAeL,EAASK,cACxBC,eAAgBN,EAASM,eACzBC,WAAYP,EAASO,WACrBC,eAAgBR,EAASQ,eACzBC,YAAa,OACbC,SAAU1I,EAAMkG,OAAO,iGAAkG8B,EAASW,eAAgBX,EAASK,kBAIvK,QAASO,GAA6BZ,EAAU9E,GAC5C,GAAIO,GAAO8D,EAA2BrE,EAAO8E,EAASlE,MACtD,OAAO,UAAUsC,GAAV,GAGMpB,GAAShG,EAFduC,EAAU,GACVsH,EAAOb,EAASO,WAAWM,MAC/B,KAAS7D,EAAM,EAAGhG,EAAS6J,EAAK7J,OAAQgG,EAAMhG,EAAQgG,IAClDzD,GAAWvB,EAAMkG,OAAO,mCAAoClG,EAAM8I,OAAOd,EAASM,gBAAgBO,EAAK7D,IAAOhF,EAAM8I,OAAOd,EAASK,eAAeQ,EAAK7D,IAE5JlH,GAAEkC,EAAMkG,OAAO,gDAAiDlG,EAAMkI,GAAIF,EAASlE,MAAOvC,EAASyG,EAASQ,iBAAiBL,SAAS/B,GAAW3C,KAAKA,IAG9J,QAASsF,GAAkBxH,GACvB,GAAIkC,GAAO8D,EAA2BhG,EAAQ2B,MAAO3B,EAAQuC,MAC7D,OAAO,UAAUsC,EAAWlD,GACxBpF,EAAE,yDAA2DoF,EAAM+E,MAAQ,OAAOxE,KAAKA,GAAM0E,SAAS/B,IAG9G,QAAS4C,GAA0BhB,EAAU9E,GACzC,GAAIO,GAAO8D,EAA2BrE,EAAO8E,EAASlE,MACtD,OAAO,UAAUsC,GACbtI,EAAEkC,EAAMkG,OAAO,oCAAqClG,EAAMkI,GAAIF,EAASlE,QAAQqE,SAAS/B,GAAW3C,KAAKA,GAAMwF,kBAC1GZ,cAAeL,EAASK,cACxBC,eAAgBN,EAASM,eACzBC,WAAYP,EAASO,WACrBC,eAAgBR,EAASQ,eACzBU,aAAclJ,EAAMkG,OAAO,iGAAkG8B,EAASW,eAAgBX,EAASK,eAC/Jc,YAAanJ,EAAMkG,OAAO,iGAAkG8B,EAASW,eAAgBX,EAASK,kBAI1K,QAASe,GAAgCpB,EAAU9E,GAC/C,GAAIO,GAAO8D,EAA2BrE,EAAO8E,EAASlE,MACtD,OAAO,UAAUsC,GAAV,GAGMpB,GAAShG,EAFduC,EAAU,GACVsH,EAAOb,EAASO,WAAWM,MAC/B,KAAS7D,EAAM,EAAGhG,EAAS6J,EAAK7J,OAAQgG,EAAMhG,EAAQgG,IAClDzD,GAAWvB,EAAMkG,OAAO,mCAAoClG,EAAM8I,OAAOd,EAASM,gBAAgBO,EAAK7D,IAAOhF,EAAM8I,OAAOd,EAASK,eAAeQ,EAAK7D,IAE5JlH,GAAEkC,EAAMkG,OAAO,kGAAmGlG,EAAMkI,GAAIF,EAASlE,MAAOvC,EAASyG,EAASQ,iBAAiBL,SAAS/B,GAAW3C,KAAKA,IAGhN,QAAS4F,GAAetE,EAAOuE,GAA/B,GAIQnE,GAHAoE,EAAWxE,EAAMI,IAAIqE,UAAYzE,EAAME,MAAMuE,UAC7CvE,EAAQ,GAAIwE,MAAK1E,EAAME,MAAMuE,UAIjC,OAHAxJ,GAAMuF,KAAKmE,QAAQzE,EAAOqE,GACtBnE,EAAM,GAAIsE,MAAKxE,EAAMuE,WACzBxJ,EAAMuF,KAAKmE,QAAQvE,EAAKoE,GAAU,IAE9BtE,MAAOA,EACPE,IAAKA,GA5pBhB,GA2iBOwE,GAoHAC,EAqBAC,EA8NAC,EAmMAC,EA0PAC,EAutEAC,EAcIC,EAoCJC,EA2EAC,EACAC,EACAC,EApqHAtK,EAAQD,OAAOC,MAAOuF,EAAOvF,EAAMuF,KAAMgF,EAAahF,EAAKgF,WAAYC,EAAUjF,EAAKiF,QAASC,EAAkBzK,EAAMuF,KAAKkF,gBAAiBC,EAAa1K,EAAM0K,WAAYC,EAAO7M,EAAEqC,QAASyK,IAAK,KAAO5K,EAAM2K,MAAO5E,EAAK/F,EAAM+F,GAAI8E,EAAS9E,EAAG8E,OAAQC,EAAkB/E,EAAG+E,gBAAiBC,EAAS,SAAUC,EAAQjF,EAAGiF,MAAOC,EAAWlF,EAAGkF,SAAUC,EAAalL,EAAMuE,KAAK2G,WAAYjH,EAAgBnG,EAAEmG,cAAe9D,EAASrC,EAAEqC,OAAQgL,EAAQrN,EAAEqN,MAAOzG,EAAW0G,OAAOC,UAAU3G,SAAU4G,EAAUxN,EAAEwN,QAASC,EAAK,kBAAmBC,GAAQ,QAASC,GAAY,YAAaC,GAAa1L,EAAM2L,QAAQC,SAAW,cAAgB,aAAcC,GAAY7L,EAAM2L,QAAQC,SAAW,cAAgB,YAAaE,GAAW9L,EAAM2L,QAAQC,SAAW,YAAc,WAAYG,GAAY/L,EAAM2L,QAAQK,UAAWC,GAAS,SAAUC,GAAW,WAAYC,GAAQ,QAASC,GAAS,SAAUC,GAAS,SAAUC,GAAQ,cAAeC,GAAO,OAAQC,GAAM,MAAOC,GAAO,OAAQC,GAAe,kBAAmBC,GAAgB,mBAAoBC,GAAgB,qBAAsBC,GAAoB,kBAAmBC,GAA0B,mCAAoCC,GAAQvC,EAAQ,GAAIf,OAASuD,GAAsB,IAAKC,GAAiC,MAAOC,GAAuB,sBAAuBC,GAAgB,8CAA+CC,GAAkB,wEAAyEC,GAAgB,sEAAuEC,GAAyB,yDAA0DC,GAAqB,mDAAoDC,GAAsB,oDAAqDC,GAAoB,kEAAmEC,GAAqB1N,EAAM0I,SAAS,gIAAiIiF,GAAkB3N,EAAM0I,SAAS,8jDAAgtDkF,GAAwB5N,EAAM0I,SAAS,i4CAA6hDmF,GAAwB,SAAUzH,EAAW7E,GAArB,GACv0KkC,IACAyD,KAAM3F,EAAQuC,MACdmE,MAAO1G,EAAQ0G,OAEf6F,EAAWvM,EAAQ2B,MAAM4K,SACzBC,EAAmB/N,EAAMyD,KAAK,YAAc,MAASqK,EAAW,IAChEE,EAAehO,EAAMyD,KAAK,YAAc,KAAQqK,EAAW,GAC/DxK,GAAmBG,EAAMlC,GACzBmC,EAAyBD,EAAMlC,GAC/B2C,EAA2BT,EAAMlC,GACjCzD,EAAE,yCAA2CkC,EAAMyD,KAAK,QAAU,qBAAuBzD,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,yBAA2BiK,EAAmB,MAAMtK,KAAKA,GAAM0E,SAAS/B,GACnNtI,EAAE,+BAAiCkC,EAAMyD,KAAK,QAAU,WAAazD,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,sBAAwBkK,EAAe,MAAMvK,KAAKA,GAAM0E,SAAS/B,GACxLtI,EAAE,SAAWkC,EAAMyD,KAAK,OAAS,KAAOlC,EAAQuC,MAAQ,6BAA6BmK,OAAO9F,SAAS/B,IACtG8H,GAAkB,SAAU9H,EAAW7E,GACtC,GAAIkC,IACIyD,KAAM3F,EAAQuC,MACdmE,MAAO1G,EAAQ0G,OAChB6F,EAAWvM,EAAQ2B,MAAM4K,SAAUC,EAAmB/N,EAAMyD,KAAK,YAAc,MAASqK,EAAW,KAAOE,EAAehO,EAAMyD,KAAK,YAAc,KAAQqK,EAAW,IAC5KxK,GAAmBG,EAAMlC,GACzBmC,EAAyBD,EAAMlC,GAC/B2C,EAA2BT,EAAMlC,GACjCzD,EAAE,+BAAiCkC,EAAMyD,KAAK,QAAU,WAAkBzD,EAAMyD,KAAK,QAAU,qBAAuBzD,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,wBAA0BiK,EAAmB,MAAMtK,KAAKA,GAAM0E,SAAS/B,GAC/OtI,EAAE,+BAAiCkC,EAAMyD,KAAK,QAAU,WAAkBzD,EAAMyD,KAAK,QAAU,iBAAmBzD,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,sBAAwBkK,EAAe,MAAMvK,KAAKA,GAAM0E,SAAS/B,GACrOtI,EAAE,SAAWkC,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,qBAAqBqE,SAAS/B,GACvE,QAAlB7E,EAAQuC,OACRhG,EAAE,SAAWkC,EAAMyD,KAAK,QAAU,0DAA0D0E,SAAS/B,GAEzGtI,EAAE,SAAWkC,EAAMyD,KAAK,OAAS,KAAOlC,EAAQuC,MAAQ,6BAA6BmK,OAAO9F,SAAS/B,IACtG+H,GAAmB,SAAU/H,EAAW7E,GACvCzD,EAAE,QAAUkC,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,QAAQL,MAAOyD,KAAM3F,EAAQuC,QAASqE,SAAS/B,GAAWgI,uBACpHnJ,MAAO1D,EAAQ2B,MAAM+B,MACrBzB,SAAUjC,EAAQiC,SAClB6K,SAAU9M,EAAQ8M,YAEvBC,GAAyB,SAAUlI,EAAW7E,GAC7CzD,EAAE,QAAUkC,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,QAAQL,MAAOyD,KAAM3F,EAAQuC,QAASqE,SAAS/B,GAAWmI,6BACpHtJ,MAAO1D,EAAQ2B,MAAM+B,MACrBzB,SAAUjC,EAAQiC,SAClB6K,SAAU9M,EAAQ8M,SAClBG,KAAMjN,EAAQiN,KACd7N,MAAOY,EAAQ2B,MAAM3B,EAAQuC,UAElC2K,GAAuB,SAAUrI,EAAW7E,GAC3CzD,EAAE,4CAA8CkC,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,QAAQqE,SAAS/B,IACpHsI,GAAsB,SAAUtI,EAAW7E,GAC1C,GAAIvD,GAAOiF,EAAmB1B,EAAQ2B,MAAO3B,EAAQ8M,SAASM,WAC9D7Q,GAAE,0CAA0CE,KAAKA,GAAMmK,SAAS/B,GAChEtI,EAAE,wDAAwDqK,SAAS/B,GACnEA,EAAUM,QAAQ,mBAAmBkI,MAAMrN,EAAQqN,QACpDC,GAAgB,SAAUzI,EAAW7E,GACpCzD,EAAE,+DAAiEyD,EAAQ8M,SAASS,qBAAuB,QAAQF,MAAMrN,EAAQqN,OAAOzG,SAAS/B,IAClJ2I,GAAuB,SAAU3I,EAAW7E,GAC3CzD,EAAE,uCAAyCkC,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,QAAQL,MAAOyD,KAAM3F,EAAQuC,QAASqE,SAAS/B,GAAW4I,2BAA4BvG,YAAalH,EAAQoN,cACzMM,GAAiB,SAAU7I,EAAW7E,GACrC,GAAI2N,GAAU3N,EAAQ2N,SAAW3N,EAAQ2N,UAAYlM,CACrDlF,GAAE,QAAUkC,EAAMyD,KAAK,QAAU,WAAalC,EAAQuC,MAAQ,QAAQL,MAAOyD,KAAM3F,EAAQuC,QAASqL,OAAOD,GAAS/G,SAAS/B,GAAWgJ,qBACpI3G,YAAalH,EAAQoN,WACrB1G,MAAO1G,EAAQ0G,SAwGvBoH,GAAsBrP,EAAME,MAAMC,QAClCC,KAAM,SAAUkP,EAAQC,GACpB,GAAI/L,GAAW8L,EAAO9L,QACtBlD,MAAKiP,OAASA,EACVA,EAAOrM,QACP5C,KAAK4C,MAAQqM,EAAOrM,OAExB5C,KAAKkD,SAAWA,EAChBlD,KAAKiE,KAAOF,EAAevG,EAAEqN,MAAM7K,KAAKiE,KAAMjE,MAAOkD,GACrDlD,KAAKkP,UAAY/K,EAAsB3G,EAAEqN,MAAM7K,KAAKkP,UAAWlP,MAAOkD,IAE1EiM,OAAQ,SAAUlL,GACd,MAAOjE,MAAKiP,OAAOE,OAAOlL,IAE9BmL,MAAO,SAAUnL,GACb,MAAOjE,MAAKiP,OAAOG,MAAMnL,IAE7BA,KAAM,SAAUA,GACZ,MAAOjE,MAAKiP,OAAOhL,KAAKA,IAE5BoL,MAAO,SAAUpL,GACb,MAAOjE,MAAKiP,OAAOI,MAAMpL,IAE7BqL,OAAQ,SAAUrL,GACd,MAAOjE,MAAKiP,OAAOK,OAAOrL,IAE9BsL,WAAY,SAAUtL,GAClB,MAAOjE,MAAKiP,OAAOM,WAAWtL,IAElCiL,UAAW,SAAUjL,GACjB,MAAOjE,MAAKiP,OAAOC,UAAUjL,MA6DjCuL,GAAiB9P,EAAMuE,KAAKwL,MAAMlS,QAClCuC,KAAM,SAAUO,GACZ,GAAIqP,GAAO1P,IACXN,GAAMuE,KAAKwL,MAAME,GAAG7P,KAAKuE,KAAKqL,EAAMrP,GACpCqP,EAAKE,WAAaF,EAAKG,SAASH,EAAKI,UAEzCC,MAAO,SAAUvM,GAAV,GACCyB,GAAOjF,KAAKwD,GACZwM,EAAY,IAAMxM,EAAQ,MAC9B,OAAIxD,MAAKgQ,GACEhQ,KAAKgQ,GAAatQ,EAAMuF,KAAKgL,UAAUvQ,EAAMuF,KAAKiF,QAAQjF,IAE9DkF,EAAgBlF,IAE3BiL,MAAO,SAAU1M,GACb,GAAIwM,GAAY,IAAMxM,EAAQ,MAC9B,OAAIxD,MAAKgQ,GACEhQ,KAAKgQ,GAAahQ,KAAK+P,MAAMvM,GAEjC9D,EAAMuF,KAAKiF,QAAQlK,KAAKwD,KAEnC2M,MAAO,SAAUlP,EAASmP,GACtB,GAAItL,GAAM9E,KAAK8E,IAAKL,EAAQ,GAAIzE,MAAKqQ,YAAY7S,EAAEqC,UAAWG,KAAKsQ,SAAUrP,GAI7E,OAHKmP,KACD3L,EAAMK,IAAMA,GAETL,GAEXwE,SAAU,WAAA,GACFpE,GAAM7E,KAAK6E,IACXF,EAAQ3E,KAAK2E,MACb4L,GAAU1L,EAAI2L,oBAAsB7L,EAAM6L,qBAAuB9Q,EAAMuF,KAAKwL,aAChF,OAAO5L,GAAMF,EAAQ4L,GAEzB7J,OAAQ,SAAU/B,EAAOE,EAAK2B,GAC1B,MAAO4D,GAAaA,EAAW1D,OAAO1G,KAAM2E,EAAOE,EAAK2B,IAASxG,OAErE0Q,OAAQ,SAAUC,GACd,IAAK,GAAInN,KAASmN,GACd3Q,KAAK4Q,IAAIpN,EAAOmN,EAAUnN,GAE1BxD,MAAK6Q,YACL7Q,KAAK4Q,IAAI,aAAclR,EAAMuF,KAAKgL,UAAUjQ,KAAK2E,QAEjD3E,KAAK8Q,UACL9Q,KAAK4Q,IAAI,WAAYlR,EAAMuF,KAAKgL,UAAUjQ,KAAK6E,OAGvDkM,WAAY,WACR,MAAO/Q,MAAKwN,UAAYxN,KAAKiJ,YAAcvJ,EAAMuF,KAAKgF,YAE1D+G,YAAa,WACT,OAAQhR,KAAKiR,SAAWjR,KAAKkR,cAEjCC,aAAc,WACV,MAAOnR,MAAKiR,SAAWjR,KAAKkR,cAEhCE,YAAa,WACT,SAAUpR,KAAKqR,iBAAkBrR,KAAKkR,eAE1CI,iBAAkB,WACd,SAAUtR,KAAKuR,KAAMvR,KAAKqR,iBAE9BG,aAAc,SAAUvQ,GAOpB,MANAA,GAAUzD,EAAEqC,OAAOoB,GACfwQ,oBAAqB,KACrBJ,eAAgB,KAChBH,aAAclR,KAAKuR,IAAMvR,KAAKkR,eAElCjQ,EAAQjB,KAAK8P,SAAW9P,KAAK6P,SAAS7P,KAAK8P,SACpC9P,KAAKmQ,MAAMlP,GAAS,IAE/BqP,OAAQ,WACJ,GAAIoB,GAAMhS,EAAMuE,KAAKwL,MAAME,GAAGW,OAAOjM,KAAKrE,KAI1C,OAHA0R,GAAI5M,IAAM9E,KAAK8E,UACR4M,GAAIb,iBACJa,GAAIZ,SACJY,GAEXC,gBAAiB,SAAUnO,GACvB,MAAO9D,GAAMuE,KAAKwL,MAAME,GAAGgC,gBAAgBtN,KAAKrE,KAAMwD,IAAoB,eAAVA,GAEpEoN,IAAK,SAAU3S,EAAKoC,GAAf,GAIOsE,GACAE,EACA+M,EALJpE,EAAWxN,KAAKwN,WAAY,CAChC9N,GAAMuE,KAAKwL,MAAME,GAAGiB,IAAIvM,KAAKrE,KAAM/B,EAAKoC,GAC7B,YAAPpC,GAAqBoC,GAASmN,IAC1B7I,EAAQjF,EAAMuF,KAAKiF,QAAQlK,KAAK2E,OAChCE,EAAM,GAAIsE,MAAKnJ,KAAK6E,KACpB+M,EAAelS,EAAMuF,KAAKkF,gBAAgBtF,GACzB,IAAjB+M,GAAsBvR,IACtBuR,EAAe3H,GAEnBjK,KAAK4Q,IAAI,QAASjM,GACdtE,KAAU,GACVX,EAAMuF,KAAKmE,QAAQvE,GAAM+M,GACrB/M,EAAMF,IACNE,EAAMF,IAGVjF,EAAMuF,KAAKmE,QAAQvE,EAAKoF,EAAa2H,GAEzC5R,KAAK4Q,IAAI,MAAO/L,KAGxB0M,GAAI,KACJhO,QACIgO,IAAMM,KAAM,UACZlK,OACImK,aAAc,GACdD,KAAM,UAEVlN,OACIkN,KAAM,OACNpO,YACIsO,UAAU,EACVC,WAAa3R,MAAOqD,KAG5BZ,eAAiB+O,KAAM,UACvBhN,KACIgN,KAAM,OACNpO,YACIsO,UAAU,EACVC,WAAa3R,MAAOqD,GACpBI,aAAezD,MAAOwF,KAG9B9C,aAAe8O,KAAM,UACrBR,gBACIS,aAAc,GACdD,KAAM,UAEVJ,qBACIK,aAAc,GACdD,KAAM,UAEVrE,UACIqE,KAAM,UACNC,cAAc,GAElBG,aAAeJ,KAAM,aAGzBK,GAAsBtH,EAAW/K,QACjCC,KAAM,SAAUmB,GACZ2J,EAAW+E,GAAG7P,KAAKuE,KAAKrE,KAAMH,GAAO,MACjCmP,QACImD,UAAW3C,GACX5M,MAAO4M,KAEZvO,IACHjB,KAAKiP,OAAS,GAAIF,IAAoB/O,KAAKiB,QAAQ+N,OAAQhP,KAAKiP,SAEpEvI,OAAQ,SAAU/B,EAAOE,GACrB,GAAqCuN,GAAjCnO,EAAOjE,KAAKuI,OAAQhD,IAiDxB,OAhDIZ,IAASE,IACTuN,EAAYvN,EAAI2L,oBAChB3L,EAAM,GAAIsE,MAAKtE,EAAIqE,UAAYe,EAAa,GACxCpF,EAAI2L,sBAAwB4B,IAC5BvN,EAAMnF,EAAMwD,SAASmP,MAAMxN,EAAKuN,IAEpC7M,GACI+M,MAAO,KACPC,UAEQD,MAAO,MACPC,UAEQ/O,MAAO,QACPgP,SAAU,MACVnS,MAAOsE,IAGPnB,MAAO,MACPgP,SAAU,MACVnS,MAAOsE,IAGPnB,MAAO,QACPgP,SAAU,MACVnS,MAAOwE,MAKfyN,MAAO,MACPC,UAEQ/O,MAAO,QACPgP,SAAU,MACVnS,MAAO,GAAI8I,MAAKxE,EAAMuE,UAAYe,EAAa,KAG/CzG,MAAO,MACPgP,SAAU,MACVnS,MAAOsE,OAM3BV,EAAO,GAAIvE,GAAMuE,KAAKwO,MAAMnM,EAAUrC,EAAMU,EAAOE,EAAK7E,KAAKiP,OAAO/L,WAAWqC,OAAOA,GAAQmN,WAE3FzO,GAEX0O,cAAe,SAAU/P,GACjBA,GAASA,EAAMuO,gBACfnR,KAAK4S,qBAAqBhQ,GAE9BgI,EAAW+E,GAAGgD,cAActO,KAAKrE,KAAM4C,IAE3CiQ,OAAQ,SAAUC,EAAOlQ,GACrB,GAAKA,EAAL,CAGA,KAAMA,YAAiB4M,KAAiB,CACpC,GAAImB,GAAY/N,CAChBA,GAAQ5C,KAAK+S,kBACbnQ,EAAMoQ,OAAOrC,GAMjB,QAJK3Q,KAAKiT,cAAgBrQ,EAAM0O,oBAAsB1O,EAAMsO,gBACxDtO,EAAQA,EAAMsO,aAAetO,EAAQA,EAAM4O,eAC3CxR,KAAKkT,kBAAkBtQ,IAEpBgI,EAAW+E,GAAGkD,OAAOxO,KAAKrE,KAAM8S,EAAOlQ,KAElDuQ,WAAY,SAAUC,GAClBpT,KAAKiT,cAAe,EACpBrI,EAAW+E,GAAGwD,WAAW9O,KAAKrE,KAAMoT,GACpCpT,KAAKiT,cAAe,GAExB7N,OAAQ,SAAUxC,GAMd,MALIA,GAAM0O,mBACNtR,KAAKqT,kBAAkBzQ,GAChBA,EAAMwO,eACbpR,KAAKkT,kBAAkBtQ,GAEpBgI,EAAW+E,GAAGvK,OAAOf,KAAKrE,KAAM4C,IAE3CyQ,kBAAmB,SAAUzQ,GAEzB,IADA,GAAIqB,GAAOjE,KAAKiE,OAAOqP,MAAM,GAAIC,EAAOtP,EAAKuP,QAASjC,EAAK3O,EAAM2O,GAC1DgC,GACCA,EAAKrC,eAAiBK,GACtB3G,EAAW+E,GAAGvK,OAAOf,KAAKrE,KAAMuT,GAEpCA,EAAOtP,EAAKuP,OAEhB5Q,GAAMgO,IAAIhE,GAAsB,KAEpCgG,qBAAsB,SAAUhQ,GAAV,GAEV6Q,GAEI9O,EACA+O,EACAjC,CALR7O,GAAMsO,eACFuC,EAAOzT,KAAKY,IAAIgC,EAAMsO,cACtBuC,IACI9O,EAAQ/B,EAAMiN,SAASlL,MACvB+O,EAAoBC,OAAO,MAAQjH,GAAsB,KAAOtC,EAAWwJ,kBAAkBjP,EAAO3E,KAAKiP,OAAO/L,WAChHuO,GAAuBgC,EAAKhC,qBAAuB,IAAI7T,QAAQ+O,GAAgCD,IAAqB9O,QAAQ,MAAO,IACnI8V,EAAcG,KAAKpC,GACnBgC,EAAK7C,IAAIhE,GAAsB6E,EAAoB7T,QAAQ8V,EAAe,MAE1E/O,EAAQ/B,EAAM+B,MACd+O,EAAoBC,OAAO,MAAQjH,GAAsB,KAAOtC,EAAWwJ,kBAAkBjP,EAAO3E,KAAKiP,OAAO/L,WAChHuQ,EAAK7C,IAAIhE,GAAsB6E,EAAoB7T,QAAQ8V,EAAe,SAK1FR,kBAAmB,SAAUtQ,GAAV,GAMPkR,GALJnP,EAAQ/B,EAAM+B,MACd6B,EAAOxG,KAAKiP,OAAO/L,SACnBuQ,EAAOzT,KAAKY,IAAIgC,EAAMsO,cACtBO,GAAuBgC,EAAKhC,qBAAuB,IAAI7T,QAAQ+O,GAAgCD,IAAqB9O,QAAQ,MAAO,GAClIwM,GAAW4G,YAAYS,EAAqB9M,EAAO6B,KAChDsN,EAAe1J,EAAWwJ,kBAAkBjP,EAAO6B,GACvD5D,EAAMiN,SAASlL,MAAQA,EACvB8O,EAAK7C,IAAIhE,GAAsB6E,GAAuBA,GAAuBqC,EAAepH,GAAsB,IAAMoH,MAWpI5B,IAAoB6B,OAAS,SAAU9S,IAC/B+J,EAAQ/J,IAAYA,YAAmBvB,GAAMuE,KAAKK,mBAClDrD,GAAYgD,KAAMhD,GAEtB,IAAIgH,GAAahH,MAAegD,EAAOgE,EAAWhE,IAElD,IADAgE,EAAWhE,KAAOA,IACZgE,YAAsBiK,MAAwBjK,YAAsBvI,GAAMuE,KAAK2G,WACjF,KAAUoJ,OAAM,8EAEpB,OAAO/L,aAAsBiK,IAAsBjK,EAAa,GAAIiK,IAAoBjK,IAE5FpI,GAAO,EAAMH,EAAMuE,MACfiO,oBAAqBA,GACrBnD,oBAAqBA,GACrBS,eAAgBA,KAEhBnG,GACAqH,QACIhT,KAAM,OACNuW,UAAW,gCAEfC,YACIxW,KAAM,SACNuW,UAAW,sBAEfE,SACIzW,KAAM,SACN0W,WAAY,YACZH,UAAW,+BACXI,UAAW,WAuGf/K,GACAgL,QACIC,UAAWhH,GACXiH,cAAepG,GACflL,SAAUuL,GACVrE,WAAY4D,GACZiE,YAAaxJ,EACbgM,kBAAmB3L,EACnB4L,UAAWpM,EACXkF,SAAUW,IAEdwG,SACIJ,UAAW3G,GACX4G,cAAejG,GACfrL,SAAUyL,GACVvE,WAAYyD,GACZoE,YAAaxJ,EACbgM,kBAAmB/L,EACnBgM,UAAWjN,IAGf8B,EAAS7J,EAAMkV,WAAW/U,QAC1BC,KAAM,SAAU+U,EAAS5T,GACrBvB,EAAMkV,WAAWjF,GAAG7P,KAAKuE,KAAKrE,MAC9BA,KAAK6U,QAAUA,EACf7U,KAAKiB,QAAUpB,GAAO,KAAUG,KAAKiB,QAASA,GAC9CjB,KAAK8U,aAAe9U,KAAKiB,QAAQ6T,aACjC9U,KAAK+U,4BAA8BlK,EAAM7K,KAAKgV,sBAAuBhV,OAEzEgV,sBAAuB,SAAUC,GAC7B,GAAe,YAAXA,EAAEzR,MAAqB,CACvB,GAAqHqR,GAASK,EAAiBC,EAA3IrP,EAAY9F,KAAK8F,UAAW0H,EAAWxN,KAAKmG,SAASlF,QAAQ2B,MAAM4K,SAAU4H,EAAgB1V,EAAMyD,KAAK,OAC5G2C,GAAUO,KAAK,IAAM+O,EAAgB,WAAaA,EAAgB,YAAYC,KAAK,WAC/ER,EAAUrX,EAAEwC,MACRwM,GAAwBqH,KAAKgB,EAAQ1R,KAAKiS,MAC1CF,EAAkBL,EAAQS,GAAG,IAAM5V,EAAMyD,KAAK,QAAU,qCACxDgS,EAAiB3H,IAAa0H,EAC9BL,EAAQ1R,KAAKzD,EAAMyD,KAAK,YAAagS,QAKrD5R,OAAQ,SAAU+F,EAAS1G,GAAnB,GAyEK2S,GACD7N,EAzEJgI,EAAO1P,KACP+N,EAAW2B,EAAKzO,QAAQ8M,SACxB7K,EAAWwM,EAAKzO,QAAQiC,SACxBoL,EAAQ,SAAU2G,GAClBA,EAAEO,iBACF9F,EAAK+F,oBAAoB7S,EAAO5C,OAEhCuD,IAEIC,MAAO,QACPmE,MAAOoG,EAAS2H,OAAO/N,QAGvBnE,MAAO,QACPmE,MAAOoG,EAAS2H,OAAO/Q,MACvB+Q,OAAQpM,EAAQiL,UAChBrR,SAAUA,IAGVM,MAAO,MACPmE,MAAOoG,EAAS2H,OAAO7Q,IACvB6Q,OAAQpM,EAAQiL,UAChBrR,SAAUA,IAGVM,MAAO,WACPmE,MAAOoG,EAAS2H,OAAOC,YACvBD,OAAQpM,EAAQkE,UAGpB9N,GAAMwD,SAAS0S,gBACfrS,EAAOpF,MACHqF,MAAO,WACPmE,MAAOoG,EAAS2H,OAAOxS,SACvBwS,OAAQpM,EAAQkL,cAChBlG,MAAOA,EACPP,SAAUA,EAAS2H,OACnB9S,MAAOA,IAEXW,EAAOpF,MACHqF,MAAO,gBACPmE,MAAOoG,EAAS2H,OAAO5S,cACvB4S,OAAQpM,EAAQpG,SAChBmL,WAAYN,EAAS2H,OAAOrH,aAEhC9K,EAAOpF,MACHqF,MAAO,cACPmE,MAAOoG,EAAS2H,OAAO3S,YACvB2S,OAAQpM,EAAQpG,SAChBmL,WAAYN,EAAS2H,OAAOrH,cAG/BzL,EAAMsO,cACP3N,EAAOpF,MACHqF,MAAO,iBACPmE,MAAOoG,EAAS2H,OAAOG,OACvBH,OAAQpM,EAAQc,WAChBlH,SAAUA,EACV6K,SAAUA,EAAS+H,iBACnB5H,KAAMlO,KAAKkO,OAGf,eAAiBtL,IACjBW,EAAOpF,MACHqF,MAAO,cACPmE,MAAOoG,EAAS2H,OAAOzD,YACvByD,OAAQpM,EAAQ2I,aACZrP,MAAOA,EACPY,MAAO,iBAInB,KAAS+R,EAAgB,EAAGA,EAAgBvV,KAAKiB,QAAQyT,UAAUhW,OAAQ6W,IACnE7N,EAAW1H,KAAKiB,QAAQyT,UAAUa,GACtChS,EAAOpF,MACHqF,MAAOkE,EAASlE,MAChBmE,MAAOD,EAASC,MAChB+N,OAAQhO,EAASqO,SAAWzM,EAAQmL,kBAAkB/M,EAAU9E,GAAS0G,EAAQoL,UAAUhN,EAAU9E,IAG7G,OAAOW,IAEXsB,IAAK,WACD,MAAO7E,MAAKmG,SAAStB,OAEzBmR,0BAA2B,SAAUpT,EAAOW,EAAQ0S,GAAzB,GAKdvR,GAAShG,EACV8E,EAYI0S,EAjBRnI,EAAW/N,KAAKiB,QAAQ8M,SACxBoI,EAAWtW,KAAWH,EAAM0W,SAAUpW,KAAKiB,QAAQoV,kBACnDC,EAAYH,EAASG,UACrBC,EAAO,EACX,KAAS7R,EAAM,EAAGhG,EAAS6E,EAAO7E,OAAQgG,EAAMhG,EAAQgG,IAChDlB,EAAQD,EAAOmB,GACC,kBAAhBlB,EAAMA,QACN+S,GAAQ,mGACRA,GAAQ,sCACRA,GAAQ,mCACRA,GAAQ,uGAAyGxI,EAAS2H,OAAOc,kBAAoB,kBAEzJD,GAAQ,yCAA2C/S,EAAMA,MAAQ,MAAQA,EAAMmE,OAASnE,EAAMA,OAAS,IAAM,kBACxGZ,EAAMuD,UAAYvD,EAAMuD,SAAS3C,EAAMA,QACxCyS,EAAe9X,KAAKqF,GACpB+S,GAAQ,QAAU7W,EAAMyD,KAAK,iBAAmB,KAAOK,EAAMA,MAAQ,kCAEjE0S,EAAO,KACP1S,EAAMA,OACNA,EAAQ9D,EAAM+W,KAAKjT,EAAMA,MAAO8S,GAChCJ,GAAQ1S,EAAQ,aAAiBA,GAEjC0S,GAAQ,KAEZA,GAAQ,IACRA,EAAOxW,EAAM0I,SAAS8N,EAAMC,GAC5BI,GAAQ,6BAA+BL,EAAKtT,GAAS,UAErC,gBAAhBY,EAAMA,QACN+S,GAAQvW,KAAK0W,2BAGrB,OAAOH,IAEXI,yBAA0B,SAAU/T,EAAOW,EAAQ0S,GAAzB,GAMbvR,GAAShG,EACV8E,EAsBI0S,EA5BRnI,EAAW/N,KAAKiB,QAAQ8M,SACxBoI,EAAWtW,KAAWH,EAAM0W,SAAUpW,KAAKiB,QAAQoV,kBACnDC,EAAYH,EAASG,UACrBC,EAAO,EAEX,KADAA,GAAQ,OACC7R,EAAM,EAAGhG,EAAS6E,EAAO7E,OAAQgG,EAAMhG,EAAQgG,IAChDlB,EAAQD,EAAOmB,GACC,aAAhBlB,EAAMA,OAAwC,mBAAhBA,EAAMA,QACpC+S,GAAQ,aAEQ,kBAAhB/S,EAAMA,QACN+S,GAAQ,mGACRA,GAAQ,iDACRA,GAAQ,8BAAgCxI,EAAS2H,OAAOc,kBAAoB,UAC5ED,GAAQ,uEACRA,GAAQ,kBAEP3T,EAAMuD,UAAYvD,EAAMuD,SAAS3C,EAAMA,QACxC+S,GAAQ,sBAEJA,GADgB,aAAhB/S,EAAMA,MACE,0EAEA,0BAEZ+S,GAAQ,+BAAiC/S,EAAMmE,OAASnE,EAAMA,OAAS,IAAM,UAC7EyS,EAAe9X,KAAKqF,GACpB+S,GAAQ,QAAU7W,EAAMyD,KAAK,iBAAmB,KAAOK,EAAMA,MAAQ,aAEjE0S,EAAO,KACXK,GAAQ,sBACRA,GAAQ,qCACRA,GAAQ,+BAAiC/S,EAAMmE,OAASnE,EAAMA,OAAS,IAAM,UACzEA,EAAMA,OACNA,EAAQ9D,EAAM+W,KAAKjT,EAAMA,MAAO8S,GAChCJ,GAAQ1S,EAAQ,aAAiBA,GAEjC0S,GAAQ,KAEZA,GAAQ,IACRA,EAAOxW,EAAM0I,SAAS8N,EAAMC,GAC5BI,GAAQ,6BAA+BL,EAAKtT,GAAS,WAEzD2T,GAAQ,gBACY,mBAAhB/S,EAAMA,QACN+S,GAAQ,aAEQ,gBAAhB/S,EAAMA,QACN+S,GAAQvW,KAAK0W,2BAIrB,OADAH,IAAQ,SAGZK,mBAAoB,SAAUhU,EAAOW,EAAQ0S,EAAgBY,GAAzC,GACZV,GAAWtW,KAAWH,EAAM0W,SAAUpW,KAAKiB,QAAQoV,kBACnDjO,EAAWpI,KAAKiB,QAAQkF,SAASiC,SACjCmO,EAAO,EAWX,OAVInO,UACWA,KAAaqC,IACpBrC,EAAW3I,OAAOqX,SAAS1O,IAE/BmO,GAAQ7W,EAAM0I,SAASA,EAAU+N,GAAUvT,IAE3C2T,GADOM,EACC,4BAA8B7W,KAAK2W,yBAAyB/T,EAAOW,EAAQ0S,GAAkB,SAE7FjW,KAAKgW,0BAA0BpT,EAAOW,EAAQ0S,GAEnDM,GAEXG,yBAA0B,WACtB,MAAO,eAEXK,iBAAkB,SAAUnU,GACxBA,EAAMgO,IAAI,gBAAiB5Q,KAAKgX,gBAChCpU,EAAMgO,IAAI,cAAe5Q,KAAKiX,oBACvBjX,MAAKgX,qBACLhX,MAAKiX,gBAGhBzN,EAAeD,EAAO1J,QACtBC,KAAM,WACFyJ,EAAOoG,GAAG7P,KAAKuS,MAAMrS,KAAMkX,WAC3BlX,KAAKkO,KAAOxO,EAAMyX,KAAKC,KAAKpX,KAAK6U,SAC7BwC,YACIC,aACIC,cAAc,EACdH,MAAM,EACNI,gBAAgB,EAChBC,cAAc,EACdC,iBAAiB,MAI7B1X,KAAKkO,KAAK2G,QAAQ8C,SAASC,IAAI,SAAU5X,KAAKiB,QAAQnC,QACtDkB,KAAKuI,KAAOvI,KAAKkO,KAAK3F,QAE1BtH,SACI4W,YACIC,KAAM,QACNC,MAAO,gBAGf5D,QAAS,WACLnU,KAAKgY,QACLhY,KAAKiY,SACLjY,KAAKkO,KAAKiG,WAEdsB,oBAAqB,SAAU7S,GAAV,GAsBT2T,GArBJ7G,EAAO1P,KACPkO,EAAOwB,EAAKxB,KACZH,EAAW2B,EAAKzO,QAAQ8M,SACxBmK,EAAexI,EAAKwI,aACpBpS,EAAYoS,EAAeA,EAAaC,QAAQ9R,KAAK,0BAA4BqJ,EAAK5J,UAAUO,KAAK,0BACrG+R,EAAUtS,EAAUO,KAAK,2BAA2BpC,KAAK,eACzDoU,EAAiBvS,EAAUO,KAAK,qCAChCiS,EAAsB,SAAUrD,GAChC,GAAgB,kBAAZA,EAAEzR,MAA2B,CAC7B,GAAInD,GAAQuC,EAAME,aAClBsV,GAAQG,OAAOlY,GACVA,IACDgY,EAAe1K,OACf/K,EAAMgO,IAAI,cAAe,IACzBwH,EAAQ/X,OAAM,KAI1BqP,GAAKsH,eAAiBpU,EAAME,eAAiB,GAC7C4M,EAAKuH,aAAerU,EAAMG,aAAe,GACpCmV,IACG3B,EAAO,uMAAmNxI,EAASyK,OAAS,gBAAuBzK,EAASyK,OAAS,4DAA8DzK,EAAS2H,OAAO+C,cAAgB,wEAA0E1K,EAAS2K,KAAO,iBAAwB3K,EAAS2K,KAAO,oFACzf1Y,KAAKkY,aAAeA,EAAehK,EAAKyK,OAAOpC,GAC/C2B,EAAaU,eAAeD,OAAO7S,EAAU+S,QAC7CX,EAAarD,QAAQiE,GAAG5N,GAAQD,EAAI,2CAA4C,SAAUgK,GAAV,GAOxE8D,GACArb,CAPJuX,GAAEO,iBACFP,EAAE+D,kBACExb,EAAEwC,MAAMiZ,SAAS,uBACjBvJ,EAAKqH,iBAAiBnU,GAE1BA,EAAMqV,OAAO,SAAUK,GACnBS,EAAWrJ,EAAKwJ,UAChBxb,EAAOiF,EAAmBC,EAAOmL,EAAS2H,OAAOrH,YACrD0K,EAASZ,QAAQ9R,KAAK,qBAAqB3I,KAAKA,GAChDwQ,EAAKlH,SAAS+R,EAAUrJ,EAAKzO,QAAQ4W,WAAWE,SAEpDK,EAAQe,KAAK,SAAU,SAAUC,GAC7Bf,EAAexJ,OAAOuK,EAAGC,SACzBzW,EAAMgO,IAAI,cAAe,MAE7BhO,EAAMuW,KAAK,SAAUb,IAEzBF,EAAQ/X,QAAQuC,EAAMG,aACtBqV,EAAQG,SAAS3V,EAAME,eACnBF,EAAMG,YACNsV,EAAeQ,OAEfR,EAAe1K,OAEnBO,EAAKlH,SAASkR,EAAcxI,EAAKzO,QAAQ4W,WAAWC,OAExDwB,WAAY,SAAUrY,GAClB,GAAIsY,GAAUtY,EAAQuY,QAAQlZ,IAAI,SAAUmZ,GACxC,OACI/b,KAAM+b,EAAO/b,KACbgc,OAAQD,EAAOnL,QAGvBiL,GAAQpb,MACJT,KAAMsC,KAAKiB,QAAQ8M,SAASyK,OAC5BmB,SAAS,IAEbnc,EAAE,WAAWqK,SAAS/G,SAASe,MAAM+X,aACjC5B,MAAO,WACHhY,KAAKmU,WAET0F,OAASC,eAAe,GACxBC,UAAU,EACVpS,OAAO,EACPwQ,QAASlX,EAAQvD,KACjB6b,QAASA,KAGjBS,UAAW,SAAUpX,GAAV,GAUHW,GACAmM,EACAuG,EAGA1N,EAEI0R,EAIAC,EAIJpU,EAxBAoI,EAAOlO,KAAKkO,KACZqI,EAAO,GACPxI,EAAW/N,KAAKiB,QAAQ8M,SACxBoM,EAAapM,EAAS2K,KACtB0B,EAAarM,EAASoG,QACtBkG,EAAatM,EAASyK,OACtB8B,EAAYvM,EAAS2H,OAAO6E,YAC5BC,EAAczM,EAASyM,WA0D3B,OAzDAjE,IAAQ,sFAAwF7W,EAAMyD,KAAK,OAAS,KAAOP,EAAMkC,IAAM,qHAAiIuV,EAAa,gBAAuBA,EAAa,4DAA8DC,EAAY,wEAA0EH,EAAa,iBAAwBA,EAAa,qDAC3f5W,EAASvD,KAAKuD,OAAO+F,EAAQgL,OAAQ1R,GACrC8M,EAAO1P,KACPiW,KACJM,GAAQvW,KAAK4W,mBAAmBhU,EAAOW,EAAQ0S,GAAgB,GAC/DM,GAAQ,SACJhO,EAAO2F,EAAKyK,OAAOpC,IAClB3T,EAAMqO,SAAWjR,KAAKiB,QAAQkF,UAAYnG,KAAKiB,QAAQkF,SAASgO,WAAY,GAASvR,EAAM0O,oBAAsB1O,EAAM6O,sBACpHwI,EAAiB,oHAAsHO,EAAc,KAAOA,EAAc,oBAC9KjS,EAAKqQ,eAAeD,OAAOsB,KAE1BrX,EAAMqO,SAAWjR,KAAKiB,QAAQkF,UAAYnG,KAAKiB,QAAQkF,SAASgO,WAAY,IACzE+F,EAAY,+GAAiHE,EAAa,KAAOA,EAAa,oBAClK7R,EAAKqQ,eAAeD,OAAOuB,IAE/Bla,KAAKkZ,UAAY3Q,EACbzC,EAAY9F,KAAK8F,UAAYyC,EAAKsM,QACtC7U,KAAKmG,SAAWL,EAAU2U,eACtBlX,OAAQ0S,EACRrT,MAAOA,EACP8X,gBAAgB,EAChBC,OAAQjL,EAAKzO,QAAQ0Z,OACrBC,gBAAgB,IACjB3W,KAAK,iBACHjE,KAAK6a,QAAQ,QACV/U,UAAWA,EACXlD,MAAOA,IA0BX5C,KAAK6a,QAAQ,UACT/U,UAAWA,EACXlD,MAAOA,KA1BXkD,EAAUgT,GAAG5N,GAAQD,EAAI,wHAAyH,SAAUgK,GAAV,GAG1IwE,GAEI7S,CAJRqO,GAAEO,iBACFP,EAAE+D,kBACES,EAASjc,EAAEwC,MACVyZ,EAAOR,SAAS,oBAcjB/K,EAAKlH,SAAShH,KAAKkZ,UAAWxJ,EAAKzO,QAAQ4W,WAAWE,QAblDnR,EAAO,SACP6S,EAAOR,SAAS,sBAChBrS,EAAO,OACA6S,EAAOR,SAAS,sBACvBrS,EAAO,SACA6S,EAAOR,SAAS,6BACvBrS,EAAOoF,IAEX0D,EAAKmL,QAAQjU,GACTd,UAAWA,EACXlD,MAAOA,OAMnBsL,EAAKlH,SAASuB,EAAMmH,EAAKzO,QAAQ4W,WAAWC,MAC5ClV,EAAMuW,KAAK,SAAUzJ,EAAKqF,8BAOvB/U,KAAKmG,UAEhB2U,OAAQ,WACJ,MAAO9a,MAAKkO,KAAK2G,QAAQxO,KAAK3G,EAAMqb,aAAa,SAASC,IAAIhb,KAAKuI,KAAKsM,UAE5EmD,MAAO,WAAA,GAGKiD,GACA1S,EACK7D,EAAShG,CAJtB,IAAIsB,KAAK8F,UAAW,CAIhB,IAHA9F,KAAKkO,KAAKlH,SAAS,GAAIhH,KAAKiB,QAAQ4W,WAAWE,OAC3CkD,EAAQjb,KAAK8a,SAERpW,EAAM,EAAGhG,EAASuc,EAAMvc,OAAQgG,EAAMhG,EAAQgG,IACnD6D,EAAO0S,EAAMC,GAAGxW,GAAKT,KAAK,aACtBsE,GACAA,EAAK4S,OAGbF,GAAM7V,SACNpF,KAAK8F,UAAY,KACb9F,KAAKmG,WACLnG,KAAKmG,SAASlF,QAAQ2B,MAAMqV,OAAO,SAAUjY,KAAK+U,6BAClD/U,KAAKmG,SAASgO,UACdnU,KAAKmG,SAAW,MAEpBnG,KAAKkY,aAAe,SAI5BzO,EAAcF,EAAO1J,QACrBsU,QAAS,WACLnU,KAAKgY,QACLhY,KAAKiY,UAET+B,UAAW,SAAUpX,GAAV,GAYHO,GACAlC,EA0BA6E,EAtCA4J,EAAO1P,KACPmG,EAAWuJ,EAAKzO,QAAQkF,SACxBoQ,EAAO,QAAU7W,EAAMyD,KAAK,OAAS,KAAOP,EAAMkC,IAAM,wFACxDiJ,EAAW2B,EAAKzO,QAAQ8M,SACxBoM,EAAapM,EAAS2K,KACtB2B,EAAatM,EAASyK,OACtB4C,EAAarN,EAASoG,QACtBqG,EAAczM,EAASyM,YACvBjX,EAASvD,KAAKuD,OAAO+F,EAAQqL,QAAS/R,GACtCqT,IAkGJ,OAjGAM,IAAQvW,KAAK4W,mBAAmBhU,EAAOW,EAAQ0S,GAAgB,GAE3DhV,EAAU0C,EAAcwC,GAAYA,EAAS1G,UACjD8W,GAAQ,+CACRA,GAAQvW,KAAK8U,cACTlO,KAAM,SACNlJ,KAAMyc,EACNhX,KAAMA,IACLnD,KAAK8U,cACNlO,KAAM,aACNlJ,KAAM2c,EACNlX,KAAMA,KAELP,EAAMqO,SAAW9K,EAASgO,WAAY,GAASvR,EAAM0O,oBAAsB1O,EAAM6O,sBAClF8E,GAAQvW,KAAK8U,cACTlO,KAAM,cACNlJ,KAAM8c,EACNrX,KAAMA,KAGTP,EAAMqO,SAAW9K,EAASgO,WAAY,IACvCoC,GAAQvW,KAAK8U,cACTlO,KAAM,SACNlJ,KAAM0d,EACNjY,KAAMA,KAGdoT,GAAQ,qBACJzQ,EAAY9F,KAAK8F,UAAYtI,EAAE+Y,GAAM1O,SAAS6H,EAAKmF,SAASqG,GAAG,GAAGG,YAAYxb,GAC9Ega,OAAO,EACPyB,WAAW,EACXC,WAAW,EACX5T,MAAOoG,EAAS2H,OAAO6E,YACvB3L,SAAS,EACToJ,MAAO,SAAU/C,GACTA,EAAEuG,eACE9L,EAAKmL,QAAQ/O,IACThG,UAAWA,EACXlD,MAAOA,KAEXqS,EAAEO,mBAIfvU,IACHyO,EAAKvJ,SAAWL,EAAU2U,eACtBlX,OAAQ0S,EACRrT,MAAOA,EACP8X,gBAAgB,EAChBE,gBAAgB,EAChBD,OAAQjL,EAAKzO,QAAQ0Z,SACtB1W,KAAK,iBACHyL,EAAKmL,QAAQ1O,IACVrG,UAAWA,EACXlD,MAAOA,IAsCX8M,EAAKmL,QAAQ/O,IACThG,UAAWA,EACXlD,MAAOA,KAtCXkD,EAAU7B,KAAK,eAAewX,SAASC,OACvC5V,EAAUgT,GAAG5N,GAAQD,EAAI,uBAAwB,SAAUgK,GACvDA,EAAEO,iBACFP,EAAE+D,kBACFtJ,EAAKmL,QAAQ/O,IACThG,UAAWA,EACXlD,MAAOA,MAGfkD,EAAUgT,GAAG5N,GAAQD,EAAI,uBAAwB,SAAUgK,GACvDA,EAAEO,iBACFP,EAAE+D;AACFtJ,EAAKmL,QAAQ,QACT/U,UAAWA,EACXlD,MAAOA,MAGfkD,EAAUgT,GAAG5N,GAAQD,EAAI,uBAAwB,SAAUgK,GACvDA,EAAEO,iBACFP,EAAE+D,kBACFtJ,EAAKmL,QAAQ9O,IACTjG,UAAWA,EACXlD,MAAOA,MAGfkD,EAAUgT,GAAG5N,GAAQD,EAAI,4BAA6B,SAAUgK,GAC5DA,EAAEO,iBACFP,EAAE+D,kBACFtJ,EAAKmL,QAAQ7O,IACTlG,UAAWA,EACXlD,MAAOA,MAGflD,EAAMic,UAAU7V,GAChBlD,EAAMuW,KAAK,SAAUzJ,EAAKqF,8BAOvBrF,EAAKvJ,UAEhB6R,MAAO,WAAA,GACCtI,GAAO1P,KACPmU,EAAU,WACNzE,EAAKvJ,WACLuJ,EAAKvJ,SAASlF,QAAQ2B,MAAMqV,OAAO,SAAUvI,EAAKqF,6BAClDrF,EAAKvJ,SAASgO,UACdzE,EAAKvJ,SAAW,KAChBuJ,EAAK5J,UAAY,MAEjB4J,EAAKkM,QACLlM,EAAKkM,MAAMzH,UACXzE,EAAKkM,MAAQ,MAGjBlM,GAAKvJ,UACDuJ,EAAKmM,gBAAkBnM,EAAKmM,eAAe5X,KAAK,iBAChDyL,EAAKmM,eAAe5X,KAAK,eAAekQ,UACxCzE,EAAKmM,eAAiB,MAEtBnM,EAAK5J,UAAUwP,GAAG,YAClB5F,EAAK5J,UAAU7B,KAAK,eAAekV,KAAK,aAAchF,GAAS6D,QAE/D7D,KAGJA,KAGRuC,yBAA0B,WAAA,GAClB3I,GAAW/N,KAAKiB,QAAQ8M,SACxBwI,EAAO,EAUX,OATAA,IAAQ,+CACRA,GAAQvW,KAAK8U,cACTlO,KAAM,eACNlJ,KAAMqQ,EAAS2K,OACd1Y,KAAK8U,cACNlO,KAAM,iBACNlJ,KAAMqQ,EAASyK,SAEnBjC,GAAQ,sBAGZ+C,WAAY,SAAUrY,GAAV,GAGC6a,GAILC,EAIAH,EAVArF,EAAO7W,EAAMkG,OAAO,uGAA8G3E,EAAQvD,KAE9I,KADA6Y,GAAQ,+CACCuF,EAAc,EAAGA,EAAc7a,EAAQuY,QAAQ9a,OAAQod,IAC5DvF,GAAQvW,KAAK8U,aAAa7T,EAAQuY,QAAQsC,GAE9CvF,IAAQ,qBACJwF,EAAU/b,KAAK6U,QACf7U,KAAK4b,OACL5b,KAAK4b,MAAMzH,UAEXyH,EAAQ5b,KAAK4b,MAAQpe,EAAE+Y,GAAM1O,SAASkU,GAASb,GAAG,GAAGpC,GAAG5N,GAAO,YAAa,SAAU+J,GACtFA,EAAEO,iBACFoG,EAAM5D,OACN,IAAI8D,GAActe,EAAEyX,EAAE+G,eAAelJ,OACrC7R,GAAQuY,QAAQsC,GAAaxN,UAC9B+M,aACCxB,OAAO,EACPyB,WAAW,EACXC,WAAW,EACX5T,MAAO1G,EAAQ0G,MACfiH,SAAS,EACToJ,MAAO,WACHhY,KAAKmU,UACL4H,EAAQE,WAEbC,iBACHN,EAAMH,SAASC,QAEnBjG,oBAAqB,SAAU7S,EAAOuZ,GAAjB,GAmBbC,GAlBA1M,EAAO1P,KACP8F,EAAY4J,EAAK5J,UAAUO,KAAK,0BAChCgW,EAAWvW,EAAUO,KAAK,2BAC1BgS,EAAiBvS,EAAUO,KAAK,sBAAsBU,IAAIjB,EAAUO,KAAK,uBACzEiW,EAAaxW,EAAUO,KAAK,6BAC5BkW,EAAezW,EAAUO,KAAK,+BAC9BmW,EAAgB9M,EAAKmM,eACrBvD,EAAsB,SAAUrD,GAChC,GAAgB,kBAAZA,EAAEzR,MAA2B,CAC7B,GAAInD,GAAQuC,EAAME,aAClBuZ,GAASI,KAAK,YAAapc,GACtBA,IACDgY,EAAe1K,OACf/K,EAAMgO,IAAI,cAAe,IACzByL,EAASI,KAAK,WAAW,KAKrC/M,GAAKsH,eAAiBpU,EAAME,cAC5B4M,EAAKuH,aAAerU,EAAMG,YACrByZ,IACD9M,EAAKmM,eAAiBW,EAAgB1W,EAAUuV,aAC5CxB,OAAO,EACPyB,WAAW,EACXC,WAAW,EACX5T,MAAO+H,EAAKzO,QAAQ8M,SAAS2H,OAAOgH,oBACpC9N,SAAS,EACToJ,MAAO,SAAU/C,GACbrS,EAAMqV,OAAO,SAAUK,GACnBrD,EAAEuG,eACF9L,EAAKqH,iBAAiBnU,GAEtBuZ,GACAA,EAAUF,WAItBI,EAAS/N,MAAM,WACX+J,EAAexJ,OAAOwN,EAASI,KAAK,YACpC7Z,EAAMgO,IAAI,cAAe,MAE7B0L,EAAWhO,MAAM,SAAU2G,GACvBA,EAAEO,iBACF4G,EAAIpE,UAERuE,EAAajO,MAAM,SAAU2G,GACzBA,EAAEO,iBACF9F,EAAKqH,iBAAiBnU,GACtBwZ,EAAIpE,UAERpV,EAAMuW,KAAK,SAAUb,IAEzB+D,EAASI,KAAK,UAAW7Z,EAAMG,aAAa0Z,KAAK,YAAa7Z,EAAME,eAChEF,EAAMG,YACNsV,EAAeQ,OAEfR,EAAe1K,OAEnByO,EAAMI,EAAcvY,KAAK,eACzBmY,EAAIX,SAASC,UAGjBhS,EAAYc,EAAgB3K,QAC5BC,KAAM,SAAU+U,EAAS5T,GACrB,GAAIyO,GAAO1P,IACXuK,GAAOoF,GAAG7P,KAAKuE,KAAKqL,EAAMmF,EAAS5T,GAC9ByO,EAAKzO,QAAQga,OAAUvL,EAAKzO,QAAQga,MAAMvc,SAC3CgR,EAAKzO,QAAQga,OACT,MACA,SAGRvL,EAAKgF,aACLhF,EAAKiN,aACLjN,EAAKkN,WACLlN,EAAKoL,SACLpL,EAAKmN,WACLnN,EAAKoN,cACLpN,EAAKqN,aACLrN,EAAKsN,eAAiB,WAClBtN,EAAKuN,UAETvN,EAAKqM,QAAQjD,GAAG3N,GAAYF,EAAK,eAAiBA,EAAI,SAAUgK,GACvDzX,EAAEyX,EAAE0F,QAAQrF,GAAG,oBAChBL,EAAEO,mBAGN9F,EAAKzO,QAAQkF,UAAYuJ,EAAKzO,QAAQkF,SAAS8W,UAAW,GAC1DvN,EAAKwN,aAETxN,EAAKyN,WACLzN,EAAK0N,cACD1N,EAAKzO,QAAQ8M,UAAY2B,EAAKzO,QAAQ8M,SAAS3D,aAC/CA,EAAWnJ,QAAUyO,EAAKzO,QAAQ8M,SAAS3D,YAE/CsF,EAAK2N,cACL3N,EAAK4N,iBACL5N,EAAK6N,QAAU7d,EAAM8d,OACrB9N,EAAK+N,iBAETL,YAAa,WACT5f,EAAEiC,QAAQqZ,GAAG,SAAW7N,EAAIjL,KAAKgd,iBAErCU,cAAe,WACXlgB,EAAEiC,QAAQke,IAAI,SAAW1S,EAAIjL,KAAKgd,iBAEtCY,UAAW,WAAA,GAOHpf,GACAP,EAMA4f,EAEIpZ,EAKJqZ,EApBApO,EAAO1P,KACPoT,EAAQ1D,EAAK0D,QACb7M,EAASmJ,EAAKqO,MACdC,EAAaxgB,EAAE8C,IAAI8S,EAAO,SAAUG,GACpC,MAAO/V,GAAE+V,GAAMpQ,KAAK,cAIpB8a,KACAC,EAAmBF,EAAWtf,MAClC,KAAKF,EAAI,EAAGA,EAAI0f,EAAkB1f,IAC9Byf,EAAKD,EAAWxf,IAAM,IAG1B,KADIqf,EAActX,EAAO7H,OACpBF,EAAI,EAAGA,EAAIqf,EAAarf,IACrBiG,EAAQ8B,EAAO/H,GACfyf,EAAKxZ,EAAMK,OAASpC,IACpBub,EAAKxZ,EAAMK,KAAOL,EAGtBqZ,KACJ,KAAK7f,IAAOggB,GACRH,EAAW3f,KAAK8f,EAAKhgB,GAEzB,OAAO6f,IAEXK,UAAW,WACP,GAAIld,GAAUjB,KAAKiB,OACnB,OAAOA,GAAQqT,UAAW,GAAQ5U,EAAM2L,QAAQ+S,UAA+B,UAAnBnd,EAAQqT,QAAyC,WAAnBrT,EAAQqT,QAEtG+J,SAAU,SAAU5Z,GAChB,MAAO,QAAQoP,KAAKpP,EAAMoN,OAASpN,EAAM6Z,eAAiB,QAAQzK,KAAKpP,EAAM6Z,cAAcC,cAE/FC,gBAAiB,SAAUC,GACvB,MAAOA,GAAaxF,SAAS1M,KAEjCmS,kBAAmB,SAAUhK,EAAWiK,EAAYC,EAAaC,EAAkBC,GAAhE,GAIXpX,GAEIqX,EACA9a,EACA+a,EACKC,EACDC,EACAC,CANZ,IAJKP,IACDA,MAEAlX,EAAWgN,EAAU,GACX,CAIV,IAFIzQ,EAAOyD,EAASO,WAAWM,OAC3ByW,EAAY,EACPC,EAAY,EAAGA,EAAYhb,EAAKvF,OAAQugB,IACzCC,EAAaxf,EAAM8I,OAAOd,EAASM,gBAAgB/D,EAAKgb,IACxDE,EAAoBR,EAAaK,EAAYC,EACjDF,EAAQ/e,KAAK0e,kBAAkBhK,EAAUpB,MAAM,GAAI6L,EAAmBP,EAAaM,EAAYxX,EAASlE,OACxGub,EAAMrX,EAASlE,OAAS0b,EACxBF,EAAYD,EAAMJ,WACdG,GAAeD,IACfE,EAAMD,GAAeD,GAEA,IAArBnK,EAAUhW,SACVqgB,EAAMJ,WAAaA,EAAaM,EAChCL,EAAYzgB,KAAK4gB,GAGzB,OAAOA,GAEP,UAGR9a,KAAM,WACF,MAAOjE,MAAK+d,OAEhBqB,OAAQ,SAAUne,GAAV,GAKAoe,GAEIC,EACAC,EAyBAC,EACAjN,EACAkN,EAmBAC,EACAC,EACAZ,EACAa,EAxDJlQ,EAAO1P,KACPuI,EAAOmH,EAAKnH,OACZsX,EAAYnQ,EAAKoQ,WACjBxQ,EAAS/G,EAAK+G,MAElB,IAAIrO,IAAYyB,EAGZ,MADI6c,GAAQhX,EAAKwX,eACZF,GAGDA,GAAaA,EAAUtZ,SACvB+Y,EAAiB5P,EAAKsQ,oBAGtBrb,MAAOkb,EAAUlb,MACjBE,IAAKgb,EAAUhb,IACf0B,OAAQ+Y,EACRC,MAAOA,EACP7K,UAAWnM,EAAK0X,gBAAgBJ,OAGxC,KAAK5e,EAID,MAHAyO,GAAKoQ,WAAa,KAClBpQ,EAAKwQ,KAAO,KACZ3X,EAAK4X,iBACL,CAKJ,IAHI3iB,EAAEwN,QAAQ/J,KACVA,GAAYsF,OAAQtF,EAAQmf,OAAO,KAEnCnf,EAAQyT,UAAW,CAEfnC,KACAkN,KACAlX,EAAK8X,kBACL3Q,EAAKgP,kBAAkBnW,EAAK8X,iBAAkB,EAAGZ,EAErD,KAAKD,IAAave,GAAQyT,UACtBnC,EAAQpU,MACJqF,MAAOgc,EACPhN,SAAU,KACVnS,MAAOY,EAAQyT,UAAU8K,IAGjCH,GAAiB,GAAI3f,GAAMuE,KAAKwO,MAAMgN,GAAkBla,OAAOgN,GAASG,UAE5E,MAAIzR,GAAQsF,QAAUtF,EAAQsF,OAAO7H,QACjCgR,EAAK4Q,cAAcrf,EAAQsF,OAAQ8Y,GACnC3P,EAAK6Q,UACL,IAEAjR,GAAWrO,EAAQ0D,OAAS1D,EAAQ4D,MAChC6a,EAAaxV,EAAQ3B,EAAKiY,YAC1Bb,EAAWjgB,EAAMuF,KAAKwb,QAAQvW,EAAQ3B,EAAKmY,UAAW,GAGtDzf,EAAQ0D,MAAQgb,GAAYD,GAAcze,EAAQ4D,MAE9Cka,EADAM,GAAkBA,EAAe3gB,OACzB4Q,EAAO+P,EAAe,GAAGV,YAEzBrP,EAAO,GAEdyP,EAAM4B,4BACP1f,EAAQuM,UAAW,GAEvBoS,EAASb,EAAMa,OAAO3e,EAAQ0D,MAAO1D,EAAQ4D,IAAK5D,EAAQuM,UAAU,GAChEoS,EAAOlhB,SACPgR,EAAKoQ,YACDnb,MAAOjF,EAAMwD,SAAS0d,YAAYhB,EAAO,GAAGjb,MAAMA,OAClDE,IAAKnF,EAAMwD,SAAS0d,YAAYhB,EAAOA,EAAOlhB,OAAS,GAAGmG,IAAIA,KAC9D8Z,WAAYiB,EAAO,GAAGjb,MAAMga,WAC5B7L,MAAO8M,EAAO,GAAGjb,MAAMmO,MACvBtF,SAAUoS,EAAO,GAAGjb,MAAMkc,UAC1Bta,WAEJmJ,EAAK6Q,aAxBjB,IA6BJD,cAAe,SAAUtC,EAAYqB,GAAtB,GAEP3a,GAOQoc,EACAva,EACAoa,EACAI,EACKC,EAGAC,EAYLpM,EA3BRnF,EAAO1P,KAEPuI,EAAOmH,EAAKnH,OACZ+G,EAAS/G,EAAK+G,OACd4R,EAAelD,EAAWtf,OAC1ByiB,EAAY9B,GAAkBA,EAAe3gB,MACjD,KAAKgG,EAAM,EAAGA,EAAMwc,EAAcxc,IAC9B,GAAI4K,GAAU6R,EAAW,CAKrB,IAJIL,EAAexR,EAAO+P,EAAe,GAAGV,YACxCpY,KACAoa,EAA0BG,EAAaH,0BACvCI,EAAyBD,EAAaC,yBACjCC,EAAU,EAAGA,EAAUL,EAAyBK,IACrDza,EAASA,EAAOE,OAAOqa,EAAaM,sBAAsBJ,GAASza,SAEvE,KAAS0a,EAAa,EAAGA,EAAaF,EAAwBE,IAC1D1a,EAASA,EAAOE,OAAOqa,EAAaO,qBAAqBJ,GAAY1a,SAEzEA,GAAS,GAAI7G,GAAMuE,KAAKwO,MAAMlM,GAAQhB,QAClC/B,MAAO,sCACPgP,SAAU,KACVnS,MAAO2d,EAAWtZ,KACnBgO,UACCnM,EAAO,IACPmJ,EAAK4R,iBAAiB/a,EAAO,GAAGsO,aAGhCA,GAAUtM,EAAKsM,QAAQxO,KAAK3G,EAAMkG,OAAO,gDAAiDoY,EAAWtZ,KACrGmQ,EAAQnW,QACRgR,EAAK4R,iBAAiBzM,EAAQ,KAK9CyI,eAAgB,WAAA,GAERiE,GACAC,EACAC,EACAC,EACAC,EALAjS,EAAO1P,KAMP+b,EAAUrM,EAAKqM,QACf6F,EAAmBpkB,EAAEqN,MAAM6E,EAAKmS,WAAYnS,EAChDqM,GAAQjD,GAAG1N,GAAaH,EAAI,oEAAqE,SAAUgK,GACvG,GAAIkD,GAAUzI,EAAKqM,QAAQ1V,KAAK,uBAC3BqJ,GAAK2O,SAASpJ,KAGnBkD,EAAQ2J,MAAK,GAAM,GACnBpS,EAAKqS,WAAaR,EAAS7R,EAAKsS,aAAa/M,EAAG,KAChDvF,EAAKuS,WAAaT,EAAS9R,EAAKsS,aAAa/M,EAAG,KAChDvF,EAAKwS,cAAe,EACpBxS,EAAKnH,OAAO4Z,YAAa,EACzBR,EAAYxY,KAAKiZ,MACjBrG,EAAQjD,GAAGvN,GAAYN,EAAI,oEAAqE2W,MAEpG7F,EAAQjD,GAAGtN,GAAWP,EAAI,oEAAqE,SAAUgK,GAAV,GAIvFoN,GACAlK,EACAmK,CALC5S,GAAK2O,SAASpJ,KAGfoN,EAAQlZ,KAAKiZ,MAAQT,EACrBxJ,EAAUzI,EAAKqM,QAAQ1V,KAAK,wBAC5Bic,GAAa5S,EAAK6S,YAAc,IAAOF,GAC3CZ,EAAO/R,EAAKsS,aAAa/M,EAAG,KAC5ByM,EAAOhS,EAAKsS,aAAa/M,EAAG,KACxBvF,EAAK8S,YAGL9S,EAAKzO,QAAQwhB,aAAeC,KAAKC,IAAIlB,EAAOF,IAAW,IAAMmB,KAAKC,IAAIjB,EAAOF,IAAW,KACxF9R,EAAKkT,oBAAoB3N,IAExBvV,EAAM2L,QAAQwX,qBAAuBR,EAAQ,KAAOK,KAAKC,IAAIlB,EAAOF,GAAU,IAC/EpJ,EAAQ2K,SAAUC,UAAW5K,EAAQ,GAAG4K,UAAYT,IAExDvG,EAAQ4B,IAAIpS,GAAYN,EAAI,0DAA2D2W,QAG/FvE,YAAa,WAAA,GAaL2F,GAZAtT,EAAO1P,KACP+b,EAAUrM,EAAKqM,OACdrM,GAAKzO,QAAQwhB,aAGlB/S,EAAKuT,YACLlH,EAAQjD,GAAG3N,GAAYF,EAAI,oEAAqE,SAAUgK,GAClGvF,EAAK2O,SAASpJ,IAGlBvF,EAAKkT,oBAAoB3N,KAEzB+N,EAAmBxlB,EAAEqN,MAAM6E,EAAKwT,WAAYxT,GAChDqM,EAAQjD,GAAG3N,GAAYF,EAAI,0DAA2D,SAAUgK,GAAV,GAC9EkO,GAAQlO,EAAEkO,MACV1J,EAASxE,EAAEwE,OACX2J,EAAUD,GAAmB,IAAVA,GAAe1J,GAAoB,GAAVA,CAC5C/J,GAAK2O,SAASpJ,IAGbmO,GACDrH,EAAQjD,GAAGrN,GAAYR,EAAI,0DAA2D+X,KAG9FjH,EAAQjD,GAAG,UAAY7N,EAAK,eAAiBA,EAAI,WAC7C8Q,EAAQ4B,IAAIlS,GAAYR,EAAI,0DAA2D+X,KAE3FjH,EAAQjD,GAAG,QAAU7N,EAAI,WAChByE,EAAKoQ,YAAepQ,EAAKwS,cAC1BxS,EAAK2T,mBAET3T,EAAK6Q,YAETxE,EAAQjD,GAAG,WAAa7N,EAAI,SAAUgK,GAClCvF,EAAK4T,SAAW5T,EAAK6T,WAAY,EACjC7T,EAAK8T,QAAQnd,KAAK,WAAWod,YAAYrX,IACpC5O,EAAEyX,EAAEyO,eAAetd,QAAQkG,IAAe5N,QAC3CgR,EAAK8T,QAAQnd,KAAKiG,IAAemX,YAAYpX,MAGrD0P,EAAQjD,GAAG,UAAY7N,EAAIJ,EAAM6E,EAAKiU,SAAUjU,IAChDqM,EAAQjD,GAAG,QAAU7N,EAAI,SAAUgK,GAC/BvF,EAAK4T,SAAWrO,EAAE2O,QAClBlU,EAAK6T,UAAYtO,EAAE4O,aAG3BjB,oBAAqB,SAAU3N,GAAV,GACbkO,GAAQlO,EAAEkO,MACV1J,EAASxE,EAAEwE,OACX2J,EAAUD,GAAmB,IAAVA,GAAe1J,GAAoB,GAAVA,CAC3C2J,KACGnO,EAAE2O,UACF5jB,KAAKsjB,SAAWrO,EAAE2O,SAElB3O,EAAE4O,WACF7jB,KAAKujB,UAAYtO,EAAE4O,UAEvB7jB,KAAKshB,iBAAiBrM,EAAE+G,gBAExBtc,EAAMokB,mBAAqB9jB,KAAK+b,QAAQnb,IAAI,GAC5ClB,EAAMqkB,aAAa/jB,KAAK+b,SAExB/b,KAAKugB,UAETvgB,KAAKwjB,QAAQnd,KAAK,WAAWod,YAAYrX,KAE7CiX,iBAAkB,WACdrjB,KAAKshB,iBAAiBthB,KAAK+b,QAAQ1V,KAAK,wBAAwBA,KAAK,cAEzEka,QAAS,WAAA,GAmBGyD,GAIAC,EACAhgB,EACAsC,EACAgZ,EAzBJ7P,EAAO1P,KACPuI,EAAOmH,EAAKnH,OACZwT,EAAUrM,EAAKqM,QACf3c,EAAUmJ,EAAKnJ,UACfygB,EAAYnQ,EAAKoQ,WACjBoE,EAAexU,EAAKwQ,KAAOxQ,EAAKwQ,KAAKL,UAAY,KACjDsE,EAAkBzU,EAAKwQ,KAAOxQ,EAAKwQ,KAAKgB,aAAe,IAC3D,IAAKrB,IAGDzgB,IACAA,EAAQglB,gBAAgB,MACxBhlB,EAAQglB,gBAAgB,cACxBrI,EAAQsI,WAAW,0BAEvB9b,EAAK6W,OAAOS,GACZzgB,EAAUmJ,EAAKnJ,UACXA,IAAY8kB,IAAiB9kB,GAAWygB,EAAUtZ,QAAU4d,IAAoBtE,EAAUtZ,OAAO7H,SAAS,CAE1G,GADIslB,EAAaxmB,EAAE4B,GAAS6E,KAAK,OAC7ByL,EAAKwQ,MAAQ8D,GAAcA,IAAexmB,EAAEkS,EAAKwQ,KAAKL,WAAW5b,KAAK,QAAW4b,EAAUtZ,QAAUmJ,EAAKwQ,KAAKgB,eAAiBrB,EAAUtZ,OAAO7H,OACjJ,MAGAuF,GAAO4b,EACPtZ,EAASmJ,EAAKsQ,kBACdT,EAAQhX,EAAKwX,eACbxZ,EAAO,IACPtC,EAAOsC,EAAO,IAAMsZ,EACpBoE,EAAcvkB,EAAMkG,OAAO8J,EAAKzO,QAAQ8M,SAASuW,eAAgBrgB,EAAK0D,MAAO1D,EAAKU,MAAOV,EAAKU,QAE9Fsf,EAAcvkB,EAAMkG,OAAO8J,EAAKzO,QAAQ8M,SAASwW,cAAetgB,EAAKU,MAAOV,EAAKY,KAErFzF,EAAQolB,aAAa,KAAM9U,EAAK6N,SAChCne,EAAQolB,aAAa,aAAcP,GACnClI,EAAQ5Y,KAAK,wBAAyBuM,EAAK6N,SAC3C7N,EAAKwQ,MACDL,UAAWzgB,EACX8hB,aAAc3a,EAAO7H,QAEzBgR,EAAKmL,QAAQ,UACTlW,MAAOkb,EAAUlb,MACjBE,IAAKgb,EAAUhb,IACf0B,OAAQA,EACRgZ,MAAOA,EACP7K,UAAWnM,EAAK0X,gBAAgBJ,OAI5CG,gBAAiB,WAMb,IANa,GAITvb,GAHAggB,EAAOzkB,KAAK8f,WAAWvZ,OACvB7H,EAAS+lB,EAAK/lB,OACdgG,EAAM,EAEN6B,KACG7B,EAAMhG,EAAQgG,IACjBD,EAAQzE,KAAK0kB,gBAAgBD,EAAK/f,IAC9BD,GACA8B,EAAOpI,KAAKsG,EAGpB,OAAO8B,IAEXyb,aAAc,SAAUvd,EAAOkgB,GAC3B,MAAO,QAAQ9Q,KAAKpP,EAAMoN,OAASpN,EAAM6Z,eAAiB7Z,GAAOmgB,eAAe,GAAG,OAASD,GAAclgB,EAAM,OAASkgB,IAE7H9C,WAAY,SAAU5M,GAAV,GACJvF,GAAO1P,KACPmY,EAAUzI,EAAKqM,QAAQ1V,KAAK,wBAC5Bwe,EAAiB1M,EAAQ,GAAG2M,aAAe3M,EAAQ,GAAG4M,aACtDC,EAAmB7M,EAAQ,GAAG8M,YAAc9M,EAAQ,GAAG+M,YACvDxD,EAAOhS,EAAKsS,aAAa/M,EAAG,KAC5BwM,EAAO/R,EAAKsS,aAAa/M,EAAG,KAC5B8N,EAAY5K,EAAQ,GAAG4K,UAAYL,KAAKyC,MAAMzD,EAAOhS,EAAKuS,YAC1DmD,EAAajN,EAAQ,GAAGiN,WAAa1C,KAAKyC,MAAM1D,EAAO/R,EAAKqS,YAC5DsD,EAAsBR,GAAkBnC,KAAKC,IAAIjB,EAAOhS,EAAKuS,YAAc,GAC3EqD,EAAwBN,GAAoBtC,KAAKC,IAAIjB,EAAOhS,EAAKuS,YAAc,EAC/EvS,GAAK8S,WAAa9iB,EAAM2L,QAAQwX,sBAAwBnT,EAAK2O,SAASpJ,KAGtEoQ,GAAuBC,KACvB5V,EAAK6S,WAAaG,KAAKyC,MAAMzD,EAAOhS,EAAKuS,YACzCvS,EAAKuS,WAAaP,EAClBhS,EAAKqS,WAAaN,EAClBtJ,EAAQ2K,SACJC,UAAWA,EACXqC,WAAYA,GACb,GACH1V,EAAKnH,OAAO4Z,YAAa,IAGjCe,WAAY,SAAUjO,GAClB,GAAIvF,GAAO1P,IACXulB,cAAa7V,EAAK8V,YACd9V,EAAK2O,SAASpJ,KAGlBvF,EAAK8V,WAAaC,WAAW,WAAA,GAIjBC,GAEIC,EACAC,EANRrd,EAAOmH,EAAKnH,OACZsX,EAAYnQ,EAAKoQ,UACjBD,KACI6F,EAAOnd,EAAKsd,mBAAmBroB,EAAEyX,EAAE+G,gBACnC0J,GAAQ7F,EAAUlB,aAAe+G,EAAK/G,aAClCgH,EAAYD,EAAKC,YACjBC,EAAUF,EAAKE,UACfD,GAAa9F,EAAUhb,IACvBgb,EAAUiG,UAAW,EACdF,GAAW/F,EAAUlb,QAC5Bkb,EAAUiG,UAAW,GAErBjG,EAAUiG,SACVjG,EAAUlb,MAAQghB,EAElB9F,EAAUhb,IAAM+gB,EAEpBlW,EAAK6Q,aAGd,KAEPwF,aAAc,SAAUjT,GACpB,GAAIvK,GAAM0S,EAAQjb,KAAKib,KACvB,KAAK1S,IAAQ0S,GAAO,CAChB,IAAKnI,EACD,MAAOvK,EAEXuK,OAGR6Q,SAAU,SAAU1O,GAAV,GAC+uB+Q,GAYzuBthB,EAIAuhB,EAeAC,EAmHJC,EAYIzM,EA9JRhK,EAAO1P,KAAM/B,EAAMgX,EAAEmR,QAAS7d,EAAOmH,EAAKnH,OAAQpC,EAAWoC,EAAKtH,QAAQkF,SAAU0Z,EAAYnQ,EAAKoQ,WAAYuG,EAAgB7oB,EAAEqC,OAAOggB,GAAYyG,EAAqB,KAARroB,GAAsB,KAARA,GAAsB,KAARA,GAAsB,KAARA,GAAsB,KAARA,EAAYsoB,EAA2B,wJAAwKC,EAAiB9W,EAAK8T,QAAQnd,KAAKkgB,GAA2BE,EAAe/W,EAAK8T,QAAQnd,KAAKiG,IAAgBoa,EAAiBlpB,EAAEyX,EAAE0F,QAAQvU,QAAQkG,IAAe5N,QAAUgR,EAAK8T,QAAQnd,KAAK,uCAAuC3H,OAAQioB,EAAmBF,EAAaG,WAAW9T,MAAMpD,EAAK8T,QAAQnd,KAAK,IAAM+F,KAA+Bya,EAAQnnB,EAAM2L,QAAQwb,MAAMnX,EAAKmF,SAAUiS,EAAYD,KAAa,CAMx0B,IALIF,QACAA,EAAmBF,EAAaG,WAAW9T,MAAMpD,EAAK8T,QAAQnd,KAAK,uBAEvEqJ,EAAK4T,SAAWrO,EAAE2O,QAClBlU,EAAK6T,UAAYtO,EAAE4O,SACf5lB,IAAQoM,EAAKC,IAGb,MAFAoF,GAAK8T,QAAQnd,KAAK,iBAAiB4V,QAAQ8K,SAAS3a,IACpD6I,EAAEO,iBACF,CACG,IAAIvX,IAAQoM,EAAK2c,KACpB,GAAItX,EAAK8T,QAAQnd,KAAK,IAAM+F,IAAc1N,OAOtC,MANIgG,GAAM8hB,EAAe1T,MAAMpD,EAAK8T,QAAQnd,KAAK,IAAM+F,KACnD1H,QAAcgL,EAAKuX,eACnBviB,EAAM8hB,EAAe1T,MAAMpD,EAAK8T,QAAQnd,KAAK,4CAE7C4f,EAAchR,EAAE4O,SAAW2C,EAAe9hB,EAAM,GAAK8hB,EAAe9hB,EAAM,GAC9EgL,EAAK8T,QAAQnd,KAAK,IAAM+F,IAAcqX,YAAYrX,IAC9C6Z,GACAzoB,EAAEyoB,GAAac,SAAS3a,IAAc6P,QACtCvM,EAAKuX,aAAe,KACpBhS,EAAEO,iBACF,IAEA9F,EAAKmF,QAAQoH,QACbhH,EAAEO,iBACF,OAGL,IAAIvX,IAAQoM,EAAK6c,OAASjpB,IAAQoM,EAAK8c,SAAU,CACpD,GAAIT,GAAkBhX,EAAKuX,eAAiBvX,EAAKuX,aAAahO,SAAS,oBAenE,MAdIiN,GAAkBxW,EAAKuX,aAAahjB,OAAO2C,KAC1C8I,EAAKmL,QAAQ,YACVtS,KAAM2d,EACNxM,OAAQ,aACRzU,KAAMyK,EAAKzK,WAEfyK,EAAKnH,KAAK2d,GACVO,EAAahD,YAAYpX,IACrBqD,EAAK8T,QAAQnd,KAAK,2BAA2B3H,SAC7ClB,EAAEsD,SAASsmB,eAAeC,OAC1B3X,EAAK8T,QAAQnd,KAAK,2BAA2B0gB,SAAS3a,IAAc/F,KAAK,WAAW4V,UAG5FhH,EAAEO,iBACF,CAEJ,IAAI9F,EAAK8T,QAAQnd,KAAK,IAAM+F,GAAe,YAAY1N,OAGnD,MAFAgR,GAAK8T,QAAQnd,KAAK,IAAM+F,GAAe,YAAYkC,QACnD2G,EAAEO,iBACF,MAED,IAAIP,EAAEqS,QAAUrpB,IAAQoM,EAAKkd,MAChC,GAAI7X,EAAK8T,QAAQnd,KAAK,IAAM+F,GAAe,YAAY1N,OAGnD,MAFAgR,GAAK8T,QAAQnd,KAAK,IAAM+F,GAAe,YAAYkC,QACnD2G,EAAEO,iBACF,MAED,CAAA,GAAIvX,IAAQoM,EAAKmd,OAASd,EAS7B,MARAlpB,GAAEkS,EAAK8T,QAAQnd,KAAK,IAAM+F,KAAeqX,YAAYrX,IAEjDsD,EAAKuX,aAA4CzpB,EADjDqpB,EACoBF,EAAmB,IAAM,EAAMF,EAAaG,SAAS,8BAAmCH,EAAaG,WAAWD,EAAmB,EAAIG,GAEvIH,EAAmB,IAAMF,EAAaG,WAAWloB,OAAW+nB,EAAaG,SAAS,+BAAoCH,EAAaG,WAAWD,EAAmB,EAAIG,IAE7LpX,EAAKuX,aAAahL,QAAQ8K,SAAS3a,IACnC6I,EAAEO,iBACF,CACG,IAAIvX,IAAQoM,EAAKod,MAAQf,EAS5B,MARAlpB,GAAEkS,EAAK8T,QAAQnd,KAAK,IAAM+F,KAAeqX,YAAYrX,IAEjDsD,EAAKuX,aAAyEzpB,EAD9EqpB,EACoBF,EAAmB,IAAMF,EAAaG,WAAWloB,OAAW+nB,EAAaG,SAAS,+BAAoCH,EAAaG,WAAWD,EAAmB,EAAIG,GAErKH,EAAmB,IAAM,EAAMF,EAAaG,SAAS,8BAAmCH,EAAaG,WAAWD,EAAmB,EAAIG,IAE/JpX,EAAKuX,aAAahL,QAAQ8K,SAAS3a,IACnC6I,EAAEO,iBACF,CACG,IAAIvX,IAAQoM,EAAKkd,MAAQ7X,EAAK8T,QAAQnd,KAAKiG,IAAe2M,SAAS5M,IAUtE,MATAqD,GAAK8T,QAAQnd,KAAK,IAAM+F,IAAcqX,YAAYrX,IAE9C4Z,EADAtW,EAAKuX,aACWR,EAAapgB,KAAKqJ,EAAKuX,cAAcnU,QAErC2T,EAAaG,SAAS,0CAA0C9T,QAEpFpD,EAAKuX,aAAsEzpB,EAAvDwoB,EAAgB,IAAMS,EAAaG,WAAWloB,OAAW+nB,EAAaG,SAAS,+BAAoCH,EAAaG,WAAWZ,EAAgB,EAAIc,IACnLpX,EAAKuX,aAAahL,QAAQ8K,SAAS3a,IACnC6I,EAAEO,iBACF,CACG,IAAIvX,IAAQoM,EAAKqd,IAAMhY,EAAK8T,QAAQnd,KAAKiG,IAAe2M,SAAS5M,IAUpE,MATAqD,GAAK8T,QAAQnd,KAAK,IAAM+F,IAAcqX,YAAYrX,IAE9C4Z,EADAtW,EAAKuX,aACWR,EAAapgB,KAAKqJ,EAAKuX,cAAcnU,QAErC2T,EAAaG,SAAS,0CAA0C9T,QAEpFpD,EAAKuX,aAAyCzpB,EAA1BwoB,EAAgB,IAAM,EAAMS,EAAaG,SAAS,8BAAmCH,EAAaG,WAAWZ,EAAgB,EAAIc,IACrJpX,EAAKuX,aAAahL,QAAQ8K,SAAS3a,IACnC6I,EAAEO,iBACF,CACG,IAAIP,EAAEqS,QAAUrpB,IAAQoM,EAAKkd,MAAQ7X,EAAK8T,QAAQnd,KAAK,kBAAkB4S,SAAS7M,IAGrF,MAFAsD,GAAKiY,gBACL1S,EAAEO,iBACF,CACG,IAAIvX,IAAQoM,EAAKud,KAAOlY,EAAKkM,OAASlM,EAAKkM,MAAMhN,UAGpD,MAFAc,GAAKkM,MAAM5D,QACX/C,EAAEO,iBACF,CACG,IAAIvX,IAAQoM,EAAKud,KAAOlY,EAAK8T,QAAQnd,KAAKiG,IAAe2M,SAAS5M,IAMrE,MALAqD,GAAK8T,QAAQnd,KAAKiG,IAAemX,YAAYpX,IAC7CqD,EAAK8T,QAAQnd,KAAKiG,IAAesa,WAAWnD,YAAYrX,IACxDsD,EAAKuX,aAAe,KACpBvX,EAAK8T,QAAQnd,KAAK,mBAAmB4V,QAAQ8K,SAAS3a,IACtD6I,EAAEO,iBACF,EAEJ,IAAI8Q,EAAJ,CAGA,IAAKzG,EAID,MAHAnQ,GAAK2T,mBACL3T,EAAK6Q,UACL7Q,EAAKmF,QAAQoH,QACb,CAEAhe,KAAQoM,EAAK2c,IACTze,EAAKsf,YAAYhI,EAAW5K,EAAE4O,YAC9BnU,EAAK6Q,UACL7Q,EAAKmF,QAAQoH,QACbhH,EAAEO,kBAECvX,IAAQoM,EAAK6c,OAASjpB,IAAQoM,EAAK8c,SACtCtH,EAAUtZ,OAAO7H,QAAUyH,EACvBA,EAASuK,UAAW,GACpBhB,EAAKsK,UAAU6F,EAAUtZ,OAAO,IAE7BJ,GAAYA,EAAS4N,UAAW,IACnC8L,EAAUrS,WACVqS,EAAYriB,EAAEqC,UAAWggB,GAAahb,IAAKnF,EAAMuF,KAAKwb,QAAQZ,EAAUhb,WAE5EoQ,EAAEO,iBACF9F,EAAKoY,SAASjoB,KAAWggB,EAAWtX,EAAK0X,gBAAgBJ,MAEtD5hB,IAAQoM,EAAK0d,QAAU5hB,KAAa,GAASA,EAASgO,WAAY,EACzEzE,EAAKsY,YAAYnI,EAAUtZ,OAAO,IAC3BtI,GAAO,IAAMA,GAAO,IACvBkoB,EAAczW,EAAKqW,aAAa9nB,EAAM,IACtCkoB,IAAgBzW,EAAKmL,QAAQ,YACzBtS,KAAM4d,EACNzM,OAAQ,aACRzU,KAAMyK,EAAKzK,UAEfyK,EAAKnH,KAAK4d,IAEP5d,EAAK0f,KAAKpI,EAAW5hB,EAAKgX,EAAE4O,YAC/Btb,EAAK2f,QAAQrI,GACbnQ,EAAK6Q,WAED7G,EAAShK,EAAKzK,OAAOiE,UAAY2W,EAAUlb,MAAMuE,UAAY,WAAa,OACzEwG,EAAKmL,QAAQ,YACVtS,KAAMmH,EAAKyY,kBACXzO,OAAQA,EACRzU,KAAM4a,EAAUlb,SAIpBkb,EAAUlb,MAAQ0hB,EAAc1hB,MAChCkb,EAAUhb,IAAMwhB,EAAcxhB,KAH9B6K,EAAKzK,KAAK4a,EAAUlb,QAM5B+K,EAAK8T,QAAQnd,KAAK,WAAWod,YAAYrX,IACzC6I,EAAEO,kBAEN9F,EAAK0Y,wBAET9G,iBAAkB,SAAU/N,GAAV,GAEVzO,GACA4gB,EAFA7F,EAAY7f,KAAK8f,UAGrBvM,GAAO/V,EAAE+V,GACLA,EAAK+B,GAAG,cACRxQ,EAAMyO,EAAKpQ,KAAKzD,EAAMyD,KAAK,QACvB0c,GAAaA,EAAUtZ,OAAO8hB,QAAQvjB,UAAgB9E,KAAKsjB,YAI9DzD,IAAc7f,KAAKsjB,UAAatjB,KAAKujB,aACtC1D,EAAY7f,KAAK8f,YACbvZ,UACAoY,WAAY,IAGpB+G,EAAO1lB,KAAKuI,OAAOsd,mBAAmBtS,GAClCmS,IACA7F,EAAUlB,WAAa+G,EAAK/G,YAAc,GAE1C7Z,IACA4gB,EAAO3gB,EAAmB/E,KAAK+d,MAAOjZ,IAEtC4gB,GAAQA,EAAK5gB,MACbA,GAAO4gB,EAAK5gB,MAEhB9E,KAAKsoB,iBAAiB5C,EAAM5gB,GAC5B9E,KAAKooB,wBAETE,iBAAkB,SAAUC,EAAUhiB,EAAQoY,GAA5B,GAGNpW,GAKIud,EAmBA0C,EA1BR3I,EAAY7f,KAAK8f,UACjByI,IAAY1I,IACRtX,EAAOvI,KAAKuI,OACZggB,EAASzjB,MACTyjB,EAAWhgB,EAAKkgB,yBAAyBF,IAEzCvoB,KAAKujB,WAAa1D,EAAUlb,OAASkb,EAAUhb,KAC3CihB,EAAWyC,EAAS1jB,IAAMgb,EAAUhb,IACxCgb,EAAUhb,IAAM0jB,EAAS3C,QAAU2C,EAAS3C,UAAY2C,EAAS1jB,IAC7DihB,GAAYvd,EAAKmgB,mBACjBhpB,EAAMuF,KAAKmE,QAAQyW,EAAUhb,KAAM0D,EAAKmgB,uBAG5C7I,EAAUlb,MAAQ4jB,EAAS5C,UAAY4C,EAAS5C,YAAc4C,EAAS5jB,MACvEkb,EAAUhb,IAAM0jB,EAAS3C,QAAU2C,EAAS3C,UAAY2C,EAAS1jB,KAGjEgb,EAAUrS,SADV,aAAe+a,GACMA,EAAS1H,UAET0H,EAAS/a,SAEf,OAAfmR,GAAuBA,IAAejc,IACtCmd,EAAUlB,WAAaA,GAE3BkB,EAAU/M,MAAQyV,EAASzV,MACvB9S,KAAKsjB,UACDkF,EAAejiB,GAAUA,EAAO7H,OAASmhB,EAAUtZ,OAAO8hB,QAAQ9hB,EAAO,OACzEiiB,KACA3I,EAAUtZ,OAAO6Z,OAAOoI,EAAc,GAEtC3I,EAAUtZ,OAASsZ,EAAUtZ,OAAOE,OAAOF,QAG/CsZ,EAAUtZ,OAASA,QAI/BtF,SACI2F,KAAM,YACN3B,KAAMwH,GACNtG,UAAU,EACVwiB,UAAU,EACVC,MAAM,EACNtU,QAAQ,EACRpR,SAAU,GACV2lB,YAAY,EACZC,IAAK,GAAI3f,MAAK,KAAM,EAAG,GACvB4f,IAAK,GAAI5f,MAAK,KAAM,GAAI,IACxBqa,QAAS,KACTwF,cAAe,EACfC,YAAa,EACbC,eAAe,EACfC,UAAW1c,GACX2c,QAAS3c,GACT4c,mBACIC,eAAgB,IAChBC,kBAAkB,GAEtBC,UACAzb,UACI0b,MAAO,QACPC,IAAK,gBACLhR,KAAM,OACNF,OAAQ,SACRrE,QAAS,SACTqG,YAAa,eACbmP,kBAAmB,eACnBC,KAAM,OACNC,SAAU,WACVtF,cAAe,+BACfD,eAAgB,wBAChBrJ,OACI6O,IAAK,MACLC,KAAM,OACNC,SAAU,YACVC,OAAQ,SACRC,MAAO,QACPC,SAAU,WACVC,aAAc,gBACdC,iBAAkB,qBAClBC,cAAe,kBAEnBC,oBACIZ,kBAAmB,wBACnBa,uBAAwB,eACxBC,uBAAwB,4BACxBC,mBAAoB,oBACpBC,gBAAiB,sBACjBC,qBAAsB,0BACtBC,iBAAkB,mBAEtB1kB,UAAY2kB,aAAcje,IAC1B6I,QACI/N,MAAO,QACPhD,MAAO,QACPE,IAAK,MACL8Q,YAAa,gBACb1D,YAAa,cACb4D,OAAQ,SACR3S,SAAU,WACVJ,cAAe,iBACfC,YAAa,eACbyT,kBAAmB,wCACnBkG,oBAAqB,YACrBlO,qBAAsB,YACtBiK,cAAe,aACfpK,WAAY,cACZkM,YAAa,UAGrBzb,OAAQ,KACRD,MAAO,KACP6V,aACAqK,OACIrK,aACAqW,YAAa,cAEjB9P,SACAwH,YAAY,GAEhBlc,QACIwF,GACAI,GACAL,GACAG,GACA,MACA,cACA,YACA,YACA,OACA,UACA,cACA,SACA,YACA,WACA,UAEJkI,QAAS,WAAA,GACYU,GAQJnQ,EACDsmB,EATRtb,EAAO1P,IAOX,IANAuK,EAAOoF,GAAGwE,QAAQ9P,KAAKqL,GACnBA,EAAKzH,aACLyH,EAAKzH,WAAWgQ,OAAOtM,GAAQ+D,EAAKub,iBACpCvb,EAAKzH,WAAWgQ,OAAOrM,GAAU8D,EAAKwb,kBACtCxb,EAAKzH,WAAWgQ,OAAOpM,GAAO6D,EAAKyb,gBAEnCzb,EAAK0b,wBACL,IAAS1mB,EAAM,EAAGA,EAAMgL,EAAKgF,UAAUhW,OAAQgG,IACvCsmB,EAAatb,EAAKgF,UAAUhQ,GAAKuD,WACrC+iB,EAAW/S,OAAOtM,GAAQ+D,EAAK0b,yBAC/BJ,EAAW/S,OAAOrM,GAAU8D,EAAK2b,0BACjCL,EAAW/S,OAAOpM,GAAO6D,EAAK4b,sBAGlC5b,GAAK6b,WACL7b,EAAK6b,SAASpX,UACdzE,EAAKkM,MAAMzH,WAEXzE,EAAKnH,QACLmH,EAAKnH,OAAO4L,UAEZzE,EAAK8b,SACL9b,EAAK8b,QAAQrX,UAEbnU,KAAKyrB,gBACLzrB,KAAKyrB,eAAetX,UAEpBnU,KAAK0rB,kBACL1rB,KAAK0rB,iBAAiBvX,UAE1BU,EAAUnF,EAAKmF,QAAQ9N,IAAI2I,EAAKqM,SAAShV,IAAI2I,EAAK8T,SAASzc,IAAI2I,EAAKkM,OACpE/G,EAAQ8I,IAAI1S,GACZsa,aAAa7V,EAAK8V,YAClB9V,EAAKic,OAAS,KACdjc,EAAK8T,QAAU,KACf9T,EAAKmF,QAAU,KACfrX,EAAEiC,QAAQke,IAAI,SAAW1S,EAAIyE,EAAKsN,gBAClCtd,EAAMyU,QAAQzE,EAAKqM,UAEvB6P,cAAe,SAAU3jB,GACrBjI,KAAKiB,QAAQgH,WAAaA,EAC1BjI,KAAK8c,cACD9c,KAAKiB,QAAQ0nB,UAAY1gB,EAAW4jB,MACpC5jB,EAAW4jB,QACJ7gB,EAAQ/C,IACfjI,KAAKuI,KAAKvI,KAAK8rB,gBAGvB1Y,MAAO,WAAA,GACC+E,GAAUnY,KAAK+b,QAAQ1V,KAAK,wBAC5BkC,EAAOvI,KAAKuI,MAChB,OAAIA,IAA8B,WAAtBA,EAAKtH,QAAQ2F,KACduR,EAAQ9R,KAAK,WAEb8R,EAAQ9R,KAAK,YAAYU,IAAI/G,KAAK+b,QAAQ1V,KAAK,4BAA4BA,KAAK,+BAA+B0lB,aAG9H5O,SAAU,WAAA,GACF6O,GACAC,EACAC,EACA/C,EACAC,EACA3kB,EACA0nB,EAEAC,EACAC,EACA/N,EAHA5O,EAAO1P,KAIPgJ,EAAW,EACXsjB,KACAC,KACA1V,EAAWnH,EAAKyO,YAChBqO,EAAU9c,EAAKzO,QAAQkF,UAAYuJ,EAAKzO,QAAQkF,SAAS8hB,QAAS,EAClE3M,EAAY5L,EAAKzO,QAAQkF,UAAYuJ,EAAKzO,QAAQkF,SAAS8W,UAAW,GACtEuP,GAAWlR,GAAazE,KACxBnH,EAAK8S,WAAY,EACb3L,GAAYnX,EAAM2L,QAAQ+S,SAASqO,UACnCzjB,EAAW,GAEf0G,EAAK+b,eAAiB,GAAI/rB,GAAM+F,GAAGinB,UAAUhd,EAAKmF,SAC9C7L,SAAUA,EACVzD,OAAQ,WACRonB,OAAQ,mBACRC,WAAY/V,EACZgW,YAAY,IAEZL,GACA9c,EAAK+b,eAAetS,KAAK,YAAa,SAAUlE,GAAV,GAuB1B1O,GACK/H,EACDsuB,EACAC,EAzBRxkB,EAAOmH,EAAKnH,OACZkW,EAAexJ,EAAE+G,cACjBgR,EAAUtd,EAAK2O,SAASpJ,EAE5B,IADAvF,EAAK8S,WAAY,GACZja,EAAKtH,QAAQkF,UAAYoC,EAAKtH,QAAQkF,SAAS8hB,QAAS,EAGzD,MAFAvY,GAAK8S,WAAY,EACjBvN,EAAEO,iBACF,CAEJ,IAAIwX,IAAYvO,EAAaxF,SAAS,kBAIlC,MAHAvJ,GAAK8S,WAAY,EACjB9S,EAAKmF,QAAQxO,KAAK,mBAAmBod,YAAY,kBACjDxO,EAAEO,iBACF,CAQJ,IANA/Q,EAAQiL,EAAKgV,gBAAgBjG,EAAatb,KAAKzD,EAAMyD,KAAK,SAC1DgpB,EAAc1nB,EAAM0L,QACpBmO,EAAgB7Z,EAAM0L,QACtBgc,EAAYzb,OAAOnI,EAAK0kB,qBAAqBd,IAC7CA,EAAYe,aAAexd,EAAK8O,gBAAgBC,GAChD6N,KACI5c,EAAKoQ,WAEL,IADIvZ,EAASmJ,EAAKoQ,WAAWvZ,OACpB/H,EAAI,EAAGA,EAAI+H,EAAO7H,OAAQF,IAC3BsuB,EAAWpd,EAAKgV,gBAAgBne,EAAO/H,IAAI2R,QAC3C4c,EAAkB/sB,KAAK6U,QAAQxO,KAAK,yBAA2BymB,EAAShoB,IAAM,MAAMoW,GAAG,GAC3F4R,EAASpc,OAAOnI,EAAK0kB,qBAAqBH,IACtCC,EAAgBruB,SAChBouB,EAASI,aAAexd,EAAK8O,gBAAgBuO,IAEjDT,EAAanuB,KAAK2uB,OAGtBR,GAAanuB,KAAKguB,EAEtBH,GAAYzjB,EAAK4kB,gBAAgBlY,EAAEmY,EAAEC,cAAepY,EAAEqY,EAAED,eACxDnB,EAAiB3jB,EAAK0X,gBAAgB+L,GACtCK,EAAkBlD,EAAY6C,EAAUuB,YAAYtY,EAAEmY,EAAEC,cAAepY,EAAEqY,EAAED,cAAe3d,EAAKzO,QAAQ2nB,MACvGqD,EAAUD,EACVI,EAAaJ,EACRA,IAAatc,EAAKmL,QAAQ,aAAepW,MAAOA,KACjDwQ,EAAEO,mBAEP2D,KAAK,OAAQ,SAAUlE,GAAV,GAGRjM,GACAwkB,EACAhvB,EAOQivB,EACAC,EAZRnlB,EAAOmH,EAAKnH,OACZmd,EAAOnd,EAAK4kB,gBAAgBlY,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAIlD,IAAKjI,EAAL,CAIA,GADA0D,EAAU1D,EAAK6H,YAAYtY,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAAUje,EAAKzO,QAAQ2nB,MAChElD,EAAK7E,YAAcmL,EAAUnL,UAC7B,GAAI6E,EAAK7E,YAAcuL,EAAWvL,UAQ9B,IAPI4M,EAAYjwB,EAAEwuB,EAAUnX,SAAS/B,QACjC4a,EAAoBlwB,EAAEkoB,EAAK7Q,SAAS8C,SAASiP,WAAW1L,GAAGuS,GAC/DzB,EAAYzjB,EAAK4kB,gBAAgBO,EAAkBnd,SAASuH,KAAM4V,EAAkBnd,SAASqd,KAC7FzE,EAAY6C,EAAUuB,YAAYtY,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,UAAU,GAC9DpB,EAAeD,EAAahsB,IAAI,SAAUmE,GACtC,MAAOA,GAAM0L,UAEZ3R,EAAI,EAAGA,EAAI8tB,EAAa5tB,OAAQF,IAC7B8tB,EAAa9tB,GAAGgP,UAAYkY,EAAK7E,YACjCyL,EAAa9tB,GAAGgP,SAAWkY,EAAK7E,UAChCyL,EAAa9tB,GAAGqG,IAAMnF,EAAMuF,KAAKiF,QAAQoiB,EAAa9tB,GAAGmG,OACzD2nB,EAAa9tB,GAAGmG,MAAQjF,EAAMuF,KAAKiF,QAAQoiB,EAAa9tB,GAAGmG,OACtD+gB,EAAK7E,YACNnhB,EAAMuF,KAAKmE,QAAQkjB,EAAa9tB,GAAGmG,MAAOjF,EAAMuF,KAAKkF,gBAAgB5B,EAAK4gB,cAC1EzpB,EAAMuF,KAAKmE,QAAQkjB,EAAa9tB,GAAGqG,IAAKnF,EAAMuF,KAAKkF,gBAAgB5B,EAAK4gB,aAAe5gB,EAAKmgB,2BAKxGsD,GAAYxuB,EAAEqC,QAAO,KAAUusB,GAC/BjD,EAAYkD,EACZC,EAAeC,CAIvB,KADAvjB,EAAWogB,EAAUD,EAChB3qB,EAAI,EAAGA,EAAI8tB,EAAa5tB,OAAQF,IACjC+J,EAAKslB,gBAAgBvB,EAAa9tB,GAAIknB,EAAK/G,WAAY3V,EAG3D,IADAwkB,EAAQzkB,EAAeojB,EAAanjB,GAC/B0G,EAAKmL,QAAQ,QACVpW,MAAOA,EACPihB,MACI7Q,QAAS6Q,EAAK7Q,QACdlQ,MAAO+gB,EAAKC,YACZ9gB,IAAK6gB,EAAKE,UACV/E,UAAW6E,EAAK7E,WAEpBnM,UAAWnM,EAAK0X,gBAAgByF,GAChC/gB,MAAO6oB,EAAM7oB,MACbE,IAAK2oB,EAAM3oB,MAIf,IAAKrG,EAAI,EAAGA,EAAI8tB,EAAa5tB,OAAQF,IACjC+J,EAAKslB,gBAAgBvB,EAAa9tB,GAAIknB,EAAK/G,WAAY3V,OAH3DijB,GAAUvG,KAMfvM,KAAK,UAAW,SAAUlE,GAAV,GAEXjM,GACAwkB,EACA7oB,EACAE,EAEAipB,EACAC,EAaSvvB,EACDwvB,EAEAC,EACAC,CANZ,IAlBAxe,EAAKnH,OAAO4lB,kBACRnlB,EAAWogB,EAAUD,EACrBqE,EAAQzkB,EAAeojB,EAAanjB,GACpCrE,EAAQ6oB,EAAM7oB,MACdE,EAAM2oB,EAAM3oB,IAChB6K,EAAK8S,WAAY,EACbsL,EAAepe,EAAKnH,OAAO0X,gBAAgBgM,GAC3C8B,EAAYre,EAAKmL,QAAQ,WACzBpW,MAAOA,EACPihB,MACI7Q,QAASoX,EAAQpX,QACjBlQ,MAAOsnB,EAAQtG,YACf9gB,IAAKonB,EAAQrG,WAEjBjhB,MAAOA,EACPE,IAAKA,EACL6P,UAAWoZ,KAEVC,IAActpB,EAAME,MAAMuE,YAAcvE,EAAMuE,WAAazE,EAAMI,IAAIqE,YAAcrE,EAAIqE,WAAakjB,EAAWvL,YAAcoL,EAAQpL,WAAanhB,EAAM0uB,UAAUN,KAAkBpuB,EAAM0uB,UAAUlC,IAAkB,CAEvN,IADAxc,EAAK2e,aAAe/B,EAAa5tB,OAAS,EACjCF,EAAI,EAAGA,EAAI8tB,EAAa5tB,OAAQF,IACjCwvB,EAAM1B,EAAa9tB,GACvBgvB,EAAQzkB,EAAeilB,EAAKhlB,GACxBilB,EAAsBve,EAAKnH,OAAO0kB,qBAAqBe,GACvDE,EAAe1wB,EAAEqC,QACjB2N,SAAUwgB,EAAIxgB,SACd7I,MAAO6oB,EAAM7oB,MACbE,IAAK2oB,EAAM3oB,KACZopB,EAAqBH,GACxBpe,EAAK4e,aAAa,KAAMN,EAAKE,EAE7Bxe,GAAK2e,eACL3e,EAAKzH,WAAWsmB,OAChB7e,EAAK2e,cAAe,GAG5BpZ,EAAE+G,cAAcyH,YAAY,kBAC5BzjB,KAAKwuB,aACLlC,KACAC,OACDpT,KAAK,aAAc,WAClBzJ,EAAKnH,OAAO4lB,kBACZnuB,KAAKwuB,aACLlC,KACAC,OAGR7c,EAAK+b,eAAetS,KAAK,OAAQ,SAAUlE,GACnCvF,EAAK2O,SAASpJ,KACdvF,EAAKmF,QAAQxO,KAAK,mBAAmBod,YAAY,kBAC7C/T,EAAKzO,QAAQwhB,YACb/S,EAAK4R,iBAAiBrM,EAAE+G,eAE5B/G,EAAE+G,cAAc+K,SAAS,uBAKzC7J,WAAY,WAQR,QAAS4J,GAAU2H,GAAnB,GAOaxwB,GANLywB,GACAC,aAAc,OACdC,aAAc,OACdC,aAAc,QACdC,aAAc,QAElB,KAAS7wB,IAAOywB,GACZ,GAAID,EAAOxV,SAAShb,GAChB,MAAOywB,GAAWzwB,GAjBtB,GACJkrB,GACAC,EACA3kB,EACA0nB,EACAzG,EACAhW,EAAO1P,KACPgJ,EAAW,CAcX0G,GAAKyO,aAAeze,EAAM2L,QAAQ+S,SAASqO,UAC3CzjB,EAAW,GAEf0G,EAAKgc,iBAAmB,GAAIhsB,GAAM+F,GAAGinB,UAAUhd,EAAKmF,SAChD7L,SAAUA,EACVzD,OAAQ,mBACRsnB,YAAY,EACZkC,UAAW,SAAU9Z,GAAV,GACH+Z,GAAaxxB,EAAEyX,EAAE+G,eACjByC,EAAeuQ,EAAW5oB,QAAQ,YAClCtB,EAAM2Z,EAAatb,KAAKzD,EAAMyD,KAAK,QACnCoF,EAAOmH,EAAKnH,MAChBmH,GAAK8S,WAAY,EACjB/d,EAAQiL,EAAKgV,gBAAgB5f,GAC7BqnB,EAAc1nB,EAAM0L,QACpB5H,EAAK0mB,sBAAsB9C,GAC3BzG,EAAOnd,EAAK4kB,gBAAgBlY,EAAEmY,EAAEC,cAAepY,EAAEqY,EAAED,eAC/C3d,EAAKmL,QAAQ,eAAiBpW,MAAOA,KACrCwQ,EAAEO,iBAEN2T,EAAYzpB,EAAMuF,KAAKgL,UAAUkc,EAAYxnB,OAC7CykB,EAAU1pB,EAAMuF,KAAKgL,UAAUkc,EAAYtnB,MAE/CqqB,KAAM,SAAUja,GAAV,GAIE+Z,GACAG,EACA5mB,EACA6mB,EAKAC,EACAC,CAZC5J,KAGDsJ,EAAaxxB,EAAEyX,EAAE+G,eACjBmT,EAAMrI,EAAUkI,GAChBzmB,EAAOmH,EAAKnH,OACZ6mB,EAAc7mB,EAAK4kB,gBAAgBlY,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,UACpDyB,GAAe1J,EAAK/G,YAAcyQ,EAAYzQ,aAGnD+G,EAAO0J,EACHC,EAAgBlG,EAChBmG,EAAclG,EACP,SAAP+F,GACKzJ,EAAK7E,WAAa6E,EAAK7gB,IAAMnF,EAAMuF,KAAKgL,UAAUkc,EAAYxnB,QAAU4D,EAAKmgB,sBAE1EU,EADA+C,EAAY3e,SACFkY,EAAK6H,YAAYtY,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAAUje,EAAKzO,QAAQ2nB,MAE1DlD,EAAKtT,UAAU6C,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAAUje,EAAKzO,QAAQ2nB,OAG5D,SAAPuG,GACFzJ,EAAK7E,WAAanhB,EAAMuF,KAAKgL,UAAUkc,EAAYtnB,KAAO6gB,EAAK/gB,OAAS4D,EAAKmgB,sBAC9ES,EAAYzD,EAAK6H,YAAYtY,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAAUje,EAAKzO,QAAQ2nB,OAE5D,QAAPuG,EACHzJ,EAAK7E,WAAanhB,EAAMuF,KAAKgL,UAAUvQ,EAAMuF,KAAKiF,QAAQwb,EAAKE,aAAelmB,EAAMuF,KAAKgL,UAAUvQ,EAAMuF,KAAKiF,QAAQiiB,EAAYxnB,QAE9HykB,EADA+C,EAAY3e,SACFkY,EAAK6H,YAAYtY,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAAUje,EAAKzO,QAAQ2nB,MAE1DlD,EAAKtT,UAAU6C,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAAUje,EAAKzO,QAAQ2nB,OAE9DlD,EAAK7E,WAAa6E,EAAK7gB,IAAMnF,EAAMuF,KAAKgL,UAAUkc,EAAYxnB,QAAU4D,EAAKmgB,sBACrFU,EAAU1D,EAAKtT,UAAU6C,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAAUje,EAAKzO,QAAQ2nB,OAExD,QAAPuG,IACHzJ,EAAK7E,WAAanhB,EAAMuF,KAAKgL,UAAUvQ,EAAMuF,KAAKiF,QAAQiiB,EAAYtnB,OAASnF,EAAMuF,KAAKgL,UAAUvQ,EAAMuF,KAAKiF,QAAQwb,EAAKC,cAC5HwD,EAAYzD,EAAK6H,YAAYtY,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAAUje,EAAKzO,QAAQ2nB,OAC9DlD,EAAK7E,WAAanhB,EAAMuF,KAAKgL,UAAUkc,EAAYtnB,KAAO6gB,EAAK/gB,OAAS4D,EAAKmgB,sBACrFS,EAAYzD,EAAK6H,YAAYtY,EAAEmY,EAAEO,SAAU1Y,EAAEqY,EAAEK,SAAUje,EAAKzO,QAAQ2nB,QAGzElZ,EAAKmL,QAAQ,UACVpW,MAAOA,EACPihB,MACI7Q,QAAS6Q,EAAK7Q,QACdlQ,MAAO+gB,EAAKC,YACZ9gB,IAAK6gB,EAAKE,WAEdjhB,MAAOjF,EAAMwD,SAAS0d,YAAYuI,GAClCtkB,IAAKnF,EAAMwD,SAAS0d,YAAYwI,GAChC1U,UAAWnM,EAAK0X,gBAAgByF,MAIpCyD,EAAYkG,EACZjG,EAAUkG,GAHV/mB,EAAKgnB,kBAAkBpD,EAAazG,EAAK/G,WAAYwK,EAAWC,MAMxEoG,QAAS,SAAUva,GAAV,GA0BD8Y,GAzBAiB,EAAaxxB,EAAEyX,EAAE+G,eACjBrX,EAAQ,GAAIwE,MAAKgjB,EAAYxnB,MAAMuE,WACnCrE,EAAM,GAAIsE,MAAKgjB,EAAYtnB,IAAIqE,WAC/BimB,EAAMrI,EAAUkI,EACpBtf,GAAK8S,WAAY,EACjB9S,EAAKnH,OAAOknB,oBACD,SAAPN,EACAtqB,EAAMnF,EAAMwD,SAAS0d,YAAYwI,GACnB,SAAP+F,EACPxqB,EAAQjF,EAAMwD,SAAS0d,YAAYuI,GACrB,QAAPgG,EAEHtqB,EADA6gB,EAAK7E,UACCnhB,EAAMuF,KAAKiF,QAAQxK,EAAMwD,SAAS0d,YAAYwI,IAE9C1pB,EAAMwD,SAAS0d,YAAYwI,GAEvB,QAAP+F,IACHzJ,EAAK7E,WACLlc,EAAQ,GAAIwE,MAAKzJ,EAAMwD,SAAS0d,YAAYuI,IAC5CxkB,EAAM+qB,SAAS,GACf/qB,EAAMgrB,WAAW,IAEjBhrB,EAAQjF,EAAMwD,SAAS0d,YAAYuI,IAGvC4E,EAAYre,EAAKmL,QAAQ,aACzBpW,MAAOA,EACPihB,MACI7Q,QAAS6Q,EAAK7Q,QACdlQ,MAAO+gB,EAAKC,YACZ9gB,IAAK6gB,EAAKE,WAEdjhB,MAAOA,EACPE,IAAKA,EACL6P,UAAWhF,EAAKnH,OAAO0X,gBAAgByF,MAEtCqI,GAAalpB,EAAIqE,WAAavE,EAAMuE,YACjCijB,EAAYxnB,MAAMuE,WAAavE,EAAMuE,WAAaijB,EAAYtnB,IAAIqE,WAAarE,EAAIqE,YACnFwG,EAAKnH,OAAO0mB,sBAAsBxqB,GAClCiL,EAAK4e,aAAaa,EAAK1qB,GACnBE,MAAOA,EACPE,IAAKA,MAIjB6gB,EAAO,KACPjhB,EAAQ,MAEZmrB,WAAY,WACRlgB,EAAK8S,WAAY,EACjB9S,EAAKnH,OAAOknB,oBACZ/J,EAAO,KACPjhB,EAAQ,SAIpB6pB,aAAc,SAAUa,EAAK1qB,EAAOkM,GAAtB,GA4DF4Z,GA3DJ7a,EAAO1P,KACP6vB,EAAc,SAAUprB,EAAOqrB,GAC/B,IACIpgB,EAAKqgB,iBAAkB,EACvBtrB,EAAMiM,OAAOC,GACbjB,EAAKsgB,cAAcvrB,GACrB,QACEiL,EAAKqgB,iBAAkB,EAEtBrgB,EAAKmL,QAAQ5O,IAAQxH,MAAOA,MACzBqrB,GACAA,IAECpgB,EAAK2e,cACN3e,EAAKzH,WAAWsmB,SAIxB0B,EAAiB,SAAUxrB,GAC3B,MAAIA,GAAM4M,eACC3B,EAAKzH,WAAWioB,SAASzrB,EAAMK,KAE/B4K,EAAKzH,WAAWrH,IAAI6D,EAAMyM,eAGrCif,EAAe,WAAA,GAIHxrB,GAKAE,EARR4O,EAAOwc,EAAexrB,EACf,UAAP0qB,GAAyB,SAAPA,IACdxe,EAAUhM,QACNA,EAAQjF,EAAMuF,KAAKiF,QAAQuJ,EAAK9O,OACpCjF,EAAMuF,KAAKmE,QAAQzE,EAAOwF,EAAgBwG,EAAUhM,QACpDgM,EAAUhM,MAAQA,GAElBgM,EAAU9L,MACNA,EAAMnF,EAAMuF,KAAKiF,QAAQuJ,EAAK5O,KAClCnF,EAAMuF,KAAKmE,QAAQvE,EAAKsF,EAAgBwG,EAAU9L,MAClD8L,EAAU9L,IAAMA,IAGxB6K,EAAKzH,WAAWoL,kBAAkBI,GAClCoc,EAAYpc,IAEZ2c,EAAmB,WAAA,GACf3c,GAAOwc,EAAexrB,GAEtBqrB,EAAW,WACXpgB,EAAKsgB,cAAcvc,GACf/D,EAAKoQ,YACLpQ,EAAKoQ,WAAWvZ,OAAOpI,KAAKkyB,IAGhCC,EAAY7c,EAAKjC,cACjB7M,MAAOF,EAAME,MACbE,IAAKJ,EAAMI,MATXwrB,EAWOC,EAAUxrB,GACrB+qB,GAAYngB,EAAKzH,WAAWlB,IAAIupB,GAAYR,GAE5CrrB,GAAM4M,gBAAkB5M,EAAM0M,gBAC1BoZ,EAAqB7a,EAAKzO,QAAQ8M,SAASwc,mBAC/C7a,EAAK6gB,qBAAqB9rB,EAAO2rB,EAAkBD,GAC/CxoB,MAAO4iB,EAAmBI,gBAC1BjtB,KAAM6sB,EAAmBiG,cAAgBjG,EAAmBiG,cAAgBzjB,GAC5E0jB,eAAgBlG,EAAmBK,qBACnC8F,WAAYnG,EAAmBM,oBAGnCgF,EAAYngB,EAAKzH,WAAWioB,SAASzrB,EAAMK,OAGnD6rB,mBAAoB,SAAU7qB,GAE1B,MADAA,GAAYtI,EAAEsI,GAAWM,QAAQ,IAAM1G,EAAMyD,KAAK,OAAS,KACpDnD,KAAKiI,WAAWioB,SAASpqB,EAAU3C,KAAKzD,EAAMyD,KAAK,UAE9DmW,WAAY,SAAUrY,GAClBjB,KAAKwrB,QAAQlS,WAAWrY,IAE5Bgb,MAAO,WACHjc,KAAK+b,QAAQE,SAEjB2U,cAAe,SAAUd,EAAUltB,EAAOiuB,GAA3B,GAGH9iB,GACApG,EACAjK,EAEI6sB,EAYJ/Q,EAlBJrT,EAAWnG,KAAKiB,QAAQkF,QACxBA,MAAa,GAAQA,EAAS2kB,gBAAiB,GAC3C/c,EAAW/N,KAAKiB,QAAQ8M,SACxBpG,EAAQoG,EAAS4b,kBACjBjsB,QAAcyI,GAAS2kB,eAAiBrgB,EAAStE,EAAS2kB,aAAe/c,EAAS5H,SAAS2kB,aAC3F9qB,KAAK8wB,mBAAqBluB,EAAMwO,gBAC5BmZ,EAAqBvqB,KAAKiB,QAAQ8M,SAASwc,mBAC/C5iB,EAAQ4iB,EAAmBZ,kBAEvBjsB,EADAkF,EAAMoO,cACCuZ,EAAmBwG,4BAA8BxG,EAAmBwG,4BAA8B/jB,GAElGud,EAAmByG,yBAA2BzG,EAAmByG,yBAA2B9jB,GAEnG2jB,IACAlpB,EAAQ4iB,EAAmBC,uBAC3B9sB,EAAO6sB,EAAmB0G,wBAA0B1G,EAAmB0G,wBAA0BhkB,KAGrGuM,IACI5S,KAAM,UACNlJ,KAAMmzB,EAAgB9iB,EAASyM,YAAczM,EAASoG,QACtD7F,MAAO,WACHwhB,OAGN9vB,KAAKme,aAAeze,EAAMyX,MAC5BqC,EAAQrb,MACJyI,KAAM,aACNlJ,KAAMqQ,EAASyK,OACflK,MAAO,WACHwhB,GAAS,MAIrB9vB,KAAK0d,gBACL1d,KAAKsZ,YACD1W,MAAOA,EACPlF,KAAMA,EACNiK,MAAOA,EACP6R,QAASA,IAEbxZ,KAAKod,eAEL0S,KAGRhI,SAAU,SAAUnX,GAAV,GAGFlM,GAEAspB,EAJA5nB,EAAWnG,KAAKwrB,QAAQrlB,SACxB8B,EAAajI,KAAKiI,UAEtB0I,GAAYA,MACRod,EAAY/tB,KAAK6a,QAAQ,OAASpW,MAAOkM,KACxCod,IAAc5nB,GAAYA,EAAStB,QAAUsB,KAC9CnG,KAAKkxB,cACDvgB,GAAaA,EAAUL,SACvBK,EAAYA,EAAUL,UAE1B7L,EAAQwD,EAAWlB,IAAI4J,GACnBlM,IACAzE,KAAKkxB,cACLlxB,KAAKmxB,WAAW1sB,MAI5B2sB,UAAW,WAAA,GAKHjrB,GACAL,EACAlD,EANA8S,EAAS1V,KAAKwrB,OACb9V,KAGDvP,EAAWuP,EAAOvP,SAClBL,EAAY4P,EAAO5P,UACnBlD,EAAQ5C,KAAK2wB,mBAAmB7qB,GAChCA,GAAaK,GAAYA,EAAStB,QAAU7E,KAAK6a,QAAQ5O,IACrDnG,UAAWA,EACXrB,MAAO7B,MAENA,EAAMyuB,OAAUzuB,EAAMuO,gBACvBnR,KAAKgwB,cAAcptB,EAAO,UAE9B5C,KAAKiI,WAAWsmB,UAGxB2C,YAAa,WAAA,GAGLtuB,GAFA8S,EAAS1V,KAAKwrB,QACd1lB,EAAY4P,EAAO5P,SAEnBA,KACAlD,EAAQ5C,KAAK2wB,mBAAmB7qB,GAC5BlD,GAASA,EAAMuO,iBACfnR,KAAKgwB,cAAcptB,EAAO,UAC1B5C,KAAKgwB,cAAchwB,KAAKiI,WAAWrH,IAAIgC,EAAMsO,cAAe,WAEhElR,KAAKiI,WAAW0K,cAAc/P,GAC9B8S,EAAOsC,UAGfgC,UAAW,SAAUlV,GACjB,GAAIlC,GAAsB,gBAAPkC,GAAkB9E,KAAK0kB,gBAAgB5f,GAAOA,CAC5DlC,KAGL5C,KAAKkxB,cACDtuB,EAAMwO,cACNpR,KAAKsxB,qBAAqB1uB,GAE1B5C,KAAKmxB,WAAWvuB,KAGxBuuB,WAAY,SAAUvuB,GAClB5C,KAAK+vB,iBAAkB,EACvB/vB,KAAK0d,gBACL1d,KAAKuxB,mBAAmB3uB,GACxB5C,KAAKod,eAETkU,qBAAsB,SAAU1uB,GAAV,GACd8M,GAAO1P,KACPwxB,EAAiB,WACb5uB,EAAMoO,cACNtB,EAAKyhB,WAAWvuB,GAEhB8M,EAAKoY,SAASllB,IAGlB6uB,EAAa,WACT7uB,EAAMsO,eACNtO,EAAQ8M,EAAKzH,WAAWrH,IAAIgC,EAAMsO,eAEtCxB,EAAKyhB,WAAWvuB,IAEhB2nB,EAAqB7a,EAAKzO,QAAQ8M,SAASwc,kBAC/C7a,GAAK6gB,qBAAqB3tB,EAAO4uB,EAAgBC,GAC7C9pB,MAAO4iB,EAAmBI,gBAC1BjtB,KAAM6sB,EAAmBiG,cAAgBjG,EAAmBiG,cAAgBzjB,GAC5E0jB,eAAgBlG,EAAmBK,qBACnC8F,WAAYnG,EAAmBM,oBAGvC0F,qBAAsB,SAAU3tB,EAAO4uB,EAAgBC,EAAY1jB,GAA7C,GACd5H,GAAWnG,KAAKiB,QAAQkF,SACxBurB,EAAoB/tB,EAAcwC,GAAYA,EAASurB,kBAAoB,QACrD,gBAAtBA,GAAsC1xB,KAAKquB,aAC3CmD,IAC6B,WAAtBE,EACPD,KAEAzxB,KAAK0d,gBACL1d,KAAKsZ,YACD1W,MAAOA,EACP+E,MAAOoG,EAASpG,MAChBjK,KAAMqQ,EAASrQ,KACf8b,UAEQ9b,KAAMqQ,EAAS0iB,eACfniB,MAAOkjB,IAGP9zB,KAAMqQ,EAAS2iB,WACfpiB,MAAOmjB,MAInBzxB,KAAKod,gBAGbuU,cAAe,SAAUC,GACrB,GAAIxpB,GAAWwpB,EAAQxpB,UAAY+E,GAAmB0kB,QAAqBD,KAAYnnB,EAASmnB,EAAUA,EAAQhrB,MAAQgrB,EAAQl0B,KAAMuD,GAChIgT,UAAW,gBAAkB4d,GAAe,IAAIj0B,QAAQ,MAAO,IAC/DF,KAAMm0B,EACN1uB,KAAM,GAEd,MAAK0uB,GAAiBluB,EAAciuB,IAAYA,EAAQxpB,UACpD,KAAU4L,OAAM,6CAcpB,OAZIrQ,GAAciuB,IACVA,EAAQ3d,YACR2d,EAAQ3d,WAAa,IAAMhT,EAAQgT,WAEnB,SAAhB4d,GAA0BluB,EAAciuB,EAAQl0B,QAChDk0B,EAAU/xB,GAAO,KAAU+xB,GAC3BA,EAAQl0B,KAAOk0B,EAAQl0B,KAAKoJ,MAEhC7F,EAAUpB,GAAO,EAAMoB,EAASoI,EAAgBwoB,GAAcD,IAE9D3wB,EAAUpB,GAAO,EAAMoB,EAASoI,EAAgBwoB,IAE7CnyB,EAAM0I,SAASA,GAAUnH,IAEpC+uB,cAAe,SAAUptB,EAAO2B,GAAjB,GACPrB,GAAWlD,KAAKiI,WAAWgH,OAAO/L,SAClCJ,EAAgBF,EAAME,cACtBC,EAAcH,EAAMG,YACpB4B,EAAQ/B,EAAM+B,MACdE,EAAMjC,EAAM+B,KAChBJ,GAASA,GAAU,QACnBzB,EAAgBA,GAAiBC,EACjCA,EAAcA,GAAeD,EACzBA,IACII,EACe,UAAXqB,GACAI,EAAQjF,EAAMwD,SAAS0B,QAAQhC,EAAM+B,MAAOzB,EAAUJ,GACtD+B,EAAMnF,EAAMwD,SAAS0B,QAAQhC,EAAMiC,IAAK3B,EAAUH,KAElD4B,EAAQjF,EAAMwD,SAAS0B,QAAQhC,EAAM+B,MAAO7B,EAAeI,GAC3D2B,EAAMnF,EAAMwD,SAAS0B,QAAQhC,EAAMiC,IAAK9B,EAAaG,KAGzDyB,EAAQjF,EAAMwD,SAASqB,GAAQ3B,EAAM+B,MAAO7B,GAC5C+B,EAAMnF,EAAMwD,SAASqB,GAAQ3B,EAAMiC,IAAK9B,IAE5CH,EAAMkvB,KAAK,QAASntB,GACpB/B,EAAMkvB,KAAK,MAAOjtB,KAG1B4Y,cAAe,WAAA,GAEP/H,GADAhG,EAAO1P,IAGP0V,GAAShG,EAAK8b,QADdxrB,KAAKme,aAAeze,EAAMyX,KACF,GAAI3N,GAAaxJ,KAAK+b,QAASlc,KAAWG,KAAKiB,SACnE0Z,OAAQ3a,KACRkD,SAAUwM,EAAKzH,WAAWgH,OAAO/L,SACjCwR,UAAWhF,EAAKgF,UAChBI,aAAcjK,EAAM7K,KAAK2xB,cAAe3xB,SAGpB,GAAIyJ,GAAYzJ,KAAK+b,QAASlc,KAAWG,KAAKiB,SAClE0Z,OAAQ3a,KACR8U,aAAcjK,EAAM7K,KAAK2xB,cAAe3xB,MACxCkD,SAAUwM,EAAKzH,WAAWgH,OAAO/L,SACjCwR,UAAWhF,EAAKgF,aAGxBgB,EAAOyD,KAAK,SAAU,SAAUlE,GAC5B,MAAIvF,GAAKmL,QAAQ,UACT/U,UAAWmP,EAAEnP,UACbrB,MAAOwQ,EAAErS,SAEbqS,EAAEO,iBACF,IAEJ9F,EAAKqgB,iBAAkB,EACvBrgB,EAAKwhB,cACDxhB,EAAKqiB,iBACLriB,EAAKsiB,UAETtiB,EAAKuM,QALLvM,KAOJgG,EAAOyD,KAAK,OAAQ,SAAUlE,GACtBvF,EAAKmL,QAAQ1O,IACTrG,UAAWmP,EAAEnP,UACbrB,MAAOwQ,EAAErS,SAEbqS,EAAEO,mBAGVE,EAAOyD,KAAK,OAAQ,WAChBzJ,EAAKqgB,iBAAkB,EACvBrgB,EAAK0hB,cAET1b,EAAOyD,KAAK,SAAU,SAAUlE,GAC5BvF,EAAKqgB,iBAAkB,EACvBrgB,EAAKsY,YAAY/S,EAAErS,SAEvB8S,EAAOyD,KAAK,cAAe,SAAUlE,GACjCvF,EAAKkhB,cAAc,SAAUpY,GACzB9I,EAAKqgB,iBAAkB,EAClBvX,IACD9I,EAAKzH,WAAWoL,kBAAkB4B,EAAErS,OACpC8M,EAAK0hB,cAEVnc,EAAErS,OAAO,MAGpB2uB,mBAAoB,SAAU3uB,GAC1B,GAAI8S,GAAS1V,KAAKwrB,OACb5oB,GAAMqO,UAAWrO,EAAMuO,iBACpBvO,EAAMuO,gBACNnR,KAAKgwB,cAAcptB,EAAMsO,aAAelR,KAAKiI,WAAWrH,IAAIgC,EAAMsO,cAAgBtO,GAEtF5C,KAAKgwB,cAAcptB,IAEvB5C,KAAKmG,SAAWuP,EAAOsE,UAAUpX,IAErColB,YAAa,SAAUljB,GACnB,GAAI4K,GAAO1P,KAAM4C,EAAsB,gBAAPkC,GAAkB4K,EAAKgV,gBAAgB5f,GAAOA,CACzElC,KAGDA,EAAMwO,cACN1B,EAAKuiB,uBAAuBrvB,GAE5B8M,EAAKkhB,cAAc,SAAUpY,GACpBA,GACD9I,EAAKwiB,aAAatvB,IAEvBA,KAGX8hB,gBAAiB,SAAU5f,GACvB,GAAIqtB,GAAanyB,KAAKiI,WAAWioB,SAASprB,EAI1C,OAHKqtB,KACDA,EAAaptB,EAAmB/E,KAAK+d,MAAOjZ,IAEzCqtB,GAEXC,mBAAoB,SAAUztB,EAAOE,GACjC,MAAO,IAAInF,GAAMuE,KAAKwO,MAAMzS,KAAK+d,OAAOxY,QACpC+M,MAAO,KACPC,UAEQD,MAAO,MACPC,UAEQ/O,MAAO,QACPgP,SAAU,MACVnS,MAAOsE,IAGPnB,MAAO,MACPgP,SAAU,MACVnS,MAAOsE,IAGPnB,MAAO,QACPgP,SAAU,KACVnS,MAAOwE,MAKfyN,MAAO,MACPC,UAEQ/O,MAAO,QACPgP,SAAU,MACVnS,MAAOsE,IAGPnB,MAAO,MACPgP,SAAU,KACVnS,MAAOsE,QAKxB+N,WAEPwf,aAAc,SAAUtvB,GACf5C,KAAK6a,QAAQ9O,IAAUtH,MAAO7B,KAC3B5C,KAAKiI,WAAW7C,OAAOxC,IACvB5C,KAAKiI,WAAWsmB;EAI5B0D,uBAAwB,SAAUrvB,GAAV,GAMhByvB,GACArB,EA8BAsB,EACAC,EAQIhI,EA7CJ7a,EAAO1P,KACPwyB,EAAe5vB,EACfuD,EAAWuJ,EAAKzO,QAAQkF,SAKxBurB,EAAoB/tB,EAAcwC,GAAYA,EAASurB,kBAAoB,SAJ3Ee,EAKe,WAAA,GACXN,GAAaK,EAAathB,aAAeshB,EAAeA,EAAahhB,eACrEiC,EAAO/D,EAAKzH,WAAWrH,IAAIuxB,EAAWjhB,aAC1CxB,GAAKsgB,cAAcvc,GACnB/D,EAAKwiB,aAAaC,IARlBO,EAUW,WACPF,EAAathB,eACbshB,EAAe9iB,EAAKzH,WAAWrH,IAAI4xB,EAAathB,eAEpDxB,EAAKwiB,aAAaM,KAEG,UAArBd,GAAiChiB,EAAKohB,qBACtCuB,EAA+B,WAC3B3iB,EAAKkhB,cAAc,SAAUpY,GACpBA,GACDia,KAELD,IAEPxB,EAA2B,WACvBthB,EAAKkhB,cAAc,SAAUpY,GACpBA,GACDka,KAELF,KAGPF,EAAiBtB,GAA4B0B,EAC7CH,EAAqBF,GAAgCI,EACrD/iB,EAAKohB,kBACDluB,EAAMoO,cACNuhB,IAEAD,KAGA/H,EAAqB7a,EAAKzO,QAAQ8M,SAASwc,mBAC/C7a,EAAK6gB,qBAAqB3tB,EAAO2vB,EAAoBD,GACjD3qB,MAAO4iB,EAAmBZ,kBAC1BjsB,KAAM6sB,EAAmBoI,gBAAkBpI,EAAmBoI,gBAAkB7lB,GAChF2jB,eAAgBlG,EAAmBE,uBACnCiG,WAAYnG,EAAmBG,uBAI3CoG,gBAAiB,WACb,QAAS9wB,KAAKwrB,QAAQ1lB,WAE1B8sB,YAAa,SAAUrqB,GACnB,GAAImH,GAAO1P,IACX0P,GAAKmjB,QAAQ,UAAW,WACpB,OAASC,SAAUpjB,EAAK0D,WAE5B7K,EAAK4L,WAET4e,UAAW,SAAUxqB,GACjB,GAAImH,GAAO1P,IACP0P,GAAKzO,QAAQkF,WACTuJ,EAAKsjB,oBACLzqB,EAAK0P,OAAOlM,GAAQ2D,EAAKsjB,oBAE7BtjB,EAAKsjB,mBAAqB,SAAU/d,GAChCvF,EAAKsY,YAAY/S,EAAEnQ,MAEvByD,EAAK4Q,KAAKpN,GAAQ2D,EAAKsjB,oBACnBtjB,EAAKujB,iBACL1qB,EAAK0P,OAAO/L,GAAKwD,EAAKujB,iBAE1BvjB,EAAKujB,gBAAkB,SAAUhe,GAC7BvF,EAAKoY,SAAS7S,EAAEtE,YAEpBpI,EAAK4Q,KAAKjN,GAAKlM,KAAKizB,iBAChBvjB,EAAKwjB,kBACL3qB,EAAK0P,OAAO9L,GAAMuD,EAAKwjB,kBAE3BxjB,EAAKwjB,iBAAmB,SAAUje,GAC9BvF,EAAKsK,UAAU/E,EAAEnQ,MAErByD,EAAK4Q,KAAKhN,GAAMnM,KAAKkzB,mBAErBxjB,EAAKyjB,sBACL5qB,EAAK0P,OAAO,WAAYvI,EAAKyjB,sBAEjCzjB,EAAKyjB,qBAAuB,SAAUle,GAAV,GAEhBme,GACA1Z,CAFJzE,GAAE1M,OACE6qB,EAAgB,aAAene,GAC/ByE,EAAS0Z,EAAgB,gBAAkB,aAC1C1jB,EAAKmL,QAAQ,YACVtS,KAAM0M,EAAE1M,KACR8qB,UAAWpe,EAAEoe,UACb3Z,OAAQA,EACRzU,KAAMgQ,EAAEhQ,SAERmuB,IACA1jB,EAAK4jB,aAAere,EAAEoe,WAE1B3jB,EAAK6jB,YAAYte,EAAE1M,MACnBmH,EAAKzK,KAAKgQ,EAAEhQ,SAIxBsD,EAAK4Q,KAAK,WAAYzJ,EAAKyjB,sBACvBzjB,EAAK8jB,sBACLjrB,EAAK0P,OAAO,WAAYvI,EAAK8jB,sBAEjC9jB,EAAK8jB,qBAAuB,WACxB,GAAIjrB,GAAOvI,IACP0P,GAAKoQ,aACLvX,EAAKkrB,mBAAmB/jB,EAAKoQ,YAC7BpQ,EAAK6Q,UACL7Q,EAAK0Y,wBAGb7f,EAAK4Q,KAAK,WAAYzJ,EAAK8jB,uBAE/BD,YAAa,SAAU3sB,GAAV,GASG8sB,GAKAC,EAMAC,EAGAC,EAtBRnkB,EAAO1P,IACP4G,IAAQ8I,EAAKuL,MAAMrU,KACf8I,EAAKoc,eACLpc,EAAKkjB,YAAYljB,EAAKoc,eAE1Bpc,EAAKoc,cAAgBpc,EAAKokB,YAAYltB,GACtC8I,EAAKyY,kBAAoBvhB,EACrB8I,EAAKqkB,YAAc,IAAMrkB,EAAKyO,aAC1BuV,EAAatmB,IACb6N,MAAOvL,EAAKuL,MACZ1S,KAAM3B,EACNgB,GAAIlI,EAAMkI,KAEV+rB,EAAcjkB,EAAK8T,QAAQnd,KAAK,qCAChCstB,EAAYre,GAAG,mBACfqe,EAAYK,YAAYN,GAExBhkB,EAAK8T,QAAQnd,KAAK,sBAAsB4tB,QAAQP,GAEhDE,EAAclkB,EAAK8T,QAAQnd,KAAK,yBAAyBod,YAAY,oBACzEmQ,EAAY/uB,MAAMwB,KAAK,WAAaO,EAAKhJ,QAAQ,MAAO,OAAOs2B,eAAenN,SAAS,sBAEnF8M,EAAankB,EAAK8T,QAAQnd,KAAK,6BACnCwtB,EAAWxtB,KAAK,UAAYO,EAAKhJ,QAAQ,MAAO,OAAS,KAAK6e,KAAK,WAAY,eAI3FlU,KAAM,SAAU3B,GACZ,GAAI8I,GAAO1P,IACX,OAAI4G,IACA8I,EAAK6jB,YAAY3sB,GACjB8I,EAAKykB,SACL,GAEGzkB,EAAKoc,eAEhBsI,SAAU,WACN,MAAOp0B,MAAKuI,OAAO3B,MAEvBktB,YAAa,SAAUltB,GACnB,GAAI2B,GAAOvI,KAAKq0B,gBAAgBztB,EAUhC,OATA5G,MAAK+yB,UAAUxqB,IACX7I,EAAM2L,QAAQipB,sBAAwB50B,EAAM2L,QAAQC,YACpD/C,EAAK4P,QAAQP,IAAI,mBAAoB,cACrCrP,EAAK4P,QAAQP,IAAI,eAAgB,eAErC5X,KAAK2rB,OAAO/a,IAAI,gBAAiBrI,EAAKgsB,gBACtCv0B,KAAK2rB,OAAO/a,IAAI,qBAAsBrI,EAAKisB,qBAC3Cx0B,KAAK2rB,OAAO/a,IAAI,sBAAuBrI,EAAKksB,mBAAqBlsB,EAAKksB,qBAAuBlsB,EAAKisB,qBAClGx0B,KAAK2rB,OAAO/a,IAAI,gBAAiBlR,EAAMkG,OAAO,WAAY2C,EAAKod,cACxDpd,GAEX0U,OAAQ,SAAUyX,GAAV,GACA30B,GAAOC,KAAK20B,UACZC,EAAc50B,KAAKC,MACnBsI,EAAOvI,KAAKuI,MACXA,IAASA,EAAK+G,UAGfolB,GAAUE,GAAe70B,EAAKlB,QAAU+1B,EAAY/1B,OAASkB,EAAKjB,SAAW81B,EAAY91B,SACzFkB,KAAKgyB,SAAUtY,OAAQ,WACvB1Z,KAAKC,MAAQF,KAGrBqoB,oBAAqB,WACjB,GAAInjB,GAAOjF,KAAK2rB,OAAOkJ,aAAchV,EAAY7f,KAAK8f,WAAYnb,EAAQkb,EAAUlb,KAChFA,KAAUjF,EAAMuF,KAAK6vB,cAAc7vB,EAAMiF,EAAQvF,GAAQuF,EAAQ2V,EAAUhb,OAC3EI,EAAK8vB,YAAYpwB,EAAMqwB,cAAerwB,EAAMswB,WAAYtwB,EAAMuF,YAGtEmqB,gBAAiB,SAAUztB,GAAV,GAGLsuB,GAAkCrjB,EAFtCtJ,EAAOvI,KAAKib,MAAMrU,EACtB,IAAI2B,EAAM,CAKN,GAJI2sB,EAAavxB,EAAc4E,GAAOsJ,EAAOtJ,EAAKsJ,WACvCA,KAASpH,IAChBoH,EAAOnS,EAAM8I,OAAOD,EAAKsJ,MAAMpS,UAE/BoS,EASA,KAAUmC,OAAM,wBARhBzL,GAAO,GAAIsJ,GAAK7R,KAAK+b,QAASpV,EAAY9G,GAAO,KAAUG,KAAKiB,QAASi0B,EAAa3sB,MAClFmM,UAAW1U,KAAK0U,UAChBzP,KAAMjF,KAAKiF,OACXkkB,UAAWzpB,EAAMgG,UAAU1F,KAAKiB,QAAQkoB,WACxCC,QAAS1pB,EAAMgG,UAAU1F,KAAKiB,QAAQmoB,SACtCF,cAAelpB,KAAKszB,iBAMhC,MAAO/qB,IAEXuS,OAAQ,WAAA,GAEAvS,GACA4sB,EACAC,EACAF,EACAtuB,EACAiL,EACAnN,EACAhG,EAII22B,EAZJpa,EAAQjb,KAAKiB,QAAQga,KAWzB,KAFAjb,KAAKib,SACLjb,KAAK+zB,YAAc,EACdrvB,EAAM,EAAGhG,EAASuc,EAAMvc,OAAQgG,EAAMhG,EAAQgG,IAC3C2wB,GAAU,EACd9sB,EAAO0S,EAAMvW,GACbwwB,EAAavxB,EAAc4E,GACvB2sB,GACArjB,EAAOjL,EAAO2B,EAAKsJ,KAAOtJ,EAAKsJ,KAAOtJ,QAC3BsJ,KAASpH,IAChB7D,EAAO2B,EAAK3B,MAAQ2B,EAAKZ,MACzB0tB,GAAU,IAGdxjB,EAAOjL,EAAO2B,EAElB4sB,EAAcxrB,EAAa/C,GACvBuuB,IAAgBE,IAChB9sB,EAAKsJ,KAAOsjB,EAAYtjB,KACxBsjB,EAAYxtB,MAAQ3H,KAAKiB,QAAQ8M,SAASkN,MAAMrU,GACvB,QAArBuuB,EAAYtjB,KACZsjB,EAAYpnB,UAAaunB,OAAQt1B,KAAKiB,QAAQ8M,SAASunB,QAC3B,WAArBH,EAAYtjB,OACnBsjB,EAAYpnB,UACRtJ,MAAOzE,KAAKiB,QAAQ8M,SAAStJ,MAC7BQ,KAAMjF,KAAKiB,QAAQ8M,SAAS9I,KAC5BswB,KAAMv1B,KAAKiB,QAAQ8M,SAASwnB,QAIxChtB,EAAO1I,GAAS8H,MAAOf,GAAQuuB,EAAaD,EAAa3sB,MACrD3B,IACA5G,KAAKib,MAAMrU,GAAQ2B,EACnBvI,KAAK+zB,cACAqB,IAAY7sB,EAAK6sB,WAClBA,EAAWxuB,GAInBwuB,KACAp1B,KAAKmoB,kBAAoBiN,IAGjCjB,OAAQ,WACJn0B,KAAKiI,WAAW4jB,SAEpB/O,YAAa,WACT,GAAIpN,GAAO1P,KAAMiB,EAAUyO,EAAKzO,QAASgH,EAAahH,EAAQgH,UAC9DA,GAAa+C,EAAQ/C,IAAgBhE,KAAMgE,GAAeA,GACtDhH,EAAQiC,UAAc+E,YAAsBiK,IAErCjK,YAAsBiK,MAC7BjR,EAAQiC,SAAW+E,EAAWhH,QAAQ+N,OAAS/G,EAAWhH,QAAQ+N,OAAO9L,SAAW,IAFpF+E,EAAapI,GAAO,EAAMoI,GAAc+G,QAAU9L,SAAUjC,EAAQiC,YAIpEwM,EAAKzH,YAAcyH,EAAKub,gBACxBvb,EAAKzH,WAAWgQ,OAAOtM,GAAQ+D,EAAKub,iBAAiBhT,OAAOrM,GAAU8D,EAAKwb,kBAAkBjT,OAAOpM,GAAO6D,EAAKyb,gBAEhHzb,EAAKub,gBAAkBpgB,EAAM6E,EAAKsiB,QAAStiB,GAC3CA,EAAKwb,iBAAmBrgB,EAAM6E,EAAK8lB,cAAe9lB,GAClDA,EAAKyb,cAAgBtgB,EAAM6E,EAAK+lB,OAAQ/lB,IAE5CA,EAAKzH,WAAavI,EAAMuE,KAAKiO,oBAAoB6B,OAAO9L,GAAYkR,KAAKxN,GAAQ+D,EAAKub,iBAAiB9R,KAAKvN,GAAU8D,EAAKwb,kBAAkB/R,KAAKtN,GAAO6D,EAAKyb,eAC9Jzb,EAAKzO,QAAQgH,WAAayH,EAAKzH,YAEnCwtB,OAAQ,WACJz1B,KAAK01B,WAAU,IAEnBF,cAAe,WACXx1B,KAAK01B,WAAU,IAEnBA,UAAW,SAAU7mB,GACjB,GAAIgG,GAAU7U,KAAK6U,QAAQxO,KAAK,uBAChC3G,GAAM+F,GAAGkwB,SAAS9gB,EAAShG,IAE/BkO,WAAY,WAAA,GAICrY,GACDgD,EACAlE,EACAoD,EACAqB,EAPJyH,EAAO1P,KACP0U,EAAYhF,EAAKzO,QAAQyT,UACzBkhB,IACJ,KAASlxB,EAAM,EAAGA,EAAMgQ,EAAUhW,OAAQgG,IAAO,CAK7C,GAJIgD,EAAWgN,EAAUhQ,GACrBlB,EAAQkE,EAASlE,MACjBoD,EAAOc,EAASd,MAAQpD,EACxByE,EAAaP,EAASO,YACrBzE,IAAUyE,EACX,KAAU+L,OAAM,gFAEpBtE,GAAKgF,UAAUvW,MACXqF,MAAOA,EACPoD,KAAMA,EACNe,MAAOD,EAASC,OAASnE,EACzBuE,cAAeL,EAASK,eAAiB,OACzCC,eAAgBN,EAASM,gBAAkB,QAC3CK,eAAgBX,EAASW,gBAAkB,QAC3CH,eAA2C,MAA3BR,EAASQ,gBAAyBR,EAASQ,eAC3D6N,SAAUrO,EAASqO,WAAY,EAC/B9N,WAAYyH,EAAKmmB,oBAAoB5tB,EAAYrB,EAAMgvB,KAG1DlmB,EAAKzO,QAAQ0nB,SAGdnrB,EAAEs4B,KAAKzjB,MAAM,KAAMujB,GAAkBG,KAAK,WACtCrmB,EAAKnH,KAAKmH,EAAKyY,qBAHnBzY,EAAK6jB,YAAY7jB,EAAKyY,oBAO9B0N,oBAAqB,SAAU7K,EAAYgL,EAAWC,GAAjC,GACbvmB,GAAO1P,KACPiI,EAAa+C,EAAQggB,IAAgB/mB,KAAM+mB,GAAeA,EAC1DkL,EAAqBx2B,EAAMuE,KAAK2G,WAAWmJ,OAAO9L,EAQtD,OAPIyH,GAAKzO,QAAQ0nB,SACbsN,EAAS93B,KAAK+3B,EAAmBrK,MAAM,WACnCnc,EAAKymB,oBAAoBn2B,KAAMg2B,MAGnCtmB,EAAKymB,oBAAoBD,EAAoBF,GAE1CE,GAEXC,oBAAqB,SAAUnL,EAAYgL,GAAtB,GACbtmB,GAAO1P,KACPmhB,EAAYzR,EAAKzO,QAAQ8d,OAASrP,EAAKzO,QAAQ8d,MAAMrK,UAAUhW,OAC/D03B,EAAoBjV,GAAazR,EAAKzO,QAAQ8d,MAAMrK,UAAU2T,QAAQ2N,OACrEtmB,EAAK0b,yBAA2BgL,IACjC1mB,EAAK0b,wBAA0BvgB,EAAM6E,EAAK2mB,iBAAkB3mB,GAC5DA,EAAK2b,yBAA2BxgB,EAAM6E,EAAK8lB,cAAe9lB,GAC1DA,EAAK4b,sBAAwBzgB,EAAM6E,EAAK+lB,OAAQ/lB,IAEhD0mB,GACApL,EAAW7R,KAAKxN,GAAQ+D,EAAK0b,yBAAyBjS,KAAKvN,GAAU8D,EAAK2b,0BAA0BlS,KAAKtN,GAAO6D,EAAK4b,wBAG7H+K,iBAAkB,WACd,GAAI3mB,GAAO1P,IACX0P,GAAKnH,KAAKmH,EAAKyY,oBAEnBxL,WAAY,WACR,GAAIjN,GAAO1P,IACX0P,GAAKic,OAASjsB,EAAM42B,YAChBzB,aAAc,GAAI1rB,MAAKnJ,KAAKiB,QAAQgE,MACpCsxB,cAAe,GACfC,mBAAoB,KAExB9mB,EAAKic,OAAOxS,KAAK,SAAU,SAAUlE,GACjB,iBAAZA,EAAEzR,OACFkM,EAAKnH,KAAKmH,EAAKyY,sBAI3BvL,SAAU,WAAA,GACFlN,GAAO1P,KACPiB,EAAUyO,EAAKzO,QACfnC,EAASmC,EAAQnC,OACjBD,EAAQoC,EAAQpC,KACpB6Q,GAAKqM,QAAUrM,EAAKmF,QAAQkS,SAAS,oCAAoC5jB,KAAK,OAAQ,QAAQA,KAAK,wBAAwB,GACvHuM,EAAKyO,aACLzO,EAAKqM,QAAQgL,SAAS,sBAEtBjoB,GACA4Q,EAAKqM,QAAQjd,OAAOA,GAEpBD,GACA6Q,EAAKqM,QAAQld,MAAMA,IAG3BoG,KAAM,SAAU5E,GAIZ,MAHa,OAATA,GAAiB6J,EAAQ7J,IAAU6J,EAAQlK,KAAKiB,QAAQ6nB,MAAQ5e,EAAQ7J,IAAU6J,EAAQlK,KAAKiB,QAAQ8nB,MACvG/oB,KAAK2rB,OAAO/a,IAAI,eAAgBvQ,GAE7B6J,EAAQlK,KAAK2rB,OAAO/qB,IAAI,kBAEnCic,SAAU,WAAA,GAOFzU,GACAob,EAPA9T,EAAO1P,KACPiB,EAAUyO,EAAKzO,QACfw1B,IACAx1B,GAAQuiB,UACRiT,EAAWj5B,EAAEwN,QAAQ/J,EAAQuiB,SAAWviB,EAAQuiB,SAAWviB,EAAQuiB,UAEnEpb,EAAWpI,KAAKme,YAAc7Q,GAAwBD,GACtDmW,EAAUhmB,EAAE4K,GACZ2F,SAAU9M,EAAQ8M,SAClB2b,IAAKlsB,EAAEk5B,KAAKD,EAAU,SAAUljB,GAC5B,MAAe,OAARA,GAA8B,OAAbA,EAAK3M,OAC9BlI,OAAS,EACZkJ,GAAIlI,EAAMkI,GACVW,KAAMmH,EAAKyY,kBACXlN,MAAOvL,EAAKuL,MACZ0b,WAAYjnB,EAAKqkB,YACjB5tB,SAAUuJ,EAAKzO,QAAQkF,YAE3BuJ,EAAKqM,QAAQpD,OAAO6K,GACpB9T,EAAK8T,QAAUA,EACf9jB,EAAMyZ,KAAKzJ,EAAK8T,QAAS9T,EAAKic,QAC9BnI,EAAQ1K,GAAG5N,GAAQD,EAAI,SAAU,SAAUgK,GACvCA,EAAEO,iBACF9F,EAAKknB,cAETpT,EAAQ1K,GAAG5N,GAAQD,EAAI,kBAAmB,SAAUgK,GAChDA,EAAEO,iBACF9F,EAAKoY,aAETtE,EAAQ1K,GAAG5N,GAAQD,EAAI,kBAAmB,SAAUgK,GAChDA,EAAEO,iBACF9F,EAAKiY,cAAc1S,EAAE0F,UAEzB6I,EAAQ1K,GAAG5N,GAAQD,EAAI,6BAA8B,SAAUgK,GAAV,GAUrC4hB,GATRC,EAAKt5B,EAAEwC,MACPiF,EAAO,GAAIkE,MAAKuG,EAAKzK,QACrByU,EAAS,GACTqd,EAAc,GAAI5tB,MAClBjG,EAAWwM,EAAKzO,QAAQiC,QAE5B,IADA+R,EAAEO,iBACEshB,EAAG7d,SAAS,eACZS,EAAS,QACLxW,GACI2zB,EAAiBn3B,EAAMwD,SAASqN,OAAOwmB,EAAa7zB,GACxD+B,EAAOvF,EAAMwD,SAAS0B,QAAQmyB,EAAaA,EAAYvmB,oBAAqBqmB,IAE5E5xB,EAAO8xB,MAER,IAAID,EAAG7d,SAAS,cACnBS,EAAS,OACTzU,EAAOyK,EAAKnH,OAAOyuB,eAChB,IAAIF,EAAG7d,SAAS,cACnBS,EAAS,WACTzU,EAAOyK,EAAKnH,OAAO0uB,mBAChB,IAAIH,EAAG7d,SAAS,mBAAqBvJ,EAAKyO,YAE7C,MADAzO,GAAKiY,gBACL,CAECjY,GAAKmL,QAAQ,YACVtS,KAAMmH,EAAKyY,kBACXzO,OAAQA,EACRzU,KAAMA,KAEVyK,EAAKzK,KAAKA,KAGlBue,EAAQ1K,GAAG5N,GAAQD,EAAI,mEAAoE,SAAUgK,GACjGA,EAAEO,gBACF,IAAI5O,GAAOpJ,EAAEwC,MAAMmD,KAAKzD,EAAMyD,KAAK,QAC9BuM,GAAKmL,QAAQ,YACVtS,KAAM3B,EACN8S,OAAQ,aACRzU,KAAMyK,EAAKzK,WAEfyK,EAAKnH,KAAK3B,GACV8I,EAAKmF,QAAQxO,KAAK,IAAMgG,IAAeoX,YAAYpX,OAG3DmX,EAAQ1K,GAAG5N,GAAQD,EAAI,uCAAwC,SAAUgK,GACrEA,EAAEO,iBACF9F,EAAKmF,QAAQxO,KAAK,sBAAsB6wB,YAAY7qB,IACpD7O,EAAEsD,UAAUgY,GAAG3N,GAAYF,EAAI,SAAUgK,GACoB,IAArDzX,EAAEyX,EAAE0F,QAAQvU,QAAQ,sBAAsB1H,SAC1CgR,EAAKmF,QAAQxO,KAAK,IAAMgG,IAAeoX,YAAYpX,IACnD7O,EAAEsD,UAAU6c,IAAIzS,GAAQD,QAIpCuY,EAAQnd,KAAK,6BAA6ByS,GAAG,SAAU,SAAU7D,GAC7DvF,EAAKnH,KAAK0M,EAAE0F,OAAOta,SAEvBmjB,EAAQnd,KAAK,MAAM8wB,MAAM,WACrB35B,EAAEwC,MAAM+mB,SAAS,kBAClB,WACCvpB,EAAEwC,MAAMyjB,YAAY,oBAG5BkE,cAAe,SAAUyP,GACrB,GAAI1nB,GAAO1P,KAAM2a,EAASyc,GAAa1nB,EAAK8T,QAAQnd,KAAK,kBAAmBkQ,EAAO/Y,EAAE,8EAChFkS,GAAKkM,QACNlM,EAAKkM,MAAQ,GAAIlR,GAAM6L,GACnB8gB,OAAQ1c,EACR2c,SAAU,WACF5nB,EAAKkM,OAASlM,EAAK6b,WACnB7b,EAAKkM,MAAM2b,eAAc,GACzB7nB,EAAK6b,SAAS1W,QAAQxO,KAAK,SAAS4V,QACpCvM,EAAKkM,MAAM2b,eAAc,KAGjC7b,KAAM,WACGhM,EAAK6b,WACN7b,EAAK6b,SAAW,GAAI5gB,GAAS3K,KAAK6U,QAAQxO,KAAK,0BAC3CmxB,OAAQ,WACJ,GAAIvyB,GAAOjF,KAAKK,OACXqP,GAAKmL,QAAQ,YACVtS,KAAMmH,EAAKyY,kBACXzO,OAAQ,aACRzU,KAAMA,MAEVyK,EAAKzK,KAAKA,GACVyK,EAAKkM,MAAM5D,SAEVtI,EAAKyO,YACNzO,EAAKoc,cAAcjX,QAAQoH,QAC3BvM,EAAK8T,QAAQnd,KAAK,kBAAkB4V,QAAQ8K,SAAS3a,MAG7D0c,IAAKpZ,EAAKzO,QAAQ6nB,IAClBC,IAAKrZ,EAAKzO,QAAQ8nB,OAG1BrZ,EAAK6b,SAAS1W,QAAQiE,GAAG,UAAY7N,EAAI,SAAUgK,GAC3CA,EAAEmR,UAAY/b,EAAKud,KAAO3S,EAAEmR,UAAY/b,EAAK2c,MAC7CtX,EAAKkM,MAAM5D,QACXtI,EAAKoc,cAAcjX,QAAQoH,QAC3BvM,EAAK8T,QAAQnd,KAAK,kBAAkB4V,QAAQ8K,SAAS3a,OAG7DsD,EAAK6b,SAASlrB,MAAMqP,EAAKzK,SAE7BwyB,kBAAkB,KAG1B/nB,EAAKkM,MAAMF,QAEfsW,QAAS,SAAU/c,GAAV,GACDvF,GAAO1P,KACPuI,EAAOvI,KAAKuI,OACZmvB,EAAiBziB,GAAkB,eAAbA,EAAEyE,SAA4B1Z,KAAKwrB,QAAQrlB,UAAYnG,KAAK+vB,kBAAqD,YAAjC/vB,KAAKiI,WAAWhH,QAAQ4Q,MAAsB7R,KAAK+vB,eAM7J,IALA/vB,KAAK01B,WAAU,GACf11B,KAAK6yB,QAAQ,UAAW,WACpB,OAASC,SAAUpjB,EAAK0D,WAE5B6B,EAAIA,MACC1M,EAGL,MAAImvB,IACA13B,KAAK+xB,gBAA2D,YAAjC/xB,KAAKiI,WAAWhH,QAAQ4Q,KACvD,IAEA7R,KAAK6a,QAAQ,eACTnB,OAAQzE,EAAEyE,QAAU,SACpB5G,MAAOmC,EAAEnC,MACTM,MAAO6B,EAAE7B,UAIX6B,GAAkB,WAAbA,EAAEyE,SAAwB1Z,KAAKwrB,SACtCxrB,KAAKwrB,QAAQxT,QAEjBhY,KAAK+d,MAAQ/d,KAAKiI,WAAWvB,OAAO6B,EAAKod,YAAapd,EAAKovB,kBAC3DpvB,EAAKqvB,gBACLrvB,EAAKsvB,OAAO73B,KAAK+d,OACjB/d,KAAK6a,QAAQ,aACb7a,KAAK+xB,iBAAkB,GAdvB,IAgBJ+F,eAAgB,SAAU1K,EAAGE,GAAb,GAKR5H,GAJAnd,EAAOvI,KAAKuI,MAChB,OAAKA,GAAK4kB,iBAGNzH,EAAOnd,EAAK4kB,gBAAgBC,EAAGE,GAC9B5H,GAIDC,UAAWD,EAAKC,YAChBC,QAASF,EAAKE,UACdjH,WAAY+G,EAAK/G,WACjB9J,QAAS6Q,EAAK7Q,QACdgM,UAAW6E,EAAK7E,WAPT,MAJA,MAcfkX,cAAe,SAAUljB,GACrB,GAAItE,GAAS/S,EAAEqX,GAAStE,QACxB,OAAOvQ,MAAK83B,eAAevnB,EAAOuH,KAAMvH,EAAOqd,MAEnDoK,gBAAiB,SAAUtS,GACvB,MAAO1lB,MAAKuI,OAAO0X,gBAAgByF,MAGvC/b,GACAmgB,KAAOjY,KAAM,oBACbkY,MAAQlY,KAAM,qBACdmY,UAAYnY,KAAM,yBAClBoY,QAAUpY,KAAM,uBAChBqY,OAASrY,KAAM,sBACfsY,UAAYtY,KAAM,yBAClBuY,cAAgBvY,KAAM,6BACtBwY,kBAAoBxY,KAAM,iCAC1ByY,eAAiBzY,KAAM,+BAE3BpM,EAAGwyB,OAAOvuB,GACNhK,EAAMw4B,WACNx4B,EAAMw4B,SAASr4B,OAAO6J,EAAUqB,WAC5BnB,EAAmB,yBACvBF,EAAUiG,GAAGwoB,SAAW,SAAUxC,GAAV,GAQhByC,GACAC,EACAC,EATAvc,EAAU/b,KAAK+b,QACfwc,EAASxc,EAAQ,GAAG9c,MAAM+B,OA8B9B,OA7BA+a,GAAQnE,KACJ/Y,MAAOkd,EAAQld,QACfC,OAAQid,EAAQjd,WAEpBid,EAAQgL,SAASnd,GACbwuB,EAAYp4B,KACZq4B,EAAU,GAAI76B,GAAEg7B,SAChBF,EAAQvc,EAAQ1V,KAAK,wBAAwBA,KAAK,SAASuR,IAAI,eAAgB,QACnF6N,WAAW,WACP6S,EAAM1gB,IAAI,eAAgB,SAC1BwgB,EAAUnb,QAAO,GACjBmb,EAAUK,mBAAqBC,WAAYN,EAAUn3B,QAAQyoB,IAAIgP,aAAcC,KAAK,SAAU5Z,GAC1F,GAAI6Z,IACAC,KAAM9Z,EACN+Z,WAAY,EACZnD,SAAU,EACVoD,WAAY,EAEhBpD,GAASqD,OAAOJ,GAChBP,EAAQY,QAAQL,EAAKC,QACtBK,KAAK,SAAUC,GACdd,EAAQe,OAAOD,KAChBE,OAAO,WACNtd,EAAQ,GAAG9c,MAAM+B,QAAUu3B,EAC3Bxc,EAAQ0H,YAAY7Z,GACpBwuB,EAAUnb,QAAO,GACjBmb,EAAUnb,QAAO,OAGlBob,IAGXxuB,EAAiBU,EAAO1K,QACxBC,KAAM,SAAU+U,EAAS5T,GACrB,GAAIyO,GAAO1P,KAAMs5B,EAAQ55B,EAAMwD,SAAS0S,aACxC,KAAK0jB,IAAU55B,EAAMwD,SAASq2B,aAC1B,KAAUvlB,OAAM,0CAEpBzJ,GAAOoF,GAAG7P,KAAKuE,KAAKqL,EAAMmF,EAAS5T,GACnCyO,EAAKqM,QAAUrM,EAAKmF,QACpBnF,EAAK8pB,YAAc,GAAI95B,GAAMuE,KAAKwO,MAAM6mB,GACxC5pB,EAAK+pB,aAAe/5B,EAAM8d,OAC1B9N,EAAKgqB,mBACLhqB,EAAKiqB,cACLjqB,EAAKkqB,WAAWzgB,KAAK,UAAW,WACvBnZ,KAAKK,SACNqP,EAAKmqB,MAAM9d,QAAQpO,SAG3B+B,EAAKmqB,MAAM1gB,KAAK,UAAW,WACvBzJ,EAAKoqB,OAAS95B,KAAKK,QACnBqP,EAAKmL,QAAQ,YAEjBnL,EAAKrP,MAAMqP,EAAKzO,QAAQZ,QAE5BY,SACI2F,KAAM,iBACNvG,MAAO,GACP8H,YAAa,eAEjB5B,QAAS,UACTmzB,iBAAkB,WACd,GAAIhqB,GAAO1P,KAAM+5B,EAAYv8B,EAAE,cAAgBkS,EAAK+pB,aAAe,iBAAmB/pB,EAAKzO,QAAQ0G,MAAQ,OAAOE,SAAS6H,EAAKqM,QAChIrM,GAAKkqB,WAAa,GAAIl6B,GAAM+F,GAAGu0B,aAAaD,GACxC9xB,WAAYvI,EAAMwD,SAASq2B,aAC3BvxB,eAAgB,aAChBD,cAAe,OACfI,YAAauH,EAAKzO,QAAQkH,eAGlCwxB,YAAa,WACT,GAAIjqB,GAAO1P,KAAMwG,EAAOhJ,EAAE,sBAAwBkS,EAAKzO,QAAQ0G,MAAQ,OAAOE,SAAS7H,KAAK+b,QAC5FrM,GAAKmqB,MAAQ,GAAIn6B,GAAM+F,GAAGu0B,aAAaxzB,GACnCwB,eAAgB,OAChBD,cAAe,YACfE,WAAYyH,EAAK8pB,YAAYv1B,KAC7Bg2B,YAAavqB,EAAK+pB,aAClBS,UAAW,WACPxqB,EAAKoqB,OAAS95B,KAAKK,QACnBL,KAAK+b,QAAQlN,OAAO7O,KAAKiI,WAAWM,OAAO7J,OAAS,MAG5DgR,EAAKmqB,MAAM9d,QAAQpO,QAEvBwG,QAAS,WACL5J,EAAOoF,GAAGwE,QAAQ9P,KAAKrE,MACvBN,EAAMyU,QAAQnU,KAAK+b,UAEvB1b,MAAO,SAAUA,GACb,GAAiBmG,GAAbkJ,EAAO1P,IACX,OAAIK,KAAUqC,EACHgN,EAAKoqB,QAEhBtzB,EAAOkJ,EAAK8pB,YAAYj0B,QACpB/B,MAAO,OACPgP,SAAU,KACVnS,MAAOA,IACR4D,KAAK,GACJuC,GACAkJ,EAAKkqB,WAAWv5B,MAAMmG,EAAK2zB,YAC3BzqB,EAAKmqB,MAAMx5B,MAAMmG,EAAKA,OAEtBkJ,EAAKkqB,WAAWxa,OAAO,GAT3B5Y,MAaRf,EAAGwyB,OAAOpuB,GACNC,EAA0BpK,EAAM0I,SAAS,kDACzC2B,EAAqBrK,EAAM0I,SAAS,iDACpC4B,EAAuBO,EAAO1K,QAC9BC,KAAM,SAAU+U,EAAS5T,GACrB,GAAIyO,GAAO1P,KAAMs5B,EAAQ55B,EAAMwD,SAAS0S,aACxC,KAAK0jB,IAAU55B,EAAMwD,SAASq2B,aAC1B,KAAUvlB,OAAM,0CAEpBzJ,GAAOoF,GAAG7P,KAAKuE,KAAKqL,EAAMmF,EAAS5T,GACnCyO,EAAKqM,QAAUrM,EAAKmF,QACpBnF,EAAK8pB,YAAc,GAAI95B,GAAMuE,KAAKwO,MAAM6mB,GACxC5pB,EAAKgqB,mBACLhqB,EAAKiqB,cACLjqB,EAAKrP,MAAMqP,EAAKzO,QAAQZ,QAE5BY,SACI2F,KAAM,uBACNuB,YAAa,cACb9H,MAAO,IAEXkG,QAAS,UACT6zB,WAAY,SAAU/5B,GAClB,GAAI4D,GAAO5D,EAAQL,KAAKq6B,QAAQh6B,KAChCL,MAAK65B,MAAMtjB,KAAKvW,KAAKs6B,SAASr2B,EAAM8F,KAExCswB,QAAS,SAAUh6B,GACf,MAAOL,MAAKw5B,YAAYj0B,QACpB/B,MAAO,aACPgP,SAAU,KACVnS,MAAOA,IACR4D,MAEPq2B,SAAU,SAAUr2B,EAAMmE,EAAUD,GAA1B,GACFzD,GAAM,EACN6R,EAAO,GACP7X,EAASuF,EAAKvF,MAOlB,KANIyJ,IACAoO,GAAQnO,GACJ+xB,WAAY,GACZvzB,KAAMuB,KAGPzD,EAAMhG,EAAQgG,IACjB6R,GAAQnO,EAASnE,EAAKS,GAE1B,OAAO6R,IAEXmjB,iBAAkB,WAAA,GACVhqB,GAAO1P,KACPiB,EAAUyO,EAAK4qB,SAAS56B,EAAMwD,SAASq2B,aAAczvB,EAAyB4F,EAAKzO,QAAQkH,YAC/FuH,GAAKkqB,WAAap8B,EAAE,WAAayD,EAAU,aAAa4G,SAAS6H,EAAKqM,SAASyb,OAAO,WAAA,GAC9En3B,GAAQL,KAAKK,MACbmG,EAAOkJ,EAAK6qB,iBACZC,EAAah0B,EAAKH,KAAK,SAC3BqJ,GAAK0qB,WAAW/5B,GACZA,GAASm6B,EAAW5T,WAAWloB,OAAS,EACxC8H,EAAKqS,OAELrS,EAAKmH,OAET+B,EAAKoqB,OAASpqB,EAAKmqB,MAAM,GAAGx5B,MAC5BqP,EAAKmL,QAAQ,aAGrB8e,YAAa,WACT,GAAIjqB,GAAO1P,IACX0P,GAAK6qB,iBAAmB/8B,EAAE,0IAC1BkS,EAAKmqB,MAAQr8B,EAAE,qBAAqBqK,SAAS6H,EAAK6qB,iBAAiBl0B,KAAK,QAAQmxB,OAAO,WACnF9nB,EAAKoqB,OAAS95B,KAAKK,MACnBqP,EAAKmL,QAAQ,YAEjB7a,KAAK+b,QAAQ3V,QAAQ,WAAWq0B,MAAM/qB,EAAK6qB,kBAC3C7qB,EAAK0qB,WAAW1qB,EAAKkqB,WAAWj0B,OAChC+J,EAAKoqB,OAASpqB,EAAKmqB,MAAM,GAAGx5B,OAEhC8T,QAAS,WACL5J,EAAOoF,GAAGwE,QAAQ9P,KAAKrE,MACvBN,EAAMyU,QAAQnU,KAAK+b,UAEvB1b,MAAO,SAAUA,GAAV,GAKCmG,GAJAkJ,EAAO1P,KACP06B,EAAahrB,EAAKmqB,MAClBM,EAAa,GACbQ,EAAa,EAEjB,OAAIt6B,KAAUqC,EACHgN,EAAKoqB,QAEhBtzB,EAAOkJ,EAAK8pB,YAAYj0B,QACpB/B,MAAO,OACPgP,SAAU,KACVnS,MAAOA,IACR4D,KAAK,GACJuC,IACAm0B,EAAan0B,EAAKA,KAClB2zB,EAAa3zB,EAAK2zB,YAEtBzqB,EAAKkqB,WAAWj0B,IAAIw0B,GACpBzqB,EAAK0qB,WAAWD,GAChBO,EAAW/0B,IAAIg1B,GACfA,EAAaD,EAAW,GAAGr6B,MACvBs6B,GAAcD,EAAW9T,SAASloB,OAAS,EAC3CgR,EAAK6qB,iBAAiB1hB,OAEtBnJ,EAAK6qB,iBAAiB5sB,OAE1B+B,EAAKoqB,OAASa,EAlBdn0B,MAqBRf,EAAGwyB,OAAOjuB,IACZvK,OAAOC,MAAM2C,QACR5C,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.scheduler.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.scheduler', [\n        'kendo.dropdownlist',\n        'kendo.editable',\n        'kendo.multiselect',\n        'kendo.window',\n        'kendo.datetimepicker',\n        'kendo.scheduler.recurrence',\n        'kendo.scheduler.view',\n        'kendo.scheduler.dayview',\n        'kendo.scheduler.agendaview',\n        'kendo.scheduler.monthview',\n        'kendo.scheduler.timelineview',\n        'kendo.dialog',\n        'kendo.pane',\n        'kendo.pdf',\n        'kendo.switch'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'scheduler',\n        name: 'Scheduler',\n        category: 'web',\n        description: 'The Scheduler is an event calendar.',\n        depends: [\n            'dropdownlist',\n            'editable',\n            'multiselect',\n            'window',\n            'datepicker',\n            'datetimepicker',\n            'scheduler.recurrence',\n            'scheduler.view'\n        ],\n        features: [\n            {\n                id: 'scheduler-dayview',\n                name: 'Scheduler Day View',\n                description: 'Scheduler Day View',\n                depends: ['scheduler.dayview']\n            },\n            {\n                id: 'scheduler-agendaview',\n                name: 'Scheduler Agenda View',\n                description: 'Scheduler Agenda View',\n                depends: ['scheduler.agendaview']\n            },\n            {\n                id: 'scheduler-monthview',\n                name: 'Scheduler Month View',\n                description: 'Scheduler Month View',\n                depends: ['scheduler.monthview']\n            },\n            {\n                id: 'scheduler-timelineview',\n                name: 'Scheduler Timeline View',\n                description: 'Scheduler Timeline View',\n                depends: ['scheduler.timelineview']\n            },\n            {\n                id: 'scheduler-mobile',\n                name: 'Scheduler adaptive rendering',\n                description: 'Support for adaptive rendering',\n                depends: [\n                    'dialog',\n                    'pane',\n                    'switch'\n                ]\n            },\n            {\n                id: 'scheduler-pdf-export',\n                name: 'PDF export',\n                description: 'Export the scheduler events as PDF',\n                depends: [\n                    'pdf',\n                    'drawing'\n                ]\n            },\n            {\n                id: 'scheduler-timezones',\n                name: 'Timezones',\n                description: 'Allow selecting timezones different than Etc/UTC',\n                depends: ['timezones']\n            }\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, date = kendo.date, MS_PER_DAY = date.MS_PER_DAY, getDate = date.getDate, getMilliseconds = kendo.date.getMilliseconds, recurrence = kendo.recurrence, keys = $.extend({ F10: 121 }, kendo.keys), ui = kendo.ui, Widget = ui.Widget, DataBoundWidget = ui.DataBoundWidget, STRING = 'string', Popup = ui.Popup, Calendar = ui.Calendar, DataSource = kendo.data.DataSource, isPlainObject = $.isPlainObject, extend = $.extend, proxy = $.proxy, toString = Object.prototype.toString, isArray = $.isArray, NS = '.kendoScheduler', CLICK = 'click', MOUSEDOWN = 'mousedown', TOUCHSTART = kendo.support.pointers ? 'pointerdown' : 'touchstart', TOUCHMOVE = kendo.support.pointers ? 'pointermove' : 'touchmove', TOUCHEND = kendo.support.pointers ? 'pointerup' : 'touchend', MOUSEMOVE = kendo.support.mousemove, CHANGE = 'change', PROGRESS = 'progress', ERROR = 'error', CANCEL = 'cancel', REMOVE = 'remove', RESET = 'resetSeries', SAVE = 'save', ADD = 'add', EDIT = 'edit', FOCUSEDSTATE = 'k-state-focused', EXPANDEDSTATE = 'k-state-expanded', VIEWSSELECTOR = '.k-scheduler-views', INVERSECOLORCLASS = 'k-event-inverse', valueStartEndBoundRegex = /(?:value:start|value:end)(?:,|$)/, TODAY = getDate(new Date()), EXCEPTION_SEPARATOR = ',', OLD_EXCEPTION_SEPARATOR_REGEXP = /\\;/g, RECURRENCE_EXCEPTION = 'recurrenceException', DELETECONFIRM = 'Are you sure you want to delete this event?', DELETERECURRING = 'Do you want to delete only this event occurrence or the whole series?', EDITRECURRING = 'Do you want to edit only this event occurrence or the whole series?', DELETERECURRINGCONFIRM = 'Are you sure you want to delete this event occurrence?', RESETSERIESCONFIRM = 'Are you sure you want to reset the whole series?', DELETESERIESCONFIRM = 'Are you sure you want to delete the whole series?', COMMANDBUTTONTMPL = '<a class=\"k-button #=className#\" #=attr# href=\"\\\\#\">#=text#</a>', VIEWBUTTONTEMPLATE = kendo.template('<li class=\"k-current-view\" data-#=ns#name=\"#=view#\"><a role=\"button\" href=\"\\\\#\" class=\"k-link\">${views[view].title}</a></li>'), TOOLBARTEMPLATE = kendo.template('<div class=\"k-floatwrap k-header k-scheduler-toolbar\">' + '# if (pdf) { #' + '<ul class=\"k-reset k-scheduler-tools\">' + '<li><a role=\"button\" href=\"\\\\#\" class=\"k-button k-pdf\"><span class=\"k-icon k-i-file-pdf\"></span>${messages.pdf}</a></li>' + '</ul>' + '# } #' + '<ul class=\"k-reset k-scheduler-navigation\">' + '<li class=\"k-state-default k-header k-nav-today\"><a role=\"button\" href=\"\\\\#\" class=\"k-link\" title=\"${messages.today}\">${messages.today}</a></li>' + '<li class=\"k-state-default k-header k-nav-prev\"><a role=\"button\" href=\"\\\\#\" class=\"k-link\" title=\"${messages.previous}\" aria-label=\"${messages.previous}\"><span class=\"k-icon k-i-arrow-60-left\" style=\"pointer-events: none\"></span></a></li>' + '<li class=\"k-state-default k-header k-nav-next\"><a role=\"button\" href=\"\\\\#\" class=\"k-link\" title=\"${messages.next}\" aria-label=\"${messages.next}\"><span class=\"k-icon k-i-arrow-60-right\" style=\"pointer-events: none\"></span></a></li>' + '<li class=\"k-state-default k-nav-current\">' + '<a role=\"button\" href=\"\\\\#\" class=\"k-link\">' + '<span class=\"k-icon k-i-calendar\"></span>' + '<span class=\"k-sm-date-format\" data-#=ns#bind=\"text: formattedShortDate\"></span>' + '<span class=\"k-lg-date-format\" data-#=ns#bind=\"text: formattedDate\"></span>' + '</a>' + '</li>' + '</ul>' + '#if(viewsCount === 1){#' + '<a role=\"button\" data-#=ns#name=\"#=view#\" href=\"\\\\#\" class=\"k-link k-scheduler-refresh\">' + '<span class=\"k-icon k-i-reload\"></span>' + '</a>' + '#}else{#' + '<ul class=\"k-reset k-header k-scheduler-views\">' + '#for(var view in views){#' + '<li class=\"k-state-default k-view-#= view.toLowerCase() #\" data-#=ns#name=\"#=view#\"><a role=\"button\" href=\"\\\\#\" class=\"k-link\">${views[view].title}</a></li>' + '#}#' + '</ul>' + '#}#' + '</div>'), MOBILETOOLBARTEMPLATE = kendo.template('<div class=\"k-header k-scheduler-toolbar\">' + '<ul class=\"k-reset k-scheduler-tools\">' + '# if (pdf) { #' + '<li><a role=\"button\" href=\"\\\\#\" class=\"k-button k-pdf\"><span class=\"k-icon k-i-file-pdf\"></span></a></li>' + '# } #' + '<li><a role=\"button\" href=\"\\\\#\" class=\"k-button k-nav-calendar\"><span class=\"k-icon k-i-calendar\"></span></a></li>' + '# if (editable) { #' + '<li><a role=\"button\" href=\"\\\\#\" class=\"k-button k-create-event\"><span class=\"k-icon k-i-plus\"></span></a></li>' + '# } #' + '</ul>' + '#if(viewsCount === 1){#' + '<a role=\"button\" data-#=ns#name=\"#=view#\" href=\"\\\\#\" class=\"k-link k-scheduler-refresh\">' + '<span class=\"k-icon k-i-reload\"></span>' + '</a>' + '#}else{#' + '<select class=\"k-scheduler-mobile-views\">' + '#for(var view in views){#' + '<option class=\"k-state-default k-view-#= view.toLowerCase() #\" value=\"#=view#\">${views[view].title}</option>' + '#}#' + '</select>' + '#}#' + '</div>' + '<div class=\"k-header k-scheduler-toolbar\">' + '<ul class=\"k-reset k-header k-scheduler-navigation\">' + '<li class=\"k-state-default k-nav-prev\"><a role=\"button\" href=\"\\\\#\" class=\"k-link\"><span class=\"k-icon k-i-arrow-chevron-left\"></span></a></li>' + '<li class=\"k-state-default k-nav-current\">' + '<span class=\"k-m-date-format\" data-#=ns#bind=\"text: formattedMobileDate\"></span>' + '<span class=\"k-y-date-format\" data-#=ns#bind=\"text: formattedYear\"></span>' + '</li>' + '<li class=\"k-state-default k-nav-next\"><a role=\"button\" href=\"\\\\#\" class=\"k-link\"><span class=\"k-icon k-i-arrow-chevron-right\"></span></a></li>' + '</ul>' + '</div>'), MOBILEDATERANGEEDITOR = function (container, options) {\n                var attr = {\n                    name: options.field,\n                    title: options.title\n                };\n                var isAllDay = options.model.isAllDay;\n                var dateTimeValidate = kendo.attr('validate') + '=\\'' + !isAllDay + '\\'';\n                var dateValidate = kendo.attr('validate') + '=\\'' + isAllDay + '\\'';\n                appendTimezoneAttr(attr, options);\n                appendValidDateValidator(attr, options);\n                appendDateCompareValidator(attr, options);\n                $('<input type=\"datetime-local\" required ' + kendo.attr('type') + '=\"datetime-local\" ' + kendo.attr('bind') + '=\"value:' + options.field + ', invisible:isAllDay\" ' + dateTimeValidate + '/>').attr(attr).appendTo(container);\n                $('<input type=\"date\" required ' + kendo.attr('type') + '=\"date\" ' + kendo.attr('bind') + '=\"value:' + options.field + ',visible:isAllDay\" ' + dateValidate + '/>').attr(attr).appendTo(container);\n                $('<span ' + kendo.attr('for') + '=\"' + options.field + '\" class=\"k-invalid-msg\"/>').hide().appendTo(container);\n            }, DATERANGEEDITOR = function (container, options) {\n                var attr = {\n                        name: options.field,\n                        title: options.title\n                    }, isAllDay = options.model.isAllDay, dateTimeValidate = kendo.attr('validate') + '=\\'' + !isAllDay + '\\' ', dateValidate = kendo.attr('validate') + '=\\'' + isAllDay + '\\' ';\n                appendTimezoneAttr(attr, options);\n                appendValidDateValidator(attr, options);\n                appendDateCompareValidator(attr, options);\n                $('<input type=\"text\" required ' + kendo.attr('type') + '=\"date\"' + ' ' + kendo.attr('role') + '=\"datetimepicker\" ' + kendo.attr('bind') + '=\"value:' + options.field + ',invisible:isAllDay\" ' + dateTimeValidate + '/>').attr(attr).appendTo(container);\n                $('<input type=\"text\" required ' + kendo.attr('type') + '=\"date\"' + ' ' + kendo.attr('role') + '=\"datepicker\" ' + kendo.attr('bind') + '=\"value:' + options.field + ',visible:isAllDay\" ' + dateValidate + '/>').attr(attr).appendTo(container);\n                $('<span ' + kendo.attr('bind') + '=\"text: ' + options.field + 'Timezone\"></span>').appendTo(container);\n                if (options.field === 'end') {\n                    $('<span ' + kendo.attr('bind') + '=\"text: startTimezone, invisible: endTimezone\"></span>').appendTo(container);\n                }\n                $('<span ' + kendo.attr('for') + '=\"' + options.field + '\" class=\"k-invalid-msg\"/>').hide().appendTo(container);\n            }, RECURRENCEEDITOR = function (container, options) {\n                $('<div ' + kendo.attr('bind') + '=\"value:' + options.field + '\" />').attr({ name: options.field }).appendTo(container).kendoRecurrenceEditor({\n                    start: options.model.start,\n                    timezone: options.timezone,\n                    messages: options.messages\n                });\n            }, MOBILERECURRENCEEDITOR = function (container, options) {\n                $('<div ' + kendo.attr('bind') + '=\"value:' + options.field + '\" />').attr({ name: options.field }).appendTo(container).kendoMobileRecurrenceEditor({\n                    start: options.model.start,\n                    timezone: options.timezone,\n                    messages: options.messages,\n                    pane: options.pane,\n                    value: options.model[options.field]\n                });\n            }, MOBILEISALLDAYEDITOR = function (container, options) {\n                $('<input type=\"checkbox\" data-role=\"switch\"' + kendo.attr('bind') + '=\"value:' + options.field + '\" />').appendTo(container);\n            }, MOBILETIMEZONEPOPUP = function (container, options) {\n                var text = timezoneButtonText(options.model, options.messages.noTimezone);\n                $('<span class=\"k-timezone-label\"></span>').text(text).appendTo(container);\n                $('<span class=\"k-icon k-i-arrow-chevron-right\"></span>').appendTo(container);\n                container.closest('li.k-item label').click(options.click);\n            }, TIMEZONEPOPUP = function (container, options) {\n                $('<a href=\"#\" class=\"k-button\" data-bind=\"invisible:isAllDay\">' + options.messages.timezoneEditorButton + '</a>').click(options.click).appendTo(container);\n            }, MOBILETIMEZONEEDITOR = function (container, options) {\n                $('<div class=\"k-mobiletimezoneeditor\" ' + kendo.attr('bind') + '=\"value:' + options.field + '\" />').attr({ name: options.field }).appendTo(container).kendoMobileTimezoneEditor({ optionLabel: options.noTimezone });\n            }, TIMEZONEEDITOR = function (container, options) {\n                var visible = options.visible || options.visible === undefined;\n                $('<div ' + kendo.attr('bind') + '=\"value:' + options.field + '\" />').attr({ name: options.field }).toggle(visible).appendTo(container).kendoTimezoneEditor({\n                    optionLabel: options.noTimezone,\n                    title: options.title\n                });\n            };\n        function timezoneButtonText(model, message) {\n            message = message || '';\n            if (model.startTimezone) {\n                message = model.startTimezone;\n                if (model.endTimezone) {\n                    message += ' | ' + model.endTimezone;\n                }\n            }\n            return message;\n        }\n        function appendTimezoneAttr(attrs, options) {\n            var timezone = options.timezone;\n            if (timezone) {\n                attrs[kendo.attr('timezone')] = timezone;\n            }\n        }\n        function appendValidDateValidator(attrs, options) {\n            var validationRules = options.model.fields[options.field].validation;\n            if (validationRules) {\n                var validDateRule = validationRules.validDateValidator;\n                if (validDateRule && isPlainObject(validDateRule) && validDateRule.message) {\n                    attrs[kendo.attr('validDate-msg')] = validDateRule.message;\n                }\n            }\n        }\n        function appendDateCompareValidator(attrs, options) {\n            var validationRules = options.model.fields[options.field].validation;\n            if (validationRules) {\n                var dateCompareRule = validationRules.dateCompare;\n                if (dateCompareRule && isPlainObject(dateCompareRule) && dateCompareRule.message) {\n                    attrs[kendo.attr('dateCompare-msg')] = dateCompareRule.message;\n                }\n            }\n        }\n        function wrapDataAccess(originalFunction, timezone) {\n            return function (data) {\n                data = originalFunction(data);\n                convertData(data, 'apply', timezone);\n                return data || [];\n            };\n        }\n        function wrapDataSerialization(originalFunction, timezone) {\n            return function (data) {\n                if (data) {\n                    if (toString.call(data) !== '[object Array]' && !(data instanceof kendo.data.ObservableArray)) {\n                        data = [data];\n                    }\n                }\n                convertData(data, 'remove', timezone, true);\n                data = originalFunction(data);\n                return data || [];\n            };\n        }\n        function convertData(data, method, timezone, removeUid) {\n            var event, idx, length;\n            data = data || [];\n            for (idx = 0, length = data.length; idx < length; idx++) {\n                event = data[idx];\n                if (removeUid) {\n                    if (event.startTimezone || event.endTimezone) {\n                        if (timezone) {\n                            event.start = kendo.timezone.convert(event.start, event.startTimezone || event.endTimezone, timezone);\n                            event.end = kendo.timezone.convert(event.end, event.endTimezone || event.startTimezone, timezone);\n                            event.start = kendo.timezone[method](event.start, timezone);\n                            event.end = kendo.timezone[method](event.end, timezone);\n                        } else {\n                            event.start = kendo.timezone[method](event.start, event.startTimezone || event.endTimezone);\n                            event.end = kendo.timezone[method](event.end, event.endTimezone || event.startTimezone);\n                        }\n                    } else if (timezone) {\n                        event.start = kendo.timezone[method](event.start, timezone);\n                        event.end = kendo.timezone[method](event.end, timezone);\n                    }\n                } else {\n                    if (event.startTimezone || event.endTimezone) {\n                        event.start = kendo.timezone[method](event.start, event.startTimezone || event.endTimezone);\n                        event.end = kendo.timezone[method](event.end, event.endTimezone || event.startTimezone);\n                        if (timezone) {\n                            event.start = kendo.timezone.convert(event.start, event.startTimezone || event.endTimezone, timezone);\n                            event.end = kendo.timezone.convert(event.end, event.endTimezone || event.startTimezone, timezone);\n                        }\n                    } else if (timezone) {\n                        event.start = kendo.timezone[method](event.start, timezone);\n                        event.end = kendo.timezone[method](event.end, timezone);\n                    }\n                }\n                if (removeUid) {\n                    delete event.uid;\n                }\n            }\n            return data;\n        }\n        function getOccurrenceByUid(data, uid) {\n            var length = data.length, idx = 0, event;\n            for (; idx < length; idx++) {\n                event = data[idx];\n                if (event.uid === uid) {\n                    return event;\n                }\n            }\n        }\n        var SchedulerDataReader = kendo.Class.extend({\n            init: function (schema, reader) {\n                var timezone = schema.timezone;\n                this.reader = reader;\n                if (reader.model) {\n                    this.model = reader.model;\n                }\n                this.timezone = timezone;\n                this.data = wrapDataAccess($.proxy(this.data, this), timezone);\n                this.serialize = wrapDataSerialization($.proxy(this.serialize, this), timezone);\n            },\n            errors: function (data) {\n                return this.reader.errors(data);\n            },\n            parse: function (data) {\n                return this.reader.parse(data);\n            },\n            data: function (data) {\n                return this.reader.data(data);\n            },\n            total: function (data) {\n                return this.reader.total(data);\n            },\n            groups: function (data) {\n                return this.reader.groups(data);\n            },\n            aggregates: function (data) {\n                return this.reader.aggregates(data);\n            },\n            serialize: function (data) {\n                return this.reader.serialize(data);\n            }\n        });\n        function applyZone(date, fromZone, toZone) {\n            if (toZone) {\n                date = kendo.timezone.convert(date, fromZone, toZone);\n            } else {\n                date = kendo.timezone.remove(date, fromZone);\n            }\n            return date;\n        }\n        function validDateValidator(input) {\n            if (input.filter('[name=start]').length && input.filter('[title=Start]').length || input.filter('[name=end]').length && input.filter('[title=End]').length) {\n                var date;\n                var picker = kendo.widgetInstance(input, kendo.ui);\n                if (picker) {\n                    date = kendo.parseDate(input.val(), picker.options.format);\n                    return !!date && picker.value();\n                } else {\n                    date = kendo.parseDate(input.val());\n                    return !!date;\n                }\n            }\n            return true;\n        }\n        function dateCompareValidator(input) {\n            if (input.filter('[name=end]').length) {\n                var container = input.closest('.k-scheduler-edit-form');\n                var startInput = container.find('[name=start]:visible');\n                var endInput = container.find('[name=end]:visible');\n                if (endInput[0] && startInput[0]) {\n                    var start, end;\n                    var startPicker = kendo.widgetInstance(startInput, kendo.ui);\n                    var endPicker = kendo.widgetInstance(endInput, kendo.ui);\n                    var editable = container.data('kendoEditable');\n                    var model = editable ? editable.options.model : null;\n                    if (startPicker && endPicker) {\n                        start = startPicker.value();\n                        end = endPicker.value();\n                    } else {\n                        start = kendo.parseDate(startInput.val());\n                        end = kendo.parseDate(endInput.val());\n                    }\n                    if (start && end) {\n                        if (model) {\n                            var timezone = startInput.attr(kendo.attr('timezone'));\n                            var startTimezone = model.startTimezone;\n                            var endTimezone = model.endTimezone;\n                            startTimezone = startTimezone || endTimezone;\n                            endTimezone = endTimezone || startTimezone;\n                            if (startTimezone) {\n                                start = applyZone(start, startTimezone, timezone);\n                                end = applyZone(end, endTimezone, timezone);\n                            }\n                        }\n                        return start <= end;\n                    }\n                }\n            }\n            return true;\n        }\n        var SchedulerEvent = kendo.data.Model.define({\n            init: function (value) {\n                var that = this;\n                kendo.data.Model.fn.init.call(that, value);\n                that._defaultId = that.defaults[that.idField];\n            },\n            _time: function (field) {\n                var date = this[field];\n                var fieldTime = '_' + field + 'Time';\n                if (this[fieldTime]) {\n                    return this[fieldTime] - kendo.date.toUtcTime(kendo.date.getDate(date));\n                }\n                return getMilliseconds(date);\n            },\n            _date: function (field) {\n                var fieldTime = '_' + field + 'Time';\n                if (this[fieldTime]) {\n                    return this[fieldTime] - this._time(field);\n                }\n                return kendo.date.getDate(this[field]);\n            },\n            clone: function (options, updateUid) {\n                var uid = this.uid, event = new this.constructor($.extend({}, this.toJSON(), options));\n                if (!updateUid) {\n                    event.uid = uid;\n                }\n                return event;\n            },\n            duration: function () {\n                var end = this.end;\n                var start = this.start;\n                var offset = (end.getTimezoneOffset() - start.getTimezoneOffset()) * kendo.date.MS_PER_MINUTE;\n                return end - start - offset;\n            },\n            expand: function (start, end, zone) {\n                return recurrence ? recurrence.expand(this, start, end, zone) : [this];\n            },\n            update: function (eventInfo) {\n                for (var field in eventInfo) {\n                    this.set(field, eventInfo[field]);\n                }\n                if (this._startTime) {\n                    this.set('_startTime', kendo.date.toUtcTime(this.start));\n                }\n                if (this._endTime) {\n                    this.set('_endTime', kendo.date.toUtcTime(this.end));\n                }\n            },\n            isMultiDay: function () {\n                return this.isAllDay || this.duration() >= kendo.date.MS_PER_DAY;\n            },\n            isException: function () {\n                return !this.isNew() && this.recurrenceId;\n            },\n            isOccurrence: function () {\n                return this.isNew() && this.recurrenceId;\n            },\n            isRecurring: function () {\n                return !!(this.recurrenceRule || this.recurrenceId);\n            },\n            isRecurrenceHead: function () {\n                return !!(this.id && this.recurrenceRule);\n            },\n            toOccurrence: function (options) {\n                options = $.extend(options, {\n                    recurrenceException: null,\n                    recurrenceRule: null,\n                    recurrenceId: this.id || this.recurrenceId\n                });\n                options[this.idField] = this.defaults[this.idField];\n                return this.clone(options, true);\n            },\n            toJSON: function () {\n                var obj = kendo.data.Model.fn.toJSON.call(this);\n                obj.uid = this.uid;\n                delete obj._startTime;\n                delete obj._endTime;\n                return obj;\n            },\n            shouldSerialize: function (field) {\n                return kendo.data.Model.fn.shouldSerialize.call(this, field) && field !== '_defaultId';\n            },\n            set: function (key, value) {\n                var isAllDay = this.isAllDay || false;\n                kendo.data.Model.fn.set.call(this, key, value);\n                if (key == 'isAllDay' && value != isAllDay) {\n                    var start = kendo.date.getDate(this.start);\n                    var end = new Date(this.end);\n                    var milliseconds = kendo.date.getMilliseconds(end);\n                    if (milliseconds === 0 && value) {\n                        milliseconds = MS_PER_DAY;\n                    }\n                    this.set('start', start);\n                    if (value === true) {\n                        kendo.date.setTime(end, -milliseconds);\n                        if (end < start) {\n                            end = start;\n                        }\n                    } else {\n                        kendo.date.setTime(end, MS_PER_DAY - milliseconds);\n                    }\n                    this.set('end', end);\n                }\n            },\n            id: 'id',\n            fields: {\n                id: { type: 'number' },\n                title: {\n                    defaultValue: '',\n                    type: 'string'\n                },\n                start: {\n                    type: 'date',\n                    validation: {\n                        required: true,\n                        validDate: { value: validDateValidator }\n                    }\n                },\n                startTimezone: { type: 'string' },\n                end: {\n                    type: 'date',\n                    validation: {\n                        required: true,\n                        validDate: { value: validDateValidator },\n                        dateCompare: { value: dateCompareValidator }\n                    }\n                },\n                endTimezone: { type: 'string' },\n                recurrenceRule: {\n                    defaultValue: '',\n                    type: 'string'\n                },\n                recurrenceException: {\n                    defaultValue: '',\n                    type: 'string'\n                },\n                isAllDay: {\n                    type: 'boolean',\n                    defaultValue: false\n                },\n                description: { type: 'string' }\n            }\n        });\n        var SchedulerDataSource = DataSource.extend({\n            init: function (options) {\n                DataSource.fn.init.call(this, extend(true, {}, {\n                    schema: {\n                        modelBase: SchedulerEvent,\n                        model: SchedulerEvent\n                    }\n                }, options));\n                this.reader = new SchedulerDataReader(this.options.schema, this.reader);\n            },\n            expand: function (start, end) {\n                var data = this.view(), filter = {}, endOffset;\n                if (start && end) {\n                    endOffset = end.getTimezoneOffset();\n                    end = new Date(end.getTime() + MS_PER_DAY - 1);\n                    if (end.getTimezoneOffset() !== endOffset) {\n                        end = kendo.timezone.apply(end, endOffset);\n                    }\n                    filter = {\n                        logic: 'or',\n                        filters: [\n                            {\n                                logic: 'and',\n                                filters: [\n                                    {\n                                        field: 'start',\n                                        operator: 'gte',\n                                        value: start\n                                    },\n                                    {\n                                        field: 'end',\n                                        operator: 'gte',\n                                        value: start\n                                    },\n                                    {\n                                        field: 'start',\n                                        operator: 'lte',\n                                        value: end\n                                    }\n                                ]\n                            },\n                            {\n                                logic: 'and',\n                                filters: [\n                                    {\n                                        field: 'start',\n                                        operator: 'lte',\n                                        value: new Date(start.getTime() + MS_PER_DAY - 1)\n                                    },\n                                    {\n                                        field: 'end',\n                                        operator: 'gte',\n                                        value: start\n                                    }\n                                ]\n                            }\n                        ]\n                    };\n                    data = new kendo.data.Query(expandAll(data, start, end, this.reader.timezone)).filter(filter).toArray();\n                }\n                return data;\n            },\n            cancelChanges: function (model) {\n                if (model && model.isOccurrence()) {\n                    this._removeExceptionDate(model);\n                }\n                DataSource.fn.cancelChanges.call(this, model);\n            },\n            insert: function (index, model) {\n                if (!model) {\n                    return;\n                }\n                if (!(model instanceof SchedulerEvent)) {\n                    var eventInfo = model;\n                    model = this._createNewModel();\n                    model.accept(eventInfo);\n                }\n                if (!this._pushCreated && model.isRecurrenceHead() || model.recurrenceId) {\n                    model = model.recurrenceId ? model : model.toOccurrence();\n                    this._addExceptionDate(model);\n                }\n                return DataSource.fn.insert.call(this, index, model);\n            },\n            pushCreate: function (items) {\n                this._pushCreated = true;\n                DataSource.fn.pushCreate.call(this, items);\n                this._pushCreated = false;\n            },\n            remove: function (model) {\n                if (model.isRecurrenceHead()) {\n                    this._removeExceptions(model);\n                } else if (model.isRecurring()) {\n                    this._addExceptionDate(model);\n                }\n                return DataSource.fn.remove.call(this, model);\n            },\n            _removeExceptions: function (model) {\n                var data = this.data().slice(0), item = data.shift(), id = model.id;\n                while (item) {\n                    if (item.recurrenceId === id) {\n                        DataSource.fn.remove.call(this, item);\n                    }\n                    item = data.shift();\n                }\n                model.set(RECURRENCE_EXCEPTION, '');\n            },\n            _removeExceptionDate: function (model) {\n                if (model.recurrenceId) {\n                    var head = this.get(model.recurrenceId);\n                    if (head) {\n                        var start = model.defaults.start;\n                        var replaceRegExp = new RegExp('(\\\\' + EXCEPTION_SEPARATOR + '?)' + recurrence.toExceptionString(start, this.reader.timezone));\n                        var recurrenceException = (head.recurrenceException || '').replace(OLD_EXCEPTION_SEPARATOR_REGEXP, EXCEPTION_SEPARATOR).replace(/\\,$/, '');\n                        if (replaceRegExp.test(recurrenceException)) {\n                            head.set(RECURRENCE_EXCEPTION, recurrenceException.replace(replaceRegExp, ''));\n                        } else {\n                            start = model.start;\n                            replaceRegExp = new RegExp('(\\\\' + EXCEPTION_SEPARATOR + '?)' + recurrence.toExceptionString(start, this.reader.timezone));\n                            head.set(RECURRENCE_EXCEPTION, recurrenceException.replace(replaceRegExp, ''));\n                        }\n                    }\n                }\n            },\n            _addExceptionDate: function (model) {\n                var start = model.start;\n                var zone = this.reader.timezone;\n                var head = this.get(model.recurrenceId);\n                var recurrenceException = (head.recurrenceException || '').replace(OLD_EXCEPTION_SEPARATOR_REGEXP, EXCEPTION_SEPARATOR).replace(/\\,$/, '');\n                if (!recurrence.isException(recurrenceException, start, zone)) {\n                    var newException = recurrence.toExceptionString(start, zone);\n                    model.defaults.start = start;\n                    head.set(RECURRENCE_EXCEPTION, recurrenceException + (recurrenceException && newException ? EXCEPTION_SEPARATOR : '') + newException);\n                }\n            }\n        });\n        function expandAll(events, start, end, zone) {\n            var length = events.length, data = [], idx = 0;\n            for (; idx < length; idx++) {\n                data = data.concat(events[idx].expand(start, end, zone));\n            }\n            return data;\n        }\n        SchedulerDataSource.create = function (options) {\n            if (isArray(options) || options instanceof kendo.data.ObservableArray) {\n                options = { data: options };\n            }\n            var dataSource = options || {}, data = dataSource.data;\n            dataSource.data = data;\n            if (!(dataSource instanceof SchedulerDataSource) && dataSource instanceof kendo.data.DataSource) {\n                throw new Error('Incorrect DataSource type. Only SchedulerDataSource instances are supported');\n            }\n            return dataSource instanceof SchedulerDataSource ? dataSource : new SchedulerDataSource(dataSource);\n        };\n        extend(true, kendo.data, {\n            SchedulerDataSource: SchedulerDataSource,\n            SchedulerDataReader: SchedulerDataReader,\n            SchedulerEvent: SchedulerEvent\n        });\n        var defaultCommands = {\n            update: {\n                text: 'Save',\n                className: 'k-primary k-scheduler-update'\n            },\n            canceledit: {\n                text: 'Cancel',\n                className: 'k-scheduler-cancel'\n            },\n            destroy: {\n                text: 'Delete',\n                imageClass: 'k-i-close',\n                className: 'k-primary k-scheduler-delete',\n                iconClass: 'k-icon'\n            }\n        };\n        function trimOptions(options) {\n            delete options.name;\n            delete options.prefix;\n            delete options.remove;\n            delete options.edit;\n            delete options.add;\n            delete options.navigate;\n            return options;\n        }\n        function createValidationAttributes(model, field) {\n            var modelField = (model.fields || model)[field];\n            var specialRules = [\n                'url',\n                'email',\n                'number',\n                'date',\n                'boolean'\n            ];\n            var validation = modelField ? modelField.validation : {};\n            var datatype = kendo.attr('type');\n            var inArray = $.inArray;\n            var ruleName;\n            var rule;\n            var attr = {};\n            for (ruleName in validation) {\n                rule = validation[ruleName];\n                if (inArray(ruleName, specialRules) >= 0) {\n                    attr[datatype] = ruleName;\n                } else if (!kendo.isFunction(rule)) {\n                    attr[ruleName] = isPlainObject(rule) ? rule.value || ruleName : rule;\n                }\n                attr[kendo.attr(ruleName + '-msg')] = rule.message;\n            }\n            return attr;\n        }\n        function dropDownResourceEditor(resource, model) {\n            var attr = createValidationAttributes(model, resource.field);\n            return function (container) {\n                $(kendo.format('<select data-{0}bind=\"value:{1}\" title=\"' + model.title + '\">', kendo.ns, resource.field)).appendTo(container).attr(attr).kendoDropDownList({\n                    dataTextField: resource.dataTextField,\n                    dataValueField: resource.dataValueField,\n                    dataSource: resource.dataSource,\n                    valuePrimitive: resource.valuePrimitive,\n                    optionLabel: 'None',\n                    template: kendo.format('<span class=\"k-scheduler-mark\" style=\"background-color:#= data.{0}?{0}:\"none\" #\"></span>#={1}#', resource.dataColorField, resource.dataTextField)\n                });\n            };\n        }\n        function dropDownResourceEditorMobile(resource, model) {\n            var attr = createValidationAttributes(model, resource.field);\n            return function (container) {\n                var options = '';\n                var view = resource.dataSource.view();\n                for (var idx = 0, length = view.length; idx < length; idx++) {\n                    options += kendo.format('<option value=\"{0}\">{1}</option>', kendo.getter(resource.dataValueField)(view[idx]), kendo.getter(resource.dataTextField)(view[idx]));\n                }\n                $(kendo.format('<select data-{0}bind=\"value:{1}\">{2}</select>', kendo.ns, resource.field, options, resource.valuePrimitive)).appendTo(container).attr(attr);\n            };\n        }\n        function descriptionEditor(options) {\n            var attr = createValidationAttributes(options.model, options.field);\n            return function (container, model) {\n                $('<textarea name=\"description\" class=\"k-textbox\" title=\"' + model.title + '\"/>').attr(attr).appendTo(container);\n            };\n        }\n        function multiSelectResourceEditor(resource, model) {\n            var attr = createValidationAttributes(model, resource.field);\n            return function (container) {\n                $(kendo.format('<select data-{0}bind=\"value:{1}\">', kendo.ns, resource.field)).appendTo(container).attr(attr).kendoMultiSelect({\n                    dataTextField: resource.dataTextField,\n                    dataValueField: resource.dataValueField,\n                    dataSource: resource.dataSource,\n                    valuePrimitive: resource.valuePrimitive,\n                    itemTemplate: kendo.format('<span class=\"k-scheduler-mark\" style=\"background-color:#= data.{0}?{0}:\"none\" #\"></span>#={1}#', resource.dataColorField, resource.dataTextField),\n                    tagTemplate: kendo.format('<span class=\"k-scheduler-mark\" style=\"background-color:#= data.{0}?{0}:\"none\" #\"></span>#={1}#', resource.dataColorField, resource.dataTextField)\n                });\n            };\n        }\n        function multiSelectResourceEditorMobile(resource, model) {\n            var attr = createValidationAttributes(model, resource.field);\n            return function (container) {\n                var options = '';\n                var view = resource.dataSource.view();\n                for (var idx = 0, length = view.length; idx < length; idx++) {\n                    options += kendo.format('<option value=\"{0}\">{1}</option>', kendo.getter(resource.dataValueField)(view[idx]), kendo.getter(resource.dataTextField)(view[idx]));\n                }\n                $(kendo.format('<select data-{0}bind=\"value:{1}\" multiple=\"multiple\" data-{0}value-primitive=\"{3}\">{2}</select>', kendo.ns, resource.field, options, resource.valuePrimitive)).appendTo(container).attr(attr);\n            };\n        }\n        function moveEventRange(event, distance) {\n            var duration = event.end.getTime() - event.start.getTime();\n            var start = new Date(event.start.getTime());\n            kendo.date.setTime(start, distance);\n            var end = new Date(start.getTime());\n            kendo.date.setTime(end, duration, true);\n            return {\n                start: start,\n                end: end\n            };\n        }\n        var editors = {\n            mobile: {\n                dateRange: MOBILEDATERANGEEDITOR,\n                timezonePopUp: MOBILETIMEZONEPOPUP,\n                timezone: MOBILETIMEZONEEDITOR,\n                recurrence: MOBILERECURRENCEEDITOR,\n                description: descriptionEditor,\n                multipleResources: multiSelectResourceEditorMobile,\n                resources: dropDownResourceEditorMobile,\n                isAllDay: MOBILEISALLDAYEDITOR\n            },\n            desktop: {\n                dateRange: DATERANGEEDITOR,\n                timezonePopUp: TIMEZONEPOPUP,\n                timezone: TIMEZONEEDITOR,\n                recurrence: RECURRENCEEDITOR,\n                description: descriptionEditor,\n                multipleResources: multiSelectResourceEditor,\n                resources: dropDownResourceEditor\n            }\n        };\n        var Editor = kendo.Observable.extend({\n            init: function (element, options) {\n                kendo.Observable.fn.init.call(this);\n                this.element = element;\n                this.options = extend(true, {}, this.options, options);\n                this.createButton = this.options.createButton;\n                this.toggleDateValidationHandler = proxy(this._toggleDateValidation, this);\n            },\n            _toggleDateValidation: function (e) {\n                if (e.field == 'isAllDay') {\n                    var container = this.container, isAllDay = this.editable.options.model.isAllDay, bindAttribute = kendo.attr('bind'), element, isDateTimeInput, shouldValidate;\n                    container.find('[' + bindAttribute + '*=end],[' + bindAttribute + '*=start]').each(function () {\n                        element = $(this);\n                        if (valueStartEndBoundRegex.test(element.attr(bindAttribute))) {\n                            isDateTimeInput = element.is('[' + kendo.attr('role') + '=datetimepicker],[type*=datetime]');\n                            shouldValidate = isAllDay !== isDateTimeInput;\n                            element.attr(kendo.attr('validate'), shouldValidate);\n                        }\n                    });\n                }\n            },\n            fields: function (editors, model) {\n                var that = this;\n                var messages = that.options.messages;\n                var timezone = that.options.timezone;\n                var click = function (e) {\n                    e.preventDefault();\n                    that._initTimezoneEditor(model, this);\n                };\n                var fields = [\n                    {\n                        field: 'title',\n                        title: messages.editor.title\n                    },\n                    {\n                        field: 'start',\n                        title: messages.editor.start,\n                        editor: editors.dateRange,\n                        timezone: timezone\n                    },\n                    {\n                        field: 'end',\n                        title: messages.editor.end,\n                        editor: editors.dateRange,\n                        timezone: timezone\n                    },\n                    {\n                        field: 'isAllDay',\n                        title: messages.editor.allDayEvent,\n                        editor: editors.isAllDay\n                    }\n                ];\n                if (kendo.timezone.windows_zones) {\n                    fields.push({\n                        field: 'timezone',\n                        title: messages.editor.timezone,\n                        editor: editors.timezonePopUp,\n                        click: click,\n                        messages: messages.editor,\n                        model: model\n                    });\n                    fields.push({\n                        field: 'startTimezone',\n                        title: messages.editor.startTimezone,\n                        editor: editors.timezone,\n                        noTimezone: messages.editor.noTimezone\n                    });\n                    fields.push({\n                        field: 'endTimezone',\n                        title: messages.editor.endTimezone,\n                        editor: editors.timezone,\n                        noTimezone: messages.editor.noTimezone\n                    });\n                }\n                if (!model.recurrenceId) {\n                    fields.push({\n                        field: 'recurrenceRule',\n                        title: messages.editor.repeat,\n                        editor: editors.recurrence,\n                        timezone: timezone,\n                        messages: messages.recurrenceEditor,\n                        pane: this.pane\n                    });\n                }\n                if ('description' in model) {\n                    fields.push({\n                        field: 'description',\n                        title: messages.editor.description,\n                        editor: editors.description({\n                            model: model,\n                            field: 'description'\n                        })\n                    });\n                }\n                for (var resourceIndex = 0; resourceIndex < this.options.resources.length; resourceIndex++) {\n                    var resource = this.options.resources[resourceIndex];\n                    fields.push({\n                        field: resource.field,\n                        title: resource.title,\n                        editor: resource.multiple ? editors.multipleResources(resource, model) : editors.resources(resource, model)\n                    });\n                }\n                return fields;\n            },\n            end: function () {\n                return this.editable.end();\n            },\n            _buildDesktopEditTemplate: function (model, fields, editableFields) {\n                var messages = this.options.messages;\n                var settings = extend({}, kendo.Template, this.options.templateSettings);\n                var paramName = settings.paramName;\n                var html = '';\n                for (var idx = 0, length = fields.length; idx < length; idx++) {\n                    var field = fields[idx];\n                    if (field.field === 'startTimezone') {\n                        html += '<div class=\"k-popup-edit-form k-scheduler-edit-form k-scheduler-timezones\" style=\"display:none\">';\n                        html += '<div class=\"k-edit-form-container\">';\n                        html += '<div class=\"k-edit-label\"></div>';\n                        html += '<div class=\"k-edit-field\"><label class=\"k-check\"><input class=\"k-timezone-toggle\" type=\"checkbox\" />' + messages.editor.separateTimezones + '</label></div>';\n                    }\n                    html += '<div class=\"k-edit-label\"><label for=\"' + field.field + '\">' + (field.title || field.field || '') + '</label></div>';\n                    if (!model.editable || model.editable(field.field)) {\n                        editableFields.push(field);\n                        html += '<div ' + kendo.attr('container-for') + '=\"' + field.field + '\" class=\"k-edit-field\"></div>';\n                    } else {\n                        var tmpl = '#:';\n                        if (field.field) {\n                            field = kendo.expr(field.field, paramName);\n                            tmpl += field + '==null?\\'\\':' + field;\n                        } else {\n                            tmpl += '\\'\\'';\n                        }\n                        tmpl += '#';\n                        tmpl = kendo.template(tmpl, settings);\n                        html += '<div class=\"k-edit-field\">' + tmpl(model) + '</div>';\n                    }\n                    if (field.field === 'endTimezone') {\n                        html += this._createEndTimezoneButton();\n                    }\n                }\n                return html;\n            },\n            _buildMobileEditTemplate: function (model, fields, editableFields) {\n                var messages = this.options.messages;\n                var settings = extend({}, kendo.Template, this.options.templateSettings);\n                var paramName = settings.paramName;\n                var html = '';\n                html += '<ul>';\n                for (var idx = 0, length = fields.length; idx < length; idx++) {\n                    var field = fields[idx];\n                    if (field.field === 'timezone' || field.field === 'recurrenceRule') {\n                        html += '</ul><ul>';\n                    }\n                    if (field.field === 'startTimezone') {\n                        html += '<div class=\"k-popup-edit-form k-scheduler-edit-form k-scheduler-timezones\" style=\"display:none\">';\n                        html += '<ul><li class=\"k-item\"><label class=\"k-label\">';\n                        html += '<span class=\"k-item-title\">' + messages.editor.separateTimezones + '</span>';\n                        html += '<input class=\"k-timezone-toggle\" data-role=\"switch\" type=\"checkbox\">';\n                        html += '</label></li>';\n                    }\n                    if (!model.editable || model.editable(field.field)) {\n                        html += '<li class=\"k-item\">';\n                        if (field.field === 'timezone') {\n                            html += '<label class=\"k-label\" data-bind=\"css: { k-state-disabled: isAllDay }\">';\n                        } else {\n                            html += '<label class=\"k-label\">';\n                        }\n                        html += '<span class=\"k-item-title\">' + (field.title || field.field || '') + '</span>';\n                        editableFields.push(field);\n                        html += '<div ' + kendo.attr('container-for') + '=\"' + field.field + '\"></div>';\n                    } else {\n                        var tmpl = '#:';\n                        html += '<li class=\"k-item\">';\n                        html += '<label class=\"k-label k-no-click\">';\n                        html += '<span class=\"k-item-title\">' + (field.title || field.field || '') + '</span>';\n                        if (field.field) {\n                            field = kendo.expr(field.field, paramName);\n                            tmpl += field + '==null?\\'\\':' + field;\n                        } else {\n                            tmpl += '\\'\\'';\n                        }\n                        tmpl += '#';\n                        tmpl = kendo.template(tmpl, settings);\n                        html += '<span class=\"k-no-editor\">' + tmpl(model) + '</span>';\n                    }\n                    html += '</label></li>';\n                    if (field.field === 'recurrenceRule') {\n                        html += '</ul><ul>';\n                    }\n                    if (field.field === 'endTimezone') {\n                        html += this._createEndTimezoneButton();\n                    }\n                }\n                html += '</ul>';\n                return html;\n            },\n            _buildEditTemplate: function (model, fields, editableFields, isMobile) {\n                var settings = extend({}, kendo.Template, this.options.templateSettings);\n                var template = this.options.editable.template;\n                var html = '';\n                if (template) {\n                    if (typeof template === STRING) {\n                        template = window.unescape(template);\n                    }\n                    html += kendo.template(template, settings)(model);\n                } else if (isMobile) {\n                    html += '<div data-role=\"content\">' + this._buildMobileEditTemplate(model, fields, editableFields) + '</div>';\n                } else {\n                    html += this._buildDesktopEditTemplate(model, fields, editableFields);\n                }\n                return html;\n            },\n            _createEndTimezoneButton: function () {\n                return '</ul></div>';\n            },\n            _revertTimezones: function (model) {\n                model.set('startTimezone', this._startTimezone);\n                model.set('endTimezone', this._endTimezone);\n                delete this._startTimezone;\n                delete this._endTimezone;\n            }\n        });\n        var MobileEditor = Editor.extend({\n            init: function () {\n                Editor.fn.init.apply(this, arguments);\n                this.pane = kendo.Pane.wrap(this.element, {\n                    viewEngine: {\n                        viewOptions: {\n                            renderOnInit: true,\n                            wrap: false,\n                            wrapInSections: true,\n                            detachOnHide: false,\n                            detachOnDestroy: false\n                        }\n                    }\n                });\n                this.pane.element.parent().css('height', this.options.height);\n                this.view = this.pane.view();\n            },\n            options: {\n                animations: {\n                    left: 'slide',\n                    right: 'slide:right'\n                }\n            },\n            destroy: function () {\n                this.close();\n                this.unbind();\n                this.pane.destroy();\n            },\n            _initTimezoneEditor: function (model) {\n                var that = this;\n                var pane = that.pane;\n                var messages = that.options.messages;\n                var timezoneView = that.timezoneView;\n                var container = timezoneView ? timezoneView.content.find('.k-scheduler-timezones') : that.container.find('.k-scheduler-timezones');\n                var kSwitch = container.find('input.k-timezone-toggle').data('kendoSwitch');\n                var endTimezoneRow = container.find('li.k-item:not(.k-zonepicker):last');\n                var startTimezoneChange = function (e) {\n                    if (e.field === 'startTimezone') {\n                        var value = model.startTimezone;\n                        kSwitch.enable(value);\n                        if (!value) {\n                            endTimezoneRow.hide();\n                            model.set('endTimezone', '');\n                            kSwitch.value(false);\n                        }\n                    }\n                };\n                that._startTimezone = model.startTimezone || '';\n                that._endTimezone = model.endTimezone || '';\n                if (!timezoneView) {\n                    var html = '<div data-role=\"view\" class=\"k-popup-edit-form k-scheduler-edit-form k-mobile-list\">' + '<div data-role=\"header\" class=\"k-header\">' + '<a href=\"\\\\#\" class=\"k-header-cancel k-scheduler-cancel k-link\" title=\"' + messages.cancel + '\"' + 'aria-label=\"' + messages.cancel + '\"><span class=\"k-icon k-i-arrow-chevron-left\"></span></a>' + messages.editor.timezoneTitle + '<a href=\"\\\\#\" class=\"k-header-done k-scheduler-update k-link\" title=\"' + messages.save + '\" ' + 'aria-label=\"' + messages.save + '\"><span class=\"k-icon k-i-check\"></span></a>' + '</div><div data-role=\"content\"></div>';\n                    this.timezoneView = timezoneView = pane.append(html);\n                    timezoneView.contentElement.append(container.show());\n                    timezoneView.element.on(CLICK + NS, '.k-scheduler-cancel, .k-scheduler-update', function (e) {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        if ($(this).hasClass('k-scheduler-cancel')) {\n                            that._revertTimezones(model);\n                        }\n                        model.unbind('change', startTimezoneChange);\n                        var editView = that._editPane;\n                        var text = timezoneButtonText(model, messages.editor.noTimezone);\n                        editView.content.find('.k-timezone-label').text(text);\n                        pane.navigate(editView, that.options.animations.right);\n                    });\n                    kSwitch.bind('change', function (ev) {\n                        endTimezoneRow.toggle(ev.checked);\n                        model.set('endTimezone', '');\n                    });\n                    model.bind('change', startTimezoneChange);\n                }\n                kSwitch.value(!!model.endTimezone);\n                kSwitch.enable(!!model.startTimezone);\n                if (model.endTimezone) {\n                    endTimezoneRow.show();\n                } else {\n                    endTimezoneRow.hide();\n                }\n                pane.navigate(timezoneView, that.options.animations.left);\n            },\n            showDialog: function (options) {\n                var actions = options.buttons.map(function (button) {\n                    return {\n                        text: button.text,\n                        action: button.click\n                    };\n                });\n                actions.push({\n                    text: this.options.messages.cancel,\n                    primary: true\n                });\n                $('<div />').appendTo(document.body).kendoDialog({\n                    close: function () {\n                        this.destroy();\n                    },\n                    modal: { preventScroll: true },\n                    closable: false,\n                    title: false,\n                    content: options.text,\n                    actions: actions\n                });\n            },\n            editEvent: function (model) {\n                var pane = this.pane;\n                var html = '';\n                var messages = this.options.messages;\n                var updateText = messages.save;\n                var removeText = messages.destroy;\n                var cancelText = messages.cancel;\n                var titleText = messages.editor.editorTitle;\n                var resetSeries = messages.resetSeries;\n                html += '<div data-role=\"view\" class=\"k-popup-edit-form k-scheduler-edit-form k-mobile-list\"' + kendo.attr('uid') + '=\"' + model.uid + '\">' + '<div data-role=\"header\" class=\"k-header\">' + '<a href=\"\\\\#\" class=\"k-header-cancel k-scheduler-cancel k-link\" title=\"' + cancelText + '\"' + 'aria-label=\"' + cancelText + '\"><span class=\"k-icon k-i-arrow-chevron-left\"></span></a>' + titleText + '<a href=\"\\\\#\" class=\"k-header-done k-scheduler-update k-link\" title=\"' + updateText + '\" ' + 'aria-label=\"' + updateText + '\"><span class=\"k-icon k-i-check\"></span></a>' + '</div>';\n                var fields = this.fields(editors.mobile, model);\n                var that = this;\n                var editableFields = [];\n                html += this._buildEditTemplate(model, fields, editableFields, true);\n                html += '</div>';\n                var view = pane.append(html);\n                if (!model.isNew() && this.options.editable && this.options.editable.destroy !== false && model.isRecurrenceHead() && model.recurrenceException) {\n                    var resetSeriesBtn = '<ul class=\"k-edit-buttons\"><li class=\"k-item\"><span href=\"#\" class=\"k-scheduler-resetSeries k-label\" aria-label=\"' + resetSeries + '\">' + resetSeries + '</span></li></ul>';\n                    view.contentElement.append(resetSeriesBtn);\n                }\n                if (!model.isNew() && this.options.editable && this.options.editable.destroy !== false) {\n                    var deleteBtn = '<ul class=\"k-edit-buttons\"><li class=\"k-item\"><span href=\"#\" class=\"k-scheduler-delete k-label\" aria-label=\"' + removeText + '\">' + removeText + '</span></li></ul>';\n                    view.contentElement.append(deleteBtn);\n                }\n                this._editPane = view;\n                var container = this.container = view.element;\n                this.editable = container.kendoEditable({\n                    fields: editableFields,\n                    model: model,\n                    clearContainer: false,\n                    target: that.options.target,\n                    validateOnBlur: true\n                }).data('kendoEditable');\n                if (!this.trigger('edit', {\n                        container: container,\n                        model: model\n                    })) {\n                    container.on(CLICK + NS, 'a.k-scheduler-edit, a.k-scheduler-cancel, a.k-scheduler-update, span.k-scheduler-delete, span.k-scheduler-resetSeries', function (e) {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        var button = $(this);\n                        if (!button.hasClass('k-scheduler-edit')) {\n                            var name = 'cancel';\n                            if (button.hasClass('k-scheduler-update')) {\n                                name = 'save';\n                            } else if (button.hasClass('k-scheduler-delete')) {\n                                name = 'remove';\n                            } else if (button.hasClass('k-scheduler-resetSeries')) {\n                                name = RESET;\n                            }\n                            that.trigger(name, {\n                                container: container,\n                                model: model\n                            });\n                        } else {\n                            pane.navigate(this._editPane, that.options.animations.right);\n                        }\n                    });\n                    pane.navigate(view, that.options.animations.left);\n                    model.bind('change', that.toggleDateValidationHandler);\n                } else {\n                    this.trigger('cancel', {\n                        container: container,\n                        model: model\n                    });\n                }\n                return this.editable;\n            },\n            _views: function () {\n                return this.pane.element.find(kendo.roleSelector('view')).not(this.view.element);\n            },\n            close: function () {\n                if (this.container) {\n                    this.pane.navigate('', this.options.animations.right);\n                    var views = this._views();\n                    var view;\n                    for (var idx = 0, length = views.length; idx < length; idx++) {\n                        view = views.eq(idx).data('kendoView');\n                        if (view) {\n                            view.purge();\n                        }\n                    }\n                    views.remove();\n                    this.container = null;\n                    if (this.editable) {\n                        this.editable.options.model.unbind('change', this.toggleDateValidationHandler);\n                        this.editable.destroy();\n                        this.editable = null;\n                    }\n                    this.timezoneView = null;\n                }\n            }\n        });\n        var PopupEditor = Editor.extend({\n            destroy: function () {\n                this.close();\n                this.unbind();\n            },\n            editEvent: function (model) {\n                var that = this;\n                var editable = that.options.editable;\n                var html = '<div ' + kendo.attr('uid') + '=\"' + model.uid + '\" class=\"k-popup-edit-form k-scheduler-edit-form\"><div class=\"k-edit-form-container\">';\n                var messages = that.options.messages;\n                var updateText = messages.save;\n                var cancelText = messages.cancel;\n                var deleteText = messages.destroy;\n                var resetSeries = messages.resetSeries;\n                var fields = this.fields(editors.desktop, model);\n                var editableFields = [];\n                html += this._buildEditTemplate(model, fields, editableFields, false);\n                var attr;\n                var options = isPlainObject(editable) ? editable.window : {};\n                html += '<div class=\"k-edit-buttons k-state-default\">';\n                html += this.createButton({\n                    name: 'update',\n                    text: updateText,\n                    attr: attr\n                }) + this.createButton({\n                    name: 'canceledit',\n                    text: cancelText,\n                    attr: attr\n                });\n                if (!model.isNew() && editable.destroy !== false && model.isRecurrenceHead() && model.recurrenceException) {\n                    html += this.createButton({\n                        name: 'resetSeries',\n                        text: resetSeries,\n                        attr: attr\n                    });\n                }\n                if (!model.isNew() && editable.destroy !== false) {\n                    html += this.createButton({\n                        name: 'delete',\n                        text: deleteText,\n                        attr: attr\n                    });\n                }\n                html += '</div></div></div>';\n                var container = this.container = $(html).appendTo(that.element).eq(0).kendoWindow(extend({\n                    modal: true,\n                    resizable: false,\n                    draggable: true,\n                    title: messages.editor.editorTitle,\n                    visible: false,\n                    close: function (e) {\n                        if (e.userTriggered) {\n                            if (that.trigger(CANCEL, {\n                                    container: container,\n                                    model: model\n                                })) {\n                                e.preventDefault();\n                            }\n                        }\n                    }\n                }, options));\n                that.editable = container.kendoEditable({\n                    fields: editableFields,\n                    model: model,\n                    clearContainer: false,\n                    validateOnBlur: true,\n                    target: that.options.target\n                }).data('kendoEditable');\n                if (!that.trigger(EDIT, {\n                        container: container,\n                        model: model\n                    })) {\n                    container.data('kendoWindow').center().open();\n                    container.on(CLICK + NS, 'a.k-scheduler-cancel', function (e) {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        that.trigger(CANCEL, {\n                            container: container,\n                            model: model\n                        });\n                    });\n                    container.on(CLICK + NS, 'a.k-scheduler-update', function (e) {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        that.trigger('save', {\n                            container: container,\n                            model: model\n                        });\n                    });\n                    container.on(CLICK + NS, 'a.k-scheduler-delete', function (e) {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        that.trigger(REMOVE, {\n                            container: container,\n                            model: model\n                        });\n                    });\n                    container.on(CLICK + NS, 'a.k-scheduler-resetSeries', function (e) {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        that.trigger(RESET, {\n                            container: container,\n                            model: model\n                        });\n                    });\n                    kendo.cycleForm(container);\n                    model.bind('change', that.toggleDateValidationHandler);\n                } else {\n                    that.trigger(CANCEL, {\n                        container: container,\n                        model: model\n                    });\n                }\n                return that.editable;\n            },\n            close: function () {\n                var that = this;\n                var destroy = function () {\n                    if (that.editable) {\n                        that.editable.options.model.unbind('change', that.toggleDateValidationHandler);\n                        that.editable.destroy();\n                        that.editable = null;\n                        that.container = null;\n                    }\n                    if (that.popup) {\n                        that.popup.destroy();\n                        that.popup = null;\n                    }\n                };\n                if (that.editable) {\n                    if (that._timezonePopup && that._timezonePopup.data('kendoWindow')) {\n                        that._timezonePopup.data('kendoWindow').destroy();\n                        that._timezonePopup = null;\n                    }\n                    if (that.container.is(':visible')) {\n                        that.container.data('kendoWindow').bind('deactivate', destroy).close();\n                    } else {\n                        destroy();\n                    }\n                } else {\n                    destroy();\n                }\n            },\n            _createEndTimezoneButton: function () {\n                var messages = this.options.messages;\n                var html = '';\n                html += '<div class=\"k-edit-buttons k-state-default\">';\n                html += this.createButton({\n                    name: 'savetimezone',\n                    text: messages.save\n                }) + this.createButton({\n                    name: 'canceltimezone',\n                    text: messages.cancel\n                });\n                html += '</div></div></div>';\n                return html;\n            },\n            showDialog: function (options) {\n                var html = kendo.format('<div class=\\'k-popup-edit-form\\'><div class=\\'k-edit-form-container\\'><p class=\\'k-popup-message\\'>{0}</p>', options.text);\n                html += '<div class=\"k-edit-buttons k-state-default\">';\n                for (var buttonIndex = 0; buttonIndex < options.buttons.length; buttonIndex++) {\n                    html += this.createButton(options.buttons[buttonIndex]);\n                }\n                html += '</div></div></div>';\n                var wrapper = this.element;\n                if (this.popup) {\n                    this.popup.destroy();\n                }\n                var popup = this.popup = $(html).appendTo(wrapper).eq(0).on(CLICK, '.k-button', function (e) {\n                    e.preventDefault();\n                    popup.close();\n                    var buttonIndex = $(e.currentTarget).index();\n                    options.buttons[buttonIndex].click();\n                }).kendoWindow({\n                    modal: true,\n                    resizable: false,\n                    draggable: false,\n                    title: options.title,\n                    visible: false,\n                    close: function () {\n                        this.destroy();\n                        wrapper.focus();\n                    }\n                }).getKendoWindow();\n                popup.center().open();\n            },\n            _initTimezoneEditor: function (model, activator) {\n                var that = this;\n                var container = that.container.find('.k-scheduler-timezones');\n                var checkbox = container.find('input.k-timezone-toggle');\n                var endTimezoneRow = container.find('.k-edit-label:last').add(container.find('.k-edit-field:last'));\n                var saveButton = container.find('.k-scheduler-savetimezone');\n                var cancelButton = container.find('.k-scheduler-canceltimezone');\n                var timezonePopup = that._timezonePopup;\n                var startTimezoneChange = function (e) {\n                    if (e.field === 'startTimezone') {\n                        var value = model.startTimezone;\n                        checkbox.prop('disabled', !value);\n                        if (!value) {\n                            endTimezoneRow.hide();\n                            model.set('endTimezone', '');\n                            checkbox.prop('checked', false);\n                        }\n                    }\n                };\n                var wnd;\n                that._startTimezone = model.startTimezone;\n                that._endTimezone = model.endTimezone;\n                if (!timezonePopup) {\n                    that._timezonePopup = timezonePopup = container.kendoWindow({\n                        modal: true,\n                        resizable: false,\n                        draggable: true,\n                        title: that.options.messages.editor.timezoneEditorTitle,\n                        visible: false,\n                        close: function (e) {\n                            model.unbind('change', startTimezoneChange);\n                            if (e.userTriggered) {\n                                that._revertTimezones(model);\n                            }\n                            if (activator) {\n                                activator.focus();\n                            }\n                        }\n                    });\n                    checkbox.click(function () {\n                        endTimezoneRow.toggle(checkbox.prop('checked'));\n                        model.set('endTimezone', '');\n                    });\n                    saveButton.click(function (e) {\n                        e.preventDefault();\n                        wnd.close();\n                    });\n                    cancelButton.click(function (e) {\n                        e.preventDefault();\n                        that._revertTimezones(model);\n                        wnd.close();\n                    });\n                    model.bind('change', startTimezoneChange);\n                }\n                checkbox.prop('checked', model.endTimezone).prop('disabled', !model.startTimezone);\n                if (model.endTimezone) {\n                    endTimezoneRow.show();\n                } else {\n                    endTimezoneRow.hide();\n                }\n                wnd = timezonePopup.data('kendoWindow');\n                wnd.center().open();\n            }\n        });\n        var Scheduler = DataBoundWidget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                if (!that.options.views || !that.options.views.length) {\n                    that.options.views = [\n                        'day',\n                        'week'\n                    ];\n                }\n                that.resources = [];\n                that._initModel();\n                that._wrapper();\n                that._views();\n                that._toolbar();\n                that._dataSource();\n                that._resources();\n                that._resizeHandler = function () {\n                    that.resize();\n                };\n                that.wrapper.on(MOUSEDOWN + NS + ' selectstart' + NS, function (e) {\n                    if (!$(e.target).is(':kendoFocusable')) {\n                        e.preventDefault();\n                    }\n                });\n                if (that.options.editable && that.options.editable.resize !== false) {\n                    that._resizable();\n                }\n                that._movable();\n                that._bindResize();\n                if (that.options.messages && that.options.messages.recurrence) {\n                    recurrence.options = that.options.messages.recurrence;\n                }\n                that._selectable();\n                that._touchHandlers();\n                that._ariaId = kendo.guid();\n                that._createEditor();\n            },\n            _bindResize: function () {\n                $(window).on('resize' + NS, this._resizeHandler);\n            },\n            _unbindResize: function () {\n                $(window).off('resize' + NS, this._resizeHandler);\n            },\n            dataItems: function () {\n                var that = this;\n                var items = that.items();\n                var events = that._data;\n                var eventsUids = $.map(items, function (item) {\n                    return $(item).attr('data-uid');\n                });\n                var i;\n                var key;\n                var dict = {};\n                var eventsUidsLength = eventsUids.length;\n                for (i = 0; i < eventsUidsLength; i++) {\n                    dict[eventsUids[i]] = null;\n                }\n                var eventsCount = events.length;\n                for (i = 0; i < eventsCount; i++) {\n                    var event = events[i];\n                    if (dict[event.uid] !== undefined) {\n                        dict[event.uid] = event;\n                    }\n                }\n                var sortedData = [];\n                for (key in dict) {\n                    sortedData.push(dict[key]);\n                }\n                return sortedData;\n            },\n            _isMobile: function () {\n                var options = this.options;\n                return options.mobile === true && kendo.support.mobileOS || options.mobile === 'phone' || options.mobile === 'tablet';\n            },\n            _isTouch: function (event) {\n                return /touch/.test(event.type) || event.originalEvent && /touch/.test(event.originalEvent.pointerType);\n            },\n            _isInverseColor: function (eventElement) {\n                return eventElement.hasClass(INVERSECOLORCLASS);\n            },\n            _groupsByResource: function (resources, groupIndex, groupsArray, parentFieldValue, parentField) {\n                if (!groupsArray) {\n                    groupsArray = [];\n                }\n                var resource = resources[0];\n                if (resource) {\n                    var group;\n                    var data = resource.dataSource.view();\n                    var prevIndex = 0;\n                    for (var dataIndex = 0; dataIndex < data.length; dataIndex++) {\n                        var fieldValue = kendo.getter(resource.dataValueField)(data[dataIndex]);\n                        var currentGroupIndex = groupIndex + prevIndex + dataIndex;\n                        group = this._groupsByResource(resources.slice(1), currentGroupIndex, groupsArray, fieldValue, resource.field);\n                        group[resource.field] = fieldValue;\n                        prevIndex = group.groupIndex;\n                        if (parentField && parentFieldValue) {\n                            group[parentField] = parentFieldValue;\n                        }\n                        if (resources.length === 1) {\n                            group.groupIndex = groupIndex + dataIndex;\n                            groupsArray.push(group);\n                        }\n                    }\n                    return group;\n                } else {\n                    return {};\n                }\n            },\n            data: function () {\n                return this._data;\n            },\n            select: function (options) {\n                var that = this;\n                var view = that.view();\n                var selection = that._selection;\n                var groups = view.groups;\n                var selectedGroups;\n                if (options === undefined) {\n                    var selectedEvents;\n                    var slots = view._selectedSlots;\n                    if (!selection) {\n                        return [];\n                    }\n                    if (selection && selection.events) {\n                        selectedEvents = that._selectedEvents();\n                    }\n                    return {\n                        start: selection.start,\n                        end: selection.end,\n                        events: selectedEvents,\n                        slots: slots,\n                        resources: view._resourceBySlot(selection)\n                    };\n                }\n                if (!options) {\n                    that._selection = null;\n                    that._old = null;\n                    view.clearSelection();\n                    return;\n                }\n                if ($.isArray(options)) {\n                    options = { events: options.splice(0) };\n                }\n                if (options.resources) {\n                    var fieldName;\n                    var filters = [];\n                    var groupsByResource = [];\n                    if (view.groupedResources) {\n                        that._groupsByResource(view.groupedResources, 0, groupsByResource);\n                    }\n                    for (fieldName in options.resources) {\n                        filters.push({\n                            field: fieldName,\n                            operator: 'eq',\n                            value: options.resources[fieldName]\n                        });\n                    }\n                    selectedGroups = new kendo.data.Query(groupsByResource).filter(filters).toArray();\n                }\n                if (options.events && options.events.length) {\n                    that._selectEvents(options.events, selectedGroups);\n                    that._select();\n                    return;\n                }\n                if (groups && (options.start && options.end)) {\n                    var rangeStart = getDate(view._startDate);\n                    var rangeEnd = kendo.date.addDays(getDate(view._endDate), 1);\n                    var group;\n                    var ranges;\n                    if (options.start < rangeEnd && rangeStart <= options.end) {\n                        if (selectedGroups && selectedGroups.length) {\n                            group = groups[selectedGroups[0].groupIndex];\n                        } else {\n                            group = groups[0];\n                        }\n                        if (!group.timeSlotCollectionCount()) {\n                            options.isAllDay = true;\n                        }\n                        ranges = group.ranges(options.start, options.end, options.isAllDay, false);\n                        if (ranges.length) {\n                            that._selection = {\n                                start: kendo.timezone.toLocalDate(ranges[0].start.start),\n                                end: kendo.timezone.toLocalDate(ranges[ranges.length - 1].end.end),\n                                groupIndex: ranges[0].start.groupIndex,\n                                index: ranges[0].start.index,\n                                isAllDay: ranges[0].start.isDaySlot,\n                                events: []\n                            };\n                            that._select();\n                        }\n                    }\n                }\n            },\n            _selectEvents: function (eventsUids, selectedGroups) {\n                var that = this;\n                var idx;\n                var view = that.view();\n                var groups = view.groups;\n                var eventsLength = eventsUids.length;\n                var isGrouped = selectedGroups && selectedGroups.length;\n                for (idx = 0; idx < eventsLength; idx++) {\n                    if (groups && isGrouped) {\n                        var currentGroup = groups[selectedGroups[0].groupIndex];\n                        var events = [];\n                        var timeSlotCollectionCount = currentGroup.timeSlotCollectionCount();\n                        var daySlotCollectionCount = currentGroup.daySlotCollectionCount();\n                        for (var collIdx = 0; collIdx < timeSlotCollectionCount; collIdx++) {\n                            events = events.concat(currentGroup.getTimeSlotCollection(collIdx).events());\n                        }\n                        for (var dayCollIdx = 0; dayCollIdx < daySlotCollectionCount; dayCollIdx++) {\n                            events = events.concat(currentGroup.getDaySlotCollection(dayCollIdx).events());\n                        }\n                        events = new kendo.data.Query(events).filter({\n                            field: 'element[0].getAttribute(\\'data-uid\\')',\n                            operator: 'eq',\n                            value: eventsUids[idx]\n                        }).toArray();\n                        if (events[0]) {\n                            that._createSelection(events[0].element);\n                        }\n                    } else {\n                        var element = view.element.find(kendo.format('.k-event[data-uid={0}], .k-task[data-uid={0}]', eventsUids[idx]));\n                        if (element.length) {\n                            that._createSelection(element[0]);\n                        }\n                    }\n                }\n            },\n            _touchHandlers: function () {\n                var that = this;\n                var startX;\n                var startY;\n                var endX;\n                var endY;\n                var timeStamp;\n                var wrapper = that.wrapper;\n                var touchMoveHandler = $.proxy(that._touchMove, that);\n                wrapper.on(TOUCHSTART + NS, '.k-scheduler-header-all-day td, .k-scheduler-content td, .k-event', function (e) {\n                    var content = that.wrapper.find('.k-scheduler-content');\n                    if (!that._isTouch(e)) {\n                        return;\n                    }\n                    content.stop(true, false);\n                    that._touchPosX = startX = that._tapPosition(e, 'X');\n                    that._touchPosY = startY = that._tapPosition(e, 'Y');\n                    that._userTouched = true;\n                    that.view()._scrolling = false;\n                    timeStamp = Date.now();\n                    wrapper.on(TOUCHMOVE + NS, '.k-scheduler-header-all-day td, .k-scheduler-content td, .k-event', touchMoveHandler);\n                });\n                wrapper.on(TOUCHEND + NS, '.k-scheduler-header-all-day td, .k-scheduler-content td, .k-event', function (e) {\n                    if (!that._isTouch(e)) {\n                        return;\n                    }\n                    var delta = Date.now() - timeStamp;\n                    var content = that.wrapper.find('.k-scheduler-content');\n                    var amplitude = -that._amplitude * (3000 / delta);\n                    endX = that._tapPosition(e, 'X');\n                    endY = that._tapPosition(e, 'Y');\n                    if (that._dragging) {\n                        return;\n                    }\n                    if (that.options.selectable && (Math.abs(endX - startX) <= 10 || Math.abs(endY - startY) <= 10)) {\n                        that._mouseDownSelection(e);\n                    }\n                    if (!kendo.support.kineticScrollNeeded && delta < 200 && Math.abs(endX - startX) > 10) {\n                        content.animate({ scrollTop: content[0].scrollTop + amplitude });\n                    }\n                    wrapper.off(TOUCHMOVE + NS, '.k-scheduler-header-all-day td, .k-scheduler-content td', touchMoveHandler);\n                });\n            },\n            _selectable: function () {\n                var that = this;\n                var wrapper = that.wrapper;\n                if (!that.options.selectable) {\n                    return;\n                }\n                that._tabindex();\n                wrapper.on(MOUSEDOWN + NS, '.k-scheduler-header-all-day td, .k-scheduler-content td, .k-event', function (e) {\n                    if (that._isTouch(e)) {\n                        return;\n                    }\n                    that._mouseDownSelection(e);\n                });\n                var mouseMoveHandler = $.proxy(that._mouseMove, that);\n                wrapper.on(MOUSEDOWN + NS, '.k-scheduler-header-all-day td, .k-scheduler-content td', function (e) {\n                    var which = e.which;\n                    var button = e.button;\n                    var isRight = which && which === 3 || button && button == 2;\n                    if (that._isTouch(e)) {\n                        return;\n                    }\n                    if (!isRight) {\n                        wrapper.on(MOUSEMOVE + NS, '.k-scheduler-header-all-day td, .k-scheduler-content td', mouseMoveHandler);\n                    }\n                });\n                wrapper.on('mouseup' + NS + ' mousecancel' + NS, function () {\n                    wrapper.off(MOUSEMOVE + NS, '.k-scheduler-header-all-day td, .k-scheduler-content td', mouseMoveHandler);\n                });\n                wrapper.on('focus' + NS, function () {\n                    if (!that._selection && !that._userTouched) {\n                        that._selectFirstSlot();\n                    }\n                    that._select();\n                });\n                wrapper.on('focusout' + NS, function (e) {\n                    that._ctrlKey = that._shiftKey = false;\n                    that.toolbar.find('ul > li').removeClass(FOCUSEDSTATE);\n                    if (!$(e.relatedTarget).closest(VIEWSSELECTOR).length) {\n                        that.toolbar.find(VIEWSSELECTOR).removeClass(EXPANDEDSTATE);\n                    }\n                });\n                wrapper.on('keydown' + NS, proxy(that._keydown, that));\n                wrapper.on('keyup' + NS, function (e) {\n                    that._ctrlKey = e.ctrlKey;\n                    that._shiftKey = e.shiftKey;\n                });\n            },\n            _mouseDownSelection: function (e) {\n                var which = e.which;\n                var button = e.button;\n                var isRight = which && which === 3 || button && button == 2;\n                if (!isRight) {\n                    if (e.ctrlKey) {\n                        this._ctrlKey = e.ctrlKey;\n                    }\n                    if (e.shiftKey) {\n                        this._shiftKey = e.shiftKey;\n                    }\n                    this._createSelection(e.currentTarget);\n                }\n                if (kendo._activeElement() !== this.wrapper.get(0)) {\n                    kendo.focusElement(this.wrapper);\n                } else {\n                    this._select();\n                }\n                this.toolbar.find('ul > li').removeClass(FOCUSEDSTATE);\n            },\n            _selectFirstSlot: function () {\n                this._createSelection(this.wrapper.find('.k-scheduler-content').find('td:first'));\n            },\n            _select: function () {\n                var that = this;\n                var view = that.view();\n                var wrapper = that.wrapper;\n                var current = view.current();\n                var selection = that._selection;\n                var oldSelection = that._old ? that._old.selection : null;\n                var oldEventsLength = that._old ? that._old.eventsLength : null;\n                if (!selection) {\n                    return;\n                }\n                if (current) {\n                    current.removeAttribute('id');\n                    current.removeAttribute('aria-label');\n                    wrapper.removeAttr('aria-activedescendant');\n                }\n                view.select(selection);\n                current = view.current();\n                if (current && (oldSelection !== current || selection.events && oldEventsLength !== selection.events.length)) {\n                    var currentUid = $(current).data('uid');\n                    if (that._old && currentUid && currentUid === $(that._old.selection).data('uid') && (selection.events && that._old.eventsLength === selection.events.length)) {\n                        return;\n                    }\n                    var labelFormat;\n                    var data = selection;\n                    var events = that._selectedEvents();\n                    var slots = view._selectedSlots;\n                    if (events[0]) {\n                        data = events[0] || selection;\n                        labelFormat = kendo.format(that.options.messages.ariaEventLabel, data.title, data.start, data.start);\n                    } else {\n                        labelFormat = kendo.format(that.options.messages.ariaSlotLabel, data.start, data.end);\n                    }\n                    current.setAttribute('id', that._ariaId);\n                    current.setAttribute('aria-label', labelFormat);\n                    wrapper.attr('aria-activedescendant', that._ariaId);\n                    that._old = {\n                        selection: current,\n                        eventsLength: events.length\n                    };\n                    that.trigger('change', {\n                        start: selection.start,\n                        end: selection.end,\n                        events: events,\n                        slots: slots,\n                        resources: view._resourceBySlot(selection)\n                    });\n                }\n            },\n            _selectedEvents: function () {\n                var uids = this._selection.events;\n                var length = uids.length;\n                var idx = 0;\n                var event;\n                var events = [];\n                for (; idx < length; idx++) {\n                    event = this.occurrenceByUid(uids[idx]);\n                    if (event) {\n                        events.push(event);\n                    }\n                }\n                return events;\n            },\n            _tapPosition: function (event, coordinate) {\n                return /touch/.test(event.type) ? (event.originalEvent || event).changedTouches[0]['page' + coordinate] : event['page' + coordinate];\n            },\n            _touchMove: function (e) {\n                var that = this;\n                var content = that.wrapper.find('.k-scheduler-content');\n                var verticalScroll = content[0].scrollHeight > content[0].clientHeight;\n                var horizontalScroll = content[0].scrollWidth > content[0].clientWidth;\n                var endY = that._tapPosition(e, 'Y');\n                var endX = that._tapPosition(e, 'X');\n                var scrollTop = content[0].scrollTop - Math.round(endY - that._touchPosY);\n                var scrollLeft = content[0].scrollLeft - Math.round(endX - that._touchPosX);\n                var applyVerticalScroll = verticalScroll && Math.abs(endY - that._touchPosY) > 10;\n                var applyhorizontalScroll = horizontalScroll && Math.abs(endY - that._touchPosY) > 10;\n                if (that._dragging || kendo.support.kineticScrollNeeded || !that._isTouch(e)) {\n                    return;\n                }\n                if (applyVerticalScroll || applyhorizontalScroll) {\n                    that._amplitude = Math.round(endY - that._touchPosY);\n                    that._touchPosY = endY;\n                    that._touchPosX = endX;\n                    content.animate({\n                        scrollTop: scrollTop,\n                        scrollLeft: scrollLeft\n                    }, 0);\n                    that.view()._scrolling = true;\n                }\n            },\n            _mouseMove: function (e) {\n                var that = this;\n                clearTimeout(that._moveTimer);\n                if (that._isTouch(e)) {\n                    return;\n                }\n                that._moveTimer = setTimeout(function () {\n                    var view = that.view();\n                    var selection = that._selection;\n                    if (selection) {\n                        var slot = view.selectionByElement($(e.currentTarget));\n                        if (slot && selection.groupIndex === slot.groupIndex) {\n                            var startDate = slot.startDate();\n                            var endDate = slot.endDate();\n                            if (startDate >= selection.end) {\n                                selection.backward = false;\n                            } else if (endDate <= selection.start) {\n                                selection.backward = true;\n                            }\n                            if (selection.backward) {\n                                selection.start = startDate;\n                            } else {\n                                selection.end = endDate;\n                            }\n                            that._select();\n                        }\n                    }\n                }, 5);\n            },\n            _viewByIndex: function (index) {\n                var view, views = this.views;\n                for (view in views) {\n                    if (!index) {\n                        return view;\n                    }\n                    index--;\n                }\n            },\n            _keydown: function (e) {\n                var that = this, key = e.keyCode, view = that.view(), editable = view.options.editable, selection = that._selection, prevSelection = $.extend(selection), isModifier = key === 16 || key === 18 || key === 17 || key === 91 || key === 92, focusableToolBarSelector = '.k-scheduler-tools > li,' + '.k-scheduler-navigation > li,' + '.k-scheduler-views > li.k-state-selected:visible, ' + '.k-scheduler-views > li.k-current-view:visible', focusableItems = that.toolbar.find(focusableToolBarSelector), viewsWrapper = that.toolbar.find(VIEWSSELECTOR), shouldNavigate = $(e.target).closest(VIEWSSELECTOR).length || that.toolbar.find('.k-scheduler-views .k-state-focused').length, focusedViewIndex = viewsWrapper.children().index(that.toolbar.find('.' + FOCUSEDSTATE)), selectedIndex, isRtl = kendo.support.isRtl(that.element), direction = isRtl ? -1 : 1;\n                if (focusedViewIndex == -1) {\n                    focusedViewIndex = viewsWrapper.children().index(that.toolbar.find('.k-state-selected'));\n                }\n                that._ctrlKey = e.ctrlKey;\n                that._shiftKey = e.shiftKey;\n                if (key === keys.F10) {\n                    that.toolbar.find('ul > li:first').focus().addClass(FOCUSEDSTATE);\n                    e.preventDefault();\n                    return;\n                } else if (key === keys.TAB) {\n                    if (that.toolbar.find('.' + FOCUSEDSTATE).length) {\n                        var idx = focusableItems.index(that.toolbar.find('.' + FOCUSEDSTATE));\n                        if (idx === -1 && that._focusedView) {\n                            idx = focusableItems.index(that.toolbar.find('.k-scheduler-views > .k-state-selected'));\n                        }\n                        var itemToFocus = e.shiftKey ? focusableItems[idx - 1] : focusableItems[idx + 1];\n                        that.toolbar.find('.' + FOCUSEDSTATE).removeClass(FOCUSEDSTATE);\n                        if (itemToFocus) {\n                            $(itemToFocus).addClass(FOCUSEDSTATE).focus();\n                            that._focusedView = null;\n                            e.preventDefault();\n                            return;\n                        } else {\n                            that.element.focus();\n                            e.preventDefault();\n                            return;\n                        }\n                    }\n                } else if (key === keys.ENTER || key === keys.SPACEBAR) {\n                    if (shouldNavigate && that._focusedView && !that._focusedView.hasClass('k-state-selected')) {\n                        var focusedViewName = that._focusedView.data().name;\n                        if (!that.trigger('navigate', {\n                                view: focusedViewName,\n                                action: 'changeView',\n                                date: that.date()\n                            })) {\n                            that.view(focusedViewName);\n                            viewsWrapper.removeClass(EXPANDEDSTATE);\n                            if (that.toolbar.find('.k-current-view:visible').length) {\n                                $(document.activeElement).blur();\n                                that.toolbar.find('.k-current-view:visible').addClass(FOCUSEDSTATE).find('.k-link').focus();\n                            }\n                        }\n                        e.preventDefault();\n                        return;\n                    }\n                    if (that.toolbar.find('.' + FOCUSEDSTATE + ':visible').length) {\n                        that.toolbar.find('.' + FOCUSEDSTATE + ':visible').click();\n                        e.preventDefault();\n                        return;\n                    }\n                } else if (e.altKey && key === keys.DOWN) {\n                    if (that.toolbar.find('.' + FOCUSEDSTATE + ':visible').length) {\n                        that.toolbar.find('.' + FOCUSEDSTATE + ':visible').click();\n                        e.preventDefault();\n                        return;\n                    }\n                } else if (key === keys.RIGHT && shouldNavigate) {\n                    $(that.toolbar.find('.' + FOCUSEDSTATE)).removeClass(FOCUSEDSTATE);\n                    if (isRtl) {\n                        that._focusedView = focusedViewIndex - 1 === 0 ? $(viewsWrapper.children(':not(.k-current-view):last')) : $(viewsWrapper.children()[focusedViewIndex + 1 * direction]);\n                    } else {\n                        that._focusedView = focusedViewIndex + 1 === viewsWrapper.children().length ? $(viewsWrapper.children(':not(.k-current-view):first')) : $(viewsWrapper.children()[focusedViewIndex + 1 * direction]);\n                    }\n                    that._focusedView.focus().addClass(FOCUSEDSTATE);\n                    e.preventDefault();\n                    return;\n                } else if (key === keys.LEFT && shouldNavigate) {\n                    $(that.toolbar.find('.' + FOCUSEDSTATE)).removeClass(FOCUSEDSTATE);\n                    if (isRtl) {\n                        that._focusedView = focusedViewIndex + 1 === viewsWrapper.children().length ? $(viewsWrapper.children(':not(.k-current-view):first')) : $(viewsWrapper.children()[focusedViewIndex - 1 * direction]);\n                    } else {\n                        that._focusedView = focusedViewIndex - 1 === 0 ? $(viewsWrapper.children(':not(.k-current-view):last')) : $(viewsWrapper.children()[focusedViewIndex - 1 * direction]);\n                    }\n                    that._focusedView.focus().addClass(FOCUSEDSTATE);\n                    e.preventDefault();\n                    return;\n                } else if (key === keys.DOWN && that.toolbar.find(VIEWSSELECTOR).hasClass(EXPANDEDSTATE)) {\n                    that.toolbar.find('.' + FOCUSEDSTATE).removeClass(FOCUSEDSTATE);\n                    if (that._focusedView) {\n                        selectedIndex = viewsWrapper.find(that._focusedView).index();\n                    } else {\n                        selectedIndex = viewsWrapper.children('.k-scheduler-views > .k-state-selected').index();\n                    }\n                    that._focusedView = selectedIndex + 1 === viewsWrapper.children().length ? $(viewsWrapper.children(':not(.k-current-view):first')) : $(viewsWrapper.children()[selectedIndex + 1 * direction]);\n                    that._focusedView.focus().addClass(FOCUSEDSTATE);\n                    e.preventDefault();\n                    return;\n                } else if (key === keys.UP && that.toolbar.find(VIEWSSELECTOR).hasClass(EXPANDEDSTATE)) {\n                    that.toolbar.find('.' + FOCUSEDSTATE).removeClass(FOCUSEDSTATE);\n                    if (that._focusedView) {\n                        selectedIndex = viewsWrapper.find(that._focusedView).index();\n                    } else {\n                        selectedIndex = viewsWrapper.children('.k-scheduler-views > .k-state-selected').index();\n                    }\n                    that._focusedView = selectedIndex - 1 === 0 ? $(viewsWrapper.children(':not(.k-current-view):last')) : $(viewsWrapper.children()[selectedIndex - 1 * direction]);\n                    that._focusedView.focus().addClass(FOCUSEDSTATE);\n                    e.preventDefault();\n                    return;\n                } else if (e.altKey && key === keys.DOWN && that.toolbar.find('.k-nav-current').hasClass(FOCUSEDSTATE)) {\n                    that._showCalendar();\n                    e.preventDefault();\n                    return;\n                } else if (key === keys.ESC && that.popup && that.popup.visible()) {\n                    that.popup.close();\n                    e.preventDefault();\n                    return;\n                } else if (key === keys.ESC && that.toolbar.find(VIEWSSELECTOR).hasClass(EXPANDEDSTATE)) {\n                    that.toolbar.find(VIEWSSELECTOR).removeClass(EXPANDEDSTATE);\n                    that.toolbar.find(VIEWSSELECTOR).children().removeClass(FOCUSEDSTATE);\n                    that._focusedView = null;\n                    that.toolbar.find('.k-current-view').focus().addClass(FOCUSEDSTATE);\n                    e.preventDefault();\n                    return;\n                }\n                if (isModifier) {\n                    return;\n                }\n                if (!selection) {\n                    that._selectFirstSlot();\n                    that._select();\n                    that.element.focus();\n                    return;\n                }\n                if (key === keys.TAB) {\n                    if (view.moveToEvent(selection, e.shiftKey)) {\n                        that._select();\n                        that.element.focus();\n                        e.preventDefault();\n                    }\n                } else if (key === keys.ENTER || key === keys.SPACEBAR) {\n                    if (selection.events.length && editable) {\n                        if (editable.update !== false) {\n                            that.editEvent(selection.events[0]);\n                        }\n                    } else if (editable && editable.create !== false) {\n                        if (selection.isAllDay) {\n                            selection = $.extend({}, selection, { end: kendo.date.addDays(selection.end, -1) });\n                        }\n                        e.preventDefault();\n                        that.addEvent(extend({}, selection, view._resourceBySlot(selection)));\n                    }\n                } else if (key === keys.DELETE && editable !== false && editable.destroy !== false) {\n                    that.removeEvent(selection.events[0]);\n                } else if (key >= 49 && key <= 57) {\n                    var viewByIndex = that._viewByIndex(key - 49);\n                    if (viewByIndex && !that.trigger('navigate', {\n                            view: viewByIndex,\n                            action: 'changeView',\n                            date: that.date()\n                        })) {\n                        that.view(viewByIndex);\n                    }\n                } else if (view.move(selection, key, e.shiftKey)) {\n                    if (view.inRange(selection)) {\n                        that._select();\n                    } else {\n                        var action = that.date().getTime() > selection.start.getTime() ? 'previous' : 'next';\n                        if (!that.trigger('navigate', {\n                                view: that._selectedViewName,\n                                action: action,\n                                date: selection.start\n                            })) {\n                            that.date(selection.start);\n                        } else {\n                            selection.start = prevSelection.start;\n                            selection.end = prevSelection.end;\n                        }\n                    }\n                    that.toolbar.find('ul > li').removeClass(FOCUSEDSTATE);\n                    e.preventDefault();\n                }\n                that._adjustSelectedDate();\n            },\n            _createSelection: function (item) {\n                var selection = this._selection;\n                var uid;\n                var slot;\n                item = $(item);\n                if (item.is('.k-event')) {\n                    uid = item.attr(kendo.attr('uid'));\n                    if (selection && selection.events.indexOf(uid) !== -1 && !this._ctrlKey) {\n                        return;\n                    }\n                }\n                if (!selection || !this._ctrlKey && !this._shiftKey) {\n                    selection = this._selection = {\n                        events: [],\n                        groupIndex: 0\n                    };\n                }\n                slot = this.view().selectionByElement(item);\n                if (slot) {\n                    selection.groupIndex = slot.groupIndex || 0;\n                }\n                if (uid) {\n                    slot = getOccurrenceByUid(this._data, uid);\n                }\n                if (slot && slot.uid) {\n                    uid = [slot.uid];\n                }\n                this._updateSelection(slot, uid);\n                this._adjustSelectedDate();\n            },\n            _updateSelection: function (dataItem, events, groupIndex) {\n                var selection = this._selection;\n                if (dataItem && selection) {\n                    var view = this.view();\n                    if (dataItem.uid) {\n                        dataItem = view._updateEventForSelection(dataItem);\n                    }\n                    if (this._shiftKey && selection.start && selection.end) {\n                        var backward = dataItem.end < selection.end;\n                        selection.end = dataItem.endDate ? dataItem.endDate() : dataItem.end;\n                        if (backward && view._timeSlotInterval) {\n                            kendo.date.setTime(selection.end, -view._timeSlotInterval());\n                        }\n                    } else {\n                        selection.start = dataItem.startDate ? dataItem.startDate() : dataItem.start;\n                        selection.end = dataItem.endDate ? dataItem.endDate() : dataItem.end;\n                    }\n                    if ('isDaySlot' in dataItem) {\n                        selection.isAllDay = dataItem.isDaySlot;\n                    } else {\n                        selection.isAllDay = dataItem.isAllDay;\n                    }\n                    if (groupIndex !== null && groupIndex !== undefined) {\n                        selection.groupIndex = groupIndex;\n                    }\n                    selection.index = dataItem.index;\n                    if (this._ctrlKey) {\n                        var indexOfEvent = events && events.length ? selection.events.indexOf(events[0]) : -1;\n                        if (indexOfEvent > -1) {\n                            selection.events.splice(indexOfEvent, 1);\n                        } else {\n                            selection.events = selection.events.concat(events || []);\n                        }\n                    } else {\n                        selection.events = events || [];\n                    }\n                }\n            },\n            options: {\n                name: 'Scheduler',\n                date: TODAY,\n                editable: true,\n                autoBind: true,\n                snap: true,\n                mobile: false,\n                timezone: '',\n                allDaySlot: true,\n                min: new Date(1900, 0, 1),\n                max: new Date(2099, 11, 31),\n                toolbar: null,\n                workWeekStart: 1,\n                workWeekEnd: 5,\n                showWorkHours: false,\n                startTime: TODAY,\n                endTime: TODAY,\n                currentTimeMarker: {\n                    updateInterval: 10000,\n                    useLocalTimezone: true\n                },\n                footer: {},\n                messages: {\n                    today: 'Today',\n                    pdf: 'Export to PDF',\n                    save: 'Save',\n                    cancel: 'Cancel',\n                    destroy: 'Delete',\n                    resetSeries: 'Reset Series',\n                    deleteWindowTitle: 'Delete event',\n                    next: 'Next',\n                    previous: 'Previous',\n                    ariaSlotLabel: 'Selected from {0:t} to {1:t}',\n                    ariaEventLabel: '{0} on {1:D} at {2:t}',\n                    views: {\n                        day: 'Day',\n                        week: 'Week',\n                        workWeek: 'Work Week',\n                        agenda: 'Agenda',\n                        month: 'Month',\n                        timeline: 'Timeline',\n                        timelineWeek: 'Timeline Week',\n                        timelineWorkWeek: 'Timeline Work Week',\n                        timelineMonth: 'Timeline Month'\n                    },\n                    recurrenceMessages: {\n                        deleteWindowTitle: 'Delete Recurring Item',\n                        resetSeriesWindowTitle: 'Reset Series',\n                        deleteWindowOccurrence: 'Delete current occurrence',\n                        deleteWindowSeries: 'Delete the series',\n                        editWindowTitle: 'Edit Recurring Item',\n                        editWindowOccurrence: 'Edit current occurrence',\n                        editWindowSeries: 'Edit the series'\n                    },\n                    editable: { confirmation: DELETECONFIRM },\n                    editor: {\n                        title: 'Title',\n                        start: 'Start',\n                        end: 'End',\n                        allDayEvent: 'All day event',\n                        description: 'Description',\n                        repeat: 'Repeat',\n                        timezone: 'Timezone',\n                        startTimezone: 'Start timezone',\n                        endTimezone: 'End timezone',\n                        separateTimezones: 'Use separate start and end time zones',\n                        timezoneEditorTitle: 'Timezones',\n                        timezoneEditorButton: 'Time zone',\n                        timezoneTitle: 'Time zones',\n                        noTimezone: 'No timezone',\n                        editorTitle: 'Event'\n                    }\n                },\n                height: null,\n                width: null,\n                resources: [],\n                group: {\n                    resources: [],\n                    orientation: 'horizontal'\n                },\n                views: [],\n                selectable: false\n            },\n            events: [\n                REMOVE,\n                EDIT,\n                CANCEL,\n                SAVE,\n                'add',\n                'dataBinding',\n                'dataBound',\n                'moveStart',\n                'move',\n                'moveEnd',\n                'resizeStart',\n                'resize',\n                'resizeEnd',\n                'navigate',\n                'change'\n            ],\n            destroy: function () {\n                var that = this, element;\n                Widget.fn.destroy.call(that);\n                if (that.dataSource) {\n                    that.dataSource.unbind(CHANGE, that._refreshHandler);\n                    that.dataSource.unbind(PROGRESS, that._progressHandler);\n                    that.dataSource.unbind(ERROR, that._errorHandler);\n                }\n                if (that._resourceRefreshHandler) {\n                    for (var idx = 0; idx < that.resources.length; idx++) {\n                        var resourceDS = that.resources[idx].dataSource;\n                        resourceDS.unbind(CHANGE, that._resourceRefreshHandler);\n                        resourceDS.unbind(PROGRESS, that._resourceProgressHandler);\n                        resourceDS.unbind(ERROR, that._resourceErrorHandler);\n                    }\n                }\n                if (that.calendar) {\n                    that.calendar.destroy();\n                    that.popup.destroy();\n                }\n                if (that.view()) {\n                    that.view().destroy();\n                }\n                if (that._editor) {\n                    that._editor.destroy();\n                }\n                if (this._moveDraggable) {\n                    this._moveDraggable.destroy();\n                }\n                if (this._resizeDraggable) {\n                    this._resizeDraggable.destroy();\n                }\n                element = that.element.add(that.wrapper).add(that.toolbar).add(that.popup);\n                element.off(NS);\n                clearTimeout(that._moveTimer);\n                that._model = null;\n                that.toolbar = null;\n                that.element = null;\n                $(window).off('resize' + NS, that._resizeHandler);\n                kendo.destroy(that.wrapper);\n            },\n            setDataSource: function (dataSource) {\n                this.options.dataSource = dataSource;\n                this._dataSource();\n                if (this.options.autoBind && dataSource.fetch) {\n                    dataSource.fetch();\n                } else if (isArray(dataSource)) {\n                    this.view(this._selectedView);\n                }\n            },\n            items: function () {\n                var content = this.wrapper.find('.k-scheduler-content');\n                var view = this.view();\n                if (view && view.options.name === 'agenda') {\n                    return content.find('.k-task');\n                } else {\n                    return content.find('.k-event').add(this.wrapper.find('.k-scheduler-header-wrap').find('.k-scheduler-header-all-day').siblings());\n                }\n            },\n            _movable: function () {\n                var startSlot;\n                var endSlot;\n                var startResources;\n                var startTime;\n                var endTime;\n                var event;\n                var clonedEvent;\n                var that = this;\n                var originSlot;\n                var originStartTime;\n                var originalEvent;\n                var distance = 0;\n                var clonedEvents = [];\n                var cachedEvents = [];\n                var isMobile = that._isMobile();\n                var movable = that.options.editable && that.options.editable.move !== false;\n                var resizable = that.options.editable && that.options.editable.resize !== false;\n                if (movable || resizable && isMobile) {\n                    that._dragging = false;\n                    if (isMobile && kendo.support.mobileOS.android) {\n                        distance = 5;\n                    }\n                    that._moveDraggable = new kendo.ui.Draggable(that.element, {\n                        distance: distance,\n                        filter: '.k-event',\n                        ignore: '.k-resize-handle',\n                        holdToDrag: isMobile,\n                        autoScroll: true\n                    });\n                    if (movable) {\n                        that._moveDraggable.bind('dragstart', function (e) {\n                            var view = that.view();\n                            var eventElement = e.currentTarget;\n                            var isTouch = that._isTouch(e);\n                            that._dragging = true;\n                            if (!view.options.editable || view.options.editable.move === false) {\n                                that._dragging = false;\n                                e.preventDefault();\n                                return;\n                            }\n                            if (isTouch && !eventElement.hasClass('k-event-active')) {\n                                that._dragging = false;\n                                that.element.find('.k-event-active').removeClass('k-event-active');\n                                e.preventDefault();\n                                return;\n                            }\n                            event = that.occurrenceByUid(eventElement.attr(kendo.attr('uid')));\n                            clonedEvent = event.clone();\n                            originalEvent = event.clone();\n                            clonedEvent.update(view._eventOptionsForMove(clonedEvent));\n                            clonedEvent.inverseColor = that._isInverseColor(eventElement);\n                            clonedEvents = [];\n                            if (that._selection) {\n                                var events = that._selection.events;\n                                for (var i = 0; i < events.length; i++) {\n                                    var evtClone = that.occurrenceByUid(events[i]).clone();\n                                    var evtCloneElement = this.element.find('div.k-event[data-uid=\"' + evtClone.uid + '\"]').eq(0);\n                                    evtClone.update(view._eventOptionsForMove(evtClone));\n                                    if (evtCloneElement.length) {\n                                        evtClone.inverseColor = that._isInverseColor(evtCloneElement);\n                                    }\n                                    clonedEvents.push(evtClone);\n                                }\n                            } else {\n                                clonedEvents.push(clonedEvent);\n                            }\n                            startSlot = view._slotByPosition(e.x.startLocation, e.y.startLocation);\n                            startResources = view._resourceBySlot(startSlot);\n                            originStartTime = startTime = startSlot.startOffset(e.x.startLocation, e.y.startLocation, that.options.snap);\n                            endSlot = startSlot;\n                            originSlot = startSlot;\n                            if (!startSlot || that.trigger('moveStart', { event: event })) {\n                                e.preventDefault();\n                            }\n                        }).bind('drag', function (e) {\n                            var view = that.view();\n                            var slot = view._slotByPosition(e.x.location, e.y.location);\n                            var distance;\n                            var range;\n                            var i;\n                            if (!slot) {\n                                return;\n                            }\n                            endTime = slot.startOffset(e.x.location, e.y.location, that.options.snap);\n                            if (slot.isDaySlot !== startSlot.isDaySlot) {\n                                if (slot.isDaySlot !== originSlot.isDaySlot) {\n                                    var slotIndex = $(startSlot.element).index();\n                                    var targetSlotElement = $(slot.element).parent().children().eq(slotIndex);\n                                    startSlot = view._slotByPosition(targetSlotElement.offset().left, targetSlotElement.offset().top);\n                                    startTime = startSlot.startOffset(e.x.location, e.y.location, true);\n                                    cachedEvents = clonedEvents.map(function (event) {\n                                        return event.clone();\n                                    });\n                                    for (i = 0; i < clonedEvents.length; i++) {\n                                        if (clonedEvents[i].isAllDay != slot.isDaySlot) {\n                                            clonedEvents[i].isAllDay = slot.isDaySlot;\n                                            clonedEvents[i].end = kendo.date.getDate(clonedEvents[i].start);\n                                            clonedEvents[i].start = kendo.date.getDate(clonedEvents[i].start);\n                                            if (!slot.isDaySlot) {\n                                                kendo.date.setTime(clonedEvents[i].start, kendo.date.getMilliseconds(view.startTime()));\n                                                kendo.date.setTime(clonedEvents[i].end, kendo.date.getMilliseconds(view.startTime()) + view._timeSlotInterval());\n                                            }\n                                        }\n                                    }\n                                } else {\n                                    startSlot = $.extend(true, {}, originSlot);\n                                    startTime = originStartTime;\n                                    clonedEvents = cachedEvents;\n                                }\n                            }\n                            distance = endTime - startTime;\n                            for (i = 0; i < clonedEvents.length; i++) {\n                                view._updateMoveHint(clonedEvents[i], slot.groupIndex, distance);\n                            }\n                            range = moveEventRange(clonedEvent, distance);\n                            if (!that.trigger('move', {\n                                    event: event,\n                                    slot: {\n                                        element: slot.element,\n                                        start: slot.startDate(),\n                                        end: slot.endDate(),\n                                        isDaySlot: slot.isDaySlot\n                                    },\n                                    resources: view._resourceBySlot(slot),\n                                    start: range.start,\n                                    end: range.end\n                                })) {\n                                endSlot = slot;\n                            } else {\n                                for (i = 0; i < clonedEvents.length; i++) {\n                                    view._updateMoveHint(clonedEvents[i], slot.groupIndex, distance);\n                                }\n                            }\n                        }).bind('dragend', function (e) {\n                            that.view()._removeMoveHint();\n                            var distance = endTime - startTime;\n                            var range = moveEventRange(clonedEvent, distance);\n                            var start = range.start;\n                            var end = range.end;\n                            that._dragging = false;\n                            var endResources = that.view()._resourceBySlot(endSlot);\n                            var prevented = that.trigger('moveEnd', {\n                                event: event,\n                                slot: {\n                                    element: endSlot.element,\n                                    start: endSlot.startDate(),\n                                    end: endSlot.endDate()\n                                },\n                                start: start,\n                                end: end,\n                                resources: endResources\n                            });\n                            if (!prevented && (event.start.getTime() !== start.getTime() || event.end.getTime() !== end.getTime() || originSlot.isDaySlot !== endSlot.isDaySlot || kendo.stringify(endResources) !== kendo.stringify(startResources))) {\n                                that._isMultiDrag = clonedEvents.length > 1;\n                                for (var i = 0; i < clonedEvents.length; i++) {\n                                    var evt = clonedEvents[i];\n                                    range = moveEventRange(evt, distance);\n                                    var updatedEventOptions = that.view()._eventOptionsForMove(evt);\n                                    var eventOptions = $.extend({\n                                        isAllDay: evt.isAllDay,\n                                        start: range.start,\n                                        end: range.end\n                                    }, updatedEventOptions, endResources);\n                                    that._updateEvent(null, evt, eventOptions);\n                                }\n                                if (that._isMultiDrag) {\n                                    that.dataSource.sync();\n                                    that._isMultiDrag = false;\n                                }\n                            }\n                            e.currentTarget.removeClass('k-event-active');\n                            this.cancelHold();\n                            clonedEvents = [];\n                            cachedEvents = [];\n                        }).bind('dragcancel', function () {\n                            that.view()._removeMoveHint();\n                            this.cancelHold();\n                            clonedEvents = [];\n                            cachedEvents = [];\n                        });\n                    }\n                    that._moveDraggable.bind('hold', function (e) {\n                        if (that._isTouch(e)) {\n                            that.element.find('.k-event-active').removeClass('k-event-active');\n                            if (that.options.selectable) {\n                                that._createSelection(e.currentTarget);\n                            }\n                            e.currentTarget.addClass('k-event-active');\n                        }\n                    });\n                }\n            },\n            _resizable: function () {\n                var startTime;\n                var endTime;\n                var event;\n                var clonedEvent;\n                var slot;\n                var that = this;\n                var distance = 0;\n                function direction(handle) {\n                    var directions = {\n                        'k-resize-e': 'east',\n                        'k-resize-w': 'west',\n                        'k-resize-n': 'north',\n                        'k-resize-s': 'south'\n                    };\n                    for (var key in directions) {\n                        if (handle.hasClass(key)) {\n                            return directions[key];\n                        }\n                    }\n                }\n                if (that._isMobile() && kendo.support.mobileOS.android) {\n                    distance = 5;\n                }\n                that._resizeDraggable = new kendo.ui.Draggable(that.element, {\n                    distance: distance,\n                    filter: '.k-resize-handle',\n                    autoScroll: true,\n                    dragstart: function (e) {\n                        var dragHandle = $(e.currentTarget);\n                        var eventElement = dragHandle.closest('.k-event');\n                        var uid = eventElement.attr(kendo.attr('uid'));\n                        var view = that.view();\n                        that._dragging = true;\n                        event = that.occurrenceByUid(uid);\n                        clonedEvent = event.clone();\n                        view._updateEventForResize(clonedEvent);\n                        slot = view._slotByPosition(e.x.startLocation, e.y.startLocation);\n                        if (that.trigger('resizeStart', { event: event })) {\n                            e.preventDefault();\n                        }\n                        startTime = kendo.date.toUtcTime(clonedEvent.start);\n                        endTime = kendo.date.toUtcTime(clonedEvent.end);\n                    },\n                    drag: function (e) {\n                        if (!slot) {\n                            return;\n                        }\n                        var dragHandle = $(e.currentTarget);\n                        var dir = direction(dragHandle);\n                        var view = that.view();\n                        var currentSlot = view._slotByPosition(e.x.location, e.y.location);\n                        if (!currentSlot || slot.groupIndex != currentSlot.groupIndex) {\n                            return;\n                        }\n                        slot = currentSlot;\n                        var originalStart = startTime;\n                        var originalEnd = endTime;\n                        if (dir == 'south') {\n                            if (!slot.isDaySlot && slot.end - kendo.date.toUtcTime(clonedEvent.start) >= view._timeSlotInterval()) {\n                                if (clonedEvent.isAllDay) {\n                                    endTime = slot.startOffset(e.x.location, e.y.location, that.options.snap);\n                                } else {\n                                    endTime = slot.endOffset(e.x.location, e.y.location, that.options.snap);\n                                }\n                            }\n                        } else if (dir == 'north') {\n                            if (!slot.isDaySlot && kendo.date.toUtcTime(clonedEvent.end) - slot.start >= view._timeSlotInterval()) {\n                                startTime = slot.startOffset(e.x.location, e.y.location, that.options.snap);\n                            }\n                        } else if (dir == 'east') {\n                            if (slot.isDaySlot && kendo.date.toUtcTime(kendo.date.getDate(slot.endDate())) >= kendo.date.toUtcTime(kendo.date.getDate(clonedEvent.start))) {\n                                if (clonedEvent.isAllDay) {\n                                    endTime = slot.startOffset(e.x.location, e.y.location, that.options.snap);\n                                } else {\n                                    endTime = slot.endOffset(e.x.location, e.y.location, that.options.snap);\n                                }\n                            } else if (!slot.isDaySlot && slot.end - kendo.date.toUtcTime(clonedEvent.start) >= view._timeSlotInterval()) {\n                                endTime = slot.endOffset(e.x.location, e.y.location, that.options.snap);\n                            }\n                        } else if (dir == 'west') {\n                            if (slot.isDaySlot && kendo.date.toUtcTime(kendo.date.getDate(clonedEvent.end)) >= kendo.date.toUtcTime(kendo.date.getDate(slot.startDate()))) {\n                                startTime = slot.startOffset(e.x.location, e.y.location, that.options.snap);\n                            } else if (!slot.isDaySlot && kendo.date.toUtcTime(clonedEvent.end) - slot.start >= view._timeSlotInterval()) {\n                                startTime = slot.startOffset(e.x.location, e.y.location, that.options.snap);\n                            }\n                        }\n                        if (!that.trigger('resize', {\n                                event: event,\n                                slot: {\n                                    element: slot.element,\n                                    start: slot.startDate(),\n                                    end: slot.endDate()\n                                },\n                                start: kendo.timezone.toLocalDate(startTime),\n                                end: kendo.timezone.toLocalDate(endTime),\n                                resources: view._resourceBySlot(slot)\n                            })) {\n                            view._updateResizeHint(clonedEvent, slot.groupIndex, startTime, endTime);\n                        } else {\n                            startTime = originalStart;\n                            endTime = originalEnd;\n                        }\n                    },\n                    dragend: function (e) {\n                        var dragHandle = $(e.currentTarget);\n                        var start = new Date(clonedEvent.start.getTime());\n                        var end = new Date(clonedEvent.end.getTime());\n                        var dir = direction(dragHandle);\n                        that._dragging = false;\n                        that.view()._removeResizeHint();\n                        if (dir == 'south') {\n                            end = kendo.timezone.toLocalDate(endTime);\n                        } else if (dir == 'north') {\n                            start = kendo.timezone.toLocalDate(startTime);\n                        } else if (dir == 'east') {\n                            if (slot.isDaySlot) {\n                                end = kendo.date.getDate(kendo.timezone.toLocalDate(endTime));\n                            } else {\n                                end = kendo.timezone.toLocalDate(endTime);\n                            }\n                        } else if (dir == 'west') {\n                            if (slot.isDaySlot) {\n                                start = new Date(kendo.timezone.toLocalDate(startTime));\n                                start.setHours(0);\n                                start.setMinutes(0);\n                            } else {\n                                start = kendo.timezone.toLocalDate(startTime);\n                            }\n                        }\n                        var prevented = that.trigger('resizeEnd', {\n                            event: event,\n                            slot: {\n                                element: slot.element,\n                                start: slot.startDate(),\n                                end: slot.endDate()\n                            },\n                            start: start,\n                            end: end,\n                            resources: that.view()._resourceBySlot(slot)\n                        });\n                        if (!prevented && end.getTime() >= start.getTime()) {\n                            if (clonedEvent.start.getTime() != start.getTime() || clonedEvent.end.getTime() != end.getTime()) {\n                                that.view()._updateEventForResize(event);\n                                that._updateEvent(dir, event, {\n                                    start: start,\n                                    end: end\n                                });\n                            }\n                        }\n                        slot = null;\n                        event = null;\n                    },\n                    dragcancel: function () {\n                        that._dragging = false;\n                        that.view()._removeResizeHint();\n                        slot = null;\n                        event = null;\n                    }\n                });\n            },\n            _updateEvent: function (dir, event, eventInfo) {\n                var that = this;\n                var updateEvent = function (event, callback) {\n                    try {\n                        that._preventRefresh = true;\n                        event.update(eventInfo);\n                        that._convertDates(event);\n                    } finally {\n                        that._preventRefresh = false;\n                    }\n                    if (!that.trigger(SAVE, { event: event })) {\n                        if (callback) {\n                            callback();\n                        }\n                        if (!that._isMultiDrag) {\n                            that.dataSource.sync();\n                        }\n                    }\n                };\n                var recurrenceHead = function (event) {\n                    if (event.recurrenceRule) {\n                        return that.dataSource.getByUid(event.uid);\n                    } else {\n                        return that.dataSource.get(event.recurrenceId);\n                    }\n                };\n                var updateSeries = function () {\n                    var head = recurrenceHead(event);\n                    if (dir == 'south' || dir == 'north') {\n                        if (eventInfo.start) {\n                            var start = kendo.date.getDate(head.start);\n                            kendo.date.setTime(start, getMilliseconds(eventInfo.start));\n                            eventInfo.start = start;\n                        }\n                        if (eventInfo.end) {\n                            var end = kendo.date.getDate(head.end);\n                            kendo.date.setTime(end, getMilliseconds(eventInfo.end));\n                            eventInfo.end = end;\n                        }\n                    }\n                    that.dataSource._removeExceptions(head);\n                    updateEvent(head);\n                };\n                var updateOccurrence = function () {\n                    var head = recurrenceHead(event);\n                    var eventUid;\n                    var callback = function () {\n                        that._convertDates(head);\n                        if (that._selection) {\n                            that._selection.events.push(eventUid);\n                        }\n                    };\n                    var exception = head.toOccurrence({\n                        start: event.start,\n                        end: event.end\n                    });\n                    eventUid = exception.uid;\n                    updateEvent(that.dataSource.add(exception), callback);\n                };\n                if (event.recurrenceRule || event.isOccurrence()) {\n                    var recurrenceMessages = that.options.messages.recurrenceMessages;\n                    that._showRecurringDialog(event, updateOccurrence, updateSeries, {\n                        title: recurrenceMessages.editWindowTitle,\n                        text: recurrenceMessages.editRecurring ? recurrenceMessages.editRecurring : EDITRECURRING,\n                        occurrenceText: recurrenceMessages.editWindowOccurrence,\n                        seriesText: recurrenceMessages.editWindowSeries\n                    });\n                } else {\n                    updateEvent(that.dataSource.getByUid(event.uid));\n                }\n            },\n            _modelForContainer: function (container) {\n                container = $(container).closest('[' + kendo.attr('uid') + ']');\n                return this.dataSource.getByUid(container.attr(kendo.attr('uid')));\n            },\n            showDialog: function (options) {\n                this._editor.showDialog(options);\n            },\n            focus: function () {\n                this.wrapper.focus();\n            },\n            _confirmation: function (callback, model, isResetSeries) {\n                var editable = this.options.editable;\n                if (editable === true || editable.confirmation !== false) {\n                    var messages = this.options.messages;\n                    var title = messages.deleteWindowTitle;\n                    var text = typeof editable.confirmation === STRING ? editable.confirmation : messages.editable.confirmation;\n                    if (this._isEditorOpened() && model.isRecurring()) {\n                        var recurrenceMessages = this.options.messages.recurrenceMessages;\n                        title = recurrenceMessages.deleteWindowTitle;\n                        if (model.isException()) {\n                            text = recurrenceMessages.deleteRecurringConfirmation ? recurrenceMessages.deleteRecurringConfirmation : DELETERECURRINGCONFIRM;\n                        } else {\n                            text = recurrenceMessages.deleteSeriesConfirmation ? recurrenceMessages.deleteSeriesConfirmation : DELETESERIESCONFIRM;\n                        }\n                        if (isResetSeries) {\n                            title = recurrenceMessages.resetSeriesWindowTitle;\n                            text = recurrenceMessages.resetSeriesConfirmation ? recurrenceMessages.resetSeriesConfirmation : RESETSERIESCONFIRM;\n                        }\n                    }\n                    var buttons = [{\n                            name: 'destroy',\n                            text: isResetSeries ? messages.resetSeries : messages.destroy,\n                            click: function () {\n                                callback();\n                            }\n                        }];\n                    if (!(this._isMobile() && kendo.Pane)) {\n                        buttons.push({\n                            name: 'canceledit',\n                            text: messages.cancel,\n                            click: function () {\n                                callback(true);\n                            }\n                        });\n                    }\n                    this._unbindResize();\n                    this.showDialog({\n                        model: model,\n                        text: text,\n                        title: title,\n                        buttons: buttons\n                    });\n                    this._bindResize();\n                } else {\n                    callback();\n                }\n            },\n            addEvent: function (eventInfo) {\n                var editable = this._editor.editable;\n                var dataSource = this.dataSource;\n                var event;\n                eventInfo = eventInfo || {};\n                var prevented = this.trigger('add', { event: eventInfo });\n                if (!prevented && (editable && editable.end() || !editable)) {\n                    this.cancelEvent();\n                    if (eventInfo && eventInfo.toJSON) {\n                        eventInfo = eventInfo.toJSON();\n                    }\n                    event = dataSource.add(eventInfo);\n                    if (event) {\n                        this.cancelEvent();\n                        this._editEvent(event);\n                    }\n                }\n            },\n            saveEvent: function () {\n                var editor = this._editor;\n                if (!editor) {\n                    return;\n                }\n                var editable = editor.editable;\n                var container = editor.container;\n                var model = this._modelForContainer(container);\n                if (container && editable && editable.end() && !this.trigger(SAVE, {\n                        container: container,\n                        event: model\n                    })) {\n                    if (!model.dirty && !model.isOccurrence()) {\n                        this._convertDates(model, 'remove');\n                    }\n                    this.dataSource.sync();\n                }\n            },\n            cancelEvent: function () {\n                var editor = this._editor;\n                var container = editor.container;\n                var model;\n                if (container) {\n                    model = this._modelForContainer(container);\n                    if (model && model.isOccurrence()) {\n                        this._convertDates(model, 'remove');\n                        this._convertDates(this.dataSource.get(model.recurrenceId), 'remove');\n                    }\n                    this.dataSource.cancelChanges(model);\n                    editor.close();\n                }\n            },\n            editEvent: function (uid) {\n                var model = typeof uid == 'string' ? this.occurrenceByUid(uid) : uid;\n                if (!model) {\n                    return;\n                }\n                this.cancelEvent();\n                if (model.isRecurring()) {\n                    this._editRecurringDialog(model);\n                } else {\n                    this._editEvent(model);\n                }\n            },\n            _editEvent: function (model) {\n                this._preventRefresh = true;\n                this._unbindResize();\n                this._createPopupEditor(model);\n                this._bindResize();\n            },\n            _editRecurringDialog: function (model) {\n                var that = this;\n                var editOccurrence = function () {\n                    if (model.isException()) {\n                        that._editEvent(model);\n                    } else {\n                        that.addEvent(model);\n                    }\n                };\n                var editSeries = function () {\n                    if (model.recurrenceId) {\n                        model = that.dataSource.get(model.recurrenceId);\n                    }\n                    that._editEvent(model);\n                };\n                var recurrenceMessages = that.options.messages.recurrenceMessages;\n                that._showRecurringDialog(model, editOccurrence, editSeries, {\n                    title: recurrenceMessages.editWindowTitle,\n                    text: recurrenceMessages.editRecurring ? recurrenceMessages.editRecurring : EDITRECURRING,\n                    occurrenceText: recurrenceMessages.editWindowOccurrence,\n                    seriesText: recurrenceMessages.editWindowSeries\n                });\n            },\n            _showRecurringDialog: function (model, editOccurrence, editSeries, messages) {\n                var editable = this.options.editable;\n                var editRecurringMode = isPlainObject(editable) ? editable.editRecurringMode : 'dialog';\n                if (editRecurringMode === 'occurrence' || this._isMultiDrag) {\n                    editOccurrence();\n                } else if (editRecurringMode === 'series') {\n                    editSeries();\n                } else {\n                    this._unbindResize();\n                    this.showDialog({\n                        model: model,\n                        title: messages.title,\n                        text: messages.text,\n                        buttons: [\n                            {\n                                text: messages.occurrenceText,\n                                click: editOccurrence\n                            },\n                            {\n                                text: messages.seriesText,\n                                click: editSeries\n                            }\n                        ]\n                    });\n                    this._bindResize();\n                }\n            },\n            _createButton: function (command) {\n                var template = command.template || COMMANDBUTTONTMPL, commandName = typeof command === STRING ? command : command.name || command.text, options = {\n                        className: 'k-scheduler-' + (commandName || '').replace(/\\s/g, ''),\n                        text: commandName,\n                        attr: ''\n                    };\n                if (!commandName && !(isPlainObject(command) && command.template)) {\n                    throw new Error('Custom commands should have name specified');\n                }\n                if (isPlainObject(command)) {\n                    if (command.className) {\n                        command.className += ' ' + options.className;\n                    }\n                    if (commandName === 'edit' && isPlainObject(command.text)) {\n                        command = extend(true, {}, command);\n                        command.text = command.text.edit;\n                    }\n                    options = extend(true, options, defaultCommands[commandName], command);\n                } else {\n                    options = extend(true, options, defaultCommands[commandName]);\n                }\n                return kendo.template(template)(options);\n            },\n            _convertDates: function (model, method) {\n                var timezone = this.dataSource.reader.timezone;\n                var startTimezone = model.startTimezone;\n                var endTimezone = model.endTimezone;\n                var start = model.start;\n                var end = model.start;\n                method = method || 'apply';\n                startTimezone = startTimezone || endTimezone;\n                endTimezone = endTimezone || startTimezone;\n                if (startTimezone) {\n                    if (timezone) {\n                        if (method === 'apply') {\n                            start = kendo.timezone.convert(model.start, timezone, startTimezone);\n                            end = kendo.timezone.convert(model.end, timezone, endTimezone);\n                        } else {\n                            start = kendo.timezone.convert(model.start, startTimezone, timezone);\n                            end = kendo.timezone.convert(model.end, endTimezone, timezone);\n                        }\n                    } else {\n                        start = kendo.timezone[method](model.start, startTimezone);\n                        end = kendo.timezone[method](model.end, endTimezone);\n                    }\n                    model._set('start', start);\n                    model._set('end', end);\n                }\n            },\n            _createEditor: function () {\n                var that = this;\n                var editor;\n                if (this._isMobile() && kendo.Pane) {\n                    editor = that._editor = new MobileEditor(this.wrapper, extend({}, this.options, {\n                        target: this,\n                        timezone: that.dataSource.reader.timezone,\n                        resources: that.resources,\n                        createButton: proxy(this._createButton, this)\n                    }));\n                } else {\n                    editor = that._editor = new PopupEditor(this.wrapper, extend({}, this.options, {\n                        target: this,\n                        createButton: proxy(this._createButton, this),\n                        timezone: that.dataSource.reader.timezone,\n                        resources: that.resources\n                    }));\n                }\n                editor.bind('cancel', function (e) {\n                    if (that.trigger('cancel', {\n                            container: e.container,\n                            event: e.model\n                        })) {\n                        e.preventDefault();\n                        return;\n                    }\n                    that._preventRefresh = false;\n                    that.cancelEvent();\n                    if (that._attemptRefresh) {\n                        that.refresh();\n                    }\n                    that.focus();\n                });\n                editor.bind('edit', function (e) {\n                    if (that.trigger(EDIT, {\n                            container: e.container,\n                            event: e.model\n                        })) {\n                        e.preventDefault();\n                    }\n                });\n                editor.bind('save', function () {\n                    that._preventRefresh = false;\n                    that.saveEvent();\n                });\n                editor.bind('remove', function (e) {\n                    that._preventRefresh = false;\n                    that.removeEvent(e.model);\n                });\n                editor.bind('resetSeries', function (e) {\n                    that._confirmation(function (cancel) {\n                        that._preventRefresh = false;\n                        if (!cancel) {\n                            that.dataSource._removeExceptions(e.model);\n                            that.saveEvent();\n                        }\n                    }, e.model, true);\n                });\n            },\n            _createPopupEditor: function (model) {\n                var editor = this._editor;\n                if (!model.isNew() || model.isOccurrence()) {\n                    if (model.isOccurrence()) {\n                        this._convertDates(model.recurrenceId ? this.dataSource.get(model.recurrenceId) : model);\n                    }\n                    this._convertDates(model);\n                }\n                this.editable = editor.editEvent(model);\n            },\n            removeEvent: function (uid) {\n                var that = this, model = typeof uid == 'string' ? that.occurrenceByUid(uid) : uid;\n                if (!model) {\n                    return;\n                }\n                if (model.isRecurring()) {\n                    that._deleteRecurringDialog(model);\n                } else {\n                    that._confirmation(function (cancel) {\n                        if (!cancel) {\n                            that._removeEvent(model);\n                        }\n                    }, model);\n                }\n            },\n            occurrenceByUid: function (uid) {\n                var occurrence = this.dataSource.getByUid(uid);\n                if (!occurrence) {\n                    occurrence = getOccurrenceByUid(this._data, uid);\n                }\n                return occurrence;\n            },\n            occurrencesInRange: function (start, end) {\n                return new kendo.data.Query(this._data).filter({\n                    logic: 'or',\n                    filters: [\n                        {\n                            logic: 'and',\n                            filters: [\n                                {\n                                    field: 'start',\n                                    operator: 'gte',\n                                    value: start\n                                },\n                                {\n                                    field: 'end',\n                                    operator: 'gte',\n                                    value: start\n                                },\n                                {\n                                    field: 'start',\n                                    operator: 'lt',\n                                    value: end\n                                }\n                            ]\n                        },\n                        {\n                            logic: 'and',\n                            filters: [\n                                {\n                                    field: 'start',\n                                    operator: 'lte',\n                                    value: start\n                                },\n                                {\n                                    field: 'end',\n                                    operator: 'gt',\n                                    value: start\n                                }\n                            ]\n                        }\n                    ]\n                }).toArray();\n            },\n            _removeEvent: function (model) {\n                if (!this.trigger(REMOVE, { event: model })) {\n                    if (this.dataSource.remove(model)) {\n                        this.dataSource.sync();\n                    }\n                }\n            },\n            _deleteRecurringDialog: function (model) {\n                var that = this;\n                var currentModel = model;\n                var editable = that.options.editable;\n                var deleteOccurrence;\n                var deleteSeries;\n                var deleteOccurrenceConfirmation;\n                var deleteSeriesConfirmation;\n                var editRecurringMode = isPlainObject(editable) ? editable.editRecurringMode : 'dialog';\n                deleteOccurrence = function () {\n                    var occurrence = currentModel.recurrenceId ? currentModel : currentModel.toOccurrence();\n                    var head = that.dataSource.get(occurrence.recurrenceId);\n                    that._convertDates(head);\n                    that._removeEvent(occurrence);\n                };\n                deleteSeries = function () {\n                    if (currentModel.recurrenceId) {\n                        currentModel = that.dataSource.get(currentModel.recurrenceId);\n                    }\n                    that._removeEvent(currentModel);\n                };\n                if (editRecurringMode != 'dialog' || that._isEditorOpened()) {\n                    deleteOccurrenceConfirmation = function () {\n                        that._confirmation(function (cancel) {\n                            if (!cancel) {\n                                deleteOccurrence();\n                            }\n                        }, currentModel);\n                    };\n                    deleteSeriesConfirmation = function () {\n                        that._confirmation(function (cancel) {\n                            if (!cancel) {\n                                deleteSeries();\n                            }\n                        }, currentModel);\n                    };\n                }\n                var seriesCallback = deleteSeriesConfirmation || deleteSeries;\n                var occurrenceCallback = deleteOccurrenceConfirmation || deleteOccurrence;\n                if (that._isEditorOpened()) {\n                    if (model.isException()) {\n                        occurrenceCallback();\n                    } else {\n                        seriesCallback();\n                    }\n                } else {\n                    var recurrenceMessages = that.options.messages.recurrenceMessages;\n                    that._showRecurringDialog(model, occurrenceCallback, seriesCallback, {\n                        title: recurrenceMessages.deleteWindowTitle,\n                        text: recurrenceMessages.deleteRecurring ? recurrenceMessages.deleteRecurring : DELETERECURRING,\n                        occurrenceText: recurrenceMessages.deleteWindowOccurrence,\n                        seriesText: recurrenceMessages.deleteWindowSeries\n                    });\n                }\n            },\n            _isEditorOpened: function () {\n                return !!this._editor.container;\n            },\n            _unbindView: function (view) {\n                var that = this;\n                that.angular('cleanup', function () {\n                    return { elements: that.items() };\n                });\n                view.destroy();\n            },\n            _bindView: function (view) {\n                var that = this;\n                if (that.options.editable) {\n                    if (that._viewRemoveHandler) {\n                        view.unbind(REMOVE, that._viewRemoveHandler);\n                    }\n                    that._viewRemoveHandler = function (e) {\n                        that.removeEvent(e.uid);\n                    };\n                    view.bind(REMOVE, that._viewRemoveHandler);\n                    if (that._viewAddHandler) {\n                        view.unbind(ADD, that._viewAddHandler);\n                    }\n                    that._viewAddHandler = function (e) {\n                        that.addEvent(e.eventInfo);\n                    };\n                    view.bind(ADD, this._viewAddHandler);\n                    if (that._viewEditHandler) {\n                        view.unbind(EDIT, that._viewEditHandler);\n                    }\n                    that._viewEditHandler = function (e) {\n                        that.editEvent(e.uid);\n                    };\n                    view.bind(EDIT, this._viewEditHandler);\n                }\n                if (that._viewNavigateHandler) {\n                    view.unbind('navigate', that._viewNavigateHandler);\n                }\n                that._viewNavigateHandler = function (e) {\n                    if (e.view) {\n                        var switchWorkDay = 'isWorkDay' in e;\n                        var action = switchWorkDay ? 'changeWorkDay' : 'changeView';\n                        if (!that.trigger('navigate', {\n                                view: e.view,\n                                isWorkDay: e.isWorkDay,\n                                action: action,\n                                date: e.date\n                            })) {\n                            if (switchWorkDay) {\n                                that._workDayMode = e.isWorkDay;\n                            }\n                            that._selectView(e.view);\n                            that.date(e.date);\n                        }\n                    }\n                };\n                view.bind('navigate', that._viewNavigateHandler);\n                if (that._viewActivateHandler) {\n                    view.unbind('activate', that._viewActivateHandler);\n                }\n                that._viewActivateHandler = function () {\n                    var view = this;\n                    if (that._selection) {\n                        view.constrainSelection(that._selection);\n                        that._select();\n                        that._adjustSelectedDate();\n                    }\n                };\n                view.bind('activate', that._viewActivateHandler);\n            },\n            _selectView: function (name) {\n                var that = this;\n                if (name && that.views[name]) {\n                    if (that._selectedView) {\n                        that._unbindView(that._selectedView);\n                    }\n                    that._selectedView = that._renderView(name);\n                    that._selectedViewName = name;\n                    if (that._viewsCount > 1 && !that._isMobile()) {\n                        var viewButton = VIEWBUTTONTEMPLATE({\n                            views: that.views,\n                            view: name,\n                            ns: kendo.ns\n                        });\n                        var firstButton = that.toolbar.find('.k-scheduler-views li:first-child');\n                        if (firstButton.is('.k-current-view')) {\n                            firstButton.replaceWith(viewButton);\n                        } else {\n                            that.toolbar.find('.k-scheduler-views').prepend(viewButton);\n                        }\n                        var viewButtons = that.toolbar.find('.k-scheduler-views li').removeClass('k-state-selected');\n                        viewButtons.end().find('.k-view-' + name.replace(/\\./g, '\\\\.').toLowerCase()).addClass('k-state-selected');\n                    } else {\n                        var viewSelect = that.toolbar.find('.k-scheduler-mobile-views');\n                        viewSelect.find('[value=' + name.replace(/\\./g, '\\\\.') + ']').prop('selected', 'selected');\n                    }\n                }\n            },\n            view: function (name) {\n                var that = this;\n                if (name) {\n                    that._selectView(name);\n                    that.rebind();\n                    return;\n                }\n                return that._selectedView;\n            },\n            viewName: function () {\n                return this.view().name;\n            },\n            _renderView: function (name) {\n                var view = this._initializeView(name);\n                this._bindView(view);\n                if (kendo.support.mouseAndTouchPresent || kendo.support.pointers) {\n                    view.content.css('-ms-touch-action', 'pinch-zoom');\n                    view.content.css('touch-action', 'pinch-zoom');\n                }\n                this._model.set('formattedDate', view.dateForTitle());\n                this._model.set('formattedShortDate', view.shortDateForTitle());\n                this._model.set('formattedMobileDate', view.mobileDateForTitle ? view.mobileDateForTitle() : view.shortDateForTitle());\n                this._model.set('formattedYear', kendo.format('{0:yyyy}', view.startDate()));\n                return view;\n            },\n            resize: function (force) {\n                var size = this.getSize();\n                var currentSize = this._size;\n                var view = this.view();\n                if (!view || !view.groups) {\n                    return;\n                }\n                if (force || !currentSize || size.width !== currentSize.width || size.height !== currentSize.height) {\n                    this.refresh({ action: 'resize' });\n                    this._size = size;\n                }\n            },\n            _adjustSelectedDate: function () {\n                var date = this._model.selectedDate, selection = this._selection, start = selection.start;\n                if (start && !kendo.date.isInDateRange(date, getDate(start), getDate(selection.end))) {\n                    date.setFullYear(start.getFullYear(), start.getMonth(), start.getDate());\n                }\n            },\n            _initializeView: function (name) {\n                var view = this.views[name];\n                if (view) {\n                    var isSettings = isPlainObject(view), type = view.type;\n                    if (typeof type === STRING) {\n                        type = kendo.getter(view.type)(window);\n                    }\n                    if (type) {\n                        view = new type(this.wrapper, trimOptions(extend(true, {}, this.options, isSettings ? view : {}, {\n                            resources: this.resources,\n                            date: this.date(),\n                            startTime: kendo.parseDate(this.options.startTime),\n                            endTime: kendo.parseDate(this.options.endTime),\n                            showWorkHours: this._workDayMode\n                        })));\n                    } else {\n                        throw new Error('There is no such view');\n                    }\n                }\n                return view;\n            },\n            _views: function () {\n                var views = this.options.views;\n                var view;\n                var defaultView;\n                var selected;\n                var isSettings;\n                var name;\n                var type;\n                var idx;\n                var length;\n                this.views = {};\n                this._viewsCount = 0;\n                for (idx = 0, length = views.length; idx < length; idx++) {\n                    var hasType = false;\n                    view = views[idx];\n                    isSettings = isPlainObject(view);\n                    if (isSettings) {\n                        type = name = view.type ? view.type : view;\n                        if (typeof type !== STRING) {\n                            name = view.name || view.title;\n                            hasType = true;\n                        }\n                    } else {\n                        type = name = view;\n                    }\n                    defaultView = defaultViews[name];\n                    if (defaultView && !hasType) {\n                        view.type = defaultView.type;\n                        defaultView.title = this.options.messages.views[name];\n                        if (defaultView.type === 'day') {\n                            defaultView.messages = { allDay: this.options.messages.allDay };\n                        } else if (defaultView.type === 'agenda') {\n                            defaultView.messages = {\n                                event: this.options.messages.event,\n                                date: this.options.messages.date,\n                                time: this.options.messages.time\n                            };\n                        }\n                    }\n                    view = extend({ title: name }, defaultView, isSettings ? view : {});\n                    if (name) {\n                        this.views[name] = view;\n                        this._viewsCount++;\n                        if (!selected || view.selected) {\n                            selected = name;\n                        }\n                    }\n                }\n                if (selected) {\n                    this._selectedViewName = selected;\n                }\n            },\n            rebind: function () {\n                this.dataSource.fetch();\n            },\n            _dataSource: function () {\n                var that = this, options = that.options, dataSource = options.dataSource;\n                dataSource = isArray(dataSource) ? { data: dataSource } : dataSource;\n                if (options.timezone && !(dataSource instanceof SchedulerDataSource)) {\n                    dataSource = extend(true, dataSource, { schema: { timezone: options.timezone } });\n                } else if (dataSource instanceof SchedulerDataSource) {\n                    options.timezone = dataSource.options.schema ? dataSource.options.schema.timezone : '';\n                }\n                if (that.dataSource && that._refreshHandler) {\n                    that.dataSource.unbind(CHANGE, that._refreshHandler).unbind(PROGRESS, that._progressHandler).unbind(ERROR, that._errorHandler);\n                } else {\n                    that._refreshHandler = proxy(that.refresh, that);\n                    that._progressHandler = proxy(that._requestStart, that);\n                    that._errorHandler = proxy(that._error, that);\n                }\n                that.dataSource = kendo.data.SchedulerDataSource.create(dataSource).bind(CHANGE, that._refreshHandler).bind(PROGRESS, that._progressHandler).bind(ERROR, that._errorHandler);\n                that.options.dataSource = that.dataSource;\n            },\n            _error: function () {\n                this._progress(false);\n            },\n            _requestStart: function () {\n                this._progress(true);\n            },\n            _progress: function (toggle) {\n                var element = this.element.find('.k-scheduler-content');\n                kendo.ui.progress(element, toggle);\n            },\n            _resources: function () {\n                var that = this;\n                var resources = that.options.resources;\n                var resourcePromises = [];\n                for (var idx = 0; idx < resources.length; idx++) {\n                    var resource = resources[idx];\n                    var field = resource.field;\n                    var name = resource.name || field;\n                    var dataSource = resource.dataSource;\n                    if (!field || !dataSource) {\n                        throw new Error('The \"field\" and \"dataSource\" options of the scheduler resource are mandatory.');\n                    }\n                    that.resources.push({\n                        field: field,\n                        name: name,\n                        title: resource.title || field,\n                        dataTextField: resource.dataTextField || 'text',\n                        dataValueField: resource.dataValueField || 'value',\n                        dataColorField: resource.dataColorField || 'color',\n                        valuePrimitive: resource.valuePrimitive != null ? resource.valuePrimitive : true,\n                        multiple: resource.multiple || false,\n                        dataSource: that._resourceDataSource(dataSource, name, resourcePromises)\n                    });\n                }\n                if (!that.options.autoBind) {\n                    that._selectView(that._selectedViewName);\n                } else {\n                    $.when.apply(null, resourcePromises).then(function () {\n                        that.view(that._selectedViewName);\n                    });\n                }\n            },\n            _resourceDataSource: function (resourceDS, groupName, promises) {\n                var that = this;\n                var dataSource = isArray(resourceDS) ? { data: resourceDS } : resourceDS;\n                var dataSourceInstance = kendo.data.DataSource.create(dataSource);\n                if (that.options.autoBind) {\n                    promises.push(dataSourceInstance.fetch(function () {\n                        that._bindResourceEvents(this, groupName);\n                    }));\n                } else {\n                    that._bindResourceEvents(dataSourceInstance, groupName);\n                }\n                return dataSourceInstance;\n            },\n            _bindResourceEvents: function (resourceDS, groupName) {\n                var that = this;\n                var isGrouped = that.options.group && that.options.group.resources.length;\n                var isResourceGrouped = isGrouped && that.options.group.resources.indexOf(groupName) > -1;\n                if (!that._resourceRefreshHandler && isResourceGrouped) {\n                    that._resourceRefreshHandler = proxy(that._refreshResource, that);\n                    that._resourceProgressHandler = proxy(that._requestStart, that);\n                    that._resourceErrorHandler = proxy(that._error, that);\n                }\n                if (isResourceGrouped) {\n                    resourceDS.bind(CHANGE, that._resourceRefreshHandler).bind(PROGRESS, that._resourceProgressHandler).bind(ERROR, that._resourceErrorHandler);\n                }\n            },\n            _refreshResource: function () {\n                var that = this;\n                that.view(that._selectedViewName);\n            },\n            _initModel: function () {\n                var that = this;\n                that._model = kendo.observable({\n                    selectedDate: new Date(this.options.date),\n                    formattedDate: '',\n                    formattedShortDate: ''\n                });\n                that._model.bind('change', function (e) {\n                    if (e.field === 'selectedDate') {\n                        that.view(that._selectedViewName);\n                    }\n                });\n            },\n            _wrapper: function () {\n                var that = this;\n                var options = that.options;\n                var height = options.height;\n                var width = options.width;\n                that.wrapper = that.element.addClass('k-widget k-scheduler k-floatwrap').attr('role', 'grid').attr('aria-multiselectable', true);\n                if (that._isMobile()) {\n                    that.wrapper.addClass('k-scheduler-mobile');\n                }\n                if (height) {\n                    that.wrapper.height(height);\n                }\n                if (width) {\n                    that.wrapper.width(width);\n                }\n            },\n            date: function (value) {\n                if (value != null && getDate(value) >= getDate(this.options.min) && getDate(value) <= getDate(this.options.max)) {\n                    this._model.set('selectedDate', value);\n                }\n                return getDate(this._model.get('selectedDate'));\n            },\n            _toolbar: function () {\n                var that = this;\n                var options = that.options;\n                var commands = [];\n                if (options.toolbar) {\n                    commands = $.isArray(options.toolbar) ? options.toolbar : [options.toolbar];\n                }\n                var template = this._isMobile() ? MOBILETOOLBARTEMPLATE : TOOLBARTEMPLATE;\n                var toolbar = $(template({\n                    messages: options.messages,\n                    pdf: $.grep(commands, function (item) {\n                        return item == 'pdf' || item.name == 'pdf';\n                    }).length > 0,\n                    ns: kendo.ns,\n                    view: that._selectedViewName,\n                    views: that.views,\n                    viewsCount: that._viewsCount,\n                    editable: that.options.editable\n                }));\n                that.wrapper.append(toolbar);\n                that.toolbar = toolbar;\n                kendo.bind(that.toolbar, that._model);\n                toolbar.on(CLICK + NS, '.k-pdf', function (e) {\n                    e.preventDefault();\n                    that.saveAsPDF();\n                });\n                toolbar.on(CLICK + NS, '.k-create-event', function (e) {\n                    e.preventDefault();\n                    that.addEvent();\n                });\n                toolbar.on(CLICK + NS, '.k-nav-calendar', function (e) {\n                    e.preventDefault();\n                    that._showCalendar(e.target);\n                });\n                toolbar.on(CLICK + NS, '.k-scheduler-navigation li', function (e) {\n                    var li = $(this);\n                    var date = new Date(that.date());\n                    var action = '';\n                    var currentDate = new Date();\n                    var timezone = that.options.timezone;\n                    e.preventDefault();\n                    if (li.hasClass('k-nav-today')) {\n                        action = 'today';\n                        if (timezone) {\n                            var timezoneOffset = kendo.timezone.offset(currentDate, timezone);\n                            date = kendo.timezone.convert(currentDate, currentDate.getTimezoneOffset(), timezoneOffset);\n                        } else {\n                            date = currentDate;\n                        }\n                    } else if (li.hasClass('k-nav-next')) {\n                        action = 'next';\n                        date = that.view().nextDate();\n                    } else if (li.hasClass('k-nav-prev')) {\n                        action = 'previous';\n                        date = that.view().previousDate();\n                    } else if (li.hasClass('k-nav-current') && !that._isMobile()) {\n                        that._showCalendar();\n                        return;\n                    }\n                    if (!that.trigger('navigate', {\n                            view: that._selectedViewName,\n                            action: action,\n                            date: date\n                        })) {\n                        that.date(date);\n                    }\n                });\n                toolbar.on(CLICK + NS, '.k-scheduler-views li:not(.k-current-view), .k-scheduler-refresh', function (e) {\n                    e.preventDefault();\n                    var name = $(this).attr(kendo.attr('name'));\n                    if (!that.trigger('navigate', {\n                            view: name,\n                            action: 'changeView',\n                            date: that.date()\n                        })) {\n                        that.view(name);\n                        that.element.find('.' + EXPANDEDSTATE).removeClass(EXPANDEDSTATE);\n                    }\n                });\n                toolbar.on(CLICK + NS, '.k-scheduler-views li.k-current-view', function (e) {\n                    e.preventDefault();\n                    that.element.find('.k-scheduler-views').toggleClass(EXPANDEDSTATE);\n                    $(document).on(MOUSEDOWN + NS, function (e) {\n                        if ($(e.target).closest('.k-scheduler-views').length === 0) {\n                            that.element.find('.' + EXPANDEDSTATE).removeClass(EXPANDEDSTATE);\n                            $(document).off(CLICK + NS);\n                        }\n                    });\n                });\n                toolbar.find('.k-scheduler-mobile-views').on('change', function (e) {\n                    that.view(e.target.value);\n                });\n                toolbar.find('li').hover(function () {\n                    $(this).addClass('k-state-hover');\n                }, function () {\n                    $(this).removeClass('k-state-hover');\n                });\n            },\n            _showCalendar: function (targetElm) {\n                var that = this, target = targetElm || that.toolbar.find('.k-nav-current'), html = $('<div class=\"k-calendar-container\"><div class=\"k-scheduler-calendar\"/></div>');\n                if (!that.popup) {\n                    that.popup = new Popup(html, {\n                        anchor: target,\n                        activate: function () {\n                            if (that.popup && that.calendar) {\n                                that.popup._toggleResize(false);\n                                that.calendar.element.find('table').focus();\n                                that.popup._toggleResize(true);\n                            }\n                        },\n                        open: function () {\n                            if (!that.calendar) {\n                                that.calendar = new Calendar(this.element.find('.k-scheduler-calendar'), {\n                                    change: function () {\n                                        var date = this.value();\n                                        if (!that.trigger('navigate', {\n                                                view: that._selectedViewName,\n                                                action: 'changeDate',\n                                                date: date\n                                            })) {\n                                            that.date(date);\n                                            that.popup.close();\n                                        }\n                                        if (!that._isMobile) {\n                                            that._selectedView.element.focus();\n                                            that.toolbar.find('.k-nav-current').focus().addClass(FOCUSEDSTATE);\n                                        }\n                                    },\n                                    min: that.options.min,\n                                    max: that.options.max\n                                });\n                            }\n                            that.calendar.element.on('keydown' + NS, function (e) {\n                                if (e.keyCode === keys.ESC || e.keyCode === keys.TAB) {\n                                    that.popup.close();\n                                    that._selectedView.element.focus();\n                                    that.toolbar.find('.k-nav-current').focus().addClass(FOCUSEDSTATE);\n                                }\n                            });\n                            that.calendar.value(that.date());\n                        },\n                        copyAnchorStyles: false\n                    });\n                }\n                that.popup.open();\n            },\n            refresh: function (e) {\n                var that = this;\n                var view = this.view();\n                var preventRefresh = e && e.action === 'itemchange' && (this._editor.editable || this._preventRefresh) || this.dataSource.options.type === 'signalr' && this._preventRefresh;\n                this._progress(false);\n                this.angular('cleanup', function () {\n                    return { elements: that.items() };\n                });\n                e = e || {};\n                if (!view) {\n                    return;\n                }\n                if (preventRefresh) {\n                    this._attemptRefresh = true && this.dataSource.options.type === 'signalr';\n                    return;\n                }\n                if (this.trigger('dataBinding', {\n                        action: e.action || 'rebind',\n                        index: e.index,\n                        items: e.items\n                    })) {\n                    return;\n                }\n                if (!(e && e.action === 'resize') && this._editor) {\n                    this._editor.close();\n                }\n                this._data = this.dataSource.expand(view.startDate(), view.visibleEndDate());\n                view.refreshLayout();\n                view.render(this._data);\n                this.trigger('dataBound');\n                this._attemptRefresh = false;\n            },\n            slotByPosition: function (x, y) {\n                var view = this.view();\n                if (!view._slotByPosition) {\n                    return null;\n                }\n                var slot = view._slotByPosition(x, y);\n                if (!slot) {\n                    return null;\n                }\n                return {\n                    startDate: slot.startDate(),\n                    endDate: slot.endDate(),\n                    groupIndex: slot.groupIndex,\n                    element: slot.element,\n                    isDaySlot: slot.isDaySlot\n                };\n            },\n            slotByElement: function (element) {\n                var offset = $(element).offset();\n                return this.slotByPosition(offset.left, offset.top);\n            },\n            resourcesBySlot: function (slot) {\n                return this.view()._resourceBySlot(slot);\n            }\n        });\n        var defaultViews = {\n            day: { type: 'kendo.ui.DayView' },\n            week: { type: 'kendo.ui.WeekView' },\n            workWeek: { type: 'kendo.ui.WorkWeekView' },\n            agenda: { type: 'kendo.ui.AgendaView' },\n            month: { type: 'kendo.ui.MonthView' },\n            timeline: { type: 'kendo.ui.TimelineView' },\n            timelineWeek: { type: 'kendo.ui.TimelineWeekView' },\n            timelineWorkWeek: { type: 'kendo.ui.TimelineWorkWeekView' },\n            timelineMonth: { type: 'kendo.ui.TimelineMonthView' }\n        };\n        ui.plugin(Scheduler);\n        if (kendo.PDFMixin) {\n            kendo.PDFMixin.extend(Scheduler.prototype);\n            var SCHEDULER_EXPORT = 'k-scheduler-pdf-export';\n            Scheduler.fn._drawPDF = function (progress) {\n                var wrapper = this.wrapper;\n                var styles = wrapper[0].style.cssText;\n                wrapper.css({\n                    width: wrapper.width(),\n                    height: wrapper.height()\n                });\n                wrapper.addClass(SCHEDULER_EXPORT);\n                var scheduler = this;\n                var promise = new $.Deferred();\n                var table = wrapper.find('.k-scheduler-content').find('table').css('table-layout', 'auto');\n                setTimeout(function () {\n                    table.css('table-layout', 'fixed');\n                    scheduler.resize(true);\n                    scheduler._drawPDFShadow({}, { avoidLinks: scheduler.options.pdf.avoidLinks }).done(function (group) {\n                        var args = {\n                            page: group,\n                            pageNumber: 1,\n                            progress: 1,\n                            totalPages: 1\n                        };\n                        progress.notify(args);\n                        promise.resolve(args.page);\n                    }).fail(function (err) {\n                        promise.reject(err);\n                    }).always(function () {\n                        wrapper[0].style.cssText = styles;\n                        wrapper.removeClass(SCHEDULER_EXPORT);\n                        scheduler.resize(true);\n                        scheduler.resize(true);\n                    });\n                });\n                return promise;\n            };\n        }\n        var TimezoneEditor = Widget.extend({\n            init: function (element, options) {\n                var that = this, zones = kendo.timezone.windows_zones;\n                if (!zones || !kendo.timezone.zones_titles) {\n                    throw new Error('kendo.timezones.min.js is not included.');\n                }\n                Widget.fn.init.call(that, element, options);\n                that.wrapper = that.element;\n                that._zonesQuery = new kendo.data.Query(zones);\n                that._zoneTitleId = kendo.guid();\n                that._zoneTitlePicker();\n                that._zonePicker();\n                that._zoneTitle.bind('cascade', function () {\n                    if (!this.value()) {\n                        that._zone.wrapper.hide();\n                    }\n                });\n                that._zone.bind('cascade', function () {\n                    that._value = this.value();\n                    that.trigger('change');\n                });\n                that.value(that.options.value);\n            },\n            options: {\n                name: 'TimezoneEditor',\n                value: '',\n                optionLabel: 'No timezone'\n            },\n            events: ['change'],\n            _zoneTitlePicker: function () {\n                var that = this, zoneTitle = $('<input id=\"' + that._zoneTitleId + '\" aria-label=\"' + that.options.title + '\"/>').appendTo(that.wrapper);\n                that._zoneTitle = new kendo.ui.DropDownList(zoneTitle, {\n                    dataSource: kendo.timezone.zones_titles,\n                    dataValueField: 'other_zone',\n                    dataTextField: 'name',\n                    optionLabel: that.options.optionLabel\n                });\n            },\n            _zonePicker: function () {\n                var that = this, zone = $('<input aria-label=\"' + that.options.title + '\"/>').appendTo(this.wrapper);\n                that._zone = new kendo.ui.DropDownList(zone, {\n                    dataValueField: 'zone',\n                    dataTextField: 'territory',\n                    dataSource: that._zonesQuery.data,\n                    cascadeFrom: that._zoneTitleId,\n                    dataBound: function () {\n                        that._value = this.value();\n                        this.wrapper.toggle(this.dataSource.view().length > 1);\n                    }\n                });\n                that._zone.wrapper.hide();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                kendo.destroy(this.wrapper);\n            },\n            value: function (value) {\n                var that = this, zone;\n                if (value === undefined) {\n                    return that._value;\n                }\n                zone = that._zonesQuery.filter({\n                    field: 'zone',\n                    operator: 'eq',\n                    value: value\n                }).data[0];\n                if (zone) {\n                    that._zoneTitle.value(zone.other_zone);\n                    that._zone.value(zone.zone);\n                } else {\n                    that._zoneTitle.select(0);\n                }\n            }\n        });\n        ui.plugin(TimezoneEditor);\n        var ZONETITLEOPTIONTEMPLATE = kendo.template('<option value=\"#=other_zone#\">#=name#</option>');\n        var ZONEOPTIONTEMPLATE = kendo.template('<option value=\"#=zone#\">#=territory#</option>');\n        var MobileTimezoneEditor = Widget.extend({\n            init: function (element, options) {\n                var that = this, zones = kendo.timezone.windows_zones;\n                if (!zones || !kendo.timezone.zones_titles) {\n                    throw new Error('kendo.timezones.min.js is not included.');\n                }\n                Widget.fn.init.call(that, element, options);\n                that.wrapper = that.element;\n                that._zonesQuery = new kendo.data.Query(zones);\n                that._zoneTitlePicker();\n                that._zonePicker();\n                that.value(that.options.value);\n            },\n            options: {\n                name: 'MobileTimezoneEditor',\n                optionLabel: 'No timezone',\n                value: ''\n            },\n            events: ['change'],\n            _bindZones: function (value) {\n                var data = value ? this._filter(value) : [];\n                this._zone.html(this._options(data, ZONEOPTIONTEMPLATE));\n            },\n            _filter: function (value) {\n                return this._zonesQuery.filter({\n                    field: 'other_zone',\n                    operator: 'eq',\n                    value: value\n                }).data;\n            },\n            _options: function (data, template, optionLabel) {\n                var idx = 0;\n                var html = '';\n                var length = data.length;\n                if (optionLabel) {\n                    html += template({\n                        other_zone: '',\n                        name: optionLabel\n                    });\n                }\n                for (; idx < length; idx++) {\n                    html += template(data[idx]);\n                }\n                return html;\n            },\n            _zoneTitlePicker: function () {\n                var that = this;\n                var options = that._options(kendo.timezone.zones_titles, ZONETITLEOPTIONTEMPLATE, that.options.optionLabel);\n                that._zoneTitle = $('<select>' + options + '</select>').appendTo(that.wrapper).change(function () {\n                    var value = this.value;\n                    var zone = that._zonePickerLabel;\n                    var zoneSelect = zone.find('select');\n                    that._bindZones(value);\n                    if (value && zoneSelect.children().length > 1) {\n                        zone.show();\n                    } else {\n                        zone.hide();\n                    }\n                    that._value = that._zone[0].value;\n                    that.trigger('change');\n                });\n            },\n            _zonePicker: function () {\n                var that = this;\n                that._zonePickerLabel = $('<li class=\\'k-item k-zonepicker\\' style=\\'display:none\\'>' + '<label class=\\'k-label\\'><span class=\\'k-item-title\\'></span>' + '<div></div>' + '</label>' + '</li>');\n                that._zone = $('<select></select>').appendTo(that._zonePickerLabel.find('div')).change(function () {\n                    that._value = this.value;\n                    that.trigger('change');\n                });\n                this.wrapper.closest('.k-item').after(that._zonePickerLabel);\n                that._bindZones(that._zoneTitle.val());\n                that._value = that._zone[0].value;\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                kendo.destroy(this.wrapper);\n            },\n            value: function (value) {\n                var that = this;\n                var zonePicker = that._zone;\n                var other_zone = '';\n                var zone_value = '';\n                var zone;\n                if (value === undefined) {\n                    return that._value;\n                }\n                zone = that._zonesQuery.filter({\n                    field: 'zone',\n                    operator: 'eq',\n                    value: value\n                }).data[0];\n                if (zone) {\n                    zone_value = zone.zone;\n                    other_zone = zone.other_zone;\n                }\n                that._zoneTitle.val(other_zone);\n                that._bindZones(other_zone);\n                zonePicker.val(zone_value);\n                zone_value = zonePicker[0].value;\n                if (zone_value && zonePicker.children.length > 1) {\n                    that._zonePickerLabel.show();\n                } else {\n                    that._zonePickerLabel.hide();\n                }\n                that._value = zone_value;\n            }\n        });\n        ui.plugin(MobileTimezoneEditor);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}