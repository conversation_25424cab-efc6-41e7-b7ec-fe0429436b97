/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.pivot.configurator.min",["kendo.dom.min"],e)}(function(){return function(e,t){function s(e){for(var t,s=0,a=e.length;s<a;s++)if(2==e[s].type){t=!0;break}t&&e.splice(s+1,0,{caption:"KPIs",defaultHierarchy:"[KPIs]",name:"KPIs",uniqueName:"[KPIs]"})}function a(e){return{name:e.uniqueName,type:e.type}}function i(e){for(var t=0,s=e.length;t<s;t++)e[t].uniqueName=e[t].name,e[t].type="kpi";return e}function r(t){var s=e(t).closest(".k-pivot-setting");return s.length?s.data("kendoPivotSettingTarget"):null}function n(e,t,s){return{hierarchyUniqueName:e,uniqueName:t,caption:t,measure:t,name:t,type:s,kpi:!0}}function o(e){var t=e.name;return[n(t,e.value,"value"),n(t,e.goal,"goal"),n(t,e.status,"status"),n(t,e.trend,"trend")]}var u=window.kendo,l=u.ui,c=l.Widget,d=".kendoPivotConfigurator",p="mouseenter"+d+" mouseleave"+d,h=u.template('<p class="k-reset"><span class="k-icon #=icon#"></span>${name}</p><div class="k-list-container k-reset"/>'),m=c.extend({init:function(e,t){c.fn.init.call(this,e,t),this.element.addClass("k-widget k-fieldselector k-alt k-edit-form-container"),this._dataSource(),this._layout(),this.refresh(),u.notify(this)},events:[],options:{name:"PivotConfigurator",filterable:!1,sortable:!1,messages:{measures:"Drop Data Fields Here",columns:"Drop Column Fields Here",rows:"Drop Rows Fields Here",measuresLabel:"Measures",columnsLabel:"Columns",rowsLabel:"Rows",fieldsLabel:"Fields"}},_dataSource:function(){var t=this;t.dataSource&&t._refreshHandler?t.dataSource.unbind("change",t._refreshHandler).unbind("error",t._errorHandler).unbind("progress",t._progressHandler):(t._errorHandler=e.proxy(t._error,t),t._refreshHandler=e.proxy(t.refresh,t),t._progressHandler=e.proxy(t._requestStart,t)),t.dataSource=u.data.PivotDataSource.create(t.options.dataSource),t.dataSource.bind("change",t._refreshHandler).bind("error",t._errorHandler).bind("progress",t._progressHandler)},setDataSource:function(e){this.options.dataSource=e,this._dataSource(),this.measures&&this.measures.setDataSource(e),this.rows&&this.rows.setDataSource(e),this.columns&&this.columns.setDataSource(e),this.refresh()},_treeViewDataSource:function(){var t=this;return u.data.HierarchicalDataSource.create({schema:{model:{id:"uniqueName",hasChildren:function(e){return!("hierarchyUniqueName"in e||"aggregator"in e)}}},transport:{read:function(a){var r,n,u;e.isEmptyObject(a.data)?(r=t.dataSource.schemaDimensions(),r.done(function(e){t.dataSource.cubeBuilder||s(e),a.success(e)}).fail(a.error)):(n=t.treeView.dataSource.get(a.data.uniqueName),"[KPIs]"===n.uniqueName?(u=!0,r=t.dataSource.schemaKPIs(),r.done(function(e){a.success(i(e))}).fail(a.error)):"kpi"==n.type&&(u=!0,a.success(o(n))),u||(r=2==n.type?t.dataSource.schemaMeasures():n.dimensionUniqueName?t.dataSource.schemaLevels(a.data.uniqueName):t.dataSource.schemaHierarchies(a.data.uniqueName),r.done(a.success).fail(a.error)))}}})},_progress:function(e){u.ui.progress(this.element,e)},_error:function(){this._progress(!1)},_requestStart:function(){this._progress(!0)},_layout:function(){this.form=e('<div class="k-columns k-state-default k-floatwrap"/>').appendTo(this.element),this._fields(),this._targets()},_fields:function(){var t=e('<div class="k-state-default"><p class="k-reset"><span class="k-icon k-i-group"></span>'+this.options.messages.fieldsLabel+"</p></div>").appendTo(this.form),s='# if (item.type == 2 || item.uniqueName == "[KPIs]") { #<span class="k-icon k-i-#= (item.type == 2 ? "sum" : "kpi") #"></span># } else if (item.type && item.type !== "kpi") { #<span class="k-icon k-i-arrows-dimensions"></span># } ##: item.caption || item.name #';this.treeView=e("<div/>").appendTo(t).kendoTreeView({template:s,dataTextField:"caption",dragAndDrop:!0,autoBind:!1,dataSource:this._treeViewDataSource(),dragstart:function(e){var t=this.dataItem(e.sourceNode);(t.hasChildren||t.aggregator||t.measure)&&2!=t.type&&"[KPIs]"!==t.uniqueName||e.preventDefault()},drag:function(e){var t="k-i-cancel",s=r(e.dropTarget);s&&s.validate(this.dataItem(e.sourceNode))&&(t="k-i-plus"),e.setStatusClass(t)},drop:function(e){var t,s,i,n,u,l;if(e.preventDefault(),t=r(e.dropTarget),s=this.dataItem(e.sourceNode),t&&t.validate(s)){if(l=s.defaultHierarchy||s.uniqueName,"kpi"===s.type)for(u=o(s),n=u.length,l=[],i=0;i<n;i++)l.push(a(u[i]));else s.kpi&&(l=[a(s)]);t.add(l)}}}).data("kendoTreeView")},_createTarget:function(t,s){var a='<li class="k-item k-header" data-'+u.ns+'name="${data.name}">${data.name}',i=s.sortable,r="";return i&&(r+="#if (data.sortIcon) {#",r+='<span class="k-icon ${data.sortIcon}-sm"></span>',r+="#}#"),(s.filterable||i)&&(r+='<span class="k-icon k-i-more-vertical k-setting-fieldmenu"></span>'),r+='<span class="k-icon k-i-close k-setting-delete"></span>',a+='<span class="k-field-actions">'+r+"</span></li>",new u.ui.PivotSettingTarget(t,e.extend({dataSource:this.dataSource,hint:function(t){var s=e('<div class="k-fieldselector"><ul class="k-list k-reset"></ul></div>');return s.find(".k-list").append(t.clone()),s},template:a,emptyTemplate:'<li class="k-item k-empty">${data}</li>'},s))},_targets:function(){var t=e('<div class="k-state-default"/>').appendTo(this.form),s=e(h({name:this.options.messages.columnsLabel,icon:"k-i-columns"})).appendTo(t),a=e('<ul class="k-pivot-configurator-settings k-list k-reset" />').appendTo(s.last()),i=e(h({name:this.options.messages.rowsLabel,icon:"k-i-rows"})).appendTo(t),r=e('<ul class="k-pivot-configurator-settings k-list k-reset" />').appendTo(i.last()),n=e(h({name:this.options.messages.measuresLabel,icon:"k-i-sum"})).appendTo(t),o=e('<ul class="k-pivot-configurator-settings k-list k-reset" />').appendTo(n.last()),u=this.options;this.columns=this._createTarget(a,{filterable:u.filterable,sortable:u.sortable,connectWith:r,messages:{empty:u.messages.columns,fieldMenu:u.messages.fieldMenu}}),this.rows=this._createTarget(r,{filterable:u.filterable,sortable:u.sortable,setting:"rows",connectWith:a,messages:{empty:this.options.messages.rows,fieldMenu:this.options.messages.fieldMenu}}),this.measures=this._createTarget(o,{setting:"measures",messages:{empty:u.messages.measures}}),a.add(r).add(o).on(p,".k-item:not(.k-empty)",this._toggleHover)},_toggleHover:function(t){e(t.currentTarget).toggleClass("k-state-hover","mouseenter"===t.type)},_resize:function(){var e,t,s=this.element,a=this.options.height,i=u._outerHeight;a&&(s.height(a),s.is(":visible")&&(t=s.children(".k-columns").children("div.k-state-default"),a=s.innerHeight(),e=(i(s)-a)/2,a=a-(i(t,!0)-t.height())-e,t.height(a)))},refresh:function(){var e=this.dataSource;(e.cubeBuilder||this._cube!==e.cube()||this._catalog!==e.catalog())&&this.treeView.dataSource.fetch(),this._catalog=this.dataSource.catalog(),this._cube=this.dataSource.cube(),this._resize(),this._progress(!1)},destroy:function(){c.fn.destroy.call(this),this.dataSource.unbind("change",this._refreshHandler),this.form.find(".k-list").off(d),this.rows.destroy(),this.columns.destroy(),this.measures.destroy(),this.treeView.destroy(),this.element=null,this._refreshHandler=null}});l.plugin(m)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,s){(s||t)()});
//# sourceMappingURL=kendo.pivot.configurator.min.js.map
