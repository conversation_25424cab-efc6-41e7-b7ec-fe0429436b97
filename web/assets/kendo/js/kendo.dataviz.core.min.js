/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function i(t){return(t+"").replace(a,l)}function n(t){var i,n=[];for(i in t)n.push(i+t[i]);return n.sort().join("")}function e(t){var i,n=2166136261;for(i=0;i<t.length;++i)n+=(n<<1)+(n<<4)+(n<<7)+(n<<8)+(n<<24),n^=t.charCodeAt(i);return n>>>0}function o(){return{width:0,height:0,baseline:0}}function s(t,i,n){return c.current.measure(t,i,n)}var r,a,l,h,u,c;window.kendo.util=window.kendo.util||{},r=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,i){var n=this._map,e={key:t,value:i};n[t]=e,this._head?(this._tail.newer=e,e.older=this._tail,this._tail=e):this._head=this._tail=e,this._length>=this._size?(n[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var i=this._map[t];if(i)return i===this._head&&i!==this._tail&&(this._head=i.newer,this._head.older=null),i!==this._tail&&(i.older&&(i.older.newer=i.newer,i.newer.older=i.older),i.older=this._tail,i.newer=null,this._tail.newer=i,this._tail=i),i.value}}),a=/\r?\n|\r|\t/g,l=" ",h={baselineMarkerSize:1},"undefined"!=typeof document&&(u=document.createElement("div"),u.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),c=kendo.Class.extend({init:function(i){this._cache=new r(1e3),this.options=t.extend({},h,i)},measure:function(t,s,r){var a,l,h,c,f,d,p,m,g;if(void 0===r&&(r={}),!t)return o();if(a=n(s),l=e(t+a),h=this._cache.get(l))return h;c=o(),f=r.box||u,d=this._baselineMarker().cloneNode(!1);for(p in s)m=s[p],void 0!==m&&(f.style[p]=m);return g=r.normalizeText!==!1?i(t):t+"",f.textContent=g,f.appendChild(d),document.body.appendChild(f),g.length&&(c.width=f.offsetWidth-this.options.baselineMarkerSize,c.height=f.offsetHeight,c.baseline=d.offsetTop+this.options.baselineMarkerSize),c.width>0&&c.height>0&&this._cache.put(l,c),f.parentNode.removeChild(f),c},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),c.current=new c,kendo.deepExtend(kendo.util,{LRUCache:r,TextMetrics:c,measureText:s,objectKey:n,hashKey:e,normalizeText:i})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,i,n){(n||i)()}),function(t,define){define("dataviz/core/kendo-core.min",["kendo.core.min","kendo.drawing.min"],t)}(function(){!function(t){function i(t){return Array.isArray(t)}function n(t,n){var e,o,s=i(n)?n:[n];for(e=0;e<s.length;e++)o=s[e],t.className.indexOf(o)===-1&&(t.className+=" "+o)}function e(t,i){t&&t.className&&(t.className=t.className.replace(i,"").replace(vi," "))}function o(t){var i,n=.5;for(t.options.stroke&&kendo.drawing.util.defined(t.options.stroke.width)&&t.options.stroke.width%2===0&&(n=0),i=0;i<t.segments.length;i++)t.segments[i].anchor().round(0).translate(n,n);return t}function s(t,i){return-t.x*i.y+t.y*i.x<0}function r(t){return"number"==typeof t&&!isNaN(t)}function a(t){return typeof t===hi}function l(t){return r(t)||a(t)&&isFinite(t)}function h(t){return"object"==typeof t}function u(t){return r(t)?t+"px":t}function c(t){return bi.test(t)}function f(t,n){var e,o,s,r,l,f=a(n)?[n]:n;if(i(f)){for(e={},o=window.getComputedStyle(t),s=0;s<f.length;s++)r=f[s],e[r]=c(r)?parseFloat(o[r]):o[r];return e}if(h(n))for(l in n)t.style[l]=u(n[l])}function d(t,i){void 0===i&&(i=0);var n={top:0,right:0,bottom:0,left:0};return"number"==typeof t?n[ui]=n[ai]=n[Vt]=n[Zt]=t:(n[ui]=t[ui]||i,n[ai]=t[ai]||i,n[Vt]=t[Vt]||i,n[Zt]=t[Zt]||i),n}function p(t){void 0===t&&(t={});var i;return t.template?t.template=i=ji.compile(t.template):Bt(t.content)&&(i=t.content),i}function m(t,i){var n,e=t.length,o=[];for(n=0;n<e;n++)i(t[n])&&o.push(t[n]);return o}function g(t,i){var n,e;if(t.className)for(n=i.split(" "),e=0;e<n.length;e++)if(t.className.indexOf(n[e])!==-1)return!0}function x(t,i){if(i)return i.indexOf(t)!==-1}function v(t,i,n){return kendo.drawing.util.round(t+(i-t)*n,Dt)}function b(t,i){var n,e,o=t.length,s=[];for(n=0;n<o;n++)e=i(t[n]),kendo.drawing.util.defined(e)&&s.push(e);return s}function y(t){var i=0;return t.wheelDelta&&(i=-t.wheelDelta/120,i=i>0?Math.ceil(i):Math.floor(i)),t.detail&&(i=kendo.drawing.util.round(t.detail/3)),i}function w(t,i){var n=t.prototype;n.options=n.options?It({},n.options,i):i}function M(t){var i,n,e,o=$t,s=ti;for(i=0,n=t.length;i<n;i++)e=t[i],null!==e&&isFinite(e)&&(o=Math.min(o,e),s=Math.max(s,e));return{min:o===$t?void 0:o,max:s===ti?void 0:s}}function k(t,i){var n,e,o,s=qi(i-t,Yt-1);if(0===s){if(0===i)return.1;s=Math.abs(i)}return n=Math.pow(10,Math.floor(Math.log(s)/Math.log(10))),e=qi(s/n,Yt),o=1,o=e<1.904762?.2:e<4.761904?.5:e<9.523809?1:2,qi(n*o,Yt)}function T(t,i,n,e,o){var s=Xi(o);return new Ji(n+(t-n)*Math.cos(s)+(i-e)*Math.sin(s),e-(t-n)*Math.sin(s)+(i-e)*Math.cos(s))}function S(t,i){return t-i}function R(t,i){var n,e,o,s,r,a,l,h,u;return t.x1===i.x1&&t.y1===i.y1&&t.x2===i.x2&&t.y2===i.y2?i:(n=Math.min(t.x1,i.x1),e=Math.max(t.x1,i.x1),o=Math.min(t.x2,i.x2),s=Math.max(t.x2,i.x2),r=Math.min(t.y1,i.y1),a=Math.max(t.y1,i.y1),l=Math.min(t.y2,i.y2),h=Math.max(t.y2,i.y2),u=[],u[0]=new Zi(e,r,o,a),u[1]=new Zi(n,a,e,l),u[2]=new Zi(o,a,s,l),u[3]=new Zi(e,l,o,h),t.x1===n&&t.y1===r||i.x1===n&&i.y1===r?(u[4]=new Zi(n,r,e,a),u[5]=new Zi(o,l,s,h)):(u[4]=new Zi(o,r,s,a),u[5]=new Zi(n,l,e,h)),m(u,function(t){return t.height()>0&&t.width()>0})[0])}function U(i){var n,e,o=i.stops,s=i.innerRadius/i.radius*100,r=o.length,a=[];for(n=0;n<r;n++)e=t.extend({},o[n]),e.offset=(e.offset*(100-s)+s)/100,a.push(e);return a}function L(t){var i=t.origin,n=t.bottomRight();return new Zi(i.x,i.y,n.x,n.y)}function j(t,i){var n=t.tickX,e=t.tickY,s=t.position,r=new Mt({stroke:{width:i.width,color:i.color}});return t.vertical?r.moveTo(n,s).lineTo(n+i.size,s):r.moveTo(s,e).lineTo(s,e+i.size),o(r),r}function A(t,i){var n=t.lineStart,e=t.lineEnd,s=t.position,r=new Mt({stroke:{width:i.width,color:i.color,dashType:i.dashType}});return t.vertical?r.moveTo(n,s).lineTo(e,s):r.moveTo(s,n).lineTo(s,e),o(r),r}function I(t,i){var n=t.getTime()-i,e=t.getTimezoneOffset()-i.getTimezoneOffset();return n-e*Bn}function B(t,i){return new Date(t.getTime()+i)}function C(t){var i;return t instanceof Date?i=t:t&&(i=new Date(t)),i}function P(t,i){var n,e;if(void 0===i&&(i=0),n=0,e=t.getDay(),!isNaN(e))for(;e!==i;)0===e?e=6:e--,n++;return B(t,-n*Pn)}function _(t,i){return 0===i&&23===t.getHours()&&(t.setHours(t.getHours()+2),!0)}function z(t,i){var n,e=new Date(t);return e.setMinutes(0,0,0),n=(t.getTimezoneOffset()-e.getTimezoneOffset())*Bn,B(e,n+i*Cn)}function V(t,i,n,e){var o,s,r=t;return t&&(o=C(t),s=o.getHours(),n===jn?(r=new Date(o.getFullYear()+i,0,1),_(r,0)):n===Ln?(r=new Date(o.getFullYear(),o.getMonth()+i,1),_(r,s)):n===Un?(r=V(P(o,e),7*i,Rn),_(r,s)):n===Rn?(r=new Date(o.getFullYear(),o.getMonth(),o.getDate()+i),_(r,s)):n===Sn?r=z(o,i):n===Tn?(r=B(o,i*Bn),r.getSeconds()>0&&r.setSeconds(0)):n===kn?r=B(o,i*In):n===Mn&&(r=B(o,i)),n!==Mn&&r.getMilliseconds()>0&&r.setMilliseconds(0)),r}function E(t,i,n){return V(C(t),0,i,n)}function O(t,i,n){var e=C(t);return e&&E(e,i,n).getTime()===e.getTime()?e:V(e,1,i,n)}function D(t,i){return t&&i?t.getTime()-i.getTime():-1}function G(t,i){return t.getTime()-i}function F(t){var n,e;if(i(t)){for(n=[],e=0;e<t.length;e++)n.push(F(t[e]));return n}if(t)return C(t).getTime()}function N(t,i){return t&&i?F(t)===F(i):t===i}function H(t,i,n){return I(t,i)/En[n]}function Y(t,i,n,e){var o,s=C(t),r=C(i);return o=n===Ln?s.getMonth()-r.getMonth()+12*(s.getFullYear()-r.getFullYear())+H(s,new Date(s.getFullYear(),s.getMonth()),Rn)/new Date(s.getFullYear(),s.getMonth()+1,0).getDate():n===jn?s.getFullYear()-r.getFullYear()+Y(s,new Date(s.getFullYear(),0),Ln,1)/12:n===Rn||n===Un?H(s,r,n):G(s,i)/En[n],o/e}function X(t,i,n){var e;return e=n===jn?i.getFullYear()-t.getFullYear():n===Ln?12*X(t,i,jn)+i.getMonth()-t.getMonth():n===Rn?Math.floor(G(i,t)/Pn):Math.floor(G(i,t)/En[n])}function q(t,i){for(var n,e,o=0,s=i.length-1;o<=s;)if(n=Math.floor((o+s)/2),e=i[n],e<t)o=n+1;else{if(!(e>t)){for(;N(i[n-1],t);)n--;return n}s=n-1}return i[n]<=t?n:n-1}function K(t,i){var n;return n=a(i)?t.parseDate(i)||C(i):C(i)}function W(t,n){var e,o;if(i(n)){for(e=[],o=0;o<n.length;o++)e.push(K(t,n[o]));return e}return K(t,n)}function Q(t,i){var n,e;if(t instanceof Date){for(n=i.length,e=0;e<n;e++)if(N(i[e],t))return e;return-1}return i.indexOf(t)}function J(t){var i=t._range;return i||(i=t._range=M(t),i.min=C(i.min),i.max=C(i.max)),i}function Z(t,i,n){for(var e,o,s,r=J(t.categories),a=(t.max||r.max)-(t.min||r.min),l=t.autoBaseUnitSteps,h=t.maxDateGroups,u=t.baseUnit===Xn,c=i?Yn.indexOf(i):0,f=u?Yn[c++]:t.baseUnit,d=a/En[f],p=d;!o||d>=h;){e=e||l[f].slice(0);do s=e.shift();while(s&&i===f&&s<n);if(s)o=s,d=p/o;else{if(f===Ni(Yn)){o=Math.ceil(p/h);break}if(!u){d>h&&(o=Math.ceil(p/h));break}f=Yn[c++]||Ni(Yn),p=a/En[f],e=null}}t.baseUnitStep=o,t.baseUnit=f}function $(t){var i,n,e,o,s,r=t.categories,a=Vi(r)?r.length:0,l=$t;for(e=0;e<a;e++)o=r[e],o&&i&&(s=I(o,i),s>0&&(l=Math.min(l,s),n=l>=Vn?jn:l>=zn-3*Pn?Ln:l>=_n?Un:l>=Pn?Rn:l>=Cn?Sn:l>=Bn?Tn:kn)),i=o;t.baseUnit=n||Rn}function tt(t){var i=(t.baseUnit||"").toLowerCase(),n=i!==Xn&&!x(i,Yn);return n&&$(t),i!==Xn&&t.baseUnitStep!==Hn||Z(t),t}function it(t,i,n){var e,o,s;if(!t&&!i)return 0;if(t>=0&&i>=0){if(o=t===i?0:t,s=(i-o)/i,n===!1||!n&&s>Nn)return 0;e=Math.max(0,o-(i-o)/2)}else e=t;return e}function nt(t,i,n){var e,o,s;if(!t&&!i)return 1;if(t<=0&&i<=0){if(o=t===i?0:i,s=Math.abs((o-t)/o),n===!1||!n&&s>Nn)return 0;e=Math.min(0,o-(t-o)/2)}else e=i;return e}function et(t,i){return qi(Math.floor(t/i)*i,Yt)}function ot(t,i){return qi(Math.ceil(t/i)*i,Yt)}function st(t){return Math.max(Math.min(t,Gn),-Gn)}function rt(t,i,n){var e=n.narrowRange,o=it(t,i,e),s=nt(t,i,e),r=k(o,s),a={majorUnit:r};return n.roundToMajorUnit!==!1&&(o<0&&ut(o,r,1/3)&&(o-=r),s>0&&ut(s,r,1/3)&&(s+=r)),a.min=et(o,r),a.max=ot(s,r),a}function at(t,i){return{min:Vi(i.min)?Math.min(t.min,i.min):t.min,max:Vi(i.max)?Math.max(t.max,i.max):t.max,majorUnit:t.majorUnit}}function lt(t,i){var n,e;for(n=0;n<i.length;n++)e=i[n],null===t[e]&&(t[e]=void 0)}function ht(t,i){var n,e,o,s,r=i;return i&&(lt(i,["min","max"]),n=Vi(i.min),e=Vi(i.max),o=n||e,o&&i.min===i.max&&(i.min>0?i.min=0:i.max=1),i.majorUnit?(t.min=et(t.min,i.majorUnit),t.max=ot(t.max,i.majorUnit)):o&&(r=It(t,i),t.majorUnit=k(r.min,r.max))),t.minorUnit=(r.majorUnit||t.majorUnit)/5,s=It(t,r),s.min>=s.max&&(n&&!e?s.max=s.min+s.majorUnit:!n&&e&&(s.min=s.max-s.majorUnit)),s}function ut(t,i,n){var e=qi(Math.abs(t%i),Yt),o=i*(1-n);return 0===e||e>o}function ct(t){var i=Sn;return t>=Vn?i=jn:t>=zn?i=Ln:t>=_n?i=Un:t>=Pn&&(i=Rn),i}function ft(t,i,n){var e=n.min||t,o=n.max||i,s=n.baseUnit||(o&&e?ct(I(o,e)):Sn),r=En[s],a=E(F(e)-1,s)||C(o),l=O(F(o)+1,s),h=n.majorUnit?n.majorUnit:void 0,u=h||ot(k(a.getTime(),l.getTime()),r)/r,c=X(a,l,s),f=ot(c,u),d=f-c,p=Math.floor(d/2),m=d-p;return n.baseUnit||delete n.baseUnit,n.baseUnit=n.baseUnit||s,n.min=n.min||V(a,-p,s),n.max=n.max||V(l,m,s),n.minorUnit=n.minorUnit||u/5,n.majorUnit=u,n}function dt(t,i,n,e){var o=n.min,s=n.max;return Vi(n.axisCrossingValue)&&n.axisCrossingValue<=0&&gt(),Vi(e.max)?e.max<=0&&gt():s=i,Vi(e.min)?e.min<=0&&gt():o=t,{min:o,max:s}}function pt(t,i,n){var e=n.majorUnit,o=t;return t<=0?o=i<=1?Math.pow(e,-2):1:n.narrowRange||(o=Math.pow(e,Math.floor(xt(t,e)))),o}function mt(t,i){var n,e=qi(xt(t,i),Yt)%1;return n=t<=0?i:0!==e&&(e<.3||e>.9)?Math.pow(i,xt(t,i)+.2):Math.pow(i,Math.ceil(xt(t,i)))}function gt(){throw Error("Non positive values cannot be used for a logarithmic axis")}function xt(t,i){return Math.log(t)/Math.log(i)}function vt(t,i){return 180-Math.abs(Math.abs(t-i)-180)}function bt(t){return t<=0?-1:1}var yt,wt,Mt,kt,Tt,St,Rt,Ut,Lt,jt,At,It,Bt,Ct,Pt,_t,zt,Vt,Et,Ot,Dt,Gt,Ft,Nt,Ht,Yt,Xt,qt,Kt,Wt,Qt,Jt,Zt,$t,ti,ii,ni,ei,oi,si,ri,ai,li,hi,ui,ci,fi,di,pi,mi,gi,xi,vi,bi,yi,wi,Mi,ki,Ti,Si,Ri,Ui,Li,ji,Ai,Ii,Bi,Ci,Pi,_i,zi,Vi,Ei,Oi,Di,Gi,Fi,Ni,Hi,Yi,Xi,qi,Ki,Wi,Qi,Ji,Zi,$i,tn,nn,en,on,sn,rn,an,ln,hn,un,cn,fn,dn,pn,mn,gn,xn,vn,bn,yn,wn,Mn,kn,Tn,Sn,Rn,Un,Ln,jn,An,In,Bn,Cn,Pn,_n,zn,Vn,En,On,Dn,Gn,Fn,Nn,Hn,Yn,Xn,qn,Kn,Wn,Qn,Jn,Zn,$n,te,ie,ne,ee,oe,se,re,ae,le,he;window.kendo.dataviz=window.kendo.dataviz||{},yt=kendo.drawing,wt=yt.util,Mt=yt.Path,kt=yt.Group,Tt=kendo.Class,St=kendo.geometry,Rt=St.Rect,Ut=St.Circle,Lt=St.transform,jt=St.Segment,At=kendo.dataviz,It=kendo.deepExtend,Bt=kendo.isFunction,Ct=kendo.getter,Pt="arc",_t="axisLabelClick",zt="#000",Vt="bottom",Et="center",Ot="circle",Dt=3,Gt="cross",Ft="date",Nt="12px sans-serif",Ht=400,Yt=10,Xt=600,qt="end",Kt=/\{\d+:?/,Wt="height",Qt=100,Jt="inside",Zt="left",$t=Number.MAX_VALUE,ti=-Number.MAX_VALUE,ii="none",ni="noteClick",ei="noteHover",oi="noteLeave",si="object",ri="outside",ai="right",li="start",hi="string",ui="top",ci="triangle",fi="value",di="#fff",pi="width",mi="x",gi="y",xi={ARC:Pt,AXIS_LABEL_CLICK:_t,BLACK:zt,BOTTOM:Vt,CENTER:Et,CIRCLE:Ot,COORD_PRECISION:Dt,CROSS:Gt,DATE:Ft,DEFAULT_FONT:Nt,DEFAULT_HEIGHT:Ht,DEFAULT_PRECISION:Yt,DEFAULT_WIDTH:Xt,END:qt,FORMAT_REGEX:Kt,HEIGHT:Wt,HIGHLIGHT_ZINDEX:Qt,INSIDE:Jt,LEFT:Zt,MAX_VALUE:$t,MIN_VALUE:ti,NONE:ii,NOTE_CLICK:ni,NOTE_HOVER:ei,NOTE_LEAVE:oi,OBJECT:si,OUTSIDE:ri,RIGHT:ai,START:li,STRING:hi,TOP:ui,TRIANGLE:ci,VALUE:fi,WHITE:di,WIDTH:pi,X:mi,Y:gi},vi=/\s+/g,bi=/width|height|top|left|bottom|right/i,yi={format:function(t,i){return i},toString:function(t){return t},parseDate:function(t){return new Date(t)}},wi=yi,Mi=Tt.extend({}),Mi.register=function(t){wi=t},Object.defineProperties&&Object.defineProperties(Mi,{implementation:{get:function(){return wi}}}),ki=/\{(\d+)(:[^\}]+)?\}/g,Ti=Tt.extend({init:function(t){this._intlService=t},auto:function(t){for(var i,n=[],e=arguments.length-1;e-- >0;)n[e]=arguments[e+1];return i=this.intl,a(t)&&t.match(Kt)?i.format.apply(i,[t].concat(n)):i.toString(n[0],t)},localeAuto:function(t,i,n){var e,o=this.intl;return e=a(t)&&t.match(Kt)?t.replace(ki,function(t,e,s){var r=i[parseInt(e,10)];return o.toString(r,s?s.substring(1):"",n)}):o.toString(i[0],t,n)}}),Object.defineProperties&&Object.defineProperties(Ti.fn,{intl:{get:function(){return this._intlService||Mi.implementation},set:function(t){this._intlService=t}}}),Si=Tt.extend({init:function(t,i){void 0===i&&(i={}),this._intlService=i.intlService,this.sender=i.sender||t,this.format=new Ti(i.intlService),this.chart=t,this.rtl=!!i.rtl},notify:function(t,i){this.chart&&this.chart.trigger(t,i)},isPannable:function(t){var i=((this.chart||{}).options||{}).pannable;return i&&i.lock!==t}}),Object.defineProperties&&Object.defineProperties(Si.fn,{intl:{get:function(){return this._intlService||Mi.implementation},set:function(t){this._intlService=t,this.format.intl=t}}}),Ui=Tt.extend({}),Ui.register=function(t){Ri=t},Ui.create=function(t,i){if(Ri)return Ri.create(t,i)},Li={compile:function(t){return t}},ji=Tt.extend({}),ji.register=function(t){Li=t},ji.compile=function(t){return Li.compile(t)},Ai={ChartService:Si,DomEventsBuilder:Ui,FormatService:Ti,IntlService:Mi,TemplateService:ji},Ii=function(){this._map={}},Ii.prototype.get=function(t){return this._map[this._key(t)]},Ii.prototype.set=function(t,i){this._map[this._key(t)]=i},Ii.prototype._key=function(t){return t instanceof Date?t.getTime():t},Bi="trigger",Ci=Tt.extend({init:function(t,i){this.observer=t,this.handlerMap=It({},this.handlerMap,i)},trigger:function(t,i){var n,e=this,o=e.observer,s=e.handlerMap;return s[t]?n=this.callObserver(s[t],i):o[Bi]&&(n=this.callObserver(Bi,t,i)),n},callObserver:function(t){for(var i=[],n=arguments.length-1;n-- >0;)i[n]=arguments[n+1];return this.observer[t].apply(this.observer,i)},requiresHandlers:function(t){var i,n=this;if(this.observer.requiresHandlers)return this.observer.requiresHandlers(t);for(i=0;i<t.length;i++)if(n.handlerMap[t[i]])return!0}}),Pi=kendo.drawing.util,_i=Pi.append,zi=Pi.bindEvents,Vi=Pi.defined,Ei=Pi.deg,Oi=Pi.elementOffset,Di=Pi.elementSize,Gi=Pi.eventElement,Fi=Pi.eventCoordinates,Ni=Pi.last,Hi=Pi.limitValue,Yi=Pi.objectKey,Xi=Pi.rad,qi=Pi.round,Ki=Pi.unbindEvents,Wi=Pi.valueOrDefault,Qi=Tt.extend({}),Qi.fetchFonts=function(t,i,n){void 0===n&&(n={depth:0});var e=5;!t||n.depth>e||!document.fonts||Object.keys(t).forEach(function(e){var o=t[e];"dataSource"!==e&&"$"!==e[0]&&o&&("font"===e?i.push(o):"object"==typeof o&&(n.depth++,Qi.fetchFonts(o,i,n),n.depth--))})},Qi.loadFonts=function(t,i){var n=[];if(t.length>0&&document.fonts){try{n=t.map(function(t){return document.fonts.load(t)})}catch(e){kendo.logToConsole(e)}Promise.all(n).then(i,i)}else i()},Qi.preloadFonts=function(t,i){var n=[];Qi.fetchFonts(t,n),Qi.loadFonts(n,i)},Ji=Tt.extend({init:function(t,i){this.x=t||0,this.y=i||0},clone:function(){return new Ji(this.x,this.y)},equals:function(t){return t&&this.x===t.x&&this.y===t.y},rotate:function(t,i){var n=Xi(i),e=Math.cos(n),o=Math.sin(n),s=t.x,r=t.y,a=this,l=a.x,h=a.y;return this.x=qi(s+(l-s)*e+(h-r)*o,Dt),this.y=qi(r+(h-r)*e-(l-s)*o,Dt),this},multiply:function(t){return this.x*=t,this.y*=t,this},distanceTo:function(t){var i=this.x-t.x,n=this.y-t.y;return Math.sqrt(i*i+n*n)}}),Ji.onCircle=function(t,i,n){var e=Xi(i);return new Ji(t.x-n*Math.cos(e),t.y-n*Math.sin(e))},Zi=Tt.extend({init:function(t,i,n,e){this.x1=t||0,this.y1=i||0,this.x2=n||0,this.y2=e||0},equals:function(t){return this.x1===t.x1&&this.x2===t.x2&&this.y1===t.y1&&this.y2===t.y2},width:function(){return this.x2-this.x1},height:function(){return this.y2-this.y1},translate:function(t,i){return this.x1+=t,this.x2+=t,this.y1+=i,this.y2+=i,this},move:function(t,i){var n=this.height(),e=this.width();return Vi(t)&&(this.x1=t,this.x2=this.x1+e),Vi(i)&&(this.y1=i,this.y2=this.y1+n),this},wrap:function(t){return this.x1=Math.min(this.x1,t.x1),this.y1=Math.min(this.y1,t.y1),this.x2=Math.max(this.x2,t.x2),this.y2=Math.max(this.y2,t.y2),this},wrapPoint:function(t){var n=i(t),e=n?t[0]:t.x,o=n?t[1]:t.y;return this.wrap(new Zi(e,o,e,o)),this},snapTo:function(t,i){return i!==mi&&i||(this.x1=t.x1,this.x2=t.x2),i!==gi&&i||(this.y1=t.y1,this.y2=t.y2),this},alignTo:function(t,i){var n,e,o=this.height(),s=this.width(),r=i===ui||i===Vt?gi:mi,a=r===gi?o:s;return i===Et?(n=t.center(),e=this.center(),this.x1+=n.x-e.x,this.y1+=n.y-e.y):this[r+1]=i===ui||i===Zt?t[r+1]-a:t[r+2],this.x2=this.x1+s,this.y2=this.y1+o,this},shrink:function(t,i){return this.x2-=t,this.y2-=i,this},expand:function(t,i){return this.shrink(-t,-i),this},pad:function(t){var i=d(t);return this.x1-=i.left,this.x2+=i.right,this.y1-=i.top,this.y2+=i.bottom,this},unpad:function(t){var i=d(t);return i.left=-i.left,i.top=-i.top,i.right=-i.right,i.bottom=-i.bottom,this.pad(i)},clone:function(){return new Zi(this.x1,this.y1,this.x2,this.y2)},center:function(){return new Ji(this.x1+this.width()/2,this.y1+this.height()/2)},containsPoint:function(t){return t.x>=this.x1&&t.x<=this.x2&&t.y>=this.y1&&t.y<=this.y2},points:function(){return[new Ji(this.x1,this.y1),new Ji(this.x2,this.y1),new Ji(this.x2,this.y2),new Ji(this.x1,this.y2)]},getHash:function(){return[this.x1,this.y1,this.x2,this.y2].join(",")},overlaps:function(t){return!(t.y2<this.y1||this.y2<t.y1||t.x2<this.x1||this.x2<t.x1)},rotate:function(t){var i=this.width(),n=this.height(),e=this.center(),o=e.x,s=e.y,r=T(0,0,o,s,t),a=T(i,0,o,s,t),l=T(i,n,o,s,t),h=T(0,n,o,s,t);return i=Math.max(r.x,a.x,l.x,h.x)-Math.min(r.x,a.x,l.x,h.x),n=Math.max(r.y,a.y,l.y,h.y)-Math.min(r.y,a.y,l.y,h.y),this.x2=this.x1+i,this.y2=this.y1+n,this},toRect:function(){return new Rt([this.x1,this.y1],[this.width(),this.height()])},hasSize:function(){return 0!==this.width()&&0!==this.height()},align:function(t,i,n){var e=i+1,o=i+2,s=i===mi?pi:Wt,r=this[s]();x(n,[Zt,ui])?(this[e]=t[e],this[o]=this[e]+r):x(n,[ai,Vt])?(this[o]=t[o],this[e]=this[o]-r):n===Et&&(this[e]=t[e]+(t[s]()-r)/2,this[o]=this[e]+r)}}),$i=Tt.extend({init:function(t,i,n,e,o){this.center=t,this.innerRadius=i,this.radius=n,this.startAngle=e,this.angle=o},clone:function(){return new $i(this.center,this.innerRadius,this.radius,this.startAngle,this.angle)},middle:function(){return this.startAngle+this.angle/2},setRadius:function(t,i){return i?this.innerRadius=t:this.radius=t,this},point:function(t,i){var n=Xi(t),e=Math.cos(n),o=Math.sin(n),s=i?this.innerRadius:this.radius,r=qi(this.center.x-e*s,Dt),a=qi(this.center.y-o*s,Dt);return new Ji(r,a)},adjacentBox:function(t,i,n){var e=this.clone().expand(t),o=e.middle(),s=e.point(o),r=i/2,a=n/2,l=Math.sin(Xi(o)),h=Math.cos(Xi(o)),u=s.x-r,c=s.y-a;return Math.abs(l)<.9&&(u+=r*-h/Math.abs(h)),Math.abs(h)<.9&&(c+=a*-l/Math.abs(l)),new Zi(u,c,u+i,c+n)},containsPoint:function(t){var i=this.center,n=this.innerRadius,e=this.radius,o=this.startAngle,r=this.startAngle+this.angle,a=t.x-i.x,l=t.y-i.y,h=new Ji(a,l),u=this.point(o),c=new Ji(u.x-i.x,u.y-i.y),f=this.point(r),d=new Ji(f.x-i.x,f.y-i.y),p=qi(a*a+l*l,Dt);return(c.equals(h)||s(c,h))&&!s(d,h)&&p>=n*n&&p<=e*e},getBBox:function(){var t,i,n,e=this,o=new Zi($t,$t,ti,ti),s=qi(this.startAngle%360),r=qi((s+this.angle)%360),a=this.innerRadius,l=[0,90,180,270,s,r].sort(S),h=l.indexOf(s),u=l.indexOf(r);for(t=s===r?l:h<u?l.slice(h,u+1):[].concat(l.slice(0,u+1),l.slice(h,l.length)),i=0;i<t.length;i++)n=e.point(t[i]),o.wrapPoint(n),o.wrapPoint(n,a);return a||o.wrapPoint(this.center),o},expand:function(t){return this.radius+=t,this}}),tn=$i.extend({init:function(t,i,n,e){$i.fn.init.call(this,t,0,i,n,e)},expand:function(t){return $i.fn.expand.call(this,t)},clone:function(){return new tn(this.center,this.radius,this.startAngle,this.angle)},setRadius:function(t){return this.radius=t,this}}),nn=.001,en=Tt.extend({createRing:function(t,i){var n,e,o,s,r,a,l=t.startAngle+180,h=t.angle+l;return t.angle>0&&l===h&&(h+=nn),n=new St.Point(t.center.x,t.center.y),e=Math.max(t.radius,0),o=Math.max(t.innerRadius,0),s=new St.Arc(n,{startAngle:l,endAngle:h,radiusX:e,radiusY:e}),r=Mt.fromArc(s,i).close(),o?(s.radiusX=s.radiusY=o,a=s.pointAt(h),r.lineTo(a.x,a.y),r.arc(h,l,o,o,!0)):r.lineTo(n.x,n.y),r}}),en.current=new en,on=Tt.extend({init:function(t){this.children=[],this.options=It({},this.options,this.initUserOptions(t))},initUserOptions:function(t){return t},reflow:function(t){var i,n,e,o=this.children;for(n=0;n<o.length;n++)e=o[n],e.reflow(t),i=i?i.wrap(e.box):e.box.clone();this.box=i||t},destroy:function(){var t,i=this.children;for(this.animation&&this.animation.destroy(),t=0;t<i.length;t++)i[t].destroy()},getRoot:function(){var t=this.parent;return t?t.getRoot():null},getSender:function(){var t=this.getService();if(t)return t.sender},getService:function(){for(var t=this;t;){if(t.chartService)return t.chartService;t=t.parent}},translateChildren:function(t,i){var n,e=this.children,o=e.length;for(n=0;n<o;n++)e[n].box.translate(t,i)},append:function(){var t,i,n=arguments,e=this;for(t=0;t<arguments.length;t++)i=n[t],e.children.push(i),i.parent=e},renderVisual:function(){this.options.visible!==!1&&(this.createVisual(),this.addVisual(),this.renderChildren(),this.createAnimation(),this.renderComplete())},addVisual:function(){this.visual&&(this.visual.chartElement=this,this.parent&&this.parent.appendVisual(this.visual))},renderChildren:function(){var t,i=this.children,n=i.length;for(t=0;t<n;t++)i[t].renderVisual()},createVisual:function(){this.visual=new kt({zIndex:this.options.zIndex,visible:Wi(this.options.visible,!0)})},createAnimation:function(){this.visual&&this.options.animation&&(this.animation=yt.Animation.create(this.visual,this.options.animation))},appendVisual:function(t){t.chartElement||(t.chartElement=this),t.options.noclip?this.clipRoot().visual.append(t):Vi(t.options.zIndex)?this.stackRoot().stackVisual(t):this.isStackRoot?this.stackVisual(t):this.visual?this.visual.append(t):this.parent.appendVisual(t)},clipRoot:function(){return this.parent?this.parent.clipRoot():this},stackRoot:function(){return this.parent?this.parent.stackRoot():this},stackVisual:function(t){var i,n,e,o=t.options.zIndex||0,s=this.visual.children,r=s.length;for(i=0;i<r&&(n=s[i],e=Wi(n.options.zIndex,0),!(e>o));i++);this.visual.insert(i,t)},traverse:function(t){var i,n,e=this.children,o=e.length;for(i=0;i<o;i++)n=e[i],t(n),n.traverse&&n.traverse(t)},closest:function(t){for(var i=this,n=!1;i&&!n;)n=t(i),n||(i=i.parent);if(n)return i},renderComplete:function(){},hasHighlight:function(){var t=(this.options||{}).highlight;return!(!this.createHighlight||t&&t.visible===!1)},toggleHighlight:function(i){var n,e=this,o=(this.options||{}).highlight||{},s=o.visual,r=this._highlight;if(!r){if(n={fill:{color:di,opacity:.2},stroke:{color:di,width:1,opacity:.2}},s){if(r=this._highlight=s(t.extend(this.highlightVisualArgs(),{createVisual:function(){return e.createHighlight(n)},sender:this.getSender(),series:this.series,dataItem:this.dataItem,category:this.category,value:this.value,percentage:this.percentage,runningTotal:this.runningTotal,total:this.total})),!r)return}else r=this._highlight=this.createHighlight(n);Vi(r.options.zIndex)||(r.options.zIndex=Wi(o.zIndex,this.options.zIndex)),this.appendVisual(r)}r.visible(i)},createGradientOverlay:function(i,n,e){var o=new Mt(t.extend({stroke:{color:"none"},fill:this.createGradient(e),closed:i.options.closed},n));return o.segments.elements(i.segments.elements()),o},createGradient:function(t){if(this.parent)return this.parent.createGradient(t)}}),on.prototype.options={},sn=on.extend({init:function(t){on.fn.init.call(this,t),this.options.margin=d(this.options.margin),this.options.padding=d(this.options.padding)},reflow:function(t){var i,n,e,o,s=this,r=this.options,a=r.width,l=r.height,h=r.shrinkToFit,u=a&&l,c=r.margin,f=r.padding,d=r.border.width,p=function(){s.align(t,mi,r.align),s.align(t,gi,r.vAlign),s.paddingBox=i.clone().unpad(c).unpad(d)},m=t.clone();for(u&&(m.x2=m.x1+a,m.y2=m.y1+l),h&&m.unpad(c).unpad(d).unpad(f),on.fn.reflow.call(this,m),i=u?this.box=new Zi(0,0,a,l):this.box,h&&u?(p(),m=this.contentBox=this.paddingBox.clone().unpad(f)):(m=this.contentBox=i.clone(),i.pad(f).pad(d).pad(c),p()),this.translateChildren(i.x1-m.x1+c.left+d+f.left,i.y1-m.y1+c.top+d+f.top),n=this.children,e=0;e<n.length;e++)o=n[e],o.reflow(o.box)},align:function(t,i,n){this.box.align(t,i,n)},hasBox:function(){var t=this.options;return t.border.width||t.background},createVisual:function(){on.fn.createVisual.call(this);var t=this.options;t.visible&&this.hasBox()&&this.visual.append(Mt.fromRect(this.paddingBox.toRect(),this.visualStyle()))},visualStyle:function(){var t=this.options,i=t.border||{};return{stroke:{width:i.width,color:i.color,opacity:Wi(i.opacity,t.opacity),dashType:i.dashType},fill:{color:t.background,opacity:t.opacity},cursor:t.cursor}}}),w(sn,{align:Zt,vAlign:ui,margin:{},padding:{},border:{color:zt,width:0},background:"",shrinkToFit:!1,width:0,height:0,visible:!0}),rn=sn.extend({init:function(t,i){sn.fn.init.call(this,t),this.pointData=i},getElement:function(){var t,i,n=this,e=n.options,o=n.paddingBox,s=e.type,r=e.rotation,a=o.center(),l=o.width()/2;return e.visible&&this.hasBox()?(t=this.visualStyle(),s===Ot?i=new yt.Circle(new Ut([qi(o.x1+l,Dt),qi(o.y1+o.height()/2,Dt)],l),t):s===ci?i=Mt.fromPoints([[o.x1+l,o.y1],[o.x1,o.y2],[o.x2,o.y2]],t).close():s===Gt?(i=new yt.MultiPath(t),i.moveTo(o.x1,o.y1).lineTo(o.x2,o.y2),i.moveTo(o.x1,o.y2).lineTo(o.x2,o.y1)):i=Mt.fromRect(o.toRect(),t),r&&i.transform(Lt().rotate(-r,[a.x,a.y])),i.options.zIndex=e.zIndex,i):null},createElement:function(){var t,i=this,n=this.options.visual,e=this.pointData||{};return t=n?n({value:e.value,dataItem:e.dataItem,sender:this.getSender(),series:e.series,category:e.category,rect:this.paddingBox.toRect(),options:this.visualOptions(),createVisual:function(){return i.getElement()}}):this.getElement()},visualOptions:function(){var t=this.options;return{background:t.background,border:t.border,margin:t.margin,padding:t.padding,type:t.type,size:t.width,visible:t.visible}},createVisual:function(){this.visual=this.createElement()}}),w(rn,{type:Ot,align:Et,vAlign:Et}),an="linear",ln="radial",hn={glass:{type:an,rotation:0,stops:[{offset:0,color:di,opacity:0},{offset:.25,color:di,opacity:.3},{offset:1,color:di,opacity:0}]},sharpBevel:{type:ln,stops:[{offset:0,color:di,opacity:.55},{offset:.65,color:di,opacity:0},{offset:.95,color:di,opacity:.25}]},roundedBevel:{type:ln,stops:[{offset:.33,color:di,opacity:.06},{offset:.83,color:di,opacity:.2},{offset:.95,color:di,opacity:0}]},roundedGlass:{type:ln,supportVML:!1,stops:[{offset:0,color:di,opacity:0},{offset:.5,color:di,opacity:.3},{offset:.99,color:di,opacity:0}]},sharpGlass:{type:ln,supportVML:!1,stops:[{offset:0,color:di,opacity:.2},{offset:.15,color:di,opacity:.15},{offset:.17,color:di,opacity:.35},{offset:.85,color:di,opacity:.05},{offset:.87,color:di,opacity:.15},{offset:.99,color:di,opacity:0}]},bubbleShadow:{type:ln,center:[.5,.5],radius:.5}},un=on.extend({init:function(t){on.fn.init.call(this,t);var i=this.options;i.width=parseInt(i.width,10),i.height=parseInt(i.height,10),this.gradients={}},reflow:function(){var t,i=this,n=i.options,e=i.children,o=new Zi(0,0,n.width,n.height);for(this.box=o.unpad(n.margin),t=0;t<e.length;t++)e[t].reflow(o),o=R(o,e[t].box)||new Zi},createVisual:function(){this.visual=new kt,this.createBackground()},createBackground:function(){var t=this.options,i=t.border||{},n=this.box.clone().pad(t.margin).unpad(i.width),e=Mt.fromRect(n.toRect(),{stroke:{color:i.width?i.color:"",width:i.width,dashType:i.dashType},fill:{color:t.background,opacity:t.opacity},zIndex:-10});this.visual.append(e)},getRoot:function(){return this},createGradient:function(i){var n,e,o=this.gradients,s=Yi(i),r=hn[i.gradient];return o[s]?n=o[s]:(e=t.extend({},r,i),"linear"===r.type?n=new yt.LinearGradient(e):(i.innerRadius&&(e.stops=U(e)),n=new yt.RadialGradient(e),n.supportVML=r.supportVML!==!1),o[s]=n),n},cleanGradients:function(){var t,i=this.gradients;for(t in i)i[t]._observers=[]},size:function(){var t=this.options;return new Zi(0,0,t.width,t.height)}}),w(un,{width:Xt,height:Ht,background:di,border:{color:zt,width:0},margin:d(5),zIndex:-2}),cn=on.extend({init:function(t){on.fn.init.call(this,t),this._initDirection()},_initDirection:function(){var t=this.options;t.vertical?(this.groupAxis=mi,this.elementAxis=gi,this.groupSizeField=pi,this.elementSizeField=Wt,this.groupSpacing=t.spacing,this.elementSpacing=t.vSpacing):(this.groupAxis=gi,this.elementAxis=mi,this.groupSizeField=Wt,this.elementSizeField=pi,this.groupSpacing=t.vSpacing,this.elementSpacing=t.spacing)},reflow:function(t){this.box=t.clone(),this.reflowChildren()},reflowChildren:function(){var t,i,n,e,o,s,r,a,l,h,u,c=this,f=this,d=f.box,p=f.elementAxis,m=f.groupAxis,g=f.elementSizeField,x=f.groupSizeField,v=this.groupOptions(),b=v.groups,y=v.groupsSize,w=v.maxGroupElementsSize,M=b.length,k=d[m+1]+this.alignStart(y,d[x]());if(M){for(t=k,i=0;i<M;i++){for(n=b[i],e=n.groupElements,o=d[p+1],s=e.length,r=0;r<s;r++)a=e[r],l=c.elementSize(a),h=t+c.alignStart(l[x],n.groupSize),u=new Zi,u[m+1]=h,u[m+2]=h+l[x],u[p+1]=o,u[p+2]=o+l[g],a.reflow(u),o+=l[g]+c.elementSpacing;t+=n.groupSize+c.groupSpacing}d[m+1]=k,d[m+2]=k+y,d[p+2]=d[p+1]+w}},alignStart:function(t,i){var n=0,e=this.options.align;return e===ai||e===Vt?n=i-t:e===Et&&(n=(i-t)/2),n},groupOptions:function(){var t,i,n,e=this,o=this,s=o.box,r=o.children,a=o.elementSizeField,l=o.groupSizeField,h=o.elementSpacing,u=o.groupSpacing,c=qi(s[a]()),f=r.length,d=[],p=0,m=0,g=0,x=0,v=[];for(t=0;t<f;t++)i=r[t],i.box||i.reflow(s),n=e.elementSize(i),e.options.wrap&&qi(m+h+n[a])>c&&(d.push({groupElements:v,groupSize:p,groupElementsSize:m}),x=Math.max(x,m),g+=u+p,p=0,m=0,v=[]),p=Math.max(p,n[l]),m>0&&(m+=h),m+=n[a],v.push(i);return d.push({groupElements:v,groupSize:p,groupElementsSize:m}),x=Math.max(x,m),g+=p,{groups:d,groupsSize:g,maxGroupElementsSize:x
}},elementSize:function(t){return{width:t.box.width(),height:t.box.height()}},createVisual:function(){}}),w(cn,{vertical:!0,wrap:!0,vSpacing:0,spacing:0}),fn=yt.Text,dn=on.extend({init:function(t,i){on.fn.init.call(this,i),this.content=t,this.reflow(new Zi)},reflow:function(t){var i=this.options,n=i.size=wt.measureText(this.content,{font:i.font});this.baseline=n.baseline,this.box=new Zi(t.x1,t.y1,t.x1+n.width,t.y1+n.height)},createVisual:function(){var t=this.options,i=t.font,n=t.color,e=t.opacity,o=t.cursor;this.visual=new fn(this.content,this.box.toRect().topLeft(),{font:i,fill:{color:n,opacity:e},cursor:o})}}),w(dn,{font:Nt,color:zt}),pn=/\n/m,mn=sn.extend({init:function(t,i,n){sn.fn.init.call(this,i),this.content=t,this.data=n,this._initContainer(),this.options._autoReflow!==!1&&this.reflow(new Zi)},_initContainer:function(){var t,i,n=this.options,e=(this.content+"").split(pn),o=new cn({vertical:!0,align:n.align,wrap:!1}),s=It({},n,{opacity:1,animation:null});for(this.container=o,this.append(o),t=0;t<e.length;t++)i=new dn(e[t].trim(),s),o.append(i)},reflow:function(t){var i,n,e,o,s=this.options,r=s.visual;this.container.options.align=s.align,r&&!this._boxReflow?(i=t,i.hasSize()||(this._boxReflow=!0,this.reflow(i),this._boxReflow=!1,i=this.box),n=this.visual=r(this.visualContext(i)),n&&(i=L(n.clippedBBox()||new Rt),n.options.zIndex=s.zIndex),this.box=this.contentBox=this.paddingBox=i):(sn.fn.reflow.call(this,t),s.rotation&&(e=d(s.margin),o=this.box.unpad(e),this.targetBox=t,this.normalBox=o.clone(),o=this.rotate(),o.translate(e.left-e.right,e.top-e.bottom),this.rotatedBox=o.clone(),o.pad(e)))},createVisual:function(){var t,i=this.options;this.visual=new kt({transform:this.rotationTransform(),zIndex:i.zIndex,noclip:i.noclip}),this.hasBox()&&(t=Mt.fromRect(this.paddingBox.toRect(),this.visualStyle()),this.visual.append(t))},renderVisual:function(){if(this.options.visible)if(this.options.visual){var t=this.visual;t&&!Vi(t.options.noclip)&&(t.options.noclip=this.options.noclip),this.addVisual(),this.createAnimation()}else sn.fn.renderVisual.call(this)},visualContext:function(i){var n=this,e={text:this.content,rect:i.toRect(),sender:this.getSender(),options:this.options,createVisual:function(){return n._boxReflow=!0,n.reflow(i),n._boxReflow=!1,n.getDefaultVisual()}};return this.data&&t.extend(e,this.data),e},getDefaultVisual:function(){this.createVisual(),this.renderChildren();var t=this.visual;return delete this.visual,t},rotate:function(){var t=this.options;return this.box.rotate(t.rotation),this.align(this.targetBox,mi,t.align),this.align(this.targetBox,gi,t.vAlign),this.box},rotationTransform:function(){var t,i,n,e,o=this.options.rotation;return o?(t=this.normalBox.center(),i=t.x,n=t.y,e=this.rotatedBox.center(),Lt().translate(e.x-i,e.y-n).rotate(o,[i,n])):null}}),gn=on.extend({init:function(i){on.fn.init.call(this,i),this.append(new mn(this.options.text,t.extend({},this.options,{vAlign:this.options.position})))},reflow:function(t){on.fn.reflow.call(this,t),this.box.snapTo(t,mi)}}),gn.buildTitle=function(i,n,e){var o,s=i;return"string"==typeof i&&(s={text:i}),s=t.extend({visible:!0},e,s),s&&s.visible&&s.text&&(o=new gn(s),n.append(o)),o},w(gn,{color:zt,position:ui,align:Et,margin:d(5),padding:d(5)}),xn=mn.extend({init:function(t,i,n,e,o){mn.fn.init.call(this,i,o),this.text=i,this.value=t,this.index=n,this.dataItem=e,this.reflow(new Zi)},visualContext:function(t){var i=mn.fn.visualContext.call(this,t);return i.value=this.value,i.dataItem=this.dataItem,i.format=this.options.format,i.culture=this.options.culture,i},click:function(t,i){t.trigger(_t,{element:Gi(i),value:this.value,text:this.text,index:this.index,dataItem:this.dataItem,axis:this.parent.options})},rotate:function(){var t,i;return this.options.alignRotation!==Et?(t=this.normalBox.toRect(),i=this.rotationTransform(),this.box=L(t.bbox(i.matrix()))):mn.fn.rotate.call(this),this.box},rotationTransform:function(){var t,i,n,e,o,s,r,a,l,h,u,c,f,d,p,m,g,x,v=this.options,b=v.rotation;return b?v.alignRotation===Et?mn.fn.rotationTransform.call(this):(t=Lt().rotate(b).matrix(),i=this.normalBox.toRect(),n=this.targetBox.toRect(),e=v.rotationOrigin||ui,o=e===ui||e===Vt?mi:gi,s=e===ui||e===Vt?gi:mi,r=e===ui||e===Zt?n.origin:n.bottomRight(),a=i.topLeft().transformCopy(t),l=i.topRight().transformCopy(t),h=i.bottomRight().transformCopy(t),u=i.bottomLeft().transformCopy(t),c=Rt.fromPoints(a,l,h,u),f={},f[s]=n.origin[s]-c.origin[s],d=Math.abs(a[s]+f[s]-r[s]),p=Math.abs(l[s]+f[s]-r[s]),qi(d,Yt)===qi(p,Yt)?(m=a,g=l):p<d?(m=l,g=h):(m=a,g=u),x=m[o]+(g[o]-m[o])/2,f[o]=n.center()[o]-x,Lt().translate(f.x,f.y).rotate(b)):null}}),w(xn,{_autoReflow:!1}),vn=7,bn="#fff",yn=sn.extend({init:function(t,i,n){sn.fn.init.call(this,i),this.fields=t,this.chartService=n,this.render()},hide:function(){this.options.visible=!1},show:function(){this.options.visible=!0},render:function(){var t,i,n,e,o,s,r,a,l,h,u=this,c=this.options;c.visible&&(t=c.label,i=c.icon,n=new Zi,e=function(){return u},o=i.size,s=this.fields.text,Vi(t)&&t.visible&&(l=p(t),l?s=l(this.fields):t.format&&(s=this.chartService.format.auto(t.format,s)),t.color||(t.color=t.position===Jt?bn:i.background),this.label=new mn(s,It({},t)),this.label.aliasFor=e,t.position!==Jt||Vi(o)||(i.type===Ot?o=Math.max(this.label.box.width(),this.label.box.height()):(r=this.label.box.width(),a=this.label.box.height()),n.wrap(this.label.box))),i.width=r||o||vn,i.height=a||o||vn,h=new rn(It({},i)),h.aliasFor=e,this.marker=h,this.append(h),this.label&&this.append(this.label),h.reflow(new Zi),this.wrapperBox=n.wrap(h.box))},reflow:function(t){var i,n,e,o=this,s=o.options,r=o.label,a=o.marker,l=o.wrapperBox,h=t.center(),u=s.line.length,c=s.position;s.visible&&(x(c,[Zt,ai])?c===Zt?(e=l.alignTo(t,c).translate(-u,t.center().y-l.center().y),s.line.visible&&(i=[t.x1,h.y],this.linePoints=[i,[e.x2,h.y]],n=e.clone().wrapPoint(i))):(e=l.alignTo(t,c).translate(u,t.center().y-l.center().y),s.line.visible&&(i=[t.x2,h.y],this.linePoints=[i,[e.x1,h.y]],n=e.clone().wrapPoint(i))):c===Vt?(e=l.alignTo(t,c).translate(t.center().x-l.center().x,u),s.line.visible&&(i=[h.x,t.y2],this.linePoints=[i,[h.x,e.y1]],n=e.clone().wrapPoint(i))):(e=l.alignTo(t,c).translate(t.center().x-l.center().x,-u),s.line.visible&&(i=[h.x,t.y1],this.linePoints=[i,[h.x,e.y2]],n=e.clone().wrapPoint(i))),a&&a.reflow(e),r&&(r.reflow(e),a&&(s.label.position===ri&&r.box.alignTo(a.box,c),r.reflow(r.box))),this.contentBox=e,this.targetBox=t,this.box=n||e)},createVisual:function(){sn.fn.createVisual.call(this),this.visual.options.noclip=this.options.noclip,this.options.visible&&this.createLine()},renderVisual:function(){var i=this,n=this.options,e=n.visual;n.visible&&e?(this.visual=e(t.extend(this.fields,{sender:this.getSender(),rect:this.targetBox.toRect(),options:{background:n.background,border:n.background,icon:n.icon,label:n.label,line:n.line,position:n.position,visible:n.visible},createVisual:function(){i.createVisual(),i.renderChildren();var t=i.visual;return delete i.visual,t}})),this.addVisual()):sn.fn.renderVisual.call(this)},createLine:function(){var t,i=this.options.line;this.linePoints&&(t=Mt.fromPoints(this.linePoints,{stroke:{color:i.color,width:i.width,dashType:i.dashType}}),o(t),this.visual.append(t))},click:function(t,i){var n=this.eventArgs(i);t.trigger(ni,n)||i.preventDefault()},over:function(t,i){var n=this.eventArgs(i);t.trigger(ei,n)||i.preventDefault()},out:function(t,i){var n=this.eventArgs(i);t.trigger(oi,n)},eventArgs:function(i){var n=this.options;return t.extend(this.fields,{element:Gi(i),text:Vi(n.label)?n.label.text:"",visual:this.visual})}}),w(yn,{icon:{visible:!0,type:Ot},label:{position:Jt,visible:!0,align:Et,vAlign:Et},line:{visible:!0},visible:!0,position:ui,zIndex:2}),wn=on.extend({init:function(t,i){void 0===i&&(i=new Si),on.fn.init.call(this,t),this.chartService=i,this.options.visible||(this.options=It({},this.options,{labels:{visible:!1},line:{visible:!1},margin:0,majorTickSize:0,minorTickSize:0})),this.options.minorTicks=It({},{color:this.options.line.color,width:this.options.line.width,visible:this.options.minorTickType!==ii},this.options.minorTicks,{size:this.options.minorTickSize,align:this.options.minorTickType}),this.options.majorTicks=It({},{color:this.options.line.color,width:this.options.line.width,visible:this.options.majorTickType!==ii},this.options.majorTicks,{size:this.options.majorTickSize,align:this.options.majorTickType}),this.initFields(),this.options._deferLabels||this.createLabels(),this.createTitle(),this.createNotes()},initFields:function(){},labelsRange:function(){return{min:this.options.labels.skip,max:this.labelsCount()}},createLabels:function(){var t,i,n,e,o=this,s=this.options,r=s.vertical?ai:Et,a=It({},s.labels,{align:r,zIndex:s.zIndex}),l=Math.max(1,a.step);if(this.clearLabels(),a.visible)for(t=this.labelsRange(),i=a.rotation,h(i)&&(a.alignRotation=i.align,a.rotation=i.angle),"auto"===a.rotation&&(a.rotation=0,s.autoRotateLabels=!0),n=t.min;n<t.max;n+=l)e=o.createAxisLabel(n,a),e&&(o.append(e),o.labels.push(e))},clearLabels:function(){this.children=m(this.children,function(t){return!(t instanceof xn)}),this.labels=[]},clearTitle:function(){var t=this;this.title&&(this.children=m(this.children,function(i){return i!==t.title}),this.title=void 0)},clear:function(){this.clearLabels(),this.clearTitle()},lineBox:function(){var t=this,i=t.options,n=t.box,e=i.vertical,o=i.labels.mirror,s=o?n.x1:n.x2,r=o?n.y2:n.y1,a=i.line.width||0;return e?new Zi(s,n.y1,s,n.y2-a):new Zi(n.x1,r,n.x2-a,r)},createTitle:function(){var t,i=this.options,n=It({rotation:i.vertical?-90:0,text:"",zIndex:1,visualSize:!0},i.title);n.visible&&n.text&&(t=new mn(n.text,n),this.append(t),this.title=t)},createNotes:function(){var t,i,n,e=this,o=this.options,s=o.notes,r=s.data||[];for(this.notes=[],t=0;t<r.length;t++)i=It({},s,r[t]),i.value=e.parseNoteValue(i.value),n=new yn({value:i.value,text:i.label.text,dataItem:i},i,e.chartService),n.options.visible&&(Vi(n.options.position)?o.vertical&&!x(n.options.position,[Zt,ai])?n.options.position=o.reverse?Zt:ai:o.vertical||x(n.options.position,[ui,Vt])||(n.options.position=o.reverse?Vt:ui):n.options.position=o.vertical?o.reverse?Zt:ai:o.reverse?Vt:ui,e.append(n),e.notes.push(n))},parseNoteValue:function(t){return t},renderVisual:function(){on.fn.renderVisual.call(this),this.createPlotBands()},createVisual:function(){on.fn.createVisual.call(this),this.createBackground(),this.createLine()},gridLinesVisual:function(){var t=this._gridLines;return t||(t=this._gridLines=new kt({zIndex:-2}),this.appendVisual(this._gridLines)),t},createTicks:function(t){function i(i,n,s){var a,l=i.length,h=Math.max(1,n.step);if(n.visible)for(a=n.skip;a<l;a+=h)Vi(s)&&a%s===0||(r.tickX=o?e.x2:e.x2-n.size,r.tickY=o?e.y1-n.size:e.y1,r.position=i[a],t.append(j(r,n)))}var n=this.options,e=this.lineBox(),o=n.labels.mirror,s=n.majorTicks.visible?n.majorUnit:0,r={vertical:n.vertical};i(this.getMajorTickPositions(),n.majorTicks),i(this.getMinorTickPositions(),n.minorTicks,s/n.minorUnit)},createLine:function(){var t,i,n=this.options,e=n.line,s=this.lineBox();e.width>0&&e.visible&&(t=new Mt({stroke:{width:e.width,color:e.color,dashType:e.dashType}}),t.moveTo(s.x1,s.y1).lineTo(s.x2,s.y2),n._alignLines&&o(t),i=this._lineGroup=new kt,i.append(t),this.visual.append(i),this.createTicks(i))},getActualTickSize:function(){var t=this.options,i=0;return t.majorTicks.visible&&t.minorTicks.visible?i=Math.max(t.majorTicks.size,t.minorTicks.size):t.majorTicks.visible?i=t.majorTicks.size:t.minorTicks.visible&&(i=t.minorTicks.size),i},createBackground:function(){var t=this,i=t.options,n=t.box,e=i.background;e&&(this._backgroundPath=Mt.fromRect(n.toRect(),{fill:{color:e},stroke:null}),this.visual.append(this._backgroundPath))},createPlotBands:function(){var t,i,n,e,o,s,r,a,l=this,h=this.options,u=h.plotBands||[],c=h.vertical,f=this.plotArea;if(0!==u.length){for(t=this._plotbandGroup=new kt({zIndex:-1}),i=m(this.pane.axes,function(t){return t.options.vertical!==l.options.vertical})[0],n=0;n<u.length;n++)e=u[n],o=void 0,s=void 0,c?(o=(i||f.axisX).lineBox(),s=l.getSlot(e.from,e.to,!0)):(o=l.getSlot(e.from,e.to,!0),s=(i||f.axisY).lineBox()),0!==o.width()&&0!==s.height()&&(r=new Rt([o.x1,s.y1],[o.width(),s.height()]),a=Mt.fromRect(r,{fill:{color:e.color,opacity:e.opacity},stroke:null}),t.append(a));this.appendVisual(t)}},createGridLines:function(t){function i(t,i,n){var e,o,s=t.length,r=Math.max(1,i.step);if(i.visible)for(e=i.skip;e<s;e+=r)o=qi(t[e]),x(o,f)||e%n===0||a&&u===o||(c.position=o,d.append(A(c,i)),f.push(o))}var n=this.options,e=n.minorGridLines,o=n.majorGridLines,s=n.minorUnit,r=n.vertical,a=t.options.line.visible,l=o.visible?n.majorUnit:0,h=t.lineBox(),u=h[r?"y1":"x1"],c={lineStart:h[r?"x1":"y1"],lineEnd:h[r?"x2":"y2"],vertical:r},f=[],d=this.gridLinesVisual();return i(this.getMajorTickPositions(),o),i(this.getMinorTickPositions(),e,l/s),d.children},reflow:function(t){var i,n,e=this,o=e.options,s=e.labels,r=e.title,a=o.vertical,l=s.length,h=a?pi:Wt,u=r?r.box[h]():0,c=this.getActualTickSize()+o.margin+u,f=(this.getRoot()||{}).box||t,d=f[h](),p=0;for(i=0;i<l;i++)n=s[i].box[h](),n+c<=d&&(p=Math.max(p,n));this.box=a?new Zi(t.x1,t.y1,t.x1+p+c,t.y2):new Zi(t.x1,t.y1,t.x2,t.y1+p+c),this.arrangeTitle(),this.arrangeLabels(),this.arrangeNotes()},getLabelsTickPositions:function(){return this.getMajorTickPositions()},labelTickIndex:function(t){return t.index},arrangeLabels:function(){var t,i,n,e,o,s,r,a,l,h,u,c=this,f=this,d=f.options,p=f.labels,m=this.labelsBetweenTicks(),g=d.vertical,x=this.lineBox(),v=d.labels.mirror,b=this.getLabelsTickPositions(),y=this.getActualTickSize()+d.margin;for(t=0;t<p.length;t++)i=p[t],n=c.labelTickIndex(i),e=g?i.box.height():i.box.width(),o=b[n]-e/2,s=void 0,r=void 0,a=void 0,g?(m&&(r=b[n],a=b[n+1],l=r+(a-r)/2,o=l-e/2),h=x.x2,v?(h+=y,i.options.rotationOrigin=Zt):(h-=y+i.box.width(),i.options.rotationOrigin=ai),s=i.box.move(h,o)):(m?(r=b[n],a=b[n+1]):(r=o,a=o+e),u=x.y1,v?(u-=y+i.box.height(),i.options.rotationOrigin=Vt):(u+=y,i.options.rotationOrigin=ui),s=new Zi(r,u,a,u+i.box.height())),i.reflow(s)},autoRotateLabels:function(){var t,i,n,e,o,s,r;if(this.options.autoRotateLabels&&!this.options.vertical){for(t=this.getMajorTickPositions(),i=this.labels,e=0;e<i.length;e++)if(o=Math.abs(t[e+1]-t[e]),s=i[e].box,s.width()>o){if(s.height()>o){n=-90;break}n=-45}if(n){for(r=0;r<i.length;r++)i[r].options.rotation=n,i[r].reflow(new Zi);return!0}}},arrangeTitle:function(){var t=this,i=t.options,n=t.title,e=i.labels.mirror,o=i.vertical;n&&(o?(n.options.align=e?ai:Zt,n.options.vAlign=n.options.position):(n.options.align=n.options.position,n.options.vAlign=e?ui:Vt),n.reflow(this.box))},arrangeNotes:function(){var t,i,n,e,o=this;for(t=0;t<this.notes.length;t++)i=o.notes[t],n=i.options.value,e=void 0,Vi(n)?(o.shouldRenderNote(n)?i.show():i.hide(),e=o.noteSlot(n)):i.hide(),i.reflow(e||o.lineBox())},noteSlot:function(t){return this.getSlot(t)},alignTo:function(t){var i=t.lineBox(),n=this.options.vertical,e=n?gi:mi;this.box.snapTo(i,e),n?this.box.shrink(0,this.lineBox().height()-i.height()):this.box.shrink(this.lineBox().width()-i.width(),0),this.box[e+1]-=this.lineBox()[e+1]-i[e+1],this.box[e+2]-=this.lineBox()[e+2]-i[e+2]},axisLabelText:function(t,i,n){var e=p(n),o=t;return e?o=e({value:t,dataItem:i,format:n.format,culture:n.culture}):n.format&&(o=this.chartService.format.localeAuto(n.format,[t],n.culture)),o},slot:function(t,i,n){var e=this.getSlot(t,i,n);if(e)return e.toRect()},contentBox:function(){var t,i,n,e=this.box.clone(),o=this.labels;return o.length&&(t=this.options.vertical?gi:mi,this.chartService.isPannable(t)?(i=this.maxLabelOffset(),e[t+1]-=i.start,e[t+2]+=i.end):(o[0].options.visible&&e.wrap(o[0].box),n=o[o.length-1],n.options.visible&&e.wrap(n.box))),e},maxLabelOffset:function(){var t,i,n,e,o,s=this,r=this.options,a=r.vertical,l=r.reverse,h=this.labelsBetweenTicks(),u=this.getLabelsTickPositions(),c=a?gi:mi,f=this.labels,d=l?1:0,p=l?0:1,m=0,g=0;for(t=0;t<f.length;t++)i=f[t],n=s.labelTickIndex(i),e=void 0,o=void 0,h?(e=u[n+d],o=u[n+p]):e=o=u[n],m=Math.max(m,e-i.box[c+1]),g=Math.max(g,i.box[c+2]-o);return{start:m,end:g}},limitRange:function(t,i,n,e,o){var s,r,a,l=this.options;return t<n&&o<0&&(!Vi(l.min)||l.min<=n)||e<i&&o>0&&(!Vi(l.max)||e<=l.max)?null:i<n&&o>0||e<t&&o<0?{min:t,max:i}:(s=i-t,r=t,a=i,t<n&&o<0?(r=Hi(t,n,e),a=Hi(t+s,n+s,e)):i>e&&o>0&&(a=Hi(i,n,e),r=Hi(i-s,n,e-s)),{min:r,max:a})},valueRange:function(){return{min:this.seriesMin,max:this.seriesMax}},labelsBetweenTicks:function(){return!this.options.justified},prepareUserOptions:function(){}}),w(wn,{labels:{visible:!0,rotation:0,mirror:!1,step:1,skip:0},line:{width:1,color:zt,visible:!0},title:{visible:!0,position:Et},majorTicks:{align:ri,size:4,skip:0,step:1},minorTicks:{align:ri,size:3,skip:0,step:1},axisCrossingValue:0,majorTickType:ri,minorTickType:ii,majorGridLines:{skip:0,step:1},minorGridLines:{visible:!1,width:1,color:zt,skip:0,step:1},margin:5,visible:!0,reverse:!1,justified:!0,notes:{label:{text:""}},_alignLines:!0,_deferLabels:!1}),Mn="milliseconds",kn="seconds",Tn="minutes",Sn="hours",Rn="days",Un="weeks",Ln="months",jn="years",An=1,In=1e3,Bn=60*In,Cn=60*Bn,Pn=24*Cn,_n=7*Pn,zn=31*Pn,Vn=365*Pn,En={years:Vn,months:zn,weeks:_n,days:Pn,hours:Cn,minutes:Bn,seconds:In,milliseconds:An},On=.01,Dn=wn.extend({initFields:function(){this._ticks={}},categoriesHash:function(){return""},clone:function(){var i=new Dn(t.extend({},this.options),this.chartService);return i.createLabels(),i},initUserOptions:function(t){var i,n,e=t.categories||[],o=Vi(t.min),s=Vi(t.max);return t.srcCategories=t.categories=e,(o||s)&&e.length&&(i=o?Math.floor(t.min):0,n=s?t.justified?Math.floor(t.max)+1:Math.ceil(t.max):e.length,t.categories=t.categories.slice(i,n)),t},rangeIndices:function(){var t,i=this.options,n=i.categories.length||1,e=r(i.min)?i.min%1:0;return t=r(i.max)&&i.max%1!==0&&i.max<this.totalRange().max?n-(1-i.max%1):n-(i.justified?1:0),{min:e,max:t}},totalRangeIndices:function(t){var i,n,e=this.options,o=r(e.min)?e.min:0;return i=r(e.max)?e.max:r(e.min)?o+e.categories.length:this.totalRange().max||1,t&&(n=this.totalRange(),o=Hi(o,0,n.max),i=Hi(i,0,n.max)),{min:o,max:i}},range:function(){var t=this.options,i=r(t.min)?t.min:0,n=r(t.max)?t.max:this.totalRange().max;return{min:i,max:n}},roundedRange:function(){return this.range()},totalRange:function(){var t=this.options;return{min:0,max:Math.max(this._seriesMax||0,t.srcCategories.length)-(t.justified?1:0)}},scaleOptions:function(){var t=this.rangeIndices(),i=t.min,n=t.max,e=this.lineBox(),o=this.options.vertical?e.height():e.width(),s=o/(n-i||1);return{scale:s*(this.options.reverse?-1:1),box:e,min:i,max:n}},arrangeLabels:function(){wn.fn.arrangeLabels.call(this),this.hideOutOfRangeLabels()},hideOutOfRangeLabels:function(){var t,i,n,e,o,s=this,r=s.box,a=s.labels;a.length&&(t=this.options.vertical?gi:mi,i=r[t+1],n=r[t+2],e=a[0],o=Ni(a),(e.box[t+1]>n||e.box[t+2]<i)&&(e.options.visible=!1),(o.box[t+1]>n||o.box[t+2]<i)&&(o.options.visible=!1))},getMajorTickPositions:function(){return this.getTicks().majorTicks},getMinorTickPositions:function(){return this.getTicks().minorTicks},getLabelsTickPositions:function(){return this.getTicks().labelTicks},tickIndices:function(t){for(var i=this.rangeIndices(),n=i.min,e=i.max,o=Math.ceil(e),s=Math.floor(n),r=[];s<=o;)r.push(s),s+=t;return r},getTickPositions:function(t){var i,n=this.options,e=n.vertical,o=n.reverse,s=this.scaleOptions(),r=s.scale,a=s.box,l=s.min,h=a[(e?gi:mi)+(o?2:1)],u=this.tickIndices(t),c=[];for(i=0;i<u.length;i++)c.push(h+qi(r*(u[i]-l),Dt));return c},getTicks:function(){var t,i=this.options,n=this._ticks,e=this.rangeIndices(),o=this.lineBox(),s=o.getHash()+e.min+","+e.max+i.reverse+i.justified;return n._hash!==s&&(t=i.minorTicks.visible||i.minorGridLines.visible,n._hash=s,n.labelTicks=this.getTickPositions(1),n.majorTicks=this.filterOutOfRangePositions(n.labelTicks,o),n.minorTicks=t?this.filterOutOfRangePositions(this.getTickPositions(.5),o):[]),n},filterOutOfRangePositions:function(t,i){var n,e,o,s,r;if(!t.length)return t;for(n=this.options.vertical?gi:mi,e=function(t){return i[n+1]<=t&&t<=i[n+2]},o=t.length-1,s=0;!e(t[s])&&s<=o;)s++;for(r=o;!e(t[r])&&r>=0;)r--;return t.slice(s,r+1)},getSlot:function(t,i,n){var e,o,s=this.options,r=s.reverse,a=s.justified,l=s.vertical,h=this.scaleOptions(),u=h.scale,c=h.box,f=h.min,d=l?gi:mi,p=c[d+(r?2:1)],m=c.clone(),g=!Vi(i),x=Wi(t,0),v=Wi(i,x);return v=Math.max(v-1,x),v=Math.max(x,v),e=p+(x-f)*u,o=p+(v+1-f)*u,g&&a&&(o=e),n&&(e=Hi(e,c[d+1],c[d+2]),o=Hi(o,c[d+1],c[d+2])),m[d+1]=r?o:e,m[d+2]=r?e:o,m},limitSlot:function(t){var i=this.options.vertical,n=i?gi:mi,e=this.lineBox(),o=t.clone();return o[n+1]=Hi(t[n+1],e[n+1],e[n+2]),o[n+2]=Hi(t[n+2],e[n+1],e[n+2]),o},slot:function(t,i,n){var e=Math.floor(this.options.min||0),o=t,s=i;return"string"==typeof o?o=this.categoryIndex(o):r(o)&&(o-=e),"string"==typeof s?s=this.categoryIndex(s):r(s)&&(s-=e),wn.fn.slot.call(this,o,s,n)},pointCategoryIndex:function(t){var i,n,e=this.options,o=e.reverse,s=e.justified,r=e.vertical,a=r?gi:mi,l=this.scaleOptions(),h=l.scale,u=l.box,c=l.min,f=l.max,d=o?f:c,p=u[a+1],m=u[a+2],g=t[a];return g<p||g>m?null:(i=d+(g-p)/h,n=i%1,s?i=Math.round(i):0===n&&i>0&&i--,Math.floor(i))},getCategory:function(t){var i=this.pointCategoryIndex(t);return null===i?null:this.options.categories[i]},categoryIndex:function(t){return this.totalIndex(t)-Math.floor(this.options.min||0)},categoryAt:function(t,i){var n=this.options;return(i?n.srcCategories:n.categories)[t]},categoriesCount:function(){return(this.options.categories||[]).length},translateRange:function(t){var i=this.options,n=this.lineBox(),e=i.vertical?n.height():n.width(),o=i.categories.length,s=e/o,r=qi(t/s,Yt);return{min:r,max:o+r}},zoomRange:function(t){var i=this.totalRangeIndices(),n=this.totalRange(),e=n.min,o=n.max,s=Hi(i.min+t,e,o),r=Hi(i.max-t,e,o);if(r-s>0)return{min:s,max:r}},scaleRange:function(t){var i=this.options.categories.length,n=t*i;return{min:-n,max:i+n}},labelsCount:function(){var t=this.labelsRange();return t.max-t.min},labelsRange:function(){var t,i=this.options,n=i.justified,e=i.labels,o=this.totalRangeIndices(!0),s=o.min,r=o.max,a=Math.floor(s);return n?(s=Math.ceil(s),r=Math.floor(r)):(s=Math.floor(s),r=Math.ceil(r)),t=s>e.skip?e.skip+e.step*Math.ceil((s-e.skip)/e.step):e.skip,{min:t-a,max:(i.categories.length?r+(n?1:0):0)-a}},createAxisLabel:function(t,i){var n=this.options,e=n.dataItems?n.dataItems[t]:null,o=Wi(n.categories[t],""),s=this.axisLabelText(o,e,i);return new xn(o,s,t,e,i)},shouldRenderNote:function(t){var i=this.totalRangeIndices();return Math.floor(i.min)<=t&&t<=Math.ceil(i.max)},noteSlot:function(t){var i=this.options,n=t-Math.floor(i.min||0);return this.getSlot(n)},arrangeNotes:function(){wn.fn.arrangeNotes.call(this),this.hideOutOfRangeNotes()},hideOutOfRangeNotes:function(){var t,i,n,e,o,s=this,r=s.notes,a=s.box;if(r&&r.length)for(t=this.options.vertical?gi:mi,i=a[t+1],n=a[t+2],e=0;e<r.length;e++)o=r[e],o.box&&(n<o.box[t+1]||o.box[t+2]<i)&&o.hide()},pan:function(t){var i=this.totalRangeIndices(!0),n=this.scaleOptions(),e=n.scale,o=qi(t/e,Yt),s=this.totalRange(),r=i.min+o,a=i.max+o;return this.limitRange(r,a,0,s.max,o)},pointsRange:function(t,i){var n=this.options,e=n.reverse,o=n.vertical,s=o?gi:mi,r=this.totalRangeIndices(!0),a=this.scaleOptions(),l=a.scale,h=a.box,u=h[s+(e?2:1)],c=t[s]-u,f=i[s]-u,d=r.min+c/l,p=r.min+f/l,m=Math.min(d,p),g=Math.max(d,p);if(g-m>=On)return{min:m,max:g}},valueRange:function(){return this.range()},totalIndex:function(t){var i=this.options,n=this._categoriesMap?this._categoriesMap.get(t):Q(t,i.srcCategories);return n},currentRangeIndices:function(){var t,i=this.options,n=0;return r(i.min)&&(n=Math.floor(i.min)),t=r(i.max)?i.justified?Math.floor(i.max):Math.ceil(i.max)-1:this.totalCount()-1,{min:n,max:t}},mapCategories:function(){var t,i,n;if(!this._categoriesMap)for(t=this._categoriesMap=new Ii,i=this.options.srcCategories,n=0;n<i.length;n++)t.set(i[n],n)},totalCount:function(){return Math.max(this.options.srcCategories.length,this._seriesMax||0)}}),w(Dn,{type:"category",vertical:!1,majorGridLines:{visible:!1,width:1,color:zt},labels:{zIndex:1},justified:!1,_deferLabels:!0}),Gn=3e5,Fn={milliseconds:"HH:mm:ss.fff",seconds:"HH:mm:ss",minutes:"HH:mm",hours:"HH:mm",days:"M/d",weeks:"M/d",months:"MMM 'yy",years:"yyyy"},Nn=.2,Hn="auto",Yn=[Mn,kn,Tn,Sn,Rn,Un,Ln,jn],Xn="fit",qn=Tt.extend({init:function(t){this.options=t},displayIndices:function(){return{min:0,max:1}},displayRange:function(){return{}},total:function(){return{}},valueRange:function(){return{}},valueIndex:function(){return-1},values:function(){return[]},totalIndex:function(){return-1},valuesCount:function(){return 0},totalCount:function(){return 0},dateAt:function(){return null}}),Kn=Tt.extend({init:function(t,i,n){var e,o,s,r,a,l;this.options=n,n.baseUnitStep=n.baseUnitStep||1,e=n.roundToBaseUnit,o=n.justified,this.start=V(t,0,n.baseUnit,n.weekStartDay),s=this.roundToTotalStep(i),r=!o&&N(i,s)&&!n.justifyEnd,this.end=this.roundToTotalStep(i,!o,r?1:0),a=n.min||t,this.valueStart=this.roundToTotalStep(a),this.displayStart=e?this.valueStart:a,l=n.max,l?(this.valueEnd=this.roundToTotalStep(l,!1,!o&&N(l,this.roundToTotalStep(l))?-1:0),this.displayEnd=e?this.roundToTotalStep(l,!o):n.max):(this.valueEnd=s,this.displayEnd=e||r?this.end:i),this.valueEnd<this.valueStart&&(this.valueEnd=this.valueStart),this.displayEnd<=this.displayStart&&(this.displayEnd=this.roundToTotalStep(this.displayStart,!1,1))},displayRange:function(){return{min:this.displayStart,max:this.displayEnd}},displayIndices:function(){var t,i,n,e,o;return this._indices||(t=this.options,i=t.baseUnit,n=t.baseUnitStep,e=Y(this.displayStart,this.valueStart,i,n),o=Y(this.displayEnd,this.valueStart,i,n),this._indices={min:e,max:o}),this._indices},total:function(){return{min:this.start,max:this.end}},totalCount:function(){var t=this.totalIndex(this.end);return t+(this.options.justified?1:0)},valueRange:function(){return{min:this.valueStart,max:this.valueEnd}},valueIndex:function(t){var i=this.options;return Math.floor(Y(t,this.valueStart,i.baseUnit,i.baseUnitStep))},totalIndex:function(t){var i=this.options;return Math.floor(Y(t,this.start,i.baseUnit,i.baseUnitStep))},dateIndex:function(t){var i=this.options;return Y(t,this.valueStart,i.baseUnit,i.baseUnitStep)},valuesCount:function(){var t=this.valueIndex(this.valueEnd);return t+1},values:function(){var t,i,n,e=this._values;if(!e)for(t=this.options,i=this.valueRange(),this._values=e=[],n=i.min;n<=i.max;)e.push(n),n=V(n,t.baseUnitStep,t.baseUnit,t.weekStartDay);return e},dateAt:function(t,i){var n=this.options;return V(i?this.start:this.valueStart,n.baseUnitStep*t,n.baseUnit,n.weekStartDay)},roundToTotalStep:function(t,i,n){var e=this.options,o=e.baseUnit,s=e.baseUnitStep,r=e.weekStartDay,a=this.start,l=Y(t,a,o,s),h=i?Math.ceil(l):Math.floor(l);return n&&(h+=n),V(a,h*s,o,r)}}),Wn=Dn.extend({clone:function(){var i=new Wn(t.extend({},this.options),this.chartService);return i.createLabels(),i},categoriesHash:function(){var t=this.dataRange.total().min;return this.options.baseUnit+this.options.baseUnitStep+t},initUserOptions:function(t){return t},initFields:function(){var i,n,e,o,s,r,a,l,h;Dn.fn.initFields.call(this),i=this.chartService,n=i.intl,e=this.options,o=e.categories||[],o._parsed||(o=W(n,o),o._parsed=!0),e=It({roundToBaseUnit:!0},e,{categories:o,min:K(n,e.min),max:K(n,e.max)}),i.panning&&i.isPannable(e.vertical?gi:mi)&&(e.roundToBaseUnit=!1),e.userSetBaseUnit=e.userSetBaseUnit||e.baseUnit,e.userSetBaseUnitStep=e.userSetBaseUnitStep||e.baseUnitStep,this.options=e,e.srcCategories=o,o.length>0?(s=J(o),r=e.maxDivisions,this.dataRange=new Kn(s.min,s.max,tt(e)),r?(a=this.dataRange.displayRange(),l=t.extend({},e,{justified:!0,roundToBaseUnit:!1,baseUnit:"fit",min:a.min,max:a.max,maxDateGroups:r}),h=this.dataRange.options,Z(l,h.baseUnit,h.baseUnitStep),this.divisionRange=new Kn(s.min,s.max,l)):this.divisionRange=this.dataRange):(e.baseUnit=e.baseUnit||Rn,this.dataRange=this.divisionRange=new qn(e))},tickIndices:function(t){var i,n,e,o,s,r=this,a=r.dataRange,l=r.divisionRange,h=l.valuesCount();if(!this.options.maxDivisions||!h)return Dn.fn.tickIndices.call(this,t);for(i=[],n=l.values(),e=0,this.options.justified||(n=n.concat(l.dateAt(h)),e=.5),o=0;o<n.length;o++)i.push(a.dateIndex(n[o])+e),1!==t&&o>=1&&(s=i.length-1,i.splice(o,0,i[s-1]+(i[s]-i[s-1])*t));return i},shouldRenderNote:function(t){var i=this.range(),n=this.options.categories||[];return D(t,i.min)>=0&&D(t,i.max)<=0&&n.length},parseNoteValue:function(t){return K(this.chartService.intl,t)},noteSlot:function(t){return this.getSlot(t)},translateRange:function(t){var i,n,e=this.options,o=e.baseUnit,s=e.weekStartDay,r=e.vertical,a=this.lineBox(),l=r?a.height():a.width(),h=this.range(),u=l/(h.max-h.min),c=qi(t/u,Yt);return h.min&&h.max&&(i=B(e.min||h.min,c),n=B(e.max||h.max,c),h={min:V(i,0,o,s),max:V(n,0,o,s)}),h},scaleRange:function(t){var i,n,e=Math.abs(t),o=this.range(),s=o.min,r=o.max;if(s&&r){for(;e--;)i=G(s,r),n=Math.round(.1*i),t<0?(s=B(s,n),r=B(r,-n)):(s=B(s,-n),r=B(r,n));o={min:s,max:r}}return o},labelsRange:function(){return{min:this.options.labels.skip,max:this.divisionRange.valuesCount()}},pan:function(t){var i,n,e,o,s,r,a,l,h,u,c,f;return this.isEmpty()?null:(i=this.options,n=this.lineBox(),e=i.vertical?n.height():n.width(),o=this.dataRange.displayRange(),s=o.min,r=o.max,a=this.dataRange.total(),l=e/(r-s),h=qi(t/l,Yt)*(i.reverse?-1:1),u=B(s,h),c=B(r,h),f=this.limitRange(F(u),F(c),F(a.min),F(a.max),h),f?(f.min=C(f.min),f.max=C(f.max),f.baseUnit=i.baseUnit,f.baseUnitStep=i.baseUnitStep||1,f.userSetBaseUnit=i.userSetBaseUnit,f.userSetBaseUnitStep=i.userSetBaseUnitStep,f):void 0)},pointsRange:function(t,i){var n,e,o,s,r,a,l;return this.isEmpty()?null:(n=Dn.fn.pointsRange.call(this,t,i),e=this.dataRange.displayRange(),o=this.dataRange.displayIndices(),s=G(e.max,e.min)/(o.max-o.min),r=this.options,a=B(e.min,n.min*s),l=B(e.min,n.max*s),{min:a,max:l,baseUnit:r.userSetBaseUnit||r.baseUnit,baseUnitStep:r.userSetBaseUnitStep||r.baseUnitStep})},zoomRange:function(t){var i,n,e,o,s,r,a,l,h,u,c,f,d,p,m,g,x,v,b,y,w;if(this.isEmpty())return null;if(i=this.options,n=i.userSetBaseUnit===Xn,e=this.dataRange.total(),o=this.dataRange.displayRange(),s=o.min,r=o.max,a=this.dataRange.options,l=a.weekStartDay,h=a.baseUnit,u=a.baseUnitStep,c=V(s,t*u,h,l),f=V(r,-t*u,h,l),n)if(d=i.autoBaseUnitSteps,p=i.maxDateGroups,m=Ni(d[h])*p*En[h],g=G(r,s),x=G(f,c),v=Yn.indexOf(h),x<En[h]&&h!==Mn)h=Yn[v-1],b=Ni(d[h]),y=(g-(p-1)*b*En[h])/2,c=B(s,y),f=B(r,-y);else if(x>m&&h!==jn){w=0;do{v++,h=Yn[v],w=0,y=2*En[h];do b=d[h][w],w++;while(w<d[h].length&&y*b<g)}while(h!==jn&&y*b<g);y=(y*b-g)/2,y>0&&(c=B(s,-y),f=B(r,y),c=B(c,Hi(f,e.min,e.max)-f),f=B(f,Hi(c,e.min,e.max)-c))}return c<e.min&&(c=e.min),f>e.max&&(f=e.max),c&&f&&G(f,c)>0?{min:c,max:f,baseUnit:i.userSetBaseUnit||i.baseUnit,baseUnitStep:i.userSetBaseUnitStep||i.baseUnitStep}:void 0},range:function(){return this.dataRange.displayRange()},createAxisLabel:function(t,i){var n,e=this.options,o=e.dataItems&&!e.maxDivisions?e.dataItems[t]:null,s=this.divisionRange.dateAt(t),r=i.dateFormats[this.divisionRange.options.baseUnit];if(i.format=i.format||r,n=this.axisLabelText(s,o,i))return new xn(s,n,t,o,i)},categoryIndex:function(t){return this.dataRange.valueIndex(t)},slot:function(t,i,n){var e,o=this.dataRange,s=t,r=i;if(s instanceof Date&&(s=o.dateIndex(s)),r instanceof Date&&(r=o.dateIndex(r)),e=this.getSlot(s,r,n))return e.toRect()},getSlot:function(t,i,n){var e=t,o=i;return typeof e===si&&(e=this.categoryIndex(e)),typeof o===si&&(o=this.categoryIndex(o)),Dn.fn.getSlot.call(this,e,o,n)},valueRange:function(){var t=this.options,i=J(t.srcCategories);return{min:C(i.min),max:C(i.max)}},categoryAt:function(t,i){return this.dataRange.dateAt(t,i)},categoriesCount:function(){return this.dataRange.valuesCount()},rangeIndices:function(){return this.dataRange.displayIndices();
},labelsBetweenTicks:function(){return!this.divisionRange.options.justified},prepareUserOptions:function(){this.isEmpty()||(this.options.categories=this.dataRange.values())},getCategory:function(t){var i=this.pointCategoryIndex(t);return null===i?null:this.dataRange.dateAt(i)},totalIndex:function(t){return this.dataRange.totalIndex(t)},currentRangeIndices:function(){var t=this.dataRange.valueRange();return{min:this.dataRange.totalIndex(t.min),max:this.dataRange.totalIndex(t.max)}},totalRange:function(){return this.dataRange.total()},totalCount:function(){return this.dataRange.totalCount()},isEmpty:function(){return!this.options.srcCategories.length},roundedRange:function(){var i,n,e;return this.options.roundToBaseUnit!==!1||this.isEmpty()?this.range():(i=this.options,n=J(i.srcCategories),e=new Kn(n.min,n.max,t.extend({},i,{justified:!1,roundToBaseUnit:!0,justifyEnd:i.justified})),e.displayRange())}}),w(Wn,{type:Ft,labels:{dateFormats:Fn},autoBaseUnitSteps:{milliseconds:[1,10,100],seconds:[1,2,5,15,30],minutes:[1,2,5,15,30],hours:[1,2,3],days:[1,2,3],weeks:[1,2],months:[1,2,3,6],years:[1,2,3,5,10,25,50]},maxDateGroups:10}),Qn=Math.pow(10,-Yt+1),Jn=wn.extend({init:function(i,n,e,o){wn.fn.init.call(this,t.extend({},e,{seriesMin:i,seriesMax:n}),o)},initUserOptions:function(t){var i=rt(t.seriesMin,t.seriesMax,t);return this.totalOptions=at(i,t),ht(i,t)},initFields:function(){this.totalMin=this.totalOptions.min,this.totalMax=this.totalOptions.max,this.totalMajorUnit=this.totalOptions.majorUnit,this.seriesMin=this.options.seriesMin,this.seriesMax=this.options.seriesMax},clone:function(){return new Jn(this.seriesMin,this.seriesMax,t.extend({},this.options),this.chartService)},startValue:function(){return 0},range:function(){var t=this.options;return{min:t.min,max:t.max}},getDivisions:function(t){var i,n;return 0===t?1:(i=this.options,n=i.max-i.min,Math.floor(qi(n/t,Dt))+1)},getTickPositions:function(t,i){var n,e=this.options,o=e.vertical,s=e.reverse,r=this.lineBox(),a=o?r.height():r.width(),l=e.max-e.min,h=a/l,u=t*h,c=this.getDivisions(t),f=(o?-1:1)*(s?-1:1),d=1===f?1:2,p=[],m=r[(o?gi:mi)+d],g=0;for(i&&(g=i/t),n=0;n<c;n++)n%g!==0&&p.push(qi(m,Dt)),m+=u*f;return p},getMajorTickPositions:function(){return this.getTickPositions(this.options.majorUnit)},getMinorTickPositions:function(){return this.getTickPositions(this.options.minorUnit)},getSlot:function(t,i,n){var e,o,s,r,a,l,h,u,c,f,d,p,m,g;return void 0===n&&(n=!1),e=this.options,o=e.vertical,s=e.reverse,r=o?gi:mi,a=this.lineBox(),l=a[r+(s?2:1)],h=o?a.height():a.width(),u=s?-1:1,c=u*(h/(e.max-e.min)),f=new Zi(a.x1,a.y1,a.x1,a.y1),d=t,p=i,Vi(d)||(d=p||0),Vi(p)||(p=d||0),n&&(d=Math.max(Math.min(d,e.max),e.min),p=Math.max(Math.min(p,e.max),e.min)),o?(m=e.max-Math.max(d,p),g=e.max-Math.min(d,p)):(m=Math.min(d,p)-e.min,g=Math.max(d,p)-e.min),f[r+1]=st(l+c*(s?g:m)),f[r+2]=st(l+c*(s?m:g)),f},getValue:function(t){var i,n=this.options,e=n.vertical,o=n.reverse,s=+n.max,r=+n.min,a=e?gi:mi,l=this.lineBox(),h=l[a+(o?2:1)],u=e?l.height():l.width(),c=o?-1:1,f=c*(t[a]-h),d=(s-r)/u,p=f*d;return f<0||f>u?null:(i=e?s-p:r+p,qi(i,Yt))},translateRange:function(t){var i=this.options,n=i.vertical,e=i.reverse,o=i.max,s=i.min,r=this.lineBox(),a=n?r.height():r.width(),l=o-s,h=a/l,u=qi(t/h,Yt);return!n&&!e||n&&e||(u=-u),{min:s+u,max:o+u,offset:u}},scaleRange:function(t){var i=this.options,n=-t*i.majorUnit;return{min:i.min-n,max:i.max+n}},labelsCount:function(){return this.getDivisions(this.options.majorUnit)},createAxisLabel:function(t,i){var n=this.options,e=qi(n.min+t*n.majorUnit,Yt),o=this.axisLabelText(e,null,i);return new xn(e,o,t,null,i)},shouldRenderNote:function(t){var i=this.range();return i.min<=t&&t<=i.max},pan:function(t){var i=this.translateRange(t);return this.limitRange(i.min,i.max,this.totalMin,this.totalMax,i.offset)},pointsRange:function(t,i){var n=this.getValue(t),e=this.getValue(i),o=Math.min(n,e),s=Math.max(n,e);if(this.isValidRange(o,s))return{min:o,max:s}},zoomRange:function(t){var i=this,n=i.totalMin,e=i.totalMax,o=this.scaleRange(t),s=Hi(o.min,n,e),r=Hi(o.max,n,e);if(this.isValidRange(s,r))return{min:s,max:r}},isValidRange:function(t,i){return i-t>Qn}}),w(Jn,{type:"numeric",min:0,max:1,vertical:!0,majorGridLines:{visible:!0,width:1,color:zt},labels:{format:"#.####################"},zIndex:1}),Zn=wn.extend({init:function(t,i,n,e){var o=C(t),s=C(i),r=e.intl,a=n||{};a=It(a||{},{min:K(r,a.min),max:K(r,a.max),axisCrossingValue:W(r,a.axisCrossingValues||a.axisCrossingValue)}),a=ft(o,s,a),wn.fn.init.call(this,a,e),this.intlService=r,this.seriesMin=o,this.seriesMax=s,this.totalMin=F(E(F(o)-1,a.baseUnit)),this.totalMax=F(O(F(s)+1,a.baseUnit))},clone:function(){return new Zn(this.seriesMin,this.seriesMax,t.extend({},this.options),this.chartService)},range:function(){var t=this.options;return{min:t.min,max:t.max}},getDivisions:function(t){var i=this.options;return Math.floor(X(i.min,i.max,i.baseUnit)/t+1)},getTickPositions:function(t){var i,n,e,o=this.options,s=o.vertical,r=this.lineBox(),a=(s?-1:1)*(o.reverse?-1:1),l=1===a?1:2,h=r[(s?gi:mi)+l],u=this.getDivisions(t),c=G(o.max,o.min),f=s?r.height():r.width(),d=f/c,p=[h];for(i=1;i<u;i++)n=V(o.min,i*t,o.baseUnit),e=h+G(n,o.min)*d*a,p.push(qi(e,Dt));return p},getMajorTickPositions:function(){return this.getTickPositions(this.options.majorUnit)},getMinorTickPositions:function(){return this.getTickPositions(this.options.minorUnit)},getSlot:function(t,i,n){return Jn.prototype.getSlot.call(this,K(this.intlService,t),K(this.intlService,i),n)},getValue:function(t){var i=Jn.prototype.getValue.call(this,t);return null!==i?C(i):null},labelsCount:function(){return this.getDivisions(this.options.majorUnit)},createAxisLabel:function(t,i){var n,e,o=this.options,s=t*o.majorUnit,r=o.min;return s>0&&(r=V(r,s,o.baseUnit)),n=i.dateFormats[o.baseUnit],i.format=i.format||n,e=this.axisLabelText(r,null,i),new xn(r,e,t,null,i)},translateRange:function(t,i){var n=this.options,e=n.baseUnit,o=n.weekStartDay,s=this.lineBox(),r=n.vertical?s.height():s.width(),a=this.range(),l=r/G(a.max,a.min),h=qi(t/l,Yt)*(n.reverse?-1:1),u=B(n.min,h),c=B(n.max,h);return i||(u=V(u,0,e,o),c=V(c,0,e,o)),{min:u,max:c,offset:h}},scaleRange:function(t){for(var i,n,e=this.options,o=e.min,s=e.max,r=Math.abs(t);r--;)i=G(o,s),n=Math.round(.1*i),t<0?(o=B(o,n),s=B(s,-n)):(o=B(o,-n),s=B(s,n));return{min:o,max:s}},shouldRenderNote:function(t){var i=this.range();return D(t,i.min)>=0&&D(t,i.max)<=0},pan:function(t){var i=this.translateRange(t,!0),n=this.limitRange(F(i.min),F(i.max),this.totalMin,this.totalMax,i.offset);if(n)return{min:C(n.min),max:C(n.max)}},pointsRange:function(t,i){var n=this.getValue(t),e=this.getValue(i),o=Math.min(n,e),s=Math.max(n,e);return{min:C(o),max:C(s)}},zoomRange:function(t){var i=this.scaleRange(t),n=C(Hi(F(i.min),this.totalMin,this.totalMax)),e=C(Hi(F(i.max),this.totalMin,this.totalMax));return{min:n,max:e}}}),w(Zn,{type:Ft,majorGridLines:{visible:!0,width:1,color:zt},labels:{dateFormats:Fn}}),$n=10,te=wn.extend({init:function(t,i,n,e){var o=It({majorUnit:$n,min:t,max:i},n),s=o.majorUnit,r=mt(i,s),a=pt(t,i,o),l=dt(a,r,o,n);o.max=l.max,o.min=l.min,o.minorUnit=n.minorUnit||qi(s-1,Yt),wn.fn.init.call(this,o,e),this.totalMin=Vi(n.min)?Math.min(a,n.min):a,this.totalMax=Vi(n.max)?Math.max(r,n.max):r,this.logMin=qi(xt(l.min,s),Yt),this.logMax=qi(xt(l.max,s),Yt),this.seriesMin=t,this.seriesMax=i,this.createLabels()},clone:function(){return new te(this.seriesMin,this.seriesMax,t.extend({},this.options),this.chartService)},startValue:function(){return this.options.min},getSlot:function(t,i,n){var e,o,s=this,r=s.options,a=s.logMin,l=s.logMax,h=r.reverse,u=r.vertical,c=r.majorUnit,f=u?gi:mi,d=this.lineBox(),p=d[f+(h?2:1)],m=u?d.height():d.width(),g=h?-1:1,x=g*(m/(l-a)),v=new Zi(d.x1,d.y1,d.x1,d.y1),b=t,y=i;return Vi(b)||(b=y||1),Vi(y)||(y=b||1),b<=0||y<=0?null:(n&&(b=Math.max(Math.min(b,r.max),r.min),y=Math.max(Math.min(y,r.max),r.min)),b=xt(b,c),y=xt(y,c),u?(e=l-Math.max(b,y),o=l-Math.min(b,y)):(e=Math.min(b,y)-a,o=Math.max(b,y)-a),v[f+1]=st(p+x*(h?o:e)),v[f+2]=st(p+x*(h?e:o)),v)},getValue:function(t){var i,n=this,e=n.options,o=n.logMin,s=n.logMax,r=e.reverse,a=e.vertical,l=e.majorUnit,h=this.lineBox(),u=a===r?1:-1,c=1===u?1:2,f=a?h.height():h.width(),d=(s-o)/f,p=a?gi:mi,m=h[p+c],g=u*(t[p]-m),x=g*d;return g<0||g>f?null:(i=o+x,qi(Math.pow(l,i),Yt))},range:function(){var t=this.options;return{min:t.min,max:t.max}},scaleRange:function(t){var i=this.options.majorUnit,n=-t;return{min:Math.pow(i,this.logMin-n),max:Math.pow(i,this.logMax+n)}},translateRange:function(t){var i=this,n=i.options,e=i.logMin,o=i.logMax,s=n.reverse,r=n.vertical,a=n.majorUnit,l=this.lineBox(),h=r?l.height():l.width(),u=h/(o-e),c=qi(t/u,Yt);return!r&&!s||r&&s||(c=-c),{min:Math.pow(a,e+c),max:Math.pow(a,o+c),offset:c}},labelsCount:function(){var t=Math.floor(this.logMax),i=Math.floor(t-this.logMin)+1;return i},getMajorTickPositions:function(){var t=[];return this.traverseMajorTicksPositions(function(i){t.push(i)},{step:1,skip:0}),t},createTicks:function(t){function i(i,n){h.tickX=r?a.x2:a.x2-n.size,h.tickY=r?a.y1-n.size:a.y1,h.position=i,t.append(j(h,n))}var n=this.options,e=n.majorTicks,o=n.minorTicks,s=n.vertical,r=n.labels.mirror,a=this.lineBox(),l=[],h={vertical:s};return e.visible&&this.traverseMajorTicksPositions(i,e),o.visible&&this.traverseMinorTicksPositions(i,o),l},createGridLines:function(t){function i(t,i){x(t,l)||(a.position=t,h.append(A(a,i)),l.push(t))}var n=this.options,e=n.minorGridLines,o=n.majorGridLines,s=n.vertical,r=t.lineBox(),a={lineStart:r[s?"x1":"y1"],lineEnd:r[s?"x2":"y2"],vertical:s},l=[],h=this.gridLinesVisual();return o.visible&&this.traverseMajorTicksPositions(i,o),e.visible&&this.traverseMinorTicksPositions(i,e),h.children},traverseMajorTicksPositions:function(t,i){var n,e,o=this._lineOptions(),s=o.lineStart,r=o.step,a=this,l=a.logMin,h=a.logMax;for(n=Math.ceil(l)+i.skip;n<=h;n+=i.step)e=qi(s+r*(n-l),Yt),t(e,i)},traverseMinorTicksPositions:function(t,i){var n,e,o,s,r,a=this,l=this.options,h=l.min,u=l.max,c=l.minorUnit,f=l.majorUnit,d=this._lineOptions(),p=d.lineStart,m=d.step,g=this,x=g.logMin,v=g.logMax,b=Math.floor(x);for(n=b;n<v;n++)for(e=a._minorIntervalOptions(n),o=i.skip;o<c&&(s=e.value+o*e.minorStep,!(s>u));o+=i.step)s>=h&&(r=qi(p+m*(xt(s,f)-x),Yt),t(r,i))},createAxisLabel:function(t,i){var n=Math.ceil(this.logMin+t),e=Math.pow(this.options.majorUnit,n),o=this.axisLabelText(e,null,i);return new xn(e,o,t,null,i)},shouldRenderNote:function(t){var i=this.range();return i.min<=t&&t<=i.max},pan:function(t){var i=this.translateRange(t);return this.limitRange(i.min,i.max,this.totalMin,this.totalMax,i.offset)},pointsRange:function(t,i){var n=this.getValue(t),e=this.getValue(i),o=Math.min(n,e),s=Math.max(n,e);return{min:o,max:s}},zoomRange:function(t){var i=this,n=i.options,e=i.totalMin,o=i.totalMax,s=this.scaleRange(t),r=Hi(s.min,e,o),a=Hi(s.max,e,o),l=n.majorUnit,h=a>r&&n.min&&n.max&&qi(xt(n.max,l)-xt(n.min,l),Yt)<1,u=!(n.min===e&&n.max===o)&&qi(xt(a,l)-xt(r,l),Yt)>=1;if(h||u)return{min:r,max:a}},_minorIntervalOptions:function(t){var i=this.options,n=i.minorUnit,e=i.majorUnit,o=Math.pow(e,t),s=Math.pow(e,t+1),r=s-o,a=r/n;return{value:o,minorStep:a}},_lineOptions:function(){var t=this.options,i=t.reverse,n=t.vertical,e=n?gi:mi,o=this.lineBox(),s=n===i?1:-1,r=1===s?1:2,a=n?o.height():o.width(),l=s*(a/(this.logMax-this.logMin)),h=o[e+r];return{step:l,lineStart:h,lineBox:o}}}),w(te,{type:"log",majorUnit:$n,minorUnit:1,axisCrossingValue:1,vertical:!0,majorGridLines:{visible:!0,width:1,color:zt},zIndex:1,_deferLabels:!0}),ie={createGridLines:function(t){var i,n,e=this.options,o=Math.abs(this.box.center().y-t.lineBox().y1),s=[],r=!1;return e.majorGridLines.visible&&(i=this.majorGridLineAngles(t),r=!0,s=this.renderMajorGridLines(i,o,e.majorGridLines)),e.minorGridLines.visible&&(n=this.minorGridLineAngles(t,r),_i(s,this.renderMinorGridLines(n,o,e.minorGridLines,t,r))),s},renderMajorGridLines:function(t,i,n){return this.renderGridLines(t,i,n)},renderMinorGridLines:function(t,i,n,e,o){var s=this.radiusCallback&&this.radiusCallback(i,e,o);return this.renderGridLines(t,i,n,s)},renderGridLines:function(t,i,n,e){var o,s,r={stroke:{width:n.width,color:n.color,dashType:n.dashType}},a=this.box.center(),l=new Ut([a.x,a.y],i),h=this.gridLinesVisual();for(o=0;o<t.length;o++)s=new Mt(r),e&&(l.radius=e(t[o])),s.moveTo(l.center).lineTo(l.pointAt(t[o]+180)),h.append(s);return h.children},gridLineAngles:function(t,i,n,e,o){var s=this,r=this.intervals(i,n,e,o),a=t.options,l=a.visible&&(a.line||{}).visible!==!1;return b(r,function(t){var i=s.intervalAngle(t);if(!l||90!==i)return i})}},ne=Dn.extend({range:function(){return{min:0,max:this.options.categories.length}},reflow:function(t){this.box=t,this.reflowLabels()},lineBox:function(){return this.box},reflowLabels:function(){var t,i,n=this,e=this,o=e.labels,s=e.options.labels,r=s.skip||0,a=s.step||1,l=new Zi;for(t=0;t<o.length;t++)o[t].reflow(l),i=o[t].box,o[t].reflow(n.getSlot(r+t*a).adjacentBox(0,i.width(),i.height()))},intervals:function(t,i,n,e){var o,s,r,a,l,h,u,c,f;for(void 0===e&&(e=!1),o=this.options,s=o.categories.length,r=s/t||1,a=360/r,l=i||0,h=n||1,u=[],c=0,f=l;f<r;f+=h)c=o.reverse?360-f*a:f*a,c=qi(c,Dt)%360,e&&x(c,e)||u.push(c);return u},majorIntervals:function(){return this.intervals(1)},minorIntervals:function(){return this.intervals(.5)},intervalAngle:function(t){return(360+t+this.options.startAngle)%360},majorAngles:function(){var t=this;return b(this.majorIntervals(),function(i){return t.intervalAngle(i)})},createLine:function(){return[]},majorGridLineAngles:function(t){var i=this.options.majorGridLines;return this.gridLineAngles(t,1,i.skip,i.step)},minorGridLineAngles:function(t,i){var n=this.options,e=n.minorGridLines,o=n.majorGridLines,s=i?this.intervals(1,o.skip,o.step):null;return this.gridLineAngles(t,.5,e.skip,e.step,s)},radiusCallback:function(t,i,n){var e,o,s,r;if(i.options.type!==Pt)return e=Xi(360/(2*this.options.categories.length)),o=Math.cos(e)*t,s=this.majorAngles(),r=function(i){return!n&&x(i,s)?t:o}},createPlotBands:function(){var t,i,n,e,o,s,r,a=this,l=this.options.plotBands||[],h=this._plotbandGroup=new kt({zIndex:-1});for(t=0;t<l.length;t++)i=l[t],n=a.plotBandSlot(i),e=a.getSlot(i.from),o=i.from-Math.floor(i.from),n.startAngle+=o*e.angle,s=Math.ceil(i.to)-i.to,n.angle-=(s+o)*e.angle,r=en.current.createRing(n,{fill:{color:i.color,opacity:i.opacity},stroke:{opacity:i.opacity}}),h.append(r);this.appendVisual(h)},plotBandSlot:function(t){return this.getSlot(t.from,t.to-1)},getSlot:function(t,i){var n,e,o,s,r=this.options,a=r.justified,l=this.box,h=this.majorAngles(),u=h.length,c=360/u,f=t;return r.reverse&&!a&&(f=(f+1)%u),f=Hi(Math.floor(f),0,u-1),n=h[f],a&&(n-=c/2,n<0&&(n+=360)),e=Hi(Math.ceil(i||f),f,u-1),o=e-f+1,s=c*o,new $i(l.center(),0,l.height()/2,n,s)},slot:function(t,i){var n=this.getSlot(t,i),e=n.startAngle+180,o=e+n.angle;return new St.Arc([n.center.x,n.center.y],{startAngle:e,endAngle:o,radiusX:n.radius,radiusY:n.radius})},pointCategoryIndex:function(t){var i,n,e=this,o=this.options.categories.length,s=null;for(i=0;i<o;i++)if(n=e.getSlot(i),n.containsPoint(t)){s=i;break}return s}}),w(ne,{startAngle:90,labels:{margin:d(10)},majorGridLines:{visible:!0},justified:!0}),It(ne.prototype,ie),ee=wn.extend({init:function(t,i){wn.fn.init.call(this,t,i);var n=this.options;n.minorUnit=n.minorUnit||n.majorUnit/2},getDivisions:function(t){return Jn.prototype.getDivisions.call(this,t)-1},reflow:function(t){this.box=t,this.reflowLabels()},reflowLabels:function(){var t,i,n=this,e=this,o=e.options,s=e.labels,r=e.options.labels,a=r.skip||0,l=r.step||1,h=new Zi,u=this.intervals(o.majorUnit,a,l);for(t=0;t<s.length;t++)s[t].reflow(h),i=s[t].box,s[t].reflow(n.getSlot(u[t]).adjacentBox(0,i.width(),i.height()))},lineBox:function(){return this.box},intervals:function(t,i,n,e){var o,s,r,a,l,h,u;for(void 0===e&&(e=!1),o=this.options.min,s=this.getDivisions(t),r=[],a=i||0,l=n||1,h=a;h<s;h+=l)u=(360+o+h*t)%360,e&&x(u,e)||r.push(u);return r},majorIntervals:function(){return this.intervals(this.options.majorUnit)},minorIntervals:function(){return this.intervals(this.options.minorUnit)},intervalAngle:function(t){return(540-t-this.options.startAngle)%360},createLine:function(){return[]},majorGridLineAngles:function(t){var i=this.options.majorGridLines;return this.gridLineAngles(t,this.options.majorUnit,i.skip,i.step)},minorGridLineAngles:function(t,i){var n=this.options,e=n.minorGridLines,o=n.majorGridLines,s=i?this.intervals(n.majorUnit,o.skip,o.step):null;return this.gridLineAngles(t,n.minorUnit,e.skip,e.step,s)},plotBandSlot:function(t){return this.getSlot(t.from,t.to)},getSlot:function(t,i){var n,e=this,o=e.options,s=e.box,r=o.startAngle,a=Hi(t,o.min,o.max),l=Hi(i||a,a,o.max);return o.reverse&&(a*=-1,l*=-1),a=(540-a-r)%360,l=(540-l-r)%360,l<a&&(n=a,a=l,l=n),new $i(s.center(),0,s.height()/2,a,l-a)},slot:function(t,i){var n,e,o,s,r,a,l;return void 0===i&&(i=t),n=this.options,e=360-n.startAngle,o=this.getSlot(t,i),s=Math.min(t,i),r=Math.max(t,i),n.reverse?(a=s,l=r):(a=360-r,l=360-s),a=(a+e)%360,l=(l+e)%360,new St.Arc([o.center.x,o.center.y],{startAngle:a,endAngle:l,radiusX:o.radius,radiusY:o.radius})},getValue:function(t){var i=this.options,n=this.box.center(),e=t.x-n.x,o=t.y-n.y,s=Math.round(Ei(Math.atan2(o,e))),r=i.startAngle;return i.reverse||(s*=-1,r*=-1),(s+r+360)%360},valueRange:function(){return{min:0,max:2*Math.PI}}}),w(ee,{type:"polar",startAngle:0,reverse:!1,majorUnit:60,min:0,max:360,labels:{margin:d(10)},majorGridLines:{color:zt,visible:!0,width:1},minorGridLines:{color:"#aaa"}}),It(ee.prototype,ie,{createPlotBands:ne.prototype.createPlotBands,majorAngles:ne.prototype.majorAngles,range:Jn.prototype.range,labelsCount:Jn.prototype.labelsCount,createAxisLabel:Jn.prototype.createAxisLabel}),oe={options:{majorGridLines:{visible:!0}},createPlotBands:function(){var t,i,n,e,o,s,r,a,l,h,u=this,c=this.options,f=c.majorGridLines.type,d=c.plotBands;for(void 0===d&&(d=[]),t=this.plotArea.polarAxis,i=t.majorAngles(),n=t.box.center(),e=this._plotbandGroup=new kt({zIndex:-1}),o=0;o<d.length;o++)s=d[o],r={fill:{color:s.color,opacity:s.opacity},stroke:{opacity:s.opacity}},a=u.getSlot(s.from,s.to,!0),l=new $i(n,n.y-a.y2,n.y-a.y1,0,360),h=void 0,h=f===Pt?en.current.createRing(l,r):Mt.fromPoints(u.plotBandPoints(l,i),r).close(),e.append(h);this.appendVisual(e)},plotBandPoints:function(t,i){var n,e=[],o=[],s=[t.center.x,t.center.y],r=new Ut(s,t.innerRadius),a=new Ut(s,t.radius);for(n=0;n<i.length;n++)e.push(r.pointAt(i[n]+180)),o.push(a.pointAt(i[n]+180));return e.reverse(),e.push(e[0]),o.push(o[0]),o.concat(e)},createGridLines:function(t){var i,n=this.options,e=this.radarMajorGridLinePositions(),o=t.majorAngles(),s=t.box.center(),r=[];return n.majorGridLines.visible&&(r=this.renderGridLines(s,e,o,n.majorGridLines)),n.minorGridLines.visible&&(i=this.radarMinorGridLinePositions(),_i(r,this.renderGridLines(s,i,o,n.minorGridLines))),r},renderGridLines:function(t,i,n,e){var o,s,r,a,l,h,u,c={stroke:{width:e.width,color:e.color,dashType:e.dashType}},f=e.skip;for(void 0===f&&(f=0),o=e.step,void 0===o&&(o=0),s=this.gridLinesVisual(),r=f;r<i.length;r+=o)if(a=t.y-i[r],a>0)if(l=new Ut([t.x,t.y],a),e.type===Pt)s.append(new yt.Circle(l,c));else{for(h=new Mt(c),u=0;u<n.length;u++)h.lineTo(l.pointAt(n[u]+180));h.close(),s.append(h)}return s.children},getValue:function(t){var i,n,e,o,s,r,a,l=this.lineBox(),h=this.plotArea.polarAxis,u=h.majorAngles(),c=h.box.center(),f=t.distanceTo(c),d=f;return this.options.majorGridLines.type!==Pt&&u.length>1&&(i=t.x-c.x,n=t.y-c.y,e=(Ei(Math.atan2(n,i))+540)%360,u.sort(function(t,i){return vt(t,e)-vt(i,e)}),o=vt(u[0],u[1])/2,s=vt(e,u[0]),r=90-o,a=180-s-r,d=f*(Math.sin(Xi(a))/Math.sin(Xi(r)))),this.axisType().prototype.getValue.call(this,new Ji(l.x1,l.y2-d))}},se=Jn.extend({radarMajorGridLinePositions:function(){return this.getTickPositions(this.options.majorUnit)},radarMinorGridLinePositions:function(){var t=this.options,i=0;return t.majorGridLines.visible&&(i=t.majorUnit),this.getTickPositions(t.minorUnit,i)},axisType:function(){return Jn}}),It(se.prototype,oe),re=te.extend({radarMajorGridLinePositions:function(){var t=[];return this.traverseMajorTicksPositions(function(i){t.push(i)},this.options.majorGridLines),t},radarMinorGridLinePositions:function(){var t=[];return this.traverseMinorTicksPositions(function(i){t.push(i)},this.options.minorGridLines),t},axisType:function(){return te}}),It(re.prototype,oe),ae=.333,le=.01,he=Tt.extend({init:function(t){this.closed=t},process:function(t){var i,n,e,o,s,r,a,l,h,u,c,f,d,p,m=this,g=t.slice(0),x=[],v=this.closed,b=g.length;if(b>2&&(this.removeDuplicates(0,g),b=g.length),b<2||2===b&&g[0].equals(g[1]))return x;for(i=g[0],n=g[1],e=g[2],x.push(new jt(i));i.equals(g[b-1]);)v=!0,g.pop(),b--;if(2===b)return o=this.tangent(i,n,mi,gi),Ni(x).controlOut(this.firstControlPoint(o,i,n,mi,gi)),x.push(new jt(n,this.secondControlPoint(o,i,n,mi,gi))),x;for(v?(i=g[b-1],n=g[0],e=g[1],a=this.controlPoints(i,n,e),s=a[1],r=a[0]):(l=this.tangent(i,n,mi,gi),s=this.firstControlPoint(l,i,n,mi,gi)),h=s,u=0;u<=b-3;u++)m.removeDuplicates(u,g),b=g.length,u+3<=b&&(i=g[u],n=g[u+1],e=g[u+2],c=m.controlPoints(i,n,e),Ni(x).controlOut(h),h=c[1],f=c[0],x.push(new jt(n,f)));return v?(i=g[b-2],n=g[b-1],e=g[0],d=this.controlPoints(i,n,e),Ni(x).controlOut(h),x.push(new jt(n,d[0])),Ni(x).controlOut(d[1]),x.push(new jt(e,r))):(p=this.tangent(n,e,mi,gi),Ni(x).controlOut(h),x.push(new jt(e,this.secondControlPoint(p,n,e,mi,gi)))),x},removeDuplicates:function(t,i){for(;i[t+1]&&(i[t].equals(i[t+1])||i[t+1].equals(i[t+2]));)i.splice(t+1,1)},invertAxis:function(t,i,n){var e,o,s=!1;return t.x===i.x?s=!0:i.x===n.x?(i.y<n.y&&t.y<=i.y||n.y<i.y&&i.y<=t.y)&&(s=!0):(e=this.lineFunction(t,i),o=this.calculateFunction(e,n.x),t.y<=i.y&&n.y<=o||i.y<=t.y&&n.y>=o||(s=!0)),s},isLine:function(t,i,n){var e=this.lineFunction(t,i),o=this.calculateFunction(e,n.x);return t.x===i.x&&i.x===n.x||qi(o,1)===qi(n.y,1)},lineFunction:function(t,i){var n=(i.y-t.y)/(i.x-t.x),e=t.y-n*t.x;return[e,n]},controlPoints:function(t,i,n){var e,o,s,r,a,l,h=mi,u=gi,c=!1,f=!1;return this.isLine(t,i,n)?e=this.tangent(t,i,mi,gi):(o={x:this.isMonotonicByField(t,i,n,mi),y:this.isMonotonicByField(t,i,n,gi)},o.x&&o.y?(e=this.tangent(t,n,mi,gi),c=!0):(this.invertAxis(t,i,n)&&(h=gi,u=mi),o[h]?e=0:(s=n[u]<t[u]&&t[u]<=i[u]||t[u]<n[u]&&i[u]<=t[u]?bt((n[u]-t[u])*(i[h]-t[h])):-bt((n[h]-t[h])*(i[u]-t[u])),e=le*s,f=!0))),r=this.secondControlPoint(e,t,i,h,u),f&&(a=h,h=u,u=a),l=this.firstControlPoint(e,i,n,h,u),c&&(this.restrictControlPoint(t,i,r,e),this.restrictControlPoint(i,n,l,e)),[r,l]},restrictControlPoint:function(t,i,n,e){t.y<i.y?i.y<n.y?(n.x=t.x+(i.y-t.y)/e,n.y=i.y):n.y<t.y&&(n.x=i.x-(i.y-t.y)/e,n.y=t.y):n.y<i.y?(n.x=t.x-(t.y-i.y)/e,n.y=i.y):t.y<n.y&&(n.x=i.x+(t.y-i.y)/e,n.y=t.y)},tangent:function(t,i,n,e){var o,s=i[n]-t[n],r=i[e]-t[e];return o=0===s?0:r/s},isMonotonicByField:function(t,i,n,e){return n[e]>i[e]&&i[e]>t[e]||n[e]<i[e]&&i[e]<t[e]},firstControlPoint:function(t,i,n,e,o){var s=i[e],r=n[e],a=(r-s)*ae;return this.point(s+a,i[o]+a*t,e,o)},secondControlPoint:function(t,i,n,e,o){var s=i[e],r=n[e],a=(r-s)*ae;return this.point(r-a,n[o]-a*t,e,o)},point:function(t,i,n,e){var o=new St.Point;return o[n]=t,o[e]=i,o},calculateFunction:function(t,i){var n,e=t.length,o=0;for(n=0;n<e;n++)o+=Math.pow(i,n)*t[n];return o}}),At.Gradients=hn,kendo.deepExtend(kendo.dataviz,{constants:xi,services:Ai,autoMajorUnit:k,Point:Ji,Box:Zi,Ring:$i,Sector:tn,ShapeBuilder:en,ShapeElement:rn,ChartElement:on,BoxElement:sn,RootElement:un,FloatElement:cn,Text:dn,TextBox:mn,Title:gn,AxisLabel:xn,Axis:wn,Note:yn,CategoryAxis:Dn,DateCategoryAxis:Wn,DateValueAxis:Zn,NumericAxis:Jn,LogarithmicAxis:te,PolarAxis:ee,RadarCategoryAxis:ne,RadarNumericAxis:se,RadarLogarithmicAxis:re,CurveProcessor:he,rectToBox:L,addClass:n,removeClass:e,alignPathToPixel:o,clockwise:s,convertableToNumber:l,deepExtend:It,elementStyles:f,getSpacing:d,getTemplate:p,getter:Ct,grep:m,hasClasses:g,HashMap:Ii,inArray:x,interpolateValue:v,InstanceObserver:Ci,isArray:i,isFunction:Bt,isNumber:r,isObject:h,isString:a,map:b,mousewheelDelta:y,FontLoader:Qi,setDefaultOptions:w,sparseArrayLimits:M,styleValue:u,append:_i,bindEvents:zi,Class:Tt,defined:Vi,deg:Ei,elementOffset:Oi,elementSize:Di,eventElement:Gi,eventCoordinates:Fi,last:Ni,limitValue:Hi,logToConsole:kendo.logToConsole,objectKey:Yi,rad:Xi,round:qi,unbindEvents:Ki,valueOrDefault:Wi,absoluteDateDiff:I,addDuration:V,addTicks:B,ceilDate:O,dateComparer:D,dateDiff:G,dateEquals:N,dateIndex:Y,duration:X,floorDate:E,lteDateIndex:q,startOfWeek:P,toDate:C,parseDate:K,parseDates:W,toTime:F})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,i,n){(n||i)()}),function(t,define){define("dataviz/core/core.min",["dataviz/core/kendo-core.min"],t)}(function(){!function(t){var i=kendo.dataviz,n=i.services,e=kendo.drawing;i.SASS_THEMES=["sass","default-v2","bootstrap-v4","material-v2"],i.ExportMixin={extend:function(t,i){if(!t.exportVisual)throw Error("Mixin target has no exportVisual method defined.");t.exportSVG=this.exportSVG,t.exportImage=this.exportImage,t.exportPDF=this.exportPDF,i||(t.svg=this.svg,t.imageDataURL=this.imageDataURL)},exportSVG:function(t){return e.exportSVG(this.exportVisual(),t)},exportImage:function(t){return e.exportImage(this.exportVisual(t),t)},exportPDF:function(t){return e.exportPDF(this.exportVisual(),t)},svg:function(){if(e.svg.Surface)return e.svg.exportGroup(this.exportVisual());throw Error("SVG Export failed. Unable to export instantiate kendo.drawing.svg.Surface")},imageDataURL:function(){var i,n,o;if(!kendo.support.canvas)return null;if(e.canvas.Surface)return i=t("<div />").css({display:"none",width:this.element.width(),height:this.element.height()}).appendTo(document.body),n=new e.canvas.Surface(i[0]),n.draw(this.exportVisual()),o=n._rootElement.toDataURL(),n.destroy(),i.remove(),o;throw Error("Image Export failed. Unable to export instantiate kendo.drawing.canvas.Surface")}},n.IntlService.register({format:function(t){return kendo.format.apply(null,[t].concat(Array.prototype.slice.call(arguments,1)))},toString:kendo.toString,parseDate:kendo.parseDate}),n.TemplateService.register({compile:kendo.template}),i.Point2D=i.Point,i.Box2D=i.Box,i.mwDelta=function(t){return i.mousewheelDelta(t.originalEvent)}}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,i,n){(n||i)()}),function(t,define){define("kendo.dataviz.core.min",["dataviz/core/kendo-core.min","dataviz/core/core.min"],t)}(function(){},"function"==typeof define&&define.amd?define:function(t,i,n){(n||i)()});
//# sourceMappingURL=kendo.dataviz.core.min.js.map
