{"version": 3, "sources": ["kendo.dataviz.qrcode.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "undefined", "toDecimal", "parseInt", "toBitsString", "result", "toString", "Array", "splitInto", "n", "idx", "substring", "fillFunctionCell", "matrices", "bit", "x", "y", "fillDataCell", "maskPatternConditions", "generatePowersOfTwo", "power", "powersOfTwoResult", "powersOfTwo", "generateGeneratorPolynomials", "firstPolynomial", "secondPolynomial", "maxErrorCorrectionCodeWordsCount", "generatorPolynomials", "multiplyPolynomials", "multiplyByConstant", "polynomial", "getNumberAt", "char<PERSON>t", "scoreFinderPatternOccurance", "patterns", "scores", "rowColumn", "finderPatternValue", "scoreAdjacentSameBits", "previousBits", "adjacentBits", "calculateDarkModulesRatioScore", "darkModules", "total", "percent", "Math", "floor", "mod5", "previous", "abs", "next", "score", "min", "modeInstances", "mode", "FreeCellVisitor", "fillData", "padDataString", "xorPolynomials", "generateErrorCodewords", "getBlocks", "chooseMode", "getModes", "getDataCodewordsCount", "getVersion", "getDataString", "encodeFormatInformation", "encodeBCH", "dividePolynomials", "initMatrices", "addFormatInformation", "encodeVersionInformation", "addVersionInformation", "addCentricPattern", "addFinderSeparator", "addFinderPatterns", "addAlignmentPatterns", "addTimingFunctions", "scoreMaskMatrixes", "EncodingResult", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UTF8Encoder", "QRCodeDataEncoder", "encodeData", "QRCodeDefaults", "QRCode", "draw", "drawing", "dataviz", "Widget", "ui", "Box2D", "terminator", "NUMERIC", "ALPHA_NUMERIC", "BYTE", "1", "0", "irregularAlignmentPatternsStartDistance", "15", "16", "18", "19", "22", "24", "26", "28", "30", "31", "32", "33", "36", "37", "39", "40", "versionsCodewordsInformation", "L", "groups", "totalDataCodewords", "errorCodewordsPerBlock", "M", "Q", "H", "finderPattern", "alignmentPattern", "errorCorrectionPatterns", "formatMaskPattern", "formatGeneratorPolynomial", "versionGeneratorPolynomial", "paddingCodewords", "row", "column", "numberRegex", "alphaPattern", "alphaExclusiveSet", "alphaRegex", "RegExp", "alphaNumericRegex", "byteRegex", "initMinNumericBeforeAlpha", "initMinNumericBeforeByte", "initMinAlphaBeforeByte", "minNumericBeforeAlpha", "minNumericBeforeByte", "minAlphaBeforeByte", "round", "QRDataMode", "getVersionIndex", "version", "getBitsCharacterCount", "bitsInCharacterCount", "getModeCountString", "modeIndicator", "encode", "getStringBitsLength", "getValue", "modes", "character", "inputLength", "mod3", "characters", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "I", "J", "K", "N", "O", "P", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", " ", "%", "*", "+", "-", ".", "/", ":", "code", "Error", "matrix", "that", "startColumn", "dir", "c", "move", "getNextCell", "getNextRemainderCell", "blocks", "block", "codewordIdx", "cell", "blockIdx", "j", "cellVisitor", "splice", "dataString", "dataBitsCount", "terminatorIndex", "paddingCodewordIndex", "data", "errorCodewordsCount", "divisor", "generator", "concat", "generatorPolynomial", "steps", "errorCodewords", "dataStream", "versionCodewordsInformation", "dataBlock", "blockCodewordsCount", "groupBlocksCount", "messagePolynomial", "codeword", "groupIdx", "codewordStart", "dataBlocks", "errorBlocks", "versionGroups", "previousMode", "modeString", "numeric", "exec", "numericMatch", "alpha", "alphaMatch", "alphaNumeric", "alphaNumericMatch", "test", "nextMode", "substr", "ceil", "dataCodewordsCount", "errorCorrectionLevel", "format", "encodedString", "formatNumber", "codeLength", "generatorNumber", "polynomialLength", "valueNumber", "valueString", "numberX", "numberY", "y<PERSON><PERSON><PERSON>", "xLength", "modules", "formatString", "quotient", "mod", "x1", "y1", "x2", "y2", "pattern", "direction", "nextX", "nextY", "pointsCount", "points", "startDistance", "distance", "k", "minIdx", "adjacentSameBits", "Number", "MAX_VALUE", "getEncodingResult", "inputString", "encodingMode", "fn", "prototype", "utfBOM", "initialModeCountStringLength", "dataLength", "encodeCharacter", "significantOnes", "bytesCount", "getBytesCount", "bc", "ranges", "encoding", "toLowerCase", "indexOf", "optimalMatrix", "encoder", "encodingResult", "versionInformation", "DEFAULT_SIZE", "QUIET_ZONE_LENGTH", "DEFAULT_ERROR_CORRECTION_LEVEL", "DEFAULT_BACKGROUND", "DEFAULT_DARK_MODULE_COLOR", "MIN_BASE_UNIT_SIZE", "element", "call", "wrapper", "addClass", "surfaceWrap", "css", "appendTo", "surface", "Surface", "create", "type", "renderAs", "setOptions", "redraw", "_getSize", "clear", "createVisual", "visual", "getSize", "dimensions", "_resize", "_render", "exportVisual", "baseUnit", "quietZoneSize", "dataSize", "contentSize", "_value", "border", "padding", "borderWidth", "Group", "errorCorrection", "_calculateBaseUnit", "append", "_renderBackground", "_renderMatrix", "matrixSize", "path", "MultiPath", "fill", "color", "stroke", "moveTo", "lineTo", "close", "unpad", "Path", "fromRect", "toRect", "background", "name", "ExportMixin", "plugin", "QRCodeFunctions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,wBACH,qBACA,iBACDD,IACL,WAyhGE,MA9gGC,UAAUE,EAAGkF,GAypEV,QAASC,GAAUtC,GACf,MAAOuC,UAASvC,EAAO,GAE3B,QAASwC,GAAaxC,EAAO3B,GACzB,GAAIoE,KAAgBzC,GAAO0C,SAAS,EAIpC,OAHID,GAAOpE,OAASA,IAChBoE,EAAaE,MAAMtE,EAASoE,EAAOpE,OAAS,GAAGL,KAAK,GAAKyE,GAEtDA,EAEX,QAASG,GAAU1E,EAAK2E,GAEpB,IADA,GAAIJ,MAAaK,EAAM,EAChBA,EAAM5E,EAAIG,QACboE,EAAO3E,KAAKI,EAAI6E,UAAUD,EAAKA,EAAMD,IACrCC,GAAOD,CAEX,OAAOJ,GAuLX,QAASO,GAAiBC,EAAUC,EAAKC,EAAGC,GACxC,IAAK,GAAIjF,GAAI,EAAGA,EAAI8E,EAAS5E,OAAQF,IACjC8E,EAAS9E,GAAGgF,GAAGC,GAAKF,EAG5B,QAASG,GAAaJ,EAAUC,EAAKC,EAAGC,GACpC,IAAK,GAAIjF,GAAI,EAAGA,EAAImF,GAAsBjF,OAAQF,IAC9C8E,EAAS9E,GAAGgF,GAAGC,GAAKE,GAAsBnF,GAAGgF,EAAGC,GAAW,EAANF,EAAUX,SAASW,EAAK,IAuCrF,QAASK,KAAT,GACQd,GACKe,CAAT,KAASA,EAAQ,EAAGA,EAAQ,IAAKA,IAC7Bf,EAAwC,EAA/BgB,GAAkBD,EAAQ,GAC/Bf,EAAS,MACTA,EAAkB,IAATA,GAEbgB,GAAkBD,GAASf,EAC3BiB,GAAYjB,GAAUe,CAE1Bf,GAAwC,EAA/BgB,GAAkBD,EAAQ,GAAS,IAC5CC,GAAkBD,GAASf,EAC3BgB,OAAwB,EAsB5B,QAASE,KAAT,GAEab,GACDc,EAAiDC,EAFrDC,EAAmC,EACvC,KAAShB,EAAM,EAAGA,GAAOgB,EAAkChB,IACnDc,EAAkBG,GAAqBjB,EAAM,GAAIe,GAC7Cf,EACA,GAERiB,GAAqBjB,GAAOkB,EAAoBJ,EAAiBC,GAKzE,QAASI,GAAmBC,EAAYV,GACpC,GAAIf,MAAaK,EAAMoB,EAAW7F,OAAS,CAC3C,GACIoE,GAAOK,GAAOW,IAAmBS,EAAWpB,GAAOU,GAAS,KAC5DV,UACKoB,EAAWpB,KAAST,EAC7B,OAAOI,GAoIX,QAAS0B,GAAYjG,EAAK4E,GACtB,MAAOP,UAASrE,EAAIkG,OAAOtB,GAAM,IA2JrC,QAASuB,GAA4BvB,EAAKwB,EAAUC,EAAQC,EAAWtB,GACnEoB,EAASxB,GAAK0B,IAAcF,EAASxB,GAAK0B,IAAc,EAAItB,GAAO,IAC/DoB,EAASxB,GAAK0B,IAAcC,KAC5BF,EAAOzB,IAAQ,IAGvB,QAAS4B,GAAsB5B,EAAKyB,EAAQI,EAAczB,EAAK0B,EAAcJ,GACrEG,EAAa7B,GAAK0B,IAActB,EAChC0B,EAAa9B,GAAK0B,MAElBG,EAAa7B,GAAK0B,GAAatB,EAC3B0B,EAAa9B,GAAK0B,IAAc,IAChCD,EAAOzB,IAAQ,EAAI8B,EAAa9B,GAAK0B,GAAa,GAEtDI,EAAa9B,GAAK0B,GAAa,GAGvC,QAASK,GAA+BC,EAAaC,GACjD,GAAIC,GAAUC,KAAKC,MAAMJ,EAAcC,EAAQ,KAAMI,EAAOH,EAAU,EAAGI,EAAWH,KAAKI,IAAIL,EAAUG,EAAO,IAAKG,EAAOL,KAAKI,IAAIL,EAAU,EAAIG,EAAO,IAAKI,EAAQ,GAAKN,KAAKO,IAAIJ,EAAW,EAAGE,EAAO,EACxM,OAAOC,GArvFd,GA4zEOE,GACKC,EAGLC,EA0CAC,EAsBAC,EA4BAC,EAOA9B,EAiCA+B,EAYAC,EAuBAC,EAqBAC,EAkBAC,EAQAC,EAeAC,EAQAC,EAYAC,EAKAC,EAWAC,EAUAC,EAqBAC,EAGAC,EAUAC,EAYAC,EASAC,EAkBAC,EAsBAC,EAQAC,EA6DAC,EAIAC,EAMAC,EAmDAC,EAOAC,EAkBAC,EAQAC,EAp1FApI,EAAQD,OAAOC,MAAOG,EAASrC,EAAEqC,OAAQkI,EAAOrI,EAAMsI,QAASC,EAAUvI,EAAMuI,QAASC,EAASxI,EAAMyI,GAAGD,OAAQE,EAAQH,EAAQG,MAAOC,GAAa,OAAQC,GAAU,UAAWC,GAAgB,eAAgBC,GAAO,OAAQzE,IAAgB0E,EAAK,GAAK3E,IAAsB4E,EAAK,GAAKtE,KAEpR,EACA,IAGA,EACA,GACA,IAELuE,IACCC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,IACLC,KAEKC,GACIC,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,GAE5BC,GACIH,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BE,GACIJ,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BG,GACIL,SACQ,EACA,IAERC,mBAAoB,EACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BC,GACIH,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BE,GACIJ,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BG,GACIL,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BC,GACIH,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BE,GACIJ,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BG,GACIL,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BC,GACIH,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BE,GACIJ,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BG,GACIL,SACQ,EACA,IAERC,mBAAoB,GACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,MAERC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,GACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,GACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,KAERC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SACQ,EACA,KAERC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,IAE5BG,GACIL,SACQ,EACA,KAERC,mBAAoB,GACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,KAERC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SACQ,EACA,KAERC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,GACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,GACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,KAERC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,GACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,MAERC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,KAERC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SACQ,EACA,MAERC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BC,GACIH,SACQ,GACA,KAERC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SACQ,GACA,KAERC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SACQ,GACA,KAERC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,GACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,GACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,GACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,GACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SACQ,GACA,MAERC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,GACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,GACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,GACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,IACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,GACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,EACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,GACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,EACA,MAGA,GACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,GACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,EACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,MAI5BH,GACIC,SAEQ,GACA,MAGA,EACA,MAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BC,GACIH,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BE,GACIJ,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,IAE5BG,GACIL,SAEQ,GACA,KAGA,GACA,KAGRC,mBAAoB,KACpBC,uBAAwB,MAGjCI,IACC,EACA,EACA,EACA,EACA,GACDC,IACC,EACA,EACA,GACDC,IACCT,EAAG,KACHI,EAAG,KACHC,EAAG,KACHC,EAAG,MACJI,GAAoB,kBAAmBC,GAA4B,cAAeC,GAA6B,gBAAiBC,IAC/H,WACA,YACD5F,GAAqB,GAAInB,IACxB,SAAUgH,EAAKC,GACX,OAAQD,EAAMC,GAAU,IAAM,GAElC,SAAUD,GACN,MAAOA,GAAM,IAAM,GAEvB,SAAUA,EAAKC,GACX,MAAOA,GAAS,IAAM,GAE1B,SAAUD,EAAKC,GACX,OAAQD,EAAMC,GAAU,IAAM,GAElC,SAAUD,EAAKC,GACX,OAAQtF,KAAKC,MAAMoF,EAAM,GAAKrF,KAAKC,MAAMqF,EAAS,IAAM,IAAM,GAElE,SAAUD,EAAKC,GACX,MAAOD,GAAMC,EAAS,EAAID,EAAMC,EAAS,IAAM,GAEnD,SAAUD,EAAKC,GACX,OAAQD,EAAMC,EAAS,EAAID,EAAMC,EAAS,GAAK,IAAM,GAEzD,SAAUD,EAAKC,GACX,QAASD,EAAMC,GAAU,EAAID,EAAMC,EAAS,GAAK,IAAM,IAE5DC,GAAc,OAAQC,GAAe,kBAAmBC,GAAoB,eAAgBC,GAAiBC,OAAO,KAAOF,GAAoB,MAAOG,GAAwBD,OAAO,KAAOH,GAAe,MAAOK,GAAgBF,OAAO,MAAQH,GAAe,MAAOM,GAA4B,EAAGC,GAA2B,EAAGC,GAAyB,EAAGC,GAAwB,GAAIC,GAAuB,EAAGC,GAAqB,GAAIC,GAAQpG,KAAKoG,MAmB/bC,GAAajM,EAAME,MAAMC,QACzB+L,gBAAiB,SAAUC,GACvB,MAAIA,GAAU,GACH,EACAA,EAAU,GACV,EAEJ,GAEXC,sBAAuB,SAAUD,GAC7B,GAAI9F,GAAO/F,IACX,OAAO+F,GAAKgG,qBAAqBhG,EAAK6F,gBAAgBC,GAAW,MAErEG,mBAAoB,SAAUtN,EAAQmN,GAClC,GAAI9F,GAAO/F,IACX,OAAO+F,GAAKkG,cAAgBpJ,EAAanE,EAAQqH,EAAK+F,sBAAsBD,KAEhFK,OAAQ,aAERC,oBAAqB,aAErBC,SAAU,aAEVH,cAAe,GACfF,0BAEAM,KACJA,IAAM/D,IAAWqD,GAAW9L,QACxBkM,sBACI,GACA,GACA,IAEJE,cAAe,OACfG,SAAU,SAAUE,GAChB,MAAO1J,UAAS0J,EAAW,KAE/BJ,OAAQ,SAAU3N,EAAKsN,GAAf,GAEKrN,GADLuH,EAAO/F,KAAM9B,EAAQ+E,EAAU1E,EAAK,GAAIuE,EAASiD,EAAKiG,mBAAmBzN,EAAIG,OAAQmN,EACzF,KAASrN,EAAI,EAAGA,EAAIN,EAAMQ,OAAS,EAAGF,IAClCsE,GAAUD,EAAa3E,EAAMM,GAAI,GAErC,OAAOsE,GAASD,EAAa3E,EAAMM,GAAI,EAAI,EAAIN,EAAMM,GAAGE,SAE5DyN,oBAAqB,SAAUI,EAAaV,GACxC,GAAIW,GAAOD,EAAc,CACzB,OAAO,GAAIvM,KAAK8L,sBAAsBD,GAAW,GAAKvG,KAAKC,MAAMgH,EAAc,GAAK,EAAIC,GAAiB,IAATA,EAAa,EAAI,MAGzHH,GAAM9D,IAAiBoD,GAAW9L,QAC9B4M,YACI/D,EAAK,EACLD,EAAK,EACLiE,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLrD,EAAK,GACLsD,EAAK,GACLC,EAAK,GACLC,EAAK,GACL9D,EAAK,GACLI,EAAK,GACL2D,EAAK,GACLC,EAAK,GACLC,EAAK,GACL5D,EAAK,GACL6D,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,IAAK,GACLhR,EAAK,GACLiR,IAAK,GACLC,IAAK,GACLC,IAAK,GACLC,IAAK,GACLC,IAAK,GACLC,IAAK,GACLC,IAAK,IAEThD,sBACI,EACA,GACA,IAEJE,cAAe,OACfG,SAAU,SAAUE,GAChB,MAAOtM,MAAKyM,WAAWH,IAE3BJ,OAAQ,SAAU3N,EAAKsN,GAAf,GAC+FxL,GAC1F7B,EADLuH,EAAO/F,KAAM9B,EAAQ+E,EAAU1E,EAAK,GAAIuE,EAASiD,EAAKiG,mBAAmBzN,EAAIG,OAAQmN,EACzF,KAASrN,EAAI,EAAGA,EAAIN,EAAMQ,OAAS,EAAGF,IAClC6B,EAAQ,GAAK0F,EAAKqG,SAASlO,EAAMM,GAAGiG,OAAO,IAAMsB,EAAKqG,SAASlO,EAAMM,GAAGiG,OAAO,IAC/E3B,GAAUD,EAAaxC,EAAO,GAGlC,OADAA,GAA2B,GAAnBnC,EAAMM,GAAGE,OAAc,GAAKqH,EAAKqG,SAASlO,EAAMM,GAAGiG,OAAO,IAAMsB,EAAKqG,SAASlO,EAAMM,GAAGiG,OAAO,IAAMsB,EAAKqG,SAASlO,EAAMM,GAAGiG,OAAO,IACnI3B,EAASD,EAAaxC,EAAO,EAAI,EAAInC,EAAMM,GAAGE,SAEzDyN,oBAAqB,SAAUI,EAAaV,GACxC,MAAO,GAAI7L,KAAK8L,sBAAsBD,GAAW,GAAKvG,KAAKC,MAAMgH,EAAc,GAAK,GAAKA,EAAc,MAG/GF,GAAM7D,IAAQmD,GAAW9L,QACrBkM,sBACI,EACA,GACA,IAEJE,cAAe,OACfG,SAAU,SAAUE,GAChB,GAAI0C,GAAO1C,EAAU3N,WAAW,EAChC,IAAIqQ,GAAQ,KAAO,KAAOA,GAAQA,GAAQ,IACtC,MAAOA,EAEP,MAAUC,OAAM,0BAA4B3C,IAGpDJ,OAAQ,SAAU3N,EAAKsN,GAAf,GAEKrN,GADLuH,EAAO/F,KAAM8C,EAASiD,EAAKiG,mBAAmBzN,EAAIG,OAAQmN,EAC9D,KAASrN,EAAI,EAAGA,EAAID,EAAIG,OAAQF,IAC5BsE,GAAUD,EAAakD,EAAKqG,SAAS7N,EAAIkG,OAAOjG,IAAK,EAEzD,OAAOsE,IAEXqJ,oBAAqB,SAAUI,EAAaV,GACxC,MAAO,GAAI7L,KAAK8L,sBAAsBD,GAAW,EAAIU,KAGzDzG,IACJ,KAASC,IAAQsG,IACbvG,EAAcC,GAAQ,GAAIsG,IAAMtG,EAEhCC,GAAkB,SAAUkJ,GAC5B,GAAIC,GAAOnP,KAAM2K,EAAMuE,EAAOxQ,OAAS,EAAGkM,EAASsE,EAAOxQ,OAAS,EAAG0Q,EAAcxE,EAAQyE,KAAUC,EAAI,CAC1GH,GAAKI,KAAO,WACR5E,GAAO0E,EAAMC,EACbA,GAAK,EACL1E,EAASwE,EAAcE,GAE3BH,EAAKK,YAAc,WACf,KAAON,EAAOvE,GAAKC,KAAYlI,GAC3ByM,EAAKI,QACD5E,EAAM,GAAKA,GAAOuE,EAAOxQ,UACzB2Q,GAAOA,EACPD,GAA8B,GAAfA,EAAmB,EAAI,EACtCxE,EAASwE,EACTzE,EAAM0E,EAAM,EAAIH,EAAOxQ,OAAS,EAAI,EAG5C,QACIiM,IAAKA,EACLC,OAAQA,IAGhBuE,EAAKM,qBAAuB,WAExB,GADAN,EAAKI,OACDL,EAAOvE,GAAKC,KAAYlI,EACxB,OACIiI,IAAKA,EACLC,OAAQA,KAepB3E,EAAW,SAAU3C,EAAUoM,GAApB,GACyCC,GAAOC,EAAaC,EAC/DC,EAIQtR,EACIuR,EANjBC,EAAc,GAAIhK,GAAgB1C,EAAS,GAC/C,KAASwM,EAAW,EAAGA,EAAWJ,EAAOhR,OAAQoR,IAG7C,IAFAH,EAAQD,EAAOI,GACfF,EAAc,EACPD,EAAMjR,OAAS,GAAG,CACrB,IAASF,EAAI,EAAGA,EAAImR,EAAMjR,OAAQF,IAC9B,IAASuR,EAAI,EAAGA,EAAI,EAAGA,IACnBF,EAAOG,EAAYR,cACnB9L,EAAaJ,EAAUqM,EAAMnR,GAAGoR,GAAanL,OAAOsL,GAAIF,EAAKlF,IAAKkF,EAAKjF,OAI/E,KADAgF,IACOD,EAAM,IAAMC,GAAeD,EAAM,GAAGjR,QACvCiR,EAAMM,OAAO,EAAG,GAI5B,KAAOJ,EAAOG,EAAYP,wBACtB/L,EAAaJ,EAAU,EAAGuM,EAAKlF,IAAKkF,EAAKjF,SAG7C1E,EAAgB,SAAUgK,EAAYnG,GAEtC,IADA,GAAIoG,GAAqC,EAArBpG,EAAwBqG,EAAkB,EAAGC,EAAuB,EACjFH,EAAWxR,OAASyR,GAAiBC,EAAkB/H,GAAW3J,QACrEwR,GAAc7H,GAAW5D,OAAO2L,IAKpC,KAHIF,EAAWxR,OAAS,IAAM,IAC1BwR,GAAkBlN,MAAM,EAAIkN,EAAWxR,OAAS,GAAGL,KAAK,MAErD6R,EAAWxR,OAASyR,GACvBD,GAAcxF,GAAiB2F,GAC/BA,GAAwB,CAE5B,OAAOH,IAgBP/J,EAAiB,SAAU3C,EAAGC,GAAb,GAERjF,GADLsE,KAAaK,EAAMK,EAAE9E,OAAS,CAClC,KAASF,EAAI2E,EAAK3E,GAAK,EAAGA,IACtBsE,EAAOtE,GAAKgF,EAAEhF,GAAKiF,EAAEjF,EAEzB,OAAOsE,IAEPuB,EAAsB,SAAUb,EAAGC,GAAb,GAEbjF,GACIuR,EAFTjN,IACJ,KAAStE,EAAI,EAAGA,EAAIgF,EAAE9E,OAAQF,IAC1B,IAASuR,EAAI,EAAGA,EAAItM,EAAE/E,OAAQqR,IAEtBjN,EAAOtE,EAAIuR,GADXjN,EAAOtE,EAAIuR,KAAOrN,GACDc,EAAEhF,IAAMiF,EAAEsM,IAAM,EAAItM,EAAEsM,GAAK,IAAM,IAElChM,GAAYD,GAAkBhB,EAAOtE,EAAIuR,IAAMjM,IAAmBN,EAAEhF,GAAKiF,EAAEsM,IAAM,KAI7G,OAAOjN,IAYXc,IACAI,IASIoC,EAAyB,SAAUkK,EAAMC,GACzC,GAAoPC,GAASrN,EAAzPsN,EAAYrM,GAAqBmM,EAAsB,GAAIzN,EAAaE,MAAMuN,GAAqBG,OAAOJ,GAAOK,EAA0B3N,MAAMF,EAAOpE,OAAS+R,EAAU/R,QAAQgS,OAAOD,GAAYG,EAAQN,EAAK5R,OAAQmS,IAC/N,KAAK1N,EAAM,EAAGA,EAAMyN,EAAOzN,IACvBqN,EAAUlM,EAAmBqM,EAAqB5M,GAAYjB,EAAOA,EAAOpE,OAAS,KACrFiS,EAAoBV,OAAO,EAAG,GAC9BnN,EAASqD,EAAeqK,EAAS1N,EAErC,KAAKK,EAAML,EAAOpE,OAAS,EAAGyE,GAAO,EAAGA,IACpC0N,EAAeN,EAAsB,EAAIpN,GAAON,EAAaC,EAAOK,GAAM,EAE9E,OAAO0N,IAEPxK,EAAY,SAAUyK,EAAYC,GAAtB,GAC8CC,GAA+DC,EAAqBC,EAAkBC,EAAmBC,EAC1KC,EAEIvB,EAIIF,EAPb0B,EAAgB,EAAGC,KAAiBC,KAA6BC,EAAgBV,EAA4BjH,MACjH,KAASuH,EAAW,EAAGA,EAAWI,EAAc/S,OAAQ2S,IAEpD,IADAH,EAAmBO,EAAcJ,GAAU,GAClCvB,EAAW,EAAGA,EAAWoB,EAAkBpB,IAAY,CAI5D,IAHAmB,EAAsBQ,EAAcJ,GAAU,GAC9CL,KACAG,KACSvB,EAAc,EAAGA,GAAeqB,EAAqBrB,IAC1DwB,EAAWN,EAAW1N,UAAUkO,EAAeA,EAAgB,GAC/DN,EAAU7S,KAAKiT,GACfD,EAAkBF,EAAsBrB,GAAejN,EAAUyO,GACjEE,GAAiB,CAErBC,GAAWpT,KAAK6S,GAChBQ,EAAYrT,KAAKiI,EAAuB+K,EAAmBJ,EAA4B/G,yBAG/F,OACIuH,EACAC,IAGJlL,EAAa,SAAU/H,EAAKgN,EAAuBC,EAAsBC,EAAoBiG,GAC7F,GAAsP3L,GAAM4L,EAAxPC,EAAU/G,GAAYgH,KAAKtT,GAAMuT,EAAeF,EAAUA,EAAQ,GAAK,GAAIG,EAAQ/G,GAAW6G,KAAKtT,GAAMyT,EAAaD,EAAQA,EAAM,GAAK,GAAIE,EAAe/G,GAAkB2G,KAAKtT,GAAM2T,EAAoBD,EAAeA,EAAa,GAAK,EAelP,OAdIH,KAAiBA,EAAapT,QAAU6M,GAAyBhN,EAAIG,QAAUoT,EAAapT,QAAUoT,EAAapT,QAAU8M,IAAyBN,GAAkBiH,KAAK5T,EAAIkG,OAAOqN,EAAapT,WACrMqH,EAAOuC,GACPqJ,EAAaG,GACNI,IAAsB3T,EAAIG,QAAUwT,EAAkBxT,QAAUwT,EAAkBxT,QAAU+M,GAAsBiG,GAAgBnJ,KACzIxC,EAAOwC,GACPoJ,EAAaG,GAAgBE,IAE7BjM,EAAOyC,GAEHmJ,EADAO,EACaA,EAAoB/G,GAAU0G,KAAKtT,EAAI6E,UAAU8O,EAAkBxT,SAAS,GAE5EyM,GAAU0G,KAAKtT,GAAK,KAIrCwH,KAAMA,EACN4L,WAAYA,IAGhBpL,EAAW,SAAUhI,GAAV,GACKmT,GAKRU,EALJ/F,KAA0BlJ,EAAM,CAIpC,KAHAkJ,EAAMlO,KAAKmI,EAAW/H,EAAK6M,GAA2BC,GAA0BC,GAAwBoG,IACxGA,EAAerF,EAAM,GAAGtG,KACxBxH,EAAMA,EAAI8T,OAAOhG,EAAM,GAAGsF,WAAWjT,QAC9BH,EAAIG,OAAS,GACZ0T,EAAW9L,EAAW/H,EAAKgN,GAAuBC,GAAsBC,GAAoBiG,GAC5FU,EAASrM,MAAQ2L,GACjBA,EAAeU,EAASrM,KACxBsG,EAAMlO,KAAKiU,GACXjP,KAEAkJ,EAAMlJ,GAAKwO,YAAcS,EAAST,WAEtCpT,EAAMA,EAAI8T,OAAOD,EAAST,WAAWjT,OAEzC,OAAO2N,IAEP7F,EAAwB,SAAU6F,GAAV,GACRtG,GACPvH,EADLE,EAAS,CACb,KAASF,EAAI,EAAGA,EAAI6N,EAAM3N,OAAQF,IAC9BuH,EAAOD,EAAcuG,EAAM7N,GAAGuH,MAC9BrH,GAAUqH,EAAKoG,oBAAoBE,EAAM7N,GAAGmT,WAAWjT,OAE3D,OAAO4G,MAAKgN,KAAK5T,EAAS,IAE1B+H,EAAa,SAAU8L,EAAoBC,GAC3C,GAAIhP,GAAI,EAAGC,EAAImG,GAA6BlL,OAAS,EAAGmN,EAAUvG,KAAKC,MAAMqE,GAA6BlL,OAAS,EACnH,GACQ6T,GAAqB3I,GAA6BiC,GAAS2G,GAAsBzI,mBACjFtG,EAAIoI,EAEJrI,EAAIqI,EAERA,EAAUrI,EAAI8B,KAAKC,OAAO9B,EAAID,GAAK,SAC9BC,EAAID,EAAI,EACjB,OAAI+O,IAAsB3I,GAA6BpG,GAAGgP,GAAsBzI,mBACrE8B,EAAU,EAEdpI,EAAI,GAEXiD,EAAgB,SAAU2F,EAAOR,GAAjB,GACK9F,GACZvH,EADL0R,EAAa,EACjB,KAAS1R,EAAI,EAAGA,EAAI6N,EAAM3N,OAAQF,IAC9BuH,EAAOD,EAAcuG,EAAM7N,GAAGuH,MAC9BmK,GAAcnK,EAAKmG,OAAOG,EAAM7N,GAAGmT,WAAY9F,EAEnD,OAAOqE,IAEPvJ,EAA0B,SAAU8L,GAAV,GACYC,GAM7BlU,EANLmU,EAAehQ,EAAU8P,GAAwB3P,EAAS,EAC9D,IAAqB,IAAjB6P,EACA,MAAO,iBAIX,KAFID,EAAgB9L,EAAUjE,EAAU8P,GAASjI,GAA2B,IAEnEhM,EAAI,EAAGA,EAAIkU,EAAchU,OAAQF,IACtCsE,GAAU4P,EAAcjO,OAAOjG,GAAK+L,GAAkB9F,OAAOjG,EAEjE,OAAOsE,IAEP8D,EAAY,SAAUvG,EAAOsQ,EAAqBiC,GAClD,GAAIC,GAAkBlQ,EAAUgO,GAAsBmC,EAAmBnC,EAAoBjS,OAAS,EAAGqU,EAAc1S,GAASyS,EAAkBpU,EAASkU,EAAaE,EAAkBE,EAAcnQ,EAAaxC,EAAO3B,GAASoE,EAAS+D,EAAkBkM,EAAaF,EAE7Q,OADA/P,GAASkQ,EAAcnQ,EAAaC,EAAQgQ,IAG5CjM,EAAoB,SAAUoM,EAASC,GACvC,GAAIC,GAAUD,EAAQnQ,SAAS,GAAGrE,OAAQ0U,EAAUH,EAAQlQ,SAAS,GAAGrE,MACxE,GACIuU,IAAWC,GAAWE,EAAUD,EAChCC,EAAUH,EAAQlQ,SAAS,GAAGrE,aACzB0U,GAAWD,EACpB,OAAOF,IAKPnM,EAAe,SAAU+E,GAAV,GAENrN,GAEIuR,EAHTzM,KAAe+P,EAAU,GAAK,EAAIxH,CACtC,KAASrN,EAAI,EAAGA,EAAImF,GAAsBjF,OAAQF,IAE9C,IADA8E,EAAS9E,GAASwE,MAAMqQ,GACftD,EAAI,EAAGA,EAAIsD,EAAStD,IACzBzM,EAAS9E,GAAGuR,GAAS/M,MAAMqQ,EAGnC,OAAO/P,IAEPyD,EAAuB,SAAUzD,EAAUgQ,GAC3C,GAA0B9P,GAAGC,EAAzByL,EAAS5L,EAAS,GAAUH,EAAM,EAAGzE,EAAS4U,EAAa5U,MAC/D,KAAK8E,EAAI,EAAGC,EAAI,EAAGD,GAAK,EAAGA,IACb,IAANA,GACAH,EAAiBC,EAAUkB,EAAY8O,EAAc5U,EAAS,EAAIyE,KAAQK,EAAGC,EAGrF,KAAKD,EAAI,EAAGC,EAAI,EAAGA,GAAK,EAAGA,IACb,IAANA,GACAJ,EAAiBC,EAAUkB,EAAY8O,EAAc5U,EAAS,EAAIyE,KAAQK,EAAGC,EAIrF,KADAN,EAAM,EACDM,EAAIyL,EAAOxQ,OAAS,EAAG8E,EAAI,EAAGC,GAAKyL,EAAOxQ,OAAS,EAAG+E,IACvDJ,EAAiBC,EAAUkB,EAAY8O,EAAc5U,EAAS,EAAIyE,KAAQK,EAAGC,EAGjF,KADAJ,EAAiBC,EAAU,EAAG4L,EAAOxQ,OAAS,EAAG,GAC5C8E,EAAI0L,EAAOxQ,OAAS,EAAG+E,EAAI,EAAGD,EAAI0L,EAAOxQ,OAAQ8E,IAClDH,EAAiBC,EAAUkB,EAAY8O,EAAc5U,EAAS,EAAIyE,KAAQK,EAAGC,IAGjFuD,EAA2B,SAAU6E,GACrC,MAAOjF,GAAUiF,EAASpB,GAA4B,KAEtDxD,EAAwB,SAAU3D,EAAU4M,GAApB,GACiFqD,GAAUC,EAAKnT,EAC/G8C,EADL+L,EAAS5L,EAAS,GAAI+P,EAAUnE,EAAOxQ,OAAQ+U,EAAK,EAAGC,EAAKL,EAAU,GAAIM,EAAKN,EAAU,GAAIO,EAAK,CACtG,KAASzQ,EAAM,EAAGA,EAAM+M,EAAWxR,OAAQyE,IACvCoQ,EAAWjO,KAAKC,MAAMpC,EAAM,GAC5BqQ,EAAMrQ,EAAM,EACZ9C,EAAQmE,EAAY0L,EAAYA,EAAWxR,OAASyE,EAAM,GAC1DE,EAAiBC,EAAUjD,EAAOoT,EAAKF,EAAUG,EAAKF,GACtDnQ,EAAiBC,EAAUjD,EAAOsT,EAAKH,EAAKI,EAAKL,IAGrDrM,EAAoB,SAAU5D,EAAUuQ,EAASrQ,EAAGC,GAAhC,GACwCpD,GACnD7B,EACIuR,EAFThQ,EAAO8T,EAAQnV,OAAS,EAAGA,EAASmV,EAAQnV,OAAS,CACzD,KAASF,EAAI,EAAGA,EAAIqV,EAAQnV,OAAQF,IAChC,IAASuR,EAAIvR,EAAGuR,EAAIhQ,EAAOvB,EAAGuR,IAC1B1P,EAAQwT,EAAQrV,GAChB6E,EAAiBC,EAAUjD,EAAOmD,EAAIuM,EAAGtM,EAAIjF,GAC7C6E,EAAiBC,EAAUjD,EAAOmD,EAAIhF,EAAGiF,EAAIsM,GAC7C1M,EAAiBC,EAAUjD,EAAOmD,EAAI9E,EAASqR,EAAGtM,EAAI/E,EAASF,GAC/D6E,EAAiBC,EAAUjD,EAAOmD,EAAI9E,EAASF,EAAGiF,EAAI/E,EAASqR,IAIvE5I,EAAqB,SAAU7D,EAAUwQ,EAAWtQ,EAAGC,GACvD,GAAIsQ,GAAQvQ,EAAGwQ,EAAQvQ,EAAGyL,EAAS5L,EAAS,EAC5C,GACID,GAAiBC,EAAU,EAAGyQ,EAAOtQ,GACrCJ,EAAiBC,EAAU,EAAGE,EAAGwQ,GACjCD,GAASD,EAAU,GACnBE,GAASF,EAAU,SACdC,GAAS,GAAKA,EAAQ7E,EAAOxQ,SAEtC0I,EAAoB,SAAU9D,GAC9B,GAAI+P,GAAU/P,EAAS,GAAG5E,MAC1BwI,GAAkB5D,EAAU8G,GAAe,EAAG,GAC9CjD,EAAmB7D,UAGhB,EAAG,GACN4D,EAAkB5D,EAAU8G,GAAeiJ,EAAU,EAAG,GACxDlM,EAAmB7D,GACf,MAED+P,EAAU,EAAG,GAChBnM,EAAkB5D,EAAU8G,GAAe,EAAGiJ,EAAU,GACxDlM,EAAmB7D,MAEf,GACD,EAAG+P,EAAU,IAEhBhM,EAAuB,SAAU/D,EAAUuI,GAApB,GAInBqD,GAAsBmE,EAAyBY,EAAuCC,EAAcC,EAAeC,EAAUjR,EAUxH3E,EACIuR,CAdb,MAAIlE,EAAU,GAAd,CAUA,IAPIqD,EAAS5L,EAAS,GAAI+P,EAAUnE,EAAOxQ,OAAQuV,EAAc3O,KAAKC,MAAMsG,EAAU,GAAIqI,GAAU,GAA6B/Q,EAAM,GACnIgR,EAAgBxL,GAAwCkD,IACxDuI,GAAYf,EAAU,GAAKc,GAAiBF,EAE5CE,EAAgBC,GAAYf,EAAU,KAAOY,EAAc,GAE/DC,EAAO/V,KAAK+V,EAAO/Q,KAASgR,GACrBD,EAAO/Q,GAAOiR,EAAWf,GAC5Ba,EAAO/V,KAAK+V,EAAO/Q,KAASiR,EAEhC,KAAS5V,EAAI,EAAGA,EAAI0V,EAAOxV,OAAQF,IAC/B,IAASuR,EAAI,EAAGA,EAAImE,EAAOxV,OAAQqR,IAC3Bb,EAAOgF,EAAO1V,IAAI0V,EAAOnE,MAAQrN,GACjCwE,EAAkB5D,EAAU+G,GAAkB6J,EAAO1V,GAAK,EAAG0V,EAAOnE,GAAK,KAKrFzI,EAAqB,SAAUhE,GAAV,GAEZ9E,GADLmM,EAAM,EAAGC,EAAS,EAAGvK,EAAQ,EAAGgT,EAAU/P,EAAS,GAAG5E,MAC1D,KAASF,EAAI,EAAGA,EAAI6U,EAAU,EAAG7U,IAC7B6E,EAAiBC,EAAUjD,EAAOsK,EAAKnM,GACvC6E,EAAiBC,EAAUjD,EAAO7B,EAAGoM,GACrCvK,GAAS,GAGbkH,EAAoB,SAAUjE,GAAV,GACwE4L,GAAQ1Q,EAevFuR,EACIsE,EAabjP,EAA2BkP,EAAQzO,EA7BnCjB,KAAaI,KAAmBG,KAAkBR,KAAe4P,KAAkC5J,EAAM,EAAGC,EAAS,EAAGyI,EAAU/P,EAAS,GAAG5E,MAClJ,KAAKF,EAAI,EAAGA,EAAI8E,EAAS5E,OAAQF,IAC7BoG,EAAOpG,GAAK,EACZ2G,EAAY3G,GAAK,EACjB+V,EAAiB/V,IACb,EACA,GAEJmG,EAASnG,IACL,EACA,GAEJwG,EAAaxG,KAEjB,KAAKA,EAAI,EAAGA,EAAI6U,EAAS7U,IACrB,IAASuR,EAAI,EAAGA,EAAIsD,EAAStD,IACzB,IAASsE,EAAI,EAAGA,EAAI/Q,EAAS5E,OAAQ2V,IACjCnF,EAAS5L,EAAS+Q,GAClBlP,EAAYkP,IAAMzR,SAASsM,EAAO1Q,GAAGuR,GAAI,IACrC/K,EAAaqP,GAAG1J,KAASuE,EAAO1Q,GAAGuR,IAAMvR,EAAI,EAAI6U,GAAWtD,EAAI,GAAK,GAAKb,EAAO1Q,EAAI,GAAGuR,IAAM/K,EAAaqP,GAAG1J,IAAQuE,EAAO1Q,EAAI,GAAGuR,EAAI,IAAM/K,EAAaqP,GAAG1J,KAC9J/F,EAAOyP,IAAM,GAEjB3P,EAA4B2P,EAAG1P,EAAUC,EAAQ+F,EAAKuE,EAAO1Q,GAAGuR,IAChErL,EAA4B2P,EAAG1P,EAAUC,EAAQgG,EAAQsE,EAAOa,GAAGvR,IACnEuG,EAAsBsP,EAAGzP,EAAQI,EAAckK,EAAO1Q,GAAGuR,GAAIwE,EAAkB5J,GAC/E5F,EAAsBsP,EAAGzP,EAAQI,EAAckK,EAAOa,GAAGvR,GAAI+V,EAAkB3J,EAK3F,KADIxF,EAAQiO,EAAUA,EAAiBxN,EAAM2O,OAAOC,UAC/CjW,EAAI,EAAGA,EAAIoG,EAAOlG,OAAQF,IAC3BoG,EAAOpG,IAAM0G,EAA+BC,EAAY3G,GAAI4G,GACxDR,EAAOpG,GAAKqH,IACZA,EAAMjB,EAAOpG,GACb8V,EAAS9V,EAGjB,OAAO8V,IAuBP9M,EAAiB,SAAU0I,EAAYrE,GACvC7L,KAAKkQ,WAAaA,EAClBlQ,KAAK6L,QAAUA,GAEfpE,EAAa,WACbzH,KAAK0U,kBAAoB,SAAUC,EAAanC,GAC5C,GAAInG,GAAQ9F,EAASoO,GAAcpC,EAAqB/L,EAAsB6F,GAAQR,EAAUpF,EAAW8L,EAAoBC,GAAuBtC,EAAaxJ,EAAc2F,EAAOR,EACxL,OAAO,IAAIrE,GAAe0I,EAAYrE,KAG1CnE,EAAc,WACd1H,KAAK+F,KAAOD,EAAc9F,KAAK4U,eAEnClN,EAAYmN,GAAKnN,EAAYoN,WACzBF,aAAcpM,GACduM,OAAQ,2BACRC,6BAA8B,GAC9BN,kBAAmB,SAAUC,EAAanC,GACtC,GAAIrD,GAAOnP,KAAMsQ,EAAOnB,EAAKjD,OAAOyI,GAAcpC,EAAqBpD,EAAK3I,sBAAsB8J,GAAOzE,EAAUpF,EAAW8L,EAAoBC,GAAuBtC,EAAaf,EAAKpJ,KAAKiG,mBAAmBsE,EAAK5R,OAAS,EAAGmN,GAAWyE,CAC/O,OAAO,IAAI9I,GAAe0I,EAAYrE,IAE1CrF,sBAAuB,SAAU8J,GAC7B,GAAInB,GAAOnP,KAAMiV,EAAa3E,EAAK5R,OAAQ6T,EAAqBjN,KAAKgN,MAAMnD,EAAK6F,6BAA+BC,GAAc,EAC7H,OAAO1C,IAEXrG,OAAQ,SAAU3N,GAAV,GAEKC,GADL2Q,EAAOnP,KAAM8C,EAASqM,EAAK4F,MAC/B,KAASvW,EAAI,EAAGA,EAAID,EAAIG,OAAQF,IAC5BsE,GAAUqM,EAAK+F,gBAAgB3W,EAAII,WAAWH,GAElD,OAAOsE,IAEXoS,gBAAiB,SAAUlG,GAAV,GAKLmG,GACK3W,EALT4W,EAAapV,KAAKqV,cAAcrG,GAAOsG,EAAKF,EAAa,EAAGtS,EAAS,EACzE,IAAkB,GAAdsS,EACAtS,EAASD,EAAamM,EAAM,OACzB,CAEH,IADImG,EAAkB,EAAIC,EACjB5W,EAAI,EAAGA,EAAI8W,EAAI9W,IACpBsE,EAASD,EAAamM,GAAY,EAAJxQ,EAAQ,GAAK,IAAK,GAAKsE,CAEzDA,IAAUkM,GAAa,EAALsG,EAAS,KAAOH,GAAmBA,GAAiBpS,SAAS,GAAKD,EAExF,MAAOA,IAEXuS,cAAe,SAAUrG,GAAV,GAEFxQ,GADL+W,EAASvV,KAAKuV,MAClB,KAAS/W,EAAI,EAAGA,EAAI+W,EAAO7W,OAAQF,IAC/B,GAAIwQ,EAAOuG,EAAO/W,GACd,MAAOA,GAAI,GAIvB+W,QACI,IACA,KACA,MACA,QACA,WAGJ5N,EAAoB,SAAU6N,GAC9B,MAAIA,IAAYA,EAASC,cAAcC,QAAQ,UAAY,EAChD,GAAIhO,GAEJ,GAAID,IAGfG,EAAa,SAAU+M,EAAanC,EAAsBgD,GAA7C,GAUTlB,GAAsCqB,EAItCrC,EAbAsC,EAAU,GAAIjO,GAAkB6N,GAAWK,EAAiBD,EAAQlB,kBAAkBC,EAAanC,GAAuB3G,EAAUgK,EAAehK,QAASiK,EAAqBlM,GAA6BiC,EAAU,GAAG2G,GAAuBtC,EAAahK,EAAc2P,EAAe3F,WAAY4F,EAAmB/L,oBAAqB2F,EAASrJ,EAAU6J,EAAY4F,GAAqBxS,EAAWwD,EAAa+E,EAeha,OAdAzE,GAAkB9D,GAClB+D,EAAqB/D,EAAUuI,GAC/BvE,EAAmBhE,GACfuI,GAAW,GACX5E,EAAsB3D,EAAUT,EAAa,EAAG,KAEpDkE,EAAqBzD,EAAUT,EAAa,EAAG,KAC/CoD,EAAS3C,EAAUoM,GACf4E,EAAS/M,EAAkBjE,GAAWqS,EAAgBrS,EAASgR,GAC/DzI,GAAW,GACX5E,GAAuB0O,GAAgB3O,EAAyB6E,IAEhEyH,EAAehJ,GAAwBkI,GAAwB3P,EAAayR,EAAQ,GACxFvN,GAAsB4O,GAAgBhP,EAAwB2M,IACvDqC,GAEP9N,GACAkO,aAAc,IACdC,kBAAmB,EACnBC,+BAAgC,IAChCC,mBAAoB,OACpBC,0BAA2B,OAC3BC,mBAAoB,GAEpBtO,EAASI,EAAOrI,QAChBC,KAAM,SAAUuW,EAASpV,GACrB,GAAIkO,GAAOnP,IACXkI,GAAO2M,GAAG/U,KAAKwW,KAAKnH,EAAMkH,EAASpV,GACnCkO,EAAKkH,QAAU7Y,EAAE6Y,GACjBlH,EAAKoH,QAAUpH,EAAKkH,QACpBlH,EAAKkH,QAAQG,SAAS,YACtBrH,EAAKsH,YAAcjZ,EAAE,WAAWkZ,IAAI,WAAY,YAAYC,SAAS3W,KAAKqW,SAC1ElH,EAAKyH,QAAU7O,EAAK8O,QAAQC,OAAO3H,EAAKsH,aAAeM,KAAM5H,EAAKlO,QAAQ+V,WAC1E7H,EAAK8H,WAAWhW,IAEpBiW,OAAQ,WACJ,GAAInX,GAAOC,KAAKmX,UAChBnX,MAAKyW,YAAYC,KACb7X,MAAOkB,EACPjB,OAAQiB,IAEZC,KAAK4W,QAAQQ,QACbpX,KAAKqX,eACLrX,KAAK4W,QAAQ7O,KAAK/H,KAAKsX,SAE3BC,QAAS,WACL,MAAO7X,GAAM8X,WAAWxX,KAAKqW,UAEjCoB,QAAS,WACLzX,KAAKkX,UAETG,aAAc,WACVrX,KAAKsX,OAAStX,KAAK0X,WAEvBC,aAAc,WACV,MAAO3X,MAAK0X,WAEhBA,QAAS,WAAA,GACiCE,GAAoHC,EAAe3I,EAAQnP,EAAM+X,EAAUC,EAE7LT,EAFAnI,EAAOnP,KAAMK,EAAQ8O,EAAK6I,OAAkBC,EAAS9I,EAAKlO,QAAQgX,WAAcC,EAAU/I,EAAKlO,QAAQiX,SAAW,EAAGC,EAAcF,EAAOpZ,OAAS,CAavJ,OAZAoZ,GAAOpZ,MAAQsZ,EACXb,EAAS,GAAIvP,GAAKqQ,MAClB/X,IACA6O,EAAStH,EAAWvH,EAAO8O,EAAKlO,QAAQoX,gBAAiBlJ,EAAKlO,QAAQuU,UACtEzV,EAAOoP,EAAKgI,WACZY,EAAchY,EAAO,GAAKoY,EAAcD,GACxCN,EAAWzI,EAAKmJ,mBAAmBP,EAAa7I,EAAOxQ,QACvDoZ,EAAW5I,EAAOxQ,OAASkZ,EAC3BC,EAAgBM,EAAcD,GAAWH,EAAcD,GAAY,EACnER,EAAOiB,OAAOpJ,EAAKqJ,kBAAkBzY,EAAMkY,IAC3CX,EAAOiB,OAAOpJ,EAAKsJ,cAAcvJ,EAAQ0I,EAAUC,KAEhDP,GAEXH,SAAU,WAAA,GACWpX,GAITsW,EAAwBxQ,EAJ5BsJ,EAAOnP,IAWX,OAVImP,GAAKlO,QAAQlB,KACbA,EAAO6C,SAASuM,EAAKlO,QAAQlB,KAAM,KAE/BsW,EAAUlH,EAAKkH,QAASxQ,EAAMP,KAAKO,IAAIwQ,EAAQxX,QAASwX,EAAQvX,UAEhEiB,EADA8F,EAAM,EACCA,EAEAgC,EAAekO,cAGvBhW,GAEXuY,mBAAoB,SAAUvY,EAAM2Y,GAChC,GAAId,GAAWtS,KAAKC,MAAMxF,EAAO2Y,EACjC,IAAId,EAAW/P,EAAeuO,mBAC1B,KAAUnH,OAAM,qBAKpB,OAHI2I,GAAWc,GAAc3Y,GAAQ6X,EAAW,GAAK/P,EAAeuO,oBAChEwB,IAEGA,GAEXa,cAAe,SAAUvJ,EAAQ0I,EAAUC,GAA5B,GAKFlN,GACDlH,EACAmH,EAMQpH,EAIAiQ,EACAC,EACAC,EACAC,EAnBZ+E,EAAO,GAAI5Q,GAAK6Q,WAChBC,MAAQC,MAAO9Y,KAAKiB,QAAQ6X,OAC5BC,OAAQ,MAEZ,KAASpO,EAAM,EAAGA,EAAMuE,EAAOxQ,OAAQiM,IAGnC,IAFIlH,EAAIoU,EAAgBlN,EAAMiN,EAC1BhN,EAAS,EACNA,EAASsE,EAAOxQ,QAAQ,CAC3B,KAA+B,IAAxBwQ,EAAOvE,GAAKC,IAAiBA,EAASsE,EAAOxQ,QAChDkM,GAEJ,IAAIA,EAASsE,EAAOxQ,OAAQ,CAExB,IADI8E,EAAIoH,EACsB,GAAvBsE,EAAOvE,GAAKC,IACfA,GAEA6I,GAAK/H,GAAMmM,EAAgBrU,EAAIoU,GAC/BlE,EAAKhI,GAAMjI,GACXkQ,EAAKjI,GAAMmM,EAAgBjN,EAASgN,GACpChE,EAAKlI,GAAMjI,EAAImU,GACnBe,EAAKK,OAAOvF,EAAIC,GAAIuF,OAAOxF,EAAIG,GAAIqF,OAAOtF,EAAIC,GAAIqF,OAAOtF,EAAID,GAAIwF,SAI7E,MAAOP,IAEXH,kBAAmB,SAAUzY,EAAMkY,GAC/B,GAAIzW,GAAM,GAAI4G,GAAM,EAAG,EAAGrI,EAAMA,GAAMoZ,MAAMlB,EAAOpZ,MAAQ,EAC3D,OAAOkJ,GAAKqR,KAAKC,SAAS7X,EAAI8X,UAC1BT,MAAQC,MAAO9Y,KAAKiB,QAAQsY,YAC5BR,QACID,MAAOb,EAAOa,MACdja,MAAOoZ,EAAOpZ,UAI1BoY,WAAY,SAAUhW,GAClB,GAAIkO,GAAOnP,IACXiB,GAAUA,MACVkO,EAAKlO,QAAUpB,EAAOsP,EAAKlO,QAASA,GAChCA,EAAQZ,QAAUqC,IAClByM,EAAK6I,OAAS7I,EAAKlO,QAAQZ,MAAQ,IAEvC8O,EAAK+H,UAET7W,MAAO,SAAUA,GACb,GAAI8O,GAAOnP,IACX,OAAIK,KAAUqC,EACHyM,EAAK6I,QAEhB7I,EAAK6I,OAAS3X,EAAQ,GACtB8O,EAAK+H,SADL/H,IAGJlO,SACIuY,KAAM,SACNxC,SAAU,MACVxB,SAAU,aACVnV,MAAO,GACPgY,gBAAiBxQ,EAAeoO,+BAChCsD,WAAY1R,EAAeqO,mBAC3B4C,MAAOjR,EAAesO,0BACtBpW,KAAM,GACNmY,QAAS,EACTD,QACIa,MAAO,GACPja,MAAO,MAInBoJ,EAAQwR,YAAY5Z,OAAOiI,EAAO+M,IAClC5M,EAAQE,GAAGuR,OAAO5R,GAClBpI,EAAM0C,WAAW6F,GACbH,OAAQA,EACRD,eAAgBA,EAChB8R,iBACI3T,gBAAiBA,EACjBC,SAAUA,EACVC,cAAeA,EACfE,uBAAwBA,EACxBD,eAAgBA,EAChBE,UAAWA,EACXhC,oBAAqBA,EACrBiC,WAAYA,EACZC,SAAUA,EACVC,sBAAuBA,EACvBC,WAAYA,EACZC,cAAeA,EACfC,wBAAyBA,EACzBC,UAAWA,EACXC,kBAAmBA,EACnBC,aAAcA,EACdC,qBAAsBA,EACtBC,yBAA0BA,EAC1BC,sBAAuBA,EACvBC,kBAAmBA,EACnBC,mBAAoBA,EACpBC,kBAAmBA,EACnBC,qBAAsBA,EACtBC,mBAAoBA,EACpBC,kBAAmBA,EACnBK,WAAYA,EACZF,YAAaA,GAEjBkS,cACIvN,MAAOvG,EACP/B,YAAaA,GACbD,kBAAmBA,GACnBM,qBAAsBA,OAGhC3E,OAAOC,MAAM2C,QACR5C,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dataviz.qrcode.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dataviz.qrcode', [\n        'kendo.dataviz.core',\n        'kendo.drawing'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dataviz.qrcode',\n        name: 'QRCode',\n        category: 'dataviz',\n        description: 'QRCode widget.',\n        depends: [\n            'dataviz.core',\n            'drawing'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, extend = $.extend, draw = kendo.drawing, dataviz = kendo.dataviz, Widget = kendo.ui.Widget, Box2D = dataviz.Box2D, terminator = '0000', NUMERIC = 'numeric', ALPHA_NUMERIC = 'alphanumeric', BYTE = 'byte', powersOfTwo = { '1': 0 }, powersOfTwoResult = { '0': 1 }, generatorPolynomials = [\n                [\n                    1,\n                    0\n                ],\n                [\n                    1,\n                    25,\n                    0\n                ]\n            ], irregularAlignmentPatternsStartDistance = {\n                15: 20,\n                16: 20,\n                18: 24,\n                19: 24,\n                22: 20,\n                24: 22,\n                26: 24,\n                28: 20,\n                30: 20,\n                31: 24,\n                32: 28,\n                33: 24,\n                36: 18,\n                37: 22,\n                39: 20,\n                40: 24\n            }, versionsCodewordsInformation = [\n                {\n                    L: {\n                        groups: [[\n                                1,\n                                19\n                            ]],\n                        totalDataCodewords: 19,\n                        errorCodewordsPerBlock: 7\n                    },\n                    M: {\n                        groups: [[\n                                1,\n                                16\n                            ]],\n                        totalDataCodewords: 16,\n                        errorCodewordsPerBlock: 10\n                    },\n                    Q: {\n                        groups: [[\n                                1,\n                                13\n                            ]],\n                        totalDataCodewords: 13,\n                        errorCodewordsPerBlock: 13\n                    },\n                    H: {\n                        groups: [[\n                                1,\n                                9\n                            ]],\n                        totalDataCodewords: 9,\n                        errorCodewordsPerBlock: 17\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                1,\n                                34\n                            ]],\n                        totalDataCodewords: 34,\n                        errorCodewordsPerBlock: 10\n                    },\n                    M: {\n                        groups: [[\n                                1,\n                                28\n                            ]],\n                        totalDataCodewords: 28,\n                        errorCodewordsPerBlock: 16\n                    },\n                    Q: {\n                        groups: [[\n                                1,\n                                22\n                            ]],\n                        totalDataCodewords: 22,\n                        errorCodewordsPerBlock: 22\n                    },\n                    H: {\n                        groups: [[\n                                1,\n                                16\n                            ]],\n                        totalDataCodewords: 16,\n                        errorCodewordsPerBlock: 28\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                1,\n                                55\n                            ]],\n                        totalDataCodewords: 55,\n                        errorCodewordsPerBlock: 15\n                    },\n                    M: {\n                        groups: [[\n                                1,\n                                44\n                            ]],\n                        totalDataCodewords: 44,\n                        errorCodewordsPerBlock: 26\n                    },\n                    Q: {\n                        groups: [[\n                                2,\n                                17\n                            ]],\n                        totalDataCodewords: 34,\n                        errorCodewordsPerBlock: 18\n                    },\n                    H: {\n                        groups: [[\n                                2,\n                                13\n                            ]],\n                        totalDataCodewords: 26,\n                        errorCodewordsPerBlock: 22\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                1,\n                                80\n                            ]],\n                        totalDataCodewords: 80,\n                        errorCodewordsPerBlock: 20\n                    },\n                    M: {\n                        groups: [[\n                                2,\n                                32\n                            ]],\n                        totalDataCodewords: 64,\n                        errorCodewordsPerBlock: 18\n                    },\n                    Q: {\n                        groups: [[\n                                2,\n                                24\n                            ]],\n                        totalDataCodewords: 48,\n                        errorCodewordsPerBlock: 26\n                    },\n                    H: {\n                        groups: [[\n                                4,\n                                9\n                            ]],\n                        totalDataCodewords: 36,\n                        errorCodewordsPerBlock: 16\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                1,\n                                108\n                            ]],\n                        totalDataCodewords: 108,\n                        errorCodewordsPerBlock: 26\n                    },\n                    M: {\n                        groups: [[\n                                2,\n                                43\n                            ]],\n                        totalDataCodewords: 86,\n                        errorCodewordsPerBlock: 24\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                2,\n                                15\n                            ],\n                            [\n                                2,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 62,\n                        errorCodewordsPerBlock: 18\n                    },\n                    H: {\n                        groups: [\n                            [\n                                2,\n                                11\n                            ],\n                            [\n                                2,\n                                12\n                            ]\n                        ],\n                        totalDataCodewords: 46,\n                        errorCodewordsPerBlock: 22\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                2,\n                                68\n                            ]],\n                        totalDataCodewords: 136,\n                        errorCodewordsPerBlock: 18\n                    },\n                    M: {\n                        groups: [[\n                                4,\n                                27\n                            ]],\n                        totalDataCodewords: 108,\n                        errorCodewordsPerBlock: 16\n                    },\n                    Q: {\n                        groups: [[\n                                4,\n                                19\n                            ]],\n                        totalDataCodewords: 76,\n                        errorCodewordsPerBlock: 24\n                    },\n                    H: {\n                        groups: [[\n                                4,\n                                15\n                            ]],\n                        totalDataCodewords: 60,\n                        errorCodewordsPerBlock: 28\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                2,\n                                78\n                            ]],\n                        totalDataCodewords: 156,\n                        errorCodewordsPerBlock: 20\n                    },\n                    M: {\n                        groups: [[\n                                4,\n                                31\n                            ]],\n                        totalDataCodewords: 124,\n                        errorCodewordsPerBlock: 18\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                2,\n                                14\n                            ],\n                            [\n                                4,\n                                15\n                            ]\n                        ],\n                        totalDataCodewords: 88,\n                        errorCodewordsPerBlock: 18\n                    },\n                    H: {\n                        groups: [\n                            [\n                                4,\n                                13\n                            ],\n                            [\n                                1,\n                                14\n                            ]\n                        ],\n                        totalDataCodewords: 66,\n                        errorCodewordsPerBlock: 26\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                2,\n                                97\n                            ]],\n                        totalDataCodewords: 194,\n                        errorCodewordsPerBlock: 24\n                    },\n                    M: {\n                        groups: [\n                            [\n                                2,\n                                38\n                            ],\n                            [\n                                2,\n                                39\n                            ]\n                        ],\n                        totalDataCodewords: 154,\n                        errorCodewordsPerBlock: 22\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                4,\n                                18\n                            ],\n                            [\n                                2,\n                                19\n                            ]\n                        ],\n                        totalDataCodewords: 110,\n                        errorCodewordsPerBlock: 22\n                    },\n                    H: {\n                        groups: [\n                            [\n                                4,\n                                14\n                            ],\n                            [\n                                2,\n                                15\n                            ]\n                        ],\n                        totalDataCodewords: 86,\n                        errorCodewordsPerBlock: 26\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                2,\n                                116\n                            ]],\n                        totalDataCodewords: 232,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                3,\n                                36\n                            ],\n                            [\n                                2,\n                                37\n                            ]\n                        ],\n                        totalDataCodewords: 182,\n                        errorCodewordsPerBlock: 22\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                4,\n                                16\n                            ],\n                            [\n                                4,\n                                17\n                            ]\n                        ],\n                        totalDataCodewords: 132,\n                        errorCodewordsPerBlock: 20\n                    },\n                    H: {\n                        groups: [\n                            [\n                                4,\n                                12\n                            ],\n                            [\n                                4,\n                                13\n                            ]\n                        ],\n                        totalDataCodewords: 100,\n                        errorCodewordsPerBlock: 24\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                2,\n                                68\n                            ],\n                            [\n                                2,\n                                69\n                            ]\n                        ],\n                        totalDataCodewords: 274,\n                        errorCodewordsPerBlock: 18\n                    },\n                    M: {\n                        groups: [\n                            [\n                                4,\n                                43\n                            ],\n                            [\n                                1,\n                                44\n                            ]\n                        ],\n                        totalDataCodewords: 216,\n                        errorCodewordsPerBlock: 26\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                6,\n                                19\n                            ],\n                            [\n                                2,\n                                20\n                            ]\n                        ],\n                        totalDataCodewords: 154,\n                        errorCodewordsPerBlock: 24\n                    },\n                    H: {\n                        groups: [\n                            [\n                                6,\n                                15\n                            ],\n                            [\n                                2,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 122,\n                        errorCodewordsPerBlock: 28\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                4,\n                                81\n                            ]],\n                        totalDataCodewords: 324,\n                        errorCodewordsPerBlock: 20\n                    },\n                    M: {\n                        groups: [\n                            [\n                                1,\n                                50\n                            ],\n                            [\n                                4,\n                                51\n                            ]\n                        ],\n                        totalDataCodewords: 254,\n                        errorCodewordsPerBlock: 30\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                4,\n                                22\n                            ],\n                            [\n                                4,\n                                23\n                            ]\n                        ],\n                        totalDataCodewords: 180,\n                        errorCodewordsPerBlock: 28\n                    },\n                    H: {\n                        groups: [\n                            [\n                                3,\n                                12\n                            ],\n                            [\n                                8,\n                                13\n                            ]\n                        ],\n                        totalDataCodewords: 140,\n                        errorCodewordsPerBlock: 24\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                2,\n                                92\n                            ],\n                            [\n                                2,\n                                93\n                            ]\n                        ],\n                        totalDataCodewords: 370,\n                        errorCodewordsPerBlock: 24\n                    },\n                    M: {\n                        groups: [\n                            [\n                                6,\n                                36\n                            ],\n                            [\n                                2,\n                                37\n                            ]\n                        ],\n                        totalDataCodewords: 290,\n                        errorCodewordsPerBlock: 22\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                4,\n                                20\n                            ],\n                            [\n                                6,\n                                21\n                            ]\n                        ],\n                        totalDataCodewords: 206,\n                        errorCodewordsPerBlock: 26\n                    },\n                    H: {\n                        groups: [\n                            [\n                                7,\n                                14\n                            ],\n                            [\n                                4,\n                                15\n                            ]\n                        ],\n                        totalDataCodewords: 158,\n                        errorCodewordsPerBlock: 28\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                4,\n                                107\n                            ]],\n                        totalDataCodewords: 428,\n                        errorCodewordsPerBlock: 26\n                    },\n                    M: {\n                        groups: [\n                            [\n                                8,\n                                37\n                            ],\n                            [\n                                1,\n                                38\n                            ]\n                        ],\n                        totalDataCodewords: 334,\n                        errorCodewordsPerBlock: 22\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                8,\n                                20\n                            ],\n                            [\n                                4,\n                                21\n                            ]\n                        ],\n                        totalDataCodewords: 244,\n                        errorCodewordsPerBlock: 24\n                    },\n                    H: {\n                        groups: [\n                            [\n                                12,\n                                11\n                            ],\n                            [\n                                4,\n                                12\n                            ]\n                        ],\n                        totalDataCodewords: 180,\n                        errorCodewordsPerBlock: 22\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                3,\n                                115\n                            ],\n                            [\n                                1,\n                                116\n                            ]\n                        ],\n                        totalDataCodewords: 461,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                4,\n                                40\n                            ],\n                            [\n                                5,\n                                41\n                            ]\n                        ],\n                        totalDataCodewords: 365,\n                        errorCodewordsPerBlock: 24\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                11,\n                                16\n                            ],\n                            [\n                                5,\n                                17\n                            ]\n                        ],\n                        totalDataCodewords: 261,\n                        errorCodewordsPerBlock: 20\n                    },\n                    H: {\n                        groups: [\n                            [\n                                11,\n                                12\n                            ],\n                            [\n                                5,\n                                13\n                            ]\n                        ],\n                        totalDataCodewords: 197,\n                        errorCodewordsPerBlock: 24\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                5,\n                                87\n                            ],\n                            [\n                                1,\n                                88\n                            ]\n                        ],\n                        totalDataCodewords: 523,\n                        errorCodewordsPerBlock: 22\n                    },\n                    M: {\n                        groups: [\n                            [\n                                5,\n                                41\n                            ],\n                            [\n                                5,\n                                42\n                            ]\n                        ],\n                        totalDataCodewords: 415,\n                        errorCodewordsPerBlock: 24\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                5,\n                                24\n                            ],\n                            [\n                                7,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 295,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                11,\n                                12\n                            ],\n                            [\n                                7,\n                                13\n                            ]\n                        ],\n                        totalDataCodewords: 223,\n                        errorCodewordsPerBlock: 24\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                5,\n                                98\n                            ],\n                            [\n                                1,\n                                99\n                            ]\n                        ],\n                        totalDataCodewords: 589,\n                        errorCodewordsPerBlock: 24\n                    },\n                    M: {\n                        groups: [\n                            [\n                                7,\n                                45\n                            ],\n                            [\n                                3,\n                                46\n                            ]\n                        ],\n                        totalDataCodewords: 453,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                15,\n                                19\n                            ],\n                            [\n                                2,\n                                20\n                            ]\n                        ],\n                        totalDataCodewords: 325,\n                        errorCodewordsPerBlock: 24\n                    },\n                    H: {\n                        groups: [\n                            [\n                                3,\n                                15\n                            ],\n                            [\n                                13,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 253,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                1,\n                                107\n                            ],\n                            [\n                                5,\n                                108\n                            ]\n                        ],\n                        totalDataCodewords: 647,\n                        errorCodewordsPerBlock: 28\n                    },\n                    M: {\n                        groups: [\n                            [\n                                10,\n                                46\n                            ],\n                            [\n                                1,\n                                47\n                            ]\n                        ],\n                        totalDataCodewords: 507,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                1,\n                                22\n                            ],\n                            [\n                                15,\n                                23\n                            ]\n                        ],\n                        totalDataCodewords: 367,\n                        errorCodewordsPerBlock: 28\n                    },\n                    H: {\n                        groups: [\n                            [\n                                2,\n                                14\n                            ],\n                            [\n                                17,\n                                15\n                            ]\n                        ],\n                        totalDataCodewords: 283,\n                        errorCodewordsPerBlock: 28\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                5,\n                                120\n                            ],\n                            [\n                                1,\n                                121\n                            ]\n                        ],\n                        totalDataCodewords: 721,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                9,\n                                43\n                            ],\n                            [\n                                4,\n                                44\n                            ]\n                        ],\n                        totalDataCodewords: 563,\n                        errorCodewordsPerBlock: 26\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                17,\n                                22\n                            ],\n                            [\n                                1,\n                                23\n                            ]\n                        ],\n                        totalDataCodewords: 397,\n                        errorCodewordsPerBlock: 28\n                    },\n                    H: {\n                        groups: [\n                            [\n                                2,\n                                14\n                            ],\n                            [\n                                19,\n                                15\n                            ]\n                        ],\n                        totalDataCodewords: 313,\n                        errorCodewordsPerBlock: 28\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                3,\n                                113\n                            ],\n                            [\n                                4,\n                                114\n                            ]\n                        ],\n                        totalDataCodewords: 795,\n                        errorCodewordsPerBlock: 28\n                    },\n                    M: {\n                        groups: [\n                            [\n                                3,\n                                44\n                            ],\n                            [\n                                11,\n                                45\n                            ]\n                        ],\n                        totalDataCodewords: 627,\n                        errorCodewordsPerBlock: 26\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                17,\n                                21\n                            ],\n                            [\n                                4,\n                                22\n                            ]\n                        ],\n                        totalDataCodewords: 445,\n                        errorCodewordsPerBlock: 26\n                    },\n                    H: {\n                        groups: [\n                            [\n                                9,\n                                13\n                            ],\n                            [\n                                16,\n                                14\n                            ]\n                        ],\n                        totalDataCodewords: 341,\n                        errorCodewordsPerBlock: 26\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                3,\n                                107\n                            ],\n                            [\n                                5,\n                                108\n                            ]\n                        ],\n                        totalDataCodewords: 861,\n                        errorCodewordsPerBlock: 28\n                    },\n                    M: {\n                        groups: [\n                            [\n                                3,\n                                41\n                            ],\n                            [\n                                13,\n                                42\n                            ]\n                        ],\n                        totalDataCodewords: 669,\n                        errorCodewordsPerBlock: 26\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                15,\n                                24\n                            ],\n                            [\n                                5,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 485,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                15,\n                                15\n                            ],\n                            [\n                                10,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 385,\n                        errorCodewordsPerBlock: 28\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                4,\n                                116\n                            ],\n                            [\n                                4,\n                                117\n                            ]\n                        ],\n                        totalDataCodewords: 932,\n                        errorCodewordsPerBlock: 28\n                    },\n                    M: {\n                        groups: [[\n                                17,\n                                42\n                            ]],\n                        totalDataCodewords: 714,\n                        errorCodewordsPerBlock: 26\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                17,\n                                22\n                            ],\n                            [\n                                6,\n                                23\n                            ]\n                        ],\n                        totalDataCodewords: 512,\n                        errorCodewordsPerBlock: 28\n                    },\n                    H: {\n                        groups: [\n                            [\n                                19,\n                                16\n                            ],\n                            [\n                                6,\n                                17\n                            ]\n                        ],\n                        totalDataCodewords: 406,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                2,\n                                111\n                            ],\n                            [\n                                7,\n                                112\n                            ]\n                        ],\n                        totalDataCodewords: 1006,\n                        errorCodewordsPerBlock: 28\n                    },\n                    M: {\n                        groups: [[\n                                17,\n                                46\n                            ]],\n                        totalDataCodewords: 782,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                7,\n                                24\n                            ],\n                            [\n                                16,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 568,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [[\n                                34,\n                                13\n                            ]],\n                        totalDataCodewords: 442,\n                        errorCodewordsPerBlock: 24\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                4,\n                                121\n                            ],\n                            [\n                                5,\n                                122\n                            ]\n                        ],\n                        totalDataCodewords: 1094,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                4,\n                                47\n                            ],\n                            [\n                                14,\n                                48\n                            ]\n                        ],\n                        totalDataCodewords: 860,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                11,\n                                24\n                            ],\n                            [\n                                14,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 614,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                16,\n                                15\n                            ],\n                            [\n                                14,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 464,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                6,\n                                117\n                            ],\n                            [\n                                4,\n                                118\n                            ]\n                        ],\n                        totalDataCodewords: 1174,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                6,\n                                45\n                            ],\n                            [\n                                14,\n                                46\n                            ]\n                        ],\n                        totalDataCodewords: 914,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                11,\n                                24\n                            ],\n                            [\n                                16,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 664,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                30,\n                                16\n                            ],\n                            [\n                                2,\n                                17\n                            ]\n                        ],\n                        totalDataCodewords: 514,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                8,\n                                106\n                            ],\n                            [\n                                4,\n                                107\n                            ]\n                        ],\n                        totalDataCodewords: 1276,\n                        errorCodewordsPerBlock: 26\n                    },\n                    M: {\n                        groups: [\n                            [\n                                8,\n                                47\n                            ],\n                            [\n                                13,\n                                48\n                            ]\n                        ],\n                        totalDataCodewords: 1000,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                7,\n                                24\n                            ],\n                            [\n                                22,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 718,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                22,\n                                15\n                            ],\n                            [\n                                13,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 538,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                10,\n                                114\n                            ],\n                            [\n                                2,\n                                115\n                            ]\n                        ],\n                        totalDataCodewords: 1370,\n                        errorCodewordsPerBlock: 28\n                    },\n                    M: {\n                        groups: [\n                            [\n                                19,\n                                46\n                            ],\n                            [\n                                4,\n                                47\n                            ]\n                        ],\n                        totalDataCodewords: 1062,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                28,\n                                22\n                            ],\n                            [\n                                6,\n                                23\n                            ]\n                        ],\n                        totalDataCodewords: 754,\n                        errorCodewordsPerBlock: 28\n                    },\n                    H: {\n                        groups: [\n                            [\n                                33,\n                                16\n                            ],\n                            [\n                                4,\n                                17\n                            ]\n                        ],\n                        totalDataCodewords: 596,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                8,\n                                122\n                            ],\n                            [\n                                4,\n                                123\n                            ]\n                        ],\n                        totalDataCodewords: 1468,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                22,\n                                45\n                            ],\n                            [\n                                3,\n                                46\n                            ]\n                        ],\n                        totalDataCodewords: 1128,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                8,\n                                23\n                            ],\n                            [\n                                26,\n                                24\n                            ]\n                        ],\n                        totalDataCodewords: 808,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                12,\n                                15\n                            ],\n                            [\n                                28,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 628,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                3,\n                                117\n                            ],\n                            [\n                                10,\n                                118\n                            ]\n                        ],\n                        totalDataCodewords: 1531,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                3,\n                                45\n                            ],\n                            [\n                                23,\n                                46\n                            ]\n                        ],\n                        totalDataCodewords: 1193,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                4,\n                                24\n                            ],\n                            [\n                                31,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 871,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                11,\n                                15\n                            ],\n                            [\n                                31,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 661,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                7,\n                                116\n                            ],\n                            [\n                                7,\n                                117\n                            ]\n                        ],\n                        totalDataCodewords: 1631,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                21,\n                                45\n                            ],\n                            [\n                                7,\n                                46\n                            ]\n                        ],\n                        totalDataCodewords: 1267,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                1,\n                                23\n                            ],\n                            [\n                                37,\n                                24\n                            ]\n                        ],\n                        totalDataCodewords: 911,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                19,\n                                15\n                            ],\n                            [\n                                26,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 701,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                5,\n                                115\n                            ],\n                            [\n                                10,\n                                116\n                            ]\n                        ],\n                        totalDataCodewords: 1735,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                19,\n                                47\n                            ],\n                            [\n                                10,\n                                48\n                            ]\n                        ],\n                        totalDataCodewords: 1373,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                15,\n                                24\n                            ],\n                            [\n                                25,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 985,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                23,\n                                15\n                            ],\n                            [\n                                25,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 745,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                13,\n                                115\n                            ],\n                            [\n                                3,\n                                116\n                            ]\n                        ],\n                        totalDataCodewords: 1843,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                2,\n                                46\n                            ],\n                            [\n                                29,\n                                47\n                            ]\n                        ],\n                        totalDataCodewords: 1455,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                42,\n                                24\n                            ],\n                            [\n                                1,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1033,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                23,\n                                15\n                            ],\n                            [\n                                28,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 793,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [[\n                                17,\n                                115\n                            ]],\n                        totalDataCodewords: 1955,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                10,\n                                46\n                            ],\n                            [\n                                23,\n                                47\n                            ]\n                        ],\n                        totalDataCodewords: 1541,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                10,\n                                24\n                            ],\n                            [\n                                35,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1115,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                19,\n                                15\n                            ],\n                            [\n                                35,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 845,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                17,\n                                115\n                            ],\n                            [\n                                1,\n                                116\n                            ]\n                        ],\n                        totalDataCodewords: 2071,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                14,\n                                46\n                            ],\n                            [\n                                21,\n                                47\n                            ]\n                        ],\n                        totalDataCodewords: 1631,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                29,\n                                24\n                            ],\n                            [\n                                19,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1171,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                11,\n                                15\n                            ],\n                            [\n                                46,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 901,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                13,\n                                115\n                            ],\n                            [\n                                6,\n                                116\n                            ]\n                        ],\n                        totalDataCodewords: 2191,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                14,\n                                46\n                            ],\n                            [\n                                23,\n                                47\n                            ]\n                        ],\n                        totalDataCodewords: 1725,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                44,\n                                24\n                            ],\n                            [\n                                7,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1231,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                59,\n                                16\n                            ],\n                            [\n                                1,\n                                17\n                            ]\n                        ],\n                        totalDataCodewords: 961,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                12,\n                                121\n                            ],\n                            [\n                                7,\n                                122\n                            ]\n                        ],\n                        totalDataCodewords: 2306,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                12,\n                                47\n                            ],\n                            [\n                                26,\n                                48\n                            ]\n                        ],\n                        totalDataCodewords: 1812,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                39,\n                                24\n                            ],\n                            [\n                                14,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1286,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                22,\n                                15\n                            ],\n                            [\n                                41,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 986,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                6,\n                                121\n                            ],\n                            [\n                                14,\n                                122\n                            ]\n                        ],\n                        totalDataCodewords: 2434,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                6,\n                                47\n                            ],\n                            [\n                                34,\n                                48\n                            ]\n                        ],\n                        totalDataCodewords: 1914,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                46,\n                                24\n                            ],\n                            [\n                                10,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1354,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                2,\n                                15\n                            ],\n                            [\n                                64,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 1054,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                17,\n                                122\n                            ],\n                            [\n                                4,\n                                123\n                            ]\n                        ],\n                        totalDataCodewords: 2566,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                29,\n                                46\n                            ],\n                            [\n                                14,\n                                47\n                            ]\n                        ],\n                        totalDataCodewords: 1992,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                49,\n                                24\n                            ],\n                            [\n                                10,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1426,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                24,\n                                15\n                            ],\n                            [\n                                46,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 1096,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                4,\n                                122\n                            ],\n                            [\n                                18,\n                                123\n                            ]\n                        ],\n                        totalDataCodewords: 2702,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                13,\n                                46\n                            ],\n                            [\n                                32,\n                                47\n                            ]\n                        ],\n                        totalDataCodewords: 2102,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                48,\n                                24\n                            ],\n                            [\n                                14,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1502,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                42,\n                                15\n                            ],\n                            [\n                                32,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 1142,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                20,\n                                117\n                            ],\n                            [\n                                4,\n                                118\n                            ]\n                        ],\n                        totalDataCodewords: 2812,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                40,\n                                47\n                            ],\n                            [\n                                7,\n                                48\n                            ]\n                        ],\n                        totalDataCodewords: 2216,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                43,\n                                24\n                            ],\n                            [\n                                22,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1582,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                10,\n                                15\n                            ],\n                            [\n                                67,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 1222,\n                        errorCodewordsPerBlock: 30\n                    }\n                },\n                {\n                    L: {\n                        groups: [\n                            [\n                                19,\n                                118\n                            ],\n                            [\n                                6,\n                                119\n                            ]\n                        ],\n                        totalDataCodewords: 2956,\n                        errorCodewordsPerBlock: 30\n                    },\n                    M: {\n                        groups: [\n                            [\n                                18,\n                                47\n                            ],\n                            [\n                                31,\n                                48\n                            ]\n                        ],\n                        totalDataCodewords: 2334,\n                        errorCodewordsPerBlock: 28\n                    },\n                    Q: {\n                        groups: [\n                            [\n                                34,\n                                24\n                            ],\n                            [\n                                34,\n                                25\n                            ]\n                        ],\n                        totalDataCodewords: 1666,\n                        errorCodewordsPerBlock: 30\n                    },\n                    H: {\n                        groups: [\n                            [\n                                20,\n                                15\n                            ],\n                            [\n                                61,\n                                16\n                            ]\n                        ],\n                        totalDataCodewords: 1276,\n                        errorCodewordsPerBlock: 30\n                    }\n                }\n            ], finderPattern = [\n                1,\n                0,\n                1,\n                1,\n                1\n            ], alignmentPattern = [\n                1,\n                0,\n                1\n            ], errorCorrectionPatterns = {\n                L: '01',\n                M: '00',\n                Q: '11',\n                H: '10'\n            }, formatMaskPattern = '101010000010010', formatGeneratorPolynomial = '10100110111', versionGeneratorPolynomial = '1111100100101', paddingCodewords = [\n                '11101100',\n                '00010001'\n            ], finderPatternValue = 93, maskPatternConditions = [\n                function (row, column) {\n                    return (row + column) % 2 === 0;\n                },\n                function (row) {\n                    return row % 2 === 0;\n                },\n                function (row, column) {\n                    return column % 3 === 0;\n                },\n                function (row, column) {\n                    return (row + column) % 3 === 0;\n                },\n                function (row, column) {\n                    return (Math.floor(row / 2) + Math.floor(column / 3)) % 2 === 0;\n                },\n                function (row, column) {\n                    return row * column % 2 + row * column % 3 === 0;\n                },\n                function (row, column) {\n                    return (row * column % 2 + row * column % 3) % 2 === 0;\n                },\n                function (row, column) {\n                    return ((row + column) % 2 + row * column % 3) % 2 === 0;\n                }\n            ], numberRegex = /^\\d+/, alphaPattern = 'A-Z0-9 $%*+./:-', alphaExclusiveSet = 'A-Z $%*+./:-', alphaRegex = new RegExp('^[' + alphaExclusiveSet + ']+'), alphaNumericRegex = new RegExp('^[' + alphaPattern + ']+'), byteRegex = new RegExp('^[^' + alphaPattern + ']+'), initMinNumericBeforeAlpha = 8, initMinNumericBeforeByte = 5, initMinAlphaBeforeByte = 8, minNumericBeforeAlpha = 17, minNumericBeforeByte = 9, minAlphaBeforeByte = 16, round = Math.round;\n        function toDecimal(value) {\n            return parseInt(value, 2);\n        }\n        function toBitsString(value, length) {\n            var result = Number(value).toString(2);\n            if (result.length < length) {\n                result = new Array(length - result.length + 1).join(0) + result;\n            }\n            return result;\n        }\n        function splitInto(str, n) {\n            var result = [], idx = 0;\n            while (idx < str.length) {\n                result.push(str.substring(idx, idx + n));\n                idx += n;\n            }\n            return result;\n        }\n        var QRDataMode = kendo.Class.extend({\n            getVersionIndex: function (version) {\n                if (version < 10) {\n                    return 0;\n                } else if (version > 26) {\n                    return 2;\n                }\n                return 1;\n            },\n            getBitsCharacterCount: function (version) {\n                var mode = this;\n                return mode.bitsInCharacterCount[mode.getVersionIndex(version || 40)];\n            },\n            getModeCountString: function (length, version) {\n                var mode = this;\n                return mode.modeIndicator + toBitsString(length, mode.getBitsCharacterCount(version));\n            },\n            encode: function () {\n            },\n            getStringBitsLength: function () {\n            },\n            getValue: function () {\n            },\n            modeIndicator: '',\n            bitsInCharacterCount: []\n        });\n        var modes = {};\n        modes[NUMERIC] = QRDataMode.extend({\n            bitsInCharacterCount: [\n                10,\n                12,\n                14\n            ],\n            modeIndicator: '0001',\n            getValue: function (character) {\n                return parseInt(character, 10);\n            },\n            encode: function (str, version) {\n                var mode = this, parts = splitInto(str, 3), result = mode.getModeCountString(str.length, version);\n                for (var i = 0; i < parts.length - 1; i++) {\n                    result += toBitsString(parts[i], 10);\n                }\n                return result + toBitsString(parts[i], 1 + 3 * parts[i].length);\n            },\n            getStringBitsLength: function (inputLength, version) {\n                var mod3 = inputLength % 3;\n                return 4 + this.getBitsCharacterCount(version) + 10 * Math.floor(inputLength / 3) + 3 * mod3 + (mod3 === 0 ? 0 : 1);\n            }\n        });\n        modes[ALPHA_NUMERIC] = QRDataMode.extend({\n            characters: {\n                '0': 0,\n                '1': 1,\n                '2': 2,\n                '3': 3,\n                '4': 4,\n                '5': 5,\n                '6': 6,\n                '7': 7,\n                '8': 8,\n                '9': 9,\n                'A': 10,\n                'B': 11,\n                'C': 12,\n                'D': 13,\n                'E': 14,\n                'F': 15,\n                'G': 16,\n                'H': 17,\n                'I': 18,\n                'J': 19,\n                'K': 20,\n                'L': 21,\n                'M': 22,\n                'N': 23,\n                'O': 24,\n                'P': 25,\n                'Q': 26,\n                'R': 27,\n                'S': 28,\n                'T': 29,\n                'U': 30,\n                'V': 31,\n                'W': 32,\n                'X': 33,\n                'Y': 34,\n                'Z': 35,\n                ' ': 36,\n                '$': 37,\n                '%': 38,\n                '*': 39,\n                '+': 40,\n                '-': 41,\n                '.': 42,\n                '/': 43,\n                ':': 44\n            },\n            bitsInCharacterCount: [\n                9,\n                11,\n                13\n            ],\n            modeIndicator: '0010',\n            getValue: function (character) {\n                return this.characters[character];\n            },\n            encode: function (str, version) {\n                var mode = this, parts = splitInto(str, 2), result = mode.getModeCountString(str.length, version), value;\n                for (var i = 0; i < parts.length - 1; i++) {\n                    value = 45 * mode.getValue(parts[i].charAt(0)) + mode.getValue(parts[i].charAt(1));\n                    result += toBitsString(value, 11);\n                }\n                value = parts[i].length == 2 ? 45 * mode.getValue(parts[i].charAt(0)) + mode.getValue(parts[i].charAt(1)) : mode.getValue(parts[i].charAt(0));\n                return result + toBitsString(value, 1 + 5 * parts[i].length);\n            },\n            getStringBitsLength: function (inputLength, version) {\n                return 4 + this.getBitsCharacterCount(version) + 11 * Math.floor(inputLength / 2) + 6 * (inputLength % 2);\n            }\n        });\n        modes[BYTE] = QRDataMode.extend({\n            bitsInCharacterCount: [\n                8,\n                16,\n                16\n            ],\n            modeIndicator: '0100',\n            getValue: function (character) {\n                var code = character.charCodeAt(0);\n                if (code <= 127 || 160 <= code && code <= 255) {\n                    return code;\n                } else {\n                    throw new Error('Unsupported character: ' + character);\n                }\n            },\n            encode: function (str, version) {\n                var mode = this, result = mode.getModeCountString(str.length, version);\n                for (var i = 0; i < str.length; i++) {\n                    result += toBitsString(mode.getValue(str.charAt(i)), 8);\n                }\n                return result;\n            },\n            getStringBitsLength: function (inputLength, version) {\n                return 4 + this.getBitsCharacterCount(version) + 8 * inputLength;\n            }\n        });\n        var modeInstances = {};\n        for (var mode in modes) {\n            modeInstances[mode] = new modes[mode]();\n        }\n        var FreeCellVisitor = function (matrix) {\n            var that = this, row = matrix.length - 1, column = matrix.length - 1, startColumn = column, dir = -1, c = 0;\n            that.move = function () {\n                row += dir * c;\n                c ^= 1;\n                column = startColumn - c;\n            };\n            that.getNextCell = function () {\n                while (matrix[row][column] !== undefined) {\n                    that.move();\n                    if (row < 0 || row >= matrix.length) {\n                        dir = -dir;\n                        startColumn -= startColumn != 8 ? 2 : 3;\n                        column = startColumn;\n                        row = dir < 0 ? matrix.length - 1 : 0;\n                    }\n                }\n                return {\n                    row: row,\n                    column: column\n                };\n            };\n            that.getNextRemainderCell = function () {\n                that.move();\n                if (matrix[row][column] === undefined) {\n                    return {\n                        row: row,\n                        column: column\n                    };\n                }\n            };\n        };\n        function fillFunctionCell(matrices, bit, x, y) {\n            for (var i = 0; i < matrices.length; i++) {\n                matrices[i][x][y] = bit;\n            }\n        }\n        function fillDataCell(matrices, bit, x, y) {\n            for (var i = 0; i < maskPatternConditions.length; i++) {\n                matrices[i][x][y] = maskPatternConditions[i](x, y) ? bit ^ 1 : parseInt(bit, 10);\n            }\n        }\n        var fillData = function (matrices, blocks) {\n            var cellVisitor = new FreeCellVisitor(matrices[0]), block, codewordIdx, cell;\n            for (var blockIdx = 0; blockIdx < blocks.length; blockIdx++) {\n                block = blocks[blockIdx];\n                codewordIdx = 0;\n                while (block.length > 0) {\n                    for (var i = 0; i < block.length; i++) {\n                        for (var j = 0; j < 8; j++) {\n                            cell = cellVisitor.getNextCell();\n                            fillDataCell(matrices, block[i][codewordIdx].charAt(j), cell.row, cell.column);\n                        }\n                    }\n                    codewordIdx++;\n                    while (block[0] && codewordIdx == block[0].length) {\n                        block.splice(0, 1);\n                    }\n                }\n            }\n            while (cell = cellVisitor.getNextRemainderCell()) {\n                fillDataCell(matrices, 0, cell.row, cell.column);\n            }\n        };\n        var padDataString = function (dataString, totalDataCodewords) {\n            var dataBitsCount = totalDataCodewords * 8, terminatorIndex = 0, paddingCodewordIndex = 0;\n            while (dataString.length < dataBitsCount && terminatorIndex < terminator.length) {\n                dataString += terminator.charAt(terminatorIndex++);\n            }\n            if (dataString.length % 8 !== 0) {\n                dataString += new Array(9 - dataString.length % 8).join('0');\n            }\n            while (dataString.length < dataBitsCount) {\n                dataString += paddingCodewords[paddingCodewordIndex];\n                paddingCodewordIndex ^= 1;\n            }\n            return dataString;\n        };\n        function generatePowersOfTwo() {\n            var result;\n            for (var power = 1; power < 255; power++) {\n                result = powersOfTwoResult[power - 1] * 2;\n                if (result > 255) {\n                    result = result ^ 285;\n                }\n                powersOfTwoResult[power] = result;\n                powersOfTwo[result] = power;\n            }\n            result = powersOfTwoResult[power - 1] * 2 ^ 285;\n            powersOfTwoResult[power] = result;\n            powersOfTwoResult[-1] = 0;\n        }\n        var xorPolynomials = function (x, y) {\n            var result = [], idx = x.length - 2;\n            for (var i = idx; i >= 0; i--) {\n                result[i] = x[i] ^ y[i];\n            }\n            return result;\n        };\n        var multiplyPolynomials = function (x, y) {\n            var result = [];\n            for (var i = 0; i < x.length; i++) {\n                for (var j = 0; j < y.length; j++) {\n                    if (result[i + j] === undefined) {\n                        result[i + j] = (x[i] + (y[j] >= 0 ? y[j] : 0)) % 255;\n                    } else {\n                        result[i + j] = powersOfTwo[powersOfTwoResult[result[i + j]] ^ powersOfTwoResult[(x[i] + y[j]) % 255]];\n                    }\n                }\n            }\n            return result;\n        };\n        function generateGeneratorPolynomials() {\n            var maxErrorCorrectionCodeWordsCount = 68;\n            for (var idx = 2; idx <= maxErrorCorrectionCodeWordsCount; idx++) {\n                var firstPolynomial = generatorPolynomials[idx - 1], secondPolynomial = [\n                        idx,\n                        0\n                    ];\n                generatorPolynomials[idx] = multiplyPolynomials(firstPolynomial, secondPolynomial);\n            }\n        }\n        generatePowersOfTwo();\n        generateGeneratorPolynomials();\n        function multiplyByConstant(polynomial, power) {\n            var result = [], idx = polynomial.length - 1;\n            do {\n                result[idx] = powersOfTwoResult[(polynomial[idx] + power) % 255];\n                idx--;\n            } while (polynomial[idx] !== undefined);\n            return result;\n        }\n        var generateErrorCodewords = function (data, errorCodewordsCount) {\n            var generator = generatorPolynomials[errorCodewordsCount - 1], result = new Array(errorCodewordsCount).concat(data), generatorPolynomial = new Array(result.length - generator.length).concat(generator), steps = data.length, errorCodewords = [], divisor, idx;\n            for (idx = 0; idx < steps; idx++) {\n                divisor = multiplyByConstant(generatorPolynomial, powersOfTwo[result[result.length - 1]]);\n                generatorPolynomial.splice(0, 1);\n                result = xorPolynomials(divisor, result);\n            }\n            for (idx = result.length - 1; idx >= 0; idx--) {\n                errorCodewords[errorCodewordsCount - 1 - idx] = toBitsString(result[idx], 8);\n            }\n            return errorCodewords;\n        };\n        var getBlocks = function (dataStream, versionCodewordsInformation) {\n            var codewordStart = 0, dataBlocks = [], errorBlocks = [], dataBlock, versionGroups = versionCodewordsInformation.groups, blockCodewordsCount, groupBlocksCount, messagePolynomial, codeword;\n            for (var groupIdx = 0; groupIdx < versionGroups.length; groupIdx++) {\n                groupBlocksCount = versionGroups[groupIdx][0];\n                for (var blockIdx = 0; blockIdx < groupBlocksCount; blockIdx++) {\n                    blockCodewordsCount = versionGroups[groupIdx][1];\n                    dataBlock = [];\n                    messagePolynomial = [];\n                    for (var codewordIdx = 1; codewordIdx <= blockCodewordsCount; codewordIdx++) {\n                        codeword = dataStream.substring(codewordStart, codewordStart + 8);\n                        dataBlock.push(codeword);\n                        messagePolynomial[blockCodewordsCount - codewordIdx] = toDecimal(codeword);\n                        codewordStart += 8;\n                    }\n                    dataBlocks.push(dataBlock);\n                    errorBlocks.push(generateErrorCodewords(messagePolynomial, versionCodewordsInformation.errorCodewordsPerBlock));\n                }\n            }\n            return [\n                dataBlocks,\n                errorBlocks\n            ];\n        };\n        var chooseMode = function (str, minNumericBeforeAlpha, minNumericBeforeByte, minAlphaBeforeByte, previousMode) {\n            var numeric = numberRegex.exec(str), numericMatch = numeric ? numeric[0] : '', alpha = alphaRegex.exec(str), alphaMatch = alpha ? alpha[0] : '', alphaNumeric = alphaNumericRegex.exec(str), alphaNumericMatch = alphaNumeric ? alphaNumeric[0] : '', mode, modeString;\n            if (numericMatch && (numericMatch.length >= minNumericBeforeAlpha || str.length == numericMatch.length || numericMatch.length >= minNumericBeforeByte && !alphaNumericRegex.test(str.charAt(numericMatch.length)))) {\n                mode = NUMERIC;\n                modeString = numericMatch;\n            } else if (alphaNumericMatch && (str.length == alphaNumericMatch.length || alphaNumericMatch.length >= minAlphaBeforeByte || previousMode == ALPHA_NUMERIC)) {\n                mode = ALPHA_NUMERIC;\n                modeString = numericMatch || alphaMatch;\n            } else {\n                mode = BYTE;\n                if (alphaNumericMatch) {\n                    modeString = alphaNumericMatch + byteRegex.exec(str.substring(alphaNumericMatch.length))[0];\n                } else {\n                    modeString = byteRegex.exec(str)[0];\n                }\n            }\n            return {\n                mode: mode,\n                modeString: modeString\n            };\n        };\n        var getModes = function (str) {\n            var modes = [], previousMode, idx = 0;\n            modes.push(chooseMode(str, initMinNumericBeforeAlpha, initMinNumericBeforeByte, initMinAlphaBeforeByte, previousMode));\n            previousMode = modes[0].mode;\n            str = str.substr(modes[0].modeString.length);\n            while (str.length > 0) {\n                var nextMode = chooseMode(str, minNumericBeforeAlpha, minNumericBeforeByte, minAlphaBeforeByte, previousMode);\n                if (nextMode.mode != previousMode) {\n                    previousMode = nextMode.mode;\n                    modes.push(nextMode);\n                    idx++;\n                } else {\n                    modes[idx].modeString += nextMode.modeString;\n                }\n                str = str.substr(nextMode.modeString.length);\n            }\n            return modes;\n        };\n        var getDataCodewordsCount = function (modes) {\n            var length = 0, mode;\n            for (var i = 0; i < modes.length; i++) {\n                mode = modeInstances[modes[i].mode];\n                length += mode.getStringBitsLength(modes[i].modeString.length);\n            }\n            return Math.ceil(length / 8);\n        };\n        var getVersion = function (dataCodewordsCount, errorCorrectionLevel) {\n            var x = 0, y = versionsCodewordsInformation.length - 1, version = Math.floor(versionsCodewordsInformation.length / 2);\n            do {\n                if (dataCodewordsCount < versionsCodewordsInformation[version][errorCorrectionLevel].totalDataCodewords) {\n                    y = version;\n                } else {\n                    x = version;\n                }\n                version = x + Math.floor((y - x) / 2);\n            } while (y - x > 1);\n            if (dataCodewordsCount <= versionsCodewordsInformation[x][errorCorrectionLevel].totalDataCodewords) {\n                return version + 1;\n            }\n            return y + 1;\n        };\n        var getDataString = function (modes, version) {\n            var dataString = '', mode;\n            for (var i = 0; i < modes.length; i++) {\n                mode = modeInstances[modes[i].mode];\n                dataString += mode.encode(modes[i].modeString, version);\n            }\n            return dataString;\n        };\n        var encodeFormatInformation = function (format) {\n            var formatNumber = toDecimal(format), encodedString, result = '';\n            if (formatNumber === 0) {\n                return '101010000010010';\n            } else {\n                encodedString = encodeBCH(toDecimal(format), formatGeneratorPolynomial, 15);\n            }\n            for (var i = 0; i < encodedString.length; i++) {\n                result += encodedString.charAt(i) ^ formatMaskPattern.charAt(i);\n            }\n            return result;\n        };\n        var encodeBCH = function (value, generatorPolynomial, codeLength) {\n            var generatorNumber = toDecimal(generatorPolynomial), polynomialLength = generatorPolynomial.length - 1, valueNumber = value << polynomialLength, length = codeLength - polynomialLength, valueString = toBitsString(value, length), result = dividePolynomials(valueNumber, generatorNumber);\n            result = valueString + toBitsString(result, polynomialLength);\n            return result;\n        };\n        var dividePolynomials = function (numberX, numberY) {\n            var yLength = numberY.toString(2).length, xLength = numberX.toString(2).length;\n            do {\n                numberX ^= numberY << xLength - yLength;\n                xLength = numberX.toString(2).length;\n            } while (xLength >= yLength);\n            return numberX;\n        };\n        function getNumberAt(str, idx) {\n            return parseInt(str.charAt(idx), 10);\n        }\n        var initMatrices = function (version) {\n            var matrices = [], modules = 17 + 4 * version;\n            for (var i = 0; i < maskPatternConditions.length; i++) {\n                matrices[i] = new Array(modules);\n                for (var j = 0; j < modules; j++) {\n                    matrices[i][j] = new Array(modules);\n                }\n            }\n            return matrices;\n        };\n        var addFormatInformation = function (matrices, formatString) {\n            var matrix = matrices[0], x, y, idx = 0, length = formatString.length;\n            for (x = 0, y = 8; x <= 8; x++) {\n                if (x !== 6) {\n                    fillFunctionCell(matrices, getNumberAt(formatString, length - 1 - idx++), x, y);\n                }\n            }\n            for (x = 8, y = 7; y >= 0; y--) {\n                if (y !== 6) {\n                    fillFunctionCell(matrices, getNumberAt(formatString, length - 1 - idx++), x, y);\n                }\n            }\n            idx = 0;\n            for (y = matrix.length - 1, x = 8; y >= matrix.length - 8; y--) {\n                fillFunctionCell(matrices, getNumberAt(formatString, length - 1 - idx++), x, y);\n            }\n            fillFunctionCell(matrices, 1, matrix.length - 8, 8);\n            for (x = matrix.length - 7, y = 8; x < matrix.length; x++) {\n                fillFunctionCell(matrices, getNumberAt(formatString, length - 1 - idx++), x, y);\n            }\n        };\n        var encodeVersionInformation = function (version) {\n            return encodeBCH(version, versionGeneratorPolynomial, 18);\n        };\n        var addVersionInformation = function (matrices, dataString) {\n            var matrix = matrices[0], modules = matrix.length, x1 = 0, y1 = modules - 11, x2 = modules - 11, y2 = 0, quotient, mod, value;\n            for (var idx = 0; idx < dataString.length; idx++) {\n                quotient = Math.floor(idx / 3);\n                mod = idx % 3;\n                value = getNumberAt(dataString, dataString.length - idx - 1);\n                fillFunctionCell(matrices, value, x1 + quotient, y1 + mod);\n                fillFunctionCell(matrices, value, x2 + mod, y2 + quotient);\n            }\n        };\n        var addCentricPattern = function (matrices, pattern, x, y) {\n            var size = pattern.length + 2, length = pattern.length + 1, value;\n            for (var i = 0; i < pattern.length; i++) {\n                for (var j = i; j < size - i; j++) {\n                    value = pattern[i];\n                    fillFunctionCell(matrices, value, x + j, y + i);\n                    fillFunctionCell(matrices, value, x + i, y + j);\n                    fillFunctionCell(matrices, value, x + length - j, y + length - i);\n                    fillFunctionCell(matrices, value, x + length - i, y + length - j);\n                }\n            }\n        };\n        var addFinderSeparator = function (matrices, direction, x, y) {\n            var nextX = x, nextY = y, matrix = matrices[0];\n            do {\n                fillFunctionCell(matrices, 0, nextX, y);\n                fillFunctionCell(matrices, 0, x, nextY);\n                nextX += direction[0];\n                nextY += direction[1];\n            } while (nextX >= 0 && nextX < matrix.length);\n        };\n        var addFinderPatterns = function (matrices) {\n            var modules = matrices[0].length;\n            addCentricPattern(matrices, finderPattern, 0, 0);\n            addFinderSeparator(matrices, [\n                -1,\n                -1\n            ], 7, 7);\n            addCentricPattern(matrices, finderPattern, modules - 7, 0);\n            addFinderSeparator(matrices, [\n                1,\n                -1\n            ], modules - 8, 7);\n            addCentricPattern(matrices, finderPattern, 0, modules - 7);\n            addFinderSeparator(matrices, [\n                -1,\n                1\n            ], 7, modules - 8);\n        };\n        var addAlignmentPatterns = function (matrices, version) {\n            if (version < 2) {\n                return;\n            }\n            var matrix = matrices[0], modules = matrix.length, pointsCount = Math.floor(version / 7), points = [6], startDistance, distance, idx = 0;\n            if (startDistance = irregularAlignmentPatternsStartDistance[version]) {\n                distance = (modules - 13 - startDistance) / pointsCount;\n            } else {\n                startDistance = distance = (modules - 13) / (pointsCount + 1);\n            }\n            points.push(points[idx++] + startDistance);\n            while (points[idx] + distance < modules) {\n                points.push(points[idx++] + distance);\n            }\n            for (var i = 0; i < points.length; i++) {\n                for (var j = 0; j < points.length; j++) {\n                    if (matrix[points[i]][points[j]] === undefined) {\n                        addCentricPattern(matrices, alignmentPattern, points[i] - 2, points[j] - 2);\n                    }\n                }\n            }\n        };\n        var addTimingFunctions = function (matrices) {\n            var row = 6, column = 6, value = 1, modules = matrices[0].length;\n            for (var i = 8; i < modules - 8; i++) {\n                fillFunctionCell(matrices, value, row, i);\n                fillFunctionCell(matrices, value, i, column);\n                value ^= 1;\n            }\n        };\n        var scoreMaskMatrixes = function (matrices) {\n            var scores = [], previousBits = [], darkModules = [], patterns = [], adjacentSameBits = [], matrix, i, row = 0, column = 1, modules = matrices[0].length;\n            for (i = 0; i < matrices.length; i++) {\n                scores[i] = 0;\n                darkModules[i] = 0;\n                adjacentSameBits[i] = [\n                    0,\n                    0\n                ];\n                patterns[i] = [\n                    0,\n                    0\n                ];\n                previousBits[i] = [];\n            }\n            for (i = 0; i < modules; i++) {\n                for (var j = 0; j < modules; j++) {\n                    for (var k = 0; k < matrices.length; k++) {\n                        matrix = matrices[k];\n                        darkModules[k] += parseInt(matrix[i][j], 10);\n                        if (previousBits[k][row] === matrix[i][j] && i + 1 < modules && j - 1 >= 0 && matrix[i + 1][j] == previousBits[k][row] && matrix[i + 1][j - 1] == previousBits[k][row]) {\n                            scores[k] += 3;\n                        }\n                        scoreFinderPatternOccurance(k, patterns, scores, row, matrix[i][j]);\n                        scoreFinderPatternOccurance(k, patterns, scores, column, matrix[j][i]);\n                        scoreAdjacentSameBits(k, scores, previousBits, matrix[i][j], adjacentSameBits, row);\n                        scoreAdjacentSameBits(k, scores, previousBits, matrix[j][i], adjacentSameBits, column);\n                    }\n                }\n            }\n            var total = modules * modules, minIdx, min = Number.MAX_VALUE;\n            for (i = 0; i < scores.length; i++) {\n                scores[i] += calculateDarkModulesRatioScore(darkModules[i], total);\n                if (scores[i] < min) {\n                    min = scores[i];\n                    minIdx = i;\n                }\n            }\n            return minIdx;\n        };\n        function scoreFinderPatternOccurance(idx, patterns, scores, rowColumn, bit) {\n            patterns[idx][rowColumn] = (patterns[idx][rowColumn] << 1 ^ bit) % 128;\n            if (patterns[idx][rowColumn] == finderPatternValue) {\n                scores[idx] += 40;\n            }\n        }\n        function scoreAdjacentSameBits(idx, scores, previousBits, bit, adjacentBits, rowColumn) {\n            if (previousBits[idx][rowColumn] == bit) {\n                adjacentBits[idx][rowColumn]++;\n            } else {\n                previousBits[idx][rowColumn] = bit;\n                if (adjacentBits[idx][rowColumn] >= 5) {\n                    scores[idx] += 3 + adjacentBits[idx][rowColumn] - 5;\n                }\n                adjacentBits[idx][rowColumn] = 1;\n            }\n        }\n        function calculateDarkModulesRatioScore(darkModules, total) {\n            var percent = Math.floor(darkModules / total * 100), mod5 = percent % 5, previous = Math.abs(percent - mod5 - 50), next = Math.abs(percent + 5 - mod5 - 50), score = 10 * Math.min(previous / 5, next / 5);\n            return score;\n        }\n        var EncodingResult = function (dataString, version) {\n            this.dataString = dataString;\n            this.version = version;\n        };\n        var IsoEncoder = function () {\n            this.getEncodingResult = function (inputString, errorCorrectionLevel) {\n                var modes = getModes(inputString), dataCodewordsCount = getDataCodewordsCount(modes), version = getVersion(dataCodewordsCount, errorCorrectionLevel), dataString = getDataString(modes, version);\n                return new EncodingResult(dataString, version);\n            };\n        };\n        var UTF8Encoder = function () {\n            this.mode = modeInstances[this.encodingMode];\n        };\n        UTF8Encoder.fn = UTF8Encoder.prototype = {\n            encodingMode: BYTE,\n            utfBOM: '111011111011101110111111',\n            initialModeCountStringLength: 20,\n            getEncodingResult: function (inputString, errorCorrectionLevel) {\n                var that = this, data = that.encode(inputString), dataCodewordsCount = that.getDataCodewordsCount(data), version = getVersion(dataCodewordsCount, errorCorrectionLevel), dataString = that.mode.getModeCountString(data.length / 8, version) + data;\n                return new EncodingResult(dataString, version);\n            },\n            getDataCodewordsCount: function (data) {\n                var that = this, dataLength = data.length, dataCodewordsCount = Math.ceil((that.initialModeCountStringLength + dataLength) / 8);\n                return dataCodewordsCount;\n            },\n            encode: function (str) {\n                var that = this, result = that.utfBOM;\n                for (var i = 0; i < str.length; i++) {\n                    result += that.encodeCharacter(str.charCodeAt(i));\n                }\n                return result;\n            },\n            encodeCharacter: function (code) {\n                var bytesCount = this.getBytesCount(code), bc = bytesCount - 1, result = '';\n                if (bytesCount == 1) {\n                    result = toBitsString(code, 8);\n                } else {\n                    var significantOnes = 8 - bytesCount;\n                    for (var i = 0; i < bc; i++) {\n                        result = toBitsString(code >> i * 6 & 63 | 128, 8) + result;\n                    }\n                    result = (code >> bc * 6 | 255 >> significantOnes << significantOnes).toString(2) + result;\n                }\n                return result;\n            },\n            getBytesCount: function (code) {\n                var ranges = this.ranges;\n                for (var i = 0; i < ranges.length; i++) {\n                    if (code < ranges[i]) {\n                        return i + 1;\n                    }\n                }\n            },\n            ranges: [\n                128,\n                2048,\n                65536,\n                2097152,\n                67108864\n            ]\n        };\n        var QRCodeDataEncoder = function (encoding) {\n            if (encoding && encoding.toLowerCase().indexOf('utf_8') >= 0) {\n                return new UTF8Encoder();\n            } else {\n                return new IsoEncoder();\n            }\n        };\n        var encodeData = function (inputString, errorCorrectionLevel, encoding) {\n            var encoder = new QRCodeDataEncoder(encoding), encodingResult = encoder.getEncodingResult(inputString, errorCorrectionLevel), version = encodingResult.version, versionInformation = versionsCodewordsInformation[version - 1][errorCorrectionLevel], dataString = padDataString(encodingResult.dataString, versionInformation.totalDataCodewords), blocks = getBlocks(dataString, versionInformation), matrices = initMatrices(version);\n            addFinderPatterns(matrices);\n            addAlignmentPatterns(matrices, version);\n            addTimingFunctions(matrices);\n            if (version >= 7) {\n                addVersionInformation(matrices, toBitsString(0, 18));\n            }\n            addFormatInformation(matrices, toBitsString(0, 15));\n            fillData(matrices, blocks);\n            var minIdx = scoreMaskMatrixes(matrices), optimalMatrix = matrices[minIdx];\n            if (version >= 7) {\n                addVersionInformation([optimalMatrix], encodeVersionInformation(version));\n            }\n            var formatString = errorCorrectionPatterns[errorCorrectionLevel] + toBitsString(minIdx, 3);\n            addFormatInformation([optimalMatrix], encodeFormatInformation(formatString));\n            return optimalMatrix;\n        };\n        var QRCodeDefaults = {\n            DEFAULT_SIZE: 200,\n            QUIET_ZONE_LENGTH: 4,\n            DEFAULT_ERROR_CORRECTION_LEVEL: 'L',\n            DEFAULT_BACKGROUND: '#fff',\n            DEFAULT_DARK_MODULE_COLOR: '#000',\n            MIN_BASE_UNIT_SIZE: 1\n        };\n        var QRCode = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that.element = $(element);\n                that.wrapper = that.element;\n                that.element.addClass('k-qrcode');\n                that.surfaceWrap = $('<div />').css('position', 'relative').appendTo(this.element);\n                that.surface = draw.Surface.create(that.surfaceWrap, { type: that.options.renderAs });\n                that.setOptions(options);\n            },\n            redraw: function () {\n                var size = this._getSize();\n                this.surfaceWrap.css({\n                    width: size,\n                    height: size\n                });\n                this.surface.clear();\n                this.createVisual();\n                this.surface.draw(this.visual);\n            },\n            getSize: function () {\n                return kendo.dimensions(this.element);\n            },\n            _resize: function () {\n                this.redraw();\n            },\n            createVisual: function () {\n                this.visual = this._render();\n            },\n            exportVisual: function () {\n                return this._render();\n            },\n            _render: function () {\n                var that = this, value = that._value, baseUnit, border = that.options.border || {}, padding = that.options.padding || 0, borderWidth = border.width || 0, quietZoneSize, matrix, size, dataSize, contentSize;\n                border.width = borderWidth;\n                var visual = new draw.Group();\n                if (value) {\n                    matrix = encodeData(value, that.options.errorCorrection, that.options.encoding);\n                    size = that._getSize();\n                    contentSize = size - 2 * (borderWidth + padding);\n                    baseUnit = that._calculateBaseUnit(contentSize, matrix.length);\n                    dataSize = matrix.length * baseUnit;\n                    quietZoneSize = borderWidth + padding + (contentSize - dataSize) / 2;\n                    visual.append(that._renderBackground(size, border));\n                    visual.append(that._renderMatrix(matrix, baseUnit, quietZoneSize));\n                }\n                return visual;\n            },\n            _getSize: function () {\n                var that = this, size;\n                if (that.options.size) {\n                    size = parseInt(that.options.size, 10);\n                } else {\n                    var element = that.element, min = Math.min(element.width(), element.height());\n                    if (min > 0) {\n                        size = min;\n                    } else {\n                        size = QRCodeDefaults.DEFAULT_SIZE;\n                    }\n                }\n                return size;\n            },\n            _calculateBaseUnit: function (size, matrixSize) {\n                var baseUnit = Math.floor(size / matrixSize);\n                if (baseUnit < QRCodeDefaults.MIN_BASE_UNIT_SIZE) {\n                    throw new Error('Insufficient size.');\n                }\n                if (baseUnit * matrixSize >= size && baseUnit - 1 >= QRCodeDefaults.MIN_BASE_UNIT_SIZE) {\n                    baseUnit--;\n                }\n                return baseUnit;\n            },\n            _renderMatrix: function (matrix, baseUnit, quietZoneSize) {\n                var path = new draw.MultiPath({\n                    fill: { color: this.options.color },\n                    stroke: null\n                });\n                for (var row = 0; row < matrix.length; row++) {\n                    var y = quietZoneSize + row * baseUnit;\n                    var column = 0;\n                    while (column < matrix.length) {\n                        while (matrix[row][column] === 0 && column < matrix.length) {\n                            column++;\n                        }\n                        if (column < matrix.length) {\n                            var x = column;\n                            while (matrix[row][column] == 1) {\n                                column++;\n                            }\n                            var x1 = round(quietZoneSize + x * baseUnit);\n                            var y1 = round(y);\n                            var x2 = round(quietZoneSize + column * baseUnit);\n                            var y2 = round(y + baseUnit);\n                            path.moveTo(x1, y1).lineTo(x1, y2).lineTo(x2, y2).lineTo(x2, y1).close();\n                        }\n                    }\n                }\n                return path;\n            },\n            _renderBackground: function (size, border) {\n                var box = new Box2D(0, 0, size, size).unpad(border.width / 2);\n                return draw.Path.fromRect(box.toRect(), {\n                    fill: { color: this.options.background },\n                    stroke: {\n                        color: border.color,\n                        width: border.width\n                    }\n                });\n            },\n            setOptions: function (options) {\n                var that = this;\n                options = options || {};\n                that.options = extend(that.options, options);\n                if (options.value !== undefined) {\n                    that._value = that.options.value + '';\n                }\n                that.redraw();\n            },\n            value: function (value) {\n                var that = this;\n                if (value === undefined) {\n                    return that._value;\n                }\n                that._value = value + '';\n                that.redraw();\n            },\n            options: {\n                name: 'QRCode',\n                renderAs: 'svg',\n                encoding: 'ISO_8859_1',\n                value: '',\n                errorCorrection: QRCodeDefaults.DEFAULT_ERROR_CORRECTION_LEVEL,\n                background: QRCodeDefaults.DEFAULT_BACKGROUND,\n                color: QRCodeDefaults.DEFAULT_DARK_MODULE_COLOR,\n                size: '',\n                padding: 0,\n                border: {\n                    color: '',\n                    width: 0\n                }\n            }\n        });\n        dataviz.ExportMixin.extend(QRCode.fn);\n        dataviz.ui.plugin(QRCode);\n        kendo.deepExtend(dataviz, {\n            QRCode: QRCode,\n            QRCodeDefaults: QRCodeDefaults,\n            QRCodeFunctions: {\n                FreeCellVisitor: FreeCellVisitor,\n                fillData: fillData,\n                padDataString: padDataString,\n                generateErrorCodewords: generateErrorCodewords,\n                xorPolynomials: xorPolynomials,\n                getBlocks: getBlocks,\n                multiplyPolynomials: multiplyPolynomials,\n                chooseMode: chooseMode,\n                getModes: getModes,\n                getDataCodewordsCount: getDataCodewordsCount,\n                getVersion: getVersion,\n                getDataString: getDataString,\n                encodeFormatInformation: encodeFormatInformation,\n                encodeBCH: encodeBCH,\n                dividePolynomials: dividePolynomials,\n                initMatrices: initMatrices,\n                addFormatInformation: addFormatInformation,\n                encodeVersionInformation: encodeVersionInformation,\n                addVersionInformation: addVersionInformation,\n                addCentricPattern: addCentricPattern,\n                addFinderSeparator: addFinderSeparator,\n                addFinderPatterns: addFinderPatterns,\n                addAlignmentPatterns: addAlignmentPatterns,\n                addTimingFunctions: addTimingFunctions,\n                scoreMaskMatrixes: scoreMaskMatrixes,\n                encodeData: encodeData,\n                UTF8Encoder: UTF8Encoder\n            },\n            QRCodeFields: {\n                modes: modeInstances,\n                powersOfTwo: powersOfTwo,\n                powersOfTwoResult: powersOfTwoResult,\n                generatorPolynomials: generatorPolynomials\n            }\n        });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}