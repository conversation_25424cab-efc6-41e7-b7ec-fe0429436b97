{"version": 3, "sources": ["kendo.list.js"], "names": ["f", "define", "$", "undefined", "unifyType", "value", "type", "last", "list", "length", "getSelectedOption", "select", "index", "selectedIndex", "options", "mapChangedItems", "selected", "itemsToMatch", "dataItem", "found", "i", "j", "itemsLength", "<PERSON><PERSON><PERSON><PERSON>", "changed", "unchanged", "push", "item", "isValidFilterExpr", "expression", "isEmptyObject", "filters", "removeFiltersForField", "field", "grep", "filter", "STATIC_LIST_NS", "StaticList", "kendo", "window", "ui", "outerHeight", "_outerHeight", "percentageUnitsRegex", "Widget", "keys", "support", "htmlEncode", "activeElement", "_activeElement", "outerWidth", "_outerWidth", "ObservableArray", "data", "ID", "CHANGE", "FOCUSED", "HOVER", "LOADING", "GROUPHEADER", "ITEMSELECTOR", "LABELIDPART", "OPEN", "CLOSE", "CASCADE", "SELECT", "SELECTED", "REQUESTSTART", "REQUESTEND", "extend", "proxy", "isArray", "browser", "HIDDENCLASS", "WIDTH", "isIE", "msie", "isIE8", "version", "quotRegExp", "alternativeNames", "ComboBox", "DropDownList", "List", "DataBoundWidget", "init", "element", "id", "that", "this", "ns", "fn", "call", "_isSelect", "is", "dataSource", "dataTextField", "dataValueField", "ul", "attr", "tabIndex", "aria-hidden", "append", "on", "_listMousedown", "guid", "columns", "removeClass", "addClass", "_columnsHeader", "_header", "_noData", "_footer", "_accessors", "_initValue", "valuePrimitive", "footerTemplate", "headerTemplate", "noDataTemplate", "setOptions", "enable", "enabled", "_renderFooter", "_renderNoData", "focus", "_focused", "readonly", "_editable", "disable", "header", "template", "_angularElement", "destroy", "remove", "prepend", "colGroup", "row", "idx", "currentColumn", "title", "columnsHeaderTemplate", "currentWidth", "currentWidthInt", "widthStyle", "columnsHeader", "width", "parseInt", "isNaN", "test", "noData", "angular", "elements", "appendTo", "footer", "_listOptions", "currentOptions", "virtual", "changeEventOption", "change", "_listChange", "listBoundHandler", "_listBound", "autoBind", "selectable", "click", "_click", "activate", "_activateItem", "deactivate", "_deactivateItem", "dataBinding", "trigger", "dataBound", "height", "groupTemplate", "fixedGroupTemplate", "expr", "$angular", "_initList", "listOptions", "selectedItemChange", "listView", "VirtualList", "bind", "_setListValue", "done", "_updateSelectionState", "noop", "e", "filterInput", "target", "preventDefault", "_isFilterEnabled", "_hideClear", "_clear", "_showClear", "_clearValue", "_clearText", "_accessor", "_customOption", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_filter", "word", "open", "highlight<PERSON><PERSON><PERSON>", "_change", "text", "_clearFilter", "bound", "_filterSource", "force", "newExpression", "dataSourceState", "isMultiColumnFiltering", "filterFields", "logic", "resetPageSettings", "removed", "_clearFilterExpressions", "_pushFilterExpression", "concat", "_cascading", "setDSFilter", "page", "pageSize", "sort", "group", "aggregate", "_mergeState", "filtersToRemove", "splice", "action", "children", "html", "instance", "_toggleNoData", "show", "toggle", "_toggleHeader", "groupHeader", "content", "prev", "_allowOpening", "flatView", "val", "_old", "_ignoreCase", "model", "reader", "fields", "ignoreCase", "_focus", "candidate", "widgetOptions", "fromFilter", "_buildExpression", "_open", "accentFoldingFiltering", "toLocaleLowerCase", "toLowerCase", "operator", "_clearButton", "clearTitle", "messages", "clear", "role", "clearButton", "search", "_inputValue", "clearTimeout", "_typingTimeout", "<PERSON><PERSON><PERSON><PERSON>", "_state", "_emptySearch", "trim", "_searchByWord", "current", "items", "_unbindDataSource", "off", "popup", "_form", "_reset<PERSON><PERSON><PERSON>", "selectedDataItems", "getByUid", "add", "removeAttr", "getter", "textField", "valueField", "_text", "_value", "_aria", "suggest", "_a<PERSON><PERSON><PERSON><PERSON>", "labelId", "focusedElm", "inputElm", "inputId", "labelElm", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "_generateLabelId", "label", "_blur", "close", "optionValue", "_oldText", "_valueBeforeCascade", "_userTriggered", "_oldIndex", "isFiltered", "_typing", "typing", "_data", "view", "_enable", "disabled", "_dataValue", "_offsetHeight", "offsetHeight", "siblings", "prevAll", "each", "_height", "offsetTop", "popups", "footerHeight", "visible", "parent", "hide", "scrollHeight", "_open<PERSON><PERSON><PERSON>", "_adjustListWidth", "computedStyle", "computedWidth", "style", "wrapper", "getComputedStyle", "parseFloat", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "css", "fontFamily", "autoWidth", "min<PERSON><PERSON><PERSON>", "whiteSpace", "_<PERSON><PERSON><PERSON><PERSON>", "_focusItem", "noFocusedItem", "scrollToIndex", "_calculateGroupPadding", "li", "padding", "direction", "display", "scrollbar", "parents", "_calculatePopupHeight", "_calculateColumnsHeaderPadding", "isRtl", "_refreshScroll", "enableYScroll", "overflowX", "overflowY", "_resizePopup", "one", "_popup", "Popup", "anchor", "animation", "autosize", "_makeUnselectable", "find", "not", "_toggleHover", "currentTarget", "toggleClass", "_toggle", "preventFocus", "touchEnabled", "mobileOS", "touch", "MSPointers", "pointers", "_prevent", "_triggerCascade", "_cascadeTriggered", "_cascadedValue", "userTriggered", "_triggerChange", "unbind", "_requestStartHandler", "_requestEndHandler", "_error<PERSON><PERSON><PERSON>", "requireValueMapper", "hasValue", "Array", "valueMapper", "Error", "inArray", "node", "parentNode", "Select", "_initial", "setDataSource", "_dataSource", "_initialIndex", "_current", "fetch", "_parentWidget", "_cascadeSelect", "_select", "_cascadeValue", "_accessorInput", "_accessorSelect", "_custom", "_syncValueAndText", "custom", "_hideBusy", "_busy", "_arrowIcon", "_showBusy", "isDefaultPrevented", "_request", "setTimeout", "_requestEnd", "DataSource", "create", "_firstItem", "focusFirst", "_lastItem", "focusLast", "_nextItem", "focusNext", "_prevItem", "focusPrev", "_move", "pressed", "activeFilter", "selection", "should<PERSON><PERSON>ger", "key", "keyCode", "down", "DOWN", "UP", "altKey", "<PERSON><PERSON><PERSON><PERSON>", "_fetch", "hasClass", "dataItemByIndex", "getElementIndex", "ENTER", "TAB", "input", "_focusElement", "focusout", "ESC", "PAGEDOWN", "PAGEUP", "scrollWith", "screenHeight", "_fetchData", "hasItems", "cascadeFrom", "_options", "optionLabel", "option", "dataText", "dataValue", "htmlElement", "indexOf", "replace", "setAttribute", "_reset", "formId", "form", "closest", "parentElement", "name", "_cascade", "cascade", "_cascadeHandlerProxy", "_cascadeHandler", "_cascadeFilterRequests", "_selectedValue", "first", "_toggleCascadeOnFocus", "valueBeforeCascade", "_clearSelection", "_cascadeChange", "expressions", "handler", "filterValue", "cascadeFromParentField", "cascadeFromField", "<PERSON><PERSON><PERSON><PERSON>", "shift", "_touchHandlers", "wrap", "before", "_optionID", "_selectedIndices", "_view", "_dataItems", "_values", "slice", "_getter", "_templates", "_onScroll", "_scrollId", "_renderHeader", "events", "source", "_refresh<PERSON><PERSON><PERSON>", "refresh", "_fixedHeader", "startY", "endY", "tapPosition", "event", "originalEvent", "changedTouches", "pageY", "Math", "abs", "_touchTriggered", "_triggerClick", "get", "skip", "_render", "clientHeight", "scroll", "scrollTop", "itemOffsetTop", "itemOffsetHeight", "contentScrollTop", "contentOffsetHeight", "bottomDistance", "dataItems", "_getValues", "_valueGetter", "map", "next", "hasCandidate", "_get", "focusIndex", "skipUpdate", "_skipUpdate", "indices", "result", "deferred", "filtered", "singleSelection", "selectedIndices", "uiSelectedIndices", "added", "Deferred", "resolve", "_deselectFiltered", "_deselect", "_valueComparer", "removeAt", "position", "setValue", "_valueDeferred", "state", "_valueIndices", "_valueExpr", "values", "body", "comparer", "normalized", "_valueType", "Function", "_dataItemPosition", "valueExpr", "removedIndices", "_template", "useWithBlock", "templateText", "templates", "_normalizeIndices", "newIndices", "_firstVisibleItem", "itemHeight", "itemIndex", "floor", "<PERSON><PERSON><PERSON><PERSON>", "forward", "nextS<PERSON>ling", "previousSibling", "isGrouped", "visibleItem", "_renderItem", "context", "notFirstItem", "hasColumns", "newGroup", "isLastGroupedItem", "_renderColumns", "dataContext", "_selected", "innerHTML", "_lastDS<PERSON>ilter", "Query", "compareFilters", "skipUpdateOnBind", "isItemChange", "_angularItems", "_bound", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,cACH,aACA,eACDD,IACL,WA0hEE,MA/gEC,UAAUE,EAAGC,GA0xBV,QAASC,GAAUC,EAAOC,GAUtB,MATID,KAAUF,GAAuB,KAAVE,GAA0B,OAAVA,IAC1B,YAATC,EACAD,IAAgBA,EACA,WAATC,EACPD,GAAeA,EACC,WAATC,IACPD,EAAQA,GAAAA,IAGTA,EAuqCX,QAASE,GAAKC,GACV,MAAOA,GAAKA,EAAKC,OAAS,GAE9B,QAASC,GAAkBC,GACvB,GAAIC,GAAQD,EAAOE,aACnB,OAAOD,MAAaD,EAAOG,QAAQF,MAEvC,QAASG,GAAgBC,EAAUC,GAAnC,GAGQC,GACAC,EACAC,EAAGC,EAJHC,EAAcL,EAAaR,OAC3Bc,EAAiBP,EAASP,OAI1Be,KACAC,IACJ,IAAIF,EACA,IAAKH,EAAI,EAAGA,EAAIG,EAAgBH,IAAK,CAGjC,IAFAF,EAAWF,EAASI,GACpBD,GAAQ,EACHE,EAAI,EAAGA,EAAIC,EAAaD,IACzB,GAAIH,IAAaD,EAAaI,GAAI,CAC9BF,GAAQ,EACRK,EAAQE,MACJd,MAAOQ,EACPO,KAAMT,GAEV,OAGHC,GACDM,EAAUC,KAAKR,GAI3B,OACIM,QAASA,EACTC,UAAWA,GAGnB,QAASG,GAAkBC,GACvB,SAAKA,GAAc3B,EAAE4B,cAAcD,OAG/BA,EAAWE,UAAYF,EAAWE,QAAQtB,QAKlD,QAASuB,GAAsBH,EAAYI,GAA3C,GACQF,GACAZ,GAAQ,CAeZ,OAdIU,GAAWE,UACXA,EAAU7B,EAAEgC,KAAKL,EAAWE,QAAS,SAAUI,GAE3C,MADAhB,GAAQa,EAAsBG,EAAQF,GAClCE,EAAOJ,QACAI,EAAOJ,QAAQtB,OAEf0B,EAAOF,OAASA,IAG1Bd,GAASU,EAAWE,QAAQtB,SAAWsB,EAAQtB,SAChDU,GAAQ,GAEZU,EAAWE,QAAUA,GAElBZ,EA5gEd,GAuwCOiB,GACAC,EAvwCAC,EAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAAcH,EAAMI,aAAcC,EAAuB,kBAAmBC,EAASJ,EAAGI,OAAQC,EAAOP,EAAMO,KAAMC,EAAUR,EAAMQ,QAASC,EAAaT,EAAMS,WAAYC,EAAgBV,EAAMW,eAAgBC,EAAaZ,EAAMa,YAAaC,EAAkBd,EAAMe,KAAKD,gBAAiBE,EAAK,KAAMC,EAAS,SAAUC,EAAU,kBAAmBC,EAAQ,gBAAiBC,EAAU,cAAeC,EAAc,kBAAmBC,EAAe,UAAWC,EAAc,SAAUC,EAAO,OAAQC,EAAQ,QAASC,EAAU,UAAWC,EAAS,SAAUC,EAAW,WAAYC,EAAe,eAAgBC,EAAa,aAAcC,EAASnE,EAAEmE,OAAQC,EAAQpE,EAAEoE,MAAOC,EAAUrE,EAAEqE,QAASC,EAAU1B,EAAQ0B,QAASC,EAAc,WAAYC,EAAQ,QAASC,EAAOH,EAAQI,KAAMC,EAAQF,GAAQH,EAAQM,QAAU,EAAGC,EAAa,KAAMC,GACr2BC,SAAY,eACZC,aAAgB,YAEpBC,EAAO7C,EAAME,GAAG4C,gBAAgBf,QAChCgB,KAAM,SAAUC,EAASxE,GACrB,GAA+ByE,GAA3BC,EAAOC,KAAMC,EAAKF,EAAKE,EAC3B9C,GAAO+C,GAAGN,KAAKO,KAAKJ,EAAMF,EAASxE,GACnCwE,EAAUE,EAAKF,QACfxE,EAAU0E,EAAK1E,QACf0E,EAAKK,UAAYP,EAAQQ,GAAG7B,GACxBuB,EAAKK,WAAaL,EAAKF,QAAQ,GAAG7E,SAC7BK,EAAQiF,aACTjF,EAAQkF,cAAgBlF,EAAQkF,eAAiB,OACjDlF,EAAQmF,eAAiBnF,EAAQmF,gBAAkB,UAG3DT,EAAKU,GAAKhG,EAAE,kDAAkDiG,MAC1DC,YACAC,eAAe,IAEnBb,EAAKhF,KAAON,EAAE,mCAAqCoG,OAAOd,EAAKU,IAAIK,GAAG,YAAcb,EAAIpB,EAAMkB,EAAKgB,eAAgBhB,IACnHD,EAAKD,EAAQa,KAAK7C,GACbiC,IACDA,EAAKjD,EAAMmE,QAEfjB,EAAKhF,KAAK2F,KAAK7C,EAAIiC,EAAK,SACxBC,EAAKU,GAAGC,KAAK7C,EAAIiC,EAAK,YAClBzE,EAAQ4F,SAAW5F,EAAQ4F,QAAQjG,SACnC+E,EAAKU,GAAGS,YAAY,UAAUC,SAAS,eACvCpB,EAAKqB,kBAETrB,EAAKsB,UACLtB,EAAKuB,UACLvB,EAAKwB,UACLxB,EAAKyB,aACLzB,EAAK0B,cAETpG,SACIqG,gBAAgB,EAChBC,eAAgB,GAChBC,eAAgB,GAChBC,eAAgB,kBAEpBC,WAAY,SAAUzG,GAClB8B,EAAO+C,GAAG4B,WAAW3B,KAAKH,KAAM3E,GAC5BA,GAAWA,EAAQ0G,SAAWrH,IAC9BW,EAAQ2G,QAAU3G,EAAQ0G,QAE1B1G,EAAQ4F,SAAW5F,EAAQ4F,QAAQjG,QACnCgF,KAAKoB,iBAETpB,KAAKqB,UACLrB,KAAKsB,UACLtB,KAAKuB,UACLvB,KAAKiC,gBACLjC,KAAKkC,iBAETC,MAAO,WACHnC,KAAKoC,SAASD,SAElBE,SAAU,SAAUA,GAChBrC,KAAKsC,WACDD,SAAUA,IAAa3H,GAAmB2H,EAC1CE,SAAS,KAGjBR,OAAQ,SAAUA,GACd/B,KAAKsC,WACDD,UAAU,EACVE,UAAWR,EAASA,IAAWrH,GAAmBqH,MAG1DV,QAAS,WAAA,GAWDO,GAVA7G,EAAOiF,KACPwC,EAAS/H,EAAEM,EAAKyH,QAChBC,EAAW1H,EAAKM,QAAQuG,cAI5B,OAHA5B,MAAK0C,gBAAgBF,EAAQ,WAC7B3F,EAAM8F,QAAQH,GACdA,EAAOI,SACFH,GAIDb,EAAqC,kBAAba,GAA0B5F,EAAM4F,SAASA,GAAYA,EACjFD,EAAS/H,EAAEmH,OACX7G,EAAKyH,OAASA,EAAO,GAAKA,EAAS,KACnCzH,EAAKA,KAAK8H,QAAQL,GAClBxC,KAAK0C,gBAAgB3H,EAAKyH,OAAQ,WAJ9BZ,IAHA7G,EAAKyH,OAAS,KACd,IAQRpB,eAAgB,WAAA,GAMRoB,GACAM,EACAC,EACKC,EACDC,EACAC,EACAT,EACAU,EACAC,EACAC,EACAC,EAfJvI,EAAOiF,KACPuD,EAAgB9I,EAAEM,EAAKwI,cAO3B,KANAvD,KAAK0C,gBAAgBa,EAAe,WACpC1G,EAAM8F,QAAQY,GACdA,EAAcX,SACVJ,EAAS,qEACTM,EAAW,aACXC,EAAM,OACDC,EAAM,EAAGA,EAAMhD,KAAK3E,QAAQ4F,QAAQjG,OAAQgI,IAC7CC,EAAgBjD,KAAK3E,QAAQ4F,QAAQ+B,GACrCE,EAAQD,EAAcC,OAASD,EAAczG,OAAS,GACtDiG,EAAWQ,EAAcrB,gBAAkBsB,EAC3CC,EAA4C,kBAAbV,GAA0B5F,EAAM4F,SAASA,GAAYA,EACpFW,EAAeH,EAAcO,MAC7BH,EAAkBI,SAASL,EAAc,IACzCE,EAAa,GACbF,IAAiBM,MAAML,KACvBC,GAAc,gBACdA,GAAcD,EACdC,GAAcpG,EAAqByG,KAAKP,GAAgB,IAAM,KAC9DE,GAAc,MAElBR,GAAY,QAAUQ,EAAa,KACnCP,GAAO,wBACPA,GAAOI,EAAsBF,GAC7BF,GAAO,OAEXD,IAAY,cACZC,GAAO,QACPP,GAAUM,EACVN,GAAUO,EACVP,GAAU,uBACVzH,EAAKwI,cAAgBA,EAAgB9I,EAAE+H,GACvCzH,EAAKA,KAAK8H,QAAQU,GAClBvD,KAAK0C,gBAAgB3H,EAAKwI,cAAe,YAE7CjC,QAAS,WAAA,GACDvG,GAAOiF,KACP4D,EAASnJ,EAAEM,EAAK6I,QAChBnB,EAAW1H,EAAKM,QAAQwG,cAM5B,OALA9G,GAAK8I,QAAQ,UAAW,WACpB,OAASC,SAAUF,KAEvB/G,EAAM8F,QAAQiB,GACdA,EAAOhB,SACFH,GAIL1H,EAAK6I,OAASnJ,EAAE,gEAAgEsJ,SAAShJ,EAAKA,MAC9FA,EAAK8G,eAAqC,kBAAbY,GAA0B5F,EAAM4F,SAASA,GAAYA,EADlF1H,IAHIA,EAAK6I,OAAS,KACd,IAKRrC,QAAS,WAAA,GACDxG,GAAOiF,KACPgE,EAASvJ,EAAEM,EAAKiJ,QAChBvB,EAAW1H,EAAKM,QAAQsG,cAI5B,OAHA3B,MAAK0C,gBAAgBsB,EAAQ,WAC7BnH,EAAM8F,QAAQqB,GACdA,EAAOpB,SACFH,GAIL1H,EAAKiJ,OAASvJ,EAAE,gCAAgCsJ,SAAShJ,EAAKA,MAC9DA,EAAK4G,eAAqC,kBAAbc,GAA0B5F,EAAM4F,SAASA,GAAYA,EADlF1H,IAHIA,EAAKiJ,OAAS,KACd,IAKRC,aAAc,SAAU5I,GAAV,GACN0E,GAAOC,KACPkE,EAAiBnE,EAAK1E,QACtB8I,EAAUD,EAAeC,QACzBC,GAAsBC,OAAQxF,EAAMkB,EAAKuE,YAAavE,IACtDwE,EAAmB1F,EAAMkB,EAAKyE,WAAYzE,EA2B9C,OA1BAoE,GAA6B,gBAAZA,GAAuBA,KACxC9I,EAAUZ,EAAEmE,QACR6F,UAAU,EACVC,YAAY,EACZpE,WAAYP,EAAKO,WACjBqE,MAAO9F,EAAMkB,EAAK6E,OAAQ7E,GAC1B8E,SAAUhG,EAAMkB,EAAK+E,cAAe/E,GACpCkB,QAASiD,EAAejD,QACxB8D,WAAYlG,EAAMkB,EAAKiF,gBAAiBjF,GACxCkF,YAAa,WACTlF,EAAKmF,QAAQ,gBAEjBC,UAAWZ,EACXa,OAAQlB,EAAekB,OACvB5E,eAAgB0D,EAAe1D,eAC/BD,cAAe2D,EAAe3D,cAC9B8E,cAAenB,EAAemB,cAC9BC,mBAAoBpB,EAAeoB,mBACnC7C,SAAUyB,EAAezB,UAC1BpH,EAAS8I,EAASC,GAChB/I,EAAQoH,WACTpH,EAAQoH,SAAW,KAAO5F,EAAM0I,KAAKlK,EAAQkF,cAAe,QAAU,KAEtE2D,EAAesB,WACfnK,EAAQmK,SAAWtB,EAAesB,UAE/BnK,GAEXoK,UAAW,WAAA,GACH1F,GAAOC,KACP0F,EAAc3F,EAAKkE,cAAe0B,mBAAoB9G,EAAMkB,EAAKuE,YAAavE,IAI9EA,GAAK6F,SAHJ7F,EAAK1E,QAAQ8I,QAGE,GAAItH,GAAME,GAAG8I,YAAY9F,EAAKU,GAAIiF,GAFlC,GAAI7I,GAAME,GAAGH,WAAWmD,EAAKU,GAAIiF,GAIrD3F,EAAK6F,SAASE,KAAK,YAAajH,EAAMkB,EAAKyE,WAAYzE,IACvDA,EAAKgG,iBAETA,cAAe,SAAUnL,GACrBA,EAAQA,GAASoF,KAAK3E,QAAQT,MAC1BA,IAAUF,GACVsF,KAAK4F,SAAShL,MAAMA,GAAOoL,KAAKnH,EAAMmB,KAAKiG,sBAAuBjG,QAG1EiG,sBAAuBxL,EAAEyL,KACzBnF,eAAgB,SAAUoF,GACjBnG,KAAKoG,aAAepG,KAAKoG,YAAY,KAAOD,EAAEE,QAC/CF,EAAEG,kBAGVC,iBAAkB,WACd,GAAI7J,GAASsD,KAAK3E,QAAQqB,MAC1B,OAAOA,IAAqB,SAAXA,GAErB8J,WAAY,WACR,GAAIzL,GAAOiF,IACPjF,GAAK0L,QACL1L,EAAK0L,OAAOtF,SAASnC,IAG7B0H,WAAY,WACJ1G,KAAKyG,QACLzG,KAAKyG,OAAOvF,YAAYlC,IAGhC2H,YAAa,WACT3G,KAAK4G,aACL5G,KAAK6G,UAAU,IACf7G,KAAK4F,SAAShL,UACVoF,KAAKI,YACLJ,KAAK8G,cAAgBpM,GAErBsF,KAAKuG,qBAAuBvG,KAAK3E,QAAQ0L,mBACzC/G,KAAKgH,SACDC,KAAM,GACNC,MAAM,IAENlH,KAAK3E,QAAQ8L,gBACbnH,KAAK4F,SAASzD,MAAM,IAG5BnC,KAAKoH,WAETR,WAAY,WACR5G,KAAKqH,KAAK,KAEdC,aAAc,WACLtH,KAAK3E,QAAQ8I,SACdnE,KAAK4F,SAAS2B,OAAM,GAExBvH,KAAKwH,iBAETA,cAAe,SAAU9K,EAAQ+K,GAAlB,GAYPC,GAmBAC,EA9BA5H,EAAOC,KACP3E,EAAU0E,EAAK1E,QACfuM,EAAyBvM,EAAQwM,cAAgBnL,GAAUA,EAAOoL,OAASpL,EAAOJ,SAAWI,EAAOJ,QAAQtB,OAC5GsF,EAAaP,EAAKO,WAClBlE,EAAawC,KAAW0B,EAAW5D,cACnCqL,EAAoBrL,GAAUN,EAAWE,SAAWF,EAAWE,QAAQtB,SAAW0B,EAClFsL,EAAUzL,EAAsBH,EAAYf,EAAQkF,cAExD,IADAP,KAAKiI,wBAAwB7L,IACxBM,IAAUsL,IAAYjI,EAAKmF,QAAQ,aAAexI,OAAQA,IA8B/D,MA3BIgL,IACApL,WACAwL,MAAO,OAEPF,EACAF,EAAcpL,QAAQL,KAAKS,GAE3BsD,KAAKkI,sBAAsBR,EAAehL,GAE1CP,EAAkBC,KACdsL,EAAcI,QAAU1L,EAAW0L,MACnCJ,EAAcpL,QAAUoL,EAAcpL,QAAQ6L,OAAO/L,EAAWE,SAEhEoL,EAAcpL,QAAQL,KAAKG,IAG/B2D,EAAKqI,YACLpI,KAAK4F,SAASyC,YAAYX,GAE1BC,EAAkB/I,MAClB0J,KAAMP,EAAoB,EAAIzH,EAAWgI,OACzCC,SAAUR,EAAoBzH,EAAWjF,QAAQkN,SAAWjI,EAAWiI,WACvEC,KAAMlI,EAAWkI,OACjB9L,OAAQ4D,EAAW5D,SACnB+L,MAAOnI,EAAWmI,QAClBC,UAAWpI,EAAWoI,cACrBhM,OAAQgL,IACNpH,EAAWmH,EAAQ,OAAS,SAASnH,EAAWqI,YAAYhB,KAEvEO,sBAAuB,SAAUR,EAAehL,GACxCP,EAAkBO,IAA4B,KAAjBA,EAAO9B,OACpC8M,EAAcpL,QAAQL,KAAKS,IAGnCuL,wBAAyB,SAAU7L,GAAV,GAIjBwM,GACKjN,CAJT,IAAKS,EAAWE,QAAhB,CAIA,IAASX,EAAI,EAAGA,EAAIS,EAAWE,QAAQtB,OAAQW,IACvC,cAAgBS,GAAWE,QAAQX,KACnCiN,EAAkBjN,EAGrB+H,OAAMkF,IACPxM,EAAWE,QAAQuM,OAAOD,EAAiB,KAGnDlG,gBAAiB,SAAU7C,EAASiJ,GAC3BjJ,GAGLG,KAAK6D,QAAQiF,EAAQ,WACjB,OAAShF,SAAUjE,MAG3BqC,cAAe,WAAA,GACPnH,GAAOiF,KACP4D,EAAS7I,EAAK6I,MACbA,KAGL5D,KAAK0C,gBAAgBkB,EAAQ,WAC7BA,EAAOmF,SAAS,UAAUC,KAAKjO,EAAK8G,gBAAiBoH,SAAUlO,KAC/DiF,KAAK0C,gBAAgBkB,EAAQ,aAEjCsF,cAAe,SAAUC,GACrB1O,EAAEuF,KAAK4D,QAAQwF,OAAOD,IAE1BE,cAAe,SAAUF,GACrB,GAAIG,GAActJ,KAAK4F,SAAS2D,QAAQC,KAAKtL,EAC7CoL,GAAYF,OAAOD,IAEvBlH,cAAe,WAAA,GACPlH,GAAOiF,KACPgE,EAASjJ,EAAKiJ,MACbA,KAGLhE,KAAK0C,gBAAgBsB,EAAQ,WAC7BA,EAAOgF,KAAKjO,EAAK4G,gBAAiBsH,SAAUlO,KAC5CiF,KAAK0C,gBAAgBsB,EAAQ,aAEjCyF,cAAe,WACX,MAAOzJ,MAAK3E,QAAQwG,gBAAkB7B,KAAKM,WAAWoJ,WAAW1O,QAErEyG,WAAY,WACR,GAAI1B,GAAOC,KAAMpF,EAAQmF,EAAK1E,QAAQT,KACxB,QAAVA,EACAmF,EAAKF,QAAQ8J,IAAI/O,IAEjBA,EAAQmF,EAAK8G,YACb9G,EAAK1E,QAAQT,MAAQA,GAEzBmF,EAAK6J,KAAOhP,GAEhBiP,YAAa,WACT,GAAuDrN,GAAnDuD,EAAOC,KAAM8J,EAAQ/J,EAAKO,WAAWyJ,OAAOD,KAC5CA,IAASA,EAAME,SACfxN,EAAQsN,EAAME,OAAOjK,EAAK1E,QAAQkF,eAC9B/D,GAASA,EAAM3B,MAAuB,WAAf2B,EAAM3B,OAC7BkF,EAAK1E,QAAQ4O,YAAa,KAItCC,OAAQ,SAAUC,GACd,MAAOnK,MAAK4F,SAASzD,MAAMgI,IAE/BnD,QAAS,SAAU3L,GAAV,GAMDe,GAOST,EAZToE,EAAOC,KACPoK,EAAgBrK,EAAK1E,QACrB4L,EAAO5L,EAAQ4L,KACfY,EAAeuC,EAAcvC,aAC7BrL,EAAQ4N,EAAc7J,aAE1B,IAAIsH,GAAgBA,EAAa7M,OAM7B,IALAoB,GACI0L,MAAO,KACPxL,WACA+N,YAAY,GAEP1O,EAAI,EAAGA,EAAIkM,EAAa7M,OAAQW,IACrCqE,KAAKkI,sBAAsB9L,EAAY2D,EAAKuK,iBAAiBrD,EAAMY,EAAalM,SAGpFS,GAAa2D,EAAKuK,iBAAiBrD,EAAMzK,EAE7CuD,GAAKwK,MAAQlP,EAAQ6L,KACrBnH,EAAKyH,cAAcpL,IAEvBkO,iBAAkB,SAAU1P,EAAO4B,GAAjB,GACVuD,GAAOC,KACPoK,EAAgBrK,EAAK1E,QACrB4O,EAAaG,EAAcH,WAC3BO,EAAyBzK,EAAKO,WAAWjF,QAAQmP,sBACrD,QACI5P,MAAOqP,EAAaO,EAAyB5P,EAAM6P,kBAAkBD,GAA0B5P,EAAM8P,cAAgB9P,EACrH4B,MAAOA,EACPmO,SAAUP,EAAc1N,OACxBuN,WAAYA,IAGpBW,aAAc,WAAA,GACN7P,GAAOiF,KACP6K,EAAa9P,EAAKM,QAAQyP,UAAY/P,EAAKM,QAAQyP,SAASC,MAAQhQ,EAAKM,QAAQyP,SAASC,MAAQ,OACjGhQ,GAAK0L,SACN1L,EAAK0L,OAAShM,EAAE,yEAA2EoQ,EAAa,aAAanK,MACjHsK,KAAQ,SACRrK,eAGH5F,EAAKM,QAAQ4P,aACdlQ,EAAK0L,OAAO7D,SAEhB5C,KAAKwG,cAET0E,OAAQ,SAAUjE,GACd,GAAI5L,GAAU2E,KAAK3E,OACnB4L,GAAuB,gBAATA,GAAoBA,EAAOjH,KAAKmL,cAC9CC,aAAapL,KAAKqL,kBACbhQ,EAAQ0L,mBAAqBE,EAAKjM,QAAUiM,EAAKjM,QAAUK,EAAQiQ,aACpEtL,KAAKuL,OAAS,SACVvL,KAAK4F,WACL5F,KAAK4F,SAAS4F,cAAgB/Q,EAAEgR,KAAKxE,GAAMjM,QAE1CgF,KAAKuG,mBAGNvG,KAAKgH,SACDC,KAAMA,EACNC,MAAM,IAJVlH,KAAK0L,cAAczE,KAS/B0E,QAAS,SAAUxB,GACf,MAAOnK,MAAKkK,OAAOC,IAEvByB,MAAO,WACH,MAAO5L,MAAKS,GAAG,GAAGsI,UAEtBpG,QAAS,WAAA,GACD5C,GAAOC,KACPC,EAAKF,EAAKE,EACd9C,GAAO+C,GAAGyC,QAAQxC,KAAKJ,GACvBA,EAAK8L,oBACL9L,EAAK6F,SAASjD,UACd5C,EAAKhF,KAAK+Q,IAAI7L,GACdF,EAAKgM,MAAMpJ,UACP5C,EAAKiM,OACLjM,EAAKiM,MAAMF,IAAI,QAAS/L,EAAKkM,gBAGrCxQ,SAAU,SAAUN,GAChB,GAAI4E,GAAOC,IACX,IAAI7E,IAAUT,EACV,MAAOqF,GAAK6F,SAASsG,oBAAoB,EAE7C,IAAqB,gBAAV/Q,GAAoB,CAC3B,GAAI4E,EAAK1E,QAAQ8I,QACb,MAAOpE,GAAKO,WAAW6L,SAAS1R,EAAEU,GAAOyC,KAAK,OAElDzC,GAAQV,EAAEsF,EAAK6L,SAASzQ,MAAMA,GAElC,MAAO4E,GAAKO,WAAWoJ,WAAWvO,IAEtC2J,cAAe,WACX,GAAI6G,GAAU3L,KAAK4F,SAASzD,OACxBwJ,IACA3L,KAAKoC,SAASgK,IAAIpM,KAAKoG,aAAa1F,KAAK,wBAAyBiL,EAAQjL,KAAK,QAGvFsE,gBAAiB,WACbhF,KAAKoC,SAASgK,IAAIpM,KAAKoG,aAAaiG,WAAW,0BAEnD7K,WAAY,WAAA,GACJzB,GAAOC,KACPH,EAAUE,EAAKF,QACfxE,EAAU0E,EAAK1E,QACfiR,EAASzP,EAAMyP,OACfC,EAAY1M,EAAQa,KAAK7D,EAAM6D,KAAK,eACpC8L,EAAa3M,EAAQa,KAAK7D,EAAM6D,KAAK,iBACpCrF,EAAQkF,eAAiBgM,IAC1BlR,EAAQkF,cAAgBgM,IAEvBlR,EAAQmF,gBAAkBgM,IAC3BnR,EAAQmF,eAAiBgM,GAE7BzM,EAAK0M,MAAQH,EAAOjR,EAAQkF,eAC5BR,EAAK2M,OAASJ,EAAOjR,EAAQmF,iBAEjCmM,MAAO,SAAU7M,GACb,GAAIC,GAAOC,KAAM3E,EAAU0E,EAAK1E,QAASwE,EAAUE,EAAKqC,SAASgK,IAAIrM,EAAKqG,YACtE/K,GAAQuR,UAAYlS,GACpBmF,EAAQa,KAAK,oBAAqBrF,EAAQuR,QAAU,OAAS,QAEjE9M,EAAKA,EAAKA,EAAK,IAAMC,EAAKU,GAAG,GAAGX,GAAKC,EAAKU,GAAG,GAAGX,GAChDD,EAAQa,KAAK,YAAaZ,GAC1BC,EAAKU,GAAGC,KAAK,YAAcX,EAAKwG,mBAA6B,SAAR,OACrDxG,EAAK8M,cAETA,WAAY,WAAA,GAgBAC,GAfJ/M,EAAOC,KACP+M,EAAahN,EAAKqC,SAClB4K,EAAWjN,EAAKF,QAChBoN,EAAUD,EAAStM,KAAK,MACxBwM,EAAWzS,EAAE,cAAgBwS,EAAU,MACvCE,EAAYH,EAAStM,KAAK,cAC1B0M,EAAiBJ,EAAStM,KAAK,kBAC/BqM,KAAeC,IAGfG,EACAJ,EAAWrM,KAAK,aAAcyM,GACvBC,EACPL,EAAWrM,KAAK,kBAAmB0M,GAC5BF,EAASlS,SACZ8R,EAAUI,EAASxM,KAAK,OAASX,EAAKsN,iBAAiBH,EAAUD,GAAWpQ,EAAMmE,QACtF+L,EAAWrM,KAAK,kBAAmBoM,MAG3CO,iBAAkB,SAAUC,EAAOL,GAC/B,GAAIH,GAAUG,EAAU7O,CAExB,OADAkP,GAAM5M,KAAK,KAAMoM,GACVA,GAEXS,MAAO,WACH,GAAIxN,GAAOC,IACXD,GAAKqH,UACLrH,EAAKyN,SAETpG,QAAS,WAAA,GAKDlC,GAJAnF,EAAOC,KACP7E,EAAQ4E,EAAK3E,cACbqS,EAAc1N,EAAK1E,QAAQT,MAC3BA,EAAQmF,EAAKnF,OAEbmF,GAAKK,YAAcL,EAAK6F,SAAS2B,SAAWkG,IAC5C7S,EAAQ6S,GAER7S,IAAUD,EAAUoF,EAAK6J,WAAahP,KAAUA,IAAUD,EAAUoF,EAAK2N,eAAiB9S,IAC1FsK,GAAU,EACHnF,EAAK4N,sBAAwBjT,GAAaqF,EAAK4N,sBAAwBhT,EAAUoF,EAAK6J,WAAa7J,GAAK4N,sBAAwB5N,EAAK6N,eAC5I1I,GAAU,EACH/J,IAAUT,GAAaS,IAAU4E,EAAK8N,WAAc9N,EAAK6F,SAASkI,eACzE5I,GAAU,GAEVA,IAEInF,EAAK4N,oBAAsB5N,EAAK6J,KADlB,OAAd7J,EAAK6J,MAA+B,KAAd7J,EAAK6J,MAAyB,KAAVhP,EACHA,EAEnCmF,EAAKtE,WACkCsE,EAAK1E,QAAQmF,eAAiBT,EAAKtE,WAAWsE,EAAK1E,QAAQmF,gBAAkBT,EAAKtE,WAElF,KAG/CsE,EAAK8N,UAAY1S,EACjB4E,EAAK2N,SAAW3N,EAAKsH,MAAQtH,EAAKsH,OAC7BtH,EAAKgO,SACNhO,EAAKF,QAAQqF,QAAQpH,GAEzBiC,EAAKmF,QAAQpH,IAEjBiC,EAAKiO,QAAS,GAElBC,MAAO,WACH,MAAOjO,MAAKM,WAAW4N,QAE3BC,QAAS,WACL,GAAIpO,GAAOC,KAAM3E,EAAU0E,EAAK1E,QAAS+S,EAAWrO,EAAKF,QAAQQ,GAAG,aAChEhF,GAAQ0G,SAAWrH,IACnBW,EAAQ2G,QAAU3G,EAAQ0G,SAEzB1G,EAAQ2G,SAAWoM,EACpBrO,EAAKgC,QAAO,GAEZhC,EAAKsC,SAAStC,EAAKF,QAAQQ,GAAG,gBAGtCgO,WAAY,SAAU5S,GAClB,GAAIb,GAAQoF,KAAK0M,OAAOjR,EAIxB,OAHIb,KAAUF,IACVE,EAAQoF,KAAKyM,MAAMhR,IAEhBb,GAEX0T,cAAe,WAAA,GACPC,GAAe,EACfC,EAAWxO,KAAK4F,SAAS2D,QAAQkF,QAAQ,WAK7C,OAJAD,GAASE,KAAK,WACV,GAAI7O,GAAUpF,EAAEuF,KAChBuO,IAAgBvR,EAAY6C,GAAS,KAElC0O,GAEXI,QAAS,SAAU3T,GAAV,GAKD4T,GACAC,EACAC,EANA/O,EAAOC,KACPjF,EAAOgF,EAAKhF,KACZqK,EAASrF,EAAK1E,QAAQ+J,OACtB2J,EAAUhP,EAAKgM,MAAMgD,SAIzB,IAAI/T,GAAU+E,EAAK1E,QAAQwG,eAAgB,CAEvC,GADAgN,EAAS9T,EAAKqR,IAAIrR,EAAKiU,OAAO,2BAA2B7F,QACpDpO,EAAKsF,GAAG,YAET,MADAwO,GAAOI,OACP,CAEJ7J,GAASrF,EAAK6F,SAAS2D,QAAQ,GAAG2F,aAAe9J,EAASA,EAAS,OACnEyJ,EAAOzJ,OAAOA,GACC,SAAXA,IACAwJ,EAAY7O,EAAKuO,gBACjBQ,EAAe9R,EAAYvC,EAAEsF,EAAKiE,UAAY,EAC9CoB,EAASA,EAASwJ,EAAYE,GAElC/O,EAAK6F,SAAS2D,QAAQnE,OAAOA,GACxB2J,GACDF,EAAOI,OAGf,MAAO7J,IAEX+J,aAAc,SAAUhJ,GACpBnG,KAAKoP,mBACDpP,KAAKkF,QAAQ7G,GACb8H,EAAEG,kBAEFtG,KAAKoC,SAAS1B,KAAK,iBAAiB,GACpCV,KAAKS,GAAGC,KAAK,eAAe,KAGpC0O,iBAAkB,WACd,GAAwFC,GAAeC,EAAnGvP,EAAOC,KAAMjF,EAAOgF,EAAKhF,KAAMyI,EAAQzI,EAAK,GAAGwU,MAAM/L,MAAOgM,EAAUzP,EAAKyP,OAC/E,IAAKzU,EAAK6C,KAAKqB,KAAUuE,EAmBzB,MAhBA6L,GAAgBvS,OAAO2S,iBAAmB3S,OAAO2S,iBAAiBD,EAAQ,GAAI,MAAQ,EACtFF,EAAgBI,WAAWL,GAAiBA,EAAc7L,QAAU/F,EAAW+R,GAC3EH,GAAiBtQ,EAAQI,OACzBmQ,GAAiBI,WAAWL,EAAcM,aAAeD,WAAWL,EAAcO,cAAgBF,WAAWL,EAAcQ,iBAAmBH,WAAWL,EAAcS,mBAGvKtM,EAD2B,eAA3BzI,EAAKgV,IAAI,cACDT,GAAiB7R,EAAW1C,GAAQA,EAAKyI,SAEzC8L,EAEZvU,EAAKgV,KACDC,WAAYR,EAAQO,IAAI,eACxBvM,MAAOzD,EAAK1E,QAAQ4U,UAAY,OAASzM,EACzC0M,SAAU1M,EACV2M,WAAYpQ,EAAK1E,QAAQ4U,UAAY,SAAW,WACjDrS,KAAKqB,EAAOuE,IACR,GAEX4M,cAAe,SAAUjK,GACjBnG,KAAKkF,QAAQ5G,GACb6H,EAAEG,kBAEFtG,KAAKoC,SAAS1B,KAAK,iBAAiB,GACpCV,KAAKS,GAAGC,KAAK,eAAe,KAGpC2P,WAAY,WAAA,GACJzK,GAAW5F,KAAK4F,SAChB0K,GAAiB1K,EAASzD,QAC1BhH,EAAQL,EAAK8K,EAAS1K,SACtBC,KAAUT,GAAasF,KAAK3E,QAAQ8L,gBAAkBmJ,IACtDnV,EAAQ,GAERA,IAAUT,EACVkL,EAASzD,MAAMhH,GACRmV,GACP1K,EAAS2K,cAAc,IAG/BC,uBAAwB,SAAUpL,GAAV,GAChBqL,GAAKzQ,KAAKS,GAAGsI,SAAS,kBACtBO,EAActJ,KAAK4F,SAAS2D,QAAQC,KAAKtL,GACzCwS,EAAU,EACVC,EAAY,OACZrH,GAAY,IAAuC,SAAjCA,EAAY,GAAGiG,MAAMqB,UACxB,SAAXxL,IACAsL,EAAU7T,EAAMQ,QAAQwT,aAExB7Q,KAAKH,QAAQiR,QAAQ,UAAU9V,SAC/B2V,EAAY,QAEhBD,GAAWhB,WAAWe,EAAGV,IAAI,UAAYY,EAAY,UAAW,IAAMjB,WAAWe,EAAG1H,SAAS,YAAYgH,IAAI,WAAaY,GAAY,IACtIrH,EAAYyG,IAAI,WAAaY,EAAWD,KAGhDK,sBAAuB,SAAUtJ,GAC7B,GAAIrC,GAASpF,KAAK2O,QAAQ3O,KAAKM,WAAWoJ,WAAW1O,QAAUyM,EAC/DzH,MAAKwQ,uBAAuBpL,GAC5BpF,KAAKgR,+BAA+B5L,IAExC4L,+BAAgC,SAAU5L,GAAV,GAEpBrK,GACAkW,EACAJ,CAHJ7Q,MAAK3E,QAAQ4F,SAAWjB,KAAK3E,QAAQ4F,QAAQjG,SACzCD,EAAOiF,KACPiR,EAAQ5T,EAAQ4T,MAAMlW,EAAKyU,SAC3BqB,EAAYhU,EAAMQ,QAAQwT,YAC9B9V,EAAKwI,cAAcwM,IAAIkB,EAAQ,eAAiB,gBAA4B,SAAX7L,EAAoByL,EAAY,KAGzGK,eAAgB,WAAA,GACRtL,GAAW5F,KAAK4F,SAChBuL,EAAgBvL,EAAS/F,QAAQuF,SAAWQ,EAAS2D,QAAQnE,QAC7DpF,MAAK3E,QAAQ4U,WACbrK,EAAS2D,QAAQwG,KACbqB,UAAW,SACXC,UAAWF,EAAgB,SAAW,UAIlDG,aAAc,SAAU7J,GAChBzH,KAAK3E,QAAQ8I,UAGZnE,KAAK+L,MAAMlM,QAAQQ,GAAG,YAQvBL,KAAK+Q,sBAAsBtJ,IAP3BzH,KAAK+L,MAAMwF,IAAI,OAAQ,SAAU9J,GAC7B,MAAO5I,GAAM,WACTmB,KAAK+Q,sBAAsBtJ,IAC5BzH,OACLG,KAAKH,KAAMyH,IACbzH,KAAK+L,MAAMwF,IAAI,WAAY1S,EAAMmB,KAAKkR,eAAgBlR,UAK9DwR,OAAQ,WACJ,GAAIzW,GAAOiF,IACXjF,GAAKgR,MAAQ,GAAIhP,GAAG0U,MAAM1W,EAAKA,KAAM6D,KAAW7D,EAAKM,QAAQ0Q,OACzD2F,OAAQ3W,EAAKyU,QACbtI,KAAMrI,EAAM9D,EAAKoU,aAAcpU,GAC/ByS,MAAO3O,EAAM9D,EAAKqV,cAAerV,GACjC4W,UAAW5W,EAAKM,QAAQsW,UACxBV,MAAO5T,EAAQ4T,MAAMlW,EAAKyU,SAC1BoC,SAAU7W,EAAKM,QAAQ4U,cAG/B4B,kBAAmB,WACXzS,GACAY,KAAKjF,KAAK+W,KAAK,KAAKC,IAAI,cAAcrR,KAAK,eAAgB,OAGnEsR,aAAc,SAAU7L,GACpB1L,EAAE0L,EAAE8L,eAAeC,YAAYlU,EAAkB,eAAXmI,EAAEtL,OAE5CsX,QAAS,SAAUjL,EAAMkL,GAAhB,GACDrS,GAAOC,KACPqS,EAAehV,EAAQiV,WAAajV,EAAQkV,OAASlV,EAAQmV,YAAcnV,EAAQoV,SACvFvL,GAAOA,IAASxM,EAAYwM,GAAQnH,EAAKgM,MAAMgD,UAC1CqD,GAAiBC,GAAgBtS,EAAKqC,SAAS,KAAO7E,MACvDwC,EAAK2S,UAAW,EAChB3S,EAAKqC,SAASD,QACdpC,EAAK2S,UAAW,GAEpB3S,EAAKmH,EAAO7I,EAAOC,MAEvBqU,gBAAiB,WACb,GAAI5S,GAAOC,IACND,GAAK6S,mBAAqB7S,EAAKnF,UAAYD,EAAUoF,EAAK8S,qBAAuB9S,GAAKnF,WACvFmF,EAAK8S,eAAiB9S,EAAKnF,QAC3BmF,EAAK6S,mBAAoB,EACzB7S,EAAKmF,QAAQ3G,GAAWuU,cAAe/S,EAAK6N,mBAGpDmF,eAAgB,WACR/S,KAAK2N,sBAAwB3N,KAAKpF,SAClCoF,KAAKkF,QAAQpH,IAGrB+N,kBAAmB,WACf,GAAI9L,GAAOC,IACXD,GAAKO,WAAW0S,OAAOtU,EAAcqB,EAAKkT,sBAAsBD,OAAOrU,EAAYoB,EAAKmT,oBAAoBF,OAAO,QAASjT,EAAKoT,gBAErIC,mBAAoB,SAAU/X,EAAST,GACnC,GAAIyY,IAAYhY,EAAQT,gBAAiB0Y,OAAQjY,EAAQT,MAAMI,OAASK,EAAQT,SAAWA,YAAiB0Y,OAAQ1Y,EAAMI,OAASJ,EACnI,IAAIyY,GAAYhY,EAAQ8I,SAAkD,kBAAhC9I,GAAQ8I,QAAQoP,YACtD,KAAUC,OAAM,sKAgB5B5U,GAAOc,GACH+T,QAAS,SAAUC,EAAMC,GACrB,GAAI3Q,GAAKhI,EAAQwT,EAAWmF,EAAW5K,QACvC,KAAK2K,GAAQA,EAAKC,aAAeA,EAC7B,QAEJ,KAAK3Q,EAAM,EAAGhI,EAASwT,EAASxT,OAAQgI,EAAMhI,EAAQgI,IAClD,GAAI0Q,IAASlF,EAASxL,GAClB,MAAOA,EAGf,WAEJrI,UAAWA,IAEfkC,EAAME,GAAG2C,KAAOA,EAChB3C,EAAG6W,OAASlU,EAAKd,QACbgB,KAAM,SAAUC,EAASxE,GACrBqE,EAAKQ,GAAGN,KAAKO,KAAKH,KAAMH,EAASxE,GACjC2E,KAAK6T,SAAW7T,KAAKH,QAAQ8J,OAEjCmK,cAAe,SAAUxT,GAAV,GAEP0O,GADAjP,EAAOC,IAEXD,GAAK1E,QAAQiF,WAAaA,EAC1BP,EAAKgU,cACDhU,EAAK6F,SAAS2B,UACdxH,EAAKiU,cAAgB,KACrBjU,EAAK6F,SAASqO,SAAW,MAE7BlU,EAAK6F,SAASkO,cAAc/T,EAAKO,YAC7BP,EAAK1E,QAAQoJ,UACb1E,EAAKO,WAAW4T,QAEpBlF,EAASjP,EAAKoU,gBACVnF,GACAjP,EAAKqU,eAAepF,IAG5BxB,MAAO,WACHxN,KAAK+L,MAAMyB,SAEftS,OAAQ,SAAUiP,GACd,GAAIpK,GAAOC,IACX,OAAImK,KAAczP,EACPqF,EAAK3E,cAEL2E,EAAKsU,QAAQlK,GAAWnE,KAAK,WAChCjG,EAAKuU,cAAgBvU,EAAK6J,KAAO7J,EAAK8G,YACtC9G,EAAK8N,UAAY9N,EAAK3E,iBAIlCyL,UAAW,SAAUjM,EAAOoI,GACxB,MAAOhD,MAAKA,KAAKI,UAAY,kBAAoB,kBAAkBxF,EAAOoI,IAE9EuR,eAAgB,SAAU3Z,GACtB,GAAIiF,GAAUG,KAAKH,QAAQ,EAC3B,OAAIjF,KAAUF,EACHmF,EAAQjF,OAED,OAAVA,IACAA,EAAQ,IAEZiF,EAAQjF,MAAQA,EAHhB,IAMR4Z,gBAAiB,SAAU5Z,EAAOoI,GAAjB,GAETqQ,GADAxT,EAAUG,KAAKH,QAAQ,EAE3B,OAAIjF,KAAUF,EACHO,EAAkB4E,GAASjF,OAAS,IAE/CK,EAAkB4E,GAAStE,UAAW,EAClCyH,IAAQtI,IACRsI,MAEJqQ,EAAqB,OAAVzY,GAA4B,KAAVA,EACzByY,GAAYrQ,MACZhD,KAAKyU,QAAQ7Z,GAETA,EACAiF,EAAQjF,MAAQA,EAEhBiF,EAAQzE,cAAgB4H,EAXhC/H,IAeJyZ,kBAAmB,WACf,OAAO,GAEXD,QAAS,SAAU7Z,GAAV,GACDmF,GAAOC,KACPH,EAAUE,EAAKF,QACf8U,EAAS5U,EAAK+G,aACb6N,KACDA,EAASla,EAAE,aACXsF,EAAK+G,cAAgB6N,EACrB9U,EAAQgB,OAAO8T,IAEnBA,EAAOtN,KAAKzM,GACZ+Z,EAAO,GAAGpZ,UAAW,GAEzBqZ,UAAW,WACP,GAAI7U,GAAOC,IACXoL,cAAarL,EAAK8U,OAClB9U,EAAK+U,WAAW5T,YAAYjD,GAC5B8B,EAAKqC,SAAS1B,KAAK,aAAa,GAChCX,EAAK8U,MAAQ,KACb9U,EAAK2G,cAETqO,UAAW,SAAU5O,GACjB,GAAIpG,GAAOC,IACPmG,GAAE6O,uBAGNjV,EAAKkV,UAAW,EACZlV,EAAK8U,QAGT9U,EAAK8U,MAAQK,WAAW,WAChBnV,EAAK+U,aACL/U,EAAKqC,SAAS1B,KAAK,aAAa,GAChCX,EAAK+U,WAAW3T,SAASlD,GACzB8B,EAAKyG,eAEV,QAEP2O,YAAa,WACTnV,KAAKiV,UAAW,EAChBjV,KAAK4U,aAETb,YAAa,WACT,GAAwG/Q,GAApGjD,EAAOC,KAAMH,EAAUE,EAAKF,QAASxE,EAAU0E,EAAK1E,QAASiF,EAAajF,EAAQiF,cACtFA,GAAa7F,EAAEqE,QAAQwB,IAAgB1C,KAAM0C,GAAeA,EACxDP,EAAKK,YACL4C,EAAMnD,EAAQ,GAAGzE,cACb4H,OACA3H,EAAQF,MAAQ6H,GAEpB1C,EAAWpF,OAAS2E,EACpBS,EAAW0J,SACLxN,MAAOnB,EAAQkF,gBACf/D,MAAOnB,EAAQmF,kBAGrBT,EAAKO,WACLP,EAAK8L,qBAEL9L,EAAKkT,qBAAuBpU,EAAMkB,EAAKgV,UAAWhV,GAClDA,EAAKmT,mBAAqBrU,EAAMkB,EAAKoV,YAAapV,GAClDA,EAAKoT,cAAgBtU,EAAMkB,EAAK6U,UAAW7U,IAE/CA,EAAKO,WAAazD,EAAMe,KAAKwX,WAAWC,OAAO/U,GAAYwF,KAAKpH,EAAcqB,EAAKkT,sBAAsBnN,KAAKnH,EAAYoB,EAAKmT,oBAAoBpN,KAAK,QAAS/F,EAAKoT,gBAE1KmC,WAAY,WACRtV,KAAK4F,SAAS2P,cAElBC,UAAW,WACPxV,KAAK4F,SAAS6P,aAElBC,UAAW,WACP1V,KAAK4F,SAAS+P,aAElBC,UAAW,WACP5V,KAAK4F,SAASiQ,aAElBC,MAAO,SAAU3P,GAAV,GAKC1K,GACAsa,EACApK,EA6DIqK,EACAC,EAGIC,EAyCJvF,EAhHJ5Q,EAAOC,KACP4F,EAAW7F,EAAK6F,SAChBuQ,EAAMhQ,EAAEiQ,QACRC,EAAOF,IAAQ/Y,EAAKkZ,IAIxB,IAAIH,IAAQ/Y,EAAKmZ,IAAMF,EAAM,CACzB,GAAIlQ,EAAEqQ,OACFzW,EAAKqJ,OAAOiN,OACT,CACH,IAAKzQ,EAAS2B,UAAYxH,EAAKU,GAAG,GAAGgW,WAUjC,MATK1W,GAAK2W,SACN3W,EAAKO,WAAWiR,IAAIzT,EAAQ,WACxBiC,EAAK2W,QAAS,EACd3W,EAAK+V,MAAM3P,KAEfpG,EAAK2W,QAAS,EACd3W,EAAKyH,iBAETrB,EAAEG,kBACK,CAiBX,IAfAqF,EAAU5L,EAAKmK,SACVnK,EAAK2W,QAAY/K,IAAWA,EAAQgL,SAAS,sBAC1CN,GACAtW,EAAK2V,YACA3V,EAAKmK,UACNnK,EAAKyV,cAGTzV,EAAK6V,YACA7V,EAAKmK,UACNnK,EAAKuV,eAIjB7Z,EAAWmK,EAASgR,gBAAgBhR,EAASiR,gBAAgB9W,EAAKmK,WAC9DnK,EAAKmF,QAAQ1G,GACT/C,SAAUA,EACVS,KAAM6D,EAAKmK,WAGf,MADAnK,GAAKmK,OAAOyB,GACZ,CAEJ5L,GAAKsU,QAAQtU,EAAKmK,UAAU,GAAMlE,KAAK,WAC9BjG,EAAKgM,MAAMgD,WACZhP,EAAKwN,QAGLxN,EAAK8S,eADmB,OAAxB9S,EAAK8S,eACiB9S,EAAKnF,QAELmF,EAAKtE,WAAasE,EAAKtE,WAAWsE,EAAK1E,QAAQmF,iBAAmBT,EAAKtE,WAAa,OAItH0K,EAAEG,iBACFyP,GAAU,MACP,IAAII,IAAQ/Y,EAAK0Z,OAASX,IAAQ/Y,EAAK2Z,IAAK,CAW/C,GAVIhX,EAAKgM,MAAMgD,WACX5I,EAAEG,iBAENqF,EAAU5L,EAAKmK,SACfzO,EAAWsE,EAAKtE,WACXsE,EAAKgM,MAAMgD,WAAetT,GAAYsE,EAAKsH,SAAWtH,EAAK0M,MAAMhR,KAClEkQ,EAAU,MAEVqK,EAAejW,EAAKqG,aAAerG,EAAKqG,YAAY,KAAO7I,IAE3DoO,EAAS,CAMT,GALAlQ,EAAWmK,EAASgR,gBAAgBhR,EAASiR,gBAAgBlL,IACzDuK,GAAgB,EAChBza,IACAya,EAAgBnW,EAAK2M,OAAOjR,KAAciE,EAAK/E,UAAUoF,EAAKnF,cAAgBmF,GAAK2M,OAAOjR,KAE1Fya,GAAiBnW,EAAKmF,QAAQ1G,GAC1B/C,SAAUA,EACVS,KAAMyP,IAEV,MAEJsK,GAAYlW,EAAKsU,QAAQ1I,OAClB5L,GAAKiX,SACRjX,EAAK2U,qBAAuB3U,EAAKK,YACjCL,EAAK8G,UAAU9G,EAAKiX,MAAMrN,OAE9B5J,EAAK6F,SAAShL,MAAMmF,EAAKiX,MAAMrN,OAE/B5J,GAAKkX,eACLlX,EAAKkX,cAAclX,EAAKyP,SAExBwG,GAAgBG,IAAQ/Y,EAAK2Z,IAC7BhX,EAAKyP,QAAQ0H,WAETjB,GAAuC,kBAAnBA,GAAUjQ,KAC9BiQ,EAAUjQ,KAAK,WACXjG,EAAKwN,UAGTxN,EAAKwN,QAGbxN,EAAKyN,QACLuI,GAAU,MACHI,KAAQ/Y,EAAK+Z,KAChBpX,EAAKgM,MAAMgD,WACX5I,EAAEG,iBAENvG,EAAKyN,QACLuI,GAAU,IACHhW,EAAKgM,MAAMgD,WAAcoH,IAAQ/Y,EAAKga,UAAYjB,IAAQ/Y,EAAKia,SACtElR,EAAEG,iBACEqK,EAAYwF,IAAQ/Y,EAAKga,SAAW,KACxCxR,EAAS0R,WAAW3G,EAAY/K,EAAS2R,gBACzCxB,GAAU,EAEd,OAAOA,IAEXyB,WAAY,WAAA,GACJzX,GAAOC,KACPyX,IAAa1X,EAAKO,WAAW4N,OAAOlT,MACpC+E,GAAKkV,UAAYlV,EAAK1E,QAAQqc,aAG7B3X,EAAK6F,SAAS2B,SAAYxH,EAAK2W,QAAWe,IAC3C1X,EAAK2W,QAAS,EACd3W,EAAKO,WAAW4T,QAAQlO,KAAK,WACzBjG,EAAK2W,QAAS,MAI1BiB,SAAU,SAAU/Z,EAAMga,EAAahd,GACnC,GAAuGid,GAAQpc,EAAUqc,EAAUC,EAA/HhY,EAAOC,KAAMH,EAAUE,EAAKF,QAASmY,EAAcnY,EAAQ,GAAI7E,EAAS4C,EAAK5C,OAAQK,EAAU,GAA2C2H,EAAM,CAIpJ,KAHI4U,IACAvc,EAAUuc,GAEP5U,EAAMhI,EAAQgI,IACjB6U,EAAS,UACTpc,EAAWmC,EAAKoF,GAChB8U,EAAW/X,EAAK0M,MAAMhR,GACtBsc,EAAYhY,EAAK2M,OAAOjR,GACpBsc,IAAcrd,IACdqd,GAAa,GACTA,EAAUE,QAAQ,YAClBF,EAAYA,EAAUG,QAAQ5Y,EAAY,WAE9CuY,GAAU,WAAaE,EAAY,KAEvCF,GAAU,IACNC,IAAapd,IACbmd,GAAUva,EAAWwa,IAEzBD,GAAU,YACVxc,GAAWwc,CAEfhY,GAAQmJ,KAAK3N,GACTT,IAAUF,IACVsd,EAAYpd,MAAQA,EAChBod,EAAYpd,QAAUA,IACtBod,EAAY5c,mBAGhB4c,EAAY5c,qBACZyc,EAAS5c,EAAkB+c,GACvBH,GACAA,EAAOM,aAAa1Z,EAAUA,KAI1C2Z,OAAQ,WACJ,GAAIrY,GAAOC,KAAMH,EAAUE,EAAKF,QAASwY,EAASxY,EAAQa,KAAK,QAAS4X,EAAOD,EAAS5d,EAAE,IAAM4d,GAAUxY,EAAQ0Y,QAAQ,OACtHD,GAAK,KACLvY,EAAKkM,cAAgB,WACjBiJ,WAAW,WACPnV,EAAKnF,MAAMmF,EAAK8T,aAGxB9T,EAAKiM,MAAQsM,EAAKxX,GAAG,QAASf,EAAKkM,iBAG3CkI,cAAe,WAAA,GAKPqE,GACAxJ,EALAyJ,EAAOzY,KAAK3E,QAAQod,IACxB,IAAKzY,KAAK3E,QAAQqc,YAQlB,MALIc,GAAgB/d,EAAE,IAAMuF,KAAK3E,QAAQqc,aACrC1I,EAASwJ,EAAc5a,KAAK,QAAU6a,GACrCzJ,IACDA,EAASwJ,EAAc5a,KAAK,QAAU2B,EAAiBkZ,KAEpDzJ,GAEX0J,SAAU,WAAA,GAIF1J,GAHAjP,EAAOC,KACP3E,EAAU0E,EAAK1E,QACfsd,EAAUtd,EAAQqc,WAEtB,IAAIiB,EAAS,CAET,GADA3J,EAASjP,EAAKoU,iBACTnF,EACD,MAEJjP,GAAK6Y,qBAAuB/Z,EAAMkB,EAAK8Y,gBAAiB9Y,GACxDA,EAAK+Y,0BACLzd,EAAQoJ,UAAW,EACnBuK,EAAOlJ,KAAK,MAAO,WACf/F,EAAKwR,IAAI,MAAO,SAAUpL,GACtBpG,EAAKgZ,eAAiB5S,EAAEvL,OAASmF,EAAK8G,gBAG9CmI,EAAOgK,MAAMza,EAASwB,EAAK6Y,sBACvB5J,EAAOpJ,SAAS2B,SAChBxH,EAAKkZ,wBACLlZ,EAAKqU,eAAepF,KAEpBA,EAAOuC,IAAI,YAAa,WACpBxR,EAAKkZ,wBACDjK,EAAOjD,MAAMgD,WACbC,EAAO5M,SAASD,UAGnB6M,EAAOpU,SACRmF,EAAKgC,QAAO,MAK5BkX,sBAAuB,WAAA,GACflZ,GAAOC,KACPgP,EAASjP,EAAKoU,gBACd+C,EAAWhY,EAAO,OAAS,UAC/B8P,GAAO5M,SAASgK,IAAI4C,EAAO5I,aAAaN,KAAK,QAAS,WAClDkJ,EAAOgE,OAAOzU,EAASwB,EAAK6Y,sBAC5B5J,EAAOgK,MAAMlb,EAAQiC,EAAK6Y,wBAE9B5J,EAAO5M,SAASgK,IAAI4C,EAAO5I,aAAaN,KAAKoR,EAAU,WACnDlI,EAAOgE,OAAOlV,EAAQiC,EAAK6Y,sBAC3B5J,EAAOgK,MAAMza,EAASwB,EAAK6Y,yBAGnCC,gBAAiB,SAAU1S,GAAV,GACT6I,GAAShP,KAAKmU,gBACd+E,EAAqBlZ,KAAKpF,OAC9BoF,MAAK4N,eAAiBzH,EAAE2M,cACpB9S,KAAK4F,SAAS2B,SACdvH,KAAKmZ,gBAAgBnK,GAAQ,GAEjChP,KAAKoU,eAAepF,EAAQkK,IAEhCE,eAAgB,SAAUpK,GAAV,GACRjP,GAAOC,KACPpF,EAAQmF,EAAK8G,aAAe9G,EAAKgZ,cAChChZ,GAAK+Y,uBAAuB9d,SAC7B+E,EAAKgZ,eAAiB,MAEtBhZ,EAAK6N,eACL7N,EAAKoZ,gBAAgBnK,GAAQ,GACtBpU,GACHA,IAAUD,EAAUoF,EAAK6F,SAAShL,QAAQ,SAAWA,KACrDmF,EAAKnF,MAAMA,GAEVmF,EAAKO,WAAW4N,OAAO,IAAMnO,EAAK3E,oBACnC2E,EAAKoZ,gBAAgBnK,GAAQ,IAE1BjP,EAAKO,WAAWoJ,WAAW1O,QAClC+E,EAAK7E,OAAO6E,EAAK1E,QAAQF,OAE7B4E,EAAKgC,SACLhC,EAAK4S,kBACL5S,EAAKgT,iBACLhT,EAAK6N,gBAAiB,GAE1BwG,eAAgB,SAAUpF,EAAQkK,GAAlB,GAKRG,GAKIC,EATJvZ,EAAOC,KACPvE,EAAWuT,EAAOvT,WAClB8d,EAAc9d,EAAWA,EAASsE,EAAK1E,QAAQme,yBAA2BxK,EAAOtC,OAAOjR,GAAY,KACpG+Q,EAAazM,EAAK1E,QAAQoe,kBAAoBzK,EAAO3T,QAAQmF,cAEjET,GAAK4N,oBAAsBuL,IAAuBxe,EAAYwe,EAAqBnZ,EAAKnF,QACpF2e,GAA+B,IAAhBA,GACfF,EAActZ,EAAKO,WAAW5D,aAC9BH,EAAsB8c,EAAa7M,GAC/B8M,EAAU,WACV,GAAII,GAAiB3Z,EAAK+Y,uBAAuBa,OAC7CD,IACA3Z,EAAKiT,OAAO,YAAa0G,GAE7BA,EAAiB3Z,EAAK+Y,uBAAuB,GACzCY,GACA3Z,EAAKiZ,MAAM,YAAaU,GAE5B3Z,EAAKqZ,eAAepK,IAExBjP,EAAK+Y,uBAAuB7c,KAAKqd,GACU,IAAvCvZ,EAAK+Y,uBAAuB9d,QAC5B+E,EAAKiZ,MAAM,YAAaM,GAE5BvZ,EAAKqI,YAAa,EAClBrI,EAAKyH,eACDhL,MAAOgQ,EACP7B,SAAU,KACV/P,MAAO2e,IAEXxZ,EAAKqI,YAAa,IAElBrI,EAAKgC,QAAO,GACZhC,EAAKoZ,gBAAgBnK,GACrBjP,EAAK4S,kBACL5S,EAAKgT,iBACLhT,EAAK6N,gBAAiB,MAI9BjR,EAAiB,cACjBC,EAAaC,EAAME,GAAG4C,gBAAgBf,QACtCgB,KAAM,SAAUC,EAASxE,GACrB8B,EAAO+C,GAAGN,KAAKO,KAAKH,KAAMH,EAASxE,GACnC2E,KAAKH,QAAQa,KAAK,OAAQ,WAAWI,GAAG,QAAUnE,EAAgB,KAAMkC,EAAMmB,KAAK4E,OAAQ5E,OAAOc,GAAG,aAAenE,EAAgB,KAAM,WACtIlC,EAAEuF,MAAMmB,SAASnD,KAClB8C,GAAG,aAAenE,EAAgB,KAAM,WACvClC,EAAEuF,MAAMkB,YAAYlD,KAEpBX,EAAQkV,OACRvS,KAAK4Z,iBAEuB,aAA5B5Z,KAAK3E,QAAQqJ,YACb1E,KAAKH,QAAQa,KAAK,wBAAwB,GAE9CV,KAAKuJ,QAAUvJ,KAAKH,QAAQga,KAAK,yDAA6D7K,SAC9FhP,KAAKwC,OAASxC,KAAKuJ,QAAQuQ,OAAO,2DAA2DtQ,OAC7FxJ,KAAKuH,OAAM,GACXvH,KAAK+Z,UAAYld,EAAMmE,OACvBhB,KAAKga,oBACLha,KAAKia,SACLja,KAAKka,cACLla,KAAKma,UACL,IAAIvf,GAAQoF,KAAK3E,QAAQT,KACrBA,KACAoF,KAAKma,QAAU1f,EAAEqE,QAAQlE,GAASA,EAAMwf,MAAM,IAAMxf,IAExDoF,KAAKqa,UACLra,KAAKsa,aACLta,KAAK8T,cAAc9T,KAAK3E,QAAQiF,YAChCN,KAAKua,UAAY1b,EAAM,WACnB,GAAIkB,GAAOC,IACXoL,cAAarL,EAAKya,WAClBza,EAAKya,UAAYtF,WAAW,WACxBnV,EAAK0a,iBACN,KACJza,OAEP3E,SACIod,KAAM,aACNjY,eAAgB,KAChBkB,gBAAgB,EAChBgD,YAAY,EACZjC,SAAU,KACV4C,cAAe,KACfC,mBAAoB,MAExBoV,QACI,QACA5c,EACA,WACA,aACA,cACA,YACA,sBAEJgW,cAAe,SAAU6G,GAAV,GAGP/f,GAFAmF,EAAOC,KACPM,EAAaqa,KAEjBra,GAAa7F,EAAEqE,QAAQwB,IAAgB1C,KAAM0C,GAAeA,EAC5DA,EAAazD,EAAMe,KAAKwX,WAAWC,OAAO/U,GACtCP,EAAKO,YACLP,EAAKO,WAAW0S,OAAOlV,EAAQiC,EAAK6a,iBACpChgB,EAAQmF,EAAKnF,QACbmF,EAAKnF,UACLmF,EAAKwH,OAAM,GACXxH,EAAKnF,MAAMA,IAEXmF,EAAK6a,gBAAkB/b,EAAMkB,EAAK8a,QAAS9a,GAE/CA,EAAKsI,YAAY/H,EAAW5D,UAC5BqD,EAAKO,WAAaA,EAAWwF,KAAKhI,EAAQiC,EAAK6a,iBAC/C7a,EAAK+a,gBAETlB,eAAgB,WAAA,GAERmB,GACAC,EAFAjb,EAAOC,KAGPib,EAAc,SAAUC,GACxB,OAAQA,EAAMC,eAAiBD,GAAOE,eAAe,GAAGC,MAE5Dtb,GAAKF,QAAQiB,GAAG,aAAenE,EAAgB,SAAUwJ,GACrD4U,EAASE,EAAY9U,KAEzBpG,EAAKF,QAAQiB,GAAG,WAAanE,EAAgB,SAAUwJ,GAC/CA,EAAE6O,uBAGNgG,EAAOC,EAAY9U,GACfmV,KAAKC,IAAIP,EAAOD,GAAU,KAC1Bhb,EAAKyb,iBAAkB,EACvBzb,EAAK0b,cAAchhB,EAAE0L,EAAEE,QAAQkS,QAAQpa,GAAcud,IAAI,SAIrEC,KAAM,WACF,MAAO3b,MAAKM,WAAWqb,QAE3B7Z,WAAY,SAAUzG,GAClB8B,EAAO+C,GAAG4B,WAAW3B,KAAKH,KAAM3E,GAChC2E,KAAKqa,UACLra,KAAKsa,aACLta,KAAK4b,WAETjZ,QAAS,WACL3C,KAAKH,QAAQiM,IAAInP,GACbqD,KAAK4a,iBACL5a,KAAKM,WAAW0S,OAAOlV,EAAQkC,KAAK4a,iBAExCxP,aAAapL,KAAKwa,WAClBrd,EAAO+C,GAAGyC,QAAQxC,KAAKH,OAE3B4W,gBAAiB,SAAUzb,GACvB,MAAO6E,MAAKM,WAAWoJ,WAAWvO,IAEtCoc,aAAc,WACV,MAAOvX,MAAKuJ,QAAQ,GAAGsS,cAE3BtL,cAAe,SAAUpV,GACrB,GAAIe,GAAO8D,KAAKH,QAAQ,GAAGkJ,SAAS5N,EAChCe,IACA8D,KAAK8b,OAAO5f,IAGpBob,WAAY,SAAU1c,GAClBoF,KAAKuJ,QAAQwS,UAAU/b,KAAKuJ,QAAQwS,YAAcnhB,IAEtDkhB,OAAQ,SAAU5f,GACd,GAAKA,EAAL,CAGIA,EAAK,KACLA,EAAOA,EAAK,GAEhB,IAAIqN,GAAUvJ,KAAKuJ,QAAQ,GAAIyS,EAAgB9f,EAAK0S,UAAWqN,EAAmB/f,EAAKqS,aAAc2N,EAAmB3S,EAAQwS,UAAWI,EAAsB5S,EAAQsS,aAAcO,EAAiBJ,EAAgBC,CACpNC,GAAmBF,EACnBE,EAAmBF,EACZI,EAAiBF,EAAmBC,IAC3CD,EAAmBE,EAAiBD,GAExC5S,EAAQwS,UAAYG,IAExBhQ,kBAAmB,SAAUmQ,GACzB,MAAIA,KAAc3hB,EACPsF,KAAKka,WAAWE,SAE3Bpa,KAAKka,WAAamC,EAClBrc,KAAKma,QAAUna,KAAKsc,WAAWD,GAD/Brc,IAGJsc,WAAY,SAAUD,GAClB,GAAI/P,GAAStM,KAAKuc,YAClB,OAAO9hB,GAAE+hB,IAAIH,EAAW,SAAU5gB,GAC9B,MAAO6Q,GAAO7Q,MAGtBka,UAAW,WACP,GAAIhK,GAAU3L,KAAKmC,OAIfwJ,GAHCA,EAGSA,EAAQ8Q,OAFR,EAIdzc,KAAKmC,MAAMwJ,IAEfkK,UAAW,WACP,GAAIlK,GAAU3L,KAAKmC,OAIfwJ,GAHCA,EAGSA,EAAQnC,OAFRxJ,KAAKH,QAAQ,GAAGkJ,SAAS/N,OAAS,EAIhDgF,KAAKmC,MAAMwJ,IAEf4J,WAAY,WACRvV,KAAKmC,MAAMnC,KAAKH,QAAQ,GAAGkJ,SAAS,KAExC0M,UAAW,WACPzV,KAAKmC,MAAMrH,EAAKkF,KAAKH,QAAQ,GAAGkJ,YAEpC5G,MAAO,SAAUgI,GAAV,GAGCuS,GAFA3c,EAAOC,KACPF,EAAKC,EAAKga,SAEd,OAAI5P,KAAczP,EACPqF,EAAKkU,UAEhB9J,EAAYrP,EAAKiF,EAAK4c,KAAKxS,IAC3BA,EAAY1P,EAAEuF,KAAKH,QAAQ,GAAGkJ,SAASoB,IACnCpK,EAAKkU,WACLlU,EAAKkU,SAAS/S,YAAYnD,GAASsO,WAAWxO,GAC9CkC,EAAKmF,QAAQ,eAEjBwX,IAAiBvS,EAAU,GACvBuS,IACAvS,EAAUhJ,SAASpD,GACnBgC,EAAK+b,OAAO3R,GACZA,EAAUzJ,KAAK,KAAMZ,IAEzBC,EAAKkU,SAAWyI,EAAevS,EAAY,KAC3CpK,EAAKmF,QAAQ,YAbbiF,IAeJyS,WAAY,WACR,MAAO5c,MAAKmC,QAAUnC,KAAKmC,QAAQhH,QAAUT,GAEjDmiB,WAAY,SAAUA,GAClB7c,KAAK8c,YAAcD,GAEvB3hB,OAAQ,SAAU6hB,GAAV,GAQAC,GAQAC,EACAC,EAhBAnd,EAAOC,KACP0E,EAAa3E,EAAK1E,QAAQqJ,WAC1ByY,EAAiC,aAAfzY,GAA6BA,KAAe,EAC9D0Y,EAAkBrd,EAAKia,iBACvBqD,GAAqBrd,KAAKH,QAAQiS,KAAK,qBAAqB3W,SAC5DmiB,KACAtV,IAEJ,OAAI+U,KAAYriB,EACL0iB,EAAgBhD,SAE3B2C,EAAUhd,EAAK4c,KAAKI,GACG,IAAnBA,EAAQ/hB,QAAgB+hB,EAAQ,UAChCA,MAEAE,EAAWxiB,EAAE8iB,WAAWC,UACxBN,EAAWnd,EAAK+N,aAChBoP,IAAaC,GAAmBpd,EAAK0d,kBAAkBV,GAChDE,EAEPE,IAAoBD,GAAYziB,EAAEgZ,QAAQ3Y,EAAKiiB,GAAUK,SAA2B3iB,EAAEgZ,QAAQ3Y,EAAKiiB,GAAUM,SACzGtd,EAAKma,WAAWlf,QAAU+E,EAAKka,MAAMjf,SACrC+E,EAAKma,YAAcna,EAAKka,MAAMmD,EAAgB,IAAIlhB,OAE/C+gB,IAEXD,EAASjd,EAAK2d,UAAUX,GACxB/U,EAAUgV,EAAOhV,QACjB+U,EAAUC,EAAOD,QACbA,EAAQ/hB,SACJmiB,IACAJ,GAAWjiB,EAAKiiB,KAEpBO,EAAQvd,EAAKsU,QAAQ0I,KAErBO,EAAMtiB,QAAUgN,EAAQhN,UACxB+E,EAAK4d,eAAiB,KACtB5d,EAAKmF,QAAQpH,GACTwf,MAAOA,EACPtV,QAASA,KAGViV,KAEXW,SAAU,SAAUC,GAIhB,MAHA7d,MAAKga,iBAAiBnR,OAAOgV,EAAU,GACvC7d,KAAKma,QAAQtR,OAAOgV,EAAU,GAC9B7d,KAAK2d,eAAiB,MAElBE,SAAUA,EACVpiB,SAAUuE,KAAKka,WAAWrR,OAAOgV,EAAU,GAAG,KAGtDC,SAAU,SAAUljB,GAChBA,EAAQH,EAAEqE,QAAQlE,IAAUA,YAAiB+C,GAAkB/C,EAAMwf,MAAM,IAAMxf,GACjFoF,KAAKma,QAAUvf,EACfoF,KAAK2d,eAAiB,MAE1B/iB,MAAO,SAAUA,GAAV,GAGCmiB,GAFAhd,EAAOC,KACPid,EAAWld,EAAKge,cAEpB,OAAInjB,KAAUF,EACHqF,EAAKoa,QAAQC,SAExBra,EAAK+d,SAASljB,GACTqiB,GAAiC,aAArBA,EAASe,UACtBje,EAAKge,eAAiBd,EAAWxiB,EAAE8iB,YAEnCxd,EAAKwH,UACLwV,EAAUhd,EAAKke,cAAcle,EAAKoa,SACF,aAA5Bpa,EAAK1E,QAAQqJ,YACb3E,EAAK7E,WAET6E,EAAK7E,OAAO6hB,GACZE,EAASO,WAEbzd,EAAK+c,aAAc,EACZG,IAEXrR,MAAO,WACH,MAAO5L,MAAKH,QAAQkJ,SAAS5K,IAEjCyG,OAAQ,SAAUuB,GACd,MAAInG,MAAKwb,iBACLxb,KAAKwb,iBAAkB,EACvB,IAECrV,EAAE6O,sBACHhV,KAAKyb,cAActV,EAAE8L,eADzB,IAIJwJ,cAAe,SAAUvf,GAChB8D,KAAKkF,QAAQ,SAAWhJ,KAAMzB,EAAEyB,MACjC8D,KAAK9E,OAAOgB,IAGpBgiB,WAAY,SAAUrjB,EAAMsjB,GAAhB,GAGJC,GACAC,EAHAte,EAAOC,KACPgD,EAAM,EAGNsb,IACJ,KAAKve,EAAK4d,gBAAkB5d,EAAKwe,aAAe1jB,EAAM,CAElD,IADAkF,EAAKwe,WAAa1jB,EACXmI,EAAMmb,EAAOnjB,OAAQgI,IACxBsb,EAAWriB,KAAKtB,EAAUwjB,EAAOnb,GAAMnI,GAE3CujB,GAAO,2BAA6BE,EAAWtjB,OAAS,wEACxDqjB,EAAeG,SAAS,UAAW,SAAUJ,GAC7Cre,EAAK4d,eAAiB,SAAUhS,GAC5B,MAAO0S,GAAS1S,EAAS2S,IAGjC,MAAOve,GAAK4d,gBAEhBc,kBAAmB,SAAUhjB,EAAU0iB,GAApB,GACXvjB,GAAQoF,KAAKuc,aAAa9gB,GAC1BijB,EAAY1e,KAAKke,iBAAkBtjB,GAAOujB,EAC9C,OAAOO,GAAU9jB,IAErByf,QAAS,WACLra,KAAKuc,aAAe1f,EAAMyP,OAAOtM,KAAK3E,QAAQmF,iBAElDkd,UAAW,SAAUX,GAAV,GASHnhB,GACAT,EAAOC,EATP2E,EAAOC,KACP+I,EAAWhJ,EAAKF,QAAQ,GAAGkJ,SAC3BrE,EAAa3E,EAAK1E,QAAQqJ,WAC1B0Y,EAAkBrd,EAAKia,iBACvBqC,EAAYtc,EAAKma,WACjBiE,EAASpe,EAAKoa,QACdnS,KACArM,EAAI,EAGJgjB,EAAiB,CAErB,IADA5B,EAAUA,EAAQ3C,QACd1V,KAAe,GAASqY,EAAQ/hB,QAW7B,GAAmB,aAAf0J,EACP,KAAO/I,EAAIohB,EAAQ/hB,OAAQW,IAEvB,GADAR,EAAQ4hB,EAAQphB,GACXlB,EAAEsO,EAAS5N,IAAQwb,SAAS,oBAGjC,IAAK/a,EAAI,EAAGA,EAAIwhB,EAAgBpiB,OAAQY,IAEpC,GADAR,EAAgBgiB,EAAgBxhB,GAC5BR,IAAkBD,EAAO,CACzBV,EAAEsO,EAAS3N,IAAgB8F,YAAY,oBAAoBR,KAAK,iBAAiB,GACjFsH,EAAQ/L,MACJ4hB,SAAUjiB,EAAI+iB,EACdljB,SAAU4gB,EAAUxT,OAAOjN,EAAG,GAAG,KAErCwhB,EAAgBvU,OAAOjN,EAAG,GAC1BmhB,EAAQlU,OAAOlN,EAAG,GAClBwiB,EAAOtV,OAAOjN,EAAG,GACjB+iB,GAAkB,EAClBhjB,GAAK,EACLC,GAAK,CACL,YA/B4B,CACxC,KAAOD,EAAIyhB,EAAgBpiB,OAAQW,IAC/BlB,EAAEsO,EAASqU,EAAgBzhB,KAAKuF,YAAY,oBAAoBR,KAAK,iBAAiB,GACtFsH,EAAQ/L,MACJ4hB,SAAUliB,EACVF,SAAU4gB,EAAU1gB,IAG5BoE,GAAKoa,WACLpa,EAAKma,cACLna,EAAKia,oBA0BT,OACI+C,QAASA,EACT/U,QAASA,IAGjByV,kBAAmB,SAAUV,GAKzB,IALe,GAEXthB,GAAUN,EAAO0iB,EADjB9U,EAAW/I,KAAKH,QAAQ,GAAGkJ,SAE3Bf,KACAhF,EAAM,EACHA,EAAM+Z,EAAQ/hB,OAAQgI,IACzB7H,EAAQ4hB,EAAQ/Z,GAChBvH,EAAWuE,KAAKia,MAAM9e,GAAOe,KAC7B2hB,EAAW7d,KAAKye,kBAAkBhjB,EAAUuE,KAAKma,SAC7C0D,OACA7V,EAAQ/L,KAAK+D,KAAK4d,SAASC,IAC3BpjB,EAAEsO,EAAS5N,IAAQ+F,YAAY,oBAGvC,SAAI8G,EAAQhN,SACRgF,KAAKkF,QAAQpH,GACTwf,SACAtV,QAASA,KAEN,IAIfqM,QAAS,SAAU0I,GAAV,GAIDthB,GAAUN,EAHV4E,EAAOC,KACP+I,EAAWhJ,EAAKF,QAAQ,GAAGkJ,SAC3BnL,EAAOmC,EAAKka,MAEZqD,KACAta,EAAM,CAIV,KAHIlI,EAAKiiB,SACLhd,EAAKoC,MAAM4a,GAER/Z,EAAM+Z,EAAQ/hB,OAAQgI,IACzB7H,EAAQ4hB,EAAQ/Z,GAChBvH,EAAWmC,EAAKzC,GACZA,QAAiBM,IAGrBA,EAAWA,EAASS,KACpB6D,EAAKia,iBAAiB/d,KAAKd,GAC3B4E,EAAKma,WAAWje,KAAKR,GACrBsE,EAAKoa,QAAQle,KAAK8D,EAAKwc,aAAa9gB,IACpChB,EAAEsO,EAAS5N,IAAQgG,SAAS,oBAAoBT,KAAK,iBAAiB,GACtE4c,EAAMrhB,MAAOR,SAAUA,IAE3B,OAAO6hB,IAEXzG,gBAAiB,SAAUhX,GACvB,MAAOpF,GAAEoF,GAASjC,KAAK,iBAE3B+e,KAAM,SAAUxS,GAOZ,MANyB,gBAAdA,GACPA,GAAaA,GACLrL,EAAQqL,KAChBA,EAAYnK,KAAK6W,gBAAgB1M,GACjCA,GAAaA,IAAczP,EAAYyP,OAEpCA,GAEXyU,UAAW,WAAA,GACH7e,GAAOC,KACP3E,EAAU0E,EAAK1E,QACfoH,EAAWpH,EAAQoH,QASvB,OARKA,IAGDA,EAAW5F,EAAM4F,SAASA,GAC1BA,EAAW,SAAU7E,GACjB,MAAO,oEAAsE6E,EAAS7E,GAAQ,UAJlG6E,EAAW5F,EAAM4F,SAAS,sEAAwE5F,EAAM0I,KAAKlK,EAAQkF,cAAe,QAAU,UAAYse,cAAc,IAOrKpc,GAEX6X,WAAY,WAAA,GACJ7X,GAQS9G,EACDsH,EACA6b,EAIH3I,EAbL9a,EAAU2E,KAAK3E,QACf0jB,GACAtc,SAAUpH,EAAQoH,SAClB4C,cAAehK,EAAQgK,cACvBC,mBAAoBjK,EAAQiK,mBAEhC,IAAIjK,EAAQ4F,QACR,IAAStF,EAAI,EAAGA,EAAIN,EAAQ4F,QAAQjG,OAAQW,IACpCsH,EAAgB5H,EAAQ4F,QAAQtF,GAChCmjB,EAAe7b,EAAczG,MAAQyG,GAAAA,EAAczG,MAAmB,OAC1EuiB,EAAU,SAAWpjB,GAAKsH,EAAcR,UAAY,MAAQqc,EAAe,GAGnF,KAAS3I,IAAO4I,GACZtc,EAAWsc,EAAU5I,GACjB1T,GAAgC,kBAAbA,KACnBsc,EAAU5I,GAAOtZ,EAAM4F,SAASA,GAGxCzC,MAAK+e,UAAYA,GAErBC,kBAAmB,SAAUjC,GAGzB,IAHe,GACXkC,MACAjc,EAAM,EACHA,EAAM+Z,EAAQ/hB,OAAQgI,IACrB+Z,EAAQ/Z,KAAStI,GACjBukB,EAAWhjB,KAAK8gB,EAAQ/Z,GAGhC,OAAOic,IAEXhB,cAAe,SAAUE,EAAQpB,GAAlB,GAGP5hB,GAFAyC,EAAOoC,KAAKia,MACZjX,EAAM,CAGV,IADA+Z,EAAUA,EAAUA,EAAQ3C,YACvB+D,EAAOnjB,OACR,QAEJ,MAAOgI,EAAMpF,EAAK5C,OAAQgI,IACtB7H,EAAQ6E,KAAKye,kBAAkB7gB,EAAKoF,GAAK9G,KAAMiiB,GAC3ChjB,SACA4hB,EAAQ5hB,GAAS6H,EAGzB,OAAOhD,MAAKgf,kBAAkBjC,IAElCmC,kBAAmB,WAQf,IARe,GACXrf,GAAUG,KAAKH,QAAQ,GACvB0J,EAAUvJ,KAAKuJ,QAAQ,GACvBwS,EAAYxS,EAAQwS,UACpBoD,EAAa1kB,EAAEoF,EAAQkJ,SAAS,IAAI3D,SACpCga,EAAY9D,KAAK+D,MAAMtD,EAAYoD,IAAe,EAClDjjB,EAAO2D,EAAQkJ,SAASqW,IAAcvf,EAAQyf,UAC9CC,EAAUrjB,EAAK0S,UAAYmN,EACxB7f,GACH,GAAIqjB,EAAS,CACT,GAAIrjB,EAAK0S,UAAYuQ,EAAapD,IAAc7f,EAAKsjB,YACjD,KAEJtjB,GAAOA,EAAKsjB,gBACT,CACH,GAAItjB,EAAK0S,WAAamN,IAAc7f,EAAKujB,gBACrC,KAEJvjB,GAAOA,EAAKujB,gBAGpB,MAAOzf,MAAKia,MAAMxf,EAAEyB,GAAM0B,KAAK,kBAEnCkd,aAAc,WACN9a,KAAK0f,aAAe1f,KAAK+e,UAAUzZ,oBACnCtF,KAAKwC,OAAO2G,OACZnJ,KAAKuJ,QAAQuS,OAAO9b,KAAKua,aAEzBva,KAAKwC,OAAOyM,OACZjP,KAAKuJ,QAAQuC,IAAI,SAAU9L,KAAKua,aAGxCE,cAAe,WAAA,GAKPkF,GAJAld,EAAWzC,KAAK+e,UAAUzZ,kBACzB7C,KAGDkd,EAAc3f,KAAKkf,oBACnBS,IAAeA,GAAAA,EAAYlX,OAAiBzN,QAC5CgF,KAAKwC,OAAOwG,KAAKvG,EAASkd,EAAYlX,UAG9CmX,YAAa,SAAUC,GAAV,GACL3jB,GAAO,kEACPT,EAAWokB,EAAQ3jB,KACnB4jB,EAAiC,IAAlBD,EAAQ1kB,MACvBI,EAAWskB,EAAQtkB,SACnBmkB,EAAY1f,KAAK0f,YACjBK,EAAa/f,KAAK3E,QAAQ4F,SAAWjB,KAAK3E,QAAQ4F,QAAQjG,MAyB9D,OAxBI8kB,IAAgBD,EAAQG,WACxB9jB,GAAQ,YAER2jB,EAAQI,mBAAqBF,IAC7B7jB,GAAQ,WAERX,IACAW,GAAQ,qBAEZA,GAAQ,qBAAuBX,EAAW,OAAS,SAAW,wBAA0BskB,EAAQ1kB,MAAQ,KAEpGe,GADA6jB,EACQ/f,KAAKkgB,eAAezkB,GAEpBuE,KAAK+e,UAAUtc,SAAShH,GAEhCqkB,GAAgBD,EAAQG,SAEpB9jB,GADA6jB,EACQ,0CAA4C/f,KAAK+e,UAAU1Z,cAAcwa,EAAQpX,OAAS,gBAE1F,wBAA0BzI,KAAK+e,UAAU1Z,cAAcwa,EAAQpX,OAAS,SAE7EiX,GAAaK,IACpB7jB,GAAQ;AAELA,EAAO,SAElBgkB,eAAgB,SAAUzkB,GAAV,GAEHE,GACDyH,EACAC,EACAC,EAJJpH,EAAO,EACX,KAASP,EAAI,EAAGA,EAAIqE,KAAK3E,QAAQ4F,QAAQjG,OAAQW,IACzCyH,EAAepD,KAAK3E,QAAQ4F,QAAQtF,GAAG6H,MACvCH,EAAkBI,SAASL,EAAc,IACzCE,EAAa,GACbF,IAAiBM,MAAML,KACvBC,GAAc,gBACdA,GAAcD,EACdC,GAAcpG,EAAqByG,KAAKP,GAAgB,IAAM,KAC9DE,GAAc,MAElBpH,GAAQ,wBAA4BoH,EAAa,IACjDpH,GAAQ8D,KAAK+e,UAAU,SAAWpjB,GAAGF,GACrCS,GAAQ,SAEZ,OAAOA,IAEX0f,QAAS,WAAA,GAIDiE,GAIApX,EAAOuX,EAAUpkB,EAPjBoN,EAAO,GACPrN,EAAI,EACJqH,EAAM,EAENmd,KACAjS,EAAOlO,KAAKM,WAAW4N,OACvBiQ,EAASne,KAAKpF,QAEd8kB,EAAY1f,KAAK0f,WACrB,IAAIA,EACA,IAAK/jB,EAAI,EAAGA,EAAIuS,EAAKlT,OAAQW,IAGzB,IAFA8M,EAAQyF,EAAKvS,GACbqkB,GAAW,EACNpkB,EAAI,EAAGA,EAAI6M,EAAMmD,MAAM5Q,OAAQY,IAChCikB,GACItkB,SAAUyE,KAAKogB,UAAU3X,EAAMmD,MAAMhQ,GAAIuiB,GACzCjiB,KAAMuM,EAAMmD,MAAMhQ,GAClB6M,MAAOA,EAAM7N,MACbolB,SAAUA,EACVC,kBAAmBrkB,IAAM6M,EAAMmD,MAAM5Q,OAAS,EAC9CG,MAAO6H,GAEXmd,EAAYnd,GAAO6c,EACnB7c,GAAO,EACPgG,GAAQhJ,KAAK4f,YAAYC,GACzBG,GAAW,MAInB,KAAKrkB,EAAI,EAAGA,EAAIuS,EAAKlT,OAAQW,IACzBkkB,GACItkB,SAAUyE,KAAKogB,UAAUlS,EAAKvS,GAAIwiB,GAClCjiB,KAAMgS,EAAKvS,GACXR,MAAOQ,GAEXwkB,EAAYxkB,GAAKkkB,EACjB7W,GAAQhJ,KAAK4f,YAAYC,EAGjC7f,MAAKia,MAAQkG,EACbngB,KAAKH,QAAQ,GAAGwgB,UAAYrX,EACxB0W,GAAaS,EAAYnlB,QACzBgF,KAAKya,iBAGb2F,UAAW,SAAU3kB,EAAU0iB,GAC3B,GAAIjjB,IAAU8E,KAAK8N,cAA4C,aAA5B9N,KAAK3E,QAAQqJ,UAChD,OAAOxJ,IAAU8E,KAAKye,kBAAkBhjB,EAAU0iB,SAEtD9V,YAAa,SAAU3L,GACnBsD,KAAKsgB,cAAgB1hB,KAAWlC,IAEpCoR,WAAY,WAIR,MAHK9N,MAAKsgB,eACNtgB,KAAKqI,YAAYrI,KAAKM,WAAW5D,WAE7BG,EAAMe,KAAK2iB,MAAMC,eAAexgB,KAAKM,WAAW5D,SAAUsD,KAAKsgB,gBAE3EzF,QAAS,SAAU1U,GAAV,GAKD6W,GAJAjd,EAAOC,KACP8I,EAAS3C,GAAKA,EAAE2C,OAChB2X,EAAmB1gB,EAAK1E,QAAQolB,iBAChCC,EAA0B,eAAX5X,CAEnB/I,GAAKmF,QAAQ,eACbnF,EAAK4gB,cAAc,WACnB5gB,EAAK+a,eACL/a,EAAK6b,UACL7b,EAAKwH,OAAM,GACPmZ,GAA2B,WAAX5X,GAChBkU,EAAS1hB,EAAgByE,EAAKma,WAAY/T,EAAEyF,OACxCoR,EAAOjhB,QAAQf,SACX0lB,EACA3gB,EAAKmF,QAAQ,sBAAwB0G,MAAOoR,EAAOjhB,UAEnDgE,EAAKnF,MAAMmF,EAAKuc,WAAWU,EAAOhhB,cAGnC+D,EAAK+N,cAAgB/N,EAAK+c,aAAe/c,EAAKyL,cACrDzL,EAAKoC,MAAM,GACPpC,EAAK+c,cACL/c,EAAK+c,aAAc,EACnB/c,EAAKia,iBAAmBja,EAAKke,cAAcle,EAAKoa,QAASpa,EAAKia,oBAE1DyG,GAAsB3X,GAAqB,QAAXA,GACxC/I,EAAKnF,MAAMmF,EAAKoa,SAEhBpa,EAAKge,gBACLhe,EAAKge,eAAeP,UAExBzd,EAAK4gB,cAAc,WACnB5gB,EAAKmF,QAAQ,cAEjBqC,MAAO,SAAUA,GACb,MAAIA,KAAU7M,EACHsF,KAAK4gB,QAEhB5gB,KAAK4gB,OAASrZ,EAAdvH,IAEJ0f,UAAW,WACP,OAAQ1f,KAAKM,WAAWmI,aAAezN,UAG/C+B,EAAG8jB,OAAOjkB,IAoEZE,OAAOD,MAAMikB,QACRhkB,OAAOD,OACE,kBAAVrC,SAAwBA,OAAOumB,IAAMvmB,OAAS,SAAUwmB,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.list.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.list', [\n        'kendo.data',\n        'kendo.popup'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'list',\n        name: 'List',\n        category: 'framework',\n        depends: [\n            'data',\n            'popup'\n        ],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, outerHeight = kendo._outerHeight, percentageUnitsRegex = /^\\d+(\\.\\d+)?%$/i, Widget = ui.Widget, keys = kendo.keys, support = kendo.support, htmlEncode = kendo.htmlEncode, activeElement = kendo._activeElement, outerWidth = kendo._outerWidth, ObservableArray = kendo.data.ObservableArray, ID = 'id', CHANGE = 'change', FOCUSED = 'k-state-focused', HOVER = 'k-state-hover', LOADING = 'k-i-loading', GROUPHEADER = '.k-group-header', ITEMSELECTOR = '.k-item', LABELIDPART = '_label', OPEN = 'open', CLOSE = 'close', CASCADE = 'cascade', SELECT = 'select', SELECTED = 'selected', REQUESTSTART = 'requestStart', REQUESTEND = 'requestEnd', extend = $.extend, proxy = $.proxy, isArray = $.isArray, browser = support.browser, HIDDENCLASS = 'k-hidden', WIDTH = 'width', isIE = browser.msie, isIE8 = isIE && browser.version < 9, quotRegExp = /\"/g, alternativeNames = {\n                'ComboBox': 'DropDownList',\n                'DropDownList': 'ComboBox'\n            };\n        var List = kendo.ui.DataBoundWidget.extend({\n            init: function (element, options) {\n                var that = this, ns = that.ns, id;\n                Widget.fn.init.call(that, element, options);\n                element = that.element;\n                options = that.options;\n                that._isSelect = element.is(SELECT);\n                if (that._isSelect && that.element[0].length) {\n                    if (!options.dataSource) {\n                        options.dataTextField = options.dataTextField || 'text';\n                        options.dataValueField = options.dataValueField || 'value';\n                    }\n                }\n                that.ul = $('<ul unselectable=\"on\" class=\"k-list k-reset\"/>').attr({\n                    tabIndex: -1,\n                    'aria-hidden': true\n                });\n                that.list = $('<div class=\\'k-list-container\\'/>').append(that.ul).on('mousedown' + ns, proxy(that._listMousedown, that));\n                id = element.attr(ID);\n                if (!id) {\n                    id = kendo.guid();\n                }\n                that.list.attr(ID, id + '-list');\n                that.ul.attr(ID, id + '_listbox');\n                if (options.columns && options.columns.length) {\n                    that.ul.removeClass('k-list').addClass('k-grid-list');\n                    that._columnsHeader();\n                }\n                that._header();\n                that._noData();\n                that._footer();\n                that._accessors();\n                that._initValue();\n            },\n            options: {\n                valuePrimitive: false,\n                footerTemplate: '',\n                headerTemplate: '',\n                noDataTemplate: 'No data found.'\n            },\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n                if (options && options.enable !== undefined) {\n                    options.enabled = options.enable;\n                }\n                if (options.columns && options.columns.length) {\n                    this._columnsHeader();\n                }\n                this._header();\n                this._noData();\n                this._footer();\n                this._renderFooter();\n                this._renderNoData();\n            },\n            focus: function () {\n                this._focused.focus();\n            },\n            readonly: function (readonly) {\n                this._editable({\n                    readonly: readonly === undefined ? true : readonly,\n                    disable: false\n                });\n            },\n            enable: function (enable) {\n                this._editable({\n                    readonly: false,\n                    disable: !(enable = enable === undefined ? true : enable)\n                });\n            },\n            _header: function () {\n                var list = this;\n                var header = $(list.header);\n                var template = list.options.headerTemplate;\n                this._angularElement(header, 'cleanup');\n                kendo.destroy(header);\n                header.remove();\n                if (!template) {\n                    list.header = null;\n                    return;\n                }\n                var headerTemplate = typeof template !== 'function' ? kendo.template(template) : template;\n                header = $(headerTemplate({}));\n                list.header = header[0] ? header : null;\n                list.list.prepend(header);\n                this._angularElement(list.header, 'compile');\n            },\n            _columnsHeader: function () {\n                var list = this;\n                var columnsHeader = $(list.columnsHeader);\n                this._angularElement(columnsHeader, 'cleanup');\n                kendo.destroy(columnsHeader);\n                columnsHeader.remove();\n                var header = '<div class=\\'k-grid-header\\'><div class=\\'k-grid-header-wrap\\'><table>';\n                var colGroup = '<colgroup>';\n                var row = '<tr>';\n                for (var idx = 0; idx < this.options.columns.length; idx++) {\n                    var currentColumn = this.options.columns[idx];\n                    var title = currentColumn.title || currentColumn.field || '';\n                    var template = currentColumn.headerTemplate || title;\n                    var columnsHeaderTemplate = typeof template !== 'function' ? kendo.template(template) : template;\n                    var currentWidth = currentColumn.width;\n                    var currentWidthInt = parseInt(currentWidth, 10);\n                    var widthStyle = '';\n                    if (currentWidth && !isNaN(currentWidthInt)) {\n                        widthStyle += 'style=\\'width:';\n                        widthStyle += currentWidthInt;\n                        widthStyle += percentageUnitsRegex.test(currentWidth) ? '%' : 'px';\n                        widthStyle += ';\\'';\n                    }\n                    colGroup += '<col ' + widthStyle + '/>';\n                    row += '<th class=\\'k-header\\'>';\n                    row += columnsHeaderTemplate(currentColumn);\n                    row += '</th>';\n                }\n                colGroup += '</colgroup>';\n                row += '</tr>';\n                header += colGroup;\n                header += row;\n                header += '</table></div></div>';\n                list.columnsHeader = columnsHeader = $(header);\n                list.list.prepend(columnsHeader);\n                this._angularElement(list.columnsHeader, 'compile');\n            },\n            _noData: function () {\n                var list = this;\n                var noData = $(list.noData);\n                var template = list.options.noDataTemplate;\n                list.angular('cleanup', function () {\n                    return { elements: noData };\n                });\n                kendo.destroy(noData);\n                noData.remove();\n                if (!template) {\n                    list.noData = null;\n                    return;\n                }\n                list.noData = $('<div class=\"k-nodata\" style=\"display:none\"><div></div></div>').appendTo(list.list);\n                list.noDataTemplate = typeof template !== 'function' ? kendo.template(template) : template;\n            },\n            _footer: function () {\n                var list = this;\n                var footer = $(list.footer);\n                var template = list.options.footerTemplate;\n                this._angularElement(footer, 'cleanup');\n                kendo.destroy(footer);\n                footer.remove();\n                if (!template) {\n                    list.footer = null;\n                    return;\n                }\n                list.footer = $('<div class=\"k-footer\"></div>').appendTo(list.list);\n                list.footerTemplate = typeof template !== 'function' ? kendo.template(template) : template;\n            },\n            _listOptions: function (options) {\n                var that = this;\n                var currentOptions = that.options;\n                var virtual = currentOptions.virtual;\n                var changeEventOption = { change: proxy(that._listChange, that) };\n                var listBoundHandler = proxy(that._listBound, that);\n                virtual = typeof virtual === 'object' ? virtual : {};\n                options = $.extend({\n                    autoBind: false,\n                    selectable: true,\n                    dataSource: that.dataSource,\n                    click: proxy(that._click, that),\n                    activate: proxy(that._activateItem, that),\n                    columns: currentOptions.columns,\n                    deactivate: proxy(that._deactivateItem, that),\n                    dataBinding: function () {\n                        that.trigger('dataBinding');\n                    },\n                    dataBound: listBoundHandler,\n                    height: currentOptions.height,\n                    dataValueField: currentOptions.dataValueField,\n                    dataTextField: currentOptions.dataTextField,\n                    groupTemplate: currentOptions.groupTemplate,\n                    fixedGroupTemplate: currentOptions.fixedGroupTemplate,\n                    template: currentOptions.template\n                }, options, virtual, changeEventOption);\n                if (!options.template) {\n                    options.template = '#:' + kendo.expr(options.dataTextField, 'data') + '#';\n                }\n                if (currentOptions.$angular) {\n                    options.$angular = currentOptions.$angular;\n                }\n                return options;\n            },\n            _initList: function () {\n                var that = this;\n                var listOptions = that._listOptions({ selectedItemChange: proxy(that._listChange, that) });\n                if (!that.options.virtual) {\n                    that.listView = new kendo.ui.StaticList(that.ul, listOptions);\n                } else {\n                    that.listView = new kendo.ui.VirtualList(that.ul, listOptions);\n                }\n                that.listView.bind('listBound', proxy(that._listBound, that));\n                that._setListValue();\n            },\n            _setListValue: function (value) {\n                value = value || this.options.value;\n                if (value !== undefined) {\n                    this.listView.value(value).done(proxy(this._updateSelectionState, this));\n                }\n            },\n            _updateSelectionState: $.noop,\n            _listMousedown: function (e) {\n                if (!this.filterInput || this.filterInput[0] !== e.target) {\n                    e.preventDefault();\n                }\n            },\n            _isFilterEnabled: function () {\n                var filter = this.options.filter;\n                return filter && filter !== 'none';\n            },\n            _hideClear: function () {\n                var list = this;\n                if (list._clear) {\n                    list._clear.addClass(HIDDENCLASS);\n                }\n            },\n            _showClear: function () {\n                if (this._clear) {\n                    this._clear.removeClass(HIDDENCLASS);\n                }\n            },\n            _clearValue: function () {\n                this._clearText();\n                this._accessor('');\n                this.listView.value([]);\n                if (this._isSelect) {\n                    this._customOption = undefined;\n                }\n                if (this._isFilterEnabled() && !this.options.enforceMinLength) {\n                    this._filter({\n                        word: '',\n                        open: false\n                    });\n                    if (this.options.highlightFirst) {\n                        this.listView.focus(0);\n                    }\n                }\n                this._change();\n            },\n            _clearText: function () {\n                this.text('');\n            },\n            _clearFilter: function () {\n                if (!this.options.virtual) {\n                    this.listView.bound(false);\n                }\n                this._filterSource();\n            },\n            _filterSource: function (filter, force) {\n                var that = this;\n                var options = that.options;\n                var isMultiColumnFiltering = options.filterFields && filter && filter.logic && filter.filters && filter.filters.length;\n                var dataSource = that.dataSource;\n                var expression = extend({}, dataSource.filter() || {});\n                var resetPageSettings = filter || expression.filters && expression.filters.length && !filter;\n                var removed = removeFiltersForField(expression, options.dataTextField);\n                this._clearFilterExpressions(expression);\n                if ((filter || removed) && that.trigger('filtering', { filter: filter })) {\n                    return;\n                }\n                var newExpression = {\n                    filters: [],\n                    logic: 'and'\n                };\n                if (isMultiColumnFiltering) {\n                    newExpression.filters.push(filter);\n                } else {\n                    this._pushFilterExpression(newExpression, filter);\n                }\n                if (isValidFilterExpr(expression)) {\n                    if (newExpression.logic === expression.logic) {\n                        newExpression.filters = newExpression.filters.concat(expression.filters);\n                    } else {\n                        newExpression.filters.push(expression);\n                    }\n                }\n                if (that._cascading) {\n                    this.listView.setDSFilter(newExpression);\n                }\n                var dataSourceState = extend({}, {\n                    page: resetPageSettings ? 1 : dataSource.page(),\n                    pageSize: resetPageSettings ? dataSource.options.pageSize : dataSource.pageSize(),\n                    sort: dataSource.sort(),\n                    filter: dataSource.filter(),\n                    group: dataSource.group(),\n                    aggregate: dataSource.aggregate()\n                }, { filter: newExpression });\n                return dataSource[force ? 'read' : 'query'](dataSource._mergeState(dataSourceState));\n            },\n            _pushFilterExpression: function (newExpression, filter) {\n                if (isValidFilterExpr(filter) && filter.value !== '') {\n                    newExpression.filters.push(filter);\n                }\n            },\n            _clearFilterExpressions: function (expression) {\n                if (!expression.filters) {\n                    return;\n                }\n                var filtersToRemove;\n                for (var i = 0; i < expression.filters.length; i++) {\n                    if ('fromFilter' in expression.filters[i]) {\n                        filtersToRemove = i;\n                    }\n                }\n                if (!isNaN(filtersToRemove)) {\n                    expression.filters.splice(filtersToRemove, 1);\n                }\n            },\n            _angularElement: function (element, action) {\n                if (!element) {\n                    return;\n                }\n                this.angular(action, function () {\n                    return { elements: element };\n                });\n            },\n            _renderNoData: function () {\n                var list = this;\n                var noData = list.noData;\n                if (!noData) {\n                    return;\n                }\n                this._angularElement(noData, 'cleanup');\n                noData.children(':first').html(list.noDataTemplate({ instance: list }));\n                this._angularElement(noData, 'compile');\n            },\n            _toggleNoData: function (show) {\n                $(this.noData).toggle(show);\n            },\n            _toggleHeader: function (show) {\n                var groupHeader = this.listView.content.prev(GROUPHEADER);\n                groupHeader.toggle(show);\n            },\n            _renderFooter: function () {\n                var list = this;\n                var footer = list.footer;\n                if (!footer) {\n                    return;\n                }\n                this._angularElement(footer, 'cleanup');\n                footer.html(list.footerTemplate({ instance: list }));\n                this._angularElement(footer, 'compile');\n            },\n            _allowOpening: function () {\n                return this.options.noDataTemplate || this.dataSource.flatView().length;\n            },\n            _initValue: function () {\n                var that = this, value = that.options.value;\n                if (value !== null) {\n                    that.element.val(value);\n                } else {\n                    value = that._accessor();\n                    that.options.value = value;\n                }\n                that._old = value;\n            },\n            _ignoreCase: function () {\n                var that = this, model = that.dataSource.reader.model, field;\n                if (model && model.fields) {\n                    field = model.fields[that.options.dataTextField];\n                    if (field && field.type && field.type !== 'string') {\n                        that.options.ignoreCase = false;\n                    }\n                }\n            },\n            _focus: function (candidate) {\n                return this.listView.focus(candidate);\n            },\n            _filter: function (options) {\n                var that = this;\n                var widgetOptions = that.options;\n                var word = options.word;\n                var filterFields = widgetOptions.filterFields;\n                var field = widgetOptions.dataTextField;\n                var expression;\n                if (filterFields && filterFields.length) {\n                    expression = {\n                        logic: 'or',\n                        filters: [],\n                        fromFilter: true\n                    };\n                    for (var i = 0; i < filterFields.length; i++) {\n                        this._pushFilterExpression(expression, that._buildExpression(word, filterFields[i]));\n                    }\n                } else {\n                    expression = that._buildExpression(word, field);\n                }\n                that._open = options.open;\n                that._filterSource(expression);\n            },\n            _buildExpression: function (value, field) {\n                var that = this;\n                var widgetOptions = that.options;\n                var ignoreCase = widgetOptions.ignoreCase;\n                var accentFoldingFiltering = that.dataSource.options.accentFoldingFiltering;\n                return {\n                    value: ignoreCase ? accentFoldingFiltering ? value.toLocaleLowerCase(accentFoldingFiltering) : value.toLowerCase() : value,\n                    field: field,\n                    operator: widgetOptions.filter,\n                    ignoreCase: ignoreCase\n                };\n            },\n            _clearButton: function () {\n                var list = this;\n                var clearTitle = list.options.messages && list.options.messages.clear ? list.options.messages.clear : 'clear';\n                if (!list._clear) {\n                    list._clear = $('<span unselectable=\"on\" class=\"k-icon k-clear-value k-i-close\" title=\"' + clearTitle + '\"></span>').attr({\n                        'role': 'button',\n                        'tabIndex': -1\n                    });\n                }\n                if (!list.options.clearButton) {\n                    list._clear.remove();\n                }\n                this._hideClear();\n            },\n            search: function (word) {\n                var options = this.options;\n                word = typeof word === 'string' ? word : this._inputValue();\n                clearTimeout(this._typingTimeout);\n                if (!options.enforceMinLength && !word.length || word.length >= options.minLength) {\n                    this._state = 'filter';\n                    if (this.listView) {\n                        this.listView._emptySearch = !$.trim(word).length;\n                    }\n                    if (!this._isFilterEnabled()) {\n                        this._searchByWord(word);\n                    } else {\n                        this._filter({\n                            word: word,\n                            open: true\n                        });\n                    }\n                }\n            },\n            current: function (candidate) {\n                return this._focus(candidate);\n            },\n            items: function () {\n                return this.ul[0].children;\n            },\n            destroy: function () {\n                var that = this;\n                var ns = that.ns;\n                Widget.fn.destroy.call(that);\n                that._unbindDataSource();\n                that.listView.destroy();\n                that.list.off(ns);\n                that.popup.destroy();\n                if (that._form) {\n                    that._form.off('reset', that._resetHandler);\n                }\n            },\n            dataItem: function (index) {\n                var that = this;\n                if (index === undefined) {\n                    return that.listView.selectedDataItems()[0];\n                }\n                if (typeof index !== 'number') {\n                    if (that.options.virtual) {\n                        return that.dataSource.getByUid($(index).data('uid'));\n                    }\n                    index = $(that.items()).index(index);\n                }\n                return that.dataSource.flatView()[index];\n            },\n            _activateItem: function () {\n                var current = this.listView.focus();\n                if (current) {\n                    this._focused.add(this.filterInput).attr('aria-activedescendant', current.attr('id'));\n                }\n            },\n            _deactivateItem: function () {\n                this._focused.add(this.filterInput).removeAttr('aria-activedescendant');\n            },\n            _accessors: function () {\n                var that = this;\n                var element = that.element;\n                var options = that.options;\n                var getter = kendo.getter;\n                var textField = element.attr(kendo.attr('text-field'));\n                var valueField = element.attr(kendo.attr('value-field'));\n                if (!options.dataTextField && textField) {\n                    options.dataTextField = textField;\n                }\n                if (!options.dataValueField && valueField) {\n                    options.dataValueField = valueField;\n                }\n                that._text = getter(options.dataTextField);\n                that._value = getter(options.dataValueField);\n            },\n            _aria: function (id) {\n                var that = this, options = that.options, element = that._focused.add(that.filterInput);\n                if (options.suggest !== undefined) {\n                    element.attr('aria-autocomplete', options.suggest ? 'both' : 'list');\n                }\n                id = id ? id + ' ' + that.ul[0].id : that.ul[0].id;\n                element.attr('aria-owns', id);\n                that.ul.attr('aria-live', !that._isFilterEnabled() ? 'off' : 'polite');\n                that._ariaLabel();\n            },\n            _ariaLabel: function () {\n                var that = this;\n                var focusedElm = that._focused;\n                var inputElm = that.element;\n                var inputId = inputElm.attr('id');\n                var labelElm = $('label[for=\"' + inputId + '\"]');\n                var ariaLabel = inputElm.attr('aria-label');\n                var ariaLabelledBy = inputElm.attr('aria-labelledby');\n                if (focusedElm === inputElm) {\n                    return;\n                }\n                if (ariaLabel) {\n                    focusedElm.attr('aria-label', ariaLabel);\n                } else if (ariaLabelledBy) {\n                    focusedElm.attr('aria-labelledby', ariaLabelledBy);\n                } else if (labelElm.length) {\n                    var labelId = labelElm.attr('id') || that._generateLabelId(labelElm, inputId || kendo.guid());\n                    focusedElm.attr('aria-labelledby', labelId);\n                }\n            },\n            _generateLabelId: function (label, inputId) {\n                var labelId = inputId + LABELIDPART;\n                label.attr('id', labelId);\n                return labelId;\n            },\n            _blur: function () {\n                var that = this;\n                that._change();\n                that.close();\n            },\n            _change: function () {\n                var that = this;\n                var index = that.selectedIndex;\n                var optionValue = that.options.value;\n                var value = that.value();\n                var trigger;\n                if (that._isSelect && !that.listView.bound() && optionValue) {\n                    value = optionValue;\n                }\n                if (value !== unifyType(that._old, typeof value) && value !== unifyType(that._oldText, typeof value)) {\n                    trigger = true;\n                } else if (that._valueBeforeCascade !== undefined && that._valueBeforeCascade !== unifyType(that._old, typeof that._valueBeforeCascade) && that._userTriggered) {\n                    trigger = true;\n                } else if (index !== undefined && index !== that._oldIndex && !that.listView.isFiltered()) {\n                    trigger = true;\n                }\n                if (trigger) {\n                    if (that._old === null || that._old === '' || value === '') {\n                        that._valueBeforeCascade = that._old = value;\n                    } else {\n                        if (that.dataItem()) {\n                            that._valueBeforeCascade = that._old = that.options.dataValueField ? that.dataItem()[that.options.dataValueField] : that.dataItem();\n                        } else {\n                            that._valueBeforeCascade = that._old = null;\n                        }\n                    }\n                    that._oldIndex = index;\n                    that._oldText = that.text && that.text();\n                    if (!that._typing) {\n                        that.element.trigger(CHANGE);\n                    }\n                    that.trigger(CHANGE);\n                }\n                that.typing = false;\n            },\n            _data: function () {\n                return this.dataSource.view();\n            },\n            _enable: function () {\n                var that = this, options = that.options, disabled = that.element.is('[disabled]');\n                if (options.enable !== undefined) {\n                    options.enabled = options.enable;\n                }\n                if (!options.enabled || disabled) {\n                    that.enable(false);\n                } else {\n                    that.readonly(that.element.is('[readonly]'));\n                }\n            },\n            _dataValue: function (dataItem) {\n                var value = this._value(dataItem);\n                if (value === undefined) {\n                    value = this._text(dataItem);\n                }\n                return value;\n            },\n            _offsetHeight: function () {\n                var offsetHeight = 0;\n                var siblings = this.listView.content.prevAll(':visible');\n                siblings.each(function () {\n                    var element = $(this);\n                    offsetHeight += outerHeight(element, true);\n                });\n                return offsetHeight;\n            },\n            _height: function (length) {\n                var that = this;\n                var list = that.list;\n                var height = that.options.height;\n                var visible = that.popup.visible();\n                var offsetTop;\n                var popups;\n                var footerHeight;\n                if (length || that.options.noDataTemplate) {\n                    popups = list.add(list.parent('.k-animation-container')).show();\n                    if (!list.is(':visible')) {\n                        popups.hide();\n                        return;\n                    }\n                    height = that.listView.content[0].scrollHeight > height ? height : 'auto';\n                    popups.height(height);\n                    if (height !== 'auto') {\n                        offsetTop = that._offsetHeight();\n                        footerHeight = outerHeight($(that.footer)) || 0;\n                        height = height - offsetTop - footerHeight;\n                    }\n                    that.listView.content.height(height);\n                    if (!visible) {\n                        popups.hide();\n                    }\n                }\n                return height;\n            },\n            _openHandler: function (e) {\n                this._adjustListWidth();\n                if (this.trigger(OPEN)) {\n                    e.preventDefault();\n                } else {\n                    this._focused.attr('aria-expanded', true);\n                    this.ul.attr('aria-hidden', false);\n                }\n            },\n            _adjustListWidth: function () {\n                var that = this, list = that.list, width = list[0].style.width, wrapper = that.wrapper, computedStyle, computedWidth;\n                if (!list.data(WIDTH) && width) {\n                    return;\n                }\n                computedStyle = window.getComputedStyle ? window.getComputedStyle(wrapper[0], null) : 0;\n                computedWidth = parseFloat(computedStyle && computedStyle.width) || outerWidth(wrapper);\n                if (computedStyle && browser.msie) {\n                    computedWidth += parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight) + parseFloat(computedStyle.borderLeftWidth) + parseFloat(computedStyle.borderRightWidth);\n                }\n                if (list.css('box-sizing') !== 'border-box') {\n                    width = computedWidth - (outerWidth(list) - list.width());\n                } else {\n                    width = computedWidth;\n                }\n                list.css({\n                    fontFamily: wrapper.css('font-family'),\n                    width: that.options.autoWidth ? 'auto' : width,\n                    minWidth: width,\n                    whiteSpace: that.options.autoWidth ? 'nowrap' : 'normal'\n                }).data(WIDTH, width);\n                return true;\n            },\n            _closeHandler: function (e) {\n                if (this.trigger(CLOSE)) {\n                    e.preventDefault();\n                } else {\n                    this._focused.attr('aria-expanded', false);\n                    this.ul.attr('aria-hidden', true);\n                }\n            },\n            _focusItem: function () {\n                var listView = this.listView;\n                var noFocusedItem = !listView.focus();\n                var index = last(listView.select());\n                if (index === undefined && this.options.highlightFirst && noFocusedItem) {\n                    index = 0;\n                }\n                if (index !== undefined) {\n                    listView.focus(index);\n                } else if (noFocusedItem) {\n                    listView.scrollToIndex(0);\n                }\n            },\n            _calculateGroupPadding: function (height) {\n                var li = this.ul.children('.k-first:first');\n                var groupHeader = this.listView.content.prev(GROUPHEADER);\n                var padding = 0;\n                var direction = 'right';\n                if (groupHeader[0] && groupHeader[0].style.display !== 'none') {\n                    if (height !== 'auto') {\n                        padding = kendo.support.scrollbar();\n                    }\n                    if (this.element.parents('.k-rtl').length) {\n                        direction = 'left';\n                    }\n                    padding += parseFloat(li.css('border-' + direction + '-width'), 10) + parseFloat(li.children('.k-group').css('padding-' + direction), 10);\n                    groupHeader.css('padding-' + direction, padding);\n                }\n            },\n            _calculatePopupHeight: function (force) {\n                var height = this._height(this.dataSource.flatView().length || force);\n                this._calculateGroupPadding(height);\n                this._calculateColumnsHeaderPadding(height);\n            },\n            _calculateColumnsHeaderPadding: function (height) {\n                if (this.options.columns && this.options.columns.length) {\n                    var list = this;\n                    var isRtl = support.isRtl(list.wrapper);\n                    var scrollbar = kendo.support.scrollbar();\n                    list.columnsHeader.css(isRtl ? 'padding-left' : 'padding-right', height !== 'auto' ? scrollbar : 0);\n                }\n            },\n            _refreshScroll: function () {\n                var listView = this.listView;\n                var enableYScroll = listView.element.height() > listView.content.height();\n                if (this.options.autoWidth) {\n                    listView.content.css({\n                        overflowX: 'hidden',\n                        overflowY: enableYScroll ? 'scroll' : 'auto'\n                    });\n                }\n            },\n            _resizePopup: function (force) {\n                if (this.options.virtual) {\n                    return;\n                }\n                if (!this.popup.element.is(':visible')) {\n                    this.popup.one('open', function (force) {\n                        return proxy(function () {\n                            this._calculatePopupHeight(force);\n                        }, this);\n                    }.call(this, force));\n                    this.popup.one('activate', proxy(this._refreshScroll, this));\n                } else {\n                    this._calculatePopupHeight(force);\n                }\n            },\n            _popup: function () {\n                var list = this;\n                list.popup = new ui.Popup(list.list, extend({}, list.options.popup, {\n                    anchor: list.wrapper,\n                    open: proxy(list._openHandler, list),\n                    close: proxy(list._closeHandler, list),\n                    animation: list.options.animation,\n                    isRtl: support.isRtl(list.wrapper),\n                    autosize: list.options.autoWidth\n                }));\n            },\n            _makeUnselectable: function () {\n                if (isIE8) {\n                    this.list.find('*').not('.k-textbox').attr('unselectable', 'on');\n                }\n            },\n            _toggleHover: function (e) {\n                $(e.currentTarget).toggleClass(HOVER, e.type === 'mouseenter');\n            },\n            _toggle: function (open, preventFocus) {\n                var that = this;\n                var touchEnabled = support.mobileOS && (support.touch || support.MSPointers || support.pointers);\n                open = open !== undefined ? open : !that.popup.visible();\n                if (!preventFocus && !touchEnabled && that._focused[0] !== activeElement()) {\n                    that._prevent = true;\n                    that._focused.focus();\n                    that._prevent = false;\n                }\n                that[open ? OPEN : CLOSE]();\n            },\n            _triggerCascade: function () {\n                var that = this;\n                if (!that._cascadeTriggered || that.value() !== unifyType(that._cascadedValue, typeof that.value())) {\n                    that._cascadedValue = that.value();\n                    that._cascadeTriggered = true;\n                    that.trigger(CASCADE, { userTriggered: that._userTriggered });\n                }\n            },\n            _triggerChange: function () {\n                if (this._valueBeforeCascade !== this.value()) {\n                    this.trigger(CHANGE);\n                }\n            },\n            _unbindDataSource: function () {\n                var that = this;\n                that.dataSource.unbind(REQUESTSTART, that._requestStartHandler).unbind(REQUESTEND, that._requestEndHandler).unbind('error', that._errorHandler);\n            },\n            requireValueMapper: function (options, value) {\n                var hasValue = (options.value instanceof Array ? options.value.length : options.value) || (value instanceof Array ? value.length : value);\n                if (hasValue && options.virtual && typeof options.virtual.valueMapper !== 'function') {\n                    throw new Error('ValueMapper is not provided while the value is being set. See http://docs.telerik.com/kendo-ui/controls/editors/combobox/virtualization#the-valuemapper-function');\n                }\n            }\n        });\n        function unifyType(value, type) {\n            if (value !== undefined && value !== '' && value !== null) {\n                if (type === 'boolean') {\n                    value = Boolean(value);\n                } else if (type === 'number') {\n                    value = Number(value);\n                } else if (type === 'string') {\n                    value = value.toString();\n                }\n            }\n            return value;\n        }\n        extend(List, {\n            inArray: function (node, parentNode) {\n                var idx, length, siblings = parentNode.children;\n                if (!node || node.parentNode !== parentNode) {\n                    return -1;\n                }\n                for (idx = 0, length = siblings.length; idx < length; idx++) {\n                    if (node === siblings[idx]) {\n                        return idx;\n                    }\n                }\n                return -1;\n            },\n            unifyType: unifyType\n        });\n        kendo.ui.List = List;\n        ui.Select = List.extend({\n            init: function (element, options) {\n                List.fn.init.call(this, element, options);\n                this._initial = this.element.val();\n            },\n            setDataSource: function (dataSource) {\n                var that = this;\n                var parent;\n                that.options.dataSource = dataSource;\n                that._dataSource();\n                if (that.listView.bound()) {\n                    that._initialIndex = null;\n                    that.listView._current = null;\n                }\n                that.listView.setDataSource(that.dataSource);\n                if (that.options.autoBind) {\n                    that.dataSource.fetch();\n                }\n                parent = that._parentWidget();\n                if (parent) {\n                    that._cascadeSelect(parent);\n                }\n            },\n            close: function () {\n                this.popup.close();\n            },\n            select: function (candidate) {\n                var that = this;\n                if (candidate === undefined) {\n                    return that.selectedIndex;\n                } else {\n                    return that._select(candidate).done(function () {\n                        that._cascadeValue = that._old = that._accessor();\n                        that._oldIndex = that.selectedIndex;\n                    });\n                }\n            },\n            _accessor: function (value, idx) {\n                return this[this._isSelect ? '_accessorSelect' : '_accessorInput'](value, idx);\n            },\n            _accessorInput: function (value) {\n                var element = this.element[0];\n                if (value === undefined) {\n                    return element.value;\n                } else {\n                    if (value === null) {\n                        value = '';\n                    }\n                    element.value = value;\n                }\n            },\n            _accessorSelect: function (value, idx) {\n                var element = this.element[0];\n                var hasValue;\n                if (value === undefined) {\n                    return getSelectedOption(element).value || '';\n                }\n                getSelectedOption(element).selected = false;\n                if (idx === undefined) {\n                    idx = -1;\n                }\n                hasValue = value !== null && value !== '';\n                if (hasValue && idx == -1) {\n                    this._custom(value);\n                } else {\n                    if (value) {\n                        element.value = value;\n                    } else {\n                        element.selectedIndex = idx;\n                    }\n                }\n            },\n            _syncValueAndText: function () {\n                return true;\n            },\n            _custom: function (value) {\n                var that = this;\n                var element = that.element;\n                var custom = that._customOption;\n                if (!custom) {\n                    custom = $('<option/>');\n                    that._customOption = custom;\n                    element.append(custom);\n                }\n                custom.text(value);\n                custom[0].selected = true;\n            },\n            _hideBusy: function () {\n                var that = this;\n                clearTimeout(that._busy);\n                that._arrowIcon.removeClass(LOADING);\n                that._focused.attr('aria-busy', false);\n                that._busy = null;\n                that._showClear();\n            },\n            _showBusy: function (e) {\n                var that = this;\n                if (e.isDefaultPrevented()) {\n                    return;\n                }\n                that._request = true;\n                if (that._busy) {\n                    return;\n                }\n                that._busy = setTimeout(function () {\n                    if (that._arrowIcon) {\n                        that._focused.attr('aria-busy', true);\n                        that._arrowIcon.addClass(LOADING);\n                        that._hideClear();\n                    }\n                }, 100);\n            },\n            _requestEnd: function () {\n                this._request = false;\n                this._hideBusy();\n            },\n            _dataSource: function () {\n                var that = this, element = that.element, options = that.options, dataSource = options.dataSource || {}, idx;\n                dataSource = $.isArray(dataSource) ? { data: dataSource } : dataSource;\n                if (that._isSelect) {\n                    idx = element[0].selectedIndex;\n                    if (idx > -1) {\n                        options.index = idx;\n                    }\n                    dataSource.select = element;\n                    dataSource.fields = [\n                        { field: options.dataTextField },\n                        { field: options.dataValueField }\n                    ];\n                }\n                if (that.dataSource) {\n                    that._unbindDataSource();\n                } else {\n                    that._requestStartHandler = proxy(that._showBusy, that);\n                    that._requestEndHandler = proxy(that._requestEnd, that);\n                    that._errorHandler = proxy(that._hideBusy, that);\n                }\n                that.dataSource = kendo.data.DataSource.create(dataSource).bind(REQUESTSTART, that._requestStartHandler).bind(REQUESTEND, that._requestEndHandler).bind('error', that._errorHandler);\n            },\n            _firstItem: function () {\n                this.listView.focusFirst();\n            },\n            _lastItem: function () {\n                this.listView.focusLast();\n            },\n            _nextItem: function () {\n                this.listView.focusNext();\n            },\n            _prevItem: function () {\n                this.listView.focusPrev();\n            },\n            _move: function (e) {\n                var that = this;\n                var listView = that.listView;\n                var key = e.keyCode;\n                var down = key === keys.DOWN;\n                var dataItem;\n                var pressed;\n                var current;\n                if (key === keys.UP || down) {\n                    if (e.altKey) {\n                        that.toggle(down);\n                    } else {\n                        if (!listView.bound() && !that.ul[0].firstChild) {\n                            if (!that._fetch) {\n                                that.dataSource.one(CHANGE, function () {\n                                    that._fetch = false;\n                                    that._move(e);\n                                });\n                                that._fetch = true;\n                                that._filterSource();\n                            }\n                            e.preventDefault();\n                            return true;\n                        }\n                        current = that._focus();\n                        if (!that._fetch && (!current || current.hasClass('k-state-selected'))) {\n                            if (down) {\n                                that._nextItem();\n                                if (!that._focus()) {\n                                    that._lastItem();\n                                }\n                            } else {\n                                that._prevItem();\n                                if (!that._focus()) {\n                                    that._firstItem();\n                                }\n                            }\n                        }\n                        dataItem = listView.dataItemByIndex(listView.getElementIndex(that._focus()));\n                        if (that.trigger(SELECT, {\n                                dataItem: dataItem,\n                                item: that._focus()\n                            })) {\n                            that._focus(current);\n                            return;\n                        }\n                        that._select(that._focus(), true).done(function () {\n                            if (!that.popup.visible()) {\n                                that._blur();\n                            }\n                            if (that._cascadedValue === null) {\n                                that._cascadedValue = that.value();\n                            } else {\n                                that._cascadedValue = that.dataItem() ? that.dataItem()[that.options.dataValueField] || that.dataItem() : null;\n                            }\n                        });\n                    }\n                    e.preventDefault();\n                    pressed = true;\n                } else if (key === keys.ENTER || key === keys.TAB) {\n                    if (that.popup.visible()) {\n                        e.preventDefault();\n                    }\n                    current = that._focus();\n                    dataItem = that.dataItem();\n                    if (!that.popup.visible() && (!dataItem || that.text() !== that._text(dataItem))) {\n                        current = null;\n                    }\n                    var activeFilter = that.filterInput && that.filterInput[0] === activeElement();\n                    var selection;\n                    if (current) {\n                        dataItem = listView.dataItemByIndex(listView.getElementIndex(current));\n                        var shouldTrigger = true;\n                        if (dataItem) {\n                            shouldTrigger = that._value(dataItem) !== List.unifyType(that.value(), typeof that._value(dataItem));\n                        }\n                        if (shouldTrigger && that.trigger(SELECT, {\n                                dataItem: dataItem,\n                                item: current\n                            })) {\n                            return;\n                        }\n                        selection = that._select(current);\n                    } else if (that.input) {\n                        if (that._syncValueAndText() || that._isSelect) {\n                            that._accessor(that.input.val());\n                        }\n                        that.listView.value(that.input.val());\n                    }\n                    if (that._focusElement) {\n                        that._focusElement(that.wrapper);\n                    }\n                    if (activeFilter && key === keys.TAB) {\n                        that.wrapper.focusout();\n                    } else {\n                        if (selection && typeof selection.done === 'function') {\n                            selection.done(function () {\n                                that._blur();\n                            });\n                        } else {\n                            that._blur();\n                        }\n                    }\n                    that.close();\n                    pressed = true;\n                } else if (key === keys.ESC) {\n                    if (that.popup.visible()) {\n                        e.preventDefault();\n                    }\n                    that.close();\n                    pressed = true;\n                } else if (that.popup.visible() && (key === keys.PAGEDOWN || key === keys.PAGEUP)) {\n                    e.preventDefault();\n                    var direction = key === keys.PAGEDOWN ? 1 : -1;\n                    listView.scrollWith(direction * listView.screenHeight());\n                    pressed = true;\n                }\n                return pressed;\n            },\n            _fetchData: function () {\n                var that = this;\n                var hasItems = !!that.dataSource.view().length;\n                if (that._request || that.options.cascadeFrom) {\n                    return;\n                }\n                if (!that.listView.bound() && !that._fetch && !hasItems) {\n                    that._fetch = true;\n                    that.dataSource.fetch().done(function () {\n                        that._fetch = false;\n                    });\n                }\n            },\n            _options: function (data, optionLabel, value) {\n                var that = this, element = that.element, htmlElement = element[0], length = data.length, options = '', option, dataItem, dataText, dataValue, idx = 0;\n                if (optionLabel) {\n                    options = optionLabel;\n                }\n                for (; idx < length; idx++) {\n                    option = '<option';\n                    dataItem = data[idx];\n                    dataText = that._text(dataItem);\n                    dataValue = that._value(dataItem);\n                    if (dataValue !== undefined) {\n                        dataValue += '';\n                        if (dataValue.indexOf('\"') !== -1) {\n                            dataValue = dataValue.replace(quotRegExp, '&quot;');\n                        }\n                        option += ' value=\"' + dataValue + '\"';\n                    }\n                    option += '>';\n                    if (dataText !== undefined) {\n                        option += htmlEncode(dataText);\n                    }\n                    option += '</option>';\n                    options += option;\n                }\n                element.html(options);\n                if (value !== undefined) {\n                    htmlElement.value = value;\n                    if (htmlElement.value && !value) {\n                        htmlElement.selectedIndex = -1;\n                    }\n                }\n                if (htmlElement.selectedIndex !== -1) {\n                    option = getSelectedOption(htmlElement);\n                    if (option) {\n                        option.setAttribute(SELECTED, SELECTED);\n                    }\n                }\n            },\n            _reset: function () {\n                var that = this, element = that.element, formId = element.attr('form'), form = formId ? $('#' + formId) : element.closest('form');\n                if (form[0]) {\n                    that._resetHandler = function () {\n                        setTimeout(function () {\n                            that.value(that._initial);\n                        });\n                    };\n                    that._form = form.on('reset', that._resetHandler);\n                }\n            },\n            _parentWidget: function () {\n                var name = this.options.name;\n                if (!this.options.cascadeFrom) {\n                    return;\n                }\n                var parentElement = $('#' + this.options.cascadeFrom);\n                var parent = parentElement.data('kendo' + name);\n                if (!parent) {\n                    parent = parentElement.data('kendo' + alternativeNames[name]);\n                }\n                return parent;\n            },\n            _cascade: function () {\n                var that = this;\n                var options = that.options;\n                var cascade = options.cascadeFrom;\n                var parent;\n                if (cascade) {\n                    parent = that._parentWidget();\n                    if (!parent) {\n                        return;\n                    }\n                    that._cascadeHandlerProxy = proxy(that._cascadeHandler, that);\n                    that._cascadeFilterRequests = [];\n                    options.autoBind = false;\n                    parent.bind('set', function () {\n                        that.one('set', function (e) {\n                            that._selectedValue = e.value || that._accessor();\n                        });\n                    });\n                    parent.first(CASCADE, that._cascadeHandlerProxy);\n                    if (parent.listView.bound()) {\n                        that._toggleCascadeOnFocus();\n                        that._cascadeSelect(parent);\n                    } else {\n                        parent.one('dataBound', function () {\n                            that._toggleCascadeOnFocus();\n                            if (parent.popup.visible()) {\n                                parent._focused.focus();\n                            }\n                        });\n                        if (!parent.value()) {\n                            that.enable(false);\n                        }\n                    }\n                }\n            },\n            _toggleCascadeOnFocus: function () {\n                var that = this;\n                var parent = that._parentWidget();\n                var focusout = isIE ? 'blur' : 'focusout';\n                parent._focused.add(parent.filterInput).bind('focus', function () {\n                    parent.unbind(CASCADE, that._cascadeHandlerProxy);\n                    parent.first(CHANGE, that._cascadeHandlerProxy);\n                });\n                parent._focused.add(parent.filterInput).bind(focusout, function () {\n                    parent.unbind(CHANGE, that._cascadeHandlerProxy);\n                    parent.first(CASCADE, that._cascadeHandlerProxy);\n                });\n            },\n            _cascadeHandler: function (e) {\n                var parent = this._parentWidget();\n                var valueBeforeCascade = this.value();\n                this._userTriggered = e.userTriggered;\n                if (this.listView.bound()) {\n                    this._clearSelection(parent, true);\n                }\n                this._cascadeSelect(parent, valueBeforeCascade);\n            },\n            _cascadeChange: function (parent) {\n                var that = this;\n                var value = that._accessor() || that._selectedValue;\n                if (!that._cascadeFilterRequests.length) {\n                    that._selectedValue = null;\n                }\n                if (that._userTriggered) {\n                    that._clearSelection(parent, true);\n                } else if (value) {\n                    if (value !== unifyType(that.listView.value()[0], typeof value)) {\n                        that.value(value);\n                    }\n                    if (!that.dataSource.view()[0] || that.selectedIndex === -1) {\n                        that._clearSelection(parent, true);\n                    }\n                } else if (that.dataSource.flatView().length) {\n                    that.select(that.options.index);\n                }\n                that.enable();\n                that._triggerCascade();\n                that._triggerChange();\n                that._userTriggered = false;\n            },\n            _cascadeSelect: function (parent, valueBeforeCascade) {\n                var that = this;\n                var dataItem = parent.dataItem();\n                var filterValue = dataItem ? dataItem[that.options.cascadeFromParentField] || parent._value(dataItem) : null;\n                var valueField = that.options.cascadeFromField || parent.options.dataValueField;\n                var expressions;\n                that._valueBeforeCascade = valueBeforeCascade !== undefined ? valueBeforeCascade : that.value();\n                if (filterValue || filterValue === 0) {\n                    expressions = that.dataSource.filter() || {};\n                    removeFiltersForField(expressions, valueField);\n                    var handler = function () {\n                        var currentHandler = that._cascadeFilterRequests.shift();\n                        if (currentHandler) {\n                            that.unbind('dataBound', currentHandler);\n                        }\n                        currentHandler = that._cascadeFilterRequests[0];\n                        if (currentHandler) {\n                            that.first('dataBound', currentHandler);\n                        }\n                        that._cascadeChange(parent);\n                    };\n                    that._cascadeFilterRequests.push(handler);\n                    if (that._cascadeFilterRequests.length === 1) {\n                        that.first('dataBound', handler);\n                    }\n                    that._cascading = true;\n                    that._filterSource({\n                        field: valueField,\n                        operator: 'eq',\n                        value: filterValue\n                    });\n                    that._cascading = false;\n                } else {\n                    that.enable(false);\n                    that._clearSelection(parent);\n                    that._triggerCascade();\n                    that._triggerChange();\n                    that._userTriggered = false;\n                }\n            }\n        });\n        var STATIC_LIST_NS = '.StaticList';\n        var StaticList = kendo.ui.DataBoundWidget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this.element.attr('role', 'listbox').on('click' + STATIC_LIST_NS, 'li', proxy(this._click, this)).on('mouseenter' + STATIC_LIST_NS, 'li', function () {\n                    $(this).addClass(HOVER);\n                }).on('mouseleave' + STATIC_LIST_NS, 'li', function () {\n                    $(this).removeClass(HOVER);\n                });\n                if (support.touch) {\n                    this._touchHandlers();\n                }\n                if (this.options.selectable === 'multiple') {\n                    this.element.attr('aria-multiselectable', true);\n                }\n                this.content = this.element.wrap('<div class=\\'k-list-scroller\\' unselectable=\\'on\\'></div>').parent();\n                this.header = this.content.before('<div class=\"k-group-header\" style=\"display:none\"></div>').prev();\n                this.bound(false);\n                this._optionID = kendo.guid();\n                this._selectedIndices = [];\n                this._view = [];\n                this._dataItems = [];\n                this._values = [];\n                var value = this.options.value;\n                if (value) {\n                    this._values = $.isArray(value) ? value.slice(0) : [value];\n                }\n                this._getter();\n                this._templates();\n                this.setDataSource(this.options.dataSource);\n                this._onScroll = proxy(function () {\n                    var that = this;\n                    clearTimeout(that._scrollId);\n                    that._scrollId = setTimeout(function () {\n                        that._renderHeader();\n                    }, 50);\n                }, this);\n            },\n            options: {\n                name: 'StaticList',\n                dataValueField: null,\n                valuePrimitive: false,\n                selectable: true,\n                template: null,\n                groupTemplate: null,\n                fixedGroupTemplate: null\n            },\n            events: [\n                'click',\n                CHANGE,\n                'activate',\n                'deactivate',\n                'dataBinding',\n                'dataBound',\n                'selectedItemChange'\n            ],\n            setDataSource: function (source) {\n                var that = this;\n                var dataSource = source || {};\n                var value;\n                dataSource = $.isArray(dataSource) ? { data: dataSource } : dataSource;\n                dataSource = kendo.data.DataSource.create(dataSource);\n                if (that.dataSource) {\n                    that.dataSource.unbind(CHANGE, that._refreshHandler);\n                    value = that.value();\n                    that.value([]);\n                    that.bound(false);\n                    that.value(value);\n                } else {\n                    that._refreshHandler = proxy(that.refresh, that);\n                }\n                that.setDSFilter(dataSource.filter());\n                that.dataSource = dataSource.bind(CHANGE, that._refreshHandler);\n                that._fixedHeader();\n            },\n            _touchHandlers: function () {\n                var that = this;\n                var startY;\n                var endY;\n                var tapPosition = function (event) {\n                    return (event.originalEvent || event).changedTouches[0].pageY;\n                };\n                that.element.on('touchstart' + STATIC_LIST_NS, function (e) {\n                    startY = tapPosition(e);\n                });\n                that.element.on('touchend' + STATIC_LIST_NS, function (e) {\n                    if (e.isDefaultPrevented()) {\n                        return;\n                    }\n                    endY = tapPosition(e);\n                    if (Math.abs(endY - startY) < 10) {\n                        that._touchTriggered = true;\n                        that._triggerClick($(e.target).closest(ITEMSELECTOR).get(0));\n                    }\n                });\n            },\n            skip: function () {\n                return this.dataSource.skip();\n            },\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n                this._getter();\n                this._templates();\n                this._render();\n            },\n            destroy: function () {\n                this.element.off(STATIC_LIST_NS);\n                if (this._refreshHandler) {\n                    this.dataSource.unbind(CHANGE, this._refreshHandler);\n                }\n                clearTimeout(this._scrollId);\n                Widget.fn.destroy.call(this);\n            },\n            dataItemByIndex: function (index) {\n                return this.dataSource.flatView()[index];\n            },\n            screenHeight: function () {\n                return this.content[0].clientHeight;\n            },\n            scrollToIndex: function (index) {\n                var item = this.element[0].children[index];\n                if (item) {\n                    this.scroll(item);\n                }\n            },\n            scrollWith: function (value) {\n                this.content.scrollTop(this.content.scrollTop() + value);\n            },\n            scroll: function (item) {\n                if (!item) {\n                    return;\n                }\n                if (item[0]) {\n                    item = item[0];\n                }\n                var content = this.content[0], itemOffsetTop = item.offsetTop, itemOffsetHeight = item.offsetHeight, contentScrollTop = content.scrollTop, contentOffsetHeight = content.clientHeight, bottomDistance = itemOffsetTop + itemOffsetHeight;\n                if (contentScrollTop > itemOffsetTop) {\n                    contentScrollTop = itemOffsetTop;\n                } else if (bottomDistance > contentScrollTop + contentOffsetHeight) {\n                    contentScrollTop = bottomDistance - contentOffsetHeight;\n                }\n                content.scrollTop = contentScrollTop;\n            },\n            selectedDataItems: function (dataItems) {\n                if (dataItems === undefined) {\n                    return this._dataItems.slice();\n                }\n                this._dataItems = dataItems;\n                this._values = this._getValues(dataItems);\n            },\n            _getValues: function (dataItems) {\n                var getter = this._valueGetter;\n                return $.map(dataItems, function (dataItem) {\n                    return getter(dataItem);\n                });\n            },\n            focusNext: function () {\n                var current = this.focus();\n                if (!current) {\n                    current = 0;\n                } else {\n                    current = current.next();\n                }\n                this.focus(current);\n            },\n            focusPrev: function () {\n                var current = this.focus();\n                if (!current) {\n                    current = this.element[0].children.length - 1;\n                } else {\n                    current = current.prev();\n                }\n                this.focus(current);\n            },\n            focusFirst: function () {\n                this.focus(this.element[0].children[0]);\n            },\n            focusLast: function () {\n                this.focus(last(this.element[0].children));\n            },\n            focus: function (candidate) {\n                var that = this;\n                var id = that._optionID;\n                var hasCandidate;\n                if (candidate === undefined) {\n                    return that._current;\n                }\n                candidate = last(that._get(candidate));\n                candidate = $(this.element[0].children[candidate]);\n                if (that._current) {\n                    that._current.removeClass(FOCUSED).removeAttr(ID);\n                    that.trigger('deactivate');\n                }\n                hasCandidate = !!candidate[0];\n                if (hasCandidate) {\n                    candidate.addClass(FOCUSED);\n                    that.scroll(candidate);\n                    candidate.attr('id', id);\n                }\n                that._current = hasCandidate ? candidate : null;\n                that.trigger('activate');\n            },\n            focusIndex: function () {\n                return this.focus() ? this.focus().index() : undefined;\n            },\n            skipUpdate: function (skipUpdate) {\n                this._skipUpdate = skipUpdate;\n            },\n            select: function (indices) {\n                var that = this;\n                var selectable = that.options.selectable;\n                var singleSelection = selectable !== 'multiple' && selectable !== false;\n                var selectedIndices = that._selectedIndices;\n                var uiSelectedIndices = [this.element.find('.k-state-selected').index()];\n                var added = [];\n                var removed = [];\n                var result;\n                if (indices === undefined) {\n                    return selectedIndices.slice();\n                }\n                indices = that._get(indices);\n                if (indices.length === 1 && indices[0] === -1) {\n                    indices = [];\n                }\n                var deferred = $.Deferred().resolve();\n                var filtered = that.isFiltered();\n                if (filtered && !singleSelection && that._deselectFiltered(indices)) {\n                    return deferred;\n                }\n                if (singleSelection && !filtered && $.inArray(last(indices), selectedIndices) !== -1 && $.inArray(last(indices), uiSelectedIndices) !== -1) {\n                    if (that._dataItems.length && that._view.length) {\n                        that._dataItems = [that._view[selectedIndices[0]].item];\n                    }\n                    return deferred;\n                }\n                result = that._deselect(indices);\n                removed = result.removed;\n                indices = result.indices;\n                if (indices.length) {\n                    if (singleSelection) {\n                        indices = [last(indices)];\n                    }\n                    added = that._select(indices);\n                }\n                if (added.length || removed.length) {\n                    that._valueComparer = null;\n                    that.trigger(CHANGE, {\n                        added: added,\n                        removed: removed\n                    });\n                }\n                return deferred;\n            },\n            removeAt: function (position) {\n                this._selectedIndices.splice(position, 1);\n                this._values.splice(position, 1);\n                this._valueComparer = null;\n                return {\n                    position: position,\n                    dataItem: this._dataItems.splice(position, 1)[0]\n                };\n            },\n            setValue: function (value) {\n                value = $.isArray(value) || value instanceof ObservableArray ? value.slice(0) : [value];\n                this._values = value;\n                this._valueComparer = null;\n            },\n            value: function (value) {\n                var that = this;\n                var deferred = that._valueDeferred;\n                var indices;\n                if (value === undefined) {\n                    return that._values.slice();\n                }\n                that.setValue(value);\n                if (!deferred || deferred.state() === 'resolved') {\n                    that._valueDeferred = deferred = $.Deferred();\n                }\n                if (that.bound()) {\n                    indices = that._valueIndices(that._values);\n                    if (that.options.selectable === 'multiple') {\n                        that.select(-1);\n                    }\n                    that.select(indices);\n                    deferred.resolve();\n                }\n                that._skipUpdate = false;\n                return deferred;\n            },\n            items: function () {\n                return this.element.children(ITEMSELECTOR);\n            },\n            _click: function (e) {\n                if (this._touchTriggered) {\n                    this._touchTriggered = false;\n                    return;\n                }\n                if (!e.isDefaultPrevented()) {\n                    this._triggerClick(e.currentTarget);\n                }\n            },\n            _triggerClick: function (item) {\n                if (!this.trigger('click', { item: $(item) })) {\n                    this.select(item);\n                }\n            },\n            _valueExpr: function (type, values) {\n                var that = this;\n                var idx = 0;\n                var body;\n                var comparer;\n                var normalized = [];\n                if (!that._valueComparer || that._valueType !== type) {\n                    that._valueType = type;\n                    for (; idx < values.length; idx++) {\n                        normalized.push(unifyType(values[idx], type));\n                    }\n                    body = 'for (var idx = 0; idx < ' + normalized.length + '; idx++) {' + ' if (current === values[idx]) {' + '   return idx;' + ' }' + '} ' + 'return -1;';\n                    comparer = new Function('current', 'values', body);\n                    that._valueComparer = function (current) {\n                        return comparer(current, normalized);\n                    };\n                }\n                return that._valueComparer;\n            },\n            _dataItemPosition: function (dataItem, values) {\n                var value = this._valueGetter(dataItem);\n                var valueExpr = this._valueExpr(typeof value, values);\n                return valueExpr(value);\n            },\n            _getter: function () {\n                this._valueGetter = kendo.getter(this.options.dataValueField);\n            },\n            _deselect: function (indices) {\n                var that = this;\n                var children = that.element[0].children;\n                var selectable = that.options.selectable;\n                var selectedIndices = that._selectedIndices;\n                var dataItems = that._dataItems;\n                var values = that._values;\n                var removed = [];\n                var i = 0;\n                var j;\n                var index, selectedIndex;\n                var removedIndices = 0;\n                indices = indices.slice();\n                if (selectable === true || !indices.length) {\n                    for (; i < selectedIndices.length; i++) {\n                        $(children[selectedIndices[i]]).removeClass('k-state-selected').attr('aria-selected', false);\n                        removed.push({\n                            position: i,\n                            dataItem: dataItems[i]\n                        });\n                    }\n                    that._values = [];\n                    that._dataItems = [];\n                    that._selectedIndices = [];\n                } else if (selectable === 'multiple') {\n                    for (; i < indices.length; i++) {\n                        index = indices[i];\n                        if (!$(children[index]).hasClass('k-state-selected')) {\n                            continue;\n                        }\n                        for (j = 0; j < selectedIndices.length; j++) {\n                            selectedIndex = selectedIndices[j];\n                            if (selectedIndex === index) {\n                                $(children[selectedIndex]).removeClass('k-state-selected').attr('aria-selected', false);\n                                removed.push({\n                                    position: j + removedIndices,\n                                    dataItem: dataItems.splice(j, 1)[0]\n                                });\n                                selectedIndices.splice(j, 1);\n                                indices.splice(i, 1);\n                                values.splice(j, 1);\n                                removedIndices += 1;\n                                i -= 1;\n                                j -= 1;\n                                break;\n                            }\n                        }\n                    }\n                }\n                return {\n                    indices: indices,\n                    removed: removed\n                };\n            },\n            _deselectFiltered: function (indices) {\n                var children = this.element[0].children;\n                var dataItem, index, position;\n                var removed = [];\n                var idx = 0;\n                for (; idx < indices.length; idx++) {\n                    index = indices[idx];\n                    dataItem = this._view[index].item;\n                    position = this._dataItemPosition(dataItem, this._values);\n                    if (position > -1) {\n                        removed.push(this.removeAt(position));\n                        $(children[index]).removeClass('k-state-selected');\n                    }\n                }\n                if (removed.length) {\n                    this.trigger(CHANGE, {\n                        added: [],\n                        removed: removed\n                    });\n                    return true;\n                }\n                return false;\n            },\n            _select: function (indices) {\n                var that = this;\n                var children = that.element[0].children;\n                var data = that._view;\n                var dataItem, index;\n                var added = [];\n                var idx = 0;\n                if (last(indices) !== -1) {\n                    that.focus(indices);\n                }\n                for (; idx < indices.length; idx++) {\n                    index = indices[idx];\n                    dataItem = data[index];\n                    if (index === -1 || !dataItem) {\n                        continue;\n                    }\n                    dataItem = dataItem.item;\n                    that._selectedIndices.push(index);\n                    that._dataItems.push(dataItem);\n                    that._values.push(that._valueGetter(dataItem));\n                    $(children[index]).addClass('k-state-selected').attr('aria-selected', true);\n                    added.push({ dataItem: dataItem });\n                }\n                return added;\n            },\n            getElementIndex: function (element) {\n                return $(element).data('offset-index');\n            },\n            _get: function (candidate) {\n                if (typeof candidate === 'number') {\n                    candidate = [candidate];\n                } else if (!isArray(candidate)) {\n                    candidate = this.getElementIndex(candidate);\n                    candidate = [candidate !== undefined ? candidate : -1];\n                }\n                return candidate;\n            },\n            _template: function () {\n                var that = this;\n                var options = that.options;\n                var template = options.template;\n                if (!template) {\n                    template = kendo.template('<li tabindex=\"-1\" role=\"option\" unselectable=\"on\" class=\"k-item\">${' + kendo.expr(options.dataTextField, 'data') + '}</li>', { useWithBlock: false });\n                } else {\n                    template = kendo.template(template);\n                    template = function (data) {\n                        return '<li tabindex=\"-1\" role=\"option\" unselectable=\"on\" class=\"k-item\">' + template(data) + '</li>';\n                    };\n                }\n                return template;\n            },\n            _templates: function () {\n                var template;\n                var options = this.options;\n                var templates = {\n                    template: options.template,\n                    groupTemplate: options.groupTemplate,\n                    fixedGroupTemplate: options.fixedGroupTemplate\n                };\n                if (options.columns) {\n                    for (var i = 0; i < options.columns.length; i++) {\n                        var currentColumn = options.columns[i];\n                        var templateText = currentColumn.field ? currentColumn.field.toString() : 'text';\n                        templates['column' + i] = currentColumn.template || '#: ' + templateText + '#';\n                    }\n                }\n                for (var key in templates) {\n                    template = templates[key];\n                    if (template && typeof template !== 'function') {\n                        templates[key] = kendo.template(template);\n                    }\n                }\n                this.templates = templates;\n            },\n            _normalizeIndices: function (indices) {\n                var newIndices = [];\n                var idx = 0;\n                for (; idx < indices.length; idx++) {\n                    if (indices[idx] !== undefined) {\n                        newIndices.push(indices[idx]);\n                    }\n                }\n                return newIndices;\n            },\n            _valueIndices: function (values, indices) {\n                var data = this._view;\n                var idx = 0;\n                var index;\n                indices = indices ? indices.slice() : [];\n                if (!values.length) {\n                    return [];\n                }\n                for (; idx < data.length; idx++) {\n                    index = this._dataItemPosition(data[idx].item, values);\n                    if (index !== -1) {\n                        indices[index] = idx;\n                    }\n                }\n                return this._normalizeIndices(indices);\n            },\n            _firstVisibleItem: function () {\n                var element = this.element[0];\n                var content = this.content[0];\n                var scrollTop = content.scrollTop;\n                var itemHeight = $(element.children[0]).height();\n                var itemIndex = Math.floor(scrollTop / itemHeight) || 0;\n                var item = element.children[itemIndex] || element.lastChild;\n                var forward = item.offsetTop < scrollTop;\n                while (item) {\n                    if (forward) {\n                        if (item.offsetTop + itemHeight > scrollTop || !item.nextSibling) {\n                            break;\n                        }\n                        item = item.nextSibling;\n                    } else {\n                        if (item.offsetTop <= scrollTop || !item.previousSibling) {\n                            break;\n                        }\n                        item = item.previousSibling;\n                    }\n                }\n                return this._view[$(item).data('offset-index')];\n            },\n            _fixedHeader: function () {\n                if (this.isGrouped() && this.templates.fixedGroupTemplate) {\n                    this.header.show();\n                    this.content.scroll(this._onScroll);\n                } else {\n                    this.header.hide();\n                    this.content.off('scroll', this._onScroll);\n                }\n            },\n            _renderHeader: function () {\n                var template = this.templates.fixedGroupTemplate;\n                if (!template) {\n                    return;\n                }\n                var visibleItem = this._firstVisibleItem();\n                if (visibleItem && visibleItem.group.toString().length) {\n                    this.header.html(template(visibleItem.group));\n                }\n            },\n            _renderItem: function (context) {\n                var item = '<li tabindex=\"-1\" role=\"option\" unselectable=\"on\" class=\"k-item';\n                var dataItem = context.item;\n                var notFirstItem = context.index !== 0;\n                var selected = context.selected;\n                var isGrouped = this.isGrouped();\n                var hasColumns = this.options.columns && this.options.columns.length;\n                if (notFirstItem && context.newGroup) {\n                    item += ' k-first';\n                }\n                if (context.isLastGroupedItem && hasColumns) {\n                    item += ' k-last';\n                }\n                if (selected) {\n                    item += ' k-state-selected';\n                }\n                item += '\" aria-selected=\"' + (selected ? 'true' : 'false') + '\" data-offset-index=\"' + context.index + '\">';\n                if (hasColumns) {\n                    item += this._renderColumns(dataItem);\n                } else {\n                    item += this.templates.template(dataItem);\n                }\n                if (notFirstItem && context.newGroup) {\n                    if (hasColumns) {\n                        item += '<div class=\"k-cell k-group-cell\"><span>' + this.templates.groupTemplate(context.group) + '</span></div>';\n                    } else {\n                        item += '<div class=\"k-group\">' + this.templates.groupTemplate(context.group) + '</div>';\n                    }\n                } else if (isGrouped && hasColumns) {\n                    item += '<div class=\\'k-cell k-spacer-cell\\'></div>';\n                }\n                return item + '</li>';\n            },\n            _renderColumns: function (dataItem) {\n                var item = '';\n                for (var i = 0; i < this.options.columns.length; i++) {\n                    var currentWidth = this.options.columns[i].width;\n                    var currentWidthInt = parseInt(currentWidth, 10);\n                    var widthStyle = '';\n                    if (currentWidth && !isNaN(currentWidthInt)) {\n                        widthStyle += 'style=\\'width:';\n                        widthStyle += currentWidthInt;\n                        widthStyle += percentageUnitsRegex.test(currentWidth) ? '%' : 'px';\n                        widthStyle += ';\\'';\n                    }\n                    item += '<span class=\\'k-cell\\' ' + widthStyle + '>';\n                    item += this.templates['column' + i](dataItem);\n                    item += '</span>';\n                }\n                return item;\n            },\n            _render: function () {\n                var html = '';\n                var i = 0;\n                var idx = 0;\n                var context;\n                var dataContext = [];\n                var view = this.dataSource.view();\n                var values = this.value();\n                var group, newGroup, j;\n                var isGrouped = this.isGrouped();\n                if (isGrouped) {\n                    for (i = 0; i < view.length; i++) {\n                        group = view[i];\n                        newGroup = true;\n                        for (j = 0; j < group.items.length; j++) {\n                            context = {\n                                selected: this._selected(group.items[j], values),\n                                item: group.items[j],\n                                group: group.value,\n                                newGroup: newGroup,\n                                isLastGroupedItem: j === group.items.length - 1,\n                                index: idx\n                            };\n                            dataContext[idx] = context;\n                            idx += 1;\n                            html += this._renderItem(context);\n                            newGroup = false;\n                        }\n                    }\n                } else {\n                    for (i = 0; i < view.length; i++) {\n                        context = {\n                            selected: this._selected(view[i], values),\n                            item: view[i],\n                            index: i\n                        };\n                        dataContext[i] = context;\n                        html += this._renderItem(context);\n                    }\n                }\n                this._view = dataContext;\n                this.element[0].innerHTML = html;\n                if (isGrouped && dataContext.length) {\n                    this._renderHeader();\n                }\n            },\n            _selected: function (dataItem, values) {\n                var select = !this.isFiltered() || this.options.selectable === 'multiple';\n                return select && this._dataItemPosition(dataItem, values) !== -1;\n            },\n            setDSFilter: function (filter) {\n                this._lastDSFilter = extend({}, filter);\n            },\n            isFiltered: function () {\n                if (!this._lastDSFilter) {\n                    this.setDSFilter(this.dataSource.filter());\n                }\n                return !kendo.data.Query.compareFilters(this.dataSource.filter(), this._lastDSFilter);\n            },\n            refresh: function (e) {\n                var that = this;\n                var action = e && e.action;\n                var skipUpdateOnBind = that.options.skipUpdateOnBind;\n                var isItemChange = action === 'itemchange';\n                var result;\n                that.trigger('dataBinding');\n                that._angularItems('cleanup');\n                that._fixedHeader();\n                that._render();\n                that.bound(true);\n                if (isItemChange || action === 'remove') {\n                    result = mapChangedItems(that._dataItems, e.items);\n                    if (result.changed.length) {\n                        if (isItemChange) {\n                            that.trigger('selectedItemChange', { items: result.changed });\n                        } else {\n                            that.value(that._getValues(result.unchanged));\n                        }\n                    }\n                } else if (that.isFiltered() || that._skipUpdate || that._emptySearch) {\n                    that.focus(0);\n                    if (that._skipUpdate) {\n                        that._skipUpdate = false;\n                        that._selectedIndices = that._valueIndices(that._values, that._selectedIndices);\n                    }\n                } else if (!skipUpdateOnBind && (!action || action === 'add')) {\n                    that.value(that._values);\n                }\n                if (that._valueDeferred) {\n                    that._valueDeferred.resolve();\n                }\n                that._angularItems('compile');\n                that.trigger('dataBound');\n            },\n            bound: function (bound) {\n                if (bound === undefined) {\n                    return this._bound;\n                }\n                this._bound = bound;\n            },\n            isGrouped: function () {\n                return (this.dataSource.group() || []).length;\n            }\n        });\n        ui.plugin(StaticList);\n        function last(list) {\n            return list[list.length - 1];\n        }\n        function getSelectedOption(select) {\n            var index = select.selectedIndex;\n            return index > -1 ? select.options[index] : {};\n        }\n        function mapChangedItems(selected, itemsToMatch) {\n            var itemsLength = itemsToMatch.length;\n            var selectedLength = selected.length;\n            var dataItem;\n            var found;\n            var i, j;\n            var changed = [];\n            var unchanged = [];\n            if (selectedLength) {\n                for (i = 0; i < selectedLength; i++) {\n                    dataItem = selected[i];\n                    found = false;\n                    for (j = 0; j < itemsLength; j++) {\n                        if (dataItem === itemsToMatch[j]) {\n                            found = true;\n                            changed.push({\n                                index: i,\n                                item: dataItem\n                            });\n                            break;\n                        }\n                    }\n                    if (!found) {\n                        unchanged.push(dataItem);\n                    }\n                }\n            }\n            return {\n                changed: changed,\n                unchanged: unchanged\n            };\n        }\n        function isValidFilterExpr(expression) {\n            if (!expression || $.isEmptyObject(expression)) {\n                return false;\n            }\n            if (expression.filters && !expression.filters.length) {\n                return false;\n            }\n            return true;\n        }\n        function removeFiltersForField(expression, field) {\n            var filters;\n            var found = false;\n            if (expression.filters) {\n                filters = $.grep(expression.filters, function (filter) {\n                    found = removeFiltersForField(filter, field);\n                    if (filter.filters) {\n                        return filter.filters.length;\n                    } else {\n                        return filter.field != field;\n                    }\n                });\n                if (!found && expression.filters.length !== filters.length) {\n                    found = true;\n                }\n                expression.filters = filters;\n            }\n            return found;\n        }\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}