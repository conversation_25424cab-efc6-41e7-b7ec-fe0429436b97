{"version": 3, "sources": ["kendo.touch.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "Widget", "ui", "proxy", "abs", "Math", "MAX_DOUBLE_TAP_DISTANCE", "Touch", "extend", "init", "element", "options", "eventProxy", "name", "e", "that", "_triggerTouch", "gestureEventProxy", "trigger", "touches", "distance", "center", "event", "this", "fn", "call", "wrapper", "events", "UserEvents", "filter", "surface", "minHold", "multiTouch", "allowSelection", "fastTap", "press", "hold", "tap", "gesturestart", "gesturechange", "gestureend", "enableSwipe", "bind", "notify", "global", "minXDelta", "maxY<PERSON><PERSON><PERSON>", "maxDuration", "doubleTapTimeout", "cancel", "destroy", "type", "touch", "preventDefault", "_tap", "lastTap", "endTime", "touchDelta", "_dragstart", "_swipestart", "x", "velocity", "y", "sender", "capture", "_swipemove", "duration", "timeStamp", "startTime", "direction", "initialDelta", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,eACH,aACA,oBACDD,IACL,WAoIE,MAzHC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAMG,GAAGD,OAAQE,EAAQN,EAAEM,MAAOC,EAAMC,KAAKD,IAAKE,EAA0B,GAC3GC,EAAQN,EAAOO,QACfC,KAAM,SAAUC,EAASC,GAMrB,QAASC,GAAWC,GAChB,MAAO,UAAUC,GACbC,EAAKC,cAAcH,EAAMC,IAGjC,QAASG,GAAkBJ,GACvB,MAAO,UAAUC,GACbC,EAAKG,QAAQL,GACTM,QAASL,EAAEK,QACXC,SAAUN,EAAEM,SACZC,OAAQP,EAAEO,OACVC,MAAOR,EAAEQ,SAhBrB,GAAIP,GAAOQ,IACXtB,GAAOuB,GAAGf,KAAKgB,KAAKV,EAAML,EAASC,GACnCA,EAAUI,EAAKJ,QACfD,EAAUK,EAAKL,QACfK,EAAKW,QAAUhB,EAgBfK,EAAKY,OAAS,GAAI5B,GAAM6B,WAAWlB,GAC/BmB,OAAQlB,EAAQkB,OAChBC,QAASnB,EAAQmB,QACjBC,QAASpB,EAAQoB,QACjBC,WAAYrB,EAAQqB,WACpBC,gBAAgB,EAChBC,QAASvB,EAAQuB,QACjBC,MAAOvB,EAAW,cAClBwB,KAAMxB,EAAW,QACjByB,IAAKlC,EAAMY,EAAM,QACjBuB,aAAcrB,EAAkB,gBAChCsB,cAAetB,EAAkB,iBACjCuB,WAAYvB,EAAkB,gBAE9BN,EAAQ8B,aACR1B,EAAKY,OAAOe,KAAK,QAASvC,EAAMY,EAAM,gBACtCA,EAAKY,OAAOe,KAAK,OAAQvC,EAAMY,EAAM,iBAErCA,EAAKY,OAAOe,KAAK,QAASvC,EAAMY,EAAM,eACtCA,EAAKY,OAAOe,KAAK,OAAQ9B,EAAW,SACpCG,EAAKY,OAAOe,KAAK,MAAO9B,EAAW,aAEvCb,EAAM4C,OAAO5B,IAEjBY,QACI,aACA,YACA,OACA,UACA,MACA,YACA,OACA,QACA,eACA,gBACA,cAEJhB,SACIE,KAAM,QACNiB,QAAS,KACTc,QAAQ,EACRV,SAAS,EACTL,OAAQ,KACRG,YAAY,EACZS,aAAa,EACbI,UAAW,GACXC,UAAW,GACXC,YAAa,IACbhB,QAAS,IACTiB,iBAAkB,KAEtBC,OAAQ,WACJ1B,KAAKI,OAAOsB,UAEhBC,QAAS,WACLjD,EAAOuB,GAAG0B,QAAQzB,KAAKF,MACvBA,KAAKI,OAAOuB,WAEhBlC,cAAe,SAAUmC,EAAMrC,GACvBS,KAAKL,QAAQiC,GACTC,MAAOtC,EAAEsC,MACT9B,MAAOR,EAAEQ,SAEbR,EAAEuC,kBAGVC,KAAM,SAAUxC,GACZ,GAAIC,GAAOQ,KAAMgC,EAAUxC,EAAKwC,QAASH,EAAQtC,EAAEsC,KAC/CG,IAAWH,EAAMI,QAAUD,EAAQC,QAAUzC,EAAKJ,QAAQqC,kBAAoBjD,EAAM0D,WAAWL,EAAOG,GAASnC,SAAWd,GAC1HS,EAAKC,cAAc,YAAaF,GAChCC,EAAKwC,QAAU,OAEfxC,EAAKC,cAAc,MAAOF,GAC1BC,EAAKwC,QAAUH,IAGvBM,WAAY,SAAU5C,GAClBS,KAAKP,cAAc,YAAaF,IAEpC6C,YAAa,SAAU7C,GACK,EAApBV,EAAIU,EAAE8C,EAAEC,WAAiBzD,EAAIU,EAAEgD,EAAED,WACjC/C,EAAEiD,OAAOC,WAGjBC,WAAY,SAAUnD,GAClB,GAAIC,GAAOQ,KAAMZ,EAAUI,EAAKJ,QAASyC,EAAQtC,EAAEsC,MAAOc,EAAWpD,EAAEQ,MAAM6C,UAAYf,EAAMgB,UAAWC,EAAYjB,EAAMQ,EAAEU,aAAe,EAAI,QAAU,MACvJlE,GAAIgD,EAAMQ,EAAEU,eAAiB3D,EAAQkC,WAAazC,EAAIgD,EAAMU,EAAEQ,cAAgB3D,EAAQmC,WAAaoB,EAAWvD,EAAQoC,cACtHhC,EAAKG,QAAQ,SACTmD,UAAWA,EACXjB,MAAOtC,EAAEsC,QAEbA,EAAMH,YAIlBlD,GAAMG,GAAGqE,OAAOhE,IAClBP,OAAOD,MAAMyE,QACRxE,OAAOD,OACE,kBAAVH,SAAwBA,OAAO6E,IAAM7E,OAAS,SAAU8E,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.touch.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.touch', [\n        'kendo.core',\n        'kendo.userevents'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'touch',\n        name: 'Touch',\n        category: 'mobile',\n        description: 'The kendo Touch widget provides a cross-platform compatible API for handling user-initiated touch events, multi-touch gestures and event sequences (drag, swipe, etc.). ',\n        depends: [\n            'core',\n            'userevents'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, proxy = $.proxy, abs = Math.abs, MAX_DOUBLE_TAP_DISTANCE = 20;\n        var Touch = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.element;\n                that.wrapper = element;\n                function eventProxy(name) {\n                    return function (e) {\n                        that._triggerTouch(name, e);\n                    };\n                }\n                function gestureEventProxy(name) {\n                    return function (e) {\n                        that.trigger(name, {\n                            touches: e.touches,\n                            distance: e.distance,\n                            center: e.center,\n                            event: e.event\n                        });\n                    };\n                }\n                that.events = new kendo.UserEvents(element, {\n                    filter: options.filter,\n                    surface: options.surface,\n                    minHold: options.minHold,\n                    multiTouch: options.multiTouch,\n                    allowSelection: true,\n                    fastTap: options.fastTap,\n                    press: eventProxy('touchstart'),\n                    hold: eventProxy('hold'),\n                    tap: proxy(that, '_tap'),\n                    gesturestart: gestureEventProxy('gesturestart'),\n                    gesturechange: gestureEventProxy('gesturechange'),\n                    gestureend: gestureEventProxy('gestureend')\n                });\n                if (options.enableSwipe) {\n                    that.events.bind('start', proxy(that, '_swipestart'));\n                    that.events.bind('move', proxy(that, '_swipemove'));\n                } else {\n                    that.events.bind('start', proxy(that, '_dragstart'));\n                    that.events.bind('move', eventProxy('drag'));\n                    that.events.bind('end', eventProxy('dragend'));\n                }\n                kendo.notify(that);\n            },\n            events: [\n                'touchstart',\n                'dragstart',\n                'drag',\n                'dragend',\n                'tap',\n                'doubletap',\n                'hold',\n                'swipe',\n                'gesturestart',\n                'gesturechange',\n                'gestureend'\n            ],\n            options: {\n                name: 'Touch',\n                surface: null,\n                global: false,\n                fastTap: false,\n                filter: null,\n                multiTouch: false,\n                enableSwipe: false,\n                minXDelta: 30,\n                maxYDelta: 20,\n                maxDuration: 1000,\n                minHold: 800,\n                doubleTapTimeout: 800\n            },\n            cancel: function () {\n                this.events.cancel();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.events.destroy();\n            },\n            _triggerTouch: function (type, e) {\n                if (this.trigger(type, {\n                        touch: e.touch,\n                        event: e.event\n                    })) {\n                    e.preventDefault();\n                }\n            },\n            _tap: function (e) {\n                var that = this, lastTap = that.lastTap, touch = e.touch;\n                if (lastTap && touch.endTime - lastTap.endTime < that.options.doubleTapTimeout && kendo.touchDelta(touch, lastTap).distance < MAX_DOUBLE_TAP_DISTANCE) {\n                    that._triggerTouch('doubletap', e);\n                    that.lastTap = null;\n                } else {\n                    that._triggerTouch('tap', e);\n                    that.lastTap = touch;\n                }\n            },\n            _dragstart: function (e) {\n                this._triggerTouch('dragstart', e);\n            },\n            _swipestart: function (e) {\n                if (abs(e.x.velocity) * 2 >= abs(e.y.velocity)) {\n                    e.sender.capture();\n                }\n            },\n            _swipemove: function (e) {\n                var that = this, options = that.options, touch = e.touch, duration = e.event.timeStamp - touch.startTime, direction = touch.x.initialDelta > 0 ? 'right' : 'left';\n                if (abs(touch.x.initialDelta) >= options.minXDelta && abs(touch.y.initialDelta) < options.maxYDelta && duration < options.maxDuration) {\n                    that.trigger('swipe', {\n                        direction: direction,\n                        touch: e.touch\n                    });\n                    touch.cancel();\n                }\n            }\n        });\n        kendo.ui.plugin(Touch);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}