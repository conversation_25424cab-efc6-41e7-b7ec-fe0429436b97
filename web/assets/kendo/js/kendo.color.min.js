/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.color.min",["kendo.core.min"],e)}(function(){function e(e,t,r){void 0===r&&(r="0");for(var n=e.toString(16);t>n.length;)n=r+n;return n}function t(e,t,r){var n=r;return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function r(e,t){var n,a,s;if(null==e||"none"===e)return null;if(e instanceof o)return e;if(s=e.toLowerCase(),n=f(s))return s="transparent"===n[1]?new l(1,1,1,0):r(i[n[1]],t),s.match=[n[1]],s;if((n=/^#?([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})\b/i.exec(s))?a=new u(parseInt(n[1],16),parseInt(n[2],16),parseInt(n[3],16),1):(n=/^#?([0-9a-f])([0-9a-f])([0-9a-f])\b/i.exec(s))?a=new u(parseInt(n[1]+n[1],16),parseInt(n[2]+n[2],16),parseInt(n[3]+n[3],16),1):(n=/^rgb\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/.exec(s))?a=new u(parseInt(n[1],10),parseInt(n[2],10),parseInt(n[3],10),1):(n=/^rgba\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9.]+)\s*\)/.exec(s))?a=new u(parseInt(n[1],10),parseInt(n[2],10),parseInt(n[3],10),parseFloat(n[4])):(n=/^rgb\(\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*\)/.exec(s))?a=new l(parseFloat(n[1])/100,parseFloat(n[2])/100,parseFloat(n[3])/100,1):(n=/^rgba\(\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9.]+)\s*\)/.exec(s))&&(a=new l(parseFloat(n[1])/100,parseFloat(n[2])/100,parseFloat(n[3])/100,parseFloat(n[4]))),a)a.match=n;else if(!t)throw Error("Cannot parse color: "+s);return a}var n,a,i,s,f,o,l,u,h,d,c;window.kendo=window.kendo||{},n=kendo.Class,a=kendo.support,i={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgrey:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",grey:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"778899",lightslategrey:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"},s=a.browser,f=function(e){var t,r=Object.keys(i);return r.push("transparent"),t=RegExp("^("+r.join("|")+")(\\W|$)","i"),f=function(e){return t.exec(e)},t.exec(e)},o=n.extend({init:function(){},toHSV:function(){return this},toRGB:function(){return this},toHex:function(){return this.toBytes().toHex()},toBytes:function(){return this},toCss:function(){return"#"+this.toHex()},toCssRgba:function(){var e=this.toBytes();return"rgba("+e.r+", "+e.g+", "+e.b+", "+parseFloat((+this.a).toFixed(3))+")"},toDisplay:function(){return s.msie&&s.version<9?this.toCss():this.toCssRgba()},equals:function(e){return e===this||null!==e&&this.toCssRgba()===r(e).toCssRgba()},diff:function(e){var t,r;return null===e?NaN:(t=this.toBytes(),r=e.toBytes(),Math.sqrt(Math.pow(.3*(t.r-r.r),2)+Math.pow(.59*(t.g-r.g),2)+Math.pow(.11*(t.b-r.b),2)))},clone:function(){var e=this.toBytes();return e===this&&(e=new u(e.r,e.g,e.b,e.a)),e}}),l=o.extend({init:function(e,t,r,n){o.fn.init.call(this),this.r=e,this.g=t,this.b=r,this.a=n},toHSV:function(){var e,t,r=this,n=r.r,a=r.g,i=r.b,s=Math.min(n,a,i),f=Math.max(n,a,i),o=f-s,l=f;return 0===o?new h(0,0,l,this.a):(0!==f?(t=o/f,e=n===f?(a-i)/o:a===f?2+(i-n)/o:4+(n-a)/o,e*=60,e<0&&(e+=360)):(t=0,e=-1),new h(e,t,l,this.a))},toHSL:function(){var e,t,r,n=this,a=n.r,i=n.g,s=n.b,f=Math.max(a,i,s),o=Math.min(a,i,s),l=(f+o)/2;if(f===o)e=t=0;else switch(r=f-o,t=l>.5?r/(2-f-o):r/(f+o),f){case a:e=(i-s)/r+(i<s?6:0);break;case i:e=(s-a)/r+2;break;case s:e=(a-i)/r+4}return new d(60*e,100*t,100*l,this.a)},toBytes:function(){return new u(255*this.r,255*this.g,255*this.b,this.a)}}),u=l.extend({init:function(e,t,r,n){l.fn.init.call(this,Math.round(e),Math.round(t),Math.round(r),n)},toRGB:function(){return new l(this.r/255,this.g/255,this.b/255,this.a)},toHSV:function(){return this.toRGB().toHSV()},toHSL:function(){return this.toRGB().toHSL()},toHex:function(){return e(this.r,2)+e(this.g,2)+e(this.b,2)},toBytes:function(){return this}}),h=o.extend({init:function(e,t,r,n){o.fn.init.call(this),this.h=e,this.s=t,this.v=r,this.a=n},toRGB:function(){var e,t,r,n,a,i,s,f,o=this,u=o.h,h=o.s,d=o.v;if(0===h)e=t=r=d;else switch(u/=60,n=Math.floor(u),a=u-n,i=d*(1-h),s=d*(1-h*a),f=d*(1-h*(1-a)),n){case 0:e=d,t=f,r=i;break;case 1:e=s,t=d,r=i;break;case 2:e=i,t=d,r=f;break;case 3:e=i,t=s,r=d;break;case 4:e=f,t=i,r=d;break;default:e=d,t=i,r=s}return new l(e,t,r,this.a)},toHSL:function(){return this.toRGB().toHSL()},toBytes:function(){return this.toRGB().toBytes()}}),d=o.extend({init:function(e,t,r,n){o.fn.init.call(this),this.h=e,this.s=t,this.l=r,this.a=n},toRGB:function(){var e,r,n,a,i,s=this.h/360,f=this.s/100,o=this.l/100;return 0===f?e=r=n=o:(a=o<.5?o*(1+f):o+f-o*f,i=2*o-a,e=t(i,a,s+1/3),r=t(i,a,s),n=t(i,a,s-1/3)),new l(e,r,n,this.a)},toHSV:function(){return this.toRGB().toHSV()},toBytes:function(){return this.toRGB().toBytes()}}),c=n.extend({init:function(e){var t,r,n,a,i,s,f,o=this;if(1===arguments.length)for(t=c.formats,r=this.resolveColor(e),n=0;n<t.length;n++)a=t[n].re,i=t[n].process,s=a.exec(r),s&&(f=i(s),o.r=f[0],o.g=f[1],o.b=f[2]);else this.r=arguments[0],this.g=arguments[1],this.b=arguments[2];this.r=this.normalizeByte(this.r),this.g=this.normalizeByte(this.g),this.b=this.normalizeByte(this.b)},toHex:function(){var e=this.padDigit,t=this.r.toString(16),r=this.g.toString(16),n=this.b.toString(16);return"#"+e(t)+e(r)+e(n)},resolveColor:function(e){var t=e||"black";return"#"===t.charAt(0)&&(t=t.substr(1,6)),t=t.replace(/ /g,""),t=t.toLowerCase(),t=c.namedColors[t]||t},normalizeByte:function(e){return e<0||isNaN(e)?0:e>255?255:e},padDigit:function(e){return 1===e.length?"0"+e:e},brightness:function(e){var t=Math.round;return this.r=t(this.normalizeByte(this.r*e)),this.g=t(this.normalizeByte(this.g*e)),this.b=t(this.normalizeByte(this.b*e)),this},percBrightness:function(){return Math.sqrt(.241*this.r*this.r+.691*this.g*this.g+.068*this.b*this.b)}}),c.fromBytes=function(e,t,r,n){return new u(e,t,r,null!=n?n:1)},c.fromRGB=function(e,t,r,n){return new l(e,t,r,null!=n?n:1)},c.fromHSV=function(e,t,r,n){return new h(e,t,r,null!=n?n:1)},c.fromHSL=function(e,t,r,n){return new d(e,t,r,null!=n?n:1)},c.formats=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,process:function(e){return[parseInt(e[1],10),parseInt(e[2],10),parseInt(e[3],10)]}},{re:/^(\w{2})(\w{2})(\w{2})$/,process:function(e){return[parseInt(e[1],16),parseInt(e[2],16),parseInt(e[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,process:function(e){return[parseInt(e[1]+e[1],16),parseInt(e[2]+e[2],16),parseInt(e[3]+e[3],16)]}}],c.namedColors=i,kendo.deepExtend(kendo,{parseColor:r,Color:c})},"function"==typeof define&&define.amd?define:function(e,t,r){(r||t)()});
//# sourceMappingURL=kendo.color.min.js.map
