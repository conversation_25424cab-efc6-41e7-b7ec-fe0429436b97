{"version": 3, "sources": ["kendo.dataviz.gauge.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "pad", "bbox", "origin", "<PERSON><PERSON><PERSON><PERSON>", "getSize", "spacing", "getSpacing", "<PERSON><PERSON><PERSON><PERSON>", "x", "left", "y", "top", "setSize", "right", "bottom", "buildLabelElement", "label", "labelBox", "textBox", "children", "border", "background", "wrapper", "Path$1", "fromRect", "Rect", "x1", "y1", "stroke", "Text", "GeometryPoint", "font", "fill", "color", "styleGeometry", "clone", "padding", "styleBox", "dashType", "lineJoin", "lineCap", "elements", "Group", "append", "getRange", "range", "min", "max", "from", "defined", "constants", "MIN_VALUE", "to", "MAX_VALUE", "Math", "unpad", "renderAxisTick", "tickRenderOptions", "tickOptions", "start", "end", "tick<PERSON>ath", "position", "tickX", "tickY", "vertical", "Point", "Path$2", "moveTo", "lineTo", "renderTicks", "tickGroup", "tickPositions", "mirror", "lineBox", "count", "visible", "skip", "step", "skip<PERSON><PERSON><PERSON>", "x2", "drawTicks", "arc", "tickAngles", "unit", "tickStart", "tickEnd", "ticks", "Group$5", "center", "radius", "getRadiusX", "pointAt", "rotate", "Path$5", "rangeSegment", "opacity", "dataviz", "BLACK", "COORD_PRECISION", "services", "isArray", "setDefaultOptions", "NumericAxis", "limitValue", "Box", "interpolateV<PERSON>ue", "round", "drawing", "DrawingGroup", "DrawingPath", "Animation", "AnimationFactory", "geometry", "transform", "ANGULAR_SPEED", "LINEAR_SPEED", "ARROW", "ARROW_POINTER", "BAR_POINTER", "DEFAULT_HEIGHT", "DEFAULT_LINE_WIDTH", "DEFAULT_WIDTH", "DEGREE", "INSIDE", "LINEAR", "OUTSIDE", "RADIAL_POINTER", "RADIAL_RANGE_POINTER", "DEFAULT_MARGIN", "Path", "Surface", "Gauge", "Group$2", "LinearScale", "Pointer", "LinearPointer", "ArrowLinearPointerAnimation", "Point$1", "Path$3", "ArrowLinearPointer", "BarLinearPointerAnimation", "Group$3", "Path$4", "BarLinearPointer", "DEFAULT_MIN_WIDTH", "DEFAULT_MIN_HEIGHT", "Group$1", "LinearGauge", "GEO_ARC_ADJUST_ANGLE", "Arc", "RadialScale", "RadialPointerAnimation", "CAP_SIZE", "Circle", "Group$6", "Path$6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Group$4", "RadialGauge", "ArcScale", "MAX_DURATION", "RangePointerAnimation", "RangePointer", "ArcGauge", "PI", "element", "userOptions", "theme", "context", "contextService", "ChartService", "_originalOptions", "_initTheme", "redraw", "destroy", "surface", "surfaceElement", "pointerValue", "pointer", "pointers", "arguments", "_setValueOptions", "_draw", "clear", "draw", "_visuals", "exportVisual", "allValues", "values", "i$1", "isNumber", "concat", "arrayValues", "resize", "noTransitionsRedraw", "transitions", "_toggleTransitions", "_surfaceSize", "_initSurface", "gaugeArea", "_createGaugeArea", "_createModel", "_gauge<PERSON><PERSON><PERSON><PERSON><PERSON>", "reflow", "setOptions", "setDirection", "rtl", "type", "setIntlService", "intl", "currentTheme", "areaGeometry", "margin", "ref", "_surfaceElement", "elementSize", "renderAs", "create", "_getSize", "defaultSize", "_defaultSize", "this$1", "animation", "<PERSON><PERSON><PERSON>", "scale", "service", "scaleOptions", "reverse", "fn", "call", "minorUnit", "majorUnit", "initUserOptions", "labels", "autoMajorUnit", "initFields", "render", "renderLabels", "scaleLine", "renderLine", "scaleTicks", "ranges", "renderRanges", "slot", "slotX", "slotY", "rangeSize", "minorTicks", "getSlot", "y2", "linePath", "line", "majorTicks", "getMajorTickPositions", "getMinorTickPositions", "align", "_alignLines", "newValue", "_old<PERSON><PERSON><PERSON>", "repaint", "track", "pointerBox", "pointerRangeBox", "trackBox", "ref$1", "trackSize", "pointerHalfSize", "space", "shape", "getElementOptions", "_margin", "setup", "fromScale", "toScale", "axis", "duration", "distanceTo", "pos", "translateX", "translateY", "translate", "easing", "register", "pointerShape", "halfSize", "sign", "play", "elementOptions", "close", "Y", "X", "newPoints", "oldPoints", "abs", "speed", "_set", "setter", "toUpperCase", "points", "p2", "p3", "p4", "dir", "minSlot", "sizeAxis", "p1", "pointer<PERSON>ath", "oldShape", "group", "renderTrack", "trackOptions", "toRect", "bboxX", "bboxY", "_shrinkScaleWidth", "_getBox", "_alignElements", "_shrinkElements", "_buildVisual", "visuals", "scaleElements", "currentOptions", "pointerType", "boxCenter", "plotAreaBox", "wrap", "diff", "scaleBox", "overflow", "contentBox", "shrink", "alignTo", "renderArc", "labelElements", "reposition<PERSON><PERSON><PERSON>", "slotAngle", "result", "startAngle", "endAngle", "angle", "<PERSON><PERSON><PERSON><PERSON>", "ticksSize", "labelsOptions", "isInside", "hasLabelElements", "halfWidth", "halfHeight", "labelAngle", "lp", "cx", "cy", "labelPos", "labelElement", "prevLabelPos", "labelTransform", "labelsGroup", "rangeDistance", "setRadiusX", "setRadiusY", "cos", "sin", "union", "rangeRadius", "newRadius", "getRangeRadius", "_geometry", "segment", "segments", "rangeSegments", "segmentsCount", "createRange", "rangeGeom", "radiusX", "radiusY", "defaultColor", "j", "rangePlaceholderColor", "majorTickSize", "allTicks", "minorTickSize", "tickArc", "majorTickAngles", "_tickDifference", "minorTickAngles", "normalizeTickAngles", "angles", "splice", "ring", "<PERSON><PERSON><PERSON><PERSON>", "positions", "tickCount", "repositionTicks", "minor", "newPoint", "xDiff", "yDiff", "anchor", "animationOptions", "newAngle", "oldAngle", "setAngle", "_renderNeedle", "_renderCap", "capSize", "cap", "fromPoints", "needleColor", "needlePath", "capColor", "circle", "arrow", "_initialPlotArea", "fitScale", "alignScale", "_buildPointers", "minDiff", "midDiff", "maxDiff", "mid", "oldDiff", "getDiff", "staleFlag", "getPlotBox", "scaleArc", "plotBbox", "plotBox", "plotBoxCenter", "paddingX", "paddingY", "pointersArr", "rangeLineCap", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "geometry$$1", "setEndAngle", "setStartAngle", "addRange", "startColor", "currentColor", "Color", "r", "g", "b", "toHex", "abort", "idx", "ref$2", "rangeColor", "colors", "currentValue", "centerLabelPosition", "yLimit", "bottomRight", "themeOptions", "themes", "ui", "themeName", "lowerName", "toLowerCase", "SASS_THEMES", "indexOf", "autoTheme", "gauge", "createProxyMember", "name", "_instance", "apply", "createExportMethod", "ArcGaugeWidget", "method", "_centerElement", "drawDOM", "then", "visual", "RadialGaugeWidget", "LinearGaugeWidget", "exportMethods", "Widget", "empty", "_createInstance", "addClass", "notify", "_copyFields", "gaugeType", "_gaugeType", "_resize", "proxyMembers", "ExportMixin", "css", "_centerTemplate", "instance", "template", "centerElement", "centerTemplate", "_getCenterElement", "html", "remove", "plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,8BACH,aACA,cACA,gBACA,sBACDD,IACL,YACG,SAAUE,GAwCP,QAASkF,GAAIC,EAAMtC,GAAnB,GACQuC,GAASD,EAAKE,YACd9C,EAAO4C,EAAKG,UACZC,EAAUC,EAAW3C,EASzB,OARAsC,GAAKM,WACDL,EAAOM,EAAIH,EAAQI,KACnBP,EAAOQ,EAAIL,EAAQM,MAEvBV,EAAKW,SACDvD,EAAKlB,OAASkE,EAAQI,KAAOJ,EAAQQ,OACrCxD,EAAKjB,QAAUiE,EAAQM,IAAMN,EAAQS,UAElCb,EAKX,QAASc,GAAkBC,EAAOzC,GAAlC,GACQ0C,GAAWD,EAAMlC,IACjBoC,EAAUF,EAAMG,SAAS,GAAGrC,IAC5BsC,EAAS7C,EAAQ6C,WACjBC,EAAa9C,EAAQ8C,YAAc,GACnCC,EAAUC,EAAOC,SAAS,GAAIC,IAC9BR,EAASS,GACTT,EAASU,KAETV,EAAS9E,QACT8E,EAAS7E,YACPwF,YACF5G,EAAO,GAAI6G,GAAKb,EAAMhG,KAAM,GAAI8G,GAAcZ,EAAQQ,GAAIR,EAAQS,KAClEI,KAAMxD,EAAQwD,KACdC,MAAQC,MAAO1D,EAAQ0D,SAEvBC,EAAgBlC,EAAIhF,EAAKiF,OAAOkC,QAAS5D,EAAQ6D,SACjDC,EAAWd,EAAOC,SAASU,GAC3BN,QACIK,MAAOb,EAAOjF,MAAQiF,EAAOa,MAAQ,GACrC9F,MAAOiF,EAAOjF,MACdmG,SAAUlB,EAAOkB,SACjBC,SAAU,QACVC,QAAS,SAEbR,MAAQC,MAAOZ,KAEfoB,EAAW,GAAIC,EAInB,OAHAD,GAASE,OAAOrB,GAChBmB,EAASE,OAAON,GAChBI,EAASE,OAAO3H,GACTyH,EAEX,QAASG,GAASC,EAAOC,EAAKC,GAA9B,GACQC,GAAOC,EAAQJ,EAAMG,MAAQH,EAAMG,KAAOE,EAAUC,UACpDC,EAAKH,EAAQJ,EAAMO,IAAMP,EAAMO,GAAKF,EAAUG,SAGlD,OAFAR,GAAMG,KAAOM,KAAKP,IAAIO,KAAKR,IAAIM,EAAIJ,GAAOF,GAC1CD,EAAMO,GAAKE,KAAKR,IAAIQ,KAAKP,IAAIK,EAAIJ,GAAOD,GACjCF,EAEX,QAASU,GAAMtD,EAAMtC,GACjB,GAAI0C,GAAUC,EAAW3C,EAKzB,OAJA0C,GAAQI,MAAQJ,EAAQI,KACxBJ,EAAQM,KAAON,EAAQM,IACvBN,EAAQQ,OAASR,EAAQQ,MACzBR,EAAQS,QAAUT,EAAQS,OACnBd,EAAIC,EAAMI,GAgOrB,QAASmD,GAAeC,EAAmBC,GAA3C,GAIQC,GAAOC,EAQPC,EAXAC,EAAWL,EAAkBK,SAC7BC,EAAQN,EAAkBM,MAC1BC,EAAQP,EAAkBO,KAe9B,OAbIP,GAAkBQ,UAClBN,EAAQ,GAAIO,IAAMH,EAAOD,GACzBF,EAAM,GAAIM,IAAMH,EAAQL,EAAYrG,KAAMyG,KAE1CH,EAAQ,GAAIO,IAAMJ,EAAUE,GAC5BJ,EAAM,GAAIM,IAAMJ,EAAUE,EAAQN,EAAYrG,OAE9CwG,EAAW,GAAIM,KACfvC,QACIK,MAAOyB,EAAYzB,MACnB9F,MAAOuH,EAAYvH,SAExBiI,OAAOT,GAAOU,OAAOT,GAG5B,QAASU,GAAYC,EAAWC,EAAef,EAAmBC,GAAlE,GAGYe,GACAC,EACK5I,EAJT6I,EAAQH,EAAcxI,MAC1B,IAAI0H,EAAYkB,QAGZ,IAFIH,EAAShB,EAAkBgB,OAC3BC,EAAUjB,EAAkBiB,QACvB5I,EAAI4H,EAAYmB,KAAM/I,EAAI6I,EAAO7I,GAAK4H,EAAYoB,KACnDhJ,EAAI4H,EAAYqB,WAAa,IAGjCtB,EAAkBM,MAAQU,EAASC,EAAQM,GAAKN,EAAQM,GAAKtB,EAAYrG,KACzEoG,EAAkBO,MAAQS,EAASC,EAAQ/C,GAAK+B,EAAYrG,KAAOqH,EAAQ/C,GAC3E8B,EAAkBK,SAAWU,EAAc1I,GAC3CyI,EAAU5B,OAAOa,EAAeC,EAAmBC,KAumB/D,QAASuB,GAAUC,EAAKC,EAAYC,EAAM1B,GAA1C,GAKiB5H,GACDuJ,EACAC,EANRC,EAAQ,GAAIC,IACZC,EAASP,EAAIO,OACbC,EAASR,EAAIS,YACjB,IAAIjC,EAAYkB,QACZ,IAAS9I,EAAI,EAAGA,EAAIqJ,EAAWnJ,OAAQF,IAC/BuJ,EAAYH,EAAIU,QAAQT,EAAWrJ,IACnCwJ,EAAU,GAAIxD,GAAc2D,EAAOjF,EAAIkF,EAAShC,EAAYrG,KAAMoI,EAAO/E,GAAGmF,OAAOV,EAAWrJ,GAAI2J,GACtGF,EAAM5C,OAAO,GAAImD,KACblE,QACIK,MAAOyB,EAAYzB,MACnB9F,MAAOuH,EAAYvH,SAExBiI,OAAOiB,GAAWhB,OAAOiB,GAGpC,OAAOC,GAEX,QAASQ,GAAa/C,EAAMI,EAAInB,EAAO+D,GACnC,OACIhD,KAAMA,EACNI,GAAIA,EACJnB,MAAOA,EACP+D,QAASA,GAr+BpB,GAEOC,GACA3F,EACA2C,EACAC,EACAgD,EACAC,EACAC,EACA1G,EACA2G,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAxF,EACAK,EACAoF,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAeAtF,EACAnB,EACAM,EAiDAoG,EACAC,EACAC,EACAC,EAwNAjE,GACAkE,GACAnE,GAqCAoE,GAiIAC,GA+BAC,GAmFAC,GAgCAC,GACAC,GACAC,GA+DAC,GA2BAC,GACAC,GACAC,GA2GAC,GACAC,GACAC,GACAC,GAgIAC,GACAC,GACAxD,GACAN,GA2BA+D,GA0UAC,GAiBAC,GACAC,GACAC,GACAC,GACAC,GA+EAC,GACAC,GAqIAC,GA4CAC,GACAC,GAiCAC,GA4FAC,EArsDJrN,QAAOC,MAAMiJ,QAAUlJ,OAAOC,MAAMiJ,YAChCA,EAAUjJ,MAAMiJ,QAChB3F,EAAa2F,EAAQ3F,WACrB2C,EAAUgD,EAAQhD,QAClBC,EAAY+C,EAAQ/C,UACpBgD,EAAQhD,EAAUgD,MAClBC,EAAkBjD,EAAUiD,gBAC5BC,EAAWH,EAAQG,SACnB1G,EAAauG,EAAQvG,WACrB2G,EAAUJ,EAAQI,QAClBC,EAAoBL,EAAQK,kBAC5BC,EAAcN,EAAQM,YACtBC,EAAaP,EAAQO,WACrBC,EAAMR,EAAQQ,IACdC,EAAmBT,EAAQS,iBAC3BC,EAAQV,EAAQU,MAChBC,EAAU5J,MAAM4J,QAChBC,EAAeD,EAAQlE,MACvBoE,EAAcF,EAAQsB,KACtBnB,EAAYH,EAAQG,UACpBC,EAAmBJ,EAAQI,iBAC3BC,EAAWjK,MAAMiK,SACjBxF,EAAOwF,EAASxF,KAChBK,EAAgBmF,EAAS/C,MACzBgD,EAAYD,EAASC,UACrBC,EAAgB,IAChBC,EAAe,IACfC,EAAQ,QACRC,EAAgB,eAChBC,EAAc,aACdC,EAAiB,IACjBC,EAAqB,GACrBC,EAAgB,IAChBC,EAASrE,KAAK+G,GAAK,IACnBzC,EAAS,SACTC,EAAS,SACTC,EAAU,UACVC,EAAiB,gBACjBC,EAAuB,qBAevBtF,EAAQmE,EACRtF,EAASuF,EACTjF,EAAO+E,EAAQ/E,KAiDfoG,EAAiB,EACjBC,EAAOpB,EACPqB,EAAUvB,EAAQuB,QAClBC,EAAQnC,EAAQ/I,MAAMC,QACtBC,KAAM,SAAUkN,EAASC,EAAaC,EAAOC,GACzB,SAAZA,IACAA,MAEJnN,KAAKgN,QAAUA,EACfhN,KAAKkN,MAAQA,EACblN,KAAKoN,eAAiB,GAAItE,GAASuE,aAAarN,KAAMmN,GACtDnN,KAAKsN,iBAAmBlL,KAAepC,KAAKiB,QAASgM,GACrDjN,KAAKiB,QAAUmB,KAAepC,KAAKsN,kBACnCtN,KAAKuN,WAAWL,GAChBlN,KAAKwN,UAETC,QAAS,WACDzN,KAAK0N,UACL1N,KAAK0N,QAAQD,UACbzN,KAAK0N,QAAU,YAEZ1N,MAAKgN,cACLhN,MAAK2N,gBAEhBtN,MAAO,SAAUuN,GACb,GAAIC,GAAU7N,KAAK8N,SAAS,EAC5B,OAAyB,KAArBC,UAAUrP,OACHmP,EAAQxN,SAEnBwN,EAAQxN,MAAMuN,OACd5N,MAAKgO,iBAAiBJ,KAE1BK,MAAO,WACH,GAAIP,GAAU1N,KAAK0N,OACnBA,GAAQQ,QACRR,EAAQS,KAAKnO,KAAKoO,WAEtBC,aAAc,WACV,MAAOrO,MAAKoO,UAEhBE,UAAW,SAAUC,GAAV,GAIM/P,GAMAgQ,EATTV,EAAW9N,KAAK8N,SAChBQ,IACJ,IAAyB,IAArBP,UAAUrP,OAAc,CACxB,IAASF,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IACjC8P,EAAUnQ,KAAK2P,EAAStP,GAAG6B,QAE/B,OAAOiO,GAEX,GAAIvF,EAAQwF,GACR,IAASC,EAAM,EAAGA,EAAMD,EAAO7P,OAAQ8P,IAC/B7F,EAAQ8F,SAASF,EAAOC,KACxBV,EAASU,GAAKnO,MAAMkO,EAAOC,GAIvCxO,MAAKgO,iBAAiBO,IAE1BP,iBAAkB,SAAUO,GAAV,GAGL/P,GAFLsP,KAAcY,OAAO1O,KAAKiB,QAAQ4M,SAClCc,KAAiBD,OAAOH,EAC5B,KAAS/P,EAAI,EAAGA,EAAImQ,EAAYjQ,OAAQF,IACpCsP,EAAStP,GAAG6B,MAAQsO,EAAYnQ,IAGxCoQ,OAAQ,WACJ5O,KAAK6O,uBAETA,oBAAqB,WACjB,GAAIC,GAAc9O,KAAKiB,QAAQ6N,WAC/B9O,MAAK+O,oBAAmB,GACxB/O,KAAKwN,SACLxN,KAAK+O,mBAAmBD,IAE5BtB,OAAQ,WAAA,GAYA7K,GAXA5C,EAAOC,KAAKgP,eACZhL,EAAU,GAAIG,IACd,EACA,IAEApE,EAAKlB,MACLkB,EAAKjB,QAETkB,MAAKiP,eACLjP,KAAKkP,UAAYlP,KAAKmP,mBACtBnP,KAAKoP,eACDzM,EAAOsD,EAAMjC,EAAQrB,OAAQ3C,KAAKqP,kBACtCrP,KAAKsP,OAAO3M,IAEhB4M,WAAY,SAAUtO,EAASiM,GAC3BlN,KAAKsN,iBAAmBlL,EAAWpC,KAAKsN,iBAAkBrM,GAC1DjB,KAAKiB,QAAUmB,KAAepC,KAAKsN,kBACnCtN,KAAKuN,WAAWL,GAChBlN,KAAKwN,UAETgC,aAAc,SAAUC,GACpBzP,KAAKoN,eAAeqC,MAAcA,EAC9BzP,KAAK0N,SAAiC,QAAtB1N,KAAK0N,QAAQgC,OAC7B1P,KAAK0N,QAAQD,UACbzN,KAAK0N,QAAU,OAGvBiC,eAAgB,SAAUC,GACtB5P,KAAKoN,eAAewC,KAAOA,GAE/BrC,WAAY,SAAUL,GAAV,GAIJjM,GACA4M,EAEIC,EACKtP,EAPTqR,EAAe3C,GAASlN,KAAKkN,SAKjC,IAJAlN,KAAKkN,MAAQ2C,EACb7P,KAAKiB,QAAUmB,KAAeyN,EAAc7P,KAAKiB,SAC7CA,EAAUjB,KAAKiB,QACf4M,EAAU5M,EAAQ4M,QAClB9E,EAAQ8E,GAAU,CAElB,IADIC,KACKtP,EAAI,EAAGA,EAAIqP,EAAQnP,OAAQF,IAChCsP,EAAS3P,KAAKiE,KAAeyN,EAAahC,QAASA,EAAQrP,IAE/DyC,GAAQ4M,QAAUC,IAG1BqB,iBAAkB,WAAA,GAeVD,GAdAjO,EAAUjB,KAAKiB,QAAQiO,UACvBnP,EAAOC,KAAK0N,QAAQ3N,OACpB+D,EAAS7C,EAAQ6C,WACjBgM,EAAe,GAAI3L,IACnB,EACA,IAEApE,EAAKlB,MACLkB,EAAKjB,QAgBT,OAdAkB,MAAKqP,iBAAmBpO,EAAQ8O,QAAUpF,EACtC7G,EAAOjF,MAAQ,IACfiR,EAAe7J,EAAM6J,EAAchM,EAAOjF,QAE1CqQ,EAAYtE,EAAK1G,SAAS4L,GAC1BxL,QACIK,MAAOb,EAAOjF,MAAQiF,EAAOa,MAAQ,GACrC9F,MAAOiF,EAAOjF,MACdmG,SAAUlB,EAAOkB,SACjBC,SAAU,QACVC,QAAS,SAEbR,MAAQC,MAAO1D,EAAQ8C,eAI/BkL,aAAc,WAAA,GACNe,GAAMhQ,KACNiB,EAAU+O,EAAI/O,QACdyM,EAAUsC,EAAItC,QACdV,EAAUhN,KAAKiQ,kBACflQ,EAAOC,KAAKgP,cAChBrG,GAAQuH,YAAYlD,EAASjN,GACxB2N,GAAWA,EAAQzM,QAAQyO,OAASzO,EAAQkP,UAM7CnQ,KAAK0N,QAAQQ,QACblO,KAAK0N,QAAQkB,WANTlB,GACAA,EAAQD,UAEZzN,KAAK0N,QAAU7C,EAAQuF,OAAOpD,GAAW0C,KAAMzO,EAAQkP,aAM/DnB,aAAc,WAAA,GACN/N,GAAUjB,KAAKiB,QACflB,EAAOC,KAAKqQ,UAIhB,OAHIpP,GAAQiO,WACR9M,EAAWrC,EAAMkB,EAAQiO,WAEtBnP,GAEXkQ,gBAAiB,WAKb,MAJKjQ,MAAK2N,iBACN3N,KAAK2N,eAAiB7M,SAASC,cAAc,OAC7Cf,KAAKgN,QAAQpL,YAAY5B,KAAK2N,iBAE3B3N,KAAK2N,gBAEhB7K,QAAS,WACL,MAAO9C,MAAKqQ,YAEhBA,SAAU,WAAA,GACFrD,GAAUhN,KAAKgN,QACfsD,EAActQ,KAAKuQ,eACnB1R,EAAQmO,EAAQlL,YAChBhD,EAASkO,EAAQjL,YAOrB,OANKlD,KACDA,EAAQyR,EAAYzR,OAEnBC,IACDA,EAASwR,EAAYxR,SAGrBD,MAAOA,EACPC,OAAQA,IAGhByR,aAAc,WACV,OACI1R,MAAOuL,EACPtL,OAAQoL,IAGhB6E,mBAAoB,SAAU1O,GAAV,GAGP7B,GAFLgS,EAASxQ,IAEb,KADAA,KAAKiB,QAAQ6N,YAAczO,EAClB7B,EAAI,EAAGA,EAAIwB,KAAK8N,SAASpP,OAAQF,IACtCgS,EAAO1C,SAAStP,GAAGyC,QAAQwP,UAAU3B,YAAczO,KAI/D2I,EAAkB8B,GACd4F,YACAxD,MAAO,UACPiD,SAAU,GACVtC,WACA8C,SACAzB,eAEArI,GAAS2C,EACTuB,GAAUxB,EACV3C,GAAQpC,EAqCRwG,GAAc/B,EAAYpJ,QAC1BC,KAAM,SAAUmB,EAAS2P,GACrB,GAAIC,GAAe5P,OACd0E,EAAQkL,EAAaC,UAAYD,EAAalK,YAAa,IAAUiK,OAAenB,MACrFoB,EAAerT,EAAEqC,UAAWgR,GAAgBC,SAAS,KAEzD7H,EAAY8H,GAAGjR,KAAKkR,KAAKhR,KAAM,EAAG,EAAG6Q,EAAcD,GACnD5Q,KAAKiB,QAAQgQ,UAAYjR,KAAKiB,QAAQgQ,WAAajR,KAAKiB,QAAQiQ,UAAY,IAEhFC,gBAAiB,SAAUlQ,GACvB,GAAI4P,GAAezO,KAAepC,KAAKiB,QAASA,EAGhD,OAFA4P,GAAezO,KAAeyO,GAAgBO,QAAUjK,OAAQ0J,EAAa1J,UAC7E0J,EAAaK,UAAYL,EAAaK,WAAavI,EAAQ0I,cAAcR,EAAarL,IAAKqL,EAAapL,KACjGoL,GAEXS,WAAY,aAEZC,OAAQ,WAAA,GACApM,GAAWnF,KAAKmF,SAAW,GAAI4F,IAC/BqG,EAASpR,KAAKwR,eACdC,EAAYzR,KAAK0R,aACjBC,EAAa3R,KAAKgH,cAClB4K,EAAS5R,KAAK6R,cAElB,OADA1M,GAASE,OAAOoM,EAAWL,EAAQO,EAAYC,GACxCzM,GAEX0M,aAAc,WAAA,GAWDrT,GACD+G,EACAuM,EACAC,EACAC,EAdJxB,EAASxQ,KACTiB,EAAUjB,KAAKiB,QACfuE,EAAMvE,EAAQuE,IACdC,EAAMxE,EAAQwE,IACdkB,EAAW1F,EAAQ0F,SACnBQ,EAASlG,EAAQmQ,OAAOjK,OACxByK,EAAS3Q,EAAQ2Q,WACjBzM,EAAW,GAAI4F,IACf1D,EAAQuK,EAAOlT,OACfuT,EAAYhR,EAAQgR,WAAahR,EAAQiR,WAAWnS,KAAO,CAC/D,KAASvB,EAAI,EAAGA,EAAI6I,EAAO7I,IACnB+G,EAAQD,EAASsM,EAAOpT,GAAIgH,EAAKC,GACjCqM,EAAOtB,EAAO2B,QAAQ5M,EAAMG,KAAMH,EAAMO,IACxCiM,EAAQpL,EAAW6J,EAAOpJ,UAAY0K,EACtCE,EAAQrL,EAAWmL,EAAOtB,EAAOpJ,UACjCT,EACAoL,EAAM3N,IAAM6N,GAAa9K,KAAc,GAEvC6K,EAAMI,IAAMH,GAAa9K,KAAc,GAE3ChC,EAASE,OAAOwB,GAAO3C,SAAS,GAAIC,IAChC4N,EAAM3N,GACN4N,EAAM3N,KAEN0N,EAAMrK,GAAKqK,EAAM3N,GACjB4N,EAAMI,GAAKJ,EAAM3N,MAEjBK,MACIC,MAAOY,EAAMZ,MACb+D,QAASnD,EAAMmD,SAEnBpE,YAGR,OAAOa,IAEXqM,aAAc,WAAA,GAKDhT,GAJLwR,EAAMhQ,KACNoR,EAASpB,EAAIoB,OACbnQ,EAAU+O,EAAI/O,QACdkE,EAAW,GAAI4F,GACnB,KAASvM,EAAI,EAAGA,EAAI4S,EAAO1S,OAAQF,IAC/B2G,EAASE,OAAO5B,EAAkB2N,EAAO5S,GAAIyC,EAAQmQ,QAEzD,OAAOjM,IAEXuM,WAAY,WAAA,GAKAW,GAJJC,EAAOtS,KAAKiB,QAAQqR,KACpBlL,EAAUpH,KAAKoH,UACfjC,EAAW,GAAI4F,GAYnB,OAXIuH,GAAKzT,MAAQ,GAAKyT,EAAKhL,UACnB+K,EAAW,GAAIxL,KACfvC,QACIK,MAAO2N,EAAK3N,MACZK,SAAUsN,EAAKtN,SACfnG,MAAOyT,EAAKzT,SAGpBwT,EAASvL,OAAOM,EAAQhD,GAAIgD,EAAQ/C,IAAI0C,OAAOK,EAAQM,GAAIN,EAAQgL,IACnEjN,EAASE,OAAOgN,IAEblN,GAEX6B,YAAa,WAAA,GACLiB,GAAQ,GAAI8C,IACZ9J,EAAUjB,KAAKiB,QACfiQ,EAAYjQ,EAAQsR,WAAWjL,QAAUrG,EAAQiQ,UAAY,EAC7D/K,GACAQ,SAAU1F,EAAQ0F,SAClBQ,OAAQlG,EAAQmQ,OAAOjK,OACvBC,QAASpH,KAAKoH,UAIlB,OAFAJ,GAAYiB,EAAOjI,KAAKwS,wBAAyBrM,EAAmBlF,EAAQsR,YAC5EvL,EAAYiB,EAAOjI,KAAKyS,wBAAyBtM,EAAmB/D,MAAiBqF,SAAUyJ,EAAYjQ,EAAQgQ,WAAahQ,EAAQiR,aACjIjK,KAGfe,EAAkBgC,IACdxF,IAAK,EACLC,IAAK,GACL8M,YACIxS,KAAM,GACN2S,MAAOpI,EACP3F,MAAOiE,EACP/J,MAAOsL,EACP7C,SAAS,GAEb4K,YACInS,KAAM,GACN2S,MAAOpI,EACP3F,MAAOiE,EACP/J,MAAOsL,EACP7C,SAAS,GAEbgL,MAAQzT,MAAOsL,GACfiH,QACI5K,SAAU8D,EACVxF,QAAS,GAEbqC,QAAQ,EACRwL,aAAa,IAEb1H,GAAUtC,EAAQ/I,MAAMC,QACxBC,KAAM,SAAU6Q,EAAO1D,GAAjB,GACE+C,GAAMW,EAAM1P,QACZuE,EAAMwK,EAAIxK,IACVC,EAAMuK,EAAIvK,IACVxE,EAAUjB,KAAKiB,QAAUmB,KAAepC,KAAKiB,QAASgM,EAC1DhM,GAAQyD,KAAOzD,EAAQ0D,MACvB3E,KAAK2Q,MAAQA,EAET1P,EAAQZ,MADRsF,EAAQ1E,EAAQZ,OACA6I,EAAWjI,EAAQZ,MAAOmF,EAAKC,GAE/BD,GAGxBnF,MAAO,SAAUuS,GAAV,GAMC5C,GACAxK,EACAC,EAPAxE,EAAUjB,KAAKiB,QACfZ,EAAQY,EAAQZ,KACpB,OAAyB,KAArB0N,UAAUrP,OACH2B,GAEP2P,EAAMhQ,KAAK2Q,MAAM1P,QACjBuE,EAAMwK,EAAIxK,IACVC,EAAMuK,EAAIvK,IACdxE,EAAQ4R,UAAYlN,EAAQ1E,EAAQ4R,WAAa5R,EAAQZ,MAAQmF,EACjEvE,EAAQZ,MAAQ6I,EAAW0J,EAAUpN,EAAKC,QACtCzF,KAAKmF,UACLnF,KAAK8S,eAIjB9J,EAAkBiC,IAAWtG,MAAOiE,IAChCsC,GAAgBD,GAAQpL,QACxBC,KAAM,SAAU6Q,EAAO1P,GACnBgK,GAAQ8F,GAAGjR,KAAKkR,KAAKhR,KAAM2Q,EAAO1P,GAClCjB,KAAKiB,QAAUmB,GAAa2Q,OAASzL,QAAS3B,EAAQ1E,EAAQ8R,SAAY/S,KAAKiB,UAEnFqO,OAAQ,WAAA,GAYA0D,GAAYC,EAAiBC,EAX7BlD,EAAMhQ,KACNiB,EAAU+O,EAAI/O,QACd0P,EAAQX,EAAIW,MACZwC,EAAQxC,EAAM1P,QACdkG,EAASgM,EAAMhM,OACfR,EAAWwM,EAAMxM,SACjB8K,EAAYd,EAAMvJ,UAClBgM,EAAYnS,EAAQ8R,MAAMhT,MAAQkB,EAAQlB,KAC1CsT,EAAkBpS,EAAQlB,KAAO,EACjCgQ,EAAS/M,EAAW/B,EAAQ8O,QAC5BuD,EAAQ3M,EAAWoJ,EAAO5I,EAAS,OAAS,SAAW4I,EAAO5I,EAAS,SAAW,MAEtFmM,GAAQnM,GAAUmM,EAAQA,EACtB3M,GACAuM,EAAW,GAAI/J,GAAIsI,EAAUrN,GAAKkP,EAAO7B,EAAUpN,GAAIoN,EAAUrN,GAAKkP,EAAO7B,EAAUW,IACnFjL,EACA+L,EAAS9O,IAAMgP,EAEfF,EAASxL,IAAM0L,EAEfnS,EAAQsS,QAAUtJ,IAClBgJ,EAAkB,GAAI9J,GAAIsI,EAAU/J,GAAK4L,EAAO7B,EAAUpN,GAAKgP,EAAiB5B,EAAU/J,GAAK4L,EAAO7B,EAAUW,GAAKiB,GACrHL,EAAaC,KAGjBC,EAAW,GAAI/J,GAAIsI,EAAUrN,GAAIqN,EAAUpN,GAAKiP,EAAO7B,EAAU/J,GAAI+J,EAAUpN,GAAKiP,GAChFnM,EACA+L,EAASd,IAAMgB,EAEfF,EAAS7O,IAAM+O,EAEfnS,EAAQsS,QAAUtJ,IAClBgJ,EAAkB,GAAI9J,GAAIsI,EAAUrN,GAAKiP,EAAiB5B,EAAUpN,GAAKiP,EAAO7B,EAAU/J,GAAK2L,EAAiB5B,EAAUpN,GAAKiP,GAC/HN,EAAaC,IAGrBjT,KAAKkT,SAAWA,EAChBlT,KAAKiT,gBAAkBA,EACvBjT,KAAKwB,IAAMwR,GAAcE,EAASrO,QAAQnC,IAAIzB,EAAQ6C,OAAOjF,QAEjE2U,kBAAmB,WACf,GAAIvS,GAAUjB,KAAKiB,OACnB,QACIyD,MACIC,MAAO1D,EAAQ0D,MACf+D,QAASzH,EAAQyH,SAErBpE,OAAQqB,EAAQ1E,EAAQ6C,SACpBa,MAAO1D,EAAQ6C,OAAOjF,MAAQoC,EAAQ6C,OAAOa,OAAS1D,EAAQ0D,MAAQ,GACtE9F,MAAOoC,EAAQ6C,OAAOjF,MACtBmG,SAAU/D,EAAQ6C,OAAOkB,SACzB0D,QAASzH,EAAQyH,SACjB,OAGZ+K,QAAS,WAAA,GACDzD,GAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ1P,EAAU+O,EAAI/O,QACdkS,EAAQxC,EAAM1P,QACdkG,EAASgM,EAAMhM,OACfR,EAAWwM,EAAMxM,SACjBoJ,EAAS/M,EAAW/B,EAAQ8O,QAC5BuD,EAAQ3M,EAAWoJ,EAAO5I,EAAS,OAAS,SAAW4I,EAAO5I,EAAS,SAAW,MACtF,OAAOmM,MAGftK,EAAkBkC,IACdqI,MAAOtJ,EACP8I,OAASjP,QAAUjF,MAAO,IAC1B8F,MAAOiE,EACP9E,QAAUjF,MAAO,GACjB6J,QAAS,EACTqH,OAAQ/M,EAAW,GACnByN,WAAaf,KAAMzF,GACnB3C,SAAS,IAET6D,GAA8B1B,EAAU5J,QACxC6T,MAAO,WAAA,GAcCC,GACAC,EAdA3S,EAAUjB,KAAKiB,QACf8O,EAAS9O,EAAQ8O,OACjBrK,EAAOzE,EAAQyE,KACfI,EAAK7E,EAAQ6E,GACba,EAAW1F,EAAQ0F,SACnBkN,EAAOlN,EAAW,KAAO,IACzB1F,GAAQkG,SAAWR,GACnBjB,EAAKmO,IAAS9D,EACdjK,EAAG+N,IAAS9D,IAEZrK,EAAKmO,IAAS9D,EACdjK,EAAG+N,IAAS9D,GAEZ4D,EAAY3T,KAAK2T,UAAY,GAAInP,GAAckB,EAAKtB,GAAIsB,EAAKrB,IAC7DuP,EAAU5T,KAAK4T,QAAU,GAAIpP,GAAcsB,EAAG1B,GAAI0B,EAAGzB,IAChC,IAArBpD,EAAQ6S,WACR7S,EAAQ6S,SAAW9N,KAAKP,IAAIkO,EAAUI,WAAWH,GAAW3S,EAAQ6S,SAAW,IAAM,KAG7FtM,KAAM,SAAUwM,GAAV,GACEC,GAAa7K,EAAiBpJ,KAAK2T,UAAUzQ,EAAGlD,KAAK4T,QAAQ1Q,EAAG8Q,GAChEE,EAAa9K,EAAiBpJ,KAAK2T,UAAUvQ,EAAGpD,KAAK4T,QAAQxQ,EAAG4Q,EACpEhU,MAAKgN,QAAQpD,UAAUA,IAAYuK,UAAUF,EAAYC,OAGjElL,EAAkBmC,IACdiJ,OAAQ7J,EACRuJ,SAAUhK,IAEdJ,EAAiBtK,QAAQiV,SAASrK,EAAemB,IAC7CC,GAAU5G,EACV6G,GAAS7B,EACT8B,GAAqBJ,GAAcrL,QACnCC,KAAM,SAAU6Q,EAAO1P,GACnBiK,GAAc6F,GAAGjR,KAAKkR,KAAKhR,KAAM2Q,EAAO1P,GACnC0E,EAAQ3F,KAAKiB,QAAQlB,QACtBC,KAAKiB,QAAQlB,KAA4C,GAArCC,KAAK2Q,MAAM1P,QAAQsR,WAAWxS,OAG1DuU,aAAc,WAAA,GAMNf,GALAvD,EAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ5Q,EAAOiQ,EAAI/O,QAAQlB,KACnBwU,EAAWxU,EAAO,EAClByU,EAAO7D,EAAM1P,QAAQkG,UAAc,CAevC,OAZIoM,GADA5C,EAAM1P,QAAQ0F,UAEV,GAAIyE,IAAQ,EAAG,EAAImJ,GACnB,GAAInJ,IAAQ,EAAIoJ,EAAOzU,EAAM,GAC7B,GAAIqL,IAAQ,EAAG,EAAImJ,KAInB,GAAInJ,IAAQ,EAAImJ,EAAU,GAC1B,GAAInJ,IAAQ,EAAG,EAAIoJ,EAAOzU,GAC1B,GAAIqL,IAAQ,EAAImJ,EAAU,KAKtCzB,QAAS,WAAA,GACD9C,GAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ1P,EAAU+O,EAAI/O,QACdwP,EAAY,GAAItF,IAA4BnL,KAAKmF,SAAU/C,EAAWnB,EAAQwP,WAC9E9J,SAAUgK,EAAM1P,QAAQ0F,SACxBQ,OAAQwJ,EAAM1P,QAAQkG,OACtB4I,OAAQ/P,KAAKyT,QAAQxS,EAAQ8O,QAC7BrK,KAAMiL,EAAMwB,QAAQlR,EAAQ4R,WAC5B/M,GAAI6K,EAAMwB,QAAQlR,EAAQZ,SAE1BY,GAAQwP,UAAU3B,eAAgB,IAClC2B,EAAUxP,QAAQ6S,SAAW,GAEjCrD,EAAUiD,QACVjD,EAAUgE,QAEdlD,OAAQ,WAAA,GAOApM,GAIA2M,EAVA9B,EAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ1P,EAAU+O,EAAI/O,QACdyT,EAAiB1U,KAAKwT,oBACtBD,EAAQvT,KAAKsU,aAAarT,EAAQZ,MAStC,OARAY,GAAQwP,UAAUf,KAAO1F,EACrB7E,EAAW,GAAIkG,KACf/G,OAAQoQ,EAAepQ,OACvBI,KAAMgQ,EAAehQ,OACtBoC,OAAOyM,EAAM,IAAIxM,OAAOwM,EAAM,IAAIxM,OAAOwM,EAAM,IAAIoB,QAClD7C,EAAOnB,EAAMwB,QAAQlR,EAAQZ,OACjC8E,EAASyE,UAAUA,IAAYuK,UAAUrC,EAAK1N,GAAI0N,EAAKzN,KACvDrE,KAAKmF,SAAWA,EACTA,KAGXoG,GAA4B9B,EAAU5J,QACtC6T,MAAO,WAAA,GACCzS,GAAUjB,KAAKiB,QACf4S,EAAO7T,KAAK6T,KAAO5S,EAAQ0F,SAAWf,EAAUgP,EAAIhP,EAAUiP,EAC9D/O,EAAK9F,KAAK8F,GAAK7E,EAAQ6T,UAAU,GAAGjB,GACpCnO,EAAO1F,KAAK0F,KAAOzE,EAAQ8T,UAAU,GAAGlB,EACnB,KAArB5S,EAAQ6S,WACR7S,EAAQ6S,SAAW9N,KAAKP,IAAIO,KAAKgP,IAAIlP,EAAKJ,GAAQzE,EAAQgU,MAAQ,IAAM,IAE5EjV,KAAKkV,KAAKxP,IAEd8B,KAAM,SAAUwM,GACZ,GAAI3T,GAAQ+I,EAAiBpJ,KAAK0F,KAAM1F,KAAK8F,GAAIkO,EACjDhU,MAAKkV,KAAK7U,IAEd6U,KAAM,SAAU7U,GAAV,GACE8U,GAAS,MAAQnV,KAAK6T,KAAKuB,cAC3BC,EAASrV,KAAKiB,QAAQ6T,SAC1BO,GAAO,GAAGF,GAAQ9U,GAClBgV,EAAO,GAAGF,GAAQ9U,MAG1B2I,EAAkBuC,IACd6I,OAAQ7J,EACR0K,MAAOnL,IAEXJ,EAAiBtK,QAAQiV,SAASpK,EAAasB,IAC3CC,GAAUjC,EACVkC,GAASjC,EACTkC,GAAmBR,GAAcrL,QACjCC,KAAM,SAAU6Q,EAAO1P,GACnBiK,GAAc6F,GAAGjR,KAAKkR,KAAKhR,KAAM2Q,EAAO1P,GACnC0E,EAAQ3F,KAAKiB,QAAQlB,QACtBC,KAAKiB,QAAQlB,KAA4C,GAArCC,KAAK2Q,MAAM1P,QAAQsR,WAAWxS,OAG1DuU,aAAc,SAAUjU,GAAV,GAiBNiV,GAUAC,EACAC,EA3BAxF,EAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ1P,EAAU+O,EAAI/O,QACdkS,EAAQxC,EAAM1P,QACdkG,EAASgM,EAAMhM,OACfR,EAAWwM,EAAMxM,SACjB8O,EAAMtO,IAAWR,KAAgB,EACjC5G,EAAOkB,EAAQlB,KAAO0V,EACtBC,EAAU/E,EAAMwB,QAAQxB,EAAM1P,QAAQuE,KACtCsM,EAAOnB,EAAMwB,QAAQ9R,GACrBwT,EAAOlN,EAAWf,EAAUgP,EAAIhP,EAAUiP,EAC1Cc,EAAWhP,EAAWf,EAAUiP,EAAIjP,EAAUgP,EAC9C7E,EAAS/P,KAAKyT,UAAYgC,EAC1BG,EAAK,GAAIpR,EAsBb,OArBAoR,GAAG/B,GAAQ6B,EAAQ7B,EAAO,KAC1B+B,EAAGD,GAAYD,EAAQC,EAAW,KAC9BL,EAAK,GAAI9Q,GACb8Q,EAAGzB,GAAQ/B,EAAK+B,EAAO,KACvByB,EAAGK,GAAY7D,EAAK6D,EAAW,KAC3BhP,GACAiP,EAAGzB,UAAUpE,EAAQ,GACrBuF,EAAGnB,UAAUpE,EAAQ,KAErB6F,EAAGzB,UAAU,EAAGpE,GAChBuF,EAAGnB,UAAU,EAAGpE,IAEhBwF,EAAKD,EAAGzQ,QACR2Q,EAAKI,EAAG/Q,QACR8B,GACA4O,EAAGpB,UAAUpU,EAAM,GACnByV,EAAGrB,UAAUpU,EAAM,KAEnBwV,EAAGpB,UAAU,EAAGpU,GAChByV,EAAGrB,UAAU,EAAGpU,KAGhB6V,EACAN,EACAC,EACAC,IAGR1C,QAAS,WAAA,GAQDrC,GAPAT,EAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ1P,EAAU+O,EAAI/O,QACdsS,EAAQvT,KAAKsU,aAAarT,EAAQZ,OAClCwV,EAAc7V,KAAK6V,YACnBC,EAAW9V,KAAKsU,aAAarT,EAAQ4R,UACzCgD,GAAY/O,OAAOyM,EAAM,IAAIxM,OAAOwM,EAAM,IAAIxM,OAAOwM,EAAM,IAAIxM,OAAOwM,EAAM,IAAIoB,QAC5ElE,EAAY,GAAIlF,IAA0BsK,EAAazT,EAAWnB,EAAQwP,WAC1EK,QAASH,EAAM1P,QAAQ6P,QACvBnK,SAAUgK,EAAM1P,QAAQ0F,SACxBoO,WACIe,EAAS,GACTA,EAAS,IAEbhB,WACIvB,EAAM,GACNA,EAAM,OAGVtS,EAAQwP,UAAU3B,eAAgB,IAClC2B,EAAUxP,QAAQ6S,SAAW,GAEjCrD,EAAUiD,QACVjD,EAAUgE,QAEdlD,OAAQ,WAAA,GAMA1D,GALAkI,EAAQ,GAAIvK,IACZkJ,EAAiB1U,KAAKwT,mBAU1B,OATIxT,MAAKiB,QAAQ8R,MAAMzL,SACnByO,EAAM1Q,OAAOrF,KAAKgW,eAElBnI,EAAU7N,KAAK6V,YAAc,GAAIpK,KACjCnH,OAAQoQ,EAAepQ,OACvBI,KAAMgQ,EAAehQ,OAEzBqR,EAAM1Q,OAAOwI,GACb7N,KAAKmF,SAAW4Q,EACTA,GAEXC,YAAa,WAAA,GACLC,GAAejW,KAAKiB,QAAQ8R,MAC5BjP,EAASmS,EAAanS,WACtBoP,EAAWlT,KAAKkT,SAASrO,QAAQnC,IAAIoB,EAAOjF,OAAS,EACzD,OAAO,IAAI4M,IAAOvH,SAASgP,EAASgD,UAChCxR,MACIC,MAAOsR,EAAatR,MACpB+D,QAASuN,EAAavN,SAE1BpE,QACIK,MAAOb,EAAOjF,MAAQiF,EAAOa,OAASsR,EAAatR,MAAQ,GAC3D9F,MAAOiF,EAAOjF,MACdmG,SAAUlB,EAAOkB,eAK7B2G,GAAoB,GACpBC,GAAqB,GACrBC,GAAUtC,EACVuC,GAAchB,EAAMjL,QACpByP,OAAQ,SAAU3M,GAAV,GAOKnE,GANLsP,EAAW9N,KAAK8N,SAChBqI,EAAQxT,EAAKC,OAAOM,EACpBkT,EAAQzT,EAAKC,OAAOQ,EACpB5B,EAAM,GAAI2H,GAAIgN,EAAOC,EAAOD,EAAQxT,EAAK9D,QAASuX,EAAQzT,EAAK7D,SAGnE,KAFAkB,KAAK2Q,MAAMrB,OAAO9N,GAClBxB,KAAKqW,kBAAkB7U,GACdhD,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IACjCsP,EAAStP,GAAG8Q,QAEhBtP,MAAK2C,KAAO3C,KAAKsW,QAAQ9U,GACzBxB,KAAKuW,iBACLvW,KAAKwW,kBACLxW,KAAKyW,eACLzW,KAAKiO,SAETwI,aAAc,WAAA,GAMDjY,GACDY,EANJsX,EAAU,GAAI7K,IACd8K,EAAgB3W,KAAK2Q,MAAMY,SAC3BzD,EAAW9N,KAAK8N,QAGpB,KAFA4I,EAAQrR,OAAOrF,KAAKkP,WACpBwH,EAAQrR,OAAOsR,GACNnY,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IAC7BY,EAAU0O,EAAStP,GACvBkY,EAAQrR,OAAOjG,EAAQmS,UACvBnS,EAAQiB,MAAMjB,EAAQ6B,QAAQZ,MAElCL,MAAKoO,SAAWsI,GAEpBtH,aAAc,WAAA,GAKNtB,GAEKtP,EACDoY,EACAC,EARJrG,EAASxQ,KACTiB,EAAUjB,KAAKiB,QACf0P,EAAQ3Q,KAAK2Q,MAAQ,GAAI3F,IAAY/J,EAAQ0P,MAAO3Q,KAAKoN,eAI7D,KAHApN,KAAK8N,YACDA,EAAW7M,EAAQ4M,QACvBC,EAAW/E,EAAQ+E,GAAYA,GAAYA,GAClCtP,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IAC7BoY,EAAiBxU,KAAe0L,EAAStP,IAAMiS,WAAa3B,YAAa7N,EAAQ6N,eACjF+H,EAAcD,EAAerD,QAAUxJ,EAAQuB,GAAqBI,GACxE8E,EAAO1C,SAAS3P,KAAK,GAAI0Y,GAAYlG,EAAOiG,KAGpDrG,aAAc,WACV,GAAI5J,GAAW3G,KAAKiB,QAAQ0P,MAAMhK,QAClC,QACI9H,MAAO8H,EAAWgF,GAAoBvB,EACtCtL,OAAQ6H,EAAWuD,EAAiB0B,KAG5C0K,QAAS,SAAU9U,GAAV,GAMIhD,GAGLuB,EARAiQ,EAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ7C,EAAWkC,EAAIlC,SACfgJ,EAAYtV,EAAI2G,SAChB4O,EAAcjJ,EAAS,GAAGtM,IAAIqD,QAAQmS,KAAKrG,EAAMnP,IACrD,KAAShD,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IACjCuY,EAAYC,KAAKlJ,EAAStP,GAAGgD,IAAIqD,QAUrC,OAPI8L,GAAM1P,QAAQ0F,UACd5G,EAAOgX,EAAYlY,QAAU,EAC7BkY,EAAc,GAAI5N,GAAI2N,EAAU5T,EAAInD,EAAMyB,EAAI6C,GAAIyS,EAAU5T,EAAInD,EAAMyB,EAAI4Q,MAE1ErS,EAAOgX,EAAYjY,SAAW,EAC9BiY,EAAc,GAAI5N,GAAI3H,EAAI4C,GAAI0S,EAAU1T,EAAIrD,EAAMyB,EAAIkG,GAAIoP,EAAU1T,EAAIrD,IAErEgX,GAEXR,eAAgB,WAAA,GAQH/X,GAGLyY,EAQKzI,EAlBLgC,EAASxQ,KACTgQ,EAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ7C,EAAWkC,EAAIlC,SACfoJ,EAAWvG,EAAMnP,IACjBA,EAAMsM,EAAS,GAAGtM,IAAIqD,QAAQmS,KAAKrG,EAAMnP,KACzCuV,EAAc/W,KAAK2C,IACvB,KAASnE,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IACjCgD,EAAIwV,KAAKlJ,EAAStP,GAAGgD,IAAIqD,QAU7B,KAPI8L,EAAM1P,QAAQ0F,UACdsQ,EAAOF,EAAY5O,SAASjF,EAAI1B,EAAI2G,SAASjF,EAC7CyN,EAAMrB,OAAO,GAAInG,GAAI+N,EAAS9S,GAAK6S,EAAMF,EAAY1S,GAAI6S,EAASxP,GAAKuP,EAAMF,EAAY3E,OAEzF6E,EAAOF,EAAY5O,SAAS/E,EAAI5B,EAAI2G,SAAS/E,EAC7CuN,EAAMrB,OAAO,GAAInG,GAAI+N,EAAS9S,GAAI8S,EAAS7S,GAAK4S,EAAMC,EAASxP,GAAIwP,EAAS9E,GAAK6E,KAE5EzI,EAAM,EAAGA,EAAMV,EAASpP,OAAQ8P,IACrCV,EAASU,GAAKc,OAAOkB,EAAO7N,OAGpC0T,kBAAmB,SAAU1T,GAAV,GAIPwU,GAHJnH,EAAMhQ,KACN2Q,EAAQX,EAAIW,KACXA,GAAM1P,QAAQ0F,WACXwQ,EAAWxG,EAAMyG,aAAavY,QAAU8D,EAAK9D,QAC7CsY,EAAW,IACXxG,EAAMnP,IAAI6V,OAAOF,EAAU,GAC3BxG,EAAMnP,IAAI8V,QAAQ3U,EAAM,UACxBgO,EAAMrB,OAAOqB,EAAMnP,QAI/BgV,gBAAiB,WAAA,GAQJhY,GAMAgQ,EAbLgC,EAASxQ,KACTgQ,EAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ7C,EAAWkC,EAAIlC,SACfoJ,EAAWvG,EAAMnP,IAAIqD,QACrBmP,EAAMrD,EAAM1P,QAAQ0F,SAAW,IAAM,IACrCqM,EAAalF,EAAS,GAAGtM,GAC7B,KAAShD,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IACjCwU,EAAWgE,KAAKlJ,EAAStP,GAAGgD,IAAIqD,QAKpC,KAHAqS,EAASlD,EAAM,IAAMhO,KAAKP,IAAIyR,EAASlD,EAAM,GAAKhB,EAAWgB,EAAM,GAAI,GACvEkD,EAASlD,EAAM,IAAMhO,KAAKP,IAAIuN,EAAWgB,EAAM,GAAKkD,EAASlD,EAAM,GAAI,GACvErD,EAAMrB,OAAO4H,GACJ1I,EAAM,EAAGA,EAAMV,EAASpP,OAAQ8P,IACrCV,EAASU,GAAKc,OAAOkB,EAAO7N,SAIxCqG,EAAkB8C,IACdgD,aAAa,EACbI,WAAanL,WAAY,IACzB4M,OAAShK,UAAU,KAEnBoF,GAAuB,IACvBC,GAAM1C,EAAQ0C,IACdxD,GAASgB,EACTtB,GAAUqB,EA2BV0C,GAAchD,EAAYpJ,QAC1BC,KAAM,SAAUmB,EAAS2P,GACrB3H,EAAY8H,GAAGjR,KAAKkR,KAAKhR,KAAM,EAAG,EAAGiB,EAAS2P,IAElDO,gBAAiB,SAAUlQ,GACvB,GAAI4P,GAAezO,KAAepC,KAAKiB,QAASA,EAGhD,OAFA4P,GAAaK,UAAYL,EAAaK,WAAavI,EAAQ0I,cAAcR,EAAarL,IAAKqL,EAAapL,KACxGoL,EAAaI,UAAYJ,EAAaI,WAAaJ,EAAaK,UAAY,GACrEL,GAEXS,WAAY,aAEZC,OAAQ,SAAUpJ,EAAQC,GACtB,GAAIR,GAAM5H,KAAKuX,UAAUpP,EAAQC,EACjCpI,MAAK2C,KAAOiF,EAAIjF,OAChB3C,KAAKwX,cAAgBxX,KAAKwR,eAC1BxR,KAAKiI,MAAQjI,KAAKgH,cAClBhH,KAAK4R,OAAS5R,KAAK6R,gBAEvBvC,OAAQ,SAAU3M,GAAV,GACAwF,GAASxF,EAAKwF,SACdC,EAASpC,KAAKR,IAAI7C,EAAK7D,SAAU6D,EAAK9D,SAAW,CACrD,OAAI8G,GAAQ3F,KAAK2C,OACb3C,KAAK2C,KAAO3C,KAAK4H,IAAIjF,OACrB3C,KAAKoI,OAAOpI,KAAK4H,IAAIS,cACrBrI,KAAKyX,mBACLzX,KAAKwR,eAHLxR,QAKOA,KAAKuR,OAAOpJ,EAAQC,IAGnCsP,UAAW,SAAUrX,GAAV,GAQHsX,GAPA3H,EAAMhQ,KAAKiB,QACXuE,EAAMwK,EAAIxK,IACVC,EAAMuK,EAAIvK,IACVqL,EAAUd,EAAIc,QACd8G,EAAa5H,EAAI4H,WACjBC,EAAW7H,EAAI6H,SACfC,EAAQD,EAAWD,CAOvB,OAJID,GADA7G,EACS+G,GAAYxX,EAAQmF,IAAQC,EAAMD,GAAOsS,GAExCzX,EAAQmF,IAAQC,EAAMD,GAAOsS,EAAQF,EAE5CD,EAAS5L,IAEpBgM,UAAW,WACP,GAAInG,GAAS5R,KAAKiB,QAAQ2Q,MAC1B,OAAOA,IAAUA,EAAOlT,QAE5BsZ,UAAW,WAAA,GACHhI,GAAMhQ,KAAKiB,QACXsR,EAAavC,EAAIuC,WACjBL,EAAalC,EAAIkC,WACjBnS,EAAO,CAOX,OANIwS,GAAWjL,UACXvH,EAAOwS,EAAWxS,MAElBmS,EAAW5K,UACXvH,EAAOiG,KAAKP,IAAIyM,EAAWnS,KAAMA,IAE9BA,GAEXyR,aAAc,WAAA,GAcNyG,GACAC,EACAC,EAQA/G,EACA/J,EACAvC,EACKtG,EACDkF,EACA0U,EACAC,EACAP,EACAQ,EACAC,EACAC,EACAC,EAEAC,EACAC,EAMIC,EACAC,EA5CRrI,EAASxQ,KACTiB,EAAUjB,KAAKiB,QACf2G,EAAM5H,KAAK4H,IAAI/C,QACfuD,EAASR,EAAIS,aACbR,EAAa7H,KAAK6H,WAAWD,EAAK3G,EAAQiQ,WAC1Ce,EAAYhR,EAAQgR,UAAYhR,EAAQgR,WAAsB,GAAT7J,EACrD0Q,EAAc,GAAI5Q,IAClB6Q,EAAyB,IAAT3Q,CAmBpB,KAlBIzC,EAAQ1E,EAAQ8X,eAChBA,EAAgB9X,EAAQ8X,cAExB9X,EAAQ8X,cAAgBA,EAExBd,EAAgBhX,EAAQmQ,OACxB8G,EAAWD,EAAczR,WAAa8D,EACtC6N,EAAmBxS,EAAQ3F,KAAKwX,eAChCU,IACA9P,GAAUpI,KAAKgY,YACXhY,KAAK+X,cAAgBI,IACrB/P,GAAU6J,EAAY8G,GAE1BnR,EAAIoR,WAAW5Q,GAAQ6Q,WAAW7Q,IAElCgJ,EAASpR,KAAKoR,OACd/J,EAAQ+J,EAAO1S,OACfoG,EAAUmT,EAAcnT,QACnBtG,EAAI,EAAGA,EAAI6I,EAAO7I,IACnBkF,EAAQ0N,EAAO5S,GACf4Z,EAAY1U,EAAMlC,IAAI3C,QAAU,EAChCwZ,EAAa3U,EAAMlC,IAAI1C,SAAW,EAClCgZ,EAAQjQ,EAAWrJ,GACnB8Z,GAAcR,EAAQ/L,IAAwB1B,EAC9CkO,EAAK3Q,EAAIU,QAAQwP,GACjBU,EAAKD,EAAGrV,EAAI8C,KAAKkT,IAAIZ,IAAeF,EAAYtT,IAAYoT,EAAW,MACvEO,EAAKF,EAAGnV,EAAI4C,KAAKmT,IAAIb,IAAeD,EAAavT,IAAYoT,EAAW,MAC5ExU,EAAM4L,OAAO,GAAInG,GAAIqP,EAAKJ,EAAWK,EAAKJ,EAAYG,EAAKJ,EAAWK,EAAKJ,IACvEK,EAAW,GAAIlU,GAAcd,EAAMlC,IAAI4C,GAAIV,EAAMlC,IAAI6C,IACrDsU,EAAe,OACdR,GAIDQ,EAAenI,EAAOgH,cAAc3T,SAASrF,GACzCoa,EAAeD,EAAahW,OAAOC,OACnCiW,EAAiBF,EAAa/O,aAAeA,IACjDiP,EAAe1E,UAAUuE,EAASxV,EAAI0V,EAAa1V,EAAGwV,EAAStV,EAAIwV,EAAaxV,GAChFuV,EAAa/O,UAAUiP,KAPvBF,EAAelV,EAAkBC,EAAOzC,EAAQmQ,QAChD0H,EAAYzT,OAAOsT,IAQvBnI,EAAO7N,KAAOwB,EAAKiV,MAAM5I,EAAO7N,KAAMgW,EAAahW,OAEvD,OAAOmW,IAEXrB,iBAAkB,WAAA,GAGNzH,GACA+I,EACA9G,EACAoH,EAIAC,EACK9a,EAVToT,EAAS5R,KAAK4R,OAAO/N,QACzB,IAAI+N,EAAOlT,OAAS,EAAG,CASnB,IARIsR,EAAMhQ,KAAKiB,QACX8X,EAAgB/I,EAAI+I,cACpB9G,EAAYjC,EAAIiC,UAChBoH,EAAcrZ,KAAKuZ,iBACnBvZ,KAAKiB,QAAQmQ,OAAO5K,WAAa8D,IACjC+O,GAAepH,EAAY8G,GAE3BO,EAAYD,EAAcpH,EAAY,EACjCzT,EAAI,EAAGA,EAAIoT,EAAOlT,OAAQF,IAC/BoT,EAAOpT,GAAGgb,UAAUR,WAAWM,GAAWL,WAAWK,EAEzDtZ,MAAK2C,KAAOwB,EAAKiV,MAAMpZ,KAAK2C,KAAM3C,KAAK4R,OAAOjP,UAGtDkP,aAAc,WAAA,GAMF7B,GACAiC,EACAnB,EACAiI,EACAM,EAEK7a,EACDib,EACA/T,EACAI,EAdR0K,EAASxQ,KACT0Z,EAAW1Z,KAAK2Z,gBAChBC,EAAgBF,EAAShb,OACzBiZ,EAAS,GAAIzP,GACjB,IAAI0R,EAOA,IANI5J,EAAMhQ,KAAKiB,QACXgR,EAAYjC,EAAIiC,UAChBnB,EAAUd,EAAIc,QACdiI,EAAgB/I,EAAI+I,cACpBM,EAAcrZ,KAAKuZ,iBACvBvZ,KAAKoI,OAAOpI,KAAKoI,SAAW6J,EAAY8G,GAC/Bva,EAAI,EAAGA,EAAIob,EAAepb,IAC3Bib,EAAUC,EAASlb,GACnBkH,EAAO8K,EAAOkH,UAAU+B,EAAQ3I,EAAU,KAAO,SACjDhL,EAAK0K,EAAOkH,UAAU+B,EAAS3I,EAAiB,OAAP,OACzChL,EAAKJ,IAAS,GACdiS,EAAOtS,OAAOmL,EAAOqJ,YAAYnU,EAAMI,EAAIuT,EAAaI,GAIpE,OAAO9B,IAEXkC,YAAa,SAAUjC,EAAYC,EAAUwB,EAAapY,GAA7C,GACLgR,GAAYjS,KAAKiB,QAAQgR,UACzB6H,EAAY,GAAInQ,GAASqC,IAAIhM,KAAK4H,IAAIO,QACtC4R,QAASV,EAAcpH,EAAY,EACnC+H,QAASX,EAAcpH,EAAY,EACnC2F,WAAYA,EACZC,SAAUA,GAEd,OAAO,IAAI7L,IAAI8N,GACXxV,QACIzF,MAAOoT,EACPtN,MAAO1D,EAAQ0D,MACf+D,QAASzH,EAAQyH,QACjBxD,QAASjE,EAAQiE,YAI7ByU,cAAe,WAAA,GAMHnU,GACAC,EACAwU,EAEKzb,EACD+G,EACAqU,EACKM,EACDT,EAbZxY,EAAUjB,KAAKiB,QACf2Q,EAAS3Q,EAAQ2Q,WACjBvK,EAAQuK,EAAOlT,OACfgb,IACJ,IAAIrS,EAKA,IAJI7B,EAAMvE,EAAQuE,IACdC,EAAMxE,EAAQwE,IACdwU,EAAehZ,EAAQkZ,sBAC3BT,EAASvb,KAAKsK,EAAajD,EAAKC,EAAKwU,IAC5Bzb,EAAI,EAAGA,EAAI6I,EAAO7I,IAGvB,IAFI+G,EAAQD,EAASsM,EAAOpT,GAAIgH,EAAKC,GACjCmU,EAAgBF,EAAShb,OACpBwb,EAAI,EAAGA,EAAIN,EAAeM,IAE/B,GADIT,EAAUC,EAASQ,GACnBT,EAAQ/T,MAAQH,EAAMG,MAAQH,EAAMG,MAAQ+T,EAAQ3T,GAAI,CACxD4T,EAASvb,KAAKsK,EAAalD,EAAMG,KAAMH,EAAMO,GAAIP,EAAMZ,MAAOY,EAAMmD,UAChE+Q,EAAQ/T,MAAQH,EAAMO,IAAMP,EAAMO,IAAM2T,EAAQ3T,IAChD4T,EAASvb,KAAKsK,EAAalD,EAAMO,GAAI2T,EAAQ3T,GAAImU,EAAc1U,EAAMmD,UAEzE+Q,EAAQ3T,GAAKP,EAAMG,IACnB,OAKhB,MAAOgU,IAEXH,eAAgB,WAAA,GAORnR,GANA4H,EAAMhQ,KACN4H,EAAMoI,EAAIpI,IACV3G,EAAU+O,EAAI/O,QACdgR,EAAYhR,EAAQgR,UACpB8G,EAAgB9X,EAAQ8X,cACxBqB,EAAgBnZ,EAAQsR,WAAWxS,IAOvC,OAJIqI,GADAnH,EAAQmQ,OAAO5K,WAAagE,EACnB5C,EAAIS,aAAe+R,EAAgBrB,EAAgB9G,EAEnDrK,EAAIS,aAAe4J,GAIpCsF,UAAW,SAAUpP,EAAQC,GAAlB,GACHnH,GAAUjB,KAAKiB,QACf2G,EAAM5H,KAAK4H,IAAM,GAAI+B,GAASqC,IAAI7D,GAClC4R,QAAS3R,EACT4R,QAAS5R,EACTwP,WAAY3W,EAAQ2W,WAAa7L,GACjC8L,SAAU5W,EAAQ4W,SAAW9L,IAEjC,OAAOnE,IAEXZ,YAAa,WAAA,GAOLqT,GAEAD,EACAE,EAGIlS,EAZJ4H,EAAMhQ,KACN4H,EAAMoI,EAAIpI,IACV3G,EAAU+O,EAAI/O,QACdsZ,EAAU3S,EAAI/C,OAelB,OAdA7E,MAAKwa,gBAAkBxa,KAAK6H,WAAWD,EAAK3G,EAAQiQ,WACpDlR,KAAKuS,WAAa5K,EAAU4S,EAASva,KAAKwa,gBAAiBvZ,EAAQiQ,UAAWjQ,EAAQsR,YAClF8H,EAAW,GAAInS,IACnBmS,EAAShV,OAAOrF,KAAKuS,YACjB6H,EAAgBnZ,EAAQsR,WAAWxS,KACnCua,EAAgBrZ,EAAQiR,WAAWnS,KACvCC,KAAKya,gBAAkBL,EAAgBE,EACnCrZ,EAAQmQ,OAAO5K,WAAagE,IACxBpC,EAASmS,EAAQlS,aACrBkS,EAAQvB,WAAW5Q,EAASgS,EAAgBE,GAAerB,WAAW7Q,EAASgS,EAAgBE,IAEnGta,KAAK0a,gBAAkB1a,KAAK2a,oBAAoB3a,KAAK6H,WAAWD,EAAK3G,EAAQgQ,YAC7EjR,KAAKkS,WAAavK,EAAU4S,EAASva,KAAK0a,gBAAiBzZ,EAAQgQ,UAAWhQ,EAAQiR,YACtFmI,EAAShV,OAAOrF,KAAKkS,YACdmI,GAEXM,oBAAqB,SAAUC,GAAV,GAGRpc,GAFLyC,EAAUjB,KAAKiB,QACfsG,EAAOtG,EAAQiQ,UAAYjQ,EAAQgQ,SACvC,KAASzS,EAAIoc,EAAOlc,OAAS,EAAGF,GAAK,EAAGA,IAChCA,EAAI+I,IAAS,GACbqT,EAAOC,OAAOrc,EAAG,EAGzB,OAAOoc,IAEX/S,WAAY,SAAUiT,EAAMC,GAAhB,GAYJC,GACKxc,EAZLyC,EAAUjB,KAAKiB,QACf6P,EAAU7P,EAAQ6P,QAClBvL,EAAQtE,EAAQwE,IAAMxE,EAAQuE,IAC9BsS,EAAQgD,EAAKjD,SAAWiD,EAAKlD,WAC7BqD,EAAY1V,EAAQwV,EACpB/G,EAAM8G,EAAKlD,WACXpQ,EAAOsQ,EAAQmD,CAMnB,KALInK,IACAkD,GAAO8D,EACPtQ,GAAQA,GAERwT,KACKxc,EAAI,EAAGA,EAAIyc,EAAWzc,IAC3Bwc,EAAU7c,KAAKkL,EAAM2K,EAAKnL,IAC1BmL,GAAOxM,CAKX,OAHI6B,GAAM2K,IAAQ8G,EAAKjD,UACnBmD,EAAU7c,KAAK6V,GAEZgH,GAEX5S,OAAQ,SAAU/H,GACd,MAAIA,IACAL,KAAK4H,IAAIoR,WAAW3Y,GAAO4Y,WAAW5Y,GACtCL,KAAKkb,gBAAgBlb,KAAKuS,WAAW1O,SAAU7D,KAAKwa,iBACpDxa,KAAKkb,gBAAgBlb,KAAKkS,WAAWrO,SAAU7D,KAAK0a,iBAAiB,GAFrE1a,QAIOA,KAAK4H,IAAIS,cAGxB6S,gBAAiB,SAAUjT,EAAOJ,EAAYsT,GAA7B,GAQJ3c,GACD4c,EACA1B,EACA2B,EACAC,EAXJrE,EAAOkE,EAAQnb,KAAKya,iBAAmB,EAAI,EAC3CF,EAAUva,KAAK4H,IACfQ,EAASmS,EAAQlS,YAKrB,KAJI8S,GAASnb,KAAKiB,QAAQmQ,OAAO5K,WAAagE,GAAoB,IAATyM,IACrDsD,EAAUva,KAAK4H,IAAI/C,QACnB0V,EAAQvB,WAAW5Q,EAAS6O,GAAMgC,WAAW7Q,EAAS6O,IAEjDzY,EAAI,EAAGA,EAAIyJ,EAAMvJ,OAAQF,IAC1B4c,EAAWb,EAAQjS,QAAQT,EAAWrJ,IACtCkb,EAAWzR,EAAMzJ,GAAGkb,SACpB2B,EAAQD,EAASlY,EAAIwW,EAAS,GAAG6B,SAASrY,EAC1CoY,EAAQF,EAAShY,EAAIsW,EAAS,GAAG6B,SAASnY,EAC9C6E,EAAMzJ,GAAGoL,WAAU,GAAIA,IAAYuK,UAAUkH,EAAOC,OAIhEtS,EAAkBiD,IACdzG,IAAK,EACLC,IAAK,IACL8M,YACIxS,KAAM,GACN2S,MAAOpI,EACP3F,MAAOiE,EACP/J,MAAOsL,EACP7C,SAAS,GAEb4K,YACInS,KAAM,GACN2S,MAAOpI,EACP3F,MAAOiE,EACP/J,MAAOsL,EACP7C,SAAS,GAEbsQ,eACAC,SAAU,IACVzG,QACI5K,SAAU8D,EACVxF,QAAS,KAGboH,GAAyBzC,EAAU5J,QACnCC,KAAM,SAAUkN,EAAS/L,GACrBwI,EAAUsH,GAAGjR,KAAKkR,KAAKhR,KAAMgN,EAAS/L,EACtC,IAAIua,GAAmBxb,KAAKiB,OAC5Bua,GAAiB1H,SAAW9N,KAAKP,IAAIO,KAAKgP,IAAIwG,EAAiBC,SAAWD,EAAiBE,UAAYF,EAAiB1H,SAAW,IAAM,IAE7ItM,KAAM,SAAUwM,GAAV,GACE/S,GAAUjB,KAAKiB,QACf6W,EAAQ1O,EAAiBnI,EAAQya,SAAUza,EAAQwa,SAAUzH,EACjEhU,MAAKgN,QAAQpD,UAAUA,IAAYrB,OAAOuP,EAAO7W,EAAQkH,YAGjEa,EAAkBkD,IACdkI,OAAQ7J,EACRuJ,SAAUjK,IAEdH,EAAiBtK,QAAQiV,SAAS5J,EAAgByB,IAC9CC,GAAW,IACXC,GAAS9C,EAAQ8C,OACjBC,GAAU9C,EACV+C,GAAS9C,EACT+C,GAAgBtB,GAAQpL,QACxB8b,SAAU,SAAU7D,GAChB9X,KAAKmF,SAASyE,UAAUA,IAAYrB,OAAOuP,EAAO9X,KAAKmI,UAE3D2K,QAAS,WAAA,GACD9C,GAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ1P,EAAU+O,EAAI/O,QACdya,EAAW/K,EAAM+G,UAAUzW,EAAQ4R,WACnC4I,EAAW9K,EAAM+G,UAAUzW,EAAQZ,MACnCY,GAAQwP,UAAU3B,eAAgB,EAClC9O,KAAK2b,SAASF,GAEd,GAAIvP,IAAuBlM,KAAKmF,SAAU/C,EAAWnB,EAAQwP,WACzDiL,SAAUA,EACVD,SAAUA,KACVhH,QAGZlD,OAAQ,WAAA,GACAvB,GAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ1P,EAAU+O,EAAI/O,QACdkE,EAAW,GAAIkH,GAWnB,OAVIpL,GAAQwP,aAAc,GACtBrO,EAAWnB,EAAQwP,WACfmH,WAAY,EACZzP,OAAQwI,EAAM/I,IAAIO,OAClB2I,QAASH,EAAM1P,QAAQ6P,UAG/B3L,EAASE,OAAOrF,KAAK4b,gBAAiB5b,KAAK6b,cAC3C7b,KAAKmF,SAAWA,EAChBnF,KAAK2b,SAAStR,GACPlF,GAEXmK,OAAQ,SAAU1H,GAAV,GACAO,GAASnI,KAAKmI,OAASP,EAAIO,OAC3BzJ,EAASwK,EAAWlJ,KAAKiB,QAAQvC,QAAU,EAAG,GAAK,KACnD0J,EAASpI,KAAKoI,OAASR,EAAIS,aAAe3J,EAC1Cod,EAAU9b,KAAK8b,QAAU9V,KAAKqD,MAAMjB,EAASpI,KAAKiB,QAAQ8a,IAAIhc,KAClEC,MAAK2C,KAAOwB,EAAK6X,WAAW,GAAIxX,GAAc2D,EAAOjF,EAAI4Y,EAAS3T,EAAO/E,EAAI0Y,GAAU,GAAItX,GAAc2D,EAAOjF,EAAI4Y,EAAS3T,EAAO/E,EAAI0Y,KAE5IF,cAAe,WAAA,GACPtB,GAAgBta,KAAK2Q,MAAM1P,QAAQiR,WAAWnS,KAC9CoI,EAASnI,KAAKmI,OACd8T,EAAcjc,KAAKiB,QAAQ0D,MAC3BuX,EAAa,GAAI5P,KACjB5H,MAAQC,MAAOsX,GACf3X,QACIK,MAAOsX,EACPpd,MAAOsL,IAIf,OADA+R,GAAWpV,OAAOqB,EAAOjF,EAAIlD,KAAKoI,OAASkS,EAAenS,EAAO/E,GAAG2D,OAAOoB,EAAOjF,EAAGiF,EAAO/E,EAAIpD,KAAK8b,QAAU,GAAG/U,OAAOoB,EAAOjF,EAAGiF,EAAO/E,EAAIpD,KAAK8b,QAAU,GAAGnH,QACzJuH,GAEXL,WAAY,WAAA,GACJ5a,GAAUjB,KAAKiB,QACfkb,EAAWlb,EAAQ8a,IAAIpX,OAAS1D,EAAQ0D,MACxCyX,EAAS,GAAIzS,GAASyC,OAAOpM,KAAKmI,OAAQnI,KAAK8b,SAC/CC,EAAM,GAAI3P,IAAOgQ,GACjB1X,MAAQC,MAAOwX,GACf7X,QAAUK,MAAOwX,IAErB,OAAOJ,MAGf/S,EAAkBuD,IACdwP,KAAOhc,KAAMoM,IACbkQ,OACIxd,MAAO,GACPC,OAAQ,IAEZ2R,WACIf,KAAMjF,EACNqJ,SAAUjK,KAGd2C,GAAUjD,EACVkD,GAAc3B,EAAMjL,QACpByP,OAAQ,SAAU3M,GAAV,GAKKnE,GAJLgS,EAASxQ,KACT8N,EAAW9N,KAAK8N,QAGpB,KAFA9N,KAAK2Q,MAAMrB,OAAO3M,GAClB3C,KAAKsc,iBAAmBtc,KAAK2Q,MAAMhO,KAC1BnE,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IACjCsP,EAAStP,GAAG8Q,OAAOkB,EAAOG,MAAM/I,KAChC4I,EAAO8L,iBAAmBnY,EAAKiV,MAAM5I,EAAO8L,iBAAkBxO,EAAStP,GAAGmE,KAE9E3C,MAAKuc,SAAS5Z,GACd3C,KAAKwc,WAAW7Z,GAChB3C,KAAKyW,aAAazW,KAAKkP,UAAWpB,EAAU9N,KAAK2Q,OACjD3Q,KAAKiO,SAETwI,aAAc,SAAUvH,EAAWpB,EAAU6C,GACzC,GAAI+F,GAAU1W,KAAKoO,SAAW,GAAI5B,GAClCkK,GAAQrR,OAAO6J,GACfwH,EAAQrR,OAAOsL,EAAM1I,OACrByO,EAAQrR,OAAOsL,EAAMiB,QACrB5R,KAAKyc,eAAe3O,GACpB4I,EAAQrR,OAAOsL,EAAM6G,gBAEzBiF,eAAgB,SAAU3O,GAAV,GAEHtP,GACDY,EAFJoR,EAASxQ,IACb,KAASxB,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IAC7BY,EAAU0O,EAAStP,GACvBY,EAAQmS,SACRf,EAAOpC,SAAS/I,OAAOjG,EAAQ+F,UAC/B/F,EAAQiB,MAAMjB,EAAQ6B,QAAQZ,QAGtCkc,SAAU,SAAU5Z,GAUhB,IAVM,GAOF+Z,GAASC,EAASC,EAASC,EAAKC,EANhCtM,EAASxQ,KACT4H,EAAM5H,KAAK2Q,MAAM/I,IACjBmP,EAAc/W,KAAKsc,iBACnB9U,EAAOxB,KAAKgP,IAAIhV,KAAK+c,QAAQhG,EAAapU,IAC1C6C,EAAM6D,EAAM7B,EAAMqB,GAClBpD,EAAM4D,GAAO7B,EAAMqB,GAEnBmU,EAAY,EACZxe,EAAI,MACDA,IAAM,OACTwe,EAAYF,IAAYF,EAAUI,EAAY,EAAI,EAC9CA,EAAY,IAGZxX,IAAQqX,IACRH,EAAUlM,EAAOyM,WAAWzX,EAAK7C,EAAMiF,GACnC,GAAK8U,GAAWA,GAAW,IAI/BjX,IAAQoX,IACRD,EAAUpM,EAAOyM,WAAWxX,EAAK9C,EAAMiF,GACnC,GAAKgV,GAAWA,GAAW,KAK/BC,EADAH,EAAU,GAAKE,EAAU,EACb,EAANpX,EACCkX,EAAU,GAAKE,EAAU,EACpB,EAANnX,EAEA4D,GAAO7D,EAAMC,GAAO,GAAK,EAAGoD,GAEtC8T,EAAUnM,EAAOyM,WAAWJ,EAAKla,EAAMiF,GACnC,GAAK+U,GAAWA,GAAW,KAG/BG,EAAUF,EACND,EAAU,GACVlX,EAAMoX,EACND,EAAUD,IAEVnX,EAAMqX,EACNH,EAAUC,IAItBM,WAAY,SAAUzV,EAAM7E,EAAMiF,GAAtB,GAUCpJ,GATLgS,EAASxQ,KACT2Q,EAAQ3Q,KAAK2Q,MACb7C,EAAW9N,KAAK8N,SAChB1F,EAASR,EAAIS,aACb6U,EAAWtV,EAAI/C,OAKnB,KAJAqY,EAASlE,WAAW5Q,EAASZ,GAAMyR,WAAW7Q,EAASZ,GACvDmJ,EAAM/I,IAAMsV,EACZvM,EAAMrB,OAAO3M,GACb3C,KAAKmd,SAAWxM,EAAMhO,KACbnE,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IACjCsP,EAAStP,GAAG8Q,OAAO4N,GACnB1M,EAAO2M,SAAWhZ,EAAKiV,MAAM5I,EAAO2M,SAAUrP,EAAStP,GAAGmE,KAE9D,OAAO3C,MAAK+c,QAAQ/c,KAAKmd,SAAUxa,IAEvCoa,QAAS,SAAUK,EAAS5b,GACxB,MAAOwE,MAAKR,IAAIhE,EAAI3C,QAAUue,EAAQve,QAAS2C,EAAI1C,SAAWse,EAAQte,WAE1E0d,WAAY,SAAU7Z,GAAV,GAYCnE,GAXLgS,EAASxQ,KACTqd,EAAgBrd,KAAKmd,SAAShV,SAC9B2O,EAAYnU,EAAKwF,SACjBmV,EAAWD,EAAcna,EAAI4T,EAAU5T,EACvCqa,EAAWF,EAAcja,EAAI0T,EAAU1T,EACvC4M,EAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ7C,EAAWkC,EAAIlC,QAInB,KAHA6C,EAAM/I,IAAIO,OAAOjF,GAAKoa,EACtB3M,EAAM/I,IAAIO,OAAO/E,GAAKma,EACtB5M,EAAMrB,OAAO3M,GACJnE,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IACjCsP,EAAStP,GAAG8Q,OAAOqB,EAAM/I,KACzB4I,EAAO2M,SAAWhZ,EAAKiV,MAAMzI,EAAMhO,KAAMmL,EAAStP,GAAGmE,OAG7DyM,aAAc,WAAA,GAMNoO,GACKhf,EACDY,EAPJoR,EAASxQ,KACTiB,EAAUjB,KAAKiB,QACf6M,EAAW7M,EAAQ4M,QACnB8C,EAAQ3Q,KAAK2Q,MAAQ,GAAI1E,IAAYhL,EAAQ0P,MAAO3Q,KAAKoN,eAG7D,KAFApN,KAAK8N,YACD0P,EAAczU,EAAQ+E,GAAYA,GAAYA,GACzCtP,EAAI,EAAGA,EAAIgf,EAAY9e,OAAQF,IAChCY,EAAU,GAAImN,IAAcoE,EAAOvO,KAAeob,EAAYhf,IAAMiS,WAAa3B,YAAa7N,EAAQ6N,gBAC1G0B,EAAO1C,SAAS3P,KAAKiB,MAIjC4J,EAAkByD,IACdqC,aAAa,EACbI,WAAanL,WAAY,MAEzB2I,GAAWT,GAAYpM,QACvB8Z,cAAe,WAAA,GACP3J,GAAMhQ,KAAKiB,QACXuE,EAAMwK,EAAIxK,IACVC,EAAMuK,EAAIvK,IACV0U,EAAwBnK,EAAImK,sBAC5BsD,EAAezN,EAAIyN,YACvB,SACQ/X,KAAMF,EACNM,GAAIL,EACJd,MAAOwV,EACPjV,QAASuY,KAGrB1F,UAAW,WACP,OAAO,GAEX2F,sBAAuB,SAAU5F,GAC7B,GAAI6F,GAAc3d,KAAK4R,OAAO/N,SAAS,GAAG8F,UACtC3J,MAAKiB,QAAQ6P,QACb6M,EAAYC,YAAY9F,GAExB6F,EAAYE,cAAc/F,IAGlCgG,SAAU,SAAUpY,EAAMI,EAAI7E,GAApB,GACF6P,GAAU9Q,KAAKiB,QAAQ6P,QACvB8G,EAAa5X,KAAK0X,UAAU5G,EAAUhL,EAAKJ,GAC3CmS,EAAW7X,KAAK0X,UAAU5G,EAAUpL,EAAOI,GAC3CP,EAAQvF,KAAK6Z,YAAYjC,EAAYC,EAAU7X,KAAKuZ,iBAAkBtY,EAE1E,OADAjB,MAAK4R,OAAOvM,OAAOE,GACZA,KAGfyD,EAAkB0D,IACdlH,IAAK,EACLC,IAAK,IACL8M,YAAcjL,SAAS,GACvB4K,YAAc5K,SAAS,GACvB8J,QAAU9J,SAAS,GACnBsQ,WAAY,EACZC,SAAU,IACV4F,aAAc,UAEd9Q,GAAe,IACfC,GAAwBnD,EAAU5J,QAClCC,KAAM,SAAUkN,EAAS/L,GAAnB,GAEEua,GACA1H,EAEAiK,EACApZ,CALJ8E,GAAUsH,GAAGjR,KAAKkR,KAAKhR,KAAMgN,EAAS/L,GAClCua,EAAmBxb,KAAKiB,QACxB6S,EAAW9N,KAAKgP,IAAIwG,EAAiBC,SAAWD,EAAiBE,UAAYF,EAAiB1H,SAAW,IAC7G0H,EAAiB1H,SAAW5K,EAAW4K,EAAUjK,EAAe8C,IAC5DoR,EAAa/Q,EAAQ7H,SAASlE,QAAQL,IAAI,gBAC1C+D,EAAQqI,EAAQgR,eAChBD,IAAepZ,IACf3E,KAAK+d,WAAa,GAAIre,OAAMue,MAAMF,GAClC/d,KAAK2E,MAAQ,GAAIjF,OAAMue,MAAMtZ,KAGrC6C,KAAM,SAAUwM,GAAV,GAQMkK,GACAC,EACAC,EATJpO,EAAMhQ,KACNiB,EAAU+O,EAAI/O,QACd8c,EAAa/N,EAAI+N,WACjBpZ,EAAQqL,EAAIrL,MACZmT,EAAQ1O,EAAiBnI,EAAQya,SAAUza,EAAQwa,SAAUzH,EACjEhU,MAAKgN,QAAQ8K,MAAMA,GACfnT,IACIuZ,EAAI7U,EAAMD,EAAiB2U,EAAWG,EAAGvZ,EAAMuZ,EAAGlK,IAClDmK,EAAI9U,EAAMD,EAAiB2U,EAAWI,EAAGxZ,EAAMwZ,EAAGnK,IAClDoK,EAAI/U,EAAMD,EAAiB2U,EAAWK,EAAGzZ,EAAMyZ,EAAGpK,IACtDhU,KAAKgN,QAAQ1I,OAAO,GAAI5E,OAAMue,MAAMC,EAAGC,EAAGC,GAAGC,aAIzDrV,EAAkB4D,IACdwH,OAAQ7J,EACRuJ,SAAUjK,IAEdH,EAAiBtK,QAAQiV,SAAS3J,EAAsBkC,IACpDC,GAAe5B,GAAQpL,QACvBiT,QAAS,WAAA,GACD9C,GAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ1P,EAAU+O,EAAI/O,QACdya,EAAW/K,EAAM+G,UAAUzW,EAAQ4R,WACnC4I,EAAW9K,EAAM+G,UAAUzW,EAAQZ,MACnCL,MAAKyQ,WACLzQ,KAAKyQ,UAAU6N,QAEfrd,EAAQwP,UAAU3B,eAAgB,GAClC9O,KAAK8X,MAAM2D,GACXzb,KAAKsE,OAAOtE,KAAKge,kBAEjBhe,KAAKyQ,UAAY,GAAI7D,IAAsB5M,KAAMoC,EAAWnB,EAAQwP,WAChEiL,SAAUA,EACVD,SAAUA,KAEdzb,KAAKyQ,UAAUgE,SAGvBqD,MAAO,SAAUzX,GACb,GAAIsd,GAAc3d,KAAKmF,SAASwE,UAC5B3J,MAAK2Q,MAAM1P,QAAQ6P,QACnB6M,EAAYE,cAAcxd,GAE1Bsd,EAAYC,YAAYvd,GAE5BL,KAAK2Q,MAAM+M,sBAAsBrd,IAErCiE,OAAQ,SAAUjE,GACdL,KAAKmF,SAASb,OAAOjE,IAEzBkR,OAAQ,WAAA,GAIAvB,GACAW,EACA1P,CALAjB,MAAKmF,WAGL6K,EAAMhQ,KACN2Q,EAAQX,EAAIW,MACZ1P,EAAU+O,EAAI/O,QACdA,EAAQwP,aAAc,GACtBrO,EAAWnB,EAAQwP,WACfmH,WAAY,EACZzP,OAAQwI,EAAM/I,IAAIO,OAClB2I,QAASH,EAAM1P,QAAQ6P,UAG/B9Q,KAAKmF,SAAWwL,EAAMmN,SAASnN,EAAM1P,QAAQuE,IAAKxF,KAAKiB,QAAQZ,OAC3DsE,MAAO3E,KAAKge,eACZtV,QAASzH,EAAQyH,QACjBxD,QAASyL,EAAM1P,QAAQwc,iBAG/BO,aAAc,WAAA,GAUGO,GACDC,EACAC,EACA/Y,EAIAI,EAhBRkK,EAAMhQ,KAAK2Q,MAAM1P,QACjBuE,EAAMwK,EAAIxK,IACVC,EAAMuK,EAAIvK,IACV0N,EAAQnT,KAAKiB,QACbyd,EAASvL,EAAMuL,OACf/Z,EAAQwO,EAAMxO,MACdtE,EAAQ8S,EAAM9S,MACdse,EAAehW,EAAQ8F,SAASpO,GAASA,EAAQmF,CACrD,IAAIkZ,EACA,IAASH,EAAM,EAAGA,EAAMG,EAAOhgB,OAAQ6f,IAWnC,GAVIC,EAAQE,EAAOH,GACfE,EAAaD,EAAM7Z,MACnBe,EAAO8Y,EAAM9Y,KACJ,SAATA,IACAA,EAAOF,GAEPM,EAAK0Y,EAAM1Y,GACJ,SAAPA,IACAA,EAAKL,GAELC,GAAQiZ,GAAgBA,GAAgB7Y,EACxC,MAAO2Y,EAInB,OAAO9Z,IAEX2K,OAAQ,WACJtP,KAAKuR,SACLvR,KAAK2C,KAAO3C,KAAKmF,SAASxC,UAGlCqG,EAAkB6D,IACd4D,WACIf,KAAMhF,EACNoJ,SAAUjK,KAGdiD,GAAWL,GAAY5M,QACvB0N,WAAY,SAAUL,GAClBT,GAAYsE,GAAGxD,WAAWyD,KAAKhR,KAAMkN,GACrClN,KAAKiB,QAAQ0D,MAAQ3E,KAAKiB,QAAQ0D,QAAU3E,KAAKkN,MAAMW,aAAelJ,OAE1EyK,aAAc,WAAA,GACNnO,GAAUjB,KAAKiB,QACf0P,EAAQ3Q,KAAK2Q,MAAQ,GAAIjE,IAASzL,EAAQ0P,MAAO3Q,KAAKoN,gBACtDS,EAAU,GAAIhB,IAAa8D,EAAOvO,MAClCsc,OAAQzd,EAAQyd,OAChB/Z,MAAO1D,EAAQ0D,MACftE,MAAOY,EAAQZ,MACfqI,QAASzH,EAAQyH,QACjB+H,WAAa3B,YAAa7N,EAAQ6N,eAEtC9O,MAAK8N,UAAYD,IAErB4O,eAAgB,SAAU3O,GAAV,GACHtP,GACDY,CADR,KAASZ,EAAI,EAAGA,EAAIsP,EAASpP,OAAQF,IAC7BY,EAAU0O,EAAStP,GACvBY,EAAQmS,SACRnS,EAAQiB,MAAMjB,EAAQ6B,QAAQZ,QAGtC2N,iBAAkB,SAAU3N,GACxBL,KAAKiB,QAAQZ,MAAQA,GAEzB2d,aAAc,WACV,GAAInQ,GAAU7N,KAAK8N,SAAS,EAC5B,IAAID,EACA,MAAOA,GAAQmQ,gBAGvBY,oBAAqB,SAAU/f,EAAOC,GAAjB,GAMTyE,GAOAZ,EACAkc,EACArb,EAdJzD,EAAOC,KAAK8C,UACZqF,EAASnI,KAAK2Q,MAAM/I,IAAIO,OACxBhF,EAAOgF,EAAOjF,EAAIrE,EAAQ,EAC1BwE,EAAM8E,EAAO/E,EAAItE,EAAS,CAiB9B,OAhBID,GAAQkB,EAAKlB,QACT0E,EAAQJ,EAAOtE,EACnBsE,EAAO6C,KAAKP,IAAItC,EAAM,GAClBI,EAAQxD,EAAKlB,QACbsE,GAAQI,EAAQxD,EAAKlB,QAGzBC,EAASiB,EAAKjB,SACV6D,EAAO3C,KAAK2Q,MAAMhO,KAClBkc,EAASlc,EAAKmc,cAAc1b,EAC5BI,EAASH,EAAMvE,EACnBuE,EAAM2C,KAAKP,IAAIpC,EAAKV,EAAKC,OAAOQ,GAC5BI,EAASqb,IACTxb,GAAOG,EAASqb,KAIpB1b,KAAMA,EACNE,IAAKA,MAIjB3D,MAAM0C,WAAW1C,MAAMiJ,SACnBmC,MAAOA,EACPgB,YAAaA,GACbZ,cAAeA,GACfI,mBAAoBA,GACpBI,iBAAkBA,GAClBV,YAAaA,GACbyB,YAAaA,GACbF,cAAeA,GACfN,YAAaA,GACba,SAAUA,GACVD,aAAcA,GACdH,SAAUA,MAEhBjN,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,sBAAuB,8BAA+BD,IAC/D,WAiME,MAhMC,UAAUE,GAQP,QAASuhB,GAAa9d,GAAtB,GACQ+d,GAASrW,EAAQsW,GAAGD,WACpBE,EAAYje,EAAQiM,OAAS,GAC7BiS,EAAYD,EAAUE,aAC1B,OAAIzW,GAAQ0W,YAAYC,QAAQH,OACrBxW,EAAQ4W,YAAYC,OAEvBR,EAAOE,IAAcF,EAAOG,QAAkBK,MAyD1D,QAASC,GAAkBC,GACvB5U,EAAMiG,GAAG2O,GAAQ,WACb,MAAO1f,MAAK2f,UAAUD,GAAME,MAAM5f,KAAK2f,UAAW5R,YAwF1D,QAAS8R,GAAmBH,GACxBI,EAAe/O,GAAG2O,GAAQ,SAAUze,GAAV,GAClBue,GAAQxf,KACR+f,EAAS5R,EAAKuR,EAClB,OAAKF,GAAMQ,eAGJ7R,EAAK8R,QAAQT,EAAMxS,SAASkT,KAAK,SAAUC,GAC9C,MAAOJ,GAAOI,EAAQlf,KAHf8e,EAAOP,EAAMnR,eAAgBpN,IAvKnD,GA6EYsd,GAIL6B,EAMAC,EASAP,EA8EAQ,EA7KA5gB,EAAQD,OAAOC,MACf6gB,EAAS7gB,EAAMuf,GAAGsB,OAClB5X,EAAUjJ,EAAMiJ,QAChBmD,EAAcnD,EAAQmD,YACtBW,EAAc9D,EAAQ8D,YACtBK,EAAWnE,EAAQmE,SACnBqB,EAAOzO,EAAM4J,QAUbwB,EAAQyV,EAAO1gB,QACfC,KAAM,SAAUkN,EAASC,GACrBvN,EAAM+N,QAAQT,GACdxP,EAAEwP,GAASwT,QACXD,EAAOxP,GAAGjR,KAAKkR,KAAKhR,KAAMgN,GAC1BhN,KAAKiB,QAAUvB,EAAM0C,WAAWpC,KAAKiB,QAASgM,GAC9CjN,KAAKgE,QAAUhE,KAAKgN,QACpBhN,KAAKygB,kBACLzgB,KAAKgN,QAAQ0T,SAAS,WACtBhhB,EAAMihB,OAAO3gB,KAAM2I,EAAQsW,KAE/Bhe,SACIiM,MAAO,UACPiD,SAAU,GACVtC,WACA8C,SACAzB,WAAanL,WAAY,IACzB+K,aAAa,GAEjBS,WAAY,SAAUtO,GAClBjB,KAAK2f,UAAUpQ,WAAWtO,EAAS8d,EAAa9d,IAChDjB,KAAK4gB,eAETpT,OAAQ,WACJxN,KAAK2f,UAAUnS,SACfxN,KAAK4gB,eAETnT,QAAS,WACL8S,EAAOxP,GAAGtD,QAAQuD,KAAKhR,MACvBA,KAAK2f,UAAUlS,WAEnBgT,gBAAiB,WACb,GAAII,GAAY7gB,KAAK8gB,YACrB9gB,MAAK2f,UAAY,GAAIkB,GAAU7gB,KAAKgN,QAAQ,GAAIhN,KAAKiB,QAAS8d,EAAa/e,KAAKiB,UAChFjB,KAAK4gB,eAETA,YAAa,WACT5gB,KAAKsN,iBAAmBtN,KAAK2f,UAAUrS,iBACvCtN,KAAKiB,QAAUjB,KAAK2f,UAAU1e,QAC9BjB,KAAK0N,QAAU1N,KAAK2f,UAAUjS,QAC9B1N,KAAK2C,KAAO3C,KAAK2f,UAAUhd,KAC3B3C,KAAKkP,UAAYlP,KAAK2f,UAAUzQ,UAChClP,KAAK8N,SAAW9N,KAAK2f,UAAU7R,SAC/B9N,KAAK2Q,MAAQ3Q,KAAK2f,UAAUhP,OAEhCoQ,QAAS,WACL/gB,KAAK2f,UAAU/Q,YAGnBoS,GACA,UACA,QACA,YACA,eAOJ,KAASzC,EAAM,EAAGA,EAAMyC,EAAatiB,OAAQ6f,IACzCkB,EAAkBuB,EAAazC,GAqGnC,KAnGA5V,EAAQsY,YAAYphB,OAAOiL,EAAMiG,IAC7BqP,EAAoBtV,EAAMjL,QAC1BoB,SAAWye,KAAM,eACjBoB,WAAY,WACR,MAAOrU,MAGX4T,EAAoBvV,EAAMjL,QAC1BoB,SACIye,KAAM,cACN/O,OAAShK,UAAU,IAEvBma,WAAY,WACR,MAAOhV,MAGXgU,EAAiBhV,EAAMjL,QACvBC,KAAM,SAAUkN,EAASC,GACrBnC,EAAMiG,GAAGjR,KAAKkR,KAAKhR,KAAMgN,EAASC,GAClCjN,KAAKgN,QAAQkU,IAAI,WAAY,YAC7BlhB,KAAKgN,QAAQ0T,SAAS,cACtB1gB,KAAKmhB,mBAETlgB,SAAWye,KAAM,YACjBnQ,WAAY,SAAUtO,GAClB6J,EAAMiG,GAAGxB,WAAWyB,KAAKhR,KAAMiB,GAC/BjB,KAAKmhB,mBAET3T,OAAQ,WACJ1C,EAAMiG,GAAGvD,OAAOwD,KAAKhR,MACrBA,KAAKmhB,mBAET9gB,MAAO,SAAUA,GACb,GAAI+gB,GAAWphB,KAAK2f,SACpB,OAAyB,KAArB5R,UAAUrP,OACH0iB,EAAS/gB,SAEpB+gB,EAAS/gB,MAAMA,OACfL,MAAKmhB,oBAET1T,QAAS;AACL3C,EAAMiG,GAAGtD,QAAQuD,KAAKhR,YACfA,MAAKggB,gBAEhB3R,aAAc,WACV,OAAIrO,KAAKggB,gBAGFlV,EAAMiG,GAAG1C,aAAa2C,KAAKhR,OAEtC+gB,QAAS,WACL/gB,KAAK2f,UAAU/Q,SACf5O,KAAKmhB,mBAETA,gBAAiB,WAAA,GAELE,GACAD,EACAE,EAKA9a,CARJxG,MAAKiB,QAAQsgB,gBACTF,EAAW3hB,EAAM2hB,SAASrhB,KAAKiB,QAAQsgB,gBACvCH,EAAWphB,KAAK2f,UAChB2B,EAAgBthB,KAAKwhB,oBACzBF,EAAcG,KAAKJ,GACf1c,MAAOyc,EAASpD,eAChB3d,MAAO+gB,EAAS/gB,WAEhBmG,EAAW4a,EAASxC,oBAAoB0C,EAAcziB,QAASyiB,EAAcxiB,UACjFwiB,EAAcJ,IAAI1a,IACXxG,KAAKggB,iBACZhgB,KAAKggB,eAAe0B,SACpB1hB,KAAKggB,eAAiB,OAG9BwB,kBAAmB,WACf,GAAIF,GAAgBthB,KAAKggB,cAKzB,OAJKsB,KACDA,EAAgBthB,KAAKggB,eAAiBxiB,EAAE,eAAekjB,SAAS,oBAChE1gB,KAAKgN,QAAQ3H,OAAOic,IAEjBA,GAEXR,WAAY,WACR,MAAOhU,MAeXwT,GACA,YACA,cACA,aAEC/B,EAAM,EAAGA,EAAM+B,EAAc5hB,OAAQ6f,IACtCsB,EAAmBS,EAAc/B,GAErC5V,GAAQsW,GAAG0C,OAAOtB,GAClB1X,EAAQsW,GAAG0C,OAAOvB,GAClBzX,EAAQsW,GAAG0C,OAAO7B,GAClBpgB,EAAM0C,WAAWuG,GACbmC,MAAOA,EACPgB,YAAauU,EACb5T,YAAa2T,EACbtT,SAAUgT,KAEhBrgB,OAAOC,MAAM2C,QACR5C,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,uBACH,qBACA,wBACDD,IACL,WAWE,MAAOmC,QAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dataviz.gauge.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/gauge/kendo-gauges', [\n        'kendo.core',\n        'kendo.color',\n        'kendo.drawing',\n        'kendo.dataviz.core'\n    ], f);\n}(function () {\n    (function ($) {\n        window.kendo.dataviz = window.kendo.dataviz || {};\n        var dataviz = kendo.dataviz;\n        var getSpacing = dataviz.getSpacing;\n        var defined = dataviz.defined;\n        var constants = dataviz.constants;\n        var BLACK = constants.BLACK;\n        var COORD_PRECISION = constants.COORD_PRECISION;\n        var services = dataviz.services;\n        var deepExtend = dataviz.deepExtend;\n        var isArray = dataviz.isArray;\n        var setDefaultOptions = dataviz.setDefaultOptions;\n        var NumericAxis = dataviz.NumericAxis;\n        var limitValue = dataviz.limitValue;\n        var Box = dataviz.Box;\n        var interpolateValue = dataviz.interpolateValue;\n        var round = dataviz.round;\n        var drawing = kendo.drawing;\n        var DrawingGroup = drawing.Group;\n        var DrawingPath = drawing.Path;\n        var Animation = drawing.Animation;\n        var AnimationFactory = drawing.AnimationFactory;\n        var geometry = kendo.geometry;\n        var Rect = geometry.Rect;\n        var GeometryPoint = geometry.Point;\n        var transform = geometry.transform;\n        var ANGULAR_SPEED = 150;\n        var LINEAR_SPEED = 250;\n        var ARROW = 'arrow';\n        var ARROW_POINTER = 'arrowPointer';\n        var BAR_POINTER = 'barPointer';\n        var DEFAULT_HEIGHT = 200;\n        var DEFAULT_LINE_WIDTH = 0.5;\n        var DEFAULT_WIDTH = 200;\n        var DEGREE = Math.PI / 180;\n        var INSIDE = 'inside';\n        var LINEAR = 'linear';\n        var OUTSIDE = 'outside';\n        var RADIAL_POINTER = 'radialPointer';\n        var RADIAL_RANGE_POINTER = 'radialRangePointer';\n        function pad(bbox, value) {\n            var origin = bbox.getOrigin();\n            var size = bbox.getSize();\n            var spacing = getSpacing(value);\n            bbox.setOrigin([\n                origin.x - spacing.left,\n                origin.y - spacing.top\n            ]);\n            bbox.setSize([\n                size.width + (spacing.left + spacing.right),\n                size.height + (spacing.top + spacing.bottom)\n            ]);\n            return bbox;\n        }\n        var Group = DrawingGroup;\n        var Path$1 = DrawingPath;\n        var Text = drawing.Text;\n        function buildLabelElement(label, options) {\n            var labelBox = label.box;\n            var textBox = label.children[0].box;\n            var border = options.border || {};\n            var background = options.background || '';\n            var wrapper = Path$1.fromRect(new Rect([\n                labelBox.x1,\n                labelBox.y1\n            ], [\n                labelBox.width(),\n                labelBox.height()\n            ]), { stroke: {} });\n            var text = new Text(label.text, new GeometryPoint(textBox.x1, textBox.y1), {\n                font: options.font,\n                fill: { color: options.color }\n            });\n            var styleGeometry = pad(text.bbox().clone(), options.padding);\n            var styleBox = Path$1.fromRect(styleGeometry, {\n                stroke: {\n                    color: border.width ? border.color : '',\n                    width: border.width,\n                    dashType: border.dashType,\n                    lineJoin: 'round',\n                    lineCap: 'round'\n                },\n                fill: { color: background }\n            });\n            var elements = new Group();\n            elements.append(wrapper);\n            elements.append(styleBox);\n            elements.append(text);\n            return elements;\n        }\n        function getRange(range, min, max) {\n            var from = defined(range.from) ? range.from : constants.MIN_VALUE;\n            var to = defined(range.to) ? range.to : constants.MAX_VALUE;\n            range.from = Math.max(Math.min(to, from), min);\n            range.to = Math.min(Math.max(to, from), max);\n            return range;\n        }\n        function unpad(bbox, value) {\n            var spacing = getSpacing(value);\n            spacing.left = -spacing.left;\n            spacing.top = -spacing.top;\n            spacing.right = -spacing.right;\n            spacing.bottom = -spacing.bottom;\n            return pad(bbox, spacing);\n        }\n        var DEFAULT_MARGIN = 5;\n        var Path = DrawingPath;\n        var Surface = drawing.Surface;\n        var Gauge = dataviz.Class.extend({\n            init: function (element, userOptions, theme, context) {\n                if (context === void 0) {\n                    context = {};\n                }\n                this.element = element;\n                this.theme = theme;\n                this.contextService = new services.ChartService(this, context);\n                this._originalOptions = deepExtend({}, this.options, userOptions);\n                this.options = deepExtend({}, this._originalOptions);\n                this._initTheme(theme);\n                this.redraw();\n            },\n            destroy: function () {\n                if (this.surface) {\n                    this.surface.destroy();\n                    this.surface = null;\n                }\n                delete this.element;\n                delete this.surfaceElement;\n            },\n            value: function (pointerValue) {\n                var pointer = this.pointers[0];\n                if (arguments.length === 0) {\n                    return pointer.value();\n                }\n                pointer.value(pointerValue);\n                this._setValueOptions(pointerValue);\n            },\n            _draw: function () {\n                var surface = this.surface;\n                surface.clear();\n                surface.draw(this._visuals);\n            },\n            exportVisual: function () {\n                return this._visuals;\n            },\n            allValues: function (values) {\n                var pointers = this.pointers;\n                var allValues = [];\n                if (arguments.length === 0) {\n                    for (var i = 0; i < pointers.length; i++) {\n                        allValues.push(pointers[i].value());\n                    }\n                    return allValues;\n                }\n                if (isArray(values)) {\n                    for (var i$1 = 0; i$1 < values.length; i$1++) {\n                        if (dataviz.isNumber(values[i$1])) {\n                            pointers[i$1].value(values[i$1]);\n                        }\n                    }\n                }\n                this._setValueOptions(values);\n            },\n            _setValueOptions: function (values) {\n                var pointers = [].concat(this.options.pointer);\n                var arrayValues = [].concat(values);\n                for (var i = 0; i < arrayValues.length; i++) {\n                    pointers[i].value = arrayValues[i];\n                }\n            },\n            resize: function () {\n                this.noTransitionsRedraw();\n            },\n            noTransitionsRedraw: function () {\n                var transitions = this.options.transitions;\n                this._toggleTransitions(false);\n                this.redraw();\n                this._toggleTransitions(transitions);\n            },\n            redraw: function () {\n                var size = this._surfaceSize();\n                var wrapper = new Rect([\n                    0,\n                    0\n                ], [\n                    size.width,\n                    size.height\n                ]);\n                this._initSurface();\n                this.gaugeArea = this._createGaugeArea();\n                this._createModel();\n                var bbox = unpad(wrapper.bbox(), this._gaugeAreaMargin);\n                this.reflow(bbox);\n            },\n            setOptions: function (options, theme) {\n                this._originalOptions = deepExtend(this._originalOptions, options);\n                this.options = deepExtend({}, this._originalOptions);\n                this._initTheme(theme);\n                this.redraw();\n            },\n            setDirection: function (rtl) {\n                this.contextService.rtl = Boolean(rtl);\n                if (this.surface && this.surface.type === 'svg') {\n                    this.surface.destroy();\n                    this.surface = null;\n                }\n            },\n            setIntlService: function (intl) {\n                this.contextService.intl = intl;\n            },\n            _initTheme: function (theme) {\n                var currentTheme = theme || this.theme || {};\n                this.theme = currentTheme;\n                this.options = deepExtend({}, currentTheme, this.options);\n                var options = this.options;\n                var pointer = options.pointer;\n                if (isArray(pointer)) {\n                    var pointers = [];\n                    for (var i = 0; i < pointer.length; i++) {\n                        pointers.push(deepExtend({}, currentTheme.pointer, pointer[i]));\n                    }\n                    options.pointer = pointers;\n                }\n            },\n            _createGaugeArea: function () {\n                var options = this.options.gaugeArea;\n                var size = this.surface.size();\n                var border = options.border || {};\n                var areaGeometry = new Rect([\n                    0,\n                    0\n                ], [\n                    size.width,\n                    size.height\n                ]);\n                this._gaugeAreaMargin = options.margin || DEFAULT_MARGIN;\n                if (border.width > 0) {\n                    areaGeometry = unpad(areaGeometry, border.width);\n                }\n                var gaugeArea = Path.fromRect(areaGeometry, {\n                    stroke: {\n                        color: border.width ? border.color : '',\n                        width: border.width,\n                        dashType: border.dashType,\n                        lineJoin: 'round',\n                        lineCap: 'round'\n                    },\n                    fill: { color: options.background }\n                });\n                return gaugeArea;\n            },\n            _initSurface: function () {\n                var ref = this;\n                var options = ref.options;\n                var surface = ref.surface;\n                var element = this._surfaceElement();\n                var size = this._surfaceSize();\n                dataviz.elementSize(element, size);\n                if (!surface || surface.options.type !== options.renderAs) {\n                    if (surface) {\n                        surface.destroy();\n                    }\n                    this.surface = Surface.create(element, { type: options.renderAs });\n                } else {\n                    this.surface.clear();\n                    this.surface.resize();\n                }\n            },\n            _surfaceSize: function () {\n                var options = this.options;\n                var size = this._getSize();\n                if (options.gaugeArea) {\n                    deepExtend(size, options.gaugeArea);\n                }\n                return size;\n            },\n            _surfaceElement: function () {\n                if (!this.surfaceElement) {\n                    this.surfaceElement = document.createElement('div');\n                    this.element.appendChild(this.surfaceElement);\n                }\n                return this.surfaceElement;\n            },\n            getSize: function () {\n                return this._getSize();\n            },\n            _getSize: function () {\n                var element = this.element;\n                var defaultSize = this._defaultSize();\n                var width = element.offsetWidth;\n                var height = element.offsetHeight;\n                if (!width) {\n                    width = defaultSize.width;\n                }\n                if (!height) {\n                    height = defaultSize.height;\n                }\n                return {\n                    width: width,\n                    height: height\n                };\n            },\n            _defaultSize: function () {\n                return {\n                    width: DEFAULT_WIDTH,\n                    height: DEFAULT_HEIGHT\n                };\n            },\n            _toggleTransitions: function (value) {\n                var this$1 = this;\n                this.options.transitions = value;\n                for (var i = 0; i < this.pointers.length; i++) {\n                    this$1.pointers[i].options.animation.transitions = value;\n                }\n            }\n        });\n        setDefaultOptions(Gauge, {\n            plotArea: {},\n            theme: 'default',\n            renderAs: '',\n            pointer: {},\n            scale: {},\n            gaugeArea: {}\n        });\n        var Path$2 = DrawingPath;\n        var Group$2 = DrawingGroup;\n        var Point = GeometryPoint;\n        function renderAxisTick(tickRenderOptions, tickOptions) {\n            var position = tickRenderOptions.position;\n            var tickX = tickRenderOptions.tickX;\n            var tickY = tickRenderOptions.tickY;\n            var start, end;\n            if (tickRenderOptions.vertical) {\n                start = new Point(tickX, position);\n                end = new Point(tickX + tickOptions.size, position);\n            } else {\n                start = new Point(position, tickY);\n                end = new Point(position, tickY + tickOptions.size);\n            }\n            var tickPath = new Path$2({\n                stroke: {\n                    color: tickOptions.color,\n                    width: tickOptions.width\n                }\n            }).moveTo(start).lineTo(end);\n            return tickPath;\n        }\n        function renderTicks(tickGroup, tickPositions, tickRenderOptions, tickOptions) {\n            var count = tickPositions.length;\n            if (tickOptions.visible) {\n                var mirror = tickRenderOptions.mirror;\n                var lineBox = tickRenderOptions.lineBox;\n                for (var i = tickOptions.skip; i < count; i += tickOptions.step) {\n                    if (i % tickOptions.skipUnit === 0) {\n                        continue;\n                    }\n                    tickRenderOptions.tickX = mirror ? lineBox.x2 : lineBox.x2 - tickOptions.size;\n                    tickRenderOptions.tickY = mirror ? lineBox.y1 - tickOptions.size : lineBox.y1;\n                    tickRenderOptions.position = tickPositions[i];\n                    tickGroup.append(renderAxisTick(tickRenderOptions, tickOptions));\n                }\n            }\n        }\n        var LinearScale = NumericAxis.extend({\n            init: function (options, service) {\n                var scaleOptions = options || {};\n                if (!defined(scaleOptions.reverse) && scaleOptions.vertical === false && (service || {}).rtl) {\n                    scaleOptions = $.extend({}, scaleOptions, { reverse: true });\n                }\n                NumericAxis.fn.init.call(this, 0, 1, scaleOptions, service);\n                this.options.minorUnit = this.options.minorUnit || this.options.majorUnit / 10;\n            },\n            initUserOptions: function (options) {\n                var scaleOptions = deepExtend({}, this.options, options);\n                scaleOptions = deepExtend({}, scaleOptions, { labels: { mirror: scaleOptions.mirror } });\n                scaleOptions.majorUnit = scaleOptions.majorUnit || dataviz.autoMajorUnit(scaleOptions.min, scaleOptions.max);\n                return scaleOptions;\n            },\n            initFields: function () {\n            },\n            render: function () {\n                var elements = this.elements = new Group$2();\n                var labels = this.renderLabels();\n                var scaleLine = this.renderLine();\n                var scaleTicks = this.renderTicks();\n                var ranges = this.renderRanges();\n                elements.append(scaleLine, labels, scaleTicks, ranges);\n                return elements;\n            },\n            renderRanges: function () {\n                var this$1 = this;\n                var options = this.options;\n                var min = options.min;\n                var max = options.max;\n                var vertical = options.vertical;\n                var mirror = options.labels.mirror;\n                var ranges = options.ranges || [];\n                var elements = new Group$2();\n                var count = ranges.length;\n                var rangeSize = options.rangeSize || options.minorTicks.size / 2;\n                for (var i = 0; i < count; i++) {\n                    var range = getRange(ranges[i], min, max);\n                    var slot = this$1.getSlot(range.from, range.to);\n                    var slotX = vertical ? this$1.lineBox() : slot;\n                    var slotY = vertical ? slot : this$1.lineBox();\n                    if (vertical) {\n                        slotX.x1 -= rangeSize * (mirror ? -1 : 1);\n                    } else {\n                        slotY.y2 += rangeSize * (mirror ? -1 : 1);\n                    }\n                    elements.append(Path$2.fromRect(new Rect([\n                        slotX.x1,\n                        slotY.y1\n                    ], [\n                        slotX.x2 - slotX.x1,\n                        slotY.y2 - slotY.y1\n                    ]), {\n                        fill: {\n                            color: range.color,\n                            opacity: range.opacity\n                        },\n                        stroke: {}\n                    }));\n                }\n                return elements;\n            },\n            renderLabels: function () {\n                var ref = this;\n                var labels = ref.labels;\n                var options = ref.options;\n                var elements = new Group$2();\n                for (var i = 0; i < labels.length; i++) {\n                    elements.append(buildLabelElement(labels[i], options.labels));\n                }\n                return elements;\n            },\n            renderLine: function () {\n                var line = this.options.line;\n                var lineBox = this.lineBox();\n                var elements = new Group$2();\n                if (line.width > 0 && line.visible) {\n                    var linePath = new Path$2({\n                        stroke: {\n                            color: line.color,\n                            dashType: line.dashType,\n                            width: line.width\n                        }\n                    });\n                    linePath.moveTo(lineBox.x1, lineBox.y1).lineTo(lineBox.x2, lineBox.y2);\n                    elements.append(linePath);\n                }\n                return elements;\n            },\n            renderTicks: function () {\n                var ticks = new Group$2();\n                var options = this.options;\n                var majorUnit = options.majorTicks.visible ? options.majorUnit : 0;\n                var tickRenderOptions = {\n                    vertical: options.vertical,\n                    mirror: options.labels.mirror,\n                    lineBox: this.lineBox()\n                };\n                renderTicks(ticks, this.getMajorTickPositions(), tickRenderOptions, options.majorTicks);\n                renderTicks(ticks, this.getMinorTickPositions(), tickRenderOptions, deepExtend({}, { skipUnit: majorUnit / options.minorUnit }, options.minorTicks));\n                return ticks;\n            }\n        });\n        setDefaultOptions(LinearScale, {\n            min: 0,\n            max: 50,\n            majorTicks: {\n                size: 15,\n                align: INSIDE,\n                color: BLACK,\n                width: DEFAULT_LINE_WIDTH,\n                visible: true\n            },\n            minorTicks: {\n                size: 10,\n                align: INSIDE,\n                color: BLACK,\n                width: DEFAULT_LINE_WIDTH,\n                visible: true\n            },\n            line: { width: DEFAULT_LINE_WIDTH },\n            labels: {\n                position: INSIDE,\n                padding: 2\n            },\n            mirror: false,\n            _alignLines: false\n        });\n        var Pointer = dataviz.Class.extend({\n            init: function (scale, userOptions) {\n                var ref = scale.options;\n                var min = ref.min;\n                var max = ref.max;\n                var options = this.options = deepExtend({}, this.options, userOptions);\n                options.fill = options.color;\n                this.scale = scale;\n                if (defined(options.value)) {\n                    options.value = limitValue(options.value, min, max);\n                } else {\n                    options.value = min;\n                }\n            },\n            value: function (newValue) {\n                var options = this.options;\n                var value = options.value;\n                if (arguments.length === 0) {\n                    return value;\n                }\n                var ref = this.scale.options;\n                var min = ref.min;\n                var max = ref.max;\n                options._oldValue = defined(options._oldValue) ? options.value : min;\n                options.value = limitValue(newValue, min, max);\n                if (this.elements) {\n                    this.repaint();\n                }\n            }\n        });\n        setDefaultOptions(Pointer, { color: BLACK });\n        var LinearPointer = Pointer.extend({\n            init: function (scale, options) {\n                Pointer.fn.init.call(this, scale, options);\n                this.options = deepExtend({ track: { visible: defined(options.track) } }, this.options);\n            },\n            reflow: function () {\n                var ref = this;\n                var options = ref.options;\n                var scale = ref.scale;\n                var ref$1 = scale.options;\n                var mirror = ref$1.mirror;\n                var vertical = ref$1.vertical;\n                var scaleLine = scale.lineBox();\n                var trackSize = options.track.size || options.size;\n                var pointerHalfSize = options.size / 2;\n                var margin = getSpacing(options.margin);\n                var space = vertical ? margin[mirror ? 'left' : 'right'] : margin[mirror ? 'bottom' : 'top'];\n                var pointerBox, pointerRangeBox, trackBox;\n                space = mirror ? -space : space;\n                if (vertical) {\n                    trackBox = new Box(scaleLine.x1 + space, scaleLine.y1, scaleLine.x1 + space, scaleLine.y2);\n                    if (mirror) {\n                        trackBox.x1 -= trackSize;\n                    } else {\n                        trackBox.x2 += trackSize;\n                    }\n                    if (options.shape !== BAR_POINTER) {\n                        pointerRangeBox = new Box(scaleLine.x2 + space, scaleLine.y1 - pointerHalfSize, scaleLine.x2 + space, scaleLine.y2 + pointerHalfSize);\n                        pointerBox = pointerRangeBox;\n                    }\n                } else {\n                    trackBox = new Box(scaleLine.x1, scaleLine.y1 - space, scaleLine.x2, scaleLine.y1 - space);\n                    if (mirror) {\n                        trackBox.y2 += trackSize;\n                    } else {\n                        trackBox.y1 -= trackSize;\n                    }\n                    if (options.shape !== BAR_POINTER) {\n                        pointerRangeBox = new Box(scaleLine.x1 - pointerHalfSize, scaleLine.y1 - space, scaleLine.x2 + pointerHalfSize, scaleLine.y1 - space);\n                        pointerBox = pointerRangeBox;\n                    }\n                }\n                this.trackBox = trackBox;\n                this.pointerRangeBox = pointerRangeBox;\n                this.box = pointerBox || trackBox.clone().pad(options.border.width);\n            },\n            getElementOptions: function () {\n                var options = this.options;\n                return {\n                    fill: {\n                        color: options.color,\n                        opacity: options.opacity\n                    },\n                    stroke: defined(options.border) ? {\n                        color: options.border.width ? options.border.color || options.color : '',\n                        width: options.border.width,\n                        dashType: options.border.dashType,\n                        opacity: options.opacity\n                    } : null\n                };\n            },\n            _margin: function () {\n                var ref = this;\n                var scale = ref.scale;\n                var options = ref.options;\n                var ref$1 = scale.options;\n                var mirror = ref$1.mirror;\n                var vertical = ref$1.vertical;\n                var margin = getSpacing(options.margin);\n                var space = vertical ? margin[mirror ? 'left' : 'right'] : margin[mirror ? 'bottom' : 'top'];\n                return space;\n            }\n        });\n        setDefaultOptions(LinearPointer, {\n            shape: BAR_POINTER,\n            track: { border: { width: 1 } },\n            color: BLACK,\n            border: { width: 1 },\n            opacity: 1,\n            margin: getSpacing(3),\n            animation: { type: BAR_POINTER },\n            visible: true\n        });\n        var ArrowLinearPointerAnimation = Animation.extend({\n            setup: function () {\n                var options = this.options;\n                var margin = options.margin;\n                var from = options.from;\n                var to = options.to;\n                var vertical = options.vertical;\n                var axis = vertical ? 'x1' : 'y1';\n                if (options.mirror === vertical) {\n                    from[axis] -= margin;\n                    to[axis] -= margin;\n                } else {\n                    from[axis] += margin;\n                    to[axis] += margin;\n                }\n                var fromScale = this.fromScale = new GeometryPoint(from.x1, from.y1);\n                var toScale = this.toScale = new GeometryPoint(to.x1, to.y1);\n                if (options.duration !== 0) {\n                    options.duration = Math.max(fromScale.distanceTo(toScale) / options.duration * 1000, 1);\n                }\n            },\n            step: function (pos) {\n                var translateX = interpolateValue(this.fromScale.x, this.toScale.x, pos);\n                var translateY = interpolateValue(this.fromScale.y, this.toScale.y, pos);\n                this.element.transform(transform().translate(translateX, translateY));\n            }\n        });\n        setDefaultOptions(ArrowLinearPointerAnimation, {\n            easing: LINEAR,\n            duration: LINEAR_SPEED\n        });\n        AnimationFactory.current.register(ARROW_POINTER, ArrowLinearPointerAnimation);\n        var Point$1 = GeometryPoint;\n        var Path$3 = DrawingPath;\n        var ArrowLinearPointer = LinearPointer.extend({\n            init: function (scale, options) {\n                LinearPointer.fn.init.call(this, scale, options);\n                if (!defined(this.options.size)) {\n                    this.options.size = this.scale.options.majorTicks.size * 0.6;\n                }\n            },\n            pointerShape: function () {\n                var ref = this;\n                var scale = ref.scale;\n                var size = ref.options.size;\n                var halfSize = size / 2;\n                var sign = scale.options.mirror ? -1 : 1;\n                var shape;\n                if (scale.options.vertical) {\n                    shape = [\n                        new Point$1(0, 0 - halfSize),\n                        new Point$1(0 - sign * size, 0),\n                        new Point$1(0, 0 + halfSize)\n                    ];\n                } else {\n                    shape = [\n                        new Point$1(0 - halfSize, 0),\n                        new Point$1(0, 0 + sign * size),\n                        new Point$1(0 + halfSize, 0)\n                    ];\n                }\n                return shape;\n            },\n            repaint: function () {\n                var ref = this;\n                var scale = ref.scale;\n                var options = ref.options;\n                var animation = new ArrowLinearPointerAnimation(this.elements, deepExtend(options.animation, {\n                    vertical: scale.options.vertical,\n                    mirror: scale.options.mirror,\n                    margin: this._margin(options.margin),\n                    from: scale.getSlot(options._oldValue),\n                    to: scale.getSlot(options.value)\n                }));\n                if (options.animation.transitions === false) {\n                    animation.options.duration = 0;\n                }\n                animation.setup();\n                animation.play();\n            },\n            render: function () {\n                var ref = this;\n                var scale = ref.scale;\n                var options = ref.options;\n                var elementOptions = this.getElementOptions();\n                var shape = this.pointerShape(options.value);\n                options.animation.type = ARROW_POINTER;\n                var elements = new Path$3({\n                    stroke: elementOptions.stroke,\n                    fill: elementOptions.fill\n                }).moveTo(shape[0]).lineTo(shape[1]).lineTo(shape[2]).close();\n                var slot = scale.getSlot(options.value);\n                elements.transform(transform().translate(slot.x1, slot.y1));\n                this.elements = elements;\n                return elements;\n            }\n        });\n        var BarLinearPointerAnimation = Animation.extend({\n            setup: function () {\n                var options = this.options;\n                var axis = this.axis = options.vertical ? constants.Y : constants.X;\n                var to = this.to = options.newPoints[0][axis];\n                var from = this.from = options.oldPoints[0][axis];\n                if (options.duration !== 0) {\n                    options.duration = Math.max(Math.abs(to - from) / options.speed * 1000, 1);\n                }\n                this._set(from);\n            },\n            step: function (pos) {\n                var value = interpolateValue(this.from, this.to, pos);\n                this._set(value);\n            },\n            _set: function (value) {\n                var setter = 'set' + this.axis.toUpperCase();\n                var points = this.options.newPoints;\n                points[0][setter](value);\n                points[1][setter](value);\n            }\n        });\n        setDefaultOptions(BarLinearPointerAnimation, {\n            easing: LINEAR,\n            speed: LINEAR_SPEED\n        });\n        AnimationFactory.current.register(BAR_POINTER, BarLinearPointerAnimation);\n        var Group$3 = DrawingGroup;\n        var Path$4 = DrawingPath;\n        var BarLinearPointer = LinearPointer.extend({\n            init: function (scale, options) {\n                LinearPointer.fn.init.call(this, scale, options);\n                if (!defined(this.options.size)) {\n                    this.options.size = this.scale.options.majorTicks.size * 0.3;\n                }\n            },\n            pointerShape: function (value) {\n                var ref = this;\n                var scale = ref.scale;\n                var options = ref.options;\n                var ref$1 = scale.options;\n                var mirror = ref$1.mirror;\n                var vertical = ref$1.vertical;\n                var dir = mirror === vertical ? -1 : 1;\n                var size = options.size * dir;\n                var minSlot = scale.getSlot(scale.options.min);\n                var slot = scale.getSlot(value);\n                var axis = vertical ? constants.Y : constants.X;\n                var sizeAxis = vertical ? constants.X : constants.Y;\n                var margin = this._margin() * dir;\n                var p1 = new GeometryPoint();\n                p1[axis] = minSlot[axis + '1'];\n                p1[sizeAxis] = minSlot[sizeAxis + '1'];\n                var p2 = new GeometryPoint();\n                p2[axis] = slot[axis + '1'];\n                p2[sizeAxis] = slot[sizeAxis + '1'];\n                if (vertical) {\n                    p1.translate(margin, 0);\n                    p2.translate(margin, 0);\n                } else {\n                    p1.translate(0, margin);\n                    p2.translate(0, margin);\n                }\n                var p3 = p2.clone();\n                var p4 = p1.clone();\n                if (vertical) {\n                    p3.translate(size, 0);\n                    p4.translate(size, 0);\n                } else {\n                    p3.translate(0, size);\n                    p4.translate(0, size);\n                }\n                return [\n                    p1,\n                    p2,\n                    p3,\n                    p4\n                ];\n            },\n            repaint: function () {\n                var ref = this;\n                var scale = ref.scale;\n                var options = ref.options;\n                var shape = this.pointerShape(options.value);\n                var pointerPath = this.pointerPath;\n                var oldShape = this.pointerShape(options._oldValue);\n                pointerPath.moveTo(shape[0]).lineTo(shape[1]).lineTo(shape[2]).lineTo(shape[3]).close();\n                var animation = new BarLinearPointerAnimation(pointerPath, deepExtend(options.animation, {\n                    reverse: scale.options.reverse,\n                    vertical: scale.options.vertical,\n                    oldPoints: [\n                        oldShape[1],\n                        oldShape[2]\n                    ],\n                    newPoints: [\n                        shape[1],\n                        shape[2]\n                    ]\n                }));\n                if (options.animation.transitions === false) {\n                    animation.options.duration = 0;\n                }\n                animation.setup();\n                animation.play();\n            },\n            render: function () {\n                var group = new Group$3();\n                var elementOptions = this.getElementOptions();\n                if (this.options.track.visible) {\n                    group.append(this.renderTrack());\n                }\n                var pointer = this.pointerPath = new Path$4({\n                    stroke: elementOptions.stroke,\n                    fill: elementOptions.fill\n                });\n                group.append(pointer);\n                this.elements = group;\n                return group;\n            },\n            renderTrack: function () {\n                var trackOptions = this.options.track;\n                var border = trackOptions.border || {};\n                var trackBox = this.trackBox.clone().pad(border.width || 0);\n                return new Path$4.fromRect(trackBox.toRect(), {\n                    fill: {\n                        color: trackOptions.color,\n                        opacity: trackOptions.opacity\n                    },\n                    stroke: {\n                        color: border.width ? border.color || trackOptions.color : '',\n                        width: border.width,\n                        dashType: border.dashType\n                    }\n                });\n            }\n        });\n        var DEFAULT_MIN_WIDTH = 60;\n        var DEFAULT_MIN_HEIGHT = 60;\n        var Group$1 = DrawingGroup;\n        var LinearGauge = Gauge.extend({\n            reflow: function (bbox) {\n                var pointers = this.pointers;\n                var bboxX = bbox.origin.x;\n                var bboxY = bbox.origin.y;\n                var box = new Box(bboxX, bboxY, bboxX + bbox.width(), bboxY + bbox.height());\n                this.scale.reflow(box);\n                this._shrinkScaleWidth(box);\n                for (var i = 0; i < pointers.length; i++) {\n                    pointers[i].reflow();\n                }\n                this.bbox = this._getBox(box);\n                this._alignElements();\n                this._shrinkElements();\n                this._buildVisual();\n                this._draw();\n            },\n            _buildVisual: function () {\n                var visuals = new Group$1();\n                var scaleElements = this.scale.render();\n                var pointers = this.pointers;\n                visuals.append(this.gaugeArea);\n                visuals.append(scaleElements);\n                for (var i = 0; i < pointers.length; i++) {\n                    var current = pointers[i];\n                    visuals.append(current.render());\n                    current.value(current.options.value);\n                }\n                this._visuals = visuals;\n            },\n            _createModel: function () {\n                var this$1 = this;\n                var options = this.options;\n                var scale = this.scale = new LinearScale(options.scale, this.contextService);\n                this.pointers = [];\n                var pointers = options.pointer;\n                pointers = isArray(pointers) ? pointers : [pointers];\n                for (var i = 0; i < pointers.length; i++) {\n                    var currentOptions = deepExtend({}, pointers[i], { animation: { transitions: options.transitions } });\n                    var pointerType = currentOptions.shape === ARROW ? ArrowLinearPointer : BarLinearPointer;\n                    this$1.pointers.push(new pointerType(scale, currentOptions));\n                }\n            },\n            _defaultSize: function () {\n                var vertical = this.options.scale.vertical;\n                return {\n                    width: vertical ? DEFAULT_MIN_WIDTH : DEFAULT_WIDTH,\n                    height: vertical ? DEFAULT_HEIGHT : DEFAULT_MIN_HEIGHT\n                };\n            },\n            _getBox: function (box) {\n                var ref = this;\n                var scale = ref.scale;\n                var pointers = ref.pointers;\n                var boxCenter = box.center();\n                var plotAreaBox = pointers[0].box.clone().wrap(scale.box);\n                for (var i = 0; i < pointers.length; i++) {\n                    plotAreaBox.wrap(pointers[i].box.clone());\n                }\n                var size;\n                if (scale.options.vertical) {\n                    size = plotAreaBox.width() / 2;\n                    plotAreaBox = new Box(boxCenter.x - size, box.y1, boxCenter.x + size, box.y2);\n                } else {\n                    size = plotAreaBox.height() / 2;\n                    plotAreaBox = new Box(box.x1, boxCenter.y - size, box.x2, boxCenter.y + size);\n                }\n                return plotAreaBox;\n            },\n            _alignElements: function () {\n                var this$1 = this;\n                var ref = this;\n                var scale = ref.scale;\n                var pointers = ref.pointers;\n                var scaleBox = scale.box;\n                var box = pointers[0].box.clone().wrap(scale.box);\n                var plotAreaBox = this.bbox;\n                for (var i = 0; i < pointers.length; i++) {\n                    box.wrap(pointers[i].box.clone());\n                }\n                var diff;\n                if (scale.options.vertical) {\n                    diff = plotAreaBox.center().x - box.center().x;\n                    scale.reflow(new Box(scaleBox.x1 + diff, plotAreaBox.y1, scaleBox.x2 + diff, plotAreaBox.y2));\n                } else {\n                    diff = plotAreaBox.center().y - box.center().y;\n                    scale.reflow(new Box(scaleBox.x1, scaleBox.y1 + diff, scaleBox.x2, scaleBox.y2 + diff));\n                }\n                for (var i$1 = 0; i$1 < pointers.length; i$1++) {\n                    pointers[i$1].reflow(this$1.bbox);\n                }\n            },\n            _shrinkScaleWidth: function (bbox) {\n                var ref = this;\n                var scale = ref.scale;\n                if (!scale.options.vertical) {\n                    var overflow = scale.contentBox().width() - bbox.width();\n                    if (overflow > 0) {\n                        scale.box.shrink(overflow, 0);\n                        scale.box.alignTo(bbox, 'center');\n                        scale.reflow(scale.box);\n                    }\n                }\n            },\n            _shrinkElements: function () {\n                var this$1 = this;\n                var ref = this;\n                var scale = ref.scale;\n                var pointers = ref.pointers;\n                var scaleBox = scale.box.clone();\n                var pos = scale.options.vertical ? 'y' : 'x';\n                var pointerBox = pointers[0].box;\n                for (var i = 0; i < pointers.length; i++) {\n                    pointerBox.wrap(pointers[i].box.clone());\n                }\n                scaleBox[pos + 1] += Math.max(scaleBox[pos + 1] - pointerBox[pos + 1], 0);\n                scaleBox[pos + 2] -= Math.max(pointerBox[pos + 2] - scaleBox[pos + 2], 0);\n                scale.reflow(scaleBox);\n                for (var i$1 = 0; i$1 < pointers.length; i$1++) {\n                    pointers[i$1].reflow(this$1.bbox);\n                }\n            }\n        });\n        setDefaultOptions(LinearGauge, {\n            transitions: true,\n            gaugeArea: { background: '' },\n            scale: { vertical: true }\n        });\n        var GEO_ARC_ADJUST_ANGLE = 180;\n        var Arc = drawing.Arc;\n        var Path$5 = DrawingPath;\n        var Group$5 = DrawingGroup;\n        function drawTicks(arc, tickAngles, unit, tickOptions) {\n            var ticks = new Group$5();\n            var center = arc.center;\n            var radius = arc.getRadiusX();\n            if (tickOptions.visible) {\n                for (var i = 0; i < tickAngles.length; i++) {\n                    var tickStart = arc.pointAt(tickAngles[i]);\n                    var tickEnd = new GeometryPoint(center.x + radius - tickOptions.size, center.y).rotate(tickAngles[i], center);\n                    ticks.append(new Path$5({\n                        stroke: {\n                            color: tickOptions.color,\n                            width: tickOptions.width\n                        }\n                    }).moveTo(tickStart).lineTo(tickEnd));\n                }\n            }\n            return ticks;\n        }\n        function rangeSegment(from, to, color, opacity) {\n            return {\n                from: from,\n                to: to,\n                color: color,\n                opacity: opacity\n            };\n        }\n        var RadialScale = NumericAxis.extend({\n            init: function (options, service) {\n                NumericAxis.fn.init.call(this, 0, 1, options, service);\n            },\n            initUserOptions: function (options) {\n                var scaleOptions = deepExtend({}, this.options, options);\n                scaleOptions.majorUnit = scaleOptions.majorUnit || dataviz.autoMajorUnit(scaleOptions.min, scaleOptions.max);\n                scaleOptions.minorUnit = scaleOptions.minorUnit || scaleOptions.majorUnit / 10;\n                return scaleOptions;\n            },\n            initFields: function () {\n            },\n            render: function (center, radius) {\n                var arc = this.renderArc(center, radius);\n                this.bbox = arc.bbox();\n                this.labelElements = this.renderLabels();\n                this.ticks = this.renderTicks();\n                this.ranges = this.renderRanges();\n            },\n            reflow: function (bbox) {\n                var center = bbox.center();\n                var radius = Math.min(bbox.height(), bbox.width()) / 2;\n                if (defined(this.bbox)) {\n                    this.bbox = this.arc.bbox();\n                    this.radius(this.arc.getRadiusX());\n                    this.repositionRanges();\n                    this.renderLabels();\n                } else {\n                    return this.render(center, radius);\n                }\n            },\n            slotAngle: function (value) {\n                var ref = this.options;\n                var min = ref.min;\n                var max = ref.max;\n                var reverse = ref.reverse;\n                var startAngle = ref.startAngle;\n                var endAngle = ref.endAngle;\n                var angle = endAngle - startAngle;\n                var result;\n                if (reverse) {\n                    result = endAngle - (value - min) / (max - min) * angle;\n                } else {\n                    result = (value - min) / (max - min) * angle + startAngle;\n                }\n                return result + GEO_ARC_ADJUST_ANGLE;\n            },\n            hasRanges: function () {\n                var ranges = this.options.ranges;\n                return ranges && ranges.length;\n            },\n            ticksSize: function () {\n                var ref = this.options;\n                var majorTicks = ref.majorTicks;\n                var minorTicks = ref.minorTicks;\n                var size = 0;\n                if (majorTicks.visible) {\n                    size = majorTicks.size;\n                }\n                if (minorTicks.visible) {\n                    size = Math.max(minorTicks.size, size);\n                }\n                return size;\n            },\n            renderLabels: function () {\n                var this$1 = this;\n                var options = this.options;\n                var arc = this.arc.clone();\n                var radius = arc.getRadiusX();\n                var tickAngles = this.tickAngles(arc, options.majorUnit);\n                var rangeSize = options.rangeSize = options.rangeSize || radius * 0.1;\n                var labelsGroup = new Group$5();\n                var rangeDistance = radius * 0.05;\n                if (defined(options.rangeDistance)) {\n                    rangeDistance = options.rangeDistance;\n                } else {\n                    options.rangeDistance = rangeDistance;\n                }\n                var labelsOptions = options.labels;\n                var isInside = labelsOptions.position === INSIDE;\n                var hasLabelElements = defined(this.labelElements);\n                if (isInside) {\n                    radius -= this.ticksSize();\n                    if (this.hasRanges() && !hasLabelElements) {\n                        radius -= rangeSize + rangeDistance;\n                    }\n                    arc.setRadiusX(radius).setRadiusY(radius);\n                }\n                var labels = this.labels;\n                var count = labels.length;\n                var padding = labelsOptions.padding;\n                for (var i = 0; i < count; i++) {\n                    var label = labels[i];\n                    var halfWidth = label.box.width() / 2;\n                    var halfHeight = label.box.height() / 2;\n                    var angle = tickAngles[i];\n                    var labelAngle = (angle - GEO_ARC_ADJUST_ANGLE) * DEGREE;\n                    var lp = arc.pointAt(angle);\n                    var cx = lp.x + Math.cos(labelAngle) * (halfWidth + padding) * (isInside ? 1 : -1);\n                    var cy = lp.y + Math.sin(labelAngle) * (halfHeight + padding) * (isInside ? 1 : -1);\n                    label.reflow(new Box(cx - halfWidth, cy - halfHeight, cx + halfWidth, cy + halfHeight));\n                    var labelPos = new GeometryPoint(label.box.x1, label.box.y1);\n                    var labelElement = void 0;\n                    if (!hasLabelElements) {\n                        labelElement = buildLabelElement(label, options.labels);\n                        labelsGroup.append(labelElement);\n                    } else {\n                        labelElement = this$1.labelElements.children[i];\n                        var prevLabelPos = labelElement.bbox().origin;\n                        var labelTransform = labelElement.transform() || transform();\n                        labelTransform.translate(labelPos.x - prevLabelPos.x, labelPos.y - prevLabelPos.y);\n                        labelElement.transform(labelTransform);\n                    }\n                    this$1.bbox = Rect.union(this$1.bbox, labelElement.bbox());\n                }\n                return labelsGroup;\n            },\n            repositionRanges: function () {\n                var ranges = this.ranges.children;\n                if (ranges.length > 0) {\n                    var ref = this.options;\n                    var rangeDistance = ref.rangeDistance;\n                    var rangeSize = ref.rangeSize;\n                    var rangeRadius = this.getRangeRadius();\n                    if (this.options.labels.position === INSIDE) {\n                        rangeRadius += rangeSize + rangeDistance;\n                    }\n                    var newRadius = rangeRadius + rangeSize / 2;\n                    for (var i = 0; i < ranges.length; i++) {\n                        ranges[i]._geometry.setRadiusX(newRadius).setRadiusY(newRadius);\n                    }\n                    this.bbox = Rect.union(this.bbox, this.ranges.bbox());\n                }\n            },\n            renderRanges: function () {\n                var this$1 = this;\n                var segments = this.rangeSegments();\n                var segmentsCount = segments.length;\n                var result = new Group$5();\n                if (segmentsCount) {\n                    var ref = this.options;\n                    var rangeSize = ref.rangeSize;\n                    var reverse = ref.reverse;\n                    var rangeDistance = ref.rangeDistance;\n                    var rangeRadius = this.getRangeRadius();\n                    this.radius(this.radius() - rangeSize - rangeDistance);\n                    for (var i = 0; i < segmentsCount; i++) {\n                        var segment = segments[i];\n                        var from = this$1.slotAngle(segment[reverse ? 'to' : 'from']);\n                        var to = this$1.slotAngle(segment[!reverse ? 'to' : 'from']);\n                        if (to - from !== 0) {\n                            result.append(this$1.createRange(from, to, rangeRadius, segment));\n                        }\n                    }\n                }\n                return result;\n            },\n            createRange: function (startAngle, endAngle, rangeRadius, options) {\n                var rangeSize = this.options.rangeSize;\n                var rangeGeom = new geometry.Arc(this.arc.center, {\n                    radiusX: rangeRadius + rangeSize / 2,\n                    radiusY: rangeRadius + rangeSize / 2,\n                    startAngle: startAngle,\n                    endAngle: endAngle\n                });\n                return new Arc(rangeGeom, {\n                    stroke: {\n                        width: rangeSize,\n                        color: options.color,\n                        opacity: options.opacity,\n                        lineCap: options.lineCap\n                    }\n                });\n            },\n            rangeSegments: function () {\n                var options = this.options;\n                var ranges = options.ranges || [];\n                var count = ranges.length;\n                var segments = [];\n                if (count) {\n                    var min = options.min;\n                    var max = options.max;\n                    var defaultColor = options.rangePlaceholderColor;\n                    segments.push(rangeSegment(min, max, defaultColor));\n                    for (var i = 0; i < count; i++) {\n                        var range = getRange(ranges[i], min, max);\n                        var segmentsCount = segments.length;\n                        for (var j = 0; j < segmentsCount; j++) {\n                            var segment = segments[j];\n                            if (segment.from <= range.from && range.from <= segment.to) {\n                                segments.push(rangeSegment(range.from, range.to, range.color, range.opacity));\n                                if (segment.from <= range.to && range.to <= segment.to) {\n                                    segments.push(rangeSegment(range.to, segment.to, defaultColor, range.opacity));\n                                }\n                                segment.to = range.from;\n                                break;\n                            }\n                        }\n                    }\n                }\n                return segments;\n            },\n            getRangeRadius: function () {\n                var ref = this;\n                var arc = ref.arc;\n                var options = ref.options;\n                var rangeSize = options.rangeSize;\n                var rangeDistance = options.rangeDistance;\n                var majorTickSize = options.majorTicks.size;\n                var radius;\n                if (options.labels.position === OUTSIDE) {\n                    radius = arc.getRadiusX() - majorTickSize - rangeDistance - rangeSize;\n                } else {\n                    radius = arc.getRadiusX() - rangeSize;\n                }\n                return radius;\n            },\n            renderArc: function (center, radius) {\n                var options = this.options;\n                var arc = this.arc = new geometry.Arc(center, {\n                    radiusX: radius,\n                    radiusY: radius,\n                    startAngle: options.startAngle + GEO_ARC_ADJUST_ANGLE,\n                    endAngle: options.endAngle + GEO_ARC_ADJUST_ANGLE\n                });\n                return arc;\n            },\n            renderTicks: function () {\n                var ref = this;\n                var arc = ref.arc;\n                var options = ref.options;\n                var tickArc = arc.clone();\n                this.majorTickAngles = this.tickAngles(arc, options.majorUnit);\n                this.majorTicks = drawTicks(tickArc, this.majorTickAngles, options.majorUnit, options.majorTicks);\n                var allTicks = new Group$5();\n                allTicks.append(this.majorTicks);\n                var majorTickSize = options.majorTicks.size;\n                var minorTickSize = options.minorTicks.size;\n                this._tickDifference = majorTickSize - minorTickSize;\n                if (options.labels.position === OUTSIDE) {\n                    var radius = tickArc.getRadiusX();\n                    tickArc.setRadiusX(radius - majorTickSize + minorTickSize).setRadiusY(radius - majorTickSize + minorTickSize);\n                }\n                this.minorTickAngles = this.normalizeTickAngles(this.tickAngles(arc, options.minorUnit));\n                this.minorTicks = drawTicks(tickArc, this.minorTickAngles, options.minorUnit, options.minorTicks);\n                allTicks.append(this.minorTicks);\n                return allTicks;\n            },\n            normalizeTickAngles: function (angles) {\n                var options = this.options;\n                var skip = options.majorUnit / options.minorUnit;\n                for (var i = angles.length - 1; i >= 0; i--) {\n                    if (i % skip === 0) {\n                        angles.splice(i, 1);\n                    }\n                }\n                return angles;\n            },\n            tickAngles: function (ring, stepValue) {\n                var options = this.options;\n                var reverse = options.reverse;\n                var range = options.max - options.min;\n                var angle = ring.endAngle - ring.startAngle;\n                var tickCount = range / stepValue;\n                var pos = ring.startAngle;\n                var step = angle / tickCount;\n                if (reverse) {\n                    pos += angle;\n                    step = -step;\n                }\n                var positions = [];\n                for (var i = 0; i < tickCount; i++) {\n                    positions.push(round(pos, COORD_PRECISION));\n                    pos += step;\n                }\n                if (round(pos) <= ring.endAngle) {\n                    positions.push(pos);\n                }\n                return positions;\n            },\n            radius: function (value) {\n                if (value) {\n                    this.arc.setRadiusX(value).setRadiusY(value);\n                    this.repositionTicks(this.majorTicks.children, this.majorTickAngles);\n                    this.repositionTicks(this.minorTicks.children, this.minorTickAngles, true);\n                } else {\n                    return this.arc.getRadiusX();\n                }\n            },\n            repositionTicks: function (ticks, tickAngles, minor) {\n                var diff = minor ? this._tickDifference || 0 : 0;\n                var tickArc = this.arc;\n                var radius = tickArc.getRadiusX();\n                if (minor && this.options.labels.position === OUTSIDE && diff !== 0) {\n                    tickArc = this.arc.clone();\n                    tickArc.setRadiusX(radius - diff).setRadiusY(radius - diff);\n                }\n                for (var i = 0; i < ticks.length; i++) {\n                    var newPoint = tickArc.pointAt(tickAngles[i]);\n                    var segments = ticks[i].segments;\n                    var xDiff = newPoint.x - segments[0].anchor().x;\n                    var yDiff = newPoint.y - segments[0].anchor().y;\n                    ticks[i].transform(new transform().translate(xDiff, yDiff));\n                }\n            }\n        });\n        setDefaultOptions(RadialScale, {\n            min: 0,\n            max: 100,\n            majorTicks: {\n                size: 15,\n                align: INSIDE,\n                color: BLACK,\n                width: DEFAULT_LINE_WIDTH,\n                visible: true\n            },\n            minorTicks: {\n                size: 10,\n                align: INSIDE,\n                color: BLACK,\n                width: DEFAULT_LINE_WIDTH,\n                visible: true\n            },\n            startAngle: -30,\n            endAngle: 210,\n            labels: {\n                position: INSIDE,\n                padding: 2\n            }\n        });\n        var RadialPointerAnimation = Animation.extend({\n            init: function (element, options) {\n                Animation.fn.init.call(this, element, options);\n                var animationOptions = this.options;\n                animationOptions.duration = Math.max(Math.abs(animationOptions.newAngle - animationOptions.oldAngle) / animationOptions.duration * 1000, 1);\n            },\n            step: function (pos) {\n                var options = this.options;\n                var angle = interpolateValue(options.oldAngle, options.newAngle, pos);\n                this.element.transform(transform().rotate(angle, options.center));\n            }\n        });\n        setDefaultOptions(RadialPointerAnimation, {\n            easing: LINEAR,\n            duration: ANGULAR_SPEED\n        });\n        AnimationFactory.current.register(RADIAL_POINTER, RadialPointerAnimation);\n        var CAP_SIZE = 0.05;\n        var Circle = drawing.Circle;\n        var Group$6 = DrawingGroup;\n        var Path$6 = DrawingPath;\n        var RadialPointer = Pointer.extend({\n            setAngle: function (angle) {\n                this.elements.transform(transform().rotate(angle, this.center));\n            },\n            repaint: function () {\n                var ref = this;\n                var scale = ref.scale;\n                var options = ref.options;\n                var oldAngle = scale.slotAngle(options._oldValue);\n                var newAngle = scale.slotAngle(options.value);\n                if (options.animation.transitions === false) {\n                    this.setAngle(newAngle);\n                } else {\n                    new RadialPointerAnimation(this.elements, deepExtend(options.animation, {\n                        oldAngle: oldAngle,\n                        newAngle: newAngle\n                    })).play();\n                }\n            },\n            render: function () {\n                var ref = this;\n                var scale = ref.scale;\n                var options = ref.options;\n                var elements = new Group$6();\n                if (options.animation !== false) {\n                    deepExtend(options.animation, {\n                        startAngle: 0,\n                        center: scale.arc.center,\n                        reverse: scale.options.reverse\n                    });\n                }\n                elements.append(this._renderNeedle(), this._renderCap());\n                this.elements = elements;\n                this.setAngle(DEGREE);\n                return elements;\n            },\n            reflow: function (arc) {\n                var center = this.center = arc.center;\n                var length = limitValue(this.options.length || 1, 0.1, 1.5);\n                var radius = this.radius = arc.getRadiusX() * length;\n                var capSize = this.capSize = Math.round(radius * this.options.cap.size);\n                this.bbox = Rect.fromPoints(new GeometryPoint(center.x - capSize, center.y - capSize), new GeometryPoint(center.x + capSize, center.y + capSize));\n            },\n            _renderNeedle: function () {\n                var minorTickSize = this.scale.options.minorTicks.size;\n                var center = this.center;\n                var needleColor = this.options.color;\n                var needlePath = new Path$6({\n                    fill: { color: needleColor },\n                    stroke: {\n                        color: needleColor,\n                        width: DEFAULT_LINE_WIDTH\n                    }\n                });\n                needlePath.moveTo(center.x + this.radius - minorTickSize, center.y).lineTo(center.x, center.y - this.capSize / 2).lineTo(center.x, center.y + this.capSize / 2).close();\n                return needlePath;\n            },\n            _renderCap: function () {\n                var options = this.options;\n                var capColor = options.cap.color || options.color;\n                var circle = new geometry.Circle(this.center, this.capSize);\n                var cap = new Circle(circle, {\n                    fill: { color: capColor },\n                    stroke: { color: capColor }\n                });\n                return cap;\n            }\n        });\n        setDefaultOptions(RadialPointer, {\n            cap: { size: CAP_SIZE },\n            arrow: {\n                width: 16,\n                height: 14\n            },\n            animation: {\n                type: RADIAL_POINTER,\n                duration: ANGULAR_SPEED\n            }\n        });\n        var Group$4 = DrawingGroup;\n        var RadialGauge = Gauge.extend({\n            reflow: function (bbox) {\n                var this$1 = this;\n                var pointers = this.pointers;\n                this.scale.reflow(bbox);\n                this._initialPlotArea = this.scale.bbox;\n                for (var i = 0; i < pointers.length; i++) {\n                    pointers[i].reflow(this$1.scale.arc);\n                    this$1._initialPlotArea = Rect.union(this$1._initialPlotArea, pointers[i].bbox);\n                }\n                this.fitScale(bbox);\n                this.alignScale(bbox);\n                this._buildVisual(this.gaugeArea, pointers, this.scale);\n                this._draw();\n            },\n            _buildVisual: function (gaugeArea, pointers, scale) {\n                var visuals = this._visuals = new Group$4();\n                visuals.append(gaugeArea);\n                visuals.append(scale.ticks);\n                visuals.append(scale.ranges);\n                this._buildPointers(pointers);\n                visuals.append(scale.labelElements);\n            },\n            _buildPointers: function (pointers) {\n                var this$1 = this;\n                for (var i = 0; i < pointers.length; i++) {\n                    var current = pointers[i];\n                    current.render();\n                    this$1._visuals.append(current.elements);\n                    current.value(current.options.value);\n                }\n            },\n            fitScale: function (bbox) {\n                var this$1 = this;\n                var arc = this.scale.arc;\n                var plotAreaBox = this._initialPlotArea;\n                var step = Math.abs(this.getDiff(plotAreaBox, bbox));\n                var min = round(step, COORD_PRECISION);\n                var max = round(-step, COORD_PRECISION);\n                var minDiff, midDiff, maxDiff, mid, oldDiff;\n                var staleFlag = 0;\n                var i = 0;\n                while (i++ < 100) {\n                    staleFlag = oldDiff === maxDiff ? staleFlag + 1 : 0;\n                    if (staleFlag > 5) {\n                        break;\n                    }\n                    if (min !== mid) {\n                        minDiff = this$1.getPlotBox(min, bbox, arc);\n                        if (0 <= minDiff && minDiff <= 2) {\n                            break;\n                        }\n                    }\n                    if (max !== mid) {\n                        maxDiff = this$1.getPlotBox(max, bbox, arc);\n                        if (0 <= maxDiff && maxDiff <= 2) {\n                            break;\n                        }\n                    }\n                    if (minDiff > 0 && maxDiff > 0) {\n                        mid = min * 2;\n                    } else if (minDiff < 0 && maxDiff < 0) {\n                        mid = max * 2;\n                    } else {\n                        mid = round((min + max) / 2 || 1, COORD_PRECISION);\n                    }\n                    midDiff = this$1.getPlotBox(mid, bbox, arc);\n                    if (0 <= midDiff && midDiff <= 2) {\n                        break;\n                    }\n                    oldDiff = maxDiff;\n                    if (midDiff > 0) {\n                        max = mid;\n                        maxDiff = midDiff;\n                    } else {\n                        min = mid;\n                        minDiff = midDiff;\n                    }\n                }\n            },\n            getPlotBox: function (step, bbox, arc) {\n                var this$1 = this;\n                var scale = this.scale;\n                var pointers = this.pointers;\n                var radius = arc.getRadiusX();\n                var scaleArc = arc.clone();\n                scaleArc.setRadiusX(radius + step).setRadiusY(radius + step);\n                scale.arc = scaleArc;\n                scale.reflow(bbox);\n                this.plotBbox = scale.bbox;\n                for (var i = 0; i < pointers.length; i++) {\n                    pointers[i].reflow(scaleArc);\n                    this$1.plotBbox = Rect.union(this$1.plotBbox, pointers[i].bbox);\n                }\n                return this.getDiff(this.plotBbox, bbox);\n            },\n            getDiff: function (plotBox, box) {\n                return Math.min(box.width() - plotBox.width(), box.height() - plotBox.height());\n            },\n            alignScale: function (bbox) {\n                var this$1 = this;\n                var plotBoxCenter = this.plotBbox.center();\n                var boxCenter = bbox.center();\n                var paddingX = plotBoxCenter.x - boxCenter.x;\n                var paddingY = plotBoxCenter.y - boxCenter.y;\n                var ref = this;\n                var scale = ref.scale;\n                var pointers = ref.pointers;\n                scale.arc.center.x -= paddingX;\n                scale.arc.center.y -= paddingY;\n                scale.reflow(bbox);\n                for (var i = 0; i < pointers.length; i++) {\n                    pointers[i].reflow(scale.arc);\n                    this$1.plotBbox = Rect.union(scale.bbox, pointers[i].bbox);\n                }\n            },\n            _createModel: function () {\n                var this$1 = this;\n                var options = this.options;\n                var pointers = options.pointer;\n                var scale = this.scale = new RadialScale(options.scale, this.contextService);\n                this.pointers = [];\n                var pointersArr = isArray(pointers) ? pointers : [pointers];\n                for (var i = 0; i < pointersArr.length; i++) {\n                    var current = new RadialPointer(scale, deepExtend({}, pointersArr[i], { animation: { transitions: options.transitions } }));\n                    this$1.pointers.push(current);\n                }\n            }\n        });\n        setDefaultOptions(RadialGauge, {\n            transitions: true,\n            gaugeArea: { background: '' }\n        });\n        var ArcScale = RadialScale.extend({\n            rangeSegments: function () {\n                var ref = this.options;\n                var min = ref.min;\n                var max = ref.max;\n                var rangePlaceholderColor = ref.rangePlaceholderColor;\n                var rangeLineCap = ref.rangeLineCap;\n                return [{\n                        from: min,\n                        to: max,\n                        color: rangePlaceholderColor,\n                        lineCap: rangeLineCap\n                    }];\n            },\n            hasRanges: function () {\n                return true;\n            },\n            placeholderRangeAngle: function (angle) {\n                var geometry$$1 = this.ranges.children[0].geometry();\n                if (this.options.reverse) {\n                    geometry$$1.setEndAngle(angle);\n                } else {\n                    geometry$$1.setStartAngle(angle);\n                }\n            },\n            addRange: function (from, to, options) {\n                var reverse = this.options.reverse;\n                var startAngle = this.slotAngle(reverse ? to : from);\n                var endAngle = this.slotAngle(reverse ? from : to);\n                var range = this.createRange(startAngle, endAngle, this.getRangeRadius(), options);\n                this.ranges.append(range);\n                return range;\n            }\n        });\n        setDefaultOptions(ArcScale, {\n            min: 0,\n            max: 100,\n            majorTicks: { visible: false },\n            minorTicks: { visible: false },\n            labels: { visible: false },\n            startAngle: 0,\n            endAngle: 180,\n            rangeLineCap: 'round'\n        });\n        var MAX_DURATION = 800;\n        var RangePointerAnimation = Animation.extend({\n            init: function (element, options) {\n                Animation.fn.init.call(this, element, options);\n                var animationOptions = this.options;\n                var duration = Math.abs(animationOptions.newAngle - animationOptions.oldAngle) / animationOptions.duration * 1000;\n                animationOptions.duration = limitValue(duration, ANGULAR_SPEED, MAX_DURATION);\n                var startColor = element.elements.options.get('stroke.color');\n                var color = element.currentColor();\n                if (startColor !== color) {\n                    this.startColor = new kendo.Color(startColor);\n                    this.color = new kendo.Color(color);\n                }\n            },\n            step: function (pos) {\n                var ref = this;\n                var options = ref.options;\n                var startColor = ref.startColor;\n                var color = ref.color;\n                var angle = interpolateValue(options.oldAngle, options.newAngle, pos);\n                this.element.angle(angle);\n                if (color) {\n                    var r = round(interpolateValue(startColor.r, color.r, pos));\n                    var g = round(interpolateValue(startColor.g, color.g, pos));\n                    var b = round(interpolateValue(startColor.b, color.b, pos));\n                    this.element.stroke(new kendo.Color(r, g, b).toHex());\n                }\n            }\n        });\n        setDefaultOptions(RangePointerAnimation, {\n            easing: LINEAR,\n            duration: ANGULAR_SPEED\n        });\n        AnimationFactory.current.register(RADIAL_RANGE_POINTER, RangePointerAnimation);\n        var RangePointer = Pointer.extend({\n            repaint: function () {\n                var ref = this;\n                var scale = ref.scale;\n                var options = ref.options;\n                var oldAngle = scale.slotAngle(options._oldValue);\n                var newAngle = scale.slotAngle(options.value);\n                if (this.animation) {\n                    this.animation.abort();\n                }\n                if (options.animation.transitions === false) {\n                    this.angle(newAngle);\n                    this.stroke(this.currentColor());\n                } else {\n                    this.animation = new RangePointerAnimation(this, deepExtend(options.animation, {\n                        oldAngle: oldAngle,\n                        newAngle: newAngle\n                    }));\n                    this.animation.play();\n                }\n            },\n            angle: function (value) {\n                var geometry$$1 = this.elements.geometry();\n                if (this.scale.options.reverse) {\n                    geometry$$1.setStartAngle(value);\n                } else {\n                    geometry$$1.setEndAngle(value);\n                }\n                this.scale.placeholderRangeAngle(value);\n            },\n            stroke: function (value) {\n                this.elements.stroke(value);\n            },\n            render: function () {\n                if (this.elements) {\n                    return;\n                }\n                var ref = this;\n                var scale = ref.scale;\n                var options = ref.options;\n                if (options.animation !== false) {\n                    deepExtend(options.animation, {\n                        startAngle: 0,\n                        center: scale.arc.center,\n                        reverse: scale.options.reverse\n                    });\n                }\n                this.elements = scale.addRange(scale.options.min, this.options.value, {\n                    color: this.currentColor(),\n                    opacity: options.opacity,\n                    lineCap: scale.options.rangeLineCap\n                });\n            },\n            currentColor: function () {\n                var ref = this.scale.options;\n                var min = ref.min;\n                var max = ref.max;\n                var ref$1 = this.options;\n                var colors = ref$1.colors;\n                var color = ref$1.color;\n                var value = ref$1.value;\n                var currentValue = dataviz.isNumber(value) ? value : min;\n                if (colors) {\n                    for (var idx = 0; idx < colors.length; idx++) {\n                        var ref$2 = colors[idx];\n                        var rangeColor = ref$2.color;\n                        var from = ref$2.from;\n                        if (from === void 0) {\n                            from = min;\n                        }\n                        var to = ref$2.to;\n                        if (to === void 0) {\n                            to = max;\n                        }\n                        if (from <= currentValue && currentValue <= to) {\n                            return rangeColor;\n                        }\n                    }\n                }\n                return color;\n            },\n            reflow: function () {\n                this.render();\n                this.bbox = this.elements.bbox();\n            }\n        });\n        setDefaultOptions(RangePointer, {\n            animation: {\n                type: RADIAL_RANGE_POINTER,\n                duration: ANGULAR_SPEED\n            }\n        });\n        var ArcGauge = RadialGauge.extend({\n            _initTheme: function (theme) {\n                RadialGauge.fn._initTheme.call(this, theme);\n                this.options.color = this.options.color || (this.theme.pointer || {}).color;\n            },\n            _createModel: function () {\n                var options = this.options;\n                var scale = this.scale = new ArcScale(options.scale, this.contextService);\n                var pointer = new RangePointer(scale, deepExtend({}, {\n                    colors: options.colors,\n                    color: options.color,\n                    value: options.value,\n                    opacity: options.opacity,\n                    animation: { transitions: options.transitions }\n                }));\n                this.pointers = [pointer];\n            },\n            _buildPointers: function (pointers) {\n                for (var i = 0; i < pointers.length; i++) {\n                    var current = pointers[i];\n                    current.render();\n                    current.value(current.options.value);\n                }\n            },\n            _setValueOptions: function (value) {\n                this.options.value = value;\n            },\n            currentColor: function () {\n                var pointer = this.pointers[0];\n                if (pointer) {\n                    return pointer.currentColor();\n                }\n            },\n            centerLabelPosition: function (width, height) {\n                var size = this.getSize();\n                var center = this.scale.arc.center;\n                var left = center.x - width / 2;\n                var top = center.y - height / 2;\n                if (width < size.width) {\n                    var right = left + width;\n                    left = Math.max(left, 0);\n                    if (right > size.width) {\n                        left -= right - size.width;\n                    }\n                }\n                if (height < size.height) {\n                    var bbox = this.scale.bbox;\n                    var yLimit = bbox.bottomRight().y;\n                    var bottom = top + height;\n                    top = Math.max(top, bbox.origin.y);\n                    if (bottom > yLimit) {\n                        top -= bottom - yLimit;\n                    }\n                }\n                return {\n                    left: left,\n                    top: top\n                };\n            }\n        });\n        kendo.deepExtend(kendo.dataviz, {\n            Gauge: Gauge,\n            LinearGauge: LinearGauge,\n            LinearPointer: LinearPointer,\n            ArrowLinearPointer: ArrowLinearPointer,\n            BarLinearPointer: BarLinearPointer,\n            LinearScale: LinearScale,\n            RadialGauge: RadialGauge,\n            RadialPointer: RadialPointer,\n            RadialScale: RadialScale,\n            ArcGauge: ArcGauge,\n            RangePointer: RangePointer,\n            ArcScale: ArcScale\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/gauge/main', ['dataviz/gauge/kendo-gauges'], f);\n}(function () {\n    (function ($) {\n        var kendo = window.kendo;\n        var Widget = kendo.ui.Widget;\n        var dataviz = kendo.dataviz;\n        var LinearGauge = dataviz.LinearGauge;\n        var RadialGauge = dataviz.RadialGauge;\n        var ArcGauge = dataviz.ArcGauge;\n        var draw = kendo.drawing;\n        function themeOptions(options) {\n            var themes = dataviz.ui.themes || {};\n            var themeName = options.theme || '';\n            var lowerName = themeName.toLowerCase();\n            if (dataviz.SASS_THEMES.indexOf(lowerName) != -1) {\n                return dataviz.autoTheme().gauge;\n            }\n            return (themes[themeName] || themes[lowerName] || {}).gauge;\n        }\n        var Gauge = Widget.extend({\n            init: function (element, userOptions) {\n                kendo.destroy(element);\n                $(element).empty();\n                Widget.fn.init.call(this, element);\n                this.options = kendo.deepExtend(this.options, userOptions);\n                this.wrapper = this.element;\n                this._createInstance();\n                this.element.addClass('k-gauge');\n                kendo.notify(this, dataviz.ui);\n            },\n            options: {\n                theme: 'default',\n                renderAs: '',\n                pointer: {},\n                scale: {},\n                gaugeArea: { background: '' },\n                transitions: true\n            },\n            setOptions: function (options) {\n                this._instance.setOptions(options, themeOptions(options));\n                this._copyFields();\n            },\n            redraw: function () {\n                this._instance.redraw();\n                this._copyFields();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this._instance.destroy();\n            },\n            _createInstance: function () {\n                var gaugeType = this._gaugeType();\n                this._instance = new gaugeType(this.element[0], this.options, themeOptions(this.options));\n                this._copyFields();\n            },\n            _copyFields: function () {\n                this._originalOptions = this._instance._originalOptions;\n                this.options = this._instance.options;\n                this.surface = this._instance.surface;\n                this.bbox = this._instance.bbox;\n                this.gaugeArea = this._instance.gaugeArea;\n                this.pointers = this._instance.pointers;\n                this.scale = this._instance.scale;\n            },\n            _resize: function () {\n                this._instance.resize();\n            }\n        });\n        var proxyMembers = [\n            'getSize',\n            'value',\n            'allValues',\n            'exportVisual'\n        ];\n        function createProxyMember(name) {\n            Gauge.fn[name] = function () {\n                return this._instance[name].apply(this._instance, arguments);\n            };\n        }\n        for (var idx = 0; idx < proxyMembers.length; idx++) {\n            createProxyMember(proxyMembers[idx]);\n        }\n        dataviz.ExportMixin.extend(Gauge.fn);\n        var RadialGaugeWidget = Gauge.extend({\n            options: { name: 'RadialGauge' },\n            _gaugeType: function () {\n                return RadialGauge;\n            }\n        });\n        var LinearGaugeWidget = Gauge.extend({\n            options: {\n                name: 'LinearGauge',\n                scale: { vertical: true }\n            },\n            _gaugeType: function () {\n                return LinearGauge;\n            }\n        });\n        var ArcGaugeWidget = Gauge.extend({\n            init: function (element, userOptions) {\n                Gauge.fn.init.call(this, element, userOptions);\n                this.element.css('position', 'relative');\n                this.element.addClass('k-arcgauge');\n                this._centerTemplate();\n            },\n            options: { name: 'ArcGauge' },\n            setOptions: function (options) {\n                Gauge.fn.setOptions.call(this, options);\n                this._centerTemplate();\n            },\n            redraw: function () {\n                Gauge.fn.redraw.call(this);\n                this._centerTemplate();\n            },\n            value: function (value) {\n                var instance = this._instance;\n                if (arguments.length === 0) {\n                    return instance.value();\n                }\n                instance.value(value);\n                this._centerTemplate();\n            },\n            destroy: function () {\n                Gauge.fn.destroy.call(this);\n                delete this._centerElement;\n            },\n            exportVisual: function () {\n                if (this._centerElement) {\n                    return false;\n                }\n                return Gauge.fn.exportVisual.call(this);\n            },\n            _resize: function () {\n                this._instance.resize();\n                this._centerTemplate();\n            },\n            _centerTemplate: function () {\n                if (this.options.centerTemplate) {\n                    var template = kendo.template(this.options.centerTemplate);\n                    var instance = this._instance;\n                    var centerElement = this._getCenterElement();\n                    centerElement.html(template({\n                        color: instance.currentColor(),\n                        value: instance.value()\n                    }));\n                    var position = instance.centerLabelPosition(centerElement.width(), centerElement.height());\n                    centerElement.css(position);\n                } else if (this._centerElement) {\n                    this._centerElement.remove();\n                    this._centerElement = null;\n                }\n            },\n            _getCenterElement: function () {\n                var centerElement = this._centerElement;\n                if (!centerElement) {\n                    centerElement = this._centerElement = $('<div></div>').addClass('k-arcgauge-label');\n                    this.element.append(centerElement);\n                }\n                return centerElement;\n            },\n            _gaugeType: function () {\n                return ArcGauge;\n            }\n        });\n        function createExportMethod(name) {\n            ArcGaugeWidget.fn[name] = function (options) {\n                var gauge = this;\n                var method = draw[name];\n                if (!gauge._centerElement) {\n                    return method(gauge.exportVisual(), options);\n                }\n                return draw.drawDOM(gauge.element).then(function (visual) {\n                    return method(visual, options);\n                });\n            };\n        }\n        var exportMethods = [\n            'exportSVG',\n            'exportImage',\n            'exportPDF'\n        ];\n        for (idx = 0; idx < exportMethods.length; idx++) {\n            createExportMethod(exportMethods[idx]);\n        }\n        dataviz.ui.plugin(LinearGaugeWidget);\n        dataviz.ui.plugin(RadialGaugeWidget);\n        dataviz.ui.plugin(ArcGaugeWidget);\n        kendo.deepExtend(dataviz, {\n            Gauge: Gauge,\n            LinearGauge: LinearGaugeWidget,\n            RadialGauge: RadialGaugeWidget,\n            ArcGauge: ArcGaugeWidget\n        });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dataviz.gauge', [\n        'dataviz/gauge/main',\n        'kendo.dataviz.themes'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dataviz.gauge',\n        name: 'Gauge',\n        category: 'dataviz',\n        description: 'Linear, Radial and Arc gauges.',\n        depends: [\n            'dataviz.core',\n            'dataviz.themes'\n        ]\n    };\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}