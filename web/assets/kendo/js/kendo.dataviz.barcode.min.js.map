{"version": 3, "sources": ["kendo.dataviz.barcode.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "undefined", "getNext", "index", "count", "substring", "state128", "state128AB", "states128", "code128Base", "msiBase", "Barcode", "Widget", "ui", "inArray", "isPlainObject", "draw", "drawing", "geom", "geometry", "defined", "dataviz", "Box2D", "TextBox", "DEFAULT_WIDTH", "DEFAULT_HEIGHT", "DEFAULT_QUIETZONE_LENGTH", "numberRegex", "alphanumericRegex", "InvalidCharacterErrorTemplate", "Encoding", "setOptions", "that", "quietZoneLength", "addQuietZone", "encode", "initValue", "addData", "baseUnit", "pattern", "addCheckSum", "invalidCharacterError", "character", "Error", "format", "name", "encodings", "code39Base", "minBaseUnitLength", "idx", "addStart", "addCharacter", "char<PERSON>t", "pushCheckSum", "addStop", "prepareV<PERSON>ues", "char<PERSON><PERSON>", "characterMap", "addBase", "code39ExtendedBase", "addExtended", "code", "patterns", "j", "extendedMappings", "call", "dataLength", "shiftCharacters", "fromCharCode", "result", "dataCharacter", "specialAsciiCodes", "Math", "floor", "0", "64", "96", "127", "shiftValuesAsciiCodes", "39", "40", "41", "42", "+", "/", "%", "code39", "checkSumMod", "minRatio", "maxRatio", "gapWidth", "splitCharacter", "patternString", "min<PERSON><PERSON><PERSON>", "minBaseUnit", "ratio", "minHeight", "max", "getBaseUnit", "parseFloat", "toFixed", "ceil", "getBase<PERSON>idth", "concat", "split", "<PERSON><PERSON><PERSON><PERSON>", "addPattern", "START", "addCharacterGap", "patternMappings", "b", "w", "B", "W", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "X", "Y", "Z", "-", ".", " ", "code39extended", "SHIFT0", "SHIFT1", "SHIFT2", "SHIFT3", "code93", "cCheckSumTotal", "kCheckSumTotal", "values", "setBaseUnit", "checkSum<PERSON>ength", "TERMINATION_BAR", "checkValues", "_get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checksum", "_findCharacterByValue", "cValue", "kValue", "wightedSum", "weightedValue", "total", "parseInt", "code93extended", "encoding", "is", "move", "pushState", "FNC4", "states", "_initMoves", "isCode", "encodingState", "_moves", "numberMatch", "max<PERSON><PERSON><PERSON>", "substr", "match", "indexOf", "getValue", "_moveFNC", "shift<PERSON>ey", "_shiftState", "_moveState", "fnc", "previousState", "SHIFT", "shifted", "MOVE", "next4", "test", "dependentStates", "_initSubStates", "subState", "_getSubState", "_initSubState", "state", "_pushStart", "pushData", "subStates", "_getAll", "FNC1", "startState", "startAI", "endAI", "nextStart", "separator<PERSON><PERSON><PERSON>", "regexSeparators", "RegExp", "getBySeparator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ai", "id", "validate", "type", "min", "getAI", "unsupportedAIError", "start", "end", "ranges", "applicationIdentifiers", "multiKey", "ids", "22", "402", "7004", "242", "8020", "703", "8008", "253", "8003", "fn", "_initStates", "checkSum", "totalUnits", "position", "getNextState", "temp", "STOP", "code128a", "code128b", "code128c", "code128", "startStopLength", "checkSumFunction", "checkSums", "checkSumType", "Modulo10", "evenSum", "oddSum", "odd", "Modulo11", "weight", "checkValue", "weightedSum", "mod", "Modulo11Modulo10", "mod11Value", "Modulo10Modulo10", "mod10Value", "msimod10", "msimod11", "msimod1110", "msimod1010", "code11", "kCheckSumMinLength", "DASH_VALUE", "DASH", "getWeightedSum", "isNaN", "postnet", "VALID_CODE_LENGTHS", "DIGIT_SEPARATOR", "baseHeight", "pop", "y1", "y2", "ean13", "calculateChecksum", "leftKey", "leftPart", "rightPart", "addPieces", "addSide", "middle", "keyTable", "Array", "prototype", "slice", "digits", "reverse", "arrToAdd", "limitedHeight", "even", "ean8", "element", "wrapper", "addClass", "css", "surfaceWrap", "appendTo", "surface", "Surface", "create", "renderAs", "_setOptions", "redraw", "_getSize", "clear", "setSize", "createVisual", "visual", "getSize", "dimensions", "_resize", "_render", "textToDisplay", "textHeight", "textOptions", "textMargin", "getSpacing", "margin", "border", "contentBox", "unpad", "padding", "barHeight", "Group", "append", "_getBackground", "visible", "font", "top", "bottom", "_getText", "_bandsGroup", "_getBands", "exportVisual", "Size", "step", "item", "rect", "path", "x1", "group", "Rect", "fromPoints", "Point", "Path", "fromRect", "fill", "color", "stroke", "toRect", "background", "dashType", "_textbox", "align", "vAlign", "reflow", "renderVisual", "toLowerCase", "left", "right", "ExportMixin", "plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,yBACH,qBACA,iBACDD,IACL,WA4lEE,MAplEC,UAAUE,EAAGkF,GAEV,QAASC,GAAQtC,EAAOuC,EAAOC,GAC3B,MAAOxC,GAAMyC,UAAUF,EAAOA,EAAQC,GAH7C,GAutBOE,GAaAC,EAiEAC,EAkcAC,EAsOAC,EAwcAC,EAp5DA1D,EAAQD,OAAOC,MAAO2D,EAAS3D,EAAM4D,GAAGD,OAAQxD,EAASrC,EAAEqC,OAAQuC,EAAa1C,EAAM0C,WAAYmB,EAAU/F,EAAE+F,QAASC,EAAgBhG,EAAEgG,cAAeC,EAAO/D,EAAMgE,QAASC,EAAOjE,EAAMkE,SAAUjE,EAAOD,EAAMgE,QAAQ/D,KAAMkE,EAAUlE,EAAKkE,QAASC,EAAUpE,EAAMoE,QAASC,EAAQD,EAAQC,MAAOC,EAAUF,EAAQE,QAASC,EAAgB,IAAKC,EAAiB,IAAKC,EAA2B,GAAIC,EAAc,QAASC,EAAoB,eAAgBC,EAAgC,iDAIxeC,EAAW7E,EAAME,MAAMC,QACvBC,KAAM,SAAUmB,GACZjB,KAAKwE,WAAWvD,IAEpBuD,WAAY,SAAUvD,GAClB,GAAIwD,GAAOzE,IACXyE,GAAKxD,QAAUpB,KAAW4E,EAAKxD,QAASA,GACxCwD,EAAKC,gBAAkBD,EAAKxD,QAAQ0D,aAAe,EAAIF,EAAKxD,QAAQyD,gBAAkB,GAE1FE,OAAQ,SAAUvE,EAAOxB,EAAOC,GAC5B,GAAI2F,GAAOzE,IAYX,OAXI6D,GAAQxD,KACRA,GAAS,IAEboE,EAAKI,UAAUxE,EAAOxB,EAAOC,GACzB2F,EAAKxD,QAAQ0D,cACbF,EAAKE,eAETF,EAAKK,UACDL,EAAKxD,QAAQ0D,cACbF,EAAKE,gBAGLI,SAAUN,EAAKM,SACfC,QAASP,EAAKO,UAGtB/D,SACIyD,gBAAiBP,EACjBQ,cAAc,EACdM,aAAa,GAEjBJ,UAAW,aAEXF,aAAc,WACV3E,KAAKgF,QAAQ7G,KAAK6B,KAAKiB,QAAQyD,iBAAmBP,IAEtDW,QAAS,aAETI,sBAAuB,SAAUC,GAC7B,KAAUC,OAAM1F,EAAM2F,OAAOf,EAA+Ba,EAAWnF,KAAKsF,UAGhFC,KACAC,EAAajB,EAAS1E,QACtB4F,kBAAmB,GACnBX,QAAS,WAAA,GAGIY,GAFLjB,EAAOzE,KAAMK,EAAQoE,EAAKpE,KAE9B,KADAoE,EAAKkB,WACID,EAAM,EAAGA,EAAMrF,EAAM3B,OAAQgH,IAClCjB,EAAKmB,aAAavF,EAAMwF,OAAOH,GAE/BjB,GAAKxD,QAAQgE,aACbR,EAAKqB,eAETrB,EAAKsB,UACLtB,EAAKuB,iBAETJ,aAAc,SAAUT,GACpB,GAAIV,GAAOzE,KAAMiG,EAAWxB,EAAKyB,aAAaf,EACzCc,IACDxB,EAAKS,sBAAsBC,GAE/BV,EAAK0B,QAAQF,IAEjBE,QAAS,eAGTC,GACAR,aAAc,SAAUT,GACpB,GAAIV,GAAOzE,IACPyE,GAAKyB,aAAaf,GAClBV,EAAK0B,QAAQ1B,EAAKyB,aAAaf,IACxBA,EAAUxG,WAAW,GAAK,IACjC8F,EAAKS,sBAAsBC,GAE3BV,EAAK4B,YAAYlB,EAAUxG,WAAW,KAG9C0H,YAAa,SAAUC,GAAV,GACQC,GACR/H,EAEQgI,EAHb/B,EAAOzE,IACX,KAASxB,EAAI,EAAGA,EAAIiG,EAAKgC,iBAAiB/H,OAAQF,IAC9C,GAAI+H,EAAW9B,EAAKgC,iBAAiBjI,GAAGkI,KAAKjC,EAAM6B,GAAO,CACtD,IAASE,EAAI,EAAGA,EAAID,EAAS7H,OAAQ8H,IACjC/B,EAAK0B,QAAQI,EAASC,GAG1B,OADA/B,GAAKkC,YAAcJ,EAAS7H,OAAS,EACrC,IAIZ+H,kBACI,SAAUH,GACN,GAAI,IAAMA,GAAQA,GAAQ,IAAK,CAC3B,GAAI7B,GAAOzE,IACX,QACIyE,EAAKyB,aAAazB,EAAKmC,gBAAgB,IACvCnC,EAAKyB,aAAavI,OAAOkJ,aAAaP,EAAO,QAIzD,SAAUA,GACN,GAAI,IAAMA,GAAQA,GAAQ,GAAI,CAC1B,GAAI7B,GAAOzE,IACX,QACIyE,EAAKyB,aAAazB,EAAKmC,gBAAgB,IACvCnC,EAAKyB,aAAavI,OAAOkJ,aAAaP,EAAO,QAIzD,SAAUA,GACN,GAAI,GAAKA,GAAQA,GAAQ,GAAI,CACzB,GAAI7B,GAAOzE,IACX,QACIyE,EAAKyB,aAAazB,EAAKmC,gBAAgB,IACvCnC,EAAKyB,aAAavI,OAAOkJ,aAAaP,EAAO,QAIzD,SAAUA,GAAV,GACqBQ,GAAQC,EASZvI,EATTiG,EAAOzE,IACX,IAAKyE,EAAKuC,kBAAkBV,GAQxB,IADAQ,KACStI,EAAI,EAAGA,EAAIiG,EAAKuC,kBAAkBV,GAAM5H,OAAQF,IACrDsI,EAAO3I,KAAKsG,EAAKyB,aAAazB,EAAKmC,gBAAgB,KACnDE,EAAO3I,KAAKsG,EAAKyB,aAAazB,EAAKuC,kBAAkBV,GAAM9H,SAT/DuI,GAAwC,EAAxBE,KAAKC,MAAMZ,EAAO,KAAWA,EAAO,IAAM,GAAK,GAC/DQ,GACIrC,EAAKyB,aAAazB,EAAKmC,gBAAgB,IACvCnC,EAAKyB,aAAavI,OAAOkJ,aAAaE,IAS9C,OAAOD,KAGfE,mBACIG,GAAM,KACNC,IAAO,KACPC,IAAO,KACPC,KACI,IACA,IACA,IACA,MAGRC,uBACIC,GAAM,GACNC,GAAM,GACNC,GAAM,GACNC,GAAM,IAEVzB,cACI0B,KAAK,EACLC,KAAK,EACLrK,GAAK,EACLsK,KAAK,GAETlB,iBACI,SACA,SACA,SACA,UAGRrB,GAAUwC,OAASvC,EAAW3F,QAC1ByF,KAAM,UACN0C,YAAa,GACbC,SAAU,IACVC,SAAU,EACVC,SAAU,EACVC,eAAgB,IAChBvD,UAAW,SAAUxE,EAAOxB,EAAOC,GAC/B,GAAI2F,GAAOzE,IACXyE,GAAK5F,MAAQA,EACb4F,EAAK3F,OAASA,EACd2F,EAAKpE,MAAQA,EACboE,EAAKkC,WAAatG,EAAM3B,OACxB+F,EAAKO,WACLP,EAAK4D,cAAgB,IAEzBrC,cAAe,WAAA,GACMjB,GAQTuD,EARJ7D,EAAOzE,KAAgBuI,EAAc9D,EAAKgB,kBAAmB+C,EAAQ/D,EAAKyD,SAAUD,EAAWxD,EAAKwD,SAAUQ,EAAYxB,KAAKyB,IAAI,IAAOjE,EAAK5F,MAAO,GAC1J,IAAI4F,EAAK3F,OAAS2J,EACd,KAAUrD,OAAM,sDAAwDX,EAAKpE,MAAQ,QAAUoI,EAEnG,OAAQ1D,EAAWN,EAAKkE,YAAYH,IAAUD,GAAeC,EAAQP,GACjEO,EAAQI,YAAYJ,EAAQ,IAAKK,QAAQ,GAE7C,IAAI9D,EAAWwD,EAEX,KADID,GAAWrB,KAAK6B,KAAKrE,EAAKsE,aAAad,GAAYM,GAC7CnD,MAAM,oDAAsDX,EAAKpE,MAAQ,QAAUiI,EAEjG7D,GAAK+D,MAAQA,EACb/D,EAAKM,SAAWA,EAChBN,EAAK4D,cAAgB5D,EAAK4D,cAAcvF,UAAU,EAAG2B,EAAK4D,cAAc3J,OAAS,GACjF+F,EAAKO,QAAUP,EAAKO,QAAQgE,OAAOvE,EAAK4D,cAAczK,QAAQ,SAAU4K,GAAOS,MAAMxE,EAAK2D,kBAE9FO,YAAa,SAAUH,GACnB,MAAOxI,MAAKnB,MAAQmB,KAAK+I,aAAaP,IAE1CO,aAAc,SAAUP,GACpB,GAAI/D,GAAOzE,KAAMkJ,EAAkB,GAAKV,EAAQ,EAChD,OAAO/D,GAAKC,gBAAkBwE,GAAmBzE,EAAKkC,WAAa,GAAKlC,EAAK0D,UAAY1D,EAAKkC,WAAa,IAE/GhB,SAAU,WACN,GAAIlB,GAAOzE,IACXyE,GAAK0E,WAAW1E,EAAKyB,aAAakD,MAAMpE,SACxCP,EAAK4E,mBAETlD,QAAS,SAAUhB,GACfnF,KAAKmJ,WAAWhE,EAAUH,SAC1BhF,KAAKqJ,mBAETtD,QAAS,WACL/F,KAAKmJ,WAAWnJ,KAAKkG,aAAakD,MAAMpE,UAE5CmE,WAAY,SAAUnE,GAClB,IAAK,GAAIxG,GAAI,EAAGA,EAAIwG,EAAQtG,OAAQF,IAChCwB,KAAKqI,eAAiBrI,KAAKsJ,gBAAgBtE,EAAQa,OAAOrH,KAGlE6K,gBAAiB,WACb,GAAI5E,GAAOzE,IACXyE,GAAK4D,eAAiB5D,EAAK0D,SAAW1D,EAAK2D,gBAE/CkB,iBACIC,EAAK,KACLC,EAAK,KACLC,EAAK,SACLC,EAAK,UAETxD,cACIiB,GACInC,QAAW,YACX3E,MAAS,GAEbsJ,GACI3E,QAAW,YACX3E,MAAS,GAEbuJ,GACI5E,QAAW,YACX3E,MAAS,GAEbwJ,GACI7E,QAAW,YACX3E,MAAS,GAEbyJ,GACI9E,QAAW,YACX3E,MAAS,GAEb0J,GACI/E,QAAW,YACX3E,MAAS,GAEb2J,GACIhF,QAAW,YACX3E,MAAS,GAEb4J,GACIjF,QAAW,YACX3E,MAAS,GAEb6J,GACIlF,QAAW,YACX3E,MAAS,GAEb8J,GACInF,QAAW,YACX3E,MAAS,GAEb+J,GACIpF,QAAW,YACX3E,MAAS,IAEboJ,GACIzE,QAAW,YACX3E,MAAS,IAEbgK,GACIrF,QAAW,YACX3E,MAAS,IAEbiK,GACItF,QAAW,YACX3E,MAAS,IAEbkK,GACIvF,QAAW,YACX3E,MAAS,IAEbmK,GACIxF,QAAW,YACX3E,MAAS,IAEboK,GACIzF,QAAW,YACX3E,MAAS,IAEbqK,GACI1F,QAAW,YACX3E,MAAS,IAEbsK,GACI3F,QAAW,YACX3E,MAAS,IAEbuK,GACI5F,QAAW,YACX3E,MAAS,IAEbwK,GACI7F,QAAW,YACX3E,MAAS,IAEbyK,GACI9F,QAAW,YACX3E,MAAS,IAEb0K,GACI/F,QAAW,YACX3E,MAAS,IAEb2K,GACIhG,QAAW,YACX3E,MAAS,IAEb4K,GACIjG,QAAW,YACX3E,MAAS,IAEb6K,GACIlG,QAAW,YACX3E,MAAS,IAEb8K,GACInG,QAAW,YACX3E,MAAS,IAEb+K,GACIpG,QAAW,YACX3E,MAAS,IAEbgL,GACIrG,QAAW,YACX3E,MAAS,IAEbiL,GACItG,QAAW,YACX3E,MAAS,IAEbkL,GACIvG,QAAW,YACX3E,MAAS,IAEbmL,GACIxG,QAAW,YACX3E,MAAS,IAEbqJ,GACI1E,QAAW,YACX3E,MAAS,IAEboL,GACIzG,QAAW,YACX3E,MAAS,IAEbqL,GACI1G,QAAW,YACX3E,MAAS,IAEbsL,GACI3G,QAAW,YACX3E,MAAS,IAEbuL,KACI5G,QAAW,YACX3E,MAAS,IAEbwL,KACI7G,QAAW,YACX3E,MAAS,IAEbyL,KACI9G,QAAW,YACX3E,MAAS,IAEb7C,GACIwH,QAAW,YACX3E,MAAS,IAEbwH,KACI7C,QAAW,YACX3E,MAAS,IAEbuH,KACI5C,QAAW,YACX3E,MAAS,IAEbyH,KACI9C,QAAW,YACX3E,MAAS,IAEb+I,OAASpE,QAAS,cAEtB/D,SAAWgE,aAAa,KAE5BM,EAAUwG,eAAiBxG,EAAUwC,OAAOlI,OAAOuC,KAAegE,GAC9Dd,KAAM,mBACNY,cACI8F,QACIhH,QAAW,YACX3E,MAAS,IAEb4L,QACIjH,QAAW,YACX3E,MAAS,IAEb6L,QACIlH,QAAW,YACX3E,MAAS,IAEb8L,QACInH,QAAW,YACX3E,MAAS,QAIrBkF,EAAU6G,OAAS5G,EAAW3F,QAC1ByF,KAAM,UACN+G,eAAgB,GAChBC,eAAgB,GAChBtE,YAAa,GACbnD,UAAW,SAAUxE,EAAOxB,EAAOC,GAC/B,GAAI2F,GAAOzE,IACXyE,GAAKpE,MAAQA,EACboE,EAAK5F,MAAQA,EACb4F,EAAK3F,OAASA,EACd2F,EAAKO,WACLP,EAAK8H,UACL9H,EAAKkC,WAAatG,EAAM3B,QAE5BsH,cAAe,WACX,GAAIvB,GAAOzE,KAAMyI,EAAYxB,KAAKyB,IAAI,IAAOjE,EAAK5F,MAAO,GACzD,IAAI4F,EAAK3F,OAAS2J,EACd,KAAUrD,OAAM,sBAGpB,IADAX,EAAK+H,cACD/H,EAAKM,SAAWN,EAAKgB,kBACrB,KAAUL,OAAM,uBAGxBoH,YAAa,WACT,GAAI/H,GAAOzE,KAAMyM,EAAiB,CAClChI,GAAKM,SAAWN,EAAK5F,OAAS,GAAK4F,EAAKkC,WAAa,EAAI8F,GAAkBhI,EAAKC,gBAAkB,IAEtGiB,SAAU,WACN,GAAIX,GAAUhF,KAAKkG,aAAakD,MAAMpE,OACtChF,MAAKmJ,WAAWnE,IAEpBe,QAAS,WACL,GAAItB,GAAOzE,IACXyE,GAAKkB,WACLlB,EAAKO,QAAQ7G,KAAKsG,EAAKyB,aAAawG,kBAExCvG,QAAS,SAAUF,GACfjG,KAAKmJ,WAAWlD,EAASjB,SACzBhF,KAAKuM,OAAOpO,KAAK8H,EAAS5F,QAE9ByF,aAAc,WAAA,GAC6CG,GAE9CzH,EAFLiG,EAAOzE,KAAM2M,EAAclI,EAAKmI,iBAEpC,KADAnI,EAAKoI,SAAWF,EAAYtO,KAAK,IACxBG,EAAI,EAAGA,EAAImO,EAAYjO,OAAQF,IACpCyH,EAAWxB,EAAKyB,aAAazB,EAAKqI,sBAAsBH,EAAYnO,KACpEiG,EAAK0E,WAAWlD,EAASjB,UAGjC4H,gBAAiB,WACb,GAA+EG,GAAQC,EAAQtH,EAA3FjB,EAAOzE,KAAMuM,EAAS9H,EAAK8H,OAAQ7N,EAAS6N,EAAO7N,OAAQuO,EAAa,CAC5E,KAAKvH,EAAMhH,EAAS,EAAGgH,GAAO,EAAGA,IAC7BuH,GAAcxI,EAAKyI,cAAcX,EAAO7G,GAAMhH,EAASgH,EAAKjB,EAAK4H,eAIrE,KAFAU,EAASE,EAAaxI,EAAKuD,YAC3BiF,EAAaxI,EAAKyI,cAAcH,EAAQ,EAAGtI,EAAK6H,gBAC3C5G,EAAMhH,EAAS,EAAGgH,GAAO,EAAGA,IAC7BuH,GAAcxI,EAAKyI,cAAcX,EAAO7G,GAAMhH,EAASgH,EAAM,EAAGjB,EAAK6H,eAGzE,OADAU,GAASC,EAAaxI,EAAKuD,aAEvB+E,EACAC,IAGRF,sBAAuB,SAAUzM,GAC7B,IAAK,GAAI8E,KAAanF,MAAKkG,aACvB,GAAIlG,KAAKkG,aAAaf,GAAW9E,QAAUA,EACvC,MAAO8E,IAInB+H,cAAe,SAAU7M,EAAOuC,EAAOuK,GACnC,OAAQvK,EAAQuK,GAASA,GAAS9M,GAEtC8I,WAAY,SAAUnE,GAAV,GACJ3E,GACK7B,CAAT,KAASA,EAAI,EAAGA,EAAIwG,EAAQtG,OAAQF,IAChC6B,EAAQ+M,SAASpI,EAAQa,OAAOrH,GAAI,IACpCwB,KAAKgF,QAAQ7G,KAAKkC,IAG1B6F,cACIiB,GACInC,QAAW,SACX3E,MAAS,GAEbsJ,GACI3E,QAAW,SACX3E,MAAS,GAEbuJ,GACI5E,QAAW,SACX3E,MAAS,GAEbwJ,GACI7E,QAAW,SACX3E,MAAS,GAEbyJ,GACI9E,QAAW,SACX3E,MAAS,GAEb0J,GACI/E,QAAW,SACX3E,MAAS,GAEb2J,GACIhF,QAAW,SACX3E,MAAS,GAEb4J,GACIjF,QAAW,SACX3E,MAAS,GAEb6J,GACIlF,QAAW,SACX3E,MAAS,GAEb8J,GACInF,QAAW,SACX3E,MAAS,GAEb+J,GACIpF,QAAW,SACX3E,MAAS,IAEboJ,GACIzE,QAAW,SACX3E,MAAS,IAEbgK,GACIrF,QAAW,SACX3E,MAAS,IAEbiK,GACItF,QAAW,SACX3E,MAAS,IAEbkK,GACIvF,QAAW,SACX3E,MAAS,IAEbmK,GACIxF,QAAW,SACX3E,MAAS,IAEboK,GACIzF,QAAW,SACX3E,MAAS,IAEbqK,GACI1F,QAAW,SACX3E,MAAS,IAEbsK,GACI3F,QAAW,SACX3E,MAAS,IAEbuK,GACI5F,QAAW,SACX3E,MAAS,IAEbwK,GACI7F,QAAW,SACX3E,MAAS,IAEbyK,GACI9F,QAAW,SACX3E,MAAS,IAEb0K,GACI/F,QAAW,SACX3E,MAAS,IAEb2K,GACIhG,QAAW,SACX3E,MAAS,IAEb4K,GACIjG,QAAW,SACX3E,MAAS,IAEb6K,GACIlG,QAAW,SACX3E,MAAS,IAEb8K,GACInG,QAAW,SACX3E,MAAS,IAEb+K,GACIpG,QAAW,SACX3E,MAAS,IAEbgL,GACIrG,QAAW,SACX3E,MAAS,IAEbiL,GACItG,QAAW,SACX3E,MAAS,IAEbkL,GACIvG,QAAW,SACX3E,MAAS,IAEbmL,GACIxG,QAAW,SACX3E,MAAS,IAEbqJ,GACI1E,QAAW,SACX3E,MAAS,IAEboL,GACIzG,QAAW,SACX3E,MAAS,IAEbqL,GACI1G,QAAW,SACX3E,MAAS,IAEbsL,GACI3G,QAAW,SACX3E,MAAS,IAEbuL,KACI5G,QAAW,SACX3E,MAAS,IAEbwL,KACI7G,QAAW,SACX3E,MAAS,IAEbyL,KACI9G,QAAW,SACX3E,MAAS,IAEb7C,GACIwH,QAAW,SACX3E,MAAS,IAEbwH,KACI7C,QAAW,SACX3E,MAAS,IAEbuH,KACI5C,QAAW,SACX3E,MAAS,IAEbyH,KACI9C,QAAW,SACX3E,MAAS,IAEb2L,QACIhH,QAAW,SACX3E,MAAS,IAEb4L,QACIjH,QAAW,SACX3E,MAAS,IAEb6L,QACIlH,QAAW,SACX3E,MAAS,IAEb8L,QACInH,QAAW,SACX3E,MAAS,IAEb+I,OAASpE,QAAW,UACpB0H,gBAAiB,OAGzBnH,EAAU8H,eAAiB9H,EAAU6G,OAAOvM,OAAOuC,KAAegE,GAC9Dd,KAAM,mBACNQ,aAAc,WAAA,GAC6CzF,GAE9C7B,EAFLiG,EAAOzE,KAAM2M,EAAclI,EAAKmI,iBAEpC,KADAnI,EAAKoI,SAAWF,EAAYtO,KAAK,IACxBG,EAAI,EAAGA,EAAImO,EAAYjO,OAAQF,IACpC6B,EAAQsM,EAAYnO,GAChBiG,EAAK8C,sBAAsBlH,GAC3BoE,EAAK4B,YAAY5B,EAAK8C,sBAAsBlH,IAE5CoE,EAAK0E,WAAW1E,EAAKyB,aAAazB,EAAKqI,sBAAsBzM,IAAQ2E,aAKjFjC,EAAWrD,EAAME,MAAMC,QACvBC,KAAM,SAAUwN,GACZtN,KAAKsN,SAAWA,GAEpB3H,SAAU,aAEV4H,GAAI,aAEJC,KAAM,aAENC,UAAW,eAGXzK,EAAaD,EAASlD,QACtB6N,KAAM,OACN5N,KAAM,SAAUwN,EAAUK,GACtB,GAAIlJ,GAAOzE,IACXyE,GAAK6I,SAAWA,EAChB7I,EAAKkJ,OAASA,EACdlJ,EAAKmJ,WAAWD,IAEpBhI,SAAU,WACN3F,KAAKsN,SAASnE,WAAWnJ,KAAKoJ,QAElCmE,GAAI,SAAUlN,EAAOuC,GACjB,GAAI0D,GAAOjG,EAAM1B,WAAWiE,EAC5B,OAAO5C,MAAK6N,OAAOvH,IAEvBkH,KAAM,SAAUM,GAEZ,IADA,GAAIrJ,GAAOzE,KAAM0F,EAAM,GACfjB,EAAKsJ,OAAOrI,GAAKgB,KAAKjC,EAAMqJ,IAAkBpI,EAAMjB,EAAKsJ,OAAOrP,QACpEgH,KAGR+H,UAAW,SAAUK,GAAV,GACuFxH,GAEtF0H,EAFJvJ,EAAOzE,KAAM2N,EAASlJ,EAAKkJ,OAAQtN,EAAQyN,EAAczN,MAAO4N,EAAY5N,EAAM3B,MAOtF,KANI6E,EAAQ,IAAKoK,IAAW,IACpBK,EAAc3N,EAAM6N,OAAOJ,EAAclL,OAAOuL,MAAM,WACtDH,IACAC,EAAY5N,EAAM+N,QAAQJ,EAAY,GAAIF,EAAclL,UAGxD0D,EAAOwH,EAAczN,MAAM1B,WAAWmP,EAAclL,SAAW,GAAK6B,EAAKoJ,OAAOvH,IAASwH,EAAclL,MAAQqL,GACnHxJ,EAAK6I,SAASnE,WAAW1E,EAAK4J,SAAS/H,IACvCwH,EAAclL,SAGtBgL,WAAY,SAAUD,GAClB,GAAIlJ,GAAOzE,IACXyE,GAAKsJ,UACDxK,EAAQkB,EAAKiJ,KAAMC,IAAW,GAC9BlJ,EAAKsJ,OAAO5P,KAAKsG,EAAK6J,UAEtB/K,EAAQkB,EAAK8J,SAAUZ,IAAW,GAClClJ,EAAKsJ,OAAO5P,KAAKsG,EAAK+J,aAE1B/J,EAAKsJ,OAAO5P,KAAKsG,EAAKgK,aAE1BH,SAAU,SAAUR,GAChB,GAAIA,EAAcY,IAEd,MADAZ,GAAcY,KAAM,EACbZ,EAAca,eAAiB3O,KAAK/B,KAGnDuQ,YAAa,SAAUV,GACnB,GAAIrJ,GAAOzE,IACX,IAAI8N,EAAca,eAAiBlK,EAAK8J,WAAaT,EAAclL,MAAQ,GAAKkL,EAAczN,MAAM3B,QAAU+F,EAAK6I,SAAS7I,EAAK8J,UAAUhB,GAAGO,EAAczN,MAAOyN,EAAclL,MAAQ,IAGrL,MAFA6B,GAAK6I,SAASnE,WAAW1E,EAAKmK,OAC9Bd,EAAce,SAAU,GACjB,GAGfJ,WAAY,WAER,MADAzO,MAAKsN,SAASnE,WAAWnJ,KAAK8O,OACvB,GAEXF,MAAO,KAEP3L,KACJA,EAAUmH,EAAIpH,EAAWnD,QACrB5B,IAAK,IACLsQ,SAAU,IACVV,OAAQ,SAAUvH,GACd,MAAO,IAAKA,GAAQA,EAAO,IAE/B+H,SAAU,SAAU/H,GAChB,MAAIA,GAAO,GACAA,EAAO,GAEXA,EAAO,IAElBwI,KAAM,IACN1F,MAAO,MAEXnG,EAAUwG,EAAIzG,EAAWnD,QACrB5B,IAAK,IACLsQ,SAAU,IACVV,OAAQ,SAAUvH,GACd,MAAO,KAAMA,GAAQA,EAAO,KAEhC+H,SAAU,SAAU/H,GAChB,MAAOA,GAAO,IAElBwI,KAAM,IACN1F,MAAO,MAEXnG,EAAUoH,EAAItH,EAASlD,QACnB5B,IAAK,IACL0H,SAAU,WACN3F,KAAKsN,SAASnE,WAAWnJ,KAAKoJ,QAElCmE,GAAI,SAAUlN,EAAOuC,GACjB,GAAImM,GAAQpM,EAAQtC,EAAOuC,EAAO,EAClC,QAAQA,EAAQ,GAAKvC,EAAM3B,QAA0B,GAAhB2B,EAAM3B,SAAgB0F,EAAY4K,KAAKD,IAEhFvB,KAAM,WACFxN,KAAKsN,SAASnE,WAAWnJ,KAAK8O,OAElCrB,UAAW,SAAUK,GAEjB,IADA,GAAIxH,IACIA,EAAO3D,EAAQmL,EAAczN,MAAOyN,EAAclL,MAAO,KAAOwB,EAAY4K,KAAK1I,IAAwB,GAAfA,EAAK5H,QACnGsB,KAAKsN,SAASnE,WAAWiE,SAAS9G,EAAM,KACxCwH,EAAclL,OAAS,GAG/ByL,SAAU,SAAU/H,GAChB,MAAOA,IAEXwI,KAAM,GACN1F,MAAO,MAEXnG,EAAUyK,KAAO3K,EAASlD,QACtB5B,IAAK,OACLgR,iBACI,IACA,KAEJnP,KAAM,SAAUwN,EAAUK,GACtB3N,KAAKsN,SAAWA,EAChBtN,KAAKkP,eAAevB,IAExBhI,SAAU,SAAUmI,GAChB,GAAIxH,GAAOwH,EAAczN,MAAM1B,WAAW,GAAK,IAAKwQ,EAAWnP,KAAKoP,aAAa9I,EACjFtG,MAAKsN,SAAS6B,GAAUxJ,YAE5B4H,GAAI,SAAUlN,EAAOuC,GACjB,GAAI0D,GAAOjG,EAAM1B,WAAWiE,EAC5B,OAAO5C,MAAK6N,OAAOvH,IAEvBuH,OAAQ,SAAUvH,GACd,MAAO,MAAOA,GAAQA,EAAO,KAEjCmH,UAAW,SAAUK,GAAV,GAICxH,GAHJ7B,EAAOzE,KAAMmP,EAAW1K,EAAK4K,cAAcvB,GAAgBR,EAAW7I,EAAK6I,SAAU5O,EAASyQ,EAAS9O,MAAM3B,MAEjH,IADAoP,EAAclL,OAASlE,EACnBA,EAAS,EAET,KAAOyQ,EAASvM,MAAQlE,EAAQyQ,EAASvM,QACrC0D,EAAO6I,EAAS9O,MAAM1B,WAAWwQ,EAASvM,OAC1CuM,EAASG,MAAQ7K,EAAK2K,aAAa9I,GAC/B6I,EAASR,eAAiBQ,EAASG,QACnCH,EAASR,cAAgBQ,EAASG,MAClChC,EAAS6B,EAASG,OAAO9B,KAAK2B,IAElC7B,EAASnE,WAAWmE,EAAS6B,EAASG,OAAOR,MAC7CxB,EAASnE,WAAWmE,EAAS6B,EAASG,OAAOjB,SAAS/H,QAGtD6I,GAASG,OAASH,EAASR,eAC3BrB,EAAS6B,EAASG,OAAO9B,KAAK2B,GAElC1K,EAAK8K,WAAWJ,GAChB7B,EAASkC,SAASL,EAAU1K,EAAKgL,WAC7B3B,EAAclL,MAAQkL,EAAczN,MAAM3B,QAC1C+F,EAAK8K,WAAWJ,EAGxBrB,GAAcY,KAAM,EACpBZ,EAAcwB,MAAQH,EAASG,OAEnCC,WAAY,SAAUJ,GAClB,GAAI1K,GAAOzE,IACXyE,GAAK6I,SAASnE,WAAW1E,EAAK6I,SAAS6B,EAASG,OAAOR,MACvDrK,EAAK6I,SAASnE,WAAW1E,EAAK6I,SAAS6B,EAASG,OAAOR,OAE3DO,cAAe,SAAUvB,GACrB,GAAIrJ,GAAOzE,KAAMmP,GACT9O,MAAOoE,EAAKiL,QAAQ5B,EAAczN,MAAOyN,EAAclL,OACvDA,MAAO,EAIf,OAFAuM,GAASG,MAAQ7K,EAAK2K,aAAaD,EAAS9O,MAAM1B,WAAW,IAC7DwQ,EAASR,cAAgBb,EAAca,eAAiBlK,EAAKxG,IAAMkR,EAASG,MAAQxB,EAAca,cAC3FQ,GAEXD,eAAgB,SAAUvB,GAAV,GAGHnP,GAFLiG,EAAOzE,IAEX,KADAyE,EAAKgL,aACIjR,EAAI,EAAGA,EAAImP,EAAOjP,OAAQF,IAC3B+E,EAAQoK,EAAOnP,GAAIiG,EAAKwK,kBAAoB,GAC5CxK,EAAKgL,UAAUtR,KAAKwP,EAAOnP,KAIvC4Q,aAAc,SAAU9I,GAAV,GAED9H,GADLiG,EAAOzE,IACX,KAASxB,EAAI,EAAGA,EAAIiG,EAAKgL,UAAU/Q,OAAQF,IACvC,GAAIiG,EAAK6I,SAAS7I,EAAKgL,UAAUjR,IAAIqP,OAAOvH,GACxC,MAAO7B,GAAKgL,UAAUjR,IAIlCkR,QAAS,SAAUrP,EAAOuC,GAEtB,IADA,GAAI0D,GAAMQ,EAAS,IACXR,EAAOjG,EAAM1B,WAAWiE,OAAa5C,KAAK6N,OAAOvH,IACrDQ,GAAUnJ,OAAOkJ,aAAaP,EAAO,IAEzC,OAAOQ,MAGf7D,EAAU0M,KAAO5M,EAASlD,QACtB5B,IAAK,OACL2R,WAAY,IACZX,iBACI,IACA,KAEJY,QAAS,IACTC,MAAO,IACPhQ,KAAM,SAAUwN,EAAUK,GACtB3N,KAAKsN,SAAWA,EAChBtN,KAAK2N,OAASA,GAElBhI,SAAU,WACN3F,KAAKsN,SAAStN,KAAK4P,YAAYjK,YAEnC4H,GAAI,WACA,MAAOhK,GAAQvD,KAAK/B,IAAK+B,KAAK2N,SAAW,GAE7CF,UAAW,SAAUK,GACjB,GAAyO1O,GAAS2Q,EAAWC,EAAzPvL,EAAOzE,KAAMsN,EAAW7I,EAAK6I,SAAUjN,EAAQyN,EAAczN,MAAMzC,QAAQ,MAAO,IAAKqS,EAAsBC,OAAO,IAAMzL,EAAKoL,QAAUpL,EAAKqL,MAAQ,IAAK,KAAMlN,EAAQkL,EAAclL,MAAOuM,GAAaG,MAAO7K,EAAKmL,WAE3N,KADAtC,EAASnE,WAAW1E,EAAK2E,SACZ,CAIT,GAHA+F,EAASvM,MAAQ,EACjBoN,EAAkB3P,EAAMwF,OAAOjD,KAAW6B,EAAKoL,QAAU,EAAI,EAC7DzQ,EAAU4Q,EAAkB,EAAIvL,EAAK0L,eAAe9P,EAAOuC,GAAS6B,EAAK2L,YAAY/P,EAAOuC,GACxFxD,EAAQiR,GAAG3R,OACXqR,EAAYnN,EAAQoN,EAAkB5Q,EAAQkR,GAAG5R,OAASU,EAAQiR,GAAG3R,WAGrE,IADAqR,EAAY1P,EAAM+N,QAAQ3J,EAAKoL,QAASjN,EAAQ,GAC5CmN,EAAY,EAAG,CACf,GAAInN,EAAQxD,EAAQiR,GAAG3H,IAAMtJ,EAAQkR,GAAG5R,OAASsR,EAAkB3P,EAAM3B,OACrE,KAAU0G,OAAM,4DAEpB2K,GAAY1P,EAAM3B,OAM1B,GAHAyQ,EAAS9O,MAAQA,EAAMyC,UAAUF,EAAOmN,GAAWnS,QAAQqS,EAAiB,IAC5ExL,EAAK8L,SAASnR,EAAS+P,EAAS9O,OAChCiN,EAASkC,SAASL,EAAU1K,EAAKwK,iBAC7Bc,GAAa1P,EAAM3B,OACnB,KAEJkE,GAAQmN,EACJZ,EAASG,OAAS7K,EAAKmL,aACvBtC,EAAS7I,EAAKmL,YAAYpC,KAAK2B,GAC/BA,EAASG,MAAQ7K,EAAKmL,YAErBxQ,EAAQiR,GAAG3R,QACZ4O,EAASnE,WAAW1E,EAAK2E,OAGjC0E,EAAclL,MAAQkL,EAAczN,MAAM3B,QAE9C6R,SAAU,SAAUnR,EAASiB,GACzB,GAAIiG,GAAOjG,EAAM6N,OAAO9O,EAAQkR,GAAG5R,QAAS2R,EAAKjR,EAAQiR,EACzD,KAAKA,EAAGG,OAASpM,EAAY4K,KAAK1I,GAC9B,KAAUlB,OAAM,0BAA4BhG,EAAQkR,GAAK,0DAE7D,IAAe,gBAAXD,EAAGG,OAA2BnM,EAAkB2K,KAAK1I,GACrD,KAAUlB,OAAM,0BAA4BhG,EAAQkR,GAAK,oEAE7D,IAAID,EAAG3R,QAAU2R,EAAG3R,SAAW4H,EAAK5H,OAChC,KAAU0G,OAAM,0BAA4BhG,EAAQkR,GAAK,YAAcD,EAAG3R,OAAS,oBAEvF,IAAI2R,EAAGI,KAAOJ,EAAGI,IAAMnK,EAAK5H,OACxB,KAAU0G,OAAM,0BAA4BhG,EAAQkR,GAAK,qBAAuBD,EAAGI,IAAM,oBAE7F,IAAIJ,EAAG3H,KAAO2H,EAAG3H,IAAMpC,EAAK5H,OACxB,KAAU0G,OAAM,0BAA4BhG,EAAQkR,GAAK,oBAAsBD,EAAG3H,IAAM,sBAGhG0H,YAAa,SAAU/P,EAAOuC,GAAjB,GACQ0N,GAAID,EACZ7R,EADLiG,EAAOzE,IACX,KAASxB,EAAI,EAAGA,GAAK,EAAGA,IAGpB,GAFA8R,EAAK3N,EAAQtC,EAAOuC,EAAOpE,GAC3B6R,EAAK5L,EAAKiM,MAAMJ,IAAO7L,EAAKiM,MAAMJ,EAAGxN,UAAU,EAAGwN,EAAG5R,OAAS,IAE1D,OACI4R,GAAIA,EACJD,GAAIA,EAIhB5L,GAAKkM,mBAAmBL,IAE5BK,mBAAoB,SAAUL,GAC1B,KAAUlL,OAAM1F,EAAM2F,OAAO,mDAAsDiL,IAEvFH,eAAgB,SAAU9P,EAAOuC,GAC7B,GAAI6B,GAAOzE,KAAM4Q,EAAQvQ,EAAM+N,QAAQ3J,EAAKoL,QAASjN,GAAQiO,EAAMxQ,EAAM+N,QAAQ3J,EAAKqL,MAAOc,GAAQN,EAAKjQ,EAAMyC,UAAU8N,EAAQ,EAAGC,GAAMR,EAAK5L,EAAKiM,MAAMJ,IAAO7L,EAAKiM,MAAMJ,EAAGpC,OAAOoC,EAAG5R,OAAS,GAInM,OAHK2R,IACD5L,EAAKkM,mBAAmBL,IAGxBD,GAAIA,EACJC,GAAIA,IAGZI,MAAO,SAAUJ,GAAV,GAKM9R,GAIGsS,EACKtK,EATb6J,EAAKrQ,KAAK+Q,uBAAwBC,EAAWX,EAAGW,QACpD,IAAIX,EAAGC,GACH,MAAOD,GAAGC,EAEd,KAAS9R,EAAI,EAAGA,EAAIwS,EAAStS,OAAQF,IAAK,CACtC,GAAIwS,EAASxS,GAAGyS,KAAO1N,EAAQ+M,EAAIU,EAASxS,GAAGyS,MAAQ,EACnD,MAAOD,GAASxS,GAAGgS,IAChB,IAAIQ,EAASxS,GAAGsS,OAEnB,IADIA,EAASE,EAASxS,GAAGsS,OAChBtK,EAAI,EAAGA,EAAIsK,EAAOpS,OAAQ8H,IAC/B,GAAIsK,EAAOtK,GAAG,IAAM8J,GAAMA,GAAMQ,EAAOtK,GAAG,GACtC,MAAOwK,GAASxS,GAAGgS,OAMvCO,wBACIG,IACIxI,IAAK,GACL8H,KAAM,gBAEVW,KAASzS,OAAQ,IACjB0S,MACI1I,IAAK,EACL8H,KAAM,gBAEVa,KACI3I,IAAK,EACL8H,KAAM,gBAEVc,MACI5I,IAAK,GACL8H,KAAM,gBAEVe,KACId,IAAK,EACL/H,IAAK,GACL8H,KAAM,gBAEVgB,MACIf,IAAK,EACL/H,IAAK,GACL8H,KAAM,gBAEViB,KACIhB,IAAK,GACL/H,IAAK,GACL8H,KAAM,gBAEVkB,MACIjB,IAAK,GACL/H,IAAK,GACL8H,KAAM,gBAEVQ,WAEQC,KACI,KACA,KACA,OACA,QAEJH,SAEQ,GACA,KAGA,IACA,MAGA,IACA,MAGA,IACA,MAGRN,MAAQ9R,OAAQ,KAGhBuS,KACI,MACA,MACA,MACA,MACA,MACA,MACA,MACA,OACA,OACA,OACA,QAEJH,cACAN,MACI9H,IAAK,GACL8H,KAAM,kBAIVS,KAAM,QACNH,SACQ,IACA,MAERN,MAAQ9R,OAAQ,MAGhBuS,KACI,KACA,KACA,MACA,MACA,QAEJT,MACI9H,IAAK,GACL8H,KAAM,kBAIVS,KACI,KACA,OACA,OACA,QAEJT,MAAQ9R,OAAQ,MAGhBuS,KACI,KACA,KACA,QAEJT,MAAQ9R,OAAQ,MAGhBuS,KAAM,OACNH,SACQ,IACA,MAERN,MAAQ9R,OAAQ,KAGhBuS,KACI,KACA,QAEJT,MAAQ9R,OAAQ,KAGhBuS,KACI,KACA,MAEJT,MACI9H,IAAK,EACL8H,KAAM,kBAIVS,KACI,MACA,OAEJT,MACI9H,IAAK,GACL8H,KAAM,kBAIVS,KACI,MACA,OAEJT,MACIC,IAAK,EACL/H,IAAK,GACL8H,KAAM,kBAIVS,KACI,MACA,OAEJT,MACIC,IAAK,EACL/H,IAAK,GACL8H,KAAM,kBAIVS,KACI,OACA,QAEJT,MAAQ9R,OAAQ,OAI5B0K,MAAO,MAEPlG,EAAcqB,EAAS1E,QACvBC,KAAM,SAAUmB,GACZsD,EAASoN,GAAG7R,KAAK4G,KAAK1G,KAAMiB,GAC5BjB,KAAK4R,eAETA,YAAa,WAAA,GAEApT,GADLiG,EAAOzE,IACX,KAASxB,EAAI,EAAGA,EAAIiG,EAAKkJ,OAAOjP,OAAQF,IACpCiG,EAAKA,EAAKkJ,OAAOnP,IAAM,GAAIyE,GAAUwB,EAAKkJ,OAAOnP,IAAIiG,EAAMA,EAAKkJ,SAGxE9I,UAAW,SAAUxE,EAAOxB,EAAOC,GAC/B,GAAI2F,GAAOzE,IACXyE,GAAKO,WACLP,EAAKpE,MAAQA,EACboE,EAAK5F,MAAQA,EACb4F,EAAK3F,OAASA,EACd2F,EAAKoN,SAAW,EAChBpN,EAAKqN,WAAa,EAClBrN,EAAK7B,MAAQ,EACb6B,EAAKsN,SAAW,GAEpBjN,QAAS,WACL,GAAIL,GAAOzE,KAAM8N,GACTzN,MAAOoE,EAAKpE,MACZuC,MAAO,EACP0M,MAAO,GAEW,KAAtB7K,EAAKpE,MAAM3B,SAGfoP,EAAcwB,MAAQxB,EAAca,cAAgBlK,EAAKuN,aAAalE,EAAerJ,EAAKkJ,QAC1FlJ,EAAKkB,SAASmI,GACdrJ,EAAK+K,SAAS1B,EAAerJ,EAAKkJ,QAClClJ,EAAKQ,cACLR,EAAKsB,UACLtB,EAAK+H,gBAETgD,SAAU,SAAU1B,EAAeH,GAE/B,IAFM,GAYMsE,GAXRxN,EAAOzE,OACE,CAET,GADAyE,EAAKqJ,EAAcwB,OAAO7B,UAAUK,GAChCA,EAAclL,OAASkL,EAAczN,MAAM3B,OAC3C,KAECoP,GAAce,SAKXoD,EAAOnE,EAAcwB,MACzBxB,EAAcwB,MAAQxB,EAAca,cACpCb,EAAca,cAAgBsD,EAC9BnE,EAAce,SAAU,IAPxBf,EAAca,cAAgBb,EAAcwB,MAC5CxB,EAAcwB,MAAQ7K,EAAKuN,aAAalE,EAAeH,GACvDlJ,EAAKqJ,EAAcwB,OAAO9B,KAAKM,MAS3CnI,SAAU,SAAUmI,GAChB9N,KAAK8N,EAAcwB,OAAO3J,SAASmI,GACnC9N,KAAK+R,SAAW,GAEpB9M,YAAa,WACT,GAAIR,GAAOzE,IACXyE,GAAKoI,SAAWpI,EAAKoN,SAAW,IAChCpN,EAAK0E,WAAW1E,EAAKoI,WAEzB9G,QAAS,WACL/F,KAAKmJ,WAAWnJ,KAAKkS,OAEzB1F,YAAa,WACT,GAAI/H,GAAOzE,IACXyE,GAAKM,SAAWN,EAAK5F,OAAS4F,EAAKqN,WAAarN,EAAKC,kBAEzDyE,WAAY,SAAU7C,GAAV,GACuDjG,GACtD7B,EADLiG,EAAOzE,KAAMgF,EAAUP,GAAAA,EAAKyB,aAAaI,EAC7C,KAAS9H,EAAI,EAAGA,EAAIwG,EAAQtG,OAAQF,IAChC6B,EAAQ+M,SAASpI,EAAQa,OAAOrH,GAAI,IACpCiG,EAAKO,QAAQ7G,KAAKkC,GAClBoE,EAAKqN,YAAczR,CAEvBoE,GAAKoN,UAAYvL,EAAO7B,EAAKsN,YAEjCC,aAAc,SAAUlE,EAAeH,GACnC,IAAK,GAAInP,GAAI,EAAGA,EAAImP,EAAOjP,OAAQF,IAC/B,GAAIwB,KAAK2N,EAAOnP,IAAI+O,GAAGO,EAAczN,MAAOyN,EAAclL,OACtD,MAAO+K,GAAOnP,EAGtBwB,MAAKkF,sBAAsB4I,EAAczN,MAAMwF,OAAOiI,EAAclL,SAExEsD,cACI,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,SAEJgM,KAAM,MAEV3M,EAAU4M,SAAWjP,EAAYrD,QAC7ByF,KAAM,aACNqI,QAAS,OAEbpI,EAAU6M,SAAWlP,EAAYrD,QAC7ByF,KAAM,aACNqI,QAAS,OAEbpI,EAAU8M,SAAWnP,EAAYrD,QAC7ByF,KAAM,aACNqI,QAAS,OAEbpI,EAAU+M,QAAUpP,EAAYrD,QAC5ByF,KAAM,WACNqI,QACI,IACA,IACA,IACA,UAGRpI,EAAU,WAAarC,EAAYrD,QAC/ByF,KAAM,eACNqI,QACI,OACA,IACA,OAGJxK,EAAUoB,EAAS1E,QACnBgF,UAAW,SAAUxE,EAAOxB,GACxB,GAAI4F,GAAOzE,IACXyE,GAAKO,WACLP,EAAKpE,MAAQA,EACboE,EAAKgI,eAAiB,EACtBhI,EAAK5F,MAAQA,GAEjB2N,YAAa,WACT,GAAI/H,GAAOzE,KAAMuS,EAAkB,CACnC9N,GAAKM,SAAWN,EAAK5F,OAAS,IAAM4F,EAAKpE,MAAM3B,OAAS+F,EAAKgI,gBAAkBhI,EAAKC,gBAAkB6N,IAE1GzN,QAAS,WAAA,GAGItG,GAFLiG,EAAOzE,KAAMK,EAAQoE,EAAKpE,KAE9B,KADAoE,EAAK0E,WAAW1E,EAAK2E,OACZ5K,EAAI,EAAGA,EAAI6B,EAAM3B,OAAQF,IAC9BiG,EAAKmB,aAAavF,EAAMwF,OAAOrH,GAE/BiG,GAAKxD,QAAQgE,aACbR,EAAKQ,cAETR,EAAK0E,WAAW1E,EAAKyN,MACrBzN,EAAK+H,eAET5G,aAAc,SAAUT,GACpB,GAAIV,GAAOzE,KAAMgF,EAAUP,EAAKyB,aAAaf,EACxCH,IACDP,EAAKS,sBAAsBC,GAE/BV,EAAK0E,WAAWnE,IAEpBmE,WAAY,SAAUnE,GAClB,IAAK,GAAIxG,GAAI,EAAGA,EAAIwG,EAAQtG,OAAQF,IAChCwB,KAAKgF,QAAQ7G,KAAKiP,SAASpI,EAAQa,OAAOrH,GAAI,MAGtDyG,YAAa,WAAA,GAIAzG,GAHLiG,EAAOzE,KAAMwS,EAAmB/N,EAAKgO,UAAUhO,EAAKiO,cAAe/F,EACzD6F,EAAiB9L,KAAKjC,EAAKgO,UAAWhO,EAAKpE,MAEzD,KADAoE,EAAKoI,SAAWF,EAAYtO,KAAK,IACxBG,EAAI,EAAGA,EAAImO,EAAYjO,OAAQF,IACpCiG,EAAKgI,iBACLhI,EAAK0E,WAAW1E,EAAKyB,aAAayG,EAAYnO,MAGtDiU,WACIE,SAAU,SAAUtS,GAChB,GAG+BqF,GAAKkN,EAASC,EAHzClG,GACI,EACA,IACDmG,EAAMzS,EAAM3B,OAAS,CAC5B,KAAKgH,EAAM,EAAGA,EAAMrF,EAAM3B,OAAQgH,IAC9BiH,GAAajH,EAAMoN,GAAO,IAAM1F,SAAS/M,EAAMwF,OAAOH,GAAM,GAIhE,KAFAmN,EAASlG,EAAY,GACrBiG,EAAU,GAAkB,EAAjBjG,EAAY,GAClBjH,EAAM,EAAGA,EAAMkN,EAAQlU,OAAQgH,IAChCmN,GAAUzF,SAASwF,EAAQ/M,OAAOH,GAAM,GAE5C,SAAS,GAAKmN,EAAS,IAAM,KAEjCE,SAAU,SAAU1S,GAAV,GACgD2S,GAAQC,EACrDzU,EADL0U,EAAc,EAAGC,EAAM,GAAIzU,EAAS2B,EAAM3B,MAC9C,KAASF,EAAI,EAAGA,EAAIE,EAAQF,IACxBwU,IAAWtU,EAASF,GAAK,GAAK,GAAK,EACnC0U,GAAeF,EAAS3S,EAAMwF,OAAOrH,EAGzC,OADAyU,IAAcE,EAAMD,EAAcC,GAAOA,EACvB,IAAdF,GACQA,IAGR,EACA,IAGRG,iBAAkB,SAAU/S,GACxB,GAAwCgT,GAApC1G,EAAc3M,KAAK+S,SAAS1S,EAEhC,OADAgT,GAAahT,EAAQsM,EAAY,GAC1BA,EAAY3D,OAAOhJ,KAAK2S,SAASU,KAE5CC,iBAAkB,SAAUjT,GACxB,GAAwCkT,GAApC5G,EAAc3M,KAAK2S,SAAStS,EAEhC,OADAkT,GAAalT,EAAQsM,EAAY,GAC1BA,EAAY3D,OAAOhJ,KAAK2S,SAASY,MAGhDrN,cACI,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,YAEJkD,MAAO,KACP8I,KAAM,MACNQ,aAAc,KAElBnN,EAAUiO,SAAWrQ,EAAQtD,QACzByF,KAAM,eACNoN,aAAc,aAElBnN,EAAUkO,SAAWtQ,EAAQtD,QACzByF,KAAM,eACNoN,aAAc,aAElBnN,EAAUmO,WAAavQ,EAAQtD,QAC3ByF,KAAM,wBACNoN,aAAc,qBAElBnN,EAAUoO,WAAaxQ,EAAQtD,QAC3ByF,KAAM,wBACNoN,aAAc,qBAElBnN,EAAUqO,OAASrP,EAAS1E,QACxByF,KAAM,UACN+G,eAAgB,GAChBC,eAAgB,EAChBuH,mBAAoB,GACpB7L,YAAa,GACb8L,WAAY,GACZC,KAAM,IACN3K,MAAO,SACP8I,KAAM,QACNrN,UAAW,SAAUxE,EAAOxB,GACxB,GAAI4F,GAAOzE,IACXyE,GAAKO,WACLP,EAAKpE,MAAQA,EACboE,EAAK5F,MAAQA,EACb4F,EAAKqN,WAAa,GAEtBhN,QAAS,WAAA,GAIItG,GAHLiG,EAAOzE,KACPK,EAAQoE,EAAKpE,KAEjB,KADAoE,EAAK0E,WAAW1E,EAAK2E,OACZ5K,EAAI,EAAGA,EAAI6B,EAAM3B,OAAQF,IAC9BiG,EAAKmB,aAAavF,EAAMwF,OAAOrH,GAE/BiG,GAAKxD,QAAQgE,aACbR,EAAKQ,cAETR,EAAK0E,WAAW1E,EAAKyN,MACrBzN,EAAK+H,eAETA,YAAa,WACT,GAAI/H,GAAOzE,IACXyE,GAAKM,SAAWN,EAAK5F,OAAS4F,EAAKqN,WAAarN,EAAKC,kBAEzDO,YAAa,WAAA,GAOD+H,GANJvI,EAAOzE,KAAMK,EAAQoE,EAAKpE,MAAO3B,EAAS2B,EAAM3B,OAAQqO,EACnDtI,EAAKuP,eAAe3T,EAAO3B,EAAQ+F,EAAK4H,gBAAkB5H,EAAKuD,WACxEvD,GAAKoI,SAAWE,EAAS,GACzBtI,EAAK0E,WAAW1E,EAAKyB,aAAa6G,IAClCrO,IACIA,GAAU+F,EAAKoP,qBACX7G,GAAUD,EAAStI,EAAKuP,eAAe3T,EAAO3B,EAAQ+F,EAAK6H,iBAAmB7H,EAAKuD,YACvFvD,EAAKoI,UAAYG,EACjBvI,EAAK0E,WAAW1E,EAAKyB,aAAa8G,MAG1CgH,eAAgB,SAAU3T,EAAO3B,EAAQyO,GAAzB,GAEH3O,GADL0U,EAAc,CAClB,KAAS1U,EAAI,EAAGA,EAAI6B,EAAM3B,OAAQF,IAC9B0U,GAAelT,KAAKkN,cAAclN,KAAKqO,SAAShO,EAAMwF,OAAOrH,IAAKE,EAAQF,EAAG2O,EAEjF,OAAO+F,IAEXhG,cAAe,SAAU7M,EAAO3B,EAAQkE,EAAOuK,GAC3C,GAAI6F,IAAUtU,EAASkE,GAASuK,GAASA,CACzC,OAAO6F,GAAS3S,GAEpBgO,SAAU,SAAUlJ,GAChB,GAAIV,GAAOzE,IACX,OAAKiU,OAAM9O,IAEAA,IAAcV,EAAKsP,MAC1BtP,EAAKS,sBAAsBC,GAExBV,EAAKqP,YAJD1G,SAASjI,EAAW,KAMnCS,aAAc,SAAUT,GACpB,GAAIV,GAAOzE,KAAMK,EAAQoE,EAAK4J,SAASlJ,GAAYH,EAAUP,EAAKyB,aAAa7F,EAC/EoE,GAAK0E,WAAWnE,IAEpBmE,WAAY,SAAUnE,GAAV,GACJ3E,GACK7B,CAAT,KAASA,EAAI,EAAGA,EAAIwG,EAAQtG,OAAQF,IAChC6B,EAAQ+M,SAASpI,EAAQa,OAAOrH,GAAI,IACpCwB,KAAKgF,QAAQ7G,KAAKkC,GAClBL,KAAK8R,YAAczR,GAG3B6F,cACI,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,UAEJjF,SAAWgE,aAAa,KAE5BM,EAAU2O,QAAU3P,EAAS1E,QACzByF,KAAM,UACN8D,MAAO,IACP+K,oBACI,EACA,EACA,IAEJC,gBAAiB,IACjBvP,UAAW,SAAUxE,EAAOxB,EAAOC,GAC/B,GAAI2F,GAAOzE,IACXyE,GAAK3F,OAASA,EACd2F,EAAK5F,MAAQA,EACb4F,EAAK4P,WAAavV,EAAS,EAC3B2F,EAAKpE,MAAQA,EAAMzC,QAAYsS,OAAOzL,EAAK2P,gBAAiB,KAAM,IAClE3P,EAAKO,WACLP,EAAK8L,SAAS9L,EAAKpE,OACnBoE,EAAKoN,SAAW,EAChBpN,EAAK+H,eAET1H,QAAS,WAAA,GAGItG,GAFLiG,EAAOzE,KAAMK,EAAQoE,EAAKpE,KAE9B,KADAoE,EAAK0E,WAAW1E,EAAK2E,OACZ5K,EAAI,EAAGA,EAAI6B,EAAM3B,OAAQF,IAC9BiG,EAAKmB,aAAavF,EAAMwF,OAAOrH,GAE/BiG,GAAKxD,QAAQgE,aACbR,EAAKQ,cAETR,EAAK0E,WAAW1E,EAAK2E,OACrB3E,EAAKO,QAAQsP,OAEjB1O,aAAc,SAAUT,GACpB,GAAIV,GAAOzE,KAAMgF,EAAUP,EAAKyB,aAAaf,EAC7CV,GAAKoN,UAAYzE,SAASjI,EAAW,IACrCV,EAAK0E,WAAWnE,IAEpBC,YAAa,WACT,GAAIR,GAAOzE,IACXyE,GAAKoI,UAAY,GAAKpI,EAAKoN,SAAW,IAAM,GAC5CpN,EAAKmB,aAAanB,EAAKoI,WAE3BL,YAAa,WACT,GAAI/H,GAAOzE,KAAMuS,EAAkB,CACnC9N,GAAKM,SAAWN,EAAK5F,OAAmC,IAAzB4F,EAAKpE,MAAM3B,OAAS,GAAU6T,EAAkB9N,EAAKC,kBAExF6L,SAAU,SAAUlQ,GAChB,GAAIoE,GAAOzE,IAIX,IAHKoE,EAAY4K,KAAK3O,IAClBoE,EAAKS,sBAAsB7E,EAAM8N,MAAM,UAAU,IAEjD5K,EAAQlD,EAAM3B,OAAQ+F,EAAK0P,oBAAsB,EACjD,KAAU/O,OAAM,qEAAuEX,EAAK0P,mBAAmB9V,KAAK,OAG5H8K,WAAY,SAAUnE,GAAV,GACSuP,GACR/V,EADLiG,EAAOzE,IACX,KAASxB,EAAI,EAAGA,EAAIwG,EAAQtG,OAAQF,IAChC+V,EAAK9P,EAAK3F,OAAS2F,EAAK4P,WAAarP,EAAQa,OAAOrH,GACpDiG,EAAKO,QAAQ7G,MACTU,MAAO,EACP0V,GAAIA,EACJC,GAAI/P,EAAK3F,SAEb2F,EAAKO,QAAQ7G,KAAK,IAG1B+H,cACI,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,WAGRX,EAAUkP,MAAQlQ,EAAS1E,QACvBgF,UAAW,SAAUxE,EAAOxB,EAAOC,GAE/B,GADAuB,GAAS,GACW,IAAhBA,EAAM3B,QAAgB,KAAKsQ,KAAK3O,GAChC,KAAU+E,OAAM,yDAEpB,IAAIX,GAAOzE,IACXyE,GAAKO,WACLP,EAAKxD,QAAQnC,OAASA,EACtB2F,EAAKM,SAAWlG,GAAS,GAAK4F,EAAKC,iBACnCD,EAAKpE,MAAQA,EACboE,EAAKoI,SAAWpI,EAAKiQ,oBACrBjQ,EAAKkQ,QAAUtU,EAAM,GACrBoE,EAAKmQ,SAAWvU,EAAM6N,OAAO,EAAG,GAChCzJ,EAAKoQ,UAAYxU,EAAM6N,OAAO,GAAKzJ,EAAKoI,UAE5C/H,QAAS,WACL,GAAIL,GAAOzE,IACXyE,GAAKqQ,UAAUrQ,EAAKyB,aAAa0K,OACjCnM,EAAKsQ,QAAQtQ,EAAKmQ,SAAUnQ,EAAKkQ,SACjClQ,EAAKqQ,UAAUrQ,EAAKyB,aAAa8O,QACjCvQ,EAAKsQ,QAAQtQ,EAAKoQ,WAClBpQ,EAAKqQ,UAAUrQ,EAAKyB,aAAa0K,QAErCmE,QAAS,SAAUH,EAAU3W,GAApB,GAEIO,GADLiG,EAAOzE,IACX,KAASxB,EAAI,EAAGA,EAAIoW,EAASlW,OAAQF,IAC7BP,GAAOmP,SAAS3I,EAAKwQ,SAAShX,GAAK4H,OAAOrH,GAAI,IAC9CiG,EAAKqQ,UAAUI,MAAMC,UAAUC,MAAM1O,KAAKjC,EAAKyB,aAAamP,OAAOT,EAAS/O,OAAOrH,KAAK8W,WAAW,GAEnG7Q,EAAKqQ,UAAUrQ,EAAKyB,aAAamP,OAAOT,EAAS/O,OAAOrH,KAAK,IAIzEsW,UAAW,SAAUS,EAAUC,GAApB,GAEEhX,GADLiG,EAAOzE,IACX,KAASxB,EAAI,EAAGA,EAAI+W,EAAS7W,OAAQF,IAE7BiG,EAAKO,QAAQ7G,KADbqX,GAEIjB,GAAI,EACJC,GAA0B,IAAtB/P,EAAKxD,QAAQnC,OACjBD,MAAO0W,EAAS/W,IAGF+W,EAAS/W,KAIvCkW,kBAAmB,WAAA,GAENlW,GAOLqO,EARAiG,EAAM,EAAG2C,EAAO,EAAGpV,EAAQL,KAAKK,MAAM4I,MAAM,IAAIqM,UAAUjX,KAAK,GACnE,KAASG,EAAI,EAAGA,EAAI6B,EAAM3B,OAAQF,IAC1BA,EAAI,EACJiX,GAAQrI,SAAS/M,EAAMwF,OAAOrH,GAAI,IAElCsU,GAAO1F,SAAS/M,EAAMwF,OAAOrH,GAAI,GAIzC,OADIqO,IAAY,IAAM,EAAIiG,EAAM2C,GAAQ,IAAM,IAGlDR,UACI,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,UAEJ/O,cACImP,SAEQ,EACA,EACA,EACA,IAGA,EACA,EACA,EACA,IAGA,EACA,EACA,EACA,IAGA,EACA,EACA,EACA,IAGA,EACA,EACA,EACA,IAGA,EACA,EACA,EACA,IAGA,EACA,EACA,EACA,IAGA,EACA,EACA,EACA,IAGA,EACA,EACA,EACA,IAGA,EACA,EACA,EACA,IAGRzE,OACI,EACA,EACA,GAEJoE,QACI,EACA,EACA,EACA,EACA,MAIZzP,EAAUmQ,KAAOnQ,EAAUkP,MAAM5U,QAC7BgF,UAAW,SAAUxE,EAAOxB,EAAOC,GAC/B,GAAI2F,GAAOzE,IACX,IAAoB,GAAhBK,EAAM3B,QAAe,KAAKsQ,KAAK3O,GAC/B,KAAU+E,OAAM,yBAEpBX,GAAKpE,MAAQA,EACboE,EAAKxD,QAAQnC,OAASA,EACtB2F,EAAKoI,SAAWpI,EAAKiQ,kBAAkBjQ,EAAKpE,OAC5CoE,EAAKmQ,SAAWnQ,EAAKpE,MAAM6N,OAAO,EAAG,GACrCzJ,EAAKoQ,UAAYpQ,EAAKpE,MAAM6N,OAAO,GAAKzJ,EAAKoI,SAC7CpI,EAAKO,WACLP,EAAKM,SAAWlG,GAAS,GAAK4F,EAAKC,oBAGvCtB,EAAUC,EAAOxD,QACjBC,KAAM,SAAU6V,EAAS1U,GACrB,GAAIwD,GAAOzE,IACXqD,GAAOsO,GAAG7R,KAAK4G,KAAKjC,EAAMkR,EAAS1U,GACnCwD,EAAKkR,QAAUnY,EAAEmY,GACjBlR,EAAKmR,QAAUnR,EAAKkR,QACpBlR,EAAKkR,QAAQE,SAAS,aAAaC,IAAI,UAAW,SAClDrR,EAAKsR,YAAcvY,EAAE,WAAWsY,IAAI,WAAY,YAAYE,SAAShW,KAAK2V,SAC1ElR,EAAKwR,QAAUxS,EAAKyS,QAAQC,OAAO1R,EAAKsR,aAAevF,KAAM/L,EAAKxD,QAAQmV,WAC1E3R,EAAK4R,YAAYpV,GACbA,GAAW4C,EAAQ5C,EAAQZ,QAC3BoE,EAAK6R,UAGb9R,WAAY,SAAUvD,GAClBjB,KAAKqW,YAAYpV,GACjBjB,KAAKsW,UAETA,OAAQ,WACJ,GAAIvW,GAAOC,KAAKuW,UAChBvW,MAAKiW,QAAQO,QACbxW,KAAKiW,QAAQQ,SACT5X,MAAOkB,EAAKlB,MACZC,OAAQiB,EAAKjB,SAEjBkB,KAAK0W,eACL1W,KAAKiW,QAAQxS,KAAKzD,KAAK2W,SAE3BC,QAAS,WACL,MAAOlX,GAAMmX,WAAW7W,KAAK2V,UAEjCmB,QAAS,WACL9W,KAAKsW,UAETI,aAAc,WACV1W,KAAK2W,OAAS3W,KAAK+W,WAEvBA,QAAS,WAAA,GACgWjQ,GAAQkQ,EAAeC,EAAxXxS,EAAOzE,KAAMiB,EAAUwD,EAAKxD,QAASZ,EAAQY,EAAQZ,MAAO6W,EAAcjW,EAAQvD,KAAMyZ,EAAarT,EAAQsT,WAAWF,EAAYG,QAAStX,EAAO0E,EAAK8R,WAAYe,EAASrW,EAAQqW,WAAchK,EAAW7I,EAAK6I,SAAUiK,EAAa,GAAIxT,GAAM,EAAG,EAAGhE,EAAKlB,MAAOkB,EAAKjB,QAAQ0Y,MAAMF,EAAOzY,OAAO2Y,MAAMvW,EAAQwW,SAAUC,EAAYH,EAAWzY,SACvV6X,EAAS,GAAIlT,GAAKkU,KAkBtB,OAjBAlT,GAAK8S,WAAaA,EAClBZ,EAAOiB,OAAOnT,EAAKoT,eAAe9X,IAC9BmX,EAAYY,UACZb,EAAaxT,EAAK9D,KAAKX,YAAYqB,GAAS0X,KAAMb,EAAYa,OAAQjZ,OACtE4Y,GAAaT,EAAaE,EAAWa,IAAMb,EAAWc,QAE1DnR,EAASwG,EAAS1I,OAAOvE,EAAOkX,EAAW1Y,QAAS6Y,GAChDR,EAAYY,UACZd,EAAgB3W,EACZY,EAAQ4L,UAAYhJ,EAAQyJ,EAAST,YACrCmK,GAAiB,IAAM1J,EAAST,UAEpC8J,EAAOiB,OAAOnT,EAAKyT,SAASlB,KAEhCvS,EAAKiT,UAAYA,EACjB1X,KAAKmY,YAAcnY,KAAKoY,UAAUtR,EAAO9B,QAAS8B,EAAO/B,UACzD4R,EAAOiB,OAAO5X,KAAKmY,aACZxB,GAEX0B,aAAc,WACV,MAAOrY,MAAK+W,WAEhBR,SAAU,WACN,GAAI9R,GAAOzE,KAAM2V,EAAUlR,EAAKkR,QAAS5V,EAAO,GAAI4D,GAAK2U,KAAKrU,EAAeC,EAa7E,OAZIyR,GAAQ9W,QAAU,IAClBkB,EAAKlB,MAAQ8W,EAAQ9W,SAErB8W,EAAQ7W,SAAW,IACnBiB,EAAKjB,OAAS6W,EAAQ7W,UAEtB2F,EAAKxD,QAAQpC,QACbkB,EAAKlB,MAAQ4F,EAAKxD,QAAQpC,OAE1B4F,EAAKxD,QAAQnC,SACbiB,EAAKjB,OAAS2F,EAAKxD,QAAQnC,QAExBiB,GAEXM,MAAO,SAAUA,GACb,GAAIoE,GAAOzE,IACX,OAAK6D,GAAQxD,IAGboE,EAAKxD,QAAQZ,MAAQA,EAAQ,GAC7BoE,EAAK6R,SADL7R,GAFWA,EAAKxD,QAAQZ,OAK5B+X,UAAW,SAAUpT,EAASD,GAAnB,GACkEwT,GAAMC,EAEtEha,EAQGia,EACAC,EAXRjU,EAAOzE,KAAMuX,EAAa9S,EAAK8S,WAAYxF,EAAWwF,EAAWoB,GACjEC,EAAQ,GAAInV,GAAKkU,KACrB,KAASnZ,EAAI,EAAGA,EAAIwG,EAAQtG,OAAQF,IAChCga,EAAOhV,EAAcwB,EAAQxG,IAAMwG,EAAQxG,IACvCK,MAAOmG,EAAQxG,GACf+V,GAAI,EACJC,GAAI/P,EAAKiT,WAEba,EAAOC,EAAK3Z,MAAQkG,EAChBvG,EAAI,IACAia,EAAO9U,EAAKkV,KAAKC,WAAW,GAAInV,GAAKoV,MAAMhH,EAAUyG,EAAKjE,GAAKgD,EAAWhD,IAAK,GAAI5Q,GAAKoV,MAAMhH,EAAWwG,EAAMC,EAAKhE,GAAK+C,EAAWhD,KACpImE,EAAOjV,EAAKuV,KAAKC,SAASR,GAC1BS,MAAQC,MAAO1U,EAAKxD,QAAQkY,OAC5BC,OAAQ,OAEZR,EAAMhB,OAAOc,IAEjB3G,GAAYwG,CAEhB,OAAOK,IAEXf,eAAgB,SAAU9X,GAAV,GACR0E,GAAOzE,KAAMiB,EAAUwD,EAAKxD,QAASqW,EAASrW,EAAQqW,WACtD9V,EAAM,GAAIuC,GAAM,EAAG,EAAGhE,EAAKlB,MAAOkB,EAAKjB,QAAQ0Y,MAAMF,EAAOzY,MAAQ,GACpE6Z,EAAOjV,EAAKuV,KAAKC,SAASzX,EAAI6X,UAC9BH,MAAQC,MAAOlY,EAAQqY,YACvBF,QACID,MAAO7B,EAAOzY,MAAQyY,EAAO6B,MAAQ,GACrCta,MAAOyY,EAAOzY,MACd0a,SAAUjC,EAAOiC,WAGzB,OAAOb,IAEXR,SAAU,SAAU7X,GAChB,GAAIoE,GAAOzE,KAAMkX,EAAczS,EAAKxD,QAAQvD,KAAMA,EAAO+G,EAAK+U,SAAW,GAAIxV,GAAQ3D,GAC7E0X,KAAMb,EAAYa,KAClBoB,MAAOjC,EAAYiC,MACnBM,MAAO,SACPC,OAAQ,SACRrC,OAAQH,EAAYG,QAI5B,OAFA3Z,GAAKic,OAAOlV,EAAK8S,YACjB7Z,EAAKkc,eACElc,EAAKiZ,QAEhBN,YAAa,SAAUpV,GACnB,GAAIwD,GAAOzE,IAUX,IATAyE,EAAK+L,MAAQvP,EAAQuP,MAAQ/L,EAAKxD,QAAQuP,MAAMqJ,cAC/B,QAAbpV,EAAK+L,OACL/L,EAAK+L,KAAO,QACZvP,EAAQZ,MAAQ,IAAMY,EAAQZ,OAEjB,QAAboE,EAAK+L,OACL/L,EAAK+L,KAAO,OACZvP,EAAQZ,MAAQ,IAAMY,EAAQZ,QAE7BkF,EAAUd,EAAK+L,MAChB,KAAUpL,OAAM,YAAcX,EAAK+L,KAAO,oBAE9C/L,GAAK6I,SAAW,GAAI/H,GAAUd,EAAK+L,MACnC/L,EAAKxD,QAAUpB,GAAO,EAAM4E,EAAKxD,QAASA,IAE9CA,SACIqE,KAAM,UACN8Q,SAAU,MACV/V,MAAO,GACPmQ,KAAM,SACN3D,UAAU,EACVhO,MAAO,EACPC,OAAQ,EACRqa,MAAO,QACPG,WAAY,QACZ5b,MACIoa,SAAS,EACTC,KAAM,0DACNoB,MAAO,QACP9B,QACIW,IAAK,EACLC,OAAQ,EACR6B,KAAM,EACNC,MAAO,IAGfzC,QACIzY,MAAO,EACP0a,SAAU,QACVJ,MAAO,SAEX1B,SACIO,IAAK,EACLC,OAAQ,EACR6B,KAAM,EACNC,MAAO,MAInBjW,EAAQkW,YAAYna,OAAOuD,EAAQuO,IACnC7N,EAAQR,GAAG2W,OAAO7W,GAClB1D,EAAM0C,WAAW0B,GACbyB,UAAWA,EACXhB,SAAUA,KAEhB9E,OAAOC,MAAM2C,QACR5C,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dataviz.barcode.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dataviz.barcode', [\n        'kendo.dataviz.core',\n        'kendo.drawing'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dataviz.barcode',\n        name: 'Barcode',\n        category: 'dataviz',\n        description: 'Barcode widget',\n        depends: ['dataviz.core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, extend = $.extend, deepExtend = kendo.deepExtend, inArray = $.inArray, isPlainObject = $.isPlainObject, draw = kendo.drawing, geom = kendo.geometry, util = kendo.drawing.util, defined = util.defined, dataviz = kendo.dataviz, Box2D = dataviz.Box2D, TextBox = dataviz.TextBox, DEFAULT_WIDTH = 300, DEFAULT_HEIGHT = 100, DEFAULT_QUIETZONE_LENGTH = 10, numberRegex = /^\\d+$/, alphanumericRegex = /^[a-z0-9]+$/i, InvalidCharacterErrorTemplate = 'Character \\'{0}\\' is not valid for symbology {1}';\n        function getNext(value, index, count) {\n            return value.substring(index, index + count);\n        }\n        var Encoding = kendo.Class.extend({\n            init: function (options) {\n                this.setOptions(options);\n            },\n            setOptions: function (options) {\n                var that = this;\n                that.options = extend({}, that.options, options);\n                that.quietZoneLength = that.options.addQuietZone ? 2 * that.options.quietZoneLength : 0;\n            },\n            encode: function (value, width, height) {\n                var that = this;\n                if (defined(value)) {\n                    value += '';\n                }\n                that.initValue(value, width, height);\n                if (that.options.addQuietZone) {\n                    that.addQuietZone();\n                }\n                that.addData();\n                if (that.options.addQuietZone) {\n                    that.addQuietZone();\n                }\n                return {\n                    baseUnit: that.baseUnit,\n                    pattern: that.pattern\n                };\n            },\n            options: {\n                quietZoneLength: DEFAULT_QUIETZONE_LENGTH,\n                addQuietZone: true,\n                addCheckSum: true\n            },\n            initValue: function () {\n            },\n            addQuietZone: function () {\n                this.pattern.push(this.options.quietZoneLength || DEFAULT_QUIETZONE_LENGTH);\n            },\n            addData: function () {\n            },\n            invalidCharacterError: function (character) {\n                throw new Error(kendo.format(InvalidCharacterErrorTemplate, character, this.name));\n            }\n        });\n        var encodings = {};\n        var code39Base = Encoding.extend({\n            minBaseUnitLength: 0.7,\n            addData: function () {\n                var that = this, value = that.value;\n                that.addStart();\n                for (var idx = 0; idx < value.length; idx++) {\n                    that.addCharacter(value.charAt(idx));\n                }\n                if (that.options.addCheckSum) {\n                    that.pushCheckSum();\n                }\n                that.addStop();\n                that.prepareValues();\n            },\n            addCharacter: function (character) {\n                var that = this, charData = that.characterMap[character];\n                if (!charData) {\n                    that.invalidCharacterError(character);\n                }\n                that.addBase(charData);\n            },\n            addBase: function () {\n            }\n        });\n        var code39ExtendedBase = {\n            addCharacter: function (character) {\n                var that = this;\n                if (that.characterMap[character]) {\n                    that.addBase(that.characterMap[character]);\n                } else if (character.charCodeAt(0) > 127) {\n                    that.invalidCharacterError(character);\n                } else {\n                    that.addExtended(character.charCodeAt(0));\n                }\n            },\n            addExtended: function (code) {\n                var that = this, patterns;\n                for (var i = 0; i < that.extendedMappings.length; i++) {\n                    if (patterns = that.extendedMappings[i].call(that, code)) {\n                        for (var j = 0; j < patterns.length; j++) {\n                            that.addBase(patterns[j]);\n                        }\n                        that.dataLength += patterns.length - 1;\n                        return;\n                    }\n                }\n            },\n            extendedMappings: [\n                function (code) {\n                    if (97 <= code && code <= 122) {\n                        var that = this;\n                        return [\n                            that.characterMap[that.shiftCharacters[0]],\n                            that.characterMap[String.fromCharCode(code - 32)]\n                        ];\n                    }\n                },\n                function (code) {\n                    if (33 <= code && code <= 58) {\n                        var that = this;\n                        return [\n                            that.characterMap[that.shiftCharacters[1]],\n                            that.characterMap[String.fromCharCode(code + 32)]\n                        ];\n                    }\n                },\n                function (code) {\n                    if (1 <= code && code <= 26) {\n                        var that = this;\n                        return [\n                            that.characterMap[that.shiftCharacters[2]],\n                            that.characterMap[String.fromCharCode(code + 64)]\n                        ];\n                    }\n                },\n                function (code) {\n                    var that = this, result, dataCharacter;\n                    if (!that.specialAsciiCodes[code]) {\n                        dataCharacter = Math.floor(code / 32) * 6 + (code - 27) % 32 + 64;\n                        result = [\n                            that.characterMap[that.shiftCharacters[3]],\n                            that.characterMap[String.fromCharCode(dataCharacter)]\n                        ];\n                    } else {\n                        result = [];\n                        for (var i = 0; i < that.specialAsciiCodes[code].length; i++) {\n                            result.push(that.characterMap[that.shiftCharacters[3]]);\n                            result.push(that.characterMap[that.specialAsciiCodes[code][i]]);\n                        }\n                    }\n                    return result;\n                }\n            ],\n            specialAsciiCodes: {\n                '0': ['U'],\n                '64': ['V'],\n                '96': ['W'],\n                '127': [\n                    'T',\n                    'X',\n                    'Y',\n                    'Z'\n                ]\n            },\n            shiftValuesAsciiCodes: {\n                '39': 36,\n                '40': 47,\n                '41': 43,\n                '42': 37\n            },\n            characterMap: {\n                '+': false,\n                '/': false,\n                '$': false,\n                '%': false\n            },\n            shiftCharacters: [\n                'SHIFT0',\n                'SHIFT1',\n                'SHIFT2',\n                'SHIFT3'\n            ]\n        };\n        encodings.code39 = code39Base.extend({\n            name: 'Code 39',\n            checkSumMod: 43,\n            minRatio: 2.5,\n            maxRatio: 3,\n            gapWidth: 1,\n            splitCharacter: '|',\n            initValue: function (value, width, height) {\n                var that = this;\n                that.width = width;\n                that.height = height;\n                that.value = value;\n                that.dataLength = value.length;\n                that.pattern = [];\n                that.patternString = '';\n            },\n            prepareValues: function () {\n                var that = this, baseUnit, minBaseUnit = that.minBaseUnitLength, ratio = that.maxRatio, minRatio = that.minRatio, minHeight = Math.max(0.15 * that.width, 24);\n                if (that.height < minHeight) {\n                    throw new Error('Insufficient Height. The minimum height for value: ' + that.value + ' is: ' + minHeight);\n                }\n                while ((baseUnit = that.getBaseUnit(ratio)) < minBaseUnit && ratio > minRatio) {\n                    ratio = parseFloat((ratio - 0.1).toFixed(1));\n                }\n                if (baseUnit < minBaseUnit) {\n                    var minWidth = Math.ceil(that.getBaseWidth(minRatio) * minBaseUnit);\n                    throw new Error('Insufficient width. The minimum width for value: ' + that.value + ' is: ' + minWidth);\n                }\n                that.ratio = ratio;\n                that.baseUnit = baseUnit;\n                that.patternString = that.patternString.substring(0, that.patternString.length - 1);\n                that.pattern = that.pattern.concat(that.patternString.replace(/ratio/g, ratio).split(that.splitCharacter));\n            },\n            getBaseUnit: function (ratio) {\n                return this.width / this.getBaseWidth(ratio);\n            },\n            getBaseWidth: function (ratio) {\n                var that = this, characterLength = 3 * (ratio + 2);\n                return that.quietZoneLength + characterLength * (that.dataLength + 2) + that.gapWidth * (that.dataLength + 1);\n            },\n            addStart: function () {\n                var that = this;\n                that.addPattern(that.characterMap.START.pattern);\n                that.addCharacterGap();\n            },\n            addBase: function (character) {\n                this.addPattern(character.pattern);\n                this.addCharacterGap();\n            },\n            addStop: function () {\n                this.addPattern(this.characterMap.START.pattern);\n            },\n            addPattern: function (pattern) {\n                for (var i = 0; i < pattern.length; i++) {\n                    this.patternString += this.patternMappings[pattern.charAt(i)];\n                }\n            },\n            addCharacterGap: function () {\n                var that = this;\n                that.patternString += that.gapWidth + that.splitCharacter;\n            },\n            patternMappings: {\n                'b': '1|',\n                'w': '1|',\n                'B': 'ratio|',\n                'W': 'ratio|'\n            },\n            characterMap: {\n                '0': {\n                    'pattern': 'bwbWBwBwb',\n                    'value': 0\n                },\n                '1': {\n                    'pattern': 'BwbWbwbwB',\n                    'value': 1\n                },\n                '2': {\n                    'pattern': 'bwBWbwbwB',\n                    'value': 2\n                },\n                '3': {\n                    'pattern': 'BwBWbwbwb',\n                    'value': 3\n                },\n                '4': {\n                    'pattern': 'bwbWBwbwB',\n                    'value': 4\n                },\n                '5': {\n                    'pattern': 'BwbWBwbwb',\n                    'value': 5\n                },\n                '6': {\n                    'pattern': 'bwBWBwbwb',\n                    'value': 6\n                },\n                '7': {\n                    'pattern': 'bwbWbwBwB',\n                    'value': 7\n                },\n                '8': {\n                    'pattern': 'BwbWbwBwb',\n                    'value': 8\n                },\n                '9': {\n                    'pattern': 'bwBWbwBwb',\n                    'value': 9\n                },\n                'A': {\n                    'pattern': 'BwbwbWbwB',\n                    'value': 10\n                },\n                'B': {\n                    'pattern': 'bwBwbWbwB',\n                    'value': 11\n                },\n                'C': {\n                    'pattern': 'BwBwbWbwb',\n                    'value': 12\n                },\n                'D': {\n                    'pattern': 'bwbwBWbwB',\n                    'value': 13\n                },\n                'E': {\n                    'pattern': 'BwbwBWbwb',\n                    'value': 14\n                },\n                'F': {\n                    'pattern': 'bwBwBWbwb',\n                    'value': 15\n                },\n                'G': {\n                    'pattern': 'bwbwbWBwB',\n                    'value': 16\n                },\n                'H': {\n                    'pattern': 'BwbwbWBwb',\n                    'value': 17\n                },\n                'I': {\n                    'pattern': 'bwBwbWBwb',\n                    'value': 18\n                },\n                'J': {\n                    'pattern': 'bwbwBWBwb',\n                    'value': 19\n                },\n                'K': {\n                    'pattern': 'BwbwbwbWB',\n                    'value': 20\n                },\n                'L': {\n                    'pattern': 'bwBwbwbWB',\n                    'value': 21\n                },\n                'M': {\n                    'pattern': 'BwBwbwbWb',\n                    'value': 22\n                },\n                'N': {\n                    'pattern': 'bwbwBwbWB',\n                    'value': 23\n                },\n                'O': {\n                    'pattern': 'BwbwBwbWb',\n                    'value': 24\n                },\n                'P': {\n                    'pattern': 'bwBwBwbWb',\n                    'value': 25\n                },\n                'Q': {\n                    'pattern': 'bwbwbwBWB',\n                    'value': 26\n                },\n                'R': {\n                    'pattern': 'BwbwbwBWb',\n                    'value': 27\n                },\n                'S': {\n                    'pattern': 'bwBwbwBWb',\n                    'value': 28\n                },\n                'T': {\n                    'pattern': 'bwbwBwBWb',\n                    'value': 29\n                },\n                'U': {\n                    'pattern': 'BWbwbwbwB',\n                    'value': 30\n                },\n                'V': {\n                    'pattern': 'bWBwbwbwB',\n                    'value': 31\n                },\n                'W': {\n                    'pattern': 'BWBwbwbwb',\n                    'value': 32\n                },\n                'X': {\n                    'pattern': 'bWbwBwbwB',\n                    'value': 33\n                },\n                'Y': {\n                    'pattern': 'BWbwBwbwb',\n                    'value': 34\n                },\n                'Z': {\n                    'pattern': 'bWBwBwbwb',\n                    'value': 35\n                },\n                '-': {\n                    'pattern': 'bWbwbwBwB',\n                    'value': 36\n                },\n                '.': {\n                    'pattern': 'BWbwbwBwb',\n                    'value': 37\n                },\n                ' ': {\n                    'pattern': 'bWBwbwBwb',\n                    'value': 38\n                },\n                '$': {\n                    'pattern': 'bWbWbWbwb',\n                    'value': 39\n                },\n                '/': {\n                    'pattern': 'bWbWbwbWb',\n                    'value': 40\n                },\n                '+': {\n                    'pattern': 'bWbwbWbWb',\n                    'value': 41\n                },\n                '%': {\n                    'pattern': 'bwbWbWbWb',\n                    'value': 42\n                },\n                START: { pattern: 'bWbwBwBwb' }\n            },\n            options: { addCheckSum: false }\n        });\n        encodings.code39extended = encodings.code39.extend(deepExtend({}, code39ExtendedBase, {\n            name: 'Code 39 extended',\n            characterMap: {\n                SHIFT0: {\n                    'pattern': 'bWbwbWbWb',\n                    'value': 41\n                },\n                SHIFT1: {\n                    'pattern': 'bWbWbwbWb',\n                    'value': 40\n                },\n                SHIFT2: {\n                    'pattern': 'bWbWbWbwb',\n                    'value': 39\n                },\n                SHIFT3: {\n                    'pattern': 'bwbWbWbWb',\n                    'value': 42\n                }\n            }\n        }));\n        encodings.code93 = code39Base.extend({\n            name: 'Code 93',\n            cCheckSumTotal: 20,\n            kCheckSumTotal: 15,\n            checkSumMod: 47,\n            initValue: function (value, width, height) {\n                var that = this;\n                that.value = value;\n                that.width = width;\n                that.height = height;\n                that.pattern = [];\n                that.values = [];\n                that.dataLength = value.length;\n            },\n            prepareValues: function () {\n                var that = this, minHeight = Math.max(0.15 * that.width, 24);\n                if (that.height < minHeight) {\n                    throw new Error('Insufficient Height');\n                }\n                that.setBaseUnit();\n                if (that.baseUnit < that.minBaseUnitLength) {\n                    throw new Error('Insufficient Width');\n                }\n            },\n            setBaseUnit: function () {\n                var that = this, checkSumLength = 2;\n                that.baseUnit = that.width / (9 * (that.dataLength + 2 + checkSumLength) + that.quietZoneLength + 1);\n            },\n            addStart: function () {\n                var pattern = this.characterMap.START.pattern;\n                this.addPattern(pattern);\n            },\n            addStop: function () {\n                var that = this;\n                that.addStart();\n                that.pattern.push(that.characterMap.TERMINATION_BAR);\n            },\n            addBase: function (charData) {\n                this.addPattern(charData.pattern);\n                this.values.push(charData.value);\n            },\n            pushCheckSum: function () {\n                var that = this, checkValues = that._getCheckValues(), charData;\n                that.checksum = checkValues.join('');\n                for (var i = 0; i < checkValues.length; i++) {\n                    charData = that.characterMap[that._findCharacterByValue(checkValues[i])];\n                    that.addPattern(charData.pattern);\n                }\n            },\n            _getCheckValues: function () {\n                var that = this, values = that.values, length = values.length, wightedSum = 0, cValue, kValue, idx;\n                for (idx = length - 1; idx >= 0; idx--) {\n                    wightedSum += that.weightedValue(values[idx], length - idx, that.cCheckSumTotal);\n                }\n                cValue = wightedSum % that.checkSumMod;\n                wightedSum = that.weightedValue(cValue, 1, that.kCheckSumTotal);\n                for (idx = length - 1; idx >= 0; idx--) {\n                    wightedSum += that.weightedValue(values[idx], length - idx + 1, that.kCheckSumTotal);\n                }\n                kValue = wightedSum % that.checkSumMod;\n                return [\n                    cValue,\n                    kValue\n                ];\n            },\n            _findCharacterByValue: function (value) {\n                for (var character in this.characterMap) {\n                    if (this.characterMap[character].value === value) {\n                        return character;\n                    }\n                }\n            },\n            weightedValue: function (value, index, total) {\n                return (index % total || total) * value;\n            },\n            addPattern: function (pattern) {\n                var value;\n                for (var i = 0; i < pattern.length; i++) {\n                    value = parseInt(pattern.charAt(i), 10);\n                    this.pattern.push(value);\n                }\n            },\n            characterMap: {\n                '0': {\n                    'pattern': '131112',\n                    'value': 0\n                },\n                '1': {\n                    'pattern': '111213',\n                    'value': 1\n                },\n                '2': {\n                    'pattern': '111312',\n                    'value': 2\n                },\n                '3': {\n                    'pattern': '111411',\n                    'value': 3\n                },\n                '4': {\n                    'pattern': '121113',\n                    'value': 4\n                },\n                '5': {\n                    'pattern': '121212',\n                    'value': 5\n                },\n                '6': {\n                    'pattern': '121311',\n                    'value': 6\n                },\n                '7': {\n                    'pattern': '111114',\n                    'value': 7\n                },\n                '8': {\n                    'pattern': '131211',\n                    'value': 8\n                },\n                '9': {\n                    'pattern': '141111',\n                    'value': 9\n                },\n                'A': {\n                    'pattern': '211113',\n                    'value': 10\n                },\n                'B': {\n                    'pattern': '211212',\n                    'value': 11\n                },\n                'C': {\n                    'pattern': '211311',\n                    'value': 12\n                },\n                'D': {\n                    'pattern': '221112',\n                    'value': 13\n                },\n                'E': {\n                    'pattern': '221211',\n                    'value': 14\n                },\n                'F': {\n                    'pattern': '231111',\n                    'value': 15\n                },\n                'G': {\n                    'pattern': '112113',\n                    'value': 16\n                },\n                'H': {\n                    'pattern': '112212',\n                    'value': 17\n                },\n                'I': {\n                    'pattern': '112311',\n                    'value': 18\n                },\n                'J': {\n                    'pattern': '122112',\n                    'value': 19\n                },\n                'K': {\n                    'pattern': '132111',\n                    'value': 20\n                },\n                'L': {\n                    'pattern': '111123',\n                    'value': 21\n                },\n                'M': {\n                    'pattern': '111222',\n                    'value': 22\n                },\n                'N': {\n                    'pattern': '111321',\n                    'value': 23\n                },\n                'O': {\n                    'pattern': '121122',\n                    'value': 24\n                },\n                'P': {\n                    'pattern': '131121',\n                    'value': 25\n                },\n                'Q': {\n                    'pattern': '212112',\n                    'value': 26\n                },\n                'R': {\n                    'pattern': '212211',\n                    'value': 27\n                },\n                'S': {\n                    'pattern': '211122',\n                    'value': 28\n                },\n                'T': {\n                    'pattern': '211221',\n                    'value': 29\n                },\n                'U': {\n                    'pattern': '221121',\n                    'value': 30\n                },\n                'V': {\n                    'pattern': '222111',\n                    'value': 31\n                },\n                'W': {\n                    'pattern': '112122',\n                    'value': 32\n                },\n                'X': {\n                    'pattern': '112221',\n                    'value': 33\n                },\n                'Y': {\n                    'pattern': '122121',\n                    'value': 34\n                },\n                'Z': {\n                    'pattern': '123111',\n                    'value': 35\n                },\n                '-': {\n                    'pattern': '121131',\n                    'value': 36\n                },\n                '.': {\n                    'pattern': '311112',\n                    'value': 37\n                },\n                ' ': {\n                    'pattern': '311211',\n                    'value': 38\n                },\n                '$': {\n                    'pattern': '321111',\n                    'value': 39\n                },\n                '/': {\n                    'pattern': '112131',\n                    'value': 40\n                },\n                '+': {\n                    'pattern': '113121',\n                    'value': 41\n                },\n                '%': {\n                    'pattern': '211131',\n                    'value': 42\n                },\n                SHIFT0: {\n                    'pattern': '122211',\n                    'value': 46\n                },\n                SHIFT1: {\n                    'pattern': '311121',\n                    'value': 45\n                },\n                SHIFT2: {\n                    'pattern': '121221',\n                    'value': 43\n                },\n                SHIFT3: {\n                    'pattern': '312111',\n                    'value': 44\n                },\n                START: { 'pattern': '111141' },\n                TERMINATION_BAR: '1'\n            }\n        });\n        encodings.code93extended = encodings.code93.extend(deepExtend({}, code39ExtendedBase, {\n            name: 'Code 93 extended',\n            pushCheckSum: function () {\n                var that = this, checkValues = that._getCheckValues(), value;\n                that.checksum = checkValues.join('');\n                for (var i = 0; i < checkValues.length; i++) {\n                    value = checkValues[i];\n                    if (that.shiftValuesAsciiCodes[value]) {\n                        that.addExtended(that.shiftValuesAsciiCodes[value]);\n                    } else {\n                        that.addPattern(that.characterMap[that._findCharacterByValue(value)].pattern);\n                    }\n                }\n            }\n        }));\n        var state128 = kendo.Class.extend({\n            init: function (encoding) {\n                this.encoding = encoding;\n            },\n            addStart: function () {\n            },\n            is: function () {\n            },\n            move: function () {\n            },\n            pushState: function () {\n            }\n        });\n        var state128AB = state128.extend({\n            FNC4: 'FNC4',\n            init: function (encoding, states) {\n                var that = this;\n                that.encoding = encoding;\n                that.states = states;\n                that._initMoves(states);\n            },\n            addStart: function () {\n                this.encoding.addPattern(this.START);\n            },\n            is: function (value, index) {\n                var code = value.charCodeAt(index);\n                return this.isCode(code);\n            },\n            move: function (encodingState) {\n                var that = this, idx = 0;\n                while (!that._moves[idx].call(that, encodingState) && idx < that._moves.length) {\n                    idx++;\n                }\n            },\n            pushState: function (encodingState) {\n                var that = this, states = that.states, value = encodingState.value, maxLength = value.length, code;\n                if (inArray('C', states) >= 0) {\n                    var numberMatch = value.substr(encodingState.index).match(/\\d{4,}/g);\n                    if (numberMatch) {\n                        maxLength = value.indexOf(numberMatch[0], encodingState.index);\n                    }\n                }\n                while ((code = encodingState.value.charCodeAt(encodingState.index)) >= 0 && that.isCode(code) && encodingState.index < maxLength) {\n                    that.encoding.addPattern(that.getValue(code));\n                    encodingState.index++;\n                }\n            },\n            _initMoves: function (states) {\n                var that = this;\n                that._moves = [];\n                if (inArray(that.FNC4, states) >= 0) {\n                    that._moves.push(that._moveFNC);\n                }\n                if (inArray(that.shiftKey, states) >= 0) {\n                    that._moves.push(that._shiftState);\n                }\n                that._moves.push(that._moveState);\n            },\n            _moveFNC: function (encodingState) {\n                if (encodingState.fnc) {\n                    encodingState.fnc = false;\n                    return encodingState.previousState == this.key;\n                }\n            },\n            _shiftState: function (encodingState) {\n                var that = this;\n                if (encodingState.previousState == that.shiftKey && (encodingState.index + 1 >= encodingState.value.length || that.encoding[that.shiftKey].is(encodingState.value, encodingState.index + 1))) {\n                    that.encoding.addPattern(that.SHIFT);\n                    encodingState.shifted = true;\n                    return true;\n                }\n            },\n            _moveState: function () {\n                this.encoding.addPattern(this.MOVE);\n                return true;\n            },\n            SHIFT: 98\n        });\n        var states128 = {};\n        states128.A = state128AB.extend({\n            key: 'A',\n            shiftKey: 'B',\n            isCode: function (code) {\n                return 0 <= code && code < 96;\n            },\n            getValue: function (code) {\n                if (code < 32) {\n                    return code + 64;\n                }\n                return code - 32;\n            },\n            MOVE: 101,\n            START: 103\n        });\n        states128.B = state128AB.extend({\n            key: 'B',\n            shiftKey: 'A',\n            isCode: function (code) {\n                return 32 <= code && code < 128;\n            },\n            getValue: function (code) {\n                return code - 32;\n            },\n            MOVE: 100,\n            START: 104\n        });\n        states128.C = state128.extend({\n            key: 'C',\n            addStart: function () {\n                this.encoding.addPattern(this.START);\n            },\n            is: function (value, index) {\n                var next4 = getNext(value, index, 4);\n                return (index + 4 <= value.length || value.length == 2) && numberRegex.test(next4);\n            },\n            move: function () {\n                this.encoding.addPattern(this.MOVE);\n            },\n            pushState: function (encodingState) {\n                var code;\n                while ((code = getNext(encodingState.value, encodingState.index, 2)) && numberRegex.test(code) && code.length == 2) {\n                    this.encoding.addPattern(parseInt(code, 10));\n                    encodingState.index += 2;\n                }\n            },\n            getValue: function (code) {\n                return code;\n            },\n            MOVE: 99,\n            START: 105\n        });\n        states128.FNC4 = state128.extend({\n            key: 'FNC4',\n            dependentStates: [\n                'A',\n                'B'\n            ],\n            init: function (encoding, states) {\n                this.encoding = encoding;\n                this._initSubStates(states);\n            },\n            addStart: function (encodingState) {\n                var code = encodingState.value.charCodeAt(0) - 128, subState = this._getSubState(code);\n                this.encoding[subState].addStart();\n            },\n            is: function (value, index) {\n                var code = value.charCodeAt(index);\n                return this.isCode(code);\n            },\n            isCode: function (code) {\n                return 128 <= code && code < 256;\n            },\n            pushState: function (encodingState) {\n                var that = this, subState = that._initSubState(encodingState), encoding = that.encoding, length = subState.value.length;\n                encodingState.index += length;\n                if (length < 3) {\n                    var code;\n                    for (; subState.index < length; subState.index++) {\n                        code = subState.value.charCodeAt(subState.index);\n                        subState.state = that._getSubState(code);\n                        if (subState.previousState != subState.state) {\n                            subState.previousState = subState.state;\n                            encoding[subState.state].move(subState);\n                        }\n                        encoding.addPattern(encoding[subState.state].MOVE);\n                        encoding.addPattern(encoding[subState.state].getValue(code));\n                    }\n                } else {\n                    if (subState.state != subState.previousState) {\n                        encoding[subState.state].move(subState);\n                    }\n                    that._pushStart(subState);\n                    encoding.pushData(subState, that.subStates);\n                    if (encodingState.index < encodingState.value.length) {\n                        that._pushStart(subState);\n                    }\n                }\n                encodingState.fnc = true;\n                encodingState.state = subState.state;\n            },\n            _pushStart: function (subState) {\n                var that = this;\n                that.encoding.addPattern(that.encoding[subState.state].MOVE);\n                that.encoding.addPattern(that.encoding[subState.state].MOVE);\n            },\n            _initSubState: function (encodingState) {\n                var that = this, subState = {\n                        value: that._getAll(encodingState.value, encodingState.index),\n                        index: 0\n                    };\n                subState.state = that._getSubState(subState.value.charCodeAt(0));\n                subState.previousState = encodingState.previousState == that.key ? subState.state : encodingState.previousState;\n                return subState;\n            },\n            _initSubStates: function (states) {\n                var that = this;\n                that.subStates = [];\n                for (var i = 0; i < states.length; i++) {\n                    if (inArray(states[i], that.dependentStates) >= 0) {\n                        that.subStates.push(states[i]);\n                    }\n                }\n            },\n            _getSubState: function (code) {\n                var that = this;\n                for (var i = 0; i < that.subStates.length; i++) {\n                    if (that.encoding[that.subStates[i]].isCode(code)) {\n                        return that.subStates[i];\n                    }\n                }\n            },\n            _getAll: function (value, index) {\n                var code, result = '';\n                while ((code = value.charCodeAt(index++)) && this.isCode(code)) {\n                    result += String.fromCharCode(code - 128);\n                }\n                return result;\n            }\n        });\n        states128.FNC1 = state128.extend({\n            key: 'FNC1',\n            startState: 'C',\n            dependentStates: [\n                'C',\n                'B'\n            ],\n            startAI: '(',\n            endAI: ')',\n            init: function (encoding, states) {\n                this.encoding = encoding;\n                this.states = states;\n            },\n            addStart: function () {\n                this.encoding[this.startState].addStart();\n            },\n            is: function () {\n                return inArray(this.key, this.states) >= 0;\n            },\n            pushState: function (encodingState) {\n                var that = this, encoding = that.encoding, value = encodingState.value.replace(/\\s/g, ''), regexSeparators = new RegExp('[' + that.startAI + that.endAI + ']', 'g'), index = encodingState.index, subState = { state: that.startState }, current, nextStart, separatorLength;\n                encoding.addPattern(that.START);\n                while (true) {\n                    subState.index = 0;\n                    separatorLength = value.charAt(index) === that.startAI ? 2 : 0;\n                    current = separatorLength > 0 ? that.getBySeparator(value, index) : that.getByLength(value, index);\n                    if (current.ai.length) {\n                        nextStart = index + separatorLength + current.id.length + current.ai.length;\n                    } else {\n                        nextStart = value.indexOf(that.startAI, index + 1);\n                        if (nextStart < 0) {\n                            if (index + current.ai.max + current.id.length + separatorLength < value.length) {\n                                throw new Error('Separators are required after variable length identifiers');\n                            }\n                            nextStart = value.length;\n                        }\n                    }\n                    subState.value = value.substring(index, nextStart).replace(regexSeparators, '');\n                    that.validate(current, subState.value);\n                    encoding.pushData(subState, that.dependentStates);\n                    if (nextStart >= value.length) {\n                        break;\n                    }\n                    index = nextStart;\n                    if (subState.state != that.startState) {\n                        encoding[that.startState].move(subState);\n                        subState.state = that.startState;\n                    }\n                    if (!current.ai.length) {\n                        encoding.addPattern(that.START);\n                    }\n                }\n                encodingState.index = encodingState.value.length;\n            },\n            validate: function (current, value) {\n                var code = value.substr(current.id.length), ai = current.ai;\n                if (!ai.type && !numberRegex.test(code)) {\n                    throw new Error('Application identifier ' + current.id + ' is numeric only but contains non numeric character(s).');\n                }\n                if (ai.type == 'alphanumeric' && !alphanumericRegex.test(code)) {\n                    throw new Error('Application identifier ' + current.id + ' is alphanumeric only but contains non alphanumeric character(s).');\n                }\n                if (ai.length && ai.length !== code.length) {\n                    throw new Error('Application identifier ' + current.id + ' must be ' + ai.length + ' characters long.');\n                }\n                if (ai.min && ai.min > code.length) {\n                    throw new Error('Application identifier ' + current.id + ' must be at least ' + ai.min + ' characters long.');\n                }\n                if (ai.max && ai.max < code.length) {\n                    throw new Error('Application identifier ' + current.id + ' must be at most ' + ai.max + ' characters long.');\n                }\n            },\n            getByLength: function (value, index) {\n                var that = this, id, ai;\n                for (var i = 2; i <= 4; i++) {\n                    id = getNext(value, index, i);\n                    ai = that.getAI(id) || that.getAI(id.substring(0, id.length - 1));\n                    if (ai) {\n                        return {\n                            id: id,\n                            ai: ai\n                        };\n                    }\n                }\n                that.unsupportedAIError(id);\n            },\n            unsupportedAIError: function (id) {\n                throw new Error(kendo.format('\\'{0}\\' is not a supported Application Identifier'), id);\n            },\n            getBySeparator: function (value, index) {\n                var that = this, start = value.indexOf(that.startAI, index), end = value.indexOf(that.endAI, start), id = value.substring(start + 1, end), ai = that.getAI(id) || that.getAI(id.substr(id.length - 1));\n                if (!ai) {\n                    that.unsupportedAIError(id);\n                }\n                return {\n                    ai: ai,\n                    id: id\n                };\n            },\n            getAI: function (id) {\n                var ai = this.applicationIdentifiers, multiKey = ai.multiKey;\n                if (ai[id]) {\n                    return ai[id];\n                }\n                for (var i = 0; i < multiKey.length; i++) {\n                    if (multiKey[i].ids && inArray(id, multiKey[i].ids) >= 0) {\n                        return multiKey[i].type;\n                    } else if (multiKey[i].ranges) {\n                        var ranges = multiKey[i].ranges;\n                        for (var j = 0; j < ranges.length; j++) {\n                            if (ranges[j][0] <= id && id <= ranges[j][1]) {\n                                return multiKey[i].type;\n                            }\n                        }\n                    }\n                }\n            },\n            applicationIdentifiers: {\n                '22': {\n                    max: 29,\n                    type: 'alphanumeric'\n                },\n                '402': { length: 17 },\n                '7004': {\n                    max: 4,\n                    type: 'alphanumeric'\n                },\n                '242': {\n                    max: 6,\n                    type: 'alphanumeric'\n                },\n                '8020': {\n                    max: 25,\n                    type: 'alphanumeric'\n                },\n                '703': {\n                    min: 3,\n                    max: 30,\n                    type: 'alphanumeric'\n                },\n                '8008': {\n                    min: 8,\n                    max: 12,\n                    type: 'alphanumeric'\n                },\n                '253': {\n                    min: 13,\n                    max: 17,\n                    type: 'alphanumeric'\n                },\n                '8003': {\n                    min: 14,\n                    max: 30,\n                    type: 'alphanumeric'\n                },\n                multiKey: [\n                    {\n                        ids: [\n                            '15',\n                            '17',\n                            '8005',\n                            '8100'\n                        ],\n                        ranges: [\n                            [\n                                11,\n                                13\n                            ],\n                            [\n                                310,\n                                316\n                            ],\n                            [\n                                320,\n                                336\n                            ],\n                            [\n                                340,\n                                369\n                            ]\n                        ],\n                        type: { length: 6 }\n                    },\n                    {\n                        ids: [\n                            '240',\n                            '241',\n                            '250',\n                            '251',\n                            '400',\n                            '401',\n                            '403',\n                            '7002',\n                            '8004',\n                            '8007',\n                            '8110'\n                        ],\n                        ranges: [[90 - 99]],\n                        type: {\n                            max: 30,\n                            type: 'alphanumeric'\n                        }\n                    },\n                    {\n                        ids: ['7001'],\n                        ranges: [[\n                                410,\n                                414\n                            ]],\n                        type: { length: 13 }\n                    },\n                    {\n                        ids: [\n                            '10',\n                            '21',\n                            '254',\n                            '420',\n                            '8002'\n                        ],\n                        type: {\n                            max: 20,\n                            type: 'alphanumeric'\n                        }\n                    },\n                    {\n                        ids: [\n                            '00',\n                            '8006',\n                            '8017',\n                            '8018'\n                        ],\n                        type: { length: 18 }\n                    },\n                    {\n                        ids: [\n                            '01',\n                            '02',\n                            '8001'\n                        ],\n                        type: { length: 14 }\n                    },\n                    {\n                        ids: ['422'],\n                        ranges: [[\n                                424,\n                                426\n                            ]],\n                        type: { length: 3 }\n                    },\n                    {\n                        ids: [\n                            '20',\n                            '8102'\n                        ],\n                        type: { length: 2 }\n                    },\n                    {\n                        ids: [\n                            '30',\n                            '37'\n                        ],\n                        type: {\n                            max: 8,\n                            type: 'alphanumeric'\n                        }\n                    },\n                    {\n                        ids: [\n                            '390',\n                            '392'\n                        ],\n                        type: {\n                            max: 15,\n                            type: 'alphanumeric'\n                        }\n                    },\n                    {\n                        ids: [\n                            '421',\n                            '423'\n                        ],\n                        type: {\n                            min: 3,\n                            max: 15,\n                            type: 'alphanumeric'\n                        }\n                    },\n                    {\n                        ids: [\n                            '391',\n                            '393'\n                        ],\n                        type: {\n                            min: 3,\n                            max: 18,\n                            type: 'alphanumeric'\n                        }\n                    },\n                    {\n                        ids: [\n                            '7003',\n                            '8101'\n                        ],\n                        type: { length: 10 }\n                    }\n                ]\n            },\n            START: 102\n        });\n        var code128Base = Encoding.extend({\n            init: function (options) {\n                Encoding.fn.init.call(this, options);\n                this._initStates();\n            },\n            _initStates: function () {\n                var that = this;\n                for (var i = 0; i < that.states.length; i++) {\n                    that[that.states[i]] = new states128[that.states[i]](that, that.states);\n                }\n            },\n            initValue: function (value, width, height) {\n                var that = this;\n                that.pattern = [];\n                that.value = value;\n                that.width = width;\n                that.height = height;\n                that.checkSum = 0;\n                that.totalUnits = 0;\n                that.index = 0;\n                that.position = 1;\n            },\n            addData: function () {\n                var that = this, encodingState = {\n                        value: that.value,\n                        index: 0,\n                        state: ''\n                    };\n                if (that.value.length === 0) {\n                    return;\n                }\n                encodingState.state = encodingState.previousState = that.getNextState(encodingState, that.states);\n                that.addStart(encodingState);\n                that.pushData(encodingState, that.states);\n                that.addCheckSum();\n                that.addStop();\n                that.setBaseUnit();\n            },\n            pushData: function (encodingState, states) {\n                var that = this;\n                while (true) {\n                    that[encodingState.state].pushState(encodingState);\n                    if (encodingState.index >= encodingState.value.length) {\n                        break;\n                    }\n                    if (!encodingState.shifted) {\n                        encodingState.previousState = encodingState.state;\n                        encodingState.state = that.getNextState(encodingState, states);\n                        that[encodingState.state].move(encodingState);\n                    } else {\n                        var temp = encodingState.state;\n                        encodingState.state = encodingState.previousState;\n                        encodingState.previousState = temp;\n                        encodingState.shifted = false;\n                    }\n                }\n            },\n            addStart: function (encodingState) {\n                this[encodingState.state].addStart(encodingState);\n                this.position = 1;\n            },\n            addCheckSum: function () {\n                var that = this;\n                that.checksum = that.checkSum % 103;\n                that.addPattern(that.checksum);\n            },\n            addStop: function () {\n                this.addPattern(this.STOP);\n            },\n            setBaseUnit: function () {\n                var that = this;\n                that.baseUnit = that.width / (that.totalUnits + that.quietZoneLength);\n            },\n            addPattern: function (code) {\n                var that = this, pattern = that.characterMap[code].toString(), value;\n                for (var i = 0; i < pattern.length; i++) {\n                    value = parseInt(pattern.charAt(i), 10);\n                    that.pattern.push(value);\n                    that.totalUnits += value;\n                }\n                that.checkSum += code * that.position++;\n            },\n            getNextState: function (encodingState, states) {\n                for (var i = 0; i < states.length; i++) {\n                    if (this[states[i]].is(encodingState.value, encodingState.index)) {\n                        return states[i];\n                    }\n                }\n                this.invalidCharacterError(encodingState.value.charAt(encodingState.index));\n            },\n            characterMap: [\n                212222,\n                222122,\n                222221,\n                121223,\n                121322,\n                131222,\n                122213,\n                122312,\n                132212,\n                221213,\n                221312,\n                231212,\n                112232,\n                122132,\n                122231,\n                113222,\n                123122,\n                123221,\n                223211,\n                221132,\n                221231,\n                213212,\n                223112,\n                312131,\n                311222,\n                321122,\n                321221,\n                312212,\n                322112,\n                322211,\n                212123,\n                212321,\n                232121,\n                111323,\n                131123,\n                131321,\n                112313,\n                132113,\n                132311,\n                211313,\n                231113,\n                231311,\n                112133,\n                112331,\n                132131,\n                113123,\n                113321,\n                133121,\n                313121,\n                211331,\n                231131,\n                213113,\n                213311,\n                213131,\n                311123,\n                311321,\n                331121,\n                312113,\n                312311,\n                332111,\n                314111,\n                221411,\n                431111,\n                111224,\n                111422,\n                121124,\n                121421,\n                141122,\n                141221,\n                112214,\n                112412,\n                122114,\n                122411,\n                142112,\n                142211,\n                241211,\n                221114,\n                413111,\n                241112,\n                134111,\n                111242,\n                121142,\n                121241,\n                114212,\n                124112,\n                124211,\n                411212,\n                421112,\n                421211,\n                212141,\n                214121,\n                412121,\n                111143,\n                111341,\n                131141,\n                114113,\n                114311,\n                411113,\n                411311,\n                113141,\n                114131,\n                311141,\n                411131,\n                211412,\n                211214,\n                211232,\n                2331112\n            ],\n            STOP: 106\n        });\n        encodings.code128a = code128Base.extend({\n            name: 'Code 128 A',\n            states: ['A']\n        });\n        encodings.code128b = code128Base.extend({\n            name: 'Code 128 B',\n            states: ['B']\n        });\n        encodings.code128c = code128Base.extend({\n            name: 'Code 128 C',\n            states: ['C']\n        });\n        encodings.code128 = code128Base.extend({\n            name: 'Code 128',\n            states: [\n                'C',\n                'B',\n                'A',\n                'FNC4'\n            ]\n        });\n        encodings['gs1-128'] = code128Base.extend({\n            name: 'Code GS1-128',\n            states: [\n                'FNC1',\n                'C',\n                'B'\n            ]\n        });\n        var msiBase = Encoding.extend({\n            initValue: function (value, width) {\n                var that = this;\n                that.pattern = [];\n                that.value = value;\n                that.checkSumLength = 0;\n                that.width = width;\n            },\n            setBaseUnit: function () {\n                var that = this, startStopLength = 7;\n                that.baseUnit = that.width / (12 * (that.value.length + that.checkSumLength) + that.quietZoneLength + startStopLength);\n            },\n            addData: function () {\n                var that = this, value = that.value;\n                that.addPattern(that.START);\n                for (var i = 0; i < value.length; i++) {\n                    that.addCharacter(value.charAt(i));\n                }\n                if (that.options.addCheckSum) {\n                    that.addCheckSum();\n                }\n                that.addPattern(that.STOP);\n                that.setBaseUnit();\n            },\n            addCharacter: function (character) {\n                var that = this, pattern = that.characterMap[character];\n                if (!pattern) {\n                    that.invalidCharacterError(character);\n                }\n                that.addPattern(pattern);\n            },\n            addPattern: function (pattern) {\n                for (var i = 0; i < pattern.length; i++) {\n                    this.pattern.push(parseInt(pattern.charAt(i), 10));\n                }\n            },\n            addCheckSum: function () {\n                var that = this, checkSumFunction = that.checkSums[that.checkSumType], checkValues;\n                checkValues = checkSumFunction.call(that.checkSums, that.value);\n                that.checksum = checkValues.join('');\n                for (var i = 0; i < checkValues.length; i++) {\n                    that.checkSumLength++;\n                    that.addPattern(that.characterMap[checkValues[i]]);\n                }\n            },\n            checkSums: {\n                Modulo10: function (value) {\n                    var checkValues = [\n                            0,\n                            ''\n                        ], odd = value.length % 2, idx, evenSum, oddSum;\n                    for (idx = 0; idx < value.length; idx++) {\n                        checkValues[(idx + odd) % 2] += parseInt(value.charAt(idx), 10);\n                    }\n                    oddSum = checkValues[0];\n                    evenSum = (checkValues[1] * 2).toString();\n                    for (idx = 0; idx < evenSum.length; idx++) {\n                        oddSum += parseInt(evenSum.charAt(idx), 10);\n                    }\n                    return [(10 - oddSum % 10) % 10];\n                },\n                Modulo11: function (value) {\n                    var weightedSum = 0, mod = 11, length = value.length, weight, checkValue;\n                    for (var i = 0; i < length; i++) {\n                        weight = ((length - i) % 6 || 6) + 1;\n                        weightedSum += weight * value.charAt(i);\n                    }\n                    checkValue = (mod - weightedSum % mod) % mod;\n                    if (checkValue != 10) {\n                        return [checkValue];\n                    }\n                    return [\n                        1,\n                        0\n                    ];\n                },\n                Modulo11Modulo10: function (value) {\n                    var checkValues = this.Modulo11(value), mod11Value;\n                    mod11Value = value + checkValues[0];\n                    return checkValues.concat(this.Modulo10(mod11Value));\n                },\n                Modulo10Modulo10: function (value) {\n                    var checkValues = this.Modulo10(value), mod10Value;\n                    mod10Value = value + checkValues[0];\n                    return checkValues.concat(this.Modulo10(mod10Value));\n                }\n            },\n            characterMap: [\n                '12121212',\n                '12121221',\n                '12122112',\n                '12122121',\n                '12211212',\n                '12211221',\n                '12212112',\n                '12212121',\n                '21121212',\n                '21121221'\n            ],\n            START: '21',\n            STOP: '121',\n            checkSumType: ''\n        });\n        encodings.msimod10 = msiBase.extend({\n            name: 'MSI Modulo10',\n            checkSumType: 'Modulo10'\n        });\n        encodings.msimod11 = msiBase.extend({\n            name: 'MSI Modulo11',\n            checkSumType: 'Modulo11'\n        });\n        encodings.msimod1110 = msiBase.extend({\n            name: 'MSI Modulo11 Modulo10',\n            checkSumType: 'Modulo11Modulo10'\n        });\n        encodings.msimod1010 = msiBase.extend({\n            name: 'MSI Modulo10 Modulo10',\n            checkSumType: 'Modulo10Modulo10'\n        });\n        encodings.code11 = Encoding.extend({\n            name: 'Code 11',\n            cCheckSumTotal: 10,\n            kCheckSumTotal: 9,\n            kCheckSumMinLength: 10,\n            checkSumMod: 11,\n            DASH_VALUE: 10,\n            DASH: '-',\n            START: '112211',\n            STOP: '11221',\n            initValue: function (value, width) {\n                var that = this;\n                that.pattern = [];\n                that.value = value;\n                that.width = width;\n                that.totalUnits = 0;\n            },\n            addData: function () {\n                var that = this;\n                var value = that.value;\n                that.addPattern(that.START);\n                for (var i = 0; i < value.length; i++) {\n                    that.addCharacter(value.charAt(i));\n                }\n                if (that.options.addCheckSum) {\n                    that.addCheckSum();\n                }\n                that.addPattern(that.STOP);\n                that.setBaseUnit();\n            },\n            setBaseUnit: function () {\n                var that = this;\n                that.baseUnit = that.width / (that.totalUnits + that.quietZoneLength);\n            },\n            addCheckSum: function () {\n                var that = this, value = that.value, length = value.length, cValue;\n                cValue = that.getWeightedSum(value, length, that.cCheckSumTotal) % that.checkSumMod;\n                that.checksum = cValue + '';\n                that.addPattern(that.characterMap[cValue]);\n                length++;\n                if (length >= that.kCheckSumMinLength) {\n                    var kValue = (cValue + that.getWeightedSum(value, length, that.kCheckSumTotal)) % that.checkSumMod;\n                    that.checksum += kValue;\n                    that.addPattern(that.characterMap[kValue]);\n                }\n            },\n            getWeightedSum: function (value, length, total) {\n                var weightedSum = 0;\n                for (var i = 0; i < value.length; i++) {\n                    weightedSum += this.weightedValue(this.getValue(value.charAt(i)), length, i, total);\n                }\n                return weightedSum;\n            },\n            weightedValue: function (value, length, index, total) {\n                var weight = (length - index) % total || total;\n                return weight * value;\n            },\n            getValue: function (character) {\n                var that = this;\n                if (!isNaN(character)) {\n                    return parseInt(character, 10);\n                } else if (character !== that.DASH) {\n                    that.invalidCharacterError(character);\n                }\n                return that.DASH_VALUE;\n            },\n            addCharacter: function (character) {\n                var that = this, value = that.getValue(character), pattern = that.characterMap[value];\n                that.addPattern(pattern);\n            },\n            addPattern: function (pattern) {\n                var value;\n                for (var i = 0; i < pattern.length; i++) {\n                    value = parseInt(pattern.charAt(i), 10);\n                    this.pattern.push(value);\n                    this.totalUnits += value;\n                }\n            },\n            characterMap: [\n                '111121',\n                '211121',\n                '121121',\n                '221111',\n                '112121',\n                '212111',\n                '122111',\n                '111221',\n                '211211',\n                '211111',\n                '112111'\n            ],\n            options: { addCheckSum: true }\n        });\n        encodings.postnet = Encoding.extend({\n            name: 'Postnet',\n            START: '2',\n            VALID_CODE_LENGTHS: [\n                5,\n                9,\n                11\n            ],\n            DIGIT_SEPARATOR: '-',\n            initValue: function (value, width, height) {\n                var that = this;\n                that.height = height;\n                that.width = width;\n                that.baseHeight = height / 2;\n                that.value = value.replace(new RegExp(that.DIGIT_SEPARATOR, 'g'), '');\n                that.pattern = [];\n                that.validate(that.value);\n                that.checkSum = 0;\n                that.setBaseUnit();\n            },\n            addData: function () {\n                var that = this, value = that.value;\n                that.addPattern(that.START);\n                for (var i = 0; i < value.length; i++) {\n                    that.addCharacter(value.charAt(i));\n                }\n                if (that.options.addCheckSum) {\n                    that.addCheckSum();\n                }\n                that.addPattern(that.START);\n                that.pattern.pop();\n            },\n            addCharacter: function (character) {\n                var that = this, pattern = that.characterMap[character];\n                that.checkSum += parseInt(character, 10);\n                that.addPattern(pattern);\n            },\n            addCheckSum: function () {\n                var that = this;\n                that.checksum = (10 - that.checkSum % 10) % 10;\n                that.addCharacter(that.checksum);\n            },\n            setBaseUnit: function () {\n                var that = this, startStopLength = 3;\n                that.baseUnit = that.width / ((that.value.length + 1) * 10 + startStopLength + that.quietZoneLength);\n            },\n            validate: function (value) {\n                var that = this;\n                if (!numberRegex.test(value)) {\n                    that.invalidCharacterError(value.match(/[^0-9]/)[0]);\n                }\n                if (inArray(value.length, that.VALID_CODE_LENGTHS) < 0) {\n                    throw new Error('Invalid value length. Valid lengths for the Postnet symbology are ' + that.VALID_CODE_LENGTHS.join(','));\n                }\n            },\n            addPattern: function (pattern) {\n                var that = this, y1;\n                for (var i = 0; i < pattern.length; i++) {\n                    y1 = that.height - that.baseHeight * pattern.charAt(i);\n                    that.pattern.push({\n                        width: 1,\n                        y1: y1,\n                        y2: that.height\n                    });\n                    that.pattern.push(1);\n                }\n            },\n            characterMap: [\n                '22111',\n                '11122',\n                '11212',\n                '11221',\n                '12112',\n                '12121',\n                '12211',\n                '21112',\n                '21121',\n                '21211'\n            ]\n        });\n        encodings.ean13 = Encoding.extend({\n            initValue: function (value, width, height) {\n                value += '';\n                if (value.length != 12 || /\\D/.test(value)) {\n                    throw new Error('The value of the \"EAN13\" encoding should be 12 symbols');\n                }\n                var that = this;\n                that.pattern = [];\n                that.options.height = height;\n                that.baseUnit = width / (95 + that.quietZoneLength);\n                that.value = value;\n                that.checksum = that.calculateChecksum();\n                that.leftKey = value[0];\n                that.leftPart = value.substr(1, 6);\n                that.rightPart = value.substr(7) + that.checksum;\n            },\n            addData: function () {\n                var that = this;\n                that.addPieces(that.characterMap.start);\n                that.addSide(that.leftPart, that.leftKey);\n                that.addPieces(that.characterMap.middle);\n                that.addSide(that.rightPart);\n                that.addPieces(that.characterMap.start);\n            },\n            addSide: function (leftPart, key) {\n                var that = this;\n                for (var i = 0; i < leftPart.length; i++) {\n                    if (key && parseInt(that.keyTable[key].charAt(i), 10)) {\n                        that.addPieces(Array.prototype.slice.call(that.characterMap.digits[leftPart.charAt(i)]).reverse(), true);\n                    } else {\n                        that.addPieces(that.characterMap.digits[leftPart.charAt(i)], true);\n                    }\n                }\n            },\n            addPieces: function (arrToAdd, limitedHeight) {\n                var that = this;\n                for (var i = 0; i < arrToAdd.length; i++) {\n                    if (limitedHeight) {\n                        that.pattern.push({\n                            y1: 0,\n                            y2: that.options.height * 0.95,\n                            width: arrToAdd[i]\n                        });\n                    } else {\n                        that.pattern.push(arrToAdd[i]);\n                    }\n                }\n            },\n            calculateChecksum: function () {\n                var odd = 0, even = 0, value = this.value.split('').reverse().join('');\n                for (var i = 0; i < value.length; i++) {\n                    if (i % 2) {\n                        even += parseInt(value.charAt(i), 10);\n                    } else {\n                        odd += parseInt(value.charAt(i), 10);\n                    }\n                }\n                var checksum = (10 - (3 * odd + even) % 10) % 10;\n                return checksum;\n            },\n            keyTable: [\n                '000000',\n                '001011',\n                '001101',\n                '001110',\n                '010011',\n                '011001',\n                '011100',\n                '010101',\n                '010110',\n                '011010'\n            ],\n            characterMap: {\n                digits: [\n                    [\n                        3,\n                        2,\n                        1,\n                        1\n                    ],\n                    [\n                        2,\n                        2,\n                        2,\n                        1\n                    ],\n                    [\n                        2,\n                        1,\n                        2,\n                        2\n                    ],\n                    [\n                        1,\n                        4,\n                        1,\n                        1\n                    ],\n                    [\n                        1,\n                        1,\n                        3,\n                        2\n                    ],\n                    [\n                        1,\n                        2,\n                        3,\n                        1\n                    ],\n                    [\n                        1,\n                        1,\n                        1,\n                        4\n                    ],\n                    [\n                        1,\n                        3,\n                        1,\n                        2\n                    ],\n                    [\n                        1,\n                        2,\n                        1,\n                        3\n                    ],\n                    [\n                        3,\n                        1,\n                        1,\n                        2\n                    ]\n                ],\n                start: [\n                    1,\n                    1,\n                    1\n                ],\n                middle: [\n                    1,\n                    1,\n                    1,\n                    1,\n                    1\n                ]\n            }\n        });\n        encodings.ean8 = encodings.ean13.extend({\n            initValue: function (value, width, height) {\n                var that = this;\n                if (value.length != 7 || /\\D/.test(value)) {\n                    throw new Error('Invalid value provided');\n                }\n                that.value = value;\n                that.options.height = height;\n                that.checksum = that.calculateChecksum(that.value);\n                that.leftPart = that.value.substr(0, 4);\n                that.rightPart = that.value.substr(4) + that.checksum;\n                that.pattern = [];\n                that.baseUnit = width / (67 + that.quietZoneLength);\n            }\n        });\n        var Barcode = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that.element = $(element);\n                that.wrapper = that.element;\n                that.element.addClass('k-barcode').css('display', 'block');\n                that.surfaceWrap = $('<div />').css('position', 'relative').appendTo(this.element);\n                that.surface = draw.Surface.create(that.surfaceWrap, { type: that.options.renderAs });\n                that._setOptions(options);\n                if (options && defined(options.value)) {\n                    that.redraw();\n                }\n            },\n            setOptions: function (options) {\n                this._setOptions(options);\n                this.redraw();\n            },\n            redraw: function () {\n                var size = this._getSize();\n                this.surface.clear();\n                this.surface.setSize({\n                    width: size.width,\n                    height: size.height\n                });\n                this.createVisual();\n                this.surface.draw(this.visual);\n            },\n            getSize: function () {\n                return kendo.dimensions(this.element);\n            },\n            _resize: function () {\n                this.redraw();\n            },\n            createVisual: function () {\n                this.visual = this._render();\n            },\n            _render: function () {\n                var that = this, options = that.options, value = options.value, textOptions = options.text, textMargin = dataviz.getSpacing(textOptions.margin), size = that._getSize(), border = options.border || {}, encoding = that.encoding, contentBox = new Box2D(0, 0, size.width, size.height).unpad(border.width).unpad(options.padding), barHeight = contentBox.height(), result, textToDisplay, textHeight;\n                var visual = new draw.Group();\n                that.contentBox = contentBox;\n                visual.append(that._getBackground(size));\n                if (textOptions.visible) {\n                    textHeight = draw.util.measureText(value, { font: textOptions.font }).height;\n                    barHeight -= textHeight + textMargin.top + textMargin.bottom;\n                }\n                result = encoding.encode(value, contentBox.width(), barHeight);\n                if (textOptions.visible) {\n                    textToDisplay = value;\n                    if (options.checksum && defined(encoding.checksum)) {\n                        textToDisplay += ' ' + encoding.checksum;\n                    }\n                    visual.append(that._getText(textToDisplay));\n                }\n                that.barHeight = barHeight;\n                this._bandsGroup = this._getBands(result.pattern, result.baseUnit);\n                visual.append(this._bandsGroup);\n                return visual;\n            },\n            exportVisual: function () {\n                return this._render();\n            },\n            _getSize: function () {\n                var that = this, element = that.element, size = new geom.Size(DEFAULT_WIDTH, DEFAULT_HEIGHT);\n                if (element.width() > 0) {\n                    size.width = element.width();\n                }\n                if (element.height() > 0) {\n                    size.height = element.height();\n                }\n                if (that.options.width) {\n                    size.width = that.options.width;\n                }\n                if (that.options.height) {\n                    size.height = that.options.height;\n                }\n                return size;\n            },\n            value: function (value) {\n                var that = this;\n                if (!defined(value)) {\n                    return that.options.value;\n                }\n                that.options.value = value + '';\n                that.redraw();\n            },\n            _getBands: function (pattern, baseUnit) {\n                var that = this, contentBox = that.contentBox, position = contentBox.x1, step, item;\n                var group = new draw.Group();\n                for (var i = 0; i < pattern.length; i++) {\n                    item = isPlainObject(pattern[i]) ? pattern[i] : {\n                        width: pattern[i],\n                        y1: 0,\n                        y2: that.barHeight\n                    };\n                    step = item.width * baseUnit;\n                    if (i % 2) {\n                        var rect = geom.Rect.fromPoints(new geom.Point(position, item.y1 + contentBox.y1), new geom.Point(position + step, item.y2 + contentBox.y1));\n                        var path = draw.Path.fromRect(rect, {\n                            fill: { color: that.options.color },\n                            stroke: null\n                        });\n                        group.append(path);\n                    }\n                    position += step;\n                }\n                return group;\n            },\n            _getBackground: function (size) {\n                var that = this, options = that.options, border = options.border || {};\n                var box = new Box2D(0, 0, size.width, size.height).unpad(border.width / 2);\n                var path = draw.Path.fromRect(box.toRect(), {\n                    fill: { color: options.background },\n                    stroke: {\n                        color: border.width ? border.color : '',\n                        width: border.width,\n                        dashType: border.dashType\n                    }\n                });\n                return path;\n            },\n            _getText: function (value) {\n                var that = this, textOptions = that.options.text, text = that._textbox = new TextBox(value, {\n                        font: textOptions.font,\n                        color: textOptions.color,\n                        align: 'center',\n                        vAlign: 'bottom',\n                        margin: textOptions.margin\n                    });\n                text.reflow(that.contentBox);\n                text.renderVisual();\n                return text.visual;\n            },\n            _setOptions: function (options) {\n                var that = this;\n                that.type = (options.type || that.options.type).toLowerCase();\n                if (that.type == 'upca') {\n                    that.type = 'ean13';\n                    options.value = '0' + options.value;\n                }\n                if (that.type == 'upce') {\n                    that.type = 'ean8';\n                    options.value = '0' + options.value;\n                }\n                if (!encodings[that.type]) {\n                    throw new Error('Encoding ' + that.type + 'is not supported.');\n                }\n                that.encoding = new encodings[that.type]();\n                that.options = extend(true, that.options, options);\n            },\n            options: {\n                name: 'Barcode',\n                renderAs: 'svg',\n                value: '',\n                type: 'code39',\n                checksum: false,\n                width: 0,\n                height: 0,\n                color: 'black',\n                background: 'white',\n                text: {\n                    visible: true,\n                    font: '16px Consolas, Monaco, Sans Mono, monospace, sans-serif',\n                    color: 'black',\n                    margin: {\n                        top: 0,\n                        bottom: 0,\n                        left: 0,\n                        right: 0\n                    }\n                },\n                border: {\n                    width: 0,\n                    dashType: 'solid',\n                    color: 'black'\n                },\n                padding: {\n                    top: 0,\n                    bottom: 0,\n                    left: 0,\n                    right: 0\n                }\n            }\n        });\n        dataviz.ExportMixin.extend(Barcode.fn);\n        dataviz.ui.plugin(Barcode);\n        kendo.deepExtend(dataviz, {\n            encodings: encodings,\n            Encoding: Encoding\n        });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}