{"version": 3, "sources": ["kendo.filebrowser.js"], "names": ["f", "define", "$", "undefined", "bindDragEventWrappers", "element", "onDragEnter", "onDragLeave", "hideInterval", "lastDrag", "on", "NS", "Date", "setInterval", "sinceLastDrag", "clearInterval", "concatPaths", "path", "name", "match", "sizeF<PERSON>atter", "value", "suffix", "Math", "round", "fieldName", "fields", "descriptor", "isPlainObject", "from", "field", "offsetTop", "FileBrowser", "SearchBox", "Breadcrumbs", "kendo", "window", "Widget", "ui", "proxy", "extend", "placeholderSupported", "support", "placeholder", "browser", "isFunction", "trimSlashesRegExp", "CHANGE", "APPLY", "ERROR", "CLICK", "BREADCRUBMSNS", "SEARCHBOXNS", "NAMEFIELD", "SIZEFIELD", "TYPEFIELD", "DEFAULTSORTORDER", "dir", "EMPTYTILE", "template", "TOOLBARTMPL", "data", "schemas", "filebrowser", "items", "model", "id", "size", "type", "transports", "RemoteTransport", "init", "options", "fn", "call", "this", "_call", "read", "create", "destroy", "update", "msie", "version", "height", "that", "addClass", "_deleteClick", "_addClick", "_directoryKeyDown", "_directoryBlur", "_dataSource", "refresh", "messages", "uploadFile", "orderBy", "orderByName", "orderBySize", "directoryNotFound", "emptyFolder", "deleteFile", "invalidFileType", "overwriteFile", "dropFilesHere", "search", "transport", "fileTypes", "events", "dataSource", "unbind", "_error<PERSON><PERSON><PERSON>", "add", "list", "toolbar", "off", "selected", "_selectedItem", "fileUrl", "get", "replace", "format", "encodeURIComponent", "listView", "select", "length", "getByUid", "attr", "_toolbar", "arrangeBy", "text", "showUpload", "uploadUrl", "showCreate", "showDelete", "appendTo", "find", "kendoUpload", "multiple", "localization", "async", "saveUrl", "autoUpload", "upload", "_fileUpload", "error", "e", "_error", "xhr", "XMLHttpRequest", "status", "end", "kendoDropDownList", "dataTextField", "dataValueField", "change", "_attachDropzoneEvents", "document", "documentElement", "_dropEnter", "_dropLeave", "_<PERSON><PERSON><PERSON><PERSON>", "_positionDropzone", "_removeDropzone", "offset", "css", "width", "clientWidth", "clientHeight", "lineHeight", "removeClass", "top", "left", "item", "message", "_showMessage", "remove", "createDirectory", "_getField<PERSON>ame", "reader", "file", "filterRegExp", "RegExp", "split", "join", "fileName", "files", "fileSize", "fileNameField", "sizeField", "test", "_createFile", "one", "_insertFileToList", "_override", "set", "response", "pushUpdate", "_tiles", "filter", "preventDefault", "_findFile", "idx", "result", "typeField", "nameField", "toLowerCase", "index", "view", "i", "insert", "lastDirectoryIdx", "_nameDirectory", "uid", "edit", "scrollTop", "offsetHeight", "setTimeout", "_nameExists", "keyCode", "currentTarget", "blur", "save", "candidate", "directoryNames", "indexOf", "push", "inArray", "sort", "operator", "_content", "_dblClick", "ListView", "_itemTmpl", "editTemplate", "_editTmpl", "selectable", "autoBind", "dataBinding", "parent", "action", "progress", "dataBound", "wrapper", "append", "_listViewChange", "folder", "li", "hasClass", "breadcrumbs", "trigger", "schema", "typeSortOrder", "nameSortOrder", "DataSource", "bind", "_navigation", "navigation", "kendoBreadcrumbs", "searchBox", "kendoSearchBox", "label", "statusText", "has<PERSON><PERSON><PERSON>", "cancelChanges", "html", "_path", "_wrapper", "_keydown", "_updateValue", "_click", "_focus", "_blur", "val", "_toggle<PERSON><PERSON>l", "toggle", "hide", "parents", "style", "wrap", "insertBefore", "_rootClick", "gap", "_update", "char<PERSON>t", "target", "prevAll", "addBack", "overlay", "is", "show", "focus", "segments", "segment", "empty", "_adjustSectionWidth", "a", "links", "each", "prev", "_value", "trail", "map", "b", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBACH,iBACA,qBACA,gBACDD,IACL,WA2wBE,MA7vBC,UAAUE,EAAGC,GAwDV,QAASC,GAAsBC,EAASC,EAAaC,GACjD,GAAIC,GAAcC,CAClBJ,GAAQK,GAAG,YAAcC,EAAI,WACzBL,IACAG,EAAW,GAAIG,MACVJ,IACDA,EAAeK,YAAY,WACvB,GAAIC,GAAgB,GAAIF,MAASH,CAC7BK,GAAgB,MAChBP,IACAQ,cAAcP,GACdA,EAAe,OAEpB,QAERE,GAAG,WAAaC,EAAI,WACnBF,EAAW,GAAIG,QAavB,QAASI,GAAYC,EAAMC,GAIvB,MAHID,KAASd,GAAcc,EAAKE,MAAM,SAClCF,GAAQA,GAAQ,IAAM,KAEnBA,EAAOC,EAElB,QAASE,GAAcC,GACnB,IAAKA,EACD,MAAO,EAEX,IAAIC,GAAS,QAWb,OAVID,IAAS,YACTC,EAAS,MACTD,GAAS,YACFA,GAAS,SAChBC,EAAS,MACTD,GAAS,SACFA,GAAS,OAChBC,EAAS,MACTD,GAAS,MAENE,KAAKC,MAAc,IAARH,GAAe,IAAMC,EAE3C,QAASG,GAAUC,EAAQR,GACvB,GAAIS,GAAaD,EAAOR,EACxB,OAAIU,GAAcD,GACPA,EAAWE,MAAQF,EAAWG,OAASZ,EAE3CS,EAjHd,GA2EOI,GAwCAC,EA+bAC,EA+EAC,EAhoBAC,EAAQC,OAAOD,MAAOE,EAASF,EAAMG,GAAGD,OAAQT,EAAgB1B,EAAE0B,cAAeW,EAAQrC,EAAEqC,MAAOC,EAAStC,EAAEsC,OAAQC,EAAuBN,EAAMO,QAAQC,YAAaC,EAAUT,EAAMO,QAAQE,QAASC,EAAaV,EAAMU,WAAYC,EAAoB,aAAcC,EAAS,SAAUC,EAAQ,QAASC,EAAQ,QAASC,EAAQ,QAASvC,EAAK,oBAAqBwC,EAAgB,oBAAqBC,EAAc,kBAAmBC,EAAY,OAAQC,EAAY,OAAQC,EAAY,OAAQC,GACve1B,MAAOyB,EACPE,IAAK,OACNC,EAAYvB,EAAMwB,SAAS,0DAA2DC,EAAc,oqBAC3GpB,IAAO,EAAML,EAAM0B,MACfC,SACIC,aACIF,KAAM,SAAUA,GACZ,MAAOA,GAAKG,OAASH,OAEzBI,OACIC,GAAI,OACJxC,QACIR,KAAM,OACNiD,KAAM,OACNC,KAAM,aAM1B5B,GAAO,EAAML,EAAM0B,MACfQ,YACIN,YAAe5B,EAAM0B,KAAKS,gBAAgB9B,QACtC+B,KAAM,SAAUC,GACZrC,EAAM0B,KAAKS,gBAAgBG,GAAGF,KAAKG,KAAKC,KAAMzE,EAAEsC,QAAO,KAAUmC,KAAKH,QAASA,KAEnFI,MAAO,SAAUR,EAAMI,GACnBA,EAAQX,KAAO3D,EAAEsC,UAAWgC,EAAQX,MAAQ5C,KAAM0D,KAAKH,QAAQvD,SAC3D4B,EAAW8B,KAAKH,QAAQJ,IACxBO,KAAKH,QAAQJ,GAAMM,KAAKC,KAAMH,GAE9BrC,EAAM0B,KAAKS,gBAAgBG,GAAGL,GAAMM,KAAKC,KAAMH,IAGvDK,KAAM,SAAUL,GACZG,KAAKC,MAAM,OAAQJ,IAEvBM,OAAQ,SAAUN,GACdG,KAAKC,MAAM,SAAUJ,IAEzBO,QAAS,SAAUP,GACfG,KAAKC,MAAM,UAAWJ,IAE1BQ,OAAQ,aAERR,SACIK,MAAQT,KAAM,QACdY,QAAUZ,KAAM,QAChBU,QAAUV,KAAM,QAChBW,SAAWX,KAAM,cA0B7BrC,EADAa,EAAQqC,MAAQrC,EAAQsC,QAAU,EACtB,SAAU7E,GAClB,MAAOA,GAAQ0B,WAGP,SAAU1B,GAClB,MAAOA,GAAQ0B,UAAY7B,EAAEG,GAAS8E,UAiC1CnD,EAAcK,EAAOG,QACrB+B,KAAM,SAAUlE,EAASmE,GACrB,GAAIY,GAAOT,IACXH,GAAUA,MACVnC,EAAOoC,GAAGF,KAAKG,KAAKU,EAAM/E,EAASmE,GACnCY,EAAK/E,QAAQgF,SAAS,iBACtBD,EAAK/E,QAAQK,GAAGwC,EAAQvC,EAAI,uEAAwE4B,EAAM6C,EAAKE,aAAcF,IAAO1E,GAAGwC,EAAQvC,EAAI,4EAA6E4B,EAAM6C,EAAKG,UAAWH,IAAO1E,GAAG,UAAYC,EAAI,4BAA6B4B,EAAM6C,EAAKI,kBAAmBJ,IAAO1E,GAAG,OAASC,EAAI,4BAA6B4B,EAAM6C,EAAKK,eAAgBL,IAC1ZA,EAAKM,cACLN,EAAKO,UACLP,EAAKnE,KAAKmE,EAAKZ,QAAQvD,OAE3BuD,SACItD,KAAM,cACN0E,UACIC,WAAY,SACZC,QAAS,aACTC,YAAa,OACbC,YAAa,OACbC,kBAAmB,4CACnBC,YAAa,eACbC,WAAY,yCACZC,gBAAiB,sEACjBC,cAAe,+FACfC,cAAe,2BACfC,OAAQ,UAEZC,aACAvF,KAAM,IACNwF,UAAW,OAEfC,QACIzD,EACAF,EACAC,GAEJ+B,QAAS,WACL,GAAIK,GAAOT,IACXtC,GAAOoC,GAAGM,QAAQL,KAAKU,GACvBA,EAAKuB,WAAWC,OAAO3D,EAAOmC,EAAKyB,eACnCzB,EAAK/E,QAAQyG,IAAI1B,EAAK2B,MAAMD,IAAI1B,EAAK4B,SAASC,IAAItG,GAClDwB,EAAM4C,QAAQK,EAAK/E,UAEvBgB,MAAO,WACH,GAAkDJ,GAA9CmE,EAAOT,KAAMuC,EAAW9B,EAAK+B,gBAAuBC,EAAUhC,EAAKZ,QAAQgC,UAAUY,OACzF,IAAIF,GAAwC,MAA5BA,EAASG,IAAI9D,GAKzB,MAJAtC,GAAOD,EAAYoE,EAAKnE,OAAQiG,EAASG,IAAIhE,IAAYiE,QAAQxE,EAAmB,IAChFsE,IACAnG,EAAO4B,EAAWuE,GAAWA,EAAQnG,GAAQkB,EAAMoF,OAAOH,EAASI,mBAAmBvG,KAEnFA,GAGfkG,cAAe,WACX,GAAIM,GAAW9C,KAAK8C,SAAUP,EAAWO,EAASC,QAClD,IAAIR,EAASS,OACT,MAAOhD,MAAKgC,WAAWiB,SAASV,EAASW,KAAK1F,EAAM0F,KAAK,UAGjEC,SAAU,WACN,GAAI1C,GAAOT,KAAMhB,EAAWxB,EAAMwB,SAASC,GAAcgC,EAAWR,EAAKZ,QAAQoB,SAAUmC,IAE/EC,KAAMpC,EAASG,YACf1E,MAAO,SAGP2G,KAAMpC,EAASI,YACf3E,MAAO,QAGnB+D,GAAK4B,QAAU9G,EAAEyD,GACbiC,SAAUA,EACVqC,WAAY7C,EAAKZ,QAAQgC,UAAU0B,UACnCC,WAAY/C,EAAKZ,QAAQgC,UAAU1B,OACnCsD,WAAYhD,EAAKZ,QAAQgC,UAAUzB,WACnCsD,SAASjD,EAAK/E,SAASiI,KAAK,mBAAmBC,aAC/CC,UAAU,EACVC,cAAgBnC,cAAeV,EAASU,eACxCoC,OACIC,QAASvD,EAAKZ,QAAQgC,UAAU0B,UAChCU,YAAY,GAEhBC,OAAQtG,EAAM6C,EAAK0D,YAAa1D,GAChC2D,MAAO,SAAUC,GACb5D,EAAK6D,QACDC,IAAKF,EAAEG,eACPC,OAAQ,aAGjBC,MACHjE,EAAKyD,OAASzD,EAAK4B,QAAQsB,KAAK,mBAAmBzE,KAAK,eACxDuB,EAAK2C,UAAY3C,EAAK4B,QAAQsB,KAAK,2BAA2BgB,mBAC1D3C,WAAYoB,EACZwB,cAAe,OACfC,eAAgB,QAChBC,OAAQ,WACJrE,EAAKU,QAAQnB,KAAKtD,YAEvBwC,KAAK,qBACRuB,EAAKsE,yBAETA,sBAAuB,WACnB,GAAItE,GAAOT,IACPS,GAAKZ,QAAQgC,UAAU0B,YACvB9H,EAAsBF,EAAEyJ,SAASC,iBAAkB1J,EAAEqC,MAAM6C,EAAKyE,WAAYzE,GAAOlF,EAAEqC,MAAM6C,EAAK0E,WAAY1E,IAC5GA,EAAK2E,eAAiBxH,EAAM6C,EAAK4E,kBAAmB5E,KAG5DyE,WAAY,WACRlF,KAAKqF,oBACL9J,EAAEyJ,UAAUjJ,GAAG,SAAWC,EAAIgE,KAAKoF,iBAEvCD,WAAY,WACRnF,KAAKsF,kBACL/J,EAAEyJ,UAAU1C,IAAI,SAAWtG,EAAIgE,KAAKoF,iBAExCC,kBAAmB,WACf,GAAI5E,GAAOT,KAAMtE,EAAU+E,EAAK/E,QAAS6J,EAAS7J,EAAQ6J,QAC1D9E,GAAK4B,QAAQsB,KAAK,eAAejD,SAAS,0BAA0B6E,OAAOA,GAAQC,KAC/EC,MAAO/J,EAAQ,GAAGgK,YAClBlF,OAAQ9E,EAAQ,GAAGiK,aACnBC,WAAYlK,EAAQ,GAAGiK,aAAe,QAG9CL,gBAAiB,WACbtF,KAAKqC,QAAQsB,KAAK,eAAekC,YAAY,0BAA0BL,KACnEC,MAAO,GACPjF,OAAQ,GACRoF,WAAY,GACZE,IAAK,GACLC,KAAM,MAGdpF,aAAc,WACV,GAAIF,GAAOT,KAAMgG,EAAOvF,EAAKqC,SAASC,SAAUkD,EAAUzI,EAAMoF,OAAOnC,EAAKZ,QAAQoB,SAASO,WAAYwE,EAAKrC,KAAK,UAAUN,OACzH2C,GAAKhD,QAAUvC,EAAKyF,aAAaD,EAAS,YAC1CxF,EAAKqC,SAASqD,OAAOH,IAG7BpF,UAAW,WACPZ,KAAKoG,mBAETC,cAAe,SAAU9J,GACrB,MAAOO,GAAUkD,KAAKgC,WAAWsE,OAAOhH,MAAMvC,OAAQR,IAE1D4H,YAAa,SAAUE,GACnB,GAA0RkC,GAAtR9F,EAAOT,KAAMH,EAAUY,EAAKZ,QAASiC,EAAYjC,EAAQiC,UAAW0E,EAAmBC,QAAQ,IAAM3E,EAAU4E,MAAM,KAAKC,KAAK,OAAS,KAAKhE,QAAQ,QAAS,OAAQ,KAAMiE,EAAWvC,EAAEwC,MAAM,GAAGtK,KAAMuK,EAAWzC,EAAEwC,MAAM,GAAGrH,KAAMuH,EAAgBrI,EAAWsI,EAAYrI,CAC3Q6H,GAAaS,KAAKL,IAClBvC,EAAEnF,MAAS5C,KAAMmE,EAAKnE,QACtBiK,EAAO9F,EAAKyG,YAAYN,EAAUE,GAC7BP,EAGD9F,EAAKyD,OAAOiD,IAAI,UAAW,SAAU9C,GACjC,GAAI/E,GAAQmB,EAAK2G,kBAAkBb,EAC/BjH,GAAM+H,YACN/H,EAAMgI,IAAIP,EAAe1C,EAAEkD,SAAS9G,EAAK4F,cAAcU,KACvDzH,EAAMgI,IAAIN,EAAW3C,EAAEkD,SAAS9G,EAAK4F,cAAcW,KACnDvG,EAAKqC,SAASd,WAAWwF,WAAWlI,IAExCmB,EAAKgH,OAAShH,EAAKqC,SAASzD,QAAQqI,OAAO,IAAMlK,EAAM0F,KAAK,QAAU,SAT1EmB,EAAEsD,mBAaNtD,EAAEsD,iBACFlH,EAAKyF,aAAa1I,EAAMoF,OAAO/C,EAAQoB,SAASQ,gBAAiBmF,EAAU9E,MAGnF8F,UAAW,SAAUrL,GACjB,GAAmCsL,GAAKC,EAAsD9E,EAA1F9D,EAAOc,KAAKgC,WAAW9C,OAAqB6I,EAAYnJ,EAAWoJ,EAAYtJ,CAEnF,KADAnC,EAAOA,EAAK0L,cACPJ,EAAM,EAAG7E,EAAS9D,EAAK8D,OAAQ6E,EAAM7E,EAAQ6E,IAC9C,GAAiC,MAA7B3I,EAAK2I,GAAKnF,IAAIqF,IAAsB7I,EAAK2I,GAAKnF,IAAIsF,GAAWC,gBAAkB1L,EAAM,CACrFuL,EAAS5I,EAAK2I,EACd,OAGR,MAAOC,IAEXZ,YAAa,SAAUN,EAAUE,GAC7B,GAAIrG,GAAOT,KAAMV,KAAYyI,EAAYnJ,EAAW2H,EAAO9F,EAAKmH,UAAUhB,EAC1E,OAAIL,GACK9F,EAAKyF,aAAa1I,EAAMoF,OAAOnC,EAAKZ,QAAQoB,SAASS,cAAekF,GAAW,YAGhFL,EAAKc,WAAY,EACVd,GAHA,MAMfjH,EAAMyI,GAAa,IACnBzI,EAAMZ,GAAakI,EACnBtH,EAAMX,GAAamI,EACZxH,IAEX8H,kBAAmB,SAAU9H,GAAV,GACX4I,GAIAlG,EACAmG,EACKC,EAAOpF,CALhB,IAAI1D,EAAM+H,UACN,MAAO/H,EAIX,KAFI0C,EAAahC,KAAKgC,WAClBmG,EAAOnG,EAAWmG,OACbC,EAAI,EAAGpF,EAASmF,EAAKnF,OAAQoF,EAAIpF,EAAQoF,IAC9C,GAA+B,MAA3BD,EAAKC,GAAG1F,IAAI9D,GAAoB,CAChCsJ,EAAQE,CACR,OAGR,MAAOpG,GAAWqG,SAASH,EAAO5I,IAEtC8G,gBAAiB,WACb,GAAiByB,GAAK7E,EAAlBvC,EAAOT,KAAmBsI,EAAmB,EAAGP,EAAYnJ,EAAWoJ,EAAYtJ,EAAWyJ,EAAO1H,EAAKuB,WAAW9C,OAAQ3C,EAAOkE,EAAK8H,iBAAkBjJ,EAAQ,GAAImB,GAAKuB,WAAWsE,OAAOhH,KAClM,KAAKuI,EAAM,EAAG7E,EAASmF,EAAKnF,OAAQ6E,EAAM7E,EAAQ6E,IACb,MAA7BM,EAAKN,GAAKnF,IAAIqF,KACdO,EAAmBT,EAG3BvI,GAAMgI,IAAIS,EAAW,KACrBzI,EAAMgI,IAAIU,EAAWzL,GACrBkE,EAAKqC,SAASqE,IAAI,YAAa,WAC3B,GAAI5E,GAAW9B,EAAKqC,SAASzD,QAAQqI,OAAO,IAAMlK,EAAM0F,KAAK,OAAS,IAAM5D,EAAMkJ,IAAM,IACpFjG,GAASS,QACThD,KAAKyI,KAAKlG,GAEdvC,KAAKtE,QAAQgN,UAAUnG,EAASW,KAAK,aAAelD,KAAKtE,QAAQ,GAAGiN,cACpEC,WAAW,WACPnI,EAAKqC,SAASpH,QAAQiI,KAAK,sBAAsBZ,aAEtDoE,IAAI,OAAQ,SAAU9C,GACrB,GAAI3H,GAAQ2H,EAAE/E,MAAMoD,IAAIsF,EACnBtL,GAGD2H,EAAE/E,MAAMgI,IAAIU,EAAWvH,EAAKoI,YAAYnM,EAAO4C,EAAMkJ,KAAO/H,EAAK8H,iBAAmB7L,GAFpF2H,EAAE/E,MAAMgI,IAAIU,EAAWzL,KAK/BkE,EAAKuB,WAAWqG,SAASC,EAAkBhJ,IAE/CuB,kBAAmB,SAAUwD,GACR,IAAbA,EAAEyE,SACFzE,EAAE0E,cAAcC,QAGxBlI,eAAgB,WACZd,KAAK8C,SAASmG,QAElBJ,YAAa,SAAUtM,EAAMiM,GACzB,GAAiFX,GAAK7E,EAAlF9D,EAAOc,KAAKgC,WAAW9C,OAAQ6I,EAAYnJ,EAAWoJ,EAAYtJ,CACtE,KAAKmJ,EAAM,EAAG7E,EAAS9D,EAAK8D,OAAQ6E,EAAM7E,EAAQ6E,IAC9C,GAAiC,MAA7B3I,EAAK2I,GAAKnF,IAAIqF,IAAsB7I,EAAK2I,GAAKnF,IAAIsF,GAAWC,gBAAkB1L,EAAK0L,eAAiB/I,EAAK2I,GAAKW,MAAQA,EACvH,OAAO,CAGf,QAAO,GAEXD,eAAgB,WACZ,GAA2HW,GAAWrB,EAAK7E,EAAvIzG,EAAO,aAAc2C,EAAOc,KAAKgC,WAAW9C,OAAQiK,KAAqBpB,EAAYnJ,EAAWoJ,EAAYtJ,CAChH,KAAKmJ,EAAM,EAAG7E,EAAS9D,EAAK8D,OAAQ6E,EAAM7E,EAAQ6E,IACb,MAA7B3I,EAAK2I,GAAKnF,IAAIqF,IAAsB7I,EAAK2I,GAAKnF,IAAIsF,GAAWC,cAAcmB,QAAQ7M,EAAK0L,mBACxFkB,EAAeE,KAAKnK,EAAK2I,GAAKnF,IAAIsF,GAG1C,IAAIzM,EAAE+N,QAAQ/M,EAAM4M,MAAsB,CACtCtB,EAAM,CACN,GACIqB,GAAY3M,EAAO,KAAOsL,EAAM,IAChCA,UACKtM,EAAE+N,QAAQJ,EAAWC,MAC9B5M,GAAO2M,EAEX,MAAO3M,IAEX4E,QAAS,SAAUhE,GACf6C,KAAKgC,WAAWuH,OAERpM,MAAOyB,EACPE,IAAK,QAGL3B,MAAOA,EACP2B,IAAK,UAIjB8C,OAAQ,SAAUrF,GACdyD,KAAKgC,WAAW0F,QACZvK,MAAOuB,EACP8K,SAAU,WACV9M,MAAOH,KAGfkN,SAAU,WACN,GAAIhJ,GAAOT,IACXS,GAAK2B,KAAO7G,EAAE,2CAA2CmI,SAASjD,EAAK/E,SAASK,GAAG,WAAaC,EAAI,KAAM4B,EAAM6C,EAAKiJ,UAAWjJ,IAChIA,EAAKqC,SAAW,GAAItF,GAAMG,GAAGgM,SAASlJ,EAAK2B,MACvCJ,WAAYvB,EAAKuB,WACjBhD,SAAUyB,EAAKmJ,YACfC,aAAcpJ,EAAKqJ,YACnBC,YAAY,EACZC,UAAU,EACVC,YAAa,SAAU5F,GACnB5D,EAAK4B,QAAQsB,KAAK,cAAcuG,SAASxJ,SAAS,oBACjC,WAAb2D,EAAE8F,QAAoC,SAAb9F,EAAE8F,SAC3B9F,EAAEsD,iBACFnK,EAAMG,GAAGyM,SAAS3J,EAAKqC,SAASpH,SAAS,KAGjD2O,UAAW,WACH5J,EAAKuB,WAAWmG,OAAOnF,OACvBvC,EAAKgH,OAASzH,KAAKX,QAAQqI,OAAO,IAAMlK,EAAM0F,KAAK,QAAU,OAE7DlD,KAAKsK,QAAQC,OAAOxL,GAAYsE,KAAM5C,EAAKZ,QAAQoB,SAASM,gBAGpEuD,OAAQlH,EAAM6C,EAAK+J,gBAAiB/J,MAG5CiJ,UAAW,SAAUrF,GAAV,GAMCoG,GALJhK,EAAOT,KAAM0K,EAAKnP,EAAE8I,EAAE0E,cACtB2B,GAAGC,SAAS,gBACZlK,EAAKK,iBAEL4J,EAAGhD,OAAO,IAAMlK,EAAM0F,KAAK,QAAU,OAAOF,QACxCyH,EAAShK,EAAKuB,WAAWiB,SAASyH,EAAGxH,KAAK1F,EAAM0F,KAAK,SACrDuH,IACAhK,EAAKnE,KAAKD,EAAYoE,EAAKnE,OAAQmO,EAAO/H,IAAIhE,KAC9C+B,EAAKmK,YAAYlO,MAAM+D,EAAKnE,UAEzBoO,EAAGhD,OAAO,IAAMlK,EAAM0F,KAAK,QAAU,OAAOF,QACnDvC,EAAKoK,QAAQxM,IAGrBmM,gBAAiB,WACb,GAAIjI,GAAWvC,KAAKwC,eAChBD,KACAvC,KAAKqC,QAAQsB,KAAK,cAAcuG,SAASrE,YAAY,oBACrD7F,KAAK6K,QAAQzM,GAAUmE,SAAUA,MAGzCxB,YAAa,WACT,GAGO+J,GAHHrK,EAAOT,KAAMH,EAAUY,EAAKZ,QAASgC,EAAYhC,EAAQgC,UAAWkJ,EAAgBlN,KAAWgB,GAAmBmM,GAC9G7N,MAAOuB,EACPI,IAAK,OACEkD,GACPvC,KAAMoC,EAAUpC,MAAQ,cACxB8J,MACIwB,EACAC,GAGR/N,GAAc4E,KACdA,EAAUvF,KAAOsB,EAAM6C,EAAKnE,KAAMmE,GAClCuB,EAAWH,UAAYA,GAEvB5E,EAAc4C,EAAQiL,QACtB9I,EAAW8I,OAASjL,EAAQiL,OACrBjJ,EAAUpC,MAAQxC,EAAcO,EAAM0B,KAAKC,QAAQ0C,EAAUpC,SACpEqL,EAAStN,EAAM0B,KAAKC,QAAQ0C,EAAUpC,OAEtCgB,EAAKuB,YAAcvB,EAAKyB,cACxBzB,EAAKuB,WAAWC,OAAO3D,EAAOmC,EAAKyB,eAEnCzB,EAAKyB,cAAgBtE,EAAM6C,EAAK6D,OAAQ7D,GAE5CA,EAAKuB,WAAaxE,EAAM0B,KAAK+L,WAAW9K,OAAO6B,GAAYkJ,KAAK5M,EAAOmC,EAAKyB,gBAEhFiJ,YAAa,WACT,GAAI1K,GAAOT,KAAMoL,EAAa7P,EAAE,mDAAmDmI,SAAS1D,KAAKtE,QACjG+E,GAAKmK,YAAcQ,EAAWzH,KAAK,eAAe0H,kBAC9C3O,MAAO+D,EAAKZ,QAAQvD,KACpBwI,OAAQ,WACJrE,EAAKnE,KAAK0D,KAAKtD,YAEpBwC,KAAK,oBACRuB,EAAK6K,UAAYF,EAAWlB,SAASvG,KAAK,cAAc4H,gBACpDC,MAAO/K,EAAKZ,QAAQoB,SAASW,OAC7BkD,OAAQ,WACJrE,EAAKmB,OAAO5B,KAAKtD,YAEtBwC,KAAK,mBAEZoF,OAAQ,SAAUD,GAAV,GACaI,GAYTzC,EAZJvB,EAAOT,IACNS,GAAKoK,QAAQvM,EAAO+F,KACrBI,EAASJ,EAAEE,IAAIE,OACC,SAAZJ,EAAEI,OACY,OAAVA,EACAhE,EAAKyF,aAAazF,EAAKZ,QAAQoB,SAASK,mBACvB,KAAVmD,GACPhE,EAAKyF,aAAa,qCAAuCzB,EAAS,MAAQJ,EAAEE,IAAIkH,YAEnE,WAAVhH,GACPhE,EAAKyF,aAAa,0BAElBlE,EAAavB,EAAKuB,WAClBA,EAAW0J,cACX1J,EAAW2J,kBAIvBzF,aAAc,SAAUD,EAASxG,GAC7B,MAAOhC,QAAOgC,GAAQ,SAASwG,IAEnCjF,QAAS,WACL,GAAIP,GAAOT,IACXS,GAAK0K,cACL1K,EAAK0C,WACL1C,EAAKgJ,YAETK,UAAW,WACP,GAAI8B,GAAO,uCAAyCpO,EAAM0F,KAAK,OAAS,YAWxE,OAVA0I,IAAQpO,EAAM0F,KAAK,QAAU,OAAStE,EAAY,MAClDgN,GAAQ,OAAShN,EAAY,eAC7BgN,GAAQ,qEACRA,GAAQ,WACRA,GAAQ,sEACRA,GAAQ,MACRA,GAAQ,OAAShN,EAAY,eAC7BgN,GAAQ,0BAA4BpO,EAAM0F,KAAK,QAAU,WAAaxE,EAAY,MAClFkN,GAAQ,MACRA,GAAQ,QACDhO,EAAMJ,EAAMwB,SAAS4M,IAASnP,cAAeA,KAExDmN,UAAW,WACP,GAAIgC,GAAO,sBAAwBpO,EAAM0F,KAAK,OAAS,YAUvD,OATA0I,IAAQpO,EAAM0F,KAAK,QAAU,OAAStE,EAAY,MAClDgN,GAAQ,OAAShN,EAAY,eAC7BgN,GAAQ,qEACRA,GAAQ,WACRA,GAAQ,mEACRA,GAAQ,MACRA,GAAQ,aAAelN,EAAY,aACnCkN,GAAQ,OAAShN,EAAY,8DAAgED,EAAY,gBACzGiN,GAAQ,QACDhO,EAAMJ,EAAMwB,SAAS4M,IAASnP,cAAeA,KAExDH,KAAM,SAAUI,GACZ,GAAI+D,GAAOT,KAAM1D,EAAOmE,EAAKoL,OAAS,EACtC,OAAInP,KAAUlB,GACViF,EAAKoL,MAAQnP,EAAMiG,QAAQxE,EAAmB,IAAM,IACpDsC,EAAKuB,WAAW9B,MAAO5D,KAAMmE,EAAKoL,QAClC,IAEAvP,IACAA,EAAOA,EAAKqG,QAAQxE,EAAmB,KAE3B,MAAT7B,GAAyB,KAATA,EAAc,GAAKA,EAAO,QAGrDgB,EAAYI,EAAOG,QACnB+B,KAAM,SAAUlE,EAASmE,GACrB,GAAIY,GAAOT,IACXH,GAAUA,MACVnC,EAAOoC,GAAGF,KAAKG,KAAKU,EAAM/E,EAASmE,GAC/B/B,GACA2C,EAAK/E,QAAQwH,KAAK,cAAezC,EAAKZ,QAAQ2L,OAElD/K,EAAKqL,WACLrL,EAAK/E,QAAQK,GAAG,UAAY0C,EAAab,EAAM6C,EAAKsL,SAAUtL,IAAO1E,GAAG,SAAW0C,EAAab,EAAM6C,EAAKuL,aAAcvL,IACzHA,EAAK6J,QAAQvO,GAAGwC,EAAQE,EAAa,IAAKb,EAAM6C,EAAKwL,OAAQxL,IACxD3C,GACD2C,EAAK/E,QAAQK,GAAG,QAAU0C,EAAab,EAAM6C,EAAKyL,OAAQzL,IAAO1E,GAAG,OAAS0C,EAAab,EAAM6C,EAAK0L,MAAO1L,KAGpHZ,SACItD,KAAM,YACNiP,MAAO,SACP9O,MAAO,IAEXqF,QAAS3D,GACTgC,QAAS,WACL,GAAIK,GAAOT,IACXS,GAAK6J,QAAQnI,IAAI1B,EAAK/E,SAASyG,IAAI1B,EAAK+K,OAAOlJ,IAAI7D,GACnDf,EAAOoC,GAAGM,QAAQL,KAAKU,IAE3BsL,SAAU,SAAU1H,GACE,KAAdA,EAAEyE,SACF9I,KAAKgM,gBAGbC,OAAQ,SAAU5H,GACdA,EAAEsD,iBACF3H,KAAKgM,gBAETA,aAAc,WACV,GAAIvL,GAAOT,KAAMtD,EAAQ+D,EAAK/E,QAAQ0Q,KAClC1P,KAAU+D,EAAK/D,UACf+D,EAAK/D,MAAMA,GACX+D,EAAKoK,QAAQzM,KAGrB+N,MAAO,WACHnM,KAAKgM,eACLhM,KAAKqM,gBAETA,aAAc,WACLvO,GACDkC,KAAKwL,MAAMc,QAAQtM,KAAKtE,QAAQ0Q,QAGxCF,OAAQ,WACJlM,KAAKwL,MAAMe,QAEfT,SAAU,WACN,GAAIpQ,GAAUsE,KAAKtE,QAAS4O,EAAU5O,EAAQ8Q,QAAQ,iBACtD9Q,GAAQ,GAAG+Q,MAAMhH,MAAQ,GACzB/J,EAAQgF,SAAS,WACZ4J,EAAQtH,SACTsH,EAAU5O,EAAQgR,KAAKnR,EAAE,oDAAoD2O,SACxEpM,GACDvC,EAAE,gCAAkCyE,KAAKH,QAAQ2L,MAAQ,YAAYmB,aAAajR,GAEtFH,EAAE,kDAAkDmI,SAAS4G,IAEjEtK,KAAKsK,QAAUA,EACftK,KAAKwL,MAAQlB,EAAQ3G,KAAK,WAE9BjH,MAAO,SAAUA,GACb,GAAI+D,GAAOT,IACX,OAAItD,KAAUlB,GACViF,EAAKZ,QAAQnD,MAAQA,EACrB+D,EAAK/E,QAAQ0Q,IAAI1P,GACjB+D,EAAK4L,eACL,GAEG5L,EAAKZ,QAAQnD,SAGxBa,EAAcG,EAAOG,QACrB+B,KAAM,SAAUlE,EAASmE,GACrB,GAAIY,GAAOT,IACXH,GAAUA,MACVnC,EAAOoC,GAAGF,KAAKG,KAAKU,EAAM/E,EAASmE,GACnCY,EAAKqL,WACLrL,EAAK6J,QAAQvO,GAAG,QAAUyC,EAAe,QAASZ,EAAM6C,EAAKyL,OAAQzL,IAAO1E,GAAG,OAASyC,EAAe,QAASZ,EAAM6C,EAAK0L,MAAO1L,IAAO1E,GAAG,UAAYyC,EAAe,QAASZ,EAAM6C,EAAKsL,SAAUtL,IAAO1E,GAAGwC,EAAQC,EAAe,0BAA2BZ,EAAM6C,EAAKmM,WAAYnM,IAAO1E,GAAGwC,EAAQC,EAAe,0BAA2BZ,EAAM6C,EAAKwL,OAAQxL,IACvWA,EAAK/D,MAAM+D,EAAKZ,QAAQnD,QAE5BmD,SACItD,KAAM,cACNsQ,IAAK,IAET9K,QAAS3D,GACTgC,QAAS,WACL,GAAIK,GAAOT,IACXtC,GAAOoC,GAAGM,QAAQL,KAAKU,GACvBA,EAAK6J,QAAQnI,IAAI1B,EAAK6J,QAAQ3G,KAAK,UAAUxB,IAAI1B,EAAK6J,QAAQ3G,KAAK,MAAMrB,IAAI9D,IAEjFsO,QAAS,SAAUV,GACfA,EAAgC,OAAzBA,GAAO,IAAIW,OAAO,GAAaX,EAAM,KAAOA,GAAO,IACtDA,IAAQpM,KAAKtD,UACbsD,KAAKtD,MAAM0P,GACXpM,KAAK6K,QAAQzM,KAGrB6N,OAAQ,SAAU5H,GACdA,EAAEsD,iBACF3H,KAAK8M,QAAQ9M,KAAK6L,MAAMtQ,EAAE8I,EAAE2I,QAAQC,QAAQ,2BAA2BC,aAE3EN,WAAY,SAAUvI,GAClBA,EAAEsD,iBACF3H,KAAK8M,QAAQ,KAEjBZ,OAAQ,WACJ,GAAIzL,GAAOT,KAAMtE,EAAU+E,EAAK/E,OAChC+E,GAAK0M,QAAQZ,OACb9L,EAAK/E,QAAQ0Q,IAAI3L,EAAK/D,SACtBkM,WAAW,WACPlN,EAAQqH,YAGhBoJ,MAAO,WACH,IAAInM,KAAKmN,QAAQC,GAAG,YAApB,CAGA,GAAI3M,GAAOT,KAAMtE,EAAU+E,EAAK/E,QAAS0Q,EAAM1Q,EAAQ0Q,MAAMzJ,QAAQ,UAAW,IAChFlC,GAAK0M,QAAQE,OACb3R,EAAQ0Q,IAAI,IACZ3L,EAAKqM,QAAQV,KAEjBL,SAAU,SAAU1H,GAChB,GAAI5D,GAAOT,IACO,MAAdqE,EAAEyE,UACFrI,EAAK0L,QACLvD,WAAW,WACPnI,EAAK0M,QAAQxJ,KAAK,WAAW2J,YAIzCxB,SAAU,WACN,GAAyEqB,GAArEzR,EAAUsE,KAAKtE,QAAS4O,EAAU5O,EAAQ8Q,QAAQ,iBACtD9Q,GAAQ,GAAG+Q,MAAMhH,MAAQ,GACzB/J,EAAQgF,SAAS,WACZ4J,EAAQtH,SACTsH,EAAU5O,EAAQgR,KAAKnR,EAAE,oDAAoD2O,UAEjFiD,EAAU7C,EAAQ3G,KAAK,uBAClBwJ,EAAQnK,SACTmK,EAAU5R,EAAE,qCAAqCmI,SAAS4G,IAE9DtK,KAAKsK,QAAUA,EACftK,KAAKmN,QAAUA,GAEnBnM,QAAS,WACL,GAAqCuM,GAAUC,EAAS3F,EAAK7E,EAAzD4I,EAAO,GAAIlP,EAAQsD,KAAKtD,OAK5B,KAJIA,IAAUlB,GAAckB,EAAMF,MAAM,SACpCE,EAAQ,KAAOA,GAAS,KAE5B6Q,EAAW7Q,EAAMgK,MAAM,KAClBmB,EAAM,EAAG7E,EAASuK,EAASvK,OAAQ6E,EAAM7E,EAAQ6E,IAClD2F,EAAUD,EAAS1F,GACf2F,IACK5B,IACDA,GAAQ,+EAEZA,GAAQ,8BAAgC2B,EAAS1F,GAAO,OACxD+D,GAAQ,6EAGhB5L,MAAKmN,QAAQM,QAAQlD,OAAOhP,EAAEqQ,IAC9B5L,KAAK0N,uBAETA,oBAAqB,WACjB,GAAqHC,GAAjHlN,EAAOT,KAAMsK,EAAU7J,EAAK6J,QAAS7E,EAAQ6E,EAAQ7E,QAAUhF,EAAKZ,QAAQgN,IAAKe,EAAQnN,EAAK0M,QAAQxJ,KAAK,IAC/GiK,GAAMC,KAAK,SAAU3F,GACjByF,EAAIpS,EAAEyE,MACF2N,EAAEzD,SAASzE,QAAUA,IACjByC,GAAS0F,EAAM5K,OAAS,EACxB2K,EAAElI,MAAMA,GAERkI,EAAEG,OAAOZ,UAAUX,WAKnC7P,MAAO,SAAU0P,GACb,MAAIA,KAAQ5Q,GACRwE,KAAK+N,OAAS3B,EAAIzJ,QAAQ,UAAW,KACrC3C,KAAKgB,UACL,GAEGhB,KAAK+N,QAEhBlC,MAAO,SAAUmC,GACb,MAAO,IAAMzS,EAAE0S,IAAID,EAAO,SAAUE,GAChC,MAAO3S,GAAE2S,GAAG7K,SACbsD,KAAK,QAGhBnJ,EAAMG,GAAGwQ,OAAO9Q,GAChBG,EAAMG,GAAGwQ,OAAO5Q,GAChBC,EAAMG,GAAGwQ,OAAO7Q,IAClBG,OAAOD,MAAM4Q,QACR3Q,OAAOD,OACE,kBAAVlC,SAAwBA,OAAO+S,IAAM/S,OAAS,SAAUgT,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.filebrowser.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.filebrowser', [\n        'kendo.listview',\n        'kendo.dropdownlist',\n        'kendo.upload'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'filebrowser',\n        name: '<PERSON><PERSON>rowser',\n        category: 'web',\n        description: '',\n        hidden: true,\n        depends: [\n            'selectable',\n            'listview',\n            'dropdownlist',\n            'upload'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, isPlainObject = $.isPlainObject, proxy = $.proxy, extend = $.extend, placeholderSupported = kendo.support.placeholder, browser = kendo.support.browser, isFunction = kendo.isFunction, trimSlashesRegExp = /(^\\/|\\/$)/g, CHANGE = 'change', APPLY = 'apply', ERROR = 'error', CLICK = 'click', NS = '.kendoFileBrowser', BREADCRUBMSNS = '.kendoBreadcrumbs', SEARCHBOXNS = '.kendoSearchBox', NAMEFIELD = 'name', SIZEFIELD = 'size', TYPEFIELD = 'type', DEFAULTSORTORDER = {\n                field: TYPEFIELD,\n                dir: 'asc'\n            }, EMPTYTILE = kendo.template('<li class=\"k-tile-empty\"><strong>${text}</strong></li>'), TOOLBARTMPL = '<div class=\"k-widget k-filebrowser-toolbar k-header k-floatwrap\">' + '<div class=\"k-toolbar-wrap\">' + '# if (showUpload) { # ' + '<div class=\"k-widget k-upload\"><div class=\"k-button k-button-icontext k-upload-button\">' + '<span class=\"k-icon k-i-plus\"></span>#=messages.uploadFile#<input type=\"file\" name=\"file\" /></div></div>' + '# } #' + '# if (showCreate) { #' + '<button type=\"button\" class=\"k-button k-button-icon\"><span class=\"k-icon k-i-folder-add\" /></button>' + '# } #' + '# if (showDelete) { #' + '<button type=\"button\" class=\"k-button k-button-icon k-state-disabled\"><span class=\"k-icon k-i-close\" /></button>&nbsp;' + '# } #' + '</div>' + '<div class=\"k-tiles-arrange\">' + '<label>#=messages.orderBy#: <select /></label>' + '</div>' + '</div>';\n        extend(true, kendo.data, {\n            schemas: {\n                'filebrowser': {\n                    data: function (data) {\n                        return data.items || data || [];\n                    },\n                    model: {\n                        id: 'name',\n                        fields: {\n                            name: 'name',\n                            size: 'size',\n                            type: 'type'\n                        }\n                    }\n                }\n            }\n        });\n        extend(true, kendo.data, {\n            transports: {\n                'filebrowser': kendo.data.RemoteTransport.extend({\n                    init: function (options) {\n                        kendo.data.RemoteTransport.fn.init.call(this, $.extend(true, {}, this.options, options));\n                    },\n                    _call: function (type, options) {\n                        options.data = $.extend({}, options.data, { path: this.options.path() });\n                        if (isFunction(this.options[type])) {\n                            this.options[type].call(this, options);\n                        } else {\n                            kendo.data.RemoteTransport.fn[type].call(this, options);\n                        }\n                    },\n                    read: function (options) {\n                        this._call('read', options);\n                    },\n                    create: function (options) {\n                        this._call('create', options);\n                    },\n                    destroy: function (options) {\n                        this._call('destroy', options);\n                    },\n                    update: function () {\n                    },\n                    options: {\n                        read: { type: 'POST' },\n                        update: { type: 'POST' },\n                        create: { type: 'POST' },\n                        destroy: { type: 'POST' }\n                    }\n                })\n            }\n        });\n        function bindDragEventWrappers(element, onDragEnter, onDragLeave) {\n            var hideInterval, lastDrag;\n            element.on('dragenter' + NS, function () {\n                onDragEnter();\n                lastDrag = new Date();\n                if (!hideInterval) {\n                    hideInterval = setInterval(function () {\n                        var sinceLastDrag = new Date() - lastDrag;\n                        if (sinceLastDrag > 100) {\n                            onDragLeave();\n                            clearInterval(hideInterval);\n                            hideInterval = null;\n                        }\n                    }, 100);\n                }\n            }).on('dragover' + NS, function () {\n                lastDrag = new Date();\n            });\n        }\n        var offsetTop;\n        if (browser.msie && browser.version < 8) {\n            offsetTop = function (element) {\n                return element.offsetTop;\n            };\n        } else {\n            offsetTop = function (element) {\n                return element.offsetTop - $(element).height();\n            };\n        }\n        function concatPaths(path, name) {\n            if (path === undefined || !path.match(/\\/$/)) {\n                path = (path || '') + '/';\n            }\n            return path + name;\n        }\n        function sizeFormatter(value) {\n            if (!value) {\n                return '';\n            }\n            var suffix = ' bytes';\n            if (value >= 1073741824) {\n                suffix = ' GB';\n                value /= 1073741824;\n            } else if (value >= 1048576) {\n                suffix = ' MB';\n                value /= 1048576;\n            } else if (value >= 1024) {\n                suffix = ' KB';\n                value /= 1024;\n            }\n            return Math.round(value * 100) / 100 + suffix;\n        }\n        function fieldName(fields, name) {\n            var descriptor = fields[name];\n            if (isPlainObject(descriptor)) {\n                return descriptor.from || descriptor.field || name;\n            }\n            return descriptor;\n        }\n        var FileBrowser = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                options = options || {};\n                Widget.fn.init.call(that, element, options);\n                that.element.addClass('k-filebrowser');\n                that.element.on(CLICK + NS, '.k-filebrowser-toolbar button:not(.k-state-disabled):has(.k-i-close)', proxy(that._deleteClick, that)).on(CLICK + NS, '.k-filebrowser-toolbar button:not(.k-state-disabled):has(.k-i-folder-add)', proxy(that._addClick, that)).on('keydown' + NS, 'li.k-state-selected input', proxy(that._directoryKeyDown, that)).on('blur' + NS, 'li.k-state-selected input', proxy(that._directoryBlur, that));\n                that._dataSource();\n                that.refresh();\n                that.path(that.options.path);\n            },\n            options: {\n                name: 'FileBrowser',\n                messages: {\n                    uploadFile: 'Upload',\n                    orderBy: 'Arrange by',\n                    orderByName: 'Name',\n                    orderBySize: 'Size',\n                    directoryNotFound: 'A directory with this name was not found.',\n                    emptyFolder: 'Empty Folder',\n                    deleteFile: 'Are you sure you want to delete \"{0}\"?',\n                    invalidFileType: 'The selected file \"{0}\" is not valid. Supported file types are {1}.',\n                    overwriteFile: 'A file with name \"{0}\" already exists in the current directory. Do you want to overwrite it?',\n                    dropFilesHere: 'drop file here to upload',\n                    search: 'Search'\n                },\n                transport: {},\n                path: '/',\n                fileTypes: '*.*'\n            },\n            events: [\n                ERROR,\n                CHANGE,\n                APPLY\n            ],\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.dataSource.unbind(ERROR, that._errorHandler);\n                that.element.add(that.list).add(that.toolbar).off(NS);\n                kendo.destroy(that.element);\n            },\n            value: function () {\n                var that = this, selected = that._selectedItem(), path, fileUrl = that.options.transport.fileUrl;\n                if (selected && selected.get(TYPEFIELD) === 'f') {\n                    path = concatPaths(that.path(), selected.get(NAMEFIELD)).replace(trimSlashesRegExp, '');\n                    if (fileUrl) {\n                        path = isFunction(fileUrl) ? fileUrl(path) : kendo.format(fileUrl, encodeURIComponent(path));\n                    }\n                    return path;\n                }\n            },\n            _selectedItem: function () {\n                var listView = this.listView, selected = listView.select();\n                if (selected.length) {\n                    return this.dataSource.getByUid(selected.attr(kendo.attr('uid')));\n                }\n            },\n            _toolbar: function () {\n                var that = this, template = kendo.template(TOOLBARTMPL), messages = that.options.messages, arrangeBy = [\n                        {\n                            text: messages.orderByName,\n                            value: 'name'\n                        },\n                        {\n                            text: messages.orderBySize,\n                            value: 'size'\n                        }\n                    ];\n                that.toolbar = $(template({\n                    messages: messages,\n                    showUpload: that.options.transport.uploadUrl,\n                    showCreate: that.options.transport.create,\n                    showDelete: that.options.transport.destroy\n                })).appendTo(that.element).find('.k-upload input').kendoUpload({\n                    multiple: false,\n                    localization: { dropFilesHere: messages.dropFilesHere },\n                    async: {\n                        saveUrl: that.options.transport.uploadUrl,\n                        autoUpload: true\n                    },\n                    upload: proxy(that._fileUpload, that),\n                    error: function (e) {\n                        that._error({\n                            xhr: e.XMLHttpRequest,\n                            status: 'error'\n                        });\n                    }\n                }).end();\n                that.upload = that.toolbar.find('.k-upload input').data('kendoUpload');\n                that.arrangeBy = that.toolbar.find('.k-tiles-arrange select').kendoDropDownList({\n                    dataSource: arrangeBy,\n                    dataTextField: 'text',\n                    dataValueField: 'value',\n                    change: function () {\n                        that.orderBy(this.value());\n                    }\n                }).data('kendoDropDownList');\n                that._attachDropzoneEvents();\n            },\n            _attachDropzoneEvents: function () {\n                var that = this;\n                if (that.options.transport.uploadUrl) {\n                    bindDragEventWrappers($(document.documentElement), $.proxy(that._dropEnter, that), $.proxy(that._dropLeave, that));\n                    that._scrollHandler = proxy(that._positionDropzone, that);\n                }\n            },\n            _dropEnter: function () {\n                this._positionDropzone();\n                $(document).on('scroll' + NS, this._scrollHandler);\n            },\n            _dropLeave: function () {\n                this._removeDropzone();\n                $(document).off('scroll' + NS, this._scrollHandler);\n            },\n            _positionDropzone: function () {\n                var that = this, element = that.element, offset = element.offset();\n                that.toolbar.find('.k-dropzone').addClass('k-filebrowser-dropzone').offset(offset).css({\n                    width: element[0].clientWidth,\n                    height: element[0].clientHeight,\n                    lineHeight: element[0].clientHeight + 'px'\n                });\n            },\n            _removeDropzone: function () {\n                this.toolbar.find('.k-dropzone').removeClass('k-filebrowser-dropzone').css({\n                    width: '',\n                    height: '',\n                    lineHeight: '',\n                    top: '',\n                    left: ''\n                });\n            },\n            _deleteClick: function () {\n                var that = this, item = that.listView.select(), message = kendo.format(that.options.messages.deleteFile, item.find('strong').text());\n                if (item.length && that._showMessage(message, 'confirm')) {\n                    that.listView.remove(item);\n                }\n            },\n            _addClick: function () {\n                this.createDirectory();\n            },\n            _getFieldName: function (name) {\n                return fieldName(this.dataSource.reader.model.fields, name);\n            },\n            _fileUpload: function (e) {\n                var that = this, options = that.options, fileTypes = options.fileTypes, filterRegExp = new RegExp(('(' + fileTypes.split(',').join(')|(') + ')').replace(/\\*\\./g, '.*.'), 'i'), fileName = e.files[0].name, fileSize = e.files[0].size, fileNameField = NAMEFIELD, sizeField = SIZEFIELD, file;\n                if (filterRegExp.test(fileName)) {\n                    e.data = { path: that.path() };\n                    file = that._createFile(fileName, fileSize);\n                    if (!file) {\n                        e.preventDefault();\n                    } else {\n                        that.upload.one('success', function (e) {\n                            var model = that._insertFileToList(file);\n                            if (model._override) {\n                                model.set(fileNameField, e.response[that._getFieldName(fileNameField)]);\n                                model.set(sizeField, e.response[that._getFieldName(sizeField)]);\n                                that.listView.dataSource.pushUpdate(model);\n                            }\n                            that._tiles = that.listView.items().filter('[' + kendo.attr('type') + '=f]');\n                        });\n                    }\n                } else {\n                    e.preventDefault();\n                    that._showMessage(kendo.format(options.messages.invalidFileType, fileName, fileTypes));\n                }\n            },\n            _findFile: function (name) {\n                var data = this.dataSource.data(), idx, result, typeField = TYPEFIELD, nameField = NAMEFIELD, length;\n                name = name.toLowerCase();\n                for (idx = 0, length = data.length; idx < length; idx++) {\n                    if (data[idx].get(typeField) === 'f' && data[idx].get(nameField).toLowerCase() === name) {\n                        result = data[idx];\n                        break;\n                    }\n                }\n                return result;\n            },\n            _createFile: function (fileName, fileSize) {\n                var that = this, model = {}, typeField = TYPEFIELD, file = that._findFile(fileName);\n                if (file) {\n                    if (!that._showMessage(kendo.format(that.options.messages.overwriteFile, fileName), 'confirm')) {\n                        return null;\n                    } else {\n                        file._override = true;\n                        return file;\n                    }\n                }\n                model[typeField] = 'f';\n                model[NAMEFIELD] = fileName;\n                model[SIZEFIELD] = fileSize;\n                return model;\n            },\n            _insertFileToList: function (model) {\n                var index;\n                if (model._override) {\n                    return model;\n                }\n                var dataSource = this.dataSource;\n                var view = dataSource.view();\n                for (var i = 0, length = view.length; i < length; i++) {\n                    if (view[i].get(TYPEFIELD) === 'f') {\n                        index = i;\n                        break;\n                    }\n                }\n                return dataSource.insert(++index, model);\n            },\n            createDirectory: function () {\n                var that = this, idx, length, lastDirectoryIdx = 0, typeField = TYPEFIELD, nameField = NAMEFIELD, view = that.dataSource.data(), name = that._nameDirectory(), model = new that.dataSource.reader.model();\n                for (idx = 0, length = view.length; idx < length; idx++) {\n                    if (view[idx].get(typeField) === 'd') {\n                        lastDirectoryIdx = idx;\n                    }\n                }\n                model.set(typeField, 'd');\n                model.set(nameField, name);\n                that.listView.one('dataBound', function () {\n                    var selected = that.listView.items().filter('[' + kendo.attr('uid') + '=' + model.uid + ']');\n                    if (selected.length) {\n                        this.edit(selected);\n                    }\n                    this.element.scrollTop(selected.attr('offsetTop') - this.element[0].offsetHeight);\n                    setTimeout(function () {\n                        that.listView.element.find('.k-edit-item input').select();\n                    });\n                }).one('save', function (e) {\n                    var value = e.model.get(nameField);\n                    if (!value) {\n                        e.model.set(nameField, name);\n                    } else {\n                        e.model.set(nameField, that._nameExists(value, model.uid) ? that._nameDirectory() : value);\n                    }\n                });\n                that.dataSource.insert(++lastDirectoryIdx, model);\n            },\n            _directoryKeyDown: function (e) {\n                if (e.keyCode == 13) {\n                    e.currentTarget.blur();\n                }\n            },\n            _directoryBlur: function () {\n                this.listView.save();\n            },\n            _nameExists: function (name, uid) {\n                var data = this.dataSource.data(), typeField = TYPEFIELD, nameField = NAMEFIELD, idx, length;\n                for (idx = 0, length = data.length; idx < length; idx++) {\n                    if (data[idx].get(typeField) === 'd' && data[idx].get(nameField).toLowerCase() === name.toLowerCase() && data[idx].uid !== uid) {\n                        return true;\n                    }\n                }\n                return false;\n            },\n            _nameDirectory: function () {\n                var name = 'New folder', data = this.dataSource.data(), directoryNames = [], typeField = TYPEFIELD, nameField = NAMEFIELD, candidate, idx, length;\n                for (idx = 0, length = data.length; idx < length; idx++) {\n                    if (data[idx].get(typeField) === 'd' && data[idx].get(nameField).toLowerCase().indexOf(name.toLowerCase()) > -1) {\n                        directoryNames.push(data[idx].get(nameField));\n                    }\n                }\n                if ($.inArray(name, directoryNames) > -1) {\n                    idx = 2;\n                    do {\n                        candidate = name + ' (' + idx + ')';\n                        idx++;\n                    } while ($.inArray(candidate, directoryNames) > -1);\n                    name = candidate;\n                }\n                return name;\n            },\n            orderBy: function (field) {\n                this.dataSource.sort([\n                    {\n                        field: TYPEFIELD,\n                        dir: 'asc'\n                    },\n                    {\n                        field: field,\n                        dir: 'asc'\n                    }\n                ]);\n            },\n            search: function (name) {\n                this.dataSource.filter({\n                    field: NAMEFIELD,\n                    operator: 'contains',\n                    value: name\n                });\n            },\n            _content: function () {\n                var that = this;\n                that.list = $('<ul class=\"k-reset k-floats k-tiles\" />').appendTo(that.element).on('dblclick' + NS, 'li', proxy(that._dblClick, that));\n                that.listView = new kendo.ui.ListView(that.list, {\n                    dataSource: that.dataSource,\n                    template: that._itemTmpl(),\n                    editTemplate: that._editTmpl(),\n                    selectable: true,\n                    autoBind: false,\n                    dataBinding: function (e) {\n                        that.toolbar.find('.k-i-close').parent().addClass('k-state-disabled');\n                        if (e.action === 'remove' || e.action === 'sync') {\n                            e.preventDefault();\n                            kendo.ui.progress(that.listView.element, false);\n                        }\n                    },\n                    dataBound: function () {\n                        if (that.dataSource.view().length) {\n                            that._tiles = this.items().filter('[' + kendo.attr('type') + '=f]');\n                        } else {\n                            this.wrapper.append(EMPTYTILE({ text: that.options.messages.emptyFolder }));\n                        }\n                    },\n                    change: proxy(that._listViewChange, that)\n                });\n            },\n            _dblClick: function (e) {\n                var that = this, li = $(e.currentTarget);\n                if (li.hasClass('k-edit-item')) {\n                    that._directoryBlur();\n                }\n                if (li.filter('[' + kendo.attr('type') + '=d]').length) {\n                    var folder = that.dataSource.getByUid(li.attr(kendo.attr('uid')));\n                    if (folder) {\n                        that.path(concatPaths(that.path(), folder.get(NAMEFIELD)));\n                        that.breadcrumbs.value(that.path());\n                    }\n                } else if (li.filter('[' + kendo.attr('type') + '=f]').length) {\n                    that.trigger(APPLY);\n                }\n            },\n            _listViewChange: function () {\n                var selected = this._selectedItem();\n                if (selected) {\n                    this.toolbar.find('.k-i-close').parent().removeClass('k-state-disabled');\n                    this.trigger(CHANGE, { selected: selected });\n                }\n            },\n            _dataSource: function () {\n                var that = this, options = that.options, transport = options.transport, typeSortOrder = extend({}, DEFAULTSORTORDER), nameSortOrder = {\n                        field: NAMEFIELD,\n                        dir: 'asc'\n                    }, schema, dataSource = {\n                        type: transport.type || 'filebrowser',\n                        sort: [\n                            typeSortOrder,\n                            nameSortOrder\n                        ]\n                    };\n                if (isPlainObject(transport)) {\n                    transport.path = proxy(that.path, that);\n                    dataSource.transport = transport;\n                }\n                if (isPlainObject(options.schema)) {\n                    dataSource.schema = options.schema;\n                } else if (transport.type && isPlainObject(kendo.data.schemas[transport.type])) {\n                    schema = kendo.data.schemas[transport.type];\n                }\n                if (that.dataSource && that._errorHandler) {\n                    that.dataSource.unbind(ERROR, that._errorHandler);\n                } else {\n                    that._errorHandler = proxy(that._error, that);\n                }\n                that.dataSource = kendo.data.DataSource.create(dataSource).bind(ERROR, that._errorHandler);\n            },\n            _navigation: function () {\n                var that = this, navigation = $('<div class=\"k-floatwrap\"><input/><input/></div>').appendTo(this.element);\n                that.breadcrumbs = navigation.find('input:first').kendoBreadcrumbs({\n                    value: that.options.path,\n                    change: function () {\n                        that.path(this.value());\n                    }\n                }).data('kendoBreadcrumbs');\n                that.searchBox = navigation.parent().find('input:last').kendoSearchBox({\n                    label: that.options.messages.search,\n                    change: function () {\n                        that.search(this.value());\n                    }\n                }).data('kendoSearchBox');\n            },\n            _error: function (e) {\n                var that = this, status;\n                if (!that.trigger(ERROR, e)) {\n                    status = e.xhr.status;\n                    if (e.status == 'error') {\n                        if (status == '404') {\n                            that._showMessage(that.options.messages.directoryNotFound);\n                        } else if (status != '0') {\n                            that._showMessage('Error! The requested URL returned ' + status + ' - ' + e.xhr.statusText);\n                        }\n                    } else if (status == 'timeout') {\n                        that._showMessage('Error! Server timeout.');\n                    }\n                    var dataSource = that.dataSource;\n                    if (dataSource.hasChanges()) {\n                        dataSource.cancelChanges();\n                    }\n                }\n            },\n            _showMessage: function (message, type) {\n                return window[type || 'alert'](message);\n            },\n            refresh: function () {\n                var that = this;\n                that._navigation();\n                that._toolbar();\n                that._content();\n            },\n            _editTmpl: function () {\n                var html = '<li class=\"k-tile k-state-selected\" ' + kendo.attr('uid') + '=\"#=uid#\" ';\n                html += kendo.attr('type') + '=\"${' + TYPEFIELD + '}\">';\n                html += '#if(' + TYPEFIELD + ' == \"d\") { #';\n                html += '<div class=\"k-thumb\"><span class=\"k-icon k-i-folder\"></span></div>';\n                html += '#}else{#';\n                html += '<div class=\"k-thumb\"><span class=\"k-icon k-i-loading\"></span></div>';\n                html += '#}#';\n                html += '#if(' + TYPEFIELD + ' == \"d\") { #';\n                html += '<input class=\"k-input\" ' + kendo.attr('bind') + '=\"value:' + NAMEFIELD + '\"/>';\n                html += '#}#';\n                html += '</li>';\n                return proxy(kendo.template(html), { sizeFormatter: sizeFormatter });\n            },\n            _itemTmpl: function () {\n                var html = '<li class=\"k-tile\" ' + kendo.attr('uid') + '=\"#=uid#\" ';\n                html += kendo.attr('type') + '=\"${' + TYPEFIELD + '}\">';\n                html += '#if(' + TYPEFIELD + ' == \"d\") { #';\n                html += '<div class=\"k-thumb\"><span class=\"k-icon k-i-folder\"></span></div>';\n                html += '#}else{#';\n                html += '<div class=\"k-thumb\"><span class=\"k-icon k-i-file\"></span></div>';\n                html += '#}#';\n                html += '<strong>${' + NAMEFIELD + '}</strong>';\n                html += '#if(' + TYPEFIELD + ' == \"f\") { # <span class=\"k-filesize\">${this.sizeFormatter(' + SIZEFIELD + ')}</span> #}#';\n                html += '</li>';\n                return proxy(kendo.template(html), { sizeFormatter: sizeFormatter });\n            },\n            path: function (value) {\n                var that = this, path = that._path || '';\n                if (value !== undefined) {\n                    that._path = value.replace(trimSlashesRegExp, '') + '/';\n                    that.dataSource.read({ path: that._path });\n                    return;\n                }\n                if (path) {\n                    path = path.replace(trimSlashesRegExp, '');\n                }\n                return path === '/' || path === '' ? '' : path + '/';\n            }\n        });\n        var SearchBox = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                options = options || {};\n                Widget.fn.init.call(that, element, options);\n                if (placeholderSupported) {\n                    that.element.attr('placeholder', that.options.label);\n                }\n                that._wrapper();\n                that.element.on('keydown' + SEARCHBOXNS, proxy(that._keydown, that)).on('change' + SEARCHBOXNS, proxy(that._updateValue, that));\n                that.wrapper.on(CLICK + SEARCHBOXNS, 'a', proxy(that._click, that));\n                if (!placeholderSupported) {\n                    that.element.on('focus' + SEARCHBOXNS, proxy(that._focus, that)).on('blur' + SEARCHBOXNS, proxy(that._blur, that));\n                }\n            },\n            options: {\n                name: 'SearchBox',\n                label: 'Search',\n                value: ''\n            },\n            events: [CHANGE],\n            destroy: function () {\n                var that = this;\n                that.wrapper.add(that.element).add(that.label).off(SEARCHBOXNS);\n                Widget.fn.destroy.call(that);\n            },\n            _keydown: function (e) {\n                if (e.keyCode === 13) {\n                    this._updateValue();\n                }\n            },\n            _click: function (e) {\n                e.preventDefault();\n                this._updateValue();\n            },\n            _updateValue: function () {\n                var that = this, value = that.element.val();\n                if (value !== that.value()) {\n                    that.value(value);\n                    that.trigger(CHANGE);\n                }\n            },\n            _blur: function () {\n                this._updateValue();\n                this._toggleLabel();\n            },\n            _toggleLabel: function () {\n                if (!placeholderSupported) {\n                    this.label.toggle(!this.element.val());\n                }\n            },\n            _focus: function () {\n                this.label.hide();\n            },\n            _wrapper: function () {\n                var element = this.element, wrapper = element.parents('.k-search-wrap');\n                element[0].style.width = '';\n                element.addClass('k-input');\n                if (!wrapper.length) {\n                    wrapper = element.wrap($('<div class=\"k-widget k-search-wrap k-textbox\"/>')).parent();\n                    if (!placeholderSupported) {\n                        $('<label style=\"display:block\">' + this.options.label + '</label>').insertBefore(element);\n                    }\n                    $('<a href=\"#\" class=\"k-icon k-i-zoom k-search\"/>').appendTo(wrapper);\n                }\n                this.wrapper = wrapper;\n                this.label = wrapper.find('>label');\n            },\n            value: function (value) {\n                var that = this;\n                if (value !== undefined) {\n                    that.options.value = value;\n                    that.element.val(value);\n                    that._toggleLabel();\n                    return;\n                }\n                return that.options.value;\n            }\n        });\n        var Breadcrumbs = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                options = options || {};\n                Widget.fn.init.call(that, element, options);\n                that._wrapper();\n                that.wrapper.on('focus' + BREADCRUBMSNS, 'input', proxy(that._focus, that)).on('blur' + BREADCRUBMSNS, 'input', proxy(that._blur, that)).on('keydown' + BREADCRUBMSNS, 'input', proxy(that._keydown, that)).on(CLICK + BREADCRUBMSNS, 'a.k-i-arrow-60-up:first', proxy(that._rootClick, that)).on(CLICK + BREADCRUBMSNS, 'a:not(.k-i-arrow-60-up)', proxy(that._click, that));\n                that.value(that.options.value);\n            },\n            options: {\n                name: 'Breadcrumbs',\n                gap: 50\n            },\n            events: [CHANGE],\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.wrapper.add(that.wrapper.find('input')).add(that.wrapper.find('a')).off(BREADCRUBMSNS);\n            },\n            _update: function (val) {\n                val = (val || '').charAt(0) === '/' ? val : '/' + (val || '');\n                if (val !== this.value()) {\n                    this.value(val);\n                    this.trigger(CHANGE);\n                }\n            },\n            _click: function (e) {\n                e.preventDefault();\n                this._update(this._path($(e.target).prevAll('a:not(.k-i-arrow-60-up)').addBack()));\n            },\n            _rootClick: function (e) {\n                e.preventDefault();\n                this._update('');\n            },\n            _focus: function () {\n                var that = this, element = that.element;\n                that.overlay.hide();\n                that.element.val(that.value());\n                setTimeout(function () {\n                    element.select();\n                });\n            },\n            _blur: function () {\n                if (this.overlay.is(':visible')) {\n                    return;\n                }\n                var that = this, element = that.element, val = element.val().replace(/\\/{2,}/g, '/');\n                that.overlay.show();\n                element.val('');\n                that._update(val);\n            },\n            _keydown: function (e) {\n                var that = this;\n                if (e.keyCode === 13) {\n                    that._blur();\n                    setTimeout(function () {\n                        that.overlay.find('a:first').focus();\n                    });\n                }\n            },\n            _wrapper: function () {\n                var element = this.element, wrapper = element.parents('.k-breadcrumbs'), overlay;\n                element[0].style.width = '';\n                element.addClass('k-input');\n                if (!wrapper.length) {\n                    wrapper = element.wrap($('<div class=\"k-widget k-breadcrumbs k-textbox\"/>')).parent();\n                }\n                overlay = wrapper.find('.k-breadcrumbs-wrap');\n                if (!overlay.length) {\n                    overlay = $('<div class=\"k-breadcrumbs-wrap\"/>').appendTo(wrapper);\n                }\n                this.wrapper = wrapper;\n                this.overlay = overlay;\n            },\n            refresh: function () {\n                var html = '', value = this.value(), segments, segment, idx, length;\n                if (value === undefined || !value.match(/^\\//)) {\n                    value = '/' + (value || '');\n                }\n                segments = value.split('/');\n                for (idx = 0, length = segments.length; idx < length; idx++) {\n                    segment = segments[idx];\n                    if (segment) {\n                        if (!html) {\n                            html += '<a href=\"#\" class=\"k-icon k-i-arrow-60-up\" title=\"Go to parent folder\"></a>';\n                        }\n                        html += '<a class=\"k-link\" href=\"#\">' + segments[idx] + '</a>';\n                        html += '<span class=\"k-icon k-i-arrow-60-right\" title=\"Go to child folder\"></span>';\n                    }\n                }\n                this.overlay.empty().append($(html));\n                this._adjustSectionWidth();\n            },\n            _adjustSectionWidth: function () {\n                var that = this, wrapper = that.wrapper, width = wrapper.width() - that.options.gap, links = that.overlay.find('a'), a;\n                links.each(function (index) {\n                    a = $(this);\n                    if (a.parent().width() > width) {\n                        if (index == links.length - 1) {\n                            a.width(width);\n                        } else {\n                            a.prev().addBack().hide();\n                        }\n                    }\n                });\n            },\n            value: function (val) {\n                if (val !== undefined) {\n                    this._value = val.replace(/\\/{2,}/g, '/');\n                    this.refresh();\n                    return;\n                }\n                return this._value;\n            },\n            _path: function (trail) {\n                return '/' + $.map(trail, function (b) {\n                    return $(b).text();\n                }).join('/');\n            }\n        });\n        kendo.ui.plugin(FileBrowser);\n        kendo.ui.plugin(Breadcrumbs);\n        kendo.ui.plugin(SearchBox);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}