{"version": 3, "sources": ["kendo.timepicker.js"], "names": ["f", "define", "$", "undefined", "setTime", "date", "time", "ignoreDST", "offsetDiff", "offset", "getTimezoneOffset", "getTime", "MS_PER_MINUTE", "dst", "today", "DATE", "midnight", "getFullYear", "getMonth", "getDate", "noon", "getMilliseconds", "getHours", "getMinutes", "getSeconds", "lastTimeOption", "interval", "Date", "setMinutes", "isInRange", "value", "min", "max", "msValue", "msMin", "msMax", "MS_PER_DAY", "normalize", "options", "parseFormats", "format", "extractFormat", "kendo", "getCulture", "culture", "calendars", "standard", "patterns", "t", "isArray", "splice", "preventDefault", "e", "TimeView", "TimePicker", "window", "keys", "parse", "parseDate", "activeElement", "_activeElement", "_extractFormat", "support", "browser", "ui", "Widget", "OPEN", "CLOSE", "CHANGE", "ns", "CLICK", "DEFAULT", "DISABLED", "READONLY", "LI", "SPAN", "FOCUSED", "HOVER", "HOVEREVENTS", "MOUSEDOWN", "SELECTED", "STATEDISABLED", "ARIA_SELECTED", "ARIA_EXPANDED", "ARIA_HIDDEN", "ARIA_DISABLED", "ARIA_ACTIVEDESCENDANT", "ID", "extend", "proxy", "TODAY", "that", "this", "id", "_dates", "ul", "css", "overflow", "kineticScrollNeeded", "on", "_click", "addClass", "removeClass", "list", "append", "_timeViewID", "_optionID", "attr", "_popup", "_heightH<PERSON>ler", "_height", "template", "useWithBlock", "prototype", "current", "candidate", "active", "_current", "length", "removeAttribute", "scroll", "close", "popup", "destroy", "off", "open", "popupHovered", "<PERSON><PERSON><PERSON><PERSON>", "bind", "_hovered", "setTimeout", "dataBind", "dates", "toString", "idx", "html", "_html", "refresh", "msStart", "lastIdx", "msLastTime", "msInterval", "start", "startDate", "parseInt", "push", "innerHTML", "unbind", "one", "select", "_value", "item", "content", "itemOffsetTop", "offsetTop", "itemOffsetHeight", "offsetHeight", "contentScrollTop", "scrollTop", "contentOffsetHeight", "clientHeight", "bottomDistance", "li", "selection", "text", "grep", "childNodes", "node", "textContent", "innerText", "_distinctSelection", "currentValue", "selectionIndex", "inArray", "children", "setOptions", "old", "change", "toggle", "visible", "currentTarget", "index", "isDefaultPrevented", "parent", "height", "add", "show", "scrollHeight", "hide", "_parse", "_adjustListWidth", "computedStyle", "computedWidth", "width", "style", "wrapper", "anchor", "outerWidth", "_outerWidth", "data", "getComputedStyle", "parseFloat", "mozilla", "msie", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "fontFamily", "Popup", "animation", "isRtl", "move", "key", "keyCode", "down", "DOWN", "UP", "altKey", "nextS<PERSON>ling", "previousSibling", "<PERSON><PERSON><PERSON><PERSON>", "ENTER", "TAB", "ESC", "init", "element", "timeView", "disabled", "fn", "call", "_initialOptions", "_wrapper", "trigger", "_change", "val", "_icon", "_reset", "setAttribute", "type", "role", "aria-expanded", "aria-owns", "autocomplete", "is", "parents", "enable", "readonly", "dateInput", "_dateInput", "DateInput", "_old", "_update", "_oldText", "notify", "name", "events", "_editable", "disable", "arrow", "_arrow", "_inputWrapper", "_unbindInput", "_toggleHover", "_keydown", "_blur", "_bindInput", "_form", "_reset<PERSON><PERSON><PERSON>", "_option", "touch", "dateChanged", "valueUpdated", "textFormatted", "oldValue", "calendar", "isEqualDate", "_typing", "next", "insertAfter", "aria-controls", "stopImmediatePropagation", "option", "toggleClass", "wrap", "cssText", "className", "formId", "form", "closest", "defaultValue", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,oBACH,cACA,mBACDD,IACL,WA4nBE,MApnBC,UAAUE,EAAGC,GAoRV,QAASC,GAAQC,EAAMC,EAAMC,GACzB,GAAuCC,GAAnCC,EAASJ,EAAKK,mBAClBL,GAAKD,QAAQC,EAAKM,UAAYL,GACzBC,IACDC,EAAaH,EAAKK,oBAAsBD,EACxCJ,EAAKD,QAAQC,EAAKM,UAAYH,EAAaI,IAGnD,QAASC,KACL,GAAIC,GAAQ,GAAIC,GAAQC,EAAW,GAAID,GAAKD,EAAMG,cAAeH,EAAMI,WAAYJ,EAAMK,UAAW,EAAG,EAAG,GAAIC,EAAO,GAAIL,GAAKD,EAAMG,cAAeH,EAAMI,WAAYJ,EAAMK,UAAW,GAAI,EAAG,EAC7L,WAAaH,EAASN,oBAAsBU,EAAKV,qBAErD,QAASW,GAAgBhB,GACrB,MAAyB,IAAlBA,EAAKiB,WAAkBV,EAAgBP,EAAKkB,aAAeX,EAAoC,IAApBP,EAAKmB,aAAsBnB,EAAKgB,kBAEtH,QAASI,GAAeC,GACpB,GAAIrB,GAAO,GAAIsB,MAAK,KAAM,EAAG,EAE7B,OADAtB,GAAKuB,YAAYF,GACVrB,EAEX,QAASwB,GAAUC,EAAOC,EAAKC,GAC3B,GAAgEC,GAA5DC,EAAQb,EAAgBU,GAAMI,EAAQd,EAAgBW,EAC1D,QAAKF,GAASI,GAASC,IAGvBF,EAAUZ,EAAgBS,GACtBI,EAAQD,IACRA,GAAWG,GAEXD,EAAQD,IACRC,GAASC,GAENH,GAAWC,GAASD,GAAWE,GAoT1C,QAASE,GAAUC,GACf,GAAIC,GAAeD,EAAQC,YAC3BD,GAAQE,OAASC,EAAcH,EAAQE,QAAUE,EAAMC,WAAWL,EAAQM,SAASC,UAAUC,SAASC,SAASC,GAC/GT,EAAeU,EAAQV,GAAgBA,GAAgBA,GACvDA,EAAaW,OAAO,EAAG,EAAGZ,EAAQE,QAClCF,EAAQC,aAAeA,EAE3B,QAASY,GAAeC,GACpBA,EAAED,iBAhnBT,GAGOE,GAqTAC,EAvTAZ,EAAQa,OAAOb,MAAOc,EAAOd,EAAMc,KAAMC,EAAQf,EAAMgB,UAAWC,EAAgBjB,EAAMkB,eAAgBnB,EAAgBC,EAAMmB,eAAgBC,EAAUpB,EAAMoB,QAASC,EAAUD,EAAQC,QAASC,EAAKtB,EAAMsB,GAAIC,EAASD,EAAGC,OAAQC,EAAO,OAAQC,EAAQ,QAASC,EAAS,SAAUC,EAAK,mBAAoBC,EAAQ,QAAUD,EAAIE,EAAU,kBAAmBC,EAAW,WAAYC,EAAW,WAAYC,EAAK,KAAMC,EAAO,UAAWC,EAAU,kBAAmBC,EAAQ,gBAAiBC,EAAc,aAAeT,EAAK,cAAgBA,EAAIU,EAAY,YAAcV,EAAIzD,EAAgB,IAAOwB,EAAa,MAAU4C,EAAW,mBAAoBC,EAAgB,mBAAoBC,EAAgB,gBAAiBC,EAAgB,gBAAiBC,EAAc,cAAeC,EAAgB,gBAAiBC,EAAwB,wBAAyBC,EAAK,KAAMtC,EAAU/C,EAAE+C,QAASuC,EAAStF,EAAEsF,OAAQC,EAAQvF,EAAEuF,MAAO1E,EAAOY,KAAM+D,EAAQ,GAAI3E,EAC57B2E,GAAQ,GAAI3E,GAAK2E,EAAMzE,cAAeyE,EAAMxE,WAAYwE,EAAMvE,UAAW,EAAG,EAAG,GAC3EkC,EAAW,SAAUf,GACrB,GAAIqD,GAAOC,KAAMC,EAAKvD,EAAQuD,EAC9BF,GAAKrD,QAAUA,EACfqD,EAAKG,UACLH,EAAKI,GAAK7F,EAAE,kGAAkG8F,KAAMC,SAAUnC,EAAQoC,oBAAsB,GAAK,SAAUC,GAAG7B,EAAOI,EAAIe,EAAME,EAAKS,OAAQT,IAAOQ,GAAG,aAAe9B,EAAIK,EAAI,WACzOxE,EAAE0F,MAAMS,SAASxB,KAClBsB,GAAG,aAAe9B,EAAIK,EAAI,WACzBxE,EAAE0F,MAAMU,YAAYzB,KAExBc,EAAKY,KAAOrG,EAAE,qEAAyEsG,OAAOb,EAAKI,IAAII,GAAGpB,EAAW5B,GACjH0C,IACAF,EAAKc,YAAcZ,EAAK,YACxBF,EAAKe,UAAYb,EAAK,mBACtBF,EAAKI,GAAGY,KAAKpB,EAAII,EAAKc,cAE1Bd,EAAKiB,SACLjB,EAAKkB,eAAiBpB,EAAME,EAAKmB,QAASnB,GAC1CA,EAAKoB,SAAWrE,EAAMqE,SAAS,iFAAmFC,cAAc,KAEpI3D,EAAS4D,WACLC,QAAS,SAAUC,GACf,GAAIxB,GAAOC,KAAMwB,EAASzB,EAAKrD,QAAQ8E,MACvC,OAAID,KAAchH,EAiBPwF,EAAK0B,UAhBR1B,EAAK0B,WACL1B,EAAK0B,SAASf,YAAYtB,GACtBW,EAAK0B,UAAY1B,EAAK0B,SAASC,SAC/B3B,EAAK0B,SAAS,GAAGE,gBAAgBhC,GACjCI,EAAK0B,SAAS,GAAGE,gBAAgBrC,KAGrCiC,IACAA,EAAYjH,EAAEiH,GAAWd,SAASrB,GAAU2B,KAAKpB,EAAII,EAAKe,WAAWC,KAAKzB,GAAe,GACzFS,EAAK6B,OAAOL,EAAU,KAE1BxB,EAAK0B,SAAWF,EACZC,GACAA,EAAOD,GAbX,IAmBRM,MAAO,WACH7B,KAAK8B,MAAMD,SAEfE,QAAS,WACL,GAAIhC,GAAOC,IACXD,GAAKI,GAAG6B,IAAIvD,GACZsB,EAAKY,KAAKqB,IAAIvD,GACdsB,EAAK+B,MAAMC,WAEfE,KAAM,WAAA,GAEEC,GADAnC,EAAOC,IAEND,GAAKI,GAAG,GAAGgC,YACZpC,EAAKqC,OAETF,EAAenC,EAAK+B,MAAMO,SAC1BtC,EAAK+B,MAAMO,UAAW,EACtBtC,EAAK+B,MAAMG,OACXK,WAAW,WACPvC,EAAK+B,MAAMO,SAAWH,GACvB,GACCnC,EAAK0B,UACL1B,EAAK6B,OAAO7B,EAAK0B,SAAS,KAGlCc,SAAU,SAAUC,GAEhB,IADA,GAAuJ/H,GAAnJsF,EAAOC,KAAMtD,EAAUqD,EAAKrD,QAASE,EAASF,EAAQE,OAAQ6F,EAAW3F,EAAM2F,SAAUtB,EAAWpB,EAAKoB,SAAUO,EAASc,EAAMd,OAAQgB,EAAM,EAASC,EAAO,GAC7JD,EAAMhB,EAAQgB,IACjBjI,EAAO+H,EAAME,GACTzG,EAAUxB,EAAMiC,EAAQP,IAAKO,EAAQN,OACrCuG,GAAQxB,EAASsB,EAAShI,EAAMmC,EAAQF,EAAQM,UAGxD+C,GAAK6C,MAAMD,IAEfE,QAAS,WACL,GAAqaC,GAASC,EAAkBrB,EAA5b3B,EAAOC,KAAMtD,EAAUqD,EAAKrD,QAASE,EAASF,EAAQE,OAAQ/B,EAASI,IAAON,EAAYE,EAAS,EAAGsB,EAAMO,EAAQP,IAAKC,EAAMM,EAAQN,IAAKE,EAAQb,EAAgBU,GAAMI,EAAQd,EAAgBW,GAAM4G,EAAavH,EAAgBI,EAAea,EAAQZ,WAAYmH,EAAavG,EAAQZ,SAAWd,EAAeyH,EAAW3F,EAAM2F,SAAUtB,EAAWpB,EAAKoB,SAAU+B,EAAQ,GAAI/H,KAAMgB,IAAMgH,EAAY,GAAIhI,GAAK+H,GAA0BR,EAAM,EAAWC,EAAO,EAa/c,KAXIjB,EADA/G,GACU6B,EAAa3B,EAASG,GAAiBiI,EAExCzG,EAAayG,EAEtB3G,GAASC,GAASyG,IAAezG,IAC7BD,EAAQC,IACRA,GAASC,GAEbkF,GAAUnF,EAAQD,GAAS2G,EAAa,GAE5CF,EAAUK,SAAS1B,EAAQ,IACpBgB,EAAMhB,EAAQgB,IACbA,GACAlI,EAAQ0I,EAAOD,EAAYtI,GAE3B4B,GAASwG,GAAWL,IACpBI,EAAUrH,EAAgByH,GACtBC,EAAYD,IACZJ,GAAWtG,GAEXsG,EAAUvG,IACV2G,EAAQ,GAAI/H,KAAMiB,MAG1B2D,EAAKG,OAAOmD,KAAK5H,EAAgByH,IACjCP,GAAQxB,EAASsB,EAASS,EAAOtG,EAAQF,EAAQM,SAErD+C,GAAK6C,MAAMD,IAEfP,KAAM,WACF,GAAIrC,GAAOC,KAAMwC,EAAQzC,EAAKrD,QAAQ8F,KAClCA,IAASA,EAAM,GACfzC,EAAKwC,SAASC,GAEdzC,EAAK8C,WAGbD,MAAO,SAAUD,GACb,GAAI5C,GAAOC,IACXD,GAAKI,GAAG,GAAGmD,UAAYX,EACvB5C,EAAK+B,MAAMyB,OAAOjF,EAAMyB,EAAKkB,gBAC7BlB,EAAK+B,MAAM0B,IAAIlF,EAAMyB,EAAKkB,gBAC1BlB,EAAKuB,QAAQ,MACbvB,EAAK0D,OAAO1D,EAAK2D,SAErB9B,OAAQ,SAAU+B,GACd,GAAKA,EAAL,CAGA,GAAIC,GAAU5D,KAAKW,KAAK,GAAIkD,EAAgBF,EAAKG,UAAWC,EAAmBJ,EAAKK,aAAcC,EAAmBL,EAAQM,UAAWC,EAAsBP,EAAQQ,aAAcC,EAAiBR,EAAgBE,CACjNE,GAAmBJ,EACnBI,EAAmBJ,EACZQ,EAAiBJ,EAAmBE,IAC3CF,EAAmBI,EAAiBF,GAExCP,EAAQM,UAAYD,IAExBR,OAAQ,SAAUa,GACd,GAAkEC,GAA9DxE,EAAOC,KAAMtD,EAAUqD,EAAKrD,QAAS4E,EAAUvB,EAAK0B,QACpD6C,aAAcvI,QACduI,EAAKxH,EAAM2F,SAAS6B,EAAI5H,EAAQE,OAAQF,EAAQM,UAElC,gBAAPsH,KACFhD,GAAWA,EAAQkD,SAAWF,EAM/BA,EAAKhD,GALLgD,EAAKhK,EAAEmK,KAAK1E,EAAKI,GAAG,GAAGuE,WAAY,SAAUC,GACzC,OAAQA,EAAKC,aAAeD,EAAKE,YAAcP,IAEnDA,EAAKA,EAAG,GAAKA,EAAK,OAK1BC,EAAYxE,EAAK+E,mBAAmBR,GACpCvE,EAAKuB,QAAQiD,IAEjBO,mBAAoB,SAAUP,GAC1B,GAAiBQ,GAAcC,EAA3BjF,EAAOC,IAMX,OALIuE,IAAaA,EAAU7C,OAAS,IAChCqD,EAAetJ,EAAgBsE,EAAK2D,QACpCsB,EAAiB1K,EAAE2K,QAAQF,EAAchF,EAAKG,QAC9CqE,EAAYxE,EAAKI,GAAG+E,WAAWF,IAE5BT,GAEXY,WAAY,SAAUzI,GAClB,GAAI0I,GAAMpF,KAAKtD,OACfA,GAAQP,IAAM0B,EAAMnB,EAAQP,KAC5BO,EAAQN,IAAMyB,EAAMnB,EAAQN,KAC5B4D,KAAKtD,QAAUkD,EAAOwF,EAAK1I,GACvB8E,OAAQ4D,EAAI5D,OACZ6D,OAAQD,EAAIC,OACZxD,MAAOuD,EAAIvD,MACXI,KAAMmD,EAAInD,OAEdjC,KAAKoC,QAETkD,OAAQ,WACJ,GAAIvF,GAAOC,IACPD,GAAK+B,MAAMyD,UACXxF,EAAK8B,QAEL9B,EAAKkC,QAGb/F,MAAO,SAAUA,GACb,GAAI6D,GAAOC,IACXD,GAAK2D,OAASxH,EACV6D,EAAKI,GAAG,GAAGgC,YACXpC,EAAK0D,OAAOvH,IAGpBsE,OAAQ,SAAUhD,GACd,GAAIuC,GAAOC,KAAMsE,EAAKhK,EAAEkD,EAAEgI,eAAgB/K,EAAO6J,EAAGE,OAAQhC,EAAQzC,EAAKrD,QAAQ8F,KAC7EA,IAASA,EAAMd,OAAS,IACxBjH,EAAO+H,EAAM8B,EAAGmB,UAEfjI,EAAEkI,uBACH3F,EAAK0D,OAAOa,GACZvE,EAAKrD,QAAQ2I,OAAO5K,GAAM,GAC1BsF,EAAK8B,UAGbX,QAAS,WAAA,GACDnB,GAAOC,KACPW,EAAOZ,EAAKY,KACZgF,EAAShF,EAAKgF,OAAO,0BACrBC,EAAS7F,EAAKrD,QAAQkJ,MACtB7F,GAAKI,GAAG,GAAG+E,SAASxD,QACpBf,EAAKkF,IAAIF,GAAQG,OAAOF,OAAO7F,EAAKI,GAAG,GAAG4F,aAAeH,EAASA,EAAS,QAAQI,QAG3FC,OAAQ,SAAU/J,GACd,GAAI6D,GAAOC,KAAMtD,EAAUqD,EAAKrD,QAASP,EAAMV,EAAgBiB,EAAQP,MAAQV,EAAgBqE,GAASpD,EAAQP,IAAM,KAAMC,EAAMX,EAAgBiB,EAAQN,MAAQX,EAAgBqE,GAASpD,EAAQN,IAAM,KAAMkF,EAAUvB,EAAK2D,QAAUvH,GAAOC,GAAO0D,CACtP,OAAI5D,aAAiBf,GACVe,GAEXA,EAAQ2B,EAAM3B,EAAOQ,EAAQC,aAAcD,EAAQM,SAC/Cd,IACAA,EAAQ,GAAIf,GAAKmG,EAAQjG,cAAeiG,EAAQhG,WAAYgG,EAAQ/F,UAAWW,EAAMR,WAAYQ,EAAMP,aAAcO,EAAMN,aAAcM,EAAMT,oBAE5IS,IAEXgK,iBAAkB,WACd,GAAkFC,GAAeC,EAA7FzF,EAAOX,KAAKW,KAAM0F,EAAQ1F,EAAK,GAAG2F,MAAMD,MAAOE,EAAUvG,KAAKtD,QAAQ8J,OAAsCC,EAAa3J,EAAM4J,aAC9H/F,EAAKgG,KAAK,UAAYN,IAG3BF,EAAgBxI,OAAOiJ,iBAAmBjJ,OAAOiJ,iBAAiBL,EAAQ,GAAI,MAAQ,EACtFH,EAAgBD,EAAgBU,WAAWV,EAAcE,OAASI,EAAWF,GACzEJ,IAAkBhI,EAAQ2I,SAAW3I,EAAQ4I,QAC7CX,GAAiBS,WAAWV,EAAca,aAAeH,WAAWV,EAAcc,cAAgBJ,WAAWV,EAAce,iBAAmBL,WAAWV,EAAcgB,mBAE3Kd,EAAQD,GAAiBK,EAAW9F,GAAQA,EAAK0F,SACjD1F,EAAKP,KACDgH,WAAYb,EAAQnG,IAAI,eACxBiG,MAAOA,IACRM,KAAK,QAASN,KAErBrF,OAAQ,WACJ,GAAIjB,GAAOC,KAAMW,EAAOZ,EAAKY,KAAMjE,EAAUqD,EAAKrD,QAAS8J,EAAS9J,EAAQ8J,MAC5EzG,GAAK+B,MAAQ,GAAI1D,GAAGiJ,MAAM1G,EAAMf,EAAOlD,EAAQoF,OAC3C0E,OAAQA,EACRvE,KAAMvF,EAAQuF,KACdJ,MAAOnF,EAAQmF,MACfyF,UAAW5K,EAAQ4K,UACnBC,MAAOrJ,EAAQqJ,MAAM7K,EAAQ8J,YAGrCgB,KAAM,SAAUhK,GACZ,GAAIuC,GAAOC,KAAMyH,EAAMjK,EAAEkK,QAASvH,EAAKJ,EAAKI,GAAG,GAAImB,EAAUvB,EAAK0B,SAAUkG,EAAOF,IAAQ7J,EAAKgK,IAChG,IAAIH,IAAQ7J,EAAKiK,IAAMF,EAAM,CACzB,GAAInK,EAAEsK,OAEF,MADA/H,GAAKuF,OAAOqC,GACZ,CAEArG,GADOqG,EACGrG,EAAUA,EAAQ,GAAGyG,YAAc5H,EAAGgC,WAEtCb,EAAUA,EAAQ,GAAG0G,gBAAkB7H,EAAG8H,UAEpD3G,GACAvB,EAAK0D,OAAOnC,GAEhBvB,EAAKrD,QAAQ2I,OAAOtF,EAAK0B,SAAS+C,QAClChH,EAAED,qBACKkK,KAAQ7J,EAAKsK,OAAST,IAAQ7J,EAAKuK,KAAOV,IAAQ7J,EAAKwK,MAC9D5K,EAAED,iBACE+D,GACAvB,EAAKrD,QAAQ2I,OAAO/D,EAAQkD,QAAQ,GAExCzE,EAAK8B,WAsCjBpE,EAAShC,gBAAkBA,EAC3BqB,EAAMW,SAAWA,EACbC,EAAaW,EAAOuB,QACpByI,KAAM,SAAUC,EAAS5L,GAAnB,GACeyD,GAAIoI,EAAUC,EAmEvBrM,EACAC,EACAlB,EArEJ6E,EAAOC,IACX3B,GAAOoK,GAAGJ,KAAKK,KAAK3I,EAAMuI,EAAS5L,GACnC4L,EAAUvI,EAAKuI,QACf5L,EAAUqD,EAAKrD,QACfA,EAAQP,IAAM0B,EAAMyK,EAAQvH,KAAK,SAAWlD,EAAMnB,EAAQP,KAC1DO,EAAQN,IAAMyB,EAAMyK,EAAQvH,KAAK,SAAWlD,EAAMnB,EAAQN,KAC1DK,EAAUC,GACVqD,EAAK4I,gBAAkB/I,KAAWlD,GAClCqD,EAAK6I,WACL7I,EAAKwI,SAAWA,EAAW,GAAI9K,GAASmC,KAAWlD,GAC/CuD,GAAIqI,EAAQvH,KAAKpB,GACjB6G,OAAQzG,EAAKwG,QACb3J,OAAQF,EAAQE,OAChByI,OAAQ,SAAUnJ,EAAO2M,GACjBA,EACA9I,EAAK+I,QAAQ5M,GAEboM,EAAQS,IAAI7M,IAGpB+F,KAAM,SAAUzE,GACZuC,EAAKwI,SAASrC,mBACVnG,EAAK8I,QAAQvK,GACbd,EAAED,kBAEF+K,EAAQvH,KAAKxB,GAAe,GAC5BY,EAAGY,KAAKvB,GAAa,KAG7BqC,MAAO,SAAUrE,GACTuC,EAAK8I,QAAQtK,GACbf,EAAED,kBAEF+K,EAAQvH,KAAKxB,GAAe,GAC5BY,EAAGY,KAAKvB,GAAa,KAG7BgC,OAAQ,SAAUF,GACVgH,GAAWA,EAAQ5G,QACnB4G,EAAQ,GAAG3G,gBAAgBjC,GAE3B4B,GACAgH,EAAQvH,KAAKrB,EAAuB6I,EAASzH,eAIzDX,EAAKoI,EAASpI,GACdJ,EAAKiJ,QACLjJ,EAAKkJ,QACL,KACIX,EAAQ,GAAGY,aAAa,OAAQ,QAClC,MAAO1L,GACL8K,EAAQ,GAAGa,KAAO,OAEtBb,EAAQ7H,SAAS,WAAWM,MACxBqI,KAAQ,WACRC,iBAAiB,EACjBC,YAAaf,EAAS1H,YACtB0I,aAAgB,QAEpBf,EAAWF,EAAQkB,GAAG,eAAiBlP,EAAEyF,EAAKuI,SAASmB,QAAQ,YAAYD,GAAG,aAC1EhB,EACAzI,EAAK2J,QAAO,GAEZ3J,EAAK4J,SAASrB,EAAQkB,GAAG,eAEzB9M,EAAQkN,YACJzN,EAAMO,EAAQP,IACdC,EAAMM,EAAQN,IACdlB,EAAQ,GAAIC,GACZM,EAAgBU,IAAQV,EAAgBW,KACxCD,EAAM,GAAIhB,GAAKD,EAAMG,cAAeH,EAAMI,WAAYJ,EAAMK,UAAW,EAAG,EAAG,GAC7Ea,EAAM,GAAIjB,GAAKD,EAAMG,cAAeH,EAAMI,WAAYJ,EAAMK,UAAW,GAAI,EAAG,IAElFwE,EAAK8J,WAAa,GAAIzL,GAAG0L,UAAUxB,GAC/BtL,QAASN,EAAQM,QACjBJ,OAAQF,EAAQE,OAChBT,IAAKA,EACLC,IAAKA,EACLF,MAAOQ,EAAQR,SAGvB6D,EAAKgK,KAAOhK,EAAKiK,QAAQtN,EAAQR,OAAS6D,EAAKuI,QAAQS,OACvDhJ,EAAKkK,SAAW3B,EAAQS,MACxBjM,EAAMoN,OAAOnK,IAEjBrD,SACIyN,KAAM,aACNhO,IAAK2D,EACL1D,IAAK0D,EACLlD,OAAQ,GACR4F,SACA7F,gBACAT,MAAO,KACPJ,SAAU,GACV8J,OAAQ,IACR0B,aACAsC,WAAW,GAEfQ,QACI9L,EACAC,EACAC,GAEJ2G,WAAY,SAAUzI,GAAV,GACJqD,GAAOC,KACP9D,EAAQ6D,EAAK2D,MACjBrF,GAAOoK,GAAGtD,WAAWuD,KAAK3I,EAAMrD,GAChCA,EAAUqD,EAAKrD,QACfD,EAAUC,GACVqD,EAAKwI,SAASpD,WAAWzI,GACrBR,GACA6D,EAAKuI,QAAQS,IAAIjM,EAAM2F,SAASvG,EAAOQ,EAAQE,OAAQF,EAAQM,WAGvEuF,SAAU,SAAUC,GACZnF,EAAQmF,IACRxC,KAAKuI,SAAShG,SAASC,IAG/B6H,UAAW,SAAU3N,GACjB,GAAIqD,GAAOC,KAAMsK,EAAU5N,EAAQ4N,QAASX,EAAWjN,EAAQiN,SAAUY,EAAQxK,EAAKyK,OAAOxI,IAAIvD,GAAK6J,EAAUvI,EAAKuI,QAAQtG,IAAIvD,GAAK8H,EAAUxG,EAAK0K,cAAczI,IAAIvD,EACnKsB,GAAK8J,YACL9J,EAAK8J,WAAWa,eAEff,GAAaW,GAcd/D,EAAQ9F,SAAS6J,EAAUjL,EAAgBV,GAAS+B,YAAY4J,EAAU3L,EAAUU,GACpFiJ,EAAQvH,KAAKnC,EAAU0L,GAASvJ,KAAKlC,EAAU8K,GAAU5I,KAAKtB,EAAe6K,KAd7E/D,EAAQ9F,SAAS9B,GAAS+B,YAAYrB,GAAekB,GAAGrB,EAAaa,EAAK4K,cACtErC,GAAWA,EAAQ5G,SACnB4G,EAAQ,GAAG3G,gBAAgB/C,GAC3B0J,EAAQ,GAAG3G,gBAAgB9C,IAE/ByJ,EAAQvH,KAAKtB,GAAe,GAAOc,GAAG,UAAY9B,EAAIoB,EAAME,EAAK6K,SAAU7K,IAAOQ,GAAG,WAAa9B,EAAIoB,EAAME,EAAK8K,MAAO9K,IAAOQ,GAAG,QAAU9B,EAAI,WAC5IsB,EAAK0K,cAAchK,SAASzB,KAE5Be,EAAK8J,YACL9J,EAAK8J,WAAWiB,aAEpBP,EAAMhK,GAAG7B,EAAOmB,EAAME,EAAKS,OAAQT,IAAOQ,GAAGpB,EAAW5B,KAMhEoM,SAAU,SAAUA,GAChB3J,KAAKqK,WACDV,SAAUA,IAAapP,GAAmBoP,EAC1CW,SAAS,KAGjBZ,OAAQ,SAAUA,GACd1J,KAAKqK,WACDV,UAAU,EACVW,UAAWZ,EAASA,IAAWnP,GAAmBmP,MAG1D3H,QAAS,WACL,GAAIhC,GAAOC,IACX3B,GAAOoK,GAAG1G,QAAQ2G,KAAK3I,GACvBA,EAAKwI,SAASxG,UACdhC,EAAKuI,QAAQtG,IAAIvD,GACjBsB,EAAKyK,OAAOxI,IAAIvD,GAChBsB,EAAK0K,cAAczI,IAAIvD,GACnBsB,EAAKgL,OACLhL,EAAKgL,MAAM/I,IAAI,QAASjC,EAAKiL,gBAGrCnJ,MAAO,WACH7B,KAAKuI,SAAS1G,SAElBI,KAAM,WACFjC,KAAKuI,SAAStG,QAElB9F,IAAK,SAAUD,GACX,MAAO8D,MAAKiL,QAAQ,MAAO/O,IAE/BE,IAAK,SAAUF,GACX,MAAO8D,MAAKiL,QAAQ,MAAO/O,IAE/BA,MAAO,SAAUA,GACb,GAAI6D,GAAOC,IACX,OAAI9D,KAAU3B,EACHwF,EAAK2D,QAEhB3D,EAAKgK,KAAOhK,EAAKiK,QAAQ9N,GACP,OAAd6D,EAAKgK,MACLhK,EAAKuI,QAAQS,IAAI,IAErBhJ,EAAKkK,SAAWlK,EAAKuI,QAAQS,MAJ7BhJ,IAMJ8K,MAAO,WACH,GAAI9K,GAAOC,KAAM9D,EAAQ6D,EAAKuI,QAAQS,KACtChJ,GAAK8B,QACD3F,IAAU6D,EAAKkK,UACflK,EAAK+I,QAAQ5M,GAEjB6D,EAAK0K,cAAc/J,YAAY1B,IAEnCwB,OAAQ,WACJ,GAAIT,GAAOC,KAAMsI,EAAUvI,EAAKuI,OAChCvI,GAAKwI,SAASjD,SACTpH,EAAQgN,OAAS5C,EAAQ,KAAOvK,KACjCuK,EAAQO,QAAQ,UAGxBC,QAAS,SAAU5M,GAAV,GAC2CiP,GAG5CC,EACAC,EAJAtL,EAAOC,KAAMsL,EAAWvL,EAAKuI,QAAQS,KACzC7M,GAAQ6D,EAAKiK,QAAQ9N,GACrBiP,GAAerO,EAAMyO,SAASC,YAAYzL,EAAKgK,KAAM7N,GACjDkP,EAAeD,IAAgBpL,EAAK0L,QACpCJ,EAAgBC,IAAavL,EAAKuI,QAAQS,OAC1CqC,GAAgBC,IAChBtL,EAAKuI,QAAQO,QAAQrK,GAErB2M,IACApL,EAAKgK,KAAO7N,EACZ6D,EAAKkK,SAAWlK,EAAKuI,QAAQS,MAC7BhJ,EAAK8I,QAAQrK,IAEjBuB,EAAK0L,SAAU,GAEnBzC,MAAO,WACH,GAAyCuB,GAArCxK,EAAOC,KAAMsI,EAAUvI,EAAKuI,OAChCiC,GAAQjC,EAAQoD,KAAK,iBAChBnB,EAAM,KACPA,EAAQjQ,EAAE,8GAA8GqR,YAAYrD,IAExIvI,EAAKyK,OAASD,EAAMxJ,MAChBqI,KAAQ,SACRwC,gBAAiB7L,EAAKwI,SAAS1H,eAGvC+J,SAAU,SAAUpN,GAChB,GAAIuC,GAAOC,KAAMyH,EAAMjK,EAAEkK,QAASa,EAAWxI,EAAKwI,SAAUrM,EAAQ6D,EAAKuI,QAAQS,KAC7ER,GAASzG,MAAMyD,WAAa/H,EAAEsK,QAC9BS,EAASf,KAAKhK,GACVuC,EAAK8J,YAAcrM,EAAEqO,0BACrBrO,EAAEqO,4BAECpE,IAAQ7J,EAAKsK,OAAShM,IAAU6D,EAAKkK,SAC5ClK,EAAK+I,QAAQ5M,GAEb6D,EAAK0L,SAAU,GAGvBR,QAAS,SAAUa,EAAQ5P,GACvB,GAAI6D,GAAOC,KAAMtD,EAAUqD,EAAKrD,OAChC,OAAIR,KAAU3B,EACHmC,EAAQoP,IAEnB5P,EAAQ6D,EAAKwI,SAAStC,OAAO/J,GACxBA,IAGLA,EAAQ,GAAIf,KAAMe,IAClBQ,EAAQoP,GAAU5P,EAClB6D,EAAKwI,SAAS7L,QAAQoP,GAAU5P,EAChC6D,EAAKwI,SAASnG,QAPdlG,IASJyO,aAAc,SAAUnN,GACpBlD,EAAEkD,EAAEgI,eAAeuG,YAAY9M,EAAkB,eAAXzB,EAAE2L,OAE5Ca,QAAS,SAAU9N,GACf,GAAI6D,GAAOC,KAAMtD,EAAUqD,EAAKrD,QAAS6L,EAAWxI,EAAKwI,SAAU9N,EAAO8N,EAAStC,OAAO/J,EAW1F,OAVKD,GAAUxB,EAAMiC,EAAQP,IAAKO,EAAQN,OACtC3B,EAAO,MAEXsF,EAAK2D,OAASjJ,EACVsF,EAAK8J,YAAcpP,EACnBsF,EAAK8J,WAAW3N,MAAMzB,GAAQyB,GAE9B6D,EAAKuI,QAAQS,IAAIjM,EAAM2F,SAAShI,GAAQyB,EAAOQ,EAAQE,OAAQF,EAAQM,UAE3EuL,EAASrM,MAAMzB,GACRA,GAEXmO,SAAU,WACN,GAAyCrC,GAArCxG,EAAOC,KAAMsI,EAAUvI,EAAKuI,OAChC/B,GAAU+B,EAAQmB,QAAQ,iBACrBlD,EAAQ,KACTA,EAAU+B,EAAQ0D,KAAKjN,GAAM4G,SAASlF,SAAS,iCAC/C8F,EAAUA,EAAQyF,KAAKjN,GAAM4G,UAEjCY,EAAQ,GAAGD,MAAM2F,QAAU3D,EAAQ,GAAGhC,MAAM2F,QAC5ClM,EAAKwG,QAAUA,EAAQ9F,SAAS,yBAAyBA,SAAS6H,EAAQ,GAAG4D,WAC7E5D,EAAQlI,KACJiG,MAAO,OACPT,OAAQ0C,EAAQ,GAAGhC,MAAMV,SAE7B7F,EAAK0K,cAAgBnQ,EAAEiM,EAAQ,GAAGpE,aAEtC8G,OAAQ,WACJ,GAAIlJ,GAAOC,KAAMsI,EAAUvI,EAAKuI,QAAS6D,EAAS7D,EAAQvH,KAAK,QAASqL,EAAOD,EAAS7R,EAAE,IAAM6R,GAAU7D,EAAQ+D,QAAQ,OACtHD,GAAK,KACLrM,EAAKiL,cAAgB,WACjBjL,EAAK7D,MAAMoM,EAAQ,GAAGgE,cACtBvM,EAAK3D,IAAI2D,EAAK4I,gBAAgBvM,KAC9B2D,EAAK5D,IAAI4D,EAAK4I,gBAAgBxM,MAElC4D,EAAKgL,MAAQqB,EAAK7L,GAAG,QAASR,EAAKiL,mBAc/C5M,EAAGmO,OAAO7O,IACZC,OAAOb,MAAM0P,QACR7O,OAAOb,OACE,kBAAVzC,SAAwBA,OAAOoS,IAAMpS,OAAS,SAAUqS,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.timepicker.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.timepicker', [\n        'kendo.popup',\n        'kendo.dateinput'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'timepicker',\n        name: 'TimePicker',\n        category: 'web',\n        description: 'The TimePicker widget allows the end user to select a value from a list of predefined values or to type a new value.',\n        depends: ['popup']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, keys = kendo.keys, parse = kendo.parseDate, activeElement = kendo._activeElement, extractFormat = kendo._extractFormat, support = kendo.support, browser = support.browser, ui = kendo.ui, Widget = ui.Widget, OPEN = 'open', CLOSE = 'close', CHANGE = 'change', ns = '.kendoTimePicker', CLICK = 'click' + ns, DEFAULT = 'k-state-default', DISABLED = 'disabled', READONLY = 'readonly', LI = 'li', SPAN = '<span/>', FOCUSED = 'k-state-focused', HOVER = 'k-state-hover', HOVEREVENTS = 'mouseenter' + ns + ' mouseleave' + ns, MOUSEDOWN = 'mousedown' + ns, MS_PER_MINUTE = 60000, MS_PER_DAY = 86400000, SELECTED = 'k-state-selected', STATEDISABLED = 'k-state-disabled', ARIA_SELECTED = 'aria-selected', ARIA_EXPANDED = 'aria-expanded', ARIA_HIDDEN = 'aria-hidden', ARIA_DISABLED = 'aria-disabled', ARIA_ACTIVEDESCENDANT = 'aria-activedescendant', ID = 'id', isArray = $.isArray, extend = $.extend, proxy = $.proxy, DATE = Date, TODAY = new DATE();\n        TODAY = new DATE(TODAY.getFullYear(), TODAY.getMonth(), TODAY.getDate(), 0, 0, 0);\n        var TimeView = function (options) {\n            var that = this, id = options.id;\n            that.options = options;\n            that._dates = [];\n            that.ul = $('<ul tabindex=\"-1\" role=\"listbox\" aria-hidden=\"true\" unselectable=\"on\" class=\"k-list k-reset\"/>').css({ overflow: support.kineticScrollNeeded ? '' : 'auto' }).on(CLICK, LI, proxy(that._click, that)).on('mouseenter' + ns, LI, function () {\n                $(this).addClass(HOVER);\n            }).on('mouseleave' + ns, LI, function () {\n                $(this).removeClass(HOVER);\n            });\n            that.list = $('<div class=\\'k-list-container k-list-scroller\\' unselectable=\\'on\\'/>').append(that.ul).on(MOUSEDOWN, preventDefault);\n            if (id) {\n                that._timeViewID = id + '_timeview';\n                that._optionID = id + '_option_selected';\n                that.ul.attr(ID, that._timeViewID);\n            }\n            that._popup();\n            that._heightHandler = proxy(that._height, that);\n            that.template = kendo.template('<li tabindex=\"-1\" role=\"option\" class=\"k-item\" unselectable=\"on\">#=data#</li>', { useWithBlock: false });\n        };\n        TimeView.prototype = {\n            current: function (candidate) {\n                var that = this, active = that.options.active;\n                if (candidate !== undefined) {\n                    if (that._current) {\n                        that._current.removeClass(SELECTED);\n                        if (that._current && that._current.length) {\n                            that._current[0].removeAttribute(ID);\n                            that._current[0].removeAttribute(ARIA_SELECTED);\n                        }\n                    }\n                    if (candidate) {\n                        candidate = $(candidate).addClass(SELECTED).attr(ID, that._optionID).attr(ARIA_SELECTED, true);\n                        that.scroll(candidate[0]);\n                    }\n                    that._current = candidate;\n                    if (active) {\n                        active(candidate);\n                    }\n                } else {\n                    return that._current;\n                }\n            },\n            close: function () {\n                this.popup.close();\n            },\n            destroy: function () {\n                var that = this;\n                that.ul.off(ns);\n                that.list.off(ns);\n                that.popup.destroy();\n            },\n            open: function () {\n                var that = this;\n                var popupHovered;\n                if (!that.ul[0].firstChild) {\n                    that.bind();\n                }\n                popupHovered = that.popup._hovered;\n                that.popup._hovered = true;\n                that.popup.open();\n                setTimeout(function () {\n                    that.popup._hovered = popupHovered;\n                }, 1);\n                if (that._current) {\n                    that.scroll(that._current[0]);\n                }\n            },\n            dataBind: function (dates) {\n                var that = this, options = that.options, format = options.format, toString = kendo.toString, template = that.template, length = dates.length, idx = 0, date, html = '';\n                for (; idx < length; idx++) {\n                    date = dates[idx];\n                    if (isInRange(date, options.min, options.max)) {\n                        html += template(toString(date, format, options.culture));\n                    }\n                }\n                that._html(html);\n            },\n            refresh: function () {\n                var that = this, options = that.options, format = options.format, offset = dst(), ignoreDST = offset < 0, min = options.min, max = options.max, msMin = getMilliseconds(min), msMax = getMilliseconds(max), msLastTime = getMilliseconds(lastTimeOption(options.interval)), msInterval = options.interval * MS_PER_MINUTE, toString = kendo.toString, template = that.template, start = new DATE(+min), startDate = new DATE(start), msStart, lastIdx, idx = 0, length, html = '';\n                if (ignoreDST) {\n                    length = (MS_PER_DAY + offset * MS_PER_MINUTE) / msInterval;\n                } else {\n                    length = MS_PER_DAY / msInterval;\n                }\n                if (msMin != msMax || msLastTime === msMax) {\n                    if (msMin > msMax) {\n                        msMax += MS_PER_DAY;\n                    }\n                    length = (msMax - msMin) / msInterval + 1;\n                }\n                lastIdx = parseInt(length, 10);\n                for (; idx < length; idx++) {\n                    if (idx) {\n                        setTime(start, msInterval, ignoreDST);\n                    }\n                    if (msMax && lastIdx == idx) {\n                        msStart = getMilliseconds(start);\n                        if (startDate < start) {\n                            msStart += MS_PER_DAY;\n                        }\n                        if (msStart > msMax) {\n                            start = new DATE(+max);\n                        }\n                    }\n                    that._dates.push(getMilliseconds(start));\n                    html += template(toString(start, format, options.culture));\n                }\n                that._html(html);\n            },\n            bind: function () {\n                var that = this, dates = that.options.dates;\n                if (dates && dates[0]) {\n                    that.dataBind(dates);\n                } else {\n                    that.refresh();\n                }\n            },\n            _html: function (html) {\n                var that = this;\n                that.ul[0].innerHTML = html;\n                that.popup.unbind(OPEN, that._heightHandler);\n                that.popup.one(OPEN, that._heightHandler);\n                that.current(null);\n                that.select(that._value);\n            },\n            scroll: function (item) {\n                if (!item) {\n                    return;\n                }\n                var content = this.list[0], itemOffsetTop = item.offsetTop, itemOffsetHeight = item.offsetHeight, contentScrollTop = content.scrollTop, contentOffsetHeight = content.clientHeight, bottomDistance = itemOffsetTop + itemOffsetHeight;\n                if (contentScrollTop > itemOffsetTop) {\n                    contentScrollTop = itemOffsetTop;\n                } else if (bottomDistance > contentScrollTop + contentOffsetHeight) {\n                    contentScrollTop = bottomDistance - contentOffsetHeight;\n                }\n                content.scrollTop = contentScrollTop;\n            },\n            select: function (li) {\n                var that = this, options = that.options, current = that._current, selection;\n                if (li instanceof Date) {\n                    li = kendo.toString(li, options.format, options.culture);\n                }\n                if (typeof li === 'string') {\n                    if (!current || current.text() !== li) {\n                        li = $.grep(that.ul[0].childNodes, function (node) {\n                            return (node.textContent || node.innerText) == li;\n                        });\n                        li = li[0] ? li : null;\n                    } else {\n                        li = current;\n                    }\n                }\n                selection = that._distinctSelection(li);\n                that.current(selection);\n            },\n            _distinctSelection: function (selection) {\n                var that = this, currentValue, selectionIndex;\n                if (selection && selection.length > 1) {\n                    currentValue = getMilliseconds(that._value);\n                    selectionIndex = $.inArray(currentValue, that._dates);\n                    selection = that.ul.children()[selectionIndex];\n                }\n                return selection;\n            },\n            setOptions: function (options) {\n                var old = this.options;\n                options.min = parse(options.min);\n                options.max = parse(options.max);\n                this.options = extend(old, options, {\n                    active: old.active,\n                    change: old.change,\n                    close: old.close,\n                    open: old.open\n                });\n                this.bind();\n            },\n            toggle: function () {\n                var that = this;\n                if (that.popup.visible()) {\n                    that.close();\n                } else {\n                    that.open();\n                }\n            },\n            value: function (value) {\n                var that = this;\n                that._value = value;\n                if (that.ul[0].firstChild) {\n                    that.select(value);\n                }\n            },\n            _click: function (e) {\n                var that = this, li = $(e.currentTarget), date = li.text(), dates = that.options.dates;\n                if (dates && dates.length > 0) {\n                    date = dates[li.index()];\n                }\n                if (!e.isDefaultPrevented()) {\n                    that.select(li);\n                    that.options.change(date, true);\n                    that.close();\n                }\n            },\n            _height: function () {\n                var that = this;\n                var list = that.list;\n                var parent = list.parent('.k-animation-container');\n                var height = that.options.height;\n                if (that.ul[0].children.length) {\n                    list.add(parent).show().height(that.ul[0].scrollHeight > height ? height : 'auto').hide();\n                }\n            },\n            _parse: function (value) {\n                var that = this, options = that.options, min = getMilliseconds(options.min) != getMilliseconds(TODAY) ? options.min : null, max = getMilliseconds(options.max) != getMilliseconds(TODAY) ? options.max : null, current = that._value || min || max || TODAY;\n                if (value instanceof DATE) {\n                    return value;\n                }\n                value = parse(value, options.parseFormats, options.culture);\n                if (value) {\n                    value = new DATE(current.getFullYear(), current.getMonth(), current.getDate(), value.getHours(), value.getMinutes(), value.getSeconds(), value.getMilliseconds());\n                }\n                return value;\n            },\n            _adjustListWidth: function () {\n                var list = this.list, width = list[0].style.width, wrapper = this.options.anchor, computedStyle, computedWidth, outerWidth = kendo._outerWidth;\n                if (!list.data('width') && width) {\n                    return;\n                }\n                computedStyle = window.getComputedStyle ? window.getComputedStyle(wrapper[0], null) : 0;\n                computedWidth = computedStyle ? parseFloat(computedStyle.width) : outerWidth(wrapper);\n                if (computedStyle && (browser.mozilla || browser.msie)) {\n                    computedWidth += parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight) + parseFloat(computedStyle.borderLeftWidth) + parseFloat(computedStyle.borderRightWidth);\n                }\n                width = computedWidth - (outerWidth(list) - list.width());\n                list.css({\n                    fontFamily: wrapper.css('font-family'),\n                    width: width\n                }).data('width', width);\n            },\n            _popup: function () {\n                var that = this, list = that.list, options = that.options, anchor = options.anchor;\n                that.popup = new ui.Popup(list, extend(options.popup, {\n                    anchor: anchor,\n                    open: options.open,\n                    close: options.close,\n                    animation: options.animation,\n                    isRtl: support.isRtl(options.anchor)\n                }));\n            },\n            move: function (e) {\n                var that = this, key = e.keyCode, ul = that.ul[0], current = that._current, down = key === keys.DOWN;\n                if (key === keys.UP || down) {\n                    if (e.altKey) {\n                        that.toggle(down);\n                        return;\n                    } else if (down) {\n                        current = current ? current[0].nextSibling : ul.firstChild;\n                    } else {\n                        current = current ? current[0].previousSibling : ul.lastChild;\n                    }\n                    if (current) {\n                        that.select(current);\n                    }\n                    that.options.change(that._current.text());\n                    e.preventDefault();\n                } else if (key === keys.ENTER || key === keys.TAB || key === keys.ESC) {\n                    e.preventDefault();\n                    if (current) {\n                        that.options.change(current.text(), true);\n                    }\n                    that.close();\n                }\n            }\n        };\n        function setTime(date, time, ignoreDST) {\n            var offset = date.getTimezoneOffset(), offsetDiff;\n            date.setTime(date.getTime() + time);\n            if (!ignoreDST) {\n                offsetDiff = date.getTimezoneOffset() - offset;\n                date.setTime(date.getTime() + offsetDiff * MS_PER_MINUTE);\n            }\n        }\n        function dst() {\n            var today = new DATE(), midnight = new DATE(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0), noon = new DATE(today.getFullYear(), today.getMonth(), today.getDate(), 12, 0, 0);\n            return -1 * (midnight.getTimezoneOffset() - noon.getTimezoneOffset());\n        }\n        function getMilliseconds(date) {\n            return date.getHours() * 60 * MS_PER_MINUTE + date.getMinutes() * MS_PER_MINUTE + date.getSeconds() * 1000 + date.getMilliseconds();\n        }\n        function lastTimeOption(interval) {\n            var date = new Date(2100, 0, 1);\n            date.setMinutes(-interval);\n            return date;\n        }\n        function isInRange(value, min, max) {\n            var msMin = getMilliseconds(min), msMax = getMilliseconds(max), msValue;\n            if (!value || msMin == msMax) {\n                return true;\n            }\n            msValue = getMilliseconds(value);\n            if (msMin > msValue) {\n                msValue += MS_PER_DAY;\n            }\n            if (msMax < msMin) {\n                msMax += MS_PER_DAY;\n            }\n            return msValue >= msMin && msValue <= msMax;\n        }\n        TimeView.getMilliseconds = getMilliseconds;\n        kendo.TimeView = TimeView;\n        var TimePicker = Widget.extend({\n            init: function (element, options) {\n                var that = this, ul, timeView, disabled;\n                Widget.fn.init.call(that, element, options);\n                element = that.element;\n                options = that.options;\n                options.min = parse(element.attr('min')) || parse(options.min);\n                options.max = parse(element.attr('max')) || parse(options.max);\n                normalize(options);\n                that._initialOptions = extend({}, options);\n                that._wrapper();\n                that.timeView = timeView = new TimeView(extend({}, options, {\n                    id: element.attr(ID),\n                    anchor: that.wrapper,\n                    format: options.format,\n                    change: function (value, trigger) {\n                        if (trigger) {\n                            that._change(value);\n                        } else {\n                            element.val(value);\n                        }\n                    },\n                    open: function (e) {\n                        that.timeView._adjustListWidth();\n                        if (that.trigger(OPEN)) {\n                            e.preventDefault();\n                        } else {\n                            element.attr(ARIA_EXPANDED, true);\n                            ul.attr(ARIA_HIDDEN, false);\n                        }\n                    },\n                    close: function (e) {\n                        if (that.trigger(CLOSE)) {\n                            e.preventDefault();\n                        } else {\n                            element.attr(ARIA_EXPANDED, false);\n                            ul.attr(ARIA_HIDDEN, true);\n                        }\n                    },\n                    active: function (current) {\n                        if (element && element.length) {\n                            element[0].removeAttribute(ARIA_ACTIVEDESCENDANT);\n                        }\n                        if (current) {\n                            element.attr(ARIA_ACTIVEDESCENDANT, timeView._optionID);\n                        }\n                    }\n                }));\n                ul = timeView.ul;\n                that._icon();\n                that._reset();\n                try {\n                    element[0].setAttribute('type', 'text');\n                } catch (e) {\n                    element[0].type = 'text';\n                }\n                element.addClass('k-input').attr({\n                    'role': 'combobox',\n                    'aria-expanded': false,\n                    'aria-owns': timeView._timeViewID,\n                    'autocomplete': 'off'\n                });\n                disabled = element.is('[disabled]') || $(that.element).parents('fieldset').is(':disabled');\n                if (disabled) {\n                    that.enable(false);\n                } else {\n                    that.readonly(element.is('[readonly]'));\n                }\n                if (options.dateInput) {\n                    var min = options.min;\n                    var max = options.max;\n                    var today = new DATE();\n                    if (getMilliseconds(min) == getMilliseconds(max)) {\n                        min = new DATE(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);\n                        max = new DATE(today.getFullYear(), today.getMonth(), today.getDate(), 24, 0, 0);\n                    }\n                    that._dateInput = new ui.DateInput(element, {\n                        culture: options.culture,\n                        format: options.format,\n                        min: min,\n                        max: max,\n                        value: options.value\n                    });\n                }\n                that._old = that._update(options.value || that.element.val());\n                that._oldText = element.val();\n                kendo.notify(that);\n            },\n            options: {\n                name: 'TimePicker',\n                min: TODAY,\n                max: TODAY,\n                format: '',\n                dates: [],\n                parseFormats: [],\n                value: null,\n                interval: 30,\n                height: 200,\n                animation: {},\n                dateInput: false\n            },\n            events: [\n                OPEN,\n                CLOSE,\n                CHANGE\n            ],\n            setOptions: function (options) {\n                var that = this;\n                var value = that._value;\n                Widget.fn.setOptions.call(that, options);\n                options = that.options;\n                normalize(options);\n                that.timeView.setOptions(options);\n                if (value) {\n                    that.element.val(kendo.toString(value, options.format, options.culture));\n                }\n            },\n            dataBind: function (dates) {\n                if (isArray(dates)) {\n                    this.timeView.dataBind(dates);\n                }\n            },\n            _editable: function (options) {\n                var that = this, disable = options.disable, readonly = options.readonly, arrow = that._arrow.off(ns), element = that.element.off(ns), wrapper = that._inputWrapper.off(ns);\n                if (that._dateInput) {\n                    that._dateInput._unbindInput();\n                }\n                if (!readonly && !disable) {\n                    wrapper.addClass(DEFAULT).removeClass(STATEDISABLED).on(HOVEREVENTS, that._toggleHover);\n                    if (element && element.length) {\n                        element[0].removeAttribute(DISABLED);\n                        element[0].removeAttribute(READONLY);\n                    }\n                    element.attr(ARIA_DISABLED, false).on('keydown' + ns, proxy(that._keydown, that)).on('focusout' + ns, proxy(that._blur, that)).on('focus' + ns, function () {\n                        that._inputWrapper.addClass(FOCUSED);\n                    });\n                    if (that._dateInput) {\n                        that._dateInput._bindInput();\n                    }\n                    arrow.on(CLICK, proxy(that._click, that)).on(MOUSEDOWN, preventDefault);\n                } else {\n                    wrapper.addClass(disable ? STATEDISABLED : DEFAULT).removeClass(disable ? DEFAULT : STATEDISABLED);\n                    element.attr(DISABLED, disable).attr(READONLY, readonly).attr(ARIA_DISABLED, disable);\n                }\n            },\n            readonly: function (readonly) {\n                this._editable({\n                    readonly: readonly === undefined ? true : readonly,\n                    disable: false\n                });\n            },\n            enable: function (enable) {\n                this._editable({\n                    readonly: false,\n                    disable: !(enable = enable === undefined ? true : enable)\n                });\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.timeView.destroy();\n                that.element.off(ns);\n                that._arrow.off(ns);\n                that._inputWrapper.off(ns);\n                if (that._form) {\n                    that._form.off('reset', that._resetHandler);\n                }\n            },\n            close: function () {\n                this.timeView.close();\n            },\n            open: function () {\n                this.timeView.open();\n            },\n            min: function (value) {\n                return this._option('min', value);\n            },\n            max: function (value) {\n                return this._option('max', value);\n            },\n            value: function (value) {\n                var that = this;\n                if (value === undefined) {\n                    return that._value;\n                }\n                that._old = that._update(value);\n                if (that._old === null) {\n                    that.element.val('');\n                }\n                that._oldText = that.element.val();\n            },\n            _blur: function () {\n                var that = this, value = that.element.val();\n                that.close();\n                if (value !== that._oldText) {\n                    that._change(value);\n                }\n                that._inputWrapper.removeClass(FOCUSED);\n            },\n            _click: function () {\n                var that = this, element = that.element;\n                that.timeView.toggle();\n                if (!support.touch && element[0] !== activeElement()) {\n                    element.trigger('focus');\n                }\n            },\n            _change: function (value) {\n                var that = this, oldValue = that.element.val(), dateChanged;\n                value = that._update(value);\n                dateChanged = !kendo.calendar.isEqualDate(that._old, value);\n                var valueUpdated = dateChanged && !that._typing;\n                var textFormatted = oldValue !== that.element.val();\n                if (valueUpdated || textFormatted) {\n                    that.element.trigger(CHANGE);\n                }\n                if (dateChanged) {\n                    that._old = value;\n                    that._oldText = that.element.val();\n                    that.trigger(CHANGE);\n                }\n                that._typing = false;\n            },\n            _icon: function () {\n                var that = this, element = that.element, arrow;\n                arrow = element.next('span.k-select');\n                if (!arrow[0]) {\n                    arrow = $('<span unselectable=\"on\" class=\"k-select\" aria-label=\"select\"><span class=\"k-icon k-i-clock\"></span></span>').insertAfter(element);\n                }\n                that._arrow = arrow.attr({\n                    'role': 'button',\n                    'aria-controls': that.timeView._timeViewID\n                });\n            },\n            _keydown: function (e) {\n                var that = this, key = e.keyCode, timeView = that.timeView, value = that.element.val();\n                if (timeView.popup.visible() || e.altKey) {\n                    timeView.move(e);\n                    if (that._dateInput && e.stopImmediatePropagation) {\n                        e.stopImmediatePropagation();\n                    }\n                } else if (key === keys.ENTER && value !== that._oldText) {\n                    that._change(value);\n                } else {\n                    that._typing = true;\n                }\n            },\n            _option: function (option, value) {\n                var that = this, options = that.options;\n                if (value === undefined) {\n                    return options[option];\n                }\n                value = that.timeView._parse(value);\n                if (!value) {\n                    return;\n                }\n                value = new DATE(+value);\n                options[option] = value;\n                that.timeView.options[option] = value;\n                that.timeView.bind();\n            },\n            _toggleHover: function (e) {\n                $(e.currentTarget).toggleClass(HOVER, e.type === 'mouseenter');\n            },\n            _update: function (value) {\n                var that = this, options = that.options, timeView = that.timeView, date = timeView._parse(value);\n                if (!isInRange(date, options.min, options.max)) {\n                    date = null;\n                }\n                that._value = date;\n                if (that._dateInput && date) {\n                    that._dateInput.value(date || value);\n                } else {\n                    that.element.val(kendo.toString(date || value, options.format, options.culture));\n                }\n                timeView.value(date);\n                return date;\n            },\n            _wrapper: function () {\n                var that = this, element = that.element, wrapper;\n                wrapper = element.parents('.k-timepicker');\n                if (!wrapper[0]) {\n                    wrapper = element.wrap(SPAN).parent().addClass('k-picker-wrap k-state-default');\n                    wrapper = wrapper.wrap(SPAN).parent();\n                }\n                wrapper[0].style.cssText = element[0].style.cssText;\n                that.wrapper = wrapper.addClass('k-widget k-timepicker').addClass(element[0].className);\n                element.css({\n                    width: '100%',\n                    height: element[0].style.height\n                });\n                that._inputWrapper = $(wrapper[0].firstChild);\n            },\n            _reset: function () {\n                var that = this, element = that.element, formId = element.attr('form'), form = formId ? $('#' + formId) : element.closest('form');\n                if (form[0]) {\n                    that._resetHandler = function () {\n                        that.value(element[0].defaultValue);\n                        that.max(that._initialOptions.max);\n                        that.min(that._initialOptions.min);\n                    };\n                    that._form = form.on('reset', that._resetHandler);\n                }\n            }\n        });\n        function normalize(options) {\n            var parseFormats = options.parseFormats;\n            options.format = extractFormat(options.format || kendo.getCulture(options.culture).calendars.standard.patterns.t);\n            parseFormats = isArray(parseFormats) ? parseFormats : [parseFormats];\n            parseFormats.splice(0, 0, options.format);\n            options.parseFormats = parseFormats;\n        }\n        function preventDefault(e) {\n            e.preventDefault();\n        }\n        ui.plugin(TimePicker);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}