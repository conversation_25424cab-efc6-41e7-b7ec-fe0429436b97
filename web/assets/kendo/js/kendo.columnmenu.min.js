/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.columnmenu.min",["kendo.popup.min","kendo.filtermenu.min","kendo.menu.min"],e)}(function(){return function(e,n){function t(n){return e.trim(n).replace(/&nbsp;/gi,"")}function s(e,n){var t,s,i,l={};for(t=0,s=e.length;t<s;t++)i=e[t],l[i[n]]=i;return l}function i(e){var n,t=[];for(n=0;n<e.length;n++)e[n].columns?t=t.concat(i(e[n].columns)):t.push(e[n]);return t}function l(e,n){return"["+o.attr(e)+"='"+(n||"").replace(/'/g,'"')+"']"}function a(e,n,t){e>0?n.insertAfter(t.children().eq(e-1)):t.prepend(n)}var o=window.kendo,r=o.ui,c=e.proxy,u=e.extend,d=e.grep,m=e.map,p=e.inArray,f="k-state-selected",k="asc",h="desc",g="change",v="init",C="open",w="select",_="kendoPopup",b="kendoFilterMenu",M="kendoMenu",x=".kendoColumnMenu",S=r.Widget,y=S.extend({init:function(n,t){var s,i=this;S.fn.init.call(i,n,t),n=i.element,t=i.options,i.owner=t.owner,i.dataSource=t.dataSource,i.field=n.attr(o.attr("field")),i.title=n.attr(o.attr("title")),s=n.find(".k-header-column-menu"),s[0]||(s=n.addClass("k-with-icon").prepend('<a class="k-header-column-menu" href="#" title="'+t.messages.settings+'" aria-label="'+t.messages.settings+'"><span class="k-icon k-i-more-vertical"></span></a>').find(".k-header-column-menu")),i.link=s.attr("tabindex",-1).on("click"+x,c(i._click,i)),i.wrapper=e('<div class="k-column-menu"/>'),i._refreshHandler=c(i.refresh,i),i.dataSource.bind(g,i._refreshHandler)},_init:function(){var e=this;e.pane=e.options.pane,e.pane&&(e._isMobile=!0),e._isMobile?e._createMobileMenu():e._createMenu(),e.owner._muteAngularRebind(function(){e._angularItems("compile")}),e._sort(),e._columns(),e._filter(),e._lockColumns(),e.trigger(v,{field:e.field,container:e.wrapper})},events:[v,C,"sort","filtering"],options:{name:"ColumnMenu",messages:{sortAscending:"Sort Ascending",sortDescending:"Sort Descending",filter:"Filter",column:"Column",columns:"Columns",columnVisibility:"Column Visibility",clear:"Clear",cancel:"Cancel",done:"Done",settings:"Edit Column Settings",lock:"Lock",unlock:"Unlock"},filter:"",columns:!0,sortable:!0,filterable:!0,animations:{left:"slide"}},_createMenu:function(){var e=this,n=e.options;e.wrapper.html(o.template(H)({uid:o.guid(),ns:o.ns,messages:n.messages,sortable:n.sortable,filterable:n.filterable,columns:e._ownerColumns(),showColumns:n.columns,lockedColumns:n.lockedColumns})),e.popup=e.wrapper[_]({anchor:e.link,open:c(e._open,e),activate:c(e._activate,e),close:function(){e.options.closeCallback&&e.options.closeCallback(e.element)}}).data(_),e.menu=e.wrapper.children()[M]({orientation:"vertical",closeOnClick:!1,open:function(){e._updateMenuItems()}}).data(M)},_createMobileMenu:function(){var e,n=this,t=n.options,s=o.template(I)({ns:o.ns,field:n.field,title:n.title||n.field,messages:t.messages,sortable:t.sortable,filterable:t.filterable,columns:n._ownerColumns(),showColumns:t.columns,lockedColumns:t.lockedColumns});n.view=n.pane.append(s),n.view.state={columns:{}},n.wrapper=n.view.element.find(".k-column-menu"),n.menu=new L(n.wrapper.children(),{pane:n.pane,columnMenu:n}),n.menu.element.on("transitionend"+x,function(e){e.stopPropagation()}),e=n.view.wrapper&&n.view.wrapper[0]?n.view.wrapper:n.view.element,e.on("click",".k-header-done",function(e){e.preventDefault(),n.menu._applyChanges(),n.menu._cancelChanges(!1),n.close()}),e.on("click",".k-header-cancel",function(e){e.preventDefault(),n.menu._cancelChanges(!0),n.close()}),n.view.bind("show",function(){var e=n.view||{columns:{}};n.options.lockedColumns&&n._updateLockedColumns(),e.element.find(".k-sort-asc.k-state-selected").length?e.state.initialSort="asc":e.element.find(".k-sort-desc.k-state-selected").length&&(e.state.initialSort="desc")})},_angularItems:function(n){var t=this;t.angular(n,function(){var n=t.wrapper.find(".k-columns-item input["+o.attr("field")+"]").map(function(){return e(this).closest("li")}),s=m(t._ownerColumns(),function(e){return{column:e._originalObject}});return{elements:n,data:s}})},destroy:function(){var e=this;e._angularItems("cleanup"),S.fn.destroy.call(e),e.filterMenu&&e.filterMenu.destroy(),e._refreshHandler&&e.dataSource.unbind(g,e._refreshHandler),e.options.columns&&e.owner&&(e._updateColumnsMenuHandler&&(e.owner.unbind("columnShow",e._updateColumnsMenuHandler),e.owner.unbind("columnHide",e._updateColumnsMenuHandler)),e._updateColumnsLockedStateHandler&&(e.owner.unbind("columnLock",e._updateColumnsLockedStateHandler),e.owner.unbind("columnUnlock",e._updateColumnsLockedStateHandler))),e.menu&&(e.menu.element.off(x),e.menu.destroy()),e.wrapper.off(x),e.popup&&e.popup.destroy(),e.view&&e.view.purge(),e.link.off(x),e.owner=null,e.wrapper=null,e.element=null},close:function(){this.menu.close(),this.popup&&(this.popup.close(),this.popup.element.off("keydown"+x))},_click:function(e){var n,t=this;e.preventDefault(),e.stopPropagation(),n=this.options,n.filter&&this.element.is(!n.filter)||(this.popup||this.pane?t._updateMenuItems():this._init(),this._isMobile?this.pane.navigate(this.view,this.options.animations.left):this.popup.toggle())},_updateMenuItems:function(){var e=this;e._setMenuItemsVisibility(),e._reorderMenuItems()},_setMenuItemsVisibility:function(){var e=this;e._eachRenderedMenuItem(function(e,n,t){n.matchesMedia===!1?t.hide():t.show()})},_reorderMenuItems:function(){var e=this;e._eachRenderedMenuItem(function(e,n,t,s){t[0]&&t.index()!==e&&a(e,t,s)})},_eachRenderedMenuItem:function(n){var s,a,o,r,c,u,m=this,p=d(i(m.owner.columns),function(e){var n=!0,s=t(e.title||"");return e.menu!==!1&&(e.field||s.length)||(n=!1),n}).map(function(e){return{field:e.field,title:e.title,matchesMedia:e.matchesMedia}}),f=m._isMobile&&m.view?e(m.view.element).find(".k-columns-item").children("ul"):e(m.wrapper).find(".k-menu-group").first(),k=function(n,t,s){return n.find(t).filter(function(){return h(p[s],e(this).text())})},h=function(e,n){return g(e,n)},g=function(e,n){return e.title?n===e.title:n===e.field},v=function(e){return d(p,function(n){return JSON.stringify(p[e])==JSON.stringify(n)})};for(u=0;u<p.length;u++)c=p[u],a=v(u),o=e.inArray(c,a),s=k(f,"span",u),s=this._isMobile?s.next():s,r=c.field?c.field:c.title,s=s.find(l("field",r)).closest("li").eq(o),n(u,c,s,f)},_open:function(){var n=this;e(".k-column-menu").not(n.wrapper).each(function(){e(this).data(_).close()}),n.popup.element.on("keydown"+x,function(e){e.keyCode==o.keys.ESC&&n.close()}),n.options.lockedColumns&&n._updateLockedColumns()},_activate:function(){this.menu.element.focus(),this.trigger(C,{field:this.field,container:this.wrapper})},_ownerColumns:function(){var e=i(this.owner.columns),n=d(e,function(e){var n=!0,s=t(e.title||"");return e.menu!==!1&&(e.field||s.length)||(n=!1),n});return m(n,function(n){return{originalField:n.field,field:n.field||n.title,title:n.title||n.field,hidden:n.hidden,matchesMedia:n.matchesMedia,index:p(n,e),locked:!!n.locked,_originalObject:n}})},_sort:function(){var n=this;n.options.sortable&&(n.refresh(),n.menu.bind(w,function(t){var s,i=e(t.item);i.hasClass("k-sort-asc")?s=k:i.hasClass("k-sort-desc")&&(s=h),s&&(i.parent().find(".k-sort-"+(s==k?h:k)).removeClass(f),n._sortDataSource(i,s),n._isMobile||n.close())}))},_sortDataSource:function(e,t){var s,i,l=this,a=l.options.sortable,o=null===a.compare?n:a.compare,r=l.dataSource,c=r.sort()||[],u=e.hasClass(f)&&a&&a.allowUnsort!==!1;if(t=u?n:t,!l.trigger("sort",{sort:{field:l.field,dir:t,compare:o}})){if(u?e.removeClass(f):e.addClass(f),"multiple"===a.mode){for(s=0,i=c.length;s<i;s++)if(c[s].field===l.field){c.splice(s,1);break}c.push({field:l.field,dir:t,compare:o})}else c=[{field:l.field,dir:t,compare:o}];r.sort(c)}},_columns:function(){var n=this;n.options.columns&&(n._updateColumnsMenu(),n._updateColumnsMenuHandler=c(n._updateColumnsMenu,n),n.owner.bind(["columnHide","columnShow"],n._updateColumnsMenuHandler),n._updateColumnsLockedStateHandler=c(n._updateColumnsLockedState,n),n.owner.bind(["columnUnlock","columnLock"],n._updateColumnsLockedStateHandler),n.menu.bind(w,function(s){var l,a,o=e(s.item),r=d(i(n.owner.columns),function(e){var n=!0,s=t(e.title||"");return e.menu!==!1&&(e.field||s.length)||(n=!1),n});n._isMobile&&s.preventDefault(),o.parent().closest("li.k-columns-item")[0]&&(l=o.find(":checkbox"),l.attr("disabled")||(a=r[o.index()],a.hidden===!0?n.owner.showColumn(a):n.owner.hideColumn(a)))}))},_updateColumnsMenu:function(){var e,n,s,l,a,r,c,u=o.attr("field"),m=o.attr("locked"),p=d(i(this.owner.columns),function(e){var n=!0,s=t(e.title||"");return e.menu!==!1&&(e.field||s.length)||(n=!1),n}),f=d(this._ownerColumns(),function(e){return!e.hidden&&e.matchesMedia!==!1}),k=d(f,function(e){return e.originalField}),h=d(k,function(e){return e.locked===!0}).length,g=d(k,function(e){return e.locked!==!0}).length,v=d(this.owner.columns,function(e){return e.menu===!1}),C=d(v,function(e){return e.hidden});for(this.wrapper.find("[role='menuitemcheckbox']").attr("aria-checked",!1),r=this.wrapper.find(".k-columns-item input["+u+"]").prop("disabled",!1).prop("checked",!1),e=0,n=r.length;e<n;e++)s=r.eq(e),a="true"===s.attr(m),l=!1,c=s.data("kendoSwitch"),l=!p[e].hidden&&p[e].matchesMedia!==!1,s.prop("checked",l),c&&(c.enable(!0),c.check(l)),s.closest("[role='menuitemcheckbox']").attr("aria-checked",l),l&&(1==h&&a&&(s.prop("disabled",!0),c&&c.enable(!1)),0!==v.length&&v.length!==C.length||1!=g||a||(s.prop("disabled",!0),c&&c.enable(!1)))},_updateColumnsLockedState:function(){var e,n,t,i,l=o.attr("field"),a=o.attr("locked"),r=s(this._ownerColumns(),"field"),c=this.wrapper.find(".k-columns-item input[type=checkbox]");for(e=0,n=c.length;e<n;e++)t=c.eq(e),i=r[t.attr(l)],i&&t.attr(a,i.locked);this._updateColumnsMenu()},_filter:function(){var n=this,t=b,s=n.options;s.filterable!==!1&&(s.filterable.multi&&(t="kendoFilterMultiCheck",s.filterable.dataSource&&(s.filterable.checkSource=s.filterable.dataSource,delete s.filterable.dataSource)),n.filterMenu=n.wrapper.find(".k-filterable")[t](u(!0,{},{appendToElement:!0,dataSource:s.dataSource,values:s.values,field:n.field,title:n.title,change:function(e){n.trigger("filtering",{filter:e.filter,field:e.field})&&e.preventDefault()}},s.filterable)).data(t),n._isMobile&&n.menu.bind(w,function(t){var s=e(t.item);s.hasClass("k-filter-item")&&n.pane.navigate(n.filterMenu.view,n.options.animations.left)}))},_lockColumns:function(){var n=this;n.menu.bind(w,function(t){var s=e(t.item);s.hasClass("k-lock")?(n.owner.lockColumn(n.field),n._isMobile||n.close()):s.hasClass("k-unlock")&&(n.owner.unlockColumn(n.field),n._isMobile||n.close())})},_updateLockedColumns:function(){var e,n,t,s,i=this.field,l=this.owner.columns,a=d(l,function(e){return e.field==i||e.title==i})[0];a&&(e=a.locked===!0,n=d(l,function(n){return!n.hidden&&(n.locked&&e||!n.locked&&!e)}).length,t=this.wrapper.find(".k-lock").removeClass("k-state-disabled"),s=this.wrapper.find(".k-unlock").removeClass("k-state-disabled"),(e||1==n)&&t.addClass("k-state-disabled"),e&&1!=n||s.addClass("k-state-disabled"),this._updateColumnsLockedState())},refresh:function(){var e,n,t,s=this,i=s.options.dataSource.sort()||[],l=s.field;for(s.wrapper.find(".k-sort-asc, .k-sort-desc").removeClass(f),n=0,t=i.length;n<t;n++)e=i[n],l==e.field&&s.wrapper.find(".k-sort-"+e.dir).addClass(f);s.link[s._filterExist(s.dataSource.filter())?"addClass":"removeClass"]("k-state-active")},_filterExist:function(e){var n,t,s,i=!1;if(e){for(e=e.filters,t=0,s=e.length;t<s;t++)n=e[t],n.field==this.field?i=!0:n.filters&&(i=i||this._filterExist(n));return i}}}),H='<ul id="#=uid#">#if(sortable){#<li class="k-item k-sort-asc"><span class="k-link"><span class="k-icon k-i-sort-asc-sm"></span>${messages.sortAscending}</span></li><li class="k-item k-sort-desc"><span class="k-link"><span class="k-icon k-i-sort-desc-sm"></span>${messages.sortDescending}</span></li>#if(showColumns || filterable){#<li class="k-separator" role="presentation"></li>#}##}##if(showColumns){#<li class="k-item k-columns-item" aria-haspopup="true"><span class="k-link"><span class="k-icon k-i-columns"></span>${messages.columns}</span><ul>#for (var idx = 0; idx < columns.length; idx++) {#<li role="menuitemcheckbox" aria-checked="false" #=columns[idx].matchesMedia === false ? "style=\'display:none;\'" : ""#><input type="checkbox" title="#=columns[idx].title#" data-#=ns#field="#=columns[idx].field.replace(/"/g,"&\\#34;")#" data-#=ns#index="#=columns[idx].index#" data-#=ns#locked="#=columns[idx].locked#"/>#=columns[idx].title#</li>#}#</ul></li>#if(filterable || lockedColumns){#<li class="k-separator" role="presentation"></li>#}##}##if(filterable){#<li class="k-item k-filter-item" aria-haspopup="true"><span class="k-link"><span class="k-icon k-i-filter"></span>${messages.filter}</span><ul><li><div class="k-filterable"></div></li></ul></li>#if(lockedColumns){#<li class="k-separator" role="presentation"></li>#}##}##if(lockedColumns){#<li class="k-item k-lock"><span class="k-link"><span class="k-icon k-i-lock"></span>${messages.lock}</span></li><li class="k-item k-unlock"><span class="k-link"><span class="k-icon k-i-unlock"></span>${messages.unlock}</span></li>#}#</ul>',I='<div data-#=ns#role="view" class="k-grid-column-menu"><div data-#=ns#role="header" class="k-header"><a href="\\#" class="k-header-cancel k-link" title="#=messages.cancel#" aria-label="#=messages.cancel#"><span class="k-icon k-i-arrow-chevron-left"></span></a>${messages.settings}<a href="\\#" class="k-header-done k-link" title="#=messages.done#" aria-label="#=messages.done#"><span class="k-icon k-i-check"></span></a></div><div class="k-column-menu k-mobile-list"><ul><li><span class="k-list-title">#=messages.column#: ${title}</span><ul>#if(sortable){#<li id="#=kendo.guid()#" class="k-item k-sort-asc"><span class="k-link"><span class="k-icon k-i-sort-asc-sm"></span><span class="k-item-title">${messages.sortAscending}</span></span></li><li id="#=kendo.guid()#" class="k-item k-sort-desc"><span class="k-link"><span class="k-icon k-i-sort-desc-sm"></span><span class="k-item-title">${messages.sortDescending}</span></span></li>#}##if(lockedColumns){#<li id="#=kendo.guid()#" class="k-item k-lock"><span class="k-link"><span class="k-icon k-i-lock"></span><span class="k-item-title">${messages.lock}</span></span></li><li id="#=kendo.guid()#" class="k-item k-unlock"><span class="k-link"><span class="k-icon k-i-unlock"></span><span class="k-item-title">${messages.unlock}</span></span></li>#}##if(filterable){#<li id="#=kendo.guid()#" class="k-item k-filter-item"><span class="k-link k-filterable"><span class="k-icon k-i-filter"></span><span class="k-item-title">${messages.filter}</span></span></li>#}#</ul></li>#if(showColumns){#<li class="k-columns-item"><span class="k-list-title">${messages.columnVisibility}</span><ul>#for (var idx = 0; idx < columns.length; idx++) {#<li id="#=kendo.guid()#" class="k-item"><span class="k-item-title">#=columns[idx].title#</span><input type="checkbox" title="#=columns[idx].title#"  data-#=ns#field="#=columns[idx].field.replace(/"/g,"&\\#34;")#" data-#=ns#index="#=columns[idx].index#" data-#=ns#locked="#=columns[idx].locked#"/></li>#}#</ul></li>#}#<li class="k-item k-clear-wrap"><span class="k-label k-clear" title="#=messages.clear#" aria-label="#=messages.clear#">#=messages.clear#</span></li></ul></div></div>',L=S.extend({init:function(e,n){var t=this;S.fn.init.call(t,e,n),t._createCheckBoxes(),t.element.on("click"+x,"li.k-item:not(.k-separator):not(.k-state-disabled):not(:has(.k-switch))","_click")},events:[w],_click:function(t){var s=this;return e(t.target).is("[type=checkbox]")||t.preventDefault(),e(t.target).hasClass("k-clear")?(s._cancelChanges(!0),n):e(t.target).hasClass("k-filterable")?(s._cancelChanges(!0),s.trigger(w,{item:t.currentTarget}),n):(s._updateSelectedItems(t.currentTarget),n)},_updateSelectedItems:function(n){var t,s,i,l=this,a=e(n),o=l.options.columnMenu.view.state||{columns:{}},r=a.prop("id");a.hasClass("k-filter-item")||(o[r]=!o[r],(a.hasClass("k-sort-asc")||a.hasClass("k-sort-desc"))&&(a.hasClass("k-sort-asc")?(t="asc",s=l.element.find(".k-sort-desc")):(t="desc",s=l.element.find(".k-sort-asc")),i=s.prop("id"),t!==o.initialSort||a.hasClass("k-state-selected")||(o[r]=!1),o[i]&&(o[i]=!1),s.removeClass(f)),a.hasClass(f)?a.removeClass(f):a.addClass(f))},_cancelChanges:function(e){var n,t,s,i,l=this,a=l.options.columnMenu,o=a.view,r=o.state||{columns:{}},c=r.columns;if(l.element.find("."+f).removeClass(f),a.refresh(),e){n=[];for(t in c)c.hasOwnProperty(t)&&c[t]===!0&&(s=o.element.find("#"+t),n.push(s[0]));for(i=n.length-1;i>=0;i--)l.trigger(w,{item:n[i]});a.options.lockedColumns&&a._updateLockedColumns()}l.options.columnMenu.view.state={columns:{}}},_applyChanges:function(){var e,n,t=this,s=t.options.columnMenu.view,i=s.state||{columns:{}};for(e in i)i.hasOwnProperty(e)&&"initialSort"!==e&&"columns"!==e&&i[e]===!0&&(n=s.element.find("#"+e),n.hasClass(f)?n.removeClass(f):n.addClass(f),t.trigger(w,{item:n[0]}))},_createCheckBoxes:function(){var e=this;e.element.find(".k-columns-item").find("[type='checkbox']").kendoSwitch({messages:{checked:"",unchecked:""},change:function(n){var t=n.sender.element.closest(".k-item"),s=e.options.columnMenu.view.state||{columns:{}},i=t.prop("id");s.columns[i]=!s.columns[i],e.trigger(w,{item:t})}})},_destroyCheckBoxes:function(){var e,n,t=this,s=t.element.find(".k-columns-item").find("[type='checkbox']");for(n=0;n<s.length;n++)e=s.eq(n).data("kendoSwitch"),e&&e.destroy()},close:function(){this.options.pane.navigate("")},destroy:function(){var e=this;S.fn.destroy.call(e),e.element.off(x),e._destroyCheckBoxes()}});r.plugin(y)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,n,t){(t||n)()});
//# sourceMappingURL=kendo.columnmenu.min.js.map
