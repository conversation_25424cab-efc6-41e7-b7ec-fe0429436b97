{"version": 3, "sources": ["kendo.core.js"], "names": ["f", "define", "$", "window", "undefined", "Class", "compilePart", "part", "stringPart", "split", "join", "replace", "first", "char<PERSON>t", "rest", "substring", "pad", "number", "digits", "end", "length", "zeros", "getShadows", "element", "shadow", "css", "kendo", "support", "transitions", "radius", "match", "boxShadowRegExp", "blur", "math", "max", "left", "right", "bottom", "wrap", "autosize", "percentage", "width", "height", "percentWidth", "percentHeight", "forceWidth", "browser", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "parent", "windowOuterWidth", "removeClass", "hasClass", "wrapResize", "style", "percentRegExp", "test", "is", "addClass", "boxSizing", "mozBoxSizing", "webkitBoxSizing", "msie", "floor", "version", "zoom", "children", "wrapper", "wrapperStyle", "display", "position", "deepExtend", "destination", "i", "arguments", "deepExtendOne", "source", "property", "propValue", "propType", "propInit", "destProp", "ObservableArray", "data", "LazyObservableArray", "DataSource", "HierarchicalDataSource", "OBJECT", "constructor", "Array", "RegExp", "Date", "getTime", "isFunction", "clone", "UNDEFINED", "testRx", "agent", "rxs", "dflt", "rx", "hasOwnProperty", "toHyphens", "str", "g", "toLowerCase", "toCamelCase", "strMatch", "g1", "toUpperCase", "getComputedStyles", "properties", "computedStyle", "styles", "document", "defaultView", "getComputedStyle", "each", "idx", "value", "getPropertyValue", "currentStyle", "size", "isScrollable", "className", "indexOf", "overflow", "scrollLeft", "isRtl", "webkit", "mozila", "mozilla", "el", "scrollWidth", "clientWidth", "Math", "abs", "obj", "key", "result", "getOffset", "type", "positioned", "offset", "sign", "top", "pointers", "msPointers", "pageYOffset", "documentElement", "scrollTop", "pageXOffset", "parseEffects", "input", "effects", "this", "fx", "Element", "prepareAnimationOptions", "options", "duration", "reverse", "complete", "STRING", "BOOLEAN", "extend", "init", "noop", "teardown", "hide", "completeCallback", "animate", "instance", "queue", "promise", "toggleClass", "classes", "add", "htmlEncode", "ampRegExp", "ltRegExp", "gtRegExp", "quoteRegExp", "aposRegExp", "parseOption", "option", "dashRegExp", "getAttribute", "ns", "numberRegExp", "parseFloat", "jsonRegExp", "jsonFormatRegExp", "Function", "parseOptions", "role", "templateRegExp", "template", "html", "containmentComparer", "a", "b", "contains", "resizableWidget", "widget", "inArray", "attr", "focusable", "isTabIndexNotNaN", "nodeName", "disabled", "href", "visible", "expr", "pseudos", "parents", "addBack", "filter", "kendoJ<PERSON>uery", "selector", "context", "fn", "Template", "preventDefault", "isDefaultPrevented", "Observable", "argumentNameRegExp", "encodeRegExp", "escapedCurlyRegExp", "curlyRegExp", "escapedSharpRegExp", "sharpRegExp", "directions", "eventTarget", "wrapExpression", "localUrlRe", "Widget", "DataBoundWidget", "ContainerNullObject", "MobileWidget", "MOUSE_EVENTS", "EXCLUDE_BUST_CLICK_SELECTOR", "MouseEventNormalizer", "eventMap", "getEventMap", "eventRegEx", "on", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animationFrame", "animationQueue", "cultures", "isArray", "proxy", "JSON", "formatRegExp", "FUNCTION", "NUMBER", "NULL", "getter<PERSON>ache", "setter<PERSON><PERSON>", "slice", "noDepricateExtend", "src", "copyIsArray", "copy", "name", "target", "deep", "j<PERSON><PERSON><PERSON>", "isPlainObject", "proto", "member", "base", "that", "subclass", "apply", "prototype", "Object", "_initOptions", "_defaultPrevented", "_events", "bind", "eventName", "handlers", "one", "original", "handler", "events", "eventNames", "handlersIsFunction", "unbind", "push", "unshift", "trigger", "e", "sender", "call", "splice", "paramName", "useWithBlock", "render", "compile", "parts", "settings", "argumentName", "functionBody", "_slotCount", "Error", "format", "quote", "string", "escapable", "lastIndex", "c", "meta", "charCodeAt", "toString", "holder", "k", "v", "partial", "mind", "gap", "toJSON", "rep", "isFinite", "String", "indent", "\b", "\t", "\n", "\f", "\r", "\"", "\\", "valueOf", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "Number", "Boolean", "stringify", "replacer", "space", "", "findCulture", "culture", "numberFormat", "getCulture", "current", "formatDate", "date", "calendar", "calendars", "standard", "days", "months", "patterns", "dateFormatRegExp", "minutes", "getDate", "namesAbbr", "getDay", "names", "getMonth", "getFullYear", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "AM", "PM", "getTimezoneOffset", "formatNumber", "decimal", "precision", "pattern", "literals", "symbol", "isCurrency", "isPercent", "customPrecision", "formatAndPrecision", "negative", "integer", "fraction", "integerLength", "fractionLength", "replacement", "ch", "hasGroup", "hasNegativeFormat", "decimalIndex", "sharpIndex", "zeroIndex", "hasZero", "has<PERSON>harp", "percentIndex", "currencyIndex", "startZeroIndex", "start", "exp", "rounded", "POINT", "decimals", "EMPTY", "toLocaleString", "standardFormatRegExp", "exec", "currency", "percent", "toExponential", "round", "groupInteger", "literalRegExp", "quoteChar", "literal", "PLACEHOLDER", "SHARP", "ZERO", "COMMA", "commaRegExp", "lastIndexOf", "EN", "objectToString", ",", ".", "groupSize", "abbr", "namesShort", "d", "D", "F", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "twoDigitYearMax", "cultureName", "newGroupSize", "groupSizes", "shift", "toFixed", "min", "fmt", "values", "index", "placeholderFormat", "parseInt", "_extractFormat", "_activeElement", "activeElement", "_round", "<PERSON><PERSON><PERSON><PERSON>", "outOfRange", "designatorPredicate", "designator", "mapDesignators", "designators", "map", "adjustDST", "hours", "setHours", "lowerArray", "array", "lowerLocalInfo", "localInfo", "newLocalInfo", "parseExact", "strict", "count", "pmHour", "UTC", "matches", "amDesignators", "pmDesignators", "hoursOffset", "minutesOffset", "hasTime", "lookAhead", "getNumber", "rg", "substr", "valueIdx", "getIndexByName", "lower", "name<PERSON><PERSON><PERSON>", "subValue", "matchLength", "matchIdx", "checkLiteral", "year", "month", "day", "seconds", "milliseconds", "defaultYear", "_lowerDays", "_lowerMonths", "longTimeZoneRegExp", "shortTimeZoneRegExp", "isNaN", "setFullYear", "parseMicrosoftFormatOffset", "getDefaultFormats", "cultureFormats", "formatIdx", "FORMATS_SEQUENCE", "STANDARD_FORMATS", "formats", "concat", "internalParseDate", "tzoffset", "dateRegExp", "offsetRegExp", "timezone", "convert", "nonBreakingSpaceRegExp", "exponentRegExp", "2", "3", "4", "parseDate", "parseExactDate", "percentSymbol", "table", "docStyle", "transforms", "elementProto", "mobileOS", "documentMode", "chrome", "_scrollbar", "scrollbar", "refresh", "div", "createElement", "cssText", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "<PERSON><PERSON><PERSON><PERSON>", "closest", "tbodyInnerHtml", "touch", "HTMLElement", "hasHW3D", "WebKitCSSMatrix", "cssFlexbox", "lowPrefix", "prefix", "hasTransitions", "event", "devicePixelRatio", "screenWidth", "screen", "availWidth", "innerWidth", "screenHeight", "availHeight", "innerHeight", "detectOS", "ua", "minorVersion", "os", "notAndroidPhone", "agentRxs", "wp", "fire", "android", "iphone", "ipad", "meego", "webos", "blackberry", "playbook", "windows", "tizen", "sailfish", "ffos", "osRxs", "ios", "flat", "formFactorRxs", "tablet", "browserRxs", "omini", "omobile", "firefox", "mobilesafari", "ie", "navigator", "device", "majorVersion", "flatVersion", "<PERSON><PERSON>", "PhoneGap", "appMode", "standalone", "location", "protocol", "userAgent", "wpDevicePixelRatio", "hasNativeScrolling", "delayedClick", "mouseAndTouchPresent", "detectBrowser", "edge", "safari", "opera", "detectClipboardAccess", "commands", "queryCommandSupported", "cut", "paste", "clipboard", "zoomLevel", "ie11WidthCorrection", "docEl", "scrollHeight", "clientHeight", "cssBorderSpacing", "borderSpacing", "cssClass", "doc<PERSON><PERSON>", "eventCapture", "addEventListener", "placeholder", "propertyChangeEvent", "types", "setAttribute", "cssFloat", "stableSort", "threshold", "sorted", "field", "sort", "matchesSelector", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "nodeList", "querySelectorAll", "parentNode", "matchMedia", "pushState", "history", "hashChange", "customElements", "MSPointerEvent", "PointerEvent", "kineticScrollNeeded", "down", "up", "in", "out", "enabled", "dequeue", "disable", "promiseShim", "enable", "animatedPromise", "kendoStop", "clearQueue", "gotoEnd", "stop", "kendoAnimate", "kendoAddClass", "kendoRemoveClass", "kendoToggleClass", "toggle", "touches", "originalEvent", "changedTouches", "elementFromPoint", "clientX", "clientY", "callback", "mousedown", "mouseup", "mousemove", "mousecancel", "click", "resize", "members", "widgets", "_widgetRegisteredCallbacks", "ui", "mobile", "dataviz", "drawing", "spreadsheet", "messages", "keys", "INSERT", "DELETE", "BACKSPACE", "TAB", "ENTER", "ESC", "LEFT", "UP", "RIGHT", "DOWN", "END", "HOME", "SPACEBAR", "PAGEUP", "PAGEDOWN", "F2", "F10", "F12", "NUMPAD_PLUS", "NUMPAD_MINUS", "NUMPAD_DOT", "isLocalUrl", "url", "expression", "safe", "getter", "setter", "accessor", "get", "set", "guid", "random", "id", "roleSelector", "directiveSelector", "directives", "selectors", "triggeredByInput", "tagName", "onWidgetRegistered", "len", "logToConsole", "message", "console", "suppressLog", "log", "dataSource", "angular", "_hasB<PERSON>ingTarget", "kendoBindingTarget", "_tabindex", "TABINDEX", "tabindex", "removeAttr", "setOptions", "_setEvents", "force", "getSize", "currentSize", "_size", "_resize", "dimensions", "setSize", "destroy", "removeData", "_destroy", "_muteAngularRebind", "_mute<PERSON><PERSON><PERSON>", "dataItems", "flatView", "_angularItems", "cmd", "elements", "items", "dataItem", "dom<PERSON>lement", "offsetHeight", "notify", "initWidget", "roles", "fullPath", "widgetKeyRegExp", "widget<PERSON>ey", "nodeType", "isEmptyObject", "rolesFromNamespaces", "namespaces", "find", "widgetsArray", "makeArray", "widgetInstance", "progress", "container", "leftRight", "webkitCorrection", "containerScrollLeft", "mask", "opacity", "loading", "prependTo", "remove", "plugin", "register", "widgetEntry", "args", "method", "nullObject", "autoApplyNS", "kendoD<PERSON>roy", "view", "viewElement", "viewHasNativeScrolling", "useNativeScrolling", "eq", "appLevelNativeScrolling", "application", "themes", "views", "touchScroller", "useNative", "<PERSON><PERSON><PERSON>", "kendoMobileScroller", "suites", "editorT<PERSON>bar", "onResize", "setTimeout", "unbindResize", "off", "attrValue", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "kendoFocusable", "setupMouseMute", "mouseTrap", "bustClick", "captureMouse", "stopPropagation", "muteMouse", "clearTimeout", "mouseTrapTimeoutID", "unMuteMouse", "move", "cancel", "MSPointerEnter", "MSPointerLeave", "orig", "fix", "special", "delegateType", "bindType", "handle", "ret", "related", "relatedTarget", "handleObj", "origType", "applyEventMap", "pop", "search", "touchstart", "touchend", "ruleToDate", "rule", "targetDay", "ourDay", "time", "cache", "getUTCDay", "setUTCDate", "findRule", "utcTime", "rules", "zone", "grep", "from", "to", "findZone", "zones", "until", "zoneRules", "zoneAndRule", "info", "fromOffset", "toOffset", "diff", "fromLocalOffset", "toLocalOffset", "tempToOffset", "toLocalDate", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "setDayOfWeek", "dir", "setDate", "dayOfWeek", "firstDayOfMonth", "lastDayOfMonth", "last", "timeOffset", "moveDateToWeekStart", "weekStartDay", "addDays", "calcWeekInYear", "firstWeekInYear", "newDate", "diffInMS", "MS_PER_DAY", "weekInYear", "prevWeekDate", "nextWeekDate", "weekNumber", "toUtcTime", "toInvariantTime", "isInTimeRange", "msValue", "msMin", "msMax", "isInDateRange", "setTime", "ignoreDST", "difference", "MS_PER_MINUTE", "today", "isToday", "staticDate", "nextDay", "previousDay", "MS_PER_HOUR", "stripWhitespace", "iterator", "child", "createNodeIterator", "Node<PERSON><PERSON><PERSON>", "SHOW_TEXT", "node", "FILTER_ACCEPT", "FILTER_REJECT", "nextNode", "referenceNode", "textContent", "trim", "childNodes", "nodeValue", "requestAnimationFrame", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "oRequestAnimationFrame", "msRequestAnimationFrame", "queueAnimation", "runNextAnimation", "parseQueryStringParams", "queryString", "params", "paramParts", "decodeURIComponent", "elementUnderCursor", "x", "client", "wheelDeltaY", "jQueryEvent", "delta", "deltaY", "wheelDelta", "detail", "axis", "VERTICAL_AXIS", "throttle", "delay", "timeout", "throttled", "lastExecTime", "elapsed", "caret", "rangeElement", "rangeDuplicated", "selectionStart", "selectionEnd", "isPosition", "focus", "setSelectionRange", "selection", "createTextRange", "collapse", "moveStart", "moveEnd", "select", "duplicate", "moveToBookmark", "createRange", "getBookmark", "setEndPoint", "text", "compileMobileDirective", "scope", "injector", "invoke", "$compile", "$$phase", "$digest", "antiForgeryTokens", "tokens", "csrf_token", "csrf_param", "cycleForm", "form", "firstElement", "lastElement", "keyCode", "shift<PERSON>ey", "focusElement", "scrollTopPositions", "scrollableParents", "parentsUntil", "setActive", "matchesMedia", "mediaQuery", "media", "_bootstrapToMedia", "bootstrapMedia", "xs", "sm", "md", "lg", "xl", "postToProxy", "dataURI", "fileName", "proxyURL", "proxyTarget", "action", "contentType", "base64", "appendTo", "submit", "saveAsBlob", "blob", "atob", "Uint8Array", "Blob", "buffer", "msSaveBlob", "saveAsDataURI", "URL", "createObjectURL", "fileSaver", "download", "createEvent", "initMouseEvent", "dispatchEvent", "revokeObjectURL", "downloadAttribute", "saveAs", "save", "forceProxy", "proxyModelSetters", "observable", "for<PERSON>ach", "defineProperty", "dirty", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,cAAe,UAAWD,IACnC,WAw5GE,MAj5GC,UAAUE,EAAGC,EAAQC,GA2ClB,QAASC,MA+GT,QAASC,GAAYC,EAAMC,GACvB,GAAIA,EACA,MAAO,IAAOD,EAAKE,MAAM,KAAMC,KAAK,OAAQD,MAAM,OAAOC,KAAK,WAAWC,QAAQ,MAAO,OAAOA,QAAQ,MAAO,OAAOA,QAAQ,MAAO,OAAS,GAE7I,IAAIC,GAAQL,EAAKM,OAAO,GAAIC,EAAOP,EAAKQ,UAAU,EAClD,OAAc,MAAVH,EACO,KAAOE,EAAO,KACJ,MAAVF,EACA,qBAAuBE,EAAO,KAE9B,IAAMP,EAAO,kBA4ChC,QAASS,GAAIC,EAAQC,EAAQC,GAIzB,MAHAF,IAAkB,GAClBC,EAASA,GAAU,EACnBC,EAAMD,EAASD,EAAOG,OAClBD,EACOE,EAAMH,GAAQH,UAAU,EAAGI,GAAOF,EAEtCA,EA2iCX,QAASK,GAAWC,GAChB,GAAIC,GAASD,EAAQE,IAAIC,GAAMC,QAAQC,YAAYH,IAAM,eAAiBF,EAAQE,IAAI,cAAeI,EAASL,EAASA,EAAOM,MAAMC,MAC5H,EACA,EACA,EACA,EACA,IAEA,EACA,EACA,EACA,EACA,GACDC,EAAOC,GAAKC,KAAKL,EAAO,KAAMA,EAAO,IAAM,GAClD,QACIM,MAAON,EAAO,GAAKG,EACnBI,OAAQP,EAAO,GAAKG,EACpBK,QAASR,EAAO,GAAKG,GAG7B,QAASM,GAAKf,EAASgB,GAAvB,GACmCC,GAGvBC,EAAgCC,EAAkCC,EAA0CC,EAA4CC,EAH5JC,EAAUnB,GAAQmB,QAAqBC,EAAarB,GAAMsB,YAAaC,EAAcvB,GAAMwB,aAAcC,EAAS5B,EAAQ4B,SAAUC,EAAmBL,EAAW5C,EAoCtK,OAnCAgD,GAAOE,YAAY,4BACdF,EAAOG,SAAS,yBAwBjBC,EAAWhC,EAASgB,IAvBhBE,EAAQlB,EAAQ,GAAGiC,MAAMf,MAAOC,EAASnB,EAAQ,GAAGiC,MAAMd,OAAQC,EAAec,GAAcC,KAAKjB,GAAQG,EAAgBa,GAAcC,KAAKhB,GAASG,EAAatB,EAAQ+B,SAAS,cAAgB/B,EAAQoC,GAAG,qCACrNnB,EAAaG,GAAgBC,GACxBD,KAAkBJ,GAAYA,GAAYE,GAASI,KACpDJ,EAAQF,EAAWQ,EAAWxB,GAAW,EAAIwB,EAAWxB,MAEvDqB,KAAmBL,GAAYA,GAAYG,IAAWnB,EAAQoC,GAAG,wCAClEjB,EAASO,EAAY1B,IAEzBA,EAAQe,KAAKpC,EAAE,UAAU0D,SAAS,yBAAyBnC,KACvDgB,MAAOA,EACPC,OAAQA,KAEZS,EAAS5B,EAAQ4B,SACbX,GACAjB,EAAQE,KACJgB,MAAO,OACPC,OAAQ,OACRmB,UAAW,aACXC,aAAc,aACdC,gBAAiB,gBAMzBX,EAAmBL,EAAWI,KAC9BA,EAAOS,SAAS,4BAChBL,EAAWhC,EAASgB,IAEpBO,EAAQkB,MAAQ/B,GAAKgC,MAAMnB,EAAQoB,UAAY,IAC/C3C,EAAQE,KAAM0C,KAAM,IACpB5C,EAAQ6C,SAAS,WAAW3B,MAAMlB,EAAQkB,UAEvCU,EAEX,QAASI,GAAWhC,EAASgB,GACzB,GAAIC,GAAYO,EAAarB,GAAMsB,YAAaC,EAAcvB,GAAMwB,aAAcmB,EAAU9C,EAAQ4B,OAAO,0BAA2BmB,EAAeD,EAAQ,GAAGb,KAC5Ja,GAAQV,GAAG,YACXU,EAAQ5C,KACJ8C,QAAS,GACTC,SAAU,KAGlBhC,EAAaiB,GAAcC,KAAKY,EAAa7B,QAAUgB,GAAcC,KAAKY,EAAa5B,QAClFF,GACD6B,EAAQ5C,KACJgB,MAAOF,EAAWQ,EAAWxB,GAAW,EAAIwB,EAAWxB,GACvDmB,OAAQO,EAAY1B,GACpBsC,UAAW,cACXC,aAAc,cACdC,gBAAiB,gBAI7B,QAASU,GAAWC,GAChB,GAAIC,GAAI,EAAGvD,EAASwD,UAAUxD,MAC9B,KAAKuD,EAAI,EAAGA,EAAIvD,EAAQuD,IACpBE,EAAcH,EAAaE,UAAUD,GAEzC,OAAOD,GAEX,QAASG,GAAcH,EAAaI,GAChC,GAAwMC,GAAUC,EAAWC,EAAUC,EAAUC,EAA7OC,EAAkB1D,GAAM2D,KAAKD,gBAAiBE,EAAsB5D,GAAM2D,KAAKC,oBAAqBC,EAAa7D,GAAM2D,KAAKE,WAAYC,EAAyB9D,GAAM2D,KAAKG,sBAChL,KAAKT,IAAYD,GACbE,EAAYF,EAAOC,GACnBE,QAAkBD,GAEdE,EADAD,IAAaQ,IAAwB,OAAdT,EACZA,EAAUU,YAEV,KAEXR,GAAYA,IAAaS,OAAST,IAAaE,GAAmBF,IAAaI,GAAuBJ,IAAaK,GAAcL,IAAaM,GAA0BN,IAAaU,OACjLZ,YAAqBa,MACrBnB,EAAYK,GAAY,GAAIc,MAAKb,EAAUc,WACpCC,EAAWf,EAAUgB,OAC5BtB,EAAYK,GAAYC,EAAUgB,SAElCb,EAAWT,EAAYK,GAEnBL,EAAYK,SADLI,KAAaM,GACIN,SAI5BN,EAAcH,EAAYK,GAAWC,IAElCC,IAAagB,KACpBvB,EAAYK,GAAYC,EAGhC,OAAON,GAEX,QAASwB,GAAOC,EAAOC,EAAKC,GACxB,IAAK,GAAIC,KAAMF,GACX,GAAIA,EAAIG,eAAeD,IAAOF,EAAIE,GAAI5C,KAAKyC,GACvC,MAAOG,EAGf,OAAOD,KAASjG,EAAYiG,EAAOF,EAEvC,QAASK,GAAUC,GACf,MAAOA,GAAI9F,QAAQ,gBAAiB,SAAU+F,GAC1C,MAAOA,GAAE7F,OAAO,GAAK,IAAM6F,EAAE7F,OAAO,GAAG8F,gBAG/C,QAASC,GAAYH,GACjB,MAAOA,GAAI9F,QAAQ,UAAW,SAAUkG,EAAUC,GAC9C,MAAOA,GAAGC,gBAGlB,QAASC,GAAkBzF,EAAS0F,GAChC,GAAiBC,GAAbC,IAmBJ,OAlBIC,UAASC,aAAeD,SAASC,YAAYC,kBAC7CJ,EAAgBE,SAASC,YAAYC,iBAAiB/F,EAAS,IAC3D0F,GACA/G,EAAEqH,KAAKN,EAAY,SAAUO,EAAKC,GAC9BN,EAAOM,GAASP,EAAcQ,iBAAiBD,OAIvDP,EAAgB3F,EAAQoG,aACpBV,GACA/G,EAAEqH,KAAKN,EAAY,SAAUO,EAAKC,GAC9BN,EAAOM,GAASP,EAAcN,EAAYa,OAIjD/F,GAAMkG,KAAKT,KACZA,EAASD,GAENC,EAEX,QAASU,GAAatG,GAClB,GAAIA,GAAWA,EAAQuG,WAA0C,gBAAtBvG,GAAQuG,WAA0BvG,EAAQuG,UAAUC,QAAQ,wBACnG,OAAO,CAEX,IAAIC,GAAWhB,EAAkBzF,GAAU,aAAayG,QACxD,OAAmB,QAAZA,GAAkC,UAAZA,EAEjC,QAASC,GAAW1G,EAASkG,GAA7B,GAIQS,GAHAC,EAASxG,GAAQmB,QAAQqF,OACzBC,EAASzG,GAAQmB,QAAQuF,QACzBC,EAAK/G,YAAmBrB,GAAIqB,EAAQ,GAAKA,CAE7C,IAAKA,EAIL,MADA2G,GAAQvG,GAAQuG,MAAM3G,GAClBkG,IAAUrH,EASN8H,GAASC,EACFG,EAAGC,YAAcD,EAAGE,YAAcF,EAAGL,WAErCQ,KAAKC,IAAIJ,EAAGL,aAVnBK,EAAGL,WADHC,GAASC,EACOG,EAAGC,YAAcD,EAAGE,YAAcf,EAC3CS,GAASE,GACCX,EAEDA,EALpB,GAmTR,QAASG,GAAKe,GACV,GAAgBC,GAAZC,EAAS,CACb,KAAKD,IAAOD,GACJA,EAAIpC,eAAeqC,IAAe,UAAPA,GAC3BC,GAGR,OAAOA,GAEX,QAASC,GAAUvH,EAASwH,EAAMC,GAAlC,GAIQC,GACAJ,EAOIK,CAIR,OAfKH,KACDA,EAAO,UAEPE,EAAS1H,EAAQwH,KACjBF,GACAM,IAAKF,EAAOE,IACZ/G,MAAO6G,EAAO7G,MACdC,OAAQ4G,EAAO5G,OACfF,KAAM8G,EAAO9G,MAEbR,GAAQmB,QAAQkB,OAASrC,GAAQyH,UAAYzH,GAAQ0H,cAAgBL,IACjEE,EAAOvH,GAAQuG,MAAM3G,GAAW,KACpCsH,EAAOM,KAAOhJ,EAAOmJ,YAAclC,SAASmC,gBAAgBC,UAC5DX,EAAO1G,MAAQhC,EAAOsJ,YAAcP,EAAO9B,SAASmC,gBAAgBtB,YAEjEY,EAYX,QAASa,GAAaC,GAClB,GAAIC,KAIJ,OAHArC,IAAsB,gBAAVoC,GAAqBA,EAAMlJ,MAAM,KAAOkJ,EAAO,SAAUnC,GACjEoC,EAAQpC,GAAOqC,OAEZD,EAEX,QAASE,GAAGvI,GACR,MAAO,IAAIG,IAAMkI,QAAQG,QAAQxI,GAiCrC,QAASyI,GAAwBC,EAASC,EAAUC,EAASC,GAsBzD,aArBWH,KAAYI,KACftE,EAAWmE,KACXE,EAAWF,EACXA,EAAW,IACXC,GAAU,GAEVpE,EAAWoE,KACXC,EAAWD,EACXA,GAAU,SAEHD,KAAaI,KACpBH,EAAUD,EACVA,EAAW,KAEfD,GACIL,QAASK,EACTC,SAAUA,EACVC,QAASA,EACTC,SAAUA,IAGXG,IACHX,WACAM,SAAU,IACVC,SAAS,EACTK,KAAMC,GACNC,SAAUD,GACVE,MAAM,GACPV,GACCW,iBAAkBX,EAAQG,SAC1BA,SAAUK,KAGlB,QAASI,GAAQtJ,EAAS0I,EAASC,EAAUC,EAASC,GAElD,IADA,GAAsCU,GAAlCtD,EAAM,EAAGpG,EAASG,EAAQH,OACvBoG,EAAMpG,EAAQoG,IACjBsD,EAAW5K,EAAEqB,EAAQiG,IACrBsD,EAASC,MAAM,WACXnB,EAAQoB,QAAQF,EAAUd,EAAwBC,EAASC,EAAUC,EAASC,KAGtF,OAAO7I,GAEX,QAAS0J,GAAY1J,EAAS2J,EAASjB,EAASkB,GAO5C,MANID,KACAA,EAAUA,EAAQzK,MAAM,KACxB8G,GAAK2D,EAAS,SAAU1D,EAAKC,GACzBlG,EAAQ0J,YAAYxD,EAAO0D,MAG5B5J,EAsBX,QAAS6J,GAAW3D,GAChB,OAAQ,GAAKA,GAAO9G,QAAQ0K,EAAW,SAAS1K,QAAQ2K,EAAU,QAAQ3K,QAAQ4K,EAAU,QAAQ5K,QAAQ6K,EAAa,UAAU7K,QAAQ8K,EAAY,SA0U3J,QAASC,GAAYnK,EAASoK,GAC1B,GAAIlE,EAoBJ,OAnB+B,KAA3BkE,EAAO5D,QAAQ,UACf4D,EAASA,EAAO5K,UAAU,GAC1B4K,EAASA,EAAO9K,OAAO,GAAG8F,cAAgBgF,EAAO5K,UAAU,IAE/D4K,EAASA,EAAOhL,QAAQiL,GAAY,OACpCnE,EAAQlG,EAAQsK,aAAa,QAAUnK,GAAMoK,GAAKH,GACpC,OAAVlE,EACAA,EAAQrH,EACS,SAAVqH,EACPA,EAAQ,KACS,SAAVA,EACPA,GAAQ,EACS,UAAVA,EACPA,GAAQ,EACDsE,GAAarI,KAAK+D,IAAoB,QAAVkE,EACnClE,EAAQuE,WAAWvE,GACZwE,GAAWvI,KAAK+D,KAAWyE,GAAiBxI,KAAK+D,KACxDA,EAAY0E,SAAS,WAAa1E,EAAQ,QAEvCA,EAEX,QAAS2E,GAAa7K,EAAS0I,EAASnF,GACpC,GAAiB6G,GAAQlE,EAArBoB,KAA4BwD,EAAO9K,EAAQsK,aAAa,QAAUnK,GAAMoK,GAAK,OACjF,KAAKH,IAAU1B,GACXxC,EAAQiE,EAAYnK,EAASoK,GACzBlE,IAAUrH,IACNkM,GAAe5I,KAAKiI,IAAmB,UAARU,IACV,gBAAV5E,GACHvH,EAAE,IAAMuH,GAAOrG,OACfqG,EAAQ/F,GAAM6K,SAASrM,EAAE,IAAMuH,GAAO+E,QAC/B1H,IACP2C,EAAQ/F,GAAM6K,SAASzH,EAAO2C,KAGlCA,EAAQlG,EAAQsK,aAAaF,IAGrC9C,EAAO8C,GAAUlE,EAGzB,OAAOoB,GA0FX,QAAS4D,GAAoBC,EAAGC,GAC5B,MAAOzM,GAAE0M,SAASF,EAAGC,MAAU,EAEnC,QAASE,KACL,GAAIC,GAAS5M,EAAE2J,KACf,OAAO3J,GAAE6M,QAAQD,EAAOE,KAAK,QAAUtL,GAAMoK,GAAK,SAC9C,SACA,oBACOgB,EAAOnJ,GAAG,YAmPzB,QAASsJ,GAAU1L,EAAS2L,GACxB,GAAIC,GAAW5L,EAAQ4L,SAASxG,aAChC,QAAQ,sCAAsCjD,KAAKyJ,IAAa5L,EAAQ6L,SAAW,MAAQD,EAAW5L,EAAQ8L,MAAQH,EAAmBA,IAAqBI,EAAQ/L,GAE1K,QAAS+L,GAAQ/L,GACb,MAAOrB,GAAEqN,KAAKC,QAAQF,QAAQ/L,KAAarB,EAAEqB,GAASkM,UAAUC,UAAUC,OAAO,WAC7E,MAAqC,WAA9BzN,EAAEuB,IAAIoI,KAAM,gBACpBzI,OAsHP,QAASwM,GAAYC,EAAUC,GAC3B,MAAO,IAAIF,GAAYG,GAAGvD,KAAKqD,EAAUC,GAhsFhD,GACoKE,GAiE7JjI,EAGAkI,EAGAC,EAGAC,EA6FAC,EAA6BC,EAAiCC,EAA8BC,EAA4BC,EAA6BC,EAA4BpN,EAolDjLqN,EAoBA9E,EAuGAyB,EAAkBC,EAAiBE,EAAoBC,EAAmBF,EAI1EoD,EA4DAC,EAmBGC,EA2IHC,GAkFAC,GA2BAzC,GAA+BL,GAAkEC,GAA4DN,GAyO7JoD,GAOAC,GA+JAC,GAUAC,GACAC,GAwCAC,GAgDAC,GAEGC,GAQHC,GAcAC,GA8ZAC,GAMAC,GA/mGAjO,GAAQvB,EAAOuB,MAAQvB,EAAOuB,QAAWkO,aAAgBrF,GAASrK,EAAEqK,OAAQhD,GAAOrH,EAAEqH,KAAMsI,GAAU3P,EAAE2P,QAASC,GAAQ5P,EAAE4P,MAAOrF,GAAOvK,EAAEuK,KAAMxI,GAAOwG,KAAgBsH,GAAO5P,EAAO4P,SAAYpO,MAAc8B,GAAgB,IAAKuM,GAAe,uBAAwBjO,GAAkB,sEAAuEgK,GAAe,uBAAwBkE,GAAW,WAAY5F,GAAS,SAAU6F,GAAS,SAAUzK,GAAS,SAAU0K,GAAO,OAAQ7F,GAAU,UAAWrE,GAAY,YAAamK,MAAkBC,MAAkBC,MAAWA,MAAOC,GAAoB,WAC9lB,GAAIC,GAAKC,EAAaC,EAAMC,EAAM1G,EAASjE,EAAO4K,EAAShM,UAAU,OAAUD,EAAI,EAAGvD,EAASwD,UAAUxD,OAAQyP,GAAO,CAaxH,KAZsB,iBAAXD,KACPC,EAAOD,EACPA,EAAShM,UAAUD,OACnBA,KAEkB,gBAAXiM,IAAwBE,OAAO/K,WAAW6K,KACjDA,MAEAjM,IAAMvD,IACNwP,EAAS/G,KACTlF,KAEGA,EAAIvD,EAAQuD,IACf,GAAgC,OAA3BsF,EAAUrF,UAAUD,IACrB,IAAKgM,IAAQ1G,GACG,WAAR0G,GAA6B,UAARA,GAA4B,KAARA,IAG7CH,EAAMI,EAAOD,GACbD,EAAOzG,EAAQ0G,GACXC,IAAWF,IAGXG,GAAQH,IAASI,OAAOC,cAAcL,KAAUD,EAAcK,OAAOjB,QAAQa,MACzED,GACAA,GAAc,EACdzK,EAAQwK,GAAOM,OAAOjB,QAAQW,GAAOA,MAErCxK,EAAQwK,GAAOM,OAAOC,cAAcP,GAAOA,KAE/CI,EAAOD,GAAQJ,GAAkBM,EAAM7K,EAAO0K,IACvCA,IAAStQ,IAChBwQ,EAAOD,GAAQD,IAK/B,OAAOE,GAEflP,IAAMwC,QAAU,aAAavD,QAAQ,aAAc,IAGnDN,EAAMkK,OAAS,SAAUyG,GACrB,GACOC,GAEAlD,EAHHmD,EAAO,aACIC,EAAOtH,KAAMuH,EAAWJ,GAASA,EAAMxG,KAAOwG,EAAMxG,KAAO,WAClE2G,EAAKE,MAAMxH,KAAMjF,WAEzBsM,GAAKI,UAAYH,EAAKG,UACtBvD,EAAKqD,EAASrD,GAAKqD,EAASE,UAAY,GAAIJ,EAC5C,KAAKD,IAAUD,GAEPjD,EAAGkD,GADc,MAAjBD,EAAMC,IAAmBD,EAAMC,GAAQvL,cAAgB6L,OAC1ChH,IAAO,KAAU2G,EAAKI,UAAUL,GAASD,EAAMC,IAE/CD,EAAMC,EAK3B,OAFAlD,GAAGrI,YAAc0L,EACjBA,EAAS7G,OAAS4G,EAAK5G,OAChB6G,GAEX/Q,EAAMiR,UAAUE,aAAe,SAAUvH,GACrCJ,KAAKI,QAAUxF,KAAeoF,KAAKI,QAASA,IAE5ClE,EAAarE,GAAMqE,WAAa,SAAUgI,GAC1C,MAAqB,kBAAPA,IAEdE,EAAiB,WACjBpE,KAAK4H,mBAAoB,GAEzBvD,EAAqB,WACrB,MAAOrE,MAAK4H,qBAAsB,GAElCtD,EAAa9N,EAAMkK,QACnBC,KAAM,WACFX,KAAK6H,YAETC,KAAM,SAAUC,EAAWC,EAAUC,GACjC,GAAiBtK,GAAyEpG,EAAQ2Q,EAAUC,EAA4DC,EAApKd,EAAOtH,KAAWqI,QAAoBN,KAAcvH,IAAUuH,GAAaA,EAAsCO,QAA4BN,KAAa5B,EAC9J,IAAI4B,IAAazR,EAAW,CACxB,IAAKoH,IAAOoK,GACRT,EAAKQ,KAAKnK,EAAKoK,EAAUpK,GAE7B,OAAO2J,GAEX,IAAK3J,EAAM,EAAGpG,EAAS8Q,EAAW9Q,OAAQoG,EAAMpG,EAAQoG,IACpDoK,EAAYM,EAAW1K,GACvBwK,EAAUG,EAAqBN,EAAWA,EAASD,GAC/CI,IACIF,IACAC,EAAWC,EACXA,EAAU,WACNb,EAAKiB,OAAOR,EAAWI,GACvBD,EAASV,MAAMF,EAAMvM,YAEzBoN,EAAQD,SAAWA,GAEvBE,EAASd,EAAKO,QAAQE,GAAaT,EAAKO,QAAQE,OAChDK,EAAOI,KAAKL,GAGpB,OAAOb,IAEXW,IAAK,SAAUI,EAAYL,GACvB,MAAOhI,MAAK8H,KAAKO,EAAYL,GAAU,IAE3CjR,MAAO,SAAUgR,EAAWC,GACxB,GAAiBrK,GAAyEpG,EAAQ4Q,EAA4DC,EAA1Jd,EAAOtH,KAAWqI,QAAoBN,KAAcvH,IAAUuH,GAAaA,EAA4BO,QAA4BN,KAAa5B,EACpJ,KAAKzI,EAAM,EAAGpG,EAAS8Q,EAAW9Q,OAAQoG,EAAMpG,EAAQoG,IACpDoK,EAAYM,EAAW1K,GACvBwK,EAAUG,EAAqBN,EAAWA,EAASD,GAC/CI,IACAC,EAASd,EAAKO,QAAQE,GAAaT,EAAKO,QAAQE,OAChDK,EAAOK,QAAQN,GAGvB,OAAOb,IAEXoB,QAAS,SAAUX,EAAWY,GAC1B,GAAmDhL,GAAKpG,EAApD+P,EAAOtH,KAAMoI,EAASd,EAAKO,QAAQE,EACvC,IAAIK,EAAQ,CAOR,IANAO,EAAIA,MACJA,EAAEC,OAAStB,EACXqB,EAAEf,mBAAoB,EACtBe,EAAEvE,eAAiBA,EACnBuE,EAAEtE,mBAAqBA,EACvB+D,EAASA,EAAO3B,QACX9I,EAAM,EAAGpG,EAAS6Q,EAAO7Q,OAAQoG,EAAMpG,EAAQoG,IAChDyK,EAAOzK,GAAKkL,KAAKvB,EAAMqB,EAE3B,OAAOA,GAAEf,qBAAsB,EAEnC,OAAO,GAEXW,OAAQ,SAAUR,EAAWI,GACzB,GAAmDxK,GAA/C2J,EAAOtH,KAAMoI,EAASd,EAAKO,QAAQE,EACvC,IAAIA,IAAcxR,EACd+Q,EAAKO,eACF,IAAIO,EACP,GAAID,EACA,IAAKxK,EAAMyK,EAAO7Q,OAAS,EAAGoG,GAAO,EAAGA,IAChCyK,EAAOzK,KAASwK,GAAWC,EAAOzK,GAAKuK,WAAaC,GACpDC,EAAOU,OAAOnL,EAAK,OAI3B2J,GAAKO,QAAQE,KAGrB,OAAOT,MAiBX/C,EAAqB,OAAQC,EAAe,iBAAkBC,EAAqB,QAASC,EAAc,aAAcC,EAAqB,OAAQC,EAAc,aAAcpN,GAC7K,GACA,IACA,KACA,MACA,QAER2M,GACI4E,UAAW,OACXC,cAAc,EACdC,OAAQ,SAAUvG,EAAUlH,GACxB,GAAImC,GAAKpG,EAAQoL,EAAO,EACxB,KAAKhF,EAAM,EAAGpG,EAASiE,EAAKjE,OAAQoG,EAAMpG,EAAQoG,IAC9CgF,GAAQD,EAASlH,EAAKmC,GAE1B,OAAOgF,IAEXuG,QAAS,SAAUxG,EAAUtC,GACzB,GAAgP8D,GAAIiF,EAAOxL,EAAvPyL,EAAW1I,MAAWV,KAAMI,GAAU2I,EAAYK,EAASL,UAAWM,EAAeN,EAAU9Q,MAAMsM,GAAoB,GAAIyE,EAAeI,EAASJ,aAAcM,EAAe,wDACtL,IAAIpN,EAAWwG,GACX,MAAOA,EAKX,KAHA4G,GAAgBN,EAAe,QAAUD,EAAY,KAAO,GAC5DO,GAAgB,gBAChBH,EAAQzG,EAAS5L,QAAQ2N,EAAoB,aAAa3N,QAAQ0N,EAAc,2BAA2B1N,QAAQ4N,EAAa,KAAK5N,QAAQ6N,EAAoB,aAAa/N,MAAM,KAC/K+G,EAAM,EAAGA,EAAMwL,EAAM5R,OAAQoG,IAC9B2L,GAAgB7S,EAAY0S,EAAMxL,GAAMA,EAAM,IAAM,EAExD2L,IAAgBN,EAAe,KAAO,IACtCM,GAAgB,uBAChBA,EAAeA,EAAaxS,QAAQ8N,EAAa,IACjD,KAGI,MAFAV,GAAS5B,SAAS+G,EAAcC,GAChCpF,EAAGqF,WAAa3K,KAAKxE,MAAM+O,EAAM5R,OAAS,GACnC2M,EACT,MAAOyE,GACL,KAAUa,OAAM3R,GAAM4R,OAAO,8CAAmD/G,EAAU4G,OAarG,WAmBG,QAASI,GAAMC,GAEX,MADAC,GAAUC,UAAY,EACfD,EAAU/P,KAAK8P,GAAU,IAAMA,EAAO7S,QAAQ8S,EAAW,SAAU/G,GACtE,GAAIiH,GAAIC,EAAKlH,EACb,cAAciH,KAAMtJ,GAASsJ,EAAI,OAAS,OAASjH,EAAEmH,WAAW,GAAGC,SAAS,KAAKxD,YAChF,IAAM,IAAMkD,EAAS,IAE9B,QAAS/M,GAAImC,EAAKmL,GACd,GAAIpP,GAAGqP,EAAGC,EAAG7S,EAAoB8S,EAA8BnL,EAA1CoL,EAAOC,EAAc3M,EAAQsM,EAAOnL,EAQzD,IAPInB,SAAgBA,KAAUhC,UAAiBgC,GAAM4M,SAAWpE,KAC5DxI,EAAQA,EAAM4M,OAAOzL,UAEd0L,KAAQrE,KACfxI,EAAQ6M,EAAI5B,KAAKqB,EAAQnL,EAAKnB,IAElCsB,QAActB,GACVsB,IAASsB,GACT,MAAOkJ,GAAM9L,EACV,IAAIsB,IAASmH,GAChB,MAAOqE,UAAS9M,GAAgBA,EAAP+M,GAAgBrE,EACtC,IAAIpH,IAASuB,IAAWvB,IAASoH,GACpC,MAAc1I,GAAP+M,EACJ,IAAIzL,IAAStD,GAAQ,CACxB,IAAKgC,EACD,MAAO0I,GAIX,IAFAiE,GAAOK,EACPP,KAC8B,mBAA1BJ,EAASzC,MAAM5J,GAA6B,CAE5C,IADArG,EAASqG,EAAMrG,OACVuD,EAAI,EAAGA,EAAIvD,EAAQuD,IACpBuP,EAAQvP,GAAK8B,EAAI9B,EAAG8C,IAAU0I,EAIlC,OAFA8D,GAAuB,IAAnBC,EAAQ9S,OAAe,KAAOgT,EAAM,MAAQA,EAAMF,EAAQxT,KAAK,MAAQ0T,GAAO,KAAOD,EAAO,IAAM,IAAMD,EAAQxT,KAAK,KAAO,IAChI0T,EAAMD,EACCF,EAEX,GAAIK,SAAcA,KAAQ7O,GAEtB,IADArE,EAASkT,EAAIlT,OACRuD,EAAI,EAAGA,EAAIvD,EAAQuD,UACT2P,GAAI3P,KAAO0F,KAClB2J,EAAIM,EAAI3P,GACRsP,EAAIxN,EAAIuN,EAAGvM,GACPwM,GACAC,EAAQ7B,KAAKkB,EAAMS,IAAMI,EAAM,KAAO,KAAOH,QAKzD,KAAKD,IAAKvM,GACF8J,OAAOhL,eAAemM,KAAKjL,EAAOuM,KAClCC,EAAIxN,EAAIuN,EAAGvM,GACPwM,GACAC,EAAQ7B,KAAKkB,EAAMS,IAAMI,EAAM,KAAO,KAAOH,GAO7D,OAFAA,GAAuB,IAAnBC,EAAQ9S,OAAe,KAAOgT,EAAM,MAAQA,EAAMF,EAAQxT,KAAK,MAAQ0T,GAAO,KAAOD,EAAO,IAAM,IAAMD,EAAQxT,KAAK,KAAO,IAChI0T,EAAMD,EACCF,GA9Ef,GAA4IG,GAAKK,EAQ1IH,EARHb,EAAY,2HAAyIG,GACjJc,KAAM,MACNC,KAAM,MACNC,KAAM,MACNC,KAAM,MACNC,KAAM,MACNC,IAAK,MACLC,KAAM,QACFlB,KAAcA,eACfjO,MAAKyL,UAAU+C,SAAWpE,KACjCpK,KAAKyL,UAAU+C,OAAS,WACpB,GAAIlD,GAAOtH,IACX,OAAO0K,UAASpD,EAAK8D,WAAajU,EAAImQ,EAAK+D,iBAAkB,GAAK,IAAMlU,EAAImQ,EAAKgE,cAAgB,GAAK,IAAMnU,EAAImQ,EAAKiE,cAAgB,IAAMpU,EAAImQ,EAAKkE,eAAiB,IAAMrU,EAAImQ,EAAKmE,iBAAmB,IAAMtU,EAAImQ,EAAKoE,iBAAmB,IAAM,MAEnPf,OAAOlD,UAAU+C,OAASmB,OAAOlE,UAAU+C,OAASoB,QAAQnE,UAAU+C,OAAS,WAC3E,MAAOxK,MAAKoL,kBAkETlF,IAAK2F,YAAczF,KAC1BF,GAAK2F,UAAY,SAAUjO,EAAOkO,EAAUC,GACxC,GAAIjR,EAGJ,IAFAyP,EAAM,GACNK,EAAS,SACEmB,KAAU1F,GACjB,IAAKvL,EAAI,EAAGA,EAAIiR,EAAOjR,GAAK,EACxB8P,GAAU,eAEAmB,KAAUvL,KACxBoK,EAASmB,EAGb,IADAtB,EAAMqB,EACFA,SAAmBA,KAAa1F,WAAoB0F,KAAalQ,UAAiBkQ,GAASvU,SAAW8O,IACtG,KAAUmD,OAAM,iBAEpB,OAAO5M,GAAI,IAAMoP,GAAIpO,SAIhC,WAgIG,QAASqO,GAAYC,GACjB,GAAIA,EAAS,CACT,GAAIA,EAAQC,aACR,MAAOD,EAEX,UAAWA,KAAY1L,GAAQ,CAC3B,GAAIuF,GAAWlO,GAAMkO,QACrB,OAAOA,GAASmG,IAAYnG,EAASmG,EAAQtV,MAAM,KAAK,KAAO,KAEnE,MAAO,MAEX,MAAO,MAEX,QAASwV,GAAWF,GAIhB,MAHIA,KACAA,EAAUD,EAAYC,IAEnBA,GAAWrU,GAAMkO,SAASsG,QAerC,QAASC,GAAWC,EAAM9C,EAAQyC,GAC9BA,EAAUE,EAAWF,EACrB,IAAIM,GAAWN,EAAQO,UAAUC,SAAUC,EAAOH,EAASG,KAAMC,EAASJ,EAASI,MAEnF,OADAnD,GAAS+C,EAASK,SAASpD,IAAWA,EAC/BA,EAAO3S,QAAQgW,EAAkB,SAAU7U,GAAV,GAChC8U,GACA/N,EACAK,CA8DJ,OA7Dc,MAAVpH,EACA+G,EAASuN,EAAKS,UACG,OAAV/U,EACP+G,EAAS7H,EAAIoV,EAAKS,WACD,QAAV/U,EACP+G,EAAS2N,EAAKM,UAAUV,EAAKW,UACZ,SAAVjV,EACP+G,EAAS2N,EAAKQ,MAAMZ,EAAKW,UACR,MAAVjV,EACP+G,EAASuN,EAAKa,WAAa,EACV,OAAVnV,EACP+G,EAAS7H,EAAIoV,EAAKa,WAAa,GACd,QAAVnV,EACP+G,EAAS4N,EAAOK,UAAUV,EAAKa,YACd,SAAVnV,EACP+G,EAAS4N,EAAOO,MAAMZ,EAAKa,YACV,OAAVnV,EACP+G,EAAS7H,EAAIoV,EAAKc,cAAgB,KACjB,SAAVpV,EACP+G,EAAS7H,EAAIoV,EAAKc,cAAe,GAChB,MAAVpV,EACP+G,EAASuN,EAAKe,WAAa,IAAM,GAChB,OAAVrV,EACP+G,EAAS7H,EAAIoV,EAAKe,WAAa,IAAM,IACpB,MAAVrV,EACP+G,EAASuN,EAAKe,WACG,OAAVrV,EACP+G,EAAS7H,EAAIoV,EAAKe,YACD,MAAVrV,EACP+G,EAASuN,EAAKgB,aACG,OAAVtV,EACP+G,EAAS7H,EAAIoV,EAAKgB,cACD,MAAVtV,EACP+G,EAASuN,EAAKiB,aACG,OAAVvV,EACP+G,EAAS7H,EAAIoV,EAAKiB,cACD,MAAVvV,EACP+G,EAAS5G,GAAKgC,MAAMmS,EAAKkB,kBAAoB,KAC5B,OAAVxV,GACP+G,EAASuN,EAAKkB,kBACVzO,EAAS,KACTA,EAAS5G,GAAKgC,MAAM4E,EAAS,KAEjCA,EAAS7H,EAAI6H,IACI,QAAV/G,EACP+G,EAAS7H,EAAIoV,EAAKkB,kBAAmB,GACpB,OAAVxV,EACP+G,EAASuN,EAAKe,WAAa,GAAKd,EAASkB,GAAG,GAAKlB,EAASmB,GAAG,GAC5C,QAAV1V,GACP8U,EAAUR,EAAKqB,oBACfvO,EAAO0N,EAAU,EACjB/N,GAAS5G,GAAAA,GAAKyG,IAAIkO,EAAU,KAAenW,MAAM,KAAK,GACtDmW,EAAU3U,GAAKyG,IAAIkO,GAAoB,GAAT/N,EAC9BA,GAAUK,EAAO,IAAM,KAAOlI,EAAI6H,GAClCA,GAAU,IAAM7H,EAAI4V,IACH,OAAV9U,GAA4B,MAAVA,IACzB+G,EAASuN,EAAKqB,oBAAsB,GACpCvO,EAAOL,EAAS,EAChBA,GAAS5G,GAAAA,GAAKyG,IAAIG,IAAmBpI,MAAM,KAAK,GAChDoI,GAAUK,EAAO,IAAM,MAAkB,OAAVpH,EAAiBd,EAAI6H,GAAUA,IAE3DA,IAAWzI,EAAYyI,EAAS/G,EAAMwO,MAAM,EAAGxO,EAAMV,OAAS,KAG7E,QAASsW,GAAazW,EAAQqS,EAAQyC,GAAtC,GAEQC,GAAqC2B,EAA+BC,EAAmCC,EAAmCC,EAAeC,EAAQC,EAAYC,EAAWC,EAAiBC,EAAoBC,EAAuBC,EAASC,EAAUC,EAAeC,EAAgBC,EAAqBhR,EAAeD,EAAKpG,EAAQsX,EAAIC,EAAUC,EAAmBC,EAAcC,EAAYC,EAAWC,EAASC,EAAUC,EAAcC,EAAeC,EAAgBC,EAAYlY,EA2BjemY,EAmGIC,CA7HhB,IAFAxD,EAAUE,EAAWF,GACjBC,EAAeD,EAAQC,aAAc2B,EAAU3B,EAAawD,GAAQ5B,EAAY5B,EAAayD,SAAU5B,EAAU7B,EAAa6B,QAAQ,GAAIC,KAAmFM,EAAWnX,EAAS,EAAqDwX,EAAciB,EAAOjS,EAAQiS,EAA0JL,KAC7dpY,IAAWb,EACX,MAAOsZ,EAEX,KAAKnF,SAAStT,GACV,MAAOA,EAEX,KAAKqS,EACD,MAAOyC,GAAQpF,KAAKvP,OAASH,EAAO0Y,iBAAmB1Y,GAAAA,CAG3D,IADAkX,EAAqByB,EAAqBC,KAAKvG,GACvB,CAepB,GAdAA,EAAS6E,EAAmB,GAAGxR,cAC/BqR,EAAwB,MAAX1E,EACb2E,EAAuB,MAAX3E,GACR0E,GAAcC,KACdjC,EAAegC,EAAahC,EAAa8D,SAAW9D,EAAa+D,QACjEpC,EAAU3B,EAAawD,GACvB5B,EAAY5B,EAAayD,SACzB1B,EAAS/B,EAAa+B,OACtBF,EAAU7B,EAAa6B,QAAQO,EAAW,EAAI,IAElDF,EAAkBC,EAAmB,GACjCD,IACAN,GAAaM,GAEF,MAAX5E,EAEA,MADIgG,GAAMpB,EAAkBjX,EAAO+Y,cAAcpC,GAAa3W,EAAO+Y,gBAC9DV,EAAI3Y,QAAQ6Y,EAAOxD,EAAawD,GAiB3C,IAfIvB,IACAhX,GAAU,KAEdA,EAASgZ,EAAMhZ,EAAQ2W,GACvBQ,EAAWnX,EAAS,EACpBA,EAASA,EAAOR,MAAM+Y,GACtBnB,EAAUpX,EAAO,GACjBqX,EAAWrX,EAAO,GACdmX,IACAC,EAAUA,EAAQtX,UAAU,IAEhC0G,EAAQyS,EAAa7B,EAAS,EAAGA,EAAQjX,OAAQ4U,GAC7CsC,IACA7Q,GAASkQ,EAAUW,GAER,MAAXhF,IAAmB8E,EACnB,MAAO3Q,EAGX,KADAxG,EAASyY,EACJlS,EAAM,EAAGpG,EAASyW,EAAQzW,OAAQoG,EAAMpG,EAAQoG,IACjDkR,EAAKb,EAAQhX,OAAO2G,GAEhBvG,GADO,MAAPyX,EACUjR,EACI,MAAPiR,GAAqB,MAAPA,EACXX,EAEAW,CAGlB,OAAOzX,GAUX,IARIqS,EAAOvL,QAAQ,SAAcuL,EAAOvL,QAAQ,SAAauL,EAAOvL,QAAQ,YACxEuL,EAASA,EAAO3S,QAAQwZ,EAAe,SAAUrY,GAC7C,GAAIsY,GAAYtY,EAAMjB,OAAO,GAAGF,QAAQ,KAAM,IAAK0Z,EAAUvY,EAAMwO,MAAM,GAAG3P,QAAQyZ,EAAW,GAE/F,OADAtC,GAASzF,KAAKgI,GACPC,KAGfhH,EAASA,EAAO7S,MAAM,KAClB2X,GAAY9E,EAAO,GACnBA,EAASA,EAAO,GAChBsF,GAAoB,MACjB,IAAe,IAAX3X,GAAgBqS,EAAO,IAE9B,GADAA,EAASA,EAAO,GACZA,EAAOvL,QAAQwS,QAAgBjH,EAAOvL,QAAQyS,OAC9C,MAAOlH,OAGXA,GAASA,EAAO,EAyBpB,IAvBA4F,EAAe5F,EAAOvL,QAAQ,KAC9BoR,EAAgB7F,EAAOvL,QAAQ,KAC/BkQ,EAAYiB,MACZlB,EAAamB,MACTlB,IACAhX,GAAU,KAEV+W,GAA4C,OAA9B1E,EAAO6F,EAAgB,KACrC7F,EAASA,EAAO7S,MAAM,MAAMC,KAAK,IACjCsX,GAAa,IAEbA,GAAcC,KACdjC,EAAegC,EAAahC,EAAa8D,SAAW9D,EAAa+D,QACjEpC,EAAU3B,EAAawD,GACvB5B,EAAY5B,EAAayD,SACzB1B,EAAS/B,EAAa+B,QAE1BY,EAAWrF,EAAOvL,QAAQ0S,MACtB9B,IACArF,EAASA,EAAO3S,QAAQ+Z,EAAahB,IAEzCb,EAAevF,EAAOvL,QAAQyR,GAC9BpY,EAASkS,EAAOlS,OACZyX,MAmBA,GAlBAP,GAAWrX,GAAAA,GAAkBR,MAAM,KAE/B6X,EADAA,EAAS,GACE2B,EAAMhZ,EAAQwH,KAAKC,IAAI4P,EAAS,KAEhCA,EAAS,GAExBA,EAAWA,EAAS7X,MAAM+Y,GAAO,IAAME,EACvCX,EAAYzF,EAAOqH,YAAYH,GAAQ3B,EACvCC,EAAaxF,EAAOqH,YAAYJ,GAAS1B,EACzCG,EAAUD,KACVE,EAAWH,KACXtR,EAAM8Q,EAASlX,OACV4X,GAAYC,IACb3F,EAASA,EAAOvS,UAAU,EAAG8X,GAAgBvF,EAAOvS,UAAU8X,EAAe,GAC7EzX,EAASkS,EAAOlS,OAChByX,KACArR,EAAM,GAENwR,GAAWD,EAAYD,EACvBtR,EAAMuR,MACH,IAAID,EAAaC,EACpB,GAAIE,GAAYzR,EAAMsR,EAAY,CAE9B,IADIS,EAAUU,EAAMhZ,EAAQ6X,EAAYV,GACjCmB,EAAQ1Y,OAAO0Y,EAAQnY,OAAS,KAAOoZ,GAAQ1B,EAAa,GAAKA,EAAaC,GACjFD,IACAS,EAAUU,EAAMhZ,EAAQ6X,EAAYV,EAExC5Q,GAAMsR,MACCE,IAAWxR,EAAMuR,IACxBvR,EAAMuR,EA0BlB,IAtBA9X,EAASgZ,EAAMhZ,EAAQuG,EAAK4Q,GAC5BU,EAAaxF,EAAOvL,QAAQwS,GAC5BnB,EAAiBL,EAAYzF,EAAOvL,QAAQyS,GAExCnB,EADAP,OAAoBC,MACZA,EACDD,OAAoBC,MACnBD,EAEAA,EAAaC,EAAYA,EAAYD,EAEjDA,EAAaxF,EAAOqH,YAAYJ,GAChCxB,EAAYzF,EAAOqH,YAAYH,GAE3BrZ,EADA2X,OAAoBC,MACdA,EACCD,OAAoBC,MACrBD,EAEAA,EAAaC,EAAYD,EAAaC,EAE5CM,GAASjY,IACTD,EAAMkY,GAENA,MAAa,CAab,IAZA5R,GAAQxG,GAAAA,GAAkBR,MAAM+Y,GAChCnB,EAAU5Q,EAAM,GAChB6Q,EAAW7Q,EAAM,IAAMiS,EACvBnB,EAAgBF,EAAQjX,OACxBoX,EAAiBF,EAASlX,OACtBgX,GAAYnX,MAAe,IAC3BmX,GAAW,GAEfnX,EAASqS,EAAOvS,UAAU,EAAGsY,GACzBjB,IAAaQ,IACb3X,GAAU,KAETuG,EAAM6R,EAAO7R,EAAMpG,EAAQoG,IAAO,CAEnC,GADAkR,EAAKpF,EAAOzS,OAAO2G,GACfqR,OACA,GAAI1X,EAAMqG,EAAM+Q,EAAe,CAC3BtX,GAAUoX,CACV,YAUJ,IAPIU,OAAmBA,EAAYvR,IAC/BiR,EAAciB,GAEdb,EAAerR,GAAO+Q,GAAiBM,EAAerR,OACtDvG,GAAUoX,EACV7Q,EAAMqR,GAENA,IAAiBrR,EAAK,CACtBvG,IAAWqX,EAAWX,EAAU+B,GAASpB,EACzC9Q,GAAOrG,EAAM0X,EAAe,CAC5B,UAGJH,IAAO8B,GACPvZ,GAAUyX,EACVD,EAAcC,GACPA,IAAO6B,IACdtZ,GAAUwX,GASlB,GANIE,IACA1X,EAASiZ,EAAajZ,EAAQoY,GAASjB,IAAaQ,EAAoB,EAAI,GAAInQ,KAAKvG,IAAIf,EAAKoX,EAAgBc,GAAQrD,IAEtH7U,GAAOkY,IACPpY,GAAUqS,EAAOvS,UAAUI,EAAM,IAEjC6W,GAAcC,EAAW,CAEzB,IADAxQ,EAAQiS,EACHlS,EAAM,EAAGpG,EAASH,EAAOG,OAAQoG,EAAMpG,EAAQoG,IAChDkR,EAAKzX,EAAOJ,OAAO2G,GACnBC,GAAgB,MAAPiR,GAAqB,MAAPA,EAAaX,EAASW,CAEjDzX,GAASwG,EAGb,GADArG,EAAS0W,EAAS1W,OAEd,IAAKoG,EAAM,EAAGA,EAAMpG,EAAQoG,IACxBvG,EAASA,EAAON,QAAQ2Z,EAAaxC,EAAStQ,IAI1D,MAAOvG,GAvcd,GAycOiZ,GAiCAD,EAWAnG,EApfA6C,EAAmB,gGAAiGiD,EAAuB,oBAAqBO,EAAgB,uCAAwCO,EAAc,MAAOhB,EAAQ,GAAIF,EAAQ,IAAKiB,EAAQ,IAAKF,EAAQ,IAAKC,EAAO,IAAKF,EAAc,KAAMM,EAAK,QAASC,KAAoB/G,QACtWpS,IAAMkO,SAAS,UACXe,KAAMiK,EACN5E,cACI6B,SAAU,MACV4B,SAAU,EACVqB,IAAK,IACLC,IAAK,IACLC,WAAY,GACZjB,SACIlC,SACI,OACA,OAEJ4B,SAAU,EACVqB,IAAK,IACLC,IAAK,IACLC,WAAY,GACZjD,OAAQ,KAEZ+B,UACInJ,KAAM,YACNsK,KAAM,MACNpD,SACI,OACA,MAEJ4B,SAAU,EACVqB,IAAK,IACLC,IAAK,IACLC,WAAY,GACZjD,OAAQ,MAGhBzB,WACIC,UACIC,MACIQ,OACI,SACA,SACA,UACA,YACA,WACA,SACA,YAEJF,WACI,MACA,MACA,MACA,MACA,MACA,MACA,OAEJoE,YACI,KACA,KACA,KACA,KACA,KACA,KACA,OAGRzE,QACIO,OACI,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,YAEJF,WACI,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,QAGRS,IACI,KACA,KACA,MAEJC,IACI,KACA,KACA,MAEJd,UACIyE,EAAG,WACHC,EAAG,sBACHC,EAAG,iCACH3U,EAAG,mBACH4U,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,8BACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,EACVC,gBAAiB,QAuB7Bxa,GAAMqU,QAAU,SAAUoG,GACtB,GAA+BpG,GAA3BnG,EAAWlO,GAAMkO,QACrB,OAAIuM,KAAgB/b,EAKTwP,EAASsG,SAJhBH,EAAUD,EAAYqG,IAAgBvM,EAASgL,GAC/C7E,EAAQM,SAAWN,EAAQO,UAAUC,SACrC3G,EAASsG,QAAUH,EAFnBA,IAORrU,GAAMoU,YAAcA,EACpBpU,GAAMuU,WAAaA,EACnBvU,GAAMqU,QAAQ6E,GA0SVV,EAAe,SAAUjZ,EAAQoY,EAAOlY,EAAK6U,GAA9B,GAIXqC,GAASE,EACT/Q,EAAKwL,EAAOvL,EACZ2U,EALAvD,EAAe5X,EAAO8G,QAAQiO,EAAawD,IAC3C6C,EAAarG,EAAagF,UAAU1K,QACpC0K,EAAYqB,EAAWC,OAO3B,IAHAnb,EAAM0X,OAAsBA,EAAe1X,EAAM,EACjDkX,EAAUpX,EAAOF,UAAUsY,EAAOlY,GAClCoX,EAAgBF,EAAQjX,OACpBmX,GAAiByC,EAAW,CAG5B,IAFAxT,EAAM+Q,EACNvF,KACOxL,MAQH,GAPAC,EAAQ4Q,EAAQtX,UAAUyG,EAAMwT,EAAWxT,GACvCC,GACAuL,EAAMX,KAAK5K,GAEfD,GAAOwT,EACPoB,EAAeC,EAAWC,QAC1BtB,EAAYoB,IAAiBhc,EAAYgc,EAAepB,EACtC,IAAdA,EAAiB,CACbxT,EAAM,GACNwL,EAAMX,KAAKgG,EAAQtX,UAAU,EAAGyG,GAEpC,OAGR6Q,EAAUrF,EAAM7I,UAAUzJ,KAAKsV,EAAayE,IAC5CxZ,EAASA,EAAOF,UAAU,EAAGsY,GAAShB,EAAUpX,EAAOF,UAAUI,GAErE,MAAOF,IAEPgZ,EAAQ,SAAUxS,EAAOmQ,EAAWQ,GASpC,MARAR,GAAYA,GAAa,EACzBnQ,GAAQA,GAAAA,GAAiBhH,MAAM,KAC/BgH,EAAQgB,KAAKwR,QAAQxS,EAAM,GAAK,KAAOA,EAAM,IAAMA,EAAM,GAAKmQ,EAAYA,KACtEQ,IACA3Q,GAASA,GAEbA,GAAQA,GAAAA,GAAiBhH,MAAM,KAC/BgH,IAAUA,EAAM,GAAK,KAAOA,EAAM,IAAMA,EAAM,GAAKmQ,GAAaA,IACzDnQ,EAAM8U,QAAQ9T,KAAK+T,IAAI5E,EAAW,MAEzC9D,EAAW,SAAUrM,EAAOgV,EAAK1G,GACjC,GAAI0G,EAAK,CACL,GAAmC,kBAA/B5B,EAAenI,KAAKjL,GACpB,MAAO0O,GAAW1O,EAAOgV,EAAK1G,EAC3B,UAAWtO,KAAUyI,GACxB,MAAOwH,GAAajQ,EAAOgV,EAAK1G,GAGxC,MAAOtO,KAAUrH,EAAYqH,EAAQ,IAEzC/F,GAAM4R,OAAS,SAAUmJ,GACrB,GAAIC,GAAS9X,SACb,OAAO6X,GAAI9b,QAAQqP,GAAc,SAAUlO,EAAO6a,EAAOC,GACrD,GAAInV,GAAQiV,EAAOG,SAASF,EAAO,IAAM,EACzC,OAAO7I,GAASrM,EAAOmV,EAAoBA,EAAkB7b,UAAU,GAAK,OAGpFW,GAAMob,eAAiB,SAAUxJ,GAI7B,MAH2B,QAAvBA,EAAOhD,MAAM,EAAG,KAChBgD,EAASA,EAAOhD,MAAM,EAAGgD,EAAOlS,OAAS,IAEtCkS,GAEX5R,GAAMqb,eAAiB,WACnB,IACI,MAAO3V,UAAS4V,cAClB,MAAOxK,GACL,MAAOpL,UAASmC,gBAAgByT,gBAGxCtb,GAAMub,OAAShD,EACfvY,GAAMsB,YAAc,SAAUzB,EAAS2b,GACnC,MAAOhd,GAAEqB,GAASwB,WAAWma,IAAiB,IAAU,GAE5Dxb,GAAMwB,aAAe,SAAU3B,EAAS2b,GACpC,MAAOhd,GAAEqB,GAAS0B,YAAYia,IAAiB,IAAU,GAE7Dxb,GAAMoS,SAAWA,KAEpB,WA8CG,QAASqJ,GAAW1V,EAAO4R,EAAOlY,GAC9B,QAASsG,GAAS4R,GAAS5R,GAAStG,GAExC,QAASic,GAAoBC,GACzB,MAAOA,GAAWxc,OAAO,GAE7B,QAASyc,GAAeC,GACpB,MAAOrd,GAAEsd,IAAID,EAAaH,GAE9B,QAASK,GAAUrH,EAAMsH,GAChBA,GAA6B,KAApBtH,EAAKe,YACff,EAAKuH,SAASvH,EAAKe,WAAa,GAGxC,QAASyG,GAAWvY,GAEhB,IADA,GAAImC,GAAM,EAAGpG,EAASiE,EAAKjE,OAAQyc,KAC5BrW,EAAMpG,EAAQoG,IACjBqW,EAAMrW,IAAQnC,EAAKmC,GAAO,IAAIb,aAElC,OAAOkX,GAEX,QAASC,GAAeC,GACpB,GAAuBhZ,GAAnBiZ,IACJ,KAAKjZ,IAAYgZ,GACbC,EAAajZ,GAAY6Y,EAAWG,EAAUhZ,GAElD,OAAOiZ,GAEX,QAASC,GAAWxW,EAAO6L,EAAQyC,EAASmI,GACxC,IAAKzW,EACD,MAAO,KAEX,IA4CySiR,GAAIyF,EAAO/c,EAAQyW,EAASuG,EAAQC,EAAKC,EAASC,EAAeC,EAAeC,EAAaC,EAAeC,EAAS7c,EA5C1Z8c,EAAY,SAAU9c,GAElB,IADA,GAAI6C,GAAI,EACD2O,EAAO9L,KAAS1F,GACnB6C,IACA6C,GAKJ,OAHI7C,GAAI,IACJ6C,GAAO,GAEJ7C,GACRka,EAAY,SAAUjX,GACrB,GAAIkX,GAAK/S,EAAanE,IAAahC,OAAO,UAAYgC,EAAO,KAAM9F,EAAQ2F,EAAMsX,OAAOC,EAAUpX,GAAM9F,MAAMgd,EAC9G,OAAIhd,IACAA,EAAQA,EAAM,GACdkd,GAAYld,EAAMV,OACXyb,SAAS/a,EAAO,KAEpB,MACRmd,EAAiB,SAAUjI,EAAOkI,GAEjC,IADA,GAAkCvO,GAAMwO,EAA2CC,EAA/Eza,EAAI,EAAGvD,EAAS4V,EAAM5V,OAA0Bie,EAAc,EAAGC,EAAW,EACzE3a,EAAIvD,EAAQuD,IACfgM,EAAOqG,EAAMrS,GACbwa,EAAaxO,EAAKvP,OAClBge,EAAW3X,EAAMsX,OAAOC,EAAUG,GAC9BD,IACAE,EAAWA,EAASzY,eAEpByY,GAAYzO,GAAQwO,EAAaE,IACjCA,EAAcF,EACdG,EAAW3a,EAGnB,OAAI0a,IACAL,GAAYK,EACLC,EAAW,GAEf,MACRC,EAAe,WACd,GAAI1W,IAAS,CAKb,OAJIpB,GAAM5G,OAAOme,KAAc1L,EAAO9L,KAClCwX,IACAnW,GAAS,GAENA,GACRwN,EAAWN,EAAQO,UAAUC,SAAUiJ,EAAO,KAAMC,EAAQ,KAAMC,EAAM,KAAMhC,EAAQ,KAAM9G,EAAU,KAAM+I,EAAU,KAAMC,EAAe,KAAMpY,EAAM,EAAGwX,EAAW,EAAG3E,GAAU,EAAOjE,EAAO,GAAIvQ,MAAQqW,EAAkB7F,EAAS6F,iBAAmB,KAAM2D,EAAczJ,EAAKc,aAU1R,KATK5D,IACDA,EAAS,KAEbuE,EAAUxB,EAASK,SAASpD,GACxBuE,IACAvE,EAASuE,GAEbvE,EAASA,EAAO7S,MAAM,IACtBW,EAASkS,EAAOlS,OACToG,EAAMpG,EAAQoG,IAEjB,GADAkR,EAAKpF,EAAO9L,GACR6S,EACW,MAAP3B,EACA2B,GAAU,EAEVkF,QAGJ,IAAW,MAAP7G,EAAY,CAKZ,GAJAyF,EAAQS,EAAU,KACbvI,EAASyJ,aACVzJ,EAASyJ,WAAahC,EAAezH,EAASG,OAEtC,OAARkJ,GAAgBvB,EAAQ,EACxB,QAGJ,IADAuB,EAAMvB,EAAQ,EAAIU,EAAU,GAAKI,EAAe5I,EAASyJ,WAAoB,GAAT3B,EAAa,YAAc,UAAU,GAC7F,OAARuB,GAAgBvC,EAAWuC,EAAK,EAAG,IACnC,MAAO,UAER,IAAW,MAAPhH,EAAY,CAMnB,GALAyF,EAAQS,EAAU,KACbvI,EAAS0J,eACV1J,EAAS0J,aAAejC,EAAezH,EAASI,SAEpDgJ,EAAQtB,EAAQ,EAAIU,EAAU,GAAKI,EAAe5I,EAAS0J,aAAsB,GAAT5B,EAAa,YAAc,UAAU,GAC/F,OAAVsB,GAAkBtC,EAAWsC,EAAO,EAAG,IACvC,MAAO,KAEXA,IAAS,MACN,IAAW,MAAP/G,EAAY,CAGnB,GAFAyF,EAAQS,EAAU,KAClBY,EAAOX,EAAUV,GACJ,OAATqB,EACA,MAAO,KAEE,IAATrB,IAC+B,gBAApBjC,KACPA,EAAkB2D,EAAchD,SAASX,EAAiB,KAE9DsD,EAAOK,EAAcA,EAAc,IAAML,EACrCA,EAAOtD,IACPsD,GAAQ,UAGb,IAAW,MAAP9G,GAMP,GALAkG,EAAU,KACVlB,EAAQmB,EAAU,GACL,IAATnB,IACAA,EAAQ,GAEE,OAAVA,GAAkBP,EAAWO,EAAO,EAAG,IACvC,MAAO,UAER,IAAW,MAAPhF,GAGP,GAFAkG,EAAU,KACVlB,EAAQmB,EAAU,GACJ,OAAVnB,GAAkBP,EAAWO,EAAO,EAAG,IACvC,MAAO,UAER,IAAW,MAAPhF,GAGP,GAFAkG,EAAU,KACVhI,EAAUiI,EAAU,GACJ,OAAZjI,GAAoBuG,EAAWvG,EAAS,EAAG,IAC3C,MAAO,UAER,IAAW,MAAP8B,GAGP,GAFAkG,EAAU,KACVe,EAAUd,EAAU,GACJ,OAAZc,GAAoBxC,EAAWwC,EAAS,EAAG,IAC3C,MAAO,UAER,IAAW,MAAPjH,GASP,GARAyF,EAAQS,EAAU,KAClB9c,EAAQ2F,EAAMsX,OAAOC,EAAUb,GAAOrc,MAAMiK,EAAa,IACzD6T,EAAef,EAAUV,GACJ,OAAjByB,IACAA,EAAe5T,WAAW,KAAOlK,EAAM,GAAI,IAC3C8d,EAAele,GAAMub,OAAO2C,EAAc,GAC1CA,GAAgB,KAEC,OAAjBA,GAAyBzC,EAAWyC,EAAc,EAAG,KACrD,MAAO,UAER,IAAW,MAAPlH,GASP,GARAyF,EAAQS,EAAU,KAClBL,EAAgBlI,EAASkB,GACzBiH,EAAgBnI,EAASmB,GACX,IAAV2G,IACAI,EAAgBjB,EAAeiB,GAC/BC,EAAgBlB,EAAekB,IAEnCJ,EAASa,EAAeT,IACnBJ,IAAWa,EAAeV,GAC3B,MAAO,UAER,IAAW,MAAP7F,EAAY,CAGnB,GAFA2F,GAAM,EACNF,EAAQS,EAAU,KACgB,MAA9BnX,EAAMsX,OAAOC,EAAU,GAAY,CACnCO,GACA,UAGJ,GADAjB,EAAU7W,EAAMsX,OAAOC,EAAU,GAAGld,MAAMqc,EAAQ,EAAI6B,EAAqBC,IACtE3B,EACD,MAAO,KAWX,IATAA,EAAUA,EAAQ,GAAG7d,MAAM,KAC3Bge,EAAcH,EAAQ,GACtBI,EAAgBJ,EAAQ,IACnBI,GAAiBD,EAAYrd,OAAS,IACvC4d,EAAWP,EAAYrd,OAAS,EAChCsd,EAAgBD,EAAY1d,UAAUie,GACtCP,EAAcA,EAAY1d,UAAU,EAAGie,IAE3CP,EAAc5B,SAAS4B,EAAa,IAChCtB,EAAWsB,MAAkB,IAC7B,MAAO,KAEX,IAAIN,EAAQ,IACRO,EAAgBJ,EAAQ,GAAG,GAAKI,EAChCA,EAAgB7B,SAAS6B,EAAe,IACpCwB,MAAMxB,IAAkBvB,EAAWuB,MAAoB,KACvD,MAAO,UAGZ,IAAW,MAAPhG,EACP2B,GAAU,EACVkF,QACG,KAAKA,IACR,MAAO,KAInB,OAAIrB,KAAW,QAAQxa,KAAK+D,EAAMsX,OAAOC,IAC9B,MAEXL,EAAoB,OAAVjB,GAA8B,OAAZ9G,GAAoB+I,GAAW,KAC9C,OAATH,GAA2B,OAAVC,GAA0B,OAARC,GAAgBf,GACnDa,EAAOK,EACPJ,EAAQrJ,EAAKa,WACbyI,EAAMtJ,EAAKS,YAEE,OAAT2I,IACAA,EAAOK,GAEC,OAARH,IACAA,EAAM,IAGVtB,GAAUV,EAAQ,KAClBA,GAAS,IAETW,GACII,IACAf,IAAUe,GAEVC,IACA9H,IAAY8H,GAEhBjX,EAAQ,GAAI5B,MAAKA,KAAKwY,IAAImB,EAAMC,EAAOC,EAAKhC,EAAO9G,EAAS+I,EAASC,MAErEnY,EAAQ,GAAI5B,MAAK2Z,EAAMC,EAAOC,EAAKhC,EAAO9G,EAAS+I,EAASC,GAC5DnC,EAAUhW,EAAOiW,IAEjB8B,EAAO,KACP/X,EAAM0Y,YAAYX,GAElB/X,EAAMoP,YAAc6I,GAAOrB,IAAQje,EAC5B,KAEJqH,GAEX,QAAS2Y,GAA2BnX,GAChC,GAAIC,GAA+B,MAAxBD,EAAO8V,OAAO,EAAG,MAAkB,CAG9C,OAFA9V,GAASA,EAAOlI,UAAU,GAC1BkI,EAA6C,GAApC4T,SAAS5T,EAAO8V,OAAO,EAAG,GAAI,IAAWlC,SAAS5T,EAAOlI,UAAU,GAAI,IACzEmI,EAAOD,EAElB,QAASoX,GAAkBtK,GAA3B,GAIQuK,GAAgBC,EAAW/Y,EAH3BpG,EAASa,GAAKC,IAAIse,EAAiBpf,OAAQqf,EAAiBrf,QAC5DiV,EAAWN,EAAQM,UAAYN,EAAQO,UAAUC,SACjDG,EAAWL,EAASK,SAEpBgK,IACJ,KAAKlZ,EAAM,EAAGA,EAAMpG,EAAQoG,IAAO,CAE/B,IADA8Y,EAAiBE,EAAiBhZ,GAC7B+Y,EAAY,EAAGA,EAAYD,EAAelf,OAAQmf,IACnDG,EAAQrO,KAAKqE,EAAS4J,EAAeC,IAEzCG,GAAUA,EAAQC,OAAOF,EAAiBjZ,IAE9C,MAAOkZ,GAEX,QAASE,GAAkBnZ,EAAOiZ,EAAS3K,EAASmI,GAApD,GAIQ1W,GACA4O,EACAhV,EACAyf,CANJ,IAAmC,kBAA/BhG,EAAenI,KAAKjL,GACpB,MAAOA,EAMX,IAJID,EAAM,EACN4O,EAAO,KAGP3O,GAAiC,IAAxBA,EAAMM,QAAQ,QACvBqO,EAAO0K,EAAWjH,KAAKpS,IAUnB,MARA2O,GAAOA,EAAK,GACZyK,EAAWE,EAAalH,KAAKzD,EAAKrV,UAAU,IAC5CqV,EAAO,GAAIvQ,MAAKgX,SAASzG,EAAM,KAC3ByK,IACAA,EAAWT,EAA2BS,EAAS,IAC/CzK,EAAO1U,GAAMsf,SAAS3P,MAAM+E,EAAM,GAClCA,EAAO1U,GAAMsf,SAASC,QAAQ7K,EAAM,KAAQyK,IAEzCzK,CASf,KANAL,EAAUrU,GAAMuU,WAAWF,GACtB2K,IACDA,EAAUL,EAAkBtK,IAEhC2K,EAAU7Q,GAAQ6Q,GAAWA,GAAWA,GACxCtf,EAASsf,EAAQtf,OACVoG,EAAMpG,EAAQoG,IAEjB,GADA4O,EAAO6H,EAAWxW,EAAOiZ,EAAQlZ,GAAMuO,EAASmI,GAE5C,MAAO9H,EAGf,OAAOA,GAxWX,GAAI8K,GAAyB,UAAWC,EAAiB,mBAAoBlB,EAAsB,gBAAiBD,EAAqB,uBAAwBc,EAAa,sBAAuBC,EAAe,UAAWP,OAGnN,IACA,IACA,MAGA,IACA,IACA,IACA,IACA,IACA,MAELC,IAEK,iCACA,8BACA,6BACA,0BACA,2BACA,yBACA,sBACA,qBACA,sBACA,sBACA,wBAGA,mBACA,mBACA,qBAGA,aACA,aACA,WACA,UAEL1U,GACCqV,EAAG,WACHC,EAAG,WACHC,EAAG,UACJzG,KAAoB/G,QA8T3BpS,IAAM6f,UAAY,SAAU9Z,EAAOiZ,EAAS3K,GACxC,MAAO6K,GAAkBnZ,EAAOiZ,EAAS3K,GAAS,IAEtDrU,GAAM8f,eAAiB,SAAU/Z,EAAOiZ,EAAS3K,GAC7C,MAAO6K,GAAkBnZ,EAAOiZ,EAAS3K,GAAS,IAEtDrU,GAAMmb,SAAW,SAAUpV,EAAOsO,GAC9B,GAAIlN,GAASnH,GAAMsK,WAAWvE,EAAOsO,EAIrC,OAHIlN,KACAA,EAAkB,EAATA,GAENA,GAEXnH,GAAMsK,WAAa,SAAUvE,EAAOsO,EAASzC,GACzC,IAAK7L,GAAmB,IAAVA,EACV,MAAO,KAEX,UAAWA,KAAUyI,GACjB,MAAOzI,EAEXA,GAAQA,GAAAA,EACRsO,EAAUrU,GAAMuU,WAAWF,EAC3B,IAAkL/C,GAAOiF,EAArLhX,EAAS8U,EAAQC,aAAc+D,EAAU9Y,EAAO8Y,QAASD,EAAW7Y,EAAO6Y,SAAU/B,EAAS+B,EAAS/B,OAAQ0J,EAAgB1H,EAAQhC,OAAQK,EAAW3Q,EAAMM,QAAQ,IAC5K,OAAIoZ,GAAezd,KAAK+D,IACpBA,EAAQuE,WAAWvE,EAAM9G,QAAQM,EAAO,KAAM,MAC1Cif,MAAMzY,KACNA,EAAQ,MAELA,GAEP2Q,EAAW,EACJ,MAEPA,EAAWA,KAEX3Q,EAAMM,QAAQgQ,OAAgBzE,GAAUA,EAAO3M,cAAcoB,QAAQ,SACrE9G,EAAS6Y,EACT9G,EAAQ/R,EAAO4W,QAAQ,GAAGlX,QAAQ,IAAKoX,GAAQtX,MAAM,KACjDgH,EAAMM,QAAQiL,EAAM,QAAYvL,EAAMM,QAAQiL,EAAM,SACpDvL,EAAQA,EAAM9G,QAAQqS,EAAM,GAAI,IAAIrS,QAAQqS,EAAM,GAAI,IACtDoF,GAAW,IAER3Q,EAAMM,QAAQ0Z,QACrBxJ,GAAY,EACZhX,EAAS8Y,EACThC,EAAS0J,GAEbha,EAAQA,EAAM9G,QAAQ,IAAK,IAAIA,QAAQoX,EAAQ,IAAIpX,QAAQugB,EAAwB,KAAKzgB,MAAMQ,EAAO,KAAKN,QAAQugB,EAAwB,MAAMxgB,KAAK,IAAIC,QAAQM,EAAO,KAAM,KAC9KwG,EAAQuE,WAAWvE,GACfyY,MAAMzY,GACNA,EAAQ,KACD2Q,IACP3Q,OAEAA,GAASwQ,IACTxQ,GAAS,KAENA,OA8Ld,WAAA,GAkBOia,GAQAC,EACA/f,EAA2CggB,EAAyCC,EA+FpFC,EAyGAnY,EAyDAoY,EAGAC,EAAiC3Z,CA9RrC1G,IAAQsgB,WAAa7hB,EACrBuB,GAAQugB,UAAY,SAAUC,GAC1B,GAAKjC,MAAMve,GAAQsgB,aAAgBE,EAE5B,CACH,GAAyCtZ,GAArCuZ,EAAMhb,SAASib,cAAc,MAMjC,OALAD,GAAI5e,MAAM8e,QAAU,oEACpBF,EAAIG,UAAY,SAChBnb,SAASob,KAAKC,YAAYL,GAC1BzgB,GAAQsgB,WAAapZ,EAASuZ,EAAIM,YAAcN,EAAI7Z,YACpDnB,SAASob,KAAKG,YAAYP,GACnBvZ,EARP,MAAOlH,IAAQsgB,YAWvBtgB,GAAQuG,MAAQ,SAAU3G,GACtB,MAAOrB,GAAEqB,GAASqhB,QAAQ,UAAUxhB,OAAS,GAE7CsgB,EAAQta,SAASib,cAAc,QACnC,KACIX,EAAMa,UAAY,qBAClB5gB,GAAQkhB,gBAAiB,EAC3B,MAAOrQ,GACL7Q,GAAQkhB,gBAAiB,EAE7BlhB,GAAQmhB,MAAQ,gBAAkB3iB,GAC9BwhB,EAAWva,SAASmC,gBAAgB/F,MACpC5B,EAAcD,GAAQC,aAAc,EAAOggB,EAAajgB,GAAQigB,YAAa,EAAOC,EAAe,eAAiB1hB,GAAS4iB,YAAYzR,aAC7I3P,GAAQqhB,QAAU,mBAAqB7iB,IAAU,OAAS,IAAIA,GAAO8iB,iBAAqB,kBAAoBtB,IAAY,iBAAmBA,GAC7IhgB,GAAQuhB,WAAa,YAAcvB,IAAY,kBAAoBA,IAAY,cAAgBA,GAC/Fpa,IACI,MACA,SACA,IACA,MACD,WAAA,GAGS4b,GAFJC,EAASvZ,GAAAA,KAAiBwZ,QAAwB3B,GAAMle,MAAM4f,EAAS,gBAAkB/Y,EAC7F,IAAIgZ,SAAyB3B,GAAMle,MAAM4f,EAAS,eAAiB/Y,GAW/D,MAVI8Y,GAAYC,EAAOzc,cACvBib,GACIngB,IAAkB,MAAb0hB,EAAoB,IAAMA,EAAY,IAAM,GACjDC,OAAQA,EACRE,MAAqB,MAAdH,GAAmC,WAAdA,EAAyBA,EAAY,IAEjEE,IACAzhB,EAAcggB,EACdhgB,EAAY0hB,MAAQ1hB,EAAY0hB,MAAQ1hB,EAAY0hB,MAAQ,gBAAkB,kBAE3E,IAGf5B,EAAQ,KACR/f,GAAQigB,WAAaA,EACrBjgB,GAAQC,YAAcA,EACtBD,GAAQ4hB,iBAAmBpjB,EAAOojB,mBAAqBnjB,EAAY,EAAID,EAAOojB,gBAC9E,KACI5hB,GAAQ6hB,YAAcrjB,EAAO4C,YAAc5C,EAAOsjB,OAAStjB,EAAOsjB,OAAOC,WAAavjB,EAAOwjB,WAC7FhiB,GAAQiiB,aAAezjB,EAAO8C,aAAe9C,EAAOsjB,OAAStjB,EAAOsjB,OAAOI,YAAc1jB,EAAO2jB,YAClG,MAAOtR,GACL7Q,GAAQ6hB,YAAcrjB,EAAOsjB,OAAOC,WACpC/hB,GAAQiiB,aAAezjB,EAAOsjB,OAAOI,YAEzCliB,GAAQoiB,SAAW,SAAUC,GAAV,GACCC,GA+BP9d,EA/BL+d,GAAK,EAAqBpiB,KAAYqiB,GAAmB,iBAAiBzgB,KAAKsgB,GAAKI,GAChFC,GAAI,gDACJC,KAAM,+BACNC,QAAS,qEACTC,OAAQ,2CACRC,KAAM,kCACNC,MAAO,2CACPC,MAAO,gCACPC,WAAY,oDACZC,SAAU,kDACVC,QAAS,gCACTC,MAAO,2CACPC,SAAU,gDACVC,KAAM,8CACPC,GACCC,IAAK,sBACLZ,QAAS,kBACTK,WAAY,wBACZE,QAAS,UACTT,GAAI,KACJe,KAAM,uBACNV,MAAO,SACRW,GAAkBC,OAAQ,uBAAyBC,GAClDC,MAAO,eACPC,QAAS,eACTC,QAAS,kBACTC,aAAc,qBACdC,GAAI,uBACJ5D,OAAQ,gBACR7Z,OAAQ,UAEhB,KAAShC,IAASie,GACd,GAAIA,EAAS7d,eAAeJ,KACxBrE,EAAQkiB,EAAGliB,MAAMsiB,EAASje,KACf,CACP,GAAa,WAATA,GAAsB,WAAa0f,WACnC,OAAO,CAEX3B,MACAA,EAAG4B,OAAS3f,EACZ+d,EAAGoB,OAASpf,EAAOC,EAAOkf,GAAe,GACzCnB,EAAGphB,QAAUoD,EAAO8d,EAAIuB,EAAY,WACpCrB,EAAGvT,KAAOzK,EAAOC,EAAO+e,GACxBhB,EAAGA,EAAGvT,OAAQ,EACduT,EAAG6B,aAAejkB,EAAM,GACxBoiB,EAAGD,cAAgBniB,EAAM,IAAM,KAAKnB,QAAQ,IAAK,KACjDsjB,EAAeC,EAAGD,aAAatjB,QAAQ,IAAK,IAAIoe,OAAO,EAAG,GAC1DmF,EAAG8B,YAAc9B,EAAG6B,aAAe9B,EAAmBte,MAAM,GAAKse,EAAa7iB,OAAS,EAAI6iB,EAAa7iB,OAAS,IAAIV,KAAK,KAC1HwjB,EAAG+B,cAAiB9lB,GAAO+lB,WAAajgB,UAAoB9F,GAAO8lB,UAAYhgB,GAC/Eie,EAAGiC,QAAUhmB,EAAO0lB,UAAUO,YAAc,mBAAmB1iB,KAAKvD,EAAOkmB,SAASC,WAAapC,EAAG+B,QAChG/B,EAAGK,UAAY5iB,GAAQ4hB,iBAAmB,KAAOW,EAAG8B,YAAc,KAAO7B,KAAqBxiB,GAAQ6hB,YAAc,KAAO7hB,GAAQiiB,aAAe,OAClJM,EAAGoB,OAASnf,EAEhB,OAIZ,MAAO+d,IAEPpC,EAAWngB,GAAQmgB,SAAWngB,GAAQoiB,SAAS8B,UAAUU,WAC7D5kB,GAAQ6kB,mBAAqB1E,EAASuC,GAAKZ,OAAOhhB,MAAQ,IAAM,EAChEd,GAAQ8kB,oBAAqB,GACzB3E,EAASqD,KAAOrD,EAASyC,SAAWzC,EAASiE,aAAe,GAAKjE,EAASuC,MAC1E1iB,GAAQ8kB,mBAAqB3E,GAEjCngB,GAAQ+kB,aAAe,WACnB,GAAI/kB,GAAQmhB,MAAO,CACf,GAAIhB,EAASqD,IACT,OAAO,CAEX,IAAIrD,EAASyC,QACT,OAAK5iB,GAAQmB,QAAQkf,UAGjBrgB,GAAQmB,QAAQoB,QAAU,OAGrBhE,EAAE,uBAAuB8M,KAAK,YAAc,IAAIlL,MAAM,qBAGvE,OAAO,GAEXH,GAAQglB,qBAAuBhlB,GAAQmhB,SAAWnhB,GAAQmgB,SAASqD,KAAOxjB,GAAQmgB,SAASyC,SAC3F5iB,GAAQilB,cAAgB,SAAU5C,GAAV,GASX7d,GARLrD,GAAU,EAAOhB,KAAYyjB,GACzBsB,KAAM,uBACN1e,OAAQ,+BACR2e,OAAQ,yBACRC,MAAO,sCACP/iB,KAAM,mCACNqE,QAAS,iCAEjB,KAASlC,IAASof,GACd,GAAIA,EAAWhf,eAAeJ,KAC1BrE,EAAQkiB,EAAGliB,MAAMyjB,EAAWpf,KACjB,CACPrD,KACAA,EAAQqD,IAAS,EACjBrD,EAAQhB,EAAM,GAAG6E,cAAclG,MAAM,KAAK,GAAGA,MAAM,KAAK,KAAM,EAC9DqC,EAAQoB,QAAU2Y,SAASzV,SAAS2a,cAAgBjgB,EAAM,GAAI,GAC9D,OAIZ,MAAOgB,IAEXnB,GAAQmB,QAAUnB,GAAQilB,cAAcf,UAAUU,WAClD5kB,GAAQqlB,sBAAwB,WAC5B,GAAIC,IACAvW,OAAMtJ,SAAS8f,uBAAwB9f,SAAS8f,sBAAsB,QACtEC,MAAK/f,SAAS8f,uBAAwB9f,SAAS8f,sBAAsB,OACrEE,QAAOhgB,SAAS8f,uBAAwB9f,SAAS8f,sBAAsB,SAS3E,OAPIvlB,IAAQmB,QAAQkf,SAChBiF,EAASG,OAAQ,EACbzlB,GAAQmB,QAAQoB,SAAW,KAC3B+iB,EAASvW,MAAO,EAChBuW,EAASE,KAAM,IAGhBF,GAEXtlB,GAAQ0lB,UAAY1lB,GAAQqlB,wBAC5BrlB,GAAQ2lB,UAAY,WAAA,GAERxkB,GACAykB,EACAC,CAHR,KAOI,MANI1kB,GAAUnB,GAAQmB,QAClBykB,EAAsB,EACtBC,EAAQpgB,SAASmC,gBACjBzG,EAAQkB,MAA2B,IAAnBlB,EAAQoB,SAAiBsjB,EAAMC,aAAeD,EAAME,eAAiB/lB,GAAQmhB,QAC7FyE,EAAsB5lB,GAAQugB,aAE3BvgB,GAAQmhB,MAAQ0E,EAAMhf,YAAcrI,EAAOwjB,WAAa7gB,EAAQkB,MAAQlB,EAAQoB,SAAW,KAAOiF,KAAOhJ,GAAQiH,SAASmC,gBAAgBmZ,YAAc6E,IAAwBpe,KAAOhJ,GAAQwjB,WAAa,EACrN,MAAOnR,GACL,MAAO,KAGf7Q,GAAQgmB,iBAAoD,IAA1BhG,EAASiG,iBAAkCjmB,GAAQmB,QAAQkB,MAAQrC,GAAQmB,QAAQoB,QAAU,GAC9H,SAAUpB,GACP,GAAI+kB,GAAW,GAAIC,EAAa5nB,EAAEkH,SAASmC,iBAAkBwc,EAAelJ,SAAS/Z,EAAQoB,QAAS,GAClGpB,GAAQkB,KACR6jB,EAAW,KACJ/kB,EAAQuF,QACfwf,EAAW,KACJ/kB,EAAQgkB,OACfe,EAAW,SACJ/kB,EAAQqF,OACf0f,EAAW,SACJ/kB,EAAQikB,MACfc,EAAW,QACJ/kB,EAAQ+jB,OACfgB,EAAW,QAEXA,IACAA,EAAW,KAAOA,EAAW,MAAQA,EAAW9B,GAEhDpkB,GAAQmgB,WACR+F,GAAY,aAEXlmB,GAAQuhB,aACT2E,GAAY,iBAEhBC,EAAWlkB,SAASikB,IACtBlmB,GAAQmB,SACVnB,GAAQomB,aAAe3gB,SAASmC,gBAAgBye,iBAC5Cre,EAAQvC,SAASib,cAAc,SACnC1gB,GAAQsmB,YAAc,eAAiBte,GACvChI,GAAQumB,oBAAsB,oBAAsBve,GACpDhI,GAAQgI,MAAQ,WAeZ,IAfY,GAcRZ,GAbAof,GACA,SACA,OACA,OACA,QACA,OACA,WACA,kBAEA/mB,EAAS+mB,EAAM/mB,OACfqG,EAAQ,OACRoB,KACArB,EAAM,EAEHA,EAAMpG,EAAQoG,IACjBuB,EAAOof,EAAM3gB,GACbmC,EAAMye,aAAa,OAAQrf,GAC3BY,EAAMlC,MAAQA,EACdoB,EAAOE,EAAKpI,QAAQ,IAAK,KAAsB,SAAfgJ,EAAMZ,MAAmBY,EAAMlC,QAAUA,CAE7E,OAAOoB,MAEXc,EAAMnG,MAAM8e,QAAU,cACtB3gB,GAAQ0mB,WAAa1e,EAAMnG,MAAM6kB,SACjC1e,EAAQ,KACRhI,GAAQ2mB,WAAa,WAAA,GAMR3jB,GALL4jB,EAAY,IACZC,IACI7L,MAAO,EACP8L,MAAO,KAEf,KAAS9jB,EAAI,EAAGA,EAAI4jB,EAAW5jB,IAC3B6jB,EAAOnW,MACHsK,MAAOhY,EACP8jB,MAAO,KAMf,OAHAD,GAAOE,KAAK,SAAUhc,EAAGC,GACrB,MAAOD,GAAE+b,MAAQ9b,EAAE8b,MAAQ,EAAI/b,EAAE+b,MAAQ9b,EAAE8b,SAAa,IAEjC,IAApBD,EAAO,GAAG7L,SAErBhb,GAAQgnB,gBAAkB9G,EAAa+G,uBAAyB/G,EAAagH,oBAAsBhH,EAAaiH,mBAAqBjH,EAAakH,kBAAoBlH,EAAa8G,iBAAmB9G,EAAavD,SAAW,SAAUzQ,GAEpO,IADA,GAAImb,GAAW5hB,SAAS6hB,kBAAoBpf,KAAKqf,YAAc9hB,UAAU6hB,iBAAiBpb,OAAkB3N,EAAE2N,GAAWlJ,EAAIqkB,EAAS5nB,OAC/HuD,KACH,GAAIqkB,EAASrkB,IAAMkF,KACf,OAAO,CAGf,QAAO,GAEXlI,GAAQwnB,WAAa,cAAgBhpB,GACrCwB,GAAQynB,UAAYjpB,EAAOkpB,SAAWlpB,EAAOkpB,QAAQD,UACjDrH,EAAe3a,SAAS2a,aAC5BpgB,GAAQ2nB,WAAa,gBAAkBnpB,MAAYwB,GAAQmB,QAAQkB,QAAU+d,GAAgBA,GAAgB,IAC7GpgB,GAAQ4nB,eAAiB,mBAAqBppB,GAAOiH,SACjD4a,EAASrgB,GAAQmB,QAAQkf,OAAQ3Z,EAAU1G,GAAQmB,QAAQuF,QAC/D1G,GAAQ0H,YAAc2Y,GAAU7hB,EAAOqpB,eACvC7nB,GAAQyH,UAAY4Y,IAAW3Z,GAAWlI,EAAOspB,aACjD9nB,GAAQ+nB,oBAAsB5H,IAAangB,GAAQmhB,OAASnhB,GAAQ0H,YAAc1H,GAAQyH,aA6B1FsF,GACAvM,MAAQgI,QAAS,SACjB/H,OAAS+H,QAAS,QAClBwf,MAAQxf,QAAS,MACjByf,IAAMzf,QAAS,QACfhB,KAAOgB,QAAS,UAChB9H,QAAU8H,QAAS,OACnB0f,MAAQ1f,QAAS,OACjB2f,KAAO3f,QAAS,OAYhBP,KACJ1J,EAAEqK,OAAOX,GACLmgB,SAAS,EACThgB,QAAS,SAAUxI,GACfsI,KAAKtI,QAAUrB,EAAEqB,IAErByJ,QAAS,SAAUzJ,EAAS0I,GACnB1I,EAAQoC,GAAG,aACZpC,EAAQE,KAAM8C,QAAShD,EAAQ8D,KAAK,eAAiB,UAAW5D,IAAI,WAEpEwI,EAAQU,MACRpJ,EAAQ8D,KAAK,aAAc9D,EAAQE,IAAI,YAAYkJ,OAEnDV,EAAQO,MACRP,EAAQO,OAERP,EAAQW,kBACRX,EAAQW,iBAAiBrJ,GAE7BA,EAAQyoB,WAEZC,QAAS,WACLpgB,KAAKkgB,SAAU,EACflgB,KAAKmB,QAAUnB,KAAKqgB,aAExBC,OAAQ,WACJtgB,KAAKkgB,SAAU,EACflgB,KAAKmB,QAAUnB,KAAKugB,mBAG5BxgB,EAAQsgB,YAActgB,EAAQoB,QAsDxB,gBAAkB9K,GAAE6N,IACtBxD,GAAOrK,EAAE6N,IACLsc,UAAW,SAAUC,EAAYC,GAC7B,MAAO1gB,MAAK2gB,KAAKF,EAAYC,IAEjCE,aAAc,SAAUxgB,EAASC,EAAUC,EAASC,GAChD,MAAOS,GAAQhB,KAAMI,EAASC,EAAUC,EAASC,IAErDsgB,cAAe,SAAUxf,EAASjB,GAC9B,MAAOvI,IAAMuJ,YAAYpB,KAAMqB,EAASjB,GAAS,IAErD0gB,iBAAkB,SAAUzf,EAASjB,GACjC,MAAOvI,IAAMuJ,YAAYpB,KAAMqB,EAASjB,GAAS,IAErD2gB,iBAAkB,SAAU1f,EAASjB,EAAS4gB,GAC1C,MAAOnpB,IAAMuJ,YAAYpB,KAAMqB,EAASjB,EAAS4gB,MAIzDxf,EAAY,KAAMC,EAAW,KAAME,EAAc,KAAMC,EAAa,KAAMF,EAAW,KAIrFoD,EAAc,SAAU6D,GACxB,MAAOA,GAAE5B,QAETjP,GAAQmhB,QACRnU,EAAc,SAAU6D,GACpB,GAAIsY,GAAU,iBAAmBtY,GAAIA,EAAEuY,cAAcC,eAAiB,kBAAoBxY,GAAIA,EAAEwY,eAAiB,IACjH,OAAOF,GAAU1jB,SAAS6jB,iBAAiBH,EAAQ,GAAGI,QAASJ,EAAQ,GAAGK,SAAW3Y,EAAE5B,QAE3FrJ,IACI,QACA,YACA,aACA,UACA,YACA,YACA,OACD,SAAUgU,EAAG9T,GACZvH,EAAE6N,GAAGtG,GAAS,SAAU2jB,GACpB,MAAOvhB,MAAK8H,KAAKlK,EAAO2jB,OAIhCzpB,GAAQmhB,MACHnhB,GAAQmgB,UAQTngB,GAAQ0pB,UAAY,aACpB1pB,GAAQ2pB,QAAU,WAClB3pB,GAAQ4pB,UAAY,YACpB5pB,GAAQ6pB,YAAc,cACtB7pB,GAAQ8pB,MAAQ,WAChB9pB,GAAQ+pB,OAAS,sBAZjB/pB,GAAQ0pB,UAAY,uBACpB1pB,GAAQ2pB,QAAU,mBAClB3pB,GAAQ4pB,UAAY,sBACpB5pB,GAAQ6pB,YAAc,yBACtB7pB,GAAQ8pB,MAAQ,QAChB9pB,GAAQ+pB,OAAS,UASd/pB,GAAQyH,UACfzH,GAAQ4pB,UAAY,cACpB5pB,GAAQ0pB,UAAY,cACpB1pB,GAAQ2pB,QAAU,YAClB3pB,GAAQ6pB,YAAc,gBACtB7pB,GAAQ8pB,MAAQ,YAChB9pB,GAAQ+pB,OAAS,4BACV/pB,GAAQ0H,YACf1H,GAAQ4pB,UAAY,gBACpB5pB,GAAQ0pB,UAAY,gBACpB1pB,GAAQ2pB,QAAU,cAClB3pB,GAAQ6pB,YAAc,kBACtB7pB,GAAQ8pB,MAAQ,cAChB9pB,GAAQ+pB,OAAS,6BAEjB/pB,GAAQ4pB,UAAY,YACpB5pB,GAAQ0pB,UAAY,YACpB1pB,GAAQ2pB,QAAU,UAClB3pB,GAAQ6pB,YAAc,aACtB7pB,GAAQ8pB,MAAQ,QAChB9pB,GAAQ+pB,OAAS,UAEjB9c,EAAiB,SAAU+c,EAAS/Y,GAChC,GAA+B+J,GAAOnV,EAAKpG,EAAQ6P,EAA/CpI,EAAS+J,GAAa,IAAiCuL,EAAQ,CACnE,KAAK3W,EAAM,EAAGpG,EAASuqB,EAAQvqB,OAAQoG,EAAMpG,EAAQoG,IACjDyJ,EAAS0a,EAAQnkB,GACF,KAAXyJ,IACA0L,EAAQ1L,EAAOlJ,QAAQ,KACT,IAAV4U,IACIA,MACA1L,EAAS,IAAMA,GAEfkN,IACAlN,EAAS,IAAMA,EAAOlQ,UAAU,EAAG4b,GAAS,UAAY1L,EAAOlQ,UAAU4b,KAGjFwB,IACAtV,GAAUoI,GAAUzJ,EAAMpG,EAAS,EAAI,UAAY,KAG3D,OAAWuE,OAAMwY,GAAOzd,KAAK,KAAOmI,GACrCgG,EAAa,mBACpBtE,GAAO7I,IACHkqB,WACAC,8BACAC,GAAIpqB,GAAMoqB,OACVhiB,GAAIpI,GAAMoI,IAAMA,EAChBF,QAASlI,GAAMkI,SAAWA,EAC1BmiB,OAAQrqB,GAAMqqB,WACd1mB,KAAM3D,GAAM2D,SACZ2mB,QAAStqB,GAAMsqB,YACfC,QAASvqB,GAAMuqB,YACfC,aAAeC,aACfC,MACIC,OAAQ,GACRC,OAAQ,GACRC,UAAW,EACXC,IAAK,EACLC,MAAO,GACPC,IAAK,GACLC,KAAM,GACNC,GAAI,GACJC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,KAAM,GACNC,SAAU,GACVC,OAAQ,GACRC,SAAU,GACVC,GAAI,IACJC,IAAK,IACLC,IAAK,IACLC,YAAa,IACbC,aAAc,IACdC,WAAY,KAEhB9rB,QAASD,GAAMC,SAAWA,GAC1BkJ,QAASnJ,GAAMmJ,SAAWA,EAC1BiB,GAAI,GACJkB,KAAM,SAAUvF,GACZ,MAAO,QAAU/F,GAAMoK,GAAKrE,GAEhCnG,WAAYA,EACZgB,KAAMA,EACNmC,WAAYA,EACZuC,kBAAmBA,EACnBa,aAAcA,EACdI,WAAYA,EACZL,KAAMA,EACNhB,YAAaA,EACbJ,UAAWA,EACXsC,UAAWpH,GAAMoH,WAAaA,EAC9BY,aAAchI,GAAMgI,cAAgBA,EACpCuB,YAAavJ,GAAMuJ,aAAeA,EAClCyD,WAAYhN,GAAMgN,YAAcA,EAChCP,WAAYA,EACZ9N,MAAOA,EACP2N,SAAUA,EACVzB,SAAUuD,GAAM9B,EAAS+E,QAAS/E,GAClC8E,OAAQhD,GAAM9B,EAAS8E,OAAQ9E,GAC/B0H,UAAW5F,GAAMC,GAAK2F,UAAW3F,IACjCpB,YAAaA,EACbvD,WAAYA,EACZsiB,WAAY,SAAUC,GAClB,MAAOA,KAAQ9e,EAAWnL,KAAKiqB,IAEnCpgB,KAAM,SAAUqgB,EAAYC,EAAMjb,GAkB9B,MAjBAgb,GAAaA,GAAc,SAChBC,IAAQxjB,KACfuI,EAAYib,EACZA,GAAO,GAEXjb,EAAYA,GAAa,IACrBgb,GAAuC,MAAzBA,EAAW/sB,OAAO,KAChC+sB,EAAa,IAAMA,GAEnBC,GACAD,EAAaA,EAAWjtB,QAAQ,sBAAuB;AACvDitB,EAAaA,EAAWjtB,QAAQ,sBAAuB,iBACvDitB,EAAahf,EAAegf,EAAWntB,MAAM,KAAMmS,GACnDgb,EAAaA,EAAWjtB,QAAQ,aAAc,MAE9CitB,EAAahb,EAAYgb,EAEtBA,GAEXE,OAAQ,SAAUF,EAAYC,GAC1B,GAAIjlB,GAAMglB,EAAaC,CACvB,OAAOzd,IAAYxH,GAAOwH,GAAYxH,IAAYuD,SAAS,IAAK,UAAYzK,GAAM6L,KAAKqgB,EAAYC,KAEvGE,OAAQ,SAAUH,GACd,MAAOvd,IAAYud,GAAcvd,GAAYud,IAAmBzhB,SAAS,UAAWzK,GAAM6L,KAAKqgB,GAAc,WAEjHI,SAAU,SAAUJ,GAChB,OACIK,IAAKvsB,GAAMosB,OAAOF,GAClBM,IAAKxsB,GAAMqsB,OAAOH,KAG1BO,KAAM,WACF,GAAaxpB,GAAGypB,EAAZC,EAAK,EACT,KAAK1pB,EAAI,EAAGA,EAAI,GAAIA,IAChBypB,EAAyB,GAAhBnsB,GAAKmsB,SAAgB,EACrB,GAALzpB,GAAe,IAALA,GAAgB,IAALA,GAAgB,IAALA,IAChC0pB,GAAM,KAEVA,IAAY,IAAL1pB,EAAU,EAAS,IAALA,EAAmB,EAATypB,EAAa,EAAIA,GAAQta,SAAS,GAErE,OAAOua,IAEXC,aAAc,SAAUjiB,GACpB,MAAOA,GAAK1L,QAAQ,SAAU,IAAMe,GAAMsL,KAAK,QAAU,SAASsD,MAAM,OAE5Eie,kBAAmB,SAAUC,GAAV,GAGF7pB,GAFT8pB,EAAYD,EAAW/tB,MAAM,IACjC,IAAIguB,EACA,IAAS9pB,EAAI,EAAGA,EAAI8pB,EAAUrtB,OAAQuD,IACd,QAAhB8pB,EAAU9pB,KACV8pB,EAAU9pB,GAAK8pB,EAAU9pB,GAAGhE,QAAQ,8BAA+B,SAI/E,OAAO8tB,GAAU/tB,KAAK,KAAKC,QAAQ,SAAU,oBAAoB2P,MAAM,OAE3Eoe,iBAAkB,SAAUlc,GACxB,MAAO,mCAAmC9O,KAAK8O,EAAE5B,OAAO+d,UAE5DC,mBAAoB,SAAUxD,GAC1B,IAAK,GAAIzmB,GAAI,EAAGkqB,EAAMntB,GAAMkqB,QAAQxqB,OAAQuD,EAAIkqB,EAAKlqB,IACjDymB,EAAS1pB,GAAMkqB,QAAQjnB,GAE3BjD,IAAMmqB,2BAA2BxZ,KAAK+Y,IAE1C0D,aAAc,SAAUC,EAAShmB,GAC7B,GAAIimB,GAAU7uB,EAAO6uB,SAChBttB,GAAMutB,aAAiC,IAAXD,GAA0BA,EAAQE,KAC/DF,EAAQjmB,GAAQ,OAAOgmB,MAI/BjgB,GAASX,EAAW5D,QACpBC,KAAM,SAAUjJ,EAAS0I,GAAnB,GAKEklB,GAJAhe,EAAOtH,IACXsH,GAAK5P,QAAUG,GAAMoP,OAAOvP,GAASyQ,QAAQb,GAC7CA,EAAKie,QAAQ,OAAQnlB,GACrBkE,EAAWJ,GAAGvD,KAAKkI,KAAKvB,GACpBge,EAAallB,EAAUA,EAAQklB,WAAa,KAC5CA,IACAllB,EAAUM,MAAWN,GAAWklB,iBAEpCllB,EAAUkH,EAAKlH,QAAUM,IAAO,KAAU4G,EAAKlH,QAASA,GACpDklB,IACAllB,EAAQklB,WAAaA,GAEpBhe,EAAK5P,QAAQyL,KAAKtL,GAAMsL,KAAK,UAC9BmE,EAAK5P,QAAQyL,KAAKtL,GAAMsL,KAAK,SAAU/C,EAAQ0G,MAAQ,IAAIhK,eAE/DwK,EAAK5P,QAAQ8D,KAAK,QAAU4E,EAAQmZ,OAASnZ,EAAQ0G,KAAMQ,GAC3DA,EAAKQ,KAAKR,EAAKc,OAAQhI,IAE3BgI,UACAhI,SAAWmZ,OAAQ,IACnBiM,kBAAmB,WACf,QAASxlB,KAAKtI,QAAQ,GAAG+tB,oBAE7BC,UAAW,SAAU3e,GACjBA,EAASA,GAAU/G,KAAKxF,OACxB,IAAI9C,GAAUsI,KAAKtI,QAASiuB,EAAW,WAAYC,EAAW7e,EAAO5D,KAAKwiB,IAAajuB,EAAQyL,KAAKwiB,EACpGjuB,GAAQmuB,WAAWF,GACnB5e,EAAO5D,KAAKwiB,EAAWtP,MAAMuP,GAAuB,EAAXA,IAE7CE,WAAY,SAAU1lB,GAClBJ,KAAK+lB,WAAW3lB,GAChB/J,EAAEqK,OAAOV,KAAKI,QAASA,IAE3B2lB,WAAY,SAAU3lB,GAElB,IADA,GAAuDuI,GAAnDrB,EAAOtH,KAAMrC,EAAM,EAAGpG,EAAS+P,EAAKc,OAAO7Q,OACxCoG,EAAMpG,EAAQoG,IACjBgL,EAAIrB,EAAKc,OAAOzK,GACZ2J,EAAKlH,QAAQuI,IAAMvI,EAAQuI,IAC3BrB,EAAKiB,OAAOI,EAAGrB,EAAKlH,QAAQuI,GAGpCrB,GAAKQ,KAAKR,EAAKc,OAAQhI,IAE3ByhB,OAAQ,SAAUmE,GACd,GAAIjoB,GAAOiC,KAAKimB,UAAWC,EAAclmB,KAAKmmB,OAC1CH,IAAUjoB,EAAKnF,MAAQ,GAAKmF,EAAKlF,OAAS,MAAQqtB,GAAenoB,EAAKnF,QAAUstB,EAAYttB,OAASmF,EAAKlF,SAAWqtB,EAAYrtB,WACjImH,KAAKmmB,MAAQpoB,EACbiC,KAAKomB,QAAQroB,EAAMioB,GACnBhmB,KAAK0I,QAAQ,SAAU3K,KAG/BkoB,QAAS,WACL,MAAOpuB,IAAMwuB,WAAWrmB,KAAKtI,UAEjCqG,KAAM,SAAUA,GACZ,MAAKA,IAGDiC,KAAKsmB,QAAQvoB,GAAbiC,GAFOA,KAAKimB,WAKpBK,QAASjwB,EAAEuK,KACXwlB,QAAS/vB,EAAEuK,KACX2lB,QAAS,WACL,GAAIjf,GAAOtH,IACXsH,GAAK5P,QAAQ8uB,WAAW,QAAUlf,EAAKlH,QAAQmZ,OAASjS,EAAKlH,QAAQ0G,MACrEQ,EAAK5P,QAAQ8uB,WAAW,WACxBlf,EAAKiB,UAETke,SAAU,WACNzmB,KAAKumB,WAEThB,QAAS,aAETmB,mBAAoB,SAAUnF,GAC1BvhB,KAAK2mB,aAAc,EACnBpF,EAAS1Y,KAAK7I,MACdA,KAAK2mB,aAAc,KAGvBzhB,GAAkBD,GAAOvE,QACzBkmB,UAAW,WACP,MAAO5mB,MAAKslB,WAAWuB,YAE3BC,cAAe,SAAUC,GACrB,GAAIzf,GAAOtH,IACXsH,GAAKie,QAAQwB,EAAK,WACd,OACIC,SAAU1f,EAAK2f,QACfzrB,KAAMnF,EAAEsd,IAAIrM,EAAKsf,YAAa,SAAUM,GACpC,OAASA,SAAUA,WAMvCrvB,GAAMwuB,WAAa,SAAU3uB,EAAS2uB,GAClC,GAAIc,GAAazvB,EAAQ,EAIzB,OAHI2uB,IACA3uB,EAAQE,IAAIyuB,IAGZztB,MAAOuuB,EAAWtO,YAClBhgB,OAAQsuB,EAAWC,eAG3BvvB,GAAMwvB,OAASzmB,GACX6B,GAAiB,aAAcL,GAAa,oDAAqDC,GAAmB,wCAAyCN,GAAa,WA6C9KlK,GAAMyvB,WAAa,SAAU5vB,EAAS0I,EAASmnB,GAA5B,GACXvoB,GAAQ8C,EAAQmB,EAAQtF,EAAKpG,EAAQiL,EAAM5E,EAAO0nB,EAAYkC,EAAUC,EAiBxEjsB,EAA0BksB,EAMrB3oB,CAfT,IAPKwoB,EAEMA,EAAMA,QACbA,EAAQA,EAAMA,OAFdA,EAAQ1vB,GAAMoqB,GAAGsF,MAIrB7vB,EAAUA,EAAQiwB,SAAWjwB,EAAUA,EAAQ,GAC/C8K,EAAO9K,EAAQsK,aAAa,QAAUnK,GAAMoK,GAAK,QACjD,CAGAulB,EAAWhlB,EAAKtE,QAAQ,UAEpB+E,EADAukB,EACSD,EAAM/kB,GAEN3K,GAAMosB,OAAOzhB,GAAMlM,GAE5BkF,EAAOnF,EAAEqB,GAAS8D,OAAQksB,EAAYzkB,EAAS,QAAUA,EAAOiB,GAAG9D,QAAQmZ,OAAStW,EAAOiB,GAAG9D,QAAQ0G,KAAO,GAE7G2gB,EADAD,EACsBzrB,OAAO,WAAayG,EAAO,IAAK,KAEhCzG,OAAO,IAAM2rB,EAAY,IAAK,IAExD,KAAS3oB,IAAOvD,GACZ,GAAIuD,EAAI9G,MAAMwvB,GAAkB,CAC5B,GAAI1oB,IAAQ2oB,EAGR,MAAOlsB,GAAKuD,EAFZC,GAASxD,EAAKuD,GAM1B,GAAKkE,EAAL,CAYA,IATAqiB,EAAazjB,EAAYnK,EAAS,cAClC0I,EAAU/J,EAAEqK,UAAW6B,EAAa7K,EAASuL,EAAOiB,GAAG9D,SAAUA,GAC7DklB,IAEIllB,EAAQklB,iBADDA,KAAe9kB,GACD3I,GAAMosB,OAAOqB,GAAYhvB,GAEzBgvB,GAGxB3nB,EAAM,EAAGpG,EAAS0L,EAAOiB,GAAGkE,OAAO7Q,OAAQoG,EAAMpG,EAAQoG,IAC1DmE,EAASmB,EAAOiB,GAAGkE,OAAOzK,GAC1BC,EAAQiE,EAAYnK,EAASoK,GACzBlE,IAAUrH,IACV6J,EAAQ0B,GAAUjK,GAAMosB,OAAOrmB,GAAOtH,GAQ9C,OALK0I,GAEO3I,EAAEuxB,cAAcxnB,IACxBpB,EAAO8mB,WAAW1lB,GAFlBpB,EAAS,GAAIiE,GAAOvL,EAAS0I,GAI1BpB,KAEXnH,GAAMgwB,oBAAsB,SAAUC,GAClC,GAAgBnqB,GAAKpG,EAAjBgwB,IAOJ,KANKO,EAAW,KACZA,GACIjwB,GAAMoqB,GACNpqB,GAAMsqB,QAAQF,KAGjBtkB,EAAM,EAAGpG,EAASuwB,EAAWvwB,OAAQoG,EAAMpG,EAAQoG,IACpD4pB,EAAM5pB,GAAOmqB,EAAWnqB,GAAK4pB,KAEjC,OAAO7mB,IAAO8G,MAAM,UAAWsP,OAAOyQ,EAAMjnB,aAEhDzI,GAAM8I,KAAO,SAAUjJ,GACnB,GAAI6vB,GAAQ1vB,GAAMgwB,oBAAoBphB,GAAMoC,KAAK9N,UAAW,GAC5D1E,GAAEqB,GAASqwB,KAAK,SAAWlwB,GAAMoK,GAAK,SAAS4B,UAAUnG,KAAK,WAC1D7F,GAAMyvB,WAAWtnB,QAAUunB,MAGnC1vB,GAAM0uB,QAAU,SAAU7uB,GACtBrB,EAAEqB,GAASqwB,KAAK,SAAWlwB,GAAMoK,GAAK,SAAS4B,UAAUnG,KAAK,WAAA,GAEjDqB,GADLvD,EAAOnF,EAAE2J,MAAMxE,MACnB,KAASuD,IAAOvD,GACiB,IAAzBuD,EAAIb,QAAQ,gBAAyB1C,GAAKuD,GAAKwnB,UAAYngB,IAC3D5K,EAAKuD,GAAKwnB,aAe1B1uB,GAAMgqB,OAAS,SAAUnqB,EAASsuB,GAAnB,GAKPgC,GAJAjG,EAAU1rB,EAAEqB,GAASqwB,KAAK,SAAWlwB,GAAMoK,GAAK,SAAS4B,UAAUC,OAAOd,EACzE+e,GAAQxqB,SAGTywB,EAAe3xB,EAAE4xB,UAAUlG,GAC/BiG,EAAanJ,KAAKjc,GAClBvM,EAAEqH,KAAKsqB,EAAc,WACjB,GAAI/kB,GAASpL,GAAMqwB,eAAe7xB,EAAE2J,MAChCiD,IACAA,EAAO4e,OAAOmE,OAI1BnuB,GAAM0K,aAAeA,EACrB7B,GAAO7I,GAAMoqB,IACThd,OAAQA,GACRC,gBAAiBA,GACjBqiB,SACAY,SAAU,SAAUC,EAAWpH,EAAQ5gB,GACnC,GAAkG/B,GAAOgqB,EAAWC,EAAkBC,EAAqBvK,EAAvJwK,EAAOJ,EAAUL,KAAK,mBAAoBjwB,EAAUD,GAAMC,QAASmB,EAAUnB,EAAQmB,OACzFmH,GAAU/J,EAAEqK,WACR9H,MAAO,OACPC,OAAQ,OACRyG,IAAK8oB,EAAUzoB,YACf8oB,SAAS,GACVroB,GACH4d,EAAW5d,EAAQqoB,QAAU,0BAA4B,iBACrDzH,EACKwH,EAAKjxB,SACN8G,EAAQvG,EAAQuG,MAAM+pB,GACtBC,EAAYhqB,EAAQ,QAAU,OAC9BkqB,EAAsBH,EAAUhqB,aAChCkqB,EAAmBrvB,EAAQqF,QAAUD,EAAY+pB,EAAU,GAAG1pB,YAAc0pB,EAAUxvB,QAAU,EAAI2vB,EAAsB,EAC1HC,EAAOnyB,EAAEwB,GAAM4R,OAAO,6HAAsIuU,EAAUnmB,GAAMoqB,GAAGkG,SAAS7F,SAASoG,UAAU9vB,MAAMwH,EAAQxH,OAAOC,OAAOuH,EAAQvH,QAAQjB,IAAI,MAAOwI,EAAQd,KAAK1H,IAAIywB,EAAWzpB,KAAKC,IAAI0pB,GAAuBD,GAAkBK,UAAUP,IAEvVI,GACPA,EAAKI,UAGbC,OAAQ,SAAU5lB,EAAQ6lB,EAAUvP,GAA5B,GAC+B0K,GAO/B8E,EAMKjuB,EAAOkqB,EAbZle,EAAO7D,EAAOiB,GAAG9D,QAAQ0G,IAa7B,KAZAgiB,EAAWA,GAAYjxB,GAAMoqB,GAC7B1I,EAASA,GAAU,GACnBuP,EAAShiB,GAAQ7D,EACjB6lB,EAASvB,MAAMzgB,EAAKhK,eAAiBmG,EACrCghB,EAAS,WAAa1K,EAASzS,EAC/BA,EAAO,QAAUyS,EAASzS,EACtBiiB,GACAjiB,KAAMA,EACN7D,OAAQA,EACRsW,OAAQA,GAAU,IAEtB1hB,GAAMkqB,QAAQvZ,KAAKugB,GACVjuB,EAAI,EAAGkqB,EAAMntB,GAAMmqB,2BAA2BzqB,OAAQuD,EAAIkqB,EAAKlqB,IACpEjD,GAAMmqB,2BAA2BlnB,GAAGiuB,EAExC1yB,GAAE6N,GAAG4C,GAAQ,SAAU1G,GACnB,GAAkB4oB,GAAdprB,EAAQoC,IAuBZ,cAtBWI,KAAYI,IACnBwoB,EAAOviB,GAAMoC,KAAK9N,UAAW,GAC7BiF,KAAKtC,KAAK,WACN,GAAiCurB,GAAQjqB,EAArCiE,EAAS5M,EAAEmF,KAAKwE,KAAM8G,EAC1B,KAAK7D,EACD,KAAUuG,OAAM3R,GAAM4R,OAAO,2DAA8DrJ,EAAS0G,GAGxG,IADAmiB,EAAShmB,EAAO7C,SACL6oB,KAAW7iB,GAClB,KAAUoD,OAAM3R,GAAM4R,OAAO,kCAAqCrJ,EAAS0G,GAG/E,IADA9H,EAASiqB,EAAOzhB,MAAMvE,EAAQ+lB,GAC1BhqB,IAAWzI,EAEX,MADAqH,GAAQoB,GACD,KAIfgB,KAAKtC,KAAK,WACN,MAAO,IAAIuF,GAAOjD,KAAMI,KAGzBxC,GAEXvH,EAAE6N,GAAG4C,GAAM7D,OAASA,EACpB5M,EAAE6N,GAAG+f,GAAU,WACX,MAAOjkB,MAAKxE,KAAKsL,OAI7BjP,GAAMoqB,GAAGkG,SAAS7F,UAAaoG,QAAS,cACpCvjB,IACA2C,KAAM,WACF,MAAO9H,OAEXkpB,YAAY,EACZ9oB,YAEAgF,GAAeH,GAAOvE,QACtBC,KAAM,SAAUjJ,EAAS0I,GACrB6E,GAAOf,GAAGvD,KAAKkI,KAAK7I,KAAMtI,EAAS0I,GACnCJ,KAAKtI,QAAQyxB,cACbnpB,KAAKxF,QAAUwF,KAAKtI,QACpBsI,KAAKtI,QAAQqC,SAAS,cAE1BwsB,QAAS,WACLthB,GAAOf,GAAGqiB,QAAQ1d,KAAK7I,MACvBA,KAAKtI,QAAQ0xB,gBAEjBhpB,SAAWmZ,OAAQ,UACnBnR,UACAihB,KAAM,WACF,GAAIC,GAActpB,KAAKtI,QAAQqhB,QAAQlhB,GAAM4sB,aAAa,mCAC1D,OAAO5sB,IAAMqwB,eAAeoB,EAAazxB,GAAMqqB,OAAOD,KAAO9c,IAEjEokB,uBAAwB,WACpB,GAAIF,GAAOrpB,KAAKqpB,MAChB,OAAOA,IAAQA,EAAKjpB,QAAQopB,oBAEhCpB,UAAW,WACP,GAAI1wB,GAAUsI,KAAKtI,QAAQqhB,QAAQlhB,GAAM4sB,aAAa,0CACtD,OAAO5sB,IAAMqwB,eAAexwB,EAAQ+xB,GAAG,GAAI5xB,GAAMqqB,OAAOD,KAAO9c,MAGvEzE,GAAO7I,GAAMqqB,QACTvhB,KAAM,SAAUjJ,GACZG,GAAM8I,KAAKjJ,EAASG,GAAMqqB,OAAOD,GAAIpqB,GAAMoqB,GAAIpqB,GAAMsqB,QAAQF,KAEjEyH,wBAAyB,WACrB,MAAO7xB,IAAMqqB,OAAOyH,aAAe9xB,GAAMqqB,OAAOyH,YAAYvpB,SAAWvI,GAAMqqB,OAAOyH,YAAYvpB,QAAQopB,oBAE5GjC,SACAtF,IACIhd,OAAQG,GACRF,gBAAiBA,GAAgBxE,OAAO0E,GAAaqC,WACrD8f,SACAsB,OAAQ,SAAU5lB,GACdpL,GAAMoqB,GAAG4G,OAAO5lB,EAAQpL,GAAMqqB,OAAOD,GAAI,cAIrDrnB,EAAW/C,GAAMsqB,SACbxhB,KAAM,SAAUjJ,GACZG,GAAM8I,KAAKjJ,EAASG,GAAMsqB,QAAQF,KAEtCA,IACIsF,SACAqC,UACAC,SACAhB,OAAQ,SAAU5lB,GACdpL,GAAMoqB,GAAG4G,OAAO5lB,EAAQpL,GAAMsqB,QAAQF,MAG9CsF,WAEJ1vB,GAAMiyB,cAAgB,SAAU9C,EAAU5mB,GAKtC,MAJKA,KACDA,MAEJA,EAAQ2pB,WAAY,EACb1zB,EAAE2wB,GAAUrT,IAAI,SAAUhW,EAAKjG,GAElC,MADAA,GAAUrB,EAAEqB,MACRI,GAAQ+nB,sBAAuBhoB,GAAMqqB,OAAOD,GAAG+H,UAAatyB,EAAQ8D,KAAK,0BACzE9D,EAAQuyB,oBAAoB7pB,GACrB1I,EAAQ8D,KAAK,0BAIzB,IAEP3D,GAAMuM,eAAiB,SAAUuE,GAC7BA,EAAEvE,kBAENvM,GAAMqwB,eAAiB,SAAUxwB,EAASwyB,GAAnB,GACuCpvB,GAAGvD,EAMjD4yB,EA2BAlnB,EAEIhC,EAnCZuB,EAAO9K,EAAQ8D,KAAK3D,GAAMoK,GAAK,QAAS8f,IAC5C,IAAIvf,EAAM,CAIN,GAHa,YAATA,IACAA,EAAO,YAEE,kBAATA,IACI2nB,EAAgBzyB,EAAQ8D,KAAK,uBAE7B,MAAO2uB,EAGf,IAAa,SAAT3nB,EACA,MAAO9K,GAAQ8D,KAAK,YAExB,IAAI0uB,EACA,GAAIA,EAAO,GACP,IAAKpvB,EAAI,EAAGvD,EAAS2yB,EAAO3yB,OAAQuD,EAAIvD,EAAQuD,IAC5CinB,EAAQvZ,KAAK0hB,EAAOpvB,GAAGysB,MAAM/kB,QAGjCuf,GAAQvZ,KAAK0hB,EAAO3C,MAAM/kB,QAG9Buf,IACIlqB,GAAMoqB,GAAGsF,MAAM/kB,GACf3K,GAAMsqB,QAAQF,GAAGsF,MAAM/kB,GACvB3K,GAAMqqB,OAAOD,GAAGsF,MAAM/kB,GAM9B,KAHIA,EAAKtE,QAAQ,MAAQ,IACrB6jB,GAAWlqB,GAAMosB,OAAOzhB,GAAMlM,KAE7BwE,EAAI,EAAGvD,EAASwqB,EAAQxqB,OAAQuD,EAAIvD,EAAQuD,IAE7C,GADImI,EAAS8e,EAAQjnB,GACjBmI,IACIhC,EAAWvJ,EAAQ8D,KAAK,QAAUyH,EAAOiB,GAAG9D,QAAQmZ,OAAStW,EAAOiB,GAAG9D,QAAQ0G,OAE/E,MAAO7F,KAM3BpJ,GAAMuyB,SAAW,SAAU7I,GACvB,GAAIpZ,GAAUoZ,CAOd,OANIzpB,IAAQmgB,SAASyC,UACjBvS,EAAU,WACNkiB,WAAW9I,EAAU,OAG7BlrB,EAAEC,GAAQqP,GAAG7N,GAAQ+pB,OAAQ1Z,GACtBA,GAEXtQ,GAAMyyB,aAAe,SAAU/I,GAC3BlrB,EAAEC,GAAQi0B,IAAIzyB,GAAQ+pB,OAAQN,IAElC1pB,GAAM2yB,UAAY,SAAU9yB,EAASqH,GACjC,MAAOrH,GAAQ8D,KAAK3D,GAAMoK,GAAKlD,IAEnClH,GAAM8U,MACF8d,OAAQ,EACRC,OAAQ,EACRC,QAAS,EACTC,UAAW,EACXC,SAAU,EACVC,OAAQ,EACRC,SAAU,GAWd10B,EAAEqK,OAAOrK,EAAEqN,KAAKC,SACZqnB,eAAgB,SAAUtzB,GACtB,GAAIiG,GAAMtH,EAAE8M,KAAKzL,EAAS,WAC1B,OAAO0L,GAAU1L,GAAU2e,MAAM1Y,IAAQA,SAG7C0H,IACA,YACA,YACA,aACA,aACA,YACA,WACA,UACA,SAEAC,GAA8B,oCAC9BC,IACA0lB,eAAgB,WAAA,GAQR9iB,GAPAxK,EAAM,EAAGpG,EAAS8N,GAAa9N,OAAQG,EAAU6F,SAASmC,eAC9D,KAAI6F,GAAqB2lB,WAAcpzB,GAAQomB,aAkB/C,IAfA3Y,GAAqB2lB,WAAY,EACjC3lB,GAAqB4lB,WAAY,EACjC5lB,GAAqB6lB,cAAe,EAChCjjB,EAAU,SAAUQ,GAChBpD,GAAqB6lB,eACN,UAAXziB,EAAEzJ,KACEqG,GAAqB4lB,YAAc90B,EAAEsS,EAAE5B,QAAQjN,GAAGwL,MAClDqD,EAAEvE,iBACFuE,EAAE0iB,mBAGN1iB,EAAE0iB,oBAIP1tB,EAAMpG,EAAQoG,IACjBjG,EAAQymB,iBAAiB9Y,GAAa1H,GAAMwK,GAAS,IAG7DmjB,UAAW,SAAU3iB,GACjBpD,GAAqB6lB,cAAe,EAChCziB,EAAEnN,KAAK2vB,YACP5lB,GAAqB4lB,WAAY,GAErCI,aAAahmB,GAAqBimB,qBAEtCC,YAAa,WACTF,aAAahmB,GAAqBimB,oBAClCjmB,GAAqBimB,mBAAqBnB,WAAW,WACjD9kB,GAAqB6lB,cAAe,EACpC7lB,GAAqB4lB,WAAY,GAClC,OAGP3lB,IACAsa,KAAM,uBACN4L,KAAM,sBACN3L,GAAI,+BACJ4L,OAAQ,0BAER7zB,GAAQmhB,QAAUnhB,GAAQmgB,SAASqD,KAAOxjB,GAAQmgB,SAASyC,SAC3DlV,IACIsa,KAAM,aACN4L,KAAM,YACN3L,GAAI,uBACJ4L,OAAQ,eAEL7zB,GAAQyH,SACfiG,IACIsa,KAAM,cACN4L,KAAM,cACN3L,GAAI,YACJ4L,OAAQ,8BAEL7zB,GAAQ0H,aACfgG,IACIsa,KAAM,gBACN4L,KAAM,gBACN3L,GAAI,cACJ4L,OAAQ,oCAGZ7zB,GAAQ0H,YAAgB,oBAAsBlJ,IAC9CD,EAAEqH,MACEkuB,eAAgB,gBAChBC,eAAgB,gBACjB,SAAUC,EAAMC,GACf11B,EAAEojB,MAAMuS,QAAQF,IACZG,aAAcF,EACdG,SAAUH,EACVI,OAAQ,SAAU1S,GACd,GAAI2S,GAAKrlB,EAAS/G,KAAMqsB,EAAU5S,EAAM6S,cAAeC,EAAY9S,EAAM8S,SAMzE,OALKF,KAAWA,IAAYtlB,GAAW1Q,EAAE0M,SAASgE,EAAQslB,MACtD5S,EAAMva,KAAOqtB,EAAUC,SACvBJ,EAAMG,EAAUpkB,QAAQX,MAAMxH,KAAMjF,WACpC0e,EAAMva,KAAO6sB,GAEVK,MAKnB3mB,GAAc,SAAUkD,GACpB,MAAOnD,IAASmD,IAAMA,GACvBjD,GAAa,WACpB7N,GAAM40B,cAAgB,SAAUrkB,EAAQnG,GAKpC,MAJAmG,GAASA,EAAOtR,QAAQ4O,GAAYD,IAChCxD,IACAmG,EAASA,EAAOtR,QAAQ4O,GAAY,MAAQzD,IAEzCmG,GAEPzC,GAAKtP,EAAE6N,GAAGyB,GAIde,IAAkB,EAAM3C,EAAa1N,GACrC0N,EAAYG,GAAKH,EAAY0D,UAAY,GAAIpR,GAC7C0N,EAAYG,GAAGrI,YAAckI,EAC7BA,EAAYG,GAAGvD,KAAO,SAAUqD,EAAUC,GAItC,MAHIA,IAAWA,YAAmB5N,MAAO4N,YAAmBF,MACxDE,EAAUF,EAAYE,IAEnB5N,EAAE6N,GAAGvD,KAAKkI,KAAK7I,KAAMgE,EAAUC,EAAS2B,KAEnD7B,EAAYG,GAAGvD,KAAK8G,UAAY1D,EAAYG,GACxC0B,GAAa7B,EAAYxG,UAC7BmD,GAAOqD,EAAYG,IACfiE,QAAS,SAAUA,GAEf,MADAnI,MAAKxE,KAAK,UAAW2M,GACdnI,MAEXmpB,YAAa,SAAUlnB,GAEnB,MADAjC,MAAKxE,KAAK,UAAWyG,GAAMpK,GAAMysB,QAC1BtkB,MAEX2F,GAAI,WAAA,GAKI1B,GAAgB+kB,EAIhBzH,EAAkCnZ,EAG9BpE,EAA+CmnB,EAXnD7jB,EAAOtH,KAAMiC,EAAKqF,EAAK9L,KAAK,UAChC,OAAyB,KAArBT,UAAUxD,OACHoO,GAAGkD,KAAKvB,EAAMvM,UAAU,KAE/BkJ,EAAUqD,EAAM0hB,EAAOviB,GAAMoC,KAAK9N,iBAC3BiuB,GAAKA,EAAKzxB,OAAS,KAAO6E,IACjC4sB,EAAK0D,MAELnL,EAAWyH,EAAKA,EAAKzxB,OAAS,GAAI6Q,EAASvQ,GAAM40B,cAAczD,EAAK,GAAI/mB,GACxEnK,GAAQglB,sBAAwB1U,EAAOukB,OAAO,mBAAuB3sB,KAAK,KAAOzC,SAASmC,kBAC1F6F,GAAqB0lB,iBACjBjnB,EAA2B,IAAhBglB,EAAKzxB,OAAe,KAAOyxB,EAAK,GAAImC,EAAY/iB,EAAOlK,QAAQ,aAAiBkK,EAAOlK,QAAQ,eAC9GyH,GAAGkD,KAAK7I,MACJ4sB,WAAYrnB,GAAqB+lB,UACjCuB,SAAUtnB,GAAqBkmB,aAChCznB,GAAYmnB,UAAWA,WAEnB5J,KAAa/gB,KACpByD,EAAUqD,EAAK9L,KAAK,WACpB+lB,EAAWtd,EAAQsd,GACnByH,EAAKA,EAAKzxB,OAAS,GAAK,SAAUoR,GAC9B4Y,EAAS1Y,KAAK5E,EAAS0E,KAG/BqgB,EAAK,GAAK5gB,EACVzC,GAAG6B,MAAMF,EAAM0hB,GACR1hB,IAEX8hB,aAAc,SAAUnnB,GAKpB,MAJAA,GAAKA,GAAMjC,KAAKxE,KAAK,WACjByG,GACAjC,KAAKuqB,IAAI,IAAMtoB,GAEZjC,QAGfnI,GAAMoP,OAASlD,EACflM,GAAM2N,SAAWA,GACjB3N,GAAMsf,SAAW,WAwBb,QAAS2V,GAAWnX,EAAMoX,GAA1B,GACQxgB,GACAygB,EACAC,EACArX,EAAQmX,EAAK,GACbpnB,EAAKonB,EAAK,GACVG,EAAOH,EAAK,GACZI,EAAQJ,EAAK,EAIjB,OAHKI,KACDJ,EAAK,GAAKI,MAEVA,EAAMxX,GACCwX,EAAMxX,IAEZU,MAAM1Q,GAEuB,IAAvBA,EAAGzH,QAAQ,SAClBqO,EAAO,GAAIvQ,MAAKA,KAAKwY,IAAImB,EAAM/I,EAAOgJ,GAAS,EAAG,EAAGsX,EAAK,GAAK,GAAIA,EAAK,GAAIA,EAAK,GAAI,IACrFF,EAAYrgB,EAAKhH,EAAGuP,OAAO,EAAG,IAC9B+X,EAAS1gB,EAAK6gB,YACd7gB,EAAK8gB,WAAW9gB,EAAKhB,aAAeyhB,EAAYC,GAAUD,EAAYC,EAAS,EAAI,KAC5EtnB,EAAGzH,QAAQ,OAAS,IAC3BqO,EAAO,GAAIvQ,MAAKA,KAAKwY,IAAImB,EAAM/I,EAAOgJ,GAAQjQ,EAAGuP,OAAO,GAAIgY,EAAK,GAAIA,EAAK,GAAIA,EAAK,GAAI,IACvFF,EAAYrgB,EAAKhH,EAAGuP,OAAO,EAAG,IAC9B+X,EAAS1gB,EAAK6gB,YACd7gB,EAAK8gB,WAAW9gB,EAAKhB,aAAeyhB,EAAYC,GAAUD,EAAYC,EAAS,EAAI,KAVnF1gB,EAAO,GAAIvQ,MAAKA,KAAKwY,IAAImB,EAAM/I,EAAOgJ,GAAQjQ,EAAIunB,EAAK,GAAIA,EAAK,GAAIA,EAAK,GAAI,IAY1EC,EAAMxX,GAAQpJ,GAEzB,QAAS+gB,GAASC,EAASC,EAAOC,GAAlC,GAGYP,GACA9tB,EAmBJuW,EAgBAoX,CArCJ,QADAS,EAAQA,EAAMC,KAsBV9X,EAAO,GAAI3Z,MAAKuxB,GAASliB,iBAC7BmiB,EAAQvmB,OAAOymB,KAAKF,EAAO,SAAUT,GAAV,GACnBY,GAAOZ,EAAK,GACZa,EAAKb,EAAK,EACd,OAAOY,IAAQhY,IAASiY,GAAMjY,GAAQgY,GAAQhY,GAAc,QAANiY,GAAsB,OAANA,KAE1EJ,EAAMhlB,KAAK+kB,GACXC,EAAM3O,KAAK,SAAUhc,EAAGC,GAOpB,MANgB,gBAALD,KACPA,GAAWiqB,EAAWnX,EAAM9S,IAEhB,gBAALC,KACPA,GAAWgqB,EAAWnX,EAAM7S,IAEzBD,EAAIC,IAEXiqB,EAAOS,EAAMvmB,OAAO/D,QAAQqqB,EAASC,GAAS,IAAMA,EAAMA,EAAMj2B,OAAS,GACtE8e,MAAM0W,GAAQA,EAAO,OArCpBG,EAAOO,EAAK72B,MAAM,KAClBwI,EAAS,EACT8tB,EAAK31B,OAAS,IACd6H,EAAmB,GAAV8tB,EAAK,KAAiBA,EAAK,UAIpC,MACA,IACA,MACA,GAEI,EACA,EACA,GAEJ9tB,EACA,MAsBZ,QAASyuB,GAASN,EAASO,EAAO3W,GAAlC,GAQaxZ,GACDowB,EAKJN,EAbAO,EAAYF,EAAM3W,EAItB,IAHyB,gBAAd6W,KACPA,EAAYF,EAAME,KAEjBA,EACD,KAAUxkB,OAAM,aAAe2N,EAAW,oEAE9C,KAASxZ,EAAMqwB,EAAUz2B,OAAS,EAAGoG,GAAO,IACpCowB,EAAQC,EAAUrwB,GAAK,KACvBowB,GAASR,EAAUQ,IAFoBpwB,KAO/C,GADI8vB,EAAOO,EAAUrwB,EAAM,IACtB8vB,EACD,KAAUjkB,OAAM,aAAe2N,EAAW,kBAAoBoW,EAAU,IAE5E,OAAOE,GAEX,QAASQ,GAAYV,EAASO,EAAON,EAAOrW,SAC7BoW,IAAWlnB,KAClBknB,EAAUvxB,KAAKwY,IAAI+Y,EAAQlgB,cAAekgB,EAAQngB,WAAYmgB,EAAQvgB,UAAWugB,EAAQjgB,WAAYigB,EAAQhgB,aAAcggB,EAAQ/f,aAAc+f,EAAQ9f,mBAE7J,IAAIggB,GAAOI,EAASN,EAASO,EAAO3W,EACpC,QACIsW,KAAMA,EACNV,KAAMO,EAASC,EAASC,EAAOC,EAAK,KAG5C,QAASruB,GAAOmuB,EAASpW,GAAzB,GAIQ+W,GACAT,EACAV,CALJ,OAAgB,WAAZ5V,GAAqC,WAAZA,EAClB,GAEP+W,EAAOD,EAAYV,EAASvtB,KAAK8tB,MAAO9tB,KAAKwtB,MAAOrW,GACpDsW,EAAOS,EAAKT,KACZV,EAAOmB,EAAKnB,KACTl1B,GAAMsK,WAAW4qB,EAAOU,EAAK,GAAKV,EAAK,GAAKU,EAAK,KAE5D,QAASrc,GAAKmc,EAASpW,GAAvB,GACQ+W,GAAOD,EAAYV,EAASvtB,KAAK8tB,MAAO9tB,KAAKwtB,MAAOrW,GACpDsW,EAAOS,EAAKT,KACZV,EAAOmB,EAAKnB,KACZ1lB,EAAOomB,EAAK,EAChB,OAAIpmB,GAAKnJ,QAAQ,MAAQ,EACdmJ,EAAKzQ,MAAM,KAAKm2B,IAASA,EAAK,GAAK,EAAI,GACvC1lB,EAAKnJ,QAAQ,OAAS,EACtBmJ,EAAKvQ,QAAQ,KAAOi2B,GAAmB,KAAXA,EAAK,GAAiBA,EAAK,GAAV,IAEjD1lB,EAEX,QAAS+P,GAAQ7K,EAAM4hB,EAAYC,GAAnC,GAEQC,GAOAC,EAEAC,EAVAC,EAAeJ,CAenB,cAbWD,IAAc3tB,KACrB2tB,EAAanuB,KAAKZ,OAAOmN,EAAM4hB,UAExBC,IAAY5tB,KACnB4tB,EAAWpuB,KAAKZ,OAAOmN,EAAM6hB,IAE7BE,EAAkB/hB,EAAKqB,oBAC3BrB,EAAO,GAAIvQ,MAAKuQ,EAAKtQ,UAAsC,KAAzBkyB,EAAaC,IAC3CG,EAAgBhiB,EAAKqB,0BACd4gB,IAAgBhuB,KACvBguB,EAAexuB,KAAKZ,OAAOmN,EAAMiiB,IAErCH,EAAOE,EAAgBD,GAAmBF,EAAWI,GAC9C,GAAIxyB,MAAKuQ,EAAKtQ,UAAmB,IAAPoyB,GAErC,QAAS7mB,GAAM+E,EAAM4K,GACjB,MAAOnX,MAAKoX,QAAQ7K,EAAMA,EAAKqB,oBAAqBuJ,GAExD,QAASyR,GAAOrc,EAAM4K,GAClB,MAAOnX,MAAKoX,QAAQ7K,EAAM4K,EAAU5K,EAAKqB,qBAE7C,QAAS6gB,GAAYvB,GACjB,MAAOltB,MAAKwH,MAAM,GAAIxL,MAAKkxB,GAAO,WA3KzB,GACTtgB,IACA8hB,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,GACLC,IAAK,IAEL1iB,GACA2iB,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,IAAK,EAuJT,QACI9B,SACAN,SACApuB,OAAQA,EACRgY,QAASA,EACT5P,MAAOA,EACPohB,OAAQA,EACRxX,KAAMA,EACNqd,YAAaA,MAGrB52B,GAAM0U,KAAO,WAET,QAASqH,GAAUrH,EAAMsH,GACrB,MAAc,KAAVA,GAAmC,KAApBtH,EAAKe,aACpBf,EAAKuH,SAASvH,EAAKe,WAAa,IACzB,GAIf,QAASuiB,GAAatjB,EAAMsJ,EAAKia,GAC7B,GAAIjc,GAAQtH,EAAKe,UACjBwiB,GAAMA,GAAO,EACbja,GAAOA,EAAMtJ,EAAKW,SAAW,EAAI4iB,GAAO,EACxCvjB,EAAKwjB,QAAQxjB,EAAKS,UAAY6I,GAC9BjC,EAAUrH,EAAMsH,GAEpB,QAASmc,GAAUzjB,EAAMsJ,EAAKia,GAG1B,MAFAvjB,GAAO,GAAIvQ,MAAKuQ,GAChBsjB,EAAatjB,EAAMsJ,EAAKia,GACjBvjB,EAEX,QAAS0jB,GAAgB1jB,GACrB,MAAO,IAAIvQ,MAAKuQ,EAAKc,cAAed,EAAKa,WAAY,GAEzD,QAAS8iB,GAAe3jB,GACpB,GAAI4jB,GAAO,GAAIn0B,MAAKuQ,EAAKc,cAAed,EAAKa,WAAa,EAAG,GAAIrW,EAAQk5B,EAAgB1jB,GAAO6jB,EAAaxxB,KAAKC,IAAIsxB,EAAKviB,oBAAsB7W,EAAM6W,oBAIvJ,OAHIwiB,IACAD,EAAKrc,SAAS/c,EAAMuW,WAAa8iB,EAAa,IAE3CD,EAEX,QAASE,GAAoB9jB,EAAM+jB,GAC/B,MAAqB,KAAjBA,EACOC,EAAQP,EAAUzjB,EAAM+jB,MAAmB,GAE/CC,EAAQhkB,EAAM,GAAKA,EAAKW,UAAY,IAE/C,QAASsjB,GAAejkB,EAAM+jB,GAA9B,GACQG,GAAkB,GAAIz0B,MAAKuQ,EAAKc,cAAe,EAAG,QAClDqjB,EAAUL,EAAoB9jB,EAAM+jB,GACpCK,EAAWD,EAAQz0B,UAAYw0B,EAAgBx0B,UAC/C0Q,EAAO/N,KAAKxE,MAAMu2B,EAAWC,EACjC,OAAO,GAAIhyB,KAAKxE,MAAMuS,EAAO,GAEjC,QAASkkB,GAAWtkB,EAAM+jB,GAA1B,GAIQQ,GACAC,EACAC,CACJ,OANIV,KAAiB/5B,IACjB+5B,EAAez4B,GAAMqU,UAAUM,SAAS4F,UAExC0e,EAAeP,EAAQhkB,MACvBwkB,EAAeR,EAAQhkB,EAAM,GAC7BykB,EAAaR,EAAejkB,EAAM+jB,GACnB,IAAfU,EACOR,EAAeM,EAAcR,GAAgB,EAErC,KAAfU,GAAqBR,EAAeO,EAAcT,GAAgB,EAC3D,EAEJU,EAEX,QAAShkB,GAAQT,GAGb,MAFAA,GAAO,GAAIvQ,MAAKuQ,EAAKc,cAAed,EAAKa,WAAYb,EAAKS,UAAW,EAAG,EAAG,GAC3E4G,EAAUrH,EAAM,GACTA,EAEX,QAAS0kB,GAAU1kB,GACf,MAAOvQ,MAAKwY,IAAIjI,EAAKc,cAAed,EAAKa,WAAYb,EAAKS,UAAWT,EAAKe,WAAYf,EAAKgB,aAAchB,EAAKiB,aAAcjB,EAAKkB,mBAErI,QAASA,GAAgBlB,GACrB,MAAO2kB,GAAgB3kB,GAAMtQ,UAAY+Q,EAAQkkB,EAAgB3kB,IAErE,QAAS4kB,GAAcvzB,EAAO+U,EAAKta,GAC/B,GAAgE+4B,GAA5DC,EAAQ5jB,EAAgBkF,GAAM2e,EAAQ7jB,EAAgBpV,EAC1D,QAAKuF,GAASyzB,GAASC,IAGnB3e,GAAOta,IACPA,GAAOu4B,GAEXQ,EAAU3jB,EAAgB7P,GACtByzB,EAAQD,IACRA,GAAWR,GAEXU,EAAQD,IACRC,GAASV,GAENQ,GAAWC,GAASD,GAAWE,GAE1C,QAASC,GAAc3zB,EAAO+U,EAAKta,GAC/B,GAAkD+4B,GAA9CC,EAAQ1e,EAAI1W,UAAWq1B,EAAQj5B,EAAI4D,SAKvC,OAJIo1B,IAASC,IACTA,GAASV,GAEbQ,EAAUxzB,EAAM3B,UACTm1B,GAAWC,GAASD,GAAWE,EAE1C,QAASf,GAAQhkB,EAAMnN,GACnB,GAAIyU,GAAQtH,EAAKe,UAIjB,OAHAf,GAAO,GAAIvQ,MAAKuQ,GAChBilB,EAAQjlB,EAAMnN,EAASwxB,GACvBhd,EAAUrH,EAAMsH,GACTtH,EAEX,QAASilB,GAAQjlB,EAAMwJ,EAAc0b,GAArC,GAEQC,GADAtyB,EAASmN,EAAKqB,mBAElBrB,GAAKilB,QAAQjlB,EAAKtQ,UAAY8Z,GACzB0b,IACDC,EAAanlB,EAAKqB,oBAAsBxO,EACxCmN,EAAKilB,QAAQjlB,EAAKtQ,UAAYy1B,EAAaC,IAGnD,QAAS7d,GAASvH,EAAM2gB,GAGpB,MAFA3gB,GAAO,GAAIvQ,MAAKnE,GAAM0U,KAAKS,QAAQT,GAAMtQ,UAAYpE,GAAM0U,KAAKkB,gBAAgByf,IAChFtZ,EAAUrH,EAAM2gB,EAAK5f,YACdf,EAEX,QAASqlB,KACL,MAAO5kB,GAAQ,GAAIhR,OAEvB,QAAS61B,GAAQtlB,GACb,MAAOS,GAAQT,GAAMtQ,WAAa21B,IAAQ31B,UAE9C,QAASi1B,GAAgB3kB,GACrB,GAAIulB,GAAa,GAAI91B,MAAK,KAAM,EAAG,EAAG,EAAG,EAAG,EAI5C,OAHIuQ,IACAulB,EAAWhe,SAASvH,EAAKe,WAAYf,EAAKgB,aAAchB,EAAKiB,aAAcjB,EAAKkB,mBAE7EqkB,EA9HX,GAAIH,GAAgB,IAAOf,EAAa,KAgIxC,QACIhd,UAAWA,EACXoc,UAAWA,EACXH,aAAcA,EACd7iB,QAASA,EACTukB,cAAeA,EACfJ,cAAeA,EACfU,QAASA,EACTE,QAAS,SAAUxlB,GACf,MAAOgkB,GAAQhkB,EAAM,IAEzBylB,YAAa,SAAUzlB,GACnB,MAAOgkB,GAAQhkB,OAEnB0kB,UAAWA,EACXL,WAAYA,EACZqB,YAAa,GAAKN,EAClBA,cAAeA,EACfH,QAASA,EACT1d,SAAUA,EACVyc,QAASA,EACTqB,MAAOA,EACPV,gBAAiBA,EACjBjB,gBAAiBA,EACjBC,eAAgBA,EAChBW,WAAYA,EACZpjB,gBAAiBA,MAGzB5V,GAAMq6B,gBAAkB,SAAUx6B,GAAV,GAEZy6B,GASKr3B,EACDs3B,CAXZ,IAAI70B,SAAS80B,mBAIT,IAHIF,EAAW50B,SAAS80B,mBAAmB36B,EAAS46B,WAAWC,UAAW,SAAUC,GAChF,MAAOA,GAAKnT,YAAc3nB,EAAU46B,WAAWG,cAAgBH,WAAWI,gBAC3E,GACIP,EAASQ,YACRR,EAASS,gBAAkBT,EAASS,cAAcC,YAAYC,QAC9DX,EAASS,cAAcvT,WAAWvG,YAAYqZ,EAASS,mBAI/D,KAAS93B,EAAI,EAAGA,EAAIpD,EAAQq7B,WAAWx7B,OAAQuD,IACvCs3B,EAAQ16B,EAAQq7B,WAAWj4B,GACT,GAAlBs3B,EAAMzK,UAAkB,KAAK9tB,KAAKu4B,EAAMY,aACxCt7B,EAAQohB,YAAYsZ,GACpBt3B,KAEkB,GAAlBs3B,EAAMzK,UACN9vB,GAAMq6B,gBAAgBE,IAKlCvsB,GAAiBvP,EAAO28B,uBAAyB38B,EAAO48B,6BAA+B58B,EAAO68B,0BAA4B78B,EAAO88B,wBAA0B98B,EAAO+8B,yBAA2B,SAAU9R,GACvM8I,WAAW9I,EAAU,IAAO,KAEhC1pB,GAAMgO,eAAiB,SAAU0b,GAC7B1b,GAAegD,KAAKvS,EAAQirB,IAE5Bzb,MACJjO,GAAMy7B,eAAiB,SAAU/R,GAC7Bzb,GAAeA,GAAevO,QAAUgqB,EACV,IAA1Bzb,GAAevO,QACfM,GAAM07B,oBAGd17B,GAAM07B,iBAAmB,WACrB17B,GAAMgO,eAAe,WACbC,GAAe,KACfA,GAAe2M,UACX3M,GAAe,IACfjO,GAAM07B,uBAKtB17B,GAAM27B,uBAAyB,SAAU1P,GAErC,IADA,GAAI2P,GAAc3P,EAAIltB,MAAM,KAAK,IAAM,GAAI88B,KAAaC,EAAaF,EAAY78B,MAAM,OAAQW,EAASo8B,EAAWp8B,OAAQoG,EAAM,EAC1HA,EAAMpG,EAAQoG,GAAO,EACA,KAApBg2B,EAAWh2B,KACX+1B,EAAOE,mBAAmBD,EAAWh2B,KAASi2B,mBAAmBD,EAAWh2B,EAAM,IAG1F,OAAO+1B,IAEX77B,GAAMg8B,mBAAqB,SAAUlrB,GACjC,GAAyB,IAAdA,EAAEmrB,EAAEC,OACX,MAAOx2B,UAAS6jB,iBAAiBzY,EAAEmrB,EAAEC,OAAQprB,EAAEqJ,EAAE+hB,SAGzDl8B,GAAMm8B,YAAc,SAAUC,GAC1B,GAA2DC,GAAvDvrB,EAAIsrB,EAAY/S,cAAeiT,EAASxrB,EAAEqrB,WAQ9C,OAPIrrB,GAAEyrB,YACED,IAAW59B,GAAa49B,KACxBD,EAAQvrB,EAAEyrB,YAEPzrB,EAAE0rB,QAAU1rB,EAAE2rB,OAAS3rB,EAAE4rB,gBAChCL,EAAoB,IAAXvrB,EAAE0rB,QAERH,GAEXr8B,GAAM28B,SAAW,SAAUtwB,EAAIuwB,GAAd,GACTC,GAKAC,EAJAC,EAAe,CACnB,QAAKH,GAASA,GAAS,EACZvwB,GAEPywB,EAAY,WAIZ,QAAS3kB,KACL9L,EAAGsD,MAAMF,EAAM0hB,GACf4L,GAAgB,GAAI54B,MANZ,GACRsL,GAAOtH,KACP60B,GAAW,GAAI74B,MAAS44B,EACxB5L,EAAOjuB,SAKX,OAAK65B,IAGDF,GACAnJ,aAAamJ,GAEbG,EAAUJ,EACVzkB,IAEA0kB,EAAUrK,WAAWra,EAAMykB,EAAQI,GANvC,GAFW7kB,KAWf2kB,EAAUhJ,OAAS,WACfJ,aAAamJ,IAEVC,IAEX98B,GAAMi9B,MAAQ,SAAUp9B,EAAS8X,EAAOlY,GAA1B,GACNy9B,GAeY7S,EAyBA8S,EAA4CC,EAAgBC,EAvCxEC,EAAa3lB,IAAUjZ,CAO3B,IANIe,IAAQf,IACRe,EAAMkY,GAEN9X,EAAQ,KACRA,EAAUA,EAAQ,KAElBy9B,IAAcz9B,EAAQ6L,SAA1B,CAGA,IACQ7L,EAAQu9B,iBAAmB1+B,EACvB4+B,GACAz9B,EAAQ09B,QACJlT,EAASpqB,GAAQmgB,SACjBiK,EAAO1H,IAAM0H,EAAOxH,QACpB2P,WAAW,WACP3yB,EAAQ29B,kBAAkB7lB,EAAOlY,IAClC,GAEHI,EAAQ29B,kBAAkB7lB,EAAOlY,IAGrCkY,GACI9X,EAAQu9B,eACRv9B,EAAQw9B,cAGT33B,SAAS+3B,YACZj/B,EAAEqB,GAASoC,GAAG,aACdpC,EAAQ09B,QAEZL,EAAer9B,EAAQ69B,kBACnBJ,GACAJ,EAAaS,UAAS,GACtBT,EAAaU,UAAU,YAAajmB,GACpCulB,EAAaW,QAAQ,YAAap+B,EAAMkY,GACxCulB,EAAaY,WAETX,EAAkBD,EAAaa,YACnCb,EAAac,eAAet4B,SAAS+3B,UAAUQ,cAAcC,eAC7Df,EAAgBgB,YAAY,aAAcjB,GAC1CE,EAAiBD,EAAgBiB,KAAK1+B,OACtC29B,EAAeD,EAAiBF,EAAakB,KAAK1+B,OAClDiY,GACIylB,EACAC,KAId,MAAOvsB,GACL6G,KAEJ,MAAOA,KAEX3X,GAAMq+B,uBAAyB,SAAUx+B,EAASy+B,GAC9C,GAAI5Q,GAAUjvB,EAAOivB,OAWrB,OAVA7tB,GAAQyL,KAAK,QAAUtL,GAAMoK,GAAK,OAAQvK,EAAQ,GAAGotB,QAAQhoB,cAAchG,QAAQ,gBAAiB,IAAIA,QAAQ,IAAK,KACrHyuB,EAAQ7tB,QAAQA,GAAS0+B,WAAWC,QAChC,WACA,SAAUC,GACNA,EAAS5+B,GAASy+B,GACb,qBAAqBt8B,KAAKs8B,EAAMI,UACjCJ,EAAMK,aAIX3+B,GAAMqwB,eAAexwB,EAASG,GAAMqqB,OAAOD,KAEtDpqB,GAAM4+B,kBAAoB,WACtB,GAAIC,MAAaC,EAAatgC,EAAE,0CAA0C8M,KAAK,WAAYyzB,EAAavgC,EAAE,iDAAiD8M,KAAK,UAOhK,OANA9M,GAAE,6CAA+CqH,KAAK,WAClDg5B,EAAO12B,KAAK8G,MAAQ9G,KAAKpC,QAEzBg5B,IAAergC,GAAaogC,IAAepgC,IAC3CmgC,EAAOE,GAAcD,GAElBD,GAEX7+B,GAAMg/B,UAAY,SAAUC,GAGxB,QAAS1B,GAAM32B,GACX,GAAIwE,GAASpL,GAAMqwB,eAAezpB,EAC9BwE,IAAUA,EAAOmyB,MACjBnyB,EAAOmyB,QAEP32B,EAAG22B,QARG,GACV2B,GAAeD,EAAK/O,KAAK,oBAAoBhxB,QAC7CigC,EAAcF,EAAK/O,KAAK,qBAAqBoI,MASjD6G,GAAYrxB,GAAG,UAAW,SAAUgD,GAC5BA,EAAEsuB,SAAWp/B,GAAM0qB,KAAKI,KAAQha,EAAEuuB,WAClCvuB,EAAEvE,iBACFgxB,EAAM2B,MAGdA,EAAapxB,GAAG,UAAW,SAAUgD,GAC7BA,EAAEsuB,SAAWp/B,GAAM0qB,KAAKI,KAAOha,EAAEuuB,WACjCvuB,EAAEvE,iBACFgxB,EAAM4B,OAIlBn/B,GAAMs/B,aAAe,SAAUz/B,GAAV,GACb0/B,MACAC,EAAoB3/B,EAAQ4/B,aAAa,QAAQxzB,OAAO,SAAUgP,EAAOpb,GACzE,GAAI2F,GAAgBxF,GAAMsF,kBAAkBzF,GAAU,YACtD,OAAkC,YAA3B2F,EAAcc,WACtBmD,IAAIhL,EACP+gC,GAAkB35B,KAAK,SAAUoV,EAAOxZ,GACpC89B,EAAmBtkB,GAASzc,EAAEiD,GAAQqG,aAE1C,KACIjI,EAAQ,GAAG6/B,YACb,MAAO5uB,GACLjR,EAAQ,GAAG09B,QAEfiC,EAAkB35B,KAAK,SAAUoV,EAAOxZ,GACpCjD,EAAEiD,GAAQqG,UAAUy3B,EAAmBtkB,OAG/Cjb,GAAM2/B,aAAe,SAAUC,GAC3B,GAAIC,GAAQ7/B,GAAM8/B,kBAAkBF,IAAeA,CACnD,OAAO3/B,IAAQwnB,YAAchpB,EAAOgpB,WAAWoY,GAAOjjB,SAE1D5c,GAAM8/B,kBAAoB,SAAUC,GAChC,OACIC,GAAM,qBACNC,GAAM,qBACNC,GAAM,qBACNC,GAAM,qBACNC,GAAM,uBACRL,IAEL,WACG,QAASM,GAAYC,EAASC,EAAUC,EAAUC,GAAlD,GAQQnvB,GAGKrC,EAVLgwB,EAAOzgC,EAAE,UAAU8M,MACnBo1B,OAAQF,EACRpP,OAAQ,OACRliB,OAAQuxB,IAER98B,EAAO3D,GAAM4+B,mBACjBj7B,GAAK48B,SAAWA,EACZjvB,EAAQgvB,EAAQvhC,MAAM,YAC1B4E,EAAKg9B,YAAcrvB,EAAM,GAAGrS,QAAQ,QAAS,IAC7C0E,EAAKi9B,OAAStvB,EAAM,EACpB,KAASrC,IAAQtL,GACTA,EAAKkB,eAAeoK,IACpBzQ,EAAE,WAAW8M,MACTvF,MAAOpC,EAAKsL,GACZA,KAAMA,EACN5H,KAAM,WACPw5B,SAAS5B,EAGpBA,GAAK4B,SAAS,QAAQC,SAAS/P,SAInC,QAASgQ,GAAWT,EAASC,GAA7B,GAGYjvB,GACAqvB,EACAC,EACAzkB,EACKrW,EANTk7B,EAAOV,CACX,IAAsB,gBAAXA,GAAqB,CAK5B,IAJIhvB,EAAQgvB,EAAQvhC,MAAM,YACtB4hC,EAAcrvB,EAAM,GACpBsvB,EAASK,KAAK3vB,EAAM,IACpB6K,EAAQ,GAAI+kB,YAAWN,EAAOlhC,QACzBoG,EAAM,EAAGA,EAAM86B,EAAOlhC,OAAQoG,IACnCqW,EAAMrW,GAAO86B,EAAOzuB,WAAWrM,EAEnCk7B,GAAO,GAAIG,OAAMhlB,EAAMilB,SAAW/5B,KAAMs5B,IAE5Cxc,UAAUkd,WAAWL,EAAMT,GAE/B,QAASe,GAAchB,EAASC,GACxB9hC,EAAO0iC,MAAQb,YAAmBa,QAClCb,EAAUiB,IAAIC,gBAAgBlB,IAElCmB,EAAUC,SAAWnB,EACrBkB,EAAU91B,KAAO20B,CACjB,IAAIxvB,GAAIpL,SAASi8B,YAAY,cAC7B7wB,GAAE8wB,eAAe,SAAS,GAAM,EAAOnjC,EAAQ,EAAG,EAAG,EAAG,EAAG,GAAG,GAAO,GAAO,GAAO,EAAO,EAAG,MAC7FgjC,EAAUI,cAAc/wB,GACxB0hB,WAAW,WACP+O,IAAIO,gBAAgBxB,KAjD/B,GAuBOmB,GAAY/7B,SAASib,cAAc,KACnCohB,EAAoB,YAAcN,KAAczhC,GAAMC,QAAQmB,QAAQ+jB,IA4B1EnlB,IAAMgiC,OAAS,SAAUz5B,GACrB,GAAI05B,GAAO5B,CACN93B,GAAQ25B,aACLH,EACAE,EAAOX,EACAnd,UAAUkd,aACjBY,EAAOlB,IAGfkB,EAAK15B,EAAQ+3B,QAAS/3B,EAAQg4B,SAAUh4B,EAAQi4B,SAAUj4B,EAAQk4B,iBAG1EzgC,GAAMmiC,kBAAoB,SAA2Bx+B,GACjD,GAAIy+B,KAYJ,OAXAvyB,QAAO6a,KAAK/mB,OAAY0+B,QAAQ,SAAUh/B,GACtCwM,OAAOyyB,eAAeF,EAAY/+B,GAC9BkpB,IAAK,WACD,MAAO5oB,GAAKN,IAEhBmpB,IAAK,SAAUzmB,GACXpC,EAAKN,GAAY0C,EACjBpC,EAAK4+B,OAAQ,OAIlBH,IAEbhzB,OAAQ3Q,QACHA,OAAOuB,OACE,kBAAVzB,SAAwBA,OAAOikC,IAAMjkC,OAAS,SAAUkkC,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.core.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.core', ['jquery'], f);\n}(function () {\n    var __meta__ = {\n        id: 'core',\n        name: 'Core',\n        category: 'framework',\n        description: 'The core of the Kendo framework.'\n    };\n    (function ($, window, undefined) {\n        var kendo = window.kendo = window.kendo || { cultures: {} }, extend = $.extend, each = $.each, isArray = $.isArray, proxy = $.proxy, noop = $.noop, math = Math, Template, JSON = window.JSON || {}, support = {}, percentRegExp = /%/, formatRegExp = /\\{(\\d+)(:[^\\}]+)?\\}/g, boxShadowRegExp = /(\\d+(?:\\.?)\\d*)px\\s*(\\d+(?:\\.?)\\d*)px\\s*(\\d+(?:\\.?)\\d*)px\\s*(\\d+)?/i, numberRegExp = /^(\\+|-?)\\d+(\\.?)\\d*$/, FUNCTION = 'function', STRING = 'string', NUMBER = 'number', OBJECT = 'object', NULL = 'null', BOOLEAN = 'boolean', UNDEFINED = 'undefined', getterCache = {}, setterCache = {}, slice = [].slice, noDepricateExtend = function () {\n                var src, copyIsArray, copy, name, options, clone, target = arguments[0] || {}, i = 1, length = arguments.length, deep = false;\n                if (typeof target === 'boolean') {\n                    deep = target;\n                    target = arguments[i] || {};\n                    i++;\n                }\n                if (typeof target !== 'object' && !jQuery.isFunction(target)) {\n                    target = {};\n                }\n                if (i === length) {\n                    target = this;\n                    i--;\n                }\n                for (; i < length; i++) {\n                    if ((options = arguments[i]) != null) {\n                        for (name in options) {\n                            if (name == 'filters' || name == 'concat' || name == ':') {\n                                continue;\n                            }\n                            src = target[name];\n                            copy = options[name];\n                            if (target === copy) {\n                                continue;\n                            }\n                            if (deep && copy && (jQuery.isPlainObject(copy) || (copyIsArray = jQuery.isArray(copy)))) {\n                                if (copyIsArray) {\n                                    copyIsArray = false;\n                                    clone = src && jQuery.isArray(src) ? src : [];\n                                } else {\n                                    clone = src && jQuery.isPlainObject(src) ? src : {};\n                                }\n                                target[name] = noDepricateExtend(deep, clone, copy);\n                            } else if (copy !== undefined) {\n                                target[name] = copy;\n                            }\n                        }\n                    }\n                }\n                return target;\n            };\n        kendo.version = '2019.2.619'.replace(/^\\s+|\\s+$/g, '');\n        function Class() {\n        }\n        Class.extend = function (proto) {\n            var base = function () {\n                }, member, that = this, subclass = proto && proto.init ? proto.init : function () {\n                    that.apply(this, arguments);\n                }, fn;\n            base.prototype = that.prototype;\n            fn = subclass.fn = subclass.prototype = new base();\n            for (member in proto) {\n                if (proto[member] != null && proto[member].constructor === Object) {\n                    fn[member] = extend(true, {}, base.prototype[member], proto[member]);\n                } else {\n                    fn[member] = proto[member];\n                }\n            }\n            fn.constructor = subclass;\n            subclass.extend = that.extend;\n            return subclass;\n        };\n        Class.prototype._initOptions = function (options) {\n            this.options = deepExtend({}, this.options, options);\n        };\n        var isFunction = kendo.isFunction = function (fn) {\n            return typeof fn === 'function';\n        };\n        var preventDefault = function () {\n            this._defaultPrevented = true;\n        };\n        var isDefaultPrevented = function () {\n            return this._defaultPrevented === true;\n        };\n        var Observable = Class.extend({\n            init: function () {\n                this._events = {};\n            },\n            bind: function (eventName, handlers, one) {\n                var that = this, idx, eventNames = typeof eventName === STRING ? [eventName] : eventName, length, original, handler, handlersIsFunction = typeof handlers === FUNCTION, events;\n                if (handlers === undefined) {\n                    for (idx in eventName) {\n                        that.bind(idx, eventName[idx]);\n                    }\n                    return that;\n                }\n                for (idx = 0, length = eventNames.length; idx < length; idx++) {\n                    eventName = eventNames[idx];\n                    handler = handlersIsFunction ? handlers : handlers[eventName];\n                    if (handler) {\n                        if (one) {\n                            original = handler;\n                            handler = function () {\n                                that.unbind(eventName, handler);\n                                original.apply(that, arguments);\n                            };\n                            handler.original = original;\n                        }\n                        events = that._events[eventName] = that._events[eventName] || [];\n                        events.push(handler);\n                    }\n                }\n                return that;\n            },\n            one: function (eventNames, handlers) {\n                return this.bind(eventNames, handlers, true);\n            },\n            first: function (eventName, handlers) {\n                var that = this, idx, eventNames = typeof eventName === STRING ? [eventName] : eventName, length, handler, handlersIsFunction = typeof handlers === FUNCTION, events;\n                for (idx = 0, length = eventNames.length; idx < length; idx++) {\n                    eventName = eventNames[idx];\n                    handler = handlersIsFunction ? handlers : handlers[eventName];\n                    if (handler) {\n                        events = that._events[eventName] = that._events[eventName] || [];\n                        events.unshift(handler);\n                    }\n                }\n                return that;\n            },\n            trigger: function (eventName, e) {\n                var that = this, events = that._events[eventName], idx, length;\n                if (events) {\n                    e = e || {};\n                    e.sender = that;\n                    e._defaultPrevented = false;\n                    e.preventDefault = preventDefault;\n                    e.isDefaultPrevented = isDefaultPrevented;\n                    events = events.slice();\n                    for (idx = 0, length = events.length; idx < length; idx++) {\n                        events[idx].call(that, e);\n                    }\n                    return e._defaultPrevented === true;\n                }\n                return false;\n            },\n            unbind: function (eventName, handler) {\n                var that = this, events = that._events[eventName], idx;\n                if (eventName === undefined) {\n                    that._events = {};\n                } else if (events) {\n                    if (handler) {\n                        for (idx = events.length - 1; idx >= 0; idx--) {\n                            if (events[idx] === handler || events[idx].original === handler) {\n                                events.splice(idx, 1);\n                            }\n                        }\n                    } else {\n                        that._events[eventName] = [];\n                    }\n                }\n                return that;\n            }\n        });\n        function compilePart(part, stringPart) {\n            if (stringPart) {\n                return '\\'' + part.split('\\'').join('\\\\\\'').split('\\\\\"').join('\\\\\\\\\\\\\"').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/\\t/g, '\\\\t') + '\\'';\n            } else {\n                var first = part.charAt(0), rest = part.substring(1);\n                if (first === '=') {\n                    return '+(' + rest + ')+';\n                } else if (first === ':') {\n                    return '+$kendoHtmlEncode(' + rest + ')+';\n                } else {\n                    return ';' + part + ';$kendoOutput+=';\n                }\n            }\n        }\n        var argumentNameRegExp = /^\\w+/, encodeRegExp = /\\$\\{([^}]*)\\}/g, escapedCurlyRegExp = /\\\\\\}/g, curlyRegExp = /__CURLY__/g, escapedSharpRegExp = /\\\\#/g, sharpRegExp = /__SHARP__/g, zeros = [\n                '',\n                '0',\n                '00',\n                '000',\n                '0000'\n            ];\n        Template = {\n            paramName: 'data',\n            useWithBlock: true,\n            render: function (template, data) {\n                var idx, length, html = '';\n                for (idx = 0, length = data.length; idx < length; idx++) {\n                    html += template(data[idx]);\n                }\n                return html;\n            },\n            compile: function (template, options) {\n                var settings = extend({}, this, options), paramName = settings.paramName, argumentName = paramName.match(argumentNameRegExp)[0], useWithBlock = settings.useWithBlock, functionBody = 'var $kendoOutput, $kendoHtmlEncode = kendo.htmlEncode;', fn, parts, idx;\n                if (isFunction(template)) {\n                    return template;\n                }\n                functionBody += useWithBlock ? 'with(' + paramName + '){' : '';\n                functionBody += '$kendoOutput=';\n                parts = template.replace(escapedCurlyRegExp, '__CURLY__').replace(encodeRegExp, '#=$kendoHtmlEncode($1)#').replace(curlyRegExp, '}').replace(escapedSharpRegExp, '__SHARP__').split('#');\n                for (idx = 0; idx < parts.length; idx++) {\n                    functionBody += compilePart(parts[idx], idx % 2 === 0);\n                }\n                functionBody += useWithBlock ? ';}' : ';';\n                functionBody += 'return $kendoOutput;';\n                functionBody = functionBody.replace(sharpRegExp, '#');\n                try {\n                    fn = new Function(argumentName, functionBody);\n                    fn._slotCount = Math.floor(parts.length / 2);\n                    return fn;\n                } catch (e) {\n                    throw new Error(kendo.format('Invalid template:\\'{0}\\' Generated code:\\'{1}\\'', template, functionBody));\n                }\n            }\n        };\n        function pad(number, digits, end) {\n            number = number + '';\n            digits = digits || 2;\n            end = digits - number.length;\n            if (end) {\n                return zeros[digits].substring(0, end) + number;\n            }\n            return number;\n        }\n        (function () {\n            var escapable = /[\\\\\\\"\\x00-\\x1f\\x7f-\\x9f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g, gap, indent, meta = {\n                    '\\b': '\\\\b',\n                    '\\t': '\\\\t',\n                    '\\n': '\\\\n',\n                    '\\f': '\\\\f',\n                    '\\r': '\\\\r',\n                    '\"': '\\\\\"',\n                    '\\\\': '\\\\\\\\'\n                }, rep, toString = {}.toString;\n            if (typeof Date.prototype.toJSON !== FUNCTION) {\n                Date.prototype.toJSON = function () {\n                    var that = this;\n                    return isFinite(that.valueOf()) ? pad(that.getUTCFullYear(), 4) + '-' + pad(that.getUTCMonth() + 1) + '-' + pad(that.getUTCDate()) + 'T' + pad(that.getUTCHours()) + ':' + pad(that.getUTCMinutes()) + ':' + pad(that.getUTCSeconds()) + 'Z' : null;\n                };\n                String.prototype.toJSON = Number.prototype.toJSON = Boolean.prototype.toJSON = function () {\n                    return this.valueOf();\n                };\n            }\n            function quote(string) {\n                escapable.lastIndex = 0;\n                return escapable.test(string) ? '\"' + string.replace(escapable, function (a) {\n                    var c = meta[a];\n                    return typeof c === STRING ? c : '\\\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);\n                }) + '\"' : '\"' + string + '\"';\n            }\n            function str(key, holder) {\n                var i, k, v, length, mind = gap, partial, value = holder[key], type;\n                if (value && typeof value === OBJECT && typeof value.toJSON === FUNCTION) {\n                    value = value.toJSON(key);\n                }\n                if (typeof rep === FUNCTION) {\n                    value = rep.call(holder, key, value);\n                }\n                type = typeof value;\n                if (type === STRING) {\n                    return quote(value);\n                } else if (type === NUMBER) {\n                    return isFinite(value) ? String(value) : NULL;\n                } else if (type === BOOLEAN || type === NULL) {\n                    return String(value);\n                } else if (type === OBJECT) {\n                    if (!value) {\n                        return NULL;\n                    }\n                    gap += indent;\n                    partial = [];\n                    if (toString.apply(value) === '[object Array]') {\n                        length = value.length;\n                        for (i = 0; i < length; i++) {\n                            partial[i] = str(i, value) || NULL;\n                        }\n                        v = partial.length === 0 ? '[]' : gap ? '[\\n' + gap + partial.join(',\\n' + gap) + '\\n' + mind + ']' : '[' + partial.join(',') + ']';\n                        gap = mind;\n                        return v;\n                    }\n                    if (rep && typeof rep === OBJECT) {\n                        length = rep.length;\n                        for (i = 0; i < length; i++) {\n                            if (typeof rep[i] === STRING) {\n                                k = rep[i];\n                                v = str(k, value);\n                                if (v) {\n                                    partial.push(quote(k) + (gap ? ': ' : ':') + v);\n                                }\n                            }\n                        }\n                    } else {\n                        for (k in value) {\n                            if (Object.hasOwnProperty.call(value, k)) {\n                                v = str(k, value);\n                                if (v) {\n                                    partial.push(quote(k) + (gap ? ': ' : ':') + v);\n                                }\n                            }\n                        }\n                    }\n                    v = partial.length === 0 ? '{}' : gap ? '{\\n' + gap + partial.join(',\\n' + gap) + '\\n' + mind + '}' : '{' + partial.join(',') + '}';\n                    gap = mind;\n                    return v;\n                }\n            }\n            if (typeof JSON.stringify !== FUNCTION) {\n                JSON.stringify = function (value, replacer, space) {\n                    var i;\n                    gap = '';\n                    indent = '';\n                    if (typeof space === NUMBER) {\n                        for (i = 0; i < space; i += 1) {\n                            indent += ' ';\n                        }\n                    } else if (typeof space === STRING) {\n                        indent = space;\n                    }\n                    rep = replacer;\n                    if (replacer && typeof replacer !== FUNCTION && (typeof replacer !== OBJECT || typeof replacer.length !== NUMBER)) {\n                        throw new Error('JSON.stringify');\n                    }\n                    return str('', { '': value });\n                };\n            }\n        }());\n        (function () {\n            var dateFormatRegExp = /dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|HH|H|hh|h|mm|m|fff|ff|f|tt|ss|s|zzz|zz|z|\"[^\"]*\"|'[^']*'/g, standardFormatRegExp = /^(n|c|p|e)(\\d*)$/i, literalRegExp = /(\\\\.)|(['][^']*[']?)|([\"][^\"]*[\"]?)/g, commaRegExp = /\\,/g, EMPTY = '', POINT = '.', COMMA = ',', SHARP = '#', ZERO = '0', PLACEHOLDER = '??', EN = 'en-US', objectToString = {}.toString;\n            kendo.cultures['en-US'] = {\n                name: EN,\n                numberFormat: {\n                    pattern: ['-n'],\n                    decimals: 2,\n                    ',': ',',\n                    '.': '.',\n                    groupSize: [3],\n                    percent: {\n                        pattern: [\n                            '-n %',\n                            'n %'\n                        ],\n                        decimals: 2,\n                        ',': ',',\n                        '.': '.',\n                        groupSize: [3],\n                        symbol: '%'\n                    },\n                    currency: {\n                        name: 'US Dollar',\n                        abbr: 'USD',\n                        pattern: [\n                            '($n)',\n                            '$n'\n                        ],\n                        decimals: 2,\n                        ',': ',',\n                        '.': '.',\n                        groupSize: [3],\n                        symbol: '$'\n                    }\n                },\n                calendars: {\n                    standard: {\n                        days: {\n                            names: [\n                                'Sunday',\n                                'Monday',\n                                'Tuesday',\n                                'Wednesday',\n                                'Thursday',\n                                'Friday',\n                                'Saturday'\n                            ],\n                            namesAbbr: [\n                                'Sun',\n                                'Mon',\n                                'Tue',\n                                'Wed',\n                                'Thu',\n                                'Fri',\n                                'Sat'\n                            ],\n                            namesShort: [\n                                'Su',\n                                'Mo',\n                                'Tu',\n                                'We',\n                                'Th',\n                                'Fr',\n                                'Sa'\n                            ]\n                        },\n                        months: {\n                            names: [\n                                'January',\n                                'February',\n                                'March',\n                                'April',\n                                'May',\n                                'June',\n                                'July',\n                                'August',\n                                'September',\n                                'October',\n                                'November',\n                                'December'\n                            ],\n                            namesAbbr: [\n                                'Jan',\n                                'Feb',\n                                'Mar',\n                                'Apr',\n                                'May',\n                                'Jun',\n                                'Jul',\n                                'Aug',\n                                'Sep',\n                                'Oct',\n                                'Nov',\n                                'Dec'\n                            ]\n                        },\n                        AM: [\n                            'AM',\n                            'am',\n                            'AM'\n                        ],\n                        PM: [\n                            'PM',\n                            'pm',\n                            'PM'\n                        ],\n                        patterns: {\n                            d: 'M/d/yyyy',\n                            D: 'dddd, MMMM dd, yyyy',\n                            F: 'dddd, MMMM dd, yyyy h:mm:ss tt',\n                            g: 'M/d/yyyy h:mm tt',\n                            G: 'M/d/yyyy h:mm:ss tt',\n                            m: 'MMMM dd',\n                            M: 'MMMM dd',\n                            s: 'yyyy\\'-\\'MM\\'-\\'ddTHH\\':\\'mm\\':\\'ss',\n                            t: 'h:mm tt',\n                            T: 'h:mm:ss tt',\n                            u: 'yyyy\\'-\\'MM\\'-\\'dd HH\\':\\'mm\\':\\'ss\\'Z\\'',\n                            y: 'MMMM, yyyy',\n                            Y: 'MMMM, yyyy'\n                        },\n                        '/': '/',\n                        ':': ':',\n                        firstDay: 0,\n                        twoDigitYearMax: 2029\n                    }\n                }\n            };\n            function findCulture(culture) {\n                if (culture) {\n                    if (culture.numberFormat) {\n                        return culture;\n                    }\n                    if (typeof culture === STRING) {\n                        var cultures = kendo.cultures;\n                        return cultures[culture] || cultures[culture.split('-')[0]] || null;\n                    }\n                    return null;\n                }\n                return null;\n            }\n            function getCulture(culture) {\n                if (culture) {\n                    culture = findCulture(culture);\n                }\n                return culture || kendo.cultures.current;\n            }\n            kendo.culture = function (cultureName) {\n                var cultures = kendo.cultures, culture;\n                if (cultureName !== undefined) {\n                    culture = findCulture(cultureName) || cultures[EN];\n                    culture.calendar = culture.calendars.standard;\n                    cultures.current = culture;\n                } else {\n                    return cultures.current;\n                }\n            };\n            kendo.findCulture = findCulture;\n            kendo.getCulture = getCulture;\n            kendo.culture(EN);\n            function formatDate(date, format, culture) {\n                culture = getCulture(culture);\n                var calendar = culture.calendars.standard, days = calendar.days, months = calendar.months;\n                format = calendar.patterns[format] || format;\n                return format.replace(dateFormatRegExp, function (match) {\n                    var minutes;\n                    var result;\n                    var sign;\n                    if (match === 'd') {\n                        result = date.getDate();\n                    } else if (match === 'dd') {\n                        result = pad(date.getDate());\n                    } else if (match === 'ddd') {\n                        result = days.namesAbbr[date.getDay()];\n                    } else if (match === 'dddd') {\n                        result = days.names[date.getDay()];\n                    } else if (match === 'M') {\n                        result = date.getMonth() + 1;\n                    } else if (match === 'MM') {\n                        result = pad(date.getMonth() + 1);\n                    } else if (match === 'MMM') {\n                        result = months.namesAbbr[date.getMonth()];\n                    } else if (match === 'MMMM') {\n                        result = months.names[date.getMonth()];\n                    } else if (match === 'yy') {\n                        result = pad(date.getFullYear() % 100);\n                    } else if (match === 'yyyy') {\n                        result = pad(date.getFullYear(), 4);\n                    } else if (match === 'h') {\n                        result = date.getHours() % 12 || 12;\n                    } else if (match === 'hh') {\n                        result = pad(date.getHours() % 12 || 12);\n                    } else if (match === 'H') {\n                        result = date.getHours();\n                    } else if (match === 'HH') {\n                        result = pad(date.getHours());\n                    } else if (match === 'm') {\n                        result = date.getMinutes();\n                    } else if (match === 'mm') {\n                        result = pad(date.getMinutes());\n                    } else if (match === 's') {\n                        result = date.getSeconds();\n                    } else if (match === 'ss') {\n                        result = pad(date.getSeconds());\n                    } else if (match === 'f') {\n                        result = math.floor(date.getMilliseconds() / 100);\n                    } else if (match === 'ff') {\n                        result = date.getMilliseconds();\n                        if (result > 99) {\n                            result = math.floor(result / 10);\n                        }\n                        result = pad(result);\n                    } else if (match === 'fff') {\n                        result = pad(date.getMilliseconds(), 3);\n                    } else if (match === 'tt') {\n                        result = date.getHours() < 12 ? calendar.AM[0] : calendar.PM[0];\n                    } else if (match === 'zzz') {\n                        minutes = date.getTimezoneOffset();\n                        sign = minutes < 0;\n                        result = math.abs(minutes / 60).toString().split('.')[0];\n                        minutes = math.abs(minutes) - result * 60;\n                        result = (sign ? '+' : '-') + pad(result);\n                        result += ':' + pad(minutes);\n                    } else if (match === 'zz' || match === 'z') {\n                        result = date.getTimezoneOffset() / 60;\n                        sign = result < 0;\n                        result = math.abs(result).toString().split('.')[0];\n                        result = (sign ? '+' : '-') + (match === 'zz' ? pad(result) : result);\n                    }\n                    return result !== undefined ? result : match.slice(1, match.length - 1);\n                });\n            }\n            function formatNumber(number, format, culture) {\n                culture = getCulture(culture);\n                var numberFormat = culture.numberFormat, decimal = numberFormat[POINT], precision = numberFormat.decimals, pattern = numberFormat.pattern[0], literals = [], symbol, isCurrency, isPercent, customPrecision, formatAndPrecision, negative = number < 0, integer, fraction, integerLength, fractionLength, replacement = EMPTY, value = EMPTY, idx, length, ch, hasGroup, hasNegativeFormat, decimalIndex, sharpIndex, zeroIndex, hasZero, hasSharp, percentIndex, currencyIndex, startZeroIndex, start = -1, end;\n                if (number === undefined) {\n                    return EMPTY;\n                }\n                if (!isFinite(number)) {\n                    return number;\n                }\n                if (!format) {\n                    return culture.name.length ? number.toLocaleString() : number.toString();\n                }\n                formatAndPrecision = standardFormatRegExp.exec(format);\n                if (formatAndPrecision) {\n                    format = formatAndPrecision[1].toLowerCase();\n                    isCurrency = format === 'c';\n                    isPercent = format === 'p';\n                    if (isCurrency || isPercent) {\n                        numberFormat = isCurrency ? numberFormat.currency : numberFormat.percent;\n                        decimal = numberFormat[POINT];\n                        precision = numberFormat.decimals;\n                        symbol = numberFormat.symbol;\n                        pattern = numberFormat.pattern[negative ? 0 : 1];\n                    }\n                    customPrecision = formatAndPrecision[2];\n                    if (customPrecision) {\n                        precision = +customPrecision;\n                    }\n                    if (format === 'e') {\n                        var exp = customPrecision ? number.toExponential(precision) : number.toExponential();\n                        return exp.replace(POINT, numberFormat[POINT]);\n                    }\n                    if (isPercent) {\n                        number *= 100;\n                    }\n                    number = round(number, precision);\n                    negative = number < 0;\n                    number = number.split(POINT);\n                    integer = number[0];\n                    fraction = number[1];\n                    if (negative) {\n                        integer = integer.substring(1);\n                    }\n                    value = groupInteger(integer, 0, integer.length, numberFormat);\n                    if (fraction) {\n                        value += decimal + fraction;\n                    }\n                    if (format === 'n' && !negative) {\n                        return value;\n                    }\n                    number = EMPTY;\n                    for (idx = 0, length = pattern.length; idx < length; idx++) {\n                        ch = pattern.charAt(idx);\n                        if (ch === 'n') {\n                            number += value;\n                        } else if (ch === '$' || ch === '%') {\n                            number += symbol;\n                        } else {\n                            number += ch;\n                        }\n                    }\n                    return number;\n                }\n                if (format.indexOf('\\'') > -1 || format.indexOf('\"') > -1 || format.indexOf('\\\\') > -1) {\n                    format = format.replace(literalRegExp, function (match) {\n                        var quoteChar = match.charAt(0).replace('\\\\', ''), literal = match.slice(1).replace(quoteChar, '');\n                        literals.push(literal);\n                        return PLACEHOLDER;\n                    });\n                }\n                format = format.split(';');\n                if (negative && format[1]) {\n                    format = format[1];\n                    hasNegativeFormat = true;\n                } else if (number === 0 && format[2]) {\n                    format = format[2];\n                    if (format.indexOf(SHARP) == -1 && format.indexOf(ZERO) == -1) {\n                        return format;\n                    }\n                } else {\n                    format = format[0];\n                }\n                percentIndex = format.indexOf('%');\n                currencyIndex = format.indexOf('$');\n                isPercent = percentIndex != -1;\n                isCurrency = currencyIndex != -1;\n                if (isPercent) {\n                    number *= 100;\n                }\n                if (isCurrency && format[currencyIndex - 1] === '\\\\') {\n                    format = format.split('\\\\').join('');\n                    isCurrency = false;\n                }\n                if (isCurrency || isPercent) {\n                    numberFormat = isCurrency ? numberFormat.currency : numberFormat.percent;\n                    decimal = numberFormat[POINT];\n                    precision = numberFormat.decimals;\n                    symbol = numberFormat.symbol;\n                }\n                hasGroup = format.indexOf(COMMA) > -1;\n                if (hasGroup) {\n                    format = format.replace(commaRegExp, EMPTY);\n                }\n                decimalIndex = format.indexOf(POINT);\n                length = format.length;\n                if (decimalIndex != -1) {\n                    fraction = number.toString().split('e');\n                    if (fraction[1]) {\n                        fraction = round(number, Math.abs(fraction[1]));\n                    } else {\n                        fraction = fraction[0];\n                    }\n                    fraction = fraction.split(POINT)[1] || EMPTY;\n                    zeroIndex = format.lastIndexOf(ZERO) - decimalIndex;\n                    sharpIndex = format.lastIndexOf(SHARP) - decimalIndex;\n                    hasZero = zeroIndex > -1;\n                    hasSharp = sharpIndex > -1;\n                    idx = fraction.length;\n                    if (!hasZero && !hasSharp) {\n                        format = format.substring(0, decimalIndex) + format.substring(decimalIndex + 1);\n                        length = format.length;\n                        decimalIndex = -1;\n                        idx = 0;\n                    }\n                    if (hasZero && zeroIndex > sharpIndex) {\n                        idx = zeroIndex;\n                    } else if (sharpIndex > zeroIndex) {\n                        if (hasSharp && idx > sharpIndex) {\n                            var rounded = round(number, sharpIndex, negative);\n                            while (rounded.charAt(rounded.length - 1) === ZERO && sharpIndex > 0 && sharpIndex > zeroIndex) {\n                                sharpIndex--;\n                                rounded = round(number, sharpIndex, negative);\n                            }\n                            idx = sharpIndex;\n                        } else if (hasZero && idx < zeroIndex) {\n                            idx = zeroIndex;\n                        }\n                    }\n                }\n                number = round(number, idx, negative);\n                sharpIndex = format.indexOf(SHARP);\n                startZeroIndex = zeroIndex = format.indexOf(ZERO);\n                if (sharpIndex == -1 && zeroIndex != -1) {\n                    start = zeroIndex;\n                } else if (sharpIndex != -1 && zeroIndex == -1) {\n                    start = sharpIndex;\n                } else {\n                    start = sharpIndex > zeroIndex ? zeroIndex : sharpIndex;\n                }\n                sharpIndex = format.lastIndexOf(SHARP);\n                zeroIndex = format.lastIndexOf(ZERO);\n                if (sharpIndex == -1 && zeroIndex != -1) {\n                    end = zeroIndex;\n                } else if (sharpIndex != -1 && zeroIndex == -1) {\n                    end = sharpIndex;\n                } else {\n                    end = sharpIndex > zeroIndex ? sharpIndex : zeroIndex;\n                }\n                if (start == length) {\n                    end = start;\n                }\n                if (start != -1) {\n                    value = number.toString().split(POINT);\n                    integer = value[0];\n                    fraction = value[1] || EMPTY;\n                    integerLength = integer.length;\n                    fractionLength = fraction.length;\n                    if (negative && number * -1 >= 0) {\n                        negative = false;\n                    }\n                    number = format.substring(0, start);\n                    if (negative && !hasNegativeFormat) {\n                        number += '-';\n                    }\n                    for (idx = start; idx < length; idx++) {\n                        ch = format.charAt(idx);\n                        if (decimalIndex == -1) {\n                            if (end - idx < integerLength) {\n                                number += integer;\n                                break;\n                            }\n                        } else {\n                            if (zeroIndex != -1 && zeroIndex < idx) {\n                                replacement = EMPTY;\n                            }\n                            if (decimalIndex - idx <= integerLength && decimalIndex - idx > -1) {\n                                number += integer;\n                                idx = decimalIndex;\n                            }\n                            if (decimalIndex === idx) {\n                                number += (fraction ? decimal : EMPTY) + fraction;\n                                idx += end - decimalIndex + 1;\n                                continue;\n                            }\n                        }\n                        if (ch === ZERO) {\n                            number += ch;\n                            replacement = ch;\n                        } else if (ch === SHARP) {\n                            number += replacement;\n                        }\n                    }\n                    if (hasGroup) {\n                        number = groupInteger(number, start + (negative && !hasNegativeFormat ? 1 : 0), Math.max(end, integerLength + start), numberFormat);\n                    }\n                    if (end >= start) {\n                        number += format.substring(end + 1);\n                    }\n                    if (isCurrency || isPercent) {\n                        value = EMPTY;\n                        for (idx = 0, length = number.length; idx < length; idx++) {\n                            ch = number.charAt(idx);\n                            value += ch === '$' || ch === '%' ? symbol : ch;\n                        }\n                        number = value;\n                    }\n                    length = literals.length;\n                    if (length) {\n                        for (idx = 0; idx < length; idx++) {\n                            number = number.replace(PLACEHOLDER, literals[idx]);\n                        }\n                    }\n                }\n                return number;\n            }\n            var groupInteger = function (number, start, end, numberFormat) {\n                var decimalIndex = number.indexOf(numberFormat[POINT]);\n                var groupSizes = numberFormat.groupSize.slice();\n                var groupSize = groupSizes.shift();\n                var integer, integerLength;\n                var idx, parts, value;\n                var newGroupSize;\n                end = decimalIndex !== -1 ? decimalIndex : end + 1;\n                integer = number.substring(start, end);\n                integerLength = integer.length;\n                if (integerLength >= groupSize) {\n                    idx = integerLength;\n                    parts = [];\n                    while (idx > -1) {\n                        value = integer.substring(idx - groupSize, idx);\n                        if (value) {\n                            parts.push(value);\n                        }\n                        idx -= groupSize;\n                        newGroupSize = groupSizes.shift();\n                        groupSize = newGroupSize !== undefined ? newGroupSize : groupSize;\n                        if (groupSize === 0) {\n                            if (idx > 0) {\n                                parts.push(integer.substring(0, idx));\n                            }\n                            break;\n                        }\n                    }\n                    integer = parts.reverse().join(numberFormat[COMMA]);\n                    number = number.substring(0, start) + integer + number.substring(end);\n                }\n                return number;\n            };\n            var round = function (value, precision, negative) {\n                precision = precision || 0;\n                value = value.toString().split('e');\n                value = Math.round(+(value[0] + 'e' + (value[1] ? +value[1] + precision : precision)));\n                if (negative) {\n                    value = -value;\n                }\n                value = value.toString().split('e');\n                value = +(value[0] + 'e' + (value[1] ? +value[1] - precision : -precision));\n                return value.toFixed(Math.min(precision, 20));\n            };\n            var toString = function (value, fmt, culture) {\n                if (fmt) {\n                    if (objectToString.call(value) === '[object Date]') {\n                        return formatDate(value, fmt, culture);\n                    } else if (typeof value === NUMBER) {\n                        return formatNumber(value, fmt, culture);\n                    }\n                }\n                return value !== undefined ? value : '';\n            };\n            kendo.format = function (fmt) {\n                var values = arguments;\n                return fmt.replace(formatRegExp, function (match, index, placeholderFormat) {\n                    var value = values[parseInt(index, 10) + 1];\n                    return toString(value, placeholderFormat ? placeholderFormat.substring(1) : '');\n                });\n            };\n            kendo._extractFormat = function (format) {\n                if (format.slice(0, 3) === '{0:') {\n                    format = format.slice(3, format.length - 1);\n                }\n                return format;\n            };\n            kendo._activeElement = function () {\n                try {\n                    return document.activeElement;\n                } catch (e) {\n                    return document.documentElement.activeElement;\n                }\n            };\n            kendo._round = round;\n            kendo._outerWidth = function (element, includeMargin) {\n                return $(element).outerWidth(includeMargin || false) || 0;\n            };\n            kendo._outerHeight = function (element, includeMargin) {\n                return $(element).outerHeight(includeMargin || false) || 0;\n            };\n            kendo.toString = toString;\n        }());\n        (function () {\n            var nonBreakingSpaceRegExp = /\\u00A0/g, exponentRegExp = /[eE][\\-+]?[0-9]+/, shortTimeZoneRegExp = /[+|\\-]\\d{1,2}/, longTimeZoneRegExp = /[+|\\-]\\d{1,2}:?\\d{2}/, dateRegExp = /^\\/Date\\((.*?)\\)\\/$/, offsetRegExp = /[+-]\\d*/, FORMATS_SEQUENCE = [\n                    [],\n                    [\n                        'G',\n                        'g',\n                        'F'\n                    ],\n                    [\n                        'D',\n                        'd',\n                        'y',\n                        'm',\n                        'T',\n                        't'\n                    ]\n                ], STANDARD_FORMATS = [\n                    [\n                        'yyyy-MM-ddTHH:mm:ss.fffffffzzz',\n                        'yyyy-MM-ddTHH:mm:ss.fffffff',\n                        'yyyy-MM-ddTHH:mm:ss.fffzzz',\n                        'yyyy-MM-ddTHH:mm:ss.fff',\n                        'ddd MMM dd yyyy HH:mm:ss',\n                        'yyyy-MM-ddTHH:mm:sszzz',\n                        'yyyy-MM-ddTHH:mmzzz',\n                        'yyyy-MM-ddTHH:mmzz',\n                        'yyyy-MM-ddTHH:mm:ss',\n                        'yyyy-MM-dd HH:mm:ss',\n                        'yyyy/MM/dd HH:mm:ss'\n                    ],\n                    [\n                        'yyyy-MM-ddTHH:mm',\n                        'yyyy-MM-dd HH:mm',\n                        'yyyy/MM/dd HH:mm'\n                    ],\n                    [\n                        'yyyy/MM/dd',\n                        'yyyy-MM-dd',\n                        'HH:mm:ss',\n                        'HH:mm'\n                    ]\n                ], numberRegExp = {\n                    2: /^\\d{1,2}/,\n                    3: /^\\d{1,3}/,\n                    4: /^\\d{4}/\n                }, objectToString = {}.toString;\n            function outOfRange(value, start, end) {\n                return !(value >= start && value <= end);\n            }\n            function designatorPredicate(designator) {\n                return designator.charAt(0);\n            }\n            function mapDesignators(designators) {\n                return $.map(designators, designatorPredicate);\n            }\n            function adjustDST(date, hours) {\n                if (!hours && date.getHours() === 23) {\n                    date.setHours(date.getHours() + 2);\n                }\n            }\n            function lowerArray(data) {\n                var idx = 0, length = data.length, array = [];\n                for (; idx < length; idx++) {\n                    array[idx] = (data[idx] + '').toLowerCase();\n                }\n                return array;\n            }\n            function lowerLocalInfo(localInfo) {\n                var newLocalInfo = {}, property;\n                for (property in localInfo) {\n                    newLocalInfo[property] = lowerArray(localInfo[property]);\n                }\n                return newLocalInfo;\n            }\n            function parseExact(value, format, culture, strict) {\n                if (!value) {\n                    return null;\n                }\n                var lookAhead = function (match) {\n                        var i = 0;\n                        while (format[idx] === match) {\n                            i++;\n                            idx++;\n                        }\n                        if (i > 0) {\n                            idx -= 1;\n                        }\n                        return i;\n                    }, getNumber = function (size) {\n                        var rg = numberRegExp[size] || new RegExp('^\\\\d{1,' + size + '}'), match = value.substr(valueIdx, size).match(rg);\n                        if (match) {\n                            match = match[0];\n                            valueIdx += match.length;\n                            return parseInt(match, 10);\n                        }\n                        return null;\n                    }, getIndexByName = function (names, lower) {\n                        var i = 0, length = names.length, name, nameLength, matchLength = 0, matchIdx = 0, subValue;\n                        for (; i < length; i++) {\n                            name = names[i];\n                            nameLength = name.length;\n                            subValue = value.substr(valueIdx, nameLength);\n                            if (lower) {\n                                subValue = subValue.toLowerCase();\n                            }\n                            if (subValue == name && nameLength > matchLength) {\n                                matchLength = nameLength;\n                                matchIdx = i;\n                            }\n                        }\n                        if (matchLength) {\n                            valueIdx += matchLength;\n                            return matchIdx + 1;\n                        }\n                        return null;\n                    }, checkLiteral = function () {\n                        var result = false;\n                        if (value.charAt(valueIdx) === format[idx]) {\n                            valueIdx++;\n                            result = true;\n                        }\n                        return result;\n                    }, calendar = culture.calendars.standard, year = null, month = null, day = null, hours = null, minutes = null, seconds = null, milliseconds = null, idx = 0, valueIdx = 0, literal = false, date = new Date(), twoDigitYearMax = calendar.twoDigitYearMax || 2029, defaultYear = date.getFullYear(), ch, count, length, pattern, pmHour, UTC, matches, amDesignators, pmDesignators, hoursOffset, minutesOffset, hasTime, match;\n                if (!format) {\n                    format = 'd';\n                }\n                pattern = calendar.patterns[format];\n                if (pattern) {\n                    format = pattern;\n                }\n                format = format.split('');\n                length = format.length;\n                for (; idx < length; idx++) {\n                    ch = format[idx];\n                    if (literal) {\n                        if (ch === '\\'') {\n                            literal = false;\n                        } else {\n                            checkLiteral();\n                        }\n                    } else {\n                        if (ch === 'd') {\n                            count = lookAhead('d');\n                            if (!calendar._lowerDays) {\n                                calendar._lowerDays = lowerLocalInfo(calendar.days);\n                            }\n                            if (day !== null && count > 2) {\n                                continue;\n                            }\n                            day = count < 3 ? getNumber(2) : getIndexByName(calendar._lowerDays[count == 3 ? 'namesAbbr' : 'names'], true);\n                            if (day === null || outOfRange(day, 1, 31)) {\n                                return null;\n                            }\n                        } else if (ch === 'M') {\n                            count = lookAhead('M');\n                            if (!calendar._lowerMonths) {\n                                calendar._lowerMonths = lowerLocalInfo(calendar.months);\n                            }\n                            month = count < 3 ? getNumber(2) : getIndexByName(calendar._lowerMonths[count == 3 ? 'namesAbbr' : 'names'], true);\n                            if (month === null || outOfRange(month, 1, 12)) {\n                                return null;\n                            }\n                            month -= 1;\n                        } else if (ch === 'y') {\n                            count = lookAhead('y');\n                            year = getNumber(count);\n                            if (year === null) {\n                                return null;\n                            }\n                            if (count == 2) {\n                                if (typeof twoDigitYearMax === 'string') {\n                                    twoDigitYearMax = defaultYear + parseInt(twoDigitYearMax, 10);\n                                }\n                                year = defaultYear - defaultYear % 100 + year;\n                                if (year > twoDigitYearMax) {\n                                    year -= 100;\n                                }\n                            }\n                        } else if (ch === 'h') {\n                            lookAhead('h');\n                            hours = getNumber(2);\n                            if (hours == 12) {\n                                hours = 0;\n                            }\n                            if (hours === null || outOfRange(hours, 0, 11)) {\n                                return null;\n                            }\n                        } else if (ch === 'H') {\n                            lookAhead('H');\n                            hours = getNumber(2);\n                            if (hours === null || outOfRange(hours, 0, 23)) {\n                                return null;\n                            }\n                        } else if (ch === 'm') {\n                            lookAhead('m');\n                            minutes = getNumber(2);\n                            if (minutes === null || outOfRange(minutes, 0, 59)) {\n                                return null;\n                            }\n                        } else if (ch === 's') {\n                            lookAhead('s');\n                            seconds = getNumber(2);\n                            if (seconds === null || outOfRange(seconds, 0, 59)) {\n                                return null;\n                            }\n                        } else if (ch === 'f') {\n                            count = lookAhead('f');\n                            match = value.substr(valueIdx, count).match(numberRegExp[3]);\n                            milliseconds = getNumber(count);\n                            if (milliseconds !== null) {\n                                milliseconds = parseFloat('0.' + match[0], 10);\n                                milliseconds = kendo._round(milliseconds, 3);\n                                milliseconds *= 1000;\n                            }\n                            if (milliseconds === null || outOfRange(milliseconds, 0, 999)) {\n                                return null;\n                            }\n                        } else if (ch === 't') {\n                            count = lookAhead('t');\n                            amDesignators = calendar.AM;\n                            pmDesignators = calendar.PM;\n                            if (count === 1) {\n                                amDesignators = mapDesignators(amDesignators);\n                                pmDesignators = mapDesignators(pmDesignators);\n                            }\n                            pmHour = getIndexByName(pmDesignators);\n                            if (!pmHour && !getIndexByName(amDesignators)) {\n                                return null;\n                            }\n                        } else if (ch === 'z') {\n                            UTC = true;\n                            count = lookAhead('z');\n                            if (value.substr(valueIdx, 1) === 'Z') {\n                                checkLiteral();\n                                continue;\n                            }\n                            matches = value.substr(valueIdx, 6).match(count > 2 ? longTimeZoneRegExp : shortTimeZoneRegExp);\n                            if (!matches) {\n                                return null;\n                            }\n                            matches = matches[0].split(':');\n                            hoursOffset = matches[0];\n                            minutesOffset = matches[1];\n                            if (!minutesOffset && hoursOffset.length > 3) {\n                                valueIdx = hoursOffset.length - 2;\n                                minutesOffset = hoursOffset.substring(valueIdx);\n                                hoursOffset = hoursOffset.substring(0, valueIdx);\n                            }\n                            hoursOffset = parseInt(hoursOffset, 10);\n                            if (outOfRange(hoursOffset, -12, 13)) {\n                                return null;\n                            }\n                            if (count > 2) {\n                                minutesOffset = matches[0][0] + minutesOffset;\n                                minutesOffset = parseInt(minutesOffset, 10);\n                                if (isNaN(minutesOffset) || outOfRange(minutesOffset, -59, 59)) {\n                                    return null;\n                                }\n                            }\n                        } else if (ch === '\\'') {\n                            literal = true;\n                            checkLiteral();\n                        } else if (!checkLiteral()) {\n                            return null;\n                        }\n                    }\n                }\n                if (strict && !/^\\s*$/.test(value.substr(valueIdx))) {\n                    return null;\n                }\n                hasTime = hours !== null || minutes !== null || seconds || null;\n                if (year === null && month === null && day === null && hasTime) {\n                    year = defaultYear;\n                    month = date.getMonth();\n                    day = date.getDate();\n                } else {\n                    if (year === null) {\n                        year = defaultYear;\n                    }\n                    if (day === null) {\n                        day = 1;\n                    }\n                }\n                if (pmHour && hours < 12) {\n                    hours += 12;\n                }\n                if (UTC) {\n                    if (hoursOffset) {\n                        hours += -hoursOffset;\n                    }\n                    if (minutesOffset) {\n                        minutes += -minutesOffset;\n                    }\n                    value = new Date(Date.UTC(year, month, day, hours, minutes, seconds, milliseconds));\n                } else {\n                    value = new Date(year, month, day, hours, minutes, seconds, milliseconds);\n                    adjustDST(value, hours);\n                }\n                if (year < 100) {\n                    value.setFullYear(year);\n                }\n                if (value.getDate() !== day && UTC === undefined) {\n                    return null;\n                }\n                return value;\n            }\n            function parseMicrosoftFormatOffset(offset) {\n                var sign = offset.substr(0, 1) === '-' ? -1 : 1;\n                offset = offset.substring(1);\n                offset = parseInt(offset.substr(0, 2), 10) * 60 + parseInt(offset.substring(2), 10);\n                return sign * offset;\n            }\n            function getDefaultFormats(culture) {\n                var length = math.max(FORMATS_SEQUENCE.length, STANDARD_FORMATS.length);\n                var calendar = culture.calendar || culture.calendars.standard;\n                var patterns = calendar.patterns;\n                var cultureFormats, formatIdx, idx;\n                var formats = [];\n                for (idx = 0; idx < length; idx++) {\n                    cultureFormats = FORMATS_SEQUENCE[idx];\n                    for (formatIdx = 0; formatIdx < cultureFormats.length; formatIdx++) {\n                        formats.push(patterns[cultureFormats[formatIdx]]);\n                    }\n                    formats = formats.concat(STANDARD_FORMATS[idx]);\n                }\n                return formats;\n            }\n            function internalParseDate(value, formats, culture, strict) {\n                if (objectToString.call(value) === '[object Date]') {\n                    return value;\n                }\n                var idx = 0;\n                var date = null;\n                var length;\n                var tzoffset;\n                if (value && value.indexOf('/D') === 0) {\n                    date = dateRegExp.exec(value);\n                    if (date) {\n                        date = date[1];\n                        tzoffset = offsetRegExp.exec(date.substring(1));\n                        date = new Date(parseInt(date, 10));\n                        if (tzoffset) {\n                            tzoffset = parseMicrosoftFormatOffset(tzoffset[0]);\n                            date = kendo.timezone.apply(date, 0);\n                            date = kendo.timezone.convert(date, 0, -1 * tzoffset);\n                        }\n                        return date;\n                    }\n                }\n                culture = kendo.getCulture(culture);\n                if (!formats) {\n                    formats = getDefaultFormats(culture);\n                }\n                formats = isArray(formats) ? formats : [formats];\n                length = formats.length;\n                for (; idx < length; idx++) {\n                    date = parseExact(value, formats[idx], culture, strict);\n                    if (date) {\n                        return date;\n                    }\n                }\n                return date;\n            }\n            kendo.parseDate = function (value, formats, culture) {\n                return internalParseDate(value, formats, culture, false);\n            };\n            kendo.parseExactDate = function (value, formats, culture) {\n                return internalParseDate(value, formats, culture, true);\n            };\n            kendo.parseInt = function (value, culture) {\n                var result = kendo.parseFloat(value, culture);\n                if (result) {\n                    result = result | 0;\n                }\n                return result;\n            };\n            kendo.parseFloat = function (value, culture, format) {\n                if (!value && value !== 0) {\n                    return null;\n                }\n                if (typeof value === NUMBER) {\n                    return value;\n                }\n                value = value.toString();\n                culture = kendo.getCulture(culture);\n                var number = culture.numberFormat, percent = number.percent, currency = number.currency, symbol = currency.symbol, percentSymbol = percent.symbol, negative = value.indexOf('-'), parts, isPercent;\n                if (exponentRegExp.test(value)) {\n                    value = parseFloat(value.replace(number['.'], '.'));\n                    if (isNaN(value)) {\n                        value = null;\n                    }\n                    return value;\n                }\n                if (negative > 0) {\n                    return null;\n                } else {\n                    negative = negative > -1;\n                }\n                if (value.indexOf(symbol) > -1 || format && format.toLowerCase().indexOf('c') > -1) {\n                    number = currency;\n                    parts = number.pattern[0].replace('$', symbol).split('n');\n                    if (value.indexOf(parts[0]) > -1 && value.indexOf(parts[1]) > -1) {\n                        value = value.replace(parts[0], '').replace(parts[1], '');\n                        negative = true;\n                    }\n                } else if (value.indexOf(percentSymbol) > -1) {\n                    isPercent = true;\n                    number = percent;\n                    symbol = percentSymbol;\n                }\n                value = value.replace('-', '').replace(symbol, '').replace(nonBreakingSpaceRegExp, ' ').split(number[','].replace(nonBreakingSpaceRegExp, ' ')).join('').replace(number['.'], '.');\n                value = parseFloat(value);\n                if (isNaN(value)) {\n                    value = null;\n                } else if (negative) {\n                    value *= -1;\n                }\n                if (value && isPercent) {\n                    value /= 100;\n                }\n                return value;\n            };\n        }());\n        function getShadows(element) {\n            var shadow = element.css(kendo.support.transitions.css + 'box-shadow') || element.css('box-shadow'), radius = shadow ? shadow.match(boxShadowRegExp) || [\n                    0,\n                    0,\n                    0,\n                    0,\n                    0\n                ] : [\n                    0,\n                    0,\n                    0,\n                    0,\n                    0\n                ], blur = math.max(+radius[3], +(radius[4] || 0));\n            return {\n                left: -radius[1] + blur,\n                right: +radius[1] + blur,\n                bottom: +radius[2] + blur\n            };\n        }\n        function wrap(element, autosize) {\n            var browser = support.browser, percentage, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, parent = element.parent(), windowOuterWidth = outerWidth(window);\n            parent.removeClass('k-animation-container-sm');\n            if (!parent.hasClass('k-animation-container')) {\n                var width = element[0].style.width, height = element[0].style.height, percentWidth = percentRegExp.test(width), percentHeight = percentRegExp.test(height), forceWidth = element.hasClass('k-tooltip') || element.is('.k-menu-horizontal.k-context-menu');\n                percentage = percentWidth || percentHeight;\n                if (!percentWidth && (!autosize || autosize && width || forceWidth)) {\n                    width = autosize ? outerWidth(element) + 1 : outerWidth(element);\n                }\n                if (!percentHeight && (!autosize || autosize && height) || element.is('.k-menu-horizontal.k-context-menu')) {\n                    height = outerHeight(element);\n                }\n                element.wrap($('<div/>').addClass('k-animation-container').css({\n                    width: width,\n                    height: height\n                }));\n                parent = element.parent();\n                if (percentage) {\n                    element.css({\n                        width: '100%',\n                        height: '100%',\n                        boxSizing: 'border-box',\n                        mozBoxSizing: 'border-box',\n                        webkitBoxSizing: 'border-box'\n                    });\n                }\n            } else {\n                wrapResize(element, autosize);\n            }\n            if (windowOuterWidth < outerWidth(parent)) {\n                parent.addClass('k-animation-container-sm');\n                wrapResize(element, autosize);\n            }\n            if (browser.msie && math.floor(browser.version) <= 7) {\n                element.css({ zoom: 1 });\n                element.children('.k-menu').width(element.width());\n            }\n            return parent;\n        }\n        function wrapResize(element, autosize) {\n            var percentage, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, wrapper = element.parent('.k-animation-container'), wrapperStyle = wrapper[0].style;\n            if (wrapper.is(':hidden')) {\n                wrapper.css({\n                    display: '',\n                    position: ''\n                });\n            }\n            percentage = percentRegExp.test(wrapperStyle.width) || percentRegExp.test(wrapperStyle.height);\n            if (!percentage) {\n                wrapper.css({\n                    width: autosize ? outerWidth(element) + 1 : outerWidth(element),\n                    height: outerHeight(element),\n                    boxSizing: 'content-box',\n                    mozBoxSizing: 'content-box',\n                    webkitBoxSizing: 'content-box'\n                });\n            }\n        }\n        function deepExtend(destination) {\n            var i = 1, length = arguments.length;\n            for (i = 1; i < length; i++) {\n                deepExtendOne(destination, arguments[i]);\n            }\n            return destination;\n        }\n        function deepExtendOne(destination, source) {\n            var ObservableArray = kendo.data.ObservableArray, LazyObservableArray = kendo.data.LazyObservableArray, DataSource = kendo.data.DataSource, HierarchicalDataSource = kendo.data.HierarchicalDataSource, property, propValue, propType, propInit, destProp;\n            for (property in source) {\n                propValue = source[property];\n                propType = typeof propValue;\n                if (propType === OBJECT && propValue !== null) {\n                    propInit = propValue.constructor;\n                } else {\n                    propInit = null;\n                }\n                if (propInit && propInit !== Array && propInit !== ObservableArray && propInit !== LazyObservableArray && propInit !== DataSource && propInit !== HierarchicalDataSource && propInit !== RegExp) {\n                    if (propValue instanceof Date) {\n                        destination[property] = new Date(propValue.getTime());\n                    } else if (isFunction(propValue.clone)) {\n                        destination[property] = propValue.clone();\n                    } else {\n                        destProp = destination[property];\n                        if (typeof destProp === OBJECT) {\n                            destination[property] = destProp || {};\n                        } else {\n                            destination[property] = {};\n                        }\n                        deepExtendOne(destination[property], propValue);\n                    }\n                } else if (propType !== UNDEFINED) {\n                    destination[property] = propValue;\n                }\n            }\n            return destination;\n        }\n        function testRx(agent, rxs, dflt) {\n            for (var rx in rxs) {\n                if (rxs.hasOwnProperty(rx) && rxs[rx].test(agent)) {\n                    return rx;\n                }\n            }\n            return dflt !== undefined ? dflt : agent;\n        }\n        function toHyphens(str) {\n            return str.replace(/([a-z][A-Z])/g, function (g) {\n                return g.charAt(0) + '-' + g.charAt(1).toLowerCase();\n            });\n        }\n        function toCamelCase(str) {\n            return str.replace(/\\-(\\w)/g, function (strMatch, g1) {\n                return g1.toUpperCase();\n            });\n        }\n        function getComputedStyles(element, properties) {\n            var styles = {}, computedStyle;\n            if (document.defaultView && document.defaultView.getComputedStyle) {\n                computedStyle = document.defaultView.getComputedStyle(element, '');\n                if (properties) {\n                    $.each(properties, function (idx, value) {\n                        styles[value] = computedStyle.getPropertyValue(value);\n                    });\n                }\n            } else {\n                computedStyle = element.currentStyle;\n                if (properties) {\n                    $.each(properties, function (idx, value) {\n                        styles[value] = computedStyle[toCamelCase(value)];\n                    });\n                }\n            }\n            if (!kendo.size(styles)) {\n                styles = computedStyle;\n            }\n            return styles;\n        }\n        function isScrollable(element) {\n            if (element && element.className && typeof element.className === 'string' && element.className.indexOf('k-auto-scrollable') > -1) {\n                return true;\n            }\n            var overflow = getComputedStyles(element, ['overflow']).overflow;\n            return overflow == 'auto' || overflow == 'scroll';\n        }\n        function scrollLeft(element, value) {\n            var webkit = support.browser.webkit;\n            var mozila = support.browser.mozilla;\n            var el = element instanceof $ ? element[0] : element;\n            var isRtl;\n            if (!element) {\n                return;\n            }\n            isRtl = support.isRtl(element);\n            if (value !== undefined) {\n                if (isRtl && webkit) {\n                    el.scrollLeft = el.scrollWidth - el.clientWidth - value;\n                } else if (isRtl && mozila) {\n                    el.scrollLeft = -value;\n                } else {\n                    el.scrollLeft = value;\n                }\n            } else {\n                if (isRtl && webkit) {\n                    return el.scrollWidth - el.clientWidth - el.scrollLeft;\n                } else {\n                    return Math.abs(el.scrollLeft);\n                }\n            }\n        }\n        (function () {\n            support._scrollbar = undefined;\n            support.scrollbar = function (refresh) {\n                if (!isNaN(support._scrollbar) && !refresh) {\n                    return support._scrollbar;\n                } else {\n                    var div = document.createElement('div'), result;\n                    div.style.cssText = 'overflow:scroll;overflow-x:hidden;zoom:1;clear:both;display:block';\n                    div.innerHTML = '&nbsp;';\n                    document.body.appendChild(div);\n                    support._scrollbar = result = div.offsetWidth - div.scrollWidth;\n                    document.body.removeChild(div);\n                    return result;\n                }\n            };\n            support.isRtl = function (element) {\n                return $(element).closest('.k-rtl').length > 0;\n            };\n            var table = document.createElement('table');\n            try {\n                table.innerHTML = '<tr><td></td></tr>';\n                support.tbodyInnerHtml = true;\n            } catch (e) {\n                support.tbodyInnerHtml = false;\n            }\n            support.touch = 'ontouchstart' in window;\n            var docStyle = document.documentElement.style;\n            var transitions = support.transitions = false, transforms = support.transforms = false, elementProto = 'HTMLElement' in window ? HTMLElement.prototype : [];\n            support.hasHW3D = 'WebKitCSSMatrix' in window && 'm11' in new window.WebKitCSSMatrix() || 'MozPerspective' in docStyle || 'msPerspective' in docStyle;\n            support.cssFlexbox = 'flexWrap' in docStyle || 'WebkitFlexWrap' in docStyle || 'msFlexWrap' in docStyle;\n            each([\n                'Moz',\n                'webkit',\n                'O',\n                'ms'\n            ], function () {\n                var prefix = this.toString(), hasTransitions = typeof table.style[prefix + 'Transition'] === STRING;\n                if (hasTransitions || typeof table.style[prefix + 'Transform'] === STRING) {\n                    var lowPrefix = prefix.toLowerCase();\n                    transforms = {\n                        css: lowPrefix != 'ms' ? '-' + lowPrefix + '-' : '',\n                        prefix: prefix,\n                        event: lowPrefix === 'o' || lowPrefix === 'webkit' ? lowPrefix : ''\n                    };\n                    if (hasTransitions) {\n                        transitions = transforms;\n                        transitions.event = transitions.event ? transitions.event + 'TransitionEnd' : 'transitionend';\n                    }\n                    return false;\n                }\n            });\n            table = null;\n            support.transforms = transforms;\n            support.transitions = transitions;\n            support.devicePixelRatio = window.devicePixelRatio === undefined ? 1 : window.devicePixelRatio;\n            try {\n                support.screenWidth = window.outerWidth || window.screen ? window.screen.availWidth : window.innerWidth;\n                support.screenHeight = window.outerHeight || window.screen ? window.screen.availHeight : window.innerHeight;\n            } catch (e) {\n                support.screenWidth = window.screen.availWidth;\n                support.screenHeight = window.screen.availHeight;\n            }\n            support.detectOS = function (ua) {\n                var os = false, minorVersion, match = [], notAndroidPhone = !/mobile safari/i.test(ua), agentRxs = {\n                        wp: /(Windows Phone(?: OS)?)\\s(\\d+)\\.(\\d+(\\.\\d+)?)/,\n                        fire: /(Silk)\\/(\\d+)\\.(\\d+(\\.\\d+)?)/,\n                        android: /(Android|Android.*(?:Opera|Firefox).*?\\/)\\s*(\\d+)\\.?(\\d+(\\.\\d+)?)?/,\n                        iphone: /(iPhone|iPod).*OS\\s+(\\d+)[\\._]([\\d\\._]+)/,\n                        ipad: /(iPad).*OS\\s+(\\d+)[\\._]([\\d_]+)/,\n                        meego: /(MeeGo).+NokiaBrowser\\/(\\d+)\\.([\\d\\._]+)/,\n                        webos: /(webOS)\\/(\\d+)\\.(\\d+(\\.\\d+)?)/,\n                        blackberry: /(BlackBerry|BB10).*?Version\\/(\\d+)\\.(\\d+(\\.\\d+)?)/,\n                        playbook: /(PlayBook).*?Tablet\\s*OS\\s*(\\d+)\\.(\\d+(\\.\\d+)?)/,\n                        windows: /(MSIE)\\s+(\\d+)\\.(\\d+(\\.\\d+)?)/,\n                        tizen: /(tizen).*?Version\\/(\\d+)\\.(\\d+(\\.\\d+)?)/i,\n                        sailfish: /(sailfish).*rv:(\\d+)\\.(\\d+(\\.\\d+)?).*firefox/i,\n                        ffos: /(Mobile).*rv:(\\d+)\\.(\\d+(\\.\\d+)?).*Firefox/\n                    }, osRxs = {\n                        ios: /^i(phone|pad|pod)$/i,\n                        android: /^android|fire$/i,\n                        blackberry: /^blackberry|playbook/i,\n                        windows: /windows/,\n                        wp: /wp/,\n                        flat: /sailfish|ffos|tizen/i,\n                        meego: /meego/\n                    }, formFactorRxs = { tablet: /playbook|ipad|fire/i }, browserRxs = {\n                        omini: /Opera\\sMini/i,\n                        omobile: /Opera\\sMobi/i,\n                        firefox: /Firefox|Fennec/i,\n                        mobilesafari: /version\\/.*safari/i,\n                        ie: /MSIE|Windows\\sPhone/i,\n                        chrome: /chrome|crios/i,\n                        webkit: /webkit/i\n                    };\n                for (var agent in agentRxs) {\n                    if (agentRxs.hasOwnProperty(agent)) {\n                        match = ua.match(agentRxs[agent]);\n                        if (match) {\n                            if (agent == 'windows' && 'plugins' in navigator) {\n                                return false;\n                            }\n                            os = {};\n                            os.device = agent;\n                            os.tablet = testRx(agent, formFactorRxs, false);\n                            os.browser = testRx(ua, browserRxs, 'default');\n                            os.name = testRx(agent, osRxs);\n                            os[os.name] = true;\n                            os.majorVersion = match[2];\n                            os.minorVersion = (match[3] || '0').replace('_', '.');\n                            minorVersion = os.minorVersion.replace('.', '').substr(0, 2);\n                            os.flatVersion = os.majorVersion + minorVersion + new Array(3 - (minorVersion.length < 3 ? minorVersion.length : 2)).join('0');\n                            os.cordova = typeof window.PhoneGap !== UNDEFINED || typeof window.cordova !== UNDEFINED;\n                            os.appMode = window.navigator.standalone || /file|local|wmapp/.test(window.location.protocol) || os.cordova;\n                            if (os.android && (support.devicePixelRatio < 1.5 && os.flatVersion < 400 || notAndroidPhone) && (support.screenWidth > 800 || support.screenHeight > 800)) {\n                                os.tablet = agent;\n                            }\n                            break;\n                        }\n                    }\n                }\n                return os;\n            };\n            var mobileOS = support.mobileOS = support.detectOS(navigator.userAgent);\n            support.wpDevicePixelRatio = mobileOS.wp ? screen.width / 320 : 0;\n            support.hasNativeScrolling = false;\n            if (mobileOS.ios || mobileOS.android && mobileOS.majorVersion > 2 || mobileOS.wp) {\n                support.hasNativeScrolling = mobileOS;\n            }\n            support.delayedClick = function () {\n                if (support.touch) {\n                    if (mobileOS.ios) {\n                        return true;\n                    }\n                    if (mobileOS.android) {\n                        if (!support.browser.chrome) {\n                            return true;\n                        }\n                        if (support.browser.version < 32) {\n                            return false;\n                        }\n                        return !($('meta[name=viewport]').attr('content') || '').match(/user-scalable=no/i);\n                    }\n                }\n                return false;\n            };\n            support.mouseAndTouchPresent = support.touch && !(support.mobileOS.ios || support.mobileOS.android);\n            support.detectBrowser = function (ua) {\n                var browser = false, match = [], browserRxs = {\n                        edge: /(edge)[ \\/]([\\w.]+)/i,\n                        webkit: /(chrome|crios)[ \\/]([\\w.]+)/i,\n                        safari: /(webkit)[ \\/]([\\w.]+)/i,\n                        opera: /(opera)(?:.*version|)[ \\/]([\\w.]+)/i,\n                        msie: /(msie\\s|trident.*? rv:)([\\w.]+)/i,\n                        mozilla: /(mozilla)(?:.*? rv:([\\w.]+)|)/i\n                    };\n                for (var agent in browserRxs) {\n                    if (browserRxs.hasOwnProperty(agent)) {\n                        match = ua.match(browserRxs[agent]);\n                        if (match) {\n                            browser = {};\n                            browser[agent] = true;\n                            browser[match[1].toLowerCase().split(' ')[0].split('/')[0]] = true;\n                            browser.version = parseInt(document.documentMode || match[2], 10);\n                            break;\n                        }\n                    }\n                }\n                return browser;\n            };\n            support.browser = support.detectBrowser(navigator.userAgent);\n            support.detectClipboardAccess = function () {\n                var commands = {\n                    copy: document.queryCommandSupported ? document.queryCommandSupported('copy') : false,\n                    cut: document.queryCommandSupported ? document.queryCommandSupported('cut') : false,\n                    paste: document.queryCommandSupported ? document.queryCommandSupported('paste') : false\n                };\n                if (support.browser.chrome) {\n                    commands.paste = false;\n                    if (support.browser.version >= 43) {\n                        commands.copy = true;\n                        commands.cut = true;\n                    }\n                }\n                return commands;\n            };\n            support.clipboard = support.detectClipboardAccess();\n            support.zoomLevel = function () {\n                try {\n                    var browser = support.browser;\n                    var ie11WidthCorrection = 0;\n                    var docEl = document.documentElement;\n                    if (browser.msie && browser.version == 11 && docEl.scrollHeight > docEl.clientHeight && !support.touch) {\n                        ie11WidthCorrection = support.scrollbar();\n                    }\n                    return support.touch ? docEl.clientWidth / window.innerWidth : browser.msie && browser.version >= 10 ? ((top || window).document.documentElement.offsetWidth + ie11WidthCorrection) / (top || window).innerWidth : 1;\n                } catch (e) {\n                    return 1;\n                }\n            };\n            support.cssBorderSpacing = typeof docStyle.borderSpacing != 'undefined' && !(support.browser.msie && support.browser.version < 8);\n            (function (browser) {\n                var cssClass = '', docElement = $(document.documentElement), majorVersion = parseInt(browser.version, 10);\n                if (browser.msie) {\n                    cssClass = 'ie';\n                } else if (browser.mozilla) {\n                    cssClass = 'ff';\n                } else if (browser.safari) {\n                    cssClass = 'safari';\n                } else if (browser.webkit) {\n                    cssClass = 'webkit';\n                } else if (browser.opera) {\n                    cssClass = 'opera';\n                } else if (browser.edge) {\n                    cssClass = 'edge';\n                }\n                if (cssClass) {\n                    cssClass = 'k-' + cssClass + ' k-' + cssClass + majorVersion;\n                }\n                if (support.mobileOS) {\n                    cssClass += ' k-mobile';\n                }\n                if (!support.cssFlexbox) {\n                    cssClass += ' k-no-flexbox';\n                }\n                docElement.addClass(cssClass);\n            }(support.browser));\n            support.eventCapture = document.documentElement.addEventListener;\n            var input = document.createElement('input');\n            support.placeholder = 'placeholder' in input;\n            support.propertyChangeEvent = 'onpropertychange' in input;\n            support.input = function () {\n                var types = [\n                    'number',\n                    'date',\n                    'time',\n                    'month',\n                    'week',\n                    'datetime',\n                    'datetime-local'\n                ];\n                var length = types.length;\n                var value = 'test';\n                var result = {};\n                var idx = 0;\n                var type;\n                for (; idx < length; idx++) {\n                    type = types[idx];\n                    input.setAttribute('type', type);\n                    input.value = value;\n                    result[type.replace('-', '')] = input.type !== 'text' && input.value !== value;\n                }\n                return result;\n            }();\n            input.style.cssText = 'float:left;';\n            support.cssFloat = !!input.style.cssFloat;\n            input = null;\n            support.stableSort = function () {\n                var threshold = 513;\n                var sorted = [{\n                        index: 0,\n                        field: 'b'\n                    }];\n                for (var i = 1; i < threshold; i++) {\n                    sorted.push({\n                        index: i,\n                        field: 'a'\n                    });\n                }\n                sorted.sort(function (a, b) {\n                    return a.field > b.field ? 1 : a.field < b.field ? -1 : 0;\n                });\n                return sorted[0].index === 1;\n            }();\n            support.matchesSelector = elementProto.webkitMatchesSelector || elementProto.mozMatchesSelector || elementProto.msMatchesSelector || elementProto.oMatchesSelector || elementProto.matchesSelector || elementProto.matches || function (selector) {\n                var nodeList = document.querySelectorAll ? (this.parentNode || document).querySelectorAll(selector) || [] : $(selector), i = nodeList.length;\n                while (i--) {\n                    if (nodeList[i] == this) {\n                        return true;\n                    }\n                }\n                return false;\n            };\n            support.matchMedia = 'matchMedia' in window;\n            support.pushState = window.history && window.history.pushState;\n            var documentMode = document.documentMode;\n            support.hashChange = 'onhashchange' in window && !(support.browser.msie && (!documentMode || documentMode <= 8));\n            support.customElements = 'registerElement' in window.document;\n            var chrome = support.browser.chrome, mozilla = support.browser.mozilla;\n            support.msPointers = !chrome && window.MSPointerEvent;\n            support.pointers = !chrome && !mozilla && window.PointerEvent;\n            support.kineticScrollNeeded = mobileOS && (support.touch || support.msPointers || support.pointers);\n        }());\n        function size(obj) {\n            var result = 0, key;\n            for (key in obj) {\n                if (obj.hasOwnProperty(key) && key != 'toJSON') {\n                    result++;\n                }\n            }\n            return result;\n        }\n        function getOffset(element, type, positioned) {\n            if (!type) {\n                type = 'offset';\n            }\n            var offset = element[type]();\n            var result = {\n                top: offset.top,\n                right: offset.right,\n                bottom: offset.bottom,\n                left: offset.left\n            };\n            if (support.browser.msie && (support.pointers || support.msPointers) && !positioned) {\n                var sign = support.isRtl(element) ? 1 : -1;\n                result.top -= window.pageYOffset - document.documentElement.scrollTop;\n                result.left -= window.pageXOffset + sign * document.documentElement.scrollLeft;\n            }\n            return result;\n        }\n        var directions = {\n            left: { reverse: 'right' },\n            right: { reverse: 'left' },\n            down: { reverse: 'up' },\n            up: { reverse: 'down' },\n            top: { reverse: 'bottom' },\n            bottom: { reverse: 'top' },\n            'in': { reverse: 'out' },\n            out: { reverse: 'in' }\n        };\n        function parseEffects(input) {\n            var effects = {};\n            each(typeof input === 'string' ? input.split(' ') : input, function (idx) {\n                effects[idx] = this;\n            });\n            return effects;\n        }\n        function fx(element) {\n            return new kendo.effects.Element(element);\n        }\n        var effects = {};\n        $.extend(effects, {\n            enabled: true,\n            Element: function (element) {\n                this.element = $(element);\n            },\n            promise: function (element, options) {\n                if (!element.is(':visible')) {\n                    element.css({ display: element.data('olddisplay') || 'block' }).css('display');\n                }\n                if (options.hide) {\n                    element.data('olddisplay', element.css('display')).hide();\n                }\n                if (options.init) {\n                    options.init();\n                }\n                if (options.completeCallback) {\n                    options.completeCallback(element);\n                }\n                element.dequeue();\n            },\n            disable: function () {\n                this.enabled = false;\n                this.promise = this.promiseShim;\n            },\n            enable: function () {\n                this.enabled = true;\n                this.promise = this.animatedPromise;\n            }\n        });\n        effects.promiseShim = effects.promise;\n        function prepareAnimationOptions(options, duration, reverse, complete) {\n            if (typeof options === STRING) {\n                if (isFunction(duration)) {\n                    complete = duration;\n                    duration = 400;\n                    reverse = false;\n                }\n                if (isFunction(reverse)) {\n                    complete = reverse;\n                    reverse = false;\n                }\n                if (typeof duration === BOOLEAN) {\n                    reverse = duration;\n                    duration = 400;\n                }\n                options = {\n                    effects: options,\n                    duration: duration,\n                    reverse: reverse,\n                    complete: complete\n                };\n            }\n            return extend({\n                effects: {},\n                duration: 400,\n                reverse: false,\n                init: noop,\n                teardown: noop,\n                hide: false\n            }, options, {\n                completeCallback: options.complete,\n                complete: noop\n            });\n        }\n        function animate(element, options, duration, reverse, complete) {\n            var idx = 0, length = element.length, instance;\n            for (; idx < length; idx++) {\n                instance = $(element[idx]);\n                instance.queue(function () {\n                    effects.promise(instance, prepareAnimationOptions(options, duration, reverse, complete));\n                });\n            }\n            return element;\n        }\n        function toggleClass(element, classes, options, add) {\n            if (classes) {\n                classes = classes.split(' ');\n                each(classes, function (idx, value) {\n                    element.toggleClass(value, add);\n                });\n            }\n            return element;\n        }\n        if (!('kendoAnimate' in $.fn)) {\n            extend($.fn, {\n                kendoStop: function (clearQueue, gotoEnd) {\n                    return this.stop(clearQueue, gotoEnd);\n                },\n                kendoAnimate: function (options, duration, reverse, complete) {\n                    return animate(this, options, duration, reverse, complete);\n                },\n                kendoAddClass: function (classes, options) {\n                    return kendo.toggleClass(this, classes, options, true);\n                },\n                kendoRemoveClass: function (classes, options) {\n                    return kendo.toggleClass(this, classes, options, false);\n                },\n                kendoToggleClass: function (classes, options, toggle) {\n                    return kendo.toggleClass(this, classes, options, toggle);\n                }\n            });\n        }\n        var ampRegExp = /&/g, ltRegExp = /</g, quoteRegExp = /\"/g, aposRegExp = /'/g, gtRegExp = />/g;\n        function htmlEncode(value) {\n            return ('' + value).replace(ampRegExp, '&amp;').replace(ltRegExp, '&lt;').replace(gtRegExp, '&gt;').replace(quoteRegExp, '&quot;').replace(aposRegExp, '&#39;');\n        }\n        var eventTarget = function (e) {\n            return e.target;\n        };\n        if (support.touch) {\n            eventTarget = function (e) {\n                var touches = 'originalEvent' in e ? e.originalEvent.changedTouches : 'changedTouches' in e ? e.changedTouches : null;\n                return touches ? document.elementFromPoint(touches[0].clientX, touches[0].clientY) : e.target;\n            };\n            each([\n                'swipe',\n                'swipeLeft',\n                'swipeRight',\n                'swipeUp',\n                'swipeDown',\n                'doubleTap',\n                'tap'\n            ], function (m, value) {\n                $.fn[value] = function (callback) {\n                    return this.bind(value, callback);\n                };\n            });\n        }\n        if (support.touch) {\n            if (!support.mobileOS) {\n                support.mousedown = 'mousedown touchstart';\n                support.mouseup = 'mouseup touchend';\n                support.mousemove = 'mousemove touchmove';\n                support.mousecancel = 'mouseleave touchcancel';\n                support.click = 'click';\n                support.resize = 'resize';\n            } else {\n                support.mousedown = 'touchstart';\n                support.mouseup = 'touchend';\n                support.mousemove = 'touchmove';\n                support.mousecancel = 'touchcancel';\n                support.click = 'touchend';\n                support.resize = 'orientationchange';\n            }\n        } else if (support.pointers) {\n            support.mousemove = 'pointermove';\n            support.mousedown = 'pointerdown';\n            support.mouseup = 'pointerup';\n            support.mousecancel = 'pointercancel';\n            support.click = 'pointerup';\n            support.resize = 'orientationchange resize';\n        } else if (support.msPointers) {\n            support.mousemove = 'MSPointerMove';\n            support.mousedown = 'MSPointerDown';\n            support.mouseup = 'MSPointerUp';\n            support.mousecancel = 'MSPointerCancel';\n            support.click = 'MSPointerUp';\n            support.resize = 'orientationchange resize';\n        } else {\n            support.mousemove = 'mousemove';\n            support.mousedown = 'mousedown';\n            support.mouseup = 'mouseup';\n            support.mousecancel = 'mouseleave';\n            support.click = 'click';\n            support.resize = 'resize';\n        }\n        var wrapExpression = function (members, paramName) {\n                var result = paramName || 'd', index, idx, length, member, count = 1;\n                for (idx = 0, length = members.length; idx < length; idx++) {\n                    member = members[idx];\n                    if (member !== '') {\n                        index = member.indexOf('[');\n                        if (index !== 0) {\n                            if (index == -1) {\n                                member = '.' + member;\n                            } else {\n                                count++;\n                                member = '.' + member.substring(0, index) + ' || {})' + member.substring(index);\n                            }\n                        }\n                        count++;\n                        result += member + (idx < length - 1 ? ' || {})' : ')');\n                    }\n                }\n                return new Array(count).join('(') + result;\n            }, localUrlRe = /^([a-z]+:)?\\/\\//i;\n        extend(kendo, {\n            widgets: [],\n            _widgetRegisteredCallbacks: [],\n            ui: kendo.ui || {},\n            fx: kendo.fx || fx,\n            effects: kendo.effects || effects,\n            mobile: kendo.mobile || {},\n            data: kendo.data || {},\n            dataviz: kendo.dataviz || {},\n            drawing: kendo.drawing || {},\n            spreadsheet: { messages: {} },\n            keys: {\n                INSERT: 45,\n                DELETE: 46,\n                BACKSPACE: 8,\n                TAB: 9,\n                ENTER: 13,\n                ESC: 27,\n                LEFT: 37,\n                UP: 38,\n                RIGHT: 39,\n                DOWN: 40,\n                END: 35,\n                HOME: 36,\n                SPACEBAR: 32,\n                PAGEUP: 33,\n                PAGEDOWN: 34,\n                F2: 113,\n                F10: 121,\n                F12: 123,\n                NUMPAD_PLUS: 107,\n                NUMPAD_MINUS: 109,\n                NUMPAD_DOT: 110\n            },\n            support: kendo.support || support,\n            animate: kendo.animate || animate,\n            ns: '',\n            attr: function (value) {\n                return 'data-' + kendo.ns + value;\n            },\n            getShadows: getShadows,\n            wrap: wrap,\n            deepExtend: deepExtend,\n            getComputedStyles: getComputedStyles,\n            isScrollable: isScrollable,\n            scrollLeft: scrollLeft,\n            size: size,\n            toCamelCase: toCamelCase,\n            toHyphens: toHyphens,\n            getOffset: kendo.getOffset || getOffset,\n            parseEffects: kendo.parseEffects || parseEffects,\n            toggleClass: kendo.toggleClass || toggleClass,\n            directions: kendo.directions || directions,\n            Observable: Observable,\n            Class: Class,\n            Template: Template,\n            template: proxy(Template.compile, Template),\n            render: proxy(Template.render, Template),\n            stringify: proxy(JSON.stringify, JSON),\n            eventTarget: eventTarget,\n            htmlEncode: htmlEncode,\n            isLocalUrl: function (url) {\n                return url && !localUrlRe.test(url);\n            },\n            expr: function (expression, safe, paramName) {\n                expression = expression || '';\n                if (typeof safe == STRING) {\n                    paramName = safe;\n                    safe = false;\n                }\n                paramName = paramName || 'd';\n                if (expression && expression.charAt(0) !== '[') {\n                    expression = '.' + expression;\n                }\n                if (safe) {\n                    expression = expression.replace(/\"([^.]*)\\.([^\"]*)\"/g, '\"$1_$DOT$_$2\"');\n                    expression = expression.replace(/'([^.]*)\\.([^']*)'/g, '\\'$1_$DOT$_$2\\'');\n                    expression = wrapExpression(expression.split('.'), paramName);\n                    expression = expression.replace(/_\\$DOT\\$_/g, '.');\n                } else {\n                    expression = paramName + expression;\n                }\n                return expression;\n            },\n            getter: function (expression, safe) {\n                var key = expression + safe;\n                return getterCache[key] = getterCache[key] || new Function('d', 'return ' + kendo.expr(expression, safe));\n            },\n            setter: function (expression) {\n                return setterCache[expression] = setterCache[expression] || new Function('d,value', kendo.expr(expression) + '=value');\n            },\n            accessor: function (expression) {\n                return {\n                    get: kendo.getter(expression),\n                    set: kendo.setter(expression)\n                };\n            },\n            guid: function () {\n                var id = '', i, random;\n                for (i = 0; i < 32; i++) {\n                    random = math.random() * 16 | 0;\n                    if (i == 8 || i == 12 || i == 16 || i == 20) {\n                        id += '-';\n                    }\n                    id += (i == 12 ? 4 : i == 16 ? random & 3 | 8 : random).toString(16);\n                }\n                return id;\n            },\n            roleSelector: function (role) {\n                return role.replace(/(\\S+)/g, '[' + kendo.attr('role') + '=$1],').slice(0, -1);\n            },\n            directiveSelector: function (directives) {\n                var selectors = directives.split(' ');\n                if (selectors) {\n                    for (var i = 0; i < selectors.length; i++) {\n                        if (selectors[i] != 'view') {\n                            selectors[i] = selectors[i].replace(/(\\w*)(view|bar|strip|over)$/, '$1-$2');\n                        }\n                    }\n                }\n                return selectors.join(' ').replace(/(\\S+)/g, 'kendo-mobile-$1,').slice(0, -1);\n            },\n            triggeredByInput: function (e) {\n                return /^(label|input|textarea|select)$/i.test(e.target.tagName);\n            },\n            onWidgetRegistered: function (callback) {\n                for (var i = 0, len = kendo.widgets.length; i < len; i++) {\n                    callback(kendo.widgets[i]);\n                }\n                kendo._widgetRegisteredCallbacks.push(callback);\n            },\n            logToConsole: function (message, type) {\n                var console = window.console;\n                if (!kendo.suppressLog && typeof console != 'undefined' && console.log) {\n                    console[type || 'log'](message);\n                }\n            }\n        });\n        var Widget = Observable.extend({\n            init: function (element, options) {\n                var that = this;\n                that.element = kendo.jQuery(element).handler(that);\n                that.angular('init', options);\n                Observable.fn.init.call(that);\n                var dataSource = options ? options.dataSource : null;\n                if (dataSource) {\n                    options = extend({}, options, { dataSource: {} });\n                }\n                options = that.options = extend(true, {}, that.options, options);\n                if (dataSource) {\n                    options.dataSource = dataSource;\n                }\n                if (!that.element.attr(kendo.attr('role'))) {\n                    that.element.attr(kendo.attr('role'), (options.name || '').toLowerCase());\n                }\n                that.element.data('kendo' + options.prefix + options.name, that);\n                that.bind(that.events, options);\n            },\n            events: [],\n            options: { prefix: '' },\n            _hasBindingTarget: function () {\n                return !!this.element[0].kendoBindingTarget;\n            },\n            _tabindex: function (target) {\n                target = target || this.wrapper;\n                var element = this.element, TABINDEX = 'tabindex', tabindex = target.attr(TABINDEX) || element.attr(TABINDEX);\n                element.removeAttr(TABINDEX);\n                target.attr(TABINDEX, !isNaN(tabindex) ? tabindex : 0);\n            },\n            setOptions: function (options) {\n                this._setEvents(options);\n                $.extend(this.options, options);\n            },\n            _setEvents: function (options) {\n                var that = this, idx = 0, length = that.events.length, e;\n                for (; idx < length; idx++) {\n                    e = that.events[idx];\n                    if (that.options[e] && options[e]) {\n                        that.unbind(e, that.options[e]);\n                    }\n                }\n                that.bind(that.events, options);\n            },\n            resize: function (force) {\n                var size = this.getSize(), currentSize = this._size;\n                if (force || (size.width > 0 || size.height > 0) && (!currentSize || size.width !== currentSize.width || size.height !== currentSize.height)) {\n                    this._size = size;\n                    this._resize(size, force);\n                    this.trigger('resize', size);\n                }\n            },\n            getSize: function () {\n                return kendo.dimensions(this.element);\n            },\n            size: function (size) {\n                if (!size) {\n                    return this.getSize();\n                } else {\n                    this.setSize(size);\n                }\n            },\n            setSize: $.noop,\n            _resize: $.noop,\n            destroy: function () {\n                var that = this;\n                that.element.removeData('kendo' + that.options.prefix + that.options.name);\n                that.element.removeData('handler');\n                that.unbind();\n            },\n            _destroy: function () {\n                this.destroy();\n            },\n            angular: function () {\n            },\n            _muteAngularRebind: function (callback) {\n                this._muteRebind = true;\n                callback.call(this);\n                this._muteRebind = false;\n            }\n        });\n        var DataBoundWidget = Widget.extend({\n            dataItems: function () {\n                return this.dataSource.flatView();\n            },\n            _angularItems: function (cmd) {\n                var that = this;\n                that.angular(cmd, function () {\n                    return {\n                        elements: that.items(),\n                        data: $.map(that.dataItems(), function (dataItem) {\n                            return { dataItem: dataItem };\n                        })\n                    };\n                });\n            }\n        });\n        kendo.dimensions = function (element, dimensions) {\n            var domElement = element[0];\n            if (dimensions) {\n                element.css(dimensions);\n            }\n            return {\n                width: domElement.offsetWidth,\n                height: domElement.offsetHeight\n            };\n        };\n        kendo.notify = noop;\n        var templateRegExp = /template$/i, jsonRegExp = /^\\s*(?:\\{(?:.|\\r\\n|\\n)*\\}|\\[(?:.|\\r\\n|\\n)*\\])\\s*$/, jsonFormatRegExp = /^\\{(\\d+)(:[^\\}]+)?\\}|^\\[[A-Za-z_]+\\]$/, dashRegExp = /([A-Z])/g;\n        function parseOption(element, option) {\n            var value;\n            if (option.indexOf('data') === 0) {\n                option = option.substring(4);\n                option = option.charAt(0).toLowerCase() + option.substring(1);\n            }\n            option = option.replace(dashRegExp, '-$1');\n            value = element.getAttribute('data-' + kendo.ns + option);\n            if (value === null) {\n                value = undefined;\n            } else if (value === 'null') {\n                value = null;\n            } else if (value === 'true') {\n                value = true;\n            } else if (value === 'false') {\n                value = false;\n            } else if (numberRegExp.test(value) && option != 'mask') {\n                value = parseFloat(value);\n            } else if (jsonRegExp.test(value) && !jsonFormatRegExp.test(value)) {\n                value = new Function('return (' + value + ')')();\n            }\n            return value;\n        }\n        function parseOptions(element, options, source) {\n            var result = {}, option, value, role = element.getAttribute('data-' + kendo.ns + 'role');\n            for (option in options) {\n                value = parseOption(element, option);\n                if (value !== undefined) {\n                    if (templateRegExp.test(option) && role != 'drawer') {\n                        if (typeof value === 'string') {\n                            if ($('#' + value).length) {\n                                value = kendo.template($('#' + value).html());\n                            } else if (source) {\n                                value = kendo.template(source[value]);\n                            }\n                        } else {\n                            value = element.getAttribute(option);\n                        }\n                    }\n                    result[option] = value;\n                }\n            }\n            return result;\n        }\n        kendo.initWidget = function (element, options, roles) {\n            var result, option, widget, idx, length, role, value, dataSource, fullPath, widgetKeyRegExp;\n            if (!roles) {\n                roles = kendo.ui.roles;\n            } else if (roles.roles) {\n                roles = roles.roles;\n            }\n            element = element.nodeType ? element : element[0];\n            role = element.getAttribute('data-' + kendo.ns + 'role');\n            if (!role) {\n                return;\n            }\n            fullPath = role.indexOf('.') === -1;\n            if (fullPath) {\n                widget = roles[role];\n            } else {\n                widget = kendo.getter(role)(window);\n            }\n            var data = $(element).data(), widgetKey = widget ? 'kendo' + widget.fn.options.prefix + widget.fn.options.name : '';\n            if (fullPath) {\n                widgetKeyRegExp = new RegExp('^kendo.*' + role + '$', 'i');\n            } else {\n                widgetKeyRegExp = new RegExp('^' + widgetKey + '$', 'i');\n            }\n            for (var key in data) {\n                if (key.match(widgetKeyRegExp)) {\n                    if (key === widgetKey) {\n                        result = data[key];\n                    } else {\n                        return data[key];\n                    }\n                }\n            }\n            if (!widget) {\n                return;\n            }\n            dataSource = parseOption(element, 'dataSource');\n            options = $.extend({}, parseOptions(element, widget.fn.options), options);\n            if (dataSource) {\n                if (typeof dataSource === STRING) {\n                    options.dataSource = kendo.getter(dataSource)(window);\n                } else {\n                    options.dataSource = dataSource;\n                }\n            }\n            for (idx = 0, length = widget.fn.events.length; idx < length; idx++) {\n                option = widget.fn.events[idx];\n                value = parseOption(element, option);\n                if (value !== undefined) {\n                    options[option] = kendo.getter(value)(window);\n                }\n            }\n            if (!result) {\n                result = new widget(element, options);\n            } else if (!$.isEmptyObject(options)) {\n                result.setOptions(options);\n            }\n            return result;\n        };\n        kendo.rolesFromNamespaces = function (namespaces) {\n            var roles = [], idx, length;\n            if (!namespaces[0]) {\n                namespaces = [\n                    kendo.ui,\n                    kendo.dataviz.ui\n                ];\n            }\n            for (idx = 0, length = namespaces.length; idx < length; idx++) {\n                roles[idx] = namespaces[idx].roles;\n            }\n            return extend.apply(null, [{}].concat(roles.reverse()));\n        };\n        kendo.init = function (element) {\n            var roles = kendo.rolesFromNamespaces(slice.call(arguments, 1));\n            $(element).find('[data-' + kendo.ns + 'role]').addBack().each(function () {\n                kendo.initWidget(this, {}, roles);\n            });\n        };\n        kendo.destroy = function (element) {\n            $(element).find('[data-' + kendo.ns + 'role]').addBack().each(function () {\n                var data = $(this).data();\n                for (var key in data) {\n                    if (key.indexOf('kendo') === 0 && typeof data[key].destroy === FUNCTION) {\n                        data[key].destroy();\n                    }\n                }\n            });\n        };\n        function containmentComparer(a, b) {\n            return $.contains(a, b) ? -1 : 1;\n        }\n        function resizableWidget() {\n            var widget = $(this);\n            return $.inArray(widget.attr('data-' + kendo.ns + 'role'), [\n                'slider',\n                'rangeslider'\n            ]) > -1 || widget.is(':visible');\n        }\n        kendo.resize = function (element, force) {\n            var widgets = $(element).find('[data-' + kendo.ns + 'role]').addBack().filter(resizableWidget);\n            if (!widgets.length) {\n                return;\n            }\n            var widgetsArray = $.makeArray(widgets);\n            widgetsArray.sort(containmentComparer);\n            $.each(widgetsArray, function () {\n                var widget = kendo.widgetInstance($(this));\n                if (widget) {\n                    widget.resize(force);\n                }\n            });\n        };\n        kendo.parseOptions = parseOptions;\n        extend(kendo.ui, {\n            Widget: Widget,\n            DataBoundWidget: DataBoundWidget,\n            roles: {},\n            progress: function (container, toggle, options) {\n                var mask = container.find('.k-loading-mask'), support = kendo.support, browser = support.browser, isRtl, leftRight, webkitCorrection, containerScrollLeft, cssClass;\n                options = $.extend({}, {\n                    width: '100%',\n                    height: '100%',\n                    top: container.scrollTop(),\n                    opacity: false\n                }, options);\n                cssClass = options.opacity ? 'k-loading-mask k-opaque' : 'k-loading-mask';\n                if (toggle) {\n                    if (!mask.length) {\n                        isRtl = support.isRtl(container);\n                        leftRight = isRtl ? 'right' : 'left';\n                        containerScrollLeft = container.scrollLeft();\n                        webkitCorrection = browser.webkit ? !isRtl ? 0 : container[0].scrollWidth - container.width() - 2 * containerScrollLeft : 0;\n                        mask = $(kendo.format('<div class=\\'{0}\\'><span class=\\'k-loading-text\\'>{1}</span><div class=\\'k-loading-image\\'/><div class=\\'k-loading-color\\'/></div>', cssClass, kendo.ui.progress.messages.loading)).width(options.width).height(options.height).css('top', options.top).css(leftRight, Math.abs(containerScrollLeft) + webkitCorrection).prependTo(container);\n                    }\n                } else if (mask) {\n                    mask.remove();\n                }\n            },\n            plugin: function (widget, register, prefix) {\n                var name = widget.fn.options.name, getter;\n                register = register || kendo.ui;\n                prefix = prefix || '';\n                register[name] = widget;\n                register.roles[name.toLowerCase()] = widget;\n                getter = 'getKendo' + prefix + name;\n                name = 'kendo' + prefix + name;\n                var widgetEntry = {\n                    name: name,\n                    widget: widget,\n                    prefix: prefix || ''\n                };\n                kendo.widgets.push(widgetEntry);\n                for (var i = 0, len = kendo._widgetRegisteredCallbacks.length; i < len; i++) {\n                    kendo._widgetRegisteredCallbacks[i](widgetEntry);\n                }\n                $.fn[name] = function (options) {\n                    var value = this, args;\n                    if (typeof options === STRING) {\n                        args = slice.call(arguments, 1);\n                        this.each(function () {\n                            var widget = $.data(this, name), method, result;\n                            if (!widget) {\n                                throw new Error(kendo.format('Cannot call method \\'{0}\\' of {1} before it is initialized', options, name));\n                            }\n                            method = widget[options];\n                            if (typeof method !== FUNCTION) {\n                                throw new Error(kendo.format('Cannot find method \\'{0}\\' of {1}', options, name));\n                            }\n                            result = method.apply(widget, args);\n                            if (result !== undefined) {\n                                value = result;\n                                return false;\n                            }\n                        });\n                    } else {\n                        this.each(function () {\n                            return new widget(this, options);\n                        });\n                    }\n                    return value;\n                };\n                $.fn[name].widget = widget;\n                $.fn[getter] = function () {\n                    return this.data(name);\n                };\n            }\n        });\n        kendo.ui.progress.messages = { loading: 'Loading...' };\n        var ContainerNullObject = {\n            bind: function () {\n                return this;\n            },\n            nullObject: true,\n            options: {}\n        };\n        var MobileWidget = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this.element.autoApplyNS();\n                this.wrapper = this.element;\n                this.element.addClass('km-widget');\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.element.kendoDestroy();\n            },\n            options: { prefix: 'Mobile' },\n            events: [],\n            view: function () {\n                var viewElement = this.element.closest(kendo.roleSelector('view splitview modalview drawer'));\n                return kendo.widgetInstance(viewElement, kendo.mobile.ui) || ContainerNullObject;\n            },\n            viewHasNativeScrolling: function () {\n                var view = this.view();\n                return view && view.options.useNativeScrolling;\n            },\n            container: function () {\n                var element = this.element.closest(kendo.roleSelector('view layout modalview drawer splitview'));\n                return kendo.widgetInstance(element.eq(0), kendo.mobile.ui) || ContainerNullObject;\n            }\n        });\n        extend(kendo.mobile, {\n            init: function (element) {\n                kendo.init(element, kendo.mobile.ui, kendo.ui, kendo.dataviz.ui);\n            },\n            appLevelNativeScrolling: function () {\n                return kendo.mobile.application && kendo.mobile.application.options && kendo.mobile.application.options.useNativeScrolling;\n            },\n            roles: {},\n            ui: {\n                Widget: MobileWidget,\n                DataBoundWidget: DataBoundWidget.extend(MobileWidget.prototype),\n                roles: {},\n                plugin: function (widget) {\n                    kendo.ui.plugin(widget, kendo.mobile.ui, 'Mobile');\n                }\n            }\n        });\n        deepExtend(kendo.dataviz, {\n            init: function (element) {\n                kendo.init(element, kendo.dataviz.ui);\n            },\n            ui: {\n                roles: {},\n                themes: {},\n                views: [],\n                plugin: function (widget) {\n                    kendo.ui.plugin(widget, kendo.dataviz.ui);\n                }\n            },\n            roles: {}\n        });\n        kendo.touchScroller = function (elements, options) {\n            if (!options) {\n                options = {};\n            }\n            options.useNative = true;\n            return $(elements).map(function (idx, element) {\n                element = $(element);\n                if (support.kineticScrollNeeded && kendo.mobile.ui.Scroller && !element.data('kendoMobileScroller')) {\n                    element.kendoMobileScroller(options);\n                    return element.data('kendoMobileScroller');\n                } else {\n                    return false;\n                }\n            })[0];\n        };\n        kendo.preventDefault = function (e) {\n            e.preventDefault();\n        };\n        kendo.widgetInstance = function (element, suites) {\n            var role = element.data(kendo.ns + 'role'), widgets = [], i, length;\n            if (role) {\n                if (role === 'content') {\n                    role = 'scroller';\n                }\n                if (role === 'editortoolbar') {\n                    var editorToolbar = element.data('kendoEditorToolbar');\n                    if (editorToolbar) {\n                        return editorToolbar;\n                    }\n                }\n                if (role === 'view') {\n                    return element.data('kendoView');\n                }\n                if (suites) {\n                    if (suites[0]) {\n                        for (i = 0, length = suites.length; i < length; i++) {\n                            widgets.push(suites[i].roles[role]);\n                        }\n                    } else {\n                        widgets.push(suites.roles[role]);\n                    }\n                } else {\n                    widgets = [\n                        kendo.ui.roles[role],\n                        kendo.dataviz.ui.roles[role],\n                        kendo.mobile.ui.roles[role]\n                    ];\n                }\n                if (role.indexOf('.') >= 0) {\n                    widgets = [kendo.getter(role)(window)];\n                }\n                for (i = 0, length = widgets.length; i < length; i++) {\n                    var widget = widgets[i];\n                    if (widget) {\n                        var instance = element.data('kendo' + widget.fn.options.prefix + widget.fn.options.name);\n                        if (instance) {\n                            return instance;\n                        }\n                    }\n                }\n            }\n        };\n        kendo.onResize = function (callback) {\n            var handler = callback;\n            if (support.mobileOS.android) {\n                handler = function () {\n                    setTimeout(callback, 600);\n                };\n            }\n            $(window).on(support.resize, handler);\n            return handler;\n        };\n        kendo.unbindResize = function (callback) {\n            $(window).off(support.resize, callback);\n        };\n        kendo.attrValue = function (element, key) {\n            return element.data(kendo.ns + key);\n        };\n        kendo.days = {\n            Sunday: 0,\n            Monday: 1,\n            Tuesday: 2,\n            Wednesday: 3,\n            Thursday: 4,\n            Friday: 5,\n            Saturday: 6\n        };\n        function focusable(element, isTabIndexNotNaN) {\n            var nodeName = element.nodeName.toLowerCase();\n            return (/input|select|textarea|button|object/.test(nodeName) ? !element.disabled : 'a' === nodeName ? element.href || isTabIndexNotNaN : isTabIndexNotNaN) && visible(element);\n        }\n        function visible(element) {\n            return $.expr.pseudos.visible(element) && !$(element).parents().addBack().filter(function () {\n                return $.css(this, 'visibility') === 'hidden';\n            }).length;\n        }\n        $.extend($.expr.pseudos, {\n            kendoFocusable: function (element) {\n                var idx = $.attr(element, 'tabindex');\n                return focusable(element, !isNaN(idx) && idx > -1);\n            }\n        });\n        var MOUSE_EVENTS = [\n            'mousedown',\n            'mousemove',\n            'mouseenter',\n            'mouseleave',\n            'mouseover',\n            'mouseout',\n            'mouseup',\n            'click'\n        ];\n        var EXCLUDE_BUST_CLICK_SELECTOR = 'label, input, [data-rel=external]';\n        var MouseEventNormalizer = {\n            setupMouseMute: function () {\n                var idx = 0, length = MOUSE_EVENTS.length, element = document.documentElement;\n                if (MouseEventNormalizer.mouseTrap || !support.eventCapture) {\n                    return;\n                }\n                MouseEventNormalizer.mouseTrap = true;\n                MouseEventNormalizer.bustClick = false;\n                MouseEventNormalizer.captureMouse = false;\n                var handler = function (e) {\n                    if (MouseEventNormalizer.captureMouse) {\n                        if (e.type === 'click') {\n                            if (MouseEventNormalizer.bustClick && !$(e.target).is(EXCLUDE_BUST_CLICK_SELECTOR)) {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        } else {\n                            e.stopPropagation();\n                        }\n                    }\n                };\n                for (; idx < length; idx++) {\n                    element.addEventListener(MOUSE_EVENTS[idx], handler, true);\n                }\n            },\n            muteMouse: function (e) {\n                MouseEventNormalizer.captureMouse = true;\n                if (e.data.bustClick) {\n                    MouseEventNormalizer.bustClick = true;\n                }\n                clearTimeout(MouseEventNormalizer.mouseTrapTimeoutID);\n            },\n            unMuteMouse: function () {\n                clearTimeout(MouseEventNormalizer.mouseTrapTimeoutID);\n                MouseEventNormalizer.mouseTrapTimeoutID = setTimeout(function () {\n                    MouseEventNormalizer.captureMouse = false;\n                    MouseEventNormalizer.bustClick = false;\n                }, 400);\n            }\n        };\n        var eventMap = {\n            down: 'touchstart mousedown',\n            move: 'mousemove touchmove',\n            up: 'mouseup touchend touchcancel',\n            cancel: 'mouseleave touchcancel'\n        };\n        if (support.touch && (support.mobileOS.ios || support.mobileOS.android)) {\n            eventMap = {\n                down: 'touchstart',\n                move: 'touchmove',\n                up: 'touchend touchcancel',\n                cancel: 'touchcancel'\n            };\n        } else if (support.pointers) {\n            eventMap = {\n                down: 'pointerdown',\n                move: 'pointermove',\n                up: 'pointerup',\n                cancel: 'pointercancel pointerleave'\n            };\n        } else if (support.msPointers) {\n            eventMap = {\n                down: 'MSPointerDown',\n                move: 'MSPointerMove',\n                up: 'MSPointerUp',\n                cancel: 'MSPointerCancel MSPointerLeave'\n            };\n        }\n        if (support.msPointers && !('onmspointerenter' in window)) {\n            $.each({\n                MSPointerEnter: 'MSPointerOver',\n                MSPointerLeave: 'MSPointerOut'\n            }, function (orig, fix) {\n                $.event.special[orig] = {\n                    delegateType: fix,\n                    bindType: fix,\n                    handle: function (event) {\n                        var ret, target = this, related = event.relatedTarget, handleObj = event.handleObj;\n                        if (!related || related !== target && !$.contains(target, related)) {\n                            event.type = handleObj.origType;\n                            ret = handleObj.handler.apply(this, arguments);\n                            event.type = fix;\n                        }\n                        return ret;\n                    }\n                };\n            });\n        }\n        var getEventMap = function (e) {\n                return eventMap[e] || e;\n            }, eventRegEx = /([^ ]+)/g;\n        kendo.applyEventMap = function (events, ns) {\n            events = events.replace(eventRegEx, getEventMap);\n            if (ns) {\n                events = events.replace(eventRegEx, '$1.' + ns);\n            }\n            return events;\n        };\n        var on = $.fn.on;\n        function kendoJQuery(selector, context) {\n            return new kendoJQuery.fn.init(selector, context);\n        }\n        noDepricateExtend(true, kendoJQuery, $);\n        kendoJQuery.fn = kendoJQuery.prototype = new $();\n        kendoJQuery.fn.constructor = kendoJQuery;\n        kendoJQuery.fn.init = function (selector, context) {\n            if (context && context instanceof $ && !(context instanceof kendoJQuery)) {\n                context = kendoJQuery(context);\n            }\n            return $.fn.init.call(this, selector, context, rootjQuery);\n        };\n        kendoJQuery.fn.init.prototype = kendoJQuery.fn;\n        var rootjQuery = kendoJQuery(document);\n        extend(kendoJQuery.fn, {\n            handler: function (handler) {\n                this.data('handler', handler);\n                return this;\n            },\n            autoApplyNS: function (ns) {\n                this.data('kendoNS', ns || kendo.guid());\n                return this;\n            },\n            on: function () {\n                var that = this, ns = that.data('kendoNS');\n                if (arguments.length === 1) {\n                    return on.call(that, arguments[0]);\n                }\n                var context = that, args = slice.call(arguments);\n                if (typeof args[args.length - 1] === UNDEFINED) {\n                    args.pop();\n                }\n                var callback = args[args.length - 1], events = kendo.applyEventMap(args[0], ns);\n                if (support.mouseAndTouchPresent && events.search(/mouse|click/) > -1 && this[0] !== document.documentElement) {\n                    MouseEventNormalizer.setupMouseMute();\n                    var selector = args.length === 2 ? null : args[1], bustClick = events.indexOf('click') > -1 && events.indexOf('touchend') > -1;\n                    on.call(this, {\n                        touchstart: MouseEventNormalizer.muteMouse,\n                        touchend: MouseEventNormalizer.unMuteMouse\n                    }, selector, { bustClick: bustClick });\n                }\n                if (typeof callback === STRING) {\n                    context = that.data('handler');\n                    callback = context[callback];\n                    args[args.length - 1] = function (e) {\n                        callback.call(context, e);\n                    };\n                }\n                args[0] = events;\n                on.apply(that, args);\n                return that;\n            },\n            kendoDestroy: function (ns) {\n                ns = ns || this.data('kendoNS');\n                if (ns) {\n                    this.off('.' + ns);\n                }\n                return this;\n            }\n        });\n        kendo.jQuery = kendoJQuery;\n        kendo.eventMap = eventMap;\n        kendo.timezone = function () {\n            var months = {\n                Jan: 0,\n                Feb: 1,\n                Mar: 2,\n                Apr: 3,\n                May: 4,\n                Jun: 5,\n                Jul: 6,\n                Aug: 7,\n                Sep: 8,\n                Oct: 9,\n                Nov: 10,\n                Dec: 11\n            };\n            var days = {\n                Sun: 0,\n                Mon: 1,\n                Tue: 2,\n                Wed: 3,\n                Thu: 4,\n                Fri: 5,\n                Sat: 6\n            };\n            function ruleToDate(year, rule) {\n                var date;\n                var targetDay;\n                var ourDay;\n                var month = rule[3];\n                var on = rule[4];\n                var time = rule[5];\n                var cache = rule[8];\n                if (!cache) {\n                    rule[8] = cache = {};\n                }\n                if (cache[year]) {\n                    return cache[year];\n                }\n                if (!isNaN(on)) {\n                    date = new Date(Date.UTC(year, months[month], on, time[0], time[1], time[2], 0));\n                } else if (on.indexOf('last') === 0) {\n                    date = new Date(Date.UTC(year, months[month] + 1, 1, time[0] - 24, time[1], time[2], 0));\n                    targetDay = days[on.substr(4, 3)];\n                    ourDay = date.getUTCDay();\n                    date.setUTCDate(date.getUTCDate() + targetDay - ourDay - (targetDay > ourDay ? 7 : 0));\n                } else if (on.indexOf('>=') >= 0) {\n                    date = new Date(Date.UTC(year, months[month], on.substr(5), time[0], time[1], time[2], 0));\n                    targetDay = days[on.substr(0, 3)];\n                    ourDay = date.getUTCDay();\n                    date.setUTCDate(date.getUTCDate() + targetDay - ourDay + (targetDay < ourDay ? 7 : 0));\n                }\n                return cache[year] = date;\n            }\n            function findRule(utcTime, rules, zone) {\n                rules = rules[zone];\n                if (!rules) {\n                    var time = zone.split(':');\n                    var offset = 0;\n                    if (time.length > 1) {\n                        offset = time[0] * 60 + Number(time[1]);\n                    }\n                    return [\n                        -1000000,\n                        'max',\n                        '-',\n                        'Jan',\n                        1,\n                        [\n                            0,\n                            0,\n                            0\n                        ],\n                        offset,\n                        '-'\n                    ];\n                }\n                var year = new Date(utcTime).getUTCFullYear();\n                rules = jQuery.grep(rules, function (rule) {\n                    var from = rule[0];\n                    var to = rule[1];\n                    return from <= year && (to >= year || from == year && to == 'only' || to == 'max');\n                });\n                rules.push(utcTime);\n                rules.sort(function (a, b) {\n                    if (typeof a != 'number') {\n                        a = Number(ruleToDate(year, a));\n                    }\n                    if (typeof b != 'number') {\n                        b = Number(ruleToDate(year, b));\n                    }\n                    return a - b;\n                });\n                var rule = rules[jQuery.inArray(utcTime, rules) - 1] || rules[rules.length - 1];\n                return isNaN(rule) ? rule : null;\n            }\n            function findZone(utcTime, zones, timezone) {\n                var zoneRules = zones[timezone];\n                if (typeof zoneRules === 'string') {\n                    zoneRules = zones[zoneRules];\n                }\n                if (!zoneRules) {\n                    throw new Error('Timezone \"' + timezone + '\" is either incorrect, or kendo.timezones.min.js is not included.');\n                }\n                for (var idx = zoneRules.length - 1; idx >= 0; idx--) {\n                    var until = zoneRules[idx][3];\n                    if (until && utcTime > until) {\n                        break;\n                    }\n                }\n                var zone = zoneRules[idx + 1];\n                if (!zone) {\n                    throw new Error('Timezone \"' + timezone + '\" not found on ' + utcTime + '.');\n                }\n                return zone;\n            }\n            function zoneAndRule(utcTime, zones, rules, timezone) {\n                if (typeof utcTime != NUMBER) {\n                    utcTime = Date.UTC(utcTime.getFullYear(), utcTime.getMonth(), utcTime.getDate(), utcTime.getHours(), utcTime.getMinutes(), utcTime.getSeconds(), utcTime.getMilliseconds());\n                }\n                var zone = findZone(utcTime, zones, timezone);\n                return {\n                    zone: zone,\n                    rule: findRule(utcTime, rules, zone[1])\n                };\n            }\n            function offset(utcTime, timezone) {\n                if (timezone == 'Etc/UTC' || timezone == 'Etc/GMT') {\n                    return 0;\n                }\n                var info = zoneAndRule(utcTime, this.zones, this.rules, timezone);\n                var zone = info.zone;\n                var rule = info.rule;\n                return kendo.parseFloat(rule ? zone[0] - rule[6] : zone[0]);\n            }\n            function abbr(utcTime, timezone) {\n                var info = zoneAndRule(utcTime, this.zones, this.rules, timezone);\n                var zone = info.zone;\n                var rule = info.rule;\n                var base = zone[2];\n                if (base.indexOf('/') >= 0) {\n                    return base.split('/')[rule && +rule[6] ? 1 : 0];\n                } else if (base.indexOf('%s') >= 0) {\n                    return base.replace('%s', !rule || rule[7] == '-' ? '' : rule[7]);\n                }\n                return base;\n            }\n            function convert(date, fromOffset, toOffset) {\n                var tempToOffset = toOffset;\n                var diff;\n                if (typeof fromOffset == STRING) {\n                    fromOffset = this.offset(date, fromOffset);\n                }\n                if (typeof toOffset == STRING) {\n                    toOffset = this.offset(date, toOffset);\n                }\n                var fromLocalOffset = date.getTimezoneOffset();\n                date = new Date(date.getTime() + (fromOffset - toOffset) * 60000);\n                var toLocalOffset = date.getTimezoneOffset();\n                if (typeof tempToOffset == STRING) {\n                    tempToOffset = this.offset(date, tempToOffset);\n                }\n                diff = toLocalOffset - fromLocalOffset + (toOffset - tempToOffset);\n                return new Date(date.getTime() + diff * 60000);\n            }\n            function apply(date, timezone) {\n                return this.convert(date, date.getTimezoneOffset(), timezone);\n            }\n            function remove(date, timezone) {\n                return this.convert(date, timezone, date.getTimezoneOffset());\n            }\n            function toLocalDate(time) {\n                return this.apply(new Date(time), 'Etc/UTC');\n            }\n            return {\n                zones: {},\n                rules: {},\n                offset: offset,\n                convert: convert,\n                apply: apply,\n                remove: remove,\n                abbr: abbr,\n                toLocalDate: toLocalDate\n            };\n        }();\n        kendo.date = function () {\n            var MS_PER_MINUTE = 60000, MS_PER_DAY = 86400000;\n            function adjustDST(date, hours) {\n                if (hours === 0 && date.getHours() === 23) {\n                    date.setHours(date.getHours() + 2);\n                    return true;\n                }\n                return false;\n            }\n            function setDayOfWeek(date, day, dir) {\n                var hours = date.getHours();\n                dir = dir || 1;\n                day = (day - date.getDay() + 7 * dir) % 7;\n                date.setDate(date.getDate() + day);\n                adjustDST(date, hours);\n            }\n            function dayOfWeek(date, day, dir) {\n                date = new Date(date);\n                setDayOfWeek(date, day, dir);\n                return date;\n            }\n            function firstDayOfMonth(date) {\n                return new Date(date.getFullYear(), date.getMonth(), 1);\n            }\n            function lastDayOfMonth(date) {\n                var last = new Date(date.getFullYear(), date.getMonth() + 1, 0), first = firstDayOfMonth(date), timeOffset = Math.abs(last.getTimezoneOffset() - first.getTimezoneOffset());\n                if (timeOffset) {\n                    last.setHours(first.getHours() + timeOffset / 60);\n                }\n                return last;\n            }\n            function moveDateToWeekStart(date, weekStartDay) {\n                if (weekStartDay !== 1) {\n                    return addDays(dayOfWeek(date, weekStartDay, -1), 4);\n                }\n                return addDays(date, 4 - (date.getDay() || 7));\n            }\n            function calcWeekInYear(date, weekStartDay) {\n                var firstWeekInYear = new Date(date.getFullYear(), 0, 1, -6);\n                var newDate = moveDateToWeekStart(date, weekStartDay);\n                var diffInMS = newDate.getTime() - firstWeekInYear.getTime();\n                var days = Math.floor(diffInMS / MS_PER_DAY);\n                return 1 + Math.floor(days / 7);\n            }\n            function weekInYear(date, weekStartDay) {\n                if (weekStartDay === undefined) {\n                    weekStartDay = kendo.culture().calendar.firstDay;\n                }\n                var prevWeekDate = addDays(date, -7);\n                var nextWeekDate = addDays(date, 7);\n                var weekNumber = calcWeekInYear(date, weekStartDay);\n                if (weekNumber === 0) {\n                    return calcWeekInYear(prevWeekDate, weekStartDay) + 1;\n                }\n                if (weekNumber === 53 && calcWeekInYear(nextWeekDate, weekStartDay) > 1) {\n                    return 1;\n                }\n                return weekNumber;\n            }\n            function getDate(date) {\n                date = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);\n                adjustDST(date, 0);\n                return date;\n            }\n            function toUtcTime(date) {\n                return Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n            }\n            function getMilliseconds(date) {\n                return toInvariantTime(date).getTime() - getDate(toInvariantTime(date));\n            }\n            function isInTimeRange(value, min, max) {\n                var msMin = getMilliseconds(min), msMax = getMilliseconds(max), msValue;\n                if (!value || msMin == msMax) {\n                    return true;\n                }\n                if (min >= max) {\n                    max += MS_PER_DAY;\n                }\n                msValue = getMilliseconds(value);\n                if (msMin > msValue) {\n                    msValue += MS_PER_DAY;\n                }\n                if (msMax < msMin) {\n                    msMax += MS_PER_DAY;\n                }\n                return msValue >= msMin && msValue <= msMax;\n            }\n            function isInDateRange(value, min, max) {\n                var msMin = min.getTime(), msMax = max.getTime(), msValue;\n                if (msMin >= msMax) {\n                    msMax += MS_PER_DAY;\n                }\n                msValue = value.getTime();\n                return msValue >= msMin && msValue <= msMax;\n            }\n            function addDays(date, offset) {\n                var hours = date.getHours();\n                date = new Date(date);\n                setTime(date, offset * MS_PER_DAY);\n                adjustDST(date, hours);\n                return date;\n            }\n            function setTime(date, milliseconds, ignoreDST) {\n                var offset = date.getTimezoneOffset();\n                var difference;\n                date.setTime(date.getTime() + milliseconds);\n                if (!ignoreDST) {\n                    difference = date.getTimezoneOffset() - offset;\n                    date.setTime(date.getTime() + difference * MS_PER_MINUTE);\n                }\n            }\n            function setHours(date, time) {\n                date = new Date(kendo.date.getDate(date).getTime() + kendo.date.getMilliseconds(time));\n                adjustDST(date, time.getHours());\n                return date;\n            }\n            function today() {\n                return getDate(new Date());\n            }\n            function isToday(date) {\n                return getDate(date).getTime() == today().getTime();\n            }\n            function toInvariantTime(date) {\n                var staticDate = new Date(1980, 1, 1, 0, 0, 0);\n                if (date) {\n                    staticDate.setHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n                }\n                return staticDate;\n            }\n            return {\n                adjustDST: adjustDST,\n                dayOfWeek: dayOfWeek,\n                setDayOfWeek: setDayOfWeek,\n                getDate: getDate,\n                isInDateRange: isInDateRange,\n                isInTimeRange: isInTimeRange,\n                isToday: isToday,\n                nextDay: function (date) {\n                    return addDays(date, 1);\n                },\n                previousDay: function (date) {\n                    return addDays(date, -1);\n                },\n                toUtcTime: toUtcTime,\n                MS_PER_DAY: MS_PER_DAY,\n                MS_PER_HOUR: 60 * MS_PER_MINUTE,\n                MS_PER_MINUTE: MS_PER_MINUTE,\n                setTime: setTime,\n                setHours: setHours,\n                addDays: addDays,\n                today: today,\n                toInvariantTime: toInvariantTime,\n                firstDayOfMonth: firstDayOfMonth,\n                lastDayOfMonth: lastDayOfMonth,\n                weekInYear: weekInYear,\n                getMilliseconds: getMilliseconds\n            };\n        }();\n        kendo.stripWhitespace = function (element) {\n            if (document.createNodeIterator) {\n                var iterator = document.createNodeIterator(element, NodeFilter.SHOW_TEXT, function (node) {\n                    return node.parentNode == element ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;\n                }, false);\n                while (iterator.nextNode()) {\n                    if (iterator.referenceNode && !iterator.referenceNode.textContent.trim()) {\n                        iterator.referenceNode.parentNode.removeChild(iterator.referenceNode);\n                    }\n                }\n            } else {\n                for (var i = 0; i < element.childNodes.length; i++) {\n                    var child = element.childNodes[i];\n                    if (child.nodeType == 3 && !/\\S/.test(child.nodeValue)) {\n                        element.removeChild(child);\n                        i--;\n                    }\n                    if (child.nodeType == 1) {\n                        kendo.stripWhitespace(child);\n                    }\n                }\n            }\n        };\n        var animationFrame = window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.oRequestAnimationFrame || window.msRequestAnimationFrame || function (callback) {\n            setTimeout(callback, 1000 / 60);\n        };\n        kendo.animationFrame = function (callback) {\n            animationFrame.call(window, callback);\n        };\n        var animationQueue = [];\n        kendo.queueAnimation = function (callback) {\n            animationQueue[animationQueue.length] = callback;\n            if (animationQueue.length === 1) {\n                kendo.runNextAnimation();\n            }\n        };\n        kendo.runNextAnimation = function () {\n            kendo.animationFrame(function () {\n                if (animationQueue[0]) {\n                    animationQueue.shift()();\n                    if (animationQueue[0]) {\n                        kendo.runNextAnimation();\n                    }\n                }\n            });\n        };\n        kendo.parseQueryStringParams = function (url) {\n            var queryString = url.split('?')[1] || '', params = {}, paramParts = queryString.split(/&|=/), length = paramParts.length, idx = 0;\n            for (; idx < length; idx += 2) {\n                if (paramParts[idx] !== '') {\n                    params[decodeURIComponent(paramParts[idx])] = decodeURIComponent(paramParts[idx + 1]);\n                }\n            }\n            return params;\n        };\n        kendo.elementUnderCursor = function (e) {\n            if (typeof e.x.client != 'undefined') {\n                return document.elementFromPoint(e.x.client, e.y.client);\n            }\n        };\n        kendo.wheelDeltaY = function (jQueryEvent) {\n            var e = jQueryEvent.originalEvent, deltaY = e.wheelDeltaY, delta;\n            if (e.wheelDelta) {\n                if (deltaY === undefined || deltaY) {\n                    delta = e.wheelDelta;\n                }\n            } else if (e.detail && e.axis === e.VERTICAL_AXIS) {\n                delta = -e.detail * 10;\n            }\n            return delta;\n        };\n        kendo.throttle = function (fn, delay) {\n            var timeout;\n            var lastExecTime = 0;\n            if (!delay || delay <= 0) {\n                return fn;\n            }\n            var throttled = function () {\n                var that = this;\n                var elapsed = +new Date() - lastExecTime;\n                var args = arguments;\n                function exec() {\n                    fn.apply(that, args);\n                    lastExecTime = +new Date();\n                }\n                if (!lastExecTime) {\n                    return exec();\n                }\n                if (timeout) {\n                    clearTimeout(timeout);\n                }\n                if (elapsed > delay) {\n                    exec();\n                } else {\n                    timeout = setTimeout(exec, delay - elapsed);\n                }\n            };\n            throttled.cancel = function () {\n                clearTimeout(timeout);\n            };\n            return throttled;\n        };\n        kendo.caret = function (element, start, end) {\n            var rangeElement;\n            var isPosition = start !== undefined;\n            if (end === undefined) {\n                end = start;\n            }\n            if (element[0]) {\n                element = element[0];\n            }\n            if (isPosition && element.disabled) {\n                return;\n            }\n            try {\n                if (element.selectionStart !== undefined) {\n                    if (isPosition) {\n                        element.focus();\n                        var mobile = support.mobileOS;\n                        if (mobile.wp || mobile.android) {\n                            setTimeout(function () {\n                                element.setSelectionRange(start, end);\n                            }, 0);\n                        } else {\n                            element.setSelectionRange(start, end);\n                        }\n                    } else {\n                        start = [\n                            element.selectionStart,\n                            element.selectionEnd\n                        ];\n                    }\n                } else if (document.selection) {\n                    if ($(element).is(':visible')) {\n                        element.focus();\n                    }\n                    rangeElement = element.createTextRange();\n                    if (isPosition) {\n                        rangeElement.collapse(true);\n                        rangeElement.moveStart('character', start);\n                        rangeElement.moveEnd('character', end - start);\n                        rangeElement.select();\n                    } else {\n                        var rangeDuplicated = rangeElement.duplicate(), selectionStart, selectionEnd;\n                        rangeElement.moveToBookmark(document.selection.createRange().getBookmark());\n                        rangeDuplicated.setEndPoint('EndToStart', rangeElement);\n                        selectionStart = rangeDuplicated.text.length;\n                        selectionEnd = selectionStart + rangeElement.text.length;\n                        start = [\n                            selectionStart,\n                            selectionEnd\n                        ];\n                    }\n                }\n            } catch (e) {\n                start = [];\n            }\n            return start;\n        };\n        kendo.compileMobileDirective = function (element, scope) {\n            var angular = window.angular;\n            element.attr('data-' + kendo.ns + 'role', element[0].tagName.toLowerCase().replace('kendo-mobile-', '').replace('-', ''));\n            angular.element(element).injector().invoke([\n                '$compile',\n                function ($compile) {\n                    $compile(element)(scope);\n                    if (!/^\\$(digest|apply)$/.test(scope.$$phase)) {\n                        scope.$digest();\n                    }\n                }\n            ]);\n            return kendo.widgetInstance(element, kendo.mobile.ui);\n        };\n        kendo.antiForgeryTokens = function () {\n            var tokens = {}, csrf_token = $('meta[name=csrf-token],meta[name=_csrf]').attr('content'), csrf_param = $('meta[name=csrf-param],meta[name=_csrf_header]').attr('content');\n            $('input[name^=\\'__RequestVerificationToken\\']').each(function () {\n                tokens[this.name] = this.value;\n            });\n            if (csrf_param !== undefined && csrf_token !== undefined) {\n                tokens[csrf_param] = csrf_token;\n            }\n            return tokens;\n        };\n        kendo.cycleForm = function (form) {\n            var firstElement = form.find('input, .k-widget').first();\n            var lastElement = form.find('button, .k-button').last();\n            function focus(el) {\n                var widget = kendo.widgetInstance(el);\n                if (widget && widget.focus) {\n                    widget.focus();\n                } else {\n                    el.focus();\n                }\n            }\n            lastElement.on('keydown', function (e) {\n                if (e.keyCode == kendo.keys.TAB && !e.shiftKey) {\n                    e.preventDefault();\n                    focus(firstElement);\n                }\n            });\n            firstElement.on('keydown', function (e) {\n                if (e.keyCode == kendo.keys.TAB && e.shiftKey) {\n                    e.preventDefault();\n                    focus(lastElement);\n                }\n            });\n        };\n        kendo.focusElement = function (element) {\n            var scrollTopPositions = [];\n            var scrollableParents = element.parentsUntil('body').filter(function (index, element) {\n                var computedStyle = kendo.getComputedStyles(element, ['overflow']);\n                return computedStyle.overflow !== 'visible';\n            }).add(window);\n            scrollableParents.each(function (index, parent) {\n                scrollTopPositions[index] = $(parent).scrollTop();\n            });\n            try {\n                element[0].setActive();\n            } catch (e) {\n                element[0].focus();\n            }\n            scrollableParents.each(function (index, parent) {\n                $(parent).scrollTop(scrollTopPositions[index]);\n            });\n        };\n        kendo.matchesMedia = function (mediaQuery) {\n            var media = kendo._bootstrapToMedia(mediaQuery) || mediaQuery;\n            return support.matchMedia && window.matchMedia(media).matches;\n        };\n        kendo._bootstrapToMedia = function (bootstrapMedia) {\n            return {\n                'xs': '(max-width: 576px)',\n                'sm': '(min-width: 576px)',\n                'md': '(min-width: 768px)',\n                'lg': '(min-width: 992px)',\n                'xl': '(min-width: 1200px)'\n            }[bootstrapMedia];\n        };\n        (function () {\n            function postToProxy(dataURI, fileName, proxyURL, proxyTarget) {\n                var form = $('<form>').attr({\n                    action: proxyURL,\n                    method: 'POST',\n                    target: proxyTarget\n                });\n                var data = kendo.antiForgeryTokens();\n                data.fileName = fileName;\n                var parts = dataURI.split(';base64,');\n                data.contentType = parts[0].replace('data:', '');\n                data.base64 = parts[1];\n                for (var name in data) {\n                    if (data.hasOwnProperty(name)) {\n                        $('<input>').attr({\n                            value: data[name],\n                            name: name,\n                            type: 'hidden'\n                        }).appendTo(form);\n                    }\n                }\n                form.appendTo('body').submit().remove();\n            }\n            var fileSaver = document.createElement('a');\n            var downloadAttribute = 'download' in fileSaver && !kendo.support.browser.edge;\n            function saveAsBlob(dataURI, fileName) {\n                var blob = dataURI;\n                if (typeof dataURI == 'string') {\n                    var parts = dataURI.split(';base64,');\n                    var contentType = parts[0];\n                    var base64 = atob(parts[1]);\n                    var array = new Uint8Array(base64.length);\n                    for (var idx = 0; idx < base64.length; idx++) {\n                        array[idx] = base64.charCodeAt(idx);\n                    }\n                    blob = new Blob([array.buffer], { type: contentType });\n                }\n                navigator.msSaveBlob(blob, fileName);\n            }\n            function saveAsDataURI(dataURI, fileName) {\n                if (window.Blob && dataURI instanceof Blob) {\n                    dataURI = URL.createObjectURL(dataURI);\n                }\n                fileSaver.download = fileName;\n                fileSaver.href = dataURI;\n                var e = document.createEvent('MouseEvents');\n                e.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n                fileSaver.dispatchEvent(e);\n                setTimeout(function () {\n                    URL.revokeObjectURL(dataURI);\n                });\n            }\n            kendo.saveAs = function (options) {\n                var save = postToProxy;\n                if (!options.forceProxy) {\n                    if (downloadAttribute) {\n                        save = saveAsDataURI;\n                    } else if (navigator.msSaveBlob) {\n                        save = saveAsBlob;\n                    }\n                }\n                save(options.dataURI, options.fileName, options.proxyURL, options.proxyTarget);\n            };\n        }());\n        kendo.proxyModelSetters = function proxyModelSetters(data) {\n            var observable = {};\n            Object.keys(data || {}).forEach(function (property) {\n                Object.defineProperty(observable, property, {\n                    get: function () {\n                        return data[property];\n                    },\n                    set: function (value) {\n                        data[property] = value;\n                        data.dirty = true;\n                    }\n                });\n            });\n            return observable;\n        };\n    }(jQuery, window));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}