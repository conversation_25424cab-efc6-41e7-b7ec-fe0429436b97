{"version": 3, "sources": ["kendo.dom.js"], "names": ["f", "define", "kendo", "Node", "this", "node", "NullNode", "Element", "nodeName", "attr", "children", "TextNode", "nodeValue", "String", "HtmlNode", "html", "insertHtml", "HTML_CONTAINER", "innerHTML", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "value", "element", "attrs", "text", "Tree", "root", "NULL_NODE", "prototype", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "style", "appendTo", "parent", "index", "document", "createElement", "length", "render", "cached", "cachedChildren", "cachedLength", "Math", "abs", "<PERSON><PERSON><PERSON><PERSON>", "syncAttributes", "removeAttributes", "cachedAttr", "name", "cachedValue", "setStyle", "setAttribute", "key", "removeStyle", "cachedStyle", "undefined", "removeAttribute", "cssText", "className", "i", "str", "createTextNode", "el", "nodes", "<PERSON><PERSON><PERSON><PERSON>", "child", "nextS<PERSON>ling", "push", "slice", "dom", "window", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,aAAc,cAAeD,IACtC,WA+PE,MAvPC,UAAUE,GACP,QAASC,KACLC,KAAKC,KAAO,KAchB,QAASC,MAUT,QAASC,GAAQC,EAAUC,EAAMC,GAC7BN,KAAKI,SAAWA,EAChBJ,KAAKK,KAAOA,MACZL,KAAKM,SAAWA,MAkHpB,QAASC,GAASC,GACdR,KAAKQ,UAAmBA,EAAPC,GAuBrB,QAASC,GAASC,GACdX,KAAKW,KAAOA,EA4BhB,QAASC,GAAWX,EAAMU,GAEtB,IADAE,EAAeC,UAAYH,EACpBE,EAAeE,YAClBd,EAAKe,YAAYH,EAAeE,YAGxC,QAASJ,GAAKM,GACV,MAAO,IAAIP,GAASO,GAExB,QAASC,GAAQd,EAAUe,EAAOb,GAC9B,MAAO,IAAIH,GAAQC,EAAUe,EAAOb,GAExC,QAASc,GAAKH,GACV,MAAO,IAAIV,GAASU,GAExB,QAASI,GAAKC,GACVtB,KAAKsB,KAAOA,EACZtB,KAAKM,YArNZ,GAyBOiB,GA0KAV,CA/LJd,GAAKyB,WACDC,OAAQ,WACAzB,KAAKC,KAAKyB,YACV1B,KAAKC,KAAKyB,WAAWC,YAAY3B,KAAKC,MAE1CD,KAAKK,SAETA,QACAe,KAAM,WACF,MAAO,KAKflB,EAASsB,WACLpB,SAAU,QACVC,MAAQuB,UACRtB,YACAmB,OAAQ,cAGRF,EAAY,GAAIrB,GAMpBC,EAAQqB,UAAY,GAAIzB,GACxBI,EAAQqB,UAAUK,SAAW,SAAUC,GAAV,GAGhBC,GAFL9B,EAAO+B,SAASC,cAAcjC,KAAKI,UACnCE,EAAWN,KAAKM,QACpB,KAASyB,EAAQ,EAAGA,EAAQzB,EAAS4B,OAAQH,IACzCzB,EAASyB,GAAOI,OAAOlC,EAAMsB,EAGjC,OADAO,GAAOd,YAAYf,GACZA,GAEXE,EAAQqB,UAAUW,OAAS,SAAUL,EAAQM,GAAlB,GACnBnC,GAMI8B,EACAzB,EACA4B,EACAG,EACAC,CATR,IAAIF,EAAOhC,WAAaJ,KAAKI,SACzBgC,EAAOX,SACPxB,EAAOD,KAAK6B,SAASC,OAClB,CAOH,GANA7B,EAAOmC,EAAOnC,KAEVK,EAAWN,KAAKM,SAChB4B,EAAS5B,EAAS4B,OAClBG,EAAiBD,EAAO9B,SACxBgC,EAAeD,EAAeH,OAC9BK,KAAKC,IAAIF,EAAeJ,GAAU,EAMlC,WALAlC,MAAKmC,QACDnB,YAAa,SAAUf,GACnB6B,EAAOW,aAAaxC,EAAMmC,EAAOnC,QAEtCsB,EAGP,KAAKQ,EAAQ,EAAGA,EAAQG,EAAQH,IAC5BzB,EAASyB,GAAOI,OAAOlC,EAAMoC,EAAeN,IAAUR,EAE1D,KAAKQ,EAAQG,EAAQH,EAAQO,EAAcP,IACvCM,EAAeN,GAAON,SAG9BzB,KAAKC,KAAOA,EACZD,KAAK0C,eAAeN,EAAO/B,MAC3BL,KAAK2C,iBAAiBP,EAAO/B,OAEjCF,EAAQqB,UAAUkB,eAAiB,SAAUE,GAAV,GAEtBC,GACD5B,EACA6B,EAHJzC,EAAOL,KAAKK,IAChB,KAASwC,IAAQxC,GACTY,EAAQZ,EAAKwC,GACbC,EAAcF,EAAWC,GAChB,UAATA,EACA7C,KAAK+C,SAAS9B,EAAO6B,GACd7B,IAAU6B,GACjB9C,KAAKgD,aAAaH,EAAM5B,EAAO6B,IAI3C3C,EAAQqB,UAAUuB,SAAW,SAAUnB,EAAOkB,GAAjB,GAErBG,GADAhD,EAAOD,KAAKC,IAEhB,IAAI6C,EACA,IAAKG,IAAOrB,GACJA,EAAMqB,KAASH,EAAYG,KAC3BhD,EAAK2B,MAAMqB,GAAOrB,EAAMqB,QAIhC,KAAKA,IAAOrB,GACR3B,EAAK2B,MAAMqB,GAAOrB,EAAMqB,IAIpC9C,EAAQqB,UAAU0B,YAAc,SAAUC,GAAV,GAGnBF,GAFLrB,EAAQ5B,KAAKK,KAAKuB,UAClB3B,EAAOD,KAAKC,IAChB,KAASgD,IAAOE,GACOC,SAAfxB,EAAMqB,KACNhD,EAAK2B,MAAMqB,GAAO,KAI9B9C,EAAQqB,UAAUmB,iBAAmB,SAAUC,GAAV,GAExBC,GADLxC,EAAOL,KAAKK,IAChB,KAASwC,IAAQD,GACA,UAATC,EACA7C,KAAKkD,YAAYN,EAAWhB,OACNwB,SAAf/C,EAAKwC,IACZ7C,KAAKqD,gBAAgBR,IAIjC1C,EAAQqB,UAAU6B,gBAAkB,SAAUR,GAC1C,GAAI5C,GAAOD,KAAKC,IACH,WAAT4C,EACA5C,EAAK2B,MAAM0B,QAAU,GACL,cAATT,EACP5C,EAAKsD,UAAY,GAEjBtD,EAAKoD,gBAAgBR,IAG7B1C,EAAQqB,UAAUwB,aAAe,SAAUH,EAAM5B,GAC7C,GAAIhB,GAAOD,KAAKC,IACGmD,UAAfnD,EAAK4C,GACL5C,EAAK4C,GAAQ5B,EAEbhB,EAAK+C,aAAaH,EAAM5B,IAGhCd,EAAQqB,UAAUJ,KAAO,WAAA,GAEZoC,GADLC,EAAM,EACV,KAASD,EAAI,EAAGA,EAAIxD,KAAKM,SAAS4B,SAAUsB,EACxCC,GAAOzD,KAAKM,SAASkD,GAAGpC,MAE5B,OAAOqC,IAKXlD,EAASiB,UAAY,GAAIzB,GACzBQ,EAASiB,UAAUpB,SAAW,QAC9BG,EAASiB,UAAUW,OAAS,SAAUL,EAAQM,GAC1C,GAAInC,EACAmC,GAAOhC,WAAaJ,KAAKI,UACzBgC,EAAOX,SACPxB,EAAO+B,SAAS0B,eAAe1D,KAAKQ,WACpCsB,EAAOd,YAAYf,KAEnBA,EAAOmC,EAAOnC,KACVD,KAAKQ,YAAc4B,EAAO5B,WACtBP,EAAKyB,aACLzB,EAAKO,UAAYR,KAAKQ,YAIlCR,KAAKC,KAAOA,GAEhBM,EAASiB,UAAUJ,KAAO,WACtB,MAAOpB,MAAKQ,WAKhBE,EAASc,WACLpB,SAAU,QACVC,QACAoB,OAAQ,WAAA,GACKM,GACD4B,CADR,KAAS5B,EAAQ,EAAGA,EAAQ/B,KAAK4D,MAAM1B,OAAQH,IACvC4B,EAAK3D,KAAK4D,MAAM7B,GAChB4B,EAAGjC,YACHiC,EAAGjC,WAAWC,YAAYgC,IAItCxB,OAAQ,SAAUL,EAAQM,GAAlB,GAGIyB,GAGKC,CALb,IAAI1B,EAAOhC,WAAaJ,KAAKI,UAAYgC,EAAOzB,OAASX,KAAKW,KAK1D,IAJAyB,EAAOX,SACHoC,EAAY/B,EAAO+B,UACvBjD,EAAWkB,EAAQ9B,KAAKW,MACxBX,KAAK4D,SACIE,EAAQD,EAAYA,EAAUE,YAAcjC,EAAOf,WAAY+C,EAAOA,EAAQA,EAAMC,YACzF/D,KAAK4D,MAAMI,KAAKF,OAGpB9D,MAAK4D,MAAQxB,EAAOwB,MAAMK,MAAM,KAIxCpD,EAAiBmB,SAASC,cAAc,OAoB5CZ,EAAKG,WACDb,KAAMA,EACNO,QAASA,EACTE,KAAMA,EACNe,OAAQ,SAAU7B,GAAV,GAEAyB,GACAG,EAEIE,EAJJC,EAAiBrC,KAAKM,QAG1B,KAAKyB,EAAQ,EAAGG,EAAS5B,EAAS4B,OAAQH,EAAQG,EAAQH,IAClDK,EAASC,EAAeN,GACvBK,EAEOA,EAAOnC,MAASmC,EAAOnC,KAAKyB,aACpCU,EAAOX,SACPW,EAASb,GAHTa,EAASb,EAKbjB,EAASyB,GAAOI,OAAOnC,KAAKsB,KAAMc,EAEtC,KAAKL,EAAQG,EAAQH,EAAQM,EAAeH,OAAQH,IAChDM,EAAeN,GAAON,QAE1BzB,MAAKM,SAAWA,IAGxBR,EAAMoE,KACFvD,KAAMA,EACNS,KAAMA,EACNF,QAASA,EACTG,KAAMA,EACNtB,KAAMA,IAEZoE,OAAOrE,OACFqE,OAAOrE,OACE,kBAAVD,SAAwBA,OAAOuE,IAAMvE,OAAS,SAAUwE,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dom.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.dom', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'dom',\n        name: 'Virtual DOM',\n        category: 'framework',\n        depends: ['core'],\n        advanced: true\n    };\n    (function (kendo) {\n        function Node() {\n            this.node = null;\n        }\n        Node.prototype = {\n            remove: function () {\n                if (this.node.parentNode) {\n                    this.node.parentNode.removeChild(this.node);\n                }\n                this.attr = {};\n            },\n            attr: {},\n            text: function () {\n                return '';\n            }\n        };\n        function NullNode() {\n        }\n        NullNode.prototype = {\n            nodeName: '#null',\n            attr: { style: {} },\n            children: [],\n            remove: function () {\n            }\n        };\n        var NULL_NODE = new NullNode();\n        function Element(nodeName, attr, children) {\n            this.nodeName = nodeName;\n            this.attr = attr || {};\n            this.children = children || [];\n        }\n        Element.prototype = new Node();\n        Element.prototype.appendTo = function (parent) {\n            var node = document.createElement(this.nodeName);\n            var children = this.children;\n            for (var index = 0; index < children.length; index++) {\n                children[index].render(node, NULL_NODE);\n            }\n            parent.appendChild(node);\n            return node;\n        };\n        Element.prototype.render = function (parent, cached) {\n            var node;\n            if (cached.nodeName !== this.nodeName) {\n                cached.remove();\n                node = this.appendTo(parent);\n            } else {\n                node = cached.node;\n                var index;\n                var children = this.children;\n                var length = children.length;\n                var cachedChildren = cached.children;\n                var cachedLength = cachedChildren.length;\n                if (Math.abs(cachedLength - length) > 2) {\n                    this.render({\n                        appendChild: function (node) {\n                            parent.replaceChild(node, cached.node);\n                        }\n                    }, NULL_NODE);\n                    return;\n                }\n                for (index = 0; index < length; index++) {\n                    children[index].render(node, cachedChildren[index] || NULL_NODE);\n                }\n                for (index = length; index < cachedLength; index++) {\n                    cachedChildren[index].remove();\n                }\n            }\n            this.node = node;\n            this.syncAttributes(cached.attr);\n            this.removeAttributes(cached.attr);\n        };\n        Element.prototype.syncAttributes = function (cachedAttr) {\n            var attr = this.attr;\n            for (var name in attr) {\n                var value = attr[name];\n                var cachedValue = cachedAttr[name];\n                if (name === 'style') {\n                    this.setStyle(value, cachedValue);\n                } else if (value !== cachedValue) {\n                    this.setAttribute(name, value, cachedValue);\n                }\n            }\n        };\n        Element.prototype.setStyle = function (style, cachedValue) {\n            var node = this.node;\n            var key;\n            if (cachedValue) {\n                for (key in style) {\n                    if (style[key] !== cachedValue[key]) {\n                        node.style[key] = style[key];\n                    }\n                }\n            } else {\n                for (key in style) {\n                    node.style[key] = style[key];\n                }\n            }\n        };\n        Element.prototype.removeStyle = function (cachedStyle) {\n            var style = this.attr.style || {};\n            var node = this.node;\n            for (var key in cachedStyle) {\n                if (style[key] === undefined) {\n                    node.style[key] = '';\n                }\n            }\n        };\n        Element.prototype.removeAttributes = function (cachedAttr) {\n            var attr = this.attr;\n            for (var name in cachedAttr) {\n                if (name === 'style') {\n                    this.removeStyle(cachedAttr.style);\n                } else if (attr[name] === undefined) {\n                    this.removeAttribute(name);\n                }\n            }\n        };\n        Element.prototype.removeAttribute = function (name) {\n            var node = this.node;\n            if (name === 'style') {\n                node.style.cssText = '';\n            } else if (name === 'className') {\n                node.className = '';\n            } else {\n                node.removeAttribute(name);\n            }\n        };\n        Element.prototype.setAttribute = function (name, value) {\n            var node = this.node;\n            if (node[name] !== undefined) {\n                node[name] = value;\n            } else {\n                node.setAttribute(name, value);\n            }\n        };\n        Element.prototype.text = function () {\n            var str = '';\n            for (var i = 0; i < this.children.length; ++i) {\n                str += this.children[i].text();\n            }\n            return str;\n        };\n        function TextNode(nodeValue) {\n            this.nodeValue = String(nodeValue);\n        }\n        TextNode.prototype = new Node();\n        TextNode.prototype.nodeName = '#text';\n        TextNode.prototype.render = function (parent, cached) {\n            var node;\n            if (cached.nodeName !== this.nodeName) {\n                cached.remove();\n                node = document.createTextNode(this.nodeValue);\n                parent.appendChild(node);\n            } else {\n                node = cached.node;\n                if (this.nodeValue !== cached.nodeValue) {\n                    if (node.parentNode) {\n                        node.nodeValue = this.nodeValue;\n                    }\n                }\n            }\n            this.node = node;\n        };\n        TextNode.prototype.text = function () {\n            return this.nodeValue;\n        };\n        function HtmlNode(html) {\n            this.html = html;\n        }\n        HtmlNode.prototype = {\n            nodeName: '#html',\n            attr: {},\n            remove: function () {\n                for (var index = 0; index < this.nodes.length; index++) {\n                    var el = this.nodes[index];\n                    if (el.parentNode) {\n                        el.parentNode.removeChild(el);\n                    }\n                }\n            },\n            render: function (parent, cached) {\n                if (cached.nodeName !== this.nodeName || cached.html !== this.html) {\n                    cached.remove();\n                    var lastChild = parent.lastChild;\n                    insertHtml(parent, this.html);\n                    this.nodes = [];\n                    for (var child = lastChild ? lastChild.nextSibling : parent.firstChild; child; child = child.nextSibling) {\n                        this.nodes.push(child);\n                    }\n                } else {\n                    this.nodes = cached.nodes.slice(0);\n                }\n            }\n        };\n        var HTML_CONTAINER = document.createElement('div');\n        function insertHtml(node, html) {\n            HTML_CONTAINER.innerHTML = html;\n            while (HTML_CONTAINER.firstChild) {\n                node.appendChild(HTML_CONTAINER.firstChild);\n            }\n        }\n        function html(value) {\n            return new HtmlNode(value);\n        }\n        function element(nodeName, attrs, children) {\n            return new Element(nodeName, attrs, children);\n        }\n        function text(value) {\n            return new TextNode(value);\n        }\n        function Tree(root) {\n            this.root = root;\n            this.children = [];\n        }\n        Tree.prototype = {\n            html: html,\n            element: element,\n            text: text,\n            render: function (children) {\n                var cachedChildren = this.children;\n                var index;\n                var length;\n                for (index = 0, length = children.length; index < length; index++) {\n                    var cached = cachedChildren[index];\n                    if (!cached) {\n                        cached = NULL_NODE;\n                    } else if (!cached.node || !cached.node.parentNode) {\n                        cached.remove();\n                        cached = NULL_NODE;\n                    }\n                    children[index].render(this.root, cached);\n                }\n                for (index = length; index < cachedChildren.length; index++) {\n                    cachedChildren[index].remove();\n                }\n                this.children = children;\n            }\n        };\n        kendo.dom = {\n            html: html,\n            text: text,\n            element: element,\n            Tree: Tree,\n            Node: Node\n        };\n    }(window.kendo));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}