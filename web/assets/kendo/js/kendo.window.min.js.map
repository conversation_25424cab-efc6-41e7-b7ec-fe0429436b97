{"version": 3, "sources": ["kendo.window.js"], "names": ["f", "define", "$", "undefined", "defined", "x", "toInt", "element", "property", "parseInt", "css", "constrain", "value", "low", "high", "normalizedValue", "isNaN", "indexOf", "Math", "max", "min", "Infinity", "executableScript", "this", "type", "toLowerCase", "getPosition", "elem", "parentOverflowX", "parentOverflowY", "result", "top", "offsetTop", "left", "offsetLeft", "parent", "offsetParent", "scrollTop", "scrollLeft", "WindowResizing", "wnd", "that", "owner", "_preventDragging", "_draggable", "Draggable", "wrapper", "filter", "KWINDOWRESIZEHANDLES", "group", "id", "dragstart", "proxy", "drag", "dragend", "userEvents", "bind", "addOverlay", "removeOverlay", "WindowDragging", "dragHandle", "dragcancel", "stopPropagation", "kendo", "window", "Widget", "ui", "TabKeyTrap", "Popup", "isPlainObject", "activeElement", "_activeElement", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "extend", "each", "template", "BODY", "NS", "MODAL_NS", "KWINDOW", "KWINDOWTITLE", "KWINDOWTITLEBAR", "KWINDOWCONTENT", "KDIALOGCONTENT", "KOVERLAY", "KCONTENTFRAME", "LOADING", "KHOVERSTATE", "KFOCUSEDSTATE", "MAXIMIZEDSTATE", "VISIBLE", "HIDDEN", "CURSOR", "OPEN", "ACTIVATE", "DEACTIVATE", "CLOSE", "REFRESH", "MINIMIZE", "MAXIMIZE", "RESIZESTART", "RESIZE", "RESIZEEND", "DRAGSTART", "DRAGEND", "ERROR", "OVERFLOW", "DATADOCOVERFLOWRULE", "ZINDEX", "MINIMIZE_MAXIMIZE", "KPIN", "KUNPIN", "PIN_UNPIN", "TITLEBAR_BUTTONS", "REFRESHICON", "WINDOWEVENTSHANDLED", "zero", "isLocalUrl", "SIZE", "small", "medium", "large", "Window", "init", "options", "visibility", "display", "position", "content", "windowContent", "windowFrame", "globalWindow", "offset", "isVisible", "suppressActions", "actions", "length", "fn", "call", "appendTo", "containment", "draggable", "first", "url", "find", "remove", "is", "visible", "closest", "addClass", "_createWindow", "title", "_dimensions", "minTop", "minLeft", "maxTop", "maxLeft", "_position", "refresh", "toFront", "children", "_tabindex", "modal", "_overlay", "opacity", "on", "_buttonEnter", "_buttonLeave", "_windowActionHandler", "_keydown", "_focus", "_blur", "data", "windowInstance", "document", "widgetInstance", "not", "i", "_resizable", "pinned", "pin", "attr", "role", "aria-<PERSON>by", "add", "touchScroller", "_resizeHandler", "_onDocumentResize", "_marker", "guid", "substring", "trigger", "notify", "_tabKeyTrap", "trap", "shouldTrap", "e", "currentTarget", "removeClass", "width", "height", "maxHeight", "sizeClass", "size", "dimensions", "contentBoxSizing", "lrBorderWidth", "tbBorderWidth", "paddingTop", "_isPinned", "_updateBoundaries", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "minHeight", "hide", "innerWidth", "innerHeight", "clientWidth", "support", "scrollbar", "clientHeight", "_animationOptions", "animation", "basicAnimation", "open", "effects", "close", "_resize", "resize", "resizable", "resizing", "off", "destroy", "target", "toggleMaximization", "split", "index", "handler", "append", "templates", "resizeHandle", "dragging", "_actions", "titlebar", "container", "windowSpecificCommands", "map", "action", "name", "html", "render", "setOptions", "scrollable", "cachedOptions", "JSON", "parse", "stringify", "restore", "_enableDocumentScrolling", "events", "zoom", "direction", "fade", "duration", "properties", "scale", "autoFocus", "isMaximized", "isMinimized", "_closable", "inArray", "handled", "newWidth", "newHeight", "w", "h", "keys", "keyCode", "distance", "ESC", "_close", "_closing", "altKey", "unpin", "UP", "focus", "maximize", "DOWN", "minimize", "getOffset", "ctrl<PERSON>ey", "LEFT", "RIGHT", "preventDefault", "overlay", "insertBefore", "toggle", "preventScroll", "_stopDocumentScrolling", "_actionForIcon", "icon", "iconClass", "exec", "className", "k-i-close", "k-i-window-maximize", "k-i-window-minimize", "k-i-window-restore", "k-i-refresh", "k-i-pin", "k-i-unpin", "_modals", "zStack", "dom", "object", "_object", "sort", "a", "b", "widget", "center", "newTop", "newLeft", "documentWindow", "_scrollIsAppended", "titleBarHeight", "encoded", "titleBar", "titleElement", "arguments", "text", "htmlEncode", "prepend", "scrollContainer", "angular", "elements", "empty", "push", "dataItem", "otherModalsVisible", "overlayFx", "showOptions", "contentElement", "containmentContext", "doc", "kendoStop", "Fade", "fx", "fadeIn", "endValue", "play", "show", "kendoAnimate", "complete", "_activate", "_containerScrollTop", "_containerScrollLeft", "_removeOverlay", "suppressAnimation", "modals", "hideOverlay", "hideOptions", "fadeOut", "startValue", "last", "systemTriggered", "defaultPrevented", "userTriggered", "reverse", "_deactivate", "lastModal", "_actionable", "_shouldFocus", "active", "windowTop", "currentWindow", "zIndex", "originalZIndex", "windowObject", "zIndexNew", "style", "setTimeout", "shouldRestrictTop", "restoreOptions", "end", "removeAttr", "_sizingAction", "actionId", "callback", "eq", "before", "$body", "$html", "_storeOverflowRule", "_restoreOverflowRule", "body", "$element", "_isOverflowStored", "overflowRule", "get", "overflow", "removeData", "win", "zoomLevel", "iframe", "showIframe", "initOptions", "src", "contentFrame", "unbind", "_triggerRefresh", "_ajaxRequest", "toggleClass", "_ajaxComplete", "clearTimeout", "_loadingIconTimeout", "_ajaxError", "xhr", "status", "_ajaxSuccess", "contentTemplate", "prop", "_showLoading", "ajax", "dataType", "cache", "error", "success", "_destroy", "iframeSrcAttributes", "contentHtml", "isRtl", "getAttribute", "editor", "prototype", "marginTop", "marginLeft", "<PERSON><PERSON><PERSON><PERSON>", "wrapperPosition", "relativeElMarginLeft", "relativeElMarginTop", "elementPadding", "initialPosition", "resizeDirection", "replace", "initialSize", "containerOffset", "test", "_relativeElMarginLeft", "_relativeElMarginTop", "rtl", "leftRtlOffset", "scrollOffset", "windowBottom", "windowRight", "y", "newWindowTop", "location", "reset", "initialWindowPosition", "initialPointerPosition", "startPosition", "client", "axis", "transforms", "_finishDrag", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gBACH,oBACA,eACDD,IACL,WA48CE,MA37CC,UAAUE,EAAGC,GAMV,QAASC,GAAQC,GACb,MAAmB,KAALA,EAElB,QAASC,GAAMC,EAASC,GACpB,MAAOC,UAASF,EAAQG,IAAIF,GAAW,KAAO,EAElD,QAASG,GAAUC,EAAOC,EAAKC,GAC3B,GAAIC,EAMJ,OAJIA,GADAH,GAASI,MAAMJ,KAAUA,GAAAA,GAAiBK,QAAQ,MAAQ,EACxCL,EAEAM,KAAKC,IAAID,KAAKE,IAAIX,SAASG,EAAO,IAAKE,IAASO,EAAAA,EAAWP,EAAOL,SAASK,EAAM,KAAMD,MAASQ,EAAAA,GAAWR,EAAMJ,SAASI,EAAK,KAIzJ,QAASS,KACL,OAAQC,KAAKC,MAAQD,KAAKC,KAAKC,cAAcR,QAAQ,WAAa,EAEtE,QAASS,GAAYC,GAKjB,IALJ,GAQYC,GACAC,EARJC,GACIC,IAAKJ,EAAKK,UACVC,KAAMN,EAAKO,YACZC,EAASR,EAAKS,aACdD,GACHL,EAAOC,KAAOI,EAAOH,UACrBF,EAAOG,MAAQE,EAAOD,WAClBN,EAAkB1B,EAAEiC,GAAQzB,IAAI,aAChCmB,EAAkB3B,EAAEiC,GAAQzB,IAAI,aACZ,SAApBmB,GAAkD,WAApBA,IAC9BC,EAAOC,KAAOI,EAAOE,WAED,SAApBT,GAAkD,WAApBA,IAC9BE,EAAOG,MAAQE,EAAOG,YAE1BH,EAASA,EAAOC,YAEpB,OAAON,GA2pCX,QAASS,GAAeC,GACpB,GAAIC,GAAOlB,IACXkB,GAAKC,MAAQF,EACbC,EAAKE,kBAAmB,EACxBF,EAAKG,WAAa,GAAIC,GAAUL,EAAIM,SAChCC,OAAQ,IAAMC,EACdC,MAAOT,EAAIM,QAAQI,GAAK,YACxBC,UAAWC,EAAMX,EAAKU,UAAWV,GACjCY,KAAMD,EAAMX,EAAKY,KAAMZ,GACvBa,QAASF,EAAMX,EAAKa,QAASb,KAEjCA,EAAKG,WAAWW,WAAWC,KAAK,QAASJ,EAAMX,EAAKgB,WAAYhB,IAChEA,EAAKG,WAAWW,WAAWC,KAAK,UAAWJ,EAAMX,EAAKiB,cAAejB,IAmIzE,QAASkB,GAAenB,EAAKoB,GACzB,GAAInB,GAAOlB,IACXkB,GAAKC,MAAQF,EACbC,EAAKE,kBAAmB,EACxBF,EAAKG,WAAa,GAAIC,GAAUL,EAAIM,SAChCC,OAAQa,EACRX,MAAOT,EAAIM,QAAQI,GAAK,UACxBC,UAAWC,EAAMX,EAAKU,UAAWV,GACjCY,KAAMD,EAAMX,EAAKY,KAAMZ,GACvBa,QAASF,EAAMX,EAAKa,QAASb,GAC7BoB,WAAYT,EAAMX,EAAKoB,WAAYpB,KAEvCA,EAAKG,WAAWW,WAAWO,iBAAkB,EAh2CpD,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAMG,GAAGD,OAAQE,EAAaJ,EAAMG,GAAGE,MAAMD,WAAYtB,EAAYkB,EAAMG,GAAGrB,UAAWwB,EAAgBnE,EAAEmE,cAAeC,EAAgBP,EAAMQ,eAAgBC,EAAaT,EAAMU,YAAaC,EAAcX,EAAMY,aAAcvB,EAAQlD,EAAEkD,MAAOwB,EAAS1E,EAAE0E,OAAQC,EAAO3E,EAAE2E,KAAMC,EAAWf,EAAMe,SAAUC,EAAO,OAAmBC,EAAK,eAAgBC,EAAW,oBAAqBC,EAAU,YAAaC,EAAe,kBAAmBC,EAAkBD,EAAe,MAAOE,EAAiB,oBAAqBC,EAAiB,oBAAqBtC,EAAuB,mBAAoBuC,EAAW,aAAcC,EAAgB,kBAAmBC,EAAU,cAAeC,EAAc,gBAAiBC,EAAgB,kBAAmBC,EAAiB,qBAAsBC,EAAU,WAAYC,EAAS,SAAUC,EAAS,SAAUC,EAAO,OAAQC,EAAW,WAAYC,EAAa,aAAcC,EAAQ,QAASC,EAAU,UAAWC,EAAW,WAAYC,EAAW,WAAYC,EAAc,cAAeC,EAAS,SAAUC,EAAY,YAAaC,EAAY,YAAaC,EAAU,UAAWC,EAAQ,QAASC,EAAW,WAAYC,EAAsB,yBAA0BC,GAAS,SAAUC,GAAoB,gFAAiFC,GAAO,WAAYC,GAAS,aAAcC,GAAYF,GAAO,IAAMC,GAAQE,GAAmB,sCAAuCC,GAAc,kCAAmCC,GAAsB,sBAAuBC,GAAO,aAAcC,GAAazD,EAAMyD,WAAYC,IAC/lDC,MAAO,cACPC,OAAQ,cACRC,MAAO,eAwCXC,GAAS5D,EAAOW,QAChBkD,KAAM,SAAUvH,EAASwH,GACrB,GAAiBjF,GAAsBkF,EAAYC,EAASC,EAA6BC,EAASC,EAAeC,EAAaC,EAAuFpF,EAAjNT,EAAOlB,KAAegH,KAA4CC,GAAY,EAA0DC,EAAkBV,GAAWA,EAAQW,UAAYX,EAAQW,QAAQC,MAC7M1E,GAAO2E,GAAGd,KAAKe,KAAKpG,EAAMlC,EAASwH,GACnCA,EAAUtF,EAAKsF,QACfG,EAAWH,EAAQG,SACnB3H,EAAUkC,EAAKlC,QACf4H,EAAUJ,EAAQI,QAClBG,EAAepI,EAAE8D,QACbyE,IACAV,EAAQW,YAEZjG,EAAKqG,SAAW5I,EAAE6H,EAAQe,UAC1BrG,EAAKsG,YAAchB,EAAQiB,UAAUD,YAAc7I,EAAE6H,EAAQiB,UAAUD,aAAaE,QAAU,KAC1Fd,IAAY9D,EAAc8D,KAC1BA,EAAUJ,EAAQI,SAAYe,IAAKf,IAEvC5H,EAAQ4I,KAAK,UAAUpG,OAAOzB,GAAkB8H,SAC3C7I,EAAQ4B,SAASkH,GAAG5G,EAAKqG,WAAcrG,EAAKsG,aAAgBb,EAASnG,MAAQ5B,GAAa+H,EAASjG,OAAS9B,IACzGI,EAAQ8I,GAAGxD,IACX0C,EAAShI,EAAQgI,SACjBC,GAAY,IAEZR,EAAazH,EAAQG,IAAI,cACzBuH,EAAU1H,EAAQG,IAAI,WACtBH,EAAQG,KACJsH,WAAYlC,EACZmC,QAAS,KAEbM,EAAShI,EAAQgI,SACjBhI,EAAQG,KACJsH,WAAYA,EACZC,QAASA,KAGbC,EAASnG,MAAQ5B,IACjB+H,EAASnG,IAAMwG,EAAOxG,KAEtBmG,EAASjG,OAAS9B,IAClB+H,EAASjG,KAAOsG,EAAOtG,OAG1B7B,EAAQ2H,EAAQuB,UAAgC,OAApBvB,EAAQuB,UACrCvB,EAAQuB,QAAU/I,EAAQ8I,GAAGxD,IAEjC/C,EAAUL,EAAKK,QAAUvC,EAAQgJ,QAAQrE,GACpC3E,EAAQ8I,GAAG,eAAkBvG,EAAQ,KACtCvC,EAAQiJ,SAAS,8BACjB/G,EAAKgH,cAAclJ,EAASwH,GAC5BjF,EAAUL,EAAKK,QAAUvC,EAAQgJ,QAAQrE,GACzCzC,EAAKiH,MAAMjH,EAAKsF,QAAQ2B,OACxBjH,EAAKkH,eAETlH,EAAKmH,OAASnH,EAAKoH,UAAWxI,EAAAA,GAC9BoB,EAAKqH,OAASrH,EAAKsH,QAAU1I,EAAAA,EAC7BoB,EAAKuH,YACD7B,GACA1F,EAAKwH,QAAQ9B,GAEbJ,EAAQuB,SACR7G,EAAKyH,UAET9B,EAAgBtF,EAAQqH,SAAS9E,GACjC5C,EAAK2H,UAAUhC,GACXL,EAAQuB,SAAWvB,EAAQsC,OAC3B5H,EAAK6H,SAASxH,EAAQuG,GAAGxD,IAAUnF,KAAM6J,QAAS,KAEtDzH,EAAQ0H,GAAG,aAAexF,EAAIoC,GAAkBhE,EAAMX,EAAKgI,aAAchI,IAAO+H,GAAG,aAAexF,EAAIoC,GAAkBhE,EAAMX,EAAKiI,aAAcjI,IAAO+H,GAAG,QAAUxF,EAAI,KAAOoC,GAAkBhE,EAAMX,EAAKkI,qBAAsBlI,IAAO+H,GAAG,UAAYxF,EAAI5B,EAAMX,EAAKmI,SAAUnI,IAAO+H,GAAG,QAAUxF,EAAI5B,EAAMX,EAAKoI,OAAQpI,IAAO+H,GAAG,OAASxF,EAAI5B,EAAMX,EAAKqI,MAAOrI,IACtW2F,EAAcoC,GAAG,UAAYxF,EAAI5B,EAAMX,EAAKmI,SAAUnI,IAAO+H,GAAG,QAAUxF,EAAI5B,EAAMX,EAAKoI,OAAQpI,IAAO+H,GAAG,OAASxF,EAAI5B,EAAMX,EAAKqI,MAAOrI,IAC1I4F,EAAcD,EAAce,KAAK,IAAM3D,GAAe,GAClD6C,IAAgBC,EAAayC,KAAKzD,MAClCgB,EAAakC,GAAG,OAASxF,EAAI,WAAA,GAGjBgG,GAFJzK,EAAUL,EAAE+K,SAAS3G,eAAenC,OAAOkD,EAC3C9E,GAAQoI,SACJqC,EAAiBjH,EAAMmH,eAAe3K,GAC1CyK,EAAeH,YAGvBvC,EAAakC,GAAG,QAAUxF,EAAI,WAC1B9E,EAAEmF,GAAgB8F,IAAI7F,GAAgBT,KAAK,SAAUuG,EAAG7K,GACpDwD,EAAMmH,eAAehL,EAAEK,IAAUuK,YAGzCxC,EAAayC,KAAKzD,IAAqB,IAE3C/F,KAAK8J,aACL9J,KAAKqB,aACDmF,EAAQuD,QAAU/J,KAAKuB,QAAQuG,GAAG,aAClC5G,EAAK8I,MAETrI,EAAK3C,EAAQiL,KAAK,MACdtI,IACAA,GAAU,aACVJ,EAAQqH,SAAS/E,GAAiB+E,SAAShF,GAAcqG,KAAK,KAAMtI,GACpEkF,EAAcoD,MACVC,KAAQ,SACRC,kBAAmBxI,KAG3BJ,EAAQ6I,IAAI7I,EAAQqH,SAAS,oBAAsB/E,IAAkBoF,GAAG,YAAcxF,EAAI5B,EAAMX,EAAKyH,QAASzH,IAC9GA,EAAKmJ,cAAgB7H,EAAM6H,cAAcrL,GACzCkC,EAAKoJ,eAAiBzI,EAAMX,EAAKqJ,kBAAmBrJ,GACpDA,EAAKsJ,QAAUhI,EAAMiI,OAAOC,UAAU,EAAG,GACzC/L,EAAE8D,QAAQwG,GAAG,SAAWxF,EAAKvC,EAAKsJ,QAAStJ,EAAKoJ,gBAC5C9D,EAAQuB,UACR7G,EAAKyJ,QAAQlG,GACbvD,EAAKyJ,QAAQjG,IAEjBlC,EAAMoI,OAAO1J,GACTlB,KAAKwG,QAAQsC,QACb9I,KAAK6K,YAAc,GAAIjI,GAAWrB,GAClCvB,KAAK6K,YAAYC,OACjB9K,KAAK6K,YAAYE,WAAa,WAC1B,MAAOlE,GAAc2C,KAAK,cAItCN,aAAc,SAAU8B,GACpBrM,EAAEqM,EAAEC,eAAehD,SAAS9D,IAEhCgF,aAAc,SAAU6B,GACpBrM,EAAEqM,EAAEC,eAAeC,YAAY/G,IAEnCmF,OAAQ,WACJtJ,KAAKuB,QAAQ0G,SAAS7D,IAE1BmF,MAAO,WACHvJ,KAAKuB,QAAQ2J,YAAY9G,IAE7BgE,YAAa,WAAA,GAsBAyB,GACDxK,EAtBJkC,EAAUvB,KAAKuB,QACfiF,EAAUxG,KAAKwG,QACf2E,EAAQ3E,EAAQ2E,MAChBC,EAAS5E,EAAQ4E,OACjBC,EAAY7E,EAAQ6E,UACpBC,EAAY9E,EAAQ+E,KACpBC,GACA,WACA,YACA,WACA,aAEAC,EAAgD,eAA7BlK,EAAQpC,IAAI,cAC/BuM,EAAgBD,EAAmB1M,EAAMwC,EAAS,qBAAuBxC,EAAMwC,EAAS,sBAAwB,EAChHoK,EAAgBF,EAAmB1M,EAAMwC,EAAS,oBAAsBxC,EAAMwC,EAAS,uBAAyB,EAChHqK,EAAaH,EAAmB1M,EAAMwC,EAAS,eAAiB,CAMpE,KALIvB,KAAKwH,cAAgBxH,KAAK6L,YAC1B7L,KAAK8L,oBACLtF,EAAQ6E,UAAY1L,KAAKE,IAAIG,KAAKwH,YAAY4D,QAAUO,EAAgBC,GAAaP,GACrF7E,EAAQuF,SAAWpM,KAAKE,IAAIG,KAAKwH,YAAY2D,MAAQO,EAAelF,EAAQuF,WAEvElC,EAAI,EAAGA,EAAI2B,EAAWpE,OAAQyC,IAC/BxK,EAAQmH,EAAQgF,EAAW3B,KAAO,GAClCxK,GAASS,EAAAA,GACTyB,EAAQpC,IAAIqM,EAAW3B,GAAIxK,EAG/BgM,IAAavL,EAAAA,GACbE,KAAKhB,QAAQG,IAAI,YAAakM,GAG9B9J,EAAQ4J,MADRA,EACc/L,EAAU+L,EAAO3E,EAAQwF,SAAUxF,EAAQuF,UAE3C,IAGdxK,EAAQ6J,OADRA,EACehM,EAAUgM,EAAQ5E,EAAQyF,UAAWzF,EAAQ6E,WAE7C,IAEd7E,EAAQuB,SACTxG,EAAQ2K,OAERZ,GAAapF,GAAKoF,IAClB/J,EAAQ0G,SAAS/B,GAAKoF,KAG9B7C,UAAW,WACP,GAAIlH,GAAUvB,KAAKuB,QAASoF,EAAW3G,KAAKwG,QAAQG,QACpD3G,MAAK8L,oBACD9L,KAAKwH,cACLb,EAASnG,IAAMb,KAAKE,IAAIG,KAAKqI,QAAU1B,EAASnG,KAAO,GAAIR,KAAKuI,QAChE5B,EAASjG,KAAOf,KAAKE,IAAIG,KAAKsI,SAAW3B,EAASjG,MAAQ,GAAIV,KAAKwI,UAElD,IAAjB7B,EAASnG,MACTmG,EAASnG,IAAMmG,GAAAA,EAASnG,KAEN,IAAlBmG,EAASjG,OACTiG,EAASjG,KAAOiG,GAAAA,EAASjG,MAE7Ba,EAAQpC,KACJqB,IAAKmG,EAASnG,KAAO,GACrBE,KAAMiG,EAASjG,MAAQ,MAG/BoL,kBAAmB,WACf,GAAItE,GAAcxH,KAAKwH,WACvB,OAAKA,IAGLA,EAAY2D,MAAQ3D,EAAY2E,aAChC3E,EAAY4D,OAAS5D,EAAY4E,cAC7BlN,SAASsI,EAAY2D,MAAO,IAAM3D,EAAY,GAAG6E,cACjD7E,EAAY2D,OAAS3I,EAAM8J,QAAQC,aAEnCrN,SAASsI,EAAY4D,OAAQ,IAAM5D,EAAY,GAAGgF,eAClDhF,EAAY4D,QAAU5I,EAAM8J,QAAQC,aAExC/E,EAAYb,SAAWxG,EAAYqH,EAAY,IAC3CxH,KAAK6L,WACL7L,KAAKqI,OAASrI,KAAKsI,UAAWxI,EAAAA,GAC9BE,KAAKuI,OAASvI,KAAKwI,QAAU1I,EAAAA,IAE7BE,KAAKqI,OAASb,EAAY1G,YAC1Bd,KAAKsI,QAAUd,EAAYzG,aAC3Bf,KAAKwI,QAAUxI,KAAKsI,QAAUd,EAAY2D,MAAQlI,EAAWjD,KAAKuB,SAAS,GAC3EvB,KAAKuI,OAASvI,KAAKqI,OAASb,EAAY4D,OAASjI,EAAYnD,KAAKuB,SAAS,IAhB/EiG,GAFW,MAqBfiF,kBAAmB,SAAU9K,GAAV,GACX+K,GAAY1M,KAAKwG,QAAQkG,UACzBC,GACAC,MAAQC,YACRC,OACIZ,MAAM,EACNW,YAGR,OAAOH,IAAaA,EAAU/K,IAAOgL,EAAehL,IAExDoL,QAAS,WACLvK,EAAMwK,OAAOhN,KAAKhB,QAAQ4J,aAE9BkB,WAAY,WAAA,GACJmD,GAAYjN,KAAKwG,QAAQyG,UACzB1L,EAAUvB,KAAKuB,OACfvB,MAAKkN,WACL3L,EAAQ4L,IAAI,WAAa1J,GAAImF,SAASnH,GAAsBoG,SAC5D7H,KAAKkN,SAASE,UACdpN,KAAKkN,SAAW,MAEhBD,IACA1L,EAAQ0H,GAAG,WAAaxF,EAAII,EAAiBhC,EAAM,SAAUmJ,GACpDrM,EAAEqM,EAAEqC,QAAQrF,QAAQ,oBAAoBZ,QACzCpH,KAAKsN,sBAEVtN,OACHsD,EAAK,sBAAsBiK,MAAM,KAAM,SAAUC,EAAOC,GACpDlM,EAAQmM,OAAOC,GAAUC,aAAaH,MAE1CzN,KAAKkN,SAAW,GAAIlM,GAAehB,OAEvCuB,EAAU,MAEdF,WAAY,WACR,GAAIoG,GAAYzH,KAAKwG,QAAQiB,SACzBzH,MAAK6N,WACL7N,KAAK6N,SAAST,UACdpN,KAAK6N,SAAW,MAEhBpG,IACAzH,KAAK6N,SAAW,GAAIzL,GAAepC,KAAMyH,EAAUpF,YAAcwB,KAGzEiK,SAAU,WAAA,GACFtH,GAAUxG,KAAKwG,QACfW,EAAUX,EAAQW,QAClB4C,EAASvD,EAAQuD,OACjBgE,EAAW/N,KAAKuB,QAAQqH,SAAS/E,GACjCmK,EAAYD,EAASnG,KAAK,qBAC1BqG,GACA,WACA,WAEJ9G,GAAUxI,EAAEuP,IAAI/G,EAAS,SAAUgH,GAE/B,MADAA,GAASpE,GAAmC,QAAzBoE,EAAOjO,cAA0B,QAAUiO,GACrDC,KAAMH,EAAuBvO,QAAQyO,EAAOjO,kBAAsB,UAAYiO,EAASA,KAEpGH,EAAUK,KAAK7L,EAAM8L,OAAOX,GAAUQ,OAAQhH,KAElDoH,WAAY,SAAU/H,GAAV,GAOJgI,GAYIzG,EAlBJ7G,EAAOlB,KACPsL,EAAYpK,EAAKsF,QAAQ+E,KACzBkD,EAAgBC,KAAKC,MAAMD,KAAKE,UAAUpI,GAC9CnD,GAAOmD,EAAQG,SAAUzF,EAAKsF,QAAQG,UACtCtD,EAAOmD,EAAQG,SAAU8H,EAAc9H,UACvCjE,EAAO2E,GAAGkH,WAAWjH,KAAKpG,EAAMsF,GAC5BgI,EAAatN,EAAKsF,QAAQgI,cAAe,EAC7CtN,EAAK2N,UACwB,IAAlBrI,EAAQ2B,OACfjH,EAAKiH,MAAM3B,EAAQ2B,OAEvBjH,EAAKK,QAAQ2J,YAAYhF,GAAKoF,IAC9BpK,EAAKkH,cACLlH,EAAKuH,YACLvH,EAAK4I,aACL5I,EAAKG,aACLH,EAAK4M,WACwB,IAAlBtH,EAAQsC,QACXf,EAAU7G,EAAKsF,QAAQuB,WAAY,EACvC7G,EAAK4N,2BACL5N,EAAK6H,SAASvC,EAAQsC,OAASf,IAEnC7G,EAAKlC,QAAQG,IAAImG,EAAUkJ,EAAa,GAAK,WAEjDO,QACItK,EACAC,EACAC,EACAC,EACAE,EACAC,EACAF,EACAG,EACAC,EACAC,EACAC,EACAC,EACAC,GAEJmB,SACI4H,KAAM,SACN1B,WACIE,MACIC,SACImC,MAAQC,UAAW,MACnBC,MAAQD,UAAW,OAEvBE,SAAU,KAEdrC,OACID,SACImC,MACIC,UAAW,MACXG,YAAcC,MAAO,KAEzBH,MAAQD,UAAW,QAEvBE,SAAU,IACVjD,MAAM,IAGd/D,MAAO,GACPhB,SAAU,SACVmI,WAAW,EACXxG,OAAO,EACPyC,KAAM,OACN0B,WAAW,EACXxF,WAAW,EACXuE,SAAU,GACVC,UAAW,GACXF,SAAUjM,EAAAA,EACVuL,UAAWvL,EAAAA,EACXiK,QAAQ,EACRyE,YAAY,EACZ7H,YACAC,QAAS,KACTmB,QAAS,KACTqD,OAAQ,KACRD,MAAO,KACP5D,SAAU,OACVgI,aAAa,EACbC,aAAa,GAEjBC,UAAW,WACP,MAAO9Q,GAAE+Q,QAAQ,QAAS/Q,EAAEuP,IAAIlO,KAAKwG,QAAQW,QAAS,SAAUrI,GAC5D,MAAOA,GAAEoB,qBAGjBmJ,SAAU,SAAU2B,GAChB,GAAyGhE,GAAQ2I,EAA8FC,EAAUC,EAAWC,EAAGC,EAAnO7O,EAAOlB,KAAMwG,EAAUtF,EAAKsF,QAASwJ,EAAOxN,EAAMwN,KAAMC,EAAUjF,EAAEiF,QAAS1O,EAAUL,EAAKK,QAA0B2O,EAAW,GAAIX,EAAc/I,EAAQ+I,YAAaC,EAAchJ,EAAQgJ,WAC9LS,IAAWD,EAAKG,KAAOjP,EAAKuO,cAC5BzE,EAAEzI,kBACFrB,EAAKkP,QAAO,IAEZpF,EAAEqC,QAAUrC,EAAEC,eAAiB/J,EAAKmP,WAGpCrF,EAAEsF,QAAqB,IAAXL,GACZ/O,EAAKwH,UAELsC,EAAEsF,QAAqB,IAAXL,IACR/O,EAAKsF,QAAQuD,OACb7I,EAAKqP,QAELrP,EAAK8I,OAGTgB,EAAEsF,QAAUL,GAAWD,EAAKQ,GACxBhB,GACAtO,EAAK2N,UACL3N,EAAKlC,QAAQyR,SACLlB,IACRrO,EAAKwP,WACLxP,EAAKlC,QAAQyR,SAEVzF,EAAEsF,QAAUL,GAAWD,EAAKW,OAC9BnB,GAAgBD,EAGVA,IACPrO,EAAK2N,UACL3N,EAAKlC,QAAQyR,UAJbvP,EAAK0P,WACL1P,EAAKK,QAAQkP,UAMrBzJ,EAASxE,EAAMqO,UAAUtP,GACrBL,EAAKsG,cAAgBtG,EAAK2K,YAC1B7E,EAAS9F,EAAKsF,QAAQG,WAEtBH,EAAQiB,WAAcuD,EAAE8F,SAAY9F,EAAEsF,QAAWf,IACjDrO,EAAK4K,oBACDmE,GAAWD,EAAKQ,IAChBxJ,EAAOxG,IAAMpB,EAAU4H,EAAOxG,IAAM0P,EAAUhP,EAAKmH,OAAQnH,EAAKqH,QAChEoH,EAAUpO,EAAQpC,IAAI,MAAO6H,EAAOxG,MAC7ByP,GAAWD,EAAKW,MACvB3J,EAAOxG,IAAMpB,EAAU4H,EAAOxG,IAAM0P,EAAUhP,EAAKmH,OAAQnH,EAAKqH,QAChEoH,EAAUpO,EAAQpC,IAAI,MAAO6H,EAAOxG,MAC7ByP,GAAWD,EAAKe,MACvB/J,EAAOtG,KAAOtB,EAAU4H,EAAOtG,KAAOwP,EAAUhP,EAAKoH,QAASpH,EAAKsH,SACnEmH,EAAUpO,EAAQpC,IAAI,OAAQ6H,EAAOtG,OAC9BuP,GAAWD,EAAKgB,QACvBhK,EAAOtG,KAAOtB,EAAU4H,EAAOtG,KAAOwP,EAAUhP,EAAKoH,QAASpH,EAAKsH,SACnEmH,EAAUpO,EAAQpC,IAAI,OAAQ6H,EAAOtG,QAGzC8F,EAAQyG,WAAajC,EAAE8F,UAAYvB,IAAgBC,IAC/CS,GAAWD,EAAKQ,IAChBb,GAAU,EACVE,EAAYtO,EAAQ6J,SAAW8E,GACxBD,GAAWD,EAAKW,OACvBhB,GAAU,EAENE,EADA3O,EAAKsG,cAAgBtG,EAAK2K,UACdlM,KAAKE,IAAI0B,EAAQ6J,SAAW8E,EAAUhP,EAAKsG,YAAY4D,OAASpE,EAAOxG,IAAMzB,EAAMwC,EAAS,eAAiBxC,EAAMwC,EAAS,qBAAuBxC,EAAMwC,EAAS,mBAElKA,EAAQ6J,SAAW8E,GAGnCD,GAAWD,EAAKe,MAChBpB,GAAU,EACVC,EAAWrO,EAAQ4J,QAAU+E,GACtBD,GAAWD,EAAKgB,QACvBrB,GAAU,EAENC,EADA1O,EAAKsG,cAAgBtG,EAAK2K,UACflM,KAAKE,IAAI0B,EAAQ4J,QAAU+E,EAAUhP,EAAKsG,YAAY2D,MAAQnE,EAAOtG,KAAO3B,EAAMwC,EAAS,mBAAqBxC,EAAMwC,EAAS,qBAE/HA,EAAQ4J,QAAU+E,GAGjCP,IACAG,EAAI1Q,EAAUwQ,EAAUpJ,EAAQwF,SAAUxF,EAAQuF,UAClDgE,EAAI3Q,EAAUyQ,EAAWrJ,EAAQyF,UAAWzF,EAAQ6E,WAC/C5L,MAAMqQ,KACPvO,EAAQ4J,MAAM2E,GACd5O,EAAKsF,QAAQ2E,MAAQ2E,EAAI,MAExBrQ,MAAMsQ,KACPxO,EAAQ6J,OAAO2E,GACf7O,EAAKsF,QAAQ4E,OAAS2E,EAAI,MAE9B7O,EAAK8L,WAGT2C,GACA3E,EAAEiG,mBAGVlI,SAAU,SAAUhB,GAChB,GAAImJ,GAAUlR,KAAKwH,YAAcxH,KAAKwH,YAAYoB,SAAS5E,GAAYhE,KAAKuH,SAASqB,SAAS5E,GAAWzC,EAAUvB,KAAKuB,OAQxH,OAPK2P,GAAQ9J,SACT8J,EAAUvS,EAAE,8BAEhBuS,EAAQC,aAAa5P,EAAQ,IAAI6P,OAAOrJ,GAAS5I,IAAIqG,GAAQtG,SAASqC,EAAQpC,IAAIqG,IAAS,IAAM,GAC7FxF,KAAKwG,QAAQsC,MAAMuI,gBAAkBrR,KAAKwH,aAC1CxH,KAAKsR,yBAEFJ,GAEXK,eAAgB,SAAUC,GACtB,GAAIC,GAAY,iBAAiBC,KAAKF,EAAK,GAAGG,WAAW,EACzD,QACIC,YAAa,SACbC,sBAAuB,WACvBC,sBAAuB,WACvBC,qBAAsB,UACtBC,cAAe,UACfC,UAAW,MACXC,YAAa,SACfT,IAENrI,qBAAsB,SAAU4B,GAAV,GAIdwG,GACArD,CAJJ,KAAInO,KAAKqQ,SAKT,MAFImB,GAAO7S,EAAEqM,EAAEqC,QAAQrF,QAAQ,oBAAoBJ,KAAK,WACpDuG,EAASnO,KAAKuR,eAAeC,GAC7BrD,GACAnD,EAAEiG,iBACFjR,KAAKmO,MACE,GAHX,GAMJgE,QAAS,WAAA,GACDjR,GAAOlB,KACPoS,EAASzT,EAAEgF,GAASnC,OAAO,WAAA,GACvB6Q,GAAM1T,EAAEqB,MACRsS,EAASpR,EAAKqR,QAAQF,GACtB7L,EAAU8L,GAAUA,EAAO9L,OAC/B,OAAOA,IAAWA,EAAQsC,OAAStC,EAAQuB,SAAWvB,EAAQe,WAAarG,EAAKsF,QAAQe,UAAY8K,EAAIvK,GAAGxD,KAC5GkO,KAAK,SAAUC,EAAGC,GACjB,OAAQ/T,EAAE8T,GAAGtT,IAAI,WAAaR,EAAE+T,GAAGvT,IAAI,WAG3C,OADA+B,GAAO,KACAkR,GAEXG,QAAS,SAAUvT,GAAV,GACD4H,GAAU5H,EAAQ4J,SAAS9E,GAC3B6O,EAASnQ,EAAMmH,eAAe/C,EAClC,OAAI+L,GACOA,EAEJ/T,GAEXgU,OAAQ,WACJ,GAAsIC,GAAQC,EAA1I5R,EAAOlB,KAAM2G,EAAWzF,EAAKsF,QAAQG,SAAUpF,EAAUL,EAAKK,QAASwR,EAAiBpU,EAAE8D,QAAS3B,EAAY,EAAGC,EAAa,CACnI,OAAIG,GAAKsF,QAAQ+I,YACNrO,GAEPA,EAAKsF,QAAQuD,SAAW7I,EAAK2K,WAC7B3K,EAAK8I,MAEJ9I,EAAKsF,QAAQuD,SACdjJ,EAAYiS,EAAejS,YAC3BC,EAAagS,EAAehS,cAE5Bf,KAAKwH,cAAgBtG,EAAKsF,QAAQuD,QAClC8I,EAAS7S,KAAKqI,QAAUrI,KAAKuI,OAASvI,KAAKqI,QAAU,EACrDyK,EAAU9S,KAAKsI,SAAWtI,KAAKwI,QAAUxI,KAAKsI,SAAW,IAEzDpH,EAAK8R,mBAAoB,EACzBF,EAAU/R,EAAapB,KAAKC,IAAI,GAAImT,EAAe5H,QAAU5J,EAAQ4J,SAAW,GAChF0H,EAAS/R,EAAYnB,KAAKC,IAAI,GAAImT,EAAe3H,SAAW7J,EAAQ6J,SAAWrM,EAAMwC,EAAS,eAAiB,IAEnHA,EAAQpC,KACJuB,KAAMoS,EACNtS,IAAKqS,IAETlM,EAASnG,IAAMqS,EACflM,EAASjG,KAAOoS,EACT5R,IAEXiH,MAAO,SAAUA,GACb,GAAiB9I,GAA6I4T,EAAgBvM,EAASD,EAAnLvF,EAAOlB,KAAakT,GAAU,EAAM3R,EAAUL,EAAKK,QAAS4R,EAAW5R,EAAQqH,SAAS/E,GAAkBuP,EAAeD,EAASvK,SAAShF,EAC/I,OAAKyP,WAAUjM,QAGXzI,EAAEmE,cAAcqF,IAChB9I,EAA8B,IAAf8I,EAAMmL,KAAuBnL,EAAMmL,KAAO,GACzDJ,EAAU/K,EAAM+K,WAAY,GAE5B7T,EAAQ8I,EAER9I,KAAU,GACVkC,EAAQ0G,SAAS,sBACjBkL,EAAStL,WAEJsL,EAAS/L,OAKVgM,EAAa/E,KAAK6E,EAAU1Q,EAAM+Q,WAAWlU,GAASA,IAJtDkC,EAAQiS,QAAQ7F,GAAUI,UAAW5F,MAAO+K,EAAU1Q,EAAM+Q,WAAWlU,GAASA,KAChF6B,EAAK4M,WACLqF,EAAW5R,EAAQqH,SAAS/E,IAIhC4C,EAAalF,EAAQpC,IAAI,cACzBuH,EAAUnF,EAAQpC,IAAI,WAClBsH,IAAelC,GACfhD,EAAQpC,KAAMuH,QAAS,KACvBuM,EAAiB/T,SAASiE,EAAYgQ,GAAW,IACjD5R,EAAQpC,KAAMuH,QAASA,MAEvBnF,EAAQpC,KACJsH,WAAYlC,EACZmC,QAAS,KAEbuM,EAAiB/T,SAASiE,EAAYgQ,GAAW,IACjD5R,EAAQpC,KACJsH,WAAYA,EACZC,QAASA,KAGjBnF,EAAQpC,IAAI,cAAe8T,GAC3BE,EAAShU,IAAI,cAAe8T,IAEhC/R,EAAKsF,QAAQ2B,MAAQ9I,EACd6B,GAxCIkS,EAAa/E,QA0C5BzH,QAAS,SAAUyH,EAAM7E,GACrB,GAAI5C,GAAU5G,KAAKuB,QAAQqH,SAAS9E,GAAiB2P,EAAkB7M,EAAQgC,SAAS,uBAExF,OADAhC,GAAU6M,EAAgB,GAAKA,EAAkB7M,EAC5C/H,EAAQwP,IAGbrO,KAAK0T,QAAQ,UAAW,WACpB,OAASC,SAAU/M,EAAQgC,cAE/BpG,EAAM4K,QAAQpN,KAAKhB,QAAQ4J,YAC3BhC,EAAQgN,QAAQvF,KAAKA,GACrBrO,KAAK0T,QAAQ,UAAW,WAAA,GAEX7J,GADL4I,IACJ,KAAS5I,EAAIjD,EAAQQ,SAAUyC,GAAK,GAChC4I,EAAEoB,MAAOC,SAAUtK,GAEvB,QACImK,SAAU/M,EAAQgC,WAClBY,KAAMiJ,KAGPzS,MAjBI4G,EAAQyH,QAmBvBzB,KAAM,WAAA,GACgKsE,GAAS6C,EAgB3JC,EAhBZ9S,EAAOlB,KAAMuB,EAAUL,EAAKK,QAASiF,EAAUtF,EAAKsF,QAASyN,EAAcjU,KAAKyM,kBAAkB,QAASyH,EAAiB3S,EAAQqH,SAAS9E,GAA8CqQ,EAAqBnU,KAAKwH,cAAgBtG,EAAK2K,UAAWuI,EAAMD,EAAqBnU,KAAKwH,YAAc7I,EAAE+K,SA+CzS,OA9CKxI,GAAKyJ,QAAQlG,KACVvD,EAAKmP,UACL9O,EAAQ8S,WAAU,GAAM,GAE5BnT,EAAKmP,UAAW,EAChBnP,EAAKyH,UACDnC,EAAQ8I,WACRpO,EAAKlC,QAAQyR,QAEjBjK,EAAQuB,SAAU,EACdvB,EAAQsC,QACRiL,IAAuB7S,EAAKiR,UAAU/K,OACtC8J,EAAUhQ,EAAK6H,SAASgL,GACxB7C,EAAQmD,WAAU,GAAM,GACpBJ,EAAY9E,UAAY3M,EAAMqK,QAAQyH,OAASP,GAC3CC,EAAYxR,EAAM+R,GAAGrD,GAASsD,SAClCR,EAAU7E,SAAS8E,EAAY9E,UAAY,GAC3C6E,EAAUS,SAAS,IACnBT,EAAUU,QAEVxD,EAAQ/R,IAAI,UAAW,IAE3B+R,EAAQyD,OACRhW,EAAE8D,QAAQwG,GAAG,QAAUvF,EAAU,WACzBwQ,EAAe1K,KAAK,aAAe7K,EAAE+K,SAAS3G,eAAeiF,QAAQkM,GAAgB9M,QACrFlG,EAAKlC,QAAQyR,WAIpBlP,EAAQuG,GAAGxD,KACZ4P,EAAe/U,IAAImG,EAAUf,GAC7BhD,EAAQoT,OAAON,YAAYO,cACvB/H,QAASoH,EAAYpH,QACrBsC,SAAU8E,EAAY9E,SACtB0F,SAAUhT,EAAM7B,KAAK8U,UAAW9U,UAIxCwG,EAAQ+I,cACRrO,EAAK6T,oBAAsBX,EAAItT,YAC/BI,EAAK8T,qBAAuBZ,EAAIrT,aAChCG,EAAKoQ,0BAELtR,KAAKwG,QAAQuD,SAAW/J,KAAK6L,WAC7B7L,KAAKgK,MAEF9I,GAEX4T,UAAW,WACP,GAAItG,GAAaxO,KAAKwG,QAAQgI,cAAe,CACzCxO,MAAKwG,QAAQ8I,WACbtP,KAAKhB,QAAQyR,QAEjBzQ,KAAKhB,QAAQG,IAAImG,EAAUkJ,EAAa,GAAK,UAC7ChM,EAAMwK,OAAOhN,KAAKhB,QAAQ4J,YAC1B5I,KAAK2K,QAAQjG,IAEjBuQ,eAAgB,SAAUC,GAAV,GAQAlB,GAPRmB,EAASnV,KAAKmS,UACd3L,EAAUxG,KAAKwG,QACf4O,EAAc5O,EAAQsC,QAAUqM,EAAO/N,OACvC8J,EAAU1K,EAAQsC,MAAQ9I,KAAK+I,UAAS,GAAQpK,EAAEC,GAClDyW,EAAcrV,KAAKyM,kBAAkB,QACrC2I,KACKF,GAAqBG,EAAYlG,UAAY3M,EAAMqK,QAAQyH,MACxDN,EAAYxR,EAAM+R,GAAGrD,GAASoE,UAClCtB,EAAU7E,SAASkG,EAAYlG,UAAY,GAC3C6E,EAAUuB,WAAW,IACrBvB,EAAUU,QAEV1U,KAAK+I,UAAS,GAAOlB,SAErBrB,EAAQsC,MAAMuI,eACdrR,KAAK8O,4BAEFqG,EAAO/N,SACdpH,KAAKuS,QAAQ4C,EAAOK,QAAQzM,UAAS,GACjCvC,EAAQsC,MAAMuI,eACdrR,KAAKsR,2BAIjBlB,OAAQ,SAAUqF,GACd,GAAmRC,GAA/QxU,EAAOlB,KAAMuB,EAAUL,EAAKK,QAASiF,EAAUtF,EAAKsF,QAASyN,EAAcjU,KAAKyM,kBAAkB,QAAS4I,EAAcrV,KAAKyM,kBAAkB,SAAU0H,EAAqBnU,KAAKwH,cAAgBtG,EAAK2K,UAAWuI,EAAMD,EAAqBnU,KAAKwH,YAAc7I,EAAE+K,SACpQxI,GAAKmP,WAGTqF,EAAmBxU,EAAKyJ,QAAQ/F,GAAS+Q,eAAgBF,IACzDvU,EAAKmP,UAAYqF,EACbnU,EAAQuG,GAAGxD,KAAaoR,IACxBlP,EAAQuB,SAAU,EAClBpJ,EAAEgF,GAASL,KAAK,SAAUuG,EAAG7K,GACzB,GAAIkV,GAAiBvV,EAAEK,GAAS4J,SAAS9E,EACrC9E,IAAWuC,GAAW2S,EAAetM,KAAK,MAAQ3D,GAAemD,OAAS,GAC1E8M,EAAetL,SAAS5E,GAAU6D,WAG1C7H,KAAKiV,iBACL1T,EAAQ8S,YAAYO,cAChB/H,QAASwI,EAAYxI,SAAWoH,EAAYpH,QAC5C+I,QAASP,EAAYO,WAAY,EACjCzG,SAAUkG,EAAYlG,SACtB0F,SAAUhT,EAAM7B,KAAK6V,YAAa7V,QAEtCrB,EAAE8D,QAAQ0K,IAAIzJ,IAEdxC,EAAKsF,QAAQ+I,cACbrO,EAAK4N,2BACD5N,EAAK6T,qBAAuB7T,EAAK6T,oBAAsB,GACvDX,EAAItT,UAAUI,EAAK6T,qBAEnB7T,EAAK8T,sBAAwB9T,EAAK8T,qBAAuB,GACzDZ,EAAIrT,WAAWG,EAAK8T,yBAIhCa,YAAa,WAAA,GAKDC,GAJJ5U,EAAOlB,IACXkB,GAAKK,QAAQ2K,OAAO/M,IAAI,UAAW,IACnC+B,EAAKyJ,QAAQhG,GACTzD,EAAKsF,QAAQsC,QACTgN,EAAY5U,EAAKqR,QAAQrR,EAAKiR,UAAUqD,QACxCM,GACAA,EAAUnN,YAItBmE,MAAO,WAEH,MADA9M,MAAKoQ,QAAO,GACLpQ,MAEX+V,YAAa,SAAU/W,GACnB,MAAOL,GAAEK,GAAS8I,GAAGjC,GAAmB,IAAMA,GAAmB,sBAErEmQ,aAAc,SAAU3I,GACpB,GAAI4I,GAASlT,IAAiB/D,EAAUgB,KAAKhB,OAC7C,OAAOgB,MAAKwG,QAAQ8I,YAAc3Q,EAAEsX,GAAQnO,GAAG9I,KAAagB,KAAK+V,YAAY1I,MAAarO,EAAQ4I,KAAKqO,GAAQ7O,SAAWpI,EAAQ4I,KAAKyF,GAAQjG,SAEnJuB,QAAS,SAAUqC,GAAV,GA0BGlK,GAAuFoV,EAzB3FhV,EAAOlB,KAAMuB,EAAUL,EAAKK,QAAS4U,EAAgB5U,EAAQ,GAAI4S,EAAqBjT,EAAKsG,cAAgBtG,EAAK2K,UAAWuK,GAAU7U,EAAQpC,IAAIqG,IAAS6Q,EAAiBD,EAAQ/I,EAASrC,GAAKA,EAAEqC,QAAU,IAmCjN,OAlCA1O,GAAEgF,GAASL,KAAK,SAAUuG,EAAG7K,GACzB,GAAIsX,GAAe3X,EAAEK,GAAUuX,EAAYD,EAAanX,IAAIqG,IAAS0O,EAAiBoC,EAAa1N,SAAS9E,EACvGrE,OAAM8W,KACPH,EAASzW,KAAKC,KAAK2W,EAAWH,IAElClC,EAAe1K,KAAK,UAAWxK,GAAWmX,GACtCnX,GAAWmX,GAAiBjC,EAAetM,KAAK,MAAQ3D,GAAemD,OAAS,GAChF8M,EAAexG,OAAOC,GAAUuD,aAGnC3P,EAAQ,GAAGiV,MAAMJ,QAAUC,EAAiBD,IAC7C7U,EAAQpC,IAAIqG,GAAQ4Q,EAAS,GAEjClV,EAAKlC,QAAQ4I,KAAK,gBAAgBC,SAC9B3G,EAAK8U,aAAa3I,KACdnM,EAAKsO,cACLtO,EAAKK,QAAQkP,QACN9R,EAAE0O,GAAQvF,GAAG9D,GACpByS,WAAW,WACPvV,EAAKlC,QAAQyR,UAGjBvP,EAAKlC,QAAQyR,QAEb3P,EAAYqT,EAAqBjT,EAAKsG,YAAY1G,YAAcnC,EAAE8D,QAAQ3B,YAAaoV,EAAYhX,SAASqC,EAAQoF,WAAWnG,IAAK,KACnIU,EAAKsF,QAAQuD,QAAUmM,EAAY,GAAKA,EAAYpV,IACjDA,EAAY,EACZnC,EAAE8D,QAAQ3B,UAAUoV,GAEpB3U,EAAQpC,IAAI,MAAO2B,KAI/BS,EAAU,KACHL,GAEXoM,mBAAoB,WAChB,MAAItN,MAAKqQ,SACErQ,KAEJA,KAAKA,KAAKwG,QAAQ+I,YAAc,UAAY,eAEvDV,QAAS,WAAA,GAKD6H,GAJAxV,EAAOlB,KACPwG,EAAUtF,EAAKsF,QACfyF,EAAYzF,EAAQyF,UACpB0K,EAAiBzV,EAAKyV,eAEtB3I,EAAY9M,EAAKsG,cAAgBtG,EAAK2K,UAAY3K,EAAKsG,YAAc7I,EAAE+K,SAC3E,OAAKlD,GAAQ+I,aAAgB/I,EAAQgJ,aAGjCvD,GAAaA,GAAanM,EAAAA,GAC1BoB,EAAKK,QAAQpC,IAAI,aAAc8M,GAE/B0K,IAAmBnQ,EAAQ+I,cAC3BoH,EAAevL,OAAShM,EAAUuX,EAAevL,OAAQlK,EAAKsF,QAAQyF,UAAW/K,EAAKsF,QAAQ6E,WAC9FqL,EAAoBlQ,EAAQG,SAASnG,IAAMtB,SAASyX,EAAevL,OAAQ,IAAMlK,EAAKqH,OAClFmO,IACAlQ,EAAQG,SAASnG,IAAMpB,EAAUoH,EAAQG,SAASnG,IAAKU,EAAKmH,OAAQnH,EAAKqH,OAASrJ,SAASyX,EAAevL,OAAQ,KAClH/H,EAAOsT,GACHjW,KAAM8F,EAAQG,SAASjG,KACvBF,IAAKgG,EAAQG,SAASnG,QAIlCU,EAAKK,QAAQpC,KACTwH,SAAUH,EAAQuD,OAAS,QAAU,WACrCrJ,KAAMiW,EAAejW,KACrBF,IAAKmW,EAAenW,IACpB2K,MAAOwL,EAAexL,MACtBC,OAAQuL,EAAevL,SACxBF,YAAY7G,GAAgBuD,KAAK,sCAAsC+M,OAAOiC,MAAMhP,KAAK,0CAA0ChH,SAASiH,SAAS+O,MAAMA,MAAMhP,KAAKnC,IAAmB7E,SAAS+T,OAAOiC,MAAMA,MAAMhP,KAAKhC,IAAWhF,SAAS+T,OAC7OnO,EAAQ+I,YACRrO,EAAKK,QAAQqG,KAAK,wBAAwBhH,SAAS6P,QAC5CjK,EAAQgJ,aACftO,EAAKK,QAAQqG,KAAK,wBAAwBhH,SAAS6P,QAEvDvP,EAAKsF,QAAQ2E,MAAQwL,EAAexL,MACpCjK,EAAKsF,QAAQ4E,OAASuL,EAAevL,OAChClK,EAAKsF,QAAQsC,MAAMuI,eACpBnQ,EAAK4N,2BAEL5N,EAAK6T,qBAAuB7T,EAAK6T,oBAAsB,GACvD/G,EAAUlN,UAAUI,EAAK6T,qBAEzB7T,EAAK8T,sBAAwB9T,EAAK8T,qBAAuB,GACzDhH,EAAUjN,WAAWG,EAAK8T,sBAE9BxO,EAAQ+I,YAAc/I,EAAQgJ,aAAc,EAC5CtO,EAAKK,QAAQsV,WAAW,YACxB3V,EAAKK,QAAQsV,WAAW,oBACxB3V,EAAK8L,SACE9L,GA3CIA,GA6Cf4V,cAAe,SAAUC,EAAUC,GAC/B,GAAI9V,GAAOlB,KAAMuB,EAAUL,EAAKK,QAASiV,EAAQjV,EAAQ,GAAGiV,MAAOhQ,EAAUtF,EAAKsF,OAClF,OAAIA,GAAQ+I,aAAe/I,EAAQgJ,YACxBtO,GAEXA,EAAKyV,gBACDxL,MAAOqL,EAAMrL,MACbC,OAAQoL,EAAMpL,QAElB7J,EAAQqH,SAASnH,GAAsByK,OAAO0K,MAAMhO,SAAS/E,GAAiB+D,KAAKnC,IAAmB7E,SAASsL,OAAO+K,GAAG,GAAGC,OAAOvJ,GAAUQ,QAASC,KAAM,oBAC5J4I,EAAS1P,KAAKpG,GACdA,EAAKK,QAAQqH,SAAS/E,GAAiB+D,KAAKhC,IAAWhF,SAASwQ,OAAoB,aAAb2F,GACvE7V,EAAKyJ,QAAQoM,GACbxV,EAAQqG,KAAK,uBAAuBhH,SAAS6P,QACtCvP,IAEXwP,SAAU,WAkBN,MAjBA1Q,MAAK8W,cAAc,WAAY,WAC3B,GAAI5V,GAAOlB,KAAMuB,EAAUL,EAAKK,QAAS4S,EAAqBnU,KAAKwH,cAAgBtG,EAAK2K,UAAWlF,EAAWpF,EAAQoF,WAAYyN,EAAMzV,EAAE+K,SAC1IrG,GAAOnC,EAAKyV,gBACRjW,KAAMiG,EAASjG,MAAQyT,EAAqBnU,KAAKwH,YAAYzG,aAAe,GAC5EP,IAAKmG,EAASnG,KAAO2T,EAAqBnU,KAAKwH,YAAY1G,YAAc,KAE7Ed,KAAK+U,oBAAsBZ,EAAqBnU,KAAKwH,YAAY1G,YAAcsT,EAAItT,YACnFd,KAAKgV,qBAAuBb,EAAqBnU,KAAKwH,YAAYzG,aAAeqT,EAAIrT,aACrFG,EAAKoQ,yBACL/P,EAAQpC,KACJqB,IAAK2T,EAAqBnU,KAAKwH,YAAY1G,YAAc,EACzDJ,KAAMyT,EAAqBnU,KAAKwH,YAAYzG,aAAe,EAC3D4F,SAAUwN,EAAqB,WAAa,UAC7ClM,SAAS5D,GACZnD,EAAKsF,QAAQ+I,aAAc,EAC3BrO,EAAKqJ,sBAEFvK,MAEXsR,uBAAwB,WAAA,GAYhB6F,GAGAC,EAdAlW,EAAOlB,KACPwH,EAActG,EAAKsG,WACvB,OAAIA,KAAgBtG,EAAK2K,WACrB3K,EAAKmW,mBAAmB7P,GACxBA,EAAYrI,IAAImG,EAAUf,GAC1BrD,EAAKK,QAAQpC,KACT4M,SAAUvE,EAAY2E,aACtBd,UAAW7D,EAAY4E,gBAE3B,IAEA+K,EAAQxY,EAAE,QACduC,EAAKmW,mBAAmBF,GACxBA,EAAMhY,IAAImG,EAAUf,GAChB6S,EAAQzY,EAAE,QACduC,EAAKmW,mBAAmBD,GACxBA,EAAMjY,IAAImG,EAAUf,GALhB4S,IAORrI,yBAA0B,WAAA,GAClB5N,GAAOlB,KACPwH,EAActG,EAAKsG,WACvB,OAAIA,KAAgBtG,EAAK2K,WACrB3K,EAAKoW,qBAAqB9P,GAC1BtG,EAAKK,QAAQpC,KACT4M,SAAUvE,EAAY2D,MACtBE,UAAW7D,EAAY4D,SAE3B,IAEJlK,EAAKoW,qBAAqB3Y,EAAE+K,SAAS6N,OACrCrW,EAAKoW,qBAAqB3Y,EAAE,SAD5BuC,IAGJmW,mBAAoB,SAAUG,GAC1B,IAAIxX,KAAKyX,kBAAkBD,GAA3B,CAGA,GAAIE,GAAeF,EAASG,IAAI,GAAGnB,MAAMoB,QACb,iBAAjBF,IACPF,EAAShO,KAAKjE,EAAqBmS,KAG3CD,kBAAmB,SAAUD,GACzB,MAAqD,gBAAvCA,GAAShO,KAAKjE,IAEhC+R,qBAAsB,SAAUE,GAC5B,GAAIE,GAAeF,EAAShO,KAAKjE,EACZ,QAAjBmS,GAAyBA,IAAiB9Y,GAC1C4Y,EAASrY,IAAImG,EAAUoS,GACvBF,EAASK,WAAWtS,IAEpBiS,EAASrY,IAAImG,EAAU,KAG/BiK,YAAa,WACT,MAAOvP,MAAKwG,QAAQ+I,aAExBqB,SAAU,WAaN,MAZA5Q,MAAK8W,cAAc,WAAY,WAC3B,GAAI5V,GAAOlB,IACXkB,GAAKK,QAAQpC,KACTiM,OAAQ,GACRa,UAAW,KAEf/K,EAAKlC,QAAQkN,OACbhL,EAAKsF,QAAQgJ,aAAc,IAE/BxP,KAAKuB,QAAQ0I,KAAK,WAAY,GAC9BjK,KAAKuB,QAAQ0I,KAAK,mBAAoBjK,KAAKhB,QAAQiL,KAAK,qBACxDjK,KAAK8L,oBACE9L,MAEXwP,YAAa,WACT,MAAOxP,MAAKwG,QAAQgJ,aAExBxF,IAAK,WACD,GAAI9I,GAAOlB,KAAM8X,EAAMnZ,EAAE8D,QAASlB,EAAUL,EAAKK,QAASiF,EAAUtF,EAAKsF,QAASG,EAAWH,EAAQG,SAAUnG,EAAMR,KAAKwH,YAAcrH,EAAYoB,EAAQ,IAAIf,IAAMzB,EAAMiB,KAAKwH,YAAa,kBAAoBzI,EAAMwC,EAAS,OAAQb,EAAOV,KAAKwH,YAAcrH,EAAYoB,EAAQ,IAAIb,KAAO3B,EAAMiB,KAAKwH,YAAa,mBAAqBzI,EAAMwC,EAAS,OACzVL,GAAKsF,QAAQ+I,cACd5I,EAASnG,IAAMA,EACfmG,EAASjG,KAAOA,GACZQ,EAAK8R,mBAAuBhT,KAAKwH,aAAoD,UAArCxH,KAAKwH,YAAYrI,IAAI,cACrEwH,EAASnG,KAAOsX,EAAIhX,YACpB6F,EAASjG,MAAQoX,EAAI/W,aACrBG,EAAK8R,mBAAoB,GAE7BzR,EAAQpC,IAAIkE,EAAOsD,GAAYA,SAAU,WACzCpF,EAAQqH,SAAS/E,GAAiB+D,KAAKlC,IAAMuC,SAAS,aAAaiD,YAAY,WAC/EhK,EAAK2K,WAAY,EACjB3K,EAAKsF,QAAQuD,QAAS,EAClB/J,KAAKwH,cACLhB,EAAQuF,SAAWvF,EAAQ6E,UAAYvL,EAAAA,EACvCyB,EAAQpC,KACJ4M,SAAU,GACVV,UAAW,QAK3BkF,MAAO,WACH,GAAIrP,GAAOlB,KAAM8X,EAAMnZ,EAAE8D,QAASlB,EAAUL,EAAKK,QAASiF,EAAUtF,EAAKsF,QAASG,EAAWzF,EAAKsF,QAAQG,SAAUa,EAActG,EAAKsG,YAAahH,EAAMtB,SAASqC,EAAQpC,IAAI,OAAQ,IAAM2Y,EAAIhX,YAAaJ,EAAOxB,SAASqC,EAAQpC,IAAI,QAAS,IAAM2Y,EAAI/W,YACxPG,GAAKsF,QAAQ+I,cACdrO,EAAK2K,WAAY,EACjB3K,EAAK8R,mBAAoB,EACzB9R,EAAKsF,QAAQuD,QAAS,EAClBvC,IACAtG,EAAK4K,oBACLtF,EAAQuF,SAAWpM,KAAKE,IAAI2H,EAAY2D,MAAO3E,EAAQuF,UACvDvF,EAAQ6E,UAAY1L,KAAKE,IAAI2H,EAAY4D,OAASrM,EAAMwC,EAAS,eAAgBiF,EAAQ6E,WACzF9J,EAAQpC,KACJ4M,SAAUvF,EAAQuF,SAClBV,UAAW7E,EAAQ6E,YAGnB7K,EADAA,EAAMgH,EAAYb,SAASnG,IACrBU,EAAKmH,OACJ7H,EAAMgH,EAAYb,SAASnG,IAAMgH,EAAY4D,OAC9ClK,EAAKqH,OAEL/H,EAAMgH,EAAY1G,aAAe0G,EAAYb,SAASnG,IAAMzB,EAAMyI,EAAa,qBAGrF9G,EADAA,EAAO8G,EAAYb,SAASjG,KACrBQ,EAAKoH,QACL5H,EAAO8G,EAAYb,SAASjG,KAAO8G,EAAY2D,MAC/CjK,EAAKsH,QAEL9H,EAAO8G,EAAYzG,cAAgByG,EAAYb,SAASjG,KAAO3B,EAAMyI,EAAa,uBAGjGb,EAASnG,IAAMpB,EAAUoB,EAAKU,EAAKmH,OAAQnH,EAAKqH,QAChD5B,EAASjG,KAAOtB,EAAUsB,EAAMQ,EAAKoH,QAASpH,EAAKsH,SACnDjH,EAAQpC,IAAIkE,EAAOsD,GAAYA,SAAU,MACzCpF,EAAQqH,SAAS/E,GAAiB+D,KAAKjC,IAAQsC,SAAS,WAAWiD,YAAY,eAGvFX,kBAAmB,WAAA,GACiJuF,GAAGC,EAI/JrE,EACAC,EACAC,EANA1K,EAAOlB,KAAMuB,EAAUL,EAAKK,QAASN,EAAMtC,EAAE8D,QAASsV,EAAYvV,EAAM8J,QAAQyL,YAAatM,EAAgD,eAA7BlK,EAAQpC,IAAI,aAC3H+B,GAAKsF,QAAQ+I,cAGd7D,EAAgBD,EAAmB1M,EAAMwC,EAAS,qBAAuBxC,EAAMwC,EAAS,sBAAwB,EAChHoK,EAAgBF,EAAmB1M,EAAMwC,EAAS,oBAAsBxC,EAAMwC,EAAS,uBAAyB,EAChHqK,EAAaH,EAAmB1M,EAAMwC,EAAS,eAAiB,EAChEL,EAAKsG,cAAgBtG,EAAK2K,WAC1BiE,EAAI5O,EAAKsG,YAAY2E,aAAeT,EACpCqE,EAAI7O,EAAKsG,YAAY4E,eAAiBT,EAAgBC,KAEtDkE,EAAI7O,EAAIkK,QAAU4M,EAAYrM,EAC9BqE,EAAI9O,EAAImK,SAAW2M,GAAapM,EAAgBC,IAEpDrK,EAAQpC,KACJgM,MAAO2E,EACP1E,OAAQ2E,IAEZ7O,EAAKsF,QAAQ2E,MAAQ2E,EACrB5O,EAAKsF,QAAQ4E,OAAS2E,EACtB7O,EAAK8L,WAETtE,QAAS,SAAUlC,GACf,GAAwEwR,GAAQC,EAAYtQ,EAAxFzG,EAAOlB,KAAMkY,EAAchX,EAAKsF,QAASxH,EAAUL,EAAEuC,EAAKlC,QA6B9D,OA5BK8D,GAAc0D,KACfA,GAAYmB,IAAKnB,IAErBA,EAAUnD,KAAW6U,EAAYtR,QAASJ,GAC1CyR,EAAapZ,EAAQqZ,EAAYF,QAAUE,EAAYF,OAASxR,EAAQwR,OACxErQ,EAAMnB,EAAQmB,IACVA,GACK9I,EAAQoZ,KACTA,GAAchS,GAAW0B,IAExBsQ,GAGDD,EAAShZ,EAAQ4I,KAAK,IAAM3D,GAAe,GACvC+T,EACAA,EAAOG,IAAMxQ,GAAOqQ,EAAOG,IAE3BnZ,EAAQqP,KAAKV,GAAUyK,aAAa/U,KAAW6U,GAAetR,QAASJ,MAE3ExH,EAAQ4I,KAAK,IAAM3D,GAAeoU,OAAO,OAAS5U,GAAIwF,GAAG,OAASxF,EAAI5B,EAAM7B,KAAKsY,gBAAiBtY,QARlGkB,EAAKqX,aAAa/R,KAWlBA,EAAQjD,UACRrC,EAAK0F,QAAQrD,EAASiD,EAAQjD,eAElCrC,EAAKyJ,QAAQ9F,IAEjB7F,EAAQwZ,YAAY,2BAA4BP,GACzC/W,GAEXoX,gBAAiB,WACbtY,KAAK2K,QAAQ9F,IAEjB4T,cAAe,WACXC,aAAa1Y,KAAK2Y,qBAClB3Y,KAAKuB,QAAQqG,KAAK9B,IAAaoF,YAAYhH,IAE/C0U,WAAY,SAAUC,EAAKC,GACvB9Y,KAAK2K,QAAQtF,GACTyT,OAAQA,EACRD,IAAKA,KAGbE,aAAc,SAAUC,GACpB,MAAO,UAAUxP,GACb,GAAI6E,GAAO7E,CACPwP,KACA3K,EAAO9K,EAASyV,GAAiBxP,QAErCxJ,KAAK4G,QAAQyH,EAAM7E,GACnBxJ,KAAKhB,QAAQia,KAAK,YAAa,GAC/BjZ,KAAK2K,QAAQ9F,KAGrBqU,aAAc,WACVlZ,KAAKuB,QAAQqG,KAAK9B,IAAamC,SAAS/D,IAE5CqU,aAAc,SAAU/R,GACpBxG,KAAK2Y,oBAAsBlC,WAAW5U,EAAM7B,KAAKkZ,aAAclZ,MAAO,KACtErB,EAAEwa,KAAK9V,GACHpD,KAAM,MACNmZ,SAAU,OACVC,OAAO,EACPC,MAAOzX,EAAM7B,KAAK4Y,WAAY5Y,MAC9B6U,SAAUhT,EAAM7B,KAAKyY,cAAezY,MACpCuZ,QAAS1X,EAAM7B,KAAK+Y,aAAavS,EAAQjD,UAAWvD,OACrDwG,KAEPgT,SAAU,WACFxZ,KAAKkN,UACLlN,KAAKkN,SAASE,UAEdpN,KAAK6N,UACL7N,KAAK6N,SAAST,UAElBpN,KAAKuB,QAAQ4L,IAAI1J,GAAImF,SAAS9E,GAAgBqJ,IAAI1J,GAAImT,MAAMhP,KAAK,uCAAuCuF,IAAI1J,GAC5G9E,EAAE8D,QAAQ0K,IAAI,SAAW1J,EAAKzD,KAAKwK,SACnC7L,EAAE8D,QAAQ0K,IAAIzJ,GACd/E,EAAE8D,QAAQ0K,IAAI1J,GACdiV,aAAa1Y,KAAK2Y,qBAClBjW,EAAO2E,GAAG+F,QAAQ9F,KAAKtH,MACvBA,KAAKqY,OAAOzZ,GACZ4D,EAAM4K,QAAQpN,KAAKuB,SACnBvB,KAAKiV,gBAAe,IAExB7H,QAAS,WACLpN,KAAKwZ,WACLxZ,KAAKuB,QAAQqS,QAAQ/L,SACrB7H,KAAKuB,QAAUvB,KAAKuH,SAAWvH,KAAKhB,QAAUL,KAElDuJ,cAAe,WACX,GAAwDuR,GAAqBlY,EAAzEmY,EAAc1Z,KAAKhB,QAASwH,EAAUxG,KAAKwG,QAAuCmT,EAAQnX,EAAM8J,QAAQqN,MAAMD,EAC9GlT,GAAQgI,cAAe,GACvBkL,EAAYva,IAAI,WAAY,UAEhCoC,EAAU5C,EAAEgP,GAAUpM,QAAQiF,IAC9BiT,EAAsBC,EAAY9R,KAAK,0BAA0BsG,IAAI,WACjE,GAAIiK,GAAMnY,KAAK4Z,aAAa,MAE5B,OADA5Z,MAAKmY,IAAM,GACJA,IAEX5W,EAAQiX,YAAY,QAASmB,GAAOjM,OAAOgM,GAAa9R,KAAK,0BAA0BtE,KAAK,SAAUkK,GAClGxN,KAAKmY,IAAMsB,EAAoBjM,KAE/BxN,KAAKwH,YACLxH,KAAKwH,YAAYgM,QAAQjS,GAClBvB,KAAKuH,UACZhG,EAAQgG,SAASvH,KAAKuH,UAE1BhG,EAAQqG,KAAK,mBAAmBzI,IAAIwa,EAAQ,OAAS,QAAS1W,EAAW1B,EAAQqG,KAAK,sBAAwB,IAC9G8R,EAAYva,IAAI,aAAc,IAAIwV,OAClC+E,EAAY9R,KAAK,sBAAsBtE,KAAK,WACxC,GAAIuW,GAASlb,EAAEqB,MAAMwJ,KAAK,cACtBqQ,IACAA,EAAOnR,YAGfnH,EAAUmY,EAAc,QAzrCkU/L,IA6rC9VpM,QAASgC,EAAS,qCAClB4K,OAAQ5K,EAAS,yKACjBwK,SAAUxK,EAAS,gIACnB2N,QAAS,4BACTkH,aAAc7U,EAAS,qDAA4DU,EAAgB,wFACnG2J,aAAcrK,EAAS,0DAgB3BvC,GAAe8Y,WACX5X,WAAY,WACRlC,KAAKmB,MAAMI,QAAQmM,OAAOC,GAAUuD,UAExC/O,cAAe,WACXnC,KAAKmB,MAAMI,QAAQqG,KAAK5D,GAAU6D,UAEtCjG,UAAW,SAAUoJ,GAAV,GAiBHnK,GAIIkZ,EACAC,EACAC,EAEIC,EACAC,EACAC,EA1BRlZ,EAAOlB,KACPiB,EAAMC,EAAKC,MACXI,EAAUN,EAAIM,OAClBL,GAAKE,iBAAmBH,EAAI0J,QAAQ3F,GAChC9D,EAAKE,mBAGTF,EAAKmZ,eAAiBnb,SAASqC,EAAQpC,IAAI,eAAgB,IAC3D+B,EAAKoZ,gBAAkB9X,EAAMqO,UAAUtP,EAAS,YAChDL,EAAKqZ,gBAAkBvP,EAAEC,cAAcgO,KAAK,aAAauB,QAAQ,4BAA6B,IAC9FtZ,EAAKuZ,aACDtP,MAAO5J,EAAQ4J,QACfC,OAAQ7J,EAAQ6J,UAEpBnK,EAAI6K,oBACJ5K,EAAKwZ,gBAAkBzZ,EAAIuG,YAAcvG,EAAIuG,YAAYb,SAAWnE,EAAMqO,UAAU5P,EAAIsG,SAAU,YAC9F1G,EAAeU,EAAQV,eACvBA,EAAaiH,GAAG,QAChB5G,EAAKwZ,gBAAgBla,IAAMU,EAAKwZ,gBAAgBha,KAAO,GAEnDqZ,EAAYlZ,EAAa1B,IAAI,cAC7B6a,EAAanZ,EAAa1B,IAAI,eAC9B8a,GAAajU,GAAK2U,KAAKZ,KAAe/T,GAAK2U,KAAKX,GAChDC,IACIC,EAAkB/Z,EAAYoB,EAAQ,IACtC4Y,EAAuBD,EAAgBxZ,KAAOQ,EAAKwZ,gBAAgBha,KAAOQ,EAAKoZ,gBAAgB5Z,KAC/F0Z,EAAsBF,EAAgB1Z,IAAMU,EAAKwZ,gBAAgBla,IAAMU,EAAKoZ,gBAAgB9Z,IAChGU,EAAK0Z,sBAAwBT,EAAuB,EAAIA,EAAuB,EAC/EjZ,EAAK2Z,qBAAuBT,EAAsB,EAAIA,EAAsB,EAC5ElZ,EAAKoZ,gBAAgB5Z,MAAQQ,EAAK0Z,sBAClC1Z,EAAKoZ,gBAAgB9Z,KAAOU,EAAK2Z,uBAGzCtZ,EAAQqH,SAASnH,GAAsBmI,IAAIoB,EAAEC,eAAeiB,OAC5DvN,EAAE6E,GAAMrE,IAAIqF,EAAQwG,EAAEC,cAAc9L,IAAIqF,MAE5C1C,KAAM,SAAUkJ,GAAV,GAIE9J,GAAaD,EAAkBM,EAAuBiF,EAAuBG,EAA6BsI,EAAkCyL,EAAwCJ,EAAwCG,EAAgCtG,EAAwD2G,EAA4CC,EAAmIC,EAMhepL,EAAUC,EAAWoL,EAAcC,EAAapc,EAA+Bqc,EAqBlFC,CA9BApb,MAAKoB,mBAGLF,EAAOlB,KAAMiB,EAAMC,EAAKC,MAAOI,EAAUN,EAAIM,QAASiF,EAAUvF,EAAIuF,QAASG,EAAWH,EAAQG,SAAUsI,EAAY/N,EAAKqZ,gBAAiBG,EAAkBxZ,EAAKwZ,gBAAiBJ,EAAkBpZ,EAAKoZ,gBAAiBG,EAAcvZ,EAAKuZ,YAAatG,EAAqBlT,EAAIuG,cAAgBvG,EAAI4K,UAAWiP,EAAMtY,EAAM8J,QAAQqN,MAAM1Y,EAAIuG,aAAcuT,EAAgB5G,GAAsB2G,GAAO7Z,EAAIuG,YAAY2E,aAAelL,EAAIuG,YAAY2D,MAAQ3I,EAAM8J,QAAQC,YAAc,EAAGyO,EAAe7G,GAC9e3T,IAAKS,EAAIuG,YAAY1G,YACrBJ,KAAMO,EAAIuG,YAAYzG,eAEtBP,IAAK,EACLE,KAAM,GACyC5B,EAAIa,KAAKC,IAAIoL,EAAElM,EAAEuc,SAAU,GAAIF,EAAIxb,KAAKC,IAAIoL,EAAEmQ,EAAEE,SAAU,GAC7GpM,EAAUvP,QAAQ,MAAQ,GAEtBkQ,EADA3O,EAAIuG,aAAe1I,EAAI2b,EAAYtP,OAASlK,EAAIuH,QAAUwS,EAAata,KAAOga,EAAgBha,KAAOqa,EAC1F9Z,EAAIuH,QAAUuS,EAAgBT,EAAgB5Z,KAAO+Z,EAAYtP,MAAQ6P,EAAata,KAEtF5B,EAAIwb,EAAgB5Z,KAAOga,EAAgBha,KAE1Da,EAAQ4J,MAAM/L,EAAUwQ,EAAUpJ,EAAQwF,SAAUxF,EAAQuF,YACrDkD,EAAUvP,QAAQ,MAAQ,IACjCwb,EAAcZ,EAAgB5Z,KAAO+Z,EAAYtP,MAAQuP,EAAgBha,KACzEkP,EAAWxQ,EAAU8b,EAAcpc,EAAG0H,EAAQwF,SAAUxF,EAAQuF,UAChEpF,EAASjG,KAAOwa,EAActL,EAAW8K,EAAgBha,KAAOqa,GAAiB7Z,EAAK0Z,uBAAyB,GAAKI,EAAata,KAC7HO,EAAIuG,aAAeb,EAASjG,MAAQO,EAAIqH,UACxC3B,EAASjG,KAAOO,EAAIqH,QACpBsH,EAAWxQ,EAAU8b,EAAcH,EAAgBpU,EAASjG,KAAOga,EAAgBha,KAAOsa,EAAata,KAAM8F,EAAQwF,SAAUxF,EAAQuF,WAE3IxK,EAAQpC,KACJuB,KAAMiG,EAASjG,KACfyK,MAAOyE,KAGXwL,EAAeD,EACfla,EAAIuF,QAAQuD,SACZqR,GAAgBzc,EAAE8D,QAAQ3B,aAE1BmO,EAAUvP,QAAQ,MAAQ,GAC1BmQ,EAAYuL,EAAed,EAAgB9Z,IAAMU,EAAKmZ,eAAiBK,EAAgBla,IACnF4a,EAAeX,EAAYrP,OAASlK,EAAKmZ,gBAAkBpZ,EAAIsH,OAASmS,EAAgBla,IAAMwa,EAAaxa,MAC3GqP,EAAY5O,EAAIsH,OAAS+R,EAAgB9Z,IAAMia,EAAYrP,OAAS4P,EAAaxa,KAErFe,EAAQ6J,OAAOhM,EAAUyQ,EAAWrJ,EAAQyF,UAAWzF,EAAQ6E,aACxD4D,EAAUvP,QAAQ,MAAQ,IACjCub,EAAeX,EAAgB9Z,IAAMia,EAAYrP,OAASsP,EAAgBla,IAC1EqP,EAAYzQ,EAAU6b,EAAeG,EAAc5U,EAAQyF,UAAWzF,EAAQ6E,WAC9E1E,EAASnG,IAAMya,EAAepL,EAAY6K,EAAgBla,KAAOU,EAAK2Z,sBAAwB,GAAKG,EAAaxa,IAC5GmG,EAASnG,KAAOS,EAAIoH,QAAUpH,EAAIuG,cAClCb,EAASnG,IAAMS,EAAIoH,OACnBwH,EAAYzQ,EAAU6b,EAAetU,EAASnG,IAAMka,EAAgBla,IAAMwa,EAAaxa,IAAKgG,EAAQyF,UAAWzF,EAAQ6E,YAE3H9J,EAAQpC,KACJqB,IAAKmG,EAASnG,IACd4K,OAAQyE,KAGZD,IACA3O,EAAIuF,QAAQ2E,MAAQyE,EAAW,MAE/BC,IACA5O,EAAIuF,QAAQ4E,OAASyE,EAAY,MAErC5O,EAAI+L,WAERjL,QAAS,SAAUiJ,GACf,IAAIhL,KAAKoB,iBAAT,CAGA,GAAIF,GAAOlB,KAAMiB,EAAMC,EAAKC,MAAOI,EAAUN,EAAIM,OAUjD,OATAA,GAAQqH,SAASnH,GAAsBmI,IAAIoB,EAAEC,eAAe0J,OAC5DhW,EAAE6E,GAAMrE,IAAIqF,EAAQ,IAChBvD,EAAIoJ,eACJpJ,EAAIoJ,cAAciR,QAEL,IAAbtQ,EAAEiF,SACF1O,EAAQpC,IAAI+B,EAAKoZ,iBAAiBnb,IAAI+B,EAAKuZ,aAE/CxZ,EAAI0J,QAAQzF,IACL,IAEXkI,QAAS,WACDpN,KAAKqB,YACLrB,KAAKqB,WAAW+L,UAEpBpN,KAAKqB,WAAarB,KAAKmB,MAAQ,OAiBvCiB,EAAe0X,WACXlY,UAAW,SAAUoJ,GACjB,GAAI/J,GAAMjB,KAAKmB,MAAOsG,EAAYxG,EAAIuF,QAAQiB,UAAWzI,EAAUiC,EAAIjC,QAASmI,EAAUnI,EAAQ4I,KAAK,qBAAsB8S,EAAkBlY,EAAMqO,UAAU5P,EAAIsG,SACnKvH,MAAKoB,iBAAmBH,EAAI0J,QAAQxF,KAAesC,EAC/CzH,KAAKoB,kBAAoBH,EAAIsO,gBAGjCtO,EAAIsa,sBAAwB/Y,EAAMqO,UAAU5P,EAAIM,QAAS,YACzDN,EAAIua,wBACA9a,KAAMO,EAAIuF,QAAQG,SAASjG,KAC3BF,IAAKS,EAAIuF,QAAQG,SAASnG,KAE9BS,EAAIwa,eACA/a,KAAMsK,EAAElM,EAAE4c,OAASza,EAAIsa,sBAAsB7a,KAC7CF,IAAKwK,EAAEmQ,EAAEO,OAASza,EAAIsa,sBAAsB/a,KAEhDS,EAAI6K,oBACC7K,EAAIuG,cAEDvG,EAAIqH,QADJnB,EAAQC,OAAS,EACHnE,EAAWkE,GAAWjI,SAASiI,EAAQhI,IAAI,SAAU,IAAM8D,EAAWjE,GAEtE,GAAKiE,EAAWjE,GAElCiC,EAAIqH,SAAWoS,EAAgBha,KAC/BO,EAAIoH,QAAUqS,EAAgBla,KAElCS,EAAIM,QAAQmM,OAAOC,GAAUuD,SAAStI,SAASnH,GAAsByK,OACrEvN,EAAE6E,GAAMrE,IAAIqF,EAAQwG,EAAEC,cAAc9L,IAAIqF,MAE5C1C,KAAM,SAAUkJ,GAAV,GAIEtK,GACAF,EAJAS,EAAMjB,KAAKmB,MACXwF,EAAW1F,EAAIuF,QAAQG,SACvBgV,EAAO1a,EAAIuF,QAAQiB,UAAUkU,IAG7B3b,MAAKoB,kBAAoBH,EAAIsO,gBAG5BoM,GAA+B,MAAvBA,EAAKzb,gBACdQ,EAAOsK,EAAElM,EAAE4c,OAASza,EAAIwa,cAAc/a,KAClCO,EAAIuG,cAAgBvG,EAAI4K,YACxBnL,GAAQO,EAAIuG,YAAYzG,cAE5B4F,EAASjG,KAAOtB,EAAUsB,EAAMO,EAAIqH,QAASrH,EAAIuH,UAEhDmT,GAA+B,MAAvBA,EAAKzb,gBACdM,EAAMwK,EAAEmQ,EAAEO,OAASza,EAAIwa,cAAcjb,IACjCS,EAAIuG,cAAgBvG,EAAI4K,YACxBrL,GAAOS,EAAIuG,YAAY1G,aAE3B6F,EAASnG,IAAMpB,EAAUoB,EAAKS,EAAIoH,OAAQpH,EAAIsH,SAE9C/F,EAAM8J,QAAQsP,WACdjd,EAAEsC,EAAIM,SAASpC,IAAI,YAAa,cAAgBwH,EAASjG,KAAOO,EAAIua,uBAAuB9a,MAAQ,QAAUiG,EAASnG,IAAMS,EAAIua,uBAAuBhb,KAAO,OAE9J7B,EAAEsC,EAAIM,SAASpC,IAAIwH,KAG3BkV,YAAa,WACT,GAAI5a,GAAMjB,KAAKmB,KACfF,GAAIM,QAAQqH,SAASnH,GAAsB2P,QAAQnQ,EAAIuF,QAAQgJ,aAAaoH,MAAMhP,KAAK5D,GAAU6D,SACjGlJ,EAAE6E,GAAMrE,IAAIqF,EAAQ,KAExBlC,WAAY,SAAU0I,GACdhL,KAAKoB,mBAGTpB,KAAK6b,cACL7Q,EAAEC,cAAcjD,QAAQrE,GAASxE,IAAIa,KAAKmB,MAAMoa,yBAEpDxZ,QAAS,WACL,GAAId,GAAMjB,KAAKmB,KACf,KAAInB,KAAKoB,mBAAoBH,EAAIsO,cAMjC,MAHA5Q,GAAEsC,EAAIM,SAASpC,IAAI8B,EAAIuF,QAAQG,UAAUxH,IAAI,YAAa,IAC1Da,KAAK6b,cACL5a,EAAI0J,QAAQvF,IACL,GAEXgI,QAAS,WACDpN,KAAKqB,YACLrB,KAAKqB,WAAW+L,UAEpBpN,KAAKqB,WAAarB,KAAKmB,MAAQ,OAGvCqB,EAAMG,GAAGmZ,OAAOxV,KAClB7D,OAAOD,MAAMuZ,QACRtZ,OAAOD,OACE,kBAAV9D,SAAwBA,OAAOsd,IAAMtd,OAAS,SAAUud,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.window.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.window', [\n        'kendo.draganddrop',\n        'kendo.popup'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'window',\n        name: 'Window',\n        category: 'web',\n        description: 'The Window widget displays content in a modal or non-modal HTML window.',\n        depends: [\n            'draganddrop',\n            'popup'\n        ],\n        features: [{\n                id: 'window-fx',\n                name: 'Animation',\n                description: 'Support for animation',\n                depends: ['fx']\n            }]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, TabKeyTrap = kendo.ui.Popup.TabKeyTrap, Draggable = kendo.ui.Draggable, isPlainObject = $.isPlainObject, activeElement = kendo._activeElement, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, proxy = $.proxy, extend = $.extend, each = $.each, template = kendo.template, BODY = 'body', templates, NS = '.kendoWindow', MODAL_NS = '.kendoWindowModal', KWINDOW = '.k-window', KWINDOWTITLE = '.k-window-title', KWINDOWTITLEBAR = KWINDOWTITLE + 'bar', KWINDOWCONTENT = '.k-window-content', KDIALOGCONTENT = '.k-dialog-content', KWINDOWRESIZEHANDLES = '.k-resize-handle', KOVERLAY = '.k-overlay', KCONTENTFRAME = 'k-content-frame', LOADING = 'k-i-loading', KHOVERSTATE = 'k-state-hover', KFOCUSEDSTATE = 'k-state-focused', MAXIMIZEDSTATE = 'k-window-maximized', VISIBLE = ':visible', HIDDEN = 'hidden', CURSOR = 'cursor', OPEN = 'open', ACTIVATE = 'activate', DEACTIVATE = 'deactivate', CLOSE = 'close', REFRESH = 'refresh', MINIMIZE = 'minimize', MAXIMIZE = 'maximize', RESIZESTART = 'resizeStart', RESIZE = 'resize', RESIZEEND = 'resizeEnd', DRAGSTART = 'dragstart', DRAGEND = 'dragend', ERROR = 'error', OVERFLOW = 'overflow', DATADOCOVERFLOWRULE = 'original-overflow-rule', ZINDEX = 'zIndex', MINIMIZE_MAXIMIZE = '.k-window-actions .k-i-window-minimize,.k-window-actions .k-i-window-maximize', KPIN = '.k-i-pin', KUNPIN = '.k-i-unpin', PIN_UNPIN = KPIN + ',' + KUNPIN, TITLEBAR_BUTTONS = '.k-window-titlebar .k-window-action', REFRESHICON = '.k-window-titlebar .k-i-refresh', WINDOWEVENTSHANDLED = 'WindowEventsHandled', zero = /^0[a-z]*$/i, isLocalUrl = kendo.isLocalUrl, SIZE = {\n                small: 'k-window-sm',\n                medium: 'k-window-md',\n                large: 'k-window-lg'\n            };\n        function defined(x) {\n            return typeof x != 'undefined';\n        }\n        function toInt(element, property) {\n            return parseInt(element.css(property), 10) || 0;\n        }\n        function constrain(value, low, high) {\n            var normalizedValue;\n            if (value && isNaN(value) && value.toString().indexOf('px') < 0) {\n                normalizedValue = value;\n            } else {\n                normalizedValue = Math.max(Math.min(parseInt(value, 10), high === Infinity ? high : parseInt(high, 10)), low === -Infinity ? low : parseInt(low, 10));\n            }\n            return normalizedValue;\n        }\n        function executableScript() {\n            return !this.type || this.type.toLowerCase().indexOf('script') >= 0;\n        }\n        function getPosition(elem) {\n            var result = {\n                    top: elem.offsetTop,\n                    left: elem.offsetLeft\n                }, parent = elem.offsetParent;\n            while (parent) {\n                result.top += parent.offsetTop;\n                result.left += parent.offsetLeft;\n                var parentOverflowX = $(parent).css('overflowX');\n                var parentOverflowY = $(parent).css('overflowY');\n                if (parentOverflowY === 'auto' || parentOverflowY === 'scroll') {\n                    result.top -= parent.scrollTop;\n                }\n                if (parentOverflowX === 'auto' || parentOverflowX === 'scroll') {\n                    result.left -= parent.scrollLeft;\n                }\n                parent = parent.offsetParent;\n            }\n            return result;\n        }\n        var Window = Widget.extend({\n            init: function (element, options) {\n                var that = this, wrapper, offset = {}, visibility, display, position, isVisible = false, content, windowContent, windowFrame, globalWindow, suppressActions = options && options.actions && !options.actions.length, id;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                position = options.position;\n                element = that.element;\n                content = options.content;\n                globalWindow = $(window);\n                if (suppressActions) {\n                    options.actions = [];\n                }\n                that.appendTo = $(options.appendTo);\n                that.containment = options.draggable.containment ? $(options.draggable.containment).first() : null;\n                if (content && !isPlainObject(content)) {\n                    content = options.content = { url: content };\n                }\n                element.find('script').filter(executableScript).remove();\n                if (!element.parent().is(that.appendTo) && !that.containment && (position.top === undefined || position.left === undefined)) {\n                    if (element.is(VISIBLE)) {\n                        offset = element.offset();\n                        isVisible = true;\n                    } else {\n                        visibility = element.css('visibility');\n                        display = element.css('display');\n                        element.css({\n                            visibility: HIDDEN,\n                            display: ''\n                        });\n                        offset = element.offset();\n                        element.css({\n                            visibility: visibility,\n                            display: display\n                        });\n                    }\n                    if (position.top === undefined) {\n                        position.top = offset.top;\n                    }\n                    if (position.left === undefined) {\n                        position.left = offset.left;\n                    }\n                }\n                if (!defined(options.visible) || options.visible === null) {\n                    options.visible = element.is(VISIBLE);\n                }\n                wrapper = that.wrapper = element.closest(KWINDOW);\n                if (!element.is('.k-content') || !wrapper[0]) {\n                    element.addClass('k-window-content k-content');\n                    that._createWindow(element, options);\n                    wrapper = that.wrapper = element.closest(KWINDOW);\n                    that.title(that.options.title);\n                    that._dimensions();\n                }\n                that.minTop = that.minLeft = -Infinity;\n                that.maxTop = that.maxLeft = Infinity;\n                that._position();\n                if (content) {\n                    that.refresh(content);\n                }\n                if (options.visible) {\n                    that.toFront();\n                }\n                windowContent = wrapper.children(KWINDOWCONTENT);\n                that._tabindex(windowContent);\n                if (options.visible && options.modal) {\n                    that._overlay(wrapper.is(VISIBLE)).css({ opacity: 0.5 });\n                }\n                wrapper.on('mouseenter' + NS, TITLEBAR_BUTTONS, proxy(that._buttonEnter, that)).on('mouseleave' + NS, TITLEBAR_BUTTONS, proxy(that._buttonLeave, that)).on('click' + NS, '> ' + TITLEBAR_BUTTONS, proxy(that._windowActionHandler, that)).on('keydown' + NS, proxy(that._keydown, that)).on('focus' + NS, proxy(that._focus, that)).on('blur' + NS, proxy(that._blur, that));\n                windowContent.on('keydown' + NS, proxy(that._keydown, that)).on('focus' + NS, proxy(that._focus, that)).on('blur' + NS, proxy(that._blur, that));\n                windowFrame = windowContent.find('.' + KCONTENTFRAME)[0];\n                if (windowFrame && !globalWindow.data(WINDOWEVENTSHANDLED)) {\n                    globalWindow.on('blur' + NS, function () {\n                        var element = $(document.activeElement).parent(KWINDOWCONTENT);\n                        if (element.length) {\n                            var windowInstance = kendo.widgetInstance(element);\n                            windowInstance._focus();\n                        }\n                    });\n                    globalWindow.on('focus' + NS, function () {\n                        $(KWINDOWCONTENT).not(KDIALOGCONTENT).each(function (i, element) {\n                            kendo.widgetInstance($(element))._blur();\n                        });\n                    });\n                    globalWindow.data(WINDOWEVENTSHANDLED, true);\n                }\n                this._resizable();\n                this._draggable();\n                if (options.pinned && this.wrapper.is(':visible')) {\n                    that.pin();\n                }\n                id = element.attr('id');\n                if (id) {\n                    id = id + '_wnd_title';\n                    wrapper.children(KWINDOWTITLEBAR).children(KWINDOWTITLE).attr('id', id);\n                    windowContent.attr({\n                        'role': 'dialog',\n                        'aria-labelledby': id\n                    });\n                }\n                wrapper.add(wrapper.children('.k-resize-handle,' + KWINDOWTITLEBAR)).on('mousedown' + NS, proxy(that.toFront, that));\n                that.touchScroller = kendo.touchScroller(element);\n                that._resizeHandler = proxy(that._onDocumentResize, that);\n                that._marker = kendo.guid().substring(0, 8);\n                $(window).on('resize' + NS + that._marker, that._resizeHandler);\n                if (options.visible) {\n                    that.trigger(OPEN);\n                    that.trigger(ACTIVATE);\n                }\n                kendo.notify(that);\n                if (this.options.modal) {\n                    this._tabKeyTrap = new TabKeyTrap(wrapper);\n                    this._tabKeyTrap.trap();\n                    this._tabKeyTrap.shouldTrap = function () {\n                        return windowContent.data('isFront');\n                    };\n                }\n            },\n            _buttonEnter: function (e) {\n                $(e.currentTarget).addClass(KHOVERSTATE);\n            },\n            _buttonLeave: function (e) {\n                $(e.currentTarget).removeClass(KHOVERSTATE);\n            },\n            _focus: function () {\n                this.wrapper.addClass(KFOCUSEDSTATE);\n            },\n            _blur: function () {\n                this.wrapper.removeClass(KFOCUSEDSTATE);\n            },\n            _dimensions: function () {\n                var wrapper = this.wrapper;\n                var options = this.options;\n                var width = options.width;\n                var height = options.height;\n                var maxHeight = options.maxHeight;\n                var sizeClass = options.size;\n                var dimensions = [\n                    'minWidth',\n                    'minHeight',\n                    'maxWidth',\n                    'maxHeight'\n                ];\n                var contentBoxSizing = wrapper.css('box-sizing') == 'content-box';\n                var lrBorderWidth = contentBoxSizing ? toInt(wrapper, 'border-left-width') + toInt(wrapper, 'border-right-width') : 0;\n                var tbBorderWidth = contentBoxSizing ? toInt(wrapper, 'border-top-width') + toInt(wrapper, 'border-bottom-width') : 0;\n                var paddingTop = contentBoxSizing ? toInt(wrapper, 'padding-top') : 0;\n                if (this.containment && !this._isPinned) {\n                    this._updateBoundaries();\n                    options.maxHeight = Math.min(this.containment.height - (tbBorderWidth + paddingTop), maxHeight);\n                    options.maxWidth = Math.min(this.containment.width - lrBorderWidth, options.maxWidth);\n                }\n                for (var i = 0; i < dimensions.length; i++) {\n                    var value = options[dimensions[i]] || '';\n                    if (value != Infinity) {\n                        wrapper.css(dimensions[i], value);\n                    }\n                }\n                if (maxHeight != Infinity) {\n                    this.element.css('maxHeight', maxHeight);\n                }\n                if (width) {\n                    wrapper.width(constrain(width, options.minWidth, options.maxWidth));\n                } else {\n                    wrapper.width('');\n                }\n                if (height) {\n                    wrapper.height(constrain(height, options.minHeight, options.maxHeight));\n                } else {\n                    wrapper.height('');\n                }\n                if (!options.visible) {\n                    wrapper.hide();\n                }\n                if (sizeClass && SIZE[sizeClass]) {\n                    wrapper.addClass(SIZE[sizeClass]);\n                }\n            },\n            _position: function () {\n                var wrapper = this.wrapper, position = this.options.position;\n                this._updateBoundaries();\n                if (this.containment) {\n                    position.top = Math.min(this.minTop + (position.top || 0), this.maxTop);\n                    position.left = Math.min(this.minLeft + (position.left || 0), this.maxLeft);\n                }\n                if (position.top === 0) {\n                    position.top = position.top.toString();\n                }\n                if (position.left === 0) {\n                    position.left = position.left.toString();\n                }\n                wrapper.css({\n                    top: position.top || '',\n                    left: position.left || ''\n                });\n            },\n            _updateBoundaries: function () {\n                var containment = this.containment;\n                if (!containment) {\n                    return null;\n                }\n                containment.width = containment.innerWidth();\n                containment.height = containment.innerHeight();\n                if (parseInt(containment.width, 10) > containment[0].clientWidth) {\n                    containment.width -= kendo.support.scrollbar();\n                }\n                if (parseInt(containment.height, 10) > containment[0].clientHeight) {\n                    containment.height -= kendo.support.scrollbar();\n                }\n                containment.position = getPosition(containment[0]);\n                if (this._isPinned) {\n                    this.minTop = this.minLeft = -Infinity;\n                    this.maxTop = this.maxLeft = Infinity;\n                } else {\n                    this.minTop = containment.scrollTop();\n                    this.minLeft = containment.scrollLeft();\n                    this.maxLeft = this.minLeft + containment.width - outerWidth(this.wrapper, true);\n                    this.maxTop = this.minTop + containment.height - outerHeight(this.wrapper, true);\n                }\n            },\n            _animationOptions: function (id) {\n                var animation = this.options.animation;\n                var basicAnimation = {\n                    open: { effects: {} },\n                    close: {\n                        hide: true,\n                        effects: {}\n                    }\n                };\n                return animation && animation[id] || basicAnimation[id];\n            },\n            _resize: function () {\n                kendo.resize(this.element.children());\n            },\n            _resizable: function () {\n                var resizable = this.options.resizable;\n                var wrapper = this.wrapper;\n                if (this.resizing) {\n                    wrapper.off('dblclick' + NS).children(KWINDOWRESIZEHANDLES).remove();\n                    this.resizing.destroy();\n                    this.resizing = null;\n                }\n                if (resizable) {\n                    wrapper.on('dblclick' + NS, KWINDOWTITLEBAR, proxy(function (e) {\n                        if (!$(e.target).closest('.k-window-action').length) {\n                            this.toggleMaximization();\n                        }\n                    }, this));\n                    each('n e s w se sw ne nw'.split(' '), function (index, handler) {\n                        wrapper.append(templates.resizeHandle(handler));\n                    });\n                    this.resizing = new WindowResizing(this);\n                }\n                wrapper = null;\n            },\n            _draggable: function () {\n                var draggable = this.options.draggable;\n                if (this.dragging) {\n                    this.dragging.destroy();\n                    this.dragging = null;\n                }\n                if (draggable) {\n                    this.dragging = new WindowDragging(this, draggable.dragHandle || KWINDOWTITLEBAR);\n                }\n            },\n            _actions: function () {\n                var options = this.options;\n                var actions = options.actions;\n                var pinned = options.pinned;\n                var titlebar = this.wrapper.children(KWINDOWTITLEBAR);\n                var container = titlebar.find('.k-window-actions');\n                var windowSpecificCommands = [\n                    'maximize',\n                    'minimize'\n                ];\n                actions = $.map(actions, function (action) {\n                    action = pinned && action.toLowerCase() === 'pin' ? 'unpin' : action;\n                    return { name: windowSpecificCommands.indexOf(action.toLowerCase()) > -1 ? 'window-' + action : action };\n                });\n                container.html(kendo.render(templates.action, actions));\n            },\n            setOptions: function (options) {\n                var that = this;\n                var sizeClass = that.options.size;\n                var cachedOptions = JSON.parse(JSON.stringify(options));\n                extend(options.position, that.options.position);\n                extend(options.position, cachedOptions.position);\n                Widget.fn.setOptions.call(that, options);\n                var scrollable = that.options.scrollable !== false;\n                that.restore();\n                if (typeof options.title !== 'undefined') {\n                    that.title(options.title);\n                }\n                that.wrapper.removeClass(SIZE[sizeClass]);\n                that._dimensions();\n                that._position();\n                that._resizable();\n                that._draggable();\n                that._actions();\n                if (typeof options.modal !== 'undefined') {\n                    var visible = that.options.visible !== false;\n                    that._enableDocumentScrolling();\n                    that._overlay(options.modal && visible);\n                }\n                that.element.css(OVERFLOW, scrollable ? '' : 'hidden');\n            },\n            events: [\n                OPEN,\n                ACTIVATE,\n                DEACTIVATE,\n                CLOSE,\n                MINIMIZE,\n                MAXIMIZE,\n                REFRESH,\n                RESIZESTART,\n                RESIZE,\n                RESIZEEND,\n                DRAGSTART,\n                DRAGEND,\n                ERROR\n            ],\n            options: {\n                name: 'Window',\n                animation: {\n                    open: {\n                        effects: {\n                            zoom: { direction: 'in' },\n                            fade: { direction: 'in' }\n                        },\n                        duration: 350\n                    },\n                    close: {\n                        effects: {\n                            zoom: {\n                                direction: 'out',\n                                properties: { scale: 0.7 }\n                            },\n                            fade: { direction: 'out' }\n                        },\n                        duration: 350,\n                        hide: true\n                    }\n                },\n                title: '',\n                actions: ['Close'],\n                autoFocus: true,\n                modal: false,\n                size: 'auto',\n                resizable: true,\n                draggable: true,\n                minWidth: 90,\n                minHeight: 50,\n                maxWidth: Infinity,\n                maxHeight: Infinity,\n                pinned: false,\n                scrollable: true,\n                position: {},\n                content: null,\n                visible: null,\n                height: null,\n                width: null,\n                appendTo: 'body',\n                isMaximized: false,\n                isMinimized: false\n            },\n            _closable: function () {\n                return $.inArray('close', $.map(this.options.actions, function (x) {\n                    return x.toLowerCase();\n                })) > -1;\n            },\n            _keydown: function (e) {\n                var that = this, options = that.options, keys = kendo.keys, keyCode = e.keyCode, wrapper = that.wrapper, offset, handled, distance = 10, isMaximized = options.isMaximized, isMinimized = options.isMinimized, newWidth, newHeight, w, h;\n                if (keyCode == keys.ESC && that._closable()) {\n                    e.stopPropagation();\n                    that._close(false);\n                }\n                if (e.target != e.currentTarget || that._closing) {\n                    return;\n                }\n                if (e.altKey && keyCode == 82) {\n                    that.refresh();\n                }\n                if (e.altKey && keyCode == 80) {\n                    if (that.options.pinned) {\n                        that.unpin();\n                    } else {\n                        that.pin();\n                    }\n                }\n                if (e.altKey && keyCode == keys.UP) {\n                    if (isMinimized) {\n                        that.restore();\n                        that.element.focus();\n                    } else if (!isMaximized) {\n                        that.maximize();\n                        that.element.focus();\n                    }\n                } else if (e.altKey && keyCode == keys.DOWN) {\n                    if (!isMinimized && !isMaximized) {\n                        that.minimize();\n                        that.wrapper.focus();\n                    } else if (isMaximized) {\n                        that.restore();\n                        that.element.focus();\n                    }\n                }\n                offset = kendo.getOffset(wrapper);\n                if (that.containment && !that._isPinned) {\n                    offset = that.options.position;\n                }\n                if (options.draggable && !e.ctrlKey && !e.altKey && !isMaximized) {\n                    that._updateBoundaries();\n                    if (keyCode == keys.UP) {\n                        offset.top = constrain(offset.top - distance, that.minTop, that.maxTop);\n                        handled = wrapper.css('top', offset.top);\n                    } else if (keyCode == keys.DOWN) {\n                        offset.top = constrain(offset.top + distance, that.minTop, that.maxTop);\n                        handled = wrapper.css('top', offset.top);\n                    } else if (keyCode == keys.LEFT) {\n                        offset.left = constrain(offset.left - distance, that.minLeft, that.maxLeft);\n                        handled = wrapper.css('left', offset.left);\n                    } else if (keyCode == keys.RIGHT) {\n                        offset.left = constrain(offset.left + distance, that.minLeft, that.maxLeft);\n                        handled = wrapper.css('left', offset.left);\n                    }\n                }\n                if (options.resizable && e.ctrlKey && !isMaximized && !isMinimized) {\n                    if (keyCode == keys.UP) {\n                        handled = true;\n                        newHeight = wrapper.height() - distance;\n                    } else if (keyCode == keys.DOWN) {\n                        handled = true;\n                        if (that.containment && !that._isPinned) {\n                            newHeight = Math.min(wrapper.height() + distance, that.containment.height - offset.top - toInt(wrapper, 'padding-top') - toInt(wrapper, 'borderBottomWidth') - toInt(wrapper, 'borderTopWidth'));\n                        } else {\n                            newHeight = wrapper.height() + distance;\n                        }\n                    }\n                    if (keyCode == keys.LEFT) {\n                        handled = true;\n                        newWidth = wrapper.width() - distance;\n                    } else if (keyCode == keys.RIGHT) {\n                        handled = true;\n                        if (that.containment && !that._isPinned) {\n                            newWidth = Math.min(wrapper.width() + distance, that.containment.width - offset.left - toInt(wrapper, 'borderLeftWidth') - toInt(wrapper, 'borderRightWidth'));\n                        } else {\n                            newWidth = wrapper.width() + distance;\n                        }\n                    }\n                    if (handled) {\n                        w = constrain(newWidth, options.minWidth, options.maxWidth);\n                        h = constrain(newHeight, options.minHeight, options.maxHeight);\n                        if (!isNaN(w)) {\n                            wrapper.width(w);\n                            that.options.width = w + 'px';\n                        }\n                        if (!isNaN(h)) {\n                            wrapper.height(h);\n                            that.options.height = h + 'px';\n                        }\n                        that.resize();\n                    }\n                }\n                if (handled) {\n                    e.preventDefault();\n                }\n            },\n            _overlay: function (visible) {\n                var overlay = this.containment ? this.containment.children(KOVERLAY) : this.appendTo.children(KOVERLAY), wrapper = this.wrapper;\n                if (!overlay.length) {\n                    overlay = $('<div class=\\'k-overlay\\' />');\n                }\n                overlay.insertBefore(wrapper[0]).toggle(visible).css(ZINDEX, parseInt(wrapper.css(ZINDEX), 10) - 1);\n                if (this.options.modal.preventScroll && !this.containment) {\n                    this._stopDocumentScrolling();\n                }\n                return overlay;\n            },\n            _actionForIcon: function (icon) {\n                var iconClass = /\\bk-i(-\\w+)+\\b/.exec(icon[0].className)[0];\n                return {\n                    'k-i-close': '_close',\n                    'k-i-window-maximize': 'maximize',\n                    'k-i-window-minimize': 'minimize',\n                    'k-i-window-restore': 'restore',\n                    'k-i-refresh': 'refresh',\n                    'k-i-pin': 'pin',\n                    'k-i-unpin': 'unpin'\n                }[iconClass];\n            },\n            _windowActionHandler: function (e) {\n                if (this._closing) {\n                    return;\n                }\n                var icon = $(e.target).closest('.k-window-action').find('.k-icon');\n                var action = this._actionForIcon(icon);\n                if (action) {\n                    e.preventDefault();\n                    this[action]();\n                    return false;\n                }\n            },\n            _modals: function () {\n                var that = this;\n                var zStack = $(KWINDOW).filter(function () {\n                    var dom = $(this);\n                    var object = that._object(dom);\n                    var options = object && object.options;\n                    return options && options.modal && options.visible && options.appendTo === that.options.appendTo && dom.is(VISIBLE);\n                }).sort(function (a, b) {\n                    return +$(a).css('zIndex') - +$(b).css('zIndex');\n                });\n                that = null;\n                return zStack;\n            },\n            _object: function (element) {\n                var content = element.children(KWINDOWCONTENT);\n                var widget = kendo.widgetInstance(content);\n                if (widget) {\n                    return widget;\n                }\n                return undefined;\n            },\n            center: function () {\n                var that = this, position = that.options.position, wrapper = that.wrapper, documentWindow = $(window), scrollTop = 0, scrollLeft = 0, newTop, newLeft;\n                if (that.options.isMaximized) {\n                    return that;\n                }\n                if (that.options.pinned && !that._isPinned) {\n                    that.pin();\n                }\n                if (!that.options.pinned) {\n                    scrollTop = documentWindow.scrollTop();\n                    scrollLeft = documentWindow.scrollLeft();\n                }\n                if (this.containment && !that.options.pinned) {\n                    newTop = this.minTop + (this.maxTop - this.minTop) / 2;\n                    newLeft = this.minLeft + (this.maxLeft - this.minLeft) / 2;\n                } else {\n                    that._scrollIsAppended = true;\n                    newLeft = scrollLeft + Math.max(0, (documentWindow.width() - wrapper.width()) / 2);\n                    newTop = scrollTop + Math.max(0, (documentWindow.height() - wrapper.height() - toInt(wrapper, 'paddingTop')) / 2);\n                }\n                wrapper.css({\n                    left: newLeft,\n                    top: newTop\n                });\n                position.top = newTop;\n                position.left = newLeft;\n                return that;\n            },\n            title: function (title) {\n                var that = this, value, encoded = true, wrapper = that.wrapper, titleBar = wrapper.children(KWINDOWTITLEBAR), titleElement = titleBar.children(KWINDOWTITLE), titleBarHeight, display, visibility;\n                if (!arguments.length) {\n                    return titleElement.html();\n                }\n                if ($.isPlainObject(title)) {\n                    value = typeof title.text !== 'undefined' ? title.text : '';\n                    encoded = title.encoded !== false;\n                } else {\n                    value = title;\n                }\n                if (value === false) {\n                    wrapper.addClass('k-window-titleless');\n                    titleBar.remove();\n                } else {\n                    if (!titleBar.length) {\n                        wrapper.prepend(templates.titlebar({ title: encoded ? kendo.htmlEncode(value) : value }));\n                        that._actions();\n                        titleBar = wrapper.children(KWINDOWTITLEBAR);\n                    } else {\n                        titleElement.html(encoded ? kendo.htmlEncode(value) : value);\n                    }\n                    visibility = wrapper.css('visibility');\n                    display = wrapper.css('display');\n                    if (visibility === HIDDEN) {\n                        wrapper.css({ display: '' });\n                        titleBarHeight = parseInt(outerHeight(titleBar), 10);\n                        wrapper.css({ display: display });\n                    } else {\n                        wrapper.css({\n                            visibility: HIDDEN,\n                            display: ''\n                        });\n                        titleBarHeight = parseInt(outerHeight(titleBar), 10);\n                        wrapper.css({\n                            visibility: visibility,\n                            display: display\n                        });\n                    }\n                    wrapper.css('padding-top', titleBarHeight);\n                    titleBar.css('margin-top', -titleBarHeight);\n                }\n                that.options.title = value;\n                return that;\n            },\n            content: function (html, data) {\n                var content = this.wrapper.children(KWINDOWCONTENT), scrollContainer = content.children('.km-scroll-container');\n                content = scrollContainer[0] ? scrollContainer : content;\n                if (!defined(html)) {\n                    return content.html();\n                }\n                this.angular('cleanup', function () {\n                    return { elements: content.children() };\n                });\n                kendo.destroy(this.element.children());\n                content.empty().html(html);\n                this.angular('compile', function () {\n                    var a = [];\n                    for (var i = content.length; --i >= 0;) {\n                        a.push({ dataItem: data });\n                    }\n                    return {\n                        elements: content.children(),\n                        data: a\n                    };\n                });\n                return this;\n            },\n            open: function () {\n                var that = this, wrapper = that.wrapper, options = that.options, showOptions = this._animationOptions('open'), contentElement = wrapper.children(KWINDOWCONTENT), overlay, otherModalsVisible, containmentContext = this.containment && !that._isPinned, doc = containmentContext ? this.containment : $(document);\n                if (!that.trigger(OPEN)) {\n                    if (that._closing) {\n                        wrapper.kendoStop(true, true);\n                    }\n                    that._closing = false;\n                    that.toFront();\n                    if (options.autoFocus) {\n                        that.element.focus();\n                    }\n                    options.visible = true;\n                    if (options.modal) {\n                        otherModalsVisible = !!that._modals().length;\n                        overlay = that._overlay(otherModalsVisible);\n                        overlay.kendoStop(true, true);\n                        if (showOptions.duration && kendo.effects.Fade && !otherModalsVisible) {\n                            var overlayFx = kendo.fx(overlay).fadeIn();\n                            overlayFx.duration(showOptions.duration || 0);\n                            overlayFx.endValue(0.5);\n                            overlayFx.play();\n                        } else {\n                            overlay.css('opacity', 0.5);\n                        }\n                        overlay.show();\n                        $(window).on('focus' + MODAL_NS, function () {\n                            if (contentElement.data('isFront') && !$(document.activeElement).closest(contentElement).length) {\n                                that.element.focus();\n                            }\n                        });\n                    }\n                    if (!wrapper.is(VISIBLE)) {\n                        contentElement.css(OVERFLOW, HIDDEN);\n                        wrapper.show().kendoStop().kendoAnimate({\n                            effects: showOptions.effects,\n                            duration: showOptions.duration,\n                            complete: proxy(this._activate, this)\n                        });\n                    }\n                }\n                if (options.isMaximized) {\n                    that._containerScrollTop = doc.scrollTop();\n                    that._containerScrollLeft = doc.scrollLeft();\n                    that._stopDocumentScrolling();\n                }\n                if (this.options.pinned && !this._isPinned) {\n                    this.pin();\n                }\n                return that;\n            },\n            _activate: function () {\n                var scrollable = this.options.scrollable !== false;\n                if (this.options.autoFocus) {\n                    this.element.focus();\n                }\n                this.element.css(OVERFLOW, scrollable ? '' : 'hidden');\n                kendo.resize(this.element.children());\n                this.trigger(ACTIVATE);\n            },\n            _removeOverlay: function (suppressAnimation) {\n                var modals = this._modals();\n                var options = this.options;\n                var hideOverlay = options.modal && !modals.length;\n                var overlay = options.modal ? this._overlay(true) : $(undefined);\n                var hideOptions = this._animationOptions('close');\n                if (hideOverlay) {\n                    if (!suppressAnimation && hideOptions.duration && kendo.effects.Fade) {\n                        var overlayFx = kendo.fx(overlay).fadeOut();\n                        overlayFx.duration(hideOptions.duration || 0);\n                        overlayFx.startValue(0.5);\n                        overlayFx.play();\n                    } else {\n                        this._overlay(false).remove();\n                    }\n                    if (options.modal.preventScroll) {\n                        this._enableDocumentScrolling();\n                    }\n                } else if (modals.length) {\n                    this._object(modals.last())._overlay(true);\n                    if (options.modal.preventScroll) {\n                        this._stopDocumentScrolling();\n                    }\n                }\n            },\n            _close: function (systemTriggered) {\n                var that = this, wrapper = that.wrapper, options = that.options, showOptions = this._animationOptions('open'), hideOptions = this._animationOptions('close'), containmentContext = this.containment && !that._isPinned, doc = containmentContext ? this.containment : $(document), defaultPrevented;\n                if (that._closing) {\n                    return;\n                }\n                defaultPrevented = that.trigger(CLOSE, { userTriggered: !systemTriggered });\n                that._closing = !defaultPrevented;\n                if (wrapper.is(VISIBLE) && !defaultPrevented) {\n                    options.visible = false;\n                    $(KWINDOW).each(function (i, element) {\n                        var contentElement = $(element).children(KWINDOWCONTENT);\n                        if (element != wrapper && contentElement.find('> .' + KCONTENTFRAME).length > 0) {\n                            contentElement.children(KOVERLAY).remove();\n                        }\n                    });\n                    this._removeOverlay();\n                    wrapper.kendoStop().kendoAnimate({\n                        effects: hideOptions.effects || showOptions.effects,\n                        reverse: hideOptions.reverse === true,\n                        duration: hideOptions.duration,\n                        complete: proxy(this._deactivate, this)\n                    });\n                    $(window).off(MODAL_NS);\n                }\n                if (that.options.isMaximized) {\n                    that._enableDocumentScrolling();\n                    if (that._containerScrollTop && that._containerScrollTop > 0) {\n                        doc.scrollTop(that._containerScrollTop);\n                    }\n                    if (that._containerScrollLeft && that._containerScrollLeft > 0) {\n                        doc.scrollLeft(that._containerScrollLeft);\n                    }\n                }\n            },\n            _deactivate: function () {\n                var that = this;\n                that.wrapper.hide().css('opacity', '');\n                that.trigger(DEACTIVATE);\n                if (that.options.modal) {\n                    var lastModal = that._object(that._modals().last());\n                    if (lastModal) {\n                        lastModal.toFront();\n                    }\n                }\n            },\n            close: function () {\n                this._close(true);\n                return this;\n            },\n            _actionable: function (element) {\n                return $(element).is(TITLEBAR_BUTTONS + ',' + TITLEBAR_BUTTONS + ' .k-icon,:input,a');\n            },\n            _shouldFocus: function (target) {\n                var active = activeElement(), element = this.element;\n                return this.options.autoFocus && !$(active).is(element) && !this._actionable(target) && (!element.find(active).length || !element.find(target).length);\n            },\n            toFront: function (e) {\n                var that = this, wrapper = that.wrapper, currentWindow = wrapper[0], containmentContext = that.containment && !that._isPinned, zIndex = +wrapper.css(ZINDEX), originalZIndex = zIndex, target = e && e.target || null;\n                $(KWINDOW).each(function (i, element) {\n                    var windowObject = $(element), zIndexNew = windowObject.css(ZINDEX), contentElement = windowObject.children(KWINDOWCONTENT);\n                    if (!isNaN(zIndexNew)) {\n                        zIndex = Math.max(+zIndexNew, zIndex);\n                    }\n                    contentElement.data('isFront', element == currentWindow);\n                    if (element != currentWindow && contentElement.find('> .' + KCONTENTFRAME).length > 0) {\n                        contentElement.append(templates.overlay);\n                    }\n                });\n                if (!wrapper[0].style.zIndex || originalZIndex < zIndex) {\n                    wrapper.css(ZINDEX, zIndex + 2);\n                }\n                that.element.find('> .k-overlay').remove();\n                if (that._shouldFocus(target)) {\n                    if (that.isMinimized()) {\n                        that.wrapper.focus();\n                    } else if ($(target).is(KOVERLAY)) {\n                        setTimeout(function () {\n                            that.element.focus();\n                        });\n                    } else {\n                        that.element.focus();\n                    }\n                    var scrollTop = containmentContext ? that.containment.scrollTop() : $(window).scrollTop(), windowTop = parseInt(wrapper.position().top, 10);\n                    if (!that.options.pinned && windowTop > 0 && windowTop < scrollTop) {\n                        if (scrollTop > 0) {\n                            $(window).scrollTop(windowTop);\n                        } else {\n                            wrapper.css('top', scrollTop);\n                        }\n                    }\n                }\n                wrapper = null;\n                return that;\n            },\n            toggleMaximization: function () {\n                if (this._closing) {\n                    return this;\n                }\n                return this[this.options.isMaximized ? 'restore' : 'maximize']();\n            },\n            restore: function () {\n                var that = this;\n                var options = that.options;\n                var minHeight = options.minHeight;\n                var restoreOptions = that.restoreOptions;\n                var shouldRestrictTop;\n                var container = that.containment && !that._isPinned ? that.containment : $(document);\n                if (!options.isMaximized && !options.isMinimized) {\n                    return that;\n                }\n                if (minHeight && minHeight != Infinity) {\n                    that.wrapper.css('min-height', minHeight);\n                }\n                if (restoreOptions && !options.isMaximized) {\n                    restoreOptions.height = constrain(restoreOptions.height, that.options.minHeight, that.options.maxHeight);\n                    shouldRestrictTop = options.position.top + parseInt(restoreOptions.height, 10) > that.maxTop;\n                    if (shouldRestrictTop) {\n                        options.position.top = constrain(options.position.top, that.minTop, that.maxTop - parseInt(restoreOptions.height, 10));\n                        extend(restoreOptions, {\n                            left: options.position.left,\n                            top: options.position.top\n                        });\n                    }\n                }\n                that.wrapper.css({\n                    position: options.pinned ? 'fixed' : 'absolute',\n                    left: restoreOptions.left,\n                    top: restoreOptions.top,\n                    width: restoreOptions.width,\n                    height: restoreOptions.height\n                }).removeClass(MAXIMIZEDSTATE).find('.k-window-content,.k-resize-handle').show().end().find('.k-window-titlebar .k-i-window-restore').parent().remove().end().end().find(MINIMIZE_MAXIMIZE).parent().show().end().end().find(PIN_UNPIN).parent().show();\n                if (options.isMaximized) {\n                    that.wrapper.find('.k-i-window-maximize').parent().focus();\n                } else if (options.isMinimized) {\n                    that.wrapper.find('.k-i-window-minimize').parent().focus();\n                }\n                that.options.width = restoreOptions.width;\n                that.options.height = restoreOptions.height;\n                if (!that.options.modal.preventScroll) {\n                    that._enableDocumentScrolling();\n                }\n                if (that._containerScrollTop && that._containerScrollTop > 0) {\n                    container.scrollTop(that._containerScrollTop);\n                }\n                if (that._containerScrollLeft && that._containerScrollLeft > 0) {\n                    container.scrollLeft(that._containerScrollLeft);\n                }\n                options.isMaximized = options.isMinimized = false;\n                that.wrapper.removeAttr('tabindex');\n                that.wrapper.removeAttr('aria-labelled-by');\n                that.resize();\n                return that;\n            },\n            _sizingAction: function (actionId, callback) {\n                var that = this, wrapper = that.wrapper, style = wrapper[0].style, options = that.options;\n                if (options.isMaximized || options.isMinimized) {\n                    return that;\n                }\n                that.restoreOptions = {\n                    width: style.width,\n                    height: style.height\n                };\n                wrapper.children(KWINDOWRESIZEHANDLES).hide().end().children(KWINDOWTITLEBAR).find(MINIMIZE_MAXIMIZE).parent().hide().eq(0).before(templates.action({ name: 'window-restore' }));\n                callback.call(that);\n                that.wrapper.children(KWINDOWTITLEBAR).find(PIN_UNPIN).parent().toggle(actionId !== 'maximize');\n                that.trigger(actionId);\n                wrapper.find('.k-i-window-restore').parent().focus();\n                return that;\n            },\n            maximize: function () {\n                this._sizingAction('maximize', function () {\n                    var that = this, wrapper = that.wrapper, containmentContext = this.containment && !that._isPinned, position = wrapper.position(), doc = $(document);\n                    extend(that.restoreOptions, {\n                        left: position.left + (containmentContext ? this.containment.scrollLeft() : 0),\n                        top: position.top + (containmentContext ? this.containment.scrollTop() : 0)\n                    });\n                    this._containerScrollTop = containmentContext ? this.containment.scrollTop() : doc.scrollTop();\n                    this._containerScrollLeft = containmentContext ? this.containment.scrollLeft() : doc.scrollLeft();\n                    that._stopDocumentScrolling();\n                    wrapper.css({\n                        top: containmentContext ? this.containment.scrollTop() : 0,\n                        left: containmentContext ? this.containment.scrollLeft() : 0,\n                        position: containmentContext ? 'absolute' : 'fixed'\n                    }).addClass(MAXIMIZEDSTATE);\n                    that.options.isMaximized = true;\n                    that._onDocumentResize();\n                });\n                return this;\n            },\n            _stopDocumentScrolling: function () {\n                var that = this;\n                var containment = that.containment;\n                if (containment && !that._isPinned) {\n                    that._storeOverflowRule(containment);\n                    containment.css(OVERFLOW, HIDDEN);\n                    that.wrapper.css({\n                        maxWidth: containment.innerWidth(),\n                        maxHeight: containment.innerHeight()\n                    });\n                    return;\n                }\n                var $body = $('body');\n                that._storeOverflowRule($body);\n                $body.css(OVERFLOW, HIDDEN);\n                var $html = $('html');\n                that._storeOverflowRule($html);\n                $html.css(OVERFLOW, HIDDEN);\n            },\n            _enableDocumentScrolling: function () {\n                var that = this;\n                var containment = that.containment;\n                if (containment && !that._isPinned) {\n                    that._restoreOverflowRule(containment);\n                    that.wrapper.css({\n                        maxWidth: containment.width,\n                        maxHeight: containment.height\n                    });\n                    return;\n                }\n                that._restoreOverflowRule($(document.body));\n                that._restoreOverflowRule($('html'));\n            },\n            _storeOverflowRule: function ($element) {\n                if (this._isOverflowStored($element)) {\n                    return;\n                }\n                var overflowRule = $element.get(0).style.overflow;\n                if (typeof overflowRule === 'string') {\n                    $element.data(DATADOCOVERFLOWRULE, overflowRule);\n                }\n            },\n            _isOverflowStored: function ($element) {\n                return typeof $element.data(DATADOCOVERFLOWRULE) === 'string';\n            },\n            _restoreOverflowRule: function ($element) {\n                var overflowRule = $element.data(DATADOCOVERFLOWRULE);\n                if (overflowRule !== null && overflowRule !== undefined) {\n                    $element.css(OVERFLOW, overflowRule);\n                    $element.removeData(DATADOCOVERFLOWRULE);\n                } else {\n                    $element.css(OVERFLOW, '');\n                }\n            },\n            isMaximized: function () {\n                return this.options.isMaximized;\n            },\n            minimize: function () {\n                this._sizingAction('minimize', function () {\n                    var that = this;\n                    that.wrapper.css({\n                        height: '',\n                        minHeight: ''\n                    });\n                    that.element.hide();\n                    that.options.isMinimized = true;\n                });\n                this.wrapper.attr('tabindex', 0);\n                this.wrapper.attr('aria-labelled-by', this.element.attr('aria-labelled-by'));\n                this._updateBoundaries();\n                return this;\n            },\n            isMinimized: function () {\n                return this.options.isMinimized;\n            },\n            pin: function () {\n                var that = this, win = $(window), wrapper = that.wrapper, options = that.options, position = options.position, top = this.containment ? getPosition(wrapper[0]).top + toInt(this.containment, 'borderTopWidth') : toInt(wrapper, 'top'), left = this.containment ? getPosition(wrapper[0]).left + toInt(this.containment, 'borderLeftWidth') : toInt(wrapper, 'left');\n                if (!that.options.isMaximized) {\n                    position.top = top;\n                    position.left = left;\n                    if (that._scrollIsAppended && (!this.containment || this.containment.css('position') !== 'fixed')) {\n                        position.top -= win.scrollTop();\n                        position.left -= win.scrollLeft();\n                        that._scrollIsAppended = false;\n                    }\n                    wrapper.css(extend(position, { position: 'fixed' }));\n                    wrapper.children(KWINDOWTITLEBAR).find(KPIN).addClass('k-i-unpin').removeClass('k-i-pin');\n                    that._isPinned = true;\n                    that.options.pinned = true;\n                    if (this.containment) {\n                        options.maxWidth = options.maxHeight = Infinity;\n                        wrapper.css({\n                            maxWidth: '',\n                            maxHeight: ''\n                        });\n                    }\n                }\n            },\n            unpin: function () {\n                var that = this, win = $(window), wrapper = that.wrapper, options = that.options, position = that.options.position, containment = that.containment, top = parseInt(wrapper.css('top'), 10) + win.scrollTop(), left = parseInt(wrapper.css('left'), 10) + win.scrollLeft();\n                if (!that.options.isMaximized) {\n                    that._isPinned = false;\n                    that._scrollIsAppended = true;\n                    that.options.pinned = false;\n                    if (containment) {\n                        that._updateBoundaries();\n                        options.maxWidth = Math.min(containment.width, options.maxWidth);\n                        options.maxHeight = Math.min(containment.height - toInt(wrapper, 'padding-top'), options.maxHeight);\n                        wrapper.css({\n                            maxWidth: options.maxWidth,\n                            maxHeight: options.maxHeight\n                        });\n                        if (top < containment.position.top) {\n                            top = that.minTop;\n                        } else if (top > containment.position.top + containment.height) {\n                            top = that.maxTop;\n                        } else {\n                            top = top + containment.scrollTop() - (containment.position.top + toInt(containment, 'border-top-width'));\n                        }\n                        if (left < containment.position.left) {\n                            left = that.minLeft;\n                        } else if (left > containment.position.left + containment.width) {\n                            left = that.maxLeft;\n                        } else {\n                            left = left + containment.scrollLeft() - (containment.position.left + toInt(containment, 'border-left-width'));\n                        }\n                    }\n                    position.top = constrain(top, that.minTop, that.maxTop);\n                    position.left = constrain(left, that.minLeft, that.maxLeft);\n                    wrapper.css(extend(position, { position: '' }));\n                    wrapper.children(KWINDOWTITLEBAR).find(KUNPIN).addClass('k-i-pin').removeClass('k-i-unpin');\n                }\n            },\n            _onDocumentResize: function () {\n                var that = this, wrapper = that.wrapper, wnd = $(window), zoomLevel = kendo.support.zoomLevel(), contentBoxSizing = wrapper.css('box-sizing') == 'content-box', w, h;\n                if (!that.options.isMaximized) {\n                    return;\n                }\n                var lrBorderWidth = contentBoxSizing ? toInt(wrapper, 'border-left-width') + toInt(wrapper, 'border-right-width') : 0;\n                var tbBorderWidth = contentBoxSizing ? toInt(wrapper, 'border-top-width') + toInt(wrapper, 'border-bottom-width') : 0;\n                var paddingTop = contentBoxSizing ? toInt(wrapper, 'padding-top') : 0;\n                if (that.containment && !that._isPinned) {\n                    w = that.containment.innerWidth() - lrBorderWidth;\n                    h = that.containment.innerHeight() - (tbBorderWidth + paddingTop);\n                } else {\n                    w = wnd.width() / zoomLevel - lrBorderWidth;\n                    h = wnd.height() / zoomLevel - (tbBorderWidth + paddingTop);\n                }\n                wrapper.css({\n                    width: w,\n                    height: h\n                });\n                that.options.width = w;\n                that.options.height = h;\n                that.resize();\n            },\n            refresh: function (options) {\n                var that = this, initOptions = that.options, element = $(that.element), iframe, showIframe, url;\n                if (!isPlainObject(options)) {\n                    options = { url: options };\n                }\n                options = extend({}, initOptions.content, options);\n                showIframe = defined(initOptions.iframe) ? initOptions.iframe : options.iframe;\n                url = options.url;\n                if (url) {\n                    if (!defined(showIframe)) {\n                        showIframe = !isLocalUrl(url);\n                    }\n                    if (!showIframe) {\n                        that._ajaxRequest(options);\n                    } else {\n                        iframe = element.find('.' + KCONTENTFRAME)[0];\n                        if (iframe) {\n                            iframe.src = url || iframe.src;\n                        } else {\n                            element.html(templates.contentFrame(extend({}, initOptions, { content: options })));\n                        }\n                        element.find('.' + KCONTENTFRAME).unbind('load' + NS).on('load' + NS, proxy(this._triggerRefresh, this));\n                    }\n                } else {\n                    if (options.template) {\n                        that.content(template(options.template)({}));\n                    }\n                    that.trigger(REFRESH);\n                }\n                element.toggleClass('k-window-iframecontent', !!showIframe);\n                return that;\n            },\n            _triggerRefresh: function () {\n                this.trigger(REFRESH);\n            },\n            _ajaxComplete: function () {\n                clearTimeout(this._loadingIconTimeout);\n                this.wrapper.find(REFRESHICON).removeClass(LOADING);\n            },\n            _ajaxError: function (xhr, status) {\n                this.trigger(ERROR, {\n                    status: status,\n                    xhr: xhr\n                });\n            },\n            _ajaxSuccess: function (contentTemplate) {\n                return function (data) {\n                    var html = data;\n                    if (contentTemplate) {\n                        html = template(contentTemplate)(data || {});\n                    }\n                    this.content(html, data);\n                    this.element.prop('scrollTop', 0);\n                    this.trigger(REFRESH);\n                };\n            },\n            _showLoading: function () {\n                this.wrapper.find(REFRESHICON).addClass(LOADING);\n            },\n            _ajaxRequest: function (options) {\n                this._loadingIconTimeout = setTimeout(proxy(this._showLoading, this), 100);\n                $.ajax(extend({\n                    type: 'GET',\n                    dataType: 'html',\n                    cache: false,\n                    error: proxy(this._ajaxError, this),\n                    complete: proxy(this._ajaxComplete, this),\n                    success: proxy(this._ajaxSuccess(options.template), this)\n                }, options));\n            },\n            _destroy: function () {\n                if (this.resizing) {\n                    this.resizing.destroy();\n                }\n                if (this.dragging) {\n                    this.dragging.destroy();\n                }\n                this.wrapper.off(NS).children(KWINDOWCONTENT).off(NS).end().find('.k-resize-handle,.k-window-titlebar').off(NS);\n                $(window).off('resize' + NS + this._marker);\n                $(window).off(MODAL_NS);\n                $(window).off(NS);\n                clearTimeout(this._loadingIconTimeout);\n                Widget.fn.destroy.call(this);\n                this.unbind(undefined);\n                kendo.destroy(this.wrapper);\n                this._removeOverlay(true);\n            },\n            destroy: function () {\n                this._destroy();\n                this.wrapper.empty().remove();\n                this.wrapper = this.appendTo = this.element = $();\n            },\n            _createWindow: function () {\n                var contentHtml = this.element, options = this.options, iframeSrcAttributes, wrapper, isRtl = kendo.support.isRtl(contentHtml);\n                if (options.scrollable === false) {\n                    contentHtml.css('overflow', 'hidden');\n                }\n                wrapper = $(templates.wrapper(options));\n                iframeSrcAttributes = contentHtml.find('iframe:not(.k-content)').map(function () {\n                    var src = this.getAttribute('src');\n                    this.src = '';\n                    return src;\n                });\n                wrapper.toggleClass('k-rtl', isRtl).append(contentHtml).find('iframe:not(.k-content)').each(function (index) {\n                    this.src = iframeSrcAttributes[index];\n                });\n                if (this.containment) {\n                    this.containment.prepend(wrapper);\n                } else if (this.appendTo) {\n                    wrapper.appendTo(this.appendTo);\n                }\n                wrapper.find('.k-window-title').css(isRtl ? 'left' : 'right', outerWidth(wrapper.find('.k-window-actions')) + 10);\n                contentHtml.css('visibility', '').show();\n                contentHtml.find('[data-role=editor]').each(function () {\n                    var editor = $(this).data('kendoEditor');\n                    if (editor) {\n                        editor.refresh();\n                    }\n                });\n                wrapper = contentHtml = null;\n            }\n        });\n        templates = {\n            wrapper: template('<div class=\\'k-widget k-window\\' />'),\n            action: template('<a role=\\'button\\' href=\\'\\\\#\\' class=\\'k-button k-bare k-button-icon k-window-action\\' aria-label=\\'#= name #\\'>' + '<span class=\\'k-icon k-i-#= name.toLowerCase() #\\'></span>' + '</a>'),\n            titlebar: template('<div class=\\'k-window-titlebar k-header\\'>' + '<span class=\\'k-window-title\\'>#= title #</span>' + '<div class=\\'k-window-actions\\' />' + '</div>'),\n            overlay: '<div class=\\'k-overlay\\' />',\n            contentFrame: template('<iframe frameborder=\\'0\\' title=\\'#= title #\\' class=\\'' + KCONTENTFRAME + '\\' ' + 'src=\\'#= content.url #\\'>' + 'This page requires frames in order to show content' + '</iframe>'),\n            resizeHandle: template('<div class=\\'k-resize-handle k-resize-#= data #\\'></div>')\n        };\n        function WindowResizing(wnd) {\n            var that = this;\n            that.owner = wnd;\n            that._preventDragging = false;\n            that._draggable = new Draggable(wnd.wrapper, {\n                filter: '>' + KWINDOWRESIZEHANDLES,\n                group: wnd.wrapper.id + '-resizing',\n                dragstart: proxy(that.dragstart, that),\n                drag: proxy(that.drag, that),\n                dragend: proxy(that.dragend, that)\n            });\n            that._draggable.userEvents.bind('press', proxy(that.addOverlay, that));\n            that._draggable.userEvents.bind('release', proxy(that.removeOverlay, that));\n        }\n        WindowResizing.prototype = {\n            addOverlay: function () {\n                this.owner.wrapper.append(templates.overlay);\n            },\n            removeOverlay: function () {\n                this.owner.wrapper.find(KOVERLAY).remove();\n            },\n            dragstart: function (e) {\n                var that = this;\n                var wnd = that.owner;\n                var wrapper = wnd.wrapper;\n                that._preventDragging = wnd.trigger(RESIZESTART);\n                if (that._preventDragging) {\n                    return;\n                }\n                that.elementPadding = parseInt(wrapper.css('padding-top'), 10);\n                that.initialPosition = kendo.getOffset(wrapper, 'position');\n                that.resizeDirection = e.currentTarget.prop('className').replace('k-resize-handle k-resize-', '');\n                that.initialSize = {\n                    width: wrapper.width(),\n                    height: wrapper.height()\n                };\n                wnd._updateBoundaries();\n                that.containerOffset = wnd.containment ? wnd.containment.position : kendo.getOffset(wnd.appendTo, 'position');\n                var offsetParent = wrapper.offsetParent();\n                if (offsetParent.is('html')) {\n                    that.containerOffset.top = that.containerOffset.left = 0;\n                } else {\n                    var marginTop = offsetParent.css('margin-top');\n                    var marginLeft = offsetParent.css('margin-left');\n                    var hasMargin = !zero.test(marginTop) || !zero.test(marginLeft);\n                    if (hasMargin) {\n                        var wrapperPosition = getPosition(wrapper[0]);\n                        var relativeElMarginLeft = wrapperPosition.left - that.containerOffset.left - that.initialPosition.left;\n                        var relativeElMarginTop = wrapperPosition.top - that.containerOffset.top - that.initialPosition.top;\n                        that._relativeElMarginLeft = relativeElMarginLeft > 1 ? relativeElMarginLeft : 0;\n                        that._relativeElMarginTop = relativeElMarginTop > 1 ? relativeElMarginTop : 0;\n                        that.initialPosition.left += that._relativeElMarginLeft;\n                        that.initialPosition.top += that._relativeElMarginTop;\n                    }\n                }\n                wrapper.children(KWINDOWRESIZEHANDLES).not(e.currentTarget).hide();\n                $(BODY).css(CURSOR, e.currentTarget.css(CURSOR));\n            },\n            drag: function (e) {\n                if (this._preventDragging) {\n                    return;\n                }\n                var that = this, wnd = that.owner, wrapper = wnd.wrapper, options = wnd.options, position = options.position, direction = that.resizeDirection, containerOffset = that.containerOffset, initialPosition = that.initialPosition, initialSize = that.initialSize, containmentContext = wnd.containment && !wnd._isPinned, rtl = kendo.support.isRtl(wnd.containment), leftRtlOffset = containmentContext && rtl && wnd.containment.innerWidth() > wnd.containment.width ? kendo.support.scrollbar() : 0, scrollOffset = containmentContext ? {\n                        top: wnd.containment.scrollTop(),\n                        left: wnd.containment.scrollLeft()\n                    } : {\n                        top: 0,\n                        left: 0\n                    }, newWidth, newHeight, windowBottom, windowRight, x = Math.max(e.x.location, 0), y = Math.max(e.y.location, 0);\n                if (direction.indexOf('e') >= 0) {\n                    if (wnd.containment && x - initialSize.width >= wnd.maxLeft - scrollOffset.left + containerOffset.left + leftRtlOffset) {\n                        newWidth = wnd.maxLeft + leftRtlOffset - initialPosition.left + initialSize.width - scrollOffset.left;\n                    } else {\n                        newWidth = x - initialPosition.left - containerOffset.left;\n                    }\n                    wrapper.width(constrain(newWidth, options.minWidth, options.maxWidth));\n                } else if (direction.indexOf('w') >= 0) {\n                    windowRight = initialPosition.left + initialSize.width + containerOffset.left;\n                    newWidth = constrain(windowRight - x, options.minWidth, options.maxWidth);\n                    position.left = windowRight - newWidth - containerOffset.left - leftRtlOffset - (that._relativeElMarginLeft || 0) + scrollOffset.left;\n                    if (wnd.containment && position.left <= wnd.minLeft) {\n                        position.left = wnd.minLeft;\n                        newWidth = constrain(windowRight - leftRtlOffset - position.left - containerOffset.left + scrollOffset.left, options.minWidth, options.maxWidth);\n                    }\n                    wrapper.css({\n                        left: position.left,\n                        width: newWidth\n                    });\n                }\n                var newWindowTop = y;\n                if (wnd.options.pinned) {\n                    newWindowTop -= $(window).scrollTop();\n                }\n                if (direction.indexOf('s') >= 0) {\n                    newHeight = newWindowTop - initialPosition.top - that.elementPadding - containerOffset.top;\n                    if (newWindowTop - initialSize.height - that.elementPadding >= wnd.maxTop + containerOffset.top - scrollOffset.top) {\n                        newHeight = wnd.maxTop - initialPosition.top + initialSize.height - scrollOffset.top;\n                    }\n                    wrapper.height(constrain(newHeight, options.minHeight, options.maxHeight));\n                } else if (direction.indexOf('n') >= 0) {\n                    windowBottom = initialPosition.top + initialSize.height + containerOffset.top;\n                    newHeight = constrain(windowBottom - newWindowTop, options.minHeight, options.maxHeight);\n                    position.top = windowBottom - newHeight - containerOffset.top - (that._relativeElMarginTop || 0) + scrollOffset.top;\n                    if (position.top <= wnd.minTop && wnd.containment) {\n                        position.top = wnd.minTop;\n                        newHeight = constrain(windowBottom - position.top - containerOffset.top + scrollOffset.top, options.minHeight, options.maxHeight);\n                    }\n                    wrapper.css({\n                        top: position.top,\n                        height: newHeight\n                    });\n                }\n                if (newWidth) {\n                    wnd.options.width = newWidth + 'px';\n                }\n                if (newHeight) {\n                    wnd.options.height = newHeight + 'px';\n                }\n                wnd.resize();\n            },\n            dragend: function (e) {\n                if (this._preventDragging) {\n                    return;\n                }\n                var that = this, wnd = that.owner, wrapper = wnd.wrapper;\n                wrapper.children(KWINDOWRESIZEHANDLES).not(e.currentTarget).show();\n                $(BODY).css(CURSOR, '');\n                if (wnd.touchScroller) {\n                    wnd.touchScroller.reset();\n                }\n                if (e.keyCode == 27) {\n                    wrapper.css(that.initialPosition).css(that.initialSize);\n                }\n                wnd.trigger(RESIZEEND);\n                return false;\n            },\n            destroy: function () {\n                if (this._draggable) {\n                    this._draggable.destroy();\n                }\n                this._draggable = this.owner = null;\n            }\n        };\n        function WindowDragging(wnd, dragHandle) {\n            var that = this;\n            that.owner = wnd;\n            that._preventDragging = false;\n            that._draggable = new Draggable(wnd.wrapper, {\n                filter: dragHandle,\n                group: wnd.wrapper.id + '-moving',\n                dragstart: proxy(that.dragstart, that),\n                drag: proxy(that.drag, that),\n                dragend: proxy(that.dragend, that),\n                dragcancel: proxy(that.dragcancel, that)\n            });\n            that._draggable.userEvents.stopPropagation = false;\n        }\n        WindowDragging.prototype = {\n            dragstart: function (e) {\n                var wnd = this.owner, draggable = wnd.options.draggable, element = wnd.element, actions = element.find('.k-window-actions'), containerOffset = kendo.getOffset(wnd.appendTo);\n                this._preventDragging = wnd.trigger(DRAGSTART) || !draggable;\n                if (this._preventDragging || wnd.isMaximized()) {\n                    return;\n                }\n                wnd.initialWindowPosition = kendo.getOffset(wnd.wrapper, 'position');\n                wnd.initialPointerPosition = {\n                    left: wnd.options.position.left,\n                    top: wnd.options.position.top\n                };\n                wnd.startPosition = {\n                    left: e.x.client - wnd.initialWindowPosition.left,\n                    top: e.y.client - wnd.initialWindowPosition.top\n                };\n                wnd._updateBoundaries();\n                if (!wnd.containment) {\n                    if (actions.length > 0) {\n                        wnd.minLeft = outerWidth(actions) + parseInt(actions.css('right'), 10) - outerWidth(element);\n                    } else {\n                        wnd.minLeft = 20 - outerWidth(element);\n                    }\n                    wnd.minLeft -= containerOffset.left;\n                    wnd.minTop = -containerOffset.top;\n                }\n                wnd.wrapper.append(templates.overlay).children(KWINDOWRESIZEHANDLES).hide();\n                $(BODY).css(CURSOR, e.currentTarget.css(CURSOR));\n            },\n            drag: function (e) {\n                var wnd = this.owner;\n                var position = wnd.options.position;\n                var axis = wnd.options.draggable.axis;\n                var left;\n                var top;\n                if (this._preventDragging || wnd.isMaximized()) {\n                    return;\n                }\n                if (!axis || axis.toLowerCase() === 'x') {\n                    left = e.x.client - wnd.startPosition.left;\n                    if (wnd.containment && !wnd._isPinned) {\n                        left += wnd.containment.scrollLeft();\n                    }\n                    position.left = constrain(left, wnd.minLeft, wnd.maxLeft);\n                }\n                if (!axis || axis.toLowerCase() === 'y') {\n                    top = e.y.client - wnd.startPosition.top;\n                    if (wnd.containment && !wnd._isPinned) {\n                        top += wnd.containment.scrollTop();\n                    }\n                    position.top = constrain(top, wnd.minTop, wnd.maxTop);\n                }\n                if (kendo.support.transforms) {\n                    $(wnd.wrapper).css('transform', 'translate(' + (position.left - wnd.initialPointerPosition.left) + 'px, ' + (position.top - wnd.initialPointerPosition.top) + 'px)');\n                } else {\n                    $(wnd.wrapper).css(position);\n                }\n            },\n            _finishDrag: function () {\n                var wnd = this.owner;\n                wnd.wrapper.children(KWINDOWRESIZEHANDLES).toggle(!wnd.options.isMinimized).end().find(KOVERLAY).remove();\n                $(BODY).css(CURSOR, '');\n            },\n            dragcancel: function (e) {\n                if (this._preventDragging) {\n                    return;\n                }\n                this._finishDrag();\n                e.currentTarget.closest(KWINDOW).css(this.owner.initialWindowPosition);\n            },\n            dragend: function () {\n                var wnd = this.owner;\n                if (this._preventDragging || wnd.isMaximized()) {\n                    return;\n                }\n                $(wnd.wrapper).css(wnd.options.position).css('transform', '');\n                this._finishDrag();\n                wnd.trigger(DRAGEND);\n                return false;\n            },\n            destroy: function () {\n                if (this._draggable) {\n                    this._draggable.destroy();\n                }\n                this._draggable = this.owner = null;\n            }\n        };\n        kendo.ui.plugin(Window);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}