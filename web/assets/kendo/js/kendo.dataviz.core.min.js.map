{"version": 3, "sources": ["kendo.dataviz.core.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "isArray", "Array", "addClass", "element", "classes", "idx", "className", "classArray", "indexOf", "removeClass", "SPACE_REGEX", "alignPathToPixel", "path", "offset", "stroke", "drawing", "defined", "segments", "anchor", "round", "translate", "clockwise", "angle1", "angle2", "x", "y", "isNumber", "isNaN", "isString", "STRING", "convertableToNumber", "isFinite", "isObject", "styleValue", "isSizeField", "field", "SIZE_STYLES_REGEX", "test", "elementStyles", "styles", "result", "field$1", "stylesArray", "getComputedStyle", "parseFloat", "getSpacing", "defaultSpacing", "spacing", "top", "right", "bottom", "left", "TOP", "RIGHT", "BOTTOM", "LEFT", "getTemplate", "template", "TemplateService", "compile", "isFunction", "content", "grep", "array", "callback", "hasClasses", "classNames", "names", "split", "inArray", "interpolateV<PERSON>ue", "start", "end", "progress", "COORD_PRECISION", "mousewheelDel<PERSON>", "e", "delta", "wheelDelta", "Math", "ceil", "floor", "detail", "setDefaultOptions", "type", "proto", "prototype", "sparseArrayLimits", "arr", "min", "MAX_VALUE", "max", "MIN_VALUE", "undefined", "autoMajorUnit", "scale", "relativeValue", "scaleMultiplier", "diff", "DEFAULT_PRECISION", "abs", "pow", "log", "rotatePoint", "cx", "cy", "angle", "theta", "rad", "Point", "cos", "sin", "numericComparer", "a", "b", "boxDiff", "r", "s", "c", "d", "g", "h", "boxes", "x1", "y1", "x2", "y2", "Box", "innerRadialStops", "currentStop", "stops", "usedSpace", "innerRadius", "radius", "currentStops", "rectToBox", "rect", "origin", "bottomRight", "createAxisTick", "tickOptions", "tickX", "tickY", "position", "tick", "Path", "color", "vertical", "moveTo", "lineTo", "createAxisGridLine", "gridLine", "lineStart", "lineEnd", "line", "dashType", "absoluteDateDiff", "getTime", "offsetDiff", "getTimezoneOffset", "TIME_PER_MINUTE", "addTicks", "date", "ticks", "Date", "toDate", "startOfWeek", "weekStartDay", "daysToSubtract", "day", "getDay", "TIME_PER_DAY", "adjustDST", "hours", "getHours", "setHours", "addHours", "tzDiff", "roundedDate", "setMinutes", "TIME_PER_HOUR", "addDuration", "dateValue", "unit", "YEARS", "getFullYear", "MONTHS", "getMonth", "WEEKS", "DAYS", "getDate", "HOURS", "MINUTES", "getSeconds", "setSeconds", "SECONDS", "TIME_PER_SECOND", "MILLISECONDS", "getMilliseconds", "setMilliseconds", "floorDate", "ceilDate", "dateC<PERSON>parer", "dateDiff", "toTime", "dateEquals", "timeIndex", "baseUnit", "TIME_PER_UNIT", "dateIndex", "baseUnitStep", "index", "startDate", "duration", "lteDateIndex", "sortedDates", "currentDate", "low", "high", "parseDate", "intlService", "parseDates", "dates", "categoryRange", "categories", "range", "_range", "autoBaseUnit", "startUnit", "startStep", "unitSteps", "step", "nextStep", "categoryLimits", "span", "autoBaseUnitSteps", "maxDateGroups", "autoUnit", "FIT", "autoUnitIx", "BASE_UNITS", "units", "totalUnits", "slice", "shift", "last", "defaultBaseUnit", "lastCategory", "categoryIx", "category", "count", "minDiff", "TIME_PER_YEAR", "TIME_PER_MONTH", "TIME_PER_WEEK", "initUnit", "toLowerCase", "useDefault", "AUTO", "autoAxisMin", "narrow", "axisMin", "minValue", "ZERO_THRESHOLD", "autoAxisMax", "axisMax", "maxValue", "limitCoordinate", "COORDINATE_LIMIT", "autoAxisOptions", "seriesMin", "seriesMax", "narrowRange", "autoMin", "autoMax", "majorUnit", "autoOptions", "roundToMajorUnit", "remainderClose", "totalAxisOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "axisOptions", "userOptions", "userSetMin", "userSetMax", "userSetLimits", "minorUnit", "divisor", "ratio", "remainder", "threshold", "timeUnits", "applyDefaults", "baseUnitTime", "userMajorUnit", "actualUnits", "unitsToAdd", "head", "tail", "initRange", "axisCrossingValue", "throwNegativeValuesError", "autoAxisMin$1", "base", "autoAxisMax$1", "logMaxRemainder", "Error", "angularDistance", "numberSign", "Group", "geometry", "Rect", "Circle", "geometryTransform", "Segment", "dataviz", "__common_getter_js", "ARC", "AXIS_LABEL_CLICK", "BLACK", "CENTER", "CIRCLE", "CROSS", "DATE", "DEFAULT_FONT", "DEFAULT_HEIGHT", "DEFAULT_WIDTH", "END", "FORMAT_REGEX", "HEIGHT", "HIGHLIGHT_ZINDEX", "INSIDE", "NONE", "NOTE_CLICK", "NOTE_HOVER", "NOTE_LEAVE", "OBJECT", "OUTSIDE", "START", "TRIANGLE", "VALUE", "WHITE", "WIDTH", "X", "Y", "constants", "defaultImplementation", "IntlService", "FORMAT_REPLACE_REGEX", "FormatService", "ChartService", "current$1", "DomEventsBuilder", "current$2", "services", "HashMap", "TRIGGER", "InstanceObserver", "ref", "append", "bindEvents", "deg", "elementOffset", "elementSize", "eventElement", "eventCoordinates", "limitValue", "unbindEvents", "valueOrDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ring", "Sector", "DIRECTION_ANGLE", "ShapeBuilder", "ChartElement", "BoxElement", "ShapeElement", "LINEAR", "RADIAL", "GRADIENTS", "RootElement", "FloatElement", "DrawingText", "Text", "ROWS_SPLIT_REGEX", "TextBox", "Title", "AxisLabel", "DEFAULT_ICON_SIZE", "DEFAULT_LABEL_COLOR", "Note", "Axis", "TIME_PER_MILLISECOND", "MIN_CATEGORY_POINTS_RANGE", "CategoryAxis", "DateLabelFormats", "EmptyDateRange", "DateRange", "DateCategoryAxis", "MIN_VALUE_RANGE", "NumericAxis", "DateValueAxis", "DEFAULT_MAJOR_UNIT", "LogarithmicAxis", "GridLinesMixin", "RadarCategoryAxis", "PolarAxis", "RadarNumericAxisMixin", "RadarNumericAxis", "RadarLogarithmicAxis", "WEIGHT", "EXTREMUM_ALLOWED_DEVIATION", "CurveProcessor", "transform", "getter", "Number", "format", "toString", "register", "userImplementation", "Object", "defineProperties", "implementation", "_intlService", "auto", "formatString", "intl", "values", "len", "arguments", "match", "apply", "concat", "localeAuto", "locale", "placeholderFormat", "parseInt", "substring", "fn", "set", "chart", "context", "sender", "rtl", "notify", "name", "args", "trigger", "isPannable", "axis", "pannable", "lock", "create", "events", "_key", "observer", "handlers", "handlerMap", "isDefaultPrevented", "callObserver", "fnName", "requiresHandlers", "this$1", "fetchFonts", "fonts", "state", "depth", "MAX_DEPTH", "keys", "for<PERSON>ach", "loadFonts", "promises", "font", "load", "logToConsole", "Promise", "all", "then", "preloadFonts", "clone", "equals", "point", "rotate", "center", "degrees", "cosT", "sinT", "multiply", "distanceTo", "dx", "dy", "sqrt", "onCircle", "radians", "move", "wrap", "targetBox", "wrapPoint", "arrayPoint", "snapTo", "alignTo", "targetCenter", "shrink", "dw", "dh", "expand", "pad", "padding", "unpad", "containsPoint", "points", "getHash", "overlaps", "rotation", "r1", "r2", "r3", "r4", "toRect", "hasSize", "align", "alignment", "c1", "c2", "sizeFunc", "startAngle", "middle", "setRadius", "newRadius", "radianAngle", "ax", "ay", "adjacentBox", "distance", "sector", "midAndle", "midPoint", "hw", "hh", "sa", "ca", "p", "endAngle", "vector", "startPoint", "startVector", "endPoint", "endVector", "dist", "getBBox", "angles", "allAngles", "startAngleIndex", "endAngleIndex", "call", "createRing", "arc", "innerEnd", "Arc", "radiusX", "radiusY", "fromArc", "close", "pointAt", "children", "initUserOptions", "reflow", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "animation", "getRoot", "parent", "getSender", "service", "getService", "chartService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenCount", "item", "arguments$1", "renderVisual", "visible", "createVisual", "addVisual", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createAnimation", "renderComplete", "visual", "chartElement", "appendVisual", "zIndex", "Animation", "childVisual", "noclip", "clipRoot", "stackRoot", "stackVisual", "isStackRoot", "pos", "sibling", "here", "visuals", "insert", "traverse", "child", "closest", "matched", "hasHighlight", "highlight", "createHighlight", "toggleHighlight", "show", "highlightOptions", "customVisual", "_highlight", "fill", "opacity", "highlightVisualArgs", "series", "dataItem", "percentage", "runningTotal", "total", "createGradientOverlay", "gradientOptions", "overlay", "createGradient", "closed", "elements", "margin", "shrinkToFit", "hasSetSize", "borderWidth", "border", "reflowPaddingBox", "vAlign", "paddingBox", "contentBox", "hasBox", "background", "fromRect", "visualStyle", "cursor", "pointData", "getElement", "halfWidth", "fromPoints", "MultiPath", "visualOptions", "glass", "sharpBevel", "roundedBevel", "roundedGlass", "supportVML", "sharpGlass", "bubbleShadow", "rootOptions", "gradients", "currentBox", "createBackground", "drawingGradient", "hashCode", "gradient", "LinearGradient", "RadialGrad<PERSON>", "cleanGradients", "_observers", "_initDirection", "groupAxis", "elementAxis", "groupSizeField", "elementSizeField", "groupSpacing", "elementSpacing", "vSpacing", "reflowChildren", "groupStart", "groupIdx", "group", "groupElements", "elementStart", "groupElementsCount", "elementSize$$1", "groupElementStart", "elementBox", "ref$1", "groupOptions", "groups", "groupsSize", "maxGroupElementsSize", "groupsCount", "groupsStart", "alignStart", "groupSize", "maxSize", "groupElementsSize", "topLeft", "data", "_initContainer", "_autoReflow", "rowIdx", "rows", "floatElement", "textOptions", "container", "trim", "visualBox", "visualFn", "_boxReflow", "visualContext", "clippedBBox", "normalBox", "rotatedBox", "rotationTransform", "getDefaultVisual", "boxCenter", "buildTitle", "defaultOptions", "title", "titleOptions", "culture", "click", "widget", "alignRotation", "bbox", "matrix", "rotationMatrix", "<PERSON><PERSON><PERSON><PERSON>", "alignAxis", "distanceAxis", "axisAnchor", "topRight", "bottomLeft", "distanceLeft", "distanceRight", "alignEnd", "alignCenter", "transformCopy", "render", "hide", "label", "icon", "<PERSON><PERSON><PERSON><PERSON>", "noteTemplate", "alias<PERSON><PERSON>", "wrapperBox", "linePoints", "createLine", "defaultVisual", "eventArgs", "preventDefault", "over", "out", "labels", "majorTickSize", "minorTickSize", "minorTicks", "minorTickType", "majorTicks", "majorTickType", "initFields", "_deferLabels", "createLabels", "createTitle", "createNotes", "labelsRange", "skip", "labelsCount", "labelOptions", "<PERSON><PERSON><PERSON><PERSON>", "autoRotateLabels", "createAxisLabel", "clearTitle", "clear", "lineBox", "mirror", "axisX", "axisY", "lineWidth", "visualSize", "note", "notes", "items", "parseNoteValue", "reverse", "createPlotBands", "gridLinesVisual", "gridLines", "_gridLines", "createTicks", "lineGroup", "tickPositions", "skip<PERSON><PERSON><PERSON>", "tickLineOptions", "getMajorTickPositions", "getMinorTickPositions", "_alignLines", "_lineGroup", "getActualTickSize", "tickSize", "_backgroundPath", "altAxis", "slotX", "slotY", "bandRect", "plotBands", "<PERSON><PERSON><PERSON>", "_plotbandGroup", "pane", "axes", "getSlot", "from", "to", "createGridLines", "axisLineVisible", "linePos", "lineOptions", "minorGridLines", "majorGridLines", "labelSize", "sizeFn", "titleSize", "space", "rootBox", "boxSize", "maxLabelSize", "arrangeTitle", "arrangeLabels", "arrangeNotes", "getLabelsTickPositions", "labelTickIndex", "tickIx", "labelPos", "labelBox", "firstTickPosition", "nextTickPosition", "labelX", "labelY", "labelsBetweenTicks", "labelOffset", "idx$1", "slot", "shouldRenderNote", "noteSlot", "secondAxis", "axisLabelText", "tmpl", "limit", "<PERSON><PERSON><PERSON><PERSON>", "maxLabelOffset", "startTick", "endTick", "offsetField", "startPosition", "endPosition", "maxStartOffset", "maxEndOffset", "limitRange", "rangeSize", "valueRange", "justified", "prepareUserOptions", "years", "months", "weeks", "days", "minutes", "seconds", "milliseconds", "_ticks", "categoriesHash", "copy", "definedMin", "definedMax", "srcCategories", "rangeIndices", "totalRange", "totalRangeIndices", "roundedRange", "_seriesMax", "scaleOptions", "hideOutOfRangeLabels", "valueAxis", "firstLabel", "getTicks", "labelTicks", "tickIndices", "stepSize", "indices", "getTickPositions", "positions", "hasMinor", "cache", "_hash", "filterOutOfRangePositions", "inRange", "startIndex", "endIndex", "p1", "p2", "slotBox", "singleSlot", "limitSlot", "limittedSlot", "categoryIndex", "pointCategoryIndex", "startValue", "getCategory", "totalIndex", "categoryAt", "categoriesCount", "translateRange", "zoomRange", "rate", "totalMin", "totalMax", "scaleRange", "dataItems", "hideOutOfRangeNotes", "pan", "pointsRange", "diffStart", "diffEnd", "rangeMin", "rangeMax", "_categoriesMap", "currentRangeIndices", "totalCount", "mapCategories", "map$$1", "displayIndices", "displayRange", "valueIndex", "valuesCount", "dateAt", "roundToBaseUnit", "lowerEnd", "expandEnd", "roundToTotalStep", "justifyEnd", "valueStart", "displayStart", "valueEnd", "displayEnd", "minIdx", "maxIdx", "_indices", "last$$1", "_values", "upper", "next", "roundedStep", "dataRange", "maxDivisions", "divisionOptions", "dataRangeOptions", "_parsed", "panning", "userSetBaseUnit", "userSetBaseUnitStep", "divisionRange", "splice", "rounds", "totalLimits", "panRange", "isEmpty", "datesRange", "indicesRange", "fit", "maxDiff", "rangeDiff", "baseUnitIndex", "autoBaseUnitStep", "stepIndex", "unitFormat", "dateFormats", "date<PERSON><PERSON><PERSON>", "totalOptions", "totalMajorUnit", "getDivisions", "<PERSON><PERSON><PERSON><PERSON>", "lineSize", "divisions", "dir", "startEdge", "skipStep", "getValue", "valueOffset", "endValue", "isValidRange", "newRange", "axisCrossingValues", "timeRange", "exact", "limittedRange", "logMin", "logMax", "floorMax", "traverseMajorTicksPositions", "tickPosition", "traverseMinorTicksPositions", "power", "_lineOptions", "minorOptions", "ref$2", "_minorIntervalOptions", "minorStep", "acceptOptionsRange", "acceptNewRange", "nextValue", "difference", "majorAngles", "minorAngles", "<PERSON><PERSON><PERSON><PERSON>", "majorGridLineAngles", "renderMajorGridLines", "minorGridLineAngles", "renderMinorGridLines", "renderGridLines", "radiusCallback", "circle", "gridLineAngles", "skipAngles", "divs", "intervals", "altAxisVisible", "alpha", "intervalAngle", "reflowLabels", "skipOption", "stepOption", "divCount", "divAngle", "majorIntervals", "minorIntervals", "interval", "minorAngle", "minorRadius", "band", "ring", "plotBandSlot", "slotStart", "toValue", "slots", "totalDivs", "slotAngle", "fromValue", "instanceOptions", "tmp", "atan2", "PI", "bandStyle", "shape", "polarAxis", "plotBandPoints", "innerPoints", "outerPoints", "innerCircle", "outerCircle", "radarMajorGridLinePositions", "radarMinorGridLinePositions", "tickRadius", "angleIx", "midAngle", "gamma", "beta", "axisType", "minorSkipStep", "process", "dataPoints", "p0", "tangent", "initialControlPoint", "lastControlPoint", "controlPoints", "tangent$1", "cp0", "controlPoints$1", "cp1", "controlPoints$2", "tangent$2", "removeDuplicates", "pop", "controlOut", "firstControlPoint", "secondControlPoint", "invertAxis", "lineFunction", "calculateFunction", "isLine", "monotonic", "sign", "oldXField", "xField", "yField", "restrict", "switchOrientation", "isMonotonicByField", "restrictControlPoint", "cp", "p3", "t1", "t2", "xValue", "yValue", "controlPoint", "Gradients", "draw", "SASS_THEMES", "ExportMixin", "skipLegacy", "exportVisual", "exportSVG", "exportImage", "exportPDF", "svg", "imageDataURL", "Surface", "exportGroup", "surface", "image", "support", "canvas", "css", "display", "appendTo", "_rootElement", "toDataURL", "remove", "Point2D", "Box2D", "mwDelta", "originalEvent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,2BACH,aACA,iBACDD,IACL,YACG,SAAUE,GA4FP,QAASkF,GAAQrC,GACb,MAAOsC,OAAMD,QAAQrC,GAEzB,QAASuC,GAASC,EAASC,GAA3B,GAEaC,GACDC,EAFJC,EAAaP,EAAQI,GAAWA,GAAWA,EAC/C,KAASC,EAAM,EAAGA,EAAME,EAAWvE,OAAQqE,IACnCC,EAAYC,EAAWF,GACvBF,EAAQG,UAAUE,QAAQF,UAC1BH,EAAQG,WAAa,IAAMA,GAKvC,QAASG,GAAYN,EAASG,GACtBH,GAAWA,EAAQG,YACnBH,EAAQG,UAAYH,EAAQG,UAAUpF,QAAQoF,EAAW,IAAIpF,QAAQwF,GAAa,MAG1F,QAASC,GAAiBC,GAA1B,GAOa9E,GANL+E,EAAS,EAMb,KALID,EAAKrC,QAAQuC,QAAU9D,MAAM+D,QAAQ9D,KAAK+D,QAAQJ,EAAKrC,QAAQuC,OAAO3E,QAClEyE,EAAKrC,QAAQuC,OAAO3E,MAAQ,IAAM,IAClC0E,EAAS,GAGR/E,EAAI,EAAGA,EAAI8E,EAAKK,SAASjF,OAAQF,IACtC8E,EAAKK,SAASnF,GAAGoF,SAASC,MAAM,GAAGC,UAAUP,EAAQA,EAEzD,OAAOD,GAEX,QAASS,GAAUC,EAAQC,GACvB,OAAQD,EAAOE,EAAID,EAAOE,EAAIH,EAAOG,EAAIF,EAAOC,EAAI,EAExD,QAASE,GAAS/D,GACd,MAAwB,gBAAVA,KAAuBgE,MAAMhE,GAE/C,QAASiE,GAASjE,GACd,aAAcA,KAAUkE,GAE5B,QAASC,GAAoBnE,GACzB,MAAO+D,GAAS/D,IAAUiE,EAASjE,IAAUoE,SAASpE,GAE1D,QAASqE,GAASrE,GACd,MAAwB,gBAAVA,GAElB,QAASsE,GAAWtE,GAChB,MAAI+D,GAAS/D,GACFA,EAAQ,KAEZA,EAGX,QAASuE,GAAYC,GACjB,MAAOC,IAAkBC,KAAKF,GAElC,QAASG,GAAcnC,EAASoC,GAAhC,GAGYC,GACAjG,EACK8D,EACD8B,EAKCM,EAVTC,EAAcd,EAASW,IAAWA,GAAUA,CAChD,IAAIvC,EAAQ0C,GAAc,CAGtB,IAFIF,KACAjG,EAAQQ,OAAO4F,iBAAiBxC,GAC3BE,EAAM,EAAGA,EAAMqC,EAAY1G,OAAQqE,IACpC8B,EAAQO,EAAYrC,GACxBmC,EAAOL,GAASD,EAAYC,GAASS,WAAWrG,EAAM4F,IAAU5F,EAAM4F,EAE1E,OAAOK,GACJ,GAAIR,EAASO,GAChB,IAASE,IAAWF,GAChBpC,EAAQ5D,MAAMkG,GAAWR,EAAWM,EAAOE,IAIvD,QAASI,GAAWlF,EAAOmF,GACA,SAAnBA,IACAA,EAAiB,EAErB,IAAIC,IACAC,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRC,KAAM,EAUV,OARqB,gBAAVxF,GACPoF,EAAQK,IAAOL,EAAQM,IAASN,EAAQO,IAAUP,EAAQQ,IAAQ5F,GAElEoF,EAAQK,IAAOzF,EAAMyF,KAAQN,EAC7BC,EAAQM,IAAS1F,EAAM0F,KAAUP,EACjCC,EAAQO,IAAU3F,EAAM2F,KAAWR,EACnCC,EAAQQ,IAAQ5F,EAAM4F,KAAST,GAE5BC,EAmIX,QAASS,GAAYjF,GACD,SAAZA,IACAA,KAEJ,IAAIkF,EAMJ,OALIlF,GAAQkF,SACRlF,EAAQkF,SAAWA,EAAWC,GAAgBC,QAAQpF,EAAQkF,UACvDG,GAAWrF,EAAQsF,WAC1BJ,EAAWlF,EAAQsF,SAEhBJ,EAEX,QAASK,GAAKC,EAAOC,GAArB,GAGa3D,GAFLrE,EAAS+H,EAAM/H,OACfwG,IACJ,KAASnC,EAAM,EAAGA,EAAMrE,EAAQqE,IACxB2D,EAASD,EAAM1D,KACfmC,EAAO/G,KAAKsI,EAAM1D,GAG1B,OAAOmC,GAEX,QAASyB,GAAW9D,EAAS+D,GAA7B,GAEYC,GACK9D,CAFb,IAAIF,EAAQG,UAER,IADI6D,EAAQD,EAAWE,MAAM,KACpB/D,EAAM,EAAGA,EAAM8D,EAAMnI,OAAQqE,IAClC,GAAIF,EAAQG,UAAUE,QAAQ2D,EAAM9D,SAChC,OAAO,EAiBvB,QAASgE,GAAQ1G,EAAOoG,GACpB,GAAIA,EACA,MAAOA,GAAMvD,QAAQ7C,QAG7B,QAAS2G,GAAiBC,EAAOC,EAAKC,GAClC,MAAOzH,OAAM+D,QAAQ9D,KAAKkE,MAAMoD,GAASC,EAAMD,GAASE,EAAUC,IAsCtE,QAAS9G,GAAImG,EAAOC,GAApB,GAGa3D,GACD1C,EAHJ3B,EAAS+H,EAAM/H,OACfwG,IACJ,KAASnC,EAAM,EAAGA,EAAMrE,EAAQqE,IACxB1C,EAAQqG,EAASD,EAAM1D,IACvBrD,MAAM+D,QAAQ9D,KAAK+D,QAAQrD,IAC3B6E,EAAO/G,KAAKkC,EAGpB,OAAO6E,GAEX,QAASmC,GAAgBC,GACrB,GAAIC,GAAQ,CAQZ,OAPID,GAAEE,aACFD,GAASD,EAAEE,WAAa,IACxBD,EAAQA,EAAQ,EAAIE,KAAKC,KAAKH,GAASE,KAAKE,MAAMJ,IAElDD,EAAEM,SACFL,EAAQ7H,MAAM+D,QAAQ9D,KAAKkE,MAAMyD,EAAEM,OAAS,IAEzCL,EA6DX,QAASM,GAAkBC,EAAM7G,GAC7B,GAAI8G,GAAQD,EAAKE,SAEbD,GAAM9G,QADN8G,EAAM9G,QACUmB,MAAe2F,EAAM9G,QAASA,GAE9BA,EAGxB,QAASgH,GAAkBC,GAA3B,GAGanF,GAASrE,EACV2B,EAHJ8H,EAAMC,GACNC,EAAMC,EACV,KAASvF,EAAM,EAAGrE,EAASwJ,EAAIxJ,OAAQqE,EAAMrE,EAAQqE,IAC7C1C,EAAQ6H,EAAInF,GACF,OAAV1C,GAAkBoE,SAASpE,KAC3B8H,EAAMV,KAAKU,IAAIA,EAAK9H,GACpBgI,EAAMZ,KAAKY,IAAIA,EAAKhI,GAG5B,QACI8H,IAAKA,IAAQC,GAAYG,OAAYJ,EACrCE,IAAKA,IAAQC,GAAYC,OAAYF,GAG7C,QAASG,GAAcL,EAAKE,GAA5B,GAQQI,GACAC,EACAC,EATAC,EAAO/E,GAAMwE,EAAMF,EAAKU,GAAoB,EAChD,IAAa,IAATD,EAAY,CACZ,GAAY,IAARP,EACA,MAAO,EAEXO,GAAOnB,KAAKqB,IAAIT,GAcpB,MAZII,GAAQhB,KAAKsB,IAAI,GAAItB,KAAKE,MAAMF,KAAKuB,IAAIJ,GAAQnB,KAAKuB,IAAI,MAC1DN,EAAgB7E,GAAM+E,EAAOH,EAAOI,IACpCF,EAAkB,EAElBA,EADAD,EAAgB,SACE,GACXA,EAAgB,SACL,GACXA,EAAgB,SACL,EAEA,EAEf7E,GAAM4E,EAAQE,EAAiBE,IAiO1C,QAASI,GAAY/E,EAAGC,EAAG+E,EAAIC,EAAIC,GAC/B,GAAIC,GAAQC,GAAIF,EAChB,OAAO,IAAIG,IAAML,GAAMhF,EAAIgF,GAAMzB,KAAK+B,IAAIH,IAAUlF,EAAIgF,GAAM1B,KAAKgC,IAAIJ,GAAQF,GAAMjF,EAAIgF,GAAMzB,KAAKgC,IAAIJ,IAAUlF,EAAIgF,GAAM1B,KAAK+B,IAAIH,IA4GzI,QAASK,GAAgBC,EAAGC,GACxB,MAAOD,GAAIC,EA2lBf,QAASC,GAAQC,EAAGC,GAApB,GAIQJ,GACAC,EACAI,EACAC,EACA3C,EACAhK,EACA4M,EACAC,EACAC,CAXJ,OAAIN,GAAEO,KAAON,EAAEM,IAAMP,EAAEQ,KAAOP,EAAEO,IAAMR,EAAES,KAAOR,EAAEQ,IAAMT,EAAEU,KAAOT,EAAES,GACvDT,GAEPJ,EAAIlC,KAAKU,IAAI2B,EAAEO,GAAIN,EAAEM,IACrBT,EAAInC,KAAKY,IAAIyB,EAAEO,GAAIN,EAAEM,IACrBL,EAAIvC,KAAKU,IAAI2B,EAAES,GAAIR,EAAEQ,IACrBN,EAAIxC,KAAKY,IAAIyB,EAAES,GAAIR,EAAEQ,IACrBjD,EAAIG,KAAKU,IAAI2B,EAAEQ,GAAIP,EAAEO,IACrBhN,EAAImK,KAAKY,IAAIyB,EAAEQ,GAAIP,EAAEO,IACrBJ,EAAIzC,KAAKU,IAAI2B,EAAEU,GAAIT,EAAES,IACrBL,EAAI1C,KAAKY,IAAIyB,EAAEU,GAAIT,EAAES,IACrBJ,KACJA,EAAM,GAAK,GAAIK,IAAIb,EAAGtC,EAAG0C,EAAG1M,GAC5B8M,EAAM,GAAK,GAAIK,IAAId,EAAGrM,EAAGsM,EAAGM,GAC5BE,EAAM,GAAK,GAAIK,IAAIT,EAAG1M,EAAG2M,EAAGC,GAC5BE,EAAM,GAAK,GAAIK,IAAIb,EAAGM,EAAGF,EAAGG,GACxBL,EAAEO,KAAOV,GAAKG,EAAEQ,KAAOhD,GAAKyC,EAAEM,KAAOV,GAAKI,EAAEO,KAAOhD,GACnD8C,EAAM,GAAK,GAAIK,IAAId,EAAGrC,EAAGsC,EAAGtM,GAC5B8M,EAAM,GAAK,GAAIK,IAAIT,EAAGE,EAAGD,EAAGE,KAE5BC,EAAM,GAAK,GAAIK,IAAIT,EAAG1C,EAAG2C,EAAG3M,GAC5B8M,EAAM,GAAK,GAAIK,IAAId,EAAGO,EAAGN,EAAGO,IAEzB3D,EAAK4D,EAAO,SAAU5I,GACzB,MAAOA,GAAI1C,SAAW,GAAK0C,EAAI3C,QAAU,IAC1C,IA0FP,QAAS6L,GAAiBzJ,GAA1B,GAKazC,GACDmM,EALJC,EAAQ3J,EAAQ2J,MAChBC,EAAY5J,EAAQ6J,YAAc7J,EAAQ8J,OAAS,IACnDrM,EAASkM,EAAMlM,OACfsM,IACJ,KAASxM,EAAI,EAAGA,EAAIE,EAAQF,IACpBmM,EAAcnN,EAAEqC,UAAW+K,EAAMpM,IACrCmM,EAAYpH,QAAUoH,EAAYpH,QAAU,IAAMsH,GAAaA,GAAa,IAC5EG,EAAa7M,KAAKwM,EAEtB,OAAOK,GAsLX,QAASC,GAAUC,GAAnB,GACQC,GAASD,EAAKC,OACdC,EAAcF,EAAKE,aACvB,OAAO,IAAIX,IAAIU,EAAOjH,EAAGiH,EAAOhH,EAAGiH,EAAYlH,EAAGkH,EAAYjH,GAuflE,QAASkH,GAAepK,EAASqK,GAAjC,GACQC,GAAQtK,EAAQsK,MAChBC,EAAQvK,EAAQuK,MAChBC,EAAWxK,EAAQwK,SACnBC,EAAO,GAAIC,KACXnI,QACI3E,MAAOyM,EAAYzM,MACnB+M,MAAON,EAAYM,QAS3B,OANI3K,GAAQ4K,SACRH,EAAKI,OAAOP,EAAOE,GAAUM,OAAOR,EAAQD,EAAYvL,KAAM0L,GAE9DC,EAAKI,OAAOL,EAAUD,GAAOO,OAAON,EAAUD,EAAQF,EAAYvL,MAEtEsD,EAAiBqI,GACVA,EAEX,QAASM,GAAmB/K,EAASgL,GAArC,GACQC,GAAYjL,EAAQiL,UACpBC,EAAUlL,EAAQkL,QAClBV,EAAWxK,EAAQwK,SACnBW,EAAO,GAAIT,KACXnI,QACI3E,MAAOoN,EAASpN,MAChB+M,MAAOK,EAASL,MAChBS,SAAUJ,EAASI,WAS3B,OANIpL,GAAQ4K,SACRO,EAAKN,OAAOI,EAAWT,GAAUM,OAAOI,EAASV,GAEjDW,EAAKN,OAAOL,EAAUS,GAAWH,OAAON,EAAUU,GAEtD9I,EAAiB+I,GACVA,EA8qBX,QAASE,GAAiB3C,EAAGC,GAA7B,GACQhB,GAAOe,EAAE4C,UAAY3C,EACrB4C,EAAa7C,EAAE8C,oBAAsB7C,EAAE6C,mBAC3C,OAAO7D,GAAO4D,EAAaE,GAE/B,QAASC,GAASC,EAAMC,GACpB,MAAO,IAAIC,MAAKF,EAAKL,UAAYM,GAErC,QAASE,GAAO1M,GACZ,GAAI6E,EAMJ,OALI7E,aAAiByM,MACjB5H,EAAS7E,EACFA,IACP6E,EAAS,GAAI4H,MAAKzM,IAEf6E,EAEX,QAAS8H,GAAYJ,EAAMK,GAA3B,GAIQC,GACAC,CACJ,IALqB,SAAjBF,IACAA,EAAe,GAEfC,EAAiB,EACjBC,EAAMP,EAAKQ,UACV/I,MAAM8I,GACP,KAAOA,IAAQF,GACC,IAARE,EACAA,EAAM,EAENA,IAEJD,GAGR,OAAOP,GAASC,GAAOM,EAAiBG,IAE5C,QAASC,GAAUV,EAAMW,GACrB,MAAc,KAAVA,GAAmC,KAApBX,EAAKY,aACpBZ,EAAKa,SAASb,EAAKY,WAAa,IACzB,GAIf,QAASE,GAASd,EAAMW,GAAxB,GAGQI,GAFAC,EAAc,GAAId,MAAKF,EAG3B,OAFAgB,GAAYC,WAAW,EAAG,EAAG,GACzBF,GAAUf,EAAKH,oBAAsBmB,EAAYnB,qBAAuBC,GACrEC,EAASiB,EAAaD,EAASJ,EAAQO,IAElD,QAASC,GAAYC,EAAW3N,EAAO4N,EAAMhB,GAA7C,GAGYL,GACAW,EAHJrI,EAAS8I,CAgCb,OA/BIA,KACIpB,EAAOG,EAAOiB,GACdT,EAAQX,EAAKY,WACbS,IAASC,IACThJ,EAAS,GAAI4H,MAAKF,EAAKuB,cAAgB9N,EAAO,EAAG,GACjDiN,EAAUpI,EAAQ,IACX+I,IAASG,IAChBlJ,EAAS,GAAI4H,MAAKF,EAAKuB,cAAevB,EAAKyB,WAAahO,EAAO,GAC/DiN,EAAUpI,EAAQqI,IACXU,IAASK,IAChBpJ,EAAS6I,EAAYf,EAAYJ,EAAMK,GAAuB,EAAR5M,EAAWkO,IACjEjB,EAAUpI,EAAQqI,IACXU,IAASM,IAChBrJ,EAAS,GAAI4H,MAAKF,EAAKuB,cAAevB,EAAKyB,WAAYzB,EAAK4B,UAAYnO,GACxEiN,EAAUpI,EAAQqI,IACXU,IAASQ,GAChBvJ,EAASwI,EAASd,EAAMvM,GACjB4N,IAASS,IAChBxJ,EAASyH,EAASC,EAAMvM,EAAQqM,IAC5BxH,EAAOyJ,aAAe,GACtBzJ,EAAO0J,WAAW,IAEfX,IAASY,GAChB3J,EAASyH,EAASC,EAAMvM,EAAQyO,IACzBb,IAASc,KAChB7J,EAASyH,EAASC,EAAMvM,IAExB4N,IAASc,IAAgB7J,EAAO8J,kBAAoB,GACpD9J,EAAO+J,gBAAgB,IAGxB/J,EAEX,QAASgK,GAAUtC,EAAMqB,EAAMhB,GAC3B,MAAOc,GAAYhB,EAAOH,GAAO,EAAGqB,EAAMhB,GAE9C,QAASkC,GAASnB,EAAWC,EAAMhB,GAC/B,GAAIL,GAAOG,EAAOiB,EAClB,OAAIpB,IAAQsC,EAAUtC,EAAMqB,EAAMhB,GAAcV,YAAcK,EAAKL,UACxDK,EAEJmB,EAAYnB,EAAM,EAAGqB,EAAMhB,GAEtC,QAASmC,GAAazF,EAAGC,GACrB,MAAID,IAAKC,EACED,EAAE4C,UAAY3C,EAAE2C,aAI/B,QAAS8C,GAAS1F,EAAGC,GACjB,MAAOD,GAAE4C,UAAY3C,EAEzB,QAAS0F,GAAOjP,GAAhB,GAEY6E,GACKnC,CAFb,IAAIL,EAAQrC,GAAQ,CAEhB,IADI6E,KACKnC,EAAM,EAAGA,EAAM1C,EAAM3B,OAAQqE,IAClCmC,EAAO/G,KAAKmR,EAAOjP,EAAM0C,IAE7B,OAAOmC,GACJ,GAAI7E,EACP,MAAO0M,GAAO1M,GAAOkM,UAG7B,QAASgD,GAAW5F,EAAGC,GACnB,MAAID,IAAKC,EACE0F,EAAO3F,KAAO2F,EAAO1F,GAEzBD,IAAMC,EAEjB,QAAS4F,GAAU5C,EAAM3F,EAAOwI,GAC5B,MAAOnD,GAAiBM,EAAM3F,GAASyI,GAAcD,GAEzD,QAASE,GAAUtP,EAAO4G,EAAOwI,EAAUG,GAA3C,GAGQC,GAFAjD,EAAOG,EAAO1M,GACdyP,EAAY/C,EAAO9F,EAWvB,OARI4I,GADAJ,IAAarB,GACLxB,EAAKyB,WAAayB,EAAUzB,WAA8D,IAAhDzB,EAAKuB,cAAgB2B,EAAU3B,eAAsBqB,EAAU5C,EAAM,GAAIE,MAAKF,EAAKuB,cAAevB,EAAKyB,YAAaE,IAAQ,GAAIzB,MAAKF,EAAKuB,cAAevB,EAAKyB,WAAa,EAAG,GAAGG,UAC5NiB,IAAavB,GACZtB,EAAKuB,cAAgB2B,EAAU3B,cAAgBwB,EAAU/C,EAAM,GAAIE,MAAKF,EAAKuB,cAAe,GAAIC,GAAQ,GAAK,GAC9GqB,IAAalB,IAAQkB,IAAanB,GACjCkB,EAAU5C,EAAMkD,EAAWL,GAE3BJ,EAASzC,EAAM3F,GAASyI,GAAcD,GAE3CI,EAAQD,EAEnB,QAASG,GAASpG,EAAGC,EAAGqE,GACpB,GAAIrF,EAUJ,OARIA,GADAqF,IAASC,GACFtE,EAAEuE,cAAgBxE,EAAEwE,cACpBF,IAASG,GACe,GAAxB2B,EAASpG,EAAGC,EAAGsE,IAActE,EAAEyE,WAAa1E,EAAE0E,WAC9CJ,IAASM,GACT9G,KAAKE,MAAM0H,EAASzF,EAAGD,GAAK0D,IAE5B5F,KAAKE,MAAM0H,EAASzF,EAAGD,GAAK+F,GAAczB,IAIzD,QAAS+B,GAAapD,EAAMqD,GAIxB,IAJJ,GAGQJ,GAGIK,EALJC,EAAM,EACNC,EAAOH,EAAYvR,OAAS,EAEzByR,GAAOC,GAGV,GAFAP,EAAQpI,KAAKE,OAAOwI,EAAMC,GAAQ,GAC9BF,EAAcD,EAAYJ,GAC1BK,EAActD,EACduD,EAAMN,EAAQ,MADlB,CAIA,KAAIK,EAActD,GAAlB,CAIA,KAAO2C,EAAWU,EAAYJ,EAAQ,GAAIjD,IACtCiD,GAEJ,OAAOA,GANHO,EAAOP,EAAQ,EAQvB,MAAII,GAAYJ,IAAUjD,EACfiD,EAEJA,EAAQ,EAEnB,QAASQ,GAAUC,EAAa1D,GAC5B,GAAI1H,EAMJ,OAJIA,GADAZ,EAASsI,GACA0D,EAAYD,UAAUzD,IAASG,EAAOH,GAEtCG,EAAOH,GAIxB,QAAS2D,GAAWD,EAAaE,GAAjC,GAEYtL,GACKnC,CAFb,IAAIL,EAAQ8N,GAAQ,CAEhB,IADItL,KACKnC,EAAM,EAAGA,EAAMyN,EAAM9R,OAAQqE,IAClCmC,EAAO/G,KAAKkS,EAAUC,EAAaE,EAAMzN,IAE7C,OAAOmC,GAEX,MAAOmL,GAAUC,EAAaE,GAGlC,QAAStN,GAAQ7C,EAAO6H,GAAxB,GAEYxJ,GACKqE,CAFb,IAAI1C,YAAiByM,MAAM,CAEvB,IADIpO,EAASwJ,EAAIxJ,OACRqE,EAAM,EAAGA,EAAMrE,EAAQqE,IAC5B,GAAIwM,EAAWrH,EAAInF,GAAM1C,GACrB,MAAO0C,EAGf,UAEJ,MAAOmF,GAAIhF,QAAQ7C,GAofvB,QAASoQ,GAAcC,GACnB,GAAIC,GAAQD,EAAWE,MAMvB,OALKD,KACDA,EAAQD,EAAWE,OAAS3I,EAAkByI,GAC9CC,EAAMxI,IAAM4E,EAAO4D,EAAMxI,KACzBwI,EAAMtI,IAAM0E,EAAO4D,EAAMtI,MAEtBsI,EAuJX,QAASE,GAAa5P,EAAS6P,EAAWC,GAWtC,IAXJ,GAUQC,GAAWC,EAAMC,EATjBC,EAAiBV,EAAcxP,EAAQyP,YACvCU,GAAQnQ,EAAQoH,KAAO8I,EAAe9I,MAAQpH,EAAQkH,KAAOgJ,EAAehJ,KAC5EkJ,EAAoBpQ,EAAQoQ,kBAC5BC,EAAgBrQ,EAAQqQ,cACxBC,EAAWtQ,EAAQwO,WAAa+B,GAChCC,EAAaX,EAAYY,GAAWxO,QAAQ4N,GAAa,EACzDrB,EAAW8B,EAAWG,GAAWD,KAAgBxQ,EAAQwO,SACzDkC,EAAQP,EAAO1B,GAAcD,GAC7BmC,EAAaD,GAETV,GAAQU,GAASL,GAAe,CACpCN,EAAYA,GAAaK,EAAkB5B,GAAUoC,MAAM,EAC3D,GACIX,GAAWF,EAAUc,cAChBZ,GAAYJ,IAAcrB,GAAYyB,EAAWH,EAC1D,IAAIG,EACAD,EAAOC,EACPS,EAAQC,EAAaX,MAClB,CAAA,GAAIxB,IAAasC,GAAKL,IAAa,CACtCT,EAAOxJ,KAAKC,KAAKkK,EAAaN,EAC9B,OACG,IAAIC,EAIJ,CACCI,EAAQL,IACRL,EAAOxJ,KAAKC,KAAKkK,EAAaN,GAElC,OAPA7B,EAAWiC,GAAWD,MAAiBM,GAAKL,IAC5CE,EAAaR,EAAO1B,GAAcD,GAClCuB,EAAY,MAQpB/P,EAAQ2O,aAAeqB,EACvBhQ,EAAQwO,SAAWA,EAEvB,QAASuC,GAAgB/Q,GAAzB,GAIQgR,GAAchE,EACTiE,EACDC,EAEIvJ,EAPR8H,EAAazP,EAAQyP,WACrB0B,EAAQ1O,GAAQgN,GAAcA,EAAWhS,OAAS,EAClD2T,EAAUjK,EAEd,KAAS8J,EAAa,EAAGA,EAAaE,EAAOF,IACrCC,EAAWzB,EAAWwB,GACtBC,GAAYF,IACRrJ,EAAO0D,EAAiB6F,EAAUF,GAClCrJ,EAAO,IACPyJ,EAAU5K,KAAKU,IAAIkK,EAASzJ,GAExBqF,EADAoE,GAAWC,GACJpE,GACAmE,GAAWE,GAAgC,EAAflF,GAC5Be,GACAiE,GAAWG,GACXlE,GACA+D,GAAWhF,GACXkB,GACA8D,GAAWvE,GACXW,GACA4D,GAAW3F,GACXgC,GAEAG,KAInBoD,EAAeE,CAEnBlR,GAAQwO,SAAWxB,GAAQM,GAE/B,QAASkE,IAASxR,GAAlB,GACQwO,IAAYxO,EAAQwO,UAAY,IAAIiD,cACpCC,EAAalD,IAAa+B,KAAQzK,EAAQ0I,EAAUiC,GAOxD,OANIiB,IACAX,EAAgB/Q,GAEhBwO,IAAa+B,IAAOvQ,EAAQ2O,eAAiBgD,IAC7C/B,EAAa5P,GAEVA,EAqaX,QAAS4R,IAAY1K,EAAKE,EAAKyK,GAA/B,GAIQC,GAEIC,EACApK,CANR,KAAKT,IAAQE,EACT,MAAO,EAGX,IAAIF,GAAO,GAAKE,GAAO,EAAG,CAGtB,GAFI2K,EAAW7K,IAAQE,EAAM,EAAIF,EAC7BS,GAAQP,EAAM2K,GAAY3K,EAC1ByK,KAAW,IAAUA,GAAUlK,EAAOqK,GACtC,MAAO,EAEXF,GAAUtL,KAAKY,IAAI,EAAG2K,GAAY3K,EAAM2K,GAAY,OAEpDD,GAAU5K,CAEd,OAAO4K,GAEX,QAASG,IAAY/K,EAAKE,EAAKyK,GAA/B,GAIQK,GAEIC,EACAxK,CANR,KAAKT,IAAQE,EACT,MAAO,EAGX,IAAIF,GAAO,GAAKE,GAAO,EAAG,CAGtB,GAFI+K,EAAWjL,IAAQE,EAAM,EAAIA,EAC7BO,EAAOnB,KAAKqB,KAAKsK,EAAWjL,GAAOiL,GACnCN,KAAW,IAAUA,GAAUlK,EAAOqK,GACtC,MAAO,EAEXE,GAAU1L,KAAKU,IAAI,EAAGiL,GAAYjL,EAAMiL,GAAY,OAEpDD,GAAU9K,CAEd,OAAO8K,GAEX,QAASxL,IAAMtH,EAAO4Q,GAClB,MAAOpN,IAAM4D,KAAKE,MAAMtH,EAAQ4Q,GAAQA,EAAMpI,IAElD,QAASnB,IAAKrH,EAAO4Q,GACjB,MAAOpN,IAAM4D,KAAKC,KAAKrH,EAAQ4Q,GAAQA,EAAMpI,IAEjD,QAASwK,IAAgBhT,GACrB,MAAOoH,MAAKY,IAAIZ,KAAKU,IAAI9H,EAAOiT,KAAoBA,IAgNxD,QAASC,IAAgBC,EAAWC,EAAWxS,GAA/C,GACQyS,GAAczS,EAAQyS,YACtBC,EAAUd,GAAYW,EAAWC,EAAWC,GAC5CE,EAAUV,GAAYM,EAAWC,EAAWC,GAC5CG,EAAYrL,EAAcmL,EAASC,GACnCE,GAAgBD,UAAWA,EAW/B,OAVI5S,GAAQ8S,oBAAqB,IACzBJ,EAAU,GAAKK,GAAeL,EAASE,EAAW,EAAI,KACtDF,GAAWE,GAEXD,EAAU,GAAKI,GAAeJ,EAASC,EAAW,EAAI,KACtDD,GAAWC,IAGnBC,EAAY3L,IAAMR,GAAMgM,EAASE,GACjCC,EAAYzL,IAAMX,GAAKkM,EAASC,GACzBC,EAEX,QAASG,IAAiBH,EAAa7S,GACnC,OACIkH,IAAKzE,GAAQzC,EAAQkH,KAAOV,KAAKU,IAAI2L,EAAY3L,IAAKlH,EAAQkH,KAAO2L,EAAY3L,IACjFE,IAAK3E,GAAQzC,EAAQoH,KAAOZ,KAAKY,IAAIyL,EAAYzL,IAAKpH,EAAQoH,KAAOyL,EAAYzL,IACjFwL,UAAWC,EAAYD,WAG/B,QAASK,IAAgBjT,EAASkT,GAAlC,GACapR,GACD8B,CADR,KAAS9B,EAAM,EAAGA,EAAMoR,EAAOzV,OAAQqE,IAC/B8B,EAAQsP,EAAOpR,GACI,OAAnB9B,EAAQ4D,KACR5D,EAAQ4D,GAAS0D,QAI7B,QAAS6L,IAAYN,EAAaO,GAAlC,GAEQC,GAAYC,EAQRC,EAmBJtP,EA5BAjE,EAAUoT,CAoCd,OAlCIA,KACAH,GAAgBG,GACZ,MACA,QAEJC,EAAa5Q,GAAQ2Q,EAAYlM,KACjCoM,EAAa7Q,GAAQ2Q,EAAYhM,KAC7BmM,EAAgBF,GAAcC,EAC9BC,GACIH,EAAYlM,MAAQkM,EAAYhM,MAC5BgM,EAAYlM,IAAM,EAClBkM,EAAYlM,IAAM,EAElBkM,EAAYhM,IAAM,GAI1BgM,EAAYR,WACZC,EAAY3L,IAAMR,GAAMmM,EAAY3L,IAAKkM,EAAYR,WACrDC,EAAYzL,IAAMX,GAAKoM,EAAYzL,IAAKgM,EAAYR,YAC7CW,IACPvT,EAAUmB,GAAW0R,EAAaO,GAClCP,EAAYD,UAAYrL,EAAcvH,EAAQkH,IAAKlH,EAAQoH,OAGnEyL,EAAYW,WAAaxT,EAAQ4S,WAAaC,EAAYD,WAAa,EACnE3O,EAAS9C,GAAW0R,EAAa7S,GACjCiE,EAAOiD,KAAOjD,EAAOmD,MACjBiM,IAAeC,EACfrP,EAAOmD,IAAMnD,EAAOiD,IAAMjD,EAAO2O,WACzBS,GAAcC,IACtBrP,EAAOiD,IAAMjD,EAAOmD,IAAMnD,EAAO2O,YAGlC3O,EAEX,QAAS8O,IAAe3T,EAAOqU,EAASC,GAAxC,GACQC,GAAY/Q,GAAM4D,KAAKqB,IAAIzI,EAAQqU,GAAU7L,IAC7CgM,EAAYH,GAAW,EAAIC,EAC/B,OAAqB,KAAdC,GAAmBA,EAAYC,EA2K1C,QAASC,IAAUvN,GACf,GAAI0G,GAAOQ,EAUX,OATIlH,IAAS+K,GACTrE,EAAOC,GACA3G,GAASgL,GAChBtE,EAAOG,GACA7G,GAASiL,GAChBvE,EAAOK,GACA/G,GAAS8F,KAChBY,EAAOM,IAEJN,EAEX,QAAS8G,IAAcvB,EAAWC,EAAWxS,GAA7C,GACQkH,GAAMlH,EAAQkH,KAAOqL,EACrBnL,EAAMpH,EAAQoH,KAAOoL,EACrBhE,EAAWxO,EAAQwO,WAAapH,GAAOF,EAAM2M,GAAUxI,EAAiBjE,EAAKF,IAAQsG,IACrFuG,EAAetF,GAAcD,GAC7BkE,EAAUzE,EAAUI,EAAOnH,GAAO,EAAGsH,IAAa1C,EAAO1E,GACzDuL,EAAUzE,EAASG,EAAOjH,GAAO,EAAGoH,GACpCwF,EAAgBhU,EAAQ4S,UAAY5S,EAAQ4S,UAAYtL,OACxDsL,EAAYoB,GAAiBvN,GAAKc,EAAcmL,EAAQpH,UAAWqH,EAAQrH,WAAYyI,GAAgBA,EACvGE,EAAcnF,EAAS4D,EAASC,EAASnE,GACzCmC,EAAalK,GAAKwN,EAAarB,GAC/BsB,EAAavD,EAAasD,EAC1BE,EAAO3N,KAAKE,MAAMwN,EAAa,GAC/BE,EAAOF,EAAaC,CASxB,OARKnU,GAAQwO,gBACFxO,GAAQwO,SAEnBxO,EAAQwO,SAAWxO,EAAQwO,UAAYA,EACvCxO,EAAQkH,IAAMlH,EAAQkH,KAAO4F,EAAY4F,GAAUyB,EAAM3F,GACzDxO,EAAQoH,IAAMpH,EAAQoH,KAAO0F,EAAY6F,EAASyB,EAAM5F,GACxDxO,EAAQwT,UAAYxT,EAAQwT,WAAaZ,EAAY,EACrD5S,EAAQ4S,UAAYA,EACb5S,EAmUX,QAASqU,IAAU3B,EAASC,EAASQ,EAAanT,GAAlD,GACQkH,GAAMiM,EAAYjM,IAClBE,EAAM+L,EAAY/L,GActB,OAbI3E,IAAQ0Q,EAAYmB,oBAAsBnB,EAAYmB,mBAAqB,GAC3EC,KAEC9R,GAAQzC,EAAQoH,KAEVpH,EAAQoH,KAAO,GACtBmN,KAFAnN,EAAMuL,EAILlQ,GAAQzC,EAAQkH,KAEVlH,EAAQkH,KAAO,GACtBqN,KAFArN,EAAMwL,GAKNxL,IAAKA,EACLE,IAAKA,GAGb,QAASoN,IAActN,EAAKE,EAAKpH,GAAjC,GACQyU,GAAOzU,EAAQ4S,UACfF,EAAUxL,CAMd,OALIA,IAAO,EACPwL,EAAUtL,GAAO,EAAIZ,KAAKsB,IAAI2M,MAAY,EAClCzU,EAAQyS,cAChBC,EAAUlM,KAAKsB,IAAI2M,EAAMjO,KAAKE,MAAMqB,GAAIb,EAAKuN,MAE1C/B,EAEX,QAASgC,IAActN,EAAKqN,GAA5B,GAEQ9B,GADAgC,EAAkB/R,GAAMmF,GAAIX,EAAKqN,GAAO7M,IAAqB,CASjE,OANI+K,GADAvL,GAAO,EACGqN,EACiB,IAApBE,IAA0BA,EAAkB,IAAOA,EAAkB,IAClEnO,KAAKsB,IAAI2M,EAAM1M,GAAIX,EAAKqN,GAAQ,IAEhCjO,KAAKsB,IAAI2M,EAAMjO,KAAKC,KAAKsB,GAAIX,EAAKqN,KAIpD,QAASF,MACL,KAAUK,OAAM,6DAEpB,QAAS7M,IAAI7E,EAAGD,GACZ,MAAOuD,MAAKuB,IAAI7E,GAAKsD,KAAKuB,IAAI9E,GA0iBlC,QAAS4R,IAAgBnM,EAAGC,GACxB,MAAO,KAAMnC,KAAKqB,IAAIrB,KAAKqB,IAAIa,EAAIC,GAAK,KAsQ5C,QAASmM,IAAW1V,GAChB,MAAOA,IAAS,KAAS,EA77LhC,GAEOoD,IACA9D,GACAgM,GACAqK,GACApW,GACAqW,GACAC,GACAC,GACAC,GACAC,GACAC,GACAlU,GACAkE,GACAiQ,GACAC,GACAC,GACAC,GACA1Q,GACA2Q,GACAC,GACAxP,GACAyP,GACAC,GACAC,GACAC,GACAnO,GACAoO,GACAC,GACAC,GACAC,GACAC,GACAC,GACArR,GACAmC,GACAE,GACAiP,GACAC,GACAC,GACAC,GACAC,GACAC,GACA7R,GACA8R,GACAtT,GACAuB,GACAgS,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GAmDAhV,GAuCA0B,GAwCAuT,GAWAjZ,GACAkZ,GAaAC,GACAC,GAwCAC,GAkCAC,GACAC,GASAC,GAKAxS,GAOAyS,GAuCAC,GAoBAC,GACAC,GAyDAC,GACAC,GACAC,GACAzV,GACA0V,GACAC,GACAC,GACAC,GACAC,GACAzH,GACA0H,GACA1b,GACAuL,GACAzF,GACA6V,GACAC,GACAC,GAwFArQ,GAuCAkB,GA4LAoP,GA6GAC,GAeAC,GACAC,GA6BAC,GAsOAC,GAkGAC,GAkGAC,GACAC,GACAC,GA4JAC,GAoGAC,GAmJAC,GACAC,GAqCAC,GACAC,GAwIAC,GA8BAC,GA+EAC,GACAC,GACAC,GAkSAC,GAkpBAnM,GACAF,GACAH,GACAD,GACAF,GACAD,GACAF,GACAF,GACAiN,GACArM,GACApC,GACAoB,GACAT,GACAmF,GACAD,GACAD,GACA5C,GA4MA0L,GAaAC,GA0dA/H,GACAgI,GAUArI,GACAL,GACAlB,GAUAF,GAUA+J,GAsCAC,GA8LAC,GA8cAC,GACAC,GAuSAC,GA0MAC,GACAC,GAsXAC,GA8DAC,GAqLAC,GAsKAC,GAoIAC,GAiBAC,GAoBAC,GACAC,GACAC,EA9tLJ9c,QAAOC,MAAM4W,QAAU7W,OAAOC,MAAM4W,YAChC7S,GAAU/D,MAAM+D,QAChB9D,GAAO8D,GAAQ9D,KACfgM,GAAOlI,GAAQkI,KACfqK,GAAQvS,GAAQuS,MAChBpW,GAAQF,MAAME,MACdqW,GAAWvW,MAAMuW,SACjBC,GAAOD,GAASC,KAChBC,GAASF,GAASE,OAClBC,GAAoBH,GAASuG,UAC7BnG,GAAUJ,GAASI,QACnBC,GAAU5W,MAAM4W,QAChBlU,GAAa1C,MAAM0C,WACnBkE,GAAa5G,MAAM4G,WACnBiQ,GAAqB7W,MAAM+c,OAC3BjG,GAAM,MACNC,GAAmB,iBACnBC,GAAQ,OACR1Q,GAAS,SACT2Q,GAAS,SACTC,GAAS,SACTxP,GAAkB,EAClByP,GAAQ,QACRC,GAAO,OACPC,GAAe,kBACfC,GAAiB,IACjBnO,GAAoB,GACpBoO,GAAgB,IAChBC,GAAM,MACNC,GAAe,UACfC,GAAS,SACTC,GAAmB,IACnBC,GAAS,SACTrR,GAAO,OACPmC,GAAYsU,OAAOtU,UACnBE,IAAaoU,OAAOtU,UACpBmP,GAAO,OACPC,GAAa,YACbC,GAAa,YACbC,GAAa,YACbC,GAAS,SACTC,GAAU,UACV7R,GAAQ,QACR8R,GAAQ,QACRtT,GAAS,SACTuB,GAAM,MACNgS,GAAW,WACXC,GAAQ,QACRC,GAAQ,OACRC,GAAQ,QACRC,GAAI,IACJC,GAAI,IACJC,IACA5B,IAAKA,GACLC,iBAAkBA,GAClBC,MAAOA,GACP1Q,OAAQA,GACR2Q,OAAQA,GACRC,OAAQA,GACRxP,gBAAiBA,GACjByP,MAAOA,GACPC,KAAMA,GACNC,aAAcA,GACdC,eAAgBA,GAChBnO,kBAAmBA,GACnBoO,cAAeA,GACfC,IAAKA,GACLC,aAAcA,GACdC,OAAQA,GACRC,iBAAkBA,GAClBC,OAAQA,GACRrR,KAAMA,GACNmC,UAAWA,GACXE,UAAWA,GACXiP,KAAMA,GACNC,WAAYA,GACZC,WAAYA,GACZC,WAAYA,GACZC,OAAQA,GACRC,QAASA,GACT7R,MAAOA,GACP8R,MAAOA,GACPtT,OAAQA,GACRuB,IAAKA,GACLgS,SAAUA,GACVC,MAAOA,GACPC,MAAOA,GACPC,MAAOA,GACPC,EAAGA,GACHC,EAAGA,IAcH/U,GAAc,OAuCd0B,GAAoB,sCAwCpBuT,IACAsE,OAAQ,SAAUA,EAAQtc,GACtB,MAAOA,IAEXuc,SAAU,SAAUvc,GAChB,MAAOA,IAEXgQ,UAAW,SAAUhQ,GACjB,MAAO,IAAIyM,MAAKzM,KAGpBjB,GAAUiZ,GACVC,GAAc1Y,GAAMC,WACxByY,GAAYuE,SAAW,SAAUC,GAC7B1d,GAAU0d,GAEVC,OAAOC,kBACPD,OAAOC,iBAAiB1E,IACpB2E,gBACIrc,IAAK,WACD,MAAOxB,QAKnBmZ,GAAuB,uBACvBC,GAAgB5Y,GAAMC,QACtBC,KAAM,SAAUwQ,GACZtQ,KAAKkd,aAAe5M,GAExB6M,KAAM,SAAUC,GAEZ,IAFE,GAIEC,GAHAC,KAAaC,EAAMC,UAAU9e,OAAS,EACnC6e,KAAQ,GACXD,EAAOC,GAAOC,UAAUD,EAAM,EAElC,OADIF,GAAOrd,KAAKqd,KACZ/Y,EAAS8Y,IAAiBA,EAAaK,MAAMtG,IACtCkG,EAAKV,OAAOe,MAAML,GAAOD,GAAcO,OAAOL,IAElDD,EAAKT,SAASU,EAAO,GAAIF,IAEpCQ,WAAY,SAAUR,EAAcE,EAAQO,GAAhC,GAEJ3Y,GADAmY,EAAOrd,KAAKqd,IAUhB,OAPInY,GADAZ,EAAS8Y,IAAiBA,EAAaK,MAAMtG,IACpCiG,EAAaxf,QAAQ2a,GAAsB,SAAUkF,EAAO5N,EAAOiO,GACxE,GAAIzd,GAAQid,EAAOS,SAASlO,EAAO,IACnC,OAAOwN,GAAKT,SAASvc,EAAOyd,EAAoBA,EAAkBE,UAAU,GAAK,GAAIH,KAGhFR,EAAKT,SAASU,EAAO,GAAIF,EAAcS,MAKxDd,OAAOC,kBACPD,OAAOC,iBAAiBxE,GAAcyF,IAClCZ,MACIzc,IAAK,WACD,MAAOZ,MAAKkd,cAAgB5E,GAAY2E,gBAE5CiB,IAAK,SAAU7d,GACXL,KAAKkd,aAAe7c,MAKhCoY,GAAe7Y,GAAMC,QACrBC,KAAM,SAAUqe,EAAOC,GACH,SAAZA,IACAA,MAEJpe,KAAKkd,aAAekB,EAAQ9N,YAC5BtQ,KAAKqe,OAASD,EAAQC,QAAUF,EAChCne,KAAK2c,OAAS,GAAInE,IAAc4F,EAAQ9N,aACxCtQ,KAAKme,MAAQA,EACbne,KAAKse,MAAcF,EAAQE,KAE/BC,OAAQ,SAAUC,EAAMC,GAChBze,KAAKme,OACLne,KAAKme,MAAMO,QAAQF,EAAMC,IAGjCE,WAAY,SAAUC,GAClB,GAAIC,KAAa7e,KAAKme,WAAald,aAAe4d,QAClD,OAAOA,IAAYA,EAASC,OAASF,KAGzC7B,OAAOC,kBACPD,OAAOC,iBAAiBvE,GAAawF,IACjCZ,MACIzc,IAAK,WACD,MAAOZ,MAAKkd,cAAgB5E,GAAY2E,gBAE5CiB,IAAK,SAAU7d,GACXL,KAAKkd,aAAe7c,EACpBL,KAAK2c,OAAOU,KAAOhd,MAM/BsY,GAAmB/Y,GAAMC,WAC7B8Y,GAAiBkE,SAAW,SAAUC,GAClCpE,GAAYoE,GAEhBnE,GAAiBoG,OAAS,SAAUlc,EAASmc,GACzC,GAAItG,GACA,MAAOA,IAAUqG,OAAOlc,EAASmc,IAGrCpG,IACAvS,QAAS,SAAUF,GACf,MAAOA,KAGXC,GAAkBxG,GAAMC,WAC5BuG,GAAgByW,SAAW,SAAUC,GACjClE,GAAYkE,GAEhB1W,GAAgBC,QAAU,SAAUF,GAChC,MAAOyS,IAAUvS,QAAQF,IAEzB0S,IACAJ,aAAcA,GACdE,iBAAkBA,GAClBH,cAAeA,GACfF,YAAaA,GACblS,gBAAiBA,IAkCjB0S,GAAU,WACV9Y,KAAKG,SAET2Y,GAAQ9Q,UAAUpH,IAAM,SAAa4d,GACjC,MAAOxe,MAAKG,KAAKH,KAAKif,KAAKT,KAE/B1F,GAAQ9Q,UAAUkW,IAAM,SAAaM,EAAMne,GACvCL,KAAKG,KAAKH,KAAKif,KAAKT,IAASne,GAEjCyY,GAAQ9Q,UAAUiX,KAAO,SAAcT,GACnC,MAAOA,aAAgB1R,MAAO0R,EAAKjS,UAAYiS,GAU/CzF,GAAU,UACVC,GAAmBpZ,GAAMC,QACzBC,KAAM,SAAUof,EAAUC,GACtBnf,KAAKkf,SAAWA,EAChBlf,KAAKof,WAAahd,MAAepC,KAAKof,WAAYD,IAEtDT,QAAS,SAAUF,EAAMC,GAAhB,GAIDY,GAHApG,EAAMjZ,KACNkf,EAAWjG,EAAIiG,SACfE,EAAanG,EAAImG,UAOrB,OALIA,GAAWZ,GACXa,EAAqBrf,KAAKsf,aAAaF,EAAWZ,GAAOC,GAClDS,EAASnG,MAChBsG,EAAqBrf,KAAKsf,aAAavG,GAASyF,EAAMC,IAEnDY,GAEXC,aAAc,SAAUC,GAEpB,IADA,GAAId,MAAWlB,EAAMC,UAAU9e,OAAS,EACjC6e,KAAQ,GACXkB,EAAKlB,GAAOC,UAAUD,EAAM,EAChC,OAAOvd,MAAKkf,SAASK,GAAQ7B,MAAM1d,KAAKkf,SAAUT,IAEtDe,iBAAkB,SAAU3Y,GAAV,GAKL9D,GAJL0c,EAASzf,IACb,IAAIA,KAAKkf,SAASM,iBACd,MAAOxf,MAAKkf,SAASM,iBAAiB3Y,EAE1C,KAAS9D,EAAM,EAAGA,EAAM8D,EAAMnI,OAAQqE,IAClC,GAAI0c,EAAOL,WAAWvY,EAAM9D,IACxB,OAAO,KA2BnBkW,GAAMvZ,MAAM+D,QAAQ9D,KACpBuZ,GAASD,GAAIC,OACbC,GAAaF,GAAIE,WACjBzV,GAAUuV,GAAIvV,QACd0V,GAAMH,GAAIG,IACVC,GAAgBJ,GAAII,cACpBC,GAAcL,GAAIK,YAClBC,GAAeN,GAAIM,aACnBC,GAAmBP,GAAIO,iBACvBzH,GAAOkH,GAAIlH,KACX0H,GAAaR,GAAIQ,WACjB1b,GAAYkb,GAAIlb,UAChBuL,GAAM2P,GAAI3P,IACVzF,GAAQoV,GAAIpV,MACZ6V,GAAeT,GAAIS,aACnBC,GAAiBV,GAAIU,eACrBC,GAAaha,GAAMC,WACvB+Z,GAAW8F,WAAa,SAAUze,EAAS0e,EAAOC,GAChC,SAAVA,IACAA,GAAUC,MAAO,GAErB,IAAIC,GAAY,GACX7e,GAAW2e,EAAMC,MAAQC,IAAchf,SAAS6e,OAGrD5C,OAAOgD,KAAK9e,GAAS+e,QAAQ,SAAU/hB,GACnC,GAAIoC,GAAQY,EAAQhD,EACR,gBAARA,GAAmC,MAAXA,EAAI,IAAeoC,IAGnC,SAARpC,EACA0hB,EAAMxhB,KAAKkC,GACa,gBAAVA,KACduf,EAAMC,QACNjG,GAAW8F,WAAWrf,EAAOsf,EAAOC,GACpCA,EAAMC,aAIlBjG,GAAWqG,UAAY,SAAUN,EAAOjZ,GACpC,GAAIwZ,KACJ,IAAIP,EAAMjhB,OAAS,GAAKoC,SAAS6e,MAAO,CACpC,IACIO,EAAWP,EAAMrf,IAAI,SAAU6f,GAC3B,MAAOrf,UAAS6e,MAAMS,KAAKD,KAEjC,MAAO7Y,GACL5H,MAAM2gB,aAAa/Y,GAEvBgZ,QAAQC,IAAIL,GAAUM,KAAK9Z,EAAUA,OAErCA,MAGRkT,GAAW6G,aAAe,SAAUxf,EAASyF,GACzC,GAAIiZ,KACJ/F,IAAW8F,WAAWze,EAAS0e,GAC/B/F,GAAWqG,UAAUN,EAAOjZ,IA+C5B6C,GAAQ3J,GAAMC,QACdC,KAAM,SAAUoE,EAAGC,GACfnE,KAAKkE,EAAIA,GAAK,EACdlE,KAAKmE,EAAIA,GAAK,GAElBuc,MAAO,WACH,MAAO,IAAInX,IAAMvJ,KAAKkE,EAAGlE,KAAKmE,IAElCwc,OAAQ,SAAUC,GACd,MAAOA,IAAS5gB,KAAKkE,IAAM0c,EAAM1c,GAAKlE,KAAKmE,IAAMyc,EAAMzc,GAE3D0c,OAAQ,SAAUC,EAAQC,GAAlB,GACA1X,GAAQC,GAAIyX,GACZC,EAAOvZ,KAAK+B,IAAIH,GAChB4X,EAAOxZ,KAAKgC,IAAIJ,GAChBH,EAAK4X,EAAO5c,EACZiF,EAAK2X,EAAO3c,EACZ8U,EAAMjZ,KACNkE,EAAI+U,EAAI/U,EACRC,EAAI8U,EAAI9U,CAGZ,OAFAnE,MAAKkE,EAAIL,GAAMqF,GAAMhF,EAAIgF,GAAM8X,GAAQ7c,EAAIgF,GAAM8X,EAAM7Z,IACvDpH,KAAKmE,EAAIN,GAAMsF,GAAMhF,EAAIgF,GAAM6X,GAAQ9c,EAAIgF,GAAM+X,EAAM7Z,IAChDpH,MAEXkhB,SAAU,SAAUvX,GAGhB,MAFA3J,MAAKkE,GAAKyF,EACV3J,KAAKmE,GAAKwF,EACH3J,MAEXmhB,WAAY,SAAUP,GAAV,GACJQ,GAAKphB,KAAKkE,EAAI0c,EAAM1c,EACpBmd,EAAKrhB,KAAKmE,EAAIyc,EAAMzc,CACxB,OAAOsD,MAAK6Z,KAAKF,EAAKA,EAAKC,EAAKA,MAGxC9X,GAAMgY,SAAW,SAAUT,EAAQ1X,EAAO2B,GACtC,GAAIyW,GAAUlY,GAAIF,EAClB,OAAO,IAAIG,IAAMuX,EAAO5c,EAAI6G,EAAStD,KAAK+B,IAAIgY,GAAUV,EAAO3c,EAAI4G,EAAStD,KAAKgC,IAAI+X,KAErF/W,GAAM7K,GAAMC,QACZC,KAAM,SAAUuK,EAAIC,EAAIC,EAAIC,GACxBxK,KAAKqK,GAAKA,GAAM,EAChBrK,KAAKsK,GAAKA,GAAM,EAChBtK,KAAKuK,GAAKA,GAAM,EAChBvK,KAAKwK,GAAKA,GAAM,GAEpBmW,OAAQ,SAAUnf,GACd,MAAOxB,MAAKqK,KAAO7I,EAAI6I,IAAMrK,KAAKuK,KAAO/I,EAAI+I,IAAMvK,KAAKsK,KAAO9I,EAAI8I,IAAMtK,KAAKwK,KAAOhJ,EAAIgJ,IAE7F3L,MAAO,WACH,MAAOmB,MAAKuK,GAAKvK,KAAKqK,IAE1BvL,OAAQ,WACJ,MAAOkB,MAAKwK,GAAKxK,KAAKsK,IAE1BxG,UAAW,SAAUsd,EAAIC,GAKrB,MAJArhB,MAAKqK,IAAM+W,EACXphB,KAAKuK,IAAM6W,EACXphB,KAAKsK,IAAM+W,EACXrhB,KAAKwK,IAAM6W,EACJrhB,MAEXyhB,KAAM,SAAUvd,EAAGC,GAAb,GACErF,GAASkB,KAAKlB,SACdD,EAAQmB,KAAKnB,OASjB,OARI6E,IAAQQ,KACRlE,KAAKqK,GAAKnG,EACVlE,KAAKuK,GAAKvK,KAAKqK,GAAKxL,GAEpB6E,GAAQS,KACRnE,KAAKsK,GAAKnG,EACVnE,KAAKwK,GAAKxK,KAAKsK,GAAKxL,GAEjBkB,MAEX0hB,KAAM,SAAUC,GAKZ,MAJA3hB,MAAKqK,GAAK5C,KAAKU,IAAInI,KAAKqK,GAAIsX,EAAUtX,IACtCrK,KAAKsK,GAAK7C,KAAKU,IAAInI,KAAKsK,GAAIqX,EAAUrX,IACtCtK,KAAKuK,GAAK9C,KAAKY,IAAIrI,KAAKuK,GAAIoX,EAAUpX,IACtCvK,KAAKwK,GAAK/C,KAAKY,IAAIrI,KAAKwK,GAAImX,EAAUnX,IAC/BxK,MAEX4hB,UAAW,SAAUhB,GAAV,GACHiB,GAAanf,EAAQke,GACrB1c,EAAI2d,EAAajB,EAAM,GAAKA,EAAM1c,EAClCC,EAAI0d,EAAajB,EAAM,GAAKA,EAAMzc,CAEtC,OADAnE,MAAK0hB,KAAK,GAAIjX,IAAIvG,EAAGC,EAAGD,EAAGC,IACpBnE,MAEX8hB,OAAQ,SAAUH,EAAW/C,GASzB,MARIA,KAAS1G,IAAM0G,IACf5e,KAAKqK,GAAKsX,EAAUtX,GACpBrK,KAAKuK,GAAKoX,EAAUpX,IAEpBqU,IAASzG,IAAMyG,IACf5e,KAAKsK,GAAKqX,EAAUrX,GACpBtK,KAAKwK,GAAKmX,EAAUnX,IAEjBxK,MAEX+hB,QAAS,SAAUJ,EAAW/d,GAArB,GAMGoe,GACAlB,EANJhiB,EAASkB,KAAKlB,SACdD,EAAQmB,KAAKnB,QACb+f,EAAOhb,IAAWkC,IAAOlC,IAAWoC,GAASmS,GAAID,GACjD3U,EAASqb,IAASzG,GAAIrZ,EAASD,CAanC,OAZI+E,KAAW+S,IACPqL,EAAeL,EAAUb,SACzBA,EAAS9gB,KAAK8gB,SAClB9gB,KAAKqK,IAAM2X,EAAa9d,EAAI4c,EAAO5c,EACnClE,KAAKsK,IAAM0X,EAAa7d,EAAI2c,EAAO3c,GAEnCnE,KAAK4e,EAAO,GADLhb,IAAWkC,IAAOlC,IAAWqC,GACnB0b,EAAU/C,EAAO,GAAKrb,EAEtBoe,EAAU/C,EAAO,GAEtC5e,KAAKuK,GAAKvK,KAAKqK,GAAKxL,EACpBmB,KAAKwK,GAAKxK,KAAKsK,GAAKxL,EACbkB,MAEXiiB,OAAQ,SAAUC,EAAIC,GAGlB,MAFAniB,MAAKuK,IAAM2X,EACXliB,KAAKwK,IAAM2X,EACJniB,MAEXoiB,OAAQ,SAAUF,EAAIC,GAElB,MADAniB,MAAKiiB,QAAQC,GAAKC,GACXniB,MAEXqiB,IAAK,SAAUC,GACX,GAAI7c,GAAUF,EAAW+c,EAKzB,OAJAtiB,MAAKqK,IAAM5E,EAAQI,KACnB7F,KAAKuK,IAAM9E,EAAQE,MACnB3F,KAAKsK,IAAM7E,EAAQC,IACnB1F,KAAKwK,IAAM/E,EAAQG,OACZ5F,MAEXuiB,MAAO,SAAUD,GACb,GAAI7c,GAAUF,EAAW+c,EAKzB,OAJA7c,GAAQI,MAAQJ,EAAQI,KACxBJ,EAAQC,KAAOD,EAAQC,IACvBD,EAAQE,OAASF,EAAQE,MACzBF,EAAQG,QAAUH,EAAQG,OACnB5F,KAAKqiB,IAAI5c,IAEpBib,MAAO,WACH,MAAO,IAAIjW,IAAIzK,KAAKqK,GAAIrK,KAAKsK,GAAItK,KAAKuK,GAAIvK,KAAKwK,KAEnDsW,OAAQ,WACJ,MAAO,IAAIvX,IAAMvJ,KAAKqK,GAAKrK,KAAKnB,QAAU,EAAGmB,KAAKsK,GAAKtK,KAAKlB,SAAW,IAE3E0jB,cAAe,SAAU5B,GACrB,MAAOA,GAAM1c,GAAKlE,KAAKqK,IAAMuW,EAAM1c,GAAKlE,KAAKuK,IAAMqW,EAAMzc,GAAKnE,KAAKsK,IAAMsW,EAAMzc,GAAKnE,KAAKwK,IAE7FiY,OAAQ,WACJ,OACI,GAAIlZ,IAAMvJ,KAAKqK,GAAIrK,KAAKsK,IACxB,GAAIf,IAAMvJ,KAAKuK,GAAIvK,KAAKsK,IACxB,GAAIf,IAAMvJ,KAAKuK,GAAIvK,KAAKwK,IACxB,GAAIjB,IAAMvJ,KAAKqK,GAAIrK,KAAKwK,MAGhCkY,QAAS,WACL,OACI1iB,KAAKqK,GACLrK,KAAKsK,GACLtK,KAAKuK,GACLvK,KAAKwK,IACPnM,KAAK,MAEXskB,SAAU,SAAUnhB,GAChB,QAASA,EAAIgJ,GAAKxK,KAAKsK,IAAMtK,KAAKwK,GAAKhJ,EAAI8I,IAAM9I,EAAI+I,GAAKvK,KAAKqK,IAAMrK,KAAKuK,GAAK/I,EAAI6I,KAEvFwW,OAAQ,SAAU+B,GAAV,GACA/jB,GAAQmB,KAAKnB,QACbC,EAASkB,KAAKlB,SACdma,EAAMjZ,KAAK8gB,SACX5X,EAAK+P,EAAI/U,EACTiF,EAAK8P,EAAI9U,EACT0e,EAAK5Z,EAAY,EAAG,EAAGC,EAAIC,EAAIyZ,GAC/BE,EAAK7Z,EAAYpK,EAAO,EAAGqK,EAAIC,EAAIyZ,GACnCG,EAAK9Z,EAAYpK,EAAOC,EAAQoK,EAAIC,EAAIyZ,GACxCI,EAAK/Z,EAAY,EAAGnK,EAAQoK,EAAIC,EAAIyZ,EAKxC,OAJA/jB,GAAQ4I,KAAKY,IAAIwa,EAAG3e,EAAG4e,EAAG5e,EAAG6e,EAAG7e,EAAG8e,EAAG9e,GAAKuD,KAAKU,IAAI0a,EAAG3e,EAAG4e,EAAG5e,EAAG6e,EAAG7e,EAAG8e,EAAG9e,GACzEpF,EAAS2I,KAAKY,IAAIwa,EAAG1e,EAAG2e,EAAG3e,EAAG4e,EAAG5e,EAAG6e,EAAG7e,GAAKsD,KAAKU,IAAI0a,EAAG1e,EAAG2e,EAAG3e,EAAG4e,EAAG5e,EAAG6e,EAAG7e,GAC1EnE,KAAKuK,GAAKvK,KAAKqK,GAAKxL,EACpBmB,KAAKwK,GAAKxK,KAAKsK,GAAKxL,EACbkB,MAEXijB,OAAQ,WACJ,MAAO,IAAI/M,KACPlW,KAAKqK,GACLrK,KAAKsK,KAELtK,KAAKnB,QACLmB,KAAKlB,YAGbokB,QAAS,WACL,MAAwB,KAAjBljB,KAAKnB,SAAmC,IAAlBmB,KAAKlB,UAEtCqkB,MAAO,SAAUxB,EAAW/C,EAAMwE,GAA3B,GACCC,GAAKzE,EAAO,EACZ0E,EAAK1E,EAAO,EACZ2E,EAAW3E,IAAS1G,GAAID,GAAQb,GAChCrX,EAAOC,KAAKujB,IACZxc,GAAQqc,GACJnd,GACAH,MAEJ9F,KAAKqjB,GAAM1B,EAAU0B,GACrBrjB,KAAKsjB,GAAMtjB,KAAKqjB,GAAMtjB,GACfgH,EAAQqc,GACXrd,GACAC,MAEJhG,KAAKsjB,GAAM3B,EAAU2B,GACrBtjB,KAAKqjB,GAAMrjB,KAAKsjB,GAAMvjB,GACfqjB,IAAczM,KACrB3W,KAAKqjB,GAAM1B,EAAU0B,IAAO1B,EAAU4B,KAAcxjB,GAAQ,EAC5DC,KAAKsjB,GAAMtjB,KAAKqjB,GAAMtjB,MAQ9B8Z,GAAOja,GAAMC,QACbC,KAAM,SAAUghB,EAAQhW,EAAaC,EAAQyY,EAAYpa,GACrDpJ,KAAK8gB,OAASA,EACd9gB,KAAK8K,YAAcA,EACnB9K,KAAK+K,OAASA,EACd/K,KAAKwjB,WAAaA,EAClBxjB,KAAKoJ,MAAQA,GAEjBsX,MAAO,WACH,MAAO,IAAI7G,IAAK7Z,KAAK8gB,OAAQ9gB,KAAK8K,YAAa9K,KAAK+K,OAAQ/K,KAAKwjB,WAAYxjB,KAAKoJ,QAEtFqa,OAAQ,WACJ,MAAOzjB,MAAKwjB,WAAaxjB,KAAKoJ,MAAQ,GAE1Csa,UAAW,SAAUC,EAAW7Y,GAM5B,MALIA,GACA9K,KAAK8K,YAAc6Y,EAEnB3jB,KAAK+K,OAAS4Y,EAEX3jB,MAEX4gB,MAAO,SAAUxX,EAAO0B,GAAjB,GACC8Y,GAActa,GAAIF,GAClBya,EAAKpc,KAAK+B,IAAIoa,GACdE,EAAKrc,KAAKgC,IAAIma,GACd7Y,EAASD,EAAc9K,KAAK8K,YAAc9K,KAAK+K,OAC/C7G,EAAIL,GAAM7D,KAAK8gB,OAAO5c,EAAI2f,EAAK9Y,EAAQ3D,IACvCjD,EAAIN,GAAM7D,KAAK8gB,OAAO3c,EAAI2f,EAAK/Y,EAAQ3D,GAC3C,OAAO,IAAImC,IAAMrF,EAAGC,IAExB4f,YAAa,SAAUC,EAAUnlB,EAAOC,GAA3B,GACLmlB,GAASjkB,KAAK0gB,QAAQ0B,OAAO4B,GAC7BE,EAAWD,EAAOR,SAClBU,EAAWF,EAAOrD,MAAMsD,GACxBE,EAAKvlB,EAAQ,EACbwlB,EAAKvlB,EAAS,EACdwlB,EAAK7c,KAAKgC,IAAIH,GAAI4a,IAClBK,EAAK9c,KAAK+B,IAAIF,GAAI4a,IAClBhgB,EAAIigB,EAASjgB,EAAIkgB,EACjBjgB,EAAIggB,EAAShgB,EAAIkgB,CAOrB,OANI5c,MAAKqB,IAAIwb,GAAM,KACfpgB,GAAKkgB,GAAMG,EAAK9c,KAAKqB,IAAIyb,IAEzB9c,KAAKqB,IAAIyb,GAAM,KACfpgB,GAAKkgB,GAAMC,EAAK7c,KAAKqB,IAAIwb,IAEtB,GAAI7Z,IAAIvG,EAAGC,EAAGD,EAAIrF,EAAOsF,EAAIrF,IAExC0jB,cAAe,SAAUgC,GAAV,GACP1D,GAAS9gB,KAAK8gB,OACdhW,EAAc9K,KAAK8K,YACnBC,EAAS/K,KAAK+K,OACdyY,EAAaxjB,KAAKwjB,WAClBiB,EAAWzkB,KAAKwjB,WAAaxjB,KAAKoJ,MAClCgY,EAAKoD,EAAEtgB,EAAI4c,EAAO5c,EAClBmd,EAAKmD,EAAErgB,EAAI2c,EAAO3c,EAClBugB,EAAS,GAAInb,IAAM6X,EAAIC,GACvBsD,EAAa3kB,KAAK4gB,MAAM4C,GACxBoB,EAAc,GAAIrb,IAAMob,EAAWzgB,EAAI4c,EAAO5c,EAAGygB,EAAWxgB,EAAI2c,EAAO3c,GACvE0gB,EAAW7kB,KAAK4gB,MAAM6D,GACtBK,EAAY,GAAIvb,IAAMsb,EAAS3gB,EAAI4c,EAAO5c,EAAG2gB,EAAS1gB,EAAI2c,EAAO3c,GACjE4gB,EAAOlhB,GAAMud,EAAKA,EAAKC,EAAKA,EAAIja,GACpC,QAAQwd,EAAYjE,OAAO+D,IAAW3gB,EAAU6gB,EAAaF,MAAa3gB,EAAU+gB,EAAWJ,IAAWK,GAAQja,EAAcA,GAAeia,GAAQha,EAASA,GAEpKia,QAAS,WAAA,GAgBDC,GAUKzmB,EACDoiB,EA1BJnB,EAASzf,KACTwB,EAAM,GAAIiJ,IAAIrC,GAAWA,GAAWE,GAAWA,IAC/Ckb,EAAa3f,GAAM7D,KAAKwjB,WAAa,KACrCiB,EAAW5gB,IAAO2f,EAAaxjB,KAAKoJ,OAAS,KAC7C0B,EAAc9K,KAAK8K,YACnBoa,GACA,EACA,GACA,IACA,IACA1B,EACAiB,GACFrmB,KAAKsL,GACHyb,EAAkBD,EAAUhiB,QAAQsgB,GACpC4B,EAAgBF,EAAUhiB,QAAQuhB,EAWtC,KARIQ,EADAzB,IAAeiB,EACNS,EAELC,EAAkBC,EACTF,EAAUrT,MAAMsT,EAAiBC,EAAgB,MAE9CzH,OAAOuH,EAAUrT,MAAM,EAAGuT,EAAgB,GAAIF,EAAUrT,MAAMsT,EAAiBD,EAAUxmB,SAGpGF,EAAI,EAAGA,EAAIymB,EAAOvmB,OAAQF,IAC3BoiB,EAAQnB,EAAOmB,MAAMqE,EAAOzmB,IAChCgD,EAAIogB,UAAUhB,GACdpf,EAAIogB,UAAUhB,EAAO9V,EAKzB,OAHKA,IACDtJ,EAAIogB,UAAU5hB,KAAK8gB,QAEhBtf,GAEX4gB,OAAQ,SAAU/hB,GAEd,MADAL,MAAK+K,QAAU1K,EACRL,QAMX8Z,GAASD,GAAKha,QACdC,KAAM,SAAUghB,EAAQ/V,EAAQyY,EAAYpa,GACxCyQ,GAAKoE,GAAGne,KAAKulB,KAAKrlB,KAAM8gB,EAAQ,EAAG/V,EAAQyY,EAAYpa,IAE3DgZ,OAAQ,SAAU/hB,GACd,MAAOwZ,IAAKoE,GAAGmE,OAAOiD,KAAKrlB,KAAMK,IAErCqgB,MAAO,WACH,MAAO,IAAI5G,IAAO9Z,KAAK8gB,OAAQ9gB,KAAK+K,OAAQ/K,KAAKwjB,WAAYxjB,KAAKoJ,QAEtEsa,UAAW,SAAUC,GAEjB,MADA3jB,MAAK+K,OAAS4Y,EACP3jB,QAGX+Z,GAAkB,KAClBC,GAAepa,GAAMC,QACrBylB,WAAY,SAAUrB,EAAQhjB,GAAlB,GAMJ6f,GACA/V,EACAD,EACAya,EAMAjiB,EAGIkiB,EAjBJhC,EAAaS,EAAOT,WAAa,IACjCiB,EAAWR,EAAO7a,MAAQoa,CAsB9B,OArBIS,GAAO7a,MAAQ,GAAKoa,IAAeiB,IACnCA,GAAY1K,IAEZ+G,EAAS,GAAI7K,IAAS1M,MAAM0a,EAAOnD,OAAO5c,EAAG+f,EAAOnD,OAAO3c,GAC3D4G,EAAStD,KAAKY,IAAI4b,EAAOlZ,OAAQ,GACjCD,EAAcrD,KAAKY,IAAI4b,EAAOnZ,YAAa,GAC3Cya,EAAM,GAAItP,IAASwP,IAAI3E,GACvB0C,WAAYA,EACZiB,SAAUA,EACViB,QAAS3a,EACT4a,QAAS5a,IAETzH,EAAOqI,GAAKia,QAAQL,EAAKtkB,GAAS4kB,QAClC/a,GACAya,EAAIG,QAAUH,EAAII,QAAU7a,EACxB0a,EAAWD,EAAIO,QAAQrB,GAC3BnhB,EAAKyI,OAAOyZ,EAASthB,EAAGshB,EAASrhB,GACjCb,EAAKiiB,IAAId,EAAUjB,EAAY1Y,EAAaA,GAAa,IAEzDxH,EAAKyI,OAAO+U,EAAO5c,EAAG4c,EAAO3c,GAE1Bb,KAGf0W,GAAa5a,QAAU,GAAI4a,IACvBC,GAAera,GAAMC,QACrBC,KAAM,SAAUmB,GACZjB,KAAK+lB,YACL/lB,KAAKiB,QAAUmB,MAAepC,KAAKiB,QAASjB,KAAKgmB,gBAAgB/kB,KAErE+kB,gBAAiB,SAAU/kB,GACvB,MAAOA,IAEXglB,OAAQ,SAAUtE,GAAV,GAEAngB,GACKhD,EACD0nB,EAHJH,EAAW/lB,KAAK+lB,QAEpB,KAASvnB,EAAI,EAAGA,EAAIunB,EAASrnB,OAAQF,IAC7B0nB,EAAeH,EAASvnB,GAC5B0nB,EAAaD,OAAOtE,GACpBngB,EAAMA,EAAMA,EAAIkgB,KAAKwE,EAAa1kB,KAAO0kB,EAAa1kB,IAAIkf,OAE9D1gB,MAAKwB,IAAMA,GAAOmgB,GAEtBwE,QAAS,WAAA,GAKI3nB,GAJLunB,EAAW/lB,KAAK+lB,QAIpB,KAHI/lB,KAAKomB,WACLpmB,KAAKomB,UAAUD,UAEV3nB,EAAI,EAAGA,EAAIunB,EAASrnB,OAAQF,IACjCunB,EAASvnB,GAAG2nB,WAGpBE,QAAS,WACL,GAAIC,GAAStmB,KAAKsmB,MAClB,OAAOA,GAASA,EAAOD,UAAY,MAEvCE,UAAW,WACP,GAAIC,GAAUxmB,KAAKymB,YACnB,IAAID,EACA,MAAOA,GAAQnI,QAGvBoI,WAAY,WAER,IADA,GAAI5jB,GAAU7C,KACP6C,GAAS,CACZ,GAAIA,EAAQ6jB,aACR,MAAO7jB,GAAQ6jB,YAEnB7jB,GAAUA,EAAQyjB,SAG1BK,kBAAmB,SAAUvF,EAAIC,GAAd,GAGN7iB,GAFLunB,EAAW/lB,KAAK+lB,SAChBa,EAAgBb,EAASrnB,MAC7B,KAASF,EAAI,EAAGA,EAAIooB,EAAepoB,IAC/BunB,EAASvnB,GAAGgD,IAAIsC,UAAUsd,EAAIC,IAGtCnI,OAAQ,WAAA,GAGK1a,GACDqoB,EAHJC,EAActJ,UACdiC,EAASzf,IACb,KAASxB,EAAI,EAAGA,EAAIgf,UAAU9e,OAAQF,IAC9BqoB,EAAOC,EAAYtoB,GACvBihB,EAAOsG,SAAS5nB,KAAK0oB,GACrBA,EAAKP,OAAS7G,GAGtBsH,aAAc,WACN/mB,KAAKiB,QAAQ+lB,WAAY,IAG7BhnB,KAAKinB,eACLjnB,KAAKknB,YACLlnB,KAAKmnB,iBACLnnB,KAAKonB,kBACLpnB,KAAKqnB,mBAETH,UAAW,WACHlnB,KAAKsnB,SACLtnB,KAAKsnB,OAAOC,aAAevnB,KACvBA,KAAKsmB,QACLtmB,KAAKsmB,OAAOkB,aAAaxnB,KAAKsnB,UAI1CH,eAAgB,WAAA,GAGH3oB,GAFLunB,EAAW/lB,KAAK+lB,SAChBrnB,EAASqnB,EAASrnB,MACtB,KAASF,EAAI,EAAGA,EAAIE,EAAQF,IACxBunB,EAASvnB,GAAGuoB,gBAGpBE,aAAc,WACVjnB,KAAKsnB,OAAS,GAAItR,KACdyR,OAAQznB,KAAKiB,QAAQwmB,OACrBT,QAASrN,GAAe3Z,KAAKiB,QAAQ+lB,SAAS,MAGtDI,gBAAiB,WACTpnB,KAAKsnB,QAAUtnB,KAAKiB,QAAQmlB,YAC5BpmB,KAAKomB,UAAY3iB,GAAQikB,UAAU3I,OAAO/e,KAAKsnB,OAAQtnB,KAAKiB,QAAQmlB,aAG5EoB,aAAc,SAAUG,GACfA,EAAYJ,eACbI,EAAYJ,aAAevnB,MAE3B2nB,EAAY1mB,QAAQ2mB,OACpB5nB,KAAK6nB,WAAWP,OAAOpO,OAAOyO,GACvBjkB,GAAQikB,EAAY1mB,QAAQwmB,QACnCznB,KAAK8nB,YAAYC,YAAYJ,GACtB3nB,KAAKgoB,YACZhoB,KAAK+nB,YAAYJ,GACV3nB,KAAKsnB,OACZtnB,KAAKsnB,OAAOpO,OAAOyO,GAEnB3nB,KAAKsmB,OAAOkB,aAAaG,IAGjCE,SAAU,WACN,MAAI7nB,MAAKsmB,OACEtmB,KAAKsmB,OAAOuB,WAEhB7nB,MAEX8nB,UAAW,WACP,MAAI9nB,MAAKsmB,OACEtmB,KAAKsmB,OAAOwB,YAEhB9nB,MAEX+nB,YAAa,SAAUJ,GAAV,GAILM,GAEIC,EACAC,EANJV,EAASE,EAAY1mB,QAAQwmB,QAAU,EACvCW,EAAUpoB,KAAKsnB,OAAOvB,SACtBrnB,EAAS0pB,EAAQ1pB,MAErB,KAAKupB,EAAM,EAAGA,EAAMvpB,IACZwpB,EAAUE,EAAQH,GAClBE,EAAOxO,GAAeuO,EAAQjnB,QAAQwmB,OAAQ,KAC9CU,EAAOV,IAHaQ,KAO5BjoB,KAAKsnB,OAAOe,OAAOJ,EAAKN,IAE5BW,SAAU,SAAU5hB,GAAV,GAGGlI,GACD+pB,EAHJxC,EAAW/lB,KAAK+lB,SAChBrnB,EAASqnB,EAASrnB,MACtB,KAASF,EAAI,EAAGA,EAAIE,EAAQF,IACpB+pB,EAAQxC,EAASvnB,GACrBkI,EAAS6hB,GACLA,EAAMD,UACNC,EAAMD,SAAS5hB,IAI3B8hB,QAAS,SAAU/K,GAGf,IAHK,GACD5a,GAAU7C,KACVyoB,GAAU,EACP5lB,IAAY4lB,GACfA,EAAUhL,EAAM5a,GACX4lB,IACD5lB,EAAUA,EAAQyjB,OAG1B,IAAImC,EACA,MAAO5lB,IAGfwkB,eAAgB,aAEhBqB,aAAc,WACV,GAAIznB,IAAWjB,KAAKiB,aAAe0nB,SACnC,UAAU3oB,KAAK4oB,iBAAmB3nB,GAAWA,EAAQ+lB,WAAY,IAErE6B,gBAAiB,SAAUC,GAAV,GAMLC,GALJtJ,EAASzf,KACTiB,GAAWjB,KAAKiB,aAAe0nB,cAC/BK,EAAe/nB,EAAQqmB,OACvBqB,EAAY3oB,KAAKipB,UACrB,KAAKN,EAAW,CAYZ,GAXII,GACAG,MACItd,MAAOoM,GACPmR,QAAS,IAEb3lB,QACIoI,MAAOoM,GACPnZ,MAAO,EACPsqB,QAAS,KAGbH,GAcA,GAbAL,EAAY3oB,KAAKipB,WAAaD,EAAaxrB,EAAEqC,OAAOG,KAAKopB,uBACrDnC,aAAc,WACV,MAAOxH,GAAOmJ,gBAAgBG,IAElC1K,OAAQre,KAAKumB,YACb8C,OAAQrpB,KAAKqpB,OACbC,SAAUtpB,KAAKspB,SACfnX,SAAUnS,KAAKmS,SACf9R,MAAOL,KAAKK,MACZkpB,WAAYvpB,KAAKupB,WACjBC,aAAcxpB,KAAKwpB,aACnBC,MAAOzpB,KAAKypB,UAEXd,EACD,WAGJA,GAAY3oB,KAAKipB,WAAajpB,KAAK4oB,gBAAgBG,EAElDrlB,IAAQilB,EAAU1nB,QAAQwmB,UAC3BkB,EAAU1nB,QAAQwmB,OAAS9N,GAAe1Y,EAAQwmB,OAAQznB,KAAKiB,QAAQwmB,SAE3EznB,KAAKwnB,aAAamB,GAEtBA,EAAU3B,QAAQ8B,IAEtBY,sBAAuB,SAAU7mB,EAAS5B,EAAS0oB,GAC/C,GAAIC,GAAU,GAAIje,IAAKnO,EAAEqC,QACrB2D,QAAUoI,MAAO,QACjBsd,KAAMlpB,KAAK6pB,eAAeF,GAC1BG,OAAQjnB,EAAQ5B,QAAQ6oB,QACzB7oB,GAEH,OADA2oB,GAAQjmB,SAASomB,SAASlnB,EAAQc,SAASomB,YACpCH,GAEXC,eAAgB,SAAU5oB,GACtB,GAAIjB,KAAKsmB,OACL,MAAOtmB,MAAKsmB,OAAOuD,eAAe5oB,MAI9CgZ,GAAajS,UAAU/G,WACnBiZ,GAAaD,GAAapa,QAC1BC,KAAM,SAAUmB,GACZgZ,GAAagE,GAAGne,KAAKulB,KAAKrlB,KAAMiB,GAChCjB,KAAKiB,QAAQ+oB,OAASzkB,EAAWvF,KAAKiB,QAAQ+oB,QAC9ChqB,KAAKiB,QAAQqhB,QAAU/c,EAAWvF,KAAKiB,QAAQqhB,UAEnD2D,OAAQ,SAAUtE,GAAV,GAUAngB,GA6BAukB,EACKvnB,EACDqoB,EAxCJpH,EAASzf,KACTiB,EAAUjB,KAAKiB,QACfpC,EAAQoC,EAAQpC,MAChBC,EAASmC,EAAQnC,OACjBmrB,EAAchpB,EAAQgpB,YACtBC,EAAarrB,GAASC,EACtBkrB,EAAS/oB,EAAQ+oB,OACjB1H,EAAUrhB,EAAQqhB,QAClB6H,EAAclpB,EAAQmpB,OAAOvrB,MAE7BwrB,EAAmB,WACnB5K,EAAO0D,MAAMxB,EAAWzJ,GAAGjX,EAAQkiB,OACnC1D,EAAO0D,MAAMxB,EAAWxJ,GAAGlX,EAAQqpB,QACnC7K,EAAO8K,WAAa/oB,EAAIkf,QAAQ6B,MAAMyH,GAAQzH,MAAM4H,IAEpDK,EAAa7I,EAAUjB,OAwB3B,KAvBIwJ,IACAM,EAAWjgB,GAAKigB,EAAWngB,GAAKxL,EAChC2rB,EAAWhgB,GAAKggB,EAAWlgB,GAAKxL,GAEhCmrB,GACAO,EAAWjI,MAAMyH,GAAQzH,MAAM4H,GAAa5H,MAAMD,GAEtDrI,GAAagE,GAAGgI,OAAOZ,KAAKrlB,KAAMwqB,GAE9BhpB,EADA0oB,EACMlqB,KAAKwB,IAAM,GAAIiJ,IAAI,EAAG,EAAG5L,EAAOC,GAEhCkB,KAAKwB,IAEXyoB,GAAeC,GACfG,IACAG,EAAaxqB,KAAKwqB,WAAaxqB,KAAKuqB,WAAW7J,QAAQ6B,MAAMD,KAE7DkI,EAAaxqB,KAAKwqB,WAAahpB,EAAIkf,QACnClf,EAAI6gB,IAAIC,GAASD,IAAI8H,GAAa9H,IAAI2H,GACtCK,KAEJrqB,KAAK2mB,kBAAkBnlB,EAAI6I,GAAKmgB,EAAWngB,GAAK2f,EAAOnkB,KAAOskB,EAAc7H,EAAQzc,KAAMrE,EAAI8I,GAAKkgB,EAAWlgB,GAAK0f,EAAOtkB,IAAMykB,EAAc7H,EAAQ5c,KAClJqgB,EAAW/lB,KAAK+lB,SACXvnB,EAAI,EAAGA,EAAIunB,EAASrnB,OAAQF,IAC7BqoB,EAAOd,EAASvnB,GACpBqoB,EAAKZ,OAAOY,EAAKrlB,MAGzB2hB,MAAO,SAAUxB,EAAW/C,EAAMwE,GAC9BpjB,KAAKwB,IAAI2hB,MAAMxB,EAAW/C,EAAMwE,IAEpCqH,OAAQ,WACJ,GAAIxpB,GAAUjB,KAAKiB,OACnB,OAAOA,GAAQmpB,OAAOvrB,OAASoC,EAAQypB,YAE3CzD,aAAc,WACVhN,GAAagE,GAAGgJ,aAAa5B,KAAKrlB,KAClC,IAAIiB,GAAUjB,KAAKiB,OACfA,GAAQ+lB,SAAWhnB,KAAKyqB,UACxBzqB,KAAKsnB,OAAOpO,OAAOvN,GAAKgf,SAAS3qB,KAAKuqB,WAAWtH,SAAUjjB,KAAK4qB,iBAGxEA,YAAa,WAAA,GACL3pB,GAAUjB,KAAKiB,QACfmpB,EAASnpB,EAAQmpB,UACrB,QACI5mB,QACI3E,MAAOurB,EAAOvrB,MACd+M,MAAOwe,EAAOxe,MACdud,QAASxP,GAAeyQ,EAAOjB,QAASloB,EAAQkoB,SAChD9c,SAAU+d,EAAO/d,UAErB6c,MACItd,MAAO3K,EAAQypB,WACfvB,QAASloB,EAAQkoB,SAErB0B,OAAQ5pB,EAAQ4pB,WAI5BhjB,EAAkBqS,IACdiJ,MAAOld,GACPqkB,OAAQxkB,GACRkkB,UACA1H,WACA8H,QACIxe,MAAO8K,GACP7X,MAAO,GAEX6rB,WAAY,GACZT,aAAa,EACbprB,MAAO,EACPC,OAAQ,EACRkoB,SAAS,IAET7M,GAAeD,GAAWra,QAC1BC,KAAM,SAAUmB,EAAS6pB,GACrB5Q,GAAW+D,GAAGne,KAAKulB,KAAKrlB,KAAMiB,GAC9BjB,KAAK8qB,UAAYA,GAErBC,WAAY,WAAA,GAWJ9rB,GACA4D,EAXAoW,EAAMjZ,KACNiB,EAAUgY,EAAIhY,QACdO,EAAMyX,EAAIsR,WACVziB,EAAO7G,EAAQ6G,KACf8a,EAAW3hB,EAAQ2hB,SACnB9B,EAAStf,EAAIsf,SACbkK,EAAYxpB,EAAI3C,QAAU,CAC9B,OAAKoC,GAAQ+lB,SAAYhnB,KAAKyqB,UAG1BxrB,EAAQe,KAAK4qB,cAEb9iB,IAAS8O,GACT/T,EAAU,GAAIY,IAAQ0S,OAAO,GAAIA,KAC7BtS,GAAMrC,EAAI6I,GAAK2gB,EAAW5jB,IAC1BvD,GAAMrC,EAAI8I,GAAK9I,EAAI1C,SAAW,EAAGsI,KAClC4jB,GAAY/rB,GACR6I,IAASgQ,GAChBjV,EAAU8I,GAAKsf,aAEPzpB,EAAI6I,GAAK2gB,EACTxpB,EAAI8I,KAGJ9I,EAAI6I,GACJ7I,EAAIgJ,KAGJhJ,EAAI+I,GACJ/I,EAAIgJ,KAETvL,GAAO4mB,QACH/d,IAAS+O,IAChBhU,EAAU,GAAIY,IAAQynB,UAAUjsB,GAChC4D,EAAQiJ,OAAOtK,EAAI6I,GAAI7I,EAAI8I,IAAIyB,OAAOvK,EAAI+I,GAAI/I,EAAIgJ,IAClD3H,EAAQiJ,OAAOtK,EAAI6I,GAAI7I,EAAIgJ,IAAIuB,OAAOvK,EAAI+I,GAAI/I,EAAI8I,KAElDzH,EAAU8I,GAAKgf,SAASnpB,EAAIyhB,SAAUhkB,GAEtC2jB,GACA/f,EAAQ2Z,UAAUpG,KAAoByK,QAAQ+B,GAC1C9B,EAAO5c,EACP4c,EAAO3c,KAGftB,EAAQ5B,QAAQwmB,OAASxmB,EAAQwmB,OAC1B5kB,GAtCI,MAwCf9B,cAAe,WAAA,GAIPumB,GAHA7H,EAASzf,KACTgpB,EAAehpB,KAAKiB,QAAQqmB,OAC5BwD,EAAY9qB,KAAK8qB,aAkBrB,OAfIxD,GADA0B,EACSA,GACL3oB,MAAOyqB,EAAUzqB,MACjBipB,SAAUwB,EAAUxB,SACpBjL,OAAQre,KAAKumB,YACb8C,OAAQyB,EAAUzB,OAClBlX,SAAU2Y,EAAU3Y,SACpBjH,KAAMlL,KAAKuqB,WAAWtH,SACtBhiB,QAASjB,KAAKmrB,gBACdlE,aAAc,WACV,MAAOxH,GAAOsL,gBAIb/qB,KAAK+qB,cAItBI,cAAe,WACX,GAAIlqB,GAAUjB,KAAKiB,OACnB,QACIypB,WAAYzpB,EAAQypB,WACpBN,OAAQnpB,EAAQmpB,OAChBJ,OAAQ/oB,EAAQ+oB,OAChB1H,QAASrhB,EAAQqhB,QACjBxa,KAAM7G,EAAQ6G,KACd/H,KAAMkB,EAAQpC,MACdmoB,QAAS/lB,EAAQ+lB,UAGzBC,aAAc,WACVjnB,KAAKsnB,OAAStnB,KAAKe,mBAG3B8G,EAAkBsS,IACdrS,KAAM8O,GACNuM,MAAOxM,GACP2T,OAAQ3T,KAERyD,GAAS,SACTC,GAAS,SACTC,IACA8Q,OACItjB,KAAMsS,GACNwI,SAAU,EACVhY,QAEQrH,OAAQ,EACRqI,MAAOoM,GACPmR,QAAS,IAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,KAGT5lB,OAAQ,EACRqI,MAAOoM,GACPmR,QAAS,KAIrBkC,YACIvjB,KAAMuS,GACNzP,QAEQrH,OAAQ,EACRqI,MAAOoM,GACPmR,QAAS,MAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,IAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,OAIrBmC,cACIxjB,KAAMuS,GACNzP,QAEQrH,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,MAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,KAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,KAIrBoC,cACIzjB,KAAMuS,GACNmR,YAAY,EACZ5gB,QAEQrH,OAAQ,EACRqI,MAAOoM,GACPmR,QAAS,IAGT5lB,OAAQ,GACRqI,MAAOoM,GACPmR,QAAS,KAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,KAIrBsC,YACI3jB,KAAMuS,GACNmR,YAAY,EACZ5gB,QAEQrH,OAAQ,EACRqI,MAAOoM,GACPmR,QAAS,KAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,MAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,MAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,MAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,MAGT5lB,OAAQ,IACRqI,MAAOoM,GACPmR,QAAS,KAIrBuC,cACI5jB,KAAMuS,GACNyG,QACI,GACA,IAEJ/V,OAAQ,KA+BZwP,GAAcN,GAAapa,QAC3BC,KAAM,SAAUmB,GACZgZ,GAAagE,GAAGne,KAAKulB,KAAKrlB,KAAMiB,EAChC,IAAI0qB,GAAc3rB,KAAKiB,OACvB0qB,GAAY9sB,MAAQkf,SAAS4N,EAAY9sB,MAAO,IAChD8sB,EAAY7sB,OAASif,SAAS4N,EAAY7sB,OAAQ,IAClDkB,KAAK4rB,cAET3F,OAAQ,WAAA,GAMKznB,GALLya,EAAMjZ,KACNiB,EAAUgY,EAAIhY,QACd8kB,EAAW9M,EAAI8M,SACf8F,EAAa,GAAIphB,IAAI,EAAG,EAAGxJ,EAAQpC,MAAOoC,EAAQnC,OAEtD,KADAkB,KAAKwB,IAAMqqB,EAAWtJ,MAAMthB,EAAQ+oB,QAC3BxrB,EAAI,EAAGA,EAAIunB,EAASrnB,OAAQF,IACjCunB,EAASvnB,GAAGynB,OAAO4F,GACnBA,EAAahiB,EAAQgiB,EAAY9F,EAASvnB,GAAGgD,MAAQ,GAAIiJ,KAGjEwc,aAAc,WACVjnB,KAAKsnB,OAAS,GAAItR,IAClBhW,KAAK8rB,oBAETA,iBAAkB,WAAA,GACV7qB,GAAUjB,KAAKiB,QACfmpB,EAASnpB,EAAQmpB,WACjB5oB,EAAMxB,KAAKwB,IAAIkf,QAAQ2B,IAAIphB,EAAQ+oB,QAAQzH,MAAM6H,EAAOvrB,OACxD6rB,EAAa/e,GAAKgf,SAASnpB,EAAIyhB,UAC/Bzf,QACIoI,MAAOwe,EAAOvrB,MAAQurB,EAAOxe,MAAQ,GACrC/M,MAAOurB,EAAOvrB,MACdwN,SAAU+d,EAAO/d,UAErB6c,MACItd,MAAO3K,EAAQypB,WACfvB,QAASloB,EAAQkoB,SAErB1B,YAEJznB,MAAKsnB,OAAOpO,OAAOwR,IAEvBrE,QAAS,WACL,MAAOrmB,OAEX6pB,eAAgB,SAAU5oB,GAAV,GAIR8qB,GAIIpC,EAPJiC,EAAY5rB,KAAK4rB,UACjBI,EAAWjuB,GAAUkD,GACrBgrB,EAAW3R,GAAUrZ,EAAQgrB,SAiBjC,OAfIL,GAAUI,GACVD,EAAkBH,EAAUI,IAExBrC,EAAkBnsB,EAAEqC,UAAWosB,EAAUhrB,GACvB,WAAlBgrB,EAASnkB,KACTikB,EAAkB,GAAItoB,IAAQyoB,eAAevC,IAEzC1oB,EAAQ6J,cACR6e,EAAgB/e,MAAQF,EAAiBif,IAE7CoC,EAAkB,GAAItoB,IAAQ0oB,eAAexC,GAC7CoC,EAAgBP,WAAaS,EAAST,cAAe,GAEzDI,EAAUI,GAAYD,GAEnBA,GAEXK,eAAgB,WAAA,GAEHJ,GADLJ,EAAY5rB,KAAK4rB,SACrB,KAASI,IAAYJ,GACjBA,EAAUI,GAAUK,eAG5BtsB,KAAM,WACF,GAAIkB,GAAUjB,KAAKiB,OACnB,OAAO,IAAIwJ,IAAI,EAAG,EAAGxJ,EAAQpC,MAAOoC,EAAQnC,WAGpD+I,EAAkB0S,IACd1b,MAAOoY,GACPnY,OAAQkY,GACR0T,WAAY1S,GACZoS,QACIxe,MAAO8K,GACP7X,MAAO,GAEXmrB,OAAQzkB,EAAW,GACnBkiB,YAcAjN,GAAeP,GAAapa,QAC5BC,KAAM,SAAUmB,GACZgZ,GAAagE,GAAGne,KAAKulB,KAAKrlB,KAAMiB,GAChCjB,KAAKssB,kBAETA,eAAgB,WACZ,GAAIrrB,GAAUjB,KAAKiB,OACfA,GAAQ4K,UACR7L,KAAKusB,UAAYrU,GACjBlY,KAAKwsB,YAAcrU,GACnBnY,KAAKysB,eAAiBxU,GACtBjY,KAAK0sB,iBAAmBtV,GACxBpX,KAAK2sB,aAAe1rB,EAAQwE,QAC5BzF,KAAK4sB,eAAiB3rB,EAAQ4rB,WAE9B7sB,KAAKusB,UAAYpU,GACjBnY,KAAKwsB,YAActU,GACnBlY,KAAKysB,eAAiBrV,GACtBpX,KAAK0sB,iBAAmBzU,GACxBjY,KAAK2sB,aAAe1rB,EAAQ4rB,SAC5B7sB,KAAK4sB,eAAiB3rB,EAAQwE,UAGtCwgB,OAAQ,SAAUtE,GACd3hB,KAAKwB,IAAMmgB,EAAUjB,QACrB1gB,KAAK8sB,kBAETA,eAAgB,WAAA,GAeJC,GACKC,EACDC,EACAC,EACAC,EACAC,EACKrqB,EACDF,EACAwqB,EACAC,EACAC,EAxBZ9N,EAASzf,KACTiZ,EAAMjZ,KACNwB,EAAMyX,EAAIzX,IACVgrB,EAAcvT,EAAIuT,YAClBD,EAAYtT,EAAIsT,UAChBG,EAAmBzT,EAAIyT,iBACvBD,EAAiBxT,EAAIwT,eACrBe,EAAQxtB,KAAKytB,eACbC,EAASF,EAAME,OACfC,EAAaH,EAAMG,WACnBC,EAAuBJ,EAAMI,qBAC7BC,EAAcH,EAAOhvB,OACrBovB,EAActsB,EAAI+qB,EAAY,GAAKvsB,KAAK+tB,WAAWJ,EAAYnsB,EAAIirB,KACvE,IAAIoB,EAAa,CAEb,IADId,EAAae,EACRd,EAAW,EAAGA,EAAWa,EAAab,IAAY,CAKvD,IAJIC,EAAQS,EAAOV,GACfE,EAAgBD,EAAMC,cACtBC,EAAe3rB,EAAIgrB,EAAc,GACjCY,EAAqBF,EAAcxuB,OAC9BqE,EAAM,EAAGA,EAAMqqB,EAAoBrqB,IACpCF,EAAUqqB,EAAcnqB,GACxBsqB,EAAiB5N,EAAOnG,YAAYzW,GACpCyqB,EAAoBP,EAAatN,EAAOsO,WAAWV,EAAeZ,GAAiBQ,EAAMe,WACzFT,EAAa,GAAI9iB,IACrB8iB,EAAWhB,EAAY,GAAKe,EAC5BC,EAAWhB,EAAY,GAAKe,EAAoBD,EAAeZ,GAC/Dc,EAAWf,EAAc,GAAKW,EAC9BI,EAAWf,EAAc,GAAKW,EAAeE,EAAeX,GAC5D7pB,EAAQojB,OAAOsH,GACfJ,GAAgBE,EAAeX,GAAoBjN,EAAOmN,cAE9DG,IAAcE,EAAMe,UAAYvO,EAAOkN,aAE3CnrB,EAAI+qB,EAAY,GAAKuB,EACrBtsB,EAAI+qB,EAAY,GAAKuB,EAAcH,EACnCnsB,EAAIgrB,EAAc,GAAKhrB,EAAIgrB,EAAc,GAAKoB,IAGtDG,WAAY,SAAUhuB,EAAMkuB,GAAhB,GACJhnB,GAAQ,EACRkc,EAAQnjB,KAAKiB,QAAQkiB,KAMzB,OALIA,KAAUpd,IAASod,IAAUnd,GAC7BiB,EAAQgnB,EAAUluB,EACXojB,IAAUxM,KACjB1P,GAASgnB,EAAUluB,GAAQ,GAExBkH,GAEXwmB,aAAc,WAAA,GAiBD1qB,GACDF,EAIAwqB,EArBJ5N,EAASzf,KACTiZ,EAAMjZ,KACNwB,EAAMyX,EAAIzX,IACVukB,EAAW9M,EAAI8M,SACf2G,EAAmBzT,EAAIyT,iBACvBD,EAAiBxT,EAAIwT,eACrBG,EAAiB3T,EAAI2T,eACrBD,EAAe1T,EAAI0T,aACnBsB,EAAUpqB,GAAMrC,EAAIkrB,MACpB9F,EAAgBb,EAASrnB,OACzBgvB,KACAM,EAAY,EACZE,EAAoB,EACpBP,EAAa,EACbC,EAAuB,EACvBV,IACJ,KAASnqB,EAAM,EAAGA,EAAM6jB,EAAe7jB,IAC/BF,EAAUkjB,EAAShjB,GAClBF,EAAQrB,KACTqB,EAAQojB,OAAOzkB,GAEf6rB,EAAiB5N,EAAOnG,YAAYzW,GACpC4c,EAAOxe,QAAQygB,MAAQ7d,GAAMqqB,EAAoBtB,EAAiBS,EAAeX,IAAqBuB,IACtGP,EAAOvvB,MACH+uB,cAAeA,EACfc,UAAWA,EACXE,kBAAmBA,IAEvBN,EAAuBnmB,KAAKY,IAAIulB,EAAsBM,GACtDP,GAAchB,EAAeqB,EAC7BA,EAAY,EACZE,EAAoB,EACpBhB,MAEJc,EAAYvmB,KAAKY,IAAI2lB,EAAWX,EAAeZ,IAC3CyB,EAAoB,IACpBA,GAAqBtB,GAEzBsB,GAAqBb,EAAeX,GACpCQ,EAAc/uB,KAAK0E,EASvB,OAPA6qB,GAAOvvB,MACH+uB,cAAeA,EACfc,UAAWA,EACXE,kBAAmBA,IAEvBN,EAAuBnmB,KAAKY,IAAIulB,EAAsBM,GACtDP,GAAcK,GAEVN,OAAQA,EACRC,WAAYA,EACZC,qBAAsBA;GAG9BtU,YAAa,SAAUzW,GACnB,OACIhE,MAAOgE,EAAQrB,IAAI3C,QACnBC,OAAQ+D,EAAQrB,IAAI1C,WAG5BmoB,aAAc,eAGlBpf,EAAkB2S,IACd3O,UAAU,EACV6V,MAAM,EACNmL,SAAU,EACVpnB,QAAS,IAETgV,GAAchX,GAAQiX,KACtBA,GAAOT,GAAapa,QACpBC,KAAM,SAAUyG,EAAStF,GACrBgZ,GAAagE,GAAGne,KAAKulB,KAAKrlB,KAAMiB,GAChCjB,KAAKuG,QAAUA,EACfvG,KAAKimB,OAAO,GAAIxb,MAEpBwb,OAAQ,SAAUtE,GAAV,GACA1gB,GAAUjB,KAAKiB,QACflB,EAAOkB,EAAQlB,KAAOJ,GAAKX,YAAYgB,KAAKuG,SAAW4Z,KAAMlf,EAAQkf,MACzEngB,MAAKjB,SAAWgB,EAAKhB,SACrBiB,KAAKwB,IAAM,GAAIiJ,IAAIkX,EAAUtX,GAAIsX,EAAUrX,GAAIqX,EAAUtX,GAAKtK,EAAKlB,MAAO8iB,EAAUrX,GAAKvK,EAAKjB,SAElGmoB,aAAc,WAAA,GACNhO,GAAMjZ,KAAKiB,QACXkf,EAAOlH,EAAIkH,KACXvU,EAAQqN,EAAIrN,MACZud,EAAUlQ,EAAIkQ,QACd0B,EAAS5R,EAAI4R,MACjB7qB,MAAKsnB,OAAS,GAAI7M,IAAYza,KAAKuG,QAASvG,KAAKwB,IAAIyhB,SAASkL,WAC1DhO,KAAMA,EACN+I,MACItd,MAAOA,EACPud,QAASA,GAEb0B,OAAQA,OAIpBhjB,EAAkB6S,IACdyF,KAAMpJ,GACNnL,MAAO8K,KAOPiE,GAAmB,MACnBC,GAAUV,GAAWra,QACrBC,KAAM,SAAUyG,EAAStF,EAASmtB,GAC9BlU,GAAW+D,GAAGne,KAAKulB,KAAKrlB,KAAMiB,GAC9BjB,KAAKuG,QAAUA,EACfvG,KAAKouB,KAAOA,EACZpuB,KAAKquB,iBACDruB,KAAKiB,QAAQqtB,eAAgB,GAC7BtuB,KAAKimB,OAAO,GAAIxb,MAGxB4jB,eAAgB,WAAA,GAcHE,GACD7wB,EAdJuD,EAAUjB,KAAKiB,QACfutB,GAAcxuB,KAAKuG,QAAZ5I,IAAqBmJ,MAAM6T,IAClC8T,EAAe,GAAIjU,KACnB3O,UAAU,EACVsX,MAAOliB,EAAQkiB,MACfzB,MAAM,IAENgN,EAActsB,MAAenB,GAC7BkoB,QAAS,EACT/C,UAAW,MAIf,KAFApmB,KAAK2uB,UAAYF,EACjBzuB,KAAKkZ,OAAOuV,GACHF,EAAS,EAAGA,EAASC,EAAK9vB,OAAQ6vB,IACnC7wB,EAAO,GAAIgd,IAAK8T,EAAKD,GAAQK,OAAQF,GACzCD,EAAavV,OAAOxb,IAG5BuoB,OAAQ,SAAUtE,GAAV,GAKIkN,GAOAvH,EASI0C,EACAxoB,EArBRP,EAAUjB,KAAKiB,QACf6tB,EAAW7tB,EAAQqmB,MACvBtnB,MAAK2uB,UAAU1tB,QAAQkiB,MAAQliB,EAAQkiB,MACnC2L,IAAa9uB,KAAK+uB,YACdF,EAAYlN,EACXkN,EAAU3L,YACXljB,KAAK+uB,YAAa,EAClB/uB,KAAKimB,OAAO4I,GACZ7uB,KAAK+uB,YAAa,EAClBF,EAAY7uB,KAAKwB,KAEjB8lB,EAAStnB,KAAKsnB,OAASwH,EAAS9uB,KAAKgvB,cAAcH,IACnDvH,IACAuH,EAAY5jB,EAAUqc,EAAO2H,eAAiB,GAAI/Y,KAClDoR,EAAOrmB,QAAQwmB,OAASxmB,EAAQwmB,QAEpCznB,KAAKwB,IAAMxB,KAAKwqB,WAAaxqB,KAAKuqB,WAAasE,IAE/C3U,GAAW+D,GAAGgI,OAAOZ,KAAKrlB,KAAM2hB,GAC5B1gB,EAAQ2hB,WACJoH,EAASzkB,EAAWtE,EAAQ+oB,QAC5BxoB,EAAMxB,KAAKwB,IAAI+gB,MAAMyH,GACzBhqB,KAAK2hB,UAAYA,EACjB3hB,KAAKkvB,UAAY1tB,EAAIkf,QACrBlf,EAAMxB,KAAK6gB,SACXrf,EAAIsC,UAAUkmB,EAAOnkB,KAAOmkB,EAAOrkB,MAAOqkB,EAAOtkB,IAAMskB,EAAOpkB,QAC9D5F,KAAKmvB,WAAa3tB,EAAIkf,QACtBlf,EAAI6gB,IAAI2H,MAIpB/C,aAAc,WAAA,GAQFzlB,GAPJP,EAAUjB,KAAKiB,OACnBjB,MAAKsnB,OAAS,GAAItR,KACdwG,UAAWxc,KAAKovB,oBAChB3H,OAAQxmB,EAAQwmB,OAChBG,OAAQ3mB,EAAQ2mB,SAEhB5nB,KAAKyqB,WACDjpB,EAAMmK,GAAKgf,SAAS3qB,KAAKuqB,WAAWtH,SAAUjjB,KAAK4qB,eACvD5qB,KAAKsnB,OAAOpO,OAAO1X,KAG3BulB,aAAc,WACV,GAAK/mB,KAAKiB,QAAQ+lB,QAGlB,GAAIhnB,KAAKiB,QAAQqmB,OAAQ,CACrB,GAAIA,GAAStnB,KAAKsnB,MACdA,KAAW5jB,GAAQ4jB,EAAOrmB,QAAQ2mB,UAClCN,EAAOrmB,QAAQ2mB,OAAS5nB,KAAKiB,QAAQ2mB,QAEzC5nB,KAAKknB,YACLlnB,KAAKonB,sBAELlN,IAAW+D,GAAG8I,aAAa1B,KAAKrlB,OAGxCgvB,cAAe,SAAUrN,GAAV,GACPlC,GAASzf,KACToe,GACA1gB,KAAMsC,KAAKuG,QACX2E,KAAMyW,EAAUsB,SAChB5E,OAAQre,KAAKumB,YACbtlB,QAASjB,KAAKiB,QACdgmB,aAAc,WAIV,MAHAxH,GAAOsP,YAAa,EACpBtP,EAAOwG,OAAOtE,GACdlC,EAAOsP,YAAa,EACbtP,EAAO4P,oBAMtB,OAHIrvB,MAAKouB,MACL5wB,EAAEqC,OAAOue,EAASpe,KAAKouB,MAEpBhQ,GAEXiR,iBAAkB,WACdrvB,KAAKinB,eACLjnB,KAAKmnB,gBACL,IAAIG,GAAStnB,KAAKsnB,MAElB,cADOtnB,MAAKsnB,OACLA,GAEXzG,OAAQ,WACJ,GAAI5f,GAAUjB,KAAKiB,OAInB,OAHAjB,MAAKwB,IAAIqf,OAAO5f,EAAQ2hB,UACxB5iB,KAAKmjB,MAAMnjB,KAAK2hB,UAAWzJ,GAAGjX,EAAQkiB,OACtCnjB,KAAKmjB,MAAMnjB,KAAK2hB,UAAWxJ,GAAGlX,EAAQqpB,QAC/BtqB,KAAKwB,KAEhB4tB,kBAAmB,WAAA,GAKXnW,GACA/P,EACAC,EACAmmB,EAPA1M,EAAW5iB,KAAKiB,QAAQ2hB,QAC5B,OAAKA,IAGD3J,EAAMjZ,KAAKkvB,UAAUpO,SACrB5X,EAAK+P,EAAI/U,EACTiF,EAAK8P,EAAI9U,EACTmrB,EAAYtvB,KAAKmvB,WAAWrO,SACzB1K,KAAoBtS,UAAUwrB,EAAUprB,EAAIgF,EAAIomB,EAAUnrB,EAAIgF,GAAI0X,OAAO+B,GAC5E1Z,EACAC,KARO,QAYf0R,GAAQZ,GAAapa,QACrBC,KAAM,SAAUmB,GACZgZ,GAAagE,GAAGne,KAAKulB,KAAKrlB,KAAMiB,GAChCjB,KAAKkZ,OAAO,GAAI0B,IAAQ5a,KAAKiB,QAAQvD,KAAMF,EAAEqC,UAAWG,KAAKiB,SAAWqpB,OAAQtqB,KAAKiB,QAAQwK,cAEjGwa,OAAQ,SAAUtE,GACd1H,GAAagE,GAAGgI,OAAOZ,KAAKrlB,KAAM2hB,GAClC3hB,KAAKwB,IAAIsgB,OAAOH,EAAWzJ,OAGnC2C,GAAM0U,WAAa,SAAUtuB,EAASqlB,EAAQkJ,GAA3B,GAMXC,GALAC,EAAezuB,CAUnB,OATuB,gBAAZA,KACPyuB,GAAiBhyB,KAAMuD,IAE3ByuB,EAAelyB,EAAEqC,QAASmnB,SAAS,GAAQwI,EAAgBE,GAEvDA,GAAgBA,EAAa1I,SAAW0I,EAAahyB,OACrD+xB,EAAQ,GAAI5U,IAAM6U,GAClBpJ,EAAOpN,OAAOuW,IAEXA,GAEX5nB,EAAkBgT,IACdjP,MAAO8K,GACPjL,SAAU3F,GACVqd,MAAOxM,GACPqT,OAAQzkB,EAAW,GACnB+c,QAAS/c,EAAW,KAEpBuV,GAAYF,GAAQ/a,QACpBC,KAAM,SAAUO,EAAO3C,EAAMmS,EAAOyZ,EAAUroB,GAC1C2Z,GAAQqD,GAAGne,KAAKulB,KAAKrlB,KAAMtC,EAAMuD,GACjCjB,KAAKtC,KAAOA,EACZsC,KAAKK,MAAQA,EACbL,KAAK6P,MAAQA,EACb7P,KAAKspB,SAAWA,EAChBtpB,KAAKimB,OAAO,GAAIxb,MAEpBukB,cAAe,SAAUrN,GACrB,GAAIvD,GAAUxD,GAAQqD,GAAG+Q,cAAc3J,KAAKrlB,KAAM2hB,EAKlD,OAJAvD,GAAQ/d,MAAQL,KAAKK,MACrB+d,EAAQkL,SAAWtpB,KAAKspB,SACxBlL,EAAQzB,OAAS3c,KAAKiB,QAAQ0b,OAC9ByB,EAAQuR,QAAU3vB,KAAKiB,QAAQ0uB,QACxBvR,GAEXwR,MAAO,SAAUC,EAAQvoB,GACrBuoB,EAAOnR,QAAQjI,IACX5T,QAAS0W,GAAajS,GACtBjH,MAAOL,KAAKK,MACZ3C,KAAMsC,KAAKtC,KACXmS,MAAO7P,KAAK6P,MACZyZ,SAAUtpB,KAAKspB,SACf1K,KAAM5e,KAAKsmB,OAAOrlB,WAG1B4f,OAAQ,WAAA,GAEIrf,GACAgb,CAKR,OAPIxc,MAAKiB,QAAQ6uB,gBAAkBnZ,IAC3BnV,EAAMxB,KAAKkvB,UAAUjM,SACrBzG,EAAYxc,KAAKovB,oBACrBpvB,KAAKwB,IAAMyJ,EAAUzJ,EAAIuuB,KAAKvT,EAAUwT,YAExCpV,GAAQqD,GAAG4C,OAAOwE,KAAKrlB,MAEpBA,KAAKwB,KAEhB4tB,kBAAmB,WAAA,GASXa,GACAzuB,EACA0J,EACAglB,EACAC,EACAC,EACAC,EACAlC,EACAmC,EACAllB,EACAmlB,EACApB,EACArrB,EAEA0sB,EACAC,EACA1C,EAAY2C,EAWZC,EAnCA1vB,EAAUjB,KAAKiB,QACf2hB,EAAW3hB,EAAQ2hB,QACvB,OAAKA,GAGD3hB,EAAQ6uB,gBAAkBnZ,GACnBiE,GAAQqD,GAAGmR,kBAAkB/J,KAAKrlB,OAEzCiwB,EAAiB7Z,KAAoByK,OAAO+B,GAAUoN,SACtDxuB,EAAMxB,KAAKkvB,UAAUjM,SACrB/X,EAAOlL,KAAK2hB,UAAUsB,SACtBiN,EAAiBjvB,EAAQivB,gBAAkBpqB,GAC3CqqB,EAAYD,IAAmBpqB,IAAOoqB,IAAmBlqB,GAASkS,GAAIC,GACtEiY,EAAeF,IAAmBpqB,IAAOoqB,IAAmBlqB,GAASmS,GAAID,GACzEmY,EAAaH,IAAmBpqB,IAAOoqB,IAAmBjqB,GAAOiF,EAAKC,OAASD,EAAKE,cACpF+iB,EAAU3sB,EAAI2sB,UAAUyC,cAAcX,GACtCK,EAAW9uB,EAAI8uB,WAAWM,cAAcX,GACxC7kB,EAAc5J,EAAI4J,cAAcwlB,cAAcX,GAC9CM,EAAa/uB,EAAI+uB,aAAaK,cAAcX,GAC5Cd,EAAajZ,GAAK+U,WAAWkD,EAASmC,EAAUllB,EAAamlB,GAC7DzsB,KACJA,EAAUssB,GAAgBllB,EAAKC,OAAOilB,GAAgBjB,EAAWhkB,OAAOilB,GACpEI,EAAe/oB,KAAKqB,IAAIqlB,EAAQiC,GAAgBtsB,EAAUssB,GAAgBC,EAAWD,IACrFK,EAAgBhpB,KAAKqB,IAAIwnB,EAASF,GAAgBtsB,EAAUssB,GAAgBC,EAAWD,IAEvFvsB,GAAM2sB,EAAc3nB,MAAuBhF,GAAM4sB,EAAe5nB,KAChEklB,EAAaI,EACbuC,EAAWJ,GACJG,EAAgBD,GACvBzC,EAAauC,EACbI,EAAWtlB,IAEX2iB,EAAaI,EACbuC,EAAWH,GAEXI,EAAc5C,EAAWoC,IAAcO,EAASP,GAAapC,EAAWoC,IAAc,EAC1FrsB,EAAUqsB,GAAajlB,EAAK4V,SAASqP,GAAaQ,EAC3Cva,KAAoBtS,UAAUA,EAAUI,EAAGJ,EAAUK,GAAG0c,OAAO+B,IAlC3D,QAqCnB/a,EAAkBiT,IAAawT,aAAa,IACxCvT,GAAoB,EACpBC,GAAsB,OACtBC,GAAOf,GAAWra,QAClBC,KAAM,SAAUqU,EAAQlT,EAASylB,GAC7BxM,GAAW+D,GAAGne,KAAKulB,KAAKrlB,KAAMiB,GAC9BjB,KAAKmU,OAASA,EACdnU,KAAK0mB,aAAeA,EACpB1mB,KAAK6wB,UAETC,KAAM,WACF9wB,KAAKiB,QAAQ+lB,SAAU,GAE3B8B,KAAM,WACF9oB,KAAKiB,QAAQ+lB,SAAU,GAE3B6J,OAAQ,WAAA,GAIIE,GACAC,EACAxvB,EACAyvB,EAGAlxB,EACArC,EACAmB,EAAOC,EAEHoyB,EAuBJ/uB,EApCJsd,EAASzf,KACTiB,EAAUjB,KAAKiB,OACfA,GAAQ+lB,UACJ+J,EAAQ9vB,EAAQ8vB,MAChBC,EAAO/vB,EAAQ+vB,KACfxvB,EAAM,GAAIiJ,IACVwmB,EAAa,WACb,MAAOxR,IAEP1f,EAAOixB,EAAKjxB,KACZrC,EAAOsC,KAAKmU,OAAOzW,KAEnBgG,GAAQqtB,IAAUA,EAAM/J,UACpBkK,EAAehrB,EAAY6qB,GAC3BG,EACAxzB,EAAOwzB,EAAalxB,KAAKmU,QAClB4c,EAAMpU,SACbjf,EAAOsC,KAAK0mB,aAAa/J,OAAOQ,KAAK4T,EAAMpU,OAAQjf,IAElDqzB,EAAMnlB,QACPmlB,EAAMnlB,MAAQmlB,EAAMtlB,WAAa6L,GAAS0D,GAAsBgW,EAAKtG,YAEzE1qB,KAAK+wB,MAAQ,GAAInW,IAAQld,EAAM0E,MAAe2uB,IAC9C/wB,KAAK+wB,MAAMI,SAAWF,EAClBF,EAAMtlB,WAAa6L,IAAW5T,GAAQ3D,KAClCixB,EAAKlpB,OAAS8O,GACd7W,EAAO0H,KAAKY,IAAIrI,KAAK+wB,MAAMvvB,IAAI3C,QAASmB,KAAK+wB,MAAMvvB,IAAI1C,WAEvDD,EAAQmB,KAAK+wB,MAAMvvB,IAAI3C,QACvBC,EAASkB,KAAK+wB,MAAMvvB,IAAI1C,UAE5B0C,EAAIkgB,KAAK1hB,KAAK+wB,MAAMvvB,OAG5BwvB,EAAKnyB,MAAQA,GAASkB,GAAQgb,GAC9BiW,EAAKlyB,OAASA,GAAUiB,GAAQgb,GAC5B5Y,EAAS,GAAIgY,IAAa/X,MAAe4uB,IAC7C7uB,EAAOgvB,SAAWF,EAClBjxB,KAAKmC,OAASA,EACdnC,KAAKkZ,OAAO/W,GACRnC,KAAK+wB,OACL/wB,KAAKkZ,OAAOlZ,KAAK+wB,OAErB5uB,EAAO8jB,OAAO,GAAIxb,KAClBzK,KAAKoxB,WAAa5vB,EAAIkgB,KAAKvf,EAAOX,OAG1CykB,OAAQ,SAAUtE,GAAV,GAUIzV,GAAW1K,EAAKgpB,EATpBvR,EAAMjZ,KACNiB,EAAUgY,EAAIhY,QACd8vB,EAAQ9X,EAAI8X,MACZ5uB,EAAS8W,EAAI9W,OACbivB,EAAanY,EAAImY,WACjBtQ,EAASa,EAAUb,SACnBpiB,EAASuC,EAAQmL,KAAK1N,OACtB+M,EAAWxK,EAAQwK,QACnBxK,GAAQ+lB,UAEJjgB,EAAQ0E,GACJxF,GACAF,KAEA0F,IAAaxF,IACbukB,EAAa4G,EAAWrP,QAAQJ,EAAWlW,GAAU3H,WAAWpF,EAAQijB,EAAUb,SAAS3c,EAAIitB,EAAWtQ,SAAS3c,GAC/GlD,EAAQmL,KAAK4a,UACb9a,GACIyV,EAAUtX,GACVyW,EAAO3c,GAEXnE,KAAKqxB,YACDnlB,GAEIse,EAAWjgB,GACXuW,EAAO3c,IAGf3C,EAAMgpB,EAAW9J,QAAQkB,UAAU1V,MAGvCse,EAAa4G,EAAWrP,QAAQJ,EAAWlW,GAAU3H,UAAUpF,EAAQijB,EAAUb,SAAS3c,EAAIitB,EAAWtQ,SAAS3c,GAC9GlD,EAAQmL,KAAK4a,UACb9a,GACIyV,EAAUpX,GACVuW,EAAO3c,GAEXnE,KAAKqxB,YACDnlB,GAEIse,EAAWngB,GACXyW,EAAO3c,IAGf3C,EAAMgpB,EAAW9J,QAAQkB,UAAU1V,KAIvCT,IAAazF,IACbwkB,EAAa4G,EAAWrP,QAAQJ,EAAWlW,GAAU3H,UAAU6d,EAAUb,SAAS5c,EAAIktB,EAAWtQ,SAAS5c,EAAGxF,GACzGuC,EAAQmL,KAAK4a,UACb9a,GACI4U,EAAO5c,EACPyd,EAAUnX,IAEdxK,KAAKqxB,YACDnlB,GAEI4U,EAAO5c,EACPsmB,EAAWlgB,KAGnB9I,EAAMgpB,EAAW9J,QAAQkB,UAAU1V,MAGvCse,EAAa4G,EAAWrP,QAAQJ,EAAWlW,GAAU3H,UAAU6d,EAAUb,SAAS5c,EAAIktB,EAAWtQ,SAAS5c,GAAIxF,GAC1GuC,EAAQmL,KAAK4a,UACb9a,GACI4U,EAAO5c,EACPyd,EAAUrX,IAEdtK,KAAKqxB,YACDnlB,GAEI4U,EAAO5c,EACPsmB,EAAWhgB,KAGnBhJ,EAAMgpB,EAAW9J,QAAQkB,UAAU1V,KAI3C/J,GACAA,EAAO8jB,OAAOuE,GAEduG,IACAA,EAAM9K,OAAOuE,GACTroB,IACIlB,EAAQ8vB,MAAMtlB,WAAamM,IAC3BmZ,EAAMvvB,IAAIugB,QAAQ5f,EAAOX,IAAKiK,GAElCslB,EAAM9K,OAAO8K,EAAMvvB,OAG3BxB,KAAKwqB,WAAaA,EAClBxqB,KAAK2hB,UAAYA,EACjB3hB,KAAKwB,IAAMA,GAAOgpB,IAG1BvD,aAAc,WACV/M,GAAW+D,GAAGgJ,aAAa5B,KAAKrlB,MAChCA,KAAKsnB,OAAOrmB,QAAQ2mB,OAAS5nB,KAAKiB,QAAQ2mB,OACtC5nB,KAAKiB,QAAQ+lB,SACbhnB,KAAKsxB,cAGbvK,aAAc,WAAA,GACNtH,GAASzf,KACTiB,EAAUjB,KAAKiB,QACf+nB,EAAe/nB,EAAQqmB,MACvBrmB,GAAQ+lB,SAAWgC,GACnBhpB,KAAKsnB,OAAS0B,EAAaxrB,EAAEqC,OAAOG,KAAKmU,QACrCkK,OAAQre,KAAKumB,YACbrb,KAAMlL,KAAK2hB,UAAUsB,SACrBhiB,SACIypB,WAAYzpB,EAAQypB,WACpBN,OAAQnpB,EAAQypB,WAChBsG,KAAM/vB,EAAQ+vB,KACdD,MAAO9vB,EAAQ8vB,MACf3kB,KAAMnL,EAAQmL,KACdX,SAAUxK,EAAQwK,SAClBub,QAAS/lB,EAAQ+lB,SAErBC,aAAc,WACVxH,EAAOwH,eACPxH,EAAO0H,gBACP,IAAIoK,GAAgB9R,EAAO6H,MAE3B,cADO7H,GAAO6H,OACPiK,MAGfvxB,KAAKknB,aAELhN,GAAW+D,GAAG8I,aAAa1B,KAAKrlB,OAGxCsxB,WAAY,WAAA,GAGAhuB,GAFJrC,EAAUjB,KAAKiB,QAAQmL,IACvBpM,MAAKqxB,aACD/tB,EAAOqI,GAAKsf,WAAWjrB,KAAKqxB,YAC5B7tB,QACIoI,MAAO3K,EAAQ2K,MACf/M,MAAOoC,EAAQpC,MACfwN,SAAUpL,EAAQoL,YAG1BhJ,EAAiBC,GACjBtD,KAAKsnB,OAAOpO,OAAO5V,KAG3BssB,MAAO,SAAUC,EAAQvoB,GACrB,GAAImX,GAAOze,KAAKwxB,UAAUlqB,EACrBuoB,GAAOnR,QAAQlH,GAAYiH,IAC5BnX,EAAEmqB,kBAGVC,KAAM,SAAU7B,EAAQvoB,GACpB,GAAImX,GAAOze,KAAKwxB,UAAUlqB,EACrBuoB,GAAOnR,QAAQjH,GAAYgH,IAC5BnX,EAAEmqB,kBAGVE,IAAK,SAAU9B,EAAQvoB,GACnB,GAAImX,GAAOze,KAAKwxB,UAAUlqB,EAC1BuoB,GAAOnR,QAAQhH,GAAY+G,IAE/B+S,UAAW,SAAUlqB,GACjB,GAAIrG,GAAUjB,KAAKiB,OACnB,OAAOzD,GAAEqC,OAAOG,KAAKmU,QACjBtR,QAAS0W,GAAajS,GACtB5J,KAAMgG,GAAQzC,EAAQ8vB,OAAS9vB,EAAQ8vB,MAAMrzB,KAAO,GACpD4pB,OAAQtnB,KAAKsnB,YAIzBzf,EAAkBoT,IACd+V,MACIhK,SAAS,EACTlf,KAAM8O,IAEVma,OACItlB,SAAU6L,GACV0P,SAAS,EACT7D,MAAOxM,GACP2T,OAAQ3T,IAEZvK,MAAQ4a,SAAS,GACjBA,SAAS,EACTvb,SAAU3F,GACV2hB,OAAQ,IAuCRvM,GAAOjB,GAAapa,QACpBC,KAAM,SAAUmB,EAASylB,GACA,SAAjBA,IACAA,EAAe,GAAIjO,KAEvBwB,GAAagE,GAAGne,KAAKulB,KAAKrlB,KAAMiB,GAChCjB,KAAK0mB,aAAeA,EACf1mB,KAAKiB,QAAQ+lB,UACdhnB,KAAKiB,QAAUmB,MAAepC,KAAKiB,SAC/B2wB,QAAU5K,SAAS,GACnB5a,MAAQ4a,SAAS,GACjBgD,OAAQ,EACR6H,cAAe,EACfC,cAAe,KAGvB9xB,KAAKiB,QAAQ8wB,WAAa3vB,OACtBwJ,MAAO5L,KAAKiB,QAAQmL,KAAKR,MACzB/M,MAAOmB,KAAKiB,QAAQmL,KAAKvN,MACzBmoB,QAAShnB,KAAKiB,QAAQ+wB,gBAAkBza,IACzCvX,KAAKiB,QAAQ8wB,YACZhyB,KAAMC,KAAKiB,QAAQ6wB,cACnB3O,MAAOnjB,KAAKiB,QAAQ+wB,gBAExBhyB,KAAKiB,QAAQgxB,WAAa7vB,OACtBwJ,MAAO5L,KAAKiB,QAAQmL,KAAKR,MACzB/M,MAAOmB,KAAKiB,QAAQmL,KAAKvN,MACzBmoB,QAAShnB,KAAKiB,QAAQixB,gBAAkB3a,IACzCvX,KAAKiB,QAAQgxB,YACZlyB,KAAMC,KAAKiB,QAAQ4wB,cACnB1O,MAAOnjB,KAAKiB,QAAQixB,gBAExBlyB,KAAKmyB,aACAnyB,KAAKiB,QAAQmxB,cACdpyB,KAAKqyB,eAETryB,KAAKsyB,cACLtyB,KAAKuyB,eAETJ,WAAY,aAEZK,YAAa,WACT,OACIrqB,IAAKnI,KAAKiB,QAAQ2wB,OAAOa,KACzBpqB,IAAKrI,KAAK0yB,gBAGlBL,aAAc,WAAA,GAWF1hB,GACAiS,EASK7f,EACDguB,EArBRtR,EAASzf,KACTiB,EAAUjB,KAAKiB,QACfkiB,EAAQliB,EAAQ4K,SAAW9F,GAAQ4Q,GACnCgc,EAAevwB,MAAenB,EAAQ2wB,QACtCzO,MAAOA,EACPsE,OAAQxmB,EAAQwmB,SAEhBxW,EAAOxJ,KAAKY,IAAI,EAAGsqB,EAAa1hB,KAEpC,IADAjR,KAAK4yB,cACDD,EAAa3L,QAWb,IAVIrW,EAAQ3Q,KAAKwyB,cACb5P,EAAW+P,EAAa/P,SACxBle,EAASke,KACT+P,EAAa7C,cAAgBlN,EAASO,MACtCwP,EAAa/P,SAAWA,EAASxZ,OAEP,SAA1BupB,EAAa/P,WACb+P,EAAa/P,SAAW,EACxB3hB,EAAQ4xB,kBAAmB,GAEtB9vB,EAAM4N,EAAMxI,IAAKpF,EAAM4N,EAAMtI,IAAKtF,GAAOkO,EAC1C8f,EAAQtR,EAAOqT,gBAAgB/vB,EAAK4vB,GACpC5B,IACAtR,EAAOvG,OAAO6X,GACdtR,EAAOmS,OAAOzzB,KAAK4yB,KAKnC6B,YAAa,WACT5yB,KAAK+lB,SAAWvf,EAAKxG,KAAK+lB,SAAU,SAAUwC,GAC1C,QAASA,YAAiBzN,OAE9B9a,KAAK4xB,WAETmB,WAAY,WACR,GAAItT,GAASzf,IACTA,MAAKyvB,QACLzvB,KAAK+lB,SAAWvf,EAAKxG,KAAK+lB,SAAU,SAAUwC,GAC1C,MAAOA,KAAU9I,EAAOgQ,QAE5BzvB,KAAKyvB,MAAQlnB,SAGrByqB,MAAO,WACHhzB,KAAK4yB,cACL5yB,KAAK+yB,cAETE,QAAS,WAAA,GACDha,GAAMjZ,KACNiB,EAAUgY,EAAIhY,QACdO,EAAMyX,EAAIzX,IACVqK,EAAW5K,EAAQ4K,SACnBqnB,EAASjyB,EAAQ2wB,OAAOsB,OACxBC,EAAQD,EAAS1xB,EAAI6I,GAAK7I,EAAI+I,GAC9B6oB,EAAQF,EAAS1xB,EAAIgJ,GAAKhJ,EAAI8I,GAC9B+oB,EAAYpyB,EAAQmL,KAAKvN,OAAS,CACtC,OAAOgN,GAAW,GAAIpB,IAAI0oB,EAAO3xB,EAAI8I,GAAI6oB,EAAO3xB,EAAIgJ,GAAK6oB,GAAa,GAAI5oB,IAAIjJ,EAAI6I,GAAI+oB,EAAO5xB,EAAI+I,GAAK8oB,EAAWD,IAErHd,YAAa,WAAA,GASD7C,GARJxuB,EAAUjB,KAAKiB,QACfyuB,EAAettB,IACfwgB,SAAU3hB,EAAQ4K,aAAiB,EACnCnO,KAAM,GACN+pB,OAAQ,EACR6L,YAAY,GACbryB,EAAQwuB,MACPC,GAAa1I,SAAW0I,EAAahyB,OACjC+xB,EAAQ,GAAI7U,IAAQ8U,EAAahyB,KAAMgyB,GAC3C1vB,KAAKkZ,OAAOuW,GACZzvB,KAAKyvB,MAAQA,IAGrB8C,YAAa,WAAA,GAMA/zB,GACDqoB,EAEA0M,EARJ9T,EAASzf,KACTiB,EAAUjB,KAAKiB,QACfuyB,EAAQvyB,EAAQuyB,MAChBC,EAAQD,EAAMpF,QAElB,KADApuB,KAAKwzB,SACIh1B,EAAI,EAAGA,EAAIi1B,EAAM/0B,OAAQF,IAC1BqoB,EAAOzkB,MAAeoxB,EAAOC,EAAMj1B,IACvCqoB,EAAKxmB,MAAQof,EAAOiU,eAAe7M,EAAKxmB,OACpCkzB,EAAO,GAAItY,KACX5a,MAAOwmB,EAAKxmB,MACZ3C,KAAMmpB,EAAKkK,MAAMrzB,KACjB4rB,SAAUzC,GACXA,EAAMpH,EAAOiH,cACZ6M,EAAKtyB,QAAQ+lB,UACTtjB,GAAQ6vB,EAAKtyB,QAAQwK,UACjBxK,EAAQ4K,WAAa9E,EAAQwsB,EAAKtyB,QAAQwK,UACtCxF,GACAF,KAEJwtB,EAAKtyB,QAAQwK,SAAWxK,EAAQ0yB,QAAU1tB,GAAOF,GACzC9E,EAAQ4K,UAAa9E,EAAQwsB,EAAKtyB,QAAQwK,UAC9C3F,GACAE,OAEJutB,EAAKtyB,QAAQwK,SAAWxK,EAAQ0yB,QAAU3tB,GAASF,IAInDytB,EAAKtyB,QAAQwK,SADbxK,EAAQ4K,SACgB5K,EAAQ0yB,QAAU1tB,GAAOF,GAEzB9E,EAAQ0yB,QAAU3tB,GAASF,GAG3D2Z,EAAOvG,OAAOqa,GACd9T,EAAO+T,MAAMr1B,KAAKo1B,KAI9BG,eAAgB,SAAUrzB,GACtB,MAAOA,IAEX0mB,aAAc,WACV9M,GAAagE,GAAG8I,aAAa1B,KAAKrlB,MAClCA,KAAK4zB,mBAET3M,aAAc,WACVhN,GAAagE,GAAGgJ,aAAa5B,KAAKrlB,MAClCA,KAAK8rB,mBACL9rB,KAAKsxB,cAETuC,gBAAiB,WACb,GAAIC,GAAY9zB,KAAK+zB,UAKrB,OAJKD,KACDA,EAAY9zB,KAAK+zB,WAAa,GAAI/d,KAAQyR,YAC1CznB,KAAKwnB,aAAaxnB,KAAK+zB,aAEpBD,GAEXE,YAAa,SAAUC,GAMnB,QAASpD,GAAOqD,EAAe5oB,EAAa6oB,GAA5C,GAIiB31B,GAHT4T,EAAQ8hB,EAAcx1B,OACtBuS,EAAOxJ,KAAKY,IAAI,EAAGiD,EAAY2F,KACnC,IAAI3F,EAAY0b,QACZ,IAASxoB,EAAI8M,EAAYmnB,KAAMj0B,EAAI4T,EAAO5T,GAAKyS,EACvCvN,GAAQywB,IAAa31B,EAAI21B,IAAa,IAG1CC,EAAgB7oB,MAAQ2nB,EAASD,EAAQ1oB,GAAK0oB,EAAQ1oB,GAAKe,EAAYvL,KACvEq0B,EAAgB5oB,MAAQ0nB,EAASD,EAAQ3oB,GAAKgB,EAAYvL,KAAOkzB,EAAQ3oB,GACzE8pB,EAAgB3oB,SAAWyoB,EAAc11B,GACzCy1B,EAAU/a,OAAO7N,EAAe+oB,EAAiB9oB,KAjBpD,GACLrK,GAAUjB,KAAKiB,QACfgyB,EAAUjzB,KAAKizB,UACfC,EAASjyB,EAAQ2wB,OAAOsB,OACxBrf,EAAY5S,EAAQgxB,WAAWjL,QAAU/lB,EAAQ4S,UAAY,EAC7DugB,GAAoBvoB,SAAU5K,EAAQ4K,SAgB1CglB,GAAO7wB,KAAKq0B,wBAAyBpzB,EAAQgxB,YAC7CpB,EAAO7wB,KAAKs0B,wBAAyBrzB,EAAQ8wB,WAAYle,EAAY5S,EAAQwT,YAEjF6c,WAAY,WAAA,GAKAhuB,GAWA2pB,EAfJhsB,EAAUjB,KAAKiB,QACfmL,EAAOnL,EAAQmL,KACf6mB,EAAUjzB,KAAKizB,SACf7mB,GAAKvN,MAAQ,GAAKuN,EAAK4a,UACnB1jB,EAAO,GAAIqI,KACXnI,QACI3E,MAAOuN,EAAKvN,MACZ+M,MAAOQ,EAAKR,MACZS,SAAUD,EAAKC,YAGvB/I,EAAKwI,OAAOmnB,EAAQ5oB,GAAI4oB,EAAQ3oB,IAAIyB,OAAOknB,EAAQ1oB,GAAI0oB,EAAQzoB,IAC3DvJ,EAAQszB,aACRlxB,EAAiBC,GAEjB2pB,EAAQjtB,KAAKw0B,WAAa,GAAIxe,IAClCiX,EAAM/T,OAAO5V,GACbtD,KAAKsnB,OAAOpO,OAAO+T,GACnBjtB,KAAKg0B,YAAY/G,KAGzBwH,kBAAmB,WAAA,GACXxzB,GAAUjB,KAAKiB,QACfyzB,EAAW,CAQf,OAPIzzB,GAAQgxB,WAAWjL,SAAW/lB,EAAQ8wB,WAAW/K,QACjD0N,EAAWjtB,KAAKY,IAAIpH,EAAQgxB,WAAWlyB,KAAMkB,EAAQ8wB,WAAWhyB,MACzDkB,EAAQgxB,WAAWjL,QAC1B0N,EAAWzzB,EAAQgxB,WAAWlyB,KACvBkB,EAAQ8wB,WAAW/K,UAC1B0N,EAAWzzB,EAAQ8wB,WAAWhyB,MAE3B20B,GAEX5I,iBAAkB,WAAA,GACV7S,GAAMjZ,KACNiB,EAAUgY,EAAIhY,QACdO,EAAMyX,EAAIzX,IACVkpB,EAAazpB,EAAQypB,UACrBA,KACA1qB,KAAK20B,gBAAkBhpB,GAAKgf,SAASnpB,EAAIyhB,UACrCiG,MAAQtd,MAAO8e,GACflnB,OAAQ,OAEZxD,KAAKsnB,OAAOpO,OAAOlZ,KAAK20B,mBAGhCf,gBAAiB,WAAA,GAST3G,GACA2H,EAGK7xB,EACD8jB,EACAgO,EAAgBC,EASZC,EAOAzxB,EA9BRmc,EAASzf,KACTiB,EAAUjB,KAAKiB,QACf+zB,EAAY/zB,EAAQ+zB,cACpBnpB,EAAW5K,EAAQ4K,SACnBopB,EAAWj1B,KAAKi1B,QACpB,IAAyB,IAArBD,EAAUt2B,OAAd,CAOA,IAJIuuB,EAAQjtB,KAAKk1B,eAAiB,GAAIlf,KAAQyR,YAC1CmN,EAAUpuB,EAAKxG,KAAKm1B,KAAKC,KAAM,SAAUxW,GACzC,MAAOA,GAAK3d,QAAQ4K,WAAa4T,EAAOxe,QAAQ4K,WACjD,GACM9I,EAAM,EAAGA,EAAMiyB,EAAUt2B,OAAQqE,IAClC8jB,EAAOmO,EAAUjyB,GACjB8xB,EAAQ,OAAQC,EAAQ,OACxBjpB,GACAgpB,GAASD,GAAWK,EAAS9B,OAAOF,UACpC6B,EAAQrV,EAAO4V,QAAQxO,EAAKyO,KAAMzO,EAAK0O,IAAI,KAE3CV,EAAQpV,EAAO4V,QAAQxO,EAAKyO,KAAMzO,EAAK0O,IAAI,GAC3CT,GAASF,GAAWK,EAAS7B,OAAOH,WAElB,IAAlB4B,EAAMh2B,SAAoC,IAAnBi2B,EAAMh2B,WACzBi2B,EAAW,GAAI7e,KACf2e,EAAMxqB,GACNyqB,EAAMxqB,KAENuqB,EAAMh2B,QACNi2B,EAAMh2B,WAENwE,EAAOqI,GAAKgf,SAASoK,GACrB7L,MACItd,MAAOib,EAAKjb,MACZud,QAAStC,EAAKsC,SAElB3lB,OAAQ,OAEZypB,EAAM/T,OAAO5V,GAGrBtD,MAAKwnB,aAAayF,KAEtBuI,gBAAiB,SAAUZ,GAiBvB,QAAS/D,GAAOqD,EAAejoB,EAAUkoB,GAAzC,GAIiB31B,GACDypB,EAJR7V,EAAQ8hB,EAAcx1B,OACtBuS,EAAOxJ,KAAKY,IAAI,EAAG4D,EAASgF,KAChC,IAAIhF,EAAS+a,QACT,IAASxoB,EAAIyN,EAASwmB,KAAMj0B,EAAI4T,EAAO5T,GAAKyS,EACpCgX,EAAMpkB,GAAMqwB,EAAc11B,IACzBuI,EAAQkhB,EAAKgK,IACVzzB,EAAI21B,IAAa,GAAOsB,GAAmBC,IAAYzN,IACvD0N,EAAYlqB,SAAWwc,EACvB0G,EAAUzV,OAAOlN,EAAmB2pB,EAAa1pB,IACjDgmB,EAAW9zB,KAAK8pB,IA3BvB,GACThnB,GAAUjB,KAAKiB,QACf20B,EAAiB30B,EAAQ20B,eACzBC,EAAiB50B,EAAQ40B,eACzBphB,EAAYxT,EAAQwT,UACpB5I,EAAW5K,EAAQ4K,SACnB4pB,EAAkBb,EAAQ3zB,QAAQmL,KAAK4a,QACvCnT,EAAYgiB,EAAe7O,QAAU/lB,EAAQ4S,UAAY,EACzDof,EAAU2B,EAAQ3B,UAClByC,EAAUzC,EAAQpnB,EAAW,KAAO,MACpC8pB,GACAzpB,UAAW+mB,EAAQpnB,EAAW,KAAO,MACrCM,QAAS8mB,EAAQpnB,EAAW,KAAO,MACnCA,SAAUA,GAEVomB,KACAtD,EAAY3uB,KAAK6zB,iBAmBrB,OAFAhD,GAAO7wB,KAAKq0B,wBAAyBwB,GACrChF,EAAO7wB,KAAKs0B,wBAAyBsB,EAAgB/hB,EAAYY,GAC1Dka,EAAU5I,UAErBE,OAAQ,SAAUzkB,GAAV,GAaKhD,GACDs3B,EAbJ7c,EAAMjZ,KACNiB,EAAUgY,EAAIhY,QACd2wB,EAAS3Y,EAAI2Y,OACbnC,EAAQxW,EAAIwW,MACZ5jB,EAAW5K,EAAQ4K,SACnBuG,EAAQwf,EAAOlzB,OACfq3B,EAASlqB,EAAWoM,GAAQb,GAC5B4e,EAAYvG,EAAQA,EAAMjuB,IAAIu0B,KAAY,EAC1CE,EAAQj2B,KAAKy0B,oBAAsBxzB,EAAQ+oB,OAASgM,EACpDE,GAAWl2B,KAAKqmB,eAAiB7kB,KAAOA,EACxC20B,EAAUD,EAAQH,KAClBK,EAAe,CACnB,KAAS53B,EAAI,EAAGA,EAAI4T,EAAO5T,IACnBs3B,EAAYlE,EAAOpzB,GAAGgD,IAAIu0B,KAC1BD,EAAYG,GAASE,IACrBC,EAAe3uB,KAAKY,IAAI+tB,EAAcN,GAI1C91B,MAAKwB,IADLqK,EACW,GAAIpB,IAAIjJ,EAAI6I,GAAI7I,EAAI8I,GAAI9I,EAAI6I,GAAK+rB,EAAeH,EAAOz0B,EAAIgJ,IAE3D,GAAIC,IAAIjJ,EAAI6I,GAAI7I,EAAI8I,GAAI9I,EAAI+I,GAAI/I,EAAI8I,GAAK8rB,EAAeH,GAEvEj2B,KAAKq2B,eACLr2B,KAAKs2B,gBACLt2B,KAAKu2B,gBAETC,uBAAwB,WACpB,MAAOx2B,MAAKq0B,yBAEhBoC,eAAgB,SAAU1F,GACtB,MAAOA,GAAMlhB,OAEjBymB,cAAe,WAAA,GAWFvzB,GACDguB,EACA2F,EACAZ,EACAa,EACAC,EAAmBC,EAA4BC,EAKvCrT,EAGJsT,EAiBAC,EAxCRvX,EAASzf,KACTiZ,EAAMjZ,KACNiB,EAAUgY,EAAIhY,QACd2wB,EAAS3Y,EAAI2Y,OACbqF,EAAqBj3B,KAAKi3B,qBAC1BprB,EAAW5K,EAAQ4K,SACnBonB,EAAUjzB,KAAKizB,UACfC,EAASjyB,EAAQ2wB,OAAOsB,OACxBgB,EAAgBl0B,KAAKw2B,yBACrBU,EAAcl3B,KAAKy0B,oBAAsBxzB,EAAQ+oB,MACrD,KAASjnB,EAAM,EAAGA,EAAM6uB,EAAOlzB,OAAQqE,IAC/BguB,EAAQa,EAAO7uB,GACf2zB,EAASjX,EAAOgX,eAAe1F,GAC/B+E,EAAYjqB,EAAWklB,EAAMvvB,IAAI1C,SAAWiyB,EAAMvvB,IAAI3C,QACtD83B,EAAWzC,EAAcwC,GAAUZ,EAAY,EAC/Cc,EAAW,OAAQC,EAAoB,OAAQC,EAAmB,OAClEjrB,GACIorB,IACAJ,EAAoB3C,EAAcwC,GAClCI,EAAmB5C,EAAcwC,EAAS,GACtCjT,EAASoT,GAAqBC,EAAmBD,GAAqB,EAC1EF,EAAWlT,EAASqS,EAAY,GAEhCiB,EAAS9D,EAAQ1oB,GACjB2oB,GACA6D,GAAUG,EACVnG,EAAM9vB,QAAQivB,eAAiBjqB,KAE/B8wB,GAAUG,EAAcnG,EAAMvvB,IAAI3C,QAClCkyB,EAAM9vB,QAAQivB,eAAiBnqB,IAEnC6wB,EAAW7F,EAAMvvB,IAAIigB,KAAKsV,EAAQJ,KAE9BM,GACAJ,EAAoB3C,EAAcwC,GAClCI,EAAmB5C,EAAcwC,EAAS,KAE1CG,EAAoBF,EACpBG,EAAmBH,EAAWb,GAE9BkB,EAAS/D,EAAQ3oB,GACjB4oB,GACA8D,GAAUE,EAAcnG,EAAMvvB,IAAI1C,SAClCiyB,EAAM9vB,QAAQivB,eAAiBlqB,KAE/BgxB,GAAUE,EACVnG,EAAM9vB,QAAQivB,eAAiBpqB,IAEnC8wB,EAAW,GAAInsB,IAAIosB,EAAmBG,EAAQF,EAAkBE,EAASjG,EAAMvvB,IAAI1C,WAEvFiyB,EAAM9K,OAAO2Q,IAGrB/D,iBAAkB,WAAA,GAENqB,GACAtC,EACAxoB,EACKrG,EACDlE,EACA+3B,EAUKO,CAhBjB,IAAIn3B,KAAKiB,QAAQ4xB,mBAAqB7yB,KAAKiB,QAAQ4K,SAAU,CAIzD,IAHIqoB,EAAgBl0B,KAAKq0B,wBACrBzC,EAAS5xB,KAAK4xB,OAET7uB,EAAM,EAAGA,EAAM6uB,EAAOlzB,OAAQqE,IAGnC,GAFIlE,EAAQ4I,KAAKqB,IAAIorB,EAAcnxB,EAAM,GAAKmxB,EAAcnxB,IACxD6zB,EAAWhF,EAAO7uB,GAAKvB,IACvBo1B,EAAS/3B,QAAUA,EAAO,CAC1B,GAAI+3B,EAAS93B,SAAWD,EAAO,CAC3BuK,KACA,OAEJA,MAGR,GAAIA,EAAO,CACP,IAAS+tB,EAAQ,EAAGA,EAAQvF,EAAOlzB,OAAQy4B,IACvCvF,EAAOuF,GAAOl2B,QAAQ2hB,SAAWxZ,EACjCwoB,EAAOuF,GAAOlR,OAAO,GAAIxb,IAE7B,QAAO,KAInB4rB,aAAc,WAAA,GACNpd,GAAMjZ,KACNiB,EAAUgY,EAAIhY,QACdwuB,EAAQxW,EAAIwW,MACZyD,EAASjyB,EAAQ2wB,OAAOsB,OACxBrnB,EAAW5K,EAAQ4K,QACnB4jB,KACI5jB,GACA4jB,EAAMxuB,QAAQkiB,MAAQ+P,EAASntB,GAAQE,GACvCwpB,EAAMxuB,QAAQqpB,OAASmF,EAAMxuB,QAAQwK,WAErCgkB,EAAMxuB,QAAQkiB,MAAQsM,EAAMxuB,QAAQwK,SACpCgkB,EAAMxuB,QAAQqpB,OAAS4I,EAASptB,GAAME,IAE1CypB,EAAMxJ,OAAOjmB,KAAKwB,OAG1B+0B,aAAc,WAAA,GAEDxzB,GACD8jB,EACAxmB,EACA+2B,EAJJ3X,EAASzf,IACb,KAAS+C,EAAM,EAAGA,EAAM/C,KAAKwzB,MAAM90B,OAAQqE,IACnC8jB,EAAOpH,EAAO+T,MAAMzwB,GACpB1C,EAAQwmB,EAAK5lB,QAAQZ,MACrB+2B,EAAO,OACP1zB,GAAQrD,IACJof,EAAO4X,iBAAiBh3B,GACxBwmB,EAAKiC,OAELjC,EAAKiK,OAETsG,EAAO3X,EAAO6X,SAASj3B,IAEvBwmB,EAAKiK,OAETjK,EAAKZ,OAAOmR,GAAQ3X,EAAOwT,YAGnCqE,SAAU,SAAUj3B,GAChB,MAAOL,MAAKq1B,QAAQh1B,IAExB0hB,QAAS,SAAUwV,GAAV,GACDtE,GAAUsE,EAAWtE,UACrBpnB,EAAW7L,KAAKiB,QAAQ4K,SACxBoc,EAAMpc,EAAWsM,GAAID,EACzBlY,MAAKwB,IAAIsgB,OAAOmR,EAAShL,GACrBpc,EACA7L,KAAKwB,IAAIygB,OAAO,EAAGjiB,KAAKizB,UAAUn0B,SAAWm0B,EAAQn0B,UAErDkB,KAAKwB,IAAIygB,OAAOjiB,KAAKizB,UAAUp0B,QAAUo0B,EAAQp0B,QAAS,GAE9DmB,KAAKwB,IAAIymB,EAAM,IAAMjoB,KAAKizB,UAAUhL,EAAM,GAAKgL,EAAQhL,EAAM,GAC7DjoB,KAAKwB,IAAIymB,EAAM,IAAMjoB,KAAKizB,UAAUhL,EAAM,GAAKgL,EAAQhL,EAAM,IAEjEuP,cAAe,SAAUn3B,EAAOipB,EAAUroB,GAA3B,GACPw2B,GAAOvxB,EAAYjF,GACnBvD,EAAO2C,CAWX,OAVIo3B,GACA/5B,EAAO+5B,GACHp3B,MAAOA,EACPipB,SAAUA,EACV3M,OAAQ1b,EAAQ0b,OAChBgT,QAAS1uB,EAAQ0uB,UAEd1uB,EAAQ0b,SACfjf,EAAOsC,KAAK0mB,aAAa/J,OAAOiB,WAAW3c,EAAQ0b,QAAStc,GAAQY,EAAQ0uB,UAEzEjyB,GAEX05B,KAAM,SAAU9B,EAAMC,EAAImC,GACtB,GAAIN,GAAOp3B,KAAKq1B,QAAQC,EAAMC,EAAImC,EAClC,IAAIN,EACA,MAAOA,GAAKnU,UAGpBuH,WAAY,WAAA,GAIA5L,GAEIrb,EAOAo0B,EAZRn2B,EAAMxB,KAAKwB,IAAIkf,QACfkR,EAAS5xB,KAAK4xB,MAiBlB,OAhBIA,GAAOlzB,SACHkgB,EAAO5e,KAAKiB,QAAQ4K,SAAWsM,GAAID,GACnClY,KAAK0mB,aAAa/H,WAAWC,IACzBrb,EAASvD,KAAK43B,iBAClBp2B,EAAIod,EAAO,IAAMrb,EAAO0D,MACxBzF,EAAIod,EAAO,IAAMrb,EAAO2D,MAEpB0qB,EAAO,GAAG3wB,QAAQ+lB,SAClBxlB,EAAIkgB,KAAKkQ,EAAO,GAAGpwB,KAEnBm2B,EAAY/F,EAAOA,EAAOlzB,OAAS,GACnCi5B,EAAU12B,QAAQ+lB,SAClBxlB,EAAIkgB,KAAKiW,EAAUn2B,OAIxBA,GAEXo2B,eAAgB,WAAA,GAaH70B,GACDguB,EACA2F,EACAmB,EAAoBC,EAfxBrY,EAASzf,KACTiZ,EAAMjZ,KAAKiB,QACX4K,EAAWoN,EAAIpN,SACf8nB,EAAU1a,EAAI0a,QACdsD,EAAqBj3B,KAAKi3B,qBAC1B/C,EAAgBl0B,KAAKw2B,yBACrBuB,EAAclsB,EAAWsM,GAAID,GAC7B0Z,EAAS5xB,KAAK4xB,OACdoG,EAAgBrE,EAAU,EAAI,EAC9BsE,EAActE,EAAU,EAAI,EAC5BuE,EAAiB,EACjBC,EAAe,CACnB,KAASp1B,EAAM,EAAGA,EAAM6uB,EAAOlzB,OAAQqE,IAC/BguB,EAAQa,EAAO7uB,GACf2zB,EAASjX,EAAOgX,eAAe1F,GAC/B8G,EAAY,OAAQC,EAAU,OAC9Bb,GACAY,EAAY3D,EAAcwC,EAASsB,GACnCF,EAAU5D,EAAcwC,EAASuB,IAEjCJ,EAAYC,EAAU5D,EAAcwC,GAExCwB,EAAiBzwB,KAAKY,IAAI6vB,EAAgBL,EAAY9G,EAAMvvB,IAAIu2B,EAAc,IAC9EI,EAAe1wB,KAAKY,IAAI8vB,EAAcpH,EAAMvvB,IAAIu2B,EAAc,GAAKD,EAEvE,QACI7wB,MAAOixB,EACPhxB,IAAKixB,IAGbC,WAAY,SAAU9C,EAAMC,EAAIptB,EAAKE,EAAK9E,GAA9B,GAWJ80B,GACArlB,EACAI,EAZAnS,EAAUjB,KAAKiB,OACnB,OAAIq0B,GAAOntB,GAAO5E,EAAS,KAAOG,GAAQzC,EAAQkH,MAAQlH,EAAQkH,KAAOA,IAAQE,EAAMktB,GAAMhyB,EAAS,KAAOG,GAAQzC,EAAQoH,MAAQA,GAAOpH,EAAQoH,KACzI,KAEPktB,EAAKptB,GAAO5E,EAAS,GAAK8E,EAAMitB,GAAQ/xB,EAAS,GAE7C4E,IAAKmtB,EACLjtB,IAAKktB,IAGT8C,EAAY9C,EAAKD,EACjBtiB,EAAWsiB,EACXliB,EAAWmiB,EACXD,EAAOntB,GAAO5E,EAAS,GACvByP,EAAWyG,GAAW6b,EAAMntB,EAAKE,GACjC+K,EAAWqG,GAAW6b,EAAO+C,EAAWlwB,EAAMkwB,EAAWhwB,IAClDktB,EAAKltB,GAAO9E,EAAS,IAC5B6P,EAAWqG,GAAW8b,EAAIptB,EAAKE,GAC/B2K,EAAWyG,GAAW8b,EAAK8C,EAAWlwB,EAAKE,EAAMgwB,KAGjDlwB,IAAK6K,EACL3K,IAAK+K,KAGbklB,WAAY,WACR,OACInwB,IAAKnI,KAAKwT,UACVnL,IAAKrI,KAAKyT,YAGlBwjB,mBAAoB,WAChB,OAAQj3B,KAAKiB,QAAQs3B,WAEzBC,mBAAoB,eAGxB3wB,EAAkBqT,IACd0W,QACI5K,SAAS,EACTpE,SAAU,EACVsQ,QAAQ,EACRjiB,KAAM,EACNwhB,KAAM,GAEVrmB,MACIvN,MAAO,EACP+M,MAAO8K,GACPsQ,SAAS,GAEbyI,OACIzI,SAAS,EACTvb,SAAUkL,IAEdsb,YACI9O,MAAOvL,GACP7X,KAAM,EACN0yB,KAAM,EACNxhB,KAAM,GAEV8gB,YACI5O,MAAOvL,GACP7X,KAAM,EACN0yB,KAAM,EACNxhB,KAAM,GAEVsE,kBAAmB,EACnB2c,cAAeta,GACfoa,cAAeza,GACfse,gBACIpD,KAAM,EACNxhB,KAAM,GAEV2kB,gBACI5O,SAAS,EACTnoB,MAAO,EACP+M,MAAO8K,GACP+b,KAAM,EACNxhB,KAAM,GAEV+Y,OAAQ,EACRhD,SAAS,EACT2M,SAAS,EACT4E,WAAW,EACX/E,OAASzC,OAASrzB,KAAM,KACxB62B,aAAa,EACbnC,cAAc,IAEdrjB,GAAe,eACfF,GAAU,UACVH,GAAU,UACVD,GAAQ,QACRF,GAAO,OACPD,GAAQ,QACRF,GAAS,SACTF,GAAQ,QACRiN,GAAuB,EACvBrM,GAAkB,IAClBpC,GAAkB,GAAKoC,GACvBhB,GAAgB,GAAKpB,GACrBW,GAAe,GAAKS,GACpB0E,GAAgB,EAAInF,GACpBkF,GAAiB,GAAKlF,GACtBiF,GAAgB,IAAMjF,GACtBqC,IACA+oB,MAASnmB,GACTomB,OAAUnmB,GACVomB,MAASnmB,GACTomB,KAAQvrB,GACRE,MAASO,GACT+qB,QAAWnsB,GACXosB,QAAWhqB,GACXiqB,aAAgB5d,IAoMhBC,GAA4B,IAa5BC,GAAeH,GAAKrb,QACpBsyB,WAAY,WACRnyB,KAAKg5B,WAETC,eAAgB,WACZ,MAAO,IAEXvY,MAAO,WACH,GAAIwY,GAAO,GAAI7d,IAAa7d,EAAEqC,UAAWG,KAAKiB,SAAUjB,KAAK0mB,aAE7D,OADAwS,GAAK7G,eACE6G,GAEXlT,gBAAiB,SAAU/kB,GAAV,GAMLkH,GACAE,EANJqI,EAAazP,EAAQyP,eACrByoB,EAAaz1B,GAAQzC,EAAQkH,KAC7BixB,EAAa11B,GAAQzC,EAAQoH,IAYjC,OAXApH,GAAQo4B,cAAgBp4B,EAAQyP,WAAaA,GACxCyoB,GAAcC,IAAe1oB,EAAWhS,SACrCyJ,EAAMgxB,EAAa1xB,KAAKE,MAAM1G,EAAQkH,KAAO,EAG7CE,EADA+wB,EACMn4B,EAAQs3B,UAAY9wB,KAAKE,MAAM1G,EAAQoH,KAAO,EAAIZ,KAAKC,KAAKzG,EAAQoH,KAEpEqI,EAAWhS,OAErBuC,EAAQyP,WAAazP,EAAQyP,WAAWmB,MAAM1J,EAAKE,IAEhDpH,GAEXq4B,aAAc,WAAA,GAINjxB,GAHApH,EAAUjB,KAAKiB,QACfvC,EAASuC,EAAQyP,WAAWhS,QAAU,EACtCyJ,EAAM/D,EAASnD,EAAQkH,KAAOlH,EAAQkH,IAAM,EAAI,CAOpD,OAJIE,GADAjE,EAASnD,EAAQoH,MAAQpH,EAAQoH,IAAM,IAAM,GAAKpH,EAAQoH,IAAMrI,KAAKu5B,aAAalxB,IAC5E3J,GAAU,EAAIuC,EAAQoH,IAAM,GAE5B3J,GAAUuC,EAAQs3B,UAAY,EAAI,IAGxCpwB,IAAKA,EACLE,IAAKA,IAGbmxB,kBAAmB,SAAU9B,GAAV,GAGXrvB,GASIkxB,EAXJt4B,EAAUjB,KAAKiB,QACfkH,EAAM/D,EAASnD,EAAQkH,KAAOlH,EAAQkH,IAAM,CAchD,OAXIE,GADAjE,EAASnD,EAAQoH,KACXpH,EAAQoH,IACPjE,EAASnD,EAAQkH,KAClBA,EAAMlH,EAAQyP,WAAWhS,OAEzBsB,KAAKu5B,aAAalxB,KAAO,EAE/BqvB,IACI6B,EAAav5B,KAAKu5B,aACtBpxB,EAAMsR,GAAWtR,EAAK,EAAGoxB,EAAWlxB,KACpCA,EAAMoR,GAAWpR,EAAK,EAAGkxB,EAAWlxB,OAGpCF,IAAKA,EACLE,IAAKA,IAGbsI,MAAO,WAAA,GACC1P,GAAUjB,KAAKiB,QACfkH,EAAM/D,EAASnD,EAAQkH,KAAOlH,EAAQkH,IAAM,EAC5CE,EAAMjE,EAASnD,EAAQoH,KAAOpH,EAAQoH,IAAMrI,KAAKu5B,aAAalxB,GAClE,QACIF,IAAKA,EACLE,IAAKA,IAGboxB,aAAc,WACV,MAAOz5B,MAAK2Q,SAEhB4oB,WAAY,WACR,GAAIt4B,GAAUjB,KAAKiB,OACnB,QACIkH,IAAK,EACLE,IAAKZ,KAAKY,IAAIrI,KAAK05B,YAAc,EAAGz4B,EAAQo4B,cAAc36B,SAAWuC,EAAQs3B,UAAY,EAAI,KAGrGoB,aAAc,WAAA,GACN1gB,GAAMjZ,KAAKs5B,eACXnxB,EAAM8Q,EAAI9Q,IACVE,EAAM4Q,EAAI5Q,IACV4qB,EAAUjzB,KAAKizB,UACflzB,EAAOC,KAAKiB,QAAQ4K,SAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QAC1D4J,EAAQ1I,GAAQsI,EAAMF,GAAO,EACjC,QACIM,MAAOA,GAASzI,KAAKiB,QAAQ0yB,WAAe,GAC5CnyB,IAAKyxB,EACL9qB,IAAKA,EACLE,IAAKA,IAGbiuB,cAAe,WACXpb,GAAK+C,GAAGqY,cAAcjR,KAAKrlB,MAC3BA,KAAK45B,wBAETA,qBAAsB,WAAA,GAKVC,GACA5yB,EACAC,EACA4yB,EACAnC,EARJ1e,EAAMjZ,KACNwB,EAAMyX,EAAIzX,IACVowB,EAAS3Y,EAAI2Y,MACbA,GAAOlzB,SACHm7B,EAAY75B,KAAKiB,QAAQ4K,SAAWsM,GAAID,GACxCjR,EAAQzF,EAAIq4B,EAAY,GACxB3yB,EAAM1F,EAAIq4B,EAAY,GACtBC,EAAalI,EAAO,GACpB+F,EAAY5lB,GAAK6f,IACjBkI,EAAWt4B,IAAIq4B,EAAY,GAAK3yB,GAAO4yB,EAAWt4B,IAAIq4B,EAAY,GAAK5yB,KACvE6yB,EAAW74B,QAAQ+lB,SAAU,IAE7B2Q,EAAUn2B,IAAIq4B,EAAY,GAAK3yB,GAAOywB,EAAUn2B,IAAIq4B,EAAY,GAAK5yB,KACrE0wB,EAAU12B,QAAQ+lB,SAAU,KAIxCqN,sBAAuB,WACnB,MAAOr0B,MAAK+5B,WAAW9H,YAE3BqC,sBAAuB,WACnB,MAAOt0B,MAAK+5B,WAAWhI,YAE3ByE,uBAAwB,WACpB,MAAOx2B,MAAK+5B,WAAWC,YAE3BC,YAAa,SAAUC,GAOnB,IAPS,GACLjhB,GAAMjZ,KAAKs5B,eACXnxB,EAAM8Q,EAAI9Q,IACVE,EAAM4Q,EAAI5Q,IACVqvB,EAAQjwB,KAAKC,KAAKW,GAClBjJ,EAAUqI,KAAKE,MAAMQ,GACrBgyB,KACG/6B,GAAWs4B,GACdyC,EAAQh8B,KAAKiB,GACbA,GAAW86B,CAEf,OAAOC,IAEXC,iBAAkB,SAAUF,GAAV,GAWLn3B,GAVLkW,EAAMjZ,KAAKiB,QACX4K,EAAWoN,EAAIpN,SACf8nB,EAAU1a,EAAI0a,QACdnG,EAAQxtB,KAAK25B,eACblxB,EAAQ+kB,EAAM/kB,MACdjH,EAAMgsB,EAAMhsB,IACZ2G,EAAMqlB,EAAMrlB,IACZ8f,EAAMzmB,GAAKqK,EAAWsM,GAAID,KAAMyb,EAAU,EAAI,IAC9CwG,EAAUn6B,KAAKi6B,YAAYC,GAC3BG,IACJ,KAASt3B,EAAM,EAAGA,EAAMo3B,EAAQz7B,OAAQqE,IACpCs3B,EAAUl8B,KAAK8pB,EAAMpkB,GAAM4E,GAAS0xB,EAAQp3B,GAAOoF,GAAMf,IAE7D,OAAOizB,IAEXN,SAAU,WAAA,GAOEO,GANJr5B,EAAUjB,KAAKiB,QACfs5B,EAAQv6B,KAAKg5B,OACbroB,EAAQ3Q,KAAKs5B,eACbrG,EAAUjzB,KAAKizB,UACfx0B,EAAOw0B,EAAQvQ,UAAY/R,EAAMxI,IAAM,IAAMwI,EAAMtI,IAAMpH,EAAQ0yB,QAAU1yB,EAAQs3B,SAQvF,OAPIgC,GAAMC,QAAU/7B,IACZ67B,EAAWr5B,EAAQ8wB,WAAW/K,SAAW/lB,EAAQ20B,eAAe5O,QACpEuT,EAAMC,MAAQ/7B,EACd87B,EAAMP,WAAah6B,KAAKo6B,iBAAiB,GACzCG,EAAMtI,WAAajyB,KAAKy6B,0BAA0BF,EAAMP,WAAY/G,GACpEsH,EAAMxI,WAAauI,EAAWt6B,KAAKy6B,0BAA0Bz6B,KAAKo6B,iBAAiB,IAAMnH,OAEtFsH,GAEXE,0BAA2B,SAAUJ,EAAWpH,GAArB,GAInBrU,GACA8b,EAGAxzB,EACAyzB,EAIAC,CAZJ,KAAKP,EAAU37B,OACX,MAAO27B,EAQX,KANIzb,EAAO5e,KAAKiB,QAAQ4K,SAAWsM,GAAID,GACnCwiB,EAAU,SAAUjvB,GACpB,MAAOwnB,GAAQrU,EAAO,IAAMnT,GAAYA,GAAYwnB,EAAQrU,EAAO,IAEnE1X,EAAMmzB,EAAU37B,OAAS,EACzBi8B,EAAa,GACTD,EAAQL,EAAUM,KAAgBA,GAAczzB,GACpDyzB,GAGJ,KADIC,EAAW1zB,GACPwzB,EAAQL,EAAUO,KAAcA,GAAY,GAChDA,GAEJ,OAAOP,GAAUxoB,MAAM8oB,EAAYC,EAAW,IAElDvF,QAAS,SAAUC,EAAMC,EAAImC,GAApB,GAiBDmD,GACAC,EAjBA75B,EAAUjB,KAAKiB,QACf0yB,EAAU1yB,EAAQ0yB,QAClB4E,EAAYt3B,EAAQs3B,UACpB1sB,EAAW5K,EAAQ4K,SACnBoN,EAAMjZ,KAAK25B,eACXlxB,EAAQwQ,EAAIxQ,MACZjH,EAAMyX,EAAIzX,IACV2G,EAAM8Q,EAAI9Q,IACV0xB,EAAYhuB,EAAWsM,GAAID,GAC3BhM,EAAY1K,EAAIq4B,GAAalG,EAAU,EAAI,IAC3CoH,EAAUv5B,EAAIkf,QACdsa,GAAct3B,GAAQ6xB,GACtBtuB,EAAQ0S,GAAe2b,EAAM,GAC7BpuB,EAAMyS,GAAe4b,EAAItuB,EAc7B,OAbAC,GAAMO,KAAKY,IAAInB,EAAM,EAAGD,GACxBC,EAAMO,KAAKY,IAAIpB,EAAOC,GAClB2zB,EAAK3uB,GAAajF,EAAQkB,GAAOM,EACjCqyB,EAAK5uB,GAAahF,EAAM,EAAIiB,GAAOM,EACnCuyB,GAAczC,IACduC,EAAKD,GAELnD,IACAmD,EAAKphB,GAAWohB,EAAIr5B,EAAIq4B,EAAY,GAAIr4B,EAAIq4B,EAAY,IACxDiB,EAAKrhB,GAAWqhB,EAAIt5B,EAAIq4B,EAAY,GAAIr4B,EAAIq4B,EAAY,KAE5DkB,EAAQlB,EAAY,GAAKlG,EAAUmH,EAAKD,EACxCE,EAAQlB,EAAY,GAAKlG,EAAUkH,EAAKC,EACjCC,GAEXE,UAAW,SAAU7D,GAAV,GACHvrB,GAAW7L,KAAKiB,QAAQ4K,SACxBguB,EAAYhuB,EAAWsM,GAAID,GAC3B+a,EAAUjzB,KAAKizB,UACfiI,EAAe9D,EAAK1W,OAGxB,OAFAwa,GAAarB,EAAY,GAAKpgB,GAAW2d,EAAKyC,EAAY,GAAI5G,EAAQ4G,EAAY,GAAI5G,EAAQ4G,EAAY,IAC1GqB,EAAarB,EAAY,GAAKpgB,GAAW2d,EAAKyC,EAAY,GAAI5G,EAAQ4G,EAAY,GAAI5G,EAAQ4G,EAAY,IACnGqB,GAEX9D,KAAM,SAAU9B,EAAMC,EAAImC,GAApB,GACEvvB,GAAMV,KAAKE,MAAM3H,KAAKiB,QAAQkH,KAAO,GACrClB,EAAQquB,EACRpuB,EAAMquB,CAWV,OAVqB,gBAAVtuB,GACPA,EAAQjH,KAAKm7B,cAAcl0B,GACpB7C,EAAS6C,KAChBA,GAASkB,GAEM,gBAARjB,GACPA,EAAMlH,KAAKm7B,cAAcj0B,GAClB9C,EAAS8C,KAChBA,GAAOiB,GAEJ+S,GAAK+C,GAAGmZ,KAAK/R,KAAKrlB,KAAMiH,EAAOC,EAAKwwB,IAE/C0D,mBAAoB,SAAUxa,GAAV,GAkBZvgB,GACAuI,EAlBAqQ,EAAMjZ,KAAKiB,QACX0yB,EAAU1a,EAAI0a,QACd4E,EAAYtf,EAAIsf,UAChB1sB,EAAWoN,EAAIpN,SACfguB,EAAYhuB,EAAWsM,GAAID,GAC3BsV,EAAQxtB,KAAK25B,eACblxB,EAAQ+kB,EAAM/kB,MACdjH,EAAMgsB,EAAMhsB,IACZ2G,EAAMqlB,EAAMrlB,IACZE,EAAMmlB,EAAMnlB,IACZgzB,EAAa1H,EAAUtrB,EAAMF,EAC7B+D,EAAY1K,EAAIq4B,EAAY,GAC5B1tB,EAAU3K,EAAIq4B,EAAY,GAC1B5R,EAAMrH,EAAMiZ,EAChB,OAAI5R,GAAM/b,GAAa+b,EAAM9b,EAClB,MAEP9L,EAAQg7B,GAAcpT,EAAM/b,GAAazD,EACzCG,EAAOvI,EAAQ,EACfk4B,EACAl4B,EAAQoH,KAAK5D,MAAMxD,GACH,IAATuI,GAAcvI,EAAQ,GAC7BA,IAEGoH,KAAKE,MAAMtH,KAEtBi7B,YAAa,SAAU1a,GACnB,GAAI/Q,GAAQ7P,KAAKo7B,mBAAmBxa,EACpC,OAAc,QAAV/Q,EACO,KAEJ7P,KAAKiB,QAAQyP,WAAWb,IAEnCsrB,cAAe,SAAU96B,GACrB,MAAOL,MAAKu7B,WAAWl7B,GAASoH,KAAKE,MAAM3H,KAAKiB,QAAQkH,KAAO,IAEnEqzB,WAAY,SAAU3rB,EAAO4Z,GACzB,GAAIxoB,GAAUjB,KAAKiB,OACnB,QAAQwoB,EAAQxoB,EAAQo4B,cAAgBp4B,EAAQyP,YAAYb,IAEhE4rB,gBAAiB,WACb,OAAQz7B,KAAKiB,QAAQyP,gBAAkBhS,QAE3Cg9B,eAAgB,SAAUn0B,GAAV,GACRtG,GAAUjB,KAAKiB,QACfgyB,EAAUjzB,KAAKizB,UACflzB,EAAOkB,EAAQ4K,SAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACrD8R,EAAQ1P,EAAQyP,WAAWhS,OAC3B+J,EAAQ1I,EAAO4Q,EACfpN,EAASM,GAAM0D,EAAQkB,EAAOI,GAClC,QACIV,IAAK5E,EACL8E,IAAKsI,EAAQpN,IAGrBo4B,UAAW,SAAUC,GAAV,GACHtC,GAAet5B,KAAKw5B,oBACpBvgB,EAAMjZ,KAAKu5B,aACXsC,EAAW5iB,EAAI9Q,IACf2zB,EAAW7iB,EAAI5Q,IACfF,EAAMsR,GAAW6f,EAAanxB,IAAMyzB,EAAMC,EAAUC,GACpDzzB,EAAMoR,GAAW6f,EAAajxB,IAAMuzB,EAAMC,EAAUC,EACxD,IAAIzzB,EAAMF,EAAM,EACZ,OACIA,IAAKA,EACLE,IAAKA,IAIjB0zB,WAAY,SAAUtzB,GAAV,GACJkI,GAAQ3Q,KAAKiB,QAAQyP,WAAWhS,OAChC6I,EAAQkB,EAAQkI,CACpB,QACIxI,KAAMZ,EACNc,IAAKsI,EAAQpJ,IAGrBmrB,YAAa,WACT,GAAIF,GAAcxyB,KAAKwyB,aACvB,OAAOA,GAAYnqB,IAAMmqB,EAAYrqB,KAEzCqqB,YAAa,WAAA,GAeLC,GAdAxxB,EAAUjB,KAAKiB,QACfs3B,EAAYt3B,EAAQs3B,UACpB5F,EAAe1xB,EAAQ2wB,OACvB3Y,EAAMjZ,KAAKw5B,mBAAkB,GAC7BrxB,EAAM8Q,EAAI9Q,IACVE,EAAM4Q,EAAI5Q,IACVpB,EAAQQ,KAAKE,MAAMQ,EAcvB,OAbKowB,IAIDpwB,EAAMV,KAAKC,KAAKS,GAChBE,EAAMZ,KAAKE,MAAMU,KAJjBF,EAAMV,KAAKE,MAAMQ,GACjBE,EAAMZ,KAAKC,KAAKW,IAOhBoqB,EADAtqB,EAAMwqB,EAAaF,KACZE,EAAaF,KAAOE,EAAa1hB,KAAOxJ,KAAKC,MAAMS,EAAMwqB,EAAaF,MAAQE,EAAa1hB,MAE3F0hB,EAAaF,MAGpBtqB,IAAKsqB,EAAOxrB,EACZoB,KAAMpH,EAAQyP,WAAWhS,OAAS2J,GAAOkwB,EAAY,EAAI,GAAK,GAAKtxB,IAG3E6rB,gBAAiB,SAAUjjB,EAAO8iB,GAAjB,GACT1xB,GAAUjB,KAAKiB,QACfqoB,EAAWroB,EAAQ+6B,UAAY/6B,EAAQ+6B,UAAUnsB,GAAS,KAC1DsC,EAAWwH,GAAe1Y,EAAQyP,WAAWb,GAAQ,IACrDnS,EAAOsC,KAAKw3B,cAAcrlB,EAAUmX,EAAUqJ,EAClD,OAAO,IAAI7X,IAAU3I,EAAUzU,EAAMmS,EAAOyZ,EAAUqJ,IAE1D0E,iBAAkB,SAAUh3B,GACxB,GAAIsQ,GAAQ3Q,KAAKw5B,mBACjB,OAAO/xB,MAAKE,MAAMgJ,EAAMxI,MAAQ9H,GAASA,GAASoH,KAAKC,KAAKiJ,EAAMtI,MAEtEivB,SAAU,SAAUj3B,GAAV,GACFY,GAAUjB,KAAKiB,QACf4O,EAAQxP,EAAQoH,KAAKE,MAAM1G,EAAQkH,KAAO,EAC9C,OAAOnI,MAAKq1B,QAAQxlB,IAExB0mB,aAAc,WACVrb,GAAK+C,GAAGsY,aAAalR,KAAKrlB,MAC1BA,KAAKi8B,uBAETA,oBAAqB,WAAA,GAKTpC,GACA5yB,EACAC,EACKnE,EACDwwB,EARRta,EAAMjZ,KACNwzB,EAAQva,EAAIua,MACZhyB,EAAMyX,EAAIzX,GACd,IAAIgyB,GAASA,EAAM90B,OAIf,IAHIm7B,EAAY75B,KAAKiB,QAAQ4K,SAAWsM,GAAID,GACxCjR,EAAQzF,EAAIq4B,EAAY,GACxB3yB,EAAM1F,EAAIq4B,EAAY,GACjB92B,EAAM,EAAGA,EAAMywB,EAAM90B,OAAQqE,IAC9BwwB,EAAOC,EAAMzwB,GACbwwB,EAAK/xB,MAAQ0F,EAAMqsB,EAAK/xB,IAAIq4B,EAAY,IAAMtG,EAAK/xB,IAAIq4B,EAAY,GAAK5yB,IACxEssB,EAAKzC,QAKrBoL,IAAK,SAAU30B,GAAV,GACGoJ,GAAQ3Q,KAAKw5B,mBAAkB,GAC/BvgB,EAAMjZ,KAAK25B,eACXlxB,EAAQwQ,EAAIxQ,MACZlF,EAASM,GAAM0D,EAAQkB,EAAOI,IAC9B0wB,EAAav5B,KAAKu5B,aAClBpxB,EAAMwI,EAAMxI,IAAM5E,EAClB8E,EAAMsI,EAAMtI,IAAM9E,CACtB,OAAOvD,MAAKo4B,WAAWjwB,EAAKE,EAAK,EAAGkxB,EAAWlxB,IAAK9E,IAExD44B,YAAa,SAAUl1B,EAAOC,GAAjB,GACL+R,GAAMjZ,KAAKiB,QACX0yB,EAAU1a,EAAI0a,QACd9nB,EAAWoN,EAAIpN,SACfguB,EAAYhuB,EAAWsM,GAAID,GAC3BvH,EAAQ3Q,KAAKw5B,mBAAkB,GAC/BhM,EAAQxtB,KAAK25B,eACblxB,EAAQ+kB,EAAM/kB,MACdjH,EAAMgsB,EAAMhsB,IACZ0K,EAAY1K,EAAIq4B,GAAalG,EAAU,EAAI,IAC3CyI,EAAYn1B,EAAM4yB,GAAa3tB,EAC/BmwB,EAAUn1B,EAAI2yB,GAAa3tB,EAC3B/D,EAAMwI,EAAMxI,IAAMi0B,EAAY3zB,EAC9BJ,EAAMsI,EAAMxI,IAAMk0B,EAAU5zB,EAC5B6zB,EAAW70B,KAAKU,IAAIA,EAAKE,GACzBk0B,EAAW90B,KAAKY,IAAIF,EAAKE,EAC7B,IAAIk0B,EAAWD,GAAYlhB,GACvB,OACIjT,IAAKm0B,EACLj0B,IAAKk0B,IAIjBjE,WAAY,WACR,MAAOt4B,MAAK2Q,SAEhB4qB,WAAY,SAAUl7B,GAAV,GACJY,GAAUjB,KAAKiB,QACf4O,EAAQ7P,KAAKw8B,eAAiBx8B,KAAKw8B,eAAe57B,IAAIP,GAAS6C,EAAQ7C,EAAOY,EAAQo4B,cAC1F,OAAOxpB,IAEX4sB,oBAAqB,WAAA,GAMbp0B,GALApH,EAAUjB,KAAKiB,QACfkH,EAAM,CAUV,OATI/D,GAASnD,EAAQkH,OACjBA,EAAMV,KAAKE,MAAM1G,EAAQkH,MAIzBE,EADAjE,EAASnD,EAAQoH,KACXpH,EAAQs3B,UAAY9wB,KAAKE,MAAM1G,EAAQoH,KAAOZ,KAAKC,KAAKzG,EAAQoH,KAAO,EAEvErI,KAAK08B,aAAe,GAG1Bv0B,IAAKA,EACLE,IAAKA,IAGbs0B,cAAe,WAAA,GAEHC,GACAvD,EACKt2B,CAHb,KAAK/C,KAAKw8B,eAGN,IAFII,EAAS58B,KAAKw8B,eAAiB,GAAI1jB,IACnCugB,EAAgBr5B,KAAKiB,QAAQo4B,cACxBt2B,EAAM,EAAGA,EAAMs2B,EAAc36B,OAAQqE,IAC1C65B,EAAO1e,IAAImb,EAAct2B,GAAMA,IAI3C25B,WAAY,WACR,MAAOj1B,MAAKY,IAAIrI,KAAKiB,QAAQo4B,cAAc36B,OAAQsB,KAAK05B,YAAc,MAG9E7xB,EAAkBwT,IACdvT,KAAM,WACN+D,UAAU,EACVgqB,gBACI7O,SAAS,EACTnoB,MAAO,EACP+M,MAAO8K,IAEXkb,QAAUnK,OAAQ,GAClB8Q,WAAW,EACXnG,cAAc,IAEd9e,GAAmB,IACnBgI,IACAyd,aAAc,eACdD,QAAS,WACTD,QAAS,QACTtrB,MAAO,QACPqrB,KAAM,MACND,MAAO,MACPD,OAAQ,UACRD,MAAO,QAEPxlB,GAAiB,GACjBL,GAAO,OACPlB,IACA3C,GACAF,GACAH,GACAD,GACAF,GACAD,GACAF,GACAF,IAEAsD,GAAM,MAUN+J,GAAiB3b,GAAMC,QACvBC,KAAM,SAAUmB,GACZjB,KAAKiB,QAAUA,GAEnB47B,eAAgB,WACZ,OACI10B,IAAK,EACLE,IAAK,IAGby0B,aAAc,WACV,UAEJrT,MAAO,WACH,UAEJ6O,WAAY,WACR,UAEJyE,WAAY,WACR,UAEJzf,OAAQ,WACJ,UAEJie,WAAY,WACR,UAEJyB,YAAa,WACT,MAAO,IAEXN,WAAY,WACR,MAAO,IAEXO,OAAQ,WACJ,MAAO,SAGXzhB,GAAY5b,GAAMC,QAClBC,KAAM,SAAUmH,EAAOC,EAAKjG,GAAtB,GAGEi8B,GACA3E,EAEA4E,EACAC,EAEAj1B,EAGAE,CAXJrI,MAAKiB,QAAUA,EACfA,EAAQ2O,aAAe3O,EAAQ2O,cAAgB,EAC3CstB,EAAkBj8B,EAAQi8B,gBAC1B3E,EAAYt3B,EAAQs3B,UACxBv4B,KAAKiH,MAAQ8G,EAAY9G,EAAO,EAAGhG,EAAQwO,SAAUxO,EAAQgM,cACzDkwB,EAAWn9B,KAAKq9B,iBAAiBn2B,GACjCk2B,GAAa7E,GAAahpB,EAAWrI,EAAKi2B,KAAcl8B,EAAQq8B,WACpEt9B,KAAKkH,IAAMlH,KAAKq9B,iBAAiBn2B,GAAMqxB,EAAW6E,EAAY,EAAI,GAC9Dj1B,EAAMlH,EAAQkH,KAAOlB,EACzBjH,KAAKu9B,WAAav9B,KAAKq9B,iBAAiBl1B,GACxCnI,KAAKw9B,aAAeN,EAAkBl9B,KAAKu9B,WAAap1B,EACpDE,EAAMpH,EAAQoH,IACbA,GAIDrI,KAAKy9B,SAAWz9B,KAAKq9B,iBAAiBh1B,GAAK,GAAQkwB,GAAahpB,EAAWlH,EAAKrI,KAAKq9B,iBAAiBh1B,OAAa,GACnHrI,KAAK09B,WAAaR,EAAkBl9B,KAAKq9B,iBAAiBh1B,GAAMkwB,GAAat3B,EAAQoH,MAJrFrI,KAAKy9B,SAAWN,EAChBn9B,KAAK09B,WAAaR,GAAmBE,EAAYp9B,KAAKkH,IAAMA,GAK5DlH,KAAKy9B,SAAWz9B,KAAKu9B,aACrBv9B,KAAKy9B,SAAWz9B,KAAKu9B,YAErBv9B,KAAK09B,YAAc19B,KAAKw9B,eACxBx9B,KAAK09B,WAAa19B,KAAKq9B,iBAAiBr9B,KAAKw9B,cAAc,EAAO,KAG1EV,aAAc,WACV,OACI30B,IAAKnI,KAAKw9B,aACVn1B,IAAKrI,KAAK09B,aAGlBb,eAAgB,WAAA,GAEJ57B,GACAwO,EACAG,EACA+tB,EACAC,CAMR,OAXK59B,MAAK69B,WACF58B,EAAUjB,KAAKiB,QACfwO,EAAWxO,EAAQwO,SACnBG,EAAe3O,EAAQ2O,aACvB+tB,EAAShuB,EAAU3P,KAAKw9B,aAAcx9B,KAAKu9B,WAAY9tB,EAAUG,GACjEguB,EAASjuB,EAAU3P,KAAK09B,WAAY19B,KAAKu9B,WAAY9tB,EAAUG,GACnE5P,KAAK69B,UACD11B,IAAKw1B,EACLt1B,IAAKu1B,IAGN59B,KAAK69B,UAEhBpU,MAAO,WACH,OACIthB,IAAKnI,KAAKiH,MACVoB,IAAKrI,KAAKkH,MAGlBw1B,WAAY,WACR,GAAIoB,GAAU99B,KAAKu7B,WAAWv7B,KAAKkH,IACnC,OAAO42B,IAAW99B,KAAKiB,QAAQs3B,UAAY,EAAI,IAEnDD,WAAY,WACR,OACInwB,IAAKnI,KAAKu9B,WACVl1B,IAAKrI,KAAKy9B,WAGlBV,WAAY,SAAU18B,GAClB,GAAIY,GAAUjB,KAAKiB,OACnB,OAAOwG,MAAKE,MAAMgI,EAAUtP,EAAOL,KAAKu9B,WAAYt8B,EAAQwO,SAAUxO,EAAQ2O,gBAElF2rB,WAAY,SAAUl7B,GAClB,GAAIY,GAAUjB,KAAKiB,OACnB,OAAOwG,MAAKE,MAAMgI,EAAUtP,EAAOL,KAAKiH,MAAOhG,EAAQwO,SAAUxO,EAAQ2O,gBAE7ED,UAAW,SAAUtP,GACjB,GAAIY,GAAUjB,KAAKiB,OACnB,OAAO0O,GAAUtP,EAAOL,KAAKu9B,WAAYt8B,EAAQwO,SAAUxO,EAAQ2O,eAEvEotB,YAAa,WACT,GAAIY,GAAS59B,KAAK+8B,WAAW/8B,KAAKy9B,SAClC,OAAOG,GAAS,GAEpBtgB,OAAQ,WAAA,GAGIrc,GACA0P,EAEK/D,EALT0Q,EAAStd,KAAK+9B,OAClB,KAAKzgB,EAID,IAHIrc,EAAUjB,KAAKiB,QACf0P,EAAQ3Q,KAAKs4B,aACjBt4B,KAAK+9B,QAAUzgB,KACN1Q,EAAO+D,EAAMxI,IAAKyE,GAAQ+D,EAAMtI,KACrCiV,EAAOnf,KAAKyO,GACZA,EAAOmB,EAAYnB,EAAM3L,EAAQ2O,aAAc3O,EAAQwO,SAAUxO,EAAQgM,aAGjF,OAAOqQ,IAEX2f,OAAQ,SAAUptB,EAAO4Z,GACrB,GAAIxoB,GAAUjB,KAAKiB,OACnB,OAAO8M,GAAY0b,EAAQzpB,KAAKiH,MAAQjH,KAAKu9B,WAAYt8B,EAAQ2O,aAAeC,EAAO5O,EAAQwO,SAAUxO,EAAQgM,eAErHowB,iBAAkB,SAAUh9B,EAAO29B,EAAOC,GAAxB,GACVhlB,GAAMjZ,KAAKiB,QACXwO,EAAWwJ,EAAIxJ,SACfG,EAAeqJ,EAAIrJ,aACnB3C,EAAegM,EAAIhM,aACnBhG,EAAQjH,KAAKiH,MACbgK,EAAOtB,EAAUtP,EAAO4G,EAAOwI,EAAUG,GACzCsuB,EAAcF,EAAQv2B,KAAKC,KAAKuJ,GAAQxJ,KAAKE,MAAMsJ,EAIvD,OAHIgtB,KACAC,GAAeD,GAEZlwB,EAAY9G,EAAOi3B,EAActuB,EAAcH,EAAUxC,MAkFpEwO,GAAmBJ,GAAaxb,QAChC6gB,MAAO,WACH,GAAIwY,GAAO,GAAIzd,IAAiBje,EAAEqC,UAAWG,KAAKiB,SAAUjB,KAAK0mB,aAEjE,OADAwS,GAAK7G,eACE6G,GAEXD,eAAgB,WACZ,GAAIhyB,GAAQjH,KAAKm+B,UAAU1U,QAAQthB,GACnC,OAAOnI,MAAKiB,QAAQwO,SAAWzP,KAAKiB,QAAQ2O,aAAe3I,GAE/D+e,gBAAiB,SAAU/kB,GACvB,MAAOA,IAEXkxB,WAAY,WAAA,GAEJzL,GACApW,EACArP,EACAyP,EAkBIC,EACAytB,EAGID,EACAE,EAQAC,CAnCZjjB,IAAa4C,GAAGkU,WAAW9M,KAAKrlB,MAC5B0mB,EAAe1mB,KAAK0mB,aACpBpW,EAAcoW,EAAarJ,KAC3Bpc,EAAUjB,KAAKiB,QACfyP,EAAazP,EAAQyP,eACpBA,EAAW6tB,UACZ7tB,EAAaH,EAAWD,EAAaI,GACrCA,EAAW6tB,SAAU,GAEzBt9B,EAAUmB,IAAa86B,iBAAiB,GAAQj8B,GAC5CyP,WAAYA,EACZvI,IAAKkI,EAAUC,EAAarP,EAAQkH,KACpCE,IAAKgI,EAAUC,EAAarP,EAAQoH,OAEpCqe,EAAa8X,SAAW9X,EAAa/H,WAAW1d,EAAQ4K,SAAWsM,GAAID,MACvEjX,EAAQi8B,iBAAkB,GAE9Bj8B,EAAQw9B,gBAAkBx9B,EAAQw9B,iBAAmBx9B,EAAQwO,SAC7DxO,EAAQy9B,oBAAsBz9B,EAAQy9B,qBAAuBz9B,EAAQ2O,aACrE5P,KAAKiB,QAAUA,EACfA,EAAQo4B,cAAgB3oB,EACpBA,EAAWhS,OAAS,GAChBiS,EAAQF,EAAcC,GACtB0tB,EAAen9B,EAAQm9B,aAC3Bp+B,KAAKm+B,UAAY,GAAI3iB,IAAU7K,EAAMxI,IAAKwI,EAAMtI,IAAKoK,GAASxR,IAC1Dm9B,GACID,EAAYn+B,KAAKm+B,UAAUrB,eAC3BuB,EAAkB7gC,EAAEqC,UAAWoB,GAC/Bs3B,WAAW,EACX2E,iBAAiB,EACjBztB,SAAU,MACVtH,IAAKg2B,EAAUh2B,IACfE,IAAK81B,EAAU91B,IACfiJ,cAAe8sB,IAEfE,EAAmBt+B,KAAKm+B,UAAUl9B,QACtC4P,EAAawtB,EAAiBC,EAAiB7uB,SAAU6uB,EAAiB1uB,cAC1E5P,KAAK2+B,cAAgB,GAAInjB,IAAU7K,EAAMxI,IAAKwI,EAAMtI,IAAKg2B,IAEzDr+B,KAAK2+B,cAAgB3+B,KAAKm+B,YAG9Bl9B,EAAQwO,SAAWxO,EAAQwO,UAAYlB,GACvCvO,KAAKm+B,UAAYn+B,KAAK2+B,cAAgB,GAAIpjB,IAAeta,KAGjEg5B,YAAa,SAAUC,GAAV,GAQLC,GACA7c,EACA/Z,EAKKR,EAGG+6B,EAjBR7kB,EAAMjZ,KACNm+B,EAAYllB,EAAIklB,UAChBQ,EAAgB1lB,EAAI0lB,cACpB3B,EAAc2B,EAAc3B,aAChC,KAAKh9B,KAAKiB,QAAQm9B,eAAiBpB,EAC/B,MAAO3hB,IAAa4C,GAAGgc,YAAY5U,KAAKrlB,KAAMk6B,EASlD,KAPIC,KACA7c,EAASqhB,EAAcrhB,SACvB/Z,EAAS,EACRvD,KAAKiB,QAAQs3B,YACdjb,EAASA,EAAOK,OAAOghB,EAAc1B,OAAOD,IAC5Cz5B,EAAS,IAEJR,EAAM,EAAGA,EAAMua,EAAO5e,OAAQqE,IACnCo3B,EAAQh8B,KAAKggC,EAAUxuB,UAAU2N,EAAOva,IAAQQ,GAC/B,IAAb22B,GAAkBn3B,GAAO,IACrB+6B,EAAU3D,EAAQz7B,OAAS,EAC/By7B,EAAQyE,OAAO77B,EAAK,EAAGo3B,EAAQ2D,EAAU,IAAM3D,EAAQ2D,GAAW3D,EAAQ2D,EAAU,IAAM5D,GAGlG,OAAOC,IAEX9C,iBAAkB,SAAUh3B,GAAV,GACVsQ,GAAQ3Q,KAAK2Q,QACbD,EAAa1Q,KAAKiB,QAAQyP,cAC9B,OAAOtB,GAAa/O,EAAOsQ,EAAMxI,MAAQ,GAAKiH,EAAa/O,EAAOsQ,EAAMtI,MAAQ,GAAKqI,EAAWhS,QAEpGg1B,eAAgB,SAAUrzB,GACtB,MAAOgQ,GAAUrQ,KAAK0mB,aAAarJ,KAAMhd,IAE7Ci3B,SAAU,SAAUj3B,GAChB,MAAOL,MAAKq1B,QAAQh1B,IAExBq7B,eAAgB,SAAUn0B,GAAV,GAWJ+tB,GACAC,EAXJt0B,EAAUjB,KAAKiB,QACfwO,EAAWxO,EAAQwO,SACnBxC,EAAehM,EAAQgM,aACvBpB,EAAW5K,EAAQ4K,SACnBonB,EAAUjzB,KAAKizB,UACflzB,EAAO8L,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QAC7C8R,EAAQ3Q,KAAK2Q,QACblI,EAAQ1I,GAAQ4Q,EAAMtI,IAAMsI,EAAMxI,KAClC5E,EAASM,GAAM0D,EAAQkB,EAAOI,GASlC,OARI8H,GAAMxI,KAAOwI,EAAMtI,MACfitB,EAAO3oB,EAAS1L,EAAQkH,KAAOwI,EAAMxI,IAAK5E,GAC1CgyB,EAAK5oB,EAAS1L,EAAQoH,KAAOsI,EAAMtI,IAAK9E,GAC5CoN,GACIxI,IAAK4F,EAAYunB,EAAM,EAAG7lB,EAAUxC,GACpC5E,IAAK0F,EAAYwnB,EAAI,EAAG9lB,EAAUxC,KAGnC0D,GAEXorB,WAAY,SAAUx0B,GAAV,GAOIoJ,GACAM,EAPR4tB,EAASp3B,KAAKqB,IAAIvB,GAClBrC,EAASlF,KAAK2Q,QACd2kB,EAAOpwB,EAAOiD,IACdotB,EAAKrwB,EAAOmD,GAChB,IAAIitB,GAAQC,EAAI,CACZ,KAAOsJ,KACCluB,EAAQtB,EAASimB,EAAMC,GACvBtkB,EAAOxJ,KAAK5D,MAAc,GAAR8M,GAClBpJ,EAAQ,GACR+tB,EAAO3oB,EAAS2oB,EAAMrkB,GACtBskB,EAAK5oB,EAAS4oB,GAAKtkB,KAEnBqkB,EAAO3oB,EAAS2oB,GAAOrkB,GACvBskB,EAAK5oB,EAAS4oB,EAAItkB,GAG1B/L,IACIiD,IAAKmtB,EACLjtB,IAAKktB,GAGb,MAAOrwB,IAEXstB,YAAa,WACT,OACIrqB,IAAKnI,KAAKiB,QAAQ2wB,OAAOa,KACzBpqB,IAAKrI,KAAK2+B,cAAc3B,gBAGhCd,IAAK,SAAU30B,GAAV,GAIGtG,GACAgyB,EACAlzB,EACAkZ,EACA9Q,EACAE,EACAy2B,EACAr2B,EACAlF,EACA+xB,EACAC,EACAwJ,CAdJ,OAAI/+B,MAAKg/B,UACE,MAEP/9B,EAAUjB,KAAKiB,QACfgyB,EAAUjzB,KAAKizB,UACflzB,EAAOkB,EAAQ4K,SAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACrDoa,EAAMjZ,KAAKm+B,UAAUrB,eACrB30B,EAAM8Q,EAAI9Q,IACVE,EAAM4Q,EAAI5Q,IACVy2B,EAAc9+B,KAAKm+B,UAAU1U,QAC7BhhB,EAAQ1I,GAAQsI,EAAMF,GACtB5E,EAASM,GAAM0D,EAAQkB,EAAOI,KAAsB5H,EAAQ0yB,WAAe,GAC3E2B,EAAO3oB,EAASxE,EAAK5E,GACrBgyB,EAAK5oB,EAAStE,EAAK9E,GACnBw7B,EAAW/+B,KAAKo4B,WAAW9oB,EAAOgmB,GAAOhmB,EAAOimB,GAAKjmB,EAAOwvB,EAAY32B,KAAMmH,EAAOwvB,EAAYz2B,KAAM9E,GACvGw7B,GACAA,EAAS52B,IAAM4E,EAAOgyB,EAAS52B,KAC/B42B,EAAS12B,IAAM0E,EAAOgyB,EAAS12B,KAC/B02B,EAAStvB,SAAWxO,EAAQwO,SAC5BsvB,EAASnvB,aAAe3O,EAAQ2O,cAAgB,EAChDmvB,EAASN,gBAAkBx9B,EAAQw9B,gBACnCM,EAASL,oBAAsBz9B,EAAQy9B,oBAChCK,GAPX,SAUJ5C,YAAa,SAAUl1B,EAAOC,GAAjB,GAILi1B,GACA8C,EACAC,EACAz2B,EACAxH,EACAkH,EACAE,CATJ,OAAIrI,MAAKg/B,UACE,MAEP7C,EAAc9gB,GAAa4C,GAAGke,YAAY9W,KAAKrlB,KAAMiH,EAAOC,GAC5D+3B,EAAaj/B,KAAKm+B,UAAUrB,eAC5BoC,EAAel/B,KAAKm+B,UAAUtB,iBAC9Bp0B,EAAQ4G,EAAS4vB,EAAW52B,IAAK42B,EAAW92B,MAAQ+2B,EAAa72B,IAAM62B,EAAa/2B,KACpFlH,EAAUjB,KAAKiB,QACfkH,EAAMwE,EAASsyB,EAAW92B,IAAKg0B,EAAYh0B,IAAMM,GACjDJ,EAAMsE,EAASsyB,EAAW92B,IAAKg0B,EAAY9zB,IAAMI,IAEjDN,IAAKA,EACLE,IAAKA,EACLoH,SAAUxO,EAAQw9B,iBAAmBx9B,EAAQwO,SAC7CG,aAAc3O,EAAQy9B,qBAAuBz9B,EAAQ2O,gBAG7D+rB,UAAW,SAAUp0B,GAAV,GAIHtG,GACAk+B,EACAL,EACA7lB,EACAqjB,EACAC,EACA/O,EACAvgB,EACAwC,EACAG,EACAzH,EACAE,EAEIgJ,EACAC,EACA8tB,EACAC,EACAz2B,EACA02B,EACAC,EAAkB1yB,EAQd2yB,CA9BZ,IAAIx/B,KAAKg/B,UACL,MAAO,KAcX,IAZI/9B,EAAUjB,KAAKiB,QACfk+B,EAAMl+B,EAAQw9B,kBAAoBjtB,GAClCstB,EAAc9+B,KAAKm+B,UAAU1U,QAC7BxQ,EAAMjZ,KAAKm+B,UAAUrB,eACrBR,EAAWrjB,EAAI9Q,IACfo0B,EAAWtjB,EAAI5Q,IACfmlB,EAAQxtB,KAAKm+B,UAAUl9B,QACvBgM,EAAeugB,EAAMvgB,aACrBwC,EAAW+d,EAAM/d,SACjBG,EAAe4d,EAAM5d,aACrBzH,EAAM4F,EAAYuuB,EAAU/0B,EAAQqI,EAAcH,EAAUxC,GAC5D5E,EAAM0F,EAAYwuB,GAAWh1B,EAAQqI,EAAcH,EAAUxC,GAC7DkyB,EAQA,GAPI9tB,EAAoBpQ,EAAQoQ,kBAC5BC,EAAgBrQ,EAAQqQ,cACxB8tB,EAAUrtB,GAAKV,EAAkB5B,IAAa6B,EAAgB5B,GAAcD,GAC5E4vB,EAAYhwB,EAASktB,EAAUD,GAC/B1zB,EAAOyG,EAAShH,EAAKF,GACrBm3B,EAAgB5tB,GAAWxO,QAAQuM,GAEnC7G,EAAO8G,GAAcD,IAAaA,IAAaV,GAC/CU,EAAWiC,GAAW4tB,EAAgB,GACtCC,EAAmBxtB,GAAKV,EAAkB5B,IAC1C5C,GAASwyB,GAAa/tB,EAAgB,GAAKiuB,EAAmB7vB,GAAcD,IAAa,EACzFtH,EAAMwE,EAAS2vB,EAAUzvB,GACzBxE,EAAMsE,EAAS4vB,GAAW1vB,OACvB,IAAIjE,EAAOw2B,GAAW3vB,IAAavB,GAAO,CACzCsxB,EAAY,CAChB,GAAG,CACCF,IACA7vB,EAAWiC,GAAW4tB,GACtBE,EAAY,EACZ3yB,EAAQ,EAAI6C,GAAcD,EAC1B,GACI8vB,GAAmBluB,EAAkB5B,GAAU+vB,GAC/CA,UACKA,EAAYnuB,EAAkB5B,GAAU/Q,QAAUmO,EAAQ0yB,EAAmBF,SACjF5vB,IAAavB,IAASrB,EAAQ0yB,EAAmBF,EAC1DxyB,IAASA,EAAQ0yB,EAAmBF,GAAa,EAC7CxyB,EAAQ,IACR1E,EAAMwE,EAAS2vB,GAAWzvB,GAC1BxE,EAAMsE,EAAS4vB,EAAU1vB,GACzB1E,EAAMwE,EAASxE,EAAKsR,GAAWpR,EAAKy2B,EAAY32B,IAAK22B,EAAYz2B,KAAOA,GACxEA,EAAMsE,EAAStE,EAAKoR,GAAWtR,EAAK22B,EAAY32B,IAAK22B,EAAYz2B,KAAOF,IAUpF,MANIA,GAAM22B,EAAY32B,MAClBA,EAAM22B,EAAY32B,KAElBE,EAAMy2B,EAAYz2B,MAClBA,EAAMy2B,EAAYz2B,KAElBF,GAAOE,GAAOgH,EAAShH,EAAKF,GAAO,GAE/BA,IAAKA,EACLE,IAAKA,EACLoH,SAAUxO,EAAQw9B,iBAAmBx9B,EAAQwO,SAC7CG,aAAc3O,EAAQy9B,qBAAuBz9B,EAAQ2O,cAL7D,QASJe,MAAO,WACH,MAAO3Q,MAAKm+B,UAAUrB,gBAE1BhK,gBAAiB,SAAUjjB,EAAO8iB,GAAjB,GAMTj1B,GALAuD,EAAUjB,KAAKiB,QACfqoB,EAAWroB,EAAQ+6B,YAAc/6B,EAAQm9B,aAAen9B,EAAQ+6B,UAAUnsB,GAAS,KACnFjD,EAAO5M,KAAK2+B,cAAc1B,OAAOptB,GACjC4vB,EAAa9M,EAAa+M,YAAY1/B,KAAK2+B,cAAc19B,QAAQwO,SAGrE,IAFAkjB,EAAahW,OAASgW,EAAahW,QAAU8iB,EACzC/hC,EAAOsC,KAAKw3B,cAAc5qB,EAAM0c,EAAUqJ,GAE1C,MAAO,IAAI7X,IAAUlO,EAAMlP,EAAMmS,EAAOyZ,EAAUqJ,IAG1DwI,cAAe,SAAU96B,GACrB,MAAOL,MAAKm+B,UAAUpB,WAAW18B,IAErC+2B,KAAM,SAAU9B,EAAMC,EAAImC,GAApB,GAUEN,GATAuI,EAAY3/B,KAAKm+B,UACjBl3B,EAAQquB,EACRpuB,EAAMquB,CAQV,IAPItuB,YAAiB6F,QACjB7F,EAAQ04B,EAAUhwB,UAAU1I,IAE5BC,YAAe4F,QACf5F,EAAMy4B,EAAUhwB,UAAUzI,IAE1BkwB,EAAOp3B,KAAKq1B,QAAQpuB,EAAOC,EAAKwwB,GAEhC,MAAON,GAAKnU,UAGpBoS,QAAS,SAAU1rB,EAAGC,EAAG8tB,GAAhB,GACDzwB,GAAQ0C,EACRzC,EAAM0C,CAOV,cANW3C,KAAU0Q,KACjB1Q,EAAQjH,KAAKm7B,cAAcl0B,UAEpBC,KAAQyQ,KACfzQ,EAAMlH,KAAKm7B,cAAcj0B,IAEtBmU,GAAa4C,GAAGoX,QAAQhQ,KAAKrlB,KAAMiH,EAAOC,EAAKwwB,IAE1DY,WAAY,WAAA,GACJr3B,GAAUjB,KAAKiB,QACf0P,EAAQF,EAAcxP,EAAQo4B,cAClC,QACIlxB,IAAK4E,EAAO4D,EAAMxI,KAClBE,IAAK0E,EAAO4D,EAAMtI,OAG1BmzB,WAAY,SAAU3rB,EAAO4Z,GACzB,MAAOzpB,MAAKm+B,UAAUlB,OAAOptB,EAAO4Z,IAExCgS,gBAAiB,WACb,MAAOz7B,MAAKm+B,UAAUnB,eAE1B1D,aAAc,WACV,MAAOt5B,MAAKm+B,UAAUtB;EAE1B5F,mBAAoB,WAChB,OAAQj3B,KAAK2+B,cAAc19B,QAAQs3B,WAEvCC,mBAAoB,WACZx4B,KAAKg/B,YAGTh/B,KAAKiB,QAAQyP,WAAa1Q,KAAKm+B,UAAU7gB,WAE7Cge,YAAa,SAAU1a,GACnB,GAAI/Q,GAAQ7P,KAAKo7B,mBAAmBxa,EACpC,OAAc,QAAV/Q,EACO,KAEJ7P,KAAKm+B,UAAUlB,OAAOptB,IAEjC0rB,WAAY,SAAUl7B,GAClB,MAAOL,MAAKm+B,UAAU5C,WAAWl7B,IAErCo8B,oBAAqB,WACjB,GAAI9rB,GAAQ3Q,KAAKm+B,UAAU7F,YAC3B,QACInwB,IAAKnI,KAAKm+B,UAAU5C,WAAW5qB,EAAMxI,KACrCE,IAAKrI,KAAKm+B,UAAU5C,WAAW5qB,EAAMtI,OAG7CkxB,WAAY,WACR,MAAOv5B,MAAKm+B,UAAU1U,SAE1BiT,WAAY,WACR,MAAO18B,MAAKm+B,UAAUzB,cAE1BsC,QAAS,WACL,OAAQh/B,KAAKiB,QAAQo4B,cAAc36B,QAEvC+6B,aAAc,WAAA,GAINx4B,GACAg+B,EACAU,CALJ,OAAI3/B,MAAKiB,QAAQi8B,mBAAoB,GAASl9B,KAAKg/B,UACxCh/B,KAAK2Q,SAEZ1P,EAAUjB,KAAKiB,QACfg+B,EAAaxuB,EAAcxP,EAAQo4B,eACnCsG,EAAY,GAAInkB,IAAUyjB,EAAW92B,IAAK82B,EAAW52B,IAAK7K,EAAEqC,UAAWoB,GACvEs3B,WAAW,EACX2E,iBAAiB,EACjBI,WAAYr8B,EAAQs3B,aAEjBoH,EAAU7C,mBAGzBj1B,EAAkB4T,IACd3T,KAAMgP,GACN8a,QAAU8N,YAAapkB,IACvBjK,mBACI0nB,cACI,EACA,GACA,KAEJD,SACI,EACA,EACA,EACA,GACA,IAEJD,SACI,EACA,EACA,EACA,GACA,IAEJtrB,OACI,EACA,EACA,GAEJqrB,MACI,EACA,EACA,GAEJD,OACI,EACA,GAEJD,QACI,EACA,EACA,EACA,GAEJD,OACI,EACA,EACA,EACA,EACA,GACA,GACA,KAGRnnB,cAAe,KA6CfoK,GAAkBjU,KAAKsB,IAAI,IAAKF,GAAoB,GACpD8S,GAAcT,GAAKrb,QACnBC,KAAM,SAAU0T,EAAWC,EAAWxS,EAASylB,GAC3CxL,GAAK+C,GAAGne,KAAKulB,KAAKrlB,KAAMxC,EAAEqC,UAAWoB,GACjCuS,UAAWA,EACXC,UAAWA,IACXiT,IAERV,gBAAiB,SAAU/kB,GACvB,GAAI6S,GAAcP,GAAgBtS,EAAQuS,UAAWvS,EAAQwS,UAAWxS,EAExE,OADAjB,MAAK4/B,aAAe3rB,GAAiBH,EAAa7S,GAC3CmT,GAAYN,EAAa7S,IAEpCkxB,WAAY,WACRnyB,KAAK67B,SAAW77B,KAAK4/B,aAAaz3B,IAClCnI,KAAK87B,SAAW97B,KAAK4/B,aAAav3B,IAClCrI,KAAK6/B,eAAiB7/B,KAAK4/B,aAAa/rB,UACxC7T,KAAKwT,UAAYxT,KAAKiB,QAAQuS,UAC9BxT,KAAKyT,UAAYzT,KAAKiB,QAAQwS,WAElCiN,MAAO,WACH,MAAO,IAAI/E,IAAY3b,KAAKwT,UAAWxT,KAAKyT,UAAWjW,EAAEqC,UAAWG,KAAKiB,SAAUjB,KAAK0mB,eAE5F2U,WAAY,WACR,MAAO,IAEX1qB,MAAO,WACH,GAAI1P,GAAUjB,KAAKiB,OACnB,QACIkH,IAAKlH,EAAQkH,IACbE,IAAKpH,EAAQoH,MAGrBy3B,aAAc,SAAUC,GAAV,GAIN9+B,GACA0P,CAJJ,OAAkB,KAAdovB,EACO,GAEP9+B,EAAUjB,KAAKiB,QACf0P,EAAQ1P,EAAQoH,IAAMpH,EAAQkH,IAC3BV,KAAKE,MAAM9D,GAAM8M,EAAQovB,EAAW34B,KAAoB,IAEnEgzB,iBAAkB,SAAUnsB,EAAMkmB,GAAhB,GAkBLpxB,GAjBL9B,EAAUjB,KAAKiB,QACf4K,EAAW5K,EAAQ4K,SACnB8nB,EAAU1yB,EAAQ0yB,QAClBV,EAAUjzB,KAAKizB,UACf+M,EAAWn0B,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACjD8R,EAAQ1P,EAAQoH,IAAMpH,EAAQkH,IAC9BM,EAAQu3B,EAAWrvB,EACnBM,EAAOhD,EAAOxF,EACdw3B,EAAYjgC,KAAK8/B,aAAa7xB,GAC9BiyB,GAAOr0B,KAAgB,IAAM8nB,KAAe,GAC5CwM,EAAoB,IAARD,EAAY,EAAI,EAC5B7F,KACApS,EAAMgL,GAASpnB,EAAWsM,GAAID,IAAKioB,GACnCC,EAAW,CAIf,KAHIjM,IACAiM,EAAWjM,EAAWlmB,GAEjBlL,EAAM,EAAGA,EAAMk9B,EAAWl9B,IAC3BA,EAAMq9B,IAAa,GACnB/F,EAAUl8B,KAAK0F,GAAMokB,EAAK7gB,KAE9B6gB,GAAYhX,EAAOivB,CAEvB,OAAO7F,IAEXhG,sBAAuB,WACnB,MAAOr0B,MAAKo6B,iBAAiBp6B,KAAKiB,QAAQ4S,YAE9CygB,sBAAuB,WACnB,MAAOt0B,MAAKo6B,iBAAiBp6B,KAAKiB,QAAQwT,YAE9C4gB,QAAS,SAAU1rB,EAAGC,EAAG8tB,GAAhB,GAIDz2B,GACA4K,EACA8nB,EACAkG,EACA5G,EACA/mB,EACA8zB,EACAE,EACAjvB,EACA8pB,EACA9zB,EACAC,EAWA2zB,EAAIC,CAUR,OAnCc,UAAVpD,IACAA,GAAQ,GAERz2B,EAAUjB,KAAKiB,QACf4K,EAAW5K,EAAQ4K,SACnB8nB,EAAU1yB,EAAQ0yB,QAClBkG,EAAYhuB,EAAWsM,GAAID,GAC3B+a,EAAUjzB,KAAKizB,UACf/mB,EAAY+mB,EAAQ4G,GAAalG,EAAU,EAAI,IAC/CqM,EAAWn0B,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACjDqhC,EAAMvM,KAAe,EACrB1iB,EAAOivB,GAAOF,GAAY/+B,EAAQoH,IAAMpH,EAAQkH,MAChD4yB,EAAU,GAAItwB,IAAIwoB,EAAQ5oB,GAAI4oB,EAAQ3oB,GAAI2oB,EAAQ5oB,GAAI4oB,EAAQ3oB,IAC9DrD,EAAQ0C,EACRzC,EAAM0C,EACLlG,GAAQuD,KACTA,EAAQC,GAAO,GAEdxD,GAAQwD,KACTA,EAAMD,GAAS,GAEfywB,IACAzwB,EAAQQ,KAAKY,IAAIZ,KAAKU,IAAIlB,EAAOhG,EAAQoH,KAAMpH,EAAQkH,KACvDjB,EAAMO,KAAKY,IAAIZ,KAAKU,IAAIjB,EAAKjG,EAAQoH,KAAMpH,EAAQkH,MAGnD0D,GACAgvB,EAAK55B,EAAQoH,IAAMZ,KAAKY,IAAIpB,EAAOC,GACnC4zB,EAAK75B,EAAQoH,IAAMZ,KAAKU,IAAIlB,EAAOC,KAEnC2zB,EAAKpzB,KAAKU,IAAIlB,EAAOC,GAAOjG,EAAQkH,IACpC2yB,EAAKrzB,KAAKY,IAAIpB,EAAOC,GAAOjG,EAAQkH,KAExC4yB,EAAQlB,EAAY,GAAKxmB,GAAgBnH,EAAY+E,GAAQ0iB,EAAUmH,EAAKD,IAC5EE,EAAQlB,EAAY,GAAKxmB,GAAgBnH,EAAY+E,GAAQ0iB,EAAUkH,EAAKC,IACrEC,GAEXsF,SAAU,SAAUzf,GAAV,GAiBFvgB,GAhBAY,EAAUjB,KAAKiB,QACf4K,EAAW5K,EAAQ4K,SACnB8nB,EAAU1yB,EAAQ0yB,QAClBtrB,GAAapH,EAAQoH,IACrBF,GAAalH,EAAQkH,IACrB0xB,EAAYhuB,EAAWsM,GAAID,GAC3B+a,EAAUjzB,KAAKizB,UACf/mB,EAAY+mB,EAAQ4G,GAAalG,EAAU,EAAI,IAC/CqM,EAAWn0B,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACjDqhC,EAAMvM,KAAe,EACrBpwB,EAAS28B,GAAOtf,EAAMiZ,GAAa3tB,GACnC+E,GAAQ5I,EAAMF,GAAO63B,EACrBM,EAAc/8B,EAAS0N,CAC3B,OAAI1N,GAAS,GAAKA,EAASy8B,EAChB,MAEP3/B,EAAQwL,EAAWxD,EAAMi4B,EAAcn4B,EAAMm4B,EAC1Cz8B,GAAMxD,EAAOwI,MAExB6yB,eAAgB,SAAUn0B,GAAV,GACRtG,GAAUjB,KAAKiB,QACf4K,EAAW5K,EAAQ4K,SACnB8nB,EAAU1yB,EAAQ0yB,QAClBtrB,EAAMpH,EAAQoH,IACdF,EAAMlH,EAAQkH,IACd8qB,EAAUjzB,KAAKizB,UACflzB,EAAO8L,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QAC7C8R,EAAQtI,EAAMF,EACdM,EAAQ1I,EAAO4Q,EACfpN,EAASM,GAAM0D,EAAQkB,EAAOI,GAIlC,QAHKgD,IAAY8nB,GAAc9nB,GAAY8nB,IACvCpwB,GAAUA,IAGV4E,IAAKA,EAAM5E,EACX8E,IAAKA,EAAM9E,EACXA,OAAQA,IAGhBw4B,WAAY,SAAUx0B,GAAV,GACJtG,GAAUjB,KAAKiB,QACfsC,GAAUgE,EAAQtG,EAAQ4S,SAC9B,QACI1L,IAAKlH,EAAQkH,IAAM5E,EACnB8E,IAAKpH,EAAQoH,IAAM9E,IAG3BmvB,YAAa,WACT,MAAO1yB,MAAK8/B,aAAa9/B,KAAKiB,QAAQ4S,YAE1Cif,gBAAiB,SAAUjjB,EAAO8iB,GAAjB,GACT1xB,GAAUjB,KAAKiB,QACfZ,EAAQwD,GAAM5C,EAAQkH,IAAM0H,EAAQ5O,EAAQ4S,UAAWhL,IACvDnL,EAAOsC,KAAKw3B,cAAcn3B,EAAO,KAAMsyB,EAC3C,OAAO,IAAI7X,IAAUza,EAAO3C,EAAMmS,EAAO,KAAM8iB,IAEnD0E,iBAAkB,SAAUh3B,GACxB,GAAIsQ,GAAQ3Q,KAAK2Q,OACjB,OAAOA,GAAMxI,KAAO9H,GAASA,GAASsQ,EAAMtI,KAEhD6zB,IAAK,SAAU30B,GACX,GAAIoJ,GAAQ3Q,KAAK07B,eAAen0B,EAChC,OAAOvH,MAAKo4B,WAAWznB,EAAMxI,IAAKwI,EAAMtI,IAAKrI,KAAK67B,SAAU77B,KAAK87B,SAAUnrB,EAAMpN,SAErF44B,YAAa,SAAUl1B,EAAOC,GAAjB,GACLm0B,GAAar7B,KAAKqgC,SAASp5B,GAC3Bs5B,EAAWvgC,KAAKqgC,SAASn5B,GACzBiB,EAAMV,KAAKU,IAAIkzB,EAAYkF,GAC3Bl4B,EAAMZ,KAAKY,IAAIgzB,EAAYkF,EAC/B,IAAIvgC,KAAKwgC,aAAar4B,EAAKE,GACvB,OACIF,IAAKA,EACLE,IAAKA,IAIjBszB,UAAW,SAAUp0B,GAAV,GACH0R,GAAMjZ,KACN67B,EAAW5iB,EAAI4iB,SACfC,EAAW7iB,EAAI6iB,SACf2E,EAAWzgC,KAAK+7B,WAAWx0B,GAC3BY,EAAMsR,GAAWgnB,EAASt4B,IAAK0zB,EAAUC,GACzCzzB,EAAMoR,GAAWgnB,EAASp4B,IAAKwzB,EAAUC,EAC7C,IAAI97B,KAAKwgC,aAAar4B,EAAKE,GACvB,OACIF,IAAKA,EACLE,IAAKA,IAIjBm4B,aAAc,SAAUr4B,EAAKE,GACzB,MAAOA,GAAMF,EAAMuT,MAgF3B7T,EAAkB8T,IACd7T,KAAM,UACNK,IAAK,EACLE,IAAK,EACLwD,UAAU,EACVgqB,gBACI7O,SAAS,EACTnoB,MAAO,EACP+M,MAAO8K,IAEXkb,QAAUjV,OAAQ,0BAClB8K,OAAQ,IAER7L,GAAgBV,GAAKrb,QACrBC,KAAM,SAAU0T,EAAWC,EAAWW,EAAasS,GAA7C,GACEve,GAAM4E,EAAOyG,GACbnL,EAAM0E,EAAO0G,GACbnD,EAAcoW,EAAarJ,KAC3Bpc,EAAUmT,KACdnT,GAAUmB,GAAWnB,OACjBkH,IAAKkI,EAAUC,EAAarP,EAAQkH,KACpCE,IAAKgI,EAAUC,EAAarP,EAAQoH,KACpCkN,kBAAmBhF,EAAWD,EAAarP,EAAQy/B,oBAAsBz/B,EAAQsU,qBAErFtU,EAAU8T,GAAc5M,EAAKE,EAAKpH,GAClCia,GAAK+C,GAAGne,KAAKulB,KAAKrlB,KAAMiB,EAASylB,GACjC1mB,KAAKsQ,YAAcA,EACnBtQ,KAAKwT,UAAYrL,EACjBnI,KAAKyT,UAAYpL,EACjBrI,KAAK67B,SAAWvsB,EAAOJ,EAAUI,EAAOnH,GAAO,EAAGlH,EAAQwO,WAC1DzP,KAAK87B,SAAWxsB,EAAOH,EAASG,EAAOjH,GAAO,EAAGpH,EAAQwO,YAE7DiR,MAAO,WACH,MAAO,IAAI9E,IAAc5b,KAAKwT,UAAWxT,KAAKyT,UAAWjW,EAAEqC,UAAWG,KAAKiB,SAAUjB,KAAK0mB,eAE9F/V,MAAO,WACH,GAAI1P,GAAUjB,KAAKiB,OACnB,QACIkH,IAAKlH,EAAQkH,IACbE,IAAKpH,EAAQoH,MAGrBy3B,aAAc,SAAUC,GACpB,GAAI9+B,GAAUjB,KAAKiB,OACnB,OAAOwG,MAAKE,MAAMoI,EAAS9O,EAAQkH,IAAKlH,EAAQoH,IAAKpH,EAAQwO,UAAYswB,EAAY,IAEzF3F,iBAAkB,SAAUnpB,GAAV,GAYLzS,GACDoO,EACAqb,EAbJhnB,EAAUjB,KAAKiB,QACf4K,EAAW5K,EAAQ4K,SACnBonB,EAAUjzB,KAAKizB,UACfiN,GAAOr0B,KAAgB,IAAM5K,EAAQ0yB,WAAe,GACpDwM,EAAoB,IAARD,EAAY,EAAI,EAC5Bj5B,EAAQgsB,GAASpnB,EAAWsM,GAAID,IAAKioB,GACrCF,EAAYjgC,KAAK8/B,aAAa7uB,GAC9B0vB,EAAYtxB,EAASpO,EAAQoH,IAAKpH,EAAQkH,KAC1C63B,EAAWn0B,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACjD4J,EAAQu3B,EAAWW,EACnBtG,GAAapzB,EACjB,KAASzI,EAAI,EAAGA,EAAIyhC,EAAWzhC,IACvBoO,EAAOmB,EAAY9M,EAAQkH,IAAK3J,EAAIyS,EAAMhQ,EAAQwO,UAClDwY,EAAMhhB,EAAQoI,EAASzC,EAAM3L,EAAQkH,KAAOM,EAAQy3B,EACxD7F,EAAUl8B,KAAK0F,GAAMokB,EAAK7gB,IAE9B,OAAOizB,IAEXhG,sBAAuB,WACnB,MAAOr0B,MAAKo6B,iBAAiBp6B,KAAKiB,QAAQ4S,YAE9CygB,sBAAuB,WACnB,MAAOt0B,MAAKo6B,iBAAiBp6B,KAAKiB,QAAQwT,YAE9C4gB,QAAS,SAAU1rB,EAAGC,EAAG8tB,GACrB,MAAO/b,IAAY3T,UAAUqtB,QAAQhQ,KAAKrlB,KAAMqQ,EAAUrQ,KAAKsQ,YAAa3G,GAAI0G,EAAUrQ,KAAKsQ,YAAa1G,GAAI8tB,IAEpH2I,SAAU,SAAUzf,GAChB,GAAIvgB,GAAQsb,GAAY3T,UAAUq4B,SAAShb,KAAKrlB,KAAM4gB,EACtD,OAAiB,QAAVvgB,EAAiB0M,EAAO1M,GAAS,MAE5CqyB,YAAa,WACT,MAAO1yB,MAAK8/B,aAAa9/B,KAAKiB,QAAQ4S,YAE1Cif,gBAAiB,SAAUjjB,EAAO8iB,GAAjB,GAOT8M,GAEA/hC,EARAuD,EAAUjB,KAAKiB,QACfsC,EAASsM,EAAQ5O,EAAQ4S,UACzBjH,EAAO3L,EAAQkH,GAOnB,OANI5E,GAAS,IACTqJ,EAAOmB,EAAYnB,EAAMrJ,EAAQtC,EAAQwO,WAEzCgwB,EAAa9M,EAAa+M,YAAYz+B,EAAQwO,UAClDkjB,EAAahW,OAASgW,EAAahW,QAAU8iB,EACzC/hC,EAAOsC,KAAKw3B,cAAc5qB,EAAM,KAAM+lB,GACnC,GAAI7X,IAAUlO,EAAMlP,EAAMmS,EAAO,KAAM8iB,IAElD+I,eAAgB,SAAUn0B,EAAOq5B,GAAjB,GACR3/B,GAAUjB,KAAKiB,QACfwO,EAAWxO,EAAQwO,SACnBxC,EAAehM,EAAQgM,aACvBgmB,EAAUjzB,KAAKizB,UACflzB,EAAOkB,EAAQ4K,SAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACrD8R,EAAQ3Q,KAAK2Q,QACblI,EAAQ1I,EAAOsP,EAASsB,EAAMtI,IAAKsI,EAAMxI,KACzC5E,EAASM,GAAM0D,EAAQkB,EAAOI,KAAsB5H,EAAQ0yB,WAAe,GAC3E2B,EAAO3oB,EAAS1L,EAAQkH,IAAK5E,GAC7BgyB,EAAK5oB,EAAS1L,EAAQoH,IAAK9E,EAK/B,OAJKq9B,KACDtL,EAAOvnB,EAAYunB,EAAM,EAAG7lB,EAAUxC,GACtCsoB,EAAKxnB,EAAYwnB,EAAI,EAAG9lB,EAAUxC,KAGlC9E,IAAKmtB,EACLjtB,IAAKktB,EACLhyB,OAAQA,IAGhBw4B,WAAY,SAAUx0B,GAKlB,IALQ,GAMAoJ,GACAM,EANJgI,EAAMjZ,KAAKiB,QACXq0B,EAAOrc,EAAI9Q,IACXotB,EAAKtc,EAAI5Q,IACTw2B,EAASp3B,KAAKqB,IAAIvB,GACfs3B,KACCluB,EAAQtB,EAASimB,EAAMC,GACvBtkB,EAAOxJ,KAAK5D,MAAc,GAAR8M,GAClBpJ,EAAQ,GACR+tB,EAAO3oB,EAAS2oB,EAAMrkB,GACtBskB,EAAK5oB,EAAS4oB,GAAKtkB,KAEnBqkB,EAAO3oB,EAAS2oB,GAAOrkB,GACvBskB,EAAK5oB,EAAS4oB,EAAItkB,GAG1B,QACI9I,IAAKmtB,EACLjtB,IAAKktB,IAGb8B,iBAAkB,SAAUh3B,GACxB,GAAIsQ,GAAQ3Q,KAAK2Q,OACjB,OAAOvB,GAAa/O,EAAOsQ,EAAMxI,MAAQ,GAAKiH,EAAa/O,EAAOsQ,EAAMtI,MAAQ,GAEpF6zB,IAAK,SAAU30B,GAAV,GACGoJ,GAAQ3Q,KAAK07B,eAAen0B,GAAO,GACnCs5B,EAAgB7gC,KAAKo4B,WAAW9oB,EAAOqB,EAAMxI,KAAMmH,EAAOqB,EAAMtI,KAAMrI,KAAK67B,SAAU77B,KAAK87B,SAAUnrB,EAAMpN,OAC9G,IAAIs9B,EACA,OACI14B,IAAK4E,EAAO8zB,EAAc14B,KAC1BE,IAAK0E,EAAO8zB,EAAcx4B,OAItC8zB,YAAa,SAAUl1B,EAAOC,GAAjB,GACLm0B,GAAar7B,KAAKqgC,SAASp5B,GAC3Bs5B,EAAWvgC,KAAKqgC,SAASn5B,GACzBiB,EAAMV,KAAKU,IAAIkzB,EAAYkF,GAC3Bl4B,EAAMZ,KAAKY,IAAIgzB,EAAYkF,EAC/B,QACIp4B,IAAK4E,EAAO5E,GACZE,IAAK0E,EAAO1E,KAGpBszB,UAAW,SAAUp0B,GAAV,GACHoJ,GAAQ3Q,KAAK+7B,WAAWx0B,GACxBY,EAAM4E,EAAO0M,GAAWnK,EAAOqB,EAAMxI,KAAMnI,KAAK67B,SAAU77B,KAAK87B,WAC/DzzB,EAAM0E,EAAO0M,GAAWnK,EAAOqB,EAAMtI,KAAMrI,KAAK67B,SAAU77B,KAAK87B,UACnE,QACI3zB,IAAKA,EACLE,IAAKA,MAyCjBR,EAAkB+T,IACd9T,KAAMgP,GACN+e,gBACI7O,SAAS,EACTnoB,MAAO,EACP+M,MAAO8K,IAEXkb,QAAU8N,YAAapkB,MAEvBO,GAAqB,GACrBC,GAAkBZ,GAAKrb,QACvBC,KAAM,SAAU0T,EAAWC,EAAWxS,EAASylB,GAAzC,GACEtS,GAAchS,IACdyR,UAAWgI,GACX1T,IAAKqL,EACLnL,IAAKoL,GACNxS,GACCyU,EAAOtB,EAAYP,UACnBD,EAAU+B,GAAclC,EAAWiC,GACnC/B,EAAU8B,GAAcjC,EAAWC,EAAWW,GAC9CzD,EAAQ2E,GAAU3B,EAASC,EAASQ,EAAanT,EACrDmT,GAAY/L,IAAMsI,EAAMtI,IACxB+L,EAAYjM,IAAMwI,EAAMxI,IACxBiM,EAAYK,UAAYxT,EAAQwT,WAAa5Q,GAAM6R,EAAO,EAAG7M,IAC7DqS,GAAK+C,GAAGne,KAAKulB,KAAKrlB,KAAMoU,EAAasS,GACrC1mB,KAAK67B,SAAWn4B,GAAQzC,EAAQkH,KAAOV,KAAKU,IAAIwL,EAAS1S,EAAQkH,KAAOwL,EACxE3T,KAAK87B,SAAWp4B,GAAQzC,EAAQoH,KAAOZ,KAAKY,IAAIuL,EAAS3S,EAAQoH,KAAOuL,EACxE5T,KAAK8gC,OAASj9B,GAAMmF,GAAI2H,EAAMxI,IAAKuN,GAAO7M,IAC1C7I,KAAK+gC,OAASl9B,GAAMmF,GAAI2H,EAAMtI,IAAKqN,GAAO7M,IAC1C7I,KAAKwT,UAAYA,EACjBxT,KAAKyT,UAAYA,EACjBzT,KAAKqyB,gBAET3R,MAAO,WACH,MAAO,IAAI5E,IAAgB9b,KAAKwT,UAAWxT,KAAKyT,UAAWjW,EAAEqC,UAAWG,KAAKiB,SAAUjB,KAAK0mB,eAEhG2U,WAAY,WACR,MAAOr7B,MAAKiB,QAAQkH,KAExBktB,QAAS,SAAU1rB,EAAGC,EAAG8tB,GAAhB,GAgCDmD,GAAIC,EA/BJ7hB,EAAMjZ,KACNiB,EAAUgY,EAAIhY,QACd6/B,EAAS7nB,EAAI6nB,OACbC,EAAS9nB,EAAI8nB,OACbpN,EAAU1yB,EAAQ0yB,QAClB9nB,EAAW5K,EAAQ4K,SACnB6J,EAAOzU,EAAQ4S,UACfgmB,EAAYhuB,EAAWsM,GAAID,GAC3B+a,EAAUjzB,KAAKizB,UACf/mB,EAAY+mB,EAAQ4G,GAAalG,EAAU,EAAI,IAC/CqM,EAAWn0B,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACjDqhC,EAAMvM,KAAe,EACrB1iB,EAAOivB,GAAOF,GAAYe,EAASD,IACnC/F,EAAU,GAAItwB,IAAIwoB,EAAQ5oB,GAAI4oB,EAAQ3oB,GAAI2oB,EAAQ5oB,GAAI4oB,EAAQ3oB,IAC9DrD,EAAQ0C,EACRzC,EAAM0C,CAOV,OANKlG,IAAQuD,KACTA,EAAQC,GAAO,GAEdxD,GAAQwD,KACTA,EAAMD,GAAS,GAEfA,GAAS,GAAKC,GAAO,EACd,MAEPwwB,IACAzwB,EAAQQ,KAAKY,IAAIZ,KAAKU,IAAIlB,EAAOhG,EAAQoH,KAAMpH,EAAQkH,KACvDjB,EAAMO,KAAKY,IAAIZ,KAAKU,IAAIjB,EAAKjG,EAAQoH,KAAMpH,EAAQkH,MAEvDlB,EAAQ+B,GAAI/B,EAAOyO,GACnBxO,EAAM8B,GAAI9B,EAAKwO,GAEX7J,GACAgvB,EAAKkG,EAASt5B,KAAKY,IAAIpB,EAAOC,GAC9B4zB,EAAKiG,EAASt5B,KAAKU,IAAIlB,EAAOC,KAE9B2zB,EAAKpzB,KAAKU,IAAIlB,EAAOC,GAAO45B,EAC5BhG,EAAKrzB,KAAKY,IAAIpB,EAAOC,GAAO45B,GAEhC/F,EAAQlB,EAAY,GAAKxmB,GAAgBnH,EAAY+E,GAAQ0iB,EAAUmH,EAAKD,IAC5EE,EAAQlB,EAAY,GAAKxmB,GAAgBnH,EAAY+E,GAAQ0iB,EAAUkH,EAAKC,IACrEC,IAEXsF,SAAU,SAAUzf,GAAV,GAoBFvgB,GAnBA4Y,EAAMjZ,KACNiB,EAAUgY,EAAIhY,QACd6/B,EAAS7nB,EAAI6nB,OACbC,EAAS9nB,EAAI8nB,OACbpN,EAAU1yB,EAAQ0yB,QAClB9nB,EAAW5K,EAAQ4K,SACnB6J,EAAOzU,EAAQ4S,UACfof,EAAUjzB,KAAKizB,UACfiN,EAAMr0B,IAAa8nB,EAAU,KAC7BwM,EAAoB,IAARD,EAAY,EAAI,EAC5BF,EAAWn0B,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACjDoS,GAAQ8vB,EAASD,GAAUd,EAC3BnG,EAAYhuB,EAAWsM,GAAID,GAC3BhM,EAAY+mB,EAAQ4G,EAAYsG,GAChC58B,EAAS28B,GAAOtf,EAAMiZ,GAAa3tB,GACnCo0B,EAAc/8B,EAAS0N,CAC3B,OAAI1N,GAAS,GAAKA,EAASy8B,EAChB,MAEP3/B,EAAQygC,EAASR,EACdz8B,GAAM4D,KAAKsB,IAAI2M,EAAMrV,GAAQwI,MAExC8H,MAAO,WACH,GAAI1P,GAAUjB,KAAKiB,OACnB,QACIkH,IAAKlH,EAAQkH,IACbE,IAAKpH,EAAQoH,MAGrB0zB,WAAY,SAAUx0B,GAAV,GACJmO,GAAO1V,KAAKiB,QAAQ4S,UACpBtQ,GAAUgE,CACd,QACIY,IAAKV,KAAKsB,IAAI2M,EAAM1V,KAAK8gC,OAASv9B,GAClC8E,IAAKZ,KAAKsB,IAAI2M,EAAM1V,KAAK+gC,OAASx9B,KAG1Cm4B,eAAgB,SAAUn0B,GAAV,GACR0R,GAAMjZ,KACNiB,EAAUgY,EAAIhY,QACd6/B,EAAS7nB,EAAI6nB,OACbC,EAAS9nB,EAAI8nB,OACbpN,EAAU1yB,EAAQ0yB,QAClB9nB,EAAW5K,EAAQ4K,SACnB6J,EAAOzU,EAAQ4S,UACfof,EAAUjzB,KAAKizB,UACflzB,EAAO8L,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QAC7C4J,EAAQ1I,GAAQghC,EAASD,GACzBv9B,EAASM,GAAM0D,EAAQkB,EAAOI,GAIlC,QAHKgD,IAAY8nB,GAAc9nB,GAAY8nB,IACvCpwB,GAAUA,IAGV4E,IAAKV,KAAKsB,IAAI2M,EAAMorB,EAASv9B,GAC7B8E,IAAKZ,KAAKsB,IAAI2M,EAAMqrB,EAASx9B,GAC7BA,OAAQA,IAGhBmvB,YAAa,WAAA,GACLsO,GAAWv5B,KAAKE,MAAM3H,KAAK+gC,QAC3B3uB,EAAQ3K,KAAKE,MAAMq5B,EAAWhhC,KAAK8gC,QAAU,CACjD,OAAO1uB,IAEXiiB,sBAAuB,WACnB,GAAIxnB,KAOJ,OANA7M,MAAKihC,4BAA4B,SAAUx1B,GACvCoB,EAAM1O,KAAKsN,KAEXwF,KAAM,EACNwhB,KAAM,IAEH5lB,GAEXmnB,YAAa,SAAUC,GASnB,QAASpD,GAAOqQ,EAAc51B,GAC1B8oB,EAAgB7oB,MAAQ2nB,EAASD,EAAQ1oB,GAAK0oB,EAAQ1oB,GAAKe,EAAYvL,KACvEq0B,EAAgB5oB,MAAQ0nB,EAASD,EAAQ3oB,GAAKgB,EAAYvL,KAAOkzB,EAAQ3oB,GACzE8pB,EAAgB3oB,SAAWy1B,EAC3BjN,EAAU/a,OAAO7N,EAAe+oB,EAAiB9oB,IAb5C,GACLrK,GAAUjB,KAAKiB,QACfgxB,EAAahxB,EAAQgxB,WACrBF,EAAa9wB,EAAQ8wB,WACrBlmB,EAAW5K,EAAQ4K,SACnBqnB,EAASjyB,EAAQ2wB,OAAOsB,OACxBD,EAAUjzB,KAAKizB,UACfpmB,KACAunB,GAAoBvoB,SAAUA,EAalC,OANIomB,GAAWjL,SACXhnB,KAAKihC,4BAA4BpQ,EAAQoB,GAEzCF,EAAW/K,SACXhnB,KAAKmhC,4BAA4BtQ,EAAQkB,GAEtCllB,GAEX2oB,gBAAiB,SAAUZ,GAavB,QAAS/D,GAAOqQ,EAAcj1B,GACrBlF,EAAQm6B,EAAcjP,KACvB0D,EAAYlqB,SAAWy1B,EACvBvS,EAAUzV,OAAOlN,EAAmB2pB,EAAa1pB,IACjDgmB,EAAW9zB,KAAK+iC,IAjBX,GACTjgC,GAAUjB,KAAKiB,QACf20B,EAAiB30B,EAAQ20B,eACzBC,EAAiB50B,EAAQ40B,eACzBhqB,EAAW5K,EAAQ4K,SACnBonB,EAAU2B,EAAQ3B,UAClB0C,GACAzpB,UAAW+mB,EAAQpnB,EAAW,KAAO,MACrCM,QAAS8mB,EAAQpnB,EAAW,KAAO,MACnCA,SAAUA,GAEVomB,KACAtD,EAAY3uB,KAAK6zB,iBAcrB,OANIgC,GAAe7O,SACfhnB,KAAKihC,4BAA4BpQ,EAAQgF,GAEzCD,EAAe5O,SACfhnB,KAAKmhC,4BAA4BtQ,EAAQ+E,GAEtCjH,EAAU5I,UAErBkb,4BAA6B,SAAUv6B,EAAU4E,GAApB,GAOhB81B,GACD31B,EAPJwN,EAAMjZ,KAAKqhC,eACXn1B,EAAY+M,EAAI/M,UAChB+E,EAAOgI,EAAIhI,KACXuc,EAAQxtB,KACR8gC,EAAStT,EAAMsT,OACfC,EAASvT,EAAMuT,MACnB,KAASK,EAAQ35B,KAAKC,KAAKo5B,GAAUx1B,EAAYmnB,KAAM2O,GAASL,EAAQK,GAAS91B,EAAY2F,KACrFxF,EAAW5H,GAAMqI,EAAY+E,GAAQmwB,EAAQN,GAASj4B,IAC1DnC,EAAS+E,EAAUH,IAG3B61B,4BAA6B,SAAUz6B,EAAU4E,GAApB,GAchB81B,GACDE,EACKv+B,EACD1C,EAKIoL,EArBZgU,EAASzf,KACTiZ,EAAMjZ,KAAKiB,QACXkH,EAAM8Q,EAAI9Q,IACVE,EAAM4Q,EAAI5Q,IACVoM,EAAYwE,EAAIxE,UAChBiB,EAAOuD,EAAIpF,UACX2Z,EAAQxtB,KAAKqhC,eACbn1B,EAAYshB,EAAMthB,UAClB+E,EAAOuc,EAAMvc,KACbswB,EAAQvhC,KACR8gC,EAASS,EAAMT,OACfC,EAASQ,EAAMR,OACf95B,EAAQQ,KAAKE,MAAMm5B,EACvB,KAASM,EAAQn6B,EAAOm6B,EAAQL,EAAQK,IAEpC,IADIE,EAAe7hB,EAAO+hB,sBAAsBJ,GACvCr+B,EAAMuI,EAAYmnB,KAAM1vB,EAAM0R,IAC/BpU,EAAQihC,EAAajhC,MAAQ0C,EAAMu+B,EAAaG,YAChDphC,EAAQgI,IAFkCtF,GAAOuI,EAAY2F,KAK7D5Q,GAAS8H,IACLsD,EAAW5H,GAAMqI,EAAY+E,GAAQjI,GAAI3I,EAAOqV,GAAQorB,GAASj4B,IACrEnC,EAAS+E,EAAUH,KAKnCwnB,gBAAiB,SAAUjjB,EAAO8iB,GAAjB,GACTyO,GAAQ35B,KAAKC,KAAK1H,KAAK8gC,OAASjxB,GAChCxP,EAAQoH,KAAKsB,IAAI/I,KAAKiB,QAAQ4S,UAAWutB,GACzC1jC,EAAOsC,KAAKw3B,cAAcn3B,EAAO,KAAMsyB,EAC3C,OAAO,IAAI7X,IAAUza,EAAO3C,EAAMmS,EAAO,KAAM8iB,IAEnD0E,iBAAkB,SAAUh3B,GACxB,GAAIsQ,GAAQ3Q,KAAK2Q,OACjB,OAAOA,GAAMxI,KAAO9H,GAASA,GAASsQ,EAAMtI,KAEhD6zB,IAAK,SAAU30B,GACX,GAAIoJ,GAAQ3Q,KAAK07B,eAAen0B,EAChC,OAAOvH,MAAKo4B,WAAWznB,EAAMxI,IAAKwI,EAAMtI,IAAKrI,KAAK67B,SAAU77B,KAAK87B,SAAUnrB,EAAMpN,SAErF44B,YAAa,SAAUl1B,EAAOC,GAAjB,GACLm0B,GAAar7B,KAAKqgC,SAASp5B,GAC3Bs5B,EAAWvgC,KAAKqgC,SAASn5B,GACzBiB,EAAMV,KAAKU,IAAIkzB,EAAYkF,GAC3Bl4B,EAAMZ,KAAKY,IAAIgzB,EAAYkF,EAC/B,QACIp4B,IAAKA,EACLE,IAAKA,IAGbszB,UAAW,SAAUp0B,GAAV,GACH0R,GAAMjZ,KACNiB,EAAUgY,EAAIhY,QACd46B,EAAW5iB,EAAI4iB,SACfC,EAAW7iB,EAAI6iB,SACf2E,EAAWzgC,KAAK+7B,WAAWx0B,GAC3BY,EAAMsR,GAAWgnB,EAASt4B,IAAK0zB,EAAUC,GACzCzzB,EAAMoR,GAAWgnB,EAASp4B,IAAKwzB,EAAUC,GACzCpmB,EAAOzU,EAAQ4S,UACf6tB,EAAqBr5B,EAAMF,GAAOlH,EAAQkH,KAAOlH,EAAQoH,KAAOxE,GAAMmF,GAAI/H,EAAQoH,IAAKqN,GAAQ1M,GAAI/H,EAAQkH,IAAKuN,GAAO7M,IAAqB,EAC5I84B,IAAmB1gC,EAAQkH,MAAQ0zB,GAAY56B,EAAQoH,MAAQyzB,IAAaj4B,GAAMmF,GAAIX,EAAKqN,GAAQ1M,GAAIb,EAAKuN,GAAO7M,KAAsB,CAC7I,IAAI64B,GAAsBC,EACtB,OACIx5B,IAAKA,EACLE,IAAKA,IAIjBm5B,sBAAuB,SAAUJ,GAAV,GACfnoB,GAAMjZ,KAAKiB,QACXwT,EAAYwE,EAAIxE,UAChBiB,EAAOuD,EAAIpF,UACXxT,EAAQoH,KAAKsB,IAAI2M,EAAM0rB,GACvBQ,EAAYn6B,KAAKsB,IAAI2M,EAAM0rB,EAAQ,GACnCS,EAAaD,EAAYvhC,EACzBohC,EAAYI,EAAaptB,CAC7B,QACIpU,MAAOA,EACPohC,UAAWA,IAGnBJ,aAAc,WAAA,GACNpoB,GAAMjZ,KAAKiB,QACX0yB,EAAU1a,EAAI0a,QACd9nB,EAAWoN,EAAIpN,SACfguB,EAAYhuB,EAAWsM,GAAID,GAC3B+a,EAAUjzB,KAAKizB,UACfiN,EAAMr0B,IAAa8nB,EAAU,KAC7BwM,EAAoB,IAARD,EAAY,EAAI,EAC5BF,EAAWn0B,EAAWonB,EAAQn0B,SAAWm0B,EAAQp0B,QACjDoS,EAAOivB,GAAOF,GAAYhgC,KAAK+gC,OAAS/gC,KAAK8gC,SAC7C50B,EAAY+mB,EAAQ4G,EAAYsG,EACpC,QACIlvB,KAAMA,EACN/E,UAAWA,EACX+mB,QAASA,MAqDrBprB,EAAkBiU,IACdhU,KAAM,MACN+L,UAAWgI,GACXpH,UAAW,EACXc,kBAAmB,EACnB1J,UAAU,EACVgqB,gBACI7O,SAAS,EACTnoB,MAAO,EACP+M,MAAO8K,IAEX+Q,OAAQ,EACR2K,cAAc,IAEdrW,IACAyZ,gBAAiB,SAAUZ,GAAV,GAKTkN,GAAaC,EAJb9gC,EAAUjB,KAAKiB,QACf8J,EAAStD,KAAKqB,IAAI9I,KAAKwB,IAAIsf,SAAS3c,EAAIywB,EAAQ3B,UAAU3oB,IAC1DwpB,KACAkO,GAAY,CAWhB,OATI/gC,GAAQ40B,eAAe7O,UACvB8a,EAAc9hC,KAAKiiC,oBAAoBrN,GACvCoN,GAAY,EACZlO,EAAY9zB,KAAKkiC,qBAAqBJ,EAAa/2B,EAAQ9J,EAAQ40B,iBAEnE50B,EAAQ20B,eAAe5O,UACvB+a,EAAc/hC,KAAKmiC,oBAAoBvN,EAASoN,GAChD9oB,GAAO4a,EAAW9zB,KAAKoiC,qBAAqBL,EAAah3B,EAAQ9J,EAAQ20B,eAAgBhB,EAASoN,KAE/FlO,GAEXoO,qBAAsB,SAAUjd,EAAQla,EAAQ9J,GAC5C,MAAOjB,MAAKqiC,gBAAgBpd,EAAQla,EAAQ9J,IAEhDmhC,qBAAsB,SAAUnd,EAAQla,EAAQ9J,EAAS2zB,EAASoN,GAC9D,GAAIM,GAAiBtiC,KAAKsiC,gBAAkBtiC,KAAKsiC,eAAev3B,EAAQ6pB,EAASoN,EACjF,OAAOhiC,MAAKqiC,gBAAgBpd,EAAQla,EAAQ9J,EAASqhC,IAEzDD,gBAAiB,SAAUpd,EAAQla,EAAQ9J,EAASqhC,GAAnC,GAcJ9jC,GACD4N,EAdJnN,GACAuE,QACI3E,MAAOoC,EAAQpC,MACf+M,MAAO3K,EAAQ2K,MACfS,SAAUpL,EAAQoL,WAGtByU,EAAS9gB,KAAKwB,IAAIsf,SAClByhB,EAAS,GAAIpsB,KACb2K,EAAO5c,EACP4c,EAAO3c,GACR4G,GACC4jB,EAAY3uB,KAAK6zB,iBACrB,KAASr1B,EAAI,EAAGA,EAAIymB,EAAOvmB,OAAQF,IAC3B4N,EAAO,GAAIT,IAAK1M,GAChBqjC,IACAC,EAAOx3B,OAASu3B,EAAerd,EAAOzmB,KAE1C4N,EAAKN,OAAOy2B,EAAOzhB,QAAQ/U,OAAOw2B,EAAOzc,QAAQb,EAAOzmB,GAAK,MAC7DmwB,EAAUzV,OAAO9M,EAErB,OAAOuiB,GAAU5I,UAErByc,eAAgB,SAAU5N,EAAS70B,EAAM0yB,EAAMxhB,EAAMwxB,GAArC,GACRhjB,GAASzf,KACT0iC,EAAO1iC,KAAK2iC,UAAU5iC,EAAM0yB,EAAMxhB,EAAMwxB,GACxCxhC,EAAU2zB,EAAQ3zB,QAClB2hC,EAAiB3hC,EAAQ+lB,UAAY/lB,EAAQmL,UAAY4a,WAAY,CACzE,OAAO1mB,GAAIoiC,EAAM,SAAUz4B,GACvB,GAAI44B,GAAQpjB,EAAOqjB,cAAc74B,EACjC,KAAK24B,GAA4B,KAAVC,EACnB,MAAOA,OAKnB7mB,GAAoBX,GAAaxb,QACjC8Q,MAAO,WACH,OACIxI,IAAK,EACLE,IAAKrI,KAAKiB,QAAQyP,WAAWhS,SAGrCunB,OAAQ,SAAUzkB,GACdxB,KAAKwB,IAAMA,EACXxB,KAAK+iC,gBAET9P,QAAS,WACL,MAAOjzB,MAAKwB,KAEhBuhC,aAAc,WAAA,GAQDvkC,GAEDo4B,EATJnX,EAASzf,KACTiZ,EAAMjZ,KACN4xB,EAAS3Y,EAAI2Y,OACbe,EAAe1Z,EAAIhY,QAAQ2wB,OAC3Ba,EAAOE,EAAaF,MAAQ,EAC5BxhB,EAAO0hB,EAAa1hB,MAAQ,EAC5B/R,EAAa,GAAIuL,GACrB,KAASjM,EAAI,EAAGA,EAAIozB,EAAOlzB,OAAQF,IAC/BozB,EAAOpzB,GAAGynB,OAAO/mB,GACb03B,EAAWhF,EAAOpzB,GAAGgD,IACzBowB,EAAOpzB,GAAGynB,OAAOxG,EAAO4V,QAAQ5C,EAAOj0B,EAAIyS,GAAM8S,YAAY,EAAG6S,EAAS/3B,QAAS+3B,EAAS93B,YAGnG6jC,UAAW,SAAU5iC,EAAMijC,EAAYC,EAAYR,GAAxC,GAIHxhC,GACAyP,EACAwyB,EACAC,EACA1Q,EACAxhB,EACAyxB,EACAt5B,EACK5K,CAAT,KAXmB,SAAfikC,IACAA,GAAa,GAEbxhC,EAAUjB,KAAKiB,QACfyP,EAAazP,EAAQyP,WAAWhS,OAChCwkC,EAAWxyB,EAAa3Q,GAAQ,EAChCojC,EAAW,IAAMD,EACjBzQ,EAAOuQ,GAAc,EACrB/xB,EAAOgyB,GAAc,EACrBP,KACAt5B,EAAQ,EACH5K,EAAIi0B,EAAMj0B,EAAI0kC,EAAU1kC,GAAKyS,EAE9B7H,EADAnI,EAAQ0yB,QACA,IAAMn1B,EAAI2kC,EAEV3kC,EAAI2kC,EAEhB/5B,EAAQvF,GAAMuF,EAAOhC,IAAmB,IAClCq7B,GAAc17B,EAAQqC,EAAOq5B,IAC/BC,EAAKvkC,KAAKiL,EAGlB,OAAOs5B,IAEXU,eAAgB,WACZ,MAAOpjC,MAAK2iC,UAAU,IAE1BU,eAAgB,WACZ,MAAOrjC,MAAK2iC,UAAU,KAE1BG,cAAe,SAAUQ,GACrB,OAAQ,IAAMA,EAAWtjC,KAAKiB,QAAQuiB,YAAc,KAExDse,YAAa,WACT,GAAIriB,GAASzf,IACb,OAAOM,GAAIN,KAAKojC,iBAAkB,SAAUE,GACxC,MAAO7jB,GAAOqjB,cAAcQ,MAGpChS,WAAY,WACR,UAEJ2Q,oBAAqB,SAAUrN,GAC3B,GAAIiB,GAAiB71B,KAAKiB,QAAQ40B,cAClC,OAAO71B,MAAKwiC,eAAe5N,EAAS,EAAGiB,EAAepD,KAAMoD,EAAe5kB,OAE/EkxB,oBAAqB,SAAUvN,EAASoN,GAAnB,GACb/oB,GAAMjZ,KAAKiB,QACX20B,EAAiB3c,EAAI2c,eACrBC,EAAiB5c,EAAI4c,eACrBoM,EAAsBD,EAAYhiC,KAAK2iC,UAAU,EAAG9M,EAAepD,KAAMoD,EAAe5kB,MAAQ,IACpG,OAAOjR,MAAKwiC,eAAe5N,EAAS,GAAKgB,EAAenD,KAAMmD,EAAe3kB,KAAMgxB,IAEvFK,eAAgB,SAAUv3B,EAAQ6pB,EAASoN,GAA3B,GAEJuB,GACAC,EACA1B,EACAQ,CAJR,IAAI1N,EAAQ3zB,QAAQ6G,OAAS0O,GAUzB,MATI+sB,GAAaj6B,GAAI,KAAwC,EAAjCtJ,KAAKiB,QAAQyP,WAAWhS,SAChD8kC,EAAc/7B,KAAK+B,IAAI+5B,GAAcx4B,EACrC+2B,EAAc9hC,KAAK8hC,cACnBQ,EAAiB,SAAUl5B,GAC3B,OAAK44B,GAAaj7B,EAAQqC,EAAO04B,GACtB/2B,EAEJy4B,IAKnB5P,gBAAiB,WAAA,GAIJp1B,GACDilC,EACArM,EACA4D,EACA5lB,EAEAC,EAEAquB,EAXJjkB,EAASzf,KACTg1B,EAAYh1B,KAAKiB,QAAQ+zB,cACzB/H,EAAQjtB,KAAKk1B,eAAiB,GAAIlf,KAAQyR,WAC9C,KAASjpB,EAAI,EAAGA,EAAIw2B,EAAUt2B,OAAQF,IAC9BilC,EAAOzO,EAAUx2B,GACjB44B,EAAO3X,EAAOkkB,aAAaF,GAC3BzI,EAAavb,EAAO4V,QAAQoO,EAAKnO,MACjClgB,EAAOquB,EAAKnO,KAAO7tB,KAAKE,MAAM87B,EAAKnO,MACvC8B,EAAK5T,YAAcpO,EAAO4lB,EAAW5xB,MACjCiM,EAAO5N,KAAKC,KAAK+7B,EAAKlO,IAAMkO,EAAKlO,GACrC6B,EAAKhuB,QAAUiM,EAAOD,GAAQ4lB,EAAW5xB,MACrCs6B,EAAO1pB,GAAa5a,QAAQkmB,WAAW8R,GACvClO,MACItd,MAAO63B,EAAK73B,MACZud,QAASsa,EAAKta,SAElB3lB,QAAU2lB,QAASsa,EAAKta,WAE5B8D,EAAM/T,OAAOwqB,EAEjB1jC,MAAKwnB,aAAayF,IAEtB0W,aAAc,SAAUF,GACpB,MAAOzjC,MAAKq1B,QAAQoO,EAAKnO,KAAMmO,EAAKlO,GAAK,IAE7CF,QAAS,SAAUC,EAAMC,GAAhB,GAYDqO,GAOAC,EACAC,EACA16B,EApBAnI,EAAUjB,KAAKiB,QACfs3B,EAAYt3B,EAAQs3B,UACpB/2B,EAAMxB,KAAKwB,IACXkhC,EAAO1iC,KAAK8hC,cACZiC,EAAYrB,EAAKhkC,OACjBslC,EAAY,IAAMD,EAClBE,EAAY3O,CAehB,OAdIr0B,GAAQ0yB,UAAY4E,IACpB0L,GAAaA,EAAY,GAAKF,GAElCE,EAAYxqB,GAAWhS,KAAKE,MAAMs8B,GAAY,EAAGF,EAAY,GACzDH,EAAYlB,EAAKuB,GACjB1L,IACAqL,GAAwBI,EAAY,EAChCJ,EAAY,IACZA,GAAa,MAGjBC,EAAUpqB,GAAWhS,KAAKC,KAAK6tB,GAAM0O,GAAYA,EAAWF,EAAY,GACxED,EAAQD,EAAUI,EAAY,EAC9B76B,EAAQ46B,EAAYF,EACjB,GAAIjqB,IAAKrY,EAAIsf,SAAU,EAAGtf,EAAI1C,SAAW,EAAG8kC,EAAWx6B,IAElEguB,KAAM,SAAU9B,EAAMC,GAAhB,GACE6B,GAAOp3B,KAAKq1B,QAAQC,EAAMC,GAC1B/R,EAAa4T,EAAK5T,WAAa,IAC/BiB,EAAWjB,EAAa4T,EAAKhuB,KACjC,OAAO,IAAI6M,IAASwP,KAChB2R,EAAKtW,OAAO5c,EACZkzB,EAAKtW,OAAO3c,IAEZqf,WAAYA,EACZiB,SAAUA,EACViB,QAAS0R,EAAKrsB,OACd4a,QAASyR,EAAKrsB,UAGtBqwB,mBAAoB,SAAUxa,GAAV,GAIPpiB,GACD44B,EAJJ3X,EAASzf,KACTtB,EAASsB,KAAKiB,QAAQyP,WAAWhS,OACjCmR,EAAQ,IACZ,KAASrR,EAAI,EAAGA,EAAIE,EAAQF,IAExB,GADI44B,EAAO3X,EAAO4V,QAAQ72B,GACtB44B,EAAK5U,cAAc5B,GAAQ,CAC3B/Q,EAAQrR,CACR,OAGR,MAAOqR,MAGfhI,EAAkBmU,IACdwH,WAAY,GACZoO,QAAU5H,OAAQzkB,EAAW,KAC7BswB,gBAAkB7O,SAAS,GAC3BuR,WAAW,IAEfn2B,GAAW4Z,GAAkBhU,UAAW+T,IACpCE,GAAYf,GAAKrb,QACjBC,KAAM,SAAUmB,EAASylB,GACrBxL,GAAK+C,GAAGne,KAAKulB,KAAKrlB,KAAMiB,EAASylB,EACjC,IAAIwd,GAAkBlkC,KAAKiB,OAC3BijC,GAAgBzvB,UAAYyvB,EAAgBzvB,WAAayvB,EAAgBrwB,UAAY,GAEzFisB,aAAc,SAAUC,GACpB,MAAOpkB,IAAY3T,UAAU83B,aAAaza,KAAKrlB,KAAM+/B,GAAa,GAEtE9Z,OAAQ,SAAUzkB,GACdxB,KAAKwB,IAAMA,EACXxB,KAAK+iC,gBAETA,aAAc,WAAA,GAUDvkC,GAEDo4B,EAXJnX,EAASzf,KACTiZ,EAAMjZ,KACNiB,EAAUgY,EAAIhY,QACd2wB,EAAS3Y,EAAI2Y,OACbe,EAAe1Z,EAAIhY,QAAQ2wB,OAC3Ba,EAAOE,EAAaF,MAAQ,EAC5BxhB,EAAO0hB,EAAa1hB,MAAQ,EAC5B/R,EAAa,GAAIuL,IACjBi4B,EAAO1iC,KAAK2iC,UAAU1hC,EAAQ4S,UAAW4e,EAAMxhB,EACnD,KAASzS,EAAI,EAAGA,EAAIozB,EAAOlzB,OAAQF,IAC/BozB,EAAOpzB,GAAGynB,OAAO/mB,GACb03B,EAAWhF,EAAOpzB,GAAGgD,IACzBowB,EAAOpzB,GAAGynB,OAAOxG,EAAO4V,QAAQqN,EAAKlkC,IAAIulB,YAAY,EAAG6S,EAAS/3B,QAAS+3B,EAAS93B,YAG3Fm0B,QAAS,WACL,MAAOjzB,MAAKwB,KAEhBmhC,UAAW,SAAU5iC,EAAMijC,EAAYC,EAAYR,GAAxC,GAIHt6B,GACA83B,EACAyC,EACAjQ,EACAxhB,EACKzS,EACDY,CADR,KARmB,SAAfqjC,IACAA,GAAa,GAEbt6B,EAAMnI,KAAKiB,QAAQkH,IACnB83B,EAAYjgC,KAAK8/B,aAAa//B,GAC9B2iC,KACAjQ,EAAOuQ,GAAc,EACrB/xB,EAAOgyB,GAAc,EAChBzkC,EAAIi0B,EAAMj0B,EAAIyhC,EAAWzhC,GAAKyS,EAC/B7R,GAAW,IAAM+I,EAAM3J,EAAIuB,GAAQ,IACjC0iC,GAAc17B,EAAQ3H,EAASqjC,IACjCC,EAAKvkC,KAAKiB,EAGlB,OAAOsjC,IAEXU,eAAgB,WACZ,MAAOpjC,MAAK2iC,UAAU3iC,KAAKiB,QAAQ4S,YAEvCwvB,eAAgB,WACZ,MAAOrjC,MAAK2iC,UAAU3iC,KAAKiB,QAAQwT,YAEvCquB,cAAe,SAAUtkC,GACrB,OAAQ,IAAMA,EAAIwB,KAAKiB,QAAQuiB,YAAc,KAEjD8N,WAAY,WACR,UAEJ2Q,oBAAqB,SAAUrN,GAC3B,GAAIiB,GAAiB71B,KAAKiB,QAAQ40B,cAClC,OAAO71B,MAAKwiC,eAAe5N,EAAS50B,KAAKiB,QAAQ4S,UAAWgiB,EAAepD,KAAMoD,EAAe5kB,OAEpGkxB,oBAAqB,SAAUvN,EAASoN,GAAnB,GACb/gC,GAAUjB,KAAKiB,QACf20B,EAAiB30B,EAAQ20B,eACzBC,EAAiB50B,EAAQ40B,eACzBoM,EAAsBD,EAAYhiC,KAAK2iC,UAAU1hC,EAAQ4S,UAAWgiB,EAAepD,KAAMoD,EAAe5kB,MAAQ,IACpH,OAAOjR,MAAKwiC,eAAe5N,EAAS3zB,EAAQwT,UAAWmhB,EAAenD,KAAMmD,EAAe3kB,KAAMgxB,IAErG0B,aAAc,SAAUF,GACpB,MAAOzjC,MAAKq1B,QAAQoO,EAAKnO,KAAMmO,EAAKlO,KAExCF,QAAS,SAAU1rB,EAAGC,GAAb,GAcGu6B,GAbJlrB,EAAMjZ,KACNiB,EAAUgY,EAAIhY,QACdO,EAAMyX,EAAIzX,IACVgiB,EAAaviB,EAAQuiB,WACrBvc,EAAQwS,GAAW9P,EAAG1I,EAAQkH,IAAKlH,EAAQoH,KAC3CnB,EAAMuS,GAAW7P,GAAK3C,EAAOA,EAAOhG,EAAQoH,IAYhD,OAXIpH,GAAQ0yB,UACR1sB,MACAC,OAEJD,GAAS,IAAMA,EAAQuc,GAAc,IACrCtc,GAAO,IAAMA,EAAMsc,GAAc,IAC7Btc,EAAMD,IACFk9B,EAAMl9B,EACVA,EAAQC,EACRA,EAAMi9B,GAEH,GAAItqB,IAAKrY,EAAIsf,SAAU,EAAGtf,EAAI1C,SAAW,EAAGmI,EAAOC,EAAMD,IAEpEmwB,KAAM,SAAU9B,EAAMC,GAAhB,GAIEt0B,GACAgG,EACAmwB,EACAjvB,EACAE,EACAmb,EAAYiB,CAUhB,OAlBW,UAAP8Q,IACAA,EAAKD,GAELr0B,EAAUjB,KAAKiB,QACfgG,EAAQ,IAAMhG,EAAQuiB,WACtB4T,EAAOp3B,KAAKq1B,QAAQC,EAAMC,GAC1BptB,EAAMV,KAAKU,IAAImtB,EAAMC,GACrBltB,EAAMZ,KAAKY,IAAIitB,EAAMC,GAErBt0B,EAAQ0yB,SACRnQ,EAAarb,EACbsc,EAAWpc,IAEXmb,EAAa,IAAMnb,EACnBoc,EAAW,IAAMtc,GAErBqb,GAAcA,EAAavc,GAAS,IACpCwd,GAAYA,EAAWxd,GAAS,IACzB,GAAIgP,IAASwP,KAChB2R,EAAKtW,OAAO5c,EACZkzB,EAAKtW,OAAO3c,IAEZqf,WAAYA,EACZiB,SAAUA,EACViB,QAAS0R,EAAKrsB,OACd4a,QAASyR,EAAKrsB,UAGtBs1B,SAAU,SAAUzf,GAAV,GACF3f,GAAUjB,KAAKiB,QACf6f,EAAS9gB,KAAKwB,IAAIsf,SAClBM,EAAKR,EAAM1c,EAAI4c,EAAO5c,EACtBmd,EAAKT,EAAMzc,EAAI2c,EAAO3c,EACtBkF,EAAQ5B,KAAK5D,MAAMuV,GAAI3R,KAAK28B,MAAM/iB,EAAID,KACtCna,EAAQhG,EAAQuiB,UAKpB,OAJKviB,GAAQ0yB,UACTtqB,MACApC,QAEIoC,EAAQpC,EAAQ,KAAO,KAEnCqxB,WAAY,WACR,OACInwB,IAAK,EACLE,IAAe,EAAVZ,KAAK48B,OAItBx8B,EAAkBoU,IACdnU,KAAM,QACN0b,WAAY,EACZmQ,SAAS,EACT9f,UAAW,GACX1L,IAAK,EACLE,IAAK,IACLupB,QAAU5H,OAAQzkB,EAAW,KAC7BswB,gBACIjqB,MAAO8K,GACPsQ,SAAS,EACTnoB,MAAO,GAEX+2B,gBAAkBhqB,MAAO,UAE7BxJ,GAAW6Z,GAAUjU,UAAW+T,IAC5B6X,gBAAiB5X,GAAkBhU,UAAU4rB,gBAC7CkO,YAAa9lB,GAAkBhU,UAAU85B,YACzCnxB,MAAOgL,GAAY3T,UAAU2I,MAC7B+hB,YAAa/W,GAAY3T,UAAU0qB,YACnCI,gBAAiBnX,GAAY3T,UAAU8qB,kBAEvC5W,IACAjb,SAAW40B,gBAAkB7O,SAAS,IACtC4M,gBAAiB,WAAA,GAQTgB,GACAkN,EACAhhB,EACAmM,EACKzuB,EACDilC,EACAa,EAOAlN,EACAsM,EACAa,EAtBJ9kB,EAASzf,KACTiZ,EAAMjZ,KAAKiB,QACX6G,EAAOmR,EAAI4c,eAAe/tB,KAC1BktB,EAAY/b,EAAI+b,SAQpB,KAPkB,SAAdA,IACAA,MAEAJ,EAAU50B,KAAKi1B,SAASuP,UACxB1C,EAAclN,EAAQkN,cACtBhhB,EAAS8T,EAAQpzB,IAAIsf,SACrBmM,EAAQjtB,KAAKk1B,eAAiB,GAAIlf,KAAQyR,YACrCjpB,EAAI,EAAGA,EAAIw2B,EAAUt2B,OAAQF,IAC9BilC,EAAOzO,EAAUx2B,GACjB8lC,GACApb,MACItd,MAAO63B,EAAK73B,MACZud,QAASsa,EAAKta,SAElB3lB,QAAU2lB,QAASsa,EAAKta,UAExBiO,EAAO3X,EAAO4V,QAAQoO,EAAKnO,KAAMmO,EAAKlO,IAAI,GAC1CmO,EAAO,GAAI7pB,IAAKiH,EAAQA,EAAO3c,EAAIizB,EAAK5sB,GAAIsW,EAAO3c,EAAIizB,EAAK9sB,GAAI,EAAG,KACnEi6B,EAAQ,OAERA,EADAz8B,IAAS0O,GACDwD,GAAa5a,QAAQkmB,WAAWoe,EAAMY,GAEtC34B,GAAKsf,WAAWxL,EAAOglB,eAAef,EAAM5B,GAAcwC,GAAWze,QAEjFoH,EAAM/T,OAAOqrB,EAEjBvkC,MAAKwnB,aAAayF,IAEtBwX,eAAgB,SAAUf,EAAMze,GAAhB,GASHzmB,GARLkmC,KACAC,KACA7jB,GACA4iB,EAAK5iB,OAAO5c,EACZw/B,EAAK5iB,OAAO3c,GAEZygC,EAAc,GAAIzuB,IAAO2K,EAAQ4iB,EAAK54B,aACtC+5B,EAAc,GAAI1uB,IAAO2K,EAAQ4iB,EAAK34B,OAC1C,KAASvM,EAAI,EAAGA,EAAIymB,EAAOvmB,OAAQF,IAC/BkmC,EAAYvmC,KAAKymC,EAAY9e,QAAQb,EAAOzmB,GAAK,MACjDmmC,EAAYxmC,KAAK0mC,EAAY/e,QAAQb,EAAOzmB,GAAK,KAKrD,OAHAkmC,GAAY/Q,UACZ+Q,EAAYvmC,KAAKumC,EAAY,IAC7BC,EAAYxmC,KAAKwmC,EAAY,IACtBA,EAAYhnB,OAAO+mB,IAE9BlP,gBAAiB,SAAUZ,GAAV,GAUL7C,GATJ9wB,EAAUjB,KAAKiB,QACfgxB,EAAajyB,KAAK8kC,8BAClBhD,EAAclN,EAAQkN,cACtBhhB,EAAS8T,EAAQpzB,IAAIsf,SACrBgT,IAQJ,OAPI7yB,GAAQ40B,eAAe7O,UACvB8M,EAAY9zB,KAAKqiC,gBAAgBvhB,EAAQmR,EAAY6P,EAAa7gC,EAAQ40B,iBAE1E50B,EAAQ20B,eAAe5O,UACnB+K,EAAa/xB,KAAK+kC,8BACtB7rB,GAAO4a,EAAW9zB,KAAKqiC,gBAAgBvhB,EAAQiR,EAAY+P,EAAa7gC,EAAQ20B,kBAE7E9B,GAEXuO,gBAAiB,SAAUvhB,EAAQjU,EAAOoY,EAAQhkB,GAAjC,GAYTgQ,GAIA0d,EACK+H,EACDsO,EAEIzC,EAOIn2B,EACK64B,EA3BjBhmC,GACAuE,QACI3E,MAAOoC,EAAQpC,MACf+M,MAAO3K,EAAQ2K,MACfS,SAAUpL,EAAQoL,WAGtBomB,EAAOxxB,EAAQwxB,IASnB,KARa,SAATA,IACAA,EAAO,GAEPxhB,EAAOhQ,EAAQgQ,KACN,SAATA,IACAA,EAAO,GAEP0d,EAAY3uB,KAAK6zB,kBACZ6C,EAASjE,EAAMiE,EAAS7pB,EAAMnO,OAAQg4B,GAAUzlB,EAErD,GADI+zB,EAAalkB,EAAO3c,EAAI0I,EAAM6pB,GAC9BsO,EAAa,EAKb,GAJIzC,EAAS,GAAIpsB,KACb2K,EAAO5c,EACP4c,EAAO3c,GACR6gC,GACC/jC,EAAQ6G,OAAS0O,GACjBmY,EAAUzV,OAAO,GAAIzV,IAAQ0S,OAAOosB,EAAQtjC,QACzC,CAEH,IADImN,EAAO,GAAIT,IAAK1M,GACXgmC,EAAU,EAAGA,EAAUhgB,EAAOvmB,OAAQumC,IAC3C74B,EAAKL,OAAOw2B,EAAOzc,QAAQb,EAAOggB,GAAW,KAEjD74B,GAAKyZ,QACL8I,EAAUzV,OAAO9M,GAI7B,MAAOuiB,GAAU5I,UAErBsa,SAAU,SAAUzf,GAAV,GAQEQ,GACAC,EACAhY,EAIA67B,EACArC,EACAsC,EACAC,EAhBJnS,EAAUjzB,KAAKizB,UACf2B,EAAU50B,KAAKi1B,SAASuP,UACxB1C,EAAclN,EAAQkN,cACtBhhB,EAAS8T,EAAQpzB,IAAIsf,SACrB/V,EAAS6V,EAAMO,WAAWL,GAC1BkD,EAAWjZ,CAcf,OAbI/K,MAAKiB,QAAQ40B,eAAe/tB,OAAS0O,IAAOsrB,EAAYpjC,OAAS,IAC7D0iB,EAAKR,EAAM1c,EAAI4c,EAAO5c,EACtBmd,EAAKT,EAAMzc,EAAI2c,EAAO3c,EACtBkF,GAAS+P,GAAI3R,KAAK28B,MAAM/iB,EAAID,IAAO,KAAO,IAC9C0gB,EAAY1jC,KAAK,SAAUuL,EAAGC,GAC1B,MAAOkM,IAAgBnM,EAAGN,GAASyM,GAAgBlM,EAAGP,KAEtD67B,EAAWpvB,GAAgBgsB,EAAY,GAAIA,EAAY,IAAM,EAC7De,EAAQ/sB,GAAgBzM,EAAOy4B,EAAY,IAC3CqD,EAAQ,GAAKD,EACbE,EAAO,IAAMvC,EAAQsC,EACzBnhB,EAAWjZ,GAAUtD,KAAKgC,IAAIH,GAAI87B,IAAS39B,KAAKgC,IAAIH,GAAI67B,MAErDnlC,KAAKqlC,WAAWr9B,UAAUq4B,SAAShb,KAAKrlB,KAAM,GAAIuJ,IAAM0pB,EAAQ5oB,GAAI4oB,EAAQzoB,GAAKwZ,MAM5F7H,GAAmBR,GAAY9b,QAC/BilC,4BAA6B,WACzB,MAAO9kC,MAAKo6B,iBAAiBp6B,KAAKiB,QAAQ4S,YAE9CkxB,4BAA6B,WAAA,GACrB9jC,GAAUjB,KAAKiB,QACfqkC,EAAgB,CAIpB,OAHIrkC,GAAQ40B,eAAe7O,UACvBse,EAAgBrkC,EAAQ4S,WAErB7T,KAAKo6B,iBAAiBn5B,EAAQwT,UAAW6wB,IAEpDD,SAAU,WACN,MAAO1pB,OAGfvZ,GAAW+Z,GAAiBnU,UAAWkU,IACnCE,GAAuBN,GAAgBjc,QACvCilC,4BAA6B,WACzB,GAAIzK,KAIJ,OAHAr6B,MAAKihC,4BAA4B,SAAUx1B,GACvC4uB,EAAUl8B,KAAKsN,IAChBzL,KAAKiB,QAAQ40B,gBACTwE,GAEX0K,4BAA6B,WACzB,GAAI1K,KAIJ,OAHAr6B,MAAKmhC,4BAA4B,SAAU11B,GACvC4uB,EAAUl8B,KAAKsN,IAChBzL,KAAKiB,QAAQ20B,gBACTyE,GAEXgL,SAAU,WACN,MAAOvpB,OAGf1Z,GAAWga,GAAqBpU,UAAWkU,IACvCG,GAAS,KACTC,GAA6B,IAC7BC,GAAiB3c,GAAMC,QACvBC,KAAM,SAAUgqB,GACZ9pB,KAAK8pB,OAASA,GAElByb,QAAS,SAAUC,GAAV,GAaDC,GACA5K,EACAC,EAQI4K,EAKJC,EAAqBC,EAKjBC,EAIAC,EAGJC,EACKhjC,EAOGijC,EAGAC,EAQJC,EAMAC,EAhEJ1mB,EAASzf,KACTyiB,EAAS+iB,EAAW3zB,MAAM,GAC1BlO,KACAmmB,EAAS9pB,KAAK8pB,OACdprB,EAAS+jB,EAAO/jB,MAKpB,IAJIA,EAAS,IACTsB,KAAKomC,iBAAiB,EAAG3jB,GACzB/jB,EAAS+jB,EAAO/jB,QAEhBA,EAAS,GAAgB,IAAXA,GAAgB+jB,EAAO,GAAG9B,OAAO8B,EAAO,IACtD,MAAO9e,EAMX,KAJI8hC,EAAKhjB,EAAO,GACZoY,EAAKpY,EAAO,GACZqY,EAAKrY,EAAO,GAChB9e,EAASxF,KAAK,GAAIkY,IAAQovB,IACnBA,EAAG9kB,OAAO8B,EAAO/jB,EAAS,KAC7BorB,GAAS,EACTrH,EAAO4jB,MACP3nC,GAEJ,IAAe,IAAXA,EAIA,MAHIgnC,GAAU1lC,KAAK0lC,QAAQD,EAAI5K,EAAI3iB,GAAGC,IACtCpG,GAAKpO,GAAU2iC,WAAWtmC,KAAKumC,kBAAkBb,EAASD,EAAI5K,EAAI3iB,GAAGC,KACrExU,EAASxF,KAAK,GAAIkY,IAAQwkB,EAAI76B,KAAKwmC,mBAAmBd,EAASD,EAAI5K,EAAI3iB,GAAGC,MACnExU,CAeX,KAZImmB,GACA2b,EAAKhjB,EAAO/jB,EAAS,GACrBm8B,EAAKpY,EAAO,GACZqY,EAAKrY,EAAO,GACRojB,EAAgB7lC,KAAK6lC,cAAcJ,EAAI5K,EAAIC,GAC/C6K,EAAsBE,EAAc,GACpCD,EAAmBC,EAAc,KAE7BC,EAAY9lC,KAAK0lC,QAAQD,EAAI5K,EAAI3iB,GAAGC,IACxCwtB,EAAsB3lC,KAAKumC,kBAAkBT,EAAWL,EAAI5K,EAAI3iB,GAAGC,KAEnE4tB,EAAMJ,EACD5iC,EAAM,EAAGA,GAAOrE,EAAS,EAAGqE,IACjC0c,EAAO2mB,iBAAiBrjC,EAAK0f,GAC7B/jB,EAAS+jB,EAAO/jB,OACZqE,EAAM,GAAKrE,IACX+mC,EAAKhjB,EAAO1f,GACZ83B,EAAKpY,EAAO1f,EAAM,GAClB+3B,EAAKrY,EAAO1f,EAAM,GACdijC,EAAkBvmB,EAAOomB,cAAcJ,EAAI5K,EAAIC,GACnD/oB,GAAKpO,GAAU2iC,WAAWP,GAC1BA,EAAMC,EAAgB,GAClBC,EAAMD,EAAgB,GAC1BriC,EAASxF,KAAK,GAAIkY,IAAQwkB,EAAIoL,IAiBtC,OAdInc,IACA2b,EAAKhjB,EAAO/jB,EAAS,GACrBm8B,EAAKpY,EAAO/jB,EAAS,GACrBo8B,EAAKrY,EAAO,GACRyjB,EAAkBlmC,KAAK6lC,cAAcJ,EAAI5K,EAAIC,GACjD/oB,GAAKpO,GAAU2iC,WAAWP,GAC1BpiC,EAASxF,KAAK,GAAIkY,IAAQwkB,EAAIqL,EAAgB,KAC9Cn0B,GAAKpO,GAAU2iC,WAAWJ,EAAgB,IAC1CviC,EAASxF,KAAK,GAAIkY,IAAQykB,EAAI8K,MAE1BO,EAAYnmC,KAAK0lC,QAAQ7K,EAAIC,EAAI5iB,GAAGC,IACxCpG,GAAKpO,GAAU2iC,WAAWP,GAC1BpiC,EAASxF,KAAK,GAAIkY,IAAQykB,EAAI96B,KAAKwmC,mBAAmBL,EAAWtL,EAAIC,EAAI5iB,GAAGC,OAEzExU,GAEXyiC,iBAAkB,SAAUrjC,EAAK0f,GAC7B,KAAOA,EAAO1f,EAAM,KAAO0f,EAAO1f,GAAK4d,OAAO8B,EAAO1f,EAAM,KAAO0f,EAAO1f,EAAM,GAAG4d,OAAO8B,EAAO1f,EAAM,MAClG0f,EAAOmc,OAAO77B,EAAM,EAAG,IAG/B0jC,WAAY,SAAUhB,EAAI5K,EAAIC,GAAlB,GASA7c,GACAzT,EATJi8B,GAAa,CAcjB,OAbIhB,GAAGvhC,IAAM22B,EAAG32B,EACZuiC,GAAa,EACN5L,EAAG32B,IAAM42B,EAAG52B,GACf22B,EAAG12B,EAAI22B,EAAG32B,GAAKshC,EAAGthC,GAAK02B,EAAG12B,GAAK22B,EAAG32B,EAAI02B,EAAG12B,GAAK02B,EAAG12B,GAAKshC,EAAGthC,KACzDsiC,GAAa,IAGbxoB,EAAKje,KAAK0mC,aAAajB,EAAI5K,GAC3BrwB,EAAKxK,KAAK2mC,kBAAkB1oB,EAAI6c,EAAG52B,GACjCuhC,EAAGthC,GAAK02B,EAAG12B,GAAK22B,EAAG32B,GAAKqG,GAASqwB,EAAG12B,GAAKshC,EAAGthC,GAAK22B,EAAG32B,GAAKqG,IAC3Di8B,GAAa,IAGdA,GAEXG,OAAQ,SAAUnB,EAAI5K,EAAIC,GAAlB,GACA7c,GAAKje,KAAK0mC,aAAajB,EAAI5K,GAC3BrwB,EAAKxK,KAAK2mC,kBAAkB1oB,EAAI6c,EAAG52B,EACvC,OAAOuhC,GAAGvhC,IAAM22B,EAAG32B,GAAK22B,EAAG32B,IAAM42B,EAAG52B,GAAKL,GAAM2G,EAAI,KAAO3G,GAAMi3B,EAAG32B,EAAG,IAE1EuiC,aAAc,SAAU7L,EAAIC,GAAd,GACNnxB,IAAKmxB,EAAG32B,EAAI02B,EAAG12B,IAAM22B,EAAG52B,EAAI22B,EAAG32B,GAC/B0F,EAAIixB,EAAG12B,EAAIwF,EAAIkxB,EAAG32B,CACtB,QACI0F,EACAD,IAGRk8B,cAAe,SAAUJ,EAAI5K,EAAIC,GAAlB,GAKP4K,GAIImB,EAeQC,EAWZN,EAEIO,EAIJR,EAxCAS,EAAS9uB,GACT+uB,EAAS9uB,GACT+uB,GAAW,EACXC,GAAoB,CA0CxB,OAxCInnC,MAAK4mC,OAAOnB,EAAI5K,EAAIC,GACpB4K,EAAU1lC,KAAK0lC,QAAQD,EAAI5K,EAAI3iB,GAAGC,KAE9B0uB,GACA3iC,EAAGlE,KAAKonC,mBAAmB3B,EAAI5K,EAAIC,EAAI5iB,IACvC/T,EAAGnE,KAAKonC,mBAAmB3B,EAAI5K,EAAIC,EAAI3iB,KAEvC0uB,EAAU3iC,GAAK2iC,EAAU1iC,GACzBuhC,EAAU1lC,KAAK0lC,QAAQD,EAAI3K,EAAI5iB,GAAGC,IAClC+uB,GAAW,IAEPlnC,KAAKymC,WAAWhB,EAAI5K,EAAIC,KACxBkM,EAAS7uB,GACT8uB,EAAS/uB,IAET2uB,EAAUG,GACVtB,EAAU,GAINoB,EADAhM,EAAGmM,GAAUxB,EAAGwB,IAAWxB,EAAGwB,IAAWpM,EAAGoM,IAAWxB,EAAGwB,GAAUnM,EAAGmM,IAAWpM,EAAGoM,IAAWxB,EAAGwB,GAC5FlxB,IAAY+kB,EAAGmM,GAAUxB,EAAGwB,KAAYpM,EAAGmM,GAAUvB,EAAGuB,MAEvDjxB,IAAY+kB,EAAGkM,GAAUvB,EAAGuB,KAAYnM,EAAGoM,GAAUxB,EAAGwB,KAEpEvB,EAAUppB,GAA6BwqB,EACvCK,GAAoB,KAI5BX,EAAqBxmC,KAAKwmC,mBAAmBd,EAASD,EAAI5K,EAAImM,EAAQC,GACtEE,IACIJ,EAAYC,EAChBA,EAASC,EACTA,EAASF,GAETR,EAAoBvmC,KAAKumC,kBAAkBb,EAAS7K,EAAIC,EAAIkM,EAAQC,GACpEC,IACAlnC,KAAKqnC,qBAAqB5B,EAAI5K,EAAI2L,EAAoBd,GACtD1lC,KAAKqnC,qBAAqBxM,EAAIC,EAAIyL,EAAmBb,KAGrDc,EACAD,IAGRc,qBAAsB,SAAUxM,EAAIC,EAAIwM,EAAI5B,GACpC7K,EAAG12B,EAAI22B,EAAG32B,EACN22B,EAAG32B,EAAImjC,EAAGnjC,GACVmjC,EAAGpjC,EAAI22B,EAAG32B,GAAK42B,EAAG32B,EAAI02B,EAAG12B,GAAKuhC,EAC9B4B,EAAGnjC,EAAI22B,EAAG32B,GACHmjC,EAAGnjC,EAAI02B,EAAG12B,IACjBmjC,EAAGpjC,EAAI42B,EAAG52B,GAAK42B,EAAG32B,EAAI02B,EAAG12B,GAAKuhC,EAC9B4B,EAAGnjC,EAAI02B,EAAG12B,GAGVmjC,EAAGnjC,EAAI22B,EAAG32B,GACVmjC,EAAGpjC,EAAI22B,EAAG32B,GAAK22B,EAAG12B,EAAI22B,EAAG32B,GAAKuhC,EAC9B4B,EAAGnjC,EAAI22B,EAAG32B,GACH02B,EAAG12B,EAAImjC,EAAGnjC,IACjBmjC,EAAGpjC,EAAI42B,EAAG52B,GAAK22B,EAAG12B,EAAI22B,EAAG32B,GAAKuhC,EAC9B4B,EAAGnjC,EAAI02B,EAAG12B,IAItBuhC,QAAS,SAAUD,EAAI5K,EAAImM,EAAQC,GAA1B,GAGDvB,GAFAxhC,EAAI22B,EAAGmM,GAAUvB,EAAGuB,GACpB7iC,EAAI02B,EAAGoM,GAAUxB,EAAGwB,EAOxB,OAJIvB,GADM,IAANxhC,EACU,EAEAC,EAAID,GAItBkjC,mBAAoB,SAAU3B,EAAI5K,EAAIC,EAAIj2B,GACtC,MAAOi2B,GAAGj2B,GAASg2B,EAAGh2B,IAAUg2B,EAAGh2B,GAAS4gC,EAAG5gC,IAAUi2B,EAAGj2B,GAASg2B,EAAGh2B,IAAUg2B,EAAGh2B,GAAS4gC,EAAG5gC,IAErG0hC,kBAAmB,SAAUb,EAASD,EAAI8B,EAAIP,EAAQC,GAAnC,GACXO,GAAK/B,EAAGuB,GACRS,EAAKF,EAAGP,GACRhjB,GAAYyjB,EAAKD,GAAMnrB,EAC3B,OAAOrc,MAAK4gB,MAAM4mB,EAAKxjB,EAAUyhB,EAAGwB,GAAUjjB,EAAW0hB,EAASsB,EAAQC,IAE9ET,mBAAoB,SAAUd,EAASD,EAAI8B,EAAIP,EAAQC,GAAnC,GACZO,GAAK/B,EAAGuB,GACRS,EAAKF,EAAGP,GACRhjB,GAAYyjB,EAAKD,GAAMnrB,EAC3B,OAAOrc,MAAK4gB,MAAM6mB,EAAKzjB,EAAUujB,EAAGN,GAAUjjB,EAAW0hB,EAASsB,EAAQC,IAE9ErmB,MAAO,SAAU8mB,EAAQC,EAAQX,EAAQC,GACrC,GAAIW,GAAe,GAAI3xB,IAAS1M,KAGhC,OAFAq+B,GAAaZ,GAAUU,EACvBE,EAAaX,GAAUU,EAChBC,GAEXjB,kBAAmB,SAAU1oB,EAAI/Z,GAAd,GAGN1F,GAFLE,EAASuf,EAAGvf,OACZwG,EAAS,CACb,KAAS1G,EAAI,EAAGA,EAAIE,EAAQF,IACxB0G,GAAUuC,KAAKsB,IAAI7E,EAAG1F,GAAKyf,EAAGzf,EAElC,OAAO0G,MAMfoR,GAAQuxB,UAAYvtB,GACpB5a,MAAM0C,WAAW1C,MAAM4W,SACnB8B,UAAWA,GACXS,SAAUA,GACVrQ,cAAeA,EACfe,MAAOA,GACPkB,IAAKA,GACLoP,KAAMA,GACNC,OAAQA,GACRE,aAAcA,GACdG,aAAcA,GACdF,aAAcA,GACdC,WAAYA,GACZK,YAAaA,GACbC,aAAcA,GACdE,KAAMA,GACNE,QAASA,GACTC,MAAOA,GACPC,UAAWA,GACXI,KAAMA,GACND,KAAMA,GACNI,aAAcA,GACdI,iBAAkBA,GAClBG,cAAeA,GACfD,YAAaA,GACbG,gBAAiBA,GACjBG,UAAWA,GACXD,kBAAmBA,GACnBG,iBAAkBA,GAClBC,qBAAsBA,GACtBG,eAAgBA,GAChBtR,UAAWA,EACXrI,SAAUA,EACVO,YAAaA,EACbE,iBAAkBA,EAClBU,UAAWA,EACXS,oBAAqBA,EACrBpC,WAAYA,GACZ4C,cAAeA,EACfO,WAAYA,EACZW,YAAaA,EACbuW,OAAQlG,GACR/P,KAAMA,EACNG,WAAYA,EACZmS,QAASA,GACT/R,QAASA,EACTC,iBAAkBA,EAClBgS,iBAAkBA,GAClBtW,QAASA,EACT4D,WAAYA,GACZlC,SAAUA,EACVM,SAAUA,EACVJ,SAAUA,EACVhE,IAAKA,EACL+G,gBAAiBA,EACjBuS,WAAYA,GACZ/R,kBAAmBA,EACnBI,kBAAmBA,EACnBtD,WAAYA,EACZuU,OAAQA,GACRC,WAAYA,GACZvZ,MAAOA,GACP8D,QAASA,GACT0V,IAAKA,GACLC,cAAeA,GACfC,YAAaA,GACbC,aAAcA,GACdC,iBAAkBA,GAClBzH,KAAMA,GACN0H,WAAYA,GACZ4G,aAAc3gB,MAAM2gB,aACpBtiB,UAAWA,GACXuL,IAAKA,GACLzF,MAAOA,GACP6V,aAAcA,GACdC,eAAgBA,GAChBrN,iBAAkBA,EAClByB,YAAaA,EACbpB,SAAUA,EACVwC,SAAUA,EACVC,aAAcA,EACdC,SAAUA,EACVE,WAAYA,EACZI,UAAWA,EACXI,SAAUA,EACVb,UAAWA,EACXc,aAAcA,EACdhD,YAAaA,EACbD,OAAQA,EACRsD,UAAWA,EACXE,WAAYA,EACZjB,OAAQA,KAEd7P,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,qBAAsB,2BAA4BD,IAC3D,YACG,SAAUE,GAAV,GACO8Y,GAAU5W,MAAM4W,QAChBuC,EAAWvC,EAAQuC,SACnBivB,EAAOpoC,MAAM+D,OACjB6S,GAAQyxB,aACJ,OACA,aACA,eACA,eAEJzxB,EAAQ0xB,aACJnoC,OAAQ,SAAUkI,EAAOkgC,GACrB,IAAKlgC,EAAMmgC,aACP,KAAUryB,OAAM,mDAEpB9N,GAAMogC,UAAYnoC,KAAKmoC,UACvBpgC,EAAMqgC,YAAcpoC,KAAKooC,YACzBrgC,EAAMsgC,UAAYroC,KAAKqoC,UAClBJ,IACDlgC,EAAMugC,IAAMtoC,KAAKsoC,IACjBvgC,EAAMwgC,aAAevoC,KAAKuoC,eAGlCJ,UAAW,SAAUlnC,GACjB,MAAO6mC,GAAKK,UAAUnoC,KAAKkoC,eAAgBjnC,IAE/CmnC,YAAa,SAAUnnC,GACnB,MAAO6mC,GAAKM,YAAYpoC,KAAKkoC,aAAajnC,GAAUA,IAExDonC,UAAW,SAAUpnC,GACjB,MAAO6mC,GAAKO,UAAUroC,KAAKkoC,eAAgBjnC,IAE/CqnC,IAAK,WACD,GAAIR,EAAKQ,IAAIE,QACT,MAAOV,GAAKQ,IAAIG,YAAYzoC,KAAKkoC,eAEjC,MAAUryB,OAAM,8EAGxB0yB,aAAc,WAAA,GAKF5Z,GAKA+Z,EAEAC,CAXR,KAAKjpC,MAAMkpC,QAAQC,OACf,MAAO,KAEX,IAAIf,EAAKe,OAAOL,QAWZ,MAVI7Z,GAAYnxB,EAAE,WAAWsrC,KACzBC,QAAS,OACTlqC,MAAOmB,KAAK6C,QAAQhE,QACpBC,OAAQkB,KAAK6C,QAAQ/D,WACtBkqC,SAASloC,SAASe,MACjB6mC,EAAU,GAAIZ,GAAKe,OAAOL,QAAQ7Z,EAAU,IAChD+Z,EAAQZ,KAAK9nC,KAAKkoC,gBACdS,EAAQD,EAAQO,aAAaC,YACjCR,EAAQviB,UACRwI,EAAUwa,SACHR,CAEP,MAAU9yB,OAAM,oFAI5BgD,EAASP,YAAYuE,UACjBF,OAAQ,SAAUA,GACd,MAAOjd,OAAMid,OAAOe,MAAM,MAAOf,GAAQgB,OAAOhb,MAAMqF,UAAU6J,MAAMwT,KAAK7H,UAAW,MAE1FZ,SAAUld,MAAMkd,SAChBvM,UAAW3Q,MAAM2Q,YAErBwI,EAASzS,gBAAgByW,UAAWxW,QAAS3G,MAAMyG,WACnDmQ,EAAQ8yB,QAAU9yB,EAAQ/M,MAC1B+M,EAAQ+yB,MAAQ/yB,EAAQ7L,IACxB6L,EAAQgzB,QAAU,SAAUhiC,GACxB,MAAOgP,GAAQjP,gBAAgBC,EAAEiiC,iBAEvC9pC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,sBACH,0BACA,qBACDD,IACL,aAYkB,kBAAVC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dataviz.core.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/core/kendo-core', [\n        'kendo.core',\n        'kendo.drawing'\n    ], f);\n}(function () {\n    (function ($) {\n        window.kendo.dataviz = window.kendo.dataviz || {};\n        var drawing = kendo.drawing;\n        var util = drawing.util;\n        var Path = drawing.Path;\n        var Group = drawing.Group;\n        var Class = kendo.Class;\n        var geometry = kendo.geometry;\n        var Rect = geometry.Rect;\n        var Circle = geometry.Circle;\n        var geometryTransform = geometry.transform;\n        var Segment = geometry.Segment;\n        var dataviz = kendo.dataviz;\n        var deepExtend = kendo.deepExtend;\n        var isFunction = kendo.isFunction;\n        var __common_getter_js = kendo.getter;\n        var ARC = 'arc';\n        var AXIS_LABEL_CLICK = 'axisLabelClick';\n        var BLACK = '#000';\n        var BOTTOM = 'bottom';\n        var CENTER = 'center';\n        var CIRCLE = 'circle';\n        var COORD_PRECISION = 3;\n        var CROSS = 'cross';\n        var DATE = 'date';\n        var DEFAULT_FONT = '12px sans-serif';\n        var DEFAULT_HEIGHT = 400;\n        var DEFAULT_PRECISION = 10;\n        var DEFAULT_WIDTH = 600;\n        var END = 'end';\n        var FORMAT_REGEX = /\\{\\d+:?/;\n        var HEIGHT = 'height';\n        var HIGHLIGHT_ZINDEX = 100;\n        var INSIDE = 'inside';\n        var LEFT = 'left';\n        var MAX_VALUE = Number.MAX_VALUE;\n        var MIN_VALUE = -Number.MAX_VALUE;\n        var NONE = 'none';\n        var NOTE_CLICK = 'noteClick';\n        var NOTE_HOVER = 'noteHover';\n        var NOTE_LEAVE = 'noteLeave';\n        var OBJECT = 'object';\n        var OUTSIDE = 'outside';\n        var RIGHT = 'right';\n        var START = 'start';\n        var STRING = 'string';\n        var TOP = 'top';\n        var TRIANGLE = 'triangle';\n        var VALUE = 'value';\n        var WHITE = '#fff';\n        var WIDTH = 'width';\n        var X = 'x';\n        var Y = 'y';\n        var constants = {\n            ARC: ARC,\n            AXIS_LABEL_CLICK: AXIS_LABEL_CLICK,\n            BLACK: BLACK,\n            BOTTOM: BOTTOM,\n            CENTER: CENTER,\n            CIRCLE: CIRCLE,\n            COORD_PRECISION: COORD_PRECISION,\n            CROSS: CROSS,\n            DATE: DATE,\n            DEFAULT_FONT: DEFAULT_FONT,\n            DEFAULT_HEIGHT: DEFAULT_HEIGHT,\n            DEFAULT_PRECISION: DEFAULT_PRECISION,\n            DEFAULT_WIDTH: DEFAULT_WIDTH,\n            END: END,\n            FORMAT_REGEX: FORMAT_REGEX,\n            HEIGHT: HEIGHT,\n            HIGHLIGHT_ZINDEX: HIGHLIGHT_ZINDEX,\n            INSIDE: INSIDE,\n            LEFT: LEFT,\n            MAX_VALUE: MAX_VALUE,\n            MIN_VALUE: MIN_VALUE,\n            NONE: NONE,\n            NOTE_CLICK: NOTE_CLICK,\n            NOTE_HOVER: NOTE_HOVER,\n            NOTE_LEAVE: NOTE_LEAVE,\n            OBJECT: OBJECT,\n            OUTSIDE: OUTSIDE,\n            RIGHT: RIGHT,\n            START: START,\n            STRING: STRING,\n            TOP: TOP,\n            TRIANGLE: TRIANGLE,\n            VALUE: VALUE,\n            WHITE: WHITE,\n            WIDTH: WIDTH,\n            X: X,\n            Y: Y\n        };\n        function isArray(value) {\n            return Array.isArray(value);\n        }\n        function addClass(element, classes) {\n            var classArray = isArray(classes) ? classes : [classes];\n            for (var idx = 0; idx < classArray.length; idx++) {\n                var className = classArray[idx];\n                if (element.className.indexOf(className) === -1) {\n                    element.className += ' ' + className;\n                }\n            }\n        }\n        var SPACE_REGEX = /\\s+/g;\n        function removeClass(element, className) {\n            if (element && element.className) {\n                element.className = element.className.replace(className, '').replace(SPACE_REGEX, ' ');\n            }\n        }\n        function alignPathToPixel(path) {\n            var offset = 0.5;\n            if (path.options.stroke && kendo.drawing.util.defined(path.options.stroke.width)) {\n                if (path.options.stroke.width % 2 === 0) {\n                    offset = 0;\n                }\n            }\n            for (var i = 0; i < path.segments.length; i++) {\n                path.segments[i].anchor().round(0).translate(offset, offset);\n            }\n            return path;\n        }\n        function clockwise(angle1, angle2) {\n            return -angle1.x * angle2.y + angle1.y * angle2.x < 0;\n        }\n        function isNumber(value) {\n            return typeof value === 'number' && !isNaN(value);\n        }\n        function isString(value) {\n            return typeof value === STRING;\n        }\n        function convertableToNumber(value) {\n            return isNumber(value) || isString(value) && isFinite(value);\n        }\n        function isObject(value) {\n            return typeof value === 'object';\n        }\n        function styleValue(value) {\n            if (isNumber(value)) {\n                return value + 'px';\n            }\n            return value;\n        }\n        var SIZE_STYLES_REGEX = /width|height|top|left|bottom|right/i;\n        function isSizeField(field) {\n            return SIZE_STYLES_REGEX.test(field);\n        }\n        function elementStyles(element, styles) {\n            var stylesArray = isString(styles) ? [styles] : styles;\n            if (isArray(stylesArray)) {\n                var result = {};\n                var style = window.getComputedStyle(element);\n                for (var idx = 0; idx < stylesArray.length; idx++) {\n                    var field = stylesArray[idx];\n                    result[field] = isSizeField(field) ? parseFloat(style[field]) : style[field];\n                }\n                return result;\n            } else if (isObject(styles)) {\n                for (var field$1 in styles) {\n                    element.style[field$1] = styleValue(styles[field$1]);\n                }\n            }\n        }\n        function getSpacing(value, defaultSpacing) {\n            if (defaultSpacing === void 0) {\n                defaultSpacing = 0;\n            }\n            var spacing = {\n                top: 0,\n                right: 0,\n                bottom: 0,\n                left: 0\n            };\n            if (typeof value === 'number') {\n                spacing[TOP] = spacing[RIGHT] = spacing[BOTTOM] = spacing[LEFT] = value;\n            } else {\n                spacing[TOP] = value[TOP] || defaultSpacing;\n                spacing[RIGHT] = value[RIGHT] || defaultSpacing;\n                spacing[BOTTOM] = value[BOTTOM] || defaultSpacing;\n                spacing[LEFT] = value[LEFT] || defaultSpacing;\n            }\n            return spacing;\n        }\n        var defaultImplementation = {\n            format: function (format, value) {\n                return value;\n            },\n            toString: function (value) {\n                return value;\n            },\n            parseDate: function (value) {\n                return new Date(value);\n            }\n        };\n        var current = defaultImplementation;\n        var IntlService = Class.extend({});\n        IntlService.register = function (userImplementation) {\n            current = userImplementation;\n        };\n        if (Object.defineProperties) {\n            Object.defineProperties(IntlService, {\n                implementation: {\n                    get: function () {\n                        return current;\n                    }\n                }\n            });\n        }\n        var FORMAT_REPLACE_REGEX = /\\{(\\d+)(:[^\\}]+)?\\}/g;\n        var FormatService = Class.extend({\n            init: function (intlService) {\n                this._intlService = intlService;\n            },\n            auto: function (formatString) {\n                var values = [], len = arguments.length - 1;\n                while (len-- > 0)\n                    values[len] = arguments[len + 1];\n                var intl = this.intl;\n                if (isString(formatString) && formatString.match(FORMAT_REGEX)) {\n                    return intl.format.apply(intl, [formatString].concat(values));\n                }\n                return intl.toString(values[0], formatString);\n            },\n            localeAuto: function (formatString, values, locale) {\n                var intl = this.intl;\n                var result;\n                if (isString(formatString) && formatString.match(FORMAT_REGEX)) {\n                    result = formatString.replace(FORMAT_REPLACE_REGEX, function (match, index, placeholderFormat) {\n                        var value = values[parseInt(index, 10)];\n                        return intl.toString(value, placeholderFormat ? placeholderFormat.substring(1) : '', locale);\n                    });\n                } else {\n                    result = intl.toString(values[0], formatString, locale);\n                }\n                return result;\n            }\n        });\n        if (Object.defineProperties) {\n            Object.defineProperties(FormatService.fn, {\n                intl: {\n                    get: function () {\n                        return this._intlService || IntlService.implementation;\n                    },\n                    set: function (value) {\n                        this._intlService = value;\n                    }\n                }\n            });\n        }\n        var ChartService = Class.extend({\n            init: function (chart, context) {\n                if (context === void 0) {\n                    context = {};\n                }\n                this._intlService = context.intlService;\n                this.sender = context.sender || chart;\n                this.format = new FormatService(context.intlService);\n                this.chart = chart;\n                this.rtl = Boolean(context.rtl);\n            },\n            notify: function (name, args) {\n                if (this.chart) {\n                    this.chart.trigger(name, args);\n                }\n            },\n            isPannable: function (axis) {\n                var pannable = ((this.chart || {}).options || {}).pannable;\n                return pannable && pannable.lock !== axis;\n            }\n        });\n        if (Object.defineProperties) {\n            Object.defineProperties(ChartService.fn, {\n                intl: {\n                    get: function () {\n                        return this._intlService || IntlService.implementation;\n                    },\n                    set: function (value) {\n                        this._intlService = value;\n                        this.format.intl = value;\n                    }\n                }\n            });\n        }\n        var current$1;\n        var DomEventsBuilder = Class.extend({});\n        DomEventsBuilder.register = function (userImplementation) {\n            current$1 = userImplementation;\n        };\n        DomEventsBuilder.create = function (element, events) {\n            if (current$1) {\n                return current$1.create(element, events);\n            }\n        };\n        var current$2 = {\n            compile: function (template) {\n                return template;\n            }\n        };\n        var TemplateService = Class.extend({});\n        TemplateService.register = function (userImplementation) {\n            current$2 = userImplementation;\n        };\n        TemplateService.compile = function (template) {\n            return current$2.compile(template);\n        };\n        var services = {\n            ChartService: ChartService,\n            DomEventsBuilder: DomEventsBuilder,\n            FormatService: FormatService,\n            IntlService: IntlService,\n            TemplateService: TemplateService\n        };\n        function getTemplate(options) {\n            if (options === void 0) {\n                options = {};\n            }\n            var template;\n            if (options.template) {\n                options.template = template = TemplateService.compile(options.template);\n            } else if (isFunction(options.content)) {\n                template = options.content;\n            }\n            return template;\n        }\n        function grep(array, callback) {\n            var length = array.length;\n            var result = [];\n            for (var idx = 0; idx < length; idx++) {\n                if (callback(array[idx])) {\n                    result.push(array[idx]);\n                }\n            }\n            return result;\n        }\n        function hasClasses(element, classNames) {\n            if (element.className) {\n                var names = classNames.split(' ');\n                for (var idx = 0; idx < names.length; idx++) {\n                    if (element.className.indexOf(names[idx]) !== -1) {\n                        return true;\n                    }\n                }\n            }\n        }\n        var HashMap = function HashMap() {\n            this._map = {};\n        };\n        HashMap.prototype.get = function get(name) {\n            return this._map[this._key(name)];\n        };\n        HashMap.prototype.set = function set(name, value) {\n            this._map[this._key(name)] = value;\n        };\n        HashMap.prototype._key = function _key(name) {\n            return name instanceof Date ? name.getTime() : name;\n        };\n        function inArray(value, array) {\n            if (array) {\n                return array.indexOf(value) !== -1;\n            }\n        }\n        function interpolateValue(start, end, progress) {\n            return kendo.drawing.util.round(start + (end - start) * progress, COORD_PRECISION);\n        }\n        var TRIGGER = 'trigger';\n        var InstanceObserver = Class.extend({\n            init: function (observer, handlers) {\n                this.observer = observer;\n                this.handlerMap = deepExtend({}, this.handlerMap, handlers);\n            },\n            trigger: function (name, args) {\n                var ref = this;\n                var observer = ref.observer;\n                var handlerMap = ref.handlerMap;\n                var isDefaultPrevented;\n                if (handlerMap[name]) {\n                    isDefaultPrevented = this.callObserver(handlerMap[name], args);\n                } else if (observer[TRIGGER]) {\n                    isDefaultPrevented = this.callObserver(TRIGGER, name, args);\n                }\n                return isDefaultPrevented;\n            },\n            callObserver: function (fnName) {\n                var args = [], len = arguments.length - 1;\n                while (len-- > 0)\n                    args[len] = arguments[len + 1];\n                return this.observer[fnName].apply(this.observer, args);\n            },\n            requiresHandlers: function (names) {\n                var this$1 = this;\n                if (this.observer.requiresHandlers) {\n                    return this.observer.requiresHandlers(names);\n                }\n                for (var idx = 0; idx < names.length; idx++) {\n                    if (this$1.handlerMap[names[idx]]) {\n                        return true;\n                    }\n                }\n            }\n        });\n        function map(array, callback) {\n            var length = array.length;\n            var result = [];\n            for (var idx = 0; idx < length; idx++) {\n                var value = callback(array[idx]);\n                if (kendo.drawing.util.defined(value)) {\n                    result.push(value);\n                }\n            }\n            return result;\n        }\n        function mousewheelDelta(e) {\n            var delta = 0;\n            if (e.wheelDelta) {\n                delta = -e.wheelDelta / 120;\n                delta = delta > 0 ? Math.ceil(delta) : Math.floor(delta);\n            }\n            if (e.detail) {\n                delta = kendo.drawing.util.round(e.detail / 3);\n            }\n            return delta;\n        }\n        var ref = kendo.drawing.util;\n        var append = ref.append;\n        var bindEvents = ref.bindEvents;\n        var defined = ref.defined;\n        var deg = ref.deg;\n        var elementOffset = ref.elementOffset;\n        var elementSize = ref.elementSize;\n        var eventElement = ref.eventElement;\n        var eventCoordinates = ref.eventCoordinates;\n        var last = ref.last;\n        var limitValue = ref.limitValue;\n        var objectKey = ref.objectKey;\n        var rad = ref.rad;\n        var round = ref.round;\n        var unbindEvents = ref.unbindEvents;\n        var valueOrDefault = ref.valueOrDefault;\n        var FontLoader = Class.extend({});\n        FontLoader.fetchFonts = function (options, fonts, state) {\n            if (state === void 0) {\n                state = { depth: 0 };\n            }\n            var MAX_DEPTH = 5;\n            if (!options || state.depth > MAX_DEPTH || !document.fonts) {\n                return;\n            }\n            Object.keys(options).forEach(function (key) {\n                var value = options[key];\n                if (key === 'dataSource' || key[0] === '$' || !value) {\n                    return;\n                }\n                if (key === 'font') {\n                    fonts.push(value);\n                } else if (typeof value === 'object') {\n                    state.depth++;\n                    FontLoader.fetchFonts(value, fonts, state);\n                    state.depth--;\n                }\n            });\n        };\n        FontLoader.loadFonts = function (fonts, callback) {\n            var promises = [];\n            if (fonts.length > 0 && document.fonts) {\n                try {\n                    promises = fonts.map(function (font) {\n                        return document.fonts.load(font);\n                    });\n                } catch (e) {\n                    kendo.logToConsole(e);\n                }\n                Promise.all(promises).then(callback, callback);\n            } else {\n                callback();\n            }\n        };\n        FontLoader.preloadFonts = function (options, callback) {\n            var fonts = [];\n            FontLoader.fetchFonts(options, fonts);\n            FontLoader.loadFonts(fonts, callback);\n        };\n        function setDefaultOptions(type, options) {\n            var proto = type.prototype;\n            if (proto.options) {\n                proto.options = deepExtend({}, proto.options, options);\n            } else {\n                proto.options = options;\n            }\n        }\n        function sparseArrayLimits(arr) {\n            var min = MAX_VALUE;\n            var max = MIN_VALUE;\n            for (var idx = 0, length = arr.length; idx < length; idx++) {\n                var value = arr[idx];\n                if (value !== null && isFinite(value)) {\n                    min = Math.min(min, value);\n                    max = Math.max(max, value);\n                }\n            }\n            return {\n                min: min === MAX_VALUE ? undefined : min,\n                max: max === MIN_VALUE ? undefined : max\n            };\n        }\n        function autoMajorUnit(min, max) {\n            var diff = round(max - min, DEFAULT_PRECISION - 1);\n            if (diff === 0) {\n                if (max === 0) {\n                    return 0.1;\n                }\n                diff = Math.abs(max);\n            }\n            var scale = Math.pow(10, Math.floor(Math.log(diff) / Math.log(10)));\n            var relativeValue = round(diff / scale, DEFAULT_PRECISION);\n            var scaleMultiplier = 1;\n            if (relativeValue < 1.904762) {\n                scaleMultiplier = 0.2;\n            } else if (relativeValue < 4.761904) {\n                scaleMultiplier = 0.5;\n            } else if (relativeValue < 9.523809) {\n                scaleMultiplier = 1;\n            } else {\n                scaleMultiplier = 2;\n            }\n            return round(scale * scaleMultiplier, DEFAULT_PRECISION);\n        }\n        var Point = Class.extend({\n            init: function (x, y) {\n                this.x = x || 0;\n                this.y = y || 0;\n            },\n            clone: function () {\n                return new Point(this.x, this.y);\n            },\n            equals: function (point) {\n                return point && this.x === point.x && this.y === point.y;\n            },\n            rotate: function (center, degrees) {\n                var theta = rad(degrees);\n                var cosT = Math.cos(theta);\n                var sinT = Math.sin(theta);\n                var cx = center.x;\n                var cy = center.y;\n                var ref = this;\n                var x = ref.x;\n                var y = ref.y;\n                this.x = round(cx + (x - cx) * cosT + (y - cy) * sinT, COORD_PRECISION);\n                this.y = round(cy + (y - cy) * cosT - (x - cx) * sinT, COORD_PRECISION);\n                return this;\n            },\n            multiply: function (a) {\n                this.x *= a;\n                this.y *= a;\n                return this;\n            },\n            distanceTo: function (point) {\n                var dx = this.x - point.x;\n                var dy = this.y - point.y;\n                return Math.sqrt(dx * dx + dy * dy);\n            }\n        });\n        Point.onCircle = function (center, angle, radius) {\n            var radians = rad(angle);\n            return new Point(center.x - radius * Math.cos(radians), center.y - radius * Math.sin(radians));\n        };\n        var Box = Class.extend({\n            init: function (x1, y1, x2, y2) {\n                this.x1 = x1 || 0;\n                this.y1 = y1 || 0;\n                this.x2 = x2 || 0;\n                this.y2 = y2 || 0;\n            },\n            equals: function (box) {\n                return this.x1 === box.x1 && this.x2 === box.x2 && this.y1 === box.y1 && this.y2 === box.y2;\n            },\n            width: function () {\n                return this.x2 - this.x1;\n            },\n            height: function () {\n                return this.y2 - this.y1;\n            },\n            translate: function (dx, dy) {\n                this.x1 += dx;\n                this.x2 += dx;\n                this.y1 += dy;\n                this.y2 += dy;\n                return this;\n            },\n            move: function (x, y) {\n                var height = this.height();\n                var width = this.width();\n                if (defined(x)) {\n                    this.x1 = x;\n                    this.x2 = this.x1 + width;\n                }\n                if (defined(y)) {\n                    this.y1 = y;\n                    this.y2 = this.y1 + height;\n                }\n                return this;\n            },\n            wrap: function (targetBox) {\n                this.x1 = Math.min(this.x1, targetBox.x1);\n                this.y1 = Math.min(this.y1, targetBox.y1);\n                this.x2 = Math.max(this.x2, targetBox.x2);\n                this.y2 = Math.max(this.y2, targetBox.y2);\n                return this;\n            },\n            wrapPoint: function (point) {\n                var arrayPoint = isArray(point);\n                var x = arrayPoint ? point[0] : point.x;\n                var y = arrayPoint ? point[1] : point.y;\n                this.wrap(new Box(x, y, x, y));\n                return this;\n            },\n            snapTo: function (targetBox, axis) {\n                if (axis === X || !axis) {\n                    this.x1 = targetBox.x1;\n                    this.x2 = targetBox.x2;\n                }\n                if (axis === Y || !axis) {\n                    this.y1 = targetBox.y1;\n                    this.y2 = targetBox.y2;\n                }\n                return this;\n            },\n            alignTo: function (targetBox, anchor) {\n                var height = this.height();\n                var width = this.width();\n                var axis = anchor === TOP || anchor === BOTTOM ? Y : X;\n                var offset = axis === Y ? height : width;\n                if (anchor === CENTER) {\n                    var targetCenter = targetBox.center();\n                    var center = this.center();\n                    this.x1 += targetCenter.x - center.x;\n                    this.y1 += targetCenter.y - center.y;\n                } else if (anchor === TOP || anchor === LEFT) {\n                    this[axis + 1] = targetBox[axis + 1] - offset;\n                } else {\n                    this[axis + 1] = targetBox[axis + 2];\n                }\n                this.x2 = this.x1 + width;\n                this.y2 = this.y1 + height;\n                return this;\n            },\n            shrink: function (dw, dh) {\n                this.x2 -= dw;\n                this.y2 -= dh;\n                return this;\n            },\n            expand: function (dw, dh) {\n                this.shrink(-dw, -dh);\n                return this;\n            },\n            pad: function (padding) {\n                var spacing = getSpacing(padding);\n                this.x1 -= spacing.left;\n                this.x2 += spacing.right;\n                this.y1 -= spacing.top;\n                this.y2 += spacing.bottom;\n                return this;\n            },\n            unpad: function (padding) {\n                var spacing = getSpacing(padding);\n                spacing.left = -spacing.left;\n                spacing.top = -spacing.top;\n                spacing.right = -spacing.right;\n                spacing.bottom = -spacing.bottom;\n                return this.pad(spacing);\n            },\n            clone: function () {\n                return new Box(this.x1, this.y1, this.x2, this.y2);\n            },\n            center: function () {\n                return new Point(this.x1 + this.width() / 2, this.y1 + this.height() / 2);\n            },\n            containsPoint: function (point) {\n                return point.x >= this.x1 && point.x <= this.x2 && point.y >= this.y1 && point.y <= this.y2;\n            },\n            points: function () {\n                return [\n                    new Point(this.x1, this.y1),\n                    new Point(this.x2, this.y1),\n                    new Point(this.x2, this.y2),\n                    new Point(this.x1, this.y2)\n                ];\n            },\n            getHash: function () {\n                return [\n                    this.x1,\n                    this.y1,\n                    this.x2,\n                    this.y2\n                ].join(',');\n            },\n            overlaps: function (box) {\n                return !(box.y2 < this.y1 || this.y2 < box.y1 || box.x2 < this.x1 || this.x2 < box.x1);\n            },\n            rotate: function (rotation) {\n                var width = this.width();\n                var height = this.height();\n                var ref = this.center();\n                var cx = ref.x;\n                var cy = ref.y;\n                var r1 = rotatePoint(0, 0, cx, cy, rotation);\n                var r2 = rotatePoint(width, 0, cx, cy, rotation);\n                var r3 = rotatePoint(width, height, cx, cy, rotation);\n                var r4 = rotatePoint(0, height, cx, cy, rotation);\n                width = Math.max(r1.x, r2.x, r3.x, r4.x) - Math.min(r1.x, r2.x, r3.x, r4.x);\n                height = Math.max(r1.y, r2.y, r3.y, r4.y) - Math.min(r1.y, r2.y, r3.y, r4.y);\n                this.x2 = this.x1 + width;\n                this.y2 = this.y1 + height;\n                return this;\n            },\n            toRect: function () {\n                return new Rect([\n                    this.x1,\n                    this.y1\n                ], [\n                    this.width(),\n                    this.height()\n                ]);\n            },\n            hasSize: function () {\n                return this.width() !== 0 && this.height() !== 0;\n            },\n            align: function (targetBox, axis, alignment) {\n                var c1 = axis + 1;\n                var c2 = axis + 2;\n                var sizeFunc = axis === X ? WIDTH : HEIGHT;\n                var size = this[sizeFunc]();\n                if (inArray(alignment, [\n                        LEFT,\n                        TOP\n                    ])) {\n                    this[c1] = targetBox[c1];\n                    this[c2] = this[c1] + size;\n                } else if (inArray(alignment, [\n                        RIGHT,\n                        BOTTOM\n                    ])) {\n                    this[c2] = targetBox[c2];\n                    this[c1] = this[c2] - size;\n                } else if (alignment === CENTER) {\n                    this[c1] = targetBox[c1] + (targetBox[sizeFunc]() - size) / 2;\n                    this[c2] = this[c1] + size;\n                }\n            }\n        });\n        function rotatePoint(x, y, cx, cy, angle) {\n            var theta = rad(angle);\n            return new Point(cx + (x - cx) * Math.cos(theta) + (y - cy) * Math.sin(theta), cy - (x - cx) * Math.sin(theta) + (y - cy) * Math.cos(theta));\n        }\n        var Ring = Class.extend({\n            init: function (center, innerRadius, radius, startAngle, angle) {\n                this.center = center;\n                this.innerRadius = innerRadius;\n                this.radius = radius;\n                this.startAngle = startAngle;\n                this.angle = angle;\n            },\n            clone: function () {\n                return new Ring(this.center, this.innerRadius, this.radius, this.startAngle, this.angle);\n            },\n            middle: function () {\n                return this.startAngle + this.angle / 2;\n            },\n            setRadius: function (newRadius, innerRadius) {\n                if (innerRadius) {\n                    this.innerRadius = newRadius;\n                } else {\n                    this.radius = newRadius;\n                }\n                return this;\n            },\n            point: function (angle, innerRadius) {\n                var radianAngle = rad(angle);\n                var ax = Math.cos(radianAngle);\n                var ay = Math.sin(radianAngle);\n                var radius = innerRadius ? this.innerRadius : this.radius;\n                var x = round(this.center.x - ax * radius, COORD_PRECISION);\n                var y = round(this.center.y - ay * radius, COORD_PRECISION);\n                return new Point(x, y);\n            },\n            adjacentBox: function (distance, width, height) {\n                var sector = this.clone().expand(distance);\n                var midAndle = sector.middle();\n                var midPoint = sector.point(midAndle);\n                var hw = width / 2;\n                var hh = height / 2;\n                var sa = Math.sin(rad(midAndle));\n                var ca = Math.cos(rad(midAndle));\n                var x = midPoint.x - hw;\n                var y = midPoint.y - hh;\n                if (Math.abs(sa) < 0.9) {\n                    x += hw * -ca / Math.abs(ca);\n                }\n                if (Math.abs(ca) < 0.9) {\n                    y += hh * -sa / Math.abs(sa);\n                }\n                return new Box(x, y, x + width, y + height);\n            },\n            containsPoint: function (p) {\n                var center = this.center;\n                var innerRadius = this.innerRadius;\n                var radius = this.radius;\n                var startAngle = this.startAngle;\n                var endAngle = this.startAngle + this.angle;\n                var dx = p.x - center.x;\n                var dy = p.y - center.y;\n                var vector = new Point(dx, dy);\n                var startPoint = this.point(startAngle);\n                var startVector = new Point(startPoint.x - center.x, startPoint.y - center.y);\n                var endPoint = this.point(endAngle);\n                var endVector = new Point(endPoint.x - center.x, endPoint.y - center.y);\n                var dist = round(dx * dx + dy * dy, COORD_PRECISION);\n                return (startVector.equals(vector) || clockwise(startVector, vector)) && !clockwise(endVector, vector) && dist >= innerRadius * innerRadius && dist <= radius * radius;\n            },\n            getBBox: function () {\n                var this$1 = this;\n                var box = new Box(MAX_VALUE, MAX_VALUE, MIN_VALUE, MIN_VALUE);\n                var startAngle = round(this.startAngle % 360);\n                var endAngle = round((startAngle + this.angle) % 360);\n                var innerRadius = this.innerRadius;\n                var allAngles = [\n                    0,\n                    90,\n                    180,\n                    270,\n                    startAngle,\n                    endAngle\n                ].sort(numericComparer);\n                var startAngleIndex = allAngles.indexOf(startAngle);\n                var endAngleIndex = allAngles.indexOf(endAngle);\n                var angles;\n                if (startAngle === endAngle) {\n                    angles = allAngles;\n                } else {\n                    if (startAngleIndex < endAngleIndex) {\n                        angles = allAngles.slice(startAngleIndex, endAngleIndex + 1);\n                    } else {\n                        angles = [].concat(allAngles.slice(0, endAngleIndex + 1), allAngles.slice(startAngleIndex, allAngles.length));\n                    }\n                }\n                for (var i = 0; i < angles.length; i++) {\n                    var point = this$1.point(angles[i]);\n                    box.wrapPoint(point);\n                    box.wrapPoint(point, innerRadius);\n                }\n                if (!innerRadius) {\n                    box.wrapPoint(this.center);\n                }\n                return box;\n            },\n            expand: function (value) {\n                this.radius += value;\n                return this;\n            }\n        });\n        function numericComparer(a, b) {\n            return a - b;\n        }\n        var Sector = Ring.extend({\n            init: function (center, radius, startAngle, angle) {\n                Ring.fn.init.call(this, center, 0, radius, startAngle, angle);\n            },\n            expand: function (value) {\n                return Ring.fn.expand.call(this, value);\n            },\n            clone: function () {\n                return new Sector(this.center, this.radius, this.startAngle, this.angle);\n            },\n            setRadius: function (newRadius) {\n                this.radius = newRadius;\n                return this;\n            }\n        });\n        var DIRECTION_ANGLE = 0.001;\n        var ShapeBuilder = Class.extend({\n            createRing: function (sector, options) {\n                var startAngle = sector.startAngle + 180;\n                var endAngle = sector.angle + startAngle;\n                if (sector.angle > 0 && startAngle === endAngle) {\n                    endAngle += DIRECTION_ANGLE;\n                }\n                var center = new geometry.Point(sector.center.x, sector.center.y);\n                var radius = Math.max(sector.radius, 0);\n                var innerRadius = Math.max(sector.innerRadius, 0);\n                var arc = new geometry.Arc(center, {\n                    startAngle: startAngle,\n                    endAngle: endAngle,\n                    radiusX: radius,\n                    radiusY: radius\n                });\n                var path = Path.fromArc(arc, options).close();\n                if (innerRadius) {\n                    arc.radiusX = arc.radiusY = innerRadius;\n                    var innerEnd = arc.pointAt(endAngle);\n                    path.lineTo(innerEnd.x, innerEnd.y);\n                    path.arc(endAngle, startAngle, innerRadius, innerRadius, true);\n                } else {\n                    path.lineTo(center.x, center.y);\n                }\n                return path;\n            }\n        });\n        ShapeBuilder.current = new ShapeBuilder();\n        var ChartElement = Class.extend({\n            init: function (options) {\n                this.children = [];\n                this.options = deepExtend({}, this.options, this.initUserOptions(options));\n            },\n            initUserOptions: function (options) {\n                return options;\n            },\n            reflow: function (targetBox) {\n                var children = this.children;\n                var box;\n                for (var i = 0; i < children.length; i++) {\n                    var currentChild = children[i];\n                    currentChild.reflow(targetBox);\n                    box = box ? box.wrap(currentChild.box) : currentChild.box.clone();\n                }\n                this.box = box || targetBox;\n            },\n            destroy: function () {\n                var children = this.children;\n                if (this.animation) {\n                    this.animation.destroy();\n                }\n                for (var i = 0; i < children.length; i++) {\n                    children[i].destroy();\n                }\n            },\n            getRoot: function () {\n                var parent = this.parent;\n                return parent ? parent.getRoot() : null;\n            },\n            getSender: function () {\n                var service = this.getService();\n                if (service) {\n                    return service.sender;\n                }\n            },\n            getService: function () {\n                var element = this;\n                while (element) {\n                    if (element.chartService) {\n                        return element.chartService;\n                    }\n                    element = element.parent;\n                }\n            },\n            translateChildren: function (dx, dy) {\n                var children = this.children;\n                var childrenCount = children.length;\n                for (var i = 0; i < childrenCount; i++) {\n                    children[i].box.translate(dx, dy);\n                }\n            },\n            append: function () {\n                var arguments$1 = arguments;\n                var this$1 = this;\n                for (var i = 0; i < arguments.length; i++) {\n                    var item = arguments$1[i];\n                    this$1.children.push(item);\n                    item.parent = this$1;\n                }\n            },\n            renderVisual: function () {\n                if (this.options.visible === false) {\n                    return;\n                }\n                this.createVisual();\n                this.addVisual();\n                this.renderChildren();\n                this.createAnimation();\n                this.renderComplete();\n            },\n            addVisual: function () {\n                if (this.visual) {\n                    this.visual.chartElement = this;\n                    if (this.parent) {\n                        this.parent.appendVisual(this.visual);\n                    }\n                }\n            },\n            renderChildren: function () {\n                var children = this.children;\n                var length = children.length;\n                for (var i = 0; i < length; i++) {\n                    children[i].renderVisual();\n                }\n            },\n            createVisual: function () {\n                this.visual = new Group({\n                    zIndex: this.options.zIndex,\n                    visible: valueOrDefault(this.options.visible, true)\n                });\n            },\n            createAnimation: function () {\n                if (this.visual && this.options.animation) {\n                    this.animation = drawing.Animation.create(this.visual, this.options.animation);\n                }\n            },\n            appendVisual: function (childVisual) {\n                if (!childVisual.chartElement) {\n                    childVisual.chartElement = this;\n                }\n                if (childVisual.options.noclip) {\n                    this.clipRoot().visual.append(childVisual);\n                } else if (defined(childVisual.options.zIndex)) {\n                    this.stackRoot().stackVisual(childVisual);\n                } else if (this.isStackRoot) {\n                    this.stackVisual(childVisual);\n                } else if (this.visual) {\n                    this.visual.append(childVisual);\n                } else {\n                    this.parent.appendVisual(childVisual);\n                }\n            },\n            clipRoot: function () {\n                if (this.parent) {\n                    return this.parent.clipRoot();\n                }\n                return this;\n            },\n            stackRoot: function () {\n                if (this.parent) {\n                    return this.parent.stackRoot();\n                }\n                return this;\n            },\n            stackVisual: function (childVisual) {\n                var zIndex = childVisual.options.zIndex || 0;\n                var visuals = this.visual.children;\n                var length = visuals.length;\n                var pos;\n                for (pos = 0; pos < length; pos++) {\n                    var sibling = visuals[pos];\n                    var here = valueOrDefault(sibling.options.zIndex, 0);\n                    if (here > zIndex) {\n                        break;\n                    }\n                }\n                this.visual.insert(pos, childVisual);\n            },\n            traverse: function (callback) {\n                var children = this.children;\n                var length = children.length;\n                for (var i = 0; i < length; i++) {\n                    var child = children[i];\n                    callback(child);\n                    if (child.traverse) {\n                        child.traverse(callback);\n                    }\n                }\n            },\n            closest: function (match) {\n                var element = this;\n                var matched = false;\n                while (element && !matched) {\n                    matched = match(element);\n                    if (!matched) {\n                        element = element.parent;\n                    }\n                }\n                if (matched) {\n                    return element;\n                }\n            },\n            renderComplete: function () {\n            },\n            hasHighlight: function () {\n                var options = (this.options || {}).highlight;\n                return !(!this.createHighlight || options && options.visible === false);\n            },\n            toggleHighlight: function (show) {\n                var this$1 = this;\n                var options = (this.options || {}).highlight || {};\n                var customVisual = options.visual;\n                var highlight = this._highlight;\n                if (!highlight) {\n                    var highlightOptions = {\n                        fill: {\n                            color: WHITE,\n                            opacity: 0.2\n                        },\n                        stroke: {\n                            color: WHITE,\n                            width: 1,\n                            opacity: 0.2\n                        }\n                    };\n                    if (customVisual) {\n                        highlight = this._highlight = customVisual($.extend(this.highlightVisualArgs(), {\n                            createVisual: function () {\n                                return this$1.createHighlight(highlightOptions);\n                            },\n                            sender: this.getSender(),\n                            series: this.series,\n                            dataItem: this.dataItem,\n                            category: this.category,\n                            value: this.value,\n                            percentage: this.percentage,\n                            runningTotal: this.runningTotal,\n                            total: this.total\n                        }));\n                        if (!highlight) {\n                            return;\n                        }\n                    } else {\n                        highlight = this._highlight = this.createHighlight(highlightOptions);\n                    }\n                    if (!defined(highlight.options.zIndex)) {\n                        highlight.options.zIndex = valueOrDefault(options.zIndex, this.options.zIndex);\n                    }\n                    this.appendVisual(highlight);\n                }\n                highlight.visible(show);\n            },\n            createGradientOverlay: function (element, options, gradientOptions) {\n                var overlay = new Path($.extend({\n                    stroke: { color: 'none' },\n                    fill: this.createGradient(gradientOptions),\n                    closed: element.options.closed\n                }, options));\n                overlay.segments.elements(element.segments.elements());\n                return overlay;\n            },\n            createGradient: function (options) {\n                if (this.parent) {\n                    return this.parent.createGradient(options);\n                }\n            }\n        });\n        ChartElement.prototype.options = {};\n        var BoxElement = ChartElement.extend({\n            init: function (options) {\n                ChartElement.fn.init.call(this, options);\n                this.options.margin = getSpacing(this.options.margin);\n                this.options.padding = getSpacing(this.options.padding);\n            },\n            reflow: function (targetBox) {\n                var this$1 = this;\n                var options = this.options;\n                var width = options.width;\n                var height = options.height;\n                var shrinkToFit = options.shrinkToFit;\n                var hasSetSize = width && height;\n                var margin = options.margin;\n                var padding = options.padding;\n                var borderWidth = options.border.width;\n                var box;\n                var reflowPaddingBox = function () {\n                    this$1.align(targetBox, X, options.align);\n                    this$1.align(targetBox, Y, options.vAlign);\n                    this$1.paddingBox = box.clone().unpad(margin).unpad(borderWidth);\n                };\n                var contentBox = targetBox.clone();\n                if (hasSetSize) {\n                    contentBox.x2 = contentBox.x1 + width;\n                    contentBox.y2 = contentBox.y1 + height;\n                }\n                if (shrinkToFit) {\n                    contentBox.unpad(margin).unpad(borderWidth).unpad(padding);\n                }\n                ChartElement.fn.reflow.call(this, contentBox);\n                if (hasSetSize) {\n                    box = this.box = new Box(0, 0, width, height);\n                } else {\n                    box = this.box;\n                }\n                if (shrinkToFit && hasSetSize) {\n                    reflowPaddingBox();\n                    contentBox = this.contentBox = this.paddingBox.clone().unpad(padding);\n                } else {\n                    contentBox = this.contentBox = box.clone();\n                    box.pad(padding).pad(borderWidth).pad(margin);\n                    reflowPaddingBox();\n                }\n                this.translateChildren(box.x1 - contentBox.x1 + margin.left + borderWidth + padding.left, box.y1 - contentBox.y1 + margin.top + borderWidth + padding.top);\n                var children = this.children;\n                for (var i = 0; i < children.length; i++) {\n                    var item = children[i];\n                    item.reflow(item.box);\n                }\n            },\n            align: function (targetBox, axis, alignment) {\n                this.box.align(targetBox, axis, alignment);\n            },\n            hasBox: function () {\n                var options = this.options;\n                return options.border.width || options.background;\n            },\n            createVisual: function () {\n                ChartElement.fn.createVisual.call(this);\n                var options = this.options;\n                if (options.visible && this.hasBox()) {\n                    this.visual.append(Path.fromRect(this.paddingBox.toRect(), this.visualStyle()));\n                }\n            },\n            visualStyle: function () {\n                var options = this.options;\n                var border = options.border || {};\n                return {\n                    stroke: {\n                        width: border.width,\n                        color: border.color,\n                        opacity: valueOrDefault(border.opacity, options.opacity),\n                        dashType: border.dashType\n                    },\n                    fill: {\n                        color: options.background,\n                        opacity: options.opacity\n                    },\n                    cursor: options.cursor\n                };\n            }\n        });\n        setDefaultOptions(BoxElement, {\n            align: LEFT,\n            vAlign: TOP,\n            margin: {},\n            padding: {},\n            border: {\n                color: BLACK,\n                width: 0\n            },\n            background: '',\n            shrinkToFit: false,\n            width: 0,\n            height: 0,\n            visible: true\n        });\n        var ShapeElement = BoxElement.extend({\n            init: function (options, pointData) {\n                BoxElement.fn.init.call(this, options);\n                this.pointData = pointData;\n            },\n            getElement: function () {\n                var ref = this;\n                var options = ref.options;\n                var box = ref.paddingBox;\n                var type = options.type;\n                var rotation = options.rotation;\n                var center = box.center();\n                var halfWidth = box.width() / 2;\n                if (!options.visible || !this.hasBox()) {\n                    return null;\n                }\n                var style = this.visualStyle();\n                var element;\n                if (type === CIRCLE) {\n                    element = new drawing.Circle(new Circle([\n                        round(box.x1 + halfWidth, COORD_PRECISION),\n                        round(box.y1 + box.height() / 2, COORD_PRECISION)\n                    ], halfWidth), style);\n                } else if (type === TRIANGLE) {\n                    element = Path.fromPoints([\n                        [\n                            box.x1 + halfWidth,\n                            box.y1\n                        ],\n                        [\n                            box.x1,\n                            box.y2\n                        ],\n                        [\n                            box.x2,\n                            box.y2\n                        ]\n                    ], style).close();\n                } else if (type === CROSS) {\n                    element = new drawing.MultiPath(style);\n                    element.moveTo(box.x1, box.y1).lineTo(box.x2, box.y2);\n                    element.moveTo(box.x1, box.y2).lineTo(box.x2, box.y1);\n                } else {\n                    element = Path.fromRect(box.toRect(), style);\n                }\n                if (rotation) {\n                    element.transform(geometryTransform().rotate(-rotation, [\n                        center.x,\n                        center.y\n                    ]));\n                }\n                element.options.zIndex = options.zIndex;\n                return element;\n            },\n            createElement: function () {\n                var this$1 = this;\n                var customVisual = this.options.visual;\n                var pointData = this.pointData || {};\n                var visual;\n                if (customVisual) {\n                    visual = customVisual({\n                        value: pointData.value,\n                        dataItem: pointData.dataItem,\n                        sender: this.getSender(),\n                        series: pointData.series,\n                        category: pointData.category,\n                        rect: this.paddingBox.toRect(),\n                        options: this.visualOptions(),\n                        createVisual: function () {\n                            return this$1.getElement();\n                        }\n                    });\n                } else {\n                    visual = this.getElement();\n                }\n                return visual;\n            },\n            visualOptions: function () {\n                var options = this.options;\n                return {\n                    background: options.background,\n                    border: options.border,\n                    margin: options.margin,\n                    padding: options.padding,\n                    type: options.type,\n                    size: options.width,\n                    visible: options.visible\n                };\n            },\n            createVisual: function () {\n                this.visual = this.createElement();\n            }\n        });\n        setDefaultOptions(ShapeElement, {\n            type: CIRCLE,\n            align: CENTER,\n            vAlign: CENTER\n        });\n        var LINEAR = 'linear';\n        var RADIAL = 'radial';\n        var GRADIENTS = {\n            glass: {\n                type: LINEAR,\n                rotation: 0,\n                stops: [\n                    {\n                        offset: 0,\n                        color: WHITE,\n                        opacity: 0\n                    },\n                    {\n                        offset: 0.25,\n                        color: WHITE,\n                        opacity: 0.3\n                    },\n                    {\n                        offset: 1,\n                        color: WHITE,\n                        opacity: 0\n                    }\n                ]\n            },\n            sharpBevel: {\n                type: RADIAL,\n                stops: [\n                    {\n                        offset: 0,\n                        color: WHITE,\n                        opacity: 0.55\n                    },\n                    {\n                        offset: 0.65,\n                        color: WHITE,\n                        opacity: 0\n                    },\n                    {\n                        offset: 0.95,\n                        color: WHITE,\n                        opacity: 0.25\n                    }\n                ]\n            },\n            roundedBevel: {\n                type: RADIAL,\n                stops: [\n                    {\n                        offset: 0.33,\n                        color: WHITE,\n                        opacity: 0.06\n                    },\n                    {\n                        offset: 0.83,\n                        color: WHITE,\n                        opacity: 0.2\n                    },\n                    {\n                        offset: 0.95,\n                        color: WHITE,\n                        opacity: 0\n                    }\n                ]\n            },\n            roundedGlass: {\n                type: RADIAL,\n                supportVML: false,\n                stops: [\n                    {\n                        offset: 0,\n                        color: WHITE,\n                        opacity: 0\n                    },\n                    {\n                        offset: 0.5,\n                        color: WHITE,\n                        opacity: 0.3\n                    },\n                    {\n                        offset: 0.99,\n                        color: WHITE,\n                        opacity: 0\n                    }\n                ]\n            },\n            sharpGlass: {\n                type: RADIAL,\n                supportVML: false,\n                stops: [\n                    {\n                        offset: 0,\n                        color: WHITE,\n                        opacity: 0.2\n                    },\n                    {\n                        offset: 0.15,\n                        color: WHITE,\n                        opacity: 0.15\n                    },\n                    {\n                        offset: 0.17,\n                        color: WHITE,\n                        opacity: 0.35\n                    },\n                    {\n                        offset: 0.85,\n                        color: WHITE,\n                        opacity: 0.05\n                    },\n                    {\n                        offset: 0.87,\n                        color: WHITE,\n                        opacity: 0.15\n                    },\n                    {\n                        offset: 0.99,\n                        color: WHITE,\n                        opacity: 0\n                    }\n                ]\n            },\n            bubbleShadow: {\n                type: RADIAL,\n                center: [\n                    0.5,\n                    0.5\n                ],\n                radius: 0.5\n            }\n        };\n        function boxDiff(r, s) {\n            if (r.x1 === s.x1 && r.y1 === s.y1 && r.x2 === s.x2 && r.y2 === s.y2) {\n                return s;\n            }\n            var a = Math.min(r.x1, s.x1);\n            var b = Math.max(r.x1, s.x1);\n            var c = Math.min(r.x2, s.x2);\n            var d = Math.max(r.x2, s.x2);\n            var e = Math.min(r.y1, s.y1);\n            var f = Math.max(r.y1, s.y1);\n            var g = Math.min(r.y2, s.y2);\n            var h = Math.max(r.y2, s.y2);\n            var boxes = [];\n            boxes[0] = new Box(b, e, c, f);\n            boxes[1] = new Box(a, f, b, g);\n            boxes[2] = new Box(c, f, d, g);\n            boxes[3] = new Box(b, g, c, h);\n            if (r.x1 === a && r.y1 === e || s.x1 === a && s.y1 === e) {\n                boxes[4] = new Box(a, e, b, f);\n                boxes[5] = new Box(c, g, d, h);\n            } else {\n                boxes[4] = new Box(c, e, d, f);\n                boxes[5] = new Box(a, g, b, h);\n            }\n            return grep(boxes, function (box) {\n                return box.height() > 0 && box.width() > 0;\n            })[0];\n        }\n        var RootElement = ChartElement.extend({\n            init: function (options) {\n                ChartElement.fn.init.call(this, options);\n                var rootOptions = this.options;\n                rootOptions.width = parseInt(rootOptions.width, 10);\n                rootOptions.height = parseInt(rootOptions.height, 10);\n                this.gradients = {};\n            },\n            reflow: function () {\n                var ref = this;\n                var options = ref.options;\n                var children = ref.children;\n                var currentBox = new Box(0, 0, options.width, options.height);\n                this.box = currentBox.unpad(options.margin);\n                for (var i = 0; i < children.length; i++) {\n                    children[i].reflow(currentBox);\n                    currentBox = boxDiff(currentBox, children[i].box) || new Box();\n                }\n            },\n            createVisual: function () {\n                this.visual = new Group();\n                this.createBackground();\n            },\n            createBackground: function () {\n                var options = this.options;\n                var border = options.border || {};\n                var box = this.box.clone().pad(options.margin).unpad(border.width);\n                var background = Path.fromRect(box.toRect(), {\n                    stroke: {\n                        color: border.width ? border.color : '',\n                        width: border.width,\n                        dashType: border.dashType\n                    },\n                    fill: {\n                        color: options.background,\n                        opacity: options.opacity\n                    },\n                    zIndex: -10\n                });\n                this.visual.append(background);\n            },\n            getRoot: function () {\n                return this;\n            },\n            createGradient: function (options) {\n                var gradients = this.gradients;\n                var hashCode = objectKey(options);\n                var gradient = GRADIENTS[options.gradient];\n                var drawingGradient;\n                if (gradients[hashCode]) {\n                    drawingGradient = gradients[hashCode];\n                } else {\n                    var gradientOptions = $.extend({}, gradient, options);\n                    if (gradient.type === 'linear') {\n                        drawingGradient = new drawing.LinearGradient(gradientOptions);\n                    } else {\n                        if (options.innerRadius) {\n                            gradientOptions.stops = innerRadialStops(gradientOptions);\n                        }\n                        drawingGradient = new drawing.RadialGradient(gradientOptions);\n                        drawingGradient.supportVML = gradient.supportVML !== false;\n                    }\n                    gradients[hashCode] = drawingGradient;\n                }\n                return drawingGradient;\n            },\n            cleanGradients: function () {\n                var gradients = this.gradients;\n                for (var hashCode in gradients) {\n                    gradients[hashCode]._observers = [];\n                }\n            },\n            size: function () {\n                var options = this.options;\n                return new Box(0, 0, options.width, options.height);\n            }\n        });\n        setDefaultOptions(RootElement, {\n            width: DEFAULT_WIDTH,\n            height: DEFAULT_HEIGHT,\n            background: WHITE,\n            border: {\n                color: BLACK,\n                width: 0\n            },\n            margin: getSpacing(5),\n            zIndex: -2\n        });\n        function innerRadialStops(options) {\n            var stops = options.stops;\n            var usedSpace = options.innerRadius / options.radius * 100;\n            var length = stops.length;\n            var currentStops = [];\n            for (var i = 0; i < length; i++) {\n                var currentStop = $.extend({}, stops[i]);\n                currentStop.offset = (currentStop.offset * (100 - usedSpace) + usedSpace) / 100;\n                currentStops.push(currentStop);\n            }\n            return currentStops;\n        }\n        var FloatElement = ChartElement.extend({\n            init: function (options) {\n                ChartElement.fn.init.call(this, options);\n                this._initDirection();\n            },\n            _initDirection: function () {\n                var options = this.options;\n                if (options.vertical) {\n                    this.groupAxis = X;\n                    this.elementAxis = Y;\n                    this.groupSizeField = WIDTH;\n                    this.elementSizeField = HEIGHT;\n                    this.groupSpacing = options.spacing;\n                    this.elementSpacing = options.vSpacing;\n                } else {\n                    this.groupAxis = Y;\n                    this.elementAxis = X;\n                    this.groupSizeField = HEIGHT;\n                    this.elementSizeField = WIDTH;\n                    this.groupSpacing = options.vSpacing;\n                    this.elementSpacing = options.spacing;\n                }\n            },\n            reflow: function (targetBox) {\n                this.box = targetBox.clone();\n                this.reflowChildren();\n            },\n            reflowChildren: function () {\n                var this$1 = this;\n                var ref = this;\n                var box = ref.box;\n                var elementAxis = ref.elementAxis;\n                var groupAxis = ref.groupAxis;\n                var elementSizeField = ref.elementSizeField;\n                var groupSizeField = ref.groupSizeField;\n                var ref$1 = this.groupOptions();\n                var groups = ref$1.groups;\n                var groupsSize = ref$1.groupsSize;\n                var maxGroupElementsSize = ref$1.maxGroupElementsSize;\n                var groupsCount = groups.length;\n                var groupsStart = box[groupAxis + 1] + this.alignStart(groupsSize, box[groupSizeField]());\n                if (groupsCount) {\n                    var groupStart = groupsStart;\n                    for (var groupIdx = 0; groupIdx < groupsCount; groupIdx++) {\n                        var group = groups[groupIdx];\n                        var groupElements = group.groupElements;\n                        var elementStart = box[elementAxis + 1];\n                        var groupElementsCount = groupElements.length;\n                        for (var idx = 0; idx < groupElementsCount; idx++) {\n                            var element = groupElements[idx];\n                            var elementSize$$1 = this$1.elementSize(element);\n                            var groupElementStart = groupStart + this$1.alignStart(elementSize$$1[groupSizeField], group.groupSize);\n                            var elementBox = new Box();\n                            elementBox[groupAxis + 1] = groupElementStart;\n                            elementBox[groupAxis + 2] = groupElementStart + elementSize$$1[groupSizeField];\n                            elementBox[elementAxis + 1] = elementStart;\n                            elementBox[elementAxis + 2] = elementStart + elementSize$$1[elementSizeField];\n                            element.reflow(elementBox);\n                            elementStart += elementSize$$1[elementSizeField] + this$1.elementSpacing;\n                        }\n                        groupStart += group.groupSize + this$1.groupSpacing;\n                    }\n                    box[groupAxis + 1] = groupsStart;\n                    box[groupAxis + 2] = groupsStart + groupsSize;\n                    box[elementAxis + 2] = box[elementAxis + 1] + maxGroupElementsSize;\n                }\n            },\n            alignStart: function (size, maxSize) {\n                var start = 0;\n                var align = this.options.align;\n                if (align === RIGHT || align === BOTTOM) {\n                    start = maxSize - size;\n                } else if (align === CENTER) {\n                    start = (maxSize - size) / 2;\n                }\n                return start;\n            },\n            groupOptions: function () {\n                var this$1 = this;\n                var ref = this;\n                var box = ref.box;\n                var children = ref.children;\n                var elementSizeField = ref.elementSizeField;\n                var groupSizeField = ref.groupSizeField;\n                var elementSpacing = ref.elementSpacing;\n                var groupSpacing = ref.groupSpacing;\n                var maxSize = round(box[elementSizeField]());\n                var childrenCount = children.length;\n                var groups = [];\n                var groupSize = 0;\n                var groupElementsSize = 0;\n                var groupsSize = 0;\n                var maxGroupElementsSize = 0;\n                var groupElements = [];\n                for (var idx = 0; idx < childrenCount; idx++) {\n                    var element = children[idx];\n                    if (!element.box) {\n                        element.reflow(box);\n                    }\n                    var elementSize$$1 = this$1.elementSize(element);\n                    if (this$1.options.wrap && round(groupElementsSize + elementSpacing + elementSize$$1[elementSizeField]) > maxSize) {\n                        groups.push({\n                            groupElements: groupElements,\n                            groupSize: groupSize,\n                            groupElementsSize: groupElementsSize\n                        });\n                        maxGroupElementsSize = Math.max(maxGroupElementsSize, groupElementsSize);\n                        groupsSize += groupSpacing + groupSize;\n                        groupSize = 0;\n                        groupElementsSize = 0;\n                        groupElements = [];\n                    }\n                    groupSize = Math.max(groupSize, elementSize$$1[groupSizeField]);\n                    if (groupElementsSize > 0) {\n                        groupElementsSize += elementSpacing;\n                    }\n                    groupElementsSize += elementSize$$1[elementSizeField];\n                    groupElements.push(element);\n                }\n                groups.push({\n                    groupElements: groupElements,\n                    groupSize: groupSize,\n                    groupElementsSize: groupElementsSize\n                });\n                maxGroupElementsSize = Math.max(maxGroupElementsSize, groupElementsSize);\n                groupsSize += groupSize;\n                return {\n                    groups: groups,\n                    groupsSize: groupsSize,\n                    maxGroupElementsSize: maxGroupElementsSize\n                };\n            },\n            elementSize: function (element) {\n                return {\n                    width: element.box.width(),\n                    height: element.box.height()\n                };\n            },\n            createVisual: function () {\n            }\n        });\n        setDefaultOptions(FloatElement, {\n            vertical: true,\n            wrap: true,\n            vSpacing: 0,\n            spacing: 0\n        });\n        var DrawingText = drawing.Text;\n        var Text = ChartElement.extend({\n            init: function (content, options) {\n                ChartElement.fn.init.call(this, options);\n                this.content = content;\n                this.reflow(new Box());\n            },\n            reflow: function (targetBox) {\n                var options = this.options;\n                var size = options.size = util.measureText(this.content, { font: options.font });\n                this.baseline = size.baseline;\n                this.box = new Box(targetBox.x1, targetBox.y1, targetBox.x1 + size.width, targetBox.y1 + size.height);\n            },\n            createVisual: function () {\n                var ref = this.options;\n                var font = ref.font;\n                var color = ref.color;\n                var opacity = ref.opacity;\n                var cursor = ref.cursor;\n                this.visual = new DrawingText(this.content, this.box.toRect().topLeft(), {\n                    font: font,\n                    fill: {\n                        color: color,\n                        opacity: opacity\n                    },\n                    cursor: cursor\n                });\n            }\n        });\n        setDefaultOptions(Text, {\n            font: DEFAULT_FONT,\n            color: BLACK\n        });\n        function rectToBox(rect) {\n            var origin = rect.origin;\n            var bottomRight = rect.bottomRight();\n            return new Box(origin.x, origin.y, bottomRight.x, bottomRight.y);\n        }\n        var ROWS_SPLIT_REGEX = /\\n/m;\n        var TextBox = BoxElement.extend({\n            init: function (content, options, data) {\n                BoxElement.fn.init.call(this, options);\n                this.content = content;\n                this.data = data;\n                this._initContainer();\n                if (this.options._autoReflow !== false) {\n                    this.reflow(new Box());\n                }\n            },\n            _initContainer: function () {\n                var options = this.options;\n                var rows = String(this.content).split(ROWS_SPLIT_REGEX);\n                var floatElement = new FloatElement({\n                    vertical: true,\n                    align: options.align,\n                    wrap: false\n                });\n                var textOptions = deepExtend({}, options, {\n                    opacity: 1,\n                    animation: null\n                });\n                this.container = floatElement;\n                this.append(floatElement);\n                for (var rowIdx = 0; rowIdx < rows.length; rowIdx++) {\n                    var text = new Text(rows[rowIdx].trim(), textOptions);\n                    floatElement.append(text);\n                }\n            },\n            reflow: function (targetBox) {\n                var options = this.options;\n                var visualFn = options.visual;\n                this.container.options.align = options.align;\n                if (visualFn && !this._boxReflow) {\n                    var visualBox = targetBox;\n                    if (!visualBox.hasSize()) {\n                        this._boxReflow = true;\n                        this.reflow(visualBox);\n                        this._boxReflow = false;\n                        visualBox = this.box;\n                    }\n                    var visual = this.visual = visualFn(this.visualContext(visualBox));\n                    if (visual) {\n                        visualBox = rectToBox(visual.clippedBBox() || new Rect());\n                        visual.options.zIndex = options.zIndex;\n                    }\n                    this.box = this.contentBox = this.paddingBox = visualBox;\n                } else {\n                    BoxElement.fn.reflow.call(this, targetBox);\n                    if (options.rotation) {\n                        var margin = getSpacing(options.margin);\n                        var box = this.box.unpad(margin);\n                        this.targetBox = targetBox;\n                        this.normalBox = box.clone();\n                        box = this.rotate();\n                        box.translate(margin.left - margin.right, margin.top - margin.bottom);\n                        this.rotatedBox = box.clone();\n                        box.pad(margin);\n                    }\n                }\n            },\n            createVisual: function () {\n                var options = this.options;\n                this.visual = new Group({\n                    transform: this.rotationTransform(),\n                    zIndex: options.zIndex,\n                    noclip: options.noclip\n                });\n                if (this.hasBox()) {\n                    var box = Path.fromRect(this.paddingBox.toRect(), this.visualStyle());\n                    this.visual.append(box);\n                }\n            },\n            renderVisual: function () {\n                if (!this.options.visible) {\n                    return;\n                }\n                if (this.options.visual) {\n                    var visual = this.visual;\n                    if (visual && !defined(visual.options.noclip)) {\n                        visual.options.noclip = this.options.noclip;\n                    }\n                    this.addVisual();\n                    this.createAnimation();\n                } else {\n                    BoxElement.fn.renderVisual.call(this);\n                }\n            },\n            visualContext: function (targetBox) {\n                var this$1 = this;\n                var context = {\n                    text: this.content,\n                    rect: targetBox.toRect(),\n                    sender: this.getSender(),\n                    options: this.options,\n                    createVisual: function () {\n                        this$1._boxReflow = true;\n                        this$1.reflow(targetBox);\n                        this$1._boxReflow = false;\n                        return this$1.getDefaultVisual();\n                    }\n                };\n                if (this.data) {\n                    $.extend(context, this.data);\n                }\n                return context;\n            },\n            getDefaultVisual: function () {\n                this.createVisual();\n                this.renderChildren();\n                var visual = this.visual;\n                delete this.visual;\n                return visual;\n            },\n            rotate: function () {\n                var options = this.options;\n                this.box.rotate(options.rotation);\n                this.align(this.targetBox, X, options.align);\n                this.align(this.targetBox, Y, options.vAlign);\n                return this.box;\n            },\n            rotationTransform: function () {\n                var rotation = this.options.rotation;\n                if (!rotation) {\n                    return null;\n                }\n                var ref = this.normalBox.center();\n                var cx = ref.x;\n                var cy = ref.y;\n                var boxCenter = this.rotatedBox.center();\n                return geometryTransform().translate(boxCenter.x - cx, boxCenter.y - cy).rotate(rotation, [\n                    cx,\n                    cy\n                ]);\n            }\n        });\n        var Title = ChartElement.extend({\n            init: function (options) {\n                ChartElement.fn.init.call(this, options);\n                this.append(new TextBox(this.options.text, $.extend({}, this.options, { vAlign: this.options.position })));\n            },\n            reflow: function (targetBox) {\n                ChartElement.fn.reflow.call(this, targetBox);\n                this.box.snapTo(targetBox, X);\n            }\n        });\n        Title.buildTitle = function (options, parent, defaultOptions) {\n            var titleOptions = options;\n            if (typeof options === 'string') {\n                titleOptions = { text: options };\n            }\n            titleOptions = $.extend({ visible: true }, defaultOptions, titleOptions);\n            var title;\n            if (titleOptions && titleOptions.visible && titleOptions.text) {\n                title = new Title(titleOptions);\n                parent.append(title);\n            }\n            return title;\n        };\n        setDefaultOptions(Title, {\n            color: BLACK,\n            position: TOP,\n            align: CENTER,\n            margin: getSpacing(5),\n            padding: getSpacing(5)\n        });\n        var AxisLabel = TextBox.extend({\n            init: function (value, text, index, dataItem, options) {\n                TextBox.fn.init.call(this, text, options);\n                this.text = text;\n                this.value = value;\n                this.index = index;\n                this.dataItem = dataItem;\n                this.reflow(new Box());\n            },\n            visualContext: function (targetBox) {\n                var context = TextBox.fn.visualContext.call(this, targetBox);\n                context.value = this.value;\n                context.dataItem = this.dataItem;\n                context.format = this.options.format;\n                context.culture = this.options.culture;\n                return context;\n            },\n            click: function (widget, e) {\n                widget.trigger(AXIS_LABEL_CLICK, {\n                    element: eventElement(e),\n                    value: this.value,\n                    text: this.text,\n                    index: this.index,\n                    dataItem: this.dataItem,\n                    axis: this.parent.options\n                });\n            },\n            rotate: function () {\n                if (this.options.alignRotation !== CENTER) {\n                    var box = this.normalBox.toRect();\n                    var transform = this.rotationTransform();\n                    this.box = rectToBox(box.bbox(transform.matrix()));\n                } else {\n                    TextBox.fn.rotate.call(this);\n                }\n                return this.box;\n            },\n            rotationTransform: function () {\n                var options = this.options;\n                var rotation = options.rotation;\n                if (!rotation) {\n                    return null;\n                }\n                if (options.alignRotation === CENTER) {\n                    return TextBox.fn.rotationTransform.call(this);\n                }\n                var rotationMatrix = geometryTransform().rotate(rotation).matrix();\n                var box = this.normalBox.toRect();\n                var rect = this.targetBox.toRect();\n                var rotationOrigin = options.rotationOrigin || TOP;\n                var alignAxis = rotationOrigin === TOP || rotationOrigin === BOTTOM ? X : Y;\n                var distanceAxis = rotationOrigin === TOP || rotationOrigin === BOTTOM ? Y : X;\n                var axisAnchor = rotationOrigin === TOP || rotationOrigin === LEFT ? rect.origin : rect.bottomRight();\n                var topLeft = box.topLeft().transformCopy(rotationMatrix);\n                var topRight = box.topRight().transformCopy(rotationMatrix);\n                var bottomRight = box.bottomRight().transformCopy(rotationMatrix);\n                var bottomLeft = box.bottomLeft().transformCopy(rotationMatrix);\n                var rotatedBox = Rect.fromPoints(topLeft, topRight, bottomRight, bottomLeft);\n                var translate = {};\n                translate[distanceAxis] = rect.origin[distanceAxis] - rotatedBox.origin[distanceAxis];\n                var distanceLeft = Math.abs(topLeft[distanceAxis] + translate[distanceAxis] - axisAnchor[distanceAxis]);\n                var distanceRight = Math.abs(topRight[distanceAxis] + translate[distanceAxis] - axisAnchor[distanceAxis]);\n                var alignStart, alignEnd;\n                if (round(distanceLeft, DEFAULT_PRECISION) === round(distanceRight, DEFAULT_PRECISION)) {\n                    alignStart = topLeft;\n                    alignEnd = topRight;\n                } else if (distanceRight < distanceLeft) {\n                    alignStart = topRight;\n                    alignEnd = bottomRight;\n                } else {\n                    alignStart = topLeft;\n                    alignEnd = bottomLeft;\n                }\n                var alignCenter = alignStart[alignAxis] + (alignEnd[alignAxis] - alignStart[alignAxis]) / 2;\n                translate[alignAxis] = rect.center()[alignAxis] - alignCenter;\n                return geometryTransform().translate(translate.x, translate.y).rotate(rotation);\n            }\n        });\n        setDefaultOptions(AxisLabel, { _autoReflow: false });\n        var DEFAULT_ICON_SIZE = 7;\n        var DEFAULT_LABEL_COLOR = '#fff';\n        var Note = BoxElement.extend({\n            init: function (fields, options, chartService) {\n                BoxElement.fn.init.call(this, options);\n                this.fields = fields;\n                this.chartService = chartService;\n                this.render();\n            },\n            hide: function () {\n                this.options.visible = false;\n            },\n            show: function () {\n                this.options.visible = true;\n            },\n            render: function () {\n                var this$1 = this;\n                var options = this.options;\n                if (options.visible) {\n                    var label = options.label;\n                    var icon = options.icon;\n                    var box = new Box();\n                    var childAlias = function () {\n                        return this$1;\n                    };\n                    var size = icon.size;\n                    var text = this.fields.text;\n                    var width, height;\n                    if (defined(label) && label.visible) {\n                        var noteTemplate = getTemplate(label);\n                        if (noteTemplate) {\n                            text = noteTemplate(this.fields);\n                        } else if (label.format) {\n                            text = this.chartService.format.auto(label.format, text);\n                        }\n                        if (!label.color) {\n                            label.color = label.position === INSIDE ? DEFAULT_LABEL_COLOR : icon.background;\n                        }\n                        this.label = new TextBox(text, deepExtend({}, label));\n                        this.label.aliasFor = childAlias;\n                        if (label.position === INSIDE && !defined(size)) {\n                            if (icon.type === CIRCLE) {\n                                size = Math.max(this.label.box.width(), this.label.box.height());\n                            } else {\n                                width = this.label.box.width();\n                                height = this.label.box.height();\n                            }\n                            box.wrap(this.label.box);\n                        }\n                    }\n                    icon.width = width || size || DEFAULT_ICON_SIZE;\n                    icon.height = height || size || DEFAULT_ICON_SIZE;\n                    var marker = new ShapeElement(deepExtend({}, icon));\n                    marker.aliasFor = childAlias;\n                    this.marker = marker;\n                    this.append(marker);\n                    if (this.label) {\n                        this.append(this.label);\n                    }\n                    marker.reflow(new Box());\n                    this.wrapperBox = box.wrap(marker.box);\n                }\n            },\n            reflow: function (targetBox) {\n                var ref = this;\n                var options = ref.options;\n                var label = ref.label;\n                var marker = ref.marker;\n                var wrapperBox = ref.wrapperBox;\n                var center = targetBox.center();\n                var length = options.line.length;\n                var position = options.position;\n                if (options.visible) {\n                    var lineStart, box, contentBox;\n                    if (inArray(position, [\n                            LEFT,\n                            RIGHT\n                        ])) {\n                        if (position === LEFT) {\n                            contentBox = wrapperBox.alignTo(targetBox, position).translate(-length, targetBox.center().y - wrapperBox.center().y);\n                            if (options.line.visible) {\n                                lineStart = [\n                                    targetBox.x1,\n                                    center.y\n                                ];\n                                this.linePoints = [\n                                    lineStart,\n                                    [\n                                        contentBox.x2,\n                                        center.y\n                                    ]\n                                ];\n                                box = contentBox.clone().wrapPoint(lineStart);\n                            }\n                        } else {\n                            contentBox = wrapperBox.alignTo(targetBox, position).translate(length, targetBox.center().y - wrapperBox.center().y);\n                            if (options.line.visible) {\n                                lineStart = [\n                                    targetBox.x2,\n                                    center.y\n                                ];\n                                this.linePoints = [\n                                    lineStart,\n                                    [\n                                        contentBox.x1,\n                                        center.y\n                                    ]\n                                ];\n                                box = contentBox.clone().wrapPoint(lineStart);\n                            }\n                        }\n                    } else {\n                        if (position === BOTTOM) {\n                            contentBox = wrapperBox.alignTo(targetBox, position).translate(targetBox.center().x - wrapperBox.center().x, length);\n                            if (options.line.visible) {\n                                lineStart = [\n                                    center.x,\n                                    targetBox.y2\n                                ];\n                                this.linePoints = [\n                                    lineStart,\n                                    [\n                                        center.x,\n                                        contentBox.y1\n                                    ]\n                                ];\n                                box = contentBox.clone().wrapPoint(lineStart);\n                            }\n                        } else {\n                            contentBox = wrapperBox.alignTo(targetBox, position).translate(targetBox.center().x - wrapperBox.center().x, -length);\n                            if (options.line.visible) {\n                                lineStart = [\n                                    center.x,\n                                    targetBox.y1\n                                ];\n                                this.linePoints = [\n                                    lineStart,\n                                    [\n                                        center.x,\n                                        contentBox.y2\n                                    ]\n                                ];\n                                box = contentBox.clone().wrapPoint(lineStart);\n                            }\n                        }\n                    }\n                    if (marker) {\n                        marker.reflow(contentBox);\n                    }\n                    if (label) {\n                        label.reflow(contentBox);\n                        if (marker) {\n                            if (options.label.position === OUTSIDE) {\n                                label.box.alignTo(marker.box, position);\n                            }\n                            label.reflow(label.box);\n                        }\n                    }\n                    this.contentBox = contentBox;\n                    this.targetBox = targetBox;\n                    this.box = box || contentBox;\n                }\n            },\n            createVisual: function () {\n                BoxElement.fn.createVisual.call(this);\n                this.visual.options.noclip = this.options.noclip;\n                if (this.options.visible) {\n                    this.createLine();\n                }\n            },\n            renderVisual: function () {\n                var this$1 = this;\n                var options = this.options;\n                var customVisual = options.visual;\n                if (options.visible && customVisual) {\n                    this.visual = customVisual($.extend(this.fields, {\n                        sender: this.getSender(),\n                        rect: this.targetBox.toRect(),\n                        options: {\n                            background: options.background,\n                            border: options.background,\n                            icon: options.icon,\n                            label: options.label,\n                            line: options.line,\n                            position: options.position,\n                            visible: options.visible\n                        },\n                        createVisual: function () {\n                            this$1.createVisual();\n                            this$1.renderChildren();\n                            var defaultVisual = this$1.visual;\n                            delete this$1.visual;\n                            return defaultVisual;\n                        }\n                    }));\n                    this.addVisual();\n                } else {\n                    BoxElement.fn.renderVisual.call(this);\n                }\n            },\n            createLine: function () {\n                var options = this.options.line;\n                if (this.linePoints) {\n                    var path = Path.fromPoints(this.linePoints, {\n                        stroke: {\n                            color: options.color,\n                            width: options.width,\n                            dashType: options.dashType\n                        }\n                    });\n                    alignPathToPixel(path);\n                    this.visual.append(path);\n                }\n            },\n            click: function (widget, e) {\n                var args = this.eventArgs(e);\n                if (!widget.trigger(NOTE_CLICK, args)) {\n                    e.preventDefault();\n                }\n            },\n            over: function (widget, e) {\n                var args = this.eventArgs(e);\n                if (!widget.trigger(NOTE_HOVER, args)) {\n                    e.preventDefault();\n                }\n            },\n            out: function (widget, e) {\n                var args = this.eventArgs(e);\n                widget.trigger(NOTE_LEAVE, args);\n            },\n            eventArgs: function (e) {\n                var options = this.options;\n                return $.extend(this.fields, {\n                    element: eventElement(e),\n                    text: defined(options.label) ? options.label.text : '',\n                    visual: this.visual\n                });\n            }\n        });\n        setDefaultOptions(Note, {\n            icon: {\n                visible: true,\n                type: CIRCLE\n            },\n            label: {\n                position: INSIDE,\n                visible: true,\n                align: CENTER,\n                vAlign: CENTER\n            },\n            line: { visible: true },\n            visible: true,\n            position: TOP,\n            zIndex: 2\n        });\n        function createAxisTick(options, tickOptions) {\n            var tickX = options.tickX;\n            var tickY = options.tickY;\n            var position = options.position;\n            var tick = new Path({\n                stroke: {\n                    width: tickOptions.width,\n                    color: tickOptions.color\n                }\n            });\n            if (options.vertical) {\n                tick.moveTo(tickX, position).lineTo(tickX + tickOptions.size, position);\n            } else {\n                tick.moveTo(position, tickY).lineTo(position, tickY + tickOptions.size);\n            }\n            alignPathToPixel(tick);\n            return tick;\n        }\n        function createAxisGridLine(options, gridLine) {\n            var lineStart = options.lineStart;\n            var lineEnd = options.lineEnd;\n            var position = options.position;\n            var line = new Path({\n                stroke: {\n                    width: gridLine.width,\n                    color: gridLine.color,\n                    dashType: gridLine.dashType\n                }\n            });\n            if (options.vertical) {\n                line.moveTo(lineStart, position).lineTo(lineEnd, position);\n            } else {\n                line.moveTo(position, lineStart).lineTo(position, lineEnd);\n            }\n            alignPathToPixel(line);\n            return line;\n        }\n        var Axis = ChartElement.extend({\n            init: function (options, chartService) {\n                if (chartService === void 0) {\n                    chartService = new ChartService();\n                }\n                ChartElement.fn.init.call(this, options);\n                this.chartService = chartService;\n                if (!this.options.visible) {\n                    this.options = deepExtend({}, this.options, {\n                        labels: { visible: false },\n                        line: { visible: false },\n                        margin: 0,\n                        majorTickSize: 0,\n                        minorTickSize: 0\n                    });\n                }\n                this.options.minorTicks = deepExtend({}, {\n                    color: this.options.line.color,\n                    width: this.options.line.width,\n                    visible: this.options.minorTickType !== NONE\n                }, this.options.minorTicks, {\n                    size: this.options.minorTickSize,\n                    align: this.options.minorTickType\n                });\n                this.options.majorTicks = deepExtend({}, {\n                    color: this.options.line.color,\n                    width: this.options.line.width,\n                    visible: this.options.majorTickType !== NONE\n                }, this.options.majorTicks, {\n                    size: this.options.majorTickSize,\n                    align: this.options.majorTickType\n                });\n                this.initFields();\n                if (!this.options._deferLabels) {\n                    this.createLabels();\n                }\n                this.createTitle();\n                this.createNotes();\n            },\n            initFields: function () {\n            },\n            labelsRange: function () {\n                return {\n                    min: this.options.labels.skip,\n                    max: this.labelsCount()\n                };\n            },\n            createLabels: function () {\n                var this$1 = this;\n                var options = this.options;\n                var align = options.vertical ? RIGHT : CENTER;\n                var labelOptions = deepExtend({}, options.labels, {\n                    align: align,\n                    zIndex: options.zIndex\n                });\n                var step = Math.max(1, labelOptions.step);\n                this.clearLabels();\n                if (labelOptions.visible) {\n                    var range = this.labelsRange();\n                    var rotation = labelOptions.rotation;\n                    if (isObject(rotation)) {\n                        labelOptions.alignRotation = rotation.align;\n                        labelOptions.rotation = rotation.angle;\n                    }\n                    if (labelOptions.rotation === 'auto') {\n                        labelOptions.rotation = 0;\n                        options.autoRotateLabels = true;\n                    }\n                    for (var idx = range.min; idx < range.max; idx += step) {\n                        var label = this$1.createAxisLabel(idx, labelOptions);\n                        if (label) {\n                            this$1.append(label);\n                            this$1.labels.push(label);\n                        }\n                    }\n                }\n            },\n            clearLabels: function () {\n                this.children = grep(this.children, function (child) {\n                    return !(child instanceof AxisLabel);\n                });\n                this.labels = [];\n            },\n            clearTitle: function () {\n                var this$1 = this;\n                if (this.title) {\n                    this.children = grep(this.children, function (child) {\n                        return child !== this$1.title;\n                    });\n                    this.title = undefined;\n                }\n            },\n            clear: function () {\n                this.clearLabels();\n                this.clearTitle();\n            },\n            lineBox: function () {\n                var ref = this;\n                var options = ref.options;\n                var box = ref.box;\n                var vertical = options.vertical;\n                var mirror = options.labels.mirror;\n                var axisX = mirror ? box.x1 : box.x2;\n                var axisY = mirror ? box.y2 : box.y1;\n                var lineWidth = options.line.width || 0;\n                return vertical ? new Box(axisX, box.y1, axisX, box.y2 - lineWidth) : new Box(box.x1, axisY, box.x2 - lineWidth, axisY);\n            },\n            createTitle: function () {\n                var options = this.options;\n                var titleOptions = deepExtend({\n                    rotation: options.vertical ? -90 : 0,\n                    text: '',\n                    zIndex: 1,\n                    visualSize: true\n                }, options.title);\n                if (titleOptions.visible && titleOptions.text) {\n                    var title = new TextBox(titleOptions.text, titleOptions);\n                    this.append(title);\n                    this.title = title;\n                }\n            },\n            createNotes: function () {\n                var this$1 = this;\n                var options = this.options;\n                var notes = options.notes;\n                var items = notes.data || [];\n                this.notes = [];\n                for (var i = 0; i < items.length; i++) {\n                    var item = deepExtend({}, notes, items[i]);\n                    item.value = this$1.parseNoteValue(item.value);\n                    var note = new Note({\n                        value: item.value,\n                        text: item.label.text,\n                        dataItem: item\n                    }, item, this$1.chartService);\n                    if (note.options.visible) {\n                        if (defined(note.options.position)) {\n                            if (options.vertical && !inArray(note.options.position, [\n                                    LEFT,\n                                    RIGHT\n                                ])) {\n                                note.options.position = options.reverse ? LEFT : RIGHT;\n                            } else if (!options.vertical && !inArray(note.options.position, [\n                                    TOP,\n                                    BOTTOM\n                                ])) {\n                                note.options.position = options.reverse ? BOTTOM : TOP;\n                            }\n                        } else {\n                            if (options.vertical) {\n                                note.options.position = options.reverse ? LEFT : RIGHT;\n                            } else {\n                                note.options.position = options.reverse ? BOTTOM : TOP;\n                            }\n                        }\n                        this$1.append(note);\n                        this$1.notes.push(note);\n                    }\n                }\n            },\n            parseNoteValue: function (value) {\n                return value;\n            },\n            renderVisual: function () {\n                ChartElement.fn.renderVisual.call(this);\n                this.createPlotBands();\n            },\n            createVisual: function () {\n                ChartElement.fn.createVisual.call(this);\n                this.createBackground();\n                this.createLine();\n            },\n            gridLinesVisual: function () {\n                var gridLines = this._gridLines;\n                if (!gridLines) {\n                    gridLines = this._gridLines = new Group({ zIndex: -2 });\n                    this.appendVisual(this._gridLines);\n                }\n                return gridLines;\n            },\n            createTicks: function (lineGroup) {\n                var options = this.options;\n                var lineBox = this.lineBox();\n                var mirror = options.labels.mirror;\n                var majorUnit = options.majorTicks.visible ? options.majorUnit : 0;\n                var tickLineOptions = { vertical: options.vertical };\n                function render(tickPositions, tickOptions, skipUnit) {\n                    var count = tickPositions.length;\n                    var step = Math.max(1, tickOptions.step);\n                    if (tickOptions.visible) {\n                        for (var i = tickOptions.skip; i < count; i += step) {\n                            if (defined(skipUnit) && i % skipUnit === 0) {\n                                continue;\n                            }\n                            tickLineOptions.tickX = mirror ? lineBox.x2 : lineBox.x2 - tickOptions.size;\n                            tickLineOptions.tickY = mirror ? lineBox.y1 - tickOptions.size : lineBox.y1;\n                            tickLineOptions.position = tickPositions[i];\n                            lineGroup.append(createAxisTick(tickLineOptions, tickOptions));\n                        }\n                    }\n                }\n                render(this.getMajorTickPositions(), options.majorTicks);\n                render(this.getMinorTickPositions(), options.minorTicks, majorUnit / options.minorUnit);\n            },\n            createLine: function () {\n                var options = this.options;\n                var line = options.line;\n                var lineBox = this.lineBox();\n                if (line.width > 0 && line.visible) {\n                    var path = new Path({\n                        stroke: {\n                            width: line.width,\n                            color: line.color,\n                            dashType: line.dashType\n                        }\n                    });\n                    path.moveTo(lineBox.x1, lineBox.y1).lineTo(lineBox.x2, lineBox.y2);\n                    if (options._alignLines) {\n                        alignPathToPixel(path);\n                    }\n                    var group = this._lineGroup = new Group();\n                    group.append(path);\n                    this.visual.append(group);\n                    this.createTicks(group);\n                }\n            },\n            getActualTickSize: function () {\n                var options = this.options;\n                var tickSize = 0;\n                if (options.majorTicks.visible && options.minorTicks.visible) {\n                    tickSize = Math.max(options.majorTicks.size, options.minorTicks.size);\n                } else if (options.majorTicks.visible) {\n                    tickSize = options.majorTicks.size;\n                } else if (options.minorTicks.visible) {\n                    tickSize = options.minorTicks.size;\n                }\n                return tickSize;\n            },\n            createBackground: function () {\n                var ref = this;\n                var options = ref.options;\n                var box = ref.box;\n                var background = options.background;\n                if (background) {\n                    this._backgroundPath = Path.fromRect(box.toRect(), {\n                        fill: { color: background },\n                        stroke: null\n                    });\n                    this.visual.append(this._backgroundPath);\n                }\n            },\n            createPlotBands: function () {\n                var this$1 = this;\n                var options = this.options;\n                var plotBands = options.plotBands || [];\n                var vertical = options.vertical;\n                var plotArea = this.plotArea;\n                if (plotBands.length === 0) {\n                    return;\n                }\n                var group = this._plotbandGroup = new Group({ zIndex: -1 });\n                var altAxis = grep(this.pane.axes, function (axis) {\n                    return axis.options.vertical !== this$1.options.vertical;\n                })[0];\n                for (var idx = 0; idx < plotBands.length; idx++) {\n                    var item = plotBands[idx];\n                    var slotX = void 0, slotY = void 0;\n                    if (vertical) {\n                        slotX = (altAxis || plotArea.axisX).lineBox();\n                        slotY = this$1.getSlot(item.from, item.to, true);\n                    } else {\n                        slotX = this$1.getSlot(item.from, item.to, true);\n                        slotY = (altAxis || plotArea.axisY).lineBox();\n                    }\n                    if (slotX.width() !== 0 && slotY.height() !== 0) {\n                        var bandRect = new Rect([\n                            slotX.x1,\n                            slotY.y1\n                        ], [\n                            slotX.width(),\n                            slotY.height()\n                        ]);\n                        var path = Path.fromRect(bandRect, {\n                            fill: {\n                                color: item.color,\n                                opacity: item.opacity\n                            },\n                            stroke: null\n                        });\n                        group.append(path);\n                    }\n                }\n                this.appendVisual(group);\n            },\n            createGridLines: function (altAxis) {\n                var options = this.options;\n                var minorGridLines = options.minorGridLines;\n                var majorGridLines = options.majorGridLines;\n                var minorUnit = options.minorUnit;\n                var vertical = options.vertical;\n                var axisLineVisible = altAxis.options.line.visible;\n                var majorUnit = majorGridLines.visible ? options.majorUnit : 0;\n                var lineBox = altAxis.lineBox();\n                var linePos = lineBox[vertical ? 'y1' : 'x1'];\n                var lineOptions = {\n                    lineStart: lineBox[vertical ? 'x1' : 'y1'],\n                    lineEnd: lineBox[vertical ? 'x2' : 'y2'],\n                    vertical: vertical\n                };\n                var majorTicks = [];\n                var container = this.gridLinesVisual();\n                function render(tickPositions, gridLine, skipUnit) {\n                    var count = tickPositions.length;\n                    var step = Math.max(1, gridLine.step);\n                    if (gridLine.visible) {\n                        for (var i = gridLine.skip; i < count; i += step) {\n                            var pos = round(tickPositions[i]);\n                            if (!inArray(pos, majorTicks)) {\n                                if (i % skipUnit !== 0 && (!axisLineVisible || linePos !== pos)) {\n                                    lineOptions.position = pos;\n                                    container.append(createAxisGridLine(lineOptions, gridLine));\n                                    majorTicks.push(pos);\n                                }\n                            }\n                        }\n                    }\n                }\n                render(this.getMajorTickPositions(), majorGridLines);\n                render(this.getMinorTickPositions(), minorGridLines, majorUnit / minorUnit);\n                return container.children;\n            },\n            reflow: function (box) {\n                var ref = this;\n                var options = ref.options;\n                var labels = ref.labels;\n                var title = ref.title;\n                var vertical = options.vertical;\n                var count = labels.length;\n                var sizeFn = vertical ? WIDTH : HEIGHT;\n                var titleSize = title ? title.box[sizeFn]() : 0;\n                var space = this.getActualTickSize() + options.margin + titleSize;\n                var rootBox = (this.getRoot() || {}).box || box;\n                var boxSize = rootBox[sizeFn]();\n                var maxLabelSize = 0;\n                for (var i = 0; i < count; i++) {\n                    var labelSize = labels[i].box[sizeFn]();\n                    if (labelSize + space <= boxSize) {\n                        maxLabelSize = Math.max(maxLabelSize, labelSize);\n                    }\n                }\n                if (vertical) {\n                    this.box = new Box(box.x1, box.y1, box.x1 + maxLabelSize + space, box.y2);\n                } else {\n                    this.box = new Box(box.x1, box.y1, box.x2, box.y1 + maxLabelSize + space);\n                }\n                this.arrangeTitle();\n                this.arrangeLabels();\n                this.arrangeNotes();\n            },\n            getLabelsTickPositions: function () {\n                return this.getMajorTickPositions();\n            },\n            labelTickIndex: function (label) {\n                return label.index;\n            },\n            arrangeLabels: function () {\n                var this$1 = this;\n                var ref = this;\n                var options = ref.options;\n                var labels = ref.labels;\n                var labelsBetweenTicks = this.labelsBetweenTicks();\n                var vertical = options.vertical;\n                var lineBox = this.lineBox();\n                var mirror = options.labels.mirror;\n                var tickPositions = this.getLabelsTickPositions();\n                var labelOffset = this.getActualTickSize() + options.margin;\n                for (var idx = 0; idx < labels.length; idx++) {\n                    var label = labels[idx];\n                    var tickIx = this$1.labelTickIndex(label);\n                    var labelSize = vertical ? label.box.height() : label.box.width();\n                    var labelPos = tickPositions[tickIx] - labelSize / 2;\n                    var labelBox = void 0, firstTickPosition = void 0, nextTickPosition = void 0;\n                    if (vertical) {\n                        if (labelsBetweenTicks) {\n                            firstTickPosition = tickPositions[tickIx];\n                            nextTickPosition = tickPositions[tickIx + 1];\n                            var middle = firstTickPosition + (nextTickPosition - firstTickPosition) / 2;\n                            labelPos = middle - labelSize / 2;\n                        }\n                        var labelX = lineBox.x2;\n                        if (mirror) {\n                            labelX += labelOffset;\n                            label.options.rotationOrigin = LEFT;\n                        } else {\n                            labelX -= labelOffset + label.box.width();\n                            label.options.rotationOrigin = RIGHT;\n                        }\n                        labelBox = label.box.move(labelX, labelPos);\n                    } else {\n                        if (labelsBetweenTicks) {\n                            firstTickPosition = tickPositions[tickIx];\n                            nextTickPosition = tickPositions[tickIx + 1];\n                        } else {\n                            firstTickPosition = labelPos;\n                            nextTickPosition = labelPos + labelSize;\n                        }\n                        var labelY = lineBox.y1;\n                        if (mirror) {\n                            labelY -= labelOffset + label.box.height();\n                            label.options.rotationOrigin = BOTTOM;\n                        } else {\n                            labelY += labelOffset;\n                            label.options.rotationOrigin = TOP;\n                        }\n                        labelBox = new Box(firstTickPosition, labelY, nextTickPosition, labelY + label.box.height());\n                    }\n                    label.reflow(labelBox);\n                }\n            },\n            autoRotateLabels: function () {\n                if (this.options.autoRotateLabels && !this.options.vertical) {\n                    var tickPositions = this.getMajorTickPositions();\n                    var labels = this.labels;\n                    var angle;\n                    for (var idx = 0; idx < labels.length; idx++) {\n                        var width = Math.abs(tickPositions[idx + 1] - tickPositions[idx]);\n                        var labelBox = labels[idx].box;\n                        if (labelBox.width() > width) {\n                            if (labelBox.height() > width) {\n                                angle = -90;\n                                break;\n                            }\n                            angle = -45;\n                        }\n                    }\n                    if (angle) {\n                        for (var idx$1 = 0; idx$1 < labels.length; idx$1++) {\n                            labels[idx$1].options.rotation = angle;\n                            labels[idx$1].reflow(new Box());\n                        }\n                        return true;\n                    }\n                }\n            },\n            arrangeTitle: function () {\n                var ref = this;\n                var options = ref.options;\n                var title = ref.title;\n                var mirror = options.labels.mirror;\n                var vertical = options.vertical;\n                if (title) {\n                    if (vertical) {\n                        title.options.align = mirror ? RIGHT : LEFT;\n                        title.options.vAlign = title.options.position;\n                    } else {\n                        title.options.align = title.options.position;\n                        title.options.vAlign = mirror ? TOP : BOTTOM;\n                    }\n                    title.reflow(this.box);\n                }\n            },\n            arrangeNotes: function () {\n                var this$1 = this;\n                for (var idx = 0; idx < this.notes.length; idx++) {\n                    var item = this$1.notes[idx];\n                    var value = item.options.value;\n                    var slot = void 0;\n                    if (defined(value)) {\n                        if (this$1.shouldRenderNote(value)) {\n                            item.show();\n                        } else {\n                            item.hide();\n                        }\n                        slot = this$1.noteSlot(value);\n                    } else {\n                        item.hide();\n                    }\n                    item.reflow(slot || this$1.lineBox());\n                }\n            },\n            noteSlot: function (value) {\n                return this.getSlot(value);\n            },\n            alignTo: function (secondAxis) {\n                var lineBox = secondAxis.lineBox();\n                var vertical = this.options.vertical;\n                var pos = vertical ? Y : X;\n                this.box.snapTo(lineBox, pos);\n                if (vertical) {\n                    this.box.shrink(0, this.lineBox().height() - lineBox.height());\n                } else {\n                    this.box.shrink(this.lineBox().width() - lineBox.width(), 0);\n                }\n                this.box[pos + 1] -= this.lineBox()[pos + 1] - lineBox[pos + 1];\n                this.box[pos + 2] -= this.lineBox()[pos + 2] - lineBox[pos + 2];\n            },\n            axisLabelText: function (value, dataItem, options) {\n                var tmpl = getTemplate(options);\n                var text = value;\n                if (tmpl) {\n                    text = tmpl({\n                        value: value,\n                        dataItem: dataItem,\n                        format: options.format,\n                        culture: options.culture\n                    });\n                } else if (options.format) {\n                    text = this.chartService.format.localeAuto(options.format, [value], options.culture);\n                }\n                return text;\n            },\n            slot: function (from, to, limit) {\n                var slot = this.getSlot(from, to, limit);\n                if (slot) {\n                    return slot.toRect();\n                }\n            },\n            contentBox: function () {\n                var box = this.box.clone();\n                var labels = this.labels;\n                if (labels.length) {\n                    var axis = this.options.vertical ? Y : X;\n                    if (this.chartService.isPannable(axis)) {\n                        var offset = this.maxLabelOffset();\n                        box[axis + 1] -= offset.start;\n                        box[axis + 2] += offset.end;\n                    } else {\n                        if (labels[0].options.visible) {\n                            box.wrap(labels[0].box);\n                        }\n                        var lastLabel = labels[labels.length - 1];\n                        if (lastLabel.options.visible) {\n                            box.wrap(lastLabel.box);\n                        }\n                    }\n                }\n                return box;\n            },\n            maxLabelOffset: function () {\n                var this$1 = this;\n                var ref = this.options;\n                var vertical = ref.vertical;\n                var reverse = ref.reverse;\n                var labelsBetweenTicks = this.labelsBetweenTicks();\n                var tickPositions = this.getLabelsTickPositions();\n                var offsetField = vertical ? Y : X;\n                var labels = this.labels;\n                var startPosition = reverse ? 1 : 0;\n                var endPosition = reverse ? 0 : 1;\n                var maxStartOffset = 0;\n                var maxEndOffset = 0;\n                for (var idx = 0; idx < labels.length; idx++) {\n                    var label = labels[idx];\n                    var tickIx = this$1.labelTickIndex(label);\n                    var startTick = void 0, endTick = void 0;\n                    if (labelsBetweenTicks) {\n                        startTick = tickPositions[tickIx + startPosition];\n                        endTick = tickPositions[tickIx + endPosition];\n                    } else {\n                        startTick = endTick = tickPositions[tickIx];\n                    }\n                    maxStartOffset = Math.max(maxStartOffset, startTick - label.box[offsetField + 1]);\n                    maxEndOffset = Math.max(maxEndOffset, label.box[offsetField + 2] - endTick);\n                }\n                return {\n                    start: maxStartOffset,\n                    end: maxEndOffset\n                };\n            },\n            limitRange: function (from, to, min, max, offset) {\n                var options = this.options;\n                if (from < min && offset < 0 && (!defined(options.min) || options.min <= min) || max < to && offset > 0 && (!defined(options.max) || max <= options.max)) {\n                    return null;\n                }\n                if (to < min && offset > 0 || max < from && offset < 0) {\n                    return {\n                        min: from,\n                        max: to\n                    };\n                }\n                var rangeSize = to - from;\n                var minValue = from;\n                var maxValue = to;\n                if (from < min && offset < 0) {\n                    minValue = limitValue(from, min, max);\n                    maxValue = limitValue(from + rangeSize, min + rangeSize, max);\n                } else if (to > max && offset > 0) {\n                    maxValue = limitValue(to, min, max);\n                    minValue = limitValue(to - rangeSize, min, max - rangeSize);\n                }\n                return {\n                    min: minValue,\n                    max: maxValue\n                };\n            },\n            valueRange: function () {\n                return {\n                    min: this.seriesMin,\n                    max: this.seriesMax\n                };\n            },\n            labelsBetweenTicks: function () {\n                return !this.options.justified;\n            },\n            prepareUserOptions: function () {\n            }\n        });\n        setDefaultOptions(Axis, {\n            labels: {\n                visible: true,\n                rotation: 0,\n                mirror: false,\n                step: 1,\n                skip: 0\n            },\n            line: {\n                width: 1,\n                color: BLACK,\n                visible: true\n            },\n            title: {\n                visible: true,\n                position: CENTER\n            },\n            majorTicks: {\n                align: OUTSIDE,\n                size: 4,\n                skip: 0,\n                step: 1\n            },\n            minorTicks: {\n                align: OUTSIDE,\n                size: 3,\n                skip: 0,\n                step: 1\n            },\n            axisCrossingValue: 0,\n            majorTickType: OUTSIDE,\n            minorTickType: NONE,\n            majorGridLines: {\n                skip: 0,\n                step: 1\n            },\n            minorGridLines: {\n                visible: false,\n                width: 1,\n                color: BLACK,\n                skip: 0,\n                step: 1\n            },\n            margin: 5,\n            visible: true,\n            reverse: false,\n            justified: true,\n            notes: { label: { text: '' } },\n            _alignLines: true,\n            _deferLabels: false\n        });\n        var MILLISECONDS = 'milliseconds';\n        var SECONDS = 'seconds';\n        var MINUTES = 'minutes';\n        var HOURS = 'hours';\n        var DAYS = 'days';\n        var WEEKS = 'weeks';\n        var MONTHS = 'months';\n        var YEARS = 'years';\n        var TIME_PER_MILLISECOND = 1;\n        var TIME_PER_SECOND = 1000;\n        var TIME_PER_MINUTE = 60 * TIME_PER_SECOND;\n        var TIME_PER_HOUR = 60 * TIME_PER_MINUTE;\n        var TIME_PER_DAY = 24 * TIME_PER_HOUR;\n        var TIME_PER_WEEK = 7 * TIME_PER_DAY;\n        var TIME_PER_MONTH = 31 * TIME_PER_DAY;\n        var TIME_PER_YEAR = 365 * TIME_PER_DAY;\n        var TIME_PER_UNIT = {\n            'years': TIME_PER_YEAR,\n            'months': TIME_PER_MONTH,\n            'weeks': TIME_PER_WEEK,\n            'days': TIME_PER_DAY,\n            'hours': TIME_PER_HOUR,\n            'minutes': TIME_PER_MINUTE,\n            'seconds': TIME_PER_SECOND,\n            'milliseconds': TIME_PER_MILLISECOND\n        };\n        function absoluteDateDiff(a, b) {\n            var diff = a.getTime() - b;\n            var offsetDiff = a.getTimezoneOffset() - b.getTimezoneOffset();\n            return diff - offsetDiff * TIME_PER_MINUTE;\n        }\n        function addTicks(date, ticks) {\n            return new Date(date.getTime() + ticks);\n        }\n        function toDate(value) {\n            var result;\n            if (value instanceof Date) {\n                result = value;\n            } else if (value) {\n                result = new Date(value);\n            }\n            return result;\n        }\n        function startOfWeek(date, weekStartDay) {\n            if (weekStartDay === void 0) {\n                weekStartDay = 0;\n            }\n            var daysToSubtract = 0;\n            var day = date.getDay();\n            if (!isNaN(day)) {\n                while (day !== weekStartDay) {\n                    if (day === 0) {\n                        day = 6;\n                    } else {\n                        day--;\n                    }\n                    daysToSubtract++;\n                }\n            }\n            return addTicks(date, -daysToSubtract * TIME_PER_DAY);\n        }\n        function adjustDST(date, hours) {\n            if (hours === 0 && date.getHours() === 23) {\n                date.setHours(date.getHours() + 2);\n                return true;\n            }\n            return false;\n        }\n        function addHours(date, hours) {\n            var roundedDate = new Date(date);\n            roundedDate.setMinutes(0, 0, 0);\n            var tzDiff = (date.getTimezoneOffset() - roundedDate.getTimezoneOffset()) * TIME_PER_MINUTE;\n            return addTicks(roundedDate, tzDiff + hours * TIME_PER_HOUR);\n        }\n        function addDuration(dateValue, value, unit, weekStartDay) {\n            var result = dateValue;\n            if (dateValue) {\n                var date = toDate(dateValue);\n                var hours = date.getHours();\n                if (unit === YEARS) {\n                    result = new Date(date.getFullYear() + value, 0, 1);\n                    adjustDST(result, 0);\n                } else if (unit === MONTHS) {\n                    result = new Date(date.getFullYear(), date.getMonth() + value, 1);\n                    adjustDST(result, hours);\n                } else if (unit === WEEKS) {\n                    result = addDuration(startOfWeek(date, weekStartDay), value * 7, DAYS);\n                    adjustDST(result, hours);\n                } else if (unit === DAYS) {\n                    result = new Date(date.getFullYear(), date.getMonth(), date.getDate() + value);\n                    adjustDST(result, hours);\n                } else if (unit === HOURS) {\n                    result = addHours(date, value);\n                } else if (unit === MINUTES) {\n                    result = addTicks(date, value * TIME_PER_MINUTE);\n                    if (result.getSeconds() > 0) {\n                        result.setSeconds(0);\n                    }\n                } else if (unit === SECONDS) {\n                    result = addTicks(date, value * TIME_PER_SECOND);\n                } else if (unit === MILLISECONDS) {\n                    result = addTicks(date, value);\n                }\n                if (unit !== MILLISECONDS && result.getMilliseconds() > 0) {\n                    result.setMilliseconds(0);\n                }\n            }\n            return result;\n        }\n        function floorDate(date, unit, weekStartDay) {\n            return addDuration(toDate(date), 0, unit, weekStartDay);\n        }\n        function ceilDate(dateValue, unit, weekStartDay) {\n            var date = toDate(dateValue);\n            if (date && floorDate(date, unit, weekStartDay).getTime() === date.getTime()) {\n                return date;\n            }\n            return addDuration(date, 1, unit, weekStartDay);\n        }\n        function dateComparer(a, b) {\n            if (a && b) {\n                return a.getTime() - b.getTime();\n            }\n            return -1;\n        }\n        function dateDiff(a, b) {\n            return a.getTime() - b;\n        }\n        function toTime(value) {\n            if (isArray(value)) {\n                var result = [];\n                for (var idx = 0; idx < value.length; idx++) {\n                    result.push(toTime(value[idx]));\n                }\n                return result;\n            } else if (value) {\n                return toDate(value).getTime();\n            }\n        }\n        function dateEquals(a, b) {\n            if (a && b) {\n                return toTime(a) === toTime(b);\n            }\n            return a === b;\n        }\n        function timeIndex(date, start, baseUnit) {\n            return absoluteDateDiff(date, start) / TIME_PER_UNIT[baseUnit];\n        }\n        function dateIndex(value, start, baseUnit, baseUnitStep) {\n            var date = toDate(value);\n            var startDate = toDate(start);\n            var index;\n            if (baseUnit === MONTHS) {\n                index = date.getMonth() - startDate.getMonth() + (date.getFullYear() - startDate.getFullYear()) * 12 + timeIndex(date, new Date(date.getFullYear(), date.getMonth()), DAYS) / new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();\n            } else if (baseUnit === YEARS) {\n                index = date.getFullYear() - startDate.getFullYear() + dateIndex(date, new Date(date.getFullYear(), 0), MONTHS, 1) / 12;\n            } else if (baseUnit === DAYS || baseUnit === WEEKS) {\n                index = timeIndex(date, startDate, baseUnit);\n            } else {\n                index = dateDiff(date, start) / TIME_PER_UNIT[baseUnit];\n            }\n            return index / baseUnitStep;\n        }\n        function duration(a, b, unit) {\n            var diff;\n            if (unit === YEARS) {\n                diff = b.getFullYear() - a.getFullYear();\n            } else if (unit === MONTHS) {\n                diff = duration(a, b, YEARS) * 12 + b.getMonth() - a.getMonth();\n            } else if (unit === DAYS) {\n                diff = Math.floor(dateDiff(b, a) / TIME_PER_DAY);\n            } else {\n                diff = Math.floor(dateDiff(b, a) / TIME_PER_UNIT[unit]);\n            }\n            return diff;\n        }\n        function lteDateIndex(date, sortedDates) {\n            var low = 0;\n            var high = sortedDates.length - 1;\n            var index;\n            while (low <= high) {\n                index = Math.floor((low + high) / 2);\n                var currentDate = sortedDates[index];\n                if (currentDate < date) {\n                    low = index + 1;\n                    continue;\n                }\n                if (currentDate > date) {\n                    high = index - 1;\n                    continue;\n                }\n                while (dateEquals(sortedDates[index - 1], date)) {\n                    index--;\n                }\n                return index;\n            }\n            if (sortedDates[index] <= date) {\n                return index;\n            }\n            return index - 1;\n        }\n        function parseDate(intlService, date) {\n            var result;\n            if (isString(date)) {\n                result = intlService.parseDate(date) || toDate(date);\n            } else {\n                result = toDate(date);\n            }\n            return result;\n        }\n        function parseDates(intlService, dates) {\n            if (isArray(dates)) {\n                var result = [];\n                for (var idx = 0; idx < dates.length; idx++) {\n                    result.push(parseDate(intlService, dates[idx]));\n                }\n                return result;\n            }\n            return parseDate(intlService, dates);\n        }\n        var MIN_CATEGORY_POINTS_RANGE = 0.01;\n        function indexOf(value, arr) {\n            if (value instanceof Date) {\n                var length = arr.length;\n                for (var idx = 0; idx < length; idx++) {\n                    if (dateEquals(arr[idx], value)) {\n                        return idx;\n                    }\n                }\n                return -1;\n            }\n            return arr.indexOf(value);\n        }\n        var CategoryAxis = Axis.extend({\n            initFields: function () {\n                this._ticks = {};\n            },\n            categoriesHash: function () {\n                return '';\n            },\n            clone: function () {\n                var copy = new CategoryAxis($.extend({}, this.options), this.chartService);\n                copy.createLabels();\n                return copy;\n            },\n            initUserOptions: function (options) {\n                var categories = options.categories || [];\n                var definedMin = defined(options.min);\n                var definedMax = defined(options.max);\n                options.srcCategories = options.categories = categories;\n                if ((definedMin || definedMax) && categories.length) {\n                    var min = definedMin ? Math.floor(options.min) : 0;\n                    var max;\n                    if (definedMax) {\n                        max = options.justified ? Math.floor(options.max) + 1 : Math.ceil(options.max);\n                    } else {\n                        max = categories.length;\n                    }\n                    options.categories = options.categories.slice(min, max);\n                }\n                return options;\n            },\n            rangeIndices: function () {\n                var options = this.options;\n                var length = options.categories.length || 1;\n                var min = isNumber(options.min) ? options.min % 1 : 0;\n                var max;\n                if (isNumber(options.max) && options.max % 1 !== 0 && options.max < this.totalRange().max) {\n                    max = length - (1 - options.max % 1);\n                } else {\n                    max = length - (options.justified ? 1 : 0);\n                }\n                return {\n                    min: min,\n                    max: max\n                };\n            },\n            totalRangeIndices: function (limit) {\n                var options = this.options;\n                var min = isNumber(options.min) ? options.min : 0;\n                var max;\n                if (isNumber(options.max)) {\n                    max = options.max;\n                } else if (isNumber(options.min)) {\n                    max = min + options.categories.length;\n                } else {\n                    max = this.totalRange().max || 1;\n                }\n                if (limit) {\n                    var totalRange = this.totalRange();\n                    min = limitValue(min, 0, totalRange.max);\n                    max = limitValue(max, 0, totalRange.max);\n                }\n                return {\n                    min: min,\n                    max: max\n                };\n            },\n            range: function () {\n                var options = this.options;\n                var min = isNumber(options.min) ? options.min : 0;\n                var max = isNumber(options.max) ? options.max : this.totalRange().max;\n                return {\n                    min: min,\n                    max: max\n                };\n            },\n            roundedRange: function () {\n                return this.range();\n            },\n            totalRange: function () {\n                var options = this.options;\n                return {\n                    min: 0,\n                    max: Math.max(this._seriesMax || 0, options.srcCategories.length) - (options.justified ? 1 : 0)\n                };\n            },\n            scaleOptions: function () {\n                var ref = this.rangeIndices();\n                var min = ref.min;\n                var max = ref.max;\n                var lineBox = this.lineBox();\n                var size = this.options.vertical ? lineBox.height() : lineBox.width();\n                var scale = size / (max - min || 1);\n                return {\n                    scale: scale * (this.options.reverse ? -1 : 1),\n                    box: lineBox,\n                    min: min,\n                    max: max\n                };\n            },\n            arrangeLabels: function () {\n                Axis.fn.arrangeLabels.call(this);\n                this.hideOutOfRangeLabels();\n            },\n            hideOutOfRangeLabels: function () {\n                var ref = this;\n                var box = ref.box;\n                var labels = ref.labels;\n                if (labels.length) {\n                    var valueAxis = this.options.vertical ? Y : X;\n                    var start = box[valueAxis + 1];\n                    var end = box[valueAxis + 2];\n                    var firstLabel = labels[0];\n                    var lastLabel = last(labels);\n                    if (firstLabel.box[valueAxis + 1] > end || firstLabel.box[valueAxis + 2] < start) {\n                        firstLabel.options.visible = false;\n                    }\n                    if (lastLabel.box[valueAxis + 1] > end || lastLabel.box[valueAxis + 2] < start) {\n                        lastLabel.options.visible = false;\n                    }\n                }\n            },\n            getMajorTickPositions: function () {\n                return this.getTicks().majorTicks;\n            },\n            getMinorTickPositions: function () {\n                return this.getTicks().minorTicks;\n            },\n            getLabelsTickPositions: function () {\n                return this.getTicks().labelTicks;\n            },\n            tickIndices: function (stepSize) {\n                var ref = this.rangeIndices();\n                var min = ref.min;\n                var max = ref.max;\n                var limit = Math.ceil(max);\n                var current = Math.floor(min);\n                var indices = [];\n                while (current <= limit) {\n                    indices.push(current);\n                    current += stepSize;\n                }\n                return indices;\n            },\n            getTickPositions: function (stepSize) {\n                var ref = this.options;\n                var vertical = ref.vertical;\n                var reverse = ref.reverse;\n                var ref$1 = this.scaleOptions();\n                var scale = ref$1.scale;\n                var box = ref$1.box;\n                var min = ref$1.min;\n                var pos = box[(vertical ? Y : X) + (reverse ? 2 : 1)];\n                var indices = this.tickIndices(stepSize);\n                var positions = [];\n                for (var idx = 0; idx < indices.length; idx++) {\n                    positions.push(pos + round(scale * (indices[idx] - min), COORD_PRECISION));\n                }\n                return positions;\n            },\n            getTicks: function () {\n                var options = this.options;\n                var cache = this._ticks;\n                var range = this.rangeIndices();\n                var lineBox = this.lineBox();\n                var hash = lineBox.getHash() + range.min + ',' + range.max + options.reverse + options.justified;\n                if (cache._hash !== hash) {\n                    var hasMinor = options.minorTicks.visible || options.minorGridLines.visible;\n                    cache._hash = hash;\n                    cache.labelTicks = this.getTickPositions(1);\n                    cache.majorTicks = this.filterOutOfRangePositions(cache.labelTicks, lineBox);\n                    cache.minorTicks = hasMinor ? this.filterOutOfRangePositions(this.getTickPositions(0.5), lineBox) : [];\n                }\n                return cache;\n            },\n            filterOutOfRangePositions: function (positions, lineBox) {\n                if (!positions.length) {\n                    return positions;\n                }\n                var axis = this.options.vertical ? Y : X;\n                var inRange = function (position) {\n                    return lineBox[axis + 1] <= position && position <= lineBox[axis + 2];\n                };\n                var end = positions.length - 1;\n                var startIndex = 0;\n                while (!inRange(positions[startIndex]) && startIndex <= end) {\n                    startIndex++;\n                }\n                var endIndex = end;\n                while (!inRange(positions[endIndex]) && endIndex >= 0) {\n                    endIndex--;\n                }\n                return positions.slice(startIndex, endIndex + 1);\n            },\n            getSlot: function (from, to, limit) {\n                var options = this.options;\n                var reverse = options.reverse;\n                var justified = options.justified;\n                var vertical = options.vertical;\n                var ref = this.scaleOptions();\n                var scale = ref.scale;\n                var box = ref.box;\n                var min = ref.min;\n                var valueAxis = vertical ? Y : X;\n                var lineStart = box[valueAxis + (reverse ? 2 : 1)];\n                var slotBox = box.clone();\n                var singleSlot = !defined(to);\n                var start = valueOrDefault(from, 0);\n                var end = valueOrDefault(to, start);\n                end = Math.max(end - 1, start);\n                end = Math.max(start, end);\n                var p1 = lineStart + (start - min) * scale;\n                var p2 = lineStart + (end + 1 - min) * scale;\n                if (singleSlot && justified) {\n                    p2 = p1;\n                }\n                if (limit) {\n                    p1 = limitValue(p1, box[valueAxis + 1], box[valueAxis + 2]);\n                    p2 = limitValue(p2, box[valueAxis + 1], box[valueAxis + 2]);\n                }\n                slotBox[valueAxis + 1] = reverse ? p2 : p1;\n                slotBox[valueAxis + 2] = reverse ? p1 : p2;\n                return slotBox;\n            },\n            limitSlot: function (slot) {\n                var vertical = this.options.vertical;\n                var valueAxis = vertical ? Y : X;\n                var lineBox = this.lineBox();\n                var limittedSlot = slot.clone();\n                limittedSlot[valueAxis + 1] = limitValue(slot[valueAxis + 1], lineBox[valueAxis + 1], lineBox[valueAxis + 2]);\n                limittedSlot[valueAxis + 2] = limitValue(slot[valueAxis + 2], lineBox[valueAxis + 1], lineBox[valueAxis + 2]);\n                return limittedSlot;\n            },\n            slot: function (from, to, limit) {\n                var min = Math.floor(this.options.min || 0);\n                var start = from;\n                var end = to;\n                if (typeof start === 'string') {\n                    start = this.categoryIndex(start);\n                } else if (isNumber(start)) {\n                    start -= min;\n                }\n                if (typeof end === 'string') {\n                    end = this.categoryIndex(end);\n                } else if (isNumber(end)) {\n                    end -= min;\n                }\n                return Axis.fn.slot.call(this, start, end, limit);\n            },\n            pointCategoryIndex: function (point) {\n                var ref = this.options;\n                var reverse = ref.reverse;\n                var justified = ref.justified;\n                var vertical = ref.vertical;\n                var valueAxis = vertical ? Y : X;\n                var ref$1 = this.scaleOptions();\n                var scale = ref$1.scale;\n                var box = ref$1.box;\n                var min = ref$1.min;\n                var max = ref$1.max;\n                var startValue = reverse ? max : min;\n                var lineStart = box[valueAxis + 1];\n                var lineEnd = box[valueAxis + 2];\n                var pos = point[valueAxis];\n                if (pos < lineStart || pos > lineEnd) {\n                    return null;\n                }\n                var value = startValue + (pos - lineStart) / scale;\n                var diff = value % 1;\n                if (justified) {\n                    value = Math.round(value);\n                } else if (diff === 0 && value > 0) {\n                    value--;\n                }\n                return Math.floor(value);\n            },\n            getCategory: function (point) {\n                var index = this.pointCategoryIndex(point);\n                if (index === null) {\n                    return null;\n                }\n                return this.options.categories[index];\n            },\n            categoryIndex: function (value) {\n                return this.totalIndex(value) - Math.floor(this.options.min || 0);\n            },\n            categoryAt: function (index, total) {\n                var options = this.options;\n                return (total ? options.srcCategories : options.categories)[index];\n            },\n            categoriesCount: function () {\n                return (this.options.categories || []).length;\n            },\n            translateRange: function (delta) {\n                var options = this.options;\n                var lineBox = this.lineBox();\n                var size = options.vertical ? lineBox.height() : lineBox.width();\n                var range = options.categories.length;\n                var scale = size / range;\n                var offset = round(delta / scale, DEFAULT_PRECISION);\n                return {\n                    min: offset,\n                    max: range + offset\n                };\n            },\n            zoomRange: function (rate) {\n                var rangeIndices = this.totalRangeIndices();\n                var ref = this.totalRange();\n                var totalMin = ref.min;\n                var totalMax = ref.max;\n                var min = limitValue(rangeIndices.min + rate, totalMin, totalMax);\n                var max = limitValue(rangeIndices.max - rate, totalMin, totalMax);\n                if (max - min > 0) {\n                    return {\n                        min: min,\n                        max: max\n                    };\n                }\n            },\n            scaleRange: function (scale) {\n                var range = this.options.categories.length;\n                var delta = scale * range;\n                return {\n                    min: -delta,\n                    max: range + delta\n                };\n            },\n            labelsCount: function () {\n                var labelsRange = this.labelsRange();\n                return labelsRange.max - labelsRange.min;\n            },\n            labelsRange: function () {\n                var options = this.options;\n                var justified = options.justified;\n                var labelOptions = options.labels;\n                var ref = this.totalRangeIndices(true);\n                var min = ref.min;\n                var max = ref.max;\n                var start = Math.floor(min);\n                if (!justified) {\n                    min = Math.floor(min);\n                    max = Math.ceil(max);\n                } else {\n                    min = Math.ceil(min);\n                    max = Math.floor(max);\n                }\n                var skip;\n                if (min > labelOptions.skip) {\n                    skip = labelOptions.skip + labelOptions.step * Math.ceil((min - labelOptions.skip) / labelOptions.step);\n                } else {\n                    skip = labelOptions.skip;\n                }\n                return {\n                    min: skip - start,\n                    max: (options.categories.length ? max + (justified ? 1 : 0) : 0) - start\n                };\n            },\n            createAxisLabel: function (index, labelOptions) {\n                var options = this.options;\n                var dataItem = options.dataItems ? options.dataItems[index] : null;\n                var category = valueOrDefault(options.categories[index], '');\n                var text = this.axisLabelText(category, dataItem, labelOptions);\n                return new AxisLabel(category, text, index, dataItem, labelOptions);\n            },\n            shouldRenderNote: function (value) {\n                var range = this.totalRangeIndices();\n                return Math.floor(range.min) <= value && value <= Math.ceil(range.max);\n            },\n            noteSlot: function (value) {\n                var options = this.options;\n                var index = value - Math.floor(options.min || 0);\n                return this.getSlot(index);\n            },\n            arrangeNotes: function () {\n                Axis.fn.arrangeNotes.call(this);\n                this.hideOutOfRangeNotes();\n            },\n            hideOutOfRangeNotes: function () {\n                var ref = this;\n                var notes = ref.notes;\n                var box = ref.box;\n                if (notes && notes.length) {\n                    var valueAxis = this.options.vertical ? Y : X;\n                    var start = box[valueAxis + 1];\n                    var end = box[valueAxis + 2];\n                    for (var idx = 0; idx < notes.length; idx++) {\n                        var note = notes[idx];\n                        if (note.box && (end < note.box[valueAxis + 1] || note.box[valueAxis + 2] < start)) {\n                            note.hide();\n                        }\n                    }\n                }\n            },\n            pan: function (delta) {\n                var range = this.totalRangeIndices(true);\n                var ref = this.scaleOptions();\n                var scale = ref.scale;\n                var offset = round(delta / scale, DEFAULT_PRECISION);\n                var totalRange = this.totalRange();\n                var min = range.min + offset;\n                var max = range.max + offset;\n                return this.limitRange(min, max, 0, totalRange.max, offset);\n            },\n            pointsRange: function (start, end) {\n                var ref = this.options;\n                var reverse = ref.reverse;\n                var vertical = ref.vertical;\n                var valueAxis = vertical ? Y : X;\n                var range = this.totalRangeIndices(true);\n                var ref$1 = this.scaleOptions();\n                var scale = ref$1.scale;\n                var box = ref$1.box;\n                var lineStart = box[valueAxis + (reverse ? 2 : 1)];\n                var diffStart = start[valueAxis] - lineStart;\n                var diffEnd = end[valueAxis] - lineStart;\n                var min = range.min + diffStart / scale;\n                var max = range.min + diffEnd / scale;\n                var rangeMin = Math.min(min, max);\n                var rangeMax = Math.max(min, max);\n                if (rangeMax - rangeMin >= MIN_CATEGORY_POINTS_RANGE) {\n                    return {\n                        min: rangeMin,\n                        max: rangeMax\n                    };\n                }\n            },\n            valueRange: function () {\n                return this.range();\n            },\n            totalIndex: function (value) {\n                var options = this.options;\n                var index = this._categoriesMap ? this._categoriesMap.get(value) : indexOf(value, options.srcCategories);\n                return index;\n            },\n            currentRangeIndices: function () {\n                var options = this.options;\n                var min = 0;\n                if (isNumber(options.min)) {\n                    min = Math.floor(options.min);\n                }\n                var max;\n                if (isNumber(options.max)) {\n                    max = options.justified ? Math.floor(options.max) : Math.ceil(options.max) - 1;\n                } else {\n                    max = this.totalCount() - 1;\n                }\n                return {\n                    min: min,\n                    max: max\n                };\n            },\n            mapCategories: function () {\n                if (!this._categoriesMap) {\n                    var map$$1 = this._categoriesMap = new HashMap();\n                    var srcCategories = this.options.srcCategories;\n                    for (var idx = 0; idx < srcCategories.length; idx++) {\n                        map$$1.set(srcCategories[idx], idx);\n                    }\n                }\n            },\n            totalCount: function () {\n                return Math.max(this.options.srcCategories.length, this._seriesMax || 0);\n            }\n        });\n        setDefaultOptions(CategoryAxis, {\n            type: 'category',\n            vertical: false,\n            majorGridLines: {\n                visible: false,\n                width: 1,\n                color: BLACK\n            },\n            labels: { zIndex: 1 },\n            justified: false,\n            _deferLabels: true\n        });\n        var COORDINATE_LIMIT = 300000;\n        var DateLabelFormats = {\n            milliseconds: 'HH:mm:ss.fff',\n            seconds: 'HH:mm:ss',\n            minutes: 'HH:mm',\n            hours: 'HH:mm',\n            days: 'M/d',\n            weeks: 'M/d',\n            months: 'MMM \\'yy',\n            years: 'yyyy'\n        };\n        var ZERO_THRESHOLD = 0.2;\n        var AUTO = 'auto';\n        var BASE_UNITS = [\n            MILLISECONDS,\n            SECONDS,\n            MINUTES,\n            HOURS,\n            DAYS,\n            WEEKS,\n            MONTHS,\n            YEARS\n        ];\n        var FIT = 'fit';\n        function categoryRange(categories) {\n            var range = categories._range;\n            if (!range) {\n                range = categories._range = sparseArrayLimits(categories);\n                range.min = toDate(range.min);\n                range.max = toDate(range.max);\n            }\n            return range;\n        }\n        var EmptyDateRange = Class.extend({\n            init: function (options) {\n                this.options = options;\n            },\n            displayIndices: function () {\n                return {\n                    min: 0,\n                    max: 1\n                };\n            },\n            displayRange: function () {\n                return {};\n            },\n            total: function () {\n                return {};\n            },\n            valueRange: function () {\n                return {};\n            },\n            valueIndex: function () {\n                return -1;\n            },\n            values: function () {\n                return [];\n            },\n            totalIndex: function () {\n                return -1;\n            },\n            valuesCount: function () {\n                return 0;\n            },\n            totalCount: function () {\n                return 0;\n            },\n            dateAt: function () {\n                return null;\n            }\n        });\n        var DateRange = Class.extend({\n            init: function (start, end, options) {\n                this.options = options;\n                options.baseUnitStep = options.baseUnitStep || 1;\n                var roundToBaseUnit = options.roundToBaseUnit;\n                var justified = options.justified;\n                this.start = addDuration(start, 0, options.baseUnit, options.weekStartDay);\n                var lowerEnd = this.roundToTotalStep(end);\n                var expandEnd = !justified && dateEquals(end, lowerEnd) && !options.justifyEnd;\n                this.end = this.roundToTotalStep(end, !justified, expandEnd ? 1 : 0);\n                var min = options.min || start;\n                this.valueStart = this.roundToTotalStep(min);\n                this.displayStart = roundToBaseUnit ? this.valueStart : min;\n                var max = options.max;\n                if (!max) {\n                    this.valueEnd = lowerEnd;\n                    this.displayEnd = roundToBaseUnit || expandEnd ? this.end : end;\n                } else {\n                    this.valueEnd = this.roundToTotalStep(max, false, !justified && dateEquals(max, this.roundToTotalStep(max)) ? -1 : 0);\n                    this.displayEnd = roundToBaseUnit ? this.roundToTotalStep(max, !justified) : options.max;\n                }\n                if (this.valueEnd < this.valueStart) {\n                    this.valueEnd = this.valueStart;\n                }\n                if (this.displayEnd <= this.displayStart) {\n                    this.displayEnd = this.roundToTotalStep(this.displayStart, false, 1);\n                }\n            },\n            displayRange: function () {\n                return {\n                    min: this.displayStart,\n                    max: this.displayEnd\n                };\n            },\n            displayIndices: function () {\n                if (!this._indices) {\n                    var options = this.options;\n                    var baseUnit = options.baseUnit;\n                    var baseUnitStep = options.baseUnitStep;\n                    var minIdx = dateIndex(this.displayStart, this.valueStart, baseUnit, baseUnitStep);\n                    var maxIdx = dateIndex(this.displayEnd, this.valueStart, baseUnit, baseUnitStep);\n                    this._indices = {\n                        min: minIdx,\n                        max: maxIdx\n                    };\n                }\n                return this._indices;\n            },\n            total: function () {\n                return {\n                    min: this.start,\n                    max: this.end\n                };\n            },\n            totalCount: function () {\n                var last$$1 = this.totalIndex(this.end);\n                return last$$1 + (this.options.justified ? 1 : 0);\n            },\n            valueRange: function () {\n                return {\n                    min: this.valueStart,\n                    max: this.valueEnd\n                };\n            },\n            valueIndex: function (value) {\n                var options = this.options;\n                return Math.floor(dateIndex(value, this.valueStart, options.baseUnit, options.baseUnitStep));\n            },\n            totalIndex: function (value) {\n                var options = this.options;\n                return Math.floor(dateIndex(value, this.start, options.baseUnit, options.baseUnitStep));\n            },\n            dateIndex: function (value) {\n                var options = this.options;\n                return dateIndex(value, this.valueStart, options.baseUnit, options.baseUnitStep);\n            },\n            valuesCount: function () {\n                var maxIdx = this.valueIndex(this.valueEnd);\n                return maxIdx + 1;\n            },\n            values: function () {\n                var values = this._values;\n                if (!values) {\n                    var options = this.options;\n                    var range = this.valueRange();\n                    this._values = values = [];\n                    for (var date = range.min; date <= range.max;) {\n                        values.push(date);\n                        date = addDuration(date, options.baseUnitStep, options.baseUnit, options.weekStartDay);\n                    }\n                }\n                return values;\n            },\n            dateAt: function (index, total) {\n                var options = this.options;\n                return addDuration(total ? this.start : this.valueStart, options.baseUnitStep * index, options.baseUnit, options.weekStartDay);\n            },\n            roundToTotalStep: function (value, upper, next) {\n                var ref = this.options;\n                var baseUnit = ref.baseUnit;\n                var baseUnitStep = ref.baseUnitStep;\n                var weekStartDay = ref.weekStartDay;\n                var start = this.start;\n                var step = dateIndex(value, start, baseUnit, baseUnitStep);\n                var roundedStep = upper ? Math.ceil(step) : Math.floor(step);\n                if (next) {\n                    roundedStep += next;\n                }\n                return addDuration(start, roundedStep * baseUnitStep, baseUnit, weekStartDay);\n            }\n        });\n        function autoBaseUnit(options, startUnit, startStep) {\n            var categoryLimits = categoryRange(options.categories);\n            var span = (options.max || categoryLimits.max) - (options.min || categoryLimits.min);\n            var autoBaseUnitSteps = options.autoBaseUnitSteps;\n            var maxDateGroups = options.maxDateGroups;\n            var autoUnit = options.baseUnit === FIT;\n            var autoUnitIx = startUnit ? BASE_UNITS.indexOf(startUnit) : 0;\n            var baseUnit = autoUnit ? BASE_UNITS[autoUnitIx++] : options.baseUnit;\n            var units = span / TIME_PER_UNIT[baseUnit];\n            var totalUnits = units;\n            var unitSteps, step, nextStep;\n            while (!step || units >= maxDateGroups) {\n                unitSteps = unitSteps || autoBaseUnitSteps[baseUnit].slice(0);\n                do {\n                    nextStep = unitSteps.shift();\n                } while (nextStep && startUnit === baseUnit && nextStep < startStep);\n                if (nextStep) {\n                    step = nextStep;\n                    units = totalUnits / step;\n                } else if (baseUnit === last(BASE_UNITS)) {\n                    step = Math.ceil(totalUnits / maxDateGroups);\n                    break;\n                } else if (autoUnit) {\n                    baseUnit = BASE_UNITS[autoUnitIx++] || last(BASE_UNITS);\n                    totalUnits = span / TIME_PER_UNIT[baseUnit];\n                    unitSteps = null;\n                } else {\n                    if (units > maxDateGroups) {\n                        step = Math.ceil(totalUnits / maxDateGroups);\n                    }\n                    break;\n                }\n            }\n            options.baseUnitStep = step;\n            options.baseUnit = baseUnit;\n        }\n        function defaultBaseUnit(options) {\n            var categories = options.categories;\n            var count = defined(categories) ? categories.length : 0;\n            var minDiff = MAX_VALUE;\n            var lastCategory, unit;\n            for (var categoryIx = 0; categoryIx < count; categoryIx++) {\n                var category = categories[categoryIx];\n                if (category && lastCategory) {\n                    var diff = absoluteDateDiff(category, lastCategory);\n                    if (diff > 0) {\n                        minDiff = Math.min(minDiff, diff);\n                        if (minDiff >= TIME_PER_YEAR) {\n                            unit = YEARS;\n                        } else if (minDiff >= TIME_PER_MONTH - TIME_PER_DAY * 3) {\n                            unit = MONTHS;\n                        } else if (minDiff >= TIME_PER_WEEK) {\n                            unit = WEEKS;\n                        } else if (minDiff >= TIME_PER_DAY) {\n                            unit = DAYS;\n                        } else if (minDiff >= TIME_PER_HOUR) {\n                            unit = HOURS;\n                        } else if (minDiff >= TIME_PER_MINUTE) {\n                            unit = MINUTES;\n                        } else {\n                            unit = SECONDS;\n                        }\n                    }\n                }\n                lastCategory = category;\n            }\n            options.baseUnit = unit || DAYS;\n        }\n        function initUnit(options) {\n            var baseUnit = (options.baseUnit || '').toLowerCase();\n            var useDefault = baseUnit !== FIT && !inArray(baseUnit, BASE_UNITS);\n            if (useDefault) {\n                defaultBaseUnit(options);\n            }\n            if (baseUnit === FIT || options.baseUnitStep === AUTO) {\n                autoBaseUnit(options);\n            }\n            return options;\n        }\n        var DateCategoryAxis = CategoryAxis.extend({\n            clone: function () {\n                var copy = new DateCategoryAxis($.extend({}, this.options), this.chartService);\n                copy.createLabels();\n                return copy;\n            },\n            categoriesHash: function () {\n                var start = this.dataRange.total().min;\n                return this.options.baseUnit + this.options.baseUnitStep + start;\n            },\n            initUserOptions: function (options) {\n                return options;\n            },\n            initFields: function () {\n                CategoryAxis.fn.initFields.call(this);\n                var chartService = this.chartService;\n                var intlService = chartService.intl;\n                var options = this.options;\n                var categories = options.categories || [];\n                if (!categories._parsed) {\n                    categories = parseDates(intlService, categories);\n                    categories._parsed = true;\n                }\n                options = deepExtend({ roundToBaseUnit: true }, options, {\n                    categories: categories,\n                    min: parseDate(intlService, options.min),\n                    max: parseDate(intlService, options.max)\n                });\n                if (chartService.panning && chartService.isPannable(options.vertical ? Y : X)) {\n                    options.roundToBaseUnit = false;\n                }\n                options.userSetBaseUnit = options.userSetBaseUnit || options.baseUnit;\n                options.userSetBaseUnitStep = options.userSetBaseUnitStep || options.baseUnitStep;\n                this.options = options;\n                options.srcCategories = categories;\n                if (categories.length > 0) {\n                    var range = categoryRange(categories);\n                    var maxDivisions = options.maxDivisions;\n                    this.dataRange = new DateRange(range.min, range.max, initUnit(options));\n                    if (maxDivisions) {\n                        var dataRange = this.dataRange.displayRange();\n                        var divisionOptions = $.extend({}, options, {\n                            justified: true,\n                            roundToBaseUnit: false,\n                            baseUnit: 'fit',\n                            min: dataRange.min,\n                            max: dataRange.max,\n                            maxDateGroups: maxDivisions\n                        });\n                        var dataRangeOptions = this.dataRange.options;\n                        autoBaseUnit(divisionOptions, dataRangeOptions.baseUnit, dataRangeOptions.baseUnitStep);\n                        this.divisionRange = new DateRange(range.min, range.max, divisionOptions);\n                    } else {\n                        this.divisionRange = this.dataRange;\n                    }\n                } else {\n                    options.baseUnit = options.baseUnit || DAYS;\n                    this.dataRange = this.divisionRange = new EmptyDateRange(options);\n                }\n            },\n            tickIndices: function (stepSize) {\n                var ref = this;\n                var dataRange = ref.dataRange;\n                var divisionRange = ref.divisionRange;\n                var valuesCount = divisionRange.valuesCount();\n                if (!this.options.maxDivisions || !valuesCount) {\n                    return CategoryAxis.fn.tickIndices.call(this, stepSize);\n                }\n                var indices = [];\n                var values = divisionRange.values();\n                var offset = 0;\n                if (!this.options.justified) {\n                    values = values.concat(divisionRange.dateAt(valuesCount));\n                    offset = 0.5;\n                }\n                for (var idx = 0; idx < values.length; idx++) {\n                    indices.push(dataRange.dateIndex(values[idx]) + offset);\n                    if (stepSize !== 1 && idx >= 1) {\n                        var last$$1 = indices.length - 1;\n                        indices.splice(idx, 0, indices[last$$1 - 1] + (indices[last$$1] - indices[last$$1 - 1]) * stepSize);\n                    }\n                }\n                return indices;\n            },\n            shouldRenderNote: function (value) {\n                var range = this.range();\n                var categories = this.options.categories || [];\n                return dateComparer(value, range.min) >= 0 && dateComparer(value, range.max) <= 0 && categories.length;\n            },\n            parseNoteValue: function (value) {\n                return parseDate(this.chartService.intl, value);\n            },\n            noteSlot: function (value) {\n                return this.getSlot(value);\n            },\n            translateRange: function (delta) {\n                var options = this.options;\n                var baseUnit = options.baseUnit;\n                var weekStartDay = options.weekStartDay;\n                var vertical = options.vertical;\n                var lineBox = this.lineBox();\n                var size = vertical ? lineBox.height() : lineBox.width();\n                var range = this.range();\n                var scale = size / (range.max - range.min);\n                var offset = round(delta / scale, DEFAULT_PRECISION);\n                if (range.min && range.max) {\n                    var from = addTicks(options.min || range.min, offset);\n                    var to = addTicks(options.max || range.max, offset);\n                    range = {\n                        min: addDuration(from, 0, baseUnit, weekStartDay),\n                        max: addDuration(to, 0, baseUnit, weekStartDay)\n                    };\n                }\n                return range;\n            },\n            scaleRange: function (delta) {\n                var rounds = Math.abs(delta);\n                var result = this.range();\n                var from = result.min;\n                var to = result.max;\n                if (from && to) {\n                    while (rounds--) {\n                        var range = dateDiff(from, to);\n                        var step = Math.round(range * 0.1);\n                        if (delta < 0) {\n                            from = addTicks(from, step);\n                            to = addTicks(to, -step);\n                        } else {\n                            from = addTicks(from, -step);\n                            to = addTicks(to, step);\n                        }\n                    }\n                    result = {\n                        min: from,\n                        max: to\n                    };\n                }\n                return result;\n            },\n            labelsRange: function () {\n                return {\n                    min: this.options.labels.skip,\n                    max: this.divisionRange.valuesCount()\n                };\n            },\n            pan: function (delta) {\n                if (this.isEmpty()) {\n                    return null;\n                }\n                var options = this.options;\n                var lineBox = this.lineBox();\n                var size = options.vertical ? lineBox.height() : lineBox.width();\n                var ref = this.dataRange.displayRange();\n                var min = ref.min;\n                var max = ref.max;\n                var totalLimits = this.dataRange.total();\n                var scale = size / (max - min);\n                var offset = round(delta / scale, DEFAULT_PRECISION) * (options.reverse ? -1 : 1);\n                var from = addTicks(min, offset);\n                var to = addTicks(max, offset);\n                var panRange = this.limitRange(toTime(from), toTime(to), toTime(totalLimits.min), toTime(totalLimits.max), offset);\n                if (panRange) {\n                    panRange.min = toDate(panRange.min);\n                    panRange.max = toDate(panRange.max);\n                    panRange.baseUnit = options.baseUnit;\n                    panRange.baseUnitStep = options.baseUnitStep || 1;\n                    panRange.userSetBaseUnit = options.userSetBaseUnit;\n                    panRange.userSetBaseUnitStep = options.userSetBaseUnitStep;\n                    return panRange;\n                }\n            },\n            pointsRange: function (start, end) {\n                if (this.isEmpty()) {\n                    return null;\n                }\n                var pointsRange = CategoryAxis.fn.pointsRange.call(this, start, end);\n                var datesRange = this.dataRange.displayRange();\n                var indicesRange = this.dataRange.displayIndices();\n                var scale = dateDiff(datesRange.max, datesRange.min) / (indicesRange.max - indicesRange.min);\n                var options = this.options;\n                var min = addTicks(datesRange.min, pointsRange.min * scale);\n                var max = addTicks(datesRange.min, pointsRange.max * scale);\n                return {\n                    min: min,\n                    max: max,\n                    baseUnit: options.userSetBaseUnit || options.baseUnit,\n                    baseUnitStep: options.userSetBaseUnitStep || options.baseUnitStep\n                };\n            },\n            zoomRange: function (delta) {\n                if (this.isEmpty()) {\n                    return null;\n                }\n                var options = this.options;\n                var fit = options.userSetBaseUnit === FIT;\n                var totalLimits = this.dataRange.total();\n                var ref = this.dataRange.displayRange();\n                var rangeMin = ref.min;\n                var rangeMax = ref.max;\n                var ref$1 = this.dataRange.options;\n                var weekStartDay = ref$1.weekStartDay;\n                var baseUnit = ref$1.baseUnit;\n                var baseUnitStep = ref$1.baseUnitStep;\n                var min = addDuration(rangeMin, delta * baseUnitStep, baseUnit, weekStartDay);\n                var max = addDuration(rangeMax, -delta * baseUnitStep, baseUnit, weekStartDay);\n                if (fit) {\n                    var autoBaseUnitSteps = options.autoBaseUnitSteps;\n                    var maxDateGroups = options.maxDateGroups;\n                    var maxDiff = last(autoBaseUnitSteps[baseUnit]) * maxDateGroups * TIME_PER_UNIT[baseUnit];\n                    var rangeDiff = dateDiff(rangeMax, rangeMin);\n                    var diff = dateDiff(max, min);\n                    var baseUnitIndex = BASE_UNITS.indexOf(baseUnit);\n                    var autoBaseUnitStep, ticks;\n                    if (diff < TIME_PER_UNIT[baseUnit] && baseUnit !== MILLISECONDS) {\n                        baseUnit = BASE_UNITS[baseUnitIndex - 1];\n                        autoBaseUnitStep = last(autoBaseUnitSteps[baseUnit]);\n                        ticks = (rangeDiff - (maxDateGroups - 1) * autoBaseUnitStep * TIME_PER_UNIT[baseUnit]) / 2;\n                        min = addTicks(rangeMin, ticks);\n                        max = addTicks(rangeMax, -ticks);\n                    } else if (diff > maxDiff && baseUnit !== YEARS) {\n                        var stepIndex = 0;\n                        do {\n                            baseUnitIndex++;\n                            baseUnit = BASE_UNITS[baseUnitIndex];\n                            stepIndex = 0;\n                            ticks = 2 * TIME_PER_UNIT[baseUnit];\n                            do {\n                                autoBaseUnitStep = autoBaseUnitSteps[baseUnit][stepIndex];\n                                stepIndex++;\n                            } while (stepIndex < autoBaseUnitSteps[baseUnit].length && ticks * autoBaseUnitStep < rangeDiff);\n                        } while (baseUnit !== YEARS && ticks * autoBaseUnitStep < rangeDiff);\n                        ticks = (ticks * autoBaseUnitStep - rangeDiff) / 2;\n                        if (ticks > 0) {\n                            min = addTicks(rangeMin, -ticks);\n                            max = addTicks(rangeMax, ticks);\n                            min = addTicks(min, limitValue(max, totalLimits.min, totalLimits.max) - max);\n                            max = addTicks(max, limitValue(min, totalLimits.min, totalLimits.max) - min);\n                        }\n                    }\n                }\n                if (min < totalLimits.min) {\n                    min = totalLimits.min;\n                }\n                if (max > totalLimits.max) {\n                    max = totalLimits.max;\n                }\n                if (min && max && dateDiff(max, min) > 0) {\n                    return {\n                        min: min,\n                        max: max,\n                        baseUnit: options.userSetBaseUnit || options.baseUnit,\n                        baseUnitStep: options.userSetBaseUnitStep || options.baseUnitStep\n                    };\n                }\n            },\n            range: function () {\n                return this.dataRange.displayRange();\n            },\n            createAxisLabel: function (index, labelOptions) {\n                var options = this.options;\n                var dataItem = options.dataItems && !options.maxDivisions ? options.dataItems[index] : null;\n                var date = this.divisionRange.dateAt(index);\n                var unitFormat = labelOptions.dateFormats[this.divisionRange.options.baseUnit];\n                labelOptions.format = labelOptions.format || unitFormat;\n                var text = this.axisLabelText(date, dataItem, labelOptions);\n                if (text) {\n                    return new AxisLabel(date, text, index, dataItem, labelOptions);\n                }\n            },\n            categoryIndex: function (value) {\n                return this.dataRange.valueIndex(value);\n            },\n            slot: function (from, to, limit) {\n                var dateRange = this.dataRange;\n                var start = from;\n                var end = to;\n                if (start instanceof Date) {\n                    start = dateRange.dateIndex(start);\n                }\n                if (end instanceof Date) {\n                    end = dateRange.dateIndex(end);\n                }\n                var slot = this.getSlot(start, end, limit);\n                if (slot) {\n                    return slot.toRect();\n                }\n            },\n            getSlot: function (a, b, limit) {\n                var start = a;\n                var end = b;\n                if (typeof start === OBJECT) {\n                    start = this.categoryIndex(start);\n                }\n                if (typeof end === OBJECT) {\n                    end = this.categoryIndex(end);\n                }\n                return CategoryAxis.fn.getSlot.call(this, start, end, limit);\n            },\n            valueRange: function () {\n                var options = this.options;\n                var range = categoryRange(options.srcCategories);\n                return {\n                    min: toDate(range.min),\n                    max: toDate(range.max)\n                };\n            },\n            categoryAt: function (index, total) {\n                return this.dataRange.dateAt(index, total);\n            },\n            categoriesCount: function () {\n                return this.dataRange.valuesCount();\n            },\n            rangeIndices: function () {\n                return this.dataRange.displayIndices();\n            },\n            labelsBetweenTicks: function () {\n                return !this.divisionRange.options.justified;\n            },\n            prepareUserOptions: function () {\n                if (this.isEmpty()) {\n                    return;\n                }\n                this.options.categories = this.dataRange.values();\n            },\n            getCategory: function (point) {\n                var index = this.pointCategoryIndex(point);\n                if (index === null) {\n                    return null;\n                }\n                return this.dataRange.dateAt(index);\n            },\n            totalIndex: function (value) {\n                return this.dataRange.totalIndex(value);\n            },\n            currentRangeIndices: function () {\n                var range = this.dataRange.valueRange();\n                return {\n                    min: this.dataRange.totalIndex(range.min),\n                    max: this.dataRange.totalIndex(range.max)\n                };\n            },\n            totalRange: function () {\n                return this.dataRange.total();\n            },\n            totalCount: function () {\n                return this.dataRange.totalCount();\n            },\n            isEmpty: function () {\n                return !this.options.srcCategories.length;\n            },\n            roundedRange: function () {\n                if (this.options.roundToBaseUnit !== false || this.isEmpty()) {\n                    return this.range();\n                }\n                var options = this.options;\n                var datesRange = categoryRange(options.srcCategories);\n                var dateRange = new DateRange(datesRange.min, datesRange.max, $.extend({}, options, {\n                    justified: false,\n                    roundToBaseUnit: true,\n                    justifyEnd: options.justified\n                }));\n                return dateRange.displayRange();\n            }\n        });\n        setDefaultOptions(DateCategoryAxis, {\n            type: DATE,\n            labels: { dateFormats: DateLabelFormats },\n            autoBaseUnitSteps: {\n                milliseconds: [\n                    1,\n                    10,\n                    100\n                ],\n                seconds: [\n                    1,\n                    2,\n                    5,\n                    15,\n                    30\n                ],\n                minutes: [\n                    1,\n                    2,\n                    5,\n                    15,\n                    30\n                ],\n                hours: [\n                    1,\n                    2,\n                    3\n                ],\n                days: [\n                    1,\n                    2,\n                    3\n                ],\n                weeks: [\n                    1,\n                    2\n                ],\n                months: [\n                    1,\n                    2,\n                    3,\n                    6\n                ],\n                years: [\n                    1,\n                    2,\n                    3,\n                    5,\n                    10,\n                    25,\n                    50\n                ]\n            },\n            maxDateGroups: 10\n        });\n        function autoAxisMin(min, max, narrow) {\n            if (!min && !max) {\n                return 0;\n            }\n            var axisMin;\n            if (min >= 0 && max >= 0) {\n                var minValue = min === max ? 0 : min;\n                var diff = (max - minValue) / max;\n                if (narrow === false || !narrow && diff > ZERO_THRESHOLD) {\n                    return 0;\n                }\n                axisMin = Math.max(0, minValue - (max - minValue) / 2);\n            } else {\n                axisMin = min;\n            }\n            return axisMin;\n        }\n        function autoAxisMax(min, max, narrow) {\n            if (!min && !max) {\n                return 1;\n            }\n            var axisMax;\n            if (min <= 0 && max <= 0) {\n                var maxValue = min === max ? 0 : max;\n                var diff = Math.abs((maxValue - min) / maxValue);\n                if (narrow === false || !narrow && diff > ZERO_THRESHOLD) {\n                    return 0;\n                }\n                axisMax = Math.min(0, maxValue - (min - maxValue) / 2);\n            } else {\n                axisMax = max;\n            }\n            return axisMax;\n        }\n        function floor(value, step) {\n            return round(Math.floor(value / step) * step, DEFAULT_PRECISION);\n        }\n        function ceil(value, step) {\n            return round(Math.ceil(value / step) * step, DEFAULT_PRECISION);\n        }\n        function limitCoordinate(value) {\n            return Math.max(Math.min(value, COORDINATE_LIMIT), -COORDINATE_LIMIT);\n        }\n        var MIN_VALUE_RANGE = Math.pow(10, -DEFAULT_PRECISION + 1);\n        var NumericAxis = Axis.extend({\n            init: function (seriesMin, seriesMax, options, chartService) {\n                Axis.fn.init.call(this, $.extend({}, options, {\n                    seriesMin: seriesMin,\n                    seriesMax: seriesMax\n                }), chartService);\n            },\n            initUserOptions: function (options) {\n                var autoOptions = autoAxisOptions(options.seriesMin, options.seriesMax, options);\n                this.totalOptions = totalAxisOptions(autoOptions, options);\n                return axisOptions(autoOptions, options);\n            },\n            initFields: function () {\n                this.totalMin = this.totalOptions.min;\n                this.totalMax = this.totalOptions.max;\n                this.totalMajorUnit = this.totalOptions.majorUnit;\n                this.seriesMin = this.options.seriesMin;\n                this.seriesMax = this.options.seriesMax;\n            },\n            clone: function () {\n                return new NumericAxis(this.seriesMin, this.seriesMax, $.extend({}, this.options), this.chartService);\n            },\n            startValue: function () {\n                return 0;\n            },\n            range: function () {\n                var options = this.options;\n                return {\n                    min: options.min,\n                    max: options.max\n                };\n            },\n            getDivisions: function (stepValue) {\n                if (stepValue === 0) {\n                    return 1;\n                }\n                var options = this.options;\n                var range = options.max - options.min;\n                return Math.floor(round(range / stepValue, COORD_PRECISION)) + 1;\n            },\n            getTickPositions: function (unit, skipUnit) {\n                var options = this.options;\n                var vertical = options.vertical;\n                var reverse = options.reverse;\n                var lineBox = this.lineBox();\n                var lineSize = vertical ? lineBox.height() : lineBox.width();\n                var range = options.max - options.min;\n                var scale = lineSize / range;\n                var step = unit * scale;\n                var divisions = this.getDivisions(unit);\n                var dir = (vertical ? -1 : 1) * (reverse ? -1 : 1);\n                var startEdge = dir === 1 ? 1 : 2;\n                var positions = [];\n                var pos = lineBox[(vertical ? Y : X) + startEdge];\n                var skipStep = 0;\n                if (skipUnit) {\n                    skipStep = skipUnit / unit;\n                }\n                for (var idx = 0; idx < divisions; idx++) {\n                    if (idx % skipStep !== 0) {\n                        positions.push(round(pos, COORD_PRECISION));\n                    }\n                    pos = pos + step * dir;\n                }\n                return positions;\n            },\n            getMajorTickPositions: function () {\n                return this.getTickPositions(this.options.majorUnit);\n            },\n            getMinorTickPositions: function () {\n                return this.getTickPositions(this.options.minorUnit);\n            },\n            getSlot: function (a, b, limit) {\n                if (limit === void 0) {\n                    limit = false;\n                }\n                var options = this.options;\n                var vertical = options.vertical;\n                var reverse = options.reverse;\n                var valueAxis = vertical ? Y : X;\n                var lineBox = this.lineBox();\n                var lineStart = lineBox[valueAxis + (reverse ? 2 : 1)];\n                var lineSize = vertical ? lineBox.height() : lineBox.width();\n                var dir = reverse ? -1 : 1;\n                var step = dir * (lineSize / (options.max - options.min));\n                var slotBox = new Box(lineBox.x1, lineBox.y1, lineBox.x1, lineBox.y1);\n                var start = a;\n                var end = b;\n                if (!defined(start)) {\n                    start = end || 0;\n                }\n                if (!defined(end)) {\n                    end = start || 0;\n                }\n                if (limit) {\n                    start = Math.max(Math.min(start, options.max), options.min);\n                    end = Math.max(Math.min(end, options.max), options.min);\n                }\n                var p1, p2;\n                if (vertical) {\n                    p1 = options.max - Math.max(start, end);\n                    p2 = options.max - Math.min(start, end);\n                } else {\n                    p1 = Math.min(start, end) - options.min;\n                    p2 = Math.max(start, end) - options.min;\n                }\n                slotBox[valueAxis + 1] = limitCoordinate(lineStart + step * (reverse ? p2 : p1));\n                slotBox[valueAxis + 2] = limitCoordinate(lineStart + step * (reverse ? p1 : p2));\n                return slotBox;\n            },\n            getValue: function (point) {\n                var options = this.options;\n                var vertical = options.vertical;\n                var reverse = options.reverse;\n                var max = Number(options.max);\n                var min = Number(options.min);\n                var valueAxis = vertical ? Y : X;\n                var lineBox = this.lineBox();\n                var lineStart = lineBox[valueAxis + (reverse ? 2 : 1)];\n                var lineSize = vertical ? lineBox.height() : lineBox.width();\n                var dir = reverse ? -1 : 1;\n                var offset = dir * (point[valueAxis] - lineStart);\n                var step = (max - min) / lineSize;\n                var valueOffset = offset * step;\n                if (offset < 0 || offset > lineSize) {\n                    return null;\n                }\n                var value = vertical ? max - valueOffset : min + valueOffset;\n                return round(value, DEFAULT_PRECISION);\n            },\n            translateRange: function (delta) {\n                var options = this.options;\n                var vertical = options.vertical;\n                var reverse = options.reverse;\n                var max = options.max;\n                var min = options.min;\n                var lineBox = this.lineBox();\n                var size = vertical ? lineBox.height() : lineBox.width();\n                var range = max - min;\n                var scale = size / range;\n                var offset = round(delta / scale, DEFAULT_PRECISION);\n                if ((vertical || reverse) && !(vertical && reverse)) {\n                    offset = -offset;\n                }\n                return {\n                    min: min + offset,\n                    max: max + offset,\n                    offset: offset\n                };\n            },\n            scaleRange: function (delta) {\n                var options = this.options;\n                var offset = -delta * options.majorUnit;\n                return {\n                    min: options.min - offset,\n                    max: options.max + offset\n                };\n            },\n            labelsCount: function () {\n                return this.getDivisions(this.options.majorUnit);\n            },\n            createAxisLabel: function (index, labelOptions) {\n                var options = this.options;\n                var value = round(options.min + index * options.majorUnit, DEFAULT_PRECISION);\n                var text = this.axisLabelText(value, null, labelOptions);\n                return new AxisLabel(value, text, index, null, labelOptions);\n            },\n            shouldRenderNote: function (value) {\n                var range = this.range();\n                return range.min <= value && value <= range.max;\n            },\n            pan: function (delta) {\n                var range = this.translateRange(delta);\n                return this.limitRange(range.min, range.max, this.totalMin, this.totalMax, range.offset);\n            },\n            pointsRange: function (start, end) {\n                var startValue = this.getValue(start);\n                var endValue = this.getValue(end);\n                var min = Math.min(startValue, endValue);\n                var max = Math.max(startValue, endValue);\n                if (this.isValidRange(min, max)) {\n                    return {\n                        min: min,\n                        max: max\n                    };\n                }\n            },\n            zoomRange: function (delta) {\n                var ref = this;\n                var totalMin = ref.totalMin;\n                var totalMax = ref.totalMax;\n                var newRange = this.scaleRange(delta);\n                var min = limitValue(newRange.min, totalMin, totalMax);\n                var max = limitValue(newRange.max, totalMin, totalMax);\n                if (this.isValidRange(min, max)) {\n                    return {\n                        min: min,\n                        max: max\n                    };\n                }\n            },\n            isValidRange: function (min, max) {\n                return max - min > MIN_VALUE_RANGE;\n            }\n        });\n        function autoAxisOptions(seriesMin, seriesMax, options) {\n            var narrowRange = options.narrowRange;\n            var autoMin = autoAxisMin(seriesMin, seriesMax, narrowRange);\n            var autoMax = autoAxisMax(seriesMin, seriesMax, narrowRange);\n            var majorUnit = autoMajorUnit(autoMin, autoMax);\n            var autoOptions = { majorUnit: majorUnit };\n            if (options.roundToMajorUnit !== false) {\n                if (autoMin < 0 && remainderClose(autoMin, majorUnit, 1 / 3)) {\n                    autoMin -= majorUnit;\n                }\n                if (autoMax > 0 && remainderClose(autoMax, majorUnit, 1 / 3)) {\n                    autoMax += majorUnit;\n                }\n            }\n            autoOptions.min = floor(autoMin, majorUnit);\n            autoOptions.max = ceil(autoMax, majorUnit);\n            return autoOptions;\n        }\n        function totalAxisOptions(autoOptions, options) {\n            return {\n                min: defined(options.min) ? Math.min(autoOptions.min, options.min) : autoOptions.min,\n                max: defined(options.max) ? Math.max(autoOptions.max, options.max) : autoOptions.max,\n                majorUnit: autoOptions.majorUnit\n            };\n        }\n        function clearNullValues(options, fields) {\n            for (var idx = 0; idx < fields.length; idx++) {\n                var field = fields[idx];\n                if (options[field] === null) {\n                    options[field] = undefined;\n                }\n            }\n        }\n        function axisOptions(autoOptions, userOptions) {\n            var options = userOptions;\n            var userSetMin, userSetMax;\n            if (userOptions) {\n                clearNullValues(userOptions, [\n                    'min',\n                    'max'\n                ]);\n                userSetMin = defined(userOptions.min);\n                userSetMax = defined(userOptions.max);\n                var userSetLimits = userSetMin || userSetMax;\n                if (userSetLimits) {\n                    if (userOptions.min === userOptions.max) {\n                        if (userOptions.min > 0) {\n                            userOptions.min = 0;\n                        } else {\n                            userOptions.max = 1;\n                        }\n                    }\n                }\n                if (userOptions.majorUnit) {\n                    autoOptions.min = floor(autoOptions.min, userOptions.majorUnit);\n                    autoOptions.max = ceil(autoOptions.max, userOptions.majorUnit);\n                } else if (userSetLimits) {\n                    options = deepExtend(autoOptions, userOptions);\n                    autoOptions.majorUnit = autoMajorUnit(options.min, options.max);\n                }\n            }\n            autoOptions.minorUnit = (options.majorUnit || autoOptions.majorUnit) / 5;\n            var result = deepExtend(autoOptions, options);\n            if (result.min >= result.max) {\n                if (userSetMin && !userSetMax) {\n                    result.max = result.min + result.majorUnit;\n                } else if (!userSetMin && userSetMax) {\n                    result.min = result.max - result.majorUnit;\n                }\n            }\n            return result;\n        }\n        function remainderClose(value, divisor, ratio) {\n            var remainder = round(Math.abs(value % divisor), DEFAULT_PRECISION);\n            var threshold = divisor * (1 - ratio);\n            return remainder === 0 || remainder > threshold;\n        }\n        setDefaultOptions(NumericAxis, {\n            type: 'numeric',\n            min: 0,\n            max: 1,\n            vertical: true,\n            majorGridLines: {\n                visible: true,\n                width: 1,\n                color: BLACK\n            },\n            labels: { format: '#.####################' },\n            zIndex: 1\n        });\n        var DateValueAxis = Axis.extend({\n            init: function (seriesMin, seriesMax, axisOptions, chartService) {\n                var min = toDate(seriesMin);\n                var max = toDate(seriesMax);\n                var intlService = chartService.intl;\n                var options = axisOptions || {};\n                options = deepExtend(options || {}, {\n                    min: parseDate(intlService, options.min),\n                    max: parseDate(intlService, options.max),\n                    axisCrossingValue: parseDates(intlService, options.axisCrossingValues || options.axisCrossingValue)\n                });\n                options = applyDefaults(min, max, options);\n                Axis.fn.init.call(this, options, chartService);\n                this.intlService = intlService;\n                this.seriesMin = min;\n                this.seriesMax = max;\n                this.totalMin = toTime(floorDate(toTime(min) - 1, options.baseUnit));\n                this.totalMax = toTime(ceilDate(toTime(max) + 1, options.baseUnit));\n            },\n            clone: function () {\n                return new DateValueAxis(this.seriesMin, this.seriesMax, $.extend({}, this.options), this.chartService);\n            },\n            range: function () {\n                var options = this.options;\n                return {\n                    min: options.min,\n                    max: options.max\n                };\n            },\n            getDivisions: function (stepValue) {\n                var options = this.options;\n                return Math.floor(duration(options.min, options.max, options.baseUnit) / stepValue + 1);\n            },\n            getTickPositions: function (step) {\n                var options = this.options;\n                var vertical = options.vertical;\n                var lineBox = this.lineBox();\n                var dir = (vertical ? -1 : 1) * (options.reverse ? -1 : 1);\n                var startEdge = dir === 1 ? 1 : 2;\n                var start = lineBox[(vertical ? Y : X) + startEdge];\n                var divisions = this.getDivisions(step);\n                var timeRange = dateDiff(options.max, options.min);\n                var lineSize = vertical ? lineBox.height() : lineBox.width();\n                var scale = lineSize / timeRange;\n                var positions = [start];\n                for (var i = 1; i < divisions; i++) {\n                    var date = addDuration(options.min, i * step, options.baseUnit);\n                    var pos = start + dateDiff(date, options.min) * scale * dir;\n                    positions.push(round(pos, COORD_PRECISION));\n                }\n                return positions;\n            },\n            getMajorTickPositions: function () {\n                return this.getTickPositions(this.options.majorUnit);\n            },\n            getMinorTickPositions: function () {\n                return this.getTickPositions(this.options.minorUnit);\n            },\n            getSlot: function (a, b, limit) {\n                return NumericAxis.prototype.getSlot.call(this, parseDate(this.intlService, a), parseDate(this.intlService, b), limit);\n            },\n            getValue: function (point) {\n                var value = NumericAxis.prototype.getValue.call(this, point);\n                return value !== null ? toDate(value) : null;\n            },\n            labelsCount: function () {\n                return this.getDivisions(this.options.majorUnit);\n            },\n            createAxisLabel: function (index, labelOptions) {\n                var options = this.options;\n                var offset = index * options.majorUnit;\n                var date = options.min;\n                if (offset > 0) {\n                    date = addDuration(date, offset, options.baseUnit);\n                }\n                var unitFormat = labelOptions.dateFormats[options.baseUnit];\n                labelOptions.format = labelOptions.format || unitFormat;\n                var text = this.axisLabelText(date, null, labelOptions);\n                return new AxisLabel(date, text, index, null, labelOptions);\n            },\n            translateRange: function (delta, exact) {\n                var options = this.options;\n                var baseUnit = options.baseUnit;\n                var weekStartDay = options.weekStartDay;\n                var lineBox = this.lineBox();\n                var size = options.vertical ? lineBox.height() : lineBox.width();\n                var range = this.range();\n                var scale = size / dateDiff(range.max, range.min);\n                var offset = round(delta / scale, DEFAULT_PRECISION) * (options.reverse ? -1 : 1);\n                var from = addTicks(options.min, offset);\n                var to = addTicks(options.max, offset);\n                if (!exact) {\n                    from = addDuration(from, 0, baseUnit, weekStartDay);\n                    to = addDuration(to, 0, baseUnit, weekStartDay);\n                }\n                return {\n                    min: from,\n                    max: to,\n                    offset: offset\n                };\n            },\n            scaleRange: function (delta) {\n                var ref = this.options;\n                var from = ref.min;\n                var to = ref.max;\n                var rounds = Math.abs(delta);\n                while (rounds--) {\n                    var range = dateDiff(from, to);\n                    var step = Math.round(range * 0.1);\n                    if (delta < 0) {\n                        from = addTicks(from, step);\n                        to = addTicks(to, -step);\n                    } else {\n                        from = addTicks(from, -step);\n                        to = addTicks(to, step);\n                    }\n                }\n                return {\n                    min: from,\n                    max: to\n                };\n            },\n            shouldRenderNote: function (value) {\n                var range = this.range();\n                return dateComparer(value, range.min) >= 0 && dateComparer(value, range.max) <= 0;\n            },\n            pan: function (delta) {\n                var range = this.translateRange(delta, true);\n                var limittedRange = this.limitRange(toTime(range.min), toTime(range.max), this.totalMin, this.totalMax, range.offset);\n                if (limittedRange) {\n                    return {\n                        min: toDate(limittedRange.min),\n                        max: toDate(limittedRange.max)\n                    };\n                }\n            },\n            pointsRange: function (start, end) {\n                var startValue = this.getValue(start);\n                var endValue = this.getValue(end);\n                var min = Math.min(startValue, endValue);\n                var max = Math.max(startValue, endValue);\n                return {\n                    min: toDate(min),\n                    max: toDate(max)\n                };\n            },\n            zoomRange: function (delta) {\n                var range = this.scaleRange(delta);\n                var min = toDate(limitValue(toTime(range.min), this.totalMin, this.totalMax));\n                var max = toDate(limitValue(toTime(range.max), this.totalMin, this.totalMax));\n                return {\n                    min: min,\n                    max: max\n                };\n            }\n        });\n        function timeUnits(delta) {\n            var unit = HOURS;\n            if (delta >= TIME_PER_YEAR) {\n                unit = YEARS;\n            } else if (delta >= TIME_PER_MONTH) {\n                unit = MONTHS;\n            } else if (delta >= TIME_PER_WEEK) {\n                unit = WEEKS;\n            } else if (delta >= TIME_PER_DAY) {\n                unit = DAYS;\n            }\n            return unit;\n        }\n        function applyDefaults(seriesMin, seriesMax, options) {\n            var min = options.min || seriesMin;\n            var max = options.max || seriesMax;\n            var baseUnit = options.baseUnit || (max && min ? timeUnits(absoluteDateDiff(max, min)) : HOURS);\n            var baseUnitTime = TIME_PER_UNIT[baseUnit];\n            var autoMin = floorDate(toTime(min) - 1, baseUnit) || toDate(max);\n            var autoMax = ceilDate(toTime(max) + 1, baseUnit);\n            var userMajorUnit = options.majorUnit ? options.majorUnit : undefined;\n            var majorUnit = userMajorUnit || ceil(autoMajorUnit(autoMin.getTime(), autoMax.getTime()), baseUnitTime) / baseUnitTime;\n            var actualUnits = duration(autoMin, autoMax, baseUnit);\n            var totalUnits = ceil(actualUnits, majorUnit);\n            var unitsToAdd = totalUnits - actualUnits;\n            var head = Math.floor(unitsToAdd / 2);\n            var tail = unitsToAdd - head;\n            if (!options.baseUnit) {\n                delete options.baseUnit;\n            }\n            options.baseUnit = options.baseUnit || baseUnit;\n            options.min = options.min || addDuration(autoMin, -head, baseUnit);\n            options.max = options.max || addDuration(autoMax, tail, baseUnit);\n            options.minorUnit = options.minorUnit || majorUnit / 5;\n            options.majorUnit = majorUnit;\n            return options;\n        }\n        setDefaultOptions(DateValueAxis, {\n            type: DATE,\n            majorGridLines: {\n                visible: true,\n                width: 1,\n                color: BLACK\n            },\n            labels: { dateFormats: DateLabelFormats }\n        });\n        var DEFAULT_MAJOR_UNIT = 10;\n        var LogarithmicAxis = Axis.extend({\n            init: function (seriesMin, seriesMax, options, chartService) {\n                var axisOptions = deepExtend({\n                    majorUnit: DEFAULT_MAJOR_UNIT,\n                    min: seriesMin,\n                    max: seriesMax\n                }, options);\n                var base = axisOptions.majorUnit;\n                var autoMax = autoAxisMax$1(seriesMax, base);\n                var autoMin = autoAxisMin$1(seriesMin, seriesMax, axisOptions);\n                var range = initRange(autoMin, autoMax, axisOptions, options);\n                axisOptions.max = range.max;\n                axisOptions.min = range.min;\n                axisOptions.minorUnit = options.minorUnit || round(base - 1, DEFAULT_PRECISION);\n                Axis.fn.init.call(this, axisOptions, chartService);\n                this.totalMin = defined(options.min) ? Math.min(autoMin, options.min) : autoMin;\n                this.totalMax = defined(options.max) ? Math.max(autoMax, options.max) : autoMax;\n                this.logMin = round(log(range.min, base), DEFAULT_PRECISION);\n                this.logMax = round(log(range.max, base), DEFAULT_PRECISION);\n                this.seriesMin = seriesMin;\n                this.seriesMax = seriesMax;\n                this.createLabels();\n            },\n            clone: function () {\n                return new LogarithmicAxis(this.seriesMin, this.seriesMax, $.extend({}, this.options), this.chartService);\n            },\n            startValue: function () {\n                return this.options.min;\n            },\n            getSlot: function (a, b, limit) {\n                var ref = this;\n                var options = ref.options;\n                var logMin = ref.logMin;\n                var logMax = ref.logMax;\n                var reverse = options.reverse;\n                var vertical = options.vertical;\n                var base = options.majorUnit;\n                var valueAxis = vertical ? Y : X;\n                var lineBox = this.lineBox();\n                var lineStart = lineBox[valueAxis + (reverse ? 2 : 1)];\n                var lineSize = vertical ? lineBox.height() : lineBox.width();\n                var dir = reverse ? -1 : 1;\n                var step = dir * (lineSize / (logMax - logMin));\n                var slotBox = new Box(lineBox.x1, lineBox.y1, lineBox.x1, lineBox.y1);\n                var start = a;\n                var end = b;\n                if (!defined(start)) {\n                    start = end || 1;\n                }\n                if (!defined(end)) {\n                    end = start || 1;\n                }\n                if (start <= 0 || end <= 0) {\n                    return null;\n                }\n                if (limit) {\n                    start = Math.max(Math.min(start, options.max), options.min);\n                    end = Math.max(Math.min(end, options.max), options.min);\n                }\n                start = log(start, base);\n                end = log(end, base);\n                var p1, p2;\n                if (vertical) {\n                    p1 = logMax - Math.max(start, end);\n                    p2 = logMax - Math.min(start, end);\n                } else {\n                    p1 = Math.min(start, end) - logMin;\n                    p2 = Math.max(start, end) - logMin;\n                }\n                slotBox[valueAxis + 1] = limitCoordinate(lineStart + step * (reverse ? p2 : p1));\n                slotBox[valueAxis + 2] = limitCoordinate(lineStart + step * (reverse ? p1 : p2));\n                return slotBox;\n            },\n            getValue: function (point) {\n                var ref = this;\n                var options = ref.options;\n                var logMin = ref.logMin;\n                var logMax = ref.logMax;\n                var reverse = options.reverse;\n                var vertical = options.vertical;\n                var base = options.majorUnit;\n                var lineBox = this.lineBox();\n                var dir = vertical === reverse ? 1 : -1;\n                var startEdge = dir === 1 ? 1 : 2;\n                var lineSize = vertical ? lineBox.height() : lineBox.width();\n                var step = (logMax - logMin) / lineSize;\n                var valueAxis = vertical ? Y : X;\n                var lineStart = lineBox[valueAxis + startEdge];\n                var offset = dir * (point[valueAxis] - lineStart);\n                var valueOffset = offset * step;\n                if (offset < 0 || offset > lineSize) {\n                    return null;\n                }\n                var value = logMin + valueOffset;\n                return round(Math.pow(base, value), DEFAULT_PRECISION);\n            },\n            range: function () {\n                var options = this.options;\n                return {\n                    min: options.min,\n                    max: options.max\n                };\n            },\n            scaleRange: function (delta) {\n                var base = this.options.majorUnit;\n                var offset = -delta;\n                return {\n                    min: Math.pow(base, this.logMin - offset),\n                    max: Math.pow(base, this.logMax + offset)\n                };\n            },\n            translateRange: function (delta) {\n                var ref = this;\n                var options = ref.options;\n                var logMin = ref.logMin;\n                var logMax = ref.logMax;\n                var reverse = options.reverse;\n                var vertical = options.vertical;\n                var base = options.majorUnit;\n                var lineBox = this.lineBox();\n                var size = vertical ? lineBox.height() : lineBox.width();\n                var scale = size / (logMax - logMin);\n                var offset = round(delta / scale, DEFAULT_PRECISION);\n                if ((vertical || reverse) && !(vertical && reverse)) {\n                    offset = -offset;\n                }\n                return {\n                    min: Math.pow(base, logMin + offset),\n                    max: Math.pow(base, logMax + offset),\n                    offset: offset\n                };\n            },\n            labelsCount: function () {\n                var floorMax = Math.floor(this.logMax);\n                var count = Math.floor(floorMax - this.logMin) + 1;\n                return count;\n            },\n            getMajorTickPositions: function () {\n                var ticks = [];\n                this.traverseMajorTicksPositions(function (position) {\n                    ticks.push(position);\n                }, {\n                    step: 1,\n                    skip: 0\n                });\n                return ticks;\n            },\n            createTicks: function (lineGroup) {\n                var options = this.options;\n                var majorTicks = options.majorTicks;\n                var minorTicks = options.minorTicks;\n                var vertical = options.vertical;\n                var mirror = options.labels.mirror;\n                var lineBox = this.lineBox();\n                var ticks = [];\n                var tickLineOptions = { vertical: vertical };\n                function render(tickPosition, tickOptions) {\n                    tickLineOptions.tickX = mirror ? lineBox.x2 : lineBox.x2 - tickOptions.size;\n                    tickLineOptions.tickY = mirror ? lineBox.y1 - tickOptions.size : lineBox.y1;\n                    tickLineOptions.position = tickPosition;\n                    lineGroup.append(createAxisTick(tickLineOptions, tickOptions));\n                }\n                if (majorTicks.visible) {\n                    this.traverseMajorTicksPositions(render, majorTicks);\n                }\n                if (minorTicks.visible) {\n                    this.traverseMinorTicksPositions(render, minorTicks);\n                }\n                return ticks;\n            },\n            createGridLines: function (altAxis) {\n                var options = this.options;\n                var minorGridLines = options.minorGridLines;\n                var majorGridLines = options.majorGridLines;\n                var vertical = options.vertical;\n                var lineBox = altAxis.lineBox();\n                var lineOptions = {\n                    lineStart: lineBox[vertical ? 'x1' : 'y1'],\n                    lineEnd: lineBox[vertical ? 'x2' : 'y2'],\n                    vertical: vertical\n                };\n                var majorTicks = [];\n                var container = this.gridLinesVisual();\n                function render(tickPosition, gridLine) {\n                    if (!inArray(tickPosition, majorTicks)) {\n                        lineOptions.position = tickPosition;\n                        container.append(createAxisGridLine(lineOptions, gridLine));\n                        majorTicks.push(tickPosition);\n                    }\n                }\n                if (majorGridLines.visible) {\n                    this.traverseMajorTicksPositions(render, majorGridLines);\n                }\n                if (minorGridLines.visible) {\n                    this.traverseMinorTicksPositions(render, minorGridLines);\n                }\n                return container.children;\n            },\n            traverseMajorTicksPositions: function (callback, tickOptions) {\n                var ref = this._lineOptions();\n                var lineStart = ref.lineStart;\n                var step = ref.step;\n                var ref$1 = this;\n                var logMin = ref$1.logMin;\n                var logMax = ref$1.logMax;\n                for (var power = Math.ceil(logMin) + tickOptions.skip; power <= logMax; power += tickOptions.step) {\n                    var position = round(lineStart + step * (power - logMin), DEFAULT_PRECISION);\n                    callback(position, tickOptions);\n                }\n            },\n            traverseMinorTicksPositions: function (callback, tickOptions) {\n                var this$1 = this;\n                var ref = this.options;\n                var min = ref.min;\n                var max = ref.max;\n                var minorUnit = ref.minorUnit;\n                var base = ref.majorUnit;\n                var ref$1 = this._lineOptions();\n                var lineStart = ref$1.lineStart;\n                var step = ref$1.step;\n                var ref$2 = this;\n                var logMin = ref$2.logMin;\n                var logMax = ref$2.logMax;\n                var start = Math.floor(logMin);\n                for (var power = start; power < logMax; power++) {\n                    var minorOptions = this$1._minorIntervalOptions(power);\n                    for (var idx = tickOptions.skip; idx < minorUnit; idx += tickOptions.step) {\n                        var value = minorOptions.value + idx * minorOptions.minorStep;\n                        if (value > max) {\n                            break;\n                        }\n                        if (value >= min) {\n                            var position = round(lineStart + step * (log(value, base) - logMin), DEFAULT_PRECISION);\n                            callback(position, tickOptions);\n                        }\n                    }\n                }\n            },\n            createAxisLabel: function (index, labelOptions) {\n                var power = Math.ceil(this.logMin + index);\n                var value = Math.pow(this.options.majorUnit, power);\n                var text = this.axisLabelText(value, null, labelOptions);\n                return new AxisLabel(value, text, index, null, labelOptions);\n            },\n            shouldRenderNote: function (value) {\n                var range = this.range();\n                return range.min <= value && value <= range.max;\n            },\n            pan: function (delta) {\n                var range = this.translateRange(delta);\n                return this.limitRange(range.min, range.max, this.totalMin, this.totalMax, range.offset);\n            },\n            pointsRange: function (start, end) {\n                var startValue = this.getValue(start);\n                var endValue = this.getValue(end);\n                var min = Math.min(startValue, endValue);\n                var max = Math.max(startValue, endValue);\n                return {\n                    min: min,\n                    max: max\n                };\n            },\n            zoomRange: function (delta) {\n                var ref = this;\n                var options = ref.options;\n                var totalMin = ref.totalMin;\n                var totalMax = ref.totalMax;\n                var newRange = this.scaleRange(delta);\n                var min = limitValue(newRange.min, totalMin, totalMax);\n                var max = limitValue(newRange.max, totalMin, totalMax);\n                var base = options.majorUnit;\n                var acceptOptionsRange = max > min && options.min && options.max && round(log(options.max, base) - log(options.min, base), DEFAULT_PRECISION) < 1;\n                var acceptNewRange = !(options.min === totalMin && options.max === totalMax) && round(log(max, base) - log(min, base), DEFAULT_PRECISION) >= 1;\n                if (acceptOptionsRange || acceptNewRange) {\n                    return {\n                        min: min,\n                        max: max\n                    };\n                }\n            },\n            _minorIntervalOptions: function (power) {\n                var ref = this.options;\n                var minorUnit = ref.minorUnit;\n                var base = ref.majorUnit;\n                var value = Math.pow(base, power);\n                var nextValue = Math.pow(base, power + 1);\n                var difference = nextValue - value;\n                var minorStep = difference / minorUnit;\n                return {\n                    value: value,\n                    minorStep: minorStep\n                };\n            },\n            _lineOptions: function () {\n                var ref = this.options;\n                var reverse = ref.reverse;\n                var vertical = ref.vertical;\n                var valueAxis = vertical ? Y : X;\n                var lineBox = this.lineBox();\n                var dir = vertical === reverse ? 1 : -1;\n                var startEdge = dir === 1 ? 1 : 2;\n                var lineSize = vertical ? lineBox.height() : lineBox.width();\n                var step = dir * (lineSize / (this.logMax - this.logMin));\n                var lineStart = lineBox[valueAxis + startEdge];\n                return {\n                    step: step,\n                    lineStart: lineStart,\n                    lineBox: lineBox\n                };\n            }\n        });\n        function initRange(autoMin, autoMax, axisOptions, options) {\n            var min = axisOptions.min;\n            var max = axisOptions.max;\n            if (defined(axisOptions.axisCrossingValue) && axisOptions.axisCrossingValue <= 0) {\n                throwNegativeValuesError();\n            }\n            if (!defined(options.max)) {\n                max = autoMax;\n            } else if (options.max <= 0) {\n                throwNegativeValuesError();\n            }\n            if (!defined(options.min)) {\n                min = autoMin;\n            } else if (options.min <= 0) {\n                throwNegativeValuesError();\n            }\n            return {\n                min: min,\n                max: max\n            };\n        }\n        function autoAxisMin$1(min, max, options) {\n            var base = options.majorUnit;\n            var autoMin = min;\n            if (min <= 0) {\n                autoMin = max <= 1 ? Math.pow(base, -2) : 1;\n            } else if (!options.narrowRange) {\n                autoMin = Math.pow(base, Math.floor(log(min, base)));\n            }\n            return autoMin;\n        }\n        function autoAxisMax$1(max, base) {\n            var logMaxRemainder = round(log(max, base), DEFAULT_PRECISION) % 1;\n            var autoMax;\n            if (max <= 0) {\n                autoMax = base;\n            } else if (logMaxRemainder !== 0 && (logMaxRemainder < 0.3 || logMaxRemainder > 0.9)) {\n                autoMax = Math.pow(base, log(max, base) + 0.2);\n            } else {\n                autoMax = Math.pow(base, Math.ceil(log(max, base)));\n            }\n            return autoMax;\n        }\n        function throwNegativeValuesError() {\n            throw new Error('Non positive values cannot be used for a logarithmic axis');\n        }\n        function log(y, x) {\n            return Math.log(y) / Math.log(x);\n        }\n        setDefaultOptions(LogarithmicAxis, {\n            type: 'log',\n            majorUnit: DEFAULT_MAJOR_UNIT,\n            minorUnit: 1,\n            axisCrossingValue: 1,\n            vertical: true,\n            majorGridLines: {\n                visible: true,\n                width: 1,\n                color: BLACK\n            },\n            zIndex: 1,\n            _deferLabels: true\n        });\n        var GridLinesMixin = {\n            createGridLines: function (altAxis) {\n                var options = this.options;\n                var radius = Math.abs(this.box.center().y - altAxis.lineBox().y1);\n                var gridLines = [];\n                var skipMajor = false;\n                var majorAngles, minorAngles;\n                if (options.majorGridLines.visible) {\n                    majorAngles = this.majorGridLineAngles(altAxis);\n                    skipMajor = true;\n                    gridLines = this.renderMajorGridLines(majorAngles, radius, options.majorGridLines);\n                }\n                if (options.minorGridLines.visible) {\n                    minorAngles = this.minorGridLineAngles(altAxis, skipMajor);\n                    append(gridLines, this.renderMinorGridLines(minorAngles, radius, options.minorGridLines, altAxis, skipMajor));\n                }\n                return gridLines;\n            },\n            renderMajorGridLines: function (angles, radius, options) {\n                return this.renderGridLines(angles, radius, options);\n            },\n            renderMinorGridLines: function (angles, radius, options, altAxis, skipMajor) {\n                var radiusCallback = this.radiusCallback && this.radiusCallback(radius, altAxis, skipMajor);\n                return this.renderGridLines(angles, radius, options, radiusCallback);\n            },\n            renderGridLines: function (angles, radius, options, radiusCallback) {\n                var style = {\n                    stroke: {\n                        width: options.width,\n                        color: options.color,\n                        dashType: options.dashType\n                    }\n                };\n                var center = this.box.center();\n                var circle = new Circle([\n                    center.x,\n                    center.y\n                ], radius);\n                var container = this.gridLinesVisual();\n                for (var i = 0; i < angles.length; i++) {\n                    var line = new Path(style);\n                    if (radiusCallback) {\n                        circle.radius = radiusCallback(angles[i]);\n                    }\n                    line.moveTo(circle.center).lineTo(circle.pointAt(angles[i] + 180));\n                    container.append(line);\n                }\n                return container.children;\n            },\n            gridLineAngles: function (altAxis, size, skip, step, skipAngles) {\n                var this$1 = this;\n                var divs = this.intervals(size, skip, step, skipAngles);\n                var options = altAxis.options;\n                var altAxisVisible = options.visible && (options.line || {}).visible !== false;\n                return map(divs, function (d) {\n                    var alpha = this$1.intervalAngle(d);\n                    if (!altAxisVisible || alpha !== 90) {\n                        return alpha;\n                    }\n                });\n            }\n        };\n        var RadarCategoryAxis = CategoryAxis.extend({\n            range: function () {\n                return {\n                    min: 0,\n                    max: this.options.categories.length\n                };\n            },\n            reflow: function (box) {\n                this.box = box;\n                this.reflowLabels();\n            },\n            lineBox: function () {\n                return this.box;\n            },\n            reflowLabels: function () {\n                var this$1 = this;\n                var ref = this;\n                var labels = ref.labels;\n                var labelOptions = ref.options.labels;\n                var skip = labelOptions.skip || 0;\n                var step = labelOptions.step || 1;\n                var measureBox = new Box();\n                for (var i = 0; i < labels.length; i++) {\n                    labels[i].reflow(measureBox);\n                    var labelBox = labels[i].box;\n                    labels[i].reflow(this$1.getSlot(skip + i * step).adjacentBox(0, labelBox.width(), labelBox.height()));\n                }\n            },\n            intervals: function (size, skipOption, stepOption, skipAngles) {\n                if (skipAngles === void 0) {\n                    skipAngles = false;\n                }\n                var options = this.options;\n                var categories = options.categories.length;\n                var divCount = categories / size || 1;\n                var divAngle = 360 / divCount;\n                var skip = skipOption || 0;\n                var step = stepOption || 1;\n                var divs = [];\n                var angle = 0;\n                for (var i = skip; i < divCount; i += step) {\n                    if (options.reverse) {\n                        angle = 360 - i * divAngle;\n                    } else {\n                        angle = i * divAngle;\n                    }\n                    angle = round(angle, COORD_PRECISION) % 360;\n                    if (!(skipAngles && inArray(angle, skipAngles))) {\n                        divs.push(angle);\n                    }\n                }\n                return divs;\n            },\n            majorIntervals: function () {\n                return this.intervals(1);\n            },\n            minorIntervals: function () {\n                return this.intervals(0.5);\n            },\n            intervalAngle: function (interval) {\n                return (360 + interval + this.options.startAngle) % 360;\n            },\n            majorAngles: function () {\n                var this$1 = this;\n                return map(this.majorIntervals(), function (interval) {\n                    return this$1.intervalAngle(interval);\n                });\n            },\n            createLine: function () {\n                return [];\n            },\n            majorGridLineAngles: function (altAxis) {\n                var majorGridLines = this.options.majorGridLines;\n                return this.gridLineAngles(altAxis, 1, majorGridLines.skip, majorGridLines.step);\n            },\n            minorGridLineAngles: function (altAxis, skipMajor) {\n                var ref = this.options;\n                var minorGridLines = ref.minorGridLines;\n                var majorGridLines = ref.majorGridLines;\n                var majorGridLineAngles = skipMajor ? this.intervals(1, majorGridLines.skip, majorGridLines.step) : null;\n                return this.gridLineAngles(altAxis, 0.5, minorGridLines.skip, minorGridLines.step, majorGridLineAngles);\n            },\n            radiusCallback: function (radius, altAxis, skipMajor) {\n                if (altAxis.options.type !== ARC) {\n                    var minorAngle = rad(360 / (this.options.categories.length * 2));\n                    var minorRadius = Math.cos(minorAngle) * radius;\n                    var majorAngles = this.majorAngles();\n                    var radiusCallback = function (angle) {\n                        if (!skipMajor && inArray(angle, majorAngles)) {\n                            return radius;\n                        }\n                        return minorRadius;\n                    };\n                    return radiusCallback;\n                }\n            },\n            createPlotBands: function () {\n                var this$1 = this;\n                var plotBands = this.options.plotBands || [];\n                var group = this._plotbandGroup = new Group({ zIndex: -1 });\n                for (var i = 0; i < plotBands.length; i++) {\n                    var band = plotBands[i];\n                    var slot = this$1.plotBandSlot(band);\n                    var singleSlot = this$1.getSlot(band.from);\n                    var head = band.from - Math.floor(band.from);\n                    slot.startAngle += head * singleSlot.angle;\n                    var tail = Math.ceil(band.to) - band.to;\n                    slot.angle -= (tail + head) * singleSlot.angle;\n                    var ring = ShapeBuilder.current.createRing(slot, {\n                        fill: {\n                            color: band.color,\n                            opacity: band.opacity\n                        },\n                        stroke: { opacity: band.opacity }\n                    });\n                    group.append(ring);\n                }\n                this.appendVisual(group);\n            },\n            plotBandSlot: function (band) {\n                return this.getSlot(band.from, band.to - 1);\n            },\n            getSlot: function (from, to) {\n                var options = this.options;\n                var justified = options.justified;\n                var box = this.box;\n                var divs = this.majorAngles();\n                var totalDivs = divs.length;\n                var slotAngle = 360 / totalDivs;\n                var fromValue = from;\n                if (options.reverse && !justified) {\n                    fromValue = (fromValue + 1) % totalDivs;\n                }\n                fromValue = limitValue(Math.floor(fromValue), 0, totalDivs - 1);\n                var slotStart = divs[fromValue];\n                if (justified) {\n                    slotStart = slotStart - slotAngle / 2;\n                    if (slotStart < 0) {\n                        slotStart += 360;\n                    }\n                }\n                var toValue = limitValue(Math.ceil(to || fromValue), fromValue, totalDivs - 1);\n                var slots = toValue - fromValue + 1;\n                var angle = slotAngle * slots;\n                return new Ring(box.center(), 0, box.height() / 2, slotStart, angle);\n            },\n            slot: function (from, to) {\n                var slot = this.getSlot(from, to);\n                var startAngle = slot.startAngle + 180;\n                var endAngle = startAngle + slot.angle;\n                return new geometry.Arc([\n                    slot.center.x,\n                    slot.center.y\n                ], {\n                    startAngle: startAngle,\n                    endAngle: endAngle,\n                    radiusX: slot.radius,\n                    radiusY: slot.radius\n                });\n            },\n            pointCategoryIndex: function (point) {\n                var this$1 = this;\n                var length = this.options.categories.length;\n                var index = null;\n                for (var i = 0; i < length; i++) {\n                    var slot = this$1.getSlot(i);\n                    if (slot.containsPoint(point)) {\n                        index = i;\n                        break;\n                    }\n                }\n                return index;\n            }\n        });\n        setDefaultOptions(RadarCategoryAxis, {\n            startAngle: 90,\n            labels: { margin: getSpacing(10) },\n            majorGridLines: { visible: true },\n            justified: true\n        });\n        deepExtend(RadarCategoryAxis.prototype, GridLinesMixin);\n        var PolarAxis = Axis.extend({\n            init: function (options, chartService) {\n                Axis.fn.init.call(this, options, chartService);\n                var instanceOptions = this.options;\n                instanceOptions.minorUnit = instanceOptions.minorUnit || instanceOptions.majorUnit / 2;\n            },\n            getDivisions: function (stepValue) {\n                return NumericAxis.prototype.getDivisions.call(this, stepValue) - 1;\n            },\n            reflow: function (box) {\n                this.box = box;\n                this.reflowLabels();\n            },\n            reflowLabels: function () {\n                var this$1 = this;\n                var ref = this;\n                var options = ref.options;\n                var labels = ref.labels;\n                var labelOptions = ref.options.labels;\n                var skip = labelOptions.skip || 0;\n                var step = labelOptions.step || 1;\n                var measureBox = new Box();\n                var divs = this.intervals(options.majorUnit, skip, step);\n                for (var i = 0; i < labels.length; i++) {\n                    labels[i].reflow(measureBox);\n                    var labelBox = labels[i].box;\n                    labels[i].reflow(this$1.getSlot(divs[i]).adjacentBox(0, labelBox.width(), labelBox.height()));\n                }\n            },\n            lineBox: function () {\n                return this.box;\n            },\n            intervals: function (size, skipOption, stepOption, skipAngles) {\n                if (skipAngles === void 0) {\n                    skipAngles = false;\n                }\n                var min = this.options.min;\n                var divisions = this.getDivisions(size);\n                var divs = [];\n                var skip = skipOption || 0;\n                var step = stepOption || 1;\n                for (var i = skip; i < divisions; i += step) {\n                    var current = (360 + min + i * size) % 360;\n                    if (!(skipAngles && inArray(current, skipAngles))) {\n                        divs.push(current);\n                    }\n                }\n                return divs;\n            },\n            majorIntervals: function () {\n                return this.intervals(this.options.majorUnit);\n            },\n            minorIntervals: function () {\n                return this.intervals(this.options.minorUnit);\n            },\n            intervalAngle: function (i) {\n                return (540 - i - this.options.startAngle) % 360;\n            },\n            createLine: function () {\n                return [];\n            },\n            majorGridLineAngles: function (altAxis) {\n                var majorGridLines = this.options.majorGridLines;\n                return this.gridLineAngles(altAxis, this.options.majorUnit, majorGridLines.skip, majorGridLines.step);\n            },\n            minorGridLineAngles: function (altAxis, skipMajor) {\n                var options = this.options;\n                var minorGridLines = options.minorGridLines;\n                var majorGridLines = options.majorGridLines;\n                var majorGridLineAngles = skipMajor ? this.intervals(options.majorUnit, majorGridLines.skip, majorGridLines.step) : null;\n                return this.gridLineAngles(altAxis, options.minorUnit, minorGridLines.skip, minorGridLines.step, majorGridLineAngles);\n            },\n            plotBandSlot: function (band) {\n                return this.getSlot(band.from, band.to);\n            },\n            getSlot: function (a, b) {\n                var ref = this;\n                var options = ref.options;\n                var box = ref.box;\n                var startAngle = options.startAngle;\n                var start = limitValue(a, options.min, options.max);\n                var end = limitValue(b || start, start, options.max);\n                if (options.reverse) {\n                    start *= -1;\n                    end *= -1;\n                }\n                start = (540 - start - startAngle) % 360;\n                end = (540 - end - startAngle) % 360;\n                if (end < start) {\n                    var tmp = start;\n                    start = end;\n                    end = tmp;\n                }\n                return new Ring(box.center(), 0, box.height() / 2, start, end - start);\n            },\n            slot: function (from, to) {\n                if (to === void 0) {\n                    to = from;\n                }\n                var options = this.options;\n                var start = 360 - options.startAngle;\n                var slot = this.getSlot(from, to);\n                var min = Math.min(from, to);\n                var max = Math.max(from, to);\n                var startAngle, endAngle;\n                if (options.reverse) {\n                    startAngle = min;\n                    endAngle = max;\n                } else {\n                    startAngle = 360 - max;\n                    endAngle = 360 - min;\n                }\n                startAngle = (startAngle + start) % 360;\n                endAngle = (endAngle + start) % 360;\n                return new geometry.Arc([\n                    slot.center.x,\n                    slot.center.y\n                ], {\n                    startAngle: startAngle,\n                    endAngle: endAngle,\n                    radiusX: slot.radius,\n                    radiusY: slot.radius\n                });\n            },\n            getValue: function (point) {\n                var options = this.options;\n                var center = this.box.center();\n                var dx = point.x - center.x;\n                var dy = point.y - center.y;\n                var theta = Math.round(deg(Math.atan2(dy, dx)));\n                var start = options.startAngle;\n                if (!options.reverse) {\n                    theta *= -1;\n                    start *= -1;\n                }\n                return (theta + start + 360) % 360;\n            },\n            valueRange: function () {\n                return {\n                    min: 0,\n                    max: Math.PI * 2\n                };\n            }\n        });\n        setDefaultOptions(PolarAxis, {\n            type: 'polar',\n            startAngle: 0,\n            reverse: false,\n            majorUnit: 60,\n            min: 0,\n            max: 360,\n            labels: { margin: getSpacing(10) },\n            majorGridLines: {\n                color: BLACK,\n                visible: true,\n                width: 1\n            },\n            minorGridLines: { color: '#aaa' }\n        });\n        deepExtend(PolarAxis.prototype, GridLinesMixin, {\n            createPlotBands: RadarCategoryAxis.prototype.createPlotBands,\n            majorAngles: RadarCategoryAxis.prototype.majorAngles,\n            range: NumericAxis.prototype.range,\n            labelsCount: NumericAxis.prototype.labelsCount,\n            createAxisLabel: NumericAxis.prototype.createAxisLabel\n        });\n        var RadarNumericAxisMixin = {\n            options: { majorGridLines: { visible: true } },\n            createPlotBands: function () {\n                var this$1 = this;\n                var ref = this.options;\n                var type = ref.majorGridLines.type;\n                var plotBands = ref.plotBands;\n                if (plotBands === void 0) {\n                    plotBands = [];\n                }\n                var altAxis = this.plotArea.polarAxis;\n                var majorAngles = altAxis.majorAngles();\n                var center = altAxis.box.center();\n                var group = this._plotbandGroup = new Group({ zIndex: -1 });\n                for (var i = 0; i < plotBands.length; i++) {\n                    var band = plotBands[i];\n                    var bandStyle = {\n                        fill: {\n                            color: band.color,\n                            opacity: band.opacity\n                        },\n                        stroke: { opacity: band.opacity }\n                    };\n                    var slot = this$1.getSlot(band.from, band.to, true);\n                    var ring = new Ring(center, center.y - slot.y2, center.y - slot.y1, 0, 360);\n                    var shape = void 0;\n                    if (type === ARC) {\n                        shape = ShapeBuilder.current.createRing(ring, bandStyle);\n                    } else {\n                        shape = Path.fromPoints(this$1.plotBandPoints(ring, majorAngles), bandStyle).close();\n                    }\n                    group.append(shape);\n                }\n                this.appendVisual(group);\n            },\n            plotBandPoints: function (ring, angles) {\n                var innerPoints = [];\n                var outerPoints = [];\n                var center = [\n                    ring.center.x,\n                    ring.center.y\n                ];\n                var innerCircle = new Circle(center, ring.innerRadius);\n                var outerCircle = new Circle(center, ring.radius);\n                for (var i = 0; i < angles.length; i++) {\n                    innerPoints.push(innerCircle.pointAt(angles[i] + 180));\n                    outerPoints.push(outerCircle.pointAt(angles[i] + 180));\n                }\n                innerPoints.reverse();\n                innerPoints.push(innerPoints[0]);\n                outerPoints.push(outerPoints[0]);\n                return outerPoints.concat(innerPoints);\n            },\n            createGridLines: function (altAxis) {\n                var options = this.options;\n                var majorTicks = this.radarMajorGridLinePositions();\n                var majorAngles = altAxis.majorAngles();\n                var center = altAxis.box.center();\n                var gridLines = [];\n                if (options.majorGridLines.visible) {\n                    gridLines = this.renderGridLines(center, majorTicks, majorAngles, options.majorGridLines);\n                }\n                if (options.minorGridLines.visible) {\n                    var minorTicks = this.radarMinorGridLinePositions();\n                    append(gridLines, this.renderGridLines(center, minorTicks, majorAngles, options.minorGridLines));\n                }\n                return gridLines;\n            },\n            renderGridLines: function (center, ticks, angles, options) {\n                var style = {\n                    stroke: {\n                        width: options.width,\n                        color: options.color,\n                        dashType: options.dashType\n                    }\n                };\n                var skip = options.skip;\n                if (skip === void 0) {\n                    skip = 0;\n                }\n                var step = options.step;\n                if (step === void 0) {\n                    step = 0;\n                }\n                var container = this.gridLinesVisual();\n                for (var tickIx = skip; tickIx < ticks.length; tickIx += step) {\n                    var tickRadius = center.y - ticks[tickIx];\n                    if (tickRadius > 0) {\n                        var circle = new Circle([\n                            center.x,\n                            center.y\n                        ], tickRadius);\n                        if (options.type === ARC) {\n                            container.append(new drawing.Circle(circle, style));\n                        } else {\n                            var line = new Path(style);\n                            for (var angleIx = 0; angleIx < angles.length; angleIx++) {\n                                line.lineTo(circle.pointAt(angles[angleIx] + 180));\n                            }\n                            line.close();\n                            container.append(line);\n                        }\n                    }\n                }\n                return container.children;\n            },\n            getValue: function (point) {\n                var lineBox = this.lineBox();\n                var altAxis = this.plotArea.polarAxis;\n                var majorAngles = altAxis.majorAngles();\n                var center = altAxis.box.center();\n                var radius = point.distanceTo(center);\n                var distance = radius;\n                if (this.options.majorGridLines.type !== ARC && majorAngles.length > 1) {\n                    var dx = point.x - center.x;\n                    var dy = point.y - center.y;\n                    var theta = (deg(Math.atan2(dy, dx)) + 540) % 360;\n                    majorAngles.sort(function (a, b) {\n                        return angularDistance(a, theta) - angularDistance(b, theta);\n                    });\n                    var midAngle = angularDistance(majorAngles[0], majorAngles[1]) / 2;\n                    var alpha = angularDistance(theta, majorAngles[0]);\n                    var gamma = 90 - midAngle;\n                    var beta = 180 - alpha - gamma;\n                    distance = radius * (Math.sin(rad(beta)) / Math.sin(rad(gamma)));\n                }\n                return this.axisType().prototype.getValue.call(this, new Point(lineBox.x1, lineBox.y2 - distance));\n            }\n        };\n        function angularDistance(a, b) {\n            return 180 - Math.abs(Math.abs(a - b) - 180);\n        }\n        var RadarNumericAxis = NumericAxis.extend({\n            radarMajorGridLinePositions: function () {\n                return this.getTickPositions(this.options.majorUnit);\n            },\n            radarMinorGridLinePositions: function () {\n                var options = this.options;\n                var minorSkipStep = 0;\n                if (options.majorGridLines.visible) {\n                    minorSkipStep = options.majorUnit;\n                }\n                return this.getTickPositions(options.minorUnit, minorSkipStep);\n            },\n            axisType: function () {\n                return NumericAxis;\n            }\n        });\n        deepExtend(RadarNumericAxis.prototype, RadarNumericAxisMixin);\n        var RadarLogarithmicAxis = LogarithmicAxis.extend({\n            radarMajorGridLinePositions: function () {\n                var positions = [];\n                this.traverseMajorTicksPositions(function (position) {\n                    positions.push(position);\n                }, this.options.majorGridLines);\n                return positions;\n            },\n            radarMinorGridLinePositions: function () {\n                var positions = [];\n                this.traverseMinorTicksPositions(function (position) {\n                    positions.push(position);\n                }, this.options.minorGridLines);\n                return positions;\n            },\n            axisType: function () {\n                return LogarithmicAxis;\n            }\n        });\n        deepExtend(RadarLogarithmicAxis.prototype, RadarNumericAxisMixin);\n        var WEIGHT = 0.333;\n        var EXTREMUM_ALLOWED_DEVIATION = 0.01;\n        var CurveProcessor = Class.extend({\n            init: function (closed) {\n                this.closed = closed;\n            },\n            process: function (dataPoints) {\n                var this$1 = this;\n                var points = dataPoints.slice(0);\n                var segments = [];\n                var closed = this.closed;\n                var length = points.length;\n                if (length > 2) {\n                    this.removeDuplicates(0, points);\n                    length = points.length;\n                }\n                if (length < 2 || length === 2 && points[0].equals(points[1])) {\n                    return segments;\n                }\n                var p0 = points[0];\n                var p1 = points[1];\n                var p2 = points[2];\n                segments.push(new Segment(p0));\n                while (p0.equals(points[length - 1])) {\n                    closed = true;\n                    points.pop();\n                    length--;\n                }\n                if (length === 2) {\n                    var tangent = this.tangent(p0, p1, X, Y);\n                    last(segments).controlOut(this.firstControlPoint(tangent, p0, p1, X, Y));\n                    segments.push(new Segment(p1, this.secondControlPoint(tangent, p0, p1, X, Y)));\n                    return segments;\n                }\n                var initialControlPoint, lastControlPoint;\n                if (closed) {\n                    p0 = points[length - 1];\n                    p1 = points[0];\n                    p2 = points[1];\n                    var controlPoints = this.controlPoints(p0, p1, p2);\n                    initialControlPoint = controlPoints[1];\n                    lastControlPoint = controlPoints[0];\n                } else {\n                    var tangent$1 = this.tangent(p0, p1, X, Y);\n                    initialControlPoint = this.firstControlPoint(tangent$1, p0, p1, X, Y);\n                }\n                var cp0 = initialControlPoint;\n                for (var idx = 0; idx <= length - 3; idx++) {\n                    this$1.removeDuplicates(idx, points);\n                    length = points.length;\n                    if (idx + 3 <= length) {\n                        p0 = points[idx];\n                        p1 = points[idx + 1];\n                        p2 = points[idx + 2];\n                        var controlPoints$1 = this$1.controlPoints(p0, p1, p2);\n                        last(segments).controlOut(cp0);\n                        cp0 = controlPoints$1[1];\n                        var cp1 = controlPoints$1[0];\n                        segments.push(new Segment(p1, cp1));\n                    }\n                }\n                if (closed) {\n                    p0 = points[length - 2];\n                    p1 = points[length - 1];\n                    p2 = points[0];\n                    var controlPoints$2 = this.controlPoints(p0, p1, p2);\n                    last(segments).controlOut(cp0);\n                    segments.push(new Segment(p1, controlPoints$2[0]));\n                    last(segments).controlOut(controlPoints$2[1]);\n                    segments.push(new Segment(p2, lastControlPoint));\n                } else {\n                    var tangent$2 = this.tangent(p1, p2, X, Y);\n                    last(segments).controlOut(cp0);\n                    segments.push(new Segment(p2, this.secondControlPoint(tangent$2, p1, p2, X, Y)));\n                }\n                return segments;\n            },\n            removeDuplicates: function (idx, points) {\n                while (points[idx + 1] && (points[idx].equals(points[idx + 1]) || points[idx + 1].equals(points[idx + 2]))) {\n                    points.splice(idx + 1, 1);\n                }\n            },\n            invertAxis: function (p0, p1, p2) {\n                var invertAxis = false;\n                if (p0.x === p1.x) {\n                    invertAxis = true;\n                } else if (p1.x === p2.x) {\n                    if (p1.y < p2.y && p0.y <= p1.y || p2.y < p1.y && p1.y <= p0.y) {\n                        invertAxis = true;\n                    }\n                } else {\n                    var fn = this.lineFunction(p0, p1);\n                    var y2 = this.calculateFunction(fn, p2.x);\n                    if (!(p0.y <= p1.y && p2.y <= y2) && !(p1.y <= p0.y && p2.y >= y2)) {\n                        invertAxis = true;\n                    }\n                }\n                return invertAxis;\n            },\n            isLine: function (p0, p1, p2) {\n                var fn = this.lineFunction(p0, p1);\n                var y2 = this.calculateFunction(fn, p2.x);\n                return p0.x === p1.x && p1.x === p2.x || round(y2, 1) === round(p2.y, 1);\n            },\n            lineFunction: function (p1, p2) {\n                var a = (p2.y - p1.y) / (p2.x - p1.x);\n                var b = p1.y - a * p1.x;\n                return [\n                    b,\n                    a\n                ];\n            },\n            controlPoints: function (p0, p1, p2) {\n                var xField = X;\n                var yField = Y;\n                var restrict = false;\n                var switchOrientation = false;\n                var tangent;\n                if (this.isLine(p0, p1, p2)) {\n                    tangent = this.tangent(p0, p1, X, Y);\n                } else {\n                    var monotonic = {\n                        x: this.isMonotonicByField(p0, p1, p2, X),\n                        y: this.isMonotonicByField(p0, p1, p2, Y)\n                    };\n                    if (monotonic.x && monotonic.y) {\n                        tangent = this.tangent(p0, p2, X, Y);\n                        restrict = true;\n                    } else {\n                        if (this.invertAxis(p0, p1, p2)) {\n                            xField = Y;\n                            yField = X;\n                        }\n                        if (monotonic[xField]) {\n                            tangent = 0;\n                        } else {\n                            var sign;\n                            if (p2[yField] < p0[yField] && p0[yField] <= p1[yField] || p0[yField] < p2[yField] && p1[yField] <= p0[yField]) {\n                                sign = numberSign((p2[yField] - p0[yField]) * (p1[xField] - p0[xField]));\n                            } else {\n                                sign = -numberSign((p2[xField] - p0[xField]) * (p1[yField] - p0[yField]));\n                            }\n                            tangent = EXTREMUM_ALLOWED_DEVIATION * sign;\n                            switchOrientation = true;\n                        }\n                    }\n                }\n                var secondControlPoint = this.secondControlPoint(tangent, p0, p1, xField, yField);\n                if (switchOrientation) {\n                    var oldXField = xField;\n                    xField = yField;\n                    yField = oldXField;\n                }\n                var firstControlPoint = this.firstControlPoint(tangent, p1, p2, xField, yField);\n                if (restrict) {\n                    this.restrictControlPoint(p0, p1, secondControlPoint, tangent);\n                    this.restrictControlPoint(p1, p2, firstControlPoint, tangent);\n                }\n                return [\n                    secondControlPoint,\n                    firstControlPoint\n                ];\n            },\n            restrictControlPoint: function (p1, p2, cp, tangent) {\n                if (p1.y < p2.y) {\n                    if (p2.y < cp.y) {\n                        cp.x = p1.x + (p2.y - p1.y) / tangent;\n                        cp.y = p2.y;\n                    } else if (cp.y < p1.y) {\n                        cp.x = p2.x - (p2.y - p1.y) / tangent;\n                        cp.y = p1.y;\n                    }\n                } else {\n                    if (cp.y < p2.y) {\n                        cp.x = p1.x - (p1.y - p2.y) / tangent;\n                        cp.y = p2.y;\n                    } else if (p1.y < cp.y) {\n                        cp.x = p2.x + (p1.y - p2.y) / tangent;\n                        cp.y = p1.y;\n                    }\n                }\n            },\n            tangent: function (p0, p1, xField, yField) {\n                var x = p1[xField] - p0[xField];\n                var y = p1[yField] - p0[yField];\n                var tangent;\n                if (x === 0) {\n                    tangent = 0;\n                } else {\n                    tangent = y / x;\n                }\n                return tangent;\n            },\n            isMonotonicByField: function (p0, p1, p2, field) {\n                return p2[field] > p1[field] && p1[field] > p0[field] || p2[field] < p1[field] && p1[field] < p0[field];\n            },\n            firstControlPoint: function (tangent, p0, p3, xField, yField) {\n                var t1 = p0[xField];\n                var t2 = p3[xField];\n                var distance = (t2 - t1) * WEIGHT;\n                return this.point(t1 + distance, p0[yField] + distance * tangent, xField, yField);\n            },\n            secondControlPoint: function (tangent, p0, p3, xField, yField) {\n                var t1 = p0[xField];\n                var t2 = p3[xField];\n                var distance = (t2 - t1) * WEIGHT;\n                return this.point(t2 - distance, p3[yField] - distance * tangent, xField, yField);\n            },\n            point: function (xValue, yValue, xField, yField) {\n                var controlPoint = new geometry.Point();\n                controlPoint[xField] = xValue;\n                controlPoint[yField] = yValue;\n                return controlPoint;\n            },\n            calculateFunction: function (fn, x) {\n                var length = fn.length;\n                var result = 0;\n                for (var i = 0; i < length; i++) {\n                    result += Math.pow(x, i) * fn[i];\n                }\n                return result;\n            }\n        });\n        function numberSign(value) {\n            return value <= 0 ? -1 : 1;\n        }\n        dataviz.Gradients = GRADIENTS;\n        kendo.deepExtend(kendo.dataviz, {\n            constants: constants,\n            services: services,\n            autoMajorUnit: autoMajorUnit,\n            Point: Point,\n            Box: Box,\n            Ring: Ring,\n            Sector: Sector,\n            ShapeBuilder: ShapeBuilder,\n            ShapeElement: ShapeElement,\n            ChartElement: ChartElement,\n            BoxElement: BoxElement,\n            RootElement: RootElement,\n            FloatElement: FloatElement,\n            Text: Text,\n            TextBox: TextBox,\n            Title: Title,\n            AxisLabel: AxisLabel,\n            Axis: Axis,\n            Note: Note,\n            CategoryAxis: CategoryAxis,\n            DateCategoryAxis: DateCategoryAxis,\n            DateValueAxis: DateValueAxis,\n            NumericAxis: NumericAxis,\n            LogarithmicAxis: LogarithmicAxis,\n            PolarAxis: PolarAxis,\n            RadarCategoryAxis: RadarCategoryAxis,\n            RadarNumericAxis: RadarNumericAxis,\n            RadarLogarithmicAxis: RadarLogarithmicAxis,\n            CurveProcessor: CurveProcessor,\n            rectToBox: rectToBox,\n            addClass: addClass,\n            removeClass: removeClass,\n            alignPathToPixel: alignPathToPixel,\n            clockwise: clockwise,\n            convertableToNumber: convertableToNumber,\n            deepExtend: deepExtend,\n            elementStyles: elementStyles,\n            getSpacing: getSpacing,\n            getTemplate: getTemplate,\n            getter: __common_getter_js,\n            grep: grep,\n            hasClasses: hasClasses,\n            HashMap: HashMap,\n            inArray: inArray,\n            interpolateValue: interpolateValue,\n            InstanceObserver: InstanceObserver,\n            isArray: isArray,\n            isFunction: isFunction,\n            isNumber: isNumber,\n            isObject: isObject,\n            isString: isString,\n            map: map,\n            mousewheelDelta: mousewheelDelta,\n            FontLoader: FontLoader,\n            setDefaultOptions: setDefaultOptions,\n            sparseArrayLimits: sparseArrayLimits,\n            styleValue: styleValue,\n            append: append,\n            bindEvents: bindEvents,\n            Class: Class,\n            defined: defined,\n            deg: deg,\n            elementOffset: elementOffset,\n            elementSize: elementSize,\n            eventElement: eventElement,\n            eventCoordinates: eventCoordinates,\n            last: last,\n            limitValue: limitValue,\n            logToConsole: kendo.logToConsole,\n            objectKey: objectKey,\n            rad: rad,\n            round: round,\n            unbindEvents: unbindEvents,\n            valueOrDefault: valueOrDefault,\n            absoluteDateDiff: absoluteDateDiff,\n            addDuration: addDuration,\n            addTicks: addTicks,\n            ceilDate: ceilDate,\n            dateComparer: dateComparer,\n            dateDiff: dateDiff,\n            dateEquals: dateEquals,\n            dateIndex: dateIndex,\n            duration: duration,\n            floorDate: floorDate,\n            lteDateIndex: lteDateIndex,\n            startOfWeek: startOfWeek,\n            toDate: toDate,\n            parseDate: parseDate,\n            parseDates: parseDates,\n            toTime: toTime\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/core/core', ['dataviz/core/kendo-core'], f);\n}(function () {\n    (function ($) {\n        var dataviz = kendo.dataviz;\n        var services = dataviz.services;\n        var draw = kendo.drawing;\n        dataviz.SASS_THEMES = [\n            'sass',\n            'default-v2',\n            'bootstrap-v4',\n            'material-v2'\n        ];\n        dataviz.ExportMixin = {\n            extend: function (proto, skipLegacy) {\n                if (!proto.exportVisual) {\n                    throw new Error('Mixin target has no exportVisual method defined.');\n                }\n                proto.exportSVG = this.exportSVG;\n                proto.exportImage = this.exportImage;\n                proto.exportPDF = this.exportPDF;\n                if (!skipLegacy) {\n                    proto.svg = this.svg;\n                    proto.imageDataURL = this.imageDataURL;\n                }\n            },\n            exportSVG: function (options) {\n                return draw.exportSVG(this.exportVisual(), options);\n            },\n            exportImage: function (options) {\n                return draw.exportImage(this.exportVisual(options), options);\n            },\n            exportPDF: function (options) {\n                return draw.exportPDF(this.exportVisual(), options);\n            },\n            svg: function () {\n                if (draw.svg.Surface) {\n                    return draw.svg.exportGroup(this.exportVisual());\n                } else {\n                    throw new Error('SVG Export failed. Unable to export instantiate kendo.drawing.svg.Surface');\n                }\n            },\n            imageDataURL: function () {\n                if (!kendo.support.canvas) {\n                    return null;\n                }\n                if (draw.canvas.Surface) {\n                    var container = $('<div />').css({\n                        display: 'none',\n                        width: this.element.width(),\n                        height: this.element.height()\n                    }).appendTo(document.body);\n                    var surface = new draw.canvas.Surface(container[0]);\n                    surface.draw(this.exportVisual());\n                    var image = surface._rootElement.toDataURL();\n                    surface.destroy();\n                    container.remove();\n                    return image;\n                } else {\n                    throw new Error('Image Export failed. Unable to export instantiate kendo.drawing.canvas.Surface');\n                }\n            }\n        };\n        services.IntlService.register({\n            format: function (format) {\n                return kendo.format.apply(null, [format].concat(Array.prototype.slice.call(arguments, 1)));\n            },\n            toString: kendo.toString,\n            parseDate: kendo.parseDate\n        });\n        services.TemplateService.register({ compile: kendo.template });\n        dataviz.Point2D = dataviz.Point;\n        dataviz.Box2D = dataviz.Box;\n        dataviz.mwDelta = function (e) {\n            return dataviz.mousewheelDelta(e.originalEvent);\n        };\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dataviz.core', [\n        'dataviz/core/kendo-core',\n        'dataviz/core/core'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dataviz.core',\n        name: 'Core',\n        description: 'The DataViz core functions',\n        category: 'dataviz',\n        depends: [\n            'core',\n            'drawing'\n        ],\n        hidden: true\n    };\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}