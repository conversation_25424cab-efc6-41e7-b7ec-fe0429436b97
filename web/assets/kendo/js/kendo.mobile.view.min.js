/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.mobile.view.min",["kendo.core.min","kendo.fx.min","kendo.mobile.scroller.min","kendo.view.min"],e)}(function(){return function(e,t){function i(e){var t,i,o=e.find(x("popover")),n=s.roles;for(t=0,i=o.length;t<i;t++)r.initWidget(o[t],{},n)}function o(e){r.triggeredByInput(e)||e.preventDefault()}function n(t){t.each(function(){r.initWidget(e(this),{},s.roles)})}var r=window.kendo,a=r.mobile,s=a.ui,l=r.attr,h=s.Widget,d=r.<PERSON>lone,c="init",u='<div style="height: 100%; width: 100%; position: absolute; top: 0; left: 0; z-index: 20000; display: none" />',p="beforeShow",f="show",g="afterShow",m="beforeHide",v="transitionEnd",w="transitionStart",y="hide",_="destroy",b=r.attrValue,x=r.roleSelector,S=r.directiveSelector,k=r.compileMobileDirective,V=h.extend({init:function(t,i){h.fn.init.call(this,t,i),this.params={},e.extend(this,i),this.transition=this.transition||this.defaultTransition,this._id(),this.options.$angular?this._overlay():(this._layout(),this._overlay(),this._scroller(),this._model())},events:[c,p,f,g,m,y,_,w,v],options:{name:"View",title:"",layout:null,getLayout:e.noop,reload:!1,transition:"",defaultTransition:"",useNativeScrolling:!1,stretch:!1,zoom:!1,model:null,modelScope:window,scroller:{},initWidgets:!0},enable:function(e){t===e&&(e=!0),e?this.overlay.hide():this.overlay.show()},destroy:function(){this.layout&&this.layout.detach(this),this.trigger(_),h.fn.destroy.call(this),this.scroller&&this.scroller.destroy(),this.options.$angular&&this.element.scope().$destroy(),r.destroy(this.element)},purge:function(){this.destroy(),this.element.remove()},triggerBeforeShow:function(){return!this.trigger(p,{view:this})},triggerBeforeHide:function(){return!this.trigger(m,{view:this})},showStart:function(){var e=this.element;e.css("display",""),this.inited?this._invokeNgController():(this.inited=!0,this.trigger(c,{view:this})),this.layout&&this.layout.attach(this),this._padIfNativeScrolling(),this.trigger(f,{view:this}),r.resize(e)},showEnd:function(){this.trigger(g,{view:this}),this._padIfNativeScrolling()},hideEnd:function(){var e=this;e.element.hide(),e.trigger(y,{view:e}),e.layout&&e.layout.trigger(y,{view:e,layout:e.layout})},beforeTransition:function(e){this.trigger(w,{type:e})},afterTransition:function(e){this.trigger(v,{type:e})},_padIfNativeScrolling:function(){if(a.appLevelNativeScrolling()){var e=r.support.mobileOS&&r.support.mobileOS.android,t=a.application.skin()||"",i=a.application.os.android||t.indexOf("android")>-1,o="flat"===t||t.indexOf("material")>-1,n=!e&&!i||o?"header":"footer",s=!e&&!i||o?"footer":"header";this.content.css({paddingTop:this[n].height(),paddingBottom:this[s].height()})}},contentElement:function(){var e=this;return e.options.stretch?e.content:e.scrollerContent},clone:function(){return new d(this)},_scroller:function(){var t=this;a.appLevelNativeScrolling()||(t.options.stretch?t.content.addClass("km-stretched-view"):(t.content.kendoMobileScroller(e.extend(t.options.scroller,{zoom:t.options.zoom,useNative:t.options.useNativeScrolling})),t.scroller=t.content.data("kendoMobileScroller"),t.scrollerContent=t.scroller.scrollElement),r.support.kineticScrollNeeded&&(e(t.element).on("touchmove",".km-header",o),t.options.useNativeScrolling||t.options.stretch||e(t.element).on("touchmove",".km-content",o)))},_model:function(){var e=this,t=e.element,o=e.options.model;"string"==typeof o&&(o=r.getter(o)(e.options.modelScope)),e.model=o,i(t),e.element.css("display",""),e.options.initWidgets&&(o?r.bind(t,o,s,r.ui,r.dataviz.ui):a.init(t.children())),e.element.css("display","none")},_id:function(){var e=this.element,t=e.attr("id")||"";this.id=b(e,"url")||"#"+t,"#"==this.id&&(this.id=r.guid(),e.attr("id",this.id))},_layout:function(){var e=x("content"),t=this.element;t.addClass("km-view"),this.header=t.children(x("header")).addClass("km-header"),this.footer=t.children(x("footer")).addClass("km-footer"),t.children(e)[0]||t.wrapInner("<div "+l("role")+'="content"></div>'),this.content=t.children(x("content")).addClass("km-content"),this.element.prepend(this.header).append(this.footer),this.layout=this.options.getLayout(this.layout),this.layout&&this.layout.setup(this)},_overlay:function(){this.overlay=e(u).appendTo(this.element)},_invokeNgController:function(){var t,i,o;this.options.$angular&&(t=this.element.controller(),i=this.options.$angular[0],t&&(o=e.proxy(this,"_callController",t,i),/^\$(digest|apply)$/.test(i.$$phase)?o():i.$apply(o)))},_callController:function(e,t){this.element.injector().invoke(e.constructor,e,{$scope:t})}}),C=h.extend({init:function(e,t){h.fn.init.call(this,e,t),e=this.element,this.header=e.children(this._locate("header")).addClass("km-header"),this.footer=e.children(this._locate("footer")).addClass("km-footer"),this.elements=this.header.add(this.footer),i(e),this.options.$angular||r.mobile.init(this.element.children()),this.element.detach(),this.trigger(c,{layout:this})},_locate:function(e){return this.options.$angular?S(e):x(e)},options:{name:"Layout",id:null,platform:null},events:[c,f,y],setup:function(e){e.header[0]||(e.header=this.header),e.footer[0]||(e.footer=this.footer)},detach:function(e){var t=this;e.header===t.header&&t.header[0]&&e.element.prepend(t.header.detach()[0].cloneNode(!0)),e.footer===t.footer&&t.footer.length&&e.element.append(t.footer.detach()[0].cloneNode(!0))},attach:function(e){var t=this,i=t.currentView;i&&t.detach(i),e.header===t.header&&(t.header.detach(),e.element.children(x("header")).remove(),e.element.prepend(t.header)),e.footer===t.footer&&(t.footer.detach(),e.element.children(x("footer")).remove(),e.element.append(t.footer)),t.trigger(f,{layout:t,view:e}),t.currentView=e}}),$=r.Observable,L=/<body[^>]*>(([\u000a\u000d\u2028\u2029]|.)*)<\/body>/i,N="loadStart",T="loadComplete",E="showStart",I="sameViewRequested",O="viewShow",R="viewTypeDetermined",W="after",z=$.extend({init:function(t){var i,o,a,s,l=this;if($.fn.init.call(l),e.extend(l,t),l.sandbox=e("<div />"),a=l.container,i=l._hideViews(a),l.rootView=i.first(),!l.rootView[0]&&t.rootNeeded)throw o=a[0]==r.mobile.application.element[0]?'Your kendo mobile application element does not contain any direct child elements with data-role="view" attribute set. Make sure that you instantiate the mobile application using the correct container.':'Your pane element does not contain any direct child elements with data-role="view" attribute set.',Error(o);l.layouts={},l.viewContainer=new r.ViewContainer(l.container),l.viewContainer.bind("accepted",function(e){e.view.params=l.params}),l.viewContainer.bind("complete",function(e){l.trigger(O,{view:e.view})}),l.viewContainer.bind(W,function(){l.trigger(W)}),this.getLayoutProxy=e.proxy(this,"_getLayout"),l._setupLayouts(a),s=a.children(l._locate("modalview drawer")),l.$angular?(l.$angular[0].viewOptions={defaultTransition:l.transition,loader:l.loader,container:l.container,getLayout:l.getLayoutProxy},s.each(function(i,o){k(e(o),t.$angular[0])})):n(s),this.bind(this.events,t)},events:[E,W,O,N,T,I,R],destroy:function(){r.destroy(this.container);for(var e in this.layouts)this.layouts[e].destroy()},view:function(){return this.viewContainer.view},showView:function(e,t,i){if(e=e.replace(RegExp("^"+this.remoteViewURLPrefix),""),""===e&&this.remoteViewURLPrefix&&(e="/"),e.replace(/^#/,"")===this.url)return this.trigger(I),!1;this.trigger(E);var o=this,n=function(i){return o.viewContainer.show(i,t,e)},a=o._findViewElement(e),s=r.widgetInstance(a);return o.url=e.replace(/^#/,""),o.params=i,s&&s.reload&&(s.purge(),a=[]),this.trigger(R,{remote:0===a.length,url:e}),a[0]?(s||(s=o._createView(a)),n(s)):(this.serverNavigation?location.href=e:o._loadView(e,n),!0)},append:function(e,t){var i,o,r,a=this.sandbox,s=(t||"").split("?")[0],h=this.container;return L.test(e)&&(e=RegExp.$1),a[0].innerHTML=e,h.append(a.children("script, style")),i=this._hideViews(a),r=i.first(),r.length||(i=r=a.wrapInner("<div data-role=view />").children()),s&&r.hide().attr(l("url"),s),this._setupLayouts(a),o=a.children(this._locate("modalview drawer")),h.append(a.children(this._locate("layout modalview drawer")).add(i)),n(o),this._createView(r)},_locate:function(e){return this.$angular?S(e):x(e)},_findViewElement:function(e){var t,i=e.split("?")[0];return i?(t=this.container.children("["+l("url")+"='"+i+"']"),t[0]||i.indexOf("/")!==-1||(t=this.container.children("#"===i.charAt(0)?i:"#"+i)),t):this.rootView},_createView:function(e){return this.$angular?k(e,this.$angular[0]):r.initWidget(e,{defaultTransition:this.transition,loader:this.loader,container:this.container,getLayout:this.getLayoutProxy,modelScope:this.modelScope,reload:b(e,"reload")},s.roles)},_getLayout:function(e){return""===e?null:e?this.layouts[e]:this.layouts[this.layout]},_loadView:function(t,i){this._xhr&&this._xhr.abort(),this.trigger(N),this._xhr=e.get(r.absoluteURL(t,this.remoteViewURLPrefix),"html").always(e.proxy(this,"_xhrComplete",i,t))},_xhrComplete:function(e,t,i){var o=!0;if("object"==typeof i&&0===i.status){if(!(i.responseText&&i.responseText.length>0))return;o=!0,i=i.responseText}this.trigger(T),o&&e(this.append(i,t))},_hideViews:function(e){return e.children(this._locate("view splitview")).hide()},_setupLayouts:function(t){var i,o=this;t.children(o._locate("layout")).each(function(){i=o.$angular?k(e(this),o.$angular[0]):r.initWidget(e(this),{},s.roles);var t=i.options.platform;t&&t!==a.application.os.name?i.destroy():o.layouts[i.options.id]=i})}});r.mobile.ViewEngine=z,s.plugin(V),s.plugin(C)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.mobile.view.min.js.map
