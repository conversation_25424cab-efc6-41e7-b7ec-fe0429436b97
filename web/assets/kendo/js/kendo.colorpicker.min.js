/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.colorpicker.min",["kendo.core.min","kendo.color.min","kendo.popup.min","kendo.slider.min","kendo.userevents.min","kendo.button.min"],e)}(function(){return function(e,t,a){function o(e,t,a){a=d(a),a&&!a.equals(e.color())&&("change"==t&&(e._value=a),a=1!=a.a?a.toCssRgba():a.toCss(),e.trigger(t,{value:a}))}function s(e,t,a){var o,s;return e=Array.prototype.slice.call(e),o=e.length,s=e.indexOf(t),s<0?a<0?e[o-1]:e[0]:(s+=a,s<0?s+=o:s%=o,e[s])}function l(e){e.preventDefault()}function n(e,t){return function(){return e.apply(t,arguments)}}var i=window.kendo,r=i.ui,c=r.Widget,d=i.parseColor,p=i.Color,u=i.keys,C="background-color",h="k-state-selected",F="000000,7f7f7f,880015,ed1c24,ff7f27,fff200,22b14c,00a2e8,3f48cc,a349a4,ffffff,c3c3c3,b97a57,ffaec9,ffc90e,efe4b0,b5e61d,99d9ea,7092be,c8bfe7",f="FFFFFF,FFCCFF,FF99FF,FF66FF,FF33FF,FF00FF,CCFFFF,CCCCFF,CC99FF,CC66FF,CC33FF,CC00FF,99FFFF,99CCFF,9999FF,9966FF,9933FF,9900FF,FFFFCC,FFCCCC,FF99CC,FF66CC,FF33CC,FF00CC,CCFFCC,CCCCCC,CC99CC,CC66CC,CC33CC,CC00CC,99FFCC,99CCCC,9999CC,9966CC,9933CC,9900CC,FFFF99,FFCC99,FF9999,FF6699,FF3399,FF0099,CCFF99,CCCC99,CC9999,CC6699,CC3399,CC0099,99FF99,99CC99,999999,996699,993399,990099,FFFF66,FFCC66,FF9966,FF6666,FF3366,FF0066,CCFF66,CCCC66,CC9966,CC6666,CC3366,CC0066,99FF66,99CC66,999966,996666,993366,990066,FFFF33,FFCC33,FF9933,FF6633,FF3333,FF0033,CCFF33,CCCC33,CC9933,CC6633,CC3333,CC0033,99FF33,99CC33,999933,996633,993333,990033,FFFF00,FFCC00,FF9900,FF6600,FF3300,FF0000,CCFF00,CCCC00,CC9900,CC6600,CC3300,CC0000,99FF00,99CC00,999900,996600,993300,990000,66FFFF,66CCFF,6699FF,6666FF,6633FF,6600FF,33FFFF,33CCFF,3399FF,3366FF,3333FF,3300FF,00FFFF,00CCFF,0099FF,0066FF,0033FF,0000FF,66FFCC,66CCCC,6699CC,6666CC,6633CC,6600CC,33FFCC,33CCCC,3399CC,3366CC,3333CC,3300CC,00FFCC,00CCCC,0099CC,0066CC,0033CC,0000CC,66FF99,66CC99,669999,666699,663399,660099,33FF99,33CC99,339999,336699,333399,330099,00FF99,00CC99,009999,006699,003399,000099,66FF66,66CC66,669966,666666,663366,660066,33FF66,33CC66,339966,336666,333366,330066,00FF66,00CC66,009966,006666,003366,000066,66FF33,66CC33,669933,666633,663333,660033,33FF33,33CC33,339933,336633,333333,330033,00FF33,00CC33,009933,006633,003333,000033,66FF00,66CC00,669900,666600,663300,660000,33FF00,33CC00,339900,336600,333300,330000,00FF00,00CC00,009900,006600,003300,000000",_="#ffffff",v={apply:"Apply",cancel:"Cancel",noColor:"no color",clearColor:"Clear color",previewInput:"Color Hexadecimal Code"},k=".kendoColorTools",g="click"+k,m="keydown"+k,b=i.support.browser,y=b.msie&&b.version<9,w=c.extend({init:function(e,t){var a,o=this;c.fn.init.call(o,e,t),e=o.element,t=o.options,o._value=t.value=d(t.value),o._tabIndex=e.attr("tabIndex")||0,a=o._ariaId=t.ariaId,a&&e.attr("aria-labelledby",a),t._standalone&&(o._triggerSelect=o._triggerChange)},options:{name:"ColorSelector",value:null,_standalone:!0},events:["change","select","cancel"],color:function(e){return e!==a&&(this._value=d(e),this._updateUI(this._value)),this._value},value:function(e){return e=this.color(e),e&&(e=this.options.opacity?e.toCssRgba():e.toCss()),e||null},enable:function(t){0===arguments.length&&(t=!0),e(".k-disabled-overlay",this.wrapper).remove(),t||this.wrapper.append("<div class='k-disabled-overlay'></div>"),this._onEnable(t)},_select:function(e,t){var a=this._value;e=this.color(e),t||(this.element.trigger("change"),e.equals(a)?this._standalone||this.trigger("cancel"):this.trigger("change",{value:this.value()}))},_triggerSelect:function(e){o(this,"select",e)},_triggerChange:function(e){o(this,"change",e)},destroy:function(){this.element&&this.element.off(k),this.wrapper&&this.wrapper.off(k).find("*").off(k),this.wrapper=null,c.fn.destroy.call(this)},_updateUI:e.noop,_selectOnHide:function(){return null},_cancel:function(){this.trigger("cancel")}}),I=w.extend({init:function(t,a){var o,s,l,r,c=this;if(w.fn.init.call(c,t,a),t=c.wrapper=c.element,a=c.options,o=a.palette,"websafe"==o?(o=f,a.columns=18):"basic"==o&&(o=F),"string"==typeof o&&(o=o.split(",")),e.isArray(o)&&(o=e.map(o,function(e){return d(e)})),c._selectedID=(a.ariaId||i.guid())+"_selected",t.addClass("k-widget k-colorpalette").attr("role","grid").attr("aria-readonly","true").append(e(c._template({colors:o,columns:a.columns,tileSize:a.tileSize,value:c._value,id:a.ariaId}))).on(g,".k-item",function(t){c._select(e(t.currentTarget).css(C))}).attr("tabIndex",c._tabIndex).on(m,n(c._keydown,c)),s=a.tileSize){if(/number|string/.test(typeof s))l=r=parseFloat(s);else{if("object"!=typeof s)throw Error("Unsupported value for the 'tileSize' argument");l=parseFloat(s.width),r=parseFloat(s.height)}t.find(".k-item").css({width:l,height:r})}},focus:function(){this.wrapper&&!this.wrapper.is("[unselectable='on']")&&this.wrapper.focus()},options:{name:"ColorPalette",columns:10,tileSize:null,palette:"basic"},_onEnable:function(e){e?this.wrapper.attr("tabIndex",this._tabIndex):this.wrapper.removeAttr("tabIndex")},_keydown:function(t){var a,o,n=this.wrapper,i=n.find(".k-item"),r=i.filter("."+h).get(0),c=t.keyCode;if(c==u.LEFT?a=s(i,r,-1):c==u.RIGHT?a=s(i,r,1):c==u.DOWN?a=s(i,r,this.options.columns):c==u.UP?a=s(i,r,-this.options.columns):c==u.ENTER?(l(t),r&&this._select(e(r).css(C))):c==u.ESC&&this._cancel(),a){l(t),this._current(a);try{o=d(a.css(C)),this._triggerSelect(o)}catch(p){}}},_current:function(t){this.wrapper.find("."+h).removeClass(h).attr("aria-selected",!1).removeAttr("id"),e(t).addClass(h).attr("aria-selected",!0).attr("id",this._selectedID),this.element.removeAttr("aria-activedescendant").attr("aria-activedescendant",this._selectedID)},_updateUI:function(t){var a=null;this.wrapper.find(".k-item").each(function(){var o=d(e(this).css(C));if(o&&o.equals(t))return a=this,!1}),this._current(a)},_template:i.template('<table class="k-palette k-reset" role="presentation"><tr role="row"># for (var i = 0; i < colors.length; ++i) { ## var selected = colors[i].equals(value); ## if (i && i % columns == 0) { # </tr><tr role="row"> # } #<td role="gridcell" unselectable="on" style="background-color:#= colors[i].toCss() #"#= selected ? " aria-selected=true" : "" # #=(id && i === 0) ? "id=\\""+id+"\\" " : "" # class="k-item#= selected ? " '+h+'" : "" #" aria-label="#= colors[i].toCss() #"></td># } #</tr></table>')}),S=w.extend({init:function(t,a){var o=this;w.fn.init.call(o,t,a),a=o.options,a.messages=a.options?e.extend(o.options.messages,a.options.messages):o.options.messages,t=o.element,o.wrapper=t.addClass("k-widget k-flatcolorpicker").append(o._template(a)),o._hueElements=e(".k-hsv-rectangle, .k-transparency-slider .k-slider-track",t),o._selectedColor=e(".k-selected-color-display",t),o._colorAsText=e("input.k-color-value",t),o._sliders(),o._hsvArea(),o._updateUI(o._value||d("#f00")),t.find("input.k-color-value").on(m,function(t){var a,s,l=this;if(t.keyCode==u.ENTER)try{a=d(l.value),s=o.color(),o._select(a,a.equals(s))}catch(n){e(l).addClass("k-state-error")}else o.options.autoupdate&&setTimeout(function(){var e=d(l.value,!0);e&&o._updateUI(e,!0)},10)}).end().on(g,".k-controls button.apply",function(){o.options._clearedColor?o.trigger("change"):o._select(o._getHSV())}).on(g,".k-controls button.cancel",function(){o._updateUI(o.color()),o._cancel()}),y&&o._applyIEFilter()},destroy:function(){this._hueSlider.destroy(),this._opacitySlider&&this._opacitySlider.destroy(),this._hueSlider=this._opacitySlider=this._hsvRect=this._hsvHandle=this._hueElements=this._selectedColor=this._colorAsText=null,w.fn.destroy.call(this)},options:{name:"FlatColorPicker",opacity:!1,buttons:!1,input:!0,preview:!0,clearButton:!1,autoupdate:!0,messages:v},_applyIEFilter:function(){var e=this.element.find(".k-hue-slider .k-slider-track")[0],t=e.currentStyle.backgroundImage;t=t.replace(/^url\([\'\"]?|[\'\"]?\)$/g,""),e.style.filter="progid:DXImageTransform.Microsoft.AlphaImageLoader(src='"+t+"', sizingMethod='scale')"},_sliders:function(){function e(e){a._updateUI(a._getHSV(e.value,null,null,null))}function t(e){a._updateUI(a._getHSV(null,null,null,e.value/100))}var a=this,o=a.element,s=o.find(".k-hue-slider"),l=o.find(".k-transparency-slider");s.attr("aria-label","hue saturation"),a._hueSlider=s.kendoSlider({min:0,max:360,tickPlacement:"none",showButtons:!1,slide:e,change:e}).data("kendoSlider"),l.attr("aria-label","opacity"),a._opacitySlider=l.kendoSlider({min:0,max:100,tickPlacement:"none",showButtons:!1,slide:t,change:t}).data("kendoSlider")},_hsvArea:function(){function e(e,a){var o=this.offset,s=e-o.left,l=a-o.top,n=this.width,i=this.height;s=s<0?0:s>n?n:s,l=l<0?0:l>i?i:l,t._svChange(s/n,1-l/i)}var t=this,a=t.element,o=a.find(".k-hsv-rectangle"),s=o.find(".k-draghandle").attr("tabIndex",0).on(m,n(t._keydown,t));t._hsvEvents=new i.UserEvents(o,{global:!0,press:function(t){this.offset=i.getOffset(o),this.width=o.width(),this.height=o.height(),s.focus(),e.call(this,t.x.location,t.y.location)},start:function(){o.addClass("k-dragging"),s.focus()},move:function(t){t.preventDefault(),e.call(this,t.x.location,t.y.location)},end:function(){o.removeClass("k-dragging")}}),t._hsvRect=o,t._hsvHandle=s},_onEnable:function(e){this._hueSlider.enable(e),this._opacitySlider&&this._opacitySlider.enable(e),this.wrapper.find("input").attr("disabled",!e);var t=this._hsvRect.find(".k-draghandle");e?t.attr("tabIndex",this._tabIndex):t.removeAttr("tabIndex")},_keydown:function(e){function t(t,a){var s=o._getHSV();s[t]+=a*(e.shiftKey?.01:.05),s[t]<0&&(s[t]=0),s[t]>1&&(s[t]=1),o._updateUI(s),l(e)}function a(t){var a=o._getHSV();a.h+=t*(e.shiftKey?1:5),a.h<0&&(a.h=0),a.h>359&&(a.h=359),o._updateUI(a),l(e)}var o=this;switch(e.keyCode){case u.LEFT:e.ctrlKey?a(-1):t("s",-1);break;case u.RIGHT:e.ctrlKey?a(1):t("s",1);break;case u.UP:t(e.ctrlKey&&o._opacitySlider?"a":"v",1);break;case u.DOWN:t(e.ctrlKey&&o._opacitySlider?"a":"v",-1);break;case u.ENTER:o._select(o._getHSV());break;case u.F2:o.wrapper.find("input.k-color-value").focus().select();break;case u.ESC:o._cancel()}},focus:function(){this._hsvHandle.focus()},_getHSV:function(e,t,a,o){var s=this._hsvRect,l=s.width(),n=s.height(),i=this._hsvHandle.position();return null==e&&(e=this._hueSlider.value()),null==t&&(t=i.left/l),null==a&&(a=1-i.top/n),null==o&&(o=this._opacitySlider?this._opacitySlider.value()/100:1),p.fromHSV(e,t,a,o)},_svChange:function(e,t){var a=this._getHSV(null,e,t,null);this._updateUI(a)},_updateUI:function(e,t){var a=this,o=a._hsvRect;e&&(this._colorAsText.attr("title",a.options.messages.previewInput),this._colorAsText.removeClass("k-state-error"),a._selectedColor.css(C,e.toDisplay()),t||a._colorAsText.val(a._opacitySlider?e.toCssRgba():e.toCss()),a._triggerSelect(e),e=e.toHSV(),a._hsvHandle.css({left:e.s*o.width()+"px",top:(1-e.v)*o.height()+"px"}),a._hueElements.css(C,p.fromHSV(e.h,1,1,1).toCss()),a._hueSlider.value(e.h),a._opacitySlider&&a._opacitySlider.value(100*e.a))},_selectOnHide:function(){return this.options.buttons?null:this._getHSV()},_template:i.template('# if (preview) { #<div class="k-selected-color"><div class="k-selected-color-display"><div class="k-color-input"><input class="k-color-value" # if (clearButton && !_standalone) { #placeholder="#: messages.noColor #" # } ##= !data.input ? \'style="visibility: hidden;"\' : "" #># if (clearButton && !_standalone) { #<span class="k-clear-color k-button k-bare" title="#: messages.clearColor #"></span># } #</div></div></div># } ## if (clearButton && !_standalone && !preview) { #<div class="k-clear-color-container"><span class="k-clear-color k-button k-bare">#: messages.clearColor #</span></div># } #<div class="k-hsv-rectangle"><div class="k-hsv-gradient"></div><div class="k-draghandle"></div></div><input class="k-hue-slider" /># if (opacity) { #<input class="k-transparency-slider" /># } ## if (buttons) { #<div unselectable="on" class="k-controls"><button class="k-button k-primary apply">#: messages.apply #</button> <button class="k-button cancel">#: messages.cancel #</button></div># } #')}),x=c.extend({init:function(t,a){var o,s,l,n,i,r=this;c.fn.init.call(r,t,a),a=r.options,t=r.element,o=t.attr("value")||t.val(),o=o?d(o,!0):d(a.value,!0),r._value=a.value=o,s=r.wrapper=e(r._template(a)),t.hide().after(s),t.is("input")&&(t.appendTo(s),l=t.closest("label"),n=t.attr("id"),n&&(l=l.add('label[for="'+n+'"]')),l.click(function(e){r.open(),e.preventDefault()})),r._tabIndex=t.attr("tabIndex")||0,r.enable(!t.attr("disabled")),i=t.attr("accesskey"),i&&(t.attr("accesskey",null),s.attr("accesskey",i)),r.bind("activate",function(e){e.isDefaultPrevented()||r.toggle()}),r._updateUI(o)},destroy:function(){this.wrapper.off(k).find("*").off(k),this._popup&&(this._selector.destroy(),this._popup.destroy()),this._selector=this._popup=this.wrapper=null,c.fn.destroy.call(this)},enable:function(e){var t=this,a=t.wrapper,o=a.children(".k-picker-wrap"),s=o.find(".k-select");0===arguments.length&&(e=!0),t.element.attr("disabled",!e),a.attr("aria-disabled",!e),s.off(k).on("mousedown"+k,l),a.addClass("k-state-disabled").removeAttr("tabIndex").add("*",a).off(k),e?a.removeClass("k-state-disabled").attr("tabIndex",t._tabIndex).on("mouseenter"+k,function(){o.addClass("k-state-hover")}).on("mouseleave"+k,function(){o.removeClass("k-state-hover")}).on("focus"+k,function(){o.addClass("k-state-focused")}).on("blur"+k,function(){o.removeClass("k-state-focused")}).on(m,n(t._keydown,t)).on(g,".k-select",n(t.toggle,t)).on(g,t.options.toolIcon?".k-tool-icon":".k-selected-color",function(){t.trigger("activate")}):t.close()},_template:i.template('<span role="textbox" aria-haspopup="true" class="k-widget k-colorpicker"><span class="k-picker-wrap k-state-default"># if (toolIcon) { #<span class="k-icon k-tool-icon #= toolIcon #"><span class="k-selected-color"></span></span># } else { #<span class="k-selected-color"><span class="k-icon k-i-line" style="display: none;"></span></span># } #<span class="k-select" unselectable="on" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span></span>'),options:{name:"ColorPicker",palette:null,columns:10,toolIcon:null,value:null,messages:v,opacity:!1,buttons:!0,preview:!0,clearButton:!1,ARIATemplate:'Current selected color is #=data || ""#'},events:["activate","change","select","open","close"],open:function(){this.element.prop("disabled")||this._getPopup().open()},close:function(){var e=this._selector&&this._selector.options||{};e._closing=!0,this._getPopup().close(),delete e._closing},toggle:function(){this.element.prop("disabled")||this._getPopup().toggle()},_noColorIcon:function(){return this.wrapper.find(".k-picker-wrap > .k-selected-color > .k-icon.k-i-line")},color:w.fn.color,value:w.fn.value,_select:w.fn._select,_triggerSelect:w.fn._triggerSelect,_isInputTypeColor:function(){var e=this.element[0];return/^input$/i.test(e.tagName)&&/^color$/i.test(e.type)},_updateUI:function(e){var t="";e&&(t=this._isInputTypeColor()||1==e.a?e.toCss():e.toCssRgba(),this.element.val(t)),this._ariaTemplate||(this._ariaTemplate=i.template(this.options.ARIATemplate)),this.wrapper.attr("aria-label",this._ariaTemplate(t)),this._triggerSelect(e),this.wrapper.find(".k-selected-color").css(C,e?e.toDisplay():_),this._noColorIcon()[t?"hide":"show"]()},_keydown:function(e){var t=e.keyCode;this._getPopup().visible()?(t==u.ESC?this._selector._cancel():this._selector._keydown(e),l(e)):t!=u.ENTER&&t!=u.DOWN||(this.open(),l(e))},_getPopup:function(){var t,o,s,l,n=this,r=n._popup;return r||(t=n.options,o=t.palette?I:S,t._standalone=!1,delete t.select,delete t.change,delete t.cancel,s=i.guid(),l=n._selector=new o(e('<div id="'+s+'"/>').appendTo(document.body),t),n.wrapper.attr("aria-owns",s),n._popup=r=l.wrapper.kendoPopup({anchor:n.wrapper,adjustSize:{width:5,height:0}}).data("kendoPopup"),l.element.find(".k-clear-color").kendoButton({icon:"reset-color",click:function(e){l.options._clearedColor=!0,n.value(null),n.element.val(null),n._updateUI(null),l._colorAsText.val(""),l._hsvHandle.css({top:"0px",left:"0px"}),l._selectedColor.css(C,_),n.trigger("change",{value:n.value()}),e.preventDefault()}}),l.bind({select:function(e){n._updateUI(d(e.value)),delete l.options._clearedColor},change:function(){l.options._clearedColor||n._select(l.color()),n.close()},cancel:function(){l.options._clearedColor&&!n.value()&&l.value()&&n._select(l.color(),!0),n.close()}}),r.bind({close:function(e){var t,o,s,i;return n.trigger("close")?(e.preventDefault(),a):(n.wrapper.children(".k-picker-wrap").removeClass("k-state-focused"),t=l._selectOnHide(),o=l.value(),s=n.value(),i=l.options,t?i._clearedColor&&!s||n._select(t):(setTimeout(function(){n.wrapper&&!n.wrapper.is("[unselectable='on']")&&n.wrapper.focus()}),!i._closing&&i._clearedColor&&!s&&o?n._select(o,!0):n._updateUI(n.color())),a)},open:function(e){n.trigger("open")?e.preventDefault():n.wrapper.children(".k-picker-wrap").addClass("k-state-focused")},activate:function(){l._select(n.color(),!0),l.focus(),n.wrapper.children(".k-picker-wrap").addClass("k-state-focused")}})),r}});r.plugin(I),r.plugin(S),r.plugin(x)}(jQuery,parseInt),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,a){(a||t)()});
//# sourceMappingURL=kendo.colorpicker.min.js.map
