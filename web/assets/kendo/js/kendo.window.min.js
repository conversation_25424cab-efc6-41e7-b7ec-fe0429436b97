/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.window.min",["kendo.draganddrop.min","kendo.popup.min"],t)}(function(){return function(t,i){function e(t){return i!==t}function n(t,i){return parseInt(t.css(i),10)||0}function o(t,i,e){var n;return n=t&&isNaN(t)&&(""+t).indexOf("px")<0?t:Math.max(Math.min(parseInt(t,10),e===1/0?e:parseInt(e,10)),i===-(1/0)?i:parseInt(i,10))}function s(){return!this.type||this.type.toLowerCase().indexOf("script")>=0}function r(i){for(var e,n,o={top:i.offsetTop,left:i.offsetLeft},s=i.offsetParent;s;)o.top+=s.offsetTop,o.left+=s.offsetLeft,e=t(s).css("overflowX"),n=t(s).css("overflowY"),"auto"!==n&&"scroll"!==n||(o.top-=s.scrollTop),"auto"!==e&&"scroll"!==e||(o.left-=s.scrollLeft),s=s.offsetParent;return o}function a(t){var i=this;i.owner=t,i._preventDragging=!1,i._draggable=new h(t.wrapper,{filter:">"+O,group:t.wrapper.id+"-resizing",dragstart:w(i.dragstart,i),drag:w(i.drag,i),dragend:w(i.dragend,i)}),i._draggable.userEvents.bind("press",w(i.addOverlay,i)),i._draggable.userEvents.bind("release",w(i.removeOverlay,i))}function l(t,i){var e=this;e.owner=t,e._preventDragging=!1,e._draggable=new h(t.wrapper,{filter:i,group:t.wrapper.id+"-moving",dragstart:w(e.dragstart,e),drag:w(e.drag,e),dragend:w(e.dragend,e),dragcancel:w(e.dragcancel,e)}),e._draggable.userEvents.stopPropagation=!1}var d=window.kendo,p=d.ui.Widget,c=d.ui.Popup.TabKeyTrap,h=d.ui.Draggable,f=t.isPlainObject,m=d._activeElement,g=d._outerWidth,u=d._outerHeight,w=t.proxy,_=t.extend,v=t.each,x=d.template,b="body",z=".kendoWindow",k=".kendoWindowModal",T=".k-window",y=".k-window-title",L=y+"bar",M=".k-window-content",P=".k-dialog-content",O=".k-resize-handle",W=".k-overlay",S="k-content-frame",H="k-i-loading",D="k-state-hover",I="k-state-focused",C="k-window-maximized",E=":visible",F="hidden",R="cursor",j="open",A="activate",N="deactivate",K="close",B="refresh",q="minimize",U="maximize",G="resizeStart",J="resize",V="resizeEnd",Q="dragstart",X="dragend",Y="error",$="overflow",Z="original-overflow-rule",tt="zIndex",it=".k-window-actions .k-i-window-minimize,.k-window-actions .k-i-window-maximize",et=".k-i-pin",nt=".k-i-unpin",ot=et+","+nt,st=".k-window-titlebar .k-window-action",rt=".k-window-titlebar .k-i-refresh",at="WindowEventsHandled",lt=/^0[a-z]*$/i,dt=d.isLocalUrl,pt={small:"k-window-sm",medium:"k-window-md",large:"k-window-lg"},ct=p.extend({init:function(n,o){var r,a,l,h,m,g,u,_,v,x=this,b={},k=!1,O=o&&o.actions&&!o.actions.length;p.fn.init.call(x,n,o),o=x.options,h=o.position,n=x.element,m=o.content,_=t(window),O&&(o.actions=[]),x.appendTo=t(o.appendTo),x.containment=o.draggable.containment?t(o.draggable.containment).first():null,m&&!f(m)&&(m=o.content={url:m}),n.find("script").filter(s).remove(),n.parent().is(x.appendTo)||x.containment||h.top!==i&&h.left!==i||(n.is(E)?(b=n.offset(),k=!0):(a=n.css("visibility"),l=n.css("display"),n.css({visibility:F,display:""}),b=n.offset(),n.css({visibility:a,display:l})),h.top===i&&(h.top=b.top),h.left===i&&(h.left=b.left)),e(o.visible)&&null!==o.visible||(o.visible=n.is(E)),r=x.wrapper=n.closest(T),n.is(".k-content")&&r[0]||(n.addClass("k-window-content k-content"),x._createWindow(n,o),r=x.wrapper=n.closest(T),x.title(x.options.title),x._dimensions()),x.minTop=x.minLeft=-(1/0),x.maxTop=x.maxLeft=1/0,x._position(),m&&x.refresh(m),o.visible&&x.toFront(),g=r.children(M),x._tabindex(g),o.visible&&o.modal&&x._overlay(r.is(E)).css({opacity:.5}),r.on("mouseenter"+z,st,w(x._buttonEnter,x)).on("mouseleave"+z,st,w(x._buttonLeave,x)).on("click"+z,"> "+st,w(x._windowActionHandler,x)).on("keydown"+z,w(x._keydown,x)).on("focus"+z,w(x._focus,x)).on("blur"+z,w(x._blur,x)),g.on("keydown"+z,w(x._keydown,x)).on("focus"+z,w(x._focus,x)).on("blur"+z,w(x._blur,x)),u=g.find("."+S)[0],u&&!_.data(at)&&(_.on("blur"+z,function(){var i,e=t(document.activeElement).parent(M);e.length&&(i=d.widgetInstance(e),i._focus())}),_.on("focus"+z,function(){t(M).not(P).each(function(i,e){d.widgetInstance(t(e))._blur()})}),_.data(at,!0)),this._resizable(),this._draggable(),o.pinned&&this.wrapper.is(":visible")&&x.pin(),v=n.attr("id"),v&&(v+="_wnd_title",r.children(L).children(y).attr("id",v),g.attr({role:"dialog","aria-labelledby":v})),r.add(r.children(".k-resize-handle,"+L)).on("mousedown"+z,w(x.toFront,x)),x.touchScroller=d.touchScroller(n),x._resizeHandler=w(x._onDocumentResize,x),x._marker=d.guid().substring(0,8),t(window).on("resize"+z+x._marker,x._resizeHandler),o.visible&&(x.trigger(j),x.trigger(A)),d.notify(x),this.options.modal&&(this._tabKeyTrap=new c(r),this._tabKeyTrap.trap(),this._tabKeyTrap.shouldTrap=function(){return g.data("isFront")})},_buttonEnter:function(i){t(i.currentTarget).addClass(D)},_buttonLeave:function(i){t(i.currentTarget).removeClass(D)},_focus:function(){this.wrapper.addClass(I)},_blur:function(){this.wrapper.removeClass(I)},_dimensions:function(){var t,i,e=this.wrapper,s=this.options,r=s.width,a=s.height,l=s.maxHeight,d=s.size,p=["minWidth","minHeight","maxWidth","maxHeight"],c="content-box"==e.css("box-sizing"),h=c?n(e,"border-left-width")+n(e,"border-right-width"):0,f=c?n(e,"border-top-width")+n(e,"border-bottom-width"):0,m=c?n(e,"padding-top"):0;for(this.containment&&!this._isPinned&&(this._updateBoundaries(),s.maxHeight=Math.min(this.containment.height-(f+m),l),s.maxWidth=Math.min(this.containment.width-h,s.maxWidth)),t=0;t<p.length;t++)i=s[p[t]]||"",i!=1/0&&e.css(p[t],i);l!=1/0&&this.element.css("maxHeight",l),e.width(r?o(r,s.minWidth,s.maxWidth):""),e.height(a?o(a,s.minHeight,s.maxHeight):""),s.visible||e.hide(),d&&pt[d]&&e.addClass(pt[d])},_position:function(){var t=this.wrapper,i=this.options.position;this._updateBoundaries(),this.containment&&(i.top=Math.min(this.minTop+(i.top||0),this.maxTop),i.left=Math.min(this.minLeft+(i.left||0),this.maxLeft)),0===i.top&&(i.top=""+i.top),0===i.left&&(i.left=""+i.left),t.css({top:i.top||"",left:i.left||""})},_updateBoundaries:function(){var t=this.containment;return t?(t.width=t.innerWidth(),t.height=t.innerHeight(),parseInt(t.width,10)>t[0].clientWidth&&(t.width-=d.support.scrollbar()),parseInt(t.height,10)>t[0].clientHeight&&(t.height-=d.support.scrollbar()),t.position=r(t[0]),this._isPinned?(this.minTop=this.minLeft=-(1/0),this.maxTop=this.maxLeft=1/0):(this.minTop=t.scrollTop(),this.minLeft=t.scrollLeft(),this.maxLeft=this.minLeft+t.width-g(this.wrapper,!0),this.maxTop=this.minTop+t.height-u(this.wrapper,!0)),i):null},_animationOptions:function(t){var i=this.options.animation,e={open:{effects:{}},close:{hide:!0,effects:{}}};return i&&i[t]||e[t]},_resize:function(){d.resize(this.element.children())},_resizable:function(){var i=this.options.resizable,e=this.wrapper;this.resizing&&(e.off("dblclick"+z).children(O).remove(),this.resizing.destroy(),this.resizing=null),i&&(e.on("dblclick"+z,L,w(function(i){t(i.target).closest(".k-window-action").length||this.toggleMaximization()},this)),v("n e s w se sw ne nw".split(" "),function(t,i){e.append(ht.resizeHandle(i))}),this.resizing=new a(this)),e=null},_draggable:function(){var t=this.options.draggable;this.dragging&&(this.dragging.destroy(),this.dragging=null),t&&(this.dragging=new l(this,t.dragHandle||L))},_actions:function(){var i=this.options,e=i.actions,n=i.pinned,o=this.wrapper.children(L),s=o.find(".k-window-actions"),r=["maximize","minimize"];e=t.map(e,function(t){return t=n&&"pin"===t.toLowerCase()?"unpin":t,{name:r.indexOf(t.toLowerCase())>-1?"window-"+t:t}}),s.html(d.render(ht.action,e))},setOptions:function(t){var e,n,o=this,s=o.options.size,r=JSON.parse(JSON.stringify(t));_(t.position,o.options.position),_(t.position,r.position),p.fn.setOptions.call(o,t),e=o.options.scrollable!==!1,o.restore(),i!==t.title&&o.title(t.title),o.wrapper.removeClass(pt[s]),o._dimensions(),o._position(),o._resizable(),o._draggable(),o._actions(),i!==t.modal&&(n=o.options.visible!==!1,o._enableDocumentScrolling(),o._overlay(t.modal&&n)),o.element.css($,e?"":"hidden")},events:[j,A,N,K,q,U,B,G,J,V,Q,X,Y],options:{name:"Window",animation:{open:{effects:{zoom:{direction:"in"},fade:{direction:"in"}},duration:350},close:{effects:{zoom:{direction:"out",properties:{scale:.7}},fade:{direction:"out"}},duration:350,hide:!0}},title:"",actions:["Close"],autoFocus:!0,modal:!1,size:"auto",resizable:!0,draggable:!0,minWidth:90,minHeight:50,maxWidth:1/0,maxHeight:1/0,pinned:!1,scrollable:!0,position:{},content:null,visible:null,height:null,width:null,appendTo:"body",isMaximized:!1,isMinimized:!1},_closable:function(){return t.inArray("close",t.map(this.options.actions,function(t){return t.toLowerCase()}))>-1},_keydown:function(t){var i,e,s,r,a,l,p=this,c=p.options,h=d.keys,f=t.keyCode,m=p.wrapper,g=10,u=c.isMaximized,w=c.isMinimized;f==h.ESC&&p._closable()&&(t.stopPropagation(),p._close(!1)),t.target!=t.currentTarget||p._closing||(t.altKey&&82==f&&p.refresh(),t.altKey&&80==f&&(p.options.pinned?p.unpin():p.pin()),t.altKey&&f==h.UP?w?(p.restore(),p.element.focus()):u||(p.maximize(),p.element.focus()):t.altKey&&f==h.DOWN&&(w||u?u&&(p.restore(),p.element.focus()):(p.minimize(),p.wrapper.focus())),i=d.getOffset(m),p.containment&&!p._isPinned&&(i=p.options.position),!c.draggable||t.ctrlKey||t.altKey||u||(p._updateBoundaries(),f==h.UP?(i.top=o(i.top-g,p.minTop,p.maxTop),e=m.css("top",i.top)):f==h.DOWN?(i.top=o(i.top+g,p.minTop,p.maxTop),e=m.css("top",i.top)):f==h.LEFT?(i.left=o(i.left-g,p.minLeft,p.maxLeft),e=m.css("left",i.left)):f==h.RIGHT&&(i.left=o(i.left+g,p.minLeft,p.maxLeft),e=m.css("left",i.left))),c.resizable&&t.ctrlKey&&!u&&!w&&(f==h.UP?(e=!0,r=m.height()-g):f==h.DOWN&&(e=!0,r=p.containment&&!p._isPinned?Math.min(m.height()+g,p.containment.height-i.top-n(m,"padding-top")-n(m,"borderBottomWidth")-n(m,"borderTopWidth")):m.height()+g),f==h.LEFT?(e=!0,s=m.width()-g):f==h.RIGHT&&(e=!0,s=p.containment&&!p._isPinned?Math.min(m.width()+g,p.containment.width-i.left-n(m,"borderLeftWidth")-n(m,"borderRightWidth")):m.width()+g),e&&(a=o(s,c.minWidth,c.maxWidth),l=o(r,c.minHeight,c.maxHeight),isNaN(a)||(m.width(a),p.options.width=a+"px"),isNaN(l)||(m.height(l),p.options.height=l+"px"),p.resize())),e&&t.preventDefault())},_overlay:function(i){var e=this.containment?this.containment.children(W):this.appendTo.children(W),n=this.wrapper;return e.length||(e=t("<div class='k-overlay' />")),e.insertBefore(n[0]).toggle(i).css(tt,parseInt(n.css(tt),10)-1),this.options.modal.preventScroll&&!this.containment&&this._stopDocumentScrolling(),e},_actionForIcon:function(t){var i=/\bk-i(-\w+)+\b/.exec(t[0].className)[0];return{"k-i-close":"_close","k-i-window-maximize":"maximize","k-i-window-minimize":"minimize","k-i-window-restore":"restore","k-i-refresh":"refresh","k-i-pin":"pin","k-i-unpin":"unpin"}[i]},_windowActionHandler:function(e){var n,o;if(!this._closing)return n=t(e.target).closest(".k-window-action").find(".k-icon"),o=this._actionForIcon(n),o?(e.preventDefault(),this[o](),!1):i},_modals:function(){var i=this,e=t(T).filter(function(){var e=t(this),n=i._object(e),o=n&&n.options;return o&&o.modal&&o.visible&&o.appendTo===i.options.appendTo&&e.is(E)}).sort(function(i,e){return+t(i).css("zIndex")-+t(e).css("zIndex")});return i=null,e},_object:function(t){var e=t.children(M),n=d.widgetInstance(e);return n?n:i},center:function(){var i,e,o=this,s=o.options.position,r=o.wrapper,a=t(window),l=0,d=0;return o.options.isMaximized?o:(o.options.pinned&&!o._isPinned&&o.pin(),o.options.pinned||(l=a.scrollTop(),d=a.scrollLeft()),this.containment&&!o.options.pinned?(i=this.minTop+(this.maxTop-this.minTop)/2,e=this.minLeft+(this.maxLeft-this.minLeft)/2):(o._scrollIsAppended=!0,e=d+Math.max(0,(a.width()-r.width())/2),i=l+Math.max(0,(a.height()-r.height()-n(r,"paddingTop"))/2)),r.css({left:e,top:i}),s.top=i,s.left=e,o)},title:function(e){var n,o,s,r,a=this,l=!0,p=a.wrapper,c=p.children(L),h=c.children(y);return arguments.length?(t.isPlainObject(e)?(n=i!==e.text?e.text:"",l=e.encoded!==!1):n=e,n===!1?(p.addClass("k-window-titleless"),c.remove()):(c.length?h.html(l?d.htmlEncode(n):n):(p.prepend(ht.titlebar({title:l?d.htmlEncode(n):n})),a._actions(),c=p.children(L)),r=p.css("visibility"),s=p.css("display"),r===F?(p.css({display:""}),o=parseInt(u(c),10),p.css({display:s})):(p.css({visibility:F,display:""}),o=parseInt(u(c),10),p.css({visibility:r,display:s})),p.css("padding-top",o),c.css("margin-top",-o)),a.options.title=n,a):h.html()},content:function(t,i){var n=this.wrapper.children(M),o=n.children(".km-scroll-container");return n=o[0]?o:n,e(t)?(this.angular("cleanup",function(){return{elements:n.children()}}),d.destroy(this.element.children()),n.empty().html(t),this.angular("compile",function(){var t,e=[];for(t=n.length;--t>=0;)e.push({dataItem:i});return{elements:n.children(),data:e}}),this):n.html()},open:function(){var i,e,n,o=this,s=o.wrapper,r=o.options,a=this._animationOptions("open"),l=s.children(M),p=this.containment&&!o._isPinned,c=p?this.containment:t(document);return o.trigger(j)||(o._closing&&s.kendoStop(!0,!0),o._closing=!1,o.toFront(),r.autoFocus&&o.element.focus(),r.visible=!0,r.modal&&(e=!!o._modals().length,i=o._overlay(e),i.kendoStop(!0,!0),a.duration&&d.effects.Fade&&!e?(n=d.fx(i).fadeIn(),n.duration(a.duration||0),n.endValue(.5),n.play()):i.css("opacity",.5),i.show(),t(window).on("focus"+k,function(){l.data("isFront")&&!t(document.activeElement).closest(l).length&&o.element.focus()})),s.is(E)||(l.css($,F),s.show().kendoStop().kendoAnimate({effects:a.effects,duration:a.duration,complete:w(this._activate,this)}))),r.isMaximized&&(o._containerScrollTop=c.scrollTop(),o._containerScrollLeft=c.scrollLeft(),o._stopDocumentScrolling()),this.options.pinned&&!this._isPinned&&this.pin(),o},_activate:function(){var t=this.options.scrollable!==!1;this.options.autoFocus&&this.element.focus(),this.element.css($,t?"":"hidden"),d.resize(this.element.children()),this.trigger(A)},_removeOverlay:function(e){var n,o=this._modals(),s=this.options,r=s.modal&&!o.length,a=s.modal?this._overlay(!0):t(i),l=this._animationOptions("close");r?(!e&&l.duration&&d.effects.Fade?(n=d.fx(a).fadeOut(),n.duration(l.duration||0),n.startValue(.5),n.play()):this._overlay(!1).remove(),s.modal.preventScroll&&this._enableDocumentScrolling()):o.length&&(this._object(o.last())._overlay(!0),s.modal.preventScroll&&this._stopDocumentScrolling())},_close:function(i){var e,n=this,o=n.wrapper,s=n.options,r=this._animationOptions("open"),a=this._animationOptions("close"),l=this.containment&&!n._isPinned,d=l?this.containment:t(document);n._closing||(e=n.trigger(K,{userTriggered:!i}),n._closing=!e,o.is(E)&&!e&&(s.visible=!1,t(T).each(function(i,e){var n=t(e).children(M);e!=o&&n.find("> ."+S).length>0&&n.children(W).remove()}),this._removeOverlay(),o.kendoStop().kendoAnimate({effects:a.effects||r.effects,reverse:a.reverse===!0,duration:a.duration,complete:w(this._deactivate,this)}),t(window).off(k)),n.options.isMaximized&&(n._enableDocumentScrolling(),n._containerScrollTop&&n._containerScrollTop>0&&d.scrollTop(n._containerScrollTop),n._containerScrollLeft&&n._containerScrollLeft>0&&d.scrollLeft(n._containerScrollLeft)))},_deactivate:function(){var t,i=this;i.wrapper.hide().css("opacity",""),i.trigger(N),i.options.modal&&(t=i._object(i._modals().last()),t&&t.toFront())},close:function(){return this._close(!0),this},_actionable:function(i){return t(i).is(st+","+st+" .k-icon,:input,a")},_shouldFocus:function(i){var e=m(),n=this.element;return this.options.autoFocus&&!t(e).is(n)&&!this._actionable(i)&&(!n.find(e).length||!n.find(i).length)},toFront:function(i){var e,n,o=this,s=o.wrapper,r=s[0],a=o.containment&&!o._isPinned,l=+s.css(tt),d=l,p=i&&i.target||null;return t(T).each(function(i,e){var n=t(e),o=n.css(tt),s=n.children(M);isNaN(o)||(l=Math.max(+o,l)),s.data("isFront",e==r),e!=r&&s.find("> ."+S).length>0&&s.append(ht.overlay)}),(!s[0].style.zIndex||d<l)&&s.css(tt,l+2),o.element.find("> .k-overlay").remove(),o._shouldFocus(p)&&(o.isMinimized()?o.wrapper.focus():t(p).is(W)?setTimeout(function(){o.element.focus()}):o.element.focus(),e=a?o.containment.scrollTop():t(window).scrollTop(),n=parseInt(s.position().top,10),!o.options.pinned&&n>0&&n<e&&(e>0?t(window).scrollTop(n):s.css("top",e))),s=null,o},toggleMaximization:function(){return this._closing?this:this[this.options.isMaximized?"restore":"maximize"]()},restore:function(){var i,e=this,n=e.options,s=n.minHeight,r=e.restoreOptions,a=e.containment&&!e._isPinned?e.containment:t(document);return n.isMaximized||n.isMinimized?(s&&s!=1/0&&e.wrapper.css("min-height",s),r&&!n.isMaximized&&(r.height=o(r.height,e.options.minHeight,e.options.maxHeight),i=n.position.top+parseInt(r.height,10)>e.maxTop,i&&(n.position.top=o(n.position.top,e.minTop,e.maxTop-parseInt(r.height,10)),_(r,{left:n.position.left,top:n.position.top}))),e.wrapper.css({position:n.pinned?"fixed":"absolute",left:r.left,top:r.top,width:r.width,height:r.height}).removeClass(C).find(".k-window-content,.k-resize-handle").show().end().find(".k-window-titlebar .k-i-window-restore").parent().remove().end().end().find(it).parent().show().end().end().find(ot).parent().show(),n.isMaximized?e.wrapper.find(".k-i-window-maximize").parent().focus():n.isMinimized&&e.wrapper.find(".k-i-window-minimize").parent().focus(),e.options.width=r.width,e.options.height=r.height,e.options.modal.preventScroll||e._enableDocumentScrolling(),e._containerScrollTop&&e._containerScrollTop>0&&a.scrollTop(e._containerScrollTop),e._containerScrollLeft&&e._containerScrollLeft>0&&a.scrollLeft(e._containerScrollLeft),n.isMaximized=n.isMinimized=!1,e.wrapper.removeAttr("tabindex"),e.wrapper.removeAttr("aria-labelled-by"),e.resize(),e):e},_sizingAction:function(t,i){var e=this,n=e.wrapper,o=n[0].style,s=e.options;return s.isMaximized||s.isMinimized?e:(e.restoreOptions={width:o.width,height:o.height},n.children(O).hide().end().children(L).find(it).parent().hide().eq(0).before(ht.action({name:"window-restore"})),i.call(e),e.wrapper.children(L).find(ot).parent().toggle("maximize"!==t),e.trigger(t),n.find(".k-i-window-restore").parent().focus(),e)},maximize:function(){return this._sizingAction("maximize",function(){var i=this,e=i.wrapper,n=this.containment&&!i._isPinned,o=e.position(),s=t(document);_(i.restoreOptions,{left:o.left+(n?this.containment.scrollLeft():0),top:o.top+(n?this.containment.scrollTop():0)}),this._containerScrollTop=n?this.containment.scrollTop():s.scrollTop(),this._containerScrollLeft=n?this.containment.scrollLeft():s.scrollLeft(),i._stopDocumentScrolling(),e.css({top:n?this.containment.scrollTop():0,left:n?this.containment.scrollLeft():0,position:n?"absolute":"fixed"}).addClass(C),i.options.isMaximized=!0,i._onDocumentResize()}),this},_stopDocumentScrolling:function(){var e,n,o=this,s=o.containment;return s&&!o._isPinned?(o._storeOverflowRule(s),s.css($,F),o.wrapper.css({maxWidth:s.innerWidth(),maxHeight:s.innerHeight()}),i):(e=t("body"),o._storeOverflowRule(e),e.css($,F),n=t("html"),o._storeOverflowRule(n),n.css($,F),i)},_enableDocumentScrolling:function(){var e=this,n=e.containment;return n&&!e._isPinned?(e._restoreOverflowRule(n),e.wrapper.css({maxWidth:n.width,maxHeight:n.height}),i):(e._restoreOverflowRule(t(document.body)),e._restoreOverflowRule(t("html")),i)},_storeOverflowRule:function(t){if(!this._isOverflowStored(t)){var i=t.get(0).style.overflow;"string"==typeof i&&t.data(Z,i)}},_isOverflowStored:function(t){return"string"==typeof t.data(Z)},_restoreOverflowRule:function(t){var e=t.data(Z);null!==e&&e!==i?(t.css($,e),t.removeData(Z)):t.css($,"")},isMaximized:function(){return this.options.isMaximized},minimize:function(){return this._sizingAction("minimize",function(){var t=this;t.wrapper.css({height:"",minHeight:""}),t.element.hide(),t.options.isMinimized=!0}),this.wrapper.attr("tabindex",0),this.wrapper.attr("aria-labelled-by",this.element.attr("aria-labelled-by")),this._updateBoundaries(),this},isMinimized:function(){return this.options.isMinimized},pin:function(){var i=this,e=t(window),o=i.wrapper,s=i.options,a=s.position,l=this.containment?r(o[0]).top+n(this.containment,"borderTopWidth"):n(o,"top"),d=this.containment?r(o[0]).left+n(this.containment,"borderLeftWidth"):n(o,"left");i.options.isMaximized||(a.top=l,a.left=d,!i._scrollIsAppended||this.containment&&"fixed"===this.containment.css("position")||(a.top-=e.scrollTop(),a.left-=e.scrollLeft(),i._scrollIsAppended=!1),o.css(_(a,{position:"fixed"})),o.children(L).find(et).addClass("k-i-unpin").removeClass("k-i-pin"),i._isPinned=!0,i.options.pinned=!0,this.containment&&(s.maxWidth=s.maxHeight=1/0,o.css({maxWidth:"",maxHeight:""})))},unpin:function(){var i=this,e=t(window),s=i.wrapper,r=i.options,a=i.options.position,l=i.containment,d=parseInt(s.css("top"),10)+e.scrollTop(),p=parseInt(s.css("left"),10)+e.scrollLeft();i.options.isMaximized||(i._isPinned=!1,i._scrollIsAppended=!0,i.options.pinned=!1,l&&(i._updateBoundaries(),r.maxWidth=Math.min(l.width,r.maxWidth),r.maxHeight=Math.min(l.height-n(s,"padding-top"),r.maxHeight),s.css({maxWidth:r.maxWidth,maxHeight:r.maxHeight}),d=d<l.position.top?i.minTop:d>l.position.top+l.height?i.maxTop:d+l.scrollTop()-(l.position.top+n(l,"border-top-width")),p=p<l.position.left?i.minLeft:p>l.position.left+l.width?i.maxLeft:p+l.scrollLeft()-(l.position.left+n(l,"border-left-width"))),a.top=o(d,i.minTop,i.maxTop),a.left=o(p,i.minLeft,i.maxLeft),s.css(_(a,{position:""})),s.children(L).find(nt).addClass("k-i-pin").removeClass("k-i-unpin"))},_onDocumentResize:function(){var i,e,o,s,r,a=this,l=a.wrapper,p=t(window),c=d.support.zoomLevel(),h="content-box"==l.css("box-sizing");a.options.isMaximized&&(o=h?n(l,"border-left-width")+n(l,"border-right-width"):0,s=h?n(l,"border-top-width")+n(l,"border-bottom-width"):0,r=h?n(l,"padding-top"):0,a.containment&&!a._isPinned?(i=a.containment.innerWidth()-o,e=a.containment.innerHeight()-(s+r)):(i=p.width()/c-o,e=p.height()/c-(s+r)),l.css({width:i,height:e}),a.options.width=i,a.options.height=e,a.resize())},refresh:function(i){var n,o,s,r=this,a=r.options,l=t(r.element);return f(i)||(i={url:i}),i=_({},a.content,i),o=e(a.iframe)?a.iframe:i.iframe,s=i.url,s?(e(o)||(o=!dt(s)),o?(n=l.find("."+S)[0],n?n.src=s||n.src:l.html(ht.contentFrame(_({},a,{content:i}))),l.find("."+S).unbind("load"+z).on("load"+z,w(this._triggerRefresh,this))):r._ajaxRequest(i)):(i.template&&r.content(x(i.template)({})),r.trigger(B)),l.toggleClass("k-window-iframecontent",!!o),r},_triggerRefresh:function(){this.trigger(B)},_ajaxComplete:function(){clearTimeout(this._loadingIconTimeout),this.wrapper.find(rt).removeClass(H)},_ajaxError:function(t,i){this.trigger(Y,{status:i,xhr:t})},_ajaxSuccess:function(t){return function(i){var e=i;t&&(e=x(t)(i||{})),this.content(e,i),this.element.prop("scrollTop",0),this.trigger(B)}},_showLoading:function(){this.wrapper.find(rt).addClass(H)},_ajaxRequest:function(i){this._loadingIconTimeout=setTimeout(w(this._showLoading,this),100),t.ajax(_({type:"GET",dataType:"html",cache:!1,error:w(this._ajaxError,this),complete:w(this._ajaxComplete,this),success:w(this._ajaxSuccess(i.template),this)},i))},_destroy:function(){this.resizing&&this.resizing.destroy(),this.dragging&&this.dragging.destroy(),this.wrapper.off(z).children(M).off(z).end().find(".k-resize-handle,.k-window-titlebar").off(z),t(window).off("resize"+z+this._marker),t(window).off(k),t(window).off(z),clearTimeout(this._loadingIconTimeout),p.fn.destroy.call(this),this.unbind(i),d.destroy(this.wrapper),this._removeOverlay(!0)},destroy:function(){this._destroy(),this.wrapper.empty().remove(),this.wrapper=this.appendTo=this.element=t()},_createWindow:function(){var i,e,n=this.element,o=this.options,s=d.support.isRtl(n);o.scrollable===!1&&n.css("overflow","hidden"),e=t(ht.wrapper(o)),i=n.find("iframe:not(.k-content)").map(function(){var t=this.getAttribute("src");return this.src="",t}),e.toggleClass("k-rtl",s).append(n).find("iframe:not(.k-content)").each(function(t){this.src=i[t]}),this.containment?this.containment.prepend(e):this.appendTo&&e.appendTo(this.appendTo),e.find(".k-window-title").css(s?"left":"right",g(e.find(".k-window-actions"))+10),n.css("visibility","").show(),n.find("[data-role=editor]").each(function(){var i=t(this).data("kendoEditor");i&&i.refresh()}),e=n=null}}),ht={wrapper:x("<div class='k-widget k-window' />"),action:x("<a role='button' href='\\#' class='k-button k-bare k-button-icon k-window-action' aria-label='#= name #'><span class='k-icon k-i-#= name.toLowerCase() #'></span></a>"),titlebar:x("<div class='k-window-titlebar k-header'><span class='k-window-title'>#= title #</span><div class='k-window-actions' /></div>"),overlay:"<div class='k-overlay' />",contentFrame:x("<iframe frameborder='0' title='#= title #' class='"+S+"' src='#= content.url #'>This page requires frames in order to show content</iframe>"),resizeHandle:x("<div class='k-resize-handle k-resize-#= data #'></div>")};a.prototype={addOverlay:function(){this.owner.wrapper.append(ht.overlay)},removeOverlay:function(){this.owner.wrapper.find(W).remove()},dragstart:function(i){var e,n,o,s,a,l,p,c=this,h=c.owner,f=h.wrapper;c._preventDragging=h.trigger(G),c._preventDragging||(c.elementPadding=parseInt(f.css("padding-top"),10),c.initialPosition=d.getOffset(f,"position"),c.resizeDirection=i.currentTarget.prop("className").replace("k-resize-handle k-resize-",""),c.initialSize={width:f.width(),height:f.height()},h._updateBoundaries(),c.containerOffset=h.containment?h.containment.position:d.getOffset(h.appendTo,"position"),e=f.offsetParent(),e.is("html")?c.containerOffset.top=c.containerOffset.left=0:(n=e.css("margin-top"),o=e.css("margin-left"),s=!lt.test(n)||!lt.test(o),s&&(a=r(f[0]),l=a.left-c.containerOffset.left-c.initialPosition.left,p=a.top-c.containerOffset.top-c.initialPosition.top,c._relativeElMarginLeft=l>1?l:0,c._relativeElMarginTop=p>1?p:0,c.initialPosition.left+=c._relativeElMarginLeft,c.initialPosition.top+=c._relativeElMarginTop)),f.children(O).not(i.currentTarget).hide(),t(b).css(R,i.currentTarget.css(R)))},drag:function(i){var e,n,s,r,a,l,p,c,h,f,m,g,u,w,_,v,x,b,z,k;this._preventDragging||(e=this,n=e.owner,s=n.wrapper,r=n.options,a=r.position,l=e.resizeDirection,p=e.containerOffset,c=e.initialPosition,h=e.initialSize,f=n.containment&&!n._isPinned,m=d.support.isRtl(n.containment),g=f&&m&&n.containment.innerWidth()>n.containment.width?d.support.scrollbar():0,u=f?{top:n.containment.scrollTop(),left:n.containment.scrollLeft()}:{top:0,left:0},b=Math.max(i.x.location,0),z=Math.max(i.y.location,0),l.indexOf("e")>=0?(w=n.containment&&b-h.width>=n.maxLeft-u.left+p.left+g?n.maxLeft+g-c.left+h.width-u.left:b-c.left-p.left,s.width(o(w,r.minWidth,r.maxWidth))):l.indexOf("w")>=0&&(x=c.left+h.width+p.left,w=o(x-b,r.minWidth,r.maxWidth),a.left=x-w-p.left-g-(e._relativeElMarginLeft||0)+u.left,n.containment&&a.left<=n.minLeft&&(a.left=n.minLeft,w=o(x-g-a.left-p.left+u.left,r.minWidth,r.maxWidth)),s.css({left:a.left,width:w})),k=z,n.options.pinned&&(k-=t(window).scrollTop()),l.indexOf("s")>=0?(_=k-c.top-e.elementPadding-p.top,k-h.height-e.elementPadding>=n.maxTop+p.top-u.top&&(_=n.maxTop-c.top+h.height-u.top),s.height(o(_,r.minHeight,r.maxHeight))):l.indexOf("n")>=0&&(v=c.top+h.height+p.top,_=o(v-k,r.minHeight,r.maxHeight),a.top=v-_-p.top-(e._relativeElMarginTop||0)+u.top,a.top<=n.minTop&&n.containment&&(a.top=n.minTop,_=o(v-a.top-p.top+u.top,r.minHeight,r.maxHeight)),s.css({top:a.top,height:_})),w&&(n.options.width=w+"px"),_&&(n.options.height=_+"px"),n.resize())},dragend:function(i){if(!this._preventDragging){var e=this,n=e.owner,o=n.wrapper;return o.children(O).not(i.currentTarget).show(),t(b).css(R,""),n.touchScroller&&n.touchScroller.reset(),27==i.keyCode&&o.css(e.initialPosition).css(e.initialSize),n.trigger(V),!1}},destroy:function(){this._draggable&&this._draggable.destroy(),this._draggable=this.owner=null}},l.prototype={dragstart:function(i){var e=this.owner,n=e.options.draggable,o=e.element,s=o.find(".k-window-actions"),r=d.getOffset(e.appendTo);this._preventDragging=e.trigger(Q)||!n,this._preventDragging||e.isMaximized()||(e.initialWindowPosition=d.getOffset(e.wrapper,"position"),e.initialPointerPosition={left:e.options.position.left,top:e.options.position.top},e.startPosition={left:i.x.client-e.initialWindowPosition.left,top:i.y.client-e.initialWindowPosition.top},e._updateBoundaries(),e.containment||(e.minLeft=s.length>0?g(s)+parseInt(s.css("right"),10)-g(o):20-g(o),e.minLeft-=r.left,e.minTop=-r.top),e.wrapper.append(ht.overlay).children(O).hide(),t(b).css(R,i.currentTarget.css(R)))},drag:function(i){var e,n,s=this.owner,r=s.options.position,a=s.options.draggable.axis;this._preventDragging||s.isMaximized()||(a&&"x"!==a.toLowerCase()||(e=i.x.client-s.startPosition.left,s.containment&&!s._isPinned&&(e+=s.containment.scrollLeft()),r.left=o(e,s.minLeft,s.maxLeft)),a&&"y"!==a.toLowerCase()||(n=i.y.client-s.startPosition.top,s.containment&&!s._isPinned&&(n+=s.containment.scrollTop()),r.top=o(n,s.minTop,s.maxTop)),d.support.transforms?t(s.wrapper).css("transform","translate("+(r.left-s.initialPointerPosition.left)+"px, "+(r.top-s.initialPointerPosition.top)+"px)"):t(s.wrapper).css(r))},_finishDrag:function(){var i=this.owner;i.wrapper.children(O).toggle(!i.options.isMinimized).end().find(W).remove(),t(b).css(R,"")},dragcancel:function(t){this._preventDragging||(this._finishDrag(),t.currentTarget.closest(T).css(this.owner.initialWindowPosition))},dragend:function(){var i=this.owner;if(!this._preventDragging&&!i.isMaximized())return t(i.wrapper).css(i.options.position).css("transform",""),this._finishDrag(),i.trigger(X),!1},destroy:function(){this._draggable&&this._draggable.destroy(),this._draggable=this.owner=null}},d.ui.plugin(ct)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,i,e){(e||i)()});
//# sourceMappingURL=kendo.window.min.js.map
