{"version": 3, "sources": ["kendo.router.js"], "names": ["f", "define", "$", "undefined", "absoluteURL", "path", "pathPrefix", "regEx", "RegExp", "test", "location", "protocol", "host", "replace", "hashDelimiter", "bang", "locationHash", "href", "indexOf", "split", "stripRoot", "root", "url", "substr", "length", "fixHash", "fixBang", "kendo", "window", "CHANGE", "BACK", "SAME", "support", "history", "CHECK_URL_INTERVAL", "BROKEN_BACK_NAV", "browser", "msie", "hashStrip", "document", "HistoryAdapter", "Class", "extend", "back", "setTimeout", "forward", "replaceLocation", "PushStateAdapter", "init", "this", "navigate", "to", "pushState", "title", "replaceState", "normalize", "current", "pathname", "search", "change", "callback", "bind", "stop", "unbind", "normalizeCurrent", "options", "fixedUrl", "hash", "hashBang", "HashAdapter", "_id", "guid", "prefix", "fix", "hashChange", "on", "_interval", "setInterval", "off", "clearInterval", "History", "Observable", "start", "_started", "adapter", "createAdapter", "history<PERSON><PERSON><PERSON>", "locations", "proxy", "silent", "_navigate", "backCalled", "push", "decodeURIComponent", "trigger", "decode", "call", "_checkUrl", "<PERSON><PERSON><PERSON><PERSON>", "navigatingInExisting", "prev", "backButtonPressed", "pop", "j<PERSON><PERSON><PERSON>", "named<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "match", "optional", "routeToRegExp", "route", "ignoreCase", "escapeRegExp", "optionalParam", "namedP<PERSON><PERSON>", "splatParam", "stripUrl", "INIT", "ROUTE_MISSING", "Route", "_callback", "params", "idx", "queryStringParams", "parseQueryStringParams", "_back", "exec", "slice", "apply", "worksWith", "Router", "fn", "routes", "destroy", "_urlChangedProxy", "_sameProxy", "_backProxy", "initEventObject", "that", "sameProxy", "_same", "backProxy", "e", "urlChangedProxy", "_url<PERSON><PERSON>ed", "same", "preventDefault", "noop", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gBAAiB,cAAeD,IACzC,WAsaE,MA7ZC,UAAUE,EAAGC,GAEV,QAASC,GAAYC,EAAMC,GACvB,IAAKA,EACD,MAAOD,EAEPA,GAAO,MAAQC,IACfD,EAAOC,EAEX,IAAIC,GAAYC,OAAO,IAAMF,EAAY,IAIzC,OAHKC,GAAME,KAAKJ,KACZA,EAAOC,EAAa,IAAMD,GAEvBK,EAASC,SAAW,MAAQD,EAASE,KAAO,IAAMP,GAAMQ,QAAQ,SAAU,KAErF,QAASC,GAAcC,GACnB,MAAOA,GAAO,KAAO,IAEzB,QAASC,GAAaF,GAClB,GAAIG,GAAOP,EAASO,IACpB,OAAsB,OAAlBH,GAA0BG,EAAKC,QAAQ,SAAaD,EAAKC,QAAQ,MAAQ,EAClE,KAEJD,EAAKE,MAAML,GAAe,IAAM,GAE3C,QAASM,GAAUC,EAAMC,GACrB,MAA0B,KAAtBA,EAAIJ,QAAQG,GACLC,EAAIC,OAAOF,EAAKG,QAAQX,QAAQ,QAAS,KAEzCS,EAoEf,QAASG,GAAQH,GACb,MAAOA,GAAIT,QAAQ,QAAS,KAEhC,QAASa,GAAQJ,GACb,MAAOA,GAAIT,QAAQ,YAAa,MArGvC,GACOc,GAAQC,OAAOD,MAAOE,EAAS,SAAUC,EAAO,OAAQC,EAAO,OAAQC,EAAUL,EAAMK,QAAStB,EAAWkB,OAAOlB,SAAUuB,EAAUL,OAAOK,QAASC,EAAqB,GAAIC,EAAkBR,EAAMK,QAAQI,QAAQC,KAAMC,EAAY,MAAOC,EAAWX,OAAOW,SA+BlQC,EAAiBb,EAAMc,MAAMC,QAC7BC,KAAM,WACER,EACAS,WAAW,WACPX,EAAQU,SAGZV,EAAQU,QAGhBE,QAAS,WACDV,EACAS,WAAW,WACPX,EAAQY,YAGZZ,EAAQY,WAGhBrB,OAAQ,WACJ,MAAOS,GAAQT,QAEnBsB,gBAAiB,SAAUxB,GACvBZ,EAASG,QAAQS,MAGrByB,EAAmBP,EAAeE,QAClCM,KAAM,SAAU3B,GACZ4B,KAAK5B,KAAOA,GAEhB6B,SAAU,SAAUC,GAChBlB,EAAQmB,aAAcb,EAASc,MAAOjD,EAAY+C,EAAIF,KAAK5B,QAE/DR,QAAS,SAAUsC,GACflB,EAAQqB,gBAAiBf,EAASc,MAAOjD,EAAY+C,EAAIF,KAAK5B,QAElEkC,UAAW,SAAUjC,GACjB,MAAOF,GAAU6B,KAAK5B,KAAMC,IAEhCkC,QAAS,WACL,GAAIA,GAAU9C,EAAS+C,QAIvB,OAHI/C,GAASgD,SACTF,GAAW9C,EAASgD,QAEjBtC,EAAU6B,KAAK5B,KAAMmC,IAEhCG,OAAQ,SAAUC,GACd1D,EAAE0B,QAAQiC,KAAK,iBAAkBD,IAErCE,KAAM,WACF5D,EAAE0B,QAAQmC,OAAO,mBAErBC,iBAAkB,SAAUC,GACxB,GAAIC,GAAU7C,EAAO4C,EAAQ5C,KAAMoC,EAAW/C,EAAS+C,SAAUU,EAAOnD,EAAaF,EAAcmD,EAAQG,UACvG/C,KAASoC,EAAW,MACpBS,EAAW7C,GAEXA,IAASoC,GAAYU,IACrBD,EAAW9D,EAAY+D,EAAKtD,QAAQyB,EAAW,IAAKjB,IAEpD6C,GACAjC,EAAQmB,aAAcb,EAASc,MAAOa,MAU9CG,EAAc7B,EAAeE,QAC7BM,KAAM,SAAUjC,GACZkC,KAAKqB,IAAM3C,EAAM4C,OACjBtB,KAAKuB,OAAS1D,EAAcC,GAC5BkC,KAAKwB,IAAM1D,EAAOW,EAAUD,GAEhCyB,SAAU,SAAUC,GAChBzC,EAASyD,KAAOlB,KAAKwB,IAAItB,IAE7BtC,QAAS,SAAUsC,GACfF,KAAKH,gBAAgBG,KAAKwB,IAAItB,KAElCI,UAAW,SAAUjC,GACjB,MAAIA,GAAIJ,QAAQ+B,KAAKuB,QAAU,EACpBlD,EAEAA,EAAIH,MAAM8B,KAAKuB,QAAQ,IAGtCb,OAAQ,SAAUC,GACV5B,EAAQ0C,WACRxE,EAAE0B,QAAQ+C,GAAG,cAAgB1B,KAAKqB,IAAKV,GAEvCX,KAAK2B,UAAYC,YAAYjB,EAAU1B,IAG/C4B,KAAM,WACF5D,EAAE0B,QAAQkD,IAAI,cAAgB7B,KAAKqB,KACnCS,cAAc9B,KAAK2B,YAEvBpB,QAAS,WACL,MAAOxC,GAAaiC,KAAKuB,SAE7BR,iBAAkB,SAAUC,GACxB,GAAIR,GAAW/C,EAAS+C,SAAUpC,EAAO4C,EAAQ5C,IACjD,UAAI4C,EAAQb,WAAa/B,IAASoC,KAC9BR,KAAKH,gBAAgBzB,EAAO4B,KAAKuB,OAASpD,EAAUC,EAAMoC,KACnD,MAKfuB,EAAUrD,EAAMsD,WAAWvC,QAC3BwC,MAAO,SAAUjB,GAOb,GANAA,EAAUA,MACVhB,KAAKY,MACDhC,EACAC,EACAC,GACDkC,IACChB,KAAKkC,SAAT,CAGAlC,KAAKkC,UAAW,EAChBlB,EAAQ5C,KAAO4C,EAAQ5C,MAAQ,GAC/B,IAA2CmC,GAAvC4B,EAAUnC,KAAKoC,cAAcpB,EAC7BmB,GAAQpB,iBAAiBC,KAG7BT,EAAU4B,EAAQ5B,UAClBtD,EAAEwC,OAAOO,MACLmC,QAASA,EACT/D,KAAM4C,EAAQ5C,KACdiE,cAAeF,EAAQ5D,SACvBgC,QAASA,EACT+B,WAAY/B,KAEhB4B,EAAQzB,OAAOzD,EAAEsF,MAAMvC,KAAM,iBAEjCoC,cAAe,SAAUpB,GACrB,MAAOjC,GAAQoB,WAAaa,EAAQb,UAAY,GAAIL,GAAiBkB,EAAQ5C,MAAQ,GAAIgD,GAAYJ,EAAQG,WAEjHN,KAAM,WACGb,KAAKkC,WAGVlC,KAAKmC,QAAQtB,OACbb,KAAKc,OAAOlC,GACZoB,KAAKkC,UAAW,IAEpBxB,OAAQ,SAAUC,GACdX,KAAKY,KAAKhC,EAAQ+B,IAEtB/C,QAAS,SAAUsC,EAAIsC,GACnBxC,KAAKyC,UAAUvC,EAAIsC,EAAQ,SAAUL,GACjCA,EAAQvE,QAAQsC,GAChBF,KAAKsC,UAAUtC,KAAKsC,UAAU/D,OAAS,GAAKyB,KAAKO,WAGzDN,SAAU,SAAUC,EAAIsC,GACpB,MAAW,WAAPtC,GACAF,KAAK0C,YAAa,EAClB1C,KAAKmC,QAAQzC,OACb,IAEJM,KAAKyC,UAAUvC,EAAIsC,EAAQ,SAAUL,GACjCA,EAAQlC,SAASC,GACjBF,KAAKsC,UAAUK,KAAK3C,KAAKO,WAF7BP,IAKJyC,UAAW,SAAUvC,EAAIsC,EAAQ7B,GAC7B,GAAIwB,GAAUnC,KAAKmC,OAEnB,OADAjC,GAAKiC,EAAQ7B,UAAUJ,GACnBF,KAAKO,UAAYL,GAAMF,KAAKO,UAAYqC,mBAAmB1C,IAC3DF,KAAK6C,QAAQ/D,GACb,KAEC0D,GACGxC,KAAK6C,QAAQjE,GACTP,IAAK6B,EACL4C,QAAQ,MAKpB9C,KAAKO,QAAUL,EACfS,EAASoC,KAAK/C,KAAMmC,GACpBnC,KAAKqC,cAAgBF,EAAQ5D,UAV7B,IAYJyE,UAAW,WACP,GAAIb,GAAUnC,KAAKmC,QAAS5B,EAAU4B,EAAQ5B,UAAW0C,EAAYd,EAAQ5D,SAAU2E,EAAuBlD,KAAKqC,gBAAkBY,EAAWvD,EAAOa,IAAYP,KAAKsC,UAAUtC,KAAKsC,UAAU/D,OAAS,IAAM2E,EAAsBR,EAAa1C,KAAK0C,WAAYS,EAAOnD,KAAKO,OAChR,OAAgB,QAAZA,GAAoBP,KAAKO,UAAYA,GAAWP,KAAKO,UAAYqC,mBAAmBrC,KAGxFP,KAAKqC,cAAgBY,EACrBjD,KAAK0C,YAAa,EAClB1C,KAAKO,QAAUA,EACXb,GAAQM,KAAK6C,QAAQ,QACjBxE,IAAK8E,EACLjD,GAAIK,KAER4B,EAAQvC,UACRI,KAAKO,QAAU4C,EACf,GAEAnD,KAAK6C,QAAQjE,GACTP,IAAKkC,EACL6C,mBAAoBV,KAEpBhD,EACAyC,EAAQvC,WAERuC,EAAQzC,OACRM,KAAKqC,iBAETrC,KAAKO,QAAU4C,EACf,IAEAzD,EACAM,KAAKsC,UAAUe,MAEfrD,KAAKsC,UAAUK,KAAKpC,GAHxB,MAOR7B,GAAMqD,QAAUA,EAChBrD,EAAMqD,QAAQxC,eAAiBA,EAC/Bb,EAAMqD,QAAQX,YAAcA,EAC5B1C,EAAMqD,QAAQjC,iBAAmBA,EACjCpB,EAAMvB,YAAcA,EACpBuB,EAAMM,QAAU,GAAI+C,IACtBpD,OAAOD,MAAM4E,QACd,WAEG,QAASC,GAAkBC,EAAOC,GAC9B,MAAOA,GAAWD,EAAQ,UAE9B,QAASE,GAAcC,EAAOC,GAC1B,MAAWrG,QAAO,IAAMoG,EAAM/F,QAAQiG,EAAc,QAAQjG,QAAQkG,EAAe,WAAWlG,QAAQmG,EAAYR,GAAmB3F,QAAQoG,EAAY,SAAW,IAAKJ,EAAa,IAAM,IAEhM,QAASK,GAAS5F,GACd,MAAOA,GAAIT,QAAQ,gBAAiB,IAT3C,GACOc,GAAQC,OAAOD,MAAOM,EAAUN,EAAMM,QAASgD,EAAatD,EAAMsD,WAAYkC,EAAO,OAAQC,EAAgB,eAAgBvF,EAAS,SAAUC,EAAO,OAAQC,EAAO,OAAQgF,EAAgB,aAAcC,EAAa,eAAgBC,EAAa,SAAUH,EAAe,2BAU/QO,EAAQ1F,EAAMc,MAAMC,QACpBM,KAAM,SAAU4D,EAAOhD,EAAUiD,GACvBD,YAAiBpG,UACnBoG,EAAQD,EAAcC,EAAOC,IAEjC5D,KAAK2D,MAAQA,EACb3D,KAAKqE,UAAY1D,GAErBA,SAAU,SAAUtC,EAAKqB,EAAMoD,GAC3B,GAAIwB,GAAiB/F,EAATgG,EAAM,EAAWC,EAAoB9F,EAAM+F,uBAAuBpG,EAK9E,IAJAmG,EAAkBE,MAAQhF,EAC1BrB,EAAM4F,EAAS5F,GACfiG,EAAStE,KAAK2D,MAAMgB,KAAKtG,GAAKuG,MAAM,GACpCrG,EAAS+F,EAAO/F,OACZuE,EACA,KAAOyB,EAAMhG,EAAQgG,IACU,SAAhBD,EAAOC,KACdD,EAAOC,GAAO3B,mBAAmB0B,EAAOC,IAIpDD,GAAO3B,KAAK6B,GACZxE,KAAKqE,UAAUQ,MAAM,KAAMP,IAE/BQ,UAAW,SAAUzG,EAAKqB,EAAMoD,GAC5B,QAAI9C,KAAK2D,MAAMnG,KAAKyG,EAAS5F,MACzB2B,KAAKW,SAAStC,EAAKqB,EAAMoD,IAClB,MAMfiC,EAAS/C,EAAWvC,QACpBM,KAAM,SAAUiB,GACPA,IACDA,MAEJgB,EAAWgD,GAAGjF,KAAKgD,KAAK/C,MACxBA,KAAKiF,UACLjF,KAAKG,UAAYa,EAAQb,UACzBH,KAAKmB,SAAWH,EAAQG,SACxBnB,KAAK5B,KAAO4C,EAAQ5C,KACpB4B,KAAK4D,WAAa5C,EAAQ4C,cAAe,EACzC5D,KAAKY,MACDsD,EACAC,EACAvF,EACAE,EACAD,GACDmC,IAEPkE,QAAS,WACLlG,EAAQ8B,OAAOlC,EAAQoB,KAAKmF,kBAC5BnG,EAAQ8B,OAAOhC,EAAMkB,KAAKoF,YAC1BpG,EAAQ8B,OAAOjC,EAAMmB,KAAKqF,YAC1BrF,KAAKc,UAETmB,MAAO,WAAA,GAgBCqD,GAfAC,EAAOvF,KAAMwF,EAAY,WACrBD,EAAKE,SACNC,EAAY,SAAUC,GACrBJ,EAAKb,MAAMiB,IACZC,EAAkB,SAAUD,GAC3BJ,EAAKM,YAAYF,GAEzB3G,GAAQiD,OACJ6D,KAAMN,EACN9E,OAAQkF,EACRlG,KAAMgG,EACNvF,UAAWoF,EAAKpF,UAChBgB,SAAUoE,EAAKpE,SACf/C,KAAMmH,EAAKnH,OAEXkH,GACAjH,IAAKW,EAAQuB,SAAW,IACxBwF,eAAgB9I,EAAE+I,MAEjBT,EAAK1C,QAAQqB,EAAMoB,IACpBC,EAAKM,YAAYP,GAErBtF,KAAKmF,iBAAmBS,EACxB5F,KAAKqF,WAAaK,GAEtB/B,MAAO,SAAUA,EAAOhD,GACpBX,KAAKiF,OAAOtC,KAAK,GAAIyB,GAAMT,EAAOhD,EAAUX,KAAK4D,cAErD3D,SAAU,SAAU5B,EAAKmE,GACrB9D,EAAMM,QAAQiB,SAAS5B,EAAKmE,IAEhC5E,QAAS,SAAUS,EAAKmE,GACpB9D,EAAMM,QAAQpB,QAAQS,EAAKmE,IAE/BkC,MAAO,SAAUiB,GACT3F,KAAK6C,QAAQhE,GACTR,IAAKsH,EAAEtH,IACP6B,GAAIyF,EAAEzF,MAEVyF,EAAEI,kBAGVN,MAAO,WACHzF,KAAK6C,QAAQ/D,IAEjB+G,YAAa,SAAUF,GAAV,GAeLpB,GAASU,EAAsBtB,EAAOpF,EAdtCF,EAAMsH,EAAEtH,IACRyE,IAAW6C,EAAE7C,OACbpD,EAAOiG,EAAEvC,iBAIb,IAHK/E,IACDA,EAAM,KAEN2B,KAAK6C,QAAQjE,GACTP,IAAKsH,EAAEtH,IACPiG,OAAQ5F,EAAM+F,uBAAuBkB,EAAEtH,KACvC+E,kBAAmB1D,IAGvB,WADAiG,GAAEI,gBAIN,KADIxB,EAAM,EAAGU,EAASjF,KAAKiF,OAAe1G,EAAS0G,EAAO1G,OACnDgG,EAAMhG,EAAQgG,IAEjB,GADAZ,EAAQsB,EAAOV,GACXZ,EAAMmB,UAAUzG,EAAKqB,EAAMoD,GAC3B,MAGJ9C,MAAK6C,QAAQsB,GACT9F,IAAKA,EACLiG,OAAQ5F,EAAM+F,uBAAuBpG,GACrC+E,kBAAmB1D,KAEvBiG,EAAEI,mBAIdrH,GAAMqG,OAASA,KAEZpG,OAAOD,OACE,kBAAV1B,SAAwBA,OAAOiJ,IAAMjJ,OAAS,SAAUkJ,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.router.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.router', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'router',\n        name: 'Router',\n        category: 'framework',\n        description: 'The Router class is responsible for tracking the application state and navigating between the application states.',\n        depends: ['core'],\n        hidden: false\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, CHANGE = 'change', BACK = 'back', SAME = 'same', support = kendo.support, location = window.location, history = window.history, CHECK_URL_INTERVAL = 50, BROKEN_BACK_NAV = kendo.support.browser.msie, hashStrip = /^#*/, document = window.document;\n        function absoluteURL(path, pathPrefix) {\n            if (!pathPrefix) {\n                return path;\n            }\n            if (path + '/' === pathPrefix) {\n                path = pathPrefix;\n            }\n            var regEx = new RegExp('^' + pathPrefix, 'i');\n            if (!regEx.test(path)) {\n                path = pathPrefix + '/' + path;\n            }\n            return location.protocol + '//' + (location.host + '/' + path).replace(/\\/\\/+/g, '/');\n        }\n        function hashDelimiter(bang) {\n            return bang ? '#!' : '#';\n        }\n        function locationHash(hashDelimiter) {\n            var href = location.href;\n            if (hashDelimiter === '#!' && href.indexOf('#') > -1 && href.indexOf('#!') < 0) {\n                return null;\n            }\n            return href.split(hashDelimiter)[1] || '';\n        }\n        function stripRoot(root, url) {\n            if (url.indexOf(root) === 0) {\n                return url.substr(root.length).replace(/\\/\\//g, '/');\n            } else {\n                return url;\n            }\n        }\n        var HistoryAdapter = kendo.Class.extend({\n            back: function () {\n                if (BROKEN_BACK_NAV) {\n                    setTimeout(function () {\n                        history.back();\n                    });\n                } else {\n                    history.back();\n                }\n            },\n            forward: function () {\n                if (BROKEN_BACK_NAV) {\n                    setTimeout(function () {\n                        history.forward();\n                    });\n                } else {\n                    history.forward();\n                }\n            },\n            length: function () {\n                return history.length;\n            },\n            replaceLocation: function (url) {\n                location.replace(url);\n            }\n        });\n        var PushStateAdapter = HistoryAdapter.extend({\n            init: function (root) {\n                this.root = root;\n            },\n            navigate: function (to) {\n                history.pushState({}, document.title, absoluteURL(to, this.root));\n            },\n            replace: function (to) {\n                history.replaceState({}, document.title, absoluteURL(to, this.root));\n            },\n            normalize: function (url) {\n                return stripRoot(this.root, url);\n            },\n            current: function () {\n                var current = location.pathname;\n                if (location.search) {\n                    current += location.search;\n                }\n                return stripRoot(this.root, current);\n            },\n            change: function (callback) {\n                $(window).bind('popstate.kendo', callback);\n            },\n            stop: function () {\n                $(window).unbind('popstate.kendo');\n            },\n            normalizeCurrent: function (options) {\n                var fixedUrl, root = options.root, pathname = location.pathname, hash = locationHash(hashDelimiter(options.hashBang));\n                if (root === pathname + '/') {\n                    fixedUrl = root;\n                }\n                if (root === pathname && hash) {\n                    fixedUrl = absoluteURL(hash.replace(hashStrip, ''), root);\n                }\n                if (fixedUrl) {\n                    history.pushState({}, document.title, fixedUrl);\n                }\n            }\n        });\n        function fixHash(url) {\n            return url.replace(/^(#)?/, '#');\n        }\n        function fixBang(url) {\n            return url.replace(/^(#(!)?)?/, '#!');\n        }\n        var HashAdapter = HistoryAdapter.extend({\n            init: function (bang) {\n                this._id = kendo.guid();\n                this.prefix = hashDelimiter(bang);\n                this.fix = bang ? fixBang : fixHash;\n            },\n            navigate: function (to) {\n                location.hash = this.fix(to);\n            },\n            replace: function (to) {\n                this.replaceLocation(this.fix(to));\n            },\n            normalize: function (url) {\n                if (url.indexOf(this.prefix) < 0) {\n                    return url;\n                } else {\n                    return url.split(this.prefix)[1];\n                }\n            },\n            change: function (callback) {\n                if (support.hashChange) {\n                    $(window).on('hashchange.' + this._id, callback);\n                } else {\n                    this._interval = setInterval(callback, CHECK_URL_INTERVAL);\n                }\n            },\n            stop: function () {\n                $(window).off('hashchange.' + this._id);\n                clearInterval(this._interval);\n            },\n            current: function () {\n                return locationHash(this.prefix);\n            },\n            normalizeCurrent: function (options) {\n                var pathname = location.pathname, root = options.root;\n                if (options.pushState && root !== pathname) {\n                    this.replaceLocation(root + this.prefix + stripRoot(root, pathname));\n                    return true;\n                }\n                return false;\n            }\n        });\n        var History = kendo.Observable.extend({\n            start: function (options) {\n                options = options || {};\n                this.bind([\n                    CHANGE,\n                    BACK,\n                    SAME\n                ], options);\n                if (this._started) {\n                    return;\n                }\n                this._started = true;\n                options.root = options.root || '/';\n                var adapter = this.createAdapter(options), current;\n                if (adapter.normalizeCurrent(options)) {\n                    return;\n                }\n                current = adapter.current();\n                $.extend(this, {\n                    adapter: adapter,\n                    root: options.root,\n                    historyLength: adapter.length(),\n                    current: current,\n                    locations: [current]\n                });\n                adapter.change($.proxy(this, '_checkUrl'));\n            },\n            createAdapter: function (options) {\n                return support.pushState && options.pushState ? new PushStateAdapter(options.root) : new HashAdapter(options.hashBang);\n            },\n            stop: function () {\n                if (!this._started) {\n                    return;\n                }\n                this.adapter.stop();\n                this.unbind(CHANGE);\n                this._started = false;\n            },\n            change: function (callback) {\n                this.bind(CHANGE, callback);\n            },\n            replace: function (to, silent) {\n                this._navigate(to, silent, function (adapter) {\n                    adapter.replace(to);\n                    this.locations[this.locations.length - 1] = this.current;\n                });\n            },\n            navigate: function (to, silent) {\n                if (to === '#:back') {\n                    this.backCalled = true;\n                    this.adapter.back();\n                    return;\n                }\n                this._navigate(to, silent, function (adapter) {\n                    adapter.navigate(to);\n                    this.locations.push(this.current);\n                });\n            },\n            _navigate: function (to, silent, callback) {\n                var adapter = this.adapter;\n                to = adapter.normalize(to);\n                if (this.current === to || this.current === decodeURIComponent(to)) {\n                    this.trigger(SAME);\n                    return;\n                }\n                if (!silent) {\n                    if (this.trigger(CHANGE, {\n                            url: to,\n                            decode: false\n                        })) {\n                        return;\n                    }\n                }\n                this.current = to;\n                callback.call(this, adapter);\n                this.historyLength = adapter.length();\n            },\n            _checkUrl: function () {\n                var adapter = this.adapter, current = adapter.current(), newLength = adapter.length(), navigatingInExisting = this.historyLength === newLength, back = current === this.locations[this.locations.length - 2] && navigatingInExisting, backCalled = this.backCalled, prev = this.current;\n                if (current === null || this.current === current || this.current === decodeURIComponent(current)) {\n                    return true;\n                }\n                this.historyLength = newLength;\n                this.backCalled = false;\n                this.current = current;\n                if (back && this.trigger('back', {\n                        url: prev,\n                        to: current\n                    })) {\n                    adapter.forward();\n                    this.current = prev;\n                    return;\n                }\n                if (this.trigger(CHANGE, {\n                        url: current,\n                        backButtonPressed: !backCalled\n                    })) {\n                    if (back) {\n                        adapter.forward();\n                    } else {\n                        adapter.back();\n                        this.historyLength--;\n                    }\n                    this.current = prev;\n                    return;\n                }\n                if (back) {\n                    this.locations.pop();\n                } else {\n                    this.locations.push(current);\n                }\n            }\n        });\n        kendo.History = History;\n        kendo.History.HistoryAdapter = HistoryAdapter;\n        kendo.History.HashAdapter = HashAdapter;\n        kendo.History.PushStateAdapter = PushStateAdapter;\n        kendo.absoluteURL = absoluteURL;\n        kendo.history = new History();\n    }(window.kendo.jQuery));\n    (function () {\n        var kendo = window.kendo, history = kendo.history, Observable = kendo.Observable, INIT = 'init', ROUTE_MISSING = 'routeMissing', CHANGE = 'change', BACK = 'back', SAME = 'same', optionalParam = /\\((.*?)\\)/g, namedParam = /(\\(\\?)?:\\w+/g, splatParam = /\\*\\w+/g, escapeRegExp = /[\\-{}\\[\\]+?.,\\\\\\^$|#\\s]/g;\n        function namedParamReplace(match, optional) {\n            return optional ? match : '([^/]+)';\n        }\n        function routeToRegExp(route, ignoreCase) {\n            return new RegExp('^' + route.replace(escapeRegExp, '\\\\$&').replace(optionalParam, '(?:$1)?').replace(namedParam, namedParamReplace).replace(splatParam, '(.*?)') + '$', ignoreCase ? 'i' : '');\n        }\n        function stripUrl(url) {\n            return url.replace(/(\\?.*)|(#.*)/g, '');\n        }\n        var Route = kendo.Class.extend({\n            init: function (route, callback, ignoreCase) {\n                if (!(route instanceof RegExp)) {\n                    route = routeToRegExp(route, ignoreCase);\n                }\n                this.route = route;\n                this._callback = callback;\n            },\n            callback: function (url, back, decode) {\n                var params, idx = 0, length, queryStringParams = kendo.parseQueryStringParams(url);\n                queryStringParams._back = back;\n                url = stripUrl(url);\n                params = this.route.exec(url).slice(1);\n                length = params.length;\n                if (decode) {\n                    for (; idx < length; idx++) {\n                        if (typeof params[idx] !== 'undefined') {\n                            params[idx] = decodeURIComponent(params[idx]);\n                        }\n                    }\n                }\n                params.push(queryStringParams);\n                this._callback.apply(null, params);\n            },\n            worksWith: function (url, back, decode) {\n                if (this.route.test(stripUrl(url))) {\n                    this.callback(url, back, decode);\n                    return true;\n                } else {\n                    return false;\n                }\n            }\n        });\n        var Router = Observable.extend({\n            init: function (options) {\n                if (!options) {\n                    options = {};\n                }\n                Observable.fn.init.call(this);\n                this.routes = [];\n                this.pushState = options.pushState;\n                this.hashBang = options.hashBang;\n                this.root = options.root;\n                this.ignoreCase = options.ignoreCase !== false;\n                this.bind([\n                    INIT,\n                    ROUTE_MISSING,\n                    CHANGE,\n                    SAME,\n                    BACK\n                ], options);\n            },\n            destroy: function () {\n                history.unbind(CHANGE, this._urlChangedProxy);\n                history.unbind(SAME, this._sameProxy);\n                history.unbind(BACK, this._backProxy);\n                this.unbind();\n            },\n            start: function () {\n                var that = this, sameProxy = function () {\n                        that._same();\n                    }, backProxy = function (e) {\n                        that._back(e);\n                    }, urlChangedProxy = function (e) {\n                        that._urlChanged(e);\n                    };\n                history.start({\n                    same: sameProxy,\n                    change: urlChangedProxy,\n                    back: backProxy,\n                    pushState: that.pushState,\n                    hashBang: that.hashBang,\n                    root: that.root\n                });\n                var initEventObject = {\n                    url: history.current || '/',\n                    preventDefault: $.noop\n                };\n                if (!that.trigger(INIT, initEventObject)) {\n                    that._urlChanged(initEventObject);\n                }\n                this._urlChangedProxy = urlChangedProxy;\n                this._backProxy = backProxy;\n            },\n            route: function (route, callback) {\n                this.routes.push(new Route(route, callback, this.ignoreCase));\n            },\n            navigate: function (url, silent) {\n                kendo.history.navigate(url, silent);\n            },\n            replace: function (url, silent) {\n                kendo.history.replace(url, silent);\n            },\n            _back: function (e) {\n                if (this.trigger(BACK, {\n                        url: e.url,\n                        to: e.to\n                    })) {\n                    e.preventDefault();\n                }\n            },\n            _same: function () {\n                this.trigger(SAME);\n            },\n            _urlChanged: function (e) {\n                var url = e.url;\n                var decode = !!e.decode;\n                var back = e.backButtonPressed;\n                if (!url) {\n                    url = '/';\n                }\n                if (this.trigger(CHANGE, {\n                        url: e.url,\n                        params: kendo.parseQueryStringParams(e.url),\n                        backButtonPressed: back\n                    })) {\n                    e.preventDefault();\n                    return;\n                }\n                var idx = 0, routes = this.routes, route, length = routes.length;\n                for (; idx < length; idx++) {\n                    route = routes[idx];\n                    if (route.worksWith(url, back, decode)) {\n                        return;\n                    }\n                }\n                if (this.trigger(ROUTE_MISSING, {\n                        url: url,\n                        params: kendo.parseQueryStringParams(url),\n                        backButtonPressed: back\n                    })) {\n                    e.preventDefault();\n                }\n            }\n        });\n        kendo.Router = Router;\n    }());\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}