{"version": 3, "sources": ["kendo.data.signalr.js"], "names": ["f", "define", "$", "isJQueryPromise", "promise", "isFunction", "done", "fail", "isNativePromise", "then", "kendo", "window", "transport", "data", "RemoteTransport", "extend", "init", "options", "hub", "signalr", "Error", "this", "on", "invoke", "fn", "call", "push", "callbacks", "client", "create", "pushCreate", "update", "pushUpdate", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "_crud", "type", "args", "server", "format", "parameterMap", "isEmptyObject", "apply", "success", "error", "read", "transports", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,sBAAuB,cAAeD,IAC/C,WAuFE,MA/EC,UAAUE,GAGP,QAASC,GAAgBC,GACrB,MAAOA,IAAWC,EAAWD,EAAQE,OAASD,EAAWD,EAAQG,MAErE,QAASC,GAAgBJ,GACrB,MAAOA,IAAWC,EAAWD,EAAQK,OAASJ,EAAWD,EAAAA,UAPhE,GACOM,GAAQC,OAAOD,MACfL,EAAaK,EAAML,WAOnBO,EAAYF,EAAMG,KAAKC,gBAAgBC,QACvCC,KAAM,SAAUC,GAAV,GAUEC,GATAC,EAAUF,GAAWA,EAAQE,QAAUF,EAAQE,WAC/Cf,EAAUe,EAAQf,OACtB,KAAKA,EACD,KAAUgB,OAAM,oCAEpB,KAAKjB,EAAgBC,KAAaI,EAAgBJ,GAC9C,KAAUgB,OAAM,0CAIpB,IAFAC,KAAKjB,QAAUA,EACXc,EAAMC,EAAQD,KACbA,EACD,KAAUE,OAAM,gCAEpB,IAAqB,kBAAVF,GAAII,IAAyC,kBAAdJ,GAAIK,OAC1C,KAAUH,OAAM,qDAEpBC,MAAKH,IAAMA,EACXR,EAAMG,KAAKC,gBAAgBU,GAAGR,KAAKS,KAAKJ,KAAMJ,IAElDS,KAAM,SAAUC,GACZ,GAAIC,GAASP,KAAKJ,QAAQE,QAAQS,UAC9BA,GAAOC,QACPR,KAAKH,IAAII,GAAGM,EAAOC,OAAQF,EAAUG,YAErCF,EAAOG,QACPV,KAAKH,IAAII,GAAGM,EAAOG,OAAQJ,EAAUK,YAErCJ,EAAOK,SACPZ,KAAKH,IAAII,GAAGM,EAAOK,QAASN,EAAUO,cAG9CC,MAAO,SAAUlB,EAASmB,GAAnB,GAOCC,GACAxB,EAPAK,EAAMG,KAAKH,IACXd,EAAUiB,KAAKjB,QACfkC,EAASjB,KAAKJ,QAAQE,QAAQmB,MAClC,KAAKA,IAAWA,EAAOF,GACnB,KAAUhB,OAAMV,EAAM6B,OAAO,uCAAwCH,GAErEC,IAAQC,EAAOF,IACfvB,EAAOQ,KAAKmB,aAAavB,EAAQJ,KAAMuB,GACtClC,EAAEuC,cAAc5B,IACjBwB,EAAKX,KAAKb,GAEVV,EAAgBC,GAChBA,EAAQE,KAAK,WACTY,EAAIK,OAAOmB,MAAMxB,EAAKmB,GAAM/B,KAAKW,EAAQ0B,SAASpC,KAAKU,EAAQ2B,SAE5DpC,EAAgBJ,IACvBA,EAAQK,KAAK,WACTS,EAAIK,OAAOmB,MAAMxB,EAAKmB,GAAM5B,KAAKQ,EAAQ0B,SAAzCzB,SAAwDD,EAAQ2B,UAI5EC,KAAM,SAAU5B,GACZI,KAAKc,MAAMlB,EAAS,SAExBY,OAAQ,SAAUZ,GACdI,KAAKc,MAAMlB,EAAS,WAExBc,OAAQ,SAAUd,GACdI,KAAKc,MAAMlB,EAAS,WAExBgB,QAAS,SAAUhB,GACfI,KAAKc,MAAMlB,EAAS,aAG5Bf,GAAEa,QAAO,EAAML,EAAMG,MAAQiC,YAAc3B,QAASP,MACtDD,OAAOD,MAAMqC,QACRpC,OAAOD,OACE,kBAAVT,SAAwBA,OAAO+C,IAAM/C,OAAS,SAAUgD,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.data.signalr.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.data.signalr', ['kendo.data'], f);\n}(function () {\n    var __meta__ = {\n        id: 'data.signalr',\n        name: 'SignalR',\n        category: 'framework',\n        depends: ['data'],\n        hidden: true\n    };\n    (function ($) {\n        var kendo = window.kendo;\n        var isFunction = kendo.isFunction;\n        function isJQueryPromise(promise) {\n            return promise && isFunction(promise.done) && isFunction(promise.fail);\n        }\n        function isNativePromise(promise) {\n            return promise && isFunction(promise.then) && isFunction(promise.catch);\n        }\n        var transport = kendo.data.RemoteTransport.extend({\n            init: function (options) {\n                var signalr = options && options.signalr ? options.signalr : {};\n                var promise = signalr.promise;\n                if (!promise) {\n                    throw new Error('The \"promise\" option must be set.');\n                }\n                if (!isJQueryPromise(promise) && !isNativePromise(promise)) {\n                    throw new Error('The \"promise\" option must be a Promise.');\n                }\n                this.promise = promise;\n                var hub = signalr.hub;\n                if (!hub) {\n                    throw new Error('The \"hub\" option must be set.');\n                }\n                if (typeof hub.on != 'function' || typeof hub.invoke != 'function') {\n                    throw new Error('The \"hub\" option is not a valid SignalR hub proxy.');\n                }\n                this.hub = hub;\n                kendo.data.RemoteTransport.fn.init.call(this, options);\n            },\n            push: function (callbacks) {\n                var client = this.options.signalr.client || {};\n                if (client.create) {\n                    this.hub.on(client.create, callbacks.pushCreate);\n                }\n                if (client.update) {\n                    this.hub.on(client.update, callbacks.pushUpdate);\n                }\n                if (client.destroy) {\n                    this.hub.on(client.destroy, callbacks.pushDestroy);\n                }\n            },\n            _crud: function (options, type) {\n                var hub = this.hub;\n                var promise = this.promise;\n                var server = this.options.signalr.server;\n                if (!server || !server[type]) {\n                    throw new Error(kendo.format('The \"server.{0}\" option must be set.', type));\n                }\n                var args = [server[type]];\n                var data = this.parameterMap(options.data, type);\n                if (!$.isEmptyObject(data)) {\n                    args.push(data);\n                }\n                if (isJQueryPromise(promise)) {\n                    promise.done(function () {\n                        hub.invoke.apply(hub, args).done(options.success).fail(options.error);\n                    });\n                } else if (isNativePromise(promise)) {\n                    promise.then(function () {\n                        hub.invoke.apply(hub, args).then(options.success).catch(options.error);\n                    });\n                }\n            },\n            read: function (options) {\n                this._crud(options, 'read');\n            },\n            create: function (options) {\n                this._crud(options, 'create');\n            },\n            update: function (options) {\n                this._crud(options, 'update');\n            },\n            destroy: function (options) {\n                this._crud(options, 'destroy');\n            }\n        });\n        $.extend(true, kendo.data, { transports: { signalr: transport } });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}