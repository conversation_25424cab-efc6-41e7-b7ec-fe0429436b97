/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.scheduler.agendaview.min",["kendo.scheduler.view.min"],e)}(function(){return function(e){function t(e){var t,s,a=0;for(t=0,s=e.length;t<s;t++)a+=e[t].items.length;return a}function s(e,t){return e.valuePrimitive&&(t=r.getter(e.dataValueField)(t)),t}function a(e){for(var t,s=0,a=e.length,r=[];s<a;s++)t=e[s],t.groups?(t=n(t.groups),r=r.concat(t)):r=r.concat(n(t.items));return r}function n(e){for(var t=[].concat(e),s=t.shift(),a=[],n=[].push;s;)s.groups?n.apply(t,s.groups):s.items?n.apply(t,s.items):n.call(a,s),s=t.shift();return a}var r=window.kendo,i=r.ui,o=".kendoAgendaView",l='<div class="k-task" title="#:(data.title || "").replace(/"/g,"\'")#" data-#=kendo.ns#uid="#=uid#"># if (resources[0]) {#<span class="k-scheduler-mark" style="background-color:#=resources[0].color#"></span># } ## if (data.isException()) { #<span class="k-icon k-i-non-recurrence"></span># } else if (data.isRecurring()) {#<span class="k-icon k-i-reload"></span># } #<span class="k-scheduler-task-text">{0}</span>#if (showDelete) {#<a href="\\#" class="k-link k-event-delete" title="${data.messages.destroy}" aria-label="${data.messages.destroy}"><span class="k-icon k-i-close"></span></a>#}#</div>',d='# if (!isMobile) { #<strong class="k-scheduler-agendaday">#=kendo.toString(date, "dd")#</strong><em class="k-scheduler-agendaweek">#=kendo.toString(date,"dddd")#</em><span class="k-scheduler-agendadate">#=kendo.toString(date, "y")#</span># } else { #<div class="k-scheduler-datecolumn-wrap"><span class="k-mobile-scheduler-agendadate"><span class="k-mobile-scheduler-agendaday">#=kendo.toString(date, "dd")#</span>&nbsp<span class="k-mobile-scheduler-agendamonth">#=kendo.toString(date, "MMMM")#</span></span><span class="k-mobile-scheduler-agendaweekday">#=kendo.toString(date, "dddd")#</span></div># } #',u='# if (!isMobile) { #<strong class="k-scheduler-adgendagroup">#=value#</strong># } else { #<span class="k-scheduler-group-text">#=value#</span># } #',c=r.Class.extend({init:function(e){this._view=e},_getColumns:function(e,t){return e.concat(t)},_getGroupsInDay:function(){return[]},_getSumOfItemsForDate:function(){return 0},_renderTaskGroupsCells:function(e,t,s,a){var n=this._view;0===s&&0===a&&t.length&&n._renderTaskGroupsCells(e,t)},_renderDateCell:function(e,t,s,a,n,i){var o=this._view,l=o._isMobile();e.push(r.format('<td class="k-scheduler-datecolumn{3}{2}" rowspan="{0}">{1}</td>',s.length,o._dateTemplate({date:a,isMobile:l}),n!=i.length-1||t.length?"":" k-last",t.length?"":" k-first"))},_renderDates:function(){},_getParents:function(e){return e.splice(0)},_getGroupsByDate:function(){},_renderTaskGroups:function(e,t,s){var a=this._view;e.append(a._renderTaskGroups(t,s))}}),h=r.Class.extend({init:function(e){this._view=e},_getColumns:function(e,t){var s,a,n=this._view;return n._isMobile()?e.concat(t):(s=t.slice(0,1),a=t.slice(1),s.concat(e).concat(a))},_compareDateGroups:function(e,t,s){return e[s].text==t[s].text&&(0===s||this._compareDateGroups(e,t,s-1))},_getGroupsInDay:function(e,t){var s,a,n,r,i,o=[],l=null;for(s=0;s<e.length;s++)for(a=0;a<e[s].items.length;a++){if(n=0,0===o.length)for(n;n<t[s].length;n++)o.push([1]);else for(n;n<t[s].length;n++)if(this._compareDateGroups(t[s],l,n))o[n][o[n].length-1]++;else{for(r=o[n][o[n].length-1]-1,i=0;i<r;i++)o[n].push(0);o[n].push(1)}l=t[s]}return o},_getSumOfItemsForDate:function(e){var t,s=0;for(t=0;t<e.length;t++)s+=e[t].items.length;return s},_renderTaskGroupsCells:function(e,t,s,a,n,i,o,l){var d,u=this._view,c=u._isMobile();if(c)0===s&&0===a&&t.length&&u._renderTaskGroupsCells(e,t);else for(0===s&&0===a&&e.push(r.format('<td class="k-scheduler-datecolumn k-first" rowspan="{0}">{1}</td>',i,u._dateTemplate({date:o,isMobile:c}))),d=0;d<t[s].length;d++)n[d][l]&&e.push(r.format('<td class="k-scheduler-groupcolumn" rowspan="{0}">{1}</td>',n[d][l],u._groupTemplate({value:t[s][d].text,isMobile:c}),t[s][d].className))},_renderDateCell:function(){},_renderDates:function(e){var t,s=this._view,a=s._groupsByDate.sort(function(e,t){return e.array[0].value.getTime()-t.array[0].value.getTime()});for(t=0;t<a.length;t++)e.append(s._renderTaskGroups(a[t].array,a[t].groups))},_getParents:function(e){return e.slice(0)},_getGroupsByDate:function(e,t,s){var a,n,r,i,o=this._view;if(e[t].items)for(a=0;a<e[t].items.length;a++){for(n=e[t].items[a].value,r=!1,i=0;i<o._groupsByDate.length;i++)o._groupsByDate[i].array[0].value.getTime()===n.getTime()&&(r=!0,o._groupsByDate[i].array.push(e[t].items[a]),o._groupsByDate[i].groups.push(s));r||o._groupsByDate.push({array:[e[t].items[a]],groups:[s]})}},_renderTaskGroups:function(){}});r.ui.scheduler.AgendaGroupedView=c,r.ui.scheduler.AgendaGroupedByDateView=h,i.AgendaView=i.SchedulerView.extend({init:function(t,s){i.SchedulerView.fn.init.call(this,t,s),this._groupedView=this._getGroupedView(),s=this.options,s.editable&&(s.editable=e.extend({"delete":!0},s.editable,{create:!1,update:!1},{messages:s.messages})),this.title=s.title,this._eventTemplate=this._eventTmpl(s.eventTemplate,l),this._dateTemplate=r.template(s.eventDateTemplate),this._groupTemplate=r.template(s.eventGroupTemplate),this._timeTemplate=r.template(s.eventTimeTemplate),this.element.on("mouseenter"+o,".k-scheduler-agenda .k-scheduler-content tr","_mouseenter").on("mouseleave"+o,".k-scheduler-agenda .k-scheduler-content tr","_mouseleave").on("click"+o,".k-scheduler-agenda .k-scheduler-content .k-link:has(.k-i-close)","_remove"),this._renderLayout(s.date)},name:"agenda",_getGroupedView:function(){return this._isGroupedByDate()?new r.ui.scheduler.AgendaGroupedByDateView(this):new r.ui.scheduler.AgendaGroupedView(this)},_mouseenter:function(t){e(t.currentTarget).addClass("k-state-hover")},_mouseleave:function(t){e(t.currentTarget).removeClass("k-state-hover")},_remove:function(t){t.preventDefault(),this.trigger("remove",{uid:e(t.currentTarget).closest(".k-task").attr(r.attr("uid"))})},nextDate:function(){return r.date.nextDay(this.startDate())},startDate:function(){return this._startDate},endDate:function(){return this._endDate},previousDate:function(){return r.date.previousDay(this.startDate())},_renderLayout:function(e){this._startDate=e,this._endDate=r.date.addDays(e,7),this.createLayout(this._layout()),this._footer(),this.table.addClass("k-scheduler-agenda")},_layout:function(){var e,t,s,a=[{text:this.options.messages.time,className:"k-scheduler-timecolumn"},{text:this.options.messages.event}];if(this._isMobile()||a.splice(0,0,{text:this.options.messages.date,className:"k-scheduler-datecolumn"}),e=this.groupedResources,e.length){for(t=[],s=0;s<e.length;s++)t.push({text:"",className:"k-scheduler-groupcolumn"});a=this._groupedView._getColumns(t,a)}return{columns:a}},_tasks:function(e){var t,s,a,n,i,o,l,d=[];for(t=0;t<e.length;t++)if(s=e[t],a=s.start,n=s.isAllDay?r.date.getDate(s.end):s.end,i=Math.ceil((n-r.date.getDate(a))/r.date.MS_PER_DAY),s.isAllDay&&(i+=1),o=s.clone(),o.startDate=r.date.getDate(a),o.startDate>=this.startDate()&&d.push(o),i>1)for(o.end=r.date.nextDay(a),o.head=!0,l=1;l<i;l++)a=o.end,o=s.clone(),o.start=o.startDate=r.date.getDate(a),o.end=r.date.nextDay(a),l==i-1?(o.end=new Date(o.start.getFullYear(),o.start.getMonth(),o.start.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()),o.tail=!0):(o.isAllDay=!0,o.middle=!0),(r.date.getDate(o.end)<=this.endDate()&&o.start>=this.startDate()||r.date.getDate(o.start).getTime()==this.endDate().getTime())&&d.push(o);return new r.data.Query(d).sort([{field:"start",dir:"asc"},{field:"end",dir:"asc"}]).groupBy({field:"startDate"}).toArray()},_renderTaskGroups:function(e,t){var s,a,n,i,o,l,d,u,c=[],h=this.options.editable,p=h&&h.destroy!==!1&&!this._isMobile(),g=this._isMobile(),m=this._groupedView._getSumOfItemsForDate(e),f=this._groupedView._getGroupsInDay(e,t),_=0;for(s=0;s<e.length;s++)for(a=e[s].value,n=e[s].items,i=r.date.isToday(a),o=0;o<n.length;o++)l=n[o],d=[],u=g?[]:d,this._groupedView._renderTaskGroupsCells(u,t,s,o,f,m,a,_),_++,0===o&&(g?(u.push(r.format('<td class="k-scheduler-datecolumn {1}" colspan="2">{0}</td>',this._dateTemplate({date:a,isMobile:g}),this.groupedResources.length?"":"k-first")),c.push('<tr role="row" aria-selected="false"'+(i?' class="k-today">':">")+u.join("")+"</tr>")):this._groupedView._renderDateCell(d,t,n,a,s,e)),l.format=l.head?"{0:t}":l.tail?"{1:t}":"{0:t}-{1:t}",l.resources=this.eventResources(l),d.push(r.format('<td class="k-scheduler-timecolumn {4}"><div>{0}{1}{2}</div></td><td>{3}</td>',l.tail||l.middle?'<span class="k-icon k-i-arrow-60-left"></span>':"",this._timeTemplate(l.clone({start:l._startTime||l.start,end:l.endTime||l.end})),l.head||l.middle?'<span class="k-icon k-i-arrow-60-right"></span>':"",this._eventTemplate(l.clone({showDelete:p,messages:this.options.messages})),!this.groupedResources.length&&g?"k-first":"")),c.push('<tr role="row" aria-selected="false"'+(i?' class="k-today">':">")+d.join("")+"</tr>");return c.join("")},_renderTaskGroupsCells:function(e,t){var s,a=this._isMobile();for(s=0;s<t.length;s++)e.push(r.format('<td class="k-scheduler-groupcolumn{2}" rowspan="{0}">{1}</td>',t[s].rowSpan,this._groupTemplate({value:t[s].text,isMobile:a}),t[s].className))},render:function(e){var t,s,n=this.content.find("table").empty(),r=[];e.length>0&&(t=this.groupedResources,t.length?(r=this._createGroupConfiguration(e,t,null),this._groupsByDate=[],this._renderGroups(r,n,[]),this._groupedView._renderDates(n)):(r=this._tasks(e),n.append(this._renderTaskGroups(r,[])))),s=this._eventsList=a(r),this._angularItems(n,s),this.refreshLayout(),this.trigger("activate")},_angularItems:function(e,t){this.angular("compile",function(){var s=[],a=t.map(function(t){return s.push({dataItem:t}),e.find(".k-task["+r.attr("uid")+"="+t.uid+"]")});return{elements:a,data:s}})},_renderGroups:function(e,t,s){var a,n,r;for(a=0,n=e.length;a<n;a++)r=this._groupedView._getParents(s),r.push(e[a]),this._groupedView._getGroupsByDate(e,a,r),e[a].groups?this._renderGroups(e[a].groups,t,r):this._groupedView._renderTaskGroups(t,e[a].items,r)},_createGroupConfiguration:function(e,a,n){var o,l,d,u,c,h,p,g=a[0],m=[],f=g.dataSource.view(),_=this._isMobile();for(o=0;o<f.length;o++)l=s(g,f[o]),d=new r.data.Query(e).filter({field:g.field,operator:i.SchedulerView.groupEqFilter(l)}).toArray(),d.length&&(u=this._tasks(d),c=n?"":" k-first",o===f.length-1&&(!n||n.className.indexOf("k-last")>-1)&&(c+=" k-last"),h={text:r.getter(g.dataTextField)(f[o]),value:l,rowSpan:0,className:c},a.length>1?(h.groups=this._createGroupConfiguration(d,a.slice(1),h),n&&(n.rowSpan+=h.rowSpan)):(h.items=u,p=t(h.items),_&&(p+=h.items.length),h.rowSpan=p,n&&(n.rowSpan+=p)),m.push(h));return m},selectionByElement:function(t){var s,a,n;if(t=e(t),!t.hasClass("k-scheduler-datecolumn")&&this._eventsList.length)return t.is(".k-task")&&(t=t.closest("td")),this._isMobile()?(n=t.parent(),s=n.parent().children().filter(function(){return e(this).children(":not(.k-scheduler-datecolumn)").length}).index(n)):s=t.parent().index(),a=this._eventsList[s],{index:s,start:a.start,end:a.end,isAllDay:a.isAllDay,uid:a.uid}},select:function(e){this.clearSelection();var t=this.table.find(".k-task").eq(e.index).closest("tr").addClass("k-state-selected").attr("aria-selected",!0)[0];this.current(t)},move:function(e,t){var s,a=!1,n=e.index;return t==r.keys.UP?(n--,a=!0):t==r.keys.DOWN&&(n++,a=!0),a&&(s=this._eventsList[n],s&&(e.start=s.start,e.end=s.end,e.isAllDay=s.isAllDay,e.events=[s.uid],e.index=n)),a},moveToEvent:function(){return!1},constrainSelection:function(e){var t=this._eventsList[0];t&&(e.start=t.start,e.end=t.end,e.isAllDay=t.isAllDay,e.events=[t.uid],e.index=0)},isInRange:function(){return!0},destroy:function(){this.element&&this.element.off(o),i.SchedulerView.fn.destroy.call(this)},options:{title:"Agenda",name:"agenda",editable:!0,selectedDateFormat:"{0:D}-{1:D}",selectedShortDateFormat:"{0:d} - {1:d}",selectedMobileDateFormat:"{0: MMM} {0:dd} - {1:dd}",eventTemplate:"#:title#",eventTimeTemplate:"#if(data.isAllDay) {##=this.options.messages.allDay##} else { ##=kendo.format(format, start, end)## } #",eventDateTemplate:d,eventGroupTemplate:u,messages:{event:"Event",date:"Date",time:"Time",allDay:"all day"}}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,s){(s||t)()});
//# sourceMappingURL=kendo.scheduler.agendaview.min.js.map
