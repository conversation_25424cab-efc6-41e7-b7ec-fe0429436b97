{"version": 3, "sources": ["kendo.dropdowntree.js"], "names": ["f", "define", "$", "undefined", "contentChild", "filter", "node", "result", "children", "length", "kendo", "window", "ui", "keys", "DISABLED", "SELECT", "CHECKED", "proxy", "DATABOUND", "CLICK", "NS", "INDETERMINATE", "NAVIGATE", "TreeView", "subGroup", "Tree", "extend", "init", "element", "options", "dropdowntree", "that", "this", "fn", "call", "_isMultipleSelection", "wrapper", "on", "_clickSelectedItem", "_checkOnSelect", "e", "isDefaultPrevented", "dataItem", "set", "checked", "_setCheckedValue", "value", "_click", "one", "currentTarget", "_trigger", "defaultrefresh", "i", "bubble", "action", "items", "parentNode", "loadOnDemand", "check<PERSON><PERSON><PERSON><PERSON>", "checkboxes", "_skip", "field", "level", "_updateNodes", "findByUid", "uid", "_progress", "_appendItems", "index", "_remove", "_refresh<PERSON><PERSON><PERSON>n", "_refreshRoot", "expanded", "load", "trigger", "_treeViewDataBound", "sender", "updateIndeterminate", "_previousVisible", "<PERSON><PERSON><PERSON><PERSON>", "prev", "root", "last", "_expanded", "parent", "checkAll", "is", "find", "focus", "filterInput", "_keydown", "target", "key", "keyCode", "focused", "current", "checkbox", "rtl", "support", "isRtl", "RIGHT", "LEFT", "_nextVisible", "hasClass", "expand", "collapse", "_enabled", "DOWN", "UP", "altKey", "HOME", "END", "ENTER", "SPACEBAR", "prop", "data", "_checkboxChange", "ESC", "_closePopup", "select", "preventDefault", "close", "refresh", "skipUpdateOnBind", "_checkValue", "selected", "_selectValue", "_dropdowntree", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "assign", "instance", "fields", "idx", "lastIndex", "SingleSelection", "MultipleSelection", "Widget", "ObservableArray", "ObservableObject", "activeElement", "_activeElement", "ns", "HIDDENCLASS", "WIDTH", "browser", "outerWidth", "_outerWidth", "DOT", "READONLY", "STATEDISABLED", "ARIA_DISABLED", "HOVER", "FOCUSED", "HOVEREVENTS", "TABINDEX", "OPEN", "CLOSE", "CHANGE", "quotRegExp", "DropDownTree", "text", "disabled", "_selection", "_getSelection", "_focusInputHandler", "_focusInput", "_initial", "val", "_values", "_noInitialValue", "_isNullorUndefined", "_valueMethodCalled", "isArray", "slice", "_inputTemplate", "_accessors", "_setTreeViewOptions", "_dataSource", "_initWrapper", "_placeholder", "_tabindex", "attr", "tree", "tabIndex", "aria-hidden", "list", "append", "_header", "_noData", "_footer", "_reset", "_popup", "popup", "_popupOpen", "_clearButton", "_filterHeader", "_treeview", "_renderFooter", "_checkAll", "_enable", "_toggleCloseVisibility", "autoBind", "_textAccessor", "placeholder", "_preselect", "parents", "enable", "notify", "treeviewOptions", "dataImageUrlField", "dataSpriteCssClassField", "dataTextField", "dataUrlField", "treeview", "template", "rootDataSource", "dataSource", "HierarchicalDataSource", "create", "wrap", "setDataSource", "name", "animation", "autoClose", "autoWidth", "clearButton", "dataValueField", "delay", "enabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "height", "ignoreCase", "messages", "singleTag", "clear", "deleteTag", "<PERSON><PERSON><PERSON><PERSON>", "noDataTemplate", "checkAllTemplate", "tagMode", "valuePrimitive", "footerTemplate", "headerTemplate", "valueTemplate", "events", "readonly", "_editable", "disable", "toggle", "open", "_toggle", "_isFilterEnabled", "_search", "fetch", "visible", "_allowOpening", "addClass", "_hovered", "search", "word", "clearTimeout", "_typingTimeout", "_getFilter", "_filtering", "operator", "flatView", "_renderNoData", "noData", "setOptions", "css", "span", "destroy", "off", "_clear", "_inputWrapper", "tagList", "unbind", "_form", "_reset<PERSON><PERSON><PERSON>", "setValue", "dataItems", "_filter", "_setValue", "loweredText", "toLowerCase", "_selectItemByText", "header", "_angularElement", "remove", "prepend", "angular", "elements", "appendTo", "html", "footer", "_adjustListWidth", "computedStyle", "computedWidth", "width", "style", "getComputedStyle", "parseFloat", "msie", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "fontFamily", "min<PERSON><PERSON><PERSON>", "whiteSpace", "formId", "form", "closest", "setTimeout", "Popup", "anchor", "_open<PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON>", "autosize", "show", "toggleClass", "_currentValue", "currentValue", "_value", "alreadyAddedTag", "itemToAdd", "itemToRemove", "indexOfValue", "isMultiple", "_level", "indexOf", "grep", "_tags", "item", "_tagUid", "toJSON", "push", "hide", "_multipleTags", "_treeViewCheckAllCheck", "splice", "_preventChangeTrigger", "position", "_updateSelectedOptions", "selectedItems", "tagName", "_option", "_text", "dataValue", "dataText", "option", "replace", "htmlEncode", "_accessor", "_clearClick", "stopPropagation", "_clearTextAndValue", "_clearInput", "_clearText", "_clearValue", "useWithBlock", "_assignInstance", "split", "getElements", "removeClass", "isPlainObject", "get", "getter", "textField", "valueField", "getterFunction", "count", "levels", "map", "x", "d", "Math", "min", "_accessorInput", "clearTitle", "role", "insertAfter", "isReadOnly", "hasValue", "valueDoesNotEqualPlaceHolder", "_hideClear", "_showClear", "guid", "rootItems", "rootItem", "subItems", "_prev", "_deselectItem", "_checkLoadedItems", "_valueComparer", "_checkLoadedItem", "_getAllChecked", "_checkCheckAll", "_uncheckCheckAll", "checkAllCheckbox", "_allItemsAreChecked", "_allItemsAreUnchecked", "icon", "_disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "aria-haspopup", "aria-expanded", "_filterChange", "insertBefore", "add", "_clickCheckAll", "_changeCheckAll", "_keydownCheckAll", "_disabledCheckedItems", "_disabledUnCheckedItems", "isChecked", "edge", "_updateCheckAll", "_toggleCheckAllItems", "_dfs", "_traverce<PERSON><PERSON><PERSON><PERSON>", "_uncheckItemByUid", "_uncheckItemEqualsUid", "_itemEqualsText", "_selectItemByValue", "_itemEqualsValue", "_checkItemByValue", "_checkItemEqualsValue", "itemText", "newValue", "itemValue", "_allCheckedItems", "_getAllCheckedItems", "childrenField", "_childrenOptions", "schema", "_checkAllCheckItem", "dropDownWrapper", "removeAttr", "_<PERSON>in<PERSON><PERSON><PERSON>", "_focusoutHandler", "_toggleHover", "_wrapperClick", "_removeTagClick", "_prevent", "blur", "_focused", "type", "isFilterInputActive", "isWrapperActive", "tagItem", "isPopupVisible", "_focusPrevTag", "_focusNextTag", "_focusFirstTag", "_focusLastTag", "DELETE", "first", "_removeTag", "BACKSPACE", "_clearFilter", "version", "activedescendant", "next", "firstTag", "_clearDisabledTag", "lastTag", "parentElement", "plugin", "Class", "view", "_wrapper", "_span", "DOMelement", "cssText", "className", "accesskey", "unselectable", "SELECTOR", "<PERSON><PERSON><PERSON><PERSON>", "_arrow", "_arrowIcon", "selectedNode", "tempItem", "_tagTemplate", "_tagList", "valueToSelect", "tagTemplate", "aria-activedescendant", "_innerWrapper", "tagCollection", "viewModel", "observable", "multipleTag", "tags", "bind", "oldValues", "_removeValues", "j", "removedV<PERSON><PERSON>", "_get<PERSON><PERSON><PERSON><PERSON><PERSON>", "tagsArray", "_findTag", "tempItemValue"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,yBAA0B,kBAAmBD,IACtD,WAoNE,MAnNC,UAAUE,EAAGC,GAEV,QAASC,GAAaC,GAClB,MAAO,UAAUC,GACb,GAAIC,GAASD,EAAKE,SAAS,yBAI3B,OAHKD,GAAOE,SACRF,EAASD,GAENC,EAAOC,SAASH,IARlC,GACOK,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAAOH,EAAMG,KAAMC,EAAW,mBAAoBC,EAAS,SAAUC,EAAU,UAAWC,EAAQf,EAAEe,MAAOC,EAAY,YAAaC,EAAQ,QAASC,EAAK,iBAAkBC,EAAgB,gBAAiBC,EAAW,WAAsBC,EAAWX,EAAGW,SAAxBC,EAUlQpB,EAAa,YACpBqB,EAAOF,EAASG,QAChBC,KAAM,SAAUC,EAASC,EAASC,GAC9B,GAAIC,GAAOC,IACXD,GAAKD,aAAeA,EACpBP,EAASU,GAAGN,KAAKO,KAAKH,EAAMH,EAASC,GACjCE,EAAKD,aAAaK,wBAClBJ,EAAKK,QAAQC,GAAGlB,EAAQC,EAAI,yBAA0BH,EAAMc,EAAKO,mBAAoBP,KAG7FQ,eAAgB,SAAUC,GACtB,IAAKA,EAAEC,qBAAsB,CACzB,GAAIC,GAAWV,KAAKU,SAASF,EAAElC,KAC/BoC,GAASC,IAAI,WAAYD,EAASE,WAG1CC,iBAAkB,SAAUvC,EAAMwC,GAC9BxC,EAAKqC,IAAI3B,EAAS8B,IAEtBC,OAAQ,SAAUP,GACd,GAAIT,GAAOC,IACPD,GAAKD,aAAaK,wBAClBJ,EAAKiB,IAAI,SAAUjB,EAAKQ,gBAE5BhB,EAASU,GAAGc,OAAOb,KAAKH,EAAMS,IAElCF,mBAAoB,SAAUE,GAC1B,GAAIT,GAAOC,KAAM1B,EAAOJ,EAAEsC,EAAES,cAC5BlB,GAAKiB,IAAI,SAAUjB,EAAKQ,gBACnBR,EAAKmB,SAASnC,EAAQT,IACvByB,EAAKW,SAASpC,GAAMqC,IAAI,YAAY,IAG5CQ,eAAgB,SAAUX,GAAV,GAQRY,GAeIC,EAtBJ/C,EAAOkC,EAAElC,KACTgD,EAASd,EAAEc,OACXC,EAAQf,EAAEe,MACVC,EAAaxB,KAAKI,QAClBP,EAAUG,KAAKH,QACf4B,EAAe5B,EAAQ4B,aACvBC,EAAgB7B,EAAQ8B,YAAc9B,EAAQ8B,WAAWD,aAE7D,KAAI1B,KAAK4B,MAAT,CAGA,GAAIpB,EAAEqB,MAAO,CACT,IAAKN,EAAM,KAAOA,EAAM,GAAGO,MACvB,MAEJ,OAAO9B,MAAK+B,aAAaR,EAAOf,EAAEqB,OAMtC,GAJIvD,IACAkD,EAAaxB,KAAKgC,UAAU1D,EAAK2D,KACjCjC,KAAKkC,UAAUV,GAAY,IAE3BE,GAA2B,UAAVJ,EAAoB,CAErC,IADID,GAAS,EACRD,EAAI,EAAGA,EAAIG,EAAM9C,OAAQ2C,IAC1B,GAAI,WAAaG,GAAMH,GAAI,CACvBC,GAAS,CACT,OAGR,IAAKA,GAAU/C,GAAQA,EAAKsC,QACxB,IAAKQ,EAAI,EAAGA,EAAIG,EAAM9C,OAAQ2C,IAC1BG,EAAMH,GAAGR,SAAU,EAe/B,GAXc,OAAVU,EACAtB,KAAKmC,aAAa3B,EAAE4B,MAAOb,EAAOC,GACjB,UAAVF,EACPtB,KAAKqC,QAAQrC,KAAKgC,UAAUT,EAAM,GAAGU,MAAM,GAC1B,cAAVX,EACPtB,KAAK+B,aAAaR,GACD,cAAVD,EACPtB,KAAKsC,iBAAiBd,EAAYD,EAAOf,EAAE4B,OAE3CpC,KAAKuC,aAAahB,GAER,UAAVD,EACA,IAAKF,EAAI,EAAGA,EAAIG,EAAM9C,OAAQ2C,IACrBK,IAAgBF,EAAMH,GAAGoB,UAC1BjB,EAAMH,GAAGqB,MAIrBzC,MAAK0C,QAAQxD,GAAaZ,KAAMA,EAAOkD,EAAarD,IACpD6B,KAAKF,aAAa6C,oBACdrE,KAAMA,EAAOkD,EAAarD,EAC1ByE,OAAQ5C,OAERA,KAAKH,QAAQ8B,WAAWD,eACxB1B,KAAK6C,wBAGbC,iBAAkB,SAAUxE,GACxB,GAAiByE,GAAWxE,EAAxBwB,EAAOC,IACX,KAAK1B,EAAKG,QAAUH,EAAK0E,OAAOvE,OAM5B,IAJIF,EADAD,EAAKG,OACIH,EAAK0E,OAELjD,EAAKkD,KAAKzE,WAAW0E,OAE3BnD,EAAKoD,UAAU5E,KAClBwE,EAAYvD,EAASjB,GAAQC,WAAW0E,OACnCH,EAAUtE,SAGfF,EAASwE,MAGbxE,GAASwB,EAAKqD,OAAO9E,IAASA,EACzBC,EAAOE,SACJsB,EAAKD,aAAauD,UAAYtD,EAAKD,aAAauD,SAASC,GAAG,YAC5DvD,EAAKD,aAAauD,SAASE,KAAK,eAAeC,QACxCzD,EAAKD,aAAa2D,YACzB1D,EAAKD,aAAa2D,YAAYD,QAE9BzD,EAAKD,aAAaM,QAAQoD,QAItC,OAAOjF,IAEXmF,SAAU,SAAUlD,GAChB,GAAkCmD,GAA9B5D,EAAOC,KAAM4D,EAAMpD,EAAEqD,QAAiBC,EAAU/D,EAAKgE,UAAWvB,EAAWzC,EAAKoD,UAAUW,GAAUE,EAAWF,EAAQP,KAAK,uCAAwCU,EAAMvF,EAAMwF,QAAQC,MAAMpE,EAAKH,QACnMY,GAAEmD,QAAUnD,EAAES,iBAGbgD,GAAOL,GAAO/E,EAAKuF,OAASH,GAAOL,GAAO/E,EAAKwF,KAC5C7B,EACAmB,EAAS5D,EAAKuE,aAAaR,GACnBA,EAAQP,KAAK,eAAegB,SAASzF,IAC7CiB,EAAKyE,OAAOV,IAERG,GAAOL,GAAO/E,EAAKwF,MAAQJ,GAAOL,GAAO/E,EAAKuF,MAClD5B,IAAasB,EAAQP,KAAK,eAAegB,SAASzF,GAClDiB,EAAK0E,SAASX,IAEdH,EAAS5D,EAAKqD,OAAOU,GAChB/D,EAAK2E,SAASf,KACfA,EAASxF,IAGVyF,GAAO/E,EAAK8F,KACnBhB,EAAS5D,EAAKuE,aAAaR,GACpBF,GAAO/E,EAAK+F,IAAOpE,EAAEqE,OAErBjB,GAAO/E,EAAKiG,KACnBnB,EAAS5D,EAAKuE,aAAapG,KACpB0F,GAAO/E,EAAKkG,IACnBpB,EAAS5D,EAAK+C,iBAAiB5E,KACxB0F,GAAO/E,EAAKmG,OAAUlB,EAAQP,KAAK,eAAegB,SAASzF,GAM3D8E,GAAO/E,EAAKoG,UAAYjB,EAASvF,SAAWqF,EAAQP,KAAK,eAAegB,SAASzF,IACxFkF,EAASkB,KAAKlG,GAAUgF,EAASkB,KAAKlG,IAAUmG,KAAK9F,GAAe,GAAO6F,KAAK7F,GAAe,GAC/FU,EAAKqF,iBAAkBzB,OAAQK,IAC/BL,EAASG,IACFtD,EAAEqE,QAAUjB,IAAQ/E,EAAK+F,IAAMhB,IAAQ/E,EAAKwG,MACnDtF,EAAKuF,cAVAxB,EAAQP,KAAK,eAAegB,SAAS,qBACjCxE,EAAKmB,SAASnC,EAAQ+E,IACvB/D,EAAKwF,OAAOzB,GARpBH,EAAS5D,EAAK+C,iBAAiBgB,GAkB/BH,IACAnD,EAAEgF,iBACE1B,EAAQ,IAAMH,EAAO,KACrB5D,EAAKmB,SAAS5B,EAAUqE,GACxB5D,EAAKgE,QAAQJ,OAIzB2B,YAAa,WACTtF,KAAKF,aAAa2F,QAClBzF,KAAKF,aAAaM,QAAQoD,SAE9BkC,QAAS,SAAUlF,GACfR,KAAKmB,eAAeX,GAChBR,KAAKF,aAAaD,QAAQ8F,mBAGb,eAAbnF,EAAEc,OACEtB,KAAKF,aAAaK,uBACF,YAAZK,EAAEqB,OACF7B,KAAKF,aAAa8F,YAAYpF,EAAEe,MAAM,IAG1B,YAAZf,EAAEqB,OAAmC,aAAZrB,EAAEqB,OAAwBrB,EAAEe,MAAM,GAAGsE,UAC9D7F,KAAKF,aAAagG,aAAatF,EAAEe,MAAM,IAI/CvB,KAAKF,aAAa4F,QAAQlF,MAItC9B,GAAME,GAAGmH,cAAgBtG,GAC3Bd,OAAOD,MAAMsH,QACRrH,OAAOD,OACE,kBAAVT,SAAwBA,OAAOgI,IAAMhI,OAAS,SAAUiI,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUnI,EAAGC,QACVA,OAAO,sBACH,wBACA,eACDD,IACL,WA4jDE,MAjjDC,UAAUE,EAAGC,GAo0CV,QAASkI,GAAOC,EAAUC,EAAQzF,GAE9B,IADA,GAA4Ce,GAAxC2E,EAAM,EAAGC,EAAYF,EAAO9H,OAAS,EAClC+H,EAAMC,IAAaD,EACtB3E,EAAQ0E,EAAOC,GACT3E,IAASyE,KACXA,EAASzE,OAEbyE,EAAWA,EAASzE,EAExByE,GAASC,EAAOE,IAAc3F,EA70CrC,GAg1CO4F,GA2EAC,EA15CAjI,EAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIgI,EAAShI,EAAGgI,OAAQrH,EAAWX,EAAGmH,cAAec,EAAkBnI,EAAMyG,KAAK0B,gBAAiBC,EAAmBpI,EAAMyG,KAAK2B,iBAAkBpH,EAASxB,EAAEwB,OAAQqH,EAAgBrI,EAAMsI,eAAgBC,EAAK,qBAAsBpI,EAAOH,EAAMG,KAAMqF,EAAUxF,EAAMwF,QAASgD,EAAc,WAAYC,EAAQ,QAASC,EAAUlD,EAAQkD,QAASC,EAAa3I,EAAM4I,YAAaC,EAAM,IAAKzI,EAAW,WAAY0I,EAAW,WAAYC,EAAgB,mBAAoBC,EAAgB,gBAAiBC,EAAQ,gBAAiBC,EAAU,kBAAmBC,EAAc,aAAeZ,EAAK,cAAgBA,EAAIa,EAAW,WAAY3I,EAAQ,QAAS4I,EAAO,OAAQC,EAAQ,QAASC,EAAS,SAAUC,EAAa,KAAMjJ,EAAQf,EAAEe,MAC5vBkJ,EAAezJ,EAAME,GAAGgI,OAAOlH,QAC/BC,KAAM,SAAUC,EAASC,GAAnB,GAOEiB,GAmCIsH,EASJC,CAlDJrI,MAAKiH,GAAKA,EACVvI,EAAME,GAAGgI,OAAO3G,GAAGN,KAAKO,KAAKF,KAAMJ,EAASC,GAC5CG,KAAKsI,WAAatI,KAAKuI,gBACvBvI,KAAKwI,mBAAqBtK,EAAEe,MAAMe,KAAKyI,YAAazI,MACpDA,KAAK0I,SAAW1I,KAAKJ,QAAQ+I,MAC7B3I,KAAK4I,WACD9H,EAAQd,KAAKH,QAAQiB,MACX,OAAVA,GAAmBA,EAAMrC,SACzBuB,KAAK6I,iBAAkB,GAEtB7I,KAAK8I,mBAAmBhI,KACzBd,KAAK+I,oBAAqB,EAC1B/I,KAAK4I,QAAU1K,EAAE8K,QAAQlI,GAASA,EAAMmI,MAAM,IAAMnI,IAExDd,KAAKkJ,iBACLlJ,KAAKmJ,aACLnJ,KAAKoJ,oBAAoBpJ,KAAKH,SAC9BG,KAAKqJ,cACLrJ,KAAKsI,WAAWgB,eAChBtJ,KAAKuJ,cAAa,GAClBvJ,KAAKwJ,YACLxJ,KAAKI,QAAQ+E,KAAK2C,EAAU9H,KAAKI,QAAQqJ,KAAK3B,IAC9C9H,KAAK0J,KAAOxL,EAAE,UAAUuL,MACpBE,YACAC,eAAe,IAEnB5J,KAAK6J,KAAO3L,EAAE,mCAAqC4L,OAAO9J,KAAK0J,MAC/D1J,KAAK+J,UACL/J,KAAKgK,UACLhK,KAAKiK,UACLjK,KAAKkK,SACLlK,KAAKmK,SACLnK,KAAKoK,MAAMpJ,IAAI,OAAQ/B,EAAMe,KAAKqK,WAAYrK,OAC9CA,KAAKsK,eACLtK,KAAKuK,gBACLvK,KAAKwK,YACLxK,KAAKyK,gBACLzK,KAAK0K,YACL1K,KAAK2K,UACL3K,KAAK4K,yBACA5K,KAAKH,QAAQgL,WACVzC,EAAOvI,EAAQuI,MAAQ,GACtBpI,KAAK8I,mBAAmBjJ,EAAQiB,OAE1BsH,EACPpI,KAAK8K,cAAc1C,GACZvI,EAAQkL,aACf/K,KAAKuJ,cAAa,GAJlBvJ,KAAKgL,WAAWnL,EAAQiB,QAO5BuH,EAAWnK,EAAE8B,KAAKJ,SAASqL,QAAQ,YAAY3H,GAAG,aAClD+E,GACArI,KAAKkL,QAAO,GAEhBlL,KAAK+I,oBAAqB,EAC1BrK,EAAMyM,OAAOnL,OAEjBgL,WAAY,SAAU7F,EAAMrE,GACxBd,KAAKsI,WAAW0C,WAAW7F,EAAMrE,IAErCsI,oBAAqB,SAAUvJ,GAC3B,GAAIuL,IACAP,SAAUhL,EAAQgL,SAClBlJ,WAAY9B,EAAQ8B,WACpB0J,kBAAmBxL,EAAQwL,kBAC3BC,wBAAyBzL,EAAQyL,wBACjCC,cAAe1L,EAAQ0L,cACvBC,aAAc3L,EAAQ2L,aACtB/J,aAAc5B,EAAQ4B,aAE1BzB,MAAKH,QAAQ4L,SAAWvN,EAAEwB,UAAW0L,EAAiBpL,KAAKH,QAAQ4L,UAC/D5L,EAAQ6L,WACR1L,KAAKH,QAAQ4L,SAASC,SAAW7L,EAAQ6L,WAGjDrC,YAAa,WACT,GAAIsC,GAAiB3L,KAAKH,QAAQ+L,UAClC5L,MAAK4L,WAAalN,EAAMyG,KAAK0G,uBAAuBC,OAAOH,GACvDA,GACAzN,EAAEwB,OAAOM,KAAKH,QAAQ4L,UAAYG,WAAY5L,KAAK4L,cAG3DvB,WAAY,WACR,GAAID,GAAQpK,KAAKoK,KACjBA,GAAMhK,QAAU1B,EAAMqN,KAAK3B,EAAMxK,UAErC2I,cAAe,WACX,MAAIvI,MAAKG,uBACE,GAAIvB,GAAGuJ,aAAaxB,kBAAkB3G,MAEtC,GAAIpB,GAAGuJ,aAAazB,gBAAgB1G,OAGnDgM,cAAe,SAAUJ,GACrB5L,KAAK4L,WAAaA,EAClB5L,KAAKyL,SAASO,cAAcJ,IAEhCzL,qBAAsB,WAClB,MAAOH,MAAKH,UAAYG,KAAKH,QAAQ4L,SAAS9J,YAAc3B,KAAKH,QAAQ8B,aAE7E9B,SACIoM,KAAM,eACNC,aACArB,UAAU,EACVsB,WAAW,EACXC,WAAW,EACXC,aAAa,EACbd,cAAe,GACfe,eAAgB,GAChBjB,kBAAmB,GACnBC,wBAAyB,GACzBE,aAAc,GACde,MAAO,IACPC,SAAS,EACTC,kBAAkB,EAClBpO,OAAQ,OACRqO,OAAQ,IACRC,YAAY,EACZvK,MAAO,EACPX,cAAc,EACdmL,UACIC,UAAa,mBACbC,MAAS,QACTC,UAAa,UAEjBC,UAAW,EACXrL,YAAY,EACZsL,eAAgB,iBAChBlC,YAAa,GACb1H,UAAU,EACV6J,iBAAkB,YAClBC,QAAS,WACTzB,SAAU,KACVtD,KAAM,KACNqD,YACA2B,gBAAgB,EAChBC,eAAgB,GAChBC,eAAgB,GAChBxM,MAAO,KACPyM,cAAe,KACfnD,MAAO,MAEXoD,QACI,OACA,QACA,YACAvF,EACA,SACA,aAEJzE,MAAO,WACHxD,KAAKI,QAAQoD,SAEjB9C,SAAU,SAAUpC,GAChB,MAAO0B,MAAKyL,SAAS/K,SAASpC,IAElCmP,SAAU,SAAUA,GAChBzN,KAAK0N,WACDD,SAAUA,IAAatP,GAAmBsP,EAC1CE,SAAS,IAEb3N,KAAK4K,0BAETM,OAAQ,SAAUA,GACdlL,KAAK0N,WACDD,UAAU,EACVE,UAAWzC,EAASA,IAAW/M,GAAmB+M,KAEtDlL,KAAK4K,0BAETgD,OAAQ,SAAUC,GACd7N,KAAK8N,QAAQD,IAEjBA,KAAM,WACF,GAAIzD,GAAQpK,KAAKoK,KACZpK,MAAKH,QAAQgL,UAAa7K,KAAK4L,WAAWzG,OAAO1G,SAClDuB,KAAKyL,SAASvJ,WAAU,GACpBlC,KAAK+N,mBACL/N,KAAKgO,UAELhO,KAAK4L,WAAWqC,UAGpB7D,EAAM8D,WAAclO,KAAKmO,kBAGzBnO,KAAKG,wBACLiK,EAAMxK,QAAQwO,SAAS,wBAE3BhE,EAAMxK,QAAQwO,SAAS,wBACvBhE,EAAMpJ,IAAI,WAAYhB,KAAKwI,oBAC3B4B,EAAMiE,UAAW,EACjBjE,EAAMyD,SAEVpI,MAAO,WACHzF,KAAKoK,MAAM3E,SAEf6I,OAAQ,SAAUC,GAAV,GAEAlQ,GADAwB,EAAUG,KAAKH,OAGnB,IADA2O,aAAaxO,KAAKyO,iBACb5O,EAAQ4M,mBAAqB8B,EAAK9P,QAAU8P,EAAK9P,QAAUoB,EAAQmN,UAAW,CAE/E,GADA3O,EAAS2B,KAAK0O,WAAWH,GACrBvO,KAAK0C,QAAQ,aAAerE,OAAQA,KAAaH,EAAE8K,QAAQhJ,KAAKH,QAAQ0L,eACxE,MAEJvL,MAAK2O,YAAa,EAClB3O,KAAKyL,SAASG,WAAWvN,OAAOA,KAGxCqQ,WAAY,SAAUH,GAClB,OACI1M,MAAO7B,KAAKH,QAAQ0L,cACpBqD,SAAU5O,KAAKH,QAAQxB,OACvByC,MAAOyN,EACP5B,WAAY3M,KAAKH,QAAQ8M,aAGjCjH,QAAS,WACL,GAAIP,GAAOnF,KAAKyL,SAASG,WAAWiD,UACpC7O,MAAKyK,gBACLzK,KAAK8O,gBACD9O,KAAKyD,aAAezD,KAAKqD,UACzBrD,KAAKqD,SAASuK,QAAQ5N,KAAKyD,YAAYkF,MAAMlK,QAEjDuB,KAAK0J,KAAKkE,SAASzI,EAAK1G,QACxBP,EAAE8B,KAAK+O,QAAQnB,QAAQzI,EAAK1G,SAEhCuQ,WAAY,SAAUnP,GAClB+G,EAAO3G,GAAG+O,WAAW9O,KAAKF,KAAMH,GAChCG,KAAKoJ,oBAAoBvJ,GACzBG,KAAKqJ,cACDrJ,KAAKH,QAAQ4L,UACbzL,KAAKyL,SAASuD,WAAWhP,KAAKH,QAAQ4L,UAEtC5L,EAAQ6M,QAAU1M,KAAK0J,MACvB1J,KAAK0J,KAAKuF,IAAI,aAAcpP,EAAQ6M,QAExC1M,KAAK+J,UACL/J,KAAKgK,UACLhK,KAAKiK,UACLjK,KAAKyK,gBACLzK,KAAK8O,gBACD9O,KAAKkP,OAASlP,KAAKG,wBAA0BH,KAAKkP,KAAK3K,SAAS,gBAChEvE,KAAKuJ,cAAa,GAEtBvJ,KAAKkJ,iBACLlJ,KAAKmJ,aACLnJ,KAAKuK,gBACLvK,KAAK0K,YACL1K,KAAK2K,UACD9K,IAAYA,EAAQqL,QAAUrL,EAAQ2M,UACtCxM,KAAKkL,QAAO,GAEhBlL,KAAKsK,gBAET6E,QAAS,WACLzQ,EAAME,GAAGgI,OAAO3G,GAAGkP,QAAQjP,KAAKF,MAC5BA,KAAKyL,UACLzL,KAAKyL,SAAS0D,UAElBnP,KAAKoK,MAAM+E,UACXnP,KAAKI,QAAQgP,IAAInI,GACjBjH,KAAKqP,OAAOD,IAAInI,GAChBjH,KAAKsP,cAAcF,IAAInI,GACnBjH,KAAKyD,aACLzD,KAAKyD,YAAY2L,IAAInI,GAErBjH,KAAKuP,SACLvP,KAAKuP,QAAQH,IAAInI,GAErBvI,EAAM8Q,OAAOxP,KAAKuP,SACdvP,KAAKH,QAAQwD,UAAYrD,KAAKqD,UAC9BrD,KAAKqD,SAAS+L,IAAInI,GAElBjH,KAAKyP,OACLzP,KAAKyP,MAAML,IAAI,QAASpP,KAAK0P,gBAGrCC,SAAU,SAAU7O,GAChBA,EAAQ5C,EAAE8K,QAAQlI,IAAUA,YAAiB+F,GAAkB/F,EAAMmI,MAAM,IAAMnI,GACjFd,KAAK4I,QAAU9H,GAEnBS,MAAO,WACHvB,KAAKyL,SAASmE,aAElB9O,MAAO,SAAUA,GACb,GAAIf,GAAOC,IACX,IAAIc,EACA,GAAIf,EAAK0D,aAAe1D,EAAK6L,WAAWiE,QACpC9P,EAAK4O,YAAa,EAClB5O,EAAK6L,WAAWvN,eACb,KAAK0B,EAAK6L,WAAWzG,OAAO1G,OAI/B,MAHAsB,GAAK6L,WAAWqC,MAAM,WAClBlO,EAAKuI,WAAWwH,UAAUhP,KAE9B,CAGR,OAAOf,GAAKuI,WAAWwH,UAAUhP,IAErCsH,KAAM,SAAUA,GAAV,GACE2H,GACApD,EAAa3M,KAAKH,QAAQ8M,UAE9B,OADAvE,GAAgB,OAATA,EAAgB,GAAKA,EACxBA,IAASjK,GAAc6B,KAAKG,uBASrBH,KAAK8K,gBARQ,gBAAT1C,IACPpI,KAAK8K,cAAc1C,GACnB,IAEJ2H,EAAcpD,EAAavE,EAAOA,EAAK4H,cACvChQ,KAAKiQ,kBAAkBF,GACvB/P,KAAK8K,cAAciF,GAFnBA,IAORhG,QAAS,WAAA,GAWDuD,GAVAzD,EAAO7J,KACPkQ,EAAShS,EAAE2L,EAAKqG,QAChBxE,EAAW7B,EAAKhK,QAAQyN,cAI5B,OAHAtN,MAAKmQ,gBAAgBD,EAAQ,WAC7BxR,EAAMyQ,QAAQe,GACdA,EAAOE,SACF1E,GAID4B,EAAqC,kBAAb5B,GAA0BhN,EAAMgN,SAASA,GAAYA,EACjFwE,EAAShS,EAAEoP,OACXzD,EAAKqG,OAASA,EAAO,GAAKA,EAAS,KACnCrG,EAAKA,KAAKwG,QAAQH,GAClBlQ,KAAKmQ,gBAAgBtG,EAAKqG,OAAQ,WAJ9B5C,IAHAzD,EAAKqG,OAAS,KACd,IAQRlG,QAAS,WAAA,GACDH,GAAO7J,KACP+O,EAAS7Q,EAAE2L,EAAKkF,QAChBrD,EAAW7B,EAAKhK,QAAQoN,cAM5B,OALApD,GAAKyG,QAAQ,UAAW,WACpB,OAASC,SAAUxB,KAEvBrQ,EAAMyQ,QAAQJ,GACdA,EAAOqB,SACF1E,GAIL7B,EAAKkF,OAAS7Q,EAAE,gEAAgEsS,SAAS3G,EAAKA,MAC9FA,EAAKoD,eAAqC,kBAAbvB,GAA0BhN,EAAMgN,SAASA,GAAYA,EADlF7B,IAHIA,EAAKkF,OAAS,KACd,IAKRD,cAAe,WAAA,GACPjF,GAAO7J,KACP+O,EAASlF,EAAKkF,MACbA,KAGL/O,KAAKmQ,gBAAgBpB,EAAQ,WAC7BA,EAAOvQ,SAAS,UAAUiS,KAAK5G,EAAKoD,gBAAiB3G,SAAUuD,KAC/D7J,KAAKmQ,gBAAgBpB,EAAQ,aAEjC9E,QAAS,WAAA,GACDJ,GAAO7J,KACP0Q,EAASxS,EAAE2L,EAAK6G,QAChBhF,EAAW7B,EAAKhK,QAAQwN,cAI5B,OAHArN,MAAKmQ,gBAAgBO,EAAQ,WAC7BhS,EAAMyQ,QAAQuB,GACdA,EAAON,SACF1E,GAIL7B,EAAK6G,OAASxS,EAAE,gCAAgCsS,SAAS3G,EAAKA,MAC9DA,EAAKwD,eAAqC,kBAAb3B,GAA0BhN,EAAMgN,SAASA,GAAYA,EADlF7B,IAHIA,EAAK6G,OAAS,KACd,IAKRjG,cAAe,WAAA,GACPZ,GAAO7J,KACP0Q,EAAS7G,EAAK6G,MACbA,KAGL1Q,KAAKmQ,gBAAgBO,EAAQ,WAC7BA,EAAOD,KAAK5G,EAAKwD,gBAAiB/G,SAAUuD,KAC5C7J,KAAKmQ,gBAAgBO,EAAQ,aAEjC/F,QAAS,WACL,GAAI5K,GAAOC,KAAMH,EAAUE,EAAKF,QAASwI,EAAWtI,EAAKH,QAAQ0D,GAAG,aAChEzD,GAAQqL,SAAW/M,IACnB0B,EAAQ2M,QAAU3M,EAAQqL,SAEzBrL,EAAQ2M,SAAWnE,EACpBtI,EAAKmL,QAAO,GAEZnL,EAAK0N,SAAS1N,EAAKH,QAAQ0D,GAAG,gBAGtCqN,iBAAkB,WACd,GAAwFC,GAAeC,EAAnG9Q,EAAOC,KAAM6J,EAAO9J,EAAK8J,KAAMiH,EAAQjH,EAAK,GAAGkH,MAAMD,MAAO1Q,EAAUL,EAAKK,OAC/E,IAAKyJ,EAAK1E,KAAKgC,KAAU2J,EAmBzB,MAhBAF,GAAgBjS,OAAOqS,iBAAmBrS,OAAOqS,iBAAiB5Q,EAAQ,GAAI,MAAQ,EACtFyQ,EAAgBI,WAAWL,GAAiBA,EAAcE,QAAUzJ,EAAWjH,GAC3EwQ,GAAiBxJ,EAAQ8J,OACzBL,GAAiBI,WAAWL,EAAcO,aAAeF,WAAWL,EAAcQ,cAAgBH,WAAWL,EAAcS,iBAAmBJ,WAAWL,EAAcU,mBAGvKR,EAD2B,eAA3BjH,EAAKoF,IAAI,cACD4B,GAAiBxJ,EAAWwC,GAAQA,EAAKiH,SAEzCD,EAEZhH,EAAKoF,KACDsC,WAAYnR,EAAQ6O,IAAI,eACxB6B,MAAO/Q,EAAKF,QAAQuM,UAAY,OAAS0E,EACzCU,SAAUV,EACVW,WAAY1R,EAAKF,QAAQuM,UAAY,SAAW,WACjDjH,KAAKgC,EAAO2J,IACR,GAEX5G,OAAQ,WACJ,GAAInK,GAAOC,KAAMJ,EAAUG,EAAKH,QAAS8R,EAAS9R,EAAQ6J,KAAK,QAASkI,EAAOD,EAASxT,EAAE,IAAMwT,GAAU9R,EAAQgS,QAAQ,OACtHD,GAAK,KACL5R,EAAK2P,cAAgB,WACjBmC,WAAW,WACP9R,EAAKe,MAAMf,EAAK2I,aAGxB3I,EAAK0P,MAAQkC,EAAKtR,GAAG,QAASN,EAAK2P,iBAG3CvF,OAAQ,WACJ,GAAIN,GAAO7J,IACX6J,GAAKO,MAAQ,GAAIxL,GAAGkT,MAAMjI,EAAKA,KAAMnK,KAAWmK,EAAKhK,QAAQuK,OACzD2H,OAAQlI,EAAKzJ,QACbyN,KAAM5O,EAAM4K,EAAKmI,aAAcnI,GAC/BpE,MAAOxG,EAAM4K,EAAKoI,cAAepI,GACjCqC,UAAWrC,EAAKhK,QAAQqM,UACxB/H,MAAOD,EAAQC,MAAM0F,EAAKzJ,SAC1B8R,SAAUrI,EAAKhK,QAAQuM,cAG/B+D,gBAAiB,SAAUvQ,EAAS0B,GAC3B1B,GAGLI,KAAKsQ,QAAQhP,EAAQ,WACjB,OAASiP,SAAU3Q,MAG3BuO,cAAe,WACX,MAAOnO,MAAKH,QAAQoN,gBAAkBjN,KAAKyL,SAASG,WAAWiD,WAAWpQ,QAE9E8K,aAAc,SAAU4I,GAChBnS,KAAKkP,MACLlP,KAAKkP,KAAKkD,YAAY,aAAcD,GAAM/J,KAAK+J,EAAOnS,KAAKH,QAAQkL,YAAc,KAGzFsH,cAAe,SAAU3R,GACrB,GAAI4R,GAAetS,KAAKuS,OAAO7R,EAI/B,OAHK4R,IAAiC,IAAjBA,IACjBA,EAAe5R,GAEZ4R,GAEX1M,YAAa,SAAUlF,GAAV,GAaD8R,GAMAC,EAcAC,EAGAlM,EAnCJ1F,EAAQ,GACR6R,KACAL,EAAetS,KAAKc,QACpB8R,EAAsC,aAAzB5S,KAAKH,QAAQsN,OAQ9B,KAPIzM,GAAyB,IAAbA,KACRA,EAASoB,QACTpB,EAASmS,OAASnS,EAASoB,SAE/BhB,EAAQd,KAAKqS,cAAc3R,GAC3BiS,EAAeL,EAAaQ,QAAQhS,IAEpCJ,EAASE,QAAS,CAIlB,GAHI4R,EAAkBtU,EAAE6U,KAAK/S,KAAKgT,MAAO,SAAUC,GAC/C,MAAOA,GAAKhR,MAAQvB,EAASwS,UAE7BV,EAAgB/T,OAChB,MAEAgU,GAAY,GAAI3L,GAAiBpG,EAASyS,UAC9CzS,EAASwS,QAAUT,EAAUxQ,IAC7BjC,KAAKgT,MAAMI,KAAKX,GACU,IAAtBzS,KAAKgT,MAAMvU,SACXuB,KAAKkP,KAAKmE,OACLT,GACD5S,KAAKsT,cAAcF,KAAKX,IAG5BE,SACAL,EAAac,KAAKtS,GAClBd,KAAK2P,SAAS2C,QAEf,CAKH,GAJII,EAAe1S,KAAKgT,MAAMzP,KAAK,SAAU0P,GACzC,MAAOA,GAAKhR,MAAQvB,EAASwS,UAE7B1M,EAAMxG,KAAKgT,MAAMF,QAAQJ,GACzBlM,OAIA,MADAxG,MAAKuT,uBAAuB7S,GAC5B,CAHAV,MAAKgT,MAAMQ,OAAOhN,EAAK,GAKD,IAAtBxG,KAAKgT,MAAMvU,SACXuB,KAAKkP,KAAKiD,OACLS,GACD5S,KAAKsT,cAAcE,OAAO,EAAG,IAGjCb,SACAL,EAAakB,OAAOb,EAAc,GAClC3S,KAAK2P,SAAS2C,IAGtBtS,KAAKuT,uBAAuB7S,GACvBV,KAAKyT,uBAA0BzT,KAAK+I,oBAAuB/I,KAAK6I,iBACjE7I,KAAK0C,QAAQuF,GAEbjI,KAAKH,QAAQsM,WAAanM,KAAKoK,MAAM8D,YACrClO,KAAKyF,QACLzF,KAAKI,QAAQoD,SAEjBxD,KAAKoK,MAAMsJ,WACX1T,KAAK4K,yBACL5K,KAAK2T,0BAETA,uBAAwB,WAAA,GAIhBC,GACA/T,EACAa,EACAI,EAES0F,CARb,IAA8C,WAA1CxG,KAAKJ,QAAQ,GAAGiU,QAAQ7D,cAA5B,CAOA,GAJI4D,EAAgB5T,KAAKgT,MACrBnT,EAAU,GACVa,EAAW,KACXI,EAAQ,KACR8S,EAAcnV,OACd,IAAS+H,EAAM,EAAGA,EAAMoN,EAAcnV,OAAQ+H,IAC1C9F,EAAWkT,EAAcpN,GACzB1F,EAAQd,KAAKuS,OAAO7R,GACpBb,GAAWG,KAAK8T,QAAQhT,EAAOd,KAAK+T,MAAMrT,IAAW,EAG7DV,MAAKJ,QAAQ6Q,KAAK5Q,KAEtBiU,QAAS,SAAUE,EAAWC,EAAUpO,GACpC,GAAIqO,GAAS,SAeb,OAdIF,KAAc7V,IACd6V,GAAa,GACTA,EAAUlB,QAAQ,YAClBkB,EAAYA,EAAUG,QAAQjM,EAAY,WAE9CgM,GAAU,WAAaF,EAAY,KAEnCnO,IACAqO,GAAU,aAEdA,GAAU,IACND,IAAa9V,IACb+V,GAAUxV,EAAM0V,WAAWH,IAExBC,GAAU,aAErBpO,aAAc,SAAUpF,GAAV,GACNI,GAAQ,GACRsH,EAAO,IACP1H,GAAyB,IAAbA,KACRA,EAASoB,QACTpB,EAASmS,OAASnS,EAASoB,SAE/BsG,EAAOpI,KAAK+T,MAAMrT,IAAaA,EAC/BI,EAAQd,KAAKqS,cAAc3R,IAEjB,OAAVI,IACAA,EAAQ,IAEZd,KAAK2P,SAAS7O,GACdd,KAAK8K,cAAc1C,EAAM1H,GACzBV,KAAKqU,UAAUvT,GACVd,KAAK+I,oBACN/I,KAAK0C,QAAQuF,GAEjBjI,KAAK+I,oBAAqB,EACtB/I,KAAKH,QAAQsM,WAAanM,KAAKoK,MAAM8D,YACrClO,KAAKyF,QACLzF,KAAKI,QAAQoD,SAEjBxD,KAAKoK,MAAMsJ,WACX1T,KAAK4K,0BAET0J,YAAa,SAAU9T,GACnBA,EAAE+T,kBACFvU,KAAKwU,sBAETA,mBAAoB,WAChBxU,KAAK2P,aACL3P,KAAKyU,cACLzU,KAAK0U,aACL1U,KAAKsI,WAAWqM,cAChB3U,KAAKoK,MAAMsJ,WACX1T,KAAK4K,0BAET8J,WAAY,WACJ1U,KAAKH,QAAQkL,YACb/K,KAAKuJ,cAAa,GAEdvJ,KAAKkP,MACLlP,KAAKkP,KAAKuB,KAAK,KAI3BvH,eAAgB,WACZ,GAAIwC,GAAW1L,KAAKH,QAAQ0N,aAIxB7B,GAHCA,EAGUhN,EAAMgN,SAASA,GAFfxN,EAAEe,MAAMP,EAAMgN,SAAS,uBAAyBkJ,cAAc,IAAU5U,MAIvFA,KAAKuN,cAAgB7B,GAEzBmJ,gBAAiB,SAAUzM,EAAMtH,GAAhB,GACTyK,GAAgBvL,KAAKH,QAAQ0L,cAC7B7K,IAQJ,OAPI6K,IACAlF,EAAO3F,EAAU6K,EAAcuJ,MAAMvN,GAAMa,GAC3C/B,EAAO3F,EAAUV,KAAKH,QAAQyM,eAAewI,MAAMvN,GAAMzG,GACzDJ,EAAW,GAAIoG,GAAiBpG,IAEhCA,EAAW0H,EAER1H,GAEXoK,cAAe,SAAU1C,EAAM1H,GAAhB,GAaPqU,GAZAxH,EAAgBvN,KAAKuN,cACrB2B,EAAOlP,KAAKkP,IAChB,IAAI9G,IAASjK,EACT,MAAO+Q,GAAK9G,MAEhB8G,GAAK8F,YAAY,eACZtU,IAAaxC,EAAE+W,cAAc7M,IAASA,YAAgBtB,MACvDpG,EAAW0H,GAEV1H,IACDA,EAAWV,KAAK6U,gBAAgBzM,EAAMpI,KAAKqU,cAE3CU,EAAc,WACd,OACIxE,SAAUrB,EAAKgG,MACf/P,OAASzE,SAAUA,MAG3BV,KAAKsQ,QAAQ,UAAWyE,EACxB,KACI7F,EAAKuB,KAAKlD,EAAc7M,IAC1B,MAAOF,GACD0O,GACAA,EAAKuB,KAAK,IAGlBzQ,KAAKsQ,QAAQ,UAAWyE,IAE5B5L,WAAY,WAAA,GACJvJ,GAAUI,KAAKJ,QACfC,EAAUG,KAAKH,QACfsV,EAASzW,EAAMyW,OACfC,EAAYxV,EAAQ6J,KAAK/K,EAAM+K,KAAK,eACpC4L,EAAazV,EAAQ6J,KAAK/K,EAAM+K,KAAK,gBACrC6L,EAAiB,SAAUzT,GAAV,GAET0T,GACAC,CAFR,OAAItX,GAAE8K,QAAQnH,IACN0T,EAAQ1T,EAAMpD,OACd+W,EAAStX,EAAEuX,IAAI5T,EAAO,SAAU6T,GAChC,MAAO,UAAUC,GACb,MAAOA,GAAED,MAGV,SAAUhV,GACb,GAAIoB,GAAQpB,EAASmS,MACrB,IAAK/Q,GAAmB,IAAVA,EAGd,MAAO0T,GAAOI,KAAKC,IAAI/T,EAAOyT,EAAQ,IAAI7U,KAGvCyU,EAAOtT,KAGjBhC,EAAQ0L,eAAiB6J,IAC1BvV,EAAQ0L,cAAgB6J,IAEvBvV,EAAQyM,gBAAkB+I,IAC3BxV,EAAQyM,eAAiB+I,GAE7BxV,EAAQ0L,cAAgB1L,EAAQ0L,eAAiB,OACjD1L,EAAQyM,eAAiBzM,EAAQyM,gBAAkB,QACnDtM,KAAK+T,MAAQuB,EAAezV,EAAQ0L,eACpCvL,KAAKuS,OAAS+C,EAAezV,EAAQyM,iBAEzC+H,UAAW,SAAUvT,EAAO0F,GACxB,MAAOxG,MAAK8V,eAAehV,EAAO0F,IAEtCsP,eAAgB,SAAUhV,GACtB,GAAIlB,GAAUI,KAAKJ,QAAQ,EAC3B,OAAIkB,KAAU3C,EACHyB,EAAQkB,OAED,OAAVA,IACAA,EAAQ,IAEZlB,EAAQkB,MAAQA,EAHhB,IAMR2T,YAAa,WACT,GAAI7U,GAAUI,KAAKJ,QAAQ,EAC3BA,GAAQkB,MAAQ,IAEpBwJ,aAAc,WACV,GAAIyL,GAAa/V,KAAKH,QAAQ+M,UAAY5M,KAAKH,QAAQ+M,SAASE,MAAQ9M,KAAKH,QAAQ+M,SAASE,MAAQ,OACjG9M,MAAKqP,SACNrP,KAAKqP,OAASnR,EAAE,yEAA2E6X,EAAa,aAAatM,MACjHuM,KAAQ,SACRrM,eAGJ3J,KAAKH,QAAQwM,aACbrM,KAAKqP,OAAO4G,YAAYjW,KAAKkP,MAC7BlP,KAAKI,QAAQgO,SAAS,6BAEjBpO,KAAKH,QAAQwM,aACdrM,KAAKqP,OAAOe,UAIxBxF,uBAAwB,WAAA,GAChBsL,GAAalW,KAAKJ,QAAQ6J,KAAKjC,GAC/B2O,EAAWnW,KAAKc,UAAYd,KAAKG,wBAA0BH,KAAKc,QAAQrC,OACxE2X,EAA+BpW,KAAKJ,QAAQ+I,OAAS3I,KAAKJ,QAAQ+I,QAAU3I,KAAKH,QAAQkL,WACxFmL,KAAeC,IAAYC,EAG5BpW,KAAKqW,aAFLrW,KAAKsW,cAKbA,WAAY,WACJtW,KAAKqP,QACLrP,KAAKqP,OAAO2F,YAAY9N,IAGhCmP,WAAY,WACJrW,KAAKqP,QACLrP,KAAKqP,OAAOjB,SAASlH,IAG7B8K,aAAc,SAAUxR,GACpBR,KAAK2Q,mBACD3Q,KAAK0C,QAAQqF,GACbvH,EAAEgF,kBAEFxF,KAAKI,QAAQqJ,KAAK,iBAAiB,GACnCzJ,KAAK0J,KAAKD,KAAK,eAAe,GAAOA,KAAK,OAAQ,UAG1DwI,cAAe,SAAUzR,GACjBR,KAAK0C,QAAQsF,GACbxH,EAAEgF,kBAEFxF,KAAKI,QAAQqJ,KAAK,iBAAiB,GACnCzJ,KAAK0J,KAAKD,KAAK,eAAe,KAGtCe,UAAW,WACP,GAAIzK,GAAOC,IACPD,GAAKF,QAAQ6M,QACb3M,EAAK2J,KAAKuF,IAAI,aAAclP,EAAKF,QAAQ6M,QAE7C3M,EAAK2J,KAAKD,KAAK,KAAM/K,EAAM6X,QAC3BxW,EAAK0L,SAAW,GAAIlM,GAASQ,EAAK2J,KAAMhK,GAAS6F,OAAQxF,EAAKF,QAAQ0F,QAAUxF,EAAKF,QAAQ4L,UAAW1L,GACxGA,EAAK6L,WAAa7L,EAAK0L,SAASG,YAEpCjJ,mBAAoB,SAAUnC,GAAV,GAiBRgW,GAMAC,EAEIC,CArBZ,OAHIlW,GAAElC,MAAQ0B,KAAK2W,OAAS3W,KAAK2W,MAAMlY,QACnC+B,EAAEoC,OAAO4B,OAAOhE,EAAElC,MAElB0B,KAAK2O,YACAnO,EAAElC,OACH0B,KAAK2O,YAAa,GAEjB3O,KAAKG,wBACNH,KAAK4W,cAAcpW,GAEvB,IAECR,KAAKyL,WACNzL,KAAKyL,SAAWjL,EAAEoC,QAEjBpC,EAAElC,MAOCmY,EAAWjW,EAAEoC,OAAOlC,SAASF,EAAElC,MAC/BmY,IACIC,EAAWD,EAASjY,SAAS2G,OACjCnF,KAAK6W,kBAAkBH,MATvBF,EAAYhW,EAAEoC,OAAOgJ,WAAWzG,OACpCnF,KAAK6W,kBAAkBL,GACnBxW,KAAK6I,kBACL7I,KAAK6I,iBAAkB,IAS/B7I,KAAK0C,QAAQ,YAAalC,GAhB1B,IAkBJoW,cAAe,SAAUpW,GAAV,GAKHiW,GAKCrV,EATLG,IASJ,KARKf,EAAElC,MAGCmY,EAAWjW,EAAEoC,OAAOlC,SAASF,EAAElC,MAC/BmY,IACAlV,EAAQkV,EAASjY,SAAS2G,SAJ9B5D,EAAQf,EAAEoC,OAAOgJ,WAAWzG,OAOvB/D,EAAI,EAAGA,EAAIG,EAAM9C,OAAQ2C,IAC1BG,EAAMH,GAAGyE,WAAa7F,KAAK8W,eAAevV,EAAMH,GAAIpB,KAAKc,UACzDS,EAAMH,GAAGT,IAAI,YAAY,IAIrCkW,kBAAmB,SAAUtV,GAAV,GAKNiF,GAJL1F,EAAQd,KAAKc,OACjB,IAAKS,EAGL,IAASiF,EAAM,EAAGA,EAAMjF,EAAM9C,OAAQ+H,IAClCxG,KAAKsI,WAAWyO,iBAAiBxV,EAAMiF,GAAM1F,IAGrDyS,uBAAwB,SAAU7S,GAC1BV,KAAKH,QAAQwD,UAAYrD,KAAKqD,WAC9BrD,KAAKgX,iBACDtW,EAASE,QACTZ,KAAKiX,iBAELjX,KAAKkX,qBAIjBD,eAAgB,WACZ,GAAIE,GAAmBnX,KAAKqD,SAASE,KAAK,cACtCvD,MAAKoX,oBACLD,EAAiBjS,KAAK,WAAW,GAAMA,KAAK,iBAAiB,GAE7DiS,EAAiBjS,KAAK,iBAAiB,IAG/CgS,iBAAkB,WACd,GAAIC,GAAmBnX,KAAKqD,SAASE,KAAK,cACtCvD,MAAKqX,sBACLF,EAAiBjS,KAAK,WAAW,GAAOA,KAAK,iBAAiB,GAE9DiS,EAAiBjS,KAAK,iBAAiB,IAG/CqF,cAAe,WACX,GAAI+M,EACAtX,MAAKyD,cACLzD,KAAKyD,YAAY2L,IAAInI,GAAI7D,SAASgN,SAClCpQ,KAAKyD,YAAc,MAEnBzD,KAAK+N,qBACL/N,KAAKuX,wBACLD,EAAO,wCACPtX,KAAKyD,YAAcvF,EAAE,8BAA8BuL,MAC/CsB,YAAa/K,KAAKJ,QAAQ6J,KAAK,eAC/B+N,MAAOxX,KAAKJ,QAAQ6J,KAAK,SACzBuM,KAAM,UACNyB,iBAAiB,EACjBC,iBAAiB,IAErB1X,KAAKyD,YAAYpD,GAAG,QAASpB,EAAMe,KAAK2X,cAAe3X,OACvD9B,EAAE,kCAAkC0Z,aAAa5X,KAAK0J,MAAMI,OAAO9J,KAAKyD,YAAYoU,IAAIP,MAGhGK,cAAe,WACP3X,KAAKyD,aACLzD,KAAKgO,WAGbuJ,sBAAuB,WACfvX,KAAKG,wBAA0BH,KAAKH,QAAQ4L,SAAS9J,YAAc3B,KAAKH,QAAQ4L,SAAS9J,WAAWD,gBACpG1B,KAAKH,QAAQ4L,SAAS9J,WAAWD,eAAgB,IAGzDgJ,UAAW,WACH1K,KAAKqD,WACLrD,KAAKqD,SAASE,KAAK,kCAAkC6L,IAAInI,GACzDjH,KAAKqD,SAAS+M,SACdpQ,KAAKqD,SAAW,MAEhBrD,KAAKG,wBAA0BH,KAAKH,QAAQwD,WAC5CrD,KAAKqD,SAAWnF,EAAE,6HAA6H0Z,aAAa5X,KAAK0J,MACjK1J,KAAKqD,SAASE,KAAK,qBAAqBkN,KAAK/R,EAAMgN,SAAS1L,KAAKH,QAAQqN,mBAAoB5G,SAAUtG,QACvGA,KAAKqD,SAASE,KAAK,qBAAqBlD,GAAGlB,EAAQ8H,EAAIhI,EAAMe,KAAK8X,eAAgB9X,OAClFA,KAAKqD,SAASE,KAAK,eAAelD,GAAG,SAAW4G,EAAIhI,EAAMe,KAAK+X,gBAAiB/X,OAAOK,GAAG,UAAY4G,EAAIhI,EAAMe,KAAKgY,iBAAkBhY,OACvIA,KAAKiY,yBACLjY,KAAKkY,2BACLlY,KAAKgX,iBACAhX,KAAKqX,uBACNrX,KAAKiX,mBAIjBc,gBAAiB,WAAA,GACTZ,GAAmBnX,KAAKqD,SAASE,KAAK,eACtC4U,EAAYhB,EAAiBjS,KAAK,UACjCkC,GAAQ8J,MAAS9J,EAAQgR,MAC1BpY,KAAKqY,gBAAgBF,IAG7BE,gBAAiB,SAAUF,GACvB,GAAIhB,GAAmBnX,KAAKqD,SAASE,KAAK,cAC1CvD,MAAKsY,qBAAqBH,GAC1BhB,EAAiBjS,KAAK,UAAWiT,GAC7BnY,KAAKiY,sBAAsBxZ,QAAUuB,KAAKkY,wBAAwBzZ,OAClE0Y,EAAiBjS,KAAK,iBAAiB,GAChClF,KAAKiY,sBAAsBxZ,OAClC0Y,EAAiBjS,KAAK,iBAAkBiT,GACjCnY,KAAKkY,wBAAwBzZ,OACpC0Y,EAAiBjS,KAAK,gBAAiBiT,GAEvChB,EAAiBjS,KAAK,iBAAiB,GAE3ClF,KAAKiY,yBACLjY,KAAKkY,4BAETF,iBAAkB,SAAUxX,GAAV,GACVoD,GAAMpD,EAAEqD,QACRgB,EAASrE,EAAEqE,MACf,OAAIA,IAAUjB,IAAQ/E,EAAK+F,IAAMhB,IAAQ/E,EAAKwG,KAC1CrF,KAAKyF,QACLzF,KAAKI,QAAQoD,QACbhD,EAAEgF,iBACF,IAEA5B,IAAQ/E,EAAK+F,KACT5E,KAAKyD,YACLzD,KAAKyD,YAAYD,QAEjBxD,KAAKI,QAAQoD,QAEjBhD,EAAEgF,kBAEF5B,IAAQ/E,EAAK8F,OACT3E,KAAK0J,MAAQ1J,KAAK0J,KAAKpG,GAAG,aAC1BtD,KAAK0J,KAAKlG,QAEdhD,EAAEgF,kBAEF5B,IAAQ/E,EAAKoG,WAAamC,EAAQ8J,MAAQ9J,EAAQgR,QAClDpY,KAAK8X,iBACLtX,EAAEgF,kBAhBN,IAmBJsS,eAAgB,WAAA,GACRX,GAAmBnX,KAAKqD,SAASE,KAAK,eACtC4U,EAAYhB,EAAiBjS,KAAK,UACtClF,MAAKqY,iBAAiBF,GACtBhB,EAAiB3T,SAErB+U,KAAM,SAAUhX,EAAOD,EAAQ4D,GAC3B,IAAK,GAAIsB,GAAM,EAAGA,EAAMjF,EAAM9C,QACrBuB,KAAKsB,GAAQC,EAAMiF,GAAMtB,GADIsB,IAIlCxG,KAAKwY,kBAAkBjX,EAAMiF,GAAMlF,EAAQ4D,IAGnDuT,kBAAmB,SAAUxW,GACzBjC,KAAKuY,KAAKvY,KAAK4L,WAAWzG,OAAQ,wBAAyBlD,IAE/DyW,sBAAuB,SAAUzF,EAAMhR,GACnC,MAAIgR,GAAKzG,WAAY,GAASyG,EAAKC,SAAWjR,IAC1CgR,EAAKtS,IAAI,WAAW,IACb,IAIfsP,kBAAmB,SAAU7H,GACzBpI,KAAKuY,KAAKvY,KAAK4L,WAAWzG,OAAQ,kBAAmBiD,IAEzDuQ,gBAAiB,SAAU1F,EAAM7K,GAC7B,MAAI6K,GAAKzG,WAAY,GAASxM,KAAK+T,MAAMd,KAAU7K,IAC/CpI,KAAKyL,SAASlG,OAAOvF,KAAKyL,SAASzJ,UAAUiR,EAAKhR,MAClDjC,KAAK8F,aAAamN,IACX,IAIf2F,mBAAoB,SAAU9X,GAC1Bd,KAAKuY,KAAKvY,KAAK4L,WAAWzG,OAAQ,mBAAoBrE,IAE1D+X,iBAAkB,SAAU5F,EAAMnS,GAC9B,MAAImS,GAAKzG,WAAY,IAASxM,KAAK8W,eAAe7D,EAAMnS,KACpDd,KAAKyL,SAASlG,OAAOvF,KAAKyL,SAASzJ,UAAUiR,EAAKhR,OAC3C,IAIf6W,kBAAmB,SAAUhY,GAAV,GAEN0F,GADLjF,EAAQvB,KAAKyL,SAASmE,WAC1B,KAASpJ,EAAM,EAAGA,EAAM1F,EAAMrC,OAAQ+H,IAClCxG,KAAKuY,KAAKhX,EAAO,wBAAyBT,EAAM0F,KAGxDuS,sBAAuB,SAAU9F,EAAMnS,GACnC,MAAImS,GAAKzG,WAAY,IAASxM,KAAK8W,eAAe7D,EAAMnS,KACpDmS,EAAKtS,IAAI,WAAW,IACb,IAIfmW,eAAgB,SAAU7D,EAAMnS,GAAhB,GAERkY,GAKIC,EANJC,EAAYlZ,KAAKuS,OAAOU,EAE5B,OAAKjT,MAAK8I,mBAAmBoQ,IAW7BF,EAAWhZ,KAAK+T,MAAMd,KAClB+F,IACIhZ,KAAK+T,MAAMjT,GACJkY,GAAYhZ,KAAK+T,MAAMjT,GAEvBkY,GAAYlY,KAfnBd,KAAK8I,mBAAmBhI,KAGxBmY,EAAWjZ,KAAKuS,OAAOzR,GACvBmY,EACOC,GAAaD,EAEbC,GAAapY,IAahCgI,mBAAoB,SAAUhI,GAC1B,MAAOA,KAAU3C,GAAuB,OAAV2C,GAElCkW,eAAgB,WAKZ,MAJAhX,MAAKmZ,oBACLnZ,KAAKoX,qBAAsB,EAC3BpX,KAAKqX,uBAAwB,EAC7BrX,KAAKuY,KAAKvY,KAAK4L,WAAWzG,OAAQ,uBAC3BnF,KAAKmZ,kBAEhBC,oBAAqB,SAAUnG,GAU3B,MATIjT,MAAKoX,sBACLpX,KAAKoX,oBAAsBnE,EAAKrS,SAEhCZ,KAAKqX,wBACLrX,KAAKqX,uBAAyBpE,EAAKrS,SAEnCqS,EAAKrS,SACLZ,KAAKmZ,iBAAiB/F,KAAKH,IAExB,GAEXuF,kBAAmB,SAAUvF,EAAM3R,EAAQ4D,GAAxB,GACXmU,GAAgBpG,EAAKqG,kBAAoBrG,EAAKqG,iBAAiBC,OAAStG,EAAKqG,iBAAiBC,OAAOpU,KAAO,KAC5GuR,EAAWzD,EAAKoG,IAAkBpG,EAAK1R,OAAS0R,EAAKzU,QACpDkY,IAGL1W,KAAKuY,KAAK7B,EAAUpV,EAAQ4D,IAEhCoT,qBAAsB,SAAU1X,GAC5BZ,KAAKuY,KAAKvY,KAAK4L,WAAWzG,OAAQ,qBAAsBvE,IAE5D4Y,mBAAoB,SAAUvG,EAAMrS,GAUhC,MATIqS,GAAKzG,WAAY,EACbyG,EAAKrS,QACLZ,KAAKiY,sBAAsB7E,KAAKH,GAEhCjT,KAAKkY,wBAAwB9E,KAAKH,GAGtCA,EAAKtS,IAAI,UAAWC,IAEjB,GAEXmN,iBAAkB,WACd,GAAI1P,GAAS2B,KAAKH,QAAQxB,MAC1B,OAAOA,IAAqB,SAAXA,GAErBqP,UAAW,SAAU7N,GAAV,GACHE,GAAOC,KACPJ,EAAUG,EAAKH,QACf+N,EAAU9N,EAAQ8N,QAClBF,EAAW5N,EAAQ4N,SACnBrN,EAAUL,EAAKK,QAAQyX,IAAI9X,EAAK0D,aAAa2L,IAAInI,GACjDwS,EAAkB1Z,EAAKuP,cAAcF,IAAIvH,EACzC9H,GAAKI,wBACLJ,EAAKwP,QAAQH,IAAIjQ,EAAQ8H,GAExBwG,GAAaE,EAcPA,GACPvN,EAAQsZ,WAAW5R,GACnB2R,EAAgBrL,SAAS3G,KAEzBrH,EAAQqJ,KAAK3B,EAAU1H,EAAQ+E,KAAK2C,IACpC2R,EAAgBzE,YAAYvN,GAC5BrH,EAAQC,GAAG,UAAY4G,EAAIhI,EAAMc,EAAK4Z,gBAAiB5Z,IAAOM,GAAG,WAAa4G,EAAIhI,EAAMc,EAAK6Z,iBAAkB7Z,MAnB/GH,EAAQ8Z,WAAW5a,GAAU4a,WAAWlS,GACxCiS,EAAgBzE,YAAYvN,GAAepH,GAAGwH,EAAa9H,EAAK8Z,cAChE9Z,EAAKsP,OAAOhP,GAAG,QAAU4G,EAAIhI,EAAMc,EAAKuU,YAAavU,IACrDK,EAAQqJ,KAAK3B,EAAU1H,EAAQ+E,KAAK2C,IAAW2B,KAAK/B,GAAe,GAAOrH,GAAG,UAAY4G,EAAIhI,EAAMc,EAAK2D,SAAU3D,IAAOM,GAAG,UAAY4G,EAAIhI,EAAMc,EAAK4Z,gBAAiB5Z,IAAOM,GAAG,WAAa4G,EAAIhI,EAAMc,EAAK6Z,iBAAkB7Z,IAChOA,EAAKK,QAAQC,GAAGlB,EAAQ8H,EAAIhI,EAAMc,EAAK+Z,cAAe/Z,IAClDC,KAAKG,yBACLJ,EAAKwP,QAAQlP,GAAGlB,EAAQ8H,EAAI,cAAe,SAAUzG,GACjDtC,EAAEsC,EAAES,eAAemN,SAASxG,KAEhC7H,EAAKwP,QAAQlP,GAAGlB,EAAQ8H,EAAI,YAAa,SAAUzG,GAC/CT,EAAKga,gBAAgBvZ,OAWjCZ,EAAQ6J,KAAK3K,EAAU6O,GAASlE,KAAKjC,EAAUiG,GAC/CrN,EAAQqJ,KAAK/B,EAAeiG,IAEhCgM,gBAAiB,WACb3Z,KAAKsP,cAAclB,SAASxG,GAC5B5H,KAAKga,UAAW,GAEpBJ,iBAAkB,WACd,GAAI7Z,GAAOC,IACPA,MAAKG,wBACLH,KAAKuP,QAAQhM,KAAKgE,EAAMK,GAASoN,YAAYpN,GAE5C7H,EAAKia,WACNha,KAAKsP,cAAc0F,YAAYpN,GAC/B7H,EAAKia,UAAW,EAChBja,EAAKH,QAAQqa,SAGrBnM,QAAS,SAAUD,GACfA,EAAOA,IAAS1P,EAAY0P,GAAQ7N,KAAKoK,MAAM8D,UAC/ClO,KAAK6N,EAAO9F,EAAOC,MAEvB8R,cAAe,SAAUtZ,GACrBA,EAAEgF,iBACFxF,KAAKoK,MAAMoF,OAAO,WAAYxP,KAAKwI,oBACnCxI,KAAKka,SAAWla,KAAKI,QACrBJ,KAAKga,UAAW,EAChBha,KAAK8N,WAET+L,aAAc,SAAUrZ,GACpBtC,EAAEsC,EAAES,eAAemR,YAAYzK,EAAkB,eAAXnH,EAAE2Z,OAE5C1R,YAAa,WACLzI,KAAKyD,YACLzD,KAAKyD,YAAYD,QACVxD,KAAKqD,SACZrD,KAAKqD,SAASE,KAAK,eAAeC,QAC3BxD,KAAK0J,KAAKpG,GAAG,aACpBtD,KAAK0J,KAAKlG,SAGlBE,SAAU,SAAUlD,GAAV,GAGF4Z,GACAC,EACAvW,EAASwW,EAJT1W,EAAMpD,EAAEqD,QACRgB,EAASrE,EAAEqE,OAIX0V,EAAiBva,KAAKoK,MAAM8D,SAOhC,IANIlO,KAAKyD,cACL2W,EAAsBpa,KAAKyD,YAAY,KAAOsD,KAE9C/G,KAAKI,UACLia,EAAkBra,KAAKI,QAAQ,KAAO2G,KAEtCsT,EAAiB,CACjB,GAAIzW,IAAQ/E,EAAKwG,IAGb,MAFArF,MAAKwU,qBACLhU,EAAEgF,iBACF,CAEJ,IAAIxF,KAAKG,uBAAwB,CAC7B,GAAIyD,IAAQ/E,EAAKwF,KAGb,MAFArE,MAAKwa,gBACLha,EAAEgF,iBACF,CAEJ,IAAI5B,IAAQ/E,EAAKuF,MAGb,MAFApE,MAAKya,gBACLja,EAAEgF,iBACF,CAEJ,IAAI5B,IAAQ/E,EAAKiG,KAGb,MAFA9E,MAAK0a,iBACLla,EAAEgF,iBACF,CAEJ,IAAI5B,IAAQ/E,EAAKkG,IAGb,MAFA/E,MAAK2a,gBACLna,EAAEgF,iBACF,CAEJ,IAAI5B,IAAQ/E,EAAK+b,OAOb,MANA9W,GAAU9D,KAAKuP,QAAQhM,KAAKgE,EAAMK,GAASiT,QACvC/W,EAAQrF,SACR6b,EAAUta,KAAKgT,MAAMlP,EAAQ1B,SAC7BpC,KAAK8a,WAAWR,IAEpB9Z,EAAEgF,iBACF,CAEJ,IAAI5B,IAAQ/E,EAAKkc,UAab,MAZAjX,GAAU9D,KAAKuP,QAAQhM,KAAKgE,EAAMK,GAASiT,QACvC/W,EAAQrF,QACR6b,EAAUta,KAAKgT,MAAMlP,EAAQ1B,SAC7BpC,KAAK8a,WAAWR,KAEhBxW,EAAU9D,KAAK2a,gBACX7W,EAAQrF,SACR6b,EAAUta,KAAKgT,MAAMlP,EAAQ1B,SAC7BpC,KAAK8a,WAAWR,KAGxB9Z,EAAEgF,iBACF,GAkBZ,MAdI4U,KACIxW,IAAQ/E,EAAKwG,KACbrF,KAAKgb,eAELpX,IAAQ/E,EAAK+F,IAAOC,IACpB7E,KAAKI,QAAQoD,QACbhD,EAAEgF,kBAEF4B,EAAQ8J,MAAQ9J,EAAQ6T,QAAU,KAC9BrX,IAAQ/E,EAAKkc,WAAanX,IAAQ/E,EAAK+b,QACvC5a,KAAKgO,YAIbnJ,GAAUjB,IAAQ/E,EAAK+F,IAAMhB,IAAQ/E,EAAKwG,KAC1CrF,KAAKI,QAAQoD,QACbxD,KAAKyF,QACLjF,EAAEgF,iBACF,GAEA5B,IAAQ/E,EAAKmG,OAAShF,KAAKyO,gBAAkBzO,KAAKyD,aAAe8W,GACjE/Z,EAAEgF,iBACF,IAEA5B,IAAQ/E,EAAKoG,UAAamV,IAC1Bpa,KAAK8N,SAASyM,GACd/Z,EAAEgF,kBAEFX,GAAUjB,IAAQ/E,EAAK8F,OAAS4V,IAChCva,KAAK6N,OACLrN,EAAEgF,kBAEF5B,IAAQ/E,EAAK8F,MAAQ4V,IACjBva,KAAKyD,cAAgB2W,EACrBpa,KAAKyD,YAAYD,QACVxD,KAAKqD,UAAYrD,KAAKqD,SAASC,GAAG,YACzCtD,KAAKqD,SAASE,KAAK,SAASC,QACrBxD,KAAK0J,KAAKpG,GAAG,aACpBtD,KAAK0J,KAAKlG,QAEdhD,EAAEgF,kBAhBN,IAmBJgV,cAAe,WAAA,GAGHU,GAFJpX,EAAU9D,KAAKuP,QAAQhM,KAAKgE,EAAMK,EAClC9D,GAAQrF,QACJyc,EAAmBlb,KAAKI,QAAQqJ,KAAK,yBACzC3F,EAAQ+W,QAAQ7F,YAAYpN,GAAS8R,WAAW,MAAM1W,OAAOoL,SAASxG,GAAS6B,KAAK,KAAMyR,GAC1Flb,KAAKI,QAAQqJ,KAAK,wBAAyByR,IAE3Clb,KAAK2a,iBAGbF,cAAe,WAAA,GAGHS,GAFJpX,EAAU9D,KAAKuP,QAAQhM,KAAKgE,EAAMK,EAClC9D,GAAQrF,QACJyc,EAAmBlb,KAAKI,QAAQqJ,KAAK,yBACzC3F,EAAQ+W,QAAQ7F,YAAYpN,GAAS8R,WAAW,MAAMyB,OAAO/M,SAASxG,GAAS6B,KAAK,KAAMyR,GAC1Flb,KAAKI,QAAQqJ,KAAK,wBAAyByR,IAE3Clb,KAAK0a,kBAGbA,eAAgB,WAAA,GAGRU,GAFAF,EAAmBlb,KAAKI,QAAQqJ,KAAK,wBAIzC,OAHAzJ,MAAKqb,oBACDD,EAAWpb,KAAKuP,QAAQ/Q,SAAS,MAAMqc,QAAQzM,SAASxG,GAAS6B,KAAK,KAAMyR,GAChFlb,KAAKI,QAAQqJ,KAAK,wBAAyByR,GACpCE,GAEXT,cAAe,WAAA,GAGPW,GAFAJ,EAAmBlb,KAAKI,QAAQqJ,KAAK,wBAIzC,OAHAzJ,MAAKqb,oBACDC,EAAUtb,KAAKuP,QAAQ/Q,SAAS,MAAM0E,OAAOkL,SAASxG,GAAS6B,KAAK,KAAMyR,GAC9Elb,KAAKI,QAAQqJ,KAAK,wBAAyByR,GACpCI,GAEXD,kBAAmB,WACfrb,KAAKuP,QAAQhM,KAAKgE,EAAMK,GAASoN,YAAYpN,GAAS8R,WAAW,OAErE1L,QAAS,WACL,GAAIjO,GAAOC,IACXwO,cAAazO,EAAK0O,gBAClB1O,EAAK0O,eAAiBoD,WAAW,WAC7B,GAAI/Q,GAAQf,EAAK0D,YAAYkF,KACzB5I,GAAK4W,QAAU7V,IACff,EAAK4W,MAAQ7V,EACbf,EAAKuO,OAAOxN,IAEhBf,EAAK0O,eAAiB,MACvB1O,EAAKF,QAAQ0M,QAEpByO,aAAc,WACNhb,KAAKyD,YAAYkF,MAAMlK,SACvBuB,KAAKyD,YAAYkF,IAAI,IACrB3I,KAAK2W,MAAQ,GACb3W,KAAK2O,YAAa,EAClB3O,KAAKyL,SAASG,WAAWvN,aAGjC0b,gBAAiB,SAAUvZ,GACvBA,EAAE+T,iBACF,IAAI+F,GAAUta,KAAKgT,MAAM9U,EAAEsC,EAAES,cAAcsa,eAAenZ,QAC1DpC,MAAK8a,WAAWR,IAEpBQ,WAAY,SAAUR,GAClB,GAAKA,EAAL,CAGA,GAAIrY,GAAMqY,EAAQrY,GAClBjC,MAAKyY,kBAAkBxW,MAc/BrD,GAAG4c,OAAOrT,GACNzB,EAAkBhI,EAAM+c,MAAM/b,QAC9BC,KAAM,SAAU+b,GACZ1b,KAAK+F,cAAgB2V,GAEzBpS,aAAc,WACVtJ,KAAK2b,WACL3b,KAAK4b,SAET5Q,WAAY,SAAU7F,GAClB,GAAIrF,GAAeE,KAAK+F,aACxBjG,GAAagG,aAAaX,IAE9BwW,SAAU,WACN,GAAgGvb,GAA5FN,EAAeE,KAAK+F,cAAenG,EAAUE,EAAaF,QAASic,EAAajc,EAAQ,EAC5FQ,GAAUR,EAAQwD,SACbhD,EAAQkD,GAAG,mBACZlD,EAAUR,EAAQmM,KAAK,YAAY3I,SACnChD,EAAQ,GAAG2Q,MAAM+K,QAAUD,EAAW9K,MAAM+K,QAC5C1b,EAAQ,GAAGoX,MAAQqE,EAAWrE,OAElC1X,EAAaoa,SAAWpa,EAAaM,QAAUA,EAAQgO,SAAS,2BAA2BA,SAASyN,EAAWE,WAAW9M,IAAI,UAAW,IAAIxF,MACzIuS,UAAWpc,EAAQ6J,KAAK,aACxBwS,aAAc,KACdjG,KAAM,UACNyB,iBAAiB,EACjBC,iBAAiB,IAErB9X,EAAQyT,OAAOqG,WAAW,cAE9BkC,MAAO,WACH,GAAkG1M,GAA9FpP,EAAeE,KAAK+F,cAAe3F,EAAUN,EAAaM,QAAS8b,EAAW,cAClFhN,GAAO9O,EAAQmD,KAAK2Y,GACfhN,EAAK,KACN9O,EAAQ0J,OAAO,kPAAkPA,OAAOhK,EAAaF,SACrRsP,EAAO9O,EAAQmD,KAAK2Y,IAExBpc,EAAaoP,KAAOA,EACpBpP,EAAawP,cAAgBpR,EAAEkC,EAAQ,GAAG+b,YAC1Crc,EAAasc,OAAShc,EAAQmD,KAAK,aACnCzD,EAAauc,WAAavc,EAAasc,OAAO7Y,KAAK,YAEvDuM,UAAW,SAAUhP,GAAV,GAEHwR,GADAxS,EAAeE,KAAK+F,aAExB,OAAIjF,KAAU3C,GAAuB,OAAV2C,GACvBwR,EAAexS,EAAa8I,QAAQK,QAAQ,GAC5CnI,EAAgC,gBAAjBwR,GAA4BA,EAAexS,EAAauU,aAAe/B,EAC/ExR,IAAU3C,GAAuB,OAAV2C,EAAiB,GAAKA,IAExDhB,EAAaiJ,oBAAqB,EACb,IAAjBjI,EAAMrC,QACNqB,EAAa0U,qBACb1U,EAAaiJ,oBAAqB,EAClC,IAEJjJ,EAAa8Y,mBAAmB9X,GAChChB,EAAa8K,yBADb9K,KAGJ6U,YAAa,WAAA,GACL7U,GAAeE,KAAK+F,cACpBuW,EAAexc,EAAa2L,SAASlG,QACrCzF,GAAa2L,SAAS/K,SAAS4b,KAC/Bxc,EAAa2L,SAAS/K,SAAS4b,GAAc3b,IAAI,YAAY,GACxDb,EAAaiJ,oBACdjJ,EAAa4C,QAAQuF,KAIjC8O,iBAAkB,SAAUwF,EAAUzb,GAClC,GAAIhB,GAAeE,KAAK+F,gBACnBjG,EAAagJ,mBAAmBhI,IAAoB,KAAVA,GAAgBhB,EAAagX,eAAeyF,EAAUzb,KAAWA,GAASyb,EAAS1W,WAC9H/F,EAAa2L,SAASlG,OAAOzF,EAAa2L,SAASzJ,UAAUua,EAASta,SAI9E0E,EAAoBjI,EAAM+c,MAAM/b,QAChCC,KAAM,SAAU+b,GACZ1b,KAAK+F,cAAgB2V,GAEzBpS,aAAc,WACV,GAAIxJ,GAAeE,KAAK+F,aACxB/F,MAAKwc,eACL1c,EAAaF,QAAQ6J,KAAK,WAAY,YAAY4J,OAClDrT,KAAK2b,WACL7b,EAAakT,MAAQ,GAAInM,OACzB/G,EAAawT,cAAgB,GAAIzM,OACjC7G,KAAKyc,WACL3c,EAAaoP,KAAOhR,EAAE,yDAAyD+X,YAAYnW,EAAayP,SACxGzP,EAAawP,cAAgBpR,EAAE4B,EAAaM,QAAQ,GAAG+b,aAE3DnR,WAAY,SAAU7F,EAAMrE,GAAhB,GACJhB,GAAeE,KAAK+F,cACpB2W,EAAgB5b,GAAShB,EAAaD,QAAQiB,KAC7C5C,GAAE8K,QAAQ7D,IAAWA,YAAgBzG,GAAMyG,KAAK0B,kBACjD1B,GAAQA,KAERjH,EAAE+W,cAAc9P,EAAK,KAAOA,EAAK,YAAczG,GAAMyG,KAAK2B,mBAAqBhH,EAAaD,QAAQyM,kBACpGxM,EAAa8L,WAAWzG,KAAKA,GAC7BrF,EAAagB,MAAM4b,KAG3BF,aAAc,WAAA,GACN1c,GAAeE,KAAK+F,cACpBlG,EAAUC,EAAaD,QACvB8c,EAAc9c,EAAQ0N,cACtBqF,EAAiC,aAApB/S,EAAQsN,QACrBN,EAAYhN,EAAQ+M,SAASC,SACjC8P,GAAcA,EAAcje,EAAMgN,SAASiR,GAAe7c,EAAayN,cACvEzN,EAAayN,cAAgB,SAAUpI,GACnC,MAAIyN,GACO,wBAA0BzN,EAAKqH,WAAY,EAAQ,mBAAqB,IAAM,sCAAwCrH,EAAKqH,WAAY,EAAQ,uBAAyB,IAAM,4BAAmCmQ,EAAYxX,GAAQ,uBAA8BrF,EAAaD,QAAQ+M,SAASG,UAAY,iBAAmBjN,EAAaD,QAAQ+M,SAASG,UAAY,wEAE9W,mJAA+JF,EAAY;GAG1L8O,SAAU,WACN,GAAI7b,GAAeE,KAAK+F,cAAenG,EAAUE,EAAaF,QAASQ,EAAUR,EAAQwD,OAAO,sBAC3FhD,GAAQ,KACTA,EAAUR,EAAQmM,KAAK,6DAA6D3I,SACpFhD,EAAQ,GAAG2Q,MAAM+K,QAAUlc,EAAQ,GAAGmR,MAAM+K,QAC5C1b,EAAQ,GAAGoX,MAAQ5X,EAAQ,GAAG4X,MAC9BtZ,EAAE,oEAAoE0Z,aAAahY,IAEvFE,EAAaM,QAAUA,EAAQgO,SAASxO,EAAQ,GAAGmc,WAAW9M,IAAI,UAAW,IAAIxF,MAC7EuM,KAAM,UACN4G,wBAAyBle,EAAM6X,OAC/BkB,iBAAiB,EACjBC,iBAAiB,IAErB5X,EAAa+c,cAAgB3e,EAAEkC,EAAQ,GAAG+b,aAE9CM,SAAU,WAAA,GAGE7J,GACAkK,EAMJC,EATAjd,EAAeE,KAAK+F,cAAewJ,EAAUzP,EAAa+c,cAAcre,SAAS,KAChF+Q,GAAQ,KACLqD,EAA8C,aAAjC9S,EAAaD,QAAQsN,QAClC2P,EAAgBlK,EAAa,OAAS,cAC1CrD,EAAUrR,EAAE,uFAAyF4e,EAAgB,uBAAuBtM,SAAS1Q,EAAa+c,gBAEtK/c,EAAayP,QAAUA,EACvBzP,EAAayP,QAAQ9F,KAAK,KAAM/K,EAAM6X,OAAS,YAC/CzW,EAAaM,QAAQqJ,KAAK,YAAa3J,EAAayP,QAAQ9F,KAAK,OAC7DsT,EAAYre,EAAMse,YAClBC,YAAand,EAAawT,cAC1B4J,KAAMpd,EAAakT,MACnB2J,YAAa7c,EAAayN,gBAE9B7O,EAAMye,KAAKrd,EAAayP,QAASwN,GACjCjd,EAAayP,QAAQ9F,KAAK,aAAa,IAE3CqG,UAAW,SAAUhP,GAAV,GACHhB,GAAeE,KAAK+F,cACpBqX,EAAYtd,EAAa8I,OAC7B,OAAI9H,KAAU3C,GAAuB,OAAV2C,EAChBhB,EAAa8I,QAAQK,SAEhCnJ,EAAa6P,SAAS7O,GACtBhB,EAAaiJ,oBAAqB,EAC9BjI,EAAMrC,QACNuB,KAAKqd,cAAcD,EAAWtc,GAC9BhB,EAAagZ,kBAAkBhY,IAE/BhB,EAAa0U,qBAEjB1U,EAAaiJ,oBAAqB,EAClCjJ,EAAa8K,yBATb9K,IAWJud,cAAe,SAAUD,EAAWtc,GAArB,GAGF0F,GACI8W,EAHTxd,EAAeE,KAAK+F,cACpBwX,EAAgBvd,KAAKwd,cAAcJ,EAAWtc,EAClD,KAAS0F,EAAM,EAAGA,EAAM+W,EAAc9e,OAAQ+H,IAC1C,IAAS8W,EAAI,EAAGA,EAAIxd,EAAakT,MAAMvU,OAAQ6e,IACvCxd,EAAagX,eAAehX,EAAakT,MAAMsK,GAAIC,EAAc/W,KACjE1G,EAAa2Y,kBAAkB3Y,EAAakT,MAAMsK,GAAGrb,MAKrEub,cAAe,SAAUJ,EAAWtc,GAArB,GAEF0F,GADL+W,IACJ,KAAS/W,EAAM,EAAGA,EAAM4W,EAAU3e,OAAQ+H,IAClC1F,EAAMgS,QAAQsK,EAAU5W,UACxB+W,EAAcnK,KAAKgK,EAAU5W,GAGrC,OAAO+W,IAEX5I,YAAa,WAAA,GAGAnO,GACDvE,EAHJnC,EAAeE,KAAK+F,cACpB0X,EAAY3d,EAAakT,MAAM/J,OACnC,KAASzC,EAAM,EAAGA,EAAMiX,EAAUhf,OAAQ+H,IAClCvE,EAAMwb,EAAUjX,GAAKvE,IACzBnC,EAAa2T,uBAAwB,EACrC3T,EAAa2Y,kBAAkBxW,EAE/Bwb,GAAUhf,SACVqB,EAAa2T,uBAAwB,EAChC3T,EAAaiJ,oBACdjJ,EAAa4C,QAAQuF,KAIjC8O,iBAAkB,SAAUwF,EAAUzb,GAClC,GAAIhB,GAAeE,KAAK+F,aACxB,OAAIjG,GAAa+I,iBAAmB0T,EAAS3b,SACzCd,EAAa8F,YAAY2W,GACzB,IAEAzb,EAAMrC,SAAWqC,EAAMgS,QAAQhT,EAAauS,cAAckK,UAAqBzb,EAAMgS,QAAQyJ,WAAsBvc,KAAK0d,SAAS5d,EAAauS,cAAckK,MACxJA,EAAS3b,QACTd,EAAa8F,YAAY2W,GAEzBA,EAAS5b,IAAI,WAAW,IAJhC,IAQJ+c,SAAU,SAAUC,GAChB,GAAI7d,GAAeE,KAAK+F,aACxB,OAAOjG,GAAakT,MAAMzP,KAAK,SAAU0P,GACrC,MAAOnT,GAAagX,eAAe7D,EAAM0K,QAIrDjf,EAAME,GAAGuJ,aAAazB,gBAAkBA,EACxChI,EAAME,GAAGuJ,aAAaxB,kBAAoBA,GAC5ChI,OAAOD,MAAMsH,QACRrH,OAAOD,OACE,kBAAVT,SAAwBA,OAAOgI,IAAMhI,OAAS,SAAUiI,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dropdowntree.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('dropdowntree/treeview', ['kendo.treeview'], f);\n}(function () {\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, keys = kendo.keys, DISABLED = 'k-state-disabled', SELECT = 'select', CHECKED = 'checked', proxy = $.proxy, DATABOUND = 'dataBound', CLICK = 'click', NS = '.kendoTreeView', INDETERMINATE = 'indeterminate', NAVIGATE = 'navigate', subGroup, TreeView = ui.TreeView;\n        function contentChild(filter) {\n            return function (node) {\n                var result = node.children('.k-animation-container');\n                if (!result.length) {\n                    result = node;\n                }\n                return result.children(filter);\n            };\n        }\n        subGroup = contentChild('.k-group');\n        var Tree = TreeView.extend({\n            init: function (element, options, dropdowntree) {\n                var that = this;\n                that.dropdowntree = dropdowntree;\n                TreeView.fn.init.call(that, element, options);\n                if (that.dropdowntree._isMultipleSelection()) {\n                    that.wrapper.on(CLICK + NS, '.k-in.k-state-selected', proxy(that._clickSelectedItem, that));\n                }\n            },\n            _checkOnSelect: function (e) {\n                if (!e.isDefaultPrevented()) {\n                    var dataItem = this.dataItem(e.node);\n                    dataItem.set('checked', !dataItem.checked);\n                }\n            },\n            _setCheckedValue: function (node, value) {\n                node.set(CHECKED, value);\n            },\n            _click: function (e) {\n                var that = this;\n                if (that.dropdowntree._isMultipleSelection()) {\n                    that.one('select', that._checkOnSelect);\n                }\n                TreeView.fn._click.call(that, e);\n            },\n            _clickSelectedItem: function (e) {\n                var that = this, node = $(e.currentTarget);\n                that.one('select', that._checkOnSelect);\n                if (!that._trigger(SELECT, node)) {\n                    that.dataItem(node).set('selected', false);\n                }\n            },\n            defaultrefresh: function (e) {\n                var node = e.node;\n                var action = e.action;\n                var items = e.items;\n                var parentNode = this.wrapper;\n                var options = this.options;\n                var loadOnDemand = options.loadOnDemand;\n                var checkChildren = options.checkboxes && options.checkboxes.checkChildren;\n                var i;\n                if (this._skip) {\n                    return;\n                }\n                if (e.field) {\n                    if (!items[0] || !items[0].level) {\n                        return;\n                    }\n                    return this._updateNodes(items, e.field);\n                }\n                if (node) {\n                    parentNode = this.findByUid(node.uid);\n                    this._progress(parentNode, false);\n                }\n                if (checkChildren && action != 'remove') {\n                    var bubble = false;\n                    for (i = 0; i < items.length; i++) {\n                        if ('checked' in items[i]) {\n                            bubble = true;\n                            break;\n                        }\n                    }\n                    if (!bubble && node && node.checked) {\n                        for (i = 0; i < items.length; i++) {\n                            items[i].checked = true;\n                        }\n                    }\n                }\n                if (action == 'add') {\n                    this._appendItems(e.index, items, parentNode);\n                } else if (action == 'remove') {\n                    this._remove(this.findByUid(items[0].uid), false);\n                } else if (action == 'itemchange') {\n                    this._updateNodes(items);\n                } else if (action == 'itemloaded') {\n                    this._refreshChildren(parentNode, items, e.index);\n                } else {\n                    this._refreshRoot(items);\n                }\n                if (action != 'remove') {\n                    for (i = 0; i < items.length; i++) {\n                        if (!loadOnDemand || items[i].expanded) {\n                            items[i].load();\n                        }\n                    }\n                }\n                this.trigger(DATABOUND, { node: node ? parentNode : undefined });\n                this.dropdowntree._treeViewDataBound({\n                    node: node ? parentNode : undefined,\n                    sender: this\n                });\n                if (this.options.checkboxes.checkChildren) {\n                    this.updateIndeterminate();\n                }\n            },\n            _previousVisible: function (node) {\n                var that = this, lastChild, result;\n                if (!node.length || node.prev().length) {\n                    if (node.length) {\n                        result = node.prev();\n                    } else {\n                        result = that.root.children().last();\n                    }\n                    while (that._expanded(result)) {\n                        lastChild = subGroup(result).children().last();\n                        if (!lastChild.length) {\n                            break;\n                        }\n                        result = lastChild;\n                    }\n                } else {\n                    result = that.parent(node) || node;\n                    if (!result.length) {\n                        if (that.dropdowntree.checkAll && that.dropdowntree.checkAll.is(':visible')) {\n                            that.dropdowntree.checkAll.find('.k-checkbox').focus();\n                        } else if (that.dropdowntree.filterInput) {\n                            that.dropdowntree.filterInput.focus();\n                        } else {\n                            that.dropdowntree.wrapper.focus();\n                        }\n                    }\n                }\n                return result;\n            },\n            _keydown: function (e) {\n                var that = this, key = e.keyCode, target, focused = that.current(), expanded = that._expanded(focused), checkbox = focused.find('.k-checkbox-wrapper:first :checkbox'), rtl = kendo.support.isRtl(that.element);\n                if (e.target != e.currentTarget) {\n                    return;\n                }\n                if (!rtl && key == keys.RIGHT || rtl && key == keys.LEFT) {\n                    if (expanded) {\n                        target = that._nextVisible(focused);\n                    } else if (!focused.find('.k-in:first').hasClass(DISABLED)) {\n                        that.expand(focused);\n                    }\n                } else if (!rtl && key == keys.LEFT || rtl && key == keys.RIGHT) {\n                    if (expanded && !focused.find('.k-in:first').hasClass(DISABLED)) {\n                        that.collapse(focused);\n                    } else {\n                        target = that.parent(focused);\n                        if (!that._enabled(target)) {\n                            target = undefined;\n                        }\n                    }\n                } else if (key == keys.DOWN) {\n                    target = that._nextVisible(focused);\n                } else if (key == keys.UP && !e.altKey) {\n                    target = that._previousVisible(focused);\n                } else if (key == keys.HOME) {\n                    target = that._nextVisible($());\n                } else if (key == keys.END) {\n                    target = that._previousVisible($());\n                } else if (key == keys.ENTER && !focused.find('.k-in:first').hasClass(DISABLED)) {\n                    if (!focused.find('.k-in:first').hasClass('k-state-selected')) {\n                        if (!that._trigger(SELECT, focused)) {\n                            that.select(focused);\n                        }\n                    }\n                } else if (key == keys.SPACEBAR && checkbox.length && !focused.find('.k-in:first').hasClass(DISABLED)) {\n                    checkbox.prop(CHECKED, !checkbox.prop(CHECKED)).data(INDETERMINATE, false).prop(INDETERMINATE, false);\n                    that._checkboxChange({ target: checkbox });\n                    target = focused;\n                } else if (e.altKey && key === keys.UP || key === keys.ESC) {\n                    that._closePopup();\n                }\n                if (target) {\n                    e.preventDefault();\n                    if (focused[0] != target[0]) {\n                        that._trigger(NAVIGATE, target);\n                        that.current(target);\n                    }\n                }\n            },\n            _closePopup: function () {\n                this.dropdowntree.close();\n                this.dropdowntree.wrapper.focus();\n            },\n            refresh: function (e) {\n                this.defaultrefresh(e);\n                if (this.dropdowntree.options.skipUpdateOnBind) {\n                    return;\n                }\n                if (e.action === 'itemchange') {\n                    if (this.dropdowntree._isMultipleSelection()) {\n                        if (e.field === 'checked') {\n                            this.dropdowntree._checkValue(e.items[0]);\n                        }\n                    } else {\n                        if (e.field !== 'checked' && e.field !== 'expanded' && e.items[0].selected) {\n                            this.dropdowntree._selectValue(e.items[0]);\n                        }\n                    }\n                } else {\n                    this.dropdowntree.refresh(e);\n                }\n            }\n        });\n        kendo.ui._dropdowntree = Tree;\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dropdowntree', [\n        'dropdowntree/treeview',\n        'kendo.popup'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dropdowntree',\n        name: 'DropDownTree',\n        category: 'web',\n        description: 'The DropDownTree widget displays a hierarchy of items and allows the selection of single or multiple items.',\n        depends: [\n            'treeview',\n            'popup'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Widget = ui.Widget, TreeView = ui._dropdowntree, ObservableArray = kendo.data.ObservableArray, ObservableObject = kendo.data.ObservableObject, extend = $.extend, activeElement = kendo._activeElement, ns = '.kendoDropDownTree', keys = kendo.keys, support = kendo.support, HIDDENCLASS = 'k-hidden', WIDTH = 'width', browser = support.browser, outerWidth = kendo._outerWidth, DOT = '.', DISABLED = 'disabled', READONLY = 'readonly', STATEDISABLED = 'k-state-disabled', ARIA_DISABLED = 'aria-disabled', HOVER = 'k-state-hover', FOCUSED = 'k-state-focused', HOVEREVENTS = 'mouseenter' + ns + ' mouseleave' + ns, TABINDEX = 'tabindex', CLICK = 'click', OPEN = 'open', CLOSE = 'close', CHANGE = 'change', quotRegExp = /\"/g, proxy = $.proxy;\n        var DropDownTree = kendo.ui.Widget.extend({\n            init: function (element, options) {\n                this.ns = ns;\n                kendo.ui.Widget.fn.init.call(this, element, options);\n                this._selection = this._getSelection();\n                this._focusInputHandler = $.proxy(this._focusInput, this);\n                this._initial = this.element.val();\n                this._values = [];\n                var value = this.options.value;\n                if (value === null || !value.length) {\n                    this._noInitialValue = true;\n                }\n                if (!this._isNullorUndefined(value)) {\n                    this._valueMethodCalled = true;\n                    this._values = $.isArray(value) ? value.slice(0) : [value];\n                }\n                this._inputTemplate();\n                this._accessors();\n                this._setTreeViewOptions(this.options);\n                this._dataSource();\n                this._selection._initWrapper();\n                this._placeholder(true);\n                this._tabindex();\n                this.wrapper.data(TABINDEX, this.wrapper.attr(TABINDEX));\n                this.tree = $('<div/>').attr({\n                    tabIndex: -1,\n                    'aria-hidden': true\n                });\n                this.list = $('<div class=\\'k-list-container\\'/>').append(this.tree);\n                this._header();\n                this._noData();\n                this._footer();\n                this._reset();\n                this._popup();\n                this.popup.one('open', proxy(this._popupOpen, this));\n                this._clearButton();\n                this._filterHeader();\n                this._treeview();\n                this._renderFooter();\n                this._checkAll();\n                this._enable();\n                this._toggleCloseVisibility();\n                if (!this.options.autoBind) {\n                    var text = options.text || '';\n                    if (!this._isNullorUndefined(options.value)) {\n                        this._preselect(options.value);\n                    } else if (text) {\n                        this._textAccessor(text);\n                    } else if (options.placeholder) {\n                        this._placeholder(true);\n                    }\n                }\n                var disabled = $(this.element).parents('fieldset').is(':disabled');\n                if (disabled) {\n                    this.enable(false);\n                }\n                this._valueMethodCalled = false;\n                kendo.notify(this);\n            },\n            _preselect: function (data, value) {\n                this._selection._preselect(data, value);\n            },\n            _setTreeViewOptions: function (options) {\n                var treeviewOptions = {\n                    autoBind: options.autoBind,\n                    checkboxes: options.checkboxes,\n                    dataImageUrlField: options.dataImageUrlField,\n                    dataSpriteCssClassField: options.dataSpriteCssClassField,\n                    dataTextField: options.dataTextField,\n                    dataUrlField: options.dataUrlField,\n                    loadOnDemand: options.loadOnDemand\n                };\n                this.options.treeview = $.extend({}, treeviewOptions, this.options.treeview);\n                if (options.template) {\n                    this.options.treeview.template = options.template;\n                }\n            },\n            _dataSource: function () {\n                var rootDataSource = this.options.dataSource;\n                this.dataSource = kendo.data.HierarchicalDataSource.create(rootDataSource);\n                if (rootDataSource) {\n                    $.extend(this.options.treeview, { dataSource: this.dataSource });\n                }\n            },\n            _popupOpen: function () {\n                var popup = this.popup;\n                popup.wrapper = kendo.wrap(popup.element);\n            },\n            _getSelection: function () {\n                if (this._isMultipleSelection()) {\n                    return new ui.DropDownTree.MultipleSelection(this);\n                } else {\n                    return new ui.DropDownTree.SingleSelection(this);\n                }\n            },\n            setDataSource: function (dataSource) {\n                this.dataSource = dataSource;\n                this.treeview.setDataSource(dataSource);\n            },\n            _isMultipleSelection: function () {\n                return this.options && (this.options.treeview.checkboxes || this.options.checkboxes);\n            },\n            options: {\n                name: 'DropDownTree',\n                animation: {},\n                autoBind: true,\n                autoClose: true,\n                autoWidth: false,\n                clearButton: true,\n                dataTextField: '',\n                dataValueField: '',\n                dataImageUrlField: '',\n                dataSpriteCssClassField: '',\n                dataUrlField: '',\n                delay: 500,\n                enabled: true,\n                enforceMinLength: false,\n                filter: 'none',\n                height: 200,\n                ignoreCase: true,\n                index: 0,\n                loadOnDemand: false,\n                messages: {\n                    'singleTag': 'item(s) selected',\n                    'clear': 'clear',\n                    'deleteTag': 'delete'\n                },\n                minLength: 1,\n                checkboxes: false,\n                noDataTemplate: 'No data found.',\n                placeholder: '',\n                checkAll: false,\n                checkAllTemplate: 'Check all',\n                tagMode: 'multiple',\n                template: null,\n                text: null,\n                treeview: {},\n                valuePrimitive: false,\n                footerTemplate: '',\n                headerTemplate: '',\n                value: null,\n                valueTemplate: null,\n                popup: null\n            },\n            events: [\n                'open',\n                'close',\n                'dataBound',\n                CHANGE,\n                'select',\n                'filtering'\n            ],\n            focus: function () {\n                this.wrapper.focus();\n            },\n            dataItem: function (node) {\n                return this.treeview.dataItem(node);\n            },\n            readonly: function (readonly) {\n                this._editable({\n                    readonly: readonly === undefined ? true : readonly,\n                    disable: false\n                });\n                this._toggleCloseVisibility();\n            },\n            enable: function (enable) {\n                this._editable({\n                    readonly: false,\n                    disable: !(enable = enable === undefined ? true : enable)\n                });\n                this._toggleCloseVisibility();\n            },\n            toggle: function (open) {\n                this._toggle(open);\n            },\n            open: function () {\n                var popup = this.popup;\n                if (!this.options.autoBind && !this.dataSource.data().length) {\n                    this.treeview._progress(true);\n                    if (this._isFilterEnabled()) {\n                        this._search();\n                    } else {\n                        this.dataSource.fetch();\n                    }\n                }\n                if (popup.visible() || !this._allowOpening()) {\n                    return;\n                }\n                if (this._isMultipleSelection()) {\n                    popup.element.addClass('k-multiple-selection');\n                }\n                popup.element.addClass('k-popup-dropdowntree');\n                popup.one('activate', this._focusInputHandler);\n                popup._hovered = true;\n                popup.open();\n            },\n            close: function () {\n                this.popup.close();\n            },\n            search: function (word) {\n                var options = this.options;\n                var filter;\n                clearTimeout(this._typingTimeout);\n                if (!options.enforceMinLength && !word.length || word.length >= options.minLength) {\n                    filter = this._getFilter(word);\n                    if (this.trigger('filtering', { filter: filter }) || $.isArray(this.options.dataTextField)) {\n                        return;\n                    }\n                    this._filtering = true;\n                    this.treeview.dataSource.filter(filter);\n                }\n            },\n            _getFilter: function (word) {\n                return {\n                    field: this.options.dataTextField,\n                    operator: this.options.filter,\n                    value: word,\n                    ignoreCase: this.options.ignoreCase\n                };\n            },\n            refresh: function () {\n                var data = this.treeview.dataSource.flatView();\n                this._renderFooter();\n                this._renderNoData();\n                if (this.filterInput && this.checkAll) {\n                    this.checkAll.toggle(!this.filterInput.val().length);\n                }\n                this.tree.toggle(!!data.length);\n                $(this.noData).toggle(!data.length);\n            },\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n                this._setTreeViewOptions(options);\n                this._dataSource();\n                if (this.options.treeview) {\n                    this.treeview.setOptions(this.options.treeview);\n                }\n                if (options.height && this.tree) {\n                    this.tree.css('max-height', options.height);\n                }\n                this._header();\n                this._noData();\n                this._footer();\n                this._renderFooter();\n                this._renderNoData();\n                if (this.span && (this._isMultipleSelection() || this.span.hasClass('k-readonly'))) {\n                    this._placeholder(true);\n                }\n                this._inputTemplate();\n                this._accessors();\n                this._filterHeader();\n                this._checkAll();\n                this._enable();\n                if (options && (options.enable || options.enabled)) {\n                    this.enable(true);\n                }\n                this._clearButton();\n            },\n            destroy: function () {\n                kendo.ui.Widget.fn.destroy.call(this);\n                if (this.treeview) {\n                    this.treeview.destroy();\n                }\n                this.popup.destroy();\n                this.wrapper.off(ns);\n                this._clear.off(ns);\n                this._inputWrapper.off(ns);\n                if (this.filterInput) {\n                    this.filterInput.off(ns);\n                }\n                if (this.tagList) {\n                    this.tagList.off(ns);\n                }\n                kendo.unbind(this.tagList);\n                if (this.options.checkAll && this.checkAll) {\n                    this.checkAll.off(ns);\n                }\n                if (this._form) {\n                    this._form.off('reset', this._resetHandler);\n                }\n            },\n            setValue: function (value) {\n                value = $.isArray(value) || value instanceof ObservableArray ? value.slice(0) : [value];\n                this._values = value;\n            },\n            items: function () {\n                this.treeview.dataItems();\n            },\n            value: function (value) {\n                var that = this;\n                if (value) {\n                    if (that.filterInput && that.dataSource._filter) {\n                        that._filtering = true;\n                        that.dataSource.filter({});\n                    } else if (!that.dataSource.data().length) {\n                        that.dataSource.fetch(function () {\n                            that._selection._setValue(value);\n                        });\n                        return;\n                    }\n                }\n                return that._selection._setValue(value);\n            },\n            text: function (text) {\n                var loweredText;\n                var ignoreCase = this.options.ignoreCase;\n                text = text === null ? '' : text;\n                if (text !== undefined && !this._isMultipleSelection()) {\n                    if (typeof text !== 'string') {\n                        this._textAccessor(text);\n                        return;\n                    }\n                    loweredText = ignoreCase ? text : text.toLowerCase();\n                    this._selectItemByText(loweredText);\n                    this._textAccessor(loweredText);\n                } else {\n                    return this._textAccessor();\n                }\n            },\n            _header: function () {\n                var list = this;\n                var header = $(list.header);\n                var template = list.options.headerTemplate;\n                this._angularElement(header, 'cleanup');\n                kendo.destroy(header);\n                header.remove();\n                if (!template) {\n                    list.header = null;\n                    return;\n                }\n                var headerTemplate = typeof template !== 'function' ? kendo.template(template) : template;\n                header = $(headerTemplate({}));\n                list.header = header[0] ? header : null;\n                list.list.prepend(header);\n                this._angularElement(list.header, 'compile');\n            },\n            _noData: function () {\n                var list = this;\n                var noData = $(list.noData);\n                var template = list.options.noDataTemplate;\n                list.angular('cleanup', function () {\n                    return { elements: noData };\n                });\n                kendo.destroy(noData);\n                noData.remove();\n                if (!template) {\n                    list.noData = null;\n                    return;\n                }\n                list.noData = $('<div class=\"k-nodata\" style=\"display:none\"><div></div></div>').appendTo(list.list);\n                list.noDataTemplate = typeof template !== 'function' ? kendo.template(template) : template;\n            },\n            _renderNoData: function () {\n                var list = this;\n                var noData = list.noData;\n                if (!noData) {\n                    return;\n                }\n                this._angularElement(noData, 'cleanup');\n                noData.children(':first').html(list.noDataTemplate({ instance: list }));\n                this._angularElement(noData, 'compile');\n            },\n            _footer: function () {\n                var list = this;\n                var footer = $(list.footer);\n                var template = list.options.footerTemplate;\n                this._angularElement(footer, 'cleanup');\n                kendo.destroy(footer);\n                footer.remove();\n                if (!template) {\n                    list.footer = null;\n                    return;\n                }\n                list.footer = $('<div class=\"k-footer\"></div>').appendTo(list.list);\n                list.footerTemplate = typeof template !== 'function' ? kendo.template(template) : template;\n            },\n            _renderFooter: function () {\n                var list = this;\n                var footer = list.footer;\n                if (!footer) {\n                    return;\n                }\n                this._angularElement(footer, 'cleanup');\n                footer.html(list.footerTemplate({ instance: list }));\n                this._angularElement(footer, 'compile');\n            },\n            _enable: function () {\n                var that = this, options = that.options, disabled = that.element.is('[disabled]');\n                if (options.enable !== undefined) {\n                    options.enabled = options.enable;\n                }\n                if (!options.enabled || disabled) {\n                    that.enable(false);\n                } else {\n                    that.readonly(that.element.is('[readonly]'));\n                }\n            },\n            _adjustListWidth: function () {\n                var that = this, list = that.list, width = list[0].style.width, wrapper = that.wrapper, computedStyle, computedWidth;\n                if (!list.data(WIDTH) && width) {\n                    return;\n                }\n                computedStyle = window.getComputedStyle ? window.getComputedStyle(wrapper[0], null) : 0;\n                computedWidth = parseFloat(computedStyle && computedStyle.width) || outerWidth(wrapper);\n                if (computedStyle && browser.msie) {\n                    computedWidth += parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight) + parseFloat(computedStyle.borderLeftWidth) + parseFloat(computedStyle.borderRightWidth);\n                }\n                if (list.css('box-sizing') !== 'border-box') {\n                    width = computedWidth - (outerWidth(list) - list.width());\n                } else {\n                    width = computedWidth;\n                }\n                list.css({\n                    fontFamily: wrapper.css('font-family'),\n                    width: that.options.autoWidth ? 'auto' : width,\n                    minWidth: width,\n                    whiteSpace: that.options.autoWidth ? 'nowrap' : 'normal'\n                }).data(WIDTH, width);\n                return true;\n            },\n            _reset: function () {\n                var that = this, element = that.element, formId = element.attr('form'), form = formId ? $('#' + formId) : element.closest('form');\n                if (form[0]) {\n                    that._resetHandler = function () {\n                        setTimeout(function () {\n                            that.value(that._initial);\n                        });\n                    };\n                    that._form = form.on('reset', that._resetHandler);\n                }\n            },\n            _popup: function () {\n                var list = this;\n                list.popup = new ui.Popup(list.list, extend({}, list.options.popup, {\n                    anchor: list.wrapper,\n                    open: proxy(list._openHandler, list),\n                    close: proxy(list._closeHandler, list),\n                    animation: list.options.animation,\n                    isRtl: support.isRtl(list.wrapper),\n                    autosize: list.options.autoWidth\n                }));\n            },\n            _angularElement: function (element, action) {\n                if (!element) {\n                    return;\n                }\n                this.angular(action, function () {\n                    return { elements: element };\n                });\n            },\n            _allowOpening: function () {\n                return this.options.noDataTemplate || this.treeview.dataSource.flatView().length;\n            },\n            _placeholder: function (show) {\n                if (this.span) {\n                    this.span.toggleClass('k-readonly', show).text(show ? this.options.placeholder : '');\n                }\n            },\n            _currentValue: function (dataItem) {\n                var currentValue = this._value(dataItem);\n                if (!currentValue && currentValue !== 0) {\n                    currentValue = dataItem;\n                }\n                return currentValue;\n            },\n            _checkValue: function (dataItem) {\n                var value = '';\n                var indexOfValue = -1;\n                var currentValue = this.value();\n                var isMultiple = this.options.tagMode === 'multiple';\n                if (dataItem || dataItem === 0) {\n                    if (dataItem.level) {\n                        dataItem._level = dataItem.level();\n                    }\n                    value = this._currentValue(dataItem);\n                    indexOfValue = currentValue.indexOf(value);\n                }\n                if (dataItem.checked) {\n                    var alreadyAddedTag = $.grep(this._tags, function (item) {\n                        return item.uid === dataItem._tagUid;\n                    });\n                    if (alreadyAddedTag.length) {\n                        return;\n                    }\n                    var itemToAdd = new ObservableObject(dataItem.toJSON());\n                    dataItem._tagUid = itemToAdd.uid;\n                    this._tags.push(itemToAdd);\n                    if (this._tags.length === 1) {\n                        this.span.hide();\n                        if (!isMultiple) {\n                            this._multipleTags.push(itemToAdd);\n                        }\n                    }\n                    if (indexOfValue === -1) {\n                        currentValue.push(value);\n                        this.setValue(currentValue);\n                    }\n                } else {\n                    var itemToRemove = this._tags.find(function (item) {\n                        return item.uid === dataItem._tagUid;\n                    });\n                    var idx = this._tags.indexOf(itemToRemove);\n                    if (idx !== -1) {\n                        this._tags.splice(idx, 1);\n                    } else {\n                        this._treeViewCheckAllCheck(dataItem);\n                        return;\n                    }\n                    if (this._tags.length === 0) {\n                        this.span.show();\n                        if (!isMultiple) {\n                            this._multipleTags.splice(0, 1);\n                        }\n                    }\n                    if (indexOfValue !== -1) {\n                        currentValue.splice(indexOfValue, 1);\n                        this.setValue(currentValue);\n                    }\n                }\n                this._treeViewCheckAllCheck(dataItem);\n                if (!this._preventChangeTrigger && !this._valueMethodCalled && !this._noInitialValue) {\n                    this.trigger(CHANGE);\n                }\n                if (this.options.autoClose && this.popup.visible()) {\n                    this.close();\n                    this.wrapper.focus();\n                }\n                this.popup.position();\n                this._toggleCloseVisibility();\n                this._updateSelectedOptions();\n            },\n            _updateSelectedOptions: function () {\n                if (this.element[0].tagName.toLowerCase() !== 'select') {\n                    return;\n                }\n                var selectedItems = this._tags;\n                var options = '';\n                var dataItem = null;\n                var value = null;\n                if (selectedItems.length) {\n                    for (var idx = 0; idx < selectedItems.length; idx++) {\n                        dataItem = selectedItems[idx];\n                        value = this._value(dataItem);\n                        options += this._option(value, this._text(dataItem), true);\n                    }\n                }\n                this.element.html(options);\n            },\n            _option: function (dataValue, dataText, selected) {\n                var option = '<option';\n                if (dataValue !== undefined) {\n                    dataValue += '';\n                    if (dataValue.indexOf('\"') !== -1) {\n                        dataValue = dataValue.replace(quotRegExp, '&quot;');\n                    }\n                    option += ' value=\"' + dataValue + '\"';\n                }\n                if (selected) {\n                    option += ' selected';\n                }\n                option += '>';\n                if (dataText !== undefined) {\n                    option += kendo.htmlEncode(dataText);\n                }\n                return option += '</option>';\n            },\n            _selectValue: function (dataItem) {\n                var value = '';\n                var text = '';\n                if (dataItem || dataItem === 0) {\n                    if (dataItem.level) {\n                        dataItem._level = dataItem.level();\n                    }\n                    text = this._text(dataItem) || dataItem;\n                    value = this._currentValue(dataItem);\n                }\n                if (value === null) {\n                    value = '';\n                }\n                this.setValue(value);\n                this._textAccessor(text, dataItem);\n                this._accessor(value);\n                if (!this._valueMethodCalled) {\n                    this.trigger(CHANGE);\n                }\n                this._valueMethodCalled = false;\n                if (this.options.autoClose && this.popup.visible()) {\n                    this.close();\n                    this.wrapper.focus();\n                }\n                this.popup.position();\n                this._toggleCloseVisibility();\n            },\n            _clearClick: function (e) {\n                e.stopPropagation();\n                this._clearTextAndValue();\n            },\n            _clearTextAndValue: function () {\n                this.setValue([]);\n                this._clearInput();\n                this._clearText();\n                this._selection._clearValue();\n                this.popup.position();\n                this._toggleCloseVisibility();\n            },\n            _clearText: function () {\n                if (this.options.placeholder) {\n                    this._placeholder(true);\n                } else {\n                    if (this.span) {\n                        this.span.html('');\n                    }\n                }\n            },\n            _inputTemplate: function () {\n                var template = this.options.valueTemplate;\n                if (!template) {\n                    template = $.proxy(kendo.template('#:this._text(data)#', { useWithBlock: false }), this);\n                } else {\n                    template = kendo.template(template);\n                }\n                this.valueTemplate = template;\n            },\n            _assignInstance: function (text, value) {\n                var dataTextField = this.options.dataTextField;\n                var dataItem = {};\n                if (dataTextField) {\n                    assign(dataItem, dataTextField.split(DOT), text);\n                    assign(dataItem, this.options.dataValueField.split(DOT), value);\n                    dataItem = new ObservableObject(dataItem);\n                } else {\n                    dataItem = text;\n                }\n                return dataItem;\n            },\n            _textAccessor: function (text, dataItem) {\n                var valueTemplate = this.valueTemplate;\n                var span = this.span;\n                if (text === undefined) {\n                    return span.text();\n                }\n                span.removeClass('k-readonly');\n                if (!dataItem && ($.isPlainObject(text) || text instanceof ObservableObject)) {\n                    dataItem = text;\n                }\n                if (!dataItem) {\n                    dataItem = this._assignInstance(text, this._accessor());\n                }\n                var getElements = function () {\n                    return {\n                        elements: span.get(),\n                        data: [{ dataItem: dataItem }]\n                    };\n                };\n                this.angular('cleanup', getElements);\n                try {\n                    span.html(valueTemplate(dataItem));\n                } catch (e) {\n                    if (span) {\n                        span.html('');\n                    }\n                }\n                this.angular('compile', getElements);\n            },\n            _accessors: function () {\n                var element = this.element;\n                var options = this.options;\n                var getter = kendo.getter;\n                var textField = element.attr(kendo.attr('text-field'));\n                var valueField = element.attr(kendo.attr('value-field'));\n                var getterFunction = function (field) {\n                    if ($.isArray(field)) {\n                        var count = field.length;\n                        var levels = $.map(field, function (x) {\n                            return function (d) {\n                                return d[x];\n                            };\n                        });\n                        return function (dataItem) {\n                            var level = dataItem._level;\n                            if (!level && level !== 0) {\n                                return;\n                            }\n                            return levels[Math.min(level, count - 1)](dataItem);\n                        };\n                    } else {\n                        return getter(field);\n                    }\n                };\n                if (!options.dataTextField && textField) {\n                    options.dataTextField = textField;\n                }\n                if (!options.dataValueField && valueField) {\n                    options.dataValueField = valueField;\n                }\n                options.dataTextField = options.dataTextField || 'text';\n                options.dataValueField = options.dataValueField || 'value';\n                this._text = getterFunction(options.dataTextField);\n                this._value = getterFunction(options.dataValueField);\n            },\n            _accessor: function (value, idx) {\n                return this._accessorInput(value, idx);\n            },\n            _accessorInput: function (value) {\n                var element = this.element[0];\n                if (value === undefined) {\n                    return element.value;\n                } else {\n                    if (value === null) {\n                        value = '';\n                    }\n                    element.value = value;\n                }\n            },\n            _clearInput: function () {\n                var element = this.element[0];\n                element.value = '';\n            },\n            _clearButton: function () {\n                var clearTitle = this.options.messages && this.options.messages.clear ? this.options.messages.clear : 'clear';\n                if (!this._clear) {\n                    this._clear = $('<span unselectable=\"on\" class=\"k-icon k-clear-value k-i-close\" title=\"' + clearTitle + '\"></span>').attr({\n                        'role': 'button',\n                        'tabIndex': -1\n                    });\n                }\n                if (this.options.clearButton) {\n                    this._clear.insertAfter(this.span);\n                    this.wrapper.addClass('k-dropdowntree-clearable');\n                } else {\n                    if (!this.options.clearButton) {\n                        this._clear.remove();\n                    }\n                }\n            },\n            _toggleCloseVisibility: function () {\n                var isReadOnly = this.element.attr(READONLY);\n                var hasValue = this.value() && !this._isMultipleSelection() || this.value().length;\n                var valueDoesNotEqualPlaceHolder = this.element.val() && this.element.val() !== this.options.placeholder;\n                if (!isReadOnly && (hasValue || valueDoesNotEqualPlaceHolder)) {\n                    this._showClear();\n                } else {\n                    this._hideClear();\n                }\n            },\n            _showClear: function () {\n                if (this._clear) {\n                    this._clear.removeClass(HIDDENCLASS);\n                }\n            },\n            _hideClear: function () {\n                if (this._clear) {\n                    this._clear.addClass(HIDDENCLASS);\n                }\n            },\n            _openHandler: function (e) {\n                this._adjustListWidth();\n                if (this.trigger(OPEN)) {\n                    e.preventDefault();\n                } else {\n                    this.wrapper.attr('aria-expanded', true);\n                    this.tree.attr('aria-hidden', false).attr('role', 'tree');\n                }\n            },\n            _closeHandler: function (e) {\n                if (this.trigger(CLOSE)) {\n                    e.preventDefault();\n                } else {\n                    this.wrapper.attr('aria-expanded', false);\n                    this.tree.attr('aria-hidden', true);\n                }\n            },\n            _treeview: function () {\n                var that = this;\n                if (that.options.height) {\n                    that.tree.css('max-height', that.options.height);\n                }\n                that.tree.attr('id', kendo.guid());\n                that.treeview = new TreeView(that.tree, extend({ select: that.options.select }, that.options.treeview), that);\n                that.dataSource = that.treeview.dataSource;\n            },\n            _treeViewDataBound: function (e) {\n                if (e.node && this._prev && this._prev.length) {\n                    e.sender.expand(e.node);\n                }\n                if (this._filtering) {\n                    if (!e.node) {\n                        this._filtering = false;\n                    }\n                    if (!this._isMultipleSelection()) {\n                        this._deselectItem(e);\n                    }\n                    return;\n                }\n                if (!this.treeview) {\n                    this.treeview = e.sender;\n                }\n                if (!e.node) {\n                    var rootItems = e.sender.dataSource.data();\n                    this._checkLoadedItems(rootItems);\n                    if (this._noInitialValue) {\n                        this._noInitialValue = false;\n                    }\n                } else {\n                    var rootItem = e.sender.dataItem(e.node);\n                    if (rootItem) {\n                        var subItems = rootItem.children.data();\n                        this._checkLoadedItems(subItems);\n                    }\n                }\n                this.trigger('dataBound', e);\n            },\n            _deselectItem: function (e) {\n                var items = [];\n                if (!e.node) {\n                    items = e.sender.dataSource.data();\n                } else {\n                    var rootItem = e.sender.dataItem(e.node);\n                    if (rootItem) {\n                        items = rootItem.children.data();\n                    }\n                }\n                for (var i = 0; i < items.length; i++) {\n                    if (items[i].selected && !this._valueComparer(items[i], this.value())) {\n                        items[i].set('selected', false);\n                    }\n                }\n            },\n            _checkLoadedItems: function (items) {\n                var value = this.value();\n                if (!items) {\n                    return;\n                }\n                for (var idx = 0; idx < items.length; idx++) {\n                    this._selection._checkLoadedItem(items[idx], value);\n                }\n            },\n            _treeViewCheckAllCheck: function (dataItem) {\n                if (this.options.checkAll && this.checkAll) {\n                    this._getAllChecked();\n                    if (dataItem.checked) {\n                        this._checkCheckAll();\n                    } else {\n                        this._uncheckCheckAll();\n                    }\n                }\n            },\n            _checkCheckAll: function () {\n                var checkAllCheckbox = this.checkAll.find('.k-checkbox');\n                if (this._allItemsAreChecked) {\n                    checkAllCheckbox.prop('checked', true).prop('indeterminate', false);\n                } else {\n                    checkAllCheckbox.prop('indeterminate', true);\n                }\n            },\n            _uncheckCheckAll: function () {\n                var checkAllCheckbox = this.checkAll.find('.k-checkbox');\n                if (this._allItemsAreUnchecked) {\n                    checkAllCheckbox.prop('checked', false).prop('indeterminate', false);\n                } else {\n                    checkAllCheckbox.prop('indeterminate', true);\n                }\n            },\n            _filterHeader: function () {\n                var icon;\n                if (this.filterInput) {\n                    this.filterInput.off(ns).parent().remove();\n                    this.filterInput = null;\n                }\n                if (this._isFilterEnabled()) {\n                    this._disableCheckChildren();\n                    icon = '<span class=\"k-icon k-i-zoom\"></span>';\n                    this.filterInput = $('<input class=\"k-textbox\"/>').attr({\n                        placeholder: this.element.attr('placeholder'),\n                        title: this.element.attr('title'),\n                        role: 'listbox',\n                        'aria-haspopup': true,\n                        'aria-expanded': false\n                    });\n                    this.filterInput.on('input', proxy(this._filterChange, this));\n                    $('<span class=\"k-list-filter\" />').insertBefore(this.tree).append(this.filterInput.add(icon));\n                }\n            },\n            _filterChange: function () {\n                if (this.filterInput) {\n                    this._search();\n                }\n            },\n            _disableCheckChildren: function () {\n                if (this._isMultipleSelection() && this.options.treeview.checkboxes && this.options.treeview.checkboxes.checkChildren) {\n                    this.options.treeview.checkboxes.checkChildren = false;\n                }\n            },\n            _checkAll: function () {\n                if (this.checkAll) {\n                    this.checkAll.find('.k-checkbox-label, .k-checkbox').off(ns);\n                    this.checkAll.remove();\n                    this.checkAll = null;\n                }\n                if (this._isMultipleSelection() && this.options.checkAll) {\n                    this.checkAll = $('<div class=\"k-check-all\"><input type=\"checkbox\" class=\"k-checkbox\"/><span class=\"k-checkbox-label\">Check All</span></div>').insertBefore(this.tree);\n                    this.checkAll.find('.k-checkbox-label').html(kendo.template(this.options.checkAllTemplate)({ instance: this }));\n                    this.checkAll.find('.k-checkbox-label').on(CLICK + ns, proxy(this._clickCheckAll, this));\n                    this.checkAll.find('.k-checkbox').on('change' + ns, proxy(this._changeCheckAll, this)).on('keydown' + ns, proxy(this._keydownCheckAll, this));\n                    this._disabledCheckedItems = [];\n                    this._disabledUnCheckedItems = [];\n                    this._getAllChecked();\n                    if (!this._allItemsAreUnchecked) {\n                        this._checkCheckAll();\n                    }\n                }\n            },\n            _changeCheckAll: function () {\n                var checkAllCheckbox = this.checkAll.find('.k-checkbox');\n                var isChecked = checkAllCheckbox.prop('checked');\n                if (!browser.msie && !browser.edge) {\n                    this._updateCheckAll(isChecked);\n                }\n            },\n            _updateCheckAll: function (isChecked) {\n                var checkAllCheckbox = this.checkAll.find('.k-checkbox');\n                this._toggleCheckAllItems(isChecked);\n                checkAllCheckbox.prop('checked', isChecked);\n                if (this._disabledCheckedItems.length && this._disabledUnCheckedItems.length) {\n                    checkAllCheckbox.prop('indeterminate', true);\n                } else if (this._disabledCheckedItems.length) {\n                    checkAllCheckbox.prop('indeterminate', !isChecked);\n                } else if (this._disabledUnCheckedItems.length) {\n                    checkAllCheckbox.prop('indeterminate', isChecked);\n                } else {\n                    checkAllCheckbox.prop('indeterminate', false);\n                }\n                this._disabledCheckedItems = [];\n                this._disabledUnCheckedItems = [];\n            },\n            _keydownCheckAll: function (e) {\n                var key = e.keyCode;\n                var altKey = e.altKey;\n                if (altKey && key === keys.UP || key === keys.ESC) {\n                    this.close();\n                    this.wrapper.focus();\n                    e.preventDefault();\n                    return;\n                }\n                if (key === keys.UP) {\n                    if (this.filterInput) {\n                        this.filterInput.focus();\n                    } else {\n                        this.wrapper.focus();\n                    }\n                    e.preventDefault();\n                }\n                if (key === keys.DOWN) {\n                    if (this.tree && this.tree.is(':visible')) {\n                        this.tree.focus();\n                    }\n                    e.preventDefault();\n                }\n                if (key === keys.SPACEBAR && (browser.msie || browser.edge)) {\n                    this._clickCheckAll();\n                    e.preventDefault();\n                }\n            },\n            _clickCheckAll: function () {\n                var checkAllCheckbox = this.checkAll.find('.k-checkbox');\n                var isChecked = checkAllCheckbox.prop('checked');\n                this._updateCheckAll(!isChecked);\n                checkAllCheckbox.focus();\n            },\n            _dfs: function (items, action, prop) {\n                for (var idx = 0; idx < items.length; idx++) {\n                    if (!this[action](items[idx], prop)) {\n                        break;\n                    }\n                    this._traverceChildren(items[idx], action, prop);\n                }\n            },\n            _uncheckItemByUid: function (uid) {\n                this._dfs(this.dataSource.data(), '_uncheckItemEqualsUid', uid);\n            },\n            _uncheckItemEqualsUid: function (item, uid) {\n                if (item.enabled !== false && item._tagUid == uid) {\n                    item.set('checked', false);\n                    return false;\n                }\n                return true;\n            },\n            _selectItemByText: function (text) {\n                this._dfs(this.dataSource.data(), '_itemEqualsText', text);\n            },\n            _itemEqualsText: function (item, text) {\n                if (item.enabled !== false && this._text(item) === text) {\n                    this.treeview.select(this.treeview.findByUid(item.uid));\n                    this._selectValue(item);\n                    return false;\n                }\n                return true;\n            },\n            _selectItemByValue: function (value) {\n                this._dfs(this.dataSource.data(), '_itemEqualsValue', value);\n            },\n            _itemEqualsValue: function (item, value) {\n                if (item.enabled !== false && this._valueComparer(item, value)) {\n                    this.treeview.select(this.treeview.findByUid(item.uid));\n                    return false;\n                }\n                return true;\n            },\n            _checkItemByValue: function (value) {\n                var items = this.treeview.dataItems();\n                for (var idx = 0; idx < value.length; idx++) {\n                    this._dfs(items, '_checkItemEqualsValue', value[idx]);\n                }\n            },\n            _checkItemEqualsValue: function (item, value) {\n                if (item.enabled !== false && this._valueComparer(item, value)) {\n                    item.set('checked', true);\n                    return false;\n                }\n                return true;\n            },\n            _valueComparer: function (item, value) {\n                var itemValue = this._value(item);\n                var itemText;\n                if (!this._isNullorUndefined(itemValue)) {\n                    if (this._isNullorUndefined(value)) {\n                        return false;\n                    }\n                    var newValue = this._value(value);\n                    if (newValue) {\n                        return itemValue == newValue;\n                    } else {\n                        return itemValue == value;\n                    }\n                }\n                itemText = this._text(item);\n                if (itemText) {\n                    if (this._text(value)) {\n                        return itemText == this._text(value);\n                    } else {\n                        return itemText == value;\n                    }\n                }\n                return false;\n            },\n            _isNullorUndefined: function (value) {\n                return value === undefined || value === null;\n            },\n            _getAllChecked: function () {\n                this._allCheckedItems = [];\n                this._allItemsAreChecked = true;\n                this._allItemsAreUnchecked = true;\n                this._dfs(this.dataSource.data(), '_getAllCheckedItems');\n                return this._allCheckedItems;\n            },\n            _getAllCheckedItems: function (item) {\n                if (this._allItemsAreChecked) {\n                    this._allItemsAreChecked = item.checked;\n                }\n                if (this._allItemsAreUnchecked) {\n                    this._allItemsAreUnchecked = !item.checked;\n                }\n                if (item.checked) {\n                    this._allCheckedItems.push(item);\n                }\n                return true;\n            },\n            _traverceChildren: function (item, action, prop) {\n                var childrenField = item._childrenOptions && item._childrenOptions.schema ? item._childrenOptions.schema.data : null;\n                var subItems = item[childrenField] || item.items || item.children;\n                if (!subItems) {\n                    return;\n                }\n                this._dfs(subItems, action, prop);\n            },\n            _toggleCheckAllItems: function (checked) {\n                this._dfs(this.dataSource.data(), '_checkAllCheckItem', checked);\n            },\n            _checkAllCheckItem: function (item, checked) {\n                if (item.enabled === false) {\n                    if (item.checked) {\n                        this._disabledCheckedItems.push(item);\n                    } else {\n                        this._disabledUnCheckedItems.push(item);\n                    }\n                } else {\n                    item.set('checked', checked);\n                }\n                return true;\n            },\n            _isFilterEnabled: function () {\n                var filter = this.options.filter;\n                return filter && filter !== 'none';\n            },\n            _editable: function (options) {\n                var that = this;\n                var element = that.element;\n                var disable = options.disable;\n                var readonly = options.readonly;\n                var wrapper = that.wrapper.add(that.filterInput).off(ns);\n                var dropDownWrapper = that._inputWrapper.off(HOVEREVENTS);\n                if (that._isMultipleSelection()) {\n                    that.tagList.off(CLICK + ns);\n                }\n                if (!readonly && !disable) {\n                    element.removeAttr(DISABLED).removeAttr(READONLY);\n                    dropDownWrapper.removeClass(STATEDISABLED).on(HOVEREVENTS, that._toggleHover);\n                    that._clear.on('click' + ns, proxy(that._clearClick, that));\n                    wrapper.attr(TABINDEX, wrapper.data(TABINDEX)).attr(ARIA_DISABLED, false).on('keydown' + ns, proxy(that._keydown, that)).on('focusin' + ns, proxy(that._focusinHandler, that)).on('focusout' + ns, proxy(that._focusoutHandler, that));\n                    that.wrapper.on(CLICK + ns, proxy(that._wrapperClick, that));\n                    if (this._isMultipleSelection()) {\n                        that.tagList.on(CLICK + ns, 'li.k-button', function (e) {\n                            $(e.currentTarget).addClass(FOCUSED);\n                        });\n                        that.tagList.on(CLICK + ns, '.k-select', function (e) {\n                            that._removeTagClick(e);\n                        });\n                    }\n                } else if (disable) {\n                    wrapper.removeAttr(TABINDEX);\n                    dropDownWrapper.addClass(STATEDISABLED);\n                } else {\n                    wrapper.attr(TABINDEX, wrapper.data(TABINDEX));\n                    dropDownWrapper.removeClass(STATEDISABLED);\n                    wrapper.on('focusin' + ns, proxy(that._focusinHandler, that)).on('focusout' + ns, proxy(that._focusoutHandler, that));\n                }\n                element.attr(DISABLED, disable).attr(READONLY, readonly);\n                wrapper.attr(ARIA_DISABLED, disable);\n            },\n            _focusinHandler: function () {\n                this._inputWrapper.addClass(FOCUSED);\n                this._prevent = false;\n            },\n            _focusoutHandler: function () {\n                var that = this;\n                if (this._isMultipleSelection()) {\n                    this.tagList.find(DOT + FOCUSED).removeClass(FOCUSED);\n                }\n                if (!that._prevent) {\n                    this._inputWrapper.removeClass(FOCUSED);\n                    that._prevent = true;\n                    that.element.blur();\n                }\n            },\n            _toggle: function (open) {\n                open = open !== undefined ? open : !this.popup.visible();\n                this[open ? OPEN : CLOSE]();\n            },\n            _wrapperClick: function (e) {\n                e.preventDefault();\n                this.popup.unbind('activate', this._focusInputHandler);\n                this._focused = this.wrapper;\n                this._prevent = false;\n                this._toggle();\n            },\n            _toggleHover: function (e) {\n                $(e.currentTarget).toggleClass(HOVER, e.type === 'mouseenter');\n            },\n            _focusInput: function () {\n                if (this.filterInput) {\n                    this.filterInput.focus();\n                } else if (this.checkAll) {\n                    this.checkAll.find('.k-checkbox').focus();\n                } else if (this.tree.is(':visible')) {\n                    this.tree.focus();\n                }\n            },\n            _keydown: function (e) {\n                var key = e.keyCode;\n                var altKey = e.altKey;\n                var isFilterInputActive;\n                var isWrapperActive;\n                var focused, tagItem;\n                var isPopupVisible = this.popup.visible();\n                if (this.filterInput) {\n                    isFilterInputActive = this.filterInput[0] === activeElement();\n                }\n                if (this.wrapper) {\n                    isWrapperActive = this.wrapper[0] === activeElement();\n                }\n                if (isWrapperActive) {\n                    if (key === keys.ESC) {\n                        this._clearTextAndValue();\n                        e.preventDefault();\n                        return;\n                    }\n                    if (this._isMultipleSelection()) {\n                        if (key === keys.LEFT) {\n                            this._focusPrevTag();\n                            e.preventDefault();\n                            return;\n                        }\n                        if (key === keys.RIGHT) {\n                            this._focusNextTag();\n                            e.preventDefault();\n                            return;\n                        }\n                        if (key === keys.HOME) {\n                            this._focusFirstTag();\n                            e.preventDefault();\n                            return;\n                        }\n                        if (key === keys.END) {\n                            this._focusLastTag();\n                            e.preventDefault();\n                            return;\n                        }\n                        if (key === keys.DELETE) {\n                            focused = this.tagList.find(DOT + FOCUSED).first();\n                            if (focused.length) {\n                                tagItem = this._tags[focused.index()];\n                                this._removeTag(tagItem);\n                            }\n                            e.preventDefault();\n                            return;\n                        }\n                        if (key === keys.BACKSPACE) {\n                            focused = this.tagList.find(DOT + FOCUSED).first();\n                            if (focused.length) {\n                                tagItem = this._tags[focused.index()];\n                                this._removeTag(tagItem);\n                            } else {\n                                focused = this._focusLastTag();\n                                if (focused.length) {\n                                    tagItem = this._tags[focused.index()];\n                                    this._removeTag(tagItem);\n                                }\n                            }\n                            e.preventDefault();\n                            return;\n                        }\n                    }\n                }\n                if (isFilterInputActive) {\n                    if (key === keys.ESC) {\n                        this._clearFilter();\n                    }\n                    if (key === keys.UP && !altKey) {\n                        this.wrapper.focus();\n                        e.preventDefault();\n                    }\n                    if (browser.msie && browser.version < 10) {\n                        if (key === keys.BACKSPACE || key === keys.DELETE) {\n                            this._search();\n                        }\n                    }\n                }\n                if (altKey && key === keys.UP || key === keys.ESC) {\n                    this.wrapper.focus();\n                    this.close();\n                    e.preventDefault();\n                    return;\n                }\n                if (key === keys.ENTER && this._typingTimeout && this.filterInput && isPopupVisible) {\n                    e.preventDefault();\n                    return;\n                }\n                if (key === keys.SPACEBAR && !isFilterInputActive) {\n                    this._toggle(!isPopupVisible);\n                    e.preventDefault();\n                }\n                if (altKey && key === keys.DOWN && !isPopupVisible) {\n                    this.open();\n                    e.preventDefault();\n                }\n                if (key === keys.DOWN && isPopupVisible) {\n                    if (this.filterInput && !isFilterInputActive) {\n                        this.filterInput.focus();\n                    } else if (this.checkAll && this.checkAll.is(':visible')) {\n                        this.checkAll.find('input').focus();\n                    } else if (this.tree.is(':visible')) {\n                        this.tree.focus();\n                    }\n                    e.preventDefault();\n                }\n            },\n            _focusPrevTag: function () {\n                var focused = this.tagList.find(DOT + FOCUSED);\n                if (focused.length) {\n                    var activedescendant = this.wrapper.attr('aria-activedescendant');\n                    focused.first().removeClass(FOCUSED).removeAttr('id').prev().addClass(FOCUSED).attr('id', activedescendant);\n                    this.wrapper.attr('aria-activedescendant', activedescendant);\n                } else {\n                    this._focusLastTag();\n                }\n            },\n            _focusNextTag: function () {\n                var focused = this.tagList.find(DOT + FOCUSED);\n                if (focused.length) {\n                    var activedescendant = this.wrapper.attr('aria-activedescendant');\n                    focused.first().removeClass(FOCUSED).removeAttr('id').next().addClass(FOCUSED).attr('id', activedescendant);\n                    this.wrapper.attr('aria-activedescendant', activedescendant);\n                } else {\n                    this._focusFirstTag();\n                }\n            },\n            _focusFirstTag: function () {\n                var activedescendant = this.wrapper.attr('aria-activedescendant');\n                this._clearDisabledTag();\n                var firstTag = this.tagList.children('li').first().addClass(FOCUSED).attr('id', activedescendant);\n                this.wrapper.attr('aria-activedescendant', activedescendant);\n                return firstTag;\n            },\n            _focusLastTag: function () {\n                var activedescendant = this.wrapper.attr('aria-activedescendant');\n                this._clearDisabledTag();\n                var lastTag = this.tagList.children('li').last().addClass(FOCUSED).attr('id', activedescendant);\n                this.wrapper.attr('aria-activedescendant', activedescendant);\n                return lastTag;\n            },\n            _clearDisabledTag: function () {\n                this.tagList.find(DOT + FOCUSED).removeClass(FOCUSED).removeAttr('id');\n            },\n            _search: function () {\n                var that = this;\n                clearTimeout(that._typingTimeout);\n                that._typingTimeout = setTimeout(function () {\n                    var value = that.filterInput.val();\n                    if (that._prev !== value) {\n                        that._prev = value;\n                        that.search(value);\n                    }\n                    that._typingTimeout = null;\n                }, that.options.delay);\n            },\n            _clearFilter: function () {\n                if (this.filterInput.val().length) {\n                    this.filterInput.val('');\n                    this._prev = '';\n                    this._filtering = true;\n                    this.treeview.dataSource.filter({});\n                }\n            },\n            _removeTagClick: function (e) {\n                e.stopPropagation();\n                var tagItem = this._tags[$(e.currentTarget.parentElement).index()];\n                this._removeTag(tagItem);\n            },\n            _removeTag: function (tagItem) {\n                if (!tagItem) {\n                    return;\n                }\n                var uid = tagItem.uid;\n                this._uncheckItemByUid(uid);\n            }\n        });\n        function assign(instance, fields, value) {\n            var idx = 0, lastIndex = fields.length - 1, field;\n            for (; idx < lastIndex; ++idx) {\n                field = fields[idx];\n                if (!(field in instance)) {\n                    instance[field] = {};\n                }\n                instance = instance[field];\n            }\n            instance[fields[lastIndex]] = value;\n        }\n        ui.plugin(DropDownTree);\n        var SingleSelection = kendo.Class.extend({\n            init: function (view) {\n                this._dropdowntree = view;\n            },\n            _initWrapper: function () {\n                this._wrapper();\n                this._span();\n            },\n            _preselect: function (data) {\n                var dropdowntree = this._dropdowntree;\n                dropdowntree._selectValue(data);\n            },\n            _wrapper: function () {\n                var dropdowntree = this._dropdowntree, element = dropdowntree.element, DOMelement = element[0], wrapper;\n                wrapper = element.parent();\n                if (!wrapper.is('span.k-widget')) {\n                    wrapper = element.wrap('<span />').parent();\n                    wrapper[0].style.cssText = DOMelement.style.cssText;\n                    wrapper[0].title = DOMelement.title;\n                }\n                dropdowntree._focused = dropdowntree.wrapper = wrapper.addClass('k-widget k-dropdowntree').addClass(DOMelement.className).css('display', '').attr({\n                    accesskey: element.attr('accesskey'),\n                    unselectable: 'on',\n                    role: 'listbox',\n                    'aria-haspopup': true,\n                    'aria-expanded': false\n                });\n                element.hide().removeAttr('accesskey');\n            },\n            _span: function () {\n                var dropdowntree = this._dropdowntree, wrapper = dropdowntree.wrapper, SELECTOR = 'span.k-input', span;\n                span = wrapper.find(SELECTOR);\n                if (!span[0]) {\n                    wrapper.append('<span unselectable=\"on\" class=\"k-dropdown-wrap k-state-default\"><span unselectable=\"on\" class=\"k-input\">&nbsp;</span><span unselectable=\"on\" class=\"k-select\" aria-label=\"select\"><span class=\"k-icon k-i-arrow-60-down\"></span></span></span>').append(dropdowntree.element);\n                    span = wrapper.find(SELECTOR);\n                }\n                dropdowntree.span = span;\n                dropdowntree._inputWrapper = $(wrapper[0].firstChild);\n                dropdowntree._arrow = wrapper.find('.k-select');\n                dropdowntree._arrowIcon = dropdowntree._arrow.find('.k-icon');\n            },\n            _setValue: function (value) {\n                var dropdowntree = this._dropdowntree;\n                var currentValue;\n                if (value === undefined || value === null) {\n                    currentValue = dropdowntree._values.slice()[0];\n                    value = typeof currentValue === 'object' ? currentValue : dropdowntree._accessor() || currentValue;\n                    return value === undefined || value === null ? '' : value;\n                }\n                dropdowntree._valueMethodCalled = true;\n                if (value.length === 0) {\n                    dropdowntree._clearTextAndValue();\n                    dropdowntree._valueMethodCalled = false;\n                    return;\n                }\n                dropdowntree._selectItemByValue(value);\n                dropdowntree._toggleCloseVisibility();\n            },\n            _clearValue: function () {\n                var dropdowntree = this._dropdowntree;\n                var selectedNode = dropdowntree.treeview.select();\n                if (dropdowntree.treeview.dataItem(selectedNode)) {\n                    dropdowntree.treeview.dataItem(selectedNode).set('selected', false);\n                    if (!dropdowntree._valueMethodCalled) {\n                        dropdowntree.trigger(CHANGE);\n                    }\n                }\n            },\n            _checkLoadedItem: function (tempItem, value) {\n                var dropdowntree = this._dropdowntree;\n                if (!dropdowntree._isNullorUndefined(value) && value !== '' && dropdowntree._valueComparer(tempItem, value) || !value && tempItem.selected) {\n                    dropdowntree.treeview.select(dropdowntree.treeview.findByUid(tempItem.uid));\n                }\n            }\n        });\n        var MultipleSelection = kendo.Class.extend({\n            init: function (view) {\n                this._dropdowntree = view;\n            },\n            _initWrapper: function () {\n                var dropdowntree = this._dropdowntree;\n                this._tagTemplate();\n                dropdowntree.element.attr('multiple', 'multiple').hide();\n                this._wrapper();\n                dropdowntree._tags = new ObservableArray([]);\n                dropdowntree._multipleTags = new ObservableArray([]);\n                this._tagList();\n                dropdowntree.span = $('<span unselectable=\"on\" class=\"k-input\">&nbsp;</span>').insertAfter(dropdowntree.tagList);\n                dropdowntree._inputWrapper = $(dropdowntree.wrapper[0].firstChild);\n            },\n            _preselect: function (data, value) {\n                var dropdowntree = this._dropdowntree;\n                var valueToSelect = value || dropdowntree.options.value;\n                if (!$.isArray(data) && !(data instanceof kendo.data.ObservableArray)) {\n                    data = [data];\n                }\n                if ($.isPlainObject(data[0]) || data[0] instanceof kendo.data.ObservableObject || !dropdowntree.options.dataValueField) {\n                    dropdowntree.dataSource.data(data);\n                    dropdowntree.value(valueToSelect);\n                }\n            },\n            _tagTemplate: function () {\n                var dropdowntree = this._dropdowntree;\n                var options = dropdowntree.options;\n                var tagTemplate = options.valueTemplate;\n                var isMultiple = options.tagMode === 'multiple';\n                var singleTag = options.messages.singleTag;\n                tagTemplate = tagTemplate ? kendo.template(tagTemplate) : dropdowntree.valueTemplate;\n                dropdowntree.valueTemplate = function (data) {\n                    if (isMultiple) {\n                        return '<li class=\"k-button ' + (data.enabled === false ? 'k-state-disabled' : '') + '\" unselectable=\"on\" role=\"option\" ' + (data.enabled === false ? 'aria-disabled=\"true\"' : '') + '>' + '<span unselectable=\"on\">' + tagTemplate(data) + '</span>' + '<span title=\"' + dropdowntree.options.messages.deleteTag + '\" aria-label=\"' + dropdowntree.options.messages.deleteTag + '\" class=\"k-select\">' + '<span class=\"k-icon k-i-close\"></span>' + '</span>' + '</li>';\n                    }\n                    return '<li class=\"k-button\" unselectable=\"on\" role=\"option\">' + '<span unselectable=\"on\" data-bind=\"text: tags.length\"></span>' + '<span unselectable=\"on\">&nbsp;' + singleTag + '</span>' + '</li>';\n                };\n            },\n            _wrapper: function () {\n                var dropdowntree = this._dropdowntree, element = dropdowntree.element, wrapper = element.parent('span.k-dropdowntree');\n                if (!wrapper[0]) {\n                    wrapper = element.wrap('<div class=\"k-widget k-dropdowntree\" unselectable=\"on\" />').parent();\n                    wrapper[0].style.cssText = element[0].style.cssText;\n                    wrapper[0].title = element[0].title;\n                    $('<div class=\"k-multiselect-wrap k-floatwrap\" unselectable=\"on\" />').insertBefore(element);\n                }\n                dropdowntree.wrapper = wrapper.addClass(element[0].className).css('display', '').attr({\n                    role: 'listbox',\n                    'aria-activedescendant': kendo.guid(),\n                    'aria-haspopup': true,\n                    'aria-expanded': false\n                });\n                dropdowntree._innerWrapper = $(wrapper[0].firstChild);\n            },\n            _tagList: function () {\n                var dropdowntree = this._dropdowntree, tagList = dropdowntree._innerWrapper.children('ul');\n                if (!tagList[0]) {\n                    var isMultiple = dropdowntree.options.tagMode === 'multiple';\n                    var tagCollection = isMultiple ? 'tags' : 'multipleTag';\n                    tagList = $('<ul role=\"listbox\" unselectable=\"on\" data-template=\"tagTemplate\" data-bind=\"source: ' + tagCollection + '\" class=\"k-reset\"/>').appendTo(dropdowntree._innerWrapper);\n                }\n                dropdowntree.tagList = tagList;\n                dropdowntree.tagList.attr('id', kendo.guid() + '_tagList');\n                dropdowntree.wrapper.attr('aria-owns', dropdowntree.tagList.attr('id'));\n                var viewModel = kendo.observable({\n                    multipleTag: dropdowntree._multipleTags,\n                    tags: dropdowntree._tags,\n                    tagTemplate: dropdowntree.valueTemplate\n                });\n                kendo.bind(dropdowntree.tagList, viewModel);\n                dropdowntree.tagList.attr('data-stop', true);\n            },\n            _setValue: function (value) {\n                var dropdowntree = this._dropdowntree;\n                var oldValues = dropdowntree._values;\n                if (value === undefined || value === null) {\n                    return dropdowntree._values.slice();\n                }\n                dropdowntree.setValue(value);\n                dropdowntree._valueMethodCalled = true;\n                if (value.length) {\n                    this._removeValues(oldValues, value);\n                    dropdowntree._checkItemByValue(value);\n                } else {\n                    dropdowntree._clearTextAndValue();\n                }\n                dropdowntree._valueMethodCalled = false;\n                dropdowntree._toggleCloseVisibility();\n            },\n            _removeValues: function (oldValues, value) {\n                var dropdowntree = this._dropdowntree;\n                var removedValues = this._getNewValues(oldValues, value);\n                for (var idx = 0; idx < removedValues.length; idx++) {\n                    for (var j = 0; j < dropdowntree._tags.length; j++) {\n                        if (dropdowntree._valueComparer(dropdowntree._tags[j], removedValues[idx])) {\n                            dropdowntree._uncheckItemByUid(dropdowntree._tags[j].uid);\n                        }\n                    }\n                }\n            },\n            _getNewValues: function (oldValues, value) {\n                var removedValues = [];\n                for (var idx = 0; idx < oldValues.length; idx++) {\n                    if (value.indexOf(oldValues[idx]) === -1) {\n                        removedValues.push(oldValues[idx]);\n                    }\n                }\n                return removedValues;\n            },\n            _clearValue: function () {\n                var dropdowntree = this._dropdowntree;\n                var tagsArray = dropdowntree._tags.slice();\n                for (var idx = 0; idx < tagsArray.length; idx++) {\n                    var uid = tagsArray[idx].uid;\n                    dropdowntree._preventChangeTrigger = true;\n                    dropdowntree._uncheckItemByUid(uid);\n                }\n                if (tagsArray.length) {\n                    dropdowntree._preventChangeTrigger = false;\n                    if (!dropdowntree._valueMethodCalled) {\n                        dropdowntree.trigger(CHANGE);\n                    }\n                }\n            },\n            _checkLoadedItem: function (tempItem, value) {\n                var dropdowntree = this._dropdowntree;\n                if (dropdowntree._noInitialValue && tempItem.checked) {\n                    dropdowntree._checkValue(tempItem);\n                    return;\n                }\n                if (value.length && (value.indexOf(dropdowntree._currentValue(tempItem)) !== -1 || value.indexOf(tempItem)) !== -1 && !this._findTag(dropdowntree._currentValue(tempItem))) {\n                    if (tempItem.checked) {\n                        dropdowntree._checkValue(tempItem);\n                    } else {\n                        tempItem.set('checked', true);\n                    }\n                }\n            },\n            _findTag: function (tempItemValue) {\n                var dropdowntree = this._dropdowntree;\n                return dropdowntree._tags.find(function (item) {\n                    return dropdowntree._valueComparer(item, tempItemValue);\n                });\n            }\n        });\n        kendo.ui.DropDownTree.SingleSelection = SingleSelection;\n        kendo.ui.DropDownTree.MultipleSelection = MultipleSelection;\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}