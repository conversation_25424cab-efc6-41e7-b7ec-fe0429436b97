/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.listview.min",["kendo.data.min","kendo.editable.min","kendo.selectable.min"],e)}(function(){return function(e,t){var n=window.kendo,a="change",i="cancel",r="dataBound",l="dataBinding",o=n.ui.Widget,s=n.keys,d=">*:not(.k-loading-mask)",c="progress",u="error",m="k-state-focused",f="k-state-selected",p="k-edit-item",g="edit",h="remove",_="save",b="mousedown",v="click",S="touchstart",k=".kendoListView",w=e.proxy,E=n._activeElement,y=n.ui.progress,x=n.data.DataSource,T=n.ui.DataBoundWidget.extend({init:function(t,a){var i=this;a=e.isArray(a)?{dataSource:a}:a,o.fn.init.call(i,t,a),a=i.options,i.wrapper=t=i.element,t[0].id&&(i._itemId=t[0].id+"_lv_active"),i._element(),i._dataSource(),i._templates(),i._navigatable(),i._selectable(),i._pageable(),i._crudHandlers(),i._scrollable(),i.options.autoBind&&i.dataSource.fetch(),n.notify(i)},events:[a,i,l,r,g,h,_],options:{name:"ListView",autoBind:!0,selectable:!1,navigatable:!1,height:null,template:"",altTemplate:"",editTemplate:""},setOptions:function(e){o.fn.setOptions.call(this,e),this._templates(),this.selectable&&(this.selectable.destroy(),this.selectable=null),this._selectable()},_templates:function(){var e=this.options;this.template=n.template(e.template||""),this.altTemplate=n.template(e.altTemplate||e.template),this.editTemplate=n.template(e.editTemplate||"")},_item:function(e){return this.element.children()[e]()},items:function(){return this.element.children()},dataItem:function(t){var a=n.attr("uid"),i=e(t).closest("["+a+"]").attr(a);return this.dataSource.getByUid(i)},setDataSource:function(e){this.options.dataSource=e,this._dataSource(),this.options.autoBind&&e.fetch()},_unbindDataSource:function(){var e=this;e.dataSource.unbind(a,e._refreshHandler).unbind(c,e._progressHandler).unbind(u,e._errorHandler)},_dataSource:function(){var e=this;e.dataSource&&e._refreshHandler?e._unbindDataSource():(e._refreshHandler=w(e.refresh,e),e._progressHandler=w(e._progress,e),e._errorHandler=w(e._error,e)),e.dataSource=x.create(e.options.dataSource).bind(a,e._refreshHandler).bind(c,e._progressHandler).bind(u,e._errorHandler)},_progress:function(e){var t=this.element;y(t,e,{opacity:!0})},_error:function(){y(this.element,!1)},_element:function(){var e=this.options.height;this.element.addClass("k-widget k-listview").attr("role","listbox"),e&&this.element.css("height",e)},refresh:function(e){var a,i,o,s,d,c=this,u=c.dataSource.view(),m="",f=c.template,p=c.altTemplate,g=E(),h=c._endlessFetchInProgress,_=h?c._skipRerenderItemsCount:0,b=c.options.scrollable;if(e=e||{},"itemchange"===e.action)return c._hasBindingTarget()||c.editable||(a=e.items[0],o=c.items().filter("["+n.attr("uid")+"="+a.uid+"]"),o.length>0&&(s=o.index(),c.angular("cleanup",function(){return{elements:[o]}}),o.replaceWith(f(a)),o=c.items().eq(s),o.attr(n.attr("uid"),a.uid),c.angular("compile",function(){return{elements:[o],data:[{dataItem:a}]}}),c.trigger("itemChange",{item:o,data:a}))),t;if(!c.trigger(l,{action:e.action||"rebind",items:e.items,index:e.index})){for(c._angularItems("cleanup"),h||c._destroyEditable(),s=_,d=u.length;s<d;s++)m+=s%2?p(u[s]):f(u[s]);for(h?c.element.append(m):c.element.html(m),i=c.items().not(".k-loading-mask"),s=_,d=u.length;s<d;s++)i.eq(s).attr(n.attr("uid"),u[s].uid).attr("role","option").attr("aria-selected","false");c.element[0]===g&&c.options.navigatable&&(c._focusNext?c.current(c.current().next()):b||c.current(i.eq(0))),c._angularItems("compile"),c._progress(!1),c._endlessFetchInProgress=null,c.trigger(r,{action:e.action||"rebind",items:e.items,index:e.index})}},_pageable:function(){var t,a,i=this,r=i.options.pageable;e.isPlainObject(r)&&(a=r.pagerId,t=e.extend({},r,{dataSource:i.dataSource,pagerId:null}),i.pager=new n.ui.Pager(e("#"+a),t))},_selectable:function(){var e,i,r=this,l=r.options.selectable,o=r.options.navigatable;l&&(e=n.ui.Selectable.parseOptions(l).multiple,r.selectable=new n.ui.Selectable(r.element,{aria:!0,multiple:e,filter:d,change:function(){r.trigger(a)}}),o&&r.element.on("keydown"+k,function(n){if(n.keyCode===s.SPACEBAR){if(i=r.current(),n.target==n.currentTarget&&n.preventDefault(),e)if(n.ctrlKey){if(i&&i.hasClass(f))return i.removeClass(f),t}else r.selectable.clear();else r.selectable.clear();r.selectable.value(i)}}))},_scrollable:function(){var e,t=this,n=t.options.scrollable;n&&(t.element.css({"overflow-y":"scroll",position:"relative","-webkit-overflow-scrolling":"touch"}),"endless"===n&&(e=t._endlessPageSize=t.dataSource.options.pageSize,t.element.off("scroll"+k).on("scroll"+k,function(){this.scrollTop+this.clientHeight-this.scrollHeight>=-15&&!t._endlessFetchInProgress&&t._endlessPageSize<t.dataSource.total()&&(t._skipRerenderItemsCount=t._endlessPageSize,t._endlessPageSize=t._skipRerenderItemsCount+e,t.dataSource.options.endless=!0,t._endlessFetchInProgress=!0,t.dataSource.pageSize(t._endlessPageSize))})))},current:function(e){var n=this,a=n.element,i=n._current,r=n._itemId;return e===t?i:(i&&i[0]&&(i[0].id===r&&i.removeAttr("id"),i.removeClass(m),a.removeAttr("aria-activedescendant")),e&&e[0]&&(r=e[0].id||r,n._scrollTo(e[0]),a.attr("aria-activedescendant",r),e.addClass(m).attr("id",r)),n._current=e,t)},_scrollTo:function(t){var n,a,i=this,r=!1,l="scroll";"auto"==i.wrapper.css("overflow")||i.wrapper.css("overflow")==l||i.wrapper.css("overflow-y")==l?n=i.wrapper[0]:(n=window,r=!0),a=function(a,i){var o=r?e(t).offset()[a.toLowerCase()]:t["offset"+a],s=t["client"+i],d=e(n)[l+a](),c=e(n)[i.toLowerCase()]();o+s>d+c?e(n)[l+a](o+s-c):o<d&&e(n)[l+a](o)},a("Top","Height"),a("Left","Width")},_navigatable:function(){var t=this,a=t.options.navigatable,i=t.element,r=function(a){t.current(e(a.currentTarget)),e(a.target).is(":button,a,:input,a>.k-icon,textarea")||n.focusElement(i)};a&&(t._tabindex(),i.on("focus"+k,function(){var e=t._current;e&&e.is(":visible")||(e=t._item("first")),t.current(e)}).on("focusout"+k,function(){t._current&&t._current.removeClass(m)}).on("keydown"+k,function(a){var r,l,o=a.keyCode,d=t.current(),c=e(a.target),u=!c.is(":button,textarea,a,a>.t-icon,input"),m=c.is(":text,:password"),f=n.preventDefault,g=i.find("."+p),h=E(),_=t.options.scrollable;if(!(!u&&!m&&s.ESC!=o||m&&s.ESC!=o&&s.ENTER!=o))if(s.UP===o||s.LEFT===o)d&&d[0]&&(d=d.prev()),d&&d[0]?t.current(d):_||t.current(t._item("last")),f(a);else if(s.DOWN===o||s.RIGHT===o)_?"endless"!==t.options.scrollable||d.next().length?(d=d.next(),d&&d[0]&&t.current(d)):(t.element[0].scrollTop=t.element[0].scrollHeight,t._focusNext=!0):(d=d.next(),t.current(d&&d[0]?d:t._item("first"))),f(a);else if(s.PAGEUP===o)t.current(null),t.dataSource.page(t.dataSource.page()-1),f(a);else if(s.PAGEDOWN===o)t.current(null),t.dataSource.page(t.dataSource.page()+1),f(a);else if(s.HOME===o)t.current(t._item("first")),f(a);else if(s.END===o)t.current(t._item("last")),f(a);else if(s.ENTER===o)0!==g.length&&(u||m)?(r=t.items().index(g),h&&h.blur(),t.save(),l=function(){t.element.trigger("focus"),t.current(t.items().eq(r))},t.one("dataBound",l)):""!==t.options.editTemplate&&t.edit(d);else if(s.ESC===o){if(g=i.find("."+p),0===g.length)return;r=t.items().index(g),t.cancel(),t.element.trigger("focus"),t.current(t.items().eq(r))}}),i.on(b+k+" "+S+k,d,w(r,t)))},clearSelection:function(){var e=this;e.selectable.clear(),e.trigger(a)},select:function(n){var a=this,i=a.selectable;return n=e(n),n.length?(i.options.multiple||(i.clear(),n=n.first()),i.value(n),t):i.value()},_destroyEditable:function(){var e=this;e.editable&&(e.editable.destroy(),delete e.editable)},_modelFromElement:function(e){var t=e.attr(n.attr("uid"));return this.dataSource.getByUid(t)},_closeEditable:function(){var e,t,a,i=this,r=i.editable,l=i.template;return r&&(r.element.index()%2&&(l=i.altTemplate),i.angular("cleanup",function(){return{elements:[r.element]}}),e=i._modelFromElement(r.element),i._destroyEditable(),a=r.element.index(),r.element.replaceWith(l(e)),t=i.items().eq(a),t.attr(n.attr("uid"),e.uid),i._hasBindingTarget()&&n.bind(t,e),i.angular("compile",function(){return{elements:[t],data:[{dataItem:e}]}})),!0},edit:function(e){var t,a,i=this,r=i._modelFromElement(e),l=r.uid;i.cancel(),e=i.items().filter("["+n.attr("uid")+"="+l+"]"),a=e.index(),e.replaceWith(i.editTemplate(r)),t=i.items().eq(a).addClass(p).attr(n.attr("uid"),r.uid),i.editable=t.kendoEditable({model:r,clearContainer:!1,errorTemplate:!1,target:i}).data("kendoEditable"),i.trigger(g,{model:r,item:t})},save:function(){var e,t,n=this,a=n.editable;a&&(t=a.element,e=n._modelFromElement(t),a.end()&&!n.trigger(_,{model:e,item:t})&&(n._closeEditable(),n.dataSource.sync()))},remove:function(e){var t=this,n=t.dataSource,a=t._modelFromElement(e);t.editable&&(n.cancelChanges(t._modelFromElement(t.editable.element)),t._closeEditable()),t.trigger(h,{model:a,item:e})||(e.hide(),n.remove(a),n.sync())},add:function(){var e,t=this,n=t.dataSource,a=n.indexOf((n.view()||[])[0]);a<0&&(a=0),t.cancel(),e=n.insert(a,{}),t.edit(t.element.find("[data-uid='"+e.uid+"']"))},cancel:function(){var e,t,n=this,a=n.dataSource;n.editable&&(e=n.editable.element,t=n._modelFromElement(e),n.trigger(i,{model:t,container:e})||(a.cancelChanges(t),n._closeEditable()))},_crudHandlers:function(){var t=this,a=b+k,i=S+k,r=v+k;t.element.on(a+" "+i,".k-edit-button",function(a){a.preventDefault();var i=e(this).closest("["+n.attr("uid")+"]");setTimeout(function(){t.edit(i)})}),t.element.on(a+" "+i,".k-delete-button",function(a){a.preventDefault();var i=e(this).closest("["+n.attr("uid")+"]");setTimeout(function(){t.remove(i)})}),t.element.on(r,".k-update-button",function(e){t.save(),e.preventDefault()}),t.element.on(r,".k-cancel-button",function(e){t.cancel(),e.preventDefault()})},destroy:function(){var e=this;o.fn.destroy.call(e),e._unbindDataSource(),e._destroyEditable(),e.element.off(k),e._endlessFetchInProgress=e._endlessPageSize=e._skipRerenderItemsCount=e._focusNext=null,e.pager&&e.pager.destroy(),n.destroy(e.element)}});n.ui.plugin(T)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.listview.min.js.map
