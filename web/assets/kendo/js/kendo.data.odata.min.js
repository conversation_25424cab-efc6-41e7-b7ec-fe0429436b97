/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.data.odata.min",["kendo.core.min"],t)}(function(){return function(t,e){function n(a,o){var r,s,d,i,p,c,u,l,f=[],y=a.logic||"and",m=a.filters;for(r=0,s=m.length;r<s;r++)a=m[r],d=a.field,u=a.value,c=a.operator,a.filters?a=n(a,o):(l=a.ignoreCase,d=d.replace(/\./g,"/"),a=v[c],o&&(a=b[c]),"isnullorempty"===c?a=h.format("{0} {1} null or {0} {1} ''",d,a):"isnotnullorempty"===c?a=h.format("{0} {1} null and {0} {1} ''",d,a):"isnull"===c||"isnotnull"===c?a=h.format("{0} {1} null",d,a):"isempty"===c||"isnotempty"===c?a=h.format("{0} {1} ''",d,a):a&&u!==e&&(i=t.type(u),"string"===i?(p="'{1}'",u=u.replace(/'/g,"''"),l===!0&&(d="tolower("+d+")")):"date"===i?o?(p="{1:yyyy-MM-ddTHH:mm:ss+00:00}",u=h.timezone.apply(u,"Etc/UTC")):p="datetime'{1:yyyy-MM-ddTHH:mm:ss}'":p="{1}",a.length>3?"substringof"!==a?p="{0}({2},"+p+")":(p="{0}("+p+",{2})","doesnotcontain"===c&&(o?(p="{0}({2},'{1}') eq -1",a="indexof"):p+=" eq false")):p="{2} {0} "+p,a=h.format(p,a,u,d))),f.push(a);return a=f.join(" "+y+" "),f.length>1&&(a="("+a+")"),a}function a(t){for(var e in t)0===e.indexOf("@odata")&&delete t[e]}function o(){return Math.floor(65536*(1+Math.random())).toString(16).substr(1)}function r(t){return t+o()+"-"+o()+"-"+o()}function s(t,e){var n=g+"--"+t;return e&&(n+="--"),n}function d(t,e,n,a){var o=t.options[a].url,r=h.format("{0} ",n);return j(o)?r+o(e):r+o}function i(t,e){var n="";return n+=s(t,!1),n+=g+"Content-Type: application/http",n+=g+"Content-Transfer-Encoding: binary",n+=g+"Content-ID: "+e}function p(t){var e="";return e+=g+"Content-Type: application/json;odata=minimalmetadata",e+=g+"Prefer: return=representation",e+=T+h.stringify(t)}function c(t,e,n,a,o,r){var s,c="";for(s=0;s<t.length;s++)c+=i(e,n),c+=T+d(o,t[s],o.options[a].type,a)+" HTTP/1.1",r||(c+=p(t[s])),c+=g,n++;return c}function u(t,e,n,a,o,r,d){var i="";return i+=l(e,n),i+=c(t,n,a,r,o,d),i+=s(n,!0),i+=g}function l(t,e){var n="";return n+="--"+t+g,n+="Content-Type: multipart/mixed; boundary="+e+g}function f(t,e){var n={},a=r("sf_batch_"),o="",d=0,i=t.options.batch.url,p=r("sf_changeset_");return n.type=t.options.batch.type,n.url=j(i)?i():i,n.headers={"Content-Type":"multipart/mixed; boundary="+a},e.updated.length&&(o+=u(e.updated,a,p,d,t,"update",!1),d+=e.updated.length,p=r("sf_changeset_")),e.destroyed.length&&(o+=u(e.destroyed,a,p,d,t,"destroy",!0),d+=e.destroyed.length,p=r("sf_changeset_")),e.created.length&&(o+=u(e.created,a,p,d,t,"create",!1)),o+=s(a,!0),n.data=o,n}function y(t){var e,n,a,o,r,s,d=t.match(/--changesetresponse_[a-z0-9-]+$/gm),i=0,p=[];for(p.push({models:[],passed:!0}),s=0;s<d.length;s++)o=d[s],o.lastIndexOf("--",o.length-1)?s<d.length-1&&p.push({models:[],passed:!0}):(i=i?t.indexOf(o,i+o.length):t.indexOf(o),e=t.substring(i,t.indexOf("--",i+1)),n=e.match(/^HTTP\/1\.\d (\d{3}) (.*)$/gm).pop(),a=h.parseFloat(n.match(/\d{3}/g).pop()),a>=200&&a<=299?(r=e.match(/\{.*\}/gm),r&&p[p.length-1].models.push(JSON.parse(r[0]))):p[p.length-1].passed=!1);return p}var h=window.kendo,m=t.extend,g="\r\n",T="\r\n\r\n",j=h.isFunction,v={eq:"eq",neq:"ne",gt:"gt",gte:"ge",lt:"lt",lte:"le",contains:"substringof",doesnotcontain:"substringof",endswith:"endswith",startswith:"startswith",isnull:"eq",isnotnull:"ne",isnullorempty:"eq",isnotnullorempty:"ne",isempty:"eq",isnotempty:"ne"},b=m({},v,{contains:"contains"}),E={pageSize:t.noop,page:t.noop,filter:function(t,e,a){e&&(e=n(e,a),e&&(t.$filter=e))},sort:function(e,n){var a=t.map(n,function(t){var e=t.field.replace(/\./g,"/");return"desc"===t.dir&&(e+=" desc"),e}).join(",");a&&(e.$orderby=a)},skip:function(t,e){e&&(t.$skip=e)},take:function(t,e){e&&(t.$top=e)}},w={read:{dataType:"jsonp"}};m(!0,h.data,{schemas:{odata:{type:"json",data:function(t){return t.d.results||[t.d]},total:"d.__count"}},transports:{odata:{read:{cache:!0,dataType:"jsonp",jsonp:"$callback"},update:{cache:!0,dataType:"json",contentType:"application/json",type:"PUT"},create:{cache:!0,dataType:"json",contentType:"application/json",type:"POST"},destroy:{cache:!0,dataType:"json",type:"DELETE"},parameterMap:function(t,e,n){var a,o,r,s;if(t=t||{},e=e||"read",s=(this.options||w)[e],s=s?s.dataType:"json","read"===e){a={$inlinecount:"allpages"},"json"!=s&&(a.$format="json");for(r in t)E[r]?E[r](a,t[r],n):a[r]=t[r]}else{if("json"!==s)throw Error("Only json dataType can be used for "+e+" operation.");if("destroy"!==e){for(r in t)o=t[r],"number"==typeof o&&(t[r]=o+"");a=h.stringify(t)}}return a}}}}),m(!0,h.data,{schemas:{"odata-v4":{type:"json",data:function(e){if(t.isArray(e)){for(var n=0;n<e.length;n++)a(e[n]);return e}return e=t.extend({},e),a(e),e.value?e.value:[e]},total:function(t){return t["@odata.count"]}}},transports:{"odata-v4":{batch:{type:"POST"},read:{cache:!0,dataType:"json"},update:{cache:!0,dataType:"json",contentType:"application/json;IEEE754Compatible=true",type:"PUT"},create:{cache:!0,dataType:"json",contentType:"application/json;IEEE754Compatible=true",type:"POST"},destroy:{cache:!0,dataType:"json",type:"DELETE"},parameterMap:function(t,e){var n=h.data.transports.odata.parameterMap(t,e,!0);return"read"==e&&(n.$count=!0,delete n.$inlinecount),n},submit:function(e){var n=this,a=f(n,e.data),o=e.data;(o.updated.length||o.destroyed.length||o.created.length)&&t.ajax(m(!0,{},{success:function(t){var n,a=y(t),r=0;o.updated.length&&(n=a[r],n.passed&&e.success(n.models.length?n.models:[],"update"),r++),o.destroyed.length&&(n=a[r],n.passed&&e.success([],"destroy"),r++),o.created.length&&(n=a[r],n.passed&&e.success(n.models,"create"))},error:function(t,n,a){e.error(t,n,a)}},a))}}}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()});
//# sourceMappingURL=kendo.data.odata.min.js.map
