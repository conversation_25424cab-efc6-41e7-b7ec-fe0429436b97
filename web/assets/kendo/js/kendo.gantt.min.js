/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("util/text-metrics.min",["kendo.core.min"],e)}(function(){!function(e){function t(e){return(e+"").replace(o,d)}function i(e){var t,i=[];for(t in e)i.push(t+e[t]);return i.sort().join("")}function n(e){var t,i=2166136261;for(t=0;t<e.length;++t)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=e.charCodeAt(t);return i>>>0}function s(){return{width:0,height:0,baseline:0}}function a(e,t,i){return u.current.measure(e,t,i)}var r,o,d,l,c,u;window.kendo.util=window.kendo.util||{},r=kendo.Class.extend({init:function(e){this._size=e,this._length=0,this._map={}},put:function(e,t){var i=this._map,n={key:e,value:t};i[e]=n,this._head?(this._tail.newer=n,n.older=this._tail,this._tail=n):this._head=this._tail=n,this._length>=this._size?(i[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(e){var t=this._map[e];if(t)return t===this._head&&t!==this._tail&&(this._head=t.newer,this._head.older=null),t!==this._tail&&(t.older&&(t.older.newer=t.newer,t.newer.older=t.older),t.older=this._tail,t.newer=null,this._tail.newer=t,this._tail=t),t.value}}),o=/\r?\n|\r|\t/g,d=" ",l={baselineMarkerSize:1},"undefined"!=typeof document&&(c=document.createElement("div"),c.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),u=kendo.Class.extend({init:function(t){this._cache=new r(1e3),this.options=e.extend({},l,t)},measure:function(e,a,r){var o,d,l,u,h,p,f,v,m;if(void 0===r&&(r={}),!e)return s();if(o=i(a),d=n(e+o),l=this._cache.get(d))return l;u=s(),h=r.box||c,p=this._baselineMarker().cloneNode(!1);for(f in a)v=a[f],void 0!==v&&(h.style[f]=v);return m=r.normalizeText!==!1?t(e):e+"",h.textContent=m,h.appendChild(p),document.body.appendChild(h),m.length&&(u.width=h.offsetWidth-this.options.baselineMarkerSize,u.height=h.offsetHeight,u.baseline=p.offsetTop+this.options.baselineMarkerSize),u.width>0&&u.height>0&&this._cache.put(d,u),h.parentNode.removeChild(h),u},_baselineMarker:function(){var e=document.createElement("div");return e.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",e}}),u.current=new u,kendo.deepExtend(kendo.util,{LRUCache:r,TextMetrics:u,measureText:a,objectKey:i,hashKey:n,normalizeText:t})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("kendo.gantt.min",["kendo.data.min","kendo.popup.min","kendo.window.min","kendo.resizable.min","kendo.gantt.list.min","kendo.gantt.timeline.min","kendo.grid.min","kendo.pdf.min"],e)}(function(){return function(e,t){function i(e){return"["+v.attr("uid")+(e?"='"+e+"']":"]")}function n(e){return delete e.name,delete e.prefix,delete e.remove,delete e.edit,delete e.add,delete e.navigate,e}function s(e){var t,i,n,s,a,r;if(e.filter("[name=end], [name=start]").length){for(t=e.attr("name"),i=v.widgetInstance(e,v.ui),n={},s=e;s!==window&&!a;)s=s.parent(),a=s.data("kendoEditable");return!(r=a?a.options.model:null)||(n.start=r.start,n.end=r.end,n[t]=i?i.value():v.parseDate(e.val()),n.start<=n.end)}return!0}function a(t,i){var n=t.parents("["+v.attr("role")+'="gantt"]'),s=[],a=r(n);t.attr(N,0),i&&a.each(function(t,i){s[t]=e(i).scrollTop()});try{t[0].setActive()}catch(o){t[0].focus()}i&&a.each(function(t,i){e(i).scrollTop(s[t])})}function r(t){return e(t).parentsUntil("body").filter(function(e,t){var i=v.getComputedStyles(t,["overflow"]);return"visible"!=i.overflow}).add(window)}var o,d,l,c,u,h,p,f,v=window.kendo,m=e.extend({F10:121},v.keys),g="matchMedia"in window,b=v.support.browser,_=v.support.mobileOS,k=v.Observable,w=v.ui.Widget,y=v.data.DataSource,D=v.data.ObservableObject,C=v.data.ObservableArray,S=v.data.Query,T=e.isArray,x=e.inArray,B=v.isFunction,R=e.proxy,E=e.extend,F=e.isPlainObject,H=e.map,I=v._outerWidth,z=v._outerHeight,W=3,A=".kendoGantt",P="p0",N="tabIndex",V="click",L="width",M="string",U={down:{origin:"bottom left",position:"top left"},up:{origin:"top left",position:"bottom left"}},q="aria-activedescendant",G="aria-label",O="gantt_active_cell",j="action-option-focused",K=".",Q="Are you sure you want to delete this task?",Y="Are you sure you want to delete this dependency?",J=v.template('<button class="#=styles.buttonToggle#" type="button" '+G+'="Toggle"><span class="#=styles.iconToggle#"></span></button>'),X='<button class="#=styles.button# #=className#" type="button" #if (action) {#data-action="#=action#"#}#><span class="#=iconClass#"></span><span>#=text#</span></button>',Z='<a class="#=className#" #=attr# href="\\#">#=text#</a>',$=v.template('<li class="#=styles.currentView# #=styles.viewButtonDefault#"><a href="\\#" class="#=styles.link#">&nbps;</a></li>'),ee=v.template('<ul class="#=styles.viewsWrapper#">#for(var view in views){#<li class="#=styles.viewButtonDefault# #=styles.viewButton#-#= view.toLowerCase() #" data-#=ns#name="#=view#"><a href="\\#" class="#=styles.link#">#=views[view].title#</a></li>#}#</ul>'),te=v.template('<div class="#=styles.popupWrapper#"><ul class="#=styles.popupList#" role="listbox">#for(var i = 0, l = actions.length; i < l; i++){#<li class="#=styles.item#" data-action="#=actions[i].data#" role="option">#=actions[i].text#</span>#}#</ul></div>'),ie=function(t,i){var n={name:i.field,title:i.title},s=i.model.fields[i.field].validation;s&&F(s)&&s.message&&(n[v.attr("dateCompare-msg")]=s.message),e('<input type="text" required '+v.attr("type")+'="date" '+v.attr("role")+'="datetimepicker" '+v.attr("bind")+'="value:'+i.field+'" '+v.attr("validate")+"='true' />").attr(n).appendTo(t),e("<span "+v.attr("for")+'="'+i.field+'" class="k-invalid-msg"/>').hide().appendTo(t)},ne=function(t,i){e('<a href="#" class="'+i.styles.button+'">'+i.messages.assignButton+"</a>").click(i.click).appendTo(t)},se={wrapper:"k-widget k-gantt",rowHeight:"k-gantt-rowheight",listWrapper:"k-gantt-layout k-gantt-treelist",list:"k-gantt-treelist",timelineWrapper:"k-gantt-layout k-gantt-timeline",timeline:"k-gantt-timeline",splitBarWrapper:"k-splitbar k-state-default k-splitbar-horizontal k-splitbar-draggable-horizontal k-gantt-layout",splitBar:"k-splitbar",splitBarHover:"k-splitbar-horizontal-hover",popupWrapper:"k-list-container",popupList:"k-list k-reset",resizeHandle:"k-resize-handle",icon:"k-icon",item:"k-item",line:"k-line",buttonDelete:"k-gantt-delete",buttonCancel:"k-gantt-cancel",buttonSave:"k-gantt-update",buttonToggle:"k-gantt-toggle",primary:"k-primary",hovered:"k-state-hover",selected:"k-state-selected",focused:"k-state-focused",gridHeader:"k-grid-header",gridHeaderWrap:"k-grid-header-wrap",gridContent:"k-grid-content",tasks:"k-gantt-tasks",popup:{form:"k-popup-edit-form",editForm:"k-gantt-edit-form",formContainer:"k-edit-form-container",resourcesFormContainer:"k-resources-form-container",message:"k-popup-message",buttonsContainer:"k-edit-buttons k-state-default",button:"k-button",editField:"k-edit-field",editLabel:"k-edit-label",resourcesField:"k-gantt-resources"},toolbar:{headerWrapper:"k-floatwrap k-header k-gantt-toolbar",footerWrapper:"k-floatwrap k-header k-gantt-toolbar",toolbar:"k-gantt-toolbar",expanded:"k-state-expanded",views:"k-gantt-views",viewsWrapper:"k-reset k-header k-gantt-views",actions:"k-gantt-actions",button:"k-button k-button-icontext",buttonToggle:"k-button k-button-icon k-gantt-toggle",iconPlus:"k-icon k-i-plus",iconPdf:"k-icon k-i-file-pdf",iconToggle:"k-icon k-i-layout-1-by-4",viewButtonDefault:"k-state-default",viewButton:"k-view",currentView:"k-current-view",link:"k-link",pdfButton:"k-gantt-pdf",appendButton:"k-gantt-create"}},ae=k.extend({init:function(e,t){k.fn.init.call(this),this.element=e,this.options=E(!0,{},this.options,t),this._popup()},options:{direction:"down",navigatable:!1},_current:function(e){var t=f.styles,i=this.list.find(K+t.focused),n=i[e]();n.length&&(i.removeClass(t.focused).removeAttr("id"),n.addClass(t.focused).attr("id",j),this.list.find("ul").removeAttr(q).attr(q,j))},_popup:function(){var t=this,i=f.styles,n="li"+K+i.item,s=K+i.toolbar.appendButton,a=this.options.messages.actions,r=this.options.navigatable;this.list=e(te({styles:i,actions:[{data:"add",text:a.addChild},{data:"insert-before",text:a.insertBefore},{data:"insert-after",text:a.insertAfter}]})),this.element.append(this.list),this.popup=new v.ui.Popup(this.list,E({anchor:this.element.find(s),open:function(){t._adjustListWidth()},animation:this.options.animation},U[this.options.direction])),this.element.on(V+A,s,function(n){var s=e(this),a=s.attr(v.attr("action"));n.preventDefault(),a?t.trigger("command",{type:a}):(t.popup.open(),r&&t.list.find("li:first").addClass(i.focused).attr("id",j).end().find("ul").attr({TABINDEX:0,"aria-activedescendant":j}).focus())}),this.list.find(n).hover(function(){e(this).addClass(i.hovered)},function(){e(this).removeClass(i.hovered)}).end().on(V+A,n,function(){t.trigger("command",{type:e(this).attr(v.attr("action"))}),t.popup.close()}),r&&(this.popup.bind("close",function(){t.list.find(n).removeClass(i.focused).end().find("ul").attr(N,0),t.element.parents("["+v.attr("role")+'="gantt"]').find(K+i.gridContent+" > table:first").focus()}),this.list.find("ul").on("keydown"+A,function(e){var n=e.keyCode;switch(n){case m.UP:e.preventDefault(),t._current("prev");break;case m.DOWN:e.preventDefault(),t._current("next");break;case m.ENTER:t.list.find(K+i.focused).click();break;case m.ESC:e.preventDefault(),t.popup.close()}}))},_adjustListWidth:function(){var e,t,i=this.list,n=f.styles,s=i[0].style.width,a=this.element.find(K+n.toolbar.appendButton),r=I(i);!i.data(L)&&s||(e=window.getComputedStyle?window.getComputedStyle(a[0],null):0,t=e?parseFloat(e.width):I(a),e&&(b.mozilla||b.msie)&&(t+=parseFloat(e.paddingLeft)+parseFloat(e.paddingRight)+parseFloat(e.borderLeftWidth)+parseFloat(e.borderRightWidth)),s="border-box"!==i.css("box-sizing")?t-(I(i)-i.width()):t,r>s&&(s=r),i.css({fontFamily:a.css("font-family"),width:s}).data(L,s))},destroy:function(){clearTimeout(this._focusTimeout),this.popup.destroy(),this.element.off(A),this.list.off(A),this.unbind()}}),re=function(e,t){return function(i){var n,s;if(i=T(i)?{data:i}:i,n=i||{},s=n.data,n.data=s,!(n instanceof e)&&n instanceof y)throw Error("Incorrect DataSource type. Only "+t+" instances are supported");return n instanceof e?n:new e(n)}},oe=v.data.Model.define({id:"id",fields:{id:{type:"number"},predecessorId:{type:"number"},successorId:{type:"number"},type:{type:"number"}}}),de=y.extend({init:function(e){y.fn.init.call(this,E(!0,{},{schema:{modelBase:oe,model:oe}},e))},successors:function(e){return this._dependencies("predecessorId",e)},predecessors:function(e){return this._dependencies("successorId",e)},dependencies:function(e){var t=this.predecessors(e),i=this.successors(e);return t.push.apply(t,i),t},_dependencies:function(e,t){var i=this.view(),n={field:e,operator:"eq",value:t};return i=new S(i).filter(n).toArray()}});de.create=re(de,"GanttDependencyDataSource"),d=v.data.Model.define({duration:function(){var e=this.end,t=this.start;return e-t},isMilestone:function(){return 0===this.duration()},_offset:function(e){var t,i,n=["start","end"];for(i=0;i<n.length;i++)t=new Date(this.get(n[i]).getTime()+e),this.set(n[i],t)},id:"id",fields:{id:{type:"number"},parentId:{type:"number",defaultValue:null,validation:{required:!0}},orderId:{type:"number",validation:{required:!0}},title:{type:"string",defaultValue:"New task"},start:{type:"date",validation:{required:!0}},end:{type:"date",validation:{required:!0,dateCompare:s,message:"End date should be after or equal to the start date"}},percentComplete:{type:"number",validation:{required:!0,min:0,max:1,step:.01}},summary:{type:"boolean"},expanded:{type:"boolean",defaultValue:!0}}}),l=y.extend({init:function(e){y.fn.init.call(this,E(!0,{},{schema:{modelBase:d,model:d}},e))},remove:function(e){var t=e.get("parentId"),i=this.taskAllChildren(e);return this._removeItems(i),e=y.fn.remove.call(this,e),this._childRemoved(t,e.get("orderId")),e},add:function(e){if(e)return e=this._toGanttTask(e),this.insert(this.taskSiblings(e).length,e)},insert:function(e,t){if(t)return t=this._toGanttTask(t),t.set("orderId",e),t=y.fn.insert.call(this,e,t),this._reorderSiblings(t,this.taskSiblings(t).length-1),this._resolveSummaryFields(this.taskParent(t)),t},taskChildren:function(e){var i,n=this.view(),s={field:"parentId",operator:"eq",value:null},a=this._sort&&this._sort.length?this._sort:{field:"orderId",dir:"asc"};if(e){if(i=e.get("id"),i===t||null===i||""===i)return[];s.value=i}return n=new S(n).filter(s).sort(a).toArray()},taskAllChildren:function(e){var t=[],i=this,n=function(e){var s=i.taskChildren(e);t.push.apply(t,s),H(s,n)};return e?n(e):t=this.view(),t},taskSiblings:function(e){if(!e)return null;var t=this.taskParent(e);return this.taskChildren(t)},taskParent:function(e){return e&&null!==e.get("parentId")?this.get(e.parentId):null},taskLevel:function(e){for(var t=0,i=this.taskParent(e);null!==i;)t+=1,i=this.taskParent(i);return t},taskTree:function(e){var t,i,n,s,a=[],r=this.taskChildren(e);for(i=0,n=r.length;i<n;i++)t=r[i],a.push(t),t.get("expanded")&&(s=this.taskTree(t),a.push.apply(a,s));return a},update:function(e,i){var n,s,a=this,r=function(e,t){var i,n,s=a.taskAllChildren(e);for(i=0,n=s.length;i<n;i++)s[i]._offset(t)},o=function(e){var t=e.field,i=e.sender;switch(t){case"start":a._resolveSummaryStart(a.taskParent(i)),r(i,i.get(t).getTime()-n.getTime());break;case"end":a._resolveSummaryEnd(a.taskParent(i));break;case"percentComplete":a._resolveSummaryPercentComplete(a.taskParent(i));break;case"orderId":a._reorderSiblings(i,n)}};i.parentId!==t&&(n=e.get("parentId"),n!==i.parentId&&(e.set("parentId",i.parentId),a._childRemoved(n,e.get("orderId")),e.set("orderId",a.taskSiblings(e).length-1),a._resolveSummaryFields(a.taskParent(e))),delete i.parentId),e.bind("change",o);for(s in i)n=e.get(s),e.set(s,i[s]);e.unbind("change",o)},_resolveSummaryFields:function(e){e&&(this._updateSummary(e),this.taskChildren(e).length&&(this._resolveSummaryStart(e),this._resolveSummaryEnd(e),this._resolveSummaryPercentComplete(e)))},_resolveSummaryStart:function(e){var t=this,i=function(e){var i,n,s,a=t.taskChildren(e),r=a[0].start.getTime();for(n=1,s=a.length;n<s;n++)i=a[n].start.getTime(),i<r&&(r=i);return new Date(r)};this._updateSummaryRecursive(e,"start",i)},_resolveSummaryEnd:function(e){var t=this,i=function(e){var i,n,s,a=t.taskChildren(e),r=a[0].end.getTime();for(n=1,s=a.length;n<s;n++)i=a[n].end.getTime(),i>r&&(r=i);return new Date(r)};this._updateSummaryRecursive(e,"end",i)},_resolveSummaryPercentComplete:function(e){var t=this,i=function(e){var i=t.taskChildren(e),n=new S(i).aggregate([{field:"percentComplete",aggregate:"average"}]);return n.percentComplete.average};this._updateSummaryRecursive(e,"percentComplete",i)},_updateSummaryRecursive:function(e,t,i){var n,s;e&&(n=i(e),e.set(t,n),s=this.taskParent(e),s&&this._updateSummaryRecursive(s,t,i))},_childRemoved:function(e,t){var i,n,s=null===e?null:this.get(e),a=this.taskChildren(s);for(i=t,n=a.length;i<n;i++)a[i].set("orderId",i);this._resolveSummaryFields(s)},_reorderSiblings:function(e,t){var i,n=e.get("orderId"),s=n>t,a=s?t:n,r=s?n:t,o=s?a:a+1,d=this.taskSiblings(e);for(r=Math.min(r,d.length-1),i=a;i<=r;i++)d[i]!==e&&(d[i].set("orderId",o),o+=1)},_updateSummary:function(e){if(null!==e){var t=this.taskChildren(e).length;e.set("summary",t>0)}},_toGanttTask:function(e){if(!(e instanceof d)){var t=e;e=this._createNewModel(),e.accept(t)}return e}}),l.create=re(l,"GanttDataSource"),E(!0,v.data,{GanttDataSource:l,GanttTask:d,GanttDependencyDataSource:de,GanttDependency:oe}),c={desktop:{dateRange:ie,resources:ne}},u=v.Observable.extend({init:function(e,t){v.Observable.fn.init.call(this),this.element=e,this.options=E(!0,{},this.options,t),this.createButton=this.options.createButton},fields:function(t,i){var n,s=this,a=this.options,r=a.messages.editor,o=a.resources,d=function(e){e.preventDefault(),o.editor(s.container.find(K+f.styles.popup.resourcesField),i)};return a.editable.template?n=e.map(i.fields,function(e,t){return{field:t}}):(n=[{field:"title",title:r.title},{field:"start",title:r.start,editor:t.dateRange},{field:"end",title:r.end,editor:t.dateRange},{field:"percentComplete",title:r.percentComplete,format:P}],i.get(o.field)&&n.push({field:o.field,title:r.resources,messages:r,editor:t.resources,click:d,styles:f.styles.popup})),n},_buildEditTemplate:function(e,t,i){var n,s,a,r,o=this.options.resources,d=this.options.editable.template,l=E({},v.Template,this.options.templateSettings),c=l.paramName,u=f.styles.popup,h="";if(d)typeof d===M&&(d=window.unescape(d)),h+=v.template(d,l)(e);else for(n=0,s=t.length;n<s;n++)a=t[n],h+='<div class="'+u.editLabel+'"><label for="'+a.field+'">'+(a.title||a.field||"")+"</label></div>",a.field===o.field&&(h+='<div class="'+u.resourcesField+'" style="display:none"></div>'),!e.editable||e.editable(a.field)?(i.push(a),h+="<div "+v.attr("container-for")+'="'+a.field+'" class="'+u.editField+'"></div>'):(r="#:",a.field?(a=v.expr(a.field,c),r+=a+"==null?'':"+a):r+="''",r+="#",r=v.template(r,l),h+='<div class="'+u.editField+'">'+r(e)+"</div>");return h}}),h=u.extend({destroy:function(){this.close(),this.unbind()},editTask:function(e){this.editable=this._createPopupEditor(e)},close:function(){var e=this,t=function(){e.editable&&(e.editable.destroy(),e.editable=null,e.container=null),e.popup&&(e.popup.destroy(),e.popup=null)};this.editable&&this.container.is(":visible")?(e.trigger("close",{window:e.container}),this.container.data("kendoWindow").bind("deactivate",t).close()):t()},showDialog:function(t){var i,n,s,a,r=t.buttons,o=f.styles.popup,d=v.format('<div class="{0}"><div class="{1}"><p class="{2}">{3}</p><div class="{4}">',o.form,o.formContainer,o.message,t.text,o.buttonsContainer);for(i=0,n=r.length;i<n;i++)d+=this.createButton(r[i]);d+="</div></div></div>",s=this.element,this.popup&&this.popup.destroy(),a=this.popup=e(d).appendTo(s).eq(0).on("click",K+o.button,function(t){t.preventDefault(),a.close();var i=e(t.currentTarget).index();r[i].click()}).kendoWindow({modal:!0,autoFocus:!1,resizable:!1,draggable:!1,title:t.title,visible:!1,deactivate:function(){this.destroy(),s.focus()}}).getKendoWindow(),a.center().open(),a.element.find(".k-primary").focus()},_createPopupEditor:function(t){var i,n,s=this,a={},r=this.options.messages,o=f.styles,d=o.popup,l=v.format('<div {0}="{1}" class="{2} {3}"><div class="{4}">',v.attr("uid"),t.uid,d.form,d.editForm,d.formContainer),u=this.fields(c.desktop,t),h=[];return l+=this._buildEditTemplate(t,u,h),l+='<div class="'+d.buttonsContainer+'">',l+=this.createButton({name:"update",text:r.save,className:f.styles.primary}),l+=this.createButton({name:"cancel",text:r.cancel}),s.options.editable.destroy!==!1&&(l+=this.createButton({name:"delete",text:r.destroy})),l+="</div></div></div>",i=this.container=e(l).appendTo(this.element).eq(0).kendoWindow(E({modal:!0,resizable:!1,draggable:!0,title:r.editor.editorTitle,visible:!1,close:function(e){e.userTriggered&&s.trigger("cancel",{container:i,model:t})&&e.preventDefault()}},a)),n=i.kendoEditable({fields:h,model:t,clearContainer:!1,validateOnBlur:!0,target:s.options.target}).data("kendoEditable"),v.cycleForm(i),this.trigger("edit",{container:i,model:t})?s.trigger("cancel",{container:i,model:t}):(i.data("kendoWindow").center().open(),i.on(V+A,K+o.buttonCancel,function(e){e.preventDefault(),e.stopPropagation(),s.trigger("cancel",{container:i,model:t})}),i.on(V+A,K+o.buttonSave,function(e){var n,a,r,o,d;for(e.preventDefault(),e.stopPropagation(),n=s.fields(c.desktop,t),a={},o=0,d=n.length;o<d;o++)r=n[o].field,a[r]=t.get(r);s.trigger("save",{container:i,model:t,updateInfo:a})}),i.on(V+A,K+o.buttonDelete,function(e){e.preventDefault(),e.stopPropagation(),s.trigger("remove",{container:i,model:t})})),n}}),p=w.extend({init:function(e,t){w.fn.init.call(this,e,t),this.wrapper=this.element,this.model=this.options.model,this.resourcesField=this.options.resourcesField,this.createButton=this.options.createButton,this._initContainer(),this._attachHandlers()},events:["save"],open:function(){this.window.center().open()},close:function(){this.window.bind("deactivate",R(this.destroy,this)).close()},destroy:function(){this._dettachHandlers(),this.grid.destroy(),this.grid=null,this.window.destroy(),this.window=null,w.fn.destroy.call(this),v.destroy(this.wrapper),this.element=this.wrapper=null},_attachHandlers:function(){var t=f.styles,i=this.grid,n=this._cancelProxy=R(this._cancel,this);this.container.on(V+A,K+t.buttonCancel,this._cancelProxy),this._saveProxy=R(this._save,this),this.container.on(V+A,K+t.buttonSave,this._saveProxy),this.window.bind("close",function(e){e.userTriggered&&n(e)}),i.wrapper.on(V+A,"input[type='checkbox']",function(){var t=e(this),n=e(t).closest("tr"),s=i.dataSource.getByUid(n.attr(v.attr("uid"))),a=e(t).is(":checked")?1:"";s.set("value",a)})},_dettachHandlers:function(){this._cancelProxy=null,this._saveProxy=null,this.container.off(A),this.grid.wrapper.off()},_cancel:function(e){e.preventDefault(),this.close()},_save:function(e){e.preventDefault(),this._updateModel(),this.wrapper.is(K+f.styles.popup.resourcesField)||this.trigger("save",{container:this.wrapper,model:this.model}),this.close()},_initContainer:function(){var t=this,i=f.styles.popup,n=v.format('<div class="{0} {1}"><div class="{2} {3}"/></div>"',i.form,i.editForm,i.formContainer,i.resourcesFormContainer);n=e(n),this.container=n.find(K+i.resourcesFormContainer),this.window=n.kendoWindow({modal:!0,resizable:!1,draggable:!0,visible:!1,title:this.options.messages.resourcesEditorTitle,open:function(){t.grid.resize(!0)}}).data("kendoWindow"),this._resourceGrid(),this._createButtons()},_resourceGrid:function(){var t=this,i=this.options.messages,n=e('<div id="resources-grid"/>').appendTo(this.container);this.grid=new v.ui.Grid(n,{columns:[{field:"name",title:i.resourcesHeader,template:"<label><input type='checkbox' value='#=name#'# if (value > 0 && value !== null) {#checked='checked'# } #/>#=name#</labe>"},{field:"value",title:i.unitsHeader,template:function(e){var t=e.format,i=null!==e.value?e.value:"";return t?v.toString(i,t):i}}],height:280,sortable:!0,editable:!0,filterable:!0,dataSource:{data:t.options.data,schema:{model:{id:"id",fields:{id:{from:"id"},name:{from:"name",type:"string",editable:!1},value:{from:"value",type:"number",validation:this.options.unitsValidation},format:{from:"format",type:"string"}}}}},save:function(e){var t=!!e.values.value;e.container.parent().find("input[type='checkbox']").prop("checked",t)}})},_createButtons:function(){var e,t,i=this.options.buttons,n='<div class="'+f.styles.popup.buttonsContainer+'">';for(e=0,t=i.length;e<t;e++)n+=this.createButton(i[e]);n+="</div>",this.container.append(n)},_updateModel:function(){var e,t,i,n=[],s=this.grid.dataSource.data();for(t=0,i=s.length;t<i;t++)e=s[t].get("value"),null!==e&&e>0&&n.push(s[t]);this.model[this.resourcesField]=n}}),f=w.extend({init:function(e,t,i){T(t)&&(t={dataSource:t}),o={append:{text:"Add Task",action:"add",className:f.styles.toolbar.appendButton,iconClass:f.styles.toolbar.iconPlus},pdf:{text:"Export to PDF",className:f.styles.toolbar.pdfButton,iconClass:f.styles.toolbar.iconPdf}},w.fn.init.call(this,e,t),i&&(this._events=i),this._wrapper(),this._resources(),this.options.views&&this.options.views.length||(this.options.views=["day","week","month"]),this._timeline(),this._toolbar(),this._footer(),this._adjustDimensions(),this._preventRefresh=!0,this.view(this.timeline._selectedViewName),this._preventRefresh=!1,this._dataSource(),this._assignments(),this._dropDowns(),this._list(),this._dependencies(),this._resizable(),this._scrollable(),this._dataBind(),this._attachEvents(),this._createEditor(),v.notify(this)},events:["dataBinding","dataBound","add","edit","remove","cancel","save","change","navigate","moveStart","move","moveEnd","resizeStart","resize","resizeEnd","columnResize"],options:{name:"Gantt",autoBind:!0,navigatable:!1,selectable:!0,editable:!0,resizable:!1,columnResizeHandleWidth:W,columns:[],views:[],dataSource:{},dependencies:{},resources:{},assignments:{},taskTemplate:null,messages:{save:"Save",cancel:"Cancel",destroy:"Delete",deleteTaskConfirmation:Q,deleteDependencyConfirmation:Y,deleteTaskWindowTitle:"Delete task",deleteDependencyWindowTitle:"Delete dependency",views:{day:"Day",week:"Week",month:"Month",year:"Year",start:"Start",end:"End"},actions:{append:"Add Task",addChild:"Add Child",insertBefore:"Add Above",insertAfter:"Add Below",pdf:"Export to PDF"},editor:{editorTitle:"Task",resourcesEditorTitle:"Resources",title:"Title",start:"Start",end:"End",percentComplete:"Complete",resources:"Resources",assignButton:"Assign",resourcesHeader:"Resources",unitsHeader:"Units"}},showWorkHours:!0,showWorkDays:!0,toolbar:null,workDayStart:new Date(1980,1,1,8,0,0),workDayEnd:new Date(1980,1,1,17,0,0),workWeekStart:1,workWeekEnd:5,hourSpan:1,snap:!0,height:600,listWidth:"30%",rowHeight:null},select:function(e){var i=this.list;return e?(i.select(e),this.list.element.find("table[role=treegrid]").focus(),t):i.select()},clearSelection:function(){this.list.clearSelection()},destroy:function(){w.fn.destroy.call(this),this.dataSource&&(this.dataSource.unbind("change",this._refreshHandler),this.dataSource.unbind("progress",this._progressHandler),this.dataSource.unbind("error",this._errorHandler)),this.dependencies&&(this.dependencies.unbind("change",this._dependencyRefreshHandler),this.dependencies.unbind("error",this._dependencyErrorHandler)),this.timeline&&(this.timeline.unbind(),this.timeline.destroy()),this.list&&(this.list.unbind(),this.list.destroy()),this.footerDropDown&&this.footerDropDown.destroy(),this.headerDropDown&&this.headerDropDown.destroy(),this._editor&&this._editor.destroy(),this._resizeDraggable&&this._resizeDraggable.destroy(),this.toolbar.off(A),g&&(this._mediaQuery.removeListener(this._mediaQueryHandler),this._mediaQuery=null),e(window).off("resize"+A,this._resizeHandler),e(this.wrapper).off(A),this.toolbar=null,this.footer=null},setOptions:function(t){var i,n=v.deepExtend({},this.options,t),s=this._events;t.views||(i=this.view().name,n.views=e.map(this.options.views,function(e){var t=F(e),n=t?"string"!=typeof e.type?e.title:e.type:e;return i===n?t?e.selected=!0:e={type:n,selected:!0}:t&&(e.selected=!1),e})),t.dataSource||(n.dataSource=this.dataSource),t.dependencies||(n.dependencies=this.dependencies),t.resources||(n.resources=this.resources),t.assignments||(n.assignments=this.assignments),this.destroy(),this.element.empty(),this.options=null,this.init(this.element,n,s),w.fn._setEvents.call(this,n)},_attachEvents:function(){this._resizeHandler=R(this.resize,this,!1),e(window).on("resize"+A,this._resizeHandler)},_wrapper:function(){var e=f.styles,t=[e.icon,e.resizeHandle].join(" "),i=this.options,n=i.height,s=i.width;this.wrapper=this.element.addClass(e.wrapper).append("<div class='"+e.listWrapper+"'><div></div></div>").append("<div class='"+e.splitBarWrapper+"'><div class='"+t+"'></div></div>").append("<div class='"+e.timelineWrapper+"'><div></div></div>"),this.wrapper.find(K+e.list).width(i.listWidth),n&&this.wrapper.height(n),s&&this.wrapper.width(s),i.rowHeight&&this.wrapper.addClass(e.rowHeight)},_toolbar:function(){var t,i,n,s=this,a=f.styles,r=K+a.toolbar.views+" > li",o=K+a.toolbar.pdfButton,d=K+a.buttonToggle,l=K+a.gridContent,c=e(K+a.list),u=e(K+a.timeline),h=a.hovered,p=this.options.toolbar,b=e("<div class='"+a.toolbar.actions+"'>"),_=function(e){e.matches?c.css({display:"none","max-width":0}):(c.css({display:"inline-block",width:"30%","max-width":"none"}),u.css("display","inline-block"),s.refresh(),u.find(l).scrollTop(s.scrollTop)),s._resize()};B(p)||(p=typeof p===M?p:this._actions(p),p=R(v.template(p),this)),n=e(J({styles:a.toolbar})),i=e(ee({ns:v.ns,views:this.timeline.views,styles:a.toolbar})),b.append(p({})),t=e("<div class='"+a.toolbar.headerWrapper+"'>").append(n).append(i).append(b),i.find("li").length>1&&i.prepend($({styles:a.toolbar})),this.wrapper.prepend(t),this.toolbar=t,g&&(this._mediaQueryHandler=R(_,this),this._mediaQuery=window.matchMedia("(max-width: 480px)"),this._mediaQuery.addListener(this._mediaQueryHandler)),t.on(V+A,r,function(t){var n,r,o;t.preventDefault(),n=s.list,r=e(this).attr(v.attr("name")),o=i.find(K+a.toolbar.currentView),o.is(":visible")&&o.parent().toggleClass(a.toolbar.expanded),n.editable&&n.editable.trigger("validate")||(s.trigger("navigate",{view:r})||s.view(r),s.toolbar.find(K+a.focused).removeClass(a.focused))}).on("keydown"+A,r,function(t){var i=e(K+a.toolbar.views).children(":not(.k-current-view)"),n=i.index(s._focusedView&&s._focusedView[0]||i.closest(K+a.selected)[0]);t.keyCode===m.RIGHT?(e(s.toolbar.find(K+a.focused)).removeClass(a.focused),s._focusedView=e(n+1===i.length?i[0]:i[n+1]),s._focusedView.focus().addClass(a.focused),t.preventDefault()):t.keyCode===m.LEFT?(e(s.toolbar.find(K+a.focused)).removeClass(a.focused),s._focusedView=e(0===n?i[i.length-1]:i[n-1]),s._focusedView.focus().addClass(a.focused),t.preventDefault()):t.keyCode===m.DOWN&&s.toolbar.find(K+a.toolbar.currentView).parent().hasClass(a.toolbar.expanded)?(e(s.toolbar.find(K+a.focused)).removeClass(a.focused),s._focusedView=e(n+1===i.length?i[0]:i[n+1]),s._focusedView.focus().addClass(a.focused),t.preventDefault()):t.keyCode===m.UP&&s.toolbar.find(K+a.toolbar.currentView).parent().hasClass(a.toolbar.expanded)?(e(s.toolbar.find(K+a.focused)).removeClass(a.focused),s._focusedView=e(0===n?i[i.length-1]:i[n-1]),s._focusedView.focus().addClass(a.focused),t.preventDefault()):t.keyCode!==m.ENTER&&t.keyCode!==m.SPACEBAR||!s._focusedView?(t.keyCode===m.SPACEBAR||t.keyCode===m.ENTER||t.keyCode===m.DOWN&&t.altKey)&&s.toolbar.find(K+a.toolbar.currentView+" > a").hasClass(a.focused)?(s.toolbar.find(K+a.toolbar.currentView).parent().toggleClass(a.toolbar.expanded),t.preventDefault()):t.keyCode===m.ESC&&s.toolbar.find(K+a.toolbar.currentView).parent().hasClass(a.toolbar.expanded)?(s.toolbar.find(K+a.toolbar.currentView).parent().toggleClass(a.toolbar.expanded).blur(),s._focusedView=null,s.toolbar.find(K+a.toolbar.currentView+" > a").addClass(a.focused).focus(),t.preventDefault()):t.keyCode>=49&&t.keyCode<=57&&s.view(s.timeline._viewByIndex(t.keyCode-49)):(s.view(s._focusedView.text().toLowerCase()),t.preventDefault())}).on(V+A,o,function(e){e.preventDefault(),s.saveAsPDF()}).on(V+A,d,function(e){e.preventDefault(),c.is(":visible")?(c.css({display:"none",width:"0"}),u.css({display:"inline-block",width:"100%"}),s.refresh(),u.find(l).scrollTop(s.scrollTop)):(u.css({display:"none",width:0}),c.css({display:"inline-block",width:"100%","max-width":"none"}).find(l).scrollTop(s.scrollTop)),s._resize()}),this.wrapper.on("focusout"+A,function(t){e(t.relatedTarget).closest(K+a.toolbar.toolbar).length||s.toolbar.find(K+a.focused).removeClass(a.focused),e(t.relatedTarget).closest(K+a.toolbar.views).length||s.toolbar.find(K+a.toolbar.views).removeClass(a.toolbar.expanded)}).find(K+a.toolbar.toolbar+" li").hover(function(){e(this).addClass(h)},function(){e(this).removeClass(h)})},_actions:function(){var e,t,i=this.options,n=i.editable,s=i.toolbar,a="";if(!T(s)){if(!n||n.create===!1)return a;s=["append"]}for(e=0,t=s.length;e<t;e++)a+=this._createButton(s[e]);return a},_footer:function(){var t,i,n,s,a,r=this.options.editable;r&&r.create!==!1&&(t=f.styles.toolbar,i=this.options.messages.actions,n=e(v.template(X)(E(!0,{styles:t},o.append,{text:i.append}))),s=e("<div class='"+t.actions+"'>").append(n),a=e("<div class='"+t.footerWrapper+"'>").append(s),this.wrapper.append(a),this.footer=a)},_createButton:function(e){var t=e.template||X,i=this.options.messages.actions,n=typeof e===M?e:e.name||e.text,s=o[n]?o[n].className:"k-gantt-"+(n||"").replace(/\s/g,""),a={iconClass:"",action:"",text:n,className:s,styles:f.styles.toolbar};if(!(n||F(e)&&e.template))throw Error("Custom commands should have name specified");
return a=E(!0,a,o[n],{text:i[n]}),F(e)&&(e.className&&x(a.className,e.className.split(" "))<0&&(e.className+=" "+a.className),a=E(!0,a,e)),v.template(t)(a)},_adjustDimensions:function(){var e=this.element,t=f.styles,i=K+t.list,n=K+t.timeline,s=K+t.splitBar,a=z(this.toolbar),r=this.footer?z(this.footer):0,o=e.height(),d=e.width(),l=I(e.find(s)),c=I(e.find(i));e.children([i,n,s].join(",")).height(o-(a+r)).end().children(n).width(d-(l+c)),d<c+l&&e.find(i).width(d-l)},_scrollTo:function(e){var t,n,s=this.timeline.view(),a=this.list,r=v.attr("uid"),o="string"==typeof e?e:e.closest("tr"+i()).attr(r),d=function(){0!==n.length&&t()};s.content.is(":visible")?(n=s.content.find(i(o)),t=function(){s._scrollTo(n)}):(n=a.content.find(i(o)),t=function(){n.get(0).scrollIntoView()}),d()},_dropDowns:function(){var e=this,t=K+f.styles.toolbar.actions,i=this.options.messages.actions,n=this.timeline,s=this.options.editable,a=function(t){var i,s=t.type,a=e.dataSource,r=a._createNewModel(),o=e.dataItem(e.select()),d=a.taskParent(o),l=n.view()._timeSlots()[0],c="add"===s?o:d,u=e.list.editable;u&&u.trigger("validate")||(r.set("title","New task"),c?(r.set("parentId",c.get("id")),r.set("start",c.get("start")),r.set("end",c.get("end"))):(r.set("start",l.start),r.set("end",l.end)),"add"!==s&&(i=o.get("orderId"),i="insert-before"===s?i:i+1),e._createTask(r,i))};s&&s.create!==!1&&(this.footerDropDown=new ae(this.footer.children(t).eq(0),{messages:{actions:i},direction:"up",animation:{open:{effects:"slideIn:up"}},navigatable:e.options.navigatable}),this.headerDropDown=new ae(this.toolbar.children(t).eq(0),{messages:{actions:i},navigatable:e.options.navigatable}),this.footerDropDown.bind("command",a),this.headerDropDown.bind("command",a))},_list:function(){var e,t,i=this,n=i.options.navigatable,s=f.styles,r=this.wrapper.find(K+s.list),o=r.find("> div"),d=this.wrapper.find(K+s.toolbar.actions+" > button"),l={columns:this.options.columns||[],dataSource:this.dataSource,selectable:this.options.selectable,editable:this.options.editable,resizable:this.options.resizable,columnResizeHandleWidth:this.options.columnResizeHandleWidth,listWidth:I(r),resourcesField:this.resources.field,rowHeight:this.options.rowHeight},c=l.columns,u=function(){n&&(i._current(i._cachedCurrent),a(i.list.content.find("table"),!0)),delete i._cachedCurrent};for(t=0;t<c.length;t++)e=c[t],e.field===this.resources.field&&"function"!=typeof e.editor&&(e.editor=R(this._createResourceEditor,this));this.list=new v.ui.GanttList(o,l),this.list.bind("render",function(){i._navigatable()},!0).bind("edit",function(e){i._cachedCurrent=e.cell,i.trigger("edit",{task:e.model,container:e.cell})&&e.preventDefault()}).bind("cancel",function(e){i.trigger("cancel",{task:e.model,container:e.cell})&&e.preventDefault(),u()}).bind("update",function(e){i._updateTask(e.task,e.updateInfo),u()}).bind("change",function(){i.trigger("change");var e=i.list.select();e.length?(d.removeAttr("data-action","add"),i.timeline.select("[data-uid='"+e.attr("data-uid")+"']")):(d.attr("data-action","add"),i.timeline.clearSelection())}).bind("columnResize",function(e){i.trigger("columnResize",{column:e.column,oldWidth:e.oldWidth,newWidth:e.newWidth})})},_timeline:function(){var e=this,i=f.styles,s=n(E(!0,{resourcesField:this.resources.field},this.options)),a=this.wrapper.find(K+i.timeline+" > div"),r=K+i.toolbar.currentView+" > "+K+i.toolbar.link;this.timeline=new v.ui.GanttTimeline(a,s),this.timeline.bind("navigate",function(t){var n=t.view.replace(/\./g,"\\.").toLowerCase(),s=e.toolbar.find(K+i.toolbar.views+" > li").removeClass(i.selected).end().find(K+i.toolbar.viewButton+"-"+n).addClass(i.selected).find(K+i.toolbar.link).text();e.toolbar.find(r).text(s),e.refresh()}).bind("moveStart",function(i){var n=e.list.editable;return n&&n.trigger("validate")?(i.preventDefault(),t):(e.trigger("moveStart",{task:i.task})&&i.preventDefault(),t)}).bind("move",function(t){var i=t.task,n=t.start,s=new Date(n.getTime()+i.duration());e.trigger("move",{task:i,start:n,end:s})&&t.preventDefault()}).bind("moveEnd",function(t){var i=t.task,n=t.start,s=new Date(n.getTime()+i.duration());e.trigger("moveEnd",{task:i,start:n,end:s})||e._updateTask(e.dataSource.getByUid(i.uid),{start:n,end:s})}).bind("resizeStart",function(i){var n=e.list.editable;return n&&n.trigger("validate")?(i.preventDefault(),t):(e.trigger("resizeStart",{task:i.task})&&i.preventDefault(),t)}).bind("resize",function(t){e.trigger("resize",{task:t.task,start:t.start,end:t.end})&&t.preventDefault()}).bind("resizeEnd",function(t){var i=t.task,n={};t.resizeStart?n.start=t.start:n.end=t.end,e.trigger("resizeEnd",{task:i,start:t.start,end:t.end})||e._updateTask(e.dataSource.getByUid(i.uid),n)}).bind("percentResizeStart",function(t){var i=e.list.editable;i&&i.trigger("validate")&&t.preventDefault()}).bind("percentResizeEnd",function(t){e._updateTask(e.dataSource.getByUid(t.task.uid),{percentComplete:t.percentComplete})}).bind("dependencyDragStart",function(t){var i=e.list.editable;i&&i.trigger("validate")&&t.preventDefault()}).bind("dependencyDragEnd",function(t){var i=e.dependencies._createNewModel({type:t.type,predecessorId:t.predecessor.id,successorId:t.successor.id});e._createDependency(i)}).bind("select",function(t){var i=e.list.editable;i&&i.trigger("validate"),e.select("[data-uid='"+t.uid+"']")}).bind("editTask",function(t){var i=e.list.editable;i&&i.trigger("validate")||e.editTask(t.uid)}).bind("clear",function(){e.clearSelection()}).bind("removeTask",function(t){var i=e.list.editable;i&&i.trigger("validate")||e.removeTask(e.dataSource.getByUid(t.uid))}).bind("removeDependency",function(t){var i=e.list.editable;i&&i.trigger("validate")||e.removeDependency(e.dependencies.getByUid(t.uid))})},_dataSource:function(){var e=this.options,t=e.dataSource;t=T(t)?{data:t}:t,this.dataSource&&this._refreshHandler?this.dataSource.unbind("change",this._refreshHandler).unbind("progress",this._progressHandler).unbind("error",this._errorHandler):(this._refreshHandler=R(this.refresh,this),this._progressHandler=R(this._requestStart,this),this._errorHandler=R(this._error,this)),this.dataSource=v.data.GanttDataSource.create(t).bind("change",this._refreshHandler).bind("progress",this._progressHandler).bind("error",this._errorHandler)},_dependencies:function(){var e=this.options.dependencies||{},t=T(e)?{data:e}:e;this.dependencies&&this._dependencyRefreshHandler?this.dependencies.unbind("change",this._dependencyRefreshHandler).unbind("error",this._dependencyErrorHandler):(this._dependencyRefreshHandler=R(this.refreshDependencies,this),this._dependencyErrorHandler=R(this._error,this)),this.dependencies=v.data.GanttDependencyDataSource.create(t).bind("change",this._dependencyRefreshHandler).bind("error",this._dependencyErrorHandler)},_resources:function(){var e=this.options.resources,t=e.dataSource||{};this.resources={field:"resources",dataTextField:"name",dataColorField:"color",dataFormatField:"format"},E(this.resources,e),this.resources.dataSource=v.data.DataSource.create(t)},_assignments:function(){var e=this.options.assignments,t=e.dataSource||{};this.assignments?this.assignments.dataSource.unbind("change",this._assignmentsRefreshHandler):this._assignmentsRefreshHandler=R(this.refresh,this),this.assignments={dataTaskIdField:"taskId",dataResourceIdField:"resourceId",dataValueField:"value"},E(this.assignments,e),this.assignments.dataSource=v.data.DataSource.create(t),this.assignments.dataSource.bind("change",this._assignmentsRefreshHandler)},_createEditor:function(){var e=this,i=this._editor=new h(this.wrapper,E({},this.options,{target:this,resources:{field:this.resources.field,editor:R(this._createResourceEditor,this)},createButton:R(this._createPopupButton,this)}));i.bind("cancel",function(i){var n=e.dataSource.getByUid(i.model.uid);return e.trigger("cancel",{container:i.container,task:n})?(i.preventDefault(),t):(e.cancelTask(),t)}).bind("edit",function(t){var i=e.dataSource.getByUid(t.model.uid);e.trigger("edit",{container:t.container,task:i})&&t.preventDefault()}).bind("save",function(t){var i=e.dataSource.getByUid(t.model.uid);e.saveTask(i,t.updateInfo)}).bind("remove",function(t){e.removeTask(t.model.uid)}).bind("close",e._onDialogClose)},_onDialogClose:function(){},_createResourceEditor:function(e,t){var i,n=this,s=t instanceof D?t:t.model,a=s.get("id"),r=this.options.messages,o=this.resources.field,d={step:.01},l=this.assignments.dataSource.options.schema.model;l&&l.fields.Units&&l.fields.Units.validation&&E(!0,d,l.fields.Units.validation),i=this._resourceEditor=new p(e,{resourcesField:o,unitsValidation:d,data:this._wrapResourceData(a),model:s,messages:E({},r.editor),buttons:[{name:"update",text:r.save,className:f.styles.primary},{name:"cancel",text:r.cancel}],createButton:R(this._createPopupButton,this),save:function(e){n._updateAssignments(e.model.get("id"),e.model.get(o))}}),i.open()},_createPopupButton:function(e){var t=e.name||e.text,i={className:f.styles.popup.button+" k-gantt-"+(t||"").replace(/\s/g,""),text:t,attr:""};if(!(t||F(e)&&e.template))throw Error("Custom commands should have name specified");return F(e)&&(e.className&&(e.className+=" "+i.className),i=E(!0,i,e)),v.template(Z)(i)},view:function(e){return this.timeline.view(e)},range:function(e){var t=this.dataSource,i=this.view(),n=this.timeline;return e&&(i.options.range={start:e.start,end:e.end},n._render(t.taskTree()),n._renderDependencies(this.dependencies.view())),{start:i.start,end:i.end}},date:function(e){var t=this.view();return e&&(t.options.date=e,t._scrollToDate(e)),t.options.date},dataItem:function(e){var t,i;return e?(t=this.list,i=t.content.find(e),t._modelFromElement(i)):null},setDataSource:function(e){this.options.dataSource=e,this._dataSource(),this.list._setDataSource(this.dataSource),this.options.autoBind&&e.fetch()},setDependenciesDataSource:function(e){this.options.dependencies=e,this._dependencies(),this.options.autoBind&&e.fetch()},items:function(){return this.wrapper.children(".k-task")},_updateAssignments:function(e,t){for(var i,n,s,a,r,o,d,l=this.assignments.dataSource,c=this.assignments.dataTaskIdField,u=this.assignments.dataResourceIdField,h=!1,p=new S(l.view()).filter({field:c,operator:"eq",value:e}).toArray();p.length;){for(i=p[0],a=0,r=t.length;a<r;a++)if(n=t[a],i.get(u)===n.get("id")){s=t[a].get("value"),this._updateAssignment(i,s),t.splice(a,1),h=!0;break}h||this._removeAssignment(i),h=!1,p.shift()}for(o=0,d=t.length;o<d;o++)n=t[o],this._createAssignment(n,e);l.sync()},cancelTask:function(){var e=this._editor,t=e.container;t&&e.close()},editTask:function(e){var t,i="string"==typeof e?this.dataSource.getByUid(e):e;i&&(t=this.dataSource._createNewModel(i.toJSON()),t.uid=i.uid,this.cancelTask(),this._editTask(t))},_editTask:function(e){this._editor.editTask(e)},saveTask:function(e,t){var i=this._editor,n=i.container,s=i.editable;n&&s&&s.end()&&this._updateTask(e,t)},_updateTask:function(e,t){var i=this.resources.field;this.trigger("save",{task:e,values:t})||(this._preventRefresh=!0,this.dataSource.update(e,t),t[i]&&this._updateAssignments(e.get("id"),t[i]),this._syncDataSource())},_updateAssignment:function(e,t){var i=this.assignments.dataValueField;e.set(i,t)},removeTask:function(e){var t=this,i="string"==typeof e?this.dataSource.getByUid(e):e;i&&this._taskConfirm(function(e){e||t._removeTask(i)},i)},_createTask:function(e,i){if(!this.trigger("add",{task:e,dependency:null})){var n=this.dataSource;this._preventRefresh=!0,i===t?n.add(e):n.insert(i,e),this._scrollToUid=e.uid,this._syncDataSource()}},_createDependency:function(e){this.trigger("add",{task:null,dependency:e})||(this._preventDependencyRefresh=!0,this.dependencies.add(e),this._preventDependencyRefresh=!1,this.dependencies.sync())},_createAssignment:function(e,t){var i=this.assignments,n=i.dataSource,s=i.dataTaskIdField,a=i.dataResourceIdField,r=i.dataValueField,o=n._createNewModel();o[s]=t,o[a]=e.get("id"),o[r]=e.get("value"),n.add(o)},removeDependency:function(e){var t=this,i="string"==typeof e?this.dependencies.getByUid(e):e;i&&this._dependencyConfirm(function(e){e||t._removeDependency(i)},i)},_removeTaskDependencies:function(e,t){this._preventDependencyRefresh=!0;for(var i=0,n=t.length;i<n;i++)this.dependencies.remove(t[i]);this._preventDependencyRefresh=!1,this.dependencies.sync()},_removeTaskAssignments:function(e){var t,i,n=this.assignments.dataSource,s=n.view(),a={field:this.assignments.dataTaskIdField,operator:"eq",value:e.get("id")};for(s=new S(s).filter(a).toArray(),this._preventRefresh=!0,t=0,i=s.length;t<i;t++)n.remove(s[t]);this._preventRefresh=!1,n.sync()},_removeTask:function(e){var t=this.dependencies.dependencies(e.id);this.trigger("remove",{task:e,dependencies:t})||(this._removeTaskDependencies(e,t),this._removeTaskAssignments(e),this._preventRefresh=!0,this.dataSource.remove(e)&&this._syncDataSource(),this._preventRefresh=!1)},_removeDependency:function(e){this.trigger("remove",{task:null,dependencies:[e]})||this.dependencies.remove(e)&&this.dependencies.sync()},_removeAssignment:function(e){this.assignments.dataSource.remove(e)},_taskConfirm:function(e,t){var i=this.options.messages;this._confirm(e,{model:t,text:i.deleteTaskConfirmation,title:i.deleteTaskWindowTitle})},_dependencyConfirm:function(e,t){var i=this.options.messages;this._confirm(e,{model:t,text:i.deleteDependencyConfirmation,title:i.deleteDependencyWindowTitle})},_confirm:function(e,t){var i,n,s=this.options.editable;s===!0||s.confirmation!==!1?(i=this.options.messages,n=[{name:"delete",text:i.destroy,className:f.styles.primary,click:function(){e()}},{name:"cancel",text:i.cancel,click:function(){e(!0)}}],this.showDialog(E(!0,{},t,{buttons:n}))):e()},showDialog:function(e){this._editor.showDialog(e)},refresh:function(){var e,t,n,s,a,r;this._preventRefresh||this.list.editable||(this._progress(!1),e=this.dataSource,t=e.taskTree(),n=this._scrollToUid,r=-1,this.current&&(a=this.current.closest("tr").attr(v.attr("uid")),r=this.current.index()),this.trigger("dataBinding")||(0!==this.resources.dataSource.data().length&&this._assignResources(t),this._editor&&this._editor.close(),this.clearSelection(),this.list._render(t),this.timeline._render(t),this.timeline._renderDependencies(this.dependencies.view()),n&&(this._scrollTo(n),this.select(i(n))),(n||a)&&r>=0&&(s=this.list.content.find("tr"+i(n||a)+" > td:eq("+r+")"),this._current(s)),this._scrollToUid=null,this.trigger("dataBound")))},refreshDependencies:function(){this._preventDependencyRefresh||this.trigger("dataBinding")||(this.timeline._renderDependencies(this.dependencies.view()),this.trigger("dataBound"))},_assignResources:function(e){var t,i,n=this.resources,s=this.assignments,a=function(){var e=s.dataSource.view(),t={field:s.dataTaskIdField};return e=new S(e).group(t).toArray()},r=a(),o=function(e,t){var i,s,a=e.get("id");for(v.setter(n.field)(e,new C([])),i=0,s=r.length;i<s;i++)r[i].value===a&&t(e,r[i].items)},d=function(e,t){var i,a,r,o,d,l,c,u;for(i=0,a=t.length;i<a;i++)r=t[i],o=n.dataSource.get(r.get(s.dataResourceIdField)),d=r.get(s.dataValueField),l=r.get(s.dataResourceIdField),c=o.get(n.dataFormatField)||P,u=v.toString(d,c),e[n.field].push(new D({id:l,name:o.get(n.dataTextField),color:o.get(n.dataColorField),value:d,formatedValue:u}))};for(t=0,i=e.length;t<i;t++)o(e[t],d)},_wrapResourceData:function(e){var t,i,n,s=this,a=[],r=this.resources.dataSource.view(),o=this.assignments.dataSource.view(),d=new S(o).filter({field:s.assignments.dataTaskIdField,operator:"eq",value:e}).toArray(),l=function(e){var t=null;return new S(d).filter({field:s.assignments.dataResourceIdField,operator:"eq",value:e}).select(function(e){t+=e.get(s.assignments.dataValueField)}),t};for(i=0,n=r.length;i<n;i++)t=r[i],a.push({id:t.get("id"),name:t.get(s.resources.dataTextField),format:t.get(s.resources.dataFormatField)||P,value:l(t.id)});return a},_syncDataSource:function(){this._preventRefresh=!1,this._requestStart(),this.dataSource.sync()},_requestStart:function(){this._progress(!0)},_error:function(){this._progress(!1)},_progress:function(e){v.ui.progress(this.element,e)},_resizable:function(){var t,i,n,s=this,a=this.wrapper,r=f.styles,o=K+r.gridContent,d=a.find(K+r.list),l=a.find(K+r.timeline);this._resizeDraggable=a.find(K+r.splitBar).height(d.height()).hover(function(){e(this).addClass(r.splitBarHover)},function(){e(this).removeClass(r.splitBarHover)}).end().kendoResizable({orientation:"horizontal",handle:K+r.splitBar,start:function(){t=d.width(),i=l.width(),n=l.find(o).scrollLeft()},resize:function(e){var r=e.x.initialDelta;v.support.isRtl(a)&&(r*=-1),t+r<0||i-r<0||(d.width(t+r),l.width(i-r),l.find(o).scrollLeft(n+r),s.timeline.view()._renderCurrentTime())}}).data("kendoResizable")},_scrollable:function(){var t=this,i=f.styles,n=K+i.gridContent,s=K+i.gridHeaderWrap,a=this.timeline.element.find(s),r=this.timeline.element.find(n),o=this.list.element.find(s),d=this.list.element.find(n);_&&d.css("overflow-y","auto"),r.on("scroll",function(){t.scrollTop=this.scrollTop,a.scrollLeft(this.scrollLeft),d.scrollTop(this.scrollTop)}),d.on("scroll",function(){o.scrollLeft(this.scrollLeft)}).on("DOMMouseScroll"+A+" mousewheel"+A,function(t){var i=r.scrollTop(),n=v.wheelDeltaY(t);n&&(t.preventDefault(),e(t.currentTarget).one("wheel"+A,!1),r.scrollTop(i+-n))})},_navigatable:function(){var n,s=this,r=this.options.navigatable,o=this.options.editable,d=this.list.header.find("table"),l=this.list.content.find("table"),c=f.styles,u=v.support.isRtl(this.wrapper),h=this.timeline.element.find(K+c.gridContent),p=d.add(l),g=i(),b={collapse:!1,expand:!0},_=function(e){var t=s.timeline.view()._timeSlots()[0].offsetWidth;h.scrollLeft(h.scrollLeft()+(e?-t:t))},k=function(e){var t=s.timeline.view()._rowHeight;h.scrollTop(h.scrollTop()+(e?-t:t))},w=function(e){var t=s.current.parent("tr"+i()),n=s.current.index(),r=t[e]();0!==s.select().length&&s.clearSelection(),0!==r.length?(s._current(r.children("td:eq("+n+")")),s._scrollTo(s.current)):s.current.is("td")&&"prev"==e?a(d):s.current.is("th")&&"next"==e&&a(l)},y=function(e){var t=s.current[e]();0!==t.length&&(s._current(t),n=s.current.index())},D=function(e){var t=s.dataItem(s.current);t.summary&&t.expanded!==e&&t.set("expanded",e)},C=function(){var e,t,i=s.options.editable;i&&i.destroy!==!1&&!s.list.editable&&(e=s.select(),t=v.attr("uid"),e.length&&s.removeTask(e.attr(t)))};return e(this.wrapper).on("mousedown"+A,"tr"+g+", div"+g+":not("+K+c.line+")",function(t){var n,d=e(t.currentTarget),l=e(t.target).is(":button,a,:input,a>.k-icon,textarea,span.k-icon,span.k-link,.k-input,.k-multiselect-wrap");t.ctrlKey||(r&&(n=d.is("tr")?e(t.target).closest("td"):s.list.content.find("tr"+i(d.attr(v.attr("uid")))+" > td:first"),s._current(n)),!r&&!o||l||(s._focusTimeout=setTimeout(function(){a(s.list.content.find("table"),!0)},2)))}).on("keydown"+A,function(i){var n,s=i.keyCode,a=this,r=e(a._getToolbarItems()),o=r.index(a.toolbar.find(K+c.focused)[0]);if(o===-1&&e(i.target).closest(K+c.toolbar.views).length&&(o=r.index(a.toolbar.find(".k-gantt-views > .k-state-selected:visible > a, .k-current-view:visible > a")[0])),n=i.shiftKey?r[o-1]:r[o+1],s===m.F10)a.toolbar.find(".k-button:visible:first").addClass(c.focused).focus(),i.preventDefault();else if(s==m.TAB&&e(i.target).closest(K+c.toolbar.toolbar).length){if(a.toolbar.find(K+c.focused).removeClass(c.focused).blur(),n)return e(n).addClass(c.focused).focus(),i.preventDefault(),t;this.list.element.is(":visible")?this.list.element.find("table[role=treegrid]").focus():this.element.find(K+c.tasks)[0].focus(),i.preventDefault()}}.bind(this)),r!==!0?(l.on("keydown"+A,function(e){e.keyCode==m.DELETE&&C()}),t):(p.on("focus"+A,function(){var t=this===l.get(0)?"td":"th",i=s.select(),a=s.current||e(i.length?i:this).find(t+":eq("+(n||0)+")");s._current(a),e(s.toolbar.find(K+c.focused)).removeClass(c.focused),e(s.toolbar.find(K+c.toolbar.currentView)).parent().removeClass(c.toolbar.expanded)}).on("blur"+A,function(){s._current(),this==d&&e(this).attr(N,-1)}).on("keydown"+A,function(t){var i,n=t.keyCode;if(s.current)switch(i=s.current.is("td"),n){case m.RIGHT:t.preventDefault(),t.altKey?_():t.ctrlKey?D(u?b.collapse:b.expand):y(u?"prev":"next");break;case m.LEFT:t.preventDefault(),t.altKey?_(!0):t.ctrlKey?D(u?b.expand:b.collapse):y(u?"next":"prev");break;case m.UP:t.preventDefault(),t.altKey?k(!0):w("prev");break;case m.DOWN:t.preventDefault(),t.altKey?k():w("next");break;case m.SPACEBAR:t.preventDefault(),i&&s.select(s.current.closest("tr"));break;case m.ENTER:t.preventDefault(),i?s.options.editable&&s.options.editable.update!==!1&&(s._cachedCurrent=s.current,s.list._startEditHandler(s.current),e(this).one("keyup",function(e){e.stopPropagation()})):s.current.children("a.k-link").click();break;case m.ESC:break;case m.DELETE:i&&C();break;default:n>=49&&n<=57&&"input"!==t.target.tagName.toLowerCase()&&s.view(s.timeline._viewByIndex(n-49))}}),t)},_getToolbarItems:function(){return this.toolbar.find(".k-gantt-toggle:visible").toArray().concat(this.toolbar.find(".k-gantt-actions > .k-button:visible").toArray(),this.toolbar.find(".k-gantt-views > .k-state-selected:visible > a, .k-current-view:visible > a").toArray())},_current:function(t){var i,n=f.styles;this.current&&this.current.length&&this.current.removeClass(n.focused).removeAttr("id"),t&&t.length?(this.current=t.addClass(n.focused).attr("id",O),i=e(v._activeElement()),i.is("table")&&this.wrapper.find(i).length>0&&i.removeAttr(q).attr(q,O)):this.current=null},_dataBind:function(){var t,i=this;i.options.autoBind&&(this._preventRefresh=!0,this._preventDependencyRefresh=!0,t=e.map([this.dataSource,this.dependencies,this.resources.dataSource,this.assignments.dataSource],function(e){return e.fetch()}),e.when.apply(null,t).done(function(){i._preventRefresh=!1,i._preventDependencyRefresh=!1,i.refresh()}))},_resize:function(){this._adjustDimensions(),this.timeline.view()._adjustHeight(),this.timeline.view()._renderCurrentTime(),this.list._adjustHeight()}}),v.PDFMixin&&(v.PDFMixin.extend(f.fn),f.fn._drawPDF=function(){var e=f.styles,t="."+e.list,i=this.wrapper.find(t).width(),n=this.wrapper.clone();return n.find(t).css("width",i),this._drawPDFShadow({content:n},{avoidLinks:this.options.pdf.avoidLinks})}),v.ui.plugin(f),E(!0,f,{styles:se})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.gantt.min.js.map
