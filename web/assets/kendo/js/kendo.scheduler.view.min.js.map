{"version": 3, "sources": ["kendo.scheduler.view.js"], "names": ["f", "define", "kendo", "ui", "scheduler", "$", "levels", "values", "key", "collect", "depth", "level", "idx", "result", "length", "push", "cellspacing", "support", "cssBorderSpacing", "table", "tableRows", "className", "trim", "join", "allDayTable", "timesHeader", "columnLevelCount", "allDaySlot", "rowCount", "text", "<PERSON><PERSON><PERSON><PERSON>", "columnLevels", "columnCount", "columnIndex", "columnLevelIndex", "th", "colspan", "column", "allDayTableRows", "lastLevel", "td", "cellContent", "dateTableRows", "times", "rowLevels", "isMobile", "rowIndex", "rowLevelIndex", "rowspan", "rows", "Array", "split", "rowHeaderRows", "allDay", "indexOf", "content", "scrollbar", "scrollbarWidth", "collidingEvents", "elements", "start", "end", "index", "startIndex", "overlaps", "endIndex", "rangeIndex", "eventsForSlot", "eventElement", "slotStart", "slotEnd", "event", "events", "createColumns", "eventElements", "_createColumns", "createRows", "eventRange", "j", "columnLength", "endOverlaps", "columns", "createDateLayoutConfiguration", "name", "dates", "inner", "configuration", "each", "item", "obj", "minorTicks", "createLayoutConfiguration", "resources", "template", "data", "dataIndex", "resource", "dataSource", "view", "htmlEncode", "getter", "dataTextField", "color", "dataColorField", "field", "title", "value", "dataValueField", "slice", "groupEqFilter", "isArray", "ObservableArray", "addSelectedState", "cell", "replace", "selectedStateRegExp", "Color", "window", "getDate", "date", "Widget", "outerHeight", "_outerHeight", "keys", "NS", "INVERSE_COLOR_CLASS", "MIN_HORIZONTAL_SCROLL_SIZE", "math", "Math", "HINT", "ResourceView", "Class", "extend", "init", "isRtl", "this", "_index", "_timeSlotCollections", "_daySlotCollections", "_isRtl", "addTimeSlotCollection", "startDate", "endDate", "_addCollection", "addDaySlotCollection", "collections", "collection", "SlotCollection", "timeSlotCollectionCount", "daySlotCollectionCount", "daySlotByPosition", "x", "y", "byDate", "_slotByPosition", "timeSlotByPosition", "collectionIndex", "slotIndex", "slot", "width", "height", "nextSlot", "horizontalEnd", "verticalEnd", "count", "at", "offsetWidth", "offsetHeight", "offsetLeft", "offsetTop", "refresh", "timeSlotRanges", "startTime", "endTime", "_startSlot", "inRange", "_endSlot", "first", "last", "_continuousRange", "TimeSlotRange", "daySlotR<PERSON><PERSON>", "isAllDay", "MS_PER_DAY", "DaySlotRange", "range", "head", "tail", "startSlot", "endSlot", "ranges", "slotRanges", "isDay", "_startTime", "toUtcTime", "_endTime", "undefined", "isMultiDay", "_startCollection", "startInRange", "_endCollection", "endInRange", "_getCollections", "continuousSlot", "reverse", "pad", "isDaySlot", "firstSlot", "lastSlot", "upSlot", "keepCollection", "groupByDateVertically", "that", "moveToDaySlot", "isFirstCell", "_verticalSlot", "downSlot", "moveToTimeSlot", "leftSlot", "_horizontalSlot", "rightSlot", "step", "swapCollection", "_collection", "multiday", "time", "slotByStartDate", "slotByEndDate", "getSlotCollection", "getTimeSlotCollection", "getDaySlotCollection", "SlotRange", "options", "innerHeight", "addEvent", "outerRect", "snap", "_rect", "property", "top", "bottom", "left", "right", "startOffset", "startSlotDuration", "endOffset", "endSlotDuration", "element", "round", "innerRect", "innerWidth", "groupIndex", "_slots", "_events", "_start", "_end", "_groupIndex", "_collectionIndex", "allday", "addTimeSlot", "isHorizontal", "TimeSlot", "addDaySlot", "eventCount", "DaySlot", "Slot", "clientWidth", "clientHeight", "timezone", "toLocalDate", "fn", "apply", "arguments", "offsetX", "rtl", "offset", "duration", "difference", "floor", "children", "<PERSON><PERSON><PERSON><PERSON>", "firstChildHeight", "firstChildTop", "Date", "SchedulerView", "call", "_normalizeOptions", "_scrollbar", "_resizeHint", "_moveHint", "_cellId", "guid", "_resourcesForGroups", "_selectedSlots", "visibleEndDate", "setMilliseconds", "workDayStart", "workDayEnd", "_isMobile", "mobile", "mobileOS", "_addResourceView", "resourceView", "groups", "dateForTitle", "format", "selectedDateFormat", "shortDateForTitle", "selectedShortDateFormat", "mobileDateForTitle", "selectedMobileDateFormat", "_changeGroup", "selection", "previous", "method", "_isGroupedByDate", "_changeDate", "group", "_changeGroupContinuously", "_changeViewPeriod", "_isInRange", "newStart", "newEnd", "min", "max", "_horizontalSlots", "multiple", "tempSlot", "horizontalRange", "isVertical", "_isVerticallyGrouped", "_normalizeHorizontalSelection", "_getNextHorizontalRange", "_continuousSlot", "_verticalSlots", "verticalRange", "_normalizeVerticalSelection", "_getNextVerticalRange", "_footer", "html", "messages", "today", "footer", "appendTo", "on", "e", "action", "currentDate", "timezoneOffset", "preventDefault", "convert", "getTimezoneOffset", "trigger", "constrainSelection", "move", "shift", "slots", "backward", "handled", "verticalByDate", "DOWN", "UP", "_updateDirection", "LEFT", "RIGHT", "moveToEventInGroup", "selectedEvents", "prev", "found", "lastSelected", "i", "_continuousEvents", "uid", "inArray", "moveToEvent", "allEvents", "uniqueAllEvents", "sortedEvents", "eventIndex", "_getAllEvents", "_getUniqueEvents", "_getSortedEvents", "_getNextEventIndexBySlot", "_getStartIdx", "_getSelectedSlot", "current", "candidate", "_current", "has", "_scrollTo", "select", "clearSelection", "_selectEvents", "_selectSlots", "eventStartDate", "tempIndex", "slotStartDate", "getTime", "selectedEventIndex", "concat", "exists", "sort", "second", "firstStartDate", "secondStartDate", "setAttribute", "groupEvent", "groupEvents", "add", "addClass", "attr", "addDays", "_resourceValue", "valuePrimitive", "_resourceBySlot", "resourceIndex", "setter", "groupedResources", "total", "_createResizeHint", "css", "_removeResizeHint", "remove", "_removeMoveHint", "filter", "container", "elementOffset", "elementOffsetDir", "containerScroll", "scrollTop", "containerOffsetDir", "bottomDistance", "_inverseEventColor", "eventColor", "eventColorIsDark", "isDark", "eventBackground", "eventBackgroundIsDark", "_eventTmpl", "wrapper", "tmpl", "settings", "Template", "templateSettings", "paramName", "type", "state", "storage", "proxy", "eventResources", "eventResource", "resourceColor", "get", "createLayout", "layout", "allDayIndex", "splice", "find", "append", "_topSection", "_bottomSection", "_groupOrientation", "_outerWidth", "_scroller", "refreshLayout", "datesHeaderRows", "isSchedulerHeightSet", "contentDiv", "timesTable", "toolbar", "headerHeight", "paddingDirection", "eq", "el", "initialHeight", "newHeight", "style", "kineticScrollNeeded", "parseInt", "removeClass", "thElm", "wrap", "parent", "touchScroller", "bind", "scrollLeft", "avoidScrolling", "target", "closest", "movable", "_touchScroller", "scrollElement", "sender", "groupIdx", "groupLength", "_createDateLayout", "_createColumnsLayout", "orientation", "_createRowsLayout", "selectionByElement", "removeAttr", "destroy", "calendarInfo", "getCulture", "calendars", "standard", "prevGroupSlot", "nextGroupSlot", "_eventOptionsForMove", "_updateEventForResize", "_updateEventForSelection", "re", "processor", "parts", "channels", "formats", "resolveColor", "process", "exec", "r", "g", "b", "normalizeByte", "prototype", "char<PERSON>t", "substr", "toLowerCase", "namedColors", "isNaN", "percBrightness", "sqrt", "brightnessValue", "aqua", "azure", "beige", "black", "blue", "brown", "coral", "cyan", "darkblue", "dark<PERSON>an", "darkgray", "darkgreen", "darkorange", "darkred", "dimgray", "fuchsia", "gold", "goldenrod", "gray", "green", "greenyellow", "indigo", "ivory", "khaki", "lightblue", "<PERSON><PERSON>rey", "lightgreen", "lightpink", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumblue", "navy", "olive", "orange", "orangered", "orchid", "pink", "plum", "purple", "red", "royalblue", "salmon", "silver", "skyblue", "slateblue", "slategray", "snow", "steelblue", "tan", "teal", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,wBAAyB,cAAeD,IACjD,WA89DE,MAr9DAE,OAAMC,GAAGC,aACR,SAAUC,GAEP,QAASC,GAAOC,EAAQC,GAEpB,QAASC,GAAQC,EAAOH,GAAxB,GAGYI,GACKC,CAFb,IADAL,EAASA,EAAOC,GAGZ,IADIG,EAAQE,EAAOH,GAASG,EAAOH,OAC1BE,EAAM,EAAGA,EAAML,EAAOO,OAAQF,IACnCD,EAAMI,KAAKR,EAAOK,IAClBH,EAAQC,EAAQ,EAAGH,EAAOK,IAPtC,GAAIC,KAYJ,OADAJ,GAAQ,EAAGF,GACJM,EAEX,QAASG,KACL,MAAId,GAAMe,QAAQC,iBACP,GAEJ,kBAEX,QAASC,GAAMC,EAAWC,GACtB,MAAKD,GAAUN,OAGR,UAAYE,IAAgB,WAAaX,EAAEiB,KAAK,sBAAwBD,GAAa,KAAO,SAAgBD,EAAUG,KAAK,aAAe,gBAFtI,GAIf,QAASC,GAAYJ,EAAWC,GAC5B,MAAKD,GAAUN,OAGR,kCAAsCK,EAAMC,EAAWC,GAAa,SAFhE,GAIf,QAASI,GAAYC,EAAkBC,EAAYC,GAAnD,GAGiBhB,GAFTQ,IACJ,IAAIQ,EAAW,EACX,IAAShB,EAAM,EAAGA,EAAMc,EAAkBd,IACtCQ,EAAUL,KAAK,mBAMvB,OAHIY,IACAP,EAAUL,KAAK,yCAA2CY,EAAWE,KAAO,SAE5ED,EAAW,EACJvB,IAEJA,EAAE,kCAAoCc,EAAMC,GAAa,UAEpE,QAASU,GAAYC,EAAcC,EAAaL,GAAhD,GAEQM,GACKC,EACDvB,EACAwB,EACAC,EAEIC,EAKRC,EAEIC,EACAC,EACAC,EAhBJC,IAEJ,KAASR,EAAmB,EAAGA,EAAmBH,EAAajB,OAAQoB,IAAoB,CAIvF,IAHIvB,EAAQoB,EAAaG,GACrBC,KACAC,EAAUJ,EAAcrB,EAAMG,OAC7BmB,EAAc,EAAGA,EAActB,EAAMG,OAAQmB,IAC1CI,EAAS1B,EAAMsB,GACnBE,EAAGpB,KAAK,iBAAmBsB,EAAOD,SAAWA,GAAW,aAAeC,EAAOhB,WAAa,IAAM,KAAOgB,EAAOR,KAAO,QAE1Ha,GAAc3B,KAAKoB,EAAGZ,KAAK,KAG/B,GADIe,KACAX,EAAY,CAIZ,IAHIY,EAAYR,EAAaA,EAAajB,OAAS,GAC/C0B,KACAC,EAAcd,EAAWc,YACxBR,EAAc,EAAGA,EAAcM,EAAUzB,OAAQmB,IAClDO,EAAGzB,KAAK,eAAiBwB,EAAUN,GAAaZ,WAAa,IAAM,MAAQoB,EAAcA,EAAYR,GAAe,UAAY,QAEpIK,GAAgBvB,KAAKyB,EAAGjB,KAAK,KAEjC,MAAOlB,GAAE,wFAA+Fc,EAAMuB,GAAiBlB,EAAYc,EAAiB,8BAAgC,gBAEhM,QAASK,GAAMC,EAAWhB,EAAUiB,GAApC,GAGQC,GACKC,EACDpC,EACAqC,EACA3B,EACAQ,EAPJoB,EAAWC,MAAMtB,GAAUL,OAAO4B,MAAM,KACxCC,IAEJ,KAASL,EAAgB,EAAGA,EAAgBH,EAAU9B,OAAQiC,IAK1D,IAJIpC,EAAQiC,EAAUG,GAClBC,EAAUpB,EAAWjB,EAAMG,OAG1BgC,EAAW,EAAGA,EAAWnC,EAAMG,OAAQgC,IACxCzB,EAAYV,EAAMmC,GAAUzB,WAAa,GACzCQ,EAAOlB,EAAMmC,GAAUjB,KACnBlB,EAAMmC,GAAUO,SAChBhC,EAAY,6BAEZwB,GAAYxB,EAAUiC,QAAQ,iCAC9BzB,EAAO,wCAA0CA,EAAO,WAE5DoB,EAAKD,EAAUF,IAAa,cAAgBzB,EAAY,cAAgB2B,EAAU,KAAOnB,EAAO,OAGxG,KAAKiB,EAAW,EAAGA,EAAWlB,EAAUkB,IACpCM,EAAcrC,KAAKkC,EAAKH,GAE5B,OAAIlB,GAAW,EACJvB,IAEJA,EAAE,kCAAoCc,EAAMiC,GAAiB,UAExE,QAASG,KACL,MAAOlD,GAAE,2CAAkDW,IAAgB,sCA0qB/E,QAASwC,KAEL,MADAC,GAAiBA,EAAiBA,EAAiBvD,EAAMe,QAAQuC,YA+5BrE,QAASE,GAAgBC,EAAUC,EAAOC,GACtC,GAAIjD,GAAKkD,EAAOC,EAAYC,EAAUC,CACtC,KAAKrD,EAAM+C,EAAS7C,OAAS,EAAGF,GAAO,EAAGA,IACtCkD,EAAQI,EAAWP,EAAS/C,IAC5BmD,EAAaD,EAAMF,MACnBK,EAAWH,EAAMD,IACjBG,EAAWD,GAAcH,GAASK,GAAYL,GAC1CI,GAAYD,GAAcH,GAASK,GAAYJ,GAAOD,GAASG,GAAcF,GAAOE,KAChFA,EAAaH,IACbA,EAAQG,GAERE,EAAWJ,IACXA,EAAMI,GAIlB,OAAOE,GAAcR,EAAUC,EAAOC,GAE1C,QAASK,GAAWE,GAChB,OACIR,MAAOQ,EAAaR,MACpBC,IAAKO,EAAaP,KAG1B,QAASM,GAAcR,EAAUU,EAAWC,GAA5C,GAEa1D,GACD2D,EAFJC,IACJ,KAAS5D,EAAM,EAAGA,EAAM+C,EAAS7C,OAAQF,IACjC2D,EAAQL,EAAWP,EAAS/C,KAC5B2D,EAAMX,MAAQS,GAAaE,EAAMV,IAAMQ,GAAaE,EAAMX,OAASS,GAAaE,EAAMV,KAAOS,IAC7FE,EAAOzD,KAAK4C,EAAS/C,GAG7B,OAAO4D,GAEX,QAASC,GAAcC,GACnB,MAAOC,GAAeD,GAE1B,QAASE,GAAWF,GAChB,MAAOC,GAAeD,GAkJ1B,QAASC,GAAeD,GAAxB,GAEa9D,GACD2D,EACAM,EACAxC,EACKyC,EAAOC,EACRC,EANRC,IACJ,KAASrE,EAAM,EAAGA,EAAM8D,EAAc5D,OAAQF,IAAO,CAIjD,IAHI2D,EAAQG,EAAc9D,GACtBiE,EAAaX,EAAWK,GACxBlC,EAAS,KACJyC,EAAI,EAAGC,EAAeE,EAAQnE,OAAQgE,EAAIC,EAAcD,IAE7D,GADIE,EAAcH,EAAWjB,MAAQqB,EAAQH,GAAGjB,IAC5CgB,EAAWjB,MAAQqB,EAAQH,GAAGlB,OAASoB,EAAa,CACpD3C,EAAS4C,EAAQH,GACbzC,EAAOwB,IAAMgB,EAAWhB,MACxBxB,EAAOwB,IAAMgB,EAAWhB,IAE5B,OAGHxB,IACDA,GACIuB,MAAOiB,EAAWjB,MAClBC,IAAKgB,EAAWhB,IAChBW,WAEJS,EAAQlE,KAAKsB,IAEjBA,EAAOmC,OAAOzD,KAAKwD,GAEvB,MAAOU,GAEX,QAASC,GAA8BC,EAAMC,EAAOC,EAAO1C,GACvD,GAAI2C,KAcJ,OAbAjF,GAAEkF,KAAKH,EAAO,SAAUtB,EAAO0B,GAAjB,GACNnE,GAAYmE,EAAKnE,UAAY,eAAiBmE,EAAKnE,UAAY,cAC/DoE,GACA5D,KAAM2D,EAAK3D,KACXR,UAAWA,EAGXoE,GAAIN,GADJxC,IAAU6C,EAAKE,WACHR,EAA8BC,EAAMK,EAAKP,QAASI,EAAO1C,GAEzD0C,EAEhBC,EAAcvE,KAAK0E,KAEhBH,EAEX,QAASK,GAA0BR,EAAMS,EAAWP,EAAOQ,EAAUT,EAAOzC,GAA5E,GAcgBmD,GACKC,EACDN,EAfZO,EAAWJ,EAAU,GACrBN,IACJ,IAAIU,EAAU,CACV,GAAIZ,GAASC,EACThF,EAAEkF,KAAKH,EAAO,SAAUtB,EAAO0B,GAEvBA,EAAKL,GADLxC,IAAU6C,EAAKE,WACFC,EAA0BR,EAAMS,EAAWJ,EAAKP,QAASY,EAAUL,EAAKP,QAAStC,GAEjFgD,EAA0BR,EAAMS,EAAW,KAAMC,KAGtEP,EAAgBF,MAGhB,KADIU,EAAOE,EAASC,WAAWC,OACtBH,EAAY,EAAGA,EAAYD,EAAKhF,OAAQiF,IACzCN,GACA5D,KAAMgE,GACFhE,KAAM3B,EAAMiG,WAAWjG,EAAMkG,OAAOJ,EAASK,eAAeP,EAAKC,KACjEO,MAAOpG,EAAMkG,OAAOJ,EAASO,gBAAgBT,EAAKC,IAClDS,MAAOR,EAASQ,MAChBC,MAAOT,EAASS,MAChBtB,KAAMa,EAASb,KACfuB,MAAOxG,EAAMkG,OAAOJ,EAASW,gBAAgBb,EAAKC,MAEtD1E,UAAW,sCAEfoE,EAAIN,GAAQQ,EAA0BR,EAAMS,EAAUgB,MAAM,GAAIvB,EAAOQ,GACvEP,EAAcvE,KAAK0E,EAG3B,OAAOH,GAEX,MAAOD,GAEX,QAASwB,GAAcH,GACnB,MAAO,UAAUlB,GACb,GAAInF,EAAEyG,QAAQtB,IAASA,YAAgBtF,GAAM4F,KAAKiB,gBAAiB,CAC/D,IAAK,GAAInG,GAAM,EAAGA,EAAM4E,EAAK1E,OAAQF,IACjC,GAAI4E,EAAK5E,IAAQ8F,EACb,OAAO,CAGf,QAAO,EAEX,MAAOlB,IAAQkB,GAIvB,QAASM,GAAiBC,GACtBA,EAAK5F,UAAY4F,EAAK5F,UAAU6F,QAAQC,EAAqB,IAAM,oBA18D1E,GAkxBO1D,GAy8BA2D,EA6OAD,EAv8DAjH,EAAQmH,OAAOnH,MAAOC,EAAKD,EAAMC,GAAImH,EAAUpH,EAAMqH,KAAKD,QAASE,EAASrH,EAAGqH,OAAQC,EAAcvH,EAAMwH,aAAcC,EAAOzH,EAAMyH,KAAMC,EAAK,sBAAuBC,EAAsB,kBAAmBC,EAA6B,KAAMC,EAAOC,KA0G3PC,EAAO,0LACPC,EAAehI,EAAMiI,MAAMC,QAC3BC,KAAM,SAAUvE,EAAOwE,GACnBC,KAAKC,OAAS1E,EACdyE,KAAKE,wBACLF,KAAKG,uBACLH,KAAKI,OAASL,GAElBM,sBAAuB,SAAUC,EAAWC,GACxC,MAAOP,MAAKQ,eAAeF,EAAWC,EAASP,KAAKE,uBAExDO,qBAAsB,SAAUH,EAAWC,GACvC,MAAOP,MAAKQ,eAAeF,EAAWC,EAASP,KAAKG,sBAExDK,eAAgB,SAAUF,EAAWC,EAASG,GAC1C,GAAIC,GAAa,GAAIC,GAAeN,EAAWC,EAASP,KAAKC,OAAQS,EAAYnI,OAEjF,OADAmI,GAAYlI,KAAKmI,GACVA,GAEXE,wBAAyB,WACrB,MAAOb,MAAKE,qBAAqB3H,QAErCuI,uBAAwB,WACpB,MAAOd,MAAKG,oBAAoB5H,QAEpCwI,kBAAmB,SAAUC,EAAGC,EAAGC,GAC/B,MAAOlB,MAAKmB,gBAAgBH,EAAGC,EAAGjB,KAAKG,oBAAqBe,IAEhEE,mBAAoB,SAAUJ,EAAGC,EAAGC,GAChC,MAAOlB,MAAKmB,gBAAgBH,EAAGC,EAAGjB,KAAKE,qBAAsBgB,IAEjEC,gBAAiB,SAAUH,EAAGC,EAAGP,EAAaQ,GAA7B,GACJG,GACDV,EACKW,EACDC,EACAC,EACAC,EACAC,EACAC,EACAC,CARZ,KAASP,EAAkB,EAAGA,EAAkBX,EAAYnI,OAAQ8I,IAEhE,IADIV,EAAaD,EAAYW,GACpBC,EAAY,EAAGA,EAAYX,EAAWkB,QAASP,IAqBpD,GApBIC,EAAOZ,EAAWmB,GAAGR,GACrBE,EAAQD,EAAKQ,YACbN,EAASF,EAAKS,aAEdL,EAAgBJ,EAAKU,WAAaT,EAClCI,EAAcL,EAAKW,UAAYT,EAC9BP,IACDQ,EAAWf,EAAWmB,GAAGR,EAAY,IAErCI,IACIA,EAASO,YAAcV,EAAKU,WAExBN,EADA3B,KAAKI,OACWmB,EAAKU,YAAcV,EAAKU,WAAaP,EAASO,YAE9CP,EAASO,WAG7BL,EAAcF,EAASQ,WAG3BlB,GAAKO,EAAKU,YAAcjB,EAAIW,GAAiBV,GAAKM,EAAKW,WAAajB,EAAIW,EACxE,MAAOL,IAKvBY,QAAS,WACL,GAAId,EACJ,KAAKA,EAAkB,EAAGA,EAAkBrB,KAAKG,oBAAoB5H,OAAQ8I,IACzErB,KAAKG,oBAAoBkB,GAAiBc,SAE9C,KAAKd,EAAkB,EAAGA,EAAkBrB,KAAKE,qBAAqB3H,OAAQ8I,IAC1ErB,KAAKE,qBAAqBmB,GAAiBc,WAGnDC,eAAgB,SAAUC,EAAWC,GAArB,GAMRhH,GALAoF,EAAcV,KAAKE,qBACnB7E,EAAQ2E,KAAKuC,WAAWF,EAAW3B,EAWvC,KAVKrF,EAAMmH,SAAWH,GAAahH,EAAMkG,KAAKjG,MAC1CD,EAAQ,MAERC,EAAMD,EACNgH,EAAYC,IACZhH,EAAM0E,KAAKyC,SAASH,EAAS5B,IAE7BpF,IAAQA,EAAIkH,SAAWF,GAAWhH,EAAIiG,KAAKlG,QAC3CC,EAAM,MAEI,OAAVD,GAA0B,OAARC,EAClB,QAEJ,IAAc,OAAVD,EAAgB,CAChB,GAAIC,EAAIiG,KAAKjG,KAAO+G,EAChB,QAEJhH,IACImH,SAAS,EACTjB,KAAMb,EAAYpF,EAAIiG,KAAKF,iBAAiBqB,SAGpD,GAAY,OAARpH,EAAc,CACd,GAAID,EAAMkG,KAAKlG,OAASiH,EACpB,QAEJhH,IACIkH,SAAS,EACTjB,KAAMb,EAAYrF,EAAMkG,KAAKF,iBAAiBsB,QAGtD,MAAO3C,MAAK4C,iBAAiBC,EAAenC,EAAarF,EAAOC,IAEpEwH,cAAe,SAAUT,EAAWC,EAASS,GAA9B,GAMPzH,GALAoF,EAAcV,KAAKG,oBACnB9E,EAAQ2E,KAAKuC,WAAWF,EAAW3B,EAAaqC,EAWpD,KAVK1H,EAAMmH,SAAWH,GAAahH,EAAMkG,KAAKjG,MAC1CD,EAAQ,MAERC,EAAMD,EACNgH,EAAYC,IACZhH,EAAM0E,KAAKyC,SAASH,EAAS5B,EAAaqC,IAE1CzH,IAAQA,EAAIkH,SAAWF,GAAWhH,EAAIiG,KAAKlG,QAC3CC,EAAM,MAEI,OAAVD,GAA0B,OAARC,EAClB,QAEJ,IAAc,OAAVD,EAAgB,CAChB,GAAIC,EAAIiG,KAAKjG,KAAO+G,EAChB,QAEJ,GACIA,IAAa1K,EAAMqH,KAAKgE,WACxB3H,EAAQ2E,KAAKuC,WAAWF,EAAW3B,EAAaqC,UAC1C1H,EAAMmH,SAAWH,GAAahH,EAAMkG,KAAKjG,KAEvD,GAAY,OAARA,EAAc,CACd,GAAID,EAAMkG,KAAKlG,OAASiH,EACpB,QAEJ,GACIA,IAAW3K,EAAMqH,KAAKgE,WACtB1H,EAAM0E,KAAKyC,SAASH,EAAS5B,EAAaqC,UACpCzH,EAAIkH,SAAWF,GAAWhH,EAAIiG,KAAKlG,OAEjD,MAAO2E,MAAK4C,iBAAiBK,EAAcvC,EAAarF,EAAOC,IAEnEsH,iBAAkB,SAAUM,EAAOxC,EAAarF,EAAOC,GAArC,GAML+F,GACDV,EACA+B,EACAC,EACAQ,EACAC,EAVJC,EAAYhI,EAAMkG,KAClB+B,EAAUhI,EAAIiG,KACd/F,EAAa6H,EAAUhC,gBACvB3F,EAAW4H,EAAQjC,gBACnBkC,IACJ,KAASlC,EAAkB7F,EAAY6F,GAAmB3F,EAAU2F,IAC5DV,EAAaD,EAAYW,GACzBqB,EAAQ/B,EAAW+B,QACnBC,EAAOhC,EAAWgC,OAClBQ,GAAO,EACPC,GAAO,EACP/B,GAAmB7F,IACnB4H,GAAQ/H,EAAMmH,SAEdnB,GAAmB3F,IACnByH,GAAQ7H,EAAIkH,SAEZE,EAAMrH,MAAQgI,EAAUhI,QACxBqH,EAAQW,GAERV,EAAKtH,MAAQiI,EAAQjI,QACrBsH,EAAOW,GAEP9H,EAAaE,IACT2F,GAAmB7F,EACnB2H,GAAO,EACA9B,GAAmB3F,EAC1B0H,GAAO,EAEPD,EAAOC,GAAO,GAGtBG,EAAO/K,KAAK,GAAI0K,IACZ7H,MAAOqH,EACPpH,IAAKqH,EACLhC,WAAYA,EACZwC,KAAMA,EACNC,KAAMA,IAGd,OAAOG,IAEXC,WAAY,SAAUxH,EAAOyH,GAAjB,GACJpB,GAAYrG,EAAM0H,YAAc/L,EAAMqH,KAAK2E,UAAU3H,EAAMX,OAC3DiH,EAAUtG,EAAM4H,UAAYjM,EAAMqH,KAAK2E,UAAU3H,EAAMV,IAI3D,OAHcuI,UAAVJ,IACAA,EAAQzH,EAAM8H,cAEdL,EACOzD,KAAK8C,cAAcT,EAAWC,EAAStG,EAAM+G,UAEjD/C,KAAKoC,eAAeC,EAAWC,IAE1CiB,OAAQ,SAAUlB,EAAWC,EAASmB,EAAOV,GAOzC,MANwB,gBAAbV,KACPA,EAAY1K,EAAMqH,KAAK2E,UAAUtB,IAEf,gBAAXC,KACPA,EAAU3K,EAAMqH,KAAK2E,UAAUrB,IAE/BmB,EACOzD,KAAK8C,cAAcT,EAAWC,EAASS,GAE3C/C,KAAKoC,eAAeC,EAAWC,IAE1CyB,iBAAkB,SAAU/E,EAAM0B,GAAhB,GACLW,GACDV,CADR,KAASU,EAAkB,EAAGA,EAAkBX,EAAYnI,OAAQ8I,IAEhE,GADIV,EAAaD,EAAYW,GACzBV,EAAWqD,aAAahF,GACxB,MAAO2B,EAGf,OAAO,OAEXsD,eAAgB,SAAUjF,EAAM0B,EAAaqC,GAA7B,GACH1B,GACDV,CADR,KAASU,EAAkB,EAAGA,EAAkBX,EAAYnI,OAAQ8I,IAEhE,GADIV,EAAaD,EAAYW,GACzBV,EAAWuD,WAAWlF,EAAM+D,GAC5B,MAAOpC,EAGf,OAAO,OAEXwD,gBAAiB,SAAUV,GACvB,MAAOA,GAAQzD,KAAKG,oBAAsBH,KAAKE,sBAEnDkE,eAAgB,SAAU7C,EAAM8C,GAAhB,GACRC,GAAMD,KAAe,EACrB3D,EAAcV,KAAKmE,gBAAgB5C,EAAKgD,WACxC5D,EAAaD,EAAYa,EAAKF,gBAAkBiD,EACpD,OAAO3D,GAAaA,EAAW0D,EAAU,OAAS,WAAaR,QAEnEW,UAAW,WACP,GAAI9D,GAAcV,KAAKmE,gBAAgBnE,KAAKc,yBAC5C,OAAOJ,GAAY,GAAGgC,SAE1B+B,SAAU,WACN,GAAI/D,GAAcV,KAAKmE,gBAAgBnE,KAAKc,yBAC5C,OAAOJ,GAAYA,EAAYnI,OAAS,GAAGoK,QAE/C+B,OAAQ,SAAUnD,EAAMoD,EAAgBC,GAAhC,GACAC,GAAO7E,KACP8E,EAAgB,SAAUP,EAAWlD,EAAiB9F,GACtD,GAAIwJ,GAAwB,IAAVxJ,CAClB,KAAKoJ,IAAmBJ,GAAaQ,GAAeF,EAAK/D,yBACrD,MAAO+D,GAAK1E,oBAAoB,GAAG2B,GAAGT,GAM9C,OAHKrB,MAAKa,4BACN8D,GAAiB,GAEd3E,KAAKgF,cAAczD,KAAUuD,EAAeF,IAEvDK,SAAU,SAAU1D,EAAMoD,EAAgBC,GAAhC,GACFC,GAAO7E,KACPkF,EAAiB,SAAUX,EAAWlD,EAAiB9F,GACvD,IAAKoJ,GAAkBJ,GAAaM,EAAKhE,0BACrC,MAAOgE,GAAK3E,qBAAqB3E,GAAOuG,GAAG,GAMnD,OAHK9B,MAAKa,4BACN8D,GAAiB,GAEd3E,KAAKgF,cAAczD,EAAM,EAAG2D,EAAgBN,IAEvDO,SAAU,SAAU5D,EAAMqD,GACtB,MAAO5E,MAAKoF,gBAAgB7D,KAAUqD,IAE1CS,UAAW,SAAU9D,EAAMqD,GACvB,MAAO5E,MAAKoF,gBAAgB7D,EAAM,EAAGqD,IAEzCQ,gBAAiB,SAAU7D,EAAM+D,EAAMV,GAAtB,GAWTjE,GAVApF,EAAQgG,EAAKhG,MACbgJ,EAAYhD,EAAKgD,UACjBlD,EAAkBE,EAAKF,gBACvBX,EAAcV,KAAKmE,gBAAgBI,EAQvC,OAPAA,IAAYK,GAAgCL,EACxCA,EACAhJ,GAAS+J,EAETjE,GAAmBiE,EAEnB3E,EAAaD,EAAYW,GACtBV,EAAaA,EAAWmB,GAAGvG,GAASsI,QAE/CmB,cAAe,SAAUzD,EAAM+D,EAAMC,EAAgBX,GAAtC,GAePjE,GAdApF,EAAQgG,EAAKhG,MACbgJ,EAAYhD,EAAKgD,UACjBlD,EAAkBE,EAAKF,gBACvBX,EAAcV,KAAKmE,gBAAgBI,EAEvC,QADAhD,EAAOgE,EAAehB,EAAWlD,EAAiB9F,IAEvCgG,GAEXgD,GAAYK,GAAgCL,EACxCA,EACAlD,GAAmBiE,EAEnB/J,GAAS+J,EAET3E,EAAaD,EAAYW,GACtBV,EAAaA,EAAWmB,GAAGvG,GAASsI,SAE/C2B,YAAa,SAAUjK,EAAOkK,GAC1B,GAAI/E,GAAc+E,EAAWzF,KAAKG,oBAAsBH,KAAKE,oBAC7D,OAAOQ,GAAYnF,IAEvBgH,WAAY,SAAUmD,EAAMhF,EAAaqC,GAA7B,GAOJxB,GANAZ,EAAaX,KAAK+D,iBAAiB2B,EAAMhF,GACzC8B,GAAU,CAUd,OATK7B,KACDA,EAAaD,EAAY,GACzB8B,GAAU,GAEVjB,EAAOZ,EAAWgF,gBAAgBD,EAAM3C,GACvCxB,IACDA,EAAOZ,EAAW+B,QAClBF,GAAU,IAGVjB,KAAMA,EACNiB,QAASA,IAGjBC,SAAU,SAAUiD,EAAMhF,EAAaqC,GAA7B,GAOFxB,GANAZ,EAAaX,KAAKiE,eAAeyB,EAAMhF,EAAaqC,GACpDP,GAAU,CAUd,OATK7B,KACDA,EAAaD,EAAYA,EAAYnI,OAAS,GAC9CiK,GAAU,GAEVjB,EAAOZ,EAAWiF,cAAcF,EAAM3C,GACrCxB,IACDA,EAAOZ,EAAWgC,OAClBH,GAAU,IAGVjB,KAAMA,EACNiB,QAASA,IAGjBqD,kBAAmB,SAAUtK,EAAOkI,GAChC,MAAOzD,MAAKyD,EAAQ,uBAAyB,yBAAyBlI,IAE1EuK,sBAAuB,SAAUvK,GAC7B,MAAOyE,MAAKE,qBAAqB3E,IAErCwK,qBAAsB,SAAUxK,GAC5B,MAAOyE,MAAKG,oBAAoB5E,MAGpCyK,EAAYrO,EAAMiI,MAAMC,QACxBC,KAAM,SAAUmG,GACZnO,EAAE+H,OAAOG,KAAMiG,IAEnBC,YAAa,WAAA,GAKA5E,GAJLX,EAAaX,KAAKW,WAClBnF,EAAawE,KAAK3E,MAAME,MACxBG,EAAWsE,KAAK1E,IAAIC,MACpBjD,EAAS,CACb,KAASgJ,EAAY9F,EAAY8F,GAAa5F,EAAU4F,IACpDhJ,GAAUqI,EAAWmB,GAAGR,GAAWU,YAEvC,OAAO1J,IAEX2D,OAAQ,WACJ,MAAO+D,MAAKW,WAAW1E,UAE3BkK,SAAU,SAAUnK,GAChBgE,KAAK/D,SAASzD,KAAKwD,IAEvBqH,UAAW,WACP,MAAIrD,MAAK3E,MAAM4G,WAAajC,KAAK1E,IAAI2G,WAC1BjC,KAAK1E,IAET0E,KAAK3E,OAEhBiI,QAAS,WACL,MAAItD,MAAK3E,MAAM4G,WAAajC,KAAK1E,IAAI2G,WAC1BjC,KAAK3E,MAET2E,KAAK1E,OAGhBuH,EAAgBmD,EAAUnG,QAC1BqG,YAAa,WAAA,GAKA5E,GAJLX,EAAaX,KAAKW,WAClBnF,EAAawE,KAAK3E,MAAME,MACxBG,EAAWsE,KAAK1E,IAAIC,MACpBjD,EAAS,CACb,KAASgJ,EAAY9F,EAAY8F,GAAa5F,EAAU4F,IACpDhJ,GAAUqI,EAAWmB,GAAGR,GAAWU,YAEvC,OAAO1J,IAEX8N,UAAW,SAAU/K,EAAOC,EAAK+K,GAC7B,MAAOrG,MAAKsG,MAAM,SAAUjL,EAAOC,EAAK+K,IAE5CC,MAAO,SAAUC,EAAUlL,EAAOC,EAAK+K,GAAhC,GACCG,GACAC,EACAC,EACAC,EAqBIC,EAIAC,EAEAC,EAIAC,EA9BJ1D,EAAYrD,KAAK3E,MACjBiI,EAAUtD,KAAK1E,IACfyE,EAAQpI,EAAMe,QAAQqH,MAAMsD,EAAU2D,QAsC1C,OArCoB,gBAAT3L,KACPA,EAAQ1D,EAAMqH,KAAK2E,UAAUtI,IAEf,gBAAPC,KACPA,EAAM3D,EAAMqH,KAAK2E,UAAUrI,IAE3B+K,GACAG,EAAMnD,EAAUnB,UAChBuE,EAASnD,EAAQpB,UAAYoB,EAAQiD,EAAW,UAC5CxG,GACA2G,EAAOpD,EAAQrB,WACf0E,EAAQtD,EAAUpB,WAAaoB,EAAUkD,EAAW,WAEpDG,EAAOrD,EAAUpB,WACjB0E,EAAQrD,EAAQrB,WAAaqB,EAAQiD,EAAW,YAGhDK,EAAcvL,EAAQgI,EAAUhI,MAChCuL,EAAc,IACdA,EAAc,GAEdC,EAAoBxD,EAAU/H,IAAM+H,EAAUhI,MAClDmL,EAAMnD,EAAUnB,UAAYmB,EAAUkD,EAAW,UAAYK,EAAcC,EACvEC,EAAYxD,EAAQhI,IAAMA,EAC1BwL,EAAY,IACZA,EAAY,GAEZC,EAAkBzD,EAAQhI,IAAMgI,EAAQjI,MAC5CoL,EAASnD,EAAQpB,UAAYoB,EAAQiD,EAAW,UAAYjD,EAAQiD,EAAW,UAAYO,EAAYC,EACnGhH,GACA2G,EAAOjH,KAAKwH,MAAM3D,EAAQrB,WAAaqB,EAAQiD,EAAW,SAAWO,EAAYC,GACjFJ,EAAQlH,KAAKwH,MAAM5D,EAAUpB,WAAaoB,EAAUkD,EAAW,SAAWlD,EAAUkD,EAAW,SAAWK,EAAcC,KAExHH,EAAOjH,KAAKwH,MAAM5D,EAAUpB,WAAaoB,EAAUkD,EAAW,SAAWK,EAAcC,GACvFF,EAAQlH,KAAKwH,MAAM3D,EAAQrB,WAAaqB,EAAQiD,EAAW,SAAWjD,EAAQiD,EAAW,SAAWO,EAAYC,MAIpHP,IAAKA,EACLC,OAAQA,EACRC,KAAe,IAATA,EAAaA,EAAOA,EAAO,EACjCC,MAAOA,IAGfO,UAAW,SAAU7L,EAAOC,EAAK+K,GAC7B,MAAOrG,MAAKsG,MAAM,SAAUjL,EAAOC,EAAK+K,MAG5CpD,EAAe+C,EAAUnG,QACzBsH,WAAY,WAAA,GAMC7F,GALLX,EAAaX,KAAKW,WAClBnF,EAAawE,KAAK3E,MAAME,MACxBG,EAAWsE,KAAK1E,IAAIC,MACpBjD,EAAS,EACTkJ,EAAQhG,IAAeE,EAAW,cAAgB,aACtD,KAAS4F,EAAY9F,EAAY8F,GAAa5F,EAAU4F,IACpDhJ,GAAUqI,EAAWmB,GAAGR,GAAWE,EAEvC,OAAOlJ,MAGXsI,EAAiBjJ,EAAMiI,MAAMC,QAC7BC,KAAM,SAAUQ,EAAWC,EAAS6G,EAAY/F,GAC5CrB,KAAKqH,UACLrH,KAAKsH,WACLtH,KAAKuH,OAAS5P,EAAMqH,KAAK2E,UAAUrD,GACnCN,KAAKwH,KAAO7P,EAAMqH,KAAK2E,UAAUpD,GACjCP,KAAKyH,YAAcL,EACnBpH,KAAK0H,iBAAmBrG,GAE5Bc,QAAS,WACL,IAAK,GAAIb,GAAY,EAAGA,EAAYtB,KAAKqH,OAAO9O,OAAQ+I,IACpDtB,KAAKqH,OAAO/F,GAAWa,WAG/B6B,aAAc,SAAUhF,GACpB,MAAOgB,MAAKuH,QAAUvI,GAAQA,EAAOgB,KAAKwH,MAE9CtD,WAAY,SAAUlF,EAAM+D,GACxB,GAAIzH,GAAMyH,EAAW/D,EAAOgB,KAAKwH,KAAOxI,GAAQgB,KAAKwH,IACrD,OAAOxH,MAAKuH,QAAUvI,GAAQ1D,GAElCqK,gBAAiB,SAAU3G,GAAV,GAKJsC,GACDC,EALJmE,EAAO1G,CAIX,KAHmB,gBAAR0G,KACPA,EAAO/N,EAAMqH,KAAK2E,UAAU3E,IAEvBsC,EAAY,EAAGA,EAAYtB,KAAKqH,OAAO9O,OAAQ+I,IAEpD,GADIC,EAAOvB,KAAKqH,OAAO/F,GACnBC,EAAKyC,aAAa0B,GAClB,MAAOnE,EAGf,OAAO,OAEXqE,cAAe,SAAU5G,EAAM2I,GAAhB,GAQFrG,GACDC,EARJmE,EAAO1G,CAIX,IAHmB,gBAAR0G,KACPA,EAAO/N,EAAMqH,KAAK2E,UAAU3E,IAE5B2I,EACA,MAAO3H,MAAK2F,gBAAgB3G,GAAM,EAEtC,KAASsC,EAAY,EAAGA,EAAYtB,KAAKqH,OAAO9O,OAAQ+I,IAEpD,GADIC,EAAOvB,KAAKqH,OAAO/F,GACnBC,EAAK2C,WAAWwB,GAChB,MAAOnE,EAGf,OAAO,OAEXM,MAAO,WACH,MAAO7B,MAAKqH,OAAO9O,QAEvB0D,OAAQ,WACJ,MAAO+D,MAAKsH,SAEhBM,YAAa,SAAUZ,EAAS3L,EAAOC,EAAKuM,GACxC,GAAItG,GAAO,GAAIuG,GAASd,EAAS3L,EAAOC,EAAK0E,KAAKyH,YAAazH,KAAK0H,iBAAkB1H,KAAKqH,OAAO9O,OAAQsP,EAC1G7H,MAAKqH,OAAO7O,KAAK+I,IAErBwG,WAAY,SAAUf,EAAS3L,EAAOC,EAAK0M,GACvC,GAAIzG,GAAO,GAAI0G,GAAQjB,EAAS3L,EAAOC,EAAK0E,KAAKyH,YAAazH,KAAK0H,iBAAkB1H,KAAKqH,OAAO9O,OAAQyP,EACzGhI,MAAKqH,OAAO7O,KAAK+I,IAErBmB,MAAO,WACH,MAAO1C,MAAKqH,OAAO,IAEvB1E,KAAM,WACF,MAAO3C,MAAKqH,OAAOrH,KAAKqH,OAAO9O,OAAS,IAE5CuJ,GAAI,SAAUvG,GACV,MAAOyE,MAAKqH,OAAO9L,MAGvB2M,EAAOvQ,EAAMiI,MAAMC,QACnBC,KAAM,SAAUkH,EAAS3L,EAAOC,EAAK8L,EAAY/F,EAAiB9F,GAC9DyE,KAAKgH,QAAUA,EACfhH,KAAKmI,YAAcnB,EAAQmB,YAC3BnI,KAAKoI,aAAepB,EAAQoB,aAC5BpI,KAAK+B,YAAciF,EAAQjF,YAC3B/B,KAAKgC,aAAegF,EAAQhF,aAC5BhC,KAAKkC,UAAY8E,EAAQ9E,UACzBlC,KAAKiC,WAAa+E,EAAQ/E,WAC1BjC,KAAK3E,MAAQA,EACb2E,KAAK1E,IAAMA,EACX0E,KAAKgH,QAAUA,EACfhH,KAAKoH,WAAaA,EAClBpH,KAAKqB,gBAAkBA,EACvBrB,KAAKzE,MAAQA,EACbyE,KAAKuE,WAAY,GAErBpC,QAAS,WACL,GAAI6E,GAAUhH,KAAKgH,OACnBhH,MAAKmI,YAAcnB,EAAQmB,YAC3BnI,KAAKoI,aAAepB,EAAQoB,aAC5BpI,KAAK+B,YAAciF,EAAQjF,YAC3B/B,KAAKgC,aAAegF,EAAQhF,aAC5BhC,KAAKkC,UAAY8E,EAAQ9E,UACzBlC,KAAKiC,WAAa+E,EAAQ/E,YAE9B3B,UAAW,WACP,MAAO3I,GAAM0Q,SAASC,YAAYtI,KAAK3E,QAE3CkF,QAAS,WACL,MAAO5I,GAAM0Q,SAASC,YAAYtI,KAAK1E,MAE3C0I,aAAc,SAAUhF,GACpB,MAAOgB,MAAK3E,OAAS2D,GAAQA,EAAOgB,KAAK1E,KAE7C4I,WAAY,SAAUlF,GAClB,MAAOgB,MAAK3E,MAAQ2D,GAAQA,GAAQgB,KAAK1E,KAE7CsL,YAAa,WACT,MAAO5G,MAAK3E,OAEhByL,UAAW,WACP,MAAO9G,MAAK1E,OAGhBwM,EAAWI,EAAKrI,QAChBC,KAAM,SAAUkH,EAAS3L,EAAOC,EAAK8L,EAAY/F,EAAiB9F,EAAOsM,GACrEK,EAAKK,GAAGzI,KAAK0I,MAAMxI,KAAMyI,WACzBzI,KAAK6H,eAAeA,GAExBa,QAAS,SAAUC,EAAKC,GACpB,MAAID,GACO3I,KAAKiC,WAAa2G,EAElB5I,KAAKiC,WAAa2G,GAGjC5E,aAAc,SAAUhF,GACpB,MAAOgB,MAAK3E,OAAS2D,GAAQA,EAAOgB,KAAK1E,KAE7C4I,WAAY,SAAUlF,GAClB,MAAOgB,MAAK3E,MAAQ2D,GAAQA,GAAQgB,KAAK1E,KAE7CsL,YAAa,SAAU5F,EAAGC,EAAGoF,GAAhB,GAILuC,GACAC,EACAC,EACApD,EAEI3F,CARR,IAAIsG,EACA,MAAOrG,MAAK3E,KAMhB,IAJIuN,EAAS9Q,EAAEkI,KAAKgH,SAAS4B,SACzBC,EAAW7I,KAAK1E,IAAM0E,KAAK3E,MAG3B2E,KAAK6H,cAIL,GAHI9H,EAAQpI,EAAMe,QAAQqH,MAAMC,KAAKgH,SACrC8B,EAAa9H,EAAI4H,EAAOlC,KACxBhB,EAAOjG,KAAKsJ,MAAMF,GAAYC,EAAa9I,KAAK+B,cAC5ChC,EACA,MAAOC,MAAK3E,MAAQwN,EAAWnD,MAGnCoD,GAAa7H,EAAI2H,EAAOpC,IACxBd,EAAOjG,KAAKsJ,MAAMF,GAAYC,EAAa9I,KAAKgC,cAEpD,OAAOhC,MAAK3E,MAAQqK,GAExBoB,UAAW,SAAU9F,EAAGC,EAAGoF,GAAhB,GAIHuC,GACAC,EACAC,EACApD,EAEI3F,CARR,IAAIsG,EACA,MAAOrG,MAAK1E,GAMhB,IAJIsN,EAAS9Q,EAAEkI,KAAKgH,SAAS4B,SACzBC,EAAW7I,KAAK1E,IAAM0E,KAAK3E,MAG3B2E,KAAK6H,cAIL,GAHI9H,EAAQpI,EAAMe,QAAQqH,MAAMC,KAAKgH,SACrC8B,EAAa9H,EAAI4H,EAAOlC,KACxBhB,EAAOjG,KAAKsJ,MAAMF,GAAYC,EAAa9I,KAAK+B,cAC5ChC,EACA,MAAOC,MAAK3E,MAAQwN,EAAWnD,MAGnCoD,GAAa7H,EAAI2H,EAAOpC,IACxBd,EAAOjG,KAAKsJ,MAAMF,GAAYC,EAAa9I,KAAKgC,cAEpD,OAAOhC,MAAK3E,MAAQqK,KAGxBuC,EAAUC,EAAKrI,QACfC,KAAM,SAAUkH,EAAS3L,EAAOC,EAAK8L,EAAY/F,EAAiB9F,EAAOyM,GAIrE,GAHAE,EAAKK,GAAGzI,KAAK0I,MAAMxI,KAAMyI,WACzBzI,KAAKgI,WAAaA,EAClBhI,KAAKuE,WAAY,EACbvE,KAAKgH,QAAQgC,SAASzQ,OAAQ,CAC9B,GAAI0Q,GAAajJ,KAAKgH,QAAQgC,SAAS,EACvChJ,MAAKkJ,iBAAmBD,EAAWjH,aACnChC,KAAKmJ,cAAgBF,EAAW/G,cAEhClC,MAAKkJ,iBAAmB,EACxBlJ,KAAKmJ,cAAgB,GAG7B7I,UAAW,WACP,GAAItB,GAAO,GAAIoK,MAAKpJ,KAAK3E,MACzB,OAAO1D,GAAM0Q,SAASG,MAAMxJ,EAAM,YAEtCuB,QAAS,WACL,GAAIvB,GAAO,GAAIoK,MAAKpJ,KAAK1E,IACzB,OAAO3D,GAAM0Q,SAASG,MAAMxJ,EAAM,YAEtCgF,aAAc,SAAUhF,GACpB,MAAOgB,MAAK3E,OAAS2D,GAAQA,EAAOgB,KAAK1E,KAE7C4I,WAAY,SAAUlF,GAClB,MAAOgB,MAAK3E,MAAQ2D,GAAQA,GAAQgB,KAAK1E,MAQjD3D,GAAMC,GAAGyR,cAAgBpK,EAAOY,QAC5BC,KAAM,SAAUkH,EAASf,GACrBhH,EAAOsJ,GAAGzI,KAAKwJ,KAAKtJ,KAAMgH,EAASf,GACnCjG,KAAKuJ,oBACLvJ,KAAKwJ,WAAavO,IAClB+E,KAAKI,OAASzI,EAAMe,QAAQqH,MAAMiH,GAClChH,KAAKyJ,YAAc3R,IACnBkI,KAAK0J,UAAY5R,IACjBkI,KAAK2J,QAAUhS,EAAMiS,OACrB5J,KAAK6J,sBACL7J,KAAK8J,mBAETC,eAAgB,WACZ,MAAO/J,MAAKO,WAEhBgJ,kBAAmB,WACf,GAAItD,GAAUjG,KAAKiG,OACfA,GAAQ5D,WACR4D,EAAQ5D,UAAU2H,gBAAgB,GAElC/D,EAAQ3D,SACR2D,EAAQ3D,QAAQ0H,gBAAgB,GAEhC/D,EAAQgE,cACRhE,EAAQgE,aAAaD,gBAAgB,GAErC/D,EAAQiE,YACRjE,EAAQiE,WAAWF,gBAAgB,IAG3CG,UAAW,WACP,GAAIlE,GAAUjG,KAAKiG,OACnB,OAAOA,GAAQmE,UAAW,GAAQzS,EAAMe,QAAQ2R,UAA+B,UAAnBpE,EAAQmE,QAAyC,WAAnBnE,EAAQmE,QAEtGE,iBAAkB,WACd,GAAIC,GAAe,GAAI5K,GAAaK,KAAKwK,OAAOjS,OAAQyH,KAAKI,OAE7D,OADAJ,MAAKwK,OAAOhS,KAAK+R,GACVA,GAEXE,aAAc,WACV,MAAO9S,GAAM+S,OAAO1K,KAAKiG,QAAQ0E,mBAAoB3K,KAAKM,YAAaN,KAAKO,YAEhFqK,kBAAmB,WACf,MAAOjT,GAAM+S,OAAO1K,KAAKiG,QAAQ4E,wBAAyB7K,KAAKM,YAAaN,KAAKO,YAErFuK,mBAAoB,WAChB,MAAOnT,GAAM+S,OAAO1K,KAAKiG,QAAQ8E,0BAA4B/K,KAAKiG,QAAQ4E,wBAAyB7K,KAAKM,YAAaN,KAAKO,YAE9HyK,aAAc,SAAUC,EAAWC,GAArB,GACNC,GAASD,EAAW,gBAAkB,gBACtC3J,EAAOvB,KAAKmL,GAAQF,EAAU5P,MAAO4P,EAAU7D,WAAY6D,EAAUlI,SAOzE,OANIxB,KACA0J,EAAU7D,YAAc8D,KAAgB,GAExClL,KAAKoL,qBAAuB7J,IAC5B0J,EAAU7D,WAAa8D,EAAWlL,KAAKwK,OAAOjS,OAAS,EAAI,GAExDgJ,GAEX8J,YAAa,SAAUJ,EAAW1J,EAAM2J,GAA3B,GAELxK,GAAanF,EAUT+F,EAXJgK,EAAQtL,KAAKwK,OAAOS,EAAU7D,WAElC,IAAI8D,GAGA,GAFAxK,EAAc4K,EAAMnH,iBAAgB,GACpC5I,EAAQ+P,EAAMxK,yBAA2BS,EAAKhG,MAAQ,EAAIgG,EAAKF,gBAAkB,EAC7E9F,GAAS,EACT,MAAOmF,GAAYnF,GAAO8L,OAAO3G,EAAYnF,GAAO8L,OAAO9O,OAAS,OAMxE,IAHAmI,EAAc4K,EAAMnH,gBAAgBmH,EAAMxK,0BAC1CvF,EAAQ+P,EAAMxK,yBAA2B,EAAIS,EAAKF,gBAAkB,EAChEC,EAAYgK,EAAMxK,yBAA2BS,EAAKF,gBAAkB,EAAI,EACxEX,EAAYnF,IAAUmF,EAAYnF,GAAO8L,OAAO/F,GAChD,MAAOZ,GAAYnF,GAAO8L,OAAO/F,IAI7CiK,yBAA0B,WACtB,MAAO,OAEXC,kBAAmB,WACf,OAAO,GAEXC,WAAY,SAAUC,EAAUC,GAC5B,SAAKD,GAAaC,GAAW3L,KAAKiG,QAAQ2F,KAAQ5L,KAAKiG,QAAQ4F,OAGxD9M,EAAQ2M,IAAa3M,EAAQiB,KAAKiG,QAAQ2F,MAAQ7M,EAAQ4M,IAAW5M,EAAQiB,KAAKiG,QAAQ4F,OAErGC,iBAAkB,SAAUb,EAAW1H,EAAQwI,EAAU1H,GAAvC,GASN9C,GAMAyK,EAaJ5H,EA3BA+G,EAAS9G,EAAU,WAAa,YAChC4H,GACA5I,UAAWE,EAAO,GAAGlI,MACrBiI,QAASC,EAAOA,EAAOhL,OAAS,GAAG+C,KAEnCgQ,EAAQtL,KAAKwK,OAAOS,EAAU7D,YAC9B8E,EAAalM,KAAKmM,sBA6BtB,OA5BKJ,KACGxK,EAAOvB,KAAKoM,8BAA8BnB,EAAW1H,EAAQc,GAC7D9C,IACA0K,EAAgB5I,UAAY4I,EAAgB3I,QAAU/B,IAG1DvB,KAAKoL,qBAAuBW,GACxBC,EAAWhM,KAAKgL,aAAaC,EAAW5G,GACvC2H,EAGDC,EAAgB5I,UAAY4I,EAAgB3I,QAAU0I,EAFtDC,EAAkBjM,KAAKqM,wBAAwBf,EAAOH,EAAQc,KAKlEA,EAAgB5I,UAAYiI,EAAMH,GAAQc,EAAgB5I,WAC1D4I,EAAgB3I,QAAUgI,EAAMH,GAAQc,EAAgB3I,SACnDyI,GAAaG,GAAgBD,EAAgB5I,WAAc4I,EAAgB3I,UAC5E2I,EAAgB5I,UAAY4I,EAAgB3I,QAAUtD,KAAKgL,aAAaC,EAAW5G,KAIrF4H,EAAgB5I,WAAc4I,EAAgB3I,SAAatD,KAAKoL,qBAClEhH,EAAiBpE,KAAKsM,gBAAgBrB,EAAW1H,EAAQc,GACzDD,EAAiBpE,KAAKuL,yBAAyBN,EAAW7G,EAAgB2H,EAAU1H,GAChFD,IACA6H,EAAgB5I,UAAY4I,EAAgB3I,QAAUc,IAGvD6H,GAEXI,wBAAyB,SAAUf,EAAOH,EAAQc,GAK9C,MAJKjM,MAAKmM,yBACNF,EAAgB5I,UAAYiI,EAAMH,GAAQc,EAAgB5I,WAC1D4I,EAAgB3I,QAAUgI,EAAMH,GAAQc,EAAgB3I,UAErD2I,GAEXM,eAAgB,SAAUtB,EAAW1H,EAAQwI,EAAU1H,GAAvC,GAER9C,GAWA4J,EAZAG,EAAQtL,KAAKwK,OAAOS,EAAU7D,YAE9BoF,GACAnJ,UAAWE,EAAO,GAAGlI,MACrBiI,QAASC,EAAOA,EAAOhL,OAAS,GAAG+C,IAiBvC,OAfKyQ,KACDxK,EAAOvB,KAAKyM,4BAA4BxB,EAAW1H,EAAQc,GACvD9C,IACAiL,EAAcnJ,UAAYmJ,EAAclJ,QAAU/B,IAGtD4J,EAAS9G,EAAU,SAAW,WAClCmI,EAAgBxM,KAAK0M,sBAAsBpB,EAAOH,EAAQqB,EAAeT,GACpEA,IAAY/L,KAAKmM,wBAA4BK,EAAcnJ,WAAcmJ,EAAclJ,UAEpFkJ,EAAcnJ,UAAYmJ,EAAclJ,QADxCtD,KAAKoL,mBAC6CpL,KAAKqL,YAAYJ,EAAW1J,EAAM8C,GAElCrE,KAAKgL,aAAaC,EAAW5G,IAGhFmI,GAEXE,sBAAuB,SAAUpB,EAAOH,EAAQqB,EAAeT,GAG3D,MAFAS,GAAcnJ,UAAYiI,EAAMH,GAAQqB,EAAcnJ,UAAW0I,GACjES,EAAclJ,QAAUgI,EAAMH,GAAQqB,EAAclJ,QAASyI,GACtDS,GAEXJ,8BAA+B,WAC3B,MAAO,OAEXK,4BAA6B,SAAUxB,EAAW1H,EAAQc,GACtD,GAAI9C,EAMJ,OAJIA,GADA8C,EACOd,EAAO,GAAGlI,MAEVkI,EAAOA,EAAOhL,OAAS,GAAG+C,KAIzCgR,gBAAiB,WACb,MAAO,OAEXK,QAAS,WAAA,GAIGC,GAHJ/H,EAAO7E,KACPiG,EAAUpB,EAAKoB,OACfpB,GAAKsF,cACDyC,EAAO,4CACXA,GAAQ,8EACRA,GAAQ3G,EAAQ4G,SAASC,MAAQ,cACjCF,GAAQ,SACR/H,EAAKkI,OAASjV,EAAE8U,GAAMI,SAASnI,EAAKmC,UAEpCnC,EAAKkI,QACLlI,EAAKkI,OAAOE,GAAG,QAAU5N,EAAI,qBAAsB,SAAU6N,GAAV,GAE3C7E,GACA8E,EACAC,EACApO,EAEIqO,CANRH,GAAEI,iBACEjF,EAAWxD,EAAKoB,QAAQoC,SACxB8E,EAAS,QACTC,EAAc,GAAIhE,MAElBf,GACIgF,EAAiB1V,EAAM0Q,SAASO,OAAOwE,EAAa/E,GACxDrJ,EAAOrH,EAAM0Q,SAASkF,QAAQH,EAAaA,EAAYI,oBAAqBH,IAE5ErO,EAAOoO,EAEXvI,EAAK4I,QAAQ,YACT9P,KAAMkH,EAAKjI,MAAQqJ,EAAQrJ,KAC3BuQ,OAAQA,EACRnO,KAAMA,OAKtB0O,mBAAoB,SAAUzC,GAAV,GAEZ1J,GADA+J,EAAQtL,KAAKwK,OAAO,EAEnBxK,MAAKwC,QAAQyI,GAMTK,EAAMxK,yBAECwK,EAAMzK,4BACdoK,EAAUlI,UAAW,GAFrBkI,EAAUlI,UAAW,GANzBxB,EAAO+J,EAAM9G,YACbyG,EAAUlI,SAAWxB,EAAKgD,UAC1B0G,EAAU5P,MAAQkG,EAAKjB,YACvB2K,EAAU3P,IAAMiG,EAAKhB,WAQpBP,KAAKwK,OAAOS,EAAU7D,cACvB6D,EAAU7D,WAAa,IAG/BuG,KAAM,SAAU1C,EAAWhT,EAAK2V,GAA1B,GAOErK,GACAF,EAAWC,EAASe,EAASwJ,EAsBrBC,EA7BRC,GAAU,EACVzC,EAAQtL,KAAKwK,OAAOS,EAAU7D,YAC9B4G,EAAiBhO,KAAKoL,oBAAsBpL,KAAKmM,sBAMrD,IALKb,EAAMzK,4BACPoK,EAAUlI,UAAW,GAErBQ,EAAS+H,EAAM/H,OAAO0H,EAAU5P,MAAO4P,EAAU3P,IAAK2P,EAAUlI,UAAU,GAE1E9K,IAAQmH,EAAK6O,MAAQhW,IAAQmH,EAAK8O,IAKlC,GAJAH,GAAU,EACV1J,EAAUpM,IAAQmH,EAAK8O,GACvBlO,KAAKmO,iBAAiBlD,EAAW1H,EAAQqK,EAAOvJ,GAAS,GACzDwJ,EAAQ7N,KAAKuM,eAAetB,EAAW1H,EAAQqK,EAAOvJ,IACjDwJ,EAAMxK,YAAcuK,GAAS5N,KAAKwL,kBAAkBP,EAAW5G,GAAU2J,GAC1E,MAAOD,OAER,KAAI9V,IAAQmH,EAAKgP,MAAQnW,IAAQmH,EAAKiP,SACzCN,GAAU,EACV1J,EAAUpM,IAAQmH,EAAKgP,KACvBpO,KAAKmO,iBAAiBlD,EAAW1H,EAAQqK,EAAOvJ,GAAS,GACzDwJ,EAAQ7N,KAAK8L,iBAAiBb,EAAW1H,EAAQqK,EAAOvJ,IACnDwJ,EAAMxK,YAAcuK,GAAS5N,KAAKwL,kBAAkBP,EAAW5G,EAAS2J,IACzE,MAAOD,EAoBf,OAjBIA,KACA1K,EAAYwK,EAAMxK,UAClBC,EAAUuK,EAAMvK,QACZsK,GACIE,EAAW7C,EAAU6C,SACrBA,GAAYzK,EACZ4H,EAAU5P,MAAQgI,EAAU/C,aACpBwN,GAAYxK,IACpB2H,EAAU3P,IAAMgI,EAAQ/C,YAErB8C,GAAaC,IACpB2H,EAAUlI,SAAWM,EAAUkB,UAC/B0G,EAAU5P,MAAQgI,EAAU/C,YAC5B2K,EAAU3P,IAAMgI,EAAQ/C,WAE5B0K,EAAUhP,WAEP8R,GAEXO,mBAAoB,SAAUhD,EAAO/J,EAAMgN,EAAgBC,GAAvC,GAEZC,GAAOzS,EAKH0S,EACKC,EAPT1S,EAASqP,EAAMsD,sBAEftK,EAAMkK,KAAY,EAClBjW,EAAS0D,EAAO1D,OAChBF,EAAMmW,EAAOjW,EAAS,EAAI,CAC9B,IAAIgW,EAAehW,OAEf,IADImW,EAAeH,EAAeA,EAAehW,OAAS,GACjDoW,EAAI,EAAGA,EAAI1S,EAAO1D,OAAQoW,IAC3B1S,EAAO0S,GAAGE,MAAQH,IAClBrW,EAAMsW,EAAIrK,EAItB,MAAOjM,EAAME,GAAUF,MAAU,CAE7B,GADA2D,EAAQC,EAAO5D,KACVmW,GAAQxS,EAAMX,MAAMiF,aAAeiB,EAAKjB,aAAekO,GAAQxS,EAAMX,MAAMiF,aAAeiB,EAAKjB,cAC5FtE,GAASlE,EAAEgX,QAAQ9S,EAAM6S,IAAKN,QAAwB,CACtDE,IAAUzS,CACV,OAGR3D,GAAOiM,EAEX,MAAOtI,IAEX+S,YAAa,SAAU9D,EAAWuD,GAArB,GAOLxS,GAEIgT,EACAC,EACAC,EAEIC,EAMA9W,EAlBR+O,EAAa6D,EAAU7D,WACvBkE,EAAQtL,KAAKwK,OAAOpD,GACpB7F,EAAO+J,EAAM/H,OAAO0H,EAAU5P,MAAO4P,EAAU3P,IAAK2P,EAAUlI,UAAU,GAAO,GAAG1H,MAClF9C,EAASyH,KAAKwK,OAAOjS,OACrB+L,EAAMkK,KAAY,EAClBvS,EAASgP,EAAUhP,MAEvB,IAAI+D,KAAKoL,mBAIL,GAHI4D,EAAYhP,KAAKoP,gBACjBH,EAAkBjP,KAAKqP,iBAAiBL,GACxCE,EAAelP,KAAKsP,iBAAiBL,GACnB,IAAlBhT,EAAO1D,OACH4W,EAAanP,KAAKuP,yBAAyBhO,EAAM2N,EAAc9H,GAC/DoH,GACAW,IAEJnT,EAAQkT,EAAaC,OAGrB,KADI9W,EAAM2H,KAAKwP,aAAavT,EAAQiT,GAC7B7W,EAAM6W,EAAa3W,QAAUF,OAC5B4D,EAAO1D,OAAS,IAChBgJ,EAAOvB,KAAKyP,iBAAiBlO,EAAM2N,EAAclT,EAAO3D,EAAKiM,EAAKkK,IAEjEjN,IAJqC,CAO1C,KAAKiN,GAAQU,EAAa7W,GAAKgD,MAAMiF,aAAeiB,EAAKjB,aAAekO,GAAQU,EAAa7W,GAAKgD,MAAMiF,aAAeiB,EAAKjB,cACpHrE,EAAO,IAAMiT,EAAa7W,GAAKwW,IAAK,CACpC7S,EAAQkT,EAAa7W,EACrB,OAGRA,GAAOiM,MAIf,MAAO8C,EAAa7O,GAAU6O,OAC1BpL,EAAQgE,KAAKsO,mBAAmBhD,EAAO/J,EAAMtF,EAAQuS,GACrDpH,GAAc9C,EACdgH,EAAQtL,KAAKwK,OAAOpD,GACfkE,IAAStP,IAGdC,KAEIsF,EADAiN,EACOlD,EAAM7G,WAEN6G,EAAM9G,WAAU,EAWnC,OAPIxI,KACAiP,EAAUhP,QAAUD,EAAM6S,KAC1B5D,EAAU5P,MAAQW,EAAMX,MAAMiF,YAC9B2K,EAAU3P,IAAMU,EAAMV,IAAIiF,UAC1B0K,EAAUlI,SAAW/G,EAAMX,MAAMkJ,UACjC0G,EAAU7D,WAAapL,EAAMX,MAAM+L,cAE9BpL,GAEb0T,QAAS,SAAUC,GACf,MAAkB9L,UAAd8L,EAMO3P,KAAK4P,UALZ5P,KAAK4P,SAAWD,OACZ3P,KAAKhF,QAAQ6U,IAAIF,IACjB3P,KAAK8P,UAAUH,EAAW3P,KAAKhF,QAAQ,OAMnD+U,OAAQ,SAAU9E,GACdjL,KAAKgQ,iBACAhQ,KAAKiQ,cAAchF,IACpBjL,KAAKkQ,aAAajF,IAG1BsE,yBAA0B,SAAUhO,EAAM2N,EAAc9H,GAA9B,GAGbuH,GACDwB,EAHJC,EAAY,EACZC,EAAgB1Y,EAAMqH,KAAKD,QAAQwC,EAAKjB,YAC5C,KAASqO,EAAI,EAAGA,EAAIO,EAAa3W,OAAQoW,IAErC,GADIwB,EAAiBxY,EAAMqH,KAAKD,QAAQmQ,EAAaP,GAAGtT,MAAMiF,aAC1D+P,EAAgBF,EAChBC,QAGJ,IAAIC,EAAcC,YAAcH,EAAeG,WAAalJ,EAAa8H,EAAaP,GAAGtT,MAAM+L,WAC3FgJ,QADJ,CAIA,KAAIC,EAAcC,YAAcH,EAAeG,WAAalJ,GAAc8H,EAAaP,GAAGtT,MAAM+L,YAAc7F,EAAKjB,YAAc4O,EAAaP,GAAGtT,MAAMiF,aAIvJ,KAHI8P,KAKR,MAAOA,IAEXX,iBAAkB,SAAUlO,EAAM2N,EAAclT,EAAO3D,EAAKiM,EAAKkK,GAA/C,GAENpH,GACAkE,CAUR,OAZI4D,GAAa7W,EAAMiM,IAAQ4K,EAAa7W,GAAKgD,MAAM+L,aAAe8H,EAAa7W,EAAMiM,GAAKjJ,MAAM+L,aAC5FA,EAAa8H,EAAa7W,EAAMiM,GAAKjJ,MAAM+L,WAC3CkE,EAAQtL,KAAKwK,OAAOpD,GACnBkE,IAAStP,IACVuF,EAAO,MAGPA,EADAiN,EACOlD,EAAM7G,WAEN6G,EAAM9G,WAAU,IAGxBjD,GAEXiO,aAAc,SAAUvT,EAAQiT,GAC5B,GAAIqB,GAAqB,CAOzB,OANAzY,GAAEkF,KAAKkS,EAAc,WACjB,MAAIlP,MAAK6O,MAAQ5S,EAAO,QAGxBsU,OAEGA,GAEXnB,cAAe,WAAA,GAGF/W,GAFL2W,KACAxE,EAASxK,KAAKwK,MAClB,KAASnS,EAAM,EAAGA,EAAMmS,EAAOjS,OAAQF,IAC/BmS,EAAOnS,GAAKuW,oBACZI,EAAYA,EAAUwB,OAAOhG,EAAOnS,GAAKuW,mBAGjD,OAAOI,IAEXK,iBAAkB,SAAUL,GAAV,GAELL,GACD8B,EACKlU,EAHT0S,IACJ,KAASN,EAAI,EAAGA,EAAIK,EAAUzW,OAAQoW,IAAK,CAEvC,IADI8B,GAAS,EACJlU,EAAI,EAAGA,EAAI0S,EAAgB1W,OAAQgE,IACxC,GAAIyS,EAAUL,GAAGE,MAAQI,EAAgB1S,GAAGsS,IAAK,CAC7C4B,GAAS,CACT,OAGHA,GACDxB,EAAgBzW,KAAKwW,EAAUL,IAGvC,MAAOM,IAEXK,iBAAkB,SAAUL,GACxB,MAAOA,GAAgByB,KAAK,SAAUhO,EAAOiO,GAAjB,GACpBC,GAAiBlO,EAAMrH,MAAMiF,YAC7BuQ,EAAkBF,EAAOtV,MAAMiF,YAC/BhI,EAASX,EAAMqH,KAAKD,QAAQ6R,GAAkBjZ,EAAMqH,KAAKD,QAAQ8R,EAkBrE,OAjBe,KAAXvY,IACAA,EAASoK,EAAMrH,MAAM+L,WAAauJ,EAAOtV,MAAM+L,YAEpC,IAAX9O,IACAA,EAASsY,EAAeN,UAAYO,EAAgBP,WAEzC,IAAXhY,IACIoK,EAAMrH,MAAMkJ,YAAcoM,EAAOtV,MAAMkJ,YACvCjM,OAECoK,EAAMrH,MAAMkJ,WAAaoM,EAAOtV,MAAMkJ,YACvCjM,EAAS,IAGF,IAAXA,IACAA,EAASR,EAAE4K,EAAMsE,SAASzL,QAAUzD,EAAE6Y,EAAO3J,SAASzL,SAEnDjD,KAGf4X,aAAc,SAAUjF,GAAV,GAON1H,GACAyD,EACAzF,EACK5F,EACDuH,EACAvC,EACKW,EAZTyB,EAAWkI,EAAUlI,SACrBuI,EAAQtL,KAAKwK,OAAOS,EAAU7D,WAQlC,KAPKkE,EAAMzK,4BACPkC,GAAW,GAEf/C,KAAK8J,kBACDvG,EAAS+H,EAAM/H,OAAO0H,EAAU5P,MAAO4P,EAAU3P,IAAKyH,GAAU,GAG3DpH,EAAa,EAAGA,EAAa4H,EAAOhL,OAAQoD,IAGjD,IAFIuH,EAAQK,EAAO5H,GACfgF,EAAauC,EAAMvC,WACdW,EAAY4B,EAAM7H,MAAME,MAAO+F,GAAa4B,EAAM5H,IAAIC,MAAO+F,IAClEC,EAAOZ,EAAWmB,GAAGR,GACrB0F,EAAUzF,EAAKyF,QACfA,EAAQ8J,aAAa,iBAAiB,GACtCrS,EAAiBuI,GACjBhH,KAAK8J,eAAetR,MAChB6C,MAAOkG,EAAKjB,YACZhF,IAAKiG,EAAKhB,UACVyG,QAASA,GAIjBiE,GAAU6C,WACV9G,EAAUzD,EAAO,GAAGlI,MAAM2L,SAE9BhH,KAAK0P,QAAQ1I,IAEjBiJ,cAAe,SAAUhF,GAAV,GAIP5S,GAAK0Y,EAILzY,EAPAmW,GAAQ,EACRxS,EAASgP,EAAUhP,OACnB+U,EAAchR,KAAKoP,gBACF7W,EAASyY,EAAYzY,MAC1C,KAAK0D,EAAO,KAAO+U,EAAY,GAC3B,MAAOvC,EAIX,KAFInW,EAASR,IACbmT,EAAUhP,UACL5D,EAAM,EAAGA,EAAME,EAAQF,IACpBP,EAAEgX,QAAQkC,EAAY3Y,GAAKwW,IAAK5S,QAChC8U,EAAaC,EAAY3Y,GACzBC,EAASA,EAAO2Y,IAAIF,EAAW/J,SAC3BiE,EAAUhP,OAAOlB,QAAQgW,EAAWlC,WACpC5D,EAAUhP,OAAOzD,KAAKuY,EAAWlC,KAU7C,OANIvW,GAAO,KACPA,EAAO4Y,SAAS,oBAAoBC,KAAK,iBAAiB,GAC1DnR,KAAK0P,QAAQpX,EAAOqK,OAAO,IAC3B3C,KAAK8J,kBACL2E,GAAQ,GAELA,GAEXjM,QAAS,SAAUyD,GAAV,GACD3F,GAAYN,KAAKM,YACjBC,EAAU5I,EAAMqH,KAAKoS,QAAQpR,KAAKO,UAAW,GAC7ClF,EAAQ4K,EAAQ5K,MAChBC,EAAM2K,EAAQ3K,GAClB,OAAOgF,IAAajF,GAASA,EAAQkF,GAAWD,EAAYhF,GAAOA,GAAOiF,GAE9E8Q,eAAgB,SAAU5T,EAAUR,GAIhC,MAHIQ,GAAS6T,iBACTrU,EAAOtF,EAAMkG,OAAOJ,EAASW,gBAAgBnB,IAE1CA,GAEXsU,gBAAiB,SAAUhQ,GAAV,GAILiQ,GACKnZ,EACDoF,EACAU,EAIAsT,EAVRpU,EAAY2C,KAAK0R,iBACjBpZ,IACJ,IAAI+E,EAAU9E,OAEV,IADIiZ,EAAgBjQ,EAAK6F,WAChB/O,EAAMgF,EAAU9E,OAAS,EAAGF,GAAO,EAAGA,IACvCoF,EAAWJ,EAAUhF,GACrB8F,EAAQ6B,KAAKqR,eAAe5T,EAAUA,EAASC,WAAWC,OAAO6T,EAAgB/T,EAASC,WAAWiU,UACrGlU,EAASsO,WACT5N,GAASA,IAETsT,EAAS9Z,EAAM8Z,OAAOhU,EAASQ,OACnCwT,EAAOnZ,EAAQ6F,GACfqT,EAAgB/R,KAAKsJ,MAAMyI,EAAgB/T,EAASC,WAAWiU,QAGvE,OAAOrZ,IAEXsZ,kBAAmB,SAAUlL,EAAMF,EAAKhF,EAAOC,GAC3C,MAAO3J,GAAE4H,GAAMmS,KACXnL,KAAMA,EACNF,IAAKA,EACLhF,MAAOA,EACPC,OAAQA,KAGhBqQ,kBAAmB,WACf9R,KAAKyJ,YAAYsI,SACjB/R,KAAKyJ,YAAc3R,KAEvBka,gBAAiB,SAAUnD,GACnBA,GACA7O,KAAK0J,UAAUuI,OAAO,cAAiBpD,EAAM,MAAOkD,SACpD/R,KAAK0J,UAAY1J,KAAK0J,UAAUuI,OAAO,eAAkBpD,EAAM,QAE/D7O,KAAK0J,UAAUqI,SACf/R,KAAK0J,UAAY5R,MAGzBgY,UAAW,SAAU9I,EAASkL,GAC1B,GAAIC,GAAgBnL,EAAQ9E,UAAWkQ,EAAmBpL,EAAQhF,aAAcqQ,EAAkBH,EAAUI,UAAWC,EAAqBL,EAAU9J,aAAcoK,EAAiBL,EAAgBC,EAAkB9Z,EAAS,CAE5NA,GADA+Z,EAAkBF,EACTA,EACFK,EAAiBH,EAAkBE,EACtCH,GAAoBG,EACXC,EAAiBD,EAEjBJ,EAGJE,EAEbH,EAAUI,UAAYha,GAE1Bma,mBAAoB,SAAUzL,GAAV,GACZ0L,GAAa1L,EAAQ6K,IAAI,SACzBc,EAAmB,GAAI9T,GAAM6T,GAAYE,SACzCC,EAAkB7L,EAAQ6K,IAAI,oBAC9BiB,EAAwB,GAAIjU,GAAMgU,GAAiBD,QACnDD,IAAoBG,GACpB9L,EAAQkK,SAAS5R,IAGzByT,WAAY,SAAUzV,EAAU0V,GAApB,GAYJC,GAXAhN,EAAUjG,KAAKiG,QAASiN,EAAWpb,EAAE+H,UAAWlI,EAAMwb,SAAUlN,EAAQmN,kBAAmBC,EAAYH,EAASG,UAAWzG,EAAO,GAAI0G,QAAchW,GAAUiW,GAC1JC,WACA3R,MAAO,EAaf,OAXa,aAATyR,GACAC,EAAMC,QAAQ,OAASD,EAAM1R,OAASvE,EACtCsP,GAAQ,cAAgB2G,EAAM1R,MAAQ,IAAMwR,EAAY,KACxDE,EAAM1R,SACU,WAATyR,IACP1G,GAAQtP,GAER2V,EAAOtb,EAAM2F,SAAS3F,EAAM+S,OAAOsI,EAASpG,GAAOsG,GACnDK,EAAM1R,MAAQ,IACdoR,EAAOnb,EAAE2b,MAAMR,EAAMM,EAAMC,UAExBP,GAEXS,eAAgB,SAAU1X,GAAV,GAKH3D,GACDoF,EACAQ,EACAyV,EAOAnW,EACKiU,EACDmC,EACAxV,EAIKX,EAODoW,EA5BZvW,KAAgB4I,EAAUjG,KAAKiG,OACnC,KAAKA,EAAQ5I,UACT,MAAOA,EAEX,KAAShF,EAAM,EAAGA,EAAM4N,EAAQ5I,UAAU9E,OAAQF,IAI9C,GAHIoF,EAAWwI,EAAQ5I,UAAUhF,GAC7B4F,EAAQR,EAASQ,MACjByV,EAAiB/b,EAAMkG,OAAOI,GAAOjC,GACnB,MAAlB0X,EAOJ,IAJKjW,EAASsO,WACV2H,GAAkBA,IAElBnW,EAAOE,EAASC,WAAWC,OACtB6T,EAAgB,EAAGA,EAAgBkC,EAAenb,OAAQiZ,IAAiB,CAMhF,IALImC,EAAgB,KAChBxV,EAAQuV,EAAelC,GACtB/T,EAAS6T,iBACVnT,EAAQxG,EAAMkG,OAAOJ,EAASW,gBAAgBD,IAEzCX,EAAY,EAAGA,EAAYD,EAAKhF,OAAQiF,IAC7C,GAAID,EAAKC,GAAWqW,IAAIpW,EAASW,iBAAmBD,EAAO,CACvDwV,EAAgBpW,EAAKC,EACrB,OAGc,OAAlBmW,IACIC,EAAgBjc,EAAMkG,OAAOJ,EAASO,gBAAgB2V,GAC1DtW,EAAU7E,MACNyF,MAAOR,EAASQ,MAChBC,MAAOT,EAASS,MAChBtB,KAAMa,EAASb,KACftD,KAAM3B,EAAMkG,OAAOJ,EAASK,eAAe6V,GAC3CxV,MAAOA,EACPJ,MAAO6V,KAKvB,MAAOvW,IAEXyW,aAAc,SAAUC,GAAV,GAKD1b,GAMLe,EAIAI,EACAa,EAEAhB,EAjBA2a,IAIJ,KAHKD,EAAOrZ,OACRqZ,EAAOrZ,SAEFrC,EAAM,EAAGA,EAAM0b,EAAOrZ,KAAKnC,OAAQF,IACxC,GAAI0b,EAAOrZ,KAAKrC,GAAKyC,OAAQ,CACzBkZ,EAAc3b,CACd,OAGJe,EAAa2a,EAAOrZ,KAAKsZ,GACzBA,GAAe,GACfD,EAAOrZ,KAAKuZ,OAAOD,EAAa,GAEhCxa,EAAewG,KAAKxG,aAAezB,EAAOgc,EAAQ,WAClD1Z,EAAY2F,KAAK3F,UAAYtC,EAAOgc,EAAQ,QAChD/T,KAAKpH,MAAQd,EAAE,UAAYW,IAAgB,0CAA4CuH,KAAKpD,KAAO,iCAC/FvD,EAAWgB,EAAUA,EAAU9B,OAAS,GAAGA,OAC/CyH,KAAKpH,MAAMsb,KAAK,eAAeC,OAAOnU,KAAKoU,YAAY5a,EAAcJ,EAAYC,IACjF2G,KAAKpH,MAAMsb,KAAK,eAAeC,OAAOnU,KAAKqU,eAAe7a,EAAca,EAAWhB,IACnF2G,KAAKgH,QAAQmN,OAAOnU,KAAKpH,OACrBoH,KAAKmK,aAAe3Q,EAAajB,OAAS,GAAkC,eAA7ByH,KAAKsU,qBAAwC3c,EAAM4c,YAAYzc,EAAEgH,SAAWS,IAC3HS,KAAKpH,MAAMsb,KAAK,2CAA2C1S,MAAqD,IAA/ChI,EAAaA,EAAajB,OAAS,GAAGA,OAAe,KACtHyH,KAAKpH,MAAMsb,KAAK,0CAA0C1S,MAAqD,IAA/ChI,EAAaA,EAAajB,OAAS,GAAGA,OAAe,MAEzHyH,KAAKwU,aAETC,cAAe,WAAA,GAEFpc,GAUDqc,EAWJC,EAgBAC,EAA8B1Z,EAQ1B2Z,EA9CJhQ,EAAO7E,KAAM8U,EAAUjQ,EAAKmC,QAAQkN,KAAK,yBAA0BzS,EAASoD,EAAKmC,QAAQd,cAAejL,EAAY+E,KAAKwJ,WAAYuL,EAAe,EAAGC,EAAmBhV,KAAKI,OAAS,OAAS,OACrM,KAAS/H,EAAM,EAAGA,EAAMyc,EAAQvc,OAAQF,IACpCoJ,GAAUvC,EAAY4V,EAAQG,GAAG5c,GAEjCwM,GAAKtL,cACLwb,EAAe7V,EAAY2F,EAAKtL,cAEhCsL,EAAK3L,aAAegG,EAAY2F,EAAK3L,aAAe6b,IACpDA,EAAe7V,EAAY2F,EAAK3L,cAEhC2L,EAAKtL,aAAesL,EAAK3L,cACrBwb,EAAkB7P,EAAKtL,YAAY2a,KAAK,kBAC5CrP,EAAK3L,YAAYgb,KAAK,MAAMzS,OAAO,SAAUlG,GACzCzD,EAAEkI,MAAMyB,OAAOiT,EAAgBO,GAAG1Z,GAAOkG,aAG7CsT,IACAtT,GAAUsT,GAEVlQ,EAAKkI,SACLtL,GAAUvC,EAAY2F,EAAKkI,SAE3B4H,EAAuB,SAAUO,GACjC,GAAIC,GAAeC,CACnB,SAAIF,EAAG,GAAGG,MAAM5T,SAGZ0T,EAAgBD,EAAGzT,SAEvByT,EAAGzT,OAAO,QACV2T,EAAYF,EAAGzT,SACX0T,GAAiBC,GACjBF,EAAGzT,OAAO,KACH,IAEXyT,EAAGzT,OAAO,KACH,KAEPmT,EAAa/P,EAAK7J,QAAQ,GAAIE,EAAkBvD,EAAMe,QAAQ4c,oBAAkC,EAAZra,EACpF0Z,EAAqB9P,EAAKmC,WAEtBnC,EAAK7J,QAAQyG,OADbA,EAAqB,EAAZxG,EACWwG,EAEY,EAAZxG,EAAgB,GAExC4J,EAAKzK,MAAMqH,OAAOmT,EAAWxM,cACzByM,EAAahQ,EAAKzK,MAAM8Z,KAAK,SAC7BW,EAAWtc,QACXsc,EAAWpT,OAAOoD,EAAK7J,QAAQkZ,KAAK,SAAS,GAAG9L,eAGpDwM,EAAW7S,YAAc6S,EAAWzM,YAAc,GAClDtD,EAAKjM,MAAMsY,SAAS,iBACpBrM,EAAKtL,YAAYsY,IAAI,WAAamD,EAAkB9Z,EAAiBqa,SAAS1Q,EAAKtL,YAAYyP,WAAW6I,IAAI,UAAYmD,EAAmB,UAAW,MAExJnQ,EAAKtL,YAAYsY,IAAI,WAAamD,EAAkB,IAEpDJ,EAAW5S,aAAe4S,EAAWxM,aAAe,GAAKwM,EAAWxM,aAAevD,EAAK7J,QAAQgO,SAAS,sBAAsBvH,SAC/HoD,EAAKjM,MAAMsY,SAAS,iBAEpBrM,EAAKjM,MAAM4c,YAAY,kBAG/BpB,YAAa,SAAU5a,EAAcJ,EAAYC,GAApC,GAILoc,GAHAhc,EAAcD,EAAaA,EAAajB,OAAS,GAAGA,MAIxD,OAHAyH,MAAK9G,YAAcA,EAAYM,EAAajB,OAAQa,EAAYC,GAChE2G,KAAKzG,YAAcA,EAAYC,EAAcC,EAAaL,GACtDqc,EAAQ,QAAUzV,KAAKmK,YAAc,0BAA8B,IAAM,IACtErS,EAAE2d,GAAOtB,OAAOnU,KAAK9G,YAAY+X,IAAIjR,KAAKzG,aAAamc,KAAK,QAAQC,WAE/EtB,eAAgB,SAAU7a,EAAca,EAAWhB,GAG/C,MAFA2G,MAAK5F,MAAQA,EAAMC,EAAWhB,EAAU2G,KAAKmK,aAC7CnK,KAAKhF,QAAUA,EAAQxB,EAAaA,EAAajB,OAAS,GAAI8B,EAAUA,EAAU9B,OAAS,IACpFT,EAAE,QAAQqc,OAAOnU,KAAK5F,MAAM6W,IAAIjR,KAAKhF,SAAS0a,KAAK,QAAQC,WAEtEnB,UAAW,WAAA,GAMHoB,GALA/Q,EAAO7E,IACXA,MAAKhF,QAAQ6a,KAAK,SAAWxW,EAAI,WAC7BwF,EAAKtL,YAAY2a,KAAK,6BAA6B4B,WAAW9V,KAAK8V,YACnEjR,EAAKzK,MAAMkY,UAAUtS,KAAKsS,aAE1BsD,EAAgBje,EAAMie,cAAc5V,KAAKhF,SACzC+a,eAAgB,SAAU7I,GACtB,MAAOpV,GAAEoV,EAAElR,MAAMga,QAAQC,QAAQ,2BAA2B1d,OAAS,KAGzEqd,GAAiBA,EAAcM,UAC/BlW,KAAKmW,eAAiBP,EACtB5V,KAAKhF,QAAU4a,EAAcQ,cAC7BR,EAAcM,QAAQL,KAAK,SAAU,SAAU3I,GAC3CrI,EAAKtL,YAAY2a,KAAK,6BAA6B4B,YAAY5I,EAAEmJ,OAAOrV,GACxE6D,EAAKzK,MAAMkY,WAAWpF,EAAEmJ,OAAOpV,OAI3C4I,oBAAqB,WAAA,GAMJxR,GAASE,EACL+d,EAAcC,EAN3Bje,KACAkS,EAASxK,KAAKiG,QAAQqF,MACtBjO,EAAY2C,KAAKiG,QAAQ5I,SAE7B,IADAmN,EAASA,GAAUA,EAAOnN,UAAYmN,EAAOnN,aACzCA,GAAamN,EAAOjS,OACpB,IAASF,EAAM,EAAGE,EAAS8E,EAAU9E,OAAQF,EAAME,EAAQF,IACvD,IAASie,EAAW,EAAGC,EAAc/L,EAAOjS,OAAQ+d,EAAWC,EAAaD,IACpEjZ,EAAUhF,GAAKuE,OAAS4N,EAAO8L,IAC/Bhe,EAAOE,KAAK6E,EAAUhF,GAKtC2H,MAAK0R,iBAAmBpZ,GAE5Bke,kBAAmB,SAAU3Z,EAAOC,EAAO1C,GACvC,MAAOuC,GAA8B,OAAQE,EAAOC,EAAO1C,IAE/Dqc,qBAAsB,SAAUpZ,EAAWP,EAAOQ,EAAUT,EAAOzC,GAC/D,MAAOgD,GAA0B,UAAWC,EAAWP,EAAOQ,EAAUT,EAAOzC,IAEnFka,kBAAmB,WACf,GAAI9J,GAASxK,KAAKiG,QAAQqF,KAC1B,OAAOd,IAAUA,EAAOnN,UAAYmN,EAAOkM,YAAc,cAE7DtL,iBAAkB,WACd,MAAOpL,MAAKiG,QAAQqF,OAAStL,KAAKiG,QAAQqF,MAAMtM,MAEpDmN,qBAAsB,WAClB,MAAOnM,MAAK0R,iBAAiBnZ,QAAuC,aAA7ByH,KAAKsU,qBAEhDqC,kBAAmB,SAAUtZ,EAAWP,EAAOQ,EAAUT,GACrD,MAAOO,GAA0B,OAAQC,EAAWP,EAAOQ,EAAUT,IAEzE+Z,mBAAoB,WAChB,MAAO,OAEX5G,eAAgB,WACZhQ,KAAKhF,QAAQkZ,KAAK,qBAAqB2C,WAAW,MAAM1F,KAAK,iBAAiB,GAAOqE,YAAY,qBAErGsB,QAAS,WACL,GAAIjS,GAAO7E,IACXf,GAAOsJ,GAAGuO,QAAQxN,KAAKtJ,MACnB6E,EAAKjM,QACLjB,EAAMmf,QAAQjS,EAAKjM,OACnBiM,EAAKjM,MAAMmZ,UAEXlN,EAAKkI,SACLpV,EAAMmf,QAAQjS,EAAKkI,QACnBlI,EAAKkI,OAAOgF,UAEhBlN,EAAK2F,OAAS,KACd3F,EAAKjM,MAAQ,KACbiM,EAAK7J,QAAU,KACf6J,EAAKzK,MAAQ,KACbyK,EAAKtL,YAAc,KACnBsL,EAAK3L,YAAc,KACnB2L,EAAKkI,OAAS,KACdlI,EAAK4E,YAAc,KACnB5E,EAAK6E,UAAY,MAErBqN,aAAc,WACV,MAAOpf,GAAMqf,aAAaC,UAAUC,UAExCC,cAAe,SAAUnY,EAAMoI,EAAY3D,GAA5B,GACP9C,GACA2K,EAAQtL,KAAKwK,OAAOpD,GACpB7F,EAAO+J,EAAM/H,OAAOvE,EAAMA,EAAMyE,GAAO,GAAO,GAAGpI,KACrD,MAAI+L,GAAc,GAGlB,MAAIpH,MAAKoL,mBACE7J,EAEPvB,KAAKmM,uBACAb,EAAMzK,2BAIPF,EAAa2K,EAAM9F,YAAY/B,EAAQlC,EAAKhG,MAAQgG,EAAKF,iBAAiB,GACnEV,EAAWgC,SAJlBhC,EAAa2K,EAAM9F,YAAY8F,EAAMxK,yBAA2B,GAAG,GAC5DH,EAAWmB,GAAGP,EAAKhG,QAMzB+P,EAAMzK,2BAIPF,EAAa2K,EAAM9F,YAAY/B,EAAQ,EAAI6H,EAAMzK,0BAA4B,EAAG4C,GACzEA,EAAQ9C,EAAWgC,OAAShC,EAAWmB,GAAGP,EAAKhG,SAJtDoF,EAAa2K,EAAM9F,YAAYjE,EAAKF,iBAAiB,GAC9CV,EAAWgC,SAO9ByU,cAAe,SAAUpY,EAAMoI,EAAY3D,GAA5B,GACP9C,GAGAG,EAFAwK,EAAQtL,KAAKwK,OAAOpD,GACpB7F,EAAO+J,EAAM/H,OAAOvE,EAAMA,EAAMyE,GAAO,GAAO,GAAGpI,KAErD,MAAI+L,GAAcpH,KAAKwK,OAAOjS,OAAS,GAGvC,MAAIyH,MAAKoL,mBACE7J,EAEPvB,KAAKmM,uBACAb,EAAMzK,2BAIPC,EAAyBwK,EAAMxK,yBAC/BH,EAAa2K,EAAM9F,YAAY1E,EAAyB,EAAIS,EAAKF,gBAAiBP,GAC3E2C,EAAQ9C,EAAW+B,QAAU/B,EAAWmB,GAAGP,EAAKF,mBALvDV,EAAa2K,EAAM9F,YAAY,GAAG,GAC3B7E,EAAWmB,GAAGP,EAAKhG,QAOzB+P,EAAMzK,2BAIPF,EAAa2K,EAAM9F,YAAY,EAAG/B,GAC3BA,EAAQ9C,EAAW+B,QAAU/B,EAAWmB,GAAGP,EAAKhG,SAJvDoF,EAAa2K,EAAM9F,YAAYjE,EAAKF,iBAAiB,GAC9CV,EAAW+B,UAO9B2U,qBAAsB,WAClB,UAEJC,sBAAuB,aAGvBC,yBAA0B,SAAUvb,GAChC,MAAOA,MA2CX6C,EAAQ,SAAUV,GAClB,GAA2CqZ,GAAIC,EAAWC,EAAO/I,EAAGgJ,EAAhE5Z,EAAQiC,KAAM4X,EAAU/Y,EAAM+Y,OAClC,IAAyB,IAArBnP,UAAUlQ,OAEV,IADA4F,EAAQJ,EAAM8Z,aAAa1Z,GACtBwQ,EAAI,EAAGA,EAAIiJ,EAAQrf,OAAQoW,IAC5B6I,EAAKI,EAAQjJ,GAAG6I,GAChBC,EAAYG,EAAQjJ,GAAGmJ,QACvBJ,EAAQF,EAAGO,KAAK5Z,GACZuZ,IACAC,EAAWF,EAAUC,GACrB3Z,EAAMia,EAAIL,EAAS,GACnB5Z,EAAMka,EAAIN,EAAS,GACnB5Z,EAAMma,EAAIP,EAAS,QAI3B5Z,GAAMia,EAAIvP,UAAU,GACpB1K,EAAMka,EAAIxP,UAAU,GACpB1K,EAAMma,EAAIzP,UAAU,EAExB1K,GAAMia,EAAIja,EAAMoa,cAAcpa,EAAMia,GACpCja,EAAMka,EAAIla,EAAMoa,cAAcpa,EAAMka,GACpCla,EAAMma,EAAIna,EAAMoa,cAAcpa,EAAMma,IAExCrZ,EAAMuZ,WACFP,aAAc,SAAU1Z,GAQpB,MAPAA,GAAQA,GAAS,OACM,KAAnBA,EAAMka,OAAO,KACbla,EAAQA,EAAMma,OAAO,EAAG,IAE5Bna,EAAQA,EAAMQ,QAAQ,KAAM,IAC5BR,EAAQA,EAAMoa,cACdpa,EAAQU,EAAM2Z,YAAYra,IAAUA,GAGxCga,cAAe,SAAUha,GACrB,MAAOA,GAAQ,GAAKsa,MAAMta,GAAS,EAAIA,EAAQ,IAAM,IAAMA,GAE/Dua,eAAgB,WACZ,GAAI3a,GAAQiC,IACZ,OAAOR,GAAKmZ,KAAK,KAAQ5a,EAAMia,EAAIja,EAAMia,EAAI,KAAQja,EAAMka,EAAIla,EAAMka,EAAI,KAAQla,EAAMma,EAAIna,EAAMma,IAErGtF,OAAQ,WAAA,GACA7U,GAAQiC,KACR4Y,EAAkB7a,EAAM2a,gBAC5B,OAAOE,GAAkB,MAGjC/Z,EAAM+Y,UAEEJ,GAAI,+CACJM,QAAS,SAAUJ,GACf,OACInC,SAASmC,EAAM,GAAI,IACnBnC,SAASmC,EAAM,GAAI,IACnBnC,SAASmC,EAAM,GAAI,QAK3BF,GAAI,0BACJM,QAAS,SAAUJ,GACf,OACInC,SAASmC,EAAM,GAAI,IACnBnC,SAASmC,EAAM,GAAI,IACnBnC,SAASmC,EAAM,GAAI,QAK3BF,GAAI,0BACJM,QAAS,SAAUJ,GACf,OACInC,SAASmC,EAAM,GAAKA,EAAM,GAAI,IAC9BnC,SAASmC,EAAM,GAAKA,EAAM,GAAI,IAC9BnC,SAASmC,EAAM,GAAKA,EAAM,GAAI,QAK9C7Y,EAAM2Z,aACFK,KAAM,SACNC,MAAO,SACPC,MAAO,SACPC,MAAO,SACPC,KAAM,SACNC,MAAO,SACPC,MAAO,SACPC,KAAM,SACNC,SAAU,SACVC,SAAU,SACVC,SAAU,SACVC,UAAW,SACXC,WAAY,SACZC,QAAS,SACTC,QAAS,SACTC,QAAS,SACTC,KAAM,SACNC,UAAW,SACXC,KAAM,SACNC,MAAO,SACPC,YAAa,SACbC,OAAQ,SACRC,MAAO,SACPC,MAAO,SACPC,UAAW,SACXC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,YAAa;AACbC,KAAM,SACNC,UAAW,SACXC,MAAO,SACPC,QAAS,SACTC,OAAQ,SACRC,WAAY,SACZC,KAAM,SACNC,MAAO,SACPC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,KAAM,SACNC,KAAM,SACNC,OAAQ,SACRC,IAAK,SACLC,UAAW,SACXC,OAAQ,SACRC,OAAQ,SACRC,QAAS,SACTC,UAAW,SACXC,UAAW,SACXC,KAAM,SACNC,UAAW,SACXC,IAAK,SACLC,KAAM,SACNC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,MAAO,SACPC,MAAO,SACPC,WAAY,SACZC,OAAQ,SACRC,YAAa,UA+Fb9d,EAAsB,sBAI1B9G,EAAE+H,OAAOjI,EAAGyR,eACRnN,cAAeA,EACfG,WAAYA,EACZV,WAAYA,EACZR,gBAAiBA,EACjBmD,cAAeA,KAErBQ,OAAOnH,MAAMglB,QACR7d,OAAOnH,OACE,kBAAVD,SAAwBA,OAAOklB,IAAMllB,OAAS,SAAUmlB,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.scheduler.view.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.scheduler.view', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'scheduler.view',\n        name: 'Scheduler View',\n        category: 'web',\n        description: 'The Scheduler Common View',\n        depends: ['core'],\n        hidden: true\n    };\n    kendo.ui.scheduler = {};\n    (function ($) {\n        var kendo = window.kendo, ui = kendo.ui, getDate = kendo.date.getDate, Widget = ui.Widget, outerHeight = kendo._outerHeight, keys = kendo.keys, NS = '.kendoSchedulerView', INVERSE_COLOR_CLASS = 'k-event-inverse', MIN_HORIZONTAL_SCROLL_SIZE = 1024, math = Math;\n        function levels(values, key) {\n            var result = [];\n            function collect(depth, values) {\n                values = values[key];\n                if (values) {\n                    var level = result[depth] = result[depth] || [];\n                    for (var idx = 0; idx < values.length; idx++) {\n                        level.push(values[idx]);\n                        collect(depth + 1, values[idx]);\n                    }\n                }\n            }\n            collect(0, values);\n            return result;\n        }\n        function cellspacing() {\n            if (kendo.support.cssBorderSpacing) {\n                return '';\n            }\n            return 'cellspacing=\"0\"';\n        }\n        function table(tableRows, className) {\n            if (!tableRows.length) {\n                return '';\n            }\n            return '<table ' + cellspacing() + ' class=\"' + $.trim('k-scheduler-table ' + (className || '')) + '\">' + '<tr>' + tableRows.join('</tr><tr>') + '</tr>' + '</table>';\n        }\n        function allDayTable(tableRows, className) {\n            if (!tableRows.length) {\n                return '';\n            }\n            return '<div style=\\'position:relative\\'>' + table(tableRows, className) + '</div>';\n        }\n        function timesHeader(columnLevelCount, allDaySlot, rowCount) {\n            var tableRows = [];\n            if (rowCount > 0) {\n                for (var idx = 0; idx < columnLevelCount; idx++) {\n                    tableRows.push('<th>&#8203;</th>');\n                }\n            }\n            if (allDaySlot) {\n                tableRows.push('<th class=\"k-scheduler-times-all-day\">' + allDaySlot.text + '</th>');\n            }\n            if (rowCount < 1) {\n                return $();\n            }\n            return $('<div class=\"k-scheduler-times\">' + table(tableRows) + '</div>');\n        }\n        function datesHeader(columnLevels, columnCount, allDaySlot) {\n            var dateTableRows = [];\n            var columnIndex;\n            for (var columnLevelIndex = 0; columnLevelIndex < columnLevels.length; columnLevelIndex++) {\n                var level = columnLevels[columnLevelIndex];\n                var th = [];\n                var colspan = columnCount / level.length;\n                for (columnIndex = 0; columnIndex < level.length; columnIndex++) {\n                    var column = level[columnIndex];\n                    th.push('<th colspan=\"' + (column.colspan || colspan) + '\" class=\"' + (column.className || '') + '\">' + column.text + '</th>');\n                }\n                dateTableRows.push(th.join(''));\n            }\n            var allDayTableRows = [];\n            if (allDaySlot) {\n                var lastLevel = columnLevels[columnLevels.length - 1];\n                var td = [];\n                var cellContent = allDaySlot.cellContent;\n                for (columnIndex = 0; columnIndex < lastLevel.length; columnIndex++) {\n                    td.push('<td class=\"' + (lastLevel[columnIndex].className || '') + '\">' + (cellContent ? cellContent(columnIndex) : '&nbsp;') + '</td>');\n                }\n                allDayTableRows.push(td.join(''));\n            }\n            return $('<div class=\"k-scheduler-header k-state-default\">' + '<div class=\"k-scheduler-header-wrap\">' + table(dateTableRows) + allDayTable(allDayTableRows, 'k-scheduler-header-all-day') + '</div>' + '</div>');\n        }\n        function times(rowLevels, rowCount, isMobile) {\n            var rows = new Array(rowCount).join().split(',');\n            var rowHeaderRows = [];\n            var rowIndex;\n            for (var rowLevelIndex = 0; rowLevelIndex < rowLevels.length; rowLevelIndex++) {\n                var level = rowLevels[rowLevelIndex];\n                var rowspan = rowCount / level.length;\n                var className;\n                var text;\n                for (rowIndex = 0; rowIndex < level.length; rowIndex++) {\n                    className = level[rowIndex].className || '';\n                    text = level[rowIndex].text;\n                    if (level[rowIndex].allDay) {\n                        className = 'k-scheduler-times-all-day';\n                    }\n                    if (isMobile && className.indexOf('k-scheduler-group-cell') !== -1) {\n                        text = '<span class=\"k-scheduler-group-text\">' + text + '</span>';\n                    }\n                    rows[rowspan * rowIndex] += '<th class=\"' + className + '\" rowspan=\"' + rowspan + '\">' + text + '</th>';\n                }\n            }\n            for (rowIndex = 0; rowIndex < rowCount; rowIndex++) {\n                rowHeaderRows.push(rows[rowIndex]);\n            }\n            if (rowCount < 1) {\n                return $();\n            }\n            return $('<div class=\"k-scheduler-times\">' + table(rowHeaderRows) + '</div>');\n        }\n        function content() {\n            return $('<div class=\"k-scheduler-content\">' + '<table ' + cellspacing() + ' class=\"k-scheduler-table\"/>' + '</div>');\n        }\n        var HINT = '<div class=\"k-marquee k-scheduler-marquee\">' + '<div class=\"k-marquee-color\"></div>' + '<div class=\"k-marquee-text\">' + '<div class=\"k-label-top\"></div>' + '<div class=\"k-label-bottom\"></div>' + '</div>' + '</div>';\n        var ResourceView = kendo.Class.extend({\n            init: function (index, isRtl) {\n                this._index = index;\n                this._timeSlotCollections = [];\n                this._daySlotCollections = [];\n                this._isRtl = isRtl;\n            },\n            addTimeSlotCollection: function (startDate, endDate) {\n                return this._addCollection(startDate, endDate, this._timeSlotCollections);\n            },\n            addDaySlotCollection: function (startDate, endDate) {\n                return this._addCollection(startDate, endDate, this._daySlotCollections);\n            },\n            _addCollection: function (startDate, endDate, collections) {\n                var collection = new SlotCollection(startDate, endDate, this._index, collections.length);\n                collections.push(collection);\n                return collection;\n            },\n            timeSlotCollectionCount: function () {\n                return this._timeSlotCollections.length;\n            },\n            daySlotCollectionCount: function () {\n                return this._daySlotCollections.length;\n            },\n            daySlotByPosition: function (x, y, byDate) {\n                return this._slotByPosition(x, y, this._daySlotCollections, byDate);\n            },\n            timeSlotByPosition: function (x, y, byDate) {\n                return this._slotByPosition(x, y, this._timeSlotCollections, byDate);\n            },\n            _slotByPosition: function (x, y, collections, byDate) {\n                for (var collectionIndex = 0; collectionIndex < collections.length; collectionIndex++) {\n                    var collection = collections[collectionIndex];\n                    for (var slotIndex = 0; slotIndex < collection.count(); slotIndex++) {\n                        var slot = collection.at(slotIndex);\n                        var width = slot.offsetWidth;\n                        var height = slot.offsetHeight;\n                        var nextSlot;\n                        var horizontalEnd = slot.offsetLeft + width;\n                        var verticalEnd = slot.offsetTop + height;\n                        if (!byDate) {\n                            nextSlot = collection.at(slotIndex + 1);\n                        }\n                        if (nextSlot) {\n                            if (nextSlot.offsetLeft != slot.offsetLeft) {\n                                if (this._isRtl) {\n                                    horizontalEnd = slot.offsetLeft + (slot.offsetLeft - nextSlot.offsetLeft);\n                                } else {\n                                    horizontalEnd = nextSlot.offsetLeft;\n                                }\n                            } else {\n                                verticalEnd = nextSlot.offsetTop;\n                            }\n                        }\n                        if (x >= slot.offsetLeft && x < horizontalEnd && y >= slot.offsetTop && y < verticalEnd) {\n                            return slot;\n                        }\n                    }\n                }\n            },\n            refresh: function () {\n                var collectionIndex;\n                for (collectionIndex = 0; collectionIndex < this._daySlotCollections.length; collectionIndex++) {\n                    this._daySlotCollections[collectionIndex].refresh();\n                }\n                for (collectionIndex = 0; collectionIndex < this._timeSlotCollections.length; collectionIndex++) {\n                    this._timeSlotCollections[collectionIndex].refresh();\n                }\n            },\n            timeSlotRanges: function (startTime, endTime) {\n                var collections = this._timeSlotCollections;\n                var start = this._startSlot(startTime, collections);\n                if (!start.inRange && startTime >= start.slot.end) {\n                    start = null;\n                }\n                var end = start;\n                if (startTime < endTime) {\n                    end = this._endSlot(endTime, collections);\n                }\n                if (end && !end.inRange && endTime <= end.slot.start) {\n                    end = null;\n                }\n                if (start === null && end === null) {\n                    return [];\n                }\n                if (start === null) {\n                    if (end.slot.end <= startTime) {\n                        return [];\n                    }\n                    start = {\n                        inRange: true,\n                        slot: collections[end.slot.collectionIndex].first()\n                    };\n                }\n                if (end === null) {\n                    if (start.slot.start >= endTime) {\n                        return [];\n                    }\n                    end = {\n                        inRange: true,\n                        slot: collections[start.slot.collectionIndex].last()\n                    };\n                }\n                return this._continuousRange(TimeSlotRange, collections, start, end);\n            },\n            daySlotRanges: function (startTime, endTime, isAllDay) {\n                var collections = this._daySlotCollections;\n                var start = this._startSlot(startTime, collections, isAllDay);\n                if (!start.inRange && startTime >= start.slot.end) {\n                    start = null;\n                }\n                var end = start;\n                if (startTime < endTime) {\n                    end = this._endSlot(endTime, collections, isAllDay);\n                }\n                if (end && !end.inRange && endTime <= end.slot.start) {\n                    end = null;\n                }\n                if (start === null && end === null) {\n                    return [];\n                }\n                if (start === null) {\n                    if (end.slot.end <= startTime) {\n                        return [];\n                    }\n                    do {\n                        startTime += kendo.date.MS_PER_DAY;\n                        start = this._startSlot(startTime, collections, isAllDay);\n                    } while (!start.inRange && startTime >= start.slot.end);\n                }\n                if (end === null) {\n                    if (start.slot.start >= endTime) {\n                        return [];\n                    }\n                    do {\n                        endTime -= kendo.date.MS_PER_DAY;\n                        end = this._endSlot(endTime, collections, isAllDay);\n                    } while (!end.inRange && endTime <= end.slot.start);\n                }\n                return this._continuousRange(DaySlotRange, collections, start, end);\n            },\n            _continuousRange: function (range, collections, start, end) {\n                var startSlot = start.slot;\n                var endSlot = end.slot;\n                var startIndex = startSlot.collectionIndex;\n                var endIndex = endSlot.collectionIndex;\n                var ranges = [];\n                for (var collectionIndex = startIndex; collectionIndex <= endIndex; collectionIndex++) {\n                    var collection = collections[collectionIndex];\n                    var first = collection.first();\n                    var last = collection.last();\n                    var head = false;\n                    var tail = false;\n                    if (collectionIndex == startIndex) {\n                        tail = !start.inRange;\n                    }\n                    if (collectionIndex == endIndex) {\n                        head = !end.inRange;\n                    }\n                    if (first.start < startSlot.start) {\n                        first = startSlot;\n                    }\n                    if (last.start > endSlot.start) {\n                        last = endSlot;\n                    }\n                    if (startIndex < endIndex) {\n                        if (collectionIndex == startIndex) {\n                            head = true;\n                        } else if (collectionIndex == endIndex) {\n                            tail = true;\n                        } else {\n                            head = tail = true;\n                        }\n                    }\n                    ranges.push(new range({\n                        start: first,\n                        end: last,\n                        collection: collection,\n                        head: head,\n                        tail: tail\n                    }));\n                }\n                return ranges;\n            },\n            slotRanges: function (event, isDay) {\n                var startTime = event._startTime || kendo.date.toUtcTime(event.start);\n                var endTime = event._endTime || kendo.date.toUtcTime(event.end);\n                if (isDay === undefined) {\n                    isDay = event.isMultiDay();\n                }\n                if (isDay) {\n                    return this.daySlotRanges(startTime, endTime, event.isAllDay);\n                }\n                return this.timeSlotRanges(startTime, endTime);\n            },\n            ranges: function (startTime, endTime, isDay, isAllDay) {\n                if (typeof startTime != 'number') {\n                    startTime = kendo.date.toUtcTime(startTime);\n                }\n                if (typeof endTime != 'number') {\n                    endTime = kendo.date.toUtcTime(endTime);\n                }\n                if (isDay) {\n                    return this.daySlotRanges(startTime, endTime, isAllDay);\n                }\n                return this.timeSlotRanges(startTime, endTime);\n            },\n            _startCollection: function (date, collections) {\n                for (var collectionIndex = 0; collectionIndex < collections.length; collectionIndex++) {\n                    var collection = collections[collectionIndex];\n                    if (collection.startInRange(date)) {\n                        return collection;\n                    }\n                }\n                return null;\n            },\n            _endCollection: function (date, collections, isAllDay) {\n                for (var collectionIndex = 0; collectionIndex < collections.length; collectionIndex++) {\n                    var collection = collections[collectionIndex];\n                    if (collection.endInRange(date, isAllDay)) {\n                        return collection;\n                    }\n                }\n                return null;\n            },\n            _getCollections: function (isDay) {\n                return isDay ? this._daySlotCollections : this._timeSlotCollections;\n            },\n            continuousSlot: function (slot, reverse) {\n                var pad = reverse ? -1 : 1;\n                var collections = this._getCollections(slot.isDaySlot);\n                var collection = collections[slot.collectionIndex + pad];\n                return collection ? collection[reverse ? 'last' : 'first']() : undefined;\n            },\n            firstSlot: function () {\n                var collections = this._getCollections(this.daySlotCollectionCount());\n                return collections[0].first();\n            },\n            lastSlot: function () {\n                var collections = this._getCollections(this.daySlotCollectionCount());\n                return collections[collections.length - 1].last();\n            },\n            upSlot: function (slot, keepCollection, groupByDateVertically) {\n                var that = this;\n                var moveToDaySlot = function (isDaySlot, collectionIndex, index) {\n                    var isFirstCell = index === 0;\n                    if (!keepCollection && !isDaySlot && isFirstCell && that.daySlotCollectionCount()) {\n                        return that._daySlotCollections[0].at(collectionIndex);\n                    }\n                };\n                if (!this.timeSlotCollectionCount()) {\n                    keepCollection = true;\n                }\n                return this._verticalSlot(slot, -1, moveToDaySlot, groupByDateVertically);\n            },\n            downSlot: function (slot, keepCollection, groupByDateVertically) {\n                var that = this;\n                var moveToTimeSlot = function (isDaySlot, collectionIndex, index) {\n                    if (!keepCollection && isDaySlot && that.timeSlotCollectionCount()) {\n                        return that._timeSlotCollections[index].at(0);\n                    }\n                };\n                if (!this.timeSlotCollectionCount()) {\n                    keepCollection = true;\n                }\n                return this._verticalSlot(slot, 1, moveToTimeSlot, groupByDateVertically);\n            },\n            leftSlot: function (slot, groupByDateVertically) {\n                return this._horizontalSlot(slot, -1, groupByDateVertically);\n            },\n            rightSlot: function (slot, groupByDateVertically) {\n                return this._horizontalSlot(slot, 1, groupByDateVertically);\n            },\n            _horizontalSlot: function (slot, step, groupByDateVertically) {\n                var index = slot.index;\n                var isDaySlot = slot.isDaySlot;\n                var collectionIndex = slot.collectionIndex;\n                var collections = this._getCollections(isDaySlot);\n                isDaySlot = groupByDateVertically ? false : isDaySlot;\n                if (isDaySlot) {\n                    index += step;\n                } else {\n                    collectionIndex += step;\n                }\n                var collection = collections[collectionIndex];\n                return collection ? collection.at(index) : undefined;\n            },\n            _verticalSlot: function (slot, step, swapCollection, groupByDateVertically) {\n                var index = slot.index;\n                var isDaySlot = slot.isDaySlot;\n                var collectionIndex = slot.collectionIndex;\n                var collections = this._getCollections(isDaySlot);\n                slot = swapCollection(isDaySlot, collectionIndex, index);\n                if (slot) {\n                    return slot;\n                }\n                isDaySlot = groupByDateVertically ? false : isDaySlot;\n                if (isDaySlot) {\n                    collectionIndex += step;\n                } else {\n                    index += step;\n                }\n                var collection = collections[collectionIndex];\n                return collection ? collection.at(index) : undefined;\n            },\n            _collection: function (index, multiday) {\n                var collections = multiday ? this._daySlotCollections : this._timeSlotCollections;\n                return collections[index];\n            },\n            _startSlot: function (time, collections, isAllDay) {\n                var collection = this._startCollection(time, collections);\n                var inRange = true;\n                if (!collection) {\n                    collection = collections[0];\n                    inRange = false;\n                }\n                var slot = collection.slotByStartDate(time, isAllDay);\n                if (!slot) {\n                    slot = collection.first();\n                    inRange = false;\n                }\n                return {\n                    slot: slot,\n                    inRange: inRange\n                };\n            },\n            _endSlot: function (time, collections, isAllDay) {\n                var collection = this._endCollection(time, collections, isAllDay);\n                var inRange = true;\n                if (!collection) {\n                    collection = collections[collections.length - 1];\n                    inRange = false;\n                }\n                var slot = collection.slotByEndDate(time, isAllDay);\n                if (!slot) {\n                    slot = collection.last();\n                    inRange = false;\n                }\n                return {\n                    slot: slot,\n                    inRange: inRange\n                };\n            },\n            getSlotCollection: function (index, isDay) {\n                return this[isDay ? 'getDaySlotCollection' : 'getTimeSlotCollection'](index);\n            },\n            getTimeSlotCollection: function (index) {\n                return this._timeSlotCollections[index];\n            },\n            getDaySlotCollection: function (index) {\n                return this._daySlotCollections[index];\n            }\n        });\n        var SlotRange = kendo.Class.extend({\n            init: function (options) {\n                $.extend(this, options);\n            },\n            innerHeight: function () {\n                var collection = this.collection;\n                var startIndex = this.start.index;\n                var endIndex = this.end.index;\n                var result = 0;\n                for (var slotIndex = startIndex; slotIndex <= endIndex; slotIndex++) {\n                    result += collection.at(slotIndex).offsetHeight;\n                }\n                return result;\n            },\n            events: function () {\n                return this.collection.events();\n            },\n            addEvent: function (event) {\n                this.events().push(event);\n            },\n            startSlot: function () {\n                if (this.start.offsetLeft > this.end.offsetLeft) {\n                    return this.end;\n                }\n                return this.start;\n            },\n            endSlot: function () {\n                if (this.start.offsetLeft > this.end.offsetLeft) {\n                    return this.start;\n                }\n                return this.end;\n            }\n        });\n        var TimeSlotRange = SlotRange.extend({\n            innerHeight: function () {\n                var collection = this.collection;\n                var startIndex = this.start.index;\n                var endIndex = this.end.index;\n                var result = 0;\n                for (var slotIndex = startIndex; slotIndex <= endIndex; slotIndex++) {\n                    result += collection.at(slotIndex).offsetHeight;\n                }\n                return result;\n            },\n            outerRect: function (start, end, snap) {\n                return this._rect('offset', start, end, snap);\n            },\n            _rect: function (property, start, end, snap) {\n                var top;\n                var bottom;\n                var left;\n                var right;\n                var startSlot = this.start;\n                var endSlot = this.end;\n                var isRtl = kendo.support.isRtl(startSlot.element);\n                if (typeof start != 'number') {\n                    start = kendo.date.toUtcTime(start);\n                }\n                if (typeof end != 'number') {\n                    end = kendo.date.toUtcTime(end);\n                }\n                if (snap) {\n                    top = startSlot.offsetTop;\n                    bottom = endSlot.offsetTop + endSlot[property + 'Height'];\n                    if (isRtl) {\n                        left = endSlot.offsetLeft;\n                        right = startSlot.offsetLeft + startSlot[property + 'Width'];\n                    } else {\n                        left = startSlot.offsetLeft;\n                        right = endSlot.offsetLeft + endSlot[property + 'Width'];\n                    }\n                } else {\n                    var startOffset = start - startSlot.start;\n                    if (startOffset < 0) {\n                        startOffset = 0;\n                    }\n                    var startSlotDuration = startSlot.end - startSlot.start;\n                    top = startSlot.offsetTop + startSlot[property + 'Height'] * startOffset / startSlotDuration;\n                    var endOffset = endSlot.end - end;\n                    if (endOffset < 0) {\n                        endOffset = 0;\n                    }\n                    var endSlotDuration = endSlot.end - endSlot.start;\n                    bottom = endSlot.offsetTop + endSlot[property + 'Height'] - endSlot[property + 'Height'] * endOffset / endSlotDuration;\n                    if (isRtl) {\n                        left = Math.round(endSlot.offsetLeft + endSlot[property + 'Width'] * endOffset / endSlotDuration);\n                        right = Math.round(startSlot.offsetLeft + startSlot[property + 'Width'] - startSlot[property + 'Width'] * startOffset / startSlotDuration);\n                    } else {\n                        left = Math.round(startSlot.offsetLeft + startSlot[property + 'Width'] * startOffset / startSlotDuration);\n                        right = Math.round(endSlot.offsetLeft + endSlot[property + 'Width'] - endSlot[property + 'Width'] * endOffset / endSlotDuration);\n                    }\n                }\n                return {\n                    top: top,\n                    bottom: bottom,\n                    left: left === 0 ? left : left + 1,\n                    right: right\n                };\n            },\n            innerRect: function (start, end, snap) {\n                return this._rect('client', start, end, snap);\n            }\n        });\n        var DaySlotRange = SlotRange.extend({\n            innerWidth: function () {\n                var collection = this.collection;\n                var startIndex = this.start.index;\n                var endIndex = this.end.index;\n                var result = 0;\n                var width = startIndex !== endIndex ? 'offsetWidth' : 'clientWidth';\n                for (var slotIndex = startIndex; slotIndex <= endIndex; slotIndex++) {\n                    result += collection.at(slotIndex)[width];\n                }\n                return result;\n            }\n        });\n        var SlotCollection = kendo.Class.extend({\n            init: function (startDate, endDate, groupIndex, collectionIndex) {\n                this._slots = [];\n                this._events = [];\n                this._start = kendo.date.toUtcTime(startDate);\n                this._end = kendo.date.toUtcTime(endDate);\n                this._groupIndex = groupIndex;\n                this._collectionIndex = collectionIndex;\n            },\n            refresh: function () {\n                for (var slotIndex = 0; slotIndex < this._slots.length; slotIndex++) {\n                    this._slots[slotIndex].refresh();\n                }\n            },\n            startInRange: function (date) {\n                return this._start <= date && date < this._end;\n            },\n            endInRange: function (date, isAllDay) {\n                var end = isAllDay ? date < this._end : date <= this._end;\n                return this._start <= date && end;\n            },\n            slotByStartDate: function (date) {\n                var time = date;\n                if (typeof time != 'number') {\n                    time = kendo.date.toUtcTime(date);\n                }\n                for (var slotIndex = 0; slotIndex < this._slots.length; slotIndex++) {\n                    var slot = this._slots[slotIndex];\n                    if (slot.startInRange(time)) {\n                        return slot;\n                    }\n                }\n                return null;\n            },\n            slotByEndDate: function (date, allday) {\n                var time = date;\n                if (typeof time != 'number') {\n                    time = kendo.date.toUtcTime(date);\n                }\n                if (allday) {\n                    return this.slotByStartDate(date, false);\n                }\n                for (var slotIndex = 0; slotIndex < this._slots.length; slotIndex++) {\n                    var slot = this._slots[slotIndex];\n                    if (slot.endInRange(time)) {\n                        return slot;\n                    }\n                }\n                return null;\n            },\n            count: function () {\n                return this._slots.length;\n            },\n            events: function () {\n                return this._events;\n            },\n            addTimeSlot: function (element, start, end, isHorizontal) {\n                var slot = new TimeSlot(element, start, end, this._groupIndex, this._collectionIndex, this._slots.length, isHorizontal);\n                this._slots.push(slot);\n            },\n            addDaySlot: function (element, start, end, eventCount) {\n                var slot = new DaySlot(element, start, end, this._groupIndex, this._collectionIndex, this._slots.length, eventCount);\n                this._slots.push(slot);\n            },\n            first: function () {\n                return this._slots[0];\n            },\n            last: function () {\n                return this._slots[this._slots.length - 1];\n            },\n            at: function (index) {\n                return this._slots[index];\n            }\n        });\n        var Slot = kendo.Class.extend({\n            init: function (element, start, end, groupIndex, collectionIndex, index) {\n                this.element = element;\n                this.clientWidth = element.clientWidth;\n                this.clientHeight = element.clientHeight;\n                this.offsetWidth = element.offsetWidth;\n                this.offsetHeight = element.offsetHeight;\n                this.offsetTop = element.offsetTop;\n                this.offsetLeft = element.offsetLeft;\n                this.start = start;\n                this.end = end;\n                this.element = element;\n                this.groupIndex = groupIndex;\n                this.collectionIndex = collectionIndex;\n                this.index = index;\n                this.isDaySlot = false;\n            },\n            refresh: function () {\n                var element = this.element;\n                this.clientWidth = element.clientWidth;\n                this.clientHeight = element.clientHeight;\n                this.offsetWidth = element.offsetWidth;\n                this.offsetHeight = element.offsetHeight;\n                this.offsetTop = element.offsetTop;\n                this.offsetLeft = element.offsetLeft;\n            },\n            startDate: function () {\n                return kendo.timezone.toLocalDate(this.start);\n            },\n            endDate: function () {\n                return kendo.timezone.toLocalDate(this.end);\n            },\n            startInRange: function (date) {\n                return this.start <= date && date < this.end;\n            },\n            endInRange: function (date) {\n                return this.start < date && date <= this.end;\n            },\n            startOffset: function () {\n                return this.start;\n            },\n            endOffset: function () {\n                return this.end;\n            }\n        });\n        var TimeSlot = Slot.extend({\n            init: function (element, start, end, groupIndex, collectionIndex, index, isHorizontal) {\n                Slot.fn.init.apply(this, arguments);\n                this.isHorizontal = isHorizontal ? true : false;\n            },\n            offsetX: function (rtl, offset) {\n                if (rtl) {\n                    return this.offsetLeft + offset;\n                } else {\n                    return this.offsetLeft + offset;\n                }\n            },\n            startInRange: function (date) {\n                return this.start <= date && date < this.end;\n            },\n            endInRange: function (date) {\n                return this.start < date && date <= this.end;\n            },\n            startOffset: function (x, y, snap) {\n                if (snap) {\n                    return this.start;\n                }\n                var offset = $(this.element).offset();\n                var duration = this.end - this.start;\n                var difference;\n                var time;\n                if (this.isHorizontal) {\n                    var isRtl = kendo.support.isRtl(this.element);\n                    difference = x - offset.left;\n                    time = Math.floor(duration * (difference / this.offsetWidth));\n                    if (isRtl) {\n                        return this.start + duration - time;\n                    }\n                } else {\n                    difference = y - offset.top;\n                    time = Math.floor(duration * (difference / this.offsetHeight));\n                }\n                return this.start + time;\n            },\n            endOffset: function (x, y, snap) {\n                if (snap) {\n                    return this.end;\n                }\n                var offset = $(this.element).offset();\n                var duration = this.end - this.start;\n                var difference;\n                var time;\n                if (this.isHorizontal) {\n                    var isRtl = kendo.support.isRtl(this.element);\n                    difference = x - offset.left;\n                    time = Math.floor(duration * (difference / this.offsetWidth));\n                    if (isRtl) {\n                        return this.start + duration - time;\n                    }\n                } else {\n                    difference = y - offset.top;\n                    time = Math.floor(duration * (difference / this.offsetHeight));\n                }\n                return this.start + time;\n            }\n        });\n        var DaySlot = Slot.extend({\n            init: function (element, start, end, groupIndex, collectionIndex, index, eventCount) {\n                Slot.fn.init.apply(this, arguments);\n                this.eventCount = eventCount;\n                this.isDaySlot = true;\n                if (this.element.children.length) {\n                    var firstChild = this.element.children[0];\n                    this.firstChildHeight = firstChild.offsetHeight;\n                    this.firstChildTop = firstChild.offsetTop;\n                } else {\n                    this.firstChildHeight = 3;\n                    this.firstChildTop = 0;\n                }\n            },\n            startDate: function () {\n                var date = new Date(this.start);\n                return kendo.timezone.apply(date, 'Etc/UTC');\n            },\n            endDate: function () {\n                var date = new Date(this.end);\n                return kendo.timezone.apply(date, 'Etc/UTC');\n            },\n            startInRange: function (date) {\n                return this.start <= date && date < this.end;\n            },\n            endInRange: function (date) {\n                return this.start < date && date <= this.end;\n            }\n        });\n        var scrollbarWidth;\n        function scrollbar() {\n            scrollbarWidth = scrollbarWidth ? scrollbarWidth : kendo.support.scrollbar();\n            return scrollbarWidth;\n        }\n        kendo.ui.SchedulerView = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                this._normalizeOptions();\n                this._scrollbar = scrollbar();\n                this._isRtl = kendo.support.isRtl(element);\n                this._resizeHint = $();\n                this._moveHint = $();\n                this._cellId = kendo.guid();\n                this._resourcesForGroups();\n                this._selectedSlots = [];\n            },\n            visibleEndDate: function () {\n                return this.endDate();\n            },\n            _normalizeOptions: function () {\n                var options = this.options;\n                if (options.startTime) {\n                    options.startTime.setMilliseconds(0);\n                }\n                if (options.endTime) {\n                    options.endTime.setMilliseconds(0);\n                }\n                if (options.workDayStart) {\n                    options.workDayStart.setMilliseconds(0);\n                }\n                if (options.workDayEnd) {\n                    options.workDayEnd.setMilliseconds(0);\n                }\n            },\n            _isMobile: function () {\n                var options = this.options;\n                return options.mobile === true && kendo.support.mobileOS || options.mobile === 'phone' || options.mobile === 'tablet';\n            },\n            _addResourceView: function () {\n                var resourceView = new ResourceView(this.groups.length, this._isRtl);\n                this.groups.push(resourceView);\n                return resourceView;\n            },\n            dateForTitle: function () {\n                return kendo.format(this.options.selectedDateFormat, this.startDate(), this.endDate());\n            },\n            shortDateForTitle: function () {\n                return kendo.format(this.options.selectedShortDateFormat, this.startDate(), this.endDate());\n            },\n            mobileDateForTitle: function () {\n                return kendo.format(this.options.selectedMobileDateFormat || this.options.selectedShortDateFormat, this.startDate(), this.endDate());\n            },\n            _changeGroup: function (selection, previous) {\n                var method = previous ? 'prevGroupSlot' : 'nextGroupSlot';\n                var slot = this[method](selection.start, selection.groupIndex, selection.isAllDay);\n                if (slot) {\n                    selection.groupIndex += previous ? -1 : 1;\n                }\n                if (this._isGroupedByDate() && !slot) {\n                    selection.groupIndex = previous ? this.groups.length - 1 : 0;\n                }\n                return slot;\n            },\n            _changeDate: function (selection, slot, previous) {\n                var group = this.groups[selection.groupIndex];\n                var collections, index;\n                if (previous) {\n                    collections = group._getCollections(false);\n                    index = group.daySlotCollectionCount() ? slot.index - 1 : slot.collectionIndex - 1;\n                    if (index >= 0) {\n                        return collections[index]._slots[collections[index]._slots.length - 1];\n                    }\n                } else {\n                    collections = group._getCollections(group.daySlotCollectionCount());\n                    index = group.daySlotCollectionCount() ? 0 : slot.collectionIndex + 1;\n                    var slotIndex = group.daySlotCollectionCount() ? slot.collectionIndex + 1 : 0;\n                    if (collections[index] && collections[index]._slots[slotIndex]) {\n                        return collections[index]._slots[slotIndex];\n                    }\n                }\n            },\n            _changeGroupContinuously: function () {\n                return null;\n            },\n            _changeViewPeriod: function () {\n                return false;\n            },\n            _isInRange: function (newStart, newEnd) {\n                if (!newStart || !newEnd || !this.options.min || !this.options.max) {\n                    return false;\n                }\n                return getDate(newStart) <= getDate(this.options.min) || getDate(newEnd) >= getDate(this.options.max);\n            },\n            _horizontalSlots: function (selection, ranges, multiple, reverse) {\n                var method = reverse ? 'leftSlot' : 'rightSlot';\n                var horizontalRange = {\n                    startSlot: ranges[0].start,\n                    endSlot: ranges[ranges.length - 1].end\n                };\n                var group = this.groups[selection.groupIndex];\n                var isVertical = this._isVerticallyGrouped();\n                if (!multiple) {\n                    var slot = this._normalizeHorizontalSelection(selection, ranges, reverse);\n                    if (slot) {\n                        horizontalRange.startSlot = horizontalRange.endSlot = slot;\n                    }\n                }\n                if (this._isGroupedByDate() && !multiple) {\n                    var tempSlot = this._changeGroup(selection, reverse);\n                    if (!tempSlot) {\n                        horizontalRange = this._getNextHorizontalRange(group, method, horizontalRange);\n                    } else {\n                        horizontalRange.startSlot = horizontalRange.endSlot = tempSlot;\n                    }\n                } else {\n                    horizontalRange.startSlot = group[method](horizontalRange.startSlot);\n                    horizontalRange.endSlot = group[method](horizontalRange.endSlot);\n                    if (!multiple && !isVertical && (!horizontalRange.startSlot || !horizontalRange.endSlot)) {\n                        horizontalRange.startSlot = horizontalRange.endSlot = this._changeGroup(selection, reverse);\n                    }\n                }\n                var continuousSlot;\n                if ((!horizontalRange.startSlot || !horizontalRange.endSlot) && !this._isGroupedByDate()) {\n                    continuousSlot = this._continuousSlot(selection, ranges, reverse);\n                    continuousSlot = this._changeGroupContinuously(selection, continuousSlot, multiple, reverse);\n                    if (continuousSlot) {\n                        horizontalRange.startSlot = horizontalRange.endSlot = continuousSlot;\n                    }\n                }\n                return horizontalRange;\n            },\n            _getNextHorizontalRange: function (group, method, horizontalRange) {\n                if (!this._isVerticallyGrouped()) {\n                    horizontalRange.startSlot = group[method](horizontalRange.startSlot);\n                    horizontalRange.endSlot = group[method](horizontalRange.endSlot);\n                }\n                return horizontalRange;\n            },\n            _verticalSlots: function (selection, ranges, multiple, reverse) {\n                var group = this.groups[selection.groupIndex];\n                var slot;\n                var verticalRange = {\n                    startSlot: ranges[0].start,\n                    endSlot: ranges[ranges.length - 1].end\n                };\n                if (!multiple) {\n                    slot = this._normalizeVerticalSelection(selection, ranges, reverse);\n                    if (slot) {\n                        verticalRange.startSlot = verticalRange.endSlot = slot;\n                    }\n                }\n                var method = reverse ? 'upSlot' : 'downSlot';\n                verticalRange = this._getNextVerticalRange(group, method, verticalRange, multiple);\n                if (!multiple && this._isVerticallyGrouped() && (!verticalRange.startSlot || !verticalRange.endSlot)) {\n                    if (this._isGroupedByDate()) {\n                        verticalRange.startSlot = verticalRange.endSlot = this._changeDate(selection, slot, reverse);\n                    } else {\n                        verticalRange.startSlot = verticalRange.endSlot = this._changeGroup(selection, reverse);\n                    }\n                }\n                return verticalRange;\n            },\n            _getNextVerticalRange: function (group, method, verticalRange, multiple) {\n                verticalRange.startSlot = group[method](verticalRange.startSlot, multiple);\n                verticalRange.endSlot = group[method](verticalRange.endSlot, multiple);\n                return verticalRange;\n            },\n            _normalizeHorizontalSelection: function () {\n                return null;\n            },\n            _normalizeVerticalSelection: function (selection, ranges, reverse) {\n                var slot;\n                if (reverse) {\n                    slot = ranges[0].start;\n                } else {\n                    slot = ranges[ranges.length - 1].end;\n                }\n                return slot;\n            },\n            _continuousSlot: function () {\n                return null;\n            },\n            _footer: function () {\n                var that = this;\n                var options = that.options;\n                if (that._isMobile()) {\n                    var html = '<div class=\"k-header k-scheduler-footer\">';\n                    html += '<span class=\"k-state-default k-scheduler-today\"><a href=\"#\" class=\"k-link\">';\n                    html += options.messages.today + '</a></span>';\n                    html += '</div>';\n                    that.footer = $(html).appendTo(that.element);\n                }\n                if (that.footer) {\n                    that.footer.on('click' + NS, '.k-scheduler-today', function (e) {\n                        e.preventDefault();\n                        var timezone = that.options.timezone;\n                        var action = 'today';\n                        var currentDate = new Date();\n                        var date;\n                        if (timezone) {\n                            var timezoneOffset = kendo.timezone.offset(currentDate, timezone);\n                            date = kendo.timezone.convert(currentDate, currentDate.getTimezoneOffset(), timezoneOffset);\n                        } else {\n                            date = currentDate;\n                        }\n                        that.trigger('navigate', {\n                            view: that.name || options.name,\n                            action: action,\n                            date: date\n                        });\n                    });\n                }\n            },\n            constrainSelection: function (selection) {\n                var group = this.groups[0];\n                var slot;\n                if (!this.inRange(selection)) {\n                    slot = group.firstSlot();\n                    selection.isAllDay = slot.isDaySlot;\n                    selection.start = slot.startDate();\n                    selection.end = slot.endDate();\n                } else {\n                    if (!group.daySlotCollectionCount()) {\n                        selection.isAllDay = false;\n                    } else if (!group.timeSlotCollectionCount()) {\n                        selection.isAllDay = true;\n                    }\n                }\n                if (!this.groups[selection.groupIndex]) {\n                    selection.groupIndex = 0;\n                }\n            },\n            move: function (selection, key, shift) {\n                var handled = false;\n                var group = this.groups[selection.groupIndex];\n                var verticalByDate = this._isGroupedByDate() && this._isVerticallyGrouped();\n                if (!group.timeSlotCollectionCount()) {\n                    selection.isAllDay = true;\n                }\n                var ranges = group.ranges(selection.start, selection.end, selection.isAllDay, false);\n                var startSlot, endSlot, reverse, slots;\n                if (key === keys.DOWN || key === keys.UP) {\n                    handled = true;\n                    reverse = key === keys.UP;\n                    this._updateDirection(selection, ranges, shift, reverse, true);\n                    slots = this._verticalSlots(selection, ranges, shift, reverse);\n                    if (!slots.startSlot && !shift && this._changeViewPeriod(selection, reverse, !verticalByDate)) {\n                        return handled;\n                    }\n                } else if (key === keys.LEFT || key === keys.RIGHT) {\n                    handled = true;\n                    reverse = key === keys.LEFT;\n                    this._updateDirection(selection, ranges, shift, reverse, false);\n                    slots = this._horizontalSlots(selection, ranges, shift, reverse);\n                    if (!slots.startSlot && !shift && this._changeViewPeriod(selection, reverse, verticalByDate)) {\n                        return handled;\n                    }\n                }\n                if (handled) {\n                    startSlot = slots.startSlot;\n                    endSlot = slots.endSlot;\n                    if (shift) {\n                        var backward = selection.backward;\n                        if (backward && startSlot) {\n                            selection.start = startSlot.startDate();\n                        } else if (!backward && endSlot) {\n                            selection.end = endSlot.endDate();\n                        }\n                    } else if (startSlot && endSlot) {\n                        selection.isAllDay = startSlot.isDaySlot;\n                        selection.start = startSlot.startDate();\n                        selection.end = endSlot.endDate();\n                    }\n                    selection.events = [];\n                }\n                return handled;\n            },\n            moveToEventInGroup: function (group, slot, selectedEvents, prev) {\n                var events = group._continuousEvents || [];\n                var found, event;\n                var pad = prev ? -1 : 1;\n                var length = events.length;\n                var idx = prev ? length - 1 : 0;\n                if (selectedEvents.length) {\n                    var lastSelected = selectedEvents[selectedEvents.length - 1];\n                    for (var i = 0; i < events.length; i++) {\n                        if (events[i].uid === lastSelected) {\n                            idx = i + pad;\n                        }\n                    }\n                }\n                while (idx < length && idx > -1) {\n                    event = events[idx];\n                    if (!prev && event.start.startDate() >= slot.startDate() || prev && event.start.startDate() <= slot.startDate()) {\n                        if (event && $.inArray(event.uid, selectedEvents) === -1) {\n                            found = !!event;\n                            break;\n                        }\n                    }\n                    idx += pad;\n                }\n                return event;\n            },\n            moveToEvent: function (selection, prev) {\n                var groupIndex = selection.groupIndex;\n                var group = this.groups[groupIndex];\n                var slot = group.ranges(selection.start, selection.end, selection.isAllDay, false)[0].start;\n                var length = this.groups.length;\n                var pad = prev ? -1 : 1;\n                var events = selection.events;\n                var event;\n                if (this._isGroupedByDate()) {\n                    var allEvents = this._getAllEvents();\n                    var uniqueAllEvents = this._getUniqueEvents(allEvents);\n                    var sortedEvents = this._getSortedEvents(uniqueAllEvents);\n                    if (events.length === 0) {\n                        var eventIndex = this._getNextEventIndexBySlot(slot, sortedEvents, groupIndex);\n                        if (prev) {\n                            eventIndex--;\n                        }\n                        event = sortedEvents[eventIndex];\n                    } else {\n                        var idx = this._getStartIdx(events, sortedEvents);\n                        while (idx < sortedEvents.length && idx > -1) {\n                            if (events.length > 0) {\n                                slot = this._getSelectedSlot(slot, sortedEvents, event, idx, pad, prev);\n                            }\n                            if (!slot) {\n                                break;\n                            }\n                            if (!prev && sortedEvents[idx].start.startDate() >= slot.startDate() || prev && sortedEvents[idx].start.startDate() <= slot.startDate()) {\n                                if (events[0] != sortedEvents[idx].uid) {\n                                    event = sortedEvents[idx];\n                                    break;\n                                }\n                            }\n                            idx += pad;\n                        }\n                    }\n                } else {\n                    while (groupIndex < length && groupIndex > -1) {\n                        event = this.moveToEventInGroup(group, slot, events, prev);\n                        groupIndex += pad;\n                        group = this.groups[groupIndex];\n                        if (!group || event) {\n                            break;\n                        }\n                        events = [];\n                        if (prev) {\n                            slot = group.lastSlot();\n                        } else {\n                            slot = group.firstSlot(true);\n                        }\n                    }\n                }\n                if (event) {\n                    selection.events = [event.uid];\n                    selection.start = event.start.startDate();\n                    selection.end = event.end.endDate();\n                    selection.isAllDay = event.start.isDaySlot;\n                    selection.groupIndex = event.start.groupIndex;\n                }\n                return !!event;\n            },\n            current: function (candidate) {\n                if (candidate !== undefined) {\n                    this._current = candidate;\n                    if (this.content.has(candidate)) {\n                        this._scrollTo(candidate, this.content[0]);\n                    }\n                } else {\n                    return this._current;\n                }\n            },\n            select: function (selection) {\n                this.clearSelection();\n                if (!this._selectEvents(selection)) {\n                    this._selectSlots(selection);\n                }\n            },\n            _getNextEventIndexBySlot: function (slot, sortedEvents, groupIndex) {\n                var tempIndex = 0;\n                var slotStartDate = kendo.date.getDate(slot.startDate());\n                for (var i = 0; i < sortedEvents.length; i++) {\n                    var eventStartDate = kendo.date.getDate(sortedEvents[i].start.startDate());\n                    if (slotStartDate > eventStartDate) {\n                        tempIndex++;\n                        continue;\n                    }\n                    if (slotStartDate.getTime() === eventStartDate.getTime() && groupIndex > sortedEvents[i].start.groupIndex) {\n                        tempIndex++;\n                        continue;\n                    }\n                    if (slotStartDate.getTime() === eventStartDate.getTime() && groupIndex >= sortedEvents[i].start.groupIndex && slot.startDate() > sortedEvents[i].start.startDate()) {\n                        tempIndex++;\n                        continue;\n                    }\n                    break;\n                }\n                return tempIndex;\n            },\n            _getSelectedSlot: function (slot, sortedEvents, event, idx, pad, prev) {\n                if (sortedEvents[idx + pad] && sortedEvents[idx].start.groupIndex !== sortedEvents[idx + pad].start.groupIndex) {\n                    var groupIndex = sortedEvents[idx + pad].start.groupIndex;\n                    var group = this.groups[groupIndex];\n                    if (!group || event) {\n                        slot = null;\n                    }\n                    if (prev) {\n                        slot = group.lastSlot();\n                    } else {\n                        slot = group.firstSlot(true);\n                    }\n                }\n                return slot;\n            },\n            _getStartIdx: function (events, sortedEvents) {\n                var selectedEventIndex = 0;\n                $.each(sortedEvents, function () {\n                    if (this.uid === events[0]) {\n                        return false;\n                    }\n                    selectedEventIndex++;\n                });\n                return selectedEventIndex;\n            },\n            _getAllEvents: function () {\n                var allEvents = [];\n                var groups = this.groups;\n                for (var idx = 0; idx < groups.length; idx++) {\n                    if (groups[idx]._continuousEvents) {\n                        allEvents = allEvents.concat(groups[idx]._continuousEvents);\n                    }\n                }\n                return allEvents;\n            },\n            _getUniqueEvents: function (allEvents) {\n                var uniqueAllEvents = [];\n                for (var i = 0; i < allEvents.length; i++) {\n                    var exists = false;\n                    for (var j = 0; j < uniqueAllEvents.length; j++) {\n                        if (allEvents[i].uid === uniqueAllEvents[j].uid) {\n                            exists = true;\n                            break;\n                        }\n                    }\n                    if (!exists) {\n                        uniqueAllEvents.push(allEvents[i]);\n                    }\n                }\n                return uniqueAllEvents;\n            },\n            _getSortedEvents: function (uniqueAllEvents) {\n                return uniqueAllEvents.sort(function (first, second) {\n                    var firstStartDate = first.start.startDate();\n                    var secondStartDate = second.start.startDate();\n                    var result = kendo.date.getDate(firstStartDate) - kendo.date.getDate(secondStartDate);\n                    if (result === 0) {\n                        result = first.start.groupIndex - second.start.groupIndex;\n                    }\n                    if (result === 0) {\n                        result = firstStartDate.getTime() - secondStartDate.getTime();\n                    }\n                    if (result === 0) {\n                        if (first.start.isDaySlot && !second.start.isDaySlot) {\n                            result = -1;\n                        }\n                        if (!first.start.isDaySlot && second.start.isDaySlot) {\n                            result = 1;\n                        }\n                    }\n                    if (result === 0) {\n                        result = $(first.element).index() - $(second.element).index();\n                    }\n                    return result;\n                });\n            },\n            _selectSlots: function (selection) {\n                var isAllDay = selection.isAllDay;\n                var group = this.groups[selection.groupIndex];\n                if (!group.timeSlotCollectionCount()) {\n                    isAllDay = true;\n                }\n                this._selectedSlots = [];\n                var ranges = group.ranges(selection.start, selection.end, isAllDay, false);\n                var element;\n                var slot;\n                for (var rangeIndex = 0; rangeIndex < ranges.length; rangeIndex++) {\n                    var range = ranges[rangeIndex];\n                    var collection = range.collection;\n                    for (var slotIndex = range.start.index; slotIndex <= range.end.index; slotIndex++) {\n                        slot = collection.at(slotIndex);\n                        element = slot.element;\n                        element.setAttribute('aria-selected', true);\n                        addSelectedState(element);\n                        this._selectedSlots.push({\n                            start: slot.startDate(),\n                            end: slot.endDate(),\n                            element: element\n                        });\n                    }\n                }\n                if (selection.backward) {\n                    element = ranges[0].start.element;\n                }\n                this.current(element);\n            },\n            _selectEvents: function (selection) {\n                var found = false;\n                var events = selection.events;\n                var groupEvents = this._getAllEvents();\n                var idx, groupEvent, length = groupEvents.length;\n                if (!events[0] || !groupEvents[0]) {\n                    return found;\n                }\n                var result = $();\n                selection.events = [];\n                for (idx = 0; idx < length; idx++) {\n                    if ($.inArray(groupEvents[idx].uid, events) > -1) {\n                        groupEvent = groupEvents[idx];\n                        result = result.add(groupEvent.element);\n                        if (selection.events.indexOf(groupEvent.uid) === -1) {\n                            selection.events.push(groupEvent.uid);\n                        }\n                    }\n                }\n                if (result[0]) {\n                    result.addClass('k-state-selected').attr('aria-selected', true);\n                    this.current(result.last()[0]);\n                    this._selectedSlots = [];\n                    found = true;\n                }\n                return found;\n            },\n            inRange: function (options) {\n                var startDate = this.startDate();\n                var endDate = kendo.date.addDays(this.endDate(), 1);\n                var start = options.start;\n                var end = options.end;\n                return startDate <= start && start < endDate && startDate < end && end <= endDate;\n            },\n            _resourceValue: function (resource, item) {\n                if (resource.valuePrimitive) {\n                    item = kendo.getter(resource.dataValueField)(item);\n                }\n                return item;\n            },\n            _resourceBySlot: function (slot) {\n                var resources = this.groupedResources;\n                var result = {};\n                if (resources.length) {\n                    var resourceIndex = slot.groupIndex;\n                    for (var idx = resources.length - 1; idx >= 0; idx--) {\n                        var resource = resources[idx];\n                        var value = this._resourceValue(resource, resource.dataSource.view()[resourceIndex % resource.dataSource.total()]);\n                        if (resource.multiple) {\n                            value = [value];\n                        }\n                        var setter = kendo.setter(resource.field);\n                        setter(result, value);\n                        resourceIndex = Math.floor(resourceIndex / resource.dataSource.total());\n                    }\n                }\n                return result;\n            },\n            _createResizeHint: function (left, top, width, height) {\n                return $(HINT).css({\n                    left: left,\n                    top: top,\n                    width: width,\n                    height: height\n                });\n            },\n            _removeResizeHint: function () {\n                this._resizeHint.remove();\n                this._resizeHint = $();\n            },\n            _removeMoveHint: function (uid) {\n                if (uid) {\n                    this._moveHint.filter('[data-uid=\\'' + uid + '\\']').remove();\n                    this._moveHint = this._moveHint.filter('[data-uid!=\\'' + uid + '\\']');\n                } else {\n                    this._moveHint.remove();\n                    this._moveHint = $();\n                }\n            },\n            _scrollTo: function (element, container) {\n                var elementOffset = element.offsetTop, elementOffsetDir = element.offsetHeight, containerScroll = container.scrollTop, containerOffsetDir = container.clientHeight, bottomDistance = elementOffset + elementOffsetDir, result = 0;\n                if (containerScroll > elementOffset) {\n                    result = elementOffset;\n                } else if (bottomDistance > containerScroll + containerOffsetDir) {\n                    if (elementOffsetDir <= containerOffsetDir) {\n                        result = bottomDistance - containerOffsetDir;\n                    } else {\n                        result = elementOffset;\n                    }\n                } else {\n                    result = containerScroll;\n                }\n                container.scrollTop = result;\n            },\n            _inverseEventColor: function (element) {\n                var eventColor = element.css('color');\n                var eventColorIsDark = new Color(eventColor).isDark();\n                var eventBackground = element.css('background-color');\n                var eventBackgroundIsDark = new Color(eventBackground).isDark();\n                if (eventColorIsDark == eventBackgroundIsDark) {\n                    element.addClass(INVERSE_COLOR_CLASS);\n                }\n            },\n            _eventTmpl: function (template, wrapper) {\n                var options = this.options, settings = $.extend({}, kendo.Template, options.templateSettings), paramName = settings.paramName, html = '', type = typeof template, state = {\n                        storage: {},\n                        count: 0\n                    };\n                if (type === 'function') {\n                    state.storage['tmpl' + state.count] = template;\n                    html += '#=this.tmpl' + state.count + '(' + paramName + ')#';\n                    state.count++;\n                } else if (type === 'string') {\n                    html += template;\n                }\n                var tmpl = kendo.template(kendo.format(wrapper, html), settings);\n                if (state.count > 0) {\n                    tmpl = $.proxy(tmpl, state.storage);\n                }\n                return tmpl;\n            },\n            eventResources: function (event) {\n                var resources = [], options = this.options;\n                if (!options.resources) {\n                    return resources;\n                }\n                for (var idx = 0; idx < options.resources.length; idx++) {\n                    var resource = options.resources[idx];\n                    var field = resource.field;\n                    var eventResources = kendo.getter(field)(event);\n                    if (eventResources == null) {\n                        continue;\n                    }\n                    if (!resource.multiple) {\n                        eventResources = [eventResources];\n                    }\n                    var data = resource.dataSource.view();\n                    for (var resourceIndex = 0; resourceIndex < eventResources.length; resourceIndex++) {\n                        var eventResource = null;\n                        var value = eventResources[resourceIndex];\n                        if (!resource.valuePrimitive) {\n                            value = kendo.getter(resource.dataValueField)(value);\n                        }\n                        for (var dataIndex = 0; dataIndex < data.length; dataIndex++) {\n                            if (data[dataIndex].get(resource.dataValueField) == value) {\n                                eventResource = data[dataIndex];\n                                break;\n                            }\n                        }\n                        if (eventResource !== null) {\n                            var resourceColor = kendo.getter(resource.dataColorField)(eventResource);\n                            resources.push({\n                                field: resource.field,\n                                title: resource.title,\n                                name: resource.name,\n                                text: kendo.getter(resource.dataTextField)(eventResource),\n                                value: value,\n                                color: resourceColor\n                            });\n                        }\n                    }\n                }\n                return resources;\n            },\n            createLayout: function (layout) {\n                var allDayIndex = -1;\n                if (!layout.rows) {\n                    layout.rows = [];\n                }\n                for (var idx = 0; idx < layout.rows.length; idx++) {\n                    if (layout.rows[idx].allDay) {\n                        allDayIndex = idx;\n                        break;\n                    }\n                }\n                var allDaySlot = layout.rows[allDayIndex];\n                if (allDayIndex >= 0) {\n                    layout.rows.splice(allDayIndex, 1);\n                }\n                var columnLevels = this.columnLevels = levels(layout, 'columns');\n                var rowLevels = this.rowLevels = levels(layout, 'rows');\n                this.table = $('<table ' + cellspacing() + ' class=\"k-scheduler-layout k-scheduler-' + this.name + 'view\"><tbody></tbody></table>');\n                var rowCount = rowLevels[rowLevels.length - 1].length;\n                this.table.find('tbody:first').append(this._topSection(columnLevels, allDaySlot, rowCount));\n                this.table.find('tbody:first').append(this._bottomSection(columnLevels, rowLevels, rowCount));\n                this.element.append(this.table);\n                if (this._isMobile() && columnLevels.length > 1 && this._groupOrientation() === 'horizontal' && kendo._outerWidth($(window)) < MIN_HORIZONTAL_SCROLL_SIZE) {\n                    this.table.find('.k-scheduler-content .k-scheduler-table').width(columnLevels[columnLevels.length - 2].length * 100 + '%');\n                    this.table.find('.k-scheduler-header .k-scheduler-table').width(columnLevels[columnLevels.length - 2].length * 100 + '%');\n                }\n                this._scroller();\n            },\n            refreshLayout: function () {\n                var that = this, toolbar = that.element.find('>.k-scheduler-toolbar'), height = that.element.innerHeight(), scrollbar = this._scrollbar, headerHeight = 0, paddingDirection = this._isRtl ? 'left' : 'right';\n                for (var idx = 0; idx < toolbar.length; idx++) {\n                    height -= outerHeight(toolbar.eq(idx));\n                }\n                if (that.datesHeader) {\n                    headerHeight = outerHeight(that.datesHeader);\n                }\n                if (that.timesHeader && outerHeight(that.timesHeader) > headerHeight) {\n                    headerHeight = outerHeight(that.timesHeader);\n                }\n                if (that.datesHeader && that.timesHeader) {\n                    var datesHeaderRows = that.datesHeader.find('table:first tr');\n                    that.timesHeader.find('tr').height(function (index) {\n                        $(this).height(datesHeaderRows.eq(index).height());\n                    });\n                }\n                if (headerHeight) {\n                    height -= headerHeight;\n                }\n                if (that.footer) {\n                    height -= outerHeight(that.footer);\n                }\n                var isSchedulerHeightSet = function (el) {\n                    var initialHeight, newHeight;\n                    if (el[0].style.height) {\n                        return true;\n                    } else {\n                        initialHeight = el.height();\n                    }\n                    el.height('auto');\n                    newHeight = el.height();\n                    if (initialHeight != newHeight) {\n                        el.height('');\n                        return true;\n                    }\n                    el.height('');\n                    return false;\n                };\n                var contentDiv = that.content[0], scrollbarWidth = !kendo.support.kineticScrollNeeded ? scrollbar : 0;\n                if (isSchedulerHeightSet(that.element)) {\n                    if (height > scrollbar * 2) {\n                        that.content.height(height);\n                    } else {\n                        that.content.height(scrollbar * 2 + 1);\n                    }\n                    that.times.height(contentDiv.clientHeight);\n                    var timesTable = that.times.find('table');\n                    if (timesTable.length) {\n                        timesTable.height(that.content.find('table')[0].clientHeight);\n                    }\n                }\n                if (contentDiv.offsetWidth - contentDiv.clientWidth > 0) {\n                    that.table.addClass('k-scrollbar-v');\n                    that.datesHeader.css('padding-' + paddingDirection, scrollbarWidth - parseInt(that.datesHeader.children().css('border-' + paddingDirection + '-width'), 10));\n                } else {\n                    that.datesHeader.css('padding-' + paddingDirection, '');\n                }\n                if (contentDiv.offsetHeight - contentDiv.clientHeight > 0 || contentDiv.clientHeight > that.content.children('.k-scheduler-table').height()) {\n                    that.table.addClass('k-scrollbar-h');\n                } else {\n                    that.table.removeClass('k-scrollbar-h');\n                }\n            },\n            _topSection: function (columnLevels, allDaySlot, rowCount) {\n                var columnCount = columnLevels[columnLevels.length - 1].length;\n                this.timesHeader = timesHeader(columnLevels.length, allDaySlot, rowCount);\n                this.datesHeader = datesHeader(columnLevels, columnCount, allDaySlot);\n                var thElm = '<tr ' + (this._isMobile() ? 'class=\\'k-mobile-header\\'' : '') + '>';\n                return $(thElm).append(this.timesHeader.add(this.datesHeader).wrap('<td>').parent());\n            },\n            _bottomSection: function (columnLevels, rowLevels, rowCount) {\n                this.times = times(rowLevels, rowCount, this._isMobile());\n                this.content = content(columnLevels[columnLevels.length - 1], rowLevels[rowLevels.length - 1]);\n                return $('<tr>').append(this.times.add(this.content).wrap('<td>').parent());\n            },\n            _scroller: function () {\n                var that = this;\n                this.content.bind('scroll' + NS, function () {\n                    that.datesHeader.find('>.k-scheduler-header-wrap').scrollLeft(this.scrollLeft);\n                    that.times.scrollTop(this.scrollTop);\n                });\n                var touchScroller = kendo.touchScroller(this.content, {\n                    avoidScrolling: function (e) {\n                        return $(e.event.target).closest('.k-event.k-event-active').length > 0;\n                    }\n                });\n                if (touchScroller && touchScroller.movable) {\n                    this._touchScroller = touchScroller;\n                    this.content = touchScroller.scrollElement;\n                    touchScroller.movable.bind('change', function (e) {\n                        that.datesHeader.find('>.k-scheduler-header-wrap').scrollLeft(-e.sender.x);\n                        that.times.scrollTop(-e.sender.y);\n                    });\n                }\n            },\n            _resourcesForGroups: function () {\n                var result = [];\n                var groups = this.options.group;\n                var resources = this.options.resources;\n                groups = groups && groups.resources ? groups.resources : [];\n                if (resources && groups.length) {\n                    for (var idx = 0, length = resources.length; idx < length; idx++) {\n                        for (var groupIdx = 0, groupLength = groups.length; groupIdx < groupLength; groupIdx++) {\n                            if (resources[idx].name === groups[groupIdx]) {\n                                result.push(resources[idx]);\n                            }\n                        }\n                    }\n                }\n                this.groupedResources = result;\n            },\n            _createDateLayout: function (dates, inner, times) {\n                return createDateLayoutConfiguration('rows', dates, inner, times);\n            },\n            _createColumnsLayout: function (resources, inner, template, dates, times) {\n                return createLayoutConfiguration('columns', resources, inner, template, dates, times);\n            },\n            _groupOrientation: function () {\n                var groups = this.options.group;\n                return groups && groups.resources ? groups.orientation : 'horizontal';\n            },\n            _isGroupedByDate: function () {\n                return this.options.group && this.options.group.date;\n            },\n            _isVerticallyGrouped: function () {\n                return this.groupedResources.length && this._groupOrientation() === 'vertical';\n            },\n            _createRowsLayout: function (resources, inner, template, dates) {\n                return createLayoutConfiguration('rows', resources, inner, template, dates);\n            },\n            selectionByElement: function () {\n                return null;\n            },\n            clearSelection: function () {\n                this.content.find('.k-state-selected').removeAttr('id').attr('aria-selected', false).removeClass('k-state-selected');\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(this);\n                if (that.table) {\n                    kendo.destroy(that.table);\n                    that.table.remove();\n                }\n                if (that.footer) {\n                    kendo.destroy(that.footer);\n                    that.footer.remove();\n                }\n                that.groups = null;\n                that.table = null;\n                that.content = null;\n                that.times = null;\n                that.datesHeader = null;\n                that.timesHeader = null;\n                that.footer = null;\n                that._resizeHint = null;\n                that._moveHint = null;\n            },\n            calendarInfo: function () {\n                return kendo.getCulture().calendars.standard;\n            },\n            prevGroupSlot: function (date, groupIndex, isDay) {\n                var collection;\n                var group = this.groups[groupIndex];\n                var slot = group.ranges(date, date, isDay, false)[0].start;\n                if (groupIndex <= 0) {\n                    return;\n                }\n                if (this._isGroupedByDate()) {\n                    return slot;\n                }\n                if (this._isVerticallyGrouped()) {\n                    if (!group.timeSlotCollectionCount()) {\n                        collection = group._collection(group.daySlotCollectionCount() - 1, true);\n                        return collection.at(slot.index);\n                    } else {\n                        collection = group._collection(isDay ? slot.index : slot.collectionIndex, false);\n                        return collection.last();\n                    }\n                } else {\n                    if (!group.timeSlotCollectionCount()) {\n                        collection = group._collection(slot.collectionIndex, true);\n                        return collection.last();\n                    } else {\n                        collection = group._collection(isDay ? 0 : group.timeSlotCollectionCount() - 1, isDay);\n                        return isDay ? collection.last() : collection.at(slot.index);\n                    }\n                }\n            },\n            nextGroupSlot: function (date, groupIndex, isDay) {\n                var collection;\n                var group = this.groups[groupIndex];\n                var slot = group.ranges(date, date, isDay, false)[0].start;\n                var daySlotCollectionCount;\n                if (groupIndex >= this.groups.length - 1) {\n                    return;\n                }\n                if (this._isGroupedByDate()) {\n                    return slot;\n                }\n                if (this._isVerticallyGrouped()) {\n                    if (!group.timeSlotCollectionCount()) {\n                        collection = group._collection(0, true);\n                        return collection.at(slot.index);\n                    } else {\n                        daySlotCollectionCount = group.daySlotCollectionCount();\n                        collection = group._collection(daySlotCollectionCount ? 0 : slot.collectionIndex, daySlotCollectionCount);\n                        return isDay ? collection.first() : collection.at(slot.collectionIndex);\n                    }\n                } else {\n                    if (!group.timeSlotCollectionCount()) {\n                        collection = group._collection(slot.collectionIndex, true);\n                        return collection.first();\n                    } else {\n                        collection = group._collection(0, isDay);\n                        return isDay ? collection.first() : collection.at(slot.index);\n                    }\n                }\n            },\n            _eventOptionsForMove: function () {\n                return {};\n            },\n            _updateEventForResize: function () {\n                return;\n            },\n            _updateEventForSelection: function (event) {\n                return event;\n            }\n        });\n        function collidingEvents(elements, start, end) {\n            var idx, index, startIndex, overlaps, endIndex;\n            for (idx = elements.length - 1; idx >= 0; idx--) {\n                index = rangeIndex(elements[idx]);\n                startIndex = index.start;\n                endIndex = index.end;\n                overlaps = startIndex <= start && endIndex >= start;\n                if (overlaps || startIndex >= start && endIndex <= end || start <= startIndex && end >= startIndex) {\n                    if (startIndex < start) {\n                        start = startIndex;\n                    }\n                    if (endIndex > end) {\n                        end = endIndex;\n                    }\n                }\n            }\n            return eventsForSlot(elements, start, end);\n        }\n        function rangeIndex(eventElement) {\n            return {\n                start: eventElement.start,\n                end: eventElement.end\n            };\n        }\n        function eventsForSlot(elements, slotStart, slotEnd) {\n            var events = [];\n            for (var idx = 0; idx < elements.length; idx++) {\n                var event = rangeIndex(elements[idx]);\n                if (event.start < slotStart && event.end > slotStart || event.start >= slotStart && event.end <= slotEnd) {\n                    events.push(elements[idx]);\n                }\n            }\n            return events;\n        }\n        function createColumns(eventElements) {\n            return _createColumns(eventElements);\n        }\n        function createRows(eventElements) {\n            return _createColumns(eventElements);\n        }\n        var Color = function (value) {\n            var color = this, formats = Color.formats, re, processor, parts, i, channels;\n            if (arguments.length === 1) {\n                value = color.resolveColor(value);\n                for (i = 0; i < formats.length; i++) {\n                    re = formats[i].re;\n                    processor = formats[i].process;\n                    parts = re.exec(value);\n                    if (parts) {\n                        channels = processor(parts);\n                        color.r = channels[0];\n                        color.g = channels[1];\n                        color.b = channels[2];\n                    }\n                }\n            } else {\n                color.r = arguments[0];\n                color.g = arguments[1];\n                color.b = arguments[2];\n            }\n            color.r = color.normalizeByte(color.r);\n            color.g = color.normalizeByte(color.g);\n            color.b = color.normalizeByte(color.b);\n        };\n        Color.prototype = {\n            resolveColor: function (value) {\n                value = value || '#000';\n                if (value.charAt(0) == '#') {\n                    value = value.substr(1, 6);\n                }\n                value = value.replace(/ /g, '');\n                value = value.toLowerCase();\n                value = Color.namedColors[value] || value;\n                return value;\n            },\n            normalizeByte: function (value) {\n                return value < 0 || isNaN(value) ? 0 : value > 255 ? 255 : value;\n            },\n            percBrightness: function () {\n                var color = this;\n                return math.sqrt(0.241 * color.r * color.r + 0.691 * color.g * color.g + 0.068 * color.b * color.b);\n            },\n            isDark: function () {\n                var color = this;\n                var brightnessValue = color.percBrightness();\n                return brightnessValue < 180;\n            }\n        };\n        Color.formats = [\n            {\n                re: /^rgb\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3})\\)$/,\n                process: function (parts) {\n                    return [\n                        parseInt(parts[1], 10),\n                        parseInt(parts[2], 10),\n                        parseInt(parts[3], 10)\n                    ];\n                }\n            },\n            {\n                re: /^(\\w{2})(\\w{2})(\\w{2})$/,\n                process: function (parts) {\n                    return [\n                        parseInt(parts[1], 16),\n                        parseInt(parts[2], 16),\n                        parseInt(parts[3], 16)\n                    ];\n                }\n            },\n            {\n                re: /^(\\w{1})(\\w{1})(\\w{1})$/,\n                process: function (parts) {\n                    return [\n                        parseInt(parts[1] + parts[1], 16),\n                        parseInt(parts[2] + parts[2], 16),\n                        parseInt(parts[3] + parts[3], 16)\n                    ];\n                }\n            }\n        ];\n        Color.namedColors = {\n            aqua: '00ffff',\n            azure: 'f0ffff',\n            beige: 'f5f5dc',\n            black: '000000',\n            blue: '0000ff',\n            brown: 'a52a2a',\n            coral: 'ff7f50',\n            cyan: '00ffff',\n            darkblue: '00008b',\n            darkcyan: '008b8b',\n            darkgray: 'a9a9a9',\n            darkgreen: '006400',\n            darkorange: 'ff8c00',\n            darkred: '8b0000',\n            dimgray: '696969',\n            fuchsia: 'ff00ff',\n            gold: 'ffd700',\n            goldenrod: 'daa520',\n            gray: '808080',\n            green: '008000',\n            greenyellow: 'adff2f',\n            indigo: '4b0082',\n            ivory: 'fffff0',\n            khaki: 'f0e68c',\n            lightblue: 'add8e6',\n            lightgrey: 'd3d3d3',\n            lightgreen: '90ee90',\n            lightpink: 'ffb6c1',\n            lightyellow: 'ffffe0',\n            lime: '00ff00',\n            limegreen: '32cd32',\n            linen: 'faf0e6',\n            magenta: 'ff00ff',\n            maroon: '800000',\n            mediumblue: '0000cd',\n            navy: '000080',\n            olive: '808000',\n            orange: 'ffa500',\n            orangered: 'ff4500',\n            orchid: 'da70d6',\n            pink: 'ffc0cb',\n            plum: 'dda0dd',\n            purple: '800080',\n            red: 'ff0000',\n            royalblue: '4169e1',\n            salmon: 'fa8072',\n            silver: 'c0c0c0',\n            skyblue: '87ceeb',\n            slateblue: '6a5acd',\n            slategray: '708090',\n            snow: 'fffafa',\n            steelblue: '4682b4',\n            tan: 'd2b48c',\n            teal: '008080',\n            tomato: 'ff6347',\n            turquoise: '40e0d0',\n            violet: 'ee82ee',\n            wheat: 'f5deb3',\n            white: 'ffffff',\n            whitesmoke: 'f5f5f5',\n            yellow: 'ffff00',\n            yellowgreen: '9acd32'\n        };\n        function _createColumns(eventElements) {\n            var columns = [];\n            for (var idx = 0; idx < eventElements.length; idx++) {\n                var event = eventElements[idx];\n                var eventRange = rangeIndex(event);\n                var column = null;\n                for (var j = 0, columnLength = columns.length; j < columnLength; j++) {\n                    var endOverlaps = eventRange.start > columns[j].end;\n                    if (eventRange.start < columns[j].start || endOverlaps) {\n                        column = columns[j];\n                        if (column.end < eventRange.end) {\n                            column.end = eventRange.end;\n                        }\n                        break;\n                    }\n                }\n                if (!column) {\n                    column = {\n                        start: eventRange.start,\n                        end: eventRange.end,\n                        events: []\n                    };\n                    columns.push(column);\n                }\n                column.events.push(event);\n            }\n            return columns;\n        }\n        function createDateLayoutConfiguration(name, dates, inner, times) {\n            var configuration = [];\n            $.each(dates, function (index, item) {\n                var className = item.className ? 'k-slot-cell ' + item.className : 'k-slot-cell';\n                var obj = {\n                    text: item.text,\n                    className: className\n                };\n                if (times && !item.minorTicks) {\n                    obj[name] = createDateLayoutConfiguration(name, item.columns, inner, times);\n                } else {\n                    obj[name] = inner;\n                }\n                configuration.push(obj);\n            });\n            return configuration;\n        }\n        function createLayoutConfiguration(name, resources, inner, template, dates, times) {\n            var resource = resources[0];\n            var configuration = [];\n            if (resource) {\n                if (dates && inner) {\n                    $.each(dates, function (index, item) {\n                        if (times && !item.minorTicks) {\n                            item[name] = createLayoutConfiguration(name, resources, item.columns, template, item.columns, times);\n                        } else {\n                            item[name] = createLayoutConfiguration(name, resources, null, template);\n                        }\n                    });\n                    configuration = dates;\n                } else {\n                    var data = resource.dataSource.view();\n                    for (var dataIndex = 0; dataIndex < data.length; dataIndex++) {\n                        var obj = {\n                            text: template({\n                                text: kendo.htmlEncode(kendo.getter(resource.dataTextField)(data[dataIndex])),\n                                color: kendo.getter(resource.dataColorField)(data[dataIndex]),\n                                field: resource.field,\n                                title: resource.title,\n                                name: resource.name,\n                                value: kendo.getter(resource.dataValueField)(data[dataIndex])\n                            }),\n                            className: 'k-slot-cell k-scheduler-group-cell'\n                        };\n                        obj[name] = createLayoutConfiguration(name, resources.slice(1), inner, template);\n                        configuration.push(obj);\n                    }\n                }\n                return configuration;\n            }\n            return inner;\n        }\n        function groupEqFilter(value) {\n            return function (item) {\n                if ($.isArray(item) || item instanceof kendo.data.ObservableArray) {\n                    for (var idx = 0; idx < item.length; idx++) {\n                        if (item[idx] == value) {\n                            return true;\n                        }\n                    }\n                    return false;\n                }\n                return item == value;\n            };\n        }\n        var selectedStateRegExp = /\\s*k-state-selected/;\n        function addSelectedState(cell) {\n            cell.className = cell.className.replace(selectedStateRegExp, '') + ' k-state-selected';\n        }\n        $.extend(ui.SchedulerView, {\n            createColumns: createColumns,\n            createRows: createRows,\n            rangeIndex: rangeIndex,\n            collidingEvents: collidingEvents,\n            groupEqFilter: groupEqFilter\n        });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}