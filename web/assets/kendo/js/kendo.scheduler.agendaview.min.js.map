{"version": 3, "sources": ["kendo.scheduler.agendaview.js"], "names": ["f", "define", "$", "rowSpan", "tasks", "idx", "length", "result", "items", "resourceValue", "resource", "item", "valuePrimitive", "kendo", "getter", "dataValueField", "flattenTaskGroups", "groups", "flattenGroup", "concat", "shift", "push", "apply", "call", "window", "ui", "NS", "EVENT_WRAPPER_FORMAT", "EVENT_DATE_TEMPLATE", "EVENT_GROUP_TEMPLATE", "AgendaGroupedView", "Class", "extend", "init", "view", "this", "_view", "_getColumns", "groupHeaders", "columns", "_getGroupsInDay", "_getSumOfItemsForDate", "_renderTaskGroupsCells", "headerCells", "taskGroupIndex", "taskIndex", "_renderDateCell", "tableRow", "date", "tasksGroups", "isMobile", "_isMobile", "format", "_dateTemplate", "_renderDates", "_getParents", "parentGroups", "splice", "_getGroupsByDate", "_renderTaskGroups", "table", "parents", "append", "AgendaGroupedByDateView", "columnsWithoutDate", "slice", "_compareDateGroups", "currentGroup", "prevGroup", "index", "text", "tasksGroupIdx", "itemsIdx", "lastItemValue", "i", "groupsInDay", "sumOfItemsForDate", "groupsRowSpanIndex", "_groupTemplate", "value", "className", "sortedArray", "_groupsByDate", "sort", "a", "b", "array", "getTime", "dateExists", "scheduler", "AgendaView", "SchedulerView", "element", "options", "fn", "_<PERSON><PERSON>iew", "_getGroupedView", "editable", "delete", "create", "update", "messages", "title", "_eventTemplate", "_eventTmpl", "eventTemplate", "template", "eventDateTemplate", "eventGroupTemplate", "_timeTemplate", "eventTimeTemplate", "on", "_renderLayout", "name", "_isGroupedByDate", "_mouseenter", "e", "currentTarget", "addClass", "_mouseleave", "removeClass", "_remove", "preventDefault", "trigger", "uid", "closest", "attr", "nextDate", "nextDay", "startDate", "_startDate", "endDate", "_endDate", "previousDate", "previousDay", "addDays", "createLayout", "_layout", "_footer", "resources", "time", "event", "groupedResources", "_tasks", "events", "start", "end", "eventDurationInDays", "task", "day", "isAllDay", "getDate", "Math", "ceil", "MS_PER_DAY", "clone", "head", "Date", "getFullYear", "getMonth", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "tail", "middle", "data", "Query", "field", "dir", "groupBy", "toArray", "today", "tableRows", "showDelete", "destroy", "isToday", "join", "eventResources", "_startTime", "endTime", "render", "content", "find", "empty", "_createGroupConfiguration", "_renderGroups", "_eventsList", "_angularItems", "refreshLayout", "angular", "elements", "map", "dataItem", "parent", "dataIndex", "tmp", "obj", "span", "configuration", "dataSource", "filter", "operator", "groupEqFilter", "indexOf", "dataTextField", "selectionByElement", "cell", "hasClass", "is", "children", "select", "selection", "clearSelection", "row", "eq", "current", "move", "key", "handled", "keys", "UP", "DOWN", "moveToEvent", "constrainSelection", "isInRange", "off", "selectedDateFormat", "selectedShortDateFormat", "selectedMobileDateFormat", "allDay", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,8BAA+B,wBAAyBD,IACjE,WAglBE,MAvkBC,UAAUE,GA8hBP,QAASC,GAAQC,GAAjB,GAEaC,GAASC,EADdC,EAAS,CACb,KAASF,EAAM,EAAGC,EAASF,EAAME,OAAQD,EAAMC,EAAQD,IACnDE,GAAUH,EAAMC,GAAKG,MAAMF,MAE/B,OAAOC,GAEX,QAASE,GAAcC,EAAUC,GAI7B,MAHID,GAASE,iBACTD,EAAOE,EAAMC,OAAOJ,EAASK,gBAAgBJ,IAE1CA,EAEX,QAASK,GAAkBC,GAEvB,IADA,GAAqCN,GAAjCN,EAAM,EAAGC,EAASW,EAAOX,OAAcC,KACpCF,EAAMC,EAAQD,IACjBM,EAAOM,EAAOZ,GACVM,EAAKM,QACLN,EAAOO,EAAaP,EAAKM,QACzBV,EAASA,EAAOY,OAAOR,IAEvBJ,EAASA,EAAOY,OAAOD,EAAaP,EAAKH,OAGjD,OAAOD,GAEX,QAASW,GAAaD,GAElB,IADA,GAAIT,MAAWW,OAAOF,GAASN,EAAOH,EAAMY,QAASb,KAAac,KAAUA,KACrEV,GACCA,EAAKM,OACLI,EAAKC,MAAMd,EAAOG,EAAKM,QAChBN,EAAKH,MACZa,EAAKC,MAAMd,EAAOG,EAAKH,OAEvBa,EAAKE,KAAKhB,EAAQI,GAEtBA,EAAOH,EAAMY,OAEjB,OAAOb,GApkBd,GACOM,GAAQW,OAAOX,MAAOY,EAAKZ,EAAMY,GAAIC,EAAK,mBAC1CC,EAAuB,ulBACvBC,EAAsB,gmBACtBC,EAAuB,sJACvBC,EAAoBjB,EAAMkB,MAAMC,QAChCC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,GAEjBG,YAAa,SAAUC,EAAcC,GACjC,MAAOD,GAAanB,OAAOoB,IAE/BC,gBAAiB,WACb,UAEJC,sBAAuB,WACnB,MAAO,IAEXC,uBAAwB,SAAUC,EAAa1B,EAAQ2B,EAAgBC,GACnE,GAAIX,GAAOC,KAAKC,KACO,KAAnBQ,GAAsC,IAAdC,GAAmB5B,EAAOX,QAClD4B,EAAKQ,uBAAuBC,EAAa1B,IAGjD6B,gBAAiB,SAAUC,EAAU9B,EAAQb,EAAO4C,EAAMJ,EAAgBK,GAAzD,GACTf,GAAOC,KAAKC,MACZc,EAAWhB,EAAKiB,WACpBJ,GAAS1B,KAAKR,EAAMuC,OAAO,kEAAmEhD,EAAME,OAAQ4B,EAAKmB,eAC7GL,KAAMA,EACNE,SAAUA,IACVN,GAAkBK,EAAY3C,OAAS,GAAMW,EAAOX,OAAqB,GAAZ,UAAiBW,EAAOX,OAAsB,GAAb,cAEtGgD,aAAc,aAGdC,YAAa,SAAUC,GACnB,MAAOA,GAAaC,OAAO,IAE/BC,iBAAkB,aAGlBC,kBAAmB,SAAUC,EAAOpD,EAAOqD,GACvC,GAAI3B,GAAOC,KAAKC,KAChBwB,GAAME,OAAO5B,EAAKyB,kBAAkBnD,EAAOqD,OAG/CE,EAA0BlD,EAAMkB,MAAMC,QACtCC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,GAEjBG,YAAa,SAAUC,EAAcC,GAAxB,GAKDS,GACAgB,EALJ9B,EAAOC,KAAKC,KAChB,OAAIF,GAAKiB,YACEb,EAAanB,OAAOoB,IAEvBS,EAAOT,EAAQ0B,MAAM,EAAG,GACxBD,EAAqBzB,EAAQ0B,MAAM,GAChCjB,EAAK7B,OAAOmB,GAAcnB,OAAO6C,KAGhDE,mBAAoB,SAAUC,EAAcC,EAAWC,GACnD,MAAIF,GAAaE,GAAOC,MAAQF,EAAUC,GAAOC,OAC/B,IAAVD,GAGOlC,KAAK+B,mBAAmBC,EAAcC,EAAWC,EAAQ,KAK5E7B,gBAAiB,SAAUS,EAAahC,GAAvB,GAGJsD,GACIC,EACDnE,EAUYoE,EACKC,EAfzBC,KACAP,EAAY,IAChB,KAASG,EAAgB,EAAGA,EAAgBtB,EAAY3C,OAAQiE,IAC5D,IAASC,EAAW,EAAGA,EAAWvB,EAAYsB,GAAe/D,MAAMF,OAAQkE,IAAY,CAEnF,GADInE,EAAM,EACiB,IAAvBsE,EAAYrE,OACZ,IAAKD,EAAKA,EAAMY,EAAOsD,GAAejE,OAAQD,IAC1CsE,EAAYtD,MAAM,QAGtB,KAAKhB,EAAKA,EAAMY,EAAOsD,GAAejE,OAAQD,IAC1C,GAAI8B,KAAK+B,mBAAmBjD,EAAOsD,GAAgBH,EAAW/D,GAC1DsE,EAAYtE,GAAKsE,EAAYtE,GAAKC,OAAS,SACxC,CAEH,IADImE,EAAgBE,EAAYtE,GAAKsE,EAAYtE,GAAKC,OAAS,GAAK,EAC3DoE,EAAI,EAAGA,EAAID,EAAeC,IAC/BC,EAAYtE,GAAKgB,KAAK,EAE1BsD,GAAYtE,GAAKgB,KAAK,GAIlC+C,EAAYnD,EAAOsD,GAG3B,MAAOI,IAEXlC,sBAAuB,SAAUQ,GAAV,GAEVyB,GADLE,EAAoB,CACxB,KAASF,EAAI,EAAGA,EAAIzB,EAAY3C,OAAQoE,IACpCE,GAAqB3B,EAAYyB,GAAGlE,MAAMF,MAE9C,OAAOsE,IAEXlC,uBAAwB,SAAUC,EAAa1B,EAAQ2B,EAAgBC,EAAW8B,EAAaC,EAAmB5B,EAAM6B,GAAhG,GAUPxE,GATT6B,EAAOC,KAAKC,MACZc,EAAWhB,EAAKiB,WACpB,IAAKD,EAgBsB,IAAnBN,GAAsC,IAAdC,GAAmB5B,EAAOX,QAClD4B,EAAKQ,uBAAuBC,EAAa1B,OAV7C,KANuB,IAAnB2B,GAAsC,IAAdC,GACxBF,EAAYtB,KAAKR,EAAMuC,OAAO,oEAAqEwB,EAAmB1C,EAAKmB,eACvHL,KAAMA,EACNE,SAAUA,MAGT7C,EAAM,EAAGA,EAAMY,EAAO2B,GAAgBtC,OAAQD,IAC/CsE,EAAYtE,GAAKwE,IACjBlC,EAAYtB,KAAKR,EAAMuC,OAAO,6DAA8DuB,EAAYtE,GAAKwE,GAAqB3C,EAAK4C,gBACnIC,MAAO9D,EAAO2B,GAAgBvC,GAAKiE,KACnCpB,SAAUA,IACVjC,EAAO2B,GAAgBvC,GAAK2E,aAShDlC,gBAAiB,aAGjBQ,aAAc,SAAUM,GAAV,GAKDc,GAJLxC,EAAOC,KAAKC,MACZ6C,EAAc/C,EAAKgD,cAAcC,KAAK,SAAUC,EAAGC,GACnD,MAAOD,GAAEE,MAAM,GAAGP,MAAMQ,UAAYF,EAAEC,MAAM,GAAGP,MAAMQ,WAEzD,KAASb,EAAI,EAAGA,EAAIO,EAAY3E,OAAQoE,IACpCd,EAAME,OAAO5B,EAAKyB,kBAAkBsB,EAAYP,GAAGY,MAAOL,EAAYP,GAAGzD,UAGjFsC,YAAa,SAAUC,GACnB,MAAOA,GAAaS,MAAM,IAE9BP,iBAAkB,SAAUzC,EAAQZ,EAAKwD,GAAvB,GAGDjB,GACDI,EACAwC,EACKd,EALbxC,EAAOC,KAAKC,KAChB,IAAInB,EAAOZ,GAAKG,MACZ,IAASoC,EAAiB,EAAGA,EAAiB3B,EAAOZ,GAAKG,MAAMF,OAAQsC,IAAkB,CAGtF,IAFII,EAAO/B,EAAOZ,GAAKG,MAAMoC,GAAgBmC,MACzCS,GAAa,EACRd,EAAI,EAAGA,EAAIxC,EAAKgD,cAAc5E,OAAQoE,IACvCxC,EAAKgD,cAAcR,GAAGY,MAAM,GAAGP,MAAMQ,YAAcvC,EAAKuC,YACxDC,GAAa,EACbtD,EAAKgD,cAAcR,GAAGY,MAAMjE,KAAKJ,EAAOZ,GAAKG,MAAMoC,IACnDV,EAAKgD,cAAcR,GAAGzD,OAAOI,KAAKwC,GAGrC2B,IACDtD,EAAKgD,cAAc7D,MACfiE,OAAQrE,EAAOZ,GAAKG,MAAMoC,IAC1B3B,QAAS4C,OAM7BF,kBAAmB,cAIvB9C,GAAMY,GAAGgE,UAAU3D,kBAAoBA,EACvCjB,EAAMY,GAAGgE,UAAU1B,wBAA0BA,EAC7CtC,EAAGiE,WAAajE,EAAGkE,cAAc3D,QAC7BC,KAAM,SAAU2D,EAASC,GACrBpE,EAAGkE,cAAcG,GAAG7D,KAAKV,KAAKY,KAAMyD,EAASC,GAC7C1D,KAAK4D,aAAe5D,KAAK6D,kBACzBH,EAAU1D,KAAK0D,QACXA,EAAQI,WACRJ,EAAQI,SAAW/F,EAAE8B,QAASkE,UAAU,GAAQL,EAAQI,UACpDE,QAAQ,EACRC,QAAQ,IACPC,SAAUR,EAAQQ,YAE3BlE,KAAKmE,MAAQT,EAAQS,MACrBnE,KAAKoE,eAAiBpE,KAAKqE,WAAWX,EAAQY,cAAe9E,GAC7DQ,KAAKkB,cAAgBxC,EAAM6F,SAASb,EAAQc,mBAC5CxE,KAAK2C,eAAiBjE,EAAM6F,SAASb,EAAQe,oBAC7CzE,KAAK0E,cAAgBhG,EAAM6F,SAASb,EAAQiB,mBAC5C3E,KAAKyD,QAAQmB,GAAG,aAAerF,EAAI,8CAA+C,eAAeqF,GAAG,aAAerF,EAAI,8CAA+C,eAAeqF,GAAG,QAAUrF,EAAI,mEAAoE,WAC1QS,KAAK6E,cAAcnB,EAAQ7C,OAE/BiE,KAAM,SACNjB,gBAAiB,WACb,MAAI7D,MAAK+E,mBACE,GAAIrG,GAAMY,GAAGgE,UAAU1B,wBAAwB5B,MAE/C,GAAItB,GAAMY,GAAGgE,UAAU3D,kBAAkBK,OAGxDgF,YAAa,SAAUC,GACnBlH,EAAEkH,EAAEC,eAAeC,SAAS,kBAEhCC,YAAa,SAAUH,GACnBlH,EAAEkH,EAAEC,eAAeG,YAAY,kBAEnCC,QAAS,SAAUL,GACfA,EAAEM,iBACFvF,KAAKwF,QAAQ,UAAYC,IAAK1H,EAAEkH,EAAEC,eAAeQ,QAAQ,WAAWC,KAAKjH,EAAMiH,KAAK,WAExFC,SAAU,WACN,MAAOlH,GAAMmC,KAAKgF,QAAQ7F,KAAK8F,cAEnCA,UAAW,WACP,MAAO9F,MAAK+F,YAEhBC,QAAS,WACL,MAAOhG,MAAKiG,UAEhBC,aAAc,WACV,MAAOxH,GAAMmC,KAAKsF,YAAYnG,KAAK8F,cAEvCjB,cAAe,SAAUhE,GACrBb,KAAK+F,WAAalF,EAClBb,KAAKiG,SAAWvH,EAAMmC,KAAKuF,QAAQvF,EAAM,GACzCb,KAAKqG,aAAarG,KAAKsG,WACvBtG,KAAKuG,UACLvG,KAAKyB,MAAM0D,SAAS,uBAExBmB,QAAS,WAAA,GAcDE,GAEIrG,EACKjC,EAhBTkC,IAEI+B,KAAMnC,KAAK0D,QAAQQ,SAASuC,KAC5B5D,UAAW,2BAEbV,KAAMnC,KAAK0D,QAAQQ,SAASwC,OASlC,IAPK1G,KAAKgB,aACNZ,EAAQkB,OAAO,EAAG,GACda,KAAMnC,KAAK0D,QAAQQ,SAASrD,KAC5BgC,UAAW,2BAGf2D,EAAYxG,KAAK2G,iBACjBH,EAAUrI,OAAQ,CAElB,IADIgC,KACKjC,EAAM,EAAGA,EAAMsI,EAAUrI,OAAQD,IACtCiC,EAAajB,MACTiD,KAAM,GACNU,UAAW,2BAGnBzC,GAAUJ,KAAK4D,aAAa1D,YAAYC,EAAcC,GAE1D,OAASA,QAASA,IAEtBwG,OAAQ,SAAUC,GAAV,GAEK3I,GACDwI,EACAI,EACAC,EACAC,EAIAC,EAQSC,EAjBbjJ,IACJ,KAASC,EAAM,EAAGA,EAAM2I,EAAO1I,OAAQD,IAanC,GAZIwI,EAAQG,EAAO3I,GACf4I,EAAQJ,EAAMI,MACdC,EAAML,EAAMS,SAAWzI,EAAMmC,KAAKuG,QAAQV,EAAMK,KAAOL,EAAMK,IAC7DC,EAAsBK,KAAKC,MAAMP,EAAMrI,EAAMmC,KAAKuG,QAAQN,IAAUpI,EAAMmC,KAAK0G,YAC/Eb,EAAMS,WACNH,GAAuB,GAEvBC,EAAOP,EAAMc,QACjBP,EAAKnB,UAAYpH,EAAMmC,KAAKuG,QAAQN,GAChCG,EAAKnB,WAAa9F,KAAK8F,aACvB7H,EAAMiB,KAAK+H,GAEXD,EAAsB,EAGtB,IAFAC,EAAKF,IAAMrI,EAAMmC,KAAKgF,QAAQiB,GAC9BG,EAAKQ,MAAO,EACHP,EAAM,EAAGA,EAAMF,EAAqBE,IACzCJ,EAAQG,EAAKF,IACbE,EAAOP,EAAMc,QACbP,EAAKH,MAAQG,EAAKnB,UAAYpH,EAAMmC,KAAKuG,QAAQN,GACjDG,EAAKF,IAAMrI,EAAMmC,KAAKgF,QAAQiB,GAC1BI,GAAOF,EAAsB,GAC7BC,EAAKF,IAAM,GAAIW,MAAKT,EAAKH,MAAMa,cAAeV,EAAKH,MAAMc,WAAYX,EAAKH,MAAMM,UAAWL,EAAIc,WAAYd,EAAIe,aAAcf,EAAIgB,aAAchB,EAAIiB,mBACnJf,EAAKgB,MAAO,IAEZhB,EAAKE,UAAW,EAChBF,EAAKiB,QAAS,IAEdxJ,EAAMmC,KAAKuG,QAAQH,EAAKF,MAAQ/G,KAAKgG,WAAaiB,EAAKH,OAAS9G,KAAK8F,aAAepH,EAAMmC,KAAKuG,QAAQH,EAAKH,OAAO1D,WAAapD,KAAKgG,UAAU5C,YAC/InF,EAAMiB,KAAK+H,EAK3B,OAAO,IAAIvI,GAAMyJ,KAAKC,MAAMnK,GAAO+E,OAE3BqF,MAAO,QACPC,IAAK,QAGLD,MAAO,MACPC,IAAK,SAEVC,SAAUF,MAAO,cAAeG,WAEvChH,kBAAmB,SAAUV,EAAahC,GAAvB,GAQN2B,GACDI,EACA5C,EACAwK,EACK/H,EACDuG,EACArG,EACAJ,EAdRkI,KACA5E,EAAW9D,KAAK0D,QAAQI,SACxB6E,EAAa7E,GAAYA,EAAS8E,WAAY,IAAU5I,KAAKgB,YAC7DD,EAAWf,KAAKgB,YAChByB,EAAoBzC,KAAK4D,aAAatD,sBAAsBQ,GAC5D0B,EAAcxC,KAAK4D,aAAavD,gBAAgBS,EAAahC,GAC7D4D,EAAqB,CACzB,KAASjC,EAAiB,EAAGA,EAAiBK,EAAY3C,OAAQsC,IAI9D,IAHII,EAAOC,EAAYL,GAAgBmC,MACnC3E,EAAQ6C,EAAYL,GAAgBpC,MACpCoK,EAAQ/J,EAAMmC,KAAKgI,QAAQhI,GACtBH,EAAY,EAAGA,EAAYzC,EAAME,OAAQuC,IAC1CuG,EAAOhJ,EAAMyC,GACbE,KACAJ,EAAeO,KAAWH,EAC9BZ,KAAK4D,aAAarD,uBAAuBC,EAAa1B,EAAQ2B,EAAgBC,EAAW8B,EAAaC,EAAmB5B,EAAM6B,GAC/HA,IACkB,IAAdhC,IACIK,GACAP,EAAYtB,KAAKR,EAAMuC,OAAO,8DAA+DjB,KAAKkB,eAC9FL,KAAMA,EACNE,SAAUA,IACTf,KAAK2G,iBAAiBxI,OAAqB,GAAZ,YACpCuK,EAAUxJ,KAAK,wCAA0CuJ,EAAQ,oBAAsB,KAAOjI,EAAYsI,KAAK,IAAM,UAErH9I,KAAK4D,aAAajD,gBAAgBC,EAAU9B,EAAQb,EAAO4C,EAAMJ,EAAgBK,IAIrFmG,EAAKhG,OADLgG,EAAKQ,KACS,QACPR,EAAKgB,KACE,QAEA,cAElBhB,EAAKT,UAAYxG,KAAK+I,eAAe9B,GACrCrG,EAAS1B,KAAKR,EAAMuC,OAAO,+EAAgFgG,EAAKgB,MAAQhB,EAAKiB,OAAS,iDAAmD,GAAIlI,KAAK0E,cAAcuC,EAAKO,OACjNV,MAAOG,EAAK+B,YAAc/B,EAAKH,MAC/BC,IAAKE,EAAKgC,SAAWhC,EAAKF,OACzBE,EAAKQ,MAAQR,EAAKiB,OAAS,kDAAoD,GAAIlI,KAAKoE,eAAe6C,EAAKO,OAC7GmB,WAAYA,EACZzE,SAAUlE,KAAK0D,QAAQQ,aACrBlE,KAAK2G,iBAAiBxI,QAAU4C,EAAW,UAAY,KAC7D2H,EAAUxJ,KAAK,wCAA0CuJ,EAAQ,oBAAsB,KAAO7H,EAASkI,KAAK,IAAM,QAG1H,OAAOJ,GAAUI,KAAK,KAE1BvI,uBAAwB,SAAUC,EAAa1B,GAAvB,GAEXZ,GADL6C,EAAWf,KAAKgB,WACpB,KAAS9C,EAAM,EAAGA,EAAMY,EAAOX,OAAQD,IACnCsC,EAAYtB,KAAKR,EAAMuC,OAAO,gEAAiEnC,EAAOZ,GAAKF,QAASgC,KAAK2C,gBACrHC,MAAO9D,EAAOZ,GAAKiE,KACnBpB,SAAUA,IACVjC,EAAOZ,GAAK2E,aAGxBqG,OAAQ,SAAUrC,GAAV,GAIIL,GAWJnI,EAdAoD,EAAQzB,KAAKmJ,QAAQC,KAAK,SAASC,QACnCvK,IACA+H,GAAO1I,OAAS,IACZqI,EAAYxG,KAAK2G,iBACjBH,EAAUrI,QACVW,EAASkB,KAAKsJ,0BAA0BzC,EAAQL,EAAW,MAC3DxG,KAAK+C,iBACL/C,KAAKuJ,cAAczK,EAAQ2C,MAC3BzB,KAAK4D,aAAazC,aAAaM,KAE/B3C,EAASkB,KAAK4G,OAAOC,GACrBpF,EAAME,OAAO3B,KAAKwB,kBAAkB1C,SAGxCT,EAAQ2B,KAAKwJ,YAAc3K,EAAkBC,GACjDkB,KAAKyJ,cAAchI,EAAOpD,GAC1B2B,KAAK0J,gBACL1J,KAAKwF,QAAQ,aAEjBiE,cAAe,SAAUhI,EAAOpD,GAC5B2B,KAAK2J,QAAQ,UAAW,WACpB,GAAIxB,MAAWyB,EAAWvL,EAAMwL,IAAI,SAAUrL,GAEtC,MADA2J,GAAKjJ,MAAO4K,SAAUtL,IACfiD,EAAM2H,KAAK,WAAa1K,EAAMiH,KAAK,OAAS,IAAMnH,EAAKiH,IAAM,MAE5E,QACImE,SAAUA,EACVzB,KAAMA,MAIlBoB,cAAe,SAAUzK,EAAQ2C,EAAOJ,GAAzB,GACFnD,GAASC,EACVuD,CADR,KAASxD,EAAM,EAAGC,EAASW,EAAOX,OAAQD,EAAMC,EAAQD,IAChDwD,EAAU1B,KAAK4D,aAAaxC,YAAYC,GAC5CK,EAAQxC,KAAKJ,EAAOZ,IACpB8B,KAAK4D,aAAarC,iBAAiBzC,EAAQZ,EAAKwD,GAC5C5C,EAAOZ,GAAKY,OACZkB,KAAKuJ,cAAczK,EAAOZ,GAAKY,OAAQ2C,EAAOC,GAE9C1B,KAAK4D,aAAapC,kBAAkBC,EAAO3C,EAAOZ,GAAKG,MAAOqD,IAI1E4H,0BAA2B,SAAUzC,EAAQL,EAAWuD,GAA7B,GAKdC,GACDpH,EACAqH,EAKIhM,EACA4E,EAIAqH,EAaIC,EA7BZ5L,EAAWiI,EAAU,GACrB4D,KACAjC,EAAO5J,EAAS8L,WAAWtK,OAC3BgB,EAAWf,KAAKgB,WACpB,KAASgJ,EAAY,EAAGA,EAAY7B,EAAKhK,OAAQ6L,IACzCpH,EAAQtE,EAAcC,EAAU4J,EAAK6B,IACrCC,EAAM,GAAIvL,GAAMyJ,KAAKC,MAAMvB,GAAQyD,QACnCjC,MAAO9J,EAAS8J,MAChBkC,SAAUjL,EAAGkE,cAAcgH,cAAc5H,KAC1C4F,UACCyB,EAAI9L,SACAF,EAAQ+B,KAAK4G,OAAOqD,GACpBpH,EAAYkH,EAAS,GAAK,WAC1BC,IAAc7B,EAAKhK,OAAS,KAAO4L,GAAUA,EAAOlH,UAAU4H,QAAQ,gBACtE5H,GAAa,WAEbqH,GACA/H,KAAMzD,EAAMC,OAAOJ,EAASmM,eAAevC,EAAK6B,IAChDpH,MAAOA,EACP5E,QAAS,EACT6E,UAAWA,GAEX2D,EAAUrI,OAAS,GACnB+L,EAAIpL,OAASkB,KAAKsJ,0BAA0BW,EAAKzD,EAAU1E,MAAM,GAAIoI,GACjEH,IACAA,EAAO/L,SAAWkM,EAAIlM,WAG1BkM,EAAI7L,MAAQJ,EACRkM,EAAOnM,EAAQkM,EAAI7L,OACnB0C,IACAoJ,GAAQD,EAAI7L,MAAMF,QAEtB+L,EAAIlM,QAAUmM,EACVJ,IACAA,EAAO/L,SAAWmM,IAG1BC,EAAclL,KAAKgL,GAG3B,OAAOE,IAEXO,mBAAoB,SAAUC,GAAV,GACZ1I,GAAOwE,EASHqD,CAPR,IADAa,EAAO7M,EAAE6M,IACLA,EAAKC,SAAS,2BAA8B7K,KAAKwJ,YAAYrL,OAejE,MAZIyM,GAAKE,GAAG,aACRF,EAAOA,EAAKlF,QAAQ,OAEpB1F,KAAKgB,aACD+I,EAASa,EAAKb,SAClB7H,EAAQ6H,EAAOA,SAASgB,WAAWT,OAAO,WACtC,MAAOvM,GAAEiC,MAAM+K,SAAS,iCAAiC5M,SAC1D+D,MAAM6H,IAET7H,EAAQ0I,EAAKb,SAAS7H,QAE1BwE,EAAQ1G,KAAKwJ,YAAYtH,IAErBA,MAAOA,EACP4E,MAAOJ,EAAMI,MACbC,IAAKL,EAAMK,IACXI,SAAUT,EAAMS,SAChB1B,IAAKiB,EAAMjB,MAGnBuF,OAAQ,SAAUC,GACdjL,KAAKkL,gBACL,IAAIC,GAAMnL,KAAKyB,MAAM2H,KAAK,WAAWgC,GAAGH,EAAU/I,OAAOwD,QAAQ,MAAMP,SAAS,oBAAoBQ,KAAK,iBAAiB,GAAM,EAChI3F,MAAKqL,QAAQF,IAEjBG,KAAM,SAAUL,EAAWM,GAArB,GAWM7E,GAVJ8E,GAAU,EACVtJ,EAAQ+I,EAAU/I,KAkBtB,OAjBIqJ,IAAO7M,EAAM+M,KAAKC,IAClBxJ,IACAsJ,GAAU,GACHD,GAAO7M,EAAM+M,KAAKE,OACzBzJ,IACAsJ,GAAU,GAEVA,IACI9E,EAAQ1G,KAAKwJ,YAAYtH,GACzBwE,IACAuE,EAAUnE,MAAQJ,EAAMI,MACxBmE,EAAUlE,IAAML,EAAMK,IACtBkE,EAAU9D,SAAWT,EAAMS,SAC3B8D,EAAUpE,QAAUH,EAAMjB,KAC1BwF,EAAU/I,MAAQA,IAGnBsJ,GAEXI,YAAa,WACT,OAAO,GAEXC,mBAAoB,SAAUZ,GAC1B,GAAIvE,GAAQ1G,KAAKwJ,YAAY,EACzB9C,KACAuE,EAAUnE,MAAQJ,EAAMI,MACxBmE,EAAUlE,IAAML,EAAMK,IACtBkE,EAAU9D,SAAWT,EAAMS,SAC3B8D,EAAUpE,QAAUH,EAAMjB,KAC1BwF,EAAU/I,MAAQ,IAG1B4J,UAAW,WACP,OAAO,GAEXlD,QAAS,WACD5I,KAAKyD,SACLzD,KAAKyD,QAAQsI,IAAIxM,GAErBD,EAAGkE,cAAcG,GAAGiF,QAAQxJ,KAAKY,OAErC0D,SACIS,MAAO,SACPW,KAAM,SACNhB,UAAU,EACVkI,mBAAoB,cACpBC,wBAAyB,gBACzBC,yBAA0B,2BAC1B5H,cAAe,WACfK,kBAAmB,0GACnBH,kBAAmB/E,EACnBgF,mBAAoB/E,EACpBwE,UACIwC,MAAO,QACP7F,KAAM,OACN4F,KAAM,OACN0F,OAAQ,eA4CtB9M,OAAOX,MAAM0N,QACR/M,OAAOX,OACE,kBAAVZ,SAAwBA,OAAOuO,IAAMvO,OAAS,SAAUwO,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.scheduler.agendaview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.scheduler.agendaview', ['kendo.scheduler.view'], f);\n}(function () {\n    var __meta__ = {\n        id: 'scheduler.agendaview',\n        name: 'Scheduler Agenda View',\n        category: 'web',\n        description: 'The Scheduler Agenda View',\n        depends: ['scheduler.view'],\n        hidden: true\n    };\n    (function ($) {\n        var kendo = window.kendo, ui = kendo.ui, NS = '.kendoAgendaView';\n        var EVENT_WRAPPER_FORMAT = '<div class=\"k-task\" title=\"#:(data.title || \"\").replace(/\"/g,\"\\'\")#\" data-#=kendo.ns#uid=\"#=uid#\">' + '# if (resources[0]) {#' + '<span class=\"k-scheduler-mark\" style=\"background-color:#=resources[0].color#\"></span>' + '# } #' + '# if (data.isException()) { #' + '<span class=\"k-icon k-i-non-recurrence\"></span>' + '# } else if (data.isRecurring()) {#' + '<span class=\"k-icon k-i-reload\"></span>' + '# } #' + '<span class=\"k-scheduler-task-text\">{0}</span>' + '#if (showDelete) {#' + '<a href=\"\\\\#\" class=\"k-link k-event-delete\" title=\"${data.messages.destroy}\" aria-label=\"${data.messages.destroy}\"><span class=\"k-icon k-i-close\"></span></a>' + '#}#' + '</div>';\n        var EVENT_DATE_TEMPLATE = '# if (!isMobile) { #' + '<strong class=\"k-scheduler-agendaday\">' + '#=kendo.toString(date, \"dd\")#' + '</strong>' + '<em class=\"k-scheduler-agendaweek\">' + '#=kendo.toString(date,\"dddd\")#' + '</em>' + '<span class=\"k-scheduler-agendadate\">' + '#=kendo.toString(date, \"y\")#' + '</span>' + '# } else { #' + '<div class=\"k-scheduler-datecolumn-wrap\">' + '<span class=\"k-mobile-scheduler-agendadate\">' + '<span class=\"k-mobile-scheduler-agendaday\">#=kendo.toString(date, \"dd\")#</span>' + '&nbsp' + '<span class=\"k-mobile-scheduler-agendamonth\">#=kendo.toString(date, \"MMMM\")#</span>' + '</span>' + '<span class=\"k-mobile-scheduler-agendaweekday\">' + '#=kendo.toString(date, \"dddd\")#' + '</span>' + '</div>' + '# } #';\n        var EVENT_GROUP_TEMPLATE = '# if (!isMobile) { #' + '<strong class=\"k-scheduler-adgendagroup\">' + '#=value#' + '</strong>' + '# } else { #' + '<span class=\"k-scheduler-group-text\">' + '#=value#' + '</span>' + '# } #';\n        var AgendaGroupedView = kendo.Class.extend({\n            init: function (view) {\n                this._view = view;\n            },\n            _getColumns: function (groupHeaders, columns) {\n                return groupHeaders.concat(columns);\n            },\n            _getGroupsInDay: function () {\n                return [];\n            },\n            _getSumOfItemsForDate: function () {\n                return 0;\n            },\n            _renderTaskGroupsCells: function (headerCells, groups, taskGroupIndex, taskIndex) {\n                var view = this._view;\n                if (taskGroupIndex === 0 && taskIndex === 0 && groups.length) {\n                    view._renderTaskGroupsCells(headerCells, groups);\n                }\n            },\n            _renderDateCell: function (tableRow, groups, tasks, date, taskGroupIndex, tasksGroups) {\n                var view = this._view;\n                var isMobile = view._isMobile();\n                tableRow.push(kendo.format('<td class=\"k-scheduler-datecolumn{3}{2}\" rowspan=\"{0}\">{1}</td>', tasks.length, view._dateTemplate({\n                    date: date,\n                    isMobile: isMobile\n                }), taskGroupIndex == tasksGroups.length - 1 && !groups.length ? ' k-last' : '', !groups.length ? ' k-first' : ''));\n            },\n            _renderDates: function () {\n                return undefined;\n            },\n            _getParents: function (parentGroups) {\n                return parentGroups.splice(0);\n            },\n            _getGroupsByDate: function () {\n                return undefined;\n            },\n            _renderTaskGroups: function (table, items, parents) {\n                var view = this._view;\n                table.append(view._renderTaskGroups(items, parents));\n            }\n        });\n        var AgendaGroupedByDateView = kendo.Class.extend({\n            init: function (view) {\n                this._view = view;\n            },\n            _getColumns: function (groupHeaders, columns) {\n                var view = this._view;\n                if (view._isMobile()) {\n                    return groupHeaders.concat(columns);\n                } else {\n                    var date = columns.slice(0, 1);\n                    var columnsWithoutDate = columns.slice(1);\n                    return date.concat(groupHeaders).concat(columnsWithoutDate);\n                }\n            },\n            _compareDateGroups: function (currentGroup, prevGroup, index) {\n                if (currentGroup[index].text == prevGroup[index].text) {\n                    if (index === 0) {\n                        return true;\n                    } else {\n                        return this._compareDateGroups(currentGroup, prevGroup, index - 1);\n                    }\n                }\n                return false;\n            },\n            _getGroupsInDay: function (tasksGroups, groups) {\n                var groupsInDay = [];\n                var prevGroup = null;\n                for (var tasksGroupIdx = 0; tasksGroupIdx < tasksGroups.length; tasksGroupIdx++) {\n                    for (var itemsIdx = 0; itemsIdx < tasksGroups[tasksGroupIdx].items.length; itemsIdx++) {\n                        var idx = 0;\n                        if (groupsInDay.length === 0) {\n                            for (idx; idx < groups[tasksGroupIdx].length; idx++) {\n                                groupsInDay.push([1]);\n                            }\n                        } else {\n                            for (idx; idx < groups[tasksGroupIdx].length; idx++) {\n                                if (this._compareDateGroups(groups[tasksGroupIdx], prevGroup, idx)) {\n                                    groupsInDay[idx][groupsInDay[idx].length - 1]++;\n                                } else {\n                                    var lastItemValue = groupsInDay[idx][groupsInDay[idx].length - 1] - 1;\n                                    for (var i = 0; i < lastItemValue; i++) {\n                                        groupsInDay[idx].push(0);\n                                    }\n                                    groupsInDay[idx].push(1);\n                                }\n                            }\n                        }\n                        prevGroup = groups[tasksGroupIdx];\n                    }\n                }\n                return groupsInDay;\n            },\n            _getSumOfItemsForDate: function (tasksGroups) {\n                var sumOfItemsForDate = 0;\n                for (var i = 0; i < tasksGroups.length; i++) {\n                    sumOfItemsForDate += tasksGroups[i].items.length;\n                }\n                return sumOfItemsForDate;\n            },\n            _renderTaskGroupsCells: function (headerCells, groups, taskGroupIndex, taskIndex, groupsInDay, sumOfItemsForDate, date, groupsRowSpanIndex) {\n                var view = this._view;\n                var isMobile = view._isMobile();\n                if (!isMobile) {\n                    if (taskGroupIndex === 0 && taskIndex === 0) {\n                        headerCells.push(kendo.format('<td class=\"k-scheduler-datecolumn k-first\" rowspan=\"{0}\">{1}</td>', sumOfItemsForDate, view._dateTemplate({\n                            date: date,\n                            isMobile: isMobile\n                        })));\n                    }\n                    for (var idx = 0; idx < groups[taskGroupIndex].length; idx++) {\n                        if (groupsInDay[idx][groupsRowSpanIndex]) {\n                            headerCells.push(kendo.format('<td class=\"k-scheduler-groupcolumn\" rowspan=\"{0}\">{1}</td>', groupsInDay[idx][groupsRowSpanIndex], view._groupTemplate({\n                                value: groups[taskGroupIndex][idx].text,\n                                isMobile: isMobile\n                            }), groups[taskGroupIndex][idx].className));\n                        }\n                    }\n                } else {\n                    if (taskGroupIndex === 0 && taskIndex === 0 && groups.length) {\n                        view._renderTaskGroupsCells(headerCells, groups);\n                    }\n                }\n            },\n            _renderDateCell: function () {\n                return undefined;\n            },\n            _renderDates: function (table) {\n                var view = this._view;\n                var sortedArray = view._groupsByDate.sort(function (a, b) {\n                    return a.array[0].value.getTime() - b.array[0].value.getTime();\n                });\n                for (var i = 0; i < sortedArray.length; i++) {\n                    table.append(view._renderTaskGroups(sortedArray[i].array, sortedArray[i].groups));\n                }\n            },\n            _getParents: function (parentGroups) {\n                return parentGroups.slice(0);\n            },\n            _getGroupsByDate: function (groups, idx, parents) {\n                var view = this._view;\n                if (groups[idx].items) {\n                    for (var taskGroupIndex = 0; taskGroupIndex < groups[idx].items.length; taskGroupIndex++) {\n                        var date = groups[idx].items[taskGroupIndex].value;\n                        var dateExists = false;\n                        for (var i = 0; i < view._groupsByDate.length; i++) {\n                            if (view._groupsByDate[i].array[0].value.getTime() === date.getTime()) {\n                                dateExists = true;\n                                view._groupsByDate[i].array.push(groups[idx].items[taskGroupIndex]);\n                                view._groupsByDate[i].groups.push(parents);\n                            }\n                        }\n                        if (!dateExists) {\n                            view._groupsByDate.push({\n                                array: [groups[idx].items[taskGroupIndex]],\n                                groups: [parents]\n                            });\n                        }\n                    }\n                }\n            },\n            _renderTaskGroups: function () {\n                return undefined;\n            }\n        });\n        kendo.ui.scheduler.AgendaGroupedView = AgendaGroupedView;\n        kendo.ui.scheduler.AgendaGroupedByDateView = AgendaGroupedByDateView;\n        ui.AgendaView = ui.SchedulerView.extend({\n            init: function (element, options) {\n                ui.SchedulerView.fn.init.call(this, element, options);\n                this._groupedView = this._getGroupedView();\n                options = this.options;\n                if (options.editable) {\n                    options.editable = $.extend({ 'delete': true }, options.editable, {\n                        create: false,\n                        update: false\n                    }, { messages: options.messages });\n                }\n                this.title = options.title;\n                this._eventTemplate = this._eventTmpl(options.eventTemplate, EVENT_WRAPPER_FORMAT);\n                this._dateTemplate = kendo.template(options.eventDateTemplate);\n                this._groupTemplate = kendo.template(options.eventGroupTemplate);\n                this._timeTemplate = kendo.template(options.eventTimeTemplate);\n                this.element.on('mouseenter' + NS, '.k-scheduler-agenda .k-scheduler-content tr', '_mouseenter').on('mouseleave' + NS, '.k-scheduler-agenda .k-scheduler-content tr', '_mouseleave').on('click' + NS, '.k-scheduler-agenda .k-scheduler-content .k-link:has(.k-i-close)', '_remove');\n                this._renderLayout(options.date);\n            },\n            name: 'agenda',\n            _getGroupedView: function () {\n                if (this._isGroupedByDate()) {\n                    return new kendo.ui.scheduler.AgendaGroupedByDateView(this);\n                } else {\n                    return new kendo.ui.scheduler.AgendaGroupedView(this);\n                }\n            },\n            _mouseenter: function (e) {\n                $(e.currentTarget).addClass('k-state-hover');\n            },\n            _mouseleave: function (e) {\n                $(e.currentTarget).removeClass('k-state-hover');\n            },\n            _remove: function (e) {\n                e.preventDefault();\n                this.trigger('remove', { uid: $(e.currentTarget).closest('.k-task').attr(kendo.attr('uid')) });\n            },\n            nextDate: function () {\n                return kendo.date.nextDay(this.startDate());\n            },\n            startDate: function () {\n                return this._startDate;\n            },\n            endDate: function () {\n                return this._endDate;\n            },\n            previousDate: function () {\n                return kendo.date.previousDay(this.startDate());\n            },\n            _renderLayout: function (date) {\n                this._startDate = date;\n                this._endDate = kendo.date.addDays(date, 7);\n                this.createLayout(this._layout());\n                this._footer();\n                this.table.addClass('k-scheduler-agenda');\n            },\n            _layout: function () {\n                var columns = [\n                    {\n                        text: this.options.messages.time,\n                        className: 'k-scheduler-timecolumn'\n                    },\n                    { text: this.options.messages.event }\n                ];\n                if (!this._isMobile()) {\n                    columns.splice(0, 0, {\n                        text: this.options.messages.date,\n                        className: 'k-scheduler-datecolumn'\n                    });\n                }\n                var resources = this.groupedResources;\n                if (resources.length) {\n                    var groupHeaders = [];\n                    for (var idx = 0; idx < resources.length; idx++) {\n                        groupHeaders.push({\n                            text: '',\n                            className: 'k-scheduler-groupcolumn'\n                        });\n                    }\n                    columns = this._groupedView._getColumns(groupHeaders, columns);\n                }\n                return { columns: columns };\n            },\n            _tasks: function (events) {\n                var tasks = [];\n                for (var idx = 0; idx < events.length; idx++) {\n                    var event = events[idx];\n                    var start = event.start;\n                    var end = event.isAllDay ? kendo.date.getDate(event.end) : event.end;\n                    var eventDurationInDays = Math.ceil((end - kendo.date.getDate(start)) / kendo.date.MS_PER_DAY);\n                    if (event.isAllDay) {\n                        eventDurationInDays += 1;\n                    }\n                    var task = event.clone();\n                    task.startDate = kendo.date.getDate(start);\n                    if (task.startDate >= this.startDate()) {\n                        tasks.push(task);\n                    }\n                    if (eventDurationInDays > 1) {\n                        task.end = kendo.date.nextDay(start);\n                        task.head = true;\n                        for (var day = 1; day < eventDurationInDays; day++) {\n                            start = task.end;\n                            task = event.clone();\n                            task.start = task.startDate = kendo.date.getDate(start);\n                            task.end = kendo.date.nextDay(start);\n                            if (day == eventDurationInDays - 1) {\n                                task.end = new Date(task.start.getFullYear(), task.start.getMonth(), task.start.getDate(), end.getHours(), end.getMinutes(), end.getSeconds(), end.getMilliseconds());\n                                task.tail = true;\n                            } else {\n                                task.isAllDay = true;\n                                task.middle = true;\n                            }\n                            if (kendo.date.getDate(task.end) <= this.endDate() && task.start >= this.startDate() || kendo.date.getDate(task.start).getTime() == this.endDate().getTime()) {\n                                tasks.push(task);\n                            }\n                        }\n                    }\n                }\n                return new kendo.data.Query(tasks).sort([\n                    {\n                        field: 'start',\n                        dir: 'asc'\n                    },\n                    {\n                        field: 'end',\n                        dir: 'asc'\n                    }\n                ]).groupBy({ field: 'startDate' }).toArray();\n            },\n            _renderTaskGroups: function (tasksGroups, groups) {\n                var tableRows = [];\n                var editable = this.options.editable;\n                var showDelete = editable && editable.destroy !== false && !this._isMobile();\n                var isMobile = this._isMobile();\n                var sumOfItemsForDate = this._groupedView._getSumOfItemsForDate(tasksGroups);\n                var groupsInDay = this._groupedView._getGroupsInDay(tasksGroups, groups);\n                var groupsRowSpanIndex = 0;\n                for (var taskGroupIndex = 0; taskGroupIndex < tasksGroups.length; taskGroupIndex++) {\n                    var date = tasksGroups[taskGroupIndex].value;\n                    var tasks = tasksGroups[taskGroupIndex].items;\n                    var today = kendo.date.isToday(date);\n                    for (var taskIndex = 0; taskIndex < tasks.length; taskIndex++) {\n                        var task = tasks[taskIndex];\n                        var tableRow = [];\n                        var headerCells = !isMobile ? tableRow : [];\n                        this._groupedView._renderTaskGroupsCells(headerCells, groups, taskGroupIndex, taskIndex, groupsInDay, sumOfItemsForDate, date, groupsRowSpanIndex);\n                        groupsRowSpanIndex++;\n                        if (taskIndex === 0) {\n                            if (isMobile) {\n                                headerCells.push(kendo.format('<td class=\"k-scheduler-datecolumn {1}\" colspan=\"2\">{0}</td>', this._dateTemplate({\n                                    date: date,\n                                    isMobile: isMobile\n                                }), !this.groupedResources.length ? 'k-first' : ''));\n                                tableRows.push('<tr role=\"row\" aria-selected=\"false\"' + (today ? ' class=\"k-today\">' : '>') + headerCells.join('') + '</tr>');\n                            } else {\n                                this._groupedView._renderDateCell(tableRow, groups, tasks, date, taskGroupIndex, tasksGroups);\n                            }\n                        }\n                        if (task.head) {\n                            task.format = '{0:t}';\n                        } else if (task.tail) {\n                            task.format = '{1:t}';\n                        } else {\n                            task.format = '{0:t}-{1:t}';\n                        }\n                        task.resources = this.eventResources(task);\n                        tableRow.push(kendo.format('<td class=\"k-scheduler-timecolumn {4}\"><div>{0}{1}{2}</div></td><td>{3}</td>', task.tail || task.middle ? '<span class=\"k-icon k-i-arrow-60-left\"></span>' : '', this._timeTemplate(task.clone({\n                            start: task._startTime || task.start,\n                            end: task.endTime || task.end\n                        })), task.head || task.middle ? '<span class=\"k-icon k-i-arrow-60-right\"></span>' : '', this._eventTemplate(task.clone({\n                            showDelete: showDelete,\n                            messages: this.options.messages\n                        })), !this.groupedResources.length && isMobile ? 'k-first' : ''));\n                        tableRows.push('<tr role=\"row\" aria-selected=\"false\"' + (today ? ' class=\"k-today\">' : '>') + tableRow.join('') + '</tr>');\n                    }\n                }\n                return tableRows.join('');\n            },\n            _renderTaskGroupsCells: function (headerCells, groups) {\n                var isMobile = this._isMobile();\n                for (var idx = 0; idx < groups.length; idx++) {\n                    headerCells.push(kendo.format('<td class=\"k-scheduler-groupcolumn{2}\" rowspan=\"{0}\">{1}</td>', groups[idx].rowSpan, this._groupTemplate({\n                        value: groups[idx].text,\n                        isMobile: isMobile\n                    }), groups[idx].className));\n                }\n            },\n            render: function (events) {\n                var table = this.content.find('table').empty();\n                var groups = [];\n                if (events.length > 0) {\n                    var resources = this.groupedResources;\n                    if (resources.length) {\n                        groups = this._createGroupConfiguration(events, resources, null);\n                        this._groupsByDate = [];\n                        this._renderGroups(groups, table, []);\n                        this._groupedView._renderDates(table);\n                    } else {\n                        groups = this._tasks(events);\n                        table.append(this._renderTaskGroups(groups, []));\n                    }\n                }\n                var items = this._eventsList = flattenTaskGroups(groups);\n                this._angularItems(table, items);\n                this.refreshLayout();\n                this.trigger('activate');\n            },\n            _angularItems: function (table, items) {\n                this.angular('compile', function () {\n                    var data = [], elements = items.map(function (item) {\n                            data.push({ dataItem: item });\n                            return table.find('.k-task[' + kendo.attr('uid') + '=' + item.uid + ']');\n                        });\n                    return {\n                        elements: elements,\n                        data: data\n                    };\n                });\n            },\n            _renderGroups: function (groups, table, parentGroups) {\n                for (var idx = 0, length = groups.length; idx < length; idx++) {\n                    var parents = this._groupedView._getParents(parentGroups);\n                    parents.push(groups[idx]);\n                    this._groupedView._getGroupsByDate(groups, idx, parents);\n                    if (groups[idx].groups) {\n                        this._renderGroups(groups[idx].groups, table, parents);\n                    } else {\n                        this._groupedView._renderTaskGroups(table, groups[idx].items, parents);\n                    }\n                }\n            },\n            _createGroupConfiguration: function (events, resources, parent) {\n                var resource = resources[0];\n                var configuration = [];\n                var data = resource.dataSource.view();\n                var isMobile = this._isMobile();\n                for (var dataIndex = 0; dataIndex < data.length; dataIndex++) {\n                    var value = resourceValue(resource, data[dataIndex]);\n                    var tmp = new kendo.data.Query(events).filter({\n                        field: resource.field,\n                        operator: ui.SchedulerView.groupEqFilter(value)\n                    }).toArray();\n                    if (tmp.length) {\n                        var tasks = this._tasks(tmp);\n                        var className = parent ? '' : ' k-first';\n                        if (dataIndex === data.length - 1 && (!parent || parent.className.indexOf('k-last') > -1)) {\n                            className += ' k-last';\n                        }\n                        var obj = {\n                            text: kendo.getter(resource.dataTextField)(data[dataIndex]),\n                            value: value,\n                            rowSpan: 0,\n                            className: className\n                        };\n                        if (resources.length > 1) {\n                            obj.groups = this._createGroupConfiguration(tmp, resources.slice(1), obj);\n                            if (parent) {\n                                parent.rowSpan += obj.rowSpan;\n                            }\n                        } else {\n                            obj.items = tasks;\n                            var span = rowSpan(obj.items);\n                            if (isMobile) {\n                                span += obj.items.length;\n                            }\n                            obj.rowSpan = span;\n                            if (parent) {\n                                parent.rowSpan += span;\n                            }\n                        }\n                        configuration.push(obj);\n                    }\n                }\n                return configuration;\n            },\n            selectionByElement: function (cell) {\n                var index, event;\n                cell = $(cell);\n                if (cell.hasClass('k-scheduler-datecolumn') || !this._eventsList.length) {\n                    return;\n                }\n                if (cell.is('.k-task')) {\n                    cell = cell.closest('td');\n                }\n                if (this._isMobile()) {\n                    var parent = cell.parent();\n                    index = parent.parent().children().filter(function () {\n                        return $(this).children(':not(.k-scheduler-datecolumn)').length;\n                    }).index(parent);\n                } else {\n                    index = cell.parent().index();\n                }\n                event = this._eventsList[index];\n                return {\n                    index: index,\n                    start: event.start,\n                    end: event.end,\n                    isAllDay: event.isAllDay,\n                    uid: event.uid\n                };\n            },\n            select: function (selection) {\n                this.clearSelection();\n                var row = this.table.find('.k-task').eq(selection.index).closest('tr').addClass('k-state-selected').attr('aria-selected', true)[0];\n                this.current(row);\n            },\n            move: function (selection, key) {\n                var handled = false;\n                var index = selection.index;\n                if (key == kendo.keys.UP) {\n                    index--;\n                    handled = true;\n                } else if (key == kendo.keys.DOWN) {\n                    index++;\n                    handled = true;\n                }\n                if (handled) {\n                    var event = this._eventsList[index];\n                    if (event) {\n                        selection.start = event.start;\n                        selection.end = event.end;\n                        selection.isAllDay = event.isAllDay;\n                        selection.events = [event.uid];\n                        selection.index = index;\n                    }\n                }\n                return handled;\n            },\n            moveToEvent: function () {\n                return false;\n            },\n            constrainSelection: function (selection) {\n                var event = this._eventsList[0];\n                if (event) {\n                    selection.start = event.start;\n                    selection.end = event.end;\n                    selection.isAllDay = event.isAllDay;\n                    selection.events = [event.uid];\n                    selection.index = 0;\n                }\n            },\n            isInRange: function () {\n                return true;\n            },\n            destroy: function () {\n                if (this.element) {\n                    this.element.off(NS);\n                }\n                ui.SchedulerView.fn.destroy.call(this);\n            },\n            options: {\n                title: 'Agenda',\n                name: 'agenda',\n                editable: true,\n                selectedDateFormat: '{0:D}-{1:D}',\n                selectedShortDateFormat: '{0:d} - {1:d}',\n                selectedMobileDateFormat: '{0: MMM} {0:dd} - {1:dd}',\n                eventTemplate: '#:title#',\n                eventTimeTemplate: '#if(data.isAllDay) {#' + '#=this.options.messages.allDay#' + '#} else { #' + '#=kendo.format(format, start, end)#' + '# } #',\n                eventDateTemplate: EVENT_DATE_TEMPLATE,\n                eventGroupTemplate: EVENT_GROUP_TEMPLATE,\n                messages: {\n                    event: 'Event',\n                    date: 'Date',\n                    time: 'Time',\n                    allDay: 'all day'\n                }\n            }\n        });\n        function rowSpan(tasks) {\n            var result = 0;\n            for (var idx = 0, length = tasks.length; idx < length; idx++) {\n                result += tasks[idx].items.length;\n            }\n            return result;\n        }\n        function resourceValue(resource, item) {\n            if (resource.valuePrimitive) {\n                item = kendo.getter(resource.dataValueField)(item);\n            }\n            return item;\n        }\n        function flattenTaskGroups(groups) {\n            var idx = 0, length = groups.length, item, result = [];\n            for (; idx < length; idx++) {\n                item = groups[idx];\n                if (item.groups) {\n                    item = flattenGroup(item.groups);\n                    result = result.concat(item);\n                } else {\n                    result = result.concat(flattenGroup(item.items));\n                }\n            }\n            return result;\n        }\n        function flattenGroup(groups) {\n            var items = [].concat(groups), item = items.shift(), result = [], push = [].push;\n            while (item) {\n                if (item.groups) {\n                    push.apply(items, item.groups);\n                } else if (item.items) {\n                    push.apply(items, item.items);\n                } else {\n                    push.call(result, item);\n                }\n                item = items.shift();\n            }\n            return result;\n        }\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}