/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.gantt.list.min",["kendo.dom.min","kendo.touch.min","kendo.draganddrop.min","kendo.columnsorter.min","kendo.datetimepicker.min","kendo.editable.min"],e)}(function(){return function(e){function t(e){var t,i,n=[],r=e.className;for(t=0,i=e.level;t<i;t++)n.push(o("span",{className:r}));return n}function i(){var t=n._activeElement();t&&"body"!==t.nodeName.toLowerCase()&&e(t).blur()}var n=window.kendo,r=n.dom,o=r.element,a=r.text,d=n.support.browser,s=n.support.mobileOS,l=n.ui,h=l.Widget,c=e.extend,u=n._outerWidth,p=n._outerHeight,f=e.map,m=e.isFunction,g=d.msie&&d.version<9,v=n.keys,b={title:"Title",start:"Start Time",end:"End Time",percentComplete:"% Done",parentId:"Predecessor ID",id:"ID",orderId:"Order ID"},k="string",_=".kendoGanttList",y="click",C=".",w="<table style='visibility: hidden;'><tbody><tr style='height:{0}'><td>&nbsp;</td></tr></tbody></table>",T={wrapper:"k-treelist k-grid k-widget",header:"k-header",alt:"k-alt",rtl:"k-rtl",editCell:"k-edit-cell",group:"k-treelist-group",gridHeader:"k-grid-header",gridHeaderWrap:"k-grid-header-wrap",gridContent:"k-grid-content",gridContentWrap:"k-grid-content",selected:"k-state-selected",icon:"k-icon",iconCollapse:"k-i-collapse",iconExpand:"k-i-expand",iconHidden:"k-i-none",iconPlaceHolder:"k-icon k-i-none",input:"k-input",link:"k-link",resizeHandle:"k-resize-handle",resizeHandleInner:"k-resize-handle-inner",dropPositions:"k-i-insert-up k-i-insert-down k-i-plus k-i-insert-middle",dropTop:"k-i-insert-up",dropBottom:"k-i-insert-down",dropAdd:"k-i-plus",dropMiddle:"k-i-insert-middle",dropDenied:"k-i-cancel",dragStatus:"k-drag-status",dragClue:"k-drag-clue",dragClueText:"k-clue-text"},x=l.GanttList=h.extend({init:function(t,i){h.fn.init.call(this,t,i),0===this.options.columns.length&&this.options.columns.push("title"),this.dataSource=this.options.dataSource,this._columns(),this._layout(),this._domTrees(),this._header(),this._sortable(),this._editable(),this._selectable(),this._draggable(),this._resizable(),this._attachEvents(),this._adjustHeight(),this.bind("render",function(){var t,i;this.options.resizable&&(t=this.header.find("col"),i=this.content.find("col"),this.header.find("th").not(":last").each(function(n){var r=u(e(this));t.eq(n).width(r),i.eq(n).width(r)}),t.last().css("width","auto"),i.last().css("width","auto"))},!0)},_adjustHeight:function(){this.content&&this.content.height(this.element.height()-p(this.header.parent()))},destroy:function(){h.fn.destroy.call(this),this._reorderDraggable&&this._reorderDraggable.destroy(),this._tableDropArea&&this._tableDropArea.destroy(),this._contentDropArea&&this._contentDropArea.destroy(),this._columnResizable&&this._columnResizable.destroy(),this.touch&&this.touch.destroy(),this.timer&&clearTimeout(this.timer),this.content.off(_),this.header.find("thead").off(_),this.header.find(C+x.link).off(_),this.header=null,this.content=null,this.levels=null,n.destroy(this.element)},options:{name:"GanttList",selectable:!0,editable:!0,resizable:!1},_attachEvents:function(){var t=this,i=x.styles;t.content.on(y+_,"td > span."+i.icon+":not(."+i.iconHidden+")",function(i){var n=e(this),r=t._modelFromElement(n);r.set("expanded",!r.get("expanded")),i.stopPropagation()})},_domTrees:function(){this.headerTree=new r.Tree(this.header[0]),this.contentTree=new r.Tree(this.content[0])},_columns:function(){var e=this.options.columns,t=function(){this.field="",this.title="",this.editable=!1,this.sortable=!1};this.columns=f(e,function(e){return e=typeof e===k?{field:e,title:b[e]}:e,c(new t,e)})},_layout:function(){var t=this,i=this.options,r=this.element,o=x.styles,a=function(){var r,o=typeof i.rowHeight===k?i.rowHeight:i.rowHeight+"px",a=e(n.format(w,o));return t.content.append(a),r=p(a.find("tr")),a.remove(),r};r.addClass(o.wrapper).append("<div class='"+o.gridHeader+"'><div class='"+o.gridHeaderWrap+"'></div></div>").append("<div class='"+o.gridContentWrap+"'></div>"),this.header=r.find(C+o.gridHeaderWrap),this.content=r.find(C+o.gridContent),i.rowHeight&&(this._rowHeight=a())},_header:function(){var e=this.headerTree,t=o("colgroup",null,this._cols()),i=o("thead",{role:"rowgroup"},[o("tr",{role:"row"},this._ths())]),n=o("table",{style:{minWidth:this.options.listWidth+"px"},role:"grid"},[t,i]);e.render([n])},_render:function(e){var t,i,n,r={style:{minWidth:this.options.listWidth+"px"},tabIndex:0,role:"treegrid"};this._rowHeight&&(r.style.height=e.length*this._rowHeight+"px"),this.levels=[{field:null,value:0}],t=o("colgroup",null,this._cols()),i=o("tbody",{role:"rowgroup"},this._trs(e)),n=o("table",r,[t,i]),this.contentTree.render([n]),this.trigger("render")},_ths:function(){var e,t,i,n,r=this.columns,d=[];for(i=0,n=r.length;i<n;i++)e=r[i],t={"data-field":e.field,"data-title":e.title,className:x.styles.header,role:"columnheader"},d.push(o("th",t,[a(e.title)]));return this.options.resizable&&d.push(o("th",{className:x.styles.header,role:"columnheader"})),d},_cols:function(){var e,t,i,n,r,a=this.columns,d=[];for(n=0,r=a.length;n<r;n++)e=a[n],i=e.width,t=i&&0!==parseInt(i,10)?{style:{width:typeof i===k?i:i+"px"}}:null,d.push(o("col",t,[]));return this.options.resizable&&d.push(o("col",{style:{width:"1px"}})),d},_trs:function(e){var t,i,n,r,o,a=[],d=[],s=x.styles;for(r=0,o=e.length;r<o;r++)t=e[r],n=this._levels({idx:t.parentId,id:t.id,summary:t.summary}),i={"data-uid":t.uid,"data-level":n,role:"row"},t.summary&&(i["aria-expanded"]=t.expanded),r%2!==0&&d.push(s.alt),t.summary&&d.push(s.group),d.length&&(i.className=d.join(" ")),a.push(this._tds({task:t,attr:i,level:n})),d=[];return a},_tds:function(e){var t,i,n,r=[],a=this.columns;for(i=0,n=a.length;i<n;i++)t=a[i],r.push(this._td({task:e.task,column:t,level:e.level}));return this.options.resizable&&r.push(o("td",{role:"gridcell"})),o("tr",e.attr,r)},_td:function(e){var i,r,d,s=[],l=this.options.resourcesField,h=x.styles,c=e.task,u=e.column,p=c.get(u.field);if(u.field==l){for(p=p||[],i=[],d=0;d<p.length;d++)i.push(n.format("{0} [{1}]",p[d].get("name"),p[d].get("formatedValue")));i=i.join(", ")}else i=u.format?n.format(u.format,p):p;return"title"===u.field&&(s=t({level:e.level,className:h.iconPlaceHolder}),s.push(o("span",{className:h.icon+" "+(c.summary?c.expanded?h.iconCollapse:h.iconExpand:h.iconHidden)})),r=n.format("{0}, {1:P0}",i,c.percentComplete)),s.push(o("span",{"aria-label":r},[a(i)])),o("td",{role:"gridcell"},s)},_levels:function(e){var t,i,n,r=this.levels,o=e.summary,a=e.idx,d=e.id;for(i=0,n=r.length;i<n;i++)if(t=r[i],t.field==a)return o&&r.push({field:d,value:t.value+1}),t.value},_sortable:function(){var e,t,i,r,o,a=this,d=this.options.resourcesField,s=this.columns,l=this.header.find("th["+n.attr("field")+"]"),h=function(e){(0===a.dataSource.total()||a.editable&&a.editable.trigger("validate"))&&e.preventDefault()};for(r=0,o=l.length;r<o;r++)e=s[r],e.sortable&&e.field!==d&&(i=l.eq(r),t=i.data("kendoColumnSorter"),t&&t.destroy(),i.attr("data-"+n.ns+"field",e.field).kendoColumnSorter({dataSource:this.dataSource,change:h}));l=null},_selectable:function(){var t=this,i=this.options.selectable;i&&this.content.on(y+_,"tr",function(i){var n=e(this);t.editable&&t.editable.trigger("validate"),i.ctrlKey?t.clearSelection():t.select(n)})},select:function(e){var t=this.content.find(e),i=x.styles.selected;return t.length?(t.siblings(C+i).removeClass(i).attr("aria-selected",!1).end().addClass(i).attr("aria-selected",!0),void this.trigger("change")):this.content.find(C+i)},clearSelection:function(){var e=this.select();e.length&&(e.removeClass(x.styles.selected),this.trigger("change"))},_setDataSource:function(e){this.dataSource=e,this._sortable()},_editable:function(){var t=this,n=this.options.editable,r=x.styles,o="span."+r.icon+":not("+r.iconHidden+")",a=function(){var e=t.editable;e&&(e.end()?t._closeCell():e.trigger("validate"))},d=function(t){var n=e(t.currentTarget);n.hasClass(r.editCell)||i()};n&&n.update!==!1&&(this._startEditHandler=function(i){var n=i.currentTarget?e(i.currentTarget):i,r=t._columnFromElement(n);t.editable||r&&r.editable&&t._editCell({cell:n,column:r})},t.content.on("focusin"+_,function(){clearTimeout(t.timer),t.timer=null}).on("focusout"+_,function(){t.timer=setTimeout(a,1)}).on("keydown"+_,function(e){e.keyCode===v.ENTER&&e.preventDefault()}).on("keyup"+_,function(e){var n,r,o=e.keyCode;switch(o){case v.ENTER:i(),a();break;case v.ESC:t.editable&&(n=t._editableContainer,r=t._modelFromElement(n),t.trigger("cancel",{model:r,cell:n})||t._closeCell(!0))}}),s?t.touch=t.content.kendoTouch({filter:"td",touchstart:function(e){d(e.touch)},doubletap:function(i){e(i.touch.initialTouch).is(o)||t._startEditHandler(i.touch)}}).data("kendoTouch"):t.content.on("mousedown"+_,"td",function(e){d(e)}).on("dblclick"+_,"td",function(i){e(i.target).is(o)||t._startEditHandler(i)}))},_editCell:function(t){var i,r=this.options.resourcesField,o=x.styles,a=t.cell,d=t.column,s=this._modelFromElement(a),l=this.dataSource._createNewModel(s.toJSON()),h=l.fields[d.field]||l[d.field],c=h.validation,u=n.attr("type"),p=n.attr("bind"),f=n.attr("format"),v={name:d.field,required:!!h.validation&&h.validation.required===!0};return d.field===r?void d.editor(a,l):(this._editableContent=a.children().detach(),this._editableContainer=a,a.data("modelCopy",l),"date"!==h.type&&"date"!==e.type(h)||d.format&&!/H|m|s|F|g|u/.test(d.format)||(v[p]="value:"+d.field,v[u]="date",d.format&&(v[f]=n._extractFormat(d.format)),i=function(t,i){e('<input type="text"/>').attr(v).appendTo(t).kendoDateTimePicker({format:i.format})}),this.editable=a.addClass(o.editCell).kendoEditable({fields:{field:d.field,format:d.format,editor:d.editor||i},model:l,clearContainer:!1}).data("kendoEditable"),c&&c.dateCompare&&m(c.dateCompare)&&c.message&&(e("<span "+n.attr("for")+'="'+d.field+'" class="k-invalid-msg"/>').hide().appendTo(a),a.find("[name="+d.field+"]").attr(n.attr("dateCompare-msg"),c.message)),this.editable.bind("validate",function(e){var t=this.element.find(":kendoFocusable:first").focus();g&&t.focus(),e.preventDefault()}),void(this.trigger("edit",{model:s,cell:a})&&this._closeCell(!0)))},_closeCell:function(e){var t=x.styles,i=this._editableContainer,n=this._modelFromElement(i),r=this._columnFromElement(i),o=r.field,a=i.data("modelCopy"),d={};d[o]=a.get(o),i.empty().removeData("modelCopy").removeClass(t.editCell).append(this._editableContent),this.editable.unbind(),this.editable.destroy(),this.editable=null,this._editableContainer=null,this._editableContent=null,e||("start"===o&&(d.end=new Date(d.start.getTime()+n.duration())),this.trigger("update",{task:n,updateInfo:d}))},_draggable:function(){var t,i=this,r=null,o=!0,a=x.styles,d=n.support.isRtl(this.element),l="tr["+n.attr("level")+" = 0]:last",h={},u=this.options.editable,p=function(){r=null,t=null,o=!0,h={}},f=function(e){for(var t=e;t;){if(r.get("id")===t.get("id")){o=!1;break}t=i.dataSource.taskParent(t)}},m=function(){var i=e(t).height(),r=n.getOffset(t).top;c(t,{beforeLimit:r+.25*i,afterLimit:r+.75*i})},g=function(e){var i,r,o,d,s;t&&(i=e.location,r=a.dropAdd,o="add",d=parseInt(t.attr(n.attr("level")),10),i<=t.beforeLimit?(s=t.prev(),r=a.dropTop,o="insert-before"):i>=t.afterLimit&&(s=t.next(),r=a.dropBottom,o="insert-after"),s&&parseInt(s.attr(n.attr("level")),10)===d&&(r=a.dropMiddle),h.className=r,h.command=o)},v=function(){return i._reorderDraggable.hint.children(C+a.dragStatus).removeClass(a.dropPositions)};u&&u.reorder!==!1&&u.update!==!1&&(this._reorderDraggable=this.content.kendoDraggable({distance:10,holdToDrag:s,group:"listGroup",filter:"tr[data-uid]",ignore:C+a.input,hint:function(t){return e('<div class="'+a.header+" "+a.dragClue+'"/>').css({width:300,paddingLeft:t.css("paddingLeft"),paddingRight:t.css("paddingRight"),lineHeight:t.height()+"px",paddingTop:t.css("paddingTop"),paddingBottom:t.css("paddingBottom")}).append('<span class="'+a.icon+" "+a.dragStatus+'" /><span class="'+a.dragClueText+'"/>')},cursorOffset:{top:-20,left:0},container:this.content,dragstart:function(e){var t=i.editable;return t&&t.reorder!==!1&&t.trigger("validate")?void e.preventDefault():(r=i._modelFromElement(e.currentTarget),this.hint.children(C+a.dragClueText).text(r.get("title")),void(d&&this.hint.addClass(a.rtl)))},drag:function(e){o&&(g(e.y),v().addClass(h.className))},dragend:function(){p()},dragcancel:function(){p()}}).data("kendoDraggable"),this._tableDropArea=this.content.kendoDropTargetArea({distance:0,group:"listGroup",filter:"tr[data-uid]",dragenter:function(e){t=e.dropTarget,f(i._modelFromElement(t)),m(),v().toggleClass(a.dropDenied,!o)},dragleave:function(){o=!0,v()},drop:function(){var e=i._modelFromElement(t),n=e.orderId,a={parentId:e.parentId};if(o){switch(h.command){case"add":a.parentId=e.id;break;case"insert-before":a.orderId=e.parentId===r.parentId&&e.orderId>r.orderId?n-1:n;break;case"insert-after":a.orderId=e.parentId===r.parentId&&e.orderId>r.orderId?n:n+1}i.trigger("update",{task:r,updateInfo:a})}}}).data("kendoDropTargetArea"),this._contentDropArea=this.element.kendoDropTargetArea({distance:0,group:"listGroup",filter:C+a.gridContent,drop:function(){var e=i._modelFromElement(i.content.find(l)),t=e.orderId,n={parentId:null,orderId:null!==r.parentId?t+1:t};i.trigger("update",{task:r,updateInfo:n})}}).data("kendoDropTargetArea"))},_resizable:function(){var t=this,i=x.styles,n=function(n){var r,o,a=e(n.currentTarget),d=t.resizeHandle,s=a.position(),l=s.left,h=u(a),c=a.closest("div"),f=n.clientX+e(window).scrollLeft(),m=t.options.columnResizeHandleWidth;return l+=c.scrollLeft(),d||(d=t.resizeHandle=e('<div class="'+i.resizeHandle+'"><div class="'+i.resizeHandleInner+'" /></div>')),r=a.offset().left+h,(o=f>r-m&&f<r+m)?(c.append(d),void d.show().css({top:s.top,left:l+h-m-1,height:p(a),width:3*m}).data("th",a)):void d.hide()};this.options.resizable&&(this._columnResizable&&this._columnResizable.destroy(),this.header.find("thead").on("mousemove"+_,"th",n),this._columnResizable=this.header.kendoResizable({handle:C+i.resizeHandle,start:function(i){var n=e(i.currentTarget).data("th"),r="col:eq("+n.index()+")",o=t.header.find("table"),a=t.content.find("table");t.element.addClass("k-grid-column-resizing"),this.col=a.children("colgroup").find(r).add(o.find(r)),this.th=n,this.startLocation=i.x.location,this.columnWidth=u(n),this.table=o.add(a),this.totalWidth=this.table.width()-u(o.find("th:last"))},resize:function(e){var t=11,i=e.x.location-this.startLocation;this.columnWidth+i<t&&(i=t-this.columnWidth),this.table.css({minWidth:this.totalWidth+i}),this.col.width(this.columnWidth+i)},resizeend:function(){var e,i,n;t.element.removeClass("k-grid-column-resizing"),e=Math.floor(this.columnWidth),i=Math.floor(u(this.th)),n=t.columns[this.th.index()],t.trigger("columnResize",{column:n,oldWidth:e,newWidth:i}),this.table=this.col=this.th=null}}).data("kendoResizable"))},_modelFromElement:function(e){var t=e.closest("tr"),i=this.dataSource.getByUid(t.attr(n.attr("uid")));return i},_columnFromElement:function(e){var t=e.closest("td"),i=t.parent(),n=i.children().index(t);return this.columns[n]}});c(!0,l.GanttList,{styles:T})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.gantt.list.min.js.map
