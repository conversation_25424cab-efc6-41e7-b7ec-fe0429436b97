{"version": 3, "sources": ["kendo.progressbar.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "Widget", "HORIZONTAL", "VERTICAL", "DEFAULTMIN", "DEFAULTMAX", "DEFAULTVALUE", "DEFAULTCHUNKCOUNT", "KPROGRESSBAR", "KPROGRESSBARREVERSE", "KPROGRESSBARINDETERMINATE", "KPROGRESSBARCOMPLETE", "KPROGRESSWRAPPER", "KPROGRESSSTATUS", "KCOMPLETEDCHUNK", "KUPCOMINGCHUNK", "KSTATEDISABLED", "PROGRESSTYPE", "VALUE", "PERCENT", "CHUNK", "CHANGE", "COMPLETE", "BOOLEAN", "math", "Math", "extend", "proxy", "HUNDREDPERCENT", "DEFAULTANIMATIONDURATION", "PRECISION", "templates", "progressStatus", "ProgressBar", "init", "element", "options", "that", "this", "fn", "call", "_progressProperty", "orientation", "_fields", "value", "_validateValue", "_validateType", "type", "_wrapper", "_progressAnimation", "min", "_updateProgress", "setOptions", "hasOwnProperty", "wrapper", "toggleClass", "reverse", "enable", "events", "name", "max", "chunkCount", "showStatus", "animation", "_isStarted", "progressWrapper", "currentType", "<PERSON><PERSON><PERSON><PERSON>", "each", "k", "Error", "format", "initialStatusValue", "container", "addClass", "_addChunkProgressWrapper", "prepend", "find", "text", "_calculatePercentage", "toFixed", "_value", "validated", "_roundValue", "isNaN", "removeClass", "parseFloat", "power", "pow", "floor", "percentage", "_updateChunks", "_onProgressUpdateAlways", "_updateProgressWrapper", "completedChunks", "percentagesPerChunk", "parseInt", "percentageParsed", "completedChunksCount", "animationDuration", "_animation", "duration", "animationCssOptions", "length", "_addRegularProgressWrapper", "animate", "start", "_onProgressAnimateStart", "progress", "_onProgressAnimate", "complete", "_onProgressAnimateComplete", "always", "show", "e", "progressStatusWrapSize", "progressInPercent", "elem", "style", "css", "currentValue", "progressValue", "progressWrapperSize", "hide", "trigger", "destroy", "i", "chunkSize", "html", "append", "first", "end", "last", "_normalizeChunkSize", "lastChunk", "currentSize", "difference", "appendTo", "_calculateChunkSize", "chunkContainer", "_onePercent", "abs", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,WAkUE,MA1TC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAASD,EAAGC,OAAQC,EAAa,aAAcC,EAAW,WAAYC,EAAa,EAAGC,EAAa,IAAKC,EAAe,EAAGC,EAAoB,EAAGC,EAAe,gBAAiBC,EAAsB,wBAAyBC,EAA4B,8BAA+BC,EAAuB,aAAcC,EAAmB,mBAAoBC,EAAkB,oBAAqBC,EAAkB,mBAAoBC,EAAiB,kBAAmBC,EAAiB,mBAAoBC,GAC9hBC,MAAO,QACPC,QAAS,UACTC,MAAO,SACRC,EAAS,SAAUC,EAAW,WAAYC,EAAU,UAAWC,EAAOC,KAAMC,EAAS9B,EAAE8B,OAAQC,EAAQ/B,EAAE+B,MAAOC,EAAiB,IAAKC,EAA2B,IAAKC,EAAY,EAAGC,GAAcC,eAAgB,uFACtNC,EAAchC,EAAOyB,QACrBQ,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOC,IACXrC,GAAOsC,GAAGL,KAAKM,KAAKF,KAAMH,EAASC,GACnCA,EAAUC,EAAKD,QACfC,EAAKI,kBAAoBL,EAAQM,cAAgBxC,EAAa,QAAU,SACxEmC,EAAKM,UACLP,EAAQQ,MAAQP,EAAKQ,eAAeT,EAAQQ,OAC5CP,EAAKS,cAAcV,EAAQW,MAC3BV,EAAKW,WACLX,EAAKY,qBACDb,EAAQQ,QAAUR,EAAQc,KAAOd,EAAQQ,SAAU,GACnDP,EAAKc,mBAGbC,WAAY,SAAUhB,GAClB,GAAIC,GAAOC,IACXrC,GAAOsC,GAAGa,WAAWZ,KAAKH,EAAMD,GAC5BA,EAAQiB,eAAe,YACvBhB,EAAKiB,QAAQC,YAAY,wBAAyBnB,EAAQoB,SAE1DpB,EAAQiB,eAAe,WACvBhB,EAAKoB,OAAOrB,EAAQqB,QAExBpB,EAAKY,qBACLZ,EAAKQ,iBACLR,EAAKc,mBAETO,QACIrC,EACAC,GAEJc,SACIuB,KAAM,cACNjB,YAAaxC,EACbsD,SAAS,EACTN,IAAK9C,EACLwD,IAAKvD,EACLuC,MAAOtC,EACPmD,QAAQ,EACRV,KAAM9B,EAAaC,MACnB2C,WAAYtD,EACZuD,YAAY,EACZC,cAEJpB,QAAS,WACL,GAAIN,GAAOC,IACXD,GAAK2B,YAAa,EAClB3B,EAAK4B,gBAAkB5B,EAAKL,eAAiBpC,KAEjDkD,cAAe,SAAUoB,GACrB,GAAIC,IAAU,CAOd,IANAvE,EAAEwE,KAAKnD,EAAc,SAAUoD,EAAGtB,GAC9B,GAAIA,IAASmB,EAET,MADAC,IAAU,GACH,KAGVA,EACD,KAAUG,OAAMxE,EAAMyE,OAAO,iCAAoCL,KAGzElB,SAAU,WAAA,GAKFwB,GAJAnC,EAAOC,KACPmC,EAAYpC,EAAKiB,QAAUjB,EAAKF,QAChCC,EAAUC,EAAKD,QACfM,EAAcN,EAAQM,WAE1B+B,GAAUC,SAAS,YAAclE,GACjCiE,EAAUC,SAASlE,EAAe,KAAOkC,IAAgBxC,EAAaA,EAAaC,IAC/EiC,EAAQqB,UAAW,GACnBgB,EAAUC,SAAS1D,GAEnBoB,EAAQoB,SACRiB,EAAUC,SAASjE,GAEnB2B,EAAQQ,SAAU,GAClB6B,EAAUC,SAAShE,GAEnB0B,EAAQW,OAAS9B,EAAaG,MAC9BiB,EAAKsC,2BAEDvC,EAAQ0B,aACRzB,EAAKL,eAAiBK,EAAKiB,QAAQsB,QAAQ7C,EAAUC,gBAAgB6C,KAAK,IAAMhE,GAChF2D,EAAqBpC,EAAQQ,SAAU,EAAQR,EAAQQ,MAAQR,EAAQc,IAEnEb,EAAKL,eAAe8C,KADpB1C,EAAQW,OAAS9B,EAAaC,MACLsD,EAEAnC,EAAK0C,qBAAqBP,GAAoBQ,UAAY,OAKnGpC,MAAO,SAAUA,GACb,MAAON,MAAK2C,OAAOrC,IAEvBqC,OAAQ,SAAUrC,GAAV,GAGAsC,GAFA7C,EAAOC,KACPF,EAAUC,EAAKD,OAEnB,OAAIQ,KAAU/C,EACHuC,EAAQQ,aAEJA,KAAUrB,GACjBqB,EAAQP,EAAK8C,YAAYvC,GACpBwC,MAAMxC,KACPsC,EAAY7C,EAAKQ,eAAeD,GAC5BsC,IAAc9C,EAAQQ,QACtBP,EAAKiB,QAAQ+B,YAAY3E,GACzB0B,EAAQQ,MAAQsC,EAChB7C,EAAK2B,YAAa,EAClB3B,EAAKc,qBAGLP,IACRP,EAAKiB,QAAQoB,SAAShE,GACtB0B,EAAQQ,OAAQ,GAbpB,IAiBRuC,YAAa,SAAUvC,GACnBA,EAAQ0C,WAAW1C,EACnB,IAAI2C,GAAQ/D,EAAKgE,IAAI,GAAI1D,EACzB,OAAON,GAAKiE,MAAM7C,EAAQ2C,GAASA,GAEvC1C,eAAgB,SAAUD,GAAV,GACRP,GAAOC,KACPF,EAAUC,EAAKD,OACnB,IAAIQ,KAAU,EAAO,CACjB,GAAIA,GAASR,EAAQc,KAAON,KAAU,EAClC,MAAOR,GAAQc,GACZ,IAAIN,GAASR,EAAQwB,IACxB,MAAOxB,GAAQwB,QAEhB,IAAIhB,KAAU,EACjB,OAAO,CAEX,OAAIwC,OAAM/C,EAAK8C,YAAYvC,IAChBR,EAAQc,IAEZN,GAEXO,gBAAiB,WAAA,GACTd,GAAOC,KACPF,EAAUC,EAAKD,QACfsD,EAAarD,EAAK0C,sBAClB3C,GAAQW,OAAS9B,EAAaG,OAC9BiB,EAAKsD,cAAcD,GACnBrD,EAAKuD,wBAAwBxD,EAAQQ,QAErCP,EAAKwD,uBAAuBH,IAGpCC,cAAe,SAAUD,GAAV,GAOPI,GANAzD,EAAOC,KACPF,EAAUC,EAAKD,QACfyB,EAAazB,EAAQyB,WACrBkC,EAAsBC,SAASpE,EAAiBiC,EAAa,IAAK,IAAM,IACxEoC,EAAmBD,SAAsB,IAAbN,EAAkB,IAAM,IACpDQ,EAAuB1E,EAAKiE,MAAMQ,EAAmBF,EAGrDD,GAAkBzD,EAAKiB,QAAQuB,KAD/BzC,EAAQM,cAAgBxC,IAAekC,EAAQoB,SAAWpB,EAAQM,cAAgBvC,GAAYiC,EAAQoB,QAClE,gBAAkB0C,EAAuB,IAEzC,kBAAoBA,EAAuB,GAAK,KAExF7D,EAAKiB,QAAQuB,KAAK,IAAM/D,GAAiBuE,YAAYvE,GAAiB4D,SAAS3D,GAC/E+E,EAAgBT,YAAYtE,GAAgB2D,SAAS5D,IAEzD+E,uBAAwB,SAAUH,GAAV,GAChBrD,GAAOC,KACPF,EAAUC,EAAKD,QACf6B,EAAkB5B,EAAKiB,QAAQuB,KAAK,IAAMjE,GAC1CuF,EAAoB9D,EAAK2B,WAAa3B,EAAK+D,WAAWC,SAAW,EACjEC,IAC2B,KAA3BrC,EAAgBsC,QAChBlE,EAAKmE,6BAETF,EAAoBjE,EAAKI,mBAAqBiD,EAAa,IAC3DrD,EAAK4B,gBAAgBwC,QAAQH,GACzBD,SAAUF,EACVO,MAAO/E,EAAMU,EAAKsE,wBAAyBtE,GAC3CuE,SAAUjF,EAAMU,EAAKwE,mBAAoBxE,GACzCyE,SAAUnF,EAAMU,EAAK0E,2BAA4B1E,EAAMD,EAAQQ,OAC/DoE,OAAQrF,EAAMU,EAAKuD,wBAAyBvD,EAAMD,EAAQQ,UAGlE+D,wBAAyB,WACrBrE,KAAK2B,gBAAgBgD,QAEzBJ,mBAAoB,SAAUK,GAAV,GAIZC,GAHA9E,EAAOC,KACPF,EAAUC,EAAKD,QACfgF,EAAoB9B,WAAW4B,EAAEG,KAAKC,MAAMjF,EAAKI,mBAAoB,GAErEL,GAAQ0B,aACRqD,EAAyB,IAAQ7B,WAAWjD,EAAK4B,gBAAgB,GAAGqD,MAAMjF,EAAKI,oBAC/EJ,EAAK4B,gBAAgBY,KAAK,2BAA2B0C,IAAIlF,EAAKI,kBAAmB0E,EAAyB,MAE1G/E,EAAQW,OAAS9B,EAAaG,OAASgG,GAAqB,IAC5D/E,EAAK4B,gBAAgBoB,YAAY1E,IAGzCoG,2BAA4B,SAAUS,GAAV,GAIpBC,GAHApF,EAAOC,KACPF,EAAUC,EAAKD,QACfsF,EAAsBpC,WAAWjD,EAAK4B,gBAAgB,GAAGqD,MAAMjF,EAAKI,mBAEpEL,GAAQW,OAAS9B,EAAaG,OAASsG,EAAsB,IAC7DrF,EAAK4B,gBAAgBS,SAAS/D,GAE9ByB,EAAQ0B,aAEJ2D,EADArF,EAAQW,OAAS9B,EAAaC,MACdsG,EACTpF,EAAQW,MAAQ9B,EAAaE,QACpBkB,EAAK0C,qBAAqByC,GAAcxC,UAAY,IAEpDxD,EAAKiE,MAAMpD,EAAK0C,qBAAqByC,IAAiB,IAE1EnF,EAAKL,eAAe8C,KAAK2C,IAEzBD,IAAiBpF,EAAQc,KACzBb,EAAK4B,gBAAgB0D,QAG7B/B,wBAAyB,SAAU4B,GAAV,GACjBnF,GAAOC,KACPF,EAAUC,EAAKD,OACfC,GAAK2B,YACL3B,EAAKuF,QAAQvG,GAAUuB,MAAO4E,IAE9BA,IAAiBpF,EAAQwB,KAAOvB,EAAK2B,YACrC3B,EAAKuF,QAAQtG,GAAYsB,MAAOR,EAAQwB,OAGhDH,OAAQ,SAAUA,GAAV,GACApB,GAAOC,KACPF,EAAUC,EAAKD,OACnBA,GAAQqB,OAA2B,IAAXA,GAAgCA,EACxDpB,EAAKiB,QAAQC,YAAYvC,GAAiBoB,EAAQqB,SAEtDoE,QAAS,WACL,GAAIxF,GAAOC,IACXrC,GAAOsC,GAAGsF,QAAQrF,KAAKH,IAE3BsC,yBAA0B,WAAA,GAUbmD,GATLzF,EAAOC,KACPF,EAAUC,EAAKD,QACfqC,EAAYpC,EAAKiB,QACjByE,EAAYnG,EAAiBQ,EAAQyB,WACrCmE,EAAO,EAKX,KAJI5F,EAAQyB,YAAc,IACtBzB,EAAQyB,WAAa,GAEzBmE,GAAQ,uBACCF,EAAI1F,EAAQyB,WAAa,EAAGiE,GAAK,EAAGA,IACzCE,GAAQ,0CAEZA,IAAQ,QACRvD,EAAUwD,OAAOD,GAAMnD,KAAK,WAAW0C,IAAIlF,EAAKI,kBAAmBsF,EAAY,KAAKG,QAAQxD,SAAS,WAAWyD,MAAMC,OAAO1D,SAAS,UACtIrC,EAAKgG,uBAETA,oBAAqB,WAAA,GACbhG,GAAOC,KACPF,EAAUC,EAAKD,QACfkG,EAAYjG,EAAKiB,QAAQuB,KAAK,gBAC9B0D,EAAcjD,WAAWgD,EAAU,GAAGhB,MAAMjF,EAAKI,oBACjD+F,EAAa5G,EAAiBQ,EAAQyB,WAAa0E,CACnDC,GAAa,GACbF,EAAUf,IAAIlF,EAAKI,kBAAmB8F,EAAcC,EAAa,MAGzEhC,2BAA4B,WACxB,GAAInE,GAAOC,IACXD,GAAK4B,gBAAkBrE,EAAE,eAAkBgB,EAAmB,YAAa6H,SAASpG,EAAKiB,SACrFjB,EAAKD,QAAQ0B,aACbzB,EAAK4B,gBAAgBgE,OAAOlG,EAAUC,gBACtCK,EAAKL,eAAiBK,EAAKiB,QAAQuB,KAAK,IAAMhE,KAGtD6H,oBAAqB,WAAA,GACbrG,GAAOC,KACPuB,EAAaxB,EAAKD,QAAQyB,WAC1B8E,EAAiBtG,EAAKiB,QAAQuB,KAAK,aACvC,QAAQmB,SAAS2C,EAAepB,IAAIlF,EAAKI,mBAAoB,KAAOoB,EAAa,IAAMA,GAE3FkB,qBAAsB,SAAUyC,GAAV,GACdnF,GAAOC,KACPF,EAAUC,EAAKD,QACfQ,EAAQ4E,IAAiB3H,EAAY2H,EAAepF,EAAQQ,MAC5DM,EAAMd,EAAQc,IACdU,EAAMxB,EAAQwB,GAElB,OADAvB,GAAKuG,YAAcpH,EAAKqH,KAAKjF,EAAMV,GAAO,KACnC1B,EAAKqH,KAAKjG,EAAQM,GAAOb,EAAKuG,cAEzC3F,mBAAoB,WAAA,GACZZ,GAAOC,KACPF,EAAUC,EAAKD,QACf2B,EAAY3B,EAAQ2B,SAEpB1B,GAAK+D,WADLrC,KAAc,GACMsC,SAAU,GAEZ3E,GAAS2E,SAAUxE,GAA4BO,EAAQ2B,aAIrFjE,GAAME,GAAG8I,OAAO7G,IAClBlC,OAAOD,MAAMiJ,QACRhJ,OAAOD,OACE,kBAAVH,SAAwBA,OAAOqJ,IAAMrJ,OAAS,SAAUsJ,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.progressbar.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.progressbar', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'progressbar',\n        name: 'ProgressBar',\n        category: 'web',\n        description: 'The ProgressBar offers rich functionality for displaying and tracking progress',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Widget = ui.Widget, HORIZONTAL = 'horizontal', VERTICAL = 'vertical', DEFAULTMIN = 0, DEFAULTMAX = 100, DEFAULTVALUE = 0, DEFAULTCHUNKCOUNT = 5, KPROGRESSBAR = 'k-progressbar', KPROGRESSBARREVERSE = 'k-progressbar-reverse', KPROGRESSBARINDETERMINATE = 'k-progressbar-indeterminate', KPROGRESSBARCOMPLETE = 'k-complete', KPROGRESSWRAPPER = 'k-state-selected', KPROGRESSSTATUS = 'k-progress-status', KCOMPLETEDCHUNK = 'k-state-selected', KUPCOMINGCHUNK = 'k-state-default', KSTATEDISABLED = 'k-state-disabled', PROGRESSTYPE = {\n                VALUE: 'value',\n                PERCENT: 'percent',\n                CHUNK: 'chunk'\n            }, CHANGE = 'change', COMPLETE = 'complete', BOOLEAN = 'boolean', math = Math, extend = $.extend, proxy = $.proxy, HUNDREDPERCENT = 100, DEFAULTANIMATIONDURATION = 400, PRECISION = 3, templates = { progressStatus: '<span class=\\'k-progress-status-wrap\\'><span class=\\'k-progress-status\\'></span></span>' };\n        var ProgressBar = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(this, element, options);\n                options = that.options;\n                that._progressProperty = options.orientation === HORIZONTAL ? 'width' : 'height';\n                that._fields();\n                options.value = that._validateValue(options.value);\n                that._validateType(options.type);\n                that._wrapper();\n                that._progressAnimation();\n                if (options.value !== options.min && options.value !== false) {\n                    that._updateProgress();\n                }\n            },\n            setOptions: function (options) {\n                var that = this;\n                Widget.fn.setOptions.call(that, options);\n                if (options.hasOwnProperty('reverse')) {\n                    that.wrapper.toggleClass('k-progressbar-reverse', options.reverse);\n                }\n                if (options.hasOwnProperty('enable')) {\n                    that.enable(options.enable);\n                }\n                that._progressAnimation();\n                that._validateValue();\n                that._updateProgress();\n            },\n            events: [\n                CHANGE,\n                COMPLETE\n            ],\n            options: {\n                name: 'ProgressBar',\n                orientation: HORIZONTAL,\n                reverse: false,\n                min: DEFAULTMIN,\n                max: DEFAULTMAX,\n                value: DEFAULTVALUE,\n                enable: true,\n                type: PROGRESSTYPE.VALUE,\n                chunkCount: DEFAULTCHUNKCOUNT,\n                showStatus: true,\n                animation: {}\n            },\n            _fields: function () {\n                var that = this;\n                that._isStarted = false;\n                that.progressWrapper = that.progressStatus = $();\n            },\n            _validateType: function (currentType) {\n                var isValid = false;\n                $.each(PROGRESSTYPE, function (k, type) {\n                    if (type === currentType) {\n                        isValid = true;\n                        return false;\n                    }\n                });\n                if (!isValid) {\n                    throw new Error(kendo.format('Invalid ProgressBar type \\'{0}\\'', currentType));\n                }\n            },\n            _wrapper: function () {\n                var that = this;\n                var container = that.wrapper = that.element;\n                var options = that.options;\n                var orientation = options.orientation;\n                var initialStatusValue;\n                container.addClass('k-widget ' + KPROGRESSBAR);\n                container.addClass(KPROGRESSBAR + '-' + (orientation === HORIZONTAL ? HORIZONTAL : VERTICAL));\n                if (options.enable === false) {\n                    container.addClass(KSTATEDISABLED);\n                }\n                if (options.reverse) {\n                    container.addClass(KPROGRESSBARREVERSE);\n                }\n                if (options.value === false) {\n                    container.addClass(KPROGRESSBARINDETERMINATE);\n                }\n                if (options.type === PROGRESSTYPE.CHUNK) {\n                    that._addChunkProgressWrapper();\n                } else {\n                    if (options.showStatus) {\n                        that.progressStatus = that.wrapper.prepend(templates.progressStatus).find('.' + KPROGRESSSTATUS);\n                        initialStatusValue = options.value !== false ? options.value : options.min;\n                        if (options.type === PROGRESSTYPE.VALUE) {\n                            that.progressStatus.text(initialStatusValue);\n                        } else {\n                            that.progressStatus.text(that._calculatePercentage(initialStatusValue).toFixed() + '%');\n                        }\n                    }\n                }\n            },\n            value: function (value) {\n                return this._value(value);\n            },\n            _value: function (value) {\n                var that = this;\n                var options = that.options;\n                var validated;\n                if (value === undefined) {\n                    return options.value;\n                } else {\n                    if (typeof value !== BOOLEAN) {\n                        value = that._roundValue(value);\n                        if (!isNaN(value)) {\n                            validated = that._validateValue(value);\n                            if (validated !== options.value) {\n                                that.wrapper.removeClass(KPROGRESSBARINDETERMINATE);\n                                options.value = validated;\n                                that._isStarted = true;\n                                that._updateProgress();\n                            }\n                        }\n                    } else if (!value) {\n                        that.wrapper.addClass(KPROGRESSBARINDETERMINATE);\n                        options.value = false;\n                    }\n                }\n            },\n            _roundValue: function (value) {\n                value = parseFloat(value);\n                var power = math.pow(10, PRECISION);\n                return math.floor(value * power) / power;\n            },\n            _validateValue: function (value) {\n                var that = this;\n                var options = that.options;\n                if (value !== false) {\n                    if (value <= options.min || value === true) {\n                        return options.min;\n                    } else if (value >= options.max) {\n                        return options.max;\n                    }\n                } else if (value === false) {\n                    return false;\n                }\n                if (isNaN(that._roundValue(value))) {\n                    return options.min;\n                }\n                return value;\n            },\n            _updateProgress: function () {\n                var that = this;\n                var options = that.options;\n                var percentage = that._calculatePercentage();\n                if (options.type === PROGRESSTYPE.CHUNK) {\n                    that._updateChunks(percentage);\n                    that._onProgressUpdateAlways(options.value);\n                } else {\n                    that._updateProgressWrapper(percentage);\n                }\n            },\n            _updateChunks: function (percentage) {\n                var that = this;\n                var options = that.options;\n                var chunkCount = options.chunkCount;\n                var percentagesPerChunk = parseInt(HUNDREDPERCENT / chunkCount * 100, 10) / 100;\n                var percentageParsed = parseInt(percentage * 100, 10) / 100;\n                var completedChunksCount = math.floor(percentageParsed / percentagesPerChunk);\n                var completedChunks;\n                if (options.orientation === HORIZONTAL && !options.reverse || options.orientation === VERTICAL && options.reverse) {\n                    completedChunks = that.wrapper.find('li.k-item:lt(' + completedChunksCount + ')');\n                } else {\n                    completedChunks = that.wrapper.find('li.k-item:gt(-' + (completedChunksCount + 1) + ')');\n                }\n                that.wrapper.find('.' + KCOMPLETEDCHUNK).removeClass(KCOMPLETEDCHUNK).addClass(KUPCOMINGCHUNK);\n                completedChunks.removeClass(KUPCOMINGCHUNK).addClass(KCOMPLETEDCHUNK);\n            },\n            _updateProgressWrapper: function (percentage) {\n                var that = this;\n                var options = that.options;\n                var progressWrapper = that.wrapper.find('.' + KPROGRESSWRAPPER);\n                var animationDuration = that._isStarted ? that._animation.duration : 0;\n                var animationCssOptions = {};\n                if (progressWrapper.length === 0) {\n                    that._addRegularProgressWrapper();\n                }\n                animationCssOptions[that._progressProperty] = percentage + '%';\n                that.progressWrapper.animate(animationCssOptions, {\n                    duration: animationDuration,\n                    start: proxy(that._onProgressAnimateStart, that),\n                    progress: proxy(that._onProgressAnimate, that),\n                    complete: proxy(that._onProgressAnimateComplete, that, options.value),\n                    always: proxy(that._onProgressUpdateAlways, that, options.value)\n                });\n            },\n            _onProgressAnimateStart: function () {\n                this.progressWrapper.show();\n            },\n            _onProgressAnimate: function (e) {\n                var that = this;\n                var options = that.options;\n                var progressInPercent = parseFloat(e.elem.style[that._progressProperty], 10);\n                var progressStatusWrapSize;\n                if (options.showStatus) {\n                    progressStatusWrapSize = 10000 / parseFloat(that.progressWrapper[0].style[that._progressProperty]);\n                    that.progressWrapper.find('.k-progress-status-wrap').css(that._progressProperty, progressStatusWrapSize + '%');\n                }\n                if (options.type !== PROGRESSTYPE.CHUNK && progressInPercent <= 98) {\n                    that.progressWrapper.removeClass(KPROGRESSBARCOMPLETE);\n                }\n            },\n            _onProgressAnimateComplete: function (currentValue) {\n                var that = this;\n                var options = that.options;\n                var progressWrapperSize = parseFloat(that.progressWrapper[0].style[that._progressProperty]);\n                var progressValue;\n                if (options.type !== PROGRESSTYPE.CHUNK && progressWrapperSize > 98) {\n                    that.progressWrapper.addClass(KPROGRESSBARCOMPLETE);\n                }\n                if (options.showStatus) {\n                    if (options.type === PROGRESSTYPE.VALUE) {\n                        progressValue = currentValue;\n                    } else if (options.type == PROGRESSTYPE.PERCENT) {\n                        progressValue = that._calculatePercentage(currentValue).toFixed() + '%';\n                    } else {\n                        progressValue = math.floor(that._calculatePercentage(currentValue)) + '%';\n                    }\n                    that.progressStatus.text(progressValue);\n                }\n                if (currentValue === options.min) {\n                    that.progressWrapper.hide();\n                }\n            },\n            _onProgressUpdateAlways: function (currentValue) {\n                var that = this;\n                var options = that.options;\n                if (that._isStarted) {\n                    that.trigger(CHANGE, { value: currentValue });\n                }\n                if (currentValue === options.max && that._isStarted) {\n                    that.trigger(COMPLETE, { value: options.max });\n                }\n            },\n            enable: function (enable) {\n                var that = this;\n                var options = that.options;\n                options.enable = typeof enable === 'undefined' ? true : enable;\n                that.wrapper.toggleClass(KSTATEDISABLED, !options.enable);\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n            },\n            _addChunkProgressWrapper: function () {\n                var that = this;\n                var options = that.options;\n                var container = that.wrapper;\n                var chunkSize = HUNDREDPERCENT / options.chunkCount;\n                var html = '';\n                if (options.chunkCount <= 1) {\n                    options.chunkCount = 1;\n                }\n                html += '<ul class=\\'k-reset\\'>';\n                for (var i = options.chunkCount - 1; i >= 0; i--) {\n                    html += '<li class=\\'k-item k-state-default\\'></li>';\n                }\n                html += '</ul>';\n                container.append(html).find('.k-item').css(that._progressProperty, chunkSize + '%').first().addClass('k-first').end().last().addClass('k-last');\n                that._normalizeChunkSize();\n            },\n            _normalizeChunkSize: function () {\n                var that = this;\n                var options = that.options;\n                var lastChunk = that.wrapper.find('.k-item:last');\n                var currentSize = parseFloat(lastChunk[0].style[that._progressProperty]);\n                var difference = HUNDREDPERCENT - options.chunkCount * currentSize;\n                if (difference > 0) {\n                    lastChunk.css(that._progressProperty, currentSize + difference + '%');\n                }\n            },\n            _addRegularProgressWrapper: function () {\n                var that = this;\n                that.progressWrapper = $('<div class=\\'' + KPROGRESSWRAPPER + '\\'></div>').appendTo(that.wrapper);\n                if (that.options.showStatus) {\n                    that.progressWrapper.append(templates.progressStatus);\n                    that.progressStatus = that.wrapper.find('.' + KPROGRESSSTATUS);\n                }\n            },\n            _calculateChunkSize: function () {\n                var that = this;\n                var chunkCount = that.options.chunkCount;\n                var chunkContainer = that.wrapper.find('ul.k-reset');\n                return (parseInt(chunkContainer.css(that._progressProperty), 10) - (chunkCount - 1)) / chunkCount;\n            },\n            _calculatePercentage: function (currentValue) {\n                var that = this;\n                var options = that.options;\n                var value = currentValue !== undefined ? currentValue : options.value;\n                var min = options.min;\n                var max = options.max;\n                that._onePercent = math.abs((max - min) / 100);\n                return math.abs((value - min) / that._onePercent);\n            },\n            _progressAnimation: function () {\n                var that = this;\n                var options = that.options;\n                var animation = options.animation;\n                if (animation === false) {\n                    that._animation = { duration: 0 };\n                } else {\n                    that._animation = extend({ duration: DEFAULTANIMATIONDURATION }, options.animation);\n                }\n            }\n        });\n        kendo.ui.plugin(ProgressBar);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}