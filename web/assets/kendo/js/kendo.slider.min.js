/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.slider.min",["kendo.draganddrop.min"],e)}(function(){return function(e,t){function n(e,t,n){var i=n?" k-slider-horizontal":" k-slider-vertical",a=e.style?e.style:t.attr("style"),o=t.attr("class")?" "+t.attr("class"):"",r="";return"bottomRight"==e.tickPlacement?r=" k-slider-bottomright":"topLeft"==e.tickPlacement&&(r=" k-slider-topleft"),a=a?" style='"+a+"'":"","<div class='k-widget k-slider"+i+o+"'"+a+"><div class='k-slider-wrap"+(e.showButtons?" k-slider-buttons":"")+r+"'></div></div>"}function i(e,t,n,i){var a="";return a=n?!i&&"increase"==t||i&&"increase"!=t?"k-i-arrow-60-right":"k-i-arrow-60-left":"increase"==t?"k-i-arrow-60-up":"k-i-arrow-60-down","<a class='k-button k-button-"+t+"' title='"+e[t+"ButtonTitle"]+"' aria-label='"+e[t+"ButtonTitle"]+"'><span class='k-icon "+a+"'></span></a>"}function a(e,t){var n,i="<ul class='k-reset k-slider-items'>",a=H.floor(u(t/e.smallStep))+1;for(n=0;n<a;n++)i+="<li class='k-tick' role='presentation'>&nbsp;</li>";return i+="</ul>"}function o(e,t){var n=t.is("input")?1:2,i=2==n?e.leftDragHandleTitle:e.dragHandleTitle;return"<div class='k-slider-track'><div class='k-slider-selection'><!-- --></div><a href='#' class='k-draghandle' title='"+i+"' role='slider' aria-valuemin='"+e.min+"' aria-valuemax='"+e.max+"' aria-valuenow='"+(n>1?e.selectionStart||e.min:e.value||e.min)+"'>Drag</a>"+(n>1?"<a href='#' class='k-draghandle' title='"+e.rightDragHandleTitle+"'role='slider' aria-valuemin='"+e.min+"' aria-valuemax='"+e.max+"' aria-valuenow='"+(e.selectionEnd||e.max)+"'>Drag</a>":"")+"</div>"}function r(e){return function(t){return t+e}}function l(e){return function(){return e}}function s(e){return(e+"").replace(".",m.cultures.current.numberFormat["."])}function d(e){var t=""+e,n=0;return t=t.split("."),t[1]&&(n=t[1].length),n=n>10?10:n}function u(e){var t,n;return e=parseFloat(e,10),t=d(e),n=H.pow(10,t||0),H.round(e*n)/n}function p(e,n){var i=x(e.getAttribute(n));return null===i&&(i=t),i}function c(e){return typeof e!==Q}function f(e){return 1e4*e}var _,m=window.kendo,v=m.ui.Widget,h=m.ui.Draggable,g=m._outerWidth,w=m._outerHeight,k=e.extend,S=m.format,x=m.parseFloat,D=e.proxy,b=e.isArray,H=Math,T=m.support,y=T.pointers,z=T.msPointers,I="change",E="slide",C=".slider",F="touchstart"+C+" mousedown"+C,R=y?"pointerdown"+C:z?"MSPointerDown"+C:F,A="touchend"+C+" mouseup"+C,V=y?"pointerup":z?"MSPointerUp"+C:A,q="moveSelection",M="keydown"+C,P="click"+C,W="mouseover"+C,N="focus"+C,O="blur"+C,B=".k-draghandle",L=".k-slider-track",Z=".k-tick",U="k-state-selected",X="k-state-focused",Y="k-state-default",j="k-state-disabled",K="disabled",Q="undefined",G="tabindex",J=m.getTouches,$=v.extend({init:function(e,t){var n,i=this;if(v.fn.init.call(i,e,t),t=i.options,i._isHorizontal="horizontal"==t.orientation,i._isRtl=i._isHorizontal&&m.support.isRtl(e),i._position=i._isHorizontal?"left":"bottom",i._sizeFn=i._isHorizontal?"width":"height",i._outerSize=i._isHorizontal?g:w,t.tooltip.format=t.tooltip.enabled?t.tooltip.format||"{0}":"{0}",t.smallStep<=0)throw Error("Kendo UI Slider smallStep must be a positive number.");i._createHtml(),i.wrapper=i.element.closest(".k-slider"),i._trackDiv=i.wrapper.find(L),i._setTrackDivWidth(),i._maxSelection=i._trackDiv[i._sizeFn](),i._sliderItemsInit(),i._reset(),i._tabindex(i.wrapper.find(B)),i[t.enabled?"enable":"disable"](),n=m.support.isRtl(i.wrapper)?-1:1,i._keyMap={37:r(-1*n*t.smallStep),40:r(-t.smallStep),39:r(1*n*t.smallStep),38:r(+t.smallStep),35:l(t.max),36:l(t.min),33:r(+t.largeStep),34:r(-t.largeStep)},m.notify(i)},events:[I,E],options:{enabled:!0,min:0,max:10,smallStep:1,largeStep:5,orientation:"horizontal",tickPlacement:"both",tooltip:{enabled:!0,format:"{0}"}},_distance:function(){return u(this.options.max-this.options.min)},_resize:function(){this._setTrackDivWidth(),this.wrapper.find(".k-slider-items").remove(),this._maxSelection=this._trackDiv[this._sizeFn](),this._sliderItemsInit(),this._refresh(),this.options.enabled&&this.enable(!0)},_sliderItemsInit:function(){var t=this,n=t.options,i=t._maxSelection/((n.max-n.min)/n.smallStep),o=t._calculateItemsWidth(H.floor(t._distance()/n.smallStep));"none"!=n.tickPlacement&&i>=2&&(e(this.element).parent().find(".k-slider-items").remove(),t._trackDiv.before(a(n,t._distance())),t._setItemsWidth(o),t._setItemsTitle()),t._calculateSteps(o),"none"!=n.tickPlacement&&i>=2&&n.largeStep>=n.smallStep&&t._setItemsLargeTick()},getSize:function(){return m.dimensions(this.wrapper)},_setTrackDivWidth:function(){var e=this,t=2*parseFloat(e._trackDiv.css(e._isRtl?"right":e._position),10);e._trackDiv[e._sizeFn](e.wrapper[e._sizeFn]()-2-t)},_setItemsWidth:function(t){var n,i=this,a=i.options,o=0,r=t.length-1,l=i.wrapper.find(Z),s=0,d=2,u=l.length,p=0;for(n=0;n<u-2;n++)e(l[n+1])[i._sizeFn](t[n]);if(i._isHorizontal?(e(l[o]).addClass("k-first")[i._sizeFn](t[r-1]),e(l[r]).addClass("k-last")[i._sizeFn](t[r])):(e(l[r]).addClass("k-first")[i._sizeFn](t[r]),e(l[o]).addClass("k-last")[i._sizeFn](t[r-1])),i._distance()%a.smallStep!==0&&!i._isHorizontal){for(n=0;n<t.length;n++)p+=t[n];s=i._maxSelection-p,s+=parseFloat(i._trackDiv.css(i._position),10)+d,i.wrapper.find(".k-slider-items").css("padding-top",s)}},_setItemsTitle:function(){for(var t=this,n=t.options,i=t.wrapper.find(Z),a=n.min,o=i.length,r=t._isHorizontal&&!t._isRtl?0:o-1,l=t._isHorizontal&&!t._isRtl?o:-1,s=t._isHorizontal&&!t._isRtl?1:-1;r-l!==0;r+=s)e(i[r]).attr("title",S(n.tooltip.format,u(a))),a+=n.smallStep},_setItemsLargeTick:function(){var t,n,i,a=this,o=a.options,r=a.wrapper.find(Z),l=0;if(f(o.largeStep)%f(o.smallStep)===0||a._distance()/o.largeStep>=3)for(a._isHorizontal||a._isRtl||(r=e.makeArray(r).reverse()),l=0;l<r.length;l++)t=e(r[l]),n=a._values[l],i=u(f(n-this.options.min)),i%f(o.smallStep)===0&&i%f(o.largeStep)===0&&(t.addClass("k-tick-large").html("<span class='k-label'>"+t.attr("title")+"</span>"),0!==l&&l!==r.length-1&&t.css("line-height",t[a._sizeFn]()+"px"))},_calculateItemsWidth:function(e){var t,n,i,a=this,o=a.options,r=parseFloat(a._trackDiv.css(a._sizeFn))+1,l=a._distance(),s=r/l;for(l/o.smallStep-H.floor(l/o.smallStep)>0&&(r-=l%o.smallStep*s),t=r/e,n=[],i=0;i<e-1;i++)n[i]=t;return n[e-1]=n[e]=t/2,a._roundWidths(n)},_roundWidths:function(e){var t,n=0,i=e.length;for(t=0;t<i;t++)n+=e[t]-H.floor(e[t]),e[t]=H.floor(e[t]);return n=H.round(n),this._addAdditionalSize(n,e)},_addAdditionalSize:function(e,t){if(0===e)return t;var n,i=parseFloat(t.length-1)/parseFloat(1==e?e:e-1);for(n=0;n<e;n++)t[parseInt(H.round(i*n),10)]+=1;return t},_calculateSteps:function(e){var t,n=this,i=n.options,a=i.min,o=0,r=n._distance(),l=H.ceil(r/i.smallStep),s=1;if(l+=r/i.smallStep%1===0?1:0,e.splice(0,0,2*e[l-2]),e.splice(l-1,1,2*e.pop()),n._pixelSteps=[o],n._values=[a],0!==l){for(;s<l;)o+=(e[s-1]+e[s])/2,n._pixelSteps[s]=o,a+=i.smallStep,n._values[s]=u(a),s++;t=r%i.smallStep===0?l-1:l,n._pixelSteps[t]=n._maxSelection,n._values[t]=i.max,n._isRtl&&(n._pixelSteps.reverse(),n._values.reverse())}},_getValueFromPosition:function(e,t){var n,i=this,a=i.options,o=H.max(a.smallStep*(i._maxSelection/i._distance()),0),r=0,l=o/2;if(i._isHorizontal?(r=e-t.startPoint,i._isRtl&&(r=i._maxSelection-r)):r=t.startPoint-e,i._maxSelection-(parseInt(i._maxSelection%o,10)-3)/2<r)return a.max;for(n=0;n<i._pixelSteps.length;n++)if(H.abs(i._pixelSteps[n]-r)-1<=l)return u(i._values[n])},_getFormattedValue:function(e,t){var n,i,a,o=this,r="",l=o.options.tooltip;return b(e)?(i=e[0],a=e[1]):t&&t.type&&(i=t.selectionStart,a=t.selectionEnd),t&&(n=t.tooltipTemplate),!n&&l.template&&(n=m.template(l.template)),b(e)||t&&t.type?n?r=n({selectionStart:i,selectionEnd:a}):(i=S(l.format,i),a=S(l.format,a),r=i+" - "+a):(t&&(t.val=e),r=n?n({value:e}):S(l.format,e)),r},_getDraggableArea:function(){var e=this,t=m.getOffset(e._trackDiv);return{startPoint:e._isHorizontal?t.left:t.top+e._maxSelection,endPoint:e._isHorizontal?t.left+e._maxSelection:t.top}},_createHtml:function(){var e=this,t=e.element,a=e.options,r=t.find("input");2==r.length?(r.eq(0).prop("value",s(a.selectionStart)),r.eq(1).prop("value",s(a.selectionEnd))):t.prop("value",s(a.value)),t.wrap(n(a,t,e._isHorizontal)).hide(),a.showButtons&&t.before(i(a,"increase",e._isHorizontal,e._isRtl)).before(i(a,"decrease",e._isHorizontal,e._isRtl)),t.before(o(a,t))},_focus:function(t){var n=this,i=t.target,a=n.value(),o=n._drag;o||(i==n.wrapper.find(B).eq(0)[0]?(o=n._firstHandleDrag,n._activeHandle=0):(o=n._lastHandleDrag,n._activeHandle=1),a=a[n._activeHandle]),e(i).addClass(X+" "+U),o&&(n._activeHandleDrag=o,o.selectionStart=n.options.selectionStart,o.selectionEnd=n.options.selectionEnd,o._updateTooltip(a))},_focusWithMouse:function(t){t=e(t);var n=this,i=t.is(B)?t.index():0;window.setTimeout(function(){n.wrapper.find(B)[2==i?1:0].focus()},1),n._setTooltipTimeout()},_blur:function(t){var n=this,i=n._activeHandleDrag;e(t.target).removeClass(X+" "+U),i&&(i._removeTooltip(),delete n._activeHandleDrag,delete n._activeHandle)},_setTooltipTimeout:function(){var e=this;e._tooltipTimeout=window.setTimeout(function(){var t=e._drag||e._activeHandleDrag;t&&t._removeTooltip()},300)},_clearTooltipTimeout:function(){var e,t=this;window.clearTimeout(this._tooltipTimeout),e=t._drag||t._activeHandleDrag,e&&e.tooltipDiv&&e.tooltipDiv.stop(!0,!1).css("opacity",1)},_reset:function(){var t=this,n=t.element,i=n.attr("form"),a=i?e("#"+i):n.closest("form");a[0]&&(t._form=a.on("reset",D(t._formResetHandler,t)))},min:function(e){return e?(this.setOptions({min:e}),t):this.options.min},max:function(e){return e?(this.setOptions({max:e}),t):this.options.max},setOptions:function(e){v.fn.setOptions.call(this,e),this._sliderItemsInit(),this._refresh()},destroy:function(){this._form&&this._form.off("reset",this._formResetHandler),v.fn.destroy.call(this)}}),ee=$.extend({init:function(n,i){var a,o=this;n.type="text",i=k({},{value:p(n,"value"),min:p(n,"min"),max:p(n,"max"),smallStep:p(n,"step")},i),n=e(n),i&&i.enabled===t&&(i.enabled=!n.is("[disabled]")),$.fn.init.call(o,n,i),i=o.options,c(i.value)&&null!==i.value||(i.value=i.min,n.prop("value",s(i.min))),i.value=H.max(H.min(i.value,i.max),i.min),a=o.wrapper.find(B),this._selection=new ee.Selection(a,o,i),o._drag=new ee.Drag(a,"",o,i)},options:{name:"Slider",showButtons:!0,increaseButtonTitle:"Increase",decreaseButtonTitle:"Decrease",dragHandleTitle:"drag",tooltip:{format:"{0:#,#.##}"},value:null},enable:function(n){var i,a,o,r=this,l=r.options;r.disable(),n!==!1&&(r.wrapper.removeClass(j).addClass(Y),r.wrapper.find("input").removeAttr(K),i=function(n){var i,a,o,l=J(n)[0];if(l){if(i=r._isHorizontal?l.location.pageX:l.location.pageY,a=r._getDraggableArea(),o=e(n.target),o.hasClass("k-draghandle"))return o.addClass(X+" "+U),t;r._update(r._getValueFromPosition(i,a)),r._focusWithMouse(n.target),r._drag.dragstart(n),n.preventDefault()}},r.wrapper.find(Z+", "+L).on(R,i).end().on(R,function(){e(document.documentElement).one("selectstart",m.preventDefault)}).on(V,function(){r._drag._end()}),r.wrapper.find(B).attr(G,0).on(A,function(){r._setTooltipTimeout()}).on(P,function(e){r._focusWithMouse(e.target),e.preventDefault()}).on(N,D(r._focus,r)).on(O,D(r._blur,r)),a=D(function(e){var t=r._nextValueByIndex(r._valueIndex+1*e);r._setValueInRange(t),r._drag._updateTooltip(t)},r),l.showButtons&&(o=D(function(e,t){this._clearTooltipTimeout(),(1===e.which||T.touch&&0===e.which)&&(a(t),this.timeout=setTimeout(D(function(){this.timer=setInterval(function(){a(t)},60)},this),200))},r),r.wrapper.find(".k-button").on(A,D(function(e){this._clearTimer(),r._focusWithMouse(e.target)},r)).on(W,function(t){e(t.currentTarget).addClass("k-state-hover")}).on("mouseout"+C,D(function(t){e(t.currentTarget).removeClass("k-state-hover"),this._clearTimer()},r)).eq(0).on(F,D(function(e){o(e,1)},r)).click(!1).end().eq(1).on(F,D(function(e){o(e,-1)},r)).click(m.preventDefault)),r.wrapper.find(B).off(M,!1).on(M,D(this._keydown,r)),l.enabled=!0)},disable:function(){var t=this;t.wrapper.removeClass(Y).addClass(j),e(t.element).prop(K,K),t.wrapper.find(".k-button").off(F).on(F,function(t){t.preventDefault(),e(this).addClass("k-state-active")}).off(A).on(A,function(t){t.preventDefault(),e(this).removeClass("k-state-active")}).off("mouseleave"+C).on("mouseleave"+C,m.preventDefault).off(W).on(W,m.preventDefault),t.wrapper.find(Z+", "+L).off(R).off(V),t.wrapper.find(B).attr(G,-1).off(A).off(M).off(P).off(N).off(O),t.options.enabled=!1},_update:function(e){var t=this,n=t.value()!=e;t.value(e),n&&t.trigger(I,{value:t.options.value})},value:function(e){var n=this,i=n.options;return e=u(e),isNaN(e)?i.value:(e>=i.min&&e<=i.max&&i.value!=e&&(n.element.prop("value",s(e)),i.value=e,n._refreshAriaAttr(e),n._refresh()),t)},_refresh:function(){this.trigger(q,{value:this.options.value})},_refreshAriaAttr:function(e){var t,n=this,i=n._drag;t=i&&i._tooltipDiv?i._tooltipDiv.text():n._getFormattedValue(e,null),this.wrapper.find(B).attr("aria-valuenow",e).attr("aria-valuetext",t)},_clearTimer:function(){clearTimeout(this.timeout),clearInterval(this.timer)},_keydown:function(e){var t=this;e.keyCode in t._keyMap&&(t._clearTooltipTimeout(),t._setValueInRange(t._keyMap[e.keyCode](t.options.value)),t._drag._updateTooltip(t.value()),e.preventDefault())},_setValueInRange:function(e){var n=this,i=n.options;return e=u(e),isNaN(e)?(n._update(i.min),t):(e=H.max(H.min(e,i.max),i.min),n._update(e),t)},_nextValueByIndex:function(e){var t=this._values.length;return this._isRtl&&(e=t-1-e),this._values[H.max(0,H.min(e,t-1))]},_formResetHandler:function(){var e=this,t=e.options.min;setTimeout(function(){var n=e.element[0].value;e.value(""===n||isNaN(n)?t:n)})},destroy:function(){var e=this;$.fn.destroy.call(e),e.wrapper.off(C).find(".k-button").off(C).end().find(B).off(C).end().find(Z+", "+L).off(C).end(),e._drag.draggable.destroy(),e._drag._removeTooltip(!0)}});ee.Selection=function(e,t,n){function i(i){var a=i-n.min,o=t._valueIndex=H.ceil(u(a/n.smallStep)),r=parseInt(t._pixelSteps[o],10),l=t._trackDiv.find(".k-slider-selection"),s=parseInt(t._outerSize(e)/2,10),d=t._isRtl?2:0;l[t._sizeFn](t._isRtl?t._maxSelection-r:r),e.css(t._position,r-s-d)}i(n.value),t.bind([E,q],function(e){i(parseFloat(e.value,10))}),t.bind(I,function(e){i(parseFloat(e.sender.value(),10))})},ee.Drag=function(e,t,n,i){var a=this;a.owner=n,a.options=i,a.element=e,a.type=t,a.draggable=new h(e,{distance:0,dragstart:D(a._dragstart,a),drag:D(a.drag,a),dragend:D(a.dragend,a),dragcancel:D(a.dragcancel,a)}),e.click(!1),e.on("dragstart",function(e){e.preventDefault()})},ee.Drag.prototype={dragstart:function(e){this.owner._activeDragHandle=this,this.draggable.userEvents.cancel(),this._dragstart(e),this.dragend()},_dragstart:function(n){var i=this,a=i.owner,o=i.options;return o.enabled?(this.owner._activeDragHandle=this,a.element.off(W),a.wrapper.find("."+X).removeClass(X+" "+U),i.element.addClass(X+" "+U),e(document.documentElement).css("cursor","pointer"),i.dragableArea=a._getDraggableArea(),i.step=H.max(o.smallStep*(a._maxSelection/a._distance()),0),i.type?(i.selectionStart=o.selectionStart,i.selectionEnd=o.selectionEnd,a._setZIndex(i.type)):i.oldVal=i.val=o.value,i._removeTooltip(!0),i._createTooltip(),t):(n.preventDefault(),t)},_createTooltip:function(){var t,n,i=this,a=i.owner,o=i.options.tooltip,r="",l=e(window);o.enabled&&(o.template&&(t=i.tooltipTemplate=m.template(o.template)),e(".k-slider-tooltip").remove(),i.tooltipDiv=e("<div class='k-widget k-tooltip k-slider-tooltip'><!-- --></div>").appendTo(document.body),r=a._getFormattedValue(i.val||a.value(),i),i.type||(n="k-callout-"+(a._isHorizontal?"s":"e"),i.tooltipInnerDiv="<div class='k-callout "+n+"'><!-- --></div>",r+=i.tooltipInnerDiv),i.tooltipDiv.html(r),i._scrollOffset={top:l.scrollTop(),left:l.scrollLeft()},i.moveTooltip())},drag:function(e){var t,n=this,i=n.owner,a=e.x.location,o=e.y.location,r=n.dragableArea.startPoint,l=n.dragableArea.endPoint;e.preventDefault(),n.val=i._isHorizontal?i._isRtl?n.constrainValue(a,r,l,a<l):n.constrainValue(a,r,l,a>=l):n.constrainValue(o,l,r,o<=l),n.oldVal!=n.val&&(n.oldVal=n.val,n.type?("firstHandle"==n.type?n.selectionStart=n.val<n.selectionEnd?n.val:n.selectionEnd=n.val:n.val>n.selectionStart?n.selectionEnd=n.val:n.selectionStart=n.selectionEnd=n.val,t={values:[n.selectionStart,n.selectionEnd],value:[n.selectionStart,n.selectionEnd]}):t={value:n.val},i.trigger(E,t)),n._updateTooltip(n.val)},_updateTooltip:function(e){var t=this,n=t.options,i=n.tooltip,a="";i.enabled&&(t.tooltipDiv||t._createTooltip(),a=t.owner._getFormattedValue(u(e),t),t.type||(a+=t.tooltipInnerDiv),t.tooltipDiv.html(a),t.moveTooltip())},dragcancel:function(){return this.owner._refresh(),e(document.documentElement).css("cursor",""),this._end()},dragend:function(){var t=this,n=t.owner;return e(document.documentElement).css("cursor",""),t.type?n._update(t.selectionStart,t.selectionEnd):(n._update(t.val),t.draggable.userEvents._disposeAll()),t.draggable.userEvents.cancel(),t._end()},_end:function(){var e=this,t=e.owner;return t._focusWithMouse(e.element),t.element.on(W),!1},_removeTooltip:function(t){var n=this,i=n.owner;n.tooltipDiv&&i.options.tooltip.enabled&&i.options.enabled&&(t?(n.tooltipDiv.remove(),n.tooltipDiv=null):n.tooltipDiv.fadeOut("slow",function(){e(this).remove(),n.tooltipDiv=null}))},moveTooltip:function(){var t,n,i,a,o=this,r=o.owner,l=0,s=0,d=o.element,u=m.getOffset(d),p=8,c=e(window),f=o.tooltipDiv.find(".k-callout"),_=g(o.tooltipDiv),v=w(o.tooltipDiv);o.type?(t=r.wrapper.find(B),u=m.getOffset(t.eq(0)),n=m.getOffset(t.eq(1)),r._isHorizontal?(l=n.top,s=u.left+(n.left-u.left)/2):(l=u.top+(n.top-u.top)/2,s=n.left),a=g(t.eq(0))+2*p):(l=u.top,s=u.left,a=g(d)+2*p),r._isHorizontal?(s-=parseInt((_-r._outerSize(d))/2,10),l-=v+p+(f.length?f.height():0)):(l-=parseInt((v-r._outerSize(d))/2,10),s-=_+p+(f.length?f.width():0)),r._isHorizontal?(i=o._flip(l,v,a,w(c)+o._scrollOffset.top),l+=i,s+=o._fit(s,_,g(c)+o._scrollOffset.left)):(i=o._flip(s,_,a,g(c)+o._scrollOffset.left),l+=o._fit(l,v,w(c)+o._scrollOffset.top),s+=i),i>0&&f&&(f.removeClass(),f.addClass("k-callout k-callout-"+(r._isHorizontal?"n":"w"))),o.tooltipDiv.css({top:l,left:s})},_fit:function(e,t,n){var i=0;return e+t>n&&(i=n-(e+t)),e<0&&(i=-e),i},_flip:function(e,t,n,i){var a=0;return e+t>i&&(a+=-(n+t)),e+a<0&&(a+=n+t),a},constrainValue:function(e,t,n,i){var a=this,o=0;return o=t<e&&e<n?a.owner._getValueFromPosition(e,a.dragableArea):i?a.options.max:a.options.min}},m.ui.plugin(ee),_=$.extend({init:function(n,i){var a,o=this,r=e(n).find("input"),l=r.eq(0)[0],d=r.eq(1)[0];l.type="text",d.type="text",i&&i.showButtons&&(window.console&&window.console.warn("showbuttons option is not supported for the range slider, ignoring"),i.showButtons=!1),i=k({},{selectionStart:p(l,"value"),min:p(l,"min"),max:p(l,"max"),smallStep:p(l,"step")},{selectionEnd:p(d,"value"),min:p(d,"min"),max:p(d,"max"),smallStep:p(d,"step")},i),i&&i.enabled===t&&(i.enabled=!r.is("[disabled]")),$.fn.init.call(o,n,i),i=o.options,c(i.selectionStart)&&null!==i.selectionStart||(i.selectionStart=i.min,r.eq(0).prop("value",s(i.min))),c(i.selectionEnd)&&null!==i.selectionEnd||(i.selectionEnd=i.max,r.eq(1).prop("value",s(i.max))),a=o.wrapper.find(B),this._selection=new _.Selection(a,o,i),o._firstHandleDrag=new ee.Drag(a.eq(0),"firstHandle",o,i),o._lastHandleDrag=new ee.Drag(a.eq(1),"lastHandle",o,i)},options:{name:"RangeSlider",leftDragHandleTitle:"drag",rightDragHandleTitle:"drag",tooltip:{format:"{0:#,#.##}"},selectionStart:null,selectionEnd:null},enable:function(n){var i,a=this,o=a.options;a.disable(),n!==!1&&(a.wrapper.removeClass(j).addClass(Y),a.wrapper.find("input").removeAttr(K),i=function(n){var i,r,l,s,d,u,p,c=J(n)[0];if(c){if(i=a._isHorizontal?c.location.pageX:c.location.pageY,r=a._getDraggableArea(),l=a._getValueFromPosition(i,r),s=e(n.target),s.hasClass("k-draghandle"))return a.wrapper.find("."+X).removeClass(X+" "+U),s.addClass(X+" "+U),t;l<o.selectionStart?(d=l,u=o.selectionEnd,p=a._firstHandleDrag):l>a.selectionEnd?(d=o.selectionStart,u=l,p=a._lastHandleDrag):l-o.selectionStart<=o.selectionEnd-l?(d=l,u=o.selectionEnd,p=a._firstHandleDrag):(d=o.selectionStart,u=l,p=a._lastHandleDrag),p.dragstart(n),a._setValueInRange(d,u),a._focusWithMouse(p.element)}},a.wrapper.find(Z+", "+L).on(R,i).end().on(R,function(){e(document.documentElement).one("selectstart",m.preventDefault)}).on(V,function(){a._activeDragHandle&&a._activeDragHandle._end()}),a.wrapper.find(B).attr(G,0).on(A,function(){a._setTooltipTimeout()}).on(P,function(e){a._focusWithMouse(e.target),e.preventDefault()}).on(N,D(a._focus,a)).on(O,D(a._blur,a)),a.wrapper.find(B).off(M,m.preventDefault).eq(0).on(M,D(function(e){this._keydown(e,"firstHandle")},a)).end().eq(1).on(M,D(function(e){this._keydown(e,"lastHandle")},a)),a.options.enabled=!0)},disable:function(){var e=this;e.wrapper.removeClass(Y).addClass(j),e.wrapper.find("input").prop(K,K),e.wrapper.find(Z+", "+L).off(R).off(V),e.wrapper.find(B).attr(G,-1).off(A).off(M).off(P).off(N).off(O),e.options.enabled=!1},_keydown:function(e,t){var n,i,a,o=this,r=o.options.selectionStart,l=o.options.selectionEnd;e.keyCode in o._keyMap&&(o._clearTooltipTimeout(),"firstHandle"==t?(a=o._activeHandleDrag=o._firstHandleDrag,r=o._keyMap[e.keyCode](r),r>l&&(l=r)):(a=o._activeHandleDrag=o._lastHandleDrag,l=o._keyMap[e.keyCode](l),r>l&&(r=l)),o._setValueInRange(u(r),u(l)),n=Math.max(r,o.options.selectionStart),i=Math.min(l,o.options.selectionEnd),a.selectionEnd=Math.max(i,o.options.selectionStart),a.selectionStart=Math.min(n,o.options.selectionEnd),a._updateTooltip(o.value()[o._activeHandle]),e.preventDefault())},_update:function(e,t){var n=this,i=n.value(),a=i[0]!=e||i[1]!=t;n.value([e,t]),a&&n.trigger(I,{values:[e,t],value:[e,t]})},value:function(e){return e&&e.length?this._value(e[0],e[1]):this._value()},_value:function(e,n){var i=this,a=i.options,o=a.selectionStart,r=a.selectionEnd;return isNaN(e)&&isNaN(n)?[o,r]:(e=u(e),n=u(n),e>=a.min&&e<=a.max&&n>=a.min&&n<=a.max&&e<=n&&(o==e&&r==n||(i.element.find("input").eq(0).prop("value",s(e)).end().eq(1).prop("value",s(n)),a.selectionStart=e,a.selectionEnd=n,i._refresh(),i._refreshAriaAttr(e,n))),t)},values:function(e,t){return b(e)?this._value(e[0],e[1]):this._value(e,t)},_refresh:function(){var e=this,t=e.options;e.trigger(q,{values:[t.selectionStart,t.selectionEnd],value:[t.selectionStart,t.selectionEnd]}),t.selectionStart==t.max&&t.selectionEnd==t.max&&e._setZIndex("firstHandle")},_refreshAriaAttr:function(e,t){var n,i=this,a=i.wrapper.find(B),o=i._activeHandleDrag;n=i._getFormattedValue([e,t],o),a.eq(0).attr("aria-valuenow",e),a.eq(1).attr("aria-valuenow",t),a.attr("aria-valuetext",n)},_setValueInRange:function(e,t){var n=this.options;e=H.max(H.min(e,n.max),n.min),t=H.max(H.min(t,n.max),n.min),e==n.max&&t==n.max&&this._setZIndex("firstHandle"),this._update(H.min(e,t),H.max(e,t))},_setZIndex:function(t){this.wrapper.find(B).each(function(n){e(this).css("z-index","firstHandle"==t?1-n:n)})},_formResetHandler:function(){var e=this,t=e.options;setTimeout(function(){var n=e.element.find("input"),i=n[0].value,a=n[1].value;e.values(""===i||isNaN(i)?t.min:i,""===a||isNaN(a)?t.max:a)})},destroy:function(){var e=this;$.fn.destroy.call(e),e.wrapper.off(C).find(Z+", "+L).off(C).end().find(B).off(C),e._firstHandleDrag.draggable.destroy(),e._lastHandleDrag.draggable.destroy()}}),_.Selection=function(e,t,n){function i(i){i=i||[];var o=i[0]-n.min,r=i[1]-n.min,l=H.ceil(u(o/n.smallStep)),s=H.ceil(u(r/n.smallStep)),d=t._pixelSteps[l],p=t._pixelSteps[s],c=parseInt(t._outerSize(e.eq(0))/2,10),f=t._isRtl?2:0;e.eq(0).css(t._position,d-c-f).end().eq(1).css(t._position,p-c-f),a(d,p)}function a(e,n){var i,a,o=t._trackDiv.find(".k-slider-selection");i=H.abs(e-n),o[t._sizeFn](i),t._isRtl?(a=H.max(e,n),o.css("right",t._maxSelection-a-1)):(a=H.min(e,n),o.css(t._position,a-1))}i(t.value()),t.bind([I,E,q],function(e){i(e.values)})},m.ui.plugin(_)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.slider.min.js.map
