{"version": 3, "sources": ["kendo.draganddrop.js"], "names": ["f", "define", "$", "undefined", "contains", "parent", "child", "e", "numericCssPropery", "element", "property", "parseInt", "css", "within", "value", "range", "Math", "min", "max", "containerBoundaries", "container", "offset", "getOffset", "outerWidth", "kendo", "_outerWidth", "outerHeight", "_outerHeight", "minX", "left", "minY", "top", "maxX", "width", "maxY", "height", "x", "y", "checkTarget", "target", "targets", "areas", "theTarget", "theFilter", "i", "targetLen", "length", "areaLen", "parentNode", "targetElement", "support", "matchesSelector", "call", "options", "filter", "destroyDroppable", "collection", "widget", "groupName", "group", "droppables", "Widget", "fn", "destroy", "splice", "scrollableViewPort", "root", "scrollableRoot", "scrollTop", "scrollLeft", "bottom", "$window", "right", "browser", "edge", "safari", "document", "body", "documentElement", "findScrollableParent", "isScrollable", "autoScrollVelocity", "mouseX", "mouseY", "rect", "velocity", "AUTO_SCROLL_AREA", "lastDropTarget", "translate", "Movable", "<PERSON><PERSON>arget", "DropTargetArea", "Draggable", "window", "Class", "ui", "Observable", "UserEvents", "proxy", "extend", "draggables", "dropTargets", "drop<PERSON><PERSON><PERSON>", "elementUnderCursor", "KEYUP", "CHANGE", "DRAGSTART", "HOLD", "DRAG", "DRAGEND", "DRAGCANCEL", "HINTDESTROYED", "DRAGENTER", "DRAGLEAVE", "DROP", "TapCapture", "init", "that", "this", "dom<PERSON>lement", "capture", "addEventListener", "each", "eventMap", "down", "split", "_press", "up", "_release", "attachEvent", "bind", "captureNext", "cancelCapture", "trigger", "preventDefault", "PaneDimension", "forcedEnabled", "scale", "horizontal", "measure", "scrollSize", "axis", "makeVirtual", "virtual", "_virtualMin", "_virtualMax", "virtualSize", "update", "outOfBounds", "forceEnabled", "getSize", "getTotal", "rescale", "silent", "total", "scaledTotal", "size", "minScale", "centerOffset", "enabled", "PaneDimensions", "forcedMinScale", "maxScale", "newScale", "refresh", "centerCoordinates", "fitScale", "PaneAxis", "dimension", "movable", "dragMove", "delta", "position", "resistance", "translateAxis", "Pane", "elastic", "dimensions", "userEvents", "gesturestart", "gesture", "press", "event", "closest", "is", "sender", "cancel", "gesturechange", "coordinates", "offsetX", "offsetY", "previousGesture", "previousCenter", "center", "scaleDelta", "distance", "scaleWith", "move", "tagName", "match", "touch", "skip", "end", "TRANSFORM_STYLE", "transitions", "prefix", "hasHW3D", "style", "webkitTransformOrigin", "_saveCoordinates", "by", "scaleTo", "moveAxis", "moveTo", "newCoordinates", "round", "msie", "version", "push", "events", "name", "_trigger", "eventName", "draggable", "drop<PERSON>ar<PERSON>", "_over", "_out", "_drop", "dropped", "destroyGroup", "_cache", "_activated", "global", "allowSelection", "threshold", "start", "_start", "hold", "_hold", "_drag", "_end", "_cancel", "select", "_select", "_afterEndHandler", "_afterEnd", "_captureEscape", "cursorOffset", "ignore", "holdToDrag", "autoScroll", "cancelHold", "keyCode", "keys", "ESC", "_updateHint", "boundaries", "location", "hintOffset", "hint", "_shouldI<PERSON><PERSON><PERSON><PERSON>get", "ignoreSelector", "initialTouch", "currentTarget", "currentTargetOffset", "stop", "remove", "isFunction", "zIndex", "appendTo", "angular", "removeAttr", "scopeTarget", "data", "elements", "get", "scopeFrom", "on", "cursorElement", "_elementUnderCursor", "_cursorElement", "_scrollableParent", "_lastEvent", "_processMovement", "_scrollCompenstation", "_scrollVelocity", "clearInterval", "_scrollInterval", "setInterval", "_withDrop<PERSON>arget", "_autoScroll", "yIsScrollable", "xIsScrollable", "isRootNode", "y<PERSON><PERSON><PERSON>", "yInBounds", "xDelta", "xInBounds", "compensation", "scrollHeight", "scrollWidth", "offsetHeight", "offsetWidth", "isDefaultPrevented", "setTimeout", "animate", "initialTarget", "hide", "show", "callback", "result", "off", "plugin", "utils", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBACH,aACA,oBACDD,IACL,WAk0BE,MAvzBC,UAAUE,EAAGC,GAEV,QAASC,GAASC,EAAQC,GACtB,IACI,MAAOJ,GAAEE,SAASC,EAAQC,IAAUD,GAAUC,EAChD,MAAOC,GACL,OAAO,GAGf,QAASC,GAAkBC,EAASC,GAChC,MAAOC,UAASF,EAAQG,IAAIF,GAAW,KAAO,EAElD,QAASG,GAAOC,EAAOC,GACnB,MAAOC,MAAKC,IAAID,KAAKE,IAAIJ,EAAOC,EAAME,KAAMF,EAAMG,KAEtD,QAASC,GAAoBC,EAAWX,GACpC,GAAIY,GAASC,EAAUF,GAAYG,EAAaC,EAAMC,YAAaC,EAAcF,EAAMG,aAAcC,EAAOP,EAAOQ,KAAOrB,EAAkBY,EAAW,mBAAqBZ,EAAkBY,EAAW,eAAgBU,EAAOT,EAAOU,IAAMvB,EAAkBY,EAAW,kBAAoBZ,EAAkBY,EAAW,cAAeY,EAAOJ,EAAOR,EAAUa,QAAUV,EAAWd,GAAS,GAAOyB,EAAOJ,EAAOV,EAAUe,SAAWT,EAAYjB,GAAS,EAC/b,QACI2B,GACInB,IAAKW,EACLV,IAAKc,GAETK,GACIpB,IAAKa,EACLZ,IAAKgB,IAIjB,QAASI,GAAYC,EAAQC,EAASC,GAElC,IADA,GAAIC,GAAWC,EAAWC,EAAI,EAAGC,EAAYL,GAAWA,EAAQM,OAAQC,EAAUN,GAASA,EAAMK,OAC1FP,GAAUA,EAAOS,YAAY,CAChC,IAAKJ,EAAI,EAAGA,EAAIC,EAAWD,IAEvB,GADAF,EAAYF,EAAQI,GAChBF,EAAUjC,QAAQ,KAAO8B,EACzB,OACIA,OAAQG,EACRO,cAAeV,EAI3B,KAAKK,EAAI,EAAGA,EAAIG,EAASH,IAErB,GADAD,EAAYF,EAAMG,GACd1C,EAAEE,SAASuC,EAAUlC,QAAQ,GAAI8B,IAAWW,EAAQC,gBAAgBC,KAAKb,EAAQI,EAAUU,QAAQC,QACnG,OACIf,OAAQI,EACRM,cAAeV,EAI3BA,GAASA,EAAOS,WAEpB,MAAO7C,GAmTX,QAASoD,GAAiBC,EAAYC,GAClC,GAA0Eb,GAAtEc,EAAYD,EAAOJ,QAAQM,MAAOC,EAAaJ,EAAWE,EAE9D,IADAG,EAAOC,GAAGC,QAAQX,KAAKK,GACnBG,EAAWd,OAAS,GACpB,IAAKF,EAAI,EAAGA,EAAIgB,EAAWd,OAAQF,IAC/B,GAAIgB,EAAWhB,IAAMa,EAAQ,CACzBG,EAAWI,OAAOpB,EAAG,EACrB,YAIRgB,GAAWd,OAAS,QACbU,GAAWE,GA0Y1B,QAASO,GAAmBxD,GACxB,GAAgCY,GAAQU,EAAKF,EAAzCqC,EAAOC,IAAiB,EAC5B,OAAI1D,GAAQ,KAAOyD,GACfnC,EAAMmC,EAAKE,UACXvC,EAAOqC,EAAKG,YAERtC,IAAKA,EACLF,KAAMA,EACNyC,OAAQvC,EAAMwC,EAAQpC,SACtBqC,MAAO3C,EAAO0C,EAAQtC,WAG1BZ,EAASZ,EAAQY,SACjBA,EAAOiD,OAASjD,EAAOU,IAAMtB,EAAQ0B,SACrCd,EAAOmD,MAAQnD,EAAOQ,KAAOpB,EAAQwB,QAC9BZ,GAGf,QAAS8C,KACL,MAAOjE,GAAEsB,EAAM0B,QAAQuB,QAAQC,MAAQlD,EAAM0B,QAAQuB,QAAQE,OAASC,EAASC,KAAOD,EAASE,iBAEnG,QAASC,GAAqBtE,GAA9B,GAKQJ,GAJA6D,EAAOC,GACX,KAAK1D,GAAWA,IAAYmE,EAASC,MAAQpE,IAAYmE,EAASE,gBAC9D,MAAOZ,EAGX,KADI7D,EAASH,EAAEO,GAAS,GACjBJ,IAAWmB,EAAMwD,aAAa3E,IAAWA,IAAWuE,EAASC,MAChExE,EAASA,EAAO2C,UAEpB,OAAI3C,KAAWuE,EAASC,KACbX,EAEJhE,EAAEG,GAEb,QAAS4E,GAAmBC,EAAQC,EAAQC,GAA5C,GACQC,IACAjD,EAAG,EACHC,EAAG,GAEHiD,EAAmB,EAWvB,OAVIJ,GAASE,EAAKvD,KAAOyD,EACrBD,EAASjD,IAAMkD,GAAoBJ,EAASE,EAAKvD,OAC1CuD,EAAKZ,MAAQU,EAASI,IAC7BD,EAASjD,EAAIkD,GAAoBF,EAAKZ,MAAQU,IAE9CC,EAASC,EAAKrD,IAAMuD,EACpBD,EAAShD,IAAMiD,GAAoBH,EAASC,EAAKrD,MAC1CqD,EAAKd,OAASa,EAASG,IAC9BD,EAAShD,EAAIiD,GAAoBF,EAAKd,OAASa,IAE5CE,EA/yBd,GACwUE,GAgSrQC,EAU5DC,EA0EAC,EAyDAC,EAoBAC,EAjcApE,EAAQqE,OAAOrE,MAAO0B,EAAU1B,EAAM0B,QAAS0B,EAAWiB,OAAOjB,SAAUL,EAAUrE,EAAE2F,QAASC,EAAQtE,EAAMsE,MAAOjC,EAASrC,EAAMuE,GAAGlC,OAAQmC,EAAaxE,EAAMwE,WAAYC,EAAazE,EAAMyE,WAAYC,EAAQhG,EAAEgG,MAAOC,EAASjG,EAAEiG,OAAQ7E,EAAYE,EAAMF,UAAW8E,KAAiBC,KAAkBC,KAAgCC,EAAqB/E,EAAM+E,mBAAoBC,EAAQ,QAASC,EAAS,SAAUC,EAAY,YAAaC,EAAO,OAAQC,EAAO,OAAQC,EAAU,UAAWC,EAAa,aAAcC,EAAgB,gBAAiBC,EAAY,YAAaC,EAAY,YAAaC,EAAO,OAoDrmBC,EAAanB,EAAWG,QACxBiB,KAAM,SAAU3G,EAAS4C,GACrB,GAAIgE,GAAOC,KAAMC,EAAa9G,EAAQ,EACtC4G,GAAKG,SAAU,EACXD,EAAWE,kBACXvH,EAAEwH,KAAKlG,EAAMmG,SAASC,KAAKC,MAAM,KAAM,WACnCN,EAAWE,iBAAiBH,KAAMpB,EAAMmB,EAAKS,OAAQT,IAAO,KAEhEnH,EAAEwH,KAAKlG,EAAMmG,SAASI,GAAGF,MAAM,KAAM,WACjCN,EAAWE,iBAAiBH,KAAMpB,EAAMmB,EAAKW,SAAUX,IAAO,OAGlEnH,EAAEwH,KAAKlG,EAAMmG,SAASC,KAAKC,MAAM,KAAM,WACnCN,EAAWU,YAAYX,KAAMpB,EAAMmB,EAAKS,OAAQT,MAEpDnH,EAAEwH,KAAKlG,EAAMmG,SAASI,GAAGF,MAAM,KAAM,WACjCN,EAAWU,YAAYX,KAAMpB,EAAMmB,EAAKW,SAAUX,OAG1DrB,EAAWlC,GAAGsD,KAAKhE,KAAKiE,GACxBA,EAAKa,MACD,QACA,WACD7E,QAEP8E,YAAa,WACTb,KAAKE,SAAU,GAEnBY,cAAe,WACXd,KAAKE,SAAU,GAEnBM,OAAQ,SAAUvH,GACd,GAAI8G,GAAOC,IACXD,GAAKgB,QAAQ,SACThB,EAAKG,SACLjH,EAAE+H,kBAGVN,SAAU,SAAUzH,GAChB,GAAI8G,GAAOC,IACXD,GAAKgB,QAAQ,WACThB,EAAKG,UACLjH,EAAE+H,iBACFjB,EAAKe,oBAIbG,EAAgBvC,EAAWG,QAC3BiB,KAAM,SAAU/D,GACZ,GAAIgE,GAAOC,IACXtB,GAAWlC,GAAGsD,KAAKhE,KAAKiE,GACxBA,EAAKmB,eAAgB,EACrBtI,EAAEiG,OAAOkB,EAAMhE,GACfgE,EAAKoB,MAAQ,EACTpB,EAAKqB,YACLrB,EAAKsB,QAAU,cACftB,EAAKuB,WAAa,cAClBvB,EAAKwB,KAAO,MAEZxB,EAAKsB,QAAU,eACftB,EAAKuB,WAAa,eAClBvB,EAAKwB,KAAO,MAGpBC,YAAa,WACT5I,EAAEiG,OAAOmB,MACLyB,SAAS,EACTP,eAAe,EACfQ,YAAa,EACbC,YAAa,KAGrBC,YAAa,SAAUjI,EAAKC,GACpBoG,KAAK0B,cAAgB/H,GAAOqG,KAAK2B,cAAgB/H,IACjDoG,KAAK0B,YAAc/H,EACnBqG,KAAK2B,YAAc/H,EACnBoG,KAAK6B,WAGbC,YAAa,SAAU/H,GACnB,MAAOA,GAASiG,KAAKpG,KAAOG,EAASiG,KAAKrG,KAE9CoI,aAAc,WACV/B,KAAKkB,eAAgB,GAEzBc,QAAS,WACL,MAAOhC,MAAKlG,UAAU,GAAGkG,KAAKqB,UAElCY,SAAU,WACN,MAAOjC,MAAK7G,QAAQ,GAAG6G,KAAKsB,aAEhCY,QAAS,SAAUf,GACfnB,KAAKmB,MAAQA,GAEjBU,OAAQ,SAAUM,GACd,GAAIpC,GAAOC,KAAMoC,EAAQrC,EAAK0B,QAAU1B,EAAK4B,YAAc5B,EAAKkC,WAAYI,EAAcD,EAAQrC,EAAKoB,MAAOmB,EAAOvC,EAAKiC,WAC5G,IAAVI,GAAgBrC,EAAKmB,iBAGzBnB,EAAKnG,IAAMmG,EAAK0B,SAAW1B,EAAK2B,YAAc,EAC9C3B,EAAKuC,KAAOA,EACZvC,EAAKqC,MAAQC,EACbtC,EAAKpG,IAAMD,KAAKC,IAAIoG,EAAKnG,IAAK0I,EAAOD,GACrCtC,EAAKwC,SAAWD,EAAOF,EACvBrC,EAAKyC,cAAgBH,EAAcC,GAAQ,EAC3CvC,EAAK0C,QAAU1C,EAAKmB,eAAiBmB,EAAcC,EAC9CH,GACDpC,EAAKgB,QAAQ5B,EAAQY,OAI7B2C,EAAiBhE,EAAWG,QAC5BiB,KAAM,SAAU/D,GACZ,GAAIgE,GAAOC,IACXtB,GAAWlC,GAAGsD,KAAKhE,KAAKiE,GACxBA,EAAKjF,EAAI,GAAImG,GAAcpC,GAASuC,YAAY,GAAQrF,IACxDgE,EAAKhF,EAAI,GAAIkG,GAAcpC,GAASuC,YAAY,GAASrF,IACzDgE,EAAKjG,UAAYiC,EAAQjC,UACzBiG,EAAK4C,eAAiB5G,EAAQwG,SAC9BxC,EAAK6C,SAAW7G,EAAQ6G,UAAY,IACpC7C,EAAKa,KAAKzB,EAAQpD,IAEtBmG,QAAS,SAAUW,GACf7C,KAAKlF,EAAEoH,QAAQW,GACf7C,KAAKjF,EAAEmH,QAAQW,GACf7C,KAAK8C,WAETC,kBAAmB,WACf,OACIjI,EAAGpB,KAAKC,IAAI,GAAIqG,KAAKlF,EAAE0H,cACvBzH,EAAGrB,KAAKC,IAAI,GAAIqG,KAAKjF,EAAEyH,gBAG/BM,QAAS,WACL,GAAI/C,GAAOC,IACXD,GAAKjF,EAAE+G,SACP9B,EAAKhF,EAAE8G,SACP9B,EAAK0C,QAAU1C,EAAKjF,EAAE2H,SAAW1C,EAAKhF,EAAE0H,QACxC1C,EAAKwC,SAAWxC,EAAK4C,gBAAkBjJ,KAAKC,IAAIoG,EAAKjF,EAAEyH,SAAUxC,EAAKhF,EAAEwH,UACxExC,EAAKiD,SAAWtJ,KAAKE,IAAImG,EAAKjF,EAAEyH,SAAUxC,EAAKhF,EAAEwH,UACjDxC,EAAKgB,QAAQ5B,MAGjB8D,EAAWvE,EAAWG,QACtBiB,KAAM,SAAU/D,GACZ,GAAIgE,GAAOC,IACXnB,GAAOkB,EAAMhE,GACb2C,EAAWlC,GAAGsD,KAAKhE,KAAKiE,IAE5B+B,YAAa,WACT,MAAO9B,MAAKkD,UAAUpB,YAAY9B,KAAKmD,QAAQnD,KAAKuB,QAExD6B,SAAU,SAAUC,GAChB,GAAItD,GAAOC,KAAMkD,EAAYnD,EAAKmD,UAAW3B,EAAOxB,EAAKwB,KAAM4B,EAAUpD,EAAKoD,QAASG,EAAWH,EAAQ5B,GAAQ8B,CAC7GH,GAAUT,WAGXa,EAAWJ,EAAUvJ,KAAO0J,EAAQ,GAAKC,EAAWJ,EAAUtJ,KAAOyJ,EAAQ,KAC7EA,GAAStD,EAAKwD,YAElBJ,EAAQK,cAAcjC,EAAM8B,GAC5BtD,EAAKgB,QAAQ5B,EAAQY,OAGzB0D,EAAOjF,EAAMK,QACbiB,KAAM,SAAU/D,GACZ,GAAiBjB,GAAGC,EAAGwI,EAAYJ,EAA/BpD,EAAOC,IACXnB,GAAOkB,GAAQ2D,SAAS,GAAQ3H,GAChCwH,EAAaxD,EAAK2D,QAAU,GAAM,EAClCP,EAAUpD,EAAKoD,QACfpD,EAAKjF,EAAIA,EAAI,GAAImI,IACb1B,KAAM,IACN2B,UAAWnD,EAAK4D,WAAW7I,EAC3ByI,WAAYA,EACZJ,QAASA,IAEbpD,EAAKhF,EAAIA,EAAI,GAAIkI,IACb1B,KAAM,IACN2B,UAAWnD,EAAK4D,WAAW5I,EAC3BwI,WAAYA,EACZJ,QAASA,IAEbpD,EAAK6D,WAAWhD,MACZ,QACA,OACA,MACA,eACA,kBAEAiD,aAAc,SAAU5K,GACpB8G,EAAK+D,QAAU7K,EACf8G,EAAKhG,OAASgG,EAAK4D,WAAW7J,UAAUC,UAE5CgK,MAAO,SAAU9K,GACTL,EAAEK,EAAE+K,MAAM/I,QAAQgJ,QAAQ,KAAKC,GAAG,kCAClCjL,EAAEkL,OAAOC,UAGjBC,cAAe,SAAUpL,GAAV,GACmNqL,GAO1NC,EAAwCC,EAPxCC,EAAkB1E,EAAK+D,QAASY,EAAiBD,EAAgBE,OAAQA,EAAS1L,EAAE0L,OAAQC,EAAa3L,EAAE4L,SAAWJ,EAAgBI,SAAUtC,EAAWxC,EAAK4D,WAAWpB,SAAUK,EAAW7C,EAAK4D,WAAWf,QAChNO,GAAQhC,OAASoB,GAAYqC,EAAa,IAC1CA,GAAiC,IAAlB,EAAIA,IAEnBzB,EAAQhC,MAAQyD,GAAchC,IAC9BgC,EAAahC,EAAWO,EAAQhC,OAEhCoD,EAAUpB,EAAQrI,EAAIiF,EAAKhG,OAAOQ,KAAMiK,EAAUrB,EAAQpI,EAAIgF,EAAKhG,OAAOU,IAC9E6J,GACIxJ,GAAIyJ,EAAUG,EAAe5J,GAAK8J,EAAaD,EAAO7J,EAAIyJ,EAC1DxJ,GAAIyJ,EAAUE,EAAe3J,GAAK6J,EAAaD,EAAO5J,EAAIyJ,GAE9DrB,EAAQ2B,UAAUF,GAClB9J,EAAEsI,SAASkB,EAAYxJ,GACvBC,EAAEqI,SAASkB,EAAYvJ,GACvBgF,EAAK4D,WAAWzB,QAAQiB,EAAQhC,OAChCpB,EAAK+D,QAAU7K,EACfA,EAAE+H,kBAEN+D,KAAM,SAAU9L,GACRA,EAAE+K,MAAM/I,OAAO+J,QAAQC,MAAM,qBAG7BnK,EAAEoI,UAAUT,SAAW1H,EAAEmI,UAAUT,SACnC3H,EAAEsI,SAASnK,EAAE6B,EAAEuI,OACftI,EAAEqI,SAASnK,EAAE8B,EAAEsI,OACfpK,EAAE+H,kBAEF/H,EAAEiM,MAAMC,SAGhBC,IAAK,SAAUnM,GACXA,EAAE+H,uBAKdqE,EAAkBzJ,EAAQ0J,YAAYC,OAAS,WAE/CrH,GADAtC,EAAQ4J,QACI,SAAU1K,EAAGC,EAAGoG,GACxB,MAAO,eAAiBrG,EAAI,MAAQC,EAAI,eAAiBoG,EAAQ,KAGzD,SAAUrG,EAAGC,EAAGoG,GACxB,MAAO,aAAerG,EAAI,MAAQC,EAAI,aAAeoG,EAAQ,KAGjEhD,EAAUO,EAAWG,QACrBiB,KAAM,SAAU3G,GACZ,GAAI4G,GAAOC,IACXtB,GAAWlC,GAAGsD,KAAKhE,KAAKiE,GACxBA,EAAK5G,QAAUP,EAAEO,GACjB4G,EAAK5G,QAAQ,GAAGsM,MAAMC,sBAAwB,WAC9C3F,EAAKjF,EAAI,EACTiF,EAAKhF,EAAI,EACTgF,EAAKoB,MAAQ,EACbpB,EAAK4F,iBAAiBzH,EAAU6B,EAAKjF,EAAGiF,EAAKhF,EAAGgF,EAAKoB,SAEzDqC,cAAe,SAAUjC,EAAMqE,GAC3B5F,KAAKuB,IAASqE,EACd5F,KAAK8C,WAET+C,QAAS,SAAU1E,GACfnB,KAAKmB,MAAQA,EACbnB,KAAK8C,WAETgC,UAAW,SAAUF,GACjB5E,KAAKmB,OAASyD,EACd5E,KAAK8C,WAET5E,UAAW,SAAUoG,GACjBtE,KAAKlF,GAAKwJ,EAAYxJ,EACtBkF,KAAKjF,GAAKuJ,EAAYvJ,EACtBiF,KAAK8C,WAETgD,SAAU,SAAUvE,EAAM/H,GACtBwG,KAAKuB,GAAQ/H,EACbwG,KAAK8C,WAETiD,OAAQ,SAAUzB,GACdzF,EAAOmB,KAAMsE,GACbtE,KAAK8C,WAETA,QAAS,WACL,GAAyCkD,GAArCjG,EAAOC,KAAMlF,EAAIiF,EAAKjF,EAAGC,EAAIgF,EAAKhF,CAClCgF,GAAKkG,QACLnL,EAAIpB,KAAKuM,MAAMnL,GACfC,EAAIrB,KAAKuM,MAAMlL,IAEnBiL,EAAiB9H,EAAUpD,EAAGC,EAAGgF,EAAKoB,OAClC6E,GAAkBjG,EAAKuE,cACnBpK,EAAM0B,QAAQuB,QAAQ+I,MAAQhM,EAAM0B,QAAQuB,QAAQgJ,QAAU,IAC9DpG,EAAK5G,QAAQ,GAAGsM,MAAMnC,SAAW,WACjCvD,EAAK5G,QAAQ,GAAGsM,MAAMlL,KAAOwF,EAAKjF,EAAI,KACtCiF,EAAK5G,QAAQ,GAAGsM,MAAMhL,IAAMsF,EAAKhF,EAAI,MAErCgF,EAAK5G,QAAQ,GAAGsM,MAAMJ,GAAmBW,EAE7CjG,EAAK4F,iBAAiBK,GACtBjG,EAAKgB,QAAQ5B,KAGrBwG,iBAAkB,SAAUrB,GACxBtE,KAAKsE,YAAcA,KAkBvBlG,EAAa7B,EAAOsC,QACpBiB,KAAM,SAAU3G,EAAS4C,GAAnB,GAGEM,GAFA0D,EAAOC,IACXzD,GAAOC,GAAGsD,KAAKhE,KAAKiE,EAAM5G,EAAS4C,GAC/BM,EAAQ0D,EAAKhE,QAAQM,MACnBA,IAAS0C,GAGXA,EAAY1C,GAAO+J,KAAKrG,GAFxBhB,EAAY1C,IAAU0D,IAK9BsG,QACI3G,EACAC,EACAC,GAEJ7D,SACIuK,KAAM,aACNjK,MAAO,WAEXI,QAAS,WACLR,EAAiB8C,EAAaiB,OAElCuG,SAAU,SAAUC,EAAWvN,GAC3B,GAAI8G,GAAOC,KAAMyG,EAAY3H,EAAWiB,EAAKhE,QAAQM,MACrD,IAAIoK,EACA,MAAO1G,GAAKgB,QAAQyF,EAAW3H,KAAW5F,EAAE+K,OACxCyC,UAAWA,EACXC,WAAYzN,EAAEyN,eAI1BC,MAAO,SAAU1N,GACb+G,KAAKuG,SAAS7G,EAAWzG,IAE7B2N,KAAM,SAAU3N,GACZ+G,KAAKuG,SAAS5G,EAAW1G,IAE7B4N,MAAO,SAAU5N,GACb,GAAI8G,GAAOC,KAAMyG,EAAY3H,EAAWiB,EAAKhE,QAAQM,MACjDoK,KACAA,EAAUK,SAAW/G,EAAKwG,SAAS3G,EAAM3G,OAIrDmF,EAAW2I,aAAe,SAAU3K,GAChC,GAA4Dd,GAAxDe,EAAQ0C,EAAY3C,IAAc4C,EAAU5C,EAChD,IAAIC,EAAO,CACP,IAAKf,EAAI,EAAGA,EAAIe,EAAMb,OAAQF,IAC1BiB,EAAOC,GAAGC,QAAQX,KAAKO,EAAMf,GAEjCe,GAAMb,OAAS,QACRuD,GAAY3C,SACZ4C,GAAU5C,KAGzBgC,EAAW4I,OAASjI,EAChBV,EAAiBD,EAAWS,QAC5BiB,KAAM,SAAU3G,EAAS4C,GAAnB,GAGEM,GAFA0D,EAAOC,IACXzD,GAAOC,GAAGsD,KAAKhE,KAAKiE,EAAM5G,EAAS4C,GAC/BM,EAAQ0D,EAAKhE,QAAQM,MACnBA,IAAS2C,GAGXA,EAAU3C,GAAO+J,KAAKrG,GAFtBf,EAAU3C,IAAU0D,IAK5BtD,QAAS,WACLR,EAAiB+C,EAAWgB,OAEhCjE,SACIuK,KAAM,iBACNjK,MAAO,UACPL,OAAQ,QAGZsC,EAAY/B,EAAOsC,QACnBiB,KAAM,SAAU3G,EAAS4C,GACrB,GAAIgE,GAAOC,IACXzD,GAAOC,GAAGsD,KAAKhE,KAAKiE,EAAM5G,EAAS4C,GACnCgE,EAAKkH,YAAa,EAClBlH,EAAK6D,WAAa,GAAIjF,GAAWoB,EAAK5G,SAClC+N,QAAQ,EACRC,gBAAgB,EAChBnL,OAAQ+D,EAAKhE,QAAQC,OACrBoL,UAAWrH,EAAKhE,QAAQ8I,SACxBwC,MAAOzI,EAAMmB,EAAKuH,OAAQvH,GAC1BwH,KAAM3I,EAAMmB,EAAKyH,MAAOzH,GACxBgF,KAAMnG,EAAMmB,EAAK0H,MAAO1H,GACxBqF,IAAKxG,EAAMmB,EAAK2H,KAAM3H,GACtBqE,OAAQxF,EAAMmB,EAAK4H,QAAS5H,GAC5B6H,OAAQhJ,EAAMmB,EAAK8H,QAAS9H,KAEhCA,EAAK+H,iBAAmBlJ,EAAMmB,EAAKgI,UAAWhI,GAC9CA,EAAKiI,eAAiBpJ,EAAMmB,EAAKiI,eAAgBjI,IAErDsG,QACIhH,EACAD,EACAE,EACAC,EACAC,EACAC,GAEJ1D,SACIuK,KAAM,YACNzB,SAAU3K,EAAM0B,QAAQsJ,MAAQ,EAAI,EACpC7I,MAAO,UACP4L,aAAc,KACd1G,KAAM,KACNzH,UAAW,KACXkC,OAAQ,KACRkM,OAAQ,KACRC,YAAY,EACZC,YAAY,EACZtB,SAAS,GAEbuB,WAAY,WACRrI,KAAKiH,YAAa,GAEtBe,eAAgB,SAAU/O,GACtB,GAAI8G,GAAOC,IACP/G,GAAEqP,UAAYpO,EAAMqO,KAAKC,MACzBzI,EAAKwG,SAAS/G,GAAcwE,MAAO/K,IACnC8G,EAAK6D,WAAWQ,WAGxBqE,YAAa,SAAUxP,GACnB,GAAiBqL,GAAbvE,EAAOC,KAAmBjE,EAAUgE,EAAKhE,QAAS2M,EAAa3I,EAAK2I,WAAYnH,EAAOxF,EAAQwF,KAAM0G,EAAelI,EAAKhE,QAAQkM,YACjIA,GACA3D,GACI/J,KAAMtB,EAAE6B,EAAE6N,SAAWV,EAAa1N,KAClCE,IAAKxB,EAAE8B,EAAE4N,SAAWV,EAAaxN,MAGrCsF,EAAK6I,WAAWrO,MAAQtB,EAAE6B,EAAEuI,MAC5BtD,EAAK6I,WAAWnO,KAAOxB,EAAE8B,EAAEsI,MAC3BiB,EAAc1L,EAAEiG,UAAWkB,EAAK6I,aAEhCF,IACApE,EAAY7J,IAAMlB,EAAO+K,EAAY7J,IAAKiO,EAAW3N,GACrDuJ,EAAY/J,KAAOhB,EAAO+K,EAAY/J,KAAMmO,EAAW5N,IAE9C,MAATyG,QACO+C,GAAY7J,IACH,MAAT8G,SACA+C,GAAY/J,KAEvBwF,EAAK8I,KAAKvP,IAAIgL,IAElBwE,oBAAqB,SAAU7N,GAC3B,GAAI8N,GAAiB/I,KAAKjE,QAAQmM,MAClC,OAAOa,IAAkBnQ,EAAEqC,GAAQiJ,GAAG6E,IAE1ClB,QAAS,SAAU5O,GACV+G,KAAK8I,oBAAoB7P,EAAE+K,MAAM/I,SAClChC,EAAE+H,kBAGVsG,OAAQ,SAAUrO,GAAV,GAaIc,GAZJgG,EAAOC,KAAMjE,EAAUgE,EAAKhE,QAASjC,EAAYiC,EAAQjC,UAAYlB,EAAEmD,EAAQjC,WAAa,KAAM+O,EAAO9M,EAAQ8M,IACrH,OAAI7I,MAAK8I,oBAAoB7P,EAAEiM,MAAM8D,eAAiBjN,EAAQoM,aAAepI,EAAKkH,YAC9ElH,EAAK6D,WAAWQ,SAChB,IAEJrE,EAAKkJ,cAAgBhQ,EAAEgC,OACvB8E,EAAKmJ,oBAAsBlP,EAAU+F,EAAKkJ,eACtCJ,IACI9I,EAAK8I,MACL9I,EAAK8I,KAAKM,MAAK,GAAM,GAAMC,SAE/BrJ,EAAK8I,KAAO3O,EAAMmP,WAAWR,GAAQjQ,EAAEiQ,EAAK/M,KAAKiE,EAAMA,EAAKkJ,gBAAkBJ,EAC1E9O,EAASC,EAAU+F,EAAKkJ,eAC5BlJ,EAAK6I,WAAa7O,EAClBgG,EAAK8I,KAAKvP,KACNgK,SAAU,WACVgG,OAAQ,IACR/O,KAAMR,EAAOQ,KACbE,IAAKV,EAAOU,MACb8O,SAASjM,EAASC,MACrBwC,EAAKyJ,QAAQ,UAAW,WACpBzJ,EAAK8I,KAAKY,WAAW,YAErB,KADA,GAAIC,GAAc9Q,EAAEK,EAAEgC,SACdyO,EAAYC,KAAK,iBAAmBD,EAAYlO,QACpDkO,EAAcA,EAAY3Q,QAE9B,QACI6Q,SAAU7J,EAAK8I,KAAKgB,MACpBC,UAAWJ,EAAYC,KAAK,oBAIxC7K,EAAW/C,EAAQM,OAAS0D,EAC5BA,EAAK+G,SAAU,EACXhN,IACAiG,EAAK2I,WAAa7O,EAAoBC,EAAWiG,EAAK8I,OAE1DjQ,EAAE0E,GAAUyM,GAAG7K,EAAOa,EAAKiI,gBACvBjI,EAAKwG,SAASnH,EAAWnG,KACzB8G,EAAK6D,WAAWQ,SAChBrE,EAAKgI,aAEThI,EAAK6D,WAAW1D,UArChBH,IAuCJyH,MAAO,SAAUvO,GACb+G,KAAKiJ,cAAgBhQ,EAAEgC,OACnB+E,KAAKjE,QAAQoM,YAAcnI,KAAKuG,SAASlH,EAAMpG,GAC/C+G,KAAK4D,WAAWQ,SAEhBpE,KAAKiH,YAAa,GAG1BQ,MAAO,SAAUxO,GAAV,GAEC+Q,GASQjM,CAVZ9E,GAAE+H,iBACEgJ,EAAgBhK,KAAKiK,oBAAoBhR,GACzC+G,KAAKjE,QAAQqM,YAAcpI,KAAKkK,iBAAmBF,IACnDhK,KAAKmK,kBAAoB1M,EAAqBuM,GAC9ChK,KAAKkK,eAAiBF,GAE1BhK,KAAKoK,WAAanR,EAClB+G,KAAKqK,iBAAiBpR,EAAG+Q,GACrBhK,KAAKjE,QAAQqM,YACTpI,KAAKmK,kBAAkB,KACnBpM,EAAWJ,EAAmB1E,EAAE6B,EAAE6N,SAAU1P,EAAE8B,EAAE4N,SAAUhM,EAAmBqD,KAAKmK,oBACtFnK,KAAKsK,qBAAuB1R,EAAEiG,UAAWmB,KAAK4I,YAC9C5I,KAAKuK,gBAAkBxM,EACJ,IAAfA,EAAShD,GAA0B,IAAfgD,EAASjD,GAC7B0P,cAAcxK,KAAKyK,iBACnBzK,KAAKyK,gBAAkB,MACfzK,KAAKyK,kBACbzK,KAAKyK,gBAAkBC,YAAY9R,EAAEgG,MAAMoB,KAAM,eAAgB,MAIzEA,KAAK6I,MACL7I,KAAKyI,YAAYxP,IAGzBoR,iBAAkB,SAAUpR,EAAG+Q,GAC3BhK,KAAK2K,gBAAgBX,EAAe,SAAU/O,EAAQU,GAClD,IAAKV,EAKD,MAJIgD,KACAA,EAAesI,SAAS5G,EAAWd,EAAO5F,GAAKyN,WAAY9N,EAAEqF,EAAetC,kBAC5EsC,EAAiB,MAErB,CAEJ,IAAIA,EAAgB,CAChB,GAAItC,IAAkBsC,EAAetC,cACjC,MAEJsC,GAAesI,SAAS5G,EAAWd,EAAO5F,GAAKyN,WAAY9N,EAAEqF,EAAetC,kBAEhFV,EAAOsL,SAAS7G,EAAWb,EAAO5F,GAAKyN,WAAY9N,EAAE+C,MACrDsC,EAAiBY,EAAO5D,GAAUU,cAAeA,MAErDqE,KAAKuG,SAASjH,EAAMT,EAAO5F,GACvByN,WAAYzI,EACZgB,mBAAoB+K,MAG5BY,YAAa,WAAA,GAKLZ,GAEAa,EAAeC,EACfC,EAQAC,EACAC,EACAC,EACAC,EAlBApS,EAASiH,KAAKmK,kBAAkB,GAAIpM,EAAWiC,KAAKuK,gBAAiBa,EAAepL,KAAKsK,oBACxFvR,KAGDiR,EAAgBhK,KAAKiK,oBAAoBjK,KAAKoK,YAClDpK,KAAKqK,iBAAiBrK,KAAKoK,WAAYJ,GAEnCe,EAAahS,IAAW8D,IAAiB,GACzCkO,GACAF,EAAgBvN,EAASC,KAAK8N,aAAepO,EAAQpC,SACrDiQ,EAAgBxN,EAASC,KAAK+N,YAAcrO,EAAQtC,UAEpDkQ,EAAgB9R,EAAOwS,cAAgBxS,EAAOsS,aAC9CP,EAAgB/R,EAAOyS,aAAezS,EAAOuS,aAE7CN,EAASjS,EAAO+D,UAAYiB,EAAShD,EACrCkQ,EAAYJ,GAAiBG,EAAS,GAAKA,EAASjS,EAAOsS,aAC3DH,EAASnS,EAAOgE,WAAagB,EAASjD,EACtCqQ,EAAYL,GAAiBI,EAAS,GAAKA,EAASnS,EAAOuS,YAC3DL,IACAlS,EAAO+D,WAAaiB,EAAShD,GAE7BoQ,IACApS,EAAOgE,YAAcgB,EAASjD,GAE9BkF,KAAK6I,MAAQkC,IAAeI,GAAaF,KACrCA,IACAG,EAAa3Q,KAAOsD,EAAShD,GAE7BoQ,IACAC,EAAa7Q,MAAQwD,EAASjD,GAElCkF,KAAK6I,KAAKvP,IAAI8R,MAGtB1D,KAAM,SAAUzO,GACZ+G,KAAK2K,gBAAgB3K,KAAKiK,oBAAoBhR,GAAI,SAAUgC,EAAQU,GAC5DV,IACAA,EAAO4L,MAAMhI,KAAW5F,GAAKyN,WAAY9N,EAAE+C,MAC3CsC,EAAiB,QAGzB+B,KAAK2H,QAAQ3H,KAAKuG,SAAShH,EAAStG,KAExC0O,QAAS,SAAU8D,GACf,GAAI1L,GAAOC,IACXD,GAAKoK,kBAAoB,KACzBnK,KAAKkK,eAAiB,KACtBM,cAAcxK,KAAKyK,iBACnB1K,EAAKkH,YAAa,EACdlH,EAAK8I,OAAS9I,EAAK+G,QACnB4E,WAAW,WACP3L,EAAK8I,KAAKM,MAAK,GAAM,GACjBsC,EACA1L,EAAK+H,mBAEL/H,EAAK8I,KAAK8C,QAAQ5L,EAAKmJ,oBAAqB,OAAQnJ,EAAK+H,mBAE9D,GAEH/H,EAAKgI,aAGbxB,SAAU,SAAUC,EAAWvN,GAC3B,GAAI8G,GAAOC,IACX,OAAOD,GAAKgB,QAAQyF,EAAW3H,KAAW5F,EAAE+K,OACxClJ,EAAG7B,EAAE6B,EACLC,EAAG9B,EAAE8B,EACLkO,cAAelJ,EAAKkJ,cACpB2C,cAAe3S,EAAEiM,MAAQjM,EAAEiM,MAAM8D,aAAe,KAChDtC,WAAYzN,EAAEyN,WACdzH,mBAAoBhG,EAAEgG,uBAG9BgL,oBAAqB,SAAUhR,GAC3B,GAAIgC,GAASgE,EAAmBhG,GAAI4P,EAAO7I,KAAK6I,IAShD,OARIA,IAAQ/P,EAAS+P,EAAK,GAAI5N,KAC1B4N,EAAKgD,OACL5Q,EAASgE,EAAmBhG,GACvBgC,IACDA,EAASgE,EAAmBhG,IAEhC4P,EAAKiD,QAEF7Q,GAEX0P,gBAAiB,SAAUxR,EAAS4S,GAChC,GAAIC,GAAQ3P,EAAQ2D,KAAKjE,QAAQM,MAAOnB,EAAU6D,EAAY1C,GAAQlB,EAAQ6D,EAAU3C,IACpFnB,GAAWA,EAAQM,QAAUL,GAASA,EAAMK,UAC5CwQ,EAAShR,EAAY7B,EAAS+B,EAASC,GACnC6Q,EACAD,EAASC,EAAO/Q,OAAQ+Q,EAAOrQ,eAE/BoQ,MAIZtP,QAAS,WACL,GAAIsD,GAAOC,IACXzD,GAAOC,GAAGC,QAAQX,KAAKiE,GACvBA,EAAKgI,YACLhI,EAAK6D,WAAWnH,UAChBuD,KAAKmK,kBAAoB,KACzBnK,KAAKkK,eAAiB,KACtBM,cAAcxK,KAAKyK,iBACnB1K,EAAKkJ,cAAgB,MAEzBlB,UAAW,WACP,GAAIhI,GAAOC,IACPD,GAAK8I,MACL9I,EAAK8I,KAAKO,eAEPtK,GAAWiB,EAAKhE,QAAQM,OAC/B0D,EAAKgB,QAAQ,WACbhB,EAAKgB,QAAQtB,GACb7G,EAAE0E,GAAU2O,IAAI/M,EAAOa,EAAKiI,mBAGpC9N,EAAMuE,GAAGyN,OAAO9N,GAChBlE,EAAMuE,GAAGyN,OAAO7N,GAChBnE,EAAMuE,GAAGyN,OAAO5N,GAChBpE,EAAM2F,WAAaA,EACnB3F,EAAML,oBAAsBA,EAC5BgF,EAAO3E,EAAMuE,IACTgF,KAAMA,EACNf,eAAgBA,EAChBvE,QAASA,IAuDbjE,EAAMuE,GAAGH,UAAU6N,OACfxO,mBAAoBA,EACpBhB,mBAAoBA,EACpBc,qBAAsBA,IAE5Bc,OAAOrE,MAAMkS,QACR7N,OAAOrE,OACE,kBAAVvB,SAAwBA,OAAO0T,IAAM1T,OAAS,SAAU2T,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.draganddrop.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.draganddrop', [\n        'kendo.core',\n        'kendo.userevents'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'draganddrop',\n        name: 'Drag & drop',\n        category: 'framework',\n        description: 'Drag & drop functionality for any DOM element.',\n        depends: [\n            'core',\n            'userevents'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, support = kendo.support, document = window.document, $window = $(window), Class = kendo.Class, Widget = kendo.ui.Widget, Observable = kendo.Observable, UserEvents = kendo.UserEvents, proxy = $.proxy, extend = $.extend, getOffset = kendo.getOffset, draggables = {}, dropTargets = {}, dropAreas = {}, lastDropTarget, elementUnderCursor = kendo.elementUnderCursor, KEYUP = 'keyup', CHANGE = 'change', DRAGSTART = 'dragstart', HOLD = 'hold', DRAG = 'drag', DRAGEND = 'dragend', DRAGCANCEL = 'dragcancel', HINTDESTROYED = 'hintDestroyed', DRAGENTER = 'dragenter', DRAGLEAVE = 'dragleave', DROP = 'drop';\n        function contains(parent, child) {\n            try {\n                return $.contains(parent, child) || parent == child;\n            } catch (e) {\n                return false;\n            }\n        }\n        function numericCssPropery(element, property) {\n            return parseInt(element.css(property), 10) || 0;\n        }\n        function within(value, range) {\n            return Math.min(Math.max(value, range.min), range.max);\n        }\n        function containerBoundaries(container, element) {\n            var offset = getOffset(container), outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, minX = offset.left + numericCssPropery(container, 'borderLeftWidth') + numericCssPropery(container, 'paddingLeft'), minY = offset.top + numericCssPropery(container, 'borderTopWidth') + numericCssPropery(container, 'paddingTop'), maxX = minX + container.width() - outerWidth(element, true), maxY = minY + container.height() - outerHeight(element, true);\n            return {\n                x: {\n                    min: minX,\n                    max: maxX\n                },\n                y: {\n                    min: minY,\n                    max: maxY\n                }\n            };\n        }\n        function checkTarget(target, targets, areas) {\n            var theTarget, theFilter, i = 0, targetLen = targets && targets.length, areaLen = areas && areas.length;\n            while (target && target.parentNode) {\n                for (i = 0; i < targetLen; i++) {\n                    theTarget = targets[i];\n                    if (theTarget.element[0] === target) {\n                        return {\n                            target: theTarget,\n                            targetElement: target\n                        };\n                    }\n                }\n                for (i = 0; i < areaLen; i++) {\n                    theFilter = areas[i];\n                    if ($.contains(theFilter.element[0], target) && support.matchesSelector.call(target, theFilter.options.filter)) {\n                        return {\n                            target: theFilter,\n                            targetElement: target\n                        };\n                    }\n                }\n                target = target.parentNode;\n            }\n            return undefined;\n        }\n        var TapCapture = Observable.extend({\n            init: function (element, options) {\n                var that = this, domElement = element[0];\n                that.capture = false;\n                if (domElement.addEventListener) {\n                    $.each(kendo.eventMap.down.split(' '), function () {\n                        domElement.addEventListener(this, proxy(that._press, that), true);\n                    });\n                    $.each(kendo.eventMap.up.split(' '), function () {\n                        domElement.addEventListener(this, proxy(that._release, that), true);\n                    });\n                } else {\n                    $.each(kendo.eventMap.down.split(' '), function () {\n                        domElement.attachEvent(this, proxy(that._press, that));\n                    });\n                    $.each(kendo.eventMap.up.split(' '), function () {\n                        domElement.attachEvent(this, proxy(that._release, that));\n                    });\n                }\n                Observable.fn.init.call(that);\n                that.bind([\n                    'press',\n                    'release'\n                ], options || {});\n            },\n            captureNext: function () {\n                this.capture = true;\n            },\n            cancelCapture: function () {\n                this.capture = false;\n            },\n            _press: function (e) {\n                var that = this;\n                that.trigger('press');\n                if (that.capture) {\n                    e.preventDefault();\n                }\n            },\n            _release: function (e) {\n                var that = this;\n                that.trigger('release');\n                if (that.capture) {\n                    e.preventDefault();\n                    that.cancelCapture();\n                }\n            }\n        });\n        var PaneDimension = Observable.extend({\n            init: function (options) {\n                var that = this;\n                Observable.fn.init.call(that);\n                that.forcedEnabled = false;\n                $.extend(that, options);\n                that.scale = 1;\n                if (that.horizontal) {\n                    that.measure = 'offsetWidth';\n                    that.scrollSize = 'scrollWidth';\n                    that.axis = 'x';\n                } else {\n                    that.measure = 'offsetHeight';\n                    that.scrollSize = 'scrollHeight';\n                    that.axis = 'y';\n                }\n            },\n            makeVirtual: function () {\n                $.extend(this, {\n                    virtual: true,\n                    forcedEnabled: true,\n                    _virtualMin: 0,\n                    _virtualMax: 0\n                });\n            },\n            virtualSize: function (min, max) {\n                if (this._virtualMin !== min || this._virtualMax !== max) {\n                    this._virtualMin = min;\n                    this._virtualMax = max;\n                    this.update();\n                }\n            },\n            outOfBounds: function (offset) {\n                return offset > this.max || offset < this.min;\n            },\n            forceEnabled: function () {\n                this.forcedEnabled = true;\n            },\n            getSize: function () {\n                return this.container[0][this.measure];\n            },\n            getTotal: function () {\n                return this.element[0][this.scrollSize];\n            },\n            rescale: function (scale) {\n                this.scale = scale;\n            },\n            update: function (silent) {\n                var that = this, total = that.virtual ? that._virtualMax : that.getTotal(), scaledTotal = total * that.scale, size = that.getSize();\n                if (total === 0 && !that.forcedEnabled) {\n                    return;\n                }\n                that.max = that.virtual ? -that._virtualMin : 0;\n                that.size = size;\n                that.total = scaledTotal;\n                that.min = Math.min(that.max, size - scaledTotal);\n                that.minScale = size / total;\n                that.centerOffset = (scaledTotal - size) / 2;\n                that.enabled = that.forcedEnabled || scaledTotal > size;\n                if (!silent) {\n                    that.trigger(CHANGE, that);\n                }\n            }\n        });\n        var PaneDimensions = Observable.extend({\n            init: function (options) {\n                var that = this;\n                Observable.fn.init.call(that);\n                that.x = new PaneDimension(extend({ horizontal: true }, options));\n                that.y = new PaneDimension(extend({ horizontal: false }, options));\n                that.container = options.container;\n                that.forcedMinScale = options.minScale;\n                that.maxScale = options.maxScale || 100;\n                that.bind(CHANGE, options);\n            },\n            rescale: function (newScale) {\n                this.x.rescale(newScale);\n                this.y.rescale(newScale);\n                this.refresh();\n            },\n            centerCoordinates: function () {\n                return {\n                    x: Math.min(0, -this.x.centerOffset),\n                    y: Math.min(0, -this.y.centerOffset)\n                };\n            },\n            refresh: function () {\n                var that = this;\n                that.x.update();\n                that.y.update();\n                that.enabled = that.x.enabled || that.y.enabled;\n                that.minScale = that.forcedMinScale || Math.min(that.x.minScale, that.y.minScale);\n                that.fitScale = Math.max(that.x.minScale, that.y.minScale);\n                that.trigger(CHANGE);\n            }\n        });\n        var PaneAxis = Observable.extend({\n            init: function (options) {\n                var that = this;\n                extend(that, options);\n                Observable.fn.init.call(that);\n            },\n            outOfBounds: function () {\n                return this.dimension.outOfBounds(this.movable[this.axis]);\n            },\n            dragMove: function (delta) {\n                var that = this, dimension = that.dimension, axis = that.axis, movable = that.movable, position = movable[axis] + delta;\n                if (!dimension.enabled) {\n                    return;\n                }\n                if (position < dimension.min && delta < 0 || position > dimension.max && delta > 0) {\n                    delta *= that.resistance;\n                }\n                movable.translateAxis(axis, delta);\n                that.trigger(CHANGE, that);\n            }\n        });\n        var Pane = Class.extend({\n            init: function (options) {\n                var that = this, x, y, resistance, movable;\n                extend(that, { elastic: true }, options);\n                resistance = that.elastic ? 0.5 : 0;\n                movable = that.movable;\n                that.x = x = new PaneAxis({\n                    axis: 'x',\n                    dimension: that.dimensions.x,\n                    resistance: resistance,\n                    movable: movable\n                });\n                that.y = y = new PaneAxis({\n                    axis: 'y',\n                    dimension: that.dimensions.y,\n                    resistance: resistance,\n                    movable: movable\n                });\n                that.userEvents.bind([\n                    'press',\n                    'move',\n                    'end',\n                    'gesturestart',\n                    'gesturechange'\n                ], {\n                    gesturestart: function (e) {\n                        that.gesture = e;\n                        that.offset = that.dimensions.container.offset();\n                    },\n                    press: function (e) {\n                        if ($(e.event.target).closest('a').is('[data-navigate-on-press=true]')) {\n                            e.sender.cancel();\n                        }\n                    },\n                    gesturechange: function (e) {\n                        var previousGesture = that.gesture, previousCenter = previousGesture.center, center = e.center, scaleDelta = e.distance / previousGesture.distance, minScale = that.dimensions.minScale, maxScale = that.dimensions.maxScale, coordinates;\n                        if (movable.scale <= minScale && scaleDelta < 1) {\n                            scaleDelta += (1 - scaleDelta) * 0.8;\n                        }\n                        if (movable.scale * scaleDelta >= maxScale) {\n                            scaleDelta = maxScale / movable.scale;\n                        }\n                        var offsetX = movable.x + that.offset.left, offsetY = movable.y + that.offset.top;\n                        coordinates = {\n                            x: (offsetX - previousCenter.x) * scaleDelta + center.x - offsetX,\n                            y: (offsetY - previousCenter.y) * scaleDelta + center.y - offsetY\n                        };\n                        movable.scaleWith(scaleDelta);\n                        x.dragMove(coordinates.x);\n                        y.dragMove(coordinates.y);\n                        that.dimensions.rescale(movable.scale);\n                        that.gesture = e;\n                        e.preventDefault();\n                    },\n                    move: function (e) {\n                        if (e.event.target.tagName.match(/textarea|input/i)) {\n                            return;\n                        }\n                        if (x.dimension.enabled || y.dimension.enabled) {\n                            x.dragMove(e.x.delta);\n                            y.dragMove(e.y.delta);\n                            e.preventDefault();\n                        } else {\n                            e.touch.skip();\n                        }\n                    },\n                    end: function (e) {\n                        e.preventDefault();\n                    }\n                });\n            }\n        });\n        var TRANSFORM_STYLE = support.transitions.prefix + 'Transform', translate;\n        if (support.hasHW3D) {\n            translate = function (x, y, scale) {\n                return 'translate3d(' + x + 'px,' + y + 'px,0) scale(' + scale + ')';\n            };\n        } else {\n            translate = function (x, y, scale) {\n                return 'translate(' + x + 'px,' + y + 'px) scale(' + scale + ')';\n            };\n        }\n        var Movable = Observable.extend({\n            init: function (element) {\n                var that = this;\n                Observable.fn.init.call(that);\n                that.element = $(element);\n                that.element[0].style.webkitTransformOrigin = 'left top';\n                that.x = 0;\n                that.y = 0;\n                that.scale = 1;\n                that._saveCoordinates(translate(that.x, that.y, that.scale));\n            },\n            translateAxis: function (axis, by) {\n                this[axis] += by;\n                this.refresh();\n            },\n            scaleTo: function (scale) {\n                this.scale = scale;\n                this.refresh();\n            },\n            scaleWith: function (scaleDelta) {\n                this.scale *= scaleDelta;\n                this.refresh();\n            },\n            translate: function (coordinates) {\n                this.x += coordinates.x;\n                this.y += coordinates.y;\n                this.refresh();\n            },\n            moveAxis: function (axis, value) {\n                this[axis] = value;\n                this.refresh();\n            },\n            moveTo: function (coordinates) {\n                extend(this, coordinates);\n                this.refresh();\n            },\n            refresh: function () {\n                var that = this, x = that.x, y = that.y, newCoordinates;\n                if (that.round) {\n                    x = Math.round(x);\n                    y = Math.round(y);\n                }\n                newCoordinates = translate(x, y, that.scale);\n                if (newCoordinates != that.coordinates) {\n                    if (kendo.support.browser.msie && kendo.support.browser.version < 10) {\n                        that.element[0].style.position = 'absolute';\n                        that.element[0].style.left = that.x + 'px';\n                        that.element[0].style.top = that.y + 'px';\n                    } else {\n                        that.element[0].style[TRANSFORM_STYLE] = newCoordinates;\n                    }\n                    that._saveCoordinates(newCoordinates);\n                    that.trigger(CHANGE);\n                }\n            },\n            _saveCoordinates: function (coordinates) {\n                this.coordinates = coordinates;\n            }\n        });\n        function destroyDroppable(collection, widget) {\n            var groupName = widget.options.group, droppables = collection[groupName], i;\n            Widget.fn.destroy.call(widget);\n            if (droppables.length > 1) {\n                for (i = 0; i < droppables.length; i++) {\n                    if (droppables[i] == widget) {\n                        droppables.splice(i, 1);\n                        break;\n                    }\n                }\n            } else {\n                droppables.length = 0;\n                delete collection[groupName];\n            }\n        }\n        var DropTarget = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                var group = that.options.group;\n                if (!(group in dropTargets)) {\n                    dropTargets[group] = [that];\n                } else {\n                    dropTargets[group].push(that);\n                }\n            },\n            events: [\n                DRAGENTER,\n                DRAGLEAVE,\n                DROP\n            ],\n            options: {\n                name: 'DropTarget',\n                group: 'default'\n            },\n            destroy: function () {\n                destroyDroppable(dropTargets, this);\n            },\n            _trigger: function (eventName, e) {\n                var that = this, draggable = draggables[that.options.group];\n                if (draggable) {\n                    return that.trigger(eventName, extend({}, e.event, {\n                        draggable: draggable,\n                        dropTarget: e.dropTarget\n                    }));\n                }\n            },\n            _over: function (e) {\n                this._trigger(DRAGENTER, e);\n            },\n            _out: function (e) {\n                this._trigger(DRAGLEAVE, e);\n            },\n            _drop: function (e) {\n                var that = this, draggable = draggables[that.options.group];\n                if (draggable) {\n                    draggable.dropped = !that._trigger(DROP, e);\n                }\n            }\n        });\n        DropTarget.destroyGroup = function (groupName) {\n            var group = dropTargets[groupName] || dropAreas[groupName], i;\n            if (group) {\n                for (i = 0; i < group.length; i++) {\n                    Widget.fn.destroy.call(group[i]);\n                }\n                group.length = 0;\n                delete dropTargets[groupName];\n                delete dropAreas[groupName];\n            }\n        };\n        DropTarget._cache = dropTargets;\n        var DropTargetArea = DropTarget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                var group = that.options.group;\n                if (!(group in dropAreas)) {\n                    dropAreas[group] = [that];\n                } else {\n                    dropAreas[group].push(that);\n                }\n            },\n            destroy: function () {\n                destroyDroppable(dropAreas, this);\n            },\n            options: {\n                name: 'DropTargetArea',\n                group: 'default',\n                filter: null\n            }\n        });\n        var Draggable = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that._activated = false;\n                that.userEvents = new UserEvents(that.element, {\n                    global: true,\n                    allowSelection: true,\n                    filter: that.options.filter,\n                    threshold: that.options.distance,\n                    start: proxy(that._start, that),\n                    hold: proxy(that._hold, that),\n                    move: proxy(that._drag, that),\n                    end: proxy(that._end, that),\n                    cancel: proxy(that._cancel, that),\n                    select: proxy(that._select, that)\n                });\n                that._afterEndHandler = proxy(that._afterEnd, that);\n                that._captureEscape = proxy(that._captureEscape, that);\n            },\n            events: [\n                HOLD,\n                DRAGSTART,\n                DRAG,\n                DRAGEND,\n                DRAGCANCEL,\n                HINTDESTROYED\n            ],\n            options: {\n                name: 'Draggable',\n                distance: kendo.support.touch ? 0 : 5,\n                group: 'default',\n                cursorOffset: null,\n                axis: null,\n                container: null,\n                filter: null,\n                ignore: null,\n                holdToDrag: false,\n                autoScroll: false,\n                dropped: false\n            },\n            cancelHold: function () {\n                this._activated = false;\n            },\n            _captureEscape: function (e) {\n                var that = this;\n                if (e.keyCode === kendo.keys.ESC) {\n                    that._trigger(DRAGCANCEL, { event: e });\n                    that.userEvents.cancel();\n                }\n            },\n            _updateHint: function (e) {\n                var that = this, coordinates, options = that.options, boundaries = that.boundaries, axis = options.axis, cursorOffset = that.options.cursorOffset;\n                if (cursorOffset) {\n                    coordinates = {\n                        left: e.x.location + cursorOffset.left,\n                        top: e.y.location + cursorOffset.top\n                    };\n                } else {\n                    that.hintOffset.left += e.x.delta;\n                    that.hintOffset.top += e.y.delta;\n                    coordinates = $.extend({}, that.hintOffset);\n                }\n                if (boundaries) {\n                    coordinates.top = within(coordinates.top, boundaries.y);\n                    coordinates.left = within(coordinates.left, boundaries.x);\n                }\n                if (axis === 'x') {\n                    delete coordinates.top;\n                } else if (axis === 'y') {\n                    delete coordinates.left;\n                }\n                that.hint.css(coordinates);\n            },\n            _shouldIgnoreTarget: function (target) {\n                var ignoreSelector = this.options.ignore;\n                return ignoreSelector && $(target).is(ignoreSelector);\n            },\n            _select: function (e) {\n                if (!this._shouldIgnoreTarget(e.event.target)) {\n                    e.preventDefault();\n                }\n            },\n            _start: function (e) {\n                var that = this, options = that.options, container = options.container ? $(options.container) : null, hint = options.hint;\n                if (this._shouldIgnoreTarget(e.touch.initialTouch) || options.holdToDrag && !that._activated) {\n                    that.userEvents.cancel();\n                    return;\n                }\n                that.currentTarget = e.target;\n                that.currentTargetOffset = getOffset(that.currentTarget);\n                if (hint) {\n                    if (that.hint) {\n                        that.hint.stop(true, true).remove();\n                    }\n                    that.hint = kendo.isFunction(hint) ? $(hint.call(that, that.currentTarget)) : hint;\n                    var offset = getOffset(that.currentTarget);\n                    that.hintOffset = offset;\n                    that.hint.css({\n                        position: 'absolute',\n                        zIndex: 20000,\n                        left: offset.left,\n                        top: offset.top\n                    }).appendTo(document.body);\n                    that.angular('compile', function () {\n                        that.hint.removeAttr('ng-repeat');\n                        var scopeTarget = $(e.target);\n                        while (!scopeTarget.data('$$kendoScope') && scopeTarget.length) {\n                            scopeTarget = scopeTarget.parent();\n                        }\n                        return {\n                            elements: that.hint.get(),\n                            scopeFrom: scopeTarget.data('$$kendoScope')\n                        };\n                    });\n                }\n                draggables[options.group] = that;\n                that.dropped = false;\n                if (container) {\n                    that.boundaries = containerBoundaries(container, that.hint);\n                }\n                $(document).on(KEYUP, that._captureEscape);\n                if (that._trigger(DRAGSTART, e)) {\n                    that.userEvents.cancel();\n                    that._afterEnd();\n                }\n                that.userEvents.capture();\n            },\n            _hold: function (e) {\n                this.currentTarget = e.target;\n                if (this.options.holdToDrag && this._trigger(HOLD, e)) {\n                    this.userEvents.cancel();\n                } else {\n                    this._activated = true;\n                }\n            },\n            _drag: function (e) {\n                e.preventDefault();\n                var cursorElement = this._elementUnderCursor(e);\n                if (this.options.autoScroll && this._cursorElement !== cursorElement) {\n                    this._scrollableParent = findScrollableParent(cursorElement);\n                    this._cursorElement = cursorElement;\n                }\n                this._lastEvent = e;\n                this._processMovement(e, cursorElement);\n                if (this.options.autoScroll) {\n                    if (this._scrollableParent[0]) {\n                        var velocity = autoScrollVelocity(e.x.location, e.y.location, scrollableViewPort(this._scrollableParent));\n                        this._scrollCompenstation = $.extend({}, this.hintOffset);\n                        this._scrollVelocity = velocity;\n                        if (velocity.y === 0 && velocity.x === 0) {\n                            clearInterval(this._scrollInterval);\n                            this._scrollInterval = null;\n                        } else if (!this._scrollInterval) {\n                            this._scrollInterval = setInterval($.proxy(this, '_autoScroll'), 50);\n                        }\n                    }\n                }\n                if (this.hint) {\n                    this._updateHint(e);\n                }\n            },\n            _processMovement: function (e, cursorElement) {\n                this._withDropTarget(cursorElement, function (target, targetElement) {\n                    if (!target) {\n                        if (lastDropTarget) {\n                            lastDropTarget._trigger(DRAGLEAVE, extend(e, { dropTarget: $(lastDropTarget.targetElement) }));\n                            lastDropTarget = null;\n                        }\n                        return;\n                    }\n                    if (lastDropTarget) {\n                        if (targetElement === lastDropTarget.targetElement) {\n                            return;\n                        }\n                        lastDropTarget._trigger(DRAGLEAVE, extend(e, { dropTarget: $(lastDropTarget.targetElement) }));\n                    }\n                    target._trigger(DRAGENTER, extend(e, { dropTarget: $(targetElement) }));\n                    lastDropTarget = extend(target, { targetElement: targetElement });\n                });\n                this._trigger(DRAG, extend(e, {\n                    dropTarget: lastDropTarget,\n                    elementUnderCursor: cursorElement\n                }));\n            },\n            _autoScroll: function () {\n                var parent = this._scrollableParent[0], velocity = this._scrollVelocity, compensation = this._scrollCompenstation;\n                if (!parent) {\n                    return;\n                }\n                var cursorElement = this._elementUnderCursor(this._lastEvent);\n                this._processMovement(this._lastEvent, cursorElement);\n                var yIsScrollable, xIsScrollable;\n                var isRootNode = parent === scrollableRoot()[0];\n                if (isRootNode) {\n                    yIsScrollable = document.body.scrollHeight > $window.height();\n                    xIsScrollable = document.body.scrollWidth > $window.width();\n                } else {\n                    yIsScrollable = parent.offsetHeight <= parent.scrollHeight;\n                    xIsScrollable = parent.offsetWidth <= parent.scrollWidth;\n                }\n                var yDelta = parent.scrollTop + velocity.y;\n                var yInBounds = yIsScrollable && yDelta > 0 && yDelta < parent.scrollHeight;\n                var xDelta = parent.scrollLeft + velocity.x;\n                var xInBounds = xIsScrollable && xDelta > 0 && xDelta < parent.scrollWidth;\n                if (yInBounds) {\n                    parent.scrollTop += velocity.y;\n                }\n                if (xInBounds) {\n                    parent.scrollLeft += velocity.x;\n                }\n                if (this.hint && isRootNode && (xInBounds || yInBounds)) {\n                    if (yInBounds) {\n                        compensation.top += velocity.y;\n                    }\n                    if (xInBounds) {\n                        compensation.left += velocity.x;\n                    }\n                    this.hint.css(compensation);\n                }\n            },\n            _end: function (e) {\n                this._withDropTarget(this._elementUnderCursor(e), function (target, targetElement) {\n                    if (target) {\n                        target._drop(extend({}, e, { dropTarget: $(targetElement) }));\n                        lastDropTarget = null;\n                    }\n                });\n                this._cancel(this._trigger(DRAGEND, e));\n            },\n            _cancel: function (isDefaultPrevented) {\n                var that = this;\n                that._scrollableParent = null;\n                this._cursorElement = null;\n                clearInterval(this._scrollInterval);\n                that._activated = false;\n                if (that.hint && !that.dropped) {\n                    setTimeout(function () {\n                        that.hint.stop(true, true);\n                        if (isDefaultPrevented) {\n                            that._afterEndHandler();\n                        } else {\n                            that.hint.animate(that.currentTargetOffset, 'fast', that._afterEndHandler);\n                        }\n                    }, 0);\n                } else {\n                    that._afterEnd();\n                }\n            },\n            _trigger: function (eventName, e) {\n                var that = this;\n                return that.trigger(eventName, extend({}, e.event, {\n                    x: e.x,\n                    y: e.y,\n                    currentTarget: that.currentTarget,\n                    initialTarget: e.touch ? e.touch.initialTouch : null,\n                    dropTarget: e.dropTarget,\n                    elementUnderCursor: e.elementUnderCursor\n                }));\n            },\n            _elementUnderCursor: function (e) {\n                var target = elementUnderCursor(e), hint = this.hint;\n                if (hint && contains(hint[0], target)) {\n                    hint.hide();\n                    target = elementUnderCursor(e);\n                    if (!target) {\n                        target = elementUnderCursor(e);\n                    }\n                    hint.show();\n                }\n                return target;\n            },\n            _withDropTarget: function (element, callback) {\n                var result, group = this.options.group, targets = dropTargets[group], areas = dropAreas[group];\n                if (targets && targets.length || areas && areas.length) {\n                    result = checkTarget(element, targets, areas);\n                    if (result) {\n                        callback(result.target, result.targetElement);\n                    } else {\n                        callback();\n                    }\n                }\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that._afterEnd();\n                that.userEvents.destroy();\n                this._scrollableParent = null;\n                this._cursorElement = null;\n                clearInterval(this._scrollInterval);\n                that.currentTarget = null;\n            },\n            _afterEnd: function () {\n                var that = this;\n                if (that.hint) {\n                    that.hint.remove();\n                }\n                delete draggables[that.options.group];\n                that.trigger('destroy');\n                that.trigger(HINTDESTROYED);\n                $(document).off(KEYUP, that._captureEscape);\n            }\n        });\n        kendo.ui.plugin(DropTarget);\n        kendo.ui.plugin(DropTargetArea);\n        kendo.ui.plugin(Draggable);\n        kendo.TapCapture = TapCapture;\n        kendo.containerBoundaries = containerBoundaries;\n        extend(kendo.ui, {\n            Pane: Pane,\n            PaneDimensions: PaneDimensions,\n            Movable: Movable\n        });\n        function scrollableViewPort(element) {\n            var root = scrollableRoot()[0], offset, top, left;\n            if (element[0] === root) {\n                top = root.scrollTop;\n                left = root.scrollLeft;\n                return {\n                    top: top,\n                    left: left,\n                    bottom: top + $window.height(),\n                    right: left + $window.width()\n                };\n            } else {\n                offset = element.offset();\n                offset.bottom = offset.top + element.height();\n                offset.right = offset.left + element.width();\n                return offset;\n            }\n        }\n        function scrollableRoot() {\n            return $(kendo.support.browser.edge || kendo.support.browser.safari ? document.body : document.documentElement);\n        }\n        function findScrollableParent(element) {\n            var root = scrollableRoot();\n            if (!element || element === document.body || element === document.documentElement) {\n                return root;\n            }\n            var parent = $(element)[0];\n            while (parent && !kendo.isScrollable(parent) && parent !== document.body) {\n                parent = parent.parentNode;\n            }\n            if (parent === document.body) {\n                return root;\n            }\n            return $(parent);\n        }\n        function autoScrollVelocity(mouseX, mouseY, rect) {\n            var velocity = {\n                x: 0,\n                y: 0\n            };\n            var AUTO_SCROLL_AREA = 50;\n            if (mouseX - rect.left < AUTO_SCROLL_AREA) {\n                velocity.x = -(AUTO_SCROLL_AREA - (mouseX - rect.left));\n            } else if (rect.right - mouseX < AUTO_SCROLL_AREA) {\n                velocity.x = AUTO_SCROLL_AREA - (rect.right - mouseX);\n            }\n            if (mouseY - rect.top < AUTO_SCROLL_AREA) {\n                velocity.y = -(AUTO_SCROLL_AREA - (mouseY - rect.top));\n            } else if (rect.bottom - mouseY < AUTO_SCROLL_AREA) {\n                velocity.y = AUTO_SCROLL_AREA - (rect.bottom - mouseY);\n            }\n            return velocity;\n        }\n        kendo.ui.Draggable.utils = {\n            autoScrollVelocity: autoScrollVelocity,\n            scrollableViewPort: scrollableViewPort,\n            findScrollableParent: findScrollableParent\n        };\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}