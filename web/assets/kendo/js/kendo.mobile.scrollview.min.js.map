{"version": 3, "sources": ["kendo.mobile.scrollview.js"], "names": ["f", "define", "$", "undefined", "className", "name", "TRANSITION_END", "DRAG_START", "DRAG_END", "ElasticPane", "<PERSON><PERSON><PERSON>iew<PERSON><PERSON><PERSON>", "VirtualScrollViewContent", "Page", "ScrollView", "kendo", "window", "mobile", "ui", "proxy", "Transition", "effects", "Pane", "PaneDimensions", "Widget", "DataBoundWidget", "DataSource", "data", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "math", "Math", "abs", "ceil", "round", "max", "min", "floor", "CHANGE", "CHANGING", "REFRESH", "CURRENT_PAGE_CLASS", "VIRTUAL_PAGE_CLASS", "FUNCTION", "ITEM_CHANGE", "CLEANUP", "VIRTUAL_PAGE_COUNT", "LEFT_PAGE", "CETER_PAGE", "RIGHT_PAGE", "LEFT_SWIPE", "NUDGE", "RIGHT_SWIPE", "Pager", "Class", "extend", "init", "scrollView", "that", "this", "element", "append", "_changeProxy", "_refreshProxy", "bind", "items", "children", "_refresh", "e", "idx", "pageHTML", "pageCount", "html", "eq", "page", "addClass", "_change", "removeClass", "destroy", "unbind", "remove", "ScrollViewPager", "Observable", "options", "movable", "transition", "userEvents", "dimensions", "dimension", "pane", "fn", "call", "container", "parent", "Movable", "axis", "onEnd", "trigger", "UserEvents", "fastTap", "start", "x", "velocity", "y", "capture", "cancel", "allowSelection", "end", "elastic", "duration", "size", "width", "getSize", "height", "total", "getTotal", "offset", "updateDimension", "update", "refresh", "moveTo", "moveAxis", "transitionTo", "ease", "instant", "location", "ScrollViewElasticPane", "_getPages", "pageSize", "contentHeight", "enablePager", "pagerOverlay", "scrollTo", "easeOutExpo", "paneMoved", "swipeType", "bounce", "callback", "snap", "nextPage", "approx", "easeOutBack", "minSnap", "maxSnap", "currentPage", "updatePage", "forcePageUpdate", "resizeTo", "containerHeight", "pager", "pageElements", "find", "length", "_outerHeight", "css", "_paged", "roleSelector", "_templates", "pages", "_initPages", "forceEnabled", "setDataSource", "dataSource", "create", "_buffer", "_pendingPageRefresh", "_pendingWidgetRefresh", "_viewShow", "setTimeout", "_resetPages", "itemsPerPage", "buffer", "_resizeProxy", "_resetProxy", "_endReachedProxy", "resize", "reset", "endreached", "template", "emptyTemplate", "templateProxy", "emptyTemplateProxy", "i", "push", "<PERSON><PERSON><PERSON><PERSON>", "_repositionPages", "dataItem", "syncDataSource", "at", "_updatePagesContent", "thresholdPassed", "isEndReached", "delta", "_cancelMove", "_moveBackward", "_moveForward", "shift", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unshift", "pop", "_resetMovable", "threshold", "_onResize", "_onReset", "_onEndReached", "position", "index", "view", "isArray", "item", "content", "ns", "theContent", "VirtualPage", "empty", "mobileContainer", "stripWhitespace", "wrapInner", "inner", "first", "transitionEnd", "dragStart", "dragEnd", "change", "_syncWithContent", "angular", "elements", "_content", "nullObject", "viewInit", "viewShow", "velocityThreshold", "bounceVelocityThreshold", "autoBind", "events", "value", "indexOf", "prev", "prevPage", "eventData", "next", "emptyDataSource", "fetch", "Array", "_dragStart", "_dragEnd", "_transitionEnd", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,2BACH,WACA,aACA,qBACDD,IACL,WA8pBE,MAlpBC,UAAUE,EAAGC,GAEV,QAASC,GAAUC,GACf,MAAO,KAAOA,EAAO,OAASA,EAHrC,GAuCOC,GAAkCC,EAA0BC,EAC5DC,EA+FAC,EA4EAC,EAqOAC,EAmBAC,EA1cAC,EAAQC,OAAOD,MAAOE,EAASF,EAAME,OAAQC,EAAKD,EAAOC,GAAIC,EAAQhB,EAAEgB,MAAOC,EAAaL,EAAMM,QAAQD,WAAYE,EAAOP,EAAMG,GAAGI,KAAMC,EAAiBR,EAAMG,GAAGK,eAAgBC,EAASN,EAAGO,gBAAiBC,EAAaX,EAAMY,KAAKD,WAAYE,EAASb,EAAMY,KAAKC,OAAQC,EAAcd,EAAMY,KAAKE,YAAaC,EAAOC,KAAMC,EAAMF,EAAKE,IAAKC,EAAOH,EAAKG,KAAMC,EAAQJ,EAAKI,MAAOC,EAAML,EAAKK,IAAKC,EAAMN,EAAKM,IAAKC,EAAQP,EAAKO,MAAOC,EAAS,SAAUC,EAAW,WAAYC,EAAU,UAAWC,EAAqB,eAAgBC,EAAqB,eAAgBC,EAAW,WAAYC,EAAc,aAAcC,EAAU,UAAWC,EAAqB,EAAGC,KAAgBC,EAAa,EAAGC,EAAa,EAAGC,KAAiBC,EAAQ,EAAGC,EAAc,EAIlvBC,EAAQtC,EAAMuC,MAAMC,QACpBC,KAAM,SAAUC,GACZ,GAAIC,GAAOC,KAAMC,EAAUzD,EAAE,cAAiBE,EAAU,SAAW,MACnEoD,GAAWG,QAAQC,OAAOD,GAC1BD,KAAKG,aAAe3C,EAAMuC,EAAM,WAChCC,KAAKI,cAAgB5C,EAAMuC,EAAM,YACjCD,EAAWO,KAAK1B,EAAQqB,KAAKG,cAC7BL,EAAWO,KAAKxB,EAASmB,KAAKI,eAC9B5D,EAAEoD,OAAOG,GACLE,QAASA,EACTH,WAAYA,KAGpBQ,MAAO,WACH,MAAON,MAAKC,QAAQM,YAExBC,SAAU,SAAUC,GAAV,GAEGC,GADLC,EAAW,EACf,KAASD,EAAM,EAAGA,EAAMD,EAAEG,UAAWF,IACjCC,GAAY,OAEhBX,MAAKC,QAAQY,KAAKF,GAClBX,KAAKM,QAAQQ,GAAGL,EAAEM,MAAMC,SAAStE,EAAUoC,KAE/CmC,QAAS,SAAUR,GACfT,KAAKM,QAAQY,YAAYxE,EAAUoC,IAAqBgC,GAAGL,EAAEM,MAAMC,SAAStE,EAAUoC,KAE1FqC,QAAS,WACLnB,KAAKF,WAAWsB,OAAOzC,EAAQqB,KAAKG,cACpCH,KAAKF,WAAWsB,OAAOvC,EAASmB,KAAKI,eACrCJ,KAAKC,QAAQoB,WAGrBjE,GAAME,OAAOC,GAAG+D,gBAAkB5B,EAC9B9C,EAAiB,gBAAiBC,EAAa,YAAaC,EAAW,UACvEC,EAAcK,EAAMmE,WAAW3B,QAC/BC,KAAM,SAAUI,EAASuB,GAAnB,GAKEC,GAASC,EAAYC,EAAYC,EAAYC,EAAWC,EAJxD/B,EAAOC,IACX5C,GAAMmE,WAAWQ,GAAGlC,KAAKmC,KAAKhC,MAC9BA,KAAKC,QAAUA,EACfD,KAAKiC,UAAYhC,EAAQiC,SAEzBT,EAAU,GAAIrE,GAAMG,GAAG4E,QAAQpC,EAAKE,SACpCyB,EAAa,GAAIjE,IACb2E,KAAM,IACNX,QAASA,EACTY,MAAO,WACHtC,EAAKuC,QAAQ1F,MAGrB+E,EAAa,GAAIvE,GAAMmF,WAAWtC,GAC9BuC,SAAS,EACTC,MAAO,SAAUhC,GACW,EAApBpC,EAAIoC,EAAEiC,EAAEC,WAAiBtE,EAAIoC,EAAEmC,EAAED,UACjChB,EAAWkB,UAEXlB,EAAWmB,SAEf/C,EAAKuC,QAAQzF,EAAY4D,GACzBiB,EAAWoB,UAEfC,gBAAgB,EAChBC,IAAK,SAAUvC,GACXV,EAAKuC,QAAQxF,EAAU2D,MAG/BmB,EAAa,GAAIhE,IACbqC,QAASF,EAAKE,QACdgC,UAAWlC,EAAKkC,YAEpBJ,EAAYD,EAAWc,EACvBb,EAAUxB,KAAK1B,EAAQ,WACnBoB,EAAKuC,QAAQ3D,KAEjBmD,EAAO,GAAInE,IACPiE,WAAYA,EACZD,WAAYA,EACZF,QAASA,EACTwB,SAAS,IAEbzG,EAAEoD,OAAOG,GACLmD,SAAU1B,GAAWA,EAAQ0B,UAAY,EACzCzB,QAASA,EACTC,WAAYA,EACZC,WAAYA,EACZC,WAAYA,EACZC,UAAWA,EACXC,KAAMA,IAEV9B,KAAKK,MACDzD,EACAC,EACAC,EACA6B,GACD6C,IAEP2B,KAAM,WACF,OACIC,MAAOpD,KAAK4B,WAAWc,EAAEW,UACzBC,OAAQtD,KAAK4B,WAAWgB,EAAES,YAGlCE,MAAO,WACH,MAAOvD,MAAK6B,UAAU2B,YAE1BC,OAAQ,WACJ,OAAQzD,KAAKyB,QAAQiB,GAEzBgB,gBAAiB,WACb1D,KAAK6B,UAAU8B,QAAO,IAE1BC,QAAS,WACL5D,KAAK4B,WAAWgC,WAEpBC,OAAQ,SAAUJ,GACdzD,KAAKyB,QAAQqC,SAAS,KAAML,IAEhCM,aAAc,SAAUN,EAAQO,EAAMC,GAC9BA,EACAjE,KAAK6D,QAAQJ,GAEbzD,KAAK0B,WAAWmC,QACZK,SAAUT,EACVP,SAAUlD,KAAKkD,SACfc,KAAMA,OAKtB5G,EAAME,OAAOC,GAAG4G,sBAAwBpH,EACpCC,EAAoBI,EAAMmE,WAAW3B,QACrCC,KAAM,SAAUI,EAAS6B,EAAMN,GAC3B,GAAIzB,GAAOC,IACX5C,GAAMmE,WAAWQ,GAAGlC,KAAKmC,KAAKhC,MAC9BD,EAAKE,QAAUA,EACfF,EAAK+B,KAAOA,EACZ/B,EAAKqE,YACLpE,KAAKe,KAAO,EACZf,KAAKqE,SAAW7C,EAAQ6C,UAAY,EACpCrE,KAAKsE,cAAgB9C,EAAQ8C,cAC7BtE,KAAKuE,YAAc/C,EAAQ+C,YAC3BvE,KAAKwE,aAAehD,EAAQgD,cAEhCC,SAAU,SAAU1D,EAAMkD,GACtBjE,KAAKe,KAAOA,EACZf,KAAK8B,KAAKiC,cAAchD,EAAOf,KAAK8B,KAAKqB,OAAOC,MAAO3F,EAAWiH,YAAaT,IAEnFU,UAAW,SAAUC,EAAWC,EAAQC,EAAUb,GAC9C,GAA+Jc,GAAMC,EAAjKjF,EAAOC,KAAM8B,EAAO/B,EAAK+B,KAAMsB,EAAQtB,EAAKqB,OAAOC,MAAQrD,EAAKsE,SAAUY,EAAS1G,EAAOyF,EAAOa,EAASpH,EAAWyH,YAAczH,EAAWiH,WAC9IE,KAAcrF,EACd0F,EAAS3G,EACFsG,IAAcnF,IACrBwF,EAASvG,GAEbsG,EAAWC,EAAOnD,EAAK2B,SAAWL,GAClC2B,EAAOvG,EAAIuB,EAAKoF,QAAS1G,GAAKuG,EAAW5B,EAAOrD,EAAKqF,UACjDJ,GAAYjF,EAAKgB,MACb+D,GAAYA,GACRO,YAAatF,EAAKgB,KAClBiE,SAAUA,MAEdD,GAAQhF,EAAKgB,KAAOe,EAAKqB,OAAOC,OAGxCtB,EAAKiC,aAAagB,EAAMf,EAAMC,IAElCqB,WAAY,WACR,GAAIxD,GAAO9B,KAAK8B,KAAMf,EAAOxC,EAAMuD,EAAK2B,SAAW3B,EAAKqB,OAAOC,MAC/D,OAAIrC,IAAQf,KAAKe,OACbf,KAAKe,KAAOA,GACL,IAIfwE,gBAAiB,WACb,MAAOvF,MAAKsF,cAEhBE,SAAU,SAAUrC,GAAV,GAIEsC,GAEIC,EALR5D,EAAO9B,KAAK8B,KAAMsB,EAAQD,EAAKC,KACnCpD,MAAK2F,aAAavC,MAAMA,GACG,SAAvBpD,KAAKsE,gBACDmB,EAAkBzF,KAAKC,QAAQiC,SAASoB,SACxCtD,KAAKuE,eAAgB,IACjBmB,EAAQ1F,KAAKC,QAAQiC,SAAS0D,KAAK,gBAClC5F,KAAKwE,cAAgBkB,EAAMG,SAC5BJ,GAAmBrI,EAAM0I,aAAaJ,GAAO,KAGrD1F,KAAKC,QAAQ8F,IAAI,SAAUN,GAC3BzF,KAAK2F,aAAaI,IAAI,SAAUN,IAEpC3D,EAAK4B,kBACA1D,KAAKgG,SACNhG,KAAKe,KAAOrC,EAAMoD,EAAK2B,SAAWL,IAEtCpD,KAAKyE,SAASzE,KAAKe,MAAM,GACzBf,KAAKY,UAAYtC,EAAKwD,EAAKyB,QAAUH,GACrCpD,KAAKmF,UAAYnF,KAAKY,UAAY,GAAKwC,EACvCpD,KAAKoF,QAAU,GAEnBhB,UAAW,WACPpE,KAAK2F,aAAe3F,KAAKC,QAAQ2F,KAAKxI,EAAM6I,aAAa,SACzDjG,KAAKgG,OAAShG,KAAK2F,aAAaE,OAAS,KAGjDzI,EAAME,OAAOC,GAAGP,kBAAoBA,EAChCC,EAA2BG,EAAMmE,WAAW3B,QAC5CC,KAAM,SAAUI,EAAS6B,EAAMN,GAC3B,GAAIzB,GAAOC,IACX5C,GAAMmE,WAAWQ,GAAGlC,KAAKmC,KAAKhC,MAC9BD,EAAKE,QAAUA,EACfF,EAAK+B,KAAOA,EACZ/B,EAAKyB,QAAUA,EACfzB,EAAKmG,aACLnG,EAAKgB,KAAOS,EAAQT,MAAQ,EAC5BhB,EAAKoG,SACLpG,EAAKqG,aACLrG,EAAKyF,SAASzF,EAAK+B,KAAKqB,QACxBpD,EAAK+B,KAAKD,UAAUwE,gBAExBC,cAAe,SAAUC,GACrBvG,KAAKuG,WAAaxI,EAAWyI,OAAOD,GACpCvG,KAAKyG,UACLzG,KAAK0G,qBAAsB,EAC3B1G,KAAK2G,uBAAwB,GAEjCC,UAAW,WACP,GAAI7G,GAAOC,IACPD,GAAK4G,wBACLE,WAAW,WACP9G,EAAK+G,eACN,GACH/G,EAAK4G,uBAAwB,IAGrCF,QAAS,WACL,GAAIM,GAAe/G,KAAKwB,QAAQuF,YAC5B/G,MAAKgH,QACLhH,KAAKgH,OAAO7F,UAGZnB,KAAKgH,OADLD,EAAe,EACD,GAAI7I,GAAY8B,KAAKuG,WAAYQ,GAEjC,GAAI9I,GAAO+B,KAAKuG,WAA2B,EAAfQ,GAE9C/G,KAAKiH,aAAezJ,EAAMwC,KAAM,aAChCA,KAAKkH,YAAc1J,EAAMwC,KAAM,YAC/BA,KAAKmH,iBAAmB3J,EAAMwC,KAAM,iBACpCA,KAAKgH,OAAO3G,MACR+G,OAAUpH,KAAKiH,aACfI,MAASrH,KAAKkH,YACdI,WAActH,KAAKmH,oBAG3BjB,WAAY,WACR,GAAIqB,GAAWvH,KAAKwB,QAAQ+F,SAAUC,EAAgBxH,KAAKwB,QAAQgG,cAAeC,KAAoBC,WAC3FH,KAAavI,IACpByI,EAAcF,SAAWA,EACzBA,EAAW,0BAEfvH,KAAKuH,SAAW/J,EAAMJ,EAAMmK,SAASA,GAAWE,SACrCD,KAAkBxI,IACzB0I,EAAmBF,cAAgBA,EACnCA,EAAgB,+BAEpBxH,KAAKwH,cAAgBhK,EAAMJ,EAAMmK,SAASC,GAAgBE,IAE9DtB,WAAY,WAAA,GACwCrF,GACvC4G,EADLxB,EAAQnG,KAAKmG,MAAOlG,EAAUD,KAAKC,OACvC,KAAS0H,EAAI,EAAGA,EAAIxI,EAAoBwI,IACpC5G,EAAO,GAAI7D,GAAK+C,GAChBkG,EAAMyB,KAAK7G,EAEff,MAAK8B,KAAK4B,mBAEd8B,SAAU,SAAUrC,GAAV,GAEGwE,GAMDlC,EAEIC,EATRS,EAAQnG,KAAKmG,MAAOrE,EAAO9B,KAAK8B,IACpC,KAAS6F,EAAI,EAAGA,EAAIxB,EAAMN,OAAQ8B,IAC9BxB,EAAMwB,GAAGE,SAAS1E,EAAKC,MAEQ,UAA/BpD,KAAKwB,QAAQ8C,cACbtE,KAAKC,QAAQ8F,IAAI,SAAU/F,KAAKmG,MAAM,GAAGlG,QAAQqD,UACX,SAA/BtD,KAAKwB,QAAQ8C,gBAChBmB,EAAkBzF,KAAKC,QAAQiC,SAASoB,SACxCtD,KAAKwB,QAAQ+C,eAAgB,IACzBmB,EAAQ1F,KAAKC,QAAQiC,SAAS0D,KAAK,gBAClC5F,KAAKwB,QAAQgD,cAAgBkB,EAAMG,SACpCJ,GAAmBrI,EAAM0I,aAAaJ,GAAO,KAGrD1F,KAAKC,QAAQ8F,IAAI,SAAUN,GAC3BU,EAAM,GAAGlG,QAAQ8F,IAAI,SAAUN,GAC/BU,EAAM,GAAGlG,QAAQ8F,IAAI,SAAUN,GAC/BU,EAAM,GAAGlG,QAAQ8F,IAAI,SAAUN,IAEnC3D,EAAK4B,kBACL1D,KAAK8H,mBACL9H,KAAKoD,MAAQD,EAAKC,OAEtBqB,SAAU,SAAU1D,GAChB,GAA0BgH,GAAtBf,EAAShH,KAAKgH,MAClBA,GAAOgB,iBACPD,EAAWf,EAAOiB,GAAGlH,GAChBgH,IAGL/H,KAAKkI,oBAAoBnH,GACzBf,KAAKe,KAAOA,IAEhB4D,UAAW,SAAUC,EAAWC,EAAQC,EAAUb,GAC9C,GAAwRe,GAApRjF,EAAOC,KAAM8B,EAAO/B,EAAK+B,KAAMsB,EAAQtB,EAAKqB,OAAOC,MAAOK,EAAS3B,EAAK2B,SAAU0E,EAAkB/J,KAAKC,IAAIoF,IAAWL,EAAQ,EAAGY,EAAOa,EAASzH,EAAMM,QAAQD,WAAWyH,YAAc9H,EAAMM,QAAQD,WAAWiH,YAAa0D,EAAerI,EAAKgB,KAAO,EAAIhB,EAAKiH,OAAOzD,QAAmB8E,EAAQ,CACtSzD,KAAcnF,EACI,IAAdM,EAAKgB,OACLsH,MAEGzD,IAAcrF,GAAe6I,EAE7B3E,EAAS,GAAM0E,IAAoBC,EAC1CC,EAAQ,EACD5E,EAAS,GAAK0E,GACH,IAAdpI,EAAKgB,OACLsH,MALJA,EAAQ,EAQZrD,EAAWjF,EAAKgB,KACZsH,IACArD,EAAWqD,EAAQ,EAAIrD,EAAW,EAAIA,EAAW,GAEjDF,GAAYA,GACRO,YAAatF,EAAKgB,KAClBiE,SAAUA,MAEdqD,EAAQ,GAEE,IAAVA,EACAtI,EAAKuI,YAAYtE,EAAMC,GAChBoE,OACPtI,EAAKwI,cAActE,GACF,IAAVoE,GACPtI,EAAKyI,aAAavE,IAG1BqB,WAAY,WACR,GAAIa,GAAQnG,KAAKmG,KACjB,OAA2B,KAAvBnG,KAAK8B,KAAK2B,WAGVzD,KAAK8B,KAAK2B,SAAW,GACrB0C,EAAMyB,KAAK5H,KAAKmG,MAAMsC,SACtBzI,KAAKe,OACLf,KAAK0I,eAAevC,EAAM,GAAInG,KAAKe,KAAO,KAE1CoF,EAAMwC,QAAQ3I,KAAKmG,MAAMyC,OACzB5I,KAAKe,OACLf,KAAK0I,eAAevC,EAAM,GAAInG,KAAKe,KAAO,IAE9Cf,KAAK8H,mBACL9H,KAAK6I,iBACE,IAEXtD,gBAAiB,WACb,GAAI9B,GAASzD,KAAK8B,KAAK2B,SAAUqF,EAAqC,EAAzB9I,KAAK8B,KAAKqB,OAAOC,MAAY,CAC1E,OAAI/E,GAAIoF,GAAUqF,GACP9I,KAAKsF,cAIpBuD,cAAe,WACX7I,KAAK8B,KAAK+B,OAAO,IAErB2E,aAAc,SAAUvE,GACpBjE,KAAK8B,KAAKiC,cAAc/D,KAAKoD,MAAOhG,EAAMM,QAAQD,WAAWiH,YAAaT,IAE9EsE,cAAe,SAAUtE,GACrBjE,KAAK8B,KAAKiC,aAAa/D,KAAKoD,MAAOhG,EAAMM,QAAQD,WAAWiH,YAAaT,IAE7EqE,YAAa,SAAUtE,EAAMC,GACzBjE,KAAK8B,KAAKiC,aAAa,EAAGC,EAAMC,IAEpC6C,YAAa,WACT9G,KAAKe,KAAOf,KAAKwB,QAAQT,MAAQ,EACjCf,KAAKkI,oBAAoBlI,KAAKe,MAC9Bf,KAAK8H,mBACL9H,KAAKsC,QAAQ,UAEjByG,UAAW,WACP/I,KAAKY,UAAYtC,EAAK0B,KAAKuG,WAAWhD,QAAUvD,KAAKwB,QAAQuF,cACzD/G,KAAK0G,sBACL1G,KAAKkI,oBAAoBlI,KAAKe,MAC9Bf,KAAK0G,qBAAsB,GAE/B1G,KAAKsC,QAAQ,WAEjB0G,SAAU,WACNhJ,KAAKY,UAAYtC,EAAK0B,KAAKuG,WAAWhD,QAAUvD,KAAKwB,QAAQuF,cAC7D/G,KAAK8G,eAETmC,cAAe,WACXjJ,KAAK0G,qBAAsB,GAE/BoB,iBAAkB,WACd,GAAI3B,GAAQnG,KAAKmG,KACjBA,GAAM,GAAG+C,SAAS9J,GAClB+G,EAAM,GAAG+C,SAAS7J,GAClB8G,EAAM,GAAG+C,SAAS5J,IAEtB4I,oBAAqB,SAAUzE,GAC3B,GAAI0C,GAAQnG,KAAKmG,MAAOd,EAAc5B,GAAU,CAChDzD,MAAK0I,eAAevC,EAAM,GAAId,EAAc,GAC5CrF,KAAK0I,eAAevC,EAAM,GAAId,GAC9BrF,KAAK0I,eAAevC,EAAM,GAAId,EAAc,IAEhDqD,eAAgB,SAAU3H,EAAMoI,GAC5B,GAAInC,GAAShH,KAAKgH,OAAQO,EAAWvH,KAAKuH,SAAUC,EAAgBxH,KAAKwH,cAAe4B,EAAO,IAC3FD,IAAS,IACTC,EAAOpC,EAAOiB,GAAGkB,GACb3M,EAAE6M,QAAQD,KAAUA,EAAKvD,SACzBuD,EAAO,OAGfpJ,KAAKsC,QAAQpD,GAAWoK,KAAMvI,EAAKd,UAE/Bc,EAAKwI,QADI,OAATH,EACa7B,EAAS6B,GAET5B,OAEjBpK,EAAME,OAAOuC,KAAKkB,EAAKd,SACvBD,KAAKsC,QAAQrD,GACTqK,KAAMvI,EAAKd,QACXjC,KAAMoL,EACNI,GAAIpM,EAAME,OAAOC,QAI7BH,EAAME,OAAOC,GAAGN,yBAA2BA,EACvCC,EAAOE,EAAMuC,MAAMC,QACnBC,KAAM,SAAUoC,GACZjC,KAAKC,QAAUzD,EAAE,eAAkBE,EAAUqC,GAAsB,YACnEiB,KAAKoD,MAAQnB,EAAUmB,QACvBpD,KAAKC,QAAQmD,MAAMpD,KAAKoD,OACxBnB,EAAU/B,OAAOF,KAAKC,UAE1BsJ,QAAS,SAAUE,GACfzJ,KAAKC,QAAQY,KAAK4I,IAEtBP,SAAU,SAAUA,GAChBlJ,KAAKC,QAAQ8F,IAAI,YAAa,eAAiB/F,KAAKoD,MAAQ8F,EAAW,cAE3ErB,SAAU,SAAUzE,GAChBpD,KAAKoD,MAAQA,EACbpD,KAAKC,QAAQmD,MAAMA,MAG3BhG,EAAME,OAAOC,GAAGmM,YAAcxM,EAC1BC,EAAaU,EAAO+B,QACpBC,KAAM,SAAUI,EAASuB,GAAnB,GA2BEmI,GACAJ,EAgCAK,EA3DA7J,EAAOC,IACXnC,GAAOkE,GAAGlC,KAAKmC,KAAKjC,EAAME,EAASuB,GACnCA,EAAUzB,EAAKyB,QACfvB,EAAUF,EAAKE,QACf7C,EAAMyM,gBAAgB5J,EAAQ,IAC9BA,EAAQ6J,UAAU,UAAU9I,SAAS,YAActE,EAAU,eACzDsD,KAAKwB,QAAQ+C,cACbvE,KAAK0F,MAAQ,GAAIhG,GAAMM,MACnBA,KAAKwB,QAAQgD,cACbvE,EAAQe,SAAStE,EAAU,wBAGnCqD,EAAKgK,MAAQ9J,EAAQM,WAAWyJ,QAChCjK,EAAKgB,KAAO,EACZhB,EAAKgK,MAAMhE,IAAI,SAAUvE,EAAQ8C,eACjCvE,EAAK+B,KAAO,GAAI/E,GAAYgD,EAAKgK,OAC7B7G,SAAUlD,KAAKwB,QAAQ0B,SACvB+G,cAAezM,EAAMwC,KAAM,kBAC3BkK,UAAW1M,EAAMwC,KAAM,cACvBmK,QAAS3M,EAAMwC,KAAM,YACrBoK,OAAQ5M,EAAMwC,KAAMnB,KAExBkB,EAAKM,KAAK,SAAU,WAChBN,EAAK+B,KAAK8B,YAEd7D,EAAKgB,KAAOS,EAAQT,KAChB4I,EAAyC,IAAjC3J,KAAK+J,MAAMxJ,WAAWsF,OAC9B0D,EAAUI,EAAQ,GAAI1M,GAAyB8C,EAAKgK,MAAOhK,EAAK+B,KAAMN,GAAW,GAAIxE,GAAkB+C,EAAKgK,MAAOhK,EAAK+B,KAAMN,GAClI+H,EAAQxI,KAAOhB,EAAKgB,KACpBwI,EAAQlJ,KAAK,QAAS,WAClBL,KAAK0G,qBAAsB,EAC3B3G,EAAKsK,mBACLtK,EAAKuC,QAAQzD,GACT+B,UAAW2I,EAAQ3I,UACnBG,KAAMwI,EAAQxI,SAGtBwI,EAAQlJ,KAAK,SAAU,WACnBN,EAAKuC,QAAQzD,GACT+B,UAAW2I,EAAQ3I,UACnBG,KAAMwI,EAAQxI,SAGtBwI,EAAQlJ,KAAKpB,EAAa,SAAUwB,GAChCV,EAAKuC,QAAQrD,EAAawB,GAC1BV,EAAKuK,QAAQ,UAAW,WACpB,OACIC,SAAU9J,EAAE6I,KACZtL,OAAS+J,SAAUtH,EAAEzC,YAIjCuL,EAAQlJ,KAAKnB,EAAS,SAAUuB,GAC5BV,EAAKuK,QAAQ,UAAW,WACpB,OAASC,SAAU9J,EAAE6I,UAG7BvJ,EAAKyK,SAAWjB,EAChBxJ,EAAKuG,cAAc9E,EAAQ+E,YACvBqD,EAAkB7J,EAAKkC,YACvB2H,EAAgBa,YAChB1K,EAAK2K,WACL3K,EAAK4K,YAELf,EAAgBvJ,KAAK,OAAQ7C,EAAMwC,KAAM,aAAaK,KAAK,OAAQ7C,EAAMwC,KAAM,cAGvFwB,SACI7E,KAAM,aACNoE,KAAM,EACNmC,SAAU,IACV0H,kBAAmB,GACnBtG,cAAe,OACfD,SAAU,EACV0C,aAAc,EACd8D,wBAAyB,IACzBtG,aAAa,EACbC,cAAc,EACdsG,UAAU,EACVvD,SAAU,GACVC,cAAe,IAEnBuD,QACInM,EACAD,EACAE,GAEJsC,QAAS,WACLtD,EAAOkE,GAAGZ,QAAQa,KAAKhC,MACvB5C,EAAM+D,QAAQnB,KAAKC,UAEvByK,SAAU,WACF1K,KAAKwB,QAAQsJ,UACb9K,KAAKwK,SAAS/F,SAASzE,KAAKwK,SAASzJ,MAAM,IAGnD4J,SAAU,WACN3K,KAAK8B,KAAK8B,WAEdA,QAAS,WACL,GAAI2F,GAAUvJ,KAAKwK,QACnBjB,GAAQ/D,SAASxF,KAAK8B,KAAKqB,QAC3BnD,KAAKe,KAAOwI,EAAQxI,KACpBf,KAAKsC,QAAQzD,GACT+B,UAAW2I,EAAQ3I,UACnBG,KAAMwI,EAAQxI,QAGtBwI,QAAS,SAAU1I,GACfb,KAAKC,QAAQM,WAAWyJ,QAAQnJ,KAAKA,GACrCb,KAAKwK,SAASpG,YACdpE,KAAK8B,KAAK8B,WAEdoH,MAAO,SAAU1B,GACb,GAAI/C,GAAavG,KAAKuG,UACtB,OAAI+C,IACAtJ,KAAKyE,SAAS8B,EAAW0E,QAAQ3B,IAAO,GAAxCtJ,GAEOuG,EAAW0B,GAAGjI,KAAKe,OAGlC0D,SAAU,SAAU1D,EAAMkD,GACtBjE,KAAKwK,SAAS/F,SAAS1D,EAAMkD,GAC7BjE,KAAKqK,oBAETa,KAAM,WACF,GAAInL,GAAOC,KAAMmL,EAAWpL,EAAKgB,KAAO,CACpChB,GAAKyK,mBAAoBvN,GACzB8C,EAAKyK,SAAS7F,UAAUlF,EAAahD,EAAW,SAAU2O,GACtD,MAAOrL,GAAKuC,QAAQ1D,EAAUwM,KAE3BD,MACPpL,EAAK0E,SAAS0G,IAGtBE,KAAM,WACF,GAAItL,GAAOC,KAAMgF,EAAWjF,EAAKgB,KAAO,CACpChB,GAAKyK,mBAAoBvN,GACzB8C,EAAKyK,SAAS7F,UAAUpF,EAAY9C,EAAW,SAAU2O,GACrD,MAAOrL,GAAKuC,QAAQ1D,EAAUwM,KAE3BpG,EAAWjF,EAAKyK,SAAS5J,WAChCb,EAAK0E,SAASO,IAGtBsB,cAAe,SAAUC,GACrB,GAAMvG,KAAKwK,mBAAoBvN,GAA/B,CAGA,GAAIqO,IAAmB/E,CACvBvG,MAAKuG,WAAaxI,EAAWyI,OAAOD,GACpCvG,KAAKwK,SAASlE,cAActG,KAAKuG,YAC7BvG,KAAKwB,QAAQsJ,WAAaQ,GAC1BtL,KAAKuG,WAAWgF,UAGxBjL,MAAO,WACH,MAAON,MAAKC,QAAQ2F,KAAK,OAAS7G,IAEtCsL,iBAAkB,WACd,GAAgErM,GAAMiC,EAAlEkG,EAAQnG,KAAKwK,SAASrE,MAAOa,EAAShH,KAAKwK,SAASxD,MACxDhH,MAAKe,KAAOf,KAAKwK,SAASzJ,KAC1B/C,EAAOgJ,EAASA,EAAOiB,GAAGjI,KAAKe,MAAQtE,EACjCuB,YAAgBwN,SAClBxN,GAAQA,IAEZiC,EAAUkG,EAAQA,EAAM,GAAGlG,QAAUxD,EACrCuD,KAAKsC,QAAQ3D,GACToC,KAAMf,KAAKe,KACXd,QAASA,EACTjC,KAAMA,KAGdyN,WAAY,WACJzL,KAAKwK,SAASjF,mBACdvF,KAAKqK,oBAGbqB,SAAU,SAAUjL,GAChB,GAAIV,GAAOC,KAAM2C,EAAWlC,EAAEiC,EAAEC,SAAUiI,EAAoB5K,KAAKwB,QAAQoJ,kBAAmBhG,EAAYpF,EAAOqF,EAASxG,EAAIsE,GAAY3C,KAAKwB,QAAQqJ,uBACnJlI,GAAWiI,EACXhG,EAAYnF,EACLkD,GAAYiI,IACnBhG,EAAYrF,GAEhBS,KAAKwK,SAAS7F,UAAUC,EAAWC,EAAQ,SAAUuG,GACjD,MAAOrL,GAAKuC,QAAQ1D,EAAUwM,MAGtCO,eAAgB,WACR3L,KAAKwK,SAASlF,cACdtF,KAAKqK,sBAIjB9M,EAAGqO,OAAOzO,IACZE,OAAOD,MAAMyO,QACRxO,OAAOD,OACE,kBAAVb,SAAwBA,OAAOuP,IAAMvP,OAAS,SAAUwP,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.scrollview.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.scrollview', [\n        'kendo.fx',\n        'kendo.data',\n        'kendo.draganddrop'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.scrollview',\n        name: 'ScrollView',\n        category: 'mobile',\n        description: 'The Kendo Mobile ScrollView widget is used to scroll content wider than the device screen.',\n        depends: [\n            'fx',\n            'data',\n            'draganddrop'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, mobile = kendo.mobile, ui = mobile.ui, proxy = $.proxy, Transition = kendo.effects.Transition, Pane = kendo.ui.Pane, PaneDimensions = kendo.ui.PaneDimensions, Widget = ui.DataBoundWidget, DataSource = kendo.data.DataSource, Buffer = kendo.data.Buffer, BatchBuffer = kendo.data.BatchBuffer, math = Math, abs = math.abs, ceil = math.ceil, round = math.round, max = math.max, min = math.min, floor = math.floor, CHANGE = 'change', CHANGING = 'changing', REFRESH = 'refresh', CURRENT_PAGE_CLASS = 'current-page', VIRTUAL_PAGE_CLASS = 'virtual-page', FUNCTION = 'function', ITEM_CHANGE = 'itemChange', CLEANUP = 'cleanup', VIRTUAL_PAGE_COUNT = 3, LEFT_PAGE = -1, CETER_PAGE = 0, RIGHT_PAGE = 1, LEFT_SWIPE = -1, NUDGE = 0, RIGHT_SWIPE = 1;\n        function className(name) {\n            return 'k-' + name + ' km-' + name;\n        }\n        var Pager = kendo.Class.extend({\n            init: function (scrollView) {\n                var that = this, element = $('<ol class=\\'' + className('pages') + '\\'/>');\n                scrollView.element.append(element);\n                this._changeProxy = proxy(that, '_change');\n                this._refreshProxy = proxy(that, '_refresh');\n                scrollView.bind(CHANGE, this._changeProxy);\n                scrollView.bind(REFRESH, this._refreshProxy);\n                $.extend(that, {\n                    element: element,\n                    scrollView: scrollView\n                });\n            },\n            items: function () {\n                return this.element.children();\n            },\n            _refresh: function (e) {\n                var pageHTML = '';\n                for (var idx = 0; idx < e.pageCount; idx++) {\n                    pageHTML += '<li/>';\n                }\n                this.element.html(pageHTML);\n                this.items().eq(e.page).addClass(className(CURRENT_PAGE_CLASS));\n            },\n            _change: function (e) {\n                this.items().removeClass(className(CURRENT_PAGE_CLASS)).eq(e.page).addClass(className(CURRENT_PAGE_CLASS));\n            },\n            destroy: function () {\n                this.scrollView.unbind(CHANGE, this._changeProxy);\n                this.scrollView.unbind(REFRESH, this._refreshProxy);\n                this.element.remove();\n            }\n        });\n        kendo.mobile.ui.ScrollViewPager = Pager;\n        var TRANSITION_END = 'transitionEnd', DRAG_START = 'dragStart', DRAG_END = 'dragEnd';\n        var ElasticPane = kendo.Observable.extend({\n            init: function (element, options) {\n                var that = this;\n                kendo.Observable.fn.init.call(this);\n                this.element = element;\n                this.container = element.parent();\n                var movable, transition, userEvents, dimensions, dimension, pane;\n                movable = new kendo.ui.Movable(that.element);\n                transition = new Transition({\n                    axis: 'x',\n                    movable: movable,\n                    onEnd: function () {\n                        that.trigger(TRANSITION_END);\n                    }\n                });\n                userEvents = new kendo.UserEvents(element, {\n                    fastTap: true,\n                    start: function (e) {\n                        if (abs(e.x.velocity) * 2 >= abs(e.y.velocity)) {\n                            userEvents.capture();\n                        } else {\n                            userEvents.cancel();\n                        }\n                        that.trigger(DRAG_START, e);\n                        transition.cancel();\n                    },\n                    allowSelection: true,\n                    end: function (e) {\n                        that.trigger(DRAG_END, e);\n                    }\n                });\n                dimensions = new PaneDimensions({\n                    element: that.element,\n                    container: that.container\n                });\n                dimension = dimensions.x;\n                dimension.bind(CHANGE, function () {\n                    that.trigger(CHANGE);\n                });\n                pane = new Pane({\n                    dimensions: dimensions,\n                    userEvents: userEvents,\n                    movable: movable,\n                    elastic: true\n                });\n                $.extend(that, {\n                    duration: options && options.duration || 1,\n                    movable: movable,\n                    transition: transition,\n                    userEvents: userEvents,\n                    dimensions: dimensions,\n                    dimension: dimension,\n                    pane: pane\n                });\n                this.bind([\n                    TRANSITION_END,\n                    DRAG_START,\n                    DRAG_END,\n                    CHANGE\n                ], options);\n            },\n            size: function () {\n                return {\n                    width: this.dimensions.x.getSize(),\n                    height: this.dimensions.y.getSize()\n                };\n            },\n            total: function () {\n                return this.dimension.getTotal();\n            },\n            offset: function () {\n                return -this.movable.x;\n            },\n            updateDimension: function () {\n                this.dimension.update(true);\n            },\n            refresh: function () {\n                this.dimensions.refresh();\n            },\n            moveTo: function (offset) {\n                this.movable.moveAxis('x', -offset);\n            },\n            transitionTo: function (offset, ease, instant) {\n                if (instant) {\n                    this.moveTo(-offset);\n                } else {\n                    this.transition.moveTo({\n                        location: offset,\n                        duration: this.duration,\n                        ease: ease\n                    });\n                }\n            }\n        });\n        kendo.mobile.ui.ScrollViewElasticPane = ElasticPane;\n        var ScrollViewContent = kendo.Observable.extend({\n            init: function (element, pane, options) {\n                var that = this;\n                kendo.Observable.fn.init.call(this);\n                that.element = element;\n                that.pane = pane;\n                that._getPages();\n                this.page = 0;\n                this.pageSize = options.pageSize || 1;\n                this.contentHeight = options.contentHeight;\n                this.enablePager = options.enablePager;\n                this.pagerOverlay = options.pagerOverlay;\n            },\n            scrollTo: function (page, instant) {\n                this.page = page;\n                this.pane.transitionTo(-page * this.pane.size().width, Transition.easeOutExpo, instant);\n            },\n            paneMoved: function (swipeType, bounce, callback, instant) {\n                var that = this, pane = that.pane, width = pane.size().width * that.pageSize, approx = round, ease = bounce ? Transition.easeOutBack : Transition.easeOutExpo, snap, nextPage;\n                if (swipeType === LEFT_SWIPE) {\n                    approx = ceil;\n                } else if (swipeType === RIGHT_SWIPE) {\n                    approx = floor;\n                }\n                nextPage = approx(pane.offset() / width);\n                snap = max(that.minSnap, min(-nextPage * width, that.maxSnap));\n                if (nextPage != that.page) {\n                    if (callback && callback({\n                            currentPage: that.page,\n                            nextPage: nextPage\n                        })) {\n                        snap = -that.page * pane.size().width;\n                    }\n                }\n                pane.transitionTo(snap, ease, instant);\n            },\n            updatePage: function () {\n                var pane = this.pane, page = round(pane.offset() / pane.size().width);\n                if (page != this.page) {\n                    this.page = page;\n                    return true;\n                }\n                return false;\n            },\n            forcePageUpdate: function () {\n                return this.updatePage();\n            },\n            resizeTo: function (size) {\n                var pane = this.pane, width = size.width;\n                this.pageElements.width(width);\n                if (this.contentHeight === '100%') {\n                    var containerHeight = this.element.parent().height();\n                    if (this.enablePager === true) {\n                        var pager = this.element.parent().find('ol.km-pages');\n                        if (!this.pagerOverlay && pager.length) {\n                            containerHeight -= kendo._outerHeight(pager, true);\n                        }\n                    }\n                    this.element.css('height', containerHeight);\n                    this.pageElements.css('height', containerHeight);\n                }\n                pane.updateDimension();\n                if (!this._paged) {\n                    this.page = floor(pane.offset() / width);\n                }\n                this.scrollTo(this.page, true);\n                this.pageCount = ceil(pane.total() / width);\n                this.minSnap = -(this.pageCount - 1) * width;\n                this.maxSnap = 0;\n            },\n            _getPages: function () {\n                this.pageElements = this.element.find(kendo.roleSelector('page'));\n                this._paged = this.pageElements.length > 0;\n            }\n        });\n        kendo.mobile.ui.ScrollViewContent = ScrollViewContent;\n        var VirtualScrollViewContent = kendo.Observable.extend({\n            init: function (element, pane, options) {\n                var that = this;\n                kendo.Observable.fn.init.call(this);\n                that.element = element;\n                that.pane = pane;\n                that.options = options;\n                that._templates();\n                that.page = options.page || 0;\n                that.pages = [];\n                that._initPages();\n                that.resizeTo(that.pane.size());\n                that.pane.dimension.forceEnabled();\n            },\n            setDataSource: function (dataSource) {\n                this.dataSource = DataSource.create(dataSource);\n                this._buffer();\n                this._pendingPageRefresh = false;\n                this._pendingWidgetRefresh = false;\n            },\n            _viewShow: function () {\n                var that = this;\n                if (that._pendingWidgetRefresh) {\n                    setTimeout(function () {\n                        that._resetPages();\n                    }, 0);\n                    that._pendingWidgetRefresh = false;\n                }\n            },\n            _buffer: function () {\n                var itemsPerPage = this.options.itemsPerPage;\n                if (this.buffer) {\n                    this.buffer.destroy();\n                }\n                if (itemsPerPage > 1) {\n                    this.buffer = new BatchBuffer(this.dataSource, itemsPerPage);\n                } else {\n                    this.buffer = new Buffer(this.dataSource, itemsPerPage * 3);\n                }\n                this._resizeProxy = proxy(this, '_onResize');\n                this._resetProxy = proxy(this, '_onReset');\n                this._endReachedProxy = proxy(this, '_onEndReached');\n                this.buffer.bind({\n                    'resize': this._resizeProxy,\n                    'reset': this._resetProxy,\n                    'endreached': this._endReachedProxy\n                });\n            },\n            _templates: function () {\n                var template = this.options.template, emptyTemplate = this.options.emptyTemplate, templateProxy = {}, emptyTemplateProxy = {};\n                if (typeof template === FUNCTION) {\n                    templateProxy.template = template;\n                    template = '#=this.template(data)#';\n                }\n                this.template = proxy(kendo.template(template), templateProxy);\n                if (typeof emptyTemplate === FUNCTION) {\n                    emptyTemplateProxy.emptyTemplate = emptyTemplate;\n                    emptyTemplate = '#=this.emptyTemplate(data)#';\n                }\n                this.emptyTemplate = proxy(kendo.template(emptyTemplate), emptyTemplateProxy);\n            },\n            _initPages: function () {\n                var pages = this.pages, element = this.element, page;\n                for (var i = 0; i < VIRTUAL_PAGE_COUNT; i++) {\n                    page = new Page(element);\n                    pages.push(page);\n                }\n                this.pane.updateDimension();\n            },\n            resizeTo: function (size) {\n                var pages = this.pages, pane = this.pane;\n                for (var i = 0; i < pages.length; i++) {\n                    pages[i].setWidth(size.width);\n                }\n                if (this.options.contentHeight === 'auto') {\n                    this.element.css('height', this.pages[1].element.height());\n                } else if (this.options.contentHeight === '100%') {\n                    var containerHeight = this.element.parent().height();\n                    if (this.options.enablePager === true) {\n                        var pager = this.element.parent().find('ol.km-pages');\n                        if (!this.options.pagerOverlay && pager.length) {\n                            containerHeight -= kendo._outerHeight(pager, true);\n                        }\n                    }\n                    this.element.css('height', containerHeight);\n                    pages[0].element.css('height', containerHeight);\n                    pages[1].element.css('height', containerHeight);\n                    pages[2].element.css('height', containerHeight);\n                }\n                pane.updateDimension();\n                this._repositionPages();\n                this.width = size.width;\n            },\n            scrollTo: function (page) {\n                var buffer = this.buffer, dataItem;\n                buffer.syncDataSource();\n                dataItem = buffer.at(page);\n                if (!dataItem) {\n                    return;\n                }\n                this._updatePagesContent(page);\n                this.page = page;\n            },\n            paneMoved: function (swipeType, bounce, callback, instant) {\n                var that = this, pane = that.pane, width = pane.size().width, offset = pane.offset(), thresholdPassed = Math.abs(offset) >= width / 3, ease = bounce ? kendo.effects.Transition.easeOutBack : kendo.effects.Transition.easeOutExpo, isEndReached = that.page + 2 > that.buffer.total(), nextPage, delta = 0;\n                if (swipeType === RIGHT_SWIPE) {\n                    if (that.page !== 0) {\n                        delta = -1;\n                    }\n                } else if (swipeType === LEFT_SWIPE && !isEndReached) {\n                    delta = 1;\n                } else if (offset > 0 && (thresholdPassed && !isEndReached)) {\n                    delta = 1;\n                } else if (offset < 0 && thresholdPassed) {\n                    if (that.page !== 0) {\n                        delta = -1;\n                    }\n                }\n                nextPage = that.page;\n                if (delta) {\n                    nextPage = delta > 0 ? nextPage + 1 : nextPage - 1;\n                }\n                if (callback && callback({\n                        currentPage: that.page,\n                        nextPage: nextPage\n                    })) {\n                    delta = 0;\n                }\n                if (delta === 0) {\n                    that._cancelMove(ease, instant);\n                } else if (delta === -1) {\n                    that._moveBackward(instant);\n                } else if (delta === 1) {\n                    that._moveForward(instant);\n                }\n            },\n            updatePage: function () {\n                var pages = this.pages;\n                if (this.pane.offset() === 0) {\n                    return false;\n                }\n                if (this.pane.offset() > 0) {\n                    pages.push(this.pages.shift());\n                    this.page++;\n                    this.setPageContent(pages[2], this.page + 1);\n                } else {\n                    pages.unshift(this.pages.pop());\n                    this.page--;\n                    this.setPageContent(pages[0], this.page - 1);\n                }\n                this._repositionPages();\n                this._resetMovable();\n                return true;\n            },\n            forcePageUpdate: function () {\n                var offset = this.pane.offset(), threshold = this.pane.size().width * 3 / 4;\n                if (abs(offset) > threshold) {\n                    return this.updatePage();\n                }\n                return false;\n            },\n            _resetMovable: function () {\n                this.pane.moveTo(0);\n            },\n            _moveForward: function (instant) {\n                this.pane.transitionTo(-this.width, kendo.effects.Transition.easeOutExpo, instant);\n            },\n            _moveBackward: function (instant) {\n                this.pane.transitionTo(this.width, kendo.effects.Transition.easeOutExpo, instant);\n            },\n            _cancelMove: function (ease, instant) {\n                this.pane.transitionTo(0, ease, instant);\n            },\n            _resetPages: function () {\n                this.page = this.options.page || 0;\n                this._updatePagesContent(this.page);\n                this._repositionPages();\n                this.trigger('reset');\n            },\n            _onResize: function () {\n                this.pageCount = ceil(this.dataSource.total() / this.options.itemsPerPage);\n                if (this._pendingPageRefresh) {\n                    this._updatePagesContent(this.page);\n                    this._pendingPageRefresh = false;\n                }\n                this.trigger('resize');\n            },\n            _onReset: function () {\n                this.pageCount = ceil(this.dataSource.total() / this.options.itemsPerPage);\n                this._resetPages();\n            },\n            _onEndReached: function () {\n                this._pendingPageRefresh = true;\n            },\n            _repositionPages: function () {\n                var pages = this.pages;\n                pages[0].position(LEFT_PAGE);\n                pages[1].position(CETER_PAGE);\n                pages[2].position(RIGHT_PAGE);\n            },\n            _updatePagesContent: function (offset) {\n                var pages = this.pages, currentPage = offset || 0;\n                this.setPageContent(pages[0], currentPage - 1);\n                this.setPageContent(pages[1], currentPage);\n                this.setPageContent(pages[2], currentPage + 1);\n            },\n            setPageContent: function (page, index) {\n                var buffer = this.buffer, template = this.template, emptyTemplate = this.emptyTemplate, view = null;\n                if (index >= 0) {\n                    view = buffer.at(index);\n                    if ($.isArray(view) && !view.length) {\n                        view = null;\n                    }\n                }\n                this.trigger(CLEANUP, { item: page.element });\n                if (view !== null) {\n                    page.content(template(view));\n                } else {\n                    page.content(emptyTemplate({}));\n                }\n                kendo.mobile.init(page.element);\n                this.trigger(ITEM_CHANGE, {\n                    item: page.element,\n                    data: view,\n                    ns: kendo.mobile.ui\n                });\n            }\n        });\n        kendo.mobile.ui.VirtualScrollViewContent = VirtualScrollViewContent;\n        var Page = kendo.Class.extend({\n            init: function (container) {\n                this.element = $('<div class=\\'' + className(VIRTUAL_PAGE_CLASS) + '\\'></div>');\n                this.width = container.width();\n                this.element.width(this.width);\n                container.append(this.element);\n            },\n            content: function (theContent) {\n                this.element.html(theContent);\n            },\n            position: function (position) {\n                this.element.css('transform', 'translate3d(' + this.width * position + 'px, 0, 0)');\n            },\n            setWidth: function (width) {\n                this.width = width;\n                this.element.width(width);\n            }\n        });\n        kendo.mobile.ui.VirtualPage = Page;\n        var ScrollView = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.element;\n                kendo.stripWhitespace(element[0]);\n                element.wrapInner('<div/>').addClass('k-widget ' + className('scrollview'));\n                if (this.options.enablePager) {\n                    this.pager = new Pager(this);\n                    if (this.options.pagerOverlay) {\n                        element.addClass(className('scrollview-overlay'));\n                    }\n                }\n                that.inner = element.children().first();\n                that.page = 0;\n                that.inner.css('height', options.contentHeight);\n                that.pane = new ElasticPane(that.inner, {\n                    duration: this.options.duration,\n                    transitionEnd: proxy(this, '_transitionEnd'),\n                    dragStart: proxy(this, '_dragStart'),\n                    dragEnd: proxy(this, '_dragEnd'),\n                    change: proxy(this, REFRESH)\n                });\n                that.bind('resize', function () {\n                    that.pane.refresh();\n                });\n                that.page = options.page;\n                var empty = this.inner.children().length === 0;\n                var content = empty ? new VirtualScrollViewContent(that.inner, that.pane, options) : new ScrollViewContent(that.inner, that.pane, options);\n                content.page = that.page;\n                content.bind('reset', function () {\n                    this._pendingPageRefresh = false;\n                    that._syncWithContent();\n                    that.trigger(REFRESH, {\n                        pageCount: content.pageCount,\n                        page: content.page\n                    });\n                });\n                content.bind('resize', function () {\n                    that.trigger(REFRESH, {\n                        pageCount: content.pageCount,\n                        page: content.page\n                    });\n                });\n                content.bind(ITEM_CHANGE, function (e) {\n                    that.trigger(ITEM_CHANGE, e);\n                    that.angular('compile', function () {\n                        return {\n                            elements: e.item,\n                            data: [{ dataItem: e.data }]\n                        };\n                    });\n                });\n                content.bind(CLEANUP, function (e) {\n                    that.angular('cleanup', function () {\n                        return { elements: e.item };\n                    });\n                });\n                that._content = content;\n                that.setDataSource(options.dataSource);\n                var mobileContainer = that.container();\n                if (mobileContainer.nullObject) {\n                    that.viewInit();\n                    that.viewShow();\n                } else {\n                    mobileContainer.bind('show', proxy(this, 'viewShow')).bind('init', proxy(this, 'viewInit'));\n                }\n            },\n            options: {\n                name: 'ScrollView',\n                page: 0,\n                duration: 400,\n                velocityThreshold: 0.8,\n                contentHeight: 'auto',\n                pageSize: 1,\n                itemsPerPage: 1,\n                bounceVelocityThreshold: 1.6,\n                enablePager: true,\n                pagerOverlay: false,\n                autoBind: true,\n                template: '',\n                emptyTemplate: ''\n            },\n            events: [\n                CHANGING,\n                CHANGE,\n                REFRESH\n            ],\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                kendo.destroy(this.element);\n            },\n            viewInit: function () {\n                if (this.options.autoBind) {\n                    this._content.scrollTo(this._content.page, true);\n                }\n            },\n            viewShow: function () {\n                this.pane.refresh();\n            },\n            refresh: function () {\n                var content = this._content;\n                content.resizeTo(this.pane.size());\n                this.page = content.page;\n                this.trigger(REFRESH, {\n                    pageCount: content.pageCount,\n                    page: content.page\n                });\n            },\n            content: function (html) {\n                this.element.children().first().html(html);\n                this._content._getPages();\n                this.pane.refresh();\n            },\n            value: function (item) {\n                var dataSource = this.dataSource;\n                if (item) {\n                    this.scrollTo(dataSource.indexOf(item), true);\n                } else {\n                    return dataSource.at(this.page);\n                }\n            },\n            scrollTo: function (page, instant) {\n                this._content.scrollTo(page, instant);\n                this._syncWithContent();\n            },\n            prev: function () {\n                var that = this, prevPage = that.page - 1;\n                if (that._content instanceof VirtualScrollViewContent) {\n                    that._content.paneMoved(RIGHT_SWIPE, undefined, function (eventData) {\n                        return that.trigger(CHANGING, eventData);\n                    });\n                } else if (prevPage > -1) {\n                    that.scrollTo(prevPage);\n                }\n            },\n            next: function () {\n                var that = this, nextPage = that.page + 1;\n                if (that._content instanceof VirtualScrollViewContent) {\n                    that._content.paneMoved(LEFT_SWIPE, undefined, function (eventData) {\n                        return that.trigger(CHANGING, eventData);\n                    });\n                } else if (nextPage < that._content.pageCount) {\n                    that.scrollTo(nextPage);\n                }\n            },\n            setDataSource: function (dataSource) {\n                if (!(this._content instanceof VirtualScrollViewContent)) {\n                    return;\n                }\n                var emptyDataSource = !dataSource;\n                this.dataSource = DataSource.create(dataSource);\n                this._content.setDataSource(this.dataSource);\n                if (this.options.autoBind && !emptyDataSource) {\n                    this.dataSource.fetch();\n                }\n            },\n            items: function () {\n                return this.element.find('.km-' + VIRTUAL_PAGE_CLASS);\n            },\n            _syncWithContent: function () {\n                var pages = this._content.pages, buffer = this._content.buffer, data, element;\n                this.page = this._content.page;\n                data = buffer ? buffer.at(this.page) : undefined;\n                if (!(data instanceof Array)) {\n                    data = [data];\n                }\n                element = pages ? pages[1].element : undefined;\n                this.trigger(CHANGE, {\n                    page: this.page,\n                    element: element,\n                    data: data\n                });\n            },\n            _dragStart: function () {\n                if (this._content.forcePageUpdate()) {\n                    this._syncWithContent();\n                }\n            },\n            _dragEnd: function (e) {\n                var that = this, velocity = e.x.velocity, velocityThreshold = this.options.velocityThreshold, swipeType = NUDGE, bounce = abs(velocity) > this.options.bounceVelocityThreshold;\n                if (velocity > velocityThreshold) {\n                    swipeType = RIGHT_SWIPE;\n                } else if (velocity < -velocityThreshold) {\n                    swipeType = LEFT_SWIPE;\n                }\n                this._content.paneMoved(swipeType, bounce, function (eventData) {\n                    return that.trigger(CHANGING, eventData);\n                });\n            },\n            _transitionEnd: function () {\n                if (this._content.updatePage()) {\n                    this._syncWithContent();\n                }\n            }\n        });\n        ui.plugin(ScrollView);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}