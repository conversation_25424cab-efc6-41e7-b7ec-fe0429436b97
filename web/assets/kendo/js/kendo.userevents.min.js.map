{"version": 3, "sources": ["kendo.userevents.js"], "names": ["f", "define", "$", "undefined", "touchDelta", "touch1", "touch2", "x1", "x", "location", "y1", "y", "x2", "y2", "dx", "dy", "center", "distance", "Math", "sqrt", "getTouches", "e", "length", "changedTouches", "touch", "touches", "originalEvent", "currentTarget", "idx", "api", "push", "id", "event", "target", "type", "match", "identifier", "support", "pointers", "msPointers", "pointerId", "withEachUpEvent", "callback", "downEvents", "kendo", "eventMap", "up", "split", "window", "Class", "Observable", "now", "extend", "OS", "mobileOS", "invalidZeroEvents", "android", "DEFAULT_MIN_HOLD", "CLICK_DELAY", "DEFAULT_THRESHOLD", "browser", "msie", "PRESS", "HOLD", "SELECT", "START", "MOVE", "END", "CANCEL", "TAP", "DOUBLETAP", "RELEASE", "GESTURESTART", "GESTURECHANGE", "GESTUREEND", "GESTURETAP", "THRESHOLD", "mouse", "pointer", "ENABLE_GLOBAL_SURFACE", "mouseAndTouchPresent", "TouchAxis", "init", "axis", "that", "this", "_updateLocationData", "startLocation", "velocity", "delta", "timeStamp", "move", "offset", "<PERSON><PERSON><PERSON><PERSON>", "initialDelta", "client", "screen", "Touch", "userEvents", "touchInfo", "useClickAsTap", "threshold", "initialTouch", "pressEvent", "_clicks", "supportDoubleTap", "_moved", "_finished", "press", "_holdTimeout", "setTimeout", "proxy", "minHold", "_trigger", "_tap", "_clickTimeout", "_hold", "_withinIgnoreThreshold", "UserEvents", "current", "dispose", "_start", "end", "endTime", "clearTimeout", "activeTouches", "splice", "inArray", "skip", "cancel", "isMoved", "startTime", "name", "jQueryEvent", "data", "notify", "preventDefault", "xDelta", "y<PERSON><PERSON><PERSON>", "element", "options", "filter", "defaultAction", "surfaceElement", "preventIfMovingProxy", "ns", "guid", "_maxTouches", "multiTouch", "allowSelection", "captureUpIfMoved", "fastTap", "delayedClick", "eventNS", "handler", "fn", "call", "surface", "global", "ownerDocument", "documentElement", "stopPropagation", "pressed", "on", "applyEventMap", "version", "css", "touchAction", "preventDragEvent", "root", "eventCapture", "preventIfMoving", "eventName", "addEventListener", "bind", "_isMoved", "destroy", "_destroyed", "removeEventListener", "kendoD<PERSON>roy", "removeData", "_disposeAll", "unbind", "capture", "trigger", "_isMultiTouch", "_apiCall", "_maxTouchesReached", "pop", "grep", "_select", "which", "_move", "_eachTouch", "_end", "_click", "pageX", "clientX", "pageY", "clientY", "methodName", "matchingTouch", "dict", "noop", "defaultThreshold", "value", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,oBAAqB,cAAeD,IAC7C,WAwdE,MAhdC,UAAUE,EAAGC,GASV,QAASC,GAAWC,EAAQC,GACxB,GAAIC,GAAKF,EAAOG,EAAEC,SAAUC,EAAKL,EAAOM,EAAEF,SAAUG,EAAKN,EAAOE,EAAEC,SAAUI,EAAKP,EAAOK,EAAEF,SAAUK,EAAKP,EAAKK,EAAIG,EAAKL,EAAKG,CAC5H,QACIG,QACIR,GAAID,EAAKK,GAAM,EACfD,GAAID,EAAKG,GAAM,GAEnBI,SAAUC,KAAKC,KAAKL,EAAKA,EAAKC,EAAKA,IAG3C,QAASK,GAAWC,GAChB,GAA6FC,GAAQC,EAAgBC,EAAjHC,KAAcC,EAAgBL,EAAEK,cAAeC,EAAgBN,EAAEM,cAAeC,EAAM,CAC1F,IAAIP,EAAEQ,IACFJ,EAAQK,MACJC,GAAI,EACJC,MAAOX,EACPY,OAAQZ,EAAEY,OACVN,cAAeN,EAAEY,OACjBxB,SAAUY,EACVa,KAAM,YAEP,IAAIb,EAAEa,KAAKC,MAAM,SAEpB,IADAZ,EAAiBG,EAAgBA,EAAcH,kBAC1CD,EAASC,EAAeD,OAAQM,EAAMN,EAAQM,IAC/CJ,EAAQD,EAAeK,GACvBH,EAAQK,MACJrB,SAAUe,EACVQ,MAAOX,EACPY,OAAQT,EAAMS,OACdN,cAAeA,EACfI,GAAIP,EAAMY,WACVF,KAAM,cAIdT,GAAQK,KADDO,EAAQC,UAAYD,EAAQE,YAE/B9B,SAAUiB,EACVM,MAAOX,EACPY,OAAQZ,EAAEY,OACVN,cAAeA,EACfI,GAAIL,EAAcc,UAClBN,KAAM,YAINH,GAAI,EACJC,MAAOX,EACPY,OAAQZ,EAAEY,OACVN,cAAeA,EACflB,SAAUY,EACVa,KAAM,SAGd,OAAOT,GAuJX,QAASgB,GAAgBC,GAErB,IADA,GAAIC,GAAaC,EAAMC,SAASC,GAAGC,MAAM,KAAMnB,EAAM,EAAGN,EAASqB,EAAWrB,OACrEM,EAAMN,EAAQM,IACjBc,EAASC,EAAWf,IAxN/B,GACOgB,GAAQI,OAAOJ,MAAOP,EAAUO,EAAMP,QAASY,EAAQL,EAAMK,MAAOC,EAAaN,EAAMM,WAAYC,EAAMjD,EAAEiD,IAAKC,EAASlD,EAAEkD,OAAQC,EAAKhB,EAAQiB,SAAUC,EAAoBF,GAAMA,EAAGG,QAASC,EAAmB,IAAKC,EAAc,IAAKC,EAAoBtB,EAAQuB,QAAQC,KAAO,EAAI,EAAGC,EAAQ,QAASC,EAAO,OAAQC,EAAS,SAAUC,EAAQ,QAASC,EAAO,OAAQC,EAAM,MAAOC,EAAS,SAAUC,EAAM,MAAOC,EAAY,YAAaC,EAAU,UAAWC,EAAe,eAAgBC,EAAgB,gBAAiBC,EAAa,aAAcC,EAAa,aACnjBC,GACA/C,IAAO,EACPL,MAAS,EACTqD,MAAS,EACTC,QAAW,GAEXC,GAAyB1C,EAAQb,OAASa,EAAQ2C,qBAwDlDC,EAAYhC,EAAMG,QAClB8B,KAAM,SAAUC,EAAM1E,GAClB,GAAI2E,GAAOC,IACXD,GAAKD,KAAOA,EACZC,EAAKE,oBAAoB7E,GACzB2E,EAAKG,cAAgBH,EAAK3E,SAC1B2E,EAAKI,SAAWJ,EAAKK,MAAQ,EAC7BL,EAAKM,UAAYvC,KAErBwC,KAAM,SAAUlF,GACZ,GAAI2E,GAAOC,KAAMO,EAASnF,EAAS,OAAS2E,EAAKD,MAAOO,EAAYvC,IAAO0C,EAAYH,EAAYN,EAAKM,WAAa,GAChHE,GAAUrC,IAGf6B,EAAKK,MAAQG,EAASR,EAAK3E,SAC3B2E,EAAKE,oBAAoB7E,GACzB2E,EAAKU,aAAeF,EAASR,EAAKG,cAClCH,EAAKI,SAAWJ,EAAKK,MAAQI,EAC7BT,EAAKM,UAAYA,IAErBJ,oBAAqB,SAAU7E,GAC3B,GAAI2E,GAAOC,KAAMF,EAAOC,EAAKD,IAC7BC,GAAK3E,SAAWA,EAAS,OAAS0E,GAClCC,EAAKW,OAAStF,EAAS,SAAW0E,GAClCC,EAAKY,OAASvF,EAAS,SAAW0E,MAGtCc,EAAQhD,EAAMG,QACd8B,KAAM,SAAUgB,EAAYjE,EAAQkE,GAChC/C,EAAOiC,MACH7E,EAAG,GAAIyE,GAAU,IAAKkB,EAAU1F,UAChCE,EAAG,GAAIsE,GAAU,IAAKkB,EAAU1F,UAChCyB,KAAMiE,EAAUjE,KAChBkE,cAAeF,EAAWE,cAC1BC,UAAWH,EAAWG,WAAazB,EAAUuB,EAAUjE,MACvDgE,WAAYA,EACZjE,OAAQA,EACRN,cAAewE,EAAUxE,cACzB2E,aAAcH,EAAUlE,OACxBF,GAAIoE,EAAUpE,GACdwE,WAAYJ,EACZK,QAASN,EAAWM,QACpBC,iBAAkBP,EAAWO,iBAC7BC,QAAQ,EACRC,WAAW,KAGnBC,MAAO,WACHvB,KAAKwB,aAAeC,WAAW5G,EAAE6G,MAAM1B,KAAM,SAAUA,KAAKa,WAAWc,SACvE3B,KAAK4B,SAASnD,EAAOuB,KAAKkB,aAE9BW,KAAM,SAAUf,GACZ,GAAIf,GAAOC,IACXD,GAAKc,WAAWM,UACe,GAA3BpB,EAAKc,WAAWM,UAChBpB,EAAK+B,cAAgBL,WAAW,WACG,GAA3B1B,EAAKc,WAAWM,QAChBpB,EAAK6B,SAAS5C,EAAK8B,GAEnBf,EAAK6B,SAAS3C,EAAW6B,GAE7Bf,EAAKc,WAAWM,QAAU,GAC3B9C,KAGX0D,MAAO,WACH/B,KAAK4B,SAASlD,EAAMsB,KAAKkB,aAE7BZ,KAAM,SAAUQ,GACZ,GAAIf,GAAOC,IACX,KAAID,EAAKuB,UAAT,CAKA,GAFAvB,EAAK5E,EAAEmF,KAAKQ,EAAU1F,UACtB2E,EAAKzE,EAAEgF,KAAKQ,EAAU1F,WACjB2E,EAAKsB,OAAQ,CACd,GAAItB,EAAKiC,yBACL,MAEJ,IAAKC,EAAWC,SAAWD,EAAWC,UAAYnC,EAAKc,WAGnD,MAAOd,GAAKoC,SAFZpC,GAAKqC,OAAOtB,GAKff,EAAKuB,WACNvB,EAAK6B,SAAS/C,EAAMiC,KAG5BuB,IAAK,SAAUvB,GACXd,KAAKsC,QAAUxE,IACXkC,KAAKsB,YAGTtB,KAAKsB,WAAY,EACjBtB,KAAK4B,SAAS1C,EAAS4B,GACnBd,KAAKqB,OACLrB,KAAK4B,SAAS9C,EAAKgC,GAEdd,KAAKe,gBACFf,KAAKoB,iBACLpB,KAAK6B,KAAKf,GAEVd,KAAK4B,SAAS5C,EAAK8B,IAI/ByB,aAAavC,KAAKwB,cAClBxB,KAAKmC,YAETA,QAAS,WACL,GAAItB,GAAab,KAAKa,WAAY2B,EAAgB3B,EAAWzE,OAC7D4D,MAAKsB,WAAY,EACjBtB,KAAKkB,WAAa,KAClBqB,aAAavC,KAAKwB,cAClBgB,EAAcC,OAAO5H,EAAE6H,QAAQ1C,KAAMwC,GAAgB,IAEzDG,KAAM,WACF3C,KAAKmC,WAETS,OAAQ,WACJ5C,KAAKmC,WAETU,QAAS,WACL,MAAO7C,MAAKqB,QAEhBe,OAAQ,SAAUtB,GACdyB,aAAavC,KAAKwB,cAClBxB,KAAK8C,UAAYhF,IACjBkC,KAAKqB,QAAS,EACdrB,KAAK4B,SAAShD,EAAOkC,IAEzBc,SAAU,SAAUmB,EAAMjC,GACtB,GAAIf,GAAOC,KAAMgD,EAAclC,EAAUnE,MAAOsG,GACxC9G,MAAO4D,EACP5E,EAAG4E,EAAK5E,EACRG,EAAGyE,EAAKzE,EACRsB,OAAQmD,EAAKnD,OACbD,MAAOqG,EAEXjD,GAAKc,WAAWqC,OAAOH,EAAME,IAC7BD,EAAYG,kBAGpBnB,uBAAwB,WACpB,GAAIoB,GAASpD,KAAK7E,EAAEsF,aAAc4C,EAASrD,KAAK1E,EAAEmF,YAClD,OAAO5E,MAAKC,KAAKsH,EAASA,EAASC,EAASA,IAAWrD,KAAKgB,aAShEiB,EAAapE,EAAWE,QACxB8B,KAAM,SAAUyD,EAASC,GAAnB,GACeC,GA4BLC,EAWJC,EAAkCC,EAvCtC5D,EAAOC,KAAc4D,EAAKrG,EAAMsG,MACpCN,GAAUA,MACVC,EAASzD,EAAKyD,OAASD,EAAQC,OAC/BzD,EAAKiB,UAAYuC,EAAQvC,WAAa1C,EACtCyB,EAAK4B,QAAU4B,EAAQ5B,SAAWvD,EAClC2B,EAAK3D,WACL2D,EAAK+D,YAAcP,EAAQQ,WAAa,EAAI,EAC5ChE,EAAKiE,eAAiBT,EAAQS,eAC9BjE,EAAKkE,iBAAmBV,EAAQU,iBAChClE,EAAKgB,eAAiBwC,EAAQW,UAAYlH,EAAQmH,eAClDpE,EAAKqE,QAAUR,EACf7D,EAAKoB,QAAU,EACfpB,EAAKqB,iBAAmBmC,EAAQnC,iBAChCkC,EAAUzI,EAAEyI,GAASe,QAAQtE,GAC7BlC,EAAWyG,GAAGzE,KAAK0E,KAAKxE,GACxBhC,EAAOgC,GACHuD,QAASA,EACTkB,QAAmD3J,EAA1C0I,EAAQkB,QAAU/E,EAA0B4D,EAAQ,GAAGoB,cAAcC,gBAAqBpB,EAAQiB,SAAWlB,GACtHsB,gBAAiBrB,EAAQqB,gBACzBC,SAAS,IAEb9E,EAAKyE,QAAQH,QAAQtE,GAAM+E,GAAGvH,EAAMwH,cAAc,OAAQnB,GAAK,SAASkB,GAAGvH,EAAMwH,cAAc,YAAanB,GAAK,QACjHN,EAAQwB,GAAGvH,EAAMwH,cAAc,OAAQnB,GAAKJ,EAAQ,UAChDzD,EAAKgB,eACLuC,EAAQwB,GAAGvH,EAAMwH,cAAc,QAASnB,GAAKJ,EAAQ,WAErDxG,EAAQC,UAAYD,EAAQE,cACxBF,EAAQuB,QAAQyG,QAAU,IACtBvB,EAAgB,6BACpBH,EAAQ2B,IAAI,mBAAoB1B,EAAQ2B,aAAsC,QAAvB3B,EAAQ2B,YAAwBzB,EAAgB,IAAMF,EAAQ2B,YAAczB,IAEnIH,EAAQ2B,IAAI,eAAgB1B,EAAQ2B,aAAe,SAGvD3B,EAAQ4B,kBACR7B,EAAQwB,GAAGvH,EAAMwH,cAAc,YAAanB,GAAKrG,EAAM4F,gBAE3DG,EAAQwB,GAAGvH,EAAMwH,cAAc,YAAanB,GAAKJ,GAAU4B,KAAM9B,GAAW,WACxEvD,EAAKkE,kBAAoBjH,EAAQqI,eAC7B3B,EAAiB3D,EAAKyE,QAAQ,GAAIb,EAAuB9I,EAAE6G,MAAM3B,EAAKuF,gBAAiBvF,GAC3F3C,EAAgB,SAAUmI,GACtB7B,EAAe8B,iBAAiBD,EAAW5B,GAAsB,MAGzE5D,EAAK0F,MACDhH,EACAC,EACAM,EACAC,EACAL,EACAC,EACAC,EACAI,EACAH,EACAI,EACAC,EACAC,EACAC,EACAX,GACD4E,IAEP+B,gBAAiB,SAAUtJ,GACnBgE,KAAK0F,YACL1J,EAAEmH,kBAGVwC,QAAS,WAAA,GAOGjC,GANJ3D,EAAOC,IACPD,GAAK6F,aAGT7F,EAAK6F,YAAa,EACd7F,EAAKkE,kBAAoBjH,EAAQqI,eAC7B3B,EAAiB3D,EAAKyE,QAAQ,GAClCpH,EAAgB,SAAUmI,GACtB7B,EAAemC,oBAAoBN,EAAWxF,EAAKuF,oBAG3DvF,EAAKuD,QAAQwC,aAAa/F,EAAKqE,SAC/BrE,EAAKyE,QAAQsB,aAAa/F,EAAKqE,SAC/BrE,EAAKuD,QAAQyC,WAAW,WACxBhG,EAAKyE,QAAQuB,WAAW,WACxBhG,EAAKiG,cACLjG,EAAKkG,eACElG,GAAKyE,cACLzE,GAAKuD,cACLvD,GAAKzD,gBAEhB4J,QAAS,WACLjE,EAAWC,QAAUlC,MAEzB4C,OAAQ,WACJ5C,KAAKgG,cACLhG,KAAKmG,QAAQpH,IAEjBmE,OAAQ,SAAUqC,EAAWtC,GACzB,GAAIlD,GAAOC,KAAM5D,EAAU2D,EAAK3D,OAChC,IAAI4D,KAAKoG,gBAAiB,CACtB,OAAQb,GACR,IAAK1G,GACD0G,EAAYnG,CACZ,MACJ,KAAKN,GACDyG,EAAYlG,CACZ,MACJ,KAAKL,GACDuG,EAAYjG,EAGhBvB,EAAOkF,GAAQ7G,QAASA,GAAWrB,EAAWqB,EAAQ,GAAIA,EAAQ,KAEtE,MAAO4D,MAAKmG,QAAQZ,EAAWxH,EAAOkF,GAAQpG,KAAM0I,MAExDhE,MAAO,SAAUpG,EAAGG,EAAGsB,GACnBoD,KAAKqG,SAAS,SAAUlL,EAAGG,EAAGsB,IAElC0D,KAAM,SAAUnF,EAAGG,GACf0E,KAAKqG,SAAS,QAASlL,EAAGG,IAE9B+G,IAAK,SAAUlH,EAAGG,GACd0E,KAAKqG,SAAS,OAAQlL,EAAGG,IAE7B8K,cAAe,WACX,MAAOpG,MAAK5D,QAAQH,OAAS,GAEjCqK,mBAAoB,WAChB,MAAOtG,MAAK5D,QAAQH,QAAU+D,KAAK8D,aAEvCkC,YAAa,WAET,IADA,GAAI5J,GAAU4D,KAAK5D,QACZA,EAAQH,OAAS,GACpBG,EAAQmK,MAAMpE,WAGtBuD,SAAU,WACN,MAAO7K,GAAE2L,KAAKxG,KAAK5D,QAAS,SAAUD,GAClC,MAAOA,GAAM0G,YACd5G,QAEPwK,QAAS,SAAUzK,GACVgE,KAAKgE,iBAAkBhE,KAAKmG,QAAQxH,GAAUhC,MAAOX,KACtDA,EAAEmH,kBAGVf,OAAQ,SAAUpG,GACd,GAAgDY,GAA0DT,EAAtG4D,EAAOC,KAAMzD,EAAM,EAAGiH,EAASzD,EAAKyD,OAAgBpH,EAAUL,EAAWC,GAAIC,EAASG,EAAQH,OAAeyK,EAAQ1K,EAAE0K,KAC3H,MAAIA,GAASA,EAAQ,GAAK3G,EAAKuG,sBAQ/B,IALArE,EAAWC,QAAU,KACrBnC,EAAKzD,cAAgBN,EAAEM,cACnByD,EAAK6E,iBACL5I,EAAE4I,kBAECrI,EAAMN,IACL8D,EAAKuG,qBADQ/J,IAIjBJ,EAAQC,EAAQG,GAEZK,EADA4G,EACS3I,EAAEsB,EAAMG,eAERyD,EAAKuD,QAEb1G,EAAOX,SAGZE,EAAQ,GAAIyE,GAAMb,EAAMnD,EAAQT,GAChC4D,EAAK3D,QAAQK,KAAKN,GAClBA,EAAMoF,QACFxB,EAAKqG,iBACLrG,EAAKmD,OAAO,qBAIxByD,MAAO,SAAU3K,GACbgE,KAAK4G,WAAW,OAAQ5K,IAE5B6K,KAAM,SAAU7K,GACZgE,KAAK4G,WAAW,MAAO5K,IAE3B8K,OAAQ,SAAU9K,GACd,GAAIiH,IACA9G,OACI8E,aAAcjF,EAAEY,OAChBA,OAAQ/B,EAAEmB,EAAEM,eACZgG,QAASxE,IACT3C,GACIC,SAAUY,EAAE+K,MACZrG,OAAQ1E,EAAEgL,SAEd1L,GACIF,SAAUY,EAAEiL,MACZvG,OAAQ1E,EAAEkL,UAGlB/L,EAAGa,EAAE+K,MACLzL,EAAGU,EAAEiL,MACLrK,OAAQ/B,EAAEmB,EAAEM,eACZK,MAAOX,EACPa,KAAM,MAENmD,MAAKmG,QAAQ,MAAOlD,IACpBjH,EAAEmH,kBAGVyD,WAAY,SAAUO,EAAYnL,GAC9B,GAAmFO,GAAKJ,EAAO2E,EAAWsG,EAAtGrH,EAAOC,KAAMqH,KAAWjL,EAAUL,EAAWC,GAAIwG,EAAgBzC,EAAK3D,OAC1E,KAAKG,EAAM,EAAGA,EAAMiG,EAAcvG,OAAQM,IACtCJ,EAAQqG,EAAcjG,GACtB8K,EAAKlL,EAAMO,IAAMP,CAErB,KAAKI,EAAM,EAAGA,EAAMH,EAAQH,OAAQM,IAChCuE,EAAY1E,EAAQG,GACpB6K,EAAgBC,EAAKvG,EAAUpE,IAC3B0K,GACAA,EAAcD,GAAYrG,IAItCuF,SAAU,SAAUxJ,EAAM1B,EAAGG,EAAGsB,GAC5BoD,KAAKnD,IACDL,KAAK,EACLuK,MAAO5L,EACP8L,MAAO3L,EACP0L,QAAS7L,EACT+L,QAAS5L,EACTsB,OAAQ/B,EAAE+B,GAAUoD,KAAKsD,SAAS,GAClCsB,gBAAiB/J,EAAEyM,KACnBnE,eAAgBtI,EAAEyM,SAI9BrF,GAAWsF,iBAAmB,SAAUC,GACpClJ,EAAoBkJ,GAExBvF,EAAWN,QAAU,SAAU6F,GAC3BpJ,EAAmBoJ,GAEvBjK,EAAMxB,WAAaA,EACnBwB,EAAMxC,WAAaA,EACnBwC,EAAM0E,WAAaA,GACrBtE,OAAOJ,MAAMkK,QACR9J,OAAOJ,OACE,kBAAV3C,SAAwBA,OAAO8M,IAAM9M,OAAS,SAAU+M,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.userevents.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.userevents', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'userevents',\n        name: 'User Events',\n        category: 'framework',\n        depends: ['core'],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, support = kendo.support, Class = kendo.Class, Observable = kendo.Observable, now = $.now, extend = $.extend, OS = support.mobileOS, invalidZeroEvents = OS && OS.android, DEFAULT_MIN_HOLD = 800, CLICK_DELAY = 300, DEFAULT_THRESHOLD = support.browser.msie ? 5 : 0, PRESS = 'press', HOLD = 'hold', SELECT = 'select', START = 'start', MOVE = 'move', END = 'end', CANCEL = 'cancel', TAP = 'tap', DOUBLETAP = 'doubleTap', RELEASE = 'release', GESTURESTART = 'gesturestart', GESTURECHANGE = 'gesturechange', GESTUREEND = 'gestureend', GESTURETAP = 'gesturetap';\n        var THRESHOLD = {\n            'api': 0,\n            'touch': 0,\n            'mouse': 9,\n            'pointer': 9\n        };\n        var ENABLE_GLOBAL_SURFACE = !support.touch || support.mouseAndTouchPresent;\n        function touchDelta(touch1, touch2) {\n            var x1 = touch1.x.location, y1 = touch1.y.location, x2 = touch2.x.location, y2 = touch2.y.location, dx = x1 - x2, dy = y1 - y2;\n            return {\n                center: {\n                    x: (x1 + x2) / 2,\n                    y: (y1 + y2) / 2\n                },\n                distance: Math.sqrt(dx * dx + dy * dy)\n            };\n        }\n        function getTouches(e) {\n            var touches = [], originalEvent = e.originalEvent, currentTarget = e.currentTarget, idx = 0, length, changedTouches, touch;\n            if (e.api) {\n                touches.push({\n                    id: 2,\n                    event: e,\n                    target: e.target,\n                    currentTarget: e.target,\n                    location: e,\n                    type: 'api'\n                });\n            } else if (e.type.match(/touch/)) {\n                changedTouches = originalEvent ? originalEvent.changedTouches : [];\n                for (length = changedTouches.length; idx < length; idx++) {\n                    touch = changedTouches[idx];\n                    touches.push({\n                        location: touch,\n                        event: e,\n                        target: touch.target,\n                        currentTarget: currentTarget,\n                        id: touch.identifier,\n                        type: 'touch'\n                    });\n                }\n            } else if (support.pointers || support.msPointers) {\n                touches.push({\n                    location: originalEvent,\n                    event: e,\n                    target: e.target,\n                    currentTarget: currentTarget,\n                    id: originalEvent.pointerId,\n                    type: 'pointer'\n                });\n            } else {\n                touches.push({\n                    id: 1,\n                    event: e,\n                    target: e.target,\n                    currentTarget: currentTarget,\n                    location: e,\n                    type: 'mouse'\n                });\n            }\n            return touches;\n        }\n        var TouchAxis = Class.extend({\n            init: function (axis, location) {\n                var that = this;\n                that.axis = axis;\n                that._updateLocationData(location);\n                that.startLocation = that.location;\n                that.velocity = that.delta = 0;\n                that.timeStamp = now();\n            },\n            move: function (location) {\n                var that = this, offset = location['page' + that.axis], timeStamp = now(), timeDelta = timeStamp - that.timeStamp || 1;\n                if (!offset && invalidZeroEvents) {\n                    return;\n                }\n                that.delta = offset - that.location;\n                that._updateLocationData(location);\n                that.initialDelta = offset - that.startLocation;\n                that.velocity = that.delta / timeDelta;\n                that.timeStamp = timeStamp;\n            },\n            _updateLocationData: function (location) {\n                var that = this, axis = that.axis;\n                that.location = location['page' + axis];\n                that.client = location['client' + axis];\n                that.screen = location['screen' + axis];\n            }\n        });\n        var Touch = Class.extend({\n            init: function (userEvents, target, touchInfo) {\n                extend(this, {\n                    x: new TouchAxis('X', touchInfo.location),\n                    y: new TouchAxis('Y', touchInfo.location),\n                    type: touchInfo.type,\n                    useClickAsTap: userEvents.useClickAsTap,\n                    threshold: userEvents.threshold || THRESHOLD[touchInfo.type],\n                    userEvents: userEvents,\n                    target: target,\n                    currentTarget: touchInfo.currentTarget,\n                    initialTouch: touchInfo.target,\n                    id: touchInfo.id,\n                    pressEvent: touchInfo,\n                    _clicks: userEvents._clicks,\n                    supportDoubleTap: userEvents.supportDoubleTap,\n                    _moved: false,\n                    _finished: false\n                });\n            },\n            press: function () {\n                this._holdTimeout = setTimeout($.proxy(this, '_hold'), this.userEvents.minHold);\n                this._trigger(PRESS, this.pressEvent);\n            },\n            _tap: function (touchInfo) {\n                var that = this;\n                that.userEvents._clicks++;\n                if (that.userEvents._clicks == 1) {\n                    that._clickTimeout = setTimeout(function () {\n                        if (that.userEvents._clicks == 1) {\n                            that._trigger(TAP, touchInfo);\n                        } else {\n                            that._trigger(DOUBLETAP, touchInfo);\n                        }\n                        that.userEvents._clicks = 0;\n                    }, CLICK_DELAY);\n                }\n            },\n            _hold: function () {\n                this._trigger(HOLD, this.pressEvent);\n            },\n            move: function (touchInfo) {\n                var that = this;\n                if (that._finished) {\n                    return;\n                }\n                that.x.move(touchInfo.location);\n                that.y.move(touchInfo.location);\n                if (!that._moved) {\n                    if (that._withinIgnoreThreshold()) {\n                        return;\n                    }\n                    if (!UserEvents.current || UserEvents.current === that.userEvents) {\n                        that._start(touchInfo);\n                    } else {\n                        return that.dispose();\n                    }\n                }\n                if (!that._finished) {\n                    that._trigger(MOVE, touchInfo);\n                }\n            },\n            end: function (touchInfo) {\n                this.endTime = now();\n                if (this._finished) {\n                    return;\n                }\n                this._finished = true;\n                this._trigger(RELEASE, touchInfo);\n                if (this._moved) {\n                    this._trigger(END, touchInfo);\n                } else {\n                    if (!this.useClickAsTap) {\n                        if (this.supportDoubleTap) {\n                            this._tap(touchInfo);\n                        } else {\n                            this._trigger(TAP, touchInfo);\n                        }\n                    }\n                }\n                clearTimeout(this._holdTimeout);\n                this.dispose();\n            },\n            dispose: function () {\n                var userEvents = this.userEvents, activeTouches = userEvents.touches;\n                this._finished = true;\n                this.pressEvent = null;\n                clearTimeout(this._holdTimeout);\n                activeTouches.splice($.inArray(this, activeTouches), 1);\n            },\n            skip: function () {\n                this.dispose();\n            },\n            cancel: function () {\n                this.dispose();\n            },\n            isMoved: function () {\n                return this._moved;\n            },\n            _start: function (touchInfo) {\n                clearTimeout(this._holdTimeout);\n                this.startTime = now();\n                this._moved = true;\n                this._trigger(START, touchInfo);\n            },\n            _trigger: function (name, touchInfo) {\n                var that = this, jQueryEvent = touchInfo.event, data = {\n                        touch: that,\n                        x: that.x,\n                        y: that.y,\n                        target: that.target,\n                        event: jQueryEvent\n                    };\n                if (that.userEvents.notify(name, data)) {\n                    jQueryEvent.preventDefault();\n                }\n            },\n            _withinIgnoreThreshold: function () {\n                var xDelta = this.x.initialDelta, yDelta = this.y.initialDelta;\n                return Math.sqrt(xDelta * xDelta + yDelta * yDelta) <= this.threshold;\n            }\n        });\n        function withEachUpEvent(callback) {\n            var downEvents = kendo.eventMap.up.split(' '), idx = 0, length = downEvents.length;\n            for (; idx < length; idx++) {\n                callback(downEvents[idx]);\n            }\n        }\n        var UserEvents = Observable.extend({\n            init: function (element, options) {\n                var that = this, filter, ns = kendo.guid();\n                options = options || {};\n                filter = that.filter = options.filter;\n                that.threshold = options.threshold || DEFAULT_THRESHOLD;\n                that.minHold = options.minHold || DEFAULT_MIN_HOLD;\n                that.touches = [];\n                that._maxTouches = options.multiTouch ? 2 : 1;\n                that.allowSelection = options.allowSelection;\n                that.captureUpIfMoved = options.captureUpIfMoved;\n                that.useClickAsTap = !options.fastTap && !support.delayedClick();\n                that.eventNS = ns;\n                that._clicks = 0;\n                that.supportDoubleTap = options.supportDoubleTap;\n                element = $(element).handler(that);\n                Observable.fn.init.call(that);\n                extend(that, {\n                    element: element,\n                    surface: options.global && ENABLE_GLOBAL_SURFACE ? $(element[0].ownerDocument.documentElement) : $(options.surface || element),\n                    stopPropagation: options.stopPropagation,\n                    pressed: false\n                });\n                that.surface.handler(that).on(kendo.applyEventMap('move', ns), '_move').on(kendo.applyEventMap('up cancel', ns), '_end');\n                element.on(kendo.applyEventMap('down', ns), filter, '_start');\n                if (that.useClickAsTap) {\n                    element.on(kendo.applyEventMap('click', ns), filter, '_click');\n                }\n                if (support.pointers || support.msPointers) {\n                    if (support.browser.version < 11) {\n                        var defaultAction = 'pinch-zoom double-tap-zoom';\n                        element.css('-ms-touch-action', options.touchAction && options.touchAction != 'none' ? defaultAction + ' ' + options.touchAction : defaultAction);\n                    } else {\n                        element.css('touch-action', options.touchAction || 'none');\n                    }\n                }\n                if (options.preventDragEvent) {\n                    element.on(kendo.applyEventMap('dragstart', ns), kendo.preventDefault);\n                }\n                element.on(kendo.applyEventMap('mousedown', ns), filter, { root: element }, '_select');\n                if (that.captureUpIfMoved && support.eventCapture) {\n                    var surfaceElement = that.surface[0], preventIfMovingProxy = $.proxy(that.preventIfMoving, that);\n                    withEachUpEvent(function (eventName) {\n                        surfaceElement.addEventListener(eventName, preventIfMovingProxy, true);\n                    });\n                }\n                that.bind([\n                    PRESS,\n                    HOLD,\n                    TAP,\n                    DOUBLETAP,\n                    START,\n                    MOVE,\n                    END,\n                    RELEASE,\n                    CANCEL,\n                    GESTURESTART,\n                    GESTURECHANGE,\n                    GESTUREEND,\n                    GESTURETAP,\n                    SELECT\n                ], options);\n            },\n            preventIfMoving: function (e) {\n                if (this._isMoved()) {\n                    e.preventDefault();\n                }\n            },\n            destroy: function () {\n                var that = this;\n                if (that._destroyed) {\n                    return;\n                }\n                that._destroyed = true;\n                if (that.captureUpIfMoved && support.eventCapture) {\n                    var surfaceElement = that.surface[0];\n                    withEachUpEvent(function (eventName) {\n                        surfaceElement.removeEventListener(eventName, that.preventIfMoving);\n                    });\n                }\n                that.element.kendoDestroy(that.eventNS);\n                that.surface.kendoDestroy(that.eventNS);\n                that.element.removeData('handler');\n                that.surface.removeData('handler');\n                that._disposeAll();\n                that.unbind();\n                delete that.surface;\n                delete that.element;\n                delete that.currentTarget;\n            },\n            capture: function () {\n                UserEvents.current = this;\n            },\n            cancel: function () {\n                this._disposeAll();\n                this.trigger(CANCEL);\n            },\n            notify: function (eventName, data) {\n                var that = this, touches = that.touches;\n                if (this._isMultiTouch()) {\n                    switch (eventName) {\n                    case MOVE:\n                        eventName = GESTURECHANGE;\n                        break;\n                    case END:\n                        eventName = GESTUREEND;\n                        break;\n                    case TAP:\n                        eventName = GESTURETAP;\n                        break;\n                    }\n                    extend(data, { touches: touches }, touchDelta(touches[0], touches[1]));\n                }\n                return this.trigger(eventName, extend(data, { type: eventName }));\n            },\n            press: function (x, y, target) {\n                this._apiCall('_start', x, y, target);\n            },\n            move: function (x, y) {\n                this._apiCall('_move', x, y);\n            },\n            end: function (x, y) {\n                this._apiCall('_end', x, y);\n            },\n            _isMultiTouch: function () {\n                return this.touches.length > 1;\n            },\n            _maxTouchesReached: function () {\n                return this.touches.length >= this._maxTouches;\n            },\n            _disposeAll: function () {\n                var touches = this.touches;\n                while (touches.length > 0) {\n                    touches.pop().dispose();\n                }\n            },\n            _isMoved: function () {\n                return $.grep(this.touches, function (touch) {\n                    return touch.isMoved();\n                }).length;\n            },\n            _select: function (e) {\n                if (!this.allowSelection || this.trigger(SELECT, { event: e })) {\n                    e.preventDefault();\n                }\n            },\n            _start: function (e) {\n                var that = this, idx = 0, filter = that.filter, target, touches = getTouches(e), length = touches.length, touch, which = e.which;\n                if (which && which > 1 || that._maxTouchesReached()) {\n                    return;\n                }\n                UserEvents.current = null;\n                that.currentTarget = e.currentTarget;\n                if (that.stopPropagation) {\n                    e.stopPropagation();\n                }\n                for (; idx < length; idx++) {\n                    if (that._maxTouchesReached()) {\n                        break;\n                    }\n                    touch = touches[idx];\n                    if (filter) {\n                        target = $(touch.currentTarget);\n                    } else {\n                        target = that.element;\n                    }\n                    if (!target.length) {\n                        continue;\n                    }\n                    touch = new Touch(that, target, touch);\n                    that.touches.push(touch);\n                    touch.press();\n                    if (that._isMultiTouch()) {\n                        that.notify('gesturestart', {});\n                    }\n                }\n            },\n            _move: function (e) {\n                this._eachTouch('move', e);\n            },\n            _end: function (e) {\n                this._eachTouch('end', e);\n            },\n            _click: function (e) {\n                var data = {\n                    touch: {\n                        initialTouch: e.target,\n                        target: $(e.currentTarget),\n                        endTime: now(),\n                        x: {\n                            location: e.pageX,\n                            client: e.clientX\n                        },\n                        y: {\n                            location: e.pageY,\n                            client: e.clientY\n                        }\n                    },\n                    x: e.pageX,\n                    y: e.pageY,\n                    target: $(e.currentTarget),\n                    event: e,\n                    type: 'tap'\n                };\n                if (this.trigger('tap', data)) {\n                    e.preventDefault();\n                }\n            },\n            _eachTouch: function (methodName, e) {\n                var that = this, dict = {}, touches = getTouches(e), activeTouches = that.touches, idx, touch, touchInfo, matchingTouch;\n                for (idx = 0; idx < activeTouches.length; idx++) {\n                    touch = activeTouches[idx];\n                    dict[touch.id] = touch;\n                }\n                for (idx = 0; idx < touches.length; idx++) {\n                    touchInfo = touches[idx];\n                    matchingTouch = dict[touchInfo.id];\n                    if (matchingTouch) {\n                        matchingTouch[methodName](touchInfo);\n                    }\n                }\n            },\n            _apiCall: function (type, x, y, target) {\n                this[type]({\n                    api: true,\n                    pageX: x,\n                    pageY: y,\n                    clientX: x,\n                    clientY: y,\n                    target: $(target || this.element)[0],\n                    stopPropagation: $.noop,\n                    preventDefault: $.noop\n                });\n            }\n        });\n        UserEvents.defaultThreshold = function (value) {\n            DEFAULT_THRESHOLD = value;\n        };\n        UserEvents.minHold = function (value) {\n            DEFAULT_MIN_HOLD = value;\n        };\n        kendo.getTouches = getTouches;\n        kendo.touchDelta = touchDelta;\n        kendo.UserEvents = UserEvents;\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}