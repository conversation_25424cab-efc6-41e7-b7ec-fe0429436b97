/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("util/ripple.min",["kendo.core.min"],e)}(function(){!function(){var e,t,n,i,o,r,s,c;this.kendo.util=this.kendo.util||{},window.kendo.util.ripple=window.kendo.util.ripple||{},e=function(e,t){var n,i;if(e.closest)return e.closest(t);for(n=Element.prototype.matches?function(e,t){return e.matches(t)}:function(e,t){return e.msMatchesSelector(t)},i=e;i;){if(n(i,t))return i;i=i.parentElement}},t=function(e){var t,n=e.createElement("div");return n.className="k-ripple",t=e.createElement("div"),t.className="k-ripple-blob",n.appendChild(t),[n,t]},n=function(e,t,n){var i=function(){n(),e.removeEventListener(t,i,!1)},o=function(){return e.addEventListener(t,i,!1)};return o(),{remove:o}},i=function(i,s){return function(c){var u,a,l,p,d,f,m,h,v,k,w,g,y,b,E,x,L=c.target,C=L.document||L.ownerDocument;if(u=s.container?s.container(L):e(L,i),u&&(a=/focus/i.test(c.type)&&u.classList.contains("k-ripple-target"),!a)){if(u.classList.add("k-ripple-target"),l=t(C),p=l[0],d=l[1],f={animated:!1,released:!1,blob:d,container:u,ripple:p},m={focusin:"focusout",keydown:"keyup",mousedown:"mouseup",pointerdown:"pointerup",touchdown:"touchup"}[c.type],n(c.currentTarget,m,function(){return r(f)}),u.appendChild(p),window.getComputedStyle(p).getPropertyValue("opacity"),h=u.getBoundingClientRect(),v=0,k=0,/mouse|pointer|touch/.test(c.type)?(v=c.clientX-h.left,k=c.clientY-h.top):(v=h.width/2,k=h.height/2),w=v<h.width/2?h.width:0,g=k<h.height/2?h.height:0,y=v-w,b=k-g,E=2*Math.sqrt(y*y+b*b),x=500,d.style.width=d.style.height=E+"px",d.offsetWidth<0)throw Error("Inconceivable!");d.style.cssText="\n    width: "+E+"px;\n    height: "+E+"px;\n    transform: translate(-50%, -50%) scale(1);\n    left: "+v+"px;\n    top: "+k+"px;\n  ",setTimeout(function(){return o(f)},x)}}},o=function(e){e.animated=!0,s(e)},r=function(e){e.released=!0,s(e)},s=function(e){if(e.released&&e.animated){var t=e.blob,i=e.ripple,o=e.container;o&&n(o,"blur",function(){return o.classList.remove("k-ripple-target")}),t&&(n(t,"transitionend",function(){i&&i.parentNode&&i.parentNode.removeChild(i)}),t.style.transition="opacity 200ms linear",t.style.opacity="0")}},c=function(e,t){var n=function(e){return[].concat.apply([],e)},o=n(t.map(function(t){var n={events:["mousedown","touchdown"],global:!1},o=t.selector,r=t.options,s=void 0===r?n:r,c=i(o,s),u=s.events||n.events,a=s.global?document.body:e;return u.forEach(function(e){return a.addEventListener(e,c,!1)}),{events:u,options:s,activator:c}}));return function(){if(e){var t=function(t){var n=t.events,i=t.options,o=t.activator,r=i.global?document.body:e;n.forEach(function(e){return r.removeEventListener(e,o,!1)})};o.forEach(t),e=null}}},kendo.deepExtend(kendo.util.ripple,{register:c})}()},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.ripple.min",["util/ripple.min"],e)}(function(){return function(e,t){var n=window.kendo,i=n.ui,o=i.Widget,r=e.extend,s=n.util.ripple,c=o.extend({init:function(e,t){var n=this;o.fn.init.call(n,e),e=n.wrapper=n.element,n.options=r({},n.options,t),n.registerListeners()},options:{name:"RippleContainer",elements:[{selector:".k-button:not(li)"},{selector:".k-list > .k-item",options:{global:!0}},{selector:".k-checkbox-label, .k-radio-label"},{selector:".k-checkbox, .k-radio",options:{events:["focusin"],container:function(e){if(/\b(k-checkbox|k-radio)\b/.test(e.className))return e.nextElementSibling}}}]},removeListeners:function(){},registerListeners:function(){var e,t=this,n=t.element[0],i=t.options.elements;t.removeListeners(),e=s.register(n,i),t.removeListeners=e},destroy:function(){var e=this;o.fn.destroy.call(e),e.removeListeners()}});i.plugin(c)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.ripple.min.js.map
