/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return(t+"").replace(a,h)}function i(t){var e,i=[];for(e in t)i.push(e+t[e]);return i.sort().join("")}function n(t){var e,i=2166136261;for(e=0;e<t.length;++e)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=t.charCodeAt(e);return i>>>0}function s(){return{width:0,height:0,baseline:0}}function o(t,e,i){return u.current.measure(t,e,i)}var r,a,h,l,c,u;window.kendo.util=window.kendo.util||{},r=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,e){var i=this._map,n={key:t,value:e};i[t]=n,this._head?(this._tail.newer=n,n.older=this._tail,this._tail=n):this._head=this._tail=n,this._length>=this._size?(i[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var e=this._map[t];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),a=/\r?\n|\r|\t/g,h=" ",l={baselineMarkerSize:1},"undefined"!=typeof document&&(c=document.createElement("div"),c.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),u=kendo.Class.extend({init:function(e){this._cache=new r(1e3),this.options=t.extend({},l,e)},measure:function(t,o,r){var a,h,l,u,p,d,g,f,v;if(void 0===r&&(r={}),!t)return s();if(a=i(o),h=n(t+a),l=this._cache.get(h))return l;u=s(),p=r.box||c,d=this._baselineMarker().cloneNode(!1);for(g in o)f=o[g],void 0!==f&&(p.style[g]=f);return v=r.normalizeText!==!1?e(t):t+"",p.textContent=v,p.appendChild(d),document.body.appendChild(p),v.length&&(u.width=p.offsetWidth-this.options.baselineMarkerSize,u.height=p.offsetHeight,u.baseline=d.offsetTop+this.options.baselineMarkerSize),u.width>0&&u.height>0&&this._cache.put(h,u),p.parentNode.removeChild(p),u},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),u.current=new u,kendo.deepExtend(kendo.util,{LRUCache:r,TextMetrics:u,measureText:o,objectKey:i,hashKey:n,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/chart/kendo-chart.min",["kendo.core.min","kendo.color.min","kendo.drawing.min","kendo.dataviz.core.min"],t)}(function(){!function(t){function e(t,e){for(var i=0;i<e.length;i++)if(e[i].options.name===t)return e[i].prepareUserOptions(),new le(e[i])}function i(t){var e,i,n=t.length,s=0;for(e=0;e<n;e++)i=t[e],et(i)&&s++;return s}function n(t,e){if(null===e)return e;var i=_t(t,!0);return i(e)}function s(t,e,i,n){var o,r,a,h,l;if(void 0===i&&(i={}),void 0===n&&(n=!1),o=i.defaults=i.defaults||{},r=i.depth=i.depth||0,a=!1,i.excluded=i.excluded||[],r>Qi)return null;for(h in t)!Pt(h,i.excluded)&&t.hasOwnProperty(h)&&(l=t[h],Tt(l)?(a=!0,n||(t[h]=Et(l(e),o[h]))):Rt(l)&&(n||(i.defaults=o[h]),i.depth++,a=s(l,e,i,n)||a,i.depth--));return a}function o(t){var e,i=t.length,n=0;for(e=0;e<i;e++)n=Math.max(n,t[e].data.length);return n}function r(t,e,i,n,s){var o=t.box,r=new ae;return r[e]=o[e+i],r[n]=o[n+s],r}function a(t){for(var e=0;e<t.length;e++)if(yt(t[e].zIndex))return!0}function h(t){var e=t.overlay;return e&&e.gradient&&"none"!==e.gradient}function l(t,e){t.forEach(e)}function c(t,e){var i,n=t.length;for(i=n-1;i>=0;i--)e(t[i],i-n-1)}function u(t){return i(t)===t.length}function p(t){return yt(t)&&null!==t}function d(t){var e,i,n,s=t.series;for(e=0;e<s.length;e++)i=s[e],n=i.data,!n||wt(n[0])||Rt(n[0])||(i.data=[n])}function g(){return"pane"+Hn++}function f(t,e){null!==e&&t.push(e)}function v(t,e,i){var n,s=e.visible;return yt(s)?s:(n=t.pointVisibility,n?n[i]:void 0)}function m(t){var e,i,n,s=t.data,o=[],r=0,a=0;for(e=0;e<s.length;e++)i=de.current.bindPoint(t,e),n=i.valueFields.value,Ft(n)&&(n=parseFloat(n)),et(n)?(i.visible=v(t,i.fields,e)!==!1,i.value=Math.abs(n),o.push(i),i.visible&&(r+=i.value),0!==n&&a++):o.push(null);return{total:r,points:o,count:a}}function x(t,e){return t&&e?t.toLowerCase()===e.toLowerCase():t===e}function y(t,e){var i,n,s=[],o=[].concat(e);for(i=0;i<t.length;i++)n=t[i],Pt(n.type,o)&&s.push(n);return s}function _(t,e,i){var n,s;return null===e?e:(n="_date_"+t,s=e[n],s||(s=Nt(i,_t(t,!0)(e)),e[n]=s),s)}function w(t,e){var i=t.type,n=e instanceof Date;return!i&&n||x(i,vt)}function b(t){return 1===t.length?t[0]:t}function A(t){return t.missingValues?t.missingValues:Fn.test(t.type)||t.stack?Le:Me}function S(t,e){var i,n=de.current.bindPoint(t,null,e),s=n.valueFields;for(i in s)if(J.convertableToNumber(s[i]))return!0}function C(t){var e,i,n=t.start,s=t.dir,o=t.min,r=t.max,a=t.getter,h=t.hasItem,l=t.series,c=n;do c+=s,h(c)&&(i=a(c),e=S(l,i.item));while(o<=c&&c<=r&&!e);if(e)return i}function k(t,e,i,n,s){var o,r,a,h,l,c=e.min,u=e.max,p=c>0&&c<i,d=u+1<i;(p||d)&&(o=A(t),o!==Me?(p&&(r=n(c-1)),d&&(a=n(u+1))):(p&&(h=n(c-1),l=S(t,h.item),r=l?h:C({start:c,dir:-1,min:0,max:i-1,getter:n,hasItem:s,series:t})),d&&(h=n(u+1),l=S(t,h.item),a=l?h:C({start:u,dir:1,min:0,max:i-1,getter:n,hasItem:s,series:t}))),r&&(t._outOfRangeMinPoint=r),a&&(t._outOfRangeMaxPoint=a))}function P(t){var e,i,n,s=t.length;if(s>0)for(i=0;i<s;i++)n=t[i].contentBox(),e?e.wrap(n):e=n.clone();return e||new St}function T(t,e){var i,n;for(i=0;i<t.length;i++)if(n=t[i],n&&n.pane===e)return n}function E(t){return""===t||null===t||"none"===t||"transparent"===t||!yt(t)}function R(t,e,i){var n,s=e,o=t;if(t.indexOf(".")>-1){for(n=t.split(".");n.length>1;)o=n.shift(),yt(s[o])||(s[o]={}),s=s[o];o=n.shift()}s[o]=i}function I(t,e,i){var n=[].concat(e instanceof qt?t.categoryAxis:t.valueAxis);It(n[e.axisIndex],i)}function V(t,e,i){return Ut(t,function(t){return 0===i&&!t.categoryAxis||t.categoryAxis===e})}function L(){this._defaultPrevented=!0}function M(t,e){var i=(e||"").toLowerCase(),n=t.event,s="none"===i&&!(n.ctrlKey||n.shiftKey||n.altKey)||n[i+"Key"];return s}function O(t){var e,i,n={};for(e=0;e<t.length;e++)i=t[e],i.axis.options.name&&(n[i.axis.options.name]={min:i.range.min,max:i.range.max});return n}function B(t){var e=document.createElement("div");return t&&(e.className=t),e}function z(t){for(var e=t;e&&!jt(e,"k-handle");)e=e.parentNode;return e}function D(t,e,i,n){var s=[].concat(i?t.yAxis:t.xAxis)[e];It(s,n)}function H(t){var e,i,n=J.rad(t),s=Xt(Math.sin(n),mt),o=Xt(Math.cos(n),mt);return e=Math.abs(s)>Ms?rt:o<0?gt:lt,i=Math.abs(s)<Ls?rt:s<0?ht:at,{horizontal:e,vertical:i}}function F(t,e,i,n){var s,o,r=(n.x-i.x)*(t.y-i.y)-(n.y-i.y)*(t.x-i.x),a=(n.y-i.y)*(e.x-t.x)-(n.x-i.x)*(e.y-t.y);return 0!==a&&(o=r/a,s=new At(t.x+o*(e.x-t.x),t.y+o*(e.y-t.y))),s}function N(t,e){return t.value.x-e.value.x}function G(t){var e,i,n=xo;for(e=0;e<n.length;e++)i=n[e]+"Axes",t[i]&&(t[n[e]+"Axis"]=t[i],delete t[i])}function q(t,e){if(t)for(var i=0;i<t.length;i++)if(t[i].category===e)return[t[i]]}function W(t,e){function i(t){var e=(t||{}).color||s.color,i=It({},a,a[n],s,s[n],{line:{color:e},labels:{color:e},title:{color:e}},t);return delete i[n],i}var n,s,o,r,a=(e||{}).axisDefaults||{};for(r=0;r<xo.length;r++)n=xo[r]+"Axis",s=t.axisDefaults||{},o=[].concat(t[n]),o=o.map(i),t[n]=o.length>1?o:o[0]}function X(t,e){var i,n,s,o=t.series,r=o.length,a=t.seriesDefaults,h=It({},t.seriesDefaults),l=e?It({},e.seriesDefaults):{},c=It({},l);for(Z(h),Z(c),i=0;i<r;i++)n=o[i].type||t.seriesDefaults.type,s=It({data:[]},c,l[n],{tooltip:t.tooltip},h,a[n]),o[i]._defaults=s,o[i]=It({},s,o[i]),o[i].data=o[i].data||[]}function Z(t){delete t.bar,delete t.column,delete t.rangeColumn,delete t.line,delete t.verticalLine,delete t.pie,delete t.donut,delete t.area,delete t.verticalArea,delete t.scatter,delete t.scatterLine,delete t.bubble,delete t.candlestick,delete t.ohlc,delete t.boxPlot,delete t.bullet,delete t.verticalBullet,delete t.polarArea,delete t.polarLine,delete t.radarArea,delete t.radarLine,delete t.waterfall}function U(t){var e,i,n,s={};for(e=0;e<t.length;e++)i=t[e],n=i.options.name,n&&(s[n]=i.range());return s}function Y(t,e){void 0===e&&(e=J.dateComparer);for(var i=1,n=t.length;i<n;i++)if(e(t[i],t[i-1])<0){t.sort(e);break}return t}function j(t,e){var i,n,s,o;for(void 0===e&&(e=J.dateComparer),i=Y(t,e),n=i.length,s=n>0?[i[0]]:[],o=1;o<n;o++)0!==e(i[o],Vt(s))&&s.push(i[o]);return s}function K(t){var e,i,n,s,o=t.length,r=[];for(e=0;e<o;e++)for(i=t[e],n=i.length,s=0;s<n;s++)r[s]=r[s]||[],r[s].push(i[s]);return r}function Q(t,e){var i,n,s,o;for(i in e)!Pt(i,So)&&e.hasOwnProperty(i)&&(n=e[i],s=t[i],yt(s)&&(o=null===n,o||!yt(n)?(delete t[i],o&&delete e[i]):s&&Rt(n)&&Rt(s)&&Q(s,n)))}function $(t){for(var e=0;e<t.length;e++)t[e].notifyRender()}var J,tt,et,it,nt,st,ot,rt,at,ht,lt,ct,ut,pt,dt,gt,ft,vt,mt,xt,yt,_t,wt,bt,At,St,Ct,kt,Pt,Tt,Et,Rt,It,Vt,Lt,Mt,Ot,Bt,zt,Dt,Ht,Ft,Nt,Gt,qt,Wt,Xt,Zt,Ut,Yt,jt,Kt,Qt,$t,Jt,te,ee,ie,ne,se,oe,re,ae,he,le,ce,ue,pe,de,ge,fe,ve,me,xe,ye,_e,we,be,Ae,Se,Ce,ke,Pe,Te,Ee,Re,Ie,Ve,Le,Me,Oe,Be,ze,De,He,Fe,Ne,Ge,qe,We,Xe,Ze,Ue,Ye,je,Ke,Qe,$e,Je,ti,ei,ii,ni,si,oi,ri,ai,hi,li,ci,ui,pi,di,gi,fi,vi,mi,xi,yi,_i,wi,bi,Ai,Si,Ci,ki,Pi,Ti,Ei,Ri,Ii,Vi,Li,Mi,Oi,Bi,zi,Di,Hi,Fi,Ni,Gi,qi,Wi,Xi,Zi,Ui,Yi,ji,Ki,Qi,$i,Ji,tn,en,nn,sn,on,rn,an,hn,ln,cn,un,pn,dn,gn,fn,vn,mn,xn,yn,_n,wn,bn,An,Sn,Cn,kn,Pn,Tn,En,Rn,In,Vn,Ln,Mn,On,Bn,zn,Dn,Hn,Fn,Nn,Gn,qn,Wn,Xn,Zn,Un,Yn,jn,Kn,Qn,$n,Jn,ts,es,is,ns,ss,os,rs,as,hs,ls,cs,us,ps,ds,gs,fs,vs,ms,xs,ys,_s,ws,bs,As,Ss,Cs,ks,Ps,Ts,Es,Rs,Is,Vs,Ls,Ms,Os,Bs,zs,Ds,Hs,Fs,Ns,Gs,qs,Ws,Xs,Zs,Us,Ys,js,Ks,Qs,$s,Js,to,eo,io,no,so,oo,ro,ao,ho,lo,co,uo,po,go,fo,vo,mo,xo,yo,_o,wo,bo,Ao,So;window.kendo.dataviz=window.kendo.dataviz||{},J=kendo.dataviz,tt=J.Class,et=J.isNumber,it=J.constants,nt=it.MAX_VALUE,st=it.MIN_VALUE,ot=it.VALUE,rt=it.CENTER,at=it.TOP,ht=it.BOTTOM,lt=it.LEFT,ct=it.WHITE,ut=it.CIRCLE,pt=it.X,dt=it.Y,gt=it.RIGHT,ft=it.BLACK,vt=it.DATE,mt=it.DEFAULT_PRECISION,xt=it.ARC,yt=J.defined,_t=J.getter,wt=J.isArray,bt=J.ChartElement,At=J.Point,St=J.Box,Ct=J.alignPathToPixel,kt=J.setDefaultOptions,Pt=J.inArray,Tt=J.isFunction,Et=J.valueOrDefault,Rt=J.isObject,It=J.deepExtend,Vt=J.last,Lt=J.eventElement,Mt=J.getTemplate,Ot=J.TextBox,Bt=J.ShapeElement,zt=J.getSpacing,Dt=J.CurveProcessor,Ht=J.append,Ft=J.isString,Nt=J.parseDate,Gt=J.styleValue,qt=J.CategoryAxis,Wt=J.BoxElement,Xt=J.round,Zt=J.limitValue,Ut=J.grep,Yt=J.elementStyles,jt=J.hasClasses,Kt=J.bindEvents,Qt=J.services,$t=J.unbindEvents,Jt=kendo.support,te=kendo.drawing,ee=te.Path,ie=te.Animation,ne=te.AnimationFactory,se=te.Group,oe=kendo.Color,re=kendo.geometry,ae=re.Point,he=re.transform,le=tt.extend({init:function(t){this._axis=t,this.options=t.options},value:function(t){var e=this._axis,i=e.getCategory?e.getCategory(t):e.getValue(t);return i},slot:function(t,e,i){return void 0===i&&(i=!0),this._axis.slot(t,e,i)},range:function(){return this._axis.range()},valueRange:function(){return this._axis.valueRange()}}),ce=kendo.Class.extend({init:function(t){this.visual=t.visual,this.chartsVisual=t.chartContainer.visual,this._pane=t},findAxisByName:function(t){return e(t,this._pane.axes)}}),ue=tt.extend({init:function(t){this._plotArea=t,this.visual=t.visual,this.backgroundVisual=t._bgVisual}}),pe={min:function(t){var e,i,n=t.length,s=nt;for(e=0;e<n;e++)i=t[e],et(i)&&(s=Math.min(s,i));return s===nt?t[0]:s},max:function(t){var e,i,n=t.length,s=st;for(e=0;e<n;e++)i=t[e],et(i)&&(s=Math.max(s,i));return s===st?t[0]:s},sum:function(t){var e,i,n=t.length,s=0;for(e=0;e<n;e++)i=t[e],et(i)&&(s+=i);return s},sumOrNull:function(t){var e=null;return i(t)&&(e=pe.sum(t)),e},count:function(t){var e,i,n=t.length,s=0;for(e=0;e<n;e++)i=t[e],null!==i&&yt(i)&&s++;return s},avg:function(t){var e=i(t),n=t[0];return e>0&&(n=pe.sum(t)/e),n},first:function(t){var e,i,n=t.length;for(e=0;e<n;e++)if(i=t[e],null!==i&&yt(i))return i;return t[0]}},de=tt.extend({init:function(){this._valueFields={},this._otherFields={},this._nullValue={},this._undefinedValue={}},register:function(t,e,i){var n,s,o=this;for(void 0===e&&(e=[ot]),void 0===i&&(i={}),n=0;n<t.length;n++)s=t[n],o._valueFields[s]=e,o._otherFields[s]=i,o._nullValue[s]=o._makeValue(e,null),o._undefinedValue[s]=o._makeValue(e,void 0)},canonicalFields:function(t){return this.valueFields(t).concat(this.otherFields(t))},valueFields:function(t){return this._valueFields[t.type]||[ot]},otherFields:function(t){return this._otherFields[t.type]||[ot]},bindPoint:function(t,e,i){var n,s,o,r,a,h=t.data,l=yt(i)?i:h[e],c={valueFields:{value:l}},u=this.valueFields(t),p=this._otherFields[t.type];return null===l?s=this._nullValue[t.type]:yt(l)?Array.isArray(l)?(o=l.slice(u.length),s=this._bindFromArray(l,u),n=this._bindFromArray(o,p)):"object"==typeof l&&(r=this.sourceFields(t,u),a=this.sourceFields(t,p),s=this._bindFromObject(l,u,r),n=this._bindFromObject(l,p,a)):s=this._undefinedValue[t.type],yt(s)&&(1===u.length?c.valueFields.value=s[u[0]]:c.valueFields=s),c.fields=n||{},c},_makeValue:function(t,e){var i,n,s={},o=t.length;for(i=0;i<o;i++)n=t[i],s[n]=e;return s},_bindFromArray:function(t,e){var i,n,s={};if(e)for(i=Math.min(e.length,t.length),n=0;n<i;n++)s[e[n]]=t[n];return s},_bindFromObject:function(t,e,i){var s,o,r,a,h;if(void 0===i&&(i=e),s={},e)for(o=e.length,r=0;r<o;r++)a=e[r],h=i[r],null!==h&&(s[a]=n(h,t));return s},sourceFields:function(t,e){var i,n,s,o,r=[];if(e)for(i=e.length,n=0;n<i;n++)s=e[n],o=s===ot?"field":s+"Field",r.push(null!==t[o]?t[o]||s:null);return r}}),de.current=new de,ge="stderr",fe="stddev",ve=/percent(?:\w*)\((\d+)\)/,me=RegExp("^"+fe+"(?:\\((\\d+(?:\\.\\d+)?)\\))?$"),xe=tt.extend({init:function(t,e,i){this.initGlobalRanges(t,e,i)},initGlobalRanges:function(t,e,i){var n,s,o,r,a,h=e.data,l=me.exec(t);l?(this.valueGetter=this.createValueGetter(e,i),n=this.getAverage(h),s=this.getStandardDeviation(h,n,!1),o=l[1]?parseFloat(l[1]):1,r={low:n.value-s*o,high:n.value+s*o},this.globalRange=function(){return r}):t.indexOf&&t.indexOf(ge)>=0&&(this.valueGetter=this.createValueGetter(e,i),a=this.getStandardError(h,this.getAverage(h)),this.globalRange=function(t){return{low:t-a,high:t+a}})},createValueGetter:function(t,e){var i,n,s,o=t.data,r=de.current,a=r.valueFields(t),h=yt(o[0])?o[0]:{};return wt(h)?(n=e?a.indexOf(e):0,i=_t("["+n+"]")):et(h)?i=_t():typeof h===it.OBJECT&&(s=r.sourceFields(t,a),i=_t(s[a.indexOf(e)])),i},getErrorRange:function(t,e){var i,n,s,o;if(!yt(e))return null;if(this.globalRange)return this.globalRange(t);if(wt(e))i=t-e[0],n=t+e[1];else if(et(s=parseFloat(e)))i=t-s,n=t+s;else{if(!(s=ve.exec(e)))throw Error("Invalid ErrorBar value: "+e);o=t*(parseFloat(s[1])/100),i=t-Math.abs(o),n=t+Math.abs(o)}return{low:i,high:n}},getStandardError:function(t,e){return this.getStandardDeviation(t,e,!0)/Math.sqrt(e.count)},getStandardDeviation:function(t,e,i){var n,s,o=this,r=t.length,a=i?e.count-1:e.count,h=0;for(n=0;n<r;n++)s=o.valueGetter(t[n]),et(s)&&(h+=Math.pow(s-e.value,2));return Math.sqrt(h/a)},getAverage:function(t){var e,i,n=this,s=t.length,o=0,r=0;for(e=0;e<s;e++)i=n.valueGetter(t[e]),et(i)&&(o+=i,r++);return{value:o/r,count:r}}}),ye=Jt.browser||{},_e=600,we="fadeIn",be="glass",Ae=.8,Se=5,Ce=ye.msie?.001:0,ke="errorLow",Pe="errorHigh",Te="xErrorLow",Ee="xErrorHigh",Re="yErrorLow",Ie="yErrorHigh",Ve=8,Le="zero",Me="interpolate",Oe="gap",Be="above",ze="below",De="smooth",He="step",Fe="area",Ne="bar",Ge="boxPlot",qe="bubble",We="bullet",Xe="candlestick",Ze="column",Ue="donut",Ye="funnel",je="horizontalWaterfall",Ke="line",Qe="ohlc",$e="pie",Je="polarArea",ti="polarLine",ei="polarScatter",ii="radarArea",ni="radarColumn",si="radarLine",oi="rangeArea",ri="rangeBar",ai="rangeColumn",hi="scatter",li="scatterLine",ci="verticalArea",ui="verticalBoxPlot",pi="verticalBullet",di="verticalLine",gi="verticalRangeArea",fi="waterfall",vi=[Ne,Ze,Qe,Xe,Ge,ui,We,ai,ri,fi,je],mi="legendItemClick",xi="legendItemHover",yi="legendItemLeave",_i="seriesClick",wi="seriesHover",bi="seriesOver",Ai="seriesLeave",Si="plotAreaClick",Ci="plotAreaHover",ki="plotAreaLeave",Pi="drag",Ti="dragEnd",Ei="dragStart",Ri="zoomStart",Ii="zoom",Vi="zoomEnd",Li="selectStart",Mi="select",Oi="selectEnd",Bi="render",zi="showTooltip",Di="hideTooltip",Hi="paneRender",Fi="log",Ni="category",Gi="insideEnd",qi="insideBase",Wi="outsideEnd",Xi="DOMMouseScroll mousewheel",Zi=150,Ui={INITIAL_ANIMATION_DURATION:_e,FADEIN:we,LEGEND_ITEM_CLICK:mi,LEGEND_ITEM_HOVER:xi,LEGEND_ITEM_LEAVE:yi,SERIES_CLICK:_i,SERIES_HOVER:wi,SERIES_OVER:bi,SERIES_LEAVE:Ai,GLASS:be,BORDER_BRIGHTNESS:Ae,TOOLTIP_OFFSET:Se,START_SCALE:Ce,ERROR_LOW_FIELD:ke,ERROR_HIGH_FIELD:Pe,X_ERROR_LOW_FIELD:Te,X_ERROR_HIGH_FIELD:Ee,Y_ERROR_LOW_FIELD:Re,Y_ERROR_HIGH_FIELD:Ie,LINE_MARKER_SIZE:Ve,INTERPOLATE:Me,ZERO:Le,SMOOTH:De,STEP:He,CATEGORY:Ni,FUNNEL:Ye,BAR:Ne,CANDLESTICK:Xe,PIE:$e,COLUMN:Ze,AREA:Fe,VERTICAL_BULLET:pi,BOX_PLOT:Ge,OHLC:Qe,WATERFALL:fi,LINE:Ke,BULLET:We,VERTICAL_LINE:di,VERTICAL_AREA:ci,RANGE_AREA:oi,VERTICAL_RANGE_AREA:gi,RANGE_COLUMN:ai,VERTICAL_BOX_PLOT:ui,RANGE_BAR:ri,HORIZONTAL_WATERFALL:je,SCATTER:hi,SCATTER_LINE:li,BUBBLE:qe,RADAR_AREA:ii,RADAR_LINE:si,RADAR_COLUMN:ni,POLAR_LINE:ti,POLAR_AREA:Je,POLAR_SCATTER:ei,RENDER:Bi,PLOT_AREA_CLICK:Si,PLOT_AREA_HOVER:Ci,PLOT_AREA_LEAVE:ki,LOGARITHMIC:Fi,DRAG:Pi,DRAG_START:Ei,DRAG_END:Ti,ZOOM_START:Ri,ZOOM:Ii,ZOOM_END:Vi,SELECT_START:Li,SELECT:Mi,SELECT_END:Oi,PANE_RENDER:Hi,GAP:Oe,DONUT:Ue,INSIDE_END:Gi,INSIDE_BASE:qi,OUTSIDE_END:Wi,MOUSEWHEEL:Xi,MOUSEWHEEL_DELAY:Zi,SHOW_TOOLTIP:zi,HIDE_TOOLTIP:Di,EQUALLY_SPACED_SERIES:vi,ABOVE:Be,BELOW:ze},Yi=4,ji=bt.extend({init:function(t,e,i,n,s,o){bt.fn.init.call(this,o),this.low=t,this.high=e,this.isVertical=i,this.chart=n,this.series=s},reflow:function(t){var e,i=this.options.endCaps,n=this.isVertical,s=this.getAxis(),o=s.getSlot(this.low,this.high),r=t.center(),a=this.getCapsWidth(t,n),h=n?r.x:r.y,l=h-a,c=h+a;n?(e=[new At(r.x,o.y1),new At(r.x,o.y2)],i&&e.push(new At(l,o.y1),new At(c,o.y1),new At(l,o.y2),new At(c,o.y2)),this.box=new St(l,o.y1,c,o.y2)):(e=[new At(o.x1,r.y),new At(o.x2,r.y)],i&&e.push(new At(o.x1,l),new At(o.x1,c),new At(o.x2,l),new At(o.x2,c)),this.box=new St(o.x1,l,o.x2,c)),this.linePoints=e},getCapsWidth:function(t,e){var i=e?t.width():t.height(),n=Math.min(Math.floor(i/2),Yi)||Yi;return n},createVisual:function(){var t=this,e=this.options,i=e.visual;i?this.visual=i({low:this.low,high:this.high,rect:this.box.toRect(),sender:this.getSender(),options:{endCaps:e.endCaps,color:e.color,line:e.line},createVisual:function(){t.createDefaultVisual();var e=t.visual;return delete t.visual,e}}):this.createDefaultVisual()},createDefaultVisual:function(){var t,e,i=this,n=this,s=n.options,o=n.linePoints,r={stroke:{color:s.color,width:s.line.width,dashType:s.line.dashType}};for(bt.fn.createVisual.call(this),t=0;t<o.length;t+=2)e=new ee(r).moveTo(o[t].x,o[t].y).lineTo(o[t+1].x,o[t+1].y),Ct(e),i.visual.append(e)}}),kt(ji,{animation:{type:we,delay:_e},endCaps:!0,line:{width:2},zIndex:1}),Ki=ji.extend({getAxis:function(){var t=this.chart.seriesValueAxis(this.series);return t}}),Qi=5,$i=bt.extend({init:function(t,e){bt.fn.init.call(this,e),this.plotArea=t,this.chartService=t.chartService,this.categoryAxis=t.seriesCategoryAxis(e.series[0]),this.valueAxisRanges={},this.points=[],this.categoryPoints=[],this.seriesPoints=[],this.seriesOptions=[],this._evalSeries=[],this.render()},render:function(){this.traverseDataPoints(this.addValue.bind(this))},pointOptions:function(t,e){var i,n=this.seriesOptions[e];return n||(i=this.pointType().prototype.defaults,this.seriesOptions[e]=n=It({},i,{vertical:!this.options.invertAxes},t)),n},plotValue:function(t){var e,i,n,s,o,r,a,h;if(!t)return 0;if(this.options.isStacked100&&et(t.value)){for(e=t.categoryIx,i=this.categoryPoints[e],n=[],s=0,o=0;o<i.length;o++)if(r=i[o]){if(a=t.series.stack,h=r.series.stack,a&&h&&a.group!==h.group)continue;et(r.value)&&(s+=Math.abs(r.value),n.push(Math.abs(r.value)))}if(s>0)return t.value/s}return t.value},plotRange:function(t,e){var i,n,s,o,r,a,h,l,c,u,p,d,g,f=this;if(void 0===e&&(e=0),i=this.categoryPoints[t.categoryIx],this.options.isStacked){for(n=this.plotValue(t),s=n>=0,o=e,r=!1,a=0;a<i.length&&(h=i[a],t!==h);a++){if(l=t.series.stack,c=h.series.stack,l&&c){if(typeof l===it.STRING&&l!==c)continue;if(l.group&&l.group!==c.group)continue}u=f.plotValue(h),(u>=0&&s||u<0&&!s)&&(o+=u,n+=u,r=!0,f.options.isStacked100&&(n=Math.min(n,1)))}return r&&(o-=e),[o,n]}return p=t.series,d=this.seriesValueAxis(p),g=this.categoryAxisCrossingValue(d),[g,J.convertableToNumber(t.value)?t.value:g]},stackLimits:function(t,e){var i,n,s,o,r,a=this,h=nt,l=st;for(i=0;i<this.categoryPoints.length;i++)if(n=a.categoryPoints[i])for(s=0;s<n.length;s++)o=n[s],o&&(o.series.stack!==e&&o.series.axis!==t||(r=a.plotRange(o,0)[1],yt(r)&&isFinite(r)&&(l=Math.max(l,r),h=Math.min(h,r))));return{min:h,max:l}},updateStackRange:function(){var t,e,i,n,s,o,r=this,a=this.options,h=a.isStacked,l=a.series,c={};if(h)for(t=0;t<l.length;t++)e=l[t],i=e.axis,n=i+e.stack,s=c[n],s||(s=r.stackLimits(i,e.stack),o=r.errorTotals,o&&(o.negative.length&&(s.min=Math.min(s.min,J.sparseArrayLimits(o.negative).min)),o.positive.length&&(s.max=Math.max(s.max,J.sparseArrayLimits(o.positive).max))),s.min!==nt||s.max!==st?c[n]=s:s=null),s&&(r.valueAxisRanges[i]=s)},addErrorBar:function(t,e,i){var n,s=t.value,o=t.series,r=t.seriesIx,a=t.options.errorBars,h=e.fields[ke],l=e.fields[Pe];et(h)&&et(l)?n={low:h,high:l}:a&&yt(a.value)&&(this.seriesErrorRanges=this.seriesErrorRanges||[],this.seriesErrorRanges[r]=this.seriesErrorRanges[r]||new xe(a.value,o,ot),n=this.seriesErrorRanges[r].getErrorRange(s,a.value)),n&&(t.low=n.low,t.high=n.high,this.addPointErrorBar(t,i))},addPointErrorBar:function(t,e){var i,n,s,o=!this.options.invertAxes,r=t.options.errorBars,a=t.series,h=t.low,l=t.high;this.options.isStacked?(i=this.stackedErrorRange(t,e),h=i.low,l=i.high):(n={categoryIx:e,series:a},this.updateRange({value:h},n),this.updateRange({value:l},n)),s=new Ki(h,l,o,this,a,r),t.errorBars=[s],t.append(s)},stackedErrorRange:function(t,e){var i=this.plotRange(t,0)[1]-t.value,n=t.low+i,s=t.high+i;return this.errorTotals=this.errorTotals||{positive:[],negative:[]},n<0&&(this.errorTotals.negative[e]=Math.min(this.errorTotals.negative[e]||0,n)),s>0&&(this.errorTotals.positive[e]=Math.max(this.errorTotals.positive[e]||0,s)),{low:n,high:s}},addValue:function(e,i){var n,s,o=i.categoryIx,r=i.series,a=i.seriesIx,h=this.categoryPoints[o];h||(this.categoryPoints[o]=h=[]),n=this.seriesPoints[a],n||(this.seriesPoints[a]=n=[]),s=this.createPoint(e,i),s&&(t.extend(s,i),s.owner=this,s.noteText=e.fields.noteText,yt(s.dataItem)||(s.dataItem=r.data[o]),this.addErrorBar(s,e,o)),this.points.push(s),n.push(s),h.push(s),this.updateRange(e.valueFields,i)},evalPointOptions:function(t,e,i,n,o,r){var a,h={defaults:o._defaults,excluded:["data","aggregate","_events","tooltip","content","template","visual","toggle","_outOfRangeMinPoint","_outOfRangeMaxPoint"]},l=this._evalSeries[r];return yt(l)||(this._evalSeries[r]=l=s(t,{},h,!0)),a=t,l&&(a=It({},a),s(a,{value:e,category:i,index:n,series:o,dataItem:o.data[n]},h)),a},updateRange:function(t,e){var i=e.series.axis,n=t.value,s=this.valueAxisRanges[i];isFinite(n)&&null!==n&&(s=this.valueAxisRanges[i]=s||{min:nt,max:st},s.min=Math.min(s.min,n),s.max=Math.max(s.max,n))},seriesValueAxis:function(t){var e=this.plotArea,i=t.axis,n=i?e.namedValueAxes[i]:e.valueAxis;if(!n)throw Error("Unable to locate value axis with name "+i);return n},reflow:function(t){var e=this,i=this.categorySlots=[],n=this.points,s=this.categoryAxis,o=0;this.traverseDataPoints(function(t,r){var a,h,l,c=r.categoryIx,u=r.series,p=e.seriesValueAxis(u),d=n[o++],g=i[c];g||(i[c]=g=e.categorySlot(s,c,p)),d&&(a=e.plotRange(d,p.startValue()),h=e.valueSlot(p,a),h?(l=e.pointSlot(g,h),d.aboveAxis=e.aboveAxis(d,p),d.stackValue=a[1],e.options.isStacked100&&(d.percentage=e.plotValue(d)),e.reflowPoint(d,l)):d.visible=!1)}),this.reflowCategories(i),!this.options.clip&&this.options.limitPoints&&this.points.length&&this.limitPoints(),this.box=t},valueSlot:function(t,e){return t.getSlot(e[0],e[1],!this.options.clip)},limitPoints:function(){var t,e=this,i=this.categoryPoints,n=i[0].concat(Vt(i));for(t=0;t<n.length;t++)n[t]&&e.limitPoint(n[t])},limitPoint:function(t){var e=this.categoryAxis.limitSlot(t.box);e.equals(t.box)||t.reflow(e)},aboveAxis:function(t,e){var i=this.categoryAxisCrossingValue(e),n=t.value;return e.options.reverse?n<i:n>=i},categoryAxisCrossingValue:function(t){var e=this.categoryAxis,i=t.options,n=[].concat(i.axisCrossingValues||i.axisCrossingValue);return n[e.axisIndex||0]||0},reflowPoint:function(t,e){t.reflow(e)},reflowCategories:function(){},pointSlot:function(t,e){var i=this.options,n=i.invertAxes,s=n?e:t,o=n?t:e;return new St(s.x1,o.y1,s.x2,o.y2)},categorySlot:function(t,e){return t.getSlot(e)},traverseDataPoints:function(t){var e,i,n,s,r,a,h,l=this,c=this.options.series,u=o(c),p=c.length;for(e=0;e<p;e++)l._outOfRangeCallback(c[e],"_outOfRangeMinPoint",e,t);for(i=0;i<u;i++)for(n=0;n<p;n++)s=c[n],r=l.categoryAxis.categoryAt(i),a=l._bindPoint(s,n,i),t(a,{category:r,categoryIx:i,series:s,seriesIx:n});for(h=0;h<p;h++)l._outOfRangeCallback(c[h],"_outOfRangeMaxPoint",h,t)},_outOfRangeCallback:function(t,e,i,n){var s,o,r=t[e];r&&(s=r.categoryIx,o=this._bindPoint(t,i,s,r.item),n(o,{category:r.category,categoryIx:s,series:t,seriesIx:i,dataItem:r.item}))},_bindPoint:function(t,e,i,n){var s,o;return this._bindCache||(this._bindCache=[]),s=this._bindCache[e],s||(s=this._bindCache[e]=[]),o=s[i],o||(o=s[i]=de.current.bindPoint(t,i,n)),o},formatPointValue:function(t,e){return null===t.value?"":this.chartService.format.auto(e,t.value)},pointValue:function(t){return t.valueFields.value}}),kt($i,{series:[],invertAxes:!1,isStacked:!1,clip:!0,limitPoints:!0}),Ji={click:function(t,e){return t.trigger(_i,this.eventArgs(e))},hover:function(t,e){return t.trigger(wi,this.eventArgs(e))},over:function(t,e){return t.trigger(bi,this.eventArgs(e))},out:function(t,e){return t.trigger(Ai,this.eventArgs(e))},eventArgs:function(t){return{value:this.value,percentage:this.percentage,stackValue:this.stackValue,category:this.category,series:this.series,dataItem:this.dataItem,runningTotal:this.runningTotal,total:this.total,element:Lt(t),originalEvent:t,point:this}}},tn={createNote:function(){var t=this.options.notes,e=this.noteText||t.label.text;t.visible!==!1&&yt(e)&&null!==e&&(this.note=new J.Note({value:this.value,text:e,dataItem:this.dataItem,category:this.category,series:this.series},this.options.notes,this.owner.chartService),this.append(this.note))}},en=bt.extend({init:function(t,e){bt.fn.init.call(this),this.value=t,this.options=e,this.aboveAxis=Et(this.options.aboveAxis,!0),this.tooltipTracking=!0},render:function(){var t,e,i,n=this.options,s=n.markers,o=n.labels;this._rendered||(this._rendered=!0,s.visible&&s.size&&(this.marker=this.createMarker(),this.append(this.marker)),o.visible&&(t=Mt(o),e=this.pointData(),i=this.value,t?i=t(e):o.format&&(i=this.formatValue(o.format)),this.label=new Ot(i,It({align:rt,vAlign:rt,margin:{left:5,right:5},zIndex:Et(o.zIndex,this.series.zIndex)},o),e),this.append(this.label)),this.createNote(),this.errorBar&&this.append(this.errorBar))},markerBorder:function(){var t=this.options.markers,e=t.background,i=It({color:this.color},t.border);return yt(i.color)||(i.color=new oe(e).brightness(Ae).toHex()),i},createVisual:function(){},createMarker:function(){var t=this.options.markers,e=new Bt({type:t.type,width:t.size,height:t.size,rotation:t.rotation,background:t.background,border:this.markerBorder(),opacity:t.opacity,zIndex:Et(t.zIndex,this.series.zIndex),animation:t.animation,visual:t.visual},{dataItem:this.dataItem,value:this.value,series:this.series,category:this.category});return e},markerBox:function(){return this.marker||(this.marker=this.createMarker(),this.marker.reflow(this._childBox)),this.marker.box},reflow:function(t){var e,i,n,s,o=this,r=this,a=r.options,h=r.aboveAxis,l=a.vertical;if(this.render(),this.box=t,e=t.clone(),l?h?e.y1-=e.height():e.y2+=e.height():h?e.x1+=e.width():e.x2-=e.width(),this._childBox=e,this.marker&&this.marker.reflow(e),this.reflowLabel(e),this.errorBars)for(i=0;i<this.errorBars.length;i++)o.errorBars[i].reflow(e);this.note&&(n=this.markerBox(),a.markers.visible&&a.markers.size||(s=n.center(),n=new St(s.x,s.y,s.x,s.y)),this.note.reflow(n))},reflowLabel:function(t){var e=this,i=e.options,n=e.label,s=i.labels.position;n&&(s=s===Be?at:s,s=s===ze?ht:s,n.reflow(t),n.box.alignTo(this.markerBox(),s),n.reflow(n.box))},createHighlight:function(){var t=this.options.highlight.markers,e=this.markerBorder().color,i=this.options.markers,n=i.size+(i.border.width||0)+(t.border.width||0),s=new Bt({type:i.type,width:n,height:n,rotation:i.rotation,background:t.color||e,border:{color:t.border.color,width:t.border.width,opacity:Et(t.border.opacity,1)},opacity:Et(t.opacity,1)});return s.reflow(this._childBox),s.getElement()},highlightVisual:function(){return(this.marker||{}).visual},highlightVisualArgs:function(){var t,e,i,n,s,o=this.marker;return o?(e=o.paddingBox.toRect(),t=o.visual):(i=this.options.markers.size,n=i/2,s=this.box.center(),e=new re.Rect([s.x-n,s.y-n],[i,i])),{options:this.options,rect:e,visual:t}},tooltipAnchor:function(){var t,e,i,n,s=this.markerBox(),o=this.owner.pane.clipBox(),r=!o||o.overlaps(s);if(r)return t=s.x2+Se,e=lt,this.aboveAxis?(i=s.y1,n=ht):(i=s.y2,n=at),{point:new At(t,i),align:{horizontal:e,vertical:n}}},formatValue:function(t){return this.owner.formatPointValue(this,t)},overlapsBox:function(t){var e=this.markerBox();return e.overlaps(t)},unclipElements:function(){this.label&&(this.label.options.noclip=!0),this.note&&(this.note.options.noclip=!0)},pointData:function(){return{dataItem:this.dataItem,category:this.category,value:this.value,percentage:this.percentage,stackValue:this.stackValue,series:this.series}}}),en.prototype.defaults={vertical:!0,markers:{visible:!0,background:ct,size:Ve,type:ut,border:{width:2},opacity:1},labels:{visible:!1,position:Be,margin:zt(3),padding:zt(4),animation:{type:we,delay:_e}},notes:{label:{}},highlight:{markers:{border:{color:"#fff",width:2}},zIndex:it.HIGHLIGHT_ZINDEX},errorBars:{line:{width:1}}},It(en.prototype,Ji),It(en.prototype,tn),nn=bt.extend({init:function(t,e,i){bt.fn.init.call(this),this.linePoints=t,this.series=e,this.seriesIx=i},points:function(){return this.toGeometryPoints(this.linePoints)},toGeometryPoints:function(t){var e,i,n=[];for(e=0,i=t.length;e<i;e++)t[e]&&t[e].visible!==!1&&n.push(t[e]._childBox.toRect().center());return n},createVisual:function(){var t=this,e=this.series.visual;e?(this.visual=e({points:this.toGeometryPoints(this.linePoints),series:this.series,sender:this.getSender(),createVisual:function(){return t.segmentVisual(),t.visual}}),this.visual&&!yt(this.visual.options.zIndex)&&(this.visual.options.zIndex=this.series.zIndex)):this.segmentVisual()},segmentVisual:function(){var t,e=this,i=e.options,n=e.series,s=n.color,o=n._defaults;Tt(s)&&o&&(s=o.color),t=ee.fromPoints(this.points(),{stroke:{color:s,width:n.width,opacity:n.opacity,dashType:n.dashType},zIndex:n.zIndex}),i.closed&&t.close(),this.visual=t},aliasFor:function(t,e){return this.parent.getNearestPoint(e.x,e.y,this.seriesIx)}}),kt(nn,{closed:!1}),sn={calculateStepPoints:function(t){var e,i,n,s=this.parent.plotArea.seriesCategoryAxis(this.series),o=s.options,a=o.justified,h=o.vertical,l=o.reverse,c=h?pt:dt,u=h?dt:pt,p=l?2:1,d=p,g=r(t[0],c,p,u,d),f=[g];for(e=1;e<t.length;e++)i=r(t[e],c,p,u,d),g[c]!==i[c]&&(n=new ae,n[c]=g[c],n[u]=i[u],f.push(n,i)),g=i;return a?g!==Vt(f)&&f.push(g):f.push(r(Vt(t),c,p,u,l?1:2)),f}},on=nn.extend({points:function(){return this.calculateStepPoints(this.linePoints)}}),It(on.prototype,sn),rn=nn.extend({segmentVisual:function(){
var t,e,i,n=this.series,s=n._defaults,o=n.color;Tt(o)&&s&&(o=s.color),t=new Dt(this.options.closed),e=t.process(this.points()),i=new ee({stroke:{color:o,width:n.width,opacity:n.opacity,dashType:n.dashType},zIndex:n.zIndex}),i.segments.push.apply(i.segments,e),this.visual=i}}),an={renderSegments:function(){var t,e,i,n,s,o,r,a,h=this,l=this,c=l.options,u=l.seriesPoints,p=c.series,d=u.length;for(this._segments=[],e=0;e<d;e++){for(i=p[e],n=h.sortPoints(u[e]),s=n.length,o=[],r=0;r<s;r++)a=n[r],a?o.push(a):h.seriesMissingValues(i)!==Me&&(o.length>1&&(t=h.createSegment(o,i,e,t),h._addSegment(t)),o=[]);o.length>1&&(t=h.createSegment(o,i,e,t),h._addSegment(t))}this.children.unshift.apply(this.children,this._segments)},_addSegment:function(t){this._segments.push(t),t.parent=this},sortPoints:function(t){return t},seriesMissingValues:function(t){var e=t.missingValues,i=!e&&this.options.isStacked;return i?Le:e||Me},getNearestPoint:function(t,e,i){var n,s,o,r,a,h=new At(t,e),l=this.seriesPoints[i],c=nt;for(s=0;s<l.length;s++)o=l[s],o&&yt(o.value)&&null!==o.value&&o.visible!==!1&&(r=o.box,a=r.center().distanceTo(h),a<c&&(n=o,c=a));return n}},hn=ie.extend({setup:function(){this._setEnd(this.options.box.x1)},step:function(t){var e=this.options.box;this._setEnd(J.interpolateValue(e.x1,e.x2,t))},_setEnd:function(t){var e=this.element,i=e.segments,n=i[1].anchor(),s=i[2].anchor();e.suspend(),n.setX(t),e.resume(),s.setX(t)}}),kt(hn,{duration:_e}),ne.current.register("clip",hn),ln={createAnimation:function(){var t,e,i=this.getRoot();i&&(i.options||{}).transitions!==!1&&(t=i.size(),e=ee.fromRect(t.toRect()),this.visual.clip(e),this.animation=new hn(e,{box:t}),a(this.options.series)&&this._setChildrenAnimation(e))},_setChildrenAnimation:function(t){var e,i,n=this.animationPoints();for(e=0;e<n.length;e++)i=n[e],i&&i.visual&&yt(i.visual.options.zIndex)&&i.visual.clip(t)}},cn=$i.extend({render:function(){$i.fn.render.call(this),this.updateStackRange(),this.renderSegments()},pointType:function(){return en},createPoint:function(t,e){var i,n,s,o=e.categoryIx,r=e.category,a=e.series,h=e.seriesIx,l=this.seriesMissingValues(a),c=t.valueFields.value;if(!yt(c)||null===c){if(l!==Le)return null;c=0}return i=this.pointOptions(a,h),i=this.evalPointOptions(i,c,r,o,a,h),n=t.fields.color||a.color,Tt(a.color)&&(n=i.color),s=new en(c,i),s.color=n,this.append(s),s},plotRange:function(t){var e,i,n,s,o=this,r=this.plotValue(t);if(this.options.isStacked)for(e=t.categoryIx,i=this.categoryPoints[e],n=0;n<i.length&&(s=i[n],t!==s);n++)r+=o.plotValue(s),o.options.isStacked100&&(r=Math.min(r,1));return[r,r]},createSegment:function(t,e,i){var n,s=e.style;return new(n=s===He?on:s===De?rn:nn)(t,e,i)},animationPoints:function(){var t,e=this.points,i=[];for(t=0;t<e.length;t++)i.push((e[t]||{}).marker);return i.concat(this._segments)}}),It(cn.prototype,an,ln),un=nn.extend({init:function(t,e,i,n,s){nn.fn.init.call(this,t,e,i),this.prevSegment=n,this.stackPoints=s},createVisual:function(){var t=this.series,e=t._defaults,i=t.line||{},n=t.color;Tt(n)&&e&&(n=e.color),this.visual=new se({zIndex:t.zIndex}),this.createFill({fill:{color:n,opacity:t.opacity},stroke:null}),i.width>0&&i.visible!==!1&&this.createStroke({stroke:It({color:n,opacity:t.opacity,lineCap:"butt"},i)})},strokeSegments:function(){var t=this._strokeSegments;return t||(t=this._strokeSegments=this.createStrokeSegments()),t},createStrokeSegments:function(){return this.segmentsFromPoints(this.points())},stackSegments:function(){return this.prevSegment?this.prevSegment.createStackSegments(this.stackPoints):this.createStackSegments(this.stackPoints)},createStackSegments:function(t){return this.segmentsFromPoints(this.toGeometryPoints(t)).reverse()},segmentsFromPoints:function(t){return t.map(function(t){return new re.Segment(t)})},createStroke:function(t){var e=new ee(t);e.segments.push.apply(e.segments,this.strokeSegments()),this.visual.append(e)},hasStackSegment:function(){return this.prevSegment||this.stackPoints&&this.stackPoints.length},createFill:function(t){var e,i,n=this.strokeSegments(),s=n.slice(0),o=this.hasStackSegment();o&&(e=this.stackSegments(),Ht(s,e)),i=new ee(t),i.segments.push.apply(i.segments,s),!o&&n.length>1&&this.fillToAxes(i),this.visual.append(i)},fillToAxes:function(t){var e=this.parent,i=e.options.invertAxes,n=e.seriesValueAxis(this.series),s=e.categoryAxisCrossingValue(n),o=n.getSlot(s,s,!0),r=this.strokeSegments(),a=r[0].anchor(),h=Vt(r).anchor(),l=i?o.x1:o.y1;i?t.lineTo(l,h.y).lineTo(l,a.y):t.lineTo(h.x,l).lineTo(a.x,l)}}),pn=un.extend({createStrokeSegments:function(){return this.segmentsFromPoints(this.calculateStepPoints(this.linePoints))},createStackSegments:function(t){return this.segmentsFromPoints(this.calculateStepPoints(t)).reverse()}}),It(pn.prototype,sn),dn=un.extend({createStrokeSegments:function(){var t=new Dt(this.options.closed),e=this.points();return t.process(e)},createStackSegments:function(){var t,e,i=this.strokeSegments(),n=[];for(t=i.length-1;t>=0;t--)e=i[t],n.push(new re.Segment(e.anchor(),e.controlOut(),e.controlIn()));return n}}),gn=cn.extend({createSegment:function(t,e,i,n){var s,o,r,a,h=this.options.isStacked,l=(e.line||{}).style;return h&&i>0&&n&&(r=this.seriesMissingValues(e),"gap"!==r?(o=n.linePoints,s=n):o=this._gapStackPoints(t,i,l)),new(a=l===He?pn:l===De?dn:un)(t,e,i,s,o)},reflow:function(t){var e,i,n,s,o=this;if(cn.fn.reflow.call(this,t),e=this._stackPoints)for(i=0;i<e.length;i++)n=e[i],s=o.categoryAxis.getSlot(n.categoryIx),n.reflow(s)},_gapStackPoints:function(t,e,i){var n,s,o,r,a,h,l,c,u=this,p=this.seriesPoints,d=t[0].categoryIx,g=t.length;for(d<0&&(d=0,g--),n=d+g,s=this.seriesOptions[0]._outOfRangeMinPoint?1:0,o=[],this._stackPoints=this._stackPoints||[],r=d;r<n;r++){a=r+s,h=e,l=void 0;do h--,l=p[h][a];while(h>0&&!l);l?(i!==He&&r>d&&!p[h][a-1]&&o.push(u._previousSegmentPoint(r,a,a-1,h)),o.push(l),i!==He&&r+1<n&&!p[h][a+1]&&o.push(u._previousSegmentPoint(r,a,a+1,h))):(c=u._createGapStackPoint(r),u._stackPoints.push(c),o.push(c))}return o},_previousSegmentPoint:function(t,e,i,n){for(var s,o=this.seriesPoints,r=n;r>0&&!s;)r--,s=o[r][i];return s?s=o[r][e]:(s=this._createGapStackPoint(t),this._stackPoints.push(s)),s},_createGapStackPoint:function(t){var e=this.pointOptions({},0),i=new en(0,e);return i.categoryIx=t,i.series={},i},seriesMissingValues:function(t){return t.missingValues||Le}}),fn=tt.extend({init:function(){this.axisRanges={}},update:function(t){var e,i,n,s=this.axisRanges;for(e in t)i=t[e],n=s[e],s[e]=n=n||{min:nt,max:st},n.min=Math.min(n.min,i.min),n.max=Math.max(n.max,i.max)},reset:function(t){this.axisRanges[t]=void 0},query:function(t){return this.axisRanges[t]}}),vn=bt.extend({init:function(t,e,i){bt.fn.init.call(this,e),this.textBox=new Ot(t,this.options,i),this.append(this.textBox)},createVisual:function(){this.textBox.options.noclip=this.options.noclip},reflow:function(t){var e=this.options,i=e.vertical,n=e.aboveAxis,s=this.children[0],o=s.options,r=s.box,a=s.options.padding,h=t;o.align=i?rt:lt,o.vAlign=i?at:rt,e.position===Gi?i?(o.vAlign=at,!n&&r.height()<t.height()&&(o.vAlign=ht)):o.align=n?gt:lt:e.position===rt?(o.vAlign=rt,o.align=rt):e.position===qi?i?o.vAlign=n?ht:at:o.align=n?lt:gt:e.position===Wi&&(i?h=n?new St(t.x1,t.y1-r.height(),t.x2,t.y1):new St(t.x1,t.y2,t.x2,t.y2+r.height()):(o.align=rt,h=n?new St(t.x2,t.y1,t.x2+r.width(),t.y2):new St(t.x1-r.width(),t.y1,t.x1,t.y2))),e.rotation||(i?a.left=a.right=(h.width()-s.contentBox.width())/2:a.top=a.bottom=(h.height()-s.contentBox.height())/2),s.reflow(h)},alignToClipBox:function(t){var e,i=this.options.vertical,n=i?dt:pt,s=n+"1",o=n+"2",r=this.children[0],a=this.parent.box;(a[s]<t[s]||t[o]<a[o])&&(e=r.paddingBox.clone(),e[s]=Math.max(a[s],t[s]),e[o]=Math.min(a[o],t[o]),this.reflow(e))}}),kt(vn,{position:Wi,margin:zt(3),padding:zt(4),color:ft,background:"",border:{width:1,color:""},aboveAxis:!0,vertical:!1,animation:{type:we,delay:_e},zIndex:2}),mn=6,xn=bt.extend({init:function(t,e){bt.fn.init.call(this),this.options=e,this.color=e.color||ct,this.aboveAxis=Et(this.options.aboveAxis,!0),this.value=t},render:function(){this._rendered||(this._rendered=!0,this.createLabel(),this.createNote(),this.errorBar&&this.append(this.errorBar))},createLabel:function(){var t,e,i,n=this.options,s=n.labels;s.visible&&(t=this.pointData(),e=Mt(s),i=e?e(t):this.formatValue(s.format),this.label=new vn(i,It({vertical:n.vertical},s),t),this.append(this.label))},formatValue:function(t){return this.owner.formatPointValue(this,t)},reflow:function(t){var e,i,n=this;if(this.render(),e=this.label,this.box=t,e&&(e.options.aboveAxis=this.aboveAxis,e.reflow(t)),this.note&&this.note.reflow(t),this.errorBars)for(i=0;i<this.errorBars.length;i++)n.errorBars[i].reflow(t)},createVisual:function(){var t,e=this,i=this,n=i.box,s=i.options,o=s.visual;this.visible!==!1&&(bt.fn.createVisual.call(this),o?(t=this.rectVisual=o({category:this.category,dataItem:this.dataItem,value:this.value,sender:this.getSender(),series:this.series,percentage:this.percentage,stackValue:this.stackValue,runningTotal:this.runningTotal,total:this.total,rect:n.toRect(),createVisual:function(){var t=new se;return e.createRect(t),t},options:s}),t&&this.visual.append(t)):n.width()>0&&n.height()>0&&this.createRect(this.visual))},createRect:function(t){var e,i,n,s,o,r=this.options,a=r.border,l=yt(a.opacity)?a.opacity:r.opacity,c=this.box.toRect();c.size.width=Math.round(c.size.width),e=this.rectVisual=ee.fromRect(c,{fill:{color:this.color,opacity:r.opacity},stroke:{color:this.getBorderColor(),width:a.width,opacity:l,dashType:a.dashType}}),i=this.box.width(),n=this.box.height(),s=r.vertical?i:n,s>mn&&(Ct(e),(i<1||n<1)&&(e.options.stroke.lineJoin="round")),t.append(e),h(r)&&(o=this.createGradientOverlay(e,{baseColor:this.color},It({end:r.vertical?void 0:[0,1]},r.overlay)),t.append(o))},createHighlight:function(t){var e=ee.fromRect(this.box.toRect(),t);return Ct(e)},highlightVisual:function(){return this.rectVisual},highlightVisualArgs:function(){return{options:this.options,rect:this.box.toRect(),visual:this.rectVisual}},getBorderColor:function(){var t=this.color,e=this.options.border,i=e._brightness||Ae,n=e.color;return yt(n)||(n=new oe(t).brightness(i).toHex()),n},tooltipAnchor:function(){var t,e,i,n,s=this,o=s.options,r=s.box,a=s.aboveAxis,h=this.owner.pane.clipBox()||r,l=lt,c=at;return o.vertical?(t=Math.min(r.x2,h.x2)+Se,a?e=Math.max(r.y1,h.y1):(e=Math.min(r.y2,h.y2),c=ht)):(i=Math.max(r.x1,h.x1),n=Math.min(r.x2,h.x2),o.isStacked?(c=ht,a?(l=gt,t=n):t=i,e=Math.max(r.y1,h.y1)-Se):(a?t=n+Se:(t=i-Se,l=gt),e=Math.max(r.y1,h.y1))),{point:new At(t,e),align:{horizontal:l,vertical:c}}},overlapsBox:function(t){return this.box.overlaps(t)},pointData:function(){return{dataItem:this.dataItem,category:this.category,value:this.value,percentage:this.percentage,stackValue:this.stackValue,runningTotal:this.runningTotal,total:this.total,series:this.series}}}),It(xn.prototype,Ji),It(xn.prototype,tn),xn.prototype.defaults={border:{width:1},vertical:!0,overlay:{gradient:"glass"},labels:{visible:!1,format:"{0}"},opacity:1,notes:{label:{}}},yn=bt.extend({init:function(t){bt.fn.init.call(this,t),this.forEach=t.rtl?c:l},reflow:function(t){var e=this.options,i=e.vertical,n=e.gap,s=e.spacing,o=this.children,r=o.length,a=i?dt:pt,h=r+n+s*(r-1),l=(i?t.height():t.width())/h,c=t[a+1]+l*(n/2);this.forEach(o,function(e,i){var n=(e.box||t).clone();n[a+1]=c,n[a+2]=c+l,e.reflow(n),i<r-1&&(c+=l*s),c+=l})}}),kt(yn,{vertical:!1,gap:0,spacing:0}),_n=bt.extend({reflow:function(t){var e,i,n,s=this,o=this.options.vertical?pt:dt,r=this.children,a=r.length,h=this.box=new St;for(e=0;e<a;e++)i=r[e],i.visible!==!1&&(n=i.box.clone(),n.snapTo(t,o),0===e&&(h=s.box=n.clone()),i.reflow(n),h.wrap(n))}}),kt(_n,{vertical:!0}),wn=$i.extend({render:function(){$i.fn.render.call(this),this.updateStackRange()},pointType:function(){return xn},clusterType:function(){return yn},stackType:function(){return _n},stackLimits:function(t,e){var i=$i.fn.stackLimits.call(this,t,e);return i},createPoint:function(t,e){var i,n,s,o,r,a,h=e.categoryIx,l=e.category,c=e.series,u=e.seriesIx,p=this,d=p.options,g=p.children,f=d.isStacked,v=this.pointValue(t),m=this.pointOptions(c,u),x=m.labels;return f&&x.position===Wi&&(x.position=Gi),m.isStacked=f,i=t.fields.color||c.color,v<0&&m.negativeColor&&(i=m.negativeColor),m=this.evalPointOptions(m,v,l,h,c,u),Tt(c.color)&&(i=m.color),n=this.pointType(),s=new n(v,m),s.color=i,o=g[h],o||(r=this.clusterType(),o=new r({vertical:d.invertAxes,gap:d.gap,spacing:d.spacing,rtl:!d.invertAxes&&(this.chartService||{}).rtl}),this.append(o)),f?(a=this.getStackWrap(c,o),a.append(s)):o.append(s),s},getStackWrap:function(t,e){var i,n,s,o=t.stack,r=o?o.group||o:o,a=e.children;if(typeof r===it.STRING){for(n=0;n<a.length;n++)if(a[n]._stackGroup===r){i=a[n];break}}else i=a[0];return i||(s=this.stackType(),i=new s({vertical:!this.options.invertAxes}),i._stackGroup=r,e.append(i)),i},categorySlot:function(t,e,i){var n,s,o=this.options,r=t.getSlot(e),a=i.startValue();return o.isStacked&&(n=i.getSlot(a,a,!0),s=o.invertAxes?pt:dt,r[s+1]=r[s+2]=n[s+1]),r},reflowCategories:function(t){var e,i=this.children,n=i.length;for(e=0;e<n;e++)i[e].reflow(t[e])},createAnimation:function(){this._setAnimationOptions(),$i.fn.createAnimation.call(this),a(this.options.series)&&this._setChildrenAnimation()},_setChildrenAnimation:function(){var t,e,i,n=this,s=this.points;for(t=0;t<s.length;t++)e=s[t],i=e.visual,i&&yt(i.options.zIndex)&&(e.options.animation=n.options.animation,e.createAnimation())},_setAnimationOptions:function(){var t,e,i=this.options,n=i.animation||{};i.isStacked?(e=this.seriesValueAxis(i.series[0]),t=e.getSlot(e.startValue())):t=this.categoryAxis.getSlot(0),n.origin=new ae(t.x1,t.y1),n.vertical=!i.invertAxes}}),kt(wn,{animation:{type:Ne}}),bn=bt.extend({init:function(t,e){bt.fn.init.call(this,e),this.value=t},reflow:function(t){var e,i,n=this,s=n.options,o=n.value,r=n.owner,a=r.seriesValueAxis(s),h=a.getSlot(o.open,o.close),l=a.getSlot(o.low,o.high);h.x1=l.x1=t.x1,h.x2=l.x2=t.x2,this.realBody=h,e=l.center().x,i=[],i.push([[e,l.y1],[e,h.y1]]),i.push([[e,h.y2],[e,l.y2]]),this.lines=i,this.box=l.clone().wrap(h),this._rendered||(this._rendered=!0,this.createNote()),this.reflowNote()},reflowNote:function(){this.note&&this.note.reflow(this.box)},createVisual:function(){bt.fn.createVisual.call(this),this._mainVisual=this.mainVisual(this.options),this.visual.append(this._mainVisual),this.createOverlay()},mainVisual:function(t){var e=new se;return this.createBody(e,t),this.createLines(e,t),e},createBody:function(t,e){var i=ee.fromRect(this.realBody.toRect(),{fill:{color:this.color,opacity:e.opacity},stroke:null});e.border.width>0&&i.options.set("stroke",{color:this.getBorderColor(),width:e.border.width,dashType:e.border.dashType,opacity:Et(e.border.opacity,e.opacity)}),Ct(i),t.append(i),h(e)&&t.append(this.createGradientOverlay(i,{baseColor:this.color},It({end:e.vertical?void 0:[0,1]},e.overlay)))},createLines:function(t,e){this.drawLines(t,e,this.lines,e.line)},drawLines:function(t,e,i,n){var s,o,r;if(i)for(s={stroke:{color:n.color||this.color,opacity:Et(n.opacity,e.opacity),width:n.width,dashType:n.dashType,lineCap:"butt"}},o=0;o<i.length;o++)r=ee.fromPoints(i[o],s),Ct(r),t.append(r)},getBorderColor:function(){var t=this.options.border,e=t.color;return yt(e)||(e=new oe(this.color).brightness(t._brightness).toHex()),e},createOverlay:function(){var t=ee.fromRect(this.box.toRect(),{fill:{color:ct,opacity:0},stroke:null});this.visual.append(t)},createHighlight:function(){var t,e=this.options.highlight,i=this.color;return this.color=e.color||this.color,t=this.mainVisual(It({},this.options,{line:{color:this.getBorderColor()}},e)),this.color=i,t},highlightVisual:function(){return this._mainVisual},highlightVisualArgs:function(){return{options:this.options,rect:this.box.toRect(),visual:this._mainVisual}},tooltipAnchor:function(){var t=this.box,e=this.owner.pane.clipBox()||t;return{point:new At(t.x2+Se,Math.max(t.y1,e.y1)+Se),align:{horizontal:lt,vertical:at}}},formatValue:function(t){return this.owner.formatPointValue(this,t)},overlapsBox:function(t){return this.box.overlaps(t)}}),kt(bn,{vertical:!0,border:{_brightness:.8},line:{width:2},overlay:{gradient:"glass"},tooltip:{format:"<table><tr><th colspan='2'>{4:d}</th></tr><tr><td>Open:</td><td>{0:C}</td></tr><tr><td>High:</td><td>{1:C}</td></tr><tr><td>Low:</td><td>{2:C}</td></tr><tr><td>Close:</td><td>{3:C}</td></tr></table>"},highlight:{opacity:1,border:{width:1,opacity:1},line:{width:1,opacity:1}},notes:{visible:!0,label:{}}}),It(bn.prototype,Ji),It(bn.prototype,tn),An=$i.extend({reflowCategories:function(t){var e,i=this.children,n=i.length;for(e=0;e<n;e++)i[e].reflow(t[e])},addValue:function(t,e){var i,n,s=e.categoryIx,o=e.category,r=e.series,a=e.seriesIx,h=this,l=h.children,c=h.options,p=t.valueFields,d=this.splitValue(p),g=u(d),f=r.data[s],v=this.categoryPoints[s];v||(this.categoryPoints[s]=v=[]),g&&(i=this.createPoint(t,e)),n=l[s],n||(n=new yn({vertical:c.invertAxes,gap:c.gap,spacing:c.spacing,rtl:!c.invertAxes&&(this.chartService||{}).rtl}),this.append(n)),i&&(this.updateRange(p,e),n.append(i),i.categoryIx=s,i.category=o,i.series=r,i.seriesIx=a,i.owner=this,i.dataItem=f,i.noteText=t.fields.noteText),this.points.push(i),v.push(i)},pointType:function(){return bn},createPoint:function(t,e){var i,n=e.categoryIx,s=e.category,o=e.series,r=e.seriesIx,a=this.pointType(),h=t.valueFields,l=It({},o),c=t.fields.color||o.color;return l=this.evalPointOptions(l,h,s,n,o,r),o.type===Xe&&h.open>h.close&&(c=t.fields.downColor||o.downColor||o.color),Tt(o.color)&&(c=l.color),l.vertical=!this.options.invertAxes,i=new a(h,l),i.color=c,i},splitValue:function(t){return[t.low,t.open,t.close,t.high]},updateRange:function(t,e){var i=e.series.axis,n=this.splitValue(t),s=this.valueAxisRanges[i];s=this.valueAxisRanges[i]=s||{min:nt,max:st},s=this.valueAxisRanges[i]={min:Math.min.apply(Math,n.concat([s.min])),max:Math.max.apply(Math,n.concat([s.max]))}},formatPointValue:function(t,e){var i=t.value;return this.chartService.format.auto(e,i.open,i.high,i.low,i.close,t.category)},animationPoints:function(){return this.points}}),It(An.prototype,ln),Sn=bn.extend({init:function(t,e){bn.fn.init.call(this,t,e),this.createNote()},reflow:function(t){var e,i,n,s,o=this,r=o.options,a=o.value,h=o.owner,l=h.seriesValueAxis(r);this.boxSlot=i=l.getSlot(a.q1,a.q3),this.realBody=i,this.reflowBoxSlot(t),this.whiskerSlot=e=l.getSlot(a.lower,a.upper),this.reflowWhiskerSlot(t),n=l.getSlot(a.median),a.mean&&(s=l.getSlot(a.mean),this.meanPoints=this.calcMeanPoints(t,s)),this.whiskerPoints=this.calcWhiskerPoints(i,e),this.medianPoints=this.calcMedianPoints(t,n),this.box=e.clone().wrap(i),this.reflowNote()},reflowBoxSlot:function(t){this.boxSlot.x1=t.x1,this.boxSlot.x2=t.x2},reflowWhiskerSlot:function(t){this.whiskerSlot.x1=t.x1,this.whiskerSlot.x2=t.x2},calcMeanPoints:function(t,e){return[[[t.x1,e.y1],[t.x2,e.y1]]]},calcWhiskerPoints:function(t,e){var i=e.center().x;return[[[i-5,e.y1],[i+5,e.y1],[i,e.y1],[i,t.y1]],[[i-5,e.y2],[i+5,e.y2],[i,e.y2],[i,t.y2]]]},calcMedianPoints:function(t,e){return[[[t.x1,e.y1],[t.x2,e.y1]]]},renderOutliers:function(t){var e,i,n,s,o=this,r=this.value,a=r.outliers||[],h=3*Math.abs(r.q3-r.q1),l=[],c=t.markers||{};for(e=0;e<a.length;e++)i=a[e],c=i<r.q3+h&&i>r.q1-h?t.outliers:t.extremes,n=It({},c.border),yt(n.color)||(n.color=yt(o.color)?o.color:new oe(c.background).brightness(Ae).toHex()),s=new Bt({type:c.type,width:c.size,height:c.size,rotation:c.rotation,background:c.background,border:n,opacity:c.opacity}),s.value=i,l.push(s);return this.reflowOutliers(l),l},reflowOutliers:function(t){var e,i,n,s=this,o=this.owner.seriesValueAxis(this.options),r=this.box.center();for(e=0;e<t.length;e++)i=t[e].value,n=o.getSlot(i),s.options.vertical?n.move(r.x):n.move(void 0,r.y),s.box=s.box.wrap(n),t[e].reflow(n)},mainVisual:function(t){var e,i,n=bn.fn.mainVisual.call(this,t),s=this.renderOutliers(t);for(e=0;e<s.length;e++)i=s[e].getElement(),i&&n.append(i);return n},createLines:function(t,e){this.drawLines(t,e,this.whiskerPoints,e.whiskers),this.drawLines(t,e,this.medianPoints,e.median),this.drawLines(t,e,this.meanPoints,e.mean)},getBorderColor:function(){return(this.options.border||{}).color?this.options.border.color:this.color?this.color:bn.fn.getBorderColor.call(this)}}),kt(Sn,{border:{_brightness:.8},line:{width:2},median:{color:"#f6f6f6"},mean:{width:2,dashType:"dash",color:"#f6f6f6"},overlay:{gradient:"glass"},tooltip:{format:"<table><tr><th colspan='2'>{6:d}</th></tr><tr><td>Lower:</td><td>{0:C}</td></tr><tr><td>Q1:</td><td>{1:C}</td></tr><tr><td>Median:</td><td>{2:C}</td></tr><tr><td>Mean:</td><td>{5:C}</td></tr><tr><td>Q3:</td><td>{3:C}</td></tr><tr><td>Upper:</td><td>{4:C}</td></tr></table>"},highlight:{opacity:1,border:{width:1,opacity:1},line:{width:1,opacity:1}},notes:{visible:!0,label:{}},outliers:{visible:!0,size:Ve,type:it.CROSS,background:ct,border:{width:2,opacity:1},opacity:0},extremes:{visible:!0,size:Ve,type:ut,background:ct,border:{width:2,opacity:1},opacity:0}}),It(Sn.prototype,Ji),Cn=Sn.extend({reflowBoxSlot:function(t){this.boxSlot.y1=t.y1,this.boxSlot.y2=t.y2},reflowWhiskerSlot:function(t){this.whiskerSlot.y1=t.y1,this.whiskerSlot.y2=t.y2},calcMeanPoints:function(t,e){return[[[e.x1,t.y1],[e.x1,t.y2]]]},calcWhiskerPoints:function(t,e){var i=e.center().y;return[[[e.x1,i-5],[e.x1,i+5],[e.x1,i],[t.x1,i]],[[e.x2,i-5],[e.x2,i+5],[e.x2,i],[t.x2,i]]]},calcMedianPoints:function(t,e){return[[[e.x1,t.y1],[e.x1,t.y2]]]}}),kn=An.extend({addValue:function(t,e){var i,n,s=e.categoryIx,o=e.category,r=e.series,a=e.seriesIx,h=this,l=h.children,c=h.options,p=t.valueFields,d=this.splitValue(p),g=u(d),f=r.data[s],v=this.categoryPoints[s];v||(this.categoryPoints[s]=v=[]),g&&(i=this.createPoint(t,e)),n=l[s],n||(n=new yn({vertical:c.invertAxes,gap:c.gap,spacing:c.spacing,rtl:!c.invertAxes&&(this.chartService||{}).rtl}),this.append(n)),i&&(this.updateRange(p,e),n.append(i),i.categoryIx=s,i.category=o,i.series=r,i.seriesIx=a,i.owner=this,i.dataItem=f),this.points.push(i),v.push(i)},pointType:function(){return this.options.invertAxes?Cn:Sn},splitValue:function(t){return[t.lower,t.q1,t.median,t.q3,t.upper]},updateRange:function(t,e){var i=e.series.axis,n=this.valueAxisRanges[i],s=this.splitValue(t).concat(this.filterOutliers(t.outliers));yt(t.mean)&&(s=s.concat(t.mean)),n=this.valueAxisRanges[i]=n||{min:nt,max:st},n=this.valueAxisRanges[i]={min:Math.min.apply(Math,s.concat([n.min])),max:Math.max.apply(Math,s.concat([n.max]))}},formatPointValue:function(t,e){var i=t.value;return this.chartService.format.auto(e,i.lower,i.q1,i.median,i.q3,i.upper,i.mean,t.category)},filterOutliers:function(t){var e,i,n=(t||[]).length,s=[];for(e=0;e<n;e++)i=t[e],yt(i)&&null!==i&&s.push(i);return s}}),Pn=ji.extend({getAxis:function(){var t=this.chart.seriesAxes(this.series),e=this.isVertical?t.y:t.x;return e}}),Tn=bt.extend({init:function(t,e){bt.fn.init.call(this,e),this.plotArea=t,this.chartService=t.chartService,this._initFields(),this.render()},_initFields:function(){this.xAxisRanges={},this.yAxisRanges={},this.points=[],this.seriesPoints=[],this.seriesOptions=[],this._evalSeries=[]},render:function(){this.traverseDataPoints(this.addValue.bind(this))},addErrorBar:function(t,e,i){var n,s=t.value[e],o=e+"Value",r=e+"ErrorLow",a=e+"ErrorHigh",h=i.seriesIx,l=i.series,c=t.options.errorBars,u=i[r],p=i[a];et(s)&&(et(u)&&et(p)&&(n={low:u,high:p}),c&&yt(c[o])&&(this.seriesErrorRanges=this.seriesErrorRanges||{x:[],y:[]},this.seriesErrorRanges[e][h]=this.seriesErrorRanges[e][h]||new xe(c[o],l,e),n=this.seriesErrorRanges[e][h].getErrorRange(s,c[o])),n&&this.addPointErrorBar(n,t,e))},addPointErrorBar:function(t,e,i){var n,s=t.low,o=t.high,r=e.series,a=e.options.errorBars,h=i===dt,l={};e[i+"Low"]=s,e[i+"High"]=o,e.errorBars=e.errorBars||[],n=new Pn(s,o,h,this,r,a),e.errorBars.push(n),e.append(n),l[i]=s,this.updateRange(l,r),l[i]=o,this.updateRange(l,r)},addValue:function(e,i){var n,s=e.x,o=e.y,r=i.seriesIx,a=this.options.series[r],h=this.seriesMissingValues(a),l=this.seriesPoints[r],c=e;p(s)&&p(o)||(c=this.createMissingValue(c,h)),c&&(n=this.createPoint(c,i),n&&(t.extend(n,i),this.addErrorBar(n,pt,i),this.addErrorBar(n,dt,i)),this.updateRange(c,i.series)),this.points.push(n),l.push(n)},seriesMissingValues:function(t){return t.missingValues},createMissingValue:function(){},updateRange:function(t,e){var i=this.chartService.intl,n=e.xAxis,s=e.yAxis,o=t.x,r=t.y,a=this.xAxisRanges[n],h=this.yAxisRanges[s];p(o)&&(a=this.xAxisRanges[n]=a||{min:nt,max:st},Ft(o)&&(o=Nt(i,o)),a.min=Math.min(a.min,o),a.max=Math.max(a.max,o)),p(r)&&(h=this.yAxisRanges[s]=h||{min:nt,max:st},Ft(r)&&(r=Nt(i,r)),h.min=Math.min(h.min,r),h.max=Math.max(h.max,r))},evalPointOptions:function(t,e,i){var n,o=i.series,r=i.seriesIx,a={defaults:o._defaults,excluded:["data","tooltip","content","template","visual","toggle","_outOfRangeMinPoint","_outOfRangeMaxPoint"]},h=this._evalSeries[r];return yt(h)||(this._evalSeries[r]=h=s(t,{},a,!0)),n=t,h&&(n=It({},t),s(n,{value:e,series:o,dataItem:i.dataItem},a)),n},pointType:function(){return en},pointOptions:function(t,e){var i,n=this.seriesOptions[e];return n||(i=this.pointType().prototype.defaults,this.seriesOptions[e]=n=It({},i,{markers:{opacity:t.opacity},tooltip:{format:this.options.tooltip.format},labels:{format:this.options.labels.format}},t)),n},createPoint:function(t,e){var i,n=e.series,s=this.pointOptions(n,e.seriesIx),o=e.color||n.color;return s=this.evalPointOptions(s,t,e),Tt(n.color)&&(o=s.color),i=new en(t,s),i.color=o,this.append(i),i},seriesAxes:function(t){var e=t.xAxis,i=t.yAxis,n=this.plotArea,s=e?n.namedXAxes[e]:n.axisX,o=i?n.namedYAxes[i]:n.axisY;if(!s)throw Error("Unable to locate X axis with name "+e);if(!o)throw Error("Unable to locate Y axis with name "+i);return{x:s,y:o}},reflow:function(t){var e=this,i=this.points,n=!this.options.clip,s=0;this.traverseDataPoints(function(t,o){var r,a=i[s++],h=e.seriesAxes(o.series),l=h.x.getSlot(t.x,t.x,n),c=h.y.getSlot(t.y,t.y,n);a&&(l&&c?(r=e.pointSlot(l,c),a.reflow(r)):a.visible=!1)}),this.box=t},pointSlot:function(t,e){return new St(t.x1,e.y1,t.x2,e.y2)},traverseDataPoints:function(t){var e,i,n,s,o,r,a,h=this,l=this,c=l.options.series,u=l.seriesPoints;for(e=0;e<c.length;e++)for(i=c[e],n=u[e],n||(u[e]=[]),s=0;s<i.data.length;s++)o=h._bindPoint(i,e,s),r=o.valueFields,a=o.fields,t(r,It({pointIx:s,series:i,seriesIx:e,dataItem:i.data[s],owner:h},a))},formatPointValue:function(t,e){var i=t.value;return this.chartService.format.auto(e,i.x,i.y)},animationPoints:function(){var t,e=this.points,i=[];for(t=0;t<e.length;t++)i.push((e[t]||{}).marker);return i}}),kt(Tn,{series:[],tooltip:{format:"{0}, {1}"},labels:{format:"{0}, {1}"},clip:!0}),It(Tn.prototype,ln,{_bindPoint:$i.prototype._bindPoint}),En=en.extend({init:function(t,e){en.fn.init.call(this,t,e),this.category=t.category},createHighlight:function(){var t=this.options.highlight,e=t.border,i=this.options.markers,n=this.box.center(),s=(i.size+i.border.width+e.width)/2,o=new se,r=new te.Circle(new re.Circle([n.x,n.y+s/5+e.width/2],s+e.width/2),{stroke:{color:"none"},fill:this.createGradient({gradient:"bubbleShadow",color:i.background,stops:[{offset:0,color:i.background,opacity:.3},{offset:1,color:i.background,opacity:0}]})}),a=new te.Circle(new re.Circle([n.x,n.y],s),{stroke:{color:e.color||new oe(i.background).brightness(Ae).toHex(),width:e.width,opacity:e.opacity},fill:{color:i.background,opacity:t.opacity}});return o.append(r,a),o}}),En.prototype.defaults=It({},En.prototype.defaults,{labels:{position:rt},highlight:{opacity:1,border:{color:"#fff",width:2,opacity:1}}}),En.prototype.defaults.highlight.zIndex=void 0,Rn=Tn.extend({_initFields:function(){this._maxSize=st,Tn.fn._initFields.call(this)},addValue:function(t,e){null!==t.size&&(t.size>0||t.size<0&&e.series.negativeValues.visible)?(this._maxSize=Math.max(this._maxSize,Math.abs(t.size)),Tn.fn.addValue.call(this,t,e)):(this.points.push(null),this.seriesPoints[e.seriesIx].push(null))},reflow:function(t){this.updateBubblesSize(t),Tn.fn.reflow.call(this,t)},pointType:function(){return En},createPoint:function(t,e){var i,n,s=e.series,o=s.data.length,r=e.pointIx*(_e/o),a={delay:r,duration:_e-r,type:qe},h=e.color||s.color;return t.size<0&&s.negativeValues.visible&&(h=Et(s.negativeValues.color,h)),i=It({labels:{animation:{delay:r,duration:_e-r}}},this.pointOptions(s,e.seriesIx),{markers:{type:ut,border:s.border,opacity:s.opacity,animation:a}}),i=this.evalPointOptions(i,t,e),Tt(s.color)&&(h=i.color),i.markers.background=h,n=new En(t,i),n.color=h,this.append(n),n},updateBubblesSize:function(t){var e,i,n,s,o,r,a,h,l,c,u,p,d,g,f,v,m,x=this,y=this,_=y.options.series,w=Math.min(t.width(),t.height());for(e=0;e<_.length;e++)for(i=_[e],n=x.seriesPoints[e],s=i.minSize||Math.max(.02*w,10),o=i.maxSize||.2*w,r=s/2,a=o/2,h=Math.PI*r*r,l=Math.PI*a*a,c=l-h,u=c/x._maxSize,p=0;p<n.length;p++)d=n[p],d&&(g=Math.abs(d.value.size)*u,f=Math.sqrt((h+g)/Math.PI),v=Et(d.options.zIndex,0),m=v+(1-f/a),It(d.options,{zIndex:m,markers:{size:2*f,zIndex:m},labels:{zIndex:m+1}}))},formatPointValue:function(t,e){var i=t.value;return this.chartService.format.auto(e,i.x,i.y,i.size,t.category)},createAnimation:function(){},createVisual:function(){}}),kt(Rn,{tooltip:{format:"{3}"},labels:{format:"{3}"}}),In=Bt.extend({}),It(In.prototype,Ji),Vn=bt.extend({init:function(t,e){bt.fn.init.call(this,e),this.aboveAxis=this.options.aboveAxis,this.color=e.color||ct,this.value=t},render:function(){var t=this.options;this._rendered||(this._rendered=!0,yt(this.value.target)&&(this.target=new In({type:t.target.shape,background:t.target.color||this.color,opacity:t.opacity,zIndex:t.zIndex,border:t.target.border,vAlign:at,align:gt}),this.target.value=this.value,this.target.dataItem=this.dataItem,this.target.series=this.series,this.append(this.target)),this.createNote())},reflow:function(t){var e,i,n,s,o,r,a,h,l,c,u;this.render(),e=this,i=e.options,n=e.target,s=e.owner,o=i.invertAxes,r=s.seriesValueAxis(this.options),a=s.categorySlot(s.categoryAxis,i.categoryIx,r),h=r.getSlot(this.value.target),l=o?h:a,c=o?a:h,n&&(u=new St(l.x1,c.y1,l.x2,c.y2),n.options.height=o?u.height():i.target.line.width,n.options.width=o?i.target.line.width:u.width(),n.reflow(u)),this.note&&this.note.reflow(t),this.box=t},createVisual:function(){var t,e;bt.fn.createVisual.call(this),t=this.options,e=ee.fromRect(this.box.toRect(),{fill:{color:this.color,opacity:t.opacity},stroke:null}),t.border.width>0&&e.options.set("stroke",{color:t.border.color||this.color,width:t.border.width,dashType:t.border.dashType,opacity:Et(t.border.opacity,t.opacity)}),this.bodyVisual=e,Ct(e),this.visual.append(e)},createAnimation:function(){this.bodyVisual&&(this.animation=ie.create(this.bodyVisual,this.options.animation))},createHighlight:function(t){return ee.fromRect(this.box.toRect(),t)},highlightVisual:function(){return this.bodyVisual},highlightVisualArgs:function(){return{rect:this.box.toRect(),visual:this.bodyVisual,options:this.options}},formatValue:function(t){return this.owner.formatPointValue(this,t)}}),Vn.prototype.tooltipAnchor=xn.prototype.tooltipAnchor,kt(Vn,{border:{width:1},vertical:!1,opacity:1,target:{shape:"",border:{width:0,color:"green"},line:{width:2}},tooltip:{format:"Current: {0}<br />Target: {1}"}}),It(Vn.prototype,Ji),It(Vn.prototype,tn),Ln=$i.extend({init:function(t,e){d(e),$i.fn.init.call(this,t,e)},reflowCategories:function(t){var e,i=this.children,n=i.length;for(e=0;e<n;e++)i[e].reflow(t[e])},plotRange:function(t){var e=t.series,i=this.seriesValueAxis(e),n=this.categoryAxisCrossingValue(i);return[n,t.value.current||n]},createPoint:function(t,e){var i,n,s=e.categoryIx,o=e.category,r=e.series,a=e.seriesIx,h=this,l=h.options,c=h.children,u=t.valueFields,p=It({vertical:!l.invertAxes,overlay:r.overlay,categoryIx:s,invertAxes:l.invertAxes},r),d=t.fields.color||r.color;return p=this.evalPointOptions(p,u,o,s,r,a),Tt(r.color)&&(d=p.color),i=new Vn(u,p),
i.color=d,n=c[s],n||(n=new yn({vertical:l.invertAxes,gap:l.gap,spacing:l.spacing,rtl:!l.invertAxes&&(this.chartService||{}).rtl}),this.append(n)),n.append(i),i},updateRange:function(t,e){var i=t.current,n=t.target,s=e.series.axis,o=this.valueAxisRanges[s];yt(i)&&!isNaN(i)&&yt(n&&!isNaN(n))&&(o=this.valueAxisRanges[s]=o||{min:nt,max:st},o.min=Math.min(o.min,i,n),o.max=Math.max(o.max,i,n))},formatPointValue:function(t,e){return this.chartService.format.auto(e,t.value.current,t.value.target)},pointValue:function(t){return t.valueFields.current},aboveAxis:function(t){var e=t.value.current;return e>0},createAnimation:function(){var t,e,i=this,n=this.points;for(this._setAnimationOptions(),t=0;t<n.length;t++)e=n[t],e.options.animation=i.options.animation,e.createAnimation()}}),Ln.prototype._setAnimationOptions=wn.prototype._setAnimationOptions,kt(Ln,{animation:{type:Ne}}),Mn=tt.extend({init:function(t,e){this.chartService=t,this.options=It({},this.options,e)},getStyle:function(t,e){var i,n,s=t.background,o=t.border.color;return e&&(i=e.color||e.options.color,s=Et(s,i),o=Et(o,i)),n=zt(t.padding||{},"auto"),{backgroundColor:s,borderColor:o,font:t.font,color:t.color,opacity:t.opacity,borderWidth:Gt(t.border.width),paddingTop:Gt(n.top),paddingBottom:Gt(n.bottom),paddingLeft:Gt(n.left),paddingRight:Gt(n.right)}},show:function(t,e,i){t.format=e.format;var n=this.getStyle(e,i);t.style=n,!yt(e.color)&&new oe(n.backgroundColor).percBrightness()>180&&(t.className="k-chart-tooltip-inverse"),this.chartService.notify(zi,t),this.visible=!0},hide:function(){this.chartService&&this.chartService.notify(Di),this.visible=!1},destroy:function(){delete this.chartService}}),kt(Mn,{border:{width:1},opacity:1}),On=Mn.extend({init:function(t,e,i){Mn.fn.init.call(this,t,i),this.crosshair=e,this.formatService=t.format,this.initAxisName()},initAxisName:function(){var t,e=this.crosshair.axis,i=e.plotArea;t=i.categoryAxis?e.getCategory?"categoryAxis":"valueAxis":e.options.vertical?"yAxis":"xAxis",this.axisName=t},showAt:function(t){var e=this,i=e.crosshair.axis,n=e.options,s=i[n.stickyMode?"getCategory":"getValue"](t),o=s;n.format?o=this.formatService.auto(n.format,s):i.options.type===vt&&(o=this.formatService.auto(i.options.labels.dateFormats[i.options.baseUnit],s)),this.show({point:t,anchor:this.getAnchor(),crosshair:this.crosshair,value:o,axisName:this.axisName,axisIndex:this.crosshair.axis.axisIndex},this.options)},hide:function(){this.chartService.notify(Di,{crosshair:this.crosshair,axisName:this.axisName,axisIndex:this.crosshair.axis.axisIndex})},getAnchor:function(){var t,e,i,n=this,s=n.crosshair,o=n.options,r=o.position,a=o.padding,h=!s.axis.options.vertical,l=s.line.bbox();return h?(t=rt,r===ht?(e=at,i=l.bottomLeft().translate(0,a)):(e=ht,i=l.topLeft().translate(0,-a))):(e=rt,r===lt?(t=gt,i=l.topLeft().translate(-a,0)):(t=lt,i=l.topRight().translate(a,0))),{point:i,align:{horizontal:t,vertical:e}}}}),kt(On,{padding:10}),Bn=bt.extend({init:function(t,e,i){bt.fn.init.call(this,i),this.axis=e,this.stickyMode=e instanceof qt;var n=this.options.tooltip;n.visible&&(this.tooltip=new On(t,this,It({},n,{stickyMode:this.stickyMode})))},showAt:function(t){this.point=t,this.moveLine(),this.line.visible(!0),this.tooltip&&this.tooltip.showAt(t)},hide:function(){this.line.visible(!1),this.tooltip&&this.tooltip.hide()},moveLine:function(){var t,e,i=this,n=i.axis,s=i.point,o=n.options.vertical,r=this.getBox(),a=o?dt:pt,h=new ae(r.x1,r.y1);t=o?new ae(r.x2,r.y1):new ae(r.x1,r.y2),s&&(this.stickyMode?(e=n.getSlot(n.pointCategoryIndex(s)),h[a]=t[a]=e.center()[a]):h[a]=t[a]=s[a]),this.box=r,this.line.moveTo(h).lineTo(t)},getBox:function(){var t,e,i,n=this.axis,s=n.pane.axes,o=s.length,r=n.options.vertical,a=n.lineBox().clone(),h=r?pt:dt;for(e=0;e<o;e++)i=s[e],i.options.vertical!==r&&(t?t.wrap(i.lineBox()):t=i.lineBox().clone());return a[h+1]=t[h+1],a[h+2]=t[h+2],a},createVisual:function(){bt.fn.createVisual.call(this);var t=this.options;this.line=new ee({stroke:{color:t.color,width:t.width,opacity:t.opacity,dashType:t.dashType},visible:!1}),this.moveLine(),this.visual.append(this.line)},destroy:function(){this.tooltip&&this.tooltip.destroy(),bt.fn.destroy.call(this)}}),kt(Bn,{color:ft,width:2,zIndex:-1,tooltip:{visible:!1}}),zn=bt.extend({init:function(t,e){bt.fn.init.call(this,t),this.pane=e},shouldClip:function(){var t,e=this.children,i=e.length;for(t=0;t<i;t++)if(e[t].options.clip===!0)return!0;return!1},_clipBox:function(){return this.pane.chartsBox()},createVisual:function(){var t,e,i;this.visual=new se({zIndex:0}),this.shouldClip()&&(t=this.clipBox=this._clipBox(),e=t.toRect(),i=ee.fromRect(e),Ct(i),this.visual.clip(i),this.unclipLabels())},stackRoot:function(){return this},unclipLabels:function(){var t,e,i,n,s,o,r,a=this,h=a.children,l=a.clipBox;for(t=0;t<h.length;t++)for(e=h[t].points||{},i=e.length,n=0;n<i;n++)s=e[n],s&&s.visible!==!1&&s.overlapsBox&&s.overlapsBox(l)&&(s.unclipElements?s.unclipElements():(o=s.label,r=s.note,o&&o.options.visible&&(o.alignToClipBox&&o.alignToClipBox(l),o.options.noclip=!0),r&&r.options.visible&&(r.options.noclip=!0)))},destroy:function(){bt.fn.destroy.call(this),delete this.parent}}),zn.prototype.isStackRoot=!0,Dn=Wt.extend({init:function(t){Wt.fn.init.call(this,t),this.id=g(),this.createTitle(),this.content=new bt,this.chartContainer=new zn({},this),this.append(this.content),this.axes=[],this.charts=[]},createTitle:function(){var t=this.options.title;Rt(t)&&(t=It({},t,{align:t.position,position:at})),this.title=J.Title.buildTitle(t,this,Dn.prototype.options.title)},appendAxis:function(t){this.content.append(t),this.axes.push(t),t.pane=this},appendAxisAt:function(t,e){this.content.append(t),this.axes.splice(e,0,t),t.pane=this},appendChart:function(t){this.chartContainer.parent!==this.content&&this.content.append(this.chartContainer),this.charts.push(t),this.chartContainer.append(t),t.pane=this},empty:function(){var t,e,i=this,n=this.parent;if(n){for(t=0;t<this.axes.length;t++)n.removeAxis(i.axes[t]);for(e=0;e<this.charts.length;e++)n.removeChart(i.charts[e])}this.axes=[],this.charts=[],this.content.destroy(),this.content.children=[],this.chartContainer.children=[]},reflow:function(t){var e;Vt(this.children)===this.content&&(e=this.children.pop()),Wt.fn.reflow.call(this,t),e&&this.children.push(e),this.title&&(this.contentBox.y1+=this.title.box.height())},visualStyle:function(){var t=Wt.fn.visualStyle.call(this);return t.zIndex=-10,t},renderComplete:function(){this.options.visible&&this.createGridLines()},stackRoot:function(){return this},clipRoot:function(){return this},createGridLines:function(){var t,e,i,n,s,o,r=this.axes,a=r.concat(this.parent.axes),h=[],l=[];for(t=0;t<r.length;t++)for(e=r[t],i=e.options.vertical,n=i?h:l,s=0;s<a.length;s++)0===n.length&&(o=a[s],i!==o.options.vertical&&Ht(n,e.createGridLines(o)))},refresh:function(){this.visual.clear(),this.content.parent=null,this.content.createGradient=this.createGradient.bind(this),this.content.renderVisual(),this.content.parent=this,this.title&&this.visual.append(this.title.visual),this.visual.append(this.content.visual),this.renderComplete(),this.notifyRender()},chartsBox:function(){var t,e,i,n,s,o,r,a,h,l=this.axes,c=l.length,u=new St;for(t=0;t<c;t++)e=l[t],i=e.options.vertical?dt:pt,n=e.lineBox(),u[i+1]=n[i+1],u[i+2]=n[i+2];if(0===u.x2)for(s=this.parent.axes,o=s.length,r=0;r<o;r++)a=s[r],a.options.vertical||(h=a.lineBox(),u.x1=h.x1,u.x2=h.x2);return u},clipBox:function(){return this.chartContainer.clipBox},notifyRender:function(){var t=this.getService();t&&t.notify(Hi,{pane:new ce(this),index:this.paneIndex,name:this.options.name})}}),Hn=1,Dn.prototype.isStackRoot=!0,kt(Dn,{zIndex:-1,shrinkToFit:!0,title:{align:lt},visible:!0}),Fn=/area/i,Nn=bt.extend({init:function(t,e,i){bt.fn.init.call(this,e),this.initFields(t,e),this.series=t,this.initSeries(),this.charts=[],this.options.legend.items=[],this.axes=[],this.crosshairs=[],this.chartService=i,this.originalOptions=e,this.createPanes(),this.render(),this.createCrosshairs()},initFields:function(){},initSeries:function(){var t,e=this.series;for(t=0;t<e.length;t++)e[t].index=t},createPanes:function(){function t(t,e){Ft(t.title)&&(t.title={text:t.title}),t.title=It({},e.title,t.title)}var e,i,n,s=this,o={title:{color:(this.options.title||{}).color}},r=[],a=this.options.panes||[],h=Math.max(a.length,1);for(e=0;e<h;e++)i=a[e]||{},t(i,o),n=new Dn(i),n.paneIndex=e,r.push(n),s.append(n);this.panes=r},createCrosshairs:function(t){var e,i,n,s,o,r=this;for(void 0===t&&(t=this.panes),e=0;e<t.length;e++)for(i=t[e],n=0;n<i.axes.length;n++)s=i.axes[n],s.options.crosshair&&s.options.crosshair.visible&&(o=new Bn(r.chartService,s,s.options.crosshair),r.crosshairs.push(o),i.content.append(o))},removeCrosshairs:function(t){var e,i,n=this.crosshairs,s=t.axes;for(e=n.length-1;e>=0;e--)for(i=0;i<s.length;i++)if(n[e].axis===s[i]){n.splice(e,1);break}},hideCrosshairs:function(){var t,e=this.crosshairs;for(t=0;t<e.length;t++)e[t].hide()},findPane:function(t){var e,i,n=this.panes;for(i=0;i<n.length;i++)if(n[i].options.name===t){e=n[i];break}return e||n[0]},findPointPane:function(t){var e,i,n=this.panes;for(i=0;i<n.length;i++)if(n[i].box.containsPoint(t)){e=n[i];break}return e},appendAxis:function(t){var e=this.findPane(t.options.pane);e.appendAxis(t),this.axes.push(t),t.plotArea=this},removeAxis:function(t){var e,i,n=this,s=[];for(e=0;e<this.axes.length;e++)i=n.axes[e],t!==i?s.push(i):i.destroy();this.axes=s},appendChart:function(t,e){this.charts.push(t),e?e.appendChart(t):this.append(t)},removeChart:function(t){var e,i,n=this,s=[];for(e=0;e<this.charts.length;e++)i=n.charts[e],i!==t?s.push(i):i.destroy();this.charts=s},addToLegend:function(t){var e,i,n,s,o,r,a,h,l,c=t.length,u=this.options.legend,d=u.labels||{},g=u.inactiveItems||{},f=g.labels||{},v=[];for(e=0;e<c;e++)i=t[e],n=i.visible!==!1,i.visibleInLegend!==!1&&(s=i.name,o=n?Mt(d):Mt(f)||Mt(d),o&&(s=o({text:p(s)?s:"",series:i})),r=i._defaults,a=i.color,Tt(a)&&r&&(a=r.color),h=void 0,l=void 0,n?(h={},l=a):(h={color:f.color,font:f.font},l=g.markers.color),p(s)&&""!==s&&v.push({text:s,labels:h,markerColor:l,series:i,active:n}));Ht(u.items,v)},groupAxes:function(t){var e,i,n,s,o=[],r=[];for(e=0;e<t.length;e++)for(i=t[e].axes,n=0;n<i.length;n++)s=i[n],s.options.vertical?r.push(s):o.push(s);return{x:o,y:r,any:o.concat(r)}},groupSeriesByPane:function(){var t,e,i,n=this,s=this.series,o={};for(t=0;t<s.length;t++)e=s[t],i=n.seriesPaneName(e),o[i]?o[i].push(e):o[i]=[e];return o},filterVisibleSeries:function(t){var e,i,n=[];for(e=0;e<t.length;e++)i=t[e],i.visible!==!1&&n.push(i);return n},reflow:function(t){var e=this.options.plotArea,i=this.panes,n=zt(e.margin);this.box=t.clone().unpad(n),this.reflowPanes(),this.detachLabels(),this.reflowAxes(i),this.reflowCharts(i)},redraw:function(t){var e,i,n,s=this,o=[].concat(t);for(this.initSeries(),e=this.getRoot(),e&&e.cleanGradients(),i=0;i<o.length;i++)s.removeCrosshairs(o[i]),o[i].empty();for(this.render(o),this.detachLabels(),this.reflowAxes(this.panes),this.reflowCharts(o),this.createCrosshairs(o),n=0;n<o.length;n++)o[n].refresh()},axisCrossingValues:function(t,e){var i,n=t.options,s=[].concat(n.axisCrossingValues||n.axisCrossingValue),o=e.length-s.length,r=s[0]||0;for(i=0;i<o;i++)s.push(r);return s},alignAxisTo:function(t,e,i,n){var s=t.getSlot(i,i,!0),o=t.options.reverse?2:1,r=e.getSlot(n,n,!0),a=e.options.reverse?2:1,h=t.box.translate(r[pt+a]-s[pt+o],r[dt+a]-s[dt+o]);t.pane!==e.pane&&h.translate(0,t.pane.box.y1-e.pane.box.y1),t.reflow(h)},alignAxes:function(t,e){var i,n,s,o,r,a,h,l,c,u,p,d,g,f,v=this,m=t[0],x=e[0],y=this.axisCrossingValues(m,e),_=this.axisCrossingValues(x,t),w={},b={},A={},S={};for(i=0;i<e.length;i++)n=e[i],s=n.pane,o=s.id,r=n.options.visible!==!1,a=T(t,s)||m,h=y,a!==m&&(h=v.axisCrossingValues(a,e)),v.alignAxisTo(n,a,_[i],h[i]),n.options._overlap||(Xt(n.lineBox().x1)===Xt(a.lineBox().x1)&&(w[o]&&n.reflow(n.box.alignTo(w[o].box,lt).translate(-n.options.margin,0)),r&&(w[o]=n)),Xt(n.lineBox().x2)===Xt(a.lineBox().x2)&&(n._mirrored||(n.options.labels.mirror=!n.options.labels.mirror,n._mirrored=!0),v.alignAxisTo(n,a,_[i],h[i]),b[o]&&n.reflow(n.box.alignTo(b[o].box,gt).translate(n.options.margin,0)),r&&(b[o]=n)),0!==i&&x.pane===n.pane&&(n.alignTo(x),n.reflow(n.box)));for(l=0;l<t.length;l++)c=t[l],u=c.pane,p=u.id,d=c.options.visible!==!1,g=T(e,u)||x,f=_,g!==x&&(f=v.axisCrossingValues(g,t)),v.alignAxisTo(c,g,y[l],f[l]),c.options._overlap||(Xt(c.lineBox().y1)===Xt(g.lineBox().y1)&&(c._mirrored||(c.options.labels.mirror=!c.options.labels.mirror,c._mirrored=!0),v.alignAxisTo(c,g,y[l],f[l]),A[p]&&c.reflow(c.box.alignTo(A[p].box,at).translate(0,-c.options.margin)),d&&(A[p]=c)),Xt(c.lineBox().y2,it.COORD_PRECISION)===Xt(g.lineBox().y2,it.COORD_PRECISION)&&(S[p]&&c.reflow(c.box.alignTo(S[p].box,ht).translate(0,c.options.margin)),d&&(S[p]=c)),0!==l&&(c.alignTo(m),c.reflow(c.box)))},shrinkAxisWidth:function(t){var e,i,n,s,o=this.groupAxes(t).any,r=P(o),a=0;for(e=0;e<t.length;e++)i=t[e],i.axes.length>0&&(a=Math.max(a,r.width()-i.contentBox.width()));if(0!==a)for(n=0;n<o.length;n++)s=o[n],s.options.vertical||s.reflow(s.box.shrink(a,0))},shrinkAxisHeight:function(t){var e,i,n,s,o,r,a;for(i=0;i<t.length;i++)if(n=t[i],s=n.axes,o=Math.max(0,P(s).height()-n.contentBox.height()),0!==o){for(r=0;r<s.length;r++)a=s[r],a.options.vertical&&a.reflow(a.box.shrink(0,o));e=!0}return e},fitAxes:function(t){var e,i,n,s,o,r,a,h,l,c,u=this.groupAxes(t).any,p=0;for(e=0;e<t.length;e++)if(i=t[e],n=i.axes,s=i.contentBox,n.length>0)for(o=P(n),r=Math.max(s.y1-o.y1,s.y2-o.y2),p=Math.max(p,s.x1-o.x1),a=0;a<n.length;a++)h=n[a],h.reflow(h.box.translate(0,r));for(l=0;l<u.length;l++)c=u[l],c.reflow(c.box.translate(p,0))},reflowAxes:function(t){var e,i=this,n=this.groupAxes(t);for(e=0;e<t.length;e++)i.reflowPaneAxes(t[e]);n.x.length>0&&n.y.length>0&&(this.alignAxes(n.x,n.y),this.shrinkAxisWidth(t),this.autoRotateAxisLabels(n),this.alignAxes(n.x,n.y),this.shrinkAxisWidth(t)&&this.alignAxes(n.x,n.y),this.shrinkAxisHeight(t),this.alignAxes(n.x,n.y),this.shrinkAxisHeight(t)&&this.alignAxes(n.x,n.y),this.fitAxes(t))},autoRotateAxisLabels:function(t){var e,i,n,s,o=this,r=this,a=r.panes,h=Gn(a);for(i=0;i<h.length;i++)n=h[i],n.autoRotateLabels()&&(e=!0);if(e){for(s=0;s<a.length;s++)o.reflowPaneAxes(a[s]);t.x.length>0&&t.y.length>0&&(this.alignAxes(t.x,t.y),this.shrinkAxisWidth(a))}},reflowPaneAxes:function(t){var e,i=t.axes,n=i.length;if(n>0)for(e=0;e<n;e++)i[e].reflow(t.contentBox)},reflowCharts:function(t){var e,i,n=this.charts,s=n.length,o=this.box;for(e=0;e<s;e++)i=n[e].pane,i&&!Pt(i,t)||n[e].reflow(o)},reflowPanes:function(){var t,e,i,n,s,o,r,a,h,l=this,c=l.box,u=l.panes,p=u.length,d=c.height(),g=p,f=0,v=c.y1;for(t=0;t<p;t++)e=u[t],i=e.options.height,e.options.width=c.width(),e.options.height?(i.indexOf&&i.indexOf("%")&&(n=parseInt(i,10)/100,e.options.height=n*c.height()),e.reflow(c.clone()),d-=e.options.height):f++;for(s=0;s<p;s++)o=u[s],o.options.height||(o.options.height=d/f);for(r=0;r<p;r++)a=u[r],h=c.clone().move(c.x1,v),a.reflow(h),g--,v+=a.options.height},backgroundBox:function(){var t,e,i,n,s,o,r=this.axes,a=r.length;for(e=0;e<a;e++)for(i=r[e],n=0;n<a;n++)s=r[n],i.options.vertical!==s.options.vertical&&(o=i.lineBox().clone().wrap(s.lineBox()),t=t?t.wrap(o):o);return t||this.box},chartsBoxes:function(){var t,e=this.panes,i=[];for(t=0;t<e.length;t++)i.push(e[t].chartsBox());return i},addBackgroundPaths:function(t){var e,i=this.chartsBoxes();for(e=0;e<i.length;e++)t.paths.push(ee.fromRect(i[e].toRect()))},backgroundContainsPoint:function(t){var e,i=this.chartsBoxes();for(e=0;e<i.length;e++)if(i[e].containsPoint(t))return!0},createVisual:function(){var t,e,i,n,s;bt.fn.createVisual.call(this),t=this.options.plotArea,e=t.opacity,i=t.background,n=t.border,void 0===n&&(n={}),E(i)&&(i=ct,e=0),s=this._bgVisual=new te.MultiPath({fill:{color:i,opacity:e},stroke:{color:n.width?n.color:"",width:n.width,dashType:n.dashType},zIndex:-1}),this.addBackgroundPaths(s),this.appendVisual(s)},pointsByCategoryIndex:function(t){var e,i,n,s,o,r=this.charts,a=[];if(null!==t)for(e=0;e<r.length;e++)if(i=r[e],"_navigator"!==i.pane.options.name&&(n=r[e].categoryPoints[t],n&&n.length))for(s=0;s<n.length;s++)o=n[s],o&&yt(o.value)&&null!==o.value&&a.push(o);return a},pointsBySeriesIndex:function(t){return this.filterPoints(function(e){return e.series.index===t})},pointsBySeriesName:function(t){return this.filterPoints(function(e){return e.series.name===t})},filterPoints:function(t){var e,i,n,s,o,r=this.charts,a=[];for(e=0;e<r.length;e++)for(i=r[e],n=i.points,s=0;s<n.length;s++)o=n[s],o&&o.visible!==!1&&t(o)&&a.push(o);return a},findPoint:function(t){var e,i,n,s,o,r=this.charts;for(e=0;e<r.length;e++)for(i=r[e],n=i.points,s=0;s<n.length;s++)if(o=n[s],o&&o.visible!==!1&&t(o))return o},paneByPoint:function(t){var e,i,n=this.panes;for(e=0;e<n.length;e++)if(i=n[e],i.box.containsPoint(t))return i},detachLabels:function(){var t=this.groupAxes(this.panes),e=t.x,i=t.y;this.detachAxisGroupLabels(i,e),this.detachAxisGroupLabels(e,i)},detachAxisGroupLabels:function(t,e){var i,n,s,o,r,a,h,l=this,c=0;for(i=0;i<t.length;i++)n=t[i],s=n.pane,o=T(e,s)||e[0],r=i+c,a=l.createLabelAxis(n,r,o),a&&(c++,h=s.axes.indexOf(n)+c,s.appendAxisAt(a,h))},createLabelAxis:function(t,e,i){var n,s,o,r,a,h,l,c,u=t.options.labels,p=u.position,d=p!==it.END&&p!==it.START,g=u.visible;return d||g===!1?null:(n=this.groupAxes(this.panes),s=i.options.vertical?n.x:n.y,o=this.axisCrossingValues(i,s),r=p===it.END,a=i.range(),h=r?a.max:a.min,l=Zt(o[e],a.min,a.max),l-h===0?null:(o.splice(e+1,0,h),i.options.axisCrossingValues=o,c=t.clone(),t.clear(),c.options.name=void 0,c.options.line.visible=!1,c.options.crosshair=void 0,c.options.notes=void 0,c.options.plotBands=void 0,c))}}),Gn=function(t){return t.reduce(function(t,e){return t.concat(e.axes)},[])},kt(Nn,{series:[],plotArea:{margin:{}},background:"",border:{color:ft,width:0},legend:{inactiveItems:{labels:{color:"#919191"},markers:{color:"#919191"}}}}),qn={hover:function(t,e){this._dispatchEvent(t,e,Ci)},click:function(t,e){this._dispatchEvent(t,e,Si)}},Wn=tt.extend({init:function(t,e,i){var n,s,o,r=e.canonicalFields(t),a=e.valueFields(t),h=e.sourceFields(t,r),l=this._seriesFields=[],c=i.query(t.type),u=t.aggregate||c;for(this._series=t,this._binder=e,n=0;n<r.length;n++){if(s=r[n],o=void 0,Rt(u))o=u[s];else{if(0!==n&&!Pt(s,a))break;o=u}o&&l.push({canonicalName:s,name:h[n],transform:Tt(o)?o:pe[o]})}},aggregatePoints:function(t,e){var i,n,s,o,r,a=this,h=this,l=h._series,c=h._seriesFields,u=this._bindPoints(t||[]),p=u.dataItems[0],d={};for(!p||et(p)||wt(p)||(i=function(){},i.prototype=p,d=new i),n=0;n<c.length;n++){if(s=c[n],o=a._bindField(u.values,s.canonicalName),r=s.transform(o,l,u.dataItems,e),!(null===r||!Rt(r)||yt(r.length)||r instanceof Date)){d=r;break}yt(r)&&R(s.name,d,r)}return d},_bindPoints:function(t){var e,i,n=this,s=n._binder,o=n._series,r=[],a=[];for(e=0;e<t.length;e++)i=t[e],r.push(s.bindPoint(o,i)),a.push(o.data[i]);return{values:r,dataItems:a}},_bindField:function(t,e){var i,n,s,o,r=[],a=t.length;for(i=0;i<a;i++)n=t[i],s=n.valueFields,o=void 0,o=yt(s[e])?s[e]:n.fields[e],r.push(o);return r}}),Xn=tt.extend({init:function(){this._defaults={}},register:function(t,e){var i,n=this;for(i=0;i<t.length;i++)n._defaults[t[i]]=e},query:function(t){return this._defaults[t]}}),Xn.current=new Xn,Zn=xn.extend({createLabel:function(){var t=this.options.labels,e=It({},t,t.from),i=It({},t,t.to);e.visible&&(this.labelFrom=this._createLabel(e),this.append(this.labelFrom)),i.visible&&(this.labelTo=this._createLabel(i),this.append(this.labelTo))},_createLabel:function(t){var e,i=Mt(t),n=this.pointData();return e=i?i(n):this.formatValue(t.format),new vn(e,It({vertical:this.options.vertical},t),n)},reflow:function(t){var e,i,n,s;this.render(),e=this,i=e.labelFrom,n=e.labelTo,s=e.value,this.box=t,i&&(i.options.aboveAxis=s.from>s.to,i.reflow(t)),n&&(n.options.aboveAxis=s.to>s.from,n.reflow(t)),this.note&&this.note.reflow(t)}}),Zn.prototype.defaults=It({},Zn.prototype.defaults,{labels:{format:"{0} - {1}"},tooltip:{format:"{1}"}}),Un=wn.extend({pointType:function(){return Zn},pointValue:function(t){return t.valueFields},formatPointValue:function(t,e){return null===t.value.from&&null===t.value.to?"":this.chartService.format.auto(e,t.value.from,t.value.to)},plotRange:function(t){return t?[t.value.from,t.value.to]:0},updateRange:function(t,e){var i=e.series.axis,n=t.from,s=t.to,o=this.valueAxisRanges[i];null!==t&&et(n)&&et(s)&&(o=this.valueAxisRanges[i]=o||{min:nt,max:st},o.min=Math.min(o.min,n),o.max=Math.max(o.max,n),o.min=Math.min(o.min,s),o.max=Math.max(o.max,s))},aboveAxis:function(t){var e=t.value;return e.from<e.to}}),Un.prototype.plotLimits=$i.prototype.plotLimits,Yn=en.extend({aliasFor:function(){return this.parent}}),jn="auto",Kn="{0}",Qn="{1}",$n=bt.extend({init:function(t,e){bt.fn.init.call(this),this.value=t,this.options=e,this.aboveAxis=Et(this.options.aboveAxis,!0),this.tooltipTracking=!0,this.initLabelsFormat()},render:function(){var t,e,i,n,s,o;this._rendered||(this._rendered=!0,t=this.options,e=t.markers,i=t.labels,n=this.value,s=this.fromPoint=new Yn(n,It({},this.options,{labels:i.from,markers:e.from})),o=this.toPoint=new Yn(n,It({},this.options,{labels:i.to,markers:e.to})),this.copyFields(s),this.copyFields(o),this.append(s),this.append(o))},reflow:function(t){var e,i;this.render(),e=t.from,i=t.to,this.positionLabels(e,i),this.fromPoint.reflow(e),this.toPoint.reflow(i),this.box=this.fromPoint.markerBox().clone().wrap(this.toPoint.markerBox())},createHighlight:function(){var t=new se;return t.append(this.fromPoint.createHighlight()),t.append(this.toPoint.createHighlight()),t},highlightVisual:function(){return this.visual},highlightVisualArgs:function(){return{options:this.options,from:this.fromPoint.highlightVisualArgs(),to:this.toPoint.highlightVisualArgs()}},tooltipAnchor:function(){var t,e,i,n,s,o,r=this.owner.pane.clipBox(),a=!r||r.overlaps(this.box);if(a)return t=this.box,e=t.center(),i=lt,this.options.vertical?(n=e.x,s=t.y1-Se,o=ht):(n=t.x2+Se,s=e.y,o=rt),{point:new At(n,s),align:{horizontal:i,vertical:o}}},formatValue:function(t){return this.owner.formatPointValue(this,t)},overlapsBox:function(t){return this.box.overlaps(t)},unclipElements:function(){this.fromPoint.unclipElements(),this.toPoint.unclipElements()},initLabelsFormat:function(){var e=this.options.labels;e.format||(e.from&&e.from.format||(e.from=t.extend({},e.from,{format:Kn})),e.to&&e.to.format||(e.to=t.extend({},e.to,{format:Qn})))},positionLabels:function(t,e){var i,n,s=this.options,o=s.labels,r=s.vertical;o.position===jn&&(r?e.y1<=t.y1?(n=Be,i=ze):(n=ze,i=Be):e.x1<=t.x1?(n=lt,i=gt):(n=gt,i=lt),o.from&&o.from.position||(this.fromPoint.options.labels.position=i),o.to&&o.to.position||(this.toPoint.options.labels.position=n))},copyFields:function(t){t.dataItem=this.dataItem,t.category=this.category,t.series=this.series,t.color=this.color,t.owner=this.owner}}),It($n.prototype,Ji),It($n.prototype,tn),$n.prototype.defaults={markers:{visible:!1,background:ct,size:Ve,type:ut,border:{width:2},opacity:1},labels:{visible:!1,margin:zt(3),padding:zt(4),animation:{type:we,delay:_e},position:jn},notes:{label:{}},highlight:{markers:{border:{color:ct,width:2}},zIndex:it.HIGHLIGHT_ZINDEX},tooltip:{format:"{0} - {1}"}},Jn=un.extend({createStrokeSegments:function(){return this.segmentsFromPoints(this.toGeometryPoints(this.toPoints()))},stackSegments:function(){var t=this.fromSegments;return this.fromSegments||(t=this.fromSegments=this.segmentsFromPoints(this.toGeometryPoints(this.fromPoints().reverse()))),t},createStroke:function(t){var e=new ee(t),i=new ee(t);e.segments.push.apply(e.segments,this.strokeSegments()),i.segments.push.apply(i.segments,this.stackSegments()),this.visual.append(e),this.visual.append(i)},hasStackSegment:function(){return!0},fromPoints:function(){return this.linePoints.map(function(t){return t.fromPoint})},toPoints:function(){return this.linePoints.map(function(t){return t.toPoint})}}),ts=Jn.extend({createStrokeSegments:function(){return this.createCurveSegments(this.toPoints())},stackSegments:function(){var t=this.fromSegments;return this.fromSegments||(t=this.fromSegments=this.createCurveSegments(this.fromPoints().reverse())),t},createCurveSegments:function(t){var e=new Dt;return e.process(this.toGeometryPoints(t))}}),es=Jn.extend({createStrokeSegments:function(){return this.segmentsFromPoints(this.calculateStepPoints(this.toPoints()))},stackSegments:function(){var t=this.fromSegments;return this.fromSegments||(t=this.fromSegments=this.segmentsFromPoints(this.calculateStepPoints(this.fromPoints())),t.reverse()),t}}),It(es.prototype,sn),is=$i.extend({render:function(){$i.fn.render.call(this),this.renderSegments()},pointType:function(){return $n},createPoint:function(t,e){var i,n,s,o=e.categoryIx,r=e.category,a=e.series,h=e.seriesIx,l=t.valueFields;if(!p(l.from)&&!p(l.to)){if(this.seriesMissingValues(a)!==Le)return null;l={from:0,to:0}}return i=this.pointOptions(a,h),i=this.evalPointOptions(i,l,r,o,a,h),n=t.fields.color||a.color,Tt(a.color)&&(n=i.color),s=new $n(l,i),s.color=n,this.append(s),s},createSegment:function(t,e,i){var n,s=(e.line||{}).style;return new(n="smooth"===s?ts:"step"===s?es:Jn)(t,e,i)},plotRange:function(t,e){return t?[t.value.from,t.value.to]:[e,e]},valueSlot:function(t,e){var i=t.getSlot(e[0],e[0],!this.options.clip),n=t.getSlot(e[1],e[1],!this.options.clip);if(i&&n)return{from:i,to:n}},pointSlot:function(t,e){var i,n,s=e.from,o=e.to;return this.options.invertAxes?(i=new St(s.x1,t.y1,s.x2,t.y2),n=new St(o.x1,t.y1,o.x2,t.y2)):(i=new St(t.x1,s.y1,t.x2,s.y2),n=new St(t.x1,o.y1,t.x2,o.y2)),{from:i,to:n}},addValue:function(t,e){var i=t.valueFields;et(i.from)||(i.from=i.to),et(i.to)||(i.to=i.from),$i.fn.addValue.call(this,t,e)},updateRange:function(t,e){var i,n,s,o;null!==t&&et(t.from)&&et(t.to)&&(i=e.series.axis,n=this.valueAxisRanges[i]=this.valueAxisRanges[i]||{min:nt,max:st},s=t.from,o=t.to,n.min=Math.min(n.min,s,o),n.max=Math.max(n.max,s,o))},formatPointValue:function(t,e){var i=t.value;return this.chartService.format.auto(e,i.from,i.to)},animationPoints:function(){var t,e,i=this.points,n=[];for(t=0;t<i.length;t++)e=i[t],e&&(n.push((e.fromPoint||{}).marker),n.push((e.toPoint||{}).marker));return n.concat(this._segments)}}),It(is.prototype,an,ln),ns=bn.extend({reflow:function(t){var e,i=this,n=i.options,s=i.value,o=i.owner,r=o.seriesValueAxis(n),a=[],h=[],l=[],c=r.getSlot(s.low,s.high),u=r.getSlot(s.open,s.open),p=r.getSlot(s.close,s.close);u.x1=p.x1=c.x1=t.x1,u.x2=p.x2=c.x2=t.x2,e=c.center().x,a.push([u.x1,u.y1]),a.push([e,u.y1]),h.push([e,p.y1]),h.push([p.x2,p.y1]),l.push([e,c.y1]),l.push([e,c.y2]),this.lines=[a,h,l],this.box=c.clone().wrap(u.clone().wrap(p)),this.reflowNote()},createBody:function(){}}),ss=An.extend({pointType:function(){return ns}}),os=bt.extend({init:function(t,e,i){bt.fn.init.call(this),this.from=t,this.to=e,this.series=i},linePoints:function(){var t,e,i=this.from,n=this,s=n.from.box,o=n.to.box,r=[];return i.isVertical?(t=i.aboveAxis?s.y1:s.y2,r.push([s.x1,t],[o.x2,t])):(e=i.aboveAxis?s.x2:s.x1,r.push([e,s.y1],[e,o.y2])),r},createVisual:function(){var t,e;bt.fn.createVisual.call(this),t=this.series.line||{},e=ee.fromPoints(this.linePoints(),{stroke:{color:t.color,width:t.width,opacity:t.opacity,dashType:t.dashType}}),Ct(e),this.visual.append(e)}}),kt(os,{animation:{type:we,delay:_e}}),rs=wn.extend({render:function(){wn.fn.render.call(this),this.createSegments()},traverseDataPoints:function(t){var e,i,n,s,r,a,h,l,c,u,p=this,d=this.options.series,g=o(d),f=!this.options.invertAxes;for(e=0;e<d.length;e++)for(i=d[e],n=0,s=0,r=0;r<g;r++)a=de.current.bindPoint(i,r),h=a.valueFields.value,l=a.fields.summary,c=n,u=void 0,l?"total"===l.toLowerCase()?(a.valueFields.value=n,c=0,u=n):(a.valueFields.value=s,u=c-s,s=0):et(h)&&(s+=h,n+=h,u=n),t(a,{category:p.categoryAxis.categoryAt(r),categoryIx:r,series:i,seriesIx:e,total:n,runningTotal:s,from:c,to:u,isVertical:f})},updateRange:function(t,e){wn.fn.updateRange.call(this,{value:e.to},e)},aboveAxis:function(t){return t.value>=0},plotRange:function(t){return[t.from,t.to]},createSegments:function(){var t,e,i,n,s,o,r,a=this,h=this.options.series,l=this.seriesPoints,c=this.segments=[];for(t=0;t<h.length;t++)if(e=h[t],i=l[t])for(n=void 0,s=0;s<i.length;s++)o=i[s],o&&n&&(r=new os(n,o,e),c.push(r),a.append(r)),n=o}}),as=[Fe,ci,oi,gi],hs=[Ke,di].concat(as),ls=Nn.extend({initFields:function(t){var e,i,n=this;if(this.namedCategoryAxes={},this.namedValueAxes={},this.valueAxisRangeTracker=new fn,t.length>0)for(this.invertAxes=Pt(t[0].type,[Ne,We,di,ci,gi,ri,je,ui]),e=0;e<t.length;e++)if(i=t[e].stack,i&&"100%"===i.type){n.stack100=!0;break}},render:function(t){void 0===t&&(t=this.panes),this.createCategoryAxes(t),this.aggregateCategories(t),this.createCategoryAxesLabels(t),this.createCharts(t),this.createValueAxes(t)},removeAxis:function(t){var e=t.options.name;Nn.fn.removeAxis.call(this,t),t instanceof qt?delete this.namedCategoryAxes[e]:(this.valueAxisRangeTracker.reset(e),delete this.namedValueAxes[e]),t===this.categoryAxis&&delete this.categoryAxis,t===this.valueAxis&&delete this.valueAxis},createCharts:function(t){var e,i,n,s,o,r,a=this,h=this.groupSeriesByPane();for(e=0;e<t.length;e++)if(i=t[e],n=h[i.options.name||"default"]||[],a.addToLegend(n),s=a.filterVisibleSeries(n))for(o=a.groupSeriesByCategoryAxis(s),r=0;r<o.length;r++)a.createChartGroup(o[r],i)},createChartGroup:function(t,e){this.createAreaChart(y(t,[Fe,ci]),e),this.createRangeAreaChart(y(t,[oi,gi]),e),this.createBarChart(y(t,[Ze,Ne]),e),this.createRangeBarChart(y(t,[ai,ri]),e),this.createBulletChart(y(t,[We,pi]),e),this.createCandlestickChart(y(t,Xe),e),this.createBoxPlotChart(y(t,[Ge,ui]),e),this.createOHLCChart(y(t,Qe),e),this.createWaterfallChart(y(t,[fi,je]),e),this.createLineChart(y(t,[Ke,di]),e)},aggregateCategories:function(t){var e,i,n,s,o,r=this,a=this.srcSeries||this.series,h=[];for(this._currentPointsCache={},this._seriesPointsCache=this._seriesPointsCache||{},e=0;e<a.length;e++)i=a[e],n=r.seriesCategoryAxis(i),s=r.findPane(n.options.pane),o=x(n.options.type,vt),i=(o||i.categoryField)&&Pt(s,t)?r.aggregateSeries(i,n):r.filterSeries(i,n),h.push(i);this._seriesPointsCache=this._currentPointsCache,this._currentPointsCache=null,this.srcSeries=a,this.series=h},filterSeries:function(t,e){var i,n,s,o=(t.data||{}).length;return e._seriesMax=Math.max(e._seriesMax||0,o),et(e.options.min)||et(e.options.max)?(i=e.currentRangeIndices(),n=Pt(t.type,hs),s=It({},t),s.data=(s.data||[]).slice(i.min,i.max+1),n&&k(s,i,o,function(n){return{item:t.data[n],category:e.categoryAt(n,!0),categoryIx:n-i.min}},function(e){return yt(t.data[e])}),s):t},clearSeriesPointsCache:function(){this._seriesPointsCache={}},seriesSourcePoints:function(t,e){var i,s,o,r,a,h,l,c,u,p=this,d=t.index+";"+e.categoriesHash();if(this._seriesPointsCache[d])return this._currentPointsCache[d]=this._seriesPointsCache[d],this._seriesPointsCache[d];for(i=e.options,s=i.srcCategories,o=x(i.type,vt),r=t.data,a=o?_:n,h=[],o||e.mapCategories(),l=0;l<r.length;l++)c=void 0,c=t.categoryField?a(t.categoryField,r[l],p.chartService.intl):s[l],yt(c)&&null!==c&&(u=e.totalIndex(c),h[u]=h[u]||{items:[],category:c},h[u].items.push(l));return this._currentPointsCache[d]=h,h},aggregateSeries:function(t,e){var i,n,s,o,r,a,h,l,c,u=t.data;if(!u.length)return t;for(i=this.seriesSourcePoints(t,e),n=It({},t),s=new Wn(It({},t),de.current,Xn.current),o=n.data=[],r=e.options.dataItems||[],a=e.currentRangeIndices(),h=function(t){var n,o=t-a.min,r=i[t];return r||(r=i[t]={}),r.categoryIx=o,r.item||(n=e.categoryAt(t,!0),r.category=n,r.item=s.aggregatePoints(r.items,n)),r},l=a.min;l<=a.max;l++)c=h(l),o[c.categoryIx]=c.item,c.items&&c.items.length&&(r[c.categoryIx]=c.item);return Pt(n.type,hs)&&k(n,a,e.totalCount(),h,function(t){return i[t]}),e.options.dataItems=r,n},appendChart:function(t,e){var i=t.options.series,n=this.seriesCategoryAxis(i[0]),s=n.options.categories,r=Math.max(0,o(i)-s.length);
if(r>0)for(s=n.options.categories=n.options.categories.slice(0);r--;)s.push("");this.valueAxisRangeTracker.update(t.valueAxisRanges),Nn.fn.appendChart.call(this,t,e)},seriesPaneName:function(t){var e=this.options,i=t.axis,n=[].concat(e.valueAxis),s=Ut(n,function(t){return t.name===i})[0],o=e.panes||[{}],r=(o[0]||{}).name||"default",a=(s||{}).pane||r;return a},seriesCategoryAxis:function(t){var e=t.categoryAxis,i=e?this.namedCategoryAxes[e]:this.categoryAxis;if(!i)throw Error("Unable to locate category axis with name "+e);return i},stackableChartOptions:function(t,e){var i=t.stack,n=i&&"100%"===i.type,s=e.options.clip;return{isStacked:i,isStacked100:n,clip:s}},groupSeriesByCategoryAxis:function(t){var e,i,n,s,o,r,a=[],h={};for(e=0;e<t.length;e++)i=t[e].categoryAxis||"$$default$$",h.hasOwnProperty(i)||(h[i]=!0,a.push(i));for(n=[],s=0;s<a.length;s++)o=a[s],r=V(t,o,s),0!==r.length&&n.push(r);return n},createBarChart:function(e,i){var n,s;0!==e.length&&(n=e[0],s=new wn(this,t.extend({series:e,invertAxes:this.invertAxes,gap:n.gap,spacing:n.spacing},this.stackableChartOptions(n,i))),this.appendChart(s,i))},createRangeBarChart:function(t,e){var i,n;0!==t.length&&(i=t[0],n=new Un(this,{series:t,invertAxes:this.invertAxes,gap:i.gap,spacing:i.spacing}),this.appendChart(n,e))},createBulletChart:function(t,e){var i,n;0!==t.length&&(i=t[0],n=new Ln(this,{series:t,invertAxes:this.invertAxes,gap:i.gap,spacing:i.spacing,clip:e.options.clip}),this.appendChart(n,e))},createLineChart:function(e,i){var n,s;0!==e.length&&(n=e[0],s=new cn(this,t.extend({invertAxes:this.invertAxes,series:e},this.stackableChartOptions(n,i))),this.appendChart(s,i))},createAreaChart:function(e,i){var n,s;0!==e.length&&(n=e[0],s=new gn(this,t.extend({invertAxes:this.invertAxes,series:e},this.stackableChartOptions(n,i))),this.appendChart(s,i))},createRangeAreaChart:function(t,e){if(0!==t.length){var i=new is(this,{invertAxes:this.invertAxes,series:t,clip:e.options.clip});this.appendChart(i,e)}},createOHLCChart:function(t,e){var i,n;0!==t.length&&(i=t[0],n=new ss(this,{invertAxes:this.invertAxes,gap:i.gap,series:t,spacing:i.spacing,clip:e.options.clip}),this.appendChart(n,e))},createCandlestickChart:function(t,e){var i,n;0!==t.length&&(i=t[0],n=new An(this,{invertAxes:this.invertAxes,gap:i.gap,series:t,spacing:i.spacing,clip:e.options.clip}),this.appendChart(n,e))},createBoxPlotChart:function(t,e){var i,n;0!==t.length&&(i=t[0],n=new kn(this,{invertAxes:this.invertAxes,gap:i.gap,series:t,spacing:i.spacing,clip:e.options.clip}),this.appendChart(n,e))},createWaterfallChart:function(t,e){var i,n;0!==t.length&&(i=t[0],n=new rs(this,{series:t,invertAxes:this.invertAxes,gap:i.gap,spacing:i.spacing}),this.appendChart(n,e))},axisRequiresRounding:function(t,e){var i,n,s,o,r,a=this,h=y(this.series,vi);for(i=0;i<this.series.length;i++)n=a.series[i],Pt(n.type,as)&&(s=n.line,s&&s.style===He&&h.push(n));for(o=0;o<h.length;o++)if(r=h[o].categoryAxis||"",r===t||!r&&0===e)return!0},aggregatedAxis:function(t,e){var i,n,s=this.series;for(i=0;i<s.length;i++)if(n=s[i].categoryAxis||"",(n===t||!n&&0===e)&&s[i].categoryField)return!0},createCategoryAxesLabels:function(){var t,e=this.axes;for(t=0;t<e.length;t++)e[t]instanceof qt&&e[t].createLabels()},createCategoryAxes:function(t){var e,i,n,s,o,r,a,h=this,l=this.invertAxes,c=[].concat(this.options.categoryAxis),u=[];for(e=0;e<c.length;e++)if(i=c[e],n=h.findPane(i.pane),Pt(n,t)){if(s=i.name,o=i.categories,void 0===o&&(o=[]),i=It({vertical:l,reverse:!l&&h.chartService.rtl,axisCrossingValue:l?nt:0},i),yt(i.justified)||(i.justified=h.isJustified()),h.axisRequiresRounding(s,e)&&(i.justified=!1),r=void 0,r=w(i,o[0])?new J.DateCategoryAxis(i,h.chartService):new qt(i,h.chartService),c[e].categories=r.options.srcCategories,s){if(h.namedCategoryAxes[s])throw Error("Category axis with name "+s+" is already defined");h.namedCategoryAxes[s]=r}r.axisIndex=e,u.push(r),h.appendAxis(r)}a=this.categoryAxis||u[0],this.categoryAxis=a,l?this.axisY=a:this.axisX=a},isJustified:function(){var t,e,i=this.series;for(t=0;t<i.length;t++)if(e=i[t],!Pt(e.type,as))return!1;return!0},createValueAxes:function(t){var e,i,n,s,o,r,a,h,l,c=this,u=this.valueAxisRangeTracker,p=u.query(),d=[].concat(this.options.valueAxis),g=this.invertAxes,f={vertical:!g,reverse:g&&this.chartService.rtl},v=[];for(this.stack100&&(f.roundToMajorUnit=!1,f.labels={format:"P0"}),e=0;e<d.length;e++)if(i=d[e],n=c.findPane(i.pane),Pt(n,t)){if(s=i.name,o=x(i.type,Fi)?{min:.1,max:1}:{min:0,max:1},r=u.query(s)||p||o,0===e&&r&&p&&(r.min=Math.min(r.min,p.min),r.max=Math.max(r.max,p.max)),a=void 0,a=x(i.type,Fi)?J.LogarithmicAxis:J.NumericAxis,h=new a(r.min,r.max,It({},f,i),c.chartService),s){if(c.namedValueAxes[s])throw Error("Value axis with name "+s+" is already defined");c.namedValueAxes[s]=h}h.axisIndex=e,v.push(h),c.appendAxis(h)}l=this.valueAxis||v[0],this.valueAxis=l,g?this.axisX=l:this.axisY=l},_dispatchEvent:function(t,e,i){var n,s,o,r=t._eventCoordinates(e),a=new At(r.x,r.y),h=this.pointPane(a),l=[],c=[];if(h){for(n=h.axes,s=0;s<n.length;s++)o=n[s],o.getValue?f(c,o.getValue(a)):f(l,o.getCategory(a));0===l.length&&f(l,this.categoryAxis.getCategory(a)),l.length>0&&c.length>0&&t.trigger(i,{element:Lt(e),originalEvent:e,category:b(l),value:b(c)})}},pointPane:function(t){var e,i,n=this.panes;for(e=0;e<n.length;e++)if(i=n[e],i.contentBox.containsPoint(t))return i},updateAxisOptions:function(t,e){I(this.options,t,e),I(this.originalOptions,t,e)}}),kt(ls,{categoryAxis:{},valueAxis:{}}),It(ls.prototype,qn),cs=tt.extend({init:function(){this._points=[]},destroy:function(){this._points=[]},show:function(t){var e,i,n=this,s=[].concat(t);for(this.hide(),e=0;e<s.length;e++)i=s[e],i&&i.toggleHighlight&&i.hasHighlight()&&(n.togglePointHighlight(i,!0),n._points.push(i))},togglePointHighlight:function(t,e){var i,n=(t.options.highlight||{}).toggle;n?(i={category:t.category,series:t.series,dataItem:t.dataItem,value:t.value,stackValue:t.stackValue,preventDefault:L,visual:t.highlightVisual(),show:e},n(i),i._defaultPrevented||t.toggleHighlight(e)):t.toggleHighlight(e)},hide:function(){for(var t=this,e=this._points;e.length;)t.togglePointHighlight(e.pop(),!1)},isHighlighted:function(t){var e,i,n=this._points;for(e=0;e<n.length;e++)if(i=n[e],t===i)return!0;return!1}}),us=tt.extend({init:function(t,e){this.plotArea=t,this.options=It({},this.options,e)},start:function(t){return this._active=M(t,this.options.key)},move:function(t){if(this._active){var e=this.axisRanges=this._panAxes(t,pt).concat(this._panAxes(t,dt));if(e.length)return this.axisRanges=e,O(e)}},end:function(){var t=this._active;return this._active=!1,t},pan:function(){var t,e,i=this,n=i.plotArea,s=i.axisRanges;if(s.length){for(t=0;t<s.length;t++)e=s[t],n.updateAxisOptions(e.axis,e.range);n.redraw(n.panes)}},destroy:function(){delete this.plotArea},_panAxes:function(t,e){var i,n,s,o,r=this.plotArea,a=-t[e].delta,h=(this.options.lock||"").toLowerCase(),l=[];if(0!==a&&(h||"").toLowerCase()!==e)for(i=r.axes,n=0;n<i.length;n++)s=i[n],(e===pt&&!s.options.vertical||e===dt&&s.options.vertical)&&(o=s.pan(a),o&&(o.limitRange=!0,l.push({axis:s,range:o})));return l}}),us.prototype.options={key:"none",lock:"none"},ps=tt.extend({init:function(t,e){this.chart=t,this.options=It({},this.options,e),this.createElement()},createElement:function(){var t,e=this._marquee=document.createElement("div");e.className="k-marquee",t=document.createElement("div"),t.className="k-marquee-color",e.appendChild(t)},removeElement:function(){this._marquee.parentNode&&this._marquee.parentNode.removeChild(this._marquee)},setStyles:function(t){Yt(this._marquee,t)},start:function(t){var e,i,n,s,o;return!!(M(t,this.options.key)&&(e=this.chart,i=e._eventCoordinates(t),n=this._zoomPane=e._plotArea.paneByPoint(i),s=n?n.chartsBox().clone():null,n&&s))&&(o=this._elementOffset(),s.translate(o.left,o.top),this._zoomPaneClipBox=s,document.body.appendChild(this._marquee),this.setStyles({left:t.pageX+1,top:t.pageY+1,width:0,height:0}),!0)},_elementOffset:function(){var t=this.chart.element,e=Yt(t,["paddingLeft","paddingTop"]),i=e.paddingLeft,n=e.paddingTop,s=J.elementOffset(t);return{left:i+s.left,top:n+s.top}},move:function(t){var e=this._zoomPane;e&&this.setStyles(this._selectionPosition(t))},end:function(t){var e,i,n,s,o=this._zoomPane;if(o)return e=this._elementOffset(),i=this._selectionPosition(t),i.left-=e.left,i.top-=e.top,n={x:i.left,y:i.top},s={x:i.left+i.width,y:i.top+i.height},this._updateAxisRanges(n,s),this.removeElement(),delete this._zoomPane,O(this.axisRanges)},zoom:function(){var t,e,i,n=this.axisRanges;if(n&&n.length){for(t=this.chart._plotArea,e=0;e<n.length;e++)i=n[e],t.updateAxisOptions(i.axis,i.range);t.redraw(t.panes)}},destroy:function(){this.removeElement(),delete this._marquee,delete this.chart},_updateAxisRanges:function(t,e){var i,n,s,o,r=(this.options.lock||"").toLowerCase(),a=[],h=this._zoomPane.axes;for(i=0;i<h.length;i++)n=h[i],s=n.options.vertical,r===pt&&!s||r===dt&&s||(o=n.pointsRange(t,e),o&&a.push({axis:n,range:o}));this.axisRanges=a},_selectionPosition:function(t){var e=this._zoomPaneClipBox,i={x:t.x.startLocation,y:t.y.startLocation},n=t.x.location,s=t.y.location,o=(this.options.lock||"").toLowerCase(),r=Math.min(i.x,n),a=Math.min(i.y,s),h=Math.abs(i.x-n),l=Math.abs(i.y-s);return o===pt&&(r=e.x1,h=e.width()),o===dt&&(a=e.y1,l=e.height()),n>e.x2&&(h=e.x2-i.x),n<e.x1&&(h=i.x-e.x1),s>e.y2&&(l=e.y2-i.y),s<e.y1&&(l=i.y-e.y1),{left:Math.max(r,e.x1),top:Math.max(a,e.y1),width:h,height:l}}}),ps.prototype.options={key:"shift",lock:"none"},ds=tt.extend({init:function(t,e){this.chart=t,this.options=It({},this.options,e)},updateRanges:function(t){var e,i,n,s,o=(this.options.lock||"").toLowerCase(),r=[],a=this.chart._plotArea.axes;for(e=0;e<a.length;e++)i=a[e],n=i.options.vertical,o===pt&&!n||o===dt&&n||(s=i.zoomRange(-t),s&&r.push({axis:i,range:s}));return this.axisRanges=r,O(r)},zoom:function(){var t,e,i,n=this.axisRanges;if(n&&n.length){for(t=this.chart._plotArea,e=0;e<n.length;e++)i=n[e],t.updateAxisOptions(i.axis,i.range);t.redraw(t.panes)}},destroy:function(){delete this.chart}}),gs=bt.extend({init:function(t,e){bt.fn.init.call(this,t),this.chartService=e},render:function(){var t,e,i=this,n=i.children,s=i.options,o=s.vertical;for(this.visual=new te.Layout(null,{spacing:o?0:s.spacing,lineSpacing:o?s.spacing:0,orientation:o?"vertical":"horizontal",reverse:s.rtl,alignItems:o?"start":"center"}),t=0;t<n.length;t++)e=n[t],e.reflow(new St),e.renderVisual()},reflow:function(t){this.visual.rect(t.toRect()),this.visual.reflow();var e=this.visual.clippedBBox();this.box=e?J.rectToBox(e):new St},renderVisual:function(){this.addVisual()},createVisual:function(){}}),fs=Wt.extend({init:function(t){Wt.fn.init.call(this,t),this.createContainer(),t.rtl?(this.createLabel(),this.createMarker()):(this.createMarker(),this.createLabel())},createContainer:function(){this.container=new J.FloatElement({vertical:!1,wrap:!1,align:rt,spacing:this.options.spacing}),this.append(this.container)},createMarker:function(){this.container.append(new Bt(this.markerOptions()))},markerOptions:function(){var t=this.options,e=t.markerColor;return It({},t.markers,{background:e,border:{color:e}})},createLabel:function(){var t=this.options,e=It({},t.labels);this.container.append(new Ot(t.text,e))},renderComplete:function(){var t,e;Wt.fn.renderComplete.call(this),t=this.options.cursor||{},e=this._itemOverlay=ee.fromRect(this.container.box.toRect(),{fill:{color:ct,opacity:0},stroke:null,cursor:t.style||t}),this.appendVisual(e)},click:function(t,e){var i=this.eventArgs(e);!t.trigger(mi,i)&&e&&"contextmenu"===e.type&&e.preventDefault()},over:function(t,e){var i=this.eventArgs(e);return t.trigger(xi,i)||t._legendItemHover(i.seriesIndex,i.pointIndex),!0},out:function(t,e){t._unsetActivePoint(),t.trigger(yi,this.eventArgs(e))},eventArgs:function(t){var e=this.options;return{element:Lt(t),text:e.text,series:e.series,seriesIndex:e.series.index,pointIndex:e.pointIndex}},renderVisual:function(){var t=this,e=this.options,i=e.visual;i?(this.visual=i({active:e.active,series:e.series,sender:this.getSender(),pointIndex:e.pointIndex,options:{markers:this.markerOptions(),labels:e.labels},createVisual:function(){t.createVisual(),t.renderChildren(),t.renderComplete();var e=t.visual;return delete t.visual,e}}),this.addVisual()):Wt.fn.renderVisual.call(this)}}),vs="horizontal",ms="pointer",xs="custom",ys=bt.extend({init:function(t,e){void 0===e&&(e={}),bt.fn.init.call(this,t),this.chartService=e,Pt(this.options.position,[at,gt,ht,lt,xs])||(this.options.position=gt),this.createContainer(),this.createItems()},createContainer:function(){var t=this.options,e=t.position,i=t.align,n=e,s=rt;e===xs?n=lt:Pt(e,[at,ht])?(n="start"===i?lt:"end"===i?gt:rt,s=e):i&&("start"===i?s=at:"end"===i&&(s=ht)),this.container=new Wt({margin:t.margin,padding:t.padding,background:t.background,border:t.border,vAlign:s,align:n,zIndex:t.zIndex,shrinkToFit:!0}),this.append(this.container)},createItems:function(){var t,e,i,n=this.getService(),s=this.options,o=this.isVertical(),r=new gs({vertical:o,spacing:s.spacing,rtl:n.rtl},n),a=s.items;for(s.reverse&&(a=a.slice(0).reverse()),t=a.length,e=0;e<t;e++)i=a[e],r.append(new fs(It({},{markers:s.markers,labels:s.labels,rtl:n.rtl},s.item,i)));r.render(),this.container.append(r)},isVertical:function(){var t=this.options,e=t.orientation,i=t.position,n=i===xs&&e!==vs||(yt(e)?e!==vs:Pt(i,[lt,gt]));return n},hasItems:function(){return this.container.children[0].children.length>0},reflow:function(t){var e=this.options,i=t.clone();return this.hasItems()?void(e.position===xs?(this.containerCustomReflow(i),this.box=i):this.containerReflow(i)):void(this.box=i)},containerReflow:function(t){var e,i=this,n=i.options,s=i.container,o=n.position,r=n.width,a=n.height,h=o===at||o===ht?pt:dt,l=this.isVertical(),c=t.clone(),u=t.clone();o!==lt&&o!==gt||(u.y1=c.y1=0),l&&a?(u.y2=u.y1+a,u.align(c,dt,s.options.vAlign)):!l&&r&&(u.x2=u.x1+r,u.align(c,pt,s.options.align)),s.reflow(u),u=s.box,e=u.clone(),(n.offsetX||n.offsetY)&&(u.translate(n.offsetX,n.offsetY),this.container.reflow(u)),e[h+1]=t[h+1],e[h+2]=t[h+2],this.box=e},containerCustomReflow:function(t){var e=this,i=e.options,n=e.container,s=i.offsetX,o=i.offsetY,r=i.width,a=i.height,h=this.isVertical(),l=t.clone();h&&a?l.y2=l.y1+a:!h&&r&&(l.x2=l.x1+r),n.reflow(l),l=n.box,n.reflow(new St(s,o,s+l.width(),o+l.height()))},renderVisual:function(){this.hasItems()&&bt.fn.renderVisual.call(this)}}),kt(ys,{position:gt,items:[],offsetX:0,offsetY:0,margin:zt(5),padding:zt(5),border:{color:ft,width:0},item:{cursor:ms,spacing:6},spacing:6,background:"",zIndex:1,markers:{border:{width:0},width:15,height:3,type:"rect",align:lt,vAlign:rt}}),_s=tt.extend({init:function(){this._registry=[]},register:function(t,e){this._registry.push({type:t,seriesTypes:e})},create:function(t,e,i){var n,s,o,r=this._registry,a=r[0];for(s=0;s<r.length;s++)if(o=r[s],n=y(t,o.seriesTypes),n.length>0){a=o;break}return new a.type(n,e,i)}}),_s.current=new _s,ws=3,bs=.1,As=tt.extend({init:function(t,e,i,n){var s=t.element;this.options=It({},this.options,i),this.chart=t,this.observer=n,this.chartElement=s,this.categoryAxis=e,this._dateAxis=this.categoryAxis instanceof J.DateCategoryAxis,this.initOptions(),this.options.visible&&(this.createElements(),this.set(this._index(this.options.from),this._index(this.options.to)),this.bindEvents())},onPane:function(t){return this.categoryAxis.pane===t},createElements:function(){var t,e,i,n,s,o,r=this.options,a=this.wrapper=B("k-selector");Yt(a,{top:r.offset.top,left:r.offset.left,width:r.width,height:r.height,direction:"ltr"}),t=this.selection=B("k-selection"),this.leftMask=B("k-mask"),this.rightMask=B("k-mask"),a.appendChild(this.leftMask),a.appendChild(this.rightMask),a.appendChild(t),t.appendChild(B("k-selection-bg")),e=this.leftHandle=B("k-handle k-left-handle"),i=this.rightHandle=B("k-handle k-right-handle"),e.appendChild(B()),i.appendChild(B()),t.appendChild(e),t.appendChild(i),this.chartElement.appendChild(a),n=Yt(t,["borderLeftWidth","borderRightWidth","height"]),s=Yt(e,"height").height,o=Yt(i,"height").height,r.selection={border:{left:n.borderLeftWidth,right:n.borderRightWidth}},Yt(e,{top:(n.height-s)/2}),Yt(i,{top:(n.height-o)/2}),a.style.cssText=a.style.cssText},bindEvents:function(){if(this.options.mousewheel!==!1){this._mousewheelHandler=this._mousewheel.bind(this);var t;Kt(this.wrapper,(t={},t[Xi]=this._mousewheelHandler,t))}this._domEvents=Qt.DomEventsBuilder.create(this.wrapper,{stopPropagation:!0,start:this._start.bind(this),move:this._move.bind(this),end:this._end.bind(this),tap:this._tap.bind(this),press:this._press.bind(this),gesturestart:this._gesturestart.bind(this),gesturechange:this._gesturechange.bind(this),gestureend:this._gestureend.bind(this)})},initOptions:function(){var t,e,i,n=this,s=n.options,o=n.categoryAxis,r=o.pane.chartsBox(),a=this.chart.chartService.intl;this._dateAxis&&It(s,{min:Nt(a,s.min),max:Nt(a,s.max),from:Nt(a,s.from),to:Nt(a,s.to)}),t=Yt(this.chartElement,["paddingLeft","paddingTop"]),e=t.paddingLeft,i=t.paddingTop,this.options=It({},{width:r.width(),height:r.height()+bs,padding:{left:e,top:i},offset:{left:r.x1+e,top:r.y1+i},from:s.min,to:s.max},s)},destroy:function(){if(this._domEvents&&(this._domEvents.destroy(),delete this._domEvents),clearTimeout(this._mwTimeout),this._state=null,this.wrapper){if(this._mousewheelHandler){var t;$t(this.wrapper,(t={},t[Xi]=this._mousewheelHandler,t)),this._mousewheelHandler=null}this.chartElement.removeChild(this.wrapper),this.wrapper=null}},_rangeEventArgs:function(t){return{axis:this.categoryAxis.options,from:this._value(t.from),to:this._value(t.to)}},_start:function(t){var e,i=this.options,n=Lt(t);!this._state&&n&&(this.chart._unsetActivePoint(),this._state={moveTarget:z(n)||n,startLocation:t.x?t.x.location:0,range:{from:this._index(i.from),to:this._index(i.to)}},e=this._rangeEventArgs({from:this._index(i.from),to:this._index(i.to)}),this.trigger(Li,e)&&(this._state=null))},_press:function(t){var e;e=this._state?this._state.moveTarget:z(Lt(t)),e&&J.addClass(e,"k-handle-active")},_move:function(t){var e,i,n,s,o,r,a,h,l,c,u,p,d,g,f,v,m,x;this._state&&(e=this,i=e._state,n=e.options,s=e.categoryAxis,o=i.range,r=i.moveTarget,a=s.options.reverse,h=this._index(n.from),l=this._index(n.to),c=this._index(n.min),u=this._index(n.max),p=i.startLocation-t.x.location,d={from:o.from,to:o.to},g=o.to-o.from,f=Yt(this.wrapper,"width").width/(s.categoriesCount()-1),v=Math.round(p/f)*(a?-1:1),r&&(m=jt(r,"k-left-handle"),x=jt(r,"k-right-handle"),jt(r,"k-selection k-selection-bg")?(o.from=Math.min(Math.max(c,h-v),u-g),o.to=Math.min(o.from+g,u)):m&&!a||x&&a?(o.from=Math.min(Math.max(c,h-v),u-1),o.to=Math.max(o.from+1,o.to)):(m&&a||x&&!a)&&(o.to=Math.min(Math.max(c+1,l-v),u),o.from=Math.min(o.to-1,o.from)),o.from===d.from&&o.to===d.to||(this.move(o.from,o.to),this.trigger(Mi,this._rangeEventArgs(o)))))},_end:function(){var t,e;this._state&&(t=this._state.moveTarget,t&&J.removeClass(t,"k-handle-active"),e=this._state.range,this.set(e.from,e.to),this.trigger(Oi,this._rangeEventArgs(e)),delete this._state)},_tap:function(t){var e=this,i=e.options,n=e.categoryAxis,s=this.chart._eventCoordinates(t),o=n.pointCategoryIndex(new At(s.x,n.box.y1)),r=this._index(i.from),a=this._index(i.to),h=this._index(i.min),l=this._index(i.max),c=a-r,u=r+c/2,p={},d=3===t.event.which,g=Math.round(u-o);this._state||d||(this.chart._unsetActivePoint(),n.options.justified||g--,p.from=Math.min(Math.max(h,r-g),l-c),p.to=Math.min(p.from+c,l),this._start(t),this._state&&(this._state.range=p,this.trigger(Mi,this._rangeEventArgs(p)),this._end()))},_mousewheel:function(t){var e,i=this,n=J.mousewheelDelta(t);this._start({target:this.selection}),this._state&&(e=this._state.range,t.preventDefault(),t.stopPropagation(),Math.abs(n)>1&&(n*=ws),this.options.mousewheel.reverse&&(n*=-1),this.expand(n)&&this.trigger(Mi,{axis:this.categoryAxis.options,delta:n,originalEvent:t,from:this._value(e.from),to:this._value(e.to)}),this._mwTimeout&&clearTimeout(this._mwTimeout),this._mwTimeout=setTimeout(function(){i._end()},Zi))},_gesturestart:function(t){var e,i=this.options;this._state={range:{from:this._index(i.from),to:this._index(i.to)}},e=this._rangeEventArgs(this._state.range),this.trigger(Li,e)?this._state=null:t.preventDefault()},_gestureend:function(){this._state&&(this.trigger(Oi,this._rangeEventArgs(this._state.range)),delete this._state)},_gesturechange:function(t){var e=this,i=e.chart,n=e._state,s=e.options,o=e.categoryAxis,r=n.range,a=i._toModelCoordinates(t.touches[0].x.location).x,h=i._toModelCoordinates(t.touches[1].x.location).x,l=Math.min(a,h),c=Math.max(a,h);t.preventDefault(),r.from=o.pointCategoryIndex(new At(l))||s.min,r.to=o.pointCategoryIndex(new At(c))||s.max,this.move(r.from,r.to),this.trigger(Mi,this._rangeEventArgs(r))},_index:function(t){var e=t;return t instanceof Date&&(e=this.categoryAxis.categoryIndex(t)),e},_value:function(t){var e=t;return this._dateAxis&&(e=this.categoryAxis.categoryAt(t),e>this.options.max&&(e=this.options.max)),e},_slot:function(t){var e=this.categoryAxis,i=this._index(t);return e.getSlot(i,i,!0)},move:function(t,e){var i,n,s=this.options,o=this.categoryAxis.options.reverse,r=s.offset,a=s.padding,h=s.selection.border,l=o?e:t,c=o?t:e,u="x"+(o?2:1),p=this._slot(l),d=Xt(p[u]-r.left+a.left);Yt(this.leftMask,{width:d}),Yt(this.selection,{left:d}),p=this._slot(c),i=Xt(s.width-(p[u]-r.left+a.left)),Yt(this.rightMask,{width:i}),n=s.width-i,n!==s.width&&(n+=h.right),Yt(this.rightMask,{left:n}),Yt(this.selection,{width:Math.max(s.width-(d+i)-h.right,0)})},set:function(t,e){var i=this.options,n=this._index(i.min),s=this._index(i.max),o=Zt(this._index(t),n,s),r=Zt(this._index(e),o+1,s);i.visible&&this.move(o,r),i.from=this._value(o),i.to=this._value(r)},expand:function(t){var e=this.options,i=this._index(e.min),n=this._index(e.max),s=e.mousewheel.zoom,o=this._index(e.from),r=this._index(e.to),a={from:o,to:r},h=It({},a);if(this._state&&(a=this._state.range),s!==gt&&(a.from=Zt(Zt(o-t,0,r-1),i,n)),s!==lt&&(a.to=Zt(Zt(r+t,a.from+1,n),i,n)),a.from!==h.from||a.to!==h.to)return this.set(a.from,a.to),!0},trigger:function(t,e){return(this.observer||this.chart).trigger(t,e)}}),kt(As,{visible:!0,mousewheel:{zoom:"both"},min:st,max:nt}),Ss=Mn.extend({show:function(t){var e,i;!t||!t.tooltipAnchor||this._current&&this._current===t||(e=It({},this.options,t.options.tooltip),i=t.tooltipAnchor(),i?(this._current=t,Mn.fn.show.call(this,{point:t,anchor:i},e,t)):this.hide())},hide:function(){delete this._current,Mn.fn.hide.call(this)}}),Cs=Mn.extend({init:function(t,e){Mn.fn.init.call(this,t.chartService,e),this.plotArea=t,this.formatService=t.chartService.format},showAt:function(t,e){var i,n,s,o=Ut(t,function(t){var e=t.series.tooltip,i=e&&e.visible===!1;return!i});o.length>0&&(i=o[0],n=this.plotArea.categoryAxis.getSlot(i.categoryIx),s=e?this._slotAnchor(e,n):this._defaultAnchor(i,n),this.show({anchor:s,shared:!0,points:t,category:i.category,categoryText:this.formatService.auto(this.options.categoryFormat,i.category),series:this.plotArea.series},this.options))},_slotAnchor:function(t,e){var i=this.plotArea.categoryAxis,n={horizontal:"left",vertical:"center"};return i.options.vertical||(t.x=e.center().x),{point:t,align:n}},_defaultAnchor:function(t,e){var i,n=t.owner.pane.chartsBox(),s=this.plotArea.categoryAxis.options.vertical,o=n.center(),r=e.center(),a={horizontal:"center",vertical:"center"};return i=s?new At(o.x,r.y):new At(r.x,o.y),{point:i,align:a}}}),kt(Cs,{categoryFormat:"{0:d}"}),ks=ie.extend({setup:function(){var t,e,i=this,n=i.element,s=i.options,o=n.bbox();o?(this.origin=s.origin,t=s.vertical?dt:pt,e=this.fromScale=new ae(1,1),e[t]=Ce,n.transform(he().scale(e.x,e.y))):this.abort()},step:function(t){var e=J.interpolateValue(this.fromScale.x,1,t),i=J.interpolateValue(this.fromScale.y,1,t);this.element.transform(he().scale(e,i,this.origin))},abort:function(){ie.fn.abort.call(this),this.element.transform(null)}}),kt(ks,{duration:_e}),ne.current.register(Ne,ks),Ps=ie.extend({setup:function(){var t=this.center=this.element.bbox().center();this.element.transform(he().scale(Ce,Ce,t))},step:function(t){this.element.transform(he().scale(t,t,this.center))}}),kt(Ps,{easing:"easeOutElastic"}),ne.current.register(qe,Ps),Ts=ie.extend({setup:function(){this.fadeTo=this.element.opacity(),this.element.opacity(0)},step:function(t){this.element.opacity(t*this.fadeTo)}}),kt(Ts,{duration:200,easing:"linear"}),ne.current.register(we,Ts),Es=ie.extend({setup:function(){this.element.transform(he().scale(Ce,Ce,this.options.center))},step:function(t){this.element.transform(he().scale(t,t,this.options.center))}}),kt(Es,{easing:"easeOutElastic",duration:_e}),ne.current.register($e,Es),Rs=Tn.extend({render:function(){Tn.fn.render.call(this),this.renderSegments()},createSegment:function(t,e,i){var n,s=e.style;return new(n=s===De?rn:nn)(t,e,i)},animationPoints:function(){var t=Tn.fn.animationPoints.call(this);return t.concat(this._segments)},createMissingValue:function(t,e){if(e===Le){var i={x:t.x,y:t.y};return p(i.x)||(i.x=0),p(i.y)||(i.y=0),i}}}),It(Rs.prototype,an),Is=Nn.extend({initFields:function(){this.namedXAxes={},this.namedYAxes={},this.xAxisRangeTracker=new fn,this.yAxisRangeTracker=new fn},render:function(t){var e,i,n,s,o,r=this;for(void 0===t&&(t=this.panes),e=this.groupSeriesByPane(),i=0;i<t.length;i++)n=t[i],s=e[n.options.name||"default"]||[],r.addToLegend(s),o=r.filterVisibleSeries(s),o&&(r.createScatterChart(y(o,hi),n),r.createScatterLineChart(y(o,li),n),r.createBubbleChart(y(o,qe),n));this.createAxes(t)},appendChart:function(t,e){this.xAxisRangeTracker.update(t.xAxisRanges),this.yAxisRangeTracker.update(t.yAxisRanges),Nn.fn.appendChart.call(this,t,e)},removeAxis:function(t){var e=t.options.name;Nn.fn.removeAxis.call(this,t),t.options.vertical?(this.yAxisRangeTracker.reset(e),delete this.namedYAxes[e]):(this.xAxisRangeTracker.reset(e),delete this.namedXAxes[e]),t===this.axisX&&delete this.axisX,t===this.axisY&&delete this.axisY},seriesPaneName:function(t){var e=this.options,i=t.xAxis,n=[].concat(e.xAxis),s=Ut(n,function(t){return t.name===i})[0],o=t.yAxis,r=[].concat(e.yAxis),a=Ut(r,function(t){return t.name===o})[0],h=e.panes||[{}],l=h[0].name||"default",c=(s||{}).pane||(a||{}).pane||l;return c},createScatterChart:function(t,e){t.length>0&&this.appendChart(new Tn(this,{series:t,clip:e.options.clip}),e)},createScatterLineChart:function(t,e){t.length>0&&this.appendChart(new Rs(this,{series:t,clip:e.options.clip}),e)},createBubbleChart:function(t,e){t.length>0&&this.appendChart(new Rn(this,{series:t,clip:e.options.clip}),e)},createXYAxis:function(t,e,i){var n,s,o,r,a,h,l,c,u=t.name,p=e?this.namedYAxes:this.namedXAxes,d=e?this.yAxisRangeTracker:this.xAxisRangeTracker,g=It({reverse:!e&&this.chartService.rtl},t,{vertical:e}),f=x(g.type,Fi),v=d.query(),m=f?{min:.1,max:1}:{min:0,max:1},y=d.query(u)||v||m,_=[g.min,g.max],w=this.series;for(n=0;n<w.length;n++)if(s=w[n],o=s[e?"yAxis":"xAxis"],o===g.name||0===i&&!o){r=de.current.bindPoint(s,0).valueFields,_.push(r[e?"y":"x"]);break}for(0===i&&v&&(y.min=Math.min(y.min,v.min),y.max=Math.max(y.max,v.max)),h=0;h<_.length;h++)if(_[h]instanceof Date){a=!0;break}if(l=x(g.type,vt)||!g.type&&a?J.DateValueAxis:f?J.LogarithmicAxis:J.NumericAxis,c=new l(y.min,y.max,g,this.chartService),c.axisIndex=i,u){if(p[u])throw Error((e?"Y":"X")+" axis with name "+u+" is already defined");p[u]=c}return this.appendAxis(c),c},createAxes:function(t){var e,i,n,s,o=this,r=this.options,a=[].concat(r.xAxis),h=[],l=[].concat(r.yAxis),c=[];for(e=0;e<a.length;e++)i=o.findPane(a[e].pane),Pt(i,t)&&h.push(o.createXYAxis(a[e],!1,e));for(n=0;n<l.length;n++)s=o.findPane(l[n].pane),Pt(s,t)&&c.push(o.createXYAxis(l[n],!0,n));this.axisX=this.axisX||h[0],this.axisY=this.axisY||c[0]},_dispatchEvent:function(t,e,i){var n,s,o,r,a=t._eventCoordinates(e),h=new At(a.x,a.y),l=this.axes,c=l.length,u=[],p=[];for(n=0;n<c;n++)s=l[n],o=s.options.vertical?p:u,r=s.getValue(h),null!==r&&o.push(r);u.length>0&&p.length>0&&t.trigger(i,{element:Lt(e),originalEvent:e,x:b(u),y:b(p)})},updateAxisOptions:function(t,e){var i=t.options.vertical,n=this.groupAxes(this.panes),s=(i?n.y:n.x).indexOf(t);D(this.options,s,i,e),D(this.originalOptions,s,i,e)}}),kt(Is,{xAxis:{},yAxis:{}}),It(Is.prototype,qn),Vs=bt.extend({init:function(t,e,i){bt.fn.init.call(this,i),this.value=t,this.sector=e},render:function(){var t,e,i,n,s=this.options.labels,o=this.owner.chartService,r=this.value;this._rendered||this.visible===!1||(this._rendered=!0,t=Mt(s),e=this.pointData(),t?r=t(e):s.format&&(r=o.format.auto(s.format,r)),s.visible&&(r||0===r)&&(s.position===rt||s.position===Gi?(s.color||(i=new oe(this.options.color).percBrightness(),s.color=i>180?ft:ct),s.background||(s.background=this.options.color)):(n=o.theme.seriesDefaults.labels,s.color=s.color||n.color,s.background=s.background||n.background),this.label=new Ot(r,It({},s,{align:rt,vAlign:"",animation:{type:we,delay:this.animationDelay}}),e),this.append(this.label)))},reflow:function(t){this.render(),this.box=t,this.reflowLabel()},reflowLabel:function(){var t,e,i,n,s=this,o=s.options.labels,r=s.label,a=this.sector.clone(),h=o.distance,l=a.middle();r&&(t=r.box.height(),e=r.box.width(),o.position===rt?(a.radius=Math.abs((a.radius-t)/2)+t,i=a.point(l),r.reflow(new St(i.x,i.y-t/2,i.x,i.y))):o.position===Gi?(a.radius=a.radius-t/2,i=a.point(l),r.reflow(new St(i.x,i.y-t/2,i.x,i.y))):(i=a.clone().expand(h).point(l),i.x>=a.center.x?(n=i.x+e,r.orientation=gt):(n=i.x-e,r.orientation=lt),r.reflow(new St(n,i.y-t,i.x,i.y))))},createVisual:function(){var t,e,i=this,n=this,s=n.sector,o=n.options;bt.fn.createVisual.call(this),this.value&&(o.visual?(t=(s.startAngle+180)%360,e=o.visual({category:this.category,dataItem:this.dataItem,value:this.value,series:this.series,percentage:this.percentage,center:new ae(s.center.x,s.center.y),radius:s.radius,innerRadius:s.innerRadius,startAngle:t,endAngle:t+s.angle,options:o,sender:this.getSender(),createVisual:function(){var t=new se;return i.createSegmentVisual(t),t}}),e&&this.visual.append(e)):this.createSegmentVisual(this.visual))},createSegmentVisual:function(t){var e=this,i=e.sector,n=e.options,s=n.border||{},o=s.width>0?{stroke:{color:s.color,width:s.width,opacity:s.opacity,dashType:s.dashType}}:{},r=n.color,a={color:r,opacity:n.opacity},l=this.createSegment(i,It({fill:a,stroke:{opacity:n.opacity},zIndex:n.zIndex},o));t.append(l),h(n)&&t.append(this.createGradientOverlay(l,{baseColor:r,fallbackFill:a},It({center:[i.center.x,i.center.y],innerRadius:i.innerRadius,radius:i.radius,userSpace:!0},n.overlay)))},createSegment:function(t,e){return e.singleSegment?new te.Circle(new re.Circle(new ae(t.center.x,t.center.y),t.radius),e):J.ShapeBuilder.current.createRing(t,e)},createAnimation:function(){var t=this,e=t.options,i=t.sector.center;It(e,{animation:{center:[i.x,i.y],delay:this.animationDelay}}),bt.fn.createAnimation.call(this)},createHighlight:function(t){var e=this.options.highlight||{},i=e.border||{};return this.createSegment(this.sector,It({},t,{fill:{color:e.color,opacity:e.opacity},stroke:{opacity:i.opacity,width:i.width,color:i.color}}))},highlightVisual:function(){return this.visual.children[0]},highlightVisualArgs:function(){var t=this.sector;return{options:this.options,radius:t.radius,innerRadius:t.innerRadius,center:new ae(t.center.x,t.center.y),startAngle:t.startAngle,endAngle:t.angle+t.startAngle,visual:this.visual}},tooltipAnchor:function(){var t=this.sector.clone().expand(Se),e=t.middle(),i=t.point(e);return{point:i,align:H(e+180)}},formatValue:function(t){return this.owner.formatPointValue(this,t)},pointData:function(){return{dataItem:this.dataItem,category:this.category,value:this.value,series:this.series,percentage:this.percentage}}}),Ls=Xt(J.rad(30),mt),Ms=Xt(J.rad(60),mt),kt(Vs,{color:ct,overlay:{gradient:"roundedBevel"},border:{width:.5},labels:{visible:!1,distance:35,font:it.DEFAULT_FONT,margin:zt(.5),align:ut,
zIndex:1,position:Wi},animation:{type:$e},highlight:{visible:!0,border:{width:1}},visible:!0}),It(Vs.prototype,Ji),Os={createLegendItem:function(t,e,i){var n,s,o,r,a,h=this.options.legend||{},l=h.labels||{},c=h.inactiveItems||{},u=c.labels||{};i&&i.visibleInLegend!==!1&&(n=i.visible!==!1,s=n?Mt(l):Mt(u)||Mt(l),o=i.category,s&&(o=s({text:o,series:i.series,dataItem:i.dataItem,percentage:i.percentage,value:t})),n?(r={},a=e.color):(r={color:u.color,font:u.font},a=(c.markers||{}).color),p(o)&&""!==o&&this.legendItems.push({active:n,pointIndex:i.index,text:o,series:i.series,markerColor:a,labels:r}))}},Bs=70,zs=bt.extend({init:function(t,e){bt.fn.init.call(this,e),this.plotArea=t,this.chartService=t.chartService,this.points=[],this.legendItems=[],this.render()},render:function(){this.traverseDataPoints(this.addValue.bind(this))},traverseDataPoints:function(t){var e,i,n,s,o,r,a,h,l,c,u,p,d,g,f,v,x,y,_,w,b=this,A=this,S=A.options,C=A.plotArea.options.seriesColors;for(void 0===C&&(C=[]),e=C.length,i=S.series,n=i.length,s=0;s<n;s++)for(o=i[s],r=o.data,a=m(o),h=a.total,l=a.points,c=a.count,u=360/h,p=void 0,isFinite(u)||(p=360/c),d=void 0,d=yt(o.startAngle)?o.startAngle:S.startAngle,s!==n-1&&o.labels.position===Wi&&(o.labels.position=rt),g=0;g<l.length;g++)f=l[g],f&&(v=f.fields,x=f.value,y=f.visible,_=0!==x?p||x*u:0,w=1!==r.length&&!!v.explode,Tt(o.color)||(o.color=v.color||C[g%e]),t(f.valueFields.value,new J.Ring(null,0,0,d,_),{owner:b,category:yt(v.category)?v.category:"",index:g,series:o,seriesIx:s,dataItem:r[g],percentage:0!==h?x/h:0,explode:w,visibleInLegend:v.visibleInLegend,visible:y,zIndex:n-s,animationDelay:b.animationDelay(g,s,n)}),y!==!1&&(d+=_))},evalSegmentOptions:function(t,e,i){var n=i.series;s(t,{value:e,series:n,dataItem:i.dataItem,category:i.category,percentage:i.percentage},{defaults:n._defaults,excluded:["data","content","template","visual","toggle"]})},addValue:function(e,i,n){var s,o=It({},n.series,{index:n.index});this.evalSegmentOptions(o,e,n),this.createLegendItem(e,o,n),n.visible!==!1&&(s=new Vs(e,i,o),t.extend(s,n),this.append(s),this.points.push(s))},reflow:function(t){var e,i,n,s,o,r,a,h,l,c,u,p,d,g,f,v,m,x,y,_,w,b=this,A=b.options,S=b.points,C=b.seriesConfigs;for(void 0===C&&(C=[]),e=S.length,i=t.clone(),n=5,s=Math.min(i.width(),i.height()),o=s/2,r=s-.85*s,a=new St(i.x1,i.y1,i.x1+s,i.y1+s),h=a.center(),l=i.center(),c=A.series.length,u=[],p=[],d=Et(A.padding,r),d=d>o-n?o-n:d,a.translate(l.x-h.x,l.y-h.y),g=o-d,f=new At(g+a.x1+d,g+a.y1+d),v=0;v<e;v++)m=S[v],x=m.sector,y=m.seriesIx,x.radius=g,x.center=f,C.length&&(_=C[y],x.innerRadius=_.innerRadius,x.radius=_.radius),y===c-1&&m.explode&&(x.center=x.clone().setRadius(.15*x.radius).point(x.middle())),m.reflow(a),w=m.label,w&&w.options.position===Wi&&y===c-1&&(w.orientation===gt?p.push(w):u.push(w));u.length>0&&(u.sort(this.labelComparator(!0)),this.leftLabelsReflow(u)),p.length>0&&(p.sort(this.labelComparator(!1)),this.rightLabelsReflow(p)),this.box=a},leftLabelsReflow:function(t){var e=this.distanceBetweenLabels(t);this.distributeLabels(e,t)},rightLabelsReflow:function(t){var e=this.distanceBetweenLabels(t);this.distributeLabels(e,t)},distanceBetweenLabels:function(t){var e,i,n=Vt(this.points),s=n.sector,o=t.length-1,r=s.radius+n.options.labels.distance,a=[],h=t[0].box,l=Xt(h.y1-(s.center.y-r-h.height()-h.height()/2));for(a.push(l),e=0;e<o;e++)i=t[e+1].box,h=t[e].box,l=Xt(i.y1-h.y2),a.push(l);return l=Xt(s.center.y+r-t[o].box.y2-t[o].box.height()/2),a.push(l),a},distributeLabels:function(t,e){var i,n,s,o,r=this,a=t.length;for(o=0;o<a;o++)for(s=-t[o],i=n=o;s>0&&(i>=0||n<a);)s=r._takeDistance(t,o,--i,s),s=r._takeDistance(t,o,++n,s);this.reflowLabels(t,e)},_takeDistance:function(t,e,i,n){var s,o=n;return t[i]>0&&(s=Math.min(t[i],o),o-=s,t[i]-=s,t[e]+=s),o},reflowLabels:function(t,e){var i,n,s,o,r=this,a=Vt(this.points),h=a.sector,l=a.options.labels,c=e.length,u=l.distance,p=h.center.y-(h.radius+u)-e[0].box.height();for(t[0]+=2,n=0;n<c;n++)s=e[n],o=s.box,p+=t[n],i=r.hAlignLabel(o.x2,h.clone().expand(u),p,p+o.height(),s.orientation===gt),s.orientation===gt?(l.align!==ut&&(i=h.radius+h.center.x+u),s.reflow(new St(i+o.width(),p,i,p))):(l.align!==ut&&(i=h.center.x-h.radius-u),s.reflow(new St(i-o.width(),p,i,p))),p+=o.height()},createVisual:function(){var t,e,i,n,s,o,r,a,h,l,c,u,p,d,g=this,f=this,v=f.options.connectors,m=f.points,x=m.length,y=4;for(bt.fn.createVisual.call(this),this._connectorLines=[],t=0;t<x;t++)e=m[t],i=e.sector,n=e.label,s=i.middle(),o=(e.options.connectors||{}).color||v.color,n&&(r=new ee({stroke:{color:o,width:v.width},animation:{type:we,delay:e.animationDelay}}),n.options.position===Wi&&(a=n.box,h=i.center,l=i.point(s),c=new At(a.x1,a.center().y),u=void 0,p=void 0,d=void 0,l=i.clone().expand(v.padding).point(s),r.moveTo(l.x,l.y),n.orientation===gt?(p=new At(a.x1-v.padding,a.center().y),d=F(h,l,c,p),c=new At(p.x-y,p.y),d=d||c,d.x=Math.min(d.x,c.x),g.pointInCircle(d,i.center,i.radius+y)||d.x<i.center.x?(u=i.center.x+i.radius+y,e.options.labels.align!==Ze?u<c.x?r.lineTo(u,l.y):r.lineTo(l.x+2*y,l.y):r.lineTo(u,l.y),r.lineTo(c.x,p.y)):(d.y=p.y,r.lineTo(d.x,d.y))):(p=new At(a.x2+v.padding,a.center().y),d=F(h,l,c,p),c=new At(p.x+y,p.y),d=d||c,d.x=Math.max(d.x,c.x),g.pointInCircle(d,i.center,i.radius+y)||d.x>i.center.x?(u=i.center.x-i.radius-y,e.options.labels.align!==Ze?u>c.x?r.lineTo(u,l.y):r.lineTo(l.x-2*y,l.y):r.lineTo(u,l.y),r.lineTo(c.x,p.y)):(d.y=p.y,r.lineTo(d.x,d.y))),r.lineTo(p.x,p.y),g._connectorLines.push(r),g.visual.append(r)))},labelComparator:function(t){var e=t?-1:1;return function(t,i){var n=(t.parent.sector.middle()+270)%360,s=(i.parent.sector.middle()+270)%360;return(n-s)*e}},hAlignLabel:function(t,e,i,n,s){var o=e.radius,r=e.center,a=r.x,h=r.y,l=Math.min(Math.abs(h-i),Math.abs(h-n));return l>o?t:a+Math.sqrt(o*o-l*l)*(s?1:-1)},pointInCircle:function(t,e,i){return Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)<Math.pow(i,2)},formatPointValue:function(t,e){return this.chartService.format.auto(e,t.value)},animationDelay:function(t){return t*Bs}}),kt(zs,{startAngle:90,connectors:{width:2,color:"#939393",padding:8},inactiveItems:{markers:{},labels:{}}}),It(zs.prototype,Os),Ds=Nn.extend({render:function(){this.createPieChart(this.series)},createPieChart:function(t){var e=t[0],i=new zs(this,{series:t,padding:e.padding,startAngle:e.startAngle,connectors:e.connectors,legend:this.options.legend});this.appendChart(i)},appendChart:function(t,e){Nn.fn.appendChart.call(this,t,e),Ht(this.options.legend.items,t.legendItems)}}),Hs=Vs.extend({reflowLabel:function(){var t,e,i=this,n=i.options.labels,s=i.label,o=this.sector.clone(),r=o.middle();s&&(t=s.box.height(),n.position===rt?(o.radius-=(o.radius-o.innerRadius)/2,e=o.point(r),s.reflow(new St(e.x,e.y-t/2,e.x,e.y))):Vs.fn.reflowLabel.call(this))},createSegment:function(t,e){return J.ShapeBuilder.current.createRing(t,e)}}),kt(Hs,{overlay:{gradient:"roundedGlass"},labels:{position:rt},animation:{type:$e}}),It(Hs.prototype,Ji),Fs=50,Ns=zs.extend({addValue:function(e,i,n){var s,o=It({},n.series,{index:n.index});this.evalSegmentOptions(o,e,n),this.createLegendItem(e,o,n),e&&n.visible!==!1&&(s=new Hs(e,i,o),t.extend(s,n),this.append(s),this.points.push(s))},reflow:function(t){var e,i,n,s,o,r,a,h,l,c,u,p,d=this,g=this.options,f=t.clone(),v=5,m=Math.min(f.width(),f.height()),x=m/2,y=m-.85*m,_=g.series,w=_.length,b=Et(g.padding,y);for(b=b>x-v?x-v:b,e=x-b,i=0,s=0;s<w;s++)o=_[s],0===s&&yt(o.holeSize)&&(n=o.holeSize,e-=o.holeSize),yt(o.size)?e-=o.size:i++,yt(o.margin)&&s!==w-1&&(e-=o.margin);for(yt(n)||(r=(x-b)/(w+.75),n=.75*r,e-=n),a=n,h=0,this.seriesConfigs=[],u=0;u<w;u++)p=_[u],l=Et(p.size,e/i),a+=h,c=a+l,d.seriesConfigs.push({innerRadius:a,radius:c}),h=p.margin||0,a=c;zs.fn.reflow.call(this,t)},animationDelay:function(t,e,i){return t*Fs+_e*(e+1)/(i+1)}}),kt(Ns,{startAngle:90,connectors:{width:2,color:"#939393",padding:8}}),Gs=Ds.extend({render:function(){this.createDonutChart(this.series)},createDonutChart:function(t){var e=t[0],i=new Ns(this,{series:t,padding:e.padding,connectors:e.connectors,legend:this.options.legend});this.appendChart(i)}}),qs=.15,Ws=Nn.extend({initFields:function(){this.valueAxisRangeTracker=new fn},render:function(){this.addToLegend(this.series),this.createPolarAxis(),this.createCharts(),this.createValueAxis()},alignAxes:function(){var t=this.valueAxis,e=t.range(),i=t.options.reverse?e.max:e.min,n=t.getSlot(i),s=this.polarAxis.getSlot(0).center,o=t.box.translate(s.x-n.x1,s.y-n.y1);t.reflow(o)},createValueAxis:function(){var t,e,i,n,s=this.valueAxisRangeTracker,o=s.query(),r=this.valueAxisOptions({roundToMajorUnit:!1,zIndex:-1});r.type===Fi?(t=J.RadarLogarithmicAxis,e={min:.1,max:1}):(t=J.RadarNumericAxis,e={min:0,max:1}),i=s.query(name)||o||e,i&&o&&(i.min=Math.min(i.min,o.min),i.max=Math.max(i.max,o.max)),n=new t(i.min,i.max,r,this.chartService),this.valueAxis=n,this.appendAxis(n)},reflowAxes:function(){var t,e,i=this,n=i.options.plotArea,s=i.valueAxis,o=i.polarAxis,r=i.box,a=Math.min(r.width(),r.height())*qs,h=zt(n.padding||{},a),l=r.clone().unpad(h),c=l.clone();c.y2=c.y1+Math.min(c.width(),c.height()),c.align(l,dt,rt),t=c.clone().shrink(0,c.height()/2),o.reflow(c),s.reflow(t),e=s.lineBox().height()-s.box.height(),s.reflow(s.box.unpad({top:e})),this.axisBox=c,this.alignAxes(c)},backgroundBox:function(){return this.box},detachLabels:function(){}}),Xs=Tn.extend({pointSlot:function(t,e){var i=t.center.y-e.y1,n=At.onCircle(t.center,t.startAngle,i);return new St(n.x,n.y,n.x,n.y)}}),kt(Xs,{clip:!1}),Zs=Rs.extend({}),Zs.prototype.pointSlot=Xs.prototype.pointSlot,kt(Zs,{clip:!1}),Us=dn.extend({fillToAxes:function(t){var e=this._polarAxisCenter();t.lineTo(e.x,e.y)},_polarAxisCenter:function(){var t=this.parent.plotArea.polarAxis,e=t.box.center();return e},strokeSegments:function(){var t,e,i,n=this._strokeSegments;return n||(t=this._polarAxisCenter(),e=new Dt((!1)),i=this.points(),i.push(t),n=this._strokeSegments=e.process(i),n.pop()),n}}),Ys=un.extend({fillToAxes:function(t){var e=this.parent.plotArea.polarAxis,i=e.box.center(),n=new re.Segment([i.x,i.y]);t.segments.unshift(n),t.segments.push(n)}}),js=Zs.extend({createSegment:function(t,e,i){var n,s=(e.line||{}).style;return n=s===De?new Us(t,e,i):new Ys(t,e,i)},createMissingValue:function(t,e){var i;return p(t.x)&&e!==Me&&(i={x:t.x,y:t.y},e===Le&&(i.y=0)),i},seriesMissingValues:function(t){return t.missingValues||Le},_hasMissingValuesGap:function(){var t,e=this,i=this.options.series;for(t=0;t<i.length;t++)if(e.seriesMissingValues(i[t])===Oe)return!0},sortPoints:function(t){var e,i,n,s=this;if(t.sort(N),this._hasMissingValuesGap())for(e=0;e<t.length;e++)i=t[e],i&&(n=i.value,p(n.y)||s.seriesMissingValues(i.series)!==Oe||delete t[e]);return t}}),Ks=Ws.extend({createPolarAxis:function(){var t=new J.PolarAxis(this.options.xAxis,this.chartService);this.polarAxis=t,this.axisX=t,this.appendAxis(t)},valueAxisOptions:function(t){return It(t,{majorGridLines:{type:xt},minorGridLines:{type:xt}},this.options.yAxis)},createValueAxis:function(){Ws.fn.createValueAxis.call(this),this.axisY=this.valueAxis},appendChart:function(t,e){this.valueAxisRangeTracker.update(t.yAxisRanges),Nn.prototype.appendChart.call(this,t,e)},createCharts:function(){var t=this.filterVisibleSeries(this.series),e=this.panes[0];this.createLineChart(y(t,[ti]),e),this.createScatterChart(y(t,[ei]),e),this.createAreaChart(y(t,[Je]),e)},createLineChart:function(t,e){if(0!==t.length){var i=new Zs(this,{series:t});this.appendChart(i,e)}},createScatterChart:function(t,e){if(0!==t.length){var i=new Xs(this,{series:t});this.appendChart(i,e)}},createAreaChart:function(t,e){if(0!==t.length){var i=new js(this,{series:t});this.appendChart(i,e)}},_dispatchEvent:function(t,e,i){var n=t._eventCoordinates(e),s=new At(n.x,n.y),o=this.axisX.getValue(s),r=this.axisY.getValue(s);null!==o&&null!==r&&t.trigger(i,{element:Lt(e),x:o,y:r})},createCrosshairs:function(){}}),kt(Ks,{xAxis:{},yAxis:{}}),It(Ks.prototype,qn),Qs=cn.extend({pointSlot:function(t,e){var i=t.center.y-e.y1,n=At.onCircle(t.center,t.middle(),i);return new St(n.x,n.y,n.x,n.y)},createSegment:function(t,e,i){var n,s,o=e.style;return n=o===De?rn:nn,s=new n(t,e,i),t.length===e.data.length&&(s.options.closed=!0),s}}),kt(Qs,{clip:!1,limitPoints:!1}),$s=dn.extend({fillToAxes:function(){}}),Js=un.extend({fillToAxes:function(){}}),to=Qs.extend({createSegment:function(t,e,i,n){var s,o,r,a=this.options.isStacked,h=(e.line||{}).style;return a&&i>0&&n&&(o=n.linePoints.slice(0),s=n),h===De?(r=new $s(t,e,i,s,o),r.options.closed=!0):(t.push(t[0]),r=new Js(t,e,i,s,o)),r},seriesMissingValues:function(t){return t.missingValues||Le}}),eo=Hs.extend({init:function(t,e){Hs.fn.init.call(this,t,null,e)}}),kt(eo,{overlay:{gradient:"none"},labels:{distance:10}}),io=bt.extend({init:function(t){bt.fn.init.call(this,t),this.forEach=t.rtl?c:l},reflow:function(t){var e=this,i=e.options,n=e.children,s=i.gap,o=i.spacing,r=n.length,a=r+s+o*(r-1),h=t.angle/a,l=t.startAngle+h*(s/2);this.forEach(n,function(e){var i=t.clone();i.startAngle=l,i.angle=h,e.sector&&(i.radius=e.sector.radius),e.reflow(i),e.sector=i,l+=h+h*o})}}),kt(io,{gap:1,spacing:0}),no=bt.extend({reflow:function(t){var e,i,n=this,s=n.options.reverse,o=n.children,r=o.length,a=s?r-1:0,h=s?-1:1;for(this.box=new St,e=a;e>=0&&e<r;e+=h)i=o[e].sector,i.startAngle=t.startAngle,i.angle=t.angle}}),so=wn.extend({pointType:function(){return eo},clusterType:function(){return io},stackType:function(){return no},categorySlot:function(t,e){return t.getSlot(e)},pointSlot:function(t,e){var i=t.clone(),n=t.center.y;return i.radius=n-e.y1,i.innerRadius=n-e.y2,i},reflowPoint:function(t,e){t.sector=e,t.reflow()},createAnimation:function(){this.options.animation.center=this.box.toRect().center(),wn.fn.createAnimation.call(this)}}),so.prototype.reflow=$i.prototype.reflow,kt(so,{clip:!1,limitPoints:!1,animation:{type:"pie"}}),oo=Ws.extend({createPolarAxis:function(){var t=new J.RadarCategoryAxis(this.options.categoryAxis,this.chartService);this.polarAxis=t,this.categoryAxis=t,this.appendAxis(t),this.aggregateCategories(),this.createCategoryAxesLabels()},valueAxisOptions:function(t){return this._hasBarCharts&&It(t,{majorGridLines:{type:xt},minorGridLines:{type:xt}}),this._isStacked100&&It(t,{roundToMajorUnit:!1,labels:{format:"P0"}}),It(t,this.options.valueAxis)},aggregateCategories:function(){ls.prototype.aggregateCategories.call(this,this.panes)},createCategoryAxesLabels:function(){ls.prototype.createCategoryAxesLabels.call(this,this.panes)},filterSeries:function(t){return t},createCharts:function(){var t=this.filterVisibleSeries(this.series),e=this.panes[0];this.createAreaChart(y(t,[ii]),e),this.createLineChart(y(t,[si]),e),this.createBarChart(y(t,[ni]),e)},chartOptions:function(t){var e,i,n={series:t},s=t[0];return s&&(e=this.filterVisibleSeries(t),i=s.stack,n.isStacked=i&&e.length>1,n.isStacked100=i&&"100%"===i.type&&e.length>1,n.isStacked100&&(this._isStacked100=!0)),n},createAreaChart:function(t,e){if(0!==t.length){var i=new to(this,this.chartOptions(t));this.appendChart(i,e)}},createLineChart:function(t,e){if(0!==t.length){var i=new Qs(this,this.chartOptions(t));this.appendChart(i,e)}},createBarChart:function(t,e){var i,n,s;0!==t.length&&(i=t[0],n=this.chartOptions(t),n.gap=i.gap,n.spacing=i.spacing,s=new so(this,n),this.appendChart(s,e),this._hasBarCharts=!0)},seriesCategoryAxis:function(){return this.categoryAxis},_dispatchEvent:function(t,e,i){var n=t._eventCoordinates(e),s=new At(n.x,n.y),o=this.categoryAxis.getCategory(s),r=this.valueAxis.getValue(s);null!==o&&null!==r&&t.trigger(i,{element:Lt(e),category:o,value:r})},createCrosshairs:function(){}}),It(oo.prototype,qn,{appendChart:ls.prototype.appendChart,aggregateSeries:ls.prototype.aggregateSeries,seriesSourcePoints:ls.prototype.seriesSourcePoints}),kt(oo,{categoryAxis:{categories:[]},valueAxis:{}}),ro=bt.extend({init:function(t,e,i){bt.fn.init.call(this,e),this.value=t,this.options.index=i.index},reflow:function(t){var e=this.points,i=this.children[0];this.box=new St(e[0].x,e[0].y,e[1].x,e[2].y),i&&i.reflow(new St(t.x1,e[0].y,t.x2,e[2].y))},createVisual:function(){var t,e=this,i=this.options;bt.fn.createVisual.call(this),t=i.visual?i.visual({category:this.category,dataItem:this.dataItem,value:this.value,series:this.series,percentage:this.percentage,points:this.points,options:i,sender:this.getSender(),createVisual:function(){return e.createPath()}}):this.createPath(),t&&this.visual.append(t)},createPath:function(){var t=this.options,e=t.border,i=ee.fromPoints(this.points,{fill:{color:t.color,opacity:t.opacity},stroke:{color:e.color,opacity:e.opacity,width:e.width}}).close();return i},createHighlight:function(t){return ee.fromPoints(this.points,t)},highlightVisual:function(){return this.visual.children[0]},highlightVisualArgs:function(){var t=ee.fromPoints(this.points).close();return{options:this.options,path:t}},tooltipAnchor:function(){var t=this.box;return{point:new At(t.center().x,t.y1),align:{horizontal:"center",vertical:"top"}}},formatValue:function(t){var e=this;return e.owner.formatPointValue(e,t)}}),kt(ro,{color:ct,border:{width:1}}),It(ro.prototype,Ji),ao=bt.extend({init:function(t,e){bt.fn.init.call(this,e),this.plotArea=t,this.points=[],this.labels=[],this.legendItems=[],this.render()},formatPointValue:function(t,e){return this.chartService.format.auto(e,t.value)},render:function(){var t,e,i,n,s,o,r,a,h,l,c,u=this,p=this,d=p.options,g=p.plotArea.options.seriesColors;if(void 0===g&&(g=[]),t=d.series[0],e=t.data)for(i=m(t),n=i.total,s=i.points,o=0;o<s.length;o++)r=s[o],r&&(a=r.fields,Tt(t.color)||(t.color=a.color||g[o%g.length]),a=It({index:o,owner:u,series:t,dataItem:e[o],percentage:r.value/n},a,{visible:r.visible}),h=r.valueFields.value,l=u.createSegment(h,a),c=u.createLabel(h,a),l&&c&&l.append(c))},evalSegmentOptions:function(t,e,i){var n=i.series;s(t,{value:e,series:n,dataItem:i.dataItem,index:i.index},{defaults:n._defaults,excluded:["data","content","template","toggle","visual"]})},createSegment:function(e,i){var n,s=It({},i.series);if(this.evalSegmentOptions(s,e,i),this.createLegendItem(e,s,i),i.visible!==!1)return n=new ro(e,s,i),t.extend(n,i),this.append(n),this.points.push(n),n},createLabel:function(t,e){var i,n,s,o,r=e.series,a=e.dataItem,h=It({},this.options.labels,r.labels),l=t;if(h.visible)return i=Mt(h),n={dataItem:a,value:t,percentage:e.percentage,category:e.category,series:r},i?l=i(n):h.format&&(l=this.plotArea.chartService.format.auto(h.format,l)),h.color||(s=new oe(r.color).percBrightness(),h.color=s>180?ft:ct,h.background||(h.background=r.color)),this.evalSegmentOptions(h,t,e),o=new Ot(l,It({vAlign:h.position},h),n),this.labels.push(o),o},labelPadding:function(){var t,e,i,n,s=this.labels,o={left:0,right:0};for(t=0;t<s.length;t++)e=s[t],i=e.options.align,i!==rt&&(n=s[t].box.width(),i===lt?o.left=Math.max(o.left,n):o.right=Math.max(o.right,n));return o},dynamicSlopeReflow:function(t,e,i){var n,s,o,r,a,h,l,c,u,p,d,g=this,f=g.options,v=g.points,m=v.length,x=v[0],y=x;for(n=0;n<v.length;n++)v[n].percentage>y.percentage&&(y=v[n]);for(s=x.percentage/y.percentage*e,o=(e-s)/2,r=0,a=0;a<m;a++)h=v[a].percentage,l=v[a+1],c=l?l.percentage:h,u=v[a].points=[],p=f.dynamicHeight?i*h:i/m,d=void 0,d=h?(e-s*(c/h))/2:c?0:e/2,d=Zt(d,0,e),u.push(new ae(t.x1+o,t.y1+r)),u.push(new ae(t.x1+e-o,t.y1+r)),u.push(new ae(t.x1+e-d,t.y1+p+r)),u.push(new ae(t.x1+d,t.y1+p+r)),o=d,r+=p+f.segmentSpacing,s=Zt(e-2*d,0,e)},constantSlopeReflow:function(t,e,i){var n,s,o,r,a,h=this,l=h.options,c=h.points,u=c.length,p=l.neckRatio<=1,d=p?l.neckRatio*e:e,g=p?0:(e-e/l.neckRatio)/2,f=p?e:e-2*g,v=(f-d)/2,m=0;for(n=0;n<u;n++)s=c[n].points=[],o=c[n].percentage,r=l.dynamicHeight?v*o:v/u,a=l.dynamicHeight?i*o:i/u,s.push(new ae(t.x1+g,t.y1+m)),s.push(new ae(t.x1+e-g,t.y1+m)),s.push(new ae(t.x1+e-g-r,t.y1+a+m)),s.push(new ae(t.x1+g+r,t.y1+a+m)),g+=r,m+=a+l.segmentSpacing},reflow:function(t){var e,i,n,s,o,r=this.points,a=r.length;if(a)for(e=this.options,i=t.clone().unpad(this.labelPadding()),n=i.height()-e.segmentSpacing*(a-1),s=i.width(),e.dynamicSlope?this.dynamicSlopeReflow(i,s,n):this.constantSlopeReflow(i,s,n),o=0;o<a;o++)r[o].reflow(t)}}),kt(ao,{neckRatio:.3,width:300,dynamicSlope:!1,dynamicHeight:!0,segmentSpacing:0,labels:{visible:!1,align:rt,position:rt,zIndex:1}}),It(ao.prototype,Os),ho=Nn.extend({render:function(){this.createFunnelChart(this.series)},createFunnelChart:function(t){var e=t[0],i=new ao(this,{series:t,legend:this.options.legend,neckRatio:e.neckRatio,dynamicHeight:e.dynamicHeight,dynamicSlope:e.dynamicSlope,segmentSpacing:e.segmentSpacing,highlight:e.highlight});this.appendChart(i)},appendChart:function(t,e){Nn.fn.appendChart.call(this,t,e),Ht(this.options.legend.items,t.legendItems)}}),lo="color",co="first",uo="from",po="max",go="min",fo="noteText",vo="summary",mo="to",_s.current.register(ls,[Ne,Ze,Ke,di,Fe,ci,Xe,Qe,We,pi,Ge,ui,ai,ri,fi,je,oi,gi]),_s.current.register(Is,[hi,li,qe]),_s.current.register(Ds,[$e]),_s.current.register(Gs,[Ue]),_s.current.register(ho,[Ye]),_s.current.register(Ks,[Je,ti,ei]),_s.current.register(oo,[ii,ni,si]),de.current.register([Ne,Ze,Ke,di,Fe,ci],[ot],[Ni,lo,fo,ke,Pe]),de.current.register([ai,ri,oi,gi],[uo,mo],[Ni,lo,fo]),de.current.register([fi,je],[ot],[Ni,lo,fo,vo]),de.current.register([Je,ti,ei],[pt,dt],[lo]),de.current.register([ii,ni,si],[ot],[lo]),de.current.register([Ye],[ot],[Ni,lo,"visibleInLegend","visible"]),Xn.current.register([Ne,Ze,Ke,di,Fe,ci,fi,je],{value:po,color:co,noteText:co,errorLow:go,errorHigh:po}),Xn.current.register([ai,ri,oi,gi],{from:go,to:po,color:co,noteText:co}),Xn.current.register([ii,ni,si],{value:po,color:co}),de.current.register([hi,li,qe],[pt,dt],[lo,fo,Te,Ee,Re,Ie]),de.current.register([qe],[pt,dt,"size"],[lo,Ni,fo]),de.current.register([Xe,Qe],["open","high","low","close"],[Ni,lo,"downColor",fo]),Xn.current.register([Xe,Qe],{open:po,high:po,low:go,close:po,color:co,downColor:co,noteText:co}),de.current.register([Ge,ui],["lower","q1","median","q3","upper","mean","outliers"],[Ni,lo,fo]),Xn.current.register([Ge,ui],{lower:po,q1:po,median:po,q3:po,upper:po,mean:po,outliers:co,color:co,noteText:co}),de.current.register([We,pi],["current","target"],[Ni,lo,"visibleInLegend",fo]),Xn.current.register([We,pi],{current:po,target:po,color:co,noteText:co}),de.current.register([$e,Ue],[ot],[Ni,lo,"explode","visibleInLegend","visible"]),xo=[Ni,ot,pt,dt],yo="mousemove",_o="contextmenu",wo="mouseleave",bo=20,Ao=tt.extend({init:function(t,e,i,n){var s,o=this;void 0===n&&(n={}),this.observers=[],this.addObserver(n.observer),this.chartService=new Qt.ChartService(this,n),this.chartService.theme=i,this._initElement(t),s=It({},this.options,e),this._originalOptions=It({},s),this._theme=i,this._initTheme(s,i),this._initHandlers(),this._initSurface(),this.bindCategories(),J.FontLoader.preloadFonts(e,function(){o.fontLoaded=!0,o._destroyed||(o.trigger("init"),o._redraw(),o._attachEvents())})},_initElement:function(t){for(this._setElementClass(t),t.style.position="relative";t.firstChild;)t.removeChild(t.firstChild);this.element=t},_setElementClass:function(t){J.addClass(t,"k-chart")},_initTheme:function(e,i){var n,s=[],o=e.series||[];for(n=0;n<o.length;n++)s.push(t.extend({},o[n]));e.series=s,G(e),this.applyDefaults(e,i),null===e.seriesColors&&delete e.seriesColors,this.options=It({},i,e),this.applySeriesColors()},getSize:function(){var t=this.options.chartArea||{},e=t.width?parseInt(t.width,10):Math.floor(this.element.offsetWidth),i=t.height?parseInt(t.height,10):Math.floor(this.element.offsetHeight);return{width:e,height:i}},resize:function(t){var e=this.getSize(),i=this._size;(t||(e.width>0||e.height>0)&&(!i||e.width!==i.width||e.height!==i.height))&&(this._size=e,this._resize(e,t),this.trigger("resize",e))},_resize:function(){this._noTransitionsRedraw()},redraw:function(t){var e,i;this.applyDefaults(this.options),this.applySeriesColors(),t?(e=this._model._plotArea,i=e.findPane(t),e.redraw(i)):this._redraw()},getAxis:function(t){return e(t,this._plotArea.axes)},findAxisByName:function(t){return this.getAxis(t)},findPaneByName:function(t){var e,i=this._plotArea.panes;for(e=0;e<i.length;e++)if(i[e].options.name===t)return new ce(i[e])},findPaneByIndex:function(t){var e=this._plotArea.panes;if(e[t])return new ce(e[t])},plotArea:function(){return new ue(this._plotArea)},toggleHighlight:function(t,e){var i,n,s,o=this._plotArea,r=(o.srcSeries||o.series||[])[0];Tt(e)?i=o.filterPoints(e):(Rt(e)?(n=e.series,s=e.category):n=s=e,i=r.type===Ue?q(o.pointsBySeriesName(n),s):r.type===$e||r.type===Ye?q((o.charts[0]||{}).points,s):o.pointsBySeriesName(n)),i&&this.togglePointsHighlight(t,i)},togglePointsHighlight:function(t,e){var i,n=this._highlight;for(i=0;i<e.length;i++)n.togglePointHighlight(e[i],t)},showTooltip:function(t){var e,i,n,s=this._sharedTooltip(),o=this,r=o._tooltip,a=o._plotArea;Tt(t)?(e=a.findPoint(t),e&&s&&(i=e.categoryIx)):s&&yt(t)&&(i=a.categoryAxis.categoryIndex(t)),s?i>=0&&(n=this._plotArea.pointsByCategoryIndex(i),r.showAt(n)):e&&r.show(e)},hideTooltip:function(){this._tooltip.hide()},_initSurface:function(){var t=this.surface,e=this._surfaceWrap(),i=this.options.chartArea;i.width&&J.elementSize(e,{width:i.width}),i.height&&J.elementSize(e,{height:i.height}),t&&t.options.type===this.options.renderAs?(this.surface.clear(),this.surface.resize()):(this._destroySurface(),this.surface=te.Surface.create(e,{type:this.options.renderAs}),this.surface.bind("mouseenter",this._surfaceMouseenterHandler),this.surface.bind("mouseleave",this._surfaceMouseleaveHandler))},_surfaceWrap:function(){return this.element},_redraw:function(){var t=this._getModel();this._size={width:t.options.width,height:t.options.height},this._destroyView(),this._model=t,this._plotArea=t._plotArea,t.renderVisual(),this.options.transitions!==!1&&t.traverse(function(t){t.animation&&t.animation.setup()}),this._initSurface(),this.surface.draw(t.visual),this.options.transitions!==!1&&t.traverse(function(t){t.animation&&t.animation.play()}),this._tooltip=this._createTooltip(),this._highlight=new cs,this._setupSelection(),this._createPannable(),this._createZoomSelection(),this._createMousewheelZoom(),this.trigger(Bi),$(this._plotArea.panes),this._navState||this._cancelDomEvents()},exportVisual:function(t){var e,i,n,s;return t&&(t.width||t.height||t.options)?(i=this.options,n=It({},t.options,{chartArea:{width:t.width,height:t.height}}),Q(this._originalOptions,n),this.options=It({},this._originalOptions,n),this._initTheme(this.options,this._theme),this.bindCategories(),s=this._getModel(),s.renderVisual(),$(s._plotArea.panes),e=s.visual,this.options=i):e=this.surface.exportVisual(),e},_sharedTooltip:function(){return this._plotArea instanceof ls&&this.options.tooltip.shared},_createPannable:function(){var t=this.options;t.pannable!==!1&&(this._pannable=new us(this._plotArea,t.pannable))},_createZoomSelection:function(){var t=this.options.zoomable,e=(t||{}).selection;t!==!1&&e!==!1&&(this._zoomSelection=new ps(this,e))},_createMousewheelZoom:function(){var t=this.options.zoomable,e=(t||{}).mousewheel;t!==!1&&e!==!1&&(this._mousewheelZoom=new ds(this,e))},_toggleDragZoomEvents:function(){var t=this.options.pannable,e=this.options.zoomable,i=(e||{}).selection,n=(e||{}).mousewheel,s=!(t||e!==!1&&i!==!1||this.requiresHandlers([Ei,Pi,Ti])),o=(e===!1||n===!1)&&!this.requiresHandlers([Ri,Ii,Vi]),r=this.element;this._dragZoomEnabled&&s&&o?(r.style.touchAction=this._touchAction||"",this._dragZoomEnabled=!1):this._dragZoomEnabled||s&&o||(r.style.touchAction="none",this._dragZoomEnabled=!0),this._toggleDomEvents(!s,!o)},_toggleDomEvents:function(t,e){var i=this.domEvents;i&&(i.toggleDrag&&i.toggleDrag(t),i.toggleZoom&&i.toggleZoom(e))},_createTooltip:function(){var t,e=this,i=e.options.tooltip;return t=this._sharedTooltip()?this._createSharedTooltip(i):new Ss(this.chartService,i)},_createSharedTooltip:function(t){return new Cs(this._plotArea,t)},applyDefaults:function(t,e){W(t,e),X(t,e)},applySeriesColors:function(){var t,e,i,n,s=this.options,o=s.series,r=s.seriesColors||[];for(t=0;t<o.length;t++)e=o[t],i=r[t%r.length],n=e._defaults,e.color=e.color||i,n&&(n.color=n.color||i)},_getModel:function(){var t=this.options,e=this._createPlotArea(),i=new J.RootElement(this._modelOptions());return i.chart=this,i._plotArea=e,J.Title.buildTitle(t.title,i),t.legend.visible&&i.append(new ys(e.options.legend,this.chartService)),i.append(e),i.reflow(),i},_modelOptions:function(){var t=this.options,e=this.getSize();return It({transitions:t.transitions,width:e.width||it.DEFAULT_WIDTH,height:e.height||it.DEFAULT_HEIGHT},t.chartArea)},_createPlotArea:function(t){var e=this.options,i=_s.current.create(t?[]:e.series,e,this.chartService);return i},_setupSelection:function(){var t,e,i,n,s,o=this,r=this,a=r._plotArea.axes,h=this._selections=[];for(t=0;t<a.length;t++)e=a[t],i=e.options,e instanceof qt&&i.select&&!i.vertical&&(n=e.range(),s=new As(o,e,It({min:n.min,max:n.max},i.select)),h.push(s))},_selectStart:function(t){return this.trigger(Li,t)},_select:function(t){return this.trigger(Mi,t)},_selectEnd:function(t){return this.trigger(Oi,t)},_initHandlers:function(){this._clickHandler=this._click.bind(this),this._mousewheelHandler=this._mousewheel.bind(this),this._mouseleaveHandler=this._mouseleave.bind(this),this._surfaceMouseenterHandler=this._mouseover.bind(this),this._surfaceMouseleaveHandler=this._mouseout.bind(this),this._mousemove=kendo.throttle(this._mousemove.bind(this),bo)},addObserver:function(t){t&&this.observers.push(t)},removeObserver:function(t){var e=this.observers.indexOf(t);e>=0&&this.observers.splice(e,1)},requiresHandlers:function(t){var e,i=this.observers;for(e=0;e<i.length;e++)if(i[e].requiresHandlers(t))return!0},trigger:function(t,e){var i,n,s;for(void 0===e&&(e={}),t===zi&&(e.anchor.point=this._toDocumentCoordinates(e.anchor.point)),e.sender=this,i=this.observers,n=!1,s=0;s<i.length;s++)i[s].trigger(t,e)&&(n=!0);return n},_attachEvents:function(){var t,e,i=this.element;this._touchAction=i.style.touchAction,Kt(i,(t={},t[_o]=this._clickHandler,t[Xi]=this._mousewheelHandler,t[wo]=this._mouseleaveHandler,t)),this._shouldAttachMouseMove()&&Kt(i,(e={},e[yo]=this._mousemove,e)),this.domEvents=Qt.DomEventsBuilder.create(this.element,{start:this._start.bind(this),move:this._move.bind(this),end:this._end.bind(this),tap:this._tap.bind(this),gesturestart:this._gesturestart.bind(this),gesturechange:this._gesturechange.bind(this),gestureend:this._gestureend.bind(this)}),this._toggleDragZoomEvents()},_mouseleave:function(t){this._hoveredPoint&&(this._hoveredPoint.out(this,t),this._hoveredPoint=null),this._plotArea.hovered&&(this.trigger(ki),this._plotArea.hovered=!1)},_cancelDomEvents:function(){this.domEvents&&this.domEvents.cancel&&this.domEvents.cancel()},_gesturestart:function(t){this._mousewheelZoom&&!this._stopChartHandlers(t)&&(this._gestureDistance=t.distance,this._unsetActivePoint(),this.surface.suspendTracking())},_gestureend:function(t){this._zooming&&!this._stopChartHandlers(t)&&(this.surface&&this.surface.resumeTracking(),this._zooming=!1,this.trigger(Vi,{}))},_gesturechange:function(t){var e,i,n,s,o=this._mousewheelZoom;o&&!this._stopChartHandlers(t)&&(t.preventDefault(),e=this._gestureDistance,i=-t.distance/e+1,Math.abs(i)>=.1&&(i=Math.round(10*i),this._gestureDistance=t.distance,n={delta:i,axisRanges:U(this._plotArea.axes),originalEvent:t},!this._zooming&&this.trigger(Ri,n)||(this._zooming||(this._zooming=!0),s=n.axisRanges=o.updateRanges(i),s&&!this.trigger(Ii,n)&&o.zoom())))},_mouseout:function(t){if(t.element){var e=this._drawingChartElement(t.element,t);e&&e.leave&&e.leave(this,t.originalEvent)}},_start:function(t){var e=this._eventCoordinates(t);!this._stopChartHandlers(t)&&this._plotArea.backgroundContainsPoint(e)&&(this.requiresHandlers([Ei,Pi,Ti])&&this._startNavigation(t,e,Ei),this._pannable&&this._pannable.start(t)&&(this.surface.suspendTracking(),this._unsetActivePoint(),this._suppressHover=!0,this.chartService.panning=!0),this._zoomSelection&&this._zoomSelection.start(t)&&this.trigger(Ri,{
axisRanges:U(this._plotArea.axes),originalEvent:t}))},_move:function(t){var e,i,n,s,o,r,a,h,l=this,c=l._navState,u=l._pannable;if(!this._stopChartHandlers(t)){if(u)e=u.move(t),e&&!this.trigger(Pi,{axisRanges:e,originalEvent:t})&&u.pan();else if(c){for(i={},n=c.axes,s=0;s<n.length;s++)o=n[s],r=o.options.name,r&&(a=o.options.vertical?t.y:t.x,h=a.startLocation-a.location,0!==h&&(i[o.options.name]=o.translateRange(h)));c.axisRanges=i,this.trigger(Pi,{axisRanges:i,originalEvent:t})}this._zoomSelection&&this._zoomSelection.move(t)}},_end:function(t){var e,i;this._stopChartHandlers(t)||(e=this._pannable,e&&e.end(t)?(this.surface.resumeTracking(),this.trigger(Ti,{axisRanges:U(this._plotArea.axes),originalEvent:t}),this._suppressHover=!1,this.chartService.panning=!1):this._endNavigation(t,Ti),this._zoomSelection&&(i=this._zoomSelection.end(t),i&&!this.trigger(Ii,{axisRanges:i,originalEvent:t})&&(this._zoomSelection.zoom(),this.trigger(Vi,{axisRanges:i,originalEvent:t}))))},_stopChartHandlers:function(t){var e,i,n,s=this._selections||[];if(!s.length)return!1;if(e=this._eventCoordinates(t),i=this._plotArea.paneByPoint(e))for(n=0;n<s.length;n++)if(s[n].onPane(i))return!0},_mousewheel:function(t){var e,i,n,s,o,r,a,h,l,c=this,u=J.mousewheelDelta(t),p=this._mousewheelZoom,d=this._eventCoordinates(t);if(!this._stopChartHandlers(t)&&this._plotArea.backgroundContainsPoint(d))if(p)e={delta:u,axisRanges:U(this._plotArea.axes),originalEvent:t},!this._zooming&&this.trigger(Ri,e)||(t.preventDefault(),this._zooming||(this._unsetActivePoint(),this.surface.suspendTracking(),this._zooming=!0),this._mwTimeout&&clearTimeout(this._mwTimeout),e.axisRanges=p.updateRanges(u),e.axisRanges&&!this.trigger(Ii,e)&&p.zoom(),this._mwTimeout=setTimeout(function(){c.trigger(Vi,e),c._zooming=!1,c.surface&&c.surface.resumeTracking()},Zi));else if(i=this._navState,i||(n=this._startNavigation(t,d,Ri),n||(i=this._navState)),i){for(s=i.totalDelta||u,i.totalDelta=s+u,o=this._navState.axes,r={},a=0;a<o.length;a++)h=o[a],l=h.options.name,l&&(r[l]=h.scaleRange(-s));this.trigger(Ii,{delta:u,axisRanges:r,originalEvent:t}),this._mwTimeout&&clearTimeout(this._mwTimeout),this._mwTimeout=setTimeout(function(){c._endNavigation(t,Vi)},Zi)}},_startNavigation:function(t,e,i){var n,s,o=this._model._plotArea,r=o.findPointPane(e),a=o.axes.slice(0);r&&(n=U(a),s=this.trigger(i,{axisRanges:n,originalEvent:t}),s?this._cancelDomEvents():(this._suppressHover=!0,this._unsetActivePoint(),this._navState={axisRanges:n,pane:r,axes:a}))},_endNavigation:function(t,e){this._navState&&(this.trigger(e,{axisRanges:this._navState.axisRanges,originalEvent:t}),this._suppressHover=!1,this._navState=null)},_getChartElement:function(t,e){var i=this.surface.eventTarget(t);if(i)return this._drawingChartElement(i,t,e)},_drawingChartElement:function(t,e,i){for(var n,s=t;s&&!n;)n=s.chartElement,s=s.parent;if(n)return n.aliasFor&&(n=n.aliasFor(e,this._eventCoordinates(e))),i&&(n=n.closest(i),n&&n.aliasFor&&(n=n.aliasFor())),n},_eventCoordinates:function(t){var e=J.eventCoordinates(t);return this._toModelCoordinates(e.x,e.y)},_elementPadding:function(){var t,e,i;return this._padding||(t=Yt(this.element,["paddingLeft","paddingTop"]),e=t.paddingLeft,i=t.paddingTop,this._padding={top:i,left:e}),this._padding},_toDocumentCoordinates:function(t){var e=this._elementPadding(),i=J.elementOffset(this.element);return{left:Xt(t.x+e.left+i.left),top:Xt(t.y+e.top+i.top)}},_toModelCoordinates:function(t,e){var i=this.element,n=J.elementOffset(i),s=this._elementPadding();return new At(t-n.left-s.left,e-n.top-s.top)},_tap:function(t){var e=this,i=this.surface.eventTarget(t),n=this._drawingChartElement(i,t),s=this._sharedTooltip();this._startHover(i,t)||s||this._unsetActivePoint(),s&&this._trackSharedTooltip(this._eventCoordinates(t),t,!0),this._propagateClick(n,t),this.handlingTap=!0,setTimeout(function(){e.handlingTap=!1},0)},_click:function(t){var e=this._getChartElement(t);this._propagateClick(e,t)},_propagateClick:function(t,e){for(var i=this,n=t;n;)n.click&&n.click(i,e),n=n.parent},_startHover:function(t,e){var i,n,s,o;return!this._suppressHover&&(i=this._drawingChartElement(t,e,function(t){return(t.hover||t.over)&&!(t instanceof Nn)}),n=this._activePoint,s=this._hoveredPoint,s&&s!==i&&(s.out(this,e),this._hoveredPoint=null),i&&s!==i&&i.over&&(this._hoveredPoint=i,i.over(this,e)),i&&n!==i&&i.hover&&(this._activePoint=i,this._sharedTooltip()||i.hover(this,e)||(o=It({},this.options.tooltip,i.options.tooltip),o.visible&&this._tooltip.show(i),this._highlight.show(i))),i)},_mouseover:function(t){var e,i=this._startHover(t.element,t.originalEvent);i&&i.tooltipTracking&&!this._mouseMoveTrackHandler&&!this._sharedTooltip()&&(this._mouseMoveTrackHandler=this._mouseMoveTracking.bind(this),Kt(document,(e={},e[yo]=this._mouseMoveTrackHandler,e)))},_mouseMoveTracking:function(t){var e,i,n,s=this,o=s.options,r=s._tooltip,a=s._highlight,h=s._activePoint,l=this._eventCoordinates(t);this._plotArea.box.containsPoint(l)?h&&h.tooltipTracking&&h.series&&h.parent.getNearestPoint&&(e=h.parent.getNearestPoint(l.x,l.y,h.seriesIx),e&&e!==h&&(this._activePoint=e,e.hover(this,t)||(i=It({},o.tooltip,e.options.tooltip),i.visible&&r.show(e),a.show(e)))):($t(document,(n={},n[yo]=this._mouseMoveTrackHandler,n)),this._unsetActivePoint(),this._mouseMoveTrackHandler=null)},_mousemove:function(t){var e,i=this._eventCoordinates(t),n=this._plotArea;this._trackCrosshairs(i),n.hover&&(e=n.backgroundContainsPoint(i),e?(n.hovered=!0,this._plotArea.hover(this,t)):n.hovered&&!e&&(this.trigger(ki),n.hovered=!1)),this._sharedTooltip()&&this._trackSharedTooltip(i,t)},_trackCrosshairs:function(t){var e,i,n=this._plotArea.crosshairs;for(e=0;e<n.length;e++)i=n[e],i.box.containsPoint(t)?i.showAt(t):i.hide()},_trackSharedTooltip:function(t,e,i){var n,s,o,r,a,h,l,c,u,p;this._suppressHover||(n=this,s=n.options.tooltip,o=n._plotArea,r=n._plotArea.categoryAxis,a=n._tooltip,h=n._highlight,o.backgroundContainsPoint(t)?(l=r.pointCategoryIndex(t),l!==this._tooltipCategoryIx||!this._sharedHighlight&&i?(c=o.pointsByCategoryIndex(l),u=c.map(function(t){return t.eventArgs(e)}),p=u[0]||{},p.categoryPoints=u,c.length>0&&!this.trigger(wi,p)?(s.visible&&a.showAt(c,t),h.show(c),this._sharedHighlight=!0):a.hide(),this._tooltipCategoryIx=l):i&&this._sharedHighlight&&(h.hide(),a.hide(),this._sharedHighlight=!1)):this._sharedHighlight&&(h.hide(),a.hide(),this._tooltipCategoryIx=null,this._sharedHighlight=!1))},hideElements:function(){var t=this._plotArea;this._mousemove.cancel(),t.hideCrosshairs(),this._unsetActivePoint()},_unsetActivePoint:function(){var t=this,e=t._tooltip,i=t._highlight;this._activePoint=null,this._hoveredPoint=null,e&&e.hide(),this._tooltipCategoryIx=null,this._sharedHighlight=!1,i&&i.hide()},_deferRedraw:function(){this._redraw()},_clearRedrawTimeout:function(){this._redrawTimeout&&(clearInterval(this._redrawTimeout),this._redrawTimeout=null)},bindCategories:function(){var t,e,i=this,n=this.options,s=[].concat(n.categoryAxis);for(t=0;t<s.length;t++)e=s[t],e.autoBind!==!1&&i.bindCategoryAxisFromSeries(e,t)},bindCategoryAxisFromSeries:function(t,e){var i,s,o,r,a,h,l,c,u,p,d,g,f=this,v=this.options.series,m=v.length,x=new J.HashMap,y=[],b=!1;for(s=0;s<m;s++)if(o=v[s],r=o.categoryAxis===t.name||!o.categoryAxis&&0===e,a=o.data,h=a.length,l=o.categoryField&&r,b=l||b,l&&h>0)for(i=w(t,n(o.categoryField,a[0])),c=i?_:n,u=0;u<h;u++)p=a[u],d=c(o.categoryField,p,f.chartService.intl),!i&&x.get(d)||(y.push([d,p]),i||x.set(d,!0));y.length>0?(i&&(y=j(y,function(t,e){return J.dateComparer(t[0],e[0])})),g=K(y),t.categories=g[0]):b&&(t.categories=[])},_isBindable:function(t){var e,i,n=de.current.valueFields(t),s=!0;for(e=0;e<n.length;e++)if(i=n[e],i===ot?i="field":i+="Field",!yt(t[i])){s=!1;break}return s},_noTransitionsRedraw:function(){var t,e=this.options;e.transitions&&(e.transitions=!1,t=!0),this._redraw(),t&&(e.transitions=!0)},_legendItemHover:function(t,e){var i,n=this,s=n._plotArea,o=n._highlight,r=(s.srcSeries||s.series)[t];i=Pt(r.type,[$e,Ue,Ye])?s.findPoint(function(i){return i.series.index===t&&i.index===e}):s.pointsBySeriesIndex(t),o.show(i)},_shouldAttachMouseMove:function(){return this._plotArea.crosshairs.length||this._tooltip&&this._sharedTooltip()||this.requiresHandlers([Ci,ki])},updateMouseMoveHandler:function(){var t,e;$t(this.element,(t={},t[yo]=this._mousemove,t)),this._shouldAttachMouseMove()&&Kt(this.element,(e={},e[yo]=this._mousemove,e))},applyOptions:function(t,e){Q(this._originalOptions,t),this._originalOptions=It(this._originalOptions,t),this.options=It({},this._originalOptions),e&&(this._theme=e,this.chartService.theme=e),this._initTheme(this.options,this._theme),this._toggleDragZoomEvents()},setOptions:function(t,e){this.applyOptions(t,e),this.bindCategories(),this.redraw(),this.updateMouseMoveHandler()},setDirection:function(t){this.chartService.rtl=!!t,this.surface&&"svg"===this.surface.type&&this._destroySurface()},setIntlService:function(t){this.chartService.intl=t},noTransitionsRedraw:function(){this._noTransitionsRedraw()},destroy:function(){var t,e;this._destroyed=!0,$t(this.element,(t={},t[_o]=this._clickHandler,t[Xi]=this._mousewheelHandler,t[yo]=this._mousemove,t[wo]=this._mouseleaveHandler,t)),this.domEvents&&(this.domEvents.destroy(),delete this.domEvents),this._mouseMoveTrackHandler&&$t(document,(e={},e[yo]=this._mouseMoveTrackHandler,e)),this._destroyView(),this._destroySurface(),this._clearRedrawTimeout()},_destroySurface:function(){var t=this.surface;t&&(t.unbind("mouseenter",this._surfaceMouseenterHandler),t.unbind("mouseleave",this._surfaceMouseleaveHandler),t.destroy(),this.surface=null)},_destroyView:function(){var t=this,e=t._model,i=t._selections;if(e&&(e.destroy(),this._model=null),i)for(;i.length>0;)i.shift().destroy();this._unsetActivePoint(),this._tooltip&&this._tooltip.destroy(),this._highlight&&this._highlight.destroy(),this._zoomSelection&&(this._zoomSelection.destroy(),delete this._zoomSelection),this._pannable&&(this._pannable.destroy(),delete this._pannable),this._mousewheelZoom&&(this._mousewheelZoom.destroy(),delete this._mousewheelZoom)}}),So=["data","categories"],kt(Ao,{renderAs:"",chartArea:{},legend:{visible:!0,labels:{}},categoryAxis:{},seriesDefaults:{type:Ze,data:[],highlight:{visible:!0},labels:{},negativeValues:{visible:!1}},series:[],seriesColors:null,tooltip:{visible:!1},transitions:!0,valueAxis:{},plotArea:{},title:{},xAxis:{},yAxis:{},panes:[{}],pannable:!1,zoomable:!1}),kendo.deepExtend(kendo.dataviz,{constants:Ui,Aggregates:pe,AreaChart:gn,AreaSegment:un,AxisGroupRangeTracker:fn,Bar:xn,BarChart:wn,BarLabel:vn,BoxPlotChart:kn,BoxPlot:Sn,BubbleChart:Rn,Bullet:Vn,BulletChart:Ln,CandlestickChart:An,Candlestick:bn,CategoricalChart:$i,CategoricalErrorBar:Ki,CategoricalPlotArea:ls,Chart:Ao,ChartContainer:zn,ClipAnimation:hn,ClusterLayout:yn,Crosshair:Bn,CrosshairTooltip:On,DefaultAggregates:Xn,DonutChart:Ns,DonutPlotArea:Gs,DonutSegment:Hs,ErrorBarBase:ji,ErrorRangeCalculator:xe,Highlight:cs,SharedTooltip:Cs,Legend:ys,LegendItem:fs,LegendLayout:gs,LineChart:cn,LinePoint:en,LineSegment:nn,Pane:Dn,PieAnimation:Es,PieChart:zs,PieChartMixin:Os,PiePlotArea:Ds,PieSegment:Vs,PlotAreaBase:Nn,PlotAreaEventsMixin:qn,PlotAreaFactory:_s,PointEventsMixin:Ji,RangeBar:Zn,RangeBarChart:Un,RangeAreaPoint:$n,RangeAreaChart:is,ScatterChart:Tn,ScatterErrorBar:Pn,ScatterLineChart:Rs,Selection:As,SeriesAggregator:Wn,SeriesBinder:de,SplineSegment:rn,SplineAreaSegment:dn,StackWrap:_n,Tooltip:Ss,OHLCChart:ss,OHLCPoint:ns,WaterfallChart:rs,WaterfallSegment:os,XYPlotArea:Is,MousewheelZoom:ds,ZoomSelection:ps,Pannable:us,ChartAxis:le,ChartPane:ce,ChartPlotArea:ue,findAxisByName:e,anyHasZIndex:a,appendIfNotNull:f,areNumbers:u,bindSegments:m,categoriesCount:o,countNumbers:i,equalsIgnoreCase:x,evalOptions:s,filterSeriesByType:y,getDateField:_,getField:n,hasGradientOverlay:h,hasValue:p,isDateAxis:w,segmentVisible:v,singleItemOrArray:b,createOutOfRangePoints:k})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/chart/chart.min",["dataviz/chart/kendo-chart.min","kendo.data.min","kendo.dataviz.core.min","kendo.dataviz.themes.min","kendo.drawing.min","kendo.userevents.min"],t)}(function(){return function(t,e){function i(t){a.fn[t]=function(){var e=this._instance;if(e)return e[t].apply(e,arguments)}}function n(t,i){var n,s,o,r=[],a=t.groupNameTemplate,h=i.length;if(0===h)return o=P({},t),o.visibleInLegend=!1,[o];for(T(a)?(m.logToConsole("'groupNameTemplate' is obsolete and will be removed in future versions. Specify the group name template as 'series.name'"),a&&(n=B(a))):(n=B(t.name||""),0===n._slotCount&&(n=B(T(t.name)?"#= group.value #: #= series.name #":"#= group.value #"))),s=0;s<h;s++)o=P({},t),m.isFunction(o.color)||(o.color=e),o._groupIx=s,o._groupValue=i[s].value,r.push(o),n&&(o.name=n({series:o,group:i[s]}));return r}function s(t){for(var i in t)t[i]===e&&(t[i]="");return t}function o(t){for(var e=0;e<t.length;e++)t[e].notifyRender()}var r,a,h,l,c,u,p,d,g,f,v=".kendoChart",m=window.kendo,x=m.Class,y=m._outerWidth,_=m._outerHeight,w=m.dataviz,b=w.constants,A=w.Chart,S=w.SeriesBinder,C=m.ui.Widget,k=m.data.DataSource,P=m.deepExtend,T=w.defined,E=w.getField,R=w.InstanceObserver,I=w.inArray,V=w.services,L=t.proxy,M=t.isArray,O=t.extend,B=m.template,z="mouseleave"+v,D=b.AXIS_LABEL_CLICK,H=b.LEGEND_ITEM_CLICK,F=b.LEGEND_ITEM_HOVER,N=b.LEGEND_ITEM_LEAVE,G=b.SERIES_CLICK,q=b.SERIES_HOVER,W=b.SERIES_OVER,X=b.SERIES_LEAVE,Z=b.PANE_RENDER,U=b.PLOT_AREA_CLICK,Y=b.PLOT_AREA_HOVER,j=b.PLOT_AREA_LEAVE,K=b.DRAG,Q=b.DRAG_END,$=b.DRAG_START,J=b.ZOOM_START,tt=b.ZOOM,et=b.ZOOM_END,it=b.SELECT_START,nt=b.SELECT,st=b.SELECT_END,ot=b.RENDER,rt=b.NOTE_CLICK,at=b.NOTE_HOVER,ht=b.NOTE_LEAVE,lt="change",ct="dataBound",ut="leave",pt=b.VALUE,dt=b.PIE,gt=b.DONUT,ft=b.FUNNEL,vt=m.Observable,mt=150,xt=100,yt="k-chart-tooltip-inverse",_t="k-chart-shared-tooltip",wt="rtl";for(V.DomEventsBuilder.register({create:function(t,e){return new m.UserEvents(t,P({global:!0,multiTouch:!0,fastTap:!0},e))}}),r=R.extend({handlerMap:{showTooltip:"_showTooltip",hideTooltip:"_hideTooltip",legendItemClick:"_onLegendItemClick",render:"_onRender",init:"_onInit"}}),a=C.extend({init:function(t,e){var i;m.destroy(t),C.fn.init.call(this,t),e&&(i=e.dataSource,delete e.dataSource),this.options=P({},this.options,e),this.wrapper=this.element,this._attachEvents(),e&&(e.dataSource=i),this._seriesVisibility=new c,this.bind(this.events,this.options),this._initDataSource(e),m.notify(this,w.ui)},events:[ct,G,q,W,X,D,H,F,N,Z,U,Y,j,$,K,Q,J,tt,et,it,nt,st,rt,at,ht,ot],options:{name:"Chart",renderAs:"",theme:"default",axisDefaults:{},chartArea:{},legend:{},categoryAxis:{},autoBind:!0,seriesDefaults:{},series:[],seriesColors:null,tooltip:{},transitions:!0,valueAxis:{},plotArea:{},title:{},xAxis:{},yAxis:{},panes:[{}],pannable:!1,zoomable:!1},items:function(){return t()},refresh:function(){var t=this,e=t._instance;e.applyDefaults(t.options),e.applySeriesColors(),t._bindSeries(),t._bindCategories(),t.trigger(ct),t._redraw()},getSize:function(){return m.dimensions(this.element)},redraw:function(t){this._size=null,this._instance.redraw(t)},setOptions:function(t){var e=this,i=t.dataSource;delete t.dataSource,C.fn._setEvents.call(e,t),this._instance.applyOptions(t,this._getThemeOptions(t)),this.options=this._instance.options,this._tooltip.setOptions(this.options.tooltip),this._seriesVisibility.setOptions(this.options),this._sourceSeries=null,i&&e.setDataSource(i),e._hasDataSource?e._onDataChanged():(e._bindCategories(),e.redraw()),e._instance.updateMouseMoveHandler()},setDataSource:function(t){var e=this;e.dataSource.unbind(lt,e._dataChangeHandler),e.dataSource=t=k.create(t),e._hasDataSource=!0,e._hasData=!1,t.bind(lt,e._dataChangeHandler),e.options.autoBind&&t.fetch()},destroy:function(){var t=this,e=t.dataSource;t.element.off(v),e&&e.unbind(lt,t._dataChangeHandler),t._instance&&(t._instance.destroy(),delete this._instance),this._tooltip&&(this._tooltip.destroy(),delete this._tooltip),this._destroyCrosshairTooltips(),C.fn.destroy.call(t)},findPaneByName:function(t){var e,i=this._plotArea.panes;for(e=0;e<i.length;e++)if(i[e].options.name===t)return new g(this,i[e])},findPaneByIndex:function(t){var e=this._plotArea.panes;if(e[t])return new g(this,e[t])},findSeries:function(t){var e,i=this._plotArea,n=i.srcSeries||i.series;for(e=0;e<n.length;e++)if(t(n[e]))return new f(this,n[e])},findSeriesByName:function(t){return this._createSeries({name:t})},findSeriesByIndex:function(t){return this._createSeries({index:t})},exportVisual:function(t){var e,i,n,s,r=this._instance;if(r)return t&&(t.width||t.height)?(i=r.options.chartArea,n=r._originalOptions.chartArea,P(i,t),s=r._getModel(),i.width=n.width,i.height=n.height,s.renderVisual(),o(s._plotArea.panes),e=s.visual):e=r.exportVisual(),e},_createSeries:function(t){var e=this._seriesOptions(t);if(e)return new f(this,e)},_seriesOptions:function(t){var e,i,n=this._plotArea,s=n.srcSeries||n.series;if(T(t.index))e=s[t.index];else if(T(t.name))for(i=0;i<s.length;i++)if(s[i].name===t.name){e=s[i];break}return e},_attachEvents:function(){this.element.on(z,L(this._mouseleave,this))},_mouseleave:function(e){var i=this._instance,n=this._tooltip,s=e.relatedTarget;s&&t(s).closest(n.element).length||!i||i.handlingTap||i.hideElements()},_getThemeOptions:function(t){var i,n,s=(t||{}).theme;return s&&w.SASS_THEMES.indexOf(s.toLowerCase())!==-1?w.autoTheme().chart:T(s)?(i=w.ui.themes||{},n=i[s]||i[s.toLowerCase()]||{},n.chart||{}):e},_initChart:function(){this._createChart(this.options,this._getThemeOptions(this.options)),this.options=this._instance.options,this._seriesVisibility.setOptions(this.options)},_createChart:function(t,e){this._instance=new A(this.element[0],t,e,{observer:new r(this),sender:this,rtl:this._isRtl()})},_onInit:function(t){this._instance=t.sender},_initDataSource:function(t){var e=this,i=(t||{}).dataSource;e._dataChangeHandler=L(e._onDataChanged,e),e.dataSource=k.create(i).bind("change",e._dataChangeHandler),e._bindCategories(),i&&(e._hasDataSource=!0),this._initChart(),this._initTooltip(),i&&e.options.autoBind&&e.dataSource.fetch()},_destroyCrosshairTooltips:function(){var t,e=this._crosshairTooltips;if(e)for(t in e)e[t].destroy();this._crosshairTooltips={}},_getCrosshairTooltip:function(t,e){var i=this._crosshairTooltips=this._crosshairTooltips||{},n=t+e,s=i[n];return s||(s=i[n]=new d(this.element)),s},_showTooltip:function(t){if(t.crosshair){var e=this._getCrosshairTooltip(t.axisName,t.axisIndex);e.show(t)}else this._tooltip&&this._tooltip.show(t)},_hideTooltip:function(t){if(t.crosshair){var e=this._getCrosshairTooltip(t.axisName,t.axisIndex);e.hide()}else this._tooltip&&this._tooltip.hide(t)},_onRender:function(t){this._destroyCrosshairTooltips(),this._copyMembers(t.sender),this._hasDataSource&&!this._hasData&&this.options.autoBind||this.trigger(ot)},_copyMembers:function(t){this.options=t.options,this._originalOptions=t._originalOptions,this.surface=t.surface,this._plotArea=t._plotArea,this._model=t._model,this._highlight=t._highlight,this._selections=t._selections,this._pannable=t._pannable,this._zoomSelection=t._zoomSelection,this._mousewheelZoom=t._mousewheelZoom},requiresHandlers:function(t){var e,i=this._events;for(e=0;e<t.length;e++)if(T(i[t[e]]))return!0},_initTooltip:function(){this._tooltip=this._createTooltip(),this._tooltip.bind(ut,L(this._tooltipleave,this))},_onLegendItemClick:function(t){this.trigger(H,t)||this._legendItemClick(t.seriesIndex,t.pointIndex)},_legendItemClick:function(e,i){var n,s,o,r=this._instance,a=r._plotArea,h=(a.srcSeries||a.series)[e];t.inArray(h.type,[dt,gt,ft])>=0?(n=h.data[i],n&&T(n.visible)?n.visible=!n.visible:(s=h.pointVisibility=h.pointVisibility||{},o=s[i],s[i]=!!T(o)&&!o)):(h.visible=!h.visible,this._seriesVisibility.save(h)),r._noTransitionsRedraw()},_createTooltip:function(){return new p(this.element,O({},this.options.tooltip,{rtl:this._isRtl()}))},_tooltipleave:function(){this._instance&&this._instance.hideElements()},_bindData:function(t){var e,i,s,o=this,r=o.options,a=o._sourceSeries||r.series,h=a.length,l=o.dataSource.view(),c=(o.dataSource.group()||[]).length>0,u=[],p=this._seriesVisibility;for(p.read(),e=0;e<h;e++)i=a[e],o._isBindable(i)&&c?(s=n(i,l),u=u.concat(s),p.applyByGroup(s,t)):(i=O({},i),u.push(i),p.applyByIndex(i,t));o._sourceSeries=a,r.series=u,this._instance.applySeriesColors(),o._bindSeries(),o._bindCategories(),this._hasData=!0},_onDataChanged:function(t){this._bindData(t),this.trigger(ct),this._instance&&this._instance.fontLoaded&&this._redraw()},_bindSeries:function(){var t,e,i,n,s=this,o=s.dataSource.view(),r=s.options.series,a=r.length;for(t=0;t<a;t++)e=r[t],s._isBindable(e)&&(i=e._groupIx,n=T(i)?(o[i]||{}).items:o,e.autoBind!==!1&&(e.data=n))},_bindCategories:function(){var t,e,i=this,n=i.dataSource.view()||[],s=(i.dataSource.group()||[]).length>0,o=n,r=i.options,a=[].concat(r.categoryAxis);for(s&&n.length&&(o=n[0].items),t=0;t<a.length;t++)e=a[t],e.autoBind!==!1&&i._bindCategoryAxis(e,o,t)},_bindCategoryAxis:function(t,e,i){var n,s,o,r=(e||[]).length;if(t.field)for(t.categories=[],n=0;n<r;n++)o=e[n],s=E(t.field,o),0===n?(t.categories=[s],t.dataItems=[o]):(t.categories.push(s),t.dataItems.push(o));else this._instance&&this._instance.bindCategoryAxisFromSeries(t,i)},_isBindable:function(t){var e,i,n=S.current.valueFields(t),s=!0;for(i=0;i<n.length;i++)if(e=n[i],e===pt?e="field":e+="Field",!T(t[e])){s=!1;break}return s},_isRtl:function(){return m.support.isRtl(this.element)&&this.element.css("direction")===wt}}),h=["getAxis","findAxisByName","plotArea","toggleHighlight","showTooltip","hideTooltip","_resize","_redraw","_noTransitionsRedraw","_legendItemHover","_eventCoordinates"],l=0;l<h.length;l++)i(h[l]);w.ExportMixin.extend(a.fn),m.PDFMixin&&m.PDFMixin.extend(a.fn),w.ui.plugin(a),c=x.extend({init:function(){this.groups={},this.index={},this.options={}},applyByGroup:function(t,e){if(e&&e.action||this.options.persistSeriesVisibility)for(var i=0;i<t.length;i++)this.groups[t[i]._groupValue]===!1&&(t[i].visible=!1);else this.groups={}},applyByIndex:function(t,e){e&&e.action||this.options.persistSeriesVisibility?this.index[t.index]===!1&&(t.visible=!1):this.index={}},save:function(t){t&&(this.options.persistSeriesVisibility?this.options.series[t.index].visible=t.visible:this.saveState(t))},setOptions:function(t){this.options=t,this.groups={},this.index={}},read:function(){var t,e,i=this.options;if(i.persistSeriesVisibility)for(t=i.series,e=0;e<t.length;e++)this.saveState(t[e])},saveState:function(t){T(t._groupValue)?this.groups[t._groupValue]=t.visible:this.index[t.index]=t.visible}}),u=m.geometry,p=vt.extend({init:function(e,i){var n,s=this;vt.fn.init.call(s),this.setOptions(i),s.chartElement=e,s.template=p.template,s.template||(s.template=p.template=m.template("<div class='k-tooltip k-chart-tooltip#= d.rtl ? \" k-rtl\" : \"\"#' style='display:none; position: absolute; font: #= d.font #;#if (d.border) {# border: #= d.border.width #px solid; #}#opacity: #= d.opacity #; filter: alpha(opacity=#= d.opacity * 100 #);'></div>",{useWithBlock:!1,paramName:"d"})),s.element=t(s.template(s.options)),s.move=L(s.move,s),s._mouseleave=L(s._mouseleave,s),n=m.format("[{0}='content'],[{0}='scroller']",m.attr("role")),s._mobileScroller=e.closest(n).data("kendoMobileScroller")},destroy:function(){this._clearShowTimeout(),this.element&&(this.element.off(z).remove(),this.element=null)},setOptions:function(t){this.options=P({},this.options,t)},options:{opacity:1,animation:{duration:mt},sharedTemplate:"<table><th colspan='#= colspan #'>#= categoryText #</th># for(var i = 0; i < points.length; i++) { ## var point = points[i]; #<tr># if(colorMarker) { # <td><span class='k-chart-shared-tooltip-marker' style='background-color:#:point.series.color#'></span></td># } ## if(nameColumn) { # <td> #if (point.series.name) {# #: point.series.name #: #} else {# &nbsp; #}#</td># } #<td>#= content(point) #</td></tr># } #</table>",categoryFormat:"{0:d}"},move:function(){var t,e=this,i=e.options,n=e.element;e.anchor&&e.element&&(t=e._offset(),e.visible||n.css({top:t.top,left:t.left}),e.visible=!0,e._ensureElement(document.body),n.stop(!0,!0).show().animate({left:t.left,top:t.top},i.animation.duration))},_clearShowTimeout:function(){this.showTimeout&&(clearTimeout(this.showTimeout),this.showTimeout=null)},getAnchor:function(t){var e=this.anchor,i=e.point,n=e.align,s=i.left,o=i.top;return"center"===n.horizontal?s-=t.width/2:"right"===n.horizontal&&(s-=t.width),"center"===n.vertical?o-=t.height/2:"bottom"===n.vertical&&(o-=t.height),{x:s,y:o}},_offset:function(){var e,i,n=this,s=n._measure(),o=n.getAnchor(s),r=o.y,a=o.x,h=m.support.zoomLevel(),l=t(window),c=window.pageYOffset||document.documentElement.scrollTop||0,p=window.pageXOffset||document.documentElement.scrollLeft||0,d=(this._mobileScroller||{}).movable;return d&&1!==d.scale?(e=u.transform().scale(d.scale,d.scale,[d.x,d.y]),i=new u.Point(a,r).transform(e),a=i.x,r=i.y):(r+=n._fit(r-c,s.height,_(l)/h),a+=n._fit(a-p,s.width,y(l)/h)),{top:r,left:a}},show:function(t){this.anchor=t.anchor,this.element.css(s(t.style)),this.element.toggleClass(yt,!!t.className),this.element.toggleClass(_t,!!t.shared);var e=t.shared?this._sharedContent(t):this._pointContent(t.point);this.element.html(e),this._clearShowTimeout(),this.showTimeout=setTimeout(this.move,xt)},hide:function(){var t=this;clearTimeout(t.showTimeout),t._hideElement(),t.visible&&(t.point=null,t.visible=!1,t.index=null)},_sharedContent:function(t){var e,i,n=t.points,s=w.grep(n,function(t){return T(t.series.name)}).length,o=t.series.length>1,r=1;return s&&r++,o&&r++,e=m.template(this.options.sharedTemplate),i=e({points:n,category:t.category,categoryText:t.categoryText,content:this._pointContent,colorMarker:o,nameColumn:s,colspan:r})},_measure:function(){this._ensureElement();var t={width:y(this.element),height:_(this.element)};return t},_ensureElement:function(){this.element&&this.element.appendTo(document.body).on(z,this._mouseleave)},_mouseleave:function(e){var i=e.relatedTarget,n=this.chartElement[0];i&&i!==n&&!t.contains(n,i)&&this.trigger(ut)},_hideElement:function(){var t=this,e=this.element;e&&e.fadeOut({always:function(){t.visible||e.off(z).remove()}})},_pointContent:function(t){var e,i,n=this,s=P({},n.options,t.options.tooltip);return T(t.value)&&(e=""+t.value),s.template?(i=B(s.template),e=i({value:t.value,category:t.category,series:t.series,dataItem:t.dataItem,percentage:t.percentage,runningTotal:t.runningTotal,total:t.total,low:t.low,high:t.high,xLow:t.xLow,xHigh:t.xHigh,yLow:t.yLow,yHigh:t.yHigh})):s.format&&(e=t.formatValue(s.format)),e},_fit:function(t,e,i){var n=0;return t+e>i&&(n=i-(t+e)),t<0&&(n=-t),n}}),d=p.extend({init:function(t,e){p.fn.init.call(this,t,e),this.element.addClass("k-chart-crosshair-tooltip")},show:function(t){var e=this.element;e&&(this.anchor=t.anchor,this.element.css(t.style),this.element.html(this.content(t)),this.move())},move:function(){var t=this,e=t.element,i=t._offset();t._ensureElement(),e.css({top:i.top,left:i.left}).show()},content:function(t){var e=t.value,i=t.crosshair.options.tooltip;return i.template&&(e=B(i.template)({value:e})),e},hide:function(){this.element.hide()}}),g=x.extend({init:function(t,e){this._chart=t,this._pane=e,this.visual=e.visual,this.chartsVisual=e.chartContainer.visual,this.name=e.options.name},series:function(){var t,e=this._chart,i=e._plotArea.groupSeriesByPane(),n=i[this.name||"default"],s=[];if(n)for(t=0;t<n.length;t++)s.push(new f(e,n[t]));return s}}),f=x.extend({init:function(t,e){this._chart=t,this._options=e},points:function(t){var e,i,n=this._points;return n||(e=this._seriesOptions(),i=this._chart._plotArea,this._points=n=i.pointsBySeriesIndex(e.index)),m.isFunction(t)&&(n=this._filterPoints(n,t)),n},data:function(t){var e,i,n,s,o=this._seriesOptions();return t&&(e=this._chart,i=e._plotArea,o.data=t,o.categoryField&&(n=i.seriesCategoryAxis(o),s=[].concat(e.options.categoryAxis),e._instance.bindCategoryAxisFromSeries(s[n.axisIndex],n.axisIndex)),e._noTransitionsRedraw(),this._clearFields()),o.data},findPoint:function(t){var e,i=this.points();for(e=0;e<i.length;e++)if(t(i[e]))return i[e]},toggleHighlight:function(t,e){e=e?m.isFunction(e)?this.points(e):M(e)?e:[e]:this.points(),this._chart._instance.togglePointsHighlight(t,e)},toggleVisibility:function(t,e){var i,n,s=this._chart,o=this._seriesOptions(),r=m.isFunction(e);if(r)if(I(o.type,[dt,gt,ft]))for(i=this._filterData(e),n=0;n<i.length;n++)i[n].visible=t;else o.visible=function(i){return!e(i.dataItem)||t};else o.visible=t,s._seriesVisibility.save(o);s._noTransitionsRedraw(),this._clearFields()},_filterData:function(t){var e,i=this._seriesOptions().data,n=i.length,s=[];for(e=0;e<n;e++)t(i[e])&&s.push(i[e]);return s},_filterPoints:function(t,e){var i,n=[],s=t.length;for(i=0;i<s;i++)e(t[i])&&n.push(t[i]);return n},_seriesOptions:function(){var t=this._series;return t||(t=this._series=this._chart._seriesOptions(this._options)),t},_clearFields:function(){delete this._points,delete this._series}}),w.Tooltip=p,w.CrosshairTooltip=d,w.ChartInstanceObserver=r,w.ChartPane=g,w.ChartSeries=f}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("kendo.dataviz.chart.min",["dataviz/chart/kendo-chart.min","dataviz/chart/chart.min"],t)}(function(){return window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()});
//# sourceMappingURL=kendo.dataviz.chart.min.js.map
