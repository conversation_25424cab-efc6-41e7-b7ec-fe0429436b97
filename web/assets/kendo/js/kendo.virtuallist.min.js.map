{"version": 3, "sources": ["kendo.virtuallist.js"], "names": ["f", "define", "$", "undefined", "lastFrom", "array", "length", "toArray", "value", "Array", "isPrimitive", "dataItem", "getItemCount", "screenHeight", "listScreens", "itemHeight", "Math", "ceil", "append<PERSON><PERSON><PERSON>", "parent", "className", "tagName", "element", "document", "createElement", "getDefaultItemHeight", "lineHeight", "mockList", "css", "position", "left", "visibility", "appendTo", "body", "parseFloat", "kendo", "getComputedStyles", "find", "remove", "bufferSizes", "opposite", "down", "up", "listValidator", "options", "downThreshold", "threshold", "upThreshold", "list", "scrollTop", "lastScrollTop", "top", "scrollCallback", "callback", "force", "syncList", "reorder", "items", "index", "y", "support", "browser", "msie", "version", "style", "webkitTransform", "transform", "map2", "templates", "arr1", "arr2", "i", "len", "item", "this", "trigger", "ITEMCHANGE", "data", "ns", "ui", "reshift", "diff", "range", "splice", "push", "apply", "unshift", "render", "itemTemplate", "template", "placeholderTemplate", "header", "group", "html", "fixedGroupTemplate", "angular", "elements", "attr", "uid", "columns", "renderColumns", "toggleClass", "FOCUSED", "current", "SELECTED", "selected", "newGroup", "isLastGroupedItem", "GROUPITEM", "groupTemplate", "currentWidth", "currentWidthInt", "widthStyle", "width", "parseInt", "percentageUnitsRegex", "test", "mapChangedItems", "itemsToMatch", "found", "j", "itemsLength", "<PERSON><PERSON><PERSON><PERSON>", "changed", "unchanged", "isActivePromise", "promise", "state", "window", "Widget", "DataBoundWidget", "proxy", "WRAPPER", "VIRTUALLIST", "CONTENT", "LIST", "HEADER", "VIRTUALITEM", "ITEM", "HEIGHTCONTAINER", "HOVER", "CHANGE", "CLICK", "LISTBOUND", "ACTIVATE", "DEACTIVATE", "VIRTUAL_LIST_NS", "VirtualList", "extend", "init", "that", "bound", "_fetching", "fn", "call", "addClass", "content", "wrap", "wrapper", "before", "prev", "removeClass", "on", "_values", "_selectedDataItems", "_selectedIndexes", "_rangesList", "_promisesList", "_optionID", "guid", "_templates", "setDataSource", "dataSource", "throttle", "_renderItems", "_triggerListBound", "delay", "_selectable", "name", "autoBind", "height", "<PERSON><PERSON><PERSON><PERSON>", "type", "selectable", "dataValueField", "mapValueTo", "valueMapper", "events", "setOptions", "_selectProxy", "off", "refresh", "_items", "destroy", "unbind", "_refresh<PERSON><PERSON><PERSON>", "source", "isArray", "DataSource", "create", "_clean", "_deferValueSet", "mute", "bind", "setDSFilter", "filter", "view", "fetch", "skip", "currentRangeStart", "_selectingValue", "_skip", "_getValues", "dataItems", "getter", "_valueGetter", "map", "_highlightSelectedItems", "_getElementByDataItem", "e", "result", "action", "isItemChange", "filtered", "isFiltered", "_mute", "focus", "_createList", "skipUpdateOnBind", "_emptySearch", "done", "removeAt", "setValue", "_forcePrefetch", "shouldClear", "slice", "_valueDeferred", "Deferred", "select", "_prefetchByValue", "_checkValuesOrder", "_removedAddedIndexes", "newValue", "idx", "dataView", "_dataView", "valueGetter", "match", "forSelection", "success", "response", "mapValueToIndex", "mapValueToDataItem", "indexes", "removed", "_deselect", "_triggerChange", "added", "_getIndecies", "resolve", "deferred<PERSON><PERSON><PERSON>", "take", "itemCount", "ranges", "defs", "low", "floor", "high", "pages", "each", "_", "deferred", "end", "existingRange", "_multiplePrefetch", "when", "then", "prefetch", "isEmptyList", "_activeD<PERSON><PERSON>red", "_getSkip", "_findDataItem", "_get<PERSON>ange", "_find<PERSON>ange", "min", "total", "dataItemByIndex", "progress", "selectedDataItems", "scrollWith", "scrollTo", "scrollToIndex", "candidate", "id", "triggerEvent", "flatView", "isNaN", "_getElementByIndex", "_focusedIndex", "hasClass", "removeAttr", "_getElementLocation", "_screenHeight", "focusIndex", "focusFirst", "focusLast", "lastIndex", "heightContainer", "offsetHeight", "focusPrev", "focusNext", "indices", "initialIndices", "isAlreadySelected", "singleSelection", "prefetchStarted", "_selectD<PERSON><PERSON>red", "_deselectCurrentValues", "_select", "_listCreated", "_lastDS<PERSON>ilter", "Query", "compareFilters", "skipUpdate", "noop", "_lastScrollTop", "empty", "_height", "hasData", "setScreenHeight", "yPosition", "yDownPostion", "screenEnd", "currentColumn", "templateText", "key", "field", "_generateItems", "count", "tabIndex", "setAttribute", "minHeight", "_saveInitialRanges", "_ranges", "start", "get", "_buildValueGetter", "_setHeight", "hide", "show", "_getter", "_onScroll", "getList", "_listItems", "_fixedHeader", "_whenChanged", "_reorderList", "_calculateGroupPadding", "_calculateColumnsHeaderPadding", "currentHeight", "padHeight", "innerHTML", "lastRequestedRange", "lastRangeStart", "pageSize", "flatGroups", "rangeStart", "flatGroup", "groups", "groupLength", "inRange", "_getter<PERSON><PERSON><PERSON><PERSON>", "reject", "firstItemIndex", "_indexConstraint", "firstVisibleGroup", "fixedGroupText", "currentVisibleGroup", "firstVisibleDataItemIndex", "firstVisibleDataItem", "_itemMapper", "listType", "currentIndex", "_currentGroup", "_range", "_view", "_getDataItemsCollection", "_listIndex", "theValidator", "theNew", "currentOffset", "Infinity", "list2", "offset", "range2", "abs", "_bufferSizes", "max", "buffers", "getElementIndex", "j<PERSON><PERSON><PERSON>", "elementIndex", "selectedIndex", "selectedIndexes", "removedindexesCounter", "inArray", "_deselectSingleItem", "children", "values", "page", "oldSkip", "_click<PERSON><PERSON><PERSON>", "currentTarget", "isDefaultPrevented", "firstItem", "first", "groupHeader", "padding", "display", "scrollbar", "isRtl", "columnsHeader", "plugin", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,WA8zCE,MAtzCC,UAAUE,EAAGC,GAEV,QAASC,GAASC,GACd,MAAOA,GAAMA,EAAMC,OAAS,GAEhC,QAASC,GAAQC,GACb,MAAOA,aAAiBC,OAAQD,GAASA,GAE7C,QAASE,GAAYC,GACjB,MAA2B,gBAAbA,IAA6C,gBAAbA,IAA6C,iBAAbA,GAElF,QAASC,GAAaC,EAAcC,EAAaC,GAC7C,MAAOC,MAAKC,KAAKJ,EAAeC,EAAcC,GAElD,QAASG,GAAYC,EAAQC,EAAWC,GACpC,GAAIC,GAAUC,SAASC,cAAcH,GAAW,MAKhD,OAJID,KACAE,EAAQF,UAAYA,GAExBD,EAAOD,YAAYI,GACZA,EAEX,QAASG,KACL,GAAgGC,GAA5FC,EAAWzB,EAAE,6EASjB,OARAyB,GAASC,KACLC,SAAU,WACVC,KAAM,YACNC,WAAY,WAEhBJ,EAASK,SAAST,SAASU,MAC3BP,EAAaQ,WAAWC,EAAMC,kBAAkBT,EAASU,KAAK,WAAW,IAAK,gBAAgB,gBAC9FV,EAASW,SACFZ,EAEX,QAASa,GAAY1B,EAAcC,EAAa0B,GAC5C,OACIC,KAAM5B,EAAe2B,EACrBE,GAAI7B,GAAgBC,EAAc,EAAI0B,IAG9C,QAASG,GAAcC,EAAS/B,GAAhC,GACQgC,IAAiBD,EAAQ9B,YAAc,EAAI8B,EAAQE,WAAajC,EAChEkC,EAAcH,EAAQE,UAAYjC,CACtC,OAAO,UAAUmC,EAAMC,EAAWC,GAC9B,MAAID,GAAYC,EACLD,EAAYD,EAAKG,IAAMN,EAEV,IAAbG,EAAKG,KAAaF,EAAYD,EAAKG,IAAMJ,GAI5D,QAASK,GAAe9B,EAAS+B,GAC7B,MAAO,UAAUC,GACb,MAAOD,GAAS/B,EAAQ2B,UAAWK,IAG3C,QAASC,GAASC,GACd,MAAO,UAAUR,EAAMM,GAEnB,MADAE,GAAQR,EAAKS,MAAOT,EAAKU,MAAOJ,GACzBN,GAGf,QAASnB,GAASP,EAASqC,GACnBxB,EAAMyB,QAAQC,QAAQC,MAAQ3B,EAAMyB,QAAQC,QAAQE,QAAU,GAC9DzC,EAAQ0C,MAAMb,IAAMQ,EAAI,MAExBrC,EAAQ0C,MAAMC,gBAAkB,cAAgBN,EAAI,MACpDrC,EAAQ0C,MAAME,UAAY,cAAgBP,EAAI,OAGtD,QAASQ,GAAKd,EAAUe,GACpB,MAAO,UAAUC,EAAMC,GACnB,IAAK,GAAIC,GAAI,EAAGC,EAAMH,EAAK/D,OAAQiE,EAAIC,EAAKD,IACxClB,EAASgB,EAAKE,GAAID,EAAKC,GAAIH,GACvBE,EAAKC,GAAGE,MACRC,KAAKC,QAAQC,GACTH,KAAMvE,EAAEmE,EAAKE,IACbM,KAAMP,EAAKC,GAAGE,KACdK,GAAI3C,EAAM4C,MAM9B,QAASC,GAAQvB,EAAOwB,GACpB,GAAIC,EAQJ,OAPID,GAAO,GACPC,EAAQzB,EAAM0B,OAAO,EAAGF,GACxBxB,EAAM2B,KAAKC,MAAM5B,EAAOyB,KAExBA,EAAQzB,EAAM0B,OAAOF,GAAOA,GAC5BxB,EAAM6B,QAAQD,MAAM5B,EAAOyB,IAExBA,EAEX,QAASK,GAAOjE,EAASuD,EAAMT,GAC3B,GAAIoB,GAAepB,EAAUqB,QAC7BnE,GAAUpB,EAAEoB,GACPuD,EAAKJ,OACNe,EAAepB,EAAUsB,qBAEV,IAAfb,EAAKnB,OAAegB,KAAKiB,QAAUd,EAAKe,OACxClB,KAAKiB,OAAOE,KAAKzB,EAAU0B,mBAAmBjB,EAAKe,QAEvDlB,KAAKqB,QAAQ,UAAW,WACpB,OAASC,UAAW1E,MAExBA,EAAQ2E,KAAK,WAAYpB,EAAKJ,KAAOI,EAAKJ,KAAKyB,IAAM,IAAID,KAAK,oBAAqBpB,EAAKnB,OAEpFpC,EAAQuE,KADRnB,KAAK9B,QAAQuD,SAAWzB,KAAK9B,QAAQuD,QAAQ7F,QAAUuE,EAAKJ,KAC/C2B,EAAc1B,KAAK9B,QAASiC,EAAKJ,KAAML,GAEvCoB,EAAaX,EAAKJ,WAEnCnD,EAAQ+E,YAAYC,EAASzB,EAAK0B,SAClCjF,EAAQ+E,YAAYG,EAAU3B,EAAK4B,UACnCnF,EAAQ+E,YAAY,UAAWxB,EAAK6B,UACpCpF,EAAQ+E,YAAY,SAAUxB,EAAK8B,mBACnCrF,EAAQ+E,YAAY,kBAAmBxB,EAAKJ,MACzB,IAAfI,EAAKnB,OAAemB,EAAK6B,UACzBxG,EAAE,cAAgB0G,EAAY,WAAW5E,SAASV,GAASuE,KAAKzB,EAAUyC,cAAchC,EAAKe,QAE7Ff,EAAK1B,MAAQhD,GACb0B,EAASP,EAAQ,GAAIuD,EAAK1B,KAE9BuB,KAAKqB,QAAQ,UAAW,WACpB,OACIC,UAAW1E,GACXuD,OACQlE,SAAUkE,EAAKJ,KACfmB,MAAOf,EAAKe,MACZc,SAAU7B,EAAK6B,cAKnC,QAASN,GAAcxD,EAASjC,EAAUyD,GAA1C,GAEaG,GACDuC,EACAC,EACAC,EAJJvC,EAAO,EACX,KAASF,EAAI,EAAGA,EAAI3B,EAAQuD,QAAQ7F,OAAQiE,IACpCuC,EAAelE,EAAQuD,QAAQ5B,GAAG0C,MAClCF,EAAkBG,SAASJ,EAAc,IACzCE,EAAa,GACbF,IACAE,GAAc,gBACdA,GAAcD,EACdC,GAAcG,EAAqBC,KAAKN,GAAgB,IAAM,KAC9DE,GAAc,MAElBvC,GAAQ,wBAA4BuC,EAAa,IACjDvC,GAAQL,EAAU,SAAWG,GAAG5D,GAChC8D,GAAQ,SAEZ,OAAOA,GAEX,QAAS4C,GAAgBZ,EAAUa,GAAnC,GAGQ3G,GACA4G,EACAhD,EAAGiD,EAJHC,EAAcH,EAAahH,OAC3BoH,EAAiBjB,EAASnG,OAI1BqH,KACAC,IACJ,IAAIF,EACA,IAAKnD,EAAI,EAAGA,EAAImD,EAAgBnD,IAAK,CAGjC,IAFA5D,EAAW8F,EAASlC,GACpBgD,GAAQ,EACHC,EAAI,EAAGA,EAAIC,EAAaD,IACzB,GAAI7G,IAAa2G,EAAaE,GAAI,CAC9BD,GAAQ,EACRI,EAAQvC,MACJ1B,MAAOa,EACPE,KAAM9D,GAEV,OAGH4G,GACDK,EAAUxC,KAAKzE,GAI3B,OACIgH,QAASA,EACTC,UAAWA,GAGnB,QAASC,GAAgBC,GACrB,MAAOA,IAA+B,aAApBA,EAAQC,QA1LjC,GACO5F,GAAQ6F,OAAO7F,MAAO4C,EAAK5C,EAAM4C,GAAIkD,EAASlD,EAAGkD,OAAQC,EAAkBnD,EAAGmD,gBAAiBC,EAAQjI,EAAEiI,MAAOhB,EAAuB,kBAAmBiB,EAAU,iBAAkBC,EAAc,iBAAkBC,EAAU,oBAAqBC,EAAO,SAAUC,EAAS,iBAAkBC,EAAc,iBAAkBC,EAAO,SAAUC,EAAkB,qBAAsB/B,EAAY,UAAWJ,EAAW,mBAAoBF,EAAU,kBAAmBsC,EAAQ,gBAAiBC,EAAS,SAAUC,EAAQ,QAASC,EAAY,YAAanE,EAAa,aAAcoE,EAAW,WAAYC,EAAa,aAAcC,EAAkB,eA2LloBC,EAAcjB,EAAgBkB,QAC9BC,KAAM,SAAU/H,EAASsB,GACrB,GAAI0G,GAAO5E,IACX4E,GAAKC,OAAM,GACXD,EAAKE,WAAY,EACjBvB,EAAOwB,GAAGJ,KAAKK,KAAKJ,EAAMhI,EAASsB,GAC9B0G,EAAK1G,QAAQ7B,aACduI,EAAK1G,QAAQ7B,WAAaU,KAE9BmB,EAAU0G,EAAK1G,QACf0G,EAAKhI,QAAQqI,SAASpB,EAAO,IAAMF,GAAapC,KAAK,OAAQ,WAC7DqD,EAAKM,QAAUN,EAAKhI,QAAQuI,KAAK,iCAAsCvB,EAAU,YAAanH,SAC9FmI,EAAKQ,QAAUR,EAAKM,QAAQC,KAAK,eAAkBzB,EAAU,YAAajH,SAC1EmI,EAAK3D,OAAS2D,EAAKM,QAAQG,OAAO,eAAkBvB,EAAS,YAAawB,OACtEpH,EAAQuD,SAAWvD,EAAQuD,QAAQ7F,QACnCgJ,EAAKhI,QAAQ2I,YAAY1B,GAE7Be,EAAKhI,QAAQ4I,GAAG,aAAehB,EAAiB,0BAA2B,WACvEhJ,EAAEwE,MAAMiF,SAASf,KAClBsB,GAAG,aAAehB,EAAiB,KAAM,WACxChJ,EAAEwE,MAAMuF,YAAYrB,KAExBU,EAAKa,QAAU5J,EAAQ+I,EAAK1G,QAAQpC,OACpC8I,EAAKc,sBACLd,EAAKe,oBACLf,EAAKgB,eACLhB,EAAKiB,iBACLjB,EAAKkB,UAAYrI,EAAMsI,OACvBnB,EAAKoB,aACLpB,EAAKqB,cAAc/H,EAAQgI,YAC3BtB,EAAKM,QAAQM,GAAG,SAAWhB,EAAiB/G,EAAM0I,SAAS,WACvDvB,EAAKwB,eACLxB,EAAKyB,qBACNnI,EAAQoI,QACX1B,EAAK2B,eAETrI,SACIsI,KAAM,cACNC,UAAU,EACVH,MAAO,IACPI,OAAQ,KACRtK,YAAa,EACbgC,UAAW,GACX/B,WAAY,KACZsK,eAAgB,EAChBC,KAAM,OACNC,YAAY,EACZ/K,SACAgL,eAAgB,KAChB/F,SAAU,UACVC,oBAAqB,aACrBmB,cAAe,UACff,mBAAoB,UACpB2F,WAAY,QACZC,YAAa,MAEjBC,QACI9C,EACAC,EACAC,EACAnE,EACAoE,EACAC,GAEJ2C,WAAY,SAAUhJ,GAClBqF,EAAOwB,GAAGmC,WAAWlC,KAAKhF,KAAM9B,GAC5B8B,KAAKmH,cAAgBnH,KAAK9B,QAAQ2I,cAAe,EACjD7G,KAAKpD,QAAQwK,IAAIhD,EAAO,IAAML,EAAa/D,KAAKmH,eACxCnH,KAAKmH,cAAgBnH,KAAK9B,QAAQ2I,YAC1C7G,KAAKuG,cAETvG,KAAKgG,aACLhG,KAAKqH,WAETtI,MAAO,WACH,MAAOvD,GAAEwE,KAAKsH,SAElBC,QAAS,WACLvH,KAAKoF,QAAQgC,IAAI5C,GACjBxE,KAAKkG,WAAWsB,OAAOrD,EAAQnE,KAAKyH,iBACpClE,EAAOwB,GAAGwC,QAAQvC,KAAKhF,OAE3BiG,cAAe,SAAUyB,GAAV,GAGP5L,GAFA8I,EAAO5E,KACPkG,EAAawB,KAEjBxB,GAAa1K,EAAEmM,QAAQzB,IAAgB/F,KAAM+F,GAAeA,EAC5DA,EAAazI,EAAM0C,KAAKyH,WAAWC,OAAO3B,GACtCtB,EAAKsB,YACLtB,EAAKsB,WAAWsB,OAAOrD,EAAQS,EAAK6C,iBACpC7C,EAAKkD,SACLlD,EAAKC,OAAM,GACXD,EAAKmD,gBAAiB,EACtBjM,EAAQ8I,EAAK9I,QACb8I,EAAK9I,UACL8I,EAAKoD,KAAK,WACNpD,EAAK9I,MAAMA,MAGf8I,EAAK6C,gBAAkBjM,EAAEiI,MAAMmB,EAAKyC,QAASzC,GAEjDA,EAAKsB,WAAaA,EAAW+B,KAAK9D,EAAQS,EAAK6C,iBAC/C7C,EAAKsD,YAAYhC,EAAWiC,UACK,IAA7BjC,EAAWkC,OAAOxM,OAClBgJ,EAAKyC,UACEzC,EAAK1G,QAAQuI,UACpBP,EAAWmC,SAGnBC,KAAM,WACF,MAAOtI,MAAKkG,WAAWqC,qBAE3BlC,kBAAmB,WAAA,GACXzB,GAAO5E,KACPsI,EAAO1D,EAAK0D,MACZ1D,GAAKC,UAAYD,EAAK4D,iBAAmB5D,EAAK6D,QAAUH,IACxD1D,EAAK6D,MAAQH,EACb1D,EAAK3E,QAAQoE,KAGrBqE,WAAY,SAAUC,GAClB,GAAIC,GAAS5I,KAAK6I,YAClB,OAAOrN,GAAEsN,IAAIH,EAAW,SAAU1M,GAC9B,MAAO2M,GAAO3M,MAGtB8M,wBAAyB,WAAA,GACZlJ,GACDE,CADR,KAASF,EAAI,EAAGA,EAAIG,KAAK0F,mBAAmB9J,OAAQiE,IAC5CE,EAAOC,KAAKgJ,sBAAsBhJ,KAAK0F,mBAAmB7F,IAC1DE,EAAKnE,QACLmE,EAAKkF,SAASnD,IAI1BuF,QAAS,SAAU4B,GAAV,GAKDC,GAJAtE,EAAO5E,KACPmJ,EAASF,GAAKA,EAAEE,OAChBC,EAA0B,eAAXD,EACfE,EAAWrJ,KAAKsJ,YAEhB1E,GAAK2E,QAGT3E,EAAKmD,gBAAiB,EACjBnD,EAAKE,WAkBFF,EAAKwB,cACLxB,EAAKwB,cAAa,GAEtBxB,EAAKyB,sBApBDgD,GACAzE,EAAK4E,MAAM,GAEf5E,EAAK6E,cACAN,IAAUvE,EAAKa,QAAQ7J,QAAWyN,GAAazE,EAAK1G,QAAQwL,kBAAqB9E,EAAK+E,cAQvF/E,EAAKC,OAAM,GACXD,EAAKmE,0BACLnE,EAAKyB,sBATLzB,EAAK4D,iBAAkB,EACvB5D,EAAKC,OAAM,GACXD,EAAK9I,MAAM8I,EAAKa,SAAS,GAAMmE,KAAK,WAChChF,EAAK4D,iBAAkB,EACvB5D,EAAKyB,yBAab+C,GAA2B,WAAXD,KAChBD,EAASvG,EAAgBiC,EAAKc,mBAAoBuD,EAAElK,OAChDmK,EAAOjG,QAAQrH,SACXwN,EACAxE,EAAK3E,QAAQ,sBAAwBlB,MAAOmK,EAAOjG,UAEnD2B,EAAK9I,MAAM8I,EAAK8D,WAAWQ,EAAOhG,cAI9C0B,EAAKE,WAAY,IAErB+E,SAAU,SAAU1M,GAGhB,MAFA6C,MAAK2F,iBAAiBlF,OAAOtD,EAAU,GACvC6C,KAAKyF,QAAQhF,OAAOtD,EAAU,IAE1BA,SAAUA,EACVlB,SAAU+D,KAAK0F,mBAAmBjF,OAAOtD,EAAU,GAAG,KAG9D2M,SAAU,SAAUhO,GAChBkE,KAAKyF,QAAU5J,EAAQC,IAE3BA,MAAO,SAAUA,EAAOiO,GAAjB,GAYCC,GAXApF,EAAO5E,IACX,OAAIlE,KAAUL,EACHmJ,EAAKa,QAAQwE,SAEV,OAAVnO,IACAA,MAEJA,EAAQD,EAAQC,GACX8I,EAAKsF,gBAAkD,aAAhCtF,EAAKsF,eAAe7G,UAC5CuB,EAAKsF,eAAiB1O,EAAE2O,YAExBH,EAA0C,aAA5BpF,EAAK1G,QAAQ2I,YAA6BjC,EAAKwF,SAASxO,QAAUE,EAAMF,QACtFoO,GAAgBlO,EAAMF,QACtBgJ,EAAKwF,WAETxF,EAAKa,QAAU3J,GACX8I,EAAKC,UAAYD,EAAK2E,QAAU3E,EAAKmD,gBAAkBgC,IACvDnF,EAAKyF,iBAAiBvO,GAEnB8I,EAAKsF,iBAEhBI,kBAAmB,SAAUxO,GACzB,GAAIkE,KAAKuK,sBAAwBvK,KAAKuK,qBAAqB3O,SAAWE,EAAMF,OAAQ,CAChF,GAAI4O,GAAWxK,KAAKuK,qBAAqBN,OAEzC,OADAjK,MAAKuK,qBAAuB,KACrBC,EAEX,MAAO1O,IAEXuO,iBAAkB,SAAUvO,GAAV,GACqGiE,GAC1GF,EACI4K,EAFT7F,EAAO5E,KAAM0K,EAAW9F,EAAK+F,UAAWC,EAAchG,EAAKiE,aAAc9B,EAAanC,EAAK1G,QAAQ6I,WAAkB8D,GAAQ,EAAOC,IACxI,KAASjL,EAAI,EAAGA,EAAI/D,EAAMF,OAAQiE,IAC9B,IAAS4K,EAAM,EAAGA,EAAMC,EAAS9O,OAAQ6O,IACrC1K,EAAO2K,EAASD,GAAK1K,KACjBA,IACA8K,EAAQ7O,EAAY+D,GAAQjE,EAAM+D,KAAOE,EAAOjE,EAAM+D,KAAO+K,EAAY7K,GACrE8K,GACAC,EAAapK,KAAKgK,EAASD,GAAKzL,OAKhD,OAAI8L,GAAalP,SAAWE,EAAMF,QAC9BgJ,EAAKa,WACLb,EAAKwF,OAAOU,GACZ,IAEoC,kBAA7BlG,GAAK1G,QAAQ8I,YACpBpC,EAAK1G,QAAQ8I,aACTlL,MAAmC,aAA5BkE,KAAK9B,QAAQ2I,WAA4B/K,EAAQA,EAAM,GAC9DiP,QAAS,SAAUC,GACI,UAAfjE,EACAnC,EAAKqG,gBAAgBD,GACC,aAAfjE,GACPnC,EAAKsG,mBAAmBF,MAK/BpG,EAAK9I,QAAQ,IAGd8I,EAAK4D,iBAAkB,EACvB5D,EAAKyB,qBAHLzB,EAAKwF,aAbb,IAoBJa,gBAAiB,SAAUE,GAMvB,GAJIA,EADAA,IAAY1P,GAAa0P,QAA8B,OAAZA,KAGjCtP,EAAQsP,GAEjBA,EAAQvP,OAEN,CACH,GAAIwP,GAAUpL,KAAKqL,cAAcD,OAC7BA,GAAQxP,QACRoE,KAAKsL,eAAeF,UAJxBD,OAOJnL,MAAKoK,OAAOe,IAEhBD,mBAAoB,SAAUvC,GAAV,GACZyC,GAASG,EAuBA1L,EACDE,CAlBZ,IAJI4I,EADAA,IAAclN,GAA2B,OAAdkN,KAGf9M,EAAQ8M,GAEnBA,EAAU/M,OAER,CAeH,IAdAwP,EAAU5P,EAAEsN,IAAI9I,KAAK0F,mBAAoB,SAAU3F,EAAMf,GACrD,OACIA,MAAOA,EACP/C,SAAU8D,KAGlBwL,EAAQ/P,EAAEsN,IAAIH,EAAW,SAAU5I,EAAMf,GACrC,OACIA,MAAOA,EACP/C,SAAU8D,KAGlBC,KAAK0F,mBAAqBiD,EAC1B3I,KAAK2F,oBACI9F,EAAI,EAAGA,EAAIG,KAAK0F,mBAAmB9J,OAAQiE,IAC5CE,EAAOC,KAAKgJ,sBAAsBhJ,KAAK0F,mBAAmB7F,IAC9DG,KAAK2F,iBAAiBjF,KAAKV,KAAKwL,aAAazL,GAAM,IACnDA,EAAKkF,SAASnD,EAElB9B,MAAKsL,eAAeF,EAASG,GACzBvL,KAAKkK,gBACLlK,KAAKkK,eAAeuB,cAvBxBzL,MAAKoK,cA2BbsB,cAAe,SAAU1M,GAAV,GACPkH,GAAalG,KAAKkG,WAClByF,EAAO3L,KAAK4L,UACZC,EAAS7L,KAAK4F,YACdsD,EAAS1N,EAAE2O,WACX2B,KACAC,EAAMzP,KAAK0P,MAAMhN,EAAQ2M,GAAQA,EACjCM,EAAO3P,KAAKC,KAAKyC,EAAQ2M,GAAQA,EACjCO,EAAQD,IAASF,GAAOE,IACxBF,EACAE,EAuBJ,OArBAzQ,GAAE2Q,KAAKD,EAAO,SAAUE,EAAG9D,GAAb,GAGN+D,GAFAC,EAAMhE,EAAOqD,EACbY,EAAgBV,EAAOvD,EAEtBiE,IAAiBA,EAAcD,MAAQA,EAUxCD,EAAWE,EAAcF,UATzBA,EAAW7Q,EAAE2O,WACb0B,EAAOvD,IACHgE,IAAKA,EACLD,SAAUA,GAEdnG,EAAWsG,kBAAkBlE,EAAMqD,EAAM,WACrCU,EAASZ,aAKjBK,EAAKpL,KAAK2L,KAEd7Q,EAAEiR,KAAK9L,MAAMnF,EAAGsQ,GAAMY,KAAK,WACvBxD,EAAOuC,YAEJvC,GAEXyD,SAAU,SAAUxB,GAChB,GAAIvG,GAAO5E,KAAM2L,EAAO3L,KAAK4L,UAAWgB,GAAehI,EAAKiB,cAAcjK,MAc1E,OAbKuH,GAAgByB,EAAKiI,mBACtBjI,EAAKiI,gBAAkBrR,EAAE2O,WACzBvF,EAAKiB,kBAETrK,EAAE2Q,KAAKhB,EAAS,SAAUiB,EAAGpN,GACzB4F,EAAKiB,cAAcnF,KAAKkE,EAAK8G,cAAc9G,EAAKkI,SAAS9N,EAAO2M,OAEhEiB,GACApR,EAAEiR,KAAK9L,MAAMnF,EAAGoJ,EAAKiB,eAAe+D,KAAK,WACrChF,EAAKiB,iBACLjB,EAAKiI,gBAAgBpB,YAGtB7G,EAAKiI,iBAEhBE,cAAe,SAAU3E,EAAMpJ,GAAhB,GACPkC,GAESrB,CADb,IAA0B,UAAtBG,KAAK9B,QAAQ0I,KACb,IAAS/G,EAAI,EAAGA,EAAIuI,EAAKxM,OAAQiE,IAAK,CAElC,GADAqB,EAAQkH,EAAKvI,GAAGd,QACZmC,EAAMtF,QAAUoD,GAGhB,MAAOkC,GAAMlC,EAFbA,IAAgBkC,EAAMtF,OAMlC,MAAOwM,GAAKpJ,IAEhBgO,UAAW,SAAU1E,EAAMqD,GACvB,MAAO3L,MAAKkG,WAAW+G,WAAW3E,EAAMhM,KAAK4Q,IAAI5E,EAAOqD,EAAM3L,KAAKkG,WAAWiH,WAElFC,gBAAiB,SAAUpO,GAAV,GACT4F,GAAO5E,KACP2L,EAAO/G,EAAKgH,UACZtD,EAAO1D,EAAKkI,SAAS9N,EAAO2M,GAC5BvD,EAAOpI,KAAKgN,UAAU1E,EAAMqD,EAChC,OAAK/G,GAAKoI,UAAU1E,EAAMqD,GAAM/P,QAGN,UAAtBgJ,EAAK1G,QAAQ0I,OACbnJ,EAAM4C,GAAGgN,SAAS7R,EAAEoJ,EAAKQ,UAAU,GACnCR,EAAKoD,KAAK,WACNpD,EAAKsB,WAAW1F,MAAM8H,EAAMqD,EAAM,WAC9BlO,EAAM4C,GAAGgN,SAAS7R,EAAEoJ,EAAKQ,UAAU,KAEvCgD,EAAOxD,EAAKsB,WAAWkC,UAGxBxD,EAAKmI,cAAc3E,GAAOpJ,EAAQsJ,KAX9B,MAafgF,kBAAmB,WACf,MAAOtN,MAAK0F,mBAAmBuE,SAEnCsD,WAAY,SAAUzR,GAClBkE,KAAKkF,QAAQ3G,UAAUyB,KAAKkF,QAAQ3G,YAAczC,IAEtD0R,SAAU,SAAUvO,GAChBe,KAAKkF,QAAQ3G,UAAUU,IAE3BwO,cAAe,SAAUzO,GACrBgB,KAAKwN,SAASxO,EAAQgB,KAAK9B,QAAQ7B,aAEvCmN,MAAO,SAAUkE,GAAV,GACC9Q,GAASoC,EAAOmB,EAAM0B,EAOb4I,EAmCLtN,EA1C2Bd,EAAa2D,KAAK9B,QAAQ7B,WAAYsR,EAAK3N,KAAK8F,UAAW8H,GAAe,CAC7G,IAAIF,IAAcjS,EAEd,MADAoG,GAAU7B,KAAKpD,QAAQe,KAAK,IAAMiE,GAC3BC,EAAQjG,OAASiG,EAAU,IAEtC,IAAyB,kBAAd6L,GAEP,IADAvN,EAAOH,KAAKkG,WAAW2H,WACdpD,EAAM,EAAGA,EAAMtK,EAAKvE,OAAQ6O,IACjC,GAAIiD,EAAUvN,EAAKsK,IAAO,CACtBiD,EAAYjD,CACZ,OAcZ,MAVIiD,aAAqB3R,SACrB2R,EAAYhS,EAASgS,IAErBI,MAAMJ,IACN9Q,EAAUpB,EAAEkS,GACZ1O,EAAQwD,SAAShH,EAAEoB,GAAS2E,KAAK,qBAAsB,MAEvDvC,EAAQ0O,EACR9Q,EAAUoD,KAAK+N,mBAAmB/O,IAElCA,QACAgB,KAAKpD,QAAQe,KAAK,IAAMiE,GAAS2D,YAAY3D,GAC7C5B,KAAKgO,cAAgBvS,EACrB,IAEAmB,EAAQhB,QACJgB,EAAQqR,SAASrM,KACjBgM,GAAe,GAEf5N,KAAKgO,gBAAkBvS,IACvBoG,EAAU7B,KAAK+N,mBAAmB/N,KAAKgO,eACvCnM,EAAQ0D,YAAY3D,GAASsM,WAAW,MACpCN,GACA5N,KAAKC,QAAQsE,IAGrBvE,KAAKgO,cAAgBhP,EACrBpC,EAAQqI,SAASrD,GAASL,KAAK,KAAMoM,GACjCxQ,EAAW6C,KAAKmO,oBAAoBnP,GACvB,QAAb7B,EACA6C,KAAKwN,SAASxO,EAAQ3C,GACF,WAAbc,EACP6C,KAAKwN,SAASxO,EAAQ3C,EAAaA,EAAa2D,KAAKoO,eACjC,cAAbjR,GACP6C,KAAKwN,SAASxO,EAAQ3C,GAEtBuR,GACA5N,KAAKC,QAAQqE,KAGjBtE,KAAKgO,cAAgBhP,EACrBgB,KAAKjB,QAAQwG,YAAY3D,GACzB5B,KAAKyN,cAAczO,IA3BvB,IA8BJqP,WAAY,WACR,MAAOrO,MAAKgO,eAEhBM,WAAY,WACRtO,KAAKwN,SAAS,GACdxN,KAAKwJ,MAAM,IAEf+E,UAAW,WACP,GAAIC,GAAYxO,KAAKkG,WAAWiH,OAChCnN,MAAKwN,SAASxN,KAAKyO,gBAAgBC,cACnC1O,KAAKwJ,MAAMgF,EAAY,IAE3BG,UAAW,WAAA,GAEH9M,GADA7C,EAAQgB,KAAKgO,aAEjB,QAAKF,MAAM9O,IAAUA,EAAQ,GACzBA,GAAS,EACTgB,KAAKwJ,MAAMxK,GACX6C,EAAU7B,KAAKwJ,QACX3H,GAAWA,EAAQoM,SAAS,oBAC5BjP,GAAS,EACTgB,KAAKwJ,MAAMxK,IAERA,IAEPA,EAAQgB,KAAKkG,WAAWiH,QAAU,EAClCnN,KAAKwJ,MAAMxK,GACJA,IAGf4P,UAAW,WAAA,GAGH/M,GAFA7C,EAAQgB,KAAKgO,cACbQ,EAAYxO,KAAKkG,WAAWiH,QAAU,CAE1C,QAAKW,MAAM9O,IAAUA,EAAQwP,GACzBxP,GAAS,EACTgB,KAAKwJ,MAAMxK,GACX6C,EAAU7B,KAAKwJ,QACX3H,GAAWA,EAAQoM,SAAS,oBAC5BjP,GAAS,EACTgB,KAAKwJ,MAAMxK,IAERA,IAEPA,EAAQ,EACRgB,KAAKwJ,MAAMxK,GACJA,IAGfsM,eAAgB,SAAUF,EAASG,GAC/BH,EAAUA,MACVG,EAAQA,OACJH,EAAQxP,QAAU2P,EAAM3P,SACxBoE,KAAKC,QAAQkE,GACTiH,QAASA,EACTG,MAAOA,KAInBnB,OAAQ,SAAUsD,GAAV,GACamB,GAASC,EAAiKC,EAAmB1C,EAAUnD,EA8BpNU,EA9BAhF,EAAO5E,KAA+BgP,EAA8C,aAA5BpK,EAAK1G,QAAQ2I,WAA2BoI,EAAkB9L,EAAgByB,EAAKiI,iBAAkBxD,EAAWrJ,KAAKsJ,aAAmD8B,IAChO,OAAIsC,KAAcjS,EACPmJ,EAAKe,iBAAiBsE,SAE5BrF,EAAKsK,iBAAoD,aAAjCtK,EAAKsK,gBAAgB7L,UAC9CuB,EAAKsK,gBAAkB1T,EAAE2O,YAE7B0E,EAAUjK,EAAK4G,aAAakC,GAC5BqB,EAAoBC,IAAoB3F,GAAY3N,EAASmT,KAAanT,EAASsE,KAAK2F,kBACxFyF,EAAUxG,EAAKuK,uBAAuBN,GAClCzD,EAAQxP,SAAWiT,EAAQjT,QAAUmT,GACrCnK,EAAK0G,eAAeF,GAChBxG,EAAKsF,gBACLtF,EAAKsF,eAAeuB,UAAUrI,UAE3BwB,EAAKsK,gBAAgBzD,UAAUrI,YAEnB,IAAnByL,EAAQjT,QAAgBiT,EAAQ,UAChCA,MAEJC,EAAiBD,EACjB3F,EAAStE,EAAKyG,UAAUwD,GACxBzD,EAAUlC,EAAOkC,QACjByD,EAAU3F,EAAO2F,QACbG,IACAC,GAAkB,EACdJ,EAAQjT,SACRiT,GAAWnT,EAASmT,MAGxBjF,EAAO,WACP,GAAI2B,GAAQ3G,EAAKwK,QAAQP,IACrBC,EAAelT,SAAWiT,EAAQjT,QAAUoT,IAC5CpK,EAAK4E,MAAMqF,GAEfjK,EAAK0G,eAAeF,EAASG,GACzB3G,EAAKsF,gBACLtF,EAAKsF,eAAeuB,UAExB7G,EAAKsK,gBAAgBzD,WAEzBY,EAAWzH,EAAK+H,SAASkC,GACpBI,IACG5C,EACAA,EAASzC,KAAKA,GAEdA,KAGDhF,EAAKsK,gBAAgB9L,aAEhCyB,MAAO,SAAUA,GACb,MAAIA,KAAUpJ,EACHuE,KAAKqP,cAEhBrP,KAAKqP,aAAexK,EAApB7E,IAEJgI,KAAM,SAAUrJ,GACZqB,KAAKuJ,OAAQ,EACb9F,EAAM9E,IAAYqB,MAClBA,KAAKuJ,OAAQ,GAEjBrB,YAAa,SAAUC,GACnBnI,KAAKsP,cAAgB9T,EAAEkJ,UAAWyD,IAEtCmB,WAAY,WAIR,MAHKtJ,MAAKsP,eACNtP,KAAKkI,YAAYlI,KAAKkG,WAAWiC,WAE7B1K,EAAM0C,KAAKoP,MAAMC,eAAexP,KAAKkG,WAAWiC,SAAUnI,KAAKsP,gBAE3EG,WAAYjU,EAAEkU,KACd3B,mBAAoB,SAAU/O,GAC1B,MAAOgB,MAAKjB,QAAQoJ,OAAO,SAAUsC,EAAK7N,GACtC,MAAOoC,KAAUwD,SAAShH,EAAEoB,GAAS2E,KAAK,qBAAsB,OAGxEyH,sBAAuB,SAAU/M,GAAV,GAC6CW,GAASiO,EAChEhL,EADL6K,EAAW1K,KAAK2K,UAAWC,EAAc5K,KAAK6I,YAClD,KAAShJ,EAAI,EAAGA,EAAI6K,EAAS9O,OAAQiE,IAEjC,GADAgL,EAAQH,EAAS7K,GAAGE,MAAQ/D,EAAY0O,EAAS7K,GAAGE,MAAQ2K,EAAS7K,GAAGE,OAAS9D,EAAWyO,EAAS7K,GAAGE,MAAQ9D,GAAY2O,EAAYF,EAAS7K,GAAGE,OAAS6K,EAAY3O,GAC9J,CACPW,EAAU8N,EAAS7K,EACnB,OAGR,MAAOjD,GAAUoD,KAAK+N,mBAAmBnR,EAAQoC,OAASxD,KAE9DsM,OAAQ,WACJ9H,KAAKkJ,OAASzN,EACduE,KAAK2P,eAAiBlU,EACtBuE,KAAKyI,MAAQhN,EACbD,EAAEwE,KAAKyO,iBAAiB7Q,SACxBoC,KAAKyO,gBAAkBhT,EACvBuE,KAAKpD,QAAQgT,SAEjBC,QAAS,WACL,GAAIC,KAAY9P,KAAKkG,WAAWkC,OAAOxM,OAAQ8K,EAAS1G,KAAK9B,QAAQwI,OAAQrK,EAAa2D,KAAK9B,QAAQ7B,WAAY8Q,EAAQnN,KAAKkG,WAAWiH,OAM3I,OALK2C,GAEMpJ,EAASrK,EAAa8Q,IAC7BzG,EAASyG,EAAQ9Q,GAFjBqK,EAAS,EAINA,GAEXqJ,gBAAiB,WACb,GAAIrJ,GAAS1G,KAAK6P,SAClB7P,MAAKkF,QAAQwB,OAAOA,GACpB1G,KAAKoO,cAAgB1H,GAEzBvK,aAAc,WACV,MAAO6D,MAAKoO,eAEhBD,oBAAqB,SAAUnP,GAC3B,GAAgO7B,GAA5NoB,EAAYyB,KAAKkF,QAAQ3G,YAAapC,EAAe6D,KAAKoO,cAAe/R,EAAa2D,KAAK9B,QAAQ7B,WAAY2T,EAAYhR,EAAQ3C,EAAY4T,EAAeD,EAAY3T,EAAY6T,EAAY3R,EAAYpC,CAUlN,OARIgB,GADA6S,IAAczR,EAAYlC,GAAc4T,EAAe1R,GAAayR,EAAYzR,EACrE,MACJyR,IAAcE,GAAaF,EAAYE,GAAaA,EAAYD,EAC5D,SACJD,GAAazR,GAAayR,GAAazR,GAAapC,EAAeE,GAC/D,WAEA,aAInB2J,WAAY,WAAA,GASKnG,GACDsQ,EACAC,EAIHC,EAdLnS,EAAU8B,KAAK9B,QACfwB,GACAqB,SAAU7C,EAAQ6C,SAClBC,oBAAqB9C,EAAQ8C,oBAC7BmB,cAAejE,EAAQiE,cACvBf,mBAAoBlD,EAAQkD,mBAEhC,IAAIlD,EAAQuD,QACR,IAAS5B,EAAI,EAAGA,EAAI3B,EAAQuD,QAAQ7F,OAAQiE,IACpCsQ,EAAgBjS,EAAQuD,QAAQ5B,GAChCuQ,EAAeD,EAAcG,MAAQH,GAAAA,EAAcG,MAAmB,OAC1E5Q,EAAU,SAAWG,GAAKsQ,EAAcpP,UAAY,MAAQqP,EAAe,GAGnF,KAASC,IAAO3Q,GACkB,kBAAnBA,GAAU2Q,KACjB3Q,EAAU2Q,GAAO5S,EAAMsD,SAASrB,EAAU2Q,IAAQ,IAG1DrQ,MAAKN,UAAYA,GAErB6Q,eAAgB,SAAU3T,EAAS4T,GAE/B,IADA,GAAgBzQ,GAAZhB,KAAkB1C,EAAa2D,KAAK9B,QAAQ7B,WAAa,KACtDmU,KAAU,GACbzQ,EAAOlD,SAASC,cAAc,MAC9BiD,EAAK0Q,YACL1Q,EAAKrD,UAAYqH,EAAc,IAAMC,EACrCjE,EAAK2Q,aAAa,OAAQ,UAC1B3Q,EAAKT,MAAMoH,OAASrK,EACpB0D,EAAKT,MAAMqR,UAAYtU,EACvBO,EAAQJ,YAAYuD,GACpBhB,EAAM2B,KAAKX,EAEf,OAAOhB,IAEX6R,mBAAoB,WAAA,GAKP/Q,GAJLgM,EAAS7L,KAAKkG,WAAW2K,QACzBxE,EAAW7Q,EAAE2O,UAGjB,KAFAkC,EAASZ,UACTzL,KAAK4F,eACI/F,EAAI,EAAGA,EAAIgM,EAAOjQ,OAAQiE,IAC/BG,KAAK4F,YAAYiG,EAAOhM,GAAGiR,QACvBxE,IAAKT,EAAOhM,GAAGyM,IACfD,SAAUA,IAItB5C,YAAa,WACT,GAAI7E,GAAO5E,KAAMkF,EAAUN,EAAKM,QAAQ6L,IAAI,GAAI7S,EAAU0G,EAAK1G,QAASgI,EAAatB,EAAKsB,UACtFtB,GAAKC,SACLD,EAAKkD,SAETlD,EAAKgM,qBACLhM,EAAKoM,oBACLpM,EAAKmL,kBACLnL,EAAKgH,UAAY1P,EAAa0I,EAAKwJ,cAAelQ,EAAQ9B,YAAa8B,EAAQ7B,YAC3EuI,EAAKgH,UAAY1F,EAAWiH,UAC5BvI,EAAKgH,UAAY1F,EAAWiH,SAEhCvI,EAAK0C,OAAS1C,EAAK2L,eAAe3L,EAAKhI,QAAQ,GAAIgI,EAAKgH,WACxDhH,EAAKqM,WAAW/S,EAAQ7B,WAAa6J,EAAWiH,SAChDvI,EAAK1G,QAAQ0I,MAAQV,EAAWhF,aAAetF,OAAS,QAAU,OACxC,SAAtBgJ,EAAK1G,QAAQ0I,KACbhC,EAAK3D,OAAOiQ,OAEZtM,EAAK3D,OAAOkQ,OAEhBvM,EAAKgE,OAAShE,EAAKwM,QAAQ,WACvBxM,EAAKwB,cAAa,KAEtBxB,EAAKyM,UAAY,SAAU9S,EAAWK,GAClC,GAAI0S,GAAU1M,EAAK2M,WAAW3M,EAAKgE,OACnC,OAAOhE,GAAK4M,aAAajT,EAAW+S,EAAQ/S,EAAWK,KAE3DgG,EAAKwB,aAAexB,EAAK6M,aAAa/S,EAAewG,EAASN,EAAKyM,WAAYxS,EAAS+F,EAAK8M,aAAa9M,EAAK0C,OAAQ9L,EAAEiI,MAAM5C,EAAQ+D,MACvIA,EAAKwB,eACLxB,EAAK+M,uBAAuB/M,EAAKwJ,eACjCxJ,EAAKgN,kCAETX,WAAY,SAAUvK,GAAV,GACJmL,GASQC,EATOrD,EAAkBzO,KAAKyO,eAM1C,IALKA,EAGDoD,EAAgBpD,EAAgBC,aAFhCD,EAAkBzO,KAAKyO,gBAAkBjS,EAAYwD,KAAKkF,QAAQ,GAAIjB,GAItEyC,IAAWmL,EAEX,IADApD,EAAgBsD,UAAY,GACrBrL,EAAS,GACRoL,EAAYxV,KAAK4Q,IAAIxG,EAAQ,MACjClK,EAAYiS,GAAiBnP,MAAMoH,OAASoL,EAAY,KACxDpL,GAAUoL,GAItBV,QAAS,WACL,GAAIY,GAAqB,KAAM9L,EAAalG,KAAKkG,WAAY+L,EAAiB/L,EAAWoC,OAAQ1B,EAAO5G,KAAK9B,QAAQ0I,KAAMsL,EAAWlS,KAAK4L,UAAWuG,IAMtJ,OALIjM,GAAWgM,WAAaA,GACxBlS,KAAKgI,KAAK,WACN9B,EAAWgM,SAASA,KAGrB,SAAUlT,EAAOoT,GAAjB,GA2BKlJ,GAGQmJ,EACAC,EACKzS,EAAOC,EACRoB,EACK4B,EAAOyP,EAjC5B3N,EAAO5E,IACX,IAAKkG,EAAWsM,QAAQJ,EAAYF,GAkB7B,CAQH,GAPID,IAAmBG,GACnBpS,KAAKgI,KAAK,WACN9B,EAAW1F,MAAM4R,EAAYF,GAC7BD,EAAiBG,IAIZ,UAATxL,EAAkB,CAClB,IAAKuL,EAAWC,GAGZ,IAFIC,EAAYF,EAAWC,MACvBE,EAASpM,EAAWkC,OACfvI,EAAI,EAAGC,EAAMwS,EAAO1W,OAAQiE,EAAIC,EAAKD,IAE1C,IADIqB,EAAQoR,EAAOzS,GACViD,EAAI,EAAGyP,EAAcrR,EAAMnC,MAAMnD,OAAQkH,EAAIyP,EAAazP,IAC/DuP,EAAU3R,MACNX,KAAMmB,EAAMnC,MAAM+D,GAClB5B,MAAOA,EAAMpF,OAK7BoN,GAASiJ,EAAWC,GAAYpT,EAAQoT,OAExClJ,GAAShD,EAAWkC,OAAOpJ,EAAQoT,EAEvC,OAAOlJ,GA3BP,MAhBI8I,KAAuBI,IACvBJ,EAAqBI,EACrBH,EAAiBG,EACbxN,EAAK6N,iBACL7N,EAAK6N,gBAAgBC,SAEzB9N,EAAK6N,gBAAkB7N,EAAK8G,cAAc0G,GAC1CxN,EAAK6N,gBAAgB/F,KAAK,WACtB,GAAIiG,GAAiB/N,EAAKgO,iBAAiBhO,EAAKM,QAAQ,GAAG3G,UAC3DqG,GAAK6N,gBAAkB,KACnBL,GAAcO,GAAkBA,GAAkBP,EAAaF,IAC/DtN,EAAKE,WAAY,EACjBoB,EAAW1F,MAAM4R,EAAYF,OAIlC,OA+BnBV,aAAc,SAAUjT,EAAWD,GAArB,GAGFuU,GAEIC,EAJR5R,EAAQlB,KAAK+S,oBAAqB1W,EAAa2D,KAAK9B,QAAQ7B,WAAY2W,EAA4B1W,KAAK0P,OAAOzN,EAAYD,EAAKG,KAAOpC,GAAa4W,EAAuB3U,EAAKS,MAAMiU,EAS3L,OARIC,IAAwBA,EAAqBlT,OACzC8S,EAAoBI,EAAqB/R,MACzC2R,IAAsB3R,IAClB4R,EAAiBD,GAAqB,GAC1C7S,KAAKiB,OAAOE,KAAKnB,KAAKN,UAAU0B,mBAAmB0R,IACnD9S,KAAK+S,oBAAsBF,IAG5BvU,GAEX4U,YAAa,SAAUnT,EAAMf,EAAOlD,GAAvB,GAWI+D,GAQAiD,EAlBTqQ,EAAWnT,KAAK9B,QAAQ0I,KAAMvK,EAAa2D,KAAK9B,QAAQ7B,WAAY+W,EAAepT,KAAKgO,cAAejM,GAAW,EAAOF,GAAU,EAAOG,GAAW,EAAOd,EAAQ,KAAM2J,GAAQ,EAAOD,EAAc5K,KAAK6I,YAShN,IARiB,UAAbsK,IACIpT,IACAiC,EAAqB,IAAVhD,GAAegB,KAAKqT,iBAAkB,GAASrT,KAAKqT,gBAAkBtT,EAAKmB,MACtFlB,KAAKqT,cAAgBtT,EAAKmB,OAE9BA,EAAQnB,EAAOA,EAAKmB,MAAQ,KAC5BnB,EAAOA,EAAOA,EAAKA,KAAO,MAEE,aAA5BC,KAAK9B,QAAQ6I,YAA6B/G,KAAK0F,mBAAmB9J,QAAUmE,GAC5E,IAASF,EAAI,EAAGA,EAAIG,KAAK0F,mBAAmB9J,OAAQiE,IAEhD,GADAgL,EAAQD,EAAY5K,KAAK0F,mBAAmB7F,MAAQ+K,EAAY7K,GACrD,CACPgC,GAAW,CACX,YAGL,KAAK/B,KAAKsJ,cAAgBxN,EAAMF,QAAUmE,EAC7C,IAAS+C,EAAI,EAAGA,EAAIhH,EAAMF,OAAQkH,IAE9B,GADA+H,EAAQ7O,EAAY+D,GAAQjE,EAAMgH,KAAO/C,EAAOjE,EAAMgH,KAAO8H,EAAY7K,GAC9D,CACPjE,EAAM2E,OAAOqC,EAAG,GAChBf,GAAW,CACX,OAOZ,MAHIqR,KAAiBpU,IACjB6C,GAAU,IAGV9B,KAAMA,EAAOA,EAAO,KACpBmB,MAAOA,EACPc,SAAUA,EACVD,SAAUA,EACVF,QAASA,EACT7C,MAAOA,EACPP,IAAKO,EAAQ3C,IAGrBiX,OAAQ,SAAUtU,GAAV,GACsEe,GAGjEF,EAAWjE,EAHhBgQ,EAAY5L,KAAK4L,UAAW9P,EAAQkE,KAAKyF,QAAQwE,QAASlL,IAG9D,KAFAiB,KAAKuT,SACLvT,KAAKqT,eAAgB,EACZxT,EAAIb,EAAOpD,EAASoD,EAAQ4M,EAAW/L,EAAIjE,EAAQiE,IACxDE,EAAOC,KAAKkT,YAAYlT,KAAK4I,OAAO/I,EAAGb,GAAQa,EAAG/D,GAC9CiD,EAAMA,EAAMnD,OAAS,KACrBmD,EAAMA,EAAMnD,OAAS,GAAGqG,kBAAoBlC,EAAKiC,UAErDjD,EAAM2B,KAAKX,GACXC,KAAKuT,MAAMxT,EAAKf,OAASe,CAG7B,OADAC,MAAK2K,UAAY5L,EACVA,GAEXyU,wBAAyB,SAAUjV,EAAWC,GAC1C,GAAIO,GAAQiB,KAAKsT,OAAOtT,KAAKyT,WAAWlV,EAAWC,GACnD,QACIQ,MAAOD,EAAMnD,OAASmD,EAAM,GAAGC,MAAQ,EACvCP,IAAKM,EAAMnD,OAASmD,EAAM,GAAGN,IAAM,EACnCM,MAAOA,IAGfwS,WAAY,WAAA,GACJpV,GAAe6D,KAAKoO,cAAelQ,EAAU8B,KAAK9B,QAClDwV,EAAezV,EAAcC,EAAS/B,EAC1C,OAAOX,GAAEiI,MAAM,SAAU3H,EAAO8C,GAC5B,GAAIsK,GAASlJ,KAAKkJ,OAAQ1K,EAAgBwB,KAAK2P,cAM/C,QALI/Q,GAAUsK,GAAWwK,EAAaxK,EAAQpN,EAAO0C,KACjD0K,EAASlJ,KAAKwT,wBAAwB1X,EAAO0C,IAEjDwB,KAAK2P,eAAiB7T,EACtBkE,KAAKkJ,OAASA,EACPA,GACRlJ,OAEPyR,aAAc,SAAU7I,EAAQjK,GAC5B,GAAIkD,EACJ,OAAO,UAAUjD,GACb,GAAI+U,GAAS/K,EAAOhK,EAChB+U,KAAW9R,IACXA,EAAU8R,EACVhV,EAASgV,EAAQ/U,MAI7B8S,aAAc,SAAUpT,EAAMQ,GAAhB,GACN8F,GAAO5E,KACPpE,EAAS0C,EAAK1C,OACdgY,IAAiBC,EAAAA,EAErB,OADA/U,GAAUtD,EAAEiI,MAAMhE,EAAKX,EAASkB,KAAKN,WAAYM,MAC1C,SAAU8T,EAAOC,EAAQnV,GAAzB,GAEC4B,GAAOwT,EADPzT,EAAOwT,EAASH,CAEhBhV,IAAStC,KAAK2X,IAAI1T,IAAS3E,GAC3B4E,EAAQlC,EACR0V,EAASF,IAETtT,EAAQF,EAAQhC,EAAMiC,GACtByT,EAASzT,EAAO,EAAIuT,EAAM7J,OAAO1J,GAAQuT,EAAM7J,MAAM,GAAI1J,IAE7DzB,EAAQ0B,EAAOwT,EAAQpP,EAAKC,SAC5B+O,EAAgBG,IAGxBG,aAAc,WACV,GAAIhW,GAAU8B,KAAK9B,OACnB,OAAOL,GAAYmC,KAAKoO,cAAelQ,EAAQ9B,YAAa8B,EAAQyI,iBAExEiM,iBAAkB,SAAUzV,GACxB,GAAIyO,GAAY5L,KAAK4L,UAAWvP,EAAa2D,KAAK9B,QAAQ7B,WAAY8Q,EAAQnN,KAAKkG,WAAWiH,OAC9F,OAAO7Q,MAAK4Q,IAAI5Q,KAAK6X,IAAIhH,EAAQvB,EAAW,GAAItP,KAAK6X,IAAI,EAAG7X,KAAK0P,MAAM7O,EAAWd,MAEtFoX,WAAY,SAAUlV,EAAWC,GAC7B,GAAmCrB,GAA/BiX,EAAUpU,KAAKkU,cAEnB,OADA/W,GAAWoB,GAAaA,EAAYC,EAAgB4V,EAAQrW,KAAOqW,EAAQpW,IACpEgC,KAAK4S,iBAAiBzV,IAEjCoJ,YAAa,WACLvG,KAAK9B,QAAQ2I,aACb7G,KAAKmH,aAAe3L,EAAEiI,MAAMzD,KAAM,iBAClCA,KAAKpD,QAAQ4I,GAAGpB,EAAQI,EAAiB,IAAMT,EAAa/D,KAAKmH,gBAGzEkN,gBAAiB,SAAUzX,GACvB,MAAMA,aAAmB0X,QAGlB9R,SAAS5F,EAAQ2E,KAAK,qBAAsB,IAFxC9F,GAIf+P,aAAc,SAAUkC,GAAV,GACOvN,GAGJsK,EAUT8J,EAbArL,IACJ,IAAyB,kBAAdwE,GAEP,IADAvN,EAAOH,KAAKkG,WAAW2H,WACdpD,EAAM,EAAGA,EAAMtK,EAAKvE,OAAQ6O,IACjC,GAAIiD,EAAUvN,EAAKsK,IAAO,CACtBvB,EAAOxI,KAAK+J,EACZ,OAcZ,MAVyB,gBAAdiD,IACPxE,EAAOxI,KAAKgN,GAEZ6G,EAAevU,KAAKqU,gBAAgB3G,GACnCI,MAAMyG,IACPrL,EAAOxI,KAAK6T,GAEZ7G,YAAqB3R,SACrBmN,EAASwE,GAENxE,GAEXmC,UAAW,SAAUwD,GAAV,GACW2F,GAAevY,EAAgN8D,EAAM8K,EAG1OJ,EAgBA5K,EAKQiD,EAxBjBsI,KAAuCqJ,EAAkBzU,KAAK2F,iBAAkB2H,EAAoBtN,KAAK0F,mBAAoBvI,EAAW,EAAG0J,EAAa7G,KAAK9B,QAAQ2I,WAAY6N,EAAwB,EAAG9J,EAAc5K,KAAK6I,aAA2BK,EAAS,IAEvQ,IADA2F,EAAUA,EAAQ5E,QACdpD,KAAe,GAASgI,EAAQjT,QAgB7B,GAAmB,aAAfiL,EACP,IAAShH,EAAI,EAAGA,EAAIgP,EAAQjT,OAAQiE,IAAK,CAIrC,GAHAqJ,EAAS,KACT/L,EAAW3B,EAAEmZ,QAAQ9F,EAAQhP,GAAI4U,GACjCxY,EAAW+D,KAAKoN,gBAAgByB,EAAQhP,IACpC1C,QAAmBlB,EACnB,IAAS6G,EAAI,EAAGA,EAAIwK,EAAkB1R,OAAQkH,IAC1C+H,EAAQ7O,EAAYC,GAAYqR,EAAkBxK,KAAO7G,EAAW2O,EAAY0C,EAAkBxK,MAAQ8H,EAAY3O,GAClH4O,IACA9K,EAAOC,KAAK+N,mBAAmBc,EAAQhP,IACvCqJ,EAASlJ,KAAK4U,oBAAoB7U,EAAM+C,EAAG+L,EAAQhP,GAAI6U,QAI/DF,GAAgBC,EAAgBtX,GAC5BqX,IAAkB/Y,IAClBsE,EAAOC,KAAK+N,mBAAmByG,GAC/BtL,EAASlJ,KAAK4U,oBAAoB7U,EAAM5C,EAAUqX,EAAeE,GAGrExL,KACA2F,EAAQpO,OAAOZ,EAAG,GAClBuL,EAAQ1K,KAAKwI,GACbwL,IACA7U,UAxCgC,CACxC,IAAS4K,EAAM,EAAGA,EAAMgK,EAAgB7Y,OAAQ6O,IACxCgK,EAAgBhK,KAAShP,EACzBuE,KAAK+N,mBAAmB0G,EAAgBhK,IAAMlF,YAAYzD,GACnDwL,EAAkB7C,IACzBzK,KAAKgJ,sBAAsBsE,EAAkB7C,IAAMlF,YAAYzD,GAEnEsJ,EAAQ1K,MACJ1B,MAAOyV,EAAgBhK,GACvBtN,SAAUsN,EACVxO,SAAUqR,EAAkB7C,IAGpCzK,MAAKyF,WACLzF,KAAK0F,sBACL1F,KAAK2F,oBA6BT,OACIkJ,QAASA,EACTzD,QAASA,IAGjBwJ,oBAAqB,SAAU7U,EAAM5C,EAAUqX,EAAeE,GAC1D,GAAIzY,EACJ,IAAK8D,EAAKkO,SAAS,oBAOnB,MAJAlO,GAAKwF,YAAYzD,GACjB9B,KAAKyF,QAAQhF,OAAOtD,EAAU,GAC9B6C,KAAK2F,iBAAiBlF,OAAOtD,EAAU,GACvClB,EAAW+D,KAAK0F,mBAAmBjF,OAAOtD,EAAU,GAAG,IAEnD6B,MAAOwV,EACPrX,SAAUA,EAAWuX,EACrBzY,SAAUA,IAGlBkT,uBAAwB,SAAUN,GAAV,GAEhB/S,GAAOkD,EAAO7B,EAId2F,EALA+R,EAAW7U,KAAKpD,QAAQ,GAAGiY,SAE3BC,EAAS9U,KAAKyF,QACd2F,KACAX,EAAM,CAEV,IAAgC,aAA5BzK,KAAK9B,QAAQ2I,aAA8B7G,KAAKsJ,aAChD,QAEJ,IAAIuF,EAAQ,QAWR,MAVArT,GAAEqZ,GAAUtP,YAAY,oBACxB6F,EAAU5P,EAAEsN,IAAI9I,KAAK0F,mBAAmBuE,MAAM,GAAI,SAAUhO,EAAUwO,GAClE,OACIxO,SAAUA,EACVkB,SAAUsN,KAGlBzK,KAAK2F,oBACL3F,KAAK0F,sBACL1F,KAAKyF,WACE2F,CAEX,MAAOX,EAAMoE,EAAQjT,OAAQ6O,IAAO,CAMhC,IALAtN,KACA6B,EAAQ6P,EAAQpE,GACZzK,KAAKoN,gBAAgBpO,KACrBlD,EAAQkE,KAAK6I,aAAa7I,KAAKoN,gBAAgBpO,KAE9C8D,EAAI,EAAGA,EAAIgS,EAAOlZ,OAAQkH,IAC3B,GAAIhH,GAASgZ,EAAOhS,GAAI,CACpB3F,EAAW2F,CACX,OAGJ3F,OACAiO,EAAQ1K,KAAKV,KAAK6J,SAAS1M,IAC3B3B,EAAEqZ,EAAS7V,IAAQuG,YAAY,qBAGvC,MAAO6F,IAEX0B,SAAU,SAAU9N,EAAO2M,GACvB,GAAIoJ,GAAO/V,EAAQ2M,EAAO,EAAIrP,KAAK0P,MAAMhN,EAAQ2M,GAAQ,CACzD,QAAQoJ,EAAO,GAAKpJ,GAExByD,QAAS,SAAUjE,GACf,GAAyGlP,GAAU+Y,EAA/GpQ,EAAO5E,KAAMgP,EAA8C,aAA5BhP,KAAK9B,QAAQ2I,WAA2BX,EAAalG,KAAKkG,WAA+ByF,EAAO3L,KAAK4L,UAAWhB,EAAc5K,KAAK6I,aAAc0C,IAwBpL,OAvBIyD,KACApK,EAAKe,oBACLf,EAAKc,sBACLd,EAAKa,YAETuP,EAAU9O,EAAWoC,OACrB9M,EAAE2Q,KAAKhB,EAAS,SAAUiB,EAAGpN,GACzB,GAAIsJ,GAAO1D,EAAKkI,SAAS9N,EAAO2M,EAChC/G,GAAKoD,KAAK,WACN9B,EAAW1F,MAAM8H,EAAMqD,GACvB1P,EAAW2I,EAAKmI,cAAc7G,EAAWkC,QAASpJ,EAAQsJ,IAC1D1D,EAAKe,iBAAiBjF,KAAK1B,GAC3B4F,EAAKc,mBAAmBhF,KAAKzE,GAC7B2I,EAAKa,QAAQ/E,KAAK1E,EAAYC,GAAYA,EAAW2O,EAAY3O,IACjEsP,EAAM7K,MACF1B,MAAOA,EACP/C,SAAUA,IAEd2I,EAAKmJ,mBAAmB/O,GAAOiG,SAASnD,GACxCoE,EAAW1F,MAAMwU,EAASrJ,OAGlC/G,EAAKa,QAAUb,EAAK0F,kBAAkB1F,EAAKa,SACpC8F,GAEX0J,cAAe,SAAUhM,GACrB,GAAIlJ,GAAOvE,EAAEyN,EAAEiM,gBACVjM,EAAEkM,sBAAwBpV,EAAKwB,KAAK,aACrCvB,KAAKC,QAAQmE,GAASrE,KAAMA,KAGpCiR,kBAAmB,WACfhR,KAAK6I,aAAepL,EAAMmL,OAAO5I,KAAK9B,QAAQ4I,iBAElD6K,uBAAwB,SAAUjL,GAC9B,GAAI0O,GAAYpV,KAAKjB,QAAQsW,QAASC,EAActV,KAAKiB,OAAQsU,EAAU,CACvED,GAAY,IAAuC,SAAjCA,EAAY,GAAGhW,MAAMkW,UACxB,SAAX9O,IACA6O,EAAU9X,EAAMyB,QAAQuW,aAE5BF,GAAW/X,WAAW4X,EAAUlY,IAAI,sBAAuB,IAAMM,WAAW4X,EAAUP,SAAS,YAAY3X,IAAI,SAAU,IACzHoY,EAAYpY,IAAI,gBAAiBqY,KAGzC3D,+BAAgC,WAAA,GAEpB8D,GACAD,EACAE,EACAxI,CAJJnN,MAAK9B,QAAQuD,SAAWzB,KAAK9B,QAAQuD,QAAQ7F,SACzC8Z,EAAQjY,EAAMyB,QAAQwW,MAAM1V,KAAKoF,SACjCqQ,EAAYhY,EAAMyB,QAAQuW,YAC1BE,EAAgB3V,KAAKkF,QAAQzI,SAASA,SAASkB,KAAK,kBACpDwP,EAAQnN,KAAKkG,WAAWiH,QAC5BwI,EAAczY,IAAIwY,EAAQ,eAAiB,gBAAiBvI,EAAQsI,EAAY,MAI5FhY,GAAM4C,GAAGoE,YAAcA,EACvBhH,EAAM4C,GAAGuV,OAAOnR,IAClBnB,OAAO7F,MAAM6W,QACRhR,OAAO7F,OACE,kBAAVlC,SAAwBA,OAAOsa,IAAMta,OAAS,SAAUua,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.virtuallist.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.virtuallist', ['kendo.data'], f);\n}(function () {\n    var __meta__ = {\n        id: 'virtuallist',\n        name: 'VirtualList',\n        category: 'framework',\n        depends: ['data'],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Widget = ui.Widget, DataBoundWidget = ui.DataBoundWidget, proxy = $.proxy, percentageUnitsRegex = /^\\d+(\\.\\d+)?%$/i, WRAPPER = 'k-virtual-wrap', VIRTUALLIST = 'k-virtual-list', CONTENT = 'k-virtual-content', LIST = 'k-list', HEADER = 'k-group-header', VIRTUALITEM = 'k-virtual-item', ITEM = 'k-item', HEIGHTCONTAINER = 'k-height-container', GROUPITEM = 'k-group', SELECTED = 'k-state-selected', FOCUSED = 'k-state-focused', HOVER = 'k-state-hover', CHANGE = 'change', CLICK = 'click', LISTBOUND = 'listBound', ITEMCHANGE = 'itemChange', ACTIVATE = 'activate', DEACTIVATE = 'deactivate', VIRTUAL_LIST_NS = '.VirtualList';\n        function lastFrom(array) {\n            return array[array.length - 1];\n        }\n        function toArray(value) {\n            return value instanceof Array ? value : [value];\n        }\n        function isPrimitive(dataItem) {\n            return typeof dataItem === 'string' || typeof dataItem === 'number' || typeof dataItem === 'boolean';\n        }\n        function getItemCount(screenHeight, listScreens, itemHeight) {\n            return Math.ceil(screenHeight * listScreens / itemHeight);\n        }\n        function appendChild(parent, className, tagName) {\n            var element = document.createElement(tagName || 'div');\n            if (className) {\n                element.className = className;\n            }\n            parent.appendChild(element);\n            return element;\n        }\n        function getDefaultItemHeight() {\n            var mockList = $('<div class=\"k-popup\"><ul class=\"k-list\"><li class=\"k-item\"><li></ul></div>'), lineHeight;\n            mockList.css({\n                position: 'absolute',\n                left: '-200000px',\n                visibility: 'hidden'\n            });\n            mockList.appendTo(document.body);\n            lineHeight = parseFloat(kendo.getComputedStyles(mockList.find('.k-item')[0], ['line-height'])['line-height']);\n            mockList.remove();\n            return lineHeight;\n        }\n        function bufferSizes(screenHeight, listScreens, opposite) {\n            return {\n                down: screenHeight * opposite,\n                up: screenHeight * (listScreens - 1 - opposite)\n            };\n        }\n        function listValidator(options, screenHeight) {\n            var downThreshold = (options.listScreens - 1 - options.threshold) * screenHeight;\n            var upThreshold = options.threshold * screenHeight;\n            return function (list, scrollTop, lastScrollTop) {\n                if (scrollTop > lastScrollTop) {\n                    return scrollTop - list.top < downThreshold;\n                } else {\n                    return list.top === 0 || scrollTop - list.top > upThreshold;\n                }\n            };\n        }\n        function scrollCallback(element, callback) {\n            return function (force) {\n                return callback(element.scrollTop, force);\n            };\n        }\n        function syncList(reorder) {\n            return function (list, force) {\n                reorder(list.items, list.index, force);\n                return list;\n            };\n        }\n        function position(element, y) {\n            if (kendo.support.browser.msie && kendo.support.browser.version < 10) {\n                element.style.top = y + 'px';\n            } else {\n                element.style.webkitTransform = 'translateY(' + y + 'px)';\n                element.style.transform = 'translateY(' + y + 'px)';\n            }\n        }\n        function map2(callback, templates) {\n            return function (arr1, arr2) {\n                for (var i = 0, len = arr1.length; i < len; i++) {\n                    callback(arr1[i], arr2[i], templates);\n                    if (arr2[i].item) {\n                        this.trigger(ITEMCHANGE, {\n                            item: $(arr1[i]),\n                            data: arr2[i].item,\n                            ns: kendo.ui\n                        });\n                    }\n                }\n            };\n        }\n        function reshift(items, diff) {\n            var range;\n            if (diff > 0) {\n                range = items.splice(0, diff);\n                items.push.apply(items, range);\n            } else {\n                range = items.splice(diff, -diff);\n                items.unshift.apply(items, range);\n            }\n            return range;\n        }\n        function render(element, data, templates) {\n            var itemTemplate = templates.template;\n            element = $(element);\n            if (!data.item) {\n                itemTemplate = templates.placeholderTemplate;\n            }\n            if (data.index === 0 && this.header && data.group) {\n                this.header.html(templates.fixedGroupTemplate(data.group));\n            }\n            this.angular('cleanup', function () {\n                return { elements: [element] };\n            });\n            element.attr('data-uid', data.item ? data.item.uid : '').attr('data-offset-index', data.index);\n            if (this.options.columns && this.options.columns.length && data.item) {\n                element.html(renderColumns(this.options, data.item, templates));\n            } else {\n                element.html(itemTemplate(data.item || {}));\n            }\n            element.toggleClass(FOCUSED, data.current);\n            element.toggleClass(SELECTED, data.selected);\n            element.toggleClass('k-first', data.newGroup);\n            element.toggleClass('k-last', data.isLastGroupedItem);\n            element.toggleClass('k-loading-item', !data.item);\n            if (data.index !== 0 && data.newGroup) {\n                $('<div class=' + GROUPITEM + '></div>').appendTo(element).html(templates.groupTemplate(data.group));\n            }\n            if (data.top !== undefined) {\n                position(element[0], data.top);\n            }\n            this.angular('compile', function () {\n                return {\n                    elements: [element],\n                    data: [{\n                            dataItem: data.item,\n                            group: data.group,\n                            newGroup: data.newGroup\n                        }]\n                };\n            });\n        }\n        function renderColumns(options, dataItem, templates) {\n            var item = '';\n            for (var i = 0; i < options.columns.length; i++) {\n                var currentWidth = options.columns[i].width;\n                var currentWidthInt = parseInt(currentWidth, 10);\n                var widthStyle = '';\n                if (currentWidth) {\n                    widthStyle += 'style=\\'width:';\n                    widthStyle += currentWidthInt;\n                    widthStyle += percentageUnitsRegex.test(currentWidth) ? '%' : 'px';\n                    widthStyle += ';\\'';\n                }\n                item += '<span class=\\'k-cell\\' ' + widthStyle + '>';\n                item += templates['column' + i](dataItem);\n                item += '</span>';\n            }\n            return item;\n        }\n        function mapChangedItems(selected, itemsToMatch) {\n            var itemsLength = itemsToMatch.length;\n            var selectedLength = selected.length;\n            var dataItem;\n            var found;\n            var i, j;\n            var changed = [];\n            var unchanged = [];\n            if (selectedLength) {\n                for (i = 0; i < selectedLength; i++) {\n                    dataItem = selected[i];\n                    found = false;\n                    for (j = 0; j < itemsLength; j++) {\n                        if (dataItem === itemsToMatch[j]) {\n                            found = true;\n                            changed.push({\n                                index: i,\n                                item: dataItem\n                            });\n                            break;\n                        }\n                    }\n                    if (!found) {\n                        unchanged.push(dataItem);\n                    }\n                }\n            }\n            return {\n                changed: changed,\n                unchanged: unchanged\n            };\n        }\n        function isActivePromise(promise) {\n            return promise && promise.state() !== 'resolved';\n        }\n        var VirtualList = DataBoundWidget.extend({\n            init: function (element, options) {\n                var that = this;\n                that.bound(false);\n                that._fetching = false;\n                Widget.fn.init.call(that, element, options);\n                if (!that.options.itemHeight) {\n                    that.options.itemHeight = getDefaultItemHeight();\n                }\n                options = that.options;\n                that.element.addClass(LIST + ' ' + VIRTUALLIST).attr('role', 'listbox');\n                that.content = that.element.wrap('<div unselectable=\\'on\\' class=\\'' + CONTENT + '\\'></div>').parent();\n                that.wrapper = that.content.wrap('<div class=\\'' + WRAPPER + '\\'></div>').parent();\n                that.header = that.content.before('<div class=\\'' + HEADER + '\\'></div>').prev();\n                if (options.columns && options.columns.length) {\n                    that.element.removeClass(LIST);\n                }\n                that.element.on('mouseenter' + VIRTUAL_LIST_NS, 'li:not(.k-loading-item)', function () {\n                    $(this).addClass(HOVER);\n                }).on('mouseleave' + VIRTUAL_LIST_NS, 'li', function () {\n                    $(this).removeClass(HOVER);\n                });\n                that._values = toArray(that.options.value);\n                that._selectedDataItems = [];\n                that._selectedIndexes = [];\n                that._rangesList = {};\n                that._promisesList = [];\n                that._optionID = kendo.guid();\n                that._templates();\n                that.setDataSource(options.dataSource);\n                that.content.on('scroll' + VIRTUAL_LIST_NS, kendo.throttle(function () {\n                    that._renderItems();\n                    that._triggerListBound();\n                }, options.delay));\n                that._selectable();\n            },\n            options: {\n                name: 'VirtualList',\n                autoBind: true,\n                delay: 100,\n                height: null,\n                listScreens: 4,\n                threshold: 0.5,\n                itemHeight: null,\n                oppositeBuffer: 1,\n                type: 'flat',\n                selectable: false,\n                value: [],\n                dataValueField: null,\n                template: '#:data#',\n                placeholderTemplate: 'loading...',\n                groupTemplate: '#:data#',\n                fixedGroupTemplate: '#:data#',\n                mapValueTo: 'index',\n                valueMapper: null\n            },\n            events: [\n                CHANGE,\n                CLICK,\n                LISTBOUND,\n                ITEMCHANGE,\n                ACTIVATE,\n                DEACTIVATE\n            ],\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n                if (this._selectProxy && this.options.selectable === false) {\n                    this.element.off(CLICK, '.' + VIRTUALITEM, this._selectProxy);\n                } else if (!this._selectProxy && this.options.selectable) {\n                    this._selectable();\n                }\n                this._templates();\n                this.refresh();\n            },\n            items: function () {\n                return $(this._items);\n            },\n            destroy: function () {\n                this.wrapper.off(VIRTUAL_LIST_NS);\n                this.dataSource.unbind(CHANGE, this._refreshHandler);\n                Widget.fn.destroy.call(this);\n            },\n            setDataSource: function (source) {\n                var that = this;\n                var dataSource = source || {};\n                var value;\n                dataSource = $.isArray(dataSource) ? { data: dataSource } : dataSource;\n                dataSource = kendo.data.DataSource.create(dataSource);\n                if (that.dataSource) {\n                    that.dataSource.unbind(CHANGE, that._refreshHandler);\n                    that._clean();\n                    that.bound(false);\n                    that._deferValueSet = true;\n                    value = that.value();\n                    that.value([]);\n                    that.mute(function () {\n                        that.value(value);\n                    });\n                } else {\n                    that._refreshHandler = $.proxy(that.refresh, that);\n                }\n                that.dataSource = dataSource.bind(CHANGE, that._refreshHandler);\n                that.setDSFilter(dataSource.filter());\n                if (dataSource.view().length !== 0) {\n                    that.refresh();\n                } else if (that.options.autoBind) {\n                    dataSource.fetch();\n                }\n            },\n            skip: function () {\n                return this.dataSource.currentRangeStart();\n            },\n            _triggerListBound: function () {\n                var that = this;\n                var skip = that.skip();\n                if (that.bound() && !that._selectingValue && that._skip !== skip) {\n                    that._skip = skip;\n                    that.trigger(LISTBOUND);\n                }\n            },\n            _getValues: function (dataItems) {\n                var getter = this._valueGetter;\n                return $.map(dataItems, function (dataItem) {\n                    return getter(dataItem);\n                });\n            },\n            _highlightSelectedItems: function () {\n                for (var i = 0; i < this._selectedDataItems.length; i++) {\n                    var item = this._getElementByDataItem(this._selectedDataItems[i]);\n                    if (item.length) {\n                        item.addClass(SELECTED);\n                    }\n                }\n            },\n            refresh: function (e) {\n                var that = this;\n                var action = e && e.action;\n                var isItemChange = action === 'itemchange';\n                var filtered = this.isFiltered();\n                var result;\n                if (that._mute) {\n                    return;\n                }\n                that._deferValueSet = false;\n                if (!that._fetching) {\n                    if (filtered) {\n                        that.focus(0);\n                    }\n                    that._createList();\n                    if (!action && that._values.length && !filtered && !that.options.skipUpdateOnBind && !that._emptySearch) {\n                        that._selectingValue = true;\n                        that.bound(true);\n                        that.value(that._values, true).done(function () {\n                            that._selectingValue = false;\n                            that._triggerListBound();\n                        });\n                    } else {\n                        that.bound(true);\n                        that._highlightSelectedItems();\n                        that._triggerListBound();\n                    }\n                } else {\n                    if (that._renderItems) {\n                        that._renderItems(true);\n                    }\n                    that._triggerListBound();\n                }\n                if (isItemChange || action === 'remove') {\n                    result = mapChangedItems(that._selectedDataItems, e.items);\n                    if (result.changed.length) {\n                        if (isItemChange) {\n                            that.trigger('selectedItemChange', { items: result.changed });\n                        } else {\n                            that.value(that._getValues(result.unchanged));\n                        }\n                    }\n                }\n                that._fetching = false;\n            },\n            removeAt: function (position) {\n                this._selectedIndexes.splice(position, 1);\n                this._values.splice(position, 1);\n                return {\n                    position: position,\n                    dataItem: this._selectedDataItems.splice(position, 1)[0]\n                };\n            },\n            setValue: function (value) {\n                this._values = toArray(value);\n            },\n            value: function (value, _forcePrefetch) {\n                var that = this;\n                if (value === undefined) {\n                    return that._values.slice();\n                }\n                if (value === null) {\n                    value = [];\n                }\n                value = toArray(value);\n                if (!that._valueDeferred || that._valueDeferred.state() === 'resolved') {\n                    that._valueDeferred = $.Deferred();\n                }\n                var shouldClear = that.options.selectable === 'multiple' && that.select().length && value.length;\n                if (shouldClear || !value.length) {\n                    that.select(-1);\n                }\n                that._values = value;\n                if (that.bound() && !that._mute && !that._deferValueSet || _forcePrefetch) {\n                    that._prefetchByValue(value);\n                }\n                return that._valueDeferred;\n            },\n            _checkValuesOrder: function (value) {\n                if (this._removedAddedIndexes && this._removedAddedIndexes.length === value.length) {\n                    var newValue = this._removedAddedIndexes.slice();\n                    this._removedAddedIndexes = null;\n                    return newValue;\n                }\n                return value;\n            },\n            _prefetchByValue: function (value) {\n                var that = this, dataView = that._dataView, valueGetter = that._valueGetter, mapValueTo = that.options.mapValueTo, item, match = false, forSelection = [];\n                for (var i = 0; i < value.length; i++) {\n                    for (var idx = 0; idx < dataView.length; idx++) {\n                        item = dataView[idx].item;\n                        if (item) {\n                            match = isPrimitive(item) ? value[i] === item : value[i] === valueGetter(item);\n                            if (match) {\n                                forSelection.push(dataView[idx].index);\n                            }\n                        }\n                    }\n                }\n                if (forSelection.length === value.length) {\n                    that._values = [];\n                    that.select(forSelection);\n                    return;\n                }\n                if (typeof that.options.valueMapper === 'function') {\n                    that.options.valueMapper({\n                        value: this.options.selectable === 'multiple' ? value : value[0],\n                        success: function (response) {\n                            if (mapValueTo === 'index') {\n                                that.mapValueToIndex(response);\n                            } else if (mapValueTo === 'dataItem') {\n                                that.mapValueToDataItem(response);\n                            }\n                        }\n                    });\n                } else {\n                    if (!that.value()[0]) {\n                        that.select([-1]);\n                    } else {\n                        that._selectingValue = false;\n                        that._triggerListBound();\n                    }\n                }\n            },\n            mapValueToIndex: function (indexes) {\n                if (indexes === undefined || indexes === -1 || indexes === null) {\n                    indexes = [];\n                } else {\n                    indexes = toArray(indexes);\n                }\n                if (!indexes.length) {\n                    indexes = [-1];\n                } else {\n                    var removed = this._deselect([]).removed;\n                    if (removed.length) {\n                        this._triggerChange(removed, []);\n                    }\n                }\n                this.select(indexes);\n            },\n            mapValueToDataItem: function (dataItems) {\n                var removed, added;\n                if (dataItems === undefined || dataItems === null) {\n                    dataItems = [];\n                } else {\n                    dataItems = toArray(dataItems);\n                }\n                if (!dataItems.length) {\n                    this.select([-1]);\n                } else {\n                    removed = $.map(this._selectedDataItems, function (item, index) {\n                        return {\n                            index: index,\n                            dataItem: item\n                        };\n                    });\n                    added = $.map(dataItems, function (item, index) {\n                        return {\n                            index: index,\n                            dataItem: item\n                        };\n                    });\n                    this._selectedDataItems = dataItems;\n                    this._selectedIndexes = [];\n                    for (var i = 0; i < this._selectedDataItems.length; i++) {\n                        var item = this._getElementByDataItem(this._selectedDataItems[i]);\n                        this._selectedIndexes.push(this._getIndecies(item)[0]);\n                        item.addClass(SELECTED);\n                    }\n                    this._triggerChange(removed, added);\n                    if (this._valueDeferred) {\n                        this._valueDeferred.resolve();\n                    }\n                }\n            },\n            deferredRange: function (index) {\n                var dataSource = this.dataSource;\n                var take = this.itemCount;\n                var ranges = this._rangesList;\n                var result = $.Deferred();\n                var defs = [];\n                var low = Math.floor(index / take) * take;\n                var high = Math.ceil(index / take) * take;\n                var pages = high === low ? [high] : [\n                    low,\n                    high\n                ];\n                $.each(pages, function (_, skip) {\n                    var end = skip + take;\n                    var existingRange = ranges[skip];\n                    var deferred;\n                    if (!existingRange || existingRange.end !== end) {\n                        deferred = $.Deferred();\n                        ranges[skip] = {\n                            end: end,\n                            deferred: deferred\n                        };\n                        dataSource._multiplePrefetch(skip, take, function () {\n                            deferred.resolve();\n                        });\n                    } else {\n                        deferred = existingRange.deferred;\n                    }\n                    defs.push(deferred);\n                });\n                $.when.apply($, defs).then(function () {\n                    result.resolve();\n                });\n                return result;\n            },\n            prefetch: function (indexes) {\n                var that = this, take = this.itemCount, isEmptyList = !that._promisesList.length;\n                if (!isActivePromise(that._activeDeferred)) {\n                    that._activeDeferred = $.Deferred();\n                    that._promisesList = [];\n                }\n                $.each(indexes, function (_, index) {\n                    that._promisesList.push(that.deferredRange(that._getSkip(index, take)));\n                });\n                if (isEmptyList) {\n                    $.when.apply($, that._promisesList).done(function () {\n                        that._promisesList = [];\n                        that._activeDeferred.resolve();\n                    });\n                }\n                return that._activeDeferred;\n            },\n            _findDataItem: function (view, index) {\n                var group;\n                if (this.options.type === 'group') {\n                    for (var i = 0; i < view.length; i++) {\n                        group = view[i].items;\n                        if (group.length <= index) {\n                            index = index - group.length;\n                        } else {\n                            return group[index];\n                        }\n                    }\n                }\n                return view[index];\n            },\n            _getRange: function (skip, take) {\n                return this.dataSource._findRange(skip, Math.min(skip + take, this.dataSource.total()));\n            },\n            dataItemByIndex: function (index) {\n                var that = this;\n                var take = that.itemCount;\n                var skip = that._getSkip(index, take);\n                var view = this._getRange(skip, take);\n                if (!that._getRange(skip, take).length) {\n                    return null;\n                }\n                if (that.options.type === 'group') {\n                    kendo.ui.progress($(that.wrapper), true);\n                    that.mute(function () {\n                        that.dataSource.range(skip, take, function () {\n                            kendo.ui.progress($(that.wrapper), false);\n                        });\n                        view = that.dataSource.view();\n                    });\n                }\n                return that._findDataItem(view, [index - skip]);\n            },\n            selectedDataItems: function () {\n                return this._selectedDataItems.slice();\n            },\n            scrollWith: function (value) {\n                this.content.scrollTop(this.content.scrollTop() + value);\n            },\n            scrollTo: function (y) {\n                this.content.scrollTop(y);\n            },\n            scrollToIndex: function (index) {\n                this.scrollTo(index * this.options.itemHeight);\n            },\n            focus: function (candidate) {\n                var element, index, data, current, itemHeight = this.options.itemHeight, id = this._optionID, triggerEvent = true;\n                if (candidate === undefined) {\n                    current = this.element.find('.' + FOCUSED);\n                    return current.length ? current : null;\n                }\n                if (typeof candidate === 'function') {\n                    data = this.dataSource.flatView();\n                    for (var idx = 0; idx < data.length; idx++) {\n                        if (candidate(data[idx])) {\n                            candidate = idx;\n                            break;\n                        }\n                    }\n                }\n                if (candidate instanceof Array) {\n                    candidate = lastFrom(candidate);\n                }\n                if (isNaN(candidate)) {\n                    element = $(candidate);\n                    index = parseInt($(element).attr('data-offset-index'), 10);\n                } else {\n                    index = candidate;\n                    element = this._getElementByIndex(index);\n                }\n                if (index === -1) {\n                    this.element.find('.' + FOCUSED).removeClass(FOCUSED);\n                    this._focusedIndex = undefined;\n                    return;\n                }\n                if (element.length) {\n                    if (element.hasClass(FOCUSED)) {\n                        triggerEvent = false;\n                    }\n                    if (this._focusedIndex !== undefined) {\n                        current = this._getElementByIndex(this._focusedIndex);\n                        current.removeClass(FOCUSED).removeAttr('id');\n                        if (triggerEvent) {\n                            this.trigger(DEACTIVATE);\n                        }\n                    }\n                    this._focusedIndex = index;\n                    element.addClass(FOCUSED).attr('id', id);\n                    var position = this._getElementLocation(index);\n                    if (position === 'top') {\n                        this.scrollTo(index * itemHeight);\n                    } else if (position === 'bottom') {\n                        this.scrollTo(index * itemHeight + itemHeight - this._screenHeight);\n                    } else if (position === 'outScreen') {\n                        this.scrollTo(index * itemHeight);\n                    }\n                    if (triggerEvent) {\n                        this.trigger(ACTIVATE);\n                    }\n                } else {\n                    this._focusedIndex = index;\n                    this.items().removeClass(FOCUSED);\n                    this.scrollToIndex(index);\n                }\n            },\n            focusIndex: function () {\n                return this._focusedIndex;\n            },\n            focusFirst: function () {\n                this.scrollTo(0);\n                this.focus(0);\n            },\n            focusLast: function () {\n                var lastIndex = this.dataSource.total();\n                this.scrollTo(this.heightContainer.offsetHeight);\n                this.focus(lastIndex - 1);\n            },\n            focusPrev: function () {\n                var index = this._focusedIndex;\n                var current;\n                if (!isNaN(index) && index > 0) {\n                    index -= 1;\n                    this.focus(index);\n                    current = this.focus();\n                    if (current && current.hasClass('k-loading-item')) {\n                        index += 1;\n                        this.focus(index);\n                    }\n                    return index;\n                } else {\n                    index = this.dataSource.total() - 1;\n                    this.focus(index);\n                    return index;\n                }\n            },\n            focusNext: function () {\n                var index = this._focusedIndex;\n                var lastIndex = this.dataSource.total() - 1;\n                var current;\n                if (!isNaN(index) && index < lastIndex) {\n                    index += 1;\n                    this.focus(index);\n                    current = this.focus();\n                    if (current && current.hasClass('k-loading-item')) {\n                        index -= 1;\n                        this.focus(index);\n                    }\n                    return index;\n                } else {\n                    index = 0;\n                    this.focus(index);\n                    return index;\n                }\n            },\n            _triggerChange: function (removed, added) {\n                removed = removed || [];\n                added = added || [];\n                if (removed.length || added.length) {\n                    this.trigger(CHANGE, {\n                        removed: removed,\n                        added: added\n                    });\n                }\n            },\n            select: function (candidate) {\n                var that = this, indices, initialIndices, singleSelection = that.options.selectable !== 'multiple', prefetchStarted = isActivePromise(that._activeDeferred), filtered = this.isFiltered(), isAlreadySelected, deferred, result, removed = [];\n                if (candidate === undefined) {\n                    return that._selectedIndexes.slice();\n                }\n                if (!that._selectDeferred || that._selectDeferred.state() === 'resolved') {\n                    that._selectDeferred = $.Deferred();\n                }\n                indices = that._getIndecies(candidate);\n                isAlreadySelected = singleSelection && !filtered && lastFrom(indices) === lastFrom(this._selectedIndexes);\n                removed = that._deselectCurrentValues(indices);\n                if (removed.length || !indices.length || isAlreadySelected) {\n                    that._triggerChange(removed);\n                    if (that._valueDeferred) {\n                        that._valueDeferred.resolve().promise();\n                    }\n                    return that._selectDeferred.resolve().promise();\n                }\n                if (indices.length === 1 && indices[0] === -1) {\n                    indices = [];\n                }\n                initialIndices = indices;\n                result = that._deselect(indices);\n                removed = result.removed;\n                indices = result.indices;\n                if (singleSelection) {\n                    prefetchStarted = false;\n                    if (indices.length) {\n                        indices = [lastFrom(indices)];\n                    }\n                }\n                var done = function () {\n                    var added = that._select(indices);\n                    if (initialIndices.length === indices.length || singleSelection) {\n                        that.focus(indices);\n                    }\n                    that._triggerChange(removed, added);\n                    if (that._valueDeferred) {\n                        that._valueDeferred.resolve();\n                    }\n                    that._selectDeferred.resolve();\n                };\n                deferred = that.prefetch(indices);\n                if (!prefetchStarted) {\n                    if (deferred) {\n                        deferred.done(done);\n                    } else {\n                        done();\n                    }\n                }\n                return that._selectDeferred.promise();\n            },\n            bound: function (bound) {\n                if (bound === undefined) {\n                    return this._listCreated;\n                }\n                this._listCreated = bound;\n            },\n            mute: function (callback) {\n                this._mute = true;\n                proxy(callback(), this);\n                this._mute = false;\n            },\n            setDSFilter: function (filter) {\n                this._lastDSFilter = $.extend({}, filter);\n            },\n            isFiltered: function () {\n                if (!this._lastDSFilter) {\n                    this.setDSFilter(this.dataSource.filter());\n                }\n                return !kendo.data.Query.compareFilters(this.dataSource.filter(), this._lastDSFilter);\n            },\n            skipUpdate: $.noop,\n            _getElementByIndex: function (index) {\n                return this.items().filter(function (idx, element) {\n                    return index === parseInt($(element).attr('data-offset-index'), 10);\n                });\n            },\n            _getElementByDataItem: function (dataItem) {\n                var dataView = this._dataView, valueGetter = this._valueGetter, element, match;\n                for (var i = 0; i < dataView.length; i++) {\n                    match = dataView[i].item && isPrimitive(dataView[i].item) ? dataView[i].item === dataItem : dataView[i].item && dataItem && valueGetter(dataView[i].item) == valueGetter(dataItem);\n                    if (match) {\n                        element = dataView[i];\n                        break;\n                    }\n                }\n                return element ? this._getElementByIndex(element.index) : $();\n            },\n            _clean: function () {\n                this.result = undefined;\n                this._lastScrollTop = undefined;\n                this._skip = undefined;\n                $(this.heightContainer).remove();\n                this.heightContainer = undefined;\n                this.element.empty();\n            },\n            _height: function () {\n                var hasData = !!this.dataSource.view().length, height = this.options.height, itemHeight = this.options.itemHeight, total = this.dataSource.total();\n                if (!hasData) {\n                    height = 0;\n                } else if (height / itemHeight > total) {\n                    height = total * itemHeight;\n                }\n                return height;\n            },\n            setScreenHeight: function () {\n                var height = this._height();\n                this.content.height(height);\n                this._screenHeight = height;\n            },\n            screenHeight: function () {\n                return this._screenHeight;\n            },\n            _getElementLocation: function (index) {\n                var scrollTop = this.content.scrollTop(), screenHeight = this._screenHeight, itemHeight = this.options.itemHeight, yPosition = index * itemHeight, yDownPostion = yPosition + itemHeight, screenEnd = scrollTop + screenHeight, position;\n                if (yPosition === scrollTop - itemHeight || yDownPostion > scrollTop && yPosition < scrollTop) {\n                    position = 'top';\n                } else if (yPosition === screenEnd || yPosition < screenEnd && screenEnd < yDownPostion) {\n                    position = 'bottom';\n                } else if (yPosition >= scrollTop && yPosition <= scrollTop + (screenHeight - itemHeight)) {\n                    position = 'inScreen';\n                } else {\n                    position = 'outScreen';\n                }\n                return position;\n            },\n            _templates: function () {\n                var options = this.options;\n                var templates = {\n                    template: options.template,\n                    placeholderTemplate: options.placeholderTemplate,\n                    groupTemplate: options.groupTemplate,\n                    fixedGroupTemplate: options.fixedGroupTemplate\n                };\n                if (options.columns) {\n                    for (var i = 0; i < options.columns.length; i++) {\n                        var currentColumn = options.columns[i];\n                        var templateText = currentColumn.field ? currentColumn.field.toString() : 'text';\n                        templates['column' + i] = currentColumn.template || '#: ' + templateText + '#';\n                    }\n                }\n                for (var key in templates) {\n                    if (typeof templates[key] !== 'function') {\n                        templates[key] = kendo.template(templates[key] || '');\n                    }\n                }\n                this.templates = templates;\n            },\n            _generateItems: function (element, count) {\n                var items = [], item, itemHeight = this.options.itemHeight + 'px';\n                while (count-- > 0) {\n                    item = document.createElement('li');\n                    item.tabIndex = -1;\n                    item.className = VIRTUALITEM + ' ' + ITEM;\n                    item.setAttribute('role', 'option');\n                    item.style.height = itemHeight;\n                    item.style.minHeight = itemHeight;\n                    element.appendChild(item);\n                    items.push(item);\n                }\n                return items;\n            },\n            _saveInitialRanges: function () {\n                var ranges = this.dataSource._ranges;\n                var deferred = $.Deferred();\n                deferred.resolve();\n                this._rangesList = {};\n                for (var i = 0; i < ranges.length; i++) {\n                    this._rangesList[ranges[i].start] = {\n                        end: ranges[i].end,\n                        deferred: deferred\n                    };\n                }\n            },\n            _createList: function () {\n                var that = this, content = that.content.get(0), options = that.options, dataSource = that.dataSource;\n                if (that.bound()) {\n                    that._clean();\n                }\n                that._saveInitialRanges();\n                that._buildValueGetter();\n                that.setScreenHeight();\n                that.itemCount = getItemCount(that._screenHeight, options.listScreens, options.itemHeight);\n                if (that.itemCount > dataSource.total()) {\n                    that.itemCount = dataSource.total();\n                }\n                that._items = that._generateItems(that.element[0], that.itemCount);\n                that._setHeight(options.itemHeight * dataSource.total());\n                that.options.type = (dataSource.group() || []).length ? 'group' : 'flat';\n                if (that.options.type === 'flat') {\n                    that.header.hide();\n                } else {\n                    that.header.show();\n                }\n                that.getter = that._getter(function () {\n                    that._renderItems(true);\n                });\n                that._onScroll = function (scrollTop, force) {\n                    var getList = that._listItems(that.getter);\n                    return that._fixedHeader(scrollTop, getList(scrollTop, force));\n                };\n                that._renderItems = that._whenChanged(scrollCallback(content, that._onScroll), syncList(that._reorderList(that._items, $.proxy(render, that))));\n                that._renderItems();\n                that._calculateGroupPadding(that._screenHeight);\n                that._calculateColumnsHeaderPadding();\n            },\n            _setHeight: function (height) {\n                var currentHeight, heightContainer = this.heightContainer;\n                if (!heightContainer) {\n                    heightContainer = this.heightContainer = appendChild(this.content[0], HEIGHTCONTAINER);\n                } else {\n                    currentHeight = heightContainer.offsetHeight;\n                }\n                if (height !== currentHeight) {\n                    heightContainer.innerHTML = '';\n                    while (height > 0) {\n                        var padHeight = Math.min(height, 250000);\n                        appendChild(heightContainer).style.height = padHeight + 'px';\n                        height -= padHeight;\n                    }\n                }\n            },\n            _getter: function () {\n                var lastRequestedRange = null, dataSource = this.dataSource, lastRangeStart = dataSource.skip(), type = this.options.type, pageSize = this.itemCount, flatGroups = {};\n                if (dataSource.pageSize() < pageSize) {\n                    this.mute(function () {\n                        dataSource.pageSize(pageSize);\n                    });\n                }\n                return function (index, rangeStart) {\n                    var that = this;\n                    if (!dataSource.inRange(rangeStart, pageSize)) {\n                        if (lastRequestedRange !== rangeStart) {\n                            lastRequestedRange = rangeStart;\n                            lastRangeStart = rangeStart;\n                            if (that._getterDeferred) {\n                                that._getterDeferred.reject();\n                            }\n                            that._getterDeferred = that.deferredRange(rangeStart);\n                            that._getterDeferred.then(function () {\n                                var firstItemIndex = that._indexConstraint(that.content[0].scrollTop);\n                                that._getterDeferred = null;\n                                if (rangeStart <= firstItemIndex && firstItemIndex <= rangeStart + pageSize) {\n                                    that._fetching = true;\n                                    dataSource.range(rangeStart, pageSize);\n                                }\n                            });\n                        }\n                        return null;\n                    } else {\n                        if (lastRangeStart !== rangeStart) {\n                            this.mute(function () {\n                                dataSource.range(rangeStart, pageSize);\n                                lastRangeStart = rangeStart;\n                            });\n                        }\n                        var result;\n                        if (type === 'group') {\n                            if (!flatGroups[rangeStart]) {\n                                var flatGroup = flatGroups[rangeStart] = [];\n                                var groups = dataSource.view();\n                                for (var i = 0, len = groups.length; i < len; i++) {\n                                    var group = groups[i];\n                                    for (var j = 0, groupLength = group.items.length; j < groupLength; j++) {\n                                        flatGroup.push({\n                                            item: group.items[j],\n                                            group: group.value\n                                        });\n                                    }\n                                }\n                            }\n                            result = flatGroups[rangeStart][index - rangeStart];\n                        } else {\n                            result = dataSource.view()[index - rangeStart];\n                        }\n                        return result;\n                    }\n                };\n            },\n            _fixedHeader: function (scrollTop, list) {\n                var group = this.currentVisibleGroup, itemHeight = this.options.itemHeight, firstVisibleDataItemIndex = Math.floor((scrollTop - list.top) / itemHeight), firstVisibleDataItem = list.items[firstVisibleDataItemIndex];\n                if (firstVisibleDataItem && firstVisibleDataItem.item) {\n                    var firstVisibleGroup = firstVisibleDataItem.group;\n                    if (firstVisibleGroup !== group) {\n                        var fixedGroupText = firstVisibleGroup || '';\n                        this.header.html(this.templates.fixedGroupTemplate(fixedGroupText));\n                        this.currentVisibleGroup = firstVisibleGroup;\n                    }\n                }\n                return list;\n            },\n            _itemMapper: function (item, index, value) {\n                var listType = this.options.type, itemHeight = this.options.itemHeight, currentIndex = this._focusedIndex, selected = false, current = false, newGroup = false, group = null, match = false, valueGetter = this._valueGetter;\n                if (listType === 'group') {\n                    if (item) {\n                        newGroup = index === 0 || this._currentGroup !== false && this._currentGroup !== item.group;\n                        this._currentGroup = item.group;\n                    }\n                    group = item ? item.group : null;\n                    item = item ? item.item : null;\n                }\n                if (this.options.mapValueTo === 'dataItem' && this._selectedDataItems.length && item) {\n                    for (var i = 0; i < this._selectedDataItems.length; i++) {\n                        match = valueGetter(this._selectedDataItems[i]) === valueGetter(item);\n                        if (match) {\n                            selected = true;\n                            break;\n                        }\n                    }\n                } else if (!this.isFiltered() && value.length && item) {\n                    for (var j = 0; j < value.length; j++) {\n                        match = isPrimitive(item) ? value[j] === item : value[j] === valueGetter(item);\n                        if (match) {\n                            value.splice(j, 1);\n                            selected = true;\n                            break;\n                        }\n                    }\n                }\n                if (currentIndex === index) {\n                    current = true;\n                }\n                return {\n                    item: item ? item : null,\n                    group: group,\n                    newGroup: newGroup,\n                    selected: selected,\n                    current: current,\n                    index: index,\n                    top: index * itemHeight\n                };\n            },\n            _range: function (index) {\n                var itemCount = this.itemCount, value = this._values.slice(), items = [], item;\n                this._view = {};\n                this._currentGroup = false;\n                for (var i = index, length = index + itemCount; i < length; i++) {\n                    item = this._itemMapper(this.getter(i, index), i, value);\n                    if (items[items.length - 1]) {\n                        items[items.length - 1].isLastGroupedItem = item.newGroup;\n                    }\n                    items.push(item);\n                    this._view[item.index] = item;\n                }\n                this._dataView = items;\n                return items;\n            },\n            _getDataItemsCollection: function (scrollTop, lastScrollTop) {\n                var items = this._range(this._listIndex(scrollTop, lastScrollTop));\n                return {\n                    index: items.length ? items[0].index : 0,\n                    top: items.length ? items[0].top : 0,\n                    items: items\n                };\n            },\n            _listItems: function () {\n                var screenHeight = this._screenHeight, options = this.options;\n                var theValidator = listValidator(options, screenHeight);\n                return $.proxy(function (value, force) {\n                    var result = this.result, lastScrollTop = this._lastScrollTop;\n                    if (force || !result || !theValidator(result, value, lastScrollTop)) {\n                        result = this._getDataItemsCollection(value, lastScrollTop);\n                    }\n                    this._lastScrollTop = value;\n                    this.result = result;\n                    return result;\n                }, this);\n            },\n            _whenChanged: function (getter, callback) {\n                var current;\n                return function (force) {\n                    var theNew = getter(force);\n                    if (theNew !== current) {\n                        current = theNew;\n                        callback(theNew, force);\n                    }\n                };\n            },\n            _reorderList: function (list, reorder) {\n                var that = this;\n                var length = list.length;\n                var currentOffset = -Infinity;\n                reorder = $.proxy(map2(reorder, this.templates), this);\n                return function (list2, offset, force) {\n                    var diff = offset - currentOffset;\n                    var range, range2;\n                    if (force || Math.abs(diff) >= length) {\n                        range = list;\n                        range2 = list2;\n                    } else {\n                        range = reshift(list, diff);\n                        range2 = diff > 0 ? list2.slice(-diff) : list2.slice(0, -diff);\n                    }\n                    reorder(range, range2, that.bound());\n                    currentOffset = offset;\n                };\n            },\n            _bufferSizes: function () {\n                var options = this.options;\n                return bufferSizes(this._screenHeight, options.listScreens, options.oppositeBuffer);\n            },\n            _indexConstraint: function (position) {\n                var itemCount = this.itemCount, itemHeight = this.options.itemHeight, total = this.dataSource.total();\n                return Math.min(Math.max(total - itemCount, 0), Math.max(0, Math.floor(position / itemHeight)));\n            },\n            _listIndex: function (scrollTop, lastScrollTop) {\n                var buffers = this._bufferSizes(), position;\n                position = scrollTop - (scrollTop > lastScrollTop ? buffers.down : buffers.up);\n                return this._indexConstraint(position);\n            },\n            _selectable: function () {\n                if (this.options.selectable) {\n                    this._selectProxy = $.proxy(this, '_clickHandler');\n                    this.element.on(CLICK + VIRTUAL_LIST_NS, '.' + VIRTUALITEM, this._selectProxy);\n                }\n            },\n            getElementIndex: function (element) {\n                if (!(element instanceof jQuery)) {\n                    return undefined;\n                }\n                return parseInt(element.attr('data-offset-index'), 10);\n            },\n            _getIndecies: function (candidate) {\n                var result = [], data;\n                if (typeof candidate === 'function') {\n                    data = this.dataSource.flatView();\n                    for (var idx = 0; idx < data.length; idx++) {\n                        if (candidate(data[idx])) {\n                            result.push(idx);\n                            break;\n                        }\n                    }\n                }\n                if (typeof candidate === 'number') {\n                    result.push(candidate);\n                }\n                var elementIndex = this.getElementIndex(candidate);\n                if (!isNaN(elementIndex)) {\n                    result.push(elementIndex);\n                }\n                if (candidate instanceof Array) {\n                    result = candidate;\n                }\n                return result;\n            },\n            _deselect: function (indices) {\n                var removed = [], selectedIndex, dataItem, selectedIndexes = this._selectedIndexes, selectedDataItems = this._selectedDataItems, position = 0, selectable = this.options.selectable, removedindexesCounter = 0, valueGetter = this._valueGetter, item, match, result = null;\n                indices = indices.slice();\n                if (selectable === true || !indices.length) {\n                    for (var idx = 0; idx < selectedIndexes.length; idx++) {\n                        if (selectedIndexes[idx] !== undefined) {\n                            this._getElementByIndex(selectedIndexes[idx]).removeClass(SELECTED);\n                        } else if (selectedDataItems[idx]) {\n                            this._getElementByDataItem(selectedDataItems[idx]).removeClass(SELECTED);\n                        }\n                        removed.push({\n                            index: selectedIndexes[idx],\n                            position: idx,\n                            dataItem: selectedDataItems[idx]\n                        });\n                    }\n                    this._values = [];\n                    this._selectedDataItems = [];\n                    this._selectedIndexes = [];\n                } else if (selectable === 'multiple') {\n                    for (var i = 0; i < indices.length; i++) {\n                        result = null;\n                        position = $.inArray(indices[i], selectedIndexes);\n                        dataItem = this.dataItemByIndex(indices[i]);\n                        if (position === -1 && dataItem) {\n                            for (var j = 0; j < selectedDataItems.length; j++) {\n                                match = isPrimitive(dataItem) ? selectedDataItems[j] === dataItem : valueGetter(selectedDataItems[j]) === valueGetter(dataItem);\n                                if (match) {\n                                    item = this._getElementByIndex(indices[i]);\n                                    result = this._deselectSingleItem(item, j, indices[i], removedindexesCounter);\n                                }\n                            }\n                        } else {\n                            selectedIndex = selectedIndexes[position];\n                            if (selectedIndex !== undefined) {\n                                item = this._getElementByIndex(selectedIndex);\n                                result = this._deselectSingleItem(item, position, selectedIndex, removedindexesCounter);\n                            }\n                        }\n                        if (result) {\n                            indices.splice(i, 1);\n                            removed.push(result);\n                            removedindexesCounter++;\n                            i--;\n                        }\n                    }\n                }\n                return {\n                    indices: indices,\n                    removed: removed\n                };\n            },\n            _deselectSingleItem: function (item, position, selectedIndex, removedindexesCounter) {\n                var dataItem;\n                if (!item.hasClass('k-state-selected')) {\n                    return;\n                }\n                item.removeClass(SELECTED);\n                this._values.splice(position, 1);\n                this._selectedIndexes.splice(position, 1);\n                dataItem = this._selectedDataItems.splice(position, 1)[0];\n                return {\n                    index: selectedIndex,\n                    position: position + removedindexesCounter,\n                    dataItem: dataItem\n                };\n            },\n            _deselectCurrentValues: function (indices) {\n                var children = this.element[0].children;\n                var value, index, position;\n                var values = this._values;\n                var removed = [];\n                var idx = 0;\n                var j;\n                if (this.options.selectable !== 'multiple' || !this.isFiltered()) {\n                    return [];\n                }\n                if (indices[0] === -1) {\n                    $(children).removeClass('k-state-selected');\n                    removed = $.map(this._selectedDataItems.slice(0), function (dataItem, idx) {\n                        return {\n                            dataItem: dataItem,\n                            position: idx\n                        };\n                    });\n                    this._selectedIndexes = [];\n                    this._selectedDataItems = [];\n                    this._values = [];\n                    return removed;\n                }\n                for (; idx < indices.length; idx++) {\n                    position = -1;\n                    index = indices[idx];\n                    if (this.dataItemByIndex(index)) {\n                        value = this._valueGetter(this.dataItemByIndex(index));\n                    }\n                    for (j = 0; j < values.length; j++) {\n                        if (value == values[j]) {\n                            position = j;\n                            break;\n                        }\n                    }\n                    if (position > -1) {\n                        removed.push(this.removeAt(position));\n                        $(children[index]).removeClass('k-state-selected');\n                    }\n                }\n                return removed;\n            },\n            _getSkip: function (index, take) {\n                var page = index < take ? 1 : Math.floor(index / take) + 1;\n                return (page - 1) * take;\n            },\n            _select: function (indexes) {\n                var that = this, singleSelection = this.options.selectable !== 'multiple', dataSource = this.dataSource, dataItem, oldSkip, take = this.itemCount, valueGetter = this._valueGetter, added = [];\n                if (singleSelection) {\n                    that._selectedIndexes = [];\n                    that._selectedDataItems = [];\n                    that._values = [];\n                }\n                oldSkip = dataSource.skip();\n                $.each(indexes, function (_, index) {\n                    var skip = that._getSkip(index, take);\n                    that.mute(function () {\n                        dataSource.range(skip, take);\n                        dataItem = that._findDataItem(dataSource.view(), [index - skip]);\n                        that._selectedIndexes.push(index);\n                        that._selectedDataItems.push(dataItem);\n                        that._values.push(isPrimitive(dataItem) ? dataItem : valueGetter(dataItem));\n                        added.push({\n                            index: index,\n                            dataItem: dataItem\n                        });\n                        that._getElementByIndex(index).addClass(SELECTED);\n                        dataSource.range(oldSkip, take);\n                    });\n                });\n                that._values = that._checkValuesOrder(that._values);\n                return added;\n            },\n            _clickHandler: function (e) {\n                var item = $(e.currentTarget);\n                if (!e.isDefaultPrevented() && item.attr('data-uid')) {\n                    this.trigger(CLICK, { item: item });\n                }\n            },\n            _buildValueGetter: function () {\n                this._valueGetter = kendo.getter(this.options.dataValueField);\n            },\n            _calculateGroupPadding: function (height) {\n                var firstItem = this.items().first(), groupHeader = this.header, padding = 0;\n                if (groupHeader[0] && groupHeader[0].style.display !== 'none') {\n                    if (height !== 'auto') {\n                        padding = kendo.support.scrollbar();\n                    }\n                    padding += parseFloat(firstItem.css('border-right-width'), 10) + parseFloat(firstItem.children('.k-group').css('right'), 10);\n                    groupHeader.css('padding-right', padding);\n                }\n            },\n            _calculateColumnsHeaderPadding: function () {\n                if (this.options.columns && this.options.columns.length) {\n                    var isRtl = kendo.support.isRtl(this.wrapper);\n                    var scrollbar = kendo.support.scrollbar();\n                    var columnsHeader = this.content.parent().parent().find('.k-grid-header');\n                    var total = this.dataSource.total();\n                    columnsHeader.css(isRtl ? 'padding-left' : 'padding-right', total ? scrollbar : 0);\n                }\n            }\n        });\n        kendo.ui.VirtualList = VirtualList;\n        kendo.ui.plugin(VirtualList);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}