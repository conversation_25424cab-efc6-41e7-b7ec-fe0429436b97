/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.mobile.scrollview.min",["kendo.fx.min","kendo.data.min","kendo.draganddrop.min"],e)}(function(){return function(e,t){function n(e){return"k-"+e+" km-"+e}var i,s,a,o,r,h,g,c,p=window.kendo,l=p.mobile,u=l.ui,d=e.proxy,f=p.effects.Transition,m=p.ui.Pane,v=p.ui.PaneDimensions,_=u.DataBoundWidget,P=p.data.DataSource,w=p.data.Buffer,b=p.data.Batch<PERSON><PERSON>er,y=Math,x=y.abs,S=y.ceil,T=y.round,C=y.max,z=y.min,E=y.floor,O="change",k="changing",R="refresh",D="current-page",M="virtual-page",V="function",W="itemChange",B="cleanup",H=3,I=-1,U=0,A=1,j=-1,q=0,F=1,Q=p.Class.extend({init:function(t){var i=this,s=e("<ol class='"+n("pages")+"'/>");t.element.append(s),this._changeProxy=d(i,"_change"),this._refreshProxy=d(i,"_refresh"),t.bind(O,this._changeProxy),t.bind(R,this._refreshProxy),e.extend(i,{element:s,scrollView:t})},items:function(){return this.element.children()},_refresh:function(e){var t,i="";for(t=0;t<e.pageCount;t++)i+="<li/>";this.element.html(i),this.items().eq(e.page).addClass(n(D))},_change:function(e){this.items().removeClass(n(D)).eq(e.page).addClass(n(D))},destroy:function(){this.scrollView.unbind(O,this._changeProxy),this.scrollView.unbind(R,this._refreshProxy),this.element.remove()}});p.mobile.ui.ScrollViewPager=Q,i="transitionEnd",s="dragStart",a="dragEnd",o=p.Observable.extend({init:function(t,n){var o,r,h,g,c,l,u=this;p.Observable.fn.init.call(this),this.element=t,this.container=t.parent(),o=new p.ui.Movable(u.element),r=new f({axis:"x",movable:o,onEnd:function(){u.trigger(i)}}),h=new p.UserEvents(t,{fastTap:!0,start:function(e){2*x(e.x.velocity)>=x(e.y.velocity)?h.capture():h.cancel(),u.trigger(s,e),r.cancel()},allowSelection:!0,end:function(e){u.trigger(a,e)}}),g=new v({element:u.element,container:u.container}),c=g.x,c.bind(O,function(){u.trigger(O)}),l=new m({dimensions:g,userEvents:h,movable:o,elastic:!0}),e.extend(u,{duration:n&&n.duration||1,movable:o,transition:r,userEvents:h,dimensions:g,dimension:c,pane:l}),this.bind([i,s,a,O],n)},size:function(){return{width:this.dimensions.x.getSize(),height:this.dimensions.y.getSize()}},total:function(){return this.dimension.getTotal()},offset:function(){return-this.movable.x},updateDimension:function(){this.dimension.update(!0)},refresh:function(){this.dimensions.refresh()},moveTo:function(e){this.movable.moveAxis("x",-e)},transitionTo:function(e,t,n){n?this.moveTo(-e):this.transition.moveTo({location:e,duration:this.duration,ease:t})}}),p.mobile.ui.ScrollViewElasticPane=o,r=p.Observable.extend({init:function(e,t,n){var i=this;p.Observable.fn.init.call(this),i.element=e,i.pane=t,i._getPages(),this.page=0,this.pageSize=n.pageSize||1,this.contentHeight=n.contentHeight,this.enablePager=n.enablePager,this.pagerOverlay=n.pagerOverlay},scrollTo:function(e,t){this.page=e,this.pane.transitionTo(-e*this.pane.size().width,f.easeOutExpo,t)},paneMoved:function(e,t,n,i){var s,a,o=this,r=o.pane,h=r.size().width*o.pageSize,g=T,c=t?f.easeOutBack:f.easeOutExpo;e===j?g=S:e===F&&(g=E),a=g(r.offset()/h),s=C(o.minSnap,z(-a*h,o.maxSnap)),a!=o.page&&n&&n({currentPage:o.page,nextPage:a})&&(s=-o.page*r.size().width),r.transitionTo(s,c,i)},updatePage:function(){var e=this.pane,t=T(e.offset()/e.size().width);return t!=this.page&&(this.page=t,!0)},forcePageUpdate:function(){return this.updatePage()},resizeTo:function(e){var t,n,i=this.pane,s=e.width;this.pageElements.width(s),"100%"===this.contentHeight&&(t=this.element.parent().height(),this.enablePager===!0&&(n=this.element.parent().find("ol.km-pages"),!this.pagerOverlay&&n.length&&(t-=p._outerHeight(n,!0))),this.element.css("height",t),this.pageElements.css("height",t)),i.updateDimension(),this._paged||(this.page=E(i.offset()/s)),this.scrollTo(this.page,!0),this.pageCount=S(i.total()/s),this.minSnap=-(this.pageCount-1)*s,this.maxSnap=0},_getPages:function(){this.pageElements=this.element.find(p.roleSelector("page")),this._paged=this.pageElements.length>0}}),p.mobile.ui.ScrollViewContent=r,h=p.Observable.extend({init:function(e,t,n){var i=this;p.Observable.fn.init.call(this),i.element=e,i.pane=t,i.options=n,i._templates(),i.page=n.page||0,i.pages=[],i._initPages(),i.resizeTo(i.pane.size()),i.pane.dimension.forceEnabled()},setDataSource:function(e){this.dataSource=P.create(e),this._buffer(),this._pendingPageRefresh=!1,this._pendingWidgetRefresh=!1},_viewShow:function(){var e=this;e._pendingWidgetRefresh&&(setTimeout(function(){e._resetPages()},0),e._pendingWidgetRefresh=!1)},_buffer:function(){var e=this.options.itemsPerPage;this.buffer&&this.buffer.destroy(),this.buffer=e>1?new b(this.dataSource,e):new w(this.dataSource,3*e),this._resizeProxy=d(this,"_onResize"),this._resetProxy=d(this,"_onReset"),this._endReachedProxy=d(this,"_onEndReached"),this.buffer.bind({resize:this._resizeProxy,reset:this._resetProxy,endreached:this._endReachedProxy})},_templates:function(){var e=this.options.template,t=this.options.emptyTemplate,n={},i={};typeof e===V&&(n.template=e,e="#=this.template(data)#"),this.template=d(p.template(e),n),typeof t===V&&(i.emptyTemplate=t,t="#=this.emptyTemplate(data)#"),this.emptyTemplate=d(p.template(t),i)},_initPages:function(){var e,t,n=this.pages,i=this.element;for(t=0;t<H;t++)e=new g(i),n.push(e);this.pane.updateDimension()},resizeTo:function(e){var t,n,i,s=this.pages,a=this.pane;for(t=0;t<s.length;t++)s[t].setWidth(e.width);"auto"===this.options.contentHeight?this.element.css("height",this.pages[1].element.height()):"100%"===this.options.contentHeight&&(n=this.element.parent().height(),this.options.enablePager===!0&&(i=this.element.parent().find("ol.km-pages"),!this.options.pagerOverlay&&i.length&&(n-=p._outerHeight(i,!0))),this.element.css("height",n),s[0].element.css("height",n),s[1].element.css("height",n),s[2].element.css("height",n)),a.updateDimension(),this._repositionPages(),this.width=e.width},scrollTo:function(e){var t,n=this.buffer;n.syncDataSource(),t=n.at(e),t&&(this._updatePagesContent(e),this.page=e)},paneMoved:function(e,t,n,i){var s,a=this,o=a.pane,r=o.size().width,h=o.offset(),g=Math.abs(h)>=r/3,c=t?p.effects.Transition.easeOutBack:p.effects.Transition.easeOutExpo,l=a.page+2>a.buffer.total(),u=0;e===F?0!==a.page&&(u=-1):e!==j||l?h>0&&g&&!l?u=1:h<0&&g&&0!==a.page&&(u=-1):u=1,s=a.page,u&&(s=u>0?s+1:s-1),n&&n({currentPage:a.page,nextPage:s})&&(u=0),0===u?a._cancelMove(c,i):u===-1?a._moveBackward(i):1===u&&a._moveForward(i)},updatePage:function(){var e=this.pages;return 0!==this.pane.offset()&&(this.pane.offset()>0?(e.push(this.pages.shift()),this.page++,this.setPageContent(e[2],this.page+1)):(e.unshift(this.pages.pop()),this.page--,this.setPageContent(e[0],this.page-1)),this._repositionPages(),this._resetMovable(),!0)},forcePageUpdate:function(){var e=this.pane.offset(),t=3*this.pane.size().width/4;return x(e)>t&&this.updatePage()},_resetMovable:function(){this.pane.moveTo(0)},_moveForward:function(e){this.pane.transitionTo(-this.width,p.effects.Transition.easeOutExpo,e)},_moveBackward:function(e){this.pane.transitionTo(this.width,p.effects.Transition.easeOutExpo,e)},_cancelMove:function(e,t){this.pane.transitionTo(0,e,t)},_resetPages:function(){this.page=this.options.page||0,this._updatePagesContent(this.page),this._repositionPages(),this.trigger("reset")},_onResize:function(){this.pageCount=S(this.dataSource.total()/this.options.itemsPerPage),this._pendingPageRefresh&&(this._updatePagesContent(this.page),this._pendingPageRefresh=!1),this.trigger("resize")},_onReset:function(){this.pageCount=S(this.dataSource.total()/this.options.itemsPerPage),this._resetPages()},_onEndReached:function(){this._pendingPageRefresh=!0},_repositionPages:function(){var e=this.pages;e[0].position(I),e[1].position(U),e[2].position(A)},_updatePagesContent:function(e){var t=this.pages,n=e||0;this.setPageContent(t[0],n-1),this.setPageContent(t[1],n),this.setPageContent(t[2],n+1)},setPageContent:function(t,n){var i=this.buffer,s=this.template,a=this.emptyTemplate,o=null;n>=0&&(o=i.at(n),e.isArray(o)&&!o.length&&(o=null)),this.trigger(B,{item:t.element}),t.content(null!==o?s(o):a({})),p.mobile.init(t.element),this.trigger(W,{item:t.element,data:o,ns:p.mobile.ui})}}),p.mobile.ui.VirtualScrollViewContent=h,g=p.Class.extend({init:function(t){this.element=e("<div class='"+n(M)+"'></div>"),this.width=t.width(),this.element.width(this.width),t.append(this.element)},content:function(e){this.element.html(e)},position:function(e){this.element.css("transform","translate3d("+this.width*e+"px, 0, 0)")},setWidth:function(e){this.width=e,this.element.width(e)}}),p.mobile.ui.VirtualPage=g,c=_.extend({init:function(e,t){var i,s,a,g=this;_.fn.init.call(g,e,t),t=g.options,e=g.element,p.stripWhitespace(e[0]),e.wrapInner("<div/>").addClass("k-widget "+n("scrollview")),this.options.enablePager&&(this.pager=new Q(this),this.options.pagerOverlay&&e.addClass(n("scrollview-overlay"))),g.inner=e.children().first(),g.page=0,g.inner.css("height",t.contentHeight),g.pane=new o(g.inner,{duration:this.options.duration,transitionEnd:d(this,"_transitionEnd"),dragStart:d(this,"_dragStart"),dragEnd:d(this,"_dragEnd"),change:d(this,R)}),g.bind("resize",function(){g.pane.refresh()}),g.page=t.page,i=0===this.inner.children().length,s=i?new h(g.inner,g.pane,t):new r(g.inner,g.pane,t),s.page=g.page,s.bind("reset",function(){this._pendingPageRefresh=!1,g._syncWithContent(),g.trigger(R,{pageCount:s.pageCount,page:s.page})}),s.bind("resize",function(){g.trigger(R,{pageCount:s.pageCount,page:s.page})}),s.bind(W,function(e){g.trigger(W,e),g.angular("compile",function(){return{elements:e.item,data:[{dataItem:e.data}]}})}),s.bind(B,function(e){g.angular("cleanup",function(){return{elements:e.item}})}),g._content=s,g.setDataSource(t.dataSource),a=g.container(),a.nullObject?(g.viewInit(),g.viewShow()):a.bind("show",d(this,"viewShow")).bind("init",d(this,"viewInit"))},options:{name:"ScrollView",page:0,duration:400,velocityThreshold:.8,contentHeight:"auto",pageSize:1,itemsPerPage:1,bounceVelocityThreshold:1.6,enablePager:!0,pagerOverlay:!1,autoBind:!0,template:"",emptyTemplate:""},events:[k,O,R],destroy:function(){_.fn.destroy.call(this),p.destroy(this.element)},viewInit:function(){this.options.autoBind&&this._content.scrollTo(this._content.page,!0)},viewShow:function(){this.pane.refresh()},refresh:function(){var e=this._content;e.resizeTo(this.pane.size()),this.page=e.page,this.trigger(R,{pageCount:e.pageCount,page:e.page})},content:function(e){this.element.children().first().html(e),this._content._getPages(),this.pane.refresh()},value:function(e){var n=this.dataSource;return e?(this.scrollTo(n.indexOf(e),!0),t):n.at(this.page)},scrollTo:function(e,t){this._content.scrollTo(e,t),this._syncWithContent()},prev:function(){var e=this,n=e.page-1;e._content instanceof h?e._content.paneMoved(F,t,function(t){return e.trigger(k,t)}):n>-1&&e.scrollTo(n)},next:function(){var e=this,n=e.page+1;e._content instanceof h?e._content.paneMoved(j,t,function(t){return e.trigger(k,t)}):n<e._content.pageCount&&e.scrollTo(n)},setDataSource:function(e){if(this._content instanceof h){var t=!e;this.dataSource=P.create(e),this._content.setDataSource(this.dataSource),this.options.autoBind&&!t&&this.dataSource.fetch()}},items:function(){return this.element.find(".km-"+M)},_syncWithContent:function(){var e,n,i=this._content.pages,s=this._content.buffer;this.page=this._content.page,e=s?s.at(this.page):t,e instanceof Array||(e=[e]),n=i?i[1].element:t,this.trigger(O,{page:this.page,element:n,data:e})},_dragStart:function(){this._content.forcePageUpdate()&&this._syncWithContent()},_dragEnd:function(e){var t=this,n=e.x.velocity,i=this.options.velocityThreshold,s=q,a=x(n)>this.options.bounceVelocityThreshold;n>i?s=F:n<-i&&(s=j),this._content.paneMoved(s,a,function(e){return t.trigger(k,e)})},_transitionEnd:function(){this._content.updatePage()&&this._syncWithContent()}}),u.plugin(c)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.mobile.scrollview.min.js.map
