{"version": 3, "sources": ["kendo.pdf.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "pdf", "supportsDeflate", "pako", "deflate", "data", "BinaryStream", "eof", "offset", "readByte", "writeByte", "b", "ensure", "readShort", "writeShort", "w", "readShort_", "writeShort_", "readLong", "writeLong", "readLong_", "writeLong_", "readFixed", "writeFixed", "Math", "round", "readFixed_", "writeFixed_", "read", "len", "times", "readString", "fromCharCode", "apply", "writeString", "n", "reader", "ret", "Array", "write", "slice", "stream", "HAS_TYPED_ARRAYS", "Uint8Array", "tmp", "max", "set", "buffer", "bytes", "start", "x", "pos", "skip", "nbytes", "toString", "Error", "saveExcursion", "writeBase64", "base64", "atob", "BASE64", "decode", "encode", "ucs2decode", "string", "extra", "output", "counter", "ucs2encode", "array", "atobUint8Array", "idx", "result", "createUint8Array", "base64ToUint8Array", "hasOwnProperty$1", "obj", "Object", "prototype", "hasOwnProperty", "call", "sortedKeys", "keys", "a", "parseFloat", "Directory", "tables", "raw", "scalerType", "tableCount", "searchRange", "entrySelector", "rangeShift", "tag", "checksum", "deftable", "methods", "Ctor", "file", "def", "definition", "rawData", "parse", "nextSubsetTag", "subsetTag", "Subfont", "font", "subset", "unicodes", "ogid2ngid", "0", "ngid2ogid", "ncid2ogid", "next", "firstChar", "nextGid", "psName", "TTFFont", "name", "numFonts", "self", "contents", "makeOutput", "out", "num", "arguments$1", "arguments", "undefined", "PDFValue", "beforeRender", "render", "isArray", "renderArray", "isDate", "renderDate", "isNaN", "toFixed", "indexOf", "test", "PDFDictionary", "indentLevel", "writeData", "with<PERSON>ndent", "indent", "NL", "pad", "wrapObject", "id", "renderValue", "renderFull", "_offset", "getPaperOptions", "getOption", "paperSize", "margin", "PAPER_SIZE", "a4", "toLowerCase", "unitsToPoints", "min", "left", "top", "right", "bottom", "PDFDocument", "defval", "catalog", "pageTree", "nameTree", "info", "objcount", "objects", "attach", "pages", "FONTS", "IMAGES", "GRAD_COL_FUNCTIONS", "GRAD_OPC_FUNCTIONS", "GRAD_COL", "GRAD_OPC", "PDFCatalog", "PDFPageTree", "JavaScript", "Names", "PDFString", "S", "_", "JS", "props", "setPages", "Producer", "Title", "Author", "Subject", "Keywords", "Creator", "CreationDate", "Date", "addPage", "content", "page", "paperOptions", "contentWidth", "contentHeight", "PDFStream", "Contents", "Parent", "MediaBox", "PDFPage", "_content", "transform", "translate", "rect", "clip", "xrefOffset", "zeropad", "Size", "Root", "Info", "loadBinary", "url", "cont", "error", "console", "log", "m", "req", "browser", "msie", "exec", "substr", "XMLHttpRequest", "open", "responseType", "onload", "status", "response", "VBArray", "responseBody", "toArray", "onerror", "send", "loadFont", "FONT_CACHE", "clearImageCache", "IMAGE_CACHE", "loadImage", "_load", "img", "src", "complete", "_onload", "_onerror", "_trycanvas", "canvas", "ctx", "imgdata", "has<PERSON><PERSON><PERSON>", "rgb", "alpha", "rawbytes", "getContext", "drawImage", "getImageData", "ex", "bloburl", "URL", "revokeObjectURL", "PDFRawImage", "toDataURL", "PDFJpegImage", "blob", "type", "FileReader", "readAsA<PERSON>y<PERSON><PERSON>er", "xhr", "Image", "crossOrigin", "createObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "loadOne", "urls", "callback", "ch", "date", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "mm2pt", "mm", "cm2pt", "cm", "in2pt", "inch", "in", "defclass", "proto", "Base", "PDFName_cache", "PDFName", "colorSpace", "bitsPerComponent", "soi", "ff", "SOF_CODES", "Type", "Subtype", "<PERSON><PERSON><PERSON>", "Height", "BitsPerComponent", "Filter", "ColorSpace", "Decode", "asStream", "_resourceName", "RESOURCE_COUNTER", "mask", "SMask", "makeHash", "cacheColorGradientFunction", "r1", "g1", "b1", "r2", "g2", "b2", "func", "FunctionType", "Domain", "Range", "N", "C0", "C1", "cacheOpacityGradientFunction", "makeGradientFunctions", "stops", "assemble", "funcs", "Functions", "Bounds", "offsets", "Encode", "prev", "cur", "prevColor", "curColor", "opacities", "colors", "color", "r", "g", "pop", "cacheColorGradient", "isRadial", "coords", "shading", "concat", "for<PERSON>ach", "ShadingType", "<PERSON><PERSON><PERSON>", "Function", "Extend", "cacheOpacityGradient", "opacity", "AIS", "CA", "ca", "G", "FormType", "BBox", "Group", "CS", "I", "Resources", "ExtGState", "a0", "Shading", "s0", "cacheGradient", "gradient", "y", "end", "userSpace", "unquote", "parseFontDef", "fontdef", "fontSize", "rx", "parseInt", "italic", "variant", "bold", "lineHeight", "fontFamily", "split", "getFontURL", "mkFamily", "FONT_MAPPINGS", "fontAlias", "alias", "defineFont", "mmul", "c1", "d1", "e1", "f1", "c2", "d2", "e2", "f2", "isIdentityMatrix", "group", "doIt", "drawPage", "add<PERSON><PERSON>gin", "origin", "optimize", "bbox", "root", "getSize", "<PERSON><PERSON><PERSON><PERSON>", "drawing", "kendoGeometry", "Matrix", "append", "landscape", "drawElement", "count", "producer", "title", "author", "subject", "keywords", "creator", "autoPrint", "multiPage", "children", "fonts", "images", "imgDPI", "traverse", "element", "dispatch", "ceil", "Text", "loadFonts", "loadImages", "toBlob", "Blob", "saveAs$1", "filename", "proxy", "supportBrowser", "safari", "saveAs", "dataURI", "fileName", "dataURL", "proxyURL", "handlers", "handler", "nodeType", "_pdfDebug", "comment", "save", "setOpacity", "setStrokeOptions", "setFillOptions", "matrix", "c", "d", "e", "setClipping", "Path", "drawPath", "MultiPath", "drawMultiPath", "Circle", "drawCircle", "Arc", "drawArc", "drawText", "drawGroup", "Rect", "drawRect", "restore", "dashType", "lineCap", "lineJoin", "stroke", "parseColor$1", "setStrokeColor", "setStrokeOpacity", "setLineWidth", "setDashPattern", "DASH_PATTERNS", "setLineCap", "LINE_CAP", "setLineJoin", "LINE_JOIN", "fill", "Gradient", "setFillColor", "setFillOpacity", "_drawPath", "shouldDraw", "thing", "maybe<PERSON>rad<PERSON>", "tl", "clipStroke", "RadialGrad<PERSON>", "center", "radius", "elements", "stop", "unshift", "rawBBox", "topLeft", "maybeFillStroke", "fillStroke", "nop", "maybeDrawRect", "path", "isRect", "segments", "closed", "controlIn", "anchor", "seg", "prevOut", "controlOut", "bezier", "lineTo", "moveTo", "close", "paths", "geometry", "circle", "points", "curvePoints", "mode", "_position", "TEXT_RENDERING_MODE$1", "fillAndStroke", "beginText", "setFont", "setTextRenderingMode", "showText", "_pdfRect", "endText", "_pdfLink", "addLink", "sz", "parseColor", "toRGB", "change", "newShape", "changed", "visible", "shape", "optArray", "el", "opt", "withClipping", "saveclipbox", "clipbox", "savematrix", "multiplyCopy", "intersect", "inClipbox", "currentBox", "union", "unit", "exportPDF", "promise", "createPromise", "_ignore<PERSON><PERSON><PERSON>", "resolve", "exportPDFToBlob", "support", "kendoPdf", "HeadTable", "LocaTable", "HheaTable", "MaxpTable", "HmtxTable", "GlyfTable", "NameTable", "PostTable", "CmapTable", "OS2Table", "PDFHexString", "PDFStandardFont", "PDFFont", "PDFToUnicodeCmap", "TEXT_RENDERING_MODE", "version", "keyStr", "enc1", "enc2", "enc3", "enc4", "chr1", "chr2", "chr3", "input", "char<PERSON>t", "readTable", "table", "directoryLength", "headOffset", "tableData", "sum", "adjustment", "this$1", "maxpow2", "pow", "floor", "LN2", "revision", "checkSumAdjustment", "magicNumber", "flags", "unitsPerEm", "created", "modified", "xMin", "yMin", "xMax", "yMax", "macStyle", "lowestRecPPEM", "fontDirectionHint", "indexToLocFormat", "glyphDataFormat", "format", "head", "offsetOf", "lengthOf", "needsLongFormat", "ascent", "descent", "lineGap", "advanceWidthMax", "minLeftSideBearing", "minRightSideBearing", "xMaxExtent", "caretSlopeRise", "caretSlopeRun", "caretOffset", "metricDataFormat", "numOfLongHorMetrics", "ids", "numGlyphs", "maxPoints", "maxContours", "maxComponentPoints", "maxComponentContours", "maxZones", "maxTwilightPoints", "maxStorage", "maxFunctionDefs", "maxInstructionDefs", "maxStackElements", "maxSizeOfInstructions", "maxComponentElements", "max<PERSON>ompo<PERSON><PERSON><PERSON><PERSON>", "glyphIds", "dir", "hhea", "lsbCount", "metrics", "advance", "lsb", "maxp", "leftSideBearings", "forGlyph", "SimpleGlyph", "CompoundGlyph", "idOffsets", "MORE_COMPONENTS", "ARG_1_AND_2_ARE_WORDS", "WE_HAVE_A_TWO_BY_TWO", "WE_HAVE_AN_X_AND_Y_SCALE", "WE_HAVE_A_SCALE", "compound", "old2new", "cache", "glyphFor", "loca", "numberOfContours", "glyph", "glyphs", "oldIds", "NameEntry", "platformID", "platformSpecificID", "languageID", "nameID", "stringOffset", "nameRecords", "strings", "rec", "postscriptEntry", "postscriptName", "strTable", "list", "j", "strCount", "POSTSCRIPT_GLYPHS", "numberOfGlyphs", "limit", "italicAngle", "underlinePosition", "underlineThickness", "isFixedPitch", "minMemType42", "maxMemType42", "minMemType1", "maxMemType1", "glyphNameIndex", "names", "code", "index", "mapping", "indexes", "post", "CmapEntry", "codeMap", "segCount", "endCode", "startCode", "id<PERSON><PERSON><PERSON>", "idRangeOffset", "glyphId", "ngroups", "endCharCode", "glyphCode", "language", "renderCharmap", "new_gid", "charcode", "gid", "delta", "segCountX2", "deltas", "rangeOffsets", "startGlyph", "codes", "startCodes", "endCodes", "last", "diff", "averageCharWidth", "weightClass", "widthClass", "ySubscriptXSize", "ySubscriptYSize", "ySubscriptXOffset", "ySubscriptYOffset", "ySuperscriptXSize", "ySuperscriptYSize", "ySuperscriptXOffset", "ySuperscriptYOffset", "yStrikeoutSize", "yStrikeoutPosition", "familyClass", "panose", "char<PERSON><PERSON><PERSON>", "vendorID", "selection", "firstCharIndex", "lastCharIndex", "winAscent", "winDescent", "codePageRange", "xHeight", "capHeight", "defaultChar", "breakChar", "max<PERSON><PERSON><PERSON>t", "use", "old_gid", "reduce", "cmap", "encodeText", "glyphsFor", "glyf", "new_gid_ids", "old_gid_ids", "lastChar", "hmtx", "OS/2", "os2", "directory", "cidToGidMap", "cid", "scale", "widthOfGlyph", "makeSubset", "a5", "a6", "a7", "a8", "a9", "a10", "b0", "b3", "b4", "b5", "b6", "b7", "b8", "b9", "b10", "c0", "c3", "c4", "c5", "c6", "c7", "c8", "c9", "c10", "executive", "folio", "legal", "letter", "tabloid", "Times-Roman", "Times-Bold", "Times-Italic", "Times-BoldItalic", "Helvetica", "Helvetica-Bold", "Helvetica-Oblique", "Helvetica-BoldOblique", "Courier", "Courier-Bold", "Courier-Oblique", "Courier-BoldOblique", "Symbol", "ZapfDingbats", "getFont", "getImage", "getOpacityGS", "forStroke", "gs", "_opacityGSCache", "dict", "txt", "val", "escape", "empty", "compress", "Length", "pagesObj", "Pages", "Kids", "Count", "pageObj", "BaseFont", "Encoding", "_pdf", "_font", "_sub", "stemV", "<PERSON><PERSON><PERSON><PERSON>", "isScript", "getTextWidth", "descendant", "unimap", "unimapStream", "sub", "fontStream", "Length1", "descriptor", "FontName", "FontBBox", "Flags", "StemV", "ItalicAngle", "Ascent", "Descent", "CapHeight", "XHeight", "FontFile2", "char<PERSON><PERSON><PERSON>", "loop", "chunk", "CIDSystemInfo", "Registry", "Ordering", "Supplement", "FontDescriptor", "FirstChar", "LastChar", "DW", "W", "CIDToGIDMap", "_makeCidToGidMap", "DescendantFonts", "ToUnicode", "unicode", "_rcount", "_textMode", "_fontResources", "_gsResources", "_xResources", "_patResources", "_shResources", "_opacity", "_matrix", "_annotations", "_fontSize", "_contextStack", "ProcSet", "Font", "XObject", "Pattern", "Ann<PERSON>", "_out", "dx", "dy", "sx", "sy", "rotate", "angle", "cos", "sin", "_requireTextMode", "_requireFont", "setTextLeading", "<PERSON><PERSON><PERSON><PERSON>", "outputWidth", "showTextNL", "uri", "ll", "_toPage", "ur", "Border", "A", "URI", "sname", "oname", "dashArray", "dashPhase", "setMitterLimit", "mitterLimit", "_context", "x1", "y1", "x2", "y2", "x3", "y3", "bezier1", "bezier2", "h", "ellipse", "ry", "_X", "v", "_Y", "k", "closeStroke", "line", "p", "serif", "serif|bold", "serif|italic", "serif|bold|italic", "sans-serif", "sans-serif|bold", "sans-serif|italic", "sans-serif|bold|italic", "monospace", "monospace|bold", "monospace|italic", "monospace|bold|italic", "zapfdingbats", "zapfdingbats|bold", "zapfdingbats|italic", "zapfdingbats|bold|italic", "invisible", "fillAndClip", "strokeAndClip", "fillStrokeClip", "dash", "dashDot", "dot", "longDash", "longDashDot", "longDashDotDot", "solid", "butt", "square", "miter", "bevel", "Document", "PDFMixin", "events", "saveAsPDF", "_drawPDF", "_drawPDFShadow", "allPages", "progress", "Deferred", "args", "trigger", "then", "done", "forceProxy", "proxyTarget", "fail", "err", "reject", "drawDOM", "wrapper", "pageNumber", "totalPages", "notify", "settings", "drawOptions", "shadow", "defer", "css", "overflow", "before", "clone", "setTimeout", "always", "remove"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,YAAa,cAAeD,IACrC,WAUE,MATC,YACGoC,MAAMgD,IAAMhD,MAAMgD,QAClBhD,MAAMgD,IAAIC,gBAAkB,WACxB,MAAOlD,QAAOmD,MAAsC,kBAAvBnD,QAAOmD,KAAKC,SAE7CnD,MAAMgD,IAAIG,QAAU,SAAUC,GAC1B,MAAOrD,QAAOmD,KAAKC,QAAQC,OAG5BrD,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,YACH,WACA,aACA,cACA,iBACDD,IACL,WA2qHE,MA1qHC,UAAUoC,GAsDP,QAASqD,GAAaD,GAkDlB,QAASE,KACL,MAAOC,IAAUvE,EAErB,QAASwE,KACL,MAAOD,GAASvE,EAASoE,EAAKG,KAAY,EAE9C,QAASE,GAAUC,GACfC,EAAOJ,GACPH,EAAKG,KAAgB,IAAJG,EACbH,EAASvE,IACTA,EAASuE,GAGjB,QAASK,KACL,MAAOJ,MAAc,EAAIA,IAE7B,QAASK,GAAWC,GAChBL,EAAUK,GAAK,GACfL,EAAUK,GAEd,QAASC,KACL,GAAID,GAAIF,GACR,OAAOE,IAAK,MAAQA,EAAI,MAAQA,EAEpC,QAASE,GAAYF,GACjBD,EAAWC,EAAI,EAAIA,EAAI,MAAQA,GAEnC,QAASG,KACL,MAAqB,OAAdL,IAAsBA,IAEjC,QAASM,GAAUJ,GACfD,EAAWC,IAAM,GAAK,OACtBD,EAAe,MAAJC,GAEf,QAASK,KACL,GAAIL,GAAIG,GACR,OAAOH,IAAK,WAAaA,EAAI,WAAaA,EAE9C,QAASM,GAAWN,GAChBI,EAAUJ,EAAI,EAAIA,EAAI,WAAaA,GAEvC,QAASO,KACL,MAAOJ,KAAa,MAExB,QAASK,GAAW1G,GAChBsG,EAAUK,KAAKC,MAAU,MAAJ5G,IAEzB,QAAS6G,KACL,MAAON,KAAc,MAEzB,QAASO,GAAY9G,GACjBwG,EAAWG,KAAKC,MAAU,MAAJ5G,IAE1B,QAAS+G,GAAKC,GACV,MAAOC,GAAMD,EAAKpB,GAEtB,QAASsB,GAAWF,GAChB,MAAO3G,QAAO8G,aAAaC,MAAM/G,OAAQ0G,EAAKC,IAElD,QAASK,GAAYpG,GACjB,IAAK,GAAIC,GAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9B2E,EAAU5E,EAAII,WAAWH,IAGjC,QAAS+F,GAAMK,EAAGC,GACd,IAAK,GAAIC,GAAUC,MAAMH,GAAIpG,EAAI,EAAGA,EAAIoG,IAAKpG,EACzCsG,EAAItG,GAAKqG,GAEb,OAAOC,GAtHf,GAOQzB,GAQAzC,EAKAoE,EAmBAC,EAiFAC,EAvHAjC,EAAS,EAAGvE,EAAS,CAiLzB,OAhLY,OAARoE,EACAA,EAAOqC,GAAmB,GAAIC,YAAW,QAEzC1G,EAASoE,EAAKpE,OAEd2E,EAAS8B,GAAmB,SAAUb,GACtC,GAAIA,GAAOxB,EAAKpE,OAAQ,CACpB,GAAI2G,GAAM,GAAID,YAAWnB,KAAKqB,IAAIhB,EAAM,IAAmB,EAAdxB,EAAKpE,QAClD2G,GAAIE,IAAIzC,EAAM,GACdA,EAAOuC,IAEX,aAEAzE,EAAMuE,GAAmB,WACzB,MAAO,IAAIC,YAAWtC,EAAK0C,OAAQ,EAAG9G,IACtC,WACA,MAAOoE,IAEPkC,EAAQG,GAAmB,SAAUM,GACrC,GAAoB,gBAATA,GACP,MAAOd,GAAYc,EAEvB,IAAInB,GAAMmB,EAAM/G,MAChB2E,GAAOJ,EAASqB,GAChBxB,EAAKyC,IAAIE,EAAOxC,GAChBA,GAAUqB,EACNrB,EAASvE,IACTA,EAASuE,IAEb,SAAUwC,GACV,GAAoB,gBAATA,GACP,MAAOd,GAAYc,EAEvB,KAAK,GAAIjH,GAAI,EAAGA,EAAIiH,EAAM/G,SAAUF,EAChC2E,EAAUsC,EAAMjH,KAGpByG,EAAQE,GAAmB,SAAUO,EAAOhH,GAC5C,GAAIoE,EAAK0C,OAAOP,MACZ,MAAO,IAAIG,YAAWtC,EAAK0C,OAAOP,MAAMS,EAAOA,EAAQhH,GAEvD,IAAIiH,GAAI,GAAIP,YAAW1G,EAEvB,OADAiH,GAAEJ,IAAI,GAAIH,YAAWtC,EAAK0C,OAAQE,EAAOhH,IAClCiH,GAEX,SAAUD,EAAOhH,GACjB,MAAOoE,GAAKmC,MAAMS,EAAOA,EAAQhH,IAwEjCwG,GACAlC,IAAKA,EACLE,SAAUA,EACVC,UAAWA,EACXG,UAAWA,EACXC,WAAYA,EACZI,SAAUA,EACVC,UAAWA,EACXG,UAAWA,EACXC,WAAYA,EACZP,WAAYA,EACZC,YAAaA,EACbG,UAAWA,EACXC,WAAYA,EACZK,WAAYA,EACZC,YAAaA,EACbC,KAAMA,EACNW,MAAOA,EACPR,WAAYA,EACZG,YAAaA,EACbJ,MAAOA,EACP3D,IAAKA,EACLqE,MAAOA,EACPhC,OAAQ,SAAU2C,GACd,MAAW,OAAPA,GACA3C,EAAS2C,EACFV,GAEJjC,GAEX4C,KAAM,SAAUC,GACZ7C,GAAU6C,GAEdC,SAAU,WACN,KAAUC,OAAM,kEAEpBtH,OAAQ,WACJ,MAAOA,IAEXuH,cAAe,SAAU3I,GACrB,GAAIsI,GAAM3C,CACV,KACI,MAAO3F,KACT,QACE2F,EAAS2C,IAGjBM,YAAa,SAAUC,GACf1G,OAAO2G,KACPzB,EAAYlF,OAAO2G,KAAKD,IAExBnB,EAAMqB,GAAOC,OAAOH,KAG5BA,OAAQ,WACJ,MAAOE,IAAOE,OAAO3F,OAKjC,QAAS4F,GAAWC,GAEhB,IADA,GAAsDpG,GAAOqG,EAAzDC,KAAaC,EAAU,EAAGlI,EAAS+H,EAAO/H,OACvCkI,EAAUlI,GACb2B,EAAQoG,EAAO9H,WAAWiI,KACtBvG,GAAS,OAASA,GAAS,OAASuG,EAAUlI,GAC9CgI,EAAQD,EAAO9H,WAAWiI,KACH,QAAV,MAARF,GACDC,EAAOxI,OAAe,KAARkC,IAAiB,KAAe,KAARqG,GAAgB,QAEtDC,EAAOxI,KAAKkC,GACZuG,MAGJD,EAAOxI,KAAKkC,EAGpB,OAAOsG,GAEX,QAASE,GAAWC,GAChB,MAAOA,GAAMxG,IAAI,SAAUD,GACvB,GAAIsG,GAAS,EAOb,OANItG,GAAQ,QACRA,GAAS,MACTsG,GAAUhJ,OAAO8G,aAAapE,IAAU,GAAK,KAAO,OACpDA,EAAQ,MAAgB,KAARA,GAEpBsG,GAAUhJ,OAAO8G,aAAapE,KAE/BhC,KAAK,IAEZ,QAAS0I,GAAeZ,GAAxB,GAGaa,GAFLlE,EAAOrD,OAAO2G,KAAKD,GACnBc,EAAS,GAAI7B,YAAWtC,EAAKpE,OACjC,KAASsI,EAAM,EAAGA,EAAMlE,EAAKpE,OAAQsI,IACjCC,EAAOD,GAAOlE,EAAKnE,WAAWqI,EAElC,OAAOC,GAEX,QAASC,GAAiBpE,GAA1B,GAEakE,GADLC,EAAS,GAAI7B,YAAWtC,EAAKpE,OACjC,KAASsI,EAAM,EAAGA,EAAMlE,EAAKpE,OAAQsI,IACjCC,EAAOD,GAAOlE,EAAKkE,EAEvB,OAAOC,GAEX,QAASE,GAAmBhB,GACxB,MAAI1G,QAAO2G,KACAW,EAAeZ,GAEnBe,EAAiBb,GAAOC,OAAOH,IAE1C,QAASiB,GAAiBC,EAAKpJ,GAC3B,MAAOqJ,QAAOC,UAAUC,eAAeC,KAAKJ,EAAKpJ,GAErD,QAASyJ,GAAWL,GAChB,MAAOC,QAAOK,KAAKN,GAAKjJ,KAAK,SAAUwJ,EAAGxE,GACtC,MAAOwE,GAAIxE,IACZ9C,IAAIuH,YAEX,QAASC,GAAUhF,GAAnB,GAOQiF,GACKvJ,EACD+B,CADR,KAPAP,KAAKgI,IAAMlF,EACX9C,KAAKiI,WAAanF,EAAKa,WACvB3D,KAAKkI,WAAapF,EAAKQ,YACvBtD,KAAKmI,YAAcrF,EAAKQ,YACxBtD,KAAKoI,cAAgBtF,EAAKQ,YAC1BtD,KAAKqI,WAAavF,EAAKQ,YACnByE,EAAS/H,KAAK+H,UACTvJ,EAAI,EAAGA,EAAIwB,KAAKkI,aAAc1J,EAC/B+B,GACA+H,IAAKxF,EAAK0B,WAAW,GACrB+D,SAAUzF,EAAKa,WACfV,OAAQH,EAAKa,WACbjF,OAAQoE,EAAKa,YAEjBoE,EAAOxH,EAAM+H,KAAO/H,EA8D5B,QAASiI,GAASC,GACd,QAASC,GAAKC,EAAMC,GAChB5I,KAAK6I,WAAaD,EAClB5I,KAAKtB,OAASkK,EAAIlK,OAClBsB,KAAKiD,OAAS2F,EAAI3F,OAClBjD,KAAK2I,KAAOA,EACZ3I,KAAK8I,QAAUH,EAAKX,IACpBhI,KAAK+I,MAAMJ,EAAKX,KAEpBU,EAAKnB,UAAUS,IAAM,WACjB,MAAOhI,MAAK8I,QAAQ7D,MAAMjF,KAAKiD,OAAQjD,KAAKtB,QAEhD,KAAK,GAAIF,KAAKiK,GACNrB,EAAiBqB,EAASjK,KAC1BkK,EAAKlK,GAAKkK,EAAKnB,UAAU/I,GAAKiK,EAAQjK,GAG9C,OAAOkK,GAkqBX,QAASM,KAAT,GAEaxK,GADLsG,EAAM,GAAIF,EAAWqE,GAAPtL,EAClB,KAASa,EAAI,EAAGA,EAAIoG,EAAElG,SAAUF,EAC5BsG,GAAOnH,OAAO8G,aAAaG,EAAEjG,WAAWH,GAAK,GAAK,GAGtD,SADEyK,GACKnE,EAEX,QAASoE,GAAQC,GACbnJ,KAAKmJ,KAAOA,EACZnJ,KAAKoJ,UACLpJ,KAAKqJ,YACLrJ,KAAKsJ,WAAcC,EAAG,GACtBvJ,KAAKwJ,WAAcD,EAAG,GACtBvJ,KAAKyJ,aACLzJ,KAAK0J,KAAO1J,KAAK2J,UAAY,EAC7B3J,KAAK4J,QAAU,EACf5J,KAAK6J,OAASb,IAAkB,IAAMhJ,KAAKmJ,KAAKU,OAwGpD,QAASC,GAAQhB,EAASiB,GAA1B,GAIY9G,GACA8F,EAQAiB,EACKxL,EAbTyL,EAAOjK,KACP8C,EAAOmH,EAAKC,SAAWnH,EAAa+F,EACxC,IAA0B,QAAtBhG,EAAK0B,WAAW,GAAc,CAM9B,GAJIuE,EAAQ,WACRjG,EAAKG,OAAOA,GACZgH,EAAKlB,UAEJgB,EACD,KAAU/D,OAAM,oCAIpB,KAFAlD,EAAKa,WACDqG,EAAWlH,EAAKa,WACXnF,EAAI,EAAGA,EAAIwL,IAAYxL,EAG5B,GAFAyE,EAASH,EAAKa,WACdb,EAAKmD,cAAc8C,GACfkB,EAAKJ,QAAUE,EACf,MAGR,MAAU/D,OAAM,QAAU+D,EAAO,4BAEjCjH,EAAKG,OAAO,GACZgH,EAAKlB,QA0Lb,QAASoB,KAEL,QAASC,KAAT,GAEa5L,GACDmH,EAcI0E,EAhBRC,EAAcC,SAClB,KAAS/L,EAAI,EAAGA,EAAI+L,UAAU7L,SAAUF,EAAG,CAEvC,GADImH,EAAI2E,EAAY9L,GACVgM,SAAN7E,EACA,KAAUK,OAAM,iCACb,IAAIL,YAAa8E,GACpB9E,EAAE+E,aAAaN,GACfzE,EAAEgF,OAAOP,OACN,IAAIQ,GAAQjF,GACfkF,EAAYlF,EAAGyE,OACZ,IAAIU,EAAOnF,GACdoF,EAAWpF,EAAGyE,OACX,IAAgB,gBAALzE,GAAe,CAC7B,GAAIqF,MAAMrF,GACN,KAAUK,OAAM,2BAEhBqE,GAAM1E,EAAEsF,QAAQ,GAChBZ,EAAIa,QAAQ,MAAQ,IACpBb,EAAMA,EAAIzM,QAAQ,SAAU,KAErB,MAAPyM,IACAA,EAAM,KAEV1D,EAAOhC,YAAY0F,OACZ,iBAAiBc,WAAYxF,IACpCgB,EAAOhC,YAAmBgB,EAAPhI,IACI,kBAATgI,GAAE/E,IAChB+F,EAAO3B,MAAMW,EAAE/E,OACI,gBAAL+E,KACTA,EAGDyE,EAAI,GAAIgB,IAAczF,IAFtBgB,EAAOhC,YAAY,UAhCnC,GAAI0G,GAAc,EAAG1E,EAAS5D,GA+D9B,OAxBAqH,GAAIkB,UAAY,SAAUxI,GACtB6D,EAAO3B,MAAMlC,IAEjBsH,EAAImB,WAAa,SAAUjO,KACrB+N,EACF/N,EAAE8M,KACAiB,GAENjB,EAAIoB,OAAS,WACTpB,EAAIqB,GAAIC,EAAI,GAAkB,EAAdL,EAAiB,OACjCjB,EAAI1F,MAAM,KAAM6F,YAEpBH,EAAInH,OAAS,WACT,MAAO0D,GAAO1D,UAElBmH,EAAIrE,SAAW,WACX,KAAUC,OAAM,eAEpBoE,EAAIxJ,IAAM,WACN,MAAO+F,GAAO/F,OAElBwJ,EAAIlF,OAAS,WACT,MAAOyB,IAEJyD,EAEX,QAASuB,GAAWtL,EAAOuL,GAA3B,GACQlB,GAAerK,EAAMqK,aACrBmB,EAAcxL,EAAMsK,MACxBtK,GAAMqK,aAAe,aAErBrK,EAAMsK,OAAS,SAAUP,GACrBA,EAAIwB,EAAI,SAEZvL,EAAMyL,WAAa,SAAU1B,GACzB/J,EAAM0L,QAAU3B,EAAInH,SACpBmH,EAAIwB,EAAI,WACRlB,EAAajD,KAAKpH,EAAO+J,GACzByB,EAAYpE,KAAKpH,EAAO+J,GACxBA,EAAI,YAGZ,QAAS4B,GAAgBC,GAAzB,GAEYhL,GAKJiL,EAkBAC,CAjBJ,IAPwB,kBAAbF,KACHhL,EAAUgL,EACdA,EAAY,SAAUhO,EAAK2K,GACvB,MAAO3K,KAAOgD,GAAUA,EAAQhD,GAAO2K,IAG3CsD,EAAYD,EAAU,YAAaG,GAAWC,KAC7CH,EACD,QAEJ,IAAwB,gBAAbA,KACPA,EAAYE,GAAWF,EAAUI,eAChB,MAAbJ,GACA,KAAUlG,OAAM,qBAkCxB,OA/BAkG,GAAU,GAAKK,EAAcL,EAAU,IACvCA,EAAU,GAAKK,EAAcL,EAAU,IACnCD,EAAU,aAAa,KACvBC,GACIjI,KAAKqB,IAAI4G,EAAU,GAAIA,EAAU,IACjCjI,KAAKuI,IAAIN,EAAU,GAAIA,EAAU,MAGrCC,EAASF,EAAU,UACnBE,IACqB,gBAAVA,IAAuC,gBAAVA,IACpCA,EAASI,EAAcJ,EAAQ,GAC/BA,GACIM,KAAMN,EACNO,IAAKP,EACLQ,MAAOR,EACPS,OAAQT,IAGZA,GACIM,KAAMF,EAAcJ,EAAOM,KAAM,GACjCC,IAAKH,EAAcJ,EAAOO,IAAK,GAC/BC,MAAOJ,EAAcJ,EAAOQ,MAAO,GACnCC,OAAQL,EAAcJ,EAAOS,OAAQ,IAGzCX,EAAU,eACVC,EAAU,IAAMC,EAAOM,KAAON,EAAOQ,MACrCT,EAAU,IAAMC,EAAOO,IAAMP,EAAOS,UAIxCV,UAAWA,EACXC,OAAQA,GAGhB,QAASU,GAAY5L,GAKjB,QAASgL,GAAUlC,EAAM+C,GACrB,MAAO7L,IAA4B,MAAjBA,EAAQ8I,GAAgB9I,EAAQ8I,GAAQ+C,EANlE,GAuBQC,GACAC,EAEIC,EAaJC,EAtCAjD,EAAOjK,KACPoK,EAAMD,IACNgD,EAAW,EACXC,IAIJnD,GAAKgC,UAAYA,EACjBhC,EAAKoD,OAAS,SAAUhN,GAKpB,MAJI+M,GAAQlC,QAAQ7K,GAAS,IACzBsL,EAAWtL,IAAS8M,GACpBC,EAAQjP,KAAKkC,IAEVA,GAEX4J,EAAKqD,SACLrD,EAAKsD,SACLtD,EAAKuD,UACLvD,EAAKwD,sBACLxD,EAAKyD,sBACLzD,EAAK0D,YACL1D,EAAK2D,YACDb,EAAU9C,EAAKoD,OAAO,GAAIQ,KAC1Bb,EAAW/C,EAAKoD,OAAO,GAAIS,KAC3B7B,EAAU,eACNgB,KACJA,EAASc,WAAa,GAAI3C,KACtB4C,OACI,GAAIC,IAAU,MACdhE,EAAKoD,OAAO,GAAIjC,KACZ8C,EAAGC,EAAE,cACLC,GAAI,GAAIH,IAAU,sBAI9BlB,EAAQsB,MAAML,MAAQ,GAAI5C,IAAc6B,IAE5CF,EAAQuB,SAAStB,GACbE,EAAOjD,EAAKoD,OAAO,GAAIjC,KACvBmD,SAAU,GAAIN,IAAUhC,EAAU,WAAY,2BAC9CuC,MAAO,GAAIP,IAAUhC,EAAU,QAAS,KACxCwC,OAAQ,GAAIR,IAAUhC,EAAU,SAAU,KAC1CyC,QAAS,GAAIT,IAAUhC,EAAU,UAAW,KAC5C0C,SAAU,GAAIV,IAAUhC,EAAU,WAAY,KAC9C2C,QAAS,GAAIX,IAAUhC,EAAU,UAAW,2BAC5C4C,aAAc5C,EAAU,OAAQ,GAAI6C,UAExC7E,EAAK8E,QAAU,SAAU9N,GAAV,GAYP+N,GACAX,EAUAY,EAtBAC,EAAelD,EAAgB,SAAUjC,EAAM+C,GAC/C,MAAO7L,IAA4B,MAAjBA,EAAQ8I,GAAgB9I,EAAQ8I,GAAQ+C,IAE1DZ,EAAYgD,EAAahD,UACzBC,EAAS+C,EAAa/C,OACtBgD,EAAejD,EAAU,GACzBkD,EAAgBlD,EAAU,EA0B9B,OAzBIC,KACAgD,GAAgBhD,EAAOM,KAAON,EAAOQ,MACrCyC,GAAiBjD,EAAOO,IAAMP,EAAOS,QAErCoC,EAAU,GAAIK,IAAUlF,IAAc,OAAM,IAC5CkE,GACAiB,SAAUrF,EAAKoD,OAAO2B,GACtBO,OAAQvC,EACRwC,UACI,EACA,EACAtD,EAAU,GACVA,EAAU,KAGd+C,EAAO,GAAIQ,IAAQxF,EAAMoE,GAC7BY,EAAKS,SAAWV,EAChBhC,EAAS+B,QAAQ9E,EAAKoD,OAAO4B,IAC7BA,EAAKU,UAAU,EAAG,EAAG,KAAO,EAAGzD,EAAU,IACrCC,IACA8C,EAAKW,UAAUzD,EAAOM,KAAMN,EAAOO,KACnCuC,EAAKY,KAAK,EAAG,EAAGV,EAAcC,GAC9BH,EAAKa,QAET7F,EAAKqD,MAAMnP,KAAK8Q,GACTA,GAEXhF,EAAKU,OAAS,WAAA,GACNnM,GAMAuR,CAJJ,KADA3F,EAAI,WAAYqB,GAAI,SAAUA,GAAIA,IAC7BjN,EAAI,EAAGA,EAAI4O,EAAQ1O,SAAUF,EAC9B4O,EAAQ5O,GAAGsN,WAAW1B,GACtBA,EAAIqB,GAAIA,GAKZ,KAHIsE,EAAa3F,EAAInH,SACrBmH,EAAI,OAAQqB,GAAI,EAAG,IAAK2B,EAAQ1O,OAAS,EAAG+M,IAC5CrB,EAAI,sBAAuBqB,IACtBjN,EAAI,EAAGA,EAAI4O,EAAQ1O,SAAUF,EAC9B4L,EAAI4F,EAAQ5C,EAAQ5O,GAAGuN,QAAS,IAAK,YAAaN,GAWtD,OATArB,GAAIqB,IACJrB,EAAI,UAAWqB,IACfrB,EAAI,GAAIgB,KACJ6E,KAAM7C,EAAQ1O,OAAS,EACvBwR,KAAMnD,EACNoD,KAAMjD,IACNzB,GAAIA,IACRrB,EAAI,YAAaqB,GAAIsE,EAAYtE,IACjCrB,EAAI,QAASqB,IACNrB,EAAIlF,SAASjC,OAAO,IAmBnC,QAASmN,GAAWC,EAAKC,GAMrB,QAASC,KACD9Q,OAAO+Q,UACH/Q,OAAO+Q,QAAQD,MACf9Q,OAAO+Q,QAAQD,MAAM,sBAAuBF,GAE5C5Q,OAAO+Q,QAAQC,IAAI,sBAAuBJ,IAGlDC,EAAK,MAdb,GACQI,GAeAC,CAdJ,OAAIC,IAAQC,OAASH,EAAI,qBAAqBI,KAAKT,QAC/CC,GAAKnJ,EAAmBkJ,EAAIU,OAAOL,EAAE,GAAGhS,WAaxCiS,EAAM,GAAIK,gBACdL,EAAIM,KAAK,MAAOZ,GAAK,GACjBlL,KACAwL,EAAIO,aAAe,eAEvBP,EAAIQ,OAAS,WACS,KAAdR,EAAIS,QAA+B,KAAdT,EAAIS,OAErBd,EADAnL,GACK,GAAIC,YAAWuL,EAAIU,UAEnB,GAAI5R,QAAO6R,QAAQX,EAAIY,cAAcC,WAG9CjB,KAGRI,EAAIc,QAAUlB,MACdI,GAAIe,KAAK,OAEb,QAASC,GAAStB,EAAKC,GACnB,GAAInH,GAAOyI,GAAWvB,EAClBlH,GACAmH,EAAKnH,GAELiH,EAAWC,EAAK,SAAUvN,GACtB,GAAY,MAARA,EACA,KAAUkD,OAAM,yBAA2BqK,EAE3C,IAAIlH,GAAO,GAAIW,GAAQhH,EACvB8O,IAAWvB,GAAOlH,EAClBmH,EAAKnH,KAMrB,QAAS0I,KACLC,MAEJ,QAASC,GAAU1B,EAAKtQ,EAAMuQ,GAwB1B,QAAS0B,GAAM3B,GACX4B,EAAIC,IAAM7B,EACN4B,EAAIE,WAAavB,GAAQC,KACzBuB,KAEAH,EAAId,OAASiB,EACbH,EAAIR,QAAUY,GAGtB,QAASC,KAAT,GAOQC,GAGAC,EAEAC,EAWAC,EAAkBC,EAAsBC,EACxCC,EACArU,EAKIoJ,EASA9E,EAEAoC,CAxCHnF,KACDA,GACIlB,MAAOoT,EAAIpT,MACXC,OAAQmT,EAAInT,SAGhByT,EAASzR,SAASC,cAAc,UACpCwR,EAAO1T,MAAQkB,EAAKlB,MACpB0T,EAAOzT,OAASiB,EAAKjB,OACjB0T,EAAMD,EAAOO,WAAW,MAC5BN,EAAIO,UAAUd,EAAK,EAAG,EAAGlS,EAAKlB,MAAOkB,EAAKjB,OAE1C,KACI2T,EAAUD,EAAIQ,aAAa,EAAG,EAAGjT,EAAKlB,MAAOkB,EAAKjB,QACpD,MAAOmU,GAEL,WADAZ,KAEF,QACMa,GACAC,IAAIC,gBAAgBF,GAM5B,IAHIR,GAAW,EAAOC,EAAM5P,IAAgB6P,EAAQ7P,IAChD8P,EAAWJ,EAAQ3P,KACnBtE,EAAI,EACDA,EAAIqU,EAASnU,QAChBiU,EAAIxP,UAAU0P,EAASrU,MACvBmU,EAAIxP,UAAU0P,EAASrU,MACvBmU,EAAIxP,UAAU0P,EAASrU,MACnBoJ,EAAIiL,EAASrU,KACboJ,EAAI,MACJ8K,GAAW,GAEfE,EAAMzP,UAAUyE,EAEhB8K,GACAT,EAAM,GAAIoB,GAAYtT,EAAKlB,MAAOkB,EAAKjB,OAAQ6T,EAAKC,IAEhD9P,EAAOyP,EAAOe,UAAU,cAC5BxQ,EAAOA,EAAKiO,OAAOjO,EAAKoI,QAAQ,YAAc,GAC1ChG,EAASnC,IACbmC,EAAOgB,YAAYpD,GACnBmP,EAAM,GAAIsB,GAAarO,IAE3BoL,EAAKwB,GAAYzB,GAAO4B,GAE5B,QAASI,KACL/B,EAAKwB,GAAYzB,GAAO,SAE5B,QAAS+B,KAML,GALIrS,IACIA,EAAKlB,OAASoT,EAAIpT,OAASkB,EAAKjB,QAAUmT,EAAInT,UAC9CiB,EAAO,OAGVA,GAAQyT,GAAQ,kBAAkBrI,KAAKqI,EAAKC,MAAO,CACpD,GAAI5O,GAAS,GAAI6O,WACjB7O,GAAOsM,OAAS,WACZ,IACI,GAAIc,GAAM,GAAIsB,GAAaxQ,EAAa,GAAIqC,YAAWpF,KAAKiH,SAC5DkM,KAAIC,gBAAgBF,GACpB5C,EAAKwB,GAAYzB,GAAO4B,GAC1B,MAAOgB,GACLX,MAGRzN,EAAO8O,kBAAkBH,OAEzBlB,KAtGZ,GACgCY,GAASM,EASzBI,EATR3B,EAAMH,GAAYzB,EAClB4B,GACA3B,EAAK2B,IAELA,EAAM,GAAI4B,OACL,UAAU1I,KAAKkF,KAChB4B,EAAI6B,YAAc,aAElB3O,KAAqB,UAAUgG,KAAKkF,IAChCuD,EAAM,GAAI5C,gBACd4C,EAAIzC,OAAS,WACTqC,EAAOI,EAAIvC,SACX6B,EAAUC,IAAIY,gBAAgBP,GAC9BxB,EAAMkB,IAEVU,EAAInC,QAAUY,EACduB,EAAI3C,KAAK,MAAOZ,GAAK,GACrBuD,EAAI1C,aAAe,OACnB0C,EAAIlC,QAEJM,EAAM3B,IAqFlB,QAAS2D,GAAWC,GAChB,MAAO,UAAUC,EAAMC,GAKnB,QAASzK,KACO,MAAN9E,GACFuP,IANR,GAAIvP,GAAIsP,EAAKxV,OAAQF,EAAIoG,CACzB,IAAU,IAANA,EACA,MAAOuP,IAOX,MAAO3V,KAAM,GACTyV,EAAQC,EAAK1V,GAAIkL,IAiF7B,QAASgC,GAAInN,EAAK+F,EAAK8P,GACnB,KAAO7V,EAAIG,OAAS4F,GAChB/F,EAAM6V,EAAK7V,CAEf,OAAOA,GAEX,QAASyR,GAAQpL,EAAGN,GAChB,MAAOoH,GAAW9G,EAAPjH,GAAW2G,EAAK,KAE/B,QAASkD,GAAeH,EAAKpJ,GACzB,MAAOqJ,QAAOC,UAAUC,eAAeC,KAAKJ,EAAKpJ,GAKrD,QAAS6M,GAAOzD,GACZ,MAAOA,aAAeyH,MAE1B,QAASjE,GAAYjD,EAAGwC,GACpBA,EAAI,KACAxC,EAAElJ,OAAS,GACX0L,EAAImB,WAAW,WACX,IAAK,GAAI/M,GAAI,EAAGA,EAAIoJ,EAAElJ,SAAUF,EACxBA,EAAI,GAAKA,EAAI,IAAM,EACnB4L,EAAIoB,OAAO5D,EAAEpJ,IAEb4L,EAAI,IAAKxC,EAAEpJ,MAK3B4L,EAAI,MAER,QAASW,GAAWsJ,EAAMjK,GACtBA,EAAI,MAAO4F,EAAQqE,EAAKC,iBAAkB,GAAItE,EAAQqE,EAAKE,cAAgB,EAAG,GAAIvE,EAAQqE,EAAKG,aAAc,GAAIxE,EAAQqE,EAAKI,cAAe,GAAIzE,EAAQqE,EAAKK,gBAAiB,GAAI1E,EAAQqE,EAAKM,gBAAiB,GAAI,MAEzN,QAASC,GAAMC,GACX,MAAOA,IAAM,GAAK,MAEtB,QAASC,GAAMC,GACX,MAAOH,GAAW,GAALG,GAEjB,QAASC,GAAMC,GACX,MAAc,IAAPA,EAEX,QAAS1I,GAAc5G,EAAGiD,GAA1B,GAKY8H,GAGIrG,CAPZ,IAAgB,gBAAL1E,GACP,MAAOA,EAEX,IAAgB,gBAALA,KAEP+K,EAAI,oCAAoCI,KAAKnL,GACzC+K,IACIrG,EAAMxC,WAAW6I,EAAE,KAClB1F,MAAMX,KACP,MAAY,MAARqG,EAAE,GACKrG,GAGPwK,GAAMD,EACNG,GAAMD,EACNI,KAAMF,GACRtE,EAAE,IAAIrG,EAIpB,IAAW,MAAPzB,EACA,MAAOA,EAEX,MAAU5C,OAAM,qBAAwBL,GAE5C,QAAS8E,MAIT,QAAS0K,GAASzM,EAAM0M,EAAOC,GACtBA,IACDA,EAAO5K,GAEX/B,EAAKnB,UAAY,GAAI8N,EACrB,KAAK,GAAI7W,KAAK4W,GACN5N,EAAe4N,EAAO5W,KACtBkK,EAAKnB,UAAU/I,GAAK4W,EAAM5W,GAGlC,OAAOkK,GA6CX,QAASyF,GAAEpE,GACP,MAAIvC,GAAe8N,GAAevL,GACvBuL,GAAcvL,GAElBuL,GAAcvL,GAAQ,GAAIwL,IAAQxL,GAmF7C,QAASwJ,GAAazQ,GAAtB,GAEQjE,GAAOC,EAAQ0W,EAAYC,EAC3BC,EAKIC,EAIAxT,EACAzD,EAaJ2P,CAtBJ,IAHAvL,EAAKG,OAAO,GAERyS,EAAM5S,EAAKQ,YACJ,OAAPoS,EACA,KAAU1P,OAAM,qBAEpB,OAAQlD,EAAKE,OAAO,CAEhB,GADI2S,EAAK7S,EAAKI,WACJ,KAANyS,EACA,KAAU3P,OAAM,qBAIpB,IAFI7D,EAASW,EAAKI,WACdxE,EAASoE,EAAKQ,YACdsS,GAAU1K,QAAQ/I,IAAW,EAAG,CAChCsT,EAAmB3S,EAAKI,WACxBpE,EAASgE,EAAKQ,YACdzE,EAAQiE,EAAKQ,YACbkS,EAAa1S,EAAKI,UAClB,OAEJJ,EAAK+C,KAAKnH,EAAS,GAEvB,GAAkB,MAAd8W,EACA,KAAUxP,OAAM,qBAUpB,QARIqI,GACAwH,KAAM1H,EAAE,WACR2H,QAAS3H,EAAE,SACX4H,MAAOlX,EACPmX,OAAQlX,EACRmX,iBAAkBR,EAClBS,OAAQ/H,EAAE,cAENqH,GACR,IAAK,GACDnH,EAAM8H,WAAahI,EAAE,aACrB,MACJ,KAAK,GACDE,EAAM8H,WAAahI,EAAE,YACrB,MACJ,KAAK,GACDE,EAAM8H,WAAahI,EAAE,cACrBE,EAAM+H,QACF,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GAIRpW,KAAKqW,SAAW,WACZvT,EAAKG,OAAO,EACZ,IAAIiC,GAAS,GAAImK,IAAUvM,EAAMuL,EAEjC,OADAnJ,GAAOoR,cAAgBnI,EAAE,OAAQoI,IAC1BrR,GAGf,QAASmO,GAAYxU,EAAOC,EAAQ6T,EAAKC,GACrC5S,KAAKqW,SAAW,SAAU3T,GAAV,GACR8T,GAAO,GAAInH,IAAUuD,GACrBiD,KAAM1H,EAAE,WACR2H,QAAS3H,EAAE,SACX4H,MAAOlX,EACPmX,OAAQlX,EACRmX,iBAAkB,EAClBE,WAAYhI,EAAE,iBACf,IACCjJ,EAAS,GAAImK,IAAUsD,GACvBkD,KAAM1H,EAAE,WACR2H,QAAS3H,EAAE,SACX4H,MAAOlX,EACPmX,OAAQlX,EACRmX,iBAAkB,EAClBE,WAAYhI,EAAE,aACdsI,MAAO/T,EAAI2K,OAAOmJ,MACnB,GAEH,OADAtR,GAAOoR,cAAgBnI,EAAE,OAAQoI,IAC1BrR,GAgKf,QAASwR,GAAS9O,GACd,MAAOA,GAAEtH,IAAI,SAAUqF,GACnB,MAAOiF,IAAQjF,GAAK+Q,EAAS/Q,GAAiB,gBAALA,IAAiB1B,KAAKC,MAAU,IAAJyB,GAAY,KAAMsF,QAAQ,GAAKtF,IACrGtH,KAAK,KAEZ,QAASsY,GAA2BjU,EAAKkU,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAA7D,GACQxY,GAAOiY,GACPE,EACAC,EACAC,EACAC,EACAC,EACAC,IAEAC,EAAOxU,EAAI+K,mBAAmBhP,EA6BlC,OA5BKyY,KACDA,EAAOxU,EAAI+K,mBAAmBhP,GAAQiE,EAAI2K,OAAO,GAAIjC,KACjD+L,aAAc,EACdC,QACI,EACA,GAEJC,OACI,EACA,EACA,EACA,EACA,EACA,GAEJC,EAAG,EACHC,IACIX,EACAC,EACAC,GAEJU,IACIT,EACAC,EACAC,OAILC,EAEX,QAASO,GAA6B/U,EAAKH,EAAIC,GAA/C,GACQ/D,GAAOiY,GACPnU,EACAC,IAEA0U,EAAOxU,EAAIgL,mBAAmBjP,EAiBlC,OAhBKyY,KACDA,EAAOxU,EAAIgL,mBAAmBjP,GAAQiE,EAAI2K,OAAO,GAAIjC,KACjD+L,aAAc,EACdC,QACI,EACA,GAEJC,OACI,EACA,GAEJC,EAAG,EACHC,IAAKhV,GACLiV,IAAKhV,OAGN0U,EAEX,QAASQ,GAAsBhV,EAAKiV,GAkChC,QAASC,GAASC,GACd,MAAoB,IAAhBA,EAAMnZ,OACCmZ,EAAM,IAGbV,aAAc,EACdW,UAAWD,EACXT,QACI,EACA,GAEJW,OAAQC,EACRC,OAAQ1R,GA9CpB,GAMQ/H,GAAG0Z,EAAMC,EAAKC,EAAWC,EALzB3F,GAAW,EACX4F,KACAC,KACAP,KACAzR,IAEJ,KAAK/H,EAAI,EAAGA,EAAImZ,EAAMjZ,SAAUF,EAC5B0Z,EAAOP,EAAMnZ,EAAI,GACjB2Z,EAAMR,EAAMnZ,GACZ4Z,EAAYF,EAAKM,MACjBH,EAAWF,EAAIK,MACfD,EAAOpa,KAAKwY,EAA2BjU,EAAK0V,EAAUK,EAAGL,EAAUM,EAAGN,EAAUhV,EAAGiV,EAASI,EAAGJ,EAASK,EAAGL,EAASjV,KAChHgV,EAAUxQ,EAAI,GAAKyQ,EAASzQ,EAAI,KAChC8K,GAAW,GAEfsF,EAAQ7Z,KAAKga,EAAIlV,QACjBsD,EAAOpI,KAAK,EAAG,EAEnB,IAAIuU,EACA,IAAKlU,EAAI,EAAGA,EAAImZ,EAAMjZ,SAAUF,EAC5B0Z,EAAOP,EAAMnZ,EAAI,GACjB2Z,EAAMR,EAAMnZ,GACZ4Z,EAAYF,EAAKM,MACjBH,EAAWF,EAAIK,MACfF,EAAUna,KAAKsZ,EAA6B/U,EAAK0V,EAAUxQ,EAAGyQ,EAASzQ,GAI/E,OADAoQ,GAAQW,OAEJjG,SAAUA,EACV6F,OAAQX,EAASW,GACjBD,UAAW5F,EAAWkF,EAASU,GAAa,MAkBpD,QAASM,GAAmBlW,EAAKmW,EAAUlB,EAAOmB,EAAQjB,EAAOrW,GAAjE,GACQuX,GAASta,EAELmJ,CA6BR,OA9BKpG,KACGoG,GAAKiR,GAAUG,OAAOF,GAC1BnB,EAAMsB,QAAQ,SAAUtT,GACpBiC,EAAEzJ,KAAKwH,EAAE1C,OAAQ0C,EAAE6S,MAAMC,EAAG9S,EAAE6S,MAAME,EAAG/S,EAAE6S,MAAMpV,KAEnD3E,EAAOiY,EAAS9O,GAChBmR,EAAUrW,EAAIiL,SAASlP,IAEtBsa,IACDA,EAAU,GAAI3N,KACVyK,KAAM1H,EAAE,WACR+K,YAAaL,EAAW,EAAI,EAC5B1C,WAAYhI,EAAE,aACdgL,OAAQL,EACR1B,QACI,EACA,GAEJgC,SAAUvB,EACVwB,SACI,GACA,KAGR3W,EAAI2K,OAAO0L,GACXA,EAAQzC,cAAgB,OAAQC,GAC5B9X,IACAiE,EAAIiL,SAASlP,GAAQsa,IAGtBA,EAEX,QAASO,GAAqB5W,EAAKmW,EAAUlB,EAAOmB,EAAQjB,EAAOrW,GAAnE,GACQ+X,GAAS9a,EAELmJ,CAsER,OAvEKpG,KACGoG,GAAKiR,GAAUG,OAAOF,GAC1BnB,EAAMsB,QAAQ,SAAUtT,GACpBiC,EAAEzJ,KAAKwH,EAAE1C,OAAQ0C,EAAE6S,MAAM5Q,KAE7BnJ,EAAOiY,EAAS9O,GAChB2R,EAAU7W,EAAIkL,SAASnP,IAEtB8a,IACDA,EAAU,GAAInO,KACVyK,KAAM1H,EAAE,aACRqL,KAAK,EACLC,GAAI,EACJC,GAAI,EACJjD,OACIZ,KAAM1H,EAAE,QACRD,EAAGC,EAAE,cACLwL,EAAGjX,EAAI2K,OAAO,GAAIgC,IAAU,iBACxBwG,KAAM1H,EAAE,WACR2H,QAAS3H,EAAE,QACXyL,SAAU,EACVC,KAAMrY,GACFA,EAAIiL,KACJjL,EAAIkL,IAAMlL,EAAI1C,OACd0C,EAAIiL,KAAOjL,EAAI3C,MACf2C,EAAIkL,MAEJ,EACA,EACA,EACA,GAEJoN,OACIjE,KAAM1H,EAAE,SACRD,EAAGC,EAAE,gBACL4L,GAAI5L,EAAE,cACN6L,GAAG,GAEPC,WACIC,WACIC,IACIV,GAAI,EACJC,GAAI,IAGZU,SACIC,IACIlE,WAAYhI,EAAE,cACdgL,OAAQL,EACR1B,QACI,EACA,GAEJ8B,YAAaL,EAAW,EAAI,EAC5BO,SAAUvB,EACVwB,SACI,GACA,YAQ5B3W,EAAI2K,OAAOkM,GACXA,EAAQjD,cAAgB,OAAQC,GAC5B9X,IACAiE,EAAIkL,SAASnP,GAAQ8a,IAGtBA,EAEX,QAASe,GAAc5X,EAAK6X,EAAU/Y,GAAtC,GACQqX,GAA4B,UAAjB0B,EAAS9G,KACpBoE,EAAQH,EAAsBhV,EAAK6X,EAAS5C,OAC5CmB,EAASD,GACT0B,EAAS7U,MAAMC,EACf4U,EAAS7U,MAAM8U,EACfD,EAAS7U,MAAM+S,EACf8B,EAASE,IAAI9U,EACb4U,EAASE,IAAID,EACbD,EAASE,IAAIhC,IAEb8B,EAAS7U,MAAMC,EACf4U,EAAS7U,MAAM8U,EACfD,EAASE,IAAI9U,EACb4U,EAASE,IAAID,GAEbzB,EAAUH,EAAmBlW,EAAKmW,EAAU0B,EAAS5C,MAAOmB,EAAQjB,EAAMU,OAAQgC,EAASG,WAAalZ,GACxG+X,EAAU1B,EAAMnF,SAAW4G,EAAqB5W,EAAKmW,EAAU0B,EAAS5C,MAAOmB,EAAQjB,EAAMS,UAAWiC,EAASG,WAAalZ,GAAO,IACzI,QACIkR,SAAUmF,EAAMnF,SAChBqG,QAASA,EACTQ,QAASA,GAiTjB,QAASoB,GAAQpc,GACb,MAAOA,GAAIX,QAAQ,uBAAwB,MAE/C,QAASgd,GAAaC,GAAtB,GASQC,GARAC,EAAK,yIACLrK,EAAIqK,EAAGjK,KAAK+J,EAChB,OAAKnK,IAMDoK,EAAWpK,EAAE,GAAKsK,SAAStK,EAAE,GAAI,IAAM,IAEvCuK,OAAQvK,EAAE,IAA4B,UAAtBA,EAAE,GAAGpE,cACrB4O,QAASxK,EAAE,GACXyK,KAAMzK,EAAE,IAAM,YAAYvF,KAAKuF,EAAE,IACjCoK,SAAUA,EACVM,WAAY1K,EAAE,IAAe,UAATA,EAAE,IAAkBoK,EAAWE,SAAStK,EAAE,IAAK,IAAM,KACzE2K,WAAY3K,EAAE,IAAI4K,MAAM,YAAYhb,IAAIqa,MAXpCG,SAAU,GACVO,WAAY,cAaxB,QAASE,GAAWtc,GAChB,QAASuc,GAASzR,GAOd,MANI9K,GAAMkc,OACNpR,GAAQ,SAER9K,EAAMgc,SACNlR,GAAQ,WAELA,EAAKuC,cARpB,GAWQvC,GAAMsG,EAEG7R,EAHT6c,EAAapc,EAAMoc,UAEvB,IAAIA,YAAsBtW,OACtB,IAASvG,EAAI,EAAGA,EAAI6c,EAAW3c,SAC3BqL,EAAOyR,EAASH,EAAW7c,MAC3B6R,EAAMoL,GAAc1R,OAFiBvL,OAQzC6R,GAAMoL,GAAcJ,EAAW/O,cAEnC,MAAqB,kBAAP+D,IACVA,EAAMA,GAKV,OAHKA,KACDA,EAAM,eAEHA,EAoBX,QAASqL,GAAUC,EAAO5R,GACtB4R,EAAQA,EAAMrP,cACdmP,GAAcE,GAAS,WACnB,MAAOF,IAAc1R,IAEzB0R,GAAcE,EAAQ,SAAW,WAC7B,MAAOF,IAAc1R,EAAO,UAEhC0R,GAAcE,EAAQ,WAAa,WAC/B,MAAOF,IAAc1R,EAAO,YAEhC0R,GAAcE,EAAQ,gBAAkB,WACpC,MAAOF,IAAc1R,EAAO,iBAYpC,QAAS6R,GAAW7R,EAAMsG,GACtB,GAAwB,GAApB9F,UAAU7L,OACV,IAAK,GAAIF,KAAKuL,GACNvC,EAAeuC,EAAMvL,IACrBod,EAAWpd,EAAGuL,EAAKvL,QAM3B,QAFAuL,EAAOA,EAAKuC,cACZmP,GAAc1R,GAAQsG,EACdtG,GACR,IAAK,cACD0R,GAAc,cAAgBpL,CAC9B,MACJ,KAAK,mBACDoL,GAAc,mBAAqBpL,CACnC,MACJ,KAAK,qBACDoL,GAAc,qBAAuBpL,CACrC,MACJ,KAAK,0BACDoL,GAAc,0BAA4BpL,CAC1C,MACJ,KAAK,eACDoL,GAAqB,MAAIpL,CACzB,MACJ,KAAK,oBACDoL,GAAc,cAAgBpL,CAC9B,MACJ,KAAK,sBACDoL,GAAc,gBAAkBpL,CAChC,MACJ,KAAK,2BACDoL,GAAc,qBAAuBpL,CACrC,MACJ,KAAK,cACDoL,GAAyB,UAAIpL,CAC7B,MACJ,KAAK,mBACDoL,GAAc,kBAAoBpL,CAClC,MACJ,KAAK,qBACDoL,GAAc,oBAAsBpL,CACpC,MACJ,KAAK,0BACDoL,GAAc,yBAA2BpL,GAKrD,QAASwL,GAAKjU,EAAGxE,GAAjB,GACQb,GAAKqF,EAAE,GAAIkP,EAAKlP,EAAE,GAAIkU,EAAKlU,EAAE,GAAImU,EAAKnU,EAAE,GAAIoU,EAAKpU,EAAE,GAAIqU,EAAKrU,EAAE,GAC9DpF,EAAKY,EAAE,GAAI6T,EAAK7T,EAAE,GAAI8Y,EAAK9Y,EAAE,GAAI+Y,EAAK/Y,EAAE,GAAIgZ,EAAKhZ,EAAE,GAAIiZ,EAAKjZ,EAAE,EAClE,QACIb,EAAKC,EAAKsU,EAAKoF,EACf3Z,EAAK0U,EAAKH,EAAKqF,EACfL,EAAKtZ,EAAKuZ,EAAKG,EACfJ,EAAK7E,EAAK8E,EAAKI,EACfH,EAAKxZ,EAAKyZ,EAAKC,EAAKE,EACpBJ,EAAK/E,EAAKgF,EAAKE,EAAKE,GAG5B,QAASC,GAAiB5L,GACtB,MAAgB,KAATA,EAAE,IAAqB,IAATA,EAAE,IAAqB,IAATA,EAAE,IAAqB,IAATA,EAAE,IAAqB,IAATA,EAAE,IAAqB,IAATA,EAAE,GAuDnF,QAAS/F,GAAO4R,EAAOpI,GAEnB,QAASlI,GAAUlC,EAAM+C,EAAQrO,GAI7B,MAHKA,KACDA,EAAOwC,GAEPxC,EAAKiE,KAAyB,MAAlBjE,EAAKiE,IAAIqH,GACdtL,EAAKiE,IAAIqH,GAEb+C,EAoCX,QAAS0P,KAcL,QAASC,GAASF,GAAlB,GAKQrQ,GAA6EwQ,EAGrE3c,EAMA4c,EASR1N,EAtBAhO,EAAUsb,EAAMtb,QAChBoE,EAAMuX,GAASL,GACfM,EAAOxX,EAAIwX,IACfN,GAAQlX,EAAIyX,KACR5Q,EAAYD,EAAU,YAAaA,EAAU,YAAa,QAAShL,GAAUyb,GAAY,EAC5E,QAAbxQ,IACI2Q,GACI9c,EAAO8c,EAAKE,UAChB7Q,GACInM,EAAKlB,MACLkB,EAAKjB,QAET4d,GAAY,EACRC,EAASE,EAAKG,YAClB3X,EAAM,GAAI4X,IAAQnD,MAClBzU,EAAIsK,UAAU,GAAIuN,IAAcC,OAAO,EAAG,EAAG,EAAG,IAAIR,EAAOhX,KAAIgX,EAAOnC,KACtEnV,EAAI+X,OAAOb,GACXA,EAAQlX,GAER6G,EAAY,MAIpB+C,EAAOvM,EAAIqM,SACP7C,UAAWA,EACXC,OAAQF,EAAU,SAAUA,EAAU,UAAWhL,GACjDyb,UAAWA,EACXW,UAAWpR,EAAU,YAAaA,EAAU,aAAa,GAAQhL,KAErEqc,GAAYf,EAAOtN,EAAMvM,GA3C7B,OAAM6a,EAAQ,GAAd,CAGA,GAAI7a,GAAM,GAAImK,IACV2Q,SAAUvR,EAAU,YACpBwR,MAAOxR,EAAU,SACjByR,OAAQzR,EAAU,UAClB0R,QAAS1R,EAAU,WACnB2R,SAAU3R,EAAU,YACpB4R,QAAS5R,EAAU,WACnBoI,KAAMpI,EAAU,QAChB6R,UAAW7R,EAAU,cAkCrB8R,GACAxB,EAAMyB,SAAS/E,QAAQwD,GAEvBA,EAASF,GAEbpI,EAASzR,EAAIiI,SAAUjI,IAhG/B,GAkGQ6a,GAjGAU,KAAYC,KAAajd,EAAUsb,EAAMtb,QAUzC8c,EAAY9R,EAAU,aACtBkS,EAASlS,EAAU,SACnBkS,IACAtM,IAEJ0K,EAAM6B,SAAS,SAAUC,GACrBC,IACIzK,MAAO,SAAUwK,GAAV,GAGK7c,GACA0W,EAHJ7H,EAAMgO,EAAQnM,KACdiM,IACI3c,EAAM6c,EAAQxB,OAAO9c,KACrBmY,EAAOgG,EAAO7N,GAClB7O,GACI3C,MAAOoF,KAAKsa,KAAK/c,EAAI3C,MAAQsf,EAAS,IACtCrf,OAAQmF,KAAKsa,KAAK/c,EAAI1C,OAASqf,EAAS,KAExCjG,IACA1W,EAAI3C,MAAQoF,KAAKqB,IAAI4S,EAAKrZ,MAAO2C,EAAI3C,OACrC2C,EAAI1C,OAASmF,KAAKqB,IAAI4S,EAAKpZ,OAAQ0C,EAAI1C,SAE3Cof,EAAO7N,GAAO7O,GAEd0c,EAAO7N,GAAO,MAGtBmO,KAAM,SAAUH,GAAV,GACEpf,GAAQ2b,EAAayD,EAAQpd,QAAQkI,MACrCkH,EAAMkL,EAAWtc,EACjBgf,GAAM/S,QAAQmF,GAAO,GACrB4N,EAAM9f,KAAKkS,KAGpBgO,KAuDHd,EAAQ,EACZkB,GAAUR,EAAOzB,GACjBkC,GAAWR,EAAQ1B,GAEvB,QAASlJ,GAAUiJ,EAAOpI,GACtBxJ,EAAO4R,EAAO,SAAUzZ,GACpBqR,EAAS,+BAAiCrR,EAAKqD,YAGvD,QAASwY,IAAOpC,EAAOpI,GACnBxJ,EAAO4R,EAAO,SAAUzZ,GACpBqR,EAAS,GAAI1U,QAAOmf,MAAM9b,EAAKlC,QAAU6S,KAAM,uBAGvD,QAASoL,IAAStC,EAAOuC,EAAUC,EAAO5K,GAClC1U,OAAOmf,OAASI,GAAeC,OAC/BN,GAAOpC,EAAO,SAAU/I,GACpB9T,EAAMwf,QACFC,QAAS3L,EACT4L,SAAUN,IAEV3K,GACAA,EAASX,KAIjBF,EAAUiJ,EAAO,SAAU8C,GACvB3f,EAAMwf,QACFC,QAASE,EACTD,SAAUN,EACVQ,SAAUP,IAEV5K,GACAA,EAASkL,KAKzB,QAASf,IAASiB,EAAUlB,GACxB,GAAImB,GAAUD,EAASlB,EAAQoB,SAC/B,OAAID,GACOA,EAAQ/X,KAAK/C,MAAM8a,EAASjV,WAEhC8T,EAEX,QAASf,IAAYe,EAASpP,EAAMvM,GAApC,GAIQiN,GACA4J,EAQI7I,CAZJ2N,GAAQpd,QAAQye,WAChBzQ,EAAK0Q,QAAQ,UAAYtB,EAAQpd,QAAQye,WAEzC/P,EAAY0O,EAAQ1O,YACpB4J,EAAU8E,EAAQ9E,UACtBtK,EAAK2Q,OACU,MAAXrG,GAAmBA,EAAU,GAC7BtK,EAAK4Q,WAAWtG,GAEpBuG,GAAiBzB,EAASpP,EAAMvM,GAChCqd,GAAe1B,EAASpP,EAAMvM,GAC1BiN,IACIe,EAAIf,EAAUqQ,SAClB/Q,EAAKU,UAAUe,EAAE9I,EAAG8I,EAAEtN,EAAGsN,EAAEuP,EAAGvP,EAAEwP,EAAGxP,EAAEyP,EAAGzP,EAAEpT,IAE9C8iB,GAAY/B,EAASpP,EAAMvM,GAC3B4b,IACI+B,KAAMC,GACNC,UAAWC,GACXC,OAAQC,GACRC,IAAKC,GACLpC,KAAMqC,GACNhN,MAAOd,GACP+G,MAAOgH,GACPC,KAAMC,IACP3C,EAASpP,EAAMvM,GAClBuM,EAAKgS,UACD5C,EAAQpd,QAAQye,WAChBzQ,EAAK0Q,QAAQ,QAAUtB,EAAQpd,QAAQye,WAG/C,QAASI,IAAiBzB,EAASpP,GAAnC,GAKQuJ,GAWA3Z,EAOAqiB,EAIAC,EAIAC,EAIA7H,EAlCA8H,EAAShD,EAAQgD,QAAUhD,EAAQgD,QACvC,IAAKA,EAAL,CAIA,GADI7I,EAAQ6I,EAAO7I,MACR,CAEP,GADAA,EAAQ8I,GAAa9I,GACR,MAATA,EACA,MAEJvJ,GAAKsS,eAAe/I,EAAMC,EAAGD,EAAME,EAAGF,EAAMpV,GAC7B,GAAXoV,EAAM5Q,GACNqH,EAAKuS,iBAAiBhJ,EAAM5Q,GAIpC,GADI/I,EAAQwiB,EAAOxiB,MACN,MAATA,EAAe,CACf,GAAc,IAAVA,EACA,MAEJoQ,GAAKwS,aAAa5iB,GAElBqiB,EAAWG,EAAOH,SAClBA,GACAjS,EAAKyS,eAAeC,GAAcT,GAAW,GAE7CC,EAAUE,EAAOF,QACjBA,GACAlS,EAAK2S,WAAWC,GAASV,IAEzBC,EAAWC,EAAOD,SAClBA,GACAnS,EAAK6S,YAAYC,GAAUX,IAE3B7H,EAAU8H,EAAO9H,QACN,MAAXA,GACAtK,EAAKuS,iBAAiBjI,IAG9B,QAASwG,IAAe1B,EAASpP,GAAjC,GAQQuJ,GAWAe,EAlBAyI,EAAO3D,EAAQ2D,MAAQ3D,EAAQ2D,MACnC,IAAKA,KAGDA,YAAgB/E,IAAQgF,UAA5B,CAIA,GADIzJ,EAAQwJ,EAAKxJ,MACN,CAEP,GADAA,EAAQ8I,GAAa9I,GACR,MAATA,EACA,MAEJvJ,GAAKiT,aAAa1J,EAAMC,EAAGD,EAAME,EAAGF,EAAMpV,GAC3B,GAAXoV,EAAM5Q,GACNqH,EAAKkT,eAAe3J,EAAM5Q,GAG9B2R,EAAUyI,EAAKzI,QACJ,MAAXA,GACAtK,EAAKkT,eAAe5I,IAG5B,QAAS6G,IAAY/B,EAASpP,EAAMvM,GAChC,GAAIoN,GAAOuO,EAAQvO,MACfA,KACAsS,GAAUtS,EAAMb,EAAMvM,GACtBuM,EAAKa,QAGb,QAASuS,IAAWC,GAChB,MAAOA,KAAUA,YAAiBrF,IAAQgF,UAAYK,EAAM9J,QAAU,wBAAwBrN,KAAKmX,EAAM9J,SAA0B,MAAf8J,EAAMzjB,OAAiByjB,EAAMzjB,MAAQ,KAAwB,MAAjByjB,EAAM/I,SAAmB+I,EAAM/I,QAAU,IAE7M,QAASgJ,IAAclE,EAASpP,EAAMvM,EAAK2e,GAA3C,GAQYxI,GACAnT,EAAO+U,EAsBP9C,EAgBA4C,EAOA/Y,EACAghB,EAAoBziB,EAtDxBiiB,EAAO3D,EAAQ2D,MACnB,IAAIA,YAAgB/E,IAAQgF,SA6DxB,MA5DIZ,GACApS,EAAKwT,aAELxT,EAAKa,OAEL+I,EAAWmJ,YAAgB/E,IAAQyF,eAEnC7J,GACAnT,GACIC,EAAGqc,EAAKW,SAAShd,EACjB6U,EAAGwH,EAAKW,SAASnI,EACjB/B,EAAG,GAEPgC,GACI9U,EAAGqc,EAAKW,SAAShd,EACjB6U,EAAGwH,EAAKW,SAASnI,EACjB/B,EAAGuJ,EAAKY,YAGZld,GACIC,EAAGqc,EAAKtc,QAAQC,EAChB6U,EAAGwH,EAAKtc,QAAQ8U,GAEpBC,GACI9U,EAAGqc,EAAKvH,MAAM9U,EACd6U,EAAGwH,EAAKvH,MAAMD,IAGlB7C,EAAQqK,EAAKrK,MAAMkL,WAAWviB,IAAI,SAAUwiB,GAAV,GAO9BtK,GANAvV,EAAS6f,EAAK7f,QAQlB,OANIA,GADA,KAAKkI,KAAKlI,GACD4E,WAAW5E,GAAU,IAErB4E,WAAW5E,GAEpBuV,EAAQ8I,GAAawB,EAAKtK,SAC9BA,EAAM5Q,GAAKkb,EAAKvJ,WAEZtW,OAAQA,EACRuV,MAAOA,KAGfb,EAAMoL,QAAQpL,EAAM,IACpBA,EAAMxZ,KAAKwZ,EAAMA,EAAMjZ,OAAS,IAC5B6b,GACAG,UAAWsH,EAAKtH,YAChBjH,KAAMoF,EAAW,SAAW,SAC5BnT,MAAOA,EACP+U,IAAKA,EACL9C,MAAOA,GAEPnW,EAAM6c,EAAQ2E,UACdR,EAAKhhB,EAAIyhB,UAAWljB,EAAOyB,EAAIub,UACnCvb,GACIiL,KAAM+V,EAAG7c,EACT+G,IAAK8V,EAAGhI,EACR3b,MAAOkB,EAAKlB,MACZC,OAAQiB,EAAKjB,QAEjBmQ,EAAKsL,SAASA,EAAU/Y,IACjB,EAGf,QAAS0hB,IAAgB7E,EAASpP,EAAMvM,GAChC2f,GAAWhE,EAAQ2D,SAAWK,GAAWhE,EAAQgD,UAC5CkB,GAAclE,EAASpP,EAAMvM,GAAK,IACnCuM,EAAKkU,aAEFd,GAAWhE,EAAQ2D,QACrBO,GAAclE,EAASpP,EAAMvM,GAAK,IACnCuM,EAAK+S,OAEFK,GAAWhE,EAAQgD,UAC1BpS,EAAKoS,SAELpS,EAAKmU,MAGb,QAASC,IAAcC,EAAMrU,GAA7B,GAGYrH,GACKpJ,EAML+kB,EATJC,EAAWF,EAAKE,QACpB,IAAuB,GAAnBA,EAAS9kB,QAAe4kB,EAAKriB,QAAQwiB,OAAQ,CAE7C,IADI7b,KACKpJ,EAAI,EAAGA,EAAIglB,EAAS9kB,SAAUF,EAAG,CACtC,GAAIglB,EAAShlB,GAAGklB,YACZ,OAAO,CAEX9b,GAAEpJ,GAAKglB,EAAShlB,GAAGmlB,SAGvB,GADIJ,EAAS3b,EAAE,GAAG4S,GAAK5S,EAAE,GAAG4S,GAAK5S,EAAE,GAAGjC,GAAKiC,EAAE,GAAGjC,GAAKiC,EAAE,GAAG4S,GAAK5S,EAAE,GAAG4S,GAAK5S,EAAE,GAAGjC,GAAKiC,EAAE,GAAGjC,GAAKiC,EAAE,GAAGjC,GAAKiC,EAAE,GAAGjC,GAAKiC,EAAE,GAAG4S,GAAK5S,EAAE,GAAG4S,GAAK5S,EAAE,GAAGjC,GAAKiC,EAAE,GAAGjC,GAAKiC,EAAE,GAAG4S,GAAK5S,EAAE,GAAG4S,EAGpK,MADAvL,GAAKY,KAAKjI,EAAE,GAAGjC,EAAGiC,EAAE,GAAG4S,EAAG5S,EAAE,GAAGjC,EAAIiC,EAAE,GAAGjC,EAAGiC,EAAE,GAAG4S,EAAI5S,EAAE,GAAG4S,IAClD,GAInB,QAAS4H,IAAU/D,EAASpP,EAAMvM,GAAlC,GAMiBwV,GAAM1Z,EACPolB,EACAD,EAIIE,EACAH,EAZZF,EAAWnF,EAAQmF,QACvB,IAAwB,IAApBA,EAAS9kB,SAGR2kB,GAAchF,EAASpP,EAAMvM,GAAM,CACpC,IAAelE,EAAI,EAAGA,EAAIglB,EAAS9kB,SAAUF,EACrColB,EAAMJ,EAAShlB,GACfmlB,EAASC,EAAID,SACZzL,GAGG2L,EAAU3L,EAAK4L,aACfJ,EAAYE,EAAIF,YAChBG,GAAWH,EACXzU,EAAK8U,OAAOF,EAAQle,EAAGke,EAAQrJ,EAAGkJ,EAAU/d,EAAG+d,EAAUlJ,EAAGmJ,EAAOhe,EAAGge,EAAOnJ,GAE7EvL,EAAK+U,OAAOL,EAAOhe,EAAGge,EAAOnJ,IAPjCvL,EAAKgV,OAAON,EAAOhe,EAAGge,EAAOnJ,GAUjCtC,EAAO0L,CAEPvF,GAAQpd,QAAQwiB,QAChBxU,EAAKiV,SAIjB,QAAS5D,IAASjC,EAASpP,EAAMvM,GAC7B0f,GAAU/D,EAASpP,EAAMvM,GACzBwgB,GAAgB7E,EAASpP,EAAMvM,GAEnC,QAAS8d,IAAcnC,EAASpP,EAAMvM,GAAtC,GAEalE,GADL2lB,EAAQ9F,EAAQ8F,KACpB,KAAS3lB,EAAI,EAAGA,EAAI2lB,EAAMzlB,SAAUF,EAChC4jB,GAAU+B,EAAM3lB,GAAIyQ,EAAMvM,EAE9BwgB,IAAgB7E,EAASpP,EAAMvM,GAEnC,QAASge,IAAWrC,EAASpP,EAAMvM,GAC/B,GAAIgW,GAAI2F,EAAQ+F,UAChBnV,GAAKoV,OAAO3L,EAAEiK,OAAOhd,EAAG+S,EAAEiK,OAAOnI,EAAG9B,EAAEkK,QACtCM,GAAgB7E,EAASpP,EAAMvM,GAEnC,QAASke,IAAQvC,EAASpP,EAAMvM,GAAhC,GAGalE,GAFL8lB,EAASjG,EAAQ+F,WAAWG,aAEhC,KADAtV,EAAKgV,OAAOK,EAAO,GAAG3e,EAAG2e,EAAO,GAAG9J,GAC1Bhc,EAAI,EAAGA,EAAI8lB,EAAO5lB,QACvBuQ,EAAK8U,OAAOO,EAAO9lB,GAAGmH,EAAG2e,EAAO9lB,KAAKgc,EAAG8J,EAAO9lB,GAAGmH,EAAG2e,EAAO9lB,KAAKgc,EAAG8J,EAAO9lB,GAAGmH,EAAG2e,EAAO9lB,KAAKgc,EAEjG0I,IAAgB7E,EAASpP,EAAMvM,GAEnC,QAASme,IAASxC,EAASpP,GAA3B,GAGQuV,GAFAvlB,EAAQ2b,EAAayD,EAAQpd,QAAQkI,MACrCvD,EAAMyY,EAAQoG,SAEdpG,GAAQ2D,QAAU3D,EAAQgD,SAC1BmD,EAAOE,GAAsBC,cACtBtG,EAAQ2D,OACfwC,EAAOE,GAAsB1C,KACtB3D,EAAQgD,WACfmD,EAAOE,GAAsBrD,QAEjCpS,EAAKU,UAAU,EAAG,EAAG,KAAO/J,EAAID,EAAGC,EAAI4U,EAAIvb,EAAM6b,UACjD7L,EAAK2V,YACL3V,EAAK4V,QAAQtJ,EAAWtc,GAAQA,EAAM6b,UACtC7L,EAAK6V,qBAAqBN,GAC1BvV,EAAK8V,SAAS1G,EAAQrP,UAAWqP,EAAQ2G,SAAW3G,EAAQ2G,SAASnmB,QAAU,MAC/EoQ,EAAKgW,UAET,QAASnE,IAAUzC,EAASpP,EAAMvM,GAAlC,GAIQsb,GACKxf,CAAT,KAJI6f,EAAQ6G,UACRjW,EAAKkW,QAAQ9G,EAAQ6G,SAAS7U,IAAKgO,EAAQ6G,UAE3ClH,EAAWK,EAAQL,SACdxf,EAAI,EAAGA,EAAIwf,EAAStf,SAAUF,EACnC8e,GAAYU,EAASxf,GAAIyQ,EAAMvM,GAGvC,QAASqQ,IAAUsL,EAASpP,GAA5B,GAKQY,GACA2S,EACA4C,EANA/U,EAAMgO,EAAQnM,KACb7B,KAGDR,EAAOwO,EAAQxO,OACf2S,EAAK3S,EAAKmN,YACVoI,EAAKvV,EAAKkN,UACd9N,EAAKU,UAAUyV,EAAGvmB,MAAO,EAAG,GAAIumB,EAAGtmB,OAAQ0jB,EAAG7c,EAAG6c,EAAGhI,EAAI4K,EAAGtmB,QAC3DmQ,EAAK8D,UAAU1C,IAEnB,QAAS2Q,IAAS3C,EAASpP,EAAMvM,GAC7B,GAAI0hB,GAAW/F,EAAQ+F,UACvBnV,GAAKY,KAAKuU,EAASzH,OAAOhX,EAAGye,EAASzH,OAAOnC,EAAG4J,EAASrkB,KAAKlB,MAAOulB,EAASrkB,KAAKjB,QACnFokB,GAAgB7E,EAASpP,EAAMvM,GAEnC,QAAS4e,IAAajhB,GAClB,GAAImY,GAAQ9Y,EAAM2lB,WAAWhlB,GAAO,EACpC,OAAOmY,GAAQA,EAAM8M,QAAU,KAEnC,QAAS1I,IAASE,GAad,QAASyI,GAAOC,GAEZ,MADAC,IAAU,EACHD,EAEX,QAASE,GAAQC,GACb,MAAOA,GAAMD,WAAaC,EAAMpM,UAAY,IAAM8I,GAAWsD,EAAM3D,SAAWK,GAAWsD,EAAMtE,WAEnG,QAASuE,GAAShe,GAAlB,GAEapJ,GACDqnB,EAFJziB,IACJ,KAAS5E,EAAI,EAAGA,EAAIoJ,EAAElJ,SAAUF,EACxBqnB,EAAKC,EAAIle,EAAEpJ,IACL,MAANqnB,GACAziB,EAAEjF,KAAK0nB,EAGf,OAAOziB,GAEX,QAAS2iB,GAAaJ,EAAOroB,GAA7B,GAMQwS,GALAkW,EAAcC,EACdC,EAAalG,CACb2F,GAAMhW,cACNqQ,EAASA,EAAOmG,aAAaR,EAAMhW,YAAYqQ,WAE/ClQ,EAAO6V,EAAM7V,OACbA,IACAA,EAAOA,EAAK+M,OACR/M,IACAA,EAAOA,EAAK+M,KAAKmD,GACjBiG,EAAUA,EAAU/I,GAAc6D,KAAKqF,UAAUH,EAASnW,GAAQA,GAG1E,KACI,MAAOxS,KACT,QACE2oB,EAAUD,EACVhG,EAASkG,GAGjB,QAASG,GAAUV,GACf,GAAe,MAAXM,EACA,OAAO,CAEX,IAAIzkB,GAAMmkB,EAAM3C,UAAUnG,KAAKmD,EAI/B,OAHIiG,IAAWzkB,IACXA,EAAM0b,GAAc6D,KAAKqF,UAAU5kB,EAAKykB,IAErCzkB,EAEX,QAASskB,GAAIH,GACT,MAAOI,GAAaJ,EAAO,WACvB,KAAMA,YAAiB1I,IAAQnD,OAAS6L,YAAiB1I,IAAQsD,WAAY,CACzE,GAAI/e,GAAM6kB,EAAUV,EACpB,KAAKnkB,EACD,MAAO+jB,GAAO,KAElBe,GAAaA,EAAapJ,GAAc6D,KAAKwF,MAAMD,EAAY9kB,GAAOA,EAE1E,MAAO8c,KACH+B,KAAM,SAAUsF,GACZ,MAA8B,KAA1BA,EAAMnC,SAAS9kB,QAAiBgnB,EAAQC,GAGrCA,EAFIJ,EAAO,OAItBhF,UAAW,SAAUoF,GACjB,IAAKD,EAAQC,GACT,MAAOJ,GAAO,KAElB,IAAIM,GAAK,GAAI5I,IAAQsD,UAAUoF,EAAM1kB,QAErC,OADA4kB,GAAG1B,MAAQyB,EAASD,EAAMxB,OACF,IAApB0B,EAAG1B,MAAMzlB,OACF6mB,EAAO,MAEXM,GAEXpF,OAAQ,SAAUkF,GACd,MAAKD,GAAQC,GAGNA,EAFIJ,EAAO,OAItB5E,IAAK,SAAUgF,GACX,MAAKD,GAAQC,GAGNA,EAFIJ,EAAO,OAItB/G,KAAM,SAAUmH,GACZ,MAAK,KAAKxa,KAAKwa,EAAM3W,YAAe0W,EAAQC,GAGrCA,EAFIJ,EAAO,OAItB1R,MAAO,SAAU8R,GACb,MAAMA,GAAMD,WAAaC,EAAMpM,UAAY,EAGpCoM,EAFIJ,EAAO,OAItBzL,MAAO,SAAU6L,GACb,GAAIE,GAAK,GAAI5I,IAAQnD,MAAM6L,EAAM1kB,QAGjC,OAFA4kB,GAAG7H,SAAW4H,EAASD,EAAM3H,UAC7B6H,EAAGX,SAAWS,EAAMT,SAChBS,IAAU7I,GAA+B,IAAvB+I,EAAG7H,SAAStf,QAAiBinB,EAAMT,SAGlDW,EAFIN,EAAO,OAItBxE,KAAM,SAAU4E,GACZ,MAAKD,GAAQC,GAGNA,EAFIJ,EAAO,QAIvBI,KA/Hf,GAIQF,GAHAQ,GAAU,EACVjG,EAAS9C,GAAcC,OAAOqJ,OAC9BF,EAAa,IAEjB,GACIb,IAAU,EACV3I,EAAOgJ,EAAIhJ,SACNA,GAAQ2I,EACjB,QACI3I,KAAMA,EACND,KAAMyJ,GAwHd,QAASG,IAAUlK,EAAOtb,GAA1B,GAEazC,GADLkoB,EAAU/mB,GAAKgnB,eACnB,KAASnoB,IAAKyC,GACD,UAALzC,GAAiB+d,EAAMtb,QAAQyB,KAAO6Z,EAAMtb,QAAQyB,IAAIkkB,eAG5DrK,EAAMtb,QAAQsE,IAAI,OAAS/G,EAAGyC,EAAQzC,GAG1C,OADA8U,GAAUiJ,EAAOmK,EAAQG,SAClBH,EAEX,QAASI,IAAgBvK,EAAOtb,GAAhC,GAEazC,GADLkoB,EAAU/mB,GAAKgnB,eACnB,KAASnoB,IAAKyC,GACD,UAALzC,GAAiB+d,EAAMtb,QAAQyB,KAAO6Z,EAAMtb,QAAQyB,IAAIkkB,eAG5DrK,EAAMtb,QAAQsE,IAAI,OAAS/G,EAAGyC,EAAQzC,GAO1C,OALIiB,QAAOmf,OAASI,GAAeC,OAC/BN,GAAOpC,EAAOmK,EAAQG,SAEtBvT,EAAUiJ,EAAOmK,EAAQG,SAEtBH,EAnpHd,GAEOK,IACA/H,GACAgI,GACA/J,GACAtd,GACAud,GACA/X,GACAkB,GA4XA4gB,GA2CAC,GAkCAC,GA8CAC,GAuCAC,GAmCAC,GAiGAC,GAwEAC,GAwFAC,GA4KAC,GA6CAze,GA+KA2H,GACAnF,GACA8K,GACAnK,GA6YAwF,GAmEAE,GA8HA2M,GACAC,GAwFA9T,GA2EAqD,GAcA0Z,GAYApS,GAeAD,GAQAlK,GAoBAiE,GA2BAxB,GAOAC,GAYA8H,GAoGAgS,GAYAC,GA0GAC,GAmSArY,GAmWAgM,GA2GAsM,GAUArD,GACA/C,GAgCAE,GAKAE,EArjGJtiB,QAAOC,MAAMgD,IAAMjD,OAAOC,MAAMgD,QAC5BqkB,GAAUrnB,EAAMqnB,QAChB/H,GAAiB+H,GAAQnW,QACzBoW,GAAWtnB,EAAMgD,IACjBua,GAAUvd,EAAMud,QAChBtd,GAAOsd,GAAQtd,KACfud,GAAgBxd,EAAM0kB,SACtBjf,GAAyC,mBAAfC,aAA8B1F,EAAMqnB,QAAQnW,WAAalR,EAAMqnB,QAAQnW,QAAQC,MAAQnR,EAAMqnB,QAAQnW,QAAQoX,QAAU,GACjJ3hB,GAAS,WACT,GAAI4hB,GAAS,mEACb,QACI3hB,OAAQ,SAAU/H,GAEd,IAFI,GAGI2pB,GACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EARJC,EAAQlqB,EAAIX,QAAQ,sBAAuB,IAAKY,EAAI,EAAGoG,EAAI6jB,EAAM/pB,OAAQiI,KACtEnI,EAAIoG,GACHsjB,EAAOD,EAAO/c,QAAQud,EAAMC,OAAOlqB,MACnC2pB,EAAOF,EAAO/c,QAAQud,EAAMC,OAAOlqB,MACnC4pB,EAAOH,EAAO/c,QAAQud,EAAMC,OAAOlqB,MACnC6pB,EAAOJ,EAAO/c,QAAQud,EAAMC,OAAOlqB,MACnC8pB,EAAOJ,GAAQ,EAAIC,IAAS,EAC5BI,GAAe,GAAPJ,IAAc,EAAIC,IAAS,EACnCI,GAAe,EAAPJ,IAAa,EAAIC,EAC7B1hB,EAAOxI,KAAKmqB,GACA,IAARF,GACAzhB,EAAOxI,KAAKoqB,GAEJ,IAARF,GACA1hB,EAAOxI,KAAKqqB,EAGpB,OAAO7hB,IAEXJ,OAAQ,SAAUd,GAGd,IAHI,GAII6iB,GACAC,EACAC,EACAN,EACAC,EACAC,EACAC,EATJ7pB,EAAI,EAAGoG,EAAIa,EAAM/G,OACjBiI,EAAS,GACNnI,EAAIoG,GACH0jB,EAAO7iB,EAAMjH,KACb+pB,EAAO9iB,EAAMjH,KACbgqB,EAAO/iB,EAAMjH,KACb0pB,EAAOI,IAAS,EAChBH,GAAe,EAAPG,IAAa,EAAIC,IAAS,EAClCH,GAAe,GAAPG,IAAc,EAAIC,IAAS,EACnCH,EAAc,GAAPG,EACPhqB,EAAIoG,GAAK,EACTwjB,EAAOC,EAAO,GACP7pB,EAAIoG,GAAK,IAChByjB,EAAO,IAEX1hB,GAAUshB,EAAOS,OAAOR,GAAQD,EAAOS,OAAOP,GAAQF,EAAOS,OAAON,GAAQH,EAAOS,OAAOL,EAE9F,OAAO1hB,QAqQnBmB,EAAUP,WACNohB,UAAW,SAAU5e,EAAMrB,GACvB,GAAIE,GAAM5I,KAAK+H,OAAOgC,EACtB,KAAKnB,EACD,KAAU5C,OAAM,SAAW+D,EAAO,0BAEtC,OAAO/J,MAAK+J,GAAQnB,EAAIggB,MAAQ,GAAIlgB,GAAK1I,KAAM4I,IAEnD+B,OAAQ,SAAU5C,GAAV,GAaA8gB,GACA5lB,EACA6lB,EACAC,EACKzgB,EAEGsgB,EAiBRI,EACAC,EApCAC,EAASlpB,KACTkI,EAAaZ,OAAOK,KAAKI,GAAQrJ,OACjCyqB,EAAUllB,KAAKmlB,IAAI,EAAGnlB,KAAKolB,MAAMplB,KAAKwM,IAAIvI,GAAcjE,KAAKqlB,MAC7DnhB,EAAwB,GAAVghB,EACd/gB,EAAgBnE,KAAKolB,MAAMplB,KAAKwM,IAAI0Y,GAAWllB,KAAKqlB,KACpDjhB,EAA0B,GAAbH,EAAkBC,EAC/BiC,EAAMrH,GACVqH,GAAIxG,UAAU5D,KAAKiI,YACnBmC,EAAI7G,WAAW2E,GACfkC,EAAI7G,WAAW4E,GACfiC,EAAI7G,WAAW6E,GACfgC,EAAI7G,WAAW8E,GACXwgB,EAA+B,GAAb3gB,EAClBjF,EAASmH,EAAInH,SAAW4lB,EACxBC,EAAa,KACbC,EAAYhmB,GAChB,KAASuF,IAAOP,GACZ,GAAIX,EAAiBW,EAAQO,GAWzB,IAVIsgB,EAAQ7gB,EAAOO,GACnB8B,EAAIzF,YAAY2D,GAChB8B,EAAIxG,UAAUslB,EAAO3gB,SAASqgB,IAC9Bxe,EAAIxG,UAAUX,GACdmH,EAAIxG,UAAUglB,EAAMlqB,QACpBqqB,EAAU/jB,MAAM4jB,GACL,QAAPtgB,IACAwgB,EAAa7lB,GAEjBA,GAAU2lB,EAAMlqB,OACTuE,EAAS,GACZ8lB,EAAU5lB,UAAU,GACpBF,GASZ,OALAmH,GAAIpF,MAAM+jB,EAAUnoB,OAChBooB,EAAMhpB,KAAKuI,SAAS6B,EAAIxJ,OACxBqoB,EAAa,WAAaD,EAC9B5e,EAAInH,OAAO6lB,EAAa,GACxB1e,EAAIxG,UAAUqlB,GACP7e,EAAIxJ,OAEf2H,SAAU,SAAUzF,GAChBA,EAAOC,EAAaD,EAEpB,KADA,GAAIkmB,GAAM,GACFlmB,EAAKE,OACTgmB,GAAOlmB,EAAKa,UAEhB,OAAa,YAANqlB,IAsBX/B,GAAYze,GACZO,MAAO,SAAUjG,GACbA,EAAKG,OAAOjD,KAAKiD,QACjBjD,KAAKgoB,QAAUllB,EAAKa,WACpB3D,KAAKupB,SAAWzmB,EAAKa,WACrB3D,KAAKwpB,mBAAqB1mB,EAAKa,WAC/B3D,KAAKypB,YAAc3mB,EAAKa,WACxB3D,KAAK0pB,MAAQ5mB,EAAKQ,YAClBtD,KAAK2pB,WAAa7mB,EAAKQ,YACvBtD,KAAK4pB,QAAU9mB,EAAKuB,KAAK,GACzBrE,KAAK6pB,SAAW/mB,EAAKuB,KAAK,GAC1BrE,KAAK8pB,KAAOhnB,EAAKW,aACjBzD,KAAK+pB,KAAOjnB,EAAKW,aACjBzD,KAAKgqB,KAAOlnB,EAAKW,aACjBzD,KAAKiqB,KAAOnnB,EAAKW,aACjBzD,KAAKkqB,SAAWpnB,EAAKQ,YACrBtD,KAAKmqB,cAAgBrnB,EAAKQ,YAC1BtD,KAAKoqB,kBAAoBtnB,EAAKW,aAC9BzD,KAAKqqB,iBAAmBvnB,EAAKW,aAC7BzD,KAAKsqB,gBAAkBxnB,EAAKW,cAEhCkH,OAAQ,SAAU0f,GACd,GAAIjgB,GAAMrH,GAkBV,OAjBAqH,GAAIxG,UAAU5D,KAAKgoB,SACnB5d,EAAIxG,UAAU5D,KAAKupB,UACnBnf,EAAIxG,UAAU,GACdwG,EAAIxG,UAAU5D,KAAKypB,aACnBrf,EAAI7G,WAAWvD,KAAK0pB,OACpBtf,EAAI7G,WAAWvD,KAAK2pB,YACpBvf,EAAIpF,MAAMhF,KAAK4pB,SACfxf,EAAIpF,MAAMhF,KAAK6pB,UACfzf,EAAI1G,YAAY1D,KAAK8pB,MACrB1f,EAAI1G,YAAY1D,KAAK+pB,MACrB3f,EAAI1G,YAAY1D,KAAKgqB,MACrB5f,EAAI1G,YAAY1D,KAAKiqB,MACrB7f,EAAI7G,WAAWvD,KAAKkqB,UACpB9f,EAAI7G,WAAWvD,KAAKmqB,eACpB/f,EAAI1G,YAAY1D,KAAKoqB,mBACrBhgB,EAAI1G,YAAY2mB,GAChBjgB,EAAI1G,YAAY1D,KAAKsqB,iBACdlgB,EAAIxJ,SAGfsmB,GAAY1e,GACZO,MAAO,SAAUjG,GACbA,EAAKG,OAAOjD,KAAKiD,OACjB,IAAIsnB,GAASvqB,KAAK2I,KAAK6hB,KAAKH,gBAExBrqB,MAAKgY,QADM,IAAXuS,EACeznB,EAAKyB,MAAMvE,KAAKtB,OAAS,EAAG,WACvC,MAAO,GAAIoE,EAAKQ,cAGLR,EAAKyB,MAAMvE,KAAKtB,OAAS,EAAGoE,EAAKa,WAGxD8mB,SAAU,SAAU7e,GAChB,MAAO5L,MAAKgY,QAAQpM,IAExB8e,SAAU,SAAU9e,GAChB,MAAO5L,MAAKgY,QAAQpM,EAAK,GAAK5L,KAAKgY,QAAQpM,IAE/CjB,OAAQ,SAAUqN,GAAV,GAGKxZ,GAFL4L,EAAMrH,IACN4nB,EAAkB3S,EAAQA,EAAQtZ,OAAS,GAAK,KACpD,KAASF,EAAI,EAAGA,EAAIwZ,EAAQtZ,SAAUF,EAC9BmsB,EACAvgB,EAAIxG,UAAUoU,EAAQxZ,IAEtB4L,EAAI7G,WAAWyU,EAAQxZ,GAAK,EAGpC,QACI+rB,OAAQI,EAAkB,EAAI,EAC9B/B,MAAOxe,EAAIxJ,UAInBumB,GAAY3e,GACZO,MAAO,SAAUjG,GACbA,EAAKG,OAAOjD,KAAKiD,QACjBjD,KAAKgoB,QAAUllB,EAAKa,WACpB3D,KAAK4qB,OAAS9nB,EAAKW,aACnBzD,KAAK6qB,QAAU/nB,EAAKW,aACpBzD,KAAK8qB,QAAUhoB,EAAKW,aACpBzD,KAAK+qB,gBAAkBjoB,EAAKQ,YAC5BtD,KAAKgrB,mBAAqBloB,EAAKW,aAC/BzD,KAAKirB,oBAAsBnoB,EAAKW,aAChCzD,KAAKkrB,WAAapoB,EAAKW,aACvBzD,KAAKmrB,eAAiBroB,EAAKW,aAC3BzD,KAAKorB,cAAgBtoB,EAAKW,aAC1BzD,KAAKqrB,YAAcvoB,EAAKW,aACxBX,EAAK+C,KAAK,GACV7F,KAAKsrB,iBAAmBxoB,EAAKW,aAC7BzD,KAAKurB,oBAAsBzoB,EAAKQ,aAEpCqH,OAAQ,SAAU6gB,GACd,GAAIphB,GAAMrH,GAwBV,OAvBAqH,GAAIxG,UAAU5D,KAAKgoB,SACnB5d,EAAI1G,YAAY1D,KAAK4qB,QACrBxgB,EAAI1G,YAAY1D,KAAK6qB,SACrBzgB,EAAI1G,YAAY1D,KAAK8qB,SACrB1gB,EAAI7G,WAAWvD,KAAK+qB,iBACpB3gB,EAAI1G,YAAY1D,KAAKgrB,oBACrB5gB,EAAI1G,YAAY1D,KAAKirB,qBACrB7gB,EAAI1G,YAAY1D,KAAKkrB,YACrB9gB,EAAI1G,YAAY1D,KAAKmrB,gBACrB/gB,EAAI1G,YAAY1D,KAAKorB,eACrBhhB,EAAI1G,YAAY1D,KAAKqrB,aACrBjhB,EAAIpF,OACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAEJoF,EAAI1G,YAAY1D,KAAKsrB,kBACrBlhB,EAAI7G,WAAWioB,EAAI9sB,QACZ0L,EAAIxJ,SAGfwmB,GAAY5e,GACZO,MAAO,SAAUjG,GACbA,EAAKG,OAAOjD,KAAKiD,QACjBjD,KAAKgoB,QAAUllB,EAAKa,WACpB3D,KAAKyrB,UAAY3oB,EAAKQ,YACtBtD,KAAK0rB,UAAY5oB,EAAKQ,YACtBtD,KAAK2rB,YAAc7oB,EAAKQ,YACxBtD,KAAK4rB,mBAAqB9oB,EAAKQ,YAC/BtD,KAAK6rB,qBAAuB/oB,EAAKQ,YACjCtD,KAAK8rB,SAAWhpB,EAAKQ,YACrBtD,KAAK+rB,kBAAoBjpB,EAAKQ,YAC9BtD,KAAKgsB,WAAalpB,EAAKQ,YACvBtD,KAAKisB,gBAAkBnpB,EAAKQ,YAC5BtD,KAAKksB,mBAAqBppB,EAAKQ,YAC/BtD,KAAKmsB,iBAAmBrpB,EAAKQ,YAC7BtD,KAAKosB,sBAAwBtpB,EAAKQ,YAClCtD,KAAKqsB,qBAAuBvpB,EAAKQ,YACjCtD,KAAKssB,kBAAoBxpB,EAAKQ,aAElCqH,OAAQ,SAAU4hB,GACd,GAAIniB,GAAMrH,GAgBV,OAfAqH,GAAIxG,UAAU5D,KAAKgoB,SACnB5d,EAAI7G,WAAWgpB,EAAS7tB,QACxB0L,EAAI7G,WAAWvD,KAAK0rB,WACpBthB,EAAI7G,WAAWvD,KAAK2rB,aACpBvhB,EAAI7G,WAAWvD,KAAK4rB,oBACpBxhB,EAAI7G,WAAWvD,KAAK6rB,sBACpBzhB,EAAI7G,WAAWvD,KAAK8rB,UACpB1hB,EAAI7G,WAAWvD,KAAK+rB,mBACpB3hB,EAAI7G,WAAWvD,KAAKgsB,YACpB5hB,EAAI7G,WAAWvD,KAAKisB,iBACpB7hB,EAAI7G,WAAWvD,KAAKksB,oBACpB9hB,EAAI7G,WAAWvD,KAAKmsB,kBACpB/hB,EAAI7G,WAAWvD,KAAKosB,uBACpBhiB,EAAI7G,WAAWvD,KAAKqsB,sBACpBjiB,EAAI7G,WAAWvD,KAAKssB,mBACbliB,EAAIxJ,SAGfymB,GAAY7e,GACZO,MAAO,SAAUjG,GAAV,GAEC0pB,GAAiBC,EAOjBC,CARJ5pB,GAAKG,OAAOjD,KAAKiD,QACbupB,EAAMxsB,KAAK2I,KAAM8jB,EAAOD,EAAIC,KAChCzsB,KAAK2sB,QAAU7pB,EAAKyB,MAAMkoB,EAAKlB,oBAAqB,WAChD,OACIqB,QAAS9pB,EAAKQ,YACdupB,IAAK/pB,EAAKW,gBAGdipB,EAAWF,EAAIM,KAAKrB,UAAYe,EAAIC,KAAKlB,oBAC7CvrB,KAAK+sB,iBAAmBjqB,EAAKyB,MAAMmoB,EAAU5pB,EAAKW,aAEtDupB,SAAU,SAAUphB,GAAV,GACF+gB,GAAU3sB,KAAK2sB,QACf/nB,EAAI+nB,EAAQjuB,MAChB,OAAIkN,GAAKhH,EACE+nB,EAAQ/gB,IAGfghB,QAASD,EAAQ/nB,EAAI,GAAGgoB,QACxBC,IAAK7sB,KAAK+sB,iBAAiBnhB,EAAKhH,KAGxC+F,OAAQ,SAAU4hB,GAAV,GAGK/tB,GACDkS,EAHJwY,EAASlpB,KACToK,EAAMrH,GACV,KAASvE,EAAI,EAAGA,EAAI+tB,EAAS7tB,SAAUF,EAC/BkS,EAAIwY,EAAO8D,SAAST,EAAS/tB,IACjC4L,EAAI7G,WAAWmN,EAAEkc,SACjBxiB,EAAI1G,YAAYgN,EAAEmc,IAEtB,OAAOziB,GAAIxJ,SAGf0mB,GAAY,WACZ,QAAS2F,GAAYjlB,GACjBhI,KAAKgI,IAAMA,EAaf,QAASklB,GAAcpqB,GAAvB,GAEQ0oB,GACAxT,EAEI0R,CADR,KAHA1pB,KAAKgI,IAAMlF,EACP0oB,EAAMxrB,KAAKusB,YACXvU,EAAUhY,KAAKmtB,eACN,CAIT,GAHIzD,EAAQ5mB,EAAKQ,YACjB0U,EAAQ7Z,KAAK2E,EAAKG,UAClBuoB,EAAIrtB,KAAK2E,EAAKQ,eACRomB,EAAQ0D,GACV;AAEJtqB,EAAK+C,KAAK6jB,EAAQ2D,EAAwB,EAAI,GAC1C3D,EAAQ4D,EACRxqB,EAAK+C,KAAK,GACH6jB,EAAQ6D,EACfzqB,EAAK+C,KAAK,GACH6jB,EAAQ8D,GACf1qB,EAAK+C,KAAK,IAhCV,GAURwnB,GACAG,EACAJ,EACAG,EACAD,CAmCJ,OA7CAL,GAAY1lB,WACRkmB,UAAU,EACV9iB,OAAQ,WACJ,MAAO3K,MAAKgI,IAAIpH,QAGpBysB,EAAwB,EACxBG,EAAkB,EAClBJ,EAAkB,GAClBG,EAA2B,GAC3BD,EAAuB,IAsB3BJ,EAAc3lB,WACVkmB,UAAU,EACV9iB,OAAQ,SAAU+iB,GAAV,GAGKlvB,GACDoN,EAHJsd,EAASlpB,KACToK,EAAMrH,EAAa/C,KAAKgI,IAAIpH,MAChC,KAASpC,EAAI,EAAGA,EAAIwB,KAAKusB,SAAS7tB,SAAUF,EACpCoN,EAAKsd,EAAOqD,SAAS/tB,GACzB4L,EAAInH,OAAOimB,EAAOiE,UAAU3uB,IAC5B4L,EAAI7G,WAAWmqB,EAAQ9hB,GAE3B,OAAOxB,GAAIxJ,QAGZ4H,GACHO,MAAO,WACH/I,KAAK2tB,UAETC,SAAU,SAAUhiB,GAAV,GAKFiiB,GACAnvB,EAIAoE,EACAG,EACA+E,EACA8lB,EACAhE,EACAC,EACAC,EACAC,EACA8D,EAjBAJ,EAAQ3tB,KAAK2tB,KACjB,OAAIvmB,GAAiBumB,EAAO/hB,GACjB+hB,EAAM/hB,IAEbiiB,EAAO7tB,KAAK2I,KAAKklB,KACjBnvB,EAASmvB,EAAKnD,SAAS9e,GACZ,IAAXlN,EACOivB,EAAM/hB,GAAM,MAEnB9I,EAAO9C,KAAK8I,QACZ7F,EAASjD,KAAKiD,OAAS4qB,EAAKpD,SAAS7e,GACrC5D,EAAMjF,EAAaD,EAAKmC,MAAMhC,EAAQvE,IACtCovB,EAAmB9lB,EAAIvE,aACvBqmB,EAAO9hB,EAAIvE,aACXsmB,EAAO/hB,EAAIvE,aACXumB,EAAOhiB,EAAIvE,aACXwmB,EAAOjiB,EAAIvE,aACXsqB,EAAQJ,EAAM/hB,GAAMkiB,MAAyB,GAAIZ,GAAcllB,GAAO,GAAIilB,GAAYjlB,GAC1F+lB,EAAMD,iBAAmBA,EACzBC,EAAMjE,KAAOA,EACbiE,EAAMhE,KAAOA,EACbgE,EAAM/D,KAAOA,EACb+D,EAAM9D,KAAOA,EACN8D,KAEXpjB,OAAQ,SAAUqjB,EAAQC,EAAQP,GAA1B,GAEKlvB,GACDoN,EACAmiB,EAHJ3jB,EAAMrH,IAAgBiV,IAC1B,KAASxZ,EAAI,EAAGA,EAAIyvB,EAAOvvB,SAAUF,EAC7BoN,EAAKqiB,EAAOzvB,GACZuvB,EAAQC,EAAOpiB,GACnBoM,EAAQ7Z,KAAKiM,EAAInH,UACb8qB,GACA3jB,EAAIpF,MAAM+oB,EAAMpjB,OAAO+iB,GAI/B,OADA1V,GAAQ7Z,KAAKiM,EAAInH,WAEb2lB,MAAOxe,EAAIxJ,MACXoX,QAASA,SAKrBuP,GAAY,WACZ,QAAS2G,GAAUxwB,EAAM6C,GACrBP,KAAKtC,KAAOA,EACZsC,KAAKtB,OAAShB,EAAKgB,OACnBsB,KAAKmuB,WAAa5tB,EAAM4tB,WACxBnuB,KAAKouB,mBAAqB7tB,EAAM6tB,mBAChCpuB,KAAKquB,WAAa9tB,EAAM8tB,WACxBruB,KAAKsuB,OAAS/tB,EAAM+tB,OAExB,MAAO9lB,IACHO,MAAO,SAAUjG,GAAV,GAGCya,GACAgR,EACAC,EAUAC,EACKjwB,EACDkwB,EAEAhxB,CAHR,KAfAoF,EAAKG,OAAOjD,KAAKiD,QACjBH,EAAKQ,YACDia,EAAQza,EAAKQ,YACbirB,EAAevuB,KAAKiD,OAASH,EAAKQ,YAClCkrB,EAAc1rB,EAAKyB,MAAMgZ,EAAO,WAChC,OACI4Q,WAAYrrB,EAAKQ,YACjB8qB,mBAAoBtrB,EAAKQ,YACzB+qB,WAAYvrB,EAAKQ,YACjBgrB,OAAQxrB,EAAKQ,YACb5E,OAAQoE,EAAKQ,YACbL,OAAQH,EAAKQ,YAAcirB,KAG/BE,EAAUzuB,KAAKyuB,WACVjwB,EAAI,EAAGA,EAAIgwB,EAAY9vB,SAAUF,EAClCkwB,EAAMF,EAAYhwB,GACtBsE,EAAKG,OAAOyrB,EAAIzrB,QACZvF,EAAOoF,EAAK0B,WAAWkqB,EAAIhwB,QAC1B+vB,EAAQC,EAAIJ,UACbG,EAAQC,EAAIJ,YAEhBG,EAAQC,EAAIJ,QAAQnwB,KAAK,GAAI+vB,GAAUxwB,EAAMgxB,GAEjD1uB,MAAK2uB,gBAAkBF,EAAQ,GAAG,GAClCzuB,KAAK4uB,eAAiB5uB,KAAK2uB,gBAAgBjxB,KAAKE,QAAQ,gBAAiB,KAE7E+M,OAAQ,SAAUd,GAAV,GAIKrL,GAKL4L,EACAykB,EAMQC,EACKC,EACDxwB,EAjBZ2qB,EAASlpB,KACTyuB,EAAUzuB,KAAKyuB,QACfO,EAAW,CACf,KAASxwB,IAAKiwB,GACNrnB,EAAiBqnB,EAASjwB,KAC1BwwB,GAAYP,EAAQjwB,GAAGE,OAG3B0L,GAAMrH,IACN8rB,EAAW9rB,IACfqH,EAAI7G,WAAW,GACf6G,EAAI7G,WAAWyrB,GACf5kB,EAAI7G,WAAW,EAAI,GAAKyrB,EACxB,KAAKxwB,IAAKiwB,GACN,GAAIrnB,EAAiBqnB,EAASjwB,GAE1B,IADIswB,EAAY,GAALtwB,GAAU,GAAI0vB,GAAUrkB,EAAQqf,EAAOyF,kBAAoBF,EAAQjwB,GACrEuwB,EAAI,EAAGA,EAAID,EAAKpwB,SAAUqwB,EAC3BxwB,EAAMuwB,EAAKC,GACf3kB,EAAI7G,WAAWhF,EAAI4vB,YACnB/jB,EAAI7G,WAAWhF,EAAI6vB,oBACnBhkB,EAAI7G,WAAWhF,EAAI8vB,YACnBjkB,EAAI7G,WAAWhF,EAAI+vB,QACnBlkB,EAAI7G,WAAWhF,EAAIG,QACnB0L,EAAI7G,WAAWsrB,EAAS5rB,UACxB4rB,EAASlqB,YAAYpG,EAAIb,KAKrC,OADA0M,GAAIpF,MAAM6pB,EAASjuB,OACZwJ,EAAIxJ,YAInB4mB,GAAY,WACZ,GAAIyH,GAAoB,4zDAA4zD3T,MAAM,OAC11D,OAAO9S,IACHO,MAAO,SAAUjG,GAAV,GAYCosB,GASIC,EApBJjG,EAASlpB,IAYb,QAXA8C,EAAKG,OAAOjD,KAAKiD,QACjBjD,KAAKuqB,OAASznB,EAAKa,WACnB3D,KAAKovB,YAActsB,EAAKqB,aACxBnE,KAAKqvB,kBAAoBvsB,EAAKW,aAC9BzD,KAAKsvB,mBAAqBxsB,EAAKW,aAC/BzD,KAAKuvB,aAAezsB,EAAKa,WACzB3D,KAAKwvB,aAAe1sB,EAAKa,WACzB3D,KAAKyvB,aAAe3sB,EAAKa,WACzB3D,KAAK0vB,YAAc5sB,EAAKa,WACxB3D,KAAK2vB,YAAc7sB,EAAKa,WAEhB3D,KAAKuqB,QACb,IAAK,OACL,IAAK,QACD,KACJ,KAAK,QAKD,IAJA2E,EAAiBpsB,EAAKQ,YACtBtD,KAAK4vB,eAAiB9sB,EAAKyB,MAAM2qB,EAAgBpsB,EAAKQ,WACtDtD,KAAK6vB,SACDV,EAAQnvB,KAAKiD,OAASjD,KAAKtB,OACxBoE,EAAKG,SAAWksB,GACnBjG,EAAO2G,MAAM1xB,KAAK2E,EAAK0B,WAAW1B,EAAKI,YAE3C,MACJ,KAAK,QACDgsB,EAAiBpsB,EAAKQ,YACtBtD,KAAKgY,QAAUlV,EAAKuB,KAAK6qB,EACzB,MACJ,KAAK,QACDlvB,KAAKM,IAAMwC,EAAKyB,MAAMvE,KAAK2I,KAAKmkB,KAAKrB,UAAW3oB,EAAKQ,aAI7DsqB,SAAU,SAAUkC,GAChB,OAAQ9vB,KAAKuqB,QACb,IAAK,OACD,MAAO0E,GAAkBa,IAAS,SACtC,KAAK,QACD,GAAIC,GAAQ/vB,KAAK4vB,eAAeE,EAChC,OAAIC,GAAQd,EAAkBvwB,OACnBuwB,EAAkBc,GAEtB/vB,KAAK6vB,MAAME,EAAQd,EAAkBvwB,SAAW,SAC3D,KAAK,QACL,IAAK,QACD,MAAO,SACX,KAAK,QACD,MAAOsB,MAAKM,IAAIwvB,IAAS,QAGjCnlB,OAAQ,SAAUqlB,GAAV,GAKA5lB,GAGA6lB,EACAxB,EACKjwB,EACDoN,EACAskB,EACAH,EAZJ7G,EAASlpB,IACb,IAAmB,QAAfA,KAAKuqB,OACL,MAAOvqB,MAAKgI,KAOhB,KALIoC,EAAMrH,EAAa/C,KAAK8I,QAAQ7D,MAAMjF,KAAKiD,OAAQ,KACvDmH,EAAIxG,UAAU,QACdwG,EAAInH,OAAO,IACPgtB,KACAxB,KACKjwB,EAAI,EAAGA,EAAIwxB,EAAQtxB,SAAUF,EAC9BoN,EAAKokB,EAAQxxB,GACb0xB,EAAOhH,EAAO0E,SAAShiB,GACvBmkB,EAAQd,EAAkB/jB,QAAQglB,GAClCH,GAAS,EACTE,EAAQ9xB,KAAK4xB,IAEbE,EAAQ9xB,KAAK8wB,EAAkBvwB,OAAS+vB,EAAQ/vB,QAChD+vB,EAAQtwB,KAAK+xB,GAIrB,KADA9lB,EAAI7G,WAAWysB,EAAQtxB,QAClBF,EAAI,EAAGA,EAAIyxB,EAAQvxB,SAAUF,EAC9B4L,EAAI7G,WAAW0sB,EAAQzxB,GAE3B,KAAKA,EAAI,EAAGA,EAAIiwB,EAAQ/vB,SAAUF,EAC9B4L,EAAIjH,UAAUsrB,EAAQjwB,GAAGE,QACzB0L,EAAIzF,YAAY8pB,EAAQjwB,GAE5B,OAAO4L,GAAIxJ,YAInB6mB,GAAY,WACZ,QAAS0I,GAAUrtB,EAAMG,EAAQmtB,GAC7B,GAAInmB,GAAOjK,IACXiK,GAAKkkB,WAAarrB,EAAKQ,YACvB2G,EAAKmkB,mBAAqBtrB,EAAKQ,YAC/B2G,EAAKhH,OAASA,EAASH,EAAKa,WAC5Bb,EAAKmD,cAAc,WAAA,GACX6pB,GAOStxB,EAOL6xB,EAEAC,EAEAC,EACAC,EACAC,EACAlT,EACAgP,EAEI7mB,EAAsB+U,EAElBiW,EAIIX,EAcZrxB,EASAiyB,EAGIC,EACAC,CAtDZ,QAFA/tB,EAAKG,OAAOgH,EAAKhH,QACjBgH,EAAKsgB,OAASznB,EAAKQ,aAEnB,IAAK,GAGD,IAFA2G,EAAKvL,OAASoE,EAAKQ,YACnB2G,EAAK6mB,SAAWhuB,EAAKQ,YACZ9E,EAAI,EAAGA,EAAI,MAAOA,EACvB4xB,EAAQ5xB,GAAKsE,EAAKI,UAEtB,MACJ,KAAK,GAYD,IAXA+G,EAAKvL,OAASoE,EAAKQ,YACnB2G,EAAK6mB,SAAWhuB,EAAKQ,YACjB+sB,EAAWvtB,EAAKQ,YAAc,EAClCR,EAAK+C,KAAK,GACNyqB,EAAUxtB,EAAKyB,MAAM8rB,EAAUvtB,EAAKQ,WACxCR,EAAK+C,KAAK,GACN0qB,EAAYztB,EAAKyB,MAAM8rB,EAAUvtB,EAAKQ,WACtCktB,EAAU1tB,EAAKyB,MAAM8rB,EAAUvtB,EAAKW,YACpCgtB,EAAgB3tB,EAAKyB,MAAM8rB,EAAUvtB,EAAKQ,WAC1Cia,GAAStT,EAAKvL,OAASuL,EAAKhH,OAASH,EAAKG,UAAY,EACtDspB,EAAWzpB,EAAKyB,MAAMgZ,EAAOza,EAAKQ,WACjC9E,EAAI,EAAGA,EAAI6xB,IAAY7xB,EAExB,IADIkH,EAAQ6qB,EAAU/xB,GAAIic,EAAM6V,EAAQ9xB,GACnCsxB,EAAOpqB,EAAOoqB,GAAQrV,IAAOqV,EAEL,IAArBW,EAAcjyB,GACdkyB,EAAUZ,EAAOU,EAAQhyB,IAErBuxB,EAAQU,EAAcjyB,GAAK,GAAK6xB,EAAW7xB,IAAMsxB,EAAOpqB,GAC5DgrB,EAAUnE,EAASwD,IAAU,EACb,IAAZW,IACAA,GAAWF,EAAQhyB,KAG3B4xB,EAAQN,GAAkB,MAAVY,CAGxB,MACJ,KAAK,GAKD,IAJAzmB,EAAKvL,OAASoE,EAAKQ,YACnB2G,EAAK6mB,SAAWhuB,EAAKQ,YACrBwsB,EAAOhtB,EAAKQ,YACR5E,EAASoE,EAAKQ,YACX5E,KAAW,GACd0xB,EAAQN,KAAUhtB,EAAKQ,WAE3B,MACJ,KAAK,IAKD,IAJAR,EAAKQ,YACL2G,EAAKvL,OAASoE,EAAKa,WACnBsG,EAAK6mB,SAAWhuB,EAAKa,WACjBgtB,EAAU7tB,EAAKa,WACZgtB,KAAY,GAIf,IAHAb,EAAOhtB,EAAKa,WACRitB,EAAc9tB,EAAKa,WACnBktB,EAAY/tB,EAAKa,WACdmsB,GAAQc,GACXR,EAAQN,KAAUe,GAG1B,MACJ,SACQpxB,OAAO+Q,SACP/Q,OAAO+Q,QAAQD,MAAM,0BAA4BtG,EAAKsgB,WAKtE,QAASwG,GAActnB,EAAWH,GAM9B,QAAS0nB,GAAQC,GACb,MAAO3nB,GAAUG,EAAUwnB,IAPnC,GASazyB,GACDsxB,EACAoB,EACAC,EAeJd,EACAe,EACAjpB,EACAC,EACAC,EACAgpB,EACAC,EACA/E,EAEIgE,EACAD,EAMAiB,EAISxC,EAQb3kB,EAtDAonB,EAAQ9pB,EAAW+B,GACnBgoB,KACAC,KACAC,EAAO,KACPC,EAAO,IAIX,KAASpzB,EAAI,EAAGA,EAAIgzB,EAAM9yB,SAAUF,EAC5BsxB,EAAO0B,EAAMhzB,GACb0yB,EAAMF,EAAQlB,GACdqB,EAAQD,EAAMpB,EACN,MAAR6B,GAAgBR,IAAUS,IACtBD,GACAD,EAASvzB,KAAKwzB,GAElBF,EAAWtzB,KAAK2xB,GAChB8B,EAAOT,GAEXQ,EAAO7B,CAeX,KAbI6B,GACAD,EAASvzB,KAAKwzB,GAElBD,EAASvzB,KAAK,OACdszB,EAAWtzB,KAAK,OACZkyB,EAAWoB,EAAW/yB,OACtB0yB,EAAwB,EAAXf,EACbloB,EAAc,EAAIlE,KAAKmlB,IAAI,EAAGnlB,KAAKolB,MAAMplB,KAAKwM,IAAI4f,GAAYpsB,KAAKqlB,MACnElhB,EAAgBnE,KAAKwM,IAAItI,EAAc,GAAKlE,KAAKqlB,IACjDjhB,EAAa+oB,EAAajpB,EAC1BkpB,KACAC,KACA/E,KACC/tB,EAAI,EAAGA,EAAI6xB,IAAY7xB,EAAG,CAG3B,GAFI+xB,EAAYkB,EAAWjzB,GACvB8xB,EAAUoB,EAASlzB,GACN,OAAb+xB,EAAoB,CACpBc,EAAOlzB,KAAK,GACZmzB,EAAanzB,KAAK,EAClB,OAGJ,GADIozB,EAAaP,EAAQT,GACrBA,EAAYgB,GAAc,MAG1B,IAFAF,EAAOlzB,KAAK,GACZmzB,EAAanzB,KAAK,GAAKouB,EAAS7tB,OAAS2xB,EAAW7xB,IAC3CuwB,EAAIwB,EAAWxB,GAAKuB,IAAWvB,EACpCxC,EAASpuB,KAAK6yB,EAAQjC,QAG1BsC,GAAOlzB,KAAKozB,EAAahB,GACzBe,EAAanzB,KAAK,GAoB1B,MAjBIiM,GAAMrH,IACVqH,EAAI7G,WAAW,GACf6G,EAAI7G,WAAW,GACf6G,EAAIxG,UAAU,IACdwG,EAAI7G,WAAW,GACf6G,EAAI7G,WAAW,GAAgB,EAAX8sB,EAAiC,EAAlB9D,EAAS7tB,QAC5C0L,EAAI7G,WAAW,GACf6G,EAAI7G,WAAW6tB,GACfhnB,EAAI7G,WAAW4E,GACfiC,EAAI7G,WAAW6E,GACfgC,EAAI7G,WAAW8E,GACfqpB,EAASzY,QAAQ7O,EAAI7G,YACrB6G,EAAI7G,WAAW,GACfkuB,EAAWxY,QAAQ7O,EAAI7G,YACvB8tB,EAAOpY,QAAQ7O,EAAI1G,aACnB4tB,EAAarY,QAAQ7O,EAAI7G,YACzBgpB,EAAStT,QAAQ7O,EAAI7G,YACd6G,EAAIxJ,MAEf,MAAO4H,IACHO,MAAO,SAAUjG,GAAV,GAMCoF,GALA+B,EAAOjK,KACPiD,EAASgH,EAAKhH,MAClBH,GAAKG,OAAOA,GACZgH,EAAKmmB,WACLnmB,EAAK+d,QAAUllB,EAAKQ,YAChB4E,EAAapF,EAAKQ,YACtB2G,EAAKlC,OAASjF,EAAKyB,MAAM2D,EAAY,WACjC,MAAO,IAAIioB,GAAUrtB,EAAMG,EAAQgH,EAAKmmB,YAGhDzlB,OAAQ,SAAUlB,EAAWH,GACzB,GAAIc,GAAMrH,GAIV,OAHAqH,GAAI7G,WAAW,GACf6G,EAAI7G,WAAW,GACf6G,EAAIpF,MAAM+rB,EAActnB,EAAWH,IAC5Bc,EAAIxJ,YAInB8mB,GAAWlf,GACXO,MAAO,SAAUjG,GACbA,EAAKG,OAAOjD,KAAKiD,QACjBjD,KAAKgoB,QAAUllB,EAAKQ,YACpBtD,KAAK6xB,iBAAmB/uB,EAAKW,aAC7BzD,KAAK8xB,YAAchvB,EAAKQ,YACxBtD,KAAK+xB,WAAajvB,EAAKQ,YACvBtD,KAAKyT,KAAO3Q,EAAKQ,YACjBtD,KAAKgyB,gBAAkBlvB,EAAKW,aAC5BzD,KAAKiyB,gBAAkBnvB,EAAKW,aAC5BzD,KAAKkyB,kBAAoBpvB,EAAKW,aAC9BzD,KAAKmyB,kBAAoBrvB,EAAKW,aAC9BzD,KAAKoyB,kBAAoBtvB,EAAKW,aAC9BzD,KAAKqyB,kBAAoBvvB,EAAKW,aAC9BzD,KAAKsyB,oBAAsBxvB,EAAKW,aAChCzD,KAAKuyB,oBAAsBzvB,EAAKW,aAChCzD,KAAKwyB,eAAiB1vB,EAAKW,aAC3BzD,KAAKyyB,mBAAqB3vB,EAAKW,aAC/BzD,KAAK0yB,YAAc5vB,EAAKW,aACxBzD,KAAK2yB,OAAS7vB,EAAKyB,MAAM,GAAIzB,EAAKI,UAClClD,KAAK4yB,UAAY9vB,EAAKyB,MAAM,EAAGzB,EAAKa,UACpC3D,KAAK6yB,SAAW/vB,EAAK0B,WAAW,GAChCxE,KAAK8yB,UAAYhwB,EAAKQ,YACtBtD,KAAK+yB,eAAiBjwB,EAAKQ,YAC3BtD,KAAKgzB,cAAgBlwB,EAAKQ,YACtBtD,KAAKgoB,QAAU,IACfhoB,KAAK4qB,OAAS9nB,EAAKW,aACnBzD,KAAK6qB,QAAU/nB,EAAKW,aACpBzD,KAAK8qB,QAAUhoB,EAAKW,aACpBzD,KAAKizB,UAAYnwB,EAAKQ,YACtBtD,KAAKkzB,WAAapwB,EAAKQ,YACvBtD,KAAKmzB,cAAgBrwB,EAAKyB,MAAM,EAAGzB,EAAKa,UACpC3D,KAAKgoB,QAAU,IACfhoB,KAAKozB,QAAUtwB,EAAKQ,YACpBtD,KAAKqzB,UAAYvwB,EAAKQ,YACtBtD,KAAKszB,YAAcxwB,EAAKQ,YACxBtD,KAAKuzB,UAAYzwB,EAAKQ,YACtBtD,KAAKwzB,WAAa1wB,EAAKQ,eAInCqH,OAAQ,WACJ,MAAO3K,MAAKgI,SAGhBiB,GAAY,IAoBhBC,EAAQ3B,WACJksB,IAAK,SAAUrf,GAAV,GAOG0b,GAKI4D,EAIQ1C,EAfZ/mB,EAAOjK,IACX,OAAiB,gBAANoU,GACA5N,EAAW4N,GAAIuf,OAAO,SAAU7uB,EAAKgrB,GACxC,MAAOhrB,GAAMnH,OAAO8G,aAAawF,EAAKwpB,IAAI3D,KAC3C,KAEHA,EAAO7lB,EAAKZ,SAAS+K,GACpB0b,IACDA,EAAO7lB,EAAKP,OACZO,EAAKb,OAAO0mB,GAAQ1b,EACpBnK,EAAKZ,SAAS+K,GAAM0b,EAChB4D,EAAUzpB,EAAKd,KAAKyqB,KAAKxD,QAAQhc,GACjCsf,IACAzpB,EAAKR,UAAUqmB,GAAQ4D,EACQ,MAA3BzpB,EAAKX,UAAUoqB,KACX1C,EAAU/mB,EAAKL,UACnBK,EAAKX,UAAUoqB,GAAW1C,EAC1B/mB,EAAKT,UAAUwnB,GAAW0C,KAI/B5D,IAEX+D,WAAY,SAAUn2B,GAClB,MAAOsC,MAAKyzB,IAAI/1B,IAEpB6uB,SAAU,WACN,MAAO7kB,GAAW1H,KAAKsJ,YAE3BwqB,UAAW,SAAUvH,EAAUtlB,GAApB,GAKEzI,GACDoN,EAEImiB,EAPR7E,EAASlpB,IAIb,KAHKiH,IACDA,MAEKzI,EAAI,EAAGA,EAAI+tB,EAAS7tB,SAAUF,EAC/BoN,EAAK2gB,EAAS/tB,GACbyI,EAAO2E,KACJmiB,EAAQ9mB,EAAO2E,GAAMsd,EAAO/f,KAAK4qB,KAAKnG,SAAShiB,GAC/CmiB,GAASA,EAAMN,UACfvE,EAAO4K,UAAU/F,EAAMxB,SAAUtlB,GAI7C,OAAOA,IAEX0D,OAAQ,WAAA,GAGK+oB,GAIO1C,EAMZgD,EACAC,EAGA9qB,EACA4qB,EACAlG,EAEA9lB,EApBAmhB,EAASlpB,KACTguB,EAAShuB,KAAK8zB,UAAU9zB,KAAKusB,WACjC,KAASmH,IAAW1F,GACZ5mB,EAAiB4mB,EAAQ0F,KACzBA,EAAU1Y,SAAS0Y,EAAS,IACK,MAA7BxK,EAAO5f,UAAUoqB,KACb1C,EAAU9H,EAAOtf,UACrBsf,EAAO5f,UAAUoqB,GAAW1C,EAC5B9H,EAAO1f,UAAUwnB,GAAW0C,GAwBxC,OApBIM,GAActsB,EAAW1H,KAAKwJ,WAC9ByqB,EAAcD,EAAY1zB,IAAI,SAAUsL,GACxC,MAAO5L,MAAKwJ,UAAUoC,IACvB5L,MACCmJ,EAAOnJ,KAAKmJ,KACZ4qB,EAAO5qB,EAAK4qB,KAAKppB,OAAOqjB,EAAQiG,EAAaj0B,KAAKsJ,WAClDukB,EAAO1kB,EAAK0kB,KAAKljB,OAAOopB,EAAK/b,SACjChY,KAAKk0B,SAAWl0B,KAAK0J,KAAO,EACxB3B,GACA6rB,KAAQnM,GAAU9c,OAAO3K,KAAKyJ,UAAWzJ,KAAKsJ,WAC9CyqB,KAAQA,EAAKnL,MACbiF,KAAQA,EAAKjF,MACbuL,KAAQhrB,EAAKgrB,KAAKxpB,OAAOspB,GACzBxH,KAAQtjB,EAAKsjB,KAAK9hB,OAAOspB,GACzBnH,KAAQ3jB,EAAK2jB,KAAKniB,OAAOspB,GACzB/D,KAAQ/mB,EAAK+mB,KAAKvlB,OAAOspB,GACzBlqB,KAAQZ,EAAKY,KAAKY,OAAO3K,KAAK6J,QAC9B2gB,KAAQrhB,EAAKqhB,KAAK7f,OAAOkjB,EAAKtD,QAC9B6J,OAAQjrB,EAAKkrB,IAAI1pB,UAEd3K,KAAKmJ,KAAKmrB,UAAU3pB,OAAO5C,IAEtCwsB,YAAa,WAAA,GAGAC,GAKDd,EAEI1C,EATR9H,EAASlpB,KACToK,EAAMrH,IAAgBuB,EAAM,CAChC,KAASkwB,EAAMx0B,KAAK2J,UAAW6qB,EAAMx0B,KAAK0J,OAAQ8qB,EAAK,CACnD,KAAOlwB,EAAMkwB,GACTpqB,EAAI7G,WAAW,GACfe,GAEAovB,GAAUxK,EAAOzf,UAAU+qB,GAC3Bd,GACI1C,EAAU9H,EAAO5f,UAAUoqB,GAC/BtpB,EAAI7G,WAAWytB,IAEf5mB,EAAI7G,WAAW,GAEnBe,IAEJ,MAAO8F,GAAIxJ,QA8BnBkJ,EAAQvC,WACJwB,MAAO,WACH,GAAIyjB,GAAMxsB,KAAKs0B,UAAY,GAAIxsB,GAAU9H,KAAKkK,SAC9ClK,MAAKwqB,KAAOgC,EAAI7D,UAAU,OAAQ1B,IAClCjnB,KAAK6tB,KAAOrB,EAAI7D,UAAU,OAAQzB,IAClClnB,KAAKysB,KAAOD,EAAI7D,UAAU,OAAQxB,IAClCnnB,KAAK8sB,KAAON,EAAI7D,UAAU,OAAQvB,IAClCpnB,KAAKm0B,KAAO3H,EAAI7D,UAAU,OAAQtB,IAClCrnB,KAAK+zB,KAAOvH,EAAI7D,UAAU,OAAQrB,IAClCtnB,KAAK+J,KAAOyiB,EAAI7D,UAAU,OAAQpB,IAClCvnB,KAAKkwB,KAAO1D,EAAI7D,UAAU,OAAQnB,IAClCxnB,KAAK4zB,KAAOpH,EAAI7D,UAAU,OAAQlB,IAClCznB,KAAKq0B,IAAM7H,EAAI7D,UAAU,OAAQjB,IACjC1nB,KAAK6J,OAAS7J,KAAK+J,KAAK6kB,eACxB5uB,KAAK4qB,OAAS5qB,KAAKq0B,IAAIzJ,QAAU5qB,KAAKysB,KAAK7B,OAC3C5qB,KAAK6qB,QAAU7qB,KAAKq0B,IAAIxJ,SAAW7qB,KAAKysB,KAAK5B,QAC7C7qB,KAAK8qB,QAAU9qB,KAAKq0B,IAAIvJ,SAAW9qB,KAAKysB,KAAK3B,QAC7C9qB,KAAKy0B,MAAQ,IAAOz0B,KAAKwqB,KAAKb,YAElC+K,aAAc,SAAU3G,GACpB,MAAO/tB,MAAKm0B,KAAKnH,SAASe,GAAOnB,QAAU5sB,KAAKy0B,OAEpDE,WAAY,WACR,MAAO,IAAIzrB,GAAQlJ,QAGvB4Q,GAAUlR,EAAMqnB,QAAQnW,QACxBnF,GAAK,KACL8K,GAAmB,EACnBnK,IACA+N,IACI,QACA,SAEJ5X,IACI,QACA,SAEJC,IACI,QACA,SAEJC,IACI,OACA,SAEJ4J,IACI,OACA,QAEJuoB,IACI,OACA,QAEJC,IACI,OACA,QAEJC,IACI,OACA,QAEJC,IACI,MACA,QAEJC,IACI,OACA,OAEJC,KACI,KACA,QAEJC,IACI,QACA,SAEJpe,IACI,QACA,SAEJG,IACI,QACA,SAEJke,IACI,QACA,SAEJC,IACI,OACA,SAEJC,IACI,MACA,QAEJC,IACI,OACA,OAEJC,IACI,OACA,QAEJC,IACI,OACA,QAEJC,IACI,OACA,QAEJC,KACI,MACA,QAEJC,IACI,QACA,SAEJ7Z,IACI,QACA,SAEJI,IACI,QACA,SAEJ0Z,IACI,OACA,SAEJC,IACI,OACA,QAEJC,IACI,OACA,QAEJC,IACI,OACA,QAEJC,IACI,OACA,QAEJC,IACI,OACA,QAEJC,IACI,OACA,QAEJC,KACI,MACA,QAEJC,WACI,OACA,KAEJC,OACI,IACA,KAEJC,OACI,IACA,MAEJC,QACI,IACA,KAEJC,SACI,IACA,OAsPJ5kB,IACA6kB,eAAe,EACfC,cAAc,EACdC,gBAAgB,EAChBC,oBAAoB,EACpBC,WAAa,EACbC,kBAAkB,EAClBC,qBAAqB,EACrBC,yBAAyB,EACzBC,SAAW,EACXC,gBAAgB,EAChBC,mBAAmB,EACnBC,uBAAuB,EACvBC,QAAU,EACVC,cAAgB,GAqDhBxlB,MA8HA2M,GAAYzK,EAAWrC,GACvB+M,GAAa,SAAUR,EAAQ/J,GAK/B,QAASzK,KACO,MAAN9E,GACFuP,IANR,GAAID,GAAO5M,OAAOK,KAAKuW,GAAStZ,EAAIsP,EAAKxV,MACzC,OAAU,KAANkG,EACOuP,QAOXD,GAAK+E,QAAQ,SAAU5I,GACnB0B,EAAU1B,EAAK6N,EAAO7N,GAAM3G,MAGpCmD,EAAYtF,WACRkX,UAAWA,GACXC,WAAYA,GACZ6Y,QAAS,SAAUlnB,GACf,GAAIlH,GAAOnJ,KAAKuN,MAAM8C,EACtB,KAAKlH,EAAM,CAEP,GADAA,EAAOyI,GAAWvB,IACblH,EACD,KAAUnD,OAAM,QAAUqK,EAAM,uBAGhClH,GAAOnJ,KAAKqN,OADZlE,KAAS,EACU,GAAIye,IAAgBvX,GAEpB,GAAIwX,IAAQ7nB,KAAMmJ,IAEzCnJ,KAAKuN,MAAM8C,GAAOlH,EAEtB,MAAOA,IAEXquB,SAAU,SAAUnnB,GAChB,GAAI4B,GAAMjS,KAAKwN,OAAO6C,EACtB,KAAK4B,EAAK,CAEN,GADAA,EAAMH,GAAYzB,IACb4B,EACD,KAAUjM,OAAM,SAAWqK,EAAM,uBAErC,IAAY,UAAR4B,EACA,MAAO,KAEXA,GAAMjS,KAAKwN,OAAO6C,GAAOrQ,KAAKqN,OAAO4E,EAAIoE,SAASrW,OAEtD,MAAOiS,IAEXwlB,aAAc,SAAUle,EAASme,GAAnB,GAIN/J,GACAgK,EAEItpB,EANJzC,EAAK/D,WAAW0R,GAAStO,QAAQ,EAgBrC,OAfAsO,GAAU1R,WAAW+D,GACrBA,GAAM8rB,EAAY,IAAM,IACpB/J,EAAQ3tB,KAAK43B,kBAAoB53B,KAAK43B,oBACtCD,EAAKhK,EAAM/hB,GACV+rB,IACGtpB,GAAUwH,KAAM1H,EAAE,cAClBupB,EACArpB,EAAMoL,GAAKF,EAEXlL,EAAMqL,GAAKH,EAEfoe,EAAK33B,KAAKqN,OAAO,GAAIjC,IAAciD,IACnCspB,EAAGrhB,cAAgBnI,EAAE,QAASoI,IAC9BoX,EAAM/hB,GAAM+rB,GAETA,GAEXE,KAAM,SAAUxpB,GACZ,MAAO,IAAIjD,IAAciD,IAE7BtE,KAAM,SAAUxL,GACZ,MAAO4P,GAAE5P,IAEb2G,OAAQ,SAAUmJ,EAAOW,GACrB,MAAO,IAAIK,IAAUL,EAASX,KAelCzD,GAAU7F,MAAM6F,SAAW,SAAUvD,GACrC,MAAOA,aAAetC,QA4D1B0F,EAASlD,UAAUmD,aAAe,aAc9BuD,GAAYkH,EAAS,SAAmB9U,GACxCL,KAAKK,MAAQA,IAEbsK,OAAQ,SAAUP,GAAV,GAEK5L,GADLs5B,EAAM,GAAIC,EAAM/3B,KAAKK,KACzB,KAAS7B,EAAI,EAAGA,EAAIu5B,EAAIr5B,SAAUF,EAC9Bs5B,GAAOn6B,OAAO8G,aAAiC,IAApBszB,EAAIp5B,WAAWH,GAE9C4L,GAAI,IAAK0tB,EAAIl6B,QAAQ,cAAe,QAAS,MAEjDmI,SAAU,WACN,MAAO/F,MAAKK,SAGhBsnB,GAAexS,EAAS,SAAsB9U,GAC9CL,KAAKK,MAAQA,IAEbsK,OAAQ,SAAUP,GAAV,GAGK5L,GAFL0qB,EAASlpB,IAEb,KADAoK,EAAI,KACK5L,EAAI,EAAGA,EAAIwB,KAAKK,MAAM3B,SAAUF,EACrC4L,EAAI4F,EAAQkZ,EAAO7oB,MAAM1B,WAAWH,GAAGuH,SAAS,IAAK,GAEzDqE,GAAI,OAET6D,IACCsH,GAAUJ,EAAS,SAAiBpL,GACpC/J,KAAK+J,KAAOA,IAEZY,OAAQ,SAAUP,GACdA,EAAI,IAAMpK,KAAKg4B,WAEnBA,OAAQ,WACJ,MAAOh4B,MAAK+J,KAAKnM,QAAQ,gBAAiB,SAAUqiB,GAChD,MAAO,IAAMjQ,EAAQiQ,EAAEthB,WAAW,GAAGoH,SAAS,IAAK,MAG3DA,SAAU,WACN,MAAO/F,MAAK+J,QAGhBuL,MACJC,GAAQ3U,IAAMuN,EAOV/C,GAAgB+J,EAAS,SAAuB9G,GAChDrO,KAAKqO,MAAQA,IAEb1D,OAAQ,SAAUP,GACd,GAAIiE,GAAQrO,KAAKqO,MAAO4pB,GAAQ,CAChC7tB,GAAI,MACJA,EAAImB,WAAW,WACX,IAAK,GAAI/M,KAAK6P,GACN7G,EAAe6G,EAAO7P,KAAO,KAAK2M,KAAK3M,KACvCy5B,GAAQ,EACR7tB,EAAIoB,OAAO2C,EAAE3P,GAAI,IAAK6P,EAAM7P,OAInCy5B,GACD7tB,EAAIoB,SAERpB,EAAI,SAGRiF,GAAY8F,EAAS,SAAmBrS,EAAMuL,EAAO6pB,GACrD,GAAmB,gBAARp1B,GAAkB,CACzB,GAAIuC,GAAMtC,GACVsC,GAAIL,MAAMlC,GACVA,EAAOuC,EAEXrF,KAAK8C,KAAOA,EACZ9C,KAAKqO,MAAQA,MACbrO,KAAKk4B,SAAWA,IAEhBvtB,OAAQ,SAAUP,GACd,GAAItH,GAAO9C,KAAK8C,KAAKlC,MAAOyN,EAAQrO,KAAKqO,KACrCrO,MAAKk4B,UAAYlR,GAASrkB,oBACrB0L,EAAM6H,OAEE7H,EAAM6H,iBAAkBnR,SACjCsJ,EAAM6H,QAAU7H,EAAM6H,SAFtB7H,EAAM6H,UAIV7H,EAAM6H,OAAO6M,QAAQ5U,EAAE,gBACvBrL,EAAOkkB,GAASnkB,QAAQC,IAE5BuL,EAAM8pB,OAASr1B,EAAKpE,OACpB0L,EAAI,GAAIgB,IAAciD,GAAQ,UAAW5C,IACzCrB,EAAIkB,UAAUxI,GACdsH,EAAIqB,GAAI,gBAGZoC,GAAasH,EAAS,WACtBnV,KAAKqO,OAAUwH,KAAM1H,EAAE,cAEvBG,SAAU,SAAU8pB,GAChBp4B,KAAKqO,MAAMgqB,MAAQD,IAExBhtB,IACC0C,GAAcqH,EAAS,WACvBnV,KAAKqO,OACDwH,KAAM1H,EAAE,SACRmqB,QACAC,MAAO,KAGXxpB,QAAS,SAAUypB,GACfx4B,KAAKqO,MAAMiqB,KAAKn6B,KAAKq6B,GACrBx4B,KAAKqO,MAAMkqB,UAEhBntB,IACCwK,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAuFAgS,GAAkBzS,EAAS,SAAyBpL,GACpD/J,KAAKqO,OACDwH,KAAM1H,EAAE,QACR2H,QAAS3H,EAAE,SACXsqB,SAAUtqB,EAAEpE,IAEhB/J,KAAKsW,cAAgBnI,EAAE,OAAQoI,MAE/Bsd,WAAY,SAAUt1B,GAClB,MAAO,IAAI0P,IAAiB1P,EAAPZ,MAE1ByN,IACCyc,GAAU1S,EAAS,SAAiBzS,EAAKyG,EAAMkF,GAA5B,GASfmc,GAEAiK,CAVJpmB,GAAQrO,KAAKqO,MAAQA,MACrBA,EAAMwH,KAAO1H,EAAE,QACfE,EAAMyH,QAAU3H,EAAE,SAClBE,EAAMqqB,SAAWvqB,EAAE,cACnBnO,KAAK24B,KAAOj2B,EACZ1C,KAAK44B,MAAQzvB,EACbnJ,KAAK64B,KAAO1vB,EAAKwrB,aACjB30B,KAAKsW,cAAgBnI,EAAE,OAAQoI,IAC3BiU,EAAOrhB,EAAKqhB,KAChBxqB,KAAK+J,KAAOZ,EAAKU,OACb4qB,EAAQz0B,KAAKy0B,MAAQtrB,EAAKsrB,MAC9Bz0B,KAAK6c,MACD2N,EAAKV,KAAO2K,EACZjK,EAAKT,KAAO0K,EACZjK,EAAKR,KAAOyK,EACZjK,EAAKP,KAAOwK,GAEhBz0B,KAAKovB,YAAcjmB,EAAK+mB,KAAKd,YAC7BpvB,KAAK4qB,OAASzhB,EAAKyhB,OAAS6J,EAC5Bz0B,KAAK6qB,QAAU1hB,EAAK0hB,QAAU4J,EAC9Bz0B,KAAK8qB,QAAU3hB,EAAK2hB,QAAU2J,EAC9Bz0B,KAAKqzB,UAAYlqB,EAAKkrB,IAAIhB,WAAarzB,KAAK4qB,OAC5C5qB,KAAKozB,QAAUjqB,EAAKkrB,IAAIjB,SAAW,EACnCpzB,KAAK84B,MAAQ,EACb94B,KAAK0yB,aAAevpB,EAAKkrB,IAAI3B,aAAe,IAAM,EAClD1yB,KAAK+4B,QAAU/4B,KAAK0yB,aAAe,GAAK1yB,KAAK0yB,aAAe,EAC5D1yB,KAAKg5B,SAA+B,IAApBh5B,KAAK0yB,YACrB1yB,KAAK0pB,OAASvgB,EAAK+mB,KAAKX,aAAe,EAAI,IAAMvvB,KAAK+4B,QAAU,EAAS,IAAM/4B,KAAKg5B,SAAW,EAAS,IAA2B,IAArBh5B,KAAKovB,YAAoB,GAAS,GAAK,KAErJyE,WAAY,SAAUn2B,GAClB,MAAO,IAAIiqB,IAAa3nB,KAAK64B,KAAKhF,WAAkBn2B,EAAPC,MAEjDs7B,aAAc,SAAUne,EAAUpd,GAApB,GAGDc,GACDkyB,EAHJxH,EAASlpB,KACTnB,EAAQ,EAAGuxB,EAAUpwB,KAAK44B,MAAMhF,KAAKxD,OACzC,KAAS5xB,EAAI,EAAGA,EAAId,EAAKgB,SAAUF,EAC3BkyB,EAAUN,EAAQ1yB,EAAKiB,WAAWH,IACtCK,GAASqqB,EAAO0P,MAAMlE,aAAahE,GAAW,EAElD,OAAO7xB,GAAQic,EAAW,KAE9BpQ,aAAc,WAAA,GAoCNwuB,GAgBArB,EAGAsB,EACAC,EAvDAnvB,EAAOjK,KACPq5B,EAAMpvB,EAAK4uB,KACX/1B,EAAOu2B,EAAI1uB,SACX2uB,EAAa,GAAIjqB,IAAUtM,EAAaD,IAASy2B,QAASz2B,EAAKpE,UAAU,IACzE86B,EAAavvB,EAAK0uB,KAAKtrB,OAAO,GAAIjC,KAClCyK,KAAM1H,EAAE,kBACRsrB,SAAUtrB,EAAElE,EAAK4uB,KAAKhvB,QACtB6vB,SAAUzvB,EAAK4S,KACf8c,MAAO1vB,EAAKyf,MACZkQ,MAAO3vB,EAAK6uB,MACZe,YAAa5vB,EAAKmlB,YAClB0K,OAAQ7vB,EAAK2gB,OACbmP,QAAS9vB,EAAK4gB,QACdmP,UAAW/vB,EAAKopB,UAChB4G,QAAShwB,EAAKmpB,QACd8G,UAAWjwB,EAAK0uB,KAAKtrB,OAAOisB,MAE5B1F,EAAOyF,EAAI5vB,UACXE,EAAY0vB,EAAI1vB,UAChBuqB,EAAWmF,EAAInF,SACfiG,MACH,QAASC,GAAK57B,EAAG67B,GACd,GAAI77B,GAAK01B,EAAU,CACf,GAAIhD,GAAM0C,EAAKp1B,EACJ,OAAP0yB,EACAkJ,EAAK57B,EAAI,IAEJ67B,GACDF,EAAWh8B,KAAKK,EAAG67B,MAEvBA,EAAMl8B,KAAK8L,EAAK2uB,MAAMlE,aAAaxD,IACnCkJ,EAAK57B,EAAI,EAAG67B,MAGtB1wB,GACEuvB,EAAa,GAAI9tB,KACjByK,KAAM1H,EAAE,QACR2H,QAAS3H,EAAE,gBACXsqB,SAAUtqB,EAAElE,EAAK4uB,KAAKhvB,QACtBywB,cAAe,GAAIlvB,KACfmvB,SAAU,GAAItsB,IAAU,SACxBusB,SAAU,GAAIvsB,IAAU,YACxBwsB,WAAY,IAEhBC,eAAgBlB,EAChBmB,UAAWhxB,EACXixB,SAAU1G,EACV2G,GAAI52B,KAAKC,MAAM+F,EAAK2uB,MAAMlE,aAAa,IACvCoG,EAAGX,EACHY,YAAa9wB,EAAK0uB,KAAKtrB,OAAOpD,EAAK+wB,sBAEnCnD,EAAO5tB,EAAKoE,MAChBwpB,EAAKY,SAAWtqB,EAAElE,EAAK4uB,KAAKhvB,QAC5BguB,EAAKoD,iBAAmBhxB,EAAK0uB,KAAKtrB,OAAO6rB,IACrCC,EAAS,GAAIrR,IAAiBne,EAAWuqB,EAAUmF,EAAIjwB,QACvDgwB,EAAe,GAAI/pB,IAAUlF,IAAc,OAAM,IACrDivB,EAAat2B,KAAKq2B,GAClBtB,EAAKqD,UAAYjxB,EAAK0uB,KAAKtrB,OAAO+rB,IAEtC4B,iBAAkB,WACd,MAAO,IAAI3rB,IAAUtM,EAAa/C,KAAK64B,KAAKtE,eAAgB,OAAM,MAEvEnpB,IACC0c,GAAmB3S,EAAS,SAAwBxL,EAAWuqB,EAAU5zB,GACzEN,KAAK2J,UAAYA,EACjB3J,KAAKk0B,SAAWA,EAChBl0B,KAAKM,IAAMA,IAEXqK,OAAQ,SAAUP,GACdA,EAAIoB,OAAO,wCACXpB,EAAIoB,OAAO,iBACXpB,EAAIoB,OAAO,aACXpB,EAAIoB,OAAO,qBACXpB,EAAIoB,OAAO,uBACXpB,EAAIoB,OAAO,qBACXpB,EAAIoB,OAAO,mBACXpB,EAAIoB,OAAO,UACXpB,EAAIoB,OAAO,qCACXpB,EAAIoB,OAAO,mBACXpB,EAAIoB,OAAO,yBACXpB,EAAIoB,OAAO,kBACXpB,EAAIoB,OAAO,oBACX,IAAIvB,GAAOjK,IACXoK,GAAIoB,OAAOvB,EAAKiqB,SAAWjqB,EAAKN,UAAY,EAAG,gBAC/CS,EAAImB,WAAW,WAAA,GACFukB,GACDqL,EACA58B,EAEKC,CAJb,KAASsxB,EAAO7lB,EAAKN,UAAWmmB,GAAQ7lB,EAAKiqB,WAAYpE,EAAM,CAI3D,IAHIqL,EAAUlxB,EAAK3J,IAAIwvB,GACnBvxB,EAAMsI,GAAYs0B,IACtB/wB,EAAIoB,OAAO,IAAKwE,EAAQ8f,EAAK/pB,SAAS,IAAK,GAAI,IAAK,KAC3CvH,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9B4L,EAAI4F,EAAQzR,EAAII,WAAWH,GAAGuH,SAAS,IAAK,GAEhDqE,GAAI,QAGZA,EAAIoB,OAAO,aACXpB,EAAIoB,OAAO,WACXpB,EAAIoB,OAAO,iDACXpB,EAAIoB,OAAO,OACXpB,EAAIoB,OAAO,UA+PfiE,GAAU0F,EAAS,SAAiBzS,EAAK2L,GACzCrO,KAAK24B,KAAOj2B,EACZ1C,KAAKo7B,QAAU,EACfp7B,KAAKq7B,WAAY,EACjBr7B,KAAKs7B,kBACLt7B,KAAKu7B,gBACLv7B,KAAKw7B,eACLx7B,KAAKy7B,iBACLz7B,KAAK07B,gBACL17B,KAAK27B,SAAW,EAChB37B,KAAK47B,SACD,EACA,EACA,EACA,EACA,EACA,GAEJ57B,KAAK67B,gBACL77B,KAAK44B,MAAQ,KACb54B,KAAK87B,UAAY,KACjB97B,KAAK+7B,iBACL1tB,EAAQrO,KAAKqO,MAAQA,MACrBA,EAAMwH,KAAO1H,EAAE,QACfE,EAAM2tB,SACF7tB,EAAE,OACFA,EAAE,QACFA,EAAE,UACFA,EAAE,UACFA,EAAE,WAENE,EAAM4L,UAAY,GAAI7O,KAClB6wB,KAAM,GAAI7wB,IAAcpL,KAAKs7B,gBAC7BphB,UAAW,GAAI9O,IAAcpL,KAAKu7B,cAClCW,QAAS,GAAI9wB,IAAcpL,KAAKw7B,aAChCW,QAAS,GAAI/wB,IAAcpL,KAAKy7B,eAChCrhB,QAAS,GAAIhP,IAAcpL,KAAK07B,gBAEpCrtB,EAAM+tB,OAASp8B,KAAK67B,eAEpBQ,KAAM,WACFr8B,KAAK0P,SAAS5M,KAAK4B,MAAM,KAAM6F,YAEnCoF,UAAW,SAAU/H,EAAGxE,EAAG6c,EAAGC,EAAGC,EAAG7iB,GAC3Bgf,EAAiB/R,aAClBvK,KAAK47B,QAAU/f,EAAKtR,UAAWvK,KAAK47B,SACpC57B,KAAKq8B,KAAKz0B,EAAG,IAAKxE,EAAG,IAAK6c,EAAG,IAAKC,EAAG,IAAKC,EAAG,IAAK7iB,EAAG,OACrD0C,KAAKq8B,KAAK5wB,MAGlBmE,UAAW,SAAU0sB,EAAIC,GACrBv8B,KAAK2P,UAAU,EAAG,EAAG,EAAG,EAAG2sB,EAAIC,IAEnC9H,MAAO,SAAU+H,EAAIC,GACjBz8B,KAAK2P,UAAU6sB,EAAI,EAAG,EAAGC,EAAI,EAAG,IAEpCC,OAAQ,SAAUC,GACd,GAAIC,GAAM34B,KAAK24B,IAAID,GAAQE,EAAM54B,KAAK44B,IAAIF,EAC1C38B,MAAK2P,UAAUitB,EAAKC,GAAMA,EAAKD,EAAK,EAAG,IAE3ChY,UAAW,WACP5kB,KAAKq7B,WAAY,EACjBr7B,KAAKq8B,KAAK,KAAM5wB,KAEpBwZ,QAAS,WACLjlB,KAAKq7B,WAAY,EACjBr7B,KAAKq8B,KAAK,KAAM5wB,KAEpBqxB,iBAAkB,WACd,IAAK98B,KAAKq7B,UACN,KAAUr1B,OAAM,oDAGxB+2B,aAAc,WACV,IAAK/8B,KAAK44B,MACN,KAAU5yB,OAAM,gDAGxB6e,QAAS,SAAU1b,EAAMpJ,GACrBC,KAAK88B,mBACO,MAAR3zB,EACAA,EAAOnJ,KAAK44B,MACHzvB,YAAgB0e,MACzB1e,EAAOnJ,KAAK24B,KAAKpB,QAAQpuB,IAEjB,MAARpJ,IACAA,EAAOC,KAAK87B,WAEhB97B,KAAKs7B,eAAenyB,EAAKmN,eAAiBnN,EAC1CnJ,KAAK44B,MAAQzvB,EACbnJ,KAAK87B,UAAY/7B,EACjBC,KAAKq8B,KAAKlzB,EAAKmN,cAAe,IAAKvW,EAAM,MAAO0L,KAEpDuxB,eAAgB,SAAUj9B,GACtBC,KAAK88B,mBACL98B,KAAKq8B,KAAKt8B,EAAM,MAAO0L,KAE3BqZ,qBAAsB,SAAUN,GAC5BxkB,KAAK88B,mBACL98B,KAAKq8B,KAAK7X,EAAM,MAAO/Y,KAE3BsZ,SAAU,SAAUrnB,EAAMu/B,GAAhB,GAGEC,GACAzI,CAHRz0B,MAAK+8B,eACDr/B,EAAKgB,OAAS,GAAKu+B,GAAkBj9B,KAAK44B,gBAAiB/Q,MACvDqV,EAAcl9B,KAAK44B,MAAMK,aAAaj5B,KAAK87B,UAAWp+B,GACtD+2B,EAAQwI,EAAiBC,EAAc,IAC3Cl9B,KAAKq8B,KAAK5H,EAAO,SAErBz0B,KAAKq8B,KAAKr8B,KAAK44B,MAAM/E,WAAWn2B,GAAO,MAAO+N,KAElD0xB,WAAY,SAAUz/B,GAClBsC,KAAK+8B,eACL/8B,KAAKq8B,KAAKr8B,KAAK44B,MAAM/E,WAAWn2B,GAAO,KAAO+N,KAElD0Z,QAAS,SAAUiY,EAAK57B,GAAf,GACD67B,GAAKr9B,KAAKs9B,SACV33B,EAAGnE,EAAIiL,KACP+N,EAAGhZ,EAAIoL,SAEP2wB,EAAKv9B,KAAKs9B,SACV33B,EAAGnE,EAAImL,MACP6N,EAAGhZ,EAAIkL,KAEX1M,MAAK67B,aAAa19B,KAAK,GAAIiN,KACvByK,KAAM1H,EAAE,SACR2H,QAAS3H,EAAE,QACX4S,MACIsc,EAAG13B,EACH03B,EAAG7iB,EACH+iB,EAAG53B,EACH43B,EAAG/iB,GAEPgjB,QACI,EACA,EACA,GAEJC,EAAG,GAAIryB,KACHyK,KAAM1H,EAAE,UACRD,EAAGC,EAAE,OACLuvB,IAAK,GAAIzvB,IAAUmvB,SAI/B7b,eAAgB,SAAU9I,EAAGC,EAAGtV,GAC5BpD,KAAKq8B,KAAK5jB,EAAG,IAAKC,EAAG,IAAKtV,EAAG,MAAOqI,KAExCoU,WAAY,SAAUtG,GAClBvZ,KAAKmiB,eAAe5I,GACpBvZ,KAAKwhB,iBAAiBjI,GACtBvZ,KAAK27B,UAAYpiB,GAErBiI,iBAAkB,SAAUjI,GACxB,GAAIA,EAAU,EAAG,CACb,GAAIoe,GAAK33B,KAAK24B,KAAKlB,aAAaz3B,KAAK27B,SAAWpiB,GAAS,EACzDvZ,MAAKu7B,aAAa5D,EAAGrhB,eAAiBqhB,EACtC33B,KAAKq8B,KAAK1E,EAAGrhB,cAAe,MAAO7K,MAG3CyW,aAAc,SAAUzJ,EAAGC,EAAGtV,GAC1BpD,KAAKq8B,KAAK5jB,EAAG,IAAKC,EAAG,IAAKtV,EAAG,MAAOqI,KAExC0W,eAAgB,SAAU5I,GACtB,GAAIA,EAAU,EAAG,CACb,GAAIoe,GAAK33B,KAAK24B,KAAKlB,aAAaz3B,KAAK27B,SAAWpiB,GAAS,EACzDvZ,MAAKu7B,aAAa5D,EAAGrhB,eAAiBqhB,EACtC33B,KAAKq8B,KAAK1E,EAAGrhB,cAAe,MAAO7K,MAG3C8O,SAAU,SAAUA,EAAU/Y,GAApB,GAOFkX,GACAilB,EAAiCC,CAPrC59B,MAAK4f,OACL5f,KAAK6P,KAAKrO,EAAIiL,KAAMjL,EAAIkL,IAAKlL,EAAI3C,MAAO2C,EAAI1C,QAC5CkB,KAAK8P,OACAyK,EAASG,WACV1a,KAAK2P,UAAUnO,EAAI3C,MAAO,EAAG,EAAG2C,EAAI1C,OAAQ0C,EAAIiL,KAAMjL,EAAIkL,KAE1DgM,EAAI4B,EAActa,KAAK24B,KAAMpe,EAAU/Y,GACvCm8B,EAAQjlB,EAAEK,QAAQzC,cACtBtW,KAAK07B,aAAaiC,GAASjlB,EAAEK,QACzBL,EAAEhG,WACFkrB,EAAQllB,EAAEa,QAAQjD,cAClBtW,KAAKu7B,aAAaqC,GAASllB,EAAEa,QAC7BvZ,KAAKq8B,KAAK,IAAMuB,EAAQ,SAE5B59B,KAAKq8B,KAAK,IAAMsB,EAAQ,MAAOlyB,IAC/BzL,KAAKihB,WAETS,eAAgB,SAAUmc,EAAWC,GACjC99B,KAAKq8B,KAAKwB,EAAW,IAAKC,EAAW,KAAMryB,KAE/CgW,aAAc,SAAU5iB,GACpBmB,KAAKq8B,KAAKx9B,EAAO,KAAM4M,KAE3BmW,WAAY,SAAUT,GAClBnhB,KAAKq8B,KAAKlb,EAAS,KAAM1V,KAE7BqW,YAAa,SAAUV,GACnBphB,KAAKq8B,KAAKjb,EAAU,KAAM3V,KAE9BsyB,eAAgB,SAAUC,GACtBh+B,KAAKq8B,KAAK2B,EAAa,KAAMvyB,KAEjCmU,KAAM,WACF5f,KAAK+7B,cAAc59B,KAAK6B,KAAKi+B,YAC7Bj+B,KAAKq8B,KAAK,IAAK5wB,KAEnBwV,QAAS,WACLjhB,KAAKq8B,KAAK,IAAK5wB,IACfzL,KAAKi+B,SAASj+B,KAAK+7B,cAAcpjB,QAErCsL,OAAQ,SAAUte,EAAG6U,GACjBxa,KAAKq8B,KAAK12B,EAAG,IAAK6U,EAAG,KAAM/O,KAE/BuY,OAAQ,SAAUre,EAAG6U,GACjBxa,KAAKq8B,KAAK12B,EAAG,IAAK6U,EAAG,KAAM/O,KAE/BsY,OAAQ,SAAUma,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAClCv+B,KAAKq8B,KAAK6B,EAAI,IAAKC,EAAI,IAAKC,EAAI,IAAKC,EAAI,IAAKC,EAAI,IAAKC,EAAI,KAAM9yB,KAErE+yB,QAAS,SAAUN,EAAIC,EAAIG,EAAIC,GAC3Bv+B,KAAKq8B,KAAK6B,EAAI,IAAKC,EAAI,IAAKG,EAAI,IAAKC,EAAI,KAAM9yB,KAEnDgzB,QAAS,SAAUL,EAAIC,EAAIC,EAAIC,GAC3Bv+B,KAAKq8B,KAAK+B,EAAI,IAAKC,EAAI,IAAKC,EAAI,IAAKC,EAAI,KAAM9yB,KAEnDyY,MAAO,WACHlkB,KAAKq8B,KAAK,IAAK5wB,KAEnBoE,KAAM,SAAUlK,EAAG6U,EAAGhX,EAAGk7B,GACrB1+B,KAAKq8B,KAAK12B,EAAG,IAAK6U,EAAG,IAAKhX,EAAG,IAAKk7B,EAAG,MAAOjzB,KAEhDkzB,QAAS,SAAUh5B,EAAG6U,EAAGO,EAAI6jB,GACzB,QAASC,GAAGC,GACR,MAAOn5B,GAAIm5B,EAEf,QAASC,GAAGD,GACR,MAAOtkB,GAAIskB,EAEf,GAAIE,GAAI,iBACRh/B,MAAKikB,OAAO4a,EAAG,GAAIE,EAAGH,IACtB5+B,KAAK+jB,OAAO8a,EAAG9jB,EAAKikB,GAAID,EAAGH,GAAKC,EAAG9jB,GAAKgkB,EAAGH,EAAKI,GAAIH,EAAG9jB,GAAKgkB,EAAG,IAC/D/+B,KAAK+jB,OAAO8a,EAAG9jB,GAAKgkB,GAAIH,EAAKI,GAAIH,EAAG9jB,EAAKikB,GAAID,GAAIH,GAAKC,EAAG,GAAIE,GAAIH,IACjE5+B,KAAK+jB,OAAO8a,GAAI9jB,EAAKikB,GAAID,GAAIH,GAAKC,GAAI9jB,GAAKgkB,GAAIH,EAAKI,GAAIH,GAAI9jB,GAAKgkB,EAAG,IACpE/+B,KAAK+jB,OAAO8a,GAAI9jB,GAAKgkB,EAAGH,EAAKI,GAAIH,GAAI9jB,EAAKikB,GAAID,EAAGH,GAAKC,EAAG,GAAIE,EAAGH,KAEpEva,OAAQ,SAAU1e,EAAG6U,EAAG/B,GACpBzY,KAAK2+B,QAAQh5B,EAAG6U,EAAG/B,EAAGA,IAE1B4I,OAAQ,WACJrhB,KAAKq8B,KAAK,IAAK5wB,KAEnB2X,IAAK,WACDpjB,KAAKq8B,KAAK,IAAK5wB,KAEnBqE,KAAM,WACF9P,KAAKq8B,KAAK,MAAO5wB,KAErBgX,WAAY,WACRziB,KAAKq8B,KAAK,MAAO5wB,KAErBwzB,YAAa,WACTj/B,KAAKq8B,KAAK,IAAK5wB,KAEnBuW,KAAM,WACFhiB,KAAKq8B,KAAK,IAAK5wB,KAEnB0X,WAAY,WACRnjB,KAAKq8B,KAAK,IAAK5wB,KAEnBsH,UAAW,SAAU1C,GACjB,GAAI4B,GAAMjS,KAAK24B,KAAKnB,SAASnnB,EACzB4B,KACAjS,KAAKw7B,YAAYvpB,EAAIqE,eAAiBrE,EACtCjS,KAAKq8B,KAAKpqB,EAAIqE,cAAe,MAAO7K,MAG5CkU,QAAS,SAAUmY,GACf,GAAI7tB,GAAOjK,IACX83B,GAAIxc,MAAM,UAAUrC,QAAQ,SAAUimB,GAClCj1B,EAAKoyB,KAAK,KAAM6C,EAAMzzB,OAG9BwyB,SAAU,SAAUlG,GAChB,MAAW,OAAPA,GAKIxe,QAASvZ,KAAK27B,SACd3b,OAAQhgB,KAAK47B,UALjB57B,KAAK27B,SAAW5D,EAAIxe,aACpBvZ,KAAK47B,QAAU7D,EAAI/X,UAQ3Bsd,QAAS,SAAU6B,GAAV,GACDzuB,GAAI1Q,KAAK47B,QACTh0B,EAAI8I,EAAE,GAAItN,EAAIsN,EAAE,GAAIuP,EAAIvP,EAAE,GAAIwP,EAAIxP,EAAE,GAAIyP,EAAIzP,EAAE,GAAIpT,EAAIoT,EAAE,EAC5D,QACI/K,EAAGiC,EAAIu3B,EAAEx5B,EAAIsa,EAAIkf,EAAE3kB,EAAI2F,EACvB3F,EAAGpX,EAAI+7B,EAAEx5B,EAAIua,EAAIif,EAAE3kB,EAAIld,KAGhC8N,IAsDCqQ,IACA2jB,MAAS,cACTC,aAAc,aACdC,eAAgB,eAChBC,oBAAqB,mBACrBC,aAAc,YACdC,kBAAmB,iBACnBC,oBAAqB,oBACrBC,yBAA0B,wBAC1BC,UAAa,UACbC,iBAAkB,eAClBC,mBAAoB,kBACpBC,wBAAyB,sBACzBC,aAAgB,eAChBC,oBAAqB,eACrBC,sBAAuB,eACvBC,2BAA4B,gBAiBhCzkB,EAAU,kBAAmB,SAC7BA,EAAU,cAAe,aACzBA,EAAU,QAAS,cACnBA,EAAU,YAAa,cACvBA,EAAU,UAAW,cACrBA,EAAU,SAAU,cACpBA,EAAU,UAAW,cACrBA,EAAU,SAAU,aACpBA,EAAU,cAAe,aAkErBqM,IACA/F,KAAM,EACNX,OAAQ,EACRsD,cAAe,EACfyb,UAAW,EACXC,YAAa,EACbC,cAAe,EACfC,eAAgB,EAChBzwB,KAAM,GAEN4U,GAAwBqD,GACxBpG,IACA6e,MAAO,GACPC,SACI,EACA,EACA,EACA,GAEJC,KACI,EACA,GAEJC,UACI,EACA,GAEJC,aACI,EACA,EACA,EACA,GAEJC,gBACI,EACA,EACA,EACA,EACA,EACA,GAEJC,UAEAjf,IACAkf,KAAM,EACN78B,MAAO,EACP88B,OAAQ,GAERjf,IACAkf,MAAO,EACP/8B,MAAO,EACPg9B,MAAO,GA4lBXxhC,EAAM0C,WAAW1C,EAAMgD,KACnBy+B,SAAUt0B,EACV9J,aAAcA,EACd6Y,WAAYA,EACZhB,aAAcA,EACdW,WAAYA,EACZkD,UAAWA,GACXC,WAAYA,GACZ1S,gBAAiBA,EACjB6F,gBAAiBA,EACjBkW,oBAAqBA,GACrBtB,UAAWA,GACXK,gBAAiBA,GACjB5H,OAAQL,GACRvL,UAAWA,EACXqL,OAAQA,GACRhU,OAAQA,IAEZjL,EAAMud,QAAQwJ,UAAY/mB,EAAMgD,IAAI+jB,UACpC/mB,EAAMud,QAAQva,IAAMhD,EAAMgD,KAC5BhD,OACKA,OACS,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,cAAe,YAAaD,IACrC,WA8FE,MA7FC,UAAUE,EAAGgN,GACV9K,MAAM0hC,UACFvhC,OAAQ,SAAUuV,GACdA,EAAMisB,OAAOljC,KAAK,aAClBiX,EAAMnU,QAAQyB,IAAM1C,KAAKiB,QACzBmU,EAAMksB,UAAYthC,KAAKshC,UACvBlsB,EAAMmsB,SAAWvhC,KAAKuhC,SACtBnsB,EAAMosB,eAAiBxhC,KAAKwhC,gBAEhCvgC,SACIme,SAAU,aACVE,SAAU,GACVpT,UAAW,OACXu1B,UAAU,EACVpkB,WAAW,EACXlR,OAAQ,KACRsR,MAAO,KACPC,OAAQ,KACRC,QAAS,KACTC,SAAU,KACVC,QAAS,4BAA8Bne,MAAMsoB,QAC7C3T,KAAM,MAEVitB,UAAW,WAAA,GAOHrgC,GANAygC,EAAW,GAAIlkC,GAAEmkC,SACjBjb,EAAUgb,EAAShb,UACnBkb,GAASlb,QAASA,EACtB,KAAI1mB,KAAK6hC,QAAQ,YAAaD,GAmB9B,MAhBI3gC,GAAUjB,KAAKiB,QAAQyB,IAC3BzB,EAAQ8c,UAAY9c,EAAQ8c,WAAa9c,EAAQwgC,SACjDzhC,KAAKuhC,SAASG,GAAUI,KAAK,SAAUhlB,GACnC,MAAOpd,OAAMud,QAAQwJ,UAAU3J,EAAM7b,KACtC8gC,KAAK,SAAU5iB,GACdzf,MAAMwf,QACFC,QAASA,EACTC,SAAUne,EAAQme,SAClBE,SAAUre,EAAQqe,SAClB0iB,WAAY/gC,EAAQ+gC,WACpBC,YAAahhC,EAAQghC,cAEzBP,EAAS7a,YACVqb,KAAK,SAAUC,GACdT,EAASU,OAAOD,KAEbzb,GAEX6a,SAAU,SAAUG,GAChB,GAAIhb,GAAU,GAAIlpB,GAAEmkC,QAapB,OAZAjiC,OAAMud,QAAQolB,QAAQriC,KAAKsiC,SAASP,KAAK,SAAUxlB,GAC/C,GAAIqlB,IACA3yB,KAAMsN,EACNgmB,WAAY,EACZb,SAAU,EACVc,WAAY,EAEhBd,GAASe,OAAOb,GAChBlb,EAAQG,QAAQ+a,EAAK3yB,QACtBizB,KAAK,SAAUC,GACdzb,EAAQ0b,OAAOD,KAEZzb,GAEX8a,eAAgB,SAAUkB,EAAUC,GAApB,GAERL,GACAM,EASAC,CAaJ,OAxBAH,GAAWA,MACPJ,EAAUtiC,KAAKsiC,QACfM,EAASplC,EAAE,qCACXklC,EAAS7jC,OACT+jC,EAAOE,KACHjkC,MAAO6jC,EAAS7jC,MAChBkkC,SAAU,YAGlBT,EAAQU,OAAOJ,GACfA,EAAOxlB,OAAOslB,EAAS1zB,SAAWszB,EAAQW,OAAM,GAAM,IAClDJ,EAAQrlC,EAAEmkC,WACduB,WAAW,WACP,GAAIxc,GAAUhnB,MAAMud,QAAQolB,QAAQO,EAAQD,EAC5Cjc,GAAQyc,OAAO,WACXP,EAAOQ,WACRtB,KAAK,WACJe,EAAMhc,QAAQniB,MAAMm+B,EAAOt4B,aAC5B23B,KAAK,WACJW,EAAMT,OAAO19B,MAAMm+B,EAAOt4B,aAC3Bm3B,SAAS,WACRmB,EAAMnB,SAASh9B,MAAMm+B,EAAOt4B,cAEjC,IACIs4B,EAAMnc,aAGvBjnB,OAAOC,MAAM2C,QACR5C,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,aACH,aACA,gBACA,WACA,cACDD,IACL,aAYkB,kBAAVC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.pdf.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('pdf/pako', ['kendo.core'], f);\n}(function () {\n    (function () {\n        kendo.pdf = kendo.pdf || {};\n        kendo.pdf.supportsDeflate = function () {\n            return window.pako && typeof window.pako.deflate == 'function';\n        };\n        kendo.pdf.deflate = function (data) {\n            return window.pako.deflate(data);\n        };\n    }());\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('pdf/core', [\n        'pdf/pako',\n        'kendo.core',\n        'kendo.color',\n        'kendo.drawing'\n    ], f);\n}(function () {\n    (function (kendo) {\n        window.kendo.pdf = window.kendo.pdf || {};\n        var support = kendo.support;\n        var supportBrowser = support.browser;\n        var kendoPdf = kendo.pdf;\n        var drawing = kendo.drawing;\n        var util = drawing.util;\n        var kendoGeometry = kendo.geometry;\n        var HAS_TYPED_ARRAYS = typeof Uint8Array !== 'undefined' && kendo.support.browser && (!kendo.support.browser.msie || kendo.support.browser.version > 9);\n        var BASE64 = function () {\n            var keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n            return {\n                decode: function (str) {\n                    var input = str.replace(/[^A-Za-z0-9\\+\\/\\=]/g, ''), i = 0, n = input.length, output = [];\n                    while (i < n) {\n                        var enc1 = keyStr.indexOf(input.charAt(i++));\n                        var enc2 = keyStr.indexOf(input.charAt(i++));\n                        var enc3 = keyStr.indexOf(input.charAt(i++));\n                        var enc4 = keyStr.indexOf(input.charAt(i++));\n                        var chr1 = enc1 << 2 | enc2 >>> 4;\n                        var chr2 = (enc2 & 15) << 4 | enc3 >>> 2;\n                        var chr3 = (enc3 & 3) << 6 | enc4;\n                        output.push(chr1);\n                        if (enc3 != 64) {\n                            output.push(chr2);\n                        }\n                        if (enc4 != 64) {\n                            output.push(chr3);\n                        }\n                    }\n                    return output;\n                },\n                encode: function (bytes) {\n                    var i = 0, n = bytes.length;\n                    var output = '';\n                    while (i < n) {\n                        var chr1 = bytes[i++];\n                        var chr2 = bytes[i++];\n                        var chr3 = bytes[i++];\n                        var enc1 = chr1 >>> 2;\n                        var enc2 = (chr1 & 3) << 4 | chr2 >>> 4;\n                        var enc3 = (chr2 & 15) << 2 | chr3 >>> 6;\n                        var enc4 = chr3 & 63;\n                        if (i - n == 2) {\n                            enc3 = enc4 = 64;\n                        } else if (i - n == 1) {\n                            enc4 = 64;\n                        }\n                        output += keyStr.charAt(enc1) + keyStr.charAt(enc2) + keyStr.charAt(enc3) + keyStr.charAt(enc4);\n                    }\n                    return output;\n                }\n            };\n        }();\n        function BinaryStream(data) {\n            var offset = 0, length = 0;\n            if (data == null) {\n                data = HAS_TYPED_ARRAYS ? new Uint8Array(256) : [];\n            } else {\n                length = data.length;\n            }\n            var ensure = HAS_TYPED_ARRAYS ? function (len) {\n                if (len >= data.length) {\n                    var tmp = new Uint8Array(Math.max(len + 256, data.length * 2));\n                    tmp.set(data, 0);\n                    data = tmp;\n                }\n            } : function () {\n            };\n            var get = HAS_TYPED_ARRAYS ? function () {\n                return new Uint8Array(data.buffer, 0, length);\n            } : function () {\n                return data;\n            };\n            var write = HAS_TYPED_ARRAYS ? function (bytes) {\n                if (typeof bytes == 'string') {\n                    return writeString(bytes);\n                }\n                var len = bytes.length;\n                ensure(offset + len);\n                data.set(bytes, offset);\n                offset += len;\n                if (offset > length) {\n                    length = offset;\n                }\n            } : function (bytes) {\n                if (typeof bytes == 'string') {\n                    return writeString(bytes);\n                }\n                for (var i = 0; i < bytes.length; ++i) {\n                    writeByte(bytes[i]);\n                }\n            };\n            var slice = HAS_TYPED_ARRAYS ? function (start, length) {\n                if (data.buffer.slice) {\n                    return new Uint8Array(data.buffer.slice(start, start + length));\n                } else {\n                    var x = new Uint8Array(length);\n                    x.set(new Uint8Array(data.buffer, start, length));\n                    return x;\n                }\n            } : function (start, length) {\n                return data.slice(start, start + length);\n            };\n            function eof() {\n                return offset >= length;\n            }\n            function readByte() {\n                return offset < length ? data[offset++] : 0;\n            }\n            function writeByte(b) {\n                ensure(offset);\n                data[offset++] = b & 255;\n                if (offset > length) {\n                    length = offset;\n                }\n            }\n            function readShort() {\n                return readByte() << 8 | readByte();\n            }\n            function writeShort(w) {\n                writeByte(w >> 8);\n                writeByte(w);\n            }\n            function readShort_() {\n                var w = readShort();\n                return w >= 32768 ? w - 65536 : w;\n            }\n            function writeShort_(w) {\n                writeShort(w < 0 ? w + 65536 : w);\n            }\n            function readLong() {\n                return readShort() * 65536 + readShort();\n            }\n            function writeLong(w) {\n                writeShort(w >>> 16 & 65535);\n                writeShort(w & 65535);\n            }\n            function readLong_() {\n                var w = readLong();\n                return w >= 2147483648 ? w - 4294967296 : w;\n            }\n            function writeLong_(w) {\n                writeLong(w < 0 ? w + 4294967296 : w);\n            }\n            function readFixed() {\n                return readLong() / 65536;\n            }\n            function writeFixed(f) {\n                writeLong(Math.round(f * 65536));\n            }\n            function readFixed_() {\n                return readLong_() / 65536;\n            }\n            function writeFixed_(f) {\n                writeLong_(Math.round(f * 65536));\n            }\n            function read(len) {\n                return times(len, readByte);\n            }\n            function readString(len) {\n                return String.fromCharCode.apply(String, read(len));\n            }\n            function writeString(str) {\n                for (var i = 0; i < str.length; ++i) {\n                    writeByte(str.charCodeAt(i));\n                }\n            }\n            function times(n, reader) {\n                for (var ret = new Array(n), i = 0; i < n; ++i) {\n                    ret[i] = reader();\n                }\n                return ret;\n            }\n            var stream = {\n                eof: eof,\n                readByte: readByte,\n                writeByte: writeByte,\n                readShort: readShort,\n                writeShort: writeShort,\n                readLong: readLong,\n                writeLong: writeLong,\n                readFixed: readFixed,\n                writeFixed: writeFixed,\n                readShort_: readShort_,\n                writeShort_: writeShort_,\n                readLong_: readLong_,\n                writeLong_: writeLong_,\n                readFixed_: readFixed_,\n                writeFixed_: writeFixed_,\n                read: read,\n                write: write,\n                readString: readString,\n                writeString: writeString,\n                times: times,\n                get: get,\n                slice: slice,\n                offset: function (pos) {\n                    if (pos != null) {\n                        offset = pos;\n                        return stream;\n                    }\n                    return offset;\n                },\n                skip: function (nbytes) {\n                    offset += nbytes;\n                },\n                toString: function () {\n                    throw new Error('FIX CALLER.  BinaryStream is no longer convertible to string!');\n                },\n                length: function () {\n                    return length;\n                },\n                saveExcursion: function (f) {\n                    var pos = offset;\n                    try {\n                        return f();\n                    } finally {\n                        offset = pos;\n                    }\n                },\n                writeBase64: function (base64) {\n                    if (window.atob) {\n                        writeString(window.atob(base64));\n                    } else {\n                        write(BASE64.decode(base64));\n                    }\n                },\n                base64: function () {\n                    return BASE64.encode(get());\n                }\n            };\n            return stream;\n        }\n        function ucs2decode(string) {\n            var output = [], counter = 0, length = string.length, value, extra;\n            while (counter < length) {\n                value = string.charCodeAt(counter++);\n                if (value >= 55296 && value <= 56319 && counter < length) {\n                    extra = string.charCodeAt(counter++);\n                    if ((extra & 64512) == 56320) {\n                        output.push(((value & 1023) << 10) + (extra & 1023) + 65536);\n                    } else {\n                        output.push(value);\n                        counter--;\n                    }\n                } else {\n                    output.push(value);\n                }\n            }\n            return output;\n        }\n        function ucs2encode(array) {\n            return array.map(function (value) {\n                var output = '';\n                if (value > 65535) {\n                    value -= 65536;\n                    output += String.fromCharCode(value >>> 10 & 1023 | 55296);\n                    value = 56320 | value & 1023;\n                }\n                output += String.fromCharCode(value);\n                return output;\n            }).join('');\n        }\n        function atobUint8Array(base64) {\n            var data = window.atob(base64);\n            var result = new Uint8Array(data.length);\n            for (var idx = 0; idx < data.length; idx++) {\n                result[idx] = data.charCodeAt(idx);\n            }\n            return result;\n        }\n        function createUint8Array(data) {\n            var result = new Uint8Array(data.length);\n            for (var idx = 0; idx < data.length; idx++) {\n                result[idx] = data[idx];\n            }\n            return result;\n        }\n        function base64ToUint8Array(base64) {\n            if (window.atob) {\n                return atobUint8Array(base64);\n            }\n            return createUint8Array(BASE64.decode(base64));\n        }\n        function hasOwnProperty$1(obj, key) {\n            return Object.prototype.hasOwnProperty.call(obj, key);\n        }\n        function sortedKeys(obj) {\n            return Object.keys(obj).sort(function (a, b) {\n                return a - b;\n            }).map(parseFloat);\n        }\n        function Directory(data) {\n            this.raw = data;\n            this.scalerType = data.readLong();\n            this.tableCount = data.readShort();\n            this.searchRange = data.readShort();\n            this.entrySelector = data.readShort();\n            this.rangeShift = data.readShort();\n            var tables = this.tables = {};\n            for (var i = 0; i < this.tableCount; ++i) {\n                var entry = {\n                    tag: data.readString(4),\n                    checksum: data.readLong(),\n                    offset: data.readLong(),\n                    length: data.readLong()\n                };\n                tables[entry.tag] = entry;\n            }\n        }\n        Directory.prototype = {\n            readTable: function (name, Ctor) {\n                var def = this.tables[name];\n                if (!def) {\n                    throw new Error('Table ' + name + ' not found in directory');\n                }\n                return this[name] = def.table = new Ctor(this, def);\n            },\n            render: function (tables) {\n                var this$1 = this;\n                var tableCount = Object.keys(tables).length;\n                var maxpow2 = Math.pow(2, Math.floor(Math.log(tableCount) / Math.LN2));\n                var searchRange = maxpow2 * 16;\n                var entrySelector = Math.floor(Math.log(maxpow2) / Math.LN2);\n                var rangeShift = tableCount * 16 - searchRange;\n                var out = BinaryStream();\n                out.writeLong(this.scalerType);\n                out.writeShort(tableCount);\n                out.writeShort(searchRange);\n                out.writeShort(entrySelector);\n                out.writeShort(rangeShift);\n                var directoryLength = tableCount * 16;\n                var offset = out.offset() + directoryLength;\n                var headOffset = null;\n                var tableData = BinaryStream();\n                for (var tag in tables) {\n                    if (hasOwnProperty$1(tables, tag)) {\n                        var table = tables[tag];\n                        out.writeString(tag);\n                        out.writeLong(this$1.checksum(table));\n                        out.writeLong(offset);\n                        out.writeLong(table.length);\n                        tableData.write(table);\n                        if (tag == 'head') {\n                            headOffset = offset;\n                        }\n                        offset += table.length;\n                        while (offset % 4) {\n                            tableData.writeByte(0);\n                            offset++;\n                        }\n                    }\n                }\n                out.write(tableData.get());\n                var sum = this.checksum(out.get());\n                var adjustment = 2981146554 - sum;\n                out.offset(headOffset + 8);\n                out.writeLong(adjustment);\n                return out.get();\n            },\n            checksum: function (data) {\n                data = BinaryStream(data);\n                var sum = 0;\n                while (!data.eof()) {\n                    sum += data.readLong();\n                }\n                return sum & 4294967295;\n            }\n        };\n        function deftable(methods) {\n            function Ctor(file, def) {\n                this.definition = def;\n                this.length = def.length;\n                this.offset = def.offset;\n                this.file = file;\n                this.rawData = file.raw;\n                this.parse(file.raw);\n            }\n            Ctor.prototype.raw = function () {\n                return this.rawData.slice(this.offset, this.length);\n            };\n            for (var i in methods) {\n                if (hasOwnProperty$1(methods, i)) {\n                    Ctor[i] = Ctor.prototype[i] = methods[i];\n                }\n            }\n            return Ctor;\n        }\n        var HeadTable = deftable({\n            parse: function (data) {\n                data.offset(this.offset);\n                this.version = data.readLong();\n                this.revision = data.readLong();\n                this.checkSumAdjustment = data.readLong();\n                this.magicNumber = data.readLong();\n                this.flags = data.readShort();\n                this.unitsPerEm = data.readShort();\n                this.created = data.read(8);\n                this.modified = data.read(8);\n                this.xMin = data.readShort_();\n                this.yMin = data.readShort_();\n                this.xMax = data.readShort_();\n                this.yMax = data.readShort_();\n                this.macStyle = data.readShort();\n                this.lowestRecPPEM = data.readShort();\n                this.fontDirectionHint = data.readShort_();\n                this.indexToLocFormat = data.readShort_();\n                this.glyphDataFormat = data.readShort_();\n            },\n            render: function (indexToLocFormat) {\n                var out = BinaryStream();\n                out.writeLong(this.version);\n                out.writeLong(this.revision);\n                out.writeLong(0);\n                out.writeLong(this.magicNumber);\n                out.writeShort(this.flags);\n                out.writeShort(this.unitsPerEm);\n                out.write(this.created);\n                out.write(this.modified);\n                out.writeShort_(this.xMin);\n                out.writeShort_(this.yMin);\n                out.writeShort_(this.xMax);\n                out.writeShort_(this.yMax);\n                out.writeShort(this.macStyle);\n                out.writeShort(this.lowestRecPPEM);\n                out.writeShort_(this.fontDirectionHint);\n                out.writeShort_(indexToLocFormat);\n                out.writeShort_(this.glyphDataFormat);\n                return out.get();\n            }\n        });\n        var LocaTable = deftable({\n            parse: function (data) {\n                data.offset(this.offset);\n                var format = this.file.head.indexToLocFormat;\n                if (format === 0) {\n                    this.offsets = data.times(this.length / 2, function () {\n                        return 2 * data.readShort();\n                    });\n                } else {\n                    this.offsets = data.times(this.length / 4, data.readLong);\n                }\n            },\n            offsetOf: function (id) {\n                return this.offsets[id];\n            },\n            lengthOf: function (id) {\n                return this.offsets[id + 1] - this.offsets[id];\n            },\n            render: function (offsets) {\n                var out = BinaryStream();\n                var needsLongFormat = offsets[offsets.length - 1] > 65535;\n                for (var i = 0; i < offsets.length; ++i) {\n                    if (needsLongFormat) {\n                        out.writeLong(offsets[i]);\n                    } else {\n                        out.writeShort(offsets[i] / 2);\n                    }\n                }\n                return {\n                    format: needsLongFormat ? 1 : 0,\n                    table: out.get()\n                };\n            }\n        });\n        var HheaTable = deftable({\n            parse: function (data) {\n                data.offset(this.offset);\n                this.version = data.readLong();\n                this.ascent = data.readShort_();\n                this.descent = data.readShort_();\n                this.lineGap = data.readShort_();\n                this.advanceWidthMax = data.readShort();\n                this.minLeftSideBearing = data.readShort_();\n                this.minRightSideBearing = data.readShort_();\n                this.xMaxExtent = data.readShort_();\n                this.caretSlopeRise = data.readShort_();\n                this.caretSlopeRun = data.readShort_();\n                this.caretOffset = data.readShort_();\n                data.skip(4 * 2);\n                this.metricDataFormat = data.readShort_();\n                this.numOfLongHorMetrics = data.readShort();\n            },\n            render: function (ids) {\n                var out = BinaryStream();\n                out.writeLong(this.version);\n                out.writeShort_(this.ascent);\n                out.writeShort_(this.descent);\n                out.writeShort_(this.lineGap);\n                out.writeShort(this.advanceWidthMax);\n                out.writeShort_(this.minLeftSideBearing);\n                out.writeShort_(this.minRightSideBearing);\n                out.writeShort_(this.xMaxExtent);\n                out.writeShort_(this.caretSlopeRise);\n                out.writeShort_(this.caretSlopeRun);\n                out.writeShort_(this.caretOffset);\n                out.write([\n                    0,\n                    0,\n                    0,\n                    0,\n                    0,\n                    0,\n                    0,\n                    0\n                ]);\n                out.writeShort_(this.metricDataFormat);\n                out.writeShort(ids.length);\n                return out.get();\n            }\n        });\n        var MaxpTable = deftable({\n            parse: function (data) {\n                data.offset(this.offset);\n                this.version = data.readLong();\n                this.numGlyphs = data.readShort();\n                this.maxPoints = data.readShort();\n                this.maxContours = data.readShort();\n                this.maxComponentPoints = data.readShort();\n                this.maxComponentContours = data.readShort();\n                this.maxZones = data.readShort();\n                this.maxTwilightPoints = data.readShort();\n                this.maxStorage = data.readShort();\n                this.maxFunctionDefs = data.readShort();\n                this.maxInstructionDefs = data.readShort();\n                this.maxStackElements = data.readShort();\n                this.maxSizeOfInstructions = data.readShort();\n                this.maxComponentElements = data.readShort();\n                this.maxComponentDepth = data.readShort();\n            },\n            render: function (glyphIds) {\n                var out = BinaryStream();\n                out.writeLong(this.version);\n                out.writeShort(glyphIds.length);\n                out.writeShort(this.maxPoints);\n                out.writeShort(this.maxContours);\n                out.writeShort(this.maxComponentPoints);\n                out.writeShort(this.maxComponentContours);\n                out.writeShort(this.maxZones);\n                out.writeShort(this.maxTwilightPoints);\n                out.writeShort(this.maxStorage);\n                out.writeShort(this.maxFunctionDefs);\n                out.writeShort(this.maxInstructionDefs);\n                out.writeShort(this.maxStackElements);\n                out.writeShort(this.maxSizeOfInstructions);\n                out.writeShort(this.maxComponentElements);\n                out.writeShort(this.maxComponentDepth);\n                return out.get();\n            }\n        });\n        var HmtxTable = deftable({\n            parse: function (data) {\n                data.offset(this.offset);\n                var dir = this.file, hhea = dir.hhea;\n                this.metrics = data.times(hhea.numOfLongHorMetrics, function () {\n                    return {\n                        advance: data.readShort(),\n                        lsb: data.readShort_()\n                    };\n                });\n                var lsbCount = dir.maxp.numGlyphs - dir.hhea.numOfLongHorMetrics;\n                this.leftSideBearings = data.times(lsbCount, data.readShort_);\n            },\n            forGlyph: function (id) {\n                var metrics = this.metrics;\n                var n = metrics.length;\n                if (id < n) {\n                    return metrics[id];\n                }\n                return {\n                    advance: metrics[n - 1].advance,\n                    lsb: this.leftSideBearings[id - n]\n                };\n            },\n            render: function (glyphIds) {\n                var this$1 = this;\n                var out = BinaryStream();\n                for (var i = 0; i < glyphIds.length; ++i) {\n                    var m = this$1.forGlyph(glyphIds[i]);\n                    out.writeShort(m.advance);\n                    out.writeShort_(m.lsb);\n                }\n                return out.get();\n            }\n        });\n        var GlyfTable = function () {\n            function SimpleGlyph(raw) {\n                this.raw = raw;\n            }\n            SimpleGlyph.prototype = {\n                compound: false,\n                render: function () {\n                    return this.raw.get();\n                }\n            };\n            var ARG_1_AND_2_ARE_WORDS = 1;\n            var WE_HAVE_A_SCALE = 8;\n            var MORE_COMPONENTS = 32;\n            var WE_HAVE_AN_X_AND_Y_SCALE = 64;\n            var WE_HAVE_A_TWO_BY_TWO = 128;\n            function CompoundGlyph(data) {\n                this.raw = data;\n                var ids = this.glyphIds = [];\n                var offsets = this.idOffsets = [];\n                while (true) {\n                    var flags = data.readShort();\n                    offsets.push(data.offset());\n                    ids.push(data.readShort());\n                    if (!(flags & MORE_COMPONENTS)) {\n                        break;\n                    }\n                    data.skip(flags & ARG_1_AND_2_ARE_WORDS ? 4 : 2);\n                    if (flags & WE_HAVE_A_TWO_BY_TWO) {\n                        data.skip(8);\n                    } else if (flags & WE_HAVE_AN_X_AND_Y_SCALE) {\n                        data.skip(4);\n                    } else if (flags & WE_HAVE_A_SCALE) {\n                        data.skip(2);\n                    }\n                }\n            }\n            CompoundGlyph.prototype = {\n                compound: true,\n                render: function (old2new) {\n                    var this$1 = this;\n                    var out = BinaryStream(this.raw.get());\n                    for (var i = 0; i < this.glyphIds.length; ++i) {\n                        var id = this$1.glyphIds[i];\n                        out.offset(this$1.idOffsets[i]);\n                        out.writeShort(old2new[id]);\n                    }\n                    return out.get();\n                }\n            };\n            return deftable({\n                parse: function () {\n                    this.cache = {};\n                },\n                glyphFor: function (id) {\n                    var cache = this.cache;\n                    if (hasOwnProperty$1(cache, id)) {\n                        return cache[id];\n                    }\n                    var loca = this.file.loca;\n                    var length = loca.lengthOf(id);\n                    if (length === 0) {\n                        return cache[id] = null;\n                    }\n                    var data = this.rawData;\n                    var offset = this.offset + loca.offsetOf(id);\n                    var raw = BinaryStream(data.slice(offset, length));\n                    var numberOfContours = raw.readShort_();\n                    var xMin = raw.readShort_();\n                    var yMin = raw.readShort_();\n                    var xMax = raw.readShort_();\n                    var yMax = raw.readShort_();\n                    var glyph = cache[id] = numberOfContours == -1 ? new CompoundGlyph(raw) : new SimpleGlyph(raw);\n                    glyph.numberOfContours = numberOfContours;\n                    glyph.xMin = xMin;\n                    glyph.yMin = yMin;\n                    glyph.xMax = xMax;\n                    glyph.yMax = yMax;\n                    return glyph;\n                },\n                render: function (glyphs, oldIds, old2new) {\n                    var out = BinaryStream(), offsets = [];\n                    for (var i = 0; i < oldIds.length; ++i) {\n                        var id = oldIds[i];\n                        var glyph = glyphs[id];\n                        offsets.push(out.offset());\n                        if (glyph) {\n                            out.write(glyph.render(old2new));\n                        }\n                    }\n                    offsets.push(out.offset());\n                    return {\n                        table: out.get(),\n                        offsets: offsets\n                    };\n                }\n            });\n        }();\n        var NameTable = function () {\n            function NameEntry(text, entry) {\n                this.text = text;\n                this.length = text.length;\n                this.platformID = entry.platformID;\n                this.platformSpecificID = entry.platformSpecificID;\n                this.languageID = entry.languageID;\n                this.nameID = entry.nameID;\n            }\n            return deftable({\n                parse: function (data) {\n                    data.offset(this.offset);\n                    data.readShort();\n                    var count = data.readShort();\n                    var stringOffset = this.offset + data.readShort();\n                    var nameRecords = data.times(count, function () {\n                        return {\n                            platformID: data.readShort(),\n                            platformSpecificID: data.readShort(),\n                            languageID: data.readShort(),\n                            nameID: data.readShort(),\n                            length: data.readShort(),\n                            offset: data.readShort() + stringOffset\n                        };\n                    });\n                    var strings = this.strings = {};\n                    for (var i = 0; i < nameRecords.length; ++i) {\n                        var rec = nameRecords[i];\n                        data.offset(rec.offset);\n                        var text = data.readString(rec.length);\n                        if (!strings[rec.nameID]) {\n                            strings[rec.nameID] = [];\n                        }\n                        strings[rec.nameID].push(new NameEntry(text, rec));\n                    }\n                    this.postscriptEntry = strings[6][0];\n                    this.postscriptName = this.postscriptEntry.text.replace(/[^\\x20-\\x7F]/g, '');\n                },\n                render: function (psName) {\n                    var this$1 = this;\n                    var strings = this.strings;\n                    var strCount = 0;\n                    for (var i in strings) {\n                        if (hasOwnProperty$1(strings, i)) {\n                            strCount += strings[i].length;\n                        }\n                    }\n                    var out = BinaryStream();\n                    var strTable = BinaryStream();\n                    out.writeShort(0);\n                    out.writeShort(strCount);\n                    out.writeShort(6 + 12 * strCount);\n                    for (i in strings) {\n                        if (hasOwnProperty$1(strings, i)) {\n                            var list = i == 6 ? [new NameEntry(psName, this$1.postscriptEntry)] : strings[i];\n                            for (var j = 0; j < list.length; ++j) {\n                                var str = list[j];\n                                out.writeShort(str.platformID);\n                                out.writeShort(str.platformSpecificID);\n                                out.writeShort(str.languageID);\n                                out.writeShort(str.nameID);\n                                out.writeShort(str.length);\n                                out.writeShort(strTable.offset());\n                                strTable.writeString(str.text);\n                            }\n                        }\n                    }\n                    out.write(strTable.get());\n                    return out.get();\n                }\n            });\n        }();\n        var PostTable = function () {\n            var POSTSCRIPT_GLYPHS = '.notdef .null nonmarkingreturn space exclam quotedbl numbersign dollar percent ampersand quotesingle parenleft parenright asterisk plus comma hyphen period slash zero one two three four five six seven eight nine colon semicolon less equal greater question at A B C D E F G H I J K L M N O P Q R S T U V W X Y Z bracketleft backslash bracketright asciicircum underscore grave a b c d e f g h i j k l m n o p q r s t u v w x y z braceleft bar braceright asciitilde Adieresis Aring Ccedilla Eacute Ntilde Odieresis Udieresis aacute agrave acircumflex adieresis atilde aring ccedilla eacute egrave ecircumflex edieresis iacute igrave icircumflex idieresis ntilde oacute ograve ocircumflex odieresis otilde uacute ugrave ucircumflex udieresis dagger degree cent sterling section bullet paragraph germandbls registered copyright trademark acute dieresis notequal AE Oslash infinity plusminus lessequal greaterequal yen mu partialdiff summation product pi integral ordfeminine ordmasculine Omega ae oslash questiondown exclamdown logicalnot radical florin approxequal Delta guillemotleft guillemotright ellipsis nonbreakingspace Agrave Atilde Otilde OE oe endash emdash quotedblleft quotedblright quoteleft quoteright divide lozenge ydieresis Ydieresis fraction currency guilsinglleft guilsinglright fi fl daggerdbl periodcentered quotesinglbase quotedblbase perthousand Acircumflex Ecircumflex Aacute Edieresis Egrave Iacute Icircumflex Idieresis Igrave Oacute Ocircumflex apple Ograve Uacute Ucircumflex Ugrave dotlessi circumflex tilde macron breve dotaccent ring cedilla hungarumlaut ogonek caron Lslash lslash Scaron scaron Zcaron zcaron brokenbar Eth eth Yacute yacute Thorn thorn minus multiply onesuperior twosuperior threesuperior onehalf onequarter threequarters franc Gbreve gbreve Idotaccent Scedilla scedilla Cacute cacute Ccaron ccaron dcroat'.split(/\\s+/g);\n            return deftable({\n                parse: function (data) {\n                    var this$1 = this;\n                    data.offset(this.offset);\n                    this.format = data.readLong();\n                    this.italicAngle = data.readFixed_();\n                    this.underlinePosition = data.readShort_();\n                    this.underlineThickness = data.readShort_();\n                    this.isFixedPitch = data.readLong();\n                    this.minMemType42 = data.readLong();\n                    this.maxMemType42 = data.readLong();\n                    this.minMemType1 = data.readLong();\n                    this.maxMemType1 = data.readLong();\n                    var numberOfGlyphs;\n                    switch (this.format) {\n                    case 65536:\n                    case 196608:\n                        break;\n                    case 131072:\n                        numberOfGlyphs = data.readShort();\n                        this.glyphNameIndex = data.times(numberOfGlyphs, data.readShort);\n                        this.names = [];\n                        var limit = this.offset + this.length;\n                        while (data.offset() < limit) {\n                            this$1.names.push(data.readString(data.readByte()));\n                        }\n                        break;\n                    case 151552:\n                        numberOfGlyphs = data.readShort();\n                        this.offsets = data.read(numberOfGlyphs);\n                        break;\n                    case 262144:\n                        this.map = data.times(this.file.maxp.numGlyphs, data.readShort);\n                        break;\n                    }\n                },\n                glyphFor: function (code) {\n                    switch (this.format) {\n                    case 65536:\n                        return POSTSCRIPT_GLYPHS[code] || '.notdef';\n                    case 131072:\n                        var index = this.glyphNameIndex[code];\n                        if (index < POSTSCRIPT_GLYPHS.length) {\n                            return POSTSCRIPT_GLYPHS[index];\n                        }\n                        return this.names[index - POSTSCRIPT_GLYPHS.length] || '.notdef';\n                    case 151552:\n                    case 196608:\n                        return '.notdef';\n                    case 262144:\n                        return this.map[code] || 65535;\n                    }\n                },\n                render: function (mapping) {\n                    var this$1 = this;\n                    if (this.format == 196608) {\n                        return this.raw();\n                    }\n                    var out = BinaryStream(this.rawData.slice(this.offset, 32));\n                    out.writeLong(131072);\n                    out.offset(32);\n                    var indexes = [];\n                    var strings = [];\n                    for (var i = 0; i < mapping.length; ++i) {\n                        var id = mapping[i];\n                        var post = this$1.glyphFor(id);\n                        var index = POSTSCRIPT_GLYPHS.indexOf(post);\n                        if (index >= 0) {\n                            indexes.push(index);\n                        } else {\n                            indexes.push(POSTSCRIPT_GLYPHS.length + strings.length);\n                            strings.push(post);\n                        }\n                    }\n                    out.writeShort(mapping.length);\n                    for (i = 0; i < indexes.length; ++i) {\n                        out.writeShort(indexes[i]);\n                    }\n                    for (i = 0; i < strings.length; ++i) {\n                        out.writeByte(strings[i].length);\n                        out.writeString(strings[i]);\n                    }\n                    return out.get();\n                }\n            });\n        }();\n        var CmapTable = function () {\n            function CmapEntry(data, offset, codeMap) {\n                var self = this;\n                self.platformID = data.readShort();\n                self.platformSpecificID = data.readShort();\n                self.offset = offset + data.readLong();\n                data.saveExcursion(function () {\n                    var code;\n                    data.offset(self.offset);\n                    self.format = data.readShort();\n                    switch (self.format) {\n                    case 0:\n                        self.length = data.readShort();\n                        self.language = data.readShort();\n                        for (var i = 0; i < 256; ++i) {\n                            codeMap[i] = data.readByte();\n                        }\n                        break;\n                    case 4:\n                        self.length = data.readShort();\n                        self.language = data.readShort();\n                        var segCount = data.readShort() / 2;\n                        data.skip(6);\n                        var endCode = data.times(segCount, data.readShort);\n                        data.skip(2);\n                        var startCode = data.times(segCount, data.readShort);\n                        var idDelta = data.times(segCount, data.readShort_);\n                        var idRangeOffset = data.times(segCount, data.readShort);\n                        var count = (self.length + self.offset - data.offset()) / 2;\n                        var glyphIds = data.times(count, data.readShort);\n                        for (i = 0; i < segCount; ++i) {\n                            var start = startCode[i], end = endCode[i];\n                            for (code = start; code <= end; ++code) {\n                                var glyphId;\n                                if (idRangeOffset[i] === 0) {\n                                    glyphId = code + idDelta[i];\n                                } else {\n                                    var index = idRangeOffset[i] / 2 - (segCount - i) + (code - start);\n                                    glyphId = glyphIds[index] || 0;\n                                    if (glyphId !== 0) {\n                                        glyphId += idDelta[i];\n                                    }\n                                }\n                                codeMap[code] = glyphId & 65535;\n                            }\n                        }\n                        break;\n                    case 6:\n                        self.length = data.readShort();\n                        self.language = data.readShort();\n                        code = data.readShort();\n                        var length = data.readShort();\n                        while (length-- > 0) {\n                            codeMap[code++] = data.readShort();\n                        }\n                        break;\n                    case 12:\n                        data.readShort();\n                        self.length = data.readLong();\n                        self.language = data.readLong();\n                        var ngroups = data.readLong();\n                        while (ngroups-- > 0) {\n                            code = data.readLong();\n                            var endCharCode = data.readLong();\n                            var glyphCode = data.readLong();\n                            while (code <= endCharCode) {\n                                codeMap[code++] = glyphCode++;\n                            }\n                        }\n                        break;\n                    default:\n                        if (window.console) {\n                            window.console.error('Unhandled CMAP format: ' + self.format);\n                        }\n                    }\n                });\n            }\n            function renderCharmap(ncid2ogid, ogid2ngid) {\n                var codes = sortedKeys(ncid2ogid);\n                var startCodes = [];\n                var endCodes = [];\n                var last = null;\n                var diff = null;\n                function new_gid(charcode) {\n                    return ogid2ngid[ncid2ogid[charcode]];\n                }\n                for (var i = 0; i < codes.length; ++i) {\n                    var code = codes[i];\n                    var gid = new_gid(code);\n                    var delta = gid - code;\n                    if (last == null || delta !== diff) {\n                        if (last) {\n                            endCodes.push(last);\n                        }\n                        startCodes.push(code);\n                        diff = delta;\n                    }\n                    last = code;\n                }\n                if (last) {\n                    endCodes.push(last);\n                }\n                endCodes.push(65535);\n                startCodes.push(65535);\n                var segCount = startCodes.length;\n                var segCountX2 = segCount * 2;\n                var searchRange = 2 * Math.pow(2, Math.floor(Math.log(segCount) / Math.LN2));\n                var entrySelector = Math.log(searchRange / 2) / Math.LN2;\n                var rangeShift = segCountX2 - searchRange;\n                var deltas = [];\n                var rangeOffsets = [];\n                var glyphIds = [];\n                for (i = 0; i < segCount; ++i) {\n                    var startCode = startCodes[i];\n                    var endCode = endCodes[i];\n                    if (startCode == 65535) {\n                        deltas.push(0);\n                        rangeOffsets.push(0);\n                        break;\n                    }\n                    var startGlyph = new_gid(startCode);\n                    if (startCode - startGlyph >= 32768) {\n                        deltas.push(0);\n                        rangeOffsets.push(2 * (glyphIds.length + segCount - i));\n                        for (var j = startCode; j <= endCode; ++j) {\n                            glyphIds.push(new_gid(j));\n                        }\n                    } else {\n                        deltas.push(startGlyph - startCode);\n                        rangeOffsets.push(0);\n                    }\n                }\n                var out = BinaryStream();\n                out.writeShort(3);\n                out.writeShort(1);\n                out.writeLong(12);\n                out.writeShort(4);\n                out.writeShort(16 + segCount * 8 + glyphIds.length * 2);\n                out.writeShort(0);\n                out.writeShort(segCountX2);\n                out.writeShort(searchRange);\n                out.writeShort(entrySelector);\n                out.writeShort(rangeShift);\n                endCodes.forEach(out.writeShort);\n                out.writeShort(0);\n                startCodes.forEach(out.writeShort);\n                deltas.forEach(out.writeShort_);\n                rangeOffsets.forEach(out.writeShort);\n                glyphIds.forEach(out.writeShort);\n                return out.get();\n            }\n            return deftable({\n                parse: function (data) {\n                    var self = this;\n                    var offset = self.offset;\n                    data.offset(offset);\n                    self.codeMap = {};\n                    self.version = data.readShort();\n                    var tableCount = data.readShort();\n                    self.tables = data.times(tableCount, function () {\n                        return new CmapEntry(data, offset, self.codeMap);\n                    });\n                },\n                render: function (ncid2ogid, ogid2ngid) {\n                    var out = BinaryStream();\n                    out.writeShort(0);\n                    out.writeShort(1);\n                    out.write(renderCharmap(ncid2ogid, ogid2ngid));\n                    return out.get();\n                }\n            });\n        }();\n        var OS2Table = deftable({\n            parse: function (data) {\n                data.offset(this.offset);\n                this.version = data.readShort();\n                this.averageCharWidth = data.readShort_();\n                this.weightClass = data.readShort();\n                this.widthClass = data.readShort();\n                this.type = data.readShort();\n                this.ySubscriptXSize = data.readShort_();\n                this.ySubscriptYSize = data.readShort_();\n                this.ySubscriptXOffset = data.readShort_();\n                this.ySubscriptYOffset = data.readShort_();\n                this.ySuperscriptXSize = data.readShort_();\n                this.ySuperscriptYSize = data.readShort_();\n                this.ySuperscriptXOffset = data.readShort_();\n                this.ySuperscriptYOffset = data.readShort_();\n                this.yStrikeoutSize = data.readShort_();\n                this.yStrikeoutPosition = data.readShort_();\n                this.familyClass = data.readShort_();\n                this.panose = data.times(10, data.readByte);\n                this.charRange = data.times(4, data.readLong);\n                this.vendorID = data.readString(4);\n                this.selection = data.readShort();\n                this.firstCharIndex = data.readShort();\n                this.lastCharIndex = data.readShort();\n                if (this.version > 0) {\n                    this.ascent = data.readShort_();\n                    this.descent = data.readShort_();\n                    this.lineGap = data.readShort_();\n                    this.winAscent = data.readShort();\n                    this.winDescent = data.readShort();\n                    this.codePageRange = data.times(2, data.readLong);\n                    if (this.version > 1) {\n                        this.xHeight = data.readShort();\n                        this.capHeight = data.readShort();\n                        this.defaultChar = data.readShort();\n                        this.breakChar = data.readShort();\n                        this.maxContext = data.readShort();\n                    }\n                }\n            },\n            render: function () {\n                return this.raw();\n            }\n        });\n        var subsetTag = 100000;\n        function nextSubsetTag() {\n            var ret = '', n = String(subsetTag);\n            for (var i = 0; i < n.length; ++i) {\n                ret += String.fromCharCode(n.charCodeAt(i) - 48 + 65);\n            }\n            ++subsetTag;\n            return ret;\n        }\n        function Subfont(font) {\n            this.font = font;\n            this.subset = {};\n            this.unicodes = {};\n            this.ogid2ngid = { 0: 0 };\n            this.ngid2ogid = { 0: 0 };\n            this.ncid2ogid = {};\n            this.next = this.firstChar = 1;\n            this.nextGid = 1;\n            this.psName = nextSubsetTag() + '+' + this.font.psName;\n        }\n        Subfont.prototype = {\n            use: function (ch) {\n                var self = this;\n                if (typeof ch == 'string') {\n                    return ucs2decode(ch).reduce(function (ret, code) {\n                        return ret + String.fromCharCode(self.use(code));\n                    }, '');\n                }\n                var code = self.unicodes[ch];\n                if (!code) {\n                    code = self.next++;\n                    self.subset[code] = ch;\n                    self.unicodes[ch] = code;\n                    var old_gid = self.font.cmap.codeMap[ch];\n                    if (old_gid) {\n                        self.ncid2ogid[code] = old_gid;\n                        if (self.ogid2ngid[old_gid] == null) {\n                            var new_gid = self.nextGid++;\n                            self.ogid2ngid[old_gid] = new_gid;\n                            self.ngid2ogid[new_gid] = old_gid;\n                        }\n                    }\n                }\n                return code;\n            },\n            encodeText: function (text) {\n                return this.use(text);\n            },\n            glyphIds: function () {\n                return sortedKeys(this.ogid2ngid);\n            },\n            glyphsFor: function (glyphIds, result) {\n                var this$1 = this;\n                if (!result) {\n                    result = {};\n                }\n                for (var i = 0; i < glyphIds.length; ++i) {\n                    var id = glyphIds[i];\n                    if (!result[id]) {\n                        var glyph = result[id] = this$1.font.glyf.glyphFor(id);\n                        if (glyph && glyph.compound) {\n                            this$1.glyphsFor(glyph.glyphIds, result);\n                        }\n                    }\n                }\n                return result;\n            },\n            render: function () {\n                var this$1 = this;\n                var glyphs = this.glyphsFor(this.glyphIds());\n                for (var old_gid in glyphs) {\n                    if (hasOwnProperty$1(glyphs, old_gid)) {\n                        old_gid = parseInt(old_gid, 10);\n                        if (this$1.ogid2ngid[old_gid] == null) {\n                            var new_gid = this$1.nextGid++;\n                            this$1.ogid2ngid[old_gid] = new_gid;\n                            this$1.ngid2ogid[new_gid] = old_gid;\n                        }\n                    }\n                }\n                var new_gid_ids = sortedKeys(this.ngid2ogid);\n                var old_gid_ids = new_gid_ids.map(function (id) {\n                    return this.ngid2ogid[id];\n                }, this);\n                var font = this.font;\n                var glyf = font.glyf.render(glyphs, old_gid_ids, this.ogid2ngid);\n                var loca = font.loca.render(glyf.offsets);\n                this.lastChar = this.next - 1;\n                var tables = {\n                    'cmap': CmapTable.render(this.ncid2ogid, this.ogid2ngid),\n                    'glyf': glyf.table,\n                    'loca': loca.table,\n                    'hmtx': font.hmtx.render(old_gid_ids),\n                    'hhea': font.hhea.render(old_gid_ids),\n                    'maxp': font.maxp.render(old_gid_ids),\n                    'post': font.post.render(old_gid_ids),\n                    'name': font.name.render(this.psName),\n                    'head': font.head.render(loca.format),\n                    'OS/2': font.os2.render()\n                };\n                return this.font.directory.render(tables);\n            },\n            cidToGidMap: function () {\n                var this$1 = this;\n                var out = BinaryStream(), len = 0;\n                for (var cid = this.firstChar; cid < this.next; ++cid) {\n                    while (len < cid) {\n                        out.writeShort(0);\n                        len++;\n                    }\n                    var old_gid = this$1.ncid2ogid[cid];\n                    if (old_gid) {\n                        var new_gid = this$1.ogid2ngid[old_gid];\n                        out.writeShort(new_gid);\n                    } else {\n                        out.writeShort(0);\n                    }\n                    len++;\n                }\n                return out.get();\n            }\n        };\n        function TTFFont(rawData, name) {\n            var self = this;\n            var data = self.contents = BinaryStream(rawData);\n            if (data.readString(4) == 'ttcf') {\n                var offset;\n                var parse = function () {\n                    data.offset(offset);\n                    self.parse();\n                };\n                if (!name) {\n                    throw new Error('Must specify a name for TTC files');\n                }\n                data.readLong();\n                var numFonts = data.readLong();\n                for (var i = 0; i < numFonts; ++i) {\n                    offset = data.readLong();\n                    data.saveExcursion(parse);\n                    if (self.psName == name) {\n                        return;\n                    }\n                }\n                throw new Error('Font ' + name + ' not found in collection');\n            } else {\n                data.offset(0);\n                self.parse();\n            }\n        }\n        TTFFont.prototype = {\n            parse: function () {\n                var dir = this.directory = new Directory(this.contents);\n                this.head = dir.readTable('head', HeadTable);\n                this.loca = dir.readTable('loca', LocaTable);\n                this.hhea = dir.readTable('hhea', HheaTable);\n                this.maxp = dir.readTable('maxp', MaxpTable);\n                this.hmtx = dir.readTable('hmtx', HmtxTable);\n                this.glyf = dir.readTable('glyf', GlyfTable);\n                this.name = dir.readTable('name', NameTable);\n                this.post = dir.readTable('post', PostTable);\n                this.cmap = dir.readTable('cmap', CmapTable);\n                this.os2 = dir.readTable('OS/2', OS2Table);\n                this.psName = this.name.postscriptName;\n                this.ascent = this.os2.ascent || this.hhea.ascent;\n                this.descent = this.os2.descent || this.hhea.descent;\n                this.lineGap = this.os2.lineGap || this.hhea.lineGap;\n                this.scale = 1000 / this.head.unitsPerEm;\n            },\n            widthOfGlyph: function (glyph) {\n                return this.hmtx.forGlyph(glyph).advance * this.scale;\n            },\n            makeSubset: function () {\n                return new Subfont(this);\n            }\n        };\n        var browser = kendo.support.browser;\n        var NL = '\\n';\n        var RESOURCE_COUNTER = 0;\n        var PAPER_SIZE = {\n            a0: [\n                2383.94,\n                3370.39\n            ],\n            a1: [\n                1683.78,\n                2383.94\n            ],\n            a2: [\n                1190.55,\n                1683.78\n            ],\n            a3: [\n                841.89,\n                1190.55\n            ],\n            a4: [\n                595.28,\n                841.89\n            ],\n            a5: [\n                419.53,\n                595.28\n            ],\n            a6: [\n                297.64,\n                419.53\n            ],\n            a7: [\n                209.76,\n                297.64\n            ],\n            a8: [\n                147.4,\n                209.76\n            ],\n            a9: [\n                104.88,\n                147.4\n            ],\n            a10: [\n                73.7,\n                104.88\n            ],\n            b0: [\n                2834.65,\n                4008.19\n            ],\n            b1: [\n                2004.09,\n                2834.65\n            ],\n            b2: [\n                1417.32,\n                2004.09\n            ],\n            b3: [\n                1000.63,\n                1417.32\n            ],\n            b4: [\n                708.66,\n                1000.63\n            ],\n            b5: [\n                498.9,\n                708.66\n            ],\n            b6: [\n                354.33,\n                498.9\n            ],\n            b7: [\n                249.45,\n                354.33\n            ],\n            b8: [\n                175.75,\n                249.45\n            ],\n            b9: [\n                124.72,\n                175.75\n            ],\n            b10: [\n                87.87,\n                124.72\n            ],\n            c0: [\n                2599.37,\n                3676.54\n            ],\n            c1: [\n                1836.85,\n                2599.37\n            ],\n            c2: [\n                1298.27,\n                1836.85\n            ],\n            c3: [\n                918.43,\n                1298.27\n            ],\n            c4: [\n                649.13,\n                918.43\n            ],\n            c5: [\n                459.21,\n                649.13\n            ],\n            c6: [\n                323.15,\n                459.21\n            ],\n            c7: [\n                229.61,\n                323.15\n            ],\n            c8: [\n                161.57,\n                229.61\n            ],\n            c9: [\n                113.39,\n                161.57\n            ],\n            c10: [\n                79.37,\n                113.39\n            ],\n            executive: [\n                521.86,\n                756\n            ],\n            folio: [\n                612,\n                936\n            ],\n            legal: [\n                612,\n                1008\n            ],\n            letter: [\n                612,\n                792\n            ],\n            tabloid: [\n                792,\n                1224\n            ]\n        };\n        function makeOutput() {\n            var indentLevel = 0, output = BinaryStream();\n            function out() {\n                var arguments$1 = arguments;\n                for (var i = 0; i < arguments.length; ++i) {\n                    var x = arguments$1[i];\n                    if (x === undefined) {\n                        throw new Error('Cannot output undefined to PDF');\n                    } else if (x instanceof PDFValue) {\n                        x.beforeRender(out);\n                        x.render(out);\n                    } else if (isArray(x)) {\n                        renderArray(x, out);\n                    } else if (isDate(x)) {\n                        renderDate(x, out);\n                    } else if (typeof x == 'number') {\n                        if (isNaN(x)) {\n                            throw new Error('Cannot output NaN to PDF');\n                        }\n                        var num = x.toFixed(7);\n                        if (num.indexOf('.') >= 0) {\n                            num = num.replace(/\\.?0+$/, '');\n                        }\n                        if (num == '-0') {\n                            num = '0';\n                        }\n                        output.writeString(num);\n                    } else if (/string|boolean/.test(typeof x)) {\n                        output.writeString(String(x));\n                    } else if (typeof x.get == 'function') {\n                        output.write(x.get());\n                    } else if (typeof x == 'object') {\n                        if (!x) {\n                            output.writeString('null');\n                        } else {\n                            out(new PDFDictionary(x));\n                        }\n                    }\n                }\n            }\n            out.writeData = function (data) {\n                output.write(data);\n            };\n            out.withIndent = function (f) {\n                ++indentLevel;\n                f(out);\n                --indentLevel;\n            };\n            out.indent = function () {\n                out(NL, pad('', indentLevel * 2, '  '));\n                out.apply(null, arguments);\n            };\n            out.offset = function () {\n                return output.offset();\n            };\n            out.toString = function () {\n                throw new Error('FIX CALLER');\n            };\n            out.get = function () {\n                return output.get();\n            };\n            out.stream = function () {\n                return output;\n            };\n            return out;\n        }\n        function wrapObject(value, id) {\n            var beforeRender = value.beforeRender;\n            var renderValue = value.render;\n            value.beforeRender = function () {\n            };\n            value.render = function (out) {\n                out(id, ' 0 R');\n            };\n            value.renderFull = function (out) {\n                value._offset = out.offset();\n                out(id, ' 0 obj ');\n                beforeRender.call(value, out);\n                renderValue.call(value, out);\n                out(' endobj');\n            };\n        }\n        function getPaperOptions(getOption) {\n            if (typeof getOption != 'function') {\n                var options = getOption;\n                getOption = function (key, def) {\n                    return key in options ? options[key] : def;\n                };\n            }\n            var paperSize = getOption('paperSize', PAPER_SIZE.a4);\n            if (!paperSize) {\n                return {};\n            }\n            if (typeof paperSize == 'string') {\n                paperSize = PAPER_SIZE[paperSize.toLowerCase()];\n                if (paperSize == null) {\n                    throw new Error('Unknown paper size');\n                }\n            }\n            paperSize[0] = unitsToPoints(paperSize[0]);\n            paperSize[1] = unitsToPoints(paperSize[1]);\n            if (getOption('landscape', false)) {\n                paperSize = [\n                    Math.max(paperSize[0], paperSize[1]),\n                    Math.min(paperSize[0], paperSize[1])\n                ];\n            }\n            var margin = getOption('margin');\n            if (margin) {\n                if (typeof margin == 'string' || typeof margin == 'number') {\n                    margin = unitsToPoints(margin, 0);\n                    margin = {\n                        left: margin,\n                        top: margin,\n                        right: margin,\n                        bottom: margin\n                    };\n                } else {\n                    margin = {\n                        left: unitsToPoints(margin.left, 0),\n                        top: unitsToPoints(margin.top, 0),\n                        right: unitsToPoints(margin.right, 0),\n                        bottom: unitsToPoints(margin.bottom, 0)\n                    };\n                }\n                if (getOption('addMargin')) {\n                    paperSize[0] += margin.left + margin.right;\n                    paperSize[1] += margin.top + margin.bottom;\n                }\n            }\n            return {\n                paperSize: paperSize,\n                margin: margin\n            };\n        }\n        function PDFDocument(options) {\n            var self = this;\n            var out = makeOutput();\n            var objcount = 0;\n            var objects = [];\n            function getOption(name, defval) {\n                return options && options[name] != null ? options[name] : defval;\n            }\n            self.getOption = getOption;\n            self.attach = function (value) {\n                if (objects.indexOf(value) < 0) {\n                    wrapObject(value, ++objcount);\n                    objects.push(value);\n                }\n                return value;\n            };\n            self.pages = [];\n            self.FONTS = {};\n            self.IMAGES = {};\n            self.GRAD_COL_FUNCTIONS = {};\n            self.GRAD_OPC_FUNCTIONS = {};\n            self.GRAD_COL = {};\n            self.GRAD_OPC = {};\n            var catalog = self.attach(new PDFCatalog());\n            var pageTree = self.attach(new PDFPageTree());\n            if (getOption('autoPrint')) {\n                var nameTree = {};\n                nameTree.JavaScript = new PDFDictionary({\n                    Names: [\n                        new PDFString('JS'),\n                        self.attach(new PDFDictionary({\n                            S: _('JavaScript'),\n                            JS: new PDFString('print(true);')\n                        }))\n                    ]\n                });\n                catalog.props.Names = new PDFDictionary(nameTree);\n            }\n            catalog.setPages(pageTree);\n            var info = self.attach(new PDFDictionary({\n                Producer: new PDFString(getOption('producer', 'Kendo UI PDF Generator')),\n                Title: new PDFString(getOption('title', '')),\n                Author: new PDFString(getOption('author', '')),\n                Subject: new PDFString(getOption('subject', '')),\n                Keywords: new PDFString(getOption('keywords', '')),\n                Creator: new PDFString(getOption('creator', 'Kendo UI PDF Generator')),\n                CreationDate: getOption('date', new Date())\n            }));\n            self.addPage = function (options) {\n                var paperOptions = getPaperOptions(function (name, defval) {\n                    return options && options[name] != null ? options[name] : defval;\n                });\n                var paperSize = paperOptions.paperSize;\n                var margin = paperOptions.margin;\n                var contentWidth = paperSize[0];\n                var contentHeight = paperSize[1];\n                if (margin) {\n                    contentWidth -= margin.left + margin.right;\n                    contentHeight -= margin.top + margin.bottom;\n                }\n                var content = new PDFStream(makeOutput(), null, true);\n                var props = {\n                    Contents: self.attach(content),\n                    Parent: pageTree,\n                    MediaBox: [\n                        0,\n                        0,\n                        paperSize[0],\n                        paperSize[1]\n                    ]\n                };\n                var page = new PDFPage(self, props);\n                page._content = content;\n                pageTree.addPage(self.attach(page));\n                page.transform(1, 0, 0, -1, 0, paperSize[1]);\n                if (margin) {\n                    page.translate(margin.left, margin.top);\n                    page.rect(0, 0, contentWidth, contentHeight);\n                    page.clip();\n                }\n                self.pages.push(page);\n                return page;\n            };\n            self.render = function () {\n                var i;\n                out('%PDF-1.4', NL, '%ÂÁÚÏÎ', NL, NL);\n                for (i = 0; i < objects.length; ++i) {\n                    objects[i].renderFull(out);\n                    out(NL, NL);\n                }\n                var xrefOffset = out.offset();\n                out('xref', NL, 0, ' ', objects.length + 1, NL);\n                out('0000000000 65535 f ', NL);\n                for (i = 0; i < objects.length; ++i) {\n                    out(zeropad(objects[i]._offset, 10), ' 00000 n ', NL);\n                }\n                out(NL);\n                out('trailer', NL);\n                out(new PDFDictionary({\n                    Size: objects.length + 1,\n                    Root: catalog,\n                    Info: info\n                }), NL, NL);\n                out('startxref', NL, xrefOffset, NL);\n                out('%%EOF', NL);\n                return out.stream().offset(0);\n            };\n        }\n        var FONT_CACHE = {\n            'Times-Roman': true,\n            'Times-Bold': true,\n            'Times-Italic': true,\n            'Times-BoldItalic': true,\n            'Helvetica': true,\n            'Helvetica-Bold': true,\n            'Helvetica-Oblique': true,\n            'Helvetica-BoldOblique': true,\n            'Courier': true,\n            'Courier-Bold': true,\n            'Courier-Oblique': true,\n            'Courier-BoldOblique': true,\n            'Symbol': true,\n            'ZapfDingbats': true\n        };\n        function loadBinary(url, cont) {\n            var m;\n            if (browser.msie && (m = /^data:.*?;base64,/i.exec(url))) {\n                cont(base64ToUint8Array(url.substr(m[0].length)));\n                return;\n            }\n            function error() {\n                if (window.console) {\n                    if (window.console.error) {\n                        window.console.error('Cannot load URL: %s', url);\n                    } else {\n                        window.console.log('Cannot load URL: %s', url);\n                    }\n                }\n                cont(null);\n            }\n            var req = new XMLHttpRequest();\n            req.open('GET', url, true);\n            if (HAS_TYPED_ARRAYS) {\n                req.responseType = 'arraybuffer';\n            }\n            req.onload = function () {\n                if (req.status == 200 || req.status == 304) {\n                    if (HAS_TYPED_ARRAYS) {\n                        cont(new Uint8Array(req.response));\n                    } else {\n                        cont(new window.VBArray(req.responseBody).toArray());\n                    }\n                } else {\n                    error();\n                }\n            };\n            req.onerror = error;\n            req.send(null);\n        }\n        function loadFont(url, cont) {\n            var font = FONT_CACHE[url];\n            if (font) {\n                cont(font);\n            } else {\n                loadBinary(url, function (data) {\n                    if (data == null) {\n                        throw new Error('Cannot load font from ' + url);\n                    } else {\n                        var font = new TTFFont(data);\n                        FONT_CACHE[url] = font;\n                        cont(font);\n                    }\n                });\n            }\n        }\n        var IMAGE_CACHE = {};\n        function clearImageCache() {\n            IMAGE_CACHE = {};\n        }\n        function loadImage(url, size, cont) {\n            var img = IMAGE_CACHE[url], bloburl, blob;\n            if (img) {\n                cont(img);\n            } else {\n                img = new Image();\n                if (!/^data:/i.test(url)) {\n                    img.crossOrigin = 'Anonymous';\n                }\n                if (HAS_TYPED_ARRAYS && !/^data:/i.test(url)) {\n                    var xhr = new XMLHttpRequest();\n                    xhr.onload = function () {\n                        blob = xhr.response;\n                        bloburl = URL.createObjectURL(blob);\n                        _load(bloburl);\n                    };\n                    xhr.onerror = _onerror;\n                    xhr.open('GET', url, true);\n                    xhr.responseType = 'blob';\n                    xhr.send();\n                } else {\n                    _load(url);\n                }\n            }\n            function _load(url) {\n                img.src = url;\n                if (img.complete && !browser.msie) {\n                    _onload();\n                } else {\n                    img.onload = _onload;\n                    img.onerror = _onerror;\n                }\n            }\n            function _trycanvas() {\n                if (!size) {\n                    size = {\n                        width: img.width,\n                        height: img.height\n                    };\n                }\n                var canvas = document.createElement('canvas');\n                canvas.width = size.width;\n                canvas.height = size.height;\n                var ctx = canvas.getContext('2d');\n                ctx.drawImage(img, 0, 0, size.width, size.height);\n                var imgdata;\n                try {\n                    imgdata = ctx.getImageData(0, 0, size.width, size.height);\n                } catch (ex) {\n                    _onerror();\n                    return;\n                } finally {\n                    if (bloburl) {\n                        URL.revokeObjectURL(bloburl);\n                    }\n                }\n                var hasAlpha = false, rgb = BinaryStream(), alpha = BinaryStream();\n                var rawbytes = imgdata.data;\n                var i = 0;\n                while (i < rawbytes.length) {\n                    rgb.writeByte(rawbytes[i++]);\n                    rgb.writeByte(rawbytes[i++]);\n                    rgb.writeByte(rawbytes[i++]);\n                    var a = rawbytes[i++];\n                    if (a < 255) {\n                        hasAlpha = true;\n                    }\n                    alpha.writeByte(a);\n                }\n                if (hasAlpha) {\n                    img = new PDFRawImage(size.width, size.height, rgb, alpha);\n                } else {\n                    var data = canvas.toDataURL('image/jpeg');\n                    data = data.substr(data.indexOf(';base64,') + 8);\n                    var stream = BinaryStream();\n                    stream.writeBase64(data);\n                    img = new PDFJpegImage(stream);\n                }\n                cont(IMAGE_CACHE[url] = img);\n            }\n            function _onerror() {\n                cont(IMAGE_CACHE[url] = 'ERROR');\n            }\n            function _onload() {\n                if (size) {\n                    if (size.width >= img.width || size.height >= img.height) {\n                        size = null;\n                    }\n                }\n                if (!size && blob && /^image\\/jpe?g$/i.test(blob.type)) {\n                    var reader = new FileReader();\n                    reader.onload = function () {\n                        try {\n                            var img = new PDFJpegImage(BinaryStream(new Uint8Array(this.result)));\n                            URL.revokeObjectURL(bloburl);\n                            cont(IMAGE_CACHE[url] = img);\n                        } catch (ex) {\n                            _trycanvas();\n                        }\n                    };\n                    reader.readAsArrayBuffer(blob);\n                } else {\n                    _trycanvas();\n                }\n            }\n        }\n        function manyLoader(loadOne) {\n            return function (urls, callback) {\n                var n = urls.length, i = n;\n                if (n === 0) {\n                    return callback();\n                }\n                function next() {\n                    if (--n === 0) {\n                        callback();\n                    }\n                }\n                while (i-- > 0) {\n                    loadOne(urls[i], next);\n                }\n            };\n        }\n        var loadFonts = manyLoader(loadFont);\n        var loadImages = function (images, callback) {\n            var urls = Object.keys(images), n = urls.length;\n            if (n === 0) {\n                return callback();\n            }\n            function next() {\n                if (--n === 0) {\n                    callback();\n                }\n            }\n            urls.forEach(function (url) {\n                loadImage(url, images[url], next);\n            });\n        };\n        PDFDocument.prototype = {\n            loadFonts: loadFonts,\n            loadImages: loadImages,\n            getFont: function (url) {\n                var font = this.FONTS[url];\n                if (!font) {\n                    font = FONT_CACHE[url];\n                    if (!font) {\n                        throw new Error('Font ' + url + ' has not been loaded');\n                    }\n                    if (font === true) {\n                        font = this.attach(new PDFStandardFont(url));\n                    } else {\n                        font = this.attach(new PDFFont(this, font));\n                    }\n                    this.FONTS[url] = font;\n                }\n                return font;\n            },\n            getImage: function (url) {\n                var img = this.IMAGES[url];\n                if (!img) {\n                    img = IMAGE_CACHE[url];\n                    if (!img) {\n                        throw new Error('Image ' + url + ' has not been loaded');\n                    }\n                    if (img === 'ERROR') {\n                        return null;\n                    }\n                    img = this.IMAGES[url] = this.attach(img.asStream(this));\n                }\n                return img;\n            },\n            getOpacityGS: function (opacity, forStroke) {\n                var id = parseFloat(opacity).toFixed(3);\n                opacity = parseFloat(id);\n                id += forStroke ? 'S' : 'F';\n                var cache = this._opacityGSCache || (this._opacityGSCache = {});\n                var gs = cache[id];\n                if (!gs) {\n                    var props = { Type: _('ExtGState') };\n                    if (forStroke) {\n                        props.CA = opacity;\n                    } else {\n                        props.ca = opacity;\n                    }\n                    gs = this.attach(new PDFDictionary(props));\n                    gs._resourceName = _('GS' + ++RESOURCE_COUNTER);\n                    cache[id] = gs;\n                }\n                return gs;\n            },\n            dict: function (props) {\n                return new PDFDictionary(props);\n            },\n            name: function (str) {\n                return _(str);\n            },\n            stream: function (props, content) {\n                return new PDFStream(content, props);\n            }\n        };\n        function pad(str, len, ch) {\n            while (str.length < len) {\n                str = ch + str;\n            }\n            return str;\n        }\n        function zeropad(n, len) {\n            return pad(String(n), len, '0');\n        }\n        function hasOwnProperty(obj, key) {\n            return Object.prototype.hasOwnProperty.call(obj, key);\n        }\n        var isArray = Array.isArray || function (obj) {\n            return obj instanceof Array;\n        };\n        function isDate(obj) {\n            return obj instanceof Date;\n        }\n        function renderArray(a, out) {\n            out('[');\n            if (a.length > 0) {\n                out.withIndent(function () {\n                    for (var i = 0; i < a.length; ++i) {\n                        if (i > 0 && i % 8 === 0) {\n                            out.indent(a[i]);\n                        } else {\n                            out(' ', a[i]);\n                        }\n                    }\n                });\n            }\n            out(' ]');\n        }\n        function renderDate(date, out) {\n            out('(D:', zeropad(date.getUTCFullYear(), 4), zeropad(date.getUTCMonth() + 1, 2), zeropad(date.getUTCDate(), 2), zeropad(date.getUTCHours(), 2), zeropad(date.getUTCMinutes(), 2), zeropad(date.getUTCSeconds(), 2), 'Z)');\n        }\n        function mm2pt(mm) {\n            return mm * (72 / 25.4);\n        }\n        function cm2pt(cm) {\n            return mm2pt(cm * 10);\n        }\n        function in2pt(inch) {\n            return inch * 72;\n        }\n        function unitsToPoints(x, def) {\n            if (typeof x == 'number') {\n                return x;\n            }\n            if (typeof x == 'string') {\n                var m;\n                m = /^\\s*([0-9.]+)\\s*(mm|cm|in|pt)\\s*$/.exec(x);\n                if (m) {\n                    var num = parseFloat(m[1]);\n                    if (!isNaN(num)) {\n                        if (m[2] == 'pt') {\n                            return num;\n                        }\n                        return {\n                            'mm': mm2pt,\n                            'cm': cm2pt,\n                            'in': in2pt\n                        }[m[2]](num);\n                    }\n                }\n            }\n            if (def != null) {\n                return def;\n            }\n            throw new Error('Can\\'t parse unit: ' + x);\n        }\n        function PDFValue() {\n        }\n        PDFValue.prototype.beforeRender = function () {\n        };\n        function defclass(Ctor, proto, Base) {\n            if (!Base) {\n                Base = PDFValue;\n            }\n            Ctor.prototype = new Base();\n            for (var i in proto) {\n                if (hasOwnProperty(proto, i)) {\n                    Ctor.prototype[i] = proto[i];\n                }\n            }\n            return Ctor;\n        }\n        var PDFString = defclass(function PDFString(value) {\n            this.value = value;\n        }, {\n            render: function (out) {\n                var txt = '', val = this.value;\n                for (var i = 0; i < val.length; ++i) {\n                    txt += String.fromCharCode(val.charCodeAt(i) & 255);\n                }\n                out('(', txt.replace(/([\\(\\)\\\\])/g, '\\\\$1'), ')');\n            },\n            toString: function () {\n                return this.value;\n            }\n        });\n        var PDFHexString = defclass(function PDFHexString(value) {\n            this.value = value;\n        }, {\n            render: function (out) {\n                var this$1 = this;\n                out('<');\n                for (var i = 0; i < this.value.length; ++i) {\n                    out(zeropad(this$1.value.charCodeAt(i).toString(16), 4));\n                }\n                out('>');\n            }\n        }, PDFString);\n        var PDFName = defclass(function PDFName(name) {\n            this.name = name;\n        }, {\n            render: function (out) {\n                out('/' + this.escape());\n            },\n            escape: function () {\n                return this.name.replace(/[^\\x21-\\x7E]/g, function (c) {\n                    return '#' + zeropad(c.charCodeAt(0).toString(16), 2);\n                });\n            },\n            toString: function () {\n                return this.name;\n            }\n        });\n        var PDFName_cache = {};\n        PDFName.get = _;\n        function _(name) {\n            if (hasOwnProperty(PDFName_cache, name)) {\n                return PDFName_cache[name];\n            }\n            return PDFName_cache[name] = new PDFName(name);\n        }\n        var PDFDictionary = defclass(function PDFDictionary(props) {\n            this.props = props;\n        }, {\n            render: function (out) {\n                var props = this.props, empty = true;\n                out('<<');\n                out.withIndent(function () {\n                    for (var i in props) {\n                        if (hasOwnProperty(props, i) && !/^_/.test(i)) {\n                            empty = false;\n                            out.indent(_(i), ' ', props[i]);\n                        }\n                    }\n                });\n                if (!empty) {\n                    out.indent();\n                }\n                out('>>');\n            }\n        });\n        var PDFStream = defclass(function PDFStream(data, props, compress) {\n            if (typeof data == 'string') {\n                var tmp = BinaryStream();\n                tmp.write(data);\n                data = tmp;\n            }\n            this.data = data;\n            this.props = props || {};\n            this.compress = compress;\n        }, {\n            render: function (out) {\n                var data = this.data.get(), props = this.props;\n                if (this.compress && kendoPdf.supportsDeflate()) {\n                    if (!props.Filter) {\n                        props.Filter = [];\n                    } else if (!(props.Filter instanceof Array)) {\n                        props.Filter = [props.Filter];\n                    }\n                    props.Filter.unshift(_('FlateDecode'));\n                    data = kendoPdf.deflate(data);\n                }\n                props.Length = data.length;\n                out(new PDFDictionary(props), ' stream', NL);\n                out.writeData(data);\n                out(NL, 'endstream');\n            }\n        });\n        var PDFCatalog = defclass(function PDFCatalog() {\n            this.props = { Type: _('Catalog') };\n        }, {\n            setPages: function (pagesObj) {\n                this.props.Pages = pagesObj;\n            }\n        }, PDFDictionary);\n        var PDFPageTree = defclass(function PDFPageTree() {\n            this.props = {\n                Type: _('Pages'),\n                Kids: [],\n                Count: 0\n            };\n        }, {\n            addPage: function (pageObj) {\n                this.props.Kids.push(pageObj);\n                this.props.Count++;\n            }\n        }, PDFDictionary);\n        var SOF_CODES = [\n            192,\n            193,\n            194,\n            195,\n            197,\n            198,\n            199,\n            201,\n            202,\n            203,\n            205,\n            206,\n            207\n        ];\n        function PDFJpegImage(data) {\n            data.offset(0);\n            var width, height, colorSpace, bitsPerComponent;\n            var soi = data.readShort();\n            if (soi != 65496) {\n                throw new Error('Invalid JPEG image');\n            }\n            while (!data.eof()) {\n                var ff = data.readByte();\n                if (ff != 255) {\n                    throw new Error('Invalid JPEG image');\n                }\n                var marker = data.readByte();\n                var length = data.readShort();\n                if (SOF_CODES.indexOf(marker) >= 0) {\n                    bitsPerComponent = data.readByte();\n                    height = data.readShort();\n                    width = data.readShort();\n                    colorSpace = data.readByte();\n                    break;\n                }\n                data.skip(length - 2);\n            }\n            if (colorSpace == null) {\n                throw new Error('Invalid JPEG image');\n            }\n            var props = {\n                Type: _('XObject'),\n                Subtype: _('Image'),\n                Width: width,\n                Height: height,\n                BitsPerComponent: bitsPerComponent,\n                Filter: _('DCTDecode')\n            };\n            switch (colorSpace) {\n            case 1:\n                props.ColorSpace = _('DeviceGray');\n                break;\n            case 3:\n                props.ColorSpace = _('DeviceRGB');\n                break;\n            case 4:\n                props.ColorSpace = _('DeviceCMYK');\n                props.Decode = [\n                    1,\n                    0,\n                    1,\n                    0,\n                    1,\n                    0,\n                    1,\n                    0\n                ];\n                break;\n            }\n            this.asStream = function () {\n                data.offset(0);\n                var stream = new PDFStream(data, props);\n                stream._resourceName = _('I' + ++RESOURCE_COUNTER);\n                return stream;\n            };\n        }\n        function PDFRawImage(width, height, rgb, alpha) {\n            this.asStream = function (pdf) {\n                var mask = new PDFStream(alpha, {\n                    Type: _('XObject'),\n                    Subtype: _('Image'),\n                    Width: width,\n                    Height: height,\n                    BitsPerComponent: 8,\n                    ColorSpace: _('DeviceGray')\n                }, true);\n                var stream = new PDFStream(rgb, {\n                    Type: _('XObject'),\n                    Subtype: _('Image'),\n                    Width: width,\n                    Height: height,\n                    BitsPerComponent: 8,\n                    ColorSpace: _('DeviceRGB'),\n                    SMask: pdf.attach(mask)\n                }, true);\n                stream._resourceName = _('I' + ++RESOURCE_COUNTER);\n                return stream;\n            };\n        }\n        var PDFStandardFont = defclass(function PDFStandardFont(name) {\n            this.props = {\n                Type: _('Font'),\n                Subtype: _('Type1'),\n                BaseFont: _(name)\n            };\n            this._resourceName = _('F' + ++RESOURCE_COUNTER);\n        }, {\n            encodeText: function (str) {\n                return new PDFString(String(str));\n            }\n        }, PDFDictionary);\n        var PDFFont = defclass(function PDFFont(pdf, font, props) {\n            props = this.props = props || {};\n            props.Type = _('Font');\n            props.Subtype = _('Type0');\n            props.Encoding = _('Identity-H');\n            this._pdf = pdf;\n            this._font = font;\n            this._sub = font.makeSubset();\n            this._resourceName = _('F' + ++RESOURCE_COUNTER);\n            var head = font.head;\n            this.name = font.psName;\n            var scale = this.scale = font.scale;\n            this.bbox = [\n                head.xMin * scale,\n                head.yMin * scale,\n                head.xMax * scale,\n                head.yMax * scale\n            ];\n            this.italicAngle = font.post.italicAngle;\n            this.ascent = font.ascent * scale;\n            this.descent = font.descent * scale;\n            this.lineGap = font.lineGap * scale;\n            this.capHeight = font.os2.capHeight || this.ascent;\n            this.xHeight = font.os2.xHeight || 0;\n            this.stemV = 0;\n            this.familyClass = (font.os2.familyClass || 0) >> 8;\n            this.isSerif = this.familyClass >= 1 && this.familyClass <= 7;\n            this.isScript = this.familyClass == 10;\n            this.flags = (font.post.isFixedPitch ? 1 : 0) | (this.isSerif ? 1 << 1 : 0) | (this.isScript ? 1 << 3 : 0) | (this.italicAngle !== 0 ? 1 << 6 : 0) | 1 << 5;\n        }, {\n            encodeText: function (text) {\n                return new PDFHexString(this._sub.encodeText(String(text)));\n            },\n            getTextWidth: function (fontSize, text) {\n                var this$1 = this;\n                var width = 0, codeMap = this._font.cmap.codeMap;\n                for (var i = 0; i < text.length; ++i) {\n                    var glyphId = codeMap[text.charCodeAt(i)];\n                    width += this$1._font.widthOfGlyph(glyphId || 0);\n                }\n                return width * fontSize / 1000;\n            },\n            beforeRender: function () {\n                var self = this;\n                var sub = self._sub;\n                var data = sub.render();\n                var fontStream = new PDFStream(BinaryStream(data), { Length1: data.length }, true);\n                var descriptor = self._pdf.attach(new PDFDictionary({\n                    Type: _('FontDescriptor'),\n                    FontName: _(self._sub.psName),\n                    FontBBox: self.bbox,\n                    Flags: self.flags,\n                    StemV: self.stemV,\n                    ItalicAngle: self.italicAngle,\n                    Ascent: self.ascent,\n                    Descent: self.descent,\n                    CapHeight: self.capHeight,\n                    XHeight: self.xHeight,\n                    FontFile2: self._pdf.attach(fontStream)\n                }));\n                var cmap = sub.ncid2ogid;\n                var firstChar = sub.firstChar;\n                var lastChar = sub.lastChar;\n                var charWidths = [];\n                (function loop(i, chunk) {\n                    if (i <= lastChar) {\n                        var gid = cmap[i];\n                        if (gid == null) {\n                            loop(i + 1);\n                        } else {\n                            if (!chunk) {\n                                charWidths.push(i, chunk = []);\n                            }\n                            chunk.push(self._font.widthOfGlyph(gid));\n                            loop(i + 1, chunk);\n                        }\n                    }\n                }(firstChar));\n                var descendant = new PDFDictionary({\n                    Type: _('Font'),\n                    Subtype: _('CIDFontType2'),\n                    BaseFont: _(self._sub.psName),\n                    CIDSystemInfo: new PDFDictionary({\n                        Registry: new PDFString('Adobe'),\n                        Ordering: new PDFString('Identity'),\n                        Supplement: 0\n                    }),\n                    FontDescriptor: descriptor,\n                    FirstChar: firstChar,\n                    LastChar: lastChar,\n                    DW: Math.round(self._font.widthOfGlyph(0)),\n                    W: charWidths,\n                    CIDToGIDMap: self._pdf.attach(self._makeCidToGidMap())\n                });\n                var dict = self.props;\n                dict.BaseFont = _(self._sub.psName);\n                dict.DescendantFonts = [self._pdf.attach(descendant)];\n                var unimap = new PDFToUnicodeCmap(firstChar, lastChar, sub.subset);\n                var unimapStream = new PDFStream(makeOutput(), null, true);\n                unimapStream.data(unimap);\n                dict.ToUnicode = self._pdf.attach(unimapStream);\n            },\n            _makeCidToGidMap: function () {\n                return new PDFStream(BinaryStream(this._sub.cidToGidMap()), null, true);\n            }\n        }, PDFDictionary);\n        var PDFToUnicodeCmap = defclass(function PDFUnicodeCMap(firstChar, lastChar, map) {\n            this.firstChar = firstChar;\n            this.lastChar = lastChar;\n            this.map = map;\n        }, {\n            render: function (out) {\n                out.indent('/CIDInit /ProcSet findresource begin');\n                out.indent('12 dict begin');\n                out.indent('begincmap');\n                out.indent('/CIDSystemInfo <<');\n                out.indent('  /Registry (Adobe)');\n                out.indent('  /Ordering (UCS)');\n                out.indent('  /Supplement 0');\n                out.indent('>> def');\n                out.indent('/CMapName /Adobe-Identity-UCS def');\n                out.indent('/CMapType 2 def');\n                out.indent('1 begincodespacerange');\n                out.indent('  <0000><ffff>');\n                out.indent('endcodespacerange');\n                var self = this;\n                out.indent(self.lastChar - self.firstChar + 1, ' beginbfchar');\n                out.withIndent(function () {\n                    for (var code = self.firstChar; code <= self.lastChar; ++code) {\n                        var unicode = self.map[code];\n                        var str = ucs2encode([unicode]);\n                        out.indent('<', zeropad(code.toString(16), 4), '>', '<');\n                        for (var i = 0; i < str.length; ++i) {\n                            out(zeropad(str.charCodeAt(i).toString(16), 4));\n                        }\n                        out('>');\n                    }\n                });\n                out.indent('endbfchar');\n                out.indent('endcmap');\n                out.indent('CMapName currentdict /CMap defineresource pop');\n                out.indent('end');\n                out.indent('end');\n            }\n        });\n        function makeHash(a) {\n            return a.map(function (x) {\n                return isArray(x) ? makeHash(x) : typeof x == 'number' ? (Math.round(x * 1000) / 1000).toFixed(3) : x;\n            }).join(' ');\n        }\n        function cacheColorGradientFunction(pdf, r1, g1, b1, r2, g2, b2) {\n            var hash = makeHash([\n                r1,\n                g1,\n                b1,\n                r2,\n                g2,\n                b2\n            ]);\n            var func = pdf.GRAD_COL_FUNCTIONS[hash];\n            if (!func) {\n                func = pdf.GRAD_COL_FUNCTIONS[hash] = pdf.attach(new PDFDictionary({\n                    FunctionType: 2,\n                    Domain: [\n                        0,\n                        1\n                    ],\n                    Range: [\n                        0,\n                        1,\n                        0,\n                        1,\n                        0,\n                        1\n                    ],\n                    N: 1,\n                    C0: [\n                        r1,\n                        g1,\n                        b1\n                    ],\n                    C1: [\n                        r2,\n                        g2,\n                        b2\n                    ]\n                }));\n            }\n            return func;\n        }\n        function cacheOpacityGradientFunction(pdf, a1, a2) {\n            var hash = makeHash([\n                a1,\n                a2\n            ]);\n            var func = pdf.GRAD_OPC_FUNCTIONS[hash];\n            if (!func) {\n                func = pdf.GRAD_OPC_FUNCTIONS[hash] = pdf.attach(new PDFDictionary({\n                    FunctionType: 2,\n                    Domain: [\n                        0,\n                        1\n                    ],\n                    Range: [\n                        0,\n                        1\n                    ],\n                    N: 1,\n                    C0: [a1],\n                    C1: [a2]\n                }));\n            }\n            return func;\n        }\n        function makeGradientFunctions(pdf, stops) {\n            var hasAlpha = false;\n            var opacities = [];\n            var colors = [];\n            var offsets = [];\n            var encode = [];\n            var i, prev, cur, prevColor, curColor;\n            for (i = 1; i < stops.length; ++i) {\n                prev = stops[i - 1];\n                cur = stops[i];\n                prevColor = prev.color;\n                curColor = cur.color;\n                colors.push(cacheColorGradientFunction(pdf, prevColor.r, prevColor.g, prevColor.b, curColor.r, curColor.g, curColor.b));\n                if (prevColor.a < 1 || curColor.a < 1) {\n                    hasAlpha = true;\n                }\n                offsets.push(cur.offset);\n                encode.push(0, 1);\n            }\n            if (hasAlpha) {\n                for (i = 1; i < stops.length; ++i) {\n                    prev = stops[i - 1];\n                    cur = stops[i];\n                    prevColor = prev.color;\n                    curColor = cur.color;\n                    opacities.push(cacheOpacityGradientFunction(pdf, prevColor.a, curColor.a));\n                }\n            }\n            offsets.pop();\n            return {\n                hasAlpha: hasAlpha,\n                colors: assemble(colors),\n                opacities: hasAlpha ? assemble(opacities) : null\n            };\n            function assemble(funcs) {\n                if (funcs.length == 1) {\n                    return funcs[0];\n                }\n                return {\n                    FunctionType: 3,\n                    Functions: funcs,\n                    Domain: [\n                        0,\n                        1\n                    ],\n                    Bounds: offsets,\n                    Encode: encode\n                };\n            }\n        }\n        function cacheColorGradient(pdf, isRadial, stops, coords, funcs, box) {\n            var shading, hash;\n            if (!box) {\n                var a = [isRadial].concat(coords);\n                stops.forEach(function (x) {\n                    a.push(x.offset, x.color.r, x.color.g, x.color.b);\n                });\n                hash = makeHash(a);\n                shading = pdf.GRAD_COL[hash];\n            }\n            if (!shading) {\n                shading = new PDFDictionary({\n                    Type: _('Shading'),\n                    ShadingType: isRadial ? 3 : 2,\n                    ColorSpace: _('DeviceRGB'),\n                    Coords: coords,\n                    Domain: [\n                        0,\n                        1\n                    ],\n                    Function: funcs,\n                    Extend: [\n                        true,\n                        true\n                    ]\n                });\n                pdf.attach(shading);\n                shading._resourceName = 'S' + ++RESOURCE_COUNTER;\n                if (hash) {\n                    pdf.GRAD_COL[hash] = shading;\n                }\n            }\n            return shading;\n        }\n        function cacheOpacityGradient(pdf, isRadial, stops, coords, funcs, box) {\n            var opacity, hash;\n            if (!box) {\n                var a = [isRadial].concat(coords);\n                stops.forEach(function (x) {\n                    a.push(x.offset, x.color.a);\n                });\n                hash = makeHash(a);\n                opacity = pdf.GRAD_OPC[hash];\n            }\n            if (!opacity) {\n                opacity = new PDFDictionary({\n                    Type: _('ExtGState'),\n                    AIS: false,\n                    CA: 1,\n                    ca: 1,\n                    SMask: {\n                        Type: _('Mask'),\n                        S: _('Luminosity'),\n                        G: pdf.attach(new PDFStream('/a0 gs /s0 sh', {\n                            Type: _('XObject'),\n                            Subtype: _('Form'),\n                            FormType: 1,\n                            BBox: box ? [\n                                box.left,\n                                box.top + box.height,\n                                box.left + box.width,\n                                box.top\n                            ] : [\n                                0,\n                                1,\n                                1,\n                                0\n                            ],\n                            Group: {\n                                Type: _('Group'),\n                                S: _('Transparency'),\n                                CS: _('DeviceGray'),\n                                I: true\n                            },\n                            Resources: {\n                                ExtGState: {\n                                    a0: {\n                                        CA: 1,\n                                        ca: 1\n                                    }\n                                },\n                                Shading: {\n                                    s0: {\n                                        ColorSpace: _('DeviceGray'),\n                                        Coords: coords,\n                                        Domain: [\n                                            0,\n                                            1\n                                        ],\n                                        ShadingType: isRadial ? 3 : 2,\n                                        Function: funcs,\n                                        Extend: [\n                                            true,\n                                            true\n                                        ]\n                                    }\n                                }\n                            }\n                        }))\n                    }\n                });\n                pdf.attach(opacity);\n                opacity._resourceName = 'O' + ++RESOURCE_COUNTER;\n                if (hash) {\n                    pdf.GRAD_OPC[hash] = opacity;\n                }\n            }\n            return opacity;\n        }\n        function cacheGradient(pdf, gradient, box) {\n            var isRadial = gradient.type == 'radial';\n            var funcs = makeGradientFunctions(pdf, gradient.stops);\n            var coords = isRadial ? [\n                gradient.start.x,\n                gradient.start.y,\n                gradient.start.r,\n                gradient.end.x,\n                gradient.end.y,\n                gradient.end.r\n            ] : [\n                gradient.start.x,\n                gradient.start.y,\n                gradient.end.x,\n                gradient.end.y\n            ];\n            var shading = cacheColorGradient(pdf, isRadial, gradient.stops, coords, funcs.colors, gradient.userSpace && box);\n            var opacity = funcs.hasAlpha ? cacheOpacityGradient(pdf, isRadial, gradient.stops, coords, funcs.opacities, gradient.userSpace && box) : null;\n            return {\n                hasAlpha: funcs.hasAlpha,\n                shading: shading,\n                opacity: opacity\n            };\n        }\n        var PDFPage = defclass(function PDFPage(pdf, props) {\n            this._pdf = pdf;\n            this._rcount = 0;\n            this._textMode = false;\n            this._fontResources = {};\n            this._gsResources = {};\n            this._xResources = {};\n            this._patResources = {};\n            this._shResources = {};\n            this._opacity = 1;\n            this._matrix = [\n                1,\n                0,\n                0,\n                1,\n                0,\n                0\n            ];\n            this._annotations = [];\n            this._font = null;\n            this._fontSize = null;\n            this._contextStack = [];\n            props = this.props = props || {};\n            props.Type = _('Page');\n            props.ProcSet = [\n                _('PDF'),\n                _('Text'),\n                _('ImageB'),\n                _('ImageC'),\n                _('ImageI')\n            ];\n            props.Resources = new PDFDictionary({\n                Font: new PDFDictionary(this._fontResources),\n                ExtGState: new PDFDictionary(this._gsResources),\n                XObject: new PDFDictionary(this._xResources),\n                Pattern: new PDFDictionary(this._patResources),\n                Shading: new PDFDictionary(this._shResources)\n            });\n            props.Annots = this._annotations;\n        }, {\n            _out: function () {\n                this._content.data.apply(null, arguments);\n            },\n            transform: function (a, b, c, d, e, f) {\n                if (!isIdentityMatrix(arguments)) {\n                    this._matrix = mmul(arguments, this._matrix);\n                    this._out(a, ' ', b, ' ', c, ' ', d, ' ', e, ' ', f, ' cm');\n                    this._out(NL);\n                }\n            },\n            translate: function (dx, dy) {\n                this.transform(1, 0, 0, 1, dx, dy);\n            },\n            scale: function (sx, sy) {\n                this.transform(sx, 0, 0, sy, 0, 0);\n            },\n            rotate: function (angle) {\n                var cos = Math.cos(angle), sin = Math.sin(angle);\n                this.transform(cos, sin, -sin, cos, 0, 0);\n            },\n            beginText: function () {\n                this._textMode = true;\n                this._out('BT', NL);\n            },\n            endText: function () {\n                this._textMode = false;\n                this._out('ET', NL);\n            },\n            _requireTextMode: function () {\n                if (!this._textMode) {\n                    throw new Error('Text mode required; call page.beginText() first');\n                }\n            },\n            _requireFont: function () {\n                if (!this._font) {\n                    throw new Error('No font selected; call page.setFont() first');\n                }\n            },\n            setFont: function (font, size) {\n                this._requireTextMode();\n                if (font == null) {\n                    font = this._font;\n                } else if (!(font instanceof PDFFont)) {\n                    font = this._pdf.getFont(font);\n                }\n                if (size == null) {\n                    size = this._fontSize;\n                }\n                this._fontResources[font._resourceName] = font;\n                this._font = font;\n                this._fontSize = size;\n                this._out(font._resourceName, ' ', size, ' Tf', NL);\n            },\n            setTextLeading: function (size) {\n                this._requireTextMode();\n                this._out(size, ' TL', NL);\n            },\n            setTextRenderingMode: function (mode) {\n                this._requireTextMode();\n                this._out(mode, ' Tr', NL);\n            },\n            showText: function (text, requestedWidth) {\n                this._requireFont();\n                if (text.length > 1 && requestedWidth && this._font instanceof PDFFont) {\n                    var outputWidth = this._font.getTextWidth(this._fontSize, text);\n                    var scale = requestedWidth / outputWidth * 100;\n                    this._out(scale, ' Tz ');\n                }\n                this._out(this._font.encodeText(text), ' Tj', NL);\n            },\n            showTextNL: function (text) {\n                this._requireFont();\n                this._out(this._font.encodeText(text), ' \\'', NL);\n            },\n            addLink: function (uri, box) {\n                var ll = this._toPage({\n                    x: box.left,\n                    y: box.bottom\n                });\n                var ur = this._toPage({\n                    x: box.right,\n                    y: box.top\n                });\n                this._annotations.push(new PDFDictionary({\n                    Type: _('Annot'),\n                    Subtype: _('Link'),\n                    Rect: [\n                        ll.x,\n                        ll.y,\n                        ur.x,\n                        ur.y\n                    ],\n                    Border: [\n                        0,\n                        0,\n                        0\n                    ],\n                    A: new PDFDictionary({\n                        Type: _('Action'),\n                        S: _('URI'),\n                        URI: new PDFString(uri)\n                    })\n                }));\n            },\n            setStrokeColor: function (r, g, b) {\n                this._out(r, ' ', g, ' ', b, ' RG', NL);\n            },\n            setOpacity: function (opacity) {\n                this.setFillOpacity(opacity);\n                this.setStrokeOpacity(opacity);\n                this._opacity *= opacity;\n            },\n            setStrokeOpacity: function (opacity) {\n                if (opacity < 1) {\n                    var gs = this._pdf.getOpacityGS(this._opacity * opacity, true);\n                    this._gsResources[gs._resourceName] = gs;\n                    this._out(gs._resourceName, ' gs', NL);\n                }\n            },\n            setFillColor: function (r, g, b) {\n                this._out(r, ' ', g, ' ', b, ' rg', NL);\n            },\n            setFillOpacity: function (opacity) {\n                if (opacity < 1) {\n                    var gs = this._pdf.getOpacityGS(this._opacity * opacity, false);\n                    this._gsResources[gs._resourceName] = gs;\n                    this._out(gs._resourceName, ' gs', NL);\n                }\n            },\n            gradient: function (gradient, box) {\n                this.save();\n                this.rect(box.left, box.top, box.width, box.height);\n                this.clip();\n                if (!gradient.userSpace) {\n                    this.transform(box.width, 0, 0, box.height, box.left, box.top);\n                }\n                var g = cacheGradient(this._pdf, gradient, box);\n                var sname = g.shading._resourceName, oname;\n                this._shResources[sname] = g.shading;\n                if (g.hasAlpha) {\n                    oname = g.opacity._resourceName;\n                    this._gsResources[oname] = g.opacity;\n                    this._out('/' + oname + ' gs ');\n                }\n                this._out('/' + sname + ' sh', NL);\n                this.restore();\n            },\n            setDashPattern: function (dashArray, dashPhase) {\n                this._out(dashArray, ' ', dashPhase, ' d', NL);\n            },\n            setLineWidth: function (width) {\n                this._out(width, ' w', NL);\n            },\n            setLineCap: function (lineCap) {\n                this._out(lineCap, ' J', NL);\n            },\n            setLineJoin: function (lineJoin) {\n                this._out(lineJoin, ' j', NL);\n            },\n            setMitterLimit: function (mitterLimit) {\n                this._out(mitterLimit, ' M', NL);\n            },\n            save: function () {\n                this._contextStack.push(this._context());\n                this._out('q', NL);\n            },\n            restore: function () {\n                this._out('Q', NL);\n                this._context(this._contextStack.pop());\n            },\n            moveTo: function (x, y) {\n                this._out(x, ' ', y, ' m', NL);\n            },\n            lineTo: function (x, y) {\n                this._out(x, ' ', y, ' l', NL);\n            },\n            bezier: function (x1, y1, x2, y2, x3, y3) {\n                this._out(x1, ' ', y1, ' ', x2, ' ', y2, ' ', x3, ' ', y3, ' c', NL);\n            },\n            bezier1: function (x1, y1, x3, y3) {\n                this._out(x1, ' ', y1, ' ', x3, ' ', y3, ' y', NL);\n            },\n            bezier2: function (x2, y2, x3, y3) {\n                this._out(x2, ' ', y2, ' ', x3, ' ', y3, ' v', NL);\n            },\n            close: function () {\n                this._out('h', NL);\n            },\n            rect: function (x, y, w, h) {\n                this._out(x, ' ', y, ' ', w, ' ', h, ' re', NL);\n            },\n            ellipse: function (x, y, rx, ry) {\n                function _X(v) {\n                    return x + v;\n                }\n                function _Y(v) {\n                    return y + v;\n                }\n                var k = 0.5522847498307936;\n                this.moveTo(_X(0), _Y(ry));\n                this.bezier(_X(rx * k), _Y(ry), _X(rx), _Y(ry * k), _X(rx), _Y(0));\n                this.bezier(_X(rx), _Y(-ry * k), _X(rx * k), _Y(-ry), _X(0), _Y(-ry));\n                this.bezier(_X(-rx * k), _Y(-ry), _X(-rx), _Y(-ry * k), _X(-rx), _Y(0));\n                this.bezier(_X(-rx), _Y(ry * k), _X(-rx * k), _Y(ry), _X(0), _Y(ry));\n            },\n            circle: function (x, y, r) {\n                this.ellipse(x, y, r, r);\n            },\n            stroke: function () {\n                this._out('S', NL);\n            },\n            nop: function () {\n                this._out('n', NL);\n            },\n            clip: function () {\n                this._out('W n', NL);\n            },\n            clipStroke: function () {\n                this._out('W S', NL);\n            },\n            closeStroke: function () {\n                this._out('s', NL);\n            },\n            fill: function () {\n                this._out('f', NL);\n            },\n            fillStroke: function () {\n                this._out('B', NL);\n            },\n            drawImage: function (url) {\n                var img = this._pdf.getImage(url);\n                if (img) {\n                    this._xResources[img._resourceName] = img;\n                    this._out(img._resourceName, ' Do', NL);\n                }\n            },\n            comment: function (txt) {\n                var self = this;\n                txt.split(/\\r?\\n/g).forEach(function (line) {\n                    self._out('% ', line, NL);\n                });\n            },\n            _context: function (val) {\n                if (val != null) {\n                    this._opacity = val.opacity;\n                    this._matrix = val.matrix;\n                } else {\n                    return {\n                        opacity: this._opacity,\n                        matrix: this._matrix\n                    };\n                }\n            },\n            _toPage: function (p) {\n                var m = this._matrix;\n                var a = m[0], b = m[1], c = m[2], d = m[3], e = m[4], f = m[5];\n                return {\n                    x: a * p.x + c * p.y + e,\n                    y: b * p.x + d * p.y + f\n                };\n            }\n        }, PDFDictionary);\n        function unquote(str) {\n            return str.replace(/^\\s*(['\"])(.*)\\1\\s*$/, '$2');\n        }\n        function parseFontDef(fontdef) {\n            var rx = /^\\s*((normal|italic)\\s+)?((normal|small-caps)\\s+)?((normal|bold|\\d+)\\s+)?(([0-9.]+)(px|pt))(\\/(([0-9.]+)(px|pt)|normal))?\\s+(.*?)\\s*$/i;\n            var m = rx.exec(fontdef);\n            if (!m) {\n                return {\n                    fontSize: 12,\n                    fontFamily: 'sans-serif'\n                };\n            }\n            var fontSize = m[8] ? parseInt(m[8], 10) : 12;\n            return {\n                italic: m[2] && m[2].toLowerCase() == 'italic',\n                variant: m[4],\n                bold: m[6] && /bold|700/i.test(m[6]),\n                fontSize: fontSize,\n                lineHeight: m[12] ? m[12] == 'normal' ? fontSize : parseInt(m[12], 10) : null,\n                fontFamily: m[14].split(/\\s*,\\s*/g).map(unquote)\n            };\n        }\n        function getFontURL(style) {\n            function mkFamily(name) {\n                if (style.bold) {\n                    name += '|bold';\n                }\n                if (style.italic) {\n                    name += '|italic';\n                }\n                return name.toLowerCase();\n            }\n            var fontFamily = style.fontFamily;\n            var name, url;\n            if (fontFamily instanceof Array) {\n                for (var i = 0; i < fontFamily.length; ++i) {\n                    name = mkFamily(fontFamily[i]);\n                    url = FONT_MAPPINGS[name];\n                    if (url) {\n                        break;\n                    }\n                }\n            } else {\n                url = FONT_MAPPINGS[fontFamily.toLowerCase()];\n            }\n            while (typeof url == 'function') {\n                url = url();\n            }\n            if (!url) {\n                url = 'Times-Roman';\n            }\n            return url;\n        }\n        var FONT_MAPPINGS = {\n            'serif': 'Times-Roman',\n            'serif|bold': 'Times-Bold',\n            'serif|italic': 'Times-Italic',\n            'serif|bold|italic': 'Times-BoldItalic',\n            'sans-serif': 'Helvetica',\n            'sans-serif|bold': 'Helvetica-Bold',\n            'sans-serif|italic': 'Helvetica-Oblique',\n            'sans-serif|bold|italic': 'Helvetica-BoldOblique',\n            'monospace': 'Courier',\n            'monospace|bold': 'Courier-Bold',\n            'monospace|italic': 'Courier-Oblique',\n            'monospace|bold|italic': 'Courier-BoldOblique',\n            'zapfdingbats': 'ZapfDingbats',\n            'zapfdingbats|bold': 'ZapfDingbats',\n            'zapfdingbats|italic': 'ZapfDingbats',\n            'zapfdingbats|bold|italic': 'ZapfDingbats'\n        };\n        function fontAlias(alias, name) {\n            alias = alias.toLowerCase();\n            FONT_MAPPINGS[alias] = function () {\n                return FONT_MAPPINGS[name];\n            };\n            FONT_MAPPINGS[alias + '|bold'] = function () {\n                return FONT_MAPPINGS[name + '|bold'];\n            };\n            FONT_MAPPINGS[alias + '|italic'] = function () {\n                return FONT_MAPPINGS[name + '|italic'];\n            };\n            FONT_MAPPINGS[alias + '|bold|italic'] = function () {\n                return FONT_MAPPINGS[name + '|bold|italic'];\n            };\n        }\n        fontAlias('Times New Roman', 'serif');\n        fontAlias('Courier New', 'monospace');\n        fontAlias('Arial', 'sans-serif');\n        fontAlias('Helvetica', 'sans-serif');\n        fontAlias('Verdana', 'sans-serif');\n        fontAlias('Tahoma', 'sans-serif');\n        fontAlias('Georgia', 'sans-serif');\n        fontAlias('Monaco', 'monospace');\n        fontAlias('Andale Mono', 'monospace');\n        function defineFont(name, url) {\n            if (arguments.length == 1) {\n                for (var i in name) {\n                    if (hasOwnProperty(name, i)) {\n                        defineFont(i, name[i]);\n                    }\n                }\n            } else {\n                name = name.toLowerCase();\n                FONT_MAPPINGS[name] = url;\n                switch (name) {\n                case 'dejavu sans':\n                    FONT_MAPPINGS['sans-serif'] = url;\n                    break;\n                case 'dejavu sans|bold':\n                    FONT_MAPPINGS['sans-serif|bold'] = url;\n                    break;\n                case 'dejavu sans|italic':\n                    FONT_MAPPINGS['sans-serif|italic'] = url;\n                    break;\n                case 'dejavu sans|bold|italic':\n                    FONT_MAPPINGS['sans-serif|bold|italic'] = url;\n                    break;\n                case 'dejavu serif':\n                    FONT_MAPPINGS['serif'] = url;\n                    break;\n                case 'dejavu serif|bold':\n                    FONT_MAPPINGS['serif|bold'] = url;\n                    break;\n                case 'dejavu serif|italic':\n                    FONT_MAPPINGS['serif|italic'] = url;\n                    break;\n                case 'dejavu serif|bold|italic':\n                    FONT_MAPPINGS['serif|bold|italic'] = url;\n                    break;\n                case 'dejavu mono':\n                    FONT_MAPPINGS['monospace'] = url;\n                    break;\n                case 'dejavu mono|bold':\n                    FONT_MAPPINGS['monospace|bold'] = url;\n                    break;\n                case 'dejavu mono|italic':\n                    FONT_MAPPINGS['monospace|italic'] = url;\n                    break;\n                case 'dejavu mono|bold|italic':\n                    FONT_MAPPINGS['monospace|bold|italic'] = url;\n                    break;\n                }\n            }\n        }\n        function mmul(a, b) {\n            var a1 = a[0], b1 = a[1], c1 = a[2], d1 = a[3], e1 = a[4], f1 = a[5];\n            var a2 = b[0], b2 = b[1], c2 = b[2], d2 = b[3], e2 = b[4], f2 = b[5];\n            return [\n                a1 * a2 + b1 * c2,\n                a1 * b2 + b1 * d2,\n                c1 * a2 + d1 * c2,\n                c1 * b2 + d1 * d2,\n                e1 * a2 + f1 * c2 + e2,\n                e1 * b2 + f1 * d2 + f2\n            ];\n        }\n        function isIdentityMatrix(m) {\n            return m[0] === 1 && m[1] === 0 && m[2] === 0 && m[3] === 1 && m[4] === 0 && m[5] === 0;\n        }\n        var TEXT_RENDERING_MODE = {\n            fill: 0,\n            stroke: 1,\n            fillAndStroke: 2,\n            invisible: 3,\n            fillAndClip: 4,\n            strokeAndClip: 5,\n            fillStrokeClip: 6,\n            clip: 7\n        };\n        var TEXT_RENDERING_MODE$1 = TEXT_RENDERING_MODE;\n        var DASH_PATTERNS = {\n            dash: [4],\n            dashDot: [\n                4,\n                2,\n                1,\n                2\n            ],\n            dot: [\n                1,\n                2\n            ],\n            longDash: [\n                8,\n                2\n            ],\n            longDashDot: [\n                8,\n                2,\n                1,\n                2\n            ],\n            longDashDotDot: [\n                8,\n                2,\n                1,\n                2,\n                1,\n                2\n            ],\n            solid: []\n        };\n        var LINE_CAP = {\n            butt: 0,\n            round: 1,\n            square: 2\n        };\n        var LINE_JOIN = {\n            miter: 0,\n            round: 1,\n            bevel: 2\n        };\n        function render(group, callback) {\n            var fonts = [], images = {}, options = group.options;\n            function getOption(name, defval, hash) {\n                if (!hash) {\n                    hash = options;\n                }\n                if (hash.pdf && hash.pdf[name] != null) {\n                    return hash.pdf[name];\n                }\n                return defval;\n            }\n            var multiPage = getOption('multiPage');\n            var imgDPI = getOption('imgDPI');\n            if (imgDPI) {\n                clearImageCache();\n            }\n            group.traverse(function (element) {\n                dispatch({\n                    Image: function (element) {\n                        var url = element.src();\n                        if (imgDPI) {\n                            var box = element.bbox().size;\n                            var prev = images[url];\n                            box = {\n                                width: Math.ceil(box.width * imgDPI / 72),\n                                height: Math.ceil(box.height * imgDPI / 72)\n                            };\n                            if (prev) {\n                                box.width = Math.max(prev.width, box.width);\n                                box.height = Math.max(prev.height, box.height);\n                            }\n                            images[url] = box;\n                        } else {\n                            images[url] = null;\n                        }\n                    },\n                    Text: function (element) {\n                        var style = parseFontDef(element.options.font);\n                        var url = getFontURL(style);\n                        if (fonts.indexOf(url) < 0) {\n                            fonts.push(url);\n                        }\n                    }\n                }, element);\n            });\n            function doIt() {\n                if (--count > 0) {\n                    return;\n                }\n                var pdf = new PDFDocument({\n                    producer: getOption('producer'),\n                    title: getOption('title'),\n                    author: getOption('author'),\n                    subject: getOption('subject'),\n                    keywords: getOption('keywords'),\n                    creator: getOption('creator'),\n                    date: getOption('date'),\n                    autoPrint: getOption('autoPrint')\n                });\n                function drawPage(group) {\n                    var options = group.options;\n                    var tmp = optimize(group);\n                    var bbox = tmp.bbox;\n                    group = tmp.root;\n                    var paperSize = getOption('paperSize', getOption('paperSize', 'auto'), options), addMargin = false;\n                    if (paperSize == 'auto') {\n                        if (bbox) {\n                            var size = bbox.getSize();\n                            paperSize = [\n                                size.width,\n                                size.height\n                            ];\n                            addMargin = true;\n                            var origin = bbox.getOrigin();\n                            tmp = new drawing.Group();\n                            tmp.transform(new kendoGeometry.Matrix(1, 0, 0, 1, -origin.x, -origin.y));\n                            tmp.append(group);\n                            group = tmp;\n                        } else {\n                            paperSize = 'A4';\n                        }\n                    }\n                    var page;\n                    page = pdf.addPage({\n                        paperSize: paperSize,\n                        margin: getOption('margin', getOption('margin'), options),\n                        addMargin: addMargin,\n                        landscape: getOption('landscape', getOption('landscape', false), options)\n                    });\n                    drawElement(group, page, pdf);\n                }\n                if (multiPage) {\n                    group.children.forEach(drawPage);\n                } else {\n                    drawPage(group);\n                }\n                callback(pdf.render(), pdf);\n            }\n            var count = 2;\n            loadFonts(fonts, doIt);\n            loadImages(images, doIt);\n        }\n        function toDataURL(group, callback) {\n            render(group, function (data) {\n                callback('data:application/pdf;base64,' + data.base64());\n            });\n        }\n        function toBlob(group, callback) {\n            render(group, function (data) {\n                callback(new window.Blob([data.get()], { type: 'application/pdf' }));\n            });\n        }\n        function saveAs$1(group, filename, proxy, callback) {\n            if (window.Blob && !supportBrowser.safari) {\n                toBlob(group, function (blob) {\n                    kendo.saveAs({\n                        dataURI: blob,\n                        fileName: filename\n                    });\n                    if (callback) {\n                        callback(blob);\n                    }\n                });\n            } else {\n                toDataURL(group, function (dataURL) {\n                    kendo.saveAs({\n                        dataURI: dataURL,\n                        fileName: filename,\n                        proxyURL: proxy\n                    });\n                    if (callback) {\n                        callback(dataURL);\n                    }\n                });\n            }\n        }\n        function dispatch(handlers, element) {\n            var handler = handlers[element.nodeType];\n            if (handler) {\n                return handler.call.apply(handler, arguments);\n            }\n            return element;\n        }\n        function drawElement(element, page, pdf) {\n            if (element.options._pdfDebug) {\n                page.comment('BEGIN: ' + element.options._pdfDebug);\n            }\n            var transform = element.transform();\n            var opacity = element.opacity();\n            page.save();\n            if (opacity != null && opacity < 1) {\n                page.setOpacity(opacity);\n            }\n            setStrokeOptions(element, page, pdf);\n            setFillOptions(element, page, pdf);\n            if (transform) {\n                var m = transform.matrix();\n                page.transform(m.a, m.b, m.c, m.d, m.e, m.f);\n            }\n            setClipping(element, page, pdf);\n            dispatch({\n                Path: drawPath,\n                MultiPath: drawMultiPath,\n                Circle: drawCircle,\n                Arc: drawArc,\n                Text: drawText,\n                Image: drawImage,\n                Group: drawGroup,\n                Rect: drawRect\n            }, element, page, pdf);\n            page.restore();\n            if (element.options._pdfDebug) {\n                page.comment('END: ' + element.options._pdfDebug);\n            }\n        }\n        function setStrokeOptions(element, page) {\n            var stroke = element.stroke && element.stroke();\n            if (!stroke) {\n                return;\n            }\n            var color = stroke.color;\n            if (color) {\n                color = parseColor$1(color);\n                if (color == null) {\n                    return;\n                }\n                page.setStrokeColor(color.r, color.g, color.b);\n                if (color.a != 1) {\n                    page.setStrokeOpacity(color.a);\n                }\n            }\n            var width = stroke.width;\n            if (width != null) {\n                if (width === 0) {\n                    return;\n                }\n                page.setLineWidth(width);\n            }\n            var dashType = stroke.dashType;\n            if (dashType) {\n                page.setDashPattern(DASH_PATTERNS[dashType], 0);\n            }\n            var lineCap = stroke.lineCap;\n            if (lineCap) {\n                page.setLineCap(LINE_CAP[lineCap]);\n            }\n            var lineJoin = stroke.lineJoin;\n            if (lineJoin) {\n                page.setLineJoin(LINE_JOIN[lineJoin]);\n            }\n            var opacity = stroke.opacity;\n            if (opacity != null) {\n                page.setStrokeOpacity(opacity);\n            }\n        }\n        function setFillOptions(element, page) {\n            var fill = element.fill && element.fill();\n            if (!fill) {\n                return;\n            }\n            if (fill instanceof drawing.Gradient) {\n                return;\n            }\n            var color = fill.color;\n            if (color) {\n                color = parseColor$1(color);\n                if (color == null) {\n                    return;\n                }\n                page.setFillColor(color.r, color.g, color.b);\n                if (color.a != 1) {\n                    page.setFillOpacity(color.a);\n                }\n            }\n            var opacity = fill.opacity;\n            if (opacity != null) {\n                page.setFillOpacity(opacity);\n            }\n        }\n        function setClipping(element, page, pdf) {\n            var clip = element.clip();\n            if (clip) {\n                _drawPath(clip, page, pdf);\n                page.clip();\n            }\n        }\n        function shouldDraw(thing) {\n            return thing && (thing instanceof drawing.Gradient || thing.color && !/^(none|transparent)$/i.test(thing.color) && (thing.width == null || thing.width > 0) && (thing.opacity == null || thing.opacity > 0));\n        }\n        function maybeGradient(element, page, pdf, stroke) {\n            var fill = element.fill();\n            if (fill instanceof drawing.Gradient) {\n                if (stroke) {\n                    page.clipStroke();\n                } else {\n                    page.clip();\n                }\n                var isRadial = fill instanceof drawing.RadialGradient;\n                var start, end;\n                if (isRadial) {\n                    start = {\n                        x: fill.center().x,\n                        y: fill.center().y,\n                        r: 0\n                    };\n                    end = {\n                        x: fill.center().x,\n                        y: fill.center().y,\n                        r: fill.radius()\n                    };\n                } else {\n                    start = {\n                        x: fill.start().x,\n                        y: fill.start().y\n                    };\n                    end = {\n                        x: fill.end().x,\n                        y: fill.end().y\n                    };\n                }\n                var stops = fill.stops.elements().map(function (stop) {\n                    var offset = stop.offset();\n                    if (/%$/.test(offset)) {\n                        offset = parseFloat(offset) / 100;\n                    } else {\n                        offset = parseFloat(offset);\n                    }\n                    var color = parseColor$1(stop.color());\n                    color.a *= stop.opacity();\n                    return {\n                        offset: offset,\n                        color: color\n                    };\n                });\n                stops.unshift(stops[0]);\n                stops.push(stops[stops.length - 1]);\n                var gradient = {\n                    userSpace: fill.userSpace(),\n                    type: isRadial ? 'radial' : 'linear',\n                    start: start,\n                    end: end,\n                    stops: stops\n                };\n                var box = element.rawBBox();\n                var tl = box.topLeft(), size = box.getSize();\n                box = {\n                    left: tl.x,\n                    top: tl.y,\n                    width: size.width,\n                    height: size.height\n                };\n                page.gradient(gradient, box);\n                return true;\n            }\n        }\n        function maybeFillStroke(element, page, pdf) {\n            if (shouldDraw(element.fill()) && shouldDraw(element.stroke())) {\n                if (!maybeGradient(element, page, pdf, true)) {\n                    page.fillStroke();\n                }\n            } else if (shouldDraw(element.fill())) {\n                if (!maybeGradient(element, page, pdf, false)) {\n                    page.fill();\n                }\n            } else if (shouldDraw(element.stroke())) {\n                page.stroke();\n            } else {\n                page.nop();\n            }\n        }\n        function maybeDrawRect(path, page) {\n            var segments = path.segments;\n            if (segments.length == 4 && path.options.closed) {\n                var a = [];\n                for (var i = 0; i < segments.length; ++i) {\n                    if (segments[i].controlIn()) {\n                        return false;\n                    }\n                    a[i] = segments[i].anchor();\n                }\n                var isRect = a[0].y == a[1].y && a[1].x == a[2].x && a[2].y == a[3].y && a[3].x == a[0].x || a[0].x == a[1].x && a[1].y == a[2].y && a[2].x == a[3].x && a[3].y == a[0].y;\n                if (isRect) {\n                    page.rect(a[0].x, a[0].y, a[2].x - a[0].x, a[2].y - a[0].y);\n                    return true;\n                }\n            }\n        }\n        function _drawPath(element, page, pdf) {\n            var segments = element.segments;\n            if (segments.length === 0) {\n                return;\n            }\n            if (!maybeDrawRect(element, page, pdf)) {\n                for (var prev, i = 0; i < segments.length; ++i) {\n                    var seg = segments[i];\n                    var anchor = seg.anchor();\n                    if (!prev) {\n                        page.moveTo(anchor.x, anchor.y);\n                    } else {\n                        var prevOut = prev.controlOut();\n                        var controlIn = seg.controlIn();\n                        if (prevOut && controlIn) {\n                            page.bezier(prevOut.x, prevOut.y, controlIn.x, controlIn.y, anchor.x, anchor.y);\n                        } else {\n                            page.lineTo(anchor.x, anchor.y);\n                        }\n                    }\n                    prev = seg;\n                }\n                if (element.options.closed) {\n                    page.close();\n                }\n            }\n        }\n        function drawPath(element, page, pdf) {\n            _drawPath(element, page, pdf);\n            maybeFillStroke(element, page, pdf);\n        }\n        function drawMultiPath(element, page, pdf) {\n            var paths = element.paths;\n            for (var i = 0; i < paths.length; ++i) {\n                _drawPath(paths[i], page, pdf);\n            }\n            maybeFillStroke(element, page, pdf);\n        }\n        function drawCircle(element, page, pdf) {\n            var g = element.geometry();\n            page.circle(g.center.x, g.center.y, g.radius);\n            maybeFillStroke(element, page, pdf);\n        }\n        function drawArc(element, page, pdf) {\n            var points = element.geometry().curvePoints();\n            page.moveTo(points[0].x, points[0].y);\n            for (var i = 1; i < points.length;) {\n                page.bezier(points[i].x, points[i++].y, points[i].x, points[i++].y, points[i].x, points[i++].y);\n            }\n            maybeFillStroke(element, page, pdf);\n        }\n        function drawText(element, page) {\n            var style = parseFontDef(element.options.font);\n            var pos = element._position;\n            var mode;\n            if (element.fill() && element.stroke()) {\n                mode = TEXT_RENDERING_MODE$1.fillAndStroke;\n            } else if (element.fill()) {\n                mode = TEXT_RENDERING_MODE$1.fill;\n            } else if (element.stroke()) {\n                mode = TEXT_RENDERING_MODE$1.stroke;\n            }\n            page.transform(1, 0, 0, -1, pos.x, pos.y + style.fontSize);\n            page.beginText();\n            page.setFont(getFontURL(style), style.fontSize);\n            page.setTextRenderingMode(mode);\n            page.showText(element.content(), element._pdfRect ? element._pdfRect.width() : null);\n            page.endText();\n        }\n        function drawGroup(element, page, pdf) {\n            if (element._pdfLink) {\n                page.addLink(element._pdfLink.url, element._pdfLink);\n            }\n            var children = element.children;\n            for (var i = 0; i < children.length; ++i) {\n                drawElement(children[i], page, pdf);\n            }\n        }\n        function drawImage(element, page) {\n            var url = element.src();\n            if (!url) {\n                return;\n            }\n            var rect = element.rect();\n            var tl = rect.getOrigin();\n            var sz = rect.getSize();\n            page.transform(sz.width, 0, 0, -sz.height, tl.x, tl.y + sz.height);\n            page.drawImage(url);\n        }\n        function drawRect(element, page, pdf) {\n            var geometry = element.geometry();\n            page.rect(geometry.origin.x, geometry.origin.y, geometry.size.width, geometry.size.height);\n            maybeFillStroke(element, page, pdf);\n        }\n        function parseColor$1(value) {\n            var color = kendo.parseColor(value, true);\n            return color ? color.toRGB() : null;\n        }\n        function optimize(root) {\n            var clipbox = false;\n            var matrix = kendoGeometry.Matrix.unit();\n            var currentBox = null;\n            var changed;\n            do {\n                changed = false;\n                root = opt(root);\n            } while (root && changed);\n            return {\n                root: root,\n                bbox: currentBox\n            };\n            function change(newShape) {\n                changed = true;\n                return newShape;\n            }\n            function visible(shape) {\n                return shape.visible() && shape.opacity() > 0 && (shouldDraw(shape.fill()) || shouldDraw(shape.stroke()));\n            }\n            function optArray(a) {\n                var b = [];\n                for (var i = 0; i < a.length; ++i) {\n                    var el = opt(a[i]);\n                    if (el != null) {\n                        b.push(el);\n                    }\n                }\n                return b;\n            }\n            function withClipping(shape, f) {\n                var saveclipbox = clipbox;\n                var savematrix = matrix;\n                if (shape.transform()) {\n                    matrix = matrix.multiplyCopy(shape.transform().matrix());\n                }\n                var clip = shape.clip();\n                if (clip) {\n                    clip = clip.bbox();\n                    if (clip) {\n                        clip = clip.bbox(matrix);\n                        clipbox = clipbox ? kendoGeometry.Rect.intersect(clipbox, clip) : clip;\n                    }\n                }\n                try {\n                    return f();\n                } finally {\n                    clipbox = saveclipbox;\n                    matrix = savematrix;\n                }\n            }\n            function inClipbox(shape) {\n                if (clipbox == null) {\n                    return false;\n                }\n                var box = shape.rawBBox().bbox(matrix);\n                if (clipbox && box) {\n                    box = kendoGeometry.Rect.intersect(box, clipbox);\n                }\n                return box;\n            }\n            function opt(shape) {\n                return withClipping(shape, function () {\n                    if (!(shape instanceof drawing.Group || shape instanceof drawing.MultiPath)) {\n                        var box = inClipbox(shape);\n                        if (!box) {\n                            return change(null);\n                        }\n                        currentBox = currentBox ? kendoGeometry.Rect.union(currentBox, box) : box;\n                    }\n                    return dispatch({\n                        Path: function (shape) {\n                            if (shape.segments.length === 0 || !visible(shape)) {\n                                return change(null);\n                            }\n                            return shape;\n                        },\n                        MultiPath: function (shape) {\n                            if (!visible(shape)) {\n                                return change(null);\n                            }\n                            var el = new drawing.MultiPath(shape.options);\n                            el.paths = optArray(shape.paths);\n                            if (el.paths.length === 0) {\n                                return change(null);\n                            }\n                            return el;\n                        },\n                        Circle: function (shape) {\n                            if (!visible(shape)) {\n                                return change(null);\n                            }\n                            return shape;\n                        },\n                        Arc: function (shape) {\n                            if (!visible(shape)) {\n                                return change(null);\n                            }\n                            return shape;\n                        },\n                        Text: function (shape) {\n                            if (!/\\S/.test(shape.content()) || !visible(shape)) {\n                                return change(null);\n                            }\n                            return shape;\n                        },\n                        Image: function (shape) {\n                            if (!(shape.visible() && shape.opacity() > 0)) {\n                                return change(null);\n                            }\n                            return shape;\n                        },\n                        Group: function (shape) {\n                            var el = new drawing.Group(shape.options);\n                            el.children = optArray(shape.children);\n                            el._pdfLink = shape._pdfLink;\n                            if (shape !== root && el.children.length === 0 && !shape._pdfLink) {\n                                return change(null);\n                            }\n                            return el;\n                        },\n                        Rect: function (shape) {\n                            if (!visible(shape)) {\n                                return change(null);\n                            }\n                            return shape;\n                        }\n                    }, shape);\n                });\n            }\n        }\n        function exportPDF(group, options) {\n            var promise = util.createPromise();\n            for (var i in options) {\n                if (i == 'margin' && group.options.pdf && group.options.pdf._ignoreMargin) {\n                    continue;\n                }\n                group.options.set('pdf.' + i, options[i]);\n            }\n            toDataURL(group, promise.resolve);\n            return promise;\n        }\n        function exportPDFToBlob(group, options) {\n            var promise = util.createPromise();\n            for (var i in options) {\n                if (i == 'margin' && group.options.pdf && group.options.pdf._ignoreMargin) {\n                    continue;\n                }\n                group.options.set('pdf.' + i, options[i]);\n            }\n            if (window.Blob && !supportBrowser.safari) {\n                toBlob(group, promise.resolve);\n            } else {\n                toDataURL(group, promise.resolve);\n            }\n            return promise;\n        }\n        kendo.deepExtend(kendo.pdf, {\n            Document: PDFDocument,\n            BinaryStream: BinaryStream,\n            defineFont: defineFont,\n            parseFontDef: parseFontDef,\n            getFontURL: getFontURL,\n            loadFonts: loadFonts,\n            loadImages: loadImages,\n            getPaperOptions: getPaperOptions,\n            clearImageCache: clearImageCache,\n            TEXT_RENDERING_MODE: TEXT_RENDERING_MODE,\n            exportPDF: exportPDF,\n            exportPDFToBlob: exportPDFToBlob,\n            saveAs: saveAs$1,\n            toDataURL: toDataURL,\n            toBlob: toBlob,\n            render: render\n        });\n        kendo.drawing.exportPDF = kendo.pdf.exportPDF;\n        kendo.drawing.pdf = kendo.pdf;\n    }(kendo));\n    return kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('pdf/mixins', ['pdf/core'], f);\n}(function () {\n    (function ($, undefined) {\n        kendo.PDFMixin = {\n            extend: function (proto) {\n                proto.events.push('pdfExport');\n                proto.options.pdf = this.options;\n                proto.saveAsPDF = this.saveAsPDF;\n                proto._drawPDF = this._drawPDF;\n                proto._drawPDFShadow = this._drawPDFShadow;\n            },\n            options: {\n                fileName: 'Export.pdf',\n                proxyURL: '',\n                paperSize: 'auto',\n                allPages: false,\n                landscape: false,\n                margin: null,\n                title: null,\n                author: null,\n                subject: null,\n                keywords: null,\n                creator: 'Kendo UI PDF Generator v.' + kendo.version,\n                date: null\n            },\n            saveAsPDF: function () {\n                var progress = new $.Deferred();\n                var promise = progress.promise();\n                var args = { promise: promise };\n                if (this.trigger('pdfExport', args)) {\n                    return;\n                }\n                var options = this.options.pdf;\n                options.multiPage = options.multiPage || options.allPages;\n                this._drawPDF(progress).then(function (root) {\n                    return kendo.drawing.exportPDF(root, options);\n                }).done(function (dataURI) {\n                    kendo.saveAs({\n                        dataURI: dataURI,\n                        fileName: options.fileName,\n                        proxyURL: options.proxyURL,\n                        forceProxy: options.forceProxy,\n                        proxyTarget: options.proxyTarget\n                    });\n                    progress.resolve();\n                }).fail(function (err) {\n                    progress.reject(err);\n                });\n                return promise;\n            },\n            _drawPDF: function (progress) {\n                var promise = new $.Deferred();\n                kendo.drawing.drawDOM(this.wrapper).done(function (group) {\n                    var args = {\n                        page: group,\n                        pageNumber: 1,\n                        progress: 1,\n                        totalPages: 1\n                    };\n                    progress.notify(args);\n                    promise.resolve(args.page);\n                }).fail(function (err) {\n                    promise.reject(err);\n                });\n                return promise;\n            },\n            _drawPDFShadow: function (settings, drawOptions) {\n                settings = settings || {};\n                var wrapper = this.wrapper;\n                var shadow = $('<div class=\\'k-pdf-export-shadow\\'>');\n                if (settings.width) {\n                    shadow.css({\n                        width: settings.width,\n                        overflow: 'visible'\n                    });\n                }\n                wrapper.before(shadow);\n                shadow.append(settings.content || wrapper.clone(true, true));\n                var defer = $.Deferred();\n                setTimeout(function () {\n                    var promise = kendo.drawing.drawDOM(shadow, drawOptions);\n                    promise.always(function () {\n                        shadow.remove();\n                    }).then(function () {\n                        defer.resolve.apply(defer, arguments);\n                    }).fail(function () {\n                        defer.reject.apply(defer, arguments);\n                    }).progress(function () {\n                        defer.progress.apply(defer, arguments);\n                    });\n                }, 15);\n                return defer.promise();\n            }\n        };\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.pdf', [\n        'kendo.core',\n        'kendo.drawing',\n        'pdf/core',\n        'pdf/mixins'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'pdf',\n        name: 'PDF export',\n        description: 'PDF Generation framework',\n        mixin: true,\n        category: 'framework',\n        depends: [\n            'core',\n            'drawing'\n        ]\n    };\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}