{"version": 3, "sources": ["kendo.dataviz.themes.js"], "names": ["f", "define", "$", "normalizeText", "text", "String", "replace", "REPLACE_REGEX", "SPACE", "object<PERSON>ey", "object", "key", "parts", "push", "sort", "join", "hash<PERSON><PERSON>", "str", "i", "hash", "length", "charCodeAt", "zeroSize", "width", "height", "baseline", "measureText", "style", "measureBox", "TextMetrics", "current", "measure", "L<PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "defaultMeasureBox", "window", "kendo", "util", "Class", "extend", "init", "size", "this", "_size", "_length", "_map", "put", "value", "map", "entry", "_head", "_tail", "newer", "older", "get", "baselineMarkerSize", "document", "createElement", "cssText", "options", "_cache", "styleKey", "cache<PERSON>ey", "cachedResult", "baseline<PERSON>arker", "textStr", "box", "_baselineMarker", "cloneNode", "textContent", "append<PERSON><PERSON><PERSON>", "body", "offsetWidth", "offsetHeight", "offsetTop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "marker", "deepExtend", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3", "BAR_GAP", "BAR_SPACING", "BLACK", "SANS", "SANS11", "SANS12", "SANS16", "TRANSPARENT", "WHITE", "notes", "axisDefaults", "areaSeries", "rangeAreaSeries", "barSeries", "boxPlotSeries", "bubbleSeries", "bulletSeries", "candlestickSeries", "columnSeries", "donutSeries", "lineSeries", "ohlcSeries", "radarAreaSeries", "radarLineSeries", "rangeBarSeries", "rangeColumnSeries", "scatterLineSeries", "waterfallSeries", "pieSeries", "funnelSeries", "seriesDefaults", "title", "legend", "baseTheme", "dataviz", "icon", "border", "label", "font", "padding", "line", "visible", "labels", "margin", "highlight", "markers", "opacity", "gap", "spacing", "outliersField", "meanField", "_brightness", "downColor", "whiskers", "mean", "median", "background", "target", "color", "top", "bottom", "left", "right", "overlay", "gradients", "gradient", "area", "rangeArea", "verticalRangeArea", "bar", "boxPlot", "bubble", "bullet", "candlestick", "column", "pie", "donut", "funnel", "horizontalWaterfall", "ohlc", "radarArea", "radarLine", "polarArea", "polarLine", "rangeBar", "rangeColumn", "scatterLine", "verticalArea", "verticalBoxPlot", "verticalBullet", "verticalLine", "waterfall", "categoryAxis", "majorGridLines", "navigator", "pane", "chartBaseTheme", "autoTheme", "force", "mapColor", "varName", "set", "queryStyle", "prop", "hook", "find", "css", "path", "store", "theme", "split", "shift", "cache", "chart", "appendTo", "defaultFont", "titleFont", "labelFont", "letterPos", "letter", "toLowerCase", "seriesPos", "name", "match", "series", "toArray", "seriesColors", "reduce", "arr", "el", "pos", "className", "remove", "fuse", "arr1", "arr2", "item", "index", "ui", "gaugeBaseTheme", "scale", "diagramBaseTheme", "shapeDefaults", "hover", "stroke", "editable", "resize", "handles", "selectable", "dashType", "connectionDefaults", "selection", "tools", "themes", "registerTheme", "themeName", "defaults", "result", "gauge", "diagram", "treeMap", "inactiveItems", "errorBars", "scatter", "chartArea", "minorGridLines", "crosshair", "pointer", "rangePlaceholderColor", "minorTicks", "majorTicks", "fill", "connectorDefaults", "content", "rotate", "thumb", "colors", "connectors", "noteStyle", "AXIS", "TEXT", "INACTIVE", "INACTIVE_SHAPE", "AXIS_MINOR", "SERIES", "SERIES_LIGHT", "PRIMARY", "DIAGRAM_HOVER", "sass"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,qBAAsB,cAAeD,IAC9C,YACG,SAAUE,GAqDP,QAASC,GAAcC,GACnB,OAAcA,EAAPC,IAAaC,QAAQC,EAAeC,GAE/C,QAASC,GAAUC,GAAnB,GAEaC,GADLC,IACJ,KAASD,IAAOD,GACZE,EAAMC,KAAKF,EAAMD,EAAOC,GAE5B,OAAOC,GAAME,OAAOC,KAAK,IAE7B,QAASC,GAAQC,GAAjB,GAEaC,GADLC,EAAO,UACX,KAASD,EAAI,EAAGA,EAAID,EAAIG,SAAUF,EAC9BC,IAASA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IACzEA,GAAQF,EAAII,WAAWH,EAE3B,OAAOC,KAAS,EAEpB,QAASG,KACL,OACIC,MAAO,EACPC,OAAQ,EACRC,SAAU,GA0DlB,QAASC,GAAYtB,EAAMuB,EAAOC,GAC9B,MAAOC,GAAYC,QAAQC,QAAQ3B,EAAMuB,EAAOC,GAtIvD,GAEOI,GAiDAzB,EACAC,EA0BAyB,EACAC,EAKAL,CAnFJM,QAAOC,MAAMC,KAAOF,OAAOC,MAAMC,SAC7BL,EAAWI,MAAME,MAAMC,QACvBC,KAAM,SAAUC,GACZC,KAAKC,MAAQF,EACbC,KAAKE,QAAU,EACfF,KAAKG,SAETC,IAAK,SAAUnC,EAAKoC,GAAf,GACGC,GAAMN,KAAKG,KACXI,GACAtC,IAAKA,EACLoC,MAAOA,EAEXC,GAAIrC,GAAOsC,EACNP,KAAKQ,OAGNR,KAAKS,MAAMC,MAAQH,EACnBA,EAAMI,MAAQX,KAAKS,MACnBT,KAAKS,MAAQF,GAJbP,KAAKQ,MAAQR,KAAKS,MAAQF,EAM1BP,KAAKE,SAAWF,KAAKC,OACrBK,EAAIN,KAAKQ,MAAMvC,KAAO,KACtB+B,KAAKQ,MAAQR,KAAKQ,MAAME,MACxBV,KAAKQ,MAAMG,MAAQ,MAEnBX,KAAKE,WAGbU,IAAK,SAAU3C,GACX,GAAIsC,GAAQP,KAAKG,KAAKlC,EACtB,IAAIsC,EAeA,MAdIA,KAAUP,KAAKQ,OAASD,IAAUP,KAAKS,QACvCT,KAAKQ,MAAQD,EAAMG,MACnBV,KAAKQ,MAAMG,MAAQ,MAEnBJ,IAAUP,KAAKS,QACXF,EAAMI,QACNJ,EAAMI,MAAMD,MAAQH,EAAMG,MAC1BH,EAAMG,MAAMC,MAAQJ,EAAMI,OAE9BJ,EAAMI,MAAQX,KAAKS,MACnBF,EAAMG,MAAQ,KACdV,KAAKS,MAAMC,MAAQH,EACnBP,KAAKS,MAAQF,GAEVA,EAAMF,SAIrBxC,EAAgB,eAChBC,EAAQ,IA0BRyB,GAAoBsB,mBAAoB,GAEpB,mBAAbC,YACPtB,EAAoBsB,SAASC,cAAc,OAC3CvB,EAAkBP,MAAM+B,QAAU,wQAElC7B,EAAcO,MAAME,MAAMC,QAC1BC,KAAM,SAAUmB,GACZjB,KAAKkB,OAAS,GAAI5B,GAAS,KAC3BU,KAAKiB,QAAUzD,EAAEqC,UAAWN,EAAiB0B,IAEjD5B,QAAS,SAAU3B,EAAMuB,EAAOgC,GAAvB,GAODE,GACAC,EACAC,EAIAtB,EACAb,EACAoC,EACKrD,EACDoC,EAKJkB,CAlBJ,IAHgB,SAAZN,IACAA,OAECvD,EACD,MAAOkB,IAKX,IAHIuC,EAAWpD,EAAUkB,GACrBmC,EAAW9C,EAAQZ,EAAOyD,GAC1BE,EAAerB,KAAKkB,OAAON,IAAIQ,GAE/B,MAAOC,EAEPtB,GAAOnB,IACPM,EAAa+B,EAAQO,KAAOhC,EAC5B8B,EAAiBtB,KAAKyB,kBAAkBC,WAAU,EACtD,KAASzD,IAAOgB,GACRoB,EAAQpB,EAAMhB,GACG,SAAVoC,IACPnB,EAAWD,MAAMhB,GAAOoC,EAgBhC,OAbIkB,GAAUN,EAAQxD,iBAAkB,EAAQA,EAAcC,GAAeA,EAAPC,GACtEuB,EAAWyC,YAAcJ,EACzBrC,EAAW0C,YAAYN,GACvBR,SAASe,KAAKD,YAAY1C,GACtBqC,EAAQ7C,SACRqB,EAAKlB,MAAQK,EAAW4C,YAAc9B,KAAKiB,QAAQJ,mBACnDd,EAAKjB,OAASI,EAAW6C,aACzBhC,EAAKhB,SAAWuC,EAAeU,UAAYhC,KAAKiB,QAAQJ,oBAExDd,EAAKlB,MAAQ,GAAKkB,EAAKjB,OAAS,GAChCkB,KAAKkB,OAAOd,IAAIgB,EAAUrB,GAE9Bb,EAAW+C,WAAWC,YAAYhD,GAC3Ba,GAEX0B,gBAAiB,WACb,GAAIU,GAASrB,SAASC,cAAc,MAEpC,OADAoB,GAAOlD,MAAM+B,QAAU,0DAA4DhB,KAAKiB,QAAQJ,mBAAqB,eAAiBb,KAAKiB,QAAQJ,mBAAqB,uBACjKsB,KAGfhD,EAAYC,QAAU,GAAID,GAI1BO,MAAM0C,WAAW1C,MAAMC,MACnBL,SAAUA,EACVH,YAAaA,EACbH,YAAaA,EACbjB,UAAWA,EACXO,QAASA,EACTb,cAAeA,KAErBgC,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,mCAAoC,sBAAuBD,IACpE,YACG,WAAA,GAEOoF,GACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAcAC,EAUAC,EAcAC,EAcAC,EAMAC,EAyBAC,EAOAC,EAOAC,EAsBAC,EAMAC,EAGAC,EAGAC,EAaAC,EAaAC,EAMAC,EAMAC,EAMAC,EAGAC,EAUAC,EAcAC,EAcAC,EAmCAC,EAGAC,EAGAC,CA3QJlF,QAAOC,MAAMkF,QAAUnF,OAAOC,MAAMkF,YAChClC,EAAU,IACVC,EAAc,GACdC,EAAQ,OACRC,EAAO,+BACPC,EAAS,QAAUD,EACnBE,EAAS,QAAUF,EACnBG,EAAS,QAAUH,EACnBI,EAAc,cACdC,EAAQ,OACRC,EAAQ,WACR,OACI0B,MAAQC,QAAUjG,MAAO,IACzBkG,OACIC,KAAMjC,EACNkC,QAAS,GAEbC,MACIxG,OAAQ,GACRG,MAAO,GAEXsG,SAAS,IAGb/B,EAAe,WACf,OACIgC,QAAUJ,KAAMjC,GAChBI,MAAOA,IACPsB,OACIO,KAAMhC,EACNqC,OAAQ,KAIhBhC,EAAa,WACb,OACIiC,WAAaC,SAAWT,YACxBI,MACIM,QAAS,EACT3G,MAAO,GAEX0G,SACIxF,KAAM,EACNoF,SAAS,GAEbK,QAAS,KAGblC,EAAkB,WAClB,OACIgC,WAAaC,SAAWT,YACxBI,MACIM,QAAS,EACT3G,MAAO,GAEX0G,SACIxF,KAAM,EACNoF,SAAS,GAEbK,QAAS,KAGbjC,EAAY,WACZ,OACIkC,IAAK/C,EACLgD,QAAS/C,IAGba,EAAgB,WAChB,OACImC,cAAe,GACfC,UAAW,GACXd,QACIe,YAAa,GACbhH,MAAO,GAEXiH,UAAW5C,EACXuC,IAAK,EACLH,WACIR,QACIU,QAAS,EACT3G,MAAO,GAEXkH,UAAYlH,MAAO,GACnBmH,MAAQnH,MAAO,GACfoH,QAAUpH,MAAO,IAErBmH,MAAQnH,MAAO,GACfoH,QAAUpH,MAAO,GACjB6G,QAAS,GACTK,UAAYlH,MAAO,KAGvB4E,EAAe,WACf,OACIqB,QAAUjG,MAAO,GACjBuG,QAAUc,WAAYjD,GACtBuC,QAAS,KAGb9B,EAAe,WACf,OACI+B,IAAK/C,EACLgD,QAAS/C,EACTwD,QAAUC,MAAO,aAGrBzC,EAAoB,WACpB,OACImB,QACIe,YAAa,GACbhH,MAAO,GAEXiH,UAAW5C,EACXuC,IAAK,EACLH,WACIR,QACIU,QAAS,EACT3G,MAAO,GAEXqG,MAAQrG,MAAO,IAEnBqG,MACIkB,MAAOxD,EACP/D,MAAO,GAEX6G,QAAS,KAGb9B,EAAe,WACf,OACI6B,IAAK/C,EACLgD,QAAS/C,IAGbkB,EAAc,WACd,OAASwB,OAAQ,IAEjBvB,EAAa,WACb,OAASjF,MAAO,IAEhBkF,EAAa,WACb,OACI0B,IAAK,EACLH,WACIJ,MACIM,QAAS,EACT3G,MAAO,IAGfqG,MAAQrG,MAAO,GACf6G,QAAS,KAGb1B,EAAkB,WAClB,OACIkB,MACIM,QAAS,EACT3G,MAAO,GAEX0G,SACIxF,KAAM,EACNoF,SAAS,GAEbK,QAAS,KAGbvB,EAAkB,WAClB,OACIsB,SAAWJ,SAAS,GACpBtG,MAAO,IAGXqF,EAAiB,WACjB,OACIuB,IAAK/C,EACLgD,QAAS/C,IAGbwB,EAAoB,WACpB,OACIsB,IAAK/C,EACLgD,QAAS/C,IAGbyB,EAAoB,WACpB,OAASvF,MAAO,IAEhBwF,EAAkB,WAClB,OACIoB,IAAK,GACLP,MACIkB,MAAOxD,EACP/D,MAAO,GAEX6G,QAAS/C,IAGb2B,EAAY,WACZ,OACIc,QACIc,WAAY,GACZE,MAAO,GACPnB,SACIoB,IAAK,EACLC,OAAQ,EACRC,KAAM,EACNC,MAAO,MAKnBjC,EAAe,WACf,OACIa,QACIc,WAAY,GACZE,MAAO,GACPnB,SACIoB,IAAK,EACLC,OAAQ,EACRC,KAAM,EACNC,MAAO,MAKnBhC,EAAiB,SAAUvD,GAC3B,OACIkE,SAAS,EACTC,QAAUJ,KAAMlC,GAChB2D,QAASxF,EAAQyF,cAAmBC,SAAU,QAC9CC,KAAMvD,IACNwD,UAAWvD,IACXwD,kBAAmBxD,IACnByD,IAAKxD,IACLyD,QAASxD,IACTyD,OAAQxD,IACRyD,OAAQxD,IACRyD,YAAaxD,IACbyD,OAAQxD,IACRyD,IAAK/C,IACLgD,MAAOzD,IACP0D,OAAQhD,IACRiD,oBAAqBnD,IACrBa,KAAMpB,IACNX,MAAOA,IACPsE,KAAM1D,IACN2D,UAAW1D,IACX2D,UAAW1D,IACX2D,UAAW5D,IACX6D,UAAW5D,IACX6D,SAAU5D,IACV6D,YAAa5D,IACb6D,YAAa5D,IACb6D,aAAc5E,IACd6E,gBAAiB1E,IACjB2E,eAAgBzE,IAChB0E,aAActE,IACduE,UAAWhE,MAGfI,EAAQ,WACR,OAASO,KAAMhC,IAEf0B,EAAS,WACT,OAASU,QAAUJ,KAAMjC,KAEzB4B,EAAY,SAAU1D,GAItB,MAHgB,UAAZA,IACAA,OAGAmC,aAAcA,IACdkF,cAAgBC,gBAAkBpD,SAAS,IAC3CqD,WACIC,MACI3J,OAAQ,GACRuG,QAAUgB,IAAK,MAGvB7B,eAAgBA,EAAevD,GAC/BwD,MAAOA,IACPC,OAAQA,MAGhBhF,MAAM0C,WAAW1C,MAAMkF,SAAW8D,eAAgB/D,QAEtC,kBAAVpH,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,6BAA8B,sBAAuBD,IAC9D,YACG,SAAUE,GAEP,QAASmL,GAAUC,GAMf,QAASC,GAAS5K,EAAK6K,GACnBC,EAAI9K,EAAK+K,EAAWF,EAAS,oBAEjC,QAASE,GAAWF,EAASG,GACzB,MAAOC,GAAKC,KAAK,WAAaL,GAASM,IAAIH,GAE/C,QAASF,GAAIM,EAAMhJ,GAIf,IAJJ,GACQiJ,GAAQC,EACRrL,EAAQmL,EAAKG,MAAM,KACnBvL,EAAMC,EAAMuL,QACTvL,EAAMQ,OAAS,GAClB4K,EAAQA,EAAMrL,GAAOqL,EAAMrL,OAC3BA,EAAMC,EAAMuL,OAEhBH,GAAMrL,GAAOoC,EApBrB,GAIQkJ,GACAL,CAJJ,QAAKN,GAASc,EACHA,GAEPH,GAAUI,MAAOjK,MAAMkF,QAAQ8D,kBAC/BQ,EAAO1L,EAAE,q5CAAokDoM,SAAS9I,SAASe,MAiBlmD,WACGgH,EAAS,qCAAsC,8BAC/CA,EAAS,kCAAmC,qBAC5CA,EAAS,gCAAiC,qBAC1CA,EAAS,0CAA2C,qBACpDA,EAAS,0CAA2C,qBACpDA,EAAS,2CAA4C,0BACrDA,EAAS,6CAA8C,sBACvDA,EAAS,sCAAuC,qBAChDA,EAAS,iCAAkC,qBAC3CA,EAAS,6BAA8B,cACvCA,EAAS,0CAA2C,kBACpDA,EAAS,2CAA4C,kBACrDA,EAAS,4BAA6B,qBACtCA,EAAS,yCAA0C,qBACnDA,EAAS,0CAA2C,QACpDA,EAAS,4CAA6C,QACtDA,EAAS,8CAA+C,UACxDA,EAAS,2CAA4C,UACrDA,EAAS,6CAA8C,qBACvDA,EAAS,8CAA+C,qBACxDA,EAAS,uCAAwC,+BACjDA,EAAS,sDAAuD,qBAChEA,EAAS,yCAA0C,qBACnDA,EAAS,yCAA0C,cACnDA,EAAS,oCAAqC,qBAC9CA,EAAS,6CAA8C,0BACvDA,EAAS,+CAAgD,sBACzDA,EAAS,wCAAyC,qBAClDA,EAAS,iDAAkD,qBAC3DA,EAAS,kDAAmD,QAC5DA,EAAS,oDAAqD,QAC9DA,EAAS,sDAAuD,UAChEA,EAAS,mDAAoD,UAC7DA,EAAS,4CAA6C,qBACtDA,EAAS,oBAAqB,qBAC9BE,EAAI,sCAAuCC,EAAW,qBAAsB,YAC5EH,EAAS,mCAAoC,UAC7CA,EAAS,sCAAuC,mBAChDA,EAAS,qDAAsD,qBAC/DA,EAAS,uDAAwD,mBACjEA,EAAS,2DAA4D,mBACrEA,EAAS,6DAA8D,qBACvEA,EAAS,+CAAgD,qBACzDA,EAAS,6CAA8C,qBACvDA,EAAS,qDAAsD,qBAC/DA,EAAS,mDAAoD,qBAC7DA,EAAS,kCAAmC,qBAC5CA,EAAS,0CAA2C,qBACpDA,EAAS,2CAA4C,qBACrDA,EAAS,0DAA2D,mBACpEA,EAAS,4DAA6D,qBACtEA,EAAS,oDAAqD,qBAC9DA,EAAS,sBAAuB,iBAChCA,EAAS,2BAA4B,qBACrCA,EAAS,+BAAgC,qBACzCA,EAAS,+BAAgC,qBACzCA,EAAS,yBAA0B,qBACnCA,EAAS,oCAAqC,kBAEjD,WACG,QAAS7D,GAAK8D,GACV,MAAOE,GAAWF,EAAS,YAAc,IAAME,EAAWF,EAAS,cAF1E,GAIOe,GAAc7E,EAAK,cACnB8E,EAAY9E,EAAK,oBACjB+E,EAAY/E,EAAK,mBACrB+D,GAAI,iCAAkCgB,GACtChB,EAAI,sCAAuCc,GAC3Cd,EAAI,gCAAiCc,GACrCd,EAAI,2BAA4Bc,GAChCd,EAAI,mCAAoCgB,GACxChB,EAAI,wCAAyCc,GAC7Cd,EAAI,mBAAoBe,MAE3B,WACG,QAASE,GAAUC,GACf,MAAOA,GAAOC,cAAcvL,WAAW,GAAK,IAAIA,WAAW,GAE/D,QAASwL,GAAUC,GACf,MAAOJ,GAAUI,EAAKC,MAAM,mBAAmB,IALtD,GAOOC,GAAS9M,EAAE,sBAAsB+M,UACjCC,EAAeF,EAAOG,OAAO,SAAUC,EAAKC,GAC5C,GAAIC,GAAMT,EAAUQ,EAAGE,UAEvB,OADAH,GAAIE,GAAOpN,EAAEmN,GAAIvB,IAAI,mBACdsB,MAEX3B,GAAI,qBAAsByB,MAE9BtB,EAAK4B,SACLpB,EAAQH,EACDA,GAnHX,GAAIG,EAqHJhK,OAAMkF,QAAQ+D,UAAYA,GAC5BlJ,OAAOC,MAAM2C,SACC,kBAAV9E,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,yBAA0B,mCAAoCD,IACvE,WA04EE,MAz4EC,UAAUE,GAg4EP,QAASuN,GAAKC,EAAMC,GAChB,MAAOzN,GAAE8C,IAAI0K,EAAM,SAAUE,EAAMC,GAC/B,QACQD,EACAD,EAAKE,OAp4ExB,GACOzL,GAAQD,OAAOC,MAAO0L,EAAK1L,EAAMkF,QAAQwG,GAAIhJ,EAAa1C,EAAM0C,WAChEQ,EAAQ,OAAQC,EAAO,6BAA8BE,EAAS,QAAUF,EAAMK,EAAQ,OACtFwF,EAAiBhJ,EAAMkF,QAAQ8D,gBAAiBhC,WAAW,IAC3D2E,GAAmBC,OAASlG,QAAUJ,KAAMjC,KAC5CwI,GACAC,eACIC,OAASjG,QAAS,IAClBkG,QAAU7M,MAAO,IAErB8M,UACIC,QACIC,SACIhN,MAAO,EACPC,OAAQ,KAIpBgN,YACIJ,QACI7M,MAAO,EACPkN,SAAU,QAGlBC,oBACIN,QAAU7M,MAAO,GACjBoN,WACIJ,SACIhN,MAAO,EACPC,OAAQ,IAGhB6M,UACIO,OACI,OACA,aAKZC,EAASf,EAAGe,OAAQC,EAAgBhB,EAAGgB,cAAgB,SAAUC,EAAWpL,GAArB,GAM/CqL,GALAC,IACJA,GAAO5C,MAAQvH,KAAesG,EAAgBzH,EAAQ0I,OACtD4C,EAAOC,MAAQpK,KAAeiJ,EAAgBpK,EAAQuL,OACtDD,EAAOE,QAAUrK,KAAemJ,EAAkBtK,EAAQwL,SAC1DF,EAAOG,QAAUtK,KAAenB,EAAQyL,SACpCJ,EAAWC,EAAO5C,MAAMnF,eAC5B8H,EAASlE,aAAehG,KAAekK,EAASpH,MAChDoH,EAASrE,aAAe7F,KAAekK,EAAS1F,MAChD0F,EAASzF,UAAYzE,KAAekK,EAAS1F,MAC7C0F,EAASxF,kBAAoB1E,KAAekK,EAASzF,WACrDyF,EAASpE,gBAAkB9F,KAAekK,EAAStF,SACnDsF,EAAS1E,UAAYxF,KAAekK,EAAS5E,WAC7C4E,EAASzE,UAAYzF,KAAekK,EAAS3E,WAC7CwE,EAAOE,GAAaE,EAE5BH,GAAc,SACVzC,OACIlF,OAAS2B,MAAOlD,GAChBwB,QACIU,QAAUgB,MAAOlD,GACjByJ,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QAAUgB,MAAOlD,GACjB0J,WAAaxG,MAAOlD,GACpBC,OACI0B,MACIqB,WAAY,UACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAOlD,GAChBgC,MAAQkB,MAAO,YAEnBiB,KAAOZ,SAAWE,SAAU,eAC5BW,OAASb,SAAWE,SAAU,eAC9BzB,MAAQK,SAAWW,WAAY,YAC/B2G,SAAWtH,SAAWW,WAAY,YAClC8B,aAAezC,SAAWW,WAAY,YACtCmC,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCe,aACIrB,UAAW,OACXZ,MAAQkB,MAAOlD,GACf4B,QACIe,YAAa,IACbL,QAAS,GAEbF,WACIR,QACIsB,MAAOlD,EACPsC,QAAS,MAIrBiC,MAAQvC,MAAQkB,MAAOlD,KAE3B4J,WAAa5G,WAAY,WACzBsE,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAOlD,GACjBqF,gBAAkBnC,MAAO,WACzB2G,gBAAkB3G,MAAO,WACzB3B,OAAS2B,MAAOlD,GAChB8J,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,UACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAOlD,GAChBgC,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAOlD,GACjBiK,YAAc/G,MAAOlD,GACrBkK,YAAchH,MAAOlD,GACrBgC,MAAQkB,MAAOlD,KAGvBuJ,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,aAGzBmH,SAAWnH,MAAOlD,IAEtByI,UACIC,QACIC,SACIwB,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAOlD,MAI7BsK,QACIC,OACI/B,QAAUtF,MAAOlD,GACjBmK,MAAQjH,MAAOlD,MAI3B4I,YAAcJ,QAAUtF,MAAOlD,IAC/B8I,oBACIN,QAAUtF,MAAOlD,GACjBqK,SAAWnH,MAAOlD,GAClB+I,WACIJ,SACIwB,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,eAKjCsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,YACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QACIgB,MAAOxD,EACPsD,WAAYhD,EACZsC,QAAS,IAEboH,WAAaxG,MAAO,WACpBe,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,YAEnBiC,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,aAGvBoE,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAO,WACjBmC,gBAAkBnC,MAAO,WACzB2G,gBAAkB3G,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,WACjB+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,aAGzBmH,SAAWnH,MAAO,YAEtBuF,UACIC,QACIC,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,cAI7BoH,QACIC,OACI/B,QAAUtF,MAAO,WACjBiH,MAAQjH,MAAO,cAI3B0F,YAAcJ,QAAUtF,MAAO,YAC/B4F,oBACIN,QAAUtF,MAAO,WACjBmH,SAAWnH,MAAO,WAClB6F,WACIJ,SACIwB,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,eAKjCsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,gBACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QAAUgB,MAAO,WACjBwG,WAAaxG,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,YAEnBiB,KAAOZ,SAAWE,SAAU,eAC5BW,OAASb,SAAWE,SAAU,eAC9BzB,MAAQK,SAAWW,WAAY,YAC/B2G,SAAWtH,SAAWW,WAAY,YAClC8B,aAAezC,SAAWW,WAAY,YACtCU,MAAQpB,QAAS,IACjB6C,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCe,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,WACftB,QACIe,YAAa,IACbL,QAAS,GAEbF,WACIR,QACIsB,MAAO,UACPZ,QAAS,KAIrBiC,MAAQvC,MAAQkB,MAAO,aAE3B0G,WAAa5G,WAAY,WACzBsE,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAO,WACjBmC,gBAAkBnC,MAAO,WACzB2G,gBAAkB3G,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,WACjB+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,KAGzBqK,SAAWnH,MAAOlD,IAEtByI,UACIC,QACIC,SACIwB,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAOlD,MAI7BsK,QACIC,OACI/B,QAAUtF,MAAOlD,GACjBmK,MAAQjH,MAAOlD,MAI3B4I,YAAcJ,QAAUtF,MAAOlD,IAC/B8I,oBACIN,QAAUtF,MAAOlD,GACjBqK,SAAWnH,MAAOlD,GAClB+I,WACIJ,SACIwB,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,OAKjCwJ,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,WACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QACIgB,MAAOxD,EACPsD,WAAYhD,EACZsC,QAAS,IAEboH,WAAaxG,MAAO,WACpBe,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,YAEnBiC,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,aAGvBoE,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAO,WACjB2G,gBAAkB3G,MAAO,WACzBmC,gBAAkBnC,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,WACjB+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,aAGzBmH,SAAWnH,MAAO,YAEtBuF,UACIC,QACIC,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,cAI7BoH,QACIC,OACI/B,QAAUtF,MAAO,WACjBiH,MAAQjH,MAAO,cAI3B0F,YAAcJ,QAAUtF,MAAO,YAC/B4F,oBACIN,QAAUtF,MAAO,WACjBmH,SAAWnH,MAAO,WAClB6F,WACIJ,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,eAKjCsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,UACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QACIgB,MAAO,UACPF,WAAY,UACZV,QAAS,IAEboH,WAAaxG,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,YAEnBlB,MAAQK,SAAWW,WAAY,YAC/B2G,SAAWtH,SAAWW,WAAY,YAClC8B,aAAezC,SAAWW,WAAY,YACtCmB,KAAOsG,YAAcvH,MAAO,YAC5BkB,OAASqG,YAAcvH,MAAO,YAC9BiC,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCe,aAAerB,UAAW,YAE9BgH,WAAa5G,WAAY,WACzBsE,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAO,WACjBmC,gBAAkBnC,MAAO,WACzB2G,gBAAkB3G,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,WACjB+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,aAGzBmH,SAAWnH,MAAO,YAEtBuF,UACIC,QACIC,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,cAI7BoH,QACIC,OACI/B,QAAUtF,MAAO,WACjBiH,MAAQjH,MAAO,cAI3B0F,YAAcJ,QAAUtF,MAAO,YAC/B4F,oBACIN,QAAUtF,MAAO,WACjBmH,SAAWnH,MAAO,WAClB6F,WACIJ,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,eAKjCsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,SACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QAAUgB,MAAOxD,GACjBgK,WAAaxG,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,YAEnBe,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,YAEnBiC,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCK,SAAWE,SAAU,QACrB7B,QAAUe,YAAa,IAE3B2E,cACI,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAO,WACjB2G,gBAAkB3G,MAAO,WACzBmC,gBAAkBnC,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,QACjB+G,YAAc/G,MAAO,QACrBgH,YAAchH,MAAO,QACrBlB,MAAQkB,MAAO,UAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAOxD,GACf8I,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAOxD,KAGzB2K,SAAWnH,MAAO,SAEtBuF,UACIC,QACIC,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,cAI7BoH,QACIC,OACI/B,QAAUtF,MAAO,WACjBiH,MAAQjH,MAAO,cAI3B0F,YAAcJ,QAAUtF,MAAO,YAC/B4F,oBACIN,QAAUtF,MAAO,WACjBmH,SAAWnH,MAAO,QAClB6F,WACIJ,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,eAKjCsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,cACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIM,QAAUe,YAAa,GACvBT,QAAUgB,MAAO,WACjBwG,WAAaxG,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,YAEnBlB,MAAQK,SAAWW,WAAY,YAC/Be,QAAUzB,QAAS,IACnBqH,SAAWtH,SAAWW,WAAY,YAClC8B,aAAezC,SAAWW,WAAY,YACtCiB,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,YAEnBiC,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCK,SAAWE,SAAU,SAEzBmG,WAAa5G,WAAY,WACzBsE,cACI,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAO,WACjB2G,gBAAkB3G,MAAO,WACzBmC,gBAAkBnC,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,WACjB+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,KAGzBqK,SAAWnH,MAAOlD,IAEtByI,UACIC,QACIC,SACIwB,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,cAI7BoH,QACIC,OACI/B,QAAUtF,MAAOlD,GACjBmK,MAAQjH,MAAOlD,MAI3B4I,YAAcJ,QAAUtF,MAAO,YAC/B4F,oBACIN,QAAUtF,MAAOlD,GACjBqK,SAAWnH,MAAOlD,GAClB+I,WACIJ,SACIwB,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,OAKjCwJ,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,aACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QAAUgB,MAAO,WACjBwG,WAAaxG,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,YAEnBiB,KAAOZ,SAAWE,SAAU,eAC5BW,OAASb,SAAWE,SAAU,eAC9BzB,MAAQK,SAAWW,WAAY,YAC/Be,QAAUzB,QAAS,IACnBqH,SAAWtH,SAAWW,WAAY,YAClC8B,aAAezC,SAAWW,WAAY,YACtCU,MAAQpB,QAAS,IACjB2B,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,WACftB,QACIe,YAAa,IACbL,QAAS,GAEbF,WACIR,QACIsB,MAAOlD,EACPsC,QAAS,MAIrB6C,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCqB,MAAQvC,MAAQkB,MAAO,aAE3B0G,WAAa5G,WAAY,WACzBsE,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACf+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBhB,QAAUgB,MAAO,WACjBmC,gBAAkBnC,MAAO,WACzB2G,gBAAkB3G,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAOlD,GACjBiK,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,KAGzBqK,SAAWnH,MAAOlD,IAEtByI,UACIC,QACIC,SACIwB,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAOlD,MAI7BsK,QACIC,OACI/B,QAAUtF,MAAOlD,GACjBmK,MAAQjH,MAAOlD,MAI3B4I,YAAcJ,QAAUtF,MAAOlD,IAC/B8I,oBACIN,QAAUtF,MAAOlD,GACjBqK,SAAWnH,MAAOlD,GAClB+I,WACIJ,SACIwB,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,OAKjCwJ,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,WACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QAAUgB,MAAO,WACjBwG,WAAaxG,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,YAEnBiB,KAAOZ,SAAWE,SAAU,eAC5BW,OAASb,SAAWE,SAAU,eAC9BzB,MAAQK,SAAWW,WAAY,YAC/Be,QAAUzB,QAAS,IACnBqH,SAAWtH,SAAWW,WAAY,YAClC8B,aAAezC,SAAWW,WAAY,YACtCU,MAAQpB,QAAS,IACjB2B,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,WACftB,QACIe,YAAa,IACbL,QAAS,GAEbF,WACIR,QACIsB,MAAO,UACPZ,QAAS,MAIrB6C,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCqB,MAAQvC,MAAQkB,MAAO,aAE3B0G,WAAa5G,WAAY,WACzBsE,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACf+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBhB,QAAUgB,MAAO,WACjBmC,gBAAkBnC,MAAO,WACzB2G,gBAAkB3G,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,WACjB+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,aAGzBmH,SAAWnH,MAAO,YAEtBuF,UACIC,QACIC,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,cAI7BoH,QACIC,OACI/B,QAAUtF,MAAO,WACjBiH,MAAQjH,MAAO,cAI3B0F,YAAcJ,QAAUtF,MAAO,YAC/B4F,oBACIN,QAAUtF,MAAO,WACjBmH,SAAWnH,MAAO,WAClB6F,WACIJ,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,eAKjCsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,aACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QAAUgB,MAAO,WACjBK,SAAWE,SAAU,QACrBiG,WAAaxG,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,UACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,YAEnBiB,KAAOZ,SAAWE,SAAU,SAC5BW,OAASb,SAAWE,SAAU,SAC9BzB,MAAQK,SAAWW,WAAY,YAC/Be,QAAUzB,QAAS,IACnBqH,SAAWtH,SAAWW,WAAY,YAClC8B,aAAezC,SAAWW,WAAY,YACtCU,MAAQpB,QAAS,IACjB2B,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,WACftB,QACIe,YAAa,IACbL,QAAS,GAEbF,WACIR,QACIsB,MAAO,UACPZ,QAAS,MAIrB6C,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCqB,MAAQvC,MAAQkB,MAAO,aAE3B0G,WAAa5G,WAAY,WACzBsE,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACf+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBhB,QAAUgB,MAAO,WACjBmC,gBAAkBnC,MAAO,WACzB2G,gBAAkB3G,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,UACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,WACjB+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,aAGzBmH,SAAWnH,MAAO,YAEtBuF,UACIC,QACIC;AACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,cAI7BoH,QACIC,OACI/B,QAAUtF,MAAO,WACjBiH,MAAQjH,MAAO,cAI3B0F,YAAcJ,QAAUtF,MAAO,YAC/B4F,oBACIN,QAAUtF,MAAO,WACjBmH,SAAWnH,MAAO,WAClB6F,WACIJ,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,YAErBsF,QAAUtF,MAAO,cAI7BsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,QACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QAAUgB,MAAO,WACjBwG,WAAaxG,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,YAEnBe,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,YAEnBQ,MAAQpB,QAAS,IACjB6C,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCK,SAAWE,SAAU,QACrB7B,QAAUe,YAAa,IAE3B2E,cACI,UACA,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAO,WACjB2G,gBAAkB3G,MAAO,WACzBmC,gBAAkBnC,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,WACjB+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,aAGzBmH,SAAWnH,MAAO,YAEtBuF,UACIC,QACIC,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,cAI7BoH,QACIC,OACI/B,QAAUtF,MAAO,WACjBiH,MAAQjH,MAAO,cAI3B0F,YAAcJ,QAAUtF,MAAO,YAC/B4F,oBACIN,QAAUtF,MAAO,WACjBmH,SAAWnH,MAAO,WAClB6F,WACIJ,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,YAErBsF,QAAUtF,MAAO,cAI7BsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,YACVzC,OACIlF,OAAS2B,MAAO,WAChB1B,QACIU,QAAUgB,MAAO,WACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QAAUgB,MAAO,WACjBwG,WAAaxG,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,YAEnBe,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,YAEnBQ,MAAQpB,QAAS,IACjB6C,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCK,SAAWE,SAAU,QACrB7B,QAAUe,YAAa,IAE3B2E,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAO,WACjB2G,gBAAkB3G,MAAO,WACzBmC,gBAAkBnC,MAAO,WACzB3B,OAAS2B,MAAO,WAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,WACjB+G,YAAc/G,MAAO,WACrBgH,YAAchH,MAAO,WACrBlB,MAAQkB,MAAO,aAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,aAGzBmH,SAAWnH,MAAO,YAEtBuF,UACIC,QACIC,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,WACjBqF,OACI4B,MAAQjH,MAAO,WACfsF,QAAUtF,MAAO,cAI7BoH,QACIC,OACI/B,QAAUtF,MAAO,WACjBiH,MAAQjH,MAAO,cAI3B0F,YAAcJ,QAAUtF,MAAO,YAC/B4F,oBACIN,QAAUtF,MAAO,WACjBmH,SAAWnH,MAAO,WAClB6F,WACIJ,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,YAErBsF,QAAUtF,MAAO,cAI7BsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKhBtB,EAAc,iBACVzC,OACIlF,OAAS2B,MAAO,QAChB1B,QACIU,QAAUgB,MAAO,QACjBuG,eACIvH,QAAUgB,MAAO,WACjBb,SAAWa,MAAO,aAG1B5B,gBACIY,QAAUgB,MAAO,QACjBwG,WAAaxG,MAAO,QACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,QAChBlB,MAAQkB,MAAO,YAEnBe,aACIrB,UAAW,UACXZ,MAAQkB,MAAO,YAEnBQ,MAAQpB,QAAS,IACjB6C,WAAanD,MAAQkB,MAAO,YAC5BoB,qBAAuBtC,MAAQkB,MAAO,YACtCK,SAAWE,SAAU,QACrB7B,QAAUe,YAAa,IAE3BiH,WAAa5G,WAAY,WACzBsE,cACI,UACA,UACA,UACA,UACA,UACA,WAEJpH,cACI8B,MAAQkB,MAAO,WACfhB,QAAUgB,MAAO,QACjB2G,gBAAkB3G,MAAO,WACzBmC,gBAAkBnC,MAAO,WACzB3B,OAAS2B,MAAO,QAChB4G,WAAa5G,MAAO,WACpBjD,OACI0B,MACIqB,WAAY,cACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,QAChBlB,MAAQkB,MAAO,cAI3BoG,OACIS,SAAW7G,MAAO,WAClBkF,OACI4B,sBAAuB,UACvB9H,QAAUgB,MAAO,QACjB+G,YAAc/G,MAAO,QACrBgH,YAAchH,MAAO,QACrBlB,MAAQkB,MAAO,UAGvBqG,SACIjB,eACI6B,MAAQjH,MAAO,WACfkH,mBACID,MAAQjH,MAAO,WACfsF,QAAUtF,MAAOlD,GACjBuI,OACI4B,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,aAGzBmH,SAAWnH,MAAO,SAEtBuF,UACIC,QACIC,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,QACjBqF,OACI4B,MAAQjH,MAAO,QACfsF,QAAUtF,MAAO,WAI7BoH,QACIC,OACI/B,QAAUtF,MAAO,QACjBiH,MAAQjH,MAAO,WAI3B0F,YAAcJ,QAAUtF,MAAO,SAC/B4F,oBACIN,QAAUtF,MAAO,WACjBmH,SAAWnH,MAAO,QAClB6F,WACIJ,SACIwB,MAAQjH,MAAOlD,GACfwI,QAAUtF,MAAO,SAErBsF,QAAUtF,MAAO,WAI7BsG,SACIgB,SAEQ,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,YAGA,UACA,eAKf,WAwBG,QAASE,KACL,OACI/I,MACIqB,WAAY,UACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAOyH,IA/B1B,GACOC,GAAO,UACPC,EAAW,UACXC,EAAiB,UACjBH,EAAO,UACPI,EAAa,UACbC,GACA,UACA,UACA,UACA,UACA,UACA,WAEAC,GACA,UACA,UACA,UACA,UACA,UACA,WAEAC,EAAUF,EAAO,GACjBG,EAAgBnL,CAWpBkJ,GAAc,SACVzC,OACIlF,OAAS2B,MAAO0H,GAChBpJ,QACIU,QAAUgB,MAAO0H,GACjBnB,eACIvH,QAAUgB,MAAO2H,GACjBxI,SAAWa,MAAO2H,KAG1BvJ,gBACIY,QAAUgB,MAAO0H,GACjBlB,WAAaxG,MAAO0H,GACpB3K,MAAOyK,IACPzG,aACIrB,UAAW+H,EACX3I,MAAQkB,MAAO4H,IAEnBpH,MAAQpB,QAAS,IACjB6C,WAAanD,MAAQkB,MAAOyH,IAC5BrG,qBAAuBtC,MAAQkB,MAAOyH,IACtCpH,SAAWE,SAAU,QACrB7B,QAAUe,YAAa,IAE3B2E,aAAc0D,EACd9K,cACI8B,MAAQkB,MAAOyH,GACfzI,QAAUgB,MAAO0H,GACjBf,gBAAkB3G,MAAO6H,GACzB1F,gBAAkBnC,MAAOyH,GACzBpJ,OAAS2B,MAAO0H,GAChBd,WAAa5G,MAAO2H,GACpB5K,MAAOyK,MAGfpB,OACIS,SAAW7G,MAAOgI,GAClB9C,OACI4B,sBAAuBW,EACvBzI,QAAUgB,MAAO0H,GACjBX,YAAc/G,MAAO0H,GACrBV,YAAchH,MAAO0H,GACrB5I,MAAQkB,MAAO0H,KAGvBrB,SACIjB,eACI6B,MAAQjH,MAAOgI,GACfd,mBACID,MAAQjH,MAAO0H,GACfpC,QAAUtF,MAAOiI,GACjB5C,OACI4B,MAAQjH,MAAOiI,GACf3C,QAAUtF,MAAO0H,KAGzBP,SAAWnH,MAAO0H,IAEtBnC,UACIC,QACIC,SACIwB,MAAQjH,MAAOiI,GACf3C,QAAUtF,MAAO4H,GACjBvC,OACI4B,MAAQjH,MAAO4H,GACftC,QAAUtF,MAAO4H,MAI7BR,QACIC,OACI/B,QAAUtF,MAAO4H,GACjBX,MAAQjH,MAAO4H,MAI3BlC,YAAcJ,QAAUtF,MAAO4H,IAC/BhC,oBACIN,QAAUtF,MAAO4H,GACjBT,SAAWnH,MAAO4H,GAClB/B,WACIJ,SACIwB,MAAQjH,MAAOiI,GACf3C,QAAUtF,MAAO4H,IAErBtC,QAAUtF,MAAO4H,MAI7BtB,SAAWgB,OAAQ3C,EAAKmD,EAAQC,SAGvC,WAwBG,QAASP,KACL,OACI/I,MACIqB,WAAY,UACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAOyH,IA/B1B,GACOC,GAAO,UACPC,EAAW,UACXC,EAAiB,UACjBH,EAAO,UACPI,EAAa,UACbC,GACA,UACA,UACA,UACA,UACA,UACA,WAEAC,GACA,UACA,UACA,UACA,UACA,UACA,WAEAC,EAAUF,EAAO,GACjBG,EAAgBnL,CAWpBkJ,GAAc,aACVzC,OACIlF,OAAS2B,MAAO0H,GAChBpJ,QACIU,QAAUgB,MAAO0H,GACjBnB,eACIvH,QAAUgB,MAAO2H,GACjBxI,SAAWa,MAAO2H,KAG1BvJ,gBACIY,QAAUgB,MAAO0H,GACjBlB,WAAaxG,MAAO0H,GACpB3K,MAAOyK,IACPzG,aACIrB,UAAW+H,EACX3I,MAAQkB,MAAO4H,IAEnBpH,MAAQpB,QAAS,IACjB6C,WAAanD,MAAQkB,MAAOyH,IAC5BrG,qBAAuBtC,MAAQkB,MAAOyH,IACtCpH,SAAWE,SAAU,QACrB7B,QAAUe,YAAa,IAE3B2E,aAAc0D,EACd9K,cACI8B,MAAQkB,MAAOyH,GACfzI,QAAUgB,MAAO0H,GACjBf,gBAAkB3G,MAAO6H,GACzB1F,gBAAkBnC,MAAOyH,GACzBpJ,OAAS2B,MAAO0H,GAChBd,WAAa5G,MAAO2H,GACpB5K,MAAOyK,MAGfpB,OACIS,SAAW7G,MAAOgI,GAClB9C,OACI4B,sBAAuBW,EACvBzI,QAAUgB,MAAO0H,GACjBX,YAAc/G,MAAO0H,GACrBV,YAAchH,MAAO0H,GACrB5I,MAAQkB,MAAO0H,KAGvBrB,SACIjB,eACI6B,MAAQjH,MAAOgI,GACfd,mBACID,MAAQjH,MAAO0H,GACfpC,QAAUtF,MAAOiI,GACjB5C,OACI4B,MAAQjH,MAAOiI,GACf3C,QAAUtF,MAAO0H,KAGzBP,SAAWnH,MAAO0H,IAEtBnC,UACIC,QACIC,SACIwB,MAAQjH,MAAOiI,GACf3C,QAAUtF,MAAO4H,GACjBvC,OACI4B,MAAQjH,MAAO4H,GACftC,QAAUtF,MAAO4H,MAI7BR,QACIC,OACI/B,QAAUtF,MAAO4H,GACjBX,MAAQjH,MAAO4H,MAI3BlC,YAAcJ,QAAUtF,MAAO4H,IAC/BhC,oBACIN,QAAUtF,MAAO4H,GACjBT,SAAWnH,MAAO4H,GAClB/B,WACIJ,SACIwB,MAAQjH,MAAOiI,GACf3C,QAAUtF,MAAO4H,IAErBtC,QAAUtF,MAAO4H,MAI7BtB,SAAWgB,OAAQ3C,EAAKmD,EAAQC,SAGvC,WAwBG,QAASP,KACL,OACI/I,MACIqB,WAAY,UACZpB,QAAUsB,MAAO,YAErBrB,OAASqB,MAAO,WAChBlB,MAAQkB,MAAOyH,IA/B1B,GACOC,GAAO,UACPC,EAAW,UACXC,EAAiB,UACjBH,EAAO,UACPI,EAAa,UACbC,GACA,UACA,UACA,UACA,UACA,UACA,WAEAC,GACA,UACA,UACA,UACA,UACA,UACA,WAEAC,EAAUF,EAAO,GACjBG,EAAgBnL,CAWpBkJ,GAAc,QACVzC,OACIlF,OAAS2B,MAAO0H,GAChBpJ,QACIU,QAAUgB,MAAO0H,GACjBnB,eACIvH,QAAUgB,MAAO2H,GACjBxI,SAAWa,MAAO2H,KAG1BvJ,gBACIY,QAAUgB,MAAO0H,GACjBlB,WAAaxG,MAAO0H,GACpB3K,MAAOyK,IACPzG,aACIrB,UAAW+H,EACX3I,MAAQkB,MAAO4H,IAEnBpH,MAAQpB,QAAS,IACjB6C,WAAanD,MAAQkB,MAAOyH,IAC5BrG,qBAAuBtC,MAAQkB,MAAOyH,IACtCpH,SAAWE,SAAU,QACrB7B,QAAUe,YAAa,IAE3B2E,aAAc0D,EACd9K,cACI8B,MAAQkB,MAAOyH,GACfzI,QAAUgB,MAAO0H,GACjBf,gBAAkB3G,MAAO6H,GACzB1F,gBAAkBnC,MAAOyH,GACzBpJ,OAAS2B,MAAO0H,GAChBd,WAAa5G,MAAO0H,GACpB3K,MAAOyK,MAGfpB,OACIS,SAAW7G,MAAOgI,GAClB9C,OACI4B,sBAAuBW,EACvBzI,QAAUgB,MAAO0H,GACjBX,YAAc/G,MAAO0H,GACrBV,YAAchH,MAAO0H,GACrB5I,MAAQkB,MAAO0H,KAGvBrB,SACIjB,eACI6B,MAAQjH,MAAOgI,GACfd,mBACID,MAAQjH,MAAO0H,GACfpC,QAAUtF,MAAOiI,GACjB5C,OACI4B,MAAQjH,MAAOiI,GACf3C,QAAUtF,MAAO0H,KAGzBP,SAAWnH,MAAO0H,IAEtBnC,UACIC,QACIC,SACIwB,MAAQjH,MAAOiI,GACf3C,QAAUtF,MAAO4H,GACjBvC,OACI4B,MAAQjH,MAAO4H,GACftC,QAAUtF,MAAO4H,MAI7BR,QACIC,OACI/B,QAAUtF,MAAO4H,GACjBX,MAAQjH,MAAO4H,MAI3BlC,YAAcJ,QAAUtF,MAAO4H,IAC/BhC,oBACIN,QAAUtF,MAAO4H,GACjBT,SAAWnH,MAAO4H,GAClB/B,WACIJ,SACIwB,MAAQjH,MAAOiI,GACf3C,QAAUtF,MAAO4H,IAErBtC,QAAUtF,MAAO4H,MAI7BtB,SAAWgB,OAAQ3C,EAAKmD,EAAQC,SAGvC,WAAA,GACOD,IACA,UACA,UACA,UACA,UACA,UACA,WAEAC,GACA,UACA,UACA,UACA,UACA,UACA,UAEJ/B,GAAc,cACVzC,SACA6C,SACAC,WACAC,SAAWgB,OAAQ3C,EAAKmD,EAAQC,MAEpChC,EAAOmC,KAAOnC,EAAO,iBAExB,WAAA,GACO2B,GAAO,UACPD,EAAO,qBACPK,GACA,UACA,UACA,UACA,UACA,UACA,WAEAC,GACA,UACA,UACA,UACA,UACA,UACA,WAEAC,EAAUF,EAAO,EACrB9B,GAAc,gBACVzC,SACA6C,OACIS,SAAW7G,MAAOgI,GAClB9C,OACI4B,sBAAuBW,EACvBzI,QAAUgB,MAAO0H,GACjBX,YAAc/G,MAAO0H,GACrBV,YAAchH,MAAO0H,GACrB5I,MAAQkB,MAAO0H,KAGvBrB,WACAC,SAAWgB,OAAQ3C,EAAKmD,EAAQC,UAW1C1O,OAAOC,MAAM2C,QACR5C,OAAOC,OACE,kBAAVnC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD,OAEV,SAAUlF,EAAGC,QACVA,OAAO,wBACH,qBACA,kCACA,4BACA,yBACDD,IACL,aASkB,kBAAVC,SAAwBA,OAAO+E,IAAM/E,OAAS,SAAUgF,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dataviz.themes.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('util/text-metrics', ['kendo.core'], f);\n}(function () {\n    (function ($) {\n        window.kendo.util = window.kendo.util || {};\n        var LRUCache = kendo.Class.extend({\n            init: function (size) {\n                this._size = size;\n                this._length = 0;\n                this._map = {};\n            },\n            put: function (key, value) {\n                var map = this._map;\n                var entry = {\n                    key: key,\n                    value: value\n                };\n                map[key] = entry;\n                if (!this._head) {\n                    this._head = this._tail = entry;\n                } else {\n                    this._tail.newer = entry;\n                    entry.older = this._tail;\n                    this._tail = entry;\n                }\n                if (this._length >= this._size) {\n                    map[this._head.key] = null;\n                    this._head = this._head.newer;\n                    this._head.older = null;\n                } else {\n                    this._length++;\n                }\n            },\n            get: function (key) {\n                var entry = this._map[key];\n                if (entry) {\n                    if (entry === this._head && entry !== this._tail) {\n                        this._head = entry.newer;\n                        this._head.older = null;\n                    }\n                    if (entry !== this._tail) {\n                        if (entry.older) {\n                            entry.older.newer = entry.newer;\n                            entry.newer.older = entry.older;\n                        }\n                        entry.older = this._tail;\n                        entry.newer = null;\n                        this._tail.newer = entry;\n                        this._tail = entry;\n                    }\n                    return entry.value;\n                }\n            }\n        });\n        var REPLACE_REGEX = /\\r?\\n|\\r|\\t/g;\n        var SPACE = ' ';\n        function normalizeText(text) {\n            return String(text).replace(REPLACE_REGEX, SPACE);\n        }\n        function objectKey(object) {\n            var parts = [];\n            for (var key in object) {\n                parts.push(key + object[key]);\n            }\n            return parts.sort().join('');\n        }\n        function hashKey(str) {\n            var hash = 2166136261;\n            for (var i = 0; i < str.length; ++i) {\n                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n                hash ^= str.charCodeAt(i);\n            }\n            return hash >>> 0;\n        }\n        function zeroSize() {\n            return {\n                width: 0,\n                height: 0,\n                baseline: 0\n            };\n        }\n        var DEFAULT_OPTIONS = { baselineMarkerSize: 1 };\n        var defaultMeasureBox;\n        if (typeof document !== 'undefined') {\n            defaultMeasureBox = document.createElement('div');\n            defaultMeasureBox.style.cssText = 'position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;' + 'padding: 0 !important; margin: 0 !important; border: 0 !important;' + 'line-height: normal !important; visibility: hidden !important; white-space: pre!important;';\n        }\n        var TextMetrics = kendo.Class.extend({\n            init: function (options) {\n                this._cache = new LRUCache(1000);\n                this.options = $.extend({}, DEFAULT_OPTIONS, options);\n            },\n            measure: function (text, style, options) {\n                if (options === void 0) {\n                    options = {};\n                }\n                if (!text) {\n                    return zeroSize();\n                }\n                var styleKey = objectKey(style);\n                var cacheKey = hashKey(text + styleKey);\n                var cachedResult = this._cache.get(cacheKey);\n                if (cachedResult) {\n                    return cachedResult;\n                }\n                var size = zeroSize();\n                var measureBox = options.box || defaultMeasureBox;\n                var baselineMarker = this._baselineMarker().cloneNode(false);\n                for (var key in style) {\n                    var value = style[key];\n                    if (typeof value !== 'undefined') {\n                        measureBox.style[key] = value;\n                    }\n                }\n                var textStr = options.normalizeText !== false ? normalizeText(text) : String(text);\n                measureBox.textContent = textStr;\n                measureBox.appendChild(baselineMarker);\n                document.body.appendChild(measureBox);\n                if (textStr.length) {\n                    size.width = measureBox.offsetWidth - this.options.baselineMarkerSize;\n                    size.height = measureBox.offsetHeight;\n                    size.baseline = baselineMarker.offsetTop + this.options.baselineMarkerSize;\n                }\n                if (size.width > 0 && size.height > 0) {\n                    this._cache.put(cacheKey, size);\n                }\n                measureBox.parentNode.removeChild(measureBox);\n                return size;\n            },\n            _baselineMarker: function () {\n                var marker = document.createElement('div');\n                marker.style.cssText = 'display: inline-block; vertical-align: baseline;width: ' + this.options.baselineMarkerSize + 'px; height: ' + this.options.baselineMarkerSize + 'px;overflow: hidden;';\n                return marker;\n            }\n        });\n        TextMetrics.current = new TextMetrics();\n        function measureText(text, style, measureBox) {\n            return TextMetrics.current.measure(text, style, measureBox);\n        }\n        kendo.deepExtend(kendo.util, {\n            LRUCache: LRUCache,\n            TextMetrics: TextMetrics,\n            measureText: measureText,\n            objectKey: objectKey,\n            hashKey: hashKey,\n            normalizeText: normalizeText\n        });\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/themes/chart-base-theme', ['kendo.dataviz.core'], f);\n}(function () {\n    (function () {\n        window.kendo.dataviz = window.kendo.dataviz || {};\n        var BAR_GAP = 1.5;\n        var BAR_SPACING = 0.4;\n        var BLACK = '#000';\n        var SANS = 'Arial, Helvetica, sans-serif';\n        var SANS11 = '11px ' + SANS;\n        var SANS12 = '12px ' + SANS;\n        var SANS16 = '16px ' + SANS;\n        var TRANSPARENT = 'transparent';\n        var WHITE = '#fff';\n        var notes = function () {\n            return {\n                icon: { border: { width: 1 } },\n                label: {\n                    font: SANS12,\n                    padding: 3\n                },\n                line: {\n                    length: 10,\n                    width: 2\n                },\n                visible: true\n            };\n        };\n        var axisDefaults = function () {\n            return {\n                labels: { font: SANS12 },\n                notes: notes(),\n                title: {\n                    font: SANS16,\n                    margin: 5\n                }\n            };\n        };\n        var areaSeries = function () {\n            return {\n                highlight: { markers: { border: {} } },\n                line: {\n                    opacity: 1,\n                    width: 0\n                },\n                markers: {\n                    size: 6,\n                    visible: false\n                },\n                opacity: 0.4\n            };\n        };\n        var rangeAreaSeries = function () {\n            return {\n                highlight: { markers: { border: {} } },\n                line: {\n                    opacity: 1,\n                    width: 0\n                },\n                markers: {\n                    size: 6,\n                    visible: false\n                },\n                opacity: 0.4\n            };\n        };\n        var barSeries = function () {\n            return {\n                gap: BAR_GAP,\n                spacing: BAR_SPACING\n            };\n        };\n        var boxPlotSeries = function () {\n            return {\n                outliersField: '',\n                meanField: '',\n                border: {\n                    _brightness: 0.8,\n                    width: 1\n                },\n                downColor: WHITE,\n                gap: 1,\n                highlight: {\n                    border: {\n                        opacity: 1,\n                        width: 2\n                    },\n                    whiskers: { width: 3 },\n                    mean: { width: 2 },\n                    median: { width: 2 }\n                },\n                mean: { width: 2 },\n                median: { width: 2 },\n                spacing: 0.3,\n                whiskers: { width: 2 }\n            };\n        };\n        var bubbleSeries = function () {\n            return {\n                border: { width: 0 },\n                labels: { background: TRANSPARENT },\n                opacity: 0.6\n            };\n        };\n        var bulletSeries = function () {\n            return {\n                gap: BAR_GAP,\n                spacing: BAR_SPACING,\n                target: { color: '#ff0000' }\n            };\n        };\n        var candlestickSeries = function () {\n            return {\n                border: {\n                    _brightness: 0.8,\n                    width: 1\n                },\n                downColor: WHITE,\n                gap: 1,\n                highlight: {\n                    border: {\n                        opacity: 1,\n                        width: 2\n                    },\n                    line: { width: 2 }\n                },\n                line: {\n                    color: BLACK,\n                    width: 1\n                },\n                spacing: 0.3\n            };\n        };\n        var columnSeries = function () {\n            return {\n                gap: BAR_GAP,\n                spacing: BAR_SPACING\n            };\n        };\n        var donutSeries = function () {\n            return { margin: 1 };\n        };\n        var lineSeries = function () {\n            return { width: 2 };\n        };\n        var ohlcSeries = function () {\n            return {\n                gap: 1,\n                highlight: {\n                    line: {\n                        opacity: 1,\n                        width: 3\n                    }\n                },\n                line: { width: 1 },\n                spacing: 0.3\n            };\n        };\n        var radarAreaSeries = function () {\n            return {\n                line: {\n                    opacity: 1,\n                    width: 0\n                },\n                markers: {\n                    size: 6,\n                    visible: false\n                },\n                opacity: 0.5\n            };\n        };\n        var radarLineSeries = function () {\n            return {\n                markers: { visible: false },\n                width: 2\n            };\n        };\n        var rangeBarSeries = function () {\n            return {\n                gap: BAR_GAP,\n                spacing: BAR_SPACING\n            };\n        };\n        var rangeColumnSeries = function () {\n            return {\n                gap: BAR_GAP,\n                spacing: BAR_SPACING\n            };\n        };\n        var scatterLineSeries = function () {\n            return { width: 1 };\n        };\n        var waterfallSeries = function () {\n            return {\n                gap: 0.5,\n                line: {\n                    color: BLACK,\n                    width: 1\n                },\n                spacing: BAR_SPACING\n            };\n        };\n        var pieSeries = function () {\n            return {\n                labels: {\n                    background: '',\n                    color: '',\n                    padding: {\n                        top: 5,\n                        bottom: 5,\n                        left: 7,\n                        right: 7\n                    }\n                }\n            };\n        };\n        var funnelSeries = function () {\n            return {\n                labels: {\n                    background: '',\n                    color: '',\n                    padding: {\n                        top: 5,\n                        bottom: 5,\n                        left: 7,\n                        right: 7\n                    }\n                }\n            };\n        };\n        var seriesDefaults = function (options) {\n            return {\n                visible: true,\n                labels: { font: SANS11 },\n                overlay: options.gradients ? {} : { gradient: 'none' },\n                area: areaSeries(),\n                rangeArea: rangeAreaSeries(),\n                verticalRangeArea: rangeAreaSeries(),\n                bar: barSeries(),\n                boxPlot: boxPlotSeries(),\n                bubble: bubbleSeries(),\n                bullet: bulletSeries(),\n                candlestick: candlestickSeries(),\n                column: columnSeries(),\n                pie: pieSeries(),\n                donut: donutSeries(),\n                funnel: funnelSeries(),\n                horizontalWaterfall: waterfallSeries(),\n                line: lineSeries(),\n                notes: notes(),\n                ohlc: ohlcSeries(),\n                radarArea: radarAreaSeries(),\n                radarLine: radarLineSeries(),\n                polarArea: radarAreaSeries(),\n                polarLine: radarLineSeries(),\n                rangeBar: rangeBarSeries(),\n                rangeColumn: rangeColumnSeries(),\n                scatterLine: scatterLineSeries(),\n                verticalArea: areaSeries(),\n                verticalBoxPlot: boxPlotSeries(),\n                verticalBullet: bulletSeries(),\n                verticalLine: lineSeries(),\n                waterfall: waterfallSeries()\n            };\n        };\n        var title = function () {\n            return { font: SANS16 };\n        };\n        var legend = function () {\n            return { labels: { font: SANS12 } };\n        };\n        var baseTheme = function (options) {\n            if (options === void 0) {\n                options = {};\n            }\n            return {\n                axisDefaults: axisDefaults(),\n                categoryAxis: { majorGridLines: { visible: true } },\n                navigator: {\n                    pane: {\n                        height: 90,\n                        margin: { top: 10 }\n                    }\n                },\n                seriesDefaults: seriesDefaults(options),\n                title: title(),\n                legend: legend()\n            };\n        };\n        kendo.deepExtend(kendo.dataviz, { chartBaseTheme: baseTheme });\n    }());\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/themes/auto-theme', ['kendo.dataviz.core'], f);\n}(function () {\n    (function ($) {\n        var cache;\n        function autoTheme(force) {\n            if (!force && cache) {\n                return cache;\n            }\n            var theme = { chart: kendo.dataviz.chartBaseTheme() };\n            var hook = $('<div style=\"display: none\">' + '  <div class=\"k-var--accent\"></div>' + '  <div class=\"k-var--accent-contrast\"></div>' + '  <div class=\"k-var--base\"></div>' + '  <div class=\"k-var--background\"></div>' + '  <div class=\"k-var--normal-background\"></div>' + '  <div class=\"k-var--normal-text-color\"></div>' + '  <div class=\"k-var--hover-background\"></div>' + '  <div class=\"k-var--hover-text-color\"></div>' + '  <div class=\"k-var--selected-background\"></div>' + '  <div class=\"k-var--selected-text-color\"></div>' + '  <div class=\"k-var--chart-error-bars-background\"></div>' + '  <div class=\"k-var--chart-notes-background\"></div>' + '  <div class=\"k-var--chart-notes-border\"></div>' + '  <div class=\"k-var--chart-notes-lines\"></div>' + '  <div class=\"k-var--chart-crosshair-background\"></div>' + '  <div class=\"k-var--chart-inactive\"></div>' + '  <div class=\"k-var--chart-major-lines\"></div>' + '  <div class=\"k-var--chart-minor-lines\"></div>' + '  <div class=\"k-var--chart-area-opacity\"></div>' + '  <div class=\"k-widget\">' + '      <div class=\"k-var--chart-font\"></div>' + '      <div class=\"k-var--chart-title-font\"></div>' + '      <div class=\"k-var--chart-label-font\"></div>' + '  </div>' + '  <div class=\"k-var--series\">' + '    <div class=\"k-var--series-a\"></div>' + '    <div class=\"k-var--series-b\"></div>' + '    <div class=\"k-var--series-c\"></div>' + '    <div class=\"k-var--series-d\"></div>' + '    <div class=\"k-var--series-e\"></div>' + '    <div class=\"k-var--series-f\"></div>' + '  </div>' + '  <div class=\"k-var--gauge-pointer\"></div>' + '  <div class=\"k-var--gauge-track\"></div>' + '</div>').appendTo(document.body);\n            function mapColor(key, varName) {\n                set(key, queryStyle(varName, 'backgroundColor'));\n            }\n            function queryStyle(varName, prop) {\n                return hook.find('.k-var--' + varName).css(prop);\n            }\n            function set(path, value) {\n                var store = theme;\n                var parts = path.split('.');\n                var key = parts.shift();\n                while (parts.length > 0) {\n                    store = store[key] = store[key] || {};\n                    key = parts.shift();\n                }\n                store[key] = value;\n            }\n            (function setColors() {\n                mapColor('chart.axisDefaults.crosshair.color', 'chart-crosshair-background');\n                mapColor('chart.axisDefaults.labels.color', 'normal-text-color');\n                mapColor('chart.axisDefaults.line.color', 'chart-major-lines');\n                mapColor('chart.axisDefaults.majorGridLines.color', 'chart-major-lines');\n                mapColor('chart.axisDefaults.minorGridLines.color', 'chart-minor-lines');\n                mapColor('chart.axisDefaults.notes.icon.background', 'chart-notes-background');\n                mapColor('chart.axisDefaults.notes.icon.border.color', 'chart-notes-border');\n                mapColor('chart.axisDefaults.notes.line.color', 'chart-notes-lines');\n                mapColor('chart.axisDefaults.title.color', 'normal-text-color');\n                mapColor('chart.chartArea.background', 'background');\n                mapColor('chart.legend.inactiveItems.labels.color', 'chart-inactive');\n                mapColor('chart.legend.inactiveItems.markers.color', 'chart-inactive');\n                mapColor('chart.legend.labels.color', 'normal-text-color');\n                mapColor('chart.seriesDefaults.boxPlot.downColor', 'chart-major-lines');\n                mapColor('chart.seriesDefaults.boxPlot.mean.color', 'base');\n                mapColor('chart.seriesDefaults.boxPlot.median.color', 'base');\n                mapColor('chart.seriesDefaults.boxPlot.whiskers.color', 'accent');\n                mapColor('chart.seriesDefaults.bullet.target.color', 'accent');\n                mapColor('chart.seriesDefaults.candlestick.downColor', 'normal-text-color');\n                mapColor('chart.seriesDefaults.candlestick.line.color', 'normal-text-color');\n                mapColor('chart.seriesDefaults.errorBars.color', 'chart-error-bars-background');\n                mapColor('chart.seriesDefaults.horizontalWaterfall.line.color', 'chart-major-lines');\n                mapColor('chart.seriesDefaults.icon.border.color', 'chart-major-lines');\n                mapColor('chart.seriesDefaults.labels.background', 'background');\n                mapColor('chart.seriesDefaults.labels.color', 'normal-text-color');\n                mapColor('chart.seriesDefaults.notes.icon.background', 'chart-notes-background');\n                mapColor('chart.seriesDefaults.notes.icon.border.color', 'chart-notes-border');\n                mapColor('chart.seriesDefaults.notes.line.color', 'chart-notes-lines');\n                mapColor('chart.seriesDefaults.verticalBoxPlot.downColor', 'chart-major-lines');\n                mapColor('chart.seriesDefaults.verticalBoxPlot.mean.color', 'base');\n                mapColor('chart.seriesDefaults.verticalBoxPlot.median.color', 'base');\n                mapColor('chart.seriesDefaults.verticalBoxPlot.whiskers.color', 'accent');\n                mapColor('chart.seriesDefaults.verticalBullet.target.color', 'accent');\n                mapColor('chart.seriesDefaults.waterfall.line.color', 'chart-major-lines');\n                mapColor('chart.title.color', 'normal-text-color');\n                set('chart.seriesDefaults.labels.opacity', queryStyle('chart-area-opacity', 'opacity'));\n                mapColor('diagram.shapeDefaults.fill.color', 'accent');\n                mapColor('diagram.shapeDefaults.content.color', 'accent-contrast');\n                mapColor('diagram.shapeDefaults.connectorDefaults.fill.color', 'normal-text-color');\n                mapColor('diagram.shapeDefaults.connectorDefaults.stroke.color', 'accent-contrast');\n                mapColor('diagram.shapeDefaults.connectorDefaults.hover.fill.color', 'accent-contrast');\n                mapColor('diagram.shapeDefaults.connectorDefaults.hover.stroke.color', 'normal-text-color');\n                mapColor('diagram.editable.resize.handles.stroke.color', 'normal-text-color');\n                mapColor('diagram.editable.resize.handles.fill.color', 'normal-background');\n                mapColor('diagram.editable.resize.handles.hover.stroke.color', 'normal-text-color');\n                mapColor('diagram.editable.resize.handles.hover.fill.color', 'normal-text-color');\n                mapColor('diagram.selectable.stroke.color', 'normal-text-color');\n                mapColor('diagram.connectionDefaults.stroke.color', 'normal-text-color');\n                mapColor('diagram.connectionDefaults.content.color', 'normal-text-color');\n                mapColor('diagram.connectionDefaults.selection.handles.fill.color', 'accent-contrast');\n                mapColor('diagram.connectionDefaults.selection.handles.stroke.color', 'normal-text-color');\n                mapColor('diagram.connectionDefaults.selection.stroke.color', 'normal-text-color');\n                mapColor('gauge.pointer.color', 'gauge-pointer');\n                mapColor('gauge.scale.labels.color', 'normal-text-color');\n                mapColor('gauge.scale.minorTicks.color', 'normal-text-color');\n                mapColor('gauge.scale.majorTicks.color', 'normal-text-color');\n                mapColor('gauge.scale.line.color', 'normal-text-color');\n                mapColor('gauge.scale.rangePlaceholderColor', 'gauge-track');\n            }());\n            (function setFonts() {\n                function font(varName) {\n                    return queryStyle(varName, 'fontSize') + ' ' + queryStyle(varName, 'fontFamily');\n                }\n                var defaultFont = font('chart-font');\n                var titleFont = font('chart-title-font');\n                var labelFont = font('chart-label-font');\n                set('chart.axisDefaults.labels.font', labelFont);\n                set('chart.axisDefaults.notes.label.font', defaultFont);\n                set('chart.axisDefaults.title.font', defaultFont);\n                set('chart.legend.labels.font', defaultFont);\n                set('chart.seriesDefaults.labels.font', labelFont);\n                set('chart.seriesDefaults.notes.label.font', defaultFont);\n                set('chart.title.font', titleFont);\n            }());\n            (function setSeriesColors() {\n                function letterPos(letter) {\n                    return letter.toLowerCase().charCodeAt(0) - 'a'.charCodeAt(0);\n                }\n                function seriesPos(name) {\n                    return letterPos(name.match(/series-([a-z])$/)[1]);\n                }\n                var series = $('.k-var--series div').toArray();\n                var seriesColors = series.reduce(function (arr, el) {\n                    var pos = seriesPos(el.className);\n                    arr[pos] = $(el).css('backgroundColor');\n                    return arr;\n                }, []);\n                set('chart.seriesColors', seriesColors);\n            }());\n            hook.remove();\n            cache = theme;\n            return theme;\n        }\n        kendo.dataviz.autoTheme = autoTheme;\n    }(window.kendo.jQuery));\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('dataviz/themes/themes', ['dataviz/themes/chart-base-theme'], f);\n}(function () {\n    (function ($) {\n        var kendo = window.kendo, ui = kendo.dataviz.ui, deepExtend = kendo.deepExtend;\n        var BLACK = '#000', SANS = 'Arial,Helvetica,sans-serif', SANS12 = '12px ' + SANS, WHITE = '#fff';\n        var chartBaseTheme = kendo.dataviz.chartBaseTheme({ gradients: true });\n        var gaugeBaseTheme = { scale: { labels: { font: SANS12 } } };\n        var diagramBaseTheme = {\n            shapeDefaults: {\n                hover: { opacity: 0.2 },\n                stroke: { width: 0 }\n            },\n            editable: {\n                resize: {\n                    handles: {\n                        width: 7,\n                        height: 7\n                    }\n                }\n            },\n            selectable: {\n                stroke: {\n                    width: 1,\n                    dashType: 'dot'\n                }\n            },\n            connectionDefaults: {\n                stroke: { width: 2 },\n                selection: {\n                    handles: {\n                        width: 8,\n                        height: 8\n                    }\n                },\n                editable: {\n                    tools: [\n                        'edit',\n                        'delete'\n                    ]\n                }\n            }\n        };\n        var themes = ui.themes, registerTheme = ui.registerTheme = function (themeName, options) {\n                var result = {};\n                result.chart = deepExtend({}, chartBaseTheme, options.chart);\n                result.gauge = deepExtend({}, gaugeBaseTheme, options.gauge);\n                result.diagram = deepExtend({}, diagramBaseTheme, options.diagram);\n                result.treeMap = deepExtend({}, options.treeMap);\n                var defaults = result.chart.seriesDefaults;\n                defaults.verticalLine = deepExtend({}, defaults.line);\n                defaults.verticalArea = deepExtend({}, defaults.area);\n                defaults.rangeArea = deepExtend({}, defaults.area);\n                defaults.verticalRangeArea = deepExtend({}, defaults.rangeArea);\n                defaults.verticalBoxPlot = deepExtend({}, defaults.boxPlot);\n                defaults.polarArea = deepExtend({}, defaults.radarArea);\n                defaults.polarLine = deepExtend({}, defaults.radarLine);\n                themes[themeName] = result;\n            };\n        registerTheme('black', {\n            chart: {\n                title: { color: WHITE },\n                legend: {\n                    labels: { color: WHITE },\n                    inactiveItems: {\n                        labels: { color: '#919191' },\n                        markers: { color: '#919191' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: { color: WHITE },\n                    errorBars: { color: WHITE },\n                    notes: {\n                        icon: {\n                            background: '#3b3b3b',\n                            border: { color: '#8e8e8e' }\n                        },\n                        label: { color: WHITE },\n                        line: { color: '#8e8e8e' }\n                    },\n                    pie: { overlay: { gradient: 'sharpBevel' } },\n                    donut: { overlay: { gradient: 'sharpGlass' } },\n                    line: { markers: { background: '#3d3d3d' } },\n                    scatter: { markers: { background: '#3d3d3d' } },\n                    scatterLine: { markers: { background: '#3d3d3d' } },\n                    waterfall: { line: { color: '#8e8e8e' } },\n                    horizontalWaterfall: { line: { color: '#8e8e8e' } },\n                    candlestick: {\n                        downColor: '#555',\n                        line: { color: WHITE },\n                        border: {\n                            _brightness: 1.5,\n                            opacity: 1\n                        },\n                        highlight: {\n                            border: {\n                                color: WHITE,\n                                opacity: 0.2\n                            }\n                        }\n                    },\n                    ohlc: { line: { color: WHITE } }\n                },\n                chartArea: { background: '#3d3d3d' },\n                seriesColors: [\n                    '#0081da',\n                    '#3aafff',\n                    '#99c900',\n                    '#ffeb3d',\n                    '#b20753',\n                    '#ff4195'\n                ],\n                axisDefaults: {\n                    line: { color: '#8e8e8e' },\n                    labels: { color: WHITE },\n                    majorGridLines: { color: '#545454' },\n                    minorGridLines: { color: '#454545' },\n                    title: { color: WHITE },\n                    crosshair: { color: '#8e8e8e' },\n                    notes: {\n                        icon: {\n                            background: '#3b3b3b',\n                            border: { color: '#8e8e8e' }\n                        },\n                        label: { color: WHITE },\n                        line: { color: '#8e8e8e' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#0070e4' },\n                scale: {\n                    rangePlaceholderColor: '#1d1d1d',\n                    labels: { color: WHITE },\n                    minorTicks: { color: WHITE },\n                    majorTicks: { color: WHITE },\n                    line: { color: WHITE }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#0066cc' },\n                    connectorDefaults: {\n                        fill: { color: WHITE },\n                        stroke: { color: '#384049' },\n                        hover: {\n                            fill: { color: '#3d3d3d' },\n                            stroke: { color: '#efefef' }\n                        }\n                    },\n                    content: { color: WHITE }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: '#3d3d3d' },\n                            stroke: { color: WHITE },\n                            hover: {\n                                fill: { color: WHITE },\n                                stroke: { color: WHITE }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: WHITE },\n                            fill: { color: WHITE }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: WHITE } },\n                connectionDefaults: {\n                    stroke: { color: WHITE },\n                    content: { color: WHITE },\n                    selection: {\n                        handles: {\n                            fill: { color: '#3d3d3d' },\n                            stroke: { color: '#efefef' }\n                        }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#0081da',\n                        '#314b5c'\n                    ],\n                    [\n                        '#3aafff',\n                        '#3c5464'\n                    ],\n                    [\n                        '#99c900',\n                        '#4f5931'\n                    ],\n                    [\n                        '#ffeb3d',\n                        '#64603d'\n                    ],\n                    [\n                        '#b20753',\n                        '#543241'\n                    ],\n                    [\n                        '#ff4195',\n                        '#643e4f'\n                    ]\n                ]\n            }\n        });\n        registerTheme('blueopal', {\n            chart: {\n                title: { color: '#293135' },\n                legend: {\n                    labels: { color: '#293135' },\n                    inactiveItems: {\n                        labels: { color: '#27A5BA' },\n                        markers: { color: '#27A5BA' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: {\n                        color: BLACK,\n                        background: WHITE,\n                        opacity: 0.5\n                    },\n                    errorBars: { color: '#293135' },\n                    candlestick: {\n                        downColor: '#c4d0d5',\n                        line: { color: '#9aabb2' }\n                    },\n                    waterfall: { line: { color: '#9aabb2' } },\n                    horizontalWaterfall: { line: { color: '#9aabb2' } },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#9aabb2' }\n                        },\n                        label: { color: '#293135' },\n                        line: { color: '#9aabb2' }\n                    }\n                },\n                seriesColors: [\n                    '#0069a5',\n                    '#0098ee',\n                    '#7bd2f6',\n                    '#ffb800',\n                    '#ff8517',\n                    '#e34a00'\n                ],\n                axisDefaults: {\n                    line: { color: '#9aabb2' },\n                    labels: { color: '#293135' },\n                    majorGridLines: { color: '#c4d0d5' },\n                    minorGridLines: { color: '#edf1f2' },\n                    title: { color: '#293135' },\n                    crosshair: { color: '#9aabb2' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#9aabb2' }\n                        },\n                        label: { color: '#293135' },\n                        line: { color: '#9aabb2' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#005c83' },\n                scale: {\n                    rangePlaceholderColor: '#daecf4',\n                    labels: { color: '#293135' },\n                    minorTicks: { color: '#293135' },\n                    majorTicks: { color: '#293135' },\n                    line: { color: '#293135' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#7ec6e3' },\n                    connectorDefaults: {\n                        fill: { color: '#003f59' },\n                        stroke: { color: WHITE },\n                        hover: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#003f59' }\n                        }\n                    },\n                    content: { color: '#293135' }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#003f59' },\n                            hover: {\n                                fill: { color: '#003f59' },\n                                stroke: { color: '#003f59' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: '#003f59' },\n                            fill: { color: '#003f59' }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#003f59' } },\n                connectionDefaults: {\n                    stroke: { color: '#003f59' },\n                    content: { color: '#293135' },\n                    selection: {\n                        handles: {\n                            fill: { color: '#3d3d3d' },\n                            stroke: { color: '#efefef' }\n                        }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#0069a5',\n                        '#bad7e7'\n                    ],\n                    [\n                        '#0098ee',\n                        '#b9e0f5'\n                    ],\n                    [\n                        '#7bd2f6',\n                        '#ceeaf6'\n                    ],\n                    [\n                        '#ffb800',\n                        '#e6e3c4'\n                    ],\n                    [\n                        '#ff8517',\n                        '#e4d8c8'\n                    ],\n                    [\n                        '#e34a00',\n                        '#ddccc2'\n                    ]\n                ]\n            }\n        });\n        registerTheme('highcontrast', {\n            chart: {\n                title: { color: '#ffffff' },\n                legend: {\n                    labels: { color: '#ffffff' },\n                    inactiveItems: {\n                        labels: { color: '#66465B' },\n                        markers: { color: '#66465B' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: { color: '#ffffff' },\n                    errorBars: { color: '#ffffff' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#ffffff' }\n                        },\n                        label: { color: '#ffffff' },\n                        line: { color: '#ffffff' }\n                    },\n                    pie: { overlay: { gradient: 'sharpGlass' } },\n                    donut: { overlay: { gradient: 'sharpGlass' } },\n                    line: { markers: { background: '#2c232b' } },\n                    scatter: { markers: { background: '#2c232b' } },\n                    scatterLine: { markers: { background: '#2c232b' } },\n                    area: { opacity: 0.5 },\n                    waterfall: { line: { color: '#ffffff' } },\n                    horizontalWaterfall: { line: { color: '#ffffff' } },\n                    candlestick: {\n                        downColor: '#664e62',\n                        line: { color: '#ffffff' },\n                        border: {\n                            _brightness: 1.5,\n                            opacity: 1\n                        },\n                        highlight: {\n                            border: {\n                                color: '#ffffff',\n                                opacity: 1\n                            }\n                        }\n                    },\n                    ohlc: { line: { color: '#ffffff' } }\n                },\n                chartArea: { background: '#2c232b' },\n                seriesColors: [\n                    '#a7008f',\n                    '#ffb800',\n                    '#3aafff',\n                    '#99c900',\n                    '#b20753',\n                    '#ff4195'\n                ],\n                axisDefaults: {\n                    line: { color: '#ffffff' },\n                    labels: { color: '#ffffff' },\n                    majorGridLines: { color: '#664e62' },\n                    minorGridLines: { color: '#4f394b' },\n                    title: { color: '#ffffff' },\n                    crosshair: { color: '#ffffff' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#ffffff' }\n                        },\n                        label: { color: '#ffffff' },\n                        line: { color: '#ffffff' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#a7008f' },\n                scale: {\n                    rangePlaceholderColor: '#2c232b',\n                    labels: { color: '#ffffff' },\n                    minorTicks: { color: '#2c232b' },\n                    majorTicks: { color: '#664e62' },\n                    line: { color: '#ffffff' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#a7018f' },\n                    connectorDefaults: {\n                        fill: { color: WHITE },\n                        stroke: { color: '#2c232b' },\n                        hover: {\n                            fill: { color: '#2c232b' },\n                            stroke: { color: WHITE }\n                        }\n                    },\n                    content: { color: WHITE }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: '#2c232b' },\n                            stroke: { color: WHITE },\n                            hover: {\n                                fill: { color: WHITE },\n                                stroke: { color: WHITE }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: WHITE },\n                            fill: { color: WHITE }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: WHITE } },\n                connectionDefaults: {\n                    stroke: { color: WHITE },\n                    content: { color: WHITE },\n                    selection: {\n                        handles: {\n                            fill: { color: '#2c232b' },\n                            stroke: { color: WHITE }\n                        }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#a7008f',\n                        '#451c3f'\n                    ],\n                    [\n                        '#ffb800',\n                        '#564122'\n                    ],\n                    [\n                        '#3aafff',\n                        '#2f3f55'\n                    ],\n                    [\n                        '#99c900',\n                        '#424422'\n                    ],\n                    [\n                        '#b20753',\n                        '#471d33'\n                    ],\n                    [\n                        '#ff4195',\n                        '#562940'\n                    ]\n                ]\n            }\n        });\n        registerTheme('default', {\n            chart: {\n                title: { color: '#8e8e8e' },\n                legend: {\n                    labels: { color: '#232323' },\n                    inactiveItems: {\n                        labels: { color: '#919191' },\n                        markers: { color: '#919191' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: {\n                        color: BLACK,\n                        background: WHITE,\n                        opacity: 0.5\n                    },\n                    errorBars: { color: '#232323' },\n                    candlestick: {\n                        downColor: '#dedede',\n                        line: { color: '#8d8d8d' }\n                    },\n                    waterfall: { line: { color: '#8e8e8e' } },\n                    horizontalWaterfall: { line: { color: '#8e8e8e' } },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#8e8e8e' }\n                        },\n                        label: { color: '#232323' },\n                        line: { color: '#8e8e8e' }\n                    }\n                },\n                seriesColors: [\n                    '#ff6800',\n                    '#a0a700',\n                    '#ff8d00',\n                    '#678900',\n                    '#ffb53c',\n                    '#396000'\n                ],\n                axisDefaults: {\n                    line: { color: '#8e8e8e' },\n                    labels: { color: '#232323' },\n                    minorGridLines: { color: '#f0f0f0' },\n                    majorGridLines: { color: '#dfdfdf' },\n                    title: { color: '#232323' },\n                    crosshair: { color: '#8e8e8e' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#8e8e8e' }\n                        },\n                        label: { color: '#232323' },\n                        line: { color: '#8e8e8e' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#ea7001' },\n                scale: {\n                    rangePlaceholderColor: '#dedede',\n                    labels: { color: '#2e2e2e' },\n                    minorTicks: { color: '#2e2e2e' },\n                    majorTicks: { color: '#2e2e2e' },\n                    line: { color: '#2e2e2e' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#e15613' },\n                    connectorDefaults: {\n                        fill: { color: '#282828' },\n                        stroke: { color: WHITE },\n                        hover: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#282828' }\n                        }\n                    },\n                    content: { color: '#2e2e2e' }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#282828' },\n                            hover: {\n                                fill: { color: '#282828' },\n                                stroke: { color: '#282828' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: '#282828' },\n                            fill: { color: '#282828' }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#a7018f' } },\n                connectionDefaults: {\n                    stroke: { color: '#282828' },\n                    content: { color: '#2e2e2e' },\n                    selection: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#282828' }\n                        }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#ff6800',\n                        '#edcfba'\n                    ],\n                    [\n                        '#a0a700',\n                        '#dadcba'\n                    ],\n                    [\n                        '#ff8d00',\n                        '#edd7ba'\n                    ],\n                    [\n                        '#678900',\n                        '#cfd6ba'\n                    ],\n                    [\n                        '#ffb53c',\n                        '#eddfc6'\n                    ],\n                    [\n                        '#396000',\n                        '#c6ceba'\n                    ]\n                ]\n            }\n        });\n        registerTheme('silver', {\n            chart: {\n                title: { color: '#4e5968' },\n                legend: {\n                    labels: { color: '#4e5968' },\n                    inactiveItems: {\n                        labels: { color: '#B1BCC8' },\n                        markers: { color: '#B1BCC8' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: {\n                        color: '#293135',\n                        background: '#eaeaec',\n                        opacity: 0.5\n                    },\n                    errorBars: { color: '#4e5968' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#4e5968' }\n                        },\n                        label: { color: '#4e5968' },\n                        line: { color: '#4e5968' }\n                    },\n                    line: { markers: { background: '#eaeaec' } },\n                    scatter: { markers: { background: '#eaeaec' } },\n                    scatterLine: { markers: { background: '#eaeaec' } },\n                    pie: { connectors: { color: '#A6B1C0' } },\n                    donut: { connectors: { color: '#A6B1C0' } },\n                    waterfall: { line: { color: '#a6b1c0' } },\n                    horizontalWaterfall: { line: { color: '#a6b1c0' } },\n                    candlestick: { downColor: '#a6afbe' }\n                },\n                chartArea: { background: '#eaeaec' },\n                seriesColors: [\n                    '#007bc3',\n                    '#76b800',\n                    '#ffae00',\n                    '#ef4c00',\n                    '#a419b7',\n                    '#430B62'\n                ],\n                axisDefaults: {\n                    line: { color: '#a6b1c0' },\n                    labels: { color: '#4e5968' },\n                    majorGridLines: { color: '#dcdcdf' },\n                    minorGridLines: { color: '#eeeeef' },\n                    title: { color: '#4e5968' },\n                    crosshair: { color: '#a6b1c0' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#4e5968' }\n                        },\n                        label: { color: '#4e5968' },\n                        line: { color: '#4e5968' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#0879c0' },\n                scale: {\n                    rangePlaceholderColor: '#f3f3f4',\n                    labels: { color: '#515967' },\n                    minorTicks: { color: '#515967' },\n                    majorTicks: { color: '#515967' },\n                    line: { color: '#515967' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#1c82c2' },\n                    connectorDefaults: {\n                        fill: { color: '#515967' },\n                        stroke: { color: WHITE },\n                        hover: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#282828' }\n                        }\n                    },\n                    content: { color: '#515967' }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#515967' },\n                            hover: {\n                                fill: { color: '#515967' },\n                                stroke: { color: '#515967' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: '#515967' },\n                            fill: { color: '#515967' }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#515967' } },\n                connectionDefaults: {\n                    stroke: { color: '#515967' },\n                    content: { color: '#515967' },\n                    selection: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#515967' }\n                        }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#007bc3',\n                        '#c2dbea'\n                    ],\n                    [\n                        '#76b800',\n                        '#dae7c3'\n                    ],\n                    [\n                        '#ffae00',\n                        '#f5e5c3'\n                    ],\n                    [\n                        '#ef4c00',\n                        '#f2d2c3'\n                    ],\n                    [\n                        '#a419b7',\n                        '#e3c7e8'\n                    ],\n                    [\n                        '#430b62',\n                        '#d0c5d7'\n                    ]\n                ]\n            }\n        });\n        registerTheme('metro', {\n            chart: {\n                title: { color: '#777777' },\n                legend: {\n                    labels: { color: '#777777' },\n                    inactiveItems: {\n                        labels: { color: '#CBCBCB' },\n                        markers: { color: '#CBCBCB' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: { color: BLACK },\n                    errorBars: { color: '#777777' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#777777' }\n                        },\n                        label: { color: '#777777' },\n                        line: { color: '#777777' }\n                    },\n                    candlestick: {\n                        downColor: '#c7c7c7',\n                        line: { color: '#787878' }\n                    },\n                    waterfall: { line: { color: '#c7c7c7' } },\n                    horizontalWaterfall: { line: { color: '#c7c7c7' } },\n                    overlay: { gradient: 'none' },\n                    border: { _brightness: 1 }\n                },\n                seriesColors: [\n                    '#8ebc00',\n                    '#309b46',\n                    '#25a0da',\n                    '#ff6900',\n                    '#e61e26',\n                    '#d8e404',\n                    '#16aba9',\n                    '#7e51a1',\n                    '#313131',\n                    '#ed1691'\n                ],\n                axisDefaults: {\n                    line: { color: '#c7c7c7' },\n                    labels: { color: '#777777' },\n                    minorGridLines: { color: '#c7c7c7' },\n                    majorGridLines: { color: '#c7c7c7' },\n                    title: { color: '#777777' },\n                    crosshair: { color: '#c7c7c7' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#777777' }\n                        },\n                        label: { color: '#777777' },\n                        line: { color: '#777777' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#8ebc00' },\n                scale: {\n                    rangePlaceholderColor: '#e6e6e6',\n                    labels: { color: '#777' },\n                    minorTicks: { color: '#777' },\n                    majorTicks: { color: '#777' },\n                    line: { color: '#777' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#8ebc00' },\n                    connectorDefaults: {\n                        fill: { color: BLACK },\n                        stroke: { color: WHITE },\n                        hover: {\n                            fill: { color: WHITE },\n                            stroke: { color: BLACK }\n                        }\n                    },\n                    content: { color: '#777' }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#787878' },\n                            hover: {\n                                fill: { color: '#787878' },\n                                stroke: { color: '#787878' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: '#787878' },\n                            fill: { color: '#787878' }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#515967' } },\n                connectionDefaults: {\n                    stroke: { color: '#787878' },\n                    content: { color: '#777' },\n                    selection: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#787878' }\n                        }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#8ebc00',\n                        '#e8f2cc'\n                    ],\n                    [\n                        '#309b46',\n                        '#d6ebda'\n                    ],\n                    [\n                        '#25a0da',\n                        '#d3ecf8'\n                    ],\n                    [\n                        '#ff6900',\n                        '#ffe1cc'\n                    ],\n                    [\n                        '#e61e26',\n                        '#fad2d4'\n                    ],\n                    [\n                        '#d8e404',\n                        '#f7facd'\n                    ],\n                    [\n                        '#16aba9',\n                        '#d0eeee'\n                    ],\n                    [\n                        '#7e51a1',\n                        '#e5dcec'\n                    ],\n                    [\n                        '#313131',\n                        '#d6d6d6'\n                    ],\n                    [\n                        '#ed1691',\n                        '#fbd0e9'\n                    ]\n                ]\n            }\n        });\n        registerTheme('metroblack', {\n            chart: {\n                title: { color: '#ffffff' },\n                legend: {\n                    labels: { color: '#ffffff' },\n                    inactiveItems: {\n                        labels: { color: '#797979' },\n                        markers: { color: '#797979' }\n                    }\n                },\n                seriesDefaults: {\n                    border: { _brightness: 1 },\n                    labels: { color: '#ffffff' },\n                    errorBars: { color: '#ffffff' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#cecece' }\n                        },\n                        label: { color: '#ffffff' },\n                        line: { color: '#cecece' }\n                    },\n                    line: { markers: { background: '#0e0e0e' } },\n                    bubble: { opacity: 0.6 },\n                    scatter: { markers: { background: '#0e0e0e' } },\n                    scatterLine: { markers: { background: '#0e0e0e' } },\n                    candlestick: {\n                        downColor: '#828282',\n                        line: { color: '#ffffff' }\n                    },\n                    waterfall: { line: { color: '#cecece' } },\n                    horizontalWaterfall: { line: { color: '#cecece' } },\n                    overlay: { gradient: 'none' }\n                },\n                chartArea: { background: '#0e0e0e' },\n                seriesColors: [\n                    '#00aba9',\n                    '#309b46',\n                    '#8ebc00',\n                    '#ff6900',\n                    '#e61e26',\n                    '#d8e404',\n                    '#25a0da',\n                    '#7e51a1',\n                    '#313131',\n                    '#ed1691'\n                ],\n                axisDefaults: {\n                    line: { color: '#cecece' },\n                    labels: { color: '#ffffff' },\n                    minorGridLines: { color: '#2d2d2d' },\n                    majorGridLines: { color: '#333333' },\n                    title: { color: '#ffffff' },\n                    crosshair: { color: '#cecece' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#cecece' }\n                        },\n                        label: { color: '#ffffff' },\n                        line: { color: '#cecece' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#00aba9' },\n                scale: {\n                    rangePlaceholderColor: '#2d2d2d',\n                    labels: { color: '#ffffff' },\n                    minorTicks: { color: '#333333' },\n                    majorTicks: { color: '#cecece' },\n                    line: { color: '#cecece' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#00aba9' },\n                    connectorDefaults: {\n                        fill: { color: WHITE },\n                        stroke: { color: '#0e0e0e' },\n                        hover: {\n                            fill: { color: '#0e0e0e' },\n                            stroke: { color: WHITE }\n                        }\n                    },\n                    content: { color: WHITE }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: '#0e0e0e' },\n                            stroke: { color: '#787878' },\n                            hover: {\n                                fill: { color: '#787878' },\n                                stroke: { color: '#787878' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: WHITE },\n                            fill: { color: WHITE }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#787878' } },\n                connectionDefaults: {\n                    stroke: { color: WHITE },\n                    content: { color: WHITE },\n                    selection: {\n                        handles: {\n                            fill: { color: '#0e0e0e' },\n                            stroke: { color: WHITE }\n                        }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#00aba9',\n                        '#0b2d2d'\n                    ],\n                    [\n                        '#309b46',\n                        '#152a19'\n                    ],\n                    [\n                        '#8ebc00',\n                        '#28310b'\n                    ],\n                    [\n                        '#ff6900',\n                        '#3e200b'\n                    ],\n                    [\n                        '#e61e26',\n                        '#391113'\n                    ],\n                    [\n                        '#d8e404',\n                        '#36390c'\n                    ],\n                    [\n                        '#25a0da',\n                        '#132b37'\n                    ],\n                    [\n                        '#7e51a1',\n                        '#241b2b'\n                    ],\n                    [\n                        '#313131',\n                        '#151515'\n                    ],\n                    [\n                        '#ed1691',\n                        '#3b1028'\n                    ]\n                ]\n            }\n        });\n        registerTheme('moonlight', {\n            chart: {\n                title: { color: '#ffffff' },\n                legend: {\n                    labels: { color: '#ffffff' },\n                    inactiveItems: {\n                        labels: { color: '#A1A7AB' },\n                        markers: { color: '#A1A7AB' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: { color: '#ffffff' },\n                    errorBars: { color: '#ffffff' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#8c909e' }\n                        },\n                        label: { color: '#ffffff' },\n                        line: { color: '#8c909e' }\n                    },\n                    pie: { overlay: { gradient: 'sharpBevel' } },\n                    donut: { overlay: { gradient: 'sharpGlass' } },\n                    line: { markers: { background: '#212a33' } },\n                    bubble: { opacity: 0.6 },\n                    scatter: { markers: { background: '#212a33' } },\n                    scatterLine: { markers: { background: '#212a33' } },\n                    area: { opacity: 0.3 },\n                    candlestick: {\n                        downColor: '#757d87',\n                        line: { color: '#ea9d06' },\n                        border: {\n                            _brightness: 1.5,\n                            opacity: 1\n                        },\n                        highlight: {\n                            border: {\n                                color: WHITE,\n                                opacity: 0.2\n                            }\n                        }\n                    },\n                    waterfall: { line: { color: '#8c909e' } },\n                    horizontalWaterfall: { line: { color: '#8c909e' } },\n                    ohlc: { line: { color: '#ea9d06' } }\n                },\n                chartArea: { background: '#212a33' },\n                seriesColors: [\n                    '#ffca08',\n                    '#ff710f',\n                    '#ed2e24',\n                    '#ff9f03',\n                    '#e13c02',\n                    '#a00201'\n                ],\n                axisDefaults: {\n                    line: { color: '#8c909e' },\n                    minorTicks: { color: '#8c909e' },\n                    majorTicks: { color: '#8c909e' },\n                    labels: { color: '#ffffff' },\n                    majorGridLines: { color: '#3e424d' },\n                    minorGridLines: { color: '#2f3640' },\n                    title: { color: '#ffffff' },\n                    crosshair: { color: '#8c909e' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#8c909e' }\n                        },\n                        label: { color: '#ffffff' },\n                        line: { color: '#8c909e' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#f4af03' },\n                scale: {\n                    rangePlaceholderColor: '#2f3640',\n                    labels: { color: WHITE },\n                    minorTicks: { color: '#8c909e' },\n                    majorTicks: { color: '#8c909e' },\n                    line: { color: '#8c909e' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#f3ae03' },\n                    connectorDefaults: {\n                        fill: { color: WHITE },\n                        stroke: { color: '#414550' },\n                        hover: {\n                            fill: { color: '#414550' },\n                            stroke: { color: WHITE }\n                        }\n                    },\n                    content: { color: WHITE }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: '#414550' },\n                            stroke: { color: WHITE },\n                            hover: {\n                                fill: { color: WHITE },\n                                stroke: { color: WHITE }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: WHITE },\n                            fill: { color: WHITE }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: WHITE } },\n                connectionDefaults: {\n                    stroke: { color: WHITE },\n                    content: { color: WHITE },\n                    selection: {\n                        handles: {\n                            fill: { color: '#414550' },\n                            stroke: { color: WHITE }\n                        }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#ffca08',\n                        '#4e4b2b'\n                    ],\n                    [\n                        '#ff710f',\n                        '#4e392d'\n                    ],\n                    [\n                        '#ed2e24',\n                        '#4b2c31'\n                    ],\n                    [\n                        '#ff9f03',\n                        '#4e422a'\n                    ],\n                    [\n                        '#e13c02',\n                        '#482e2a'\n                    ],\n                    [\n                        '#a00201',\n                        '#3b232a'\n                    ]\n                ]\n            }\n        });\n        registerTheme('uniform', {\n            chart: {\n                title: { color: '#686868' },\n                legend: {\n                    labels: { color: '#686868' },\n                    inactiveItems: {\n                        labels: { color: '#B6B6B6' },\n                        markers: { color: '#B6B6B6' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: { color: '#686868' },\n                    errorBars: { color: '#686868' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#9e9e9e' }\n                        },\n                        label: { color: '#686868' },\n                        line: { color: '#9e9e9e' }\n                    },\n                    pie: { overlay: { gradient: 'sharpBevel' } },\n                    donut: { overlay: { gradient: 'sharpGlass' } },\n                    line: { markers: { background: '#ffffff' } },\n                    bubble: { opacity: 0.6 },\n                    scatter: { markers: { background: '#ffffff' } },\n                    scatterLine: { markers: { background: '#ffffff' } },\n                    area: { opacity: 0.3 },\n                    candlestick: {\n                        downColor: '#cccccc',\n                        line: { color: '#cccccc' },\n                        border: {\n                            _brightness: 1.5,\n                            opacity: 1\n                        },\n                        highlight: {\n                            border: {\n                                color: '#cccccc',\n                                opacity: 0.2\n                            }\n                        }\n                    },\n                    waterfall: { line: { color: '#9e9e9e' } },\n                    horizontalWaterfall: { line: { color: '#9e9e9e' } },\n                    ohlc: { line: { color: '#cccccc' } }\n                },\n                chartArea: { background: '#ffffff' },\n                seriesColors: [\n                    '#527aa3',\n                    '#6f91b3',\n                    '#8ca7c2',\n                    '#a8bdd1',\n                    '#c5d3e0',\n                    '#e2e9f0'\n                ],\n                axisDefaults: {\n                    line: { color: '#9e9e9e' },\n                    minorTicks: { color: '#aaaaaa' },\n                    majorTicks: { color: '#888888' },\n                    labels: { color: '#686868' },\n                    majorGridLines: { color: '#dadada' },\n                    minorGridLines: { color: '#e7e7e7' },\n                    title: { color: '#686868' },\n                    crosshair: { color: '#9e9e9e' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#9e9e9e' }\n                        },\n                        label: { color: '#686868' },\n                        line: { color: '#9e9e9e' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#527aa3' },\n                scale: {\n                    rangePlaceholderColor: '#e7e7e7',\n                    labels: { color: '#686868' },\n                    minorTicks: { color: '#aaaaaa' },\n                    majorTicks: { color: '#888888' },\n                    line: { color: '#9e9e9e' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#d1d1d1' },\n                    connectorDefaults: {\n                        fill: { color: '#686868' },\n                        stroke: { color: WHITE },\n                        hover: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#686868' }\n                        }\n                    },\n                    content: { color: '#686868' }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#686868' },\n                            hover: {\n                                fill: { color: '#686868' },\n                                stroke: { color: '#686868' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: '#686868' },\n                            fill: { color: '#686868' }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#686868' } },\n                connectionDefaults: {\n                    stroke: { color: '#686868' },\n                    content: { color: '#686868' },\n                    selection: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#686868' }\n                        }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#527aa3',\n                        '#d0d8e1'\n                    ],\n                    [\n                        '#6f91b3',\n                        '#d6dde4'\n                    ],\n                    [\n                        '#8ca7c2',\n                        '#dce1e7'\n                    ],\n                    [\n                        '#a8bdd1',\n                        '#e2e6ea'\n                    ],\n                    [\n                        '#c5d3e0',\n                        '#e7eaed'\n                    ],\n                    [\n                        '#e2e9f0',\n                        '#edeff0'\n                    ]\n                ]\n            }\n        });\n        registerTheme('bootstrap', {\n            chart: {\n                title: { color: '#333333' },\n                legend: {\n                    labels: { color: '#333333' },\n                    inactiveItems: {\n                        labels: { color: '#999999' },\n                        markers: { color: '#9A9A9A' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: { color: '#333333' },\n                    overlay: { gradient: 'none' },\n                    errorBars: { color: '#343434' },\n                    notes: {\n                        icon: {\n                            background: '#000000',\n                            border: { color: '#000000' }\n                        },\n                        label: { color: '#333333' },\n                        line: { color: '#000000' }\n                    },\n                    pie: { overlay: { gradient: 'none' } },\n                    donut: { overlay: { gradient: 'none' } },\n                    line: { markers: { background: '#ffffff' } },\n                    bubble: { opacity: 0.6 },\n                    scatter: { markers: { background: '#ffffff' } },\n                    scatterLine: { markers: { background: '#ffffff' } },\n                    area: { opacity: 0.8 },\n                    candlestick: {\n                        downColor: '#d0d0d0',\n                        line: { color: '#333333' },\n                        border: {\n                            _brightness: 1.5,\n                            opacity: 1\n                        },\n                        highlight: {\n                            border: {\n                                color: '#b8b8b8',\n                                opacity: 0.2\n                            }\n                        }\n                    },\n                    waterfall: { line: { color: '#cccccc' } },\n                    horizontalWaterfall: { line: { color: '#cccccc' } },\n                    ohlc: { line: { color: '#333333' } }\n                },\n                chartArea: { background: '#ffffff' },\n                seriesColors: [\n                    '#428bca',\n                    '#5bc0de',\n                    '#5cb85c',\n                    '#f2b661',\n                    '#e67d4a',\n                    '#da3b36'\n                ],\n                axisDefaults: {\n                    line: { color: '#cccccc' },\n                    minorTicks: { color: '#ebebeb' },\n                    majorTicks: { color: '#cccccc' },\n                    labels: { color: '#333333' },\n                    majorGridLines: { color: '#cccccc' },\n                    minorGridLines: { color: '#ebebeb' },\n                    title: { color: '#333333' },\n                    crosshair: { color: '#000000' },\n                    notes: {\n                        icon: {\n                            background: '#000000',\n                            border: { color: '#000000' }\n                        },\n                        label: { color: '#ffffff' },\n                        line: { color: '#000000' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#428bca' },\n                scale: {\n                    rangePlaceholderColor: '#cccccc',\n                    labels: { color: '#333333' },\n                    minorTicks: { color: '#ebebeb' },\n                    majorTicks: { color: '#cccccc' },\n                    line: { color: '#cccccc' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#428bca' },\n                    connectorDefaults: {\n                        fill: { color: '#333333' },\n                        stroke: { color: WHITE },\n                        hover: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#333333' }\n                        }\n                    },\n                    content: { color: '#333333' }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#333333' },\n                            hover: {\n                                fill: { color: '#333333' },\n                                stroke: { color: '#333333' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: '#333333' },\n                            fill: { color: '#333333' }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#333333' } },\n                connectionDefaults: {\n                    stroke: { color: '#c4c4c4' },\n                    content: { color: '#333333' },\n                    selection: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#333333' }\n                        },\n                        stroke: { color: '#333333' }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#428bca',\n                        '#d1e0ec'\n                    ],\n                    [\n                        '#5bc0de',\n                        '#d6eaf0'\n                    ],\n                    [\n                        '#5cb85c',\n                        '#d6e9d6'\n                    ],\n                    [\n                        '#5cb85c',\n                        '#f4e8d7'\n                    ],\n                    [\n                        '#e67d4a',\n                        '#f2ddd3'\n                    ],\n                    [\n                        '#da3b36',\n                        '#f0d0cf'\n                    ]\n                ]\n            }\n        });\n        registerTheme('flat', {\n            chart: {\n                title: { color: '#4c5356' },\n                legend: {\n                    labels: { color: '#4c5356' },\n                    inactiveItems: {\n                        labels: { color: '#CBCBCB' },\n                        markers: { color: '#CBCBCB' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: { color: '#4c5356' },\n                    errorBars: { color: '#4c5356' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#cdcdcd' }\n                        },\n                        label: { color: '#4c5356' },\n                        line: { color: '#cdcdcd' }\n                    },\n                    candlestick: {\n                        downColor: '#c7c7c7',\n                        line: { color: '#787878' }\n                    },\n                    area: { opacity: 0.9 },\n                    waterfall: { line: { color: '#cdcdcd' } },\n                    horizontalWaterfall: { line: { color: '#cdcdcd' } },\n                    overlay: { gradient: 'none' },\n                    border: { _brightness: 1 }\n                },\n                seriesColors: [\n                    '#10c4b2',\n                    '#ff7663',\n                    '#ffb74f',\n                    '#a2df53',\n                    '#1c9ec4',\n                    '#ff63a5',\n                    '#1cc47b'\n                ],\n                axisDefaults: {\n                    line: { color: '#cdcdcd' },\n                    labels: { color: '#4c5356' },\n                    minorGridLines: { color: '#cdcdcd' },\n                    majorGridLines: { color: '#cdcdcd' },\n                    title: { color: '#4c5356' },\n                    crosshair: { color: '#cdcdcd' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#cdcdcd' }\n                        },\n                        label: { color: '#4c5356' },\n                        line: { color: '#cdcdcd' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#10c4b2' },\n                scale: {\n                    rangePlaceholderColor: '#cdcdcd',\n                    labels: { color: '#4c5356' },\n                    minorTicks: { color: '#4c5356' },\n                    majorTicks: { color: '#4c5356' },\n                    line: { color: '#4c5356' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#10c4b2' },\n                    connectorDefaults: {\n                        fill: { color: '#363940' },\n                        stroke: { color: WHITE },\n                        hover: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#363940' }\n                        }\n                    },\n                    content: { color: '#4c5356' }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#363940' },\n                            hover: {\n                                fill: { color: '#363940' },\n                                stroke: { color: '#363940' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: '#363940' },\n                            fill: { color: '#363940' }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#363940' } },\n                connectionDefaults: {\n                    stroke: { color: '#cdcdcd' },\n                    content: { color: '#4c5356' },\n                    selection: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#363940' }\n                        },\n                        stroke: { color: '#363940' }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#10c4b2',\n                        '#cff3f0'\n                    ],\n                    [\n                        '#ff7663',\n                        '#ffe4e0'\n                    ],\n                    [\n                        '#ffb74f',\n                        '#fff1dc'\n                    ],\n                    [\n                        '#a2df53',\n                        '#ecf9dd'\n                    ],\n                    [\n                        '#1c9ec4',\n                        '#d2ecf3'\n                    ],\n                    [\n                        '#ff63a5',\n                        '#ffe0ed'\n                    ],\n                    [\n                        '#1cc47b',\n                        '#d2f3e5'\n                    ]\n                ]\n            }\n        });\n        registerTheme('material', {\n            chart: {\n                title: { color: '#444444' },\n                legend: {\n                    labels: { color: '#444444' },\n                    inactiveItems: {\n                        labels: { color: '#CBCBCB' },\n                        markers: { color: '#CBCBCB' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: { color: '#444444' },\n                    errorBars: { color: '#444444' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#e5e5e5' }\n                        },\n                        label: { color: '#444444' },\n                        line: { color: '#e5e5e5' }\n                    },\n                    candlestick: {\n                        downColor: '#c7c7c7',\n                        line: { color: '#787878' }\n                    },\n                    area: { opacity: 0.9 },\n                    waterfall: { line: { color: '#e5e5e5' } },\n                    horizontalWaterfall: { line: { color: '#e5e5e5' } },\n                    overlay: { gradient: 'none' },\n                    border: { _brightness: 1 }\n                },\n                seriesColors: [\n                    '#3f51b5',\n                    '#03a9f4',\n                    '#4caf50',\n                    '#f9ce1d',\n                    '#ff9800',\n                    '#ff5722'\n                ],\n                axisDefaults: {\n                    line: { color: '#e5e5e5' },\n                    labels: { color: '#444444' },\n                    minorGridLines: { color: '#e5e5e5' },\n                    majorGridLines: { color: '#e5e5e5' },\n                    title: { color: '#444444' },\n                    crosshair: { color: '#7f7f7f' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#e5e5e5' }\n                        },\n                        label: { color: '#444444' },\n                        line: { color: '#e5e5e5' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#3f51b5' },\n                scale: {\n                    rangePlaceholderColor: '#e5e5e5',\n                    labels: { color: '#444444' },\n                    minorTicks: { color: '#444444' },\n                    majorTicks: { color: '#444444' },\n                    line: { color: '#444444' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#3f51b5' },\n                    connectorDefaults: {\n                        fill: { color: '#7f7f7f' },\n                        stroke: { color: WHITE },\n                        hover: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#7f7f7f' }\n                        }\n                    },\n                    content: { color: '#444444' }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#444444' },\n                            hover: {\n                                fill: { color: '#444444' },\n                                stroke: { color: '#444444' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: '#444444' },\n                            fill: { color: '#444444' }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#444444' } },\n                connectionDefaults: {\n                    stroke: { color: '#7f7f7f' },\n                    content: { color: '#444444' },\n                    selection: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#444444' }\n                        },\n                        stroke: { color: '#444444' }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#3f51b5',\n                        '#cff3f0'\n                    ],\n                    [\n                        '#03a9f4',\n                        '#e5f6fe'\n                    ],\n                    [\n                        '#4caf50',\n                        '#edf7ed'\n                    ],\n                    [\n                        '#f9ce1d',\n                        '#fefae8'\n                    ],\n                    [\n                        '#ff9800',\n                        '#fff4e5'\n                    ],\n                    [\n                        '#ff5722',\n                        '#ffeee8'\n                    ]\n                ]\n            }\n        });\n        registerTheme('materialblack', {\n            chart: {\n                title: { color: '#fff' },\n                legend: {\n                    labels: { color: '#fff' },\n                    inactiveItems: {\n                        labels: { color: '#CBCBCB' },\n                        markers: { color: '#CBCBCB' }\n                    }\n                },\n                seriesDefaults: {\n                    labels: { color: '#fff' },\n                    errorBars: { color: '#fff' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#e5e5e5' }\n                        },\n                        label: { color: '#fff' },\n                        line: { color: '#e5e5e5' }\n                    },\n                    candlestick: {\n                        downColor: '#c7c7c7',\n                        line: { color: '#787878' }\n                    },\n                    area: { opacity: 0.9 },\n                    waterfall: { line: { color: '#4d4d4d' } },\n                    horizontalWaterfall: { line: { color: '#4d4d4d' } },\n                    overlay: { gradient: 'none' },\n                    border: { _brightness: 1 }\n                },\n                chartArea: { background: '#1c1c1c' },\n                seriesColors: [\n                    '#3f51b5',\n                    '#03a9f4',\n                    '#4caf50',\n                    '#f9ce1d',\n                    '#ff9800',\n                    '#ff5722'\n                ],\n                axisDefaults: {\n                    line: { color: '#4d4d4d' },\n                    labels: { color: '#fff' },\n                    minorGridLines: { color: '#4d4d4d' },\n                    majorGridLines: { color: '#4d4d4d' },\n                    title: { color: '#fff' },\n                    crosshair: { color: '#7f7f7f' },\n                    notes: {\n                        icon: {\n                            background: 'transparent',\n                            border: { color: '#4d4d4d' }\n                        },\n                        label: { color: '#fff' },\n                        line: { color: '#4d4d4d' }\n                    }\n                }\n            },\n            gauge: {\n                pointer: { color: '#3f51b5' },\n                scale: {\n                    rangePlaceholderColor: '#4d4d4d',\n                    labels: { color: '#fff' },\n                    minorTicks: { color: '#fff' },\n                    majorTicks: { color: '#fff' },\n                    line: { color: '#fff' }\n                }\n            },\n            diagram: {\n                shapeDefaults: {\n                    fill: { color: '#3f51b5' },\n                    connectorDefaults: {\n                        fill: { color: '#7f7f7f' },\n                        stroke: { color: WHITE },\n                        hover: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#7f7f7f' }\n                        }\n                    },\n                    content: { color: '#fff' }\n                },\n                editable: {\n                    resize: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#fff' },\n                            hover: {\n                                fill: { color: '#fff' },\n                                stroke: { color: '#fff' }\n                            }\n                        }\n                    },\n                    rotate: {\n                        thumb: {\n                            stroke: { color: '#fff' },\n                            fill: { color: '#fff' }\n                        }\n                    }\n                },\n                selectable: { stroke: { color: '#fff' } },\n                connectionDefaults: {\n                    stroke: { color: '#7f7f7f' },\n                    content: { color: '#fff' },\n                    selection: {\n                        handles: {\n                            fill: { color: WHITE },\n                            stroke: { color: '#fff' }\n                        },\n                        stroke: { color: '#fff' }\n                    }\n                }\n            },\n            treeMap: {\n                colors: [\n                    [\n                        '#3f51b5',\n                        '#cff3f0'\n                    ],\n                    [\n                        '#03a9f4',\n                        '#e5f6fe'\n                    ],\n                    [\n                        '#4caf50',\n                        '#edf7ed'\n                    ],\n                    [\n                        '#f9ce1d',\n                        '#fefae8'\n                    ],\n                    [\n                        '#ff9800',\n                        '#fff4e5'\n                    ],\n                    [\n                        '#ff5722',\n                        '#ffeee8'\n                    ]\n                ]\n            }\n        });\n        (function () {\n            var TEXT = '#333333';\n            var INACTIVE = '#7f7f7f';\n            var INACTIVE_SHAPE = '#bdbdbd';\n            var AXIS = '#c8c8c8';\n            var AXIS_MINOR = '#dddddd';\n            var SERIES = [\n                '#008fd3',\n                '#99d101',\n                '#f39b02',\n                '#f05662',\n                '#c03c53',\n                '#acacac'\n            ];\n            var SERIES_LIGHT = [\n                '#cbe8f5',\n                '#eaf5cb',\n                '#fceacc',\n                '#fbdcdf',\n                '#f2d7dc',\n                '#eeeeee'\n            ];\n            var PRIMARY = SERIES[0];\n            var DIAGRAM_HOVER = WHITE;\n            function noteStyle() {\n                return {\n                    icon: {\n                        background: '#007cc0',\n                        border: { color: '#007cc0' }\n                    },\n                    label: { color: '#ffffff' },\n                    line: { color: AXIS }\n                };\n            }\n            registerTheme('fiori', {\n                chart: {\n                    title: { color: TEXT },\n                    legend: {\n                        labels: { color: TEXT },\n                        inactiveItems: {\n                            labels: { color: INACTIVE },\n                            markers: { color: INACTIVE }\n                        }\n                    },\n                    seriesDefaults: {\n                        labels: { color: TEXT },\n                        errorBars: { color: TEXT },\n                        notes: noteStyle(),\n                        candlestick: {\n                            downColor: AXIS,\n                            line: { color: INACTIVE_SHAPE }\n                        },\n                        area: { opacity: 0.8 },\n                        waterfall: { line: { color: AXIS } },\n                        horizontalWaterfall: { line: { color: AXIS } },\n                        overlay: { gradient: 'none' },\n                        border: { _brightness: 1 }\n                    },\n                    seriesColors: SERIES,\n                    axisDefaults: {\n                        line: { color: AXIS },\n                        labels: { color: TEXT },\n                        minorGridLines: { color: AXIS_MINOR },\n                        majorGridLines: { color: AXIS },\n                        title: { color: TEXT },\n                        crosshair: { color: INACTIVE },\n                        notes: noteStyle()\n                    }\n                },\n                gauge: {\n                    pointer: { color: PRIMARY },\n                    scale: {\n                        rangePlaceholderColor: AXIS,\n                        labels: { color: TEXT },\n                        minorTicks: { color: TEXT },\n                        majorTicks: { color: TEXT },\n                        line: { color: TEXT }\n                    }\n                },\n                diagram: {\n                    shapeDefaults: {\n                        fill: { color: PRIMARY },\n                        connectorDefaults: {\n                            fill: { color: TEXT },\n                            stroke: { color: DIAGRAM_HOVER },\n                            hover: {\n                                fill: { color: DIAGRAM_HOVER },\n                                stroke: { color: TEXT }\n                            }\n                        },\n                        content: { color: TEXT }\n                    },\n                    editable: {\n                        resize: {\n                            handles: {\n                                fill: { color: DIAGRAM_HOVER },\n                                stroke: { color: INACTIVE_SHAPE },\n                                hover: {\n                                    fill: { color: INACTIVE_SHAPE },\n                                    stroke: { color: INACTIVE_SHAPE }\n                                }\n                            }\n                        },\n                        rotate: {\n                            thumb: {\n                                stroke: { color: INACTIVE_SHAPE },\n                                fill: { color: INACTIVE_SHAPE }\n                            }\n                        }\n                    },\n                    selectable: { stroke: { color: INACTIVE_SHAPE } },\n                    connectionDefaults: {\n                        stroke: { color: INACTIVE_SHAPE },\n                        content: { color: INACTIVE_SHAPE },\n                        selection: {\n                            handles: {\n                                fill: { color: DIAGRAM_HOVER },\n                                stroke: { color: INACTIVE_SHAPE }\n                            },\n                            stroke: { color: INACTIVE_SHAPE }\n                        }\n                    }\n                },\n                treeMap: { colors: fuse(SERIES, SERIES_LIGHT) }\n            });\n        }());\n        (function () {\n            var TEXT = '#4e4e4e';\n            var INACTIVE = '#7f7f7f';\n            var INACTIVE_SHAPE = '#bdbdbd';\n            var AXIS = '#c8c8c8';\n            var AXIS_MINOR = '#e5e5e5';\n            var SERIES = [\n                '#0072c6',\n                '#5db2ff',\n                '#008a17',\n                '#82ba00',\n                '#ff8f32',\n                '#ac193d'\n            ];\n            var SERIES_LIGHT = [\n                '#cbe2f3',\n                '#deeffe',\n                '#cbe7d0',\n                '#e5f0cb',\n                '#fee8d5',\n                '#eed0d7'\n            ];\n            var PRIMARY = SERIES[0];\n            var DIAGRAM_HOVER = WHITE;\n            function noteStyle() {\n                return {\n                    icon: {\n                        background: '#00b0ff',\n                        border: { color: '#00b0ff' }\n                    },\n                    label: { color: '#ffffff' },\n                    line: { color: AXIS }\n                };\n            }\n            registerTheme('office365', {\n                chart: {\n                    title: { color: TEXT },\n                    legend: {\n                        labels: { color: TEXT },\n                        inactiveItems: {\n                            labels: { color: INACTIVE },\n                            markers: { color: INACTIVE }\n                        }\n                    },\n                    seriesDefaults: {\n                        labels: { color: TEXT },\n                        errorBars: { color: TEXT },\n                        notes: noteStyle(),\n                        candlestick: {\n                            downColor: AXIS,\n                            line: { color: INACTIVE_SHAPE }\n                        },\n                        area: { opacity: 0.8 },\n                        waterfall: { line: { color: AXIS } },\n                        horizontalWaterfall: { line: { color: AXIS } },\n                        overlay: { gradient: 'none' },\n                        border: { _brightness: 1 }\n                    },\n                    seriesColors: SERIES,\n                    axisDefaults: {\n                        line: { color: AXIS },\n                        labels: { color: TEXT },\n                        minorGridLines: { color: AXIS_MINOR },\n                        majorGridLines: { color: AXIS },\n                        title: { color: TEXT },\n                        crosshair: { color: INACTIVE },\n                        notes: noteStyle()\n                    }\n                },\n                gauge: {\n                    pointer: { color: PRIMARY },\n                    scale: {\n                        rangePlaceholderColor: AXIS,\n                        labels: { color: TEXT },\n                        minorTicks: { color: TEXT },\n                        majorTicks: { color: TEXT },\n                        line: { color: TEXT }\n                    }\n                },\n                diagram: {\n                    shapeDefaults: {\n                        fill: { color: PRIMARY },\n                        connectorDefaults: {\n                            fill: { color: TEXT },\n                            stroke: { color: DIAGRAM_HOVER },\n                            hover: {\n                                fill: { color: DIAGRAM_HOVER },\n                                stroke: { color: TEXT }\n                            }\n                        },\n                        content: { color: TEXT }\n                    },\n                    editable: {\n                        resize: {\n                            handles: {\n                                fill: { color: DIAGRAM_HOVER },\n                                stroke: { color: INACTIVE_SHAPE },\n                                hover: {\n                                    fill: { color: INACTIVE_SHAPE },\n                                    stroke: { color: INACTIVE_SHAPE }\n                                }\n                            }\n                        },\n                        rotate: {\n                            thumb: {\n                                stroke: { color: INACTIVE_SHAPE },\n                                fill: { color: INACTIVE_SHAPE }\n                            }\n                        }\n                    },\n                    selectable: { stroke: { color: INACTIVE_SHAPE } },\n                    connectionDefaults: {\n                        stroke: { color: INACTIVE_SHAPE },\n                        content: { color: INACTIVE_SHAPE },\n                        selection: {\n                            handles: {\n                                fill: { color: DIAGRAM_HOVER },\n                                stroke: { color: INACTIVE_SHAPE }\n                            },\n                            stroke: { color: INACTIVE_SHAPE }\n                        }\n                    }\n                },\n                treeMap: { colors: fuse(SERIES, SERIES_LIGHT) }\n            });\n        }());\n        (function () {\n            var TEXT = '#32364c';\n            var INACTIVE = '#7f7f7f';\n            var INACTIVE_SHAPE = '#bdbdbd';\n            var AXIS = '#dfe0e1';\n            var AXIS_MINOR = '#dfe0e1';\n            var SERIES = [\n                '#ff4350',\n                '#ff9ea5',\n                '#00acc1',\n                '#80deea',\n                '#ffbf46',\n                '#ffd78c'\n            ];\n            var SERIES_LIGHT = [\n                '#ffd9dc',\n                '#ffeced',\n                '#cceef3',\n                '#e6f8fb',\n                '#fff2da',\n                '#fff7e8'\n            ];\n            var PRIMARY = SERIES[0];\n            var DIAGRAM_HOVER = WHITE;\n            function noteStyle() {\n                return {\n                    icon: {\n                        background: '#007cc0',\n                        border: { color: '#007cc0' }\n                    },\n                    label: { color: '#ffffff' },\n                    line: { color: AXIS }\n                };\n            }\n            registerTheme('nova', {\n                chart: {\n                    title: { color: TEXT },\n                    legend: {\n                        labels: { color: TEXT },\n                        inactiveItems: {\n                            labels: { color: INACTIVE },\n                            markers: { color: INACTIVE }\n                        }\n                    },\n                    seriesDefaults: {\n                        labels: { color: TEXT },\n                        errorBars: { color: TEXT },\n                        notes: noteStyle(),\n                        candlestick: {\n                            downColor: AXIS,\n                            line: { color: INACTIVE_SHAPE }\n                        },\n                        area: { opacity: 0.8 },\n                        waterfall: { line: { color: AXIS } },\n                        horizontalWaterfall: { line: { color: AXIS } },\n                        overlay: { gradient: 'none' },\n                        border: { _brightness: 1 }\n                    },\n                    seriesColors: SERIES,\n                    axisDefaults: {\n                        line: { color: AXIS },\n                        labels: { color: TEXT },\n                        minorGridLines: { color: AXIS_MINOR },\n                        majorGridLines: { color: AXIS },\n                        title: { color: TEXT },\n                        crosshair: { color: TEXT },\n                        notes: noteStyle()\n                    }\n                },\n                gauge: {\n                    pointer: { color: PRIMARY },\n                    scale: {\n                        rangePlaceholderColor: AXIS,\n                        labels: { color: TEXT },\n                        minorTicks: { color: TEXT },\n                        majorTicks: { color: TEXT },\n                        line: { color: TEXT }\n                    }\n                },\n                diagram: {\n                    shapeDefaults: {\n                        fill: { color: PRIMARY },\n                        connectorDefaults: {\n                            fill: { color: TEXT },\n                            stroke: { color: DIAGRAM_HOVER },\n                            hover: {\n                                fill: { color: DIAGRAM_HOVER },\n                                stroke: { color: TEXT }\n                            }\n                        },\n                        content: { color: TEXT }\n                    },\n                    editable: {\n                        resize: {\n                            handles: {\n                                fill: { color: DIAGRAM_HOVER },\n                                stroke: { color: INACTIVE_SHAPE },\n                                hover: {\n                                    fill: { color: INACTIVE_SHAPE },\n                                    stroke: { color: INACTIVE_SHAPE }\n                                }\n                            }\n                        },\n                        rotate: {\n                            thumb: {\n                                stroke: { color: INACTIVE_SHAPE },\n                                fill: { color: INACTIVE_SHAPE }\n                            }\n                        }\n                    },\n                    selectable: { stroke: { color: INACTIVE_SHAPE } },\n                    connectionDefaults: {\n                        stroke: { color: INACTIVE_SHAPE },\n                        content: { color: INACTIVE_SHAPE },\n                        selection: {\n                            handles: {\n                                fill: { color: DIAGRAM_HOVER },\n                                stroke: { color: INACTIVE_SHAPE }\n                            },\n                            stroke: { color: INACTIVE_SHAPE }\n                        }\n                    }\n                },\n                treeMap: { colors: fuse(SERIES, SERIES_LIGHT) }\n            });\n        }());\n        (function () {\n            var SERIES = [\n                '#ff6358',\n                '#ffd246',\n                '#78d237',\n                '#28b4c8',\n                '#2d73f5',\n                '#aa46be'\n            ];\n            var SERIES_LIGHT = [\n                '#ffd9dc',\n                '#ffeced',\n                '#cceef3',\n                '#e6f8fb',\n                '#fff2da',\n                '#fff7e8'\n            ];\n            registerTheme('default-v2', {\n                chart: {},\n                gauge: {},\n                diagram: {},\n                treeMap: { colors: fuse(SERIES, SERIES_LIGHT) }\n            });\n            themes.sass = themes['default-v2'];\n        }());\n        (function () {\n            var TEXT = '#292b2c';\n            var AXIS = 'rgba(0, 0, 0, .04)';\n            var SERIES = [\n                '#0275d8',\n                '#5bc0de',\n                '#5cb85c',\n                '#f0ad4e',\n                '#e67d4a',\n                '#d9534f'\n            ];\n            var SERIES_LIGHT = [\n                '#ffd9dc',\n                '#ffeced',\n                '#cceef3',\n                '#e6f8fb',\n                '#fff2da',\n                '#fff7e8'\n            ];\n            var PRIMARY = SERIES[0];\n            registerTheme('bootstrap-v4', {\n                chart: {},\n                gauge: {\n                    pointer: { color: PRIMARY },\n                    scale: {\n                        rangePlaceholderColor: AXIS,\n                        labels: { color: TEXT },\n                        minorTicks: { color: TEXT },\n                        majorTicks: { color: TEXT },\n                        line: { color: TEXT }\n                    }\n                },\n                diagram: {},\n                treeMap: { colors: fuse(SERIES, SERIES_LIGHT) }\n            });\n        }());\n        function fuse(arr1, arr2) {\n            return $.map(arr1, function (item, index) {\n                return [[\n                        item,\n                        arr2[index]\n                    ]];\n            });\n        }\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));\n(function (f, define) {\n    define('kendo.dataviz.themes', [\n        'kendo.dataviz.core',\n        'dataviz/themes/chart-base-theme',\n        'dataviz/themes/auto-theme',\n        'dataviz/themes/themes'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dataviz.themes',\n        name: 'Themes',\n        description: 'Built-in themes for the DataViz widgets',\n        category: 'dataviz',\n        depends: ['dataviz.core'],\n        hidden: true\n    };\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}