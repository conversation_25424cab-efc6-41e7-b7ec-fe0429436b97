{"version": 3, "sources": ["kendo.pivotgrid.js"], "names": ["f", "define", "$", "undefined", "normalizeMeasures", "measure", "descriptor", "name", "descriptors", "toString", "call", "map", "d", "type", "normalizeMembers", "member", "expand", "slice", "normalizeName", "indexOf", "accumulateMembers", "accumulator", "rootTuple", "tuple", "level", "idx", "length", "children", "members", "kendo", "stringify", "buildPath", "descriptorsForAxes", "tuples", "k", "result", "push", "parseJSON", "addMissingPathMembers", "axis", "tupleMembers", "found", "j", "firstTuple", "getName", "hierarchy", "tupleToDescriptors", "descriptorsForMembers", "measures", "tupletoSearch", "MEASURES", "findExistingTuple", "createAggregateGetter", "m", "measureGetter", "getter", "field", "aggregatorContext", "state", "aggregate", "dataItem", "isNumber", "val", "isNaN", "isDate", "getTime", "addEmptyDataItem", "value", "fmtValue", "ordinal", "validateAxis", "newAxis", "membersCount", "adjustDataByColumn", "sourceTuples", "targetTuples", "rows<PERSON><PERSON><PERSON>", "data", "columnIdx", "rowIdx", "dataIdx", "columnsLength", "targetColumnsLength", "measures<PERSON>ength", "tupleIndex", "adjustDataByRow", "collection", "index", "queue", "current", "shift", "apply", "normalizeAxis", "findDataIndex", "memberIndex", "counter", "Math", "max", "mergeTuples", "target", "source", "targetMembers", "sourceMembers", "parsedRoot", "equalTuples", "first", "second", "equal", "to<PERSON><PERSON>", "members<PERSON><PERSON><PERSON>", "addMembers", "i", "len", "path", "findParentMember", "parentMember", "parentPath", "parentName", "measurePosition", "normalizeTupleMeasures", "extend", "dataIndex", "splice", "parseSource", "measureIndex", "prepareDataOnRows", "indices", "rowIndex", "columnIndex", "targetIndex", "sourceIndex", "calcIndex", "buildDataIndices", "parseInt", "prepareDataOnColumns", "spliceIndex", "concat", "baseHierarchyPath", "memberName", "parts", "split", "expandMemberDescriptor", "names", "sort", "sortDescriptor", "sortDescriptorForMember", "dir", "crossJoin", "r", "pop", "crossJoinCommand", "tmp", "measureNames", "join", "getRootNames", "mapNames", "rootNames", "rootName", "<PERSON><PERSON><PERSON><PERSON>", "expandedIdx", "uniquePath", "parseDescriptors", "l", "hierarchyName", "expanded", "child", "root", "serializeMembers", "crossJoinCommands", "command", "serializeExpression", "expression", "operator", "format", "filterFunctionFormats", "serializeFilters", "filter", "cube", "filters", "serializeOptions", "parentTagName", "options", "capitalize", "key", "replace", "toUpperCase", "asArray", "object", "translateAxis", "memberIdx", "captionGetter", "unameGetter", "levelNameGetter", "levelNumGetter", "childrenGetter", "hierarchyGetter", "parentNameGetter", "Member", "caption", "levelName", "levelNum", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xmlaDiscoverCommands", "convertersMap", "XmlaTransport", "schemaDataReaderMap", "xmlaReaderMethods", "XmlaDataReader", "sortExpr", "removeExpr", "PivotGrid", "element", "htmlNode", "createMetadata", "tupleName", "ColumnBuilder", "RowBuilder", "ContentBuilder", "PivotExcelMixin", "window", "ui", "Class", "Widget", "DataSource", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "identity", "o", "isFunction", "CHANGE", "ERROR", "PROGRESS", "STATERESET", "AUTO", "DIV", "NS", "ROW_TOTAL_KEY", "DATABINDING", "DATABOUND", "EXPANDMEMBER", "COLLAPSEMEMBER", "STATE_EXPANDED", "STATE_COLLAPSED", "HEADER_TEMPLATE", "KPISTATUS_TEMPLATE", "KPITREND_TEMPLATE", "DATACELL_TEMPLATE", "LAYOUT_TABLE", "AXIS_ROWS", "AXIS_COLUMNS", "functions", "sum", "count", "average", "min", "PivotCubeBuilder", "init", "this", "dimensions", "_normalizeDescriptors", "keyField", "fields", "_rootTuples", "measureAggregators", "aggregatorsLength", "dimensionsSchema", "measureIdx", "rootNamesLength", "keys", "_expandedTuples", "mapItem", "currentKeys", "memberInfo", "expandedNames", "accumulator<PERSON><PERSON>s", "_findExpandedMember", "_asTuples", "rootInfo", "expandedInfo", "_measuresInfo", "rowAxis", "aggregateNames", "resultFuncs", "formats", "_toDataArray", "measuresInfo", "row<PERSON>eys", "columnKeys", "aggregates", "n", "row", "column", "column<PERSON>ey", "columnMeasureNames", "rowMeasureNamesLength", "rowMeasureNames", "<PERSON><PERSON><PERSON><PERSON>", "columnLength", "items", "_addData", "resultFunc", "_matchDescriptors", "getters", "parentField", "expectedValue", "_calculateAggregate", "totalItem", "aggregator", "_processColumns", "columns", "rowTotal", "updateColumn", "_measureAggregators", "defaultAggregate", "measureDescriptors", "aggregators", "toLowerCase", "Error", "_buildGetters", "_parseDescriptors", "parsedDescriptors", "_filter", "expr", "_normalizeFilter", "Query", "logic", "process", "measuresRowAxis", "columnDescriptors", "rowDescriptors", "aggregatedData", "rows", "rowValue", "columnGetters", "rowGetters", "processed", "expandedColumns", "expandedRows", "hasExpandedRows", "rowDescriptor", "rowName", "columnsInfo", "rowsInfo", "measuresAxis", "axes", "PivotTransport", "transport", "discover", "read", "update", "create", "destroy", "success", "catalog", "connection", "PivotDataSource", "schema", "cubes", "catalogs", "hierarchies", "levels", "_cubeSchema", "cubeBuilder", "fn", "_columns", "_rows", "values", "_measures", "_measuresAxis", "_skipNormalize", "_axes", "uniqueName", "defaultHierarchy", "proxy", "response", "restrictions", "levelUniqueName", "memberUniqueName", "schemaData", "dataGetter", "_rawData", "distinct", "treeOp", "childrenCardinality", "dimensionUniqueName", "hierarchyUniqueName", "serverSorting", "serverPaging", "serverFiltering", "serverGrouping", "serverAggregates", "_mergeState", "_clearAxesData", "query", "rowsAxisDescriptors", "columnsAxisDescriptors", "_expandPath", "origin", "other", "memberToExpand", "_lastExpanded", "_descriptorsForAxis", "_query", "_process", "e", "_view", "trigger", "that", "page", "pageSize", "group", "_data", "_params", "_updateLocalData", "_pristineData", "Deferred", "resolve", "promise", "expandColumn", "expandRow", "originalData", "reader", "_processResult", "processedData", "columnIndexes", "rowIndexes", "resultAxis", "axisToSkip", "hasColumnTuples", "_requestData", "_rowMeasures", "_normalizeTuples", "_columnMeasures", "_normalizeData", "_mergeAxes", "_readData", "newData", "_createTuple", "buildRoot", "_hasRoot", "isRoot", "sourceAxes", "startIndex", "mergedColumns", "mergedRows", "offset", "columnMeasures", "rowMeasures", "oldRowsLength", "newRowsLength", "oldColumnsLength", "newColumnsLength", "_mergeColumnData", "_mergeRowData", "toAdd", "toJSON", "drop", "_observe", "_ranges", "_addRange", "_total", "_pristineTotal", "last", "roots", "indexes", "_addMissingDataItems", "metadata", "_normalizeOrdinals", "lastOrdinal", "converter", "deferred", "parse", "_handleCustomErrors", "error", "status", "reject", "done", "schemaMeasures", "catalogName", "cubeName", "schemaKPIs", "kpis", "schemaDimensions", "schemaHierarchies", "dimensionName", "schemaLevels", "schemaCubes", "schemaCatalogs", "schemaMembers", "dataSource", "contains", "doesnotcontain", "startswith", "endswith", "eq", "neq", "properties", "Catalog", "RemoteTransport", "originalOptions", "url", "setup", "dataType", "contentType", "parameterMap", "ajax", "description", "groupName", "displayFolder", "defaultFormat", "goal", "trend", "statusGraphic", "trendGraphic", "defaultMember", "orderingProperty", "XmlDataReader", "_extend", "methodName", "option", "xml", "errors", "fault", "faultstring", "faultcode", "cells", "ordinalGetter", "valueGetter", "fmtValueGetter", "_mapSchema", "obj", "transports", "xmla", "readers", "expressions", "PivotSettingTarget", "addClass", "_refresh<PERSON><PERSON><PERSON>", "refresh", "template", "ns", "enabled", "emptyTemplate", "_sortable", "on", "closest", "attr", "hasClass", "remove", "sortable", "currentTarget", "find", "filterable", "fieldMenu", "PivotFieldMenu", "messages", "setting", "empty", "setDataSource", "unbind", "kendoSortable", "connectWith", "hint", "cursor", "start", "item", "focus", "blur", "change", "action", "add", "move", "newIndex", "_indexOf", "_isKPI", "validate", "isMeasure", "isArray", "sortExpressions", "_sort", "allowUnsort", "skipExpr", "html", "sortIcon", "_sortIcon", "off", "icon", "columnBuilder", "rowBuilder", "_dataSource", "_bindConfigurator", "_wrapper", "_createLayout", "_columnBuilder", "_rowBuilder", "_contentBuilder", "_templates", "columnsHeader", "<PERSON><PERSON><PERSON><PERSON>", "eventName", "request", "button", "builder", "eventArgs", "parent", "is", "childrenLoaded", "max<PERSON><PERSON><PERSON><PERSON>", "toggleClass", "_scrollable", "autoBind", "fetch", "notify", "events", "reorderable", "height", "columnWidth", "configurator", "columnHeaderTemplate", "rowHeaderTemplate", "dataCellTemplate", "kpiStatusTemplate", "kpiTrendTemplate", "measureFields", "columnFields", "rowFields", "columnTemplate", "rowTemplate", "dataTemplate", "useWithBlock", "kendoPivotConfigurator", "cellInfoByElement", "cellInfo", "contentBuilder", "columnInfo", "rowInfo", "columnTuple", "rowTuple", "view", "<PERSON><PERSON>arget", "<PERSON><PERSON><PERSON><PERSON>", "columnsTarget", "setOptions", "clearTimeout", "_headerReflowTimeout", "_stateR<PERSON>tHandler", "_progress<PERSON><PERSON><PERSON>", "_error<PERSON><PERSON><PERSON>", "_requestStart", "_stateReset", "_error", "bind", "_progress", "reset", "wrapper", "css", "_measureFields", "_createSettingTarget", "icons", "_initSettingTargets", "layoutTable", "leftContainer", "right<PERSON><PERSON><PERSON>", "gridWrapper", "wrap", "support", "scrollbar", "content", "append", "columnsHeaderTree", "dom", "Tree", "rowsHeaderTree", "contentTree", "toggle", "progress", "_resize", "<PERSON><PERSON><PERSON><PERSON>", "_setSectionsWidth", "_setSectionsHeight", "_setContentWidth", "_setContentHeight", "_columnHeaderReflow", "columnTable", "browser", "mozilla", "setTimeout", "leftColumn", "width", "measureFieldsHeight", "columnFields<PERSON>eight", "rowFieldsHeight", "innerHeight", "columnsHeight", "padding", "firstRowHeight", "secondRowHeight", "contentTable", "calculatedWidth", "min<PERSON><PERSON><PERSON>", "ceil", "_resetColspan", "skipScrollbar", "offsetHeight", "clientHeight", "cell", "_colspan", "_layoutTimeout", "_axisMeasures", "hasMeasure", "touchScroller", "columnAxis", "render", "build", "_indexes", "contentResized", "movable", "scrollLeft", "sender", "x", "scrollTop", "y", "scroll", "_wheelScroll", "delta", "ctrl<PERSON>ey", "wheelDeltaY", "preventDefault", "one", "maxMembers", "tbody", "_tbody", "colgroup", "_colGroup", "_rowLength", "_buildRows", "_normalize", "cells<PERSON>ength", "cellIdx", "rowSpan", "tupleAll", "_rowIndex", "colSpan", "_row", "parentRow", "<PERSON><PERSON><PERSON>", "not<PERSON><PERSON><PERSON>", "collapsed", "className", "measureRow", "_cell", "_content", "childRow", "<PERSON><PERSON><PERSON><PERSON>", "allCell", "cellAttr", "nextMember", "cellChildren", "memberCollapsed", "rootLevelNum", "maxcolSpan", "allRow", "firstMemberName", "expandIconAttr", "tuple<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "axisInfo", "total", "axisInfoMember", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firstEmpty", "_buildRow", "templateInfo", "cellContent", "startIdx", "plugin", "PivotExcelExporter", "widget", "columnHeaderTable", "rowHeaderTable", "column<PERSON><PERSON>er<PERSON>ength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autoWidth", "_cells", "callback", "background", "color", "dataCallback", "rowRows", "columnHeaderRows", "rowHeaderRows", "contentRows", "columnRows", "_freezePane", "colSplit", "rowSplit", "workbook", "then", "sheets", "freezePane", "proto", "excel", "saveAsExcel", "proxyURL", "fileName", "exporter", "book", "ooxml", "Workbook", "toDataURLAsync", "dataURI", "saveAs", "forceProxy", "prototype", "PDFMixin", "_drawPDF", "_drawPDFShadow", "avoidLinks", "pdf", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,mBACH,YACA,cACDD,IACL,WA8hIE,MA9+HC,UAAUE,EAAGC,GAMV,QAASC,GAAkBC,GAA3B,GACQC,GAAgC,gBAAZD,KAA0BE,KAAMF,IAAaA,EACjEG,EAA4C,mBAA9BC,GAASC,KAAKJ,GAAmCA,EAAaA,IAAeH,GAAaG,KAC5G,OAAOK,IAAIH,EAAa,SAAUI,GAC9B,MAAiB,gBAANA,IACEL,KAAMK,IAGfL,KAAMK,EAAEL,KACRM,KAAMD,EAAEC,QAIpB,QAASC,GAAiBC,GAA1B,GACQT,GAA+B,gBAAXS,KAChBR,MAAOQ,GACPC,QAAQ,IACPD,EACLP,EAA4C,mBAA9BC,GAASC,KAAKJ,GAAmCA,EAAaA,IAAeH,GAAaG,KAC5G,OAAOK,IAAIH,EAAa,SAAUI,GAC9B,MAAiB,gBAANA,IAEHL,MAAOK,GACPI,QAAQ,IAIZT,KAAgC,mBAA1BE,GAASC,KAAKE,EAAEL,MAA6BK,EAAEL,KAAKU,SAAWL,EAAEL,MACvES,OAAQJ,EAAEI,UAItB,QAASE,GAAcX,GAInB,MAHIA,GAAKY,QAAQ,YACbZ,EAAO,KAAOA,EAAO,MAElBA,EAEX,QAASa,GAAkBC,EAAaC,EAAWC,EAAOC,GAA1D,GACQC,GAAKC,EACLC,EACAZ,CAQJ,IAPKQ,IACDA,EAAQD,GAEPE,IACDA,EAAQ,GAEZT,EAASQ,EAAMK,QAAQJ,GAClBT,IAAUA,EAAOV,QAAtB,CAUA,GAPAsB,EAAWZ,EAAOY,SAClBD,EAASC,EAASD,OACdH,IAAUD,EACVD,EAAYQ,GAAMC,WAAWf,EAAOR,UAAYmB,EACzCA,IACPL,EAAYQ,GAAMC,UAAUC,GAAUR,EAAOC,MAAW,GAExDE,EACA,IAAKD,EAAM,EAAGA,EAAMC,EAAQD,IACxBL,EAAkBC,EAAaC,EAAWK,EAASF,GAAMD,EAGjEJ,GAAkBC,EAAaC,EAAWC,EAAOC,EAAQ,IAE7D,QAASQ,GAAmBC,GAA5B,GAKQzB,GACK0B,EALLC,IACAF,GAAOP,QACPN,EAAkBe,EAAQF,EAAO,IAEjCzB,IACJ,KAAS0B,IAAKC,GACV3B,EAAY4B,MACR7B,KAAML,EAAEmC,UAAUH,GAClBlB,OAAQmB,EAAOD,IAGvB,OAAO1B,GAEX,QAAS8B,GAAsBV,EAASW,GAAxC,GAIYC,GACKf,EAIDgB,EACKC,EATbT,EAASM,EAAKN,WACdU,EAAaV,EAAO,EACxB,IAAIU,GAAcf,EAAQF,OAASiB,EAAWf,QAAQF,OAElD,IADIc,EAAeG,EAAWf,QACrBH,EAAM,EAAGA,EAAMe,EAAad,OAAQD,IACzC,IAAIe,EAAaf,GAAKpB,QAAtB,CAIA,IADIoC,GAAQ,EACHC,EAAI,EAAGA,EAAId,EAAQF,OAAQgB,IAChC,GAAiE,IAA7DE,EAAQhB,EAAQc,IAAIvB,QAAQqB,EAAaf,GAAKoB,WAAkB,CAChEJ,GAAQ,CACR,OAGHA,GACDb,EAAQQ,MACJ7B,MAAOiC,EAAaf,GAAKlB,MACzBS,QAAQ,KAM5B,QAAS8B,GAAmBvB,GAA5B,GAGaE,GAFLU,KACAP,EAAUL,EAAMK,OACpB,KAASH,EAAM,EAAGA,EAAMG,EAAQF,OAAQD,IAChCG,EAAQH,GAAKpB,SAGjB8B,EAAOC,MACH7B,MAAOqB,EAAQH,GAAKlB,MACpBS,OAAQY,EAAQH,GAAKE,SAASD,OAAS,GAG/C,OAAOS,GAEX,QAASY,GAAsBR,EAAMX,EAASoB,GAA9C,GAUQC,GAEId,CAKR,OAhBAI,GAAOA,MACPD,EAAsBV,EAASW,GAC3BS,EAAStB,OAAS,GAClBE,EAAQQ,MACJ7B,KAAM2C,GACN7C,SAAS,EACTsB,SAAUb,EAAiBkC,KAG/BC,GAAkBrB,QAASA,GAC3BW,EAAKN,SACDE,EAASgB,EAAkBZ,EAAKN,OAAQgB,GACxCd,EAAOZ,QACPK,EAAUkB,EAAmBX,EAAOZ,SAGrCK,EAEX,QAASwB,GAAsBC,GAC3B,GAAIC,GAAgBzB,GAAM0B,OAAOF,EAAEG,OAAO,EAC1C,OAAO,UAAUC,EAAmBC,GAChC,MAAOL,GAAEM,UAAUL,EAAcG,EAAkBG,UAAWF,EAAOD,IAG7E,QAASI,GAASC,GACd,MAAsB,gBAARA,KAAqBC,MAAMD,GAE7C,QAASE,GAAOF,GACZ,MAAOA,IAAOA,EAAIG,QAs9CtB,QAASC,GAAiB/B,GAMtB,MALAA,GAAOA,EAAOT,SACVyC,MAAO,GACPC,SAAU,GACVC,QAASlC,EAAOT,QAEbS,EAEX,QAASmC,GAAaC,EAAShC,EAAMS,GACjC,GAAIuB,EAAQtC,OAAOP,OAAS8C,EAAajC,EAAKN,OAAQe,GAClD,MAAOT,GAIf,QAASkC,GAAmBC,EAAcC,EAAcC,EAAY5B,EAAU6B,GAA9E,GACQC,GAAWC,EAAQC,EACnBC,EAAgBP,EAAahD,OAC7BwD,EAAsBV,EAAaG,EAAc3B,GACjDmC,EAAiBnC,EAAStB,QAAU,CACxC,KAAKqD,EAAS,EAAGA,EAASH,EAAYG,IAClC,IAAKD,EAAY,EAAGA,EAAYG,EAAeH,IAC3CE,EAAUI,EAAWV,EAAaI,GAAYH,GAAgBQ,EAC9DH,GAAWF,EAAYK,EACvBN,EAAKE,EAASE,EAAgBH,GAAWT,QAAUU,EAASG,EAAsBF,EAI9F,QAASK,GAAgBX,EAAcC,EAAcM,EAAejC,EAAU6B,GAA9E,GACQC,GAAWC,EAAQC,EACnBJ,EAAaF,EAAahD,OAC1ByD,EAAiBnC,EAAStB,QAAU,CACxC,KAAKqD,EAAS,EAAGA,EAASH,EAAYG,IAIlC,IAHAC,EAAUI,EAAWV,EAAaK,GAASJ,GAC3CK,GAAWG,EACXH,GAAWD,EAASI,EACfL,EAAY,EAAGA,EAAYG,EAAeH,IAC3CD,EAAKE,EAASE,EAAgBH,GAAWT,QAAUW,EAAUC,EAAgBH,EAIzF,QAASM,GAAW7D,EAAO+D,GACvB,MAAOnC,GAAkBmC,EAAY/D,GAAOgE,MAEhD,QAASf,GAAavC,EAAQe,GAA9B,GAIQwC,GACAC,EACAtD,CALJ,KAAKF,EAAOP,OACR,MAAO,EAKX,KAHI8D,EAAQvD,EAAOhB,QACfwE,EAAUD,EAAME,QAChBvD,EAAS,EACNsD,GACCA,EAAQ7D,WACLQ,KAAKuD,MAAMH,EAAOC,EAAQ7D,SACtB6D,EAAQ9D,WACV8D,EAAQpF,UACT8B,GAAUsD,EAAQ9D,SAASD,WAE5BU,KAAKuD,MAAMH,EAAOC,EAAQ9D,WAEjC8D,EAAUD,EAAME,OAKpB,OAHI1C,GAAStB,SACTS,GAAkBa,EAAStB,QAExBS,EAEX,QAASyD,GAAcrD,GAOnB,MANKA,KACDA,GAASN,YAERM,EAAKN,SACNM,EAAKN,WAEFM,EAEX,QAASsD,GAActE,EAAOuE,EAAa9C,GAA3C,GAIQmC,GACAlD,EACAwD,EACAM,CANJ,KAAKxE,EACD,MAAO,EAMX,KAJI4D,EAAiBa,KAAKC,IAAIjD,EAAStB,OAAQ,GAC3CO,EAASV,EAAMK,QAAQX,MAAM,EAAG6E,GAChCL,EAAUxD,EAAOyD,QACjBK,EAAUZ,EACPM,GACCA,EAAQlF,OAAS2C,GACjB6C,GAAWZ,EAAiB,EACrBM,EAAQ9D,YACZS,KAAKuD,MAAM1D,EAAQwD,EAAQ9D,WAE9BoE,OACG3D,KAAKuD,MAAM1D,EAAQwD,EAAQ7D,UAElC6D,EAAUxD,EAAOyD,OAErB,OAAOK,GAEX,QAASG,GAAYC,EAAQC,EAAQpD,GAArC,GASQb,GASAkE,EACAC,EACAR,EASKrE,EAASC,CA5BlB,KAAK0E,EAAO,GACR,OACIG,WAAY,KACZtE,OAAQkE,EACRL,YAAa,EACbP,MAAO,EAIf,IADIpD,EAASgB,EAAkBgD,EAAQC,EAAO,KACzCjE,EAAOZ,MACR,OACIgF,WAAY,KACZtE,OAAQmE,EACRN,YAAa,EACbP,MAAO,EAMf,IAHIc,EAAgBlE,EAAOZ,MAAMK,QAC7B0E,EAAgBF,EAAO,GAAGxE,QAC1BkE,KACAO,EAAc3E,SAAW4E,EAAc5E,OACvC,OACI6E,WAAY,KACZtE,OAAQmE,EACRN,YAAa,EACbP,MAAO,EAGf,KAAS9D,EAAM,EAAGC,EAAS2E,EAAc3E,OAAQD,EAAMC,EAAQD,KACtD4E,EAAc5E,GAAKpB,SAAWiG,EAAc7E,GAAKE,SAAS,KACvDmE,OAAqBQ,EAAc7E,GAAKE,SAASD,SACjDoE,EAAcrE,GAElB4E,EAAc5E,GAAKE,SAAW2E,EAAc7E,GAAKE,SAIzD,OADAqB,GAAWgD,KAAKC,IAAIjD,EAAStB,OAAQ,IAEjC6E,WAAYpE,EAAOZ,MACnBgE,MAAOpD,EAAOoD,MAAQvC,EACtB8C,YAAaA,EACb7D,OAAQkE,GAGhB,QAASK,GAAYC,EAAOC,GAA5B,GAEQjF,GAAKC,EADLiF,GAAQ,CAIZ,KAFAF,EAAQA,EAAM7E,QACd8E,EAASA,EAAO9E,QACXH,EAAM,EAAGC,EAAS+E,EAAM/E,OAAQD,EAAMC,EAAQD,IAC3CgF,EAAMhF,GAAKpB,SAAWqG,EAAOjF,GAAKpB,UAGtCsG,EAAQA,GAAS/D,EAAQ6D,EAAMhF,MAAUmB,EAAQ8D,EAAOjF,IAE5D,OAAOkF,GAEX,QAASxD,GAAkBlB,EAAQ2E,GAAnC,GACQnF,GAAKC,EAAQH,EAAOkB,EACpBqD,EAAae,EAAe9F,EADDgF,EAAU,CAEzC,KAAKtE,EAAM,EAAGC,EAASO,EAAOP,OAAQD,EAAMC,EAAQD,IAAO,CAEvD,GADAF,EAAQU,EAAOR,GACX+E,EAAYjF,EAAOqF,GACnB,OACIrF,MAAOA,EACPgE,MAAOQ,EAIf,KADAA,IACKD,EAAc,EAAGe,EAAgBtF,EAAMK,QAAQF,OAAQoE,EAAce,EAAef,IAErF,GADA/E,EAASQ,EAAMK,QAAQkE,IACnB/E,EAAOV,UAGXoC,EAAQU,EAAkBpC,EAAOY,SAAUiF,GAC3Cb,GAAWtD,EAAM8C,MACb9C,EAAMlB,OACN,OACIA,MAAOkB,EAAMlB,MACbgE,MAAOQ,GAKvB,OAASR,MAAOQ,GAEpB,QAASe,GAAWlF,EAASjB,GACzB,GAAII,GAAQgG,EAAGC,EAAKC,EAAO,EAC3B,KAAKF,EAAI,EAAGC,EAAMpF,EAAQF,OAAQqF,EAAIC,EAAKD,IACvChG,EAASa,EAAQmF,GACjBE,GAAQlG,EAAOR,KACVI,EAAIsG,KACLtG,EAAIsG,GAAQlG,GAIxB,QAASmG,GAAiB3F,EAAOZ,GAAjC,GAEQoG,GAAGC,EAAKjG,EAERoG,EAHAvF,EAAUL,EAAMK,QACAqF,EAAO,GACvBG,EAAa,EAEjB,KAAKL,EAAI,EAAGC,EAAMpF,EAAQF,OAAQqF,EAAIC,EAAKD,IAAK,CAE5C,GADAhG,EAASa,EAAQmF,GACbI,EAAc,CACd,GAAIxG,EAAIsG,EAAOlG,EAAOR,MAAO,CACzB0G,GAAQlG,EAAOR,KACf4G,EAAexG,EAAIsG,EACnB,UACG,MAAItG,GAAIsG,EAAOlG,EAAOsG,YAClB1G,EAAIsG,EAAOlG,EAAOsG,YAClB1G,EAAIyG,EAAarG,EAAOsG,YACxB1G,EAAIyG,EAAarG,EAAOsG,YAExB1G,EAAIyG,GAKnB,GAFAH,GAAQlG,EAAOR,KACf4G,EAAexG,EAAII,EAAOsG,aACrBF,IACDA,EAAexG,EAAIsG,IACdE,GACD,MAAO,KAGXA,KACAC,GAAcD,EAAa5G,MAGnC,MAAO4G,GAEX,QAASG,GAAgB/F,EAAOyB,GAAhC,GAIQ3C,GACAuB,EACKH,EAASuF,CALlB,IAAwB,IAApBhE,EAAStB,OACT,QAIJ,KAFIrB,EAAU2C,EAAS,GACnBpB,EAAUL,EAAMK,QACXH,EAAM,EAAGuF,EAAMpF,EAAQF,OAAQD,EAAMuF,EAAKvF,IAC/C,GAAIG,EAAQH,GAAKlB,MAAQF,EAAQE,KAC7B,MAAOkB,GAInB,QAAS8F,GAAuBhG,EAAOgE,GACnC,KAAIA,EAAQ,GAAZ,CAGA,GAAIxE,IACAR,KAAM2C,GACN7C,SAAS,EACTsB,UAAWzB,EAAEsH,QACL5F,WACA6F,UAAWlG,EAAMkG,WAClBlG,EAAMK,QAAQ2D,KAEzBhE,GAAMK,QAAQ8F,OAAOnC,EAAO,EAAGxE,GAC/BQ,EAAMkG,UAAYtH,GAEtB,QAASwH,GAAY1F,EAAQe,GAA7B,GAIQb,GACAxB,EACAiH,EACKb,EACDxF,EAGA4F,CAVR,IAAIlF,EAAOP,OAAS,EAChB,QAKJ,KAHIS,KACAxB,KACAiH,EAAeN,EAAgBrF,EAAO,GAAIe,GACrC+D,EAAI,EAAGA,EAAI9E,EAAOP,OAAQqF,IAC3BxF,EAAQU,EAAO8E,GACnBxF,EAAMkG,UAAYV,EAClBQ,EAAuBhG,EAAOqG,GAC1BT,EAAeD,EAAiB3F,EAAOZ,GACvCwG,EAEIA,EAAaxF,SAASS,KADtBwF,EAAe,IAAMT,EAAa9G,QACPkB,EAEAA,EAAMK,QAAQgG,GAAcjG,SAAS,IAGpEQ,EAAOC,KAAKb,GAEhBuF,EAAWvF,EAAMK,QAASjB,EAE9B,OAAOwB,GAEX,QAAS0F,GAAkB5F,EAAQ4C,GAAnC,GAIQ1C,GACA2F,EACAlD,EACAK,EACA8C,EAAUC,EAAaC,EAAaC,EACpCC,CARJ,KAAKlG,IAAWA,EAAOP,OACnB,MAAOmD,EAQX,KANI1C,KACA2F,EAAUM,EAAiBnG,GAC3B2C,EAAakD,EAAQpG,OACrBuD,EAAgBe,KAAKC,IAAIpB,EAAKnD,OAASkD,EAAY,GAGlDmD,EAAW,EAAGA,EAAWnD,EAAYmD,IAGtC,IAFAE,EAAchD,EAAgB8C,EAC9BG,EAAcjD,EAAgB6C,EAAQC,GACjCC,EAAc,EAAGA,EAAc/C,EAAe+C,IAC/CG,EAAYE,SAASH,EAAcF,EAAa,IAChD7F,EAAOkG,SAASJ,EAAcD,EAAa,KAAOnD,EAAKsD,KACnDhE,MAAO,GACPC,SAAU,GACVC,QAAS8D,EAIrB,OAAOhG,GAEX,QAASmG,GAAqBrG,EAAQ4C,GAAtC,GAIQ1C,GACA2F,EACA7C,EACAL,EACAoD,EAAaD,EAAUN,EAAWU,CAPtC,KAAKlG,IAAWA,EAAOP,OACnB,MAAOmD,EAOX,KALI1C,KACA2F,EAAUM,EAAiBnG,GAC3BgD,EAAgB6C,EAAQpG,OACxBkD,EAAaoB,KAAKC,IAAIpB,EAAKnD,OAASuD,EAAe,GAElD8C,EAAW,EAAGA,EAAWnD,EAAYmD,IAEtC,IADAN,EAAYxC,EAAgB8C,EACvBC,EAAc,EAAGA,EAAc/C,EAAe+C,IAC/CG,EAAYL,EAAQE,GAAeP,EACnCtF,EAAOsF,EAAYO,GAAenD,EAAKsD,KACnChE,MAAO,GACPC,SAAU,GACVC,QAAS8D,EAIrB,OAAOhG,GAEX,QAASiG,GAAiBnG,GAA1B,GAEQE,GACAZ,EACAE,EAAKC,EAAQ6G,EAAa5G,EAAUZ,CACxC,KAJAkB,EAASA,EAAOhB,QACZkB,KACAZ,EAAQU,EAAOyD,QAEZnE,GAAO,CAKV,IAJIA,EAAMkG,YAActH,GACpBgC,EAAOC,KAAKb,EAAMkG,WAEtBc,EAAc,EACT9G,EAAM,EAAGC,EAASH,EAAMK,QAAQF,OAAQD,EAAMC,EAAQD,IACvDV,EAASQ,EAAMK,QAAQH,GACvBE,EAAWZ,EAAOY,SACdZ,EAAOV,WACJqH,OAAO/B,MAAM1D,GACZ,EACA,GACFuG,OAAO7G,OAEN+F,OAAO/B,MAAM1D,GACZsG,EACA,GACFC,OAAO7G,IAEb4G,GAAe5G,EAASD,MAE5BH,GAAQU,EAAOyD,QAEnB,MAAOvD,GAWX,QAASsG,GAAkBC,GACvB,GAAIC,GAAQD,EAAWE,MAAM,IAC7B,OAAID,GAAMjH,OAAS,EACRiH,EAAM,GAAK,IAAMA,EAAM,GAE3BD,EAEX,QAASG,GAAuBC,EAAOC,GAAvC,GACQtH,GAAMqH,EAAMpH,OAAS,EACrBnB,EAAOuI,EAAMrH,GACbuH,EACaC,EAAwBF,EAAMxI,EAO/C,OANIyI,IAAkBA,EAAeE,IACjC3I,EAAO,SAAWA,EAAO,aAAeyI,EAAexF,MAAQ,iCAAmCwF,EAAeE,IAAM,IAEvH3I,GAAQ,YAEZuI,EAAMrH,GAAOlB,EACNuI,EAEX,QAASG,GAAwBF,EAAMhI,GACnC,IAAK,GAAIU,GAAM,EAAGC,EAASqH,EAAKrH,OAAQD,EAAMC,EAAQD,IAClD,GAAwC,IAApCV,EAAOI,QAAQ4H,EAAKtH,GAAK+B,OACzB,MAAOuF,GAAKtH,EAGpB,OAAO,MAEX,QAAS0H,GAAUL,GAAnB,GAEQM,GADAjH,EAAS,aAYb,OAVI2G,GAAMpH,OAAS,GACf0H,EAAIN,EAAMO,MACVlH,GAAUgH,EAAUL,KAEpB3G,GAAU2G,EAAMpD,QAChB0D,EAAIN,EAAMO,OAEdlH,GAAU,MACVA,GAAUiH,EACVjH,GAAU,KAGd,QAASmH,GAAiB1H,EAASoB,GAC/B,GAAIuG,GAAM3H,EAAQX,MAAM,EAIxB,OAHI+B,GAAStB,OAAS,GAClB6H,EAAInH,KAAK,IAAMoH,EAAaxG,GAAUyG,KAAK,KAAO,KAE/CN,EAAUI,GAErB,QAASC,GAAaxG,GAKlB,IALJ,GAIQ3C,GAHAoB,EAAM,EACNC,EAASsB,EAAStB,OAClBS,KAEGV,EAAMC,EAAQD,IACjBpB,EAAU2C,EAASvB,GACnBU,EAAOC,KAAK/B,EAAQE,OAASJ,EAAYE,EAAQE,KAAOF,EAE5D,OAAO8B,GAEX,QAASS,GAAQrC,GAKb,MAJAA,GAAOA,EAAKA,MAAQA,EACQ,mBAAxBE,GAASC,KAAKH,KACdA,EAAOA,EAAKA,EAAKmB,OAAS,IAEvBnB,EAEX,QAASmJ,GAAa9H,GAIlB,IAJJ,GACQF,GAASE,EAAQF,OACjBoH,KACArH,EAAM,EACHA,EAAMC,EAAQD,IACjBqH,EAAM1G,KAAKR,EAAQH,GAAKlB,KAAK,GAEjC,OAAOuI,GAEX,QAASa,GAASb,EAAOc,GAAzB,GACQrJ,GACAsJ,EACAnH,EACAjB,EAAM,EACNC,EAASoH,EAAMpH,OACfoI,EAAaF,EAAUlI,MAE3B,KADAkI,EAAYA,EAAU3I,MAAM,GACrBQ,EAAMC,EAAQD,IAEjB,IADAlB,EAAOuI,EAAMrH,GACRiB,EAAI,EAAGA,EAAIoH,EAAYpH,IAExB,GADAmH,EAAWpB,EAAkBmB,EAAUlH,IACnCnC,EAAKY,QAAQ0I,QAAkB,CAC/BD,EAAUlH,GAAKnC,CACf,OAIZ,OACIuI,MAAOc,EACPG,YAAarH,EACbsH,WAAYJ,EAAU3I,MAAM,EAAGyB,EAAI,GAAG+G,KAAK,KAGnD,QAASQ,GAAiBrI,GAWtB,IAXJ,GAIQb,GACA2B,EAAGwH,EAGH3J,EACA4J,EACA1H,EATA2H,KACAC,KACAC,KAGA7I,EAAM,EACNC,EAASE,EAAQF,OAIdD,EAAMC,EAAQD,IAOjB,GANAV,EAASa,EAAQH,GACjBlB,EAAOQ,EAAOR,KACdkC,GAAQ,EACoB,mBAAxBhC,GAASC,KAAKH,KACdQ,EAAOR,KAAOA,GAAQA,IAEtBA,EAAKmB,OAAS,EACd2I,EAAMjI,KAAKrB,OACR,CAEH,IADAoJ,EAAgB1B,EAAkBlI,EAAK,IAClCmC,EAAI,EAAGwH,EAAII,EAAK5I,OAAQgB,EAAIwH,EAAGxH,IAChC,GAA+C,IAA3C4H,EAAK5H,GAAGnC,KAAK,GAAGY,QAAQgJ,GAAsB,CAC9C1H,GAAQ,CACR,OAGHA,GACD6H,EAAKlI,KAAKrB,GAEVA,EAAOC,QACPoJ,EAAShI,KAAKrB,GAK1B,MADAqJ,GAAWA,EAAS5B,OAAO6B,IAEvBC,KAAMA,EACNF,SAAUA,GAGlB,QAASG,GAAiB3I,EAASoB,EAAU+F,GAA7C,GAGQqB,GACAE,EACAV,EACAY,EAEA9I,EACAD,EACAiH,EACAI,EAVA2B,EAAU,EAWd,IAVA7I,EAAUA,MACNwI,EAAWH,EAAiBrI,GAC5B0I,EAAOF,EAASE,KAChBV,EAAYF,EAAaY,GACzBE,KACJJ,EAAWA,EAASA,SAChB1I,EAAS0I,EAAS1I,OAClBD,EAAM,EAENqH,KACAc,EAAUlI,OAAS,GAAKsB,EAAStB,OAAS,EAAG,CAE7C,IADA8I,EAAkBpI,KAAKkH,EAAiBM,EAAW5G,IAC5CvB,EAAMC,EAAQD,IACjBiH,EAAaG,EAAuBuB,EAAS3I,GAAKlB,KAAMwI,GACxDD,EAAQa,EAASjB,EAAYkB,GAAWd,MACxC0B,EAAkBpI,KAAKkH,EAAiBR,EAAO9F,GAEnDyH,IAAWD,EAAkBf,KAAK,SAC/B,CACH,KAAOhI,EAAMC,EAAQD,IACjBiH,EAAaG,EAAuBuB,EAAS3I,GAAKlB,KAAMwI,GACxDD,EAAM1G,KAAKsG,EAAW,GAE1B+B,IAAWb,EAAUpB,OAAOM,GAAOW,KAAK,KAE5C,MAAOgB,GAUX,QAASC,GAAoBC,GAA7B,GACQF,GAAU,GACVtG,EAAQwG,EAAWxG,MACnBX,EAAQmH,EAAWnH,MACnBoH,EAAWD,EAAWC,QAY1B,OAXgB,MAAZA,GACAH,GAAW,IACXA,GAAWtG,EACXsG,GAAW,MAEXA,GAAuB,OAAZG,GAAiC,kBAAZA,EAA+B,IAAM,GACrEH,GAAW,UACXA,GAAWjH,EAAQ,WACnBiH,GAAW5I,GAAMgJ,OAAOC,EAAsBF,GAAWpH,EAAOW,GAChEsG,GAAW,KAERA,EAEX,QAASM,GAAiBC,EAAQC,GAAlC,GACsBxF,GAGdhE,EAHAgJ,EAAU,GACVS,EAAUF,EAAOE,QACjBxJ,EAASwJ,EAAQxJ,MAErB,KAAKD,EAAMC,EAAS,EAAGD,GAAO,EAAGA,IAC7BgE,EAAU,WACVA,GAAWiF,EAAoBQ,EAAQzJ,IACvCgE,GAAW,SACPhE,GAAOC,EAAS,GAChB+D,GAAW,UAAYwF,EAAO,IAC9BR,EAAUhF,GAEVgF,EAAUhF,EAAU,WAAagF,EAAU,IAGnD,OAAOA,GAEX,QAASU,GAAiBC,EAAeC,EAASC,GAAlD,GAIYnH,GACKoH,EAJTpJ,EAAS,EACb,IAAIkJ,EAAS,CACTlJ,GAAU,IAAMiJ,EAAgB,GAEhC,KAASG,IAAOF,GACZlH,EAAQkH,EAAQE,GACZD,IACAC,EAAMA,EAAIC,QAAQ,yCAA0C,OAAOC,cAAcD,QAAQ,KAAM,KAEnGrJ,GAAU,IAAMoJ,EAAM,IAAMpH,EAAQ,KAAOoH,EAAM,GAErDpJ,IAAU,KAAOiJ,EAAgB,QAEjCjJ,IAAU,IAAMiJ,EAAgB,IAEpC,OAAOjJ,GA4GX,QAASuJ,GAAQC,GACb,GAAc,MAAVA,EACA,QAEJ,IAAI9K,GAAOJ,GAASC,KAAKiL,EACzB,OAAa,mBAAT9K,GACQ8K,GAELA,EAEX,QAASC,GAAcrJ,GAAvB,GAUad,GACDG,EACAb,EACK8K,EAZT1J,GAAWF,WACXA,EAASyJ,EAAQ7J,GAAM0B,OAAO,gBAAgB,GAAMhB,IACpDuJ,EAAgBjK,GAAM0B,OAAO,oBAC7BwI,EAAclK,GAAM0B,OAAO,kBAC3ByI,EAAkBnK,GAAM0B,OAAO,kBAC/B0I,EAAiBpK,GAAM0B,OAAO,iBAC9B2I,EAAiBrK,GAAM0B,OAAO,iCAAmC,GACjE4I,EAAkBtK,GAAM0B,OAAO,kBAC/B6I,EAAmBvK,GAAM0B,OAAO,+BAAiC,EACrE,KAAS9B,EAAM,EAAGA,EAAMQ,EAAOP,OAAQD,IAAO,CAG1C,IAFIG,KACAb,EAAS2K,EAAQzJ,EAAOR,GAAK4K,QACxBR,EAAY,EAAGA,EAAY9K,EAAOW,OAAQmK,IAC/CjK,EAAQQ,MACJT,YACA2K,QAASR,EAAc/K,EAAO8K,IAC9BtL,KAAMwL,EAAYhL,EAAO8K,IACzBU,UAAWP,EAAgBjL,EAAO8K,IAClCW,SAAUP,EAAelL,EAAO8K,IAChCY,YAAapE,SAAS6D,EAAenL,EAAO8K,IAAa,IAAM,EAC/DxE,WAAY+E,EAAiBrL,EAAO8K,IACpChJ,UAAWsJ,EAAgBpL,EAAO8K,KAG1C1J,GAAOF,OAAOG,MAAOR,QAASA,IAElC,MAAOO,GAr1Ed,GAwoEO2I,GA8DA4B,EAUAC,EA2DAC,EA4EAC,EAwEAC,EASAC,GAkHAC,GAWAC,GA+NAC,GAobAC,GACAC,GACAC,GAWAtL,GAQAuL,GAQAC,GA0PAC,GA4MAC,GA2SAC,GAr8HA7L,GAAQ8L,OAAO9L,MAAO+L,GAAK/L,GAAM+L,GAAIC,GAAQhM,GAAMgM,MAAOC,GAASF,GAAGE,OAAQC,GAAalM,GAAMgD,KAAKkJ,WAAYC,GAAanM,GAAMoM,YAAaC,GAAcrM,GAAMsM,aAAc1N,MAAcA,SAAU2N,GAAW,SAAUC,GAC7N,MAAOA,IACR1N,GAAMT,EAAES,IAAK6G,GAAStH,EAAEsH,OAAQ8G,GAAazM,GAAMyM,WAAYC,GAAS,SAAUC,GAAQ,QAAStL,GAAW,WAAYuL,GAAW,WAAYC,GAAa,aAAcC,GAAO,OAAQC,GAAM,SAAUC,GAAK,kBAAmBC,GAAgB,gBAAiBC,GAAc,cAAeC,GAAY,YAAaC,GAAe,eAAgBC,GAAiB,iBAAkBC,GAAiB,eAAgBC,GAAkB,aAAcC,GAAkB,4DAA6DC,GAAqB,6JAA8JC,GAAoB,qKAAsKC,GAAoB,8GAA+GC,GAAe,2JAC9gCC,GAAY,OACZC,GAAe,UAuJfC,IACAC,IAAK,SAAU1L,EAAOT,GAClB,GAAIrC,GAAcqC,EAAMrC,WAMxB,OALKwC,GAASxC,GAEHwC,EAASM,KAChB9C,GAAe8C,GAFf9C,EAAc8C,EAIX9C,GAEXyO,MAAO,SAAU3L,EAAOT,GACpB,OAAQA,EAAMrC,aAAe,GAAK,GAEtC0O,SACIpM,UAAW,SAAUQ,EAAOT,GACxB,GAAIrC,GAAcqC,EAAMrC,WAYxB,OAXIqC,GAAMoM,QAAU3P,IAChBuD,EAAMoM,MAAQ,GAEbjM,EAASxC,GAEHwC,EAASM,KAChB9C,GAAe8C,GAFf9C,EAAc8C,EAIdN,EAASM,IACTT,EAAMoM,QAEHzO,GAEXc,OAAQ,SAAUuB,GACd,GAAIrC,GAAcqC,EAAMrC,WAIxB,OAHIwC,GAASxC,KACTA,GAA4BqC,EAAMoM,OAE/BzO,IAGf4E,IAAK,SAAU9B,EAAOT,GAClB,GAAIrC,GAAcqC,EAAMrC,WAOxB,OANKwC,GAASxC,IAAiB2C,EAAO3C,KAClCA,EAAc8C,GAEd9C,EAAc8C,IAAUN,EAASM,IAAUH,EAAOG,MAClD9C,EAAc8C,GAEX9C,GAEX2O,IAAK,SAAU7L,EAAOT,GAClB,GAAIrC,GAAcqC,EAAMrC,WAOxB,OANKwC,GAASxC,IAAiB2C,EAAO3C,KAClCA,EAAc8C,GAEd9C,EAAc8C,IAAUN,EAASM,IAAUH,EAAOG,MAClD9C,EAAc8C,GAEX9C,IAGX4O,GAAmBpC,GAAMrG,QACzB0I,KAAM,SAAU7E,GACZ8E,KAAK9E,QAAU7D,MAAW2I,KAAK9E,QAASA,GACxC8E,KAAKC,WAAaD,KAAKE,sBAAsB,QAASF,KAAK9E,QAAQ+E,YACnED,KAAKnN,SAAWmN,KAAKE,sBAAsB,OAAQF,KAAK9E,QAAQrI,WAEpEqN,sBAAuB,SAAUC,EAAU9P,GAApB,GAEf+P,GACA/M,EAES/B,EAASC,CADtB,IAHAlB,EAAcA,MACV+P,KAE+B,mBAA/B9P,GAASC,KAAKF,GAAmC,CACjD,IAASiB,EAAM,EAAGC,EAASlB,EAAYkB,OAAQD,EAAMC,EAAQD,IACzD+B,EAAQhD,EAAYiB,GACC,gBAAV+B,GACP+M,EAAO/M,MACAA,EAAM8M,KACbC,EAAO/M,EAAM8M,IAAa9M,EAGlChD,GAAc+P,EAElB,MAAO/P,IAEXgQ,YAAa,SAAU5G,EAAW6G,GAArB,GAGLnG,GAAM/J,EAAMoI,EAEZlH,EAJAiP,EAAoBD,EAAmB/O,QAAU,EACjDiP,EAAmBR,KAAKC,eAExBQ,EAAa,EAEbC,EAAkBjH,EAAUlI,OAC5BS,KACA2O,IACJ,IAAID,GAAmBJ,EAAmB/O,OAAQ,CAC9C,IAAKkP,EAAa,EAAGA,EAAaF,EAAmBE,IAAc,CAE/D,IADAtG,GAAS1I,YACJH,EAAM,EAAGA,EAAMoP,EAAiBpP,IACjClB,EAAOqJ,EAAUnI,GACjBkH,EAAQpI,EAAKqI,MAAM,KACnB0B,EAAK1I,QAAQ0I,EAAK1I,QAAQF,SACtBC,YACA2K,SAAUqE,EAAiBpQ,QAAa+L,SAAW,MACnD/L,KAAMA,EACNgM,UAAWhM,EACXiM,SAAU,IACVC,aAAa,EACbpF,WAAYsB,EAAMjH,OAAS,EAAIiH,EAAM,GAAKxI,EAC1C0C,UAAWtC,EAGfmQ,GAAoB,IACpBpG,EAAK1I,QAAQ0I,EAAK1I,QAAQF,SACtBC,YACA2K,QAASmE,EAAmBG,GAAYtE,QACxC/L,KAAMkQ,EAAmBG,GAAYtQ,WAAWC,KAChDgM,UAAW,WACXC,SAAU,IACVC,aAAa,EACbpF,WAAYlH,EACZ0C,UAAW,aAGnBV,EAAOA,EAAOT,QAAU4I,EAE5BwG,EAAK1O,KAAK0M,IAEd,OACIgC,KAAMA,EACN7O,OAAQE,IAGhB4O,gBAAiB,SAAUpQ,EAAKyJ,EAAUqG,GAAzB,GAGTG,GACArP,EACAgK,EACAyF,EACAvL,EACAwL,EAGAC,EACAC,EACAxI,EACApI,EACAkB,EAdAiP,EAAoBD,EAAmB/O,QAAU,EACjDiP,EAAmBR,KAAKC,eAOxB/O,KACA+P,IAMJ,KAAK7F,IAAO5K,GAAK,CAMb,IALAqQ,EAAUrQ,EAAI4K,GACd2F,EAAaf,KAAKkB,oBAAoBjH,EAAU4G,EAAQhH,YACxDvE,EAAUpE,EAAY6P,EAAW3L,WACjC0L,EAAcG,EAAgBF,EAAW3L,WACzC4L,EAAgBD,EAAWnQ,OAAO+H,MAC7B8H,EAAa,EAAGA,EAAaF,EAAmBE,IAAc,CAE/D,IADArP,GAAUK,YACLH,EAAM,EAAGA,EAAM0P,EAAczP,OAAQD,IAClCA,IAAQyP,EAAWnQ,OAAOgJ,aAC1BxI,EAAMK,QAAQL,EAAMK,QAAQF,SACxBC,YACA2K,QAAS0E,EAAQ7M,MACjB5D,KAAMyQ,EAAQzQ,KACdkM,aAAa,EACbD,SAAU,EACVD,UAAWyE,EAAQ3J,WAAa2J,EAAQzQ,KACxC8G,WAAY2J,EAAQ3J,WACpBxE,UAAWmO,EAAQ3J,WAAa2J,EAAQzQ,MAEzB,IAAfqQ,GACAK,EAAY7O,KAAKL,GAAUR,EAAOE,GAAKgI,KAAK,OAGhDlJ,EAAO4Q,EAAc1P,GACrBkH,EAAQpI,EAAKqI,MAAM,KACnBrH,EAAMK,QAAQL,EAAMK,QAAQF,SACxBC,YACA2K,SAAUqE,EAAiBpQ,QAAa+L,SAAW,MACnD/L,KAAMA,EACNgM,UAAWhM,EACXiM,SAAU,IACVC,aAAa,EACbpF,WAAYsB,EAAMjH,OAAS,EAAIiH,EAAM,GAAKxI,EAC1C0C,UAAWtC,GAInBmQ,GAAoB,IACpBnP,EAAMK,QAAQL,EAAMK,QAAQF,SACxBC,YACA2K,QAASmE,EAAmBG,GAAYtE,QACxC/L,KAAMkQ,EAAmBG,GAAYtQ,WAAWC,KAChDgM,UAAW,WACXC,SAAU,IACVC,aAAa,EACbpF,WAAYlH,EACZ0C,UAAW,aAGnB4C,EAAQA,EAAQ/D,QAAUH,EAE9BF,EAAY6P,EAAW3L,OAASE,EAChC2L,EAAgBF,EAAW3L,OAAS0L,EAExC,OACIH,KAAMM,EACNnP,OAAQZ,IAGhBgQ,oBAAqB,SAAUzP,EAASyF,GACpC,IAAK,GAAI5F,GAAM,EAAGA,EAAMG,EAAQF,OAAQD,IACpC,GAAIG,EAAQH,GAAKuI,aAAe3C,EAC5B,OACItG,OAAQa,EAAQH,GAChB8D,MAAO9D,IAKvB6P,UAAW,SAAU3Q,EAAKL,EAAYmQ,GAA3B,GAEHc,GACAC,CACJ,OAHAf,GAAqBA,MACjBc,EAAWpB,KAAKK,YAAYlQ,EAAWgK,KAAMmG,GAC7Ce,EAAerB,KAAKY,gBAAgBpQ,EAAKL,EAAW8J,SAAUqG,IAE9DK,QAAStI,OAAO7C,MAAM4L,EAAST,KAAMU,EAAaV,MAClD7O,UAAWuG,OAAO7C,MAAM4L,EAAStP,OAAQuP,EAAavP,UAG9DwP,cAAe,SAAUzO,EAAU0O,GAS/B,IATW,GAOPrR,GACAE,EAPAkB,EAAM,EACNC,EAASsB,GAAYA,EAAStB,OAC9BiQ,KACAC,KACAC,KACArR,EAAc2P,KAAKnN,aAGhBvB,EAAMC,EAAQD,IACjBlB,EAAOyC,EAASvB,GAAKnB,WAAWC,KAChCF,EAAUG,EAAYD,OACtBoR,EAAevP,KAAK7B,GAChBF,EAAQ8B,SACRyP,EAAYrR,GAAQF,EAAQ8B,QAE5B9B,EAAQwK,SACRgH,EAAQtR,GAAQF,EAAQwK,OAGhC,QACI/B,MAAO6I,EACPE,QAASA,EACTD,YAAaA,EACbF,QAASA,IAGjBI,aAAc,SAAUnR,EAAKoR,EAAcC,EAASC,GAAtC,GAENC,GACA3R,EAAMwG,EAAGrE,EAAGR,EAAGiQ,EACfC,EAAKC,EAAQC,EAGbC,EANApQ,KAIAqQ,EAAwB,EACxBC,KAEAC,EAAYV,EAAQtQ,QAAU,EAC9BiR,EAAeV,EAAWvQ,QAAU,CAOxC,KANIqQ,EAAaL,SACbe,EAAkBV,EAAajJ,MAC/B0J,EAAwBC,EAAgB/Q,QAExC6Q,EAAqBR,EAAajJ,MAEjC/B,EAAI,EAAGA,EAAI2L,EAAW3L,IAEvB,IADAqL,EAAMzR,EAAIqR,EAAQjL,IAAM+H,IACnBqD,EAAI,EAAGA,EAAIK,EAAuBL,IAInC,IAHIJ,EAAaL,UACba,GAAsBE,EAAgBN,KAErCzP,EAAI,EAAGA,EAAIiQ,EAAcjQ,IAQ1B,IAPA4P,EAAYL,EAAWvP,IAAMoM,GAC7BuD,EAASD,EAAIQ,MAAMN,GAEfJ,EADAI,IAAcxD,GACDsD,EAAIF,WAEJG,EAASA,EAAOH,cAE5BhQ,EAAI,EAAGA,EAAIqQ,EAAmB7Q,OAAQQ,IACvC3B,EAAOgS,EAAmBrQ,GAC1BiO,KAAK0C,SAAS1Q,EAAQ+P,EAAW3R,GAAOwR,EAAaF,QAAQtR,GAAOwR,EAAaH,YAAYrR,GAK7G,OAAO4B,IAEX0Q,SAAU,SAAU1Q,EAAQgC,EAAO0G,EAAQiI,GAAjC,GAEFzO,GADAD,EAAW,EAEXD,KACAA,EAAQ2O,EAAaA,EAAW3O,GAASA,EAAM9C,YAC/C+C,EAAWyG,EAAShJ,GAAMgJ,OAAOA,EAAQ1G,GAASA,GAEtDE,EAAUlC,EAAOT,OACjBS,EAAOkC,IACHA,QAASA,EACTF,MAAOA,GAAS,GAChBC,SAAUA,IAGlB2O,kBAAmB,SAAUnP,EAAUtD,EAAY0S,GAO/C,IAPe,GACXrK,GACAsK,EACAC,EAGA/O,EAFA2E,EAAQxI,EAAWwI,MACnBrH,EAAMnB,EAAWyJ,YAEdtI,EAAM,GAET,GADAkH,EAAQG,IAAQrH,GAAKmH,MAAM,KACvBD,EAAMjH,OAAS,IACfuR,EAActK,EAAM,GACpBuK,EAAgBvK,EAAM,GACtBxE,EAAQ6O,EAAQC,GAAarP,GAC7BO,EAAQA,IAAUhE,GAAuB,OAAVgE,EAAiBA,GAAAA,EAAmBA,EAC/DA,GAAS+O,GACT,OAAO,CAInB,QAAO,GAEXC,oBAAqB,SAAU1C,EAAoBhN,EAAmB2P,GAAjD,GAEb1P,GACAnD,EACKqQ,EAHLzO,IAGJ,KAASyO,EAAa,EAAGA,EAAaH,EAAmB/O,OAAQkP,IAC7DrQ,EAAOkQ,EAAmBG,GAAYtQ,WAAWC,KACjDmD,EAAQ0P,EAAUlB,WAAW3R,OAC7BmD,EAAMrC,YAAcoP,EAAmBG,GAAYyC,WAAW5P,EAAmBC,GACjFvB,EAAO5B,GAAQmD,CAEnB,OAAOvB,IAEXmR,gBAAiB,SAAU7C,EAAoBjQ,EAAawS,EAASO,EAAS9P,EAAmB+P,EAAU9P,EAAO+P,GAQ9G,IARa,GACTtP,GACA7D,EACA+R,EACAe,EACA7H,EAAKhL,EAAM8G,EAAYJ,EACvBrD,EAAWH,EAAkBG,SAC7BnC,EAAM,EACHA,EAAMjB,EAAYkB,OAAQD,IAC7BnB,EAAaE,EAAYiB,GACpB0O,KAAK4C,kBAAkBnP,EAAUtD,EAAY0S,KAGlD/L,EAAO3G,EAAWwI,MAAM7H,MAAM,EAAGX,EAAWyJ,aAAaN,KAAK,IAC9DlJ,EAAOD,EAAWwI,MAAMxI,EAAWyJ,aACnC5F,EAAQ6O,EAAQzS,GAAMqD,GACtBO,EAAQA,IAAUhE,GAAuB,OAAVgE,EAAiBA,GAAAA,EAAmBA,EACnEkD,EAAa9G,EACbA,EAAOA,EAAO,IAAM4D,EACpBoH,EAAMtE,EAAO1G,EACb8R,EAASkB,EAAQhI,KACbhG,MAAO7B,EAAMsE,YACbX,WAAYA,EACZ9G,KAAMA,EACNyJ,WAAY/C,EAAOI,EACnBlD,MAAOA,GAEXiP,EAAYI,EAASZ,MAAMrH,KAAU2G,eACrCsB,EAASZ,MAAMrH,IACXhG,MAAO8M,EAAO9M,MACd2M,WAAY/B,KAAKgD,oBAAoB1C,EAAoBhN,EAAmB2P,IAE5EK,IACKF,EAAQhI,IACT7H,EAAMsE,cAEVuL,EAAQhI,GAAO8G,KAI3BqB,oBAAqB,SAAUrI,GAAV,GAIb/K,GAAYD,EAASoB,EAAKC,EAC1BiS,EAAkBhQ,EAJlBiQ,EAAqBvI,EAAQrI,aAC7BA,EAAWmN,KAAKnN,aAChB6Q,IAGJ,IAAID,EAAmBlS,QACnB,IAAKD,EAAM,EAAGC,EAASkS,EAAmBlS,OAAQD,EAAMC,EAAQD,IAI5D,GAHAnB,EAAasT,EAAmBnS,GAChCpB,EAAU2C,EAAS1C,EAAWC,MAC9BoT,EAAmB,KACftT,EAAS,CAET,GADAsD,EAAYtD,EAAQsD,UACK,gBAAdA,GAAwB,CAE/B,GADAgQ,EAAmB/D,GAAUjM,EAAUmQ,gBAClCH,EACD,KAAUI,OAAM,sCAEpB1T,GAAQsD,UAAYgQ,EAAiBhQ,WAAagQ,EAClDtT,EAAQ8B,OAASwR,EAAiBxR,OAEtC0R,EAAYzR,MACR9B,WAAYA,EACZgM,QAASjM,EAAQiM,QACjBnK,OAAQ9B,EAAQ8B,OAChBkR,WAAYjQ,EAAsB/C,UAK9CwT,GAAYzR,MACR9B,YAAcC,KAAM,WACpB+L,QAAS,UACT+G,WAAY,WACR,MAAO,KAInB,OAAOQ,IAEXG,cAAe,SAAUlL,GAAV,GAEPH,GACApI,EACKkB,EAHLU,IAGJ,KAASV,EAAM,EAAGA,EAAMqH,EAAMpH,OAAQD,IAClClB,EAAOuI,EAAMrH,GACbkH,EAAQpI,EAAKqI,MAAM,KACfD,EAAMjH,OAAS,EACfS,EAAOwG,EAAM,IAAM9G,GAAM0B,OAAOoF,EAAM,IAAI,GAE1CxG,EAAO5B,GAAQsB,GAAM0B,OAAOrC,EAAcX,IAAO,EAGzD,OAAO4B,IAEX8R,kBAAmB,SAAUzT,GAAV,GAKNiB,GAJLyS,EAAoBjK,EAAiBzJ,GACrCoJ,EAAYF,EAAawK,EAAkB5J,MAC3CF,EAAW8J,EAAkB9J,SAC7BjI,IACJ,KAASV,EAAM,EAAGA,EAAM2I,EAAS1I,OAAQD,IACrCU,EAAOC,KAAKuH,EAASS,EAAS3I,GAAKlB,KAAMqJ,GAE7C,QACIU,KAAMV,EACNQ,SAAUjI,IAGlBgS,QAAS,SAAUtP,EAAMmG,GAAhB,GAIDoJ,GACA3S,EACAyJ,CALJ,KAAKF,EACD,MAAOnG,EAKX,KAFIpD,EAAM,EACNyJ,EAAUF,EAAOE,QACdzJ,EAAMyJ,EAAQxJ,OAAQD,IACzB2S,EAAOlJ,EAAQzJ,GACO,OAAlB2S,EAAKxJ,WACLM,EAAQzJ,GAAO0O,KAAKkE,iBAAiBD,GAG7C,OAAO,IAAIvS,IAAMgD,KAAKyP,MAAMzP,GAAMmG,OAAOA,GAAQnG,MAErDwP,iBAAkB,SAAUrJ,GAAV,GAMLvJ,GALL0C,EAAQ6G,EAAO7G,MAAMyE,MAAM,KAC3BzG,IACJ,KAAKgC,EAAMzC,OACP,MAAOyC,EAEX,KAAS1C,EAAM,EAAGA,EAAM0C,EAAMzC,OAAQD,IAClCU,EAAOC,MACHoB,MAAOwH,EAAOxH,MACdoH,SAAU,KACVzG,MAAOA,EAAM1C,IAGrB,QACI8S,MAAO,KACPrJ,QAAS/I,IAGjBqS,QAAS,SAAU3P,EAAMwG,GAAhB,GAIDrI,GACAyR,EACAC,EACAC,EAcAC,EACArB,EACAsB,EACAC,EACApR,EACA+M,EACAsE,EACAC,EACAC,EACAC,EACAC,EACAvR,EACAH,EACA2R,EACArQ,EAAQsQ,EAAeC,EAAS9B,EAChCjI,EAAKtE,EAAMI,EAAYlD,EACvBoR,EAAaC,EACb9T,EACAD,CACJ,IAvCAoD,EAAOA,MACPwG,EAAUA,MACVxG,EAAOsL,KAAKgE,QAAQtP,EAAMwG,EAAQL,QAC9BhI,EAAWqI,EAAQrI,aACnByR,EAA2C,SAAzBpJ,EAAQoK,aAC1Bf,EAAoBrJ,EAAQkI,YAC5BoB,EAAiBtJ,EAAQwJ,UACxBH,EAAkBhT,QAAUiT,EAAejT,UAAYsB,EAAStB,QAAUsB,EAAStB,QAAU+S,KAC9FC,EAAoBC,EACpBA,KACAF,GAAkB,GAEjBC,EAAkBhT,QAAWiT,EAAejT,SAC7C+S,GAAkB,IAEjBC,EAAkBhT,QAAUsB,EAAStB,SACtCgT,EAAoB5T,EAAiBuK,EAAQrI,WAEjD0R,EAAoBvE,KAAK8D,kBAAkBS,GAC3CC,EAAiBxE,KAAK8D,kBAAkBU,GACpCC,KACArB,KACAsB,KAEAnR,GAAUsE,YAAa,GACvByI,EAAqBN,KAAKuD,oBAAoBrI,GAC9C0J,EAAgB5E,KAAK6D,cAAcU,EAAkBpK,MACrD0K,EAAa7E,KAAK6D,cAAcW,EAAerK,MAC/C2K,GAAY,EACZC,EAAkBR,EAAkBtK,SACpC+K,EAAeR,EAAevK,SAG9BgL,EAA0C,IAAxBD,EAAazT,OAI/BA,EAASmD,EAAKnD,OACdD,EAAM,EACNiT,EAAkBpK,KAAK5I,QAAUiT,EAAerK,KAAK5I,OAErD,IADAuT,GAAY,EACPxT,EAAM,EAAGA,EAAMC,EAAQD,IAaxB,IAZAmC,EAAWiB,EAAKpD,GAChBgC,GACIG,SAAUA,EACV2B,MAAO9D,GAEX+R,EAAWoB,EAAe9F,MACtB8D,SACAV,eAEJ/B,KAAKmD,gBAAgB7C,EAAoByE,EAAiBH,EAAexB,EAAS9P,EAAmB+P,EAAU9P,GAAQ0R,GACvH5B,EAAStB,WAAa/B,KAAKgD,oBAAoB1C,EAAoBhN,EAAmB+P,GACtFoB,EAAe9F,IAAiB0E,EAC3BzO,EAAS,EAAGA,EAASoQ,EAAazT,OAAQqD,IAC3CsQ,EAAgBF,EAAapQ,GACxBoL,KAAK4C,kBAAkBnP,EAAUyR,EAAeL,IAOrD/N,EAAOoO,EAAcvM,MAAM7H,MAAM,EAAGoU,EAActL,aAAaN,KAAK,IACpE6L,EAAUD,EAAcvM,MAAMuM,EAActL,aAC5C1C,EAAaiO,EACbR,EAAWE,EAAWM,GAAS1R,GAC/BkR,EAAWA,IAAa3U,EAAY2U,GAAAA,EAAsBA,EAC1DQ,EAAUA,EAAU,IAAMR,EAC1BvJ,EAAMtE,EAAOqO,EACbT,EAAKtJ,IACDvB,WAAY/C,EAAOI,EACnBA,WAAYA,EACZ9G,KAAM+U,EACNnR,MAAO2Q,GAEX3Q,EAAQyQ,EAAerJ,KACnBqH,SACAV,eAEJ/B,KAAKmD,gBAAgB7C,EAAoByE,EAAiBH,EAAexB,EAAS9P,EAAmBU,EAAOT,GAAO,GACnHS,EAAM+N,WAAa/B,KAAKgD,oBAAoB1C,EAAoBhN,EAAmBU,GACnFyQ,EAAerJ,GAAOpH,GAzBlBgM,KAAKmD,gBAAgB7C,EAAoByE,EAAiBH,EAAexB,EAAS9P,GAC9EmP,SACAV,eACDxO,GAAO,EAyC1B,OAfIuR,IAAavT,KACT+O,EAAmB/O,OAAS,IAAO2J,EAAQkI,SAAYlI,EAAQkI,QAAQ7R,SACvEgT,GACIpK,QACAF,cAGRmL,EAAcpF,KAAKmB,UAAUiC,EAASmB,EAAmBD,KAAuBhE,GAChF+E,EAAWrF,KAAKmB,UAAUuD,EAAMF,EAAgBF,EAAkBhE,MAClE8C,EAAUgC,EAAYtT,OACtB4S,EAAOW,EAASvT,OAChB2S,EAAiBzE,KAAK2B,aAAa8C,EAAgBzE,KAAKsB,cAAchB,EAAoBgE,GAAkBe,EAAS1E,KAAMyE,EAAYzE,OAEvI8D,EAAiBrB,EAAUsB,MAG3Ba,MACInC,SAAWtR,OAAQsR,GACnBsB,MAAQ5S,OAAQ4S,IAEpBhQ,KAAM+P,MAIde,GAAiB9H,GAAMrG,QACvB0I,KAAM,SAAU7E,EAASuK,GACrBzF,KAAKyF,UAAYA,EACjBzF,KAAK9E,QAAUuK,EAAUvK,YACpB8E,KAAKyF,UAAUC,UACZvH,GAAWjD,EAAQwK,YACnB1F,KAAK0F,SAAWxK,EAAQwK,WAIpCC,KAAM,SAAUzK,GACZ,MAAO8E,MAAKyF,UAAUE,KAAKzK,IAE/B0K,OAAQ,SAAU1K,GACd,MAAO8E,MAAKyF,UAAUG,OAAO1K,IAEjC2K,OAAQ,SAAU3K,GACd,MAAO8E,MAAKyF,UAAUI,OAAO3K,IAEjC4K,QAAS,SAAU5K,GACf,MAAO8E,MAAKyF,UAAUK,QAAQ5K,IAElCwK,SAAU,SAAUxK,GAChB,MAAI8E,MAAKyF,UAAUC,SACR1F,KAAKyF,UAAUC,SAASxK,IAEnCA,EAAQ6K,YAAR7K,IAEJ8K,QAAS,SAAUrS,GAAV,GAKDsS,GAJA/K,EAAU8E,KAAK9E,WACnB,OAAIvH,KAAQ3D,GACAkL,EAAQ+K,gBAAkBD,SAElCC,EAAa/K,EAAQ+K,eACzBA,EAAWD,QAAUrS,EACrBqM,KAAK9E,QAAQ+K,WAAaA,EAC1BlW,EAAEsH,OAAO2I,KAAKyF,UAAUvK,SAAW+K,WAAYA,IAH3CA,IAKRnL,KAAM,SAAUnH,GAAV,GAKEsS,GAJA/K,EAAU8E,KAAK9E,WACnB,OAAIvH,KAAQ3D,GACAkL,EAAQ+K,gBAAkBnL,MAElCmL,EAAa/K,EAAQ+K,eACzBA,EAAWnL,KAAOnH,EAClBqM,KAAK9E,QAAQ+K,WAAaA,EAC1B5O,IAAO,EAAM2I,KAAKyF,UAAUvK,SAAW+K,WAAYA,IAH/CA,MAMRC,GAAkBtI,GAAWvG,QAC7B0I,KAAM,SAAU7E,GAAV,GAGErI,GAFAiI,IAASI,OAAeiL,YAAcrL,KACtCwK,EAAe,UAEfa,GACAZ,KAAMtH,GACNmI,MAAOnI,GACPoI,SAAUpI,GACVpL,SAAUoL,GACVgC,WAAYhC,GACZqI,YAAarI,GACbsI,OAAQtI,GACRxM,QAASwM,GAETnD,KACAqL,EAASpW,EAAEsH,OAAO8O,EAAQnG,KAAKwG,YAAY1L,IAC3CkF,KAAKyG,YAAc,GAAI3G,IAAiBhF,IAE5C8C,GAAW8I,GAAG3G,KAAKxP,KAAKyP,KAAM3I,IAAO,MAAY8O,OAAQA,GAAUjL,IACnE8E,KAAKyF,UAAY,GAAID,IAAexF,KAAK9E,QAAQuK,cAAiBzF,KAAKyF,WACvEzF,KAAK2G,SAAWhW,EAAiBqP,KAAK9E,QAAQkI,SAC9CpD,KAAK4G,MAAQjW,EAAiBqP,KAAK9E,QAAQwJ,MAC3C7R,EAAWmN,KAAK9E,QAAQrI,aACQ,oBAA5BvC,GAASC,KAAKsC,KACdyS,EAAezS,EAAST,MAAQ,UAChCS,EAAWA,EAASgU,YAExB7G,KAAK8G,UAAY7W,EAAkB4C,GACnCmN,KAAK+G,cAAgBzB,EACrBtF,KAAKgH,eAAiB,EACtBhH,KAAKiH,UAETT,YAAa,SAAU1L,GACnB,OACImF,WAAY,WAAA,GAGC7E,GAFLpJ,KACAiO,EAAanF,EAAKmF,UACtB,KAAS7E,IAAO6E,GACZjO,EAAOC,MACH7B,KAAMgL,EACNe,QAAS8D,EAAW7E,GAAKe,SAAWf,EACpC8L,WAAY9L,EACZ+L,iBAAkB/L,EAClB1K,KAAM,GAWd,OARIoK,GAAKjI,UACLb,EAAOC,MACH7B,KAAM2C,GACNoJ,QAASpJ,GACTmU,WAAYnU,GACZrC,KAAM,IAGPsB,GAEXsU,YAAa,WACT,UAEJzT,SAAU,WAAA,GAGGuI,GAFLpJ,KACAa,EAAWiI,EAAKjI,QACpB,KAASuI,IAAOvI,GACZb,EAAOC,MACH7B,KAAMgL,EACNe,QAASf,EACT8L,WAAY9L,EACZ8H,WAAY9H,GAGpB,OAAOpJ,IAEXP,QAAS1B,EAAEqX,MAAM,SAAUC,EAAUC,GAApB,GAMTlU,GACAY,EANA5D,EAAOkX,EAAaC,iBAAmBD,EAAaE,iBACpDC,EAAazH,KAAK9E,QAAQiL,OAAOzR,KACjCgT,EAAavJ,GAAWsJ,GAAcA,EAAa/V,GAAM0B,OAAOqU,GAAY,GAC5E/S,EAAOsL,KAAK9E,QAAQxG,MAAQgT,EAAW1H,KAAK9E,QAAQxG,OAASsL,KAAK2H,aAClE3V,KAGAV,EAAM,EACNsW,IAIJ,IAHIxX,IACAA,EAAOA,EAAKqI,MAAM,KAAK,KAEtB6O,EAAaO,OAUd,MATA7V,GAAOC,MACHkK,QAASrB,EAAKmF,WAAW7P,GAAM+L,SAAW/L,EAC1C0X,oBAAqB,IACrBC,oBAAqB3X,EACrB4X,oBAAqB5X,EACrBmX,gBAAiBnX,EACjBA,KAAMA,EACN8W,WAAY9W,IAET4B,CAGX,KADAoB,EAAS1B,GAAM0B,OAAOrC,EAAcX,IAAO,GACpCkB,EAAMoD,EAAKnD,OAAQD,IACtB0C,EAAQZ,EAAOsB,EAAKpD,KACf0C,GAAmB,IAAVA,GAAiB4T,EAAS5T,KACpC4T,EAAS5T,IAAS,EAClBhC,EAAOC,MACHkK,QAASnI,EACT8T,oBAAqB,IACrBC,oBAAqB3X,EACrB4X,oBAAqB5X,EACrBmX,gBAAiBnX,EACjBA,KAAM4D,EACNkT,WAAYlT,IAIxB,OAAOhC,IACRgO,QAGX9E,SACI+M,eAAe,EACfC,cAAc,EACdC,iBAAiB,EACjBC,gBAAgB,EAChBC,kBAAkB,GAEtBrC,QAAS,SAAUrS,GACf,MAAIA,KAAQ3D,EACDgQ,KAAKyF,UAAUO,WAE1BhG,KAAKyF,UAAUO,QAAQrS,GACvBqM,KAAKsI,gBACLtI,KAAKiH,SACLjH,KAAKtL,SAHLsL,IAKJlF,KAAM,SAAUnH,GACZ,MAAIA,KAAQ3D,EACDgQ,KAAKyF,UAAU3K,QAE1BkF,KAAKyF,UAAU3K,KAAKnH,GACpBqM,KAAKiH,SACLjH,KAAKsI,gBACLtI,KAAKtL,SAHLsL,IAKJuF,KAAM,WACF,MAAOvF,MAAKiH,OAEhB7D,QAAS,SAAUzP,GACf,MAAIA,KAAQ3D,EACDgQ,KAAK2G,UAEhB3G,KAAKgH,gBAAkB,EACvBhH,KAAKuI,gBAAiB,EACtBvI,KAAK2G,SAAWhW,EAAiBgD,GACjCqM,KAAKwI,OACDpF,QAASzP,EACT+Q,KAAM1E,KAAKyI,sBACX5V,SAAUmN,KAAKnN,WACf+F,KAAMoH,KAAKpH,OACXiC,OAAQmF,KAAKnF,WARjBmF,IAWJ0E,KAAM,SAAU/Q,GACZ,MAAIA,KAAQ3D,EACDgQ,KAAK4G,OAEhB5G,KAAKgH,gBAAkB,EACvBhH,KAAKuI,gBAAiB,EACtBvI,KAAK4G,MAAQjW,EAAiBgD,GAC9BqM,KAAKwI,OACDpF,QAASpD,KAAK0I,yBACdhE,KAAM/Q,EACNd,SAAUmN,KAAKnN,WACf+F,KAAMoH,KAAKpH,OACXiC,OAAQmF,KAAKnF,WARjBmF,IAWJnN,SAAU,SAAUc,GAChB,MAAIA,KAAQ3D,EACDgQ,KAAK8G,WAEhB9G,KAAKgH,gBAAkB,EACvBhH,KAAKuI,gBAAiB,EACtBvI,KAAKwI,OACDpF,QAASpD,KAAK0I,yBACdhE,KAAM1E,KAAKyI,sBACX5V,SAAU5C,EAAkB0D,GAC5BiF,KAAMoH,KAAKpH,OACXiC,OAAQmF,KAAKnF,WAPjBmF,IAUJsF,aAAc,WACV,MAAOtF,MAAK+G,eAAiB,WAEjC4B,YAAa,SAAU7R,EAAM1E,GAAhB,GAOAd,GACDiH,EAUJlI,EAjBAuY,EAAkB,YAATxW,EAAqB,UAAY,OAC1CyW,EAAiB,YAATzW,EAAqB,OAAS,UACtCX,EAAUd,EAAiBmG,GAC3BgS,EAAiBrW,EAAQhB,EAAQA,EAAQF,OAAS,GAGtD,KAFAyO,KAAK+I,cAAgBH,EACrBnX,EAAUmB,EAAsBoN,KAAKuF,OAAOqD,GAASnX,EAASuO,KAAKnN,YAC1DvB,EAAM,EAAGA,EAAMG,EAAQF,OAAQD,IAEpC,GADIiH,EAAa9F,EAAQhB,EAAQH,IAC7BiH,IAAeuQ,EAAgB,CAC/B,GAAIrX,EAAQH,GAAKT,OACb,MAEJY,GAAQH,GAAKT,QAAS,MAEtBY,GAAQH,GAAKT,QAAS,CAG1BR,MACJA,EAAYuY,GAAUnX,EACtBpB,EAAYwY,GAAS7I,KAAKgJ,oBAAoBH,GAC9C7I,KAAKiJ,OAAO5Y,IAEhB2Y,oBAAqB,SAAU5W,GAAV,GACbmT,GAAOvF,KAAKuF,OACZlV,EAAc2P,KAAK5N,QAIvB,OAHImT,IAAQA,EAAKnT,IAASmT,EAAKnT,GAAMN,QAAUyT,EAAKnT,GAAMN,OAAO,KAC7DzB,EAAcwB,EAAmB0T,EAAKnT,GAAMN,aAEzCzB,GAEXqY,uBAAwB,WACpB,MAAO1I,MAAKgJ,oBAAoB,YAEpCP,oBAAqB,WACjB,MAAOzI,MAAKgJ,oBAAoB,SAEpCE,SAAU,SAAUxU,EAAMyU,GACtBnJ,KAAKoJ,MAAQ1U,EACbyU,EAAIA,MACJA,EAAE1G,MAAQ0G,EAAE1G,OAASzC,KAAKoJ,MAC1BpJ,KAAKqJ,QAAQjL,GAAQ+K,IAEzBF,OAAQ,SAAU/N,GACd,GAAIoO,GAAOtJ,IAKX,OAJK9E,KACD8E,KAAKgH,gBAAkB,EACvBhH,KAAKuI,gBAAiB,GAEnBe,EAAKd,MAAMnR,OACdkS,KAAMD,EAAKC,OACXC,SAAUF,EAAKE,WACf5Q,KAAM0Q,EAAK1Q,OACXiC,OAAQyO,EAAKzO,SACb4O,MAAOH,EAAKG,QACZjW,UAAW8V,EAAK9V,YAChB4P,QAASpD,KAAK0I,yBACdhE,KAAM1E,KAAKyI,sBACX5V,SAAUmN,KAAKnN,YAChBqI,KAEPsN,MAAO,SAAUtN,GACb,GAAI3H,GAAQyM,KAAKsI,YAAYpN,EAC7B,OAAI8E,MAAK0J,MAAMnY,QAAUyO,KAAKyG,aAC1BzG,KAAK2J,QAAQpW,GACbyM,KAAK4J,iBAAiB5J,KAAK6J,eACpB9Z,EAAE+Z,WAAWC,UAAUC,WAE3BhK,KAAK2F,KAAKpS,IAErB+U,YAAa,SAAUpN,GAenB,MAdAA,GAAU0C,GAAW8I,GAAG4B,YAAY/X,KAAKyP,KAAM9E,GAC3CA,IAAYlL,IACZgQ,KAAK8G,UAAY7W,EAAkBiL,EAAQrI,UACvCqI,EAAQkI,QACRlI,EAAQkI,QAAUzS,EAAiBuK,EAAQkI,SACnClI,EAAQkI,UAChBpD,KAAK2G,aAELzL,EAAQwJ,KACRxJ,EAAQwJ,KAAO/T,EAAiBuK,EAAQwJ,MAChCxJ,EAAQwJ,OAChB1E,KAAK4G,WAGN1L,GAEXL,OAAQ,SAAUlH,GACd,MAAIA,KAAQ3D,EACDgQ,KAAKgE,SAEhBhE,KAAKgH,gBAAkB,EACvBhH,KAAKuI,gBAAiB,EACtBvI,KAAKiJ,QACDpO,OAAQlH,EACR4V,KAAM,IAJVvJ,IAOJiK,aAAc,SAAUnT,GACpBkJ,KAAK2I,YAAY7R,EAAM,YAE3BoT,UAAW,SAAUpT,GACjBkJ,KAAK2I,YAAY7R,EAAM,SAE3BiP,QAAS,SAAUrR,GACf,GAAIyV,EACAnK,MAAKyG,cACL0D,GAAgBnK,KAAKoK,OAAO1V,KAAKA,QAAa5D,MAAM,IAExD8M,GAAW8I,GAAGX,QAAQxV,KAAKyP,KAAMtL,GAC7ByV,IACAnK,KAAK6J,cAAgBM,IAG7BE,eAAgB,SAAU3V,EAAM6Q,GAAhB,GAEJ+E,GAIJC,EAAeC,EACf1Y,EAAQ2Y,EAAY5X,EAAU6X,EAC9BnG,EACAC,EACAmG,EAqEA3Y,CAEJ,OAhFIgO,MAAKyG,cACD6D,EAAgBtK,KAAKyG,YAAYpC,QAAQ3P,EAAMsL,KAAK4K,cACxDlW,EAAO4V,EAAc5V,KACrB6Q,EAAO+E,EAAc/E,MAIrBhB,EAAoBvE,KAAKoD,UACzBoB,EAAiBxE,KAAK0E,OACtBiG,EAAkBpF,EAAKnC,SAAWmC,EAAKnC,QAAQtR,OAC9CyS,EAAkBhT,SAAUiT,EAAejT,SAAUoZ,IAAoB3K,KAAK6K,eAAetZ,QAAWyO,KAAKnN,WAAWtB,SACzHgU,GACInC,WACAsB,KAAMa,EAAKnC,UAGdmB,EAAkBhT,QAAWiT,EAAejT,QAAkC,SAAxByO,KAAKsF,iBAA6BqF,IACzFpF,GACInC,WACAsB,KAAMa,EAAKnC,UAGnBpD,KAAKiH,OACD7D,QAAS3N,EAAcuK,KAAKiH,MAAM7D,SAClCsB,KAAMjP,EAAcuK,KAAKiH,MAAMvC,OAEnCa,GACInC,QAAS3N,EAAc8P,EAAKnC,SAC5BsB,KAAMjP,EAAc8P,EAAKb,OAE7B6F,EAAgBvK,KAAK8K,iBAAiBvF,EAAKnC,QAAQtR,OAAQkO,KAAKiH,MAAM7D,QAAQtR,OAAQyS,EAAmBvE,KAAK+K,mBAC9GP,EAAaxK,KAAK8K,iBAAiBvF,EAAKb,KAAK5S,OAAQkO,KAAKiH,MAAMvC,KAAK5S,OAAQ0S,EAAgBxE,KAAK6K,gBAC9F7K,KAAKgH,eAAiB,IACtBhH,KAAKgH,gBAAkB,GAEtBhH,KAAKyG,cACN/R,EAAOsL,KAAKgL,gBACRlW,cAAeyQ,EAAKnC,QAAQtR,OAAOP,OACnCkD,WAAY8Q,EAAKb,KAAK5S,OAAOP,OAC7BgZ,cAAeA,EACfC,WAAYA,EACZ9V,KAAMA,KAGY,QAAtBsL,KAAK+I,eACLjX,EAASyT,EAAKnC,QAAQtR,OACtBe,EAAWmN,KAAK+K,kBAChBN,EAAatW,EAAaoR,EAAKnC,QAASpD,KAAKiH,MAAM7D,QAASvQ,GACxD4X,IACAC,EAAa,UACbnF,EAAKnC,QAAUqH,EACfnW,EAAmBxC,EAAQ2Y,EAAW3Y,OAAQyT,EAAKb,KAAK5S,OAAOP,OAAQsB,EAAU6B,GAC5EsL,KAAKyG,cACN/R,EAAOsL,KAAKgL,gBACRlW,cAAeT,EAAakR,EAAKnC,QAAQtR,OAAQe,GACjD4B,WAAY8Q,EAAKb,KAAK5S,OAAOP,OAC7BmD,KAAMA,OAIW,WAAtBsL,KAAK+I,gBACZjX,EAASyT,EAAKb,KAAK5S,OACnBe,EAAWmN,KAAK6K,eAChBJ,EAAatW,EAAaoR,EAAKb,KAAM1E,KAAKiH,MAAMvC,KAAM7R,GAClD4X,IACAC,EAAa,OACbnF,EAAKb,KAAO+F,EACZvV,EAAgBpD,EAAQ2Y,EAAW3Y,OAAQyT,EAAKnC,QAAQtR,OAAOP,OAAQsB,EAAU6B,GAC5EsL,KAAKyG,cACN/R,EAAOsL,KAAKgL,gBACRlW,cAAeT,EAAakR,EAAKb,KAAK5S,OAAQe,GAC9C4B,WAAY8Q,EAAKnC,QAAQtR,OAAOP,OAChCmD,KAAMA,OAKtBsL,KAAK+I,cAAgB,KACjB/W,EAASgO,KAAKiL,WAAW1F,EAAM7Q,EAAMgW,GACzC1K,KAAKiH,MAAQjV,EAAOuT,KACbvT,EAAO0C,MAElBwW,UAAW,SAAUxW,GAAV,GACH6Q,GAAOvF,KAAKoK,OAAO7E,KAAK7Q,GACxByW,EAAUnL,KAAKoK,OAAO1V,KAAKA,EAI/B,OAHIsL,MAAKyG,cACLzG,KAAK2H,SAAWwD,GAEbnL,KAAKqK,eAAec,EAAS5F,IAExC6F,aAAc,SAAUha,EAAOlB,EAASmb,GAA1B,GAINjP,GAAWC,EACXjM,EAAM8G,EACNoF,EACA5J,EACAyJ,EACAvL,EARAa,EAAUL,EAAMK,QAChBF,EAASE,EAAQF,OACjB4I,GAAS1I,YAOTH,EAAM,CAIV,KAHIpB,IACAqB,GAAU,GAEPD,EAAMC,EAAQD,IACjBV,EAASa,EAAQH,GACjB+K,GAAkBzL,EAAOyL,SACzBjM,EAAOQ,EAAOR,KACd8G,EAAatG,EAAOsG,WACpBiF,EAAUvL,EAAOuL,SAAW/L,EAC5BkM,EAAc1L,EAAO0L,YACrB5J,EAAY9B,EAAO8B,UACnB0J,EAAYxL,EAAOwL,UACfiP,IACAlP,EAAU,MACO,IAAbE,EACAnF,EAAatG,EAAOR,KAEpBiM,GAAY,EAEhBC,GAAc,EACdlM,EAAOsC,EAAY0J,EAAYlF,GAEnCiD,EAAK1I,QAAQQ,MACT7B,KAAMA,EACNoB,YACA2K,QAASA,EACTC,UAAWA,EACXC,SAAUA,GAAAA,EACVC,YAAaA,EACb5J,UAAWA,EACXwE,WAAamU,EAAyB,GAAbnU,GASjC,OANIhH,IACAiK,EAAK1I,QAAQQ,MACT7B,KAAMF,EAAQE,KACdoB,cAGD2I,GAEXmR,SAAU,SAAUtV,EAAQC,EAAQ5F,GAA1B,GAIFoB,GACAb,EACAT,EACAob,EACAlP,EACK/K,EAASC,CARlB,IAAI0E,EAAO1E,OACP,MAAOyB,GAAkBiD,EAAQD,GAAQ5E,KAO7C,KALIK,EAAUuE,EAAOvE,QAGjB8Z,GAAS,EAEJja,EAAM,EAAGC,EAASE,EAAQF,OAAQD,EAAMC,EAAQD,IAIrD,GAHAV,EAASa,EAAQH,GACjB+K,GAAkBzL,EAAOyL,UAAa,EACtClM,EAAaE,EAAYiB,KACN,IAAb+K,GAAkBlM,GAAcS,EAAOR,OAASqC,EAAQtC,IAAc,CACxEob,GAAS,CACT,OAGR,MAAOA,IAEXN,WAAY,SAAUO,EAAY9W,EAAMgW,GAA5B,GAIJe,GAAY3Z,EAYZ4Z,EAQAC,EAKIC,EA5BJC,EAAiB7L,KAAK+K,kBACtBe,EAAc9L,KAAK6K,eACnBtF,EAAOvF,KAAKuF,OAEZwG,EAAgB1X,EAAakR,EAAKb,KAAK5S,OAAQga,GAC/CE,EAAgBR,EAAW9G,KAAK5S,OAAOP,OACvC0a,EAAmB5X,EAAakR,EAAKnC,QAAQtR,OAAQ+Z,GACrDK,EAAmBV,EAAWpI,QAAQtR,OAAOP,MA8BjD,OA7BkB,WAAdmZ,GACAwB,EAAmBD,EACnBna,EAAS0Z,EAAWpI,QAAQtR,SAE5BA,EAAS0F,EAAYgU,EAAWpI,QAAQtR,OAAQ+Z,GAChDnX,EAAOyD,EAAqBrG,EAAQ4C,IAEpCgX,EAAgB3V,EAAYwP,EAAKnC,QAAQtR,OAAQA,EAAQ+Z,GAC3C,QAAdnB,GACAsB,EAAgB3X,EAAamX,EAAW9G,KAAK5S,OAAQga,GACrDha,EAAS0Z,EAAW9G,KAAK5S,SAEzBA,EAAS0F,EAAYgU,EAAW9G,KAAK5S,OAAQga,GAC7CpX,EAAOgD,EAAkB5F,EAAQ4C,IAEjCiX,EAAa5V,EAAYwP,EAAKb,KAAK5S,OAAQA,EAAQga,GACvDvG,EAAKnC,QAAQtR,OAAS4Z,EAAc5Z,OACpCyT,EAAKb,KAAK5S,OAAS6Z,EAAW7Z,OAC1Bma,IAAqB5X,EAAakR,EAAKnC,QAAQtR,OAAQ+Z,IACvDJ,EAAaC,EAActW,MAAQM,EAAcgW,EAActV,WAAYsV,EAAc/V,YAAakW,GAClGD,EAASK,EAAmBC,EAChCxX,EAAOsL,KAAKmM,iBAAiBzX,EAAM+W,EAAYO,EAAeE,EAAkBN,IACzEG,IAAkB1X,EAAakR,EAAKb,KAAK5S,OAAQga,KACxDL,EAAaE,EAAWvW,MAAQM,EAAciW,EAAWvV,WAAYuV,EAAWhW,YAAamW,GAC7FpX,EAAOsL,KAAKoM,cAAc1X,EAAM+W,EAAYO,EAAeE,IAE5B,IAA/B3G,EAAKnC,QAAQtR,OAAOP,QAA4C,IAA5BgU,EAAKb,KAAK5S,OAAOP,SACrDmD,OAGA6Q,KAAMA,EACN7Q,KAAMA,IAGdyX,iBAAkB,SAAUhB,EAAStT,EAAapD,EAAYK,EAAe8W,GAA3D,GAEVhU,GAAUxC,EAAiBiX,EAD3B3X,EAAOsL,KAAKtL,OAAO4X,SACFC,EAAO,EACxBV,EAAiBhW,KAAKC,IAAIkK,KAAK+K,kBAAkBxZ,OAAQ,EAM7D,KALAkD,EAAaoB,KAAKC,IAAIrB,EAAY,GAC9BC,EAAKnD,OAAS,IACdgb,EAAOV,EACPD,GAAUC,GAETjU,EAAW,EAAGA,EAAWnD,EAAYmD,IACtCxC,EAAQyC,EAAcD,EAAWgU,EACjCS,EAAQlB,EAAQ5T,OAAO,EAAGzC,GAC1BuX,EAAM9U,OAAO,EAAGgV,MACbhV,OAAO/B,MAAMd,GACZU,EACA,GACFiD,OAAOgU,GAEb,OAAO3X,IAEX0X,cAAe,SAAUjB,EAASvT,EAAUnD,EAAYK,GAAzC,GAEPxD,GAAKgG,EAAW+U,EADhB3X,EAAOsL,KAAKtL,OAAO4X,SAEnBR,EAAcjW,KAAKC,IAAIkK,KAAK6K,eAAetZ,OAAQ,EAMvD,KALAuD,EAAgBe,KAAKC,IAAIhB,EAAe,GACpCJ,EAAKnD,OAAS,IACdkD,GAAcqX,EACdX,EAAQ5T,OAAO,EAAGzC,EAAgBgX,IAEjCxa,EAAM,EAAGA,EAAMmD,EAAYnD,IAC5B+a,EAAQlB,EAAQ5T,OAAO,EAAGzC,GAC1BwC,EAAYM,EAAW9C,EAAgBxD,EAAMwD,KAC1CyC,OAAO/B,MAAMd,GACZ4C,EACA,GACFe,OAAOgU,GAEb,OAAO3X,IAEXqW,gBAAiB,WAAA,GACTlY,GAAWmN,KAAKnN,WAChBgZ,IAQJ,OAP4B,YAAxB7L,KAAKsF,iBACyB,IAA1BtF,KAAKoD,UAAU7R,OACfsa,EAAiBhZ,EACVA,EAAStB,OAAS,IACzBsa,EAAiBhZ,IAGlBgZ,GAEXhB,aAAc,WAAA,GACNhY,GAAWmN,KAAKnN,WAChBiZ,IAQJ,OAP4B,SAAxB9L,KAAKsF,iBACsB,IAAvBtF,KAAK0E,OAAOnT,OACZua,EAAcjZ,EACPA,EAAStB,OAAS,IACzBua,EAAcjZ,IAGfiZ,GAEXlC,iBAAkB,SAAUlV,EAAMnB,GAC1ByM,KAAKyG,cACDlT,IACAyM,KAAK4K,aAAerX,GAExBmB,EAAOsL,KAAKqK,eAAe3V,IAE/BsL,KAAK0J,MAAQ1J,KAAKwM,SAAS9X,GAC3BsL,KAAKyM,WACLzM,KAAK0M,UAAU1M,KAAK0J,OACpB1J,KAAK2M,OAAS3M,KAAK0J,MAAMnY,OACzByO,KAAK4M,eAAiB5M,KAAK2M,OAC3B3M,KAAKkJ,SAASlJ,KAAK0J,QAEvBhV,KAAM,SAAUV,GACZ,GAAIsV,GAAOtJ,IACX,OAAIhM,KAAUhE,EAQHsZ,EAAKI,OAPZ1J,KAAK6J,cAAgB7V,EAAMlD,MAAM,GACjCkP,KAAK4J,iBAAiB5V,GAClBoP,QAASpD,KAAKoD,UACdsB,KAAM1E,KAAK0E,OACX7R,SAAUmN,KAAKnN,aAJnBmN,IAUR8K,iBAAkB,SAAUhZ,EAAQmE,EAAQ5F,EAAawC,GAAvC,GAMVzB,GAAOsK,EAAWmR,EALlBtb,EAASsB,EAAStB,QAAU,EAC5BD,EAAM,EACNwb,KACAC,KACAtM,EAAa,CAEjB,IAAK3O,EAAOP,OAAZ,CAGA,GAAIyO,KAAKgH,gBAAkB,IAAMhH,KAAKsL,SAASxZ,EAAO,GAAImE,EAAQ5F,GAAc,CAE5E,IADA2P,KAAKgH,eAAiB,EACf1V,EAAMC,EAAQD,IACjBwb,EAAM7a,KAAK+N,KAAKoL,aAAatZ,EAAO,GAAIe,EAASvB,IAAM,IACvDyb,EAAQzb,GAAOA,CAEnBQ,GAAOyF,OAAO/B,MAAM1D,GAChB,EACAA,EAAOP,QACT8G,OAAOyU,GAAOzU,OAAOvG,IACvBR,EAAMC,EAEV,GAAIsB,EAAStB,OAGT,IAFAsb,EAAOzb,EAAQU,EAAOR,GACtBoK,EAAYtK,EAAMK,QAAQF,OAAS,EAC5BH,GAAO,CAWV,GAVIqP,GAAclP,IACdkP,EAAa,GAEbrP,EAAMK,QAAQiK,GAAWtL,OAASyC,EAAS4N,GAAYrQ,OACvD0B,EAAOyF,OAAOjG,EAAK,EAAG0O,KAAKoL,aAAaha,EAAOyB,EAAS4N,KACxDsM,EAAQzb,GAAOA,GAEnBA,GAAO,EACPmP,GAAc,EACdrP,EAAQU,EAAOR,GACXC,EAASkP,KAAgBrP,GAAS+L,GAAU0P,EAAMnR,EAAY,KAAOyB,GAAU/L,EAAOsK,EAAY,IAAK,CACvG,KAAO+E,EAAalP,EAAQkP,IACxB3O,EAAOyF,OAAOjG,EAAK,EAAG0O,KAAKoL,aAAayB,EAAMha,EAAS4N,KACvDsM,EAAQzb,GAAOA,EACfA,GAAO,CAEXF,GAAQU,EAAOR,GAEnBub,EAAOzb,EAGf,MAAO2b,KAEXC,qBAAsB,SAAUhb,EAAQib,GACpC,KAAOA,EAASzC,WAAWtS,SAASlG,EAAOT,OAAS0b,EAASnY,cAAe,OAAS9E,GACjF,IAAK,GAAIsB,GAAM,EAAGA,EAAM2b,EAASnY,cAAexD,IAC5CU,EAAS+B,EAAiB/B,EAGlC,MAAOib,EAAS1C,cAAcvY,EAAOT,OAAS0b,EAASnY,iBAAmB9E,GACtEgC,EAAS+B,EAAiB/B,EAE9B,OAAOA,IAEXkb,mBAAoB,SAAUlb,EAAQyB,EAAUwZ,GAC5C,GAAIE,GAAcF,EAASE,WAC3B,KAAK1Z,EACD,MAAOM,GAAiB/B,EAE5B,IAAIyB,EAASS,QAAUiZ,EAAc,EAEjC,IADAA,GAAe,EACRA,EAAc1Z,EAASS,SAAWlC,EAAOT,OAAS0b,EAAS1b,QAC9DS,EAASgO,KAAKgN,qBAAqBjZ,EAAiB/B,GAASib,GAC7DE,GAAe,CAKvB,OAFA1Z,GAASS,QAAUlC,EAAOT,OAC1BS,EAAOA,EAAOT,QAAUkC,EACjBzB,GAEXgZ,eAAgB,SAAU9P,GAAV,GAGRzH,GAEA0Z,EACA5b,EALAmD,EAAOwG,EAAQxG,KACfG,EAAU,EAEV7C,IAUJ,IAPAkJ,EAAQiS,YAAc,EACtBjS,EAAQqP,cAAgBrP,EAAQqP,kBAChCrP,EAAQsP,WAAatP,EAAQsP,eAC7BtP,EAAQpG,cAAgBoG,EAAQpG,eAAiB,EACjDoG,EAAQzG,WAAayG,EAAQzG,YAAc,EAC3CyG,EAAQ3J,OAAS2J,EAAQpG,cAAgBoG,EAAQzG,WACjDlD,EAAS2J,EAAQ3J,OACbmD,EAAKnD,SAAWA,EAChB,MAAOmD,EAEX,MAAO1C,EAAOT,OAASA,GACnBkC,EAAWiB,EAAKG,KACZpB,IACA0Z,EAAc1Z,EAASS,SAE3BlC,EAASgO,KAAKkN,mBAAmBlN,KAAKgN,qBAAqBhb,EAAQkJ,GAAUzH,EAAUyH,GACvFA,EAAQiS,YAAcA,CAE1B,OAAOnb,IAEX0T,SAAU,SAAUxK,EAASkS,GACzB,GAAI9D,GAAOtJ,KAAMyF,EAAY6D,EAAK7D,SAClC,OAAO1V,GAAE+Z,SAAS,SAAUuD,GACxB5H,EAAUC,SAASrO,IACf0O,QAAS,SAAUsB,GACfA,EAAWiC,EAAKc,OAAOkD,MAAMjG,GACzBiC,EAAKiE,oBAAoBlG,KAGzB+F,IACA/F,EAAW+F,EAAU/F,IAEzBgG,EAAStD,QAAQ1C,KAErBmG,MAAO,SAAUnG,EAAUoG,EAAQD,GAC/BH,EAASK,OAAOrG,GAChBiC,EAAKkE,MAAMnG,EAAUoG,EAAQD,KAElCtS,MACJ8O,UAAU2D,KAAK,WACdrE,EAAKD,QAAQ,mBAGrBuE,eAAgB,WACZ,GAAItE,GAAOtJ,IACX,OAAOsJ,GAAK5D,UACRhR,MACI4F,QAAS,iBACTgN,cACIuG,YAAavE,EAAK7D,UAAUO,UAC5B8H,SAAUxE,EAAK7D,UAAU3K,UAGlC,SAAUuM,GACT,MAAOiC,GAAKc,OAAOvX,SAASwU,MAGpC0G,WAAY,WACR,GAAIzE,GAAOtJ,IACX,OAAOsJ,GAAK5D,UACRhR,MACI4F,QAAS,aACTgN,cACIuG,YAAavE,EAAK7D,UAAUO,UAC5B8H,SAAUxE,EAAK7D,UAAU3K,UAGlC,SAAUuM,GACT,MAAOiC,GAAKc,OAAO4D,KAAK3G,MAGhC4G,iBAAkB,WACd,GAAI3E,GAAOtJ,IACX,OAAOsJ,GAAK5D,UACRhR,MACI4F,QAAS,mBACTgN,cACIuG,YAAavE,EAAK7D,UAAUO,UAC5B8H,SAAUxE,EAAK7D,UAAU3K,UAGlC,SAAUuM,GACT,MAAOiC,GAAKc,OAAOnK,WAAWoH,MAGtC6G,kBAAmB,SAAUC,GACzB,GAAI7E,GAAOtJ,IACX,OAAOsJ,GAAK5D,UACRhR,MACI4F,QAAS,oBACTgN,cACIuG,YAAavE,EAAK7D,UAAUO,UAC5B8H,SAAUxE,EAAK7D,UAAU3K,OACzBiN,oBAAqBoG,KAG9B,SAAU9G,GACT,MAAOiC,GAAKc,OAAO9D,YAAYe,MAGvC+G,aAAc,SAAUpU,GACpB,GAAIsP,GAAOtJ,IACX,OAAOsJ,GAAK5D,UACRhR,MACI4F,QAAS,eACTgN,cACIuG,YAAavE,EAAK7D,UAAUO,UAC5B8H,SAAUxE,EAAK7D,UAAU3K,OACzBkN,oBAAqBhO,KAG9B,SAAUqN,GACT,MAAOiC,GAAKc,OAAO7D,OAAOc,MAGlCgH,YAAa,WACT,GAAI/E,GAAOtJ,IACX,OAAOsJ,GAAK5D,UACRhR,MACI4F,QAAS,cACTgN,cAAgBuG,YAAavE,EAAK7D,UAAUO,aAEjD,SAAUqB,GACT,MAAOiC,GAAKc,OAAOhE,MAAMiB,MAGjCiH,eAAgB,WACZ,GAAIhF,GAAOtJ,IACX,OAAOsJ,GAAK5D,UAAWhR,MAAQ4F,QAAS,mBAAsB,SAAU+M,GACpE,MAAOiC,GAAKc,OAAO/D,SAASgB,MAGpCkH,cAAe,SAAUjH,GAAV,GACPgC,GAAOtJ,KACP+F,EAAU,SAAUuB,GACpB,MAAO,UAAUD,GACb,MAAOiC,GAAKc,OAAO3Y,QAAQ4V,EAAUC,KAE3CA,EACF,OAAOgC,GAAK5D,UACRhR,MACI4F,QAAS,gBACTgN,aAAcjQ,IACVwW,YAAavE,EAAK7D,UAAUO,UAC5B8H,SAAUxE,EAAK7D,UAAU3K,QAC1BwM,KAERvB,IAEP4D,QAAS,SAAUjV,GACXsL,KAAKuI,iBACLvI,KAAKiH,SACLjH,KAAK0J,MAAQ1J,KAAKwM,aAClBxM,KAAKuI,gBAAiB,EACtBvI,KAAKqJ,QAAQ9K,IAEjB,IAAIrD,GAAU0C,GAAW8I,GAAGiD,QAAQpZ,KAAKyP,KAAMtL,EAU/C,OATAwG,GAAU7D,IACNxE,SAAUmN,KAAKnN,WACfyS,aAActF,KAAKsF,eACnBlC,QAASpD,KAAKoD,UACdsB,KAAM1E,KAAK0E,QACZxJ,GACC8E,KAAKyG,cACLzG,KAAK4K,aAAe1P,GAEjBA,IAsWfgL,IAAgBL,OAAS,SAAU3K,GAC/BA,EAAUA,GAAWA,EAAQjJ,MAASyC,KAAMwG,GAAYA,CACxD,IAAIsT,GAAatT,MAAexG,EAAO8Z,EAAW9Z,IAElD,IADA8Z,EAAW9Z,KAAOA,IACZ8Z,YAAsBtI,MAAoBsI,YAAsB9c,IAAMgD,KAAKkJ,WAC7E,KAAUgG,OAAM,0EAEpB,OAAO4K,aAAsBtI,IAAkBsI,EAAa,GAAItI,IAAgBsI,IA8KhF7T,GACA8T,SAAU,sDACVC,eAAgB,kDAChBC,WAAY,4DACZC,SAAU,6DACVC,GAAI,6CACJC,IAAK,8CAwDLvS,GACA8R,YAAa,iBACbC,eAAgB,oBAChBV,eAAgB,oBAChBK,iBAAkB,sBAClBC,kBAAmB,uBACnBE,aAAc,kBACdG,cAAe,mBACfR,WAAY,iBAEZvR,GACAmJ,KAAM,SAAUzK,GAAV,GAGEkI,GACAsB,EACA7R,EACAyR,EACA1L,EANA0B,EAAU,yJA0Cd,OAzCAA,IAAW,qBACP8I,EAAUlI,EAAQkI,YAClBsB,EAAOxJ,EAAQwJ,SACf7R,EAAWqI,EAAQrI,aACnByR,EAA2C,SAAzBpJ,EAAQoK,aAC1B1M,EAAOsC,EAAQtC,UACdwK,EAAQ7R,QAAUmT,EAAKnT,UAAYsB,EAAStB,QAAUsB,EAAStB,QAAU+S,KAC1ElB,EAAUsB,EACVA,KACAJ,GAAkB,GAEjBlB,EAAQ7R,QAAWmT,EAAKnT,SACzB+S,GAAkB,GAElBlB,EAAQ7R,OACR+I,GAAWF,EAAiBgJ,EAAUkB,KAAkBzR,EAAe+F,GAChE/F,EAAStB,SAAW+S,IAC3BhK,GAAWjB,EAAaxG,GAAUyG,KAAK,MAE3CgB,GAAW,8EACPoK,EAAKnT,QAAU+S,GAAmBzR,EAAStB,OAAS,KACpD+I,GAAW,gBAEPA,GADAoK,EAAKnT,OACM6I,EAAiBsK,EAAMJ,EAAkBzR,KAAe+F,GAExDS,EAAaxG,GAAUyG,KAAK,KAE3CgB,GAAW,2EAEXY,EAAQL,QACRP,GAAW,SACXA,GAAW,IACXA,GAAWM,EAAiBM,EAAQL,OAAQK,EAAQ+K,WAAWnL,MAC/DR,GAAW,KAEXA,GAAW,UAAYY,EAAQ+K,WAAWnL,KAAO,IAE9B,GAAnBjI,EAAStB,QAAe6R,EAAQ7R,SAChC+I,GAAW,WAAajB,EAAaxG,GAAUyG,KAAK,KAAO,KAE/DgB,GAAW,4DAA8DY,EAAQ+K,WAAWD,QAAU,sGAC/F1L,EAAQe,QAAQ,MAAO,UAElCqK,SAAU,SAAUxK,GAChBA,EAAUA,KACV,IAAIZ,GAAU,sIAQd,OAPAA,IAAW,iBAAmBiC,EAAqBrB,EAAQZ,UAAYY,EAAQZ,SAAW,iBAC1FA,GAAW,iBAAmBU,EAAiB,kBAAmBE,EAAQoM,cAAc,GAAQ,kBAC5FpM,EAAQ+K,YAAc/K,EAAQ+K,WAAWD,UACzC9K,EAAQ6T,WAAahf,EAAEsH;AAAa2X,QAAS9T,EAAQ+K,WAAWD,SAAW9K,EAAQ6T,aAEvFzU,GAAW,eAAiBU,EAAiB,eAAgBE,EAAQ6T,YAAc,gBACnFzU,GAAW,kCAIfmC,EAAgB/K,GAAMgD,KAAKua,gBAAgB5X,QAC3C0I,KAAM,SAAU7E,GACZ,GAAIgU,GAAkBhU,CACtBA,GAAU8E,KAAK9E,QAAU7D,IAAO,KAAU2I,KAAK9E,QAASA,GACxDxJ,GAAMgD,KAAKua,gBAAgB1e,KAAKyP,KAAM9E,GAClCiD,GAAW+Q,EAAgBxJ,UAC3B1F,KAAK0F,SAAWwJ,EAAgBxJ,SACW,gBAA7BwJ,GAAgBxJ,SAC9B1F,KAAK9E,QAAQwK,UAAayJ,IAAKD,EAAgBxJ,UACvCwJ,EAAgBxJ,WACxB1F,KAAK9E,QAAQwK,SAAW1F,KAAK9E,QAAQyK,OAG7CyJ,MAAO,SAAUlU,EAASxK,GAGtB,MAFAwK,GAAQxG,KAAOwG,EAAQxG,SACvB3E,EAAEsH,QAAO,EAAM6D,EAAQxG,MAAQuR,WAAYjG,KAAK9E,QAAQ+K,aACjDvU,GAAMgD,KAAKua,gBAAgBvI,GAAG0I,MAAM7e,KAAKyP,KAAM9E,EAASxK,IAEnEwK,SACIyK,MACI0J,SAAU,OACVC,YAAa,WACb5e,KAAM,QAEVgV,UACI2J,SAAU,OACVC,YAAa,WACb5e,KAAM,QAEV6e,aAAc,SAAUrU,EAASxK,GAC7B,MAAO8L,GAAc9L,GAAMwK,EAASxK,KAG5CgV,SAAU,SAAUxK,GAChB,MAAOnL,GAAEyf,KAAKxP,KAAKoP,MAAMlU,EAAS,gBA0CtCwB,GACA0J,OACIhW,KAAMsB,GAAM0B,OAAO,sBAAwB,GAC3C+I,QAASzK,GAAM0B,OAAO,yBAA2B,GACjDqc,YAAa/d,GAAM0B,OAAO,wBAA0B,GACpD1C,KAAMgB,GAAM0B,OAAO,sBAAwB,IAE/CiT,UACIjW,KAAMsB,GAAM0B,OAAO,yBAA2B,GAC9Cqc,YAAa/d,GAAM0B,OAAO,wBAA0B,IAExDP,UACIzC,KAAMsB,GAAM0B,OAAO,yBAA2B,GAC9C+I,QAASzK,GAAM0B,OAAO,4BAA8B,GACpD8T,WAAYxV,GAAM0B,OAAO,gCAAkC,GAC3Dqc,YAAa/d,GAAM0B,OAAO,wBAA0B,GACpD8P,WAAYxR,GAAM0B,OAAO,+BAAiC,GAC1Dsc,UAAWhe,GAAM0B,OAAO,8BAAgC,GACxDuc,cAAeje,GAAM0B,OAAO,mCAAqC,GACjEwc,cAAele,GAAM0B,OAAO,kCAAoC,IAEpE4a,MACI5d,KAAMsB,GAAM0B,OAAO,qBAAuB,GAC1C+I,QAASzK,GAAM0B,OAAO,wBAA0B,GAChDY,MAAOtC,GAAM0B,OAAO,sBAAwB,GAC5Cyc,KAAMne,GAAM0B,OAAO,qBAAuB,GAC1Cqa,OAAQ/b,GAAM0B,OAAO,uBAAyB,GAC9C0c,MAAOpe,GAAM0B,OAAO,sBAAwB,GAC5C2c,cAAere,GAAM0B,OAAO,+BAAiC,GAC7D4c,aAActe,GAAM0B,OAAO,8BAAgC,GAC3Dqc,YAAa/d,GAAM0B,OAAO,4BAA8B,GACxDsc,UAAWhe,GAAM0B,OAAO,8BAAgC,IAE5D6M,YACI7P,KAAMsB,GAAM0B,OAAO,2BAA6B,GAChD+I,QAASzK,GAAM0B,OAAO,8BAAgC,GACtDqc,YAAa/d,GAAM0B,OAAO,wBAA0B,GACpD8T,WAAYxV,GAAM0B,OAAO,kCAAoC,GAC7D+T,iBAAkBzV,GAAM0B,OAAO,8BAAgC,GAC/D1C,KAAMgB,GAAM0B,OAAO,2BAA6B,IAEpDkT,aACIlW,KAAMsB,GAAM0B,OAAO,2BAA6B,GAChD+I,QAASzK,GAAM0B,OAAO,8BAAgC,GACtDqc,YAAa/d,GAAM0B,OAAO,wBAA0B,GACpD8T,WAAYxV,GAAM0B,OAAO,kCAAoC,GAC7D2U,oBAAqBrW,GAAM0B,OAAO,kCAAoC,GACtEuc,cAAeje,GAAM0B,OAAO,qCAAuC,GACnEwV,OAAQlX,GAAM0B,OAAO,6BAA+B,GACpD6c,cAAeve,GAAM0B,OAAO,2BAA6B,IAE7DmT,QACInW,KAAMsB,GAAM0B,OAAO,uBAAyB,GAC5C+I,QAASzK,GAAM0B,OAAO,0BAA4B,GAClDqc,YAAa/d,GAAM0B,OAAO,wBAA0B,GACpD8T,WAAYxV,GAAM0B,OAAO,8BAAgC,GACzD2U,oBAAqBrW,GAAM0B,OAAO,kCAAoC,GACtEuc,cAAeje,GAAM0B,OAAO,iCAAmC,GAC/D8c,iBAAkBxe,GAAM0B,OAAO,oCAAsC,GACrEwV,OAAQlX,GAAM0B,OAAO,yBAA2B,GAChD4U,oBAAqBtW,GAAM0B,OAAO,kCAAoC,IAE1E3B,SACIrB,KAAMsB,GAAM0B,OAAO,wBAA0B,GAC7C+I,QAASzK,GAAM0B,OAAO,2BAA6B,GACnD8T,WAAYxV,GAAM0B,OAAO,+BAAiC,GAC1D2U,oBAAqBrW,GAAM0B,OAAO,kCAAoC,GACtE4U,oBAAqBtW,GAAM0B,OAAO,kCAAoC,GACtEmU,gBAAiB7V,GAAM0B,OAAO,8BAAgC,GAC9D0U,oBAAqBpW,GAAM0B,OAAO,iCAAmC,KAGzEuJ,GACA,OACA,WACA,QACA,aACA,cACA,SACA,YAEAC,GAAiBlL,GAAMgD,KAAKyb,cAAc9Y,QAC1C0I,KAAM,SAAU7E,GACZxJ,GAAMgD,KAAKyb,cAAc5f,KAAKyP,KAAM9E,GACpC8E,KAAKoQ,QAAQlV,IAEjBkV,QAAS,SAAUlV,GAKf,IALK,GAGDmV,GACAC,EAHAhf,EAAM,EACNC,EAASoL,EAAkBpL,OAGxBD,EAAMC,EAAQD,IACjB+e,EAAa1T,EAAkBrL,GAC/Bgf,EAASpV,EAAQmV,GACbC,GAAUA,IAAWrS,KACrB+B,KAAKqQ,GAAcC,IAI/BhD,MAAO,SAAUiD,GACb,GAAIve,GAASN,GAAMgD,KAAKyb,cAAczJ,GAAG4G,MAAMiD,EAAIlV,QAAQ,kBAAmB,OAC9E,OAAO3J,IAAM0B,OAAO,wBAA4B,GAAMpB,IAE1Dwe,OAAQ,SAAUrW,GACd,GAAIsW,GAAQ/e,GAAM0B,OAAO,aAAe,GAAM+G,EAC9C,OAAIsW,KAEQC,YAAahf,GAAM0B,OAAO,wBAA0B,GAAMqd,GAC1DE,UAAWjf,GAAM0B,OAAO,sBAAwB,GAAMqd,KAG3D,MAEXlL,KAAM,SAAUpL,GAAV,GAEEoL,GACAnT,EACAJ,EAIKV,CAAT,KAPA6I,EAAOzI,GAAM0B,OAAO,kCAAkC,GAAM+G,GACxDoL,EAAOhK,EAAQ7J,GAAM0B,OAAO,aAAa,GAAM+G,IAE/CnI,GACAoR,WACAsB,SAEKpT,EAAM,EAAGA,EAAMiU,EAAKhU,OAAQD,IACjCc,EAAOmT,EAAKjU,GACwB,eAAhCc,EAAK,SAASuR,gBACT3R,EAAOoR,QAAQtR,OAGhBE,EAAO0S,KAAOjJ,EAAcrJ,GAF5BJ,EAAOoR,QAAU3H,EAAcrJ,GAM3C,OAAOJ,IAEX0C,KAAM,SAAUyF,GAAV,GAEEyW,GACA5e,EACA6e,EACAC,EACAC,EACKzf,CAAT,KANA6I,EAAOzI,GAAM0B,OAAO,kCAAkC,GAAM+G,GACxDyW,EAAQrV,EAAQ7J,GAAM0B,OAAO,iBAAiB,GAAM+G,IACpDnI,KACA6e,EAAgBnf,GAAM0B,OAAO,oBAC7B0d,EAAcpf,GAAM0B,OAAO,kBAC3B2d,EAAiBrf,GAAM0B,OAAO,qBACzB9B,EAAM,EAAGA,EAAMsf,EAAMrf,OAAQD,IAClCU,EAAOC,MACH+B,MAAO8c,EAAYF,EAAMtf,IACzB2C,SAAU8c,EAAeH,EAAMtf,IAC/B4C,QAASgE,SAAS2Y,EAAcD,EAAMtf,IAAO,KAGrD,OAAOU,IAEXgf,WAAY,SAAU7W,EAAM0I,GAAhB,GAEJ6B,GACA1S,EACKV,EACD2f,EACK7V,CAFb,KAHAjB,EAAOzI,GAAM0B,OAAO,mCAAmC,GAAM+G,GACzDuK,EAAOnJ,EAAQ7J,GAAM0B,OAAO,OAAO,GAAM+G,IACzCnI,KACKV,EAAM,EAAGA,EAAMoT,EAAKnT,OAAQD,IAAO,CACpC2f,IACJ,KAAS7V,IAAOyH,GACZoO,EAAI7V,GAAOyH,EAAQzH,GAAKsJ,EAAKpT,GAEjCU,GAAOC,KAAKgf,GAEhB,MAAOjf,IAEXa,SAAU,SAAUsH,GAChB,MAAO6F,MAAKgR,WAAW7W,EAAMuC,EAAoB7J,WAErDmb,KAAM,SAAU7T,GACZ,MAAO6F,MAAKgR,WAAW7W,EAAMuC,EAAoBsR,OAErD1H,YAAa,SAAUnM,GACnB,MAAO6F,MAAKgR,WAAW7W,EAAMuC,EAAoB4J,cAErDC,OAAQ,SAAUpM,GACd,MAAO6F,MAAKgR,WAAW7W,EAAMuC,EAAoB6J,SAErDtG,WAAY,SAAU9F,GAClB,MAAO6F,MAAKgR,WAAW7W,EAAMuC,EAAoBuD,aAErDmG,MAAO,SAAUjM,GACb,MAAO6F,MAAKgR,WAAW7W,EAAMuC,EAAoB0J,QAErDC,SAAU,SAAUlM,GAChB,MAAO6F,MAAKgR,WAAW7W,EAAMuC,EAAoB2J,WAErD5U,QAAS,SAAU0I,GACf,MAAO6F,MAAKgR,WAAW7W,EAAMuC,EAAoBjL,YAGzD4F,IAAO,EAAM3F,GAAMgD,MACfwR,gBAAiBA,GACjBzJ,cAAeA,EACfG,eAAgBA,GAChBkD,iBAAkBA,GAClBoR,YAAcC,KAAM1U,GACpB2U,SAAWD,KAAMvU,MAEjBC,GAAW,SAAUwU,EAAajhB,GAClC,IAAKihB,EACD,MAAO,KAEX,KAAK,GAAI/f,GAAM,EAAGC,EAAS8f,EAAY9f,OAAQD,EAAMC,EAAQD,IACzD,GAAI+f,EAAY/f,GAAK+B,QAAUjD,EAC3B,MAAOihB,GAAY/f,EAG3B,OAAO,OAEPwL,GAAa,SAAUuU,EAAajhB,GAAvB,GAEJkB,GAASC,EADdS,IACJ,KAASV,EAAM,EAAGC,EAAS8f,EAAY9f,OAAQD,EAAMC,EAAQD,IACrD+f,EAAY/f,GAAK+B,QAAUjD,GAC3B4B,EAAOC,KAAKof,EAAY/f,GAGhC,OAAOU,IAEXN,GAAM+L,GAAG6T,mBAAqB3T,GAAOtG,QACjC0I,KAAM,SAAU/C,EAAS9B,GACrB,GAAIoO,GAAOtJ,IACXrC,IAAO+I,GAAG3G,KAAKxP,KAAK+Y,EAAMtM,EAAS9B,GACnCoO,EAAKtM,QAAQuU,SAAS,mBACtBjI,EAAKkF,WAAa9c,GAAMgD,KAAKwR,gBAAgBL,OAAO3K,EAAQsT,YAC5DlF,EAAKkI,gBAAkBzhB,EAAEqX,MAAMkC,EAAKmI,QAASnI,GAC7CA,EAAKkF,WAAWlY,MAAM8H,GAAQkL,EAAKkI,iBAC9BtW,EAAQwW,WACTpI,EAAKpO,QAAQwW,SAAW,aAAehgB,GAAMigB,GAAK,oDAAsDrI,EAAKpO,QAAQ0W,QAAU,uGAAyG,IAAM,UAElPtI,EAAKoI,SAAWhgB,GAAMggB,SAASpI,EAAKpO,QAAQwW,UAC5CpI,EAAKuI,cAAgBngB,GAAMggB,SAASpI,EAAKpO,QAAQ2W,eACjDvI,EAAKwI,YACLxI,EAAKtM,QAAQ+U,GAAG,QAAUrT,GAAI,oBAAqB,SAAUyK,GAAV,GAC3CnT,GAASjG,EAAEoZ,EAAEnT,QACb5F,EAAO4F,EAAOgc,QAAQ,IAAMtgB,GAAMugB,KAAK,QAAU,KAAKA,KAAKvgB,GAAMugB,KAAK,QACrE7hB,KAGD4F,EAAOkc,SAAS,aAChB5I,EAAK6I,OAAO/hB,GACLkZ,EAAKpO,QAAQkX,UAAYpc,EAAO,KAAOmT,EAAEkJ,eAChD/I,EAAK1Q,MACDvF,MAAOjD,EACP2I,IAAK/C,EAAOsc,KAAK,oBAAoB,GAAK,OAAS,YAI3DpX,EAAQqX,YAAcrX,EAAQkX,YAC9B9I,EAAKkJ,UAAY,GAAI/U,IAAGgV,eAAenJ,EAAKtM,SACxC0V,SAAUpJ,EAAKpO,QAAQwX,SAASF,UAChC3X,OAAQ,uBACR0X,WAAYrX,EAAQqX,WACpBH,SAAUlX,EAAQkX,SAClB5D,WAAYlF,EAAKkF,cAGzBlF,EAAKmI,WAETvW,SACI9K,KAAM,qBACNshB,SAAU,KACVa,YAAY,EACZH,UAAU,EACVP,cAAe,qCACfc,QAAS,UACTf,SAAS,EACTc,UAAYE,MAAO,qBAEvBC,cAAe,SAAUrE,GACrBxO,KAAKwO,WAAWsE,OAAO1U,GAAQ4B,KAAKwR,iBACpCxR,KAAKwO,WAAaxO,KAAK9E,QAAQsT,WAAaA,EACxCxO,KAAKwS,WACLxS,KAAKwS,UAAUK,cAAcrE,GAEjCA,EAAWlY,MAAM8H,GAAQ4B,KAAKwR,iBAC9BxR,KAAKyR,WAETK,UAAW,WACP,GAAIxI,GAAOtJ,IACPsJ,GAAKpO,QAAQ0W,UACb5R,KAAKoS,SAAWpS,KAAKhD,QAAQ+V,eACzBC,YAAahT,KAAK9E,QAAQ8X,YAC1BC,KAAM3J,EAAKpO,QAAQ+X,KACnBC,OAAQ,OACRC,MAAO,SAAUhK,GACbA,EAAEiK,KAAKC,QAAQC,QAEnBC,OAAQ,SAAUpK,GACd,GAAI/Y,GAAO+Y,EAAEiK,KAAKnB,KAAKvgB,GAAMugB,KAAK,QAClB,YAAZ9I,EAAEqK,OACFlK,EAAKmK,IAAIrjB,GACU,UAAZ+Y,EAAEqK,OACTlK,EAAK6I,OAAO/hB,GACO,QAAZ+Y,EAAEqK,QACTlK,EAAKoK,KAAKtjB,EAAM+Y,EAAEwK,aAG3Bjf,KAAK,mBAGhBkf,SAAU,SAAUxjB,EAAMqS,GACtB,GAAInR,GAAKC,EAAQ6D,IACjB,KAAK9D,EAAM,EAAGC,EAASkR,EAAMlR,OAAQD,EAAMC,EAAQD,IAC/C,GAAImB,EAAQgQ,EAAMnR,MAAUlB,EAAM,CAC9BgF,EAAQ9D,CACR,OAGR,MAAO8D,IAEXye,OAAQ,SAAUnf,GACd,MAAqB,QAAdA,EAAKhE,MAAkBgE,EAAKxE,SAEvC4jB,SAAU,SAAUpf,GAAV,GAQF+N,GACArS,EARA2jB,EAAyB,GAAbrf,EAAKhE,MAAa,cAAgBgE,IAAQsL,KAAK6T,OAAOnf,EACtE,OAAIqf,GACgC,aAAzB/T,KAAK9E,QAAQyX,QAEK,aAAzB3S,KAAK9E,QAAQyX,QACNoB,GAEPtR,EAAQzC,KAAKwO,WAAWxO,KAAK9E,QAAQyX,WACrCviB,EAAOsE,EAAKyS,kBAAoBzS,EAAKwS,aACrClH,KAAK4T,SAASxjB,EAAMqS,SAGxBA,EAAQzC,KAAKwO,WAAoC,YAAzBxO,KAAK9E,QAAQyX,QAAwB,OAAS,eAClE3S,KAAK4T,SAASxjB,EAAMqS,UAK5BgR,IAAK,SAAUrjB,GAAV,GAEGwG,GAAGmD,EADH0I,EAAQzC,KAAKwO,WAAWxO,KAAK9E,QAAQyX,UAGzC,KADAviB,EAAOL,EAAEikB,QAAQ5jB,GAAQA,EAAKU,MAAM,IAAMV,GACrCwG,EAAI,EAAGmD,EAAI3J,EAAKmB,OAAQqF,EAAImD,EAAGnD,IAC5BoJ,KAAK4T,SAASxjB,EAAKwG,GAAI6L,UACvBrS,EAAKmH,OAAOX,EAAG,GACfA,GAAK,EACLmD,GAAK,EAGT3J,GAAKmB,SACLkR,EAAQA,EAAMpK,OAAOjI,GACrB4P,KAAKwO,WAAWxO,KAAK9E,QAAQyX,SAASlQ,KAG9CiR,KAAM,SAAUtjB,EAAMgF,GAAhB,GACEqN,GAAQzC,KAAKwO,WAAWxO,KAAK9E,QAAQyX,WACrCrhB,EAAM0O,KAAK4T,SAASxjB,EAAMqS,EAC1BnR,QACAlB,EAAOqS,EAAMlL,OAAOjG,EAAK,GAAG,GAC5BmR,EAAMlL,OAAOnC,EAAO,EAAGhF,GACvB4P,KAAKwO,WAAWxO,KAAK9E,QAAQyX,SAASlQ,KAG9C0P,OAAQ,SAAU/hB,GAAV,GACAqS,GAAQzC,KAAKwO,WAAWxO,KAAK9E,QAAQyX,WACrCrhB,EAAM0O,KAAK4T,SAASxjB,EAAMqS,GAC1BwR,EAAkBjU,KAAKwO,WAAW5V,OAClCiC,EAASmF,KAAKwO,WAAW3T,QACzBvJ,QACIuJ,IACAA,EAAOE,QAAU+B,GAAWjC,EAAOE,QAAS3K,GAC5C4P,KAAKwO,WAAWxK,QAAQjJ,QAAUF,EAAOE,QACpCF,EAAOE,QAAQxJ,SAChByO,KAAKwO,WAAWxK,QAAU,OAG9BiQ,IACAA,EAAkBnX,GAAWmX,EAAiB7jB,GAC9C4P,KAAKwO,WAAW0F,MAAQD,GAE5BxR,EAAMlL,OAAOjG,EAAK,GAClB0O,KAAKwO,WAAWxO,KAAK9E,QAAQyX,SAASlQ,KAG9C7J,KAAM,SAAUqL,GAAV,GACEmO,GAAWpS,KAAK9E,QAAQkX,SACxB+B,EAAc/B,KAAa,GAAQA,EAAS+B,YAC5CC,EAAWD,GAA4B,QAAblQ,EAAKlL,IAC/BsY,EAAcrR,KAAKwO,WAAW5V,WAC9B5G,EAAS8K,GAAWuU,EAAapN,EAAK5Q,MACtC+gB,IAAY/C,EAAY9f,SAAWS,EAAOT,SAC1C0S,EAAO,MAEPA,GACAjS,EAAOC,KAAKgS,GAEhBjE,KAAKwO,WAAW5V,KAAK5G,IAEzByf,QAAS,WAAA,GAKD2B,GAJAiB,EAAO,GACP5R,EAAQzC,KAAKwO,WAAWxO,KAAK9E,QAAQyX,WACrCphB,EAASkR,EAAMlR,OACfD,EAAM,CAEV,IAAIC,EACA,KAAOD,EAAMC,EAAQD,IACjB8hB,EAAO3Q,EAAMnR,GACb8hB,EAAOA,EAAKhjB,OAASJ,GAAcI,KAAMgjB,GAASA,EAClDiB,GAAQrU,KAAK0R,SAASra,IAASid,SAAUtU,KAAKuU,UAAUnB,EAAKhjB,OAASgjB,QAG1EiB,GAAOrU,KAAK6R,cAAc7R,KAAK9E,QAAQwX,SAASE,MAEpD5S,MAAKhD,QAAQqX,KAAKA,IAEtBvO,QAAS,WACLnI,GAAO+I,GAAGZ,QAAQvV,KAAKyP,MACvBA,KAAKwO,WAAWsE,OAAO1U,GAAQ4B,KAAKwR,iBACpCxR,KAAKhD,QAAQwX,IAAI9V,IACbsB,KAAKoS,UACLpS,KAAKoS,SAAStM,UAEd9F,KAAKwS,WACLxS,KAAKwS,UAAU1M,UAEnB9F,KAAKhD,QAAU,KACfgD,KAAKwR,gBAAkB,MAE3B+C,UAAW,SAAUnkB,GAAV,GACHihB,GAAcrR,KAAKwO,WAAW5V,OAC9BqL,EAAOpH,GAASwU,EAAa5e,EAAQrC,IACrCqkB,EAAO,EAIX,OAHIxQ,KACAwQ,EAAO,YAAcxQ,EAAKlL,KAEvB0b,KAGX1X,GAAYY,GAAOtG,QACnB0I,KAAM,SAAU/C,EAAS9B,GAAnB,GAEEwZ,GACAC,EAFArL,EAAOtJ,IAGXrC,IAAO+I,GAAG3G,KAAKxP,KAAK+Y,EAAMtM,EAAS9B,GACnCoO,EAAKsL,cACLtL,EAAKuL,oBACLvL,EAAKwL,WACLxL,EAAKyL,gBACLzL,EAAK0L,eAAiBN,EAAgB,GAAItX,IAC1CkM,EAAK2L,YAAcN,EAAa,GAAItX,IACpCiM,EAAK4L,gBAAkB,GAAI5X,IAC3BgM,EAAK6L,aACL7L,EAAK8L,cAAc3B,IAAInK,EAAK+L,YAAYtD,GAAG,QAAS,cAAe,WAAA,GAI3DuD,GAWArb,EACAgT,EACAsI,EAhBAC,EAASzlB,EAAEiQ,MACXyV,EAAUf,EACVlB,EAAS,eAET1c,EAAO0e,EAAOvD,KAAKvgB,GAAMugB,KAAK,SAC9ByD,GACAtjB,KAAM,UACN0E,KAAM/G,EAAEmC,UAAU4E,GAElB0e,GAAOG,SAASC,GAAG,QACnBH,EAAUd,EACVnB,EAAS,YACTkC,EAAUtjB,KAAO,QAEjB6H,EAAWub,EAAOtD,SAASlT,IAC3BiO,EAAWwI,EAAQxI,SAASnW,GAC5Bye,EAAUtI,EAAShT,WAAajK,EACpCslB,EAAYrb,EAAW8E,GAAiBD,GACxC4W,EAAUG,eAAiB5I,EAAS6I,YAAc7I,EAASzb,SACvD8X,EAAKD,QAAQiM,EAAWI,KAG5BD,EAAQxI,SAASnW,GAAMmD,UAAYA,EACnCub,EAAOO,YAAY/W,IAAiB/E,GAAU8b,YAAY9W,GAAiBhF,IACtEA,GAAYsb,EACbjM,EAAKkF,WAAWgF,GAAQkC,EAAU5e,MAElCwS,EAAKmI,aAGbnI,EAAK0M,cACD1M,EAAKpO,QAAQ+a,UACb3M,EAAKkF,WAAW0H,QAEpBxkB,GAAMykB,OAAO7M,IAEjB8M,QACIxX,GACAC,GACAC,GACAC,IAEJ7D,SACI9K,KAAM,YACN6lB,UAAU,EACVI,aAAa,EACb9D,YAAY,EACZH,UAAU,EACVkE,OAAQ,KACRC,YAAa,IACbC,aAAc,GACdC,qBAAsB,KACtBC,kBAAmB,KACnBC,iBAAkB,KAClBC,kBAAmB,KACnBC,iBAAkB,KAClBnE,UACIoE,cAAe,wBACfC,aAAc,0BACdC,UAAW,0BAGnB7B,WAAY,WAAA,GACJ8B,GAAiBjX,KAAK9E,QAAQub,qBAC9BS,EAAclX,KAAK9E,QAAQwb,kBAC3BS,EAAenX,KAAK9E,QAAQyb,iBAC5BC,EAAoB5W,KAAK9E,QAAQ0b,kBACjCC,EAAmB7W,KAAK9E,QAAQ2b,gBACpC7W,MAAKgV,eAAetD,SAAWhgB,GAAMggB,SAASuF,GAAkB/X,IAAmBkY,eAAgBH,IACnGjX,KAAKkV,gBAAgBiC,aAAezlB,GAAMggB,SAASyF,GAAgB9X,IAAqB+X,eAAgBD,IACxGnX,KAAKkV,gBAAgB0B,kBAAoBllB,GAAMggB,SAASkF,GAAqBzX,IAAsBiY,eAAgBR,IACnH5W,KAAKkV,gBAAgB2B,iBAAmBnlB,GAAMggB,SAASmF,GAAoBzX,IAAqBgY,eAAgBP,IAChH7W,KAAKiV,YAAYvD,SAAWhgB,GAAMggB,SAASwF,GAAehY,IAAmBkY,eAAgBF,KAEjGrC,kBAAmB,WACf,GAAI2B,GAAexW,KAAK9E,QAAQsb,YAC5BA,IACAzmB,EAAEymB,GAAca,uBAAuB,gBAAiBrX,KAAKwO,aAGrE8I,kBAAmB,SAAUta,GAEzB,MADAA,GAAUjN,EAAEiN,GACLgD,KAAKuX,SAASva,EAAQ5H,QAAS4H,EAAQ2Y,OAAO,MAAMvgB,UAE/DmiB,SAAU,SAAU1f,EAAaD,GAAvB,GAIFN,GAHAkgB,EAAiBxX,KAAKkV,gBACtBuC,EAAaD,EAAejN,cAAc1S,GAAe,GACzD6f,EAAUF,EAAehN,WAAW5S,GAAY,EAEpD,OAAK6f,IAAeC,GAGpBpgB,EAAYogB,EAAQtiB,MAAQoiB,EAAejV,UAAYkV,EAAWriB,OAE9DuiB,YAAaF,EAAWrmB,MACxBwmB,SAAUF,EAAQtmB,MAClBlB,QAASunB,EAAWvnB,SAAWwnB,EAAQxnB,QACvCuD,SAAUuM,KAAKwO,WAAWqJ,OAAOvgB,KAP1B,MAUfub,cAAe,SAAUrE,GACrBxO,KAAK9E,QAAQsT,WAAaA,EAC1BxO,KAAK4U,cACD5U,KAAK8X,gBACL9X,KAAK8X,eAAejF,cAAcrE,GAElCxO,KAAK+X,YACL/X,KAAK+X,WAAWlF,cAAcrE,GAE9BxO,KAAKgY,eACLhY,KAAKgY,cAAcnF,cAAcrE,GAErCxO,KAAK6U,oBACD7U,KAAK9E,QAAQ+a,UACbzH,EAAW0H,SAGnB+B,WAAY,SAAU/c,GAClByC,GAAO+I,GAAGuR,WAAW1nB,KAAKyP,KAAM9E,GAChC8E,KAAKmV,cAETrP,QAAS,WACLnI,GAAO+I,GAAGZ,QAAQvV,KAAKyP,MACvBkY,aAAalY,KAAKmY,uBAEtBvD,YAAa,WAAA,GACLtL,GAAOtJ,KACPwO,EAAalF,EAAKpO,QAAQsT,UAC9BA,GAAaze,EAAEikB,QAAQxF,IAAgB9Z,KAAM8Z,GAAeA,EACxDlF,EAAKkF,YAAcxO,KAAKwR,gBACxBlI,EAAKkF,WAAWsE,OAAO1U,GAAQkL,EAAKkI,iBAAiBsB,OAAOvU,GAAY+K,EAAK8O,oBAAoBtF,OAAOxU,GAAUgL,EAAK+O,kBAAkBvF,OAAOzU,GAAOiL,EAAKgP,gBAE5JhP,EAAKkI,gBAAkBzhB,EAAEqX,MAAMkC,EAAKmI,QAASnI,GAC7CA,EAAK+O,iBAAmBtoB,EAAEqX,MAAMkC,EAAKiP,cAAejP,GACpDA,EAAK8O,mBAAqBroB,EAAEqX,MAAMkC,EAAKkP,YAAalP,GACpDA,EAAKgP,cAAgBvoB,EAAEqX,MAAMkC,EAAKmP,OAAQnP,IAE9CA,EAAKkF,WAAa9c,GAAMgD,KAAKwR,gBAAgBL,OAAO2I,GAAYkK,KAAKta,GAAQkL,EAAKkI,iBAAiBkH,KAAKpa,GAAUgL,EAAK+O,kBAAkBK,KAAKna,GAAY+K,EAAK8O,oBAAoBM,KAAKra,GAAOiL,EAAKgP,gBAExMG,OAAQ,WACJzY,KAAK2Y,WAAU,IAEnBJ,cAAe,WACXvY,KAAK2Y,WAAU,IAEnBH,YAAa,WACTxY,KAAKgV,eAAe4D,QACpB5Y,KAAKiV,YAAY2D,SAErB9D,SAAU,WACN,GAAIwB,GAAStW,KAAK9E,QAAQob,MAC1BtW,MAAK6Y,QAAU7Y,KAAKhD,QAAQuU,SAAS,oBACjC+E,GACAtW,KAAK6Y,QAAQC,IAAI,SAAUxC,IAGnCyC,eAAgB,WACZ/Y,KAAK8W,cAAgB/mB,EAAE0O,IAAK8S,SAAS,gDACrCvR,KAAK8X,eAAiB9X,KAAKgZ,qBAAqBhZ,KAAK8W,eACjDnE,QAAS,WACTD,UAAYE,MAAO5S,KAAK9E,QAAQwX,SAASoE,kBAGjDkC,qBAAsB,SAAUhc,EAAS9B,GAAnB,GACdwW,GAAW,4CAA8ChgB,GAAMigB,GAAK,mCACpES,EAAWlX,EAAQkX,SACnB6G,EAAQ,EAgBZ,OAfI7G,KACA6G,GAAS,yBACTA,GAAS,mDACTA,GAAS,QAET/d,EAAQqX,YAAcH,KACtB6G,GAAS,sEAETjZ,KAAK9E,QAAQmb,cACb4C,GAAS,2DAETA,IACAvH,GAAY,iCAAmCuH,EAAQ,WAE3DvH,GAAY,UACL,GAAIhgB,IAAM+L,GAAG6T,mBAAmBtU,EAASjN,EAAEsH,QAC9Cqa,SAAUA,EACVG,cAAe,uCACfD,QAAS5R,KAAK9E,QAAQmb,YACtB7H,WAAYxO,KAAKwO,YAClBtT,KAEPge,oBAAqB,WACjBlZ,KAAKgY,cAAgBhY,KAAKgZ,qBAAqBhZ,KAAK+W,cAChD/D,YAAahT,KAAKgX,UAClBrE,QAAS,UACTJ,WAAYvS,KAAK9E,QAAQqX,WACzBH,SAAUpS,KAAK9E,QAAQkX,SACvBM,UACIE,MAAO5S,KAAK9E,QAAQwX,SAASqE,aAC7BvE,UAAWxS,KAAK9E,QAAQwX,SAASF,aAGzCxS,KAAK+X,WAAa/X,KAAKgZ,qBAAqBhZ,KAAKgX,WAC7ChE,YAAahT,KAAK+W,aAClBpE,QAAS,OACTJ,WAAYvS,KAAK9E,QAAQqX,WACzBH,SAAUpS,KAAK9E,QAAQkX,SACvBM,UACIE,MAAO5S,KAAK9E,QAAQwX,SAASsE,UAC7BxE,UAAWxS,KAAK9E,QAAQwX,SAASF,cAI7CuC,cAAe,WAAA,GACPzL,GAAOtJ,KACPmZ,EAAcppB,EAAEuP,IAChB8Z,EAAgBD,EAAY7G,KAAK,uBACjC+G,EAAiBF,EAAY7G,KAAK,kBAClCgH,EAAcvpB,EAAE0O,IAAK8S,SAAS,kBAClCjI,GAAKyP,iBACLzP,EAAKyN,aAAehnB,EAAE0O,IAAK8S,SAAS,+CACpCjI,EAAK0N,UAAYjnB,EAAE0O,IAAK8S,SAAS,4CACjCjI,EAAK8L,cAAgBrlB,EAAE,sCAAsCwpB,KAAK,iCAClEjQ,EAAK8L,cAAcO,SAASmD,IAAI,gBAAiBpnB,GAAM8nB,QAAQC,aAC/DnQ,EAAK+L,WAAatlB,EAAE,wCACpBuZ,EAAKoQ,QAAU3pB,EAAE,kCACjBqpB,EAAcO,OAAOrQ,EAAKwN,eAC1BsC,EAAcO,OAAOrQ,EAAK0N,WAC1BoC,EAAcO,OAAOrQ,EAAK+L,YAC1BiE,EAAYK,OAAOrQ,EAAK8L,cAAcO,UACtC2D,EAAYK,OAAOrQ,EAAKoQ,SACxBL,EAAeM,OAAOrQ,EAAKyN,cAC3BsC,EAAeM,OAAOL,GACtBhQ,EAAKuP,QAAQc,OAAOR,GACpB7P,EAAKsQ,kBAAoB,GAAIloB,IAAMmoB,IAAIC,KAAKxQ,EAAK8L,cAAc,IAC/D9L,EAAKyQ,eAAiB,GAAIroB,IAAMmoB,IAAIC,KAAKxQ,EAAK+L,WAAW,IACzD/L,EAAK0Q,YAAc,GAAItoB,IAAMmoB,IAAIC,KAAKxQ,EAAKoQ,QAAQ,IACnDpQ,EAAK4P,uBAETP,UAAW,SAAUsB,GACjBvoB,GAAM+L,GAAGyc,SAASla,KAAK6Y,QAASoB,IAEpCE,QAAS,WACDna,KAAK0Z,QAAQ,GAAGU,aAChBpa,KAAKqa,oBACLra,KAAKsa,qBACLta,KAAKua,mBACLva,KAAKwa,oBACLxa,KAAKya,wBAGbA,oBAAqB,WACjB,GAAIC,GAAc1a,KAAKoV,cAAc5jB,SAAS,QACzCE,IAAM8nB,QAAQmB,QAAQC,UAG3B1C,aAAalY,KAAKmY,sBAClBuC,EAAY5B,IAAI,eAAgB,QAChC9Y,KAAKmY,qBAAuB0C,WAAW,WACnCH,EAAY5B,IAAI,eAAgB,QAGxCuB,kBAAmB,WAAA,GACXhF,GAAarV,KAAKqV,WAClByF,EAAazF,EAAWM,OAAO,uBAAuBoF,MAAMvc,IAC5Duc,EACIllB,KAAKC,IAAI+H,GAAWmC,KAAK8W,eAAgBjZ,GAAWmC,KAAKgX,WACjE+D,GAAQllB,KAAKC,IAAIuf,EAAW7jB,SAAS,SAASupB,QAASA,GACvDD,EAAWC,MAAMA,IAErBT,mBAAoB,WAAA,GACZU,GAAsBhb,KAAK8W,cAAcR,OAAO9X,IAAM8X,SACtD2E,EAAqBjb,KAAK+W,aAAaT,OAAO9X,IAAM8X,SACpD4E,EAAkBlb,KAAKgX,UAAUV,OAAO9X,IAAM2c,cAC9CC,EAAgBpb,KAAKoV,cAAckB,OAAO9X,IAAM2c,cAChDE,EAAUH,EAAkBlb,KAAKgX,UAAUV,SAC3CgF,EAAiBL,EAAqBD,EAAsBC,EAAqBD,EACjFO,EAAkBH,EAAgBF,EAAkBE,EAAgBF,CACxElb,MAAK8W,cAAcR,OAAOgF,GAC1Btb,KAAK+W,aAAaT,OAAOgF,GACzBtb,KAAKgX,UAAUV,OAAOiF,EAAkBF,GACxCrb,KAAKoV,cAAckB,OAAOiF,IAE9BhB,iBAAkB,WAAA,GACViB,GAAexb,KAAK0Z,QAAQpH,KAAK,SACjCoI,EAAc1a,KAAKoV,cAAc5jB,SAAS,SAC1C+Q,EAAYiZ,EAAahqB,SAAS,YAAYA,WAAWD,OACzDkqB,EAAkBlZ,EAAYvC,KAAK9E,QAAQqb,YAC3CmF,EAAW7lB,KAAK8lB,KAAKF,EAAkBzb,KAAK0Z,QAAQqB,QAAU,IAC9DW,GAAW,MACXA,EAAW,KAEfF,EAAa/H,IAAIiH,GAAa5B,IAAI,QAAS4C,EAAW,KACtD1b,KAAK4b,cAAclB,IAEvBF,kBAAmB,WAAA,GACXlR,GAAOtJ,KACP0Z,EAAUpQ,EAAKoQ,QACfrE,EAAa/L,EAAK+L,WAClB8F,EAAc7R,EAAKuP,QAAQsC,cAC3B1B,EAAY/nB,GAAM8nB,QAAQC,YAC1BoC,EAAgBnC,EAAQ,GAAGoC,eAAiBpC,EAAQ,GAAGqC,aACvDzF,EAAShN,EAAKpO,QAAQob,MAC1B,IAAIhN,EAAKuP,QAAQjD,GAAG,YAAa,CAC7B,IAAKuF,IAAgB7E,EAMjB,MALIuF,KACApC,EAAY,GAEhBC,EAAQpD,OAAO,QACfjB,EAAWiB,OAAOoD,EAAQpD,SAAWmD,GACrC,CAEJ0B,IAAepd,GAAYuL,EAAKyN,cAChCoE,GAAepd,GAAYuL,EAAK8L,cAAcO,UAC1CwF,GAA2B,EAAZ1B,IACf0B,EAA0B,EAAZ1B,EAAgB,EACzBoC,IACDV,GAAe1B,IAGvBC,EAAQpD,OAAO6E,GACXU,IACApC,EAAY,GAEhBpE,EAAWiB,OAAO6E,EAAc1B,KAGxCmC,cAAe,SAAUlB,GAAV,GACPpR,GAAOtJ,KACPgc,EAAOtB,EAAYlpB,SAAS,SAASA,SAAS,UAAUA,SAAS,SACjE8X,GAAK2S,WAAajsB,IAClBsZ,EAAK2S,SAAWD,EAAK/J,KAAK,YAE9B+J,EAAK/J,KAAK,UAAW,GACrBiG,aAAa5O,EAAK4S,gBAClB5S,EAAK4S,eAAiBrB,WAAW,WAC7BmB,EAAK/J,KAAK,UAAW3I,EAAK2S,UAC1B3S,EAAK2S,SAAWjsB,KAGxBmsB,cAAe,SAAU/pB,GAAV,GACPJ,MACAwc,EAAaxO,KAAKwO,WAClB3b,EAAW2b,EAAW3b,WACtBupB,EAAavpB,EAAStB,OAAS,GAAKsB,EAAS,IAAMA,EAAS,GAAGnC,IAMnE,OALI8d,GAAWlJ,iBAAmBlT,IACI,IAA9Boc,EAAWpc,KAAQb,QAAgB6qB,KACnCpqB,EAASa,GAGVb,GAEXyQ,MAAO,WACH,UAEJgP,QAAS,WAAA,GAgCG4K,GA/BJ/S,EAAOtJ,KACPwO,EAAalF,EAAKkF,WAClBjJ,EAAOiJ,EAAWjJ,OAClBnC,GAAWmC,EAAKnC,aAAetR,WAC/B4S,GAAQa,EAAKb,UAAY5S,WACzB4iB,EAAgBpL,EAAK0L,eACrBL,EAAarL,EAAK2L,YAClBqH,KACA/a,IACA+H,GAAKD,QAAQzK,IAAe4U,OAAQ,aAGxCkB,EAAc7hB,SAAWyW,EAAK6S,cAAc3c,IAC5CmV,EAAW9hB,SAAWyW,EAAK6S,cAAc5c,IACzC+J,EAAKsQ,kBAAkB2C,OAAO7H,EAAc8H,MAAMpZ,IAClDkG,EAAKyQ,eAAewC,OAAO5H,EAAW6H,MAAM9X,IAC5C4X,GACIvP,QAAS2H,EAAc+H,SACvB5pB,SAAU6hB,EAAc7hB,SACxBoa,SAAUyH,EAAczH,UAE5B1L,GACIwL,QAAS4H,EAAW8H,SACpB5pB,SAAU8hB,EAAW9hB,SACrBoa,SAAU0H,EAAW1H,UAEzB3D,EAAK0Q,YAAYuC,OAAOjT,EAAK4L,gBAAgBsH,MAAMhO,EAAWqJ,OAAQyE,EAAY/a,IAClF+H,EAAK6Q,UACD7Q,EAAK+S,cACL/S,EAAK+S,cAAcK,kBAEfL,EAAgB3qB,GAAM2qB,cAAc/S,EAAKoQ,SACzC2C,GAAiBA,EAAcM,UAC/BrT,EAAK+S,cAAgBA,EACrBA,EAAcM,QAAQjE,KAAK,SAAU,SAAUvP,GAC3CG,EAAK8L,cAAcwH,YAAYzT,EAAE0T,OAAOC,GACxCxT,EAAK+L,WAAW0H,WAAW5T,EAAE0T,OAAOG,OAIhD1T,EAAKqP,WAAU,GACfrP,EAAKD,QAAQxK,MAEjBmX,YAAa,WAAA,GACL1M,GAAOtJ,KACPoV,EAAgB9L,EAAK8L,cACrBC,EAAa/L,EAAK+L,UACtB/L,GAAKoQ,QAAQuD,OAAO,WAChB7H,EAAcwH,WAAW5c,KAAK4c,YAC9BvH,EAAW0H,UAAU/c,KAAK+c,aAE9B1H,EAAWqD,KAAK,iBAAmBha,GAAK,cAAgBA,GAAI3O,EAAEqX,MAAMkC,EAAK4T,aAAc5T,KAE3F4T,aAAc,SAAU/T,GAAV,GAINgU,GACAJ,CAJA5T,GAAEiU,UAGFD,EAAQzrB,GAAM2rB,YAAYlU,GAC1B4T,EAAY/c,KAAK0Z,QAAQqD,YACzBI,IACAhU,EAAEmU,iBACFvtB,EAAEoZ,EAAEkJ,eAAekL,IAAI,QAAU7e,IAAI,GACrCsB,KAAKqV,WAAW0H,UAAUA,GAAaI,GACvCnd,KAAK0Z,QAAQqD,UAAUA,GAAaI,QAI5CngB,GAAUtL,GAAMmoB,IAAI7c,QACpBC,GAAWvL,GAAMmoB,IAAIxF,KACrBnX,GAAiB,SAAUb,EAAUX,GACrC,OACIoa,YAAa,EACbtkB,SAAU,EACVgsB,WAAY,EACZ/rB,QAAS,EACToB,SAAU,EACVwJ,SAAUA,EACVrF,aAA4B,IAAd0E,IAGlB9J,GAAY,SAAUR,EAAOgE,GAG7B,IAHY,GACR0B,MACAxF,EAAM,EACHA,GAAO8D,EAAO9D,IACjBwF,EAAK7E,KAAKb,EAAMK,QAAQH,GAAKlB,KAEjC,OAAO0G,IAEPqG,GAAY,SAAU/L,EAAOgE,GAG7B,IAHY,GACRhF,GAAO,GACPkB,EAAM,EACHA,GAAO8D,EAAO9D,IACjBlB,GAAQgB,EAAMK,QAAQH,GAAKlB,IAE/B,OAAOA,IAEPgN,GAAgBM,GAAMrG,QACtB0I,KAAM,WACFC,KAAKnN,SAAW,EAChBmN,KAAKiN,aAETuP,MAAO,SAAU1qB,GAAV,GACC2rB,GAAQzd,KAAK0d,OAAO5rB,GACpB6rB,EAAW3d,KAAK4d,WACpB,QAAQ5gB,GAAQ,QAAS,MACjB2gB,EACAF,MAGZ7E,MAAO,WACH5Y,KAAKiN,aAET2Q,UAAW,WAIP,IAJO,GACHrsB,GAASyO,KAAK6d,aACdrsB,KACAF,EAAM,EACHA,EAAMC,EAAQD,IACjBE,EAASS,KAAK+K,GAAQ,MAAO,MAEjC,OAAOA,IAAQ,WAAY,KAAMxL,IAErCksB,OAAQ,SAAU5rB,GACd,GAAIqI,GAAOrI,EAAO,EAWlB,OAVAkO,MAAKxP,OACLwP,KAAK0E,QACL1E,KAAK7O,UAAYgJ,EACjB6F,KAAKyc,YACDtiB,GACA6F,KAAK8d,WAAW3jB,EAAM,GACtB6F,KAAK+d,cAEL/d,KAAK0E,KAAKzS,KAAK+K,GAAQ,KAAM,MAAOA,GAAQ,KAAM,MAAOC,GAAS,eAE/DD,GAAQ,QAAS,KAAMgD,KAAK0E,OAEvCqZ,WAAY,WASR,IATQ,GAIJ9b,GACA+b,EACAC,EACArN,EACAoL,EAPAtX,EAAO1E,KAAK0E,KACZjQ,EAAaiQ,EAAKnT,OAClBqD,EAAS,EAMNA,EAASH,EAAYG,IAExB,GADAqN,EAAMyC,EAAK9P,GACS,IAAhBqN,EAAIic,QAMR,IAHAtN,EAAQ3O,EAAIzQ,SACZysB,EAAU,EACVD,EAAcpN,EAAMrf,OACb0sB,EAAUD,EAAaC,IAC1BjC,EAAOpL,EAAMqN,GACTjC,EAAKmC,WACLnC,EAAK/J,KAAKiM,QAAUjc,EAAIic,UAKxCE,UAAW,SAAUnc,GAIjB,IAJO,GACHyC,GAAO1E,KAAK0E,KACZnT,EAASmT,EAAKnT,OACdD,EAAM,EACHA,EAAMC,GACLmT,EAAKpT,KAAS2Q,EADD3Q,KAKrB,MAAOA,IAEXusB,WAAY,WAAA,GACJjN,GAAQ5Q,KAAK0E,KAAK,GAAK1E,KAAK0E,KAAK,GAAGlT,YACpCD,EAASqf,EAAMrf,OACfgR,EAAY,EACZjR,EAAM,CACV,IAAIC,EACA,KAAOD,EAAMC,EAAQD,IACjBiR,GAAaqO,EAAMtf,GAAK2gB,KAAKoM,SAAW,CAMhD,OAHK9b,KACDA,EAAYvC,KAAKnN,UAEd0P,GAEX+b,KAAM,SAAUltB,EAAOsK,EAAW1E,GAA5B,GAKEunB,GACA/sB,EALAkI,EAAWsG,KAAK7O,UAAUM,QAAQiK,GAAWtL,KAC7CiM,EAAWjL,EAAMK,QAAQiK,GAAWW,SACpCmiB,EAAS9kB,EAAW2C,EACpB7L,EAAMwP,KAAKxP,IAGXyR,EAAMzR,EAAIguB,EA0Bd,OAzBKvc,IAkBDA,EAAIwc,UAAW,EACVxc,EAAIjL,cAAgBiL,EAAIjL,eAAiBA,IAC1CiL,EAAIjL,aAAeA,EACnBiL,EAAIyc,UAAY,EAChBzc,EAAIoc,QAAU,KArBlBpc,EAAMjF,GAAQ,KAAM,SACpBiF,EAAIjL,aAAeA,EACnBiL,EAAIyc,UAAY,EAChBzc,EAAIoc,QAAU,EACdpc,EAAIic,QAAU,EACd1tB,EAAIguB,GAAUvc,EACdsc,EAAY/tB,EAAIkJ,IAAmB2C,EAAY,IAC3CkiB,IACA/sB,EAAW+sB,EAAU/sB,SAEjByQ,EAAIwc,YADJjtB,EAAS,IAAMA,EAAS,GAAGygB,KAAK0M,UAAU3tB,QAAQ,gBAGnCutB,EAAUE,UAGjCze,KAAK0E,KAAKnN,OAAOyI,KAAKoe,UAAUG,GAAa,EAAG,EAAGtc,IAShDA,GAEX6E,UAAW,SAAUjU,EAAUzB,EAAOutB,GAA3B,GAGHzuB,GAMKoB,EAASC,EARdf,EAAMwP,KAAKxP,IACXyR,EAAMzR,EAAIouB,UAOd,KALK3c,IACDA,EAAMjF,GAAQ,KAAM,SACpBxM,EAAIouB,WAAa3c,EACjBjC,KAAK0E,KAAKzS,KAAKgQ,IAEV3Q,EAAM,EAAGC,EAASsB,EAAStB,OAAQD,EAAMC,EAAQD,IACtDpB,EAAU2C,EAASvB,GACnB2Q,EAAIzQ,SAASS,KAAK+N,KAAK6e,MAAMF,GAAa,IAAK3e,KAAK8e,SAAS5uB,EAASkB,IAASlB,GAEnF,OAAOqB,IAEXutB,SAAU,SAAUluB,EAAQQ,GACxB,MAAO6L,IAAS+C,KAAK0R,UACjB9gB,OAAQA,EACRQ,MAAOA,MAGfytB,MAAO,SAAUF,EAAWntB,EAAUZ,GAClC,GAAIorB,GAAOhf,GAAQ,MAAQ2hB,UAAW,WAAaA,GAAantB,EAEhE,OADAwqB,GAAKhoB,MAAQpD,EAAOuL,SAAWvL,EAAOR,KAC/B4rB,GAEX8B,WAAY,SAAU1sB,EAAOsK,EAAW1E,GAA5B,GAIJiL,GAAK8c,EAAUvtB,EAAUwtB,EACzBhD,EAAMiD,EAASC,EAEfpoB,EAEAmW,EACAoR,EATA5sB,EAAUL,EAAMK,QAChBb,EAASa,EAAQiK,GACjByjB,EAAa1tB,EAAQiK,EAAY,GAGjC0jB,KAEA9tB,EAAM,EAGNotB,EAAY,EACZW,EAAkB,CACtB,IAAIzuB,EAAOV,QAEP,MADA8P,MAAK8G,UAAUlW,EAAOY,SAAUJ,GAChC,CA8BJ,IA5BA0F,EAAOpF,GAAMC,UAAUC,GAAUR,EAAOsK,IACxCuG,EAAMjC,KAAKse,KAAKltB,EAAOsK,EAAW1E,GAClCxF,EAAWZ,EAAOY,SAClBwtB,EAAiBxtB,EAASD,OAC1B0b,EAAWjN,KAAKiN,SAASnW,GACpBmW,IACDjN,KAAKiN,SAASnW,GAAQmW,EAAW/P,IAAsBtM,EAAOyL,SAAWX,GACzEuR,EAASqS,cAAsBtf,KAAK7O,UAAUM,QAAQiK,GAAWW,UAErE2D,KAAKyc,SAASxqB,MACV6E,KAAMA,EACN1F,MAAOA,IAEPR,EAAO0L,cACH2Q,EAAShT,YAAa,IACtBykB,EAAYzR,EAAS6I,YACrB7T,EAAIyc,WAAaA,EACjBzR,EAASzb,SAAW,EACpBwtB,EAAiB,GAErBE,GAAaP,UAAW,WAAaK,EAAiBhgB,GAAiBC,KACvEigB,EAASxtB,GAAMugB,KAAK,SAAWnb,EAC/BsoB,EAAantB,KAAK+K,GAAQ,OAAQkiB,KAEtCE,EAAantB,KAAK+N,KAAK8e,SAASluB,EAAQQ,IACxC4qB,EAAOhc,KAAK6e,MAAM5c,EAAIwc,SAAW,WAAa,GAAIW,EAAcxuB,GAChEqR,EAAIzQ,SAASS,KAAK+pB,GAClB/Z,EAAIoc,SAAW,EACXW,EAAgB,CAGhB,IAFAC,EAAUjf,KAAK6e,MAAM,UAAW7e,KAAK8e,SAASluB,EAAQQ,IAASR,GAC/DqR,EAAIzQ,SAASS,KAAKgtB,GACX3tB,EAAM0tB,EAAgB1tB,IACzBytB,EAAW/e,KAAK8d,WAAWtsB,EAASF,GAAMoK,EAAW9K,EAEzDytB,GAAUU,EAASV,QACnBK,EAAYK,EAASL,UACrB1C,EAAK/J,KAAKoM,QAAUA,EACpBpR,EAASzb,SAAW6sB,EACpBpR,EAASxb,QAAU,EACnBwQ,EAAIoc,SAAWA,EACfpc,EAAIyc,WAAaA,EACjBzc,EAAIic,QAAUa,EAASb,QAAU,EAC7BiB,IACIA,EAAWjvB,QACXmuB,EAAUre,KAAK8G,UAAUqY,EAAW3tB,SAAUJ,EAAO,WAErD2tB,EAAW/e,KAAK8d,WAAW1sB,EAAOsK,EAAY,GAC9C2iB,EAAUU,EAASV,QACnBpc,EAAIyc,WAAaK,EAASL,UAC1BW,EAAkBN,EAASL,WAE/BO,EAAQhN,KAAKoM,QAAUA,EACvBA,GAAW,EACXpR,EAASxb,SAAW4sB,EACpBpc,EAAIoc,SAAWA,OAEZc,KACHA,EAAWjvB,QACXmuB,EAAUre,KAAK8G,UAAUqY,EAAW3tB,SAAUJ,IAE9C2tB,EAAW/e,KAAK8d,WAAW1sB,EAAOsK,EAAY,GAC9C2iB,EAAUU,EAASV,QACnBpc,EAAIyc,WAAaK,EAASL,UAC1BW,EAAkBN,EAASL,WAE/BzR,EAASxb,QAAU4sB,EACfA,EAAU,IACVrC,EAAK/J,KAAKoM,QAAUA,EACpBpc,EAAIoc,SAAWA,EAAU,GAWjC,OARIpR,GAASuQ,WAAavQ,EAASxb,QAAU4tB,IACzCpS,EAASuQ,WAAavQ,EAASxb,QAAU4tB,GAE7C7tB,EAAWyb,EAASzb,SAAWktB,EAC3BzR,EAAS6I,YAActkB,IACvByb,EAAS6I,YAActkB,IAE1BytB,GAAWjD,GAAMmC,UAAW,EACtBlc,KAGX5E,GAAaK,GAAMrG,QACnB0I,KAAM,WACFC,KAAKiN,aAETuP,MAAO,SAAU1qB,GAAV,GACC2rB,GAAQzd,KAAK0d,OAAO5rB,GACpB6rB,EAAW3d,KAAK4d,WACpB,QAAQ5gB,GAAQ,QAAS,MACjB2gB,EACAF,MAGZ7E,MAAO,WACH5Y,KAAKiN,aAET4Q,WAAY,WAKR,IALQ,GACJrsB,GAAWwO,KAAK0E,KAAK,GAAGlT,SACxBD,EAAS,EACTD,EAAM,EACN0qB,EAAOxqB,EAASF,GACb0qB,GACHzqB,GAAUyqB,EAAK/J,KAAKoM,SAAW,EAC/BrC,EAAOxqB,IAAWF,EAEtB,OAAOC,IAEXqsB,UAAW,WAIP,IAJO,GACHrsB,GAASyO,KAAK6d,aACdrsB,KACAF,EAAM,EACHA,EAAMC,EAAQD,IACjBE,EAASS,KAAK+K,GAAQ,MAAO,MAEjC,OAAOA,IAAQ,WAAY,KAAMxL,IAErCksB,OAAQ,SAAU5rB,GACd,GAAIqI,GAAOrI,EAAO,EAWlB,OAVAkO,MAAK7O,UAAYgJ,EACjB6F,KAAK0E,QACL1E,KAAKxP,OACLwP,KAAKyc,YACDtiB,GACA6F,KAAK8d,WAAW3jB,EAAM,GACtB6F,KAAK+d,cAEL/d,KAAK0E,KAAKzS,KAAK+K,GAAQ,KAAM,MAAOA,GAAQ,KAAM,MAAOC,GAAS,eAE/DD,GAAQ,QAAS,KAAMgD,KAAK0E,OAEvCqZ,WAAY,WAaR,IAbQ,GAQJ9b,GACA+Z,EACAuD,EAEAC,EAXA9a,EAAO1E,KAAK0E,KACZjQ,EAAaiQ,EAAKnT,OAClBqD,EAAS,EACTnD,EAAUuO,KAAK7O,UAAUM,QACzBguB,EAAkBhuB,EAAQ,GAAGrB,KAC7BsG,EAAgBjF,EAAQF,OACxBmK,EAAY,EAIZlL,EAAMwP,KAAKxP,IAERoE,EAASH,EAAYG,IAExB,IADAqN,EAAMyC,EAAK9P,GACN8G,EAAY,EAAGA,EAAYhF,EAAegF,IAC3C6jB,EAAavf,KAAKvO,EAAQiK,GAAWtL,MACrC4rB,EAAO/Z,EAAIoc,QAAQ,MAAQ3iB,GACvBsgB,GAAQA,EAAKqC,QAAUkB,IACvBvD,EAAK/J,KAAKoM,QAAUkB,EAAavD,EAAKqC,QAAU,EAI5Dpc,GAAMzR,EAAIivB,GACVD,EAAShvB,EAAIivB,EAAkB,OAC3Bxd,IACAA,EAAIzQ,SAAS,GAAGygB,KAAK0M,UAAY,WAEjCa,IACAA,EAAOhuB,SAAS,GAAGygB,KAAK0M,WAAa,aAG7CL,KAAM,SAAU9sB,GACZ,GAAIyQ,GAAMjF,GAAQ,KAAM,KAAMxL,EAI9B,OAHAyQ,GAAIic,QAAU,EACdjc,EAAIoc,WACJre,KAAK0E,KAAKzS,KAAKgQ,GACRA,GAEX6c,SAAU,SAAUluB,EAAQQ,GACxB,MAAO6L,IAAS+C,KAAK0R,UACjB9gB,OAAQA,EACRQ,MAAOA,MAGfytB,MAAO,SAAUF,EAAWntB,EAAUZ,GAClC,GAAIorB,GAAOhf,GAAQ,MAAQ2hB,UAAWA,GAAantB,EAEnD,OADAwqB,GAAKhoB,MAAQpD,EAAOuL,SAAWvL,EAAOR,KAC/B4rB,GAEX8B,WAAY,SAAU1sB,EAAOsK,GAAjB,GAEJ5E,GAaAklB,EAAMiD,EACNF,EAAUS,EACVvS,EACA0R,EAEAe,EACApuB,EApBAd,EAAMwP,KAAKxP,IAEXiB,EAAUL,EAAMK,QAChBb,EAASa,EAAQiK,GACjByjB,EAAa1tB,EAAQiK,EAAY,GACjClK,EAAWZ,EAAOY,SAClBwtB,EAAiBxtB,EAASD,OAC1B8K,GAAkBzL,EAAOyL,SACzB3C,EAAWsG,KAAK7O,UAAUM,QAAQiK,GAAWtL,KAC7CuvB,EAAY/tB,GAAUR,EAAOsK,EAAY,GAAGpC,KAAK,IACjDgmB,GAAsBtf,KAAK7O,UAAUM,QAAQiK,GAAWW,SACxDnF,EAAayoB,GAAaL,IAAiBjjB,EAAW,GAAKzL,EAAOsG,YAAc,IAChF+K,EAAMzR,EAAI0G,EAAa,QAAU1G,EAAI0G,GACrCmnB,EAAUhiB,EAAW,EAKrB+iB,IAQJ,KALKnd,GAAOA,EAAI2d,SACZ3d,EAAMjC,KAAKse,OAEXrc,EAAI2d,UAAW,EAEfhvB,EAAOV,QAAS,CAIhB,IAHAyuB,EAAY1c,EAAIgd,QAAU,gBAAkB,GAC5Chd,EAAIzQ,SAASS,KAAK+N,KAAK6e,MAAMF,GAAY3e,KAAK8e,SAASttB,EAAS,GAAIJ,IAASI,EAAS,KACtFyQ,EAAIic,QAAUc,EACT1tB,EAAM,EAAGA,EAAM0tB,EAAgB1tB,IAChC0O,KAAKse,MAAMte,KAAK6e,MAAMF,GAAY3e,KAAK8e,SAASttB,EAASF,GAAMF,IAASI,EAASF,KAErF,OAAO2Q,GA+BX,GA7BAzR,EAAImvB,EAAY/uB,EAAOR,MAAQ6R,EAC/BnL,EAAOpF,GAAMC,UAAUC,GAAUR,EAAOsK,IACxCuR,EAAWjN,KAAKiN,SAASnW,GACpBmW,IACDjN,KAAKiN,SAASnW,GAAQmW,EAAW/P,GAAeb,EAAUX,GAC1DuR,EAASqS,aAAeA,GAE5Btf,KAAKyc,SAASxqB,MACV6E,KAAMA,EACN1F,MAAOA,IAEPR,EAAO0L,cACH2Q,EAAShT,YAAa,IACtB+kB,EAAiB,EACjB/R,EAASzb,SAAW,GAExBkuB,GAAmBf,UAAW,WAAaK,EAAiBhgB,GAAiBC,KAC7EygB,EAAehuB,GAAMugB,KAAK,SAAWnb,EACrCsoB,EAAantB,KAAK+K,GAAQ,OAAQ0iB,KAEtCN,EAAantB,KAAK+N,KAAK8e,SAASluB,EAAQQ,IACxCutB,EAAY1c,EAAIgd,UAAYD,EAAiB,gBAAkB,GAC/DhD,EAAOhc,KAAK6e,MAAMF,EAAWS,EAAcxuB,GAC3CorB,EAAKqC,QAAUA,EACfpc,EAAIzQ,SAASS,KAAK+pB,GAClB/Z,EAAIoc,QAAQ,MAAQ3iB,GAAasgB,IAC5Bhc,KAAKtG,IAAasG,KAAKtG,GAAY2kB,KACpCre,KAAKtG,GAAY2kB,GAEjBW,EAAgB,CAGhB,IAFA/c,EAAIgd,SAAU,EACdhd,EAAI2d,UAAW,EACVtuB,EAAM,EAAGA,EAAM0tB,EAAgB1tB,IAChCytB,EAAW/e,KAAK8d,WAAWtsB,EAASF,GAAMoK,GACtCuG,IAAQ8c,IACR9c,EAAIic,SAAWa,EAASb,QAG5Bjc,GAAIic,QAAU,IACdlC,EAAK/J,KAAKiM,QAAUjc,EAAIic,SAE5BjR,EAASzb,SAAWyQ,EAAIic,QACxBe,EAAUjf,KAAK6e,MAAM,iBAAkB7e,KAAK8e,SAASluB,EAAQQ,IAASR,GACtEquB,EAAQZ,QAAUA,EAClBmB,EAASxf,KAAKse,MAAMW,IACpBO,EAAOnB,QAAQ,MAAQ3iB,GAAaujB,EACpCO,EAAOP,SAAU,EACjBzuB,EAAImvB,EAAY/uB,EAAOR,KAAO,OAASovB,EACnCL,IACAJ,EAAW/e,KAAK8d,WAAW1sB,EAAOsK,EAAY,GAC9CujB,EAAQhN,KAAKiM,QAAUa,EAASb,SAEpCjc,EAAIic,SAAWsB,EAAOtB,QACtBjR,EAASxb,QAAU+tB,EAAOtB,YACnBiB,KACPld,EAAI2d,UAAW,EACf5f,KAAK8d,WAAW1sB,EAAOsK,EAAY,IAClCujB,GAAWjD,GAAM/J,KAAKiM,QAAUjc,EAAIic,QACrCjR,EAASxb,QAAUwQ,EAAIic,QAQ3B,OANIjR,GAAS6I,YAAc7I,EAASzb,WAChCyb,EAAS6I,YAAc7I,EAASzb,UAEhCyb,EAASuQ,WAAavQ,EAASxb,UAC/Bwb,EAASuQ,WAAavQ,EAASxb,SAE5BwQ,KAGX3E,GAAiBI,GAAMrG,QACvB0I,KAAM,WACFC,KAAKsc,cACLtc,KAAKuB,YAETib,MAAO,SAAU9nB,EAAM4nB,EAAY/a,GAA5B,GAUCkc,GACAE,EAVAvoB,EAAQknB,EAAWvP,QAAQ,GAC3BE,EAAWqP,EAAWrP,SAAS7X,EAAQA,EAAM0B,KAAO9G,EAUxD,OATAgQ,MAAKsc,WAAaA,EAClBtc,KAAKuB,QAAUA,EACfvB,KAAKtL,KAAOA,EACZsL,KAAKuC,UAAY0K,EAAWA,EAAS6I,YAAc7I,EAASuQ,WAAalB,EAAWzpB,SAAStB,QAAU,EAClGyO,KAAKuC,YACNvC,KAAKuC,UAAY,GAEjBkb,EAAQzd,KAAK0d,SACbC,EAAW3d,KAAK4d,aACZ5gB,GAAQ,QAAS,MACjB2gB,EACAF,MAGZG,UAAW,WAAA,GACHrsB,GAASyO,KAAKsc,WAAWzpB,SAAStB,QAAU,EAC5CC,KACAF,EAAM,CAIV,KAHI0O,KAAK0E,KAAK,KACVnT,EAASyO,KAAK0E,KAAK,GAAGlT,SAASD,QAE5BD,EAAMC,EAAQD,IACjBE,EAASS,KAAK+K,GAAQ,MAAO,MAEjC,OAAOA,IAAQ,WAAY,KAAMxL,IAErCksB,OAAQ,WASJ,MARA1d,MAAK0E,QACD1E,KAAKtL,KAAK,IACVsL,KAAKuK,cAAgBvK,KAAKyc,SAASzc,KAAKsc,WAAYtc,KAAKuC,WACzDvC,KAAKwK,WAAaxK,KAAKyc,SAASzc,KAAKuB,QAAS1L,KAAK8lB,KAAK3b,KAAKtL,KAAKnD,OAASyO,KAAKuC,YAChFvC,KAAK8d,cAEL9d,KAAK0E,KAAKzS,KAAK+K,GAAQ,KAAM,MAAOA,GAAQ,KAAM,MAAOC,GAAS,eAE/DD,GAAQ,QAAS,KAAMgD,KAAK0E,OAEvC+X,SAAU,SAAUoD,EAAUC,GAApB,GAEFC,GAKAzqB,EAKAmL,EACArL,EACA5D,EACAwuB,EAdAhuB,KAEA+a,EAAU8S,EAAS9S,QACnBE,EAAW4S,EAAS5S,SACpBpa,EAAWgtB,EAAShtB,SACpBmC,EAAiBnC,EAAStB,QAAU,EAEpCsD,EAAU,EACVorB,EAAa,EACb3uB,EAAM,EACNC,EAASwb,EAAQxb,MAKrB,KAAKA,EAAQ,CACT,IAAKkP,EAAa,EAAGA,EAAazL,EAAgByL,IAC9CzO,EAAOyO,IACHrL,MAAOqL,EACPvQ,QAAS2C,EAAS4N,GAClBrP,MAAO,KAGf,OAAOY,GAEX,KAAOV,EAAMC,EAAQD,IAAO,CAcxB,GAbAyuB,EAAiBhT,EAAQzb,GACzBgE,EAAU2X,EAAS8S,EAAejpB,MAClCtF,EAAW8D,EAAQ9D,SAAW8D,EAAQ7D,QACtCuuB,EAAe,EACXxuB,IACAA,GAAYwD,GAEZM,EAAQ2E,YAAa,GAAS3E,EAAQ9D,WAAa8D,EAAQwgB,cAC3DkK,EAAe1qB,EAAQwgB,aAEvBxgB,EAAQ0B,cAAgB1B,EAAQ+G,WAAa/G,EAAQgqB,eACrD9tB,MAEAA,KAAe,CACf,IAAKiP,EAAa,EAAGA,EAAazL,EAAgByL,IAC9CrL,EAAQ5D,EAAWiP,EACdnL,EAAQ9D,WACT4D,GAAS6qB,GAEbjuB,EAAOR,EAAWyuB,EAAaxf,IAC3BjP,SAAUA,EACV4D,MAAOP,EACP3E,QAAS2C,EAAS4N,GAClBrP,MAAO2uB,EAAe3uB,OAE1ByD,GAAW,CAEf,MAAO7C,EAAOiuB,KAAgBjwB,GAC1BiwB,GAAc,EAGtB,GAAIA,IAAeH,EACf,KAEJjrB,IAAWmrB,EAEf,MAAOhuB,IAEX8rB,WAAY,WAIR,IAJQ,GAKAlmB,GAJJ4S,EAAaxK,KAAKwK,WAClBjZ,EAASiZ,EAAWjZ,OACpBD,EAAM,EACHA,EAAMC,EAAQD,IACbsG,EAAW4S,EAAWlZ,GACtBsG,GACAoI,KAAK0E,KAAKzS,KAAK+N,KAAKkgB,UAAUtoB,KAI1CsoB,UAAW,SAAUxI,GAUjB,IAVO,GAIHD,GAGA0I,EACAnE,EAAMoE,EACNnO,EAAMxe,EAAUvD,EARhBmwB,EAAW3I,EAAQtiB,MAAQ4K,KAAKuC,UAChCgI,EAAgBvK,KAAKuK,cACrBhZ,EAASgZ,EAAchZ,OAEvBqf,KACAtf,EAAM,EAIHA,EAAMC,EAAQD,IACjBmmB,EAAalN,EAAcjZ,GACvBmmB,IAAeznB,IAGnBiiB,KACIwF,EAAWjmB,WACXygB,EAAK0M,UAAY,SAErByB,EAAc,GACd3sB,EAAWuM,KAAKtL,KAAK2rB,EAAW5I,EAAWriB,OAC3ClF,EAAUunB,EAAWvnB,SAAWwnB,EAAQxnB,QACxCiwB,GACIxI,YAAaF,EAAWrmB,MACxBwmB,SAAUF,EAAQtmB,MAClBlB,QAASA,EACTuD,SAAUA,GAES,KAAnBA,EAASO,OAAgB9D,GAAWA,EAAQQ,OACvB,WAAjBR,EAAQQ,KACR0vB,EAAcpgB,KAAK4W,kBAAkBuJ,GACb,UAAjBjwB,EAAQQ,OACf0vB,EAAcpgB,KAAK6W,iBAAiBsJ,KAGvCC,IACDA,EAAcpgB,KAAKmX,aAAagJ,IAEpCnE,EAAOhf,GAAQ,KAAMiV,GAAOhV,GAASmjB,KACrCpE,EAAKhoB,MAAQP,EAASO,MACtB4c,EAAM3e,KAAK+pB,GAMf,OAJA/J,MACIyF,EAAQlmB,WACRygB,EAAK0M,UAAY,iBAEd3hB,GAAQ,KAAMiV,EAAMrB,MAGnCnT,GAAG6iB,OAAOvjB,IACVrL,GAAM6uB,mBAAqB7uB,GAAMgM,MAAMrG,QACnC0I,KAAM,SAAU7E,GACZ8E,KAAK9E,QAAUA,EACf8E,KAAKwgB,OAAStlB,EAAQslB,OACtBxgB,KAAKwO,WAAaxO,KAAKwgB,OAAOhS,YAElC7H,SAAU,WAAA,GAOFrV,GANAmvB,EAAoBzgB,KAAKwgB,OAAO5G,kBAAkBpoB,SAAS,GAC3DkvB,EAAiB1gB,KAAKwgB,OAAOzG,eAAevoB,SAAS,GACrDmvB,EAAqBF,EAAkBjvB,SAAS,GAAGA,SAASD,OAC5DqvB,EAAkBF,EAAelvB,SAAS,GAAGA,SAASD,OACtDwpB,EAAQ/a,KAAKwgB,OAAOtlB,QAAQqb,YAC5BvkB,IAEJ,IAAI4uB,GAAmB5gB,KAAKwO,WAAW9Z,OAAO,GAC1C,IAAKpD,EAAM,EAAGA,EAAMsvB,EAAiBtvB,IACjCU,EAAOC,MAAO4uB,WAAW,GAGjC,KAAKvvB,EAAM,EAAGA,EAAMqvB,EAAoBrvB,IACpCU,EAAOC,MACH4uB,WAAW,EACX9F,MAAOA,GAGf,OAAO/oB,IAEX8uB,OAAQ,SAAUpc,EAAMhU,EAAMqwB,GAO1B,IAPI,GAIA/C,GACA/b,EAAK2O,EACLre,EAAGypB,EALHhqB,KACA4E,EAAI,EACJrF,EAASmT,EAAKnT,OAIXqF,EAAIrF,EAAQqF,IAAK,CAIpB,IAHAqL,KACA2O,EAAQlM,EAAK9N,GAAGpF,SAChBwsB,EAAcpN,EAAMrf,OACfgB,EAAI,EAAGA,EAAIyrB,EAAazrB,IACzBypB,EAAOpL,EAAMre,GACb0P,EAAIhQ,MACA+uB,WAAY,UACZC,MAAO,OACPjtB,MAAOgoB,EAAKhoB,MACZqqB,QAASrC,EAAK/J,KAAKoM,SAAW,EAC9BH,QAASlC,EAAK/J,KAAKiM,SAAW,GAGlC6C,IACAA,EAAS9e,EAAKrL,GAElB5E,EAAOC,MACH2e,MAAO3O,EACPvR,KAAMA,IAGd,MAAOsB,IAEX4U,MAAO,WAAA,GAkBCsa,GAmBAC,EApCAV,EAAoBzgB,KAAKwgB,OAAO5G,kBAAkBpoB,SAAS,GAC3DkvB,EAAiB1gB,KAAKwgB,OAAOzG,eAAevoB,SAAS,GACrDmvB,EAAqBF,EAAkBjvB,SAAS,GAAGA,SAASD,OAC5DqvB,EAAkBF,EAAelvB,SAAS,GAAGA,SAASD,OACtD6vB,EAAmBX,EAAkBjvB,SAAS,GAAGA,SACjD6vB,EAAgBX,EAAelvB,SAAS,GAAGA,SAC3C8vB,EAActhB,KAAKwgB,OAAOxG,YAAYxoB,SAAS,GAAGA,SAAS,GAAGA,SAC9D+vB,EAAavhB,KAAK8gB,OAAOM,EAAkB,SA8B/C,OA7BIR,IACAW,EAAW,GAAG3Q,MAAMrZ,OAAO,EAAG,GAC1BypB,WAAY,UACZC,MAAO,OACPjtB,MAAO,GACPqqB,QAASuC,EACT1C,QAASkD,EAAiB7vB,SAG9B2vB,EAAe,SAAUjf,EAAK7M,GAI9B,IAJe,GAEX4mB,GAAMhoB,EADNzB,EAAI,EAEJqe,EAAQ0Q,EAAYlsB,GAAO5D,SACxBe,EAAIouB,EAAoBpuB,IAC3BypB,EAAOpL,EAAMre,GACbyB,GAAegoB,EAAKhoB,MAChBJ,MAAMI,KACNA,EAAQgoB,EAAKhoB,OAEjBiO,EAAIhQ,MACA+uB,WAAY,UACZC,MAAO,OACPjtB,MAAOA,EACPqqB,QAAS,EACTH,QAAS,KAIjBiD,EAAUnhB,KAAK8gB,OAAOO,EAAe,OAAQH,GAC1CK,EAAWlpB,OAAO8oB,IAE7BK,YAAa,WAAA,GACLf,GAAoBzgB,KAAKwgB,OAAO5G,kBAAkBpoB,SAAS,GAC3DkvB,EAAiB1gB,KAAKwgB,OAAOzG,eAAevoB,SAAS,GACrDovB,EAAkBF,EAAelvB,SAAS,GAAGA,SAASD,OACtD6vB,EAAmBX,EAAkBjvB,SAAS,GAAGA,QACrD,QACIiwB,SAAUb,EACVc,SAAUN,EAAiB7vB,SAGnCowB,SAAU,WACN,GAAI3X,EAOJ,OANIhK,MAAKwO,WAAWqJ,OAAO,IACvB7N,EAAUja,EAAE+Z,WACZE,EAAQD,WAERC,EAAUhK,KAAKwO,WAAW0H,QAEvBlM,EAAQ4X,KAAK7xB,EAAEqX,MAAM,WACxB,OACIya,SACQze,QAASpD,KAAK2G,WACdjC,KAAM1E,KAAK4G,QACXkb,WAAY9hB,KAAKwhB,cACjB3mB,OAAQ,SAGrBmF,UAGPzC,IACAlG,OAAQ,SAAU0qB,GACdA,EAAM3L,OAAOnkB,KAAK,eAClB8vB,EAAM7mB,QAAQ8mB,MAAQjyB,EAAEsH,OAAO0qB,EAAM7mB,QAAQ8mB,MAAOhiB,KAAK9E,SACzD6mB,EAAME,YAAcjiB,KAAKiiB,aAE7B/mB,SACIgnB,SAAU,GACV3P,YAAY,EACZ4P,SAAU,eAEdF,YAAa,WAAA,GACLD,GAAQhiB,KAAK9E,QAAQ8mB,UACrBI,EAAW,GAAI1wB,IAAM6uB,oBAAqBC,OAAQxgB,MACtDoiB,GAAST,WAAWC,KAAK7xB,EAAEqX,MAAM,SAAUib,GACvC,IAAKriB,KAAKqJ,QAAQ,eAAiBsY,SAAUU,IAAS,CAClD,GAAIV,GAAW,GAAIjwB,IAAM4wB,MAAMC,SAASF,EACxCV,GAASa,iBAAiBZ,KAAK,SAAUa,GACrC/wB,GAAMgxB,QACFD,QAASA,EACTN,SAAUE,EAAKF,UAAYH,EAAMG,SACjCD,SAAUF,EAAME,SAChBS,WAAYX,EAAMW,iBAI/B3iB,SAGXtO,GAAM6L,gBAAkBA,GACpB7L,GAAM4wB,OAAS5wB,GAAM4wB,MAAMC,UAC3BhlB,GAAgBlG,OAAO0F,GAAU6lB,WAEjClxB,GAAMmxB,WACNnxB,GAAMmxB,SAASxrB,OAAO0F,GAAU6lB,WAChC7lB,GAAU2J,GAAGoc,SAAW,WACpB,MAAO9iB,MAAK+iB,gBAAiBhI,MAAO/a,KAAK6Y,QAAQkC,UAAaiI,WAAYhjB,KAAK9E,QAAQ+nB,IAAID,gBAGrGxlB,OAAO9L,MAAMwxB,QACR1lB,OAAO9L,OACE,kBAAV5B,SAAwBA,OAAOqzB,IAAMrzB,OAAS,SAAUszB,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.pivotgrid.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.pivotgrid', [\n        'kendo.dom',\n        'kendo.data'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'pivotgrid',\n        name: 'PivotGrid',\n        category: 'web',\n        description: 'The PivotGrid widget is a data summarization tool.',\n        depends: [\n            'dom',\n            'data',\n            'data.xml',\n            'sortable'\n        ],\n        features: [\n            {\n                id: 'pivotgrid-configurator',\n                name: 'Configurator',\n                description: 'The PivotConfigurator widget allows the user to select data slices displayed in PivotGrid',\n                depends: ['pivot.configurator']\n            },\n            {\n                id: 'pivotgrid-filtering',\n                name: 'Filtering',\n                description: 'Support for filtering',\n                depends: ['pivot.fieldmenu']\n            },\n            {\n                id: 'pivotgrid-excel-export',\n                name: 'Excel export',\n                description: 'Export pivot grid data as Excel spreadsheet',\n                depends: ['ooxml']\n            },\n            {\n                id: 'pivotgrid-pdf-export',\n                name: 'PDF export',\n                description: 'Export pivot grid data as PDF',\n                depends: [\n                    'pdf',\n                    'drawing'\n                ]\n            },\n            {\n                id: 'mobile-scroller',\n                name: 'Mobile scroller',\n                description: 'Support for kinetic scrolling in mobile device',\n                depends: ['mobile.scroller']\n            }\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Class = kendo.Class, Widget = ui.Widget, DataSource = kendo.data.DataSource, outerWidth = kendo._outerWidth, outerHeight = kendo._outerHeight, toString = {}.toString, identity = function (o) {\n                return o;\n            }, map = $.map, extend = $.extend, isFunction = kendo.isFunction, CHANGE = 'change', ERROR = 'error', MEASURES = 'Measures', PROGRESS = 'progress', STATERESET = 'stateReset', AUTO = 'auto', DIV = '<div/>', NS = '.kendoPivotGrid', ROW_TOTAL_KEY = '__row_total__', DATABINDING = 'dataBinding', DATABOUND = 'dataBound', EXPANDMEMBER = 'expandMember', COLLAPSEMEMBER = 'collapseMember', STATE_EXPANDED = 'k-i-collapse', STATE_COLLAPSED = 'k-i-expand', HEADER_TEMPLATE = '<span>#: data.member.caption || data.member.name #</span>', KPISTATUS_TEMPLATE = '<span class=\"k-icon k-i-kpi-status-#=data.dataItem.value > 0 ? \"open\" : data.dataItem.value < 0 ? \"deny\" : \"hold\"#\" title=\"#:data.dataItem.value#\"></span>', KPITREND_TEMPLATE = '<span class=\"k-icon k-i-kpi-trend-#=data.dataItem.value > 0 ? \"increase\" : data.dataItem.value < 0 ? \"decrease\" : \"equal\"#\" title=\"#:data.dataItem.value#\"></span>', DATACELL_TEMPLATE = '#= data.dataItem ? kendo.htmlEncode(data.dataItem.fmtValue || data.dataItem.value) || \"&nbsp;\" : \"&nbsp;\" #', LAYOUT_TABLE = '<table class=\"k-pivot-layout\">' + '<tr>' + '<td>' + '<div class=\"k-pivot-rowheaders\"></div>' + '</td>' + '<td>' + '<div class=\"k-pivot-table k-state-default\"></div>' + '</td>' + '</tr>' + '</table>';\n        var AXIS_ROWS = 'rows';\n        var AXIS_COLUMNS = 'columns';\n        function normalizeMeasures(measure) {\n            var descriptor = typeof measure === 'string' ? [{ name: measure }] : measure;\n            var descriptors = toString.call(descriptor) === '[object Array]' ? descriptor : descriptor !== undefined ? [descriptor] : [];\n            return map(descriptors, function (d) {\n                if (typeof d === 'string') {\n                    return { name: d };\n                }\n                return {\n                    name: d.name,\n                    type: d.type\n                };\n            });\n        }\n        function normalizeMembers(member) {\n            var descriptor = typeof member === 'string' ? [{\n                    name: [member],\n                    expand: false\n                }] : member;\n            var descriptors = toString.call(descriptor) === '[object Array]' ? descriptor : descriptor !== undefined ? [descriptor] : [];\n            return map(descriptors, function (d) {\n                if (typeof d === 'string') {\n                    return {\n                        name: [d],\n                        expand: false\n                    };\n                }\n                return {\n                    name: toString.call(d.name) === '[object Array]' ? d.name.slice() : [d.name],\n                    expand: d.expand\n                };\n            });\n        }\n        function normalizeName(name) {\n            if (name.indexOf(' ') !== -1) {\n                name = '[\"' + name + '\"]';\n            }\n            return name;\n        }\n        function accumulateMembers(accumulator, rootTuple, tuple, level) {\n            var idx, length;\n            var children;\n            var member;\n            if (!tuple) {\n                tuple = rootTuple;\n            }\n            if (!level) {\n                level = 0;\n            }\n            member = tuple.members[level];\n            if (!member || member.measure) {\n                return;\n            }\n            children = member.children;\n            length = children.length;\n            if (tuple === rootTuple) {\n                accumulator[kendo.stringify([member.name])] = !!length;\n            } else if (length) {\n                accumulator[kendo.stringify(buildPath(tuple, level))] = true;\n            }\n            if (length) {\n                for (idx = 0; idx < length; idx++) {\n                    accumulateMembers(accumulator, rootTuple, children[idx], level);\n                }\n            }\n            accumulateMembers(accumulator, rootTuple, tuple, level + 1);\n        }\n        function descriptorsForAxes(tuples) {\n            var result = {};\n            if (tuples.length) {\n                accumulateMembers(result, tuples[0]);\n            }\n            var descriptors = [];\n            for (var k in result) {\n                descriptors.push({\n                    name: $.parseJSON(k),\n                    expand: result[k]\n                });\n            }\n            return descriptors;\n        }\n        function addMissingPathMembers(members, axis) {\n            var tuples = axis.tuples || [];\n            var firstTuple = tuples[0];\n            if (firstTuple && members.length < firstTuple.members.length) {\n                var tupleMembers = firstTuple.members;\n                for (var idx = 0; idx < tupleMembers.length; idx++) {\n                    if (tupleMembers[idx].measure) {\n                        continue;\n                    }\n                    var found = false;\n                    for (var j = 0; j < members.length; j++) {\n                        if (getName(members[j]).indexOf(tupleMembers[idx].hierarchy) === 0) {\n                            found = true;\n                            break;\n                        }\n                    }\n                    if (!found) {\n                        members.push({\n                            name: [tupleMembers[idx].name],\n                            expand: false\n                        });\n                    }\n                }\n            }\n        }\n        function tupleToDescriptors(tuple) {\n            var result = [];\n            var members = tuple.members;\n            for (var idx = 0; idx < members.length; idx++) {\n                if (members[idx].measure) {\n                    continue;\n                }\n                result.push({\n                    name: [members[idx].name],\n                    expand: members[idx].children.length > 0\n                });\n            }\n            return result;\n        }\n        function descriptorsForMembers(axis, members, measures) {\n            axis = axis || {};\n            addMissingPathMembers(members, axis);\n            if (measures.length > 1) {\n                members.push({\n                    name: MEASURES,\n                    measure: true,\n                    children: normalizeMembers(measures)\n                });\n            }\n            var tupletoSearch = { members: members };\n            if (axis.tuples) {\n                var result = findExistingTuple(axis.tuples, tupletoSearch);\n                if (result.tuple) {\n                    members = tupleToDescriptors(result.tuple);\n                }\n            }\n            return members;\n        }\n        function createAggregateGetter(m) {\n            var measureGetter = kendo.getter(m.field, true);\n            return function (aggregatorContext, state) {\n                return m.aggregate(measureGetter(aggregatorContext.dataItem), state, aggregatorContext);\n            };\n        }\n        function isNumber(val) {\n            return typeof val === 'number' && !isNaN(val);\n        }\n        function isDate(val) {\n            return val && val.getTime;\n        }\n        var functions = {\n            sum: function (value, state) {\n                var accumulator = state.accumulator;\n                if (!isNumber(accumulator)) {\n                    accumulator = value;\n                } else if (isNumber(value)) {\n                    accumulator += value;\n                }\n                return accumulator;\n            },\n            count: function (value, state) {\n                return (state.accumulator || 0) + 1;\n            },\n            average: {\n                aggregate: function (value, state) {\n                    var accumulator = state.accumulator;\n                    if (state.count === undefined) {\n                        state.count = 0;\n                    }\n                    if (!isNumber(accumulator)) {\n                        accumulator = value;\n                    } else if (isNumber(value)) {\n                        accumulator += value;\n                    }\n                    if (isNumber(value)) {\n                        state.count++;\n                    }\n                    return accumulator;\n                },\n                result: function (state) {\n                    var accumulator = state.accumulator;\n                    if (isNumber(accumulator)) {\n                        accumulator = accumulator / state.count;\n                    }\n                    return accumulator;\n                }\n            },\n            max: function (value, state) {\n                var accumulator = state.accumulator;\n                if (!isNumber(accumulator) && !isDate(accumulator)) {\n                    accumulator = value;\n                }\n                if (accumulator < value && (isNumber(value) || isDate(value))) {\n                    accumulator = value;\n                }\n                return accumulator;\n            },\n            min: function (value, state) {\n                var accumulator = state.accumulator;\n                if (!isNumber(accumulator) && !isDate(accumulator)) {\n                    accumulator = value;\n                }\n                if (accumulator > value && (isNumber(value) || isDate(value))) {\n                    accumulator = value;\n                }\n                return accumulator;\n            }\n        };\n        var PivotCubeBuilder = Class.extend({\n            init: function (options) {\n                this.options = extend({}, this.options, options);\n                this.dimensions = this._normalizeDescriptors('field', this.options.dimensions);\n                this.measures = this._normalizeDescriptors('name', this.options.measures);\n            },\n            _normalizeDescriptors: function (keyField, descriptors) {\n                descriptors = descriptors || {};\n                var fields = {};\n                var field;\n                if (toString.call(descriptors) === '[object Array]') {\n                    for (var idx = 0, length = descriptors.length; idx < length; idx++) {\n                        field = descriptors[idx];\n                        if (typeof field === 'string') {\n                            fields[field] = {};\n                        } else if (field[keyField]) {\n                            fields[field[keyField]] = field;\n                        }\n                    }\n                    descriptors = fields;\n                }\n                return descriptors;\n            },\n            _rootTuples: function (rootNames, measureAggregators) {\n                var aggregatorsLength = measureAggregators.length || 1;\n                var dimensionsSchema = this.dimensions || [];\n                var root, name, parts;\n                var measureIdx = 0;\n                var idx;\n                var rootNamesLength = rootNames.length;\n                var result = [];\n                var keys = [];\n                if (rootNamesLength || measureAggregators.length) {\n                    for (measureIdx = 0; measureIdx < aggregatorsLength; measureIdx++) {\n                        root = { members: [] };\n                        for (idx = 0; idx < rootNamesLength; idx++) {\n                            name = rootNames[idx];\n                            parts = name.split('&');\n                            root.members[root.members.length] = {\n                                children: [],\n                                caption: (dimensionsSchema[name] || {}).caption || 'All',\n                                name: name,\n                                levelName: name,\n                                levelNum: '0',\n                                hasChildren: true,\n                                parentName: parts.length > 1 ? parts[0] : undefined,\n                                hierarchy: name\n                            };\n                        }\n                        if (aggregatorsLength > 1) {\n                            root.members[root.members.length] = {\n                                children: [],\n                                caption: measureAggregators[measureIdx].caption,\n                                name: measureAggregators[measureIdx].descriptor.name,\n                                levelName: 'MEASURES',\n                                levelNum: '0',\n                                hasChildren: false,\n                                parentName: undefined,\n                                hierarchy: 'MEASURES'\n                            };\n                        }\n                        result[result.length] = root;\n                    }\n                    keys.push(ROW_TOTAL_KEY);\n                }\n                return {\n                    keys: keys,\n                    tuples: result\n                };\n            },\n            _expandedTuples: function (map, expanded, measureAggregators) {\n                var aggregatorsLength = measureAggregators.length || 1;\n                var dimensionsSchema = this.dimensions || [];\n                var measureIdx;\n                var tuple;\n                var key;\n                var mapItem;\n                var current;\n                var currentKeys;\n                var accumulator = [];\n                var accumulatorKeys = [];\n                var memberInfo;\n                var expandedNames;\n                var parts;\n                var name;\n                var idx;\n                for (key in map) {\n                    mapItem = map[key];\n                    memberInfo = this._findExpandedMember(expanded, mapItem.uniquePath);\n                    current = accumulator[memberInfo.index] || [];\n                    currentKeys = accumulatorKeys[memberInfo.index] || [];\n                    expandedNames = memberInfo.member.names;\n                    for (measureIdx = 0; measureIdx < aggregatorsLength; measureIdx++) {\n                        tuple = { members: [] };\n                        for (idx = 0; idx < expandedNames.length; idx++) {\n                            if (idx === memberInfo.member.expandedIdx) {\n                                tuple.members[tuple.members.length] = {\n                                    children: [],\n                                    caption: mapItem.value,\n                                    name: mapItem.name,\n                                    hasChildren: false,\n                                    levelNum: 1,\n                                    levelName: mapItem.parentName + mapItem.name,\n                                    parentName: mapItem.parentName,\n                                    hierarchy: mapItem.parentName + mapItem.name\n                                };\n                                if (measureIdx === 0) {\n                                    currentKeys.push(buildPath(tuple, idx).join(''));\n                                }\n                            } else {\n                                name = expandedNames[idx];\n                                parts = name.split('&');\n                                tuple.members[tuple.members.length] = {\n                                    children: [],\n                                    caption: (dimensionsSchema[name] || {}).caption || 'All',\n                                    name: name,\n                                    levelName: name,\n                                    levelNum: '0',\n                                    hasChildren: true,\n                                    parentName: parts.length > 1 ? parts[0] : undefined,\n                                    hierarchy: name\n                                };\n                            }\n                        }\n                        if (aggregatorsLength > 1) {\n                            tuple.members[tuple.members.length] = {\n                                children: [],\n                                caption: measureAggregators[measureIdx].caption,\n                                name: measureAggregators[measureIdx].descriptor.name,\n                                levelName: 'MEASURES',\n                                levelNum: '0',\n                                hasChildren: true,\n                                parentName: undefined,\n                                hierarchy: 'MEASURES'\n                            };\n                        }\n                        current[current.length] = tuple;\n                    }\n                    accumulator[memberInfo.index] = current;\n                    accumulatorKeys[memberInfo.index] = currentKeys;\n                }\n                return {\n                    keys: accumulatorKeys,\n                    tuples: accumulator\n                };\n            },\n            _findExpandedMember: function (members, parentName) {\n                for (var idx = 0; idx < members.length; idx++) {\n                    if (members[idx].uniquePath === parentName) {\n                        return {\n                            member: members[idx],\n                            index: idx\n                        };\n                    }\n                }\n            },\n            _asTuples: function (map, descriptor, measureAggregators) {\n                measureAggregators = measureAggregators || [];\n                var rootInfo = this._rootTuples(descriptor.root, measureAggregators);\n                var expandedInfo = this._expandedTuples(map, descriptor.expanded, measureAggregators);\n                return {\n                    keys: [].concat.apply(rootInfo.keys, expandedInfo.keys),\n                    tuples: [].concat.apply(rootInfo.tuples, expandedInfo.tuples)\n                };\n            },\n            _measuresInfo: function (measures, rowAxis) {\n                var idx = 0;\n                var length = measures && measures.length;\n                var aggregateNames = [];\n                var resultFuncs = {};\n                var formats = {};\n                var descriptors = this.measures || {};\n                var measure;\n                var name;\n                for (; idx < length; idx++) {\n                    name = measures[idx].descriptor.name;\n                    measure = descriptors[name] || {};\n                    aggregateNames.push(name);\n                    if (measure.result) {\n                        resultFuncs[name] = measure.result;\n                    }\n                    if (measure.format) {\n                        formats[name] = measure.format;\n                    }\n                }\n                return {\n                    names: aggregateNames,\n                    formats: formats,\n                    resultFuncs: resultFuncs,\n                    rowAxis: rowAxis\n                };\n            },\n            _toDataArray: function (map, measuresInfo, rowKeys, columnKeys) {\n                var result = [];\n                var aggregates;\n                var name, i, j, k, n;\n                var row, column, columnKey;\n                var rowMeasureNamesLength = 1;\n                var rowMeasureNames = [];\n                var columnMeasureNames;\n                var rowLength = rowKeys.length || 1;\n                var columnLength = columnKeys.length || 1;\n                if (measuresInfo.rowAxis) {\n                    rowMeasureNames = measuresInfo.names;\n                    rowMeasureNamesLength = rowMeasureNames.length;\n                } else {\n                    columnMeasureNames = measuresInfo.names;\n                }\n                for (i = 0; i < rowLength; i++) {\n                    row = map[rowKeys[i] || ROW_TOTAL_KEY];\n                    for (n = 0; n < rowMeasureNamesLength; n++) {\n                        if (measuresInfo.rowAxis) {\n                            columnMeasureNames = [rowMeasureNames[n]];\n                        }\n                        for (j = 0; j < columnLength; j++) {\n                            columnKey = columnKeys[j] || ROW_TOTAL_KEY;\n                            column = row.items[columnKey];\n                            if (columnKey === ROW_TOTAL_KEY) {\n                                aggregates = row.aggregates;\n                            } else {\n                                aggregates = column ? column.aggregates : {};\n                            }\n                            for (k = 0; k < columnMeasureNames.length; k++) {\n                                name = columnMeasureNames[k];\n                                this._addData(result, aggregates[name], measuresInfo.formats[name], measuresInfo.resultFuncs[name]);\n                            }\n                        }\n                    }\n                }\n                return result;\n            },\n            _addData: function (result, value, format, resultFunc) {\n                var fmtValue = '';\n                var ordinal;\n                if (value) {\n                    value = resultFunc ? resultFunc(value) : value.accumulator;\n                    fmtValue = format ? kendo.format(format, value) : value;\n                }\n                ordinal = result.length;\n                result[ordinal] = {\n                    ordinal: ordinal,\n                    value: value || '',\n                    fmtValue: fmtValue\n                };\n            },\n            _matchDescriptors: function (dataItem, descriptor, getters) {\n                var parts;\n                var parentField;\n                var expectedValue;\n                var names = descriptor.names;\n                var idx = descriptor.expandedIdx;\n                var value;\n                while (idx > 0) {\n                    parts = names[--idx].split('&');\n                    if (parts.length > 1) {\n                        parentField = parts[0];\n                        expectedValue = parts[1];\n                        value = getters[parentField](dataItem);\n                        value = value !== undefined && value !== null ? value.toString() : value;\n                        if (value != expectedValue) {\n                            return false;\n                        }\n                    }\n                }\n                return true;\n            },\n            _calculateAggregate: function (measureAggregators, aggregatorContext, totalItem) {\n                var result = {};\n                var state;\n                var name;\n                for (var measureIdx = 0; measureIdx < measureAggregators.length; measureIdx++) {\n                    name = measureAggregators[measureIdx].descriptor.name;\n                    state = totalItem.aggregates[name] || {};\n                    state.accumulator = measureAggregators[measureIdx].aggregator(aggregatorContext, state);\n                    result[name] = state;\n                }\n                return result;\n            },\n            _processColumns: function (measureAggregators, descriptors, getters, columns, aggregatorContext, rowTotal, state, updateColumn) {\n                var value;\n                var descriptor;\n                var column;\n                var totalItem;\n                var key, name, parentName, path;\n                var dataItem = aggregatorContext.dataItem;\n                var idx = 0;\n                for (; idx < descriptors.length; idx++) {\n                    descriptor = descriptors[idx];\n                    if (!this._matchDescriptors(dataItem, descriptor, getters)) {\n                        continue;\n                    }\n                    path = descriptor.names.slice(0, descriptor.expandedIdx).join('');\n                    name = descriptor.names[descriptor.expandedIdx];\n                    value = getters[name](dataItem);\n                    value = value !== undefined && value !== null ? value.toString() : value;\n                    parentName = name;\n                    name = name + '&' + value;\n                    key = path + name;\n                    column = columns[key] || {\n                        index: state.columnIndex,\n                        parentName: parentName,\n                        name: name,\n                        uniquePath: path + parentName,\n                        value: value\n                    };\n                    totalItem = rowTotal.items[key] || { aggregates: {} };\n                    rowTotal.items[key] = {\n                        index: column.index,\n                        aggregates: this._calculateAggregate(measureAggregators, aggregatorContext, totalItem)\n                    };\n                    if (updateColumn) {\n                        if (!columns[key]) {\n                            state.columnIndex++;\n                        }\n                        columns[key] = column;\n                    }\n                }\n            },\n            _measureAggregators: function (options) {\n                var measureDescriptors = options.measures || [];\n                var measures = this.measures || {};\n                var aggregators = [];\n                var descriptor, measure, idx, length;\n                var defaultAggregate, aggregate;\n                if (measureDescriptors.length) {\n                    for (idx = 0, length = measureDescriptors.length; idx < length; idx++) {\n                        descriptor = measureDescriptors[idx];\n                        measure = measures[descriptor.name];\n                        defaultAggregate = null;\n                        if (measure) {\n                            aggregate = measure.aggregate;\n                            if (typeof aggregate === 'string') {\n                                defaultAggregate = functions[aggregate.toLowerCase()];\n                                if (!defaultAggregate) {\n                                    throw new Error('There is no such aggregate function');\n                                }\n                                measure.aggregate = defaultAggregate.aggregate || defaultAggregate;\n                                measure.result = defaultAggregate.result;\n                            }\n                            aggregators.push({\n                                descriptor: descriptor,\n                                caption: measure.caption,\n                                result: measure.result,\n                                aggregator: createAggregateGetter(measure)\n                            });\n                        }\n                    }\n                } else {\n                    aggregators.push({\n                        descriptor: { name: 'default' },\n                        caption: 'default',\n                        aggregator: function () {\n                            return 1;\n                        }\n                    });\n                }\n                return aggregators;\n            },\n            _buildGetters: function (names) {\n                var result = {};\n                var parts;\n                var name;\n                for (var idx = 0; idx < names.length; idx++) {\n                    name = names[idx];\n                    parts = name.split('&');\n                    if (parts.length > 1) {\n                        result[parts[0]] = kendo.getter(parts[0], true);\n                    } else {\n                        result[name] = kendo.getter(normalizeName(name), true);\n                    }\n                }\n                return result;\n            },\n            _parseDescriptors: function (descriptors) {\n                var parsedDescriptors = parseDescriptors(descriptors);\n                var rootNames = getRootNames(parsedDescriptors.root);\n                var expanded = parsedDescriptors.expanded;\n                var result = [];\n                for (var idx = 0; idx < expanded.length; idx++) {\n                    result.push(mapNames(expanded[idx].name, rootNames));\n                }\n                return {\n                    root: rootNames,\n                    expanded: result\n                };\n            },\n            _filter: function (data, filter) {\n                if (!filter) {\n                    return data;\n                }\n                var expr;\n                var idx = 0;\n                var filters = filter.filters;\n                for (; idx < filters.length; idx++) {\n                    expr = filters[idx];\n                    if (expr.operator === 'in') {\n                        filters[idx] = this._normalizeFilter(expr);\n                    }\n                }\n                return new kendo.data.Query(data).filter(filter).data;\n            },\n            _normalizeFilter: function (filter) {\n                var value = filter.value.split(',');\n                var result = [];\n                if (!value.length) {\n                    return value;\n                }\n                for (var idx = 0; idx < value.length; idx++) {\n                    result.push({\n                        field: filter.field,\n                        operator: 'eq',\n                        value: value[idx]\n                    });\n                }\n                return {\n                    logic: 'or',\n                    filters: result\n                };\n            },\n            process: function (data, options) {\n                data = data || [];\n                options = options || {};\n                data = this._filter(data, options.filter);\n                var measures = options.measures || [];\n                var measuresRowAxis = options.measuresAxis === 'rows';\n                var columnDescriptors = options.columns || [];\n                var rowDescriptors = options.rows || [];\n                if (!columnDescriptors.length && rowDescriptors.length && (!measures.length || measures.length && measuresRowAxis)) {\n                    columnDescriptors = rowDescriptors;\n                    rowDescriptors = [];\n                    measuresRowAxis = false;\n                }\n                if (!columnDescriptors.length && !rowDescriptors.length) {\n                    measuresRowAxis = false;\n                }\n                if (!columnDescriptors.length && measures.length) {\n                    columnDescriptors = normalizeMembers(options.measures);\n                }\n                columnDescriptors = this._parseDescriptors(columnDescriptors);\n                rowDescriptors = this._parseDescriptors(rowDescriptors);\n                var aggregatedData = {};\n                var columns = {};\n                var rows = {};\n                var rowValue;\n                var state = { columnIndex: 0 };\n                var measureAggregators = this._measureAggregators(options);\n                var columnGetters = this._buildGetters(columnDescriptors.root);\n                var rowGetters = this._buildGetters(rowDescriptors.root);\n                var processed = false;\n                var expandedColumns = columnDescriptors.expanded;\n                var expandedRows = rowDescriptors.expanded;\n                var dataItem;\n                var aggregatorContext;\n                var hasExpandedRows = expandedRows.length !== 0;\n                var rowIdx, rowDescriptor, rowName, rowTotal;\n                var key, path, parentName, value;\n                var columnsInfo, rowsInfo;\n                var length = data.length;\n                var idx = 0;\n                if (columnDescriptors.root.length || rowDescriptors.root.length) {\n                    processed = true;\n                    for (idx = 0; idx < length; idx++) {\n                        dataItem = data[idx];\n                        aggregatorContext = {\n                            dataItem: dataItem,\n                            index: idx\n                        };\n                        rowTotal = aggregatedData[ROW_TOTAL_KEY] || {\n                            items: {},\n                            aggregates: {}\n                        };\n                        this._processColumns(measureAggregators, expandedColumns, columnGetters, columns, aggregatorContext, rowTotal, state, !hasExpandedRows);\n                        rowTotal.aggregates = this._calculateAggregate(measureAggregators, aggregatorContext, rowTotal);\n                        aggregatedData[ROW_TOTAL_KEY] = rowTotal;\n                        for (rowIdx = 0; rowIdx < expandedRows.length; rowIdx++) {\n                            rowDescriptor = expandedRows[rowIdx];\n                            if (!this._matchDescriptors(dataItem, rowDescriptor, rowGetters)) {\n                                this._processColumns(measureAggregators, expandedColumns, columnGetters, columns, aggregatorContext, {\n                                    items: {},\n                                    aggregates: {}\n                                }, state, true);\n                                continue;\n                            }\n                            path = rowDescriptor.names.slice(0, rowDescriptor.expandedIdx).join('');\n                            rowName = rowDescriptor.names[rowDescriptor.expandedIdx];\n                            parentName = rowName;\n                            rowValue = rowGetters[rowName](dataItem);\n                            rowValue = rowValue !== undefined ? rowValue.toString() : rowValue;\n                            rowName = rowName + '&' + rowValue;\n                            key = path + rowName;\n                            rows[key] = {\n                                uniquePath: path + parentName,\n                                parentName: parentName,\n                                name: rowName,\n                                value: rowValue\n                            };\n                            value = aggregatedData[key] || {\n                                items: {},\n                                aggregates: {}\n                            };\n                            this._processColumns(measureAggregators, expandedColumns, columnGetters, columns, aggregatorContext, value, state, true);\n                            value.aggregates = this._calculateAggregate(measureAggregators, aggregatorContext, value);\n                            aggregatedData[key] = value;\n                        }\n                    }\n                }\n                if (processed && length) {\n                    if (measureAggregators.length > 1 && (!options.columns || !options.columns.length)) {\n                        columnDescriptors = {\n                            root: [],\n                            expanded: []\n                        };\n                    }\n                    columnsInfo = this._asTuples(columns, columnDescriptors, measuresRowAxis ? [] : measureAggregators);\n                    rowsInfo = this._asTuples(rows, rowDescriptors, measuresRowAxis ? measureAggregators : []);\n                    columns = columnsInfo.tuples;\n                    rows = rowsInfo.tuples;\n                    aggregatedData = this._toDataArray(aggregatedData, this._measuresInfo(measureAggregators, measuresRowAxis), rowsInfo.keys, columnsInfo.keys);\n                } else {\n                    aggregatedData = columns = rows = [];\n                }\n                return {\n                    axes: {\n                        columns: { tuples: columns },\n                        rows: { tuples: rows }\n                    },\n                    data: aggregatedData\n                };\n            }\n        });\n        var PivotTransport = Class.extend({\n            init: function (options, transport) {\n                this.transport = transport;\n                this.options = transport.options || {};\n                if (!this.transport.discover) {\n                    if (isFunction(options.discover)) {\n                        this.discover = options.discover;\n                    }\n                }\n            },\n            read: function (options) {\n                return this.transport.read(options);\n            },\n            update: function (options) {\n                return this.transport.update(options);\n            },\n            create: function (options) {\n                return this.transport.create(options);\n            },\n            destroy: function (options) {\n                return this.transport.destroy(options);\n            },\n            discover: function (options) {\n                if (this.transport.discover) {\n                    return this.transport.discover(options);\n                }\n                options.success({});\n            },\n            catalog: function (val) {\n                var options = this.options || {};\n                if (val === undefined) {\n                    return (options.connection || {}).catalog;\n                }\n                var connection = options.connection || {};\n                connection.catalog = val;\n                this.options.connection = connection;\n                $.extend(this.transport.options, { connection: connection });\n            },\n            cube: function (val) {\n                var options = this.options || {};\n                if (val === undefined) {\n                    return (options.connection || {}).cube;\n                }\n                var connection = options.connection || {};\n                connection.cube = val;\n                this.options.connection = connection;\n                extend(true, this.transport.options, { connection: connection });\n            }\n        });\n        var PivotDataSource = DataSource.extend({\n            init: function (options) {\n                var cube = ((options || {}).schema || {}).cube;\n                var measuresAxis = 'columns';\n                var measures;\n                var schema = {\n                    axes: identity,\n                    cubes: identity,\n                    catalogs: identity,\n                    measures: identity,\n                    dimensions: identity,\n                    hierarchies: identity,\n                    levels: identity,\n                    members: identity\n                };\n                if (cube) {\n                    schema = $.extend(schema, this._cubeSchema(cube));\n                    this.cubeBuilder = new PivotCubeBuilder(cube);\n                }\n                DataSource.fn.init.call(this, extend(true, {}, { schema: schema }, options));\n                this.transport = new PivotTransport(this.options.transport || {}, this.transport);\n                this._columns = normalizeMembers(this.options.columns);\n                this._rows = normalizeMembers(this.options.rows);\n                measures = this.options.measures || [];\n                if (toString.call(measures) === '[object Object]') {\n                    measuresAxis = measures.axis || 'columns';\n                    measures = measures.values || [];\n                }\n                this._measures = normalizeMeasures(measures);\n                this._measuresAxis = measuresAxis;\n                this._skipNormalize = 0;\n                this._axes = {};\n            },\n            _cubeSchema: function (cube) {\n                return {\n                    dimensions: function () {\n                        var result = [];\n                        var dimensions = cube.dimensions;\n                        for (var key in dimensions) {\n                            result.push({\n                                name: key,\n                                caption: dimensions[key].caption || key,\n                                uniqueName: key,\n                                defaultHierarchy: key,\n                                type: 1\n                            });\n                        }\n                        if (cube.measures) {\n                            result.push({\n                                name: MEASURES,\n                                caption: MEASURES,\n                                uniqueName: MEASURES,\n                                type: 2\n                            });\n                        }\n                        return result;\n                    },\n                    hierarchies: function () {\n                        return [];\n                    },\n                    measures: function () {\n                        var result = [];\n                        var measures = cube.measures;\n                        for (var key in measures) {\n                            result.push({\n                                name: key,\n                                caption: key,\n                                uniqueName: key,\n                                aggregator: key\n                            });\n                        }\n                        return result;\n                    },\n                    members: $.proxy(function (response, restrictions) {\n                        var name = restrictions.levelUniqueName || restrictions.memberUniqueName;\n                        var schemaData = this.options.schema.data;\n                        var dataGetter = isFunction(schemaData) ? schemaData : kendo.getter(schemaData, true);\n                        var data = this.options.data && dataGetter(this.options.data) || this._rawData || [];\n                        var result = [];\n                        var getter;\n                        var value;\n                        var idx = 0;\n                        var distinct = {};\n                        if (name) {\n                            name = name.split('.')[0];\n                        }\n                        if (!restrictions.treeOp) {\n                            result.push({\n                                caption: cube.dimensions[name].caption || name,\n                                childrenCardinality: '1',\n                                dimensionUniqueName: name,\n                                hierarchyUniqueName: name,\n                                levelUniqueName: name,\n                                name: name,\n                                uniqueName: name\n                            });\n                            return result;\n                        }\n                        getter = kendo.getter(normalizeName(name), true);\n                        for (; idx < data.length; idx++) {\n                            value = getter(data[idx]);\n                            if ((value || value === 0) && !distinct[value]) {\n                                distinct[value] = true;\n                                result.push({\n                                    caption: value,\n                                    childrenCardinality: '0',\n                                    dimensionUniqueName: name,\n                                    hierarchyUniqueName: name,\n                                    levelUniqueName: name,\n                                    name: value,\n                                    uniqueName: value\n                                });\n                            }\n                        }\n                        return result;\n                    }, this)\n                };\n            },\n            options: {\n                serverSorting: true,\n                serverPaging: true,\n                serverFiltering: true,\n                serverGrouping: true,\n                serverAggregates: true\n            },\n            catalog: function (val) {\n                if (val === undefined) {\n                    return this.transport.catalog();\n                }\n                this.transport.catalog(val);\n                this._mergeState({});\n                this._axes = {};\n                this.data([]);\n            },\n            cube: function (val) {\n                if (val === undefined) {\n                    return this.transport.cube();\n                }\n                this.transport.cube(val);\n                this._axes = {};\n                this._mergeState({});\n                this.data([]);\n            },\n            axes: function () {\n                return this._axes;\n            },\n            columns: function (val) {\n                if (val === undefined) {\n                    return this._columns;\n                }\n                this._skipNormalize += 1;\n                this._clearAxesData = true;\n                this._columns = normalizeMembers(val);\n                this.query({\n                    columns: val,\n                    rows: this.rowsAxisDescriptors(),\n                    measures: this.measures(),\n                    sort: this.sort(),\n                    filter: this.filter()\n                });\n            },\n            rows: function (val) {\n                if (val === undefined) {\n                    return this._rows;\n                }\n                this._skipNormalize += 1;\n                this._clearAxesData = true;\n                this._rows = normalizeMembers(val);\n                this.query({\n                    columns: this.columnsAxisDescriptors(),\n                    rows: val,\n                    measures: this.measures(),\n                    sort: this.sort(),\n                    filter: this.filter()\n                });\n            },\n            measures: function (val) {\n                if (val === undefined) {\n                    return this._measures;\n                }\n                this._skipNormalize += 1;\n                this._clearAxesData = true;\n                this.query({\n                    columns: this.columnsAxisDescriptors(),\n                    rows: this.rowsAxisDescriptors(),\n                    measures: normalizeMeasures(val),\n                    sort: this.sort(),\n                    filter: this.filter()\n                });\n            },\n            measuresAxis: function () {\n                return this._measuresAxis || 'columns';\n            },\n            _expandPath: function (path, axis) {\n                var origin = axis === 'columns' ? 'columns' : 'rows';\n                var other = axis === 'columns' ? 'rows' : 'columns';\n                var members = normalizeMembers(path);\n                var memberToExpand = getName(members[members.length - 1]);\n                this._lastExpanded = origin;\n                members = descriptorsForMembers(this.axes()[origin], members, this.measures());\n                for (var idx = 0; idx < members.length; idx++) {\n                    var memberName = getName(members[idx]);\n                    if (memberName === memberToExpand) {\n                        if (members[idx].expand) {\n                            return;\n                        }\n                        members[idx].expand = true;\n                    } else {\n                        members[idx].expand = false;\n                    }\n                }\n                var descriptors = {};\n                descriptors[origin] = members;\n                descriptors[other] = this._descriptorsForAxis(other);\n                this._query(descriptors);\n            },\n            _descriptorsForAxis: function (axis) {\n                var axes = this.axes();\n                var descriptors = this[axis]() || [];\n                if (axes && axes[axis] && axes[axis].tuples && axes[axis].tuples[0]) {\n                    descriptors = descriptorsForAxes(axes[axis].tuples || []);\n                }\n                return descriptors;\n            },\n            columnsAxisDescriptors: function () {\n                return this._descriptorsForAxis('columns');\n            },\n            rowsAxisDescriptors: function () {\n                return this._descriptorsForAxis('rows');\n            },\n            _process: function (data, e) {\n                this._view = data;\n                e = e || {};\n                e.items = e.items || this._view;\n                this.trigger(CHANGE, e);\n            },\n            _query: function (options) {\n                var that = this;\n                if (!options) {\n                    this._skipNormalize += 1;\n                    this._clearAxesData = true;\n                }\n                return that.query(extend({}, {\n                    page: that.page(),\n                    pageSize: that.pageSize(),\n                    sort: that.sort(),\n                    filter: that.filter(),\n                    group: that.group(),\n                    aggregate: that.aggregate(),\n                    columns: this.columnsAxisDescriptors(),\n                    rows: this.rowsAxisDescriptors(),\n                    measures: this.measures()\n                }, options));\n            },\n            query: function (options) {\n                var state = this._mergeState(options);\n                if (this._data.length && this.cubeBuilder) {\n                    this._params(state);\n                    this._updateLocalData(this._pristineData);\n                    return $.Deferred().resolve().promise();\n                }\n                return this.read(state);\n            },\n            _mergeState: function (options) {\n                options = DataSource.fn._mergeState.call(this, options);\n                if (options !== undefined) {\n                    this._measures = normalizeMeasures(options.measures);\n                    if (options.columns) {\n                        options.columns = normalizeMembers(options.columns);\n                    } else if (!options.columns) {\n                        this._columns = [];\n                    }\n                    if (options.rows) {\n                        options.rows = normalizeMembers(options.rows);\n                    } else if (!options.rows) {\n                        this._rows = [];\n                    }\n                }\n                return options;\n            },\n            filter: function (val) {\n                if (val === undefined) {\n                    return this._filter;\n                }\n                this._skipNormalize += 1;\n                this._clearAxesData = true;\n                this._query({\n                    filter: val,\n                    page: 1\n                });\n            },\n            expandColumn: function (path) {\n                this._expandPath(path, 'columns');\n            },\n            expandRow: function (path) {\n                this._expandPath(path, 'rows');\n            },\n            success: function (data) {\n                var originalData;\n                if (this.cubeBuilder) {\n                    originalData = (this.reader.data(data) || []).slice(0);\n                }\n                DataSource.fn.success.call(this, data);\n                if (originalData) {\n                    this._pristineData = originalData;\n                }\n            },\n            _processResult: function (data, axes) {\n                if (this.cubeBuilder) {\n                    var processedData = this.cubeBuilder.process(data, this._requestData);\n                    data = processedData.data;\n                    axes = processedData.axes;\n                }\n                var columnIndexes, rowIndexes;\n                var tuples, resultAxis, measures, axisToSkip;\n                var columnDescriptors = this.columns();\n                var rowDescriptors = this.rows();\n                var hasColumnTuples = axes.columns && axes.columns.tuples;\n                if (!columnDescriptors.length && rowDescriptors.length && hasColumnTuples && (this._rowMeasures().length || !this.measures().length)) {\n                    axes = {\n                        columns: {},\n                        rows: axes.columns\n                    };\n                }\n                if (!columnDescriptors.length && !rowDescriptors.length && this.measuresAxis() === 'rows' && hasColumnTuples) {\n                    axes = {\n                        columns: {},\n                        rows: axes.columns\n                    };\n                }\n                this._axes = {\n                    columns: normalizeAxis(this._axes.columns),\n                    rows: normalizeAxis(this._axes.rows)\n                };\n                axes = {\n                    columns: normalizeAxis(axes.columns),\n                    rows: normalizeAxis(axes.rows)\n                };\n                columnIndexes = this._normalizeTuples(axes.columns.tuples, this._axes.columns.tuples, columnDescriptors, this._columnMeasures());\n                rowIndexes = this._normalizeTuples(axes.rows.tuples, this._axes.rows.tuples, rowDescriptors, this._rowMeasures());\n                if (this._skipNormalize > 0) {\n                    this._skipNormalize -= 1;\n                }\n                if (!this.cubeBuilder) {\n                    data = this._normalizeData({\n                        columnsLength: axes.columns.tuples.length,\n                        rowsLength: axes.rows.tuples.length,\n                        columnIndexes: columnIndexes,\n                        rowIndexes: rowIndexes,\n                        data: data\n                    });\n                }\n                if (this._lastExpanded == 'rows') {\n                    tuples = axes.columns.tuples;\n                    measures = this._columnMeasures();\n                    resultAxis = validateAxis(axes.columns, this._axes.columns, measures);\n                    if (resultAxis) {\n                        axisToSkip = 'columns';\n                        axes.columns = resultAxis;\n                        adjustDataByColumn(tuples, resultAxis.tuples, axes.rows.tuples.length, measures, data);\n                        if (!this.cubeBuilder) {\n                            data = this._normalizeData({\n                                columnsLength: membersCount(axes.columns.tuples, measures),\n                                rowsLength: axes.rows.tuples.length,\n                                data: data\n                            });\n                        }\n                    }\n                } else if (this._lastExpanded == 'columns') {\n                    tuples = axes.rows.tuples;\n                    measures = this._rowMeasures();\n                    resultAxis = validateAxis(axes.rows, this._axes.rows, measures);\n                    if (resultAxis) {\n                        axisToSkip = 'rows';\n                        axes.rows = resultAxis;\n                        adjustDataByRow(tuples, resultAxis.tuples, axes.columns.tuples.length, measures, data);\n                        if (!this.cubeBuilder) {\n                            data = this._normalizeData({\n                                columnsLength: membersCount(axes.rows.tuples, measures),\n                                rowsLength: axes.columns.tuples.length,\n                                data: data\n                            });\n                        }\n                    }\n                }\n                this._lastExpanded = null;\n                var result = this._mergeAxes(axes, data, axisToSkip);\n                this._axes = result.axes;\n                return result.data;\n            },\n            _readData: function (data) {\n                var axes = this.reader.axes(data);\n                var newData = this.reader.data(data);\n                if (this.cubeBuilder) {\n                    this._rawData = newData;\n                }\n                return this._processResult(newData, axes);\n            },\n            _createTuple: function (tuple, measure, buildRoot) {\n                var members = tuple.members;\n                var length = members.length;\n                var root = { members: [] };\n                var levelName, levelNum;\n                var name, parentName;\n                var hasChildren;\n                var hierarchy;\n                var caption;\n                var member;\n                var idx = 0;\n                if (measure) {\n                    length -= 1;\n                }\n                for (; idx < length; idx++) {\n                    member = members[idx];\n                    levelNum = Number(member.levelNum);\n                    name = member.name;\n                    parentName = member.parentName;\n                    caption = member.caption || name;\n                    hasChildren = member.hasChildren;\n                    hierarchy = member.hierarchy;\n                    levelName = member.levelName;\n                    if (buildRoot) {\n                        caption = 'All';\n                        if (levelNum === 0) {\n                            parentName = member.name;\n                        } else {\n                            levelNum -= 1;\n                        }\n                        hasChildren = true;\n                        name = hierarchy = levelName = parentName;\n                    }\n                    root.members.push({\n                        name: name,\n                        children: [],\n                        caption: caption,\n                        levelName: levelName,\n                        levelNum: levelNum.toString(),\n                        hasChildren: hasChildren,\n                        hierarchy: hierarchy,\n                        parentName: !buildRoot ? parentName : ''\n                    });\n                }\n                if (measure) {\n                    root.members.push({\n                        name: measure.name,\n                        children: []\n                    });\n                }\n                return root;\n            },\n            _hasRoot: function (target, source, descriptors) {\n                if (source.length) {\n                    return findExistingTuple(source, target).tuple;\n                }\n                var members = target.members;\n                var member;\n                var descriptor;\n                var isRoot = true;\n                var levelNum;\n                for (var idx = 0, length = members.length; idx < length; idx++) {\n                    member = members[idx];\n                    levelNum = Number(member.levelNum) || 0;\n                    descriptor = descriptors[idx];\n                    if (!(levelNum === 0 || descriptor && member.name === getName(descriptor))) {\n                        isRoot = false;\n                        break;\n                    }\n                }\n                return isRoot;\n            },\n            _mergeAxes: function (sourceAxes, data, axisToSkip) {\n                var columnMeasures = this._columnMeasures();\n                var rowMeasures = this._rowMeasures();\n                var axes = this.axes();\n                var startIndex, tuples;\n                var oldRowsLength = membersCount(axes.rows.tuples, rowMeasures);\n                var newRowsLength = sourceAxes.rows.tuples.length;\n                var oldColumnsLength = membersCount(axes.columns.tuples, columnMeasures);\n                var newColumnsLength = sourceAxes.columns.tuples.length;\n                if (axisToSkip == 'columns') {\n                    newColumnsLength = oldColumnsLength;\n                    tuples = sourceAxes.columns.tuples;\n                } else {\n                    tuples = parseSource(sourceAxes.columns.tuples, columnMeasures);\n                    data = prepareDataOnColumns(tuples, data);\n                }\n                var mergedColumns = mergeTuples(axes.columns.tuples, tuples, columnMeasures);\n                if (axisToSkip == 'rows') {\n                    newRowsLength = membersCount(sourceAxes.rows.tuples, rowMeasures);\n                    tuples = sourceAxes.rows.tuples;\n                } else {\n                    tuples = parseSource(sourceAxes.rows.tuples, rowMeasures);\n                    data = prepareDataOnRows(tuples, data);\n                }\n                var mergedRows = mergeTuples(axes.rows.tuples, tuples, rowMeasures);\n                axes.columns.tuples = mergedColumns.tuples;\n                axes.rows.tuples = mergedRows.tuples;\n                if (oldColumnsLength !== membersCount(axes.columns.tuples, columnMeasures)) {\n                    startIndex = mergedColumns.index + findDataIndex(mergedColumns.parsedRoot, mergedColumns.memberIndex, columnMeasures);\n                    var offset = oldColumnsLength + newColumnsLength;\n                    data = this._mergeColumnData(data, startIndex, newRowsLength, newColumnsLength, offset);\n                } else if (oldRowsLength !== membersCount(axes.rows.tuples, rowMeasures)) {\n                    startIndex = mergedRows.index + findDataIndex(mergedRows.parsedRoot, mergedRows.memberIndex, rowMeasures);\n                    data = this._mergeRowData(data, startIndex, newRowsLength, newColumnsLength);\n                }\n                if (axes.columns.tuples.length === 0 && axes.rows.tuples.length === 0) {\n                    data = [];\n                }\n                return {\n                    axes: axes,\n                    data: data\n                };\n            },\n            _mergeColumnData: function (newData, columnIndex, rowsLength, columnsLength, offset) {\n                var data = this.data().toJSON();\n                var rowIndex, index, drop = 0, toAdd;\n                var columnMeasures = Math.max(this._columnMeasures().length, 1);\n                rowsLength = Math.max(rowsLength, 1);\n                if (data.length > 0) {\n                    drop = columnMeasures;\n                    offset -= columnMeasures;\n                }\n                for (rowIndex = 0; rowIndex < rowsLength; rowIndex++) {\n                    index = columnIndex + rowIndex * offset;\n                    toAdd = newData.splice(0, columnsLength);\n                    toAdd.splice(0, drop);\n                    [].splice.apply(data, [\n                        index,\n                        0\n                    ].concat(toAdd));\n                }\n                return data;\n            },\n            _mergeRowData: function (newData, rowIndex, rowsLength, columnsLength) {\n                var data = this.data().toJSON();\n                var idx, dataIndex, toAdd;\n                var rowMeasures = Math.max(this._rowMeasures().length, 1);\n                columnsLength = Math.max(columnsLength, 1);\n                if (data.length > 0) {\n                    rowsLength -= rowMeasures;\n                    newData.splice(0, columnsLength * rowMeasures);\n                }\n                for (idx = 0; idx < rowsLength; idx++) {\n                    toAdd = newData.splice(0, columnsLength);\n                    dataIndex = rowIndex * columnsLength + idx * columnsLength;\n                    [].splice.apply(data, [\n                        dataIndex,\n                        0\n                    ].concat(toAdd));\n                }\n                return data;\n            },\n            _columnMeasures: function () {\n                var measures = this.measures();\n                var columnMeasures = [];\n                if (this.measuresAxis() === 'columns') {\n                    if (this.columns().length === 0) {\n                        columnMeasures = measures;\n                    } else if (measures.length > 1) {\n                        columnMeasures = measures;\n                    }\n                }\n                return columnMeasures;\n            },\n            _rowMeasures: function () {\n                var measures = this.measures();\n                var rowMeasures = [];\n                if (this.measuresAxis() === 'rows') {\n                    if (this.rows().length === 0) {\n                        rowMeasures = measures;\n                    } else if (measures.length > 1) {\n                        rowMeasures = measures;\n                    }\n                }\n                return rowMeasures;\n            },\n            _updateLocalData: function (data, state) {\n                if (this.cubeBuilder) {\n                    if (state) {\n                        this._requestData = state;\n                    }\n                    data = this._processResult(data);\n                }\n                this._data = this._observe(data);\n                this._ranges = [];\n                this._addRange(this._data);\n                this._total = this._data.length;\n                this._pristineTotal = this._total;\n                this._process(this._data);\n            },\n            data: function (value) {\n                var that = this;\n                if (value !== undefined) {\n                    this._pristineData = value.slice(0);\n                    this._updateLocalData(value, {\n                        columns: this.columns(),\n                        rows: this.rows(),\n                        measures: this.measures()\n                    });\n                } else {\n                    return that._data;\n                }\n            },\n            _normalizeTuples: function (tuples, source, descriptors, measures) {\n                var length = measures.length || 1;\n                var idx = 0;\n                var roots = [];\n                var indexes = {};\n                var measureIdx = 0;\n                var tuple, memberIdx, last;\n                if (!tuples.length) {\n                    return;\n                }\n                if (this._skipNormalize <= 0 && !this._hasRoot(tuples[0], source, descriptors)) {\n                    this._skipNormalize = 0;\n                    for (; idx < length; idx++) {\n                        roots.push(this._createTuple(tuples[0], measures[idx], true));\n                        indexes[idx] = idx;\n                    }\n                    tuples.splice.apply(tuples, [\n                        0,\n                        tuples.length\n                    ].concat(roots).concat(tuples));\n                    idx = length;\n                }\n                if (measures.length) {\n                    last = tuple = tuples[idx];\n                    memberIdx = tuple.members.length - 1;\n                    while (tuple) {\n                        if (measureIdx >= length) {\n                            measureIdx = 0;\n                        }\n                        if (tuple.members[memberIdx].name !== measures[measureIdx].name) {\n                            tuples.splice(idx, 0, this._createTuple(tuple, measures[measureIdx]));\n                            indexes[idx] = idx;\n                        }\n                        idx += 1;\n                        measureIdx += 1;\n                        tuple = tuples[idx];\n                        if (length > measureIdx && (!tuple || tupleName(last, memberIdx - 1) !== tupleName(tuple, memberIdx - 1))) {\n                            for (; measureIdx < length; measureIdx++) {\n                                tuples.splice(idx, 0, this._createTuple(last, measures[measureIdx]));\n                                indexes[idx] = idx;\n                                idx += 1;\n                            }\n                            tuple = tuples[idx];\n                        }\n                        last = tuple;\n                    }\n                }\n                return indexes;\n            },\n            _addMissingDataItems: function (result, metadata) {\n                while (metadata.rowIndexes[parseInt(result.length / metadata.columnsLength, 10)] !== undefined) {\n                    for (var idx = 0; idx < metadata.columnsLength; idx++) {\n                        result = addEmptyDataItem(result);\n                    }\n                }\n                while (metadata.columnIndexes[result.length % metadata.columnsLength] !== undefined) {\n                    result = addEmptyDataItem(result);\n                }\n                return result;\n            },\n            _normalizeOrdinals: function (result, dataItem, metadata) {\n                var lastOrdinal = metadata.lastOrdinal;\n                if (!dataItem) {\n                    return addEmptyDataItem(result);\n                }\n                if (dataItem.ordinal - lastOrdinal > 1) {\n                    lastOrdinal += 1;\n                    while (lastOrdinal < dataItem.ordinal && result.length < metadata.length) {\n                        result = this._addMissingDataItems(addEmptyDataItem(result), metadata);\n                        lastOrdinal += 1;\n                    }\n                }\n                dataItem.ordinal = result.length;\n                result[result.length] = dataItem;\n                return result;\n            },\n            _normalizeData: function (options) {\n                var data = options.data;\n                var dataIdx = 0;\n                var dataItem;\n                var result = [];\n                var lastOrdinal;\n                var length;\n                options.lastOrdinal = 0;\n                options.columnIndexes = options.columnIndexes || {};\n                options.rowIndexes = options.rowIndexes || {};\n                options.columnsLength = options.columnsLength || 1;\n                options.rowsLength = options.rowsLength || 1;\n                options.length = options.columnsLength * options.rowsLength;\n                length = options.length;\n                if (data.length === length) {\n                    return data;\n                }\n                while (result.length < length) {\n                    dataItem = data[dataIdx++];\n                    if (dataItem) {\n                        lastOrdinal = dataItem.ordinal;\n                    }\n                    result = this._normalizeOrdinals(this._addMissingDataItems(result, options), dataItem, options);\n                    options.lastOrdinal = lastOrdinal;\n                }\n                return result;\n            },\n            discover: function (options, converter) {\n                var that = this, transport = that.transport;\n                return $.Deferred(function (deferred) {\n                    transport.discover(extend({\n                        success: function (response) {\n                            response = that.reader.parse(response);\n                            if (that._handleCustomErrors(response)) {\n                                return;\n                            }\n                            if (converter) {\n                                response = converter(response);\n                            }\n                            deferred.resolve(response);\n                        },\n                        error: function (response, status, error) {\n                            deferred.reject(response);\n                            that.error(response, status, error);\n                        }\n                    }, options));\n                }).promise().done(function () {\n                    that.trigger('schemaChange');\n                });\n            },\n            schemaMeasures: function () {\n                var that = this;\n                return that.discover({\n                    data: {\n                        command: 'schemaMeasures',\n                        restrictions: {\n                            catalogName: that.transport.catalog(),\n                            cubeName: that.transport.cube()\n                        }\n                    }\n                }, function (response) {\n                    return that.reader.measures(response);\n                });\n            },\n            schemaKPIs: function () {\n                var that = this;\n                return that.discover({\n                    data: {\n                        command: 'schemaKPIs',\n                        restrictions: {\n                            catalogName: that.transport.catalog(),\n                            cubeName: that.transport.cube()\n                        }\n                    }\n                }, function (response) {\n                    return that.reader.kpis(response);\n                });\n            },\n            schemaDimensions: function () {\n                var that = this;\n                return that.discover({\n                    data: {\n                        command: 'schemaDimensions',\n                        restrictions: {\n                            catalogName: that.transport.catalog(),\n                            cubeName: that.transport.cube()\n                        }\n                    }\n                }, function (response) {\n                    return that.reader.dimensions(response);\n                });\n            },\n            schemaHierarchies: function (dimensionName) {\n                var that = this;\n                return that.discover({\n                    data: {\n                        command: 'schemaHierarchies',\n                        restrictions: {\n                            catalogName: that.transport.catalog(),\n                            cubeName: that.transport.cube(),\n                            dimensionUniqueName: dimensionName\n                        }\n                    }\n                }, function (response) {\n                    return that.reader.hierarchies(response);\n                });\n            },\n            schemaLevels: function (hierarchyName) {\n                var that = this;\n                return that.discover({\n                    data: {\n                        command: 'schemaLevels',\n                        restrictions: {\n                            catalogName: that.transport.catalog(),\n                            cubeName: that.transport.cube(),\n                            hierarchyUniqueName: hierarchyName\n                        }\n                    }\n                }, function (response) {\n                    return that.reader.levels(response);\n                });\n            },\n            schemaCubes: function () {\n                var that = this;\n                return that.discover({\n                    data: {\n                        command: 'schemaCubes',\n                        restrictions: { catalogName: that.transport.catalog() }\n                    }\n                }, function (response) {\n                    return that.reader.cubes(response);\n                });\n            },\n            schemaCatalogs: function () {\n                var that = this;\n                return that.discover({ data: { command: 'schemaCatalogs' } }, function (response) {\n                    return that.reader.catalogs(response);\n                });\n            },\n            schemaMembers: function (restrictions) {\n                var that = this;\n                var success = function (restrictions) {\n                    return function (response) {\n                        return that.reader.members(response, restrictions);\n                    };\n                }(restrictions);\n                return that.discover({\n                    data: {\n                        command: 'schemaMembers',\n                        restrictions: extend({\n                            catalogName: that.transport.catalog(),\n                            cubeName: that.transport.cube()\n                        }, restrictions)\n                    }\n                }, success);\n            },\n            _params: function (data) {\n                if (this._clearAxesData) {\n                    this._axes = {};\n                    this._data = this._observe([]);\n                    this._clearAxesData = false;\n                    this.trigger(STATERESET);\n                }\n                var options = DataSource.fn._params.call(this, data);\n                options = extend({\n                    measures: this.measures(),\n                    measuresAxis: this.measuresAxis(),\n                    columns: this.columns(),\n                    rows: this.rows()\n                }, options);\n                if (this.cubeBuilder) {\n                    this._requestData = options;\n                }\n                return options;\n            }\n        });\n        function addEmptyDataItem(result) {\n            result[result.length] = {\n                value: '',\n                fmtValue: '',\n                ordinal: result.length\n            };\n            return result;\n        }\n        function validateAxis(newAxis, axis, measures) {\n            if (newAxis.tuples.length < membersCount(axis.tuples, measures)) {\n                return axis;\n            }\n            return;\n        }\n        function adjustDataByColumn(sourceTuples, targetTuples, rowsLength, measures, data) {\n            var columnIdx, rowIdx, dataIdx;\n            var columnsLength = sourceTuples.length;\n            var targetColumnsLength = membersCount(targetTuples, measures);\n            var measuresLength = measures.length || 1;\n            for (rowIdx = 0; rowIdx < rowsLength; rowIdx++) {\n                for (columnIdx = 0; columnIdx < columnsLength; columnIdx++) {\n                    dataIdx = tupleIndex(sourceTuples[columnIdx], targetTuples) * measuresLength;\n                    dataIdx += columnIdx % measuresLength;\n                    data[rowIdx * columnsLength + columnIdx].ordinal = rowIdx * targetColumnsLength + dataIdx;\n                }\n            }\n        }\n        function adjustDataByRow(sourceTuples, targetTuples, columnsLength, measures, data) {\n            var columnIdx, rowIdx, dataIdx;\n            var rowsLength = sourceTuples.length;\n            var measuresLength = measures.length || 1;\n            for (rowIdx = 0; rowIdx < rowsLength; rowIdx++) {\n                dataIdx = tupleIndex(sourceTuples[rowIdx], targetTuples);\n                dataIdx *= measuresLength;\n                dataIdx += rowIdx % measuresLength;\n                for (columnIdx = 0; columnIdx < columnsLength; columnIdx++) {\n                    data[rowIdx * columnsLength + columnIdx].ordinal = dataIdx * columnsLength + columnIdx;\n                }\n            }\n        }\n        function tupleIndex(tuple, collection) {\n            return findExistingTuple(collection, tuple).index;\n        }\n        function membersCount(tuples, measures) {\n            if (!tuples.length) {\n                return 0;\n            }\n            var queue = tuples.slice();\n            var current = queue.shift();\n            var result = 1;\n            while (current) {\n                if (current.members) {\n                    [].push.apply(queue, current.members);\n                } else if (current.children) {\n                    if (!current.measure) {\n                        result += current.children.length;\n                    }\n                    [].push.apply(queue, current.children);\n                }\n                current = queue.shift();\n            }\n            if (measures.length) {\n                result = result * measures.length;\n            }\n            return result;\n        }\n        function normalizeAxis(axis) {\n            if (!axis) {\n                axis = { tuples: [] };\n            }\n            if (!axis.tuples) {\n                axis.tuples = [];\n            }\n            return axis;\n        }\n        function findDataIndex(tuple, memberIndex, measures) {\n            if (!tuple) {\n                return 0;\n            }\n            var measuresLength = Math.max(measures.length, 1);\n            var tuples = tuple.members.slice(0, memberIndex);\n            var current = tuples.shift();\n            var counter = measuresLength;\n            while (current) {\n                if (current.name === MEASURES) {\n                    counter += measuresLength - 1;\n                } else if (current.children) {\n                    [].push.apply(tuples, current.children);\n                } else {\n                    counter++;\n                    [].push.apply(tuples, current.members);\n                }\n                current = tuples.shift();\n            }\n            return counter;\n        }\n        function mergeTuples(target, source, measures) {\n            if (!source[0]) {\n                return {\n                    parsedRoot: null,\n                    tuples: target,\n                    memberIndex: 0,\n                    index: 0\n                };\n            }\n            var result = findExistingTuple(target, source[0]);\n            if (!result.tuple) {\n                return {\n                    parsedRoot: null,\n                    tuples: source,\n                    memberIndex: 0,\n                    index: 0\n                };\n            }\n            var targetMembers = result.tuple.members;\n            var sourceMembers = source[0].members;\n            var memberIndex = -1;\n            if (targetMembers.length !== sourceMembers.length) {\n                return {\n                    parsedRoot: null,\n                    tuples: source,\n                    memberIndex: 0,\n                    index: 0\n                };\n            }\n            for (var idx = 0, length = targetMembers.length; idx < length; idx++) {\n                if (!targetMembers[idx].measure && sourceMembers[idx].children[0]) {\n                    if (memberIndex == -1 && sourceMembers[idx].children.length) {\n                        memberIndex = idx;\n                    }\n                    targetMembers[idx].children = sourceMembers[idx].children;\n                }\n            }\n            measures = Math.max(measures.length, 1);\n            return {\n                parsedRoot: result.tuple,\n                index: result.index * measures,\n                memberIndex: memberIndex,\n                tuples: target\n            };\n        }\n        function equalTuples(first, second) {\n            var equal = true;\n            var idx, length;\n            first = first.members;\n            second = second.members;\n            for (idx = 0, length = first.length; idx < length; idx++) {\n                if (first[idx].measure || second[idx].measure) {\n                    continue;\n                }\n                equal = equal && getName(first[idx]) === getName(second[idx]);\n            }\n            return equal;\n        }\n        function findExistingTuple(tuples, toFind) {\n            var idx, length, tuple, found, counter = 0;\n            var memberIndex, membersLength, member;\n            for (idx = 0, length = tuples.length; idx < length; idx++) {\n                tuple = tuples[idx];\n                if (equalTuples(tuple, toFind)) {\n                    return {\n                        tuple: tuple,\n                        index: counter\n                    };\n                }\n                counter++;\n                for (memberIndex = 0, membersLength = tuple.members.length; memberIndex < membersLength; memberIndex++) {\n                    member = tuple.members[memberIndex];\n                    if (member.measure) {\n                        continue;\n                    }\n                    found = findExistingTuple(member.children, toFind);\n                    counter += found.index;\n                    if (found.tuple) {\n                        return {\n                            tuple: found.tuple,\n                            index: counter\n                        };\n                    }\n                }\n            }\n            return { index: counter };\n        }\n        function addMembers(members, map) {\n            var member, i, len, path = '';\n            for (i = 0, len = members.length; i < len; i++) {\n                member = members[i];\n                path += member.name;\n                if (!map[path]) {\n                    map[path] = member;\n                }\n            }\n        }\n        function findParentMember(tuple, map) {\n            var members = tuple.members;\n            var i, len, member, path = '';\n            var parentPath = '';\n            var parentMember;\n            for (i = 0, len = members.length; i < len; i++) {\n                member = members[i];\n                if (parentMember) {\n                    if (map[path + member.name]) {\n                        path += member.name;\n                        parentMember = map[path];\n                        continue;\n                    } else if (map[path + member.parentName]) {\n                        return map[path + member.parentName];\n                    } else if (map[parentPath + member.parentName]) {\n                        return map[parentPath + member.parentName];\n                    } else {\n                        return map[parentPath];\n                    }\n                }\n                path += member.name;\n                parentMember = map[member.parentName];\n                if (!parentMember) {\n                    parentMember = map[path];\n                    if (!parentMember) {\n                        return null;\n                    }\n                }\n                if (parentMember) {\n                    parentPath += parentMember.name;\n                }\n            }\n            return parentMember;\n        }\n        function measurePosition(tuple, measures) {\n            if (measures.length === 0) {\n                return -1;\n            }\n            var measure = measures[0];\n            var members = tuple.members;\n            for (var idx = 0, len = members.length; idx < len; idx++) {\n                if (members[idx].name == measure.name) {\n                    return idx;\n                }\n            }\n        }\n        function normalizeTupleMeasures(tuple, index) {\n            if (index < 0) {\n                return;\n            }\n            var member = {\n                name: MEASURES,\n                measure: true,\n                children: [$.extend({\n                        members: [],\n                        dataIndex: tuple.dataIndex\n                    }, tuple.members[index])]\n            };\n            tuple.members.splice(index, 1, member);\n            tuple.dataIndex = undefined;\n        }\n        function parseSource(tuples, measures) {\n            if (tuples.length < 1) {\n                return [];\n            }\n            var result = [];\n            var map = {};\n            var measureIndex = measurePosition(tuples[0], measures);\n            for (var i = 0; i < tuples.length; i++) {\n                var tuple = tuples[i];\n                tuple.dataIndex = i;\n                normalizeTupleMeasures(tuple, measureIndex);\n                var parentMember = findParentMember(tuple, map);\n                if (parentMember) {\n                    if (measureIndex < 0 || !parentMember.measure) {\n                        parentMember.children.push(tuple);\n                    } else {\n                        parentMember.children.push(tuple.members[measureIndex].children[0]);\n                    }\n                } else {\n                    result.push(tuple);\n                }\n                addMembers(tuple.members, map);\n            }\n            return result;\n        }\n        function prepareDataOnRows(tuples, data) {\n            if (!tuples || !tuples.length) {\n                return data;\n            }\n            var result = [];\n            var indices = buildDataIndices(tuples);\n            var rowsLength = indices.length;\n            var columnsLength = Math.max(data.length / rowsLength, 1);\n            var rowIndex, columnIndex, targetIndex, sourceIndex;\n            var calcIndex;\n            for (rowIndex = 0; rowIndex < rowsLength; rowIndex++) {\n                targetIndex = columnsLength * rowIndex;\n                sourceIndex = columnsLength * indices[rowIndex];\n                for (columnIndex = 0; columnIndex < columnsLength; columnIndex++) {\n                    calcIndex = parseInt(sourceIndex + columnIndex, 10);\n                    result[parseInt(targetIndex + columnIndex, 10)] = data[calcIndex] || {\n                        value: '',\n                        fmtValue: '',\n                        ordinal: calcIndex\n                    };\n                }\n            }\n            return result;\n        }\n        function prepareDataOnColumns(tuples, data) {\n            if (!tuples || !tuples.length) {\n                return data;\n            }\n            var result = [];\n            var indices = buildDataIndices(tuples);\n            var columnsLength = indices.length;\n            var rowsLength = Math.max(data.length / columnsLength, 1);\n            var columnIndex, rowIndex, dataIndex, calcIndex;\n            for (rowIndex = 0; rowIndex < rowsLength; rowIndex++) {\n                dataIndex = columnsLength * rowIndex;\n                for (columnIndex = 0; columnIndex < columnsLength; columnIndex++) {\n                    calcIndex = indices[columnIndex] + dataIndex;\n                    result[dataIndex + columnIndex] = data[calcIndex] || {\n                        value: '',\n                        fmtValue: '',\n                        ordinal: calcIndex\n                    };\n                }\n            }\n            return result;\n        }\n        function buildDataIndices(tuples) {\n            tuples = tuples.slice();\n            var result = [];\n            var tuple = tuples.shift();\n            var idx, length, spliceIndex, children, member;\n            while (tuple) {\n                if (tuple.dataIndex !== undefined) {\n                    result.push(tuple.dataIndex);\n                }\n                spliceIndex = 0;\n                for (idx = 0, length = tuple.members.length; idx < length; idx++) {\n                    member = tuple.members[idx];\n                    children = member.children;\n                    if (member.measure) {\n                        [].splice.apply(tuples, [\n                            0,\n                            0\n                        ].concat(children));\n                    } else {\n                        [].splice.apply(tuples, [\n                            spliceIndex,\n                            0\n                        ].concat(children));\n                    }\n                    spliceIndex += children.length;\n                }\n                tuple = tuples.shift();\n            }\n            return result;\n        }\n        PivotDataSource.create = function (options) {\n            options = options && options.push ? { data: options } : options;\n            var dataSource = options || {}, data = dataSource.data;\n            dataSource.data = data;\n            if (!(dataSource instanceof PivotDataSource) && dataSource instanceof kendo.data.DataSource) {\n                throw new Error('Incorrect DataSource type. Only PivotDataSource instances are supported');\n            }\n            return dataSource instanceof PivotDataSource ? dataSource : new PivotDataSource(dataSource);\n        };\n        function baseHierarchyPath(memberName) {\n            var parts = memberName.split('.');\n            if (parts.length > 2) {\n                return parts[0] + '.' + parts[1];\n            }\n            return memberName;\n        }\n        function expandMemberDescriptor(names, sort) {\n            var idx = names.length - 1;\n            var name = names[idx];\n            var sortDescriptor;\n            sortDescriptor = sortDescriptorForMember(sort, name);\n            if (sortDescriptor && sortDescriptor.dir) {\n                name = 'ORDER(' + name + '.Children,' + sortDescriptor.field + '.CurrentMember.MEMBER_CAPTION,' + sortDescriptor.dir + ')';\n            } else {\n                name += '.Children';\n            }\n            names[idx] = name;\n            return names;\n        }\n        function sortDescriptorForMember(sort, member) {\n            for (var idx = 0, length = sort.length; idx < length; idx++) {\n                if (member.indexOf(sort[idx].field) === 0) {\n                    return sort[idx];\n                }\n            }\n            return null;\n        }\n        function crossJoin(names) {\n            var result = 'CROSSJOIN({';\n            var r;\n            if (names.length > 2) {\n                r = names.pop();\n                result += crossJoin(names);\n            } else {\n                result += names.shift();\n                r = names.pop();\n            }\n            result += '},{';\n            result += r;\n            result += '})';\n            return result;\n        }\n        function crossJoinCommand(members, measures) {\n            var tmp = members.slice(0);\n            if (measures.length > 1) {\n                tmp.push('{' + measureNames(measures).join(',') + '}');\n            }\n            return crossJoin(tmp);\n        }\n        function measureNames(measures) {\n            var idx = 0;\n            var length = measures.length;\n            var result = [];\n            var measure;\n            for (; idx < length; idx++) {\n                measure = measures[idx];\n                result.push(measure.name !== undefined ? measure.name : measure);\n            }\n            return result;\n        }\n        function getName(name) {\n            name = name.name || name;\n            if (toString.call(name) === '[object Array]') {\n                name = name[name.length - 1];\n            }\n            return name;\n        }\n        function getRootNames(members) {\n            var length = members.length;\n            var names = [];\n            var idx = 0;\n            for (; idx < length; idx++) {\n                names.push(members[idx].name[0]);\n            }\n            return names;\n        }\n        function mapNames(names, rootNames) {\n            var name;\n            var rootName;\n            var j;\n            var idx = 0;\n            var length = names.length;\n            var rootLength = rootNames.length;\n            rootNames = rootNames.slice(0);\n            for (; idx < length; idx++) {\n                name = names[idx];\n                for (j = 0; j < rootLength; j++) {\n                    rootName = baseHierarchyPath(rootNames[j]);\n                    if (name.indexOf(rootName) !== -1) {\n                        rootNames[j] = name;\n                        break;\n                    }\n                }\n            }\n            return {\n                names: rootNames,\n                expandedIdx: j,\n                uniquePath: rootNames.slice(0, j + 1).join('')\n            };\n        }\n        function parseDescriptors(members) {\n            var expanded = [];\n            var child = [];\n            var root = [];\n            var member;\n            var j, l;\n            var idx = 0;\n            var length = members.length;\n            var name;\n            var hierarchyName;\n            var found;\n            for (; idx < length; idx++) {\n                member = members[idx];\n                name = member.name;\n                found = false;\n                if (toString.call(name) !== '[object Array]') {\n                    member.name = name = [name];\n                }\n                if (name.length > 1) {\n                    child.push(member);\n                } else {\n                    hierarchyName = baseHierarchyPath(name[0]);\n                    for (j = 0, l = root.length; j < l; j++) {\n                        if (root[j].name[0].indexOf(hierarchyName) === 0) {\n                            found = true;\n                            break;\n                        }\n                    }\n                    if (!found) {\n                        root.push(member);\n                    }\n                    if (member.expand) {\n                        expanded.push(member);\n                    }\n                }\n            }\n            expanded = expanded.concat(child);\n            return {\n                root: root,\n                expanded: expanded\n            };\n        }\n        function serializeMembers(members, measures, sort) {\n            var command = '';\n            members = members || [];\n            var expanded = parseDescriptors(members);\n            var root = expanded.root;\n            var rootNames = getRootNames(root);\n            var crossJoinCommands = [];\n            expanded = expanded.expanded;\n            var length = expanded.length;\n            var idx = 0;\n            var memberName;\n            var names = [];\n            if (rootNames.length > 1 || measures.length > 1) {\n                crossJoinCommands.push(crossJoinCommand(rootNames, measures));\n                for (; idx < length; idx++) {\n                    memberName = expandMemberDescriptor(expanded[idx].name, sort);\n                    names = mapNames(memberName, rootNames).names;\n                    crossJoinCommands.push(crossJoinCommand(names, measures));\n                }\n                command += crossJoinCommands.join(',');\n            } else {\n                for (; idx < length; idx++) {\n                    memberName = expandMemberDescriptor(expanded[idx].name, sort);\n                    names.push(memberName[0]);\n                }\n                command += rootNames.concat(names).join(',');\n            }\n            return command;\n        }\n        var filterFunctionFormats = {\n            contains: ', InStr({0}.CurrentMember.MEMBER_CAPTION,\"{1}\") > 0',\n            doesnotcontain: ', InStr({0}.CurrentMember.MEMBER_CAPTION,\"{1}\")',\n            startswith: ', Left({0}.CurrentMember.MEMBER_CAPTION,Len(\"{1}\"))=\"{1}\"',\n            endswith: ', Right({0}.CurrentMember.MEMBER_CAPTION,Len(\"{1}\"))=\"{1}\"',\n            eq: ', {0}.CurrentMember.MEMBER_CAPTION = \"{1}\"',\n            neq: ', {0}.CurrentMember.MEMBER_CAPTION = \"{1}\"'\n        };\n        function serializeExpression(expression) {\n            var command = '';\n            var value = expression.value;\n            var field = expression.field;\n            var operator = expression.operator;\n            if (operator == 'in') {\n                command += '{';\n                command += value;\n                command += '}';\n            } else {\n                command += operator == 'neq' || operator == 'doesnotcontain' ? '-' : '';\n                command += 'Filter(';\n                command += field + '.MEMBERS';\n                command += kendo.format(filterFunctionFormats[operator], field, value);\n                command += ')';\n            }\n            return command;\n        }\n        function serializeFilters(filter, cube) {\n            var command = '', current;\n            var filters = filter.filters;\n            var length = filters.length;\n            var idx;\n            for (idx = length - 1; idx >= 0; idx--) {\n                current = 'SELECT (';\n                current += serializeExpression(filters[idx]);\n                current += ') ON 0';\n                if (idx == length - 1) {\n                    current += ' FROM [' + cube + ']';\n                    command = current;\n                } else {\n                    command = current + ' FROM ( ' + command + ' )';\n                }\n            }\n            return command;\n        }\n        function serializeOptions(parentTagName, options, capitalize) {\n            var result = '';\n            if (options) {\n                result += '<' + parentTagName + '>';\n                var value;\n                for (var key in options) {\n                    value = options[key];\n                    if (capitalize) {\n                        key = key.replace(/([A-Z]+(?=$|[A-Z][a-z])|[A-Z]?[a-z]+)/g, '$1_').toUpperCase().replace(/_$/, '');\n                    }\n                    result += '<' + key + '>' + value + '</' + key + '>';\n                }\n                result += '</' + parentTagName + '>';\n            } else {\n                result += '<' + parentTagName + '/>';\n            }\n            return result;\n        }\n        var xmlaDiscoverCommands = {\n            schemaCubes: 'MDSCHEMA_CUBES',\n            schemaCatalogs: 'DBSCHEMA_CATALOGS',\n            schemaMeasures: 'MDSCHEMA_MEASURES',\n            schemaDimensions: 'MDSCHEMA_DIMENSIONS',\n            schemaHierarchies: 'MDSCHEMA_HIERARCHIES',\n            schemaLevels: 'MDSCHEMA_LEVELS',\n            schemaMembers: 'MDSCHEMA_MEMBERS',\n            schemaKPIs: 'MDSCHEMA_KPIS'\n        };\n        var convertersMap = {\n            read: function (options) {\n                var command = '<Envelope xmlns=\"http://schemas.xmlsoap.org/soap/envelope/\"><Header/><Body><Execute xmlns=\"urn:schemas-microsoft-com:xml-analysis\"><Command><Statement>';\n                command += 'SELECT NON EMPTY {';\n                var columns = options.columns || [];\n                var rows = options.rows || [];\n                var measures = options.measures || [];\n                var measuresRowAxis = options.measuresAxis === 'rows';\n                var sort = options.sort || [];\n                if (!columns.length && rows.length && (!measures.length || measures.length && measuresRowAxis)) {\n                    columns = rows;\n                    rows = [];\n                    measuresRowAxis = false;\n                }\n                if (!columns.length && !rows.length) {\n                    measuresRowAxis = false;\n                }\n                if (columns.length) {\n                    command += serializeMembers(columns, !measuresRowAxis ? measures : [], sort);\n                } else if (measures.length && !measuresRowAxis) {\n                    command += measureNames(measures).join(',');\n                }\n                command += '} DIMENSION PROPERTIES CHILDREN_CARDINALITY, PARENT_UNIQUE_NAME ON COLUMNS';\n                if (rows.length || measuresRowAxis && measures.length > 1) {\n                    command += ', NON EMPTY {';\n                    if (rows.length) {\n                        command += serializeMembers(rows, measuresRowAxis ? measures : [], sort);\n                    } else {\n                        command += measureNames(measures).join(',');\n                    }\n                    command += '} DIMENSION PROPERTIES CHILDREN_CARDINALITY, PARENT_UNIQUE_NAME ON ROWS';\n                }\n                if (options.filter) {\n                    command += ' FROM ';\n                    command += '(';\n                    command += serializeFilters(options.filter, options.connection.cube);\n                    command += ')';\n                } else {\n                    command += ' FROM [' + options.connection.cube + ']';\n                }\n                if (measures.length == 1 && columns.length) {\n                    command += ' WHERE (' + measureNames(measures).join(',') + ')';\n                }\n                command += '</Statement></Command><Properties><PropertyList><Catalog>' + options.connection.catalog + '</Catalog><Format>Multidimensional</Format></PropertyList></Properties></Execute></Body></Envelope>';\n                return command.replace(/\\&/g, '&amp;');\n            },\n            discover: function (options) {\n                options = options || {};\n                var command = '<Envelope xmlns=\"http://schemas.xmlsoap.org/soap/envelope/\"><Header/><Body><Discover xmlns=\"urn:schemas-microsoft-com:xml-analysis\">';\n                command += '<RequestType>' + (xmlaDiscoverCommands[options.command] || options.command) + '</RequestType>';\n                command += '<Restrictions>' + serializeOptions('RestrictionList', options.restrictions, true) + '</Restrictions>';\n                if (options.connection && options.connection.catalog) {\n                    options.properties = $.extend({}, { Catalog: options.connection.catalog }, options.properties);\n                }\n                command += '<Properties>' + serializeOptions('PropertyList', options.properties) + '</Properties>';\n                command += '</Discover></Body></Envelope>';\n                return command;\n            }\n        };\n        var XmlaTransport = kendo.data.RemoteTransport.extend({\n            init: function (options) {\n                var originalOptions = options;\n                options = this.options = extend(true, {}, this.options, options);\n                kendo.data.RemoteTransport.call(this, options);\n                if (isFunction(originalOptions.discover)) {\n                    this.discover = originalOptions.discover;\n                } else if (typeof originalOptions.discover === 'string') {\n                    this.options.discover = { url: originalOptions.discover };\n                } else if (!originalOptions.discover) {\n                    this.options.discover = this.options.read;\n                }\n            },\n            setup: function (options, type) {\n                options.data = options.data || {};\n                $.extend(true, options.data, { connection: this.options.connection });\n                return kendo.data.RemoteTransport.fn.setup.call(this, options, type);\n            },\n            options: {\n                read: {\n                    dataType: 'text',\n                    contentType: 'text/xml',\n                    type: 'POST'\n                },\n                discover: {\n                    dataType: 'text',\n                    contentType: 'text/xml',\n                    type: 'POST'\n                },\n                parameterMap: function (options, type) {\n                    return convertersMap[type](options, type);\n                }\n            },\n            discover: function (options) {\n                return $.ajax(this.setup(options, 'discover'));\n            }\n        });\n        function asArray(object) {\n            if (object == null) {\n                return [];\n            }\n            var type = toString.call(object);\n            if (type !== '[object Array]') {\n                return [object];\n            }\n            return object;\n        }\n        function translateAxis(axis) {\n            var result = { tuples: [] };\n            var tuples = asArray(kendo.getter('Tuples.Tuple', true)(axis));\n            var captionGetter = kendo.getter('Caption[\\'#text\\']');\n            var unameGetter = kendo.getter('UName[\\'#text\\']');\n            var levelNameGetter = kendo.getter('LName[\\'#text\\']');\n            var levelNumGetter = kendo.getter('LNum[\\'#text\\']');\n            var childrenGetter = kendo.getter('CHILDREN_CARDINALITY[\\'#text\\']', true);\n            var hierarchyGetter = kendo.getter('[\\'@Hierarchy\\']');\n            var parentNameGetter = kendo.getter('PARENT_UNIQUE_NAME[\\'#text\\']', true);\n            for (var idx = 0; idx < tuples.length; idx++) {\n                var members = [];\n                var member = asArray(tuples[idx].Member);\n                for (var memberIdx = 0; memberIdx < member.length; memberIdx++) {\n                    members.push({\n                        children: [],\n                        caption: captionGetter(member[memberIdx]),\n                        name: unameGetter(member[memberIdx]),\n                        levelName: levelNameGetter(member[memberIdx]),\n                        levelNum: levelNumGetter(member[memberIdx]),\n                        hasChildren: parseInt(childrenGetter(member[memberIdx]), 10) > 0,\n                        parentName: parentNameGetter(member[memberIdx]),\n                        hierarchy: hierarchyGetter(member[memberIdx])\n                    });\n                }\n                result.tuples.push({ members: members });\n            }\n            return result;\n        }\n        var schemaDataReaderMap = {\n            cubes: {\n                name: kendo.getter('CUBE_NAME[\\'#text\\']', true),\n                caption: kendo.getter('CUBE_CAPTION[\\'#text\\']', true),\n                description: kendo.getter('DESCRIPTION[\\'#text\\']', true),\n                type: kendo.getter('CUBE_TYPE[\\'#text\\']', true)\n            },\n            catalogs: {\n                name: kendo.getter('CATALOG_NAME[\\'#text\\']', true),\n                description: kendo.getter('DESCRIPTION[\\'#text\\']', true)\n            },\n            measures: {\n                name: kendo.getter('MEASURE_NAME[\\'#text\\']', true),\n                caption: kendo.getter('MEASURE_CAPTION[\\'#text\\']', true),\n                uniqueName: kendo.getter('MEASURE_UNIQUE_NAME[\\'#text\\']', true),\n                description: kendo.getter('DESCRIPTION[\\'#text\\']', true),\n                aggregator: kendo.getter('MEASURE_AGGREGATOR[\\'#text\\']', true),\n                groupName: kendo.getter('MEASUREGROUP_NAME[\\'#text\\']', true),\n                displayFolder: kendo.getter('MEASURE_DISPLAY_FOLDER[\\'#text\\']', true),\n                defaultFormat: kendo.getter('DEFAULT_FORMAT_STRING[\\'#text\\']', true)\n            },\n            kpis: {\n                name: kendo.getter('KPI_NAME[\\'#text\\']', true),\n                caption: kendo.getter('KPI_CAPTION[\\'#text\\']', true),\n                value: kendo.getter('KPI_VALUE[\\'#text\\']', true),\n                goal: kendo.getter('KPI_GOAL[\\'#text\\']', true),\n                status: kendo.getter('KPI_STATUS[\\'#text\\']', true),\n                trend: kendo.getter('KPI_TREND[\\'#text\\']', true),\n                statusGraphic: kendo.getter('KPI_STATUS_GRAPHIC[\\'#text\\']', true),\n                trendGraphic: kendo.getter('KPI_TREND_GRAPHIC[\\'#text\\']', true),\n                description: kendo.getter('KPI_DESCRIPTION[\\'#text\\']', true),\n                groupName: kendo.getter('MEASUREGROUP_NAME[\\'#text\\']', true)\n            },\n            dimensions: {\n                name: kendo.getter('DIMENSION_NAME[\\'#text\\']', true),\n                caption: kendo.getter('DIMENSION_CAPTION[\\'#text\\']', true),\n                description: kendo.getter('DESCRIPTION[\\'#text\\']', true),\n                uniqueName: kendo.getter('DIMENSION_UNIQUE_NAME[\\'#text\\']', true),\n                defaultHierarchy: kendo.getter('DEFAULT_HIERARCHY[\\'#text\\']', true),\n                type: kendo.getter('DIMENSION_TYPE[\\'#text\\']', true)\n            },\n            hierarchies: {\n                name: kendo.getter('HIERARCHY_NAME[\\'#text\\']', true),\n                caption: kendo.getter('HIERARCHY_CAPTION[\\'#text\\']', true),\n                description: kendo.getter('DESCRIPTION[\\'#text\\']', true),\n                uniqueName: kendo.getter('HIERARCHY_UNIQUE_NAME[\\'#text\\']', true),\n                dimensionUniqueName: kendo.getter('DIMENSION_UNIQUE_NAME[\\'#text\\']', true),\n                displayFolder: kendo.getter('HIERARCHY_DISPLAY_FOLDER[\\'#text\\']', true),\n                origin: kendo.getter('HIERARCHY_ORIGIN[\\'#text\\']', true),\n                defaultMember: kendo.getter('DEFAULT_MEMBER[\\'#text\\']', true)\n            },\n            levels: {\n                name: kendo.getter('LEVEL_NAME[\\'#text\\']', true),\n                caption: kendo.getter('LEVEL_CAPTION[\\'#text\\']', true),\n                description: kendo.getter('DESCRIPTION[\\'#text\\']', true),\n                uniqueName: kendo.getter('LEVEL_UNIQUE_NAME[\\'#text\\']', true),\n                dimensionUniqueName: kendo.getter('DIMENSION_UNIQUE_NAME[\\'#text\\']', true),\n                displayFolder: kendo.getter('LEVEL_DISPLAY_FOLDER[\\'#text\\']', true),\n                orderingProperty: kendo.getter('LEVEL_ORDERING_PROPERTY[\\'#text\\']', true),\n                origin: kendo.getter('LEVEL_ORIGIN[\\'#text\\']', true),\n                hierarchyUniqueName: kendo.getter('HIERARCHY_UNIQUE_NAME[\\'#text\\']', true)\n            },\n            members: {\n                name: kendo.getter('MEMBER_NAME[\\'#text\\']', true),\n                caption: kendo.getter('MEMBER_CAPTION[\\'#text\\']', true),\n                uniqueName: kendo.getter('MEMBER_UNIQUE_NAME[\\'#text\\']', true),\n                dimensionUniqueName: kendo.getter('DIMENSION_UNIQUE_NAME[\\'#text\\']', true),\n                hierarchyUniqueName: kendo.getter('HIERARCHY_UNIQUE_NAME[\\'#text\\']', true),\n                levelUniqueName: kendo.getter('LEVEL_UNIQUE_NAME[\\'#text\\']', true),\n                childrenCardinality: kendo.getter('CHILDREN_CARDINALITY[\\'#text\\']', true)\n            }\n        };\n        var xmlaReaderMethods = [\n            'axes',\n            'catalogs',\n            'cubes',\n            'dimensions',\n            'hierarchies',\n            'levels',\n            'measures'\n        ];\n        var XmlaDataReader = kendo.data.XmlDataReader.extend({\n            init: function (options) {\n                kendo.data.XmlDataReader.call(this, options);\n                this._extend(options);\n            },\n            _extend: function (options) {\n                var idx = 0;\n                var length = xmlaReaderMethods.length;\n                var methodName;\n                var option;\n                for (; idx < length; idx++) {\n                    methodName = xmlaReaderMethods[idx];\n                    option = options[methodName];\n                    if (option && option !== identity) {\n                        this[methodName] = option;\n                    }\n                }\n            },\n            parse: function (xml) {\n                var result = kendo.data.XmlDataReader.fn.parse(xml.replace(/<(\\/?)(\\w|-)+:/g, '<$1'));\n                return kendo.getter('[\\'Envelope\\'][\\'Body\\']', true)(result);\n            },\n            errors: function (root) {\n                var fault = kendo.getter('[\\'Fault\\']', true)(root);\n                if (fault) {\n                    return [{\n                            faultstring: kendo.getter('faultstring[\\'#text\\']', true)(fault),\n                            faultcode: kendo.getter('faultcode[\\'#text\\']', true)(fault)\n                        }];\n                }\n                return null;\n            },\n            axes: function (root) {\n                root = kendo.getter('ExecuteResponse[\"return\"].root', true)(root);\n                var axes = asArray(kendo.getter('Axes.Axis', true)(root));\n                var axis;\n                var result = {\n                    columns: {},\n                    rows: {}\n                };\n                for (var idx = 0; idx < axes.length; idx++) {\n                    axis = axes[idx];\n                    if (axis['@name'].toLowerCase() !== 'sliceraxis') {\n                        if (!result.columns.tuples) {\n                            result.columns = translateAxis(axis);\n                        } else {\n                            result.rows = translateAxis(axis);\n                        }\n                    }\n                }\n                return result;\n            },\n            data: function (root) {\n                root = kendo.getter('ExecuteResponse[\"return\"].root', true)(root);\n                var cells = asArray(kendo.getter('CellData.Cell', true)(root));\n                var result = [];\n                var ordinalGetter = kendo.getter('[\\'@CellOrdinal\\']');\n                var valueGetter = kendo.getter('Value[\\'#text\\']');\n                var fmtValueGetter = kendo.getter('FmtValue[\\'#text\\']');\n                for (var idx = 0; idx < cells.length; idx++) {\n                    result.push({\n                        value: valueGetter(cells[idx]),\n                        fmtValue: fmtValueGetter(cells[idx]),\n                        ordinal: parseInt(ordinalGetter(cells[idx]), 10)\n                    });\n                }\n                return result;\n            },\n            _mapSchema: function (root, getters) {\n                root = kendo.getter('DiscoverResponse[\"return\"].root', true)(root);\n                var rows = asArray(kendo.getter('row', true)(root));\n                var result = [];\n                for (var idx = 0; idx < rows.length; idx++) {\n                    var obj = {};\n                    for (var key in getters) {\n                        obj[key] = getters[key](rows[idx]);\n                    }\n                    result.push(obj);\n                }\n                return result;\n            },\n            measures: function (root) {\n                return this._mapSchema(root, schemaDataReaderMap.measures);\n            },\n            kpis: function (root) {\n                return this._mapSchema(root, schemaDataReaderMap.kpis);\n            },\n            hierarchies: function (root) {\n                return this._mapSchema(root, schemaDataReaderMap.hierarchies);\n            },\n            levels: function (root) {\n                return this._mapSchema(root, schemaDataReaderMap.levels);\n            },\n            dimensions: function (root) {\n                return this._mapSchema(root, schemaDataReaderMap.dimensions);\n            },\n            cubes: function (root) {\n                return this._mapSchema(root, schemaDataReaderMap.cubes);\n            },\n            catalogs: function (root) {\n                return this._mapSchema(root, schemaDataReaderMap.catalogs);\n            },\n            members: function (root) {\n                return this._mapSchema(root, schemaDataReaderMap.members);\n            }\n        });\n        extend(true, kendo.data, {\n            PivotDataSource: PivotDataSource,\n            XmlaTransport: XmlaTransport,\n            XmlaDataReader: XmlaDataReader,\n            PivotCubeBuilder: PivotCubeBuilder,\n            transports: { xmla: XmlaTransport },\n            readers: { xmla: XmlaDataReader }\n        });\n        var sortExpr = function (expressions, name) {\n            if (!expressions) {\n                return null;\n            }\n            for (var idx = 0, length = expressions.length; idx < length; idx++) {\n                if (expressions[idx].field === name) {\n                    return expressions[idx];\n                }\n            }\n            return null;\n        };\n        var removeExpr = function (expressions, name) {\n            var result = [];\n            for (var idx = 0, length = expressions.length; idx < length; idx++) {\n                if (expressions[idx].field !== name) {\n                    result.push(expressions[idx]);\n                }\n            }\n            return result;\n        };\n        kendo.ui.PivotSettingTarget = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that.element.addClass('k-pivot-setting');\n                that.dataSource = kendo.data.PivotDataSource.create(options.dataSource);\n                that._refreshHandler = $.proxy(that.refresh, that);\n                that.dataSource.first(CHANGE, that._refreshHandler);\n                if (!options.template) {\n                    that.options.template = '<div data-' + kendo.ns + 'name=\"${data.name || data}\">${data.name || data}' + (that.options.enabled ? '<a class=\"k-button k-button-icon k-bare\"><span class=\"k-icon k-i-close k-setting-delete\"></span></a>' : '') + '</div>';\n                }\n                that.template = kendo.template(that.options.template);\n                that.emptyTemplate = kendo.template(that.options.emptyTemplate);\n                that._sortable();\n                that.element.on('click' + NS, '.k-button,.k-item', function (e) {\n                    var target = $(e.target);\n                    var name = target.closest('[' + kendo.attr('name') + ']').attr(kendo.attr('name'));\n                    if (!name) {\n                        return;\n                    }\n                    if (target.hasClass('k-i-close')) {\n                        that.remove(name);\n                    } else if (that.options.sortable && target[0] === e.currentTarget) {\n                        that.sort({\n                            field: name,\n                            dir: target.find('.k-i-sort-asc-sm')[0] ? 'desc' : 'asc'\n                        });\n                    }\n                });\n                if (options.filterable || options.sortable) {\n                    that.fieldMenu = new ui.PivotFieldMenu(that.element, {\n                        messages: that.options.messages.fieldMenu,\n                        filter: '.k-setting-fieldmenu',\n                        filterable: options.filterable,\n                        sortable: options.sortable,\n                        dataSource: that.dataSource\n                    });\n                }\n                that.refresh();\n            },\n            options: {\n                name: 'PivotSettingTarget',\n                template: null,\n                filterable: false,\n                sortable: false,\n                emptyTemplate: '<div class=\\'k-empty\\'>${data}</div>',\n                setting: 'columns',\n                enabled: true,\n                messages: { empty: 'Drop Fields Here' }\n            },\n            setDataSource: function (dataSource) {\n                this.dataSource.unbind(CHANGE, this._refreshHandler);\n                this.dataSource = this.options.dataSource = dataSource;\n                if (this.fieldMenu) {\n                    this.fieldMenu.setDataSource(dataSource);\n                }\n                dataSource.first(CHANGE, this._refreshHandler);\n                this.refresh();\n            },\n            _sortable: function () {\n                var that = this;\n                if (that.options.enabled) {\n                    this.sortable = this.element.kendoSortable({\n                        connectWith: this.options.connectWith,\n                        hint: that.options.hint,\n                        cursor: 'move',\n                        start: function (e) {\n                            e.item.focus().blur();\n                        },\n                        change: function (e) {\n                            var name = e.item.attr(kendo.attr('name'));\n                            if (e.action == 'receive') {\n                                that.add(name);\n                            } else if (e.action == 'remove') {\n                                that.remove(name);\n                            } else if (e.action == 'sort') {\n                                that.move(name, e.newIndex);\n                            }\n                        }\n                    }).data('kendoSortable');\n                }\n            },\n            _indexOf: function (name, items) {\n                var idx, length, index = -1;\n                for (idx = 0, length = items.length; idx < length; idx++) {\n                    if (getName(items[idx]) === name) {\n                        index = idx;\n                        break;\n                    }\n                }\n                return index;\n            },\n            _isKPI: function (data) {\n                return data.type === 'kpi' || data.measure;\n            },\n            validate: function (data) {\n                var isMeasure = data.type == 2 || 'aggregator' in data || this._isKPI(data);\n                if (isMeasure) {\n                    return this.options.setting === 'measures';\n                }\n                if (this.options.setting === 'measures') {\n                    return isMeasure;\n                }\n                var items = this.dataSource[this.options.setting]();\n                var name = data.defaultHierarchy || data.uniqueName;\n                if (this._indexOf(name, items) > -1) {\n                    return false;\n                }\n                items = this.dataSource[this.options.setting === 'columns' ? 'rows' : 'columns']();\n                if (this._indexOf(name, items) > -1) {\n                    return false;\n                }\n                return true;\n            },\n            add: function (name) {\n                var items = this.dataSource[this.options.setting]();\n                var i, l;\n                name = $.isArray(name) ? name.slice(0) : [name];\n                for (i = 0, l = name.length; i < l; i++) {\n                    if (this._indexOf(name[i], items) !== -1) {\n                        name.splice(i, 1);\n                        i -= 1;\n                        l -= 1;\n                    }\n                }\n                if (name.length) {\n                    items = items.concat(name);\n                    this.dataSource[this.options.setting](items);\n                }\n            },\n            move: function (name, index) {\n                var items = this.dataSource[this.options.setting]();\n                var idx = this._indexOf(name, items);\n                if (idx > -1) {\n                    name = items.splice(idx, 1)[0];\n                    items.splice(index, 0, name);\n                    this.dataSource[this.options.setting](items);\n                }\n            },\n            remove: function (name) {\n                var items = this.dataSource[this.options.setting]();\n                var idx = this._indexOf(name, items);\n                var sortExpressions = this.dataSource.sort();\n                var filter = this.dataSource.filter();\n                if (idx > -1) {\n                    if (filter) {\n                        filter.filters = removeExpr(filter.filters, name);\n                        this.dataSource._filter.filters = filter.filters;\n                        if (!filter.filters.length) {\n                            this.dataSource._filter = null;\n                        }\n                    }\n                    if (sortExpressions) {\n                        sortExpressions = removeExpr(sortExpressions, name);\n                        this.dataSource._sort = sortExpressions;\n                    }\n                    items.splice(idx, 1);\n                    this.dataSource[this.options.setting](items);\n                }\n            },\n            sort: function (expr) {\n                var sortable = this.options.sortable;\n                var allowUnsort = sortable === true || sortable.allowUnsort;\n                var skipExpr = allowUnsort && expr.dir === 'asc';\n                var expressions = this.dataSource.sort() || [];\n                var result = removeExpr(expressions, expr.field);\n                if (skipExpr && expressions.length !== result.length) {\n                    expr = null;\n                }\n                if (expr) {\n                    result.push(expr);\n                }\n                this.dataSource.sort(result);\n            },\n            refresh: function () {\n                var html = '';\n                var items = this.dataSource[this.options.setting]();\n                var length = items.length;\n                var idx = 0;\n                var item;\n                if (length) {\n                    for (; idx < length; idx++) {\n                        item = items[idx];\n                        item = item.name === undefined ? { name: item } : item;\n                        html += this.template(extend({ sortIcon: this._sortIcon(item.name) }, item));\n                    }\n                } else {\n                    html = this.emptyTemplate(this.options.messages.empty);\n                }\n                this.element.html(html);\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.dataSource.unbind(CHANGE, this._refreshHandler);\n                this.element.off(NS);\n                if (this.sortable) {\n                    this.sortable.destroy();\n                }\n                if (this.fieldMenu) {\n                    this.fieldMenu.destroy();\n                }\n                this.element = null;\n                this._refreshHandler = null;\n            },\n            _sortIcon: function (name) {\n                var expressions = this.dataSource.sort();\n                var expr = sortExpr(expressions, getName(name));\n                var icon = '';\n                if (expr) {\n                    icon = 'k-i-sort-' + expr.dir;\n                }\n                return icon;\n            }\n        });\n        var PivotGrid = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                var columnBuilder;\n                var rowBuilder;\n                Widget.fn.init.call(that, element, options);\n                that._dataSource();\n                that._bindConfigurator();\n                that._wrapper();\n                that._createLayout();\n                that._columnBuilder = columnBuilder = new ColumnBuilder();\n                that._rowBuilder = rowBuilder = new RowBuilder();\n                that._contentBuilder = new ContentBuilder();\n                that._templates();\n                that.columnsHeader.add(that.rowsHeader).on('click', 'span.k-icon', function () {\n                    var button = $(this);\n                    var builder = columnBuilder;\n                    var action = 'expandColumn';\n                    var eventName;\n                    var path = button.attr(kendo.attr('path'));\n                    var eventArgs = {\n                        axis: 'columns',\n                        path: $.parseJSON(path)\n                    };\n                    if (button.parent().is('td')) {\n                        builder = rowBuilder;\n                        action = 'expandRow';\n                        eventArgs.axis = 'rows';\n                    }\n                    var expanded = button.hasClass(STATE_EXPANDED);\n                    var metadata = builder.metadata[path];\n                    var request = metadata.expanded === undefined;\n                    eventName = expanded ? COLLAPSEMEMBER : EXPANDMEMBER;\n                    eventArgs.childrenLoaded = metadata.maxChildren > metadata.children;\n                    if (that.trigger(eventName, eventArgs)) {\n                        return;\n                    }\n                    builder.metadata[path].expanded = !expanded;\n                    button.toggleClass(STATE_EXPANDED, !expanded).toggleClass(STATE_COLLAPSED, expanded);\n                    if (!expanded && request) {\n                        that.dataSource[action](eventArgs.path);\n                    } else {\n                        that.refresh();\n                    }\n                });\n                that._scrollable();\n                if (that.options.autoBind) {\n                    that.dataSource.fetch();\n                }\n                kendo.notify(that);\n            },\n            events: [\n                DATABINDING,\n                DATABOUND,\n                EXPANDMEMBER,\n                COLLAPSEMEMBER\n            ],\n            options: {\n                name: 'PivotGrid',\n                autoBind: true,\n                reorderable: true,\n                filterable: false,\n                sortable: false,\n                height: null,\n                columnWidth: 100,\n                configurator: '',\n                columnHeaderTemplate: null,\n                rowHeaderTemplate: null,\n                dataCellTemplate: null,\n                kpiStatusTemplate: null,\n                kpiTrendTemplate: null,\n                messages: {\n                    measureFields: 'Drop Data Fields Here',\n                    columnFields: 'Drop Column Fields Here',\n                    rowFields: 'Drop Rows Fields Here'\n                }\n            },\n            _templates: function () {\n                var columnTemplate = this.options.columnHeaderTemplate;\n                var rowTemplate = this.options.rowHeaderTemplate;\n                var dataTemplate = this.options.dataCellTemplate;\n                var kpiStatusTemplate = this.options.kpiStatusTemplate;\n                var kpiTrendTemplate = this.options.kpiTrendTemplate;\n                this._columnBuilder.template = kendo.template(columnTemplate || HEADER_TEMPLATE, { useWithBlock: !!columnTemplate });\n                this._contentBuilder.dataTemplate = kendo.template(dataTemplate || DATACELL_TEMPLATE, { useWithBlock: !!dataTemplate });\n                this._contentBuilder.kpiStatusTemplate = kendo.template(kpiStatusTemplate || KPISTATUS_TEMPLATE, { useWithBlock: !!kpiStatusTemplate });\n                this._contentBuilder.kpiTrendTemplate = kendo.template(kpiTrendTemplate || KPITREND_TEMPLATE, { useWithBlock: !!kpiTrendTemplate });\n                this._rowBuilder.template = kendo.template(rowTemplate || HEADER_TEMPLATE, { useWithBlock: !!rowTemplate });\n            },\n            _bindConfigurator: function () {\n                var configurator = this.options.configurator;\n                if (configurator) {\n                    $(configurator).kendoPivotConfigurator('setDataSource', this.dataSource);\n                }\n            },\n            cellInfoByElement: function (element) {\n                element = $(element);\n                return this.cellInfo(element.index(), element.parent('tr').index());\n            },\n            cellInfo: function (columnIndex, rowIndex) {\n                var contentBuilder = this._contentBuilder;\n                var columnInfo = contentBuilder.columnIndexes[columnIndex || 0];\n                var rowInfo = contentBuilder.rowIndexes[rowIndex || 0];\n                var dataIndex;\n                if (!columnInfo || !rowInfo) {\n                    return null;\n                }\n                dataIndex = rowInfo.index * contentBuilder.rowLength + columnInfo.index;\n                return {\n                    columnTuple: columnInfo.tuple,\n                    rowTuple: rowInfo.tuple,\n                    measure: columnInfo.measure || rowInfo.measure,\n                    dataItem: this.dataSource.view()[dataIndex]\n                };\n            },\n            setDataSource: function (dataSource) {\n                this.options.dataSource = dataSource;\n                this._dataSource();\n                if (this.measuresTarget) {\n                    this.measuresTarget.setDataSource(dataSource);\n                }\n                if (this.rowsTarget) {\n                    this.rowsTarget.setDataSource(dataSource);\n                }\n                if (this.columnsTarget) {\n                    this.columnsTarget.setDataSource(dataSource);\n                }\n                this._bindConfigurator();\n                if (this.options.autoBind) {\n                    dataSource.fetch();\n                }\n            },\n            setOptions: function (options) {\n                Widget.fn.setOptions.call(this, options);\n                this._templates();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                clearTimeout(this._headerReflowTimeout);\n            },\n            _dataSource: function () {\n                var that = this;\n                var dataSource = that.options.dataSource;\n                dataSource = $.isArray(dataSource) ? { data: dataSource } : dataSource;\n                if (that.dataSource && this._refreshHandler) {\n                    that.dataSource.unbind(CHANGE, that._refreshHandler).unbind(STATERESET, that._stateResetHandler).unbind(PROGRESS, that._progressHandler).unbind(ERROR, that._errorHandler);\n                } else {\n                    that._refreshHandler = $.proxy(that.refresh, that);\n                    that._progressHandler = $.proxy(that._requestStart, that);\n                    that._stateResetHandler = $.proxy(that._stateReset, that);\n                    that._errorHandler = $.proxy(that._error, that);\n                }\n                that.dataSource = kendo.data.PivotDataSource.create(dataSource).bind(CHANGE, that._refreshHandler).bind(PROGRESS, that._progressHandler).bind(STATERESET, that._stateResetHandler).bind(ERROR, that._errorHandler);\n            },\n            _error: function () {\n                this._progress(false);\n            },\n            _requestStart: function () {\n                this._progress(true);\n            },\n            _stateReset: function () {\n                this._columnBuilder.reset();\n                this._rowBuilder.reset();\n            },\n            _wrapper: function () {\n                var height = this.options.height;\n                this.wrapper = this.element.addClass('k-widget k-pivot');\n                if (height) {\n                    this.wrapper.css('height', height);\n                }\n            },\n            _measureFields: function () {\n                this.measureFields = $(DIV).addClass('k-pivot-toolbar k-header k-settings-measures');\n                this.measuresTarget = this._createSettingTarget(this.measureFields, {\n                    setting: 'measures',\n                    messages: { empty: this.options.messages.measureFields }\n                });\n            },\n            _createSettingTarget: function (element, options) {\n                var template = '<span tabindex=\"0\" class=\"k-button\" data-' + kendo.ns + 'name=\"${data.name}\">${data.name}';\n                var sortable = options.sortable;\n                var icons = '';\n                if (sortable) {\n                    icons += '#if (data.sortIcon) {#';\n                    icons += '<span class=\"k-icon ${data.sortIcon}-sm\"></span>';\n                    icons += '#}#';\n                }\n                if (options.filterable || sortable) {\n                    icons += '<span class=\"k-icon k-i-more-vertical k-setting-fieldmenu\"></span>';\n                }\n                if (this.options.reorderable) {\n                    icons += '<span class=\"k-icon k-i-close k-setting-delete\"></span>';\n                }\n                if (icons) {\n                    template += '<span class=\"k-field-actions\">' + icons + '</span>';\n                }\n                template += '</span>';\n                return new kendo.ui.PivotSettingTarget(element, $.extend({\n                    template: template,\n                    emptyTemplate: '<span class=\"k-empty\">${data}</span>',\n                    enabled: this.options.reorderable,\n                    dataSource: this.dataSource\n                }, options));\n            },\n            _initSettingTargets: function () {\n                this.columnsTarget = this._createSettingTarget(this.columnFields, {\n                    connectWith: this.rowFields,\n                    setting: 'columns',\n                    filterable: this.options.filterable,\n                    sortable: this.options.sortable,\n                    messages: {\n                        empty: this.options.messages.columnFields,\n                        fieldMenu: this.options.messages.fieldMenu\n                    }\n                });\n                this.rowsTarget = this._createSettingTarget(this.rowFields, {\n                    connectWith: this.columnFields,\n                    setting: 'rows',\n                    filterable: this.options.filterable,\n                    sortable: this.options.sortable,\n                    messages: {\n                        empty: this.options.messages.rowFields,\n                        fieldMenu: this.options.messages.fieldMenu\n                    }\n                });\n            },\n            _createLayout: function () {\n                var that = this;\n                var layoutTable = $(LAYOUT_TABLE);\n                var leftContainer = layoutTable.find('.k-pivot-rowheaders');\n                var rightContainer = layoutTable.find('.k-pivot-table');\n                var gridWrapper = $(DIV).addClass('k-grid k-widget');\n                that._measureFields();\n                that.columnFields = $(DIV).addClass('k-pivot-toolbar k-header k-settings-columns');\n                that.rowFields = $(DIV).addClass('k-pivot-toolbar k-header k-settings-rows');\n                that.columnsHeader = $('<div class=\"k-grid-header-wrap\" />').wrap('<div class=\"k-grid-header\" />');\n                that.columnsHeader.parent().css('padding-right', kendo.support.scrollbar());\n                that.rowsHeader = $('<div class=\"k-grid k-widget k-alt\"/>');\n                that.content = $('<div class=\"k-grid-content\" />');\n                leftContainer.append(that.measureFields);\n                leftContainer.append(that.rowFields);\n                leftContainer.append(that.rowsHeader);\n                gridWrapper.append(that.columnsHeader.parent());\n                gridWrapper.append(that.content);\n                rightContainer.append(that.columnFields);\n                rightContainer.append(gridWrapper);\n                that.wrapper.append(layoutTable);\n                that.columnsHeaderTree = new kendo.dom.Tree(that.columnsHeader[0]);\n                that.rowsHeaderTree = new kendo.dom.Tree(that.rowsHeader[0]);\n                that.contentTree = new kendo.dom.Tree(that.content[0]);\n                that._initSettingTargets();\n            },\n            _progress: function (toggle) {\n                kendo.ui.progress(this.wrapper, toggle);\n            },\n            _resize: function () {\n                if (this.content[0].firstChild) {\n                    this._setSectionsWidth();\n                    this._setSectionsHeight();\n                    this._setContentWidth();\n                    this._setContentHeight();\n                    this._columnHeaderReflow();\n                }\n            },\n            _columnHeaderReflow: function () {\n                var columnTable = this.columnsHeader.children('table');\n                if (!kendo.support.browser.mozilla) {\n                    return;\n                }\n                clearTimeout(this._headerReflowTimeout);\n                columnTable.css('table-layout', 'auto');\n                this._headerReflowTimeout = setTimeout(function () {\n                    columnTable.css('table-layout', '');\n                });\n            },\n            _setSectionsWidth: function () {\n                var rowsHeader = this.rowsHeader;\n                var leftColumn = rowsHeader.parent('.k-pivot-rowheaders').width(AUTO);\n                var width;\n                width = Math.max(outerWidth(this.measureFields), outerWidth(this.rowFields));\n                width = Math.max(rowsHeader.children('table').width(), width);\n                leftColumn.width(width);\n            },\n            _setSectionsHeight: function () {\n                var measureFieldsHeight = this.measureFields.height(AUTO).height();\n                var columnFieldsHeight = this.columnFields.height(AUTO).height();\n                var rowFieldsHeight = this.rowFields.height(AUTO).innerHeight();\n                var columnsHeight = this.columnsHeader.height(AUTO).innerHeight();\n                var padding = rowFieldsHeight - this.rowFields.height();\n                var firstRowHeight = columnFieldsHeight > measureFieldsHeight ? columnFieldsHeight : measureFieldsHeight;\n                var secondRowHeight = columnsHeight > rowFieldsHeight ? columnsHeight : rowFieldsHeight;\n                this.measureFields.height(firstRowHeight);\n                this.columnFields.height(firstRowHeight);\n                this.rowFields.height(secondRowHeight - padding);\n                this.columnsHeader.height(secondRowHeight);\n            },\n            _setContentWidth: function () {\n                var contentTable = this.content.find('table');\n                var columnTable = this.columnsHeader.children('table');\n                var rowLength = contentTable.children('colgroup').children().length;\n                var calculatedWidth = rowLength * this.options.columnWidth;\n                var minWidth = Math.ceil(calculatedWidth / this.content.width() * 100);\n                if (minWidth < 100) {\n                    minWidth = 100;\n                }\n                contentTable.add(columnTable).css('width', minWidth + '%');\n                this._resetColspan(columnTable);\n            },\n            _setContentHeight: function () {\n                var that = this;\n                var content = that.content;\n                var rowsHeader = that.rowsHeader;\n                var innerHeight = that.wrapper.innerHeight();\n                var scrollbar = kendo.support.scrollbar();\n                var skipScrollbar = content[0].offsetHeight === content[0].clientHeight;\n                var height = that.options.height;\n                if (that.wrapper.is(':visible')) {\n                    if (!innerHeight || !height) {\n                        if (skipScrollbar) {\n                            scrollbar = 0;\n                        }\n                        content.height('auto');\n                        rowsHeader.height(content.height() - scrollbar);\n                        return;\n                    }\n                    innerHeight -= outerHeight(that.columnFields);\n                    innerHeight -= outerHeight(that.columnsHeader.parent());\n                    if (innerHeight <= scrollbar * 2) {\n                        innerHeight = scrollbar * 2 + 1;\n                        if (!skipScrollbar) {\n                            innerHeight += scrollbar;\n                        }\n                    }\n                    content.height(innerHeight);\n                    if (skipScrollbar) {\n                        scrollbar = 0;\n                    }\n                    rowsHeader.height(innerHeight - scrollbar);\n                }\n            },\n            _resetColspan: function (columnTable) {\n                var that = this;\n                var cell = columnTable.children('tbody').children(':first').children(':first');\n                if (that._colspan === undefined) {\n                    that._colspan = cell.attr('colspan');\n                }\n                cell.attr('colspan', 1);\n                clearTimeout(that._layoutTimeout);\n                that._layoutTimeout = setTimeout(function () {\n                    cell.attr('colspan', that._colspan);\n                    that._colspan = undefined;\n                });\n            },\n            _axisMeasures: function (axis) {\n                var result = [];\n                var dataSource = this.dataSource;\n                var measures = dataSource.measures();\n                var hasMeasure = measures.length > 1 || measures[0] && measures[0].type;\n                if (dataSource.measuresAxis() === axis) {\n                    if (dataSource[axis]().length === 0 || hasMeasure) {\n                        result = measures;\n                    }\n                }\n                return result;\n            },\n            items: function () {\n                return [];\n            },\n            refresh: function () {\n                var that = this;\n                var dataSource = that.dataSource;\n                var axes = dataSource.axes();\n                var columns = (axes.columns || {}).tuples || [];\n                var rows = (axes.rows || {}).tuples || [];\n                var columnBuilder = that._columnBuilder;\n                var rowBuilder = that._rowBuilder;\n                var columnAxis = {};\n                var rowAxis = {};\n                if (that.trigger(DATABINDING, { action: 'rebind' })) {\n                    return;\n                }\n                columnBuilder.measures = that._axisMeasures(AXIS_COLUMNS);\n                rowBuilder.measures = that._axisMeasures(AXIS_ROWS);\n                that.columnsHeaderTree.render(columnBuilder.build(columns));\n                that.rowsHeaderTree.render(rowBuilder.build(rows));\n                columnAxis = {\n                    indexes: columnBuilder._indexes,\n                    measures: columnBuilder.measures,\n                    metadata: columnBuilder.metadata\n                };\n                rowAxis = {\n                    indexes: rowBuilder._indexes,\n                    measures: rowBuilder.measures,\n                    metadata: rowBuilder.metadata\n                };\n                that.contentTree.render(that._contentBuilder.build(dataSource.view(), columnAxis, rowAxis));\n                that._resize();\n                if (that.touchScroller) {\n                    that.touchScroller.contentResized();\n                } else {\n                    var touchScroller = kendo.touchScroller(that.content);\n                    if (touchScroller && touchScroller.movable) {\n                        that.touchScroller = touchScroller;\n                        touchScroller.movable.bind('change', function (e) {\n                            that.columnsHeader.scrollLeft(-e.sender.x);\n                            that.rowsHeader.scrollTop(-e.sender.y);\n                        });\n                    }\n                }\n                that._progress(false);\n                that.trigger(DATABOUND);\n            },\n            _scrollable: function () {\n                var that = this;\n                var columnsHeader = that.columnsHeader;\n                var rowsHeader = that.rowsHeader;\n                that.content.scroll(function () {\n                    columnsHeader.scrollLeft(this.scrollLeft);\n                    rowsHeader.scrollTop(this.scrollTop);\n                });\n                rowsHeader.bind('DOMMouseScroll' + NS + ' mousewheel' + NS, $.proxy(that._wheelScroll, that));\n            },\n            _wheelScroll: function (e) {\n                if (e.ctrlKey) {\n                    return;\n                }\n                var delta = kendo.wheelDeltaY(e);\n                var scrollTop = this.content.scrollTop();\n                if (delta) {\n                    e.preventDefault();\n                    $(e.currentTarget).one('wheel' + NS, false);\n                    this.rowsHeader.scrollTop(scrollTop + -delta);\n                    this.content.scrollTop(scrollTop + -delta);\n                }\n            }\n        });\n        var element = kendo.dom.element;\n        var htmlNode = kendo.dom.html;\n        var createMetadata = function (levelNum, memberIdx) {\n            return {\n                maxChildren: 0,\n                children: 0,\n                maxMembers: 0,\n                members: 0,\n                measures: 1,\n                levelNum: levelNum,\n                parentMember: memberIdx !== 0\n            };\n        };\n        var buildPath = function (tuple, index) {\n            var path = [];\n            var idx = 0;\n            for (; idx <= index; idx++) {\n                path.push(tuple.members[idx].name);\n            }\n            return path;\n        };\n        var tupleName = function (tuple, index) {\n            var name = '';\n            var idx = 0;\n            for (; idx <= index; idx++) {\n                name += tuple.members[idx].name;\n            }\n            return name;\n        };\n        var ColumnBuilder = Class.extend({\n            init: function () {\n                this.measures = 1;\n                this.metadata = {};\n            },\n            build: function (tuples) {\n                var tbody = this._tbody(tuples);\n                var colgroup = this._colGroup();\n                return [element('table', null, [\n                        colgroup,\n                        tbody\n                    ])];\n            },\n            reset: function () {\n                this.metadata = {};\n            },\n            _colGroup: function () {\n                var length = this._rowLength();\n                var children = [];\n                var idx = 0;\n                for (; idx < length; idx++) {\n                    children.push(element('col', null));\n                }\n                return element('colgroup', null, children);\n            },\n            _tbody: function (tuples) {\n                var root = tuples[0];\n                this.map = {};\n                this.rows = [];\n                this.rootTuple = root;\n                this._indexes = [];\n                if (root) {\n                    this._buildRows(root, 0);\n                    this._normalize();\n                } else {\n                    this.rows.push(element('tr', null, [element('th', null, [htmlNode('&nbsp;')])]));\n                }\n                return element('tbody', null, this.rows);\n            },\n            _normalize: function () {\n                var rows = this.rows;\n                var rowsLength = rows.length;\n                var rowIdx = 0;\n                var row;\n                var cellsLength;\n                var cellIdx;\n                var cells;\n                var cell;\n                for (; rowIdx < rowsLength; rowIdx++) {\n                    row = rows[rowIdx];\n                    if (row.rowSpan === 1) {\n                        continue;\n                    }\n                    cells = row.children;\n                    cellIdx = 0;\n                    cellsLength = cells.length;\n                    for (; cellIdx < cellsLength; cellIdx++) {\n                        cell = cells[cellIdx];\n                        if (cell.tupleAll) {\n                            cell.attr.rowSpan = row.rowSpan;\n                        }\n                    }\n                }\n            },\n            _rowIndex: function (row) {\n                var rows = this.rows;\n                var length = rows.length;\n                var idx = 0;\n                for (; idx < length; idx++) {\n                    if (rows[idx] === row) {\n                        break;\n                    }\n                }\n                return idx;\n            },\n            _rowLength: function () {\n                var cells = this.rows[0] ? this.rows[0].children : [];\n                var length = cells.length;\n                var rowLength = 0;\n                var idx = 0;\n                if (length) {\n                    for (; idx < length; idx++) {\n                        rowLength += cells[idx].attr.colSpan || 1;\n                    }\n                }\n                if (!rowLength) {\n                    rowLength = this.measures;\n                }\n                return rowLength;\n            },\n            _row: function (tuple, memberIdx, parentMember) {\n                var rootName = this.rootTuple.members[memberIdx].name;\n                var levelNum = tuple.members[memberIdx].levelNum;\n                var rowKey = rootName + levelNum;\n                var map = this.map;\n                var parentRow;\n                var children;\n                var row = map[rowKey];\n                if (!row) {\n                    row = element('tr', null, []);\n                    row.parentMember = parentMember;\n                    row.collapsed = 0;\n                    row.colSpan = 0;\n                    row.rowSpan = 1;\n                    map[rowKey] = row;\n                    parentRow = map[rootName + (Number(levelNum) - 1)];\n                    if (parentRow) {\n                        children = parentRow.children;\n                        if (children[1] && children[1].attr.className.indexOf('k-alt') === -1) {\n                            row.notFirst = true;\n                        } else {\n                            row.notFirst = parentRow.notFirst;\n                        }\n                    }\n                    this.rows.splice(this._rowIndex(parentRow) + 1, 0, row);\n                } else {\n                    row.notFirst = false;\n                    if (!row.parentMember || row.parentMember !== parentMember) {\n                        row.parentMember = parentMember;\n                        row.collapsed = 0;\n                        row.colSpan = 0;\n                    }\n                }\n                return row;\n            },\n            _measures: function (measures, tuple, className) {\n                var map = this.map;\n                var row = map.measureRow;\n                var measure;\n                if (!row) {\n                    row = element('tr', null, []);\n                    map.measureRow = row;\n                    this.rows.push(row);\n                }\n                for (var idx = 0, length = measures.length; idx < length; idx++) {\n                    measure = measures[idx];\n                    row.children.push(this._cell(className || '', [this._content(measure, tuple)], measure));\n                }\n                return length;\n            },\n            _content: function (member, tuple) {\n                return htmlNode(this.template({\n                    member: member,\n                    tuple: tuple\n                }));\n            },\n            _cell: function (className, children, member) {\n                var cell = element('th', { className: 'k-header' + className }, children);\n                cell.value = member.caption || member.name;\n                return cell;\n            },\n            _buildRows: function (tuple, memberIdx, parentMember) {\n                var members = tuple.members;\n                var member = members[memberIdx];\n                var nextMember = members[memberIdx + 1];\n                var row, childRow, children, childrenLength;\n                var cell, allCell, cellAttr;\n                var cellChildren = [];\n                var path;\n                var idx = 0;\n                var metadata;\n                var colSpan;\n                var collapsed = 0;\n                var memberCollapsed = 0;\n                if (member.measure) {\n                    this._measures(member.children, tuple);\n                    return;\n                }\n                path = kendo.stringify(buildPath(tuple, memberIdx));\n                row = this._row(tuple, memberIdx, parentMember);\n                children = member.children;\n                childrenLength = children.length;\n                metadata = this.metadata[path];\n                if (!metadata) {\n                    this.metadata[path] = metadata = createMetadata(Number(member.levelNum), memberIdx);\n                    metadata.rootLevelNum = Number(this.rootTuple.members[memberIdx].levelNum);\n                }\n                this._indexes.push({\n                    path: path,\n                    tuple: tuple\n                });\n                if (member.hasChildren) {\n                    if (metadata.expanded === false) {\n                        collapsed = metadata.maxChildren;\n                        row.collapsed += collapsed;\n                        metadata.children = 0;\n                        childrenLength = 0;\n                    }\n                    cellAttr = { className: 'k-icon ' + (childrenLength ? STATE_EXPANDED : STATE_COLLAPSED) };\n                    cellAttr[kendo.attr('path')] = path;\n                    cellChildren.push(element('span', cellAttr));\n                }\n                cellChildren.push(this._content(member, tuple));\n                cell = this._cell(row.notFirst ? ' k-first' : '', cellChildren, member);\n                row.children.push(cell);\n                row.colSpan += 1;\n                if (childrenLength) {\n                    allCell = this._cell(' k-alt', [this._content(member, tuple)], member);\n                    row.children.push(allCell);\n                    for (; idx < childrenLength; idx++) {\n                        childRow = this._buildRows(children[idx], memberIdx, member);\n                    }\n                    colSpan = childRow.colSpan;\n                    collapsed = childRow.collapsed;\n                    cell.attr.colSpan = colSpan;\n                    metadata.children = colSpan;\n                    metadata.members = 1;\n                    row.colSpan += colSpan;\n                    row.collapsed += collapsed;\n                    row.rowSpan = childRow.rowSpan + 1;\n                    if (nextMember) {\n                        if (nextMember.measure) {\n                            colSpan = this._measures(nextMember.children, tuple, ' k-alt');\n                        } else {\n                            childRow = this._buildRows(tuple, memberIdx + 1);\n                            colSpan = childRow.colSpan;\n                            row.collapsed += childRow.collapsed;\n                            memberCollapsed = childRow.collapsed;\n                        }\n                        allCell.attr.colSpan = colSpan;\n                        colSpan -= 1;\n                        metadata.members += colSpan;\n                        row.colSpan += colSpan;\n                    }\n                } else if (nextMember) {\n                    if (nextMember.measure) {\n                        colSpan = this._measures(nextMember.children, tuple);\n                    } else {\n                        childRow = this._buildRows(tuple, memberIdx + 1);\n                        colSpan = childRow.colSpan;\n                        row.collapsed += childRow.collapsed;\n                        memberCollapsed = childRow.collapsed;\n                    }\n                    metadata.members = colSpan;\n                    if (colSpan > 1) {\n                        cell.attr.colSpan = colSpan;\n                        row.colSpan += colSpan - 1;\n                    }\n                }\n                if (metadata.maxMembers < metadata.members + memberCollapsed) {\n                    metadata.maxMembers = metadata.members + memberCollapsed;\n                }\n                children = metadata.children + collapsed;\n                if (metadata.maxChildren < children) {\n                    metadata.maxChildren = children;\n                }\n                (allCell || cell).tupleAll = true;\n                return row;\n            }\n        });\n        var RowBuilder = Class.extend({\n            init: function () {\n                this.metadata = {};\n            },\n            build: function (tuples) {\n                var tbody = this._tbody(tuples);\n                var colgroup = this._colGroup();\n                return [element('table', null, [\n                        colgroup,\n                        tbody\n                    ])];\n            },\n            reset: function () {\n                this.metadata = {};\n            },\n            _rowLength: function () {\n                var children = this.rows[0].children;\n                var length = 0;\n                var idx = 0;\n                var cell = children[idx];\n                while (cell) {\n                    length += cell.attr.colSpan || 1;\n                    cell = children[++idx];\n                }\n                return length;\n            },\n            _colGroup: function () {\n                var length = this._rowLength();\n                var children = [];\n                var idx = 0;\n                for (; idx < length; idx++) {\n                    children.push(element('col', null));\n                }\n                return element('colgroup', null, children);\n            },\n            _tbody: function (tuples) {\n                var root = tuples[0];\n                this.rootTuple = root;\n                this.rows = [];\n                this.map = {};\n                this._indexes = [];\n                if (root) {\n                    this._buildRows(root, 0);\n                    this._normalize();\n                } else {\n                    this.rows.push(element('tr', null, [element('td', null, [htmlNode('&nbsp;')])]));\n                }\n                return element('tbody', null, this.rows);\n            },\n            _normalize: function () {\n                var rows = this.rows;\n                var rowsLength = rows.length;\n                var rowIdx = 0;\n                var members = this.rootTuple.members;\n                var firstMemberName = members[0].name;\n                var membersLength = members.length;\n                var memberIdx = 0;\n                var row;\n                var cell;\n                var maxcolSpan;\n                var map = this.map;\n                var allRow;\n                for (; rowIdx < rowsLength; rowIdx++) {\n                    row = rows[rowIdx];\n                    for (memberIdx = 0; memberIdx < membersLength; memberIdx++) {\n                        maxcolSpan = this[members[memberIdx].name];\n                        cell = row.colSpan['dim' + memberIdx];\n                        if (cell && cell.colSpan < maxcolSpan) {\n                            cell.attr.colSpan = maxcolSpan - cell.colSpan + 1;\n                        }\n                    }\n                }\n                row = map[firstMemberName];\n                allRow = map[firstMemberName + 'all'];\n                if (row) {\n                    row.children[0].attr.className = 'k-first';\n                }\n                if (allRow) {\n                    allRow.children[0].attr.className += ' k-first';\n                }\n            },\n            _row: function (children) {\n                var row = element('tr', null, children);\n                row.rowSpan = 1;\n                row.colSpan = {};\n                this.rows.push(row);\n                return row;\n            },\n            _content: function (member, tuple) {\n                return htmlNode(this.template({\n                    member: member,\n                    tuple: tuple\n                }));\n            },\n            _cell: function (className, children, member) {\n                var cell = element('td', { className: className }, children);\n                cell.value = member.caption || member.name;\n                return cell;\n            },\n            _buildRows: function (tuple, memberIdx) {\n                var map = this.map;\n                var path;\n                var members = tuple.members;\n                var member = members[memberIdx];\n                var nextMember = members[memberIdx + 1];\n                var children = member.children;\n                var childrenLength = children.length;\n                var levelNum = Number(member.levelNum);\n                var rootName = this.rootTuple.members[memberIdx].name;\n                var tuplePath = buildPath(tuple, memberIdx - 1).join('');\n                var rootLevelNum = Number(this.rootTuple.members[memberIdx].levelNum);\n                var parentName = tuplePath + (rootLevelNum === levelNum ? '' : member.parentName || '');\n                var row = map[parentName + 'all'] || map[parentName];\n                var colSpan = levelNum + 1;\n                var cell, allCell;\n                var childRow, allRow;\n                var metadata;\n                var className;\n                var cellChildren = [];\n                var expandIconAttr;\n                var idx;\n                if (!row || row.hasChild) {\n                    row = this._row();\n                } else {\n                    row.hasChild = true;\n                }\n                if (member.measure) {\n                    className = row.allCell ? 'k-grid-footer' : '';\n                    row.children.push(this._cell(className, [this._content(children[0], tuple)], children[0]));\n                    row.rowSpan = childrenLength;\n                    for (idx = 1; idx < childrenLength; idx++) {\n                        this._row([this._cell(className, [this._content(children[idx], tuple)], children[idx])]);\n                    }\n                    return row;\n                }\n                map[tuplePath + member.name] = row;\n                path = kendo.stringify(buildPath(tuple, memberIdx));\n                metadata = this.metadata[path];\n                if (!metadata) {\n                    this.metadata[path] = metadata = createMetadata(levelNum, memberIdx);\n                    metadata.rootLevelNum = rootLevelNum;\n                }\n                this._indexes.push({\n                    path: path,\n                    tuple: tuple\n                });\n                if (member.hasChildren) {\n                    if (metadata.expanded === false) {\n                        childrenLength = 0;\n                        metadata.children = 0;\n                    }\n                    expandIconAttr = { className: 'k-icon ' + (childrenLength ? STATE_EXPANDED : STATE_COLLAPSED) };\n                    expandIconAttr[kendo.attr('path')] = path;\n                    cellChildren.push(element('span', expandIconAttr));\n                }\n                cellChildren.push(this._content(member, tuple));\n                className = row.allCell && !childrenLength ? 'k-grid-footer' : '';\n                cell = this._cell(className, cellChildren, member);\n                cell.colSpan = colSpan;\n                row.children.push(cell);\n                row.colSpan['dim' + memberIdx] = cell;\n                if (!this[rootName] || this[rootName] < colSpan) {\n                    this[rootName] = colSpan;\n                }\n                if (childrenLength) {\n                    row.allCell = false;\n                    row.hasChild = false;\n                    for (idx = 0; idx < childrenLength; idx++) {\n                        childRow = this._buildRows(children[idx], memberIdx);\n                        if (row !== childRow) {\n                            row.rowSpan += childRow.rowSpan;\n                        }\n                    }\n                    if (row.rowSpan > 1) {\n                        cell.attr.rowSpan = row.rowSpan;\n                    }\n                    metadata.children = row.rowSpan;\n                    allCell = this._cell('k-grid-footer', [this._content(member, tuple)], member);\n                    allCell.colSpan = colSpan;\n                    allRow = this._row([allCell]);\n                    allRow.colSpan['dim' + memberIdx] = allCell;\n                    allRow.allCell = true;\n                    map[tuplePath + member.name + 'all'] = allRow;\n                    if (nextMember) {\n                        childRow = this._buildRows(tuple, memberIdx + 1);\n                        allCell.attr.rowSpan = childRow.rowSpan;\n                    }\n                    row.rowSpan += allRow.rowSpan;\n                    metadata.members = allRow.rowSpan;\n                } else if (nextMember) {\n                    row.hasChild = false;\n                    this._buildRows(tuple, memberIdx + 1);\n                    (allCell || cell).attr.rowSpan = row.rowSpan;\n                    metadata.members = row.rowSpan;\n                }\n                if (metadata.maxChildren < metadata.children) {\n                    metadata.maxChildren = metadata.children;\n                }\n                if (metadata.maxMembers < metadata.members) {\n                    metadata.maxMembers = metadata.members;\n                }\n                return row;\n            }\n        });\n        var ContentBuilder = Class.extend({\n            init: function () {\n                this.columnAxis = {};\n                this.rowAxis = {};\n            },\n            build: function (data, columnAxis, rowAxis) {\n                var index = columnAxis.indexes[0];\n                var metadata = columnAxis.metadata[index ? index.path : undefined];\n                this.columnAxis = columnAxis;\n                this.rowAxis = rowAxis;\n                this.data = data;\n                this.rowLength = metadata ? metadata.maxChildren + metadata.maxMembers : columnAxis.measures.length || 1;\n                if (!this.rowLength) {\n                    this.rowLength = 1;\n                }\n                var tbody = this._tbody();\n                var colgroup = this._colGroup();\n                return [element('table', null, [\n                        colgroup,\n                        tbody\n                    ])];\n            },\n            _colGroup: function () {\n                var length = this.columnAxis.measures.length || 1;\n                var children = [];\n                var idx = 0;\n                if (this.rows[0]) {\n                    length = this.rows[0].children.length;\n                }\n                for (; idx < length; idx++) {\n                    children.push(element('col', null));\n                }\n                return element('colgroup', null, children);\n            },\n            _tbody: function () {\n                this.rows = [];\n                if (this.data[0]) {\n                    this.columnIndexes = this._indexes(this.columnAxis, this.rowLength);\n                    this.rowIndexes = this._indexes(this.rowAxis, Math.ceil(this.data.length / this.rowLength));\n                    this._buildRows();\n                } else {\n                    this.rows.push(element('tr', null, [element('td', null, [htmlNode('&nbsp;')])]));\n                }\n                return element('tbody', null, this.rows);\n            },\n            _indexes: function (axisInfo, total) {\n                var result = [];\n                var axisInfoMember;\n                var indexes = axisInfo.indexes;\n                var metadata = axisInfo.metadata;\n                var measures = axisInfo.measures;\n                var measuresLength = measures.length || 1;\n                var current;\n                var dataIdx = 0;\n                var firstEmpty = 0;\n                var idx = 0;\n                var length = indexes.length;\n                var measureIdx;\n                var index;\n                var children;\n                var skipChildren;\n                if (!length) {\n                    for (measureIdx = 0; measureIdx < measuresLength; measureIdx++) {\n                        result[measureIdx] = {\n                            index: measureIdx,\n                            measure: measures[measureIdx],\n                            tuple: null\n                        };\n                    }\n                    return result;\n                }\n                for (; idx < length; idx++) {\n                    axisInfoMember = indexes[idx];\n                    current = metadata[axisInfoMember.path];\n                    children = current.children + current.members;\n                    skipChildren = 0;\n                    if (children) {\n                        children -= measuresLength;\n                    }\n                    if (current.expanded === false && current.children !== current.maxChildren) {\n                        skipChildren = current.maxChildren;\n                    }\n                    if (current.parentMember && current.levelNum === current.rootLevelNum) {\n                        children = -1;\n                    }\n                    if (children > -1) {\n                        for (measureIdx = 0; measureIdx < measuresLength; measureIdx++) {\n                            index = children + measureIdx;\n                            if (!current.children) {\n                                index += firstEmpty;\n                            }\n                            result[children + firstEmpty + measureIdx] = {\n                                children: children,\n                                index: dataIdx,\n                                measure: measures[measureIdx],\n                                tuple: axisInfoMember.tuple\n                            };\n                            dataIdx += 1;\n                        }\n                        while (result[firstEmpty] !== undefined) {\n                            firstEmpty += 1;\n                        }\n                    }\n                    if (firstEmpty === total) {\n                        break;\n                    }\n                    dataIdx += skipChildren;\n                }\n                return result;\n            },\n            _buildRows: function () {\n                var rowIndexes = this.rowIndexes;\n                var length = rowIndexes.length;\n                var idx = 0;\n                for (; idx < length; idx++) {\n                    var rowIndex = rowIndexes[idx];\n                    if (rowIndex) {\n                        this.rows.push(this._buildRow(rowIndex));\n                    }\n                }\n            },\n            _buildRow: function (rowInfo) {\n                var startIdx = rowInfo.index * this.rowLength;\n                var columnIndexes = this.columnIndexes;\n                var length = columnIndexes.length;\n                var columnInfo;\n                var cells = [];\n                var idx = 0;\n                var templateInfo;\n                var cell, cellContent;\n                var attr, dataItem, measure;\n                for (; idx < length; idx++) {\n                    columnInfo = columnIndexes[idx];\n                    if (columnInfo === undefined) {\n                        continue;\n                    }\n                    attr = {};\n                    if (columnInfo.children) {\n                        attr.className = 'k-alt';\n                    }\n                    cellContent = '';\n                    dataItem = this.data[startIdx + columnInfo.index];\n                    measure = columnInfo.measure || rowInfo.measure;\n                    templateInfo = {\n                        columnTuple: columnInfo.tuple,\n                        rowTuple: rowInfo.tuple,\n                        measure: measure,\n                        dataItem: dataItem\n                    };\n                    if (dataItem.value !== '' && measure && measure.type) {\n                        if (measure.type === 'status') {\n                            cellContent = this.kpiStatusTemplate(templateInfo);\n                        } else if (measure.type === 'trend') {\n                            cellContent = this.kpiTrendTemplate(templateInfo);\n                        }\n                    }\n                    if (!cellContent) {\n                        cellContent = this.dataTemplate(templateInfo);\n                    }\n                    cell = element('td', attr, [htmlNode(cellContent)]);\n                    cell.value = dataItem.value;\n                    cells.push(cell);\n                }\n                attr = {};\n                if (rowInfo.children) {\n                    attr.className = 'k-grid-footer';\n                }\n                return element('tr', attr, cells);\n            }\n        });\n        ui.plugin(PivotGrid);\n        kendo.PivotExcelExporter = kendo.Class.extend({\n            init: function (options) {\n                this.options = options;\n                this.widget = options.widget;\n                this.dataSource = this.widget.dataSource;\n            },\n            _columns: function () {\n                var columnHeaderTable = this.widget.columnsHeaderTree.children[0];\n                var rowHeaderTable = this.widget.rowsHeaderTree.children[0];\n                var columnHeaderLength = columnHeaderTable.children[0].children.length;\n                var rowHeaderLength = rowHeaderTable.children[0].children.length;\n                var width = this.widget.options.columnWidth;\n                var result = [];\n                var idx;\n                if (rowHeaderLength && this.dataSource.data()[0]) {\n                    for (idx = 0; idx < rowHeaderLength; idx++) {\n                        result.push({ autoWidth: true });\n                    }\n                }\n                for (idx = 0; idx < columnHeaderLength; idx++) {\n                    result.push({\n                        autoWidth: false,\n                        width: width\n                    });\n                }\n                return result;\n            },\n            _cells: function (rows, type, callback) {\n                var result = [];\n                var i = 0;\n                var length = rows.length;\n                var cellsLength;\n                var row, cells;\n                var j, cell;\n                for (; i < length; i++) {\n                    row = [];\n                    cells = rows[i].children;\n                    cellsLength = cells.length;\n                    for (j = 0; j < cellsLength; j++) {\n                        cell = cells[j];\n                        row.push({\n                            background: '#7a7a7a',\n                            color: '#fff',\n                            value: cell.value,\n                            colSpan: cell.attr.colSpan || 1,\n                            rowSpan: cell.attr.rowSpan || 1\n                        });\n                    }\n                    if (callback) {\n                        callback(row, i);\n                    }\n                    result.push({\n                        cells: row,\n                        type: type\n                    });\n                }\n                return result;\n            },\n            _rows: function () {\n                var columnHeaderTable = this.widget.columnsHeaderTree.children[0];\n                var rowHeaderTable = this.widget.rowsHeaderTree.children[0];\n                var columnHeaderLength = columnHeaderTable.children[0].children.length;\n                var rowHeaderLength = rowHeaderTable.children[0].children.length;\n                var columnHeaderRows = columnHeaderTable.children[1].children;\n                var rowHeaderRows = rowHeaderTable.children[1].children;\n                var contentRows = this.widget.contentTree.children[0].children[1].children;\n                var columnRows = this._cells(columnHeaderRows, 'header');\n                if (rowHeaderLength) {\n                    columnRows[0].cells.splice(0, 0, {\n                        background: '#7a7a7a',\n                        color: '#fff',\n                        value: '',\n                        colSpan: rowHeaderLength,\n                        rowSpan: columnHeaderRows.length\n                    });\n                }\n                var dataCallback = function (row, index) {\n                    var j = 0;\n                    var cell, value;\n                    var cells = contentRows[index].children;\n                    for (; j < columnHeaderLength; j++) {\n                        cell = cells[j];\n                        value = Number(cell.value);\n                        if (isNaN(value)) {\n                            value = cell.value;\n                        }\n                        row.push({\n                            background: '#dfdfdf',\n                            color: '#333',\n                            value: value,\n                            colSpan: 1,\n                            rowSpan: 1\n                        });\n                    }\n                };\n                var rowRows = this._cells(rowHeaderRows, 'data', dataCallback);\n                return columnRows.concat(rowRows);\n            },\n            _freezePane: function () {\n                var columnHeaderTable = this.widget.columnsHeaderTree.children[0];\n                var rowHeaderTable = this.widget.rowsHeaderTree.children[0];\n                var rowHeaderLength = rowHeaderTable.children[0].children.length;\n                var columnHeaderRows = columnHeaderTable.children[1].children;\n                return {\n                    colSplit: rowHeaderLength,\n                    rowSplit: columnHeaderRows.length\n                };\n            },\n            workbook: function () {\n                var promise;\n                if (this.dataSource.view()[0]) {\n                    promise = $.Deferred();\n                    promise.resolve();\n                } else {\n                    promise = this.dataSource.fetch();\n                }\n                return promise.then($.proxy(function () {\n                    return {\n                        sheets: [{\n                                columns: this._columns(),\n                                rows: this._rows(),\n                                freezePane: this._freezePane(),\n                                filter: null\n                            }]\n                    };\n                }, this));\n            }\n        });\n        var PivotExcelMixin = {\n            extend: function (proto) {\n                proto.events.push('excelExport');\n                proto.options.excel = $.extend(proto.options.excel, this.options);\n                proto.saveAsExcel = this.saveAsExcel;\n            },\n            options: {\n                proxyURL: '',\n                filterable: false,\n                fileName: 'Export.xlsx'\n            },\n            saveAsExcel: function () {\n                var excel = this.options.excel || {};\n                var exporter = new kendo.PivotExcelExporter({ widget: this });\n                exporter.workbook().then($.proxy(function (book) {\n                    if (!this.trigger('excelExport', { workbook: book })) {\n                        var workbook = new kendo.ooxml.Workbook(book);\n                        workbook.toDataURLAsync().then(function (dataURI) {\n                            kendo.saveAs({\n                                dataURI: dataURI,\n                                fileName: book.fileName || excel.fileName,\n                                proxyURL: excel.proxyURL,\n                                forceProxy: excel.forceProxy\n                            });\n                        });\n                    }\n                }, this));\n            }\n        };\n        kendo.PivotExcelMixin = PivotExcelMixin;\n        if (kendo.ooxml && kendo.ooxml.Workbook) {\n            PivotExcelMixin.extend(PivotGrid.prototype);\n        }\n        if (kendo.PDFMixin) {\n            kendo.PDFMixin.extend(PivotGrid.prototype);\n            PivotGrid.fn._drawPDF = function () {\n                return this._drawPDFShadow({ width: this.wrapper.width() }, { avoidLinks: this.options.pdf.avoidLinks });\n            };\n        }\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}