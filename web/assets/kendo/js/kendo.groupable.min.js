/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.groupable.min",["kendo.core.min","kendo.draganddrop.min"],t)}(function(){return function(t,e){var r=window.kendo,a=r.ui.Widget,n=r._outerWidth,i=r.attr,o=t.extend,d=t.each,s=t.proxy,l=!1,g="dir",c="field",u="title",p="asc",f="desc",h="group-sort",m=".kendoGroupable",k="change",v=r.template('<div class="k-group-indicator" data-#=data.ns#field="${data.field}" data-#=data.ns#title="${data.title || ""}" data-#=data.ns#dir="${data.dir || "asc"}"><a href="\\#" class="k-link"><span class="k-icon k-i-sort-${(data.dir || "asc") == "asc" ? "asc-sm" : "desc-sm"}" title="(sorted ${(data.dir || "asc") == "asc" ? "ascending": "descending"})"></span>${data.title ? data.title: data.field}</a><a class="k-button k-button-icon k-bare"><span class="k-icon k-i-close"></span></a></div>',{useWithBlock:!1}),_=function(e){var a=e.attr(r.attr("title"));return a&&(a=r.htmlEncode(a)),t('<div class="k-header k-group-clue k-drag-clue" />').html(a||e.attr(r.attr("field"))).prepend('<span class="k-icon k-drag-status k-i-cancel" />')},C=t('<div class="k-grouping-dropclue"/>'),b=a.extend({init:function(d,c){var u,v,b=this,I=r.guid(),D=s(b._intializePositions,b),y=b._dropCuePositions=[];a.fn.init.call(b,d,c),l=r.support.isRtl(d),v=l?"right":"left",b.draggable=u=b.options.draggable||new r.ui.Draggable(b.element,{filter:b.options.draggableElements,hint:_,group:I}),b.groupContainer=t(b.options.groupContainer,b.element).kendoDropTarget({group:u.options.group,dragenter:function(t){b._canDrag(t.draggable.currentTarget)&&(t.draggable.hint.find(".k-drag-status").removeClass("k-i-cancel").addClass("k-i-plus"),C.css(v,0).appendTo(b.groupContainer))},dragleave:function(t){t.draggable.hint.find(".k-drag-status").removeClass("k-i-plus").addClass("k-i-cancel"),C.remove()},drop:function(e){var a,n=e.draggable.currentTarget,i=n.attr(r.attr("field")),d=n.attr(r.attr("title")),s=b.indicator(i),g=b._dropCuePositions,c=g[g.length-1],u=o({},b.options.sort,n.data(h)),p=u.dir;(n.hasClass("k-group-indicator")||b._canDrag(n))&&(c?(a=b._dropCuePosition(r.getOffset(C).left+parseInt(c.element.css("marginLeft"),10)*(l?-1:1)+parseInt(c.element.css("marginRight"),10)),a&&b._canDrop(t(s),a.element,a.left)&&(a.before?a.element.before(s||b.buildIndicator(i,d,p)):a.element.after(s||b.buildIndicator(i,d,p)),b._setIndicatorSortOptions(i,u),b._change())):(b.groupContainer.empty(),b.groupContainer.append(b.buildIndicator(i,d,p)),b._setIndicatorSortOptions(i,u),b._change()))}}).kendoDraggable({filter:"div.k-group-indicator",hint:_,group:u.options.group,dragcancel:s(b._dragCancel,b),dragstart:function(t){var e=t.currentTarget,r=parseInt(e.css("marginLeft"),10),a=e.position(),i=l?a.left-r:a.left+n(e);D(),C.css("left",i).appendTo(b.groupContainer),this.hint.find(".k-drag-status").removeClass("k-i-cancel").addClass("k-i-plus")},dragend:function(){b._dragEnd(this)},drag:s(b._drag,b)}).on("click"+m,".k-button",function(e){e.preventDefault(),b._removeIndicator(t(this).parent())}).on("click"+m,".k-link",function(e){var r=t(this).parent(),a=r.attr(i(g))===p?f:p;r.attr(i(g),a),b._change(),e.preventDefault()}),u.bind(["dragend","dragcancel","dragstart","drag"],{dragend:function(){b._dragEnd(this)},dragcancel:s(b._dragCancel,b),dragstart:function(t){var r,a,i;return b.options.allowDrag||b._canDrag(t.currentTarget)?(D(),y.length?(r=y[y.length-1].element,a=parseInt(r.css("marginRight"),10),i=r.position().left+n(r)+a):i=0,e):(t.preventDefault(),e)},drag:s(b._drag,b)}),b.dataSource=b.options.dataSource,b.dataSource&&b._refreshHandler?b.dataSource.unbind(k,b._refreshHandler):b._refreshHandler=s(b.refresh,b),b.dataSource&&(b.dataSource.bind("change",b._refreshHandler),b.refresh())},refresh:function(){var e,r=this,a=r.dataSource,n=a.group()||[],s=i(c),l=i(u);r.groupContainer&&(r.groupContainer.empty(),d(n,function(a,n){var i=n.field,d=n.dir,g=r.element.find(r.options.filter).filter(function(){return t(this).attr(s)===i});e=r.buildIndicator(i,g.attr(l),d),r.groupContainer.append(e),r._setIndicatorSortOptions(i,o({},r.options.sort,{dir:d,compare:n.compare}))})),r._invalidateGroupContainer()},destroy:function(){var t=this;a.fn.destroy.call(t),t.groupContainer.off(m),t.groupContainer.data("kendoDropTarget")&&t.groupContainer.data("kendoDropTarget").destroy(),t.groupContainer.data("kendoDraggable")&&t.groupContainer.data("kendoDraggable").destroy(),t.options.draggable||t.draggable.destroy(),t.dataSource&&t._refreshHandler&&(t.dataSource.unbind("change",t._refreshHandler),t._refreshHandler=null),t.groupContainer=t.element=t.draggable=null},events:["change"],options:{name:"Groupable",filter:"th",draggableElements:"th",messages:{empty:"Drag a column header and drop it here to group by that column"},sort:{dir:p,compare:null}},indicator:function(e){var a=t(".k-group-indicator",this.groupContainer);return t.grep(a,function(a){return t(a).attr(r.attr("field"))===e})[0]},buildIndicator:function(t,e,a){var n=this,i=v({ns:r.ns,field:t.replace(/"/g,"'"),title:e,dir:a||(n.options.sort||{}).dir||p});return i},_setIndicatorSortOptions:function(e,r){var a=t(this.indicator(e));a.data(h,r)},aggregates:function(){var e,a,n,i=this;return i.element.find(i.options.filter).map(function(){var i=t(this),o=i.attr(r.attr("aggregates")),d=i.attr(r.attr("field"));if(o&&""!==o)for(e=o.split(","),o=[],a=0,n=e.length;a<n;a++)o.push({field:d,aggregate:e[a]});return o}).toArray()},descriptors:function(){var e,a=this,n=t(".k-group-indicator",a.groupContainer),i=a.aggregates();return t.map(n,function(n){var o,d;return n=t(n),e=n.attr(r.attr("field")),o=a.options.sort||{},d=n.data(h)||{},{field:e,dir:n.attr(r.attr("dir")),aggregates:i||[],compare:d.compare||o.compare}})},_removeIndicator:function(t){var e=this;t.off(),t.removeData(),t.remove(),e._invalidateGroupContainer(),e._change()},_change:function(){var t,r=this;if(r.dataSource){if(t=r.descriptors(),r.trigger("change",{groups:t}))return r.refresh(),e;r.dataSource.group(t)}},_dropCuePosition:function(e){var r,a,i,o,d,s=this._dropCuePositions;if(C.is(":visible")&&0!==s.length)return e=Math.ceil(e),r=s[s.length-1],a=r.left,i=r.right,o=parseInt(r.element.css("marginLeft"),10),d=parseInt(r.element.css("marginRight"),10),e>=i&&!l||e<a&&l?e={left:r.element.position().left+(l?-o:n(r.element)+d),element:r.element,before:!1}:(e=t.grep(s,function(t){return t.left<=e&&e<=t.right||l&&e>t.right})[0],e&&(e={left:l?e.element.position().left+n(e.element)+d:e.element.position().left-o,element:e.element,before:!0})),e},_drag:function(t){var e=this._dropCuePosition(t.x.location);e&&C.css({left:e.left,right:"auto"})},_canDrag:function(t){var e=t.attr(r.attr("field"));return"false"!=t.attr(r.attr("groupable"))&&e&&(t.hasClass("k-group-indicator")||!this.indicator(e))},_canDrop:function(t,e,r){var a=t.next(),n=t[0]!==e[0]&&(!a[0]||e[0]!==a[0]||!l&&r>a.position().left||l&&r<a.position().left);return n},_dragEnd:function(e){var a=this,n=e.currentTarget.attr(r.attr("field")),i=a.indicator(n);e!==a.options.draggable&&!e.dropped&&i&&a._removeIndicator(t(i)),a._dragCancel()},_dragCancel:function(){C.remove(),this._dropCuePositions=[]},_intializePositions:function(){var e,a=this,i=t(".k-group-indicator",a.groupContainer);a._dropCuePositions=t.map(i,function(a){return a=t(a),e=r.getOffset(a).left,{left:parseInt(e,10),right:parseInt(e+n(a),10),element:a}})},_invalidateGroupContainer:function(){var t=this.groupContainer;t&&t.is(":empty")&&t.html(this.options.messages.empty)}});r.ui.plugin(b)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,r){(r||e)()});
//# sourceMappingURL=kendo.groupable.min.js.map
