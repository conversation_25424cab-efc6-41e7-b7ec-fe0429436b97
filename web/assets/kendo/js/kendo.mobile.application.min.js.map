{"version": 3, "sources": ["kendo.mobile.application.js"], "names": ["f", "define", "$", "undefined", "osCssClass", "os", "options", "classes", "OS", "push", "name", "skin", "majorVersion", "minorVersion", "variant", "setDefaultPlatform", "<PERSON><PERSON>", "appMode", "statusBarStyle", "join", "wp8Background", "noVariantSet", "parseInt", "css", "split", "isOrientationHorizontal", "element", "wp", "Math", "abs", "window", "orientation", "getOrientationClass", "HORIZONTAL", "VERTICAL", "setMinimumHeight", "pane", "parent", "addBack", "innerHeight", "applyViewportHeight", "remove", "HEAD", "append", "viewportTemplate", "height", "support", "mobileOS", "flatVersion", "innerWidth", "kendo", "mobile", "Widget", "ui", "Pane", "DEFAULT_OS", "BERRYPHONEGAP", "device", "FONT_SIZE_COEF", "CHROME", "browser", "BROKEN_WEBVIEW_RESIZE", "ios", "INITIALLY_HORIZONTAL", "MOBILE_PLATFORMS", "ios7", "tablet", "android", "blackberry", "meego", "template", "usedWithBlock", "systemMeta", "clipTemplate", "ENABLE_CLIP", "iconMeta", "HIDEBAR", "SUPPORT_SWIPE_TO_GO_BACK", "HISTORY_TRANSITION", "BARCOMPENSATION", "STATUS_BAR_HEIGHT", "WINDOW", "SCREEN", "screen", "INIT", "proxy", "Application", "extend", "init", "application", "this", "bootstrap", "paneOptions", "that", "startHistory", "document", "body", "fn", "call", "removeAttr", "ns", "_setupPlatform", "_attachMeta", "_setupElementClass", "_attachHideBarHandlers", "navigateToInitial", "updateDocumentTitle", "_setupDocumentTitle", "_startHistory", "trigger", "$angular", "setTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "browserHistory", "historyTransition", "modelScope", "transition", "retina", "platform", "useNativeScrolling", "events", "navigate", "url", "replace", "scroller", "view", "hideLoading", "Error", "showLoading", "changeLoadingMessage", "message", "arguments", "length", "className", "destroy", "router", "refreshBackgroundColorProxy", "removeClass", "addClass", "off", "on", "Router", "pushState", "root", "hashBang", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start", "initial", "_resizeToScreenHeight", "includeStatusBar", "attr", "match", "availWidth", "availHeight", "size", "wpDevicePixelRatio", "devicePixelRatio", "width", "appendTo", "onResize", "resize", "_clearExistingMeta", "find", "filter", "icon", "prepend", "scale", "", "hideBar", "_initialHeight", "scrollTo", "defaultTitle", "title", "bind", "e", "_hideBar", "transforms", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,4BACH,oBACA,gBACDD,IACL,WAsaE,MA3ZC,UAAUE,EAAGC,GA+DV,QAASC,GAAWC,EAAIC,GACpB,GAAIC,KAgCJ,OA/BIC,IACAD,EAAQE,KAAK,SAAWD,EAAGE,MAG3BH,EAAQE,KADRJ,EAAGM,KACU,MAAQN,EAAGM,KAET,OAAXN,EAAGK,MAAiBL,EAAGO,aAAe,EACzB,UAEA,MAAQP,EAAGK,OAGjB,OAAXL,EAAGK,MAAiBL,EAAGO,aAAe,GAAgB,OAAXP,EAAGK,OAC9CH,EAAQE,KAAK,MAAQJ,EAAGK,KAAOL,EAAGO,cAEtCL,EAAQE,KAAK,MAAQJ,EAAGO,cACxBL,EAAQE,KAAK,QAAUJ,EAAGQ,aAAeR,EAAGQ,aAAa,GAAK,IAC1DR,EAAGS,UAAYT,EAAGM,MAAQN,EAAGM,OAASN,EAAGK,OAASL,EAAGM,MAAQN,EAAGU,sBAAuB,IACvFR,EAAQE,KAAK,OAASJ,EAAGM,KAAON,EAAGM,KAAON,EAAGK,MAAQ,IAAML,EAAGS,SAE9DT,EAAGW,SACHT,EAAQE,KAAK,cAGbF,EAAQE,KADRJ,EAAGY,QACU,SAEA,UAEbX,GAAWA,EAAQY,gBACnBX,EAAQE,KAAK,MAAQH,EAAQY,eAAiB,eAE3CX,EAAQY,KAAK,KAExB,QAASC,GAAcf,GACnB,MAAO,UAAYA,EAAGgB,aAAqH,IAAtGC,SAASpB,EAAE,0CAA4CqB,IAAI,oBAAoBC,MAAM,KAAK,GAAI,IAAY,OAAS,QAAUnB,EAAGS,QAAU,UAAYT,EAAGS,QAAU,UAE5M,QAASW,GAAwBC,GAC7B,MAAOlB,GAAGmB,GAAsC,oBAAjCD,EAAQH,IAAI,kBAA0CK,KAAKC,IAAIC,OAAOC,aAAe,IAAM,EAE9G,QAASC,GAAoBN,GACzB,MAAOD,GAAwBC,GAAWO,EAAaC,EAE3D,QAASC,GAAiBC,GACtBA,EAAKC,SAASC,UAAUf,IAAI,aAAcO,OAAOS,aAErD,QAASC,KACLtC,EAAE,uBAAuBuC,SACzBC,EAAKC,OAAOC,GAAmBC,OAAQ,wBAA0BpB,IAA4B,YAAcK,OAAOS,YAAc,KAAOO,EAAQC,SAASC,aAAe,KAAOF,EAAQC,SAASC,YAAc,IAAM,YAAclB,OAAOmB,WAAa,KAAO,6BAhHnQ,GACOC,GAAQpB,OAAOoB,MAAOC,EAASD,EAAMC,OAAQL,EAAUI,EAAMJ,QAASM,EAASD,EAAOE,GAAGD,OAAQE,EAAOH,EAAOE,GAAGC,KAAMC,EAAa,OAAQ/C,EAAKsC,EAAQC,SAAUS,EAA6B,cAAbhD,EAAGiD,QAA0BjD,EAAGwC,aAAe,KAAOxC,EAAGwC,YAAc,KAAQxC,EAAGS,QAASyC,EAAiB,IAAMxB,EAAW,cAAeyB,EAAwB,WAAfnD,EAAGoD,QAAsBC,EAAwBrD,EAAGsD,KAAOtD,EAAGwC,aAAe,KAAOxC,EAAGwC,YAAc,MAAQxC,EAAGS,SAAW0C,GAASI,EAAuBnC,KAAKC,IAAIC,OAAOC,aAAe,IAAM,EAAGE,EAAa,gBAAiB+B,GACtiBC,MACIH,KAAK,EACLF,QAAS,UACTH,OAAQ,SACRT,YAAa,MACbpC,aAAc,IACdC,aAAc,MACdH,KAAM,MACNwD,QAAQ,GAEZJ,KACIA,KAAK,EACLF,QAAS,UACTH,OAAQ,SACRT,YAAa,MACbpC,aAAc,IACdC,aAAc,MACdH,KAAM,MACNwD,QAAQ,GAEZC,SACIA,SAAS,EACTP,QAAS,UACTH,OAAQ,UACRT,YAAa,MACbpC,aAAc,IACdC,aAAc,MACdH,KAAM,UACNwD,QAAQ,GAEZE,YACIA,YAAY,EACZR,QAAS,UACTH,OAAQ,aACRT,YAAa,MACbpC,aAAc,IACdC,aAAc,MACdH,KAAM,aACNwD,QAAQ,GAEZG,OACIA,OAAO,EACPT,QAAS,UACTH,OAAQ,QACRT,YAAa,MACbpC,aAAc,IACdC,aAAc,MACdH,KAAM,QACNwD,QAAQ,GAEZvC,IACIA,IAAI,EACJiC,QAAS,UACTH,OAAQ,KACRT,YAAa,MACbpC,aAAc,IACdC,aAAc,MACdH,KAAM,KACNwD,QAAQ,IAEbtB,EAAmBM,EAAMoB,SAAS,mIAAqIC,eAAe,IAAUC,EAAatB,EAAMoB,SAAS,gQAA4QC,eAAe,IAAUE,EAAevB,EAAMoB,SAAS,qFAAuFC,eAAe,IAAUG,EAAclE,EAAG2D,SAAyB,UAAd3D,EAAGoD,SAAuBpD,EAAG4D,WAAYO,EAAWzB,EAAMoB,SAAS,+BAAiC9D,EAAG2D,QAAU,eAAiB,IAAM,yEAA2EI,eAAe,IAAUK,GAAwB,UAAbpE,EAAGiD,QAAmC,QAAbjD,EAAGiD,SAAqBjD,EAAGI,aAAe,EAAGiE,GAAyC,UAAbrE,EAAGiD,QAAmC,QAAbjD,EAAGiD,SAAqBjD,EAAGI,cAAgB,EAAGkE,EAAqBD,EAA2B,OAAS,KAAME,EAAgC,gBAAdvE,EAAGoD,QAA4B,GAAK,EAAGoB,EAAoB,GAAIC,EAAS/E,EAAE4B,QAASoD,EAASpD,OAAOqD,OAAQzC,EAAOxC,EAAE,QAASkF,EAAO,OAAQC,EAAQnF,EAAEmF,MAoDrzCC,EAAclC,EAAOmC,QACrBC,KAAM,SAAU9D,EAASpB,GACrB6C,EAAOsC,YAAcC,KACrBxF,EAAEA,EAAEmF,MAAMK,KAAM,YAAahE,EAASpB,KAE1CqF,UAAW,SAAUjE,EAASpB,GAAnB,GAWHsF,GAEAC,EAAaC,CAZjBpE,GAAUxB,EAAEwB,GACPA,EAAQ,KACTA,EAAUxB,EAAE6F,SAASC,OAEzB5C,EAAO6C,GAAGT,KAAKU,KAAKR,KAAMhE,EAASpB,GACnCoF,KAAKhE,QAAQyE,WAAW,QAAUjD,EAAMkD,GAAK,QAC7CV,KAAKW,iBACLX,KAAKY,cACLZ,KAAKa,qBACLb,KAAKc,yBACDZ,EAAc1F,EAAEqF,UAAWG,KAAKpF,eAC7BsF,GAAYlF,KACfmF,EAAOH,KAAMI,EAAe,WACxBD,EAAKzD,KAAO,GAAIkB,GAAKuC,EAAKnE,QAASkE,GACnCC,EAAKzD,KAAKqE,oBACNZ,EAAKvF,QAAQoG,qBACbb,EAAKc,sBAETd,EAAKe,gBACLf,EAAKgB,QAAQzB,IAEjBM,KAAKpF,QAAQwG,SACbC,WAAWjB,GAEXA,KAGRxF,SACII,KAAM,cACNsG,gBAAgB,EAChBC,gBAAgB,EAChBC,kBAAmBpC,EACnBqC,WAAYrF,OACZZ,eAAgB,QAChBkG,WAAY,GACZC,QAAQ,EACRC,SAAU,KACV3G,KAAM,KACN+F,qBAAqB,EACrBa,oBAAoB,GAExBC,QAASpC,GACTqC,SAAU,SAAUC,EAAKN,GACrB1B,KAAKtD,KAAKqF,SAASC,EAAKN,IAE5BO,QAAS,SAAUD,EAAKN,GACpB1B,KAAKtD,KAAKuF,QAAQD,EAAKN,IAE3BQ,SAAU,WACN,MAAOlC,MAAKmC,OAAOD,UAEvBE,YAAa,WACT,IAAIpC,KAAKtD,KAGL,KAAU2F,OAAM,uIAFhBrC,MAAKtD,KAAK0F,eAKlBE,YAAa,WACT,IAAItC,KAAKtD,KAGL,KAAU2F,OAAM,uIAFhBrC,MAAKtD,KAAK4F,eAKlBC,qBAAsB,SAAUC,GAC5B,IAAIxC,KAAKtD,KAGL,KAAU2F,OAAM,yIAFhBrC,MAAKtD,KAAK6F,qBAAqBC,IAKvCL,KAAM,WACF,MAAOnC,MAAKtD,KAAKyF,QAErBlH,KAAM,SAAUA,GACZ,GAAIkF,GAAOH,IACX,OAAKyC,WAAUC,QAGfvC,EAAKvF,QAAQK,KAAOA,GAAQ,GAC5BkF,EAAKnE,QAAQ,GAAG2G,UAAY,UAC5BxC,EAAKQ,iBACLR,EAAKU,qBACEV,EAAKvF,QAAQK,MANTkF,EAAKvF,QAAQK,MAQ5B2H,QAAS,WACLlF,EAAO6C,GAAGqC,QAAQpC,KAAKR,MACvBA,KAAKtD,KAAKkG,UACN5C,KAAKpF,QAAQ2G,gBACbvB,KAAK6C,OAAOD,WAGpBjC,eAAgB,WACZ,GAAIR,GAAOH,KAAM4B,EAAWzB,EAAKvF,QAAQgH,SAAU3G,EAAOkF,EAAKvF,QAAQK,KAAMa,KAAYnB,EAAKG,GAAMwD,EAAiBT,EACjH+D,KACAjH,EAAGU,oBAAqB,EACA,gBAAbuG,IACP9F,EAAQ8F,EAAS9F,MAAM,KACvBnB,EAAKH,EAAEqF,QAASzE,QAASU,EAAM,IAAMnB,EAAI2D,EAAiBxC,EAAM,MAEhEnB,EAAKiH,GAGT3G,IACAa,EAAQb,EAAKa,MAAM,KACdhB,IACDH,EAAGU,oBAAqB,GAE5BV,EAAKH,EAAEqF,UAAWlF,GACdM,KAAMa,EAAM,GACZV,QAASU,EAAM,MAGlBnB,EAAGS,UACJT,EAAGgB,cAAe,EAClBhB,EAAGS,QAAU,QAEjB+E,EAAKxF,GAAKA,EACVwF,EAAKzF,WAAaA,EAAWyF,EAAKxF,GAAIwF,EAAKvF,SAC5B,MAAXD,EAAGK,OACEmF,EAAK2C,8BACN3C,EAAK2C,4BAA8BtI,EAAEmF,MAAM,YACnCQ,EAAKxF,GAAGS,SAAY+E,EAAKxF,GAAGM,MAAQkF,EAAKxF,GAAGM,OAASkF,EAAKxF,GAAGK,OAAUmF,EAAKxF,GAAGM,OAC/EkF,EAAKnE,QAAQ+G,YAAY,6DAA6DC,SAAStH,EAAcyE,EAAKxF,MAEvHwF,IAEP3F,EAAE6F,UAAU4C,IAAI,mBAAoB9C,EAAK2C,6BACzCtI,EAAE6F,UAAU4C,IAAI,SAAU9C,EAAK2C,6BAC1BnI,EAAGM,OACJkF,EAAKnE,QAAQW,SAASd,IAAI,WAAY,UACtCrB,EAAE6F,UAAU6C,GAAG,mBAAoB/C,EAAK2C,6BACxCtI,EAAE6F,UAAU6C,GAAG,SAAU/C,EAAK2C,6BAC9B3C,EAAK2C,iCAIjB5B,cAAe,WACPlB,KAAKpF,QAAQ2G,gBACbvB,KAAK6C,OAAS,GAAIrF,GAAM2F,QACpBC,UAAWpD,KAAKpF,QAAQwI,UACxBC,KAAMrD,KAAKpF,QAAQyI,KACnBC,SAAUtD,KAAKpF,QAAQ0I,WAE3BtD,KAAKtD,KAAK6G,aAAavD,KAAK6C,QAC5B7C,KAAK6C,OAAOW,SAEPxD,KAAKpF,QAAQ6I,SACdzD,KAAKtD,KAAKqF,SAAS,KAI/B2B,sBAAuB,WACnB,GAAwJvG,GAApJwG,EAAmBnJ,EAAE,oDAAoDoJ,KAAK,WAAWC,MAAM,4BAA6B7H,EAAUgE,KAAKhE,OAE3ImB,GADAc,EACS7B,OAAOS,YAEZd,EAAwBC,GACpB2H,EACItF,EACSmB,EAAOsE,WAAaxE,EAEpBE,EAAOsE,WAGhBzF,EACSmB,EAAOsE,WAEPtE,EAAOsE,WAAaxE,EAIjCqE,EACItF,EACSmB,EAAOuE,YAEPvE,EAAOuE,YAAczE,EAG9BjB,EACSmB,EAAOuE,YAAczE,EAErBE,EAAOuE,YAKhC/H,EAAQmB,OAAOA,IAEnB0D,mBAAoB,WAChB,GAAiBmD,GAAb7D,EAAOH,KAAYhE,EAAUmE,EAAKnE,OACtCA,GAAQW,SAASqG,SAAS,eAAiB7C,EAAKxF,GAAG6D,OAAS,SAAW,UACvExC,EAAQgH,SAAS7C,EAAKzF,WAAa,IAAM4B,EAAoBN,IACzDgE,KAAKpF,QAAQiH,oBACb7F,EAAQW,SAASqG,SAAS,uBAE1B/E,GACAjC,EAAQgH,SAAS,iBAEjB5F,EAAQ6G,oBACRjI,EAAQW,SAASd,IAAI,YAAauB,EAAQ6G,mBAAqB,MAE/DjE,KAAKpF,QAAQ+G,SACb3F,EAAQW,SAASqG,SAAS,aAC1BhH,EAAQW,SAASd,IAAI,YAAauB,EAAQ8G,iBAAmBlG,EAAiB,OAE9EF,GACAhB,IAEAqD,EAAKvF,QAAQiH,mBACb7F,EAAQW,SAASqG,SAAS,uBACnBhE,IACPgF,GAAQvE,OAAOqE,WAAarE,OAAOsE,YAActE,OAAOqE,WAAarE,OAAOsE,aAAe,IAC3FvJ,EAAEuE,GACEoF,MAAOH,EACP7G,OAAQ6G,KACRI,SAASpH,IAEbmB,GACAgC,EAAKuD,wBAETlG,EAAM6G,SAAS,WACXrI,EAAQ+G,YAAY,6BAA6BC,SAAS1G,EAAoBN,IAC1EmE,EAAKvF,QAAQiH,oBACbpF,EAAiBT,GAEjBmC,GACAgC,EAAKuD,wBAEL5F,GACAhB,IAEJU,EAAM8G,OAAOtI,MAGrBuI,mBAAoB,WAChBvH,EAAKwH,KAAK,QAAQC,OAAO,gFAAsF1H,UAEnH6D,YAAa,WACT,GAAiDoD,GAA7CpJ,EAAUoF,KAAKpF,QAAS8J,EAAO9J,EAAQ8J,IAS3C,IARA1E,KAAKuE,qBACAzG,GACDd,EAAK2H,QAAQzH,GACTC,OAAQ,GACRyH,MAAO5E,KAAKpF,QAAQ+G,OAAS,EAAIvE,EAAQ8G,iBAAmB,SAGpElH,EAAK2H,QAAQ7F,EAAWlE,IACpB8J,EAAM,CACc,gBAATA,KACPA,GAASG,GAAIH,GAEjB,KAAKV,IAAQU,GACT1H,EAAK2H,QAAQ1F,GACTyF,KAAMA,EAAKV,GACXA,KAAMA,KAIdpJ,EAAQiH,oBACRpF,EAAiBuD,KAAKhE,UAG9B8E,uBAAwB,WACpB,GAAIX,GAAOH,KAAM8E,EAAUnF,EAAMQ,EAAM,aACnC/C,EAAQC,SAAS9B,SAAY4E,EAAKvF,QAAQ0G,gBAAmBpC,IAAWiB,EAAKvF,QAAQiH,qBAGzF1B,EAAK4E,kBACLxF,EAAO2D,GAAG,OAAQ4B,GAClBtH,EAAM6G,SAAS,WACXhD,WAAWjF,OAAO4I,SAAU,EAAG,EAAG,OAG1C/D,oBAAqB,WACjB,GAAId,GAAOH,KAAMiF,EAAe5E,SAAS6E,KACzC/E,GAAKzD,KAAKyI,KAAK,WAAY,SAAUC,GACjC,GAAIF,GAAQE,EAAEjD,KAAK+C,KACnB7E,UAAS6E,MAAQA,IAAUzK,EAAYyK,EAAQD,KAGvDI,SAAU,WACN,GAAIlF,GAAOH,KAAMhE,EAAUmE,EAAKnE,OAChCA,GAAQmB,OAAOK,EAAMJ,QAAQkI,WAAWzJ,IAAM,eAAiBwD,EAAkB,OACjF7E,EAAE4B,QAAQ+E,QAAQ3D,EAAMJ,QAAQkH,UAGxC9G,GAAMC,OAAOmC,YAAcA,EAC3BpC,EAAMG,GAAG4H,OAAO3F,EAAapC,EAAMC,OAAQ,WAC7CrB,OAAOoB,MAAMgI,QACRpJ,OAAOoB,OACE,kBAAVjD,SAAwBA,OAAOkL,IAAMlL,OAAS,SAAUmL,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.application.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.application', [\n        'kendo.mobile.pane',\n        'kendo.router'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.application',\n        name: 'Application',\n        category: 'mobile',\n        description: 'The Mobile application provides a framework to build native looking web applications on mobile devices.',\n        depends: [\n            'mobile.pane',\n            'router'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, mobile = kendo.mobile, support = kendo.support, Widget = mobile.ui.Widget, Pane = mobile.ui.Pane, DEFAULT_OS = 'ios7', OS = support.mobileOS, BERRYPHONEGAP = OS.device == 'blackberry' && OS.flatVersion >= 600 && OS.flatVersion < 1000 && OS.appMode, FONT_SIZE_COEF = 0.93, VERTICAL = 'km-vertical', CHROME = OS.browser === 'chrome', BROKEN_WEBVIEW_RESIZE = OS.ios && OS.flatVersion >= 700 && OS.flatVersion < 800 && (OS.appMode || CHROME), INITIALLY_HORIZONTAL = Math.abs(window.orientation) / 90 == 1, HORIZONTAL = 'km-horizontal', MOBILE_PLATFORMS = {\n                ios7: {\n                    ios: true,\n                    browser: 'default',\n                    device: 'iphone',\n                    flatVersion: '700',\n                    majorVersion: '7',\n                    minorVersion: '0.0',\n                    name: 'ios',\n                    tablet: false\n                },\n                ios: {\n                    ios: true,\n                    browser: 'default',\n                    device: 'iphone',\n                    flatVersion: '612',\n                    majorVersion: '6',\n                    minorVersion: '1.2',\n                    name: 'ios',\n                    tablet: false\n                },\n                android: {\n                    android: true,\n                    browser: 'default',\n                    device: 'android',\n                    flatVersion: '442',\n                    majorVersion: '4',\n                    minorVersion: '4.2',\n                    name: 'android',\n                    tablet: false\n                },\n                blackberry: {\n                    blackberry: true,\n                    browser: 'default',\n                    device: 'blackberry',\n                    flatVersion: '710',\n                    majorVersion: '7',\n                    minorVersion: '1.0',\n                    name: 'blackberry',\n                    tablet: false\n                },\n                meego: {\n                    meego: true,\n                    browser: 'default',\n                    device: 'meego',\n                    flatVersion: '850',\n                    majorVersion: '8',\n                    minorVersion: '5.0',\n                    name: 'meego',\n                    tablet: false\n                },\n                wp: {\n                    wp: true,\n                    browser: 'default',\n                    device: 'wp',\n                    flatVersion: '800',\n                    majorVersion: '8',\n                    minorVersion: '0.0',\n                    name: 'wp',\n                    tablet: false\n                }\n            }, viewportTemplate = kendo.template('<meta content=\"initial-scale=#: data.scale #, maximum-scale=#: data.scale #, user-scalable=no#=data.height#\" name=\"viewport\" />', { usedWithBlock: false }), systemMeta = kendo.template('<meta name=\"apple-mobile-web-app-capable\" content=\"#= data.webAppCapable === false ? \\'no\\' : \\'yes\\' #\" /> ' + '<meta name=\"apple-mobile-web-app-status-bar-style\" content=\"#=data.statusBarStyle#\" /> ' + '<meta name=\"msapplication-tap-highlight\" content=\"no\" /> ', { usedWithBlock: false }), clipTemplate = kendo.template('<style>.km-view { clip: rect(0 #= data.width #px #= data.height #px 0); }</style>', { usedWithBlock: false }), ENABLE_CLIP = OS.android && OS.browser != 'chrome' || OS.blackberry, iconMeta = kendo.template('<link rel=\"apple-touch-icon' + (OS.android ? '-precomposed' : '') + '\" # if(data.size) { # sizes=\"#=data.size#\" #}# href=\"#=data.icon#\" />', { usedWithBlock: false }), HIDEBAR = (OS.device == 'iphone' || OS.device == 'ipod') && OS.majorVersion < 7, SUPPORT_SWIPE_TO_GO_BACK = (OS.device == 'iphone' || OS.device == 'ipod') && OS.majorVersion >= 7, HISTORY_TRANSITION = SUPPORT_SWIPE_TO_GO_BACK ? 'none' : null, BARCOMPENSATION = OS.browser == 'mobilesafari' ? 60 : 0, STATUS_BAR_HEIGHT = 20, WINDOW = $(window), SCREEN = window.screen, HEAD = $('head'), INIT = 'init', proxy = $.proxy;\n        function osCssClass(os, options) {\n            var classes = [];\n            if (OS) {\n                classes.push('km-on-' + OS.name);\n            }\n            if (os.skin) {\n                classes.push('km-' + os.skin);\n            } else {\n                if (os.name == 'ios' && os.majorVersion > 6) {\n                    classes.push('km-ios7');\n                } else {\n                    classes.push('km-' + os.name);\n                }\n            }\n            if (os.name == 'ios' && os.majorVersion < 7 || os.name != 'ios') {\n                classes.push('km-' + os.name + os.majorVersion);\n            }\n            classes.push('km-' + os.majorVersion);\n            classes.push('km-m' + (os.minorVersion ? os.minorVersion[0] : 0));\n            if (os.variant && (os.skin && os.skin === os.name || !os.skin || os.setDefaultPlatform === false)) {\n                classes.push('km-' + (os.skin ? os.skin : os.name) + '-' + os.variant);\n            }\n            if (os.cordova) {\n                classes.push('km-cordova');\n            }\n            if (os.appMode) {\n                classes.push('km-app');\n            } else {\n                classes.push('km-web');\n            }\n            if (options && options.statusBarStyle) {\n                classes.push('km-' + options.statusBarStyle + '-status-bar');\n            }\n            return classes.join(' ');\n        }\n        function wp8Background(os) {\n            return 'km-wp-' + (os.noVariantSet ? parseInt($('<div style=\\'background: Background\\' />').css('background-color').split(',')[1], 10) === 0 ? 'dark' : 'light' : os.variant + ' km-wp-' + os.variant + '-force');\n        }\n        function isOrientationHorizontal(element) {\n            return OS.wp ? element.css('animation-name') == '-kendo-landscape' : Math.abs(window.orientation) / 90 == 1;\n        }\n        function getOrientationClass(element) {\n            return isOrientationHorizontal(element) ? HORIZONTAL : VERTICAL;\n        }\n        function setMinimumHeight(pane) {\n            pane.parent().addBack().css('min-height', window.innerHeight);\n        }\n        function applyViewportHeight() {\n            $('meta[name=viewport]').remove();\n            HEAD.append(viewportTemplate({ height: ', width=device-width' + (isOrientationHorizontal() ? ', height=' + window.innerHeight + 'px' : support.mobileOS.flatVersion >= 600 && support.mobileOS.flatVersion < 700 ? ', height=' + window.innerWidth + 'px' : ', height=device-height') }));\n        }\n        var Application = Widget.extend({\n            init: function (element, options) {\n                mobile.application = this;\n                $($.proxy(this, 'bootstrap', element, options));\n            },\n            bootstrap: function (element, options) {\n                element = $(element);\n                if (!element[0]) {\n                    element = $(document.body);\n                }\n                Widget.fn.init.call(this, element, options);\n                this.element.removeAttr('data-' + kendo.ns + 'role');\n                this._setupPlatform();\n                this._attachMeta();\n                this._setupElementClass();\n                this._attachHideBarHandlers();\n                var paneOptions = $.extend({}, this.options);\n                delete paneOptions.name;\n                var that = this, startHistory = function () {\n                        that.pane = new Pane(that.element, paneOptions);\n                        that.pane.navigateToInitial();\n                        if (that.options.updateDocumentTitle) {\n                            that._setupDocumentTitle();\n                        }\n                        that._startHistory();\n                        that.trigger(INIT);\n                    };\n                if (this.options.$angular) {\n                    setTimeout(startHistory);\n                } else {\n                    startHistory();\n                }\n            },\n            options: {\n                name: 'Application',\n                hideAddressBar: true,\n                browserHistory: true,\n                historyTransition: HISTORY_TRANSITION,\n                modelScope: window,\n                statusBarStyle: 'black',\n                transition: '',\n                retina: false,\n                platform: null,\n                skin: null,\n                updateDocumentTitle: true,\n                useNativeScrolling: false\n            },\n            events: [INIT],\n            navigate: function (url, transition) {\n                this.pane.navigate(url, transition);\n            },\n            replace: function (url, transition) {\n                this.pane.replace(url, transition);\n            },\n            scroller: function () {\n                return this.view().scroller;\n            },\n            hideLoading: function () {\n                if (this.pane) {\n                    this.pane.hideLoading();\n                } else {\n                    throw new Error('The mobile application instance is not fully instantiated. Please consider activating loading in the application init event handler.');\n                }\n            },\n            showLoading: function () {\n                if (this.pane) {\n                    this.pane.showLoading();\n                } else {\n                    throw new Error('The mobile application instance is not fully instantiated. Please consider activating loading in the application init event handler.');\n                }\n            },\n            changeLoadingMessage: function (message) {\n                if (this.pane) {\n                    this.pane.changeLoadingMessage(message);\n                } else {\n                    throw new Error('The mobile application instance is not fully instantiated. Please consider changing the message in the application init event handler.');\n                }\n            },\n            view: function () {\n                return this.pane.view();\n            },\n            skin: function (skin) {\n                var that = this;\n                if (!arguments.length) {\n                    return that.options.skin;\n                }\n                that.options.skin = skin || '';\n                that.element[0].className = 'km-pane';\n                that._setupPlatform();\n                that._setupElementClass();\n                return that.options.skin;\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.pane.destroy();\n                if (this.options.browserHistory) {\n                    this.router.destroy();\n                }\n            },\n            _setupPlatform: function () {\n                var that = this, platform = that.options.platform, skin = that.options.skin, split = [], os = OS || MOBILE_PLATFORMS[DEFAULT_OS];\n                if (platform) {\n                    os.setDefaultPlatform = true;\n                    if (typeof platform === 'string') {\n                        split = platform.split('-');\n                        os = $.extend({ variant: split[1] }, os, MOBILE_PLATFORMS[split[0]]);\n                    } else {\n                        os = platform;\n                    }\n                }\n                if (skin) {\n                    split = skin.split('-');\n                    if (!OS) {\n                        os.setDefaultPlatform = false;\n                    }\n                    os = $.extend({}, os, {\n                        skin: split[0],\n                        variant: split[1]\n                    });\n                }\n                if (!os.variant) {\n                    os.noVariantSet = true;\n                    os.variant = 'dark';\n                }\n                that.os = os;\n                that.osCssClass = osCssClass(that.os, that.options);\n                if (os.name == 'wp') {\n                    if (!that.refreshBackgroundColorProxy) {\n                        that.refreshBackgroundColorProxy = $.proxy(function () {\n                            if (that.os.variant && (that.os.skin && that.os.skin === that.os.name) || !that.os.skin) {\n                                that.element.removeClass('km-wp-dark km-wp-light km-wp-dark-force km-wp-light-force').addClass(wp8Background(that.os));\n                            }\n                        }, that);\n                    }\n                    $(document).off('visibilitychange', that.refreshBackgroundColorProxy);\n                    $(document).off('resume', that.refreshBackgroundColorProxy);\n                    if (!os.skin) {\n                        that.element.parent().css('overflow', 'hidden');\n                        $(document).on('visibilitychange', that.refreshBackgroundColorProxy);\n                        $(document).on('resume', that.refreshBackgroundColorProxy);\n                        that.refreshBackgroundColorProxy();\n                    }\n                }\n            },\n            _startHistory: function () {\n                if (this.options.browserHistory) {\n                    this.router = new kendo.Router({\n                        pushState: this.options.pushState,\n                        root: this.options.root,\n                        hashBang: this.options.hashBang\n                    });\n                    this.pane.bindToRouter(this.router);\n                    this.router.start();\n                } else {\n                    if (!this.options.initial) {\n                        this.pane.navigate('');\n                    }\n                }\n            },\n            _resizeToScreenHeight: function () {\n                var includeStatusBar = $('meta[name=apple-mobile-web-app-status-bar-style]').attr('content').match(/black-translucent|hidden/), element = this.element, height;\n                if (CHROME) {\n                    height = window.innerHeight;\n                } else {\n                    if (isOrientationHorizontal(element)) {\n                        if (includeStatusBar) {\n                            if (INITIALLY_HORIZONTAL) {\n                                height = SCREEN.availWidth + STATUS_BAR_HEIGHT;\n                            } else {\n                                height = SCREEN.availWidth;\n                            }\n                        } else {\n                            if (INITIALLY_HORIZONTAL) {\n                                height = SCREEN.availWidth;\n                            } else {\n                                height = SCREEN.availWidth - STATUS_BAR_HEIGHT;\n                            }\n                        }\n                    } else {\n                        if (includeStatusBar) {\n                            if (INITIALLY_HORIZONTAL) {\n                                height = SCREEN.availHeight;\n                            } else {\n                                height = SCREEN.availHeight + STATUS_BAR_HEIGHT;\n                            }\n                        } else {\n                            if (INITIALLY_HORIZONTAL) {\n                                height = SCREEN.availHeight - STATUS_BAR_HEIGHT;\n                            } else {\n                                height = SCREEN.availHeight;\n                            }\n                        }\n                    }\n                }\n                element.height(height);\n            },\n            _setupElementClass: function () {\n                var that = this, size, element = that.element;\n                element.parent().addClass('km-root km-' + (that.os.tablet ? 'tablet' : 'phone'));\n                element.addClass(that.osCssClass + ' ' + getOrientationClass(element));\n                if (this.options.useNativeScrolling) {\n                    element.parent().addClass('km-native-scrolling');\n                }\n                if (CHROME) {\n                    element.addClass('km-ios-chrome');\n                }\n                if (support.wpDevicePixelRatio) {\n                    element.parent().css('font-size', support.wpDevicePixelRatio + 'em');\n                }\n                if (this.options.retina) {\n                    element.parent().addClass('km-retina');\n                    element.parent().css('font-size', support.devicePixelRatio * FONT_SIZE_COEF + 'em');\n                }\n                if (BERRYPHONEGAP) {\n                    applyViewportHeight();\n                }\n                if (that.options.useNativeScrolling) {\n                    element.parent().addClass('km-native-scrolling');\n                } else if (ENABLE_CLIP) {\n                    size = (screen.availWidth > screen.availHeight ? screen.availWidth : screen.availHeight) + 200;\n                    $(clipTemplate({\n                        width: size,\n                        height: size\n                    })).appendTo(HEAD);\n                }\n                if (BROKEN_WEBVIEW_RESIZE) {\n                    that._resizeToScreenHeight();\n                }\n                kendo.onResize(function () {\n                    element.removeClass('km-horizontal km-vertical').addClass(getOrientationClass(element));\n                    if (that.options.useNativeScrolling) {\n                        setMinimumHeight(element);\n                    }\n                    if (BROKEN_WEBVIEW_RESIZE) {\n                        that._resizeToScreenHeight();\n                    }\n                    if (BERRYPHONEGAP) {\n                        applyViewportHeight();\n                    }\n                    kendo.resize(element);\n                });\n            },\n            _clearExistingMeta: function () {\n                HEAD.find('meta').filter('[name|=\\'apple-mobile-web-app\\'],[name|=\\'msapplication-tap\\'],[name=\\'viewport\\']').remove();\n            },\n            _attachMeta: function () {\n                var options = this.options, icon = options.icon, size;\n                this._clearExistingMeta();\n                if (!BERRYPHONEGAP) {\n                    HEAD.prepend(viewportTemplate({\n                        height: '',\n                        scale: this.options.retina ? 1 / support.devicePixelRatio : '1.0'\n                    }));\n                }\n                HEAD.prepend(systemMeta(options));\n                if (icon) {\n                    if (typeof icon === 'string') {\n                        icon = { '': icon };\n                    }\n                    for (size in icon) {\n                        HEAD.prepend(iconMeta({\n                            icon: icon[size],\n                            size: size\n                        }));\n                    }\n                }\n                if (options.useNativeScrolling) {\n                    setMinimumHeight(this.element);\n                }\n            },\n            _attachHideBarHandlers: function () {\n                var that = this, hideBar = proxy(that, '_hideBar');\n                if (support.mobileOS.appMode || !that.options.hideAddressBar || !HIDEBAR || that.options.useNativeScrolling) {\n                    return;\n                }\n                that._initialHeight = {};\n                WINDOW.on('load', hideBar);\n                kendo.onResize(function () {\n                    setTimeout(window.scrollTo, 0, 0, 1);\n                });\n            },\n            _setupDocumentTitle: function () {\n                var that = this, defaultTitle = document.title;\n                that.pane.bind('viewShow', function (e) {\n                    var title = e.view.title;\n                    document.title = title !== undefined ? title : defaultTitle;\n                });\n            },\n            _hideBar: function () {\n                var that = this, element = that.element;\n                element.height(kendo.support.transforms.css + 'calc(100% + ' + BARCOMPENSATION + 'px)');\n                $(window).trigger(kendo.support.resize);\n            }\n        });\n        kendo.mobile.Application = Application;\n        kendo.ui.plugin(Application, kendo.mobile, 'Mobile');\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}