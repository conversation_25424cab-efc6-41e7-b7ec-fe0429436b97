{"version": 3, "sources": ["kendo.mobile.scroller.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "mobile", "fx", "effects", "ui", "proxy", "extend", "Widget", "Class", "Movable", "Pane", "PaneDimensions", "Transition", "Animation", "abs", "Math", "SNAPBACK_DURATION", "SCROLLBAR_OPACITY", "FRICTION", "VELOCITY_MULTIPLIER", "MAX_VELOCITY", "OUT_OF_BOUNDS_FRICTION", "ANIMATED_SCROLLER_PRECISION", "RELEASECLASS", "REFRESHCLASS", "PULL", "CHANGE", "RESIZE", "SCROLL", "MOUSE_WHEEL_ID", "ZoomSnapBack", "init", "options", "that", "this", "fn", "call", "userEvents", "bind", "start", "tapCapture", "cancel", "enabled", "movable", "scale", "dimensions", "minScale", "done", "tick", "scaleWith", "rescale", "onEnd", "scaleTo", "DragInertia", "transition", "axis", "_end", "onCancel", "freeze", "location", "_moveTo", "paneAxis", "outOfBounds", "_snapBack", "velocity", "e", "dimension", "touch", "id", "max", "min", "velocityMultiplier", "captureNext", "friction", "delta", "elastic", "moveAxis", "cancelCapture", "end", "snapBack", "moveTo", "duration", "ease", "easeOutExpo", "AnimatedS<PERSON>roller", "origin", "destination", "offset", "_updateCoordinates", "y", "x", "callback", "setCoordinates", "from", "to", "<PERSON><PERSON><PERSON><PERSON>", "isFunction", "<PERSON><PERSON>Bar", "horizontal", "element", "elementSize", "scrollMovable", "alwaysVisible", "size", "refresh", "container", "append", "show", "paneSize", "sizeRatio", "total", "position", "round", "css", "opacity", "visibility", "hide", "<PERSON><PERSON><PERSON>", "inner", "avoidScrolling", "pane", "zoomSnapBack", "animatedScroller", "_native", "useNative", "support", "hasNativeScrolling", "addClass", "prepend", "scrollElement", "fixedContainer", "children", "first", "wrapInner", "eq", "TapCapture", "forcedEnabled", "zoom", "UserEvents", "touchAction", "fastTap", "allowSelection", "preventDragEvent", "captureUpIfMoved", "multiTouch", "supportDoubleTap", "velocityX", "velocityY", "horizontalSwipe", "originatedFromFixedContainer", "contains", "event", "target", "verticalSwipe", "capture", "coordinates", "scrollTo", "scrollTop", "scrollLeft", "trigger", "mousewheelScrolling", "on", "pulled", "_initAxis", "_wheelEnd", "_wheel", "_wheelY", "pullToRefresh", "_initPullToRefresh", "_wheelScroll", "press", "clearTimeout", "_wheelTimeout", "setTimeout", "wheelDeltaY", "move", "preventDefault", "makeVirtual", "virtualSize", "height", "scrollHeight", "scrollWidth", "name", "pullOffset", "visibleScrollHints", "messages", "pullTemplate", "releaseTemplate", "refreshTemplate", "events", "_resize", "contentResized", "setOptions", "reset", "_scale", "zoomOut", "fitScale", "centerCoordinates", "enable", "disable", "animatedScrollTo", "pullHandled", "refreshHint", "removeClass", "hintContainer", "html", "yiner<PERSON>", "xinertia", "destroy", "forceEnabled", "template", "_paneChange", "_dragEnd", "scrollBar", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,yBACH,WACA,qBACDD,IACL,WAogBE,MAzfC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAME,OAAQC,EAAKH,EAAMI,QAASC,EAAKH,EAAOG,GAAIC,EAAQR,EAAEQ,MAAOC,EAAST,EAAES,OAAQC,EAASH,EAAGG,OAAQC,EAAQT,EAAMS,MAAOC,EAAUV,EAAMK,GAAGK,QAASC,EAAOX,EAAMK,GAAGM,KAAMC,EAAiBZ,EAAMK,GAAGO,eAAgBC,EAAaV,EAAGU,WAAYC,EAAYX,EAAGW,UAAWC,EAAMC,KAAKD,IAAKE,EAAoB,IAAKC,EAAoB,GAAKC,EAAW,IAAMC,EAAsB,GAAIC,EAAe,GAAIC,EAAyB,GAAKC,EAA8B,EAAGC,EAAe,sBAAuBC,EAAe,sBAAuBC,EAAO,OAAQC,EAAS,SAAUC,EAAS,SAAUC,EAAS,SAAUC,EAAiB,EACppBC,EAAejB,EAAUP,QACzByB,KAAM,SAAUC,GACZ,GAAIC,GAAOC,IACXrB,GAAUsB,GAAGJ,KAAKK,KAAKH,GACvB3B,EAAO2B,EAAMD,GACbC,EAAKI,WAAWC,KAAK,aAAcjC,EAAM4B,EAAKM,MAAON,IACrDA,EAAKO,WAAWF,KAAK,QAASjC,EAAM4B,EAAKQ,OAAQR,KAErDS,QAAS,WACL,MAAOR,MAAKS,QAAQC,MAAQV,KAAKW,WAAWC,UAEhDC,KAAM,WACF,MAAOb,MAAKW,WAAWC,SAAWZ,KAAKS,QAAQC,MAAQ,KAE3DI,KAAM,WACF,GAAIL,GAAUT,KAAKS,OACnBA,GAAQM,UAAU,KAClBf,KAAKW,WAAWK,QAAQP,EAAQC,QAEpCO,MAAO,WACH,GAAIR,GAAUT,KAAKS,OACnBA,GAAQS,QAAQlB,KAAKW,WAAWC,UAChCZ,KAAKW,WAAWK,QAAQP,EAAQC,UAGpCS,EAAcxC,EAAUP,QACxByB,KAAM,SAAUC,GACZ,GAAIC,GAAOC,IACXrB,GAAUsB,GAAGJ,KAAKK,KAAKH,GACvB3B,EAAO2B,EAAMD,GACTsB,WAAY,GAAI1C,IACZ2C,KAAMvB,EAAQuB,KACdZ,QAASX,EAAQW,QACjBQ,MAAO,WACHlB,EAAKuB,YAIjBvB,EAAKO,WAAWF,KAAK,QAAS,WAC1BL,EAAKQ,WAETR,EAAKI,WAAWC,KAAK,MAAOjC,EAAM4B,EAAKM,MAAON,IAC9CA,EAAKI,WAAWC,KAAK,aAAcjC,EAAM4B,EAAKM,MAAON,IACrDA,EAAKI,WAAWC,KAAK,MAAOjC,EAAM4B,EAAKkB,MAAOlB,KAElDwB,SAAU,WACNvB,KAAKoB,WAAWb,UAEpBiB,OAAQ,SAAUC,GACd,GAAI1B,GAAOC,IACXD,GAAKQ,SACLR,EAAK2B,QAAQD,IAEjBR,MAAO,WACH,GAAIlB,GAAOC,IACPD,GAAK4B,SAASC,cACd7B,EAAK8B,YAEL9B,EAAKuB,QAGbT,KAAM,WACF,MAAOjC,GAAIoB,KAAK8B,UAAY,GAEhCzB,MAAO,SAAU0B,GACb,GAAiBD,GAAb/B,EAAOC,IACND,GAAKiC,UAAUxB,UAGhBT,EAAK4B,SAASC,cACd7B,EAAK8B,aAELC,EAAWC,EAAEE,MAAMC,KAAOvC,EAAiB,EAAIoC,EAAEE,MAAMlC,EAAKsB,MAAMS,SAClE/B,EAAK+B,SAAWjD,KAAKsD,IAAItD,KAAKuD,IAAIN,EAAW/B,EAAKsC,mBAAoBnD,IAAgBA,GACtFa,EAAKO,WAAWgC,cAChB3D,EAAUsB,GAAGI,MAAMH,KAAKH,MAGhCe,KAAM,WACF,GAAIf,GAAOC,KAAMgC,EAAYjC,EAAKiC,UAAWO,EAAWxC,EAAK4B,SAASC,cAAgBzC,EAAyBY,EAAKwC,SAAUC,EAAQzC,EAAK+B,UAAYS,EAAUd,EAAW1B,EAAKU,QAAQV,EAAKsB,MAAQmB,GACjMzC,EAAK0C,SAAWT,EAAUJ,YAAYH,KACvCA,EAAW5C,KAAKsD,IAAItD,KAAKuD,IAAIX,EAAUO,EAAUG,KAAMH,EAAUI,KACjErC,EAAK+B,SAAW,GAEpB/B,EAAKU,QAAQiC,SAAS3C,EAAKsB,KAAMI,IAErCH,KAAM,WACFtB,KAAKM,WAAWqC,gBAChB3C,KAAK4C,OAETf,UAAW,WACP,GAAI9B,GAAOC,KAAMgC,EAAYjC,EAAKiC,UAAWa,EAAW9C,EAAKU,QAAQV,EAAKsB,MAAQW,EAAUG,IAAMH,EAAUG,IAAMH,EAAUI,GAC5HrC,GAAK2B,QAAQmB,IAEjBnB,QAAS,SAAUD,GACfzB,KAAKoB,WAAW0B,QACZrB,SAAUA,EACVsB,SAAUjE,EACVkE,KAAMtE,EAAWuE,iBAIzBC,EAAmBvE,EAAUP,QAC7ByB,KAAM,SAAUC,GACZ,GAAIC,GAAOC,IACXnC,GAAMI,QAAQU,UAAUsB,GAAGJ,KAAKK,KAAKF,MACrC5B,EAAO2B,EAAMD,GACTqD,UACAC,eACAC,aAGRvC,KAAM,WACFd,KAAKsD,qBACLtD,KAAK8C,OAAO9C,KAAKmD,SAErBtC,KAAM,WACF,MAAOjC,GAAIoB,KAAKqD,OAAOE,GAAKnE,GAA+BR,EAAIoB,KAAKqD,OAAOG,GAAKpE,GAEpF6B,MAAO,WACHjB,KAAK8C,OAAO9C,KAAKoD,aACbpD,KAAKyD,UACLzD,KAAKyD,SAASvD,QAGtBwD,eAAgB,SAAUC,EAAMC,GAC5B5D,KAAKqD,UACLrD,KAAKmD,OAASQ,EACd3D,KAAKoD,YAAcQ,GAEvBC,YAAa,SAAUJ,GACfA,GAAY5F,EAAMiG,WAAWL,GAC7BzD,KAAKyD,SAAWA,EAEhBA,EAAW7F,GAGnB0F,mBAAoB,WAChBtD,KAAKqD,QACDG,GAAIxD,KAAKoD,YAAYI,EAAIxD,KAAKmD,OAAOK,GAAK,EAC1CD,GAAIvD,KAAKoD,YAAYG,EAAIvD,KAAKmD,OAAOI,GAAK,GAE9CvD,KAAKmD,QACDI,EAAGvD,KAAKmD,OAAOI,EAAIvD,KAAKqD,OAAOE,EAC/BC,EAAGxD,KAAKmD,OAAOK,EAAIxD,KAAKqD,OAAOG,MAIvCO,EAAYzF,EAAMF,QAClByB,KAAM,SAAUC,GACZ,GAAIC,GAAOC,KAAMgE,EAA8B,MAAjBlE,EAAQuB,KAAc4C,EAAUtG,EAAE,sCAAwCqG,EAAa,aAAe,YAAc,iBAClJ5F,GAAO2B,EAAMD,GACTmE,QAASA,EACTC,YAAa,EACbzD,QAAS,GAAIlC,GAAQ0F,GACrBE,cAAerE,EAAQW,QACvB2D,cAAetE,EAAQsE,cACvBC,KAAML,EAAa,QAAU,WAEjCjE,EAAKoE,cAAc/D,KAAKZ,EAAQrB,EAAM4B,EAAKuE,QAASvE,IACpDA,EAAKwE,UAAUC,OAAOP,GAClBnE,EAAQsE,eACRrE,EAAK0E,QAGbH,QAAS,WACL,GAAIvE,GAAOC,KAAMqB,EAAOtB,EAAKsB,KAAMW,EAAYjC,EAAKiC,UAAW0C,EAAW1C,EAAUqC,KAAMF,EAAgBpE,EAAKoE,cAAeQ,EAAYD,EAAW1C,EAAU4C,MAAOC,EAAWhG,KAAKiG,OAAOX,EAAc9C,GAAQsD,GAAYN,EAAOxF,KAAKiG,MAAMJ,EAAWC,EACxPA,IAAa,EACb3E,KAAKiE,QAAQc,IAAI,UAAW,QAE5B/E,KAAKiE,QAAQc,IAAI,UAAW,IAE5BF,EAAWR,EAAOK,EAClBL,EAAOK,EAAWG,EACXA,EAAW,IAClBR,GAAQQ,EACRA,EAAW,GAEX9E,EAAKmE,aAAeG,IACpBtE,EAAKkE,QAAQc,IAAIhF,EAAKsE,KAAMA,EAAO,MACnCtE,EAAKmE,YAAcG,GAEvBtE,EAAKU,QAAQiC,SAASrB,EAAMwD,IAEhCJ,KAAM,WACFzE,KAAKiE,QAAQc,KACTC,QAASjG,EACTkG,WAAY,aAGpBC,KAAM,WACGlF,KAAKoE,eACNpE,KAAKiE,QAAQc,KAAMC,QAAS,OAIpCG,EAAW9G,EAAOD,QAClByB,KAAM,SAAUoE,EAASnE,GAAnB,GAcEsF,GAAkC9E,EAA4CG,EAA8BE,EAIxG0E,EAA8ClF,EAiB9CmF,EAKAC,EAKAC,EA5CJzF,EAAOC,IAIX,OAHA3B,GAAO4B,GAAGJ,KAAKK,KAAKH,EAAMkE,EAASnE,GACnCmE,EAAUlE,EAAKkE,SACflE,EAAK0F,QAAU1F,EAAKD,QAAQ4F,WAAa7H,EAAM8H,QAAQC,qBAEnD3B,EAAQ4B,SAAS,sBAAsBC,QAAQ,mCAC/C1H,EAAO2B,GACHgG,cAAe9B,EACf+B,eAAgB/B,EAAQgC,WAAWC,UAEvC,IAEJjC,EAAQc,IAAI,WAAY,UAAUc,SAAS,qBAAqBM,UAAU,sCAAsCL,QAAQ,mCACpHV,EAAQnB,EAAQgC,WAAWG,GAAG,GAAI9F,EAAa,GAAIzC,GAAMwI,WAAWpC,GAAUxD,EAAU,GAAIlC,GAAQ6G,GAAQzE,EAAa,GAAIlC,IACzHwF,QAASmB,EACTb,UAAWN,EACXqC,cAAevG,EAAKD,QAAQyG,OAC5BlB,EAAiBrF,KAAKF,QAAQuF,eAAgBlF,EAAa,GAAItC,GAAM2I,WAAWvC,GAChFwC,YAAa,QACbC,SAAS,EACTC,gBAAgB,EAChBC,kBAAkB,EAClBC,kBAAkB,EAClBC,WAAY/G,EAAKD,QAAQyG,KACzBQ,iBAAkBhH,EAAKD,QAAQiH,iBAC/B1G,MAAO,SAAU0B,GACbpB,EAAW2D,SACX,IAAI0C,GAAYpI,EAAImD,EAAEyB,EAAE1B,UAAWmF,EAAYrI,EAAImD,EAAEwB,EAAEzB,UAAWoF,EAA8B,EAAZF,GAAiBC,EAAWE,EAA+BxJ,EAAEyJ,SAASrH,EAAKiG,eAAe,GAAIjE,EAAEsF,MAAMC,QAASC,EAA4B,EAAZN,GAAiBD,GAC/NG,IAAiC9B,EAAetD,IAAMhC,EAAKS,UAAYG,EAAW6C,EAAEhD,SAAW0G,GAAmBvG,EAAW4C,EAAE/C,SAAW+G,GAC3IpH,EAAWqH,UAEXrH,EAAWI,YAGnB+E,EAAO,GAAI9G,IACXiC,QAASA,EACTE,WAAYA,EACZR,WAAYA,EACZsC,QAAS1C,EAAKD,QAAQ2C,UACtB8C,EAAe,GAAI3F,IACnBa,QAASA,EACTE,WAAYA,EACZR,WAAYA,EACZG,WAAYA,IACZkF,EAAmB,GAAItC,IACvBJ,OAAQ,SAAU2E,GACd1H,EAAK2H,SAASD,EAAYjE,EAAGiE,EAAYlE,MAGrD9C,EAAQL,KAAKZ,EAAQ,WACjBO,EAAK4H,WAAalH,EAAQ8C,EAC1BxD,EAAK6H,YAAcnH,EAAQ+C,EAC3BzD,EAAK8H,QAAQnI,GACTiI,UAAW5H,EAAK4H,UAChBC,WAAY7H,EAAK6H,eAGrB7H,EAAKD,QAAQgI,qBACb7D,EAAQ8D,GAAG,4BAA6B5J,EAAM6B,KAAM,iBAExD5B,EAAO2B,GACHU,QAASA,EACTE,WAAYA,EACZ4E,aAAcA,EACdC,iBAAkBA,EAClBrF,WAAYA,EACZmF,KAAMA,EACNhF,WAAYA,EACZ0H,QAAQ,EACRxH,SAAS,EACTuF,cAAeX,EACfuC,UAAW,EACXC,WAAY,EACZ5B,eAAgB/B,EAAQgC,WAAWC,UAEvCnG,EAAKkI,UAAU,KACflI,EAAKkI,UAAU,KACflI,EAAKmI,UAAY,WACbnI,EAAKoI,QAAS,EACdpI,EAAKI,WAAWyC,IAAI,EAAG7C,EAAKqI,UAEhCzH,EAAW2D,UACPvE,EAAKD,QAAQuI,eACbtI,EAAKuI,qBAvETrE,IA0EJsE,aAAc,SAAUxG,GACf/B,KAAKmI,SACNnI,KAAKmI,QAAS,EACdnI,KAAKoI,QAAU,EACfpI,KAAKG,WAAWqI,MAAM,EAAGxI,KAAKoI,UAElCK,aAAazI,KAAK0I,eAClB1I,KAAK0I,cAAgBC,WAAW3I,KAAKkI,UAAW,GAChD,IAAI1F,GAAQ3E,EAAM+K,YAAY7G,EAC1BS,KACAxC,KAAKoI,SAAW5F,EAChBxC,KAAKG,WAAW0I,KAAK,EAAG7I,KAAKoI,UAEjCrG,EAAE+G,kBAENC,YAAa,WACT/I,KAAKW,WAAW4C,EAAEwF,eAEtBC,YAAa,SAAU5G,EAAKD,GACxBnC,KAAKW,WAAW4C,EAAEyF,YAAY5G,EAAKD,IAEvC8G,OAAQ,WACJ,MAAOjJ,MAAKW,WAAW4C,EAAEc,MAE7B6E,aAAc,WACV,MAAOlJ,MAAK+F,cAAc,GAAGmD,cAEjCC,YAAa,WACT,MAAOnJ,MAAK+F,cAAc,GAAGoD,aAEjCrJ,SACIsJ,KAAM,WACN7C,MAAM,EACN8C,WAAY,IACZC,oBAAoB,EACpB7G,SAAS,EACTiD,WAAW,EACXoC,qBAAqB,EACrBzC,eAAgB,WACZ,OAAO,GAEXgD,eAAe,EACfkB,UACIC,aAAc,kBACdC,gBAAiB,qBACjBC,gBAAiB,eAGzBC,QACIpK,EACAG,EACAD,GAEJmK,QAAS,WACA5J,KAAKyF,SACNzF,KAAK6J,kBAGbC,WAAY,SAAUhK,GAClB,GAAIC,GAAOC,IACX3B,GAAO4B,GAAG6J,WAAW5J,KAAKH,EAAMD,GAC5BA,EAAQuI,eACRtI,EAAKuI,sBAGbyB,MAAO,WACC/J,KAAKyF,QACLzF,KAAK+F,cAAc4B,UAAU,IAE7B3H,KAAKS,QAAQqC,QACTU,EAAG,EACHD,EAAG,IAEPvD,KAAKgK,OAAO,KAGpBH,eAAgB,WACZ7J,KAAKW,WAAW2D,UACZtE,KAAKsF,KAAK9B,EAAE5B,eACZ5B,KAAKS,QAAQiC,SAAS,IAAK1C,KAAKW,WAAW6C,EAAEpB,KAE7CpC,KAAKsF,KAAK/B,EAAE3B,eACZ5B,KAAKS,QAAQiC,SAAS,IAAK1C,KAAKW,WAAW4C,EAAEnB,MAGrD6H,QAAS,WACL,GAAItJ,GAAaX,KAAKW,UACtBA,GAAW2D,UACXtE,KAAKgK,OAAOrJ,EAAWuJ,UACvBlK,KAAKS,QAAQqC,OAAOnC,EAAWwJ,sBAEnCC,OAAQ,WACJpK,KAAKQ,SAAU,GAEnB6J,QAAS,WACLrK,KAAKQ,SAAU,GAEnBkH,SAAU,SAAUlE,EAAGD,GACfvD,KAAKyF,SACLzF,KAAK+F,cAAc6B,WAAWhJ,EAAI4E,IAClCxD,KAAK+F,cAAc4B,UAAU/I,EAAI2E,MAEjCvD,KAAKW,WAAW2D,UAChBtE,KAAKS,QAAQqC,QACTU,EAAGA,EACHD,EAAGA,MAIf+G,iBAAkB,SAAU9G,EAAGD,EAAGE,GAC9B,GAAIE,GAAMC,CACN5D,MAAKyF,QACLzF,KAAK0H,SAASlE,EAAGD,IAEjBI,GACIH,EAAGxD,KAAKS,QAAQ+C,EAChBD,EAAGvD,KAAKS,QAAQ8C,GAEpBK,GACIJ,EAAGA,EACHD,EAAGA,GAEPvD,KAAKwF,iBAAiB9B,eAAeC,EAAMC,GAC3C5D,KAAKwF,iBAAiB3B,YAAYJ,GAClCzD,KAAKwF,iBAAiBnF,UAG9BkK,YAAa,WACT,GAAIxK,GAAOC,IACXD,GAAKyK,YAAYC,YAAYnL,GAC7BS,EAAK2K,cAAcC,KAAK5K,EAAKyJ,kBAC7BzJ,EAAK6K,SAAS3J,QACdlB,EAAK8K,SAAS5J,QACdlB,EAAKI,WAAWI,UAEpBuK,QAAS,WACLzM,EAAO4B,GAAG6K,QAAQ5K,KAAKF,MACnBA,KAAKG,YACLH,KAAKG,WAAW2K,WAGxBd,OAAQ,SAAUtJ,GACdV,KAAKW,WAAWK,QAAQN,GACxBV,KAAKS,QAAQS,QAAQR,IAEzB4H,mBAAoB,WAChB,GAAIvI,GAAOC,IACXD,GAAKY,WAAW4C,EAAEwH,eAClBhL,EAAKyJ,aAAe3L,EAAMmN,SAASjL,EAAKD,QAAQyJ,SAASC,cACzDzJ,EAAK0J,gBAAkB5L,EAAMmN,SAASjL,EAAKD,QAAQyJ,SAASE,iBAC5D1J,EAAK2J,gBAAkB7L,EAAMmN,SAASjL,EAAKD,QAAQyJ,SAASG,iBAC5D3J,EAAKgG,cAAcD,QAAQ,oKAAsK/F,EAAKyJ,iBAAmB,kBACzNzJ,EAAKyK,YAAczK,EAAKgG,cAAcE,WAAWC,QACjDnG,EAAK2K,cAAgB3K,EAAKyK,YAAYvE,SAAS,gBAC/ClG,EAAKuF,KAAK/B,EAAEnD,KAAK,SAAUjC,EAAM4B,EAAKkL,YAAalL,IACnDA,EAAKI,WAAWC,KAAK,MAAOjC,EAAM4B,EAAKmL,SAAUnL,KAErDmL,SAAU,WACN,GAAInL,GAAOC,IACND,GAAKiI,SAGVjI,EAAKiI,QAAS,EACdjI,EAAKyK,YAAYC,YAAYpL,GAAcwG,SAASvG,GACpDS,EAAK2K,cAAcC,KAAK5K,EAAK2J,qBAC7B3J,EAAK6K,SAASpJ,OAAOzB,EAAKD,QAAQuJ,WAAa,GAC/CtJ,EAAK8H,QAAQ,UAEjBoD,YAAa,WACT,GAAIlL,GAAOC,IACPD,GAAKU,QAAQ8C,EAAIpE,EAAyBY,EAAKD,QAAQuJ,WAClDtJ,EAAKiI,SACNjI,EAAKiI,QAAS,EACdjI,EAAKyK,YAAYC,YAAYnL,GAAcuG,SAASxG,GACpDU,EAAK2K,cAAcC,KAAK5K,EAAK0J,sBAE1B1J,EAAKiI,SACZjI,EAAKiI,QAAS,EACdjI,EAAKyK,YAAYC,YAAYpL,GAC7BU,EAAK2K,cAAcC,KAAK5K,EAAKyJ,oBAGrCvB,UAAW,SAAU5G,GACjB,GAAItB,GAAOC,KAAMS,EAAUV,EAAKU,QAASuB,EAAYjC,EAAKY,WAAWU,GAAOf,EAAaP,EAAKO,WAAYqB,EAAW5B,EAAKuF,KAAKjE,GAAO8J,EAAY,GAAIpH,IAC9I1C,KAAMA,EACNZ,QAASA,EACTuB,UAAWA,EACXuC,UAAWxE,EAAKkE,QAChBG,cAAerE,EAAKD,QAAQwJ,oBAEpCtH,GAAU5B,KAAKZ,EAAQ,WACnB2L,EAAU7G,YAEd3C,EAASvB,KAAKZ,EAAQ,WAClB2L,EAAU1G,SAEd1E,EAAKsB,EAAO,WAAa,GAAIF,IACzBE,KAAMA,EACNM,SAAUA,EACVlB,QAASA,EACTH,WAAYA,EACZH,WAAYJ,EAAKI,WACjB6B,UAAWA,EACXS,QAAS1C,EAAKD,QAAQ2C,QACtBF,SAAUxC,EAAKD,QAAQyC,UAAYvD,EACnCqD,mBAAoBtC,EAAKD,QAAQuC,oBAAsBpD,EACvD2D,IAAK,WACDuI,EAAUjG,OACVnF,EAAK8H,QAAQ,aACTxG,KAAMA,EACNsG,UAAW5H,EAAK4H,UAChBC,WAAY7H,EAAK6H,kBAMrC1J,GAAGkN,OAAOjG,IACZrH,OAAOD,MAAMwN,QACRvN,OAAOD,OACE,kBAAVH,SAAwBA,OAAO4N,IAAM5N,OAAS,SAAU6N,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.scroller.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.scroller', [\n        'kendo.fx',\n        'kendo.draganddrop'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.scroller',\n        name: 'Scroller',\n        category: 'mobile',\n        description: 'The Kendo Mobile Scroller widget enables touch friendly kinetic scrolling for the contents of a given DOM element.',\n        depends: [\n            'fx',\n            'draganddrop'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, mobile = kendo.mobile, fx = kendo.effects, ui = mobile.ui, proxy = $.proxy, extend = $.extend, Widget = ui.Widget, Class = kendo.Class, Movable = kendo.ui.Movable, Pane = kendo.ui.Pane, PaneDimensions = kendo.ui.PaneDimensions, Transition = fx.Transition, Animation = fx.Animation, abs = Math.abs, SNAPBACK_DURATION = 500, SCROLLBAR_OPACITY = 0.7, FRICTION = 0.96, VELOCITY_MULTIPLIER = 10, MAX_VELOCITY = 55, OUT_OF_BOUNDS_FRICTION = 0.5, ANIMATED_SCROLLER_PRECISION = 5, RELEASECLASS = 'km-scroller-release', REFRESHCLASS = 'km-scroller-refresh', PULL = 'pull', CHANGE = 'change', RESIZE = 'resize', SCROLL = 'scroll', MOUSE_WHEEL_ID = 2;\n        var ZoomSnapBack = Animation.extend({\n            init: function (options) {\n                var that = this;\n                Animation.fn.init.call(that);\n                extend(that, options);\n                that.userEvents.bind('gestureend', proxy(that.start, that));\n                that.tapCapture.bind('press', proxy(that.cancel, that));\n            },\n            enabled: function () {\n                return this.movable.scale < this.dimensions.minScale;\n            },\n            done: function () {\n                return this.dimensions.minScale - this.movable.scale < 0.01;\n            },\n            tick: function () {\n                var movable = this.movable;\n                movable.scaleWith(1.1);\n                this.dimensions.rescale(movable.scale);\n            },\n            onEnd: function () {\n                var movable = this.movable;\n                movable.scaleTo(this.dimensions.minScale);\n                this.dimensions.rescale(movable.scale);\n            }\n        });\n        var DragInertia = Animation.extend({\n            init: function (options) {\n                var that = this;\n                Animation.fn.init.call(that);\n                extend(that, options, {\n                    transition: new Transition({\n                        axis: options.axis,\n                        movable: options.movable,\n                        onEnd: function () {\n                            that._end();\n                        }\n                    })\n                });\n                that.tapCapture.bind('press', function () {\n                    that.cancel();\n                });\n                that.userEvents.bind('end', proxy(that.start, that));\n                that.userEvents.bind('gestureend', proxy(that.start, that));\n                that.userEvents.bind('tap', proxy(that.onEnd, that));\n            },\n            onCancel: function () {\n                this.transition.cancel();\n            },\n            freeze: function (location) {\n                var that = this;\n                that.cancel();\n                that._moveTo(location);\n            },\n            onEnd: function () {\n                var that = this;\n                if (that.paneAxis.outOfBounds()) {\n                    that._snapBack();\n                } else {\n                    that._end();\n                }\n            },\n            done: function () {\n                return abs(this.velocity) < 1;\n            },\n            start: function (e) {\n                var that = this, velocity;\n                if (!that.dimension.enabled) {\n                    return;\n                }\n                if (that.paneAxis.outOfBounds()) {\n                    that._snapBack();\n                } else {\n                    velocity = e.touch.id === MOUSE_WHEEL_ID ? 0 : e.touch[that.axis].velocity;\n                    that.velocity = Math.max(Math.min(velocity * that.velocityMultiplier, MAX_VELOCITY), -MAX_VELOCITY);\n                    that.tapCapture.captureNext();\n                    Animation.fn.start.call(that);\n                }\n            },\n            tick: function () {\n                var that = this, dimension = that.dimension, friction = that.paneAxis.outOfBounds() ? OUT_OF_BOUNDS_FRICTION : that.friction, delta = that.velocity *= friction, location = that.movable[that.axis] + delta;\n                if (!that.elastic && dimension.outOfBounds(location)) {\n                    location = Math.max(Math.min(location, dimension.max), dimension.min);\n                    that.velocity = 0;\n                }\n                that.movable.moveAxis(that.axis, location);\n            },\n            _end: function () {\n                this.tapCapture.cancelCapture();\n                this.end();\n            },\n            _snapBack: function () {\n                var that = this, dimension = that.dimension, snapBack = that.movable[that.axis] > dimension.max ? dimension.max : dimension.min;\n                that._moveTo(snapBack);\n            },\n            _moveTo: function (location) {\n                this.transition.moveTo({\n                    location: location,\n                    duration: SNAPBACK_DURATION,\n                    ease: Transition.easeOutExpo\n                });\n            }\n        });\n        var AnimatedScroller = Animation.extend({\n            init: function (options) {\n                var that = this;\n                kendo.effects.Animation.fn.init.call(this);\n                extend(that, options, {\n                    origin: {},\n                    destination: {},\n                    offset: {}\n                });\n            },\n            tick: function () {\n                this._updateCoordinates();\n                this.moveTo(this.origin);\n            },\n            done: function () {\n                return abs(this.offset.y) < ANIMATED_SCROLLER_PRECISION && abs(this.offset.x) < ANIMATED_SCROLLER_PRECISION;\n            },\n            onEnd: function () {\n                this.moveTo(this.destination);\n                if (this.callback) {\n                    this.callback.call();\n                }\n            },\n            setCoordinates: function (from, to) {\n                this.offset = {};\n                this.origin = from;\n                this.destination = to;\n            },\n            setCallback: function (callback) {\n                if (callback && kendo.isFunction(callback)) {\n                    this.callback = callback;\n                } else {\n                    callback = undefined;\n                }\n            },\n            _updateCoordinates: function () {\n                this.offset = {\n                    x: (this.destination.x - this.origin.x) / 4,\n                    y: (this.destination.y - this.origin.y) / 4\n                };\n                this.origin = {\n                    y: this.origin.y + this.offset.y,\n                    x: this.origin.x + this.offset.x\n                };\n            }\n        });\n        var ScrollBar = Class.extend({\n            init: function (options) {\n                var that = this, horizontal = options.axis === 'x', element = $('<div class=\"km-touch-scrollbar km-' + (horizontal ? 'horizontal' : 'vertical') + '-scrollbar\" />');\n                extend(that, options, {\n                    element: element,\n                    elementSize: 0,\n                    movable: new Movable(element),\n                    scrollMovable: options.movable,\n                    alwaysVisible: options.alwaysVisible,\n                    size: horizontal ? 'width' : 'height'\n                });\n                that.scrollMovable.bind(CHANGE, proxy(that.refresh, that));\n                that.container.append(element);\n                if (options.alwaysVisible) {\n                    that.show();\n                }\n            },\n            refresh: function () {\n                var that = this, axis = that.axis, dimension = that.dimension, paneSize = dimension.size, scrollMovable = that.scrollMovable, sizeRatio = paneSize / dimension.total, position = Math.round(-scrollMovable[axis] * sizeRatio), size = Math.round(paneSize * sizeRatio);\n                if (sizeRatio >= 1) {\n                    this.element.css('display', 'none');\n                } else {\n                    this.element.css('display', '');\n                }\n                if (position + size > paneSize) {\n                    size = paneSize - position;\n                } else if (position < 0) {\n                    size += position;\n                    position = 0;\n                }\n                if (that.elementSize != size) {\n                    that.element.css(that.size, size + 'px');\n                    that.elementSize = size;\n                }\n                that.movable.moveAxis(axis, position);\n            },\n            show: function () {\n                this.element.css({\n                    opacity: SCROLLBAR_OPACITY,\n                    visibility: 'visible'\n                });\n            },\n            hide: function () {\n                if (!this.alwaysVisible) {\n                    this.element.css({ opacity: 0 });\n                }\n            }\n        });\n        var Scroller = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                element = that.element;\n                that._native = that.options.useNative && kendo.support.hasNativeScrolling;\n                if (that._native) {\n                    element.addClass('km-native-scroller').prepend('<div class=\"km-scroll-header\"/>');\n                    extend(that, {\n                        scrollElement: element,\n                        fixedContainer: element.children().first()\n                    });\n                    return;\n                }\n                element.css('overflow', 'hidden').addClass('km-scroll-wrapper').wrapInner('<div class=\"km-scroll-container\"/>').prepend('<div class=\"km-scroll-header\"/>');\n                var inner = element.children().eq(1), tapCapture = new kendo.TapCapture(element), movable = new Movable(inner), dimensions = new PaneDimensions({\n                        element: inner,\n                        container: element,\n                        forcedEnabled: that.options.zoom\n                    }), avoidScrolling = this.options.avoidScrolling, userEvents = new kendo.UserEvents(element, {\n                        touchAction: 'pan-y',\n                        fastTap: true,\n                        allowSelection: true,\n                        preventDragEvent: true,\n                        captureUpIfMoved: true,\n                        multiTouch: that.options.zoom,\n                        supportDoubleTap: that.options.supportDoubleTap,\n                        start: function (e) {\n                            dimensions.refresh();\n                            var velocityX = abs(e.x.velocity), velocityY = abs(e.y.velocity), horizontalSwipe = velocityX * 2 >= velocityY, originatedFromFixedContainer = $.contains(that.fixedContainer[0], e.event.target), verticalSwipe = velocityY * 2 >= velocityX;\n                            if (!originatedFromFixedContainer && !avoidScrolling(e) && that.enabled && (dimensions.x.enabled && horizontalSwipe || dimensions.y.enabled && verticalSwipe)) {\n                                userEvents.capture();\n                            } else {\n                                userEvents.cancel();\n                            }\n                        }\n                    }), pane = new Pane({\n                        movable: movable,\n                        dimensions: dimensions,\n                        userEvents: userEvents,\n                        elastic: that.options.elastic\n                    }), zoomSnapBack = new ZoomSnapBack({\n                        movable: movable,\n                        dimensions: dimensions,\n                        userEvents: userEvents,\n                        tapCapture: tapCapture\n                    }), animatedScroller = new AnimatedScroller({\n                        moveTo: function (coordinates) {\n                            that.scrollTo(coordinates.x, coordinates.y);\n                        }\n                    });\n                movable.bind(CHANGE, function () {\n                    that.scrollTop = -movable.y;\n                    that.scrollLeft = -movable.x;\n                    that.trigger(SCROLL, {\n                        scrollTop: that.scrollTop,\n                        scrollLeft: that.scrollLeft\n                    });\n                });\n                if (that.options.mousewheelScrolling) {\n                    element.on('DOMMouseScroll mousewheel', proxy(this, '_wheelScroll'));\n                }\n                extend(that, {\n                    movable: movable,\n                    dimensions: dimensions,\n                    zoomSnapBack: zoomSnapBack,\n                    animatedScroller: animatedScroller,\n                    userEvents: userEvents,\n                    pane: pane,\n                    tapCapture: tapCapture,\n                    pulled: false,\n                    enabled: true,\n                    scrollElement: inner,\n                    scrollTop: 0,\n                    scrollLeft: 0,\n                    fixedContainer: element.children().first()\n                });\n                that._initAxis('x');\n                that._initAxis('y');\n                that._wheelEnd = function () {\n                    that._wheel = false;\n                    that.userEvents.end(0, that._wheelY);\n                };\n                dimensions.refresh();\n                if (that.options.pullToRefresh) {\n                    that._initPullToRefresh();\n                }\n            },\n            _wheelScroll: function (e) {\n                if (!this._wheel) {\n                    this._wheel = true;\n                    this._wheelY = 0;\n                    this.userEvents.press(0, this._wheelY);\n                }\n                clearTimeout(this._wheelTimeout);\n                this._wheelTimeout = setTimeout(this._wheelEnd, 50);\n                var delta = kendo.wheelDeltaY(e);\n                if (delta) {\n                    this._wheelY += delta;\n                    this.userEvents.move(0, this._wheelY);\n                }\n                e.preventDefault();\n            },\n            makeVirtual: function () {\n                this.dimensions.y.makeVirtual();\n            },\n            virtualSize: function (min, max) {\n                this.dimensions.y.virtualSize(min, max);\n            },\n            height: function () {\n                return this.dimensions.y.size;\n            },\n            scrollHeight: function () {\n                return this.scrollElement[0].scrollHeight;\n            },\n            scrollWidth: function () {\n                return this.scrollElement[0].scrollWidth;\n            },\n            options: {\n                name: 'Scroller',\n                zoom: false,\n                pullOffset: 140,\n                visibleScrollHints: false,\n                elastic: true,\n                useNative: false,\n                mousewheelScrolling: true,\n                avoidScrolling: function () {\n                    return false;\n                },\n                pullToRefresh: false,\n                messages: {\n                    pullTemplate: 'Pull to refresh',\n                    releaseTemplate: 'Release to refresh',\n                    refreshTemplate: 'Refreshing'\n                }\n            },\n            events: [\n                PULL,\n                SCROLL,\n                RESIZE\n            ],\n            _resize: function () {\n                if (!this._native) {\n                    this.contentResized();\n                }\n            },\n            setOptions: function (options) {\n                var that = this;\n                Widget.fn.setOptions.call(that, options);\n                if (options.pullToRefresh) {\n                    that._initPullToRefresh();\n                }\n            },\n            reset: function () {\n                if (this._native) {\n                    this.scrollElement.scrollTop(0);\n                } else {\n                    this.movable.moveTo({\n                        x: 0,\n                        y: 0\n                    });\n                    this._scale(1);\n                }\n            },\n            contentResized: function () {\n                this.dimensions.refresh();\n                if (this.pane.x.outOfBounds()) {\n                    this.movable.moveAxis('x', this.dimensions.x.min);\n                }\n                if (this.pane.y.outOfBounds()) {\n                    this.movable.moveAxis('y', this.dimensions.y.min);\n                }\n            },\n            zoomOut: function () {\n                var dimensions = this.dimensions;\n                dimensions.refresh();\n                this._scale(dimensions.fitScale);\n                this.movable.moveTo(dimensions.centerCoordinates());\n            },\n            enable: function () {\n                this.enabled = true;\n            },\n            disable: function () {\n                this.enabled = false;\n            },\n            scrollTo: function (x, y) {\n                if (this._native) {\n                    this.scrollElement.scrollLeft(abs(x));\n                    this.scrollElement.scrollTop(abs(y));\n                } else {\n                    this.dimensions.refresh();\n                    this.movable.moveTo({\n                        x: x,\n                        y: y\n                    });\n                }\n            },\n            animatedScrollTo: function (x, y, callback) {\n                var from, to;\n                if (this._native) {\n                    this.scrollTo(x, y);\n                } else {\n                    from = {\n                        x: this.movable.x,\n                        y: this.movable.y\n                    };\n                    to = {\n                        x: x,\n                        y: y\n                    };\n                    this.animatedScroller.setCoordinates(from, to);\n                    this.animatedScroller.setCallback(callback);\n                    this.animatedScroller.start();\n                }\n            },\n            pullHandled: function () {\n                var that = this;\n                that.refreshHint.removeClass(REFRESHCLASS);\n                that.hintContainer.html(that.pullTemplate({}));\n                that.yinertia.onEnd();\n                that.xinertia.onEnd();\n                that.userEvents.cancel();\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                if (this.userEvents) {\n                    this.userEvents.destroy();\n                }\n            },\n            _scale: function (scale) {\n                this.dimensions.rescale(scale);\n                this.movable.scaleTo(scale);\n            },\n            _initPullToRefresh: function () {\n                var that = this;\n                that.dimensions.y.forceEnabled();\n                that.pullTemplate = kendo.template(that.options.messages.pullTemplate);\n                that.releaseTemplate = kendo.template(that.options.messages.releaseTemplate);\n                that.refreshTemplate = kendo.template(that.options.messages.refreshTemplate);\n                that.scrollElement.prepend('<span class=\"km-scroller-pull\"><span class=\"km-icon\"></span><span class=\"km-loading-left\"></span><span class=\"km-loading-right\"></span><span class=\"km-template\">' + that.pullTemplate({}) + '</span></span>');\n                that.refreshHint = that.scrollElement.children().first();\n                that.hintContainer = that.refreshHint.children('.km-template');\n                that.pane.y.bind('change', proxy(that._paneChange, that));\n                that.userEvents.bind('end', proxy(that._dragEnd, that));\n            },\n            _dragEnd: function () {\n                var that = this;\n                if (!that.pulled) {\n                    return;\n                }\n                that.pulled = false;\n                that.refreshHint.removeClass(RELEASECLASS).addClass(REFRESHCLASS);\n                that.hintContainer.html(that.refreshTemplate({}));\n                that.yinertia.freeze(that.options.pullOffset / 2);\n                that.trigger('pull');\n            },\n            _paneChange: function () {\n                var that = this;\n                if (that.movable.y / OUT_OF_BOUNDS_FRICTION > that.options.pullOffset) {\n                    if (!that.pulled) {\n                        that.pulled = true;\n                        that.refreshHint.removeClass(REFRESHCLASS).addClass(RELEASECLASS);\n                        that.hintContainer.html(that.releaseTemplate({}));\n                    }\n                } else if (that.pulled) {\n                    that.pulled = false;\n                    that.refreshHint.removeClass(RELEASECLASS);\n                    that.hintContainer.html(that.pullTemplate({}));\n                }\n            },\n            _initAxis: function (axis) {\n                var that = this, movable = that.movable, dimension = that.dimensions[axis], tapCapture = that.tapCapture, paneAxis = that.pane[axis], scrollBar = new ScrollBar({\n                        axis: axis,\n                        movable: movable,\n                        dimension: dimension,\n                        container: that.element,\n                        alwaysVisible: that.options.visibleScrollHints\n                    });\n                dimension.bind(CHANGE, function () {\n                    scrollBar.refresh();\n                });\n                paneAxis.bind(CHANGE, function () {\n                    scrollBar.show();\n                });\n                that[axis + 'inertia'] = new DragInertia({\n                    axis: axis,\n                    paneAxis: paneAxis,\n                    movable: movable,\n                    tapCapture: tapCapture,\n                    userEvents: that.userEvents,\n                    dimension: dimension,\n                    elastic: that.options.elastic,\n                    friction: that.options.friction || FRICTION,\n                    velocityMultiplier: that.options.velocityMultiplier || VELOCITY_MULTIPLIER,\n                    end: function () {\n                        scrollBar.hide();\n                        that.trigger('scrollEnd', {\n                            axis: axis,\n                            scrollTop: that.scrollTop,\n                            scrollLeft: that.scrollLeft\n                        });\n                    }\n                });\n            }\n        });\n        ui.plugin(Scroller);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}