{"version": 3, "sources": ["kendo.mobile.button.js"], "names": ["f", "define", "$", "undefined", "highlightButton", "widget", "event", "highlight", "target", "closest", "toggleClass", "ANDROID3UP", "deactivateTimeoutID", "clearTimeout", "createBadge", "value", "kendo", "window", "mobile", "ui", "Widget", "support", "os", "mobileOS", "android", "flatVersion", "CLICK", "DISABLED", "DISABLEDSTATE", "<PERSON><PERSON>", "extend", "init", "element", "options", "useTap", "that", "this", "fn", "call", "clickOn", "_wrap", "_style", "attr", "enable", "_userEvents", "UserEvents", "allowSelection", "fastTap", "press", "e", "_activate", "release", "stopPropagation", "bind", "_release", "on", "_timeoutDeactivate", "destroy", "events", "name", "icon", "style", "badge", "badgeElement", "appendTo", "html", "empty", "remove", "removeAttr", "setTimeout", "activeElement", "document", "nodeName", "blur", "which", "trigger", "button", "preventDefault", "styles", "split", "each", "addClass", "iconSpan", "span", "children", "image", "find", "wrapInner", "iconElement", "prepend", "BackButton", "DetailButton", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,uBAAwB,oBAAqBD,IACtD,WAgME,MAxLC,UAAUE,EAAGC,GAEV,QAASC,GAAgBC,EAAQC,EAAOC,GACpCL,EAAEI,EAAME,QAAQC,QAAQ,yBAAyBC,YAAY,kBAAmBH,GAC5EI,GAAcN,EAAOO,sBACrBC,aAAaR,EAAOO,qBACpBP,EAAOO,oBAAsB,GAGrC,QAASE,GAAYC,GACjB,MAAOb,GAAE,0BAA4Ba,EAAQ,WAVpD,GACOC,GAAQC,OAAOD,MAAOE,EAASF,EAAME,OAAQC,EAAKD,EAAOC,GAAIC,EAASD,EAAGC,OAAQC,EAAUL,EAAMK,QAASC,EAAKD,EAAQE,SAAUZ,EAAaW,EAAGE,SAAWF,EAAGG,aAAe,IAAKC,EAAQ,QAASC,EAAW,WAAYC,EAAgB,oBAW3OC,EAAST,EAAOU,QAChBC,KAAM,SAAUC,EAASC,GAAnB,GAGEC,GAFAC,EAAOC,IACXhB,GAAOiB,GAAGN,KAAKO,KAAKH,EAAMH,EAASC,GAC/BC,EAAkC,OAAzBC,EAAKF,QAAQM,QAC1BJ,EAAKK,QACLL,EAAKM,SACAP,GACDC,EAAKH,QAAQU,KAAK,0BAA0B,GAEhDP,EAAKF,QAAQU,OAASR,EAAKF,QAAQU,SAAWR,EAAKH,QAAQU,KAAKf,GAChEQ,EAAKQ,OAAOR,EAAKF,QAAQU,QACzBR,EAAKS,YAAc,GAAI5B,GAAM6B,WAAWV,EAAKH,SACzCc,gBAAiBZ,EACjBa,SAAS,EACTC,MAAO,SAAUC,GACbd,EAAKe,UAAUD,IAEnBE,QAAS,SAAUF,GACf7C,EAAgB+B,EAAMc,GAAG,GACpBf,GACDe,EAAE3C,MAAM8C,qBAIpBjB,EAAKS,YAAYS,KAAKnB,EAAS,MAAQ,QAAS,SAAUe,GACtDd,EAAKmB,SAASL,KAEdtC,GACAwB,EAAKH,QAAQuB,GAAG,OAAQ,SAAUN,GAC9Bd,EAAKqB,mBAAmBP,MAIpCQ,QAAS,WACLrC,EAAOiB,GAAGoB,QAAQnB,KAAKF,MACvBA,KAAKQ,YAAYa,WAErBC,QAAShC,GACTO,SACI0B,KAAM,SACNC,KAAM,GACNC,MAAO,GACPC,MAAO,GACPvB,QAAS,KACTI,QAAQ,GAEZmB,MAAO,SAAU/C,GACb,GAAI+C,GAAQ1B,KAAK2B,aAAe3B,KAAK2B,cAAgBjD,EAAYC,GAAOiD,SAAS5B,KAAKJ,QACtF,OAAIjB,IAAmB,IAAVA,GACT+C,EAAMG,KAAKlD,GACJqB,MAEPrB,KAAU,GACV+C,EAAMI,QAAQC,SACd/B,KAAK2B,cAAe,EACb3B,MAEJ0B,EAAMG,QAEjBtB,OAAQ,SAAUA,GACd,GAAIX,GAAUI,KAAKJ,OACE,KAAVW,IACPA,GAAS,GAEbP,KAAKH,QAAQU,OAASA,EAClBA,EACAX,EAAQoC,WAAWzC,GAEnBK,EAAQU,KAAKf,EAAUA,GAE3BK,EAAQtB,YAAYkB,GAAgBe,IAExCa,mBAAoB,SAAUP,GACrBb,KAAKxB,sBACNwB,KAAKxB,oBAAsByD,WAAWjE,EAAiB,IAAKgC,KAAMa,GAAG,KAG7EC,UAAW,SAAUD,GACjB,GAAIqB,GAAgBC,SAASD,cAAeE,EAAWF,EAAgBA,EAAcE,SAAW,EAC5FpC,MAAKH,QAAQU,SACbvC,EAAgBgC,KAAMa,GAAG,GACT,SAAZuB,GAAmC,YAAZA,GACvBF,EAAcG,SAI1BnB,SAAU,SAAUL,GAChB,GAAId,GAAOC,IACX,MAAIa,EAAEyB,MAAQ,GAGd,MAAKvC,GAAKF,QAAQU,QAIdR,EAAKwC,QAAQjD,GACTlB,OAAQN,EAAE+C,EAAEzC,QACZoE,OAAQzC,EAAKH,WAEjBiB,EAAE4B,iBAJN,IAHI5B,EAAE4B,iBACF,IASRpC,OAAQ,WACJ,GAAwDqC,GAApDjB,EAAQzB,KAAKH,QAAQ4B,MAAO7B,EAAUI,KAAKJ,OAC3C6B,KACAiB,EAASjB,EAAMkB,MAAM,KACrB7E,EAAE8E,KAAKF,EAAQ,WACX9C,EAAQiD,SAAS,MAAQ7C,UAIrCI,MAAO,WACH,GAAIL,GAAOC,KAAMwB,EAAOzB,EAAKF,QAAQ2B,KAAME,EAAQ3B,EAAKF,QAAQ6B,MAAOoB,EAAW,2BAA6BtB,EAAM5B,EAAUG,EAAKH,QAAQiD,SAAS,aAAcE,EAAOnD,EAAQoD,SAAS,sBAAsBH,SAAS,WAAYI,EAAQrD,EAAQsD,KAAK,OAAOL,SAAS,aACtQE,EAAK,IAAMnD,EAAQiC,SACpBkB,EAAOnD,EAAQuD,UAAU,4BAA4BH,SAAS,kBAE7DC,EAAM,IAAMzB,IACRuB,EAAK,KACND,GAAY,cAEhB/C,EAAKqD,YAAcxD,EAAQyD,QAAQvF,EAAEgF,EAAW,WAEhDpB,GAAmB,IAAVA,KACT3B,EAAK4B,aAAejD,EAAYgD,GAAOE,SAAShC,OAIxD0D,EAAa7D,EAAOC,QACpBG,SACI0B,KAAM,aACNE,MAAO,QAEX9B,KAAM,SAAUC,EAASC,GACrB,GAAIE,GAAOC,IACXP,GAAOQ,GAAGN,KAAKO,KAAKH,EAAMH,EAASC,GACM,IAA9BE,EAAKH,QAAQU,KAAK,SACzBP,EAAKH,QAAQU,KAAK,OAAQ,aAIlCiD,EAAe9D,EAAOC,QACtBG,SACI0B,KAAM,eACNE,MAAO,IAEX9B,KAAM,SAAUC,EAASC,GACrBJ,EAAOQ,GAAGN,KAAKO,KAAKF,KAAMJ,EAASC,IAEvCQ,OAAQ,WAAA,GAGIqC,GAFJjB,EAAQzB,KAAKH,QAAQ4B,MAAQ,UAAW7B,EAAUI,KAAKJ,OACvD6B,KACIiB,EAASjB,EAAMkB,MAAM,KACzB7E,EAAE8E,KAAKF,EAAQ,WACX9C,EAAQiD,SAAS,MAAQ7C,UAIrCI,MAAO,WACH,GAAIL,GAAOC,KAAMwB,EAAOzB,EAAKF,QAAQ2B,KAAMsB,EAAW,2BAA6BtB,EAAM5B,EAAUG,EAAKH,QAASmD,EAAOnD,EAAQoD,SAAS,QAASC,EAAQrD,EAAQsD,KAAK,OAAOL,SAAS,aAClLI,EAAM,IAAMzB,IACRuB,EAAK,KACND,GAAY,cAEhBlD,EAAQyD,QAAQvF,EAAEgF,EAAW,YAIzC/D,GAAGyE,OAAO/D,GACVV,EAAGyE,OAAOF,GACVvE,EAAGyE,OAAOD,IACZ1E,OAAOD,MAAM6E,QACR5E,OAAOD,OACE,kBAAVf,SAAwBA,OAAO6F,IAAM7F,OAAS,SAAU8F,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.button.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.button', ['kendo.userevents'], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.button',\n        name: 'Button',\n        category: 'mobile',\n        description: 'The Button widget navigates between mobile Application views when pressed.',\n        depends: ['userevents']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, mobile = kendo.mobile, ui = mobile.ui, Widget = ui.Widget, support = kendo.support, os = support.mobileOS, ANDROID3UP = os.android && os.flatVersion >= 300, CLICK = 'click', DISABLED = 'disabled', DISABLEDSTATE = 'km-state-disabled';\n        function highlightButton(widget, event, highlight) {\n            $(event.target).closest('.km-button,.km-detail').toggleClass('km-state-active', highlight);\n            if (ANDROID3UP && widget.deactivateTimeoutID) {\n                clearTimeout(widget.deactivateTimeoutID);\n                widget.deactivateTimeoutID = 0;\n            }\n        }\n        function createBadge(value) {\n            return $('<span class=\"km-badge\">' + value + '</span>');\n        }\n        var Button = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                var useTap = that.options.clickOn === 'up';\n                that._wrap();\n                that._style();\n                if (!useTap) {\n                    that.element.attr('data-navigate-on-press', true);\n                }\n                that.options.enable = that.options.enable && !that.element.attr(DISABLED);\n                that.enable(that.options.enable);\n                that._userEvents = new kendo.UserEvents(that.element, {\n                    allowSelection: !useTap,\n                    fastTap: true,\n                    press: function (e) {\n                        that._activate(e);\n                    },\n                    release: function (e) {\n                        highlightButton(that, e, false);\n                        if (!useTap) {\n                            e.event.stopPropagation();\n                        }\n                    }\n                });\n                that._userEvents.bind(useTap ? 'tap' : 'press', function (e) {\n                    that._release(e);\n                });\n                if (ANDROID3UP) {\n                    that.element.on('move', function (e) {\n                        that._timeoutDeactivate(e);\n                    });\n                }\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this._userEvents.destroy();\n            },\n            events: [CLICK],\n            options: {\n                name: 'Button',\n                icon: '',\n                style: '',\n                badge: '',\n                clickOn: 'up',\n                enable: true\n            },\n            badge: function (value) {\n                var badge = this.badgeElement = this.badgeElement || createBadge(value).appendTo(this.element);\n                if (value || value === 0) {\n                    badge.html(value);\n                    return this;\n                }\n                if (value === false) {\n                    badge.empty().remove();\n                    this.badgeElement = false;\n                    return this;\n                }\n                return badge.html();\n            },\n            enable: function (enable) {\n                var element = this.element;\n                if (typeof enable == 'undefined') {\n                    enable = true;\n                }\n                this.options.enable = enable;\n                if (enable) {\n                    element.removeAttr(DISABLED);\n                } else {\n                    element.attr(DISABLED, DISABLED);\n                }\n                element.toggleClass(DISABLEDSTATE, !enable);\n            },\n            _timeoutDeactivate: function (e) {\n                if (!this.deactivateTimeoutID) {\n                    this.deactivateTimeoutID = setTimeout(highlightButton, 500, this, e, false);\n                }\n            },\n            _activate: function (e) {\n                var activeElement = document.activeElement, nodeName = activeElement ? activeElement.nodeName : '';\n                if (this.options.enable) {\n                    highlightButton(this, e, true);\n                    if (nodeName == 'INPUT' || nodeName == 'TEXTAREA') {\n                        activeElement.blur();\n                    }\n                }\n            },\n            _release: function (e) {\n                var that = this;\n                if (e.which > 1) {\n                    return;\n                }\n                if (!that.options.enable) {\n                    e.preventDefault();\n                    return;\n                }\n                if (that.trigger(CLICK, {\n                        target: $(e.target),\n                        button: that.element\n                    })) {\n                    e.preventDefault();\n                }\n            },\n            _style: function () {\n                var style = this.options.style, element = this.element, styles;\n                if (style) {\n                    styles = style.split(' ');\n                    $.each(styles, function () {\n                        element.addClass('km-' + this);\n                    });\n                }\n            },\n            _wrap: function () {\n                var that = this, icon = that.options.icon, badge = that.options.badge, iconSpan = '<span class=\"km-icon km-' + icon, element = that.element.addClass('km-button'), span = element.children('span:not(.km-icon)').addClass('km-text'), image = element.find('img').addClass('km-image');\n                if (!span[0] && element.html()) {\n                    span = element.wrapInner('<span class=\"km-text\" />').children('span.km-text');\n                }\n                if (!image[0] && icon) {\n                    if (!span[0]) {\n                        iconSpan += ' km-notext';\n                    }\n                    that.iconElement = element.prepend($(iconSpan + '\" />'));\n                }\n                if (badge || badge === 0) {\n                    that.badgeElement = createBadge(badge).appendTo(element);\n                }\n            }\n        });\n        var BackButton = Button.extend({\n            options: {\n                name: 'BackButton',\n                style: 'back'\n            },\n            init: function (element, options) {\n                var that = this;\n                Button.fn.init.call(that, element, options);\n                if (typeof that.element.attr('href') === 'undefined') {\n                    that.element.attr('href', '#:back');\n                }\n            }\n        });\n        var DetailButton = Button.extend({\n            options: {\n                name: 'DetailButton',\n                style: ''\n            },\n            init: function (element, options) {\n                Button.fn.init.call(this, element, options);\n            },\n            _style: function () {\n                var style = this.options.style + ' detail', element = this.element;\n                if (style) {\n                    var styles = style.split(' ');\n                    $.each(styles, function () {\n                        element.addClass('km-' + this);\n                    });\n                }\n            },\n            _wrap: function () {\n                var that = this, icon = that.options.icon, iconSpan = '<span class=\"km-icon km-' + icon, element = that.element, span = element.children('span'), image = element.find('img').addClass('km-image');\n                if (!image[0] && icon) {\n                    if (!span[0]) {\n                        iconSpan += ' km-notext';\n                    }\n                    element.prepend($(iconSpan + '\" />'));\n                }\n            }\n        });\n        ui.plugin(Button);\n        ui.plugin(BackButton);\n        ui.plugin(DetailButton);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}