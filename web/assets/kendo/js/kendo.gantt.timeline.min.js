/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.gantt.timeline.min",["kendo.dom.min","kendo.touch.min","kendo.draganddrop.min"],e)}(function(){return function(e){function t(e){return delete e.name,delete e.prefix,delete e.views,e}function s(e){var t=[],s=e.workWeekStart;for(t.push(s);e.workWeekEnd!=s;)s>6?s-=7:s++,t.push(s);return t}function n(){var t=kendo._activeElement();t&&"body"!==t.nodeName.toLowerCase()&&e(t).blur()}var i,a,r=kendo.ui.Widget,o=kendo.dom.element,d=kendo.dom.text,l=kendo.dom.html,h=e.isPlainObject,p=kendo._outerWidth,c=kendo._outerHeight,u=e.extend,k=e.proxy,g=kendo.support.browser,f=!1,m=kendo.keys,_=kendo.data.Query,y="string",v=".kendoGanttTimeline",w="click",T="dblclick",D="mousemove",b="mouseenter",H="mouseleave",W="keydown",M=".",z=kendo.template("#=kendo.toString(start, 't')#"),x=kendo.template("#=kendo.toString(start, 'ddd M/dd')#"),S=kendo.template("#=kendo.toString(start, 'ddd M/dd')# - #=kendo.toString(kendo.date.addDays(end, -1), 'ddd M/dd')#"),C=kendo.template("#=kendo.toString(start, 'MMM')#"),P=kendo.template("#=kendo.toString(start, 'yyyy')#"),I=kendo.template('<div class="#=styles.marquee#"><div class="#=styles.marqueeColor#"></div></div>'),N=kendo.template('<div style="z-index: 100002;" class="#=styles.tooltipWrapper#"><div class="#=styles.tooltipContent#"><div>#=messages.start#: #=kendo.toString(start, format)#</div><div>#=messages.end#: #=kendo.toString(end, format)#</div></div></div>'),R=kendo.template('<div style="z-index: 100002;" class="#=styles.tooltipWrapper#" ><div class="#=styles.tooltipContent#">#=text#%</div><div class="#=styles.tooltipCallout#" style="left:13px;"></div></div>'),V=kendo.template('<div class="#=kendo.htmlEncode(styles.taskDetails)#"><strong>#=kendo.htmlEncode(task.title)#</strong><div class="#=styles.taskDetailsPercent#">#=kendo.toString(task.percentComplete, "p0")#</div><ul class="#=styles.reset#"><li>#=messages.start#: #=kendo.toString(task.start, "h:mm tt ddd, MMM d")#</li><li>#=messages.end#: #=kendo.toString(task.end, "h:mm tt ddd, MMM d")#</li></ul></div>'),E="<table style='visibility: hidden;'><tbody><tr style='height:{0}'><td>&nbsp;</td></tr></tbody></table>",F={day:{type:"kendo.ui.GanttDayView"},week:{type:"kendo.ui.GanttWeekView"},month:{type:"kendo.ui.GanttMonthView"},year:{type:"kendo.ui.GanttYearView"}},L={alt:"k-alt",reset:"k-reset",nonWorking:"k-nonwork-hour",header:"k-header",gridHeader:"k-grid-header",gridHeaderWrap:"k-grid-header-wrap",gridContent:"k-grid-content",tasksWrapper:"k-gantt-tables",rowsTable:"k-gantt-rows",columnsTable:"k-gantt-columns",tasksTable:"k-gantt-tasks",dependenciesWrapper:"k-gantt-dependencies",resource:"k-resource",resourceAlt:"k-resource k-alt",task:"k-task",taskSingle:"k-task-single",taskMilestone:"k-task-milestone",taskSummary:"k-task-summary",taskWrap:"k-task-wrap",taskMilestoneWrap:"k-milestone-wrap",resourcesWrap:"k-resources-wrap",taskDot:"k-task-dot",taskDotStart:"k-task-start",taskDotEnd:"k-task-end",taskDragHandle:"k-task-draghandle",taskContent:"k-task-content",taskTemplate:"k-task-template",taskActions:"k-task-actions",taskDelete:"k-task-delete",taskComplete:"k-task-complete",taskDetails:"k-task-details",taskDetailsPercent:"k-task-pct",link:"k-link",icon:"k-icon",iconDelete:"k-i-close",taskResizeHandle:"k-resize-handle",taskResizeHandleWest:"k-resize-w",taskResizeHandleEast:"k-resize-e",taskSummaryProgress:"k-task-summary-progress",taskSummaryComplete:"k-task-summary-complete",line:"k-line",lineHorizontal:"k-line-h",lineVertical:"k-line-v",arrowWest:"k-arrow-w",arrowEast:"k-arrow-e",dragHint:"k-drag-hint",dependencyHint:"k-dependency-hint",tooltipWrapper:"k-widget k-tooltip k-popup k-group k-reset",tooltipContent:"k-tooltip-content",tooltipCallout:"k-callout k-callout-s",callout:"k-callout",marquee:"k-marquee k-gantt-marquee",marqueeColor:"k-marquee-color"},B=kendo.ui.GanttView=r.extend({init:function(e,t){r.fn.init.call(this,e,t),this.title=this.options.title||this.options.name,this.header=this.element.find(M+B.styles.gridHeader),this.content=this.element.find(M+B.styles.gridContent),this.contentWidth=this.content.width(),this._workDays=s(this.options),this._headerTree=t.headerTree,this._taskTree=t.taskTree,this._taskTemplate=t.taskTemplate?kendo.template(t.taskTemplate,u({},kendo.Template,t.templateSettings)):null,this._dependencyTree=t.dependencyTree,this._taskCoordinates={},this._currentTime()},destroy:function(){r.fn.destroy.call(this),clearTimeout(this._tooltipTimeout),this.headerRow=null,this.header=null,this.content=null,this._dragHint=null,this._resizeHint=null,this._resizeTooltip=null,this._taskTooltip=null,this._percentCompleteResizeTooltip=null,this._headerTree=null,this._taskTree=null,this._dependencyTree=null},options:{showWorkHours:!1,showWorkDays:!1,workDayStart:new Date(1980,1,1,8,0,0),workDayEnd:new Date(1980,1,1,17,0,0),workWeekStart:1,workWeekEnd:5,hourSpan:1,slotSize:100,currentTimeMarker:{updateInterval:1e4}},renderLayout:function(){this._slots=this._createSlots(),this._tableWidth=this._calculateTableWidth(),this.createLayout(this._layout()),this._slotDimensions(),this._adjustHeight(),this.content.find(M+B.styles.dependenciesWrapper).width(this._tableWidth)},_adjustHeight:function(){this.content&&this.content.height(this.element.height()-c(this.header))},createLayout:function(e){var t=this._headers(e),s=this._colgroup(),n=this._headerTree,i=o("thead",null,t),a=o("table",{style:{width:this._tableWidth+"px"}},[s,i]);n.render([a]),this.headerRow=this.header.find("table:first tr").last()},_slotDimensions:function(){var e,t,s,n,i=this.headerRow[0].children,a=this._timeSlots();for(s=0,n=i.length;s<n;s++)t=i[s],e=a[s],e.offsetLeft=t.offsetLeft,e.offsetWidth=t.offsetWidth},render:function(e){var t,s,n=e.length,i=B.styles,a=this._rowsTable(n),r=this._columnsTable(n),o=this._tasksTable(e),d=this.options.currentTimeMarker,l=this.options.calculatedSize;this._taskTree.render([a,r,o]),t=this.content.find(M+i.rowsTable),l&&(s=l.row*e.length,this.content.find(M+i.tasksTable).height(s),t.height(s)),this._contentHeight=t.height(),this._rowHeight=l?l.row:this._contentHeight/t.find("tr").length,this.content.find(M+i.columnsTable).height(this._contentHeight),d!==!1&&void 0!==d.updateInterval&&this._renderCurrentTime()},_rowsTable:function(e){var t,s,n=[],i=B.styles,a=[null,{className:i.alt}];for(s=0;s<e;s++)t=o("tr",a[s%2],[o("td",null,[d(" ")])]),n.push(t);return this._createTable(1,n,{className:i.rowsTable})},_columnsTable:function(){var e,t,s,n,i,a=[],r=B.styles,l=this._timeSlots(),h=l.length,p=0;for(i=0;i<h;i++)t=l[i],n={},s=t.span,p+=s,1!==s&&(n.colspan=s),t.isNonWorking&&(n.className=r.nonWorking),a.push(o("td",n,[d(" ")]));return e=o("tr",null,a),this._createTable(p,[e],{className:r.columnsTable})},_tasksTable:function(e){var t,s,n,i,a,r,d,l,h=[],p=B.styles,c=this._taskCoordinates={},u=this._calculateMilestoneWidth(),k=Math.round(u.width),g=this.options.resourcesField,m=[p.resource,p.resourceAlt],_=this.options.calculatedSize,y=this._calculateResourcesMargin(),v=this._calculateTaskBorderWidth(),w=function(e){var t=n.left,s=t+n.width;i.isMilestone()&&(t-=k/2,s=t+k),c[i.id]={start:t,end:s,rowIndex:e}};for(d=0,l=e.length;d<l;d++)i=e[d],n=this._taskPosition(i),n.borderWidth=v,t=o("tr",null),s=o("td"),i.start<=this.end&&i.end>=this.start&&(s.children.push(this._renderTask(e[d],n)),i[g]&&i[g].length&&(a=f?this._tableWidth-n.left:Math.max(n.width||u.clientWidth,0)+n.left,r={width:this._tableWidth-(a+y)+"px"},r[f?"right":"left"]=a+"px",_&&(r.height=_.cell+"px"),s.children.push(o("div",{className:p.resourcesWrap,style:r},this._renderResources(i[g],m[d%2])))),w(d)),t.children.push(s),h.push(t);return this._createTable(1,h,{className:B.styles.tasksTable})},_createTable:function(e,t,s){var n,i,a,r=[];for(a=0;a<e;a++)r.push(o("col"));return n=o("colgroup",null,r),i=o("tbody",null,t),s.style||(s.style={}),s.style.width=this._tableWidth+"px",o("table",s,[n,i])},_calculateTableWidth:function(){var e,t,s,n,i=this._timeSlots(),a=0,r=0;for(s=0,n=i.length;s<n;s++)e=i[s].span,r+=e,e>a&&(a=e);return t=Math.round(r*this.options.slotSize/a)},_calculateMilestoneWidth:function(){var t,s,n=B.styles.task+" "+B.styles.taskMilestone,i=e("<div class='"+n+"' style='visibility: hidden; position: absolute'>");return this.content.append(i),s=i[0].getBoundingClientRect(),t={width:s.right-s.left,clientWidth:i[0].clientWidth},i.remove(),t},_calculateResourcesMargin:function(){var t,s=e("<div class='"+B.styles.resourcesWrap+"' style='visibility: hidden; position: absolute'>");return this.content.append(s),t=parseInt(s.css(f?"margin-right":"margin-left"),10),s.remove(),t},_calculateTaskBorderWidth:function(){var t,s,n=B.styles.task+" "+B.styles.taskSingle,i=e("<div class='"+n+"' style='visibility: hidden; position: absolute'>");return this.content.append(i),s=kendo.getComputedStyles(i[0],["border-left-width"]),t=parseFloat(s["border-left-width"],10),i.remove(),t},_renderTask:function(e,t){var s,n,i,a=this.options.editable,r=t.left,d=B.styles,l=d.taskWrap,h=this.options.calculatedSize,p={},c={className:l,style:{left:r+"px"}};return h&&(c.style.height=h.cell+"px"),e.summary?n=this._renderSummary(e,t):e.isMilestone()?(n=this._renderMilestone(e,t),c.className+=" "+d.taskMilestoneWrap):n=this._renderSingleTask(e,t),s=o("div",c,[n]),a&&a.dependencyCreate!==!1&&(s.children.push(o("div",{className:d.taskDot+" "+d.taskDotStart})),s.children.push(o("div",{className:d.taskDot+" "+d.taskDotEnd}))),e.summary||e.isMilestone()||!a||a.dragPercentComplete===!1||a.update===!1||null!==this._taskTemplate||(i=Math.round(t.width*e.percentComplete),p[f?"right":"left"]=i+"px",s.children.push(o("div",{className:d.taskDragHandle,style:p}))),s},_renderSingleTask:function(e,t){var s,n,i,a=B.styles,r=Math.round(t.width*e.percentComplete),h=[],p=this.options.editable;return null!==this._taskTemplate?s=l(this._taskTemplate(e)):(s=d(e.title),h.push(o("div",{className:a.taskComplete,style:{width:r+"px"}}))),n=o("div",{className:a.taskContent},[o("div",{className:a.taskTemplate},[s])]),h.push(n),p&&(p.destroy!==!1&&n.children.push(o("span",{className:a.taskActions},[o("a",{className:a.link+" "+a.taskDelete,href:"#","aria-label":"Delete"},[o("span",{className:a.icon+" "+a.iconDelete})])])),p.resize!==!1&&p.update!==!1&&(n.children.push(o("span",{className:a.taskResizeHandle+" "+a.taskResizeHandleWest})),n.children.push(o("span",{className:a.taskResizeHandle+" "+a.taskResizeHandleEast})))),i=o("div",{className:a.task+" "+a.taskSingle,"data-uid":e.uid,style:{width:Math.max(t.width-2*t.borderWidth,0)+"px"}},h)},_renderMilestone:function(e){var t=B.styles,s=o("div",{className:t.task+" "+t.taskMilestone,"data-uid":e.uid});return s},_renderSummary:function(e,t){var s=B.styles,n=Math.round(t.width*e.percentComplete),i=o("div",{className:s.task+" "+s.taskSummary,"data-uid":e.uid,style:{width:t.width+"px"}},[o("div",{className:s.taskSummaryProgress,style:{width:n+"px"}},[o("div",{className:s.taskSummaryComplete,style:{width:t.width+"px"}})])]);return i},_renderResources:function(e,t){var s,n,i,a=[];for(n=0,i=e.length;n<i;n++)s=e[n],a.push(o("span",{className:t,style:{color:s.get("color")}},[d(s.get("name"))]));return f&&a.reverse(),a},_taskPosition:function(e){var t=Math.round,s=t(this._offset(f?e.end:e.start)),n=t(this._offset(f?e.start:e.end));return{left:s,width:n-s}},_offset:function(e){var t,s,n,i,a=this._timeSlots(),r=0;return a.length?(i=this._slotIndex("start",e),t=a[i],t.end<e?r=t.offsetWidth:t.start<=e&&(s=e-t.start,n=t.end-t.start,r=s/n*t.offsetWidth),f&&(r=t.offsetWidth+1-r),t.offsetLeft+r):0},_slotIndex:function(e,t,s){var n,i=this._timeSlots(),a=0,r=i.length-1;s&&(i=[].slice.call(i).reverse());do n=Math.ceil((r+a)/2),i[n][e]<t?a=n:(n===r&&n--,r=n);while(a!==r);return s&&(a=i.length-1-a),a},_timeByPosition:function(t,s,n){var i,a,r,o=this._slotByPosition(t);return s?n?o.end:o.start:(i=t-e(M+B.styles.tasksTable).offset().left,a=o.end-o.start,r=i-o.offsetLeft,f&&(r=o.offsetWidth-r),new Date(o.start.getTime()+a*(r/o.offsetWidth)))},_slotByPosition:function(t){var s=t-e(M+B.styles.tasksTable).offset().left,n=this._slotIndex("offsetLeft",s,f);return this._timeSlots()[n]},_renderDependencies:function(e){var t,s,n=[],i=this._dependencyTree;for(t=0,s=e.length;t<s;t++)n.push.apply(n,this._renderDependency(e[t]));i.render(n)},_renderDependency:function(e){var t,s,n,i,a=this._taskCoordinates[e.predecessorId],r=this._taskCoordinates[e.successorId];if(!a||!r)return[];for(s="_render"+["FF","FS","SF","SS"][f?3-e.type:e.type],t=this[s](a,r),n=0,i=t.length;n<i;n++)t[n].attr["data-uid"]=e.uid;return t},_renderFF:function(e,t){var s=this._dependencyFF(e,t,!1);return s[s.length-1].children[0]=this._arrow(!0),s},_renderSS:function(e,t){var s=this._dependencyFF(t,e,!0);return s[0].children[0]=this._arrow(!1),s.reverse()},_renderFS:function(e,t){var s=this._dependencyFS(e,t,!1);return s[s.length-1].children[0]=this._arrow(!1),s},_renderSF:function(e,t){var s=this._dependencyFS(t,e,!0);return s[0].children[0]=this._arrow(!0),s.reverse()},_dependencyFF:function(e,t,s){var n,i=this,a=[],r=0,o=0,d=0,l=0,h=s?"start":"end",p=2,c=1,u=this._rowHeight,k=10,g=e.rowIndex*u+Math.floor(u/2)-1,f=t.rowIndex*u+Math.floor(u/2)-1,m=B.styles,_=function(){a.push(i._line(m.line+" "+m.lineHorizontal,{left:r+"px",top:o+"px",width:d+"px"}))},y=function(){a.push(i._line(m.line+" "+m.lineVertical,{left:r+"px",top:o+"px",height:l+"px"}))};return r=e[h],o=g,d=k,n=t[h]-e[h],n>0!==s&&(d=Math.abs(n)+k),s?(r-=d,d-=c,_()):(_(),r+=d-p),f<o?(l=o-f,l+=p,o=f,y()):(l=f-o,l+=p,y(),o+=l-p),d=Math.abs(r-t[h]),s||(d-=c,r-=d),_(),a},_dependencyFS:function(e,t,s){var n=this,i=[],a=0,r=0,o=0,d=0,l=this._rowHeight,h=Math.floor(l/2),p=10,c=2*p,u=t.start-e.end,k=2,g=1,f=e.rowIndex*l+Math.floor(l/2)-1,m=t.rowIndex*l+Math.floor(l/2)-1,_=B.styles,y=function(){i.push(n._line(_.line+" "+_.lineHorizontal,{left:a+"px",top:r+"px",width:o+"px"}))},v=function(){i.push(n._line(_.line+" "+_.lineVertical,{left:a+"px",top:r+"px",height:d+"px"}))};return a=e.end,r=f,o=p,s&&(a+=g,u>c&&(o=u-(p-k)),o-=g),y(),a+=o-k,u<=c&&(d=s?Math.abs(m-f)-h:h,m<f?(r-=d,d+=k,v()):(v(),r+=d),o=e.end-t.start+c,o<p&&(o=p),a-=o-k,y()),m<f?(d=r-m,r=m,d+=k,v()):(d=m-r,v(),r+=d),o=t.start-a,s||(o-=g),y(),i},_line:function(e,t){return o("div",{className:e,style:t})},_arrow:function(e){return o("span",{className:e?B.styles.arrowWest:B.styles.arrowEast})},_colgroup:function(){var e,t,s,n=this._timeSlots(),i=n.length,a=[];for(e=0;e<i;e++)for(t=0,s=n[e].span;t<s;t++)a.push(o("col"));return o("colgroup",null,a)},_createDragHint:function(e){this._dragHint=e.clone().addClass(B.styles.dragHint).css("cursor","move"),e.parent().append(this._dragHint)},_updateDragHint:function(e){var t=this._offset(e);this._dragHint.css({left:t})},_removeDragHint:function(){this._dragHint.remove(),this._dragHint=null},_createResizeHint:function(t){var s,n,i=B.styles,a=this._taskCoordinates[t.id].rowIndex*this._rowHeight,r=this.options,o=r.messages;this._resizeHint=e(I({styles:i})).css({top:0,height:this._contentHeight}),this.content.append(this._resizeHint),this._resizeTooltip=e(N({styles:i,start:t.start,end:t.end,messages:o.views,format:r.resizeTooltipFormat})).css({top:0,left:0}),this.content.append(this._resizeTooltip),this._resizeTooltipWidth=p(this._resizeTooltip),s=c(this._resizeTooltip),n=a-s,n<0&&(n=a+this._rowHeight),this._resizeTooltipTop=n},_updateResizeHint:function(t,s,n){var i=this._offset(f?s:t),a=this._offset(f?t:s),r=a-i,o=n!==f?i:a,d=this._tableWidth-kendo.support.scrollbar(),l=this._resizeTooltipWidth,h=this.options,p=h.messages,c=e(M+B.styles.tasksTable).offset().left-e(M+B.styles.tasksWrapper).offset().left;f&&(i+=c),this._resizeHint.css({left:i,width:r}),this._resizeTooltip&&this._resizeTooltip.remove(),o-=Math.round(l/2),o<0?o=0:o+l>d&&(o=d-l),f&&(o+=c),this._resizeTooltip=e(N({styles:B.styles,start:t,end:s,messages:p.views,format:h.resizeTooltipFormat})).css({top:this._resizeTooltipTop,left:o,"min-width":l}).appendTo(this.content)},_removeResizeHint:function(){this._resizeHint.remove(),this._resizeHint=null,this._resizeTooltip.remove(),this._resizeTooltip=null},_updatePercentCompleteTooltip:function(t,s,n){var i,a,r,o;this._removePercentCompleteTooltip(),i=this._percentCompleteResizeTooltip=e(R({styles:B.styles,text:n})).appendTo(this.element),a=Math.round(p(i)/2),r=i.find(M+B.styles.callout),o=Math.round(p(r)/2),i.css({top:t-(c(i)+o),left:s-a}),r.css("left",a-o)},_removePercentCompleteTooltip:function(){this._percentCompleteResizeTooltip&&this._percentCompleteResizeTooltip.remove(),this._percentCompleteResizeTooltip=null},_updateDependencyDragHint:function(e,t,s){this._removeDependencyDragHint(),s?this._creteVmlDependencyDragHint(e,t):this._creteDependencyDragHint(e,t)},_creteDependencyDragHint:function(t,s){var n=B.styles,i=s.x-t.x,a=s.y-t.y,r=Math.sqrt(i*i+a*a),o=Math.atan(a/i);i<0&&(o+=Math.PI),e("<div class='"+n.line+" "+n.lineHorizontal+" "+n.dependencyHint+"'></div>").css({top:t.y,left:t.x,width:r,"transform-origin":"0% 0","-ms-transform-origin":"0% 0","-webkit-transform-origin":"0% 0",transform:"rotate("+o+"rad)","-ms-transform":"rotate("+o+"rad)","-webkit-transform":"rotate("+o+"rad)"}).appendTo(this.content)},_creteVmlDependencyDragHint:function(t,s){var n=e("<kvml:line class='"+B.styles.dependencyHint+"' style='position:absolute; top: 0px; left: 0px;' strokecolor='black' strokeweight='2px' from='"+t.x+"px,"+t.y+"px' to='"+s.x+"px,"+s.y+"px'></kvml:line>").appendTo(this.content);n[0].outerHTML=n[0].outerHTML},_removeDependencyDragHint:function(){this.content.find(M+B.styles.dependencyHint).remove()},_createTaskTooltip:function(t,s,n){var i,a=B.styles,r=this.options,o=this.content,d=o.offset(),l=o.width(),h=kendo.scrollLeft(o),u=e(s).parents("tr").first(),k=u.offset(),g=r.tooltip&&r.tooltip.template?kendo.template(r.tooltip.template):V,m=f?n-(d.left+h+kendo.support.scrollbar()):n-(d.left-h),_=k.top+c(u)-d.top+o.scrollTop(),y=this._taskTooltip=e('<div style="z-index: 100002;" class="'+a.tooltipWrapper+'" ><div class="'+a.taskContent+'"></div></div>');y.css({left:m,top:_}).appendTo(o).find(M+a.taskContent).append(g({styles:a,task:t,messages:r.messages.views})),c(y)<k.top-d.top&&y.css("top",k.top-d.top-c(y)+o.scrollTop()),i=p(y),i+m-h>l&&(m-=i,m<h&&(m=h+l-(i+17)),y.css("left",m))},_removeTaskTooltip:function(){this._taskTooltip&&this._taskTooltip.remove(),this._taskTooltip=null},_scrollTo:function(e){var t=e.offset().left,s=e.width(),n=t+s,i=e.closest("tr"),a=i.offset().top,r=i.height(),o=a+r,d=this.content,l=d.offset(),h=l.top,p=d.height(),c=h+p,u=l.left,k=d.width(),g=u+k,f=kendo.support.scrollbar();a<h?d.scrollTop(d.scrollTop()+(a-h)):o>c&&d.scrollTop(d.scrollTop()+(o+f-c)),t<u&&s>k&&n<g||n>g&&s<k?d.scrollLeft(d.scrollLeft()+(n+f-g)):(n>g&&s>k&&t>u||t<u&&s<k)&&d.scrollLeft(d.scrollLeft()+(t-u))},_scrollToDate:function(e){var t,s=this.start,n=this.end;e>=s&&e<n&&(t=this._offset(e),kendo.support.isRtl(this.element)&&(t=this._tableWidth-t),kendo.scrollLeft(this.content,t))},_timeSlots:function(){return this._slots&&this._slots.length?this._slots[this._slots.length-1]:[]},_headers:function(e){var t,s,n,i,a,r,d,h,p=[],c=B.styles;for(a=0,r=e.length;a<r;a++){for(t=e[a],s=[],d=0,h=t.length;d<h;d++)n=t[d],i=l(n.text),s.push(o("th",{colspan:n.span,className:c.header+(n.isNonWorking?" "+c.nonWorking:"")},[i]));p.push(o("tr",null,s))}return p},_hours:function(e,t){var s,n,i,a=[],r=this.options,o=r.workDayStart.getHours(),d=r.workDayEnd.getHours(),l=r.hourSpan;for(e=new Date(e),t=new Date(t),r.showWorkHours&&e.setHours(o);e<t;)s=new Date(e),i=s.getHours(),n=i>=o&&i<d,s.setHours(s.getHours()+l),i==s.getHours()&&s.setHours(s.getHours()+2*l),r.showWorkHours&&!n||a.push({start:e,end:s,isNonWorking:!n,span:1}),e=s;return a},_days:function(e,t){var s,n,i=[];for(e=new Date(e),t=new Date(t);e<t;)s=t<kendo.date.nextDay(e)?t:kendo.date.nextDay(e),n=this._isWorkDay(e),this.options.showWorkDays&&!n||i.push({start:e,end:s,isNonWorking:!n,span:1}),e=s;return i},_weeks:function(e,t){var s,n,i,a=[],r=this.calendarInfo().firstDay;for(e=new Date(e),t=new Date(t);e<t;)s=kendo.date.dayOfWeek(kendo.date.addDays(e,1),r,1),s>t&&(s=t),n=this._days(e,s),i=n.length,i>0&&a.push({start:n[0].start,end:n[i-1].end,span:i}),e=s;return a},_months:function(e,t){var s,n,i,a,r=[];for(e=new Date(e),t=new Date(t);e<t;)s=new Date(e),n=kendo.date.firstDayOfMonth(new Date(s.setMonth(s.getMonth()+1))),s=t<n?t:n,i=this._days(e,s),a=i.length,a>0&&r.push({start:i[0].start,end:i[a-1].end,span:a}),e=s;return r},_years:function(e,t){var s,n,i,a=[];for(e=new Date(e),t=new Date(t);e<t;)s=new Date(e),s=kendo.date.firstDayOfMonth(new Date(s.setMonth(12))),s>=t&&(s=t),i=s.getMonth()||12,n=i-e.getMonth(),a.push({start:e,end:s,span:n}),e=s;return a},_slotHeaders:function(e,t){var s,n,i,a=[];for(n=0,i=e.length;n<i;n++)s=e[n],a.push({text:t(s),isNonWorking:!!s.isNonWorking,span:s.span});return a},_isWorkDay:function(e){var t,s,n=e.getDay(),i=this._workDays;for(t=0,s=i.length;t<s;t++)if(i[t]===n)return!0;return!1},calendarInfo:function(){return kendo.getCulture().calendars.standard},_renderCurrentTime:function(){var t,s=this._getCurrentTime(),n=this._offset(s),i=e("<div class='k-current-time'></div>"),a=B.styles,r=e(M+a.tasksWrapper),o=e(M+a.tasksTable);this.content&&this._timeSlots().length&&(this.content.find(".k-current-time").remove(),t=this._timeSlots()[this._slotIndex("start",s)],s<t.start||s>t.end||(r.length&&o.length&&(n+=o.offset().left-r.offset().left),i.css({left:n+"px",top:"0px",width:"1px",height:this._contentHeight+"px"}).appendTo(this.content)))},_getCurrentTime:function(){return new Date},_currentTime:function(){var e=this.options.currentTimeMarker;e!==!1&&void 0!==e.updateInterval&&(this._renderCurrentTime(),this._currentTimeUpdateTimer=setInterval(k(this._renderCurrentTime,this),e.updateInterval))}});u(!0,B,{styles:L}),kendo.ui.GanttDayView=B.extend({name:"day",options:{timeHeaderTemplate:z,dayHeaderTemplate:x,resizeTooltipFormat:"h:mm tt ddd, MMM d"},range:function(e){var t=this.options.range;this.start=kendo.date.getDate(e.start),this.end=kendo.date.getDate(e.end),(kendo.date.getMilliseconds(e.end)>0||this.end.getTime()===this.start.getTime())&&(this.end=kendo.date.addDays(this.end,1)),t&&t.start&&(this.start=kendo.date.getDate(t.start),this.start.setHours(t.start.getHours())),t&&t.end&&(this.end=kendo.date.getDate(t.end),this.end.setHours(t.end.getHours()))},_createSlots:function(){var e,t,s,n,i=[],a=this._days(this.start,this.end),r=[];for(s=0,n=a.length;s<n;s++)e=a[s],t=this._hours(e.start,e.end),e.span=t.length,r.push.apply(r,t);return i.push(a),i.push(r),i},_layout:function(){var e=[],t=this.options;return e.push(this._slotHeaders(this._slots[0],kendo.template(t.dayHeaderTemplate))),e.push(this._slotHeaders(this._slots[1],kendo.template(t.timeHeaderTemplate))),e}}),kendo.ui.GanttWeekView=B.extend({name:"week",options:{dayHeaderTemplate:x,weekHeaderTemplate:S,resizeTooltipFormat:"h:mm tt ddd, MMM d"},range:function(e){var t,s=this.options.range,n=this.calendarInfo(),i=n.firstDay,a=e.end;i===a.getDay()&&a.setDate(a.getDate()+7),this.start=kendo.date.getDate(kendo.date.dayOfWeek(e.start,i,-1)),this.end=kendo.date.getDate(kendo.date.dayOfWeek(a,i,1)),s&&s.start&&(this.start=kendo.date.getDate(s.start)),s&&s.end&&(t=new Date(s.end),this.end=kendo.date.getDate(t)<s.end?kendo.date.getDate(new Date(t.setDate(t.getDate()+1))):kendo.date.getDate(t))},_createSlots:function(){var e=[];return e.push(this._weeks(this.start,this.end)),e.push(this._days(this.start,this.end)),e},_layout:function(){var e=[],t=this.options;return e.push(this._slotHeaders(this._slots[0],kendo.template(t.weekHeaderTemplate))),e.push(this._slotHeaders(this._slots[1],kendo.template(t.dayHeaderTemplate))),e}}),kendo.ui.GanttMonthView=B.extend({name:"month",options:{weekHeaderTemplate:S,monthHeaderTemplate:C,resizeTooltipFormat:"dddd, MMM d, yyyy"},range:function(e){var t,s=this.options.range;this.start=kendo.date.firstDayOfMonth(e.start),this.end=kendo.date.addDays(kendo.date.getDate(kendo.date.lastDayOfMonth(e.end)),1),s&&s.start&&(this.start=kendo.date.getDate(s.start)),s&&s.end&&(t=new Date(s.end),this.end=kendo.date.getDate(t)<s.end?kendo.date.getDate(new Date(t.setDate(t.getDate()+1))):kendo.date.getDate(t))},_createSlots:function(){var e=[];return e.push(this._months(this.start,this.end)),e.push(this._weeks(this.start,this.end)),e},_layout:function(){var e=[],t=this.options;return e.push(this._slotHeaders(this._slots[0],kendo.template(t.monthHeaderTemplate))),e.push(this._slotHeaders(this._slots[1],kendo.template(t.weekHeaderTemplate))),e}}),kendo.ui.GanttYearView=B.extend({name:"year",options:{yearHeaderTemplate:P,monthHeaderTemplate:C,resizeTooltipFormat:"dddd, MMM d, yyyy"},range:function(e){var t,s=this.options.range;this.start=kendo.date.firstDayOfMonth(new Date(e.start.setMonth(0))),this.end=kendo.date.firstDayOfMonth(new Date(e.end.setMonth(12))),s&&s.start&&(this.start=kendo.date.firstDayOfMonth(s.start)),s&&s.end&&(t=kendo.date.firstDayOfMonth(s.end),this.end=kendo.date.getDate(new Date(t.setMonth(t.getMonth()+1))))},_createSlots:function(){var t=[],s=this._months(this.start,this.end);return e(s).each(function(e,t){t.span=1}),t.push(this._years(this.start,this.end)),t.push(s),t},_layout:function(){var e=[],t=this.options;return e.push(this._slotHeaders(this._slots[0],kendo.template(t.yearHeaderTemplate))),e.push(this._slotHeaders(this._slots[1],kendo.template(t.monthHeaderTemplate))),e}}),i={wrapper:"k-timeline k-grid k-widget",gridHeader:"k-grid-header",gridHeaderWrap:"k-grid-header-wrap",gridContent:"k-grid-content",gridContentWrap:"k-grid-content",tasksWrapper:"k-gantt-tables",dependenciesWrapper:"k-gantt-dependencies",task:"k-task",line:"k-line",taskResizeHandle:"k-resize-handle",taskResizeHandleWest:"k-resize-w",taskDragHandle:"k-task-draghandle",taskComplete:"k-task-complete",taskDelete:"k-task-delete",taskWrapActive:"k-task-wrap-active",taskWrap:"k-task-wrap",taskDot:"k-task-dot",taskDotStart:"k-task-start",taskDotEnd:"k-task-end",hovered:"k-state-hover",selected:"k-state-selected",origin:"k-origin"},a=kendo.ui.GanttTimeline=r.extend({init:function(e,t){r.fn.init.call(this,e,t),this.options.views&&this.options.views.length||(this.options.views=["day","week","month"]),f=kendo.support.isRtl(e),this._wrapper(),this._domTrees(),this._views(),this._selectable(),this._draggable(),this._resizable(),this._percentResizeDraggable(),this._createDependencyDraggable(),this._attachEvents(),this._tooltip()},options:{name:"GanttTimeline",messages:{views:{day:"Day",week:"Week",month:"Month",year:"Year",start:"Start",end:"End"}},snap:!0,selectable:!0,editable:!0},destroy:function(){r.fn.destroy.call(this),clearTimeout(this._tooltipTimeout),this._currentTimeUpdateTimer&&clearInterval(this._currentTimeUpdateTimer),this._unbindView(this._selectedView),this._moveDraggable&&this._moveDraggable.destroy(),this._resizeDraggable&&this._resizeDraggable.destroy(),this._percentDraggable&&this._percentDraggable.destroy(),this._dependencyDraggable&&this._dependencyDraggable.destroy(),this.touch&&this.touch.destroy(),this._headerTree=null,this._taskTree=null,this._dependencyTree=null,this.wrapper.off(v),kendo.destroy(this.wrapper)},_wrapper:function(){var t=a.styles,s=this,n=this.options,i=function(){var i,a,r=typeof n.rowHeight===y?n.rowHeight:n.rowHeight+"px",o=e(kendo.format(E,r)),d=s.wrapper.find(M+t.tasksWrapper);return d.append(o),i=c(o.find("tr")),a=o.find("td").height(),o.remove(),{row:i,cell:a}};this.wrapper=this.element.addClass(t.wrapper).append("<div class='"+t.gridHeader+"'><div class='"+t.gridHeaderWrap+"'></div></div>").append("<div class='"+t.gridContentWrap+"'><div class='"+t.tasksWrapper+"'></div><div class='"+t.dependenciesWrapper+"'></div></div>"),n.rowHeight&&(this._calculatedSize=i())},_domTrees:function(){var e=a.styles,t=kendo.dom.Tree,s=this.wrapper;this._headerTree=new t(s.find(M+e.gridHeaderWrap)[0]),this._taskTree=new t(s.find(M+e.tasksWrapper)[0]),this._dependencyTree=new t(s.find(M+e.dependenciesWrapper)[0])},_views:function(){var e,t,s,n,i,a,r,o=this.options.views;for(this.views={},a=0,r=o.length;a<r;a++)e=o[a],t=h(e),t&&e.selectable===!1||(s=t?"string"!=typeof e.type?e.title:e.type:e,n=F[s],n&&(t&&(e.type=n.type),n.title=this.options.messages.views[s]),e=u({title:s},n,t?e:{}),s&&(this.views[s]=e,i&&!e.selected||(i=s)));i&&(this._selectedViewName=i)},view:function(e){return e&&(this._selectView(e),this.trigger("navigate",{view:e,action:"changeView"})),this._selectedView},_selectView:function(e){e&&this.views[e]&&(this._selectedView&&this._unbindView(this._selectedView),this._selectedView=this._initializeView(e),this._selectedViewName=e)},_viewByIndex:function(e){var t,s=this.views;for(t in s){if(!e)return t;e--}},_initializeView:function(e){var s,n,i,a=this.views[e];if(a){if(s=a.type,"string"==typeof s&&(s=kendo.getter(a.type)(window)),!s)throw Error("There is no such view");n={},u(n,this.options.range,a.range),i=a.date||this.options.date,a=new s(this.wrapper,t(u(!0,{headerTree:this._headerTree,taskTree:this._taskTree,dependencyTree:this._dependencyTree,calculatedSize:this._calculatedSize},a,this.options,{date:i,range:n})))}return a},_unbindView:function(e){e&&e.destroy()},_range:function(e){var t,s,n={field:"start",dir:"asc"},i={field:"end",dir:"desc"};return e&&e.length?(t=new _(e).sort(n).toArray()[0].start||new Date,s=new _(e).sort(i).toArray()[0].end||new Date,{start:new Date(t),end:new Date(s)}):{start:new Date,end:new Date}},_render:function(e){var t=this.view(),s=this._range(e),n=t.options.date;this._tasks=e,t.range(s),t.renderLayout(),t.render(e),n&&t._scrollToDate(n)},_renderDependencies:function(e){this.view()._renderDependencies(e)},_taskByUid:function(e){var t,s,n=this._tasks,i=n.length;for(s=0;s<i;s++)if(t=n[s],t.uid===e)return t},_draggable:function(){var e,t,s,i,r=this,o=this.options.snap,d=a.styles,l=this.options.editable,h=function(){r.view()._removeDragHint(),e&&e.css("opacity",1),e=null,t=null,r.dragInProgress=!1};l&&l.move!==!1&&l.update!==!1&&(this._moveDraggable=new kendo.ui.Draggable(this.wrapper,{distance:0,filter:M+d.task,holdToDrag:kendo.support.mobileOS,ignore:M+d.taskResizeHandle}),this._moveDraggable.bind("dragstart",function(n){var a=r.view();return e=n.currentTarget.parent(),t=r._taskByUid(n.currentTarget.attr("data-uid")),r.trigger("moveStart",{task:t})?void n.preventDefault():(s=t.start,i=a._timeByPosition(n.x.location,o)-s,a._createDragHint(e),e.css("opacity",.5),clearTimeout(r._tooltipTimeout),void(r.dragInProgress=!0))}).bind("drag",kendo.throttle(function(e){var n,a,d;r.dragInProgress&&(n=r.view(),a=new Date(n._timeByPosition(e.x.location,o)-i),d=a,r.trigger("move",{task:t,start:a})||(s=a,f&&(d=new Date(s.getTime()+t.duration())),n._updateDragHint(d)))},15)).bind("dragend",function(){r.trigger("moveEnd",{task:t,start:s}),h()}).bind("dragcancel",function(){h()}).userEvents.bind("select",function(){n()}))},_resizable:function(){var e,t,s,i,r,o=this,d=this.options.snap,l=a.styles,h=this.options.editable,p=function(){o.view()._removeResizeHint(),e=null,t=null,o.dragInProgress=!1};h&&h.resize!==!1&&h.update!==!1&&(this._resizeDraggable=new kendo.ui.Draggable(this.wrapper,{distance:0,filter:M+l.taskResizeHandle,holdToDrag:!1}),this._resizeDraggable.bind("dragstart",function(n){return r=n.currentTarget.hasClass(l.taskResizeHandleWest),f&&(r=!r),e=n.currentTarget.closest(M+l.task),t=o._taskByUid(e.attr("data-uid")),o.trigger("resizeStart",{task:t})?void n.preventDefault():(s=t.start,i=t.end,o.view()._createResizeHint(t),clearTimeout(o._tooltipTimeout),void(o.dragInProgress=!0))}).bind("drag",kendo.throttle(function(e){var n,a;o.dragInProgress&&(n=o.view(),a=n._timeByPosition(e.x.location,d,!r),r?s=a<i?a:i:i=a>s?a:s,o.trigger("resize",{task:t,start:s,end:i})||n._updateResizeHint(s,i,r))},15)).bind("dragend",function(){o.trigger("resizeEnd",{task:t,resizeStart:r,start:s,end:i}),p()}).bind("dragcancel",function(){p()}).userEvents.bind("select",function(){n()}))},_percentResizeDraggable:function(){
var e,t,s,i,r,o,d,l,h,c,u=this,k=a.styles,g=this.options.editable,m=function(){u.view()._removePercentCompleteTooltip(),t=null,e=null,u.dragInProgress=!1},_=function(e){t.find(M+k.taskComplete).width(e).end().siblings(M+k.taskDragHandle).css(f?"right":"left",e)};g&&g.dragPercentComplete!==!1&&g.update!==!1&&(this._percentDraggable=new kendo.ui.Draggable(this.wrapper,{distance:0,filter:M+k.taskDragHandle,holdToDrag:!1}),this._percentDraggable.bind("dragstart",function(n){return u.trigger("percentResizeStart")?void n.preventDefault():(t=n.currentTarget.siblings(M+k.task),e=u._taskByUid(t.attr("data-uid")),d=e.percentComplete,s=t.offset(),i=this.element.offset(),r=t.find(M+k.taskComplete).width(),o=p(t),clearTimeout(u._tooltipTimeout),void(u.dragInProgress=!0))}).bind("drag",kendo.throttle(function(e){if(u.dragInProgress){c=f?-e.x.initialDelta:e.x.initialDelta;var t=Math.max(0,Math.min(o,r+c));d=Math.round(t/o*100),_(t),l=s.top-i.top,h=s.left+t-i.left,f&&(h+=o-2*t),u.view()._updatePercentCompleteTooltip(l,h,d)}},15)).bind("dragend",function(){u.trigger("percentResizeEnd",{task:e,percentComplete:d/100}),m()}).bind("dragcancel",function(){_(r),m()}).userEvents.bind("select",function(){n()}))},_createDependencyDraggable:function(){var t,s,i,r=this,o=e(),d=e(),l=g.msie&&g.version<9,h=a.styles,u=this.options.editable,k=function(){t.css("display","").removeClass(h.hovered),t.parent().removeClass(h.origin),t=null,f(!1),d=e(),o=e(),r.view()._removeDependencyDragHint(),r.dragInProgress=!1},f=function(e){d.hasClass(h.origin)||(d.find(M+h.taskDot).css("display",e?"block":""),o.toggleClass(h.hovered,e))};u&&u.dependencyCreate!==!1&&(l&&document.namespaces&&document.namespaces.add("kvml","urn:schemas-microsoft-com:vml","#default#VML"),this._dependencyDraggable=new kendo.ui.Draggable(this.wrapper,{distance:0,filter:M+h.taskDot,holdToDrag:!1}),this._dependencyDraggable.bind("dragstart",function(e){var n,a;return r.trigger("dependencyDragStart")?void e.preventDefault():(t=e.currentTarget.css("display","block").addClass(h.hovered),t.parent().addClass(h.origin),n=t.offset(),a=r.wrapper.find(M+h.tasksWrapper).offset(),s=Math.round(n.left-a.left+c(t)/2),i=Math.round(n.top-a.top+p(t)/2),clearTimeout(r._tooltipTimeout),void(r.dragInProgress=!0))}).bind("drag",kendo.throttle(function(t){var n,a,p,c;r.dragInProgress&&(r.view()._removeDependencyDragHint(),n=e(kendo.elementUnderCursor(t)),a=r.wrapper.find(M+h.tasksWrapper).offset(),p=t.x.location-a.left,c=t.y.location-a.top,r.view()._updateDependencyDragHint({x:s,y:i},{x:p,y:c},l),f(!1),o=n.hasClass(h.taskDot)?n:e(),d=n.closest(M+h.taskWrap),f(!0))},15)).bind("dragend",function(){var e,s,n,i,a;o.length&&(e=t.hasClass(h.taskDotStart),s=o.hasClass(h.taskDotStart),n=e?s?3:2:s?1:0,i=r._taskByUid(t.siblings(M+h.task).attr("data-uid")),a=r._taskByUid(o.siblings(M+h.task).attr("data-uid")),i!==a&&r.trigger("dependencyDragEnd",{type:n,predecessor:i,successor:a})),k()}).bind("dragcancel",function(){k()}).userEvents.bind("select",function(){n()}))},_selectable:function(){var t=this,s=a.styles;this.options.selectable&&this.wrapper.on(w+v,M+s.task,function(s){s.stopPropagation(),s.ctrlKey?t.trigger("clear"):t.trigger("select",{uid:e(this).attr("data-uid")})}).on(w+v,M+s.taskWrap,function(t){t.stopPropagation(),e(this).css("z-index","0");var n=e(document.elementFromPoint(t.clientX,t.clientY));n.hasClass(s.line)&&n.click(),e(this).css("z-index","")}).on(w+v,M+s.tasksWrapper,function(){t.selectDependency().length>0?t.clearSelection():t.trigger("clear")}).on(w+v,M+s.line,function(e){e.stopPropagation(),t.selectDependency(this)})},select:function(e){var t=this.wrapper.find(e),s=a.styles;return t.length?(this.clearSelection(),t.addClass(s.selected),void(kendo.support.mobileOS&&t.parent().addClass(s.taskWrapActive))):this.wrapper.find(M+s.task+M+s.selected)},selectDependency:function(t){var s,n=this.wrapper.find(t),i=a.styles;return n.length?(this.clearSelection(),this.trigger("clear"),s=e(n).attr("data-uid"),void this.wrapper.find(M+i.line+"[data-uid='"+s+"']").addClass(i.selected)):this.wrapper.find(M+i.line+M+i.selected)},clearSelection:function(){var e=a.styles;this.wrapper.find(M+e.selected).removeClass(e.selected),kendo.support.mobileOS&&this.wrapper.find(M+e.taskWrapActive).removeClass(e.taskWrapActive)},_attachEvents:function(){var t=this,s=a.styles,n=this.options.editable;n&&(this._tabindex(),this.wrapper.on(w+v,M+s.taskDelete,function(n){t.trigger("removeTask",{uid:e(this).closest(M+s.task).attr("data-uid")}),n.stopPropagation(),n.preventDefault()}).on(W+v,function(e){var s,n=t.options.editable;e.keyCode===m.DELETE&&n&&n.dependencyDestroy!==!1&&(s=t.selectDependency(),s.length&&(t.trigger("removeDependency",{uid:s.attr("data-uid")}),t.clearSelection()))}),kendo.support.mobileOS?this.touch=this.wrapper.kendoTouch({filter:M+s.task,doubletap:function(s){t.options.editable.update!==!1&&t.trigger("editTask",{uid:e(s.touch.currentTarget).attr("data-uid")})}}).data("kendoTouch"):this.wrapper.on(T+v,M+s.task,function(s){t.options.editable.update!==!1&&(t.trigger("editTask",{uid:e(this).attr("data-uid")}),s.stopPropagation(),s.preventDefault())}))},_tooltip:function(){var t,s=this,n=this.options.tooltip,i=a.styles,r=function(e){t=e.clientX};n&&n.visible===!1||(kendo.support.mobileOS?(this.wrapper.on(w+v,M+i.taskDelete,function(e){e.stopPropagation(),s.view()._removeTaskTooltip()}).on(H+v,M+i.task,function(t){var n=e(t.relatedTarget).parents(M+i.taskWrap,M+i.task);0===n.length&&s.view()._removeTaskTooltip()}),this.touch&&this.touch.bind("tap",function(t){var n=t.touch.target,i=s._taskByUid(e(n).attr("data-uid")),a=t.touch.x.client;s.view()._taskTooltip&&s.view()._removeTaskTooltip(),s.view()._createTaskTooltip(i,n,a)}).bind("doubletap",function(){s.view()._removeTaskTooltip()})):this.wrapper.on(b+v,M+i.task,function(){var n=this,i=s._taskByUid(e(this).attr("data-uid"));s.dragInProgress||(s._tooltipTimeout=setTimeout(function(){s.view()._createTaskTooltip(i,n,t)},800),e(this).on(D,r))}).on(H+v,M+i.task,function(){clearTimeout(s._tooltipTimeout),s.view()._removeTaskTooltip(),e(this).off(D,r)}))}}),u(!0,a,{styles:i})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,s){(s||t)()});
//# sourceMappingURL=kendo.gantt.timeline.min.js.map
