{"version": 3, "sources": ["kendo.switch.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "NS", "Widget", "support", "CHANGE", "switchStyles", "widget", "container", "handle", "checked", "checked<PERSON><PERSON><PERSON>", "unchecked", "uncheckedLabel", "disabled", "readonly", "active", "DISABLED", "ARIA_DISABLED", "READONLY", "ARIA_READONLY", "ARIA_CHECKED", "CHECKED", "CLICK", "click", "TOUCHEND", "pointers", "KEYDOWN", "LABELIDPART", "proxy", "SWITCH_TEMPLATE", "template", "SWITCH_CONTAINER_TEMPLATE", "Switch", "extend", "init", "element", "options", "wrapper", "that", "this", "fn", "call", "type", "styles", "wrap", "parent", "append", "messages", "addClass", "className", "on", "_click", "_touchEnd", "_keydown", "enabled", "_tabindex", "_initSettings", "_aria", "notify", "setOptions", "find", "text", "width", "css", "enable", "check", "attr", "labelId", "id", "labelFor", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "length", "guid", "events", "name", "toggleClass", "removeAttr", "value", "apply", "destroy", "off", "toggle", "_check", "focus", "trigger", "e", "keyCode", "keys", "SPACEBAR", "preventDefault", "_isTouch", "event", "test", "originalEvent", "pointerType", "which", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gBAAiB,cAAeD,IACzC,WAuME,MA/LC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAAK,eAAgBC,EAASF,EAAGE,OAAQC,EAAUL,EAAMK,QAASC,EAAS,SAAUC,GACtHC,OAAQ,oBACRC,UAAW,qBACXC,OAAQ,kBACRC,QAAS,cACTC,aAAc,oBACdC,UAAW,eACXC,eAAgB,qBAChBC,SAAU,mBACVC,SAAU,mBACVC,OAAQ,kBACTC,EAAW,WAAYC,EAAgB,gBAAiBC,EAAW,WAAYC,EAAgB,gBAAiBC,EAAe,eAAgBC,EAAU,UAAWC,EAAQnB,EAAQoB,MAAQtB,EAAIuB,EAAWrB,EAAQsB,SAAW,YAAc,WAAYC,EAAU,UAAYzB,EAAI0B,EAAc,SAAUC,EAAQhC,EAAEgC,MACvTC,EAAkB/B,EAAMgC,SAAS,wDACjCC,EAA4BjC,EAAMgC,SAAS,mMAC3CE,EAAS9B,EAAO+B,QAChBC,KAAM,SAAUC,EAASC,GACrB,GAAiBC,GAAbC,EAAOC,IACXrC,GAAOsC,GAAGN,KAAKO,KAAKH,EAAMH,EAASC,GACnCA,EAAUE,EAAKF,QACfD,EAAUG,EAAKH,QAAQ,GACvBA,EAAQO,KAAO,WACfL,EAAUzC,EAAEiC,GAAkBc,OAAQtC,KACtCiC,EAAKD,QAAUC,EAAKH,QAAQS,KAAKP,GAASQ,SAC1CP,EAAKD,QAAQS,OAAOlD,EAAEmC,GAClBY,OAAQtC,EACRI,QAAS2B,EAAQW,SAAStC,QAC1BE,UAAWyB,EAAQW,SAASpC,cAC3BqC,SAASb,EAAQc,WACtBX,EAAKD,QAAQa,GAAG5B,EAAOM,EAAMU,EAAKa,OAAQb,IAAOY,GAAG1B,EAAUI,EAAMU,EAAKc,UAAWd,IAAOY,GAAGxB,EAASE,EAAMU,EAAKe,SAAUf,IACxHA,EAAKF,QAAQkB,SACbhB,EAAKiB,YAETjB,EAAKkB,gBACLlB,EAAKmB,QACL3D,EAAM4D,OAAOpB,EAAMxC,EAAME,KAE7B2D,WAAY,SAAUvB,GAClB,GAA8C1B,GAAcE,EAAxD0B,EAAOC,KAAMQ,EAAWX,EAAQW,QACpCT,GAAKF,QAAUxC,EAAEqC,OAAOK,EAAKF,QAASA,GAClCW,GAAYA,EAAStC,UAAYZ,IACjCa,EAAe4B,EAAKD,QAAQuB,KAAK,IAAMvD,EAAaK,cACpDA,EAAamD,KAAKd,EAAStC,UAE3BsC,GAAYA,EAASpC,YAAcd,IACnCe,EAAiB0B,EAAKD,QAAQuB,KAAK,IAAMvD,EAAaO,gBACtDA,EAAeiD,KAAKd,EAASpC,YAE7ByB,EAAQ0B,OACRxB,EAAKD,QAAQ0B,KAAMD,MAAO1B,EAAQ0B,QAElC1B,EAAQkB,UAAYzD,GACpByC,EAAK0B,OAAO5B,EAAQkB,SAEpBlB,EAAQtB,WAAajB,GACrByC,EAAKxB,SAASsB,EAAQtB,UAE1BwB,EAAK2B,MAAM7B,EAAQ3B,UAEvB+C,cAAe,WACX,GAAIlB,GAAOC,KAAMJ,EAAUG,EAAKH,QAAQ,GAAIC,EAAUE,EAAKF,OACvDA,GAAQ0B,OACRxB,EAAKD,QAAQ0B,KAAMD,MAAO1B,EAAQ0B,QAEd,OAApB1B,EAAQ3B,UACR2B,EAAQ3B,QAAU0B,EAAQ1B,SAE9B6B,EAAK2B,MAAM7B,EAAQ3B,SACnB2B,EAAQkB,QAAUlB,EAAQkB,UAAYhB,EAAKH,QAAQ+B,KAAKlD,GACxDsB,EAAK0B,OAAO5B,EAAQkB,SACpBlB,EAAQtB,SAAWsB,EAAQtB,YAAcwB,EAAKH,QAAQ+B,KAAKhD,GAC3DoB,EAAKxB,SAASsB,EAAQtB,WAE1B2C,MAAO,WAAA,GAOKU,GANJ7B,EAAOC,KAAMJ,EAAUG,EAAKH,QAASE,EAAUC,EAAKD,QAAS+B,EAAKjC,EAAQ+B,KAAK,MAAOG,EAAWzE,EAAE,cAAgBwE,EAAK,MAAOE,EAAYnC,EAAQ+B,KAAK,cAAeK,EAAiBpC,EAAQ+B,KAAK,kBACrMI,GACAjC,EAAQ6B,KAAK,aAAcI,GACpBC,EACPlC,EAAQ6B,KAAK,kBAAmBK,GACzBF,EAASG,SACZL,EAAUE,EAASH,KAAK,MACvBC,IACDA,GAAWC,GAAMtE,EAAM2E,QAAU9C,EACjC0C,EAASH,KAAK,KAAMC,IAExB9B,EAAQ6B,KAAK,kBAAmBC,KAGxCO,QAAStE,GACTgC,SACIuC,KAAM,SACN5B,UACItC,QAAS,KACTE,UAAW,OAEfmD,MAAO,KACPrD,QAAS,KACT6C,SAAS,EACTxC,UAAU,GAEdmD,MAAO,SAAUxD,GACb,GAAI6B,GAAOC,KAAMJ,EAAUG,EAAKH,QAAQ,EACxC,OAAI1B,KAAYZ,EACLsC,EAAQ1B,SAEf0B,EAAQ1B,UAAYA,IACpB6B,EAAKF,QAAQ3B,QAAU0B,EAAQ1B,QAAUA,GAE7C6B,EAAKD,QAAQ6B,KAAK9C,EAAcX,GAASmE,YAAYvE,EAAaI,QAASA,GAASmE,YAAYvE,EAAaM,WAAYF,GACrHA,EACA6B,EAAKH,QAAQ+B,KAAK7C,EAASA,GAE3BiB,EAAKH,QAAQ0C,WAAWxD,GAP5B,IAUJyD,MAAO,SAAUA,GAIb,MAHqB,gBAAVA,KACPA,EAAkB,SAAVA,GAELvC,KAAK0B,MAAMc,MAAMxC,MAAOuC,KAEnCE,QAAS,WACL9E,EAAOsC,GAAGwC,QAAQvC,KAAKF,MACvBA,KAAKF,QAAQ4C,IAAIhF,IAErBiF,OAAQ,WACJ,GAAI5C,GAAOC,IACXD,GAAK2B,OAAO3B,EAAKH,QAAQ,GAAG1B,UAEhCuD,OAAQ,SAAUA,GACd,GAAI7B,GAAUI,KAAKJ,QAASE,EAAUE,KAAKF,OACtB,KAAV2B,IACPA,GAAS,GAEbzB,KAAKH,QAAQkB,QAAUU,EACnBA,GACA7B,EAAQ0C,WAAW7D,GACnBqB,EAAQwC,WAAW5D,KAEnBkB,EAAQ+B,KAAKlD,EAAUA,GACvBqB,EAAQ6B,KAAKjD,GAAe,IAEhCoB,EAAQuC,YAAYvE,EAAaQ,UAAWmD,IAEhDlD,SAAU,SAAUA,GAChB,GAAIwB,GAAOC,KAAMJ,EAAUG,EAAKH,QAASE,EAAUC,EAAKD,OACjC,KAAZvB,IACPA,GAAW,GAEfwB,EAAKF,QAAQtB,SAAWA,EACpBA,GACAqB,EAAQ+B,KAAKhD,GAAU,GACvBmB,EAAQ6B,KAAK/C,GAAe,KAE5BgB,EAAQ0C,WAAW3D,GACnBmB,EAAQwC,WAAW1D,IAEvBkB,EAAQuC,YAAYvE,EAAaS,SAAUA,IAE/CqE,OAAQ,WACJ,GAAI7C,GAAOC,KAAM9B,EAAU6B,EAAKH,QAAQ,GAAG1B,SAAW6B,EAAKH,QAAQ,GAAG1B,OAEtE,OADA6B,GAAKD,QAAQ+C,SACR9C,EAAKF,QAAQkB,SAAWhB,EAAKF,QAAQtB,UAAYwB,EAAK+C,QAAQjF,GAAUK,QAASA,KAClF6B,EAAKH,QAAQ,GAAG1B,SAAWA,EAC3B,IAEJ6B,EAAK2B,MAAMxD,GAAX6B,IAEJe,SAAU,SAAUiC,GACZA,EAAEC,UAAYzF,EAAM0F,KAAKC,WACzBlD,KAAK4C,SACLG,EAAEI,mBAGVC,SAAU,SAAUC,GAChB,MAAO,QAAQC,KAAKD,EAAMlD,OAASkD,EAAME,eAAiB,QAAQD,KAAKD,EAAME,cAAcC,cAE/F5C,OAAQ,SAAUmC,GACT/C,KAAKoD,SAASL,IAAkB,IAAZA,EAAEU,OACvBzD,KAAK4C,UAGb/B,UAAW,SAAUkC,GACb/C,KAAKoD,SAASL,KACd/C,KAAK4C,SACLG,EAAEI,oBAId1F,GAAGiG,OAAOjE,IACZjC,OAAOD,MAAMoG,QACRnG,OAAOD,OACE,kBAAVH,SAAwBA,OAAOwG,IAAMxG,OAAS,SAAUyG,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.switch.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.switch', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'switch',\n        name: 'Switch',\n        category: 'web',\n        description: 'The Switch widget is used to display two exclusive choices.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, NS = '.kendoSwitch', Widget = ui.Widget, support = kendo.support, CHANGE = 'change', switchStyles = {\n                widget: 'k-switch k-widget',\n                container: 'k-switch-container',\n                handle: 'k-switch-handle',\n                checked: 'k-switch-on',\n                checkedLabel: 'k-switch-label-on',\n                unchecked: 'k-switch-off',\n                uncheckedLabel: 'k-switch-label-off',\n                disabled: 'k-state-disabled',\n                readonly: 'k-state-readonly',\n                active: 'k-state-active'\n            }, DISABLED = 'disabled', ARIA_DISABLED = 'aria-disabled', READONLY = 'readonly', ARIA_READONLY = 'aria-readonly', ARIA_CHECKED = 'aria-checked', CHECKED = 'checked', CLICK = support.click + NS, TOUCHEND = support.pointers ? 'pointerup' : 'touchend', KEYDOWN = 'keydown' + NS, LABELIDPART = '_label', proxy = $.proxy;\n        var SWITCH_TEMPLATE = kendo.template('<span class=\"#=styles.widget#\" role=\"switch\"></span>');\n        var SWITCH_CONTAINER_TEMPLATE = kendo.template('<span class=\\'#=styles.container#\\'>' + '<span class=\\'#=styles.checkedLabel#\\'>#=checked#</span>' + '<span class=\\'#=styles.uncheckedLabel#\\'>#=unchecked#</span>' + '<span class=\\'#=styles.handle#\\'></span>' + '</span>');\n        var Switch = Widget.extend({\n            init: function (element, options) {\n                var that = this, wrapper;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.element[0];\n                element.type = 'checkbox';\n                wrapper = $(SWITCH_TEMPLATE({ styles: switchStyles }));\n                that.wrapper = that.element.wrap(wrapper).parent();\n                that.wrapper.append($(SWITCH_CONTAINER_TEMPLATE({\n                    styles: switchStyles,\n                    checked: options.messages.checked,\n                    unchecked: options.messages.unchecked\n                }))).addClass(element.className);\n                that.wrapper.on(CLICK, proxy(that._click, that)).on(TOUCHEND, proxy(that._touchEnd, that)).on(KEYDOWN, proxy(that._keydown, that));\n                if (that.options.enabled) {\n                    that._tabindex();\n                }\n                that._initSettings();\n                that._aria();\n                kendo.notify(that, kendo.ui);\n            },\n            setOptions: function (options) {\n                var that = this, messages = options.messages, checkedLabel, uncheckedLabel;\n                that.options = $.extend(that.options, options);\n                if (messages && messages.checked !== undefined) {\n                    checkedLabel = that.wrapper.find('.' + switchStyles.checkedLabel);\n                    checkedLabel.text(messages.checked);\n                }\n                if (messages && messages.unchecked !== undefined) {\n                    uncheckedLabel = that.wrapper.find('.' + switchStyles.uncheckedLabel);\n                    uncheckedLabel.text(messages.unchecked);\n                }\n                if (options.width) {\n                    that.wrapper.css({ width: options.width });\n                }\n                if (options.enabled !== undefined) {\n                    that.enable(options.enabled);\n                }\n                if (options.readonly !== undefined) {\n                    that.readonly(options.readonly);\n                }\n                that.check(options.checked);\n            },\n            _initSettings: function () {\n                var that = this, element = that.element[0], options = that.options;\n                if (options.width) {\n                    that.wrapper.css({ width: options.width });\n                }\n                if (options.checked === null) {\n                    options.checked = element.checked;\n                }\n                that.check(options.checked);\n                options.enabled = options.enabled && !that.element.attr(DISABLED);\n                that.enable(options.enabled);\n                options.readonly = options.readonly || !!that.element.attr(READONLY);\n                that.readonly(options.readonly);\n            },\n            _aria: function () {\n                var that = this, element = that.element, wrapper = that.wrapper, id = element.attr('id'), labelFor = $('label[for=\"' + id + '\"]'), ariaLabel = element.attr('aria-label'), ariaLabelledBy = element.attr('aria-labelledby');\n                if (ariaLabel) {\n                    wrapper.attr('aria-label', ariaLabel);\n                } else if (ariaLabelledBy) {\n                    wrapper.attr('aria-labelledby', ariaLabelledBy);\n                } else if (labelFor.length) {\n                    var labelId = labelFor.attr('id');\n                    if (!labelId) {\n                        labelId = (id || kendo.guid()) + LABELIDPART;\n                        labelFor.attr('id', labelId);\n                    }\n                    wrapper.attr('aria-labelledby', labelId);\n                }\n            },\n            events: [CHANGE],\n            options: {\n                name: 'Switch',\n                messages: {\n                    checked: 'On',\n                    unchecked: 'Off'\n                },\n                width: null,\n                checked: null,\n                enabled: true,\n                readonly: false\n            },\n            check: function (checked) {\n                var that = this, element = that.element[0];\n                if (checked === undefined) {\n                    return element.checked;\n                }\n                if (element.checked !== checked) {\n                    that.options.checked = element.checked = checked;\n                }\n                that.wrapper.attr(ARIA_CHECKED, checked).toggleClass(switchStyles.checked, checked).toggleClass(switchStyles.unchecked, !checked);\n                if (checked) {\n                    that.element.attr(CHECKED, CHECKED);\n                } else {\n                    that.element.removeAttr(CHECKED);\n                }\n            },\n            value: function (value) {\n                if (typeof value === 'string') {\n                    value = value === 'true';\n                }\n                return this.check.apply(this, [value]);\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.wrapper.off(NS);\n            },\n            toggle: function () {\n                var that = this;\n                that.check(!that.element[0].checked);\n            },\n            enable: function (enable) {\n                var element = this.element, wrapper = this.wrapper;\n                if (typeof enable == 'undefined') {\n                    enable = true;\n                }\n                this.options.enabled = enable;\n                if (enable) {\n                    element.removeAttr(DISABLED);\n                    wrapper.removeAttr(ARIA_DISABLED);\n                } else {\n                    element.attr(DISABLED, DISABLED);\n                    wrapper.attr(ARIA_DISABLED, true);\n                }\n                wrapper.toggleClass(switchStyles.disabled, !enable);\n            },\n            readonly: function (readonly) {\n                var that = this, element = that.element, wrapper = that.wrapper;\n                if (typeof readonly == 'undefined') {\n                    readonly = true;\n                }\n                that.options.readonly = readonly;\n                if (readonly) {\n                    element.attr(READONLY, true);\n                    wrapper.attr(ARIA_READONLY, true);\n                } else {\n                    element.removeAttr(READONLY);\n                    wrapper.removeAttr(ARIA_READONLY);\n                }\n                wrapper.toggleClass(switchStyles.readonly, readonly);\n            },\n            _check: function () {\n                var that = this, checked = that.element[0].checked = !that.element[0].checked;\n                that.wrapper.focus();\n                if (!that.options.enabled || that.options.readonly || that.trigger(CHANGE, { checked: checked })) {\n                    that.element[0].checked = !checked;\n                    return;\n                }\n                that.check(checked);\n            },\n            _keydown: function (e) {\n                if (e.keyCode === kendo.keys.SPACEBAR) {\n                    this._check();\n                    e.preventDefault();\n                }\n            },\n            _isTouch: function (event) {\n                return /touch/.test(event.type) || event.originalEvent && /touch/.test(event.originalEvent.pointerType);\n            },\n            _click: function (e) {\n                if (!this._isTouch(e) && e.which === 1) {\n                    this._check();\n                }\n            },\n            _touchEnd: function (e) {\n                if (this._isTouch(e)) {\n                    this._check();\n                    e.preventDefault();\n                }\n            }\n        });\n        ui.plugin(Switch);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}