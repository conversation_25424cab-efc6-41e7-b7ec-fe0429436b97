{"version": 3, "sources": ["kendo.color.js"], "names": ["f", "define", "hex", "n", "width", "pad", "result", "toString", "length", "hue2rgb", "p", "q", "s", "t", "parseColor", "value", "safe", "m", "ret", "color", "BaseColor", "toLowerCase", "matchNamedColor", "RGB", "namedColors", "match", "exec", "Bytes", "parseInt", "parseFloat", "Error", "Class", "support", "browser", "HSV", "HSL", "Color", "window", "kendo", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "<PERSON><PERSON>rey", "darkgreen", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "grey", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "<PERSON><PERSON>rey", "lightgreen", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "regexp", "colorNames", "Object", "keys", "push", "RegExp", "join", "extend", "init", "toHSV", "this", "toRGB", "toHex", "toBytes", "to<PERSON>s", "toCssRgba", "rgb", "r", "g", "b", "a", "toFixed", "toDisplay", "msie", "version", "equals", "c", "diff", "other", "c1", "c2", "NaN", "Math", "sqrt", "pow", "clone", "fn", "call", "h", "ref", "min", "max", "delta", "v", "toHSL", "d", "l", "round", "i", "floor", "formats", "resolvedColor", "idx", "formatRegex", "processor", "parts", "channels", "this$1", "arguments", "resolveColor", "re", "process", "normalizeByte", "pad<PERSON><PERSON><PERSON>", "char<PERSON>t", "substr", "replace", "isNaN", "brightness", "percBrightness", "fromBytes", "fromRGB", "fromHSV", "fromHSL", "deepExtend", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,eAAgB,cAAeD,IACxC,WAuTE,QAASE,GAAIC,EAAGC,EAAOC,GACP,SAARA,IACAA,EAAM,IAGV,KADA,GAAIC,GAASH,EAAEI,SAAS,IACjBH,EAAQE,EAAOE,QAClBF,EAASD,EAAMC,CAEnB,OAAOA,GAkGX,QAASG,GAAQC,EAAGC,EAAGC,GACnB,GAAIC,GAAID,CAOR,OANIC,GAAI,IACJA,GAAK,GAELA,EAAI,IACJA,GAAK,GAELA,EAAI,EAAI,EACDH,EAAc,GAATC,EAAID,GAASG,EAEzBA,EAAI,GACGF,EAEPE,EAAI,EAAI,EACDH,GAAKC,EAAID,IAAM,EAAI,EAAIG,GAAK,EAEhCH,EAEX,QAASI,GAAWC,EAAOC,GAA3B,GACQC,GAAGC,EAOHC,CANJ,IAAa,MAATJ,GAA2B,SAAVA,EACjB,MAAO,KAEX,IAAIA,YAAiBK,GACjB,MAAOL,EAGX,IADII,EAAQJ,EAAMM,cACdJ,EAAIK,EAAgBH,GAOpB,MALIA,GADS,gBAATF,EAAE,GACM,GAAIM,GAAI,EAAG,EAAG,EAAG,GAEjBT,EAAWU,EAAYP,EAAE,IAAKD,GAE1CG,EAAMM,OAASR,EAAE,IACVE,CAeX,KAbIF,EAAI,gDAAgDS,KAAKP,IACzDD,EAAM,GAAIS,GAAMC,SAASX,EAAE,GAAI,IAAKW,SAASX,EAAE,GAAI,IAAKW,SAASX,EAAE,GAAI,IAAK,IACrEA,EAAI,uCAAuCS,KAAKP,IACvDD,EAAM,GAAIS,GAAMC,SAASX,EAAE,GAAKA,EAAE,GAAI,IAAKW,SAASX,EAAE,GAAKA,EAAE,GAAI,IAAKW,SAASX,EAAE,GAAKA,EAAE,GAAI,IAAK,IAC1FA,EAAI,uDAAuDS,KAAKP,IACvED,EAAM,GAAIS,GAAMC,SAASX,EAAE,GAAI,IAAKW,SAASX,EAAE,GAAI,IAAKW,SAASX,EAAE,GAAI,IAAK,IACrEA,EAAI,wEAAwES,KAAKP,IACxFD,EAAM,GAAIS,GAAMC,SAASX,EAAE,GAAI,IAAKW,SAASX,EAAE,GAAI,IAAKW,SAASX,EAAE,GAAI,IAAKY,WAAWZ,EAAE,MAClFA,EAAI,qFAAqFS,KAAKP,IACrGD,EAAM,GAAIK,GAAIM,WAAWZ,EAAE,IAAM,IAAKY,WAAWZ,EAAE,IAAM,IAAKY,WAAWZ,EAAE,IAAM,IAAK,IAC/EA,EAAI,sGAAsGS,KAAKP,MACtHD,EAAM,GAAIK,GAAIM,WAAWZ,EAAE,IAAM,IAAKY,WAAWZ,EAAE,IAAM,IAAKY,WAAWZ,EAAE,IAAM,IAAKY,WAAWZ,EAAE,MAEnGC,EACAA,EAAIO,MAAQR,MACT,KAAKD,EACR,KAAUc,OAAM,uBAAyBX,EAE7C,OAAOD,GAxdb,GAUMa,GACAC,EACAR,EAqJAS,EACAX,EASAF,EA+CAG,EAyEAI,EA8BAO,EAiEAC,EAwFAC,CAjdJC,QAAOC,MAAQD,OAAOC,UAClBP,EAAQO,MAAMP,MACdC,EAAUM,MAAMN,QAChBR,GACAe,UAAW,SACXC,aAAc,SACdC,KAAM,SACNC,WAAY,SACZC,MAAO,SACPC,MAAO,SACPC,OAAQ,SACRC,MAAO,SACPC,eAAgB,SAChBC,KAAM,SACNC,WAAY,SACZC,MAAO,SACPC,UAAW,SACXC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,MAAO,SACPC,eAAgB,SAChBC,SAAU,SACVC,QAAS,SACTC,KAAM,SACNC,SAAU,SACVC,SAAU,SACVC,cAAe,SACfC,SAAU,SACVC,SAAU,SACVC,UAAW,SACXC,UAAW,SACXC,YAAa,SACbC,eAAgB,SAChBC,WAAY,SACZC,WAAY,SACZC,QAAS,SACTC,WAAY,SACZC,aAAc,SACdC,cAAe,SACfC,cAAe,SACfC,cAAe,SACfC,cAAe,SACfC,WAAY,SACZC,SAAU,SACVC,YAAa,SACbC,QAAS,SACTC,QAAS,SACTC,WAAY,SACZC,UAAW,SACXC,YAAa,SACbC,YAAa,SACbC,QAAS,SACTC,UAAW,SACXC,WAAY,SACZC,KAAM,SACNC,UAAW,SACXC,KAAM,SACNC,KAAM,SACNC,MAAO,SACPC,YAAa,SACbC,SAAU,SACVC,QAAS,SACTC,UAAW,SACXC,OAAQ,SACRC,MAAO,SACPC,MAAO,SACPC,SAAU,SACVC,cAAe,SACfC,UAAW,SACXC,aAAc,SACdC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,qBAAsB,SACtBC,UAAW,SACXC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,YAAa,SACbC,cAAe,SACfC,aAAc,SACdC,eAAgB,SAChBC,eAAgB,SAChBC,eAAgB,SAChBC,YAAa,SACbC,KAAM,SACNC,UAAW,SACXC,MAAO,SACPC,QAAS,SACTC,OAAQ,SACRC,iBAAkB,SAClBC,WAAY,SACZC,aAAc,SACdC,aAAc,SACdC,eAAgB,SAChBC,gBAAiB,SACjBC,kBAAmB,SACnBC,gBAAiB,SACjBC,gBAAiB,SACjBC,aAAc,SACdC,UAAW,SACXC,UAAW,SACXC,SAAU,SACVC,YAAa,SACbC,KAAM,SACNC,QAAS,SACTC,MAAO,SACPC,UAAW,SACXC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,cAAe,SACfC,UAAW,SACXC,cAAe,SACfC,cAAe,SACfC,WAAY,SACZC,UAAW,SACXC,KAAM,SACNC,KAAM,SACNC,KAAM,SACNC,WAAY,SACZC,OAAQ,SACRC,IAAK,SACLC,UAAW,SACXC,UAAW,SACXC,YAAa,SACbC,OAAQ,SACRC,WAAY,SACZC,SAAU,SACVC,SAAU,SACVC,OAAQ,SACRC,OAAQ,SACRC,QAAS,SACTC,UAAW,SACXC,UAAW,SACXC,UAAW,SACXC,KAAM,SACNC,YAAa,SACbC,UAAW,SACXC,IAAK,SACLC,KAAM,SACNC,QAAS,SACTC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,MAAO,SACPC,MAAO,SACPC,WAAY,SACZC,OAAQ,SACRC,YAAa,UAEbxJ,EAAUD,EAAQC,QAClBX,EAAkB,SAAUH,GAAV,GAGduK,GAFAC,EAAaC,OAAOC,KAAKrK,EAM7B,OALAmK,GAAWG,KAAK,eACZJ,EAAaK,OAAO,KAAOJ,EAAWK,KAAK,KAAO,WAAY,KAClE1K,EAAkB,SAAUH,GACxB,MAAOuK,GAAOhK,KAAKP,IAEhBuK,EAAOhK,KAAKP,IAEnBC,EAAYW,EAAMkK,QAClBC,KAAM,aAENC,MAAO,WACH,MAAOC,OAEXC,MAAO,WACH,MAAOD,OAEXE,MAAO,WACH,MAAOF,MAAKG,UAAUD,SAE1BC,QAAS,WACL,MAAOH,OAEXI,MAAO,WACH,MAAO,IAAMJ,KAAKE,SAEtBG,UAAW,WACP,GAAIC,GAAMN,KAAKG,SACf,OAAO,QAAUG,EAAIC,EAAI,KAAOD,EAAIE,EAAI,KAAOF,EAAIG,EAAI,KAAOhL,aAAkBuK,KAAKU,GAAGC,QAAQ,IAAM,KAE1GC,UAAW,WACP,MAAI/K,GAAQgL,MAAQhL,EAAQiL,QAAU,EAC3Bd,KAAKI,QAETJ,KAAKK,aAEhBU,OAAQ,SAAUC,GACd,MAAOA,KAAMhB,MAAc,OAANgB,GAAchB,KAAKK,cAAgB3L,EAAWsM,GAAGX,aAE1EY,KAAM,SAAUC,GAAV,GAIEC,GACAC,CAJJ,OAAc,QAAVF,EACOG,KAEPF,EAAKnB,KAAKG,UACViB,EAAKF,EAAMf,UACRmB,KAAKC,KAAKD,KAAKE,IAAoB,IAAfL,EAAGZ,EAAIa,EAAGb,GAAU,GAAKe,KAAKE,IAAoB,KAAfL,EAAGX,EAAIY,EAAGZ,GAAW,GAAKc,KAAKE,IAAoB,KAAfL,EAAGV,EAAIW,EAAGX,GAAW,MAE3HgB,MAAO,WACH,GAAIT,GAAIhB,KAAKG,SAIb,OAHIa,KAAMhB,OACNgB,EAAI,GAAIzL,GAAMyL,EAAET,EAAGS,EAAER,EAAGQ,EAAEP,EAAGO,EAAEN,IAE5BM,KAGX7L,EAAMH,EAAU6K,QAChBC,KAAM,SAAUS,EAAGC,EAAGC,EAAGC,GACrB1L,EAAU0M,GAAG5B,KAAK6B,KAAK3B,MACvBA,KAAKO,EAAIA,EACTP,KAAKQ,EAAIA,EACTR,KAAKS,EAAIA,EACTT,KAAKU,EAAIA,GAEbX,MAAO,WAAA,GASC6B,GAAGpN,EARHqN,EAAM7B,KACNO,EAAIsB,EAAItB,EACRC,EAAIqB,EAAIrB,EACRC,EAAIoB,EAAIpB,EACRqB,EAAMR,KAAKQ,IAAIvB,EAAGC,EAAGC,GACrBsB,EAAMT,KAAKS,IAAIxB,EAAGC,EAAGC,GACrBuB,EAAQD,EAAMD,EACdG,EAAIF,CAER,OAAc,KAAVC,EACO,GAAIlM,GAAI,EAAG,EAAGmM,EAAGjC,KAAKU,IAErB,IAARqB,GACAvN,EAAIwN,EAAQD,EAERH,EADArB,IAAMwB,GACDvB,EAAIC,GAAKuB,EACPxB,IAAMuB,EACT,GAAKtB,EAAIF,GAAKyB,EAEd,GAAKzB,EAAIC,GAAKwB,EAEtBJ,GAAK,GACDA,EAAI,IACJA,GAAK,OAGTpN,EAAI,EACJoN,MAEG,GAAI9L,GAAI8L,EAAGpN,EAAGyN,EAAGjC,KAAKU,KAEjCwB,MAAO,WAAA,GAOCN,GAAGpN,EAIC2N,EAVJN,EAAM7B,KACNO,EAAIsB,EAAItB,EACRC,EAAIqB,EAAIrB,EACRC,EAAIoB,EAAIpB,EACRsB,EAAMT,KAAKS,IAAIxB,EAAGC,EAAGC,GACrBqB,EAAMR,KAAKQ,IAAIvB,EAAGC,EAAGC,GACf2B,GAAKL,EAAMD,GAAO,CAC5B,IAAIC,IAAQD,EACRF,EAAIpN,EAAI,MAIR,QAFI2N,EAAIJ,EAAMD,EACdtN,EAAI4N,EAAI,GAAMD,GAAK,EAAIJ,EAAMD,GAAOK,GAAKJ,EAAMD,GACvCC,GACR,IAAKxB,GACDqB,GAAKpB,EAAIC,GAAK0B,GAAK3B,EAAIC,EAAI,EAAI,EAC/B,MACJ,KAAKD,GACDoB,GAAKnB,EAAIF,GAAK4B,EAAI,CAClB,MACJ,KAAK1B,GACDmB,GAAKrB,EAAIC,GAAK2B,EAAI,EAM1B,MAAO,IAAIpM,GAAQ,GAAJ6L,EAAY,IAAJpN,EAAa,IAAJ4N,EAASpC,KAAKU,IAElDP,QAAS,WACL,MAAO,IAAI5K,GAAe,IAATyK,KAAKO,EAAkB,IAATP,KAAKQ,EAAkB,IAATR,KAAKS,EAAST,KAAKU,MAGpEnL,EAAQJ,EAAI0K,QACZC,KAAM,SAAUS,EAAGC,EAAGC,EAAGC,GACrBvL,EAAIuM,GAAG5B,KAAK6B,KAAK3B,KAAMsB,KAAKe,MAAM9B,GAAIe,KAAKe,MAAM7B,GAAIc,KAAKe,MAAM5B,GAAIC,IAExET,MAAO,WACH,MAAO,IAAI9K,GAAI6K,KAAKO,EAAI,IAAKP,KAAKQ,EAAI,IAAKR,KAAKS,EAAI,IAAKT,KAAKU,IAElEX,MAAO,WACH,MAAOC,MAAKC,QAAQF,SAExBmC,MAAO,WACH,MAAOlC,MAAKC,QAAQiC,SAExBhC,MAAO,WACH,MAAOpM,GAAIkM,KAAKO,EAAG,GAAKzM,EAAIkM,KAAKQ,EAAG,GAAK1M,EAAIkM,KAAKS,EAAG,IAEzDN,QAAS,WACL,MAAOH,SAaXlK,EAAMd,EAAU6K,QAChBC,KAAM,SAAU8B,EAAGpN,EAAGyN,EAAGvB,GACrB1L,EAAU0M,GAAG5B,KAAK6B,KAAK3B,MACvBA,KAAK4B,EAAIA,EACT5B,KAAKxL,EAAIA,EACTwL,KAAKiC,EAAIA,EACTjC,KAAKU,EAAIA,GAEbT,MAAO,WAAA,GAKCM,GAAGC,EAAGC,EAKF6B,EACA1O,EACAU,EACAC,EACAE,EAbJoN,EAAM7B,KACN4B,EAAIC,EAAID,EACRpN,EAAIqN,EAAIrN,EACRyN,EAAIJ,EAAII,CAEZ,IAAU,IAANzN,EACA+L,EAAIC,EAAIC,EAAIwB,MAQZ,QANAL,GAAK,GACDU,EAAIhB,KAAKiB,MAAMX,GACfhO,EAAIgO,EAAIU,EACRhO,EAAI2N,GAAK,EAAIzN,GACbD,EAAI0N,GAAK,EAAIzN,EAAIZ,GACjBa,EAAIwN,GAAK,EAAIzN,GAAK,EAAIZ,IAClB0O,GACR,IAAK,GACD/B,EAAI0B,EACJzB,EAAI/L,EACJgM,EAAInM,CACJ,MACJ,KAAK,GACDiM,EAAIhM,EACJiM,EAAIyB,EACJxB,EAAInM,CACJ,MACJ,KAAK,GACDiM,EAAIjM,EACJkM,EAAIyB,EACJxB,EAAIhM,CACJ,MACJ,KAAK,GACD8L,EAAIjM,EACJkM,EAAIjM,EACJkM,EAAIwB,CACJ,MACJ,KAAK,GACD1B,EAAI9L,EACJ+L,EAAIlM,EACJmM,EAAIwB,CACJ,MACJ,SACI1B,EAAI0B,EACJzB,EAAIlM,EACJmM,EAAIlM,EAIZ,MAAO,IAAIY,GAAIoL,EAAGC,EAAGC,EAAGT,KAAKU,IAEjCwB,MAAO,WACH,MAAOlC,MAAKC,QAAQiC,SAExB/B,QAAS,WACL,MAAOH,MAAKC,QAAQE,aAGxBpK,EAAMf,EAAU6K,QAChBC,KAAM,SAAU8B,EAAGpN,EAAG4N,EAAG1B,GACrB1L,EAAU0M,GAAG5B,KAAK6B,KAAK3B,MACvBA,KAAK4B,EAAIA,EACT5B,KAAKxL,EAAIA,EACTwL,KAAKoC,EAAIA,EACTpC,KAAKU,EAAIA,GAEbT,MAAO,WAAA,GAICM,GAAGC,EAAGC,EAIFlM,EACAD,EARJsN,EAAI5B,KAAK4B,EAAI,IACbpN,EAAIwL,KAAKxL,EAAI,IACb4N,EAAIpC,KAAKoC,EAAI,GAWjB,OATU,KAAN5N,EACA+L,EAAIC,EAAIC,EAAI2B,GAER7N,EAAI6N,EAAI,GAAMA,GAAK,EAAI5N,GAAK4N,EAAI5N,EAAI4N,EAAI5N,EACxCF,EAAI,EAAI8N,EAAI7N,EAChBgM,EAAIlM,EAAQC,EAAGC,EAAGqN,EAAI,EAAI,GAC1BpB,EAAInM,EAAQC,EAAGC,EAAGqN,GAClBnB,EAAIpM,EAAQC,EAAGC,EAAGqN,EAAI,EAAI,IAEvB,GAAIzM,GAAIoL,EAAGC,EAAGC,EAAGT,KAAKU,IAEjCX,MAAO,WACH,MAAOC,MAAKC,QAAQF,SAExBI,QAAS,WACL,MAAOH,MAAKC,QAAQE,aA4DxBnK,EAAQL,EAAMkK,QACdC,KAAM,SAAUnL,GAAV,GAGM6N,GACAC,EACKC,EACDC,EACAC,EACAC,EAEIC,EATZC,EAAS/C,IACb,IAAyB,IAArBgD,UAAU5O,OAGV,IAFIoO,EAAUxM,EAAMwM,QAChBC,EAAgBzC,KAAKiD,aAAatO,GAC7B+N,EAAM,EAAGA,EAAMF,EAAQpO,OAAQsO,IAChCC,EAAcH,EAAQE,GAAKQ,GAC3BN,EAAYJ,EAAQE,GAAKS,QACzBN,EAAQF,EAAYrN,KAAKmN,GACzBI,IACIC,EAAWF,EAAUC,GACzBE,EAAOxC,EAAIuC,EAAS,GACpBC,EAAOvC,EAAIsC,EAAS,GACpBC,EAAOtC,EAAIqC,EAAS,QAI5B9C,MAAKO,EAAIyC,UAAU,GACnBhD,KAAKQ,EAAIwC,UAAU,GACnBhD,KAAKS,EAAIuC,UAAU,EAEvBhD,MAAKO,EAAIP,KAAKoD,cAAcpD,KAAKO,GACjCP,KAAKQ,EAAIR,KAAKoD,cAAcpD,KAAKQ,GACjCR,KAAKS,EAAIT,KAAKoD,cAAcpD,KAAKS,IAErCP,MAAO,WAAA,GACCjM,GAAM+L,KAAKqD,SACX9C,EAAIP,KAAKO,EAAEpM,SAAS,IACpBqM,EAAIR,KAAKQ,EAAErM,SAAS,IACpBsM,EAAIT,KAAKS,EAAEtM,SAAS,GACxB,OAAO,IAAMF,EAAIsM,GAAKtM,EAAIuM,GAAKvM,EAAIwM,IAEvCwC,aAAc,SAAUtO,GACpB,GAAII,GAAQJ,GAAS,OAOrB,OANwB,MAApBI,EAAMuO,OAAO,KACbvO,EAAQA,EAAMwO,OAAO,EAAG,IAE5BxO,EAAQA,EAAMyO,QAAQ,KAAM,IAC5BzO,EAAQA,EAAME,cACdF,EAAQiB,EAAMZ,YAAYL,IAAUA,GAGxCqO,cAAe,SAAUzO,GACrB,MAAIA,GAAQ,GAAK8O,MAAM9O,GACZ,EAEJA,EAAQ,IAAM,IAAMA,GAE/B0O,SAAU,SAAU1O,GAChB,MAAwB,KAAjBA,EAAMP,OAAe,IAAMO,EAAQA,GAE9C+O,WAAY,SAAU/O,GAClB,GAAI0N,GAAQf,KAAKe,KAIjB,OAHArC,MAAKO,EAAI8B,EAAMrC,KAAKoD,cAAcpD,KAAKO,EAAI5L,IAC3CqL,KAAKQ,EAAI6B,EAAMrC,KAAKoD,cAAcpD,KAAKQ,EAAI7L,IAC3CqL,KAAKS,EAAI4B,EAAMrC,KAAKoD,cAAcpD,KAAKS,EAAI9L,IACpCqL,MAEX2D,eAAgB,WACZ,MAAOrC,MAAKC,KAAK,KAAQvB,KAAKO,EAAIP,KAAKO,EAAI,KAAQP,KAAKQ,EAAIR,KAAKQ,EAAI,KAAQR,KAAKS,EAAIT,KAAKS,MAGnGzK,EAAM4N,UAAY,SAAUrD,EAAGC,EAAGC,EAAGC,GACjC,MAAO,IAAInL,GAAMgL,EAAGC,EAAGC,EAAQ,MAALC,EAAYA,EAAI,IAE9C1K,EAAM6N,QAAU,SAAUtD,EAAGC,EAAGC,EAAGC,GAC/B,MAAO,IAAIvL,GAAIoL,EAAGC,EAAGC,EAAQ,MAALC,EAAYA,EAAI,IAE5C1K,EAAM8N,QAAU,SAAUlC,EAAGpN,EAAGyN,EAAGvB,GAC/B,MAAO,IAAI5K,GAAI8L,EAAGpN,EAAGyN,EAAQ,MAALvB,EAAYA,EAAI,IAE5C1K,EAAM+N,QAAU,SAAUnC,EAAGpN,EAAG4N,EAAG1B,GAC/B,MAAO,IAAI3K,GAAI6L,EAAGpN,EAAG4N,EAAQ,MAAL1B,EAAYA,EAAI,IAE5C1K,EAAMwM,UAEEU,GAAI,+CACJC,QAAS,SAAUN,GACf,OACIrN,SAASqN,EAAM,GAAI,IACnBrN,SAASqN,EAAM,GAAI,IACnBrN,SAASqN,EAAM,GAAI,QAK3BK,GAAI,0BACJC,QAAS,SAAUN,GACf,OACIrN,SAASqN,EAAM,GAAI,IACnBrN,SAASqN,EAAM,GAAI,IACnBrN,SAASqN,EAAM,GAAI,QAK3BK,GAAI,0BACJC,QAAS,SAAUN,GACf,OACIrN,SAASqN,EAAM,GAAKA,EAAM,GAAI,IAC9BrN,SAASqN,EAAM,GAAKA,EAAM,GAAI,IAC9BrN,SAASqN,EAAM,GAAKA,EAAM,GAAI,QAK9C7M,EAAMZ,YAAcA,EACpBc,MAAM8N,WAAW9N,OACbxB,WAAYA,EACZsB,MAAOA,KAEK,kBAAVnC,SAAwBA,OAAOoQ,IAAMpQ,OAAS,SAAUqQ,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.color.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.color', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'color',\n        name: 'Color utils',\n        category: 'framework',\n        advanced: true,\n        description: 'Color utilities used across components',\n        depends: ['core']\n    };\n    window.kendo = window.kendo || {};\n    var Class = kendo.Class;\n    var support = kendo.support;\n    var namedColors = {\n        aliceblue: 'f0f8ff',\n        antiquewhite: 'faebd7',\n        aqua: '00ffff',\n        aquamarine: '7fffd4',\n        azure: 'f0ffff',\n        beige: 'f5f5dc',\n        bisque: 'ffe4c4',\n        black: '000000',\n        blanchedalmond: 'ffebcd',\n        blue: '0000ff',\n        blueviolet: '8a2be2',\n        brown: 'a52a2a',\n        burlywood: 'deb887',\n        cadetblue: '5f9ea0',\n        chartreuse: '7fff00',\n        chocolate: 'd2691e',\n        coral: 'ff7f50',\n        cornflowerblue: '6495ed',\n        cornsilk: 'fff8dc',\n        crimson: 'dc143c',\n        cyan: '00ffff',\n        darkblue: '00008b',\n        darkcyan: '008b8b',\n        darkgoldenrod: 'b8860b',\n        darkgray: 'a9a9a9',\n        darkgrey: 'a9a9a9',\n        darkgreen: '006400',\n        darkkhaki: 'bdb76b',\n        darkmagenta: '8b008b',\n        darkolivegreen: '556b2f',\n        darkorange: 'ff8c00',\n        darkorchid: '9932cc',\n        darkred: '8b0000',\n        darksalmon: 'e9967a',\n        darkseagreen: '8fbc8f',\n        darkslateblue: '483d8b',\n        darkslategray: '2f4f4f',\n        darkslategrey: '2f4f4f',\n        darkturquoise: '00ced1',\n        darkviolet: '9400d3',\n        deeppink: 'ff1493',\n        deepskyblue: '00bfff',\n        dimgray: '696969',\n        dimgrey: '696969',\n        dodgerblue: '1e90ff',\n        firebrick: 'b22222',\n        floralwhite: 'fffaf0',\n        forestgreen: '228b22',\n        fuchsia: 'ff00ff',\n        gainsboro: 'dcdcdc',\n        ghostwhite: 'f8f8ff',\n        gold: 'ffd700',\n        goldenrod: 'daa520',\n        gray: '808080',\n        grey: '808080',\n        green: '008000',\n        greenyellow: 'adff2f',\n        honeydew: 'f0fff0',\n        hotpink: 'ff69b4',\n        indianred: 'cd5c5c',\n        indigo: '4b0082',\n        ivory: 'fffff0',\n        khaki: 'f0e68c',\n        lavender: 'e6e6fa',\n        lavenderblush: 'fff0f5',\n        lawngreen: '7cfc00',\n        lemonchiffon: 'fffacd',\n        lightblue: 'add8e6',\n        lightcoral: 'f08080',\n        lightcyan: 'e0ffff',\n        lightgoldenrodyellow: 'fafad2',\n        lightgray: 'd3d3d3',\n        lightgrey: 'd3d3d3',\n        lightgreen: '90ee90',\n        lightpink: 'ffb6c1',\n        lightsalmon: 'ffa07a',\n        lightseagreen: '20b2aa',\n        lightskyblue: '87cefa',\n        lightslategray: '778899',\n        lightslategrey: '778899',\n        lightsteelblue: 'b0c4de',\n        lightyellow: 'ffffe0',\n        lime: '00ff00',\n        limegreen: '32cd32',\n        linen: 'faf0e6',\n        magenta: 'ff00ff',\n        maroon: '800000',\n        mediumaquamarine: '66cdaa',\n        mediumblue: '0000cd',\n        mediumorchid: 'ba55d3',\n        mediumpurple: '9370d8',\n        mediumseagreen: '3cb371',\n        mediumslateblue: '7b68ee',\n        mediumspringgreen: '00fa9a',\n        mediumturquoise: '48d1cc',\n        mediumvioletred: 'c71585',\n        midnightblue: '191970',\n        mintcream: 'f5fffa',\n        mistyrose: 'ffe4e1',\n        moccasin: 'ffe4b5',\n        navajowhite: 'ffdead',\n        navy: '000080',\n        oldlace: 'fdf5e6',\n        olive: '808000',\n        olivedrab: '6b8e23',\n        orange: 'ffa500',\n        orangered: 'ff4500',\n        orchid: 'da70d6',\n        palegoldenrod: 'eee8aa',\n        palegreen: '98fb98',\n        paleturquoise: 'afeeee',\n        palevioletred: 'd87093',\n        papayawhip: 'ffefd5',\n        peachpuff: 'ffdab9',\n        peru: 'cd853f',\n        pink: 'ffc0cb',\n        plum: 'dda0dd',\n        powderblue: 'b0e0e6',\n        purple: '800080',\n        red: 'ff0000',\n        rosybrown: 'bc8f8f',\n        royalblue: '4169e1',\n        saddlebrown: '8b4513',\n        salmon: 'fa8072',\n        sandybrown: 'f4a460',\n        seagreen: '2e8b57',\n        seashell: 'fff5ee',\n        sienna: 'a0522d',\n        silver: 'c0c0c0',\n        skyblue: '87ceeb',\n        slateblue: '6a5acd',\n        slategray: '708090',\n        slategrey: '708090',\n        snow: 'fffafa',\n        springgreen: '00ff7f',\n        steelblue: '4682b4',\n        tan: 'd2b48c',\n        teal: '008080',\n        thistle: 'd8bfd8',\n        tomato: 'ff6347',\n        turquoise: '40e0d0',\n        violet: 'ee82ee',\n        wheat: 'f5deb3',\n        white: 'ffffff',\n        whitesmoke: 'f5f5f5',\n        yellow: 'ffff00',\n        yellowgreen: '9acd32'\n    };\n    var browser = support.browser;\n    var matchNamedColor = function (color) {\n        var colorNames = Object.keys(namedColors);\n        colorNames.push('transparent');\n        var regexp = new RegExp('^(' + colorNames.join('|') + ')(\\\\W|$)', 'i');\n        matchNamedColor = function (color) {\n            return regexp.exec(color);\n        };\n        return regexp.exec(color);\n    };\n    var BaseColor = Class.extend({\n        init: function () {\n        },\n        toHSV: function () {\n            return this;\n        },\n        toRGB: function () {\n            return this;\n        },\n        toHex: function () {\n            return this.toBytes().toHex();\n        },\n        toBytes: function () {\n            return this;\n        },\n        toCss: function () {\n            return '#' + this.toHex();\n        },\n        toCssRgba: function () {\n            var rgb = this.toBytes();\n            return 'rgba(' + rgb.r + ', ' + rgb.g + ', ' + rgb.b + ', ' + parseFloat(Number(this.a).toFixed(3)) + ')';\n        },\n        toDisplay: function () {\n            if (browser.msie && browser.version < 9) {\n                return this.toCss();\n            }\n            return this.toCssRgba();\n        },\n        equals: function (c) {\n            return c === this || c !== null && this.toCssRgba() === parseColor(c).toCssRgba();\n        },\n        diff: function (other) {\n            if (other === null) {\n                return NaN;\n            }\n            var c1 = this.toBytes();\n            var c2 = other.toBytes();\n            return Math.sqrt(Math.pow((c1.r - c2.r) * 0.3, 2) + Math.pow((c1.g - c2.g) * 0.59, 2) + Math.pow((c1.b - c2.b) * 0.11, 2));\n        },\n        clone: function () {\n            var c = this.toBytes();\n            if (c === this) {\n                c = new Bytes(c.r, c.g, c.b, c.a);\n            }\n            return c;\n        }\n    });\n    var RGB = BaseColor.extend({\n        init: function (r, g, b, a) {\n            BaseColor.fn.init.call(this);\n            this.r = r;\n            this.g = g;\n            this.b = b;\n            this.a = a;\n        },\n        toHSV: function () {\n            var ref = this;\n            var r = ref.r;\n            var g = ref.g;\n            var b = ref.b;\n            var min = Math.min(r, g, b);\n            var max = Math.max(r, g, b);\n            var delta = max - min;\n            var v = max;\n            var h, s;\n            if (delta === 0) {\n                return new HSV(0, 0, v, this.a);\n            }\n            if (max !== 0) {\n                s = delta / max;\n                if (r === max) {\n                    h = (g - b) / delta;\n                } else if (g === max) {\n                    h = 2 + (b - r) / delta;\n                } else {\n                    h = 4 + (r - g) / delta;\n                }\n                h *= 60;\n                if (h < 0) {\n                    h += 360;\n                }\n            } else {\n                s = 0;\n                h = -1;\n            }\n            return new HSV(h, s, v, this.a);\n        },\n        toHSL: function () {\n            var ref = this;\n            var r = ref.r;\n            var g = ref.g;\n            var b = ref.b;\n            var max = Math.max(r, g, b);\n            var min = Math.min(r, g, b);\n            var h, s, l = (max + min) / 2;\n            if (max === min) {\n                h = s = 0;\n            } else {\n                var d = max - min;\n                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n                switch (max) {\n                case r:\n                    h = (g - b) / d + (g < b ? 6 : 0);\n                    break;\n                case g:\n                    h = (b - r) / d + 2;\n                    break;\n                case b:\n                    h = (r - g) / d + 4;\n                    break;\n                default:\n                    break;\n                }\n            }\n            return new HSL(h * 60, s * 100, l * 100, this.a);\n        },\n        toBytes: function () {\n            return new Bytes(this.r * 255, this.g * 255, this.b * 255, this.a);\n        }\n    });\n    var Bytes = RGB.extend({\n        init: function (r, g, b, a) {\n            RGB.fn.init.call(this, Math.round(r), Math.round(g), Math.round(b), a);\n        },\n        toRGB: function () {\n            return new RGB(this.r / 255, this.g / 255, this.b / 255, this.a);\n        },\n        toHSV: function () {\n            return this.toRGB().toHSV();\n        },\n        toHSL: function () {\n            return this.toRGB().toHSL();\n        },\n        toHex: function () {\n            return hex(this.r, 2) + hex(this.g, 2) + hex(this.b, 2);\n        },\n        toBytes: function () {\n            return this;\n        }\n    });\n    function hex(n, width, pad) {\n        if (pad === void 0) {\n            pad = '0';\n        }\n        var result = n.toString(16);\n        while (width > result.length) {\n            result = pad + result;\n        }\n        return result;\n    }\n    var HSV = BaseColor.extend({\n        init: function (h, s, v, a) {\n            BaseColor.fn.init.call(this);\n            this.h = h;\n            this.s = s;\n            this.v = v;\n            this.a = a;\n        },\n        toRGB: function () {\n            var ref = this;\n            var h = ref.h;\n            var s = ref.s;\n            var v = ref.v;\n            var r, g, b;\n            if (s === 0) {\n                r = g = b = v;\n            } else {\n                h /= 60;\n                var i = Math.floor(h);\n                var f = h - i;\n                var p = v * (1 - s);\n                var q = v * (1 - s * f);\n                var t = v * (1 - s * (1 - f));\n                switch (i) {\n                case 0:\n                    r = v;\n                    g = t;\n                    b = p;\n                    break;\n                case 1:\n                    r = q;\n                    g = v;\n                    b = p;\n                    break;\n                case 2:\n                    r = p;\n                    g = v;\n                    b = t;\n                    break;\n                case 3:\n                    r = p;\n                    g = q;\n                    b = v;\n                    break;\n                case 4:\n                    r = t;\n                    g = p;\n                    b = v;\n                    break;\n                default:\n                    r = v;\n                    g = p;\n                    b = q;\n                    break;\n                }\n            }\n            return new RGB(r, g, b, this.a);\n        },\n        toHSL: function () {\n            return this.toRGB().toHSL();\n        },\n        toBytes: function () {\n            return this.toRGB().toBytes();\n        }\n    });\n    var HSL = BaseColor.extend({\n        init: function (h, s, l, a) {\n            BaseColor.fn.init.call(this);\n            this.h = h;\n            this.s = s;\n            this.l = l;\n            this.a = a;\n        },\n        toRGB: function () {\n            var h = this.h / 360;\n            var s = this.s / 100;\n            var l = this.l / 100;\n            var r, g, b;\n            if (s === 0) {\n                r = g = b = l;\n            } else {\n                var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n                var p = 2 * l - q;\n                r = hue2rgb(p, q, h + 1 / 3);\n                g = hue2rgb(p, q, h);\n                b = hue2rgb(p, q, h - 1 / 3);\n            }\n            return new RGB(r, g, b, this.a);\n        },\n        toHSV: function () {\n            return this.toRGB().toHSV();\n        },\n        toBytes: function () {\n            return this.toRGB().toBytes();\n        }\n    });\n    function hue2rgb(p, q, s) {\n        var t = s;\n        if (t < 0) {\n            t += 1;\n        }\n        if (t > 1) {\n            t -= 1;\n        }\n        if (t < 1 / 6) {\n            return p + (q - p) * 6 * t;\n        }\n        if (t < 1 / 2) {\n            return q;\n        }\n        if (t < 2 / 3) {\n            return p + (q - p) * (2 / 3 - t) * 6;\n        }\n        return p;\n    }\n    function parseColor(value, safe) {\n        var m, ret;\n        if (value == null || value === 'none') {\n            return null;\n        }\n        if (value instanceof BaseColor) {\n            return value;\n        }\n        var color = value.toLowerCase();\n        if (m = matchNamedColor(color)) {\n            if (m[1] === 'transparent') {\n                color = new RGB(1, 1, 1, 0);\n            } else {\n                color = parseColor(namedColors[m[1]], safe);\n            }\n            color.match = [m[1]];\n            return color;\n        }\n        if (m = /^#?([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})\\b/i.exec(color)) {\n            ret = new Bytes(parseInt(m[1], 16), parseInt(m[2], 16), parseInt(m[3], 16), 1);\n        } else if (m = /^#?([0-9a-f])([0-9a-f])([0-9a-f])\\b/i.exec(color)) {\n            ret = new Bytes(parseInt(m[1] + m[1], 16), parseInt(m[2] + m[2], 16), parseInt(m[3] + m[3], 16), 1);\n        } else if (m = /^rgb\\(\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*\\)/.exec(color)) {\n            ret = new Bytes(parseInt(m[1], 10), parseInt(m[2], 10), parseInt(m[3], 10), 1);\n        } else if (m = /^rgba\\(\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*,\\s*([0-9]+)\\s*,\\s*([0-9.]+)\\s*\\)/.exec(color)) {\n            ret = new Bytes(parseInt(m[1], 10), parseInt(m[2], 10), parseInt(m[3], 10), parseFloat(m[4]));\n        } else if (m = /^rgb\\(\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*\\)/.exec(color)) {\n            ret = new RGB(parseFloat(m[1]) / 100, parseFloat(m[2]) / 100, parseFloat(m[3]) / 100, 1);\n        } else if (m = /^rgba\\(\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9]*\\.?[0-9]+)%\\s*,\\s*([0-9.]+)\\s*\\)/.exec(color)) {\n            ret = new RGB(parseFloat(m[1]) / 100, parseFloat(m[2]) / 100, parseFloat(m[3]) / 100, parseFloat(m[4]));\n        }\n        if (ret) {\n            ret.match = m;\n        } else if (!safe) {\n            throw new Error('Cannot parse color: ' + color);\n        }\n        return ret;\n    }\n    var Color = Class.extend({\n        init: function (value) {\n            var this$1 = this;\n            if (arguments.length === 1) {\n                var formats = Color.formats;\n                var resolvedColor = this.resolveColor(value);\n                for (var idx = 0; idx < formats.length; idx++) {\n                    var formatRegex = formats[idx].re;\n                    var processor = formats[idx].process;\n                    var parts = formatRegex.exec(resolvedColor);\n                    if (parts) {\n                        var channels = processor(parts);\n                        this$1.r = channels[0];\n                        this$1.g = channels[1];\n                        this$1.b = channels[2];\n                    }\n                }\n            } else {\n                this.r = arguments[0];\n                this.g = arguments[1];\n                this.b = arguments[2];\n            }\n            this.r = this.normalizeByte(this.r);\n            this.g = this.normalizeByte(this.g);\n            this.b = this.normalizeByte(this.b);\n        },\n        toHex: function () {\n            var pad = this.padDigit;\n            var r = this.r.toString(16);\n            var g = this.g.toString(16);\n            var b = this.b.toString(16);\n            return '#' + pad(r) + pad(g) + pad(b);\n        },\n        resolveColor: function (value) {\n            var color = value || 'black';\n            if (color.charAt(0) === '#') {\n                color = color.substr(1, 6);\n            }\n            color = color.replace(/ /g, '');\n            color = color.toLowerCase();\n            color = Color.namedColors[color] || color;\n            return color;\n        },\n        normalizeByte: function (value) {\n            if (value < 0 || isNaN(value)) {\n                return 0;\n            }\n            return value > 255 ? 255 : value;\n        },\n        padDigit: function (value) {\n            return value.length === 1 ? '0' + value : value;\n        },\n        brightness: function (value) {\n            var round = Math.round;\n            this.r = round(this.normalizeByte(this.r * value));\n            this.g = round(this.normalizeByte(this.g * value));\n            this.b = round(this.normalizeByte(this.b * value));\n            return this;\n        },\n        percBrightness: function () {\n            return Math.sqrt(0.241 * this.r * this.r + 0.691 * this.g * this.g + 0.068 * this.b * this.b);\n        }\n    });\n    Color.fromBytes = function (r, g, b, a) {\n        return new Bytes(r, g, b, a != null ? a : 1);\n    };\n    Color.fromRGB = function (r, g, b, a) {\n        return new RGB(r, g, b, a != null ? a : 1);\n    };\n    Color.fromHSV = function (h, s, v, a) {\n        return new HSV(h, s, v, a != null ? a : 1);\n    };\n    Color.fromHSL = function (h, s, l, a) {\n        return new HSL(h, s, l, a != null ? a : 1);\n    };\n    Color.formats = [\n        {\n            re: /^rgb\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3})\\)$/,\n            process: function (parts) {\n                return [\n                    parseInt(parts[1], 10),\n                    parseInt(parts[2], 10),\n                    parseInt(parts[3], 10)\n                ];\n            }\n        },\n        {\n            re: /^(\\w{2})(\\w{2})(\\w{2})$/,\n            process: function (parts) {\n                return [\n                    parseInt(parts[1], 16),\n                    parseInt(parts[2], 16),\n                    parseInt(parts[3], 16)\n                ];\n            }\n        },\n        {\n            re: /^(\\w{1})(\\w{1})(\\w{1})$/,\n            process: function (parts) {\n                return [\n                    parseInt(parts[1] + parts[1], 16),\n                    parseInt(parts[2] + parts[2], 16),\n                    parseInt(parts[3] + parts[3], 16)\n                ];\n            }\n        }\n    ];\n    Color.namedColors = namedColors;\n    kendo.deepExtend(kendo, {\n        parseColor: parseColor,\n        Color: Color\n    });\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}