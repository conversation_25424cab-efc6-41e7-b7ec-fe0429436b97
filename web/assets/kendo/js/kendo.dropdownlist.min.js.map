{"version": 3, "sources": ["kendo.dropdownlist.js"], "names": ["f", "define", "$", "undefined", "assign", "instance", "fields", "value", "field", "idx", "lastIndex", "length", "normalizeIndex", "index", "sameCharsOnly", "word", "character", "char<PERSON>t", "kendo", "window", "ui", "List", "Select", "support", "activeElement", "_activeElement", "ObservableObject", "data", "keys", "ns", "nsFocusEvent", "DISABLED", "READONLY", "CHANGE", "FOCUSED", "DEFAULT", "STATEDISABLED", "ARIA_DISABLED", "CLICKEVENTS", "HOVEREVENTS", "TABINDEX", "STATE_FILTER", "STATE_ACCEPT", "MSG_INVALID_OPTION_LABEL", "proxy", "DropDownList", "extend", "init", "element", "options", "optionLabel", "text", "disabled", "that", "this", "isArray", "dataSource", "fn", "call", "on", "_focus<PERSON><PERSON><PERSON>", "_focusInputHandler", "_focusInput", "_optionLabel", "_inputTemplate", "_reset", "_prev", "_word", "_wrapper", "_tabindex", "wrapper", "attr", "_span", "_popup", "_mobile", "_dataSource", "_ignoreCase", "_filterHeader", "_aria", "_enable", "_attachFocusHandlers", "_oldIndex", "selectedIndex", "_initialIndex", "requireValueMapper", "_initList", "_cascade", "one", "e", "sender", "listView", "bound", "hasOptionLabel", "_textAccessor", "_optionLabelText", "autoBind", "fetch", "_isSelect", "children", "parents", "is", "enable", "bind", "preventDefault", "notify", "name", "enabled", "delay", "height", "dataTextField", "dataValueField", "cascadeFrom", "cascadeFromField", "cascadeFromParentField", "ignoreCase", "animation", "filter", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virtual", "template", "valueTemplate", "optionLabelTemplate", "groupTemplate", "fixedGroupTemplate", "autoWidth", "popup", "events", "setOptions", "_listOptions", "_accessors", "select", "destroy", "off", "_inputWrapper", "_arrow", "_arrowIcon", "filterInput", "open", "isFiltered", "filters", "visible", "_state", "_allowOpening", "_focusFilter", "_hovered", "_resizeFilterInput", "_focusItem", "_open", "val", "refresh", "_filterSource", "_focusElement", "isInputActive", "caret", "originalPrevent", "_prevent", "css", "focus", "toggle", "_toggle", "current", "candidate", "_focus", "dataItem", "selectedDataItems", "getByUid", "hasClass", "items", "flatView", "_optionLabelDataItem", "loweredText", "toLowerCase", "_select", "_text", "done", "_clearFilter", "_accessor", "trigger", "_request", "_valueSetter", "unbind", "_isFilterEnabled", "_fetchData", "_old", "_valueBeforeCascade", "expr", "prependTo", "list", "html", "_click", "_toggleHover", "angular", "elements", "remove", "isPlainObject", "_assignInstance", "_buildOptions", "optionLabelValue", "_value", "_options", "unifyType", "_customOption", "_custom", "_listBound", "initialIndex", "filtered", "_presetValue", "_renderFooter", "_renderNoData", "_toggleNoData", "_resizePopup", "position", "_makeUnselectable", "_fetch", "_selectValue", "_hideBusy", "_listChange", "_filterPaste", "_search", "_<PERSON>in<PERSON><PERSON><PERSON>", "_focusoutHandler", "addClass", "isIFrame", "self", "top", "clearTimeout", "_typingTimeout", "mobileOS", "ios", "_change", "_blur", "removeClass", "blur", "_wrapperMousedown", "_wrapperClick", "_focused", "_editable", "disable", "readonly", "add", "dropDownWrapper", "removeAttr", "_keydown", "mousedown", "_keypress", "handled", "key", "keyCode", "altKey", "isPopupVisible", "LEFT", "UP", "RIGHT", "DOWN", "ESC", "ENTER", "SPACEBAR", "_move", "HOME", "_firstItem", "END", "_lastItem", "_getElementDataItem", "item", "_matchText", "indexOf", "_shuffleData", "splitIndex", "optionDataItem", "concat", "slice", "_selectNext", "oldFocusedItem", "dataLength", "isInLoop", "_last", "startIndex", "toJSON", "which", "String", "fromCharCode", "charCode", "_popupOpen", "wrap", "closest", "dataItemByIndex", "getElementIndex", "currentTarget", "close", "_userTriggered", "active", "compareElement", "touchEnabled", "touch", "MSPointers", "pointers", "_searchByWord", "_inputValue", "setTimeout", "search", "_get", "found", "isFunction", "jQueryCandidate", "focusFirst", "_resetOptionLabel", "focusLast", "_nextItem", "focusNext", "_prevItem", "focusPrev", "focusedItem", "highlight<PERSON><PERSON><PERSON>", "mapValueTo", "scrollToIndex", "content", "scrollTop", "additionalClass", "_optionID", "keepState", "_dataValue", "_triggerCascade", "root", "eq", "effects", "android", "meego", "wp", "icon", "parent", "placeholder", "title", "role", "aria-haspopup", "aria-expanded", "prepend", "append", "span", "SELECTOR", "find", "<PERSON><PERSON><PERSON><PERSON>", "DOMelement", "style", "cssText", "className", "accesskey", "unselectable", "hide", "_clearSelection", "useWithBlock", "Error", "getElements", "optionLabelText", "get", "_preselect", "setValue", "split", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,sBACH,aACA,wBACA,qBACDD,IACL,WA6hCE,MAvgCC,UAAUE,EAAGC,GA4+BV,QAASC,GAAOC,EAAUC,EAAQC,GAE9B,IADA,GAA4CC,GAAxCC,EAAM,EAAGC,EAAYJ,EAAOK,OAAS,EAClCF,EAAMC,IAAaD,EACtBD,EAAQF,EAAOG,GACTD,IAASH,KACXA,EAASG,OAEbH,EAAWA,EAASG,EAExBH,GAASC,EAAOI,IAAcH,EAElC,QAASK,GAAeC,EAAOF,GAI3B,MAHIE,IAASF,IACTE,GAASF,GAENE,EAEX,QAASC,GAAcC,EAAMC,GACzB,IAAK,GAAIP,GAAM,EAAGA,EAAMM,EAAKJ,OAAQF,IACjC,GAAIM,EAAKE,OAAOR,KAASO,EACrB,OAAO,CAGf,QAAO,EAngCd,GACOE,GAAQC,OAAOD,MAAOE,EAAKF,EAAME,GAAIC,EAAOD,EAAGC,KAAMC,EAASF,EAAGE,OAAQC,EAAUL,EAAMK,QAASC,EAAgBN,EAAMO,eAAgBC,EAAmBR,EAAMS,KAAKD,iBAAkBE,EAAOV,EAAMU,KAAMC,EAAK,qBAAsBC,EAAeD,EAAK,aAAcE,EAAW,WAAYC,EAAW,WAAYC,EAAS,SAAUC,EAAU,kBAAmBC,EAAU,kBAAmBC,EAAgB,mBAAoBC,EAAgB,gBAAiBC,EAAc,QAAUT,EAAK,YAAcA,EAAIU,EAAc,aAAeV,EAAK,cAAgBA,EAAIW,EAAW,WAAYC,EAAe,SAAUC,EAAe,SAAUC,EAA2B,mMAAoMC,EAAQ1C,EAAE0C,MAC/1BC,EAAevB,EAAOwB,QACtBC,KAAM,SAAUC,EAASC,GAAnB,GAGEC,GAAaC,EAAMC,EAFnBC,EAAOC,KACPzC,EAAQoC,GAAWA,EAAQpC,KAE/BwC,GAAKxB,GAAKA,EACVoB,EAAU/C,EAAEqD,QAAQN,IAAaO,WAAYP,GAAYA,EACzD3B,EAAOmC,GAAGV,KAAKW,KAAKL,EAAML,EAASC,GACnCA,EAAUI,EAAKJ,QACfD,EAAUK,EAAKL,QAAQW,GAAG,QAAU9B,EAAIe,EAAMS,EAAKO,cAAeP,IAClEA,EAAKQ,mBAAqB3D,EAAE0C,MAAMS,EAAKS,YAAaT,GACpDA,EAAKH,YAAchD,IACnBmD,EAAKU,eACLV,EAAKW,iBACLX,EAAKY,SACLZ,EAAKa,MAAQ,GACbb,EAAKc,MAAQ,GACbd,EAAKe,WACLf,EAAKgB,YACLhB,EAAKiB,QAAQ3C,KAAKa,EAAUa,EAAKiB,QAAQC,KAAK/B,IAC9Ca,EAAKmB,QACLnB,EAAKoB,SACLpB,EAAKqB,UACLrB,EAAKsB,cACLtB,EAAKuB,cACLvB,EAAKwB,gBACLxB,EAAKyB,QACLzB,EAAKiB,QAAQC,KAAK,YAAa,UAC/BlB,EAAK0B,UACL1B,EAAK2B,uBACL3B,EAAK4B,UAAY5B,EAAK6B,iBAClBrE,IAAUV,IACV8C,EAAQpC,MAAQA,GAEpBwC,EAAK8B,cAAgBlC,EAAQpC,MAC7BwC,EAAK+B,mBAAmB/B,EAAKJ,SAC7BI,EAAKgC,YACLhC,EAAKiC,WACLjC,EAAKkC,IAAI,MAAO,SAAUC,IACjBA,EAAEC,OAAOC,SAASC,SAAWtC,EAAKuC,kBACnCvC,EAAKwC,cAAcxC,EAAKyC,sBAG5B7C,EAAQ8C,SACR1C,EAAKG,WAAWwC,QACT3C,EAAK6B,qBACZ/B,EAAOF,EAAQE,MAAQ,GAClBA,IACDD,EAAcD,EAAQC,YAClBA,GAAiC,IAAlBD,EAAQpC,MACvBsC,EAAOD,EACAG,EAAK4C,YACZ9C,EAAOH,EAAQkD,SAAS,aAAa/C,SAG7CE,EAAKwC,cAAc1C,IAEvBC,EAAWlD,EAAEmD,EAAKL,SAASmD,QAAQ,YAAYC,GAAG,aAC9ChD,GACAC,EAAKgD,QAAO,GAEhBhD,EAAKqC,SAASY,KAAK,QAAS,SAAUd,GAClCA,EAAEe,mBAENrF,EAAMsF,OAAOnD,IAEjBJ,SACIwD,KAAM,eACNC,SAAS,EACTX,UAAU,EACVlF,MAAO,EACPsC,KAAM,KACN5C,MAAO,KACPoG,MAAO,IACPC,OAAQ,IACRC,cAAe,GACfC,eAAgB,GAChB5D,YAAa,GACb6D,YAAa,GACbC,iBAAkB,GAClBC,uBAAwB,GACxBC,YAAY,EACZC,aACAC,OAAQ,OACRC,UAAW,EACXC,kBAAkB,EAClBC,SAAS,EACTC,SAAU,KACVC,cAAe,KACfC,oBAAqB,KACrBC,cAAe,UACfC,mBAAoB,UACpBC,WAAW,EACXC,MAAO,MAEXC,QACI,OACA,QACA9F,EACA,SACA,YACA,cACA,YACA,UACA,OAEJ+F,WAAY,SAAU/E,GAClB3B,EAAOmC,GAAGuE,WAAWtE,KAAKJ,KAAML,GAChCK,KAAKoC,SAASsC,WAAW1E,KAAK2E,aAAahF,IAC3CK,KAAKS,eACLT,KAAKU,iBACLV,KAAK4E,aACL5E,KAAKuB,gBACLvB,KAAKyB,UACLzB,KAAKwB,SACAxB,KAAK/C,SAAW+C,KAAKsC,kBACtBtC,KAAK6E,OAAO,IAGpBC,QAAS,WACL,GAAI/E,GAAOC,IACXhC,GAAOmC,GAAG2E,QAAQ1E,KAAKL,GACvBA,EAAKiB,QAAQ+D,IAAIxG,GACjBwB,EAAKiB,QAAQ+D,IAAIvG,GACjBuB,EAAKL,QAAQqF,IAAIxG,GACjBwB,EAAKiF,cAAcD,IAAIxG,GACvBwB,EAAKkF,OAAOF,MACZhF,EAAKkF,OAAS,KACdlF,EAAKmF,WAAa,KAClBnF,EAAKH,YAAYmF,MACbhF,EAAKoF,aACLpF,EAAKoF,YAAYJ,IAAIvG,IAG7B4G,KAAM,WAAA,GACErF,GAAOC,KACPqF,IAAatF,EAAKG,WAAW4D,UAAW/D,EAAKG,WAAW4D,SAASwB,QAAQjI,OAAS,CAClF0C,GAAKyE,MAAMe,YAGVxF,EAAKqC,SAASC,SAAWtC,EAAKyF,SAAWpG,EAenCW,EAAK0F,kBACZ1F,EAAK2F,cAAe,EACpB3F,EAAKyE,MAAMvC,IAAI,WAAYlC,EAAKQ,oBAChCR,EAAKyE,MAAMmB,UAAW,EACtB5F,EAAKyE,MAAMY,OACXrF,EAAK6F,qBACL7F,EAAK8F,eApBL9F,EAAK+F,OAAQ,EACb/F,EAAKyF,OAAS,SACVzF,EAAKoF,cACLpF,EAAKoF,YAAYY,IAAI,IACrBhG,EAAKa,MAAQ,IAEbb,EAAKoF,aAA0C,IAA3BpF,EAAKJ,QAAQoE,YAAoBsB,GACrDtF,EAAKiG,UACLjG,EAAKyE,MAAMvC,IAAI,WAAYlC,EAAKQ,oBAChCR,EAAKyE,MAAMY,OACXrF,EAAK6F,sBAEL7F,EAAKkG,mBAWjBzF,YAAa,WACTR,KAAKkG,cAAclG,KAAKmF,cAE5BS,mBAAoB,WAAA,GAMZO,GACAC,EANAjB,EAAcnF,KAAKmF,YACnBkB,EAAkBrG,KAAKsG,QACtBnB,KAGDgB,EAAgBnG,KAAKmF,YAAY,KAAOjH,IACxCkI,EAAQxI,EAAMwI,MAAMpG,KAAKmF,YAAY,IAAI,GAC7CnF,KAAKsG,UAAW,EAChBnB,EAAYoB,IAAI,UAAW,QAAQA,IAAI,QAASvG,KAAKwE,MAAM9E,QAAQ6G,IAAI,UAAUA,IAAI,UAAW,gBAC5FJ,IACAhB,EAAYqB,QACZ5I,EAAMwI,MAAMjB,EAAY,GAAIiB,IAEhCpG,KAAKsG,SAAWD,IAEpBZ,cAAe,WACX,MAAOzF,MAAKsC,kBAAoBtC,KAAKmF,aAAenH,EAAOmC,GAAGsF,cAAcrF,KAAKJ,OAErFyG,OAAQ,SAAUA,GACdzG,KAAK0G,QAAQD,GAAQ,IAEzBE,QAAS,SAAUC,GACf,GAAID,EACJ,OAAIC,KAAc/J,GACd8J,EAAU3G,KAAKoC,SAASoE,SACnBG,GAAkC,IAAvB3G,KAAK4B,eAAuB5B,KAAKsC,iBACtCtC,KAAKJ,YAET+G,IAEX3G,KAAK6G,OAAOD,GAAZ5G,IAEJ8G,SAAU,SAAUvJ,GAAV,GACFwC,GAAOC,KACP8G,EAAW,IACf,IAAc,OAAVvJ,EACA,MAAOA,EAEX,IAAIA,IAAUV,EACViK,EAAW/G,EAAKqC,SAAS2E,oBAAoB,OAC1C,CACH,GAAqB,gBAAVxJ,GAAoB,CAC3B,GAAIwC,EAAKJ,QAAQsE,QACb,MAAOlE,GAAKG,WAAW8G,SAASpK,EAAEW,GAAOc,KAAK,OAG9Cd,GADAA,EAAM0J,SAAS,yBAGPrK,EAAEmD,EAAKmH,SAAS3J,MAAMA,OAE3BwC,GAAKuC,mBACZ/E,GAAS,EAEbuJ,GAAW/G,EAAKG,WAAWiH,WAAW5J,GAK1C,MAHKuJ,KACDA,EAAW/G,EAAKqH,wBAEbN,GAEXd,QAAS,WACLhG,KAAKoC,SAAS4D,WAElBnG,KAAM,SAAUA,GAAV,GAEEwH,GADAtH,EAAOC,KAEP4D,EAAa7D,EAAKJ,QAAQiE,UAE9B,OADA/D,GAAgB,OAATA,EAAgB,GAAKA,EACxBA,IAAShD,EAgBFkD,EAAKwC,gBAfQ,gBAAT1C,IACPE,EAAKwC,cAAc1C,GACnB,IAEJwH,EAAczD,EAAa/D,EAAKyH,cAAgBzH,EAChDE,EAAKwH,QAAQ,SAAUlJ,GAKnB,MAJAA,GAAO0B,EAAKyH,MAAMnJ,GACduF,IACAvF,GAAQA,EAAO,IAAIiJ,eAEhBjJ,IAASgJ,IACjBI,KAAK,WACJ1H,EAAKwC,cAAcxC,EAAK+G,YAAcjH,KAR1CwH,IAcRK,aAAc,WACV9K,EAAEoD,KAAKmF,aAAaY,IAAI,IACxB/H,EAAOmC,GAAGuH,aAAatH,KAAKJ,OAEhC/C,MAAO,SAAUA,GAAV,GACC8C,GAAOC,KACPoC,EAAWrC,EAAKqC,SAChBlC,EAAaH,EAAKG,UACtB,OAAIjD,KAAUJ,GACVI,EAAQ8C,EAAK4H,aAAe5H,EAAKqC,SAASnF,QAAQ,GAC3CA,IAAUJ,GAAuB,OAAVI,EAAiB,GAAKA,IAExD8C,EAAK+B,mBAAmB/B,EAAKJ,QAAS1C,IAClCA,GAAU8C,EAAKuC,mBACfvC,EAAK8B,cAAgB,MAEzB7B,KAAK4H,QAAQ,OAAS3K,MAAOA,IACzB8C,EAAK8H,UAAY9H,EAAKJ,QAAQ8D,aAAe1D,EAAKqC,SAASC,SACvDtC,EAAK+H,cACL5H,EAAW6H,OAAOpJ,EAAQoB,EAAK+H,cAEnC/H,EAAK+H,aAAexI,EAAM,WACtBS,EAAK9C,MAAMA,IACZ8C,GACHG,EAAW+B,IAAItD,EAAQoB,EAAK+H,cAC5B,IAEA/H,EAAKiI,oBAAsB5F,EAASC,SAAWD,EAASiD,aACxDtF,EAAK2H,eAEL3H,EAAKkI,aAET7F,EAASnF,MAAMA,GAAOwK,KAAK,WACvB1H,EAAKmI,KAAOnI,EAAKoI,oBAAsBpI,EAAK4H,YAC5C5H,EAAK4B,UAAY5B,EAAK6B,gBAP1B,KAUJU,eAAgB,WACZ,MAAOtC,MAAKJ,eAAiBI,KAAKJ,YAAY,IAElDa,aAAc,WAAA,GACNV,GAAOC,KACPL,EAAUI,EAAKJ,QACfC,EAAcD,EAAQC,YACtBsE,EAAWvE,EAAQyE,mBACvB,OAAKxE,IAKAsE,IACDA,EAAW,KAEPA,GADuB,gBAAhBtE,GACK,OAEAhC,EAAMwK,KAAKzI,EAAQ4D,cAAe,QAElDW,GAAY,KAEQ,kBAAbA,KACPA,EAAWtG,EAAMsG,SAASA,IAE9BnE,EAAKqE,oBAAsBF,EACtBnE,EAAKuC,mBACNvC,EAAKH,YAAchD,EAAE,0CAA0CyL,UAAUtI,EAAKuI,OAElFvI,EAAKH,YAAY2I,KAAKrE,EAAStE,IAAcmF,MAAM1E,GAAGrB,EAAaM,EAAMS,EAAKyI,OAAQzI,IAAOM,GAAGpB,EAAac,EAAK0I,cAClH1I,EAAK2I,QAAQ,UAAW,WACpB,OACIC,SAAU5I,EAAKH,YACfvB,OAASyI,SAAU/G,EAAKqH,4BApBhC,IAJIrH,EAAKH,YAAYmF,MAAM6D,SACvB7I,EAAKH,YAAchD,IACnB,IA0BR4F,iBAAkB,WACd,GAAI5C,GAAcI,KAAKL,QAAQC,WAC/B,OAA8B,gBAAhBA,GAA2BA,EAAcI,KAAKwH,MAAM5H,IAEtEwH,qBAAsB,WAAA,GACdrH,GAAOC,KACPJ,EAAcG,EAAKJ,QAAQC,WAC/B,OAAIG,GAAKuC,iBACE1F,EAAEiM,cAAcjJ,GAAe,GAAIxB,GAAiBwB,GAAeG,EAAK+I,gBAAgB/I,EAAKyC,mBAAoB,IAErH3F,GAEXkM,cAAe,SAAU1K,GAAV,GAKPpB,GACA2C,EACAoJ,EANAjJ,EAAOC,IACND,GAAK4C,YAGN1F,EAAQ8C,EAAKqC,SAASnF,QAAQ,GAC9B2C,EAAcG,EAAKqH,uBACnB4B,EAAmBpJ,GAAeG,EAAKkJ,OAAOrJ,GAC9C3C,IAAUJ,GAAuB,OAAVI,IACvBA,EAAQ,IAER2C,IACIoJ,IAAqBnM,GAAkC,OAArBmM,IAClCA,EAAmB,IAEvBpJ,EAAc,kBAAoBoJ,EAAmB,KAAOjJ,EAAKyH,MAAM5H,GAAe,aAE1FG,EAAKmJ,SAAS7K,EAAMuB,EAAa3C,GAC7BA,IAAUc,EAAKoL,UAAUpJ,EAAK4H,kBAAoB1K,MAClD8C,EAAKqJ,cAAgB,KACrBrJ,EAAKsJ,QAAQpM,MAGrBqM,WAAY,WAAA,GAKJxC,GAJA/G,EAAOC,KACPuJ,EAAexJ,EAAK8B,cACpB2H,EAAWzJ,EAAKyF,SAAWrG,EAC3Bd,EAAO0B,EAAKG,WAAWiH,UAE3BpH,GAAK0J,cAAe,EACpB1J,EAAK2J,gBACL3J,EAAK4J,gBACL5J,EAAK6J,eAAevL,EAAKhB,QACzB0C,EAAK8J,cAAa,GAClB9J,EAAKyE,MAAMsF,WACX/J,EAAKgJ,cAAc1K,GACnB0B,EAAKgK,oBACAP,IACGzJ,EAAK+F,OACL/F,EAAK0G,OAAO1G,EAAK0F,iBAErB1F,EAAK+F,OAAQ,EACR/F,EAAKiK,SACF3L,EAAKhB,SACA0C,EAAKqC,SAASnF,QAAQI,QAAUkM,MAAsC,OAAjBA,GACtDxJ,EAAK8E,OAAO0E,GAEhBxJ,EAAK8B,cAAgB,KACrBiF,EAAW/G,EAAKqC,SAAS2E,oBAAoB,GACzCD,GAAY/G,EAAKF,SAAWE,EAAKyH,MAAMV,IACvC/G,EAAKkK,aAAanD,IAEf/G,EAAKwC,kBAAoBxC,EAAKyC,qBACrCzC,EAAKqC,SAASnF,MAAM,IACpB8C,EAAKkK,aAAa,MAClBlK,EAAK4B,UAAY5B,EAAK6B,iBAIlC7B,EAAKmK,YACLnK,EAAK6H,QAAQ,cAEjBuC,YAAa,WACTnK,KAAKiK,aAAajK,KAAKoC,SAAS2E,oBAAoB,KAChD/G,KAAKyJ,cAAgBzJ,KAAKkI,MAAQlI,KAAK2B,kBACvC3B,KAAK2B,UAAY3B,KAAK4B,gBAG9BwI,aAAc,WACVpK,KAAKqK,WAET3I,qBAAsB,WAAA,GACd3B,GAAOC,KACPgB,EAAUjB,EAAKiB,OACnBA,GAAQX,GAAG,UAAY7B,EAAcc,EAAMS,EAAKuK,gBAAiBvK,IAAOM,GAAG,WAAa7B,EAAcc,EAAMS,EAAKwK,iBAAkBxK,IAC/HA,EAAKoF,aACLpF,EAAKoF,YAAY9E,GAAG,UAAY7B,EAAcc,EAAMS,EAAKuK,gBAAiBvK,IAAOM,GAAG,WAAa7B,EAAcc,EAAMS,EAAKwK,iBAAkBxK,KAGpJO,cAAe,WACXN,KAAKgB,QAAQwF,SAEjB8D,gBAAiB,WACbtK,KAAKgF,cAAcwF,SAAS5L,GAC5BoB,KAAKsG,UAAW,GAEpBiE,iBAAkB,WAAA,GACVxK,GAAOC,KACPyK,EAAW5M,OAAO6M,OAAS7M,OAAO8M,GACjC5K,GAAKuG,WACNsE,aAAa7K,EAAK8K,gBACd5M,EAAQ6M,SAASC,KAAON,EACxB1K,EAAKiL,UAELjL,EAAKkL,QAETlL,EAAKiF,cAAckG,YAAYtM,GAC/BmB,EAAKuG,UAAW,EAChBvG,EAAK+F,OAAQ,EACb/F,EAAKL,QAAQyL,SAGrBC,kBAAmB,WACfpL,KAAKsG,WAAatG,KAAKmF,aAE3BkG,cAAe,SAAUnJ,GACrBA,EAAEe,iBACFjD,KAAKwE,MAAMuD,OAAO,WAAY/H,KAAKO,oBACnCP,KAAKsL,SAAWtL,KAAKgB,QACrBhB,KAAKsG,UAAW,EAChBtG,KAAK0G,WAET6E,UAAW,SAAU5L,GAAV,GACHI,GAAOC,KACPN,EAAUK,EAAKL,QACf8L,EAAU7L,EAAQ6L,QAClBC,EAAW9L,EAAQ8L,SACnBzK,EAAUjB,EAAKiB,QAAQ0K,IAAI3L,EAAKoF,aAAaJ,IAAIxG,GACjDoN,EAAkB5L,EAAKiF,cAAcD,IAAI9F,EACxCwM,IAAaD,EAUPA,GACPxK,EAAQ4K,WAAW1M,GACnByM,EAAgBnB,SAAS1L,GAAeoM,YAAYrM,IAEpD8M,EAAgBnB,SAAS3L,GAASqM,YAAYpM,IAb9CY,EAAQkM,WAAWnN,GAAUmN,WAAWlN,GACxCiN,EAAgBnB,SAAS3L,GAASqM,YAAYpM,GAAeuB,GAAGpB,EAAac,EAAK0I,cAClFzH,EAAQC,KAAK/B,EAAU8B,EAAQ3C,KAAKa,IAAW+B,KAAKlC,GAAe,GAAOsB,GAAG,UAAY9B,EAAIe,EAAMS,EAAK8L,SAAU9L,IAAOM,GAAGzC,EAAMK,QAAQ6N,UAAYvN,EAAIe,EAAMS,EAAKqL,kBAAmBrL,IAAOM,GAAG,QAAU9B,EAAIe,EAAMS,EAAKqK,aAAcrK,IACzOA,EAAKiB,QAAQX,GAAG,QAAU9B,EAAIe,EAAMS,EAAKsL,cAAetL,IACnDA,EAAKoF,YAGNnE,EAAQX,GAAG,QAAU9B,EAAIe,EAAMS,EAAKsK,QAAStK,IAF7CiB,EAAQX,GAAG,WAAa9B,EAAIe,EAAMS,EAAKgM,UAAWhM,KAU1DL,EAAQuB,KAAKxC,EAAU+M,GAASvK,KAAKvC,EAAU+M,GAC/CzK,EAAQC,KAAKlC,EAAeyM,IAEhCK,SAAU,SAAU3J,GAAV,GAIFiE,GACA6F,EAqCIrF,EAzCJ5G,EAAOC,KACPiM,EAAM/J,EAAEgK,QACRC,EAASjK,EAAEiK,OAGXC,EAAiBrM,EAAKyE,MAAMe,SAWhC,IAVIxF,EAAKoF,cACLgB,EAAgBpG,EAAKoF,YAAY,KAAOjH,KAExC+N,IAAQ3N,EAAK+N,MACbJ,EAAM3N,EAAKgO,GACXN,GAAU,GACHC,IAAQ3N,EAAKiO,QACpBN,EAAM3N,EAAKkO,KACXR,GAAU,IAEVA,IAAW7F,EAAf,CAYA,GATAjE,EAAEgK,QAAUD,GACRE,GAAUF,IAAQ3N,EAAKgO,IAAML,IAAQ3N,EAAKmO,MAC1C1M,EAAKmG,cAAcnG,EAAKiB,SAExBjB,EAAKyF,SAAWrG,GAAgB8M,IAAQ3N,EAAKmO,MAC7C1M,EAAK2H,eACL3H,EAAK+F,OAAQ,EACb/F,EAAKyF,OAASpG,GAEd6M,IAAQ3N,EAAKoO,OAAS3M,EAAK8K,gBAAkB9K,EAAKoF,aAAeiH,EAEjE,MADAlK,GAAEe,iBACF,CAOJ,IALIgJ,IAAQ3N,EAAKqO,UAAaxG,IAC1BpG,EAAK0G,QAAQ2F,GACblK,EAAEe,kBAEN+I,EAAUjM,EAAK6M,MAAM1K,IACjB8J,EAAJ,CAGA,KAAKI,IAAmBrM,EAAKoF,eACrBwB,EAAU5G,EAAK8G,SACfoF,IAAQ3N,EAAKuO,MACbb,GAAU,EACVjM,EAAK+M,cACEb,IAAQ3N,EAAKyO,MACpBf,GAAU,EACVjM,EAAKiN,aAELhB,GAAS,CACT,GAAIjM,EAAK6H,QAAQ,UACTd,SAAU/G,EAAKkN,oBAAoBlN,EAAK8G,UACxCqG,KAAMnN,EAAK8G,WAGf,MADA9G,GAAK8G,OAAOF,GACZ,CAEJ5G,GAAKwH,QAAQxH,EAAK8G,UAAU,GAAMY,KAAK,WAC9B2E,GACDrM,EAAKkL,UAGb/I,EAAEe,iBAGLkJ,GAAWH,IAAWjM,EAAKoF,aAC5BpF,EAAKsK,aAGb8C,WAAY,SAAUtN,EAAMpC,GACxB,GAAImG,GAAa5D,KAAKL,QAAQiE,UAC9B,OAAI/D,KAAShD,GAAsB,OAATgD,IAG1BA,GAAc,GACV+D,IACA/D,EAAOA,EAAKyH,eAEc,IAAvBzH,EAAKuN,QAAQ3P,KAExB4P,aAAc,SAAUhP,EAAMiP,GAC1B,GAAIC,GAAiBvN,KAAKoH,sBAI1B,OAHImG,KACAlP,GAAQkP,GAAgBC,OAAOnP,IAE5BA,EAAKoP,MAAMH,GAAYE,OAAOnP,EAAKoP,MAAM,EAAGH,KAEvDI,YAAa,WAAA,GAMLC,GACA9N,EASK1C,EAfL4C,EAAOC,KACP3B,EAAO0B,EAAKG,WAAWiH,WACvByG,EAAavP,EAAKhB,QAAU0C,EAAKuC,iBAAmB,EAAI,GACxDuL,EAAWrQ,EAAcuC,EAAKc,MAAOd,EAAK+N,OAC1CC,EAAahO,EAAK6B,aAWtB,KARImM,OACAA,EAAa,GAEbA,GAAcF,EAAW,EAAI,EAC7BE,EAAazQ,EAAeyQ,EAAYH,IAE5CvP,EAAOA,EAAK2P,OAAS3P,EAAK2P,SAAW3P,EAAKoP,QAC1CpP,EAAO0B,EAAKsN,aAAahP,EAAM0P,GACtB5Q,EAAM,EAAGA,EAAMyQ,IACpB/N,EAAOE,EAAKyH,MAAMnJ,EAAKlB,KACnB0Q,IAAY9N,EAAKoN,WAAWtN,EAAME,EAAK+N,UAEhC/N,EAAKoN,WAAWtN,EAAME,EAAKc,OAJN1D,KAQhCA,IAAQyQ,IACRD,EAAiB5N,EAAK8G,SACtB9G,EAAKwH,QAAQjK,EAAeyQ,EAAa5Q,EAAKyQ,IAAanG,KAAK,WAC5D,GAAIA,GAAO,WACF1H,EAAKyE,MAAMe,WACZxF,EAAKiL,UAGTjL,GAAK6H,QAAQ,UACTd,SAAU/G,EAAKkN,oBAAoBlN,EAAK8G,UACxCqG,KAAMnN,EAAK8G,WAEf9G,EAAKwH,QAAQoG,GAAgBlG,KAAKA,GAElCA,QAKhBsE,UAAW,SAAU7J,GAAV,GAKHxE,GAJAqC,EAAOC,IACK,KAAZkC,EAAE+L,OAAe/L,EAAEgK,UAAYtO,EAAMU,KAAKoO,QAG1ChP,EAAYwQ,OAAOC,aAAajM,EAAEkM,UAAYlM,EAAEgK,SAChDnM,EAAKJ,QAAQiE,aACblG,EAAYA,EAAU4J,eAER,MAAd5J,GACAwE,EAAEe,iBAENlD,EAAKc,OAASnD,EACdqC,EAAK+N,MAAQpQ,EACbqC,EAAKsK,YAETgE,WAAY,WACR,GAAI7J,GAAQxE,KAAKwE,KACjBA,GAAMxD,QAAUpD,EAAM0Q,KAAK9J,EAAM9E,SAC7B8E,EAAM9E,QAAQ6O,QAAQ,YAAY,KAClC/J,EAAMxD,QAAQwJ,SAAS,sBACvBxK,KAAKgB,QAAQwJ,SAAS,eAG9BrJ,OAAQ,WACJnD,EAAOmC,GAAGgB,OAAOf,KAAKJ,MACtBA,KAAKwE,MAAMvC,IAAI,OAAQ3C,EAAMU,KAAKqO,WAAYrO,QAElDiN,oBAAqB,SAAUvN,GAC3B,MAAKA,IAAYA,EAAQ,GAGrBA,EAAQ,KAAOM,KAAKJ,YAAY,GACzBI,KAAKoH,uBAETpH,KAAKoC,SAASoM,gBAAgBxO,KAAKoC,SAASqM,gBAAgB/O,IALxD,MAOf8I,OAAQ,SAAUtG,GAAV,GACAnC,GAAOC,KACPkN,EAAOhL,EAAEgL,MAAQtQ,EAAEsF,EAAEwM,cAEzB,OADAxM,GAAEe,iBACElD,EAAK6H,QAAQ,UACTd,SAAU/G,EAAKkN,oBAAoBC,GACnCA,KAAMA,KAEVnN,EAAK4O,QACL,IAEJ5O,EAAK6O,gBAAiB,EACtB7O,EAAKwH,QAAQ2F,GAAMzF,KAAK,WACpB1H,EAAKmG,cAAcnG,EAAKiB,SACxBjB,EAAKkL,UAHTlL,IAMJmG,cAAe,SAAUxG,GAAV,GACPmP,GAAS3Q,IACT8C,EAAUhB,KAAKgB,QACfmE,EAAcnF,KAAKmF,YACnB2J,EAAiBpP,IAAYyF,EAAcnE,EAAUmE,EACrD4J,EAAe9Q,EAAQ6M,WAAa7M,EAAQ+Q,OAAS/Q,EAAQgR,YAAchR,EAAQiR,SACnF/J,IAAeA,EAAY,KAAOzF,EAAQ,IAAMqP,GAGhD5J,IAAgB2J,EAAe,KAAOD,GAAU7O,KAAK0F,gBACrD1F,KAAK0F,cAAe,EACpB1F,KAAKsG,UAAW,EAChBtG,KAAKsL,SAAW5L,EAAQ8G,UAGhC2I,cAAe,SAAU1R,GAAV,GAIPsC,GACA6D,CAJCnG,KAGDsC,EAAOC,KACP4D,EAAa7D,EAAKJ,QAAQiE,WAC1BA,IACAnG,EAAOA,EAAK6J,eAEhBvH,EAAKwH,QAAQ,SAAUT,GACnB,MAAO/G,GAAKoN,WAAWpN,EAAKyH,MAAMV,GAAWrJ,OAGrD2R,YAAa,WACT,MAAOpP,MAAKH,QAEhBwK,QAAS,WAAA,GACDtK,GAAOC,KACPE,EAAaH,EAAKG,UAEtB,IADA0K,aAAa7K,EAAK8K,gBACd9K,EAAKiI,mBACLjI,EAAK8K,eAAiBwE,WAAW,WAC7B,GAAIpS,GAAQ8C,EAAKoF,YAAYY,KACzBhG,GAAKa,QAAU3D,IACf8C,EAAKa,MAAQ3D,EACb8C,EAAKuP,OAAOrS,GACZ8C,EAAK6F,sBAET7F,EAAK8K,eAAiB,MACvB9K,EAAKJ,QAAQ0D,WACb,CAIH,GAHAtD,EAAK8K,eAAiBwE,WAAW,WAC7BtP,EAAKc,MAAQ,IACdd,EAAKJ,QAAQ0D,QACXtD,EAAKqC,SAASC,QAIf,MAHAnC,GAAWwC,QAAQ+E,KAAK,WACpB1H,EAAK2N,gBAET,CAEJ3N,GAAK2N,gBAGb6B,KAAM,SAAU3I,GAAV,GACEvI,GAAMmR,EAAOrS,EACbsS,EAAkC,kBAAd7I,GACpB8I,EAAmBD,EAA4B7S,IAAfA,EAAEgK,EAUtC,IATI5G,KAAKsC,mBACoB,gBAAdsE,GACHA,OACAA,GAAa,GAEV8I,EAAgBzI,SAAS,wBAChCL,OAGJ6I,EAAY,CAEZ,IADApR,EAAO2B,KAAKE,WAAWiH,WAClBhK,EAAM,EAAGA,EAAMkB,EAAKhB,OAAQF,IAC7B,GAAIyJ,EAAUvI,EAAKlB,IAAO,CACtByJ,EAAYzJ,EACZqS,GAAQ,CACR,OAGHA,IACD5I,MAGR,MAAOA,IAEXkG,WAAY,WACJ9M,KAAKsC,iBACLtC,KAAK6G,OAAO7G,KAAKJ,aAEjBI,KAAKoC,SAASuN,cAGtB3C,UAAW,WACPhN,KAAK4P,oBACL5P,KAAKoC,SAASyN,aAElBC,UAAW,WACH9P,KAAKJ,YAAYqH,SAAS,oBAC1BjH,KAAK4P,oBACL5P,KAAKoC,SAASuN,cAEd3P,KAAKoC,SAAS2N,aAGtBC,UAAW,WACHhQ,KAAKJ,YAAYqH,SAAS,qBAG9BjH,KAAKoC,SAAS6N,YACTjQ,KAAKoC,SAASoE,SACfxG,KAAK6G,OAAO7G,KAAKJ,eAGzBiG,WAAY,WAAA,GACJlG,GAAUK,KAAKL,QACfyC,EAAWpC,KAAKoC,SAChB8N,EAAc9N,EAASoE,QACvBjJ,EAAQ6E,EAASyC,QACrBtH,GAAQA,EAAMA,EAAMF,OAAS,GACzBE,IAAUV,GAAa8C,EAAQwQ,iBAAmBD,IAClD3S,EAAQ,GAERA,IAAUV,EACVuF,EAASoE,MAAMjJ,IAEXoC,EAAQC,aAAiBD,EAAQsE,SAA0C,aAA/BtE,EAAQsE,QAAQmM,WAK5DhO,EAASiO,cAAc,IAJvBrQ,KAAK6G,OAAO7G,KAAKJ,aACjBI,KAAKuH,QAAQvH,KAAKJ,aAClBI,KAAKoC,SAASkO,QAAQC,UAAU,KAM5CX,kBAAmB,SAAUY,GACzBxQ,KAAKJ,YAAYsL,YAAY,mBAAqBsF,GAAmB,KAAK5E,WAAW,OAEzF/E,OAAQ,SAAUD,GAAV,GACAxE,GAAWpC,KAAKoC,SAChBxC,EAAcI,KAAKJ,WACvB,OAAIgH,KAAc/J,GACd+J,EAAYxE,EAASoE,SAChBI,GAAahH,EAAYqH,SAAS,qBACnCL,EAAYhH,GAETgH,IAEX5G,KAAK4P,oBACLhJ,EAAY5G,KAAKuP,KAAK3I,GACtBxE,EAASoE,MAAMI,GACXA,SACAhH,EAAY4K,SAAS,mBAAmBvJ,KAAK,KAAMmB,EAASqO,WAC5DzQ,KAAKsL,SAASI,IAAI1L,KAAKmF,aAAayG,WAAW,yBAAyB3K,KAAK,wBAAyBmB,EAASqO,YALnHzQ,IAQJuH,QAAS,SAAUX,EAAW8J,GAC1B,GAAI3Q,GAAOC,IAEX,OADA4G,GAAY7G,EAAKwP,KAAK3I,GACf7G,EAAKqC,SAASyC,OAAO+B,GAAWa,KAAK,WACnCiJ,GAAa3Q,EAAKyF,SAAWrG,IAC9BY,EAAKyF,OAASpG,GAEdwH,QACA7G,EAAKkK,aAAa,SAI9BA,aAAc,SAAUnD,GAAV,GACN/G,GAAOC,KACPJ,EAAcG,EAAKJ,QAAQC,YAC3BzC,EAAM4C,EAAKqC,SAASyC,SACpB5H,EAAQ,GACR4C,EAAO,EACX1C,GAAMA,EAAIA,EAAIE,OAAS,GACnBF,IAAQN,IACRM,MAEJ6C,KAAK4P,kBAAkB,qBACnB9I,GAAyB,IAAbA,GACZjH,EAAOiH,EACP7J,EAAQ8C,EAAK4Q,WAAW7J,GACpBlH,IACAzC,GAAO,IAEJyC,IACPG,EAAK8G,OAAO9G,EAAKH,YAAY4K,SAAS,qBACtC3K,EAAOE,EAAKyC,mBAERvF,EADuB,gBAAhB2C,GACC,GAEAG,EAAKkJ,OAAOrJ,GAExBzC,EAAM,GAEV4C,EAAK6B,cAAgBzE,EACP,OAAVF,IACAA,EAAQ,IAEZ8C,EAAKwC,cAAc1C,GACnBE,EAAK4H,UAAU1K,EAAOE,GACtB4C,EAAK6Q,mBAETxP,QAAS,WACL,GAAIrB,GAAOC,KAAMwE,EAAQzE,EAAKyE,MAAOsG,EAAW7M,EAAQ6M,SAAU+F,EAAOrM,EAAM9E,QAAQmD,QAAQ,YAAYiO,GAAG,EAC1GD,GAAKxT,QAAUyN,IACftG,EAAM7E,QAAQkE,UAAUuB,KAAK2L,QAAUjG,EAASkG,SAAWlG,EAASmG,MAAQ,SAAWnG,EAASC,KAAOD,EAASoG,GAAK,aAAe1M,EAAM7E,QAAQkE,UAAUuB,KAAK2L,UAGzKxP,cAAe,WACX,GAAI4P,EACAnR,MAAKmF,cACLnF,KAAKmF,YAAYJ,IAAIxG,GAAI6S,SAASxI,SAClC5I,KAAKmF,YAAc,MAEnBnF,KAAKgI,qBACLmJ,EAAO,wCACPnR,KAAKmF,YAAcvI,EAAE,8BAA8BqE,MAC/CoQ,YAAarR,KAAKN,QAAQuB,KAAK,eAC/BqQ,MAAOtR,KAAKN,QAAQuB,KAAK,SACzBsQ,KAAM,UACNC,iBAAiB,EACjBC,iBAAiB,IAErBzR,KAAKsI,KAAKoJ,QAAQ9U,EAAE,kCAAkC+U,OAAO3R,KAAKmF,YAAYuG,IAAIyF,OAG1FjQ,MAAO,WACH,GAAoE0Q,GAAhE7R,EAAOC,KAAMgB,EAAUjB,EAAKiB,QAAS6Q,EAAW,cACpDD,GAAO5Q,EAAQ8Q,KAAKD,GACfD,EAAK,KACN5Q,EAAQ2Q,OAAO,kPAAkPA,OAAO5R,EAAKL,SAC7QkS,EAAO5Q,EAAQ8Q,KAAKD,IAExB9R,EAAK6R,KAAOA,EACZ7R,EAAKiF,cAAgBpI,EAAEoE,EAAQ,GAAG+Q,YAClChS,EAAKkF,OAASjE,EAAQ8Q,KAAK,aAC3B/R,EAAKmF,WAAanF,EAAKkF,OAAO6M,KAAK,YAEvChR,SAAU,WACN,GAAkEE,GAA9DjB,EAAOC,KAAMN,EAAUK,EAAKL,QAASsS,EAAatS,EAAQ,EAC9DsB,GAAUtB,EAAQ0R,SACbpQ,EAAQ8B,GAAG,mBACZ9B,EAAUtB,EAAQ4O,KAAK,YAAY8C,SACnCpQ,EAAQ,GAAGiR,MAAMC,QAAUF,EAAWC,MAAMC,QAC5ClR,EAAQ,GAAGsQ,MAAQU,EAAWV,OAElCvR,EAAKuL,SAAWvL,EAAKiB,QAAUA,EAAQwJ,SAAS,uBAAuBA,SAASwH,EAAWG,WAAW5L,IAAI,UAAW,IAAItF,MACrHmR,UAAW1S,EAAQuB,KAAK,aACxBoR,aAAc,KACdd,KAAM,UACNC,iBAAiB,EACjBC,iBAAiB,IAErB/R,EAAQ4S,OAAO1G,WAAW,cAE9B2G,gBAAiB,SAAUnB,GACvBpR,KAAK6E,OAAOuM,EAAOnU,QAAU,OAEjCyD,eAAgB,WACZ,GAAIX,GAAOC,KAAMkE,EAAWnE,EAAKJ,QAAQwE,aAOzC,IAHID,EAHCA,EAGUtG,EAAMsG,SAASA,GAFftH,EAAE0C,MAAM1B,EAAMsG,SAAS,uBAAyBsO,cAAc,IAAUzS,GAIvFA,EAAKoE,cAAgBD,EACjBnE,EAAKuC,mBAAqBvC,EAAKJ,QAAQyE,oBACvC,IACIrE,EAAKoE,cAAcpE,EAAKqH,wBAC1B,MAAOlF,GACL,KAAUuQ,OAAMpT,KAI5BkD,cAAe,SAAU1C,GAAV,GAwBP6S,GAvBA5L,EAAW,KACX5C,EAAWlE,KAAKmE,cAChBwO,EAAkB3S,KAAKwC,mBACvBoP,EAAO5R,KAAK4R,IAChB,IAAI/R,IAAShD,EACT,MAAO+U,GAAK/R,MAEZjD,GAAEiM,cAAchJ,IAASA,YAAgBzB,GACzC0I,EAAWjH,EACJ8S,GAAmBA,IAAoB9S,IAC9CiH,EAAW9G,KAAKL,QAAQC,aAEvBkH,IACDA,EAAW9G,KAAK8I,gBAAgBjJ,EAAMG,KAAK2H,cAE3C3H,KAAKsC,mBACDwE,IAAa6L,GAAmB3S,KAAKwH,MAAMV,KAAc6L,IACzDzO,EAAWlE,KAAKoE,oBACwB,gBAA7BpE,MAAKL,QAAQC,aAA6BI,KAAKL,QAAQyE,sBAC9D0C,EAAW6L,KAInBD,EAAc,WACd,OACI/J,SAAUiJ,EAAKgB,MACfvU,OAASyI,SAAUA,MAG3B9G,KAAK0I,QAAQ,UAAWgK,EACxB,KACId,EAAKrJ,KAAKrE,EAAS4C,IACrB,MAAO5E,GACL0P,EAAKrJ,KAAK,IAEdvI,KAAK0I,QAAQ,UAAWgK,IAE5BG,WAAY,SAAU5V,EAAO4C,GACpB5C,GAAU4C,IACXA,EAAOG,KAAKwC,oBAEhBxC,KAAK2H,UAAU1K,GACf+C,KAAKuC,cAAc1C,GACnBG,KAAKkI,KAAOlI,KAAK2H,YACjB3H,KAAK2B,UAAY3B,KAAK4B,cACtB5B,KAAKoC,SAAS0Q,SAAS7V,GACvB+C,KAAK6B,cAAgB,KACrB7B,KAAKyJ,cAAe,GAExBX,gBAAiB,SAAUjJ,EAAM5C,GAAhB,GACTsG,GAAgBvD,KAAKL,QAAQ4D,cAC7BuD,IAQJ,OAPIvD,IACAzG,EAAOgK,EAAUvD,EAAcwP,MAAM,KAAMlT,GAC3C/C,EAAOgK,EAAU9G,KAAKL,QAAQ6D,eAAeuP,MAAM,KAAM9V,GACzD6J,EAAW,GAAI1I,GAAiB0I,IAEhCA,EAAWjH,EAERiH,IA4BfhJ,GAAGkV,OAAOzT,IACZ1B,OAAOD,MAAMqV,QACRpV,OAAOD,OACE,kBAAVjB,SAAwBA,OAAOuW,IAAMvW,OAAS,SAAUwW,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.dropdownlist.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.dropdownlist', [\n        'kendo.list',\n        'kendo.mobile.scroller',\n        'kendo.virtuallist'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'dropdownlist',\n        name: 'DropDownList',\n        category: 'web',\n        description: 'The DropDownList widget displays a list of values and allows the selection of a single value from the list.',\n        depends: ['list'],\n        features: [\n            {\n                id: 'mobile-scroller',\n                name: 'Mobile scroller',\n                description: 'Support for kinetic scrolling in mobile device',\n                depends: ['mobile.scroller']\n            },\n            {\n                id: 'virtualization',\n                name: 'VirtualList',\n                description: 'Support for virtualization',\n                depends: ['virtuallist']\n            }\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, List = ui.List, Select = ui.Select, support = kendo.support, activeElement = kendo._activeElement, ObservableObject = kendo.data.ObservableObject, keys = kendo.keys, ns = '.kendoDropDownList', nsFocusEvent = ns + 'FocusEvent', DISABLED = 'disabled', READONLY = 'readonly', CHANGE = 'change', FOCUSED = 'k-state-focused', DEFAULT = 'k-state-default', STATEDISABLED = 'k-state-disabled', ARIA_DISABLED = 'aria-disabled', CLICKEVENTS = 'click' + ns + ' touchend' + ns, HOVEREVENTS = 'mouseenter' + ns + ' mouseleave' + ns, TABINDEX = 'tabindex', STATE_FILTER = 'filter', STATE_ACCEPT = 'accept', MSG_INVALID_OPTION_LABEL = 'The `optionLabel` option is not valid due to missing fields. Define a custom optionLabel as shown here http://docs.telerik.com/kendo-ui/api/javascript/ui/dropdownlist#configuration-optionLabel', proxy = $.proxy;\n        var DropDownList = Select.extend({\n            init: function (element, options) {\n                var that = this;\n                var index = options && options.index;\n                var optionLabel, text, disabled;\n                that.ns = ns;\n                options = $.isArray(options) ? { dataSource: options } : options;\n                Select.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.element.on('focus' + ns, proxy(that._focusHandler, that));\n                that._focusInputHandler = $.proxy(that._focusInput, that);\n                that.optionLabel = $();\n                that._optionLabel();\n                that._inputTemplate();\n                that._reset();\n                that._prev = '';\n                that._word = '';\n                that._wrapper();\n                that._tabindex();\n                that.wrapper.data(TABINDEX, that.wrapper.attr(TABINDEX));\n                that._span();\n                that._popup();\n                that._mobile();\n                that._dataSource();\n                that._ignoreCase();\n                that._filterHeader();\n                that._aria();\n                that.wrapper.attr('aria-live', 'polite');\n                that._enable();\n                that._attachFocusHandlers();\n                that._oldIndex = that.selectedIndex = -1;\n                if (index !== undefined) {\n                    options.index = index;\n                }\n                that._initialIndex = options.index;\n                that.requireValueMapper(that.options);\n                that._initList();\n                that._cascade();\n                that.one('set', function (e) {\n                    if (!e.sender.listView.bound() && that.hasOptionLabel()) {\n                        that._textAccessor(that._optionLabelText());\n                    }\n                });\n                if (options.autoBind) {\n                    that.dataSource.fetch();\n                } else if (that.selectedIndex === -1) {\n                    text = options.text || '';\n                    if (!text) {\n                        optionLabel = options.optionLabel;\n                        if (optionLabel && options.index === 0) {\n                            text = optionLabel;\n                        } else if (that._isSelect) {\n                            text = element.children(':selected').text();\n                        }\n                    }\n                    that._textAccessor(text);\n                }\n                disabled = $(that.element).parents('fieldset').is(':disabled');\n                if (disabled) {\n                    that.enable(false);\n                }\n                that.listView.bind('click', function (e) {\n                    e.preventDefault();\n                });\n                kendo.notify(that);\n            },\n            options: {\n                name: 'DropDownList',\n                enabled: true,\n                autoBind: true,\n                index: 0,\n                text: null,\n                value: null,\n                delay: 500,\n                height: 200,\n                dataTextField: '',\n                dataValueField: '',\n                optionLabel: '',\n                cascadeFrom: '',\n                cascadeFromField: '',\n                cascadeFromParentField: '',\n                ignoreCase: true,\n                animation: {},\n                filter: 'none',\n                minLength: 1,\n                enforceMinLength: false,\n                virtual: false,\n                template: null,\n                valueTemplate: null,\n                optionLabelTemplate: null,\n                groupTemplate: '#:data#',\n                fixedGroupTemplate: '#:data#',\n                autoWidth: false,\n                popup: null\n            },\n            events: [\n                'open',\n                'close',\n                CHANGE,\n                'select',\n                'filtering',\n                'dataBinding',\n                'dataBound',\n                'cascade',\n                'set'\n            ],\n            setOptions: function (options) {\n                Select.fn.setOptions.call(this, options);\n                this.listView.setOptions(this._listOptions(options));\n                this._optionLabel();\n                this._inputTemplate();\n                this._accessors();\n                this._filterHeader();\n                this._enable();\n                this._aria();\n                if (!this.value() && this.hasOptionLabel()) {\n                    this.select(0);\n                }\n            },\n            destroy: function () {\n                var that = this;\n                Select.fn.destroy.call(that);\n                that.wrapper.off(ns);\n                that.wrapper.off(nsFocusEvent);\n                that.element.off(ns);\n                that._inputWrapper.off(ns);\n                that._arrow.off();\n                that._arrow = null;\n                that._arrowIcon = null;\n                that.optionLabel.off();\n                if (that.filterInput) {\n                    that.filterInput.off(nsFocusEvent);\n                }\n            },\n            open: function () {\n                var that = this;\n                var isFiltered = that.dataSource.filter() ? that.dataSource.filter().filters.length > 0 : false;\n                if (that.popup.visible()) {\n                    return;\n                }\n                if (!that.listView.bound() || that._state === STATE_ACCEPT) {\n                    that._open = true;\n                    that._state = 'rebind';\n                    if (that.filterInput) {\n                        that.filterInput.val('');\n                        that._prev = '';\n                    }\n                    if (that.filterInput && that.options.minLength !== 1 && !isFiltered) {\n                        that.refresh();\n                        that.popup.one('activate', that._focusInputHandler);\n                        that.popup.open();\n                        that._resizeFilterInput();\n                    } else {\n                        that._filterSource();\n                    }\n                } else if (that._allowOpening()) {\n                    that._focusFilter = true;\n                    that.popup.one('activate', that._focusInputHandler);\n                    that.popup._hovered = true;\n                    that.popup.open();\n                    that._resizeFilterInput();\n                    that._focusItem();\n                }\n            },\n            _focusInput: function () {\n                this._focusElement(this.filterInput);\n            },\n            _resizeFilterInput: function () {\n                var filterInput = this.filterInput;\n                var originalPrevent = this._prevent;\n                if (!filterInput) {\n                    return;\n                }\n                var isInputActive = this.filterInput[0] === activeElement();\n                var caret = kendo.caret(this.filterInput[0])[0];\n                this._prevent = true;\n                filterInput.css('display', 'none').css('width', this.popup.element.css('width')).css('display', 'inline-block');\n                if (isInputActive) {\n                    filterInput.focus();\n                    kendo.caret(filterInput[0], caret);\n                }\n                this._prevent = originalPrevent;\n            },\n            _allowOpening: function () {\n                return this.hasOptionLabel() || this.filterInput || Select.fn._allowOpening.call(this);\n            },\n            toggle: function (toggle) {\n                this._toggle(toggle, true);\n            },\n            current: function (candidate) {\n                var current;\n                if (candidate === undefined) {\n                    current = this.listView.focus();\n                    if (!current && this.selectedIndex === 0 && this.hasOptionLabel()) {\n                        return this.optionLabel;\n                    }\n                    return current;\n                }\n                this._focus(candidate);\n            },\n            dataItem: function (index) {\n                var that = this;\n                var dataItem = null;\n                if (index === null) {\n                    return index;\n                }\n                if (index === undefined) {\n                    dataItem = that.listView.selectedDataItems()[0];\n                } else {\n                    if (typeof index !== 'number') {\n                        if (that.options.virtual) {\n                            return that.dataSource.getByUid($(index).data('uid'));\n                        }\n                        if (index.hasClass('k-list-optionlabel')) {\n                            index = -1;\n                        } else {\n                            index = $(that.items()).index(index);\n                        }\n                    } else if (that.hasOptionLabel()) {\n                        index -= 1;\n                    }\n                    dataItem = that.dataSource.flatView()[index];\n                }\n                if (!dataItem) {\n                    dataItem = that._optionLabelDataItem();\n                }\n                return dataItem;\n            },\n            refresh: function () {\n                this.listView.refresh();\n            },\n            text: function (text) {\n                var that = this;\n                var loweredText;\n                var ignoreCase = that.options.ignoreCase;\n                text = text === null ? '' : text;\n                if (text !== undefined) {\n                    if (typeof text !== 'string') {\n                        that._textAccessor(text);\n                        return;\n                    }\n                    loweredText = ignoreCase ? text.toLowerCase() : text;\n                    that._select(function (data) {\n                        data = that._text(data);\n                        if (ignoreCase) {\n                            data = (data + '').toLowerCase();\n                        }\n                        return data === loweredText;\n                    }).done(function () {\n                        that._textAccessor(that.dataItem() || text);\n                    });\n                } else {\n                    return that._textAccessor();\n                }\n            },\n            _clearFilter: function () {\n                $(this.filterInput).val('');\n                Select.fn._clearFilter.call(this);\n            },\n            value: function (value) {\n                var that = this;\n                var listView = that.listView;\n                var dataSource = that.dataSource;\n                if (value === undefined) {\n                    value = that._accessor() || that.listView.value()[0];\n                    return value === undefined || value === null ? '' : value;\n                }\n                that.requireValueMapper(that.options, value);\n                if (value || !that.hasOptionLabel()) {\n                    that._initialIndex = null;\n                }\n                this.trigger('set', { value: value });\n                if (that._request && that.options.cascadeFrom && that.listView.bound()) {\n                    if (that._valueSetter) {\n                        dataSource.unbind(CHANGE, that._valueSetter);\n                    }\n                    that._valueSetter = proxy(function () {\n                        that.value(value);\n                    }, that);\n                    dataSource.one(CHANGE, that._valueSetter);\n                    return;\n                }\n                if (that._isFilterEnabled() && listView.bound() && listView.isFiltered()) {\n                    that._clearFilter();\n                } else {\n                    that._fetchData();\n                }\n                listView.value(value).done(function () {\n                    that._old = that._valueBeforeCascade = that._accessor();\n                    that._oldIndex = that.selectedIndex;\n                });\n            },\n            hasOptionLabel: function () {\n                return this.optionLabel && !!this.optionLabel[0];\n            },\n            _optionLabel: function () {\n                var that = this;\n                var options = that.options;\n                var optionLabel = options.optionLabel;\n                var template = options.optionLabelTemplate;\n                if (!optionLabel) {\n                    that.optionLabel.off().remove();\n                    that.optionLabel = $();\n                    return;\n                }\n                if (!template) {\n                    template = '#:';\n                    if (typeof optionLabel === 'string') {\n                        template += 'data';\n                    } else {\n                        template += kendo.expr(options.dataTextField, 'data');\n                    }\n                    template += '#';\n                }\n                if (typeof template !== 'function') {\n                    template = kendo.template(template);\n                }\n                that.optionLabelTemplate = template;\n                if (!that.hasOptionLabel()) {\n                    that.optionLabel = $('<div class=\"k-list-optionlabel\"></div>').prependTo(that.list);\n                }\n                that.optionLabel.html(template(optionLabel)).off().on(CLICKEVENTS, proxy(that._click, that)).on(HOVEREVENTS, that._toggleHover);\n                that.angular('compile', function () {\n                    return {\n                        elements: that.optionLabel,\n                        data: [{ dataItem: that._optionLabelDataItem() }]\n                    };\n                });\n            },\n            _optionLabelText: function () {\n                var optionLabel = this.options.optionLabel;\n                return typeof optionLabel === 'string' ? optionLabel : this._text(optionLabel);\n            },\n            _optionLabelDataItem: function () {\n                var that = this;\n                var optionLabel = that.options.optionLabel;\n                if (that.hasOptionLabel()) {\n                    return $.isPlainObject(optionLabel) ? new ObservableObject(optionLabel) : that._assignInstance(that._optionLabelText(), '');\n                }\n                return undefined;\n            },\n            _buildOptions: function (data) {\n                var that = this;\n                if (!that._isSelect) {\n                    return;\n                }\n                var value = that.listView.value()[0];\n                var optionLabel = that._optionLabelDataItem();\n                var optionLabelValue = optionLabel && that._value(optionLabel);\n                if (value === undefined || value === null) {\n                    value = '';\n                }\n                if (optionLabel) {\n                    if (optionLabelValue === undefined || optionLabelValue === null) {\n                        optionLabelValue = '';\n                    }\n                    optionLabel = '<option value=\"' + optionLabelValue + '\">' + that._text(optionLabel) + '</option>';\n                }\n                that._options(data, optionLabel, value);\n                if (value !== List.unifyType(that._accessor(), typeof value)) {\n                    that._customOption = null;\n                    that._custom(value);\n                }\n            },\n            _listBound: function () {\n                var that = this;\n                var initialIndex = that._initialIndex;\n                var filtered = that._state === STATE_FILTER;\n                var data = that.dataSource.flatView();\n                var dataItem;\n                that._presetValue = false;\n                that._renderFooter();\n                that._renderNoData();\n                that._toggleNoData(!data.length);\n                that._resizePopup(true);\n                that.popup.position();\n                that._buildOptions(data);\n                that._makeUnselectable();\n                if (!filtered) {\n                    if (that._open) {\n                        that.toggle(that._allowOpening());\n                    }\n                    that._open = false;\n                    if (!that._fetch) {\n                        if (data.length) {\n                            if (!that.listView.value().length && initialIndex > -1 && initialIndex !== null) {\n                                that.select(initialIndex);\n                            }\n                            that._initialIndex = null;\n                            dataItem = that.listView.selectedDataItems()[0];\n                            if (dataItem && that.text() !== that._text(dataItem)) {\n                                that._selectValue(dataItem);\n                            }\n                        } else if (that._textAccessor() !== that._optionLabelText()) {\n                            that.listView.value('');\n                            that._selectValue(null);\n                            that._oldIndex = that.selectedIndex;\n                        }\n                    }\n                }\n                that._hideBusy();\n                that.trigger('dataBound');\n            },\n            _listChange: function () {\n                this._selectValue(this.listView.selectedDataItems()[0]);\n                if (this._presetValue || this._old && this._oldIndex === -1) {\n                    this._oldIndex = this.selectedIndex;\n                }\n            },\n            _filterPaste: function () {\n                this._search();\n            },\n            _attachFocusHandlers: function () {\n                var that = this;\n                var wrapper = that.wrapper;\n                wrapper.on('focusin' + nsFocusEvent, proxy(that._focusinHandler, that)).on('focusout' + nsFocusEvent, proxy(that._focusoutHandler, that));\n                if (that.filterInput) {\n                    that.filterInput.on('focusin' + nsFocusEvent, proxy(that._focusinHandler, that)).on('focusout' + nsFocusEvent, proxy(that._focusoutHandler, that));\n                }\n            },\n            _focusHandler: function () {\n                this.wrapper.focus();\n            },\n            _focusinHandler: function () {\n                this._inputWrapper.addClass(FOCUSED);\n                this._prevent = false;\n            },\n            _focusoutHandler: function () {\n                var that = this;\n                var isIFrame = window.self !== window.top;\n                if (!that._prevent) {\n                    clearTimeout(that._typingTimeout);\n                    if (support.mobileOS.ios && isIFrame) {\n                        that._change();\n                    } else {\n                        that._blur();\n                    }\n                    that._inputWrapper.removeClass(FOCUSED);\n                    that._prevent = true;\n                    that._open = false;\n                    that.element.blur();\n                }\n            },\n            _wrapperMousedown: function () {\n                this._prevent = !!this.filterInput;\n            },\n            _wrapperClick: function (e) {\n                e.preventDefault();\n                this.popup.unbind('activate', this._focusInputHandler);\n                this._focused = this.wrapper;\n                this._prevent = false;\n                this._toggle();\n            },\n            _editable: function (options) {\n                var that = this;\n                var element = that.element;\n                var disable = options.disable;\n                var readonly = options.readonly;\n                var wrapper = that.wrapper.add(that.filterInput).off(ns);\n                var dropDownWrapper = that._inputWrapper.off(HOVEREVENTS);\n                if (!readonly && !disable) {\n                    element.removeAttr(DISABLED).removeAttr(READONLY);\n                    dropDownWrapper.addClass(DEFAULT).removeClass(STATEDISABLED).on(HOVEREVENTS, that._toggleHover);\n                    wrapper.attr(TABINDEX, wrapper.data(TABINDEX)).attr(ARIA_DISABLED, false).on('keydown' + ns, proxy(that._keydown, that)).on(kendo.support.mousedown + ns, proxy(that._wrapperMousedown, that)).on('paste' + ns, proxy(that._filterPaste, that));\n                    that.wrapper.on('click' + ns, proxy(that._wrapperClick, that));\n                    if (!that.filterInput) {\n                        wrapper.on('keypress' + ns, proxy(that._keypress, that));\n                    } else {\n                        wrapper.on('input' + ns, proxy(that._search, that));\n                    }\n                } else if (disable) {\n                    wrapper.removeAttr(TABINDEX);\n                    dropDownWrapper.addClass(STATEDISABLED).removeClass(DEFAULT);\n                } else {\n                    dropDownWrapper.addClass(DEFAULT).removeClass(STATEDISABLED);\n                }\n                element.attr(DISABLED, disable).attr(READONLY, readonly);\n                wrapper.attr(ARIA_DISABLED, disable);\n            },\n            _keydown: function (e) {\n                var that = this;\n                var key = e.keyCode;\n                var altKey = e.altKey;\n                var isInputActive;\n                var handled;\n                var isPopupVisible = that.popup.visible();\n                if (that.filterInput) {\n                    isInputActive = that.filterInput[0] === activeElement();\n                }\n                if (key === keys.LEFT) {\n                    key = keys.UP;\n                    handled = true;\n                } else if (key === keys.RIGHT) {\n                    key = keys.DOWN;\n                    handled = true;\n                }\n                if (handled && isInputActive) {\n                    return;\n                }\n                e.keyCode = key;\n                if (altKey && key === keys.UP || key === keys.ESC) {\n                    that._focusElement(that.wrapper);\n                }\n                if (that._state === STATE_FILTER && key === keys.ESC) {\n                    that._clearFilter();\n                    that._open = false;\n                    that._state = STATE_ACCEPT;\n                }\n                if (key === keys.ENTER && that._typingTimeout && that.filterInput && isPopupVisible) {\n                    e.preventDefault();\n                    return;\n                }\n                if (key === keys.SPACEBAR && !isInputActive) {\n                    that.toggle(!isPopupVisible);\n                    e.preventDefault();\n                }\n                handled = that._move(e);\n                if (handled) {\n                    return;\n                }\n                if (!isPopupVisible || !that.filterInput) {\n                    var current = that._focus();\n                    if (key === keys.HOME) {\n                        handled = true;\n                        that._firstItem();\n                    } else if (key === keys.END) {\n                        handled = true;\n                        that._lastItem();\n                    }\n                    if (handled) {\n                        if (that.trigger('select', {\n                                dataItem: that._getElementDataItem(that._focus()),\n                                item: that._focus()\n                            })) {\n                            that._focus(current);\n                            return;\n                        }\n                        that._select(that._focus(), true).done(function () {\n                            if (!isPopupVisible) {\n                                that._blur();\n                            }\n                        });\n                        e.preventDefault();\n                    }\n                }\n                if (!altKey && !handled && that.filterInput) {\n                    that._search();\n                }\n            },\n            _matchText: function (text, word) {\n                var ignoreCase = this.options.ignoreCase;\n                if (text === undefined || text === null) {\n                    return false;\n                }\n                text = text + '';\n                if (ignoreCase) {\n                    text = text.toLowerCase();\n                }\n                return text.indexOf(word) === 0;\n            },\n            _shuffleData: function (data, splitIndex) {\n                var optionDataItem = this._optionLabelDataItem();\n                if (optionDataItem) {\n                    data = [optionDataItem].concat(data);\n                }\n                return data.slice(splitIndex).concat(data.slice(0, splitIndex));\n            },\n            _selectNext: function () {\n                var that = this;\n                var data = that.dataSource.flatView();\n                var dataLength = data.length + (that.hasOptionLabel() ? 1 : 0);\n                var isInLoop = sameCharsOnly(that._word, that._last);\n                var startIndex = that.selectedIndex;\n                var oldFocusedItem;\n                var text;\n                if (startIndex === -1) {\n                    startIndex = 0;\n                } else {\n                    startIndex += isInLoop ? 1 : 0;\n                    startIndex = normalizeIndex(startIndex, dataLength);\n                }\n                data = data.toJSON ? data.toJSON() : data.slice();\n                data = that._shuffleData(data, startIndex);\n                for (var idx = 0; idx < dataLength; idx++) {\n                    text = that._text(data[idx]);\n                    if (isInLoop && that._matchText(text, that._last)) {\n                        break;\n                    } else if (that._matchText(text, that._word)) {\n                        break;\n                    }\n                }\n                if (idx !== dataLength) {\n                    oldFocusedItem = that._focus();\n                    that._select(normalizeIndex(startIndex + idx, dataLength)).done(function () {\n                        var done = function () {\n                            if (!that.popup.visible()) {\n                                that._change();\n                            }\n                        };\n                        if (that.trigger('select', {\n                                dataItem: that._getElementDataItem(that._focus()),\n                                item: that._focus()\n                            })) {\n                            that._select(oldFocusedItem).done(done);\n                        } else {\n                            done();\n                        }\n                    });\n                }\n            },\n            _keypress: function (e) {\n                var that = this;\n                if (e.which === 0 || e.keyCode === kendo.keys.ENTER) {\n                    return;\n                }\n                var character = String.fromCharCode(e.charCode || e.keyCode);\n                if (that.options.ignoreCase) {\n                    character = character.toLowerCase();\n                }\n                if (character === ' ') {\n                    e.preventDefault();\n                }\n                that._word += character;\n                that._last = character;\n                that._search();\n            },\n            _popupOpen: function () {\n                var popup = this.popup;\n                popup.wrapper = kendo.wrap(popup.element);\n                if (popup.element.closest('.km-root')[0]) {\n                    popup.wrapper.addClass('km-popup km-widget');\n                    this.wrapper.addClass('km-widget');\n                }\n            },\n            _popup: function () {\n                Select.fn._popup.call(this);\n                this.popup.one('open', proxy(this._popupOpen, this));\n            },\n            _getElementDataItem: function (element) {\n                if (!element || !element[0]) {\n                    return null;\n                }\n                if (element[0] === this.optionLabel[0]) {\n                    return this._optionLabelDataItem();\n                }\n                return this.listView.dataItemByIndex(this.listView.getElementIndex(element));\n            },\n            _click: function (e) {\n                var that = this;\n                var item = e.item || $(e.currentTarget);\n                e.preventDefault();\n                if (that.trigger('select', {\n                        dataItem: that._getElementDataItem(item),\n                        item: item\n                    })) {\n                    that.close();\n                    return;\n                }\n                that._userTriggered = true;\n                that._select(item).done(function () {\n                    that._focusElement(that.wrapper);\n                    that._blur();\n                });\n            },\n            _focusElement: function (element) {\n                var active = activeElement();\n                var wrapper = this.wrapper;\n                var filterInput = this.filterInput;\n                var compareElement = element === filterInput ? wrapper : filterInput;\n                var touchEnabled = support.mobileOS && (support.touch || support.MSPointers || support.pointers);\n                if (filterInput && filterInput[0] === element[0] && touchEnabled) {\n                    return;\n                }\n                if (filterInput && (compareElement[0] === active || this._focusFilter)) {\n                    this._focusFilter = false;\n                    this._prevent = true;\n                    this._focused = element.focus();\n                }\n            },\n            _searchByWord: function (word) {\n                if (!word) {\n                    return;\n                }\n                var that = this;\n                var ignoreCase = that.options.ignoreCase;\n                if (ignoreCase) {\n                    word = word.toLowerCase();\n                }\n                that._select(function (dataItem) {\n                    return that._matchText(that._text(dataItem), word);\n                });\n            },\n            _inputValue: function () {\n                return this.text();\n            },\n            _search: function () {\n                var that = this;\n                var dataSource = that.dataSource;\n                clearTimeout(that._typingTimeout);\n                if (that._isFilterEnabled()) {\n                    that._typingTimeout = setTimeout(function () {\n                        var value = that.filterInput.val();\n                        if (that._prev !== value) {\n                            that._prev = value;\n                            that.search(value);\n                            that._resizeFilterInput();\n                        }\n                        that._typingTimeout = null;\n                    }, that.options.delay);\n                } else {\n                    that._typingTimeout = setTimeout(function () {\n                        that._word = '';\n                    }, that.options.delay);\n                    if (!that.listView.bound()) {\n                        dataSource.fetch().done(function () {\n                            that._selectNext();\n                        });\n                        return;\n                    }\n                    that._selectNext();\n                }\n            },\n            _get: function (candidate) {\n                var data, found, idx;\n                var isFunction = typeof candidate === 'function';\n                var jQueryCandidate = !isFunction ? $(candidate) : $();\n                if (this.hasOptionLabel()) {\n                    if (typeof candidate === 'number') {\n                        if (candidate > -1) {\n                            candidate -= 1;\n                        }\n                    } else if (jQueryCandidate.hasClass('k-list-optionlabel')) {\n                        candidate = -1;\n                    }\n                }\n                if (isFunction) {\n                    data = this.dataSource.flatView();\n                    for (idx = 0; idx < data.length; idx++) {\n                        if (candidate(data[idx])) {\n                            candidate = idx;\n                            found = true;\n                            break;\n                        }\n                    }\n                    if (!found) {\n                        candidate = -1;\n                    }\n                }\n                return candidate;\n            },\n            _firstItem: function () {\n                if (this.hasOptionLabel()) {\n                    this._focus(this.optionLabel);\n                } else {\n                    this.listView.focusFirst();\n                }\n            },\n            _lastItem: function () {\n                this._resetOptionLabel();\n                this.listView.focusLast();\n            },\n            _nextItem: function () {\n                if (this.optionLabel.hasClass('k-state-focused')) {\n                    this._resetOptionLabel();\n                    this.listView.focusFirst();\n                } else {\n                    this.listView.focusNext();\n                }\n            },\n            _prevItem: function () {\n                if (this.optionLabel.hasClass('k-state-focused')) {\n                    return;\n                }\n                this.listView.focusPrev();\n                if (!this.listView.focus()) {\n                    this._focus(this.optionLabel);\n                }\n            },\n            _focusItem: function () {\n                var options = this.options;\n                var listView = this.listView;\n                var focusedItem = listView.focus();\n                var index = listView.select();\n                index = index[index.length - 1];\n                if (index === undefined && options.highlightFirst && !focusedItem) {\n                    index = 0;\n                }\n                if (index !== undefined) {\n                    listView.focus(index);\n                } else {\n                    if (options.optionLabel && (!options.virtual || options.virtual.mapValueTo !== 'dataItem')) {\n                        this._focus(this.optionLabel);\n                        this._select(this.optionLabel);\n                        this.listView.content.scrollTop(0);\n                    } else {\n                        listView.scrollToIndex(0);\n                    }\n                }\n            },\n            _resetOptionLabel: function (additionalClass) {\n                this.optionLabel.removeClass('k-state-focused' + (additionalClass || '')).removeAttr('id');\n            },\n            _focus: function (candidate) {\n                var listView = this.listView;\n                var optionLabel = this.optionLabel;\n                if (candidate === undefined) {\n                    candidate = listView.focus();\n                    if (!candidate && optionLabel.hasClass('k-state-focused')) {\n                        candidate = optionLabel;\n                    }\n                    return candidate;\n                }\n                this._resetOptionLabel();\n                candidate = this._get(candidate);\n                listView.focus(candidate);\n                if (candidate === -1) {\n                    optionLabel.addClass('k-state-focused').attr('id', listView._optionID);\n                    this._focused.add(this.filterInput).removeAttr('aria-activedescendant').attr('aria-activedescendant', listView._optionID);\n                }\n            },\n            _select: function (candidate, keepState) {\n                var that = this;\n                candidate = that._get(candidate);\n                return that.listView.select(candidate).done(function () {\n                    if (!keepState && that._state === STATE_FILTER) {\n                        that._state = STATE_ACCEPT;\n                    }\n                    if (candidate === -1) {\n                        that._selectValue(null);\n                    }\n                });\n            },\n            _selectValue: function (dataItem) {\n                var that = this;\n                var optionLabel = that.options.optionLabel;\n                var idx = that.listView.select();\n                var value = '';\n                var text = '';\n                idx = idx[idx.length - 1];\n                if (idx === undefined) {\n                    idx = -1;\n                }\n                this._resetOptionLabel(' k-state-selected');\n                if (dataItem || dataItem === 0) {\n                    text = dataItem;\n                    value = that._dataValue(dataItem);\n                    if (optionLabel) {\n                        idx += 1;\n                    }\n                } else if (optionLabel) {\n                    that._focus(that.optionLabel.addClass('k-state-selected'));\n                    text = that._optionLabelText();\n                    if (typeof optionLabel === 'string') {\n                        value = '';\n                    } else {\n                        value = that._value(optionLabel);\n                    }\n                    idx = 0;\n                }\n                that.selectedIndex = idx;\n                if (value === null) {\n                    value = '';\n                }\n                that._textAccessor(text);\n                that._accessor(value, idx);\n                that._triggerCascade();\n            },\n            _mobile: function () {\n                var that = this, popup = that.popup, mobileOS = support.mobileOS, root = popup.element.parents('.km-root').eq(0);\n                if (root.length && mobileOS) {\n                    popup.options.animation.open.effects = mobileOS.android || mobileOS.meego ? 'fadeIn' : mobileOS.ios || mobileOS.wp ? 'slideIn:up' : popup.options.animation.open.effects;\n                }\n            },\n            _filterHeader: function () {\n                var icon;\n                if (this.filterInput) {\n                    this.filterInput.off(ns).parent().remove();\n                    this.filterInput = null;\n                }\n                if (this._isFilterEnabled()) {\n                    icon = '<span class=\"k-icon k-i-zoom\"></span>';\n                    this.filterInput = $('<input class=\"k-textbox\"/>').attr({\n                        placeholder: this.element.attr('placeholder'),\n                        title: this.element.attr('title'),\n                        role: 'listbox',\n                        'aria-haspopup': true,\n                        'aria-expanded': false\n                    });\n                    this.list.prepend($('<span class=\"k-list-filter\" />').append(this.filterInput.add(icon)));\n                }\n            },\n            _span: function () {\n                var that = this, wrapper = that.wrapper, SELECTOR = 'span.k-input', span;\n                span = wrapper.find(SELECTOR);\n                if (!span[0]) {\n                    wrapper.append('<span unselectable=\"on\" class=\"k-dropdown-wrap k-state-default\"><span unselectable=\"on\" class=\"k-input\">&nbsp;</span><span unselectable=\"on\" class=\"k-select\" aria-label=\"select\"><span class=\"k-icon k-i-arrow-60-down\"></span></span></span>').append(that.element);\n                    span = wrapper.find(SELECTOR);\n                }\n                that.span = span;\n                that._inputWrapper = $(wrapper[0].firstChild);\n                that._arrow = wrapper.find('.k-select');\n                that._arrowIcon = that._arrow.find('.k-icon');\n            },\n            _wrapper: function () {\n                var that = this, element = that.element, DOMelement = element[0], wrapper;\n                wrapper = element.parent();\n                if (!wrapper.is('span.k-widget')) {\n                    wrapper = element.wrap('<span />').parent();\n                    wrapper[0].style.cssText = DOMelement.style.cssText;\n                    wrapper[0].title = DOMelement.title;\n                }\n                that._focused = that.wrapper = wrapper.addClass('k-widget k-dropdown').addClass(DOMelement.className).css('display', '').attr({\n                    accesskey: element.attr('accesskey'),\n                    unselectable: 'on',\n                    role: 'listbox',\n                    'aria-haspopup': true,\n                    'aria-expanded': false\n                });\n                element.hide().removeAttr('accesskey');\n            },\n            _clearSelection: function (parent) {\n                this.select(parent.value() ? 0 : -1);\n            },\n            _inputTemplate: function () {\n                var that = this, template = that.options.valueTemplate;\n                if (!template) {\n                    template = $.proxy(kendo.template('#:this._text(data)#', { useWithBlock: false }), that);\n                } else {\n                    template = kendo.template(template);\n                }\n                that.valueTemplate = template;\n                if (that.hasOptionLabel() && !that.options.optionLabelTemplate) {\n                    try {\n                        that.valueTemplate(that._optionLabelDataItem());\n                    } catch (e) {\n                        throw new Error(MSG_INVALID_OPTION_LABEL);\n                    }\n                }\n            },\n            _textAccessor: function (text) {\n                var dataItem = null;\n                var template = this.valueTemplate;\n                var optionLabelText = this._optionLabelText();\n                var span = this.span;\n                if (text === undefined) {\n                    return span.text();\n                }\n                if ($.isPlainObject(text) || text instanceof ObservableObject) {\n                    dataItem = text;\n                } else if (optionLabelText && optionLabelText === text) {\n                    dataItem = this.options.optionLabel;\n                }\n                if (!dataItem) {\n                    dataItem = this._assignInstance(text, this._accessor());\n                }\n                if (this.hasOptionLabel()) {\n                    if (dataItem === optionLabelText || this._text(dataItem) === optionLabelText) {\n                        template = this.optionLabelTemplate;\n                        if (typeof this.options.optionLabel === 'string' && !this.options.optionLabelTemplate) {\n                            dataItem = optionLabelText;\n                        }\n                    }\n                }\n                var getElements = function () {\n                    return {\n                        elements: span.get(),\n                        data: [{ dataItem: dataItem }]\n                    };\n                };\n                this.angular('cleanup', getElements);\n                try {\n                    span.html(template(dataItem));\n                } catch (e) {\n                    span.html('');\n                }\n                this.angular('compile', getElements);\n            },\n            _preselect: function (value, text) {\n                if (!value && !text) {\n                    text = this._optionLabelText();\n                }\n                this._accessor(value);\n                this._textAccessor(text);\n                this._old = this._accessor();\n                this._oldIndex = this.selectedIndex;\n                this.listView.setValue(value);\n                this._initialIndex = null;\n                this._presetValue = true;\n            },\n            _assignInstance: function (text, value) {\n                var dataTextField = this.options.dataTextField;\n                var dataItem = {};\n                if (dataTextField) {\n                    assign(dataItem, dataTextField.split('.'), text);\n                    assign(dataItem, this.options.dataValueField.split('.'), value);\n                    dataItem = new ObservableObject(dataItem);\n                } else {\n                    dataItem = text;\n                }\n                return dataItem;\n            }\n        });\n        function assign(instance, fields, value) {\n            var idx = 0, lastIndex = fields.length - 1, field;\n            for (; idx < lastIndex; ++idx) {\n                field = fields[idx];\n                if (!(field in instance)) {\n                    instance[field] = {};\n                }\n                instance = instance[field];\n            }\n            instance[fields[lastIndex]] = value;\n        }\n        function normalizeIndex(index, length) {\n            if (index >= length) {\n                index -= length;\n            }\n            return index;\n        }\n        function sameCharsOnly(word, character) {\n            for (var idx = 0; idx < word.length; idx++) {\n                if (word.charAt(idx) !== character) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        ui.plugin(DropDownList);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}