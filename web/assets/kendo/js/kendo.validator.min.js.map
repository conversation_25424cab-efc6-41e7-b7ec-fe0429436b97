{"version": 3, "sources": ["kendo.validator.js"], "names": ["f", "define", "$", "undefined", "resolveRules", "element", "name", "resolvers", "kendo", "ui", "validator", "ruleResolvers", "rules", "extend", "resolve", "decode", "value", "replace", "numberOfDecimalDigits", "split", "length", "parseHtml", "text", "parseHTML", "searchForMessageContainer", "elements", "fieldName", "attr", "idx", "containers", "invalidMsgRegExp", "test", "className", "getAttribute", "add", "Validator", "window", "Widget", "NS", "INVALIDMSG", "RegExp", "INVALIDINPUT", "VALIDINPUT", "emailRegExp", "urlRegExp", "INPUTSELECTOR", "CHECKBOXSELECTOR", "NUMBERINPUTSELECTOR", "BLUR", "NAME", "FORM", "NOVALIDATE", "VALIDATE", "CHANGE", "VALIDATE_INPUT", "proxy", "patternMatcher", "pattern", "matcher", "input", "selector", "val", "filter", "hasAttribute", "attributes", "messages", "init", "options", "that", "this", "resolved", "validateAttributeSelector", "fn", "call", "_errorTemplate", "template", "errorTemplate", "is", "_inputSelector", "_checkboxSelector", "_errors", "_attachEvents", "_isValidated", "events", "required", "min", "max", "step", "email", "url", "date", "dateCompare", "checkbox", "parseFloat", "raise", "decimals", "Math", "pow", "floor", "parseDate", "validateOnBlur", "destroy", "off", "errors", "_submit", "e", "validate", "stopPropagation", "stopImmediatePropagation", "preventDefault", "_checkElement", "state", "validateInput", "trigger", "on", "inputs", "invalid", "result", "<PERSON><PERSON><PERSON><PERSON>", "find", "eq", "valid", "lbl", "messageText", "<PERSON><PERSON><PERSON><PERSON>", "messageLabel", "lblId", "inputWrap", "_checkValidity", "_findMessageContainer", "next", "hide", "removeAttr", "_extractMessage", "key", "message", "_decorateMessageContainer", "replaceWith", "insertAfter", "show", "toggleClass", "widgetInstance", "_inputWrapper", "hideMessages", "locators", "messageLocators", "getElementsByTagName", "locate", "container", "addClass", "decorate", "<PERSON><PERSON><PERSON>", "nonDefaultMessage", "customMessage", "prototype", "isFunction", "format", "rule", "error", "results", "push", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,mBAAoB,cAAeD,IAC5C,WA4UE,MApUC,UAAUE,EAAGC,GAwBV,QAASC,GAAaC,GAClB,GAAoEC,GAAhEC,EAAYC,EAAMC,GAAGC,UAAUC,kBAAqBC,IACxD,KAAKN,IAAQC,GACTL,EAAEW,QAAO,EAAMD,EAAOL,EAAUD,GAAMQ,QAAQT,GAElD,OAAOO,GAEX,QAASG,GAAOC,GACZ,MAAOA,GAAMC,QAAQ,QAAS,SAASA,QAAQ,UAAW,KAAKA,QAAQ,SAAU,KAAMA,QAAQ,QAAS,KAAKA,QAAQ,QAAS,KAElI,QAASC,GAAsBF,GAE3B,MADAA,IAASA,EAAQ,IAAIG,MAAM,KACvBH,EAAMI,OAAS,EACRJ,EAAM,GAAGI,OAEb,EAEX,QAASC,GAAUC,GACf,MACWpB,GADPA,EAAEqB,UACOrB,EAAEqB,UAAUD,GAEhBA,GAEb,QAASE,GAA0BC,EAAUC,GAA7C,GAC0BrB,GAASsB,EACtBC,EAASR,EADdS,EAAa3B,GACjB,KAAS0B,EAAM,EAAGR,EAASK,EAASL,OAAQQ,EAAMR,EAAQQ,IACtDvB,EAAUoB,EAASG,GACfE,EAAiBC,KAAK1B,EAAQ2B,aAC9BL,EAAOtB,EAAQ4B,aAAazB,EAAMmB,KAAK,QACnCA,IAASD,IACTG,EAAaA,EAAWK,IAAI7B,IAIxC,OAAOwB,GA1Dd,GA4DOM,GA3DA3B,EAAQ4B,OAAO5B,MAAO6B,EAAS7B,EAAMC,GAAG4B,OAAQC,EAAK,kBAAmBC,EAAa,gBAAiBT,EAAuBU,OAAOD,EAAY,KAAME,EAAe,YAAaC,EAAa,UAAWC,EAAc,yIAA0IC,EAAY,sqCAAuqCC,EAAgB,uEAAwEC,EAAmB,uCAAwCC,EAAsB,6BAA8BC,EAAO,OAAQC,EAAO,OAAQC,EAAO,OAAQC,EAAa,aAAcC,EAAW,WAAYC,EAAS,SAAUC,EAAiB,gBAAiBC,EAAQrD,EAAEqD,MAAOC,EAAiB,SAAUxC,EAAOyC,GAI/5D,MAHuB,gBAAZA,KACPA,EAAcjB,OAAO,OAASiB,EAAU,OAErCA,EAAQ1B,KAAKf,IACrB0C,EAAU,SAAUC,EAAOC,EAAUH,GACpC,GAAIzC,GAAQ2C,EAAME,KAClB,QAAIF,EAAMG,OAAOF,GAAUxC,QAAoB,KAAVJ,GAC1BwC,EAAexC,EAAOyC,IAGlCM,EAAe,SAAUJ,EAAOrD,GAC/B,QAAIqD,EAAMvC,QAC8B,MAA7BuC,EAAM,GAAGK,WAAW1D,GAIlCE,GAAMC,GAAGC,YACVF,EAAMC,GAAGC,WACLE,SACAqD,cAuCJ9B,EAAYE,EAAOxB,QACnBqD,KAAM,SAAU7D,EAAS8D,GACrB,GAAIC,GAAOC,KAAMC,EAAWlE,EAAaC,GAAUkE,EAA4B,IAAM/D,EAAMmB,KAAK,YAAc,UAC9GwC,GAAUA,MACVA,EAAQvD,MAAQV,EAAEW,UAAWL,EAAMC,GAAGC,UAAUE,MAAO0D,EAAS1D,MAAOuD,EAAQvD,OAC/EuD,EAAQF,SAAW/D,EAAEW,UAAWL,EAAMC,GAAGC,UAAUuD,SAAUK,EAASL,SAAUE,EAAQF,UACxF5B,EAAOmC,GAAGN,KAAKO,KAAKL,EAAM/D,EAAS8D,GACnCC,EAAKM,eAAiBlE,EAAMmE,SAASP,EAAKD,QAAQS,eAC9CR,EAAK/D,QAAQwE,GAAG3B,IAChBkB,EAAK/D,QAAQsB,KAAKwB,EAAYA,GAElCiB,EAAKU,eAAiBjC,EAAgB0B,EACtCH,EAAKW,kBAAoBjC,EAAmByB,EAC5CH,EAAKY,WACLZ,EAAKa,gBACLb,EAAKc,cAAe,GAExBC,QACI/B,EACAC,EACAC,GAEJa,SACI7D,KAAM,YACNsE,cAAe,oHACfX,UACImB,SAAU,kBACV3B,QAAS,mBACT4B,IAAK,6CACLC,IAAK,6CACLC,KAAM,mBACNC,MAAO,yBACPC,IAAK,uBACLC,KAAM,wBACNC,YAAa,8DAEjB/E,OACIwE,SAAU,SAAUzB,GAChB,GAAIiC,GAAWjC,EAAMG,OAAO,mBAAmB1C,SAAWuC,EAAMkB,GAAG,YAAa7D,EAAQ2C,EAAME,KAC9F,SAASE,EAAaJ,EAAO,eAAiB3C,GAAmB,KAAVA,GAAiC,IAAjBA,EAAMI,QAAgBwE,KAEjGnC,QAAS,SAAUE,GACf,OAAIA,EAAMG,OAAO,gFAAgFA,OAAO,aAAa1C,QAA0B,KAAhBuC,EAAME,OAC1HL,EAAeG,EAAME,MAAOF,EAAMhC,KAAK,aAItD0D,IAAK,SAAU1B,GACX,GAAIA,EAAMG,OAAOf,EAAsB,KAAOvC,EAAMmB,KAAK,QAAU,YAAYmC,OAAO,SAAS1C,QAA0B,KAAhBuC,EAAME,MAAc,CACzH,GAAIwB,GAAMQ,WAAWlC,EAAMhC,KAAK,SAAW,EAAGkC,EAAMrD,EAAMqF,WAAWlC,EAAME,MAC3E,OAAOwB,IAAOxB,EAElB,OAAO,GAEXyB,IAAK,SAAU3B,GACX,GAAIA,EAAMG,OAAOf,EAAsB,KAAOvC,EAAMmB,KAAK,QAAU,YAAYmC,OAAO,SAAS1C,QAA0B,KAAhBuC,EAAME,MAAc,CACzH,GAAIyB,GAAMO,WAAWlC,EAAMhC,KAAK,SAAW,EAAGkC,EAAMrD,EAAMqF,WAAWlC,EAAME,MAC3E,OAAOyB,IAAOzB,EAElB,OAAO,GAEX0B,KAAM,SAAU5B,GACZ,GAAIA,EAAMG,OAAOf,EAAsB,KAAOvC,EAAMmB,KAAK,QAAU,YAAYmC,OAAO,UAAU1C,QAA0B,KAAhBuC,EAAME,MAAc,CAC1H,GAAiKiC,GAA7JT,EAAMQ,WAAWlC,EAAMhC,KAAK,SAAW,EAAG4D,EAAOM,WAAWlC,EAAMhC,KAAK,UAAY,EAAGkC,EAAMgC,WAAWlC,EAAME,OAAQkC,EAAW7E,EAAsBqE,EAC1J,OAAIQ,IACAD,EAAQE,KAAKC,IAAI,GAAIF,GACdC,KAAKE,OAAOrC,EAAMwB,GAAOS,IAAUP,EAAOO,GAASE,KAAKC,IAAI,IAAKF,KAAc,IAElFlC,EAAMwB,GAAOE,IAAS,EAElC,OAAO,GAEXC,MAAO,SAAU7B,GACb,MAAOD,GAAQC,EAAO,iBAAmBnD,EAAMmB,KAAK,QAAU,UAAWgB,IAE7E8C,IAAK,SAAU9B,GACX,MAAOD,GAAQC,EAAO,eAAiBnD,EAAMmB,KAAK,QAAU,QAASiB,IAEzE8C,KAAM,SAAU/B,GACZ,OAAIA,EAAMG,OAAO,iBAAmBtD,EAAMmB,KAAK,QAAU,UAAUP,QAA0B,KAAhBuC,EAAME,OACL,OAAnErD,EAAM2F,UAAUxC,EAAME,MAAOF,EAAMhC,KAAKnB,EAAMmB,KAAK,cAKtEyE,gBAAgB,GAEpBC,QAAS,WACLhE,EAAOmC,GAAG6B,QAAQ5B,KAAKJ,MACvBA,KAAKhE,QAAQiG,IAAIhE,IAErBtB,MAAO,WACH,QAAKqD,KAAKa,cAGsB,IAAzBb,KAAKkC,SAASnF,QAEzBoF,QAAS,SAAUC,GACf,QAAKpC,KAAKqC,aACND,EAAEE,kBACFF,EAAEG,2BACFH,EAAEI,kBACK,IAIfC,cAAe,SAAUzG,GACrB,GAAI0G,GAAQ1C,KAAKrD,OACjBqD,MAAK2C,cAAc3G,GACfgE,KAAKrD,UAAY+F,GACjB1C,KAAK4C,QAAQ5D,IAGrB4B,cAAe,WACX,GAAIb,GAAOC,IACPD,GAAK/D,QAAQwE,GAAG3B,IAChBkB,EAAK/D,QAAQ6G,GAAG,SAAW5E,EAAIiB,EAAMa,EAAKoC,QAASpC,IAEnDA,EAAKD,QAAQiC,iBACRhC,EAAK/D,QAAQwE,GAAGhC,IAQjBuB,EAAK/D,QAAQ6G,GAAGlE,EAAOV,EAAI,WACvB8B,EAAK0C,cAAc1C,EAAK/D,WAExB+D,EAAK/D,QAAQwE,GAAG/B,IAChBsB,EAAK/D,QAAQ6G,GAAG,QAAU5E,EAAI,WAC1B8B,EAAK0C,cAAc1C,EAAK/D,aAZhC+D,EAAK/D,QAAQ6G,GAAGlE,EAAOV,EAAI8B,EAAKU,eAAgB,WAC5CV,EAAK0C,cAAc5G,EAAEmE,SAEzBD,EAAK/D,QAAQ6G,GAAG,QAAU5E,EAAI8B,EAAKW,kBAAmB,WAClDX,EAAK0C,cAAc5G,EAAEmE,YAcrCqC,SAAU,WAAA,GACFS,GACAvF,EAEAR,EAIIgG,EALJC,GAAS,EAETC,EAAUjD,KAAKrD,OAEnB,IADAqD,KAAKW,WACAX,KAAKhE,QAAQwE,GAAGhC,GAUjBwE,EAAShD,KAAK2C,cAAc3C,KAAKhE,aAVA,CAGjC,IAFI+G,GAAU,EACdD,EAAS9C,KAAKhE,QAAQkH,KAAKlD,KAAKS,gBAC3BlD,EAAM,EAAGR,EAAS+F,EAAO/F,OAAQQ,EAAMR,EAAQQ,IAC3CyC,KAAK2C,cAAcG,EAAOK,GAAG5F,MAC9BwF,GAAU,EAGlBC,IAAUD,EAQd,MAJA/C,MAAK4C,QAAQ7D,GAAYqE,MAAOJ,IAC5BC,IAAYD,GACZhD,KAAK4C,QAAQ5D,GAEVgE,GAEXL,cAAe,SAAUrD,GAAV,GAGPS,GAAaO,EAAgC0C,EAAqCI,EAAsBzF,EAA8BN,EAAoCgG,EAM9JC,EAAaC,EAKrBC,EACAC,EAsBAC,CAMR,OA1CApE,GAAQzD,EAAEyD,GACVU,KAAKa,cAAe,EAChBd,EAAOC,KAAMM,EAAWP,EAAKM,eAAgB2C,EAASjD,EAAK4D,eAAerE,GAAQ8D,EAAQJ,EAAOI,MAAOzF,EAAY,IAAMO,EAAYb,EAAYiC,EAAMhC,KAAKsB,IAAS,GAAIyE,EAAMtD,EAAK6D,sBAAsBvG,GAAWQ,IAAIyB,EAAMuE,KAAKlG,GAAW8B,OAAO,WACnP,GAAIzD,GAAUH,EAAEmE,KAChB,QAAIhE,EAAQyD,OAAO,IAAMtD,EAAMmB,KAAK,OAAS,KAAKP,QACvCf,EAAQsB,KAAKnB,EAAMmB,KAAK,UAAYD,KAG/CyG,OAAqBP,GAAYjE,EAAMhC,KAAK,gBACpDgC,EAAMyE,WAAW,gBACZX,QAeMrD,GAAKY,QAAQtD,IAdpBiG,EAAcvD,EAAKiE,gBAAgB1E,EAAO0D,EAAOiB,KACjDlE,EAAKY,QAAQtD,GAAaiG,EACtBE,EAAexG,EAAUsD,GAAW4D,QAASxH,EAAO4G,MACpDG,EAAQJ,EAAI/F,KAAK,MACrByC,EAAKoE,0BAA0BX,EAAcnG,GACzCoG,GACAD,EAAalG,KAAK,KAAMmG,GAEvBJ,EAAIe,YAAYZ,GAAczG,QAC/ByG,EAAaa,YAAY/E,GAE7BkE,EAAac,OACbhF,EAAMhC,KAAK,gBAAgB,IAI3BiG,IAAaH,GACbpD,KAAK4C,QAAQ3D,GACTmE,MAAOA,EACP9D,MAAOA,IAGfA,EAAMiF,YAAYnG,GAAegF,GACjC9D,EAAMiF,YAAYlG,EAAY+E,GAC1BjH,EAAMqI,eAAelF,KACjBoE,EAAYvH,EAAMqI,eAAelF,GAAOmF,cACxCf,IACAA,EAAUa,YAAYnG,GAAegF,GACrCM,EAAUa,YAAYnG,GAAegF,KAGtCA,GAEXsB,aAAc,WACV,GAAI3E,GAAOC,KAAMrC,EAAY,IAAMO,EAAYlC,EAAU+D,EAAK/D,OACzDA,GAAQwE,GAAGhC,GAGZxC,EAAQ6H,KAAKlG,GAAWmG,OAFxB9H,EAAQkH,KAAKvF,GAAWmG,QAKhCF,sBAAuB,SAAUvG,GAAV,GACgCpB,GAC1CsB,EAASR,EADd4H,EAAWxI,EAAMC,GAAGC,UAAUuI,gBAAuBpH,EAAa3B,GACtE,KAAS0B,EAAM,EAAGR,EAASiD,KAAKhE,QAAQe,OAAQQ,EAAMR,EAAQQ,IAC1DC,EAAaA,EAAWK,IAAIV,EAA0B6C,KAAKhE,QAAQuB,GAAKsH,qBAAqB,KAAMxH,GAEvG,KAAKpB,IAAQ0I,GACTnH,EAAaA,EAAWK,IAAI8G,EAAS1I,GAAM6I,OAAO9E,KAAKhE,QAASqB,GAEpE,OAAOG,IAEX2G,0BAA2B,SAAUY,EAAW1H,GAC5C,GAAmDpB,GAA/C0I,EAAWxI,EAAMC,GAAGC,UAAUuI,eAClCG,GAAUC,SAAS9G,GAAYZ,KAAKnB,EAAMmB,KAAK,OAAQD,GAAa,GACpE,KAAKpB,IAAQ0I,GACTA,EAAS1I,GAAMgJ,SAASF,EAAW1H,EAEvC0H,GAAUzH,KAAK,OAAQ,UAE3B0G,gBAAiB,SAAU1E,EAAO4F,GAC9B,GAA+FC,GAA3FpF,EAAOC,KAAMoF,EAAgBrF,EAAKD,QAAQF,SAASsF,GAAU7H,EAAYiC,EAAMhC,KAAKsB,EAKxF,OAJKzC,GAAMC,GAAG0B,UAAUuH,UAAUvF,QAAQF,SAASsF,KAC/CC,EAAoBhJ,EAAMmJ,WAAWF,GAAiBA,EAAc9F,GAAS8F,GAEjFA,EAAgBjJ,EAAMmJ,WAAWF,GAAiBA,EAAc9F,GAAS8F,EAClEjJ,EAAMoJ,OAAOjG,EAAMhC,KAAKnB,EAAMmB,KAAK4H,EAAU,UAAY5F,EAAMhC,KAAK,sBAAwB6H,GAAqB7F,EAAMhC,KAAK,UAAY8H,GAAiB,GAAI/H,EAAWiC,EAAMhC,KAAK4H,IAAY5F,EAAMhC,KAAKnB,EAAMmB,KAAK4H,MAEhOvB,eAAgB,SAAUrE,GACtB,GAAgCkG,GAA5BjJ,EAAQyD,KAAKF,QAAQvD,KACzB,KAAKiJ,IAAQjJ,GACT,IAAKA,EAAMiJ,GAAMpF,KAAKJ,KAAMV,GACxB,OACI8D,OAAO,EACPa,IAAKuB,EAIjB,QAASpC,OAAO,IAEpBlB,OAAQ,WACJ,GAAyCuD,GAArCC,KAAcxD,EAASlC,KAAKW,OAChC,KAAK8E,IAASvD,GACVwD,EAAQC,KAAKzD,EAAOuD,GAExB,OAAOC,MAGfvJ,EAAMC,GAAGwJ,OAAO9H,IAClBC,OAAO5B,MAAM0J,QACR9H,OAAO5B,OACE,kBAAVP,SAAwBA,OAAOkK,IAAMlK,OAAS,SAAUmK,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.validator.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.validator', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'validator',\n        name: 'Validator',\n        category: 'web',\n        description: 'The Validator offers an easy way to do a client-side form validation.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, NS = '.kendoValidator', INVALIDMSG = 'k-invalid-msg', invalidMsgRegExp = new RegExp(INVALIDMSG, 'i'), INVALIDINPUT = 'k-invalid', VALIDINPUT = 'k-valid', emailRegExp = /^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/i, urlRegExp = /^(https?|ftp):\\/\\/(((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:)*@)?(((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]))|((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.?)(:\\d*)?)(\\/((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)+(\\/(([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)*)*)?)?(\\?((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|[\\uE000-\\uF8FF]|\\/|\\?)*)?(\\#((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|\\/|\\?)*)?$/i, INPUTSELECTOR = ':input:not(:button,[type=submit],[type=reset],[disabled],[readonly])', CHECKBOXSELECTOR = ':checkbox:not([disabled],[readonly])', NUMBERINPUTSELECTOR = '[type=number],[type=range]', BLUR = 'blur', NAME = 'name', FORM = 'form', NOVALIDATE = 'novalidate', VALIDATE = 'validate', CHANGE = 'change', VALIDATE_INPUT = 'validateInput', proxy = $.proxy, patternMatcher = function (value, pattern) {\n                if (typeof pattern === 'string') {\n                    pattern = new RegExp('^(?:' + pattern + ')$');\n                }\n                return pattern.test(value);\n            }, matcher = function (input, selector, pattern) {\n                var value = input.val();\n                if (input.filter(selector).length && value !== '') {\n                    return patternMatcher(value, pattern);\n                }\n                return true;\n            }, hasAttribute = function (input, name) {\n                if (input.length) {\n                    return input[0].attributes[name] != null;\n                }\n                return false;\n            };\n        if (!kendo.ui.validator) {\n            kendo.ui.validator = {\n                rules: {},\n                messages: {}\n            };\n        }\n        function resolveRules(element) {\n            var resolvers = kendo.ui.validator.ruleResolvers || {}, rules = {}, name;\n            for (name in resolvers) {\n                $.extend(true, rules, resolvers[name].resolve(element));\n            }\n            return rules;\n        }\n        function decode(value) {\n            return value.replace(/&amp/g, '&amp;').replace(/&quot;/g, '\"').replace(/&#39;/g, '\\'').replace(/&lt;/g, '<').replace(/&gt;/g, '>');\n        }\n        function numberOfDecimalDigits(value) {\n            value = (value + '').split('.');\n            if (value.length > 1) {\n                return value[1].length;\n            }\n            return 0;\n        }\n        function parseHtml(text) {\n            if ($.parseHTML) {\n                return $($.parseHTML(text));\n            }\n            return $(text);\n        }\n        function searchForMessageContainer(elements, fieldName) {\n            var containers = $(), element, attr;\n            for (var idx = 0, length = elements.length; idx < length; idx++) {\n                element = elements[idx];\n                if (invalidMsgRegExp.test(element.className)) {\n                    attr = element.getAttribute(kendo.attr('for'));\n                    if (attr === fieldName) {\n                        containers = containers.add(element);\n                    }\n                }\n            }\n            return containers;\n        }\n        var Validator = Widget.extend({\n            init: function (element, options) {\n                var that = this, resolved = resolveRules(element), validateAttributeSelector = '[' + kendo.attr('validate') + '!=false]';\n                options = options || {};\n                options.rules = $.extend({}, kendo.ui.validator.rules, resolved.rules, options.rules);\n                options.messages = $.extend({}, kendo.ui.validator.messages, resolved.messages, options.messages);\n                Widget.fn.init.call(that, element, options);\n                that._errorTemplate = kendo.template(that.options.errorTemplate);\n                if (that.element.is(FORM)) {\n                    that.element.attr(NOVALIDATE, NOVALIDATE);\n                }\n                that._inputSelector = INPUTSELECTOR + validateAttributeSelector;\n                that._checkboxSelector = CHECKBOXSELECTOR + validateAttributeSelector;\n                that._errors = {};\n                that._attachEvents();\n                that._isValidated = false;\n            },\n            events: [\n                VALIDATE,\n                CHANGE,\n                VALIDATE_INPUT\n            ],\n            options: {\n                name: 'Validator',\n                errorTemplate: '<span class=\"k-widget k-tooltip k-tooltip-validation\">' + '<span class=\"k-icon k-i-warning\"> </span> #=message#</span>',\n                messages: {\n                    required: '{0} is required',\n                    pattern: '{0} is not valid',\n                    min: '{0} should be greater than or equal to {1}',\n                    max: '{0} should be smaller than or equal to {1}',\n                    step: '{0} is not valid',\n                    email: '{0} is not valid email',\n                    url: '{0} is not valid URL',\n                    date: '{0} is not valid date',\n                    dateCompare: 'End date should be greater than or equal to the start date'\n                },\n                rules: {\n                    required: function (input) {\n                        var checkbox = input.filter('[type=checkbox]').length && !input.is(':checked'), value = input.val();\n                        return !(hasAttribute(input, 'required') && (!value || value === '' || value.length === 0 || checkbox));\n                    },\n                    pattern: function (input) {\n                        if (input.filter('[type=text],[type=email],[type=url],[type=tel],[type=search],[type=password]').filter('[pattern]').length && input.val() !== '') {\n                            return patternMatcher(input.val(), input.attr('pattern'));\n                        }\n                        return true;\n                    },\n                    min: function (input) {\n                        if (input.filter(NUMBERINPUTSELECTOR + ',[' + kendo.attr('type') + '=number]').filter('[min]').length && input.val() !== '') {\n                            var min = parseFloat(input.attr('min')) || 0, val = kendo.parseFloat(input.val());\n                            return min <= val;\n                        }\n                        return true;\n                    },\n                    max: function (input) {\n                        if (input.filter(NUMBERINPUTSELECTOR + ',[' + kendo.attr('type') + '=number]').filter('[max]').length && input.val() !== '') {\n                            var max = parseFloat(input.attr('max')) || 0, val = kendo.parseFloat(input.val());\n                            return max >= val;\n                        }\n                        return true;\n                    },\n                    step: function (input) {\n                        if (input.filter(NUMBERINPUTSELECTOR + ',[' + kendo.attr('type') + '=number]').filter('[step]').length && input.val() !== '') {\n                            var min = parseFloat(input.attr('min')) || 0, step = parseFloat(input.attr('step')) || 1, val = parseFloat(input.val()), decimals = numberOfDecimalDigits(step), raise;\n                            if (decimals) {\n                                raise = Math.pow(10, decimals);\n                                return Math.floor((val - min) * raise) % (step * raise) / Math.pow(100, decimals) === 0;\n                            }\n                            return (val - min) % step === 0;\n                        }\n                        return true;\n                    },\n                    email: function (input) {\n                        return matcher(input, '[type=email],[' + kendo.attr('type') + '=email]', emailRegExp);\n                    },\n                    url: function (input) {\n                        return matcher(input, '[type=url],[' + kendo.attr('type') + '=url]', urlRegExp);\n                    },\n                    date: function (input) {\n                        if (input.filter('[type^=date],[' + kendo.attr('type') + '=date]').length && input.val() !== '') {\n                            return kendo.parseDate(input.val(), input.attr(kendo.attr('format'))) !== null;\n                        }\n                        return true;\n                    }\n                },\n                validateOnBlur: true\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.element.off(NS);\n            },\n            value: function () {\n                if (!this._isValidated) {\n                    return false;\n                }\n                return this.errors().length === 0;\n            },\n            _submit: function (e) {\n                if (!this.validate()) {\n                    e.stopPropagation();\n                    e.stopImmediatePropagation();\n                    e.preventDefault();\n                    return false;\n                }\n                return true;\n            },\n            _checkElement: function (element) {\n                var state = this.value();\n                this.validateInput(element);\n                if (this.value() !== state) {\n                    this.trigger(CHANGE);\n                }\n            },\n            _attachEvents: function () {\n                var that = this;\n                if (that.element.is(FORM)) {\n                    that.element.on('submit' + NS, proxy(that._submit, that));\n                }\n                if (that.options.validateOnBlur) {\n                    if (!that.element.is(INPUTSELECTOR)) {\n                        that.element.on(BLUR + NS, that._inputSelector, function () {\n                            that._checkElement($(this));\n                        });\n                        that.element.on('click' + NS, that._checkboxSelector, function () {\n                            that._checkElement($(this));\n                        });\n                    } else {\n                        that.element.on(BLUR + NS, function () {\n                            that._checkElement(that.element);\n                        });\n                        if (that.element.is(CHECKBOXSELECTOR)) {\n                            that.element.on('click' + NS, function () {\n                                that._checkElement(that.element);\n                            });\n                        }\n                    }\n                }\n            },\n            validate: function () {\n                var inputs;\n                var idx;\n                var result = false;\n                var length;\n                var isValid = this.value();\n                this._errors = {};\n                if (!this.element.is(INPUTSELECTOR)) {\n                    var invalid = false;\n                    inputs = this.element.find(this._inputSelector);\n                    for (idx = 0, length = inputs.length; idx < length; idx++) {\n                        if (!this.validateInput(inputs.eq(idx))) {\n                            invalid = true;\n                        }\n                    }\n                    result = !invalid;\n                } else {\n                    result = this.validateInput(this.element);\n                }\n                this.trigger(VALIDATE, { valid: result });\n                if (isValid !== result) {\n                    this.trigger(CHANGE);\n                }\n                return result;\n            },\n            validateInput: function (input) {\n                input = $(input);\n                this._isValidated = true;\n                var that = this, template = that._errorTemplate, result = that._checkValidity(input), valid = result.valid, className = '.' + INVALIDMSG, fieldName = input.attr(NAME) || '', lbl = that._findMessageContainer(fieldName).add(input.next(className).filter(function () {\n                        var element = $(this);\n                        if (element.filter('[' + kendo.attr('for') + ']').length) {\n                            return element.attr(kendo.attr('for')) === fieldName;\n                        }\n                        return true;\n                    })).hide(), messageText, wasValid = !input.attr('aria-invalid');\n                input.removeAttr('aria-invalid');\n                if (!valid) {\n                    messageText = that._extractMessage(input, result.key);\n                    that._errors[fieldName] = messageText;\n                    var messageLabel = parseHtml(template({ message: decode(messageText) }));\n                    var lblId = lbl.attr('id');\n                    that._decorateMessageContainer(messageLabel, fieldName);\n                    if (lblId) {\n                        messageLabel.attr('id', lblId);\n                    }\n                    if (!lbl.replaceWith(messageLabel).length) {\n                        messageLabel.insertAfter(input);\n                    }\n                    messageLabel.show();\n                    input.attr('aria-invalid', true);\n                } else {\n                    delete that._errors[fieldName];\n                }\n                if (wasValid !== valid) {\n                    this.trigger(VALIDATE_INPUT, {\n                        valid: valid,\n                        input: input\n                    });\n                }\n                input.toggleClass(INVALIDINPUT, !valid);\n                input.toggleClass(VALIDINPUT, valid);\n                if (kendo.widgetInstance(input)) {\n                    var inputWrap = kendo.widgetInstance(input)._inputWrapper;\n                    if (inputWrap) {\n                        inputWrap.toggleClass(INVALIDINPUT, !valid);\n                        inputWrap.toggleClass(INVALIDINPUT, !valid);\n                    }\n                }\n                return valid;\n            },\n            hideMessages: function () {\n                var that = this, className = '.' + INVALIDMSG, element = that.element;\n                if (!element.is(INPUTSELECTOR)) {\n                    element.find(className).hide();\n                } else {\n                    element.next(className).hide();\n                }\n            },\n            _findMessageContainer: function (fieldName) {\n                var locators = kendo.ui.validator.messageLocators, name, containers = $();\n                for (var idx = 0, length = this.element.length; idx < length; idx++) {\n                    containers = containers.add(searchForMessageContainer(this.element[idx].getElementsByTagName('*'), fieldName));\n                }\n                for (name in locators) {\n                    containers = containers.add(locators[name].locate(this.element, fieldName));\n                }\n                return containers;\n            },\n            _decorateMessageContainer: function (container, fieldName) {\n                var locators = kendo.ui.validator.messageLocators, name;\n                container.addClass(INVALIDMSG).attr(kendo.attr('for'), fieldName || '');\n                for (name in locators) {\n                    locators[name].decorate(container, fieldName);\n                }\n                container.attr('role', 'alert');\n            },\n            _extractMessage: function (input, ruleKey) {\n                var that = this, customMessage = that.options.messages[ruleKey], fieldName = input.attr(NAME), nonDefaultMessage;\n                if (!kendo.ui.Validator.prototype.options.messages[ruleKey]) {\n                    nonDefaultMessage = kendo.isFunction(customMessage) ? customMessage(input) : customMessage;\n                }\n                customMessage = kendo.isFunction(customMessage) ? customMessage(input) : customMessage;\n                return kendo.format(input.attr(kendo.attr(ruleKey + '-msg')) || input.attr('validationMessage') || nonDefaultMessage || input.attr('title') || customMessage || '', fieldName, input.attr(ruleKey) || input.attr(kendo.attr(ruleKey)));\n            },\n            _checkValidity: function (input) {\n                var rules = this.options.rules, rule;\n                for (rule in rules) {\n                    if (!rules[rule].call(this, input)) {\n                        return {\n                            valid: false,\n                            key: rule\n                        };\n                    }\n                }\n                return { valid: true };\n            },\n            errors: function () {\n                var results = [], errors = this._errors, error;\n                for (error in errors) {\n                    results.push(errors[error]);\n                }\n                return results;\n            }\n        });\n        kendo.ui.plugin(Validator);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}