{"version": 3, "sources": ["kendo.gantt.list.js"], "names": ["f", "define", "$", "createPlaceholders", "options", "i", "level", "spans", "className", "push", "kendoDomElement", "blurActiveElement", "activeElement", "kendo", "_activeElement", "nodeName", "toLowerCase", "blur", "window", "kendoDom", "dom", "element", "kendoTextElement", "text", "browser", "support", "mobileOS", "ui", "Widget", "extend", "outerWidth", "_outerWidth", "outerHeight", "_outerHeight", "map", "isFunction", "oldIE", "msie", "version", "keys", "titleFromField", "title", "start", "end", "percentComplete", "parentId", "id", "orderId", "STRING", "NS", "CLICK", "DOT", "SIZE_CALCULATION_TEMPLATE", "listStyles", "wrapper", "header", "alt", "rtl", "editCell", "group", "gridHeader", "gridHeaderWrap", "gridContent", "gridContentWrap", "selected", "icon", "iconCollapse", "iconExpand", "iconHidden", "iconPlaceHolder", "input", "link", "resizeHandle", "resizeHandleInner", "dropPositions", "dropTop", "dropBottom", "dropAdd", "dropMiddle", "dropDenied", "dragStatus", "dragClue", "dragClueText", "GanttList", "init", "fn", "call", "this", "columns", "length", "dataSource", "_columns", "_layout", "_domTrees", "_header", "_sortable", "_editable", "_selectable", "_draggable", "_resizable", "_attachEvents", "_adjustHeight", "bind", "headerCols", "tableCols", "resizable", "find", "content", "not", "each", "index", "width", "eq", "last", "css", "height", "parent", "destroy", "_reorderDraggable", "_tableDropArea", "_contentDropArea", "_columnResizable", "touch", "timer", "clearTimeout", "off", "levels", "name", "selectable", "editable", "that", "styles", "on", "e", "model", "_modelFromElement", "set", "get", "stopPropagation", "headerTree", "Tree", "contentTree", "field", "sortable", "column", "calculateRowHeight", "rowHeight", "table", "format", "append", "remove", "addClass", "_rowHeight", "<PERSON><PERSON><PERSON><PERSON>", "colgroup", "_cols", "thead", "role", "_ths", "style", "min<PERSON><PERSON><PERSON>", "listWidth", "render", "_render", "tasks", "tbody", "tableAttr", "tabIndex", "value", "_trs", "trigger", "attr", "ths", "data-field", "data-title", "cols", "parseInt", "task", "rows", "_levels", "idx", "summary", "data-uid", "uid", "data-level", "expanded", "join", "_tds", "l", "children", "_td", "formatedValue", "label", "resourcesField", "aria-label", "sortableInstance", "cell", "cells", "<PERSON><PERSON><PERSON><PERSON>", "total", "preventDefault", "data", "ns", "kendoColumnSorter", "change", "ctrl<PERSON>ey", "clearSelection", "select", "selectedClassName", "siblings", "removeClass", "_setDataSource", "iconSelector", "finishEdit", "_close<PERSON>ell", "mousedown", "currentTarget", "hasClass", "update", "_startEditHandler", "td", "_columnFromElement", "_editCell", "setTimeout", "keyCode", "ENTER", "key", "ESC", "_editableContainer", "kendoTouch", "filter", "touchstart", "doubletap", "initialTouch", "is", "target", "editor", "modelCopy", "_createNewModel", "toJSON", "fields", "validation", "DATATYPE", "BINDING", "FORMAT", "required", "_editableContent", "detach", "type", "test", "_extractFormat", "container", "appendTo", "kendoDateTimePicker", "kendoEditable", "clearContainer", "dateCompare", "message", "hide", "focusable", "focus", "cancelUpdate", "copy", "taskInfo", "empty", "removeData", "unbind", "Date", "getTime", "duration", "updateInfo", "drop<PERSON>ar<PERSON>", "draggedTask", "dropAllowed", "isRtl", "selector", "action", "clear", "allowDrop", "taskParent", "defineLimits", "offsetTop", "getOffset", "top", "beforeLimit", "afterLimit", "defineAction", "coordinate", "location", "command", "sibling", "prev", "next", "status", "hint", "reorder", "kendoDraggable", "distance", "holdToDrag", "ignore", "paddingLeft", "paddingRight", "lineHeight", "paddingTop", "paddingBottom", "cursorOffset", "left", "dragstart", "drag", "y", "dragend", "dragcancel", "kendoDropTargetArea", "dragenter", "toggleClass", "dragleave", "drop", "positionResizeHandle", "cellOffset", "show", "th", "position", "cellWidth", "closest", "clientX", "scrollLeft", "indicatorWidth", "columnResizeHandleWidth", "offset", "kendoResizable", "handle", "colSelector", "contentTable", "col", "add", "startLocation", "x", "columnWidth", "totalWidth", "resize", "minColumn<PERSON>idth", "delta", "resizeend", "oldWidth", "newWidth", "Math", "floor", "row", "getByUid", "tr", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,oBACH,YACA,cACA,oBACA,qBACA,uBACA,kBACDD,IACL,WAg5BE,MAh4BC,UAAUE,GA6DP,QAASC,GAAmBC,GAA5B,GAGaC,GAAOC,EAFZC,KACAC,EAAYJ,EAAQI,SACxB,KAASH,EAAI,EAAGC,EAAQF,EAAQE,MAAOD,EAAIC,EAAOD,IAC9CE,EAAME,KAAKC,EAAgB,QAAUF,UAAWA,IAEpD,OAAOD,GAEX,QAASI,KACL,GAAIC,GAAgBC,EAAMC,gBACtBF,IAA0D,SAAzCA,EAAcG,SAASC,eACxCd,EAAEU,GAAeK,OAxE5B,GACOJ,GAAQK,OAAOL,MACfM,EAAWN,EAAMO,IACjBV,EAAkBS,EAASE,QAC3BC,EAAmBH,EAASI,KAC5BC,EAAUX,EAAMY,QAAQD,QACxBE,EAAWb,EAAMY,QAAQC,SACzBC,EAAKd,EAAMc,GACXC,EAASD,EAAGC,OACZC,EAAS3B,EAAE2B,OACXC,EAAajB,EAAMkB,YACnBC,EAAcnB,EAAMoB,aACpBC,EAAMhC,EAAEgC,IACRC,EAAajC,EAAEiC,WACfC,EAAQZ,EAAQa,MAAQb,EAAQc,QAAU,EAC1CC,EAAO1B,EAAM0B,KACbC,GACAC,MAAS,QACTC,MAAS,aACTC,IAAO,WACPC,gBAAmB,SACnBC,SAAY,iBACZC,GAAM,KACNC,QAAW,YAEXC,EAAS,SACTC,EAAK,kBACLC,EAAQ,QACRC,EAAM,IACNC,EAA4B,wGAC5BC,GACAC,QAAS,6BACTC,OAAQ,WACRC,IAAK,QACLC,IAAK,QACLC,SAAU,cACVC,MAAO,mBACPC,WAAY,gBACZC,eAAgB,qBAChBC,YAAa,iBACbC,gBAAiB,iBACjBC,SAAU,mBACVC,KAAM,SACNC,aAAc,eACdC,WAAY,aACZC,WAAY,WACZC,gBAAiB,kBACjBC,MAAO,UACPC,KAAM,SACNC,aAAc,kBACdC,kBAAmB,wBACnBC,cAAe,2DACfC,QAAS,gBACTC,WAAY,kBACZC,QAAS,WACTC,WAAY,oBACZC,WAAY,aACZC,WAAY,gBACZC,SAAU,cACVC,aAAc,eAgBdC,EAAYxD,EAAGwD,UAAYvD,EAAOC,QAClCuD,KAAM,SAAU/D,EAASjB,GACrBwB,EAAOyD,GAAGD,KAAKE,KAAKC,KAAMlE,EAASjB,GACC,IAAhCmF,KAAKnF,QAAQoF,QAAQC,QACrBF,KAAKnF,QAAQoF,QAAQ/E,KAAK,SAE9B8E,KAAKG,WAAaH,KAAKnF,QAAQsF,WAC/BH,KAAKI,WACLJ,KAAKK,UACLL,KAAKM,YACLN,KAAKO,UACLP,KAAKQ,YACLR,KAAKS,YACLT,KAAKU,cACLV,KAAKW,aACLX,KAAKY,aACLZ,KAAKa,gBACLb,KAAKc,gBACLd,KAAKe,KAAK,SAAU,WAAA,GACZC,GACAC,CACAjB,MAAKnF,QAAQqG,YACbF,EAAahB,KAAKhC,OAAOmD,KAAK,OAC9BF,EAAYjB,KAAKoB,QAAQD,KAAK,OAC9BnB,KAAKhC,OAAOmD,KAAK,MAAME,IAAI,SAASC,KAAK,SAAUC,GAC/C,GAAIC,GAAQjF,EAAW5B,EAAEqF,MACzBgB,GAAWS,GAAGF,GAAOC,MAAMA,GAC3BP,EAAUQ,GAAGF,GAAOC,MAAMA,KAE9BR,EAAWU,OAAOC,IAAI,QAAS,QAC/BV,EAAUS,OAAOC,IAAI,QAAS,WAEnC,IAEPb,cAAe,WACPd,KAAKoB,SACLpB,KAAKoB,QAAQQ,OAAO5B,KAAKlE,QAAQ8F,SAAWnF,EAAYuD,KAAKhC,OAAO6D,YAG5EC,QAAS,WACLzF,EAAOyD,GAAGgC,QAAQ/B,KAAKC,MACnBA,KAAK+B,mBACL/B,KAAK+B,kBAAkBD,UAEvB9B,KAAKgC,gBACLhC,KAAKgC,eAAeF,UAEpB9B,KAAKiC,kBACLjC,KAAKiC,iBAAiBH,UAEtB9B,KAAKkC,kBACLlC,KAAKkC,iBAAiBJ,UAEtB9B,KAAKmC,OACLnC,KAAKmC,MAAML,UAEX9B,KAAKoC,OACLC,aAAarC,KAAKoC,OAEtBpC,KAAKoB,QAAQkB,IAAI5E,GACjBsC,KAAKhC,OAAOmD,KAAK,SAASmB,IAAI5E,GAC9BsC,KAAKhC,OAAOmD,KAAKvD,EAAMgC,EAAUZ,MAAMsD,IAAI5E,GAC3CsC,KAAKhC,OAAS,KACdgC,KAAKoB,QAAU,KACfpB,KAAKuC,OAAS,KACdjH,EAAMwG,QAAQ9B,KAAKlE,UAEvBjB,SACI2H,KAAM,YACNC,YAAY,EACZC,UAAU,EACVxB,WAAW,GAEfL,cAAe,WAAA,GACP8B,GAAO3C,KACPlC,EAAa8B,EAAUgD,MAC3BD,GAAKvB,QAAQyB,GAAGlF,EAAQD,EAAI,aAAeI,EAAWY,KAAO,SAAWZ,EAAWe,WAAa,IAAK,SAAUiE,GAAV,GAC7FhH,GAAUnB,EAAEqF,MACZ+C,EAAQJ,EAAKK,kBAAkBlH,EACnCiH,GAAME,IAAI,YAAaF,EAAMG,IAAI,aACjCJ,EAAEK,qBAGV7C,UAAW,WACPN,KAAKoD,WAAa,GAAIxH,GAASyH,KAAKrD,KAAKhC,OAAO,IAChDgC,KAAKsD,YAAc,GAAI1H,GAASyH,KAAKrD,KAAKoB,QAAQ,KAEtDhB,SAAU,WAAA,GACFH,GAAUD,KAAKnF,QAAQoF,QACvB8C,EAAQ,WACR/C,KAAKuD,MAAQ,GACbvD,KAAK9C,MAAQ,GACb8C,KAAK0C,UAAW,EAChB1C,KAAKwD,UAAW,EAEpBxD,MAAKC,QAAUtD,EAAIsD,EAAS,SAAUwD,GAKlC,MAJAA,SAAgBA,KAAWhG,GACvB8F,MAAOE,EACPvG,MAAOD,EAAewG,IACtBA,EACGnH,EAAO,GAAIyG,GAASU,MAGnCpD,QAAS,WAAA,GACDsC,GAAO3C,KACPnF,EAAUmF,KAAKnF,QACfiB,EAAUkE,KAAKlE,QACfgC,EAAa8B,EAAUgD,OACvBc,EAAqB,WAAA,GAGjB9B,GAFA+B,QAAmB9I,GAAQ8I,YAAclG,EAAS5C,EAAQ8I,UAAY9I,EAAQ8I,UAAY,KAC1FC,EAAQjJ,EAAEW,EAAMuI,OAAOhG,EAA2B8F,GAKtD,OAHAhB,GAAKvB,QAAQ0C,OAAOF,GACpBhC,EAASnF,EAAYmH,EAAMzC,KAAK,OAChCyC,EAAMG,SACCnC,EAEX9F,GAAQkI,SAASlG,EAAWC,SAAS+F,OAAO,eAAkBhG,EAAWO,WAAa,iBAAqBP,EAAWQ,eAAiB,kBAAmBwF,OAAO,eAAkBhG,EAAWU,gBAAkB,YAChNwB,KAAKhC,OAASlC,EAAQqF,KAAKvD,EAAME,EAAWQ,gBAC5C0B,KAAKoB,QAAUtF,EAAQqF,KAAKvD,EAAME,EAAWS,aACzC1D,EAAQ8I,YACR3D,KAAKiE,WAAaP,MAG1BnD,QAAS,WAAA,GACD2D,GAAUlE,KAAKoD,WACfe,EAGOhJ,EAAgB,WAAY,KAAM6E,KAAKoE,SAF9CC,EAGIlJ,EAAgB,SAAWmJ,KAAQ,aAAenJ,EAAgB,MAAQmJ,KAAQ,OAAStE,KAAKuE,UAFpGX,EAGIzI,EAAgB,SACpBqJ,OAAWC,SAAYzE,KAAKnF,QAAQ6J,UAAY,MAChDJ,KAAQ,SAERH,EACAE,GAEJH,GAAQS,QAAQf,KAEpBgB,QAAS,SAAUC,GAAV,GACDV,GACAW,EACAlB,EACAmB,GACAP,OAAWC,SAAYzE,KAAKnF,QAAQ6J,UAAY,MAChDM,SAAY,EACZV,KAAQ,WAERtE,MAAKiE,aACLc,EAAUP,MAAM5C,OAASiD,EAAM3E,OAASF,KAAKiE,WAAa,MAE9DjE,KAAKuC,SACGgB,MAAO,KACP0B,MAAO,IAEfd,EAAWhJ,EAAgB,WAAY,KAAM6E,KAAKoE,SAClDU,EAAQ3J,EAAgB,SAAWmJ,KAAQ,YAActE,KAAKkF,KAAKL,IACnEjB,EAAQzI,EAAgB,QAAS4J,GAC7BZ,EACAW,IAEJ9E,KAAKsD,YAAYqB,QAAQf,IACzB5D,KAAKmF,QAAQ,WAEjBZ,KAAM,WAAA,GAEEd,GACA2B,EAEKtK,EAAOoF,EAJZD,EAAUD,KAAKC,QAGfoF,IACJ,KAASvK,EAAI,EAAGoF,EAASD,EAAQC,OAAQpF,EAAIoF,EAAQpF,IACjD2I,EAASxD,EAAQnF,GACjBsK,GACIE,aAAc7B,EAAOF,MACrBgC,aAAc9B,EAAOvG,MACrBjC,UAAW2E,EAAUgD,OAAO5E,OAC5BsG,KAAQ,gBAEZe,EAAInK,KAAKC,EAAgB,KAAMiK,GAAOrJ,EAAiB0H,EAAOvG,SAQlE,OANI8C,MAAKnF,QAAQqG,WACbmE,EAAInK,KAAKC,EAAgB,MACrBF,UAAW2E,EAAUgD,OAAO5E,OAC5BsG,KAAQ,kBAGTe,GAEXjB,MAAO,WAAA,GAECX,GACAe,EACAhD,EAEK1G,EAAOoF,EALZD,EAAUD,KAAKC,QAIfuF,IACJ,KAAS1K,EAAI,EAAGoF,EAASD,EAAQC,OAAQpF,EAAIoF,EAAQpF,IACjD2I,EAASxD,EAAQnF,GACjB0G,EAAQiC,EAAOjC,MAEXgD,EADAhD,GAAiC,IAAxBiE,SAASjE,EAAO,KACfgD,OAAShD,YAAcA,KAAU/D,EAAS+D,EAAQA,EAAQ,OAE5D,KAEZgE,EAAKtK,KAAKC,EAAgB,MAAOqJ,MAKrC,OAHIxE,MAAKnF,QAAQqG,WACbsE,EAAKtK,KAAKC,EAAgB,OAASqJ,OAAShD,MAAO,UAEhDgE,GAEXN,KAAM,SAAUL,GAAV,GACEa,GAEAN,EAEArK,EAEKD,EAAOoF,EALZyF,KAEA1K,KAEA6C,EAAa8B,EAAUgD,MAC3B,KAAS9H,EAAI,EAAGoF,EAAS2E,EAAM3E,OAAQpF,EAAIoF,EAAQpF,IAC/C4K,EAAOb,EAAM/J,GACbC,EAAQiF,KAAK4F,SACTC,IAAKH,EAAKpI,SACVC,GAAImI,EAAKnI,GACTuI,QAASJ,EAAKI,UAElBV,GACIW,WAAYL,EAAKM,IACjBC,aAAclL,EACduJ,KAAQ,OAERoB,EAAKI,UACLV,EAAK,iBAAmBM,EAAKQ,UAE7BpL,EAAI,IAAM,GACVG,EAAUC,KAAK4C,EAAWG,KAE1ByH,EAAKI,SACL7K,EAAUC,KAAK4C,EAAWM,OAE1BnD,EAAUiF,SACVkF,EAAKnK,UAAYA,EAAUkL,KAAK,MAEpCR,EAAKzK,KAAK8E,KAAKoG,MACXV,KAAMA,EACNN,KAAMA,EACNrK,MAAOA,KAEXE,IAEJ,OAAO0K,IAEXS,KAAM,SAAUvL,GAAV,GAGE4I,GACK3I,EAAOuL,EAHZC,KACArG,EAAUD,KAAKC,OAEnB,KAASnF,EAAI,EAAGuL,EAAIpG,EAAQC,OAAQpF,EAAIuL,EAAGvL,IACvC2I,EAASxD,EAAQnF,GACjBwL,EAASpL,KAAK8E,KAAKuG,KACfb,KAAM7K,EAAQ6K,KACdjC,OAAQA,EACR1I,MAAOF,EAAQE,QAMvB,OAHIiF,MAAKnF,QAAQqG,WACboF,EAASpL,KAAKC,EAAgB,MAAQmJ,KAAQ,cAE3CnJ,EAAgB,KAAMN,EAAQuK,KAAMkB,IAE/CC,IAAK,SAAU1L,GAAV,GAOG2L,GACAC,EAIS3L,EAXTwL,KACAI,EAAiB1G,KAAKnF,QAAQ6L,eAC9B5I,EAAa8B,EAAUgD,OACvB8C,EAAO7K,EAAQ6K,KACfjC,EAAS5I,EAAQ4I,OACjBwB,EAAQS,EAAKxC,IAAIO,EAAOF,MAG5B,IAAIE,EAAOF,OAASmD,EAAgB,CAGhC,IAFAzB,EAAQA,MACRuB,KACS1L,EAAI,EAAGA,EAAImK,EAAM/E,OAAQpF,IAC9B0L,EAActL,KAAKI,EAAMuI,OAAO,YAAaoB,EAAMnK,GAAGoI,IAAI,QAAS+B,EAAMnK,GAAGoI,IAAI,kBAEpFsD,GAAgBA,EAAcL,KAAK,UAEnCK,GAAgB/C,EAAOI,OAASvI,EAAMuI,OAAOJ,EAAOI,OAAQoB,GAASA,CAWzE,OATqB,UAAjBxB,EAAOF,QACP+C,EAAW1L,GACPG,MAAOF,EAAQE,MACfE,UAAW6C,EAAWgB,kBAE1BwH,EAASpL,KAAKC,EAAgB,QAAUF,UAAW6C,EAAWY,KAAO,KAAOgH,EAAKI,QAAUJ,EAAKQ,SAAWpI,EAAWa,aAAeb,EAAWc,WAAad,EAAWe,eACxK4H,EAAQnL,EAAMuI,OAAO,cAAe2C,EAAed,EAAKrI,kBAE5DiJ,EAASpL,KAAKC,EAAgB,QAAUwL,aAAcF,IAAU1K,EAAiByK,MAC1ErL,EAAgB,MAAQmJ,KAAQ,YAAcgC,IAEzDV,QAAS,SAAU/K,GAAV,GAEDE,GAIKD,EAAOoF,EALZqC,EAASvC,KAAKuC,OAEduD,EAAUjL,EAAQiL,QAClBD,EAAMhL,EAAQgL,IACdtI,EAAK1C,EAAQ0C,EACjB,KAASzC,EAAI,EAAGoF,EAASqC,EAAOrC,OAAQpF,EAAIoF,EAAQpF,IAEhD,GADAC,EAAQwH,EAAOzH,GACXC,EAAMwI,OAASsC,EAOf,MANIC,IACAvD,EAAOrH,MACHqI,MAAOhG,EACP0H,MAAOlK,EAAMkK,MAAQ,IAGtBlK,EAAMkK,OAIzBzE,UAAW,WAAA,GAIHiD,GACAmD,EAEAC,EAMKhB,EAAS3F,EAZdyC,EAAO3C,KACP0G,EAAiB1G,KAAKnF,QAAQ6L,eAC9BzG,EAAUD,KAAKC,QAGf6G,EAAQ9G,KAAKhC,OAAOmD,KAAK,MAAQ7F,EAAM8J,KAAK,SAAW,KAEvD2B,EAAgB,SAAUjE,IACM,IAA5BH,EAAKxC,WAAW6G,SAAiBrE,EAAKD,UAAYC,EAAKD,SAASyC,QAAQ,cACxErC,EAAEmE,iBAGV,KAASpB,EAAM,EAAG3F,EAAS4G,EAAM5G,OAAQ2F,EAAM3F,EAAQ2F,IACnDpC,EAASxD,EAAQ4F,GACbpC,EAAOD,UAAYC,EAAOF,QAAUmD,IACpCG,EAAOC,EAAMrF,GAAGoE,GAChBe,EAAmBC,EAAKK,KAAK,qBACzBN,GACAA,EAAiB9E,UAErB+E,EAAKzB,KAAK,QAAU9J,EAAM6L,GAAK,QAAS1D,EAAOF,OAAO6D,mBAClDjH,WAAYH,KAAKG,WACjBkH,OAAQN,IAIpBD,GAAQ,MAEZpG,YAAa,WAAA,GACLiC,GAAO3C,KACPyC,EAAazC,KAAKnF,QAAQ4H,UAC1BA,IACAzC,KAAKoB,QAAQyB,GAAGlF,EAAQD,EAAI,KAAM,SAAUoF,GACxC,GAAIhH,GAAUnB,EAAEqF,KACZ2C,GAAKD,UACLC,EAAKD,SAASyC,QAAQ,YAErBrC,EAAEwE,QAGH3E,EAAK4E,iBAFL5E,EAAK6E,OAAO1L,MAO5B0L,OAAQ,SAAUvC,GAAV,GACAnJ,GAAUkE,KAAKoB,QAAQD,KAAK8D,GAC5BwC,EAAoB7H,EAAUgD,OAAOnE,QACzC,OAAI3C,GAAQoE,QACRpE,EAAQ4L,SAAS9J,EAAM6J,GAAmBE,YAAYF,GAAmBrC,KAAK,iBAAiB,GAAOhI,MAAM4G,SAASyD,GAAmBrC,KAAK,iBAAiB,OAC9JpF,MAAKmF,QAAQ,WAGVnF,KAAKoB,QAAQD,KAAKvD,EAAM6J,IAEnCF,eAAgB,WACZ,GAAI9I,GAAWuB,KAAKwH,QAChB/I,GAASyB,SACTzB,EAASkJ,YAAY/H,EAAUgD,OAAOnE,UACtCuB,KAAKmF,QAAQ,YAGrByC,eAAgB,SAAUzH,GACtBH,KAAKG,WAAaA,EAClBH,KAAKQ,aAETC,UAAW,WAAA,GACHkC,GAAO3C,KACP0C,EAAW1C,KAAKnF,QAAQ6H,SACxB5E,EAAa8B,EAAUgD,OACvBiF,EAAe,QAAU/J,EAAWY,KAAO,QAAUZ,EAAWe,WAAa,IAC7EiJ,EAAa,WACb,GAAIpF,GAAWC,EAAKD,QAChBA,KACIA,EAAStF,MACTuF,EAAKoF,aAELrF,EAASyC,QAAQ,cAIzB6C,EAAY,SAAUlF,GACtB,GAAImF,GAAgBtN,EAAEmI,EAAEmF,cACnBA,GAAcC,SAASpK,EAAWK,WACnC/C,IAGHsH,IAAYA,EAASyF,UAAW,IAGrCnI,KAAKoI,kBAAoB,SAAUtF,GAAV,GACjBuF,GAAKvF,EAAEmF,cAAgBtN,EAAEmI,EAAEmF,eAAiBnF,EAC5CW,EAASd,EAAK2F,mBAAmBD,EACjC1F,GAAKD,UAGLe,GAAUA,EAAOf,UACjBC,EAAK4F,WACD1B,KAAMwB,EACN5E,OAAQA,KAIpBd,EAAKvB,QAAQyB,GAAG,UAAYnF,EAAI,WAC5B2E,aAAaM,EAAKP,OAClBO,EAAKP,MAAQ,OACdS,GAAG,WAAanF,EAAI,WACnBiF,EAAKP,MAAQoG,WAAWV,EAAY,KACrCjF,GAAG,UAAYnF,EAAI,SAAUoF,GACxBA,EAAE2F,UAAYzL,EAAK0L,OACnB5F,EAAEmE,mBAEPpE,GAAG,QAAUnF,EAAI,SAAUoF,GAAV,GAEZ+D,GACA9D,EAFA4F,EAAM7F,EAAE2F,OAGZ,QAAQE,GACR,IAAK3L,GAAK0L,MACNtN,IACA0M,GACA,MACJ,KAAK9K,GAAK4L,IACFjG,EAAKD,WACLmE,EAAOlE,EAAKkG,mBACZ9F,EAAQJ,EAAKK,kBAAkB6D,GAC1BlE,EAAKwC,QAAQ,UACVpC,MAAOA,EACP8D,KAAMA,KAEVlE,EAAKoF,YAAW,OAM3B5L,EASDwG,EAAKR,MAAQQ,EAAKvB,QAAQ0H,YACtBC,OAAQ,KACRC,WAAY,SAAUlG,GAClBkF,EAAUlF,EAAEX,QAEhB8G,UAAW,SAAUnG,GACZnI,EAAEmI,EAAEX,MAAM+G,cAAcC,GAAGtB,IAC5BlF,EAAKyF,kBAAkBtF,EAAEX,UAGlC+E,KAAK,cAlBRvE,EAAKvB,QAAQyB,GAAG,YAAcnF,EAAI,KAAM,SAAUoF,GAC9CkF,EAAUlF,KACXD,GAAG,WAAanF,EAAI,KAAM,SAAUoF,GAC9BnI,EAAEmI,EAAEsG,QAAQD,GAAGtB,IAChBlF,EAAKyF,kBAAkBtF,OAiBvCyF,UAAW,SAAU1N,GAAV,GAgBHwO,GAfA3C,EAAiB1G,KAAKnF,QAAQ6L,eAC9B5I,EAAa8B,EAAUgD,OACvBiE,EAAOhM,EAAQgM,KACfpD,EAAS5I,EAAQ4I,OACjBV,EAAQ/C,KAAKgD,kBAAkB6D,GAC/ByC,EAAYtJ,KAAKG,WAAWoJ,gBAAgBxG,EAAMyG,UAClDjG,EAAQ+F,EAAUG,OAAOhG,EAAOF,QAAU+F,EAAU7F,EAAOF,OAC3DmG,EAAanG,EAAMmG,WACnBC,EAAWrO,EAAM8J,KAAK,QACtBwE,EAAUtO,EAAM8J,KAAK,QACrByE,EAASvO,EAAM8J,KAAK,UACpBA,GACA5C,KAAQiB,EAAOF,MACfuG,WAAYvG,EAAMmG,YAAanG,EAAMmG,WAAWI,YAAa,EAGjE,OAAIrG,GAAOF,QAAUmD,MACjBjD,GAAO4F,OAAOxC,EAAMyC,IAGxBtJ,KAAK+J,iBAAmBlD,EAAKP,WAAW0D,SACxChK,KAAK6I,mBAAqBhC,EAC1BA,EAAKK,KAAK,YAAaoC,GACH,SAAf/F,EAAM0G,MAAqC,SAAlBtP,EAAEsP,KAAK1G,IAAwBE,EAAOI,SAAU,cAAcqG,KAAKzG,EAAOI,UACpGuB,EAAKwE,GAAW,SAAWnG,EAAOF,MAClC6B,EAAKuE,GAAY,OACblG,EAAOI,SACPuB,EAAKyE,GAAUvO,EAAM6O,eAAe1G,EAAOI,SAE/CwF,EAAS,SAAUe,EAAWvP,GAC1BF,EAAE,wBAAwByK,KAAKA,GAAMiF,SAASD,GAAWE,qBAAsBzG,OAAQhJ,EAAQgJ,WAGvG7D,KAAK0C,SAAWmE,EAAK7C,SAASlG,EAAWK,UAAUoM,eAC/Cd,QACIlG,MAAOE,EAAOF,MACdM,OAAQJ,EAAOI,OACfwF,OAAQ5F,EAAO4F,QAAUA,GAE7BtG,MAAOuG,EACPkB,gBAAgB,IACjBtD,KAAK,iBACJwC,GAAcA,EAAWe,aAAe7N,EAAW8M,EAAWe,cAAgBf,EAAWgB,UACzF/P,EAAE,SAAWW,EAAM8J,KAAK,OAAS,KAAO3B,EAAOF,MAAQ,6BAA6BoH,OAAON,SAASxD,GACpGA,EAAK1F,KAAK,SAAWsC,EAAOF,MAAQ,KAAK6B,KAAK9J,EAAM8J,KAAK,mBAAoBsE,EAAWgB,UAE5F1K,KAAK0C,SAAS3B,KAAK,WAAY,SAAU+B,GACrC,GAAI8H,GAAY5K,KAAKlE,QAAQqF,KAAK,yBAAyB0J,OACvDhO,IACA+N,EAAUC,QAEd/H,EAAEmE,wBAEFjH,KAAKmF,QAAQ,QACTpC,MAAOA,EACP8D,KAAMA,KAEV7G,KAAK+H,YAAW,MAGxBA,WAAY,SAAU+C,GAAV,GACJhN,GAAa8B,EAAUgD,OACvBiE,EAAO7G,KAAK6I,mBACZ9F,EAAQ/C,KAAKgD,kBAAkB6D,GAC/BpD,EAASzD,KAAKsI,mBAAmBzB,GACjCtD,EAAQE,EAAOF,MACfwH,EAAOlE,EAAKK,KAAK,aACjB8D,IACJA,GAASzH,GAASwH,EAAK7H,IAAIK,GAC3BsD,EAAKoE,QAAQC,WAAW,aAAavD,YAAY7J,EAAWK,UAAU2F,OAAO9D,KAAK+J,kBAClF/J,KAAK0C,SAASyI,SACdnL,KAAK0C,SAASZ,UACd9B,KAAK0C,SAAW,KAChB1C,KAAK6I,mBAAqB,KAC1B7I,KAAK+J,iBAAmB,KACnBe,IACa,UAAVvH,IACAyH,EAAS5N,IAAM,GAAIgO,MAAKJ,EAAS7N,MAAMkO,UAAYtI,EAAMuI,aAE7DtL,KAAKmF,QAAQ,UACTO,KAAM3C,EACNwI,WAAYP,MAIxBrK,WAAY,WAAA,GAIJ6K,GAHA7I,EAAO3C,KACPyL,EAAc,KACdC,GAAc,EAEd5N,EAAa8B,EAAUgD,OACvB+I,EAAQrQ,EAAMY,QAAQyP,MAAM3L,KAAKlE,SACjC8P,EAAW,MAAQtQ,EAAM8J,KAAK,SAAW,aACzCyG,KACAnJ,EAAW1C,KAAKnF,QAAQ6H,SACxBoJ,EAAQ,WACRL,EAAc,KACdD,EAAa,KACbE,GAAc,EACdG,MAEAE,EAAY,SAAUrG,GAEtB,IADA,GAAI7D,GAAS6D,EACN7D,GAAQ,CACX,GAAI4J,EAAYvI,IAAI,QAAUrB,EAAOqB,IAAI,MAAO,CAC5CwI,GAAc,CACd,OAEJ7J,EAASc,EAAKxC,WAAW6L,WAAWnK,KAGxCoK,EAAe,WAAA,GACXrK,GAASjH,EAAE6Q,GAAY5J,SACvBsK,EAAY5Q,EAAM6Q,UAAUX,GAAYY,GAC5C9P,GAAOkP,GACHa,YAAaH,EAAqB,IAATtK,EACzB0K,WAAYJ,EAAqB,IAATtK,KAG5B2K,EAAe,SAAUC,GAAV,GAIXC,GACAxR,EACAyR,EACA3R,EACA4R,CAPCnB,KAGDiB,EAAWD,EAAWC,SACtBxR,EAAY6C,EAAWwB,QACvBoN,EAAU,MACV3R,EAAQ0K,SAAS+F,EAAWpG,KAAK9J,EAAM8J,KAAK,UAAW,IAEvDqH,GAAYjB,EAAWa,aACvBM,EAAUnB,EAAWoB,OACrB3R,EAAY6C,EAAWsB,QACvBsN,EAAU,iBACHD,GAAYjB,EAAWc,aAC9BK,EAAUnB,EAAWqB,OACrB5R,EAAY6C,EAAWuB,WACvBqN,EAAU,gBAEVC,GAAWlH,SAASkH,EAAQvH,KAAK9J,EAAM8J,KAAK,UAAW,MAAQrK,IAC/DE,EAAY6C,EAAWyB,YAE3BsM,EAAO5Q,UAAYA,EACnB4Q,EAAOa,QAAUA,IAEjBI,EAAS,WACT,MAAOnK,GAAKZ,kBAAkBgL,KAAKzG,SAAS1I,EAAME,EAAW2B,YAAYkI,YAAY7J,EAAWqB,eAE/FuD,IAAYA,EAASsK,WAAY,GAAStK,EAASyF,UAAW,IAGnEnI,KAAK+B,kBAAoB/B,KAAKoB,QAAQ6L,gBAClCC,SAAU,GACVC,WAAYhR,EACZiC,MAAO,YACP2K,OAAQ,eACRqE,OAAQxP,EAAME,EAAWiB,MACzBgO,KAAM,SAAU3D,GACZ,MAAOzO,GAAE,eAAiBmD,EAAWE,OAAS,IAAMF,EAAW4B,SAAW,OAAOiC,KAC7EH,MAAO,IACP6L,YAAajE,EAAOzH,IAAI,eACxB2L,aAAclE,EAAOzH,IAAI,gBACzB4L,WAAYnE,EAAOxH,SAAW,KAC9B4L,WAAYpE,EAAOzH,IAAI,cACvB8L,cAAerE,EAAOzH,IAAI,mBAC3BmC,OAAO,gBAAkBhG,EAAWY,KAAO,IAAMZ,EAAW2B,WAAa,oBAAsB3B,EAAW6B,aAAe,QAEhI+N,cACItB,QACAuB,KAAM,GAEVvD,UAAWpK,KAAKoB,QAChBwM,UAAa,SAAU9K,GACnB,GAAIJ,GAAWC,EAAKD,QACpB,OAAIA,IAAYA,EAASsK,WAAY,GAAStK,EAASyC,QAAQ,gBAC3DrC,GAAEmE,kBAGNwE,EAAc9I,EAAKK,kBAAkBF,EAAEmF,eACvCjI,KAAK+M,KAAKzG,SAAS1I,EAAME,EAAW6B,cAAc3D,KAAKyP,EAAYvI,IAAI,eACnEyI,GACA3L,KAAK+M,KAAK/I,SAASlG,EAAWI,QAGtC2P,KAAQ,SAAU/K,GACV4I,IACAa,EAAazJ,EAAEgL,GACfhB,IAAS9I,SAAS6H,EAAO5Q,aAGjC8S,QAAW,WACPjC,KAEJkC,WAAc,WACVlC,OAEL5E,KAAK,kBACRlH,KAAKgC,eAAiBhC,KAAKoB,QAAQ6M,qBAC/Bf,SAAU,EACV9O,MAAO,YACP2K,OAAQ,eACRmF,UAAa,SAAUpL,GACnB0I,EAAa1I,EAAE0I,WACfO,EAAUpJ,EAAKK,kBAAkBwI,IACjCS,IACAa,IAASqB,YAAYrQ,EAAW0B,YAAakM,IAEjD0C,UAAa,WACT1C,GAAc,EACdoB,KAEJuB,KAAQ,WAAA,GACAjF,GAASzG,EAAKK,kBAAkBwI,GAChChO,EAAU4L,EAAO5L,QACjBwN,GAAa1N,SAAU8L,EAAO9L,SAClC,IAAIoO,EAAa,CACb,OAAQG,EAAOa,SACf,IAAK,MACD1B,EAAS1N,SAAW8L,EAAO7L,EAC3B,MACJ,KAAK,gBAEGyN,EAASxN,QADT4L,EAAO9L,WAAamO,EAAYnO,UAAY8L,EAAO5L,QAAUiO,EAAYjO,QACtDA,EAAU,EAEVA,CAEvB,MACJ,KAAK,eAEGwN,EAASxN,QADT4L,EAAO9L,WAAamO,EAAYnO,UAAY8L,EAAO5L,QAAUiO,EAAYjO,QACtDA,EAEAA,EAAU,EAIrCmF,EAAKwC,QAAQ,UACTO,KAAM+F,EACNF,WAAYP,QAIzB9D,KAAK,uBACRlH,KAAKiC,iBAAmBjC,KAAKlE,QAAQmS,qBACjCf,SAAU,EACV9O,MAAO,YACP2K,OAAQnL,EAAME,EAAWS,YACzB8P,KAAQ,WAAA,GACAjF,GAASzG,EAAKK,kBAAkBL,EAAKvB,QAAQD,KAAKyK,IAClDpO,EAAU4L,EAAO5L,QACjBwN,GACA1N,SAAU,KACVE,QAAkC,OAAzBiO,EAAYnO,SAAoBE,EAAU,EAAIA,EAE3DmF,GAAKwC,QAAQ,UACTO,KAAM+F,EACNF,WAAYP,OAGrB9D,KAAK,yBAEZtG,WAAY,WAAA,GACJ+B,GAAO3C,KACPlC,EAAa8B,EAAUgD,OACvB0L,EAAuB,SAAUxL,GAAV,GAanByL,GACAC,EAbAC,EAAK9T,EAAEmI,EAAEmF,eACThJ,EAAe0D,EAAK1D,aACpByP,EAAWD,EAAGC,WACdf,EAAOe,EAASf,KAChBgB,EAAYpS,EAAWkS,GACvBrE,EAAYqE,EAAGG,QAAQ,OACvBC,EAAU/L,EAAE+L,QAAUlU,EAAEgB,QAAQmT,aAChCC,EAAiBpM,EAAK9H,QAAQmU,uBAOlC,OANArB,IAAQvD,EAAU0E,aACb7P,IACDA,EAAe0D,EAAK1D,aAAetE,EAAE,eAAiBmD,EAAWmB,aAAe,iBAAmBnB,EAAWoB,kBAAoB,eAElIqP,EAAaE,EAAGQ,SAAStB,KAAOgB,GAChCH,EAAOK,EAAUN,EAAaQ,GAAkBF,EAAUN,EAAaQ,IAK3E3E,EAAUtG,OAAO7E,OACjBA,GAAauP,OAAO7M,KAChByK,IAAKsC,EAAStC,IACduB,KAAMA,EAAOgB,EAAYI,EAAiB,EAC1CnN,OAAQnF,EAAYgS,GACpBjN,MAAwB,EAAjBuN,IACR7H,KAAK,KAAMuH,QATVxP,GAAa0L,OAWhB3K,MAAKnF,QAAQqG,YAGdlB,KAAKkC,kBACLlC,KAAKkC,iBAAiBJ,UAE1B9B,KAAKhC,OAAOmD,KAAK,SAAS0B,GAAG,YAAcnF,EAAI,KAAM4Q,GACrDtO,KAAKkC,iBAAmBlC,KAAKhC,OAAOkR,gBAChCC,OAAQvR,EAAME,EAAWmB,aACzB9B,MAAO,SAAU2F,GAAV,GACC2L,GAAK9T,EAAEmI,EAAEmF,eAAef,KAAK,MAC7BkI,EAAc,UAAYX,EAAGlN,QAAU,IACvCvD,EAAS2E,EAAK3E,OAAOmD,KAAK,SAC1BkO,EAAe1M,EAAKvB,QAAQD,KAAK,QACrCwB,GAAK7G,QAAQkI,SAAS,0BACtBhE,KAAKsP,IAAMD,EAAa/I,SAAS,YAAYnF,KAAKiO,GAAaG,IAAIvR,EAAOmD,KAAKiO,IAC/EpP,KAAKyO,GAAKA,EACVzO,KAAKwP,cAAgB1M,EAAE2M,EAAEhD,SACzBzM,KAAK0P,YAAcnT,EAAWkS,GAC9BzO,KAAK4D,MAAQ5F,EAAOuR,IAAIF,GACxBrP,KAAK2P,WAAa3P,KAAK4D,MAAMpC,QAAUjF,EAAWyB,EAAOmD,KAAK,aAElEyO,OAAQ,SAAU9M,GAAV,GACA+M,GAAiB,GACjBC,EAAQhN,EAAE2M,EAAEhD,SAAWzM,KAAKwP,aAC5BxP,MAAK0P,YAAcI,EAAQD,IAC3BC,EAAQD,EAAiB7P,KAAK0P,aAElC1P,KAAK4D,MAAMjC,KAAM8C,SAAYzE,KAAK2P,WAAaG,IAC/C9P,KAAKsP,IAAI9N,MAAMxB,KAAK0P,YAAcI,IAEtCC,UAAW,WAAA,GAEHC,GACAC,EACAxM,CAHJd,GAAK7G,QAAQ6L,YAAY,0BACrBqI,EAAWE,KAAKC,MAAMnQ,KAAK0P,aAC3BO,EAAWC,KAAKC,MAAM5T,EAAWyD,KAAKyO,KACtChL,EAASd,EAAK1C,QAAQD,KAAKyO,GAAGlN,SAClCoB,EAAKwC,QAAQ,gBACT1B,OAAQA,EACRuM,SAAUA,EACVC,SAAUA,IAEdjQ,KAAK4D,MAAQ5D,KAAKsP,IAAMtP,KAAKyO,GAAK,QAEvCvH,KAAK,oBAEZlE,kBAAmB,SAAUlH,GAAV,GACXsU,GAAMtU,EAAQ8S,QAAQ,MACtB7L,EAAQ/C,KAAKG,WAAWkQ,SAASD,EAAIhL,KAAK9J,EAAM8J,KAAK,QACzD,OAAOrC,IAEXuF,mBAAoB,SAAUxM,GAAV,GACZuM,GAAKvM,EAAQ8S,QAAQ,MACrB0B,EAAKjI,EAAGxG,SACRgE,EAAMyK,EAAGhK,WAAW/E,MAAM8G,EAC9B,OAAOrI,MAAKC,QAAQ4F,KAG5BvJ,IAAO,EAAMF,EAAGwD,WAAagD,OAAQ9E,KACvCnC,OAAOL,MAAMiV,QACR5U,OAAOL,OACE,kBAAVZ,SAAwBA,OAAO8V,IAAM9V,OAAS,SAAU+V,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.gantt.list.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.gantt.list', [\n        'kendo.dom',\n        'kendo.touch',\n        'kendo.draganddrop',\n        'kendo.columnsorter',\n        'kendo.datetimepicker',\n        'kendo.editable'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'gantt.list',\n        name: 'Gantt List',\n        category: 'web',\n        description: 'The Gantt List',\n        depends: [\n            'dom',\n            'touch',\n            'draganddrop',\n            'columnsorter',\n            'datetimepicker',\n            'editable'\n        ],\n        hidden: true\n    };\n    (function ($) {\n        var kendo = window.kendo;\n        var kendoDom = kendo.dom;\n        var kendoDomElement = kendoDom.element;\n        var kendoTextElement = kendoDom.text;\n        var browser = kendo.support.browser;\n        var mobileOS = kendo.support.mobileOS;\n        var ui = kendo.ui;\n        var Widget = ui.Widget;\n        var extend = $.extend;\n        var outerWidth = kendo._outerWidth;\n        var outerHeight = kendo._outerHeight;\n        var map = $.map;\n        var isFunction = $.isFunction;\n        var oldIE = browser.msie && browser.version < 9;\n        var keys = kendo.keys;\n        var titleFromField = {\n            'title': 'Title',\n            'start': 'Start Time',\n            'end': 'End Time',\n            'percentComplete': '% Done',\n            'parentId': 'Predecessor ID',\n            'id': 'ID',\n            'orderId': 'Order ID'\n        };\n        var STRING = 'string';\n        var NS = '.kendoGanttList';\n        var CLICK = 'click';\n        var DOT = '.';\n        var SIZE_CALCULATION_TEMPLATE = '<table style=\\'visibility: hidden;\\'>' + '<tbody>' + '<tr style=\\'height:{0}\\'>' + '<td>&nbsp;</td>' + '</tr>' + '</tbody>' + '</table>';\n        var listStyles = {\n            wrapper: 'k-treelist k-grid k-widget',\n            header: 'k-header',\n            alt: 'k-alt',\n            rtl: 'k-rtl',\n            editCell: 'k-edit-cell',\n            group: 'k-treelist-group',\n            gridHeader: 'k-grid-header',\n            gridHeaderWrap: 'k-grid-header-wrap',\n            gridContent: 'k-grid-content',\n            gridContentWrap: 'k-grid-content',\n            selected: 'k-state-selected',\n            icon: 'k-icon',\n            iconCollapse: 'k-i-collapse',\n            iconExpand: 'k-i-expand',\n            iconHidden: 'k-i-none',\n            iconPlaceHolder: 'k-icon k-i-none',\n            input: 'k-input',\n            link: 'k-link',\n            resizeHandle: 'k-resize-handle',\n            resizeHandleInner: 'k-resize-handle-inner',\n            dropPositions: 'k-i-insert-up k-i-insert-down k-i-plus k-i-insert-middle',\n            dropTop: 'k-i-insert-up',\n            dropBottom: 'k-i-insert-down',\n            dropAdd: 'k-i-plus',\n            dropMiddle: 'k-i-insert-middle',\n            dropDenied: 'k-i-cancel',\n            dragStatus: 'k-drag-status',\n            dragClue: 'k-drag-clue',\n            dragClueText: 'k-clue-text'\n        };\n        function createPlaceholders(options) {\n            var spans = [];\n            var className = options.className;\n            for (var i = 0, level = options.level; i < level; i++) {\n                spans.push(kendoDomElement('span', { className: className }));\n            }\n            return spans;\n        }\n        function blurActiveElement() {\n            var activeElement = kendo._activeElement();\n            if (activeElement && activeElement.nodeName.toLowerCase() !== 'body') {\n                $(activeElement).blur();\n            }\n        }\n        var GanttList = ui.GanttList = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                if (this.options.columns.length === 0) {\n                    this.options.columns.push('title');\n                }\n                this.dataSource = this.options.dataSource;\n                this._columns();\n                this._layout();\n                this._domTrees();\n                this._header();\n                this._sortable();\n                this._editable();\n                this._selectable();\n                this._draggable();\n                this._resizable();\n                this._attachEvents();\n                this._adjustHeight();\n                this.bind('render', function () {\n                    var headerCols;\n                    var tableCols;\n                    if (this.options.resizable) {\n                        headerCols = this.header.find('col');\n                        tableCols = this.content.find('col');\n                        this.header.find('th').not(':last').each(function (index) {\n                            var width = outerWidth($(this));\n                            headerCols.eq(index).width(width);\n                            tableCols.eq(index).width(width);\n                        });\n                        headerCols.last().css('width', 'auto');\n                        tableCols.last().css('width', 'auto');\n                    }\n                }, true);\n            },\n            _adjustHeight: function () {\n                if (this.content) {\n                    this.content.height(this.element.height() - outerHeight(this.header.parent()));\n                }\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                if (this._reorderDraggable) {\n                    this._reorderDraggable.destroy();\n                }\n                if (this._tableDropArea) {\n                    this._tableDropArea.destroy();\n                }\n                if (this._contentDropArea) {\n                    this._contentDropArea.destroy();\n                }\n                if (this._columnResizable) {\n                    this._columnResizable.destroy();\n                }\n                if (this.touch) {\n                    this.touch.destroy();\n                }\n                if (this.timer) {\n                    clearTimeout(this.timer);\n                }\n                this.content.off(NS);\n                this.header.find('thead').off(NS);\n                this.header.find(DOT + GanttList.link).off(NS);\n                this.header = null;\n                this.content = null;\n                this.levels = null;\n                kendo.destroy(this.element);\n            },\n            options: {\n                name: 'GanttList',\n                selectable: true,\n                editable: true,\n                resizable: false\n            },\n            _attachEvents: function () {\n                var that = this;\n                var listStyles = GanttList.styles;\n                that.content.on(CLICK + NS, 'td > span.' + listStyles.icon + ':not(.' + listStyles.iconHidden + ')', function (e) {\n                    var element = $(this);\n                    var model = that._modelFromElement(element);\n                    model.set('expanded', !model.get('expanded'));\n                    e.stopPropagation();\n                });\n            },\n            _domTrees: function () {\n                this.headerTree = new kendoDom.Tree(this.header[0]);\n                this.contentTree = new kendoDom.Tree(this.content[0]);\n            },\n            _columns: function () {\n                var columns = this.options.columns;\n                var model = function () {\n                    this.field = '';\n                    this.title = '';\n                    this.editable = false;\n                    this.sortable = false;\n                };\n                this.columns = map(columns, function (column) {\n                    column = typeof column === STRING ? {\n                        field: column,\n                        title: titleFromField[column]\n                    } : column;\n                    return extend(new model(), column);\n                });\n            },\n            _layout: function () {\n                var that = this;\n                var options = this.options;\n                var element = this.element;\n                var listStyles = GanttList.styles;\n                var calculateRowHeight = function () {\n                    var rowHeight = typeof options.rowHeight === STRING ? options.rowHeight : options.rowHeight + 'px';\n                    var table = $(kendo.format(SIZE_CALCULATION_TEMPLATE, rowHeight));\n                    var height;\n                    that.content.append(table);\n                    height = outerHeight(table.find('tr'));\n                    table.remove();\n                    return height;\n                };\n                element.addClass(listStyles.wrapper).append('<div class=\\'' + listStyles.gridHeader + '\\'><div class=\\'' + listStyles.gridHeaderWrap + '\\'></div></div>').append('<div class=\\'' + listStyles.gridContentWrap + '\\'></div>');\n                this.header = element.find(DOT + listStyles.gridHeaderWrap);\n                this.content = element.find(DOT + listStyles.gridContent);\n                if (options.rowHeight) {\n                    this._rowHeight = calculateRowHeight();\n                }\n            },\n            _header: function () {\n                var domTree = this.headerTree;\n                var colgroup;\n                var thead;\n                var table;\n                colgroup = kendoDomElement('colgroup', null, this._cols());\n                thead = kendoDomElement('thead', { 'role': 'rowgroup' }, [kendoDomElement('tr', { 'role': 'row' }, this._ths())]);\n                table = kendoDomElement('table', {\n                    'style': { 'minWidth': this.options.listWidth + 'px' },\n                    'role': 'grid'\n                }, [\n                    colgroup,\n                    thead\n                ]);\n                domTree.render([table]);\n            },\n            _render: function (tasks) {\n                var colgroup;\n                var tbody;\n                var table;\n                var tableAttr = {\n                    'style': { 'minWidth': this.options.listWidth + 'px' },\n                    'tabIndex': 0,\n                    'role': 'treegrid'\n                };\n                if (this._rowHeight) {\n                    tableAttr.style.height = tasks.length * this._rowHeight + 'px';\n                }\n                this.levels = [{\n                        field: null,\n                        value: 0\n                    }];\n                colgroup = kendoDomElement('colgroup', null, this._cols());\n                tbody = kendoDomElement('tbody', { 'role': 'rowgroup' }, this._trs(tasks));\n                table = kendoDomElement('table', tableAttr, [\n                    colgroup,\n                    tbody\n                ]);\n                this.contentTree.render([table]);\n                this.trigger('render');\n            },\n            _ths: function () {\n                var columns = this.columns;\n                var column;\n                var attr;\n                var ths = [];\n                for (var i = 0, length = columns.length; i < length; i++) {\n                    column = columns[i];\n                    attr = {\n                        'data-field': column.field,\n                        'data-title': column.title,\n                        className: GanttList.styles.header,\n                        'role': 'columnheader'\n                    };\n                    ths.push(kendoDomElement('th', attr, [kendoTextElement(column.title)]));\n                }\n                if (this.options.resizable) {\n                    ths.push(kendoDomElement('th', {\n                        className: GanttList.styles.header,\n                        'role': 'columnheader'\n                    }));\n                }\n                return ths;\n            },\n            _cols: function () {\n                var columns = this.columns;\n                var column;\n                var style;\n                var width;\n                var cols = [];\n                for (var i = 0, length = columns.length; i < length; i++) {\n                    column = columns[i];\n                    width = column.width;\n                    if (width && parseInt(width, 10) !== 0) {\n                        style = { style: { width: typeof width === STRING ? width : width + 'px' } };\n                    } else {\n                        style = null;\n                    }\n                    cols.push(kendoDomElement('col', style, []));\n                }\n                if (this.options.resizable) {\n                    cols.push(kendoDomElement('col', { style: { width: '1px' } }));\n                }\n                return cols;\n            },\n            _trs: function (tasks) {\n                var task;\n                var rows = [];\n                var attr;\n                var className = [];\n                var level;\n                var listStyles = GanttList.styles;\n                for (var i = 0, length = tasks.length; i < length; i++) {\n                    task = tasks[i];\n                    level = this._levels({\n                        idx: task.parentId,\n                        id: task.id,\n                        summary: task.summary\n                    });\n                    attr = {\n                        'data-uid': task.uid,\n                        'data-level': level,\n                        'role': 'row'\n                    };\n                    if (task.summary) {\n                        attr['aria-expanded'] = task.expanded;\n                    }\n                    if (i % 2 !== 0) {\n                        className.push(listStyles.alt);\n                    }\n                    if (task.summary) {\n                        className.push(listStyles.group);\n                    }\n                    if (className.length) {\n                        attr.className = className.join(' ');\n                    }\n                    rows.push(this._tds({\n                        task: task,\n                        attr: attr,\n                        level: level\n                    }));\n                    className = [];\n                }\n                return rows;\n            },\n            _tds: function (options) {\n                var children = [];\n                var columns = this.columns;\n                var column;\n                for (var i = 0, l = columns.length; i < l; i++) {\n                    column = columns[i];\n                    children.push(this._td({\n                        task: options.task,\n                        column: column,\n                        level: options.level\n                    }));\n                }\n                if (this.options.resizable) {\n                    children.push(kendoDomElement('td', { 'role': 'gridcell' }));\n                }\n                return kendoDomElement('tr', options.attr, children);\n            },\n            _td: function (options) {\n                var children = [];\n                var resourcesField = this.options.resourcesField;\n                var listStyles = GanttList.styles;\n                var task = options.task;\n                var column = options.column;\n                var value = task.get(column.field);\n                var formatedValue;\n                var label;\n                if (column.field == resourcesField) {\n                    value = value || [];\n                    formatedValue = [];\n                    for (var i = 0; i < value.length; i++) {\n                        formatedValue.push(kendo.format('{0} [{1}]', value[i].get('name'), value[i].get('formatedValue')));\n                    }\n                    formatedValue = formatedValue.join(', ');\n                } else {\n                    formatedValue = column.format ? kendo.format(column.format, value) : value;\n                }\n                if (column.field === 'title') {\n                    children = createPlaceholders({\n                        level: options.level,\n                        className: listStyles.iconPlaceHolder\n                    });\n                    children.push(kendoDomElement('span', { className: listStyles.icon + ' ' + (task.summary ? task.expanded ? listStyles.iconCollapse : listStyles.iconExpand : listStyles.iconHidden) }));\n                    label = kendo.format('{0}, {1:P0}', formatedValue, task.percentComplete);\n                }\n                children.push(kendoDomElement('span', { 'aria-label': label }, [kendoTextElement(formatedValue)]));\n                return kendoDomElement('td', { 'role': 'gridcell' }, children);\n            },\n            _levels: function (options) {\n                var levels = this.levels;\n                var level;\n                var summary = options.summary;\n                var idx = options.idx;\n                var id = options.id;\n                for (var i = 0, length = levels.length; i < length; i++) {\n                    level = levels[i];\n                    if (level.field == idx) {\n                        if (summary) {\n                            levels.push({\n                                field: id,\n                                value: level.value + 1\n                            });\n                        }\n                        return level.value;\n                    }\n                }\n            },\n            _sortable: function () {\n                var that = this;\n                var resourcesField = this.options.resourcesField;\n                var columns = this.columns;\n                var column;\n                var sortableInstance;\n                var cells = this.header.find('th[' + kendo.attr('field') + ']');\n                var cell;\n                var changeHandler = function (e) {\n                    if (that.dataSource.total() === 0 || that.editable && that.editable.trigger('validate')) {\n                        e.preventDefault();\n                    }\n                };\n                for (var idx = 0, length = cells.length; idx < length; idx++) {\n                    column = columns[idx];\n                    if (column.sortable && column.field !== resourcesField) {\n                        cell = cells.eq(idx);\n                        sortableInstance = cell.data('kendoColumnSorter');\n                        if (sortableInstance) {\n                            sortableInstance.destroy();\n                        }\n                        cell.attr('data-' + kendo.ns + 'field', column.field).kendoColumnSorter({\n                            dataSource: this.dataSource,\n                            change: changeHandler\n                        });\n                    }\n                }\n                cells = null;\n            },\n            _selectable: function () {\n                var that = this;\n                var selectable = this.options.selectable;\n                if (selectable) {\n                    this.content.on(CLICK + NS, 'tr', function (e) {\n                        var element = $(this);\n                        if (that.editable) {\n                            that.editable.trigger('validate');\n                        }\n                        if (!e.ctrlKey) {\n                            that.select(element);\n                        } else {\n                            that.clearSelection();\n                        }\n                    });\n                }\n            },\n            select: function (value) {\n                var element = this.content.find(value);\n                var selectedClassName = GanttList.styles.selected;\n                if (element.length) {\n                    element.siblings(DOT + selectedClassName).removeClass(selectedClassName).attr('aria-selected', false).end().addClass(selectedClassName).attr('aria-selected', true);\n                    this.trigger('change');\n                    return;\n                }\n                return this.content.find(DOT + selectedClassName);\n            },\n            clearSelection: function () {\n                var selected = this.select();\n                if (selected.length) {\n                    selected.removeClass(GanttList.styles.selected);\n                    this.trigger('change');\n                }\n            },\n            _setDataSource: function (dataSource) {\n                this.dataSource = dataSource;\n                this._sortable();\n            },\n            _editable: function () {\n                var that = this;\n                var editable = this.options.editable;\n                var listStyles = GanttList.styles;\n                var iconSelector = 'span.' + listStyles.icon + ':not(' + listStyles.iconHidden + ')';\n                var finishEdit = function () {\n                    var editable = that.editable;\n                    if (editable) {\n                        if (editable.end()) {\n                            that._closeCell();\n                        } else {\n                            editable.trigger('validate');\n                        }\n                    }\n                };\n                var mousedown = function (e) {\n                    var currentTarget = $(e.currentTarget);\n                    if (!currentTarget.hasClass(listStyles.editCell)) {\n                        blurActiveElement();\n                    }\n                };\n                if (!editable || editable.update === false) {\n                    return;\n                }\n                this._startEditHandler = function (e) {\n                    var td = e.currentTarget ? $(e.currentTarget) : e;\n                    var column = that._columnFromElement(td);\n                    if (that.editable) {\n                        return;\n                    }\n                    if (column && column.editable) {\n                        that._editCell({\n                            cell: td,\n                            column: column\n                        });\n                    }\n                };\n                that.content.on('focusin' + NS, function () {\n                    clearTimeout(that.timer);\n                    that.timer = null;\n                }).on('focusout' + NS, function () {\n                    that.timer = setTimeout(finishEdit, 1);\n                }).on('keydown' + NS, function (e) {\n                    if (e.keyCode === keys.ENTER) {\n                        e.preventDefault();\n                    }\n                }).on('keyup' + NS, function (e) {\n                    var key = e.keyCode;\n                    var cell;\n                    var model;\n                    switch (key) {\n                    case keys.ENTER:\n                        blurActiveElement();\n                        finishEdit();\n                        break;\n                    case keys.ESC:\n                        if (that.editable) {\n                            cell = that._editableContainer;\n                            model = that._modelFromElement(cell);\n                            if (!that.trigger('cancel', {\n                                    model: model,\n                                    cell: cell\n                                })) {\n                                that._closeCell(true);\n                            }\n                        }\n                        break;\n                    }\n                });\n                if (!mobileOS) {\n                    that.content.on('mousedown' + NS, 'td', function (e) {\n                        mousedown(e);\n                    }).on('dblclick' + NS, 'td', function (e) {\n                        if (!$(e.target).is(iconSelector)) {\n                            that._startEditHandler(e);\n                        }\n                    });\n                } else {\n                    that.touch = that.content.kendoTouch({\n                        filter: 'td',\n                        touchstart: function (e) {\n                            mousedown(e.touch);\n                        },\n                        doubletap: function (e) {\n                            if (!$(e.touch.initialTouch).is(iconSelector)) {\n                                that._startEditHandler(e.touch);\n                            }\n                        }\n                    }).data('kendoTouch');\n                }\n            },\n            _editCell: function (options) {\n                var resourcesField = this.options.resourcesField;\n                var listStyles = GanttList.styles;\n                var cell = options.cell;\n                var column = options.column;\n                var model = this._modelFromElement(cell);\n                var modelCopy = this.dataSource._createNewModel(model.toJSON());\n                var field = modelCopy.fields[column.field] || modelCopy[column.field];\n                var validation = field.validation;\n                var DATATYPE = kendo.attr('type');\n                var BINDING = kendo.attr('bind');\n                var FORMAT = kendo.attr('format');\n                var attr = {\n                    'name': column.field,\n                    'required': field.validation ? field.validation.required === true : false\n                };\n                var editor;\n                if (column.field === resourcesField) {\n                    column.editor(cell, modelCopy);\n                    return;\n                }\n                this._editableContent = cell.children().detach();\n                this._editableContainer = cell;\n                cell.data('modelCopy', modelCopy);\n                if ((field.type === 'date' || $.type(field) === 'date') && (!column.format || /H|m|s|F|g|u/.test(column.format))) {\n                    attr[BINDING] = 'value:' + column.field;\n                    attr[DATATYPE] = 'date';\n                    if (column.format) {\n                        attr[FORMAT] = kendo._extractFormat(column.format);\n                    }\n                    editor = function (container, options) {\n                        $('<input type=\"text\"/>').attr(attr).appendTo(container).kendoDateTimePicker({ format: options.format });\n                    };\n                }\n                this.editable = cell.addClass(listStyles.editCell).kendoEditable({\n                    fields: {\n                        field: column.field,\n                        format: column.format,\n                        editor: column.editor || editor\n                    },\n                    model: modelCopy,\n                    clearContainer: false\n                }).data('kendoEditable');\n                if (validation && validation.dateCompare && isFunction(validation.dateCompare) && validation.message) {\n                    $('<span ' + kendo.attr('for') + '=\"' + column.field + '\" class=\"k-invalid-msg\"/>').hide().appendTo(cell);\n                    cell.find('[name=' + column.field + ']').attr(kendo.attr('dateCompare-msg'), validation.message);\n                }\n                this.editable.bind('validate', function (e) {\n                    var focusable = this.element.find(':kendoFocusable:first').focus();\n                    if (oldIE) {\n                        focusable.focus();\n                    }\n                    e.preventDefault();\n                });\n                if (this.trigger('edit', {\n                        model: model,\n                        cell: cell\n                    })) {\n                    this._closeCell(true);\n                }\n            },\n            _closeCell: function (cancelUpdate) {\n                var listStyles = GanttList.styles;\n                var cell = this._editableContainer;\n                var model = this._modelFromElement(cell);\n                var column = this._columnFromElement(cell);\n                var field = column.field;\n                var copy = cell.data('modelCopy');\n                var taskInfo = {};\n                taskInfo[field] = copy.get(field);\n                cell.empty().removeData('modelCopy').removeClass(listStyles.editCell).append(this._editableContent);\n                this.editable.unbind();\n                this.editable.destroy();\n                this.editable = null;\n                this._editableContainer = null;\n                this._editableContent = null;\n                if (!cancelUpdate) {\n                    if (field === 'start') {\n                        taskInfo.end = new Date(taskInfo.start.getTime() + model.duration());\n                    }\n                    this.trigger('update', {\n                        task: model,\n                        updateInfo: taskInfo\n                    });\n                }\n            },\n            _draggable: function () {\n                var that = this;\n                var draggedTask = null;\n                var dropAllowed = true;\n                var dropTarget;\n                var listStyles = GanttList.styles;\n                var isRtl = kendo.support.isRtl(this.element);\n                var selector = 'tr[' + kendo.attr('level') + ' = 0]:last';\n                var action = {};\n                var editable = this.options.editable;\n                var clear = function () {\n                    draggedTask = null;\n                    dropTarget = null;\n                    dropAllowed = true;\n                    action = {};\n                };\n                var allowDrop = function (task) {\n                    var parent = task;\n                    while (parent) {\n                        if (draggedTask.get('id') === parent.get('id')) {\n                            dropAllowed = false;\n                            break;\n                        }\n                        parent = that.dataSource.taskParent(parent);\n                    }\n                };\n                var defineLimits = function () {\n                    var height = $(dropTarget).height();\n                    var offsetTop = kendo.getOffset(dropTarget).top;\n                    extend(dropTarget, {\n                        beforeLimit: offsetTop + height * 0.25,\n                        afterLimit: offsetTop + height * 0.75\n                    });\n                };\n                var defineAction = function (coordinate) {\n                    if (!dropTarget) {\n                        return;\n                    }\n                    var location = coordinate.location;\n                    var className = listStyles.dropAdd;\n                    var command = 'add';\n                    var level = parseInt(dropTarget.attr(kendo.attr('level')), 10);\n                    var sibling;\n                    if (location <= dropTarget.beforeLimit) {\n                        sibling = dropTarget.prev();\n                        className = listStyles.dropTop;\n                        command = 'insert-before';\n                    } else if (location >= dropTarget.afterLimit) {\n                        sibling = dropTarget.next();\n                        className = listStyles.dropBottom;\n                        command = 'insert-after';\n                    }\n                    if (sibling && parseInt(sibling.attr(kendo.attr('level')), 10) === level) {\n                        className = listStyles.dropMiddle;\n                    }\n                    action.className = className;\n                    action.command = command;\n                };\n                var status = function () {\n                    return that._reorderDraggable.hint.children(DOT + listStyles.dragStatus).removeClass(listStyles.dropPositions);\n                };\n                if (!editable || editable.reorder === false || editable.update === false) {\n                    return;\n                }\n                this._reorderDraggable = this.content.kendoDraggable({\n                    distance: 10,\n                    holdToDrag: mobileOS,\n                    group: 'listGroup',\n                    filter: 'tr[data-uid]',\n                    ignore: DOT + listStyles.input,\n                    hint: function (target) {\n                        return $('<div class=\"' + listStyles.header + ' ' + listStyles.dragClue + '\"/>').css({\n                            width: 300,\n                            paddingLeft: target.css('paddingLeft'),\n                            paddingRight: target.css('paddingRight'),\n                            lineHeight: target.height() + 'px',\n                            paddingTop: target.css('paddingTop'),\n                            paddingBottom: target.css('paddingBottom')\n                        }).append('<span class=\"' + listStyles.icon + ' ' + listStyles.dragStatus + '\" /><span class=\"' + listStyles.dragClueText + '\"/>');\n                    },\n                    cursorOffset: {\n                        top: -20,\n                        left: 0\n                    },\n                    container: this.content,\n                    'dragstart': function (e) {\n                        var editable = that.editable;\n                        if (editable && editable.reorder !== false && editable.trigger('validate')) {\n                            e.preventDefault();\n                            return;\n                        }\n                        draggedTask = that._modelFromElement(e.currentTarget);\n                        this.hint.children(DOT + listStyles.dragClueText).text(draggedTask.get('title'));\n                        if (isRtl) {\n                            this.hint.addClass(listStyles.rtl);\n                        }\n                    },\n                    'drag': function (e) {\n                        if (dropAllowed) {\n                            defineAction(e.y);\n                            status().addClass(action.className);\n                        }\n                    },\n                    'dragend': function () {\n                        clear();\n                    },\n                    'dragcancel': function () {\n                        clear();\n                    }\n                }).data('kendoDraggable');\n                this._tableDropArea = this.content.kendoDropTargetArea({\n                    distance: 0,\n                    group: 'listGroup',\n                    filter: 'tr[data-uid]',\n                    'dragenter': function (e) {\n                        dropTarget = e.dropTarget;\n                        allowDrop(that._modelFromElement(dropTarget));\n                        defineLimits();\n                        status().toggleClass(listStyles.dropDenied, !dropAllowed);\n                    },\n                    'dragleave': function () {\n                        dropAllowed = true;\n                        status();\n                    },\n                    'drop': function () {\n                        var target = that._modelFromElement(dropTarget);\n                        var orderId = target.orderId;\n                        var taskInfo = { parentId: target.parentId };\n                        if (dropAllowed) {\n                            switch (action.command) {\n                            case 'add':\n                                taskInfo.parentId = target.id;\n                                break;\n                            case 'insert-before':\n                                if (target.parentId === draggedTask.parentId && target.orderId > draggedTask.orderId) {\n                                    taskInfo.orderId = orderId - 1;\n                                } else {\n                                    taskInfo.orderId = orderId;\n                                }\n                                break;\n                            case 'insert-after':\n                                if (target.parentId === draggedTask.parentId && target.orderId > draggedTask.orderId) {\n                                    taskInfo.orderId = orderId;\n                                } else {\n                                    taskInfo.orderId = orderId + 1;\n                                }\n                                break;\n                            }\n                            that.trigger('update', {\n                                task: draggedTask,\n                                updateInfo: taskInfo\n                            });\n                        }\n                    }\n                }).data('kendoDropTargetArea');\n                this._contentDropArea = this.element.kendoDropTargetArea({\n                    distance: 0,\n                    group: 'listGroup',\n                    filter: DOT + listStyles.gridContent,\n                    'drop': function () {\n                        var target = that._modelFromElement(that.content.find(selector));\n                        var orderId = target.orderId;\n                        var taskInfo = {\n                            parentId: null,\n                            orderId: draggedTask.parentId !== null ? orderId + 1 : orderId\n                        };\n                        that.trigger('update', {\n                            task: draggedTask,\n                            updateInfo: taskInfo\n                        });\n                    }\n                }).data('kendoDropTargetArea');\n            },\n            _resizable: function () {\n                var that = this;\n                var listStyles = GanttList.styles;\n                var positionResizeHandle = function (e) {\n                    var th = $(e.currentTarget);\n                    var resizeHandle = that.resizeHandle;\n                    var position = th.position();\n                    var left = position.left;\n                    var cellWidth = outerWidth(th);\n                    var container = th.closest('div');\n                    var clientX = e.clientX + $(window).scrollLeft();\n                    var indicatorWidth = that.options.columnResizeHandleWidth;\n                    left += container.scrollLeft();\n                    if (!resizeHandle) {\n                        resizeHandle = that.resizeHandle = $('<div class=\"' + listStyles.resizeHandle + '\"><div class=\"' + listStyles.resizeHandleInner + '\" /></div>');\n                    }\n                    var cellOffset = th.offset().left + cellWidth;\n                    var show = clientX > cellOffset - indicatorWidth && clientX < cellOffset + indicatorWidth;\n                    if (!show) {\n                        resizeHandle.hide();\n                        return;\n                    }\n                    container.append(resizeHandle);\n                    resizeHandle.show().css({\n                        top: position.top,\n                        left: left + cellWidth - indicatorWidth - 1,\n                        height: outerHeight(th),\n                        width: indicatorWidth * 3\n                    }).data('th', th);\n                };\n                if (!this.options.resizable) {\n                    return;\n                }\n                if (this._columnResizable) {\n                    this._columnResizable.destroy();\n                }\n                this.header.find('thead').on('mousemove' + NS, 'th', positionResizeHandle);\n                this._columnResizable = this.header.kendoResizable({\n                    handle: DOT + listStyles.resizeHandle,\n                    start: function (e) {\n                        var th = $(e.currentTarget).data('th');\n                        var colSelector = 'col:eq(' + th.index() + ')';\n                        var header = that.header.find('table');\n                        var contentTable = that.content.find('table');\n                        that.element.addClass('k-grid-column-resizing');\n                        this.col = contentTable.children('colgroup').find(colSelector).add(header.find(colSelector));\n                        this.th = th;\n                        this.startLocation = e.x.location;\n                        this.columnWidth = outerWidth(th);\n                        this.table = header.add(contentTable);\n                        this.totalWidth = this.table.width() - outerWidth(header.find('th:last'));\n                    },\n                    resize: function (e) {\n                        var minColumnWidth = 11;\n                        var delta = e.x.location - this.startLocation;\n                        if (this.columnWidth + delta < minColumnWidth) {\n                            delta = minColumnWidth - this.columnWidth;\n                        }\n                        this.table.css({ 'minWidth': this.totalWidth + delta });\n                        this.col.width(this.columnWidth + delta);\n                    },\n                    resizeend: function () {\n                        that.element.removeClass('k-grid-column-resizing');\n                        var oldWidth = Math.floor(this.columnWidth);\n                        var newWidth = Math.floor(outerWidth(this.th));\n                        var column = that.columns[this.th.index()];\n                        that.trigger('columnResize', {\n                            column: column,\n                            oldWidth: oldWidth,\n                            newWidth: newWidth\n                        });\n                        this.table = this.col = this.th = null;\n                    }\n                }).data('kendoResizable');\n            },\n            _modelFromElement: function (element) {\n                var row = element.closest('tr');\n                var model = this.dataSource.getByUid(row.attr(kendo.attr('uid')));\n                return model;\n            },\n            _columnFromElement: function (element) {\n                var td = element.closest('td');\n                var tr = td.parent();\n                var idx = tr.children().index(td);\n                return this.columns[idx];\n            }\n        });\n        extend(true, ui.GanttList, { styles: listStyles });\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}