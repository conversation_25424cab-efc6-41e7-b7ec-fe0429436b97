/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.filebrowser.min",["kendo.listview.min","kendo.dropdownlist.min","kendo.upload.min"],e)}(function(){return function(e,t){function a(e,t,a){var i,r;e.on("dragenter"+S,function(){t(),r=new Date,i||(i=setInterval(function(){var e=new Date-r;e>100&&(a(),clearInterval(i),i=null)},100))}).on("dragover"+S,function(){r=new Date})}function i(e,a){return e!==t&&e.match(/\/$/)||(e=(e||"")+"/"),e+a}function r(e){if(!e)return"";var t=" bytes";return e>=1073741824?(t=" GB",e/=1073741824):e>=1048576?(t=" MB",e/=1048576):e>=1024&&(t=" KB",e/=1024),Math.round(100*e)/100+t}function o(e,t){var a=e[t];return p(a)?a.from||a.field||t:a}var n,s,l,d,u=window.kendo,c=u.ui.Widget,p=e.isPlainObject,f=e.proxy,h=e.extend,v=u.support.placeholder,m=u.support.browser,k=u.isFunction,_=/(^\/|\/$)/g,w="change",g="apply",b="error",y="click",S=".kendoFileBrowser",x=".kendoBreadcrumbs",T=".kendoSearchBox",B="name",F="size",C="type",D={field:C,dir:"asc"},z=u.template('<li class="k-tile-empty"><strong>${text}</strong></li>'),V='<div class="k-widget k-filebrowser-toolbar k-header k-floatwrap"><div class="k-toolbar-wrap"># if (showUpload) { # <div class="k-widget k-upload"><div class="k-button k-button-icontext k-upload-button"><span class="k-icon k-i-plus"></span>#=messages.uploadFile#<input type="file" name="file" /></div></div># } ## if (showCreate) { #<button type="button" class="k-button k-button-icon"><span class="k-icon k-i-folder-add" /></button># } ## if (showDelete) { #<button type="button" class="k-button k-button-icon k-state-disabled"><span class="k-icon k-i-close" /></button>&nbsp;# } #</div><div class="k-tiles-arrange"><label>#=messages.orderBy#: <select /></label></div></div>';h(!0,u.data,{schemas:{filebrowser:{data:function(e){return e.items||e||[]},model:{id:"name",fields:{name:"name",size:"size",type:"type"}}}}}),h(!0,u.data,{transports:{filebrowser:u.data.RemoteTransport.extend({init:function(t){u.data.RemoteTransport.fn.init.call(this,e.extend(!0,{},this.options,t))},_call:function(t,a){a.data=e.extend({},a.data,{path:this.options.path()}),k(this.options[t])?this.options[t].call(this,a):u.data.RemoteTransport.fn[t].call(this,a)},read:function(e){this._call("read",e)},create:function(e){this._call("create",e)},destroy:function(e){this._call("destroy",e)},update:function(){},options:{read:{type:"POST"},update:{type:"POST"},create:{type:"POST"},destroy:{type:"POST"}}})}}),n=m.msie&&m.version<8?function(e){return e.offsetTop}:function(t){return t.offsetTop-e(t).height()},s=c.extend({init:function(e,t){var a=this;t=t||{},c.fn.init.call(a,e,t),a.element.addClass("k-filebrowser"),a.element.on(y+S,".k-filebrowser-toolbar button:not(.k-state-disabled):has(.k-i-close)",f(a._deleteClick,a)).on(y+S,".k-filebrowser-toolbar button:not(.k-state-disabled):has(.k-i-folder-add)",f(a._addClick,a)).on("keydown"+S,"li.k-state-selected input",f(a._directoryKeyDown,a)).on("blur"+S,"li.k-state-selected input",f(a._directoryBlur,a)),a._dataSource(),a.refresh(),a.path(a.options.path)},options:{name:"FileBrowser",messages:{uploadFile:"Upload",orderBy:"Arrange by",orderByName:"Name",orderBySize:"Size",directoryNotFound:"A directory with this name was not found.",emptyFolder:"Empty Folder",deleteFile:'Are you sure you want to delete "{0}"?',invalidFileType:'The selected file "{0}" is not valid. Supported file types are {1}.',overwriteFile:'A file with name "{0}" already exists in the current directory. Do you want to overwrite it?',dropFilesHere:"drop file here to upload",search:"Search"},transport:{},path:"/",fileTypes:"*.*"},events:[b,w,g],destroy:function(){var e=this;c.fn.destroy.call(e),e.dataSource.unbind(b,e._errorHandler),e.element.add(e.list).add(e.toolbar).off(S),u.destroy(e.element)},value:function(){var e,t=this,a=t._selectedItem(),r=t.options.transport.fileUrl;if(a&&"f"===a.get(C))return e=i(t.path(),a.get(B)).replace(_,""),r&&(e=k(r)?r(e):u.format(r,encodeURIComponent(e))),e},_selectedItem:function(){var e=this.listView,t=e.select();if(t.length)return this.dataSource.getByUid(t.attr(u.attr("uid")))},_toolbar:function(){var t=this,a=u.template(V),i=t.options.messages,r=[{text:i.orderByName,value:"name"},{text:i.orderBySize,value:"size"}];t.toolbar=e(a({messages:i,showUpload:t.options.transport.uploadUrl,showCreate:t.options.transport.create,showDelete:t.options.transport.destroy})).appendTo(t.element).find(".k-upload input").kendoUpload({multiple:!1,localization:{dropFilesHere:i.dropFilesHere},async:{saveUrl:t.options.transport.uploadUrl,autoUpload:!0},upload:f(t._fileUpload,t),error:function(e){t._error({xhr:e.XMLHttpRequest,status:"error"})}}).end(),t.upload=t.toolbar.find(".k-upload input").data("kendoUpload"),t.arrangeBy=t.toolbar.find(".k-tiles-arrange select").kendoDropDownList({dataSource:r,dataTextField:"text",dataValueField:"value",change:function(){t.orderBy(this.value())}}).data("kendoDropDownList"),t._attachDropzoneEvents()},_attachDropzoneEvents:function(){var t=this;t.options.transport.uploadUrl&&(a(e(document.documentElement),e.proxy(t._dropEnter,t),e.proxy(t._dropLeave,t)),t._scrollHandler=f(t._positionDropzone,t))},_dropEnter:function(){this._positionDropzone(),e(document).on("scroll"+S,this._scrollHandler)},_dropLeave:function(){this._removeDropzone(),e(document).off("scroll"+S,this._scrollHandler)},_positionDropzone:function(){var e=this,t=e.element,a=t.offset();e.toolbar.find(".k-dropzone").addClass("k-filebrowser-dropzone").offset(a).css({width:t[0].clientWidth,height:t[0].clientHeight,lineHeight:t[0].clientHeight+"px"})},_removeDropzone:function(){this.toolbar.find(".k-dropzone").removeClass("k-filebrowser-dropzone").css({width:"",height:"",lineHeight:"",top:"",left:""})},_deleteClick:function(){var e=this,t=e.listView.select(),a=u.format(e.options.messages.deleteFile,t.find("strong").text());t.length&&e._showMessage(a,"confirm")&&e.listView.remove(t)},_addClick:function(){this.createDirectory()},_getFieldName:function(e){return o(this.dataSource.reader.model.fields,e)},_fileUpload:function(e){var t,a=this,i=a.options,r=i.fileTypes,o=RegExp(("("+r.split(",").join(")|(")+")").replace(/\*\./g,".*."),"i"),n=e.files[0].name,s=e.files[0].size,l=B,d=F;o.test(n)?(e.data={path:a.path()},t=a._createFile(n,s),t?a.upload.one("success",function(e){var i=a._insertFileToList(t);i._override&&(i.set(l,e.response[a._getFieldName(l)]),i.set(d,e.response[a._getFieldName(d)]),a.listView.dataSource.pushUpdate(i)),a._tiles=a.listView.items().filter("["+u.attr("type")+"=f]")}):e.preventDefault()):(e.preventDefault(),a._showMessage(u.format(i.messages.invalidFileType,n,r)))},_findFile:function(e){var t,a,i,r=this.dataSource.data(),o=C,n=B;for(e=e.toLowerCase(),t=0,i=r.length;t<i;t++)if("f"===r[t].get(o)&&r[t].get(n).toLowerCase()===e){a=r[t];break}return a},_createFile:function(e,t){var a=this,i={},r=C,o=a._findFile(e);return o?a._showMessage(u.format(a.options.messages.overwriteFile,e),"confirm")?(o._override=!0,o):null:(i[r]="f",i[B]=e,i[F]=t,i)},_insertFileToList:function(e){var t,a,i,r,o;if(e._override)return e;for(a=this.dataSource,i=a.view(),r=0,o=i.length;r<o;r++)if("f"===i[r].get(C)){t=r;break}return a.insert(++t,e)},createDirectory:function(){var e,t,a=this,i=0,r=C,o=B,n=a.dataSource.data(),s=a._nameDirectory(),l=new a.dataSource.reader.model;for(e=0,t=n.length;e<t;e++)"d"===n[e].get(r)&&(i=e);l.set(r,"d"),l.set(o,s),a.listView.one("dataBound",function(){var e=a.listView.items().filter("["+u.attr("uid")+"="+l.uid+"]");e.length&&this.edit(e),this.element.scrollTop(e.attr("offsetTop")-this.element[0].offsetHeight),setTimeout(function(){a.listView.element.find(".k-edit-item input").select()})}).one("save",function(e){var t=e.model.get(o);t?e.model.set(o,a._nameExists(t,l.uid)?a._nameDirectory():t):e.model.set(o,s)}),a.dataSource.insert(++i,l)},_directoryKeyDown:function(e){13==e.keyCode&&e.currentTarget.blur()},_directoryBlur:function(){this.listView.save()},_nameExists:function(e,t){var a,i,r=this.dataSource.data(),o=C,n=B;for(a=0,i=r.length;a<i;a++)if("d"===r[a].get(o)&&r[a].get(n).toLowerCase()===e.toLowerCase()&&r[a].uid!==t)return!0;return!1},_nameDirectory:function(){var t,a,i,r="New folder",o=this.dataSource.data(),n=[],s=C,l=B;for(a=0,i=o.length;a<i;a++)"d"===o[a].get(s)&&o[a].get(l).toLowerCase().indexOf(r.toLowerCase())>-1&&n.push(o[a].get(l));if(e.inArray(r,n)>-1){a=2;do t=r+" ("+a+")",a++;while(e.inArray(t,n)>-1);r=t}return r},orderBy:function(e){this.dataSource.sort([{field:C,dir:"asc"},{field:e,dir:"asc"}])},search:function(e){this.dataSource.filter({field:B,operator:"contains",value:e})},_content:function(){var t=this;t.list=e('<ul class="k-reset k-floats k-tiles" />').appendTo(t.element).on("dblclick"+S,"li",f(t._dblClick,t)),t.listView=new u.ui.ListView(t.list,{dataSource:t.dataSource,template:t._itemTmpl(),editTemplate:t._editTmpl(),selectable:!0,autoBind:!1,dataBinding:function(e){t.toolbar.find(".k-i-close").parent().addClass("k-state-disabled"),"remove"!==e.action&&"sync"!==e.action||(e.preventDefault(),u.ui.progress(t.listView.element,!1))},dataBound:function(){t.dataSource.view().length?t._tiles=this.items().filter("["+u.attr("type")+"=f]"):this.wrapper.append(z({text:t.options.messages.emptyFolder}))},change:f(t._listViewChange,t)})},_dblClick:function(t){var a,r=this,o=e(t.currentTarget);o.hasClass("k-edit-item")&&r._directoryBlur(),o.filter("["+u.attr("type")+"=d]").length?(a=r.dataSource.getByUid(o.attr(u.attr("uid"))),a&&(r.path(i(r.path(),a.get(B))),r.breadcrumbs.value(r.path()))):o.filter("["+u.attr("type")+"=f]").length&&r.trigger(g)},_listViewChange:function(){var e=this._selectedItem();e&&(this.toolbar.find(".k-i-close").parent().removeClass("k-state-disabled"),this.trigger(w,{selected:e}))},_dataSource:function(){var e,t=this,a=t.options,i=a.transport,r=h({},D),o={field:B,dir:"asc"},n={type:i.type||"filebrowser",sort:[r,o]};p(i)&&(i.path=f(t.path,t),n.transport=i),p(a.schema)?n.schema=a.schema:i.type&&p(u.data.schemas[i.type])&&(e=u.data.schemas[i.type]),t.dataSource&&t._errorHandler?t.dataSource.unbind(b,t._errorHandler):t._errorHandler=f(t._error,t),t.dataSource=u.data.DataSource.create(n).bind(b,t._errorHandler)},_navigation:function(){var t=this,a=e('<div class="k-floatwrap"><input/><input/></div>').appendTo(this.element);t.breadcrumbs=a.find("input:first").kendoBreadcrumbs({value:t.options.path,change:function(){t.path(this.value())}}).data("kendoBreadcrumbs"),t.searchBox=a.parent().find("input:last").kendoSearchBox({label:t.options.messages.search,change:function(){t.search(this.value())}}).data("kendoSearchBox")},_error:function(e){var t,a,i=this;i.trigger(b,e)||(t=e.xhr.status,"error"==e.status?"404"==t?i._showMessage(i.options.messages.directoryNotFound):"0"!=t&&i._showMessage("Error! The requested URL returned "+t+" - "+e.xhr.statusText):"timeout"==t&&i._showMessage("Error! Server timeout."),a=i.dataSource,a.hasChanges()&&a.cancelChanges())},_showMessage:function(e,t){return window[t||"alert"](e)},refresh:function(){var e=this;e._navigation(),e._toolbar(),e._content()},_editTmpl:function(){var e='<li class="k-tile k-state-selected" '+u.attr("uid")+'="#=uid#" ';return e+=u.attr("type")+'="${'+C+'}">',e+="#if("+C+' == "d") { #',e+='<div class="k-thumb"><span class="k-icon k-i-folder"></span></div>',e+="#}else{#",e+='<div class="k-thumb"><span class="k-icon k-i-loading"></span></div>',e+="#}#",e+="#if("+C+' == "d") { #',e+='<input class="k-input" '+u.attr("bind")+'="value:'+B+'"/>',e+="#}#",e+="</li>",f(u.template(e),{sizeFormatter:r})},_itemTmpl:function(){var e='<li class="k-tile" '+u.attr("uid")+'="#=uid#" ';return e+=u.attr("type")+'="${'+C+'}">',e+="#if("+C+' == "d") { #',e+='<div class="k-thumb"><span class="k-icon k-i-folder"></span></div>',e+="#}else{#",e+='<div class="k-thumb"><span class="k-icon k-i-file"></span></div>',e+="#}#",e+="<strong>${"+B+"}</strong>",e+="#if("+C+' == "f") { # <span class="k-filesize">${this.sizeFormatter('+F+")}</span> #}#",e+="</li>",f(u.template(e),{sizeFormatter:r})},path:function(e){var a=this,i=a._path||"";return e!==t?(a._path=e.replace(_,"")+"/",a.dataSource.read({path:a._path}),t):(i&&(i=i.replace(_,"")),"/"===i||""===i?"":i+"/")}}),l=c.extend({init:function(e,t){var a=this;t=t||{},c.fn.init.call(a,e,t),v&&a.element.attr("placeholder",a.options.label),a._wrapper(),a.element.on("keydown"+T,f(a._keydown,a)).on("change"+T,f(a._updateValue,a)),a.wrapper.on(y+T,"a",f(a._click,a)),v||a.element.on("focus"+T,f(a._focus,a)).on("blur"+T,f(a._blur,a))},options:{name:"SearchBox",label:"Search",value:""},events:[w],destroy:function(){var e=this;e.wrapper.add(e.element).add(e.label).off(T),c.fn.destroy.call(e)},_keydown:function(e){13===e.keyCode&&this._updateValue()},_click:function(e){e.preventDefault(),this._updateValue()},_updateValue:function(){var e=this,t=e.element.val();t!==e.value()&&(e.value(t),e.trigger(w))},_blur:function(){this._updateValue(),this._toggleLabel()},_toggleLabel:function(){v||this.label.toggle(!this.element.val())},_focus:function(){this.label.hide()},_wrapper:function(){var t=this.element,a=t.parents(".k-search-wrap");t[0].style.width="",t.addClass("k-input"),a.length||(a=t.wrap(e('<div class="k-widget k-search-wrap k-textbox"/>')).parent(),v||e('<label style="display:block">'+this.options.label+"</label>").insertBefore(t),e('<a href="#" class="k-icon k-i-zoom k-search"/>').appendTo(a)),this.wrapper=a,this.label=a.find(">label")},value:function(e){var a=this;return e!==t?(a.options.value=e,a.element.val(e),a._toggleLabel(),t):a.options.value}}),d=c.extend({init:function(e,t){var a=this;t=t||{},c.fn.init.call(a,e,t),a._wrapper(),a.wrapper.on("focus"+x,"input",f(a._focus,a)).on("blur"+x,"input",f(a._blur,a)).on("keydown"+x,"input",f(a._keydown,a)).on(y+x,"a.k-i-arrow-60-up:first",f(a._rootClick,a)).on(y+x,"a:not(.k-i-arrow-60-up)",f(a._click,a)),a.value(a.options.value)},options:{name:"Breadcrumbs",gap:50},events:[w],destroy:function(){var e=this;c.fn.destroy.call(e),e.wrapper.add(e.wrapper.find("input")).add(e.wrapper.find("a")).off(x)},_update:function(e){e="/"===(e||"").charAt(0)?e:"/"+(e||""),e!==this.value()&&(this.value(e),this.trigger(w))},_click:function(t){t.preventDefault(),this._update(this._path(e(t.target).prevAll("a:not(.k-i-arrow-60-up)").addBack()))},_rootClick:function(e){e.preventDefault(),this._update("")},_focus:function(){var e=this,t=e.element;e.overlay.hide(),e.element.val(e.value()),setTimeout(function(){t.select()})},_blur:function(){if(!this.overlay.is(":visible")){var e=this,t=e.element,a=t.val().replace(/\/{2,}/g,"/");e.overlay.show(),t.val(""),e._update(a)}},_keydown:function(e){var t=this;13===e.keyCode&&(t._blur(),setTimeout(function(){t.overlay.find("a:first").focus()}))},_wrapper:function(){var t,a=this.element,i=a.parents(".k-breadcrumbs");a[0].style.width="",a.addClass("k-input"),i.length||(i=a.wrap(e('<div class="k-widget k-breadcrumbs k-textbox"/>')).parent()),t=i.find(".k-breadcrumbs-wrap"),t.length||(t=e('<div class="k-breadcrumbs-wrap"/>').appendTo(i)),this.wrapper=i,this.overlay=t},refresh:function(){var a,i,r,o,n="",s=this.value();for(s!==t&&s.match(/^\//)||(s="/"+(s||"")),a=s.split("/"),r=0,o=a.length;r<o;r++)i=a[r],i&&(n||(n+='<a href="#" class="k-icon k-i-arrow-60-up" title="Go to parent folder"></a>'),n+='<a class="k-link" href="#">'+a[r]+"</a>",n+='<span class="k-icon k-i-arrow-60-right" title="Go to child folder"></span>');this.overlay.empty().append(e(n)),this._adjustSectionWidth()},_adjustSectionWidth:function(){var t,a=this,i=a.wrapper,r=i.width()-a.options.gap,o=a.overlay.find("a");o.each(function(a){t=e(this),t.parent().width()>r&&(a==o.length-1?t.width(r):t.prev().addBack().hide())})},value:function(e){return e!==t?(this._value=e.replace(/\/{2,}/g,"/"),this.refresh(),t):this._value},_path:function(t){return"/"+e.map(t,function(t){return e(t).text()}).join("/")}}),u.ui.plugin(s),u.ui.plugin(d),u.ui.plugin(l)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,a){(a||t)()});
//# sourceMappingURL=kendo.filebrowser.min.js.map
