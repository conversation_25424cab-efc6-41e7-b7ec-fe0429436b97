{"version": 3, "sources": ["kendo.upload.js"], "names": ["f", "define", "$", "undefined", "getFileName", "input", "map", "inputFiles", "file", "name", "join", "$input", "files", "getAllFileInfo", "stripPath", "value", "extension", "getFileExtension", "size", "rawFiles", "getFileInfo", "rawFile", "fileName", "kendo", "htmlEncode", "fileSize", "matches", "match", "rFileExtension", "slashIndex", "lastIndexOf", "substr", "assignGuidToFiles", "unique", "uid", "guid", "validateFiles", "validationInfo", "i", "allowedExtensions", "parseAllowedExtensions", "maxFileSize", "minFileSize", "length", "validateFileExtension", "validateFileSize", "extensions", "ext", "parsedExt", "substring", "toLowerCase", "indexOf", "validationErrors", "inArray", "INVALIDFILEEXTENSION", "push", "INVALIDMINFILESIZE", "INVALIDMAXFILESIZE", "getTotalFilesSizeMessage", "totalSize", "toFixed", "shouldRemoveFileEntry", "upload", "multiple", "wrapper", "removeUploadedFile", "fileEntry", "eventArgs", "shouldSendRemoveRequest", "fileNames", "_supportsRemove", "data", "_removeFileEntry", "_submitRemove", "textStatus", "xhr", "prevented", "trigger", "SUCCESS", "operation", "response", "XMLHttpRequest", "ERROR", "logToConsole", "responseText", "tryParseJSON", "onSuccess", "onError", "success", "json", "parseJSON", "normalizeJSON", "e", "stopEvent", "stopPropagation", "preventDefault", "bindDragEventWrappers", "element", "namespace", "onDragEnter", "onDragLeave", "hideInterval", "lastDrag", "on", "Date", "setInterval", "sinceLastDrag", "clearInterval", "isFileUploadStarted", "is", "getFileEntry", "target", "closest", "iframeUploadModule", "formDataUploadModule", "window", "Widget", "ui", "antiForgeryTokens", "NS", "SELECT", "UPLOAD", "COMPLETE", "CANCEL", "CLEAR", "PAUSE", "RESUME", "PROGRESS", "REMOVE", "VALIDATIONERRORS", "PROGRESSHIDEDELAY", "PROGRESSHIDEDURATION", "headerStatusIcon", "loading", "warning", "Upload", "extend", "init", "options", "activeInput", "ns", "initialFiles", "that", "this", "fn", "call", "directory", "localization", "_wrapInput", "_activeInput", "toggle", "enabled", "_ns", "proxy", "_onParentFormSubmit", "_onParentFormReset", "async", "saveUrl", "_module", "_supportsFormData", "_async", "_renderInitialFiles", "syncUploadModule", "_supportsDrop", "dropZone", "_setupCustomDropZone", "_setupDropZone", "_onFileAction", "_onClearSelected", "_onUploadSelected", "val", "_onInputChange", "events", "showFileList", "template", "autoRetryAfter", "bufferChunkSize", "maxAutoRetries", "removeVerb", "autoUpload", "withCredentials", "accept", "useArrayBuffer", "select", "cancel", "retry", "remove", "pause", "resume", "clearSelectedFiles", "uploadSelectedFiles", "dropFilesHere", "invalidFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusPaused", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "validation", "setOptions", "attr", "_supportsMultiple", "enable", "disable", "toggleClass", "prop", "focus", "destroy", "customDropZone", "document", "add", "off", "onPause", "pauseIcon", "find", "removeClass", "addClass", "parent", "onResume", "playIcon", "onSaveSelected", "getFiles", "filesData", "j", "allFiles", "listItems", "clearAllFiles", "each", "index", "_removeFileByDomElement", "removeAllFiles", "removeFileByUid", "_removeFileByUid", "clearFileByUid", "clearFile", "callback", "_removeFile", "removeFile", "fileData", "hasClass", "onCancel", "onRemove", "_hideHeaderUploadstatus", "_updateHeaderUploadStatus", "_addInput", "sourceInput", "nodeType", "clone", "insertAfter", "prefix", "hide", "removeAttr", "_onInputKeyDown", "firstButton", "keyCode", "keys", "TAB", "shift<PERSON>ey", "_inputFiles", "_isAsyncNonBatch", "onSelect", "_readDirectory", "item", "deferred", "Deferred", "<PERSON><PERSON><PERSON><PERSON>", "createReader", "allFolderFiles", "readEntries", "entries", "concat", "resolve", "reject", "promise", "_readFile", "fullpath", "fullPath", "relativePath", "slice", "droppedFolderFiles", "droppedFolderCounter", "setTimeout", "_proceedDroppedItems", "_traverseFileTree", "<PERSON><PERSON><PERSON><PERSON>", "then", "items", "isFile", "isDirectory", "_onDrop", "entry", "dt", "originalEvent", "dataTransfer", "droppedFiles", "directoryDrop", "webkitGetAsEntry", "splice", "_filesContainValidationErrors", "hasErrors", "batch", "currentFile", "idx", "_enqueueFile", "_fileAction", "_prepareTemplateData", "templateData", "_prepareDefaultSingleFileEntryTemplate", "errors", "_prepareDefaultMultipleFileEntriesTemplate", "filesHaveValidationErrors", "totalFileSize", "sort", "a", "b", "existingFileEntries", "removeEventArgs", "fileUid", "fileList", "appendTo", "angular", "elements", "width", "headers", "allCompletedFiles", "allInvalidFiles", "_hideUploadButton", "fileElement", "action<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON>", "firstActionButton", "classDictionary", "iconsClassDictionary", "hasOwnProperty", "_clearFileAction", "first", "next", "append", "show", "_renderAction", "_fileState", "stateKey", "states", "uploading", "text", "uploaded", "failed", "currentState", "actionClass", "actionText", "iconClass", "empty", "button", "icon", "hasValidationErrors", "_retryClicked", "_checkAllComplete", "finish", "onRetry", "clearEventArgs", "_onFileProgress", "percentComplete", "progressPct", "warningPct", "prepend", "_onUploadSuccess", "_setUploadErrorState", "_hideUploadProgress", "_onUploadError", "module", "chunkSize", "_decreasePosition", "_autoRetryAfter", "retries", "performUpload", "uploadPercentage", "_updateUploadProgress", "fileMetaData", "metaData", "totalChunks", "Math", "round", "chunkIndex", "delay", "fadeOut", "css", "_showUploadButton", "uploadButton", "clearButton", "_showHeaderUploadStatus", "isUploading", "headerUploadStatus", "after", "failedUploads", "headerUploadStatusIcon", "currentlyUploading", "not", "currentlyInvalid", "currentlyFailed", "currentlyPaused", "concurrent", "html", "onAbort", "FormData", "windows", "_userAgent", "support", "browser", "opera", "safari", "userAgent", "isChrome", "test", "<PERSON><PERSON><PERSON><PERSON>", "isWindowsSafari", "navigator", "wrap", "_bindDocumentDragEventWrappers", "removeUrl", "removeField", "params", "j<PERSON><PERSON><PERSON>", "ajax", "type", "dataType", "dataFilter", "url", "traditional", "error", "xhrFields", "prototype", "filesContainErrors", "relatedInput", "iframes", "_frameId", "prepareUpload", "iframe", "form", "saveField", "createFrame", "registerFrame", "createForm", "frame", "key", "dataInput", "cleanupFrame", "unregisterFrame", "body", "prependTo", "one", "onIframeLoad", "submit", "started", "contents", "ex", "processResponse", "fakeXHR", "jsonResult", "statusText", "status", "stopFrameSubmit", "id", "action", "stop", "execCommand", "grep", "position", "cancelled", "paused", "prev", "sourceElement", "fileEntries", "prepareChunk", "enqueueFiles", "filesLength", "webkitRelativePath", "formData", "createFormData", "createXHR", "removeFileEntry", "populateFormData", "FileReader", "postFormData", "reader", "onload", "fileArrayBuffer", "_appendBuffer", "result", "err", "onerror", "readAsA<PERSON>y<PERSON><PERSON>er", "_getCurrentChunk", "buffer1", "buffer2", "tmp", "Uint8Array", "byteLength", "set", "buffer", "filter", "prevEntry", "stopUploadRequest", "_increaseChunkIndex", "addEventListener", "onRequestSuccess", "onRequestError", "onRequestProgress", "open", "setRequestHeader", "send", "chunk", "serializedMetaData", "JSON", "stringify", "raiseError", "parseSuccess", "cleanupFileEntry", "_resetChunkIndex", "uploadComplete", "isUploadButtonVisible", "loaded", "total", "abort", "contentType", "ceil", "uploadUid", "_decreaseChunkIndex", "methodToInvoke", "oldPosition", "_get<PERSON><PERSON><PERSON>", "mozSlice", "webkitSlice", "plugin", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,gBAAiB,cAAeD,IACzC,WAkyDE,MA1xDC,UAAUE,EAAGC,GAylDV,QAASC,GAAYC,GACjB,MAAOH,GAAEI,IAAIC,EAAWF,GAAQ,SAAUG,GACtC,MAAOA,GAAKC,OACbC,KAAK,MAEZ,QAASH,GAAWI,GAChB,GAAIN,GAAQM,EAAO,EACnB,OAAIN,GAAMO,MACCC,EAAeR,EAAMO,SAGpBH,KAAMK,EAAUT,EAAMU,OACtBC,UAAWC,EAAiBZ,EAAMU,OAClCG,KAAM,OAItB,QAASL,GAAeM,GACpB,MAAOjB,GAAEI,IAAIa,EAAU,SAAUX,GAC7B,MAAOY,GAAYZ,KAG3B,QAASY,GAAYC,GACjB,GAAIC,GAAWD,EAAQZ,MAAQY,EAAQC,QACvC,QACIb,KAAMc,EAAMC,WAAWF,GACvBN,UAAWC,EAAiBK,GAC5BJ,KAA6B,gBAAhBG,GAAQH,KAAmBG,EAAQH,KAAOG,EAAQI,SAC/DJ,QAASA,GAGjB,QAASJ,GAAiBK,GACtB,GAAII,GAAUJ,EAASK,MAAMC,EAC7B,OAAOF,GAAUA,EAAQ,GAAK,GAElC,QAASZ,GAAUL,GACf,GAAIoB,GAAapB,EAAKqB,YAAY,KAClC,OAAOD,OAAmBpB,EAAKsB,OAAOF,EAAa,GAAKpB,EAE5D,QAASuB,GAAkBpB,EAAOqB,GAC9B,GAAIC,GAAMX,EAAMY,MAChB,OAAOjC,GAAEI,IAAIM,EAAO,SAAUJ,GAE1B,MADAA,GAAK0B,IAAMD,EAASV,EAAMY,OAASD,EAC5B1B,IAGf,QAAS4B,GAAcxB,EAAOyB,GAA9B,GAIaC,GAHLC,EAAoBC,EAAuBH,EAAeE,mBAC1DE,EAAcJ,EAAeI,YAC7BC,EAAcL,EAAeK,WACjC,KAASJ,EAAI,EAAGA,EAAI1B,EAAM+B,OAAQL,IAC9BM,EAAsBhC,EAAM0B,GAAIC,GAChCM,EAAiBjC,EAAM0B,GAAII,EAAaD,GAGhD,QAASD,GAAuBM,GAC5B,GAAIP,GAAoBrC,EAAEI,IAAIwC,EAAY,SAAUC,GAChD,GAAIC,GAAoC,MAAxBD,EAAIE,UAAU,EAAG,GAAaF,EAAM,IAAMA,CAC1D,OAAOC,GAAUE,eAErB,OAAOX,GAEX,QAASK,GAAsBpC,EAAM+B,GAC7BA,EAAkBI,OAAS,GACvBJ,EAAkBY,QAAQ3C,EAAKQ,UAAUkC,eAAiB,IAC1D1C,EAAK4C,iBAAmB5C,EAAK4C,qBACzBlD,EAAEmD,QAAQC,EAAsB9C,EAAK4C,wBACrC5C,EAAK4C,iBAAiBG,KAAKD,IAK3C,QAAST,GAAiBrC,EAAMkC,EAAaD,GACrB,IAAhBC,GAAqBlC,EAAKU,KAAOwB,IACjClC,EAAK4C,iBAAmB5C,EAAK4C,qBACzBlD,EAAEmD,QAAQG,EAAoBhD,EAAK4C,wBACnC5C,EAAK4C,iBAAiBG,KAAKC,IAGf,IAAhBf,GAAqBjC,EAAKU,KAAOuB,IACjCjC,EAAK4C,iBAAmB5C,EAAK4C,qBACzBlD,EAAEmD,QAAQI,EAAoBjD,EAAK4C,wBACnC5C,EAAK4C,iBAAiBG,KAAKE,IAIvC,QAASC,GAAyB9C,GAAlC,GAGiB0B,GAFTqB,EAAY,CAChB,IAA4B,gBAAjB/C,GAAM,GAAGM,KAOhB,MAAO,EANP,KAASoB,EAAI,EAAGA,EAAI1B,EAAM+B,OAAQL,IAC1B1B,EAAM0B,GAAGpB,OACTyC,GAAa/C,EAAM0B,GAAGpB,KAOlC,OADAyC,IAAa,KACTA,EAAY,KACLA,EAAUC,QAAQ,GAAK,OAEtBD,EAAY,MAAMC,QAAQ,GAAK,MAG/C,QAASC,GAAsBC,GAC3B,OAAQA,EAAOC,UAAY7D,EAAE,UAAW4D,EAAOE,SAASrB,OAAS,EAErE,QAASsB,GAAmBC,EAAWJ,EAAQK,EAAWC,GAA1D,GAOQxD,GACAyD,CAPJ,OAAKP,GAAOQ,mBAMR1D,EAAQsD,EAAUK,KAAK,aACvBF,EAAYnE,EAAEI,IAAIM,EAAO,SAAUJ,GACnC,MAAOA,GAAKC,OAEZ2D,KAA4B,GAC5BN,EAAOU,iBAAiBN,GACxB,IAEJJ,EAAOW,cAAcJ,EAAWF,EAAW,SAAmBI,EAAMG,EAAYC,GAC5E,GAAIC,GAAYd,EAAOe,QAAQC,GAC3BC,UAAW,SACXnE,MAAOA,EACPoE,SAAUT,EACVU,eAAgBN,GAEfC,IACDd,EAAOU,iBAAiBN,IAE7B,SAAiBS,GACZd,EAAsBC,IACtBA,EAAOU,iBAAiBN,GAE5BJ,EAAOe,QAAQK,GACXH,UAAW,SACXnE,MAAOA,EACPqE,eAAgBN,IAEpBQ,EAAa,oBAAsBR,EAAIS,gBAnB3CtB,MAbQD,EAAsBC,IAAYM,GAClCN,EAAOU,iBAAiBN,GAE5B,GAgCR,QAASmB,GAAahF,EAAOiF,EAAWC,GACpC,GAAIC,IAAU,EAAOC,EAAO,EAC5B,KACIA,EAAOvF,EAAEwF,UAAUC,EAActF,IACjCmF,GAAU,EACZ,MAAOI,GACLL,IAEAC,GACAF,EAAUG,GAGlB,QAASE,GAActF,GAInB,MAHqB,KAAVA,GAAmC,KAAVA,IAChCA,EAAQ,MAELA,EAEX,QAASwF,GAAUD,GACfA,EAAEE,kBACFF,EAAEG,iBAEN,QAASC,GAAsBC,EAASC,EAAWC,EAAaC,GAC5D,GAAIC,GAAcC,CAClBL,GAAQM,GAAG,YAAcL,EAAW,SAAUN,GAC1CO,EAAYP,GACZU,EAAW,GAAIE,MACVH,IACDA,EAAeI,YAAY,WACvB,GAAIC,GAAgB,GAAIF,MAASF,CAC7BI,GAAgB,MAChBN,IACAO,cAAcN,GACdA,EAAe,OAEpB,QAERE,GAAG,WAAaL,EAAW,WAC1BI,EAAW,GAAIE,QAGvB,QAASI,GAAoB1C,GACzB,MAAOA,GAAU2C,GAAG,oDAExB,QAASC,GAAalB,GAClB,MAAO1F,GAAE0F,EAAEmB,QAAQC,QAAQ,WAtxDlC,GA2+BOC,GAwMAC,EAlrCA3F,EAAQ4F,OAAO5F,MAAO6F,EAAS7F,EAAM8F,GAAGD,OAAQE,EAAoB/F,EAAM+F,kBAAmBnC,EAAe5D,EAAM4D,aAAcvD,EAAiB,cAAe2F,EAAK,eAAgBC,EAAS,SAAUC,EAAS,SAAU3C,EAAU,UAAWI,EAAQ,QAASwC,EAAW,WAAYC,EAAS,SAAUC,EAAQ,QAASC,EAAQ,QAASC,EAAS,SAAUC,EAAW,WAAYC,EAAS,SAAUC,EAAmB,mBAAoBxE,EAAqB,qBAAsBD,EAAqB,qBAAsBF,EAAuB,uBAAwB4E,EAAoB,IAAMC,EAAuB,IACvmBC,GACAC,QAAS,cACTC,QAAS,cACT9C,QAAS,aAET+C,EAASnB,EAAOoB,QAChBC,KAAM,SAAUxC,EAASyC,GAAnB,GAOEC,GAOAC,EAKIC,EAlBJC,EAAOC,IACX3B,GAAO4B,GAAGP,KAAKQ,KAAKH,EAAM7C,EAASyC,GACnCI,EAAKrI,KAAOwF,EAAQxF,KACpBqI,EAAK/E,SAAW+E,EAAKJ,QAAQ3E,SAC7B+E,EAAKI,UAAYJ,EAAKJ,QAAQQ,UAC9BJ,EAAKK,aAAeL,EAAKJ,QAAQS,aAC7BR,EAAcG,EAAK7C,QACvB6C,EAAK9E,QAAU2E,EAAY3B,QAAQ,aACP,IAAxB8B,EAAK9E,QAAQrB,SACbmG,EAAK9E,QAAU8E,EAAKM,WAAWT,IAEnCG,EAAKO,aAAaV,GAClBG,EAAKQ,OAAOR,EAAKJ,QAAQa,SACrBX,EAAKE,EAAKU,IAAMjC,EAAK,IAAMhG,EAAMY,OACrCwG,EAAY3B,QAAQ,QAAQT,GAAG,SAAWqC,EAAI1I,EAAEuJ,MAAMX,EAAKY,oBAAqBZ,IAAOvC,GAAG,QAAUqC,EAAI1I,EAAEuJ,MAAMX,EAAKa,mBAAoBb,IACrIA,EAAKJ,QAAQkB,MAAMC,SACnBf,EAAKgB,QAAUhB,EAAKiB,oBAAsB,GAAI7C,GAAqB4B,GAAQ,GAAI7B,GAAmB6B,GAClGA,EAAKkB,QAAS,EACVnB,EAAeC,EAAKJ,QAAQ9H,MAC5BiI,EAAalG,OAAS,GACtBmG,EAAKmB,oBAAoBpB,IAG7BC,EAAKgB,QAAU,GAAII,GAAiBpB,GAEpCA,EAAKqB,kBACyB,KAA1BrB,EAAKJ,QAAQ0B,SACbtB,EAAKuB,uBAELvB,EAAKwB,kBAGbxB,EAAK9E,QAAQuC,GAAG,QAAS,mBAAoBrG,EAAEuJ,MAAMX,EAAKyB,cAAezB,IAAOvC,GAAG,QAAS,oBAAqBrG,EAAEuJ,MAAMX,EAAK0B,iBAAkB1B,IAAOvC,GAAG,QAAS,qBAAsBrG,EAAEuJ,MAAMX,EAAK2B,kBAAmB3B,IACrNA,EAAK7C,QAAQyE,OACb5B,EAAK6B,gBAAiB5D,OAAQ+B,EAAK7C,WAG3C2E,QACIpD,EACAC,EACA3C,EACAI,EACAwC,EACAC,EACAC,EACAG,EACAC,EACAH,EACAC,GAEJY,SACIjI,KAAM,SACN8I,SAAS,EACTxF,UAAU,EACVmF,WAAW,EACX2B,cAAc,EACdC,SAAU,GACVlK,SACAgJ,OACImB,eAAgB,EAChBC,gBAAiB,IACjBC,eAAgB,EAChBC,WAAY,OACZC,YAAY,EACZC,iBAAiB,EACjBC,OAAQ,+BACRC,gBAAgB,GAEpBnC,cACIoC,OAAU,kBACVC,OAAU,SACVC,MAAS,QACTC,OAAU,SACVC,MAAS,QACTC,OAAU,SACVC,mBAAsB,QACtBC,oBAAuB,SACvBC,cAAiB,4BACjBC,aAAgB,0DAChBC,gBAAmB,YACnBC,eAAkB,WAClBC,cAAiB,UACjBC,aAAgB,SAChBC,sBAAyB,eACzBC,mBAAsB,SACtBC,qBAAwB,OACxBC,mBAAsB,uBACtBC,mBAAsB,uBACtBC,qBAAwB,0BAE5BC,YACIpK,qBACAE,YAAa,EACbC,YAAa,GAEjB0H,SAAU,IAEdwC,WAAY,SAAUlE,GAClB,GAAII,GAAOC,KAAMJ,EAAcG,EAAK7C,OACpCmB,GAAO4B,GAAG4D,WAAW3D,KAAKH,EAAMJ,GAChCI,EAAK/E,SAAW+E,EAAKJ,QAAQ3E,SAC7B+E,EAAKI,UAAYJ,EAAKJ,QAAQQ,UAC9BP,EAAYkE,KAAK,aAAY/D,EAAKgE,qBAAsBhE,EAAK/E,UACzD+E,EAAKI,YACLP,EAAYkE,KAAK,kBAAmB/D,EAAKI,WACzCP,EAAYkE,KAAK,YAAa/D,EAAKI,YAEvCJ,EAAKQ,OAAOR,EAAKJ,QAAQa,UAE7BwD,OAAQ,SAAUA,GACdA,EAA2B,IAAXA,GAAgCA,EAChDhE,KAAKO,OAAOyD,IAEhBC,QAAS,WACLjE,KAAKO,QAAO,IAEhBA,OAAQ,SAAUyD,GACdA,EAA2B,IAAXA,EAAyBA,GAAUA,EACnDhE,KAAK/E,QAAQiJ,YAAY,mBAAoBF,GAC7ChE,KAAK9C,QAAQiH,KAAK,WAAYH,IAElCI,MAAO,WACHpE,KAAK9C,QAAQkH,SAEjBC,QAAS,WAAA,GACDtE,GAAOC,KACPsE,EAAiBnN,EAAE4I,EAAKJ,QAAQ0B,SACpClK,GAAEoN,UAAUC,IAAIrN,EAAE,cAAe4I,EAAK9E,UAAUuJ,IAAIzE,EAAK9E,QAAQgD,QAAQ,SAASwG,IAAI1E,EAAKU,KACvF6D,EAAe1K,OAAS,GACxB0K,EAAeG,IAAI1E,EAAKU,KAE5BtJ,EAAE4I,EAAK7C,SAASuH,IAAIjG,GACpBH,EAAO4B,GAAGoE,QAAQnE,KAAKH,IAE3B6C,MAAO,SAAUzH,GACb6E,KAAKe,QAAQ2D,SAAU1G,OAAQ7G,EAAEgE,EAAW6E,KAAK/E,UACjD,IAAI0J,GAAYxJ,EAAUyJ,KAAK,gBAC/BD,GAAUE,YAAY,gBAAgBC,SAAS,eAAehB,KAAK,QAAS9D,KAAKI,aAAayC,QAC9F1L,EAAEwN,GAAWI,SAASjB,KAAK,aAAc9D,KAAKI,aAAayC,SAE/DA,OAAQ,SAAU1H,GACd6E,KAAKe,QAAQiE,UAAWhH,OAAQ7G,EAAEgE,EAAW6E,KAAK/E,UAClD,IAAIgK,GAAW9J,EAAUyJ,KAAK,eAC9BK,GAASJ,YAAY,eAAeC,SAAS,gBAAgBhB,KAAK,QAAS9D,KAAKI,aAAawC,OAC7FzL,EAAE8N,GAAUF,SAASjB,KAAK,aAAc9D,KAAKI,aAAawC,QAE9D7H,OAAQ,WACJ,GAAIgF,GAAOC,IACXD,GAAKgB,QAAQmE,kBAEjBC,SAAU,WAAA,GAEFC,GAGK7L,EAGQ8L,EAPbtF,EAAOC,KAEPsF,KACAC,EAAYxF,EAAK9E,QAAQ2J,KAAK,UAClC,KAASrL,EAAI,EAAGA,EAAIgM,EAAU3L,OAAQL,IAElC,GADA6L,EAAYjO,EAAEoO,EAAUhM,IAAIiC,KAAK,aAE7B,IAAS6J,EAAI,EAAGA,EAAID,EAAUxL,OAAQyL,IAClCC,EAAS9K,KAAK4K,EAAUC,GAIpC,OAAOC,IAEXE,cAAe,WAAA,GACPzF,GAAOC,KACPnI,EAAQkI,EAAK9E,QAAQ2J,KAAK,UAC9B/M,GAAM4N,KAAK,SAAUC,EAAOjO,GACxBsI,EAAK4F,wBAAwBlO,GAAM,MAG3CmO,eAAgB,WAAA,GACR7F,GAAOC,KACPnI,EAAQkI,EAAK9E,QAAQ2J,KAAK,UAC9B/M,GAAM4N,KAAK,SAAUC,EAAOjO,GACxBsI,EAAK4F,wBAAwBlO,GAAM,MAG3CoO,gBAAiB,SAAU1M,GACvB6G,KAAK8F,iBAAiB3M,GAAK,IAE/B4M,eAAgB,SAAU5M,GACtB6G,KAAK8F,iBAAiB3M,GAAK,IAE/B2M,iBAAkB,SAAU3M,EAAKkC,GAAf,GAEVF,GADA4E,EAAOC,IAEQ,iBAAR7G,KAGXgC,EAAYhE,EAAE,WAAaqB,EAAMsL,KAAK,OAAS,KAAO3K,EAAM,KAAM4G,EAAK9E,SACnEE,EAAUvB,OAAS,GACnBmG,EAAK4F,wBAAwBxK,EAAWE,KAGhD2K,UAAW,SAAUC,GACjBjG,KAAKkG,YAAYD,GAAU,IAE/BE,WAAY,SAAUF,GAClBjG,KAAKkG,YAAYD,GAAU,IAE/BC,YAAa,SAAUD,EAAU5K,GAApB,GAGL+K,GAFArG,EAAOC,KACPnI,EAAQkI,EAAK9E,QAAQ2J,KAAK,UAEN,mBAAbqB,IACPpO,EAAM4N,KAAK,SAAUC,EAAOjO,GACxB2O,EAAWjP,EAAEM,GAAM+D,KAAK,aACpByK,EAASG,IACTrG,EAAK4F,wBAAwBlO,EAAM4D,MAKnDsK,wBAAyB,SAAUxK,EAAWE,GAArB,GAGjBiK,GAFAvF,EAAOC,KACPoG,GAAapI,OAAQ7G,EAAEgE,EAAW4E,EAAK9E,SAEvC8E,GAAKJ,QAAQkB,MAAMC,SACf3J,EAAEgE,GAAWkL,SAAS,mBACtBtG,EAAKgB,QAAQuF,SAASF,GAEtBrG,EAAKgB,QAAQwF,SAASH,KAAc/K,GAExCiK,EAAWnO,EAAE,UAAW4I,EAAK9E,SACL,IAApBqK,EAAS1L,OACTmG,EAAKyG,0BAELzG,EAAK0G,6BAGT1G,EAAKgB,QAAQwF,SAASH,KAAc/K,IAG5CqL,UAAW,SAAUC,GACjB,GAAKA,EAAY,GAAGC,SAApB,CAGA,GAAI7G,GAAOC,KAAM1I,EAAQqP,EAAYE,QAAQlF,IAAI,GACjDrK,GAAMwP,YAAY/G,EAAK7C,SAAS1B,KAAK,QAAUuE,EAAKJ,QAAQoH,OAAShH,EAAKJ,QAAQjI,KAAMqI,GACxF5I,EAAE4I,EAAK7C,SAAS8J,OAAOlD,KAAK,WAAY,MAAMmD,WAAW,MAAMxC,IAAIjG,GACnEuB,EAAKO,aAAahJ,GAClByI,EAAK7C,QAAQkH,UAEjB9D,aAAc,SAAUhJ,GACpB,GAAIyI,GAAOC,KAAM/E,EAAU8E,EAAK9E,OAChC8E,GAAK7C,QAAU5F,EACXyI,EAAKI,YACL7I,EAAMwM,KAAK,kBAAmB/D,EAAKI,WACnC7I,EAAMwM,KAAK,YAAa/D,EAAKI,YAEjC7I,EAAMwM,KAAK,aAAY/D,EAAKgE,qBAAsBhE,EAAK/E,UAAkB8I,KAAK,eAAgB,OAAOtG,GAAG,QAAUgB,EAAI,SAAU3B,GACxH5B,EAAQoL,SAAS,qBACjBxJ,EAAEG,mBAEPQ,GAAG,QAAUgB,EAAI,WAChBrH,EAAE6I,MAAM+E,SAASD,SAAS,qBAC3BtH,GAAG,OAASgB,EAAI,WACfrH,EAAE6I,MAAM+E,SAASF,YAAY,qBAC9BrH,GAAG,SAAWgB,EAAIrH,EAAEuJ,MAAMX,EAAK6B,eAAgB7B,IAAOvC,GAAG,UAAYgB,EAAIrH,EAAEuJ,MAAMX,EAAKmH,gBAAiBnH,KAE9GmH,gBAAiB,SAAUrK,GAAV,GACTkD,GAAOC,KACPmH,EAAcpH,EAAK9E,QAAQ2J,KAAK,iCAChC/H,GAAEuK,UAAY5O,EAAM6O,KAAKC,KAAOH,EAAYvN,OAAS,IAAMiD,EAAE0K,WAC7D1K,EAAEG,iBACFmK,EAAY/C,UAGpBxC,eAAgB,SAAU/E,GAAV,GAKRhB,GAJAkE,EAAOC,KACP1I,EAAQH,EAAE0F,EAAEmB,QACZnG,EAAQoB,EAAkB8G,EAAKyH,YAAYlQ,GAAQyI,EAAK0H,mBAC5DpO,GAAcxB,EAAOkI,EAAKJ,QAAQiE,YAC9B/H,EAAYkE,EAAKjE,QAAQ2C,GAAU5G,MAAOA,IAC1CgE,GACAkE,EAAK2G,UAAUpP,GACfA,EAAMqL,UAEN5C,EAAKgB,QAAQ2G,UAAW1J,OAAQ1G,GAASO,IAGjD8P,eAAgB,SAAUC,GAAV,GACRC,GAAW,GAAI1Q,GAAE2Q,SACjBC,EAAYH,EAAKI,eACjBC,KACAC,EAAc,WACdH,EAAUG,YAAY,SAAUC,GACvBA,EAAQvO,QAGTqO,EAAiBA,EAAeG,OAAOD,GACvCD,KAHAL,EAASQ,QAAQJ,IAKtBJ,EAASS,QAGhB,OADAJ,KACOL,EAASU,WAEpBC,UAAW,SAAUZ,GAAV,GACH7H,GAAOC,KACPyI,EAAWb,EAAKc,QACpBd,GAAKnQ,KAAK,SAAUA,GAChBA,EAAKkR,aAAeF,EAASG,MAAM,GACnC7I,EAAK8I,mBAAmBrO,KAAK/C,GAC7BsI,EAAK+I,uBAC6B,IAA9B/I,EAAK+I,sBACLC,WAAW,WAC2B,IAA9BhJ,EAAK+I,sBACD/I,EAAK8I,mBAAmBjP,SACxBmG,EAAKiJ,qBAAqBjJ,EAAK8I,oBAC/B9I,EAAK8I,wBAGd,IAER,WACCzM,EAAa,kBAGrB6M,kBAAmB,SAAUrB,EAAMsB,GAC/B,GAAInJ,GAAOC,IACNkJ,IACDnJ,EAAK+I,uBAET9I,KAAK2H,eAAeC,GAAMuB,KAAK,SAAUC,GACrCrJ,EAAK+I,sBAAwBM,EAAMxP,MACnC,KAAK,GAAIL,GAAI,EAAGA,EAAI6P,EAAMxP,OAAQL,IAC1B6P,EAAM7P,GAAG8P,OACTtJ,EAAKyI,UAAUY,EAAM7P,IACd6P,EAAM7P,GAAG+P,aAChBvJ,EAAKkJ,kBAAkBG,EAAM7P,OAK7CgQ,QAAS,SAAU1M,GAAV,GAIDjD,GAMSL,EAEGiQ,EAXZC,EAAK5M,EAAE6M,cAAcC,aACrB5J,EAAOC,KACP4J,EAAeH,EAAG5R,KAGtB,IADAiF,EAAUD,GACNkD,EAAKJ,QAAQkK,eAAiBJ,EAAGL,MAIjC,IAHAxP,EAAS6P,EAAGL,MAAMxP,OAClBmG,EAAK+I,qBAAuB,EAC5B/I,EAAK8I,sBACItP,EAAI,EAAGA,EAAIK,EAAQL,IACpBkQ,EAAGL,MAAM7P,GAAGuQ,kBACRN,EAAQC,EAAGL,MAAM7P,GAAGuQ,mBACpBN,EAAMF,YACNvJ,EAAKkJ,kBAAkBO,GAAO,GACvBA,EAAMH,QACbtJ,EAAK8I,mBAAmBrO,KAAKiP,EAAG5R,MAAM0B,KAG1CwG,EAAKiJ,qBAAqBY,OAIlC7J,GAAKiJ,qBAAqBY,IAGlCZ,qBAAsB,SAAUY,GAAV,GAQV/N,GAPJkE,EAAOC,KACPnI,EAAQoB,EAAkBnB,EAAe8R,GAAe7J,EAAK0H,mBAC7DmC,GAAahQ,OAAS,IAAMmG,EAAK9E,QAAQoL,SAAS,uBAC7CtG,EAAK/E,UAAYnD,EAAM+B,OAAS,GACjC/B,EAAMkS,OAAO,EAAGlS,EAAM+B,OAAS,GAEnCP,EAAcxB,EAAOkI,EAAKJ,QAAQiE,YAC9B/H,EAAYkE,EAAKjE,QAAQ2C,GAAU5G,MAAOA,IACzCgE,GACDkE,EAAKgB,QAAQ2G,UAAW1J,OAAQ7G,EAAE,cAAe4I,EAAK9E,UAAYpD,KAI9EmS,8BAA+B,SAAUnS,GACrC,GAAIoS,IAAY,CAOhB,OANA9S,GAAEU,GAAO4N,KAAK,SAAUC,EAAOjO,GAC3B,GAAIA,EAAKyH,IAAqBzH,EAAKyH,GAAkBtF,OAAS,EAE1D,MADAqQ,IAAY,GACL,IAGRA,GAEXxC,iBAAkB,WACd,MAAOzH,MAAKiB,SAAWjB,KAAKL,QAAQkB,MAAMqJ,QAAS,GAEvDhJ,oBAAqB,SAAUrJ,GAAV,GAKTsS,GACAhP,EALJ4E,EAAOC,KACPoK,EAAM,CAEV,KADAvS,EAAQoB,EAAkBpB,GAAO,GAC5BuS,EAAM,EAAGA,EAAMvS,EAAM+B,OAAQwQ,IAC1BD,EAActS,EAAMuS,GACpBjP,EAAY4E,EAAKsK,aAAaF,EAAYzS,MAAQ4D,WAAY6O,KAClEhP,EAAU2J,SAAS,kBAAkBtJ,KAAK,SAAU3D,EAAMuS,KACtDrK,EAAKxE,mBACLwE,EAAKuK,YAAYnP,EAAW8D,IAIxCsL,qBAAsB,SAAU7S,EAAM8D,GAClC,GAAI4J,GAAY5J,EAAKF,UAAWkP,KAAmB5P,EAAY,EAAGwP,EAAM,CACxE,KAAKA,EAAM,EAAGA,EAAMhF,EAAUxL,OAAQwQ,IAClCxP,GAAawK,EAAUgF,GAAKjS,IAKhC,OAHAqS,GAAa9S,KAAOA,EACpB8S,EAAarS,KAAOyC,EACpB4P,EAAa3S,MAAQ2D,EAAKF,UACnBkP,GAEXC,uCAAwC,SAAUjP,GAAV,GAChCuE,GAAOC,KACPvI,EAAO+D,EAAKF,UAAU,GACtB5C,EAAWiC,EAAyBa,EAAKF,WACzCoP,EAASjT,EAAKyH,GACd6C,EAAW,EAOf,OALIA,IADA2I,GAAUA,EAAO9Q,OAAS,EACd,mSAAkVnC,EAAKC,KAAO,KAAQD,EAAKC,KAAO,kDAA2DqI,EAAKK,aAAasK,EAAO,IAAM,iBAE5c,4HAAgJjT,EAAKQ,UAAUiC,UAAU,GAAK,2HAAmJzC,EAAKC,KAAO,KAAQD,EAAKC,KAAO,oCAA6CgB,EAAW,iBAEzaqJ,GAAY,4CACL5K,EAAE4K,IAEb4I,2CAA4C,SAAUnP,GAAV,GAMpCjC,GAAG4Q,EALHpK,EAAOC,KACPnI,EAAQ2D,EAAKF,UACbsP,EAA4B7K,EAAKiK,8BAA8BnS,GAC/DgT,EAAgBlQ,EAAyB9C,GACzCkK,EAAW,EAkBf,KAfIA,GADA6I,EACY,wKAEA,uGAEhB7I,GAAY,4CACZlK,EAAMiT,KAAK,SAAUC,EAAGC,GACpB,MAAID,GAAE7L,MAGF8L,EAAE9L,GACK,EAEJ,IAEX6C,GAAY,0CACPxI,EAAI,EAAGA,EAAI1B,EAAM+B,OAAQL,IAC1B4Q,EAActS,EAAM0B,GAEhBwI,GADAoI,EAAYjL,IAAqBiL,EAAYjL,GAAkBtF,OAAS,EAC5D,wDAA6DuQ,EAAYzS,KAAO,KAAQyS,EAAYzS,KAAO,UAE3G,oCAAyCyS,EAAYzS,KAAO,KAAQyS,EAAYzS,KAAO,SAS3G,OALIqK,IADA6I,EACY,2CAA+C7K,EAAKK,aAAa6C,aAAe,UAEhF,2CAA+CpL,EAAM+B,OAAS,WAAaiR,EAAgB,UAE3G9I,GAAY,mDACL5K,EAAE4K,IAEbsI,aAAc,SAAU3S,EAAM8D,GAAhB,GAENyP,GACA9P,EAKAqP,EACAU,EARAnL,EAAOC,KAGPmL,EAAU3P,EAAKF,UAAU,GAAGnC,IAC5BiS,EAAWjU,EAAE,kBAAmB4I,EAAK9E,SACrC0E,EAAUI,EAAKJ,QACfoC,EAAWpC,EAAQoC,QA0CvB,OAvCwB,KAApBqJ,EAASxR,SACTwR,EAAWjU,EAAE,4CAA8CkU,SAAStL,EAAK9E,SACpE8E,EAAKJ,QAAQmC,cACdsJ,EAASpE,OAEbjH,EAAK9E,QAAQ4J,YAAY,mBAE7BoG,EAAsB9T,EAAE,UAAWiU,GAC9BrJ,GAODyI,EAAezK,EAAKwK,qBAAqB7S,EAAM8D,GAC/CuG,EAAWvJ,EAAMuJ,SAASA,GAC1B5G,EAAYhE,EAAE,sBAA0B4K,EAASyI,GAAgB,SACjErP,EAAUyJ,KAAK,oBAAoBE,SAAS,YAC5C/E,EAAKuL,QAAQ,UAAW,WACpB,OACIC,SAAUpQ,EACVK,MAAOgP,OAZXrP,EAD0B,IAA1BK,EAAKF,UAAU1B,OACHmG,EAAK0K,uCAAuCjP,GAE5CuE,EAAK4K,2CAA2CnP,GAcpEL,EAAU2I,KAAKtL,EAAMsL,KAAK,OAAQqH,GAASE,SAASD,GAAU5P,KAAKA,GAC9DuE,EAAKkB,QACN9J,EAAE,cAAegE,GAAWqQ,MAAM,SAEjCzL,EAAK/E,UAAYiQ,EAAoBrR,OAAS,IAC/CsR,GACIrT,MAAOoT,EAAoBzP,KAAK,aAChCiQ,YAEC1L,EAAKjE,QAAQmD,EAAQiM,IACtBnL,EAAKgB,QAAQwF,UAAWvI,OAAQ7G,EAAE8T,EAAqBlL,EAAK9E,UAAYiQ,IAGzE/P,GAEXM,iBAAkB,SAAUN,GAAV,GAGVmK,GAAUoG,EAAmBC,EAF7B5L,EAAOC,KACPoL,EAAWjQ,EAAU8C,QAAQ,kBAEjC9C,GAAUwH,SACV2C,EAAWnO,EAAE,UAAWiU,GACxBM,EAAoBvU,EAAE,iCAAkCiU,GACxDO,EAAkBxU,EAAE,kBAAmBiU,GACnCM,EAAkB9R,SAAW0L,EAAS1L,QAAU+R,EAAgB/R,SAAW0L,EAAS1L,QACpFoG,KAAK4L,oBAEe,IAApBtG,EAAS1L,QACTwR,EAASzI,SACT5C,EAAK9E,QAAQ6J,SAAS,kBACtB/E,EAAKyG,2BAELzG,EAAK0G,6BAGb6D,YAAa,SAAUuB,EAAaC,EAAWC,GAAlC,GAaLC,GAZAC,GACAtJ,OAAQ,QACRF,OAAQ,aACRC,MAAO,YACPE,MAAO,gBAEPsJ,GACAvJ,OAAQ,YACRF,OAAQ,YACRC,MAAO,gBACPE,MAAO,eAGNqJ,GAAgBE,eAAeL,KAG/BC,GACD/L,KAAKoM,iBAAiBP,GAErB7L,KAAKL,QAAQoC,UAMdiK,EAAoBH,EAAYjH,KAAK,oBAAoByH,QACpDL,EAAkBpH,KAAK,WAAWhL,OAE5BoS,EAAkBM,KAAK,oBAAoB1S,QAClDoS,EAAkBM,KAAK,oBAAoBxH,SAAS,YAAYyH,OAAO,uBAA0BL,EAAqBJ,GAAa,IAAMG,EAAgBH,GAAa,YAAgB9L,KAAKI,aAAa0L,GAAa,gBAAyB9L,KAAKI,aAAa0L,GAAa,aAAcU,OAF3RR,EAAkBlH,SAAS,YAAYyH,OAAO,uBAA0BL,EAAqBJ,GAAa,IAAMG,EAAgBH,GAAa,YAAgB9L,KAAKI,aAAa0L,GAAa,gBAAyB9L,KAAKI,aAAa0L,GAAa,aAAcU,SAPjQT,GACDF,EAAYjH,KAAK,qCAAqCjC,SAE1DkJ,EAAYjH,KAAK,oBAAoB2H,OAAOvM,KAAKyM,cAAcR,EAAgBH,GAAY9L,KAAKI,aAAa0L,GAAYI,EAAqBJ,QAUtJY,WAAY,SAAUvR,EAAWwR,GAC7B,GAAIvM,GAAeJ,KAAKI,aAAcwM,GAC9BC,WAAaC,KAAM1M,EAAa8C,iBAChC6J,UAAYD,KAAM1M,EAAa+C,gBAC/B6J,QAAUF,KAAM1M,EAAaiD,eAC9B4J,EAAeL,EAAOD,EACzBM,IACA9V,EAAE,oBAAqBgE,GAAW2R,KAAKG,EAAaH,OAG5DL,cAAe,SAAUS,EAAaC,EAAYC,GAC9C,MAAoB,KAAhBF,EACO/V,EAAE,sEAA6EgW,EAAa,yBAAkCC,EAAY,IAAMF,EAAc,YAAgBC,EAAa,sBAA4B3P,GAAG,QAAS,WACtOrG,EAAE6I,MAAM8E,SAAS,qBAClBtH,GAAG,OAAQ,WACVrG,EAAE6I,MAAM6E,YAAY,qBAGjB1N,EAAE,0CAAgDgW,EAAa,cAG9Ef,iBAAkB,SAAUP,GACxB1U,EAAE,mBAAoB0U,GAAawB,QAAQrG,QAE/CxF,cAAe,SAAU3E,GAAV,GAGHyQ,GACAC,EACApS,EACAtD,EACA2V,EACApS,EAPJ2E,EAAOC,IAmCX,OAlCKD,GAAK9E,QAAQoL,SAAS,sBACnBiH,EAASnW,EAAE0F,EAAEmB,QAAQC,QAAQ,oBAC7BsP,EAAOD,EAAO1I,KAAK,WACnBzJ,EAAYmS,EAAOrP,QAAQ,WAC3BpG,EAAQsD,EAAUK,KAAK,aACvBgS,EAAsBzN,EAAKiK,8BAA8BnS,GACzDuD,GACAvD,MAAOA,EACP4T,YAEJ1L,EAAK0N,eAAgB,EACjBF,EAAKlH,SAAS,SACTtG,EAAKjE,QAAQmD,EAAQ7D,IACtB2E,EAAKgB,QAAQwF,UAAWvI,OAAQ7G,EAAEgE,EAAW4E,EAAK9E,UAAYG,GAAYoS,GAEvED,EAAKlH,SAAS,eACrBtG,EAAKjE,QAAQ8C,EAAQxD,GACrB2E,EAAKgB,QAAQuF,UAAWtI,OAAQ7G,EAAEgE,EAAW4E,EAAK9E,WAClD8E,EAAK2N,oBACL3N,EAAK0G,6BACE8G,EAAKlH,SAAS,iBACrBtG,EAAKjE,QAAQgD,EAAO1D,GACpB2E,EAAK6C,MAAMzH,GACX4E,EAAK0G,6BACE8G,EAAKlH,SAAS,gBACrBtG,EAAKjE,QAAQiD,EAAQ3D,GACrB2E,EAAK8C,OAAO1H,IACLoS,EAAKlH,SAAS,eACrBlP,EAAE,eAAgBgE,GAAWwH,SAC7BxL,EAAE,cAAegE,GAAWwS,SAASnB,OACrCzM,EAAKgB,QAAQ6M,SAAU5P,OAAQ7G,EAAEgE,EAAW4E,EAAK9E,WACjD8E,EAAK0N,eAAgB,KAGtB,GAEX/L,kBAAmB,WAAA,GACX3B,GAAOC,KACP/E,EAAU8E,EAAK9E,OAInB,OAHKA,GAAQoL,SAAS,qBAClBrG,KAAKe,QAAQmE,kBAEV,GAEXzD,iBAAkB,WAAA,GACV1B,GAAOC,KACP/E,EAAU8E,EAAK9E,QACf4S,IAIJ,OAHK5S,GAAQoL,SAAS,qBAAwBtG,EAAKjE,QAAQ+C,EAAOgP,IAC9D9N,EAAKyF,iBAEF,GAEXsI,gBAAiB,SAAUjR,EAAGkR,GAAb,GACTC,GACAC,CACAF,GAAkB,MAClBA,EAAkB,KAEjB/N,KAAKL,QAAQoC,SAed5K,EAAE,cAAe0F,EAAEmB,QAAQwN,MAAMuC,EAAkB,MAdnDC,EAAc7W,EAAE,gBAAiB0F,EAAEmB,QACnCiQ,EAAa9W,EAAE,eAAgB0F,EAAEmB,QAC7BiQ,EAAWrU,OACXqU,EAAWpJ,YAAY,eAAeA,YAAY,UAAUC,SAAS,gBACvC,IAAvBkJ,EAAYpU,QACnBzC,EAAE,mBAAoB0F,EAAEmB,QAAQkQ,QAAQ,sCAEpB,MAApBH,EACA5W,EAAE,gBAAiB0F,EAAEmB,QAAQ8O,KAAKiB,EAAkB,KAEpD5W,EAAE,gBAAiB0F,EAAEmB,QAAQ2E,SAEjCxL,EAAE,cAAe0F,EAAEmB,QAAQwN,MAAMuC,EAAkB,MAIvD/N,KAAKlE,QAAQkD,GACTnH,MAAOkG,EAAalB,GAAGrB,KAAK,aAC5BuS,gBAAiBA,KAGzBI,iBAAkB,SAAUtR,EAAGZ,EAAUL,GAAvB,GACVmE,GAAOC,KACP7E,EAAY4C,EAAalB,GACzBhB,EAAYkE,EAAKjE,QAAQC,GACzBlE,MAAOsD,EAAUK,KAAK,aACtBS,SAAUA,EACVD,UAAW,SACXE,eAAgBN,GAEhBC,GACAkE,EAAKqO,qBAAqBjT,IAE1B4E,EAAK2M,WAAWvR,EAAW,YAC3BA,EAAU0J,YAAY,mBAAmBC,SAAS,kBAClD/E,EAAK0G,4BACD1G,EAAKxE,kBACLwE,EAAKuK,YAAYnP,EAAW8D,GAE5Bc,EAAKqM,iBAAiBjR,IAG9B4E,EAAKsO,oBAAoBlT,GACzB4E,EAAK2N,qBAETY,eAAgB,SAAUzR,EAAGjB,GAAb,GACRmE,GAAOC,KACPuO,EAASxO,EAAKgB,QACd5F,EAAY4C,EAAalB,GACzBsO,EAAUhQ,EAAUK,KAAK,MAC7BuE,GAAKqO,qBAAqBjT,GAC1B4E,EAAKjE,QAAQK,GACTH,UAAW,SACXnE,MAAOsD,EAAUK,KAAK,aACtBU,eAAgBN,IAEpBQ,EAAa,oBAAsBR,EAAIS,cAClC0D,EAAKJ,QAAQkB,MAAM2N,UAGhBD,EAAOE,mBACPF,EAAOE,kBAAkBtD,GAH7BpL,EAAKsO,oBAAoBlT,GAM7B4E,EAAK2N,oBACD1N,KAAKL,QAAQkB,MAAMmB,gBACnBhC,KAAK0O,gBAAgBvT,IAG7BuT,gBAAiB,SAAUvT,GAAV,GACT4E,GAAOC,KACP2O,EAAU3O,KAAKe,QAAQ4N,OACtBA,KAGAA,EAAQxT,EAAUK,KAAK,UACxBmT,EAAQxT,EAAUK,KAAK,QAAU,GAEjCmT,EAAQxT,EAAUK,KAAK,SAAWwE,KAAKL,QAAQkB,MAAMqB,iBACrDyM,EAAQxT,EAAUK,KAAK,UACvBuN,WAAW,WACPhJ,EAAKgB,QAAQ6N,cAAczT,IAC5B6E,KAAKL,QAAQkB,MAAMmB,mBAG9BoM,qBAAsB,SAAUjT,GAAV,GAEd0T,GADA9O,EAAOC,IAEXD,GAAK2M,WAAWvR,EAAW,UAC3BA,EAAU0J,YAAY,mBAAmBC,SAAS,gBAClD/E,EAAK+O,sBAAsB3T,GAC3B0T,EAAmB1X,EAAE,gBAAiBgE,GAClC0T,EAAiBjV,OAAS,GACrBiV,EAAiB9J,SAASH,KAAK,gBAAgBhL,QAChDiV,EAAiBhK,YAAY,gBAAgBC,SAAS,sBAE1D+J,EAAiBxB,SAEjBlW,EAAE,mBAAoBgE,GAAW+S,QAAQ,4CAE7ClO,KAAKyG,4BACLzG,KAAKsK,YAAYnP,EAAW,SAC5B6E,KAAKsK,YAAYnP,EAAW8D,GAAQ,GAChCc,EAAK0N,eACLtS,EAAUyJ,KAAK,cAAcG,SAASX,SAG9C0K,sBAAuB,SAAU3T,GAAV,GAKXgQ,GAEI4D,EAEIhB,EARZhO,EAAOC,IACND,GAAKJ,QAAQkB,MAAM2N,WAGhBrD,EAAUhQ,EAAUK,KAAK,OACzBuE,EAAKgB,QAAQiO,WACTD,EAAehP,EAAKgB,QAAQiO,SAAS7D,GACrC4D,IACIhB,EAAkBgB,EAAaE,YAAcC,KAAKC,MAAMJ,EAAaK,WAAaL,EAAaE,YAAc,KAAO,IACxHlP,EAAK+N,iBAAkB9P,OAAQ7G,EAAEgE,EAAW4E,EAAK9E,UAAY8S,MAPrE5W,EAAE,cAAegE,GAAWqQ,MAAM,SAY1C6C,oBAAqB,SAAUlT,GAC3BhE,EAAE,cAAegE,GAAWkU,MAAMlQ,GAAmBmQ,QAAQlQ,EAAsB,WAC/EjI,EAAE6I,MAAMuP,IAAI,QAAS,SAG7BC,kBAAmB,WAAA,GACXzP,GAAOC,KACPyP,EAAetY,EAAE,qBAAsB4I,EAAK9E,SAC5CyU,EAAcvY,EAAE,oBAAqB4I,EAAK9E,QAClB,KAAxBwU,EAAa7V,SACb6V,EAAe1P,EAAK0M,cAAc,GAAIzM,KAAKI,aAAa2C,qBAAqB+B,SAAS,qBACtF4K,EAAc3P,EAAK0M,cAAc,GAAIzM,KAAKI,aAAa0C,oBAAoBgC,SAAS,qBAExF9E,KAAK/E,QAAQsR,OAAOmD,EAAaD,IAErC7D,kBAAmB,WACfzU,EAAE,wCAAyC6I,KAAK/E,SAAS0H,UAE7DgN,wBAAyB,SAAUC,GAAV,GACjB7P,GAAOC,KACPI,EAAeL,EAAKK,aACpBiB,EAAWlK,EAAE,cAAe4I,EAAK9E,SACjC4U,EAAqB1Y,EAAE,yBAA0B4I,EAAK9E,QACxB,KAA9B4U,EAAmBjW,QACnBiW,EAAmBlN,SAEvBkN,EAAqB,8FACjBD,GACAC,EAAqB1Y,EAAE0Y,GAAoBtD,OAAOnM,EAAakD,uBAC/DuM,EAAmBjL,KAAK,WAAWE,SAASzF,EAAiBC,WAE7DuQ,EAAqB1Y,EAAE0Y,GAAoBtD,OAAOnM,EAAaoD,sBAC/DqM,EAAmBjL,KAAK,WAAWE,SAASzF,EAAiBE,UAE7D8B,EAASzH,OAAS,EAClByH,EAASkL,OAAOsD,GAEhB1Y,EAAE,mBAAoB4I,EAAK9E,SAAS6U,MAAMD,IAGlDpJ,0BAA2B,WAAA,GAOnBsJ,GAAeC,EANfjQ,EAAOC,KACP6P,EAAqB1Y,EAAE,yBAA0B6I,KAAK/E,SACtDgV,EAAqB9Y,EAAE,UAAW4I,EAAK9E,SAASiV,IAAI,mDACpDC,EAAmBhZ,EAAE,kBAAmB4I,EAAK9E,SAC7CmV,EAAkBjZ,EAAE,gBAAiB4I,EAAK9E,SAC1CoV,EAAkBlZ,EAAE,UAAW4I,EAAK9E,SAAS2J,KAAK,iBAElDyL,EAAgBzW,QAAWyW,EAAgBzW,SAAWqW,EAAmBrW,QAAWmG,EAAKJ,QAAQkB,MAAMyP,YAGlE,IAA9BL,EAAmBrW,QAAgBuW,EAAiBvW,OAAS,GAAKwW,EAAgBxW,OAAS,KAClGmW,EAAgB5Y,EAAE,+CAAgD4I,EAAK9E,SACvE4U,EAAqB1Y,EAAE,yBAA0B4I,EAAK9E,SACtD+U,EAAyB7Y,EAAE,UAAW0Y,GAAoBhL,cAAcC,SAAS,UAAUA,SAAkC,IAAzBiL,EAAcnW,OAAeyF,EAAiBE,QAAUF,EAAiB5C,SAC7KoT,EAAmBU,KAAKP,GAAwBzD,OAAOxM,EAAKK,aAAaoD,wBANzEwM,EAAyB7Y,EAAE,UAAW0Y,GAAoBhL,cAAcC,SAAS,UAAUA,SAAS,gBACpG+K,EAAmBU,KAAKP,GAAwBzD,OAAOxM,EAAKK,aAAamD,sBAQjFiD,wBAAyB,WACrBrP,EAAE,yBAA0B6I,KAAK/E,SAAS0H,UAE9ChC,oBAAqB,WAAA,GAMTrJ,GALJyD,EAASiF,KAAM9C,EAAUnC,EAAOmC,OACA,KAAzB8C,KAAKe,QAAQyP,SACpBxQ,KAAKe,QAAQyP,UAEZtT,EAAQlF,QACLV,EAAQH,EAAE+F,GACd5F,EAAMwM,KAAK,WAAY,YACvB1F,OAAO2K,WAAW,WACdzR,EAAM2P,WAAW,aAClB,KAGXrG,mBAAoB,WAChBzJ,EAAE,kBAAmB6I,KAAK/E,SAAS0H,UAEvC3B,kBAAmB,WACf,MAA0B,mBAAZyP,WAElB1M,kBAAmB,WACf,GAAI2M,GAAU1Q,KAAK2Q,aAAavW,QAAQ,aACxC,SAAQ5B,EAAMoY,QAAQC,QAAQC,OAAWtY,EAAMoY,QAAQC,QAAQE,QAAUL,IAE7EtP,cAAe,WAAA,GACP4P,GAAYhR,KAAK2Q,aAAaxW,cAC9B8W,EAAW,SAASC,KAAKF,GACzBG,GAAYF,GAAY,SAASC,KAAKF,GACtCI,EAAkBD,GAAY,UAAUD,KAAKF,EACjD,QAAQI,GAAmBpR,KAAKgB,qBAAuBhB,KAAKL,QAAQkB,MAAMC,SAE9E6P,WAAY,WACR,MAAOU,WAAUL,WAErBzP,eAAgB,WAAA,GAGR1B,GACAwB,EAHAtB,EAAOC,IACX7I,GAAE,mBAAoB4I,EAAK9E,SAASqW,KAAK,kCACrCzR,EAAKE,EAAKU,IACVY,EAAWlK,EAAE,cAAe4I,EAAK9E,SAASsR,OAAOpV,EAAE,OAAS4I,EAAKK,aAAa4C,cAAgB,UAAUxF,GAAG,YAAcqC,EAAI/C,GAAWU,GAAG,WAAaqC,EAAI,SAAUhD,GACtKA,EAAEG,mBACHQ,GAAG,OAASqC,EAAI1I,EAAEuJ,MAAMX,EAAKwJ,QAASxJ,IACzC9C,EAAsBoE,EAAUxB,EAAI,WAC3BwB,EAASpD,QAAQ,aAAaoI,SAAS,qBACxChF,EAASyD,SAAS,uBAEvB,WACCzD,EAASwD,YAAY,wBAEzB9E,EAAKwR,+BAA+BlQ,IAExCC,qBAAsB,WAAA,GAIdzB,GAHAE,EAAOC,KACPqB,EAAWlK,EAAE4I,EAAKJ,QAAQ0B,SAC9BlK,GAAE,mBAAoB4I,EAAK9E,SAASqW,KAAK,kCAAoCxB,MAAM3Y,EAAE,OAAS4I,EAAKK,aAAa4C,cAAgB,UAC5HnD,EAAKE,EAAKU,IACdY,EAAS7D,GAAG,YAAcqC,EAAI/C,GAAWU,GAAG,WAAaqC,EAAI,SAAUhD,GACnEA,EAAEG,mBACHQ,GAAG,OAASqC,EAAI1I,EAAEuJ,MAAMX,EAAKwJ,QAASxJ,IACzC9C,EAAsBoE,EAAUxB,EAAI,SAAUhD,GACrCkD,EAAK9E,QAAQoL,SAAS,sBACvBhF,EAASwD,YAAY,sBACrB1N,EAAE0F,EAAEmB,QAAQ8G,SAAS,wBAE1B,WACCzD,EAASwD,YAAY,sBACrBxD,EAASuD,KAAK,uBAAuBC,YAAY,wBAErD9E,EAAKwR,+BAA+BlQ,IAExCkQ,+BAAgC,SAAUlQ,GAAV,GACxBtB,GAAOC,KACPH,EAAKE,EAAKU,GACdxD,GAAsB9F,EAAEoN,UAAW1E,EAAI,WAC9BE,EAAK9E,QAAQoL,SAAS,sBACvBhF,EAASyD,SAAS,qBAClBzD,EAASpD,QAAQ,aAAa4G,YAAY,oBAE/C,WACCxD,EAASwD,YAAY,qBACwC,IAAzD1N,EAAE,YAAakK,EAASpD,QAAQ,cAAcrE,QAC9CyH,EAASpD,QAAQ,aAAa6G,SAAS,qBAInDvJ,gBAAiB,WACb,QAASyE,KAAKL,QAAQkB,MAAM2Q,WAEhC9V,cAAe,SAAUJ,EAAWF,EAAWmB,EAAWC,GACtD,GAAIzB,GAASiF,KAAMyR,EAAc1W,EAAO4E,QAAQkB,MAAM4Q,aAAe,YAAaC,EAASva,EAAEsI,OAAOrE,EAAUI,KAAM+C,IACpHmT,GAAOD,GAAenW,EACtBqW,OAAOC,MACHC,KAAM7R,KAAKL,QAAQkB,MAAMsB,WACzB2P,SAAU,OACVC,WAAYnV,EACZoV,IAAKhS,KAAKL,QAAQkB,MAAM2Q,UACxBS,aAAa,EACbzW,KAAMkW,EACNjG,QAASrQ,EAAUqQ,QACnBhP,QAASF,EACT2V,MAAO1V,EACP2V,WAAa9P,gBAAiBrC,KAAKL,QAAQkB,MAAMwB,oBAGzDhC,WAAY,SAAU/I,GAAV,GACJyI,GAAOC,KACPL,EAAUI,EAAKJ,OAOnB,OANArI,GAAMga,KAAK,6FAAoGtR,KAAKI,aAAaoC,OAAS,kBACrI7C,EAAQkB,MAAMC,SACfxJ,EAAM2G,QAAQ,aAAa6G,SAAS,iBAExCxN,EAAM2G,QAAQ,aAAa6G,SAAS,kBACpCxN,EAAM2G,QAAQ,aAAasO,OAAO,SAAWvM,KAAKI,aAAaoC,OAAS,WACjElL,EAAM2G,QAAQ,cAEzByP,kBAAmB,WAC2C,IAAtDvW,EAAE,0BAA2B6I,KAAK/E,SAASrB,QAC3CoG,KAAKlE,QAAQ6C,IAGrB6I,YAAa,SAAUb,GACnB,MAAOnP,GAAWmP,MAGtBxF,EAAmB,SAAUpG,GAC7BiF,KAAKtI,KAAO,mBACZsI,KAAK9C,QAAUnC,EAAOE,QACtB+E,KAAKjF,OAASA,EACdiF,KAAK9C,QAAQe,QAAQ,QAAQ6F,KAAK,UAAW,uBAAuBA,KAAK,WAAY,uBAEzF3C,GAAiBiR,WACb1K,SAAU,SAAU7K,EAAGhF,GAAb,GAKFuO,GAMA3O,EAVAsD,EAASiF,KAAKjF,OACd4L,EAAcxP,EAAE0F,EAAEmB,QAClBqU,EAAqBtX,EAAOiP,8BAA8BnS,EAC9DkD,GAAO2L,UAAUC,GACbP,GAAa9K,UAAazD,GAC1Bwa,EACA1L,EAAYhE,SAEZyD,EAASkM,aAAe3L,EAExBlP,EAAOsD,EAAOsP,aAAahT,EAAYsP,GAAcP,GACrDiM,GACAtX,EAAOsT,oBAAoB5W,GAE/BsD,EAAOuP,YAAY7S,EAAMwH,IAE7BsH,SAAU,SAAU1J,GAAV,GACF1B,GAAY4C,EAAalB,GACzByV,EAAenX,EAAUK,KAAK,eAC9B8W,IACAA,EAAa3P,SAEjB3C,KAAKjF,OAAOU,iBAAiBN,KAGjC+C,EAAqB,SAAUnD,GAC/BiF,KAAKtI,KAAO,qBACZsI,KAAK9C,QAAUnC,EAAOE,QACtB+E,KAAKjF,OAASA,EACdiF,KAAKuS,YAET/S,EAAOgT,SAAW,EAClBtU,EAAmBkU,WACf1K,SAAU,SAAU7K,EAAGhF,GAAb,GACFkD,GAASiF,KAAKjF,OACd4L,EAAcxP,EAAE0F,EAAEmB,QAClBwP,EAAsBzS,EAAOiP,8BAA8BnS,GAC3DsD,EAAY6E,KAAKyS,cAAc9L,EAAa9O,EAAO2V,EACnDzS,GAAO4E,QAAQkB,MAAMuB,WAChBoL,GAGDzS,EAAOuP,YAAYnP,EAAW8D,GAC9BlE,EAAO4U,yBAAwB,IAH/B3P,KAAK4O,cAAczT,IAMvBJ,EAAOuP,YAAYnP,EAAW8D,GACzBuO,EAGDzS,EAAO0L,4BAFP1L,EAAOyU,qBAKXhC,GACAzS,EAAOsT,oBAAoBlT,IAGnCsX,cAAe,SAAU9L,EAAa9O,EAAO2V,GAA9B,GAIPrS,GAAWiL,EAAUsM,EAAQC,EAH7B5X,EAASiF,KAAKjF,OACd6E,EAAczI,EAAE4D,EAAOmC,SACvBxF,EAAOqD,EAAO4E,QAAQkB,MAAM+R,WAAajM,EAAY7C,KAAK,OAwB9D,OAtBA/I,GAAO2L,UAAUC,GACjBA,EAAY7C,KAAK,OAAQpM,GACpB8V,GAUD7G,EAAYhE,SACZyD,GAAa9K,UAAazD,KAV1B6a,EAAS1S,KAAK6S,YAAY9X,EAAOrD,KAAO,IAAM8H,EAAOgT,YACrDxS,KAAK8S,cAAcJ,GACnBC,EAAO3S,KAAK+S,WAAWhY,EAAO4E,QAAQkB,MAAMC,QAAS4R,EAAO5O,KAAK,SAASyI,OAAO3M,GACjFwG,GACI4M,MAASN,EACTJ,aAAgB1S,EAChBtE,UAAazD,IAMrBsD,EAAYJ,EAAOsP,aAAahT,EAAYsP,GAAcP,GACtDsM,GACAA,EAAOlX,MACHmX,KAAQA,EACRlb,KAAQ0D,IAGTA,GAEXyT,cAAe,SAAUzT,GAAV,GAQHwX,GAEKM,EACDC,EAVRrW,GAAMhF,MAAOsD,EAAUK,KAAK,cAC5BkX,EAASvX,EAAUK,KAAK,SACxBT,EAASiF,KAAKjF,MAClB,IAAKA,EAAOe,QAAQ4C,EAAQ7B,GAsBxB9B,EAAOU,iBAAiBiX,EAAOlX,KAAK,SACpCwE,KAAKmT,aAAaT,GAClB1S,KAAKoT,gBAAgBV,OAxBO,CAC5B3X,EAAO6Q,oBACP7Q,EAAO4U,yBAAwB,GAC/B+C,EAAOrH,SAAS9G,SAAS8O,MACrBV,EAAOD,EAAOlX,KAAK,QAAQsI,KAAK,SAAU/I,EAAO4E,QAAQkB,MAAMC,SAASuK,SAAS9G,SAAS8O,MAC9FxW,EAAErB,KAAOrE,EAAEsI,UAAW5C,EAAErB,KAAM+C,IAC9B,KAAS0U,IAAOpW,GAAErB,KACV0X,EAAYP,EAAK/N,KAAK,eAAkBqO,EAAM,MACzB,IAArBC,EAAUtZ,SACVsZ,EAAY/b,EAAE,WACV0a,KAAM,SACNna,KAAMub,IACPK,UAAUX,IAEjBO,EAAUvR,IAAI9E,EAAErB,KAAKyX,GAEzBlY,GAAOuP,YAAYnP,EAAWyD,GAC9B7D,EAAO2R,WAAWvR,EAAW,aAC7BhE,EAAEgE,GAAW0J,YAAY,gBAAgBC,SAAS,mBAClD4N,EAAOa,IAAI,OAAQpc,EAAEuJ,MAAMV,KAAKwT,aAAcxT,OAC9C2S,EAAK,GAAGc,WAOhBvO,eAAgB,WAAA,GACRqJ,GAASvO,KACTjF,EAASwT,EAAOxT,MACpB5D,GAAE,UAAW6I,KAAK9C,SAASuI,KAAK,WAAA,GACxBtK,GAAYhE,EAAE6I,MACd0T,EAAU7V,EAAoB1C,GAC9BqS,EAAsBzS,EAAOiP,8BAA8B7O,EAAUK,KAAK,aACzEkY,IAAYlG,GACbe,EAAOK,cAAczT,MAIjCqY,aAAc,SAAU3W,GACpB,GAA0BR,GAAtBqW,EAASvb,EAAE0F,EAAEmB,OACjB,KACI3B,EAAeqW,EAAOiB,WAAW7G,OACnC,MAAO8G,GACLvX,EAAe,wCAA0CuX,EAE7D5T,KAAK6T,gBAAgBnB,EAAQrW,IAEjCwX,gBAAiB,SAAUnB,EAAQrW,GAC/B,GAAIlB,GAAYuX,EAAOlX,KAAK,QAAS+S,EAASvO,KAAM8T,GAAYzX,aAAcA,EAC9EC,GAAaD,EAAc,SAAU0X,GACjC5c,EAAEsI,OAAOqU,GACLE,WAAY,KACZC,OAAQ,QAEZ1F,EAAOxT,OAAO+S,iBAAkB9P,OAAQ7G,EAAEgE,EAAWoT,EAAOxT,OAAOE,UAAY,KAC/EsT,EAAOxT,OAAOoT,kBAAmBnQ,OAAQ7G,EAAEgE,EAAWoT,EAAOxT,OAAOE,UAAY8Y,EAAYD,GAC5FvF,EAAO4E,aAAaT,GACpBnE,EAAO6E,gBAAgBV,IACxB,WACCvb,EAAEsI,OAAOqU,GACLE,WAAY,QACZC,OAAQ,QAEZ1F,EAAOxT,OAAOuT,gBAAiBtQ,OAAQ7G,EAAEgE,EAAWoT,EAAOxT,OAAOE,UAAY6Y,MAGtFxN,SAAU,SAAUzJ,GAChB,GAAI6V,GAASvb,EAAE0F,EAAEmB,QAAQxC,KAAK,QAC9BwE,MAAKkU,gBAAgBxB,GACrB1S,KAAKmT,aAAaT,GAClB1S,KAAKoT,gBAAgBV,GACrB1S,KAAKjF,OAAOU,iBAAiBiX,EAAOlX,KAAK,UAE7CoS,QAAS,SAAU/Q,GACf,GAAI1B,GAAY4C,EAAalB,EAC7BmD,MAAK4O,cAAczT,IAEvBoL,SAAU,SAAU1J,EAAGzB,EAAWC,GAAxB,GACFkT,GAASvO,KACTjF,EAASwT,EAAOxT,OAChBI,EAAY4C,EAAalB,GACzB6V,EAASvX,EAAUK,KAAK,QACxBkX,IACAnE,EAAO6E,gBAAgBV,GACvB3X,EAAOU,iBAAiBN,GACxBoT,EAAO4E,aAAaT,IAEhBvX,EAAUkL,SAAS,kBACnBnL,EAAmBC,EAAWJ,EAAQK,EAAWC,GAEjDN,EAAOU,iBAAiBN,IAIpCqV,QAAS,WACL,GAAItT,GAAU8C,KAAK9C,QAASqR,EAASvO,IACrC7I,GAAEsO,KAAKzF,KAAKuS,QAAS,WACjBpb,EAAE,QAAS6I,KAAKxE,KAAK,SAAS6P,SAASnO,GACvCqR,EAAO2F,gBAAgBlU,KAAK,IAC5BA,KAAKxE,KAAK,QAAQmH,SAClB3C,KAAK2C,WAET3C,KAAKuS,YAETM,YAAa,SAAUsB,GACnB,MAAOhd,GAAE,iBAAyBgd,EAAK,SAAkBA,EAAK,+BAElEpB,WAAY,SAAUqB,EAAQpW,GAC1B,MAAO7G,GAAE,6DAAyEid,EAAS,aAAsBpW,EAAS,QAE9HkW,gBAAiB,SAAUlB,GACE,IAAdA,EAAMqB,KACbrB,EAAMqB,OACCrB,EAAMzO,UACbyO,EAAMzO,SAAS+P,YAAY,SAGnCxB,cAAe,SAAUE,GACrBhT,KAAKuS,QAAQ/X,KAAKwY,IAEtBI,gBAAiB,SAAUJ,GACvBhT,KAAKuS,QAAUpb,EAAEod,KAAKvU,KAAKuS,QAAS,SAAUva,GAC1C,MAAOA,GAAM8L,KAAK,SAAWkP,EAAMlP,KAAK,WAGhDqP,aAAc,SAAUH,GACpB,GAAIL,GAAOK,EAAMxX,KAAK,OACtBwX,GAAMxX,KAAK,QAAQA,KAAK,QAAS,MACjCuN,WAAW,WACP4J,EAAKhQ,SACLqQ,EAAMrQ,UACP,KAGPxE,EAAuB,SAAUpD,GACjCiF,KAAKtI,KAAO,uBACZsI,KAAK9C,QAAUnC,EAAOE,QACtB+E,KAAKjF,OAASA,EACdiF,KAAKwU,YACLxU,KAAKgP,YACLhP,KAAKyU,aACLzU,KAAK6C,UACL7C,KAAK0U,UACL1U,KAAK2O,YAETxQ,EAAqBiU,WACjB1K,SAAU,SAAU7K,EAAGhF,GAAb,GAKF2V,GACAmH,EALA5Z,EAASiF,KAAKjF,OACdwT,EAASvO,KACT4U,EAAgBzd,EAAE0F,EAAEmB,QACpB6W,EAAc7U,KAAKyS,cAAcmC,EAAe/c,EAGpDV,GAAEsO,KAAKoP,EAAa,SAAUnP,GAC1B8H,EAAsBzS,EAAOiP,8BAA8B7S,EAAE6I,KAAKxE,KAAK,eACnET,EAAO4E,QAAQkB,MAAMuB,WAChBoL,GAWDzS,EAAOuP,YAAYtK,KAAMf,GACzBlE,EAAO4U,yBAAwB,IAX3B5U,EAAO4E,QAAQkB,MAAM2N,WACrBD,EAAOuG,aAAa9U,MACpB2U,EAAO3U,KAAK2U,QACR5Z,EAAO4E,QAAQkB,MAAMyP,YAAwB,IAAV5K,IAAgBiP,EAAK/a,QAAoB,IAAV8L,GAAeiP,EAAKtO,SAAS,oBAC/FkI,EAAOK,cAAc5O,OAGzBuO,EAAOK,cAAc5O,OAO7BjF,EAAOuP,YAAYtK,KAAMf,GACpBuO,EAIDzS,EAAO0L,6BAHP1L,EAAOyU,oBACPxP,KAAK8E,SAAS,gBAKlB0I,GACAzS,EAAOsT,oBAAoBrO,SAIvCyS,cAAe,SAAUmC,EAAe/c,GACpC,GAAIgd,GAAc7U,KAAK+U,aAAald,EAQpC,OAPI+c,GAAc9W,GAAG,WACjB3G,EAAEsO,KAAKoP,EAAa,WAChB1d,EAAE6I,MAAMxE,KAAK,eAAgBoZ,KAEjCA,EAAcpZ,KAAK,qBAAsBqZ,GACzC7U,KAAKjF,OAAO2L,UAAUkO,IAEnBC,GAEXE,aAAc,SAAUld,GAAV,GAENH,GACA6B,EAEA4Q,EACAhP,EALAJ,EAASiF,KAAKjF,OAGdia,EAAcnd,EAAM+B,OAGpBib,IACJ,IAAI9Z,EAAO4E,QAAQkB,MAAMqJ,SAAU,EAC/BxS,EAAOP,EAAEI,IAAIM,EAAO,SAAUJ,GAC1B,MAAOA,GAAKC,OACbC,KAAK,OACJoD,EAAOoF,WAAapF,EAAO4E,QAAQkK,gBACnC1S,EAAEU,GAAO4N,KAAK,YACNzF,KAAK1H,QAAQ2c,oBAAsBjV,KAAK1H,QAAQqQ,gBAChD3I,KAAKtI,KAAOsI,KAAK1H,QAAQ2c,oBAAsBjV,KAAK1H,QAAQqQ,gBAIxExN,EAAYJ,EAAOsP,aAAa3S,GAAQ4D,UAAWzD,IACnDsD,EAAUK,KAAK,QAAS3D,GACxBgd,EAAYra,KAAKW,OAEjB,KAAK5B,EAAI,EAAGA,EAAIyb,EAAazb,IACzB4Q,EAActS,EAAM0B,GACpB7B,EAAOyS,EAAYzS,MACfqD,EAAOoF,WAAapF,EAAO4E,QAAQkK,iBAC/BM,EAAY7R,QAAQ2c,oBAAsB9K,EAAY7R,QAAQqQ,gBAC9DwB,EAAYzS,KAAOyS,EAAY7R,QAAQ2c,oBAAsB9K,EAAY7R,QAAQqQ,cAGzFxN,EAAYJ,EAAOsP,aAAa3S,GAAQ4D,WAAY6O,KACpDhP,EAAUK,KAAK,SAAU2O,IACzB0K,EAAYra,KAAKW,EAGzB,OAAO0Z,IAEXjG,cAAe,SAAUzT,GAAV,GAIJtD,GAgBUob,EAnBblY,EAASiF,KAAKjF,OAAQma,EAAWlV,KAAKmV,iBAAkBvZ,EAAMoE,KAAKoV,YAAavY,GAC5EhF,MAAOsD,EAAUK,KAAK,aACtBU,eAAgBN,EAExB,IAAKb,EAAOe,QAAQ4C,EAAQ7B,GA+BxBmD,KAAKqV,gBAAgBla,OA/BO,CAW5B,GAV6C,IAAzCA,EAAUyJ,KAAK,eAAehL,SAC1BmB,EAAO4E,QAAQkB,MAAM2N,WACrBzT,EAAOuP,YAAYnP,EAAW2D,GAElC/D,EAAOuP,YAAYnP,EAAWyD,EAAQ7D,EAAO4E,QAAQkB,MAAM2N,YAE1DzT,EAAOE,QAAQ2J,KAAK,eAAehL,QACpCmB,EAAO6Q,oBAEX7Q,EAAO4U,yBAAwB,GAC3B9S,EAAEqY,SACFA,EAAWrY,EAAEqY,aACV,CACHrY,EAAErB,KAAOrE,EAAEsI,UAAW5C,EAAErB,KAAM+C,IAC9B,KAAS0U,IAAOpW,GAAErB,KACd0Z,EAAS3I,OAAO0G,EAAKpW,EAAErB,KAAKyX,GAEhCpb,GAAQsD,EAAUK,KAAK,SACnB3D,GACAmI,KAAKsV,iBAAiBJ,EAAUrd,GAGxCkD,EAAO2R,WAAWvR,EAAW,aAC7BhE,EAAEgE,GAAW0J,YAAY,gBAAgBC,SAAS,mBAC9C/J,EAAO4E,QAAQkB,MAAM0B,gBAAkBnE,OAAOmX,WAC9CvV,KAAKwI,UAAUzN,EAAO4E,QAAQkB,MAAMC,QAASoU,EAAU/Z,EAAWS,GAElEoE,KAAKwV,aAAaza,EAAO4E,QAAQkB,MAAMC,QAASoU,EAAU/Z,EAAWS,KAMjF4M,UAAW,SAAU1H,EAASoU,EAAU/Z,EAAWS,GAAxC,GACHmE,GAAOC,KACPjF,EAASgF,EAAKhF,OACdtD,EAAO0D,EAAUK,KAAK,SAAS,GAC/Bia,EAAS,GAAIF,WACjBE,GAAOC,OAAS,SAAU7Y,GACtB,IAIQkD,EAAK4V,gBAHJ5V,EAAK4V,gBAGiB5V,EAAK6V,cAAc7V,EAAK4V,gBAAiB9Y,EAAEmB,OAAO6X,QAFlDhZ,EAAEmB,OAAO6X,OAItC,MAAOC,GAEL,MADA/a,GAAOuT,gBAAiBtQ,OAAQ7G,EAAEgE,EAAWJ,EAAOE,UAAYW,GAChE,EAEAmE,EAAKyU,SAAS/c,EAAK0B,KAAO1B,EAAKU,MAC/B4H,EAAKyV,aAAaza,EAAO4E,QAAQkB,MAAMC,QAASf,EAAK4V,gBAAiBxa,EAAWS,GACjFmE,EAAK4V,gBAAkB,MAEvB5V,EAAKyI,UAAU1H,EAASoU,EAAU/Z,EAAWS,IAGrD6Z,EAAOM,QAAU,WACbhb,EAAOuT,gBAAiBtQ,OAAQ7G,EAAEgE,EAAWJ,EAAOE,UAAYW,IAEpE6Z,EAAOO,kBAAkBjW,EAAKkW,iBAAiBxe,EAAKa,QAASb,EAAK0B,OAEtEyc,cAAe,SAAUM,EAASC,GAC9B,GAAIC,GAAM,GAAIC,YAAWH,EAAQI,WAAaH,EAAQG,WAGtD,OAFAF,GAAIG,IAAI,GAAIF,YAAWH,GAAU,GACjCE,EAAIG,IAAI,GAAIF,YAAWF,GAAUD,EAAQI,YAClCF,EAAII,QAEftR,eAAgB,WAAA,GACRqJ,GAASvO,KACTjF,EAASwT,EAAOxT,MACpB5D,GAAE,cAAe6I,KAAK9C,SAASuZ,OAAO,WAAA,GAC9Btb,GAAYhE,EAAE6I,MACd0T,EAAU7V,EAAoB1C,GAC9BqS,EAAsBzS,EAAOiP,8BAA8B7O,EAAUK,KAAK,aAC9E,QAAQkY,IAAYlG,IACrB/H,KAAK,SAAUC,GAAV,GACAvK,GAAYhE,EAAE6I,MACd0W,EAAYvb,EAAUwZ,MAC1BxZ,GAAU0J,YAAY,cAClB9J,EAAO4E,QAAQkB,MAAM2N,WACrBD,EAAOuG,aAAa3Z,IAChBJ,EAAO4E,QAAQkB,MAAMyP,YAAwB,IAAV5K,IAAgBgR,EAAU9c,QAAqB,IAAV8L,GAAegR,EAAUrQ,SAAS,mBAAqBqQ,EAAUrQ,SAAS,kBAClJkI,EAAOK,cAAczT,IAGzBoT,EAAOK,cAAczT,MAIjCmL,SAAU,SAAUzJ,GAChB,GAAI1B,GAAY4C,EAAalB,EACzBmD,MAAKjF,OAAO4E,QAAQkB,MAAM2N,YAC1BxO,KAAKyU,UAAUtZ,EAAUK,KAAK,SAAU,GAE5CwE,KAAK2W,kBAAkBxb,GACvB6E,KAAKqV,gBAAgBla,IAEzBuJ,QAAS,SAAU7H,GAAV,GACD1B,GAAY4C,EAAalB,GACzBsO,EAAUhQ,EAAUK,KAAK,OACzBqF,EAAQb,KAAKjF,OAAO4E,QAAQkB,KAC5BA,GAAM2N,YACNxO,KAAK2O,QAAQxD,GAAWtK,EAAMqB,eAAiB,EAC/ClC,KAAK0U,OAAOvJ,IAAW,EACvBnL,KAAK6C,OAAOsI,IAAW,IAG/BnG,SAAU,SAAUnI,GAAV,GACF1B,GAAY4C,EAAalB,GACzBsO,EAAUhQ,EAAUK,KAAK,MACzBwE,MAAKjF,OAAO4E,QAAQkB,MAAM2N,kBACnBxO,MAAK0U,OAAOvJ,GACnBnL,KAAK6C,OAAOsI,IAAW,EACvBnL,KAAK2O,QAAQxT,EAAUK,KAAK,QAAU,EACtCwE,KAAK4W,oBAAoBzL,GACzBnL,KAAK4O,cAAczT;EAG3ByS,QAAS,SAAU/Q,GAAV,GACD1B,GAAY4C,EAAalB,GACzBgE,EAAQb,KAAKjF,OAAO4E,QAAQkB,KAC5BA,GAAM2N,YACNxO,KAAK2O,QAAQxT,EAAUK,KAAK,QAAUqF,EAAMqB,eAAiB,QACtDlC,MAAK0U,OAAOvZ,EAAUK,KAAK,SAEtCwE,KAAK4O,cAAczT,IAEvBoL,SAAU,SAAU1J,EAAGzB,EAAWC,GAAxB,GACFkT,GAASvO,KACTjF,EAASwT,EAAOxT,OAChBI,EAAY4C,EAAalB,GACzBgE,EAAQb,KAAKjF,OAAO4E,QAAQkB,KAC5BA,GAAM2N,YACNxO,KAAK2O,QAAQxT,EAAUK,KAAK,QAAUqF,EAAMqB,eAAiB,GAE7D/G,EAAUkL,SAAS,kBACnBnL,EAAmBC,EAAWJ,EAAQK,EAAWC,GAEjDkT,EAAO8G,gBAAgBla,IAG/Bia,UAAW,WACP,MAAO,IAAIlZ,iBAEfsZ,aAAc,SAAUxD,EAAKxW,EAAML,EAAWS,GAAhC,GAcN0G,GAbAiM,EAASvO,IACb7E,GAAUK,KAAK,UAAWI,GAC1BA,EAAIib,iBAAiB,OAAQ,SAAUha,GACnC0R,EAAOuI,iBAAiB5W,KAAKqO,EAAQ1R,EAAG1B,KACzC,GACHS,EAAIib,iBAAiB1a,EAAO,SAAUU,GAClC0R,EAAOwI,eAAe7W,KAAKqO,EAAQ1R,EAAG1B,KACvC,GACHS,EAAIb,OAAO8b,iBAAiB,WAAY,SAAUha,GAC9C0R,EAAOyI,kBAAkB9W,KAAKqO,EAAQ1R,EAAG1B,KAC1C,GACHS,EAAIqb,KAAK,OAAQjF,GAAK,GACtBpW,EAAIyG,gBAAkBrC,KAAKjF,OAAO4E,QAAQkB,MAAMwB,gBAC5CC,EAAStC,KAAKjF,OAAO4E,QAAQkB,MAAMyB,OACnCA,GACA1G,EAAIsb,iBAAiB,SAAU5U,GAEnC1G,EAAIub,KAAK3b,IAEb2Z,eAAgB,WACZ,MAAO,IAAI1E,WAEf6E,iBAAkB,SAAU9Z,EAAM3D,GAAhB,GACVuf,GACA7d,EAEAJ,EAMIke,EAPJzd,EAAS/B,EAAM+B,OAEfmB,EAASiF,KAAKjF,MAClB,IAAIA,EAAO4E,QAAQkB,MAAM2N,UACrBrV,EAAMtB,EAAM,GAAGsB,IACfie,EAAQpX,KAAKiW,iBAAiBpe,EAAM,GAAGS,QAASa,GAChDqC,EAAK+Q,OAAOxR,EAAO4E,QAAQkB,MAAM+R,WAAa7X,EAAOrD,KAAM0f,GACvDC,EAAqBC,KAAKC,UAAUvX,KAAKgP,SAAS7V,IACtDqC,EAAK+Q,OAAO,WAAY8K,OAExB,KAAK9d,EAAI,EAAGA,EAAIK,EAAQL,IACpBiC,EAAK+Q,OAAOxR,EAAO4E,QAAQkB,MAAM+R,WAAa7X,EAAOrD,KAAMG,EAAM0B,GAAGjB,QAG5E,OAAOkD,IAEXsb,iBAAkB,SAAUja,EAAG1B,GAE3B,QAASqc,KACLjJ,EAAOxT,OAAOuT,gBAAiBtQ,OAAQ7G,EAAEgE,EAAWoT,EAAOxT,OAAOE,UAAYW,GAElF,QAAS6b,GAAa1D,GAAtB,GACQ7J,GAAQqE,EAAOxT,OAAO4E,QAAQkB,MAAMqJ,MACpCsE,EAAYD,EAAOxT,OAAO4E,QAAQkB,MAAM2N,UACxC8B,EAAa/B,EAAOxT,OAAO4E,QAAQkB,MAAMyP,WACzCnF,EAAU4I,EAAW5I,OACrBoD,GAAOmG,OAAOvJ,IAAYoD,EAAOkG,UAAUtJ,WAGxCoD,GAAOI,QAAQxD,IAClBqD,GAActE,GAAU6J,EAAWhH,UAG5ByB,GAActE,GAAUoG,IAAcnV,EAAUmR,OAAO1S,QAAWuB,EAAUmR,OAAOjG,SAAS,eAMnGkI,EAAOxT,OAAO+S,iBAAkB9P,OAAQ7G,EAAEgE,EAAWoT,EAAOxT,OAAOE,UAAY,KAC/EsT,EAAOxT,OAAOoT,kBAAmBnQ,OAAQ7G,EAAEgE,EAAWoT,EAAOxT,OAAOE,UAAY8Y,EAAYnY,GAC5F2S,EAAOmJ,iBAAiBvc,KAPxBoT,EAAOxT,OAAO+S,iBAAkB9P,OAAQ7G,EAAEgE,EAAWoT,EAAOxT,OAAOE,UAAY,KAC/EsT,EAAOoJ,iBAAiBxM,GACxBoD,EAAOxT,OAAOoT,kBAAmBnQ,OAAQ7G,EAAEgE,EAAWoT,EAAOxT,OAAOE,UAAY8Y,EAAYnY,GAC5F2S,EAAOK,cAAczT,EAAUmR,UAN/BiC,EAAOqI,oBAAoBzL,GAC3BoD,EAAOK,cAAczT,KAf7B,GAAIS,GAAMiB,EAAEmB,OAAQuQ,EAASvO,IA2BzBpE,GAAIqY,QAAU,KAAOrY,EAAIqY,QAAU,IACnC3X,EAAaV,EAAIS,aAAcob,EAAcD,GAE7CA,KAGRT,eAAgB,SAAUla,EAAG1B,GACzB,GAAIS,GAAMiB,EAAEmB,MACZgC,MAAKjF,OAAOuT,gBAAiBtQ,OAAQ7G,EAAEgE,EAAW6E,KAAKjF,OAAOE,UAAYW,IAE9E8b,iBAAkB,SAAUvc,GACxB,GAAImX,GAAenX,EAAUK,KAAK,gBAAiBoc,GAAiB,CAChEtF,KACAnb,EAAEsO,KAAK6M,EAAa9W,KAAK,0BAA6B,WAC9CwE,KAAK+E,SAASnL,OAAS,GAAKoG,KAAK,IAAM7E,EAAU,KACjDyc,EAAiBA,GAAkB5X,KAAKqG,SAAS,qBAGrDuR,GACAtF,EAAa3P,WAIzB0S,gBAAiB,SAAUla,GAAV,GACTqT,GAAYxO,KAAKjF,OAAO4E,QAAQkB,MAAM2N,UACtC8B,EAAatQ,KAAKjF,OAAO4E,QAAQkB,MAAMyP,WACvCuH,EAAwB7X,KAAKjF,OAAOE,QAAQ2J,KAAK,sBAAsBhL,OAAS,CACpFoG,MAAK0X,iBAAiBvc,IAClBqT,GAAc8B,GAAeuH,GACzB1c,EAAUmR,OAAO1S,QACjBoG,KAAK4O,cAAczT,EAAUmR,QAGrCtM,KAAKjF,OAAOU,iBAAiBN,IAEjC6b,kBAAmB,SAAUna,EAAG1B,GAAb,GAGX4T,GAFAhB,EAAkBmB,KAAKC,MAAiB,IAAXtS,EAAEib,OAAejb,EAAEkb,OAChD5M,EAAUhQ,EAAUK,KAAK,MAEzBwE,MAAKjF,OAAO4E,QAAQkB,MAAM2N,YAC1BO,EAAe/O,KAAKgP,SAAS7D,GAC7B4C,EAAkBgB,GAAgBA,EAAaE,YAAcC,KAAKC,MAAMJ,EAAaK,WAAaL,EAAaE,YAAc,KAAO,KAExIjP,KAAKjF,OAAO+S,iBAAkB9P,OAAQ7G,EAAEgE,EAAW6E,KAAKjF,OAAOE,UAAY8S,IAE/E4I,kBAAmB,SAAUxb,GACzBA,EAAUK,KAAK,WAAWwc,SAE9BlD,aAAc,SAAU3Z,GAAV,GACN1D,GAAO0D,EAAUK,KAAK,SAAS,GAC/BlD,EAAUb,EAAKa,QACfa,EAAM1B,EAAK0B,IACXqV,EAAYxO,KAAKjF,OAAO4E,QAAQkB,MAAM2N,SAC1CxO,MAAKwU,SAASrb,GAAO,EACrB6G,KAAKgP,SAAS7V,IACViW,WAAY,EACZ6I,YAAa3f,EAAQuZ,KACrBtZ,SAAUD,EAAQZ,KAClBiR,aAAclR,EAAKC,KACnBmT,cAAevS,EAAQH,KACvB8W,YAAaC,KAAKgJ,KAAK5f,EAAQH,KAAOqW,GACtC2J,UAAWhf,IAGnBif,oBAAqB,SAAUjf,GAC3B6G,KAAKgP,SAAS7V,GAAKiW,cAEvBwH,oBAAqB,SAAUzd,GAC3B6G,KAAKgP,SAAS7V,GAAKiW,cAEvBuI,iBAAkB,SAAUxe,GACxB6G,KAAKgP,SAAS7V,GAAKiW,WAAa,GAEpCX,kBAAmB,SAAUtV,GACzB6G,KAAKwU,SAASrb,IAAQ6G,KAAKjF,OAAO4E,QAAQkB,MAAM2N,WAEpDyH,iBAAkB,SAAUxe,EAAM0B,GAAhB,GAEVkf,GADAC,EAActY,KAAKwU,SAASrb,GAE5B0H,EAAQb,KAAKjF,OAAO4E,QAAQkB,MAC5B2N,EAAY3N,EAAM2N,WAAa3N,EAAMoB,eAKzC,OAJKjC,MAAKwU,SAASrb,KACf6G,KAAKwU,SAASrb,GAAO,GAEzB6G,KAAKwU,SAASrb,IAAQqV,GACf6J,EAAiBrY,KAAKuY,YAAY9gB,IAC9BA,EAAK4gB,GAAgBC,EAAatY,KAAKwU,SAASrb,IAEhD1B,GAGf8gB,YAAa,SAAU9gB,GACnB,MAAIA,GAAKmR,MACE,QACAnR,EAAK+gB,SACL,WACA/gB,EAAKghB,YACL,cAEA,OAmMnBjgB,EAAM8F,GAAGoa,OAAOlZ,IAClBpB,OAAO5F,MAAMmZ,QACRvT,OAAO5F,OACE,kBAAVtB,SAAwBA,OAAOyhB,IAAMzhB,OAAS,SAAU0hB,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.upload.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.upload', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'upload',\n        name: 'Upload',\n        category: 'web',\n        description: 'The Upload widget uses progressive enhancement to deliver the best possible uploading experience to users.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, antiForgeryTokens = kendo.antiForgeryTokens, logToConsole = kendo.logToConsole, rFileExtension = /\\.([^\\.]+)$/, NS = '.kendoUpload', SELECT = 'select', UPLOAD = 'upload', SUCCESS = 'success', ERROR = 'error', COMPLETE = 'complete', CANCEL = 'cancel', CLEAR = 'clear', PAUSE = 'pause', RESUME = 'resume', PROGRESS = 'progress', REMOVE = 'remove', VALIDATIONERRORS = 'validationErrors', INVALIDMAXFILESIZE = 'invalidMaxFileSize', INVALIDMINFILESIZE = 'invalidMinFileSize', INVALIDFILEEXTENSION = 'invalidFileExtension', PROGRESSHIDEDELAY = 1000, PROGRESSHIDEDURATION = 2000;\n        var headerStatusIcon = {\n            loading: 'k-i-loading',\n            warning: 'k-i-warning',\n            success: 'k-i-check'\n        };\n        var Upload = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that.name = element.name;\n                that.multiple = that.options.multiple;\n                that.directory = that.options.directory;\n                that.localization = that.options.localization;\n                var activeInput = that.element;\n                that.wrapper = activeInput.closest('.k-upload');\n                if (that.wrapper.length === 0) {\n                    that.wrapper = that._wrapInput(activeInput);\n                }\n                that._activeInput(activeInput);\n                that.toggle(that.options.enabled);\n                var ns = that._ns = NS + '-' + kendo.guid();\n                activeInput.closest('form').on('submit' + ns, $.proxy(that._onParentFormSubmit, that)).on('reset' + ns, $.proxy(that._onParentFormReset, that));\n                if (that.options.async.saveUrl) {\n                    that._module = that._supportsFormData() ? new formDataUploadModule(that) : new iframeUploadModule(that);\n                    that._async = true;\n                    var initialFiles = that.options.files;\n                    if (initialFiles.length > 0) {\n                        that._renderInitialFiles(initialFiles);\n                    }\n                } else {\n                    that._module = new syncUploadModule(that);\n                }\n                if (that._supportsDrop()) {\n                    if (that.options.dropZone !== '') {\n                        that._setupCustomDropZone();\n                    } else {\n                        that._setupDropZone();\n                    }\n                }\n                that.wrapper.on('click', '.k-upload-action', $.proxy(that._onFileAction, that)).on('click', '.k-clear-selected', $.proxy(that._onClearSelected, that)).on('click', '.k-upload-selected', $.proxy(that._onUploadSelected, that));\n                if (that.element.val()) {\n                    that._onInputChange({ target: that.element });\n                }\n            },\n            events: [\n                SELECT,\n                UPLOAD,\n                SUCCESS,\n                ERROR,\n                COMPLETE,\n                CANCEL,\n                CLEAR,\n                PROGRESS,\n                REMOVE,\n                PAUSE,\n                RESUME\n            ],\n            options: {\n                name: 'Upload',\n                enabled: true,\n                multiple: true,\n                directory: false,\n                showFileList: true,\n                template: '',\n                files: [],\n                async: {\n                    autoRetryAfter: 0,\n                    bufferChunkSize: 10000000,\n                    maxAutoRetries: 1,\n                    removeVerb: 'POST',\n                    autoUpload: true,\n                    withCredentials: true,\n                    accept: '*/*; q=0.5, application/json',\n                    useArrayBuffer: false\n                },\n                localization: {\n                    'select': 'Select files...',\n                    'cancel': 'Cancel',\n                    'retry': 'Retry',\n                    'remove': 'Remove',\n                    'pause': 'Pause',\n                    'resume': 'Resume',\n                    'clearSelectedFiles': 'Clear',\n                    'uploadSelectedFiles': 'Upload',\n                    'dropFilesHere': 'Drop files here to upload',\n                    'invalidFiles': 'Invalid file(s). Please check file upload requirements.',\n                    'statusUploading': 'uploading',\n                    'statusUploaded': 'uploaded',\n                    'statusWarning': 'warning',\n                    'statusFailed': 'failed',\n                    'headerStatusUploading': 'Uploading...',\n                    'headerStatusPaused': 'Paused',\n                    'headerStatusUploaded': 'Done',\n                    'invalidMaxFileSize': 'File size too large.',\n                    'invalidMinFileSize': 'File size too small.',\n                    'invalidFileExtension': 'File type not allowed.'\n                },\n                validation: {\n                    allowedExtensions: [],\n                    maxFileSize: 0,\n                    minFileSize: 0\n                },\n                dropZone: ''\n            },\n            setOptions: function (options) {\n                var that = this, activeInput = that.element;\n                Widget.fn.setOptions.call(that, options);\n                that.multiple = that.options.multiple;\n                that.directory = that.options.directory;\n                activeInput.attr('multiple', that._supportsMultiple() ? that.multiple : false);\n                if (that.directory) {\n                    activeInput.attr('webkitdirectory', that.directory);\n                    activeInput.attr('directory', that.directory);\n                }\n                that.toggle(that.options.enabled);\n            },\n            enable: function (enable) {\n                enable = typeof enable === 'undefined' ? true : enable;\n                this.toggle(enable);\n            },\n            disable: function () {\n                this.toggle(false);\n            },\n            toggle: function (enable) {\n                enable = typeof enable === 'undefined' ? enable : !enable;\n                this.wrapper.toggleClass('k-state-disabled', enable);\n                this.element.prop('disabled', enable);\n            },\n            focus: function () {\n                this.element.focus();\n            },\n            destroy: function () {\n                var that = this;\n                var customDropZone = $(that.options.dropZone);\n                $(document).add($('.k-dropzone', that.wrapper)).add(that.wrapper.closest('form')).off(that._ns);\n                if (customDropZone.length > 0) {\n                    customDropZone.off(that._ns);\n                }\n                $(that.element).off(NS);\n                Widget.fn.destroy.call(that);\n            },\n            pause: function (fileEntry) {\n                this._module.onPause({ target: $(fileEntry, this.wrapper) });\n                var pauseIcon = fileEntry.find('.k-i-pause-sm');\n                pauseIcon.removeClass('k-i-pause-sm').addClass('k-i-play-sm').attr('title', this.localization.resume);\n                $(pauseIcon).parent().attr('aria-label', this.localization.resume);\n            },\n            resume: function (fileEntry) {\n                this._module.onResume({ target: $(fileEntry, this.wrapper) });\n                var playIcon = fileEntry.find('.k-i-play-sm');\n                playIcon.removeClass('k-i-play-sm').addClass('k-i-pause-sm').attr('title', this.localization.pause);\n                $(playIcon).parent().attr('aria-label', this.localization.pause);\n            },\n            upload: function () {\n                var that = this;\n                that._module.onSaveSelected();\n            },\n            getFiles: function () {\n                var that = this;\n                var filesData;\n                var allFiles = [];\n                var listItems = that.wrapper.find('.k-file');\n                for (var i = 0; i < listItems.length; i++) {\n                    filesData = $(listItems[i]).data('fileNames');\n                    if (filesData) {\n                        for (var j = 0; j < filesData.length; j++) {\n                            allFiles.push(filesData[j]);\n                        }\n                    }\n                }\n                return allFiles;\n            },\n            clearAllFiles: function () {\n                var that = this;\n                var files = that.wrapper.find('.k-file');\n                files.each(function (index, file) {\n                    that._removeFileByDomElement(file, false);\n                });\n            },\n            removeAllFiles: function () {\n                var that = this;\n                var files = that.wrapper.find('.k-file');\n                files.each(function (index, file) {\n                    that._removeFileByDomElement(file, true);\n                });\n            },\n            removeFileByUid: function (uid) {\n                this._removeFileByUid(uid, true);\n            },\n            clearFileByUid: function (uid) {\n                this._removeFileByUid(uid, false);\n            },\n            _removeFileByUid: function (uid, shouldSendRemoveRequest) {\n                var that = this;\n                var fileEntry;\n                if (typeof uid !== 'string') {\n                    return;\n                }\n                fileEntry = $('.k-file[' + kendo.attr('uid') + '=\"' + uid + '\"]', that.wrapper);\n                if (fileEntry.length > 0) {\n                    that._removeFileByDomElement(fileEntry, shouldSendRemoveRequest);\n                }\n            },\n            clearFile: function (callback) {\n                this._removeFile(callback, false);\n            },\n            removeFile: function (callback) {\n                this._removeFile(callback, true);\n            },\n            _removeFile: function (callback, shouldSendRemoveRequest) {\n                var that = this;\n                var files = that.wrapper.find('.k-file');\n                var fileData;\n                if (typeof callback === 'function') {\n                    files.each(function (index, file) {\n                        fileData = $(file).data('fileNames');\n                        if (callback(fileData)) {\n                            that._removeFileByDomElement(file, shouldSendRemoveRequest);\n                        }\n                    });\n                }\n            },\n            _removeFileByDomElement: function (fileEntry, shouldSendRemoveRequest) {\n                var that = this;\n                var fileData = { target: $(fileEntry, that.wrapper) };\n                var allFiles;\n                if (that.options.async.saveUrl) {\n                    if ($(fileEntry).hasClass('k-file-progress')) {\n                        that._module.onCancel(fileData);\n                    } else {\n                        that._module.onRemove(fileData, {}, shouldSendRemoveRequest);\n                    }\n                    allFiles = $('.k-file', that.wrapper);\n                    if (allFiles.length === 0) {\n                        that._hideHeaderUploadstatus();\n                    } else {\n                        that._updateHeaderUploadStatus();\n                    }\n                } else {\n                    that._module.onRemove(fileData, {}, shouldSendRemoveRequest);\n                }\n            },\n            _addInput: function (sourceInput) {\n                if (!sourceInput[0].nodeType) {\n                    return;\n                }\n                var that = this, input = sourceInput.clone().val('');\n                input.insertAfter(that.element).data('kendo' + that.options.prefix + that.options.name, that);\n                $(that.element).hide().attr('tabindex', '-1').removeAttr('id').off(NS);\n                that._activeInput(input);\n                that.element.focus();\n            },\n            _activeInput: function (input) {\n                var that = this, wrapper = that.wrapper;\n                that.element = input;\n                if (that.directory) {\n                    input.attr('webkitdirectory', that.directory);\n                    input.attr('directory', that.directory);\n                }\n                input.attr('multiple', that._supportsMultiple() ? that.multiple : false).attr('autocomplete', 'off').on('click' + NS, function (e) {\n                    if (wrapper.hasClass('k-state-disabled')) {\n                        e.preventDefault();\n                    }\n                }).on('focus' + NS, function () {\n                    $(this).parent().addClass('k-state-focused');\n                }).on('blur' + NS, function () {\n                    $(this).parent().removeClass('k-state-focused');\n                }).on('change' + NS, $.proxy(that._onInputChange, that)).on('keydown' + NS, $.proxy(that._onInputKeyDown, that));\n            },\n            _onInputKeyDown: function (e) {\n                var that = this;\n                var firstButton = that.wrapper.find('.k-upload-action:visible:first');\n                if (e.keyCode === kendo.keys.TAB && firstButton.length > 0 && !e.shiftKey) {\n                    e.preventDefault();\n                    firstButton.focus();\n                }\n            },\n            _onInputChange: function (e) {\n                var that = this;\n                var input = $(e.target);\n                var files = assignGuidToFiles(that._inputFiles(input), that._isAsyncNonBatch());\n                validateFiles(files, that.options.validation);\n                var prevented = that.trigger(SELECT, { files: files });\n                if (prevented) {\n                    that._addInput(input);\n                    input.remove();\n                } else {\n                    that._module.onSelect({ target: input }, files);\n                }\n            },\n            _readDirectory: function (item) {\n                var deferred = new $.Deferred();\n                var dirReader = item.createReader();\n                var allFolderFiles = [];\n                var readEntries = function () {\n                    dirReader.readEntries(function (entries) {\n                        if (!entries.length) {\n                            deferred.resolve(allFolderFiles);\n                        } else {\n                            allFolderFiles = allFolderFiles.concat(entries);\n                            readEntries();\n                        }\n                    }, deferred.reject);\n                };\n                readEntries();\n                return deferred.promise();\n            },\n            _readFile: function (item) {\n                var that = this;\n                var fullpath = item.fullPath;\n                item.file(function (file) {\n                    file.relativePath = fullpath.slice(1);\n                    that.droppedFolderFiles.push(file);\n                    that.droppedFolderCounter--;\n                    if (that.droppedFolderCounter === 0) {\n                        setTimeout(function () {\n                            if (that.droppedFolderCounter === 0) {\n                                if (that.droppedFolderFiles.length) {\n                                    that._proceedDroppedItems(that.droppedFolderFiles);\n                                    that.droppedFolderFiles = [];\n                                }\n                            }\n                        }, 0);\n                    }\n                }, function () {\n                    logToConsole('File error.');\n                });\n            },\n            _traverseFileTree: function (item, skipCounter) {\n                var that = this;\n                if (!skipCounter) {\n                    that.droppedFolderCounter--;\n                }\n                this._readDirectory(item).then(function (items) {\n                    that.droppedFolderCounter += items.length;\n                    for (var i = 0; i < items.length; i++) {\n                        if (items[i].isFile) {\n                            that._readFile(items[i]);\n                        } else if (items[i].isDirectory) {\n                            that._traverseFileTree(items[i]);\n                        }\n                    }\n                });\n            },\n            _onDrop: function (e) {\n                var dt = e.originalEvent.dataTransfer;\n                var that = this;\n                var droppedFiles = dt.files;\n                var length;\n                stopEvent(e);\n                if (that.options.directoryDrop && dt.items) {\n                    length = dt.items.length;\n                    that.droppedFolderCounter = 0;\n                    that.droppedFolderFiles = [];\n                    for (var i = 0; i < length; i++) {\n                        if (dt.items[i].webkitGetAsEntry) {\n                            var entry = dt.items[i].webkitGetAsEntry();\n                            if (entry.isDirectory) {\n                                that._traverseFileTree(entry, true);\n                            } else if (entry.isFile) {\n                                that.droppedFolderFiles.push(dt.files[i]);\n                            }\n                        } else {\n                            that._proceedDroppedItems(droppedFiles);\n                        }\n                    }\n                } else {\n                    that._proceedDroppedItems(droppedFiles);\n                }\n            },\n            _proceedDroppedItems: function (droppedFiles) {\n                var that = this;\n                var files = assignGuidToFiles(getAllFileInfo(droppedFiles), that._isAsyncNonBatch());\n                if (droppedFiles.length > 0 && !that.wrapper.hasClass('k-state-disabled')) {\n                    if (!that.multiple && files.length > 1) {\n                        files.splice(1, files.length - 1);\n                    }\n                    validateFiles(files, that.options.validation);\n                    var prevented = that.trigger(SELECT, { files: files });\n                    if (!prevented) {\n                        that._module.onSelect({ target: $('.k-dropzone', that.wrapper) }, files);\n                    }\n                }\n            },\n            _filesContainValidationErrors: function (files) {\n                var hasErrors = false;\n                $(files).each(function (index, file) {\n                    if (file[VALIDATIONERRORS] && file[VALIDATIONERRORS].length > 0) {\n                        hasErrors = true;\n                        return false;\n                    }\n                });\n                return hasErrors;\n            },\n            _isAsyncNonBatch: function () {\n                return this._async && !this.options.async.batch || false;\n            },\n            _renderInitialFiles: function (files) {\n                var that = this;\n                var idx = 0;\n                files = assignGuidToFiles(files, true);\n                for (idx = 0; idx < files.length; idx++) {\n                    var currentFile = files[idx];\n                    var fileEntry = that._enqueueFile(currentFile.name, { fileNames: [currentFile] });\n                    fileEntry.addClass('k-file-success').data('files', [files[idx]]);\n                    if (that._supportsRemove()) {\n                        that._fileAction(fileEntry, REMOVE);\n                    }\n                }\n            },\n            _prepareTemplateData: function (name, data) {\n                var filesData = data.fileNames, templateData = {}, totalSize = 0, idx = 0;\n                for (idx = 0; idx < filesData.length; idx++) {\n                    totalSize += filesData[idx].size;\n                }\n                templateData.name = name;\n                templateData.size = totalSize;\n                templateData.files = data.fileNames;\n                return templateData;\n            },\n            _prepareDefaultSingleFileEntryTemplate: function (data) {\n                var that = this;\n                var file = data.fileNames[0];\n                var fileSize = getTotalFilesSizeMessage(data.fileNames);\n                var errors = file[VALIDATIONERRORS];\n                var template = '';\n                if (errors && errors.length > 0) {\n                    template += '<li class=\\'k-file k-file-invalid\\'><span class=\\'k-progress\\'></span>' + '<span class=\\'k-file-invalid-extension-wrapper\\'>' + '<span class=\\'k-file-invalid-icon\\'>!</span>' + '<span class=\\'k-file-state\\'></span>' + '</span>' + '<span class=\\'k-file-name-size-wrapper\\'>' + '<span class=\\'k-file-name k-file-name-invalid\\' title=\\'' + file.name + '\\'>' + file.name + '</span>' + '<span class=\\'k-file-validation-message\\'>' + that.localization[errors[0]] + '</span>' + '</span>';\n                } else {\n                    template += '<li class=\\'k-file\\'><span class=\\'k-progress\\'></span>' + '<span class=\\'k-file-extension-wrapper\\'>' + '<span class=\\'k-file-extension\\'>' + file.extension.substring(1) + '</span>' + '<span class=\\'k-file-state\\'></span>' + '</span>' + '<span class=\\'k-file-name-size-wrapper\\'><span class=\\'k-file-name\\' title=\\'' + file.name + '\\'>' + file.name + '</span>' + '<span class=\\'k-file-size\\'>' + fileSize + '</span></span>';\n                }\n                template += '<strong class=\\'k-upload-status\\'></strong>';\n                return $(template);\n            },\n            _prepareDefaultMultipleFileEntriesTemplate: function (data) {\n                var that = this;\n                var files = data.fileNames;\n                var filesHaveValidationErrors = that._filesContainValidationErrors(files);\n                var totalFileSize = getTotalFilesSizeMessage(files);\n                var template = '';\n                var i, currentFile;\n                if (filesHaveValidationErrors) {\n                    template += '<li class=\\'k-file k-file-invalid\\'><span class=\\'k-progress\\'></span>' + '<span class=\\'k-multiple-files-invalid-extension-wrapper\\'>' + '<span class=\\'k-file-invalid-icon\\'>!</span>';\n                } else {\n                    template += '<li class=\\'k-file\\'><span class=\\'k-progress\\'></span>' + '<span class=\\'k-multiple-files-extension-wrapper\\'>';\n                }\n                template += '<span class=\\'k-file-state\\'></span></span>';\n                files.sort(function (a, b) {\n                    if (a[VALIDATIONERRORS]) {\n                        return -1;\n                    }\n                    if (b[VALIDATIONERRORS]) {\n                        return 1;\n                    }\n                    return 0;\n                });\n                template += '<span class=\\'k-file-name-size-wrapper\\'>';\n                for (i = 0; i < files.length; i++) {\n                    currentFile = files[i];\n                    if (currentFile[VALIDATIONERRORS] && currentFile[VALIDATIONERRORS].length > 0) {\n                        template += '<span class=\\'k-file-name k-file-name-invalid\\' title=\\'' + currentFile.name + '\\'>' + currentFile.name + '</span>';\n                    } else {\n                        template += '<span class=\\'k-file-name\\' title=\\'' + currentFile.name + '\\'>' + currentFile.name + '</span>';\n                    }\n                }\n                if (filesHaveValidationErrors) {\n                    template += '<span class=\\'k-file-validation-message\\'>' + that.localization.invalidFiles + '</span>';\n                } else {\n                    template += '<span class=\\'k-file-information\\'>Total: ' + files.length + ' files, ' + totalFileSize + '</span>';\n                }\n                template += '</span><strong class=\\'k-upload-status\\'></strong>';\n                return $(template);\n            },\n            _enqueueFile: function (name, data) {\n                var that = this;\n                var existingFileEntries;\n                var fileEntry;\n                var fileUid = data.fileNames[0].uid;\n                var fileList = $('.k-upload-files', that.wrapper);\n                var options = that.options;\n                var template = options.template;\n                var templateData;\n                var removeEventArgs;\n                if (fileList.length === 0) {\n                    fileList = $('<ul class=\\'k-upload-files k-reset\\'></ul>').appendTo(that.wrapper);\n                    if (!that.options.showFileList) {\n                        fileList.hide();\n                    }\n                    that.wrapper.removeClass('k-upload-empty');\n                }\n                existingFileEntries = $('.k-file', fileList);\n                if (!template) {\n                    if (data.fileNames.length === 1) {\n                        fileEntry = that._prepareDefaultSingleFileEntryTemplate(data);\n                    } else {\n                        fileEntry = that._prepareDefaultMultipleFileEntriesTemplate(data);\n                    }\n                } else {\n                    templateData = that._prepareTemplateData(name, data);\n                    template = kendo.template(template);\n                    fileEntry = $('<li class=\\'k-file\\'>' + template(templateData) + '</li>');\n                    fileEntry.find('.k-upload-action').addClass('k-button');\n                    that.angular('compile', function () {\n                        return {\n                            elements: fileEntry,\n                            data: [templateData]\n                        };\n                    });\n                }\n                fileEntry.attr(kendo.attr('uid'), fileUid).appendTo(fileList).data(data);\n                if (!that._async) {\n                    $('.k-progress', fileEntry).width('100%');\n                }\n                if (!that.multiple && existingFileEntries.length > 0) {\n                    removeEventArgs = {\n                        files: existingFileEntries.data('fileNames'),\n                        headers: {}\n                    };\n                    if (!that.trigger(REMOVE, removeEventArgs)) {\n                        that._module.onRemove({ target: $(existingFileEntries, that.wrapper) }, removeEventArgs);\n                    }\n                }\n                return fileEntry;\n            },\n            _removeFileEntry: function (fileEntry) {\n                var that = this;\n                var fileList = fileEntry.closest('.k-upload-files');\n                var allFiles, allCompletedFiles, allInvalidFiles;\n                fileEntry.remove();\n                allFiles = $('.k-file', fileList);\n                allCompletedFiles = $('.k-file-success, .k-file-error', fileList);\n                allInvalidFiles = $('.k-file-invalid', fileList);\n                if (allCompletedFiles.length === allFiles.length || allInvalidFiles.length === allFiles.length) {\n                    this._hideUploadButton();\n                }\n                if (allFiles.length === 0) {\n                    fileList.remove();\n                    that.wrapper.addClass('k-upload-empty');\n                    that._hideHeaderUploadstatus();\n                } else {\n                    that._updateHeaderUploadStatus();\n                }\n            },\n            _fileAction: function (fileElement, actionKey, skipClear) {\n                var classDictionary = {\n                    remove: 'k-i-x',\n                    cancel: 'k-i-cancel',\n                    retry: 'k-i-retry',\n                    pause: 'k-i-pause-sm'\n                };\n                var iconsClassDictionary = {\n                    remove: 'k-i-close',\n                    cancel: 'k-i-close',\n                    retry: 'k-i-reload-sm',\n                    pause: 'k-i-pause-sm'\n                };\n                var firstActionButton;\n                if (!classDictionary.hasOwnProperty(actionKey)) {\n                    return;\n                }\n                if (!skipClear) {\n                    this._clearFileAction(fileElement);\n                }\n                if (!this.options.template) {\n                    if (!skipClear) {\n                        fileElement.find('.k-upload-status .k-upload-action').remove();\n                    }\n                    fileElement.find('.k-upload-status').append(this._renderAction(classDictionary[actionKey], this.localization[actionKey], iconsClassDictionary[actionKey]));\n                } else {\n                    firstActionButton = fileElement.find('.k-upload-action').first();\n                    if (!firstActionButton.find('.k-icon').length) {\n                        firstActionButton.addClass('k-button').append('<span class=\\'k-icon ' + iconsClassDictionary[actionKey] + ' ' + classDictionary[actionKey] + '\\' title=\\'' + this.localization[actionKey] + '\\'' + 'aria-label=\\'' + this.localization[actionKey] + '\\'></span>').show();\n                    } else if (firstActionButton.next('.k-upload-action').length) {\n                        firstActionButton.next('.k-upload-action').addClass('k-button').append('<span class=\\'k-icon ' + iconsClassDictionary[actionKey] + ' ' + classDictionary[actionKey] + '\\' title=\\'' + this.localization[actionKey] + '\\'' + 'aria-label=\\'' + this.localization[actionKey] + '\\'></span>').show();\n                    }\n                }\n            },\n            _fileState: function (fileEntry, stateKey) {\n                var localization = this.localization, states = {\n                        uploading: { text: localization.statusUploading },\n                        uploaded: { text: localization.statusUploaded },\n                        failed: { text: localization.statusFailed }\n                    }, currentState = states[stateKey];\n                if (currentState) {\n                    $('span.k-file-state', fileEntry).text(currentState.text);\n                }\n            },\n            _renderAction: function (actionClass, actionText, iconClass) {\n                if (actionClass !== '') {\n                    return $('<button type=\\'button\\' class=\\'k-button k-upload-action\\' aria-label=\\'' + actionText + '\\'>' + '<span class=\\'k-icon ' + iconClass + ' ' + actionClass + '\\' title=\\'' + actionText + '\\'></span>' + '</button>').on('focus', function () {\n                        $(this).addClass('k-state-focused');\n                    }).on('blur', function () {\n                        $(this).removeClass('k-state-focused');\n                    });\n                } else {\n                    return $('<button type=\\'button\\' class=\\'k-button\\'>' + actionText + '</button>');\n                }\n            },\n            _clearFileAction: function (fileElement) {\n                $('.k-upload-action', fileElement).empty().hide();\n            },\n            _onFileAction: function (e) {\n                var that = this;\n                if (!that.wrapper.hasClass('k-state-disabled')) {\n                    var button = $(e.target).closest('.k-upload-action');\n                    var icon = button.find('.k-icon');\n                    var fileEntry = button.closest('.k-file');\n                    var files = fileEntry.data('fileNames');\n                    var hasValidationErrors = that._filesContainValidationErrors(files);\n                    var eventArgs = {\n                        files: files,\n                        headers: {}\n                    };\n                    that._retryClicked = false;\n                    if (icon.hasClass('k-i-x')) {\n                        if (!that.trigger(REMOVE, eventArgs)) {\n                            that._module.onRemove({ target: $(fileEntry, that.wrapper) }, eventArgs, !hasValidationErrors);\n                        }\n                    } else if (icon.hasClass('k-i-cancel')) {\n                        that.trigger(CANCEL, eventArgs);\n                        that._module.onCancel({ target: $(fileEntry, that.wrapper) });\n                        that._checkAllComplete();\n                        that._updateHeaderUploadStatus();\n                    } else if (icon.hasClass('k-i-pause-sm')) {\n                        that.trigger(PAUSE, eventArgs);\n                        that.pause(fileEntry);\n                        that._updateHeaderUploadStatus();\n                    } else if (icon.hasClass('k-i-play-sm')) {\n                        that.trigger(RESUME, eventArgs);\n                        that.resume(fileEntry);\n                    } else if (icon.hasClass('k-i-retry')) {\n                        $('.k-i-warning', fileEntry).remove();\n                        $('.k-progress', fileEntry).finish().show();\n                        that._module.onRetry({ target: $(fileEntry, that.wrapper) });\n                        that._retryClicked = true;\n                    }\n                }\n                return false;\n            },\n            _onUploadSelected: function () {\n                var that = this;\n                var wrapper = that.wrapper;\n                if (!wrapper.hasClass('k-state-disabled')) {\n                    this._module.onSaveSelected();\n                }\n                return false;\n            },\n            _onClearSelected: function () {\n                var that = this;\n                var wrapper = that.wrapper;\n                var clearEventArgs = {};\n                if (!wrapper.hasClass('k-state-disabled') && !that.trigger(CLEAR, clearEventArgs)) {\n                    that.clearAllFiles();\n                }\n                return false;\n            },\n            _onFileProgress: function (e, percentComplete) {\n                var progressPct;\n                var warningPct;\n                if (percentComplete > 100) {\n                    percentComplete = 100;\n                }\n                if (!this.options.template) {\n                    progressPct = $('.k-upload-pct', e.target);\n                    warningPct = $('.k-i-warning', e.target);\n                    if (warningPct.length) {\n                        warningPct.removeClass('k-i-warning').removeClass('k-icon').addClass('k-upload-pct');\n                    } else if (progressPct.length === 0) {\n                        $('.k-upload-status', e.target).prepend('<span class=\\'k-upload-pct\\'></span>');\n                    }\n                    if (percentComplete !== 100) {\n                        $('.k-upload-pct', e.target).text(percentComplete + '%');\n                    } else {\n                        $('.k-upload-pct', e.target).remove();\n                    }\n                    $('.k-progress', e.target).width(percentComplete + '%');\n                } else {\n                    $('.k-progress', e.target).width(percentComplete + '%');\n                }\n                this.trigger(PROGRESS, {\n                    files: getFileEntry(e).data('fileNames'),\n                    percentComplete: percentComplete\n                });\n            },\n            _onUploadSuccess: function (e, response, xhr) {\n                var that = this;\n                var fileEntry = getFileEntry(e);\n                var prevented = that.trigger(SUCCESS, {\n                    files: fileEntry.data('fileNames'),\n                    response: response,\n                    operation: 'upload',\n                    XMLHttpRequest: xhr\n                });\n                if (prevented) {\n                    that._setUploadErrorState(fileEntry);\n                } else {\n                    that._fileState(fileEntry, 'uploaded');\n                    fileEntry.removeClass('k-file-progress').addClass('k-file-success');\n                    that._updateHeaderUploadStatus();\n                    if (that._supportsRemove()) {\n                        that._fileAction(fileEntry, REMOVE);\n                    } else {\n                        that._clearFileAction(fileEntry);\n                    }\n                }\n                that._hideUploadProgress(fileEntry);\n                that._checkAllComplete();\n            },\n            _onUploadError: function (e, xhr) {\n                var that = this;\n                var module = that._module;\n                var fileEntry = getFileEntry(e);\n                var fileUid = fileEntry.data('uid');\n                that._setUploadErrorState(fileEntry);\n                that.trigger(ERROR, {\n                    operation: 'upload',\n                    files: fileEntry.data('fileNames'),\n                    XMLHttpRequest: xhr\n                });\n                logToConsole('Server response: ' + xhr.responseText);\n                if (!that.options.async.chunkSize) {\n                    that._hideUploadProgress(fileEntry);\n                } else {\n                    if (module._decreasePosition) {\n                        module._decreasePosition(fileUid);\n                    }\n                }\n                that._checkAllComplete();\n                if (this.options.async.autoRetryAfter) {\n                    this._autoRetryAfter(fileEntry);\n                }\n            },\n            _autoRetryAfter: function (fileEntry) {\n                var that = this;\n                var retries = this._module.retries;\n                if (!retries) {\n                    return;\n                }\n                if (!retries[fileEntry.data('uid')]) {\n                    retries[fileEntry.data('uid')] = 1;\n                }\n                if (retries[fileEntry.data('uid')] <= this.options.async.maxAutoRetries) {\n                    retries[fileEntry.data('uid')]++;\n                    setTimeout(function () {\n                        that._module.performUpload(fileEntry);\n                    }, this.options.async.autoRetryAfter);\n                }\n            },\n            _setUploadErrorState: function (fileEntry) {\n                var that = this;\n                var uploadPercentage;\n                that._fileState(fileEntry, 'failed');\n                fileEntry.removeClass('k-file-progress').addClass('k-file-error');\n                that._updateUploadProgress(fileEntry);\n                uploadPercentage = $('.k-upload-pct', fileEntry);\n                if (uploadPercentage.length > 0) {\n                    if (!uploadPercentage.parent().find('.k-i-warning').length) {\n                        uploadPercentage.removeClass('k-upload-pct').addClass('k-icon k-i-warning');\n                    }\n                    uploadPercentage.empty();\n                } else {\n                    $('.k-upload-status', fileEntry).prepend('<span class=\\'k-icon k-i-warning\\'></span>');\n                }\n                this._updateHeaderUploadStatus();\n                this._fileAction(fileEntry, 'retry');\n                this._fileAction(fileEntry, REMOVE, true);\n                if (that._retryClicked) {\n                    fileEntry.find('.k-i-retry').parent().focus();\n                }\n            },\n            _updateUploadProgress: function (fileEntry) {\n                var that = this;\n                if (!that.options.async.chunkSize) {\n                    $('.k-progress', fileEntry).width('100%');\n                } else {\n                    var fileUid = fileEntry.data('uid');\n                    if (that._module.metaData) {\n                        var fileMetaData = that._module.metaData[fileUid];\n                        if (fileMetaData) {\n                            var percentComplete = fileMetaData.totalChunks ? Math.round(fileMetaData.chunkIndex / fileMetaData.totalChunks * 100) : 100;\n                            that._onFileProgress({ target: $(fileEntry, that.wrapper) }, percentComplete);\n                        }\n                    }\n                }\n            },\n            _hideUploadProgress: function (fileEntry) {\n                $('.k-progress', fileEntry).delay(PROGRESSHIDEDELAY).fadeOut(PROGRESSHIDEDURATION, function () {\n                    $(this).css('width', '0%');\n                });\n            },\n            _showUploadButton: function () {\n                var that = this;\n                var uploadButton = $('.k-upload-selected', that.wrapper);\n                var clearButton = $('.k-clear-selected', that.wrapper);\n                if (uploadButton.length === 0) {\n                    uploadButton = that._renderAction('', this.localization.uploadSelectedFiles).addClass('k-upload-selected');\n                    clearButton = that._renderAction('', this.localization.clearSelectedFiles).addClass('k-clear-selected');\n                }\n                this.wrapper.append(clearButton, uploadButton);\n            },\n            _hideUploadButton: function () {\n                $('.k-upload-selected, .k-clear-selected', this.wrapper).remove();\n            },\n            _showHeaderUploadStatus: function (isUploading) {\n                var that = this;\n                var localization = that.localization;\n                var dropZone = $('.k-dropzone', that.wrapper);\n                var headerUploadStatus = $('.k-upload-status-total', that.wrapper);\n                if (headerUploadStatus.length !== 0) {\n                    headerUploadStatus.remove();\n                }\n                headerUploadStatus = '<strong class=\"k-upload-status k-upload-status-total\"><span class=\"k-icon\"></span></strong>';\n                if (isUploading) {\n                    headerUploadStatus = $(headerUploadStatus).append(localization.headerStatusUploading);\n                    headerUploadStatus.find('.k-icon').addClass(headerStatusIcon.loading);\n                } else {\n                    headerUploadStatus = $(headerUploadStatus).append(localization.headerStatusUploaded);\n                    headerUploadStatus.find('.k-icon').addClass(headerStatusIcon.warning);\n                }\n                if (dropZone.length > 0) {\n                    dropZone.append(headerUploadStatus);\n                } else {\n                    $('.k-upload-button', that.wrapper).after(headerUploadStatus);\n                }\n            },\n            _updateHeaderUploadStatus: function () {\n                var that = this;\n                var headerUploadStatus = $('.k-upload-status-total', this.wrapper);\n                var currentlyUploading = $('.k-file', that.wrapper).not('.k-file-success, .k-file-error, .k-file-invalid');\n                var currentlyInvalid = $('.k-file-invalid', that.wrapper);\n                var currentlyFailed = $('.k-file-error', that.wrapper);\n                var currentlyPaused = $('.k-file', that.wrapper).find('.k-i-play-sm');\n                var failedUploads, headerUploadStatusIcon;\n                if (currentlyPaused.length && (currentlyPaused.length === currentlyUploading.length || !that.options.async.concurrent)) {\n                    headerUploadStatusIcon = $('.k-icon', headerUploadStatus).removeClass().addClass('k-icon').addClass('k-i-pause-sm');\n                    headerUploadStatus.html(headerUploadStatusIcon).append(that.localization.headerStatusPaused);\n                } else if (currentlyUploading.length === 0 || currentlyInvalid.length > 0 || currentlyFailed.length > 0) {\n                    failedUploads = $('.k-file.k-file-error, .k-file.k-file-invalid', that.wrapper);\n                    headerUploadStatus = $('.k-upload-status-total', that.wrapper);\n                    headerUploadStatusIcon = $('.k-icon', headerUploadStatus).removeClass().addClass('k-icon').addClass(failedUploads.length !== 0 ? headerStatusIcon.warning : headerStatusIcon.success);\n                    headerUploadStatus.html(headerUploadStatusIcon).append(that.localization.headerStatusUploaded);\n                }\n            },\n            _hideHeaderUploadstatus: function () {\n                $('.k-upload-status-total', this.wrapper).remove();\n            },\n            _onParentFormSubmit: function () {\n                var upload = this, element = upload.element;\n                if (typeof this._module.onAbort !== 'undefined') {\n                    this._module.onAbort();\n                }\n                if (!element.value) {\n                    var input = $(element);\n                    input.attr('disabled', 'disabled');\n                    window.setTimeout(function () {\n                        input.removeAttr('disabled');\n                    }, 0);\n                }\n            },\n            _onParentFormReset: function () {\n                $('.k-upload-files', this.wrapper).remove();\n            },\n            _supportsFormData: function () {\n                return typeof FormData != 'undefined';\n            },\n            _supportsMultiple: function () {\n                var windows = this._userAgent().indexOf('Windows') > -1;\n                return !kendo.support.browser.opera && !(kendo.support.browser.safari && windows);\n            },\n            _supportsDrop: function () {\n                var userAgent = this._userAgent().toLowerCase();\n                var isChrome = /chrome/.test(userAgent);\n                var isSafari = !isChrome && /safari/.test(userAgent);\n                var isWindowsSafari = isSafari && /windows/.test(userAgent);\n                return !isWindowsSafari && this._supportsFormData() && this.options.async.saveUrl;\n            },\n            _userAgent: function () {\n                return navigator.userAgent;\n            },\n            _setupDropZone: function () {\n                var that = this;\n                $('.k-upload-button', that.wrapper).wrap('<div class=\\'k-dropzone\\'></div>');\n                var ns = that._ns;\n                var dropZone = $('.k-dropzone', that.wrapper).append($('<em>' + that.localization.dropFilesHere + '</em>')).on('dragenter' + ns, stopEvent).on('dragover' + ns, function (e) {\n                    e.preventDefault();\n                }).on('drop' + ns, $.proxy(that._onDrop, that));\n                bindDragEventWrappers(dropZone, ns, function () {\n                    if (!dropZone.closest('.k-upload').hasClass('k-state-disabled')) {\n                        dropZone.addClass('k-dropzone-hovered');\n                    }\n                }, function () {\n                    dropZone.removeClass('k-dropzone-hovered');\n                });\n                that._bindDocumentDragEventWrappers(dropZone);\n            },\n            _setupCustomDropZone: function () {\n                var that = this;\n                var dropZone = $(that.options.dropZone);\n                $('.k-upload-button', that.wrapper).wrap('<div class=\\'k-dropzone\\'></div>').after($('<em>' + that.localization.dropFilesHere + '</em>'));\n                var ns = that._ns;\n                dropZone.on('dragenter' + ns, stopEvent).on('dragover' + ns, function (e) {\n                    e.preventDefault();\n                }).on('drop' + ns, $.proxy(that._onDrop, that));\n                bindDragEventWrappers(dropZone, ns, function (e) {\n                    if (!that.wrapper.hasClass('k-state-disabled')) {\n                        dropZone.removeClass('k-dropzone-hovered');\n                        $(e.target).addClass('k-dropzone-hovered');\n                    }\n                }, function () {\n                    dropZone.removeClass('k-dropzone-hovered');\n                    dropZone.find('.k-dropzone-hovered').removeClass('k-dropzone-hovered');\n                });\n                that._bindDocumentDragEventWrappers(dropZone);\n            },\n            _bindDocumentDragEventWrappers: function (dropZone) {\n                var that = this;\n                var ns = that._ns;\n                bindDragEventWrappers($(document), ns, function () {\n                    if (!that.wrapper.hasClass('k-state-disabled')) {\n                        dropZone.addClass('k-dropzone-active');\n                        dropZone.closest('.k-upload').removeClass('k-upload-empty');\n                    }\n                }, function () {\n                    dropZone.removeClass('k-dropzone-active');\n                    if ($('li.k-file', dropZone.closest('.k-upload')).length === 0) {\n                        dropZone.closest('.k-upload').addClass('k-upload-empty');\n                    }\n                });\n            },\n            _supportsRemove: function () {\n                return !!this.options.async.removeUrl;\n            },\n            _submitRemove: function (fileNames, eventArgs, onSuccess, onError) {\n                var upload = this, removeField = upload.options.async.removeField || 'fileNames', params = $.extend(eventArgs.data, antiForgeryTokens());\n                params[removeField] = fileNames;\n                jQuery.ajax({\n                    type: this.options.async.removeVerb,\n                    dataType: 'json',\n                    dataFilter: normalizeJSON,\n                    url: this.options.async.removeUrl,\n                    traditional: true,\n                    data: params,\n                    headers: eventArgs.headers,\n                    success: onSuccess,\n                    error: onError,\n                    xhrFields: { withCredentials: this.options.async.withCredentials }\n                });\n            },\n            _wrapInput: function (input) {\n                var that = this;\n                var options = that.options;\n                input.wrap('<div class=\\'k-widget k-upload k-header\\'><div class=\\'k-button k-upload-button\\' aria-label=\\'' + this.localization.select + '\\'></div></div>');\n                if (!options.async.saveUrl) {\n                    input.closest('.k-upload').addClass('k-upload-sync');\n                }\n                input.closest('.k-upload').addClass('k-upload-empty');\n                input.closest('.k-button').append('<span>' + this.localization.select + '</span>');\n                return input.closest('.k-upload');\n            },\n            _checkAllComplete: function () {\n                if ($('.k-file.k-file-progress', this.wrapper).length === 0) {\n                    this.trigger(COMPLETE);\n                }\n            },\n            _inputFiles: function (sourceInput) {\n                return inputFiles(sourceInput);\n            }\n        });\n        var syncUploadModule = function (upload) {\n            this.name = 'syncUploadModule';\n            this.element = upload.wrapper;\n            this.upload = upload;\n            this.element.closest('form').attr('enctype', 'multipart/form-data').attr('encoding', 'multipart/form-data');\n        };\n        syncUploadModule.prototype = {\n            onSelect: function (e, files) {\n                var upload = this.upload;\n                var sourceInput = $(e.target);\n                var filesContainErrors = upload._filesContainValidationErrors(files);\n                upload._addInput(sourceInput);\n                var fileData = { 'fileNames': files };\n                if (filesContainErrors) {\n                    sourceInput.remove();\n                } else {\n                    fileData.relatedInput = sourceInput;\n                }\n                var file = upload._enqueueFile(getFileName(sourceInput), fileData);\n                if (filesContainErrors) {\n                    upload._hideUploadProgress(file);\n                }\n                upload._fileAction(file, REMOVE);\n            },\n            onRemove: function (e) {\n                var fileEntry = getFileEntry(e);\n                var relatedInput = fileEntry.data('relatedInput');\n                if (relatedInput) {\n                    relatedInput.remove();\n                }\n                this.upload._removeFileEntry(fileEntry);\n            }\n        };\n        var iframeUploadModule = function (upload) {\n            this.name = 'iframeUploadModule';\n            this.element = upload.wrapper;\n            this.upload = upload;\n            this.iframes = [];\n        };\n        Upload._frameId = 0;\n        iframeUploadModule.prototype = {\n            onSelect: function (e, files) {\n                var upload = this.upload;\n                var sourceInput = $(e.target);\n                var hasValidationErrors = upload._filesContainValidationErrors(files);\n                var fileEntry = this.prepareUpload(sourceInput, files, hasValidationErrors);\n                if (upload.options.async.autoUpload) {\n                    if (!hasValidationErrors) {\n                        this.performUpload(fileEntry);\n                    } else {\n                        upload._fileAction(fileEntry, REMOVE);\n                        upload._showHeaderUploadStatus(false);\n                    }\n                } else {\n                    upload._fileAction(fileEntry, REMOVE);\n                    if (!hasValidationErrors) {\n                        upload._showUploadButton();\n                    } else {\n                        upload._updateHeaderUploadStatus();\n                    }\n                }\n                if (hasValidationErrors) {\n                    upload._hideUploadProgress(fileEntry);\n                }\n            },\n            prepareUpload: function (sourceInput, files, hasValidationErrors) {\n                var upload = this.upload;\n                var activeInput = $(upload.element);\n                var name = upload.options.async.saveField || sourceInput.attr('name');\n                var fileEntry, fileData, iframe, form;\n                upload._addInput(sourceInput);\n                sourceInput.attr('name', name);\n                if (!hasValidationErrors) {\n                    iframe = this.createFrame(upload.name + '_' + Upload._frameId++);\n                    this.registerFrame(iframe);\n                    form = this.createForm(upload.options.async.saveUrl, iframe.attr('name')).append(activeInput);\n                    fileData = {\n                        'frame': iframe,\n                        'relatedInput': activeInput,\n                        'fileNames': files\n                    };\n                } else {\n                    sourceInput.remove();\n                    fileData = { 'fileNames': files };\n                }\n                fileEntry = upload._enqueueFile(getFileName(sourceInput), fileData);\n                if (iframe) {\n                    iframe.data({\n                        'form': form,\n                        'file': fileEntry\n                    });\n                }\n                return fileEntry;\n            },\n            performUpload: function (fileEntry) {\n                var e = { files: fileEntry.data('fileNames') };\n                var iframe = fileEntry.data('frame');\n                var upload = this.upload;\n                if (!upload.trigger(UPLOAD, e)) {\n                    upload._hideUploadButton();\n                    upload._showHeaderUploadStatus(true);\n                    iframe.appendTo(document.body);\n                    var form = iframe.data('form').attr('action', upload.options.async.saveUrl).appendTo(document.body);\n                    e.data = $.extend({}, e.data, antiForgeryTokens());\n                    for (var key in e.data) {\n                        var dataInput = form.find('input[name=\\'' + key + '\\']');\n                        if (dataInput.length === 0) {\n                            dataInput = $('<input>', {\n                                type: 'hidden',\n                                name: key\n                            }).prependTo(form);\n                        }\n                        dataInput.val(e.data[key]);\n                    }\n                    upload._fileAction(fileEntry, CANCEL);\n                    upload._fileState(fileEntry, 'uploading');\n                    $(fileEntry).removeClass('k-file-error').addClass('k-file-progress');\n                    iframe.one('load', $.proxy(this.onIframeLoad, this));\n                    form[0].submit();\n                } else {\n                    upload._removeFileEntry(iframe.data('file'));\n                    this.cleanupFrame(iframe);\n                    this.unregisterFrame(iframe);\n                }\n            },\n            onSaveSelected: function () {\n                var module = this;\n                var upload = module.upload;\n                $('.k-file', this.element).each(function () {\n                    var fileEntry = $(this);\n                    var started = isFileUploadStarted(fileEntry);\n                    var hasValidationErrors = upload._filesContainValidationErrors(fileEntry.data('fileNames'));\n                    if (!started && !hasValidationErrors) {\n                        module.performUpload(fileEntry);\n                    }\n                });\n            },\n            onIframeLoad: function (e) {\n                var iframe = $(e.target), responseText;\n                try {\n                    responseText = iframe.contents().text();\n                } catch (ex) {\n                    responseText = 'Error trying to get server response: ' + ex;\n                }\n                this.processResponse(iframe, responseText);\n            },\n            processResponse: function (iframe, responseText) {\n                var fileEntry = iframe.data('file'), module = this, fakeXHR = { responseText: responseText };\n                tryParseJSON(responseText, function (jsonResult) {\n                    $.extend(fakeXHR, {\n                        statusText: 'OK',\n                        status: '200'\n                    });\n                    module.upload._onFileProgress({ target: $(fileEntry, module.upload.wrapper) }, 100);\n                    module.upload._onUploadSuccess({ target: $(fileEntry, module.upload.wrapper) }, jsonResult, fakeXHR);\n                    module.cleanupFrame(iframe);\n                    module.unregisterFrame(iframe);\n                }, function () {\n                    $.extend(fakeXHR, {\n                        statusText: 'error',\n                        status: '500'\n                    });\n                    module.upload._onUploadError({ target: $(fileEntry, module.upload.wrapper) }, fakeXHR);\n                });\n            },\n            onCancel: function (e) {\n                var iframe = $(e.target).data('frame');\n                this.stopFrameSubmit(iframe);\n                this.cleanupFrame(iframe);\n                this.unregisterFrame(iframe);\n                this.upload._removeFileEntry(iframe.data('file'));\n            },\n            onRetry: function (e) {\n                var fileEntry = getFileEntry(e);\n                this.performUpload(fileEntry);\n            },\n            onRemove: function (e, eventArgs, shouldSendRemoveRequest) {\n                var module = this;\n                var upload = module.upload;\n                var fileEntry = getFileEntry(e);\n                var iframe = fileEntry.data('frame');\n                if (iframe) {\n                    module.unregisterFrame(iframe);\n                    upload._removeFileEntry(fileEntry);\n                    module.cleanupFrame(iframe);\n                } else {\n                    if (fileEntry.hasClass('k-file-success')) {\n                        removeUploadedFile(fileEntry, upload, eventArgs, shouldSendRemoveRequest);\n                    } else {\n                        upload._removeFileEntry(fileEntry);\n                    }\n                }\n            },\n            onAbort: function () {\n                var element = this.element, module = this;\n                $.each(this.iframes, function () {\n                    $('input', this.data('form')).appendTo(element);\n                    module.stopFrameSubmit(this[0]);\n                    this.data('form').remove();\n                    this.remove();\n                });\n                this.iframes = [];\n            },\n            createFrame: function (id) {\n                return $('<iframe' + ' name=\\'' + id + '\\'' + ' id=\\'' + id + '\\'' + ' style=\\'display:none;\\' />');\n            },\n            createForm: function (action, target) {\n                return $('<form enctype=\\'multipart/form-data\\' method=\\'POST\\'' + ' action=\\'' + action + '\\'' + ' target=\\'' + target + '\\'' + '/>');\n            },\n            stopFrameSubmit: function (frame) {\n                if (typeof frame.stop != 'undefined') {\n                    frame.stop();\n                } else if (frame.document) {\n                    frame.document.execCommand('Stop');\n                }\n            },\n            registerFrame: function (frame) {\n                this.iframes.push(frame);\n            },\n            unregisterFrame: function (frame) {\n                this.iframes = $.grep(this.iframes, function (value) {\n                    return value.attr('name') != frame.attr('name');\n                });\n            },\n            cleanupFrame: function (frame) {\n                var form = frame.data('form');\n                frame.data('file').data('frame', null);\n                setTimeout(function () {\n                    form.remove();\n                    frame.remove();\n                }, 1);\n            }\n        };\n        var formDataUploadModule = function (upload) {\n            this.name = 'formDataUploadModule';\n            this.element = upload.wrapper;\n            this.upload = upload;\n            this.position = {};\n            this.metaData = {};\n            this.cancelled = {};\n            this.resume = {};\n            this.paused = {};\n            this.retries = {};\n        };\n        formDataUploadModule.prototype = {\n            onSelect: function (e, files) {\n                var upload = this.upload;\n                var module = this;\n                var sourceElement = $(e.target);\n                var fileEntries = this.prepareUpload(sourceElement, files);\n                var hasValidationErrors;\n                var prev;\n                $.each(fileEntries, function (index) {\n                    hasValidationErrors = upload._filesContainValidationErrors($(this.data('fileNames')));\n                    if (upload.options.async.autoUpload) {\n                        if (!hasValidationErrors) {\n                            if (upload.options.async.chunkSize) {\n                                module.prepareChunk(this);\n                                prev = this.prev();\n                                if (upload.options.async.concurrent || index === 0 && !prev.length || index === 0 && prev.hasClass('k-file-success')) {\n                                    module.performUpload(this);\n                                }\n                            } else {\n                                module.performUpload(this);\n                            }\n                        } else {\n                            upload._fileAction(this, REMOVE);\n                            upload._showHeaderUploadStatus(false);\n                        }\n                    } else {\n                        upload._fileAction(this, REMOVE);\n                        if (!hasValidationErrors) {\n                            upload._showUploadButton();\n                            this.addClass('k-toupload');\n                        } else {\n                            upload._updateHeaderUploadStatus();\n                        }\n                    }\n                    if (hasValidationErrors) {\n                        upload._hideUploadProgress(this);\n                    }\n                });\n            },\n            prepareUpload: function (sourceElement, files) {\n                var fileEntries = this.enqueueFiles(files);\n                if (sourceElement.is('input')) {\n                    $.each(fileEntries, function () {\n                        $(this).data('relatedInput', sourceElement);\n                    });\n                    sourceElement.data('relatedFileEntries', fileEntries);\n                    this.upload._addInput(sourceElement);\n                }\n                return fileEntries;\n            },\n            enqueueFiles: function (files) {\n                var upload = this.upload;\n                var name;\n                var i;\n                var filesLength = files.length;\n                var currentFile;\n                var fileEntry;\n                var fileEntries = [];\n                if (upload.options.async.batch === true) {\n                    name = $.map(files, function (file) {\n                        return file.name;\n                    }).join(', ');\n                    if (upload.directory || upload.options.directoryDrop) {\n                        $(files).each(function () {\n                            if (this.rawFile.webkitRelativePath || this.rawFile.relativePath) {\n                                this.name = this.rawFile.webkitRelativePath || this.rawFile.relativePath;\n                            }\n                        });\n                    }\n                    fileEntry = upload._enqueueFile(name, { fileNames: files });\n                    fileEntry.data('files', files);\n                    fileEntries.push(fileEntry);\n                } else {\n                    for (i = 0; i < filesLength; i++) {\n                        currentFile = files[i];\n                        name = currentFile.name;\n                        if (upload.directory || upload.options.directoryDrop) {\n                            if (currentFile.rawFile.webkitRelativePath || currentFile.rawFile.relativePath) {\n                                currentFile.name = currentFile.rawFile.webkitRelativePath || currentFile.rawFile.relativePath;\n                            }\n                        }\n                        fileEntry = upload._enqueueFile(name, { fileNames: [currentFile] });\n                        fileEntry.data('files', [currentFile]);\n                        fileEntries.push(fileEntry);\n                    }\n                }\n                return fileEntries;\n            },\n            performUpload: function (fileEntry) {\n                var upload = this.upload, formData = this.createFormData(), xhr = this.createXHR(), e = {\n                        files: fileEntry.data('fileNames'),\n                        XMLHttpRequest: xhr\n                    }, files;\n                if (!upload.trigger(UPLOAD, e)) {\n                    if (fileEntry.find('.k-i-cancel').length === 0) {\n                        if (upload.options.async.chunkSize) {\n                            upload._fileAction(fileEntry, PAUSE);\n                        }\n                        upload._fileAction(fileEntry, CANCEL, upload.options.async.chunkSize);\n                    }\n                    if (!upload.wrapper.find('.k-toupload').length) {\n                        upload._hideUploadButton();\n                    }\n                    upload._showHeaderUploadStatus(true);\n                    if (e.formData) {\n                        formData = e.formData;\n                    } else {\n                        e.data = $.extend({}, e.data, antiForgeryTokens());\n                        for (var key in e.data) {\n                            formData.append(key, e.data[key]);\n                        }\n                        files = fileEntry.data('files');\n                        if (files) {\n                            this.populateFormData(formData, files);\n                        }\n                    }\n                    upload._fileState(fileEntry, 'uploading');\n                    $(fileEntry).removeClass('k-file-error').addClass('k-file-progress');\n                    if (upload.options.async.useArrayBuffer && window.FileReader) {\n                        this._readFile(upload.options.async.saveUrl, formData, fileEntry, xhr);\n                    } else {\n                        this.postFormData(upload.options.async.saveUrl, formData, fileEntry, xhr);\n                    }\n                } else {\n                    this.removeFileEntry(fileEntry);\n                }\n            },\n            _readFile: function (saveUrl, formData, fileEntry, xhr) {\n                var that = this;\n                var upload = that.upload;\n                var file = fileEntry.data('files')[0];\n                var reader = new FileReader();\n                reader.onload = function (e) {\n                    try {\n                        if (!that.fileArrayBuffer) {\n                            that.fileArrayBuffer = e.target.result;\n                        } else {\n                            that.fileArrayBuffer = that._appendBuffer(that.fileArrayBuffer, e.target.result);\n                        }\n                    } catch (err) {\n                        upload._onUploadError({ target: $(fileEntry, upload.wrapper) }, xhr);\n                        return;\n                    }\n                    if (that.position[file.uid] > file.size) {\n                        that.postFormData(upload.options.async.saveUrl, that.fileArrayBuffer, fileEntry, xhr);\n                        that.fileArrayBuffer = null;\n                    } else {\n                        that._readFile(saveUrl, formData, fileEntry, xhr);\n                    }\n                };\n                reader.onerror = function () {\n                    upload._onUploadError({ target: $(fileEntry, upload.wrapper) }, xhr);\n                };\n                reader.readAsArrayBuffer(that._getCurrentChunk(file.rawFile, file.uid));\n            },\n            _appendBuffer: function (buffer1, buffer2) {\n                var tmp = new Uint8Array(buffer1.byteLength + buffer2.byteLength);\n                tmp.set(new Uint8Array(buffer1), 0);\n                tmp.set(new Uint8Array(buffer2), buffer1.byteLength);\n                return tmp.buffer;\n            },\n            onSaveSelected: function () {\n                var module = this;\n                var upload = module.upload;\n                $('.k-toupload', this.element).filter(function () {\n                    var fileEntry = $(this);\n                    var started = isFileUploadStarted(fileEntry);\n                    var hasValidationErrors = upload._filesContainValidationErrors(fileEntry.data('fileNames'));\n                    return !started && !hasValidationErrors;\n                }).each(function (index) {\n                    var fileEntry = $(this);\n                    var prevEntry = fileEntry.prev();\n                    fileEntry.removeClass('k-toupload');\n                    if (upload.options.async.chunkSize) {\n                        module.prepareChunk(fileEntry);\n                        if (upload.options.async.concurrent || index === 0 && !prevEntry.length || (index === 0 && prevEntry.hasClass('k-file-success') || prevEntry.hasClass('k-file-error'))) {\n                            module.performUpload(fileEntry);\n                        }\n                    } else {\n                        module.performUpload(fileEntry);\n                    }\n                });\n            },\n            onCancel: function (e) {\n                var fileEntry = getFileEntry(e);\n                if (this.upload.options.async.chunkSize) {\n                    this.cancelled[fileEntry.data('uid')] = true;\n                }\n                this.stopUploadRequest(fileEntry);\n                this.removeFileEntry(fileEntry);\n            },\n            onPause: function (e) {\n                var fileEntry = getFileEntry(e);\n                var fileUid = fileEntry.data('uid');\n                var async = this.upload.options.async;\n                if (async.chunkSize) {\n                    this.retries[fileUid] = async.maxAutoRetries + 1;\n                    this.paused[fileUid] = true;\n                    this.resume[fileUid] = false;\n                }\n            },\n            onResume: function (e) {\n                var fileEntry = getFileEntry(e);\n                var fileUid = fileEntry.data('uid');\n                if (this.upload.options.async.chunkSize) {\n                    delete this.paused[fileUid];\n                    this.resume[fileUid] = true;\n                    this.retries[fileEntry.data('uid')] = 1;\n                    this._increaseChunkIndex(fileUid);\n                    this.performUpload(fileEntry);\n                }\n            },\n            onRetry: function (e) {\n                var fileEntry = getFileEntry(e);\n                var async = this.upload.options.async;\n                if (async.chunkSize) {\n                    this.retries[fileEntry.data('uid')] = async.maxAutoRetries + 1;\n                    delete this.paused[fileEntry.data('uid')];\n                }\n                this.performUpload(fileEntry);\n            },\n            onRemove: function (e, eventArgs, shouldSendRemoveRequest) {\n                var module = this;\n                var upload = module.upload;\n                var fileEntry = getFileEntry(e);\n                var async = this.upload.options.async;\n                if (async.chunkSize) {\n                    this.retries[fileEntry.data('uid')] = async.maxAutoRetries + 1;\n                }\n                if (fileEntry.hasClass('k-file-success')) {\n                    removeUploadedFile(fileEntry, upload, eventArgs, shouldSendRemoveRequest);\n                } else {\n                    module.removeFileEntry(fileEntry);\n                }\n            },\n            createXHR: function () {\n                return new XMLHttpRequest();\n            },\n            postFormData: function (url, data, fileEntry, xhr) {\n                var module = this;\n                fileEntry.data('request', xhr);\n                xhr.addEventListener('load', function (e) {\n                    module.onRequestSuccess.call(module, e, fileEntry);\n                }, false);\n                xhr.addEventListener(ERROR, function (e) {\n                    module.onRequestError.call(module, e, fileEntry);\n                }, false);\n                xhr.upload.addEventListener('progress', function (e) {\n                    module.onRequestProgress.call(module, e, fileEntry);\n                }, false);\n                xhr.open('POST', url, true);\n                xhr.withCredentials = this.upload.options.async.withCredentials;\n                var accept = this.upload.options.async.accept;\n                if (accept) {\n                    xhr.setRequestHeader('Accept', accept);\n                }\n                xhr.send(data);\n            },\n            createFormData: function () {\n                return new FormData();\n            },\n            populateFormData: function (data, files) {\n                var chunk;\n                var i;\n                var length = files.length;\n                var uid;\n                var upload = this.upload;\n                if (upload.options.async.chunkSize) {\n                    uid = files[0].uid;\n                    chunk = this._getCurrentChunk(files[0].rawFile, uid);\n                    data.append(upload.options.async.saveField || upload.name, chunk);\n                    var serializedMetaData = JSON.stringify(this.metaData[uid]);\n                    data.append('metadata', serializedMetaData);\n                } else {\n                    for (i = 0; i < length; i++) {\n                        data.append(upload.options.async.saveField || upload.name, files[i].rawFile);\n                    }\n                }\n                return data;\n            },\n            onRequestSuccess: function (e, fileEntry) {\n                var xhr = e.target, module = this;\n                function raiseError() {\n                    module.upload._onUploadError({ target: $(fileEntry, module.upload.wrapper) }, xhr);\n                }\n                function parseSuccess(jsonResult) {\n                    var batch = module.upload.options.async.batch;\n                    var chunkSize = module.upload.options.async.chunkSize;\n                    var concurrent = module.upload.options.async.concurrent;\n                    var fileUid = jsonResult.fileUid;\n                    if (module.paused[fileUid] || module.cancelled[fileUid]) {\n                        return;\n                    }\n                    delete module.retries[fileUid];\n                    if (chunkSize && !batch && !jsonResult.uploaded) {\n                        module._increaseChunkIndex(fileUid);\n                        module.performUpload(fileEntry);\n                    } else if (chunkSize && !batch && !concurrent && fileEntry.next().length && !fileEntry.next().hasClass('k-toupload')) {\n                        module.upload._onFileProgress({ target: $(fileEntry, module.upload.wrapper) }, 100);\n                        module._resetChunkIndex(fileUid);\n                        module.upload._onUploadSuccess({ target: $(fileEntry, module.upload.wrapper) }, jsonResult, xhr);\n                        module.performUpload(fileEntry.next());\n                    } else {\n                        module.upload._onFileProgress({ target: $(fileEntry, module.upload.wrapper) }, 100);\n                        module.upload._onUploadSuccess({ target: $(fileEntry, module.upload.wrapper) }, jsonResult, xhr);\n                        module.cleanupFileEntry(fileEntry);\n                    }\n                }\n                if (xhr.status >= 200 && xhr.status <= 299) {\n                    tryParseJSON(xhr.responseText, parseSuccess, raiseError);\n                } else {\n                    raiseError();\n                }\n            },\n            onRequestError: function (e, fileEntry) {\n                var xhr = e.target;\n                this.upload._onUploadError({ target: $(fileEntry, this.upload.wrapper) }, xhr);\n            },\n            cleanupFileEntry: function (fileEntry) {\n                var relatedInput = fileEntry.data('relatedInput'), uploadComplete = true;\n                if (relatedInput) {\n                    $.each(relatedInput.data('relatedFileEntries') || [], function () {\n                        if (this.parent().length > 0 && this[0] != fileEntry[0]) {\n                            uploadComplete = uploadComplete && this.hasClass('k-file-success');\n                        }\n                    });\n                    if (uploadComplete) {\n                        relatedInput.remove();\n                    }\n                }\n            },\n            removeFileEntry: function (fileEntry) {\n                var chunkSize = this.upload.options.async.chunkSize;\n                var concurrent = this.upload.options.async.concurrent;\n                var isUploadButtonVisible = this.upload.wrapper.find('.k-upload-selected').length > 0;\n                this.cleanupFileEntry(fileEntry);\n                if (chunkSize && !concurrent && !isUploadButtonVisible) {\n                    if (fileEntry.next().length) {\n                        this.performUpload(fileEntry.next());\n                    }\n                }\n                this.upload._removeFileEntry(fileEntry);\n            },\n            onRequestProgress: function (e, fileEntry) {\n                var percentComplete = Math.round(e.loaded * 100 / e.total);\n                var fileUid = fileEntry.data('uid');\n                var fileMetaData;\n                if (this.upload.options.async.chunkSize) {\n                    fileMetaData = this.metaData[fileUid];\n                    percentComplete = fileMetaData && fileMetaData.totalChunks ? Math.round(fileMetaData.chunkIndex / fileMetaData.totalChunks * 100) : 100;\n                }\n                this.upload._onFileProgress({ target: $(fileEntry, this.upload.wrapper) }, percentComplete);\n            },\n            stopUploadRequest: function (fileEntry) {\n                fileEntry.data('request').abort();\n            },\n            prepareChunk: function (fileEntry) {\n                var file = fileEntry.data('files')[0];\n                var rawFile = file.rawFile;\n                var uid = file.uid;\n                var chunkSize = this.upload.options.async.chunkSize;\n                this.position[uid] = 0;\n                this.metaData[uid] = {\n                    chunkIndex: 0,\n                    contentType: rawFile.type,\n                    fileName: rawFile.name,\n                    relativePath: file.name,\n                    totalFileSize: rawFile.size,\n                    totalChunks: Math.ceil(rawFile.size / chunkSize),\n                    uploadUid: uid\n                };\n            },\n            _decreaseChunkIndex: function (uid) {\n                this.metaData[uid].chunkIndex--;\n            },\n            _increaseChunkIndex: function (uid) {\n                this.metaData[uid].chunkIndex++;\n            },\n            _resetChunkIndex: function (uid) {\n                this.metaData[uid].chunkIndex = 0;\n            },\n            _decreasePosition: function (uid) {\n                this.position[uid] -= this.upload.options.async.chunkSize;\n            },\n            _getCurrentChunk: function (file, uid) {\n                var oldPosition = this.position[uid];\n                var methodToInvoke;\n                var async = this.upload.options.async;\n                var chunkSize = async.chunkSize || async.bufferChunkSize;\n                if (!this.position[uid]) {\n                    this.position[uid] = 0;\n                }\n                this.position[uid] += chunkSize;\n                if (!!(methodToInvoke = this._getChunker(file))) {\n                    return file[methodToInvoke](oldPosition, this.position[uid]);\n                } else {\n                    return file;\n                }\n            },\n            _getChunker: function (file) {\n                if (file.slice) {\n                    return 'slice';\n                } else if (file.mozSlice) {\n                    return 'mozSlice';\n                } else if (file.webkitSlice) {\n                    return 'webkitSlice';\n                } else {\n                    return null;\n                }\n            }\n        };\n        function getFileName(input) {\n            return $.map(inputFiles(input), function (file) {\n                return file.name;\n            }).join(', ');\n        }\n        function inputFiles($input) {\n            var input = $input[0];\n            if (input.files) {\n                return getAllFileInfo(input.files);\n            } else {\n                return [{\n                        name: stripPath(input.value),\n                        extension: getFileExtension(input.value),\n                        size: null\n                    }];\n            }\n        }\n        function getAllFileInfo(rawFiles) {\n            return $.map(rawFiles, function (file) {\n                return getFileInfo(file);\n            });\n        }\n        function getFileInfo(rawFile) {\n            var fileName = rawFile.name || rawFile.fileName;\n            return {\n                name: kendo.htmlEncode(fileName),\n                extension: getFileExtension(fileName),\n                size: typeof rawFile.size == 'number' ? rawFile.size : rawFile.fileSize,\n                rawFile: rawFile\n            };\n        }\n        function getFileExtension(fileName) {\n            var matches = fileName.match(rFileExtension);\n            return matches ? matches[0] : '';\n        }\n        function stripPath(name) {\n            var slashIndex = name.lastIndexOf('\\\\');\n            return slashIndex != -1 ? name.substr(slashIndex + 1) : name;\n        }\n        function assignGuidToFiles(files, unique) {\n            var uid = kendo.guid();\n            return $.map(files, function (file) {\n                file.uid = unique ? kendo.guid() : uid;\n                return file;\n            });\n        }\n        function validateFiles(files, validationInfo) {\n            var allowedExtensions = parseAllowedExtensions(validationInfo.allowedExtensions);\n            var maxFileSize = validationInfo.maxFileSize;\n            var minFileSize = validationInfo.minFileSize;\n            for (var i = 0; i < files.length; i++) {\n                validateFileExtension(files[i], allowedExtensions);\n                validateFileSize(files[i], minFileSize, maxFileSize);\n            }\n        }\n        function parseAllowedExtensions(extensions) {\n            var allowedExtensions = $.map(extensions, function (ext) {\n                var parsedExt = ext.substring(0, 1) === '.' ? ext : '.' + ext;\n                return parsedExt.toLowerCase();\n            });\n            return allowedExtensions;\n        }\n        function validateFileExtension(file, allowedExtensions) {\n            if (allowedExtensions.length > 0) {\n                if (allowedExtensions.indexOf(file.extension.toLowerCase()) < 0) {\n                    file.validationErrors = file.validationErrors || [];\n                    if ($.inArray(INVALIDFILEEXTENSION, file.validationErrors) === -1) {\n                        file.validationErrors.push(INVALIDFILEEXTENSION);\n                    }\n                }\n            }\n        }\n        function validateFileSize(file, minFileSize, maxFileSize) {\n            if (minFileSize !== 0 && file.size < minFileSize) {\n                file.validationErrors = file.validationErrors || [];\n                if ($.inArray(INVALIDMINFILESIZE, file.validationErrors) === -1) {\n                    file.validationErrors.push(INVALIDMINFILESIZE);\n                }\n            }\n            if (maxFileSize !== 0 && file.size > maxFileSize) {\n                file.validationErrors = file.validationErrors || [];\n                if ($.inArray(INVALIDMAXFILESIZE, file.validationErrors) === -1) {\n                    file.validationErrors.push(INVALIDMAXFILESIZE);\n                }\n            }\n        }\n        function getTotalFilesSizeMessage(files) {\n            var totalSize = 0;\n            if (typeof files[0].size == 'number') {\n                for (var i = 0; i < files.length; i++) {\n                    if (files[i].size) {\n                        totalSize += files[i].size;\n                    }\n                }\n            } else {\n                return '';\n            }\n            totalSize /= 1024;\n            if (totalSize < 1024) {\n                return totalSize.toFixed(2) + ' KB';\n            } else {\n                return (totalSize / 1024).toFixed(2) + ' MB';\n            }\n        }\n        function shouldRemoveFileEntry(upload) {\n            return !upload.multiple && $('.k-file', upload.wrapper).length > 1;\n        }\n        function removeUploadedFile(fileEntry, upload, eventArgs, shouldSendRemoveRequest) {\n            if (!upload._supportsRemove()) {\n                if (shouldRemoveFileEntry(upload) || !shouldSendRemoveRequest) {\n                    upload._removeFileEntry(fileEntry);\n                }\n                return;\n            }\n            var files = fileEntry.data('fileNames');\n            var fileNames = $.map(files, function (file) {\n                return file.name;\n            });\n            if (shouldSendRemoveRequest === false) {\n                upload._removeFileEntry(fileEntry);\n                return;\n            }\n            upload._submitRemove(fileNames, eventArgs, function onSuccess(data, textStatus, xhr) {\n                var prevented = upload.trigger(SUCCESS, {\n                    operation: 'remove',\n                    files: files,\n                    response: data,\n                    XMLHttpRequest: xhr\n                });\n                if (!prevented) {\n                    upload._removeFileEntry(fileEntry);\n                }\n            }, function onError(xhr) {\n                if (shouldRemoveFileEntry(upload)) {\n                    upload._removeFileEntry(fileEntry);\n                }\n                upload.trigger(ERROR, {\n                    operation: 'remove',\n                    files: files,\n                    XMLHttpRequest: xhr\n                });\n                logToConsole('Server response: ' + xhr.responseText);\n            });\n        }\n        function tryParseJSON(input, onSuccess, onError) {\n            var success = false, json = '';\n            try {\n                json = $.parseJSON(normalizeJSON(input));\n                success = true;\n            } catch (e) {\n                onError();\n            }\n            if (success) {\n                onSuccess(json);\n            }\n        }\n        function normalizeJSON(input) {\n            if (typeof input === 'undefined' || input === '') {\n                input = '{}';\n            }\n            return input;\n        }\n        function stopEvent(e) {\n            e.stopPropagation();\n            e.preventDefault();\n        }\n        function bindDragEventWrappers(element, namespace, onDragEnter, onDragLeave) {\n            var hideInterval, lastDrag;\n            element.on('dragenter' + namespace, function (e) {\n                onDragEnter(e);\n                lastDrag = new Date();\n                if (!hideInterval) {\n                    hideInterval = setInterval(function () {\n                        var sinceLastDrag = new Date() - lastDrag;\n                        if (sinceLastDrag > 100) {\n                            onDragLeave();\n                            clearInterval(hideInterval);\n                            hideInterval = null;\n                        }\n                    }, 100);\n                }\n            }).on('dragover' + namespace, function () {\n                lastDrag = new Date();\n            });\n        }\n        function isFileUploadStarted(fileEntry) {\n            return fileEntry.is('.k-file-progress, .k-file-success, .k-file-error');\n        }\n        function getFileEntry(e) {\n            return $(e.target).closest('.k-file');\n        }\n        kendo.ui.plugin(Upload);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}