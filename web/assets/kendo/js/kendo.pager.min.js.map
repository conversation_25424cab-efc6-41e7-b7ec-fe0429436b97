{"version": 3, "sources": ["kendo.pager.js"], "names": ["f", "define", "$", "undefined", "button", "template", "idx", "text", "numeric", "title", "ns", "kendo", "icon", "className", "wrapClassName", "iconTemplate", "substring", "update", "element", "selector", "page", "disabled", "find", "parent", "attr", "toggleClass", "first", "FIRST", "prev", "PREV", "Math", "max", "next", "totalPages", "NEXT", "min", "last", "LAST", "window", "ui", "Widget", "proxy", "SIZE", "CHANGE", "NS", "CLICK", "KEYDOWN", "DISABLED", "MOUSEDOWN", "DOCUMENT_ELEMENT", "document", "documentElement", "MAX_VALUE", "Number", "Pager", "extend", "init", "options", "pageSizes", "pageItems", "that", "this", "sizeClassName", "fn", "call", "_createDataSource", "linkTemplate", "selectTemplate", "currentPageTemplate", "_refresh<PERSON><PERSON><PERSON>", "refresh", "dataSource", "bind", "downEvent", "applyEventMap", "guid", "previousNext", "length", "append", "messages", "previous", "list", "appendTo", "total", "empty", "wrap", "input", "format", "of", "on", "_keydown", "map", "size", "toLowerCase", "allPages", "itemsPerPage", "html", "join", "end", "val", "pageSize", "DropDownList", "show", "kendoDropDownList", "_change", "_refreshClick", "info", "_click", "addClass", "_toggleActive", "autoBind", "_resizeHandler", "resize", "_getWidthSizeClass", "width", "notify", "destroy", "off", "unbind", "events", "name", "buttonCount", "responsive", "display", "morePages", "setDataSource", "fetch", "_resize", "el", "hasClass", "removeClass", "data", "DataSource", "create", "e", "reminder", "hasAll", "selectAll", "start", "collapsedTotal", "_collapsedTotal", "_hideList", "action", "endless", "filter", "keyCode", "keys", "ENTER", "parseInt", "isNaN", "preventDefault", "read", "value", "currentTarget", "_pageSize", "_take", "_skip", "target", "eventTarget", "contains", "is", "ceil", "trigger", "index", "sizes", "split", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,eAAgB,cAAeD,IACxC,WA2VE,MAnVC,UAAUE,EAAGC,GAEV,QAASC,GAAOC,EAAUC,EAAKC,EAAMC,EAASC,GAC1C,MAAOJ,IACHC,IAAKA,EACLC,KAAMA,EACNG,GAAIC,EAAMD,GACVF,QAASA,EACTC,MAAOA,GAAS,KAGxB,QAASG,GAAKC,EAAWN,EAAMO,GAC3B,MAAOC,IACHF,UAAWA,EAAUG,UAAU,GAC/BT,KAAMA,EACNO,cAAeA,GAAiB,KAGxC,QAASG,GAAOC,EAASC,EAAUC,EAAMC,GACrCH,EAAQI,KAAKH,GAAUI,SAASC,KAAKb,EAAMa,KAAK,QAASJ,GAAMI,KAAK,eAAgBC,YAAY,mBAAoBJ,GAExH,QAASK,GAAMR,EAASE,GACpBH,EAAOC,EAASS,EAAO,EAAGP,GAAQ,GAEtC,QAASQ,GAAKV,EAASE,GACnBH,EAAOC,EAASW,EAAMC,KAAKC,IAAI,EAAGX,EAAO,GAAIA,GAAQ,GAEzD,QAASY,GAAKd,EAASE,EAAMa,GACzBhB,EAAOC,EAASgB,EAAMJ,KAAKK,IAAIF,EAAYb,EAAO,GAAIA,GAAQa,GAElE,QAASG,GAAKlB,EAASE,EAAMa,GACzBhB,EAAOC,EAASmB,EAAMJ,EAAYb,GAAQa,GA/BjD,GACOtB,GAAQ2B,OAAO3B,MAAO4B,EAAK5B,EAAM4B,GAAIC,EAASD,EAAGC,OAAQC,EAAQvC,EAAEuC,MAAOd,EAAQ,sBAAuBU,EAAO,uBAAwBR,EAAO,qBAAsBK,EAAO,sBAAuBQ,EAAO,mCAAoCC,EAAS,SAAUC,EAAK,cAAeC,EAAQ,QAASC,EAAU,UAAWC,EAAW,WAAYC,EAAY,OAAQC,EAAmB/C,EAAEgD,SAASC,iBAAkBC,EAAYC,OAAOD,UAAWrC,EAAeJ,EAAMN,SAAS,oJAgCldiD,EAAQd,EAAOe,QACfC,KAAM,SAAUtC,EAASuC,GAAnB,GACerC,GAAMa,EAmDXyB,EAMAC,EAzDRC,EAAOC,KACPC,EAAgB,IACpBtB,GAAOuB,GAAGP,KAAKQ,KAAKJ,EAAM1C,EAASuC,GACnCA,EAAUG,EAAKH,QACfG,EAAKK,kBAAkBR,GACvBG,EAAKM,aAAevD,EAAMN,SAASuD,EAAKH,QAAQS,cAChDN,EAAKO,eAAiBxD,EAAMN,SAASuD,EAAKH,QAAQU,gBAClDP,EAAKQ,oBAAsBzD,EAAMN,SAASuD,EAAKH,QAAQW,qBACvDhD,EAAOwC,EAAKxC,OACZa,EAAa2B,EAAK3B,aAClB2B,EAAKS,gBAAkB5B,EAAMmB,EAAKU,QAASV,GAC3CA,EAAKW,WAAWC,KAAK7B,EAAQiB,EAAKS,iBAClCT,EAAKa,UAAY9D,EAAM+D,cAAc1B,EAAWrC,EAAMgE,QAClDlB,EAAQmB,eACHhB,EAAK1C,QAAQI,KAAKK,GAAOkD,SAC1BjB,EAAK1C,QAAQ4D,OAAOlE,EAAKe,EAAO8B,EAAQsB,SAASrD,MAAO,kBACxDA,EAAMkC,EAAK1C,QAASE,EAAMa,IAEzB2B,EAAK1C,QAAQI,KAAKO,GAAMgD,SACzBjB,EAAK1C,QAAQ4D,OAAOlE,EAAKiB,EAAM4B,EAAQsB,SAASC,WAChDpD,EAAKgC,EAAK1C,QAASE,EAAMa,KAG7BwB,EAAQjD,UACRoD,EAAKqB,KAAOrB,EAAK1C,QAAQI,KAAK,oBACzBsC,EAAKqB,KAAKJ,SACXjB,EAAKqB,KAAO/E,EAAE,0CAA0CgF,SAAStB,EAAK1C,UAEtEuC,EAAQc,aAAed,EAAQc,WAAWY,SAC1CvB,EAAKqB,KAAKG,QAAQN,OAAOlB,EAAKQ,qBAAsB7D,KAAM,KAAMuE,OAAOlB,EAAKO,gBAAiB5D,KAAM,KAEvGqD,EAAKqB,KAAKI,KAAK,6CAEf5B,EAAQ6B,QACH1B,EAAK1C,QAAQI,KAAK,kBAAkBuD,QACrCjB,EAAK1C,QAAQ4D,OAAO,uCAAyCrB,EAAQsB,SAAS3D,KAAO,4BAA8BT,EAAM4E,OAAO9B,EAAQsB,SAASS,GAAIvD,GAAc,WAEvK2B,EAAK1C,QAAQuE,GAAG3C,EAAUF,EAAI,uBAAwBH,EAAMmB,EAAK8B,SAAU9B,KAE3EH,EAAQmB,eACHhB,EAAK1C,QAAQI,KAAKY,GAAM2C,SACzBjB,EAAK1C,QAAQ4D,OAAOlE,EAAKsB,EAAMuB,EAAQsB,SAAS/C,OAChDA,EAAK4B,EAAK1C,QAASE,EAAMa,IAExB2B,EAAK1C,QAAQI,KAAKe,GAAMwC,SACzBjB,EAAK1C,QAAQ4D,OAAOlE,EAAKyB,EAAMoB,EAAQsB,SAAS3C,KAAM,iBACtDA,EAAKwB,EAAK1C,QAASE,EAAMa,KAG7BwB,EAAQC,YACHE,EAAK1C,QAAQI,KAAK,kBAAkBuD,SACjCnB,EAAYD,EAAQC,UAAUmB,OAASpB,EAAQC,WAC/C,MACA,EACA,GACA,IAEAC,EAAYzD,EAAEyF,IAAIjC,EAAW,SAAUkC,GACvC,MAAIA,GAAKC,aAAsC,QAAvBD,EAAKC,cAClB,uBAA2BpC,EAAQsB,SAASe,SAAW,YAE3D,WAAaF,EAAO,cAE/B1F,EAAE,gDAAkDuD,EAAQsB,SAASgB,aAAe,WAAWb,SAAStB,EAAK1C,SAASI,KAAK,UAAU0E,KAAKrC,EAAUsC,KAAK,KAAKC,MAAMhB,SAAStB,EAAK1C,UAEtL0C,EAAK1C,QAAQI,KAAK,yBAAyB6E,IAAIvC,EAAKwC,YAChDzF,EAAM4B,GAAG8D,cACTzC,EAAK1C,QAAQI,KAAK,yBAAyBgF,OAAOC,oBAEtD3C,EAAK1C,QAAQuE,GAAG9C,EAASC,EAAI,wBAAyBH,EAAMmB,EAAK4C,QAAS5C,KAE1EH,EAAQa,UACHV,EAAK1C,QAAQI,KAAK,oBAAoBuD,QACvCjB,EAAK1C,QAAQ4D,OAAO,qDAAuDrB,EAAQsB,SAAST,QAAU,iBAAmBb,EAAQsB,SAAST,QAAU,iDAExJV,EAAK1C,QAAQuE,GAAG5C,EAAQD,EAAI,mBAAoBH,EAAMmB,EAAK6C,cAAe7C,KAE1EH,EAAQiD,OACH9C,EAAK1C,QAAQI,KAAK,iBAAiBuD,QACpCjB,EAAK1C,QAAQ4D,OAAO,0CAG5BlB,EAAK1C,QAAQuE,GAAG5C,EAAQD,EAAI,IAAKH,EAAMmB,EAAK+C,OAAQ/C,IAAOgD,SAAS,qCACpEhD,EAAK1C,QAAQuE,GAAG5C,EAAQD,EAAI,kBAAmBH,EAAMmB,EAAKiD,cAAejD,IACrEH,EAAQqD,UACRlD,EAAKU,UAETV,EAAKmD,eAAiBtE,EAAMmB,EAAKoD,OAAQpD,GAAM,GAC/C1D,EAAEoC,QAAQmD,GAAG,SAAW7C,EAAIgB,EAAKmD,gBACjCjD,EAAgBF,EAAKqD,mBAAmBrD,EAAK1C,QAAQgG,SACjDpD,GACAF,EAAK1C,QAAQ0F,SAAS9C,GAE1BnD,EAAMwG,OAAOvD,IAEjBwD,QAAS,WACL,GAAIxD,GAAOC,IACXrB,GAAOuB,GAAGqD,QAAQpD,KAAKJ,GACvBA,EAAK1C,QAAQmG,IAAIzE,GACjBgB,EAAKW,WAAW+C,OAAO3E,EAAQiB,EAAKS,iBACpCT,EAAKS,gBAAkB,KACvBnE,EAAEoC,QAAQ+E,IAAI,SAAWzE,EAAIiB,KAAKkD,gBAClCpG,EAAMyG,QAAQxD,EAAK1C,SACnB0C,EAAK1C,QAAU0C,EAAKqB,KAAO,MAE/BsC,QAAS5E,GACTc,SACI+D,KAAM,QACNrD,eAAgB,yDAChBC,oBAAqB,kFACrBF,aAAc,qIACduD,YAAa,GACbX,UAAU,EACVtG,SAAS,EACTkG,MAAM,EACNpB,OAAO,EACPV,cAAc,EACdlB,WAAW,EACXY,SAAS,EACToD,YAAY,EACZ3C,UACIe,SAAU,MACV6B,QAAS,yBACTvC,MAAO,sBACPhE,KAAM,OACNoE,GAAI,SACJO,aAAc,iBACdrE,MAAO,uBACPsD,SAAU,0BACVhD,KAAM,sBACNI,KAAM,sBACNkC,QAAS,UACTsD,UAAW,eAGnBC,cAAe,SAAUtD,GACrB,GAAIX,GAAOC,IACXD,GAAKW,WAAW+C,OAAO3E,EAAQiB,EAAKS,iBACpCT,EAAKW,WAAaX,EAAKH,QAAQc,WAAaA,EAC5CA,EAAWC,KAAK7B,EAAQiB,EAAKS,iBACzBT,EAAKH,QAAQqD,UACbvC,EAAWuD,SAGnBC,QAAS,SAAUnC,GAAV,GAEG9B,GACAkE,CAFJpC,GAAKsB,QACDpD,EAAgBD,KAAKoD,mBAAmBrB,EAAKsB,OAC7Cc,EAAKnE,KAAK3C,QACT4C,EAEOkE,EAAGC,SAASnE,KACpBkE,EAAGE,YAAYxF,GACfsF,EAAGpB,SAAS9C,IAHZkE,EAAGE,YAAYxF,KAO3BuB,kBAAmB,SAAUR,GACzBI,KAAKU,WAAa5D,EAAMwH,KAAKC,WAAWC,OAAO5E,EAAQc,aAE3DD,QAAS,SAAUgE,GAAV,GACYhI,GAAK4F,EAAgBqC,EA4C9BC,EACAC,EACAlI,EA9CJqD,EAAOC,KAAgB6E,EAAQ,EAAatH,EAAOwC,EAAKxC,OAAQ4E,EAAO,GAAIvC,EAAUG,EAAKH,QAAS2C,EAAWxC,EAAKwC,WAAYuC,EAAiB/E,EAAKgF,kBAAmBzD,EAAQvB,EAAKW,WAAWY,QAASlD,EAAa2B,EAAK3B,aAAciC,EAAeN,EAAKM,aAAcuD,EAAchE,EAAQgE,WAErS,IADAxE,EAAiBqE,OAAO1D,EAAKa,UAAWvE,EAAEuC,MAAMmB,EAAKiF,UAAWjF,KAC5D0E,GAAiB,cAAZA,EAAEQ,OAAX,CAGA,GAAIrF,EAAQjD,QAAS,CASjB,IARIY,EAAOqG,IACPc,EAAWnH,EAAOqG,EAClBiB,EAAqB,IAAbH,EAAiBnH,EAAOqG,EAAc,EAAIrG,EAAOmH,EAAW,GAExErC,EAAMpE,KAAKK,IAAIuG,EAAQjB,EAAc,EAAGxF,GACpCyG,EAAQ,IACR1C,GAAQ5F,EAAO8D,EAAcwE,EAAQ,EAAG,OAAO,EAAOjF,EAAQsB,SAAS6C,YAEtEtH,EAAMoI,EAAOpI,GAAO4F,EAAK5F,IAC1B0F,GAAQ5F,EAAOE,GAAOc,EAAOwC,EAAKO,eAAiBD,EAAc5D,EAAKA,GAAK,EAE3E4F,GAAMjE,IACN+D,GAAQ5F,EAAO8D,EAAc5D,EAAK,OAAO,EAAOmD,EAAQsB,SAAS6C,YAExD,KAAT5B,IACAA,EAAOpC,EAAKO,gBAAiB5D,KAAM,KAEvCyF,EAAOnC,KAAKO,qBAAsB7D,KAAMa,IAAU4E,EAClDpC,EAAKqB,KAAKiD,YAAY,oBAAoBlC,KAAKA,GAE/CvC,EAAQiD,OAEJV,EADAb,EAAQ,EACDxE,EAAM4E,OAAO9B,EAAQsB,SAAS4C,QAAS/D,EAAKW,WAAWd,QAAQsF,QAAU,EAAIjH,KAAKK,KAAKf,EAAO,IAAMwC,EAAKW,WAAW6B,YAAc,GAAK,EAAGuC,GAAiB7G,KAAKK,IAAIf,EAAOgF,EAAUuC,GAAiBxD,GAEtM1B,EAAQsB,SAASK,MAE5BxB,EAAK1C,QAAQI,KAAK,iBAAiB0E,KAAKA,IAExCvC,EAAQ6B,OACR1B,EAAK1C,QAAQI,KAAK,kBAAkB0E,KAAKpC,EAAKH,QAAQsB,SAAS3D,KAAO,wCAA0CA,EAAO,KAAOT,EAAM4E,OAAO9B,EAAQsB,SAASS,GAAIvD,IAAaX,KAAK,SAAS6E,IAAI/E,GAAMI,KAAKuB,EAAUoC,EAAQ,GAAG1D,YAAY,mBAAoB0D,EAAQ,GAEvQ1B,EAAQmB,eACRlD,EAAMkC,EAAK1C,QAASE,EAAMa,GAC1BL,EAAKgC,EAAK1C,QAASE,EAAMa,GACzBD,EAAK4B,EAAK1C,QAASE,EAAMa,GACzBG,EAAKwB,EAAK1C,QAASE,EAAMa,IAEzBwB,EAAQC,YACJ8E,EAAS5E,EAAK1C,QAAQI,KAAK,sCAAwCuD,OAAS,EAC5E4D,EAAYD,IAAWpC,IAAavC,KAAKU,WAAWY,SAAWiB,GAAYhD,GAC3E7C,EAAO6F,EACPqC,IACArC,EAAW,MACX7F,EAAOkD,EAAQsB,SAASe,UAE5BlC,EAAK1C,QAAQI,KAAK,yBAAyB6E,IAAIC,GAAU5E,KAAK,aAAc4E,GAAU4C,OAAO,IAAMrI,EAAMa,KAAK,QAAU,kBAAkB+E,kBAAkB,QAASH,GAAUG,kBAAkB,OAAQhG,MAGjNqI,gBAAiB,WACb,MAAO/E,MAAKU,WAAWY,SAE3BO,SAAU,SAAU4C,GAChB,GAAIA,EAAEW,UAAYtI,EAAMuI,KAAKC,MAAO,CAChC,GAAI7D,GAAQzB,KAAK3C,QAAQI,KAAK,kBAAkBA,KAAK,SAAUF,EAAOgI,SAAS9D,EAAMa,MAAO,KACxFkD,MAAMjI,IAASA,EAAO,GAAKA,EAAOyC,KAAK5B,gBACvCb,EAAOyC,KAAKzC,QAEhBkE,EAAMa,IAAI/E,GACVyC,KAAKzC,KAAKA,KAGlBqF,cAAe,SAAU6B,GACrBA,EAAEgB,iBACFzF,KAAKU,WAAWgF,QAEpB/C,QAAS,SAAU8B,GAAV,GACDkB,GAAQlB,EAAEmB,cAAcD,MACxBpD,EAAWgD,SAASI,EAAO,IAC3BjF,EAAaV,KAAKU,UACjB8E,OAAMjD,GAE8B,QAA7BoD,EAAQ,IAAI3D,gBACpBtB,EAAWmF,UAAYvJ,EACvBoE,EAAWoF,MAAQxJ,EACnBoE,EAAWqF,MAAQ,EACnBrF,EAAWuD,SALXvD,EAAW6B,SAASA,IAQ5BS,cAAe,WACX,GAAIjD,GAAOC,IACPD,GAAKqB,KAAKgD,SAAS,oBACnBhF,EAAiBqE,OAAO1D,EAAKa,UAAWvE,EAAEuC,MAAMmB,EAAKiF,UAAWjF,IAEhEX,EAAiBuB,KAAKZ,EAAKa,UAAWvE,EAAEuC,MAAMmB,EAAKiF,UAAWjF,IAElEA,EAAKqB,KAAKxD,YAAY,qBAE1BoH,UAAW,SAAUP,GACjB,GAAI1E,GAAOC,KAAMgG,EAASlJ,EAAMmJ,YAAYxB,EACvCpI,GAAE6J,SAASnG,EAAKqB,KAAK,GAAI4E,KAC1B5G,EAAiBqE,OAAO1D,EAAKa,UAAWvE,EAAEuC,MAAMmB,EAAKiF,UAAWjF,IAChEA,EAAKqB,KAAKiD,YAAY,sBAG9BvB,OAAQ,SAAU2B,GACd,GAAIuB,GAAS3J,EAAEoI,EAAEmB,cACjBnB,GAAEgB,iBACGO,EAAOG,GAAG,sBACXnG,KAAKzC,KAAKyI,EAAOrI,KAAKb,EAAMa,KAAK,WAGzCS,WAAY,WACR,MAAOH,MAAKmI,MAAMpG,KAAKU,WAAWY,SAAW,IAAMtB,KAAKuC,YAAc,KAE1EA,SAAU,WACN,MAAOvC,MAAKU,WAAW6B,YAAcvC,KAAKU,WAAWY,SAEzD/D,KAAM,SAAUA,GACZ,MAAIA,KAASjB,EAOL0D,KAAKU,WAAWY,QAAU,EACnBtB,KAAKU,WAAWnD,OAEhB,GATPyC,KAAKqG,QAAQ,cAAgBC,MAAO/I,MAGxCyC,KAAKU,WAAWnD,KAAKA,GACrByC,KAAKqG,QAAQvH,GAAUwH,MAAO/I,KAJ9B,IAaR6F,mBAAoB,SAAUC,GAC1B,GAAItD,GAAOC,KAAMuG,EAAQ1H,EAAK2H,MAAM,IACpC,OAAKzG,GAAKH,QAAQiE,WAEPR,GAAS,IACTkD,EAAM,GACNlD,GAAS,IACTkD,EAAM,GACNlD,GAAS,KACTkD,EAAM,GAEV,KARI,OAWnB7H,GAAG+H,OAAOhH,IACZhB,OAAO3B,MAAM4J,QACRjI,OAAO3B,OACE,kBAAVV,SAAwBA,OAAOuK,IAAMvK,OAAS,SAAUwK,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.pager.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.pager', ['kendo.data'], f);\n}(function () {\n    var __meta__ = {\n        id: 'pager',\n        name: 'Pager',\n        category: 'framework',\n        depends: ['data'],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, Widget = ui.Widget, proxy = $.proxy, FIRST = '.k-i-arrow-end-left', LAST = '.k-i-arrow-end-right', PREV = '.k-i-arrow-60-left', NEXT = '.k-i-arrow-60-right', SIZE = 'k-pager-lg k-pager-md k-pager-sm', CHANGE = 'change', NS = '.kendoPager', CLICK = 'click', KEYDOWN = 'keydown', DISABLED = 'disabled', MOUSEDOWN = 'down', DOCUMENT_ELEMENT = $(document.documentElement), MAX_VALUE = Number.MAX_VALUE, iconTemplate = kendo.template('<a href=\"\\\\#\" aria-label=\"#=text#\" title=\"#=text#\" class=\"k-link k-pager-nav #= wrapClassName #\"><span class=\"k-icon #= className #\"></span></a>');\n        function button(template, idx, text, numeric, title) {\n            return template({\n                idx: idx,\n                text: text,\n                ns: kendo.ns,\n                numeric: numeric,\n                title: title || ''\n            });\n        }\n        function icon(className, text, wrapClassName) {\n            return iconTemplate({\n                className: className.substring(1),\n                text: text,\n                wrapClassName: wrapClassName || ''\n            });\n        }\n        function update(element, selector, page, disabled) {\n            element.find(selector).parent().attr(kendo.attr('page'), page).attr('tabindex', -1).toggleClass('k-state-disabled', disabled);\n        }\n        function first(element, page) {\n            update(element, FIRST, 1, page <= 1);\n        }\n        function prev(element, page) {\n            update(element, PREV, Math.max(1, page - 1), page <= 1);\n        }\n        function next(element, page, totalPages) {\n            update(element, NEXT, Math.min(totalPages, page + 1), page >= totalPages);\n        }\n        function last(element, page, totalPages) {\n            update(element, LAST, totalPages, page >= totalPages);\n        }\n        var Pager = Widget.extend({\n            init: function (element, options) {\n                var that = this, page, totalPages;\n                var sizeClassName = null;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                that._createDataSource(options);\n                that.linkTemplate = kendo.template(that.options.linkTemplate);\n                that.selectTemplate = kendo.template(that.options.selectTemplate);\n                that.currentPageTemplate = kendo.template(that.options.currentPageTemplate);\n                page = that.page();\n                totalPages = that.totalPages();\n                that._refreshHandler = proxy(that.refresh, that);\n                that.dataSource.bind(CHANGE, that._refreshHandler);\n                that.downEvent = kendo.applyEventMap(MOUSEDOWN, kendo.guid());\n                if (options.previousNext) {\n                    if (!that.element.find(FIRST).length) {\n                        that.element.append(icon(FIRST, options.messages.first, 'k-pager-first'));\n                        first(that.element, page, totalPages);\n                    }\n                    if (!that.element.find(PREV).length) {\n                        that.element.append(icon(PREV, options.messages.previous));\n                        prev(that.element, page, totalPages);\n                    }\n                }\n                if (options.numeric) {\n                    that.list = that.element.find('.k-pager-numbers');\n                    if (!that.list.length) {\n                        that.list = $('<ul class=\"k-pager-numbers k-reset\" />').appendTo(that.element);\n                    }\n                    if (options.dataSource && !options.dataSource.total()) {\n                        that.list.empty().append(that.currentPageTemplate({ text: 0 })).append(that.selectTemplate({ text: 0 }));\n                    }\n                    that.list.wrap('<div class=\"k-pager-numbers-wrap\"></div>');\n                }\n                if (options.input) {\n                    if (!that.element.find('.k-pager-input').length) {\n                        that.element.append('<span class=\"k-pager-input k-label\">' + options.messages.page + '<input class=\"k-textbox\">' + kendo.format(options.messages.of, totalPages) + '</span>');\n                    }\n                    that.element.on(KEYDOWN + NS, '.k-pager-input input', proxy(that._keydown, that));\n                }\n                if (options.previousNext) {\n                    if (!that.element.find(NEXT).length) {\n                        that.element.append(icon(NEXT, options.messages.next));\n                        next(that.element, page, totalPages);\n                    }\n                    if (!that.element.find(LAST).length) {\n                        that.element.append(icon(LAST, options.messages.last, 'k-pager-last'));\n                        last(that.element, page, totalPages);\n                    }\n                }\n                if (options.pageSizes) {\n                    if (!that.element.find('.k-pager-sizes').length) {\n                        var pageSizes = options.pageSizes.length ? options.pageSizes : [\n                            'all',\n                            5,\n                            10,\n                            20\n                        ];\n                        var pageItems = $.map(pageSizes, function (size) {\n                            if (size.toLowerCase && size.toLowerCase() === 'all') {\n                                return '<option value=\\'all\\'>' + options.messages.allPages + '</option>';\n                            }\n                            return '<option>' + size + '</option>';\n                        });\n                        $('<span class=\"k-pager-sizes k-label\"><select/>' + options.messages.itemsPerPage + '</span>').appendTo(that.element).find('select').html(pageItems.join('')).end().appendTo(that.element);\n                    }\n                    that.element.find('.k-pager-sizes select').val(that.pageSize());\n                    if (kendo.ui.DropDownList) {\n                        that.element.find('.k-pager-sizes select').show().kendoDropDownList();\n                    }\n                    that.element.on(CHANGE + NS, '.k-pager-sizes select', proxy(that._change, that));\n                }\n                if (options.refresh) {\n                    if (!that.element.find('.k-pager-refresh').length) {\n                        that.element.append('<a href=\"#\" class=\"k-pager-refresh k-link\" title=\"' + options.messages.refresh + '\" aria-label=\"' + options.messages.refresh + '\"><span class=\"k-icon k-i-reload\"></span></a>');\n                    }\n                    that.element.on(CLICK + NS, '.k-pager-refresh', proxy(that._refreshClick, that));\n                }\n                if (options.info) {\n                    if (!that.element.find('.k-pager-info').length) {\n                        that.element.append('<span class=\"k-pager-info k-label\" />');\n                    }\n                }\n                that.element.on(CLICK + NS, 'a', proxy(that._click, that)).addClass('k-pager-wrap k-widget k-floatwrap');\n                that.element.on(CLICK + NS, '.k-current-page', proxy(that._toggleActive, that));\n                if (options.autoBind) {\n                    that.refresh();\n                }\n                that._resizeHandler = proxy(that.resize, that, true);\n                $(window).on('resize' + NS, that._resizeHandler);\n                sizeClassName = that._getWidthSizeClass(that.element.width());\n                if (sizeClassName) {\n                    that.element.addClass(sizeClassName);\n                }\n                kendo.notify(that);\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.element.off(NS);\n                that.dataSource.unbind(CHANGE, that._refreshHandler);\n                that._refreshHandler = null;\n                $(window).off('resize' + NS, this._resizeHandler);\n                kendo.destroy(that.element);\n                that.element = that.list = null;\n            },\n            events: [CHANGE],\n            options: {\n                name: 'Pager',\n                selectTemplate: '<li><span class=\"k-state-selected\">#=text#</span></li>',\n                currentPageTemplate: '<li class=\"k-current-page\"><span class=\"k-link k-pager-nav\">#=text#</span></li>',\n                linkTemplate: '<li><a tabindex=\"-1\" href=\"\\\\#\" class=\"k-link\" data-#=ns#page=\"#=idx#\" #if (title !== \"\") {# title=\"#=title#\" #}#>#=text#</a></li>',\n                buttonCount: 10,\n                autoBind: true,\n                numeric: true,\n                info: true,\n                input: false,\n                previousNext: true,\n                pageSizes: false,\n                refresh: false,\n                responsive: true,\n                messages: {\n                    allPages: 'All',\n                    display: '{0} - {1} of {2} items',\n                    empty: 'No items to display',\n                    page: 'Page',\n                    of: 'of {0}',\n                    itemsPerPage: 'items per page',\n                    first: 'Go to the first page',\n                    previous: 'Go to the previous page',\n                    next: 'Go to the next page',\n                    last: 'Go to the last page',\n                    refresh: 'Refresh',\n                    morePages: 'More pages'\n                }\n            },\n            setDataSource: function (dataSource) {\n                var that = this;\n                that.dataSource.unbind(CHANGE, that._refreshHandler);\n                that.dataSource = that.options.dataSource = dataSource;\n                dataSource.bind(CHANGE, that._refreshHandler);\n                if (that.options.autoBind) {\n                    dataSource.fetch();\n                }\n            },\n            _resize: function (size) {\n                if (size.width) {\n                    var sizeClassName = this._getWidthSizeClass(size.width);\n                    var el = this.element;\n                    if (!sizeClassName) {\n                        el.removeClass(SIZE);\n                    } else if (!el.hasClass(sizeClassName)) {\n                        el.removeClass(SIZE);\n                        el.addClass(sizeClassName);\n                    }\n                }\n            },\n            _createDataSource: function (options) {\n                this.dataSource = kendo.data.DataSource.create(options.dataSource);\n            },\n            refresh: function (e) {\n                var that = this, idx, end, start = 1, reminder, page = that.page(), html = '', options = that.options, pageSize = that.pageSize(), collapsedTotal = that._collapsedTotal(), total = that.dataSource.total(), totalPages = that.totalPages(), linkTemplate = that.linkTemplate, buttonCount = options.buttonCount;\n                DOCUMENT_ELEMENT.unbind(that.downEvent, $.proxy(that._hideList, that));\n                if (e && e.action == 'itemchange') {\n                    return;\n                }\n                if (options.numeric) {\n                    if (page > buttonCount) {\n                        reminder = page % buttonCount;\n                        start = reminder === 0 ? page - buttonCount + 1 : page - reminder + 1;\n                    }\n                    end = Math.min(start + buttonCount - 1, totalPages);\n                    if (start > 1) {\n                        html += button(linkTemplate, start - 1, '...', false, options.messages.morePages);\n                    }\n                    for (idx = start; idx <= end; idx++) {\n                        html += button(idx == page ? that.selectTemplate : linkTemplate, idx, idx, true);\n                    }\n                    if (end < totalPages) {\n                        html += button(linkTemplate, idx, '...', false, options.messages.morePages);\n                    }\n                    if (html === '') {\n                        html = that.selectTemplate({ text: 0 });\n                    }\n                    html = this.currentPageTemplate({ text: page }) + html;\n                    that.list.removeClass('k-state-expanded').html(html);\n                }\n                if (options.info) {\n                    if (total > 0) {\n                        html = kendo.format(options.messages.display, that.dataSource.options.endless ? 1 : Math.min((page - 1) * (that.dataSource.pageSize() || 0) + 1, collapsedTotal), Math.min(page * pageSize, collapsedTotal), total);\n                    } else {\n                        html = options.messages.empty;\n                    }\n                    that.element.find('.k-pager-info').html(html);\n                }\n                if (options.input) {\n                    that.element.find('.k-pager-input').html(that.options.messages.page + '<input class=\"k-textbox\" aria-label=\"' + page + '\">' + kendo.format(options.messages.of, totalPages)).find('input').val(page).attr(DISABLED, total < 1).toggleClass('k-state-disabled', total < 1);\n                }\n                if (options.previousNext) {\n                    first(that.element, page, totalPages);\n                    prev(that.element, page, totalPages);\n                    next(that.element, page, totalPages);\n                    last(that.element, page, totalPages);\n                }\n                if (options.pageSizes) {\n                    var hasAll = that.element.find('.k-pager-sizes option[value=\\'all\\']').length > 0;\n                    var selectAll = hasAll && (pageSize === this.dataSource.total() || pageSize == MAX_VALUE);\n                    var text = pageSize;\n                    if (selectAll) {\n                        pageSize = 'all';\n                        text = options.messages.allPages;\n                    }\n                    that.element.find('.k-pager-sizes select').val(pageSize).attr('aria-label', pageSize).filter('[' + kendo.attr('role') + '=dropdownlist]').kendoDropDownList('value', pageSize).kendoDropDownList('text', text);\n                }\n            },\n            _collapsedTotal: function () {\n                return this.dataSource.total();\n            },\n            _keydown: function (e) {\n                if (e.keyCode === kendo.keys.ENTER) {\n                    var input = this.element.find('.k-pager-input').find('input'), page = parseInt(input.val(), 10);\n                    if (isNaN(page) || page < 1 || page > this.totalPages()) {\n                        page = this.page();\n                    }\n                    input.val(page);\n                    this.page(page);\n                }\n            },\n            _refreshClick: function (e) {\n                e.preventDefault();\n                this.dataSource.read();\n            },\n            _change: function (e) {\n                var value = e.currentTarget.value;\n                var pageSize = parseInt(value, 10);\n                var dataSource = this.dataSource;\n                if (!isNaN(pageSize)) {\n                    dataSource.pageSize(pageSize);\n                } else if ((value + '').toLowerCase() == 'all') {\n                    dataSource._pageSize = undefined;\n                    dataSource._take = undefined;\n                    dataSource._skip = 0;\n                    dataSource.fetch();\n                }\n            },\n            _toggleActive: function () {\n                var that = this;\n                if (that.list.hasClass('k-state-expanded')) {\n                    DOCUMENT_ELEMENT.unbind(that.downEvent, $.proxy(that._hideList, that));\n                } else {\n                    DOCUMENT_ELEMENT.bind(that.downEvent, $.proxy(that._hideList, that));\n                }\n                that.list.toggleClass('k-state-expanded');\n            },\n            _hideList: function (e) {\n                var that = this, target = kendo.eventTarget(e);\n                if (!$.contains(that.list[0], target)) {\n                    DOCUMENT_ELEMENT.unbind(that.downEvent, $.proxy(that._hideList, that));\n                    that.list.removeClass('k-state-expanded');\n                }\n            },\n            _click: function (e) {\n                var target = $(e.currentTarget);\n                e.preventDefault();\n                if (!target.is('.k-state-disabled')) {\n                    this.page(target.attr(kendo.attr('page')));\n                }\n            },\n            totalPages: function () {\n                return Math.ceil((this.dataSource.total() || 0) / (this.pageSize() || 1));\n            },\n            pageSize: function () {\n                return this.dataSource.pageSize() || this.dataSource.total();\n            },\n            page: function (page) {\n                if (page !== undefined) {\n                    if (this.trigger('pageChange', { index: page })) {\n                        return;\n                    }\n                    this.dataSource.page(page);\n                    this.trigger(CHANGE, { index: page });\n                } else {\n                    if (this.dataSource.total() > 0) {\n                        return this.dataSource.page();\n                    } else {\n                        return 0;\n                    }\n                }\n            },\n            _getWidthSizeClass: function (width) {\n                var that = this, sizes = SIZE.split(' ');\n                if (!that.options.responsive) {\n                    return null;\n                } else if (width <= 480) {\n                    return sizes[2];\n                } else if (width <= 640) {\n                    return sizes[1];\n                } else if (width <= 1024) {\n                    return sizes[0];\n                }\n                return null;\n            }\n        });\n        ui.plugin(Pager);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}