/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.mobile.splitview.min",["kendo.mobile.pane.min"],e)}(function(){return function(e,i){var n=window.kendo,t=n.mobile.ui,o=t.Widget,a="<div class='km-expanded-pane-shim' />",l=t.View,s=l.extend({init:function(i,l){var s,r,c=this;o.fn.init.call(c,i,l),i=c.element,e.extend(c,l),c._id(),c.options.$angular?c._overlay():(c._layout(),c._overlay()),c._style(),r=i.children(c._locate("modalview")),c.options.$angular?r.each(function(i,t){n.compileMobileDirective(e(t),l.$angular[0])}):n.mobile.init(r),c.panes=[],c._paramsHistory=[],c.options.$angular?(c.element.children(n.directiveSelector("pane")).each(function(){s=n.compileMobileDirective(e(this),l.$angular[0]),c.panes.push(s)}),c.element.children(n.directiveSelector("header footer")).each(function(){n.compileMobileDirective(e(this),l.$angular[0])})):c.content.children(n.roleSelector("pane")).each(function(){s=n.initWidget(this,{},t.roles),c.panes.push(s)}),c.expandedPaneShim=e(a).appendTo(c.element),c._shimUserEvents=new n.UserEvents(c.expandedPaneShim,{fastTap:!0,tap:function(){c.collapsePanes()}})},_locate:function(e){return this.options.$angular?n.directiveSelector(e):n.roleSelector(e)},options:{name:"SplitView",style:"horizontal"},expandPanes:function(){this.element.addClass("km-expanded-splitview")},collapsePanes:function(){this.element.removeClass("km-expanded-splitview")},_layout:function(){var e=this,i=e.element;e.transition=n.attrValue(i,"transition"),n.mobile.ui.View.prototype._layout.call(this),n.mobile.init(this.header.add(this.footer)),e.element.addClass("km-splitview"),e.content.addClass("km-split-content")},_style:function(){var i,n=this.options.style,t=this.element;n&&(i=n.split(" "),e.each(i,function(){t.addClass("km-split-"+this)}))},showStart:function(){var i=this;i.element.css("display",""),i.inited?this._invokeNgController():(i.inited=!0,e.each(i.panes,function(){this.options.initial?this.navigateToInitial():this.navigate("")}),i.trigger("init",{view:i})),i.trigger("show",{view:i})}});t.plugin(s)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,i,n){(n||i)()});
//# sourceMappingURL=kendo.mobile.splitview.min.js.map
