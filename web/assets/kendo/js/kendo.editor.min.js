/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("util/undoredostack.min",["kendo.core.min"],e)}(function(){!function(e){var t=e.Observable.extend({init:function(t){e.Observable.fn.init.call(this,t),this.clear()},events:["undo","redo"],push:function(e){this.stack=this.stack.slice(0,this.currentCommandIndex+1),this.currentCommandIndex=this.stack.push(e)-1},undo:function(){if(this.canUndo()){var e=this.stack[this.currentCommandIndex--];e.undo(),this.trigger("undo",{command:e})}},redo:function(){if(this.canRedo()){var e=this.stack[++this.currentCommandIndex];e.redo(),this.trigger("redo",{command:e})}},clear:function(){this.stack=[],this.currentCommandIndex=-1},canUndo:function(){return this.currentCommandIndex>=0},canRedo:function(){return this.currentCommandIndex!=this.stack.length-1}});e.deepExtend(e,{util:{UndoRedoStack:t}})}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/main.min",["util/undoredostack.min","kendo.combobox.min","kendo.dropdownlist.min","kendo.window.min","kendo.colorpicker.min"],e)}(function(){!function(e,t){var n,i,o,r,a=window.kendo,s=a.Class,l=a.ui.Widget,d=a.support.mobileOS,c=a.support.browser,u=e.extend,f=e.proxy,p=a.deepExtend,m=a.keys,h="select",g="select.k-select-overlay",b="k-placeholder",v="placeholder",k=200,y=s.extend({init:function(e){this.options=e},getHtml:function(){var e=this.options;return a.template(e.template,{useWithBlock:!1})(e)}}),w={editorWrapperTemplate:'<table cellspacing="4" cellpadding="0" class="k-widget k-editor k-header" role="presentation"><tbody><tr role="presentation"><td class="k-editor-toolbar-wrap" role="presentation"><ul class="k-editor-toolbar" role="toolbar" /></td></tr><tr><td class="k-editable-area" /></tr></tbody></table>',buttonTemplate:'# var iconCssClass= "k-icon k-i-" + kendo.toHyphens(data.cssClass.replace("k-", ""));#<a tabindex="0" role="button" class="k-tool"#= data.popup ? " data-popup" : "" # unselectable="on" title="#= data.title #"><span unselectable="on" class="k-tool-icon #= iconCssClass #"></span><span class="k-tool-text">#= data.title #</span></a>',colorPickerTemplate:'<div class="k-colorpicker k-icon k-i-#= data.cssClass.replace("k-", "") #" />',comboBoxTemplate:'<select title="#= data.title #" class="#= data.cssClass #" />',dropDownListTemplate:'<span class="k-editor-dropdown"><select title="#= data.title #" class="#= data.cssClass #" /></span>',separatorTemplate:'<span class="k-separator" />',overflowAnchorTemplate:'<a tabindex="0" role="button" class="k-tool k-overflow-anchor" data-popup unselectable="on" title="#= data.title #" aria-haspopup="true" aria-expanded="false"><span unselectable="on" class="k-icon k-i-more-vertical"></span><span class="k-tool-text">#= data.title #</span></a>',formatByName:function(t,n){for(var i=0;i<n.length;i++)if(e.inArray(t,n[i].tags)>=0)return n[i]},getToolCssClass:function(e){var t={superscript:"sup-script",subscript:"sub-script",justifyLeft:"align-left",justifyCenter:"align-center",justifyRight:"align-right",justifyFull:"align-justify",insertUnorderedList:"list-unordered",insertOrderedList:"list-ordered","import":"login",indent:"indent-increase",outdent:"indent-decrease",createLink:"link-horizontal",unlink:"unlink-horizontal",insertImage:"image",insertFile:"file-add",viewHtml:"html",foreColor:"foreground-color",backColor:"paint",createTable:"table-insert",addColumnLeft:"table-column-insert-left",addColumnRight:"table-column-insert-right",addRowAbove:"table-row-insert-above",addRowBelow:"table-row-insert-below",deleteRow:"table-row-delete",deleteColumn:"table-column-delete",tableWizard:"table-properties",tableWizardInsert:"table-wizard",cleanFormatting:"clear-css"},n=t[e];return n?n:e},registerTool:function(e,t){var n=t.options;n&&n.template&&(n.template.options.cssClass="k-"+w.getToolCssClass(e)),t.name||(t.options.name=e,t.name=e.toLowerCase()),_.defaultTools[e]=t},registerFormat:function(e,t){_.fn.options.formats[e]=t},cacheComments:function(e,t){for(var n in t)e=e.replace(t[n],"{"+n+"}");return e},retrieveComments:function(e,t){for(var n in t)e=e.replace("{"+n+"}",t[n]);return e}},x={bold:"Bold",italic:"Italic",underline:"Underline",strikethrough:"Strikethrough",superscript:"Superscript",subscript:"Subscript",justifyCenter:"Center text",justifyLeft:"Align text left",justifyRight:"Align text right",justifyFull:"Justify",insertUnorderedList:"Insert unordered list",insertOrderedList:"Insert ordered list",indent:"Indent",outdent:"Outdent",createLink:"Insert hyperlink",unlink:"Remove hyperlink",insertImage:"Insert image",insertFile:"Insert file",insertHtml:"Insert HTML",viewHtml:"View HTML",fontName:"Select font family",fontNameInherit:"(inherited font)",fontSize:"Select font size",fontSizeInherit:"(inherited size)",formatBlock:"Format",formatting:"Format",foreColor:"Color",backColor:"Background color",style:"Styles",emptyFolder:"Empty Folder",editAreaTitle:"Editable area. Press F10 for toolbar.",uploadFile:"Upload",overflowAnchor:"More tools",orderBy:"Arrange by:",orderBySize:"Size",orderByName:"Name",invalidFileType:'The selected file "{0}" is not valid. Supported file types are {1}.',deleteFile:'Are you sure you want to delete "{0}"?',overwriteFile:'A file with name "{0}" already exists in the current directory. Do you want to overwrite it?',directoryNotFound:"A directory with this name was not found.",imageWebAddress:"Web address",imageAltText:"Alternate text",imageWidth:"Width (px)",imageHeight:"Height (px)",fileWebAddress:"Web address",fileTitle:"Title",fileText:"Text",linkWebAddress:"Web address",linkText:"Text",linkToolTip:"ToolTip",linkOpenInNewWindow:"Open link in new window",dialogUpdate:"Update",dialogInsert:"Insert",dialogOk:"Ok",dialogCancel:"Cancel",cleanFormatting:"Clean formatting",createTable:"Create a table",createTableHint:"Create a {0} x {1} table",addColumnLeft:"Add column on the left",addColumnRight:"Add column on the right",addRowAbove:"Add row above",addRowBelow:"Add row below",deleteRow:"Delete row",deleteColumn:"Delete column",tableWizard:"Table Wizard",tableTab:"Table",cellTab:"Cell",accessibilityTab:"Accessibility",caption:"Caption",summary:"Summary",width:"Width",height:"Height",units:"Units",cellSpacing:"Cell Spacing",cellPadding:"Cell Padding",cellMargin:"Cell Margin",alignment:"Alignment",background:"Background",cssClass:"CSS Class",id:"ID",border:"Border",borderStyle:"Border Style",collapseBorders:"Collapse borders",wrapText:"Wrap text",associateCellsWithHeaders:"Associate cells with headers",alignLeft:"Align Left",alignCenter:"Align Center",alignRight:"Align Right",alignLeftTop:"Align Left Top",alignCenterTop:"Align Center Top",alignRightTop:"Align Right Top",alignLeftMiddle:"Align Left Middle",alignCenterMiddle:"Align Center Middle",alignRightMiddle:"Align Right Middle",alignLeftBottom:"Align Left Bottom",alignCenterBottom:"Align Center Bottom",alignRightBottom:"Align Right Bottom",alignRemove:"Remove Alignment",columns:"Columns",rows:"Rows",selectAllCells:"Select All Cells",exportAs:"Export As","import":"Import"},C=!d||d.ios&&d.flatVersion>=500||!d.ios&&t!==document.documentElement.contentEditable,T={basic:["bold","italic","underline"],alignment:["justifyLeft","justifyCenter","justifyRight"],lists:["insertUnorderedList","insertOrderedList"],indenting:["indent","outdent"],links:["createLink","unlink"],tables:["tableWizard","createTable","addColumnLeft","addColumnRight","addRowAbove","addRowBelow","deleteRow","deleteColumn"]},_=l.extend({init:function(n,i){var o,r,s,d,c,m,h=this,g=a.ui.editor,b=g.Dom;C&&(l.fn.init.call(h,n,i),h.options=p({},h.options,i),h.options.tools=h.options.tools.slice(),n=h.element,m=n[0],d=b.name(m),this._registerHandler(n.closest("form"),"submit",f(h.update,h,t)),s=u({},h.options),s.editor=h,"textarea"==d?(h._wrapTextarea(),r=h.wrapper.find(".k-editor-toolbar"),m.id&&r.attr("aria-controls",m.id)):(h.element.attr("contenteditable",!0).addClass("k-widget k-editor k-editor-inline"),s.popup=!0,r=e('<ul class="k-editor-toolbar" role="toolbar" />').insertBefore(n)),h.toolbar=new g.Toolbar(r[0],s),h.toolbar.bindTo(h),"textarea"==d&&setTimeout(function(){var e=h.wrapper[0].style.height,t=parseInt(e,10),n=h.wrapper.height();e.indexOf("px")>0&&!isNaN(t)&&n>t&&h.wrapper.height(t-(n-t))}),h._resizable(),h._initializeContentElement(h),h.keyboard=new g.Keyboard([new g.BackspaceHandler(h),new g.TypingHandler(h),new g.SystemHandler(h),new g.SelectAllHandler(h)]),h.clipboard=new g.Clipboard(this),h.undoRedoStack=new a.util.UndoRedoStack,i&&i.value?o=i.value:h.textarea?(o=m.value,h.options.encoded&&e.trim(m.defaultValue).length&&(o=m.defaultValue),c=b.getAllComments(e("<div></div>").html(o)[0]),o=w.cacheComments(o,c),o=o.replace(/[\r\n\v\f\t ]+/gi," "),o=w.retrieveComments(o,c)):o=m.innerHTML,h.value(o||"\ufeff"),this._registerHandler(document,{mousedown:function(){h._endTyping()},mouseup:function(e){h._mouseup(e)}}),h._initializeImmutables(),h.toolbar.resize(),a.notify(h))},setOptions:function(e){var t=this;l.fn.setOptions.call(t,e),e.tools&&t.toolbar.bindTo(t),this._initializePlaceholder()},_togglePlaceholder:function(t){var n=this,i=n.body,o=e(i),r=n.options.placeholder;n.textarea&&r&&(o.attr("aria-label",function(){return t?r:""}),o.toggleClass(b,t))},_endTyping:function(){var e=this.keyboard;try{e.isTypingInProgress()&&(e.endTyping(!0),this.saveSelection())}catch(t){}},_selectionChange:function(){this._selectionStarted=!1,this.saveSelection(),this.trigger("select",{})},_resizable:function(){var n,i,o=this.options.resizable,r=e.isPlainObject(o)?o.content===t||o.content===!0:o;r&&this.textarea&&(n=e("<div class='k-resize-handle'><span class='k-icon k-i-arrow-45-down-right' /></div>").insertAfter(this.textarea),this.wrapper.addClass("k-resizable"),this.wrapper.kendoResizable(u({},this.options.resizable,{draggableElement:n,start:function(t){var n=this.editor=e(t.currentTarget).closest(".k-editor");this.initialSize=n.height(),n.find("td:last").append("<div class='k-overlay' />")},resize:function(e){var t=e.y.initialDelta,n=this.initialSize+t,i=this.options.min||0,o=this.options.max||1/0;n=Math.min(o,Math.max(i,n)),this.editor.height(n)},resizeend:function(){this.editor.find(".k-overlay").remove(),this.editor=null}})),a.support.mobileOS.ios&&(i=this.wrapper.getKendoResizable(),i.draggable.options.ignore=g))},_initializeTableResizing:function(){var e=this;a.ui.editor.TableResizing.create(e),e._showTableResizeHandlesProxy=f(e._showTableResizeHandles,e),e.bind(h,e._showTableResizeHandlesProxy)},_destroyTableResizing:function(){var e=this,t=e.tableResizing;t&&(t.destroy(),e.tableResizing=null),e._showTableResizeHandlesProxy&&e.unbind(h,e._showTableResizeHandlesProxy)},_showTableResizeHandles:function(){var e=this,t=e.tableResizing;t&&t.showResizeHandles()},_initializeColumnResizing:function(){a.ui.editor.ColumnResizing.create(this)},_destroyColumnResizing:function(){var e=this;e.columnResizing&&(e.columnResizing.destroy(),e.columnResizing=null)},_initializeRowResizing:function(){a.ui.editor.RowResizing.create(this)},_destroyRowResizing:function(){var e=this;e.rowResizing&&(e.rowResizing.destroy(),e.rowResizing=null)},_wrapTextarea:function(){var t=this,n=t.element,i=n[0].style.width,o=n[0].style.height,r=w.editorWrapperTemplate,a=e(r).insertBefore(n).width(i).height(o),s=a.find(".k-editable-area");n.attr("autocomplete","off").appendTo(s).addClass("k-content k-raw-content").css("display","none"),t.textarea=n,t.wrapper=a},_createContentElement:function(t){var n,i,o,r=this,s=r.textarea,l=r.options.domain,c=l||document.domain,u="",f='javascript:""';return s.hide(),n=e("<iframe />",{title:r.options.messages.editAreaTitle,frameBorder:"0"})[0],e(n).css("display","").addClass("k-content").attr("tabindex",s[0].tabIndex).insertBefore(s),(l||c!=location.hostname)&&(u='<script>document.domain="'+c+'"</script>',f="javascript:document.write('"+u+"')",n.src=f),i=n.contentWindow||n,o=i.document||n.contentDocument,e(n).one("load",function(){r.toolbar.decorateFrom(o.body)}),o.open(),o.write("<!DOCTYPE html><html><head><meta charset='utf-8' /><style>html{padding:0;margin:0;height:100%;min-height:100%;cursor:text;}body{box-sizing:border-box;font-size:12px;font-family:Verdana,Geneva,sans-serif;margin-top:-1px;padding:5px .4em 0;word-wrap: break-word;-webkit-nbsp-mode: space;-webkit-line-break: after-white-space;"+(a.support.isRtl(s)?"direction:rtl;":"")+(d.ios?"word-break:keep-all;":"")+"}h1{font-size:2em;margin:.67em 0}h2{font-size:1.5em}h3{font-size:1.16em}h4{font-size:1em}h5{font-size:.83em}h6{font-size:.7em}p{margin:0 0 1em;}.k-marker{display:none;}.k-paste-container,.Apple-style-span{position:absolute;left:-10000px;width:1px;height:1px;overflow:hidden}ul,ol{padding-left:2.5em}span{-ms-high-contrast-adjust:none;}a{color:#00a}code{font-size:1.23em}telerik\\3Ascript{display: none;}.k-table{width:100%;border-spacing:0;margin: 0 0 1em;}.k-table td{min-width:1px;padding:.2em .3em;}.k-table,.k-table td{outline:0;border: 1px dotted #ccc;}.k-table p{margin:0;padding:0;}.k-column-resize-handle-wrapper {position: absolute; height: 10px; width:10px; cursor: col-resize; z-index: 2;}.k-column-resize-handle {width: 100%; height: 100%;}.k-column-resize-handle > .k-column-resize-marker {width:2px; height:100%; margin:0 auto; background-color:#00b0ff; display:none; opacity:0.8;}.k-row-resize-handle-wrapper {position: absolute; cursor: row-resize; z-index:2; width: 10px; height: 10px;}.k-row-resize-handle {display: table; width: 100%; height: 100%;}.k-row-resize-marker-wrapper{display: table-cell; height:100%; width:100%; margin:0; padding:0; vertical-align: middle;}.k-row-resize-marker{margin: 0; padding:0; width:100%; height:2px; background-color: #00b0ff; opacity:0.8; display:none;}.k-table-resize-handle-wrapper {position: absolute; background-color: #fff; border: 1px solid #000; z-index: 100; width: 5px; height: 5px;}.k-table-resize-handle {width: 100%; height: 100%;}.k-table-resize-handle.k-resize-east{cursor:e-resize;}.k-table-resize-handle.k-resize-north{cursor:n-resize;}.k-table-resize-handle.k-resize-northeast{cursor:ne-resize;}.k-table-resize-handle.k-resize-northwest{cursor:nw-resize;}.k-table-resize-handle.k-resize-south{cursor:s-resize;}.k-table-resize-handle.k-resize-southeast{cursor:se-resize;}.k-table-resize-handle.k-resize-southwest{cursor:sw-resize;}.k-table-resize-handle.k-resize-west{cursor:w-resize;}.k-table.k-table-resizing{opacity:0.6;}.k-placeholder{color:grey}k\\:script{display:none;}</style>"+u+e.map(t,function(e){return"<link rel='stylesheet' href='"+e+"'>"}).join("")+"</head><body autocorrect='off' contenteditable='true'></body></html>"),o.close(),i},_blur:function(){var e=this.textarea,t=e?e.val():this._oldValue,n=this.options.encoded?this.encodedValue():this.value();this.update(),e&&e.trigger("blur"),n!=t&&(this.trigger("change"),e&&e.trigger("change"))},_spellCorrect:function(e){var n,i=!1;this._registerHandler(e.body,{contextmenu:function(){e.one("select",function(){n=null}),e._spellCorrectTimeout=setTimeout(function(){n=new a.ui.editor.RestorePoint(e.getRange(),e.body),i=!1},10)},input:function(){if(n)return a.support.browser.mozilla&&!i?(i=!0,t):(a.ui.editor._finishUpdate(e,n),t)}})},_registerHandler:function(t,n,i){var o,r,s,l=this,d=".kendoEditor";if(t=e(t),this._handlers||(this._handlers=[]),t.length)if(e.isPlainObject(n))for(s in n)n.hasOwnProperty(s)&&this._registerHandler(t,s,n[s]);else for(o=a.applyEventMap(n).split(" "),r=0;r<o.length;r++)l._handlers.push({element:t,type:o[r]+d,handler:i}),t.on(o[r]+d,i)},_deregisterHandlers:function(){var e,t,n=this._handlers;for(e=0;e<n.length;e++)t=n[e],t.element.off(t.type,t.handler);this._handlers=[]},_initializeContentElement:function(){var n,i,o,r,s,l=this;l.textarea?(l.window=l._createContentElement(l.options.stylesheets),n=l.document=l.window.contentDocument||l.window.document,n.body||(r=n.createElement("body"),r.setAttribute("contenteditable","true"),r.setAttribute("autocorrect","off"),n.getElementsByTagName("html")[0].appendChild(r),s=setInterval(function(){e(l.document).find("body").length>1&&(e(l.document).find("body:last").remove(),window.clearInterval(s))},10)),l.body=n.body,i=l.window,o=n,this._registerHandler(n,"mouseup",f(this._mouseup,this))):(l.window=window,n=l.document=document,l.body=l.element[0],i=l.body,o=l.body,l.toolbar.decorateFrom(l.body)),this._registerHandler(i,"blur",f(this._blur,this)),l._registerHandler(o,"down",f(l._mousedown,l));try{n.execCommand("enableInlineTableEditing",null,!1)}catch(d){}a.support.touch&&this._registerHandler(n,{keydown:function(){a._activeElement()!=n.body&&l.window.focus()}}),this._initializePlaceholder(),this._spellCorrect(l),this._registerHandler(l.body,{keydown:function(e){var n,i,o,r,a,s,d,c,u,p;return(e.keyCode!==m.BACKSPACE&&e.keyCode!==m.DELETE||"true"===l.body.getAttribute("contenteditable"))&&(e.keyCode===m.F10?(setTimeout(f(l.toolbar.focus,l.toolbar),100),l.toolbar.preventPopupHide=!0,e.preventDefault(),t):(e.keyCode!=m.LEFT&&e.keyCode!=m.RIGHT||(n=l.getRange(),i=e.keyCode==m.LEFT,o=n[i?"startContainer":"endContainer"],r=n[i?"startOffset":"endOffset"],a=i?-1:1,s=r+a,d=i?s:r,3==o.nodeType&&"\ufeff"==o.nodeValue[d]&&(n.setStart(o,s),n.collapse(!0),l.selectRange(n))),c=l.toolbar.tools,u=l.keyboard.toolFromShortcut(c,e),p=u?c[u].options:{},u&&!p.keyPressCommand?(e.preventDefault(),/^(undo|redo)$/.test(u)||l.keyboard.endTyping(!0),l.trigger("keydown",e),l.exec(u),l._runPostContentKeyCommands(e),!1):(l.keyboard.clearTimeout(),l.keyboard.keydown(e),t)))},keypress:function(e){setTimeout(function(){l._runPostContentKeyCommands(e),l._showTableResizeHandles()},0)},keyup:function(t){var n=[m.BACKSPACE,m.TAB,m.PAGEUP,m.PAGEDOWN,m.END,m.HOME,m.LEFT,m.UP,m.RIGHT,m.DOWN,m.INSERT,m.DELETE];(e.inArray(t.keyCode,n)>-1||65==t.keyCode&&t.ctrlKey&&!t.altKey&&!t.shiftKey)&&(window.clearTimeout(this._refreshInterval),this._refreshInterval=window.setTimeout(function(){l._selectionChange()},k)),l.keyboard.keyup(t)},click:function(e){var t,n=a.ui.editor.Dom;"img"===n.name(e.target)&&(t=l.createRange(),t.selectNode(e.target),l.selectRange(t))},"cut copy paste drop dragover":function(e){l.clipboard["on"+e.type](e)},focusin:function(){l.body.hasAttribute("contenteditable")&&(e(this).addClass("k-state-active"),l.toolbar.show(),l._togglePlaceholder(!1))},focusout:function(){setTimeout(function(){var t,n=a._activeElement(),i=l.body,o=l.toolbar;o.options.popup&&(t=o.window.element.get(0),t&&!e.contains(t,n)&&t!=n&&(o.preventPopupHide=!1)),n==i||e.contains(i,n)||e(n).is(".k-editortoolbar-dragHandle")||o.focused()||(e(i).removeClass("k-state-active"),o.hide()),l._togglePlaceholder(!l.value().trim())},10)}}),l._initializeColumnResizing(),l._initializeRowResizing(),l._initializeTableResizing()},_initializePlaceholder:function(){var t,n,i=this,o=i.options.placeholder;i.textarea&&o&&(t="<style id='"+v+"'>."+b+":before { content: '"+o+"'; }</style>",n=e(i.document.head),n.find("#"+v).remove(),n.append(t),i._togglePlaceholder(!i.value().trim()))},_initializeImmutables:function(){var e=this,t=a.ui.editor;e.options.immutables&&(e.immutables=new t.Immutables(e))},_mousedown:function(t){var n,i=this;i._selectionStarted=!0,e(i.body).parents(".k-window").length&&t.stopPropagation(),c.gecko||(n=e(t.target).closest("a[href]"),(2==t.which||1==t.which&&t.ctrlKey)&&n&&n.is("a[href]")&&window.open(n.attr("href"),"_new"),c.msie&&"html"===t.target.tagName.toLowerCase()&&setTimeout(function(){i.body.focus()},0))},_mouseup:function(t){var n=this;a.support.mobileOS.ios&&t&&e(t.target).is(g)||n._selectionStarted&&setTimeout(function(){n._selectionChange()},1)},_runPostContentKeyCommands:function(e){var t,n,i,o,r=this.getRange(),a=this.keyboard.toolsFromShortcut(this.toolbar.tools,e);for(t=0;t<a.length;t++)n=a[t],i=n.options,i.keyPressCommand&&(o=new i.command({range:r}),o.changesContent()&&(this.keyboard.endTyping(!0),this.exec(n.name)))},refresh:function(){var e,t=this;t.textarea&&(t._destroyResizings(),e=t.value(),t.textarea.val(e),t.wrapper.find("iframe").remove(),t._initializeContentElement(t),t.value(e))},events:["select","change","execute","error","paste","keydown","keyup"],options:{name:"Editor",messages:x,placeholder:"",formats:{},encoded:!0,domain:null,resizable:!1,deserialization:{custom:null},serialization:{entities:!0,semantic:!0,scripts:!1},pasteCleanup:{all:!1,css:!1,custom:null,keepNewLines:!1,msAllFormatting:!1,msConvertLists:!0,msTags:!0,none:!1,span:!1},stylesheets:[],dialogOptions:{modal:!0,resizable:!1,draggable:!0,animation:!1},imageBrowser:null,fileBrowser:null,fontName:[{text:"Arial",value:"Arial, Helvetica, sans-serif"},{text:"Courier New",value:'"Courier New", Courier, monospace'},{text:"Georgia",value:"Georgia, serif"},{text:"Impact",value:"Impact, Charcoal, sans-serif"},{text:"Lucida Console",value:'"Lucida Console", Monaco, monospace'},{text:"Tahoma",value:"Tahoma, Geneva, sans-serif"},{text:"Times New Roman",value:'"Times New Roman", Times, serif'},{text:"Trebuchet MS",value:'"Trebuchet MS", Helvetica, sans-serif'},{text:"Verdana",value:"Verdana, Geneva, sans-serif"}],fontSize:[{text:"1 (8pt)",value:"xx-small"},{text:"2 (10pt)",value:"x-small"},{text:"3 (12pt)",value:"small"},{text:"4 (14pt)",value:"medium"},{text:"5 (18pt)",value:"large"},{text:"6 (24pt)",value:"x-large"},{text:"7 (36pt)",value:"xx-large"}],formatBlock:[{text:"Paragraph",value:"p"},{text:"Quotation",value:"blockquote"},{text:"Heading 1",value:"h1"},{text:"Heading 2",value:"h2"},{text:"Heading 3",value:"h3"},{text:"Heading 4",value:"h4"},{text:"Heading 5",value:"h5"},{text:"Heading 6",value:"h6"}],tools:[].concat.call(["formatting"],T.basic,T.alignment,T.lists,T.indenting,T.links,["insertImage"],T.tables)},destroy:function(){var e=this;l.fn.destroy.call(this),this._endTyping(!0),this._deregisterHandlers(),clearTimeout(this._spellCorrectTimeout),this._focusOutside(),this.toolbar.destroy(),e._destroyUploadWidget(),e._destroyResizings(),a.destroy(this.wrapper)},_destroyResizings:function(){var e=this;e._destroyTableResizing(),a.ui.editor.TableResizing.dispose(e),e._destroyRowResizing(),a.ui.editor.RowResizing.dispose(e),e._destroyColumnResizing(),a.ui.editor.ColumnResizing.dispose(e)},_focusOutside:function(){if(a.support.browser.msie&&this.textarea){var t=e("<input style='position:fixed;left:1px;top:1px;width:1px;height:1px;font-size:0;border:0;opacity:0' />").appendTo(document.body).focus();t.blur().remove()}},_destroyUploadWidget:function(){var e=this;e._uploadWidget&&(e._uploadWidget.destroy(),e._uploadWidget=null)},state:function(e){var t,n,i=_.defaultTools[e],o=i&&(i.options.finder||i.finder),r=a.ui.editor.RangeUtils;return!!o&&(t=this.getRange(),n=r.textNodes(t),!n.length&&t.collapsed&&(n=[t.startContainer]),o.getFormat?o.getFormat(n):o.isFormatted(n))},value:function(e){var n=this.body,i=a.ui.editor,o=this.options,r=i.Serializer.domToXhtml(n,o.serialization);return e===t?r:(e!=r&&(i.Serializer.htmlToDom(e,n,o.deserialization),this.selectionRestorePoint=null,this.update(),this.toolbar.refreshTools()),t)},saveSelection:function(t){t=t||this.getRange();var n=t.commonAncestorContainer,i=this.body;(n==i||e.contains(i,n))&&(this.selectionRestorePoint=new a.ui.editor.RestorePoint(t,i))},_focusBody:function(){var e,t=this.body,n=this.wrapper&&this.wrapper.find("iframe")[0],i=this.document.documentElement,o=a._activeElement();!n&&t.scrollHeight>t.clientHeight?(e=t.scrollTop,t.focus(),t.scrollTop=e):o!=t&&o!=n&&(e=i.scrollTop,t.focus(),i.scrollTop=e)},restoreSelection:function(){this._focusBody(),this.selectionRestorePoint&&this.selectRange(this.selectionRestorePoint.toRange())},focus:function(){this.restoreSelection()},update:function(e){e=e||this.options.encoded?this.encodedValue():this.value(),this.textarea?(this.textarea.val(e),this._togglePlaceholder(!e.trim())):this._oldValue=e},encodedValue:function(){return a.ui.editor.Dom.encode(this.value())},createRange:function(e){return a.ui.editor.RangeUtils.createRange(e||this.document)},getSelection:function(){return a.ui.editor.SelectionUtils.selectionFromDocument(this.document)},selectRange:function(e){this._focusBody();var t=this.getSelection();t.removeAllRanges(),t.addRange(e),this.saveSelection(e)},getRange:function(){var e=this.getSelection(),t=e&&e.rangeCount>0?e.getRangeAt(0):this.createRange(),n=this.document;return t.startContainer!=n||t.endContainer!=n||t.startOffset||t.endOffset||(t.setStart(this.body,0),t.collapse(!0)),t},_containsRange:function(e){var t=a.ui.editor.Dom,n=this.body;return e&&t.isAncestorOrSelf(n,e.startContainer)&&t.isAncestorOrSelf(n,e.endContainer)},_deleteSavedRange:function(){"_range"in this&&delete this._range},selectedHtml:function(){return a.ui.editor.Serializer.domToXhtml(this.getRange().cloneContents())},paste:function(t,n){this.focus();var i=new a.ui.editor.InsertHtmlCommand(e.extend({range:this.getRange(),html:t},n));i.editor=this,i.exec()},exec:function(e,n){var i,o,r,a,s=this,l=null;if(!e)throw Error("kendoEditor.exec(): `name` parameter cannot be empty");if("true"!==s.body.getAttribute("contenteditable")&&"print"!==e&&"pdf"!==e&&"exportAs"!==e)return!1;if(e=e.toLowerCase(),s.keyboard.isTypingInProgress()||(s._focusBody(),s.selectRange(s._range||s.getRange())),o=s.toolbar.toolById(e),!o)for(a in _.defaultTools)if(a.toLowerCase()==e){o=_.defaultTools[a];break}if(o){if(i=s.getRange(),o.command&&(l=o.command(u({range:i,body:s.body,immutables:!!s.immutables},n))),r=s.trigger("execute",{name:e,command:l}))return;if(/^(undo|redo)$/i.test(e))s.undoRedoStack[e]();else if(l&&(s.execCommand(l),l.async))return l.change=f(s._selectionChange,s),t;s._selectionChange()}},execCommand:function(e){e.managesUndoRedo||this.undoRedoStack.push(e),e.editor=this,e.exec()}});_.defaultTools={undo:{options:{key:"Z",ctrl:!0}},redo:{options:{key:"Y",ctrl:!0}}},a.ui.plugin(_),n=s.extend({init:function(e){this.options=e},initialize:function(e,t){e.attr({unselectable:"on",title:t.title}),e.children(".k-tool-text").html(t.title)},command:function(e){return new this.options.command(e)},update:e.noop}),n.exec=function(e,t,n){e.exec(t,{value:n})},w.registerTool("separator",new n({template:new y({template:w.separatorTemplate})})),i=c.msie&&c.version<9?"\ufeff":"",o="\ufeff",r=o,(c.msie||c.edge)&&(r=o="&nbsp;"),u(a.ui,{editor:{ToolTemplate:y,EditorUtils:w,Tool:n,_bomFill:i,emptyElementContent:o,emptyTableCellContent:r}}),a.PDFMixin&&(a.PDFMixin.extend(_.prototype),_.prototype._drawPDF=function(){return a.drawing.drawDOM(this.body,this.options.pdf)},_.prototype.saveAsPDF=function(){var t,n=new e.Deferred,i=n.promise(),o={promise:i};if(!this.trigger("pdfExport",o))return t=this.options.pdf,this._drawPDF(n).then(function(e){return a.drawing.exportPDF(e,t)}).done(function(e){a.saveAs({dataURI:e,fileName:t.fileName,proxyURL:t.proxyURL,proxyTarget:t.proxyTarget,forceProxy:t.forceProxy}),n.resolve()}).fail(function(e){n.reject(e)}),i})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/dom.min",["editor/main.min"],e)}(function(){!function(e){function t(e){var t,n,i={};for(t=0,n=e.length;t<n;t++)i[e[t]]=!0;return i}var n,i,o,r,a,s,l,d,c,u,f,p=window.kendo,m=e.map,h=e.extend,g=p.support.browser,b="style",v="float",k="cssFloat",y="styleFloat",w="class",x="k-marker",C=t("area,base,basefont,br,col,frame,hr,img,input,isindex,link,meta,param,embed".split(",")),T="p,div,h1,h2,h3,h4,h5,h6,address,applet,blockquote,button,center,dd,dir,dl,dt,fieldset,form,frameset,hr,iframe,isindex,map,menu,noframes,noscript,object,pre,script,table,tbody,td,tfoot,th,thead,tr,header,article,nav,footer,section,aside,main,figure,figcaption".split(","),_=T.concat(["ul","ol","li"]),N=t(_),R=t("area,base,br,col,command,embed,hr,img,input,keygen,link,menuitem,meta,param,source,track,wbr".split(",")),S="span,em,a,abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,strike,strong,sub,sup,textarea,tt,u,var,data,time,mark,ruby".split(","),z=t(S),A=t("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected".split(",")),E=function(e){1==e.nodeType&&e.normalize()};g.msie&&g.version>=8&&(E=function(e){if(1==e.nodeType&&e.firstChild)for(var t=e.firstChild,n=t;;){if(n=n.nextSibling,!n)break;3==n.nodeType&&3==t.nodeType&&(n.nodeValue=t.nodeValue+n.nodeValue,f.remove(t)),t=n}}),n=/^\s+$/,i=/^[\n\r\t]+$/,o=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/i,r=/\ufeff/g,a=/^(\s+|\ufeff)$/,l="color,padding-left,padding-right,padding-top,padding-bottom,background-color,background-attachment,background-image,background-position,background-repeat,border-top-style,border-top-width,border-top-color,border-bottom-style,border-bottom-width,border-bottom-color,border-left-style,border-left-width,border-left-color,border-right-style,border-right-width,border-right-color,font-family,font-size,font-style,font-variant,font-weight,line-height".split(","),d=/[<>\&]/g,c=/[\u00A0-\u2666<>\&]/g,u={34:"quot",38:"amp",39:"apos",60:"lt",62:"gt",160:"nbsp",161:"iexcl",162:"cent",163:"pound",164:"curren",165:"yen",166:"brvbar",167:"sect",168:"uml",169:"copy",170:"ordf",171:"laquo",172:"not",173:"shy",174:"reg",175:"macr",176:"deg",177:"plusmn",178:"sup2",179:"sup3",180:"acute",181:"micro",182:"para",183:"middot",184:"cedil",185:"sup1",186:"ordm",187:"raquo",188:"frac14",189:"frac12",190:"frac34",191:"iquest",192:"Agrave",193:"Aacute",194:"Acirc",195:"Atilde",196:"Auml",197:"Aring",198:"AElig",199:"Ccedil",200:"Egrave",201:"Eacute",202:"Ecirc",203:"Euml",204:"Igrave",205:"Iacute",206:"Icirc",207:"Iuml",208:"ETH",209:"Ntilde",210:"Ograve",211:"Oacute",212:"Ocirc",213:"Otilde",214:"Ouml",215:"times",216:"Oslash",217:"Ugrave",218:"Uacute",219:"Ucirc",220:"Uuml",221:"Yacute",222:"THORN",223:"szlig",224:"agrave",225:"aacute",226:"acirc",227:"atilde",228:"auml",229:"aring",230:"aelig",231:"ccedil",232:"egrave",233:"eacute",234:"ecirc",235:"euml",236:"igrave",237:"iacute",238:"icirc",239:"iuml",240:"eth",241:"ntilde",242:"ograve",243:"oacute",244:"ocirc",245:"otilde",246:"ouml",247:"divide",248:"oslash",249:"ugrave",250:"uacute",251:"ucirc",252:"uuml",253:"yacute",254:"thorn",255:"yuml",402:"fnof",913:"Alpha",914:"Beta",915:"Gamma",916:"Delta",917:"Epsilon",918:"Zeta",919:"Eta",920:"Theta",921:"Iota",922:"Kappa",923:"Lambda",924:"Mu",925:"Nu",926:"Xi",927:"Omicron",928:"Pi",929:"Rho",931:"Sigma",932:"Tau",933:"Upsilon",934:"Phi",935:"Chi",936:"Psi",937:"Omega",945:"alpha",946:"beta",947:"gamma",948:"delta",949:"epsilon",950:"zeta",951:"eta",952:"theta",953:"iota",954:"kappa",955:"lambda",956:"mu",957:"nu",958:"xi",959:"omicron",960:"pi",961:"rho",962:"sigmaf",963:"sigma",964:"tau",965:"upsilon",966:"phi",967:"chi",968:"psi",969:"omega",977:"thetasym",978:"upsih",982:"piv",8226:"bull",8230:"hellip",8242:"prime",8243:"Prime",8254:"oline",8260:"frasl",8472:"weierp",8465:"image",8476:"real",8482:"trade",8501:"alefsym",8592:"larr",8593:"uarr",8594:"rarr",8595:"darr",8596:"harr",8629:"crarr",8656:"lArr",8657:"uArr",8658:"rArr",8659:"dArr",8660:"hArr",8704:"forall",8706:"part",8707:"exist",8709:"empty",8711:"nabla",8712:"isin",8713:"notin",8715:"ni",8719:"prod",8721:"sum",8722:"minus",8727:"lowast",8730:"radic",8733:"prop",8734:"infin",8736:"ang",8743:"and",8744:"or",8745:"cap",8746:"cup",8747:"int",8756:"there4",8764:"sim",8773:"cong",8776:"asymp",8800:"ne",8801:"equiv",8804:"le",8805:"ge",8834:"sub",8835:"sup",8836:"nsub",8838:"sube",8839:"supe",8853:"oplus",8855:"otimes",8869:"perp",8901:"sdot",8968:"lceil",8969:"rceil",8970:"lfloor",8971:"rfloor",9001:"lang",9002:"rang",9674:"loz",9824:"spades",9827:"clubs",9829:"hearts",9830:"diams",338:"OElig",339:"oelig",352:"Scaron",353:"scaron",376:"Yuml",710:"circ",732:"tilde",8194:"ensp",8195:"emsp",8201:"thinsp",8204:"zwnj",8205:"zwj",8206:"lrm",8207:"rlm",8211:"ndash",8212:"mdash",8216:"lsquo",8217:"rsquo",8218:"sbquo",8220:"ldquo",8221:"rdquo",8222:"bdquo",8224:"dagger",8225:"Dagger",8240:"permil",8249:"lsaquo",8250:"rsaquo",8364:"euro"},f={block:N,inline:z,findNodeIndex:function(e,t){
var n=0;if(!e)return-1;for(;;){if(e=e.previousSibling,!e)break;t&&3==e.nodeType||n++}return n},isDataNode:function(e){return e&&null!==e.nodeValue&&null!==e.data},isAncestorOf:function(t,n){try{return!f.isDataNode(t)&&(e.contains(t,f.isDataNode(n)?n.parentNode:n)||n.parentNode==t)}catch(i){return!1}},isAncestorOrSelf:function(e,t){return f.isAncestorOf(e,t)||e==t},findClosestAncestor:function(e,t){if(f.isAncestorOf(e,t))for(;t&&t.parentNode!=e;)t=t.parentNode;return t},getAllComments:function(e){for(var t=[],n=document.createNodeIterator(e,NodeFilter.SHOW_COMMENT,function(){return NodeFilter.FILTER_ACCEPT},!1),i=n.nextNode();i;)t.push(i.nodeValue),i=n.nextNode();return t},getNodeLength:function(e){return f.isDataNode(e)?e.length:e.childNodes.length},splitDataNode:function(e,t){for(var n,i=e.cloneNode(!1),o="",r=e.nextSibling;r&&3==r.nodeType&&r.nodeValue;)o+=r.nodeValue,n=r,r=r.nextSibling,f.remove(n);e.deleteData(t,e.length),i.deleteData(0,t),i.nodeValue+=o,f.insertAfter(i,e)},attrEquals:function(e,t){var n,i;for(n in t)if(i=e[n],n==v&&(i=e[p.support.cssFloat?k:y]),"object"==typeof i){if(!f.attrEquals(i,t[n]))return!1}else if(i!=t[n])return!1;return!0},blockParentOrBody:function(e){return f.parentOfType(e,_)||e.ownerDocument.body},blockParents:function(t){var n,i,o,r=[];for(n=0,i=t.length;n<i;n++)o=f.parentOfType(t[n],f.blockElements),o&&e.inArray(o,r)<0&&r.push(o);return r},windowFromDocument:function(e){return e.defaultView||e.parentWindow},normalize:E,blockElements:_,nonListBlockElements:T,inlineElements:S,empty:C,fillAttrs:A,nodeTypes:{ELEMENT_NODE:1,ATTRIBUTE_NODE:2,TEXT_NODE:3,CDATA_SECTION_NODE:4,ENTITY_REFERENCE_NODE:5,ENTITY_NODE:6,PROCESSING_INSTRUCTION_NODE:7,COMMENT_NODE:8,DOCUMENT_NODE:9,DOCUMENT_TYPE_NODE:10,DOCUMENT_FRAGMENT_NODE:11,NOTATION_NODE:12},toHex:function(e){var t=o.exec(e);return t?"#"+m(t.slice(1),function(e){return e=parseInt(e,10).toString(16),e.length>1?e:"0"+e}).join(""):e},encode:function(e,t){var n=!t||t.entities?c:d;return e.replace(n,function(e){var t=e.charCodeAt(0),n=u[t];return n?"&"+n+";":e})},isBom:function(e){return e&&3===e.nodeType&&/^[\ufeff]+$/.test(e.nodeValue)},stripBom:function(e){return(e||"").replace(r,"")},stripBomNode:function(e){f.isBom(e)&&e.parentNode.removeChild(e)},insignificant:function(e){var t=e.attributes;return"k-marker"==e.className||f.is(e,"br")&&("k-br"==e.className||t._moz_dirty||t._moz_editor_bogus_node)},tableCell:function(e){return f.is(e,"td")||f.is(e,"th")},significantNodes:function(t){return e.grep(t,function(e){var t=f.name(e);return"br"!=t&&(!f.insignificant(e)&&(!f.emptyTextNode(e)&&!(1==e.nodeType&&!C[t]&&f.emptyNode(e))))})},emptyTextNode:function(e){return e&&3==e.nodeType&&a.test(e.nodeValue)},emptyNode:function(e){return 1==e.nodeType&&!f.significantNodes(e.childNodes).length},name:function(e){return e.nodeName.toLowerCase()},significantChildNodes:function(t){return e.grep(t.childNodes,function(e){return 3!=e.nodeType||!f.isWhitespace(e)})},lastTextNode:function(e){var t,n=null;if(3==e.nodeType)return e;for(t=e.lastChild;t;t=t.previousSibling)if(n=f.lastTextNode(t))return n;return n},is:function(e,t){return e&&f.name(e)==t},isMarker:function(e){return e.className==x},isWhitespace:function(e){return n.test(e.nodeValue)},allWhitespaceContent:function(e){for(var t=e.firstChild;t&&f.isWhitespace(t);)t=t.nextSibling;return!t},isEmptyspace:function(e){return i.test(e.nodeValue)},htmlIndentSpace:function(t){var n,o,a,s,l,d;return!(!f.isDataNode(t)||!f.isWhitespace(t))&&(!!i.test(t.nodeValue)||(n=function(e,t){for(;e[t];)if(e=e[t],f.significantNodes([e]).length>0)return e},o=t.parentNode,a=n(t,"previousSibling"),s=n(t,"nextSibling"),r.test(t.nodeValue)?!(!a&&!s):!!e(o).is("tr,tbody,thead,tfoot,table,ol,ul")||!!((f.isBlock(o)||f.is(o,"body"))&&(l=a&&f.isBlock(a),d=s&&f.isBlock(s),!s&&l||!a&&d||l&&d))))},isBlock:function(e){return N[f.name(e)]},isSelfClosing:function(e){return R[f.name(e)]},isEmpty:function(e){return C[f.name(e)]},isInline:function(e){return z[f.name(e)]},isBr:function(e){return"br"==f.name(e)},list:function(e){var t=e?f.name(e):"";return"ul"==t||"ol"==t||"dl"==t},scrollContainer:function(e){var t=f.windowFromDocument(e),n=(t.contentWindow||t).document||t.ownerDocument||t;return n="BackCompat"==n.compatMode?n.body:n.scrollingElement||n.documentElement},scrollTo:function(t,n){var i,o,r,a,s=t.ownerDocument,l=f.windowFromDocument(s),d=l.innerHeight,c=f.scrollContainer(s);f.isDataNode(t)?n?(a=f.create(s,"span",{innerHTML:"&#xfeff;"}),f.insertBefore(a,t),i=e(a)):i=e(t.parentNode):i=e(t),o=i.offset().top,r=i[0].offsetHeight,!n&&r||(r=parseInt(i.css("line-height"),10)||Math.ceil(1.2*parseInt(i.css("font-size"),10))||15),a&&f.remove(a),r+o>c.scrollTop+d&&(c.scrollTop=r+o-d)},persistScrollTop:function(e){s=f.scrollContainer(e).scrollTop},offset:function(e,t){for(var n={top:e.offsetTop,left:e.offsetLeft},i=e.offsetParent;i&&(!t||f.isAncestorOf(t,i));)n.top+=i.offsetTop,n.left+=i.offsetLeft,i=i.offsetParent;return n},restoreScrollTop:function(e){"number"==typeof s&&(f.scrollContainer(e).scrollTop=s,s=void 0)},insertAt:function(e,t,n){e.insertBefore(t,e.childNodes[n]||null)},insertBefore:function(e,t){return t.parentNode?t.parentNode.insertBefore(e,t):t},insertAfter:function(e,t){return t.parentNode.insertBefore(e,t.nextSibling)},remove:function(e){e.parentNode&&e.parentNode.removeChild(e)},removeChildren:function(e){for(;e.firstChild;)e.removeChild(e.firstChild)},removeTextSiblings:function(e){for(var t=e.parentNode;e.nextSibling&&3==e.nextSibling.nodeType;)t.removeChild(e.nextSibling);for(;e.previousSibling&&3==e.previousSibling.nodeType;)t.removeChild(e.previousSibling)},trim:function(e){var t,n;for(t=e.childNodes.length-1;t>=0;t--)n=e.childNodes[t],f.isDataNode(n)?f.stripBom(n.nodeValue).length||f.remove(n):n.className!=x&&(f.trim(n),(!f.isEmpty(n)&&0===n.childNodes.length||f.isBlock(n)&&f.allWhitespaceContent(n))&&f.remove(n));return e},closest:function(e,t){for(;e&&f.name(e)!=t;)e=e.parentNode;return e},closestBy:function(e,t,n){for(;e&&!t(e);){if(n&&n(e))return null;e=e.parentNode}return e},sibling:function(e,t){do e=e[t];while(e&&1!=e.nodeType);return e},next:function(e){return f.sibling(e,"nextSibling")},prev:function(e){return f.sibling(e,"previousSibling")},parentOfType:function(e,t){do e=e.parentNode;while(e&&!f.ofType(e,t));return e},ofType:function(t,n){return e.inArray(f.name(t),n)>=0},changeTag:function(e,t,n){var i,o,r,a,s,l=f.create(e.ownerDocument,t),d=e.attributes;if(!n)for(i=0,o=d.length;i<o;i++)s=d[i],s.specified&&(r=s.nodeName,a=s.nodeValue,r==w?l.className=a:r==b?l.style.cssText=e.style.cssText:l.setAttribute(r,a));for(;e.firstChild;)l.appendChild(e.firstChild);return f.insertBefore(l,e),f.remove(e),l},editableParent:function(e){for(;e&&(3==e.nodeType||"true"!==e.contentEditable);)e=e.parentNode;return e},wrap:function(e,t){return f.insertBefore(t,e),t.appendChild(e),t},unwrap:function(e){for(var t=e.parentNode;e.firstChild;)t.insertBefore(e.firstChild,e);t.removeChild(e)},wrapper:function(t){var n=f.closestBy(t,function(e){return e.parentNode&&f.significantNodes(e.parentNode.childNodes).length>1});return e(n).is("body,.k-editor")?void 0:n},create:function(e,t,n){return f.attr(e.createElement(t),n)},createEmptyNode:function(e,t,n){var i=f.attr(e.createElement(t),n);return i.innerHTML="\ufeff",i},attr:function(e,t){t=h({},t),t&&b in t&&(f.style(e,t.style),delete t.style);for(var n in t)null===t[n]?(e.removeAttribute(n),delete t[n]):"className"==n&&(e[n]=t[n]);return h(e,t)},mergeAttributes:function(t,n){t.attributes.length&&e.each(t.attributes,function(){"contenteditable"!==this.name&&e(n).attr(this.name,this.value)})},style:function(t,n){e(t).css(n||{})},unstyle:function(e,t){for(var n in t)n==v&&(n=p.support.cssFloat?k:y),e.style[n]="";""===e.style.cssText&&e.removeAttribute(b)},inlineStyle:function(t,n,i){var o,r=e(f.create(t.ownerDocument,n,i));return t.appendChild(r[0]),o=m(l,function(e){return g.msie&&"line-height"==e&&"1px"==r.css(e)?"line-height:1.5":e+":"+r.css(e)}).join(";"),r.remove(),o},getEffectiveBackground:function(e){var t=e.css("background-color")||"";return t.indexOf("rgba(0, 0, 0, 0")<0&&"transparent"!==t?t:"html"===e[0].tagName.toLowerCase()?"Window":f.getEffectiveBackground(e.parent())},innerText:function(e){var t=e.innerHTML;return t=t.replace(/<!--(.|\s)*?-->/gi,""),t=t.replace(/<\/?[^>]+?\/?>/gm,"")},removeClass:function(t,n){var i,o,r=" "+t.className+" ",a=n.split(" ");for(i=0,o=a.length;i<o;i++)r=r.replace(" "+a[i]+" "," ");r=e.trim(r),r.length?t.className=r:t.removeAttribute(w)},commonAncestor:function(){var e,t,n,i,o,r=arguments.length,a=[],s=1/0,l=null;if(!r)return null;if(1==r)return arguments[0];for(e=0;e<r;e++){for(t=[],n=arguments[e];n;)t.push(n),n=n.parentNode;a.push(t.reverse()),s=Math.min(s,t.length)}if(1==r)return a[0][0];for(e=0;e<s;e++){for(i=a[0][e],o=1;o<r;o++)if(i!=a[o][e])return l;l=i}return l},closestSplittableParent:function(t){var n,i,o;return n=1==t.length?f.parentOfType(t[0],["ul","ol"]):f.commonAncestor.apply(null,t),n||(n=f.parentOfType(t[0],["p","td"])||t[0].ownerDocument.body),f.isInline(n)&&(n=f.blockParentOrBody(n)),i=m(t,f.editableParent),o=f.commonAncestor(i)[0],e.contains(n,o)&&(n=o),n},closestEditable:function(t,n){var i,o=f.editableParent(t);return i=f.ofType(t,n)?t:f.parentOfType(t,n),i&&o&&e.contains(i,o)?i=o:!i&&o&&(i=o),i},closestEditableOfType:function(t,n){var i=f.closestEditable(t,n);if(i&&f.ofType(i,n)&&!e(i).is(".k-editor"))return i},filter:function(e,t,n){var i=function(t){return f.name(t)==e};return f.filterBy(t,i,n)},filterBy:function(e,t,n){for(var i,o=0,r=e.length,a=[];o<r;o++)i=t(e[o]),(i&&!n||!i&&n)&&a.push(e[o]);return a},ensureTrailingBreaks:function(t){var n=e(t).find("p,td,th"),i=n.length,o=0;if(i)for(;o<i;o++)f.ensureTrailingBreak(n[o]);else f.ensureTrailingBreak(t)},removeTrailingBreak:function(t){e(t).find("br[type=_moz],.k-br").remove()},ensureTrailingBreak:function(e){var t,n,i;f.removeTrailingBreak(e),t=e.lastChild,n=t&&f.name(t),(!n||"br"!=n&&"img"!=n||"br"==n&&"k-br"!=t.className)&&(i=e.ownerDocument.createElement("br"),i.className="k-br",e.appendChild(i))}},p.ui.editor.Dom=f}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/serializer.min",["editor/dom.min"],e)}(function(){!function(e,t){var n,i,o,r,a=window.kendo,s=a.ui.editor,l=s.Dom,d=e.extend,c="xx-small,x-small,small,medium,large,x-large,xx-large".split(","),u=/"/g,f=/<br[^>]*>/i,p=/^\d+(\.\d*)?(px)?$/i,m=/<p>(?:&nbsp;)?<\/p>/i,h=/(\*?[-#\/\*\\\w]+(?:\[[0-9a-z_-]+\])?)\s*:\s*((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^\)]*?\)|[^};])+)/g,g=/^sizzle-\d+/i,b=/^k-script-/i,v=/\s*onerror\s*=\s*(?:'|")?([^'">\s]*)(?:'|")?/i,k='<br class="k-br">',y=document.createElement("div");y.innerHTML=" <hr>",n=3===y.firstChild.nodeType,y=null,i=e.isFunction,o="td",r={toEditableHtml:function(e){return(e||"").replace(/<!\[CDATA\[(.*)?\]\]>/g,"<!--[CDATA[$1]]-->").replace(/<(\/?)script([^>]*)>/gi,"<$1k:script$2>").replace(/<img([^>]*)>/gi,function(e){return e.replace(v,"")}).replace(/(<\/?img[^>]*>)[\r\n\v\f\t ]+/gi,"$1").replace(/^<(table|blockquote)/i,k+"<$1").replace(/^[\s]*(&nbsp;|\u00a0)/i,"$1").replace(/<\/(table|blockquote)>$/i,"</$1>"+k)},_toEditableImmutables:function(t){for(var n=s.Immutables.immutable,i=l.emptyTextNode,o=t.firstChild,r=t.lastChild;i(o);)o=o.nextSibling;for(;i(r);)r=r.previousSibling;o&&n(o)&&e(k).prependTo(t),r&&n(r)&&e(k).appendTo(t)},_fillEmptyElements:function(t){e(t).find("p,td").each(function(){var t,n=e(this);if(/^\s*$/g.test(n.text())&&!n.find("img,input").length){for(t=this;t.firstChild&&3!=t.firstChild.nodeType;)t=t.firstChild;1!=t.nodeType||l.empty[l.name(t)]||(t.innerHTML=l.is(t,"td")?a.ui.editor.emptyTableCellContent:a.ui.editor.emptyElementContent)}})},_removeSystemElements:function(t){e(".k-paste-container",t).remove()},_resetOrderedLists:function(e){var t,n,i,o=e.getElementsByTagName("ol");for(t=0;t<o.length;t++)n=o[t],i=n.getAttribute("start"),n.setAttribute("start",1),i?n.setAttribute("start",i):n.removeAttribute(i)},_preventScriptExecution:function(t){e(t).find("*").each(function(){var e,t,n,i,o=this.attributes,r=[];for(t=0,n=o.length;t<n;t++)e=o[t],i=e.nodeName,e.specified&&/^on/i.test(i)&&(this.setAttribute("k-script-"+i,e.value),r.push(i));for(t=0,n=r.length;t<n;t++)this.removeAttribute(r[t])})},htmlToDom:function(t,n,o){var s=a.support.browser,d=s.msie,c=d&&s.version<9,u="originalsrc",f="originalhref",p=o||{},m=p.immutables;return t=r.toEditableHtml(t),c&&(t="<br/>"+t,t=t.replace(/href\s*=\s*(?:'|")?([^'">\s]*)(?:'|")?/,f+'="$1"'),t=t.replace(/src\s*=\s*(?:'|")?([^'">\s]*)(?:'|")?/,u+'="$1"')),i(p.custom)&&(t=p.custom(t)||t),n.innerHTML=t,m&&m.deserialize(n),c?(l.remove(n.firstChild),e(n).find("k\\:script,script,link,img,a").each(function(){var e=this;e[f]&&(e.setAttribute("href",e[f]),e.removeAttribute(f)),e[u]&&(e.setAttribute("src",e[u]),e.removeAttribute(u))})):d&&(l.normalize(n),r._resetOrderedLists(n)),r._preventScriptExecution(n),r._fillEmptyElements(n),r._removeSystemElements(n),r._toEditableImmutables(n),e("table",n).addClass("k-table"),n},domToXhtml:function(i,r){function d(t){return e.grep(t,function(e){return"style"!=e.name})}function v(e,t){z.push("<"+t),x(e),z.push(">")}function k(t){var n,i,o,r=e.trim,a=r(t),s=[];for(h.lastIndex=0;;){if(n=h.exec(a),!n)break;i=r(n[1].toLowerCase()),o=r(n[2]),"font-size-adjust"!=i&&"font-stretch"!=i&&(i.indexOf("color")>=0?o=l.toHex(o):i.indexOf("font")>=0?o=o.replace(u,"'"):/\burl\(/g.test(o)&&(o=o.replace(u,"")),s.push({property:i,value:o}))}return s}function y(e){var t,n=k(e);for(t=0;t<n.length;t++)z.push(n[t].property),z.push(":"),z.push(n[t].value),z.push(";")}function w(e){var t,n,i,o,a,s,d=[],c=e.attributes;for(n=0,i=c.length;n<i;n++)t=c[n],o=t.nodeName,a=t.value,s=t.specified,"value"==o&&"value"in e&&e.value?s=!0:"type"==o&&"text"==a?s=!0:"class"!=o||a?g.test(o)?s=!1:"complete"==o?s=!1:"altHtml"==o?s=!1:"start"==o&&l.is(e,"ul")?s=!1:"start"==o&&l.is(e,"ol")&&"1"==a?s=!1:o.indexOf("_moz")>=0?s=!1:b.test(o)?s=!!r.scripts:"data-role"==o&&"resizable"==a&&(l.is(e,"tr")||l.is(e,"td"))&&(s=!1):s=!1,s&&d.push(t);return d}function x(n,i){var o,r,s,d,c,u,f,m;if(i=i||w(n),l.is(n,"img")&&(u=n.style.width,f=n.style.height,m=e(n),u&&p.test(u)&&(m.attr("width",parseInt(u,10)),l.unstyle(n,{width:t})),f&&p.test(f)&&(m.attr("height",parseInt(f,10)),l.unstyle(n,{height:t}))),i.length)for(o=0,r=i.length;o<r;o++)s=i[o],d=s.nodeName,c=s.value,"class"==d&&"k-table"==c||(d=d.replace(b,""),z.push(" "),z.push(d),z.push('="'),"style"==d?y(c||n.style.cssText):z.push("src"==d||"href"==d?a.htmlEncode(n.getAttribute(d,2)):l.fillAttrs[d]?d:c),z.push('"'))}function C(e,t,n){for(var i=e.firstChild;i;i=i.nextSibling)N(i,t,n)}function T(e){return e.nodeValue.replace(/\ufeff/g,"")}function _(e){if(l.isBom(e)){do{if(e=e.parentNode,l.is(e,o)&&1===e.childNodes.length)return!0;if(1!==e.childNodes.length)return!1}while(!l.isBlock(e));return!0}return!1}function N(i,o,a){var d,c,u,f,p,m,h=i.nodeType;if(A&&s.Immutables.immutable(i))z.push(A.serialize(i));else if(1==h){if(d=l.name(i),m=e(i),m.hasClass("k-table-resize-handle-wrapper")||m.hasClass("k-column-resize-handle-wrapper")||m.hasClass("k-row-resize-handle-wrapper"))return;if(!d||l.insignificant(i))return;if(!r.scripts&&("script"==d||"k:script"==d))return;if(c=E[d],c&&(t===c.semantic||r.semantic^c.semantic))return c.start(i),C(i,!1,c.skipEncoding),c.end(i),t;z.push("<"),z.push(d),x(i),l.empty[d]?z.push(" />"):(z.push(">"),C(i,o||l.is(i,"pre")),z.push("</"),z.push(d),z.push(">"))}else if(3==h){if(_(i))return z.push("&nbsp;"),t;f=T(i),!o&&n&&(u=i.parentNode,p=i.previousSibling,p||(p=(l.isInline(u)?u:i).previousSibling),p&&""!==p.innerHTML&&!l.isBlock(p)||(f=f.replace(/^[\r\n\v\f\t ]+/,"")),f=f.replace(/ +/," ")),z.push(a?f:l.encode(f,r))}else 4==h?(z.push("<![CDATA["),z.push(i.data),z.push("]]>")):8==h&&(i.data.indexOf("[CDATA[")<0?(z.push("<!--"),z.push(i.data),z.push("-->")):(z.push("<!"),z.push(i.data),z.push(">")))}function R(e){var t=e.childNodes.length,n=t&&3==e.firstChild.nodeType;return n&&(1==t||2==t&&l.insignificant(e.lastChild))}function S(){e.isFunction(r.custom)&&(z=r.custom(z)||z)}var z=[],A=r&&r.immutables,E={iframe:{start:function(e){v(e,"iframe")},end:function(){z.push("</iframe>")}},"k:script":{start:function(e){v(e,"script")},end:function(){z.push("</script>")},skipEncoding:!0},span:{semantic:!0,start:function(t){var n,i,o=t.style,r=w(t),a=d(r);a.length&&(z.push("<span"),x(t,a),z.push(">")),"underline"==o.textDecoration&&z.push("<u>"),n=[],o.color&&n.push('color="'+l.toHex(o.color)+'"'),o.fontFamily&&n.push('face="'+o.fontFamily+'"'),o.fontSize&&(i=e.inArray(o.fontSize,c),n.push('size="'+i+'"')),n.length&&z.push("<font "+n.join(" ")+">")},end:function(e){var t=e.style;(t.color||t.fontFamily||t.fontSize)&&z.push("</font>"),"underline"==t.textDecoration&&z.push("</u>"),d(w(e)).length&&z.push("</span>")}},strong:{semantic:!0,start:function(e){v(e,"b")},end:function(){z.push("</b>")}},em:{semantic:!0,start:function(e){v(e,"i")},end:function(){z.push("</i>")}},b:{semantic:!1,start:function(e){v(e,"strong")},end:function(){z.push("</strong>")}},i:{semantic:!1,start:function(e){v(e,"em")},end:function(){z.push("</em>")}},u:{semantic:!1,start:function(t){var n,i,o;z.push("<span"),n=w(t),i=e(n).filter(function(e,t){return"style"==t.name})[0],o={nodeName:"style",value:"text-decoration:underline;"},i&&(o.value=i.value,/text-decoration/i.test(o.value)||(o.value="text-decoration:underline;"+o.value),n.splice(e.inArray(i,n),1)),n.push(o),x(t,n),z.push(">")},end:function(){z.push("</span>")}},font:{semantic:!1,start:function(e){var t,n,i;z.push('<span style="'),t=e.getAttribute("color"),n=c[e.getAttribute("size")],i=e.getAttribute("face"),t&&(z.push("color:"),z.push(l.toHex(t)),z.push(";")),i&&(z.push("font-family:"),z.push(i),z.push(";")),n&&(z.push("font-size:"),z.push(n),z.push(";")),z.push('">')},end:function(){z.push("</span>")}}};return E.script=E["k:script"],r=r||{},t===r.semantic&&(r.semantic=!0),R(i)?(z=l.encode(T(i.firstChild).replace(/[\r\n\v\f\t ]+/," "),r),S(),z):(C(i),z=z.join(""),S(),""===z.replace(f,"").replace(m,"")?"":z)}},d(s,{Serializer:r})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/components.min",["editor/serializer.min"],e)}(function(){!function(e,t){var n=window.kendo,i=n.ui.DropDownList,o=n.ui.editor.Dom,r=i.extend({init:function(t,o){var r=this;i.fn.init.call(r,t,o),n.support.mobileOS.ios&&(this._initSelectOverlay(),this.bind("dataBound",e.proxy(this._initSelectOverlay,this))),r.text(r.options.title),r.element.attr("title",r.options.title),r.wrapper.attr("title",r.options.title),r.bind("open",function(){if(r.options.autoSize){var e,t=r.list;t.css({whiteSpace:"nowrap",width:"auto"}),e=t.width(),e>0?e+=20:e=r._listWidth,t.css("width",e+n.support.scrollbar()),r._listWidth=e}})},options:{name:"SelectBox",index:-1},_initSelectOverlay:function(){var t,i,o,r,a=this,s=a.value(),l=this.dataSource.view(),d="",c=n.htmlEncode;for(i=0;i<l.length;i++)t=l[i],d+="<option value='"+c(t.value)+"'",t.value==s&&(d+=" selected"),d+=">"+c(t.text)+"</option>";o=e("<select class='k-select-overlay'>"+d+"</select>"),r=e(this.element).closest(".k-widget"),r.next(".k-select-overlay").remove(),o.insertAfter(r),o.on("change",function(){a.value(this.value),a.trigger("change")})},value:function(e){var n=this,o=i.fn.value.call(n,e);return e===t?o:(i.fn.value.call(n)||n.text(n.options.title),t)},decorate:function(t){var n,i,r,a,s=this,l=s.dataSource,d=l.data();for(t&&s.list.css("background-color",o.getEffectiveBackground(e(t))),n=0;n<d.length;n++)i=d[n].tag||"span",r=d[n].className,a=o.inlineStyle(t,i,{className:r}),a=a.replace(/"/g,"'"),d[n].style=a+";display:inline-block";l.trigger("change")}});n.ui.plugin(r),n.ui.editor.SelectBox=r}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/range.min",["editor/components.min"],e)}(function(){!function(e){function t(e,t,n,i){var o,r,a,s;if(e==t)return i-n;for(o=t;o&&o.parentNode!=e;)o=o.parentNode;if(o)return x(o)-n;for(o=e;o&&o.parentNode!=t;)o=o.parentNode;if(o)return i-x(o)-1;for(r=w.commonAncestor(e,t),a=e;a&&a.parentNode!=r;)a=a.parentNode;for(a||(a=r),s=t;s&&s.parentNode!=r;)s=s.parentNode;return s||(s=r),a==s?0:x(s)-x(a)}function n(e,n){function i(e){try{return t(e.startContainer,e.endContainer,e.startOffset,e.endOffset)<0}catch(n){return!0}}i(e)&&(n?(e.commonAncestorContainer=e.endContainer=e.startContainer,e.endOffset=e.startOffset):(e.commonAncestorContainer=e.startContainer=e.endContainer,e.startOffset=e.endOffset),e.collapsed=!0)}function i(e){e.collapsed=e.startContainer==e.endContainer&&e.startOffset==e.endOffset;for(var t=e.startContainer;t&&t!=e.endContainer&&!w.isAncestorOf(t,e.endContainer);)t=t.parentNode;e.commonAncestorContainer=t}function o(e){var t=e.duplicate(),n=e.duplicate();return t.collapse(!0),n.collapse(!1),w.commonAncestor(e.parentElement(),t.parentElement(),n.parentElement())}function r(e,t,n){var i,o=t[n?"startContainer":"endContainer"],r=t[n?"startOffset":"endOffset"],a=0,s=C(o),l=s?o:o.childNodes[r]||null,d=s?o.parentNode:o,c=t.ownerDocument,u=c.body.createTextRange();3!=o.nodeType&&4!=o.nodeType||(a=r),d||(d=c.body),"img"==d.nodeName.toLowerCase()?(u.moveToElementText(d),u.collapse(!1),e.setEndPoint(n?"StartToStart":"EndToStart",u)):(i=d.insertBefore(w.create(c,"a"),l),u.moveToElementText(i),w.remove(i),u[n?"moveStart":"moveEnd"]("character",a),u.collapse(!1),e.setEndPoint(n?"StartToStart":"EndToStart",u))}function a(e,t,n,i){var o,r,a,s,l,d,c,u=w.create(t.ownerDocument,"a"),f=e.duplicate(),p=i?"StartToStart":"StartToEnd",m=!1;u.innerHTML="\ufeff",f.collapse(i),r=f.parentElement(),w.isAncestorOrSelf(n,r)||(r=n);do m?r.insertBefore(u,u.previousSibling):(r.appendChild(u),m=!0),f.moveToElementText(u);while((o=f.compareEndPoints(p,e))>0&&u.previousSibling);a=u.nextSibling,o==-1&&C(a)?(f.setEndPoint(i?"EndToStart":"EndToEnd",e),w.remove(u),d=[a,f.text.length]):(s=!i&&u.previousSibling,l=i&&u.nextSibling,C(l)?d=[l,0]:C(s)?d=[s,s.length]:(c=x(u),d=r.nextSibling&&c==r.childNodes.length-1?[r.nextSibling,0]:[r,c]),w.remove(u)),t[i?"setStart":"setEnd"].apply(t,d)}var s,l,d,c,u,f,p,m,h,g=window.kendo,b=g.Class,v=e.extend,k=g.ui.editor,y=g.support.browser,w=k.Dom,x=w.findNodeIndex,C=w.isDataNode,T=w.findClosestAncestor,_=w.getNodeLength,N=w.normalize,R={selectionFromWindow:function(e){return"getSelection"in e?e.getSelection():new l(e.document)},selectionFromRange:function(e){var t=h.documentFromRange(e);return R.selectionFromDocument(t)},selectionFromDocument:function(e){return R.selectionFromWindow(w.windowFromDocument(e))}},S=b.extend({init:function(t){e.extend(this,{ownerDocument:t,startContainer:t,endContainer:t,commonAncestorContainer:t,startOffset:0,endOffset:0,collapsed:!0})},setStart:function(e,t){this.startContainer=e,this.startOffset=t,i(this),n(this,!0)},setEnd:function(e,t){this.endContainer=e,this.endOffset=t,i(this),n(this,!1)},setStartBefore:function(e){this.setStart(e.parentNode,x(e))},setStartAfter:function(e){this.setStart(e.parentNode,x(e)+1)},setEndBefore:function(e){this.setEnd(e.parentNode,x(e))},setEndAfter:function(e){this.setEnd(e.parentNode,x(e)+1)},selectNode:function(e){this.setStartBefore(e),this.setEndAfter(e)},selectNodeContents:function(e){this.setStart(e,0),this.setEnd(e,e[1===e.nodeType?"childNodes":"nodeValue"].length)},collapse:function(e){var t=this;e?t.setEnd(t.startContainer,t.startOffset):t.setStart(t.endContainer,t.endOffset)},deleteContents:function(){var e=this,t=e.cloneRange();e.startContainer!=e.commonAncestorContainer&&e.setStartAfter(T(e.commonAncestorContainer,e.startContainer)),e.collapse(!0),function n(e){for(;e.next();)e.hasPartialSubtree()?n(e.getSubtreeIterator()):e.remove()}(new s(t))},cloneContents:function(){var e=h.documentFromRange(this);return function t(n){for(var i,o=e.createDocumentFragment();i=n.next();)i=i.cloneNode(!n.hasPartialSubtree()),n.hasPartialSubtree()&&i.appendChild(t(n.getSubtreeIterator())),o.appendChild(i);return o}(new s(this))},extractContents:function(){var e,t=this,n=t.cloneRange();return t.startContainer!=t.commonAncestorContainer&&t.setStartAfter(T(t.commonAncestorContainer,t.startContainer)),t.collapse(!0),e=h.documentFromRange(t),function i(n){for(var o,r=e.createDocumentFragment();o=n.next();)n.hasPartialSubtree()?(o=o.cloneNode(!1),o.appendChild(i(n.getSubtreeIterator()))):n.remove(t.originalRange),r.appendChild(o);return r}(new s(n))},insertNode:function(e){var t=this;C(t.startContainer)?(t.startOffset!=t.startContainer.nodeValue.length&&w.splitDataNode(t.startContainer,t.startOffset),w.insertAfter(e,t.startContainer)):w.insertAt(t.startContainer,e,t.startOffset),t.setStart(t.startContainer,t.startOffset)},cloneRange:function(){return e.extend(new S(this.ownerDocument),{startContainer:this.startContainer,endContainer:this.endContainer,commonAncestorContainer:this.commonAncestorContainer,startOffset:this.startOffset,endOffset:this.endOffset,collapsed:this.collapsed,originalRange:this})},toString:function(){var e=this.startContainer.nodeName,t=this.endContainer.nodeName;return("#text"==e?this.startContainer.nodeValue:e)+"("+this.startOffset+") : "+("#text"==t?this.endContainer.nodeValue:t)+"("+this.endOffset+")"}});S.fromNode=function(e){return new S(e.ownerDocument)},s=b.extend({init:function(t){if(e.extend(this,{range:t,_current:null,_next:null,_end:null}),!t.collapsed){var n=t.commonAncestorContainer;this._next=t.startContainer!=n||C(t.startContainer)?T(n,t.startContainer):t.startContainer.childNodes[t.startOffset],this._end=t.endContainer!=n||C(t.endContainer)?T(n,t.endContainer).nextSibling:t.endContainer.childNodes[t.endOffset]}},hasNext:function(){return!!this._next},next:function(){var e=this,t=e._current=e._next;return e._next=e._current&&e._current.nextSibling!=e._end?e._current.nextSibling:null,C(e._current)&&(e.range.endContainer==e._current&&(t=t.cloneNode(!0),t.deleteData(e.range.endOffset,t.length-e.range.endOffset)),e.range.startContainer==e._current&&(t=t.cloneNode(!0),t.deleteData(0,e.range.startOffset))),t},traverse:function(e){function t(){return i._current=i._next,i._next=i._current&&i._current.nextSibling!=i._end?i._current.nextSibling:null,i._current}for(var n,i=this;n=t();)i.hasPartialSubtree()?i.getSubtreeIterator().traverse(e):e(n);return n},remove:function(e){var t,n,i,o,r,a=this,s=a.range.startContainer==a._current,l=a.range.endContainer==a._current;C(a._current)&&(s||l)?(t=s?a.range.startOffset:0,n=l?a.range.endOffset:a._current.length,i=n-t,e&&(s||l)&&(a._current==e.startContainer&&t<=e.startOffset&&(e.startOffset-=i),a._current==e.endContainer&&n<=e.endOffset&&(e.endOffset-=i)),a._current.deleteData(t,i)):(o=a._current.parentNode,!e||a.range.startContainer!=o&&a.range.endContainer!=o||(r=x(a._current),o==e.startContainer&&r<=e.startOffset&&(e.startOffset-=1),o==e.endContainer&&r<e.endOffset&&(e.endOffset-=1)),w.remove(a._current))},hasPartialSubtree:function(){return!C(this._current)&&(w.isAncestorOrSelf(this._current,this.range.startContainer)||w.isAncestorOrSelf(this._current,this.range.endContainer))},getSubtreeIterator:function(){return new s(this.getSubRange())},getSubRange:function(){var e=this,t=e.range.cloneRange();return t.selectNodeContents(e._current),w.isAncestorOrSelf(e._current,e.range.startContainer)&&t.setStart(e.range.startContainer,e.range.startOffset),w.isAncestorOrSelf(e._current,e.range.endContainer)&&t.setEnd(e.range.endContainer,e.range.endOffset),t}}),l=b.extend({init:function(e){this.ownerDocument=e,this.rangeCount=1},addRange:function(e){var t=this.ownerDocument.body.createTextRange();r(t,e,!1),r(t,e,!0),t.select()},removeAllRanges:function(){var e=this.ownerDocument.selection;"None"!=e.type&&e.empty()},getRangeAt:function(){var e,t,n,i,r,s,l,d,c=new S(this.ownerDocument),u=this.ownerDocument.selection;try{if(e=u.createRange(),t=e.item?e.item(0):e.parentElement(),t.ownerDocument!=this.ownerDocument)return c}catch(f){return c}if("Control"==u.type)c.selectNode(e.item(0));else if(n=o(e),a(e,c,n,!0),a(e,c,n,!1),9==c.startContainer.nodeType&&c.setStart(c.endContainer,c.startOffset),9==c.endContainer.nodeType&&c.setEnd(c.startContainer,c.endOffset),0===e.compareEndPoints("StartToEnd",e)&&c.collapse(!1),i=c.startContainer,r=c.endContainer,s=this.ownerDocument.body,!(c.collapsed||0!==c.startOffset||c.endOffset!=_(c.endContainer)||i==r&&C(i)&&i.parentNode==s)){for(l=!1,d=!1;0===x(i)&&i==i.parentNode.firstChild&&i!=s;)i=i.parentNode,l=!0;for(;x(r)==_(r.parentNode)-1&&r==r.parentNode.lastChild&&r!=s;)r=r.parentNode,d=!0;i==s&&r==s&&l&&d&&(c.setStart(i,0),c.setEnd(r,_(s)))}return c}}),d=b.extend({init:function(e){this.enumerate=function(){function t(e){if(w.is(e,"img")||3==e.nodeType&&(!w.isEmptyspace(e)||"\ufeff"==e.nodeValue))n.push(e);else for(e=e.firstChild;e;)t(e),e=e.nextSibling}var n=[];return new s(e).traverse(t),n}}}),c=s.extend({hasPartialSubtree:function(){var e=k.Immutables&&k.Immutables.immutable;return e&&!e(this._current)&&s.fn.hasPartialSubtree.call(this)},getSubtreeIterator:function(){return new c(this.getSubRange())}}),u=b.extend({init:function(e){this.enumerate=function(){function t(e){if(i&&!i(e))if(w.is(e,"img")||3==e.nodeType&&(!w.isEmptyspace(e)||"\ufeff"==e.nodeValue))n.push(e);else for(e=e.firstChild;e;)t(e),e=e.nextSibling}var n=[],i=k.Immutables&&k.Immutables.immutable;return new c(e).traverse(t),n}}}),f=b.extend({init:function(e,t,n){var i=this;i.range=e,i.rootNode=h.documentFromRange(e),i.body=t||i.getEditable(e),"body"!=w.name(i.body)&&(i.rootNode=i.body),i.startContainer=i.nodeToPath(e.startContainer),i.endContainer=i.nodeToPath(e.endContainer),i.startOffset=i.offset(e.startContainer,e.startOffset),i.endOffset=i.offset(e.endContainer,e.endOffset),i.immutables=n&&n.immutables,i.immutables&&(i.serializedImmutables=k.Immutables.removeImmutables(i.body)),i.html=i.body.innerHTML,i.immutables&&!i.serializedImmutables.empty&&k.Immutables.restoreImmutables(i.body,i.serializedImmutables)},index:function(e){for(var t,n=0,i=e.nodeType;e=e.previousSibling;)t=e.nodeType,3==t&&i==t||n++,i=t;return n},getEditable:function(e){for(var t=e.commonAncestorContainer;t&&(3==t.nodeType||t.attributes&&(!t.attributes.contentEditable||"false"==t.attributes.contentEditable.nodeValue.toLowerCase()));)t=t.parentNode;return t},restoreHtml:function(){var e=this;w.removeChildren(e.body),e.body.innerHTML=e.html,e.immutables&&!e.serializedImmutables.empty&&k.Immutables.restoreImmutables(e.body,e.serializedImmutables)},offset:function(e,t){if(3==e.nodeType)for(;(e=e.previousSibling)&&3==e.nodeType;)t+=e.nodeValue.length;return t},nodeToPath:function(e){for(var t=[];e!=this.rootNode;)t.push(this.index(e)),e=e.parentNode;return t},toRangePoint:function(e,t,n,i){for(var o=this.rootNode,r=n.length,a=i;r--;)o=o.childNodes[n[r]];for(;o&&3==o.nodeType&&o.nodeValue.length<a;)a-=o.nodeValue.length,o=o.nextSibling;o&&a>=0&&e[t?"setStart":"setEnd"](o,a)},toRange:function(){var e=this,t=e.range.cloneRange();return e.toRangePoint(t,!0,e.startContainer,e.startOffset),e.toRangePoint(t,!1,e.endContainer,e.endOffset),t}}),p=b.extend({init:function(){this.caret=null},addCaret:function(e){var t=this,n=t.caret=w.create(h.documentFromRange(e),"span",{className:"k-marker"});return e.insertNode(n),w.stripBomNode(n.previousSibling),w.stripBomNode(n.nextSibling),e.selectNode(n),n},removeCaret:function(e){var t,n,i,o,r=this,a=r.caret.previousSibling,s=0;a&&(s=C(a)?a.nodeValue.length:x(a)),t=r.caret.parentNode,n=a?x(a):0,w.remove(r.caret),N(t),i=t.childNodes[n],C(i)?e.setStart(i,s):i?(o=w.lastTextNode(i),o?e.setStart(o,o.nodeValue.length):e[a?"setStartAfter":"setStartBefore"](i)):(y.msie||t.innerHTML||(t.innerHTML='<br _moz_dirty="" />'),e.selectNodeContents(t)),e.collapse(!0)},add:function(e,t){var n,i,o=this,r=e.collapsed&&!h.isExpandable(e),a=h.documentFromRange(e);return t&&e.collapsed&&(o.addCaret(e),
e=h.expand(e)),n=e.cloneRange(),n.collapse(!1),o.end=w.create(a,"span",{className:"k-marker"}),n.insertNode(o.end),n=e.cloneRange(),n.collapse(!0),o.start=o.end.cloneNode(!0),n.insertNode(o.start),o._removeDeadMarkers(o.start,o.end),r&&(i=a.createTextNode("\ufeff"),w.insertAfter(i.cloneNode(),o.start),w.insertBefore(i,o.end)),N(e.commonAncestorContainer),e.setStartBefore(o.start),e.setEndAfter(o.end),e},_removeDeadMarkers:function(e,t){e.previousSibling&&"\ufeff"==e.previousSibling.nodeValue&&w.remove(e.previousSibling),t.nextSibling&&"\ufeff"==t.nextSibling.nodeValue&&w.remove(t.nextSibling)},_normalizedIndex:function(e){for(var t=x(e),n=e;n.previousSibling;)3==n.nodeType&&3==n.previousSibling.nodeType&&t--,n=n.previousSibling;return t},remove:function(e){var t,n,i,o,r,a,s,l,d,c,u,f,p=this,m=p.start,h=p.end;for(N(e.commonAncestorContainer);!m.nextSibling&&m.parentNode;)m=m.parentNode;for(;!h.previousSibling&&h.parentNode;)h=h.parentNode;if(t=m.previousSibling&&3==m.previousSibling.nodeType&&m.nextSibling&&3==m.nextSibling.nodeType,n=h.previousSibling&&3==h.previousSibling.nodeType&&h.nextSibling&&3==h.nextSibling.nodeType,i=t&&n,m=m.nextSibling,h=h.previousSibling,o=m===h&&w.isBom(m),o&&m.length>1&&(m.nodeValue=m.nodeValue.charAt(0)),r=o,a=!1,m==p.end&&(a=!!p.start.previousSibling,m=h=p.start.previousSibling||p.end.nextSibling,r=!0),w.remove(p.start),w.remove(p.end),!m||!h)return e.selectNodeContents(e.commonAncestorContainer),void e.collapse(!0);if(s=r?C(m)?m.nodeValue.length:m.childNodes.length:0,l=C(h)?h.nodeValue.length:h.childNodes.length,3==m.nodeType)for(;m.previousSibling&&3==m.previousSibling.nodeType;)m=m.previousSibling,s+=m.nodeValue.length;if(3==h.nodeType)for(;h.previousSibling&&3==h.previousSibling.nodeType;)h=h.previousSibling,l+=h.nodeValue.length;d=m.parentNode,c=h.parentNode,u=this._normalizedIndex(m),f=this._normalizedIndex(h),N(d),3==m.nodeType&&(m=d.childNodes[u]),N(c),3==h.nodeType&&(h=c.childNodes[f]),r?(3==m.nodeType?e.setStart(m,s):e[a?"setStartAfter":"setStartBefore"](m),e.collapse(!0)):(3==m.nodeType?e.setStart(m,s):e.setStartBefore(m),3==h.nodeType?e.setEnd(h,l):e.setEndAfter(h)),p.caret&&p.removeCaret(e)}}),m=/[\u0009-\u000d]|\u0020|\u00a0|\ufeff|\.|,|;|:|!|\(|\)|\?/,h={nodes:function(e){var t=h.textNodes(e);return t.length||(e.selectNodeContents(e.commonAncestorContainer),t=h.textNodes(e),t.length||(t=w.significantChildNodes(e.commonAncestorContainer))),t},textNodes:function(e){return new d(e).enumerate()},editableTextNodes:function(e){var t=[],n=k.Immutables&&k.Immutables.immutableParent;return n&&!n(e.commonAncestorContainer)&&(t=new u(e).enumerate()),t},documentFromRange:function(e){var t=e.startContainer;return 9==t.nodeType?t:t.ownerDocument},createRange:function(e){return y.msie&&y.version<9?new S(e):e.createRange()},selectRange:function(e){var t,n=h.image(e);n&&(e.setStartAfter(n),e.setEndAfter(n)),t=R.selectionFromRange(e),t.removeAllRanges(),t.addRange(e)},stringify:function(e){return g.format("{0}:{1} - {2}:{3}",w.name(e.startContainer),e.startOffset,w.name(e.endContainer),e.endOffset)},split:function(e,t,n){function i(i){var o,r=e.cloneRange();r.collapse(i),r[i?"setStartBefore":"setEndAfter"](t),o=r.extractContents(),n&&(o=w.trim(o)),w[i?"insertBefore":"insertAfter"](o,t)}i(!0),i(!1)},mapAll:function(t,n){var i=[];return new s(t).traverse(function(t){var o=n(t);o&&e.inArray(o,i)<0&&i.push(o)}),i},getAll:function(e,t){var n=t;return"string"==typeof t&&(t=function(e){return w.is(e,n)}),h.mapAll(e,function(e){if(t(e))return e})},getMarkers:function(e){return h.getAll(e,function(e){return"k-marker"==e.className})},image:function(e){var t=h.getAll(e,"img");if(1==t.length)return t[0]},isStartOf:function(e,t){var n,i,o;if(0!==e.startOffset)return!1;for(n=e.cloneRange();0===n.startOffset&&n.startContainer!=t;){for(i=w.findNodeIndex(n.startContainer),o=n.startContainer.parentNode;i>0&&o[i-1]&&w.insignificant(o[i-1]);)i--;n.setStart(o,i)}return 0===n.startOffset&&n.startContainer==t},isEndOf:function(e,t){function n(e){w.insignificant(e)||w.isDataNode(e)&&/^[\ufeff]*$/.test(e.nodeValue)||o.push(e)}var i,o,r=e.cloneRange();return r.collapse(!1),i=r.startContainer,w.isDataNode(i)&&r.startOffset==w.getNodeLength(i)&&(r.setStart(i.parentNode,w.findNodeIndex(i)+1),r.collapse(!0)),r.setEnd(t,w.getNodeLength(t)),o=[],new s(r).traverse(n),!o.length},wrapSelectedElements:function(e){function t(e,t){var n,i=w.getNodeLength(t);if(e==i)return!0;for(n=e;n<i;n++)if(!w.insignificant(t.childNodes[n]))return!1;return!0}for(var n=w.editableParent(e.startContainer),i=w.editableParent(e.endContainer);0===e.startOffset&&e.startContainer!=n;)e.setStart(e.startContainer.parentNode,w.findNodeIndex(e.startContainer));for(;t(e.endOffset,e.endContainer)&&e.endContainer!=i;)e.setEnd(e.endContainer.parentNode,w.findNodeIndex(e.endContainer)+1);return e},expand:function(e){var t,n,i,o,r=e.cloneRange(),a=r.startContainer.childNodes[0===r.startOffset?0:r.startOffset-1],s=r.endContainer.childNodes[r.endOffset];return C(a)&&C(s)?(t=a.nodeValue,n=s.nodeValue,t&&n?(i=t.split("").reverse().join("").search(m),o=n.search(m),i&&o?(o=o==-1?n.length:o,i=i==-1?0:t.length-i,r.setStart(a,i),r.setEnd(s,o),r):r):r):r},isExpandable:function(e){var t,n,i,o,r,a,s=e.startContainer,l=h.documentFromRange(e);return s!=l&&s!=l.body&&(t=e.cloneRange(),!!(n=s.nodeValue)&&(i=n.substring(0,t.startOffset),o=n.substring(t.startOffset),r=0,a=0,i&&(r=i.split("").reverse().join("").search(m)),o&&(a=o.search(m)),r&&a))}},v(k,{SelectionUtils:R,W3CRange:S,RangeIterator:s,W3CSelection:l,RangeEnumerator:d,RestorePoint:f,Marker:p,RangeUtils:h})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/immutables.min",["editor/range.min"],e)}(function(){!function(e,t){var n=window.kendo,i=n.Class,o=n.ui.editor,r=o.Dom,a=n.template,s=o.RangeUtils,l=["ul","ol","tbody","thead","table"],d=["bold","italic","underline","strikethrough","superscript","subscript","forecolor","backcolor","fontname","fontsize","createlink","unlink","autolink","addcolumnleft","addcolumnright","addrowabove","addrowbelow","deleterow","deletecolumn","mergecells","formatting","cleanformatting"],c="k-immutable",u="["+c+"]",f="[contenteditable='false']",p=function(t){return e(t).is("body,.k-editor")},m=function(e){return e.getAttribute&&"false"==e.getAttribute("contenteditable")},h=function(e){return r.closestBy(e,m,p)},g=function(e){var t=h(e.startContainer),n=h(e.endContainer);(t||n)&&(t&&e.setStartBefore(t),n&&e.setEndAfter(n))},b=function(e){if(h(e.commonAncestorContainer))return!0;if(h(e.startContainer)||h(e.endContainer)){var t=s.editableTextNodes(e);if(0===t.length)return!0}return!1},v=function(e){var t,n="",i="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(t=e||10;t>0;--t)n+=i.charAt(Math.round(Math.random()*(i.length-1)));return n},k=function(t){var n,i,o,a={empty:!0};return e(t).find(f).each(function(t,s){n=r.name(s),i=v(),o="<"+n+" "+c+"='"+i+"'></"+n+">",a[i]={node:s,style:e(s).attr("style")},a.empty=!1,e(s).replaceWith(o)}),a},y=function(t,n){var i,o;e(t).find(u).each(function(t,r){i=r.getAttribute(c),o=n[i],e(r).replaceWith(o.node),o.style!=e(o.node).attr("style")&&e(o.node).removeAttr("style").attr("style",o.style)})},w=function(e){var t=n.keys;return e===t.BACKSPACE||e==t.DELETE},x=function(e){var n=e?e.options:t;n&&n.finder&&n.finder._initOptions({immutables:!0})},C=i.extend({init:function(t){this.editor=t,this.serializedImmutables={},this.options=e.extend({},t&&t.options&&t.options.immutables);var n=t.toolbar.tools;x(n.justifyLeft),x(n.justifyCenter),x(n.justifyRight),x(n.justifyFull)},serialize:function(e){var t,n=this._toHtml(e);return n.indexOf(c)===-1?(t=this.randomId(),n=n.replace(/>/," "+c+'="'+t+'">')):t=n.match(/k-immutable\s*=\s*['"](.*)['"]/)[1],this.serializedImmutables[t]=e,n},_toHtml:function(e){var t,n=this.options.serialization,i=typeof n;switch(i){case"string":return a(n)(e);case"function":return n(e);default:return t=r.name(e),"<"+t+"></"+t+">"}},deserialize:function(t){var i=this,o=this.options.deserialization;e(u,t).each(function(){var t=this.getAttribute(c),r=i.serializedImmutables[t];n.isFunction(o)&&o(this,r),e(this).replaceWith(r)}),i.serializedImmutables={}},randomId:function(e){return v(e)},keydown:function(e,t){var n=w(e.keyCode),i=n&&this._cancelDeleting(e,t)||!n&&this._cancelTyping(e,t);if(i)return e.preventDefault(),!0},_cancelTyping:function(e,t){var n=this.editor,i=n.keyboard;return t.collapsed&&!i.typingInProgress&&i.isTypingKey(e)&&b(t)},_cancelDeleting:function(e,t){var i,o,a,s,d=n.keys,c=e.keyCode===d.BACKSPACE,u=e.keyCode==d.DELETE;if(!c&&!u)return!1;if(i=!1,t.collapsed){if(b(t))return!0;if(o=this.nextImmutable(t,u),o&&c&&(a=r.closest(t.commonAncestorContainer,"li"),a&&(s=r.closest(o,"li"),s&&s!==a)))return i;if(o&&!r.tableCell(o)){if(r.parentOfType(o,l)===r.parentOfType(t.commonAncestorContainer,l)){for(;o&&1==o.parentNode.childNodes.length;)o=o.parentNode;if(r.tableCell(o))return i;this._removeImmutable(o,t)}i=!0}}return i},nextImmutable:function(e,t){var n,i=e.commonAncestorContainer;if(r.isBom(i)||t&&s.isEndOf(e,i)||!t&&s.isStartOf(e,i)){if(n=this._nextNode(i,t),n&&r.isBlock(n)&&!h(n))for(;n&&n.children&&n.children[t?0:n.children.length-1];)n=n.children[t?0:n.children.length-1];return h(n)}},_removeImmutable:function(e,t){var n=this.editor,i=new o.RestorePoint(t,n.body);r.remove(e),o._finishUpdate(n,i)},_nextNode:function(e,t){for(var n,i=t?"nextSibling":"previousSibling",o=e;o&&!n;)n=o[i],n&&r.isDataNode(n)&&/^\s|[\ufeff]$/.test(n.nodeValue)&&(o=n,n=o[i]),n||(o=o.parentNode);return n}});C.immutable=m,C.immutableParent=h,C.expandImmutablesIn=g,C.immutablesContext=b,C.toolsToBeUpdated=d,C.removeImmutables=k,C.restoreImmutables=y,o.Immutables=C}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/command.min",["editor/immutables.min"],e)}(function(){!function(e){function t(e,t){var n=e.selectionRestorePoint=new a(e.getRange(),e.body),i=new c(t,n);return i.editor=e,e.undoRedoStack.push(i),n}var n=window.kendo,i=n.Class,o=n.ui.editor,r=o.Dom,a=o.RestorePoint,s=o.Marker,l=e.extend,d=i.extend({init:function(e){this.options=e,this.restorePoint=new a(e.range,e.body,{immutables:e.immutables}),this.marker=new s,this.formatter=e.formatter},getRange:function(){return this.restorePoint.toRange()},lockRange:function(e){return this.marker.add(this.getRange(),e)},releaseRange:function(e){this.marker.remove(e),this.editor.selectRange(e)},undo:function(){var e=this.restorePoint;e.restoreHtml(),this.editor.selectRange(e.toRange())},redo:function(){this.exec()},createDialog:function(t,i){var o=this.editor;return e(t).appendTo(document.body).kendoWindow(l({},o.options.dialogOptions,i)).closest(".k-window").toggleClass("k-rtl",n.support.isRtl(o.wrapper)).end()},exec:function(){var e=this.lockRange(!0);this.formatter.editor=this.editor,this.formatter.toggle(e),this.releaseRange(e)},immutables:function(){return this.editor&&this.editor.options.immutables},expandImmutablesIn:function(e){this.immutables()&&(n.ui.editor.Immutables.expandImmutablesIn(e),this.restorePoint=new a(e,this.editor.body))}}),c=i.extend({init:function(e,t){this.body=e.body,this.startRestorePoint=e,this.endRestorePoint=t},redo:function(){r.removeChildren(this.body),this.body.innerHTML=this.endRestorePoint.html,this.editor.selectRange(this.endRestorePoint.toRange())},undo:function(){r.removeChildren(this.body),this.body.innerHTML=this.startRestorePoint.html,this.editor.selectRange(this.startRestorePoint.toRange())}});l(o,{_finishUpdate:t,Command:d,GenericCommand:c})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/toolbar.min",["editor/range.min"],e)}(function(){!function(e,t){var n,i=window.kendo,o=i.ui,r=o.editor,a=o.Widget,s=e.extend,l=e.proxy,d=i.keys,c=".kendoEditor",u=r.EditorUtils,f=r.ToolTemplate,p=r.Tool,m=i._outerWidth,h=i._outerHeight,g="overflowAnchor",b=".k-tool-group:visible a.k-tool:not(.k-state-disabled),.k-tool.k-overflow-anchor:visible,.k-tool-group:visible .k-widget.k-colorpicker,.k-tool-group:visible .k-selectbox,.k-tool-group:visible .k-dropdown,.k-tool-group:visible .k-combobox .k-input",v={"k-i-sup-script":"superscript","k-i-sub-script":"subscript","k-i-align-left":"justifyLeft","k-i-align-center":"justifyCenter","k-i-align-right":"justifyRight","k-i-align-justify":"justifyFull","k-i-list-unordered":"insertUnorderedList","k-i-list-ordered":"insertOrderedList","k-i-login":"import","k-i-indent-increase":"indent","k-i-indent-decrease":"outdent","k-i-link-horizontal":"createLink","k-i-unlink-horizontal":"unlink","k-i-image":"insertImage","k-i-file-add":"insertFile","k-i-html":"viewHtml","k-i-foreground-color":"foreColor","k-i-paint":"backColor","k-i-table-insert":"createTable","k-i-table-column-insert-left":"addColumnLeft","k-i-table-column-insert-right":"addColumnRight","k-i-table-row-insert-above":"addRowAbove","k-i-table-row-insert-below":"addRowBelow","k-i-table-row-delete":"deleteRow","k-i-table-column-delete":"deleteColumn","k-i-table-properties":"tableWizard","k-i-table-wizard":"tableWizardInsert","k-i-clear-css":"cleanFormatting"},k=p.extend({initialize:function(t,n){t.attr({unselectable:"on"});var i=n.editor.toolbar;t.attr("aria-controls",n.editor.element.attr("id")).on("click",e.proxy(function(){this.overflowPopup.toggle()},i))},options:{name:g},command:e.noop,update:e.noop,destroy:e.noop});u.registerTool(g,new k({key:"",ctrl:!0,template:new f({template:u.overflowAnchorTemplate})})),n=a.extend({init:function(e,t){var n=this;t=s({},t,{name:"EditorToolbar"}),a.fn.init.call(n,e,t),t.popup&&n._initPopup(),t.resizable&&t.resizable.toolbar&&(n._resizeHandler=i.onResize(function(){n.resize(!0)}),n.element.addClass("k-toolbar-resizable"))},events:["execute"],groups:{basic:["bold","italic","underline","strikethrough"],scripts:["subscript","superscript"],alignment:["justifyLeft","justifyCenter","justifyRight","justifyFull"],links:["insertImage","insertFile","createLink","unlink"],lists:["insertUnorderedList","insertOrderedList","indent","outdent"],tables:["createTable","addColumnLeft","addColumnRight","addRowAbove","addRowBelow","deleteRow","deleteColumn"],advanced:["viewHtml","cleanFormatting","print","pdf","exportAs","import"],fonts:["fontName","fontSize"],colors:["foreColor","backColor"]},overflowFlaseTools:["formatting","fontName","fontSize","foreColor","backColor","insertHtml"],_initPopup:function(){var t=this;this.window=e(this.element).wrap("<div class='editorToolbarWindow k-header' />").parent().prepend("<button class='k-button k-bare k-editortoolbar-dragHandle'><span class='k-icon k-i-handler-drag' /></button>").kendoWindow({title:!1,resizable:!1,draggable:{dragHandle:".k-editortoolbar-dragHandle"},animation:{open:{effects:"fade:in"},close:{effects:"fade:out"}},minHeight:42,visible:!1,autoFocus:!1,actions:[],dragend:function(){this._moved=!0}}).on("mousedown",function(n){e(n.target).is(".k-icon")||(t.preventPopupHide=!0)}).on("focusout",function(){t.options.editor.element.focusout()}).data("kendoWindow")},_toggleOverflowStyles:function(e,t){e.find("> li").toggleClass("k-item k-state-default",t).find(".k-tool:not(.k-state-disabled),.k-overflow-button").toggleClass("k-overflow-button k-button",t)},_initOverflowPopup:function(t){var n=this,i="<ul class='k-editor-overflow-popup k-overflow-container k-list-container'></ul>";n.overflowPopup=e(i).appendTo("body").kendoPopup({anchor:t,origin:"bottom right",position:"top right",copyAnchorStyles:!1,open:function(e){this.element.is(":empty")&&e.preventDefault(),n._toggleOverflowStyles(this.element,!0),t.attr("aria-expanded",!0)},close:function(){t.attr("aria-expanded",!1)},activate:l(n.focusOverflowPopup,n)}).data("kendoPopup")},items:function(){var e,t,n=this.options.resizable&&this.options.resizable.toolbar;return t=this.element.children().find("> *, select"),n&&(e=this.overflowPopup,t=t.add(e.element.children().find("> *"))),t},focused:function(){return this.element.find(".k-state-focused").length>0||this.preventPopupHide||this.overflowPopup&&this.overflowPopup.visible()},toolById:function(e){var t,n=this.tools;for(t in n)if(t.toLowerCase()==e)return n[t]},toolGroupFor:function(t){var n,i=this.groups;if(this.isCustomTool(t))return"custom";for(n in i)if(e.inArray(t,i[n])>=0)return n},bindTo:function(t){var n=this,i=n.window;n._editor&&n._editor.unbind("select",l(n.resize,n)),n._editor=t,n.options.resizable&&n.options.resizable.toolbar&&t.options.tools.push(g),n.tools=n.expandTools(t.options.tools),n.render(),n.element.find(".k-combobox .k-input").keydown(function(t){var n=e(this).closest(".k-combobox").data("kendoComboBox"),i=t.keyCode;i==d.RIGHT||i==d.LEFT?n.close():i==d.DOWN&&(n.dropDown.isOpened()||(t.stopImmediatePropagation(),n.open()))}),n._attachEvents(),n.items().each(function(){var i,o=n._toolName(this),r="moreVertical"!==o?n.tools[o]:n.tools.overflowAnchor,a=r&&r.options,s=t.options.messages,l=a&&a.tooltip||s[o],d=e(this);r&&r.initialize&&("fontSize"!=o&&"fontName"!=o||(i=s[o+"Inherit"],d.find("input").val(i).end().find("span.k-input").text(i).end()),r.initialize(d,{title:n._appendShortcutSequence(l,r),editor:n._editor}),d.closest(".k-widget",n.element).addClass("k-editor-widget"),d.closest(".k-colorpicker",n.element).next(".k-colorpicker").addClass("k-editor-widget"))}),t.bind("select",l(n.resize,n)),n.update(),i&&i.wrapper.css({top:"",left:"",width:""})},show:function(){var e,t,n,o=this,r=o.window,a=o.options.editor,s=i.support.browser;r&&(e=r.wrapper,t=a.element,e.is(":visible")&&o.window.options.visible||(e[0].style.width||e.width(this._getWindowWidth()),r._moved||(n=t.offset(),e.css({top:Math.max(0,parseInt(n.top,10)-h(e)-parseInt(o.window.element.css("padding-bottom"),10)),left:Math.max(0,parseInt(n.left,10))})),(s.msie||s.edge)&&o._overlaps(t)?setTimeout(function(){r.open()},0):r.open()))},_getWindowWidth:function(){var e=this,t=e.window.wrapper,n=e.options.editor.element;return m(n)-parseInt(t.css("border-left-width"),10)-parseInt(t.css("border-right-width"),10)},_overlaps:function(e){var t=this.window.wrapper,n=t.offset(),i=n.left,o=n.top,r=e.offset(),a=r.left,s=r.top;return!(a+e.width()<i||a>i+t.width()||s+e.height()<o||s>o+t.height())},hide:function(){this.window&&this.window.close()},focus:function(){var e="tabIndex",t=this.element,n=this._editor.element.attr(e);t.attr(e,n||0).focus().find(b).first().focus(),n||0===n||t.removeAttr(e)},focusOverflowPopup:function(){var e="tabIndex",t=this.overflowPopup.element,n=this._editor.element.attr(e);t.closest(".k-animation-container").addClass("k-overflow-wrapper"),t.attr(e,n||0).find(b).first().focus(),n||0===n||t.removeAttr(e)},_appendShortcutSequence:function(e,t){if(!t.key)return e;var n=e+" (";return t.ctrl&&(n+="Ctrl + "),t.shift&&(n+="Shift + "),t.alt&&(n+="Alt + "),n+=t.key+")"},_nativeTools:["insertLineBreak","insertParagraph","redo","undo","autoLink"],tools:{},isCustomTool:function(e){return!(e in i.ui.Editor.defaultTools)},expandTools:function(t){var n,o,a,l,d=this._nativeTools,c=i.deepExtend({},i.ui.Editor.defaultTools),u={};for(o=0;o<t.length;o++)n=t[o],l=n.name,e.isPlainObject(n)?l&&c[l]?(u[l]=s({},c[l]),s(u[l].options,n)):(a=s({cssClass:"k-i-gear",type:"button",title:""},n),a.name||(a.name="custom"),a.cssClass="k-"+a.name,a.template||"button"!=a.type||(a.template=r.EditorUtils.buttonTemplate,a.title=a.title||a.tooltip),u[l]={options:a}):c[n]&&(u[n]=c[n]);for(o=0;o<d.length;o++)u[d[o]]||(u[d[o]]=c[d[o]]);return u},render:function(){function t(t){var n;return t.getHtml?n=t.getHtml():(e.isFunction(t)||(t=i.template(t)),n=t(r)),e.trim(n)}function n(){f.children().length&&(x&&(f.data("position",w),w++),f.appendTo(v))}function o(t){t!==g?(f=e("<li class='k-tool-group' role='presentation' />"),f.data("overflow",e.inArray(t,C)===-1)):f=e("<li class='k-overflow-tools' />")}var r,a,s,d,c,u,f,p,m=this,h=m.tools,b=m._editor.element,v=m.element.empty(),k=m._editor.options.tools,y=i.support.browser,w=0,x=m.options.resizable&&m.options.resizable.toolbar,C=this.overflowFlaseTools;for(v.empty(),k.length&&(d=k[0].name||k[0]),o(d,C),p=0;p<k.length;p++)d=k[p].name||k[p],r=h[d]&&h[d].options,!r&&e.isPlainObject(d)&&(r=d),a=r&&r.template,"break"==d&&(n(),e("<li class='k-row-break' />").appendTo(m.element),o(d,C)),a&&(u=m.toolGroupFor(d),c==u&&d!=g||(n(),o(d,C),c=u),d==g&&(a.options.title=m.options.messages.overflowAnchor),a=t(a),s=e(a).appendTo(f),"custom"==u&&(n(),o(d,C)),r.exec&&s.hasClass("k-tool")&&s.click(l(r.exec,b[0])));n(),e(m.element).children(":has(> .k-tool)").addClass("k-button-group"),m.options.popup&&y.msie&&y.version<9&&m.window.wrapper.find("*").attr("unselectable","on"),m.updateGroups(),x&&m._initOverflowPopup(m.element.find(".k-overflow-anchor")),m.angular("compile",function(){return{elements:m.element}})},updateGroups:function(){e(this.element).children().each(function(){e(this).addClass("k-state-disabled"),e(this).children().filter(function(){return!e(this).hasClass("k-state-disabled")}).removeClass("k-group-end").first().addClass("k-group-start").end().last().addClass("k-group-end").end().parent().removeClass("k-state-disabled").css("display","")})},decorateFrom:function(t){this.items().filter(".k-decorated").each(function(){var n=e(this).data("kendoSelectBox");n&&n.decorate(t)})},destroy:function(){a.fn.destroy.call(this);var e,t=this.tools;for(e in t)t[e].destroy&&t[e].destroy();this.window&&this.window.destroy(),this._resizeHandler&&i.unbindResize(this._resizeHandler),this.overflowPopup&&this.overflowPopup.destroy()},_attachEvents:function(){var t=this,n=t.overflowPopup?t.overflowPopup.element:e([]);t.attachToolsEvents(t.element.add(n))},attachToolsEvents:function(t){var n=this,o="[role=button].k-tool",r=o+":not(.k-state-disabled)",a=o+".k-state-disabled",s=".k-dropdown",l=".k-colorpicker",u=[o,s,l].join(",");t.off(c).on("mouseenter"+c,r,function(){e(this).addClass("k-state-hover")}).on("mouseleave"+c,r,function(){e(this).removeClass("k-state-hover")}).on("mousedown"+c,u,function(e){e.preventDefault()}).on("keydown"+c,b,function(t){function o(e,t,n){var i=t.find(b),o=i.index(l)+e;return n&&(o=Math.max(0,Math.min(i.length-1,o))),i[o]}var r,a,s,l=this,c=n.options.resizable&&n.options.resizable.toolbar,u=i.support.isRtl(n.element)?-1:1,f=t.keyCode;f==d.RIGHT||f==d.LEFT?a=e(l).is(".k-dropdown")?e(l):o(f==d.RIGHT?1*u:-1*u,n.element,!0):!c||f!=d.UP&&f!=d.DOWN?f==d.HOME?(a=n.element.find(b)[0],t.preventDefault()):f==d.END?(r=n.element.find(b).filter(function(){return"hidden"!==e(this).css("visibility")}),a=r[r.length-1],t.preventDefault()):f==d.ESC?(n.overflowPopup&&n.overflowPopup.visible()&&n.overflowPopup.close(),a=n._editor):f!=d.TAB||t.ctrlKey||t.altKey||(s=c&&e(l.parentElement).hasClass("k-overflow-tool-group")?n.overflowPopup.element:n.element,t.shiftKey?a=o(-1,s):(a=o(1,s),a&&"hidden"!==e(a).closest(".k-overflow-tools").css("visibility")||(a=n._editor))):a=o(f==d.DOWN?1:-1,n.overflowPopup.element,!0),a&&(t.preventDefault(),a.focus()),f!==d.ENTER&&f!==d.SPACEBAR||!e(l).is("a")||e(l).attr("href")||n._executeToolCommand(l,t)}).on("click"+c,r,function(e){n._executeToolCommand(this,e)}).on("click"+c,a,function(e){e.preventDefault()})},_executeToolCommand:function(t,n){var i=this,o=e(t);n.preventDefault(),n.stopPropagation(),o.removeClass("k-state-hover"),o.is("[data-popup]")||i._editor.exec(i._toolName(t))},_toolName:function(t){var n,o,r;if(t)return n=t.className,/k-tool\b/i.test(n)&&(n=t.firstChild.className),o=e.grep(n.split(" "),function(e){return!/^k-(widget|tool|tool-icon|icon|state-hover|header|combobox|dropdown|selectbox|colorpicker)$/i.test(e)}),o[0]?(r=o[0],v[r]&&(r=v[r]),r.indexOf("k-i-")>=0?i.toCamelCase(r.substring(r.indexOf("k-i-")+4)):r.substring(r.lastIndexOf("-")+1)):"custom"},refreshTools:function(){var t=this,n=t._editor,i=n.getRange(),o=r.RangeUtils.textNodes(i),a=n.options.immutables,s=t._immutablesContext(i);o=r.Dom.filterBy(o,r.Dom.htmlIndentSpace,!0),o.length||(o=[i.startContainer]),t.items().each(function(){var n,i=t.tools[t._toolName(this)];i&&(n=e(this),i.update&&i.update(n,o),a&&t._updateImmutablesState(i,n,s))}),this.update()},_immutablesContext:function(e){if(this._editor.options.immutables)return e.collapsed?r.Immutables.immutablesContext(e):0===r.RangeUtils.editableTextNodes(e).length},_updateImmutablesState:function(n,i,o){var a,s,l,d,c,u=n.name,f=i,p=n.options.trackImmutables;if(p===t&&(p=e.inArray(u,r.Immutables.toolsToBeUpdated)>-1),p){if(a=o?"none":"",!i.is(".k-tool")){s=i.data();for(l in s)if(l.match(/^kendo[A-Z][a-zA-Z]*/)){d=s[l],f=d.wrapper;break}}f.css("display",a),c=f.closest("li"),0===c.children(":visible").length&&c.css("display",a)}},update:function(){this.updateGroups()},_resize:function(e){var t=e.width,n=this.options.resizable&&this.options.resizable.toolbar,i=this.overflowPopup,o=this.options.editor.element,r=this.window;this.refreshTools(),n&&(r&&(r.wrapper.width(this._getWindowWidth()),r._moved||r.wrapper.css({left:Math.max(0,parseInt(o.offset().left,10))})),i.visible()&&i.close(!0),this._refreshWidths(),this._shrink(t),this._stretch(t),this._toggleOverflowStyles(this.element,!1),this._toggleOverflowStyles(this.overflowPopup.element,!0),this.element.children("li.k-overflow-tools").css("visibility",i.element.is(":empty")?"hidden":"visible"))},_refreshWidths:function(){this.element.children("li").each(function(t,n){var i=e(n);i.data("outerWidth",m(i,!0))})},_shrink:function(e){var t,n,i;if(e<this._groupsWidth())for(n=this._visibleGroups().filter(":not(.k-overflow-tools)"),i=n.length-1;i>=0&&(t=n.eq(i),!(e>this._groupsWidth()));i--)this._hideGroup(t)},_stretch:function(e){var t,n,i;if(e>this._groupsWidth())for(n=this._hiddenGroups(),i=0;i<n.length&&(t=n.eq(i),!(e<this._groupsWidth())&&this._showGroup(t,e));i++);},_hiddenGroups:function(){var t=this.overflowPopup,n=this.element.children("li.k-tool-group").filter(":hidden");return n=n.add(t.element.children("li")),n.sort(function(t,n){return e(t).data("position")>e(n).data("position")?1:-1}),n},_visibleGroups:function(){return this.element.children("li.k-tool-group, li.k-overflow-tools").filter(":visible")},_groupsWidth:function(){var t=0;return this._visibleGroups().each(function(){t+=e(this).data("outerWidth")}),Math.ceil(t)},_hideGroup:function(e){if(e.data("overflow")){var t=this.overflowPopup;e.detach().prependTo(t.element).addClass("k-overflow-tool-group")}else e.hide()},_showGroup:function(t,n){var i,o;return!(!(t.length&&n>this._groupsWidth()+t.data("outerWidth"))||t.hasClass("k-state-disabled"))&&(t.hasClass("k-overflow-tool-group")?(i=t.data("position"),0===i?t.detach().prependTo(this.element):(o=this.element.children().filter(function(t,n){return e(n).data("position")===i-1}),t.detach().insertAfter(o)),t.removeClass("k-overflow-tool-group")):t.show(),!0)}}),e.extend(r,{Toolbar:n})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/viewhtml.min",["editor/command.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=n.ui.editor,r=o.EditorUtils,a=o.Command,s=o.Tool,l=o.ToolTemplate,d=o.Dom,c=a.extend({init:function(e){var t=this;t.options=e,a.fn.init.call(t,e),t.attributes=null,t.async=!0},exec:function(){function i(e){f.deserialization.immutables=u.immutables,u.value(m.find(h).val()),f.deserialization.immutables=t,o(e),l.change&&l.change(),u.trigger("change")}function o(e){e.preventDefault(),m.data("kendoWindow").destroy(),u.immutables&&(u.immutables.serializedImmutables={}),u.focus()}var a,s,l=this,u=l.editor,f=u.options,p=u.options.messages,m=e(n.template(c.template)(p)).appendTo(document.body),h=".k-editor-textarea";f.serialization.immutables=u.immutables,s=d.getAllComments(u.body),a=r.cacheComments(u.value(),s),a=c.indent(a),a=r.retrieveComments(a,s),f.serialization.immutables=t,this.createDialog(m,{title:p.viewHtml,close:o,visible:!1}).find(h).val(a).end().find(".k-dialog-update").click(i).end().find(".k-dialog-close").click(o).end().data("kendoWindow").center().open(),m.find(h).focus()}});i(c,{template:"<div class='k-editor-dialog k-popup-edit-form k-viewhtml-dialog'><div class='k-edit-form-container'></div><textarea class='k-editor-textarea k-input'></textarea><div class='k-edit-buttons k-state-default'><button class='k-dialog-update k-button k-primary'>#: dialogUpdate #</button><button class='k-dialog-close k-button'>#: dialogCancel #</button></div></div></div>",indent:function(e){return e.replace(/<\/(p|li|ul|ol|h[1-6]|table|tr|td|th)>/gi,"</$1>\n").replace(/<(ul|ol)([^>]*)><li/gi,"<$1$2>\n<li").replace(/<br \/>/gi,"<br />\n").replace(/\n$/,"")}}),n.ui.editor.ViewHtmlCommand=c,o.EditorUtils.registerTool("viewHtml",new s({command:c,template:new l({template:r.buttonTemplate,title:"View HTML"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/format.min",["editor/command.min"],e)}(function(){!function(e){var t=window.kendo,n=e.extend,i=t.ui.editor,o=i.Tool,r=i.Command,a=i.EditorUtils,s=r.extend({init:function(e){e.formatter=e.formatter();var t=e.formatter.finder;t&&a.formatByName("immutable",t.format)&&t._initOptions({immutables:e.immutables}),r.fn.init.call(this,e)}}),l=o.extend({init:function(e){o.fn.init.call(this,e)},command:function(e){var t=this;return new s(n(e,{formatter:t.options.formatter}))},update:function(e,t){var n=this.options.finder.isFormatted(t);e.toggleClass("k-state-selected",n),e.attr("aria-pressed",n)}});e.extend(i,{FormatCommand:s,FormatTool:l})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/inlineformat.min",["editor/plugins/format.min"],e)}(function(){!function(e){var t=window.kendo,n=t.Class,i=t.ui.editor,o=t.ui.Editor.fn.options.formats,r=i.EditorUtils,a=i.Tool,s=i.ToolTemplate,l=i.FormatTool,d=i.Dom,c=i.RangeUtils,u=e.extend,f=i.EditorUtils.registerTool,p=i.EditorUtils.registerFormat,m="mousedown.kendoEditor",h="keydown.kendoEditor",g="k-marker",b=n.extend({init:function(e){this.format=e},numberOfSiblings:function(e){var t,n=0,i=0,o=0,r=e.parentNode;for(t=r.firstChild;t;t=t.nextSibling)t!=e&&(t.className==g?o++:3==t.nodeType?n++:i++);return o>1&&r.firstChild.className==g&&r.lastChild.className==g?0:i+n},findSuitable:function(e,t){var n,i;if(!t&&this.numberOfSiblings(e)>0)return null;for(n=e.parentNode,i=this.format[0].tags;!d.ofType(n,i);){if(this.numberOfSiblings(n)>0)return null;n=n.parentNode}return n},findFormat:function(e){var t,n,i,o,r,a=this.format,s=d.attrEquals;for(t=0,n=a.length;t<n;t++){if(i=e,o=a[t].tags,r=a[t].attr,i&&d.ofType(i,o)&&s(i,r))return i;for(;i;)if(i=d.parentOfType(i,o),i&&s(i,r))return i}return null},isFormatted:function(e){var t,n;for(t=0,n=e.length;t<n;t++)if(this.findFormat(e[t]))return!0;return!1}}),v=n.extend({init:function(e,t){this.finder=new b(e),this.attributes=u({},e[0].attr,t),this.tag=e[0].tags[0]},wrap:function(e){return d.wrap(e,d.create(e.ownerDocument,this.tag,this.attributes))},activate:function(e,t){this.finder.isFormatted(t)?(this.split(e),this.remove(t)):this.apply(t)},toggle:function(e){var t=this.immutables()?c.editableTextNodes:c.textNodes,n=t(e);n.length>0&&this.activate(e,n)},immutables:function(){return this.editor&&this.editor.options.immutables},apply:function(e){var t,n,i,o,r=[];if(e.length>1)for(t=0,n=e.length;t<n;t++)i=e[t],o=this.format(i,!0),r.push(o);else i=e[0],o=this.format(i,!1);this.consolidate(r)},format:function(e,t){var n=this.finder.findSuitable(e),i=this.attributes,o=i?i.style||{}:{};if(n)d.is(n,"font")&&(o.color&&n.removeAttribute("color"),o.fontName&&n.removeAttribute("face"),o.fontSize&&n.removeAttribute("size")),d.attr(n,i);else{for(;!d.isBlock(e.parentNode)&&1==e.parentNode.childNodes.length&&"true"!==e.parentNode.contentEditable&&t;)e=e.parentNode;n=this.wrap(e)}return n},remove:function(e){var t,n,i;for(t=0,n=e.length;t<n;t++)i=this.finder.findFormat(e[t]),
i&&(this.attributes&&this.attributes.style?(d.unstyle(i,this.attributes.style),i.style.cssText||i.attributes["class"]||d.unwrap(i)):d.unwrap(i))},split:function(e){var t,n,i=c.textNodes(e),o=i.length;if(o>0)for(t=0;t<o;t++)n=this.finder.findFormat(i[t]),n&&c.split(e,n,!0)},consolidate:function(e){for(var t,n;e.length>1;)if(t=e.pop(),n=e[e.length-1],t.previousSibling&&t.previousSibling.className==g&&n.appendChild(t.previousSibling),t.tagName==n.tagName&&t.previousSibling==n&&t.style.cssText==n.style.cssText&&t.className===n.className){for(;t.firstChild;)n.appendChild(t.firstChild);d.remove(t)}}}),k=b.extend({init:function(e,t){this.format=e,this.greedyProperty=t,b.fn.init.call(this,e)},getInlineCssValue:function(t){var n,i,o,r,a,s,l,c,u,f,p,m,h=t.attributes,g=e.trim;if(h)for(n=0,i=h.length;n<i;n++)if(o=h[n],r=o.nodeName,a=o.nodeValue,o.specified&&"style"==r)for(s=g(a||t.style.cssText).split(";"),c=0,u=s.length;c<u;c++)if(l=s[c],l.length){if(f=l.split(":"),p=g(f[0].toLowerCase()),m=g(f[1]),p!=this.greedyProperty)continue;return p.indexOf("color")>=0?d.toHex(m):m}},getFormatInner:function(t){var n,i,o,r=e(d.isDataNode(t)?t.parentNode:t),a=r.parentsUntil("[contentEditable]").addBack().toArray().reverse();for(n=0,i=a.length;n<i;n++)if(o="className"==this.greedyProperty?a[n].className:this.getInlineCssValue(a[n]))return o;return"inherit"},getFormat:function(e){var t,n,i=this.getFormatInner(e[0]);for(t=1,n=e.length;t<n;t++)if(i!=this.getFormatInner(e[t]))return"";return i},isFormatted:function(e){return""!==this.getFormat(e)}}),y=v.extend({init:function(e,n,i){v.fn.init.call(this,e,n),this.values=n,this.finder=new k(e,i),i&&(this.greedyProperty=t.toCamelCase(i))},activate:function(e,t){var n=this.greedyProperty,i="apply";this.split(e),n&&"inherit"==this.values.style[n]&&(i="remove"),this[i](t)}}),w=l.extend({init:function(e){l.fn.init.call(this,u(e,{finder:new b(e.format),formatter:function(){return new v(e.format)}}))}}),x=a.extend({update:function(e,t){var n=e.data(this.type);n.close(),n.value(this.finder.getFormat(t))}}),C=x.extend({init:function(e){a.fn.init.call(this,e),this.type=t.support.browser.msie||t.support.touch?"kendoDropDownList":"kendoComboBox",this.format=[{tags:["span","font"]}],this.finder=new k(this.format,e.cssAttr)},command:function(e){var t=this.options,n=this.format,o={};return new i.FormatCommand(u(e,{formatter:function(){return o[t.domAttr]=e.value,new y(n,{style:o},t.cssAttr)}}))},initialize:function(e,n){var i,o,r,s=n.editor,l=this.options,d=l.name,c=[];l.defaultValue&&(c=[{text:s.options.messages[l.defaultValue[0].text],value:l.defaultValue[0].value}]),i=c.concat(l.items?l.items:s.options[d]||[]),e.attr({title:n.title}),e[this.type]({dataTextField:"text",dataValueField:"value",dataSource:i,change:function(){s._range=o,a.exec(s,d,this.value())},close:function(){setTimeout(function(){s._deleteSavedRange()},0)},highlightFirst:!1}),e.closest(".k-widget").removeClass("k-"+d).find("*").addBack().attr("unselectable","on"),r=e.data(this.type),r.value("inherit"),r.wrapper.on(m,".k-select,.k-input",function(){var e=s.getRange();o=s._containsRange(e)?e:o}).on(h,function(e){e.keyCode===t.keys.ENTER&&(s._deleteSavedRange(),e.preventDefault())})}}),T=a.extend({init:function(e){a.fn.init.call(this,e),this.format=[{tags:["span","font"]}],this.finder=new k(this.format,e.cssAttr)},options:{palette:"websafe"},update:function(){this._widget.close()},command:function(e){var t=this.options,n=this.format,o={};return new i.FormatCommand(u(e,{formatter:function(){return o[t.domAttr]=e.value,new y(n,{style:o},t.cssAttr)}}))},initialize:function(n,i){var o=this,s=i.editor,l=this.name,d=u({},T.fn.options,this.options),c=d.palette,f=d.columns;n=this._widget=new t.ui.ColorPicker(n,{toolIcon:"k-icon k-i-"+r.getToolCssClass(d.name),palette:c,columns:f,change:function(){var e=n.value();t.support.browser.msie&&o.storedRange&&o._inputFocused&&s.selectRange(o.storedRange),e&&a.exec(s,l,e),delete o.storedRange,delete o._inputFocused,s.focus()},open:function(t){var n=t.sender;o.storedRange=s.getRange(),n._popup.element.on(m,function(t){e(t.target).is("input.k-color-value")||t.preventDefault()}),n._popup.element.is("[unselectable='on']")||n._popup.element.attr({unselectable:"on"}).find("*:not(input)").attr("unselectable","on").end().find("input").on("focus",function(){o._inputFocused=!0})},close:function(e){e.sender._popup.element.off(m),t.support.browser.msie&&o.storedRange&&o._inputFocused&&s.selectRange(o.storedRange)},activate:function(e){e.preventDefault(),"rgba(255, 255, 255, 0)"!==e.sender._value.toCssRgba()&&n.trigger("change")}}),n.wrapper.attr({title:i.title,unselectable:"on"}).find("*:not(input)").attr("unselectable","on"),n.value("transparent")}});u(i,{InlineFormatFinder:b,InlineFormatter:v,DelayedExecutionTool:x,GreedyInlineFormatFinder:k,GreedyInlineFormatter:y,InlineFormatTool:w,FontTool:C,ColorTool:T}),p("bold",[{tags:["strong","b"]},{tags:["span"],attr:{style:{fontWeight:"bold"}}}]),f("bold",new w({key:"B",ctrl:!0,format:o.bold,template:new s({template:r.buttonTemplate,title:"Bold"})})),p("italic",[{tags:["em","i"]},{tags:["span"],attr:{style:{fontStyle:"italic"}}}]),f("italic",new w({key:"I",ctrl:!0,format:o.italic,template:new s({template:r.buttonTemplate,title:"Italic"})})),p("underline",[{tags:["span"],attr:{style:{textDecoration:"underline"}}},{tags:["u"]}]),f("underline",new w({key:"U",ctrl:!0,format:o.underline,template:new s({template:r.buttonTemplate,title:"Underline"})})),p("strikethrough",[{tags:["del","strike"]},{tags:["span"],attr:{style:{textDecoration:"line-through"}}}]),f("strikethrough",new w({format:o.strikethrough,template:new s({template:r.buttonTemplate,title:"Strikethrough"})})),p("superscript",[{tags:["sup"]}]),f("superscript",new w({format:o.superscript,template:new s({template:r.buttonTemplate,title:"Superscript"})})),p("subscript",[{tags:["sub"]}]),f("subscript",new w({format:o.subscript,template:new s({template:r.buttonTemplate,title:"Subscript"})})),f("foreColor",new T({cssAttr:"color",domAttr:"color",name:"foreColor",template:new s({template:r.colorPickerTemplate,title:"Color"})})),f("backColor",new T({cssAttr:"background-color",domAttr:"backgroundColor",name:"backColor",template:new s({template:r.colorPickerTemplate,title:"Background Color"})})),f("fontName",new C({cssAttr:"font-family",domAttr:"fontFamily",name:"fontName",defaultValue:[{text:"fontNameInherit",value:"inherit"}],template:new s({template:r.comboBoxTemplate,title:"Font Name"})})),f("fontSize",new C({cssAttr:"font-size",domAttr:"fontSize",name:"fontSize",defaultValue:[{text:"fontSizeInherit",value:"inherit"}],template:new s({template:r.comboBoxTemplate,title:"Font Size"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/link.min",["editor/plugins/inlineformat.min"],e)}(function(){!function(e,t){function n(e,t){for(var n=e.length;n--&&!t.test(e[n]););return n}function i(e,t){var n=t.exec(e);return n?n.index:-1}var o=window.kendo,r=o.Class,a=e.extend,s=e.proxy,l=o.ui.editor,d=l.Dom,c=l.RangeUtils,u=l.EditorUtils,f=l.Command,p=l.Tool,m=l.ToolTemplate,h=l.InlineFormatter,g=l.InlineFormatFinder,b=c.textNodes,v=c.editableTextNodes,k=l.EditorUtils.registerTool,y=o.keys,w="http://",x=/^\w*:\/\//,C=/[\w\/\$\-_\*\?]/i,T=r.extend({findSuitable:function(e){return d.parentOfType(e,["a"])}}),_=r.extend({init:function(){this.finder=new T},apply:function(e,t){var n,i,o,r,a,s,l,u=this.immutables?v(e):b(e);if(t.innerText){for(i=c.documentFromRange(e),n=c.getMarkers(e),e.deleteContents(),r=d.create(i,"a",t),e.insertNode(r),a=r.parentNode,"a"==d.name(a)&&d.insertAfter(r,a),d.emptyNode(a)&&d.remove(a),s=r,l=0;l<n.length;l++)d.insertAfter(n[l],s),s=n[l];n.length&&(d.insertBefore(i.createTextNode("\ufeff"),n[1]||n[0]),d.insertAfter(i.createTextNode("\ufeff"),n[1]||n[0]),e.setStartBefore(n[0]),e.setEndAfter(n[n.length-1]))}else o=new h([{tags:["a"]}],t),o.finder=this.finder,o.apply(u)}}),N=f.extend({init:function(e){var t=this;e.formatter={toggle:function(e){var n=t.immutables()?v(e):b(e);new h([{tags:["a"]}]).remove(n)}},this.options=e,f.fn.init.call(this,e)}}),R=f.extend({init:function(e){var t;this.options=e,f.fn.init.call(this,e),this.formatter=new _,e.url?this.exec=function(){this.formatter.immutables=t&&t.immutables(),this.formatter.apply(e.range,{href:e.url,innerText:e.text||e.url,target:e.target})}:(this.attributes=null,this.async=!0)},_dialogTemplate:function(){return o.template("<div class=\"k-editor-dialog k-popup-edit-form\"><div class=\"k-edit-form-container\"><div class='k-edit-label'><label for='k-editor-link-url'>#: messages.linkWebAddress #</label></div><div class='k-edit-field'><input type='text' class='k-textbox' id='k-editor-link-url'></div><div class='k-edit-label k-editor-link-text-row'><label for='k-editor-link-text'>#: messages.linkText #</label></div><div class='k-edit-field k-editor-link-text-row'><input type='text' class='k-textbox' id='k-editor-link-text'></div><div class='k-edit-label'><label for='k-editor-link-title'>#: messages.linkToolTip #</label></div><div class='k-edit-field'><input type='text' class='k-textbox' id='k-editor-link-title'></div><div class='k-edit-label'></div><div class='k-edit-field'><input type='checkbox' class='k-checkbox' id='k-editor-link-target'><label for='k-editor-link-target' class='k-checkbox-label'>#: messages.linkOpenInNewWindow #</label></div><div class='k-edit-buttons k-state-default'><button class=\"k-dialog-insert k-button k-primary\">#: messages.dialogInsert #</button><button class=\"k-dialog-close k-button\">#: messages.dialogCancel #</button></div></div></div>")({messages:this.editor.options.messages})},exec:function(){var t,n,i,o,r=this.editor.options.messages;this._initialText="",this._range=this.lockRange(!0),this.formatter.immutables=this.immutables(),t=b(this._range),n=t.length?this.formatter.finder.findSuitable(t[0]):null,i=t.length&&"img"==d.name(t[0]),o=this.createDialog(this._dialogTemplate(),{title:r.createLink,close:s(this._close,this),visible:!1}),n&&(this._range.selectNodeContents(n),t=b(this._range)),this._initialText=this.linkText(t),o.find(".k-dialog-insert").click(s(this._apply,this)).end().find(".k-dialog-close").click(s(this._close,this)).end().find(".k-edit-field input").keydown(s(this._keydown,this)).end().find("#k-editor-link-url").val(this.linkUrl(n)).end().find("#k-editor-link-text").val(this._initialText).end().find("#k-editor-link-title").val(n?n.title:"").end().find("#k-editor-link-target").attr("checked",!!n&&"_blank"==n.target).end().find(".k-editor-link-text-row").toggle(!i),this._dialog=o.data("kendoWindow").center().open(),e("#k-editor-link-url",o).focus().select()},_keydown:function(e){var t=o.keys;e.keyCode==t.ENTER?this._apply(e):e.keyCode==t.ESC&&this._close(e)},_apply:function(t){var n,i,o,r=this._dialog.element,a=e("#k-editor-link-url",r).val(),s=e("#k-editor-link-text",r);a&&a!=w&&(a.indexOf("@")>0&&!/^(\w+:)|(\/\/)/i.test(a)&&(a="mailto:"+a),this.attributes={href:a},n=e("#k-editor-link-title",r).val(),n&&(this.attributes.title=n),s.is(":visible")&&(i=s.val(),i||this._initialText?i&&i!==this._initialText&&(this.attributes.innerText=d.stripBom(i)):this.attributes.innerText=a),o=e("#k-editor-link-target",r).is(":checked"),this.attributes.target=o?"_blank":null,this.formatter.apply(this._range,this.attributes)),this._close(t),this.change&&this.change()},_close:function(e){e.preventDefault(),this._dialog.destroy(),d.windowFromDocument(c.documentFromRange(this._range)).focus(),this.releaseRange(this._range)},linkUrl:function(e){return e?e.getAttribute("href",2):w},linkText:function(e){var t,n="";for(t=0;t<e.length;t++)n+=e[t].nodeValue;return d.stripBom(n||"")},redo:function(){var e=this.lockRange(!0);this.formatter.apply(e,this.attributes),this.releaseRange(e)}}),S=f.extend({init:function(e){f.fn.init.call(this,e),this.formatter=new _},exec:function(){var e,t,n,i=this.detectLink();i&&(e=this.getRange(),t=new o.ui.editor.Marker,n=e.cloneRange(),n.setStart(i.start.node,i.start.offset),n.setEnd(i.end.node,i.end.offset),e=this.lockRange(),t.add(n),this.formatter.apply(n,{href:this._ensureWebProtocol(i.text)}),t.remove(n),this.releaseRange(e))},detectLink:function(){var e,t,n=this.getRange(),i=n.startContainer,o=n.startOffset,r=i.previousSibling;return!r&&(d.isBom(i)&&!i.nextSibling||!o&&d.isDataNode(i))&&(i=i.parentNode,o=0),e=new P({node:i,offset:o,cancelAtNode:function(e){return e&&"a"===d.name(e)}}),t=new A(e),t.detectLink()},changesContent:function(){return!!this.detectLink()},_ensureWebProtocol:function(e){var t=this._hasProtocolPrefix(e);return t?e:this._prefixWithWebProtocol(e)},_hasProtocolPrefix:function(e){return x.test(e)},_prefixWithWebProtocol:function(e){return w+e}}),z=p.extend({init:function(t){this.options=t,this.finder=new g([{tags:["a"]}]),p.fn.init.call(this,e.extend(t,{command:N}))},initialize:function(e,t){p.fn.initialize.call(this,e,t),e.addClass("k-state-disabled")},update:function(e,t){e.toggleClass("k-state-disabled",!this.finder.isFormatted(t)).removeClass("k-state-hover")}}),A=r.extend({init:function(e){this.traverser=e,this.start=E(),this.end=E(),this.text=""},detectLink:function(){var t,n,i,o,r=this.traverser.node,a=this.traverser.offset;if(d.isDataNode(r)){if(t=r.data.substring(0,a),/\s{2}$/.test(d.stripBom(t)))return}else 0===a&&(n=d.closestEditableOfType(r,d.blockElements),n&&n.previousSibling&&this.traverser.init({node:n.previousSibling}));return this.traverser.traverse(e.proxy(this._detectEnd,this)),this.end.blank()||(this.traverser=this.traverser.clone(this.end),this.traverser.traverse(e.proxy(this._detectStart,this)),this._isLinkDetected()||(i=this.traverser.extendOptions(this.start),o=new I(i),o.traverse(e.proxy(this._skipStartPuntuation,this)),this._isLinkDetected()||(this.start=E()))),this.start.blank()?null:{start:this.start,end:this.end,text:this.text}},_isLinkDetected:function(){return x.test(this.text)||/^w{3}\./i.test(this.text)},_detectEnd:function(e,t){var i=n(e,C);if(i>-1)return this.end.node=t,this.end.offset=i+1,!1},_detectStart:function(e,t){var i=n(e,/\s/),o=i+1;if(this.text=e.substring(o)+this.text,this.start.node=t,this.start.offset=o,i>-1)return!1},_skipStartPuntuation:function(e,t,n){var o=i(e,/\w/),r=o;if(o===-1&&(r=e.length),this.text=this.text.substring(r),this.start.node=t,this.start.offset=r+(0|n),o>-1)return!1}}),E=function(){return{node:null,offset:null,blank:function(){return null===this.node&&null===this.offset}}},D=r.extend({init:function(n){this.node=n.node,this.offset=n.offset===t?d.isDataNode(this.node)&&this.node.length||0:n.offset,this.cancelAtNode=n.cancelAtNode||this.cancelAtNode||e.noop},traverse:function(e){e&&(this.cancel=!1,this._traverse(e,this.node,this.offset))},_traverse:function(e,n,i){var o,r,a,s;if(n&&!this.cancel){if(3!==n.nodeType)return r=this.edgeNode(n),this.cancel=this.cancel||this.cancelAtNode(r),this._traverse(e,r);if(o=n.data,i!==t&&(o=this.subText(o,i)),this.cancel=e(o,n,i)===!1,a=this.next(n),!a)for(s=n.parentNode;!a&&d.isInline(s);)a=this.next(s),s=s.parentNode;this.cancel=this.cancel||this.cancelAtNode(a),this._traverse(e,a)}},extendOptions:function(t){return e.extend({node:this.node,offset:this.offset,cancelAtNode:this.cancelAtNode},t||{})},edgeNode:function(e){},next:function(e){},subText:function(e,t){}}),P=D.extend({subText:function(e,t){return e.substring(0,t)},next:function(e){return e.previousSibling},edgeNode:function(e){return e.lastChild},clone:function(e){var t=this.extendOptions(e);return new P(t)}}),I=D.extend({subText:function(e,t){return e.substring(t)},next:function(e){return e.nextSibling},edgeNode:function(e){return e.firstChild},clone:function(e){var t=this.extendOptions(e);return new I(t)}});a(o.ui.editor,{LinkFormatFinder:T,LinkFormatter:_,UnlinkCommand:N,LinkCommand:R,AutoLinkCommand:S,UnlinkTool:z,DomTextLinkDetection:A,LeftDomTextTraverser:P,RightDomTextTraverser:I}),k("createLink",new p({key:"K",ctrl:!0,command:R,template:new m({template:u.buttonTemplate,title:"Create Link"})})),k("unlink",new z({key:"K",ctrl:!0,shift:!0,template:new m({template:u.buttonTemplate,title:"Remove Link"})})),k("autoLink",new p({key:[y.ENTER,y.SPACEBAR],keyPressCommand:!0,command:S}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/formatblock.min",["editor/plugins/format.min"],e)}(function(){!function(e){var t,n=window.kendo,i=n.Class,o=e.extend,r=n.ui.editor,a=n.ui.Editor.fn.options.formats,s=r.Dom,l=r.ToolTemplate,d=r.FormatTool,c=r.EditorUtils,u=c.registerTool,f=c.registerFormat,p=r.RangeUtils,m=i.extend({init:function(e){this.format=e},contains:function(e,t){var n,i,o;for(n=0,i=t.length;n<i;n++)if(o=t[n],!o||!s.isAncestorOrSelf(e,o))return!1;return!0},findSuitable:function(t){var n,i,o,r,a=this.format,l=[];for(n=0,i=t.length;n<i;n++){for(r=a.length-1;r>=0&&!(o=s.ofType(t[n],a[r].tags)?t[n]:s.closestEditableOfType(t[n],a[r].tags));r--);if(!o||"true"===o.contentEditable)return[];e.inArray(o,l)<0&&l.push(o)}for(this._resolveListsItems(l),n=0,i=l.length;n<i;n++)if(this.contains(l[n],l))return[l[n]];return l},_resolveListsItems:function(e){var t,n,i;for(t=0;t<e.length;t++)n=e[t],i=s.is(n,"li")?n:s.wrapper(n),i=i&&s.list(i)?i.children[0]:i,s.is(i,"li")&&(n=e[t]=i)},findFormat:function(e){var t,n,i,o,a,l,d=this.format,c=s.editableParent(e),u=this.options&&this.options.immutables,f=r.Immutables;for(t=0,n=d.length;t<n;t++){if(i=e,o=d[t].tags,a=d[t].attr,u&&o&&"immutable"==o[0]&&(l=f.immutableParent(i),l&&s.attrEquals(l,a)))return i;for(;i&&s.isAncestorOf(c,i);){if(s.ofType(i,o)&&s.attrEquals(i,a))return i;i=i.parentNode}}return null},getFormat:function(e){var t,n,i=this,o=function(e){return i.findFormat(s.isDataNode(e)?e.parentNode:e)},r=o(e[0]);if(!r)return"";for(t=1,n=e.length;t<n;t++)if(r!=o(e[t]))return"";return r.nodeName.toLowerCase()},isFormatted:function(e){for(var t=0,n=e.length;t<n;t++)if(!this.findFormat(e[t]))return!1;return!0}}),h=i.extend({init:function(e,t){this.format=e,this.values=t,this.finder=new m(e)},wrap:function(e,t,n){var i,o,r,a,l,d=1==n.length?s.blockParentOrBody(n[0]):s.commonAncestor.apply(null,n);for(s.isInline(d)&&(d=s.blockParentOrBody(d)),i=s.significantChildNodes(d),o=s.findNodeIndex(i[0]),r=s.create(d.ownerDocument,e,t),a=0;a<i.length;a++)l=i[a],s.isBlock(l)?(s.attr(l,t),r.childNodes.length&&(s.insertBefore(r,l),r=r.cloneNode(!1)),o=s.findNodeIndex(l)+1):r.appendChild(l);r.firstChild&&s.insertAt(d,r,o)},apply:function(t){function n(e){return o({},e&&e.attr,m)}var i,r,a,l,d,u,f,p,m=this.values;if(this._handleImmutables(t,!0),r=s.filter("img",t),a=c.formatByName("img",this.format),l=n(a),e.each(r,function(){s.attr(this,l)}),r.length!=t.length)if(d=s.filter("img",t,!0),u=this.finder.findSuitable(d),u.length)for(f=0,p=u.length;f<p;f++)i=c.formatByName(s.name(u[f]),this.format),s.attr(u[f],n(i));else i=this.format[0],this.wrap(i.tags[0],n(i),d)},_handleImmutables:function(e,t){var n,i,o,a,l;if(this.immutables()&&(n=c.formatByName("immutable",this.format)))for(i=r.Immutables,o=e.length-1,a=o;a>=0;a--)l=i.immutableParent(e[a]),l&&(l!==e[a+1]&&(t?s.attr(l,n.attr):s.unstyle(l,n.attr.style)),e.splice(a,1))},immutables:function(){return this.editor&&this.editor.options.immutables},remove:function(e){var t,n,i,o,r;for(this._handleImmutables(e,!1),t=0,n=e.length;t<n;t++)i=this.finder.findFormat(e[t]),i&&(r=s.name(i),o=c.formatByName(r,this.format),o.attr.style&&s.unstyle(i,o.attr.style),o.attr.className&&s.removeClass(i,o.attr.className))},toggle:function(e){var t=this,n=s.filterBy(p.nodes(e),s.htmlIndentSpace,!0);t.finder.isFormatted(n)?t.remove(n):t.apply(n)}}),g=i.extend({init:function(e,t){var n=this;n.format=e,n.values=t,n.finder=new m(e)},apply:function(e){var t,n,i,o,a,l,d,c,u,f,p=this.format,m=s.blockParents(e),g=p[0].tags[0];if(m.length)for(t=0,n=m.length;t<n;t++)c=m[t],u=this.immutables()&&r.Immutables.immutableParent(c),u||(d=s.name(c),"li"==d?(i=c.parentNode,o=new r.ListFormatter(i.nodeName.toLowerCase(),g),a=this.editor.createRange(),a.selectNode(m[t]),o.toggle(a)):g&&("td"==d||c.attributes.contentEditable)?new h(p,this.values).apply(c.childNodes):(l=s.changeTag(c,g),s.attr(l,p[0].attr)));else f=new h(p,this.values),f.editor=this.editor,f.apply(e)},toggle:function(e){var t=p.textNodes(e);t.length||(e.selectNodeContents(e.commonAncestorContainer),t=p.textNodes(e),t.length||(t=s.significantChildNodes(e.commonAncestorContainer))),this.apply(t)},immutables:function(){return this.editor&&this.editor.options.immutables}}),b=d.extend({init:function(e){d.fn.init.call(this,o(e,{finder:new m(e.format),formatter:function(){return new h(e.format)}}))}});o(r,{BlockFormatFinder:m,BlockFormatter:h,GreedyBlockFormatter:g,BlockFormatTool:b}),t=["ul","ol","li"],f("justifyLeft",[{tags:s.nonListBlockElements,attr:{style:{textAlign:"left"}}},{tags:["img"],attr:{style:{"float":"left",display:"",marginLeft:"",marginRight:""}}},{tags:["immutable"],attr:{style:{"float":"left",display:"",marginLeft:"",marginRight:""}}},{tags:t,attr:{style:{textAlign:"left",listStylePosition:""}}}]),u("justifyLeft",new b({format:a.justifyLeft,template:new l({template:c.buttonTemplate,title:"Justify Left"})})),f("justifyCenter",[{tags:s.nonListBlockElements,attr:{style:{textAlign:"center"}}},{tags:["img"],attr:{style:{display:"block",marginLeft:"auto",marginRight:"auto","float":""}}},{tags:["immutable"],attr:{style:{display:"block",marginLeft:"auto",marginRight:"auto","float":""}}},{tags:t,attr:{style:{textAlign:"center",listStylePosition:"inside"}}}]),u("justifyCenter",new b({format:a.justifyCenter,template:new l({template:c.buttonTemplate,title:"Justify Center"})})),f("justifyRight",[{tags:s.nonListBlockElements,attr:{style:{textAlign:"right"}}},{tags:["img"],attr:{style:{"float":"right",display:"",marginLeft:"",marginRight:""}}},{tags:["immutable"],attr:{style:{"float":"right",display:"",marginLeft:"",marginRight:""}}},{tags:t,attr:{style:{textAlign:"right",listStylePosition:"inside"}}}]),u("justifyRight",new b({format:a.justifyRight,template:new l({template:c.buttonTemplate,title:"Justify Right"})})),f("justifyFull",[{tags:s.nonListBlockElements,attr:{style:{textAlign:"justify"}}},{tags:["img"],attr:{style:{display:"block",marginLeft:"auto",marginRight:"auto","float":""}}},{tags:["immutable"],attr:{style:{display:"block",marginLeft:"auto",marginRight:"auto","float":""}}},{tags:t,attr:{style:{textAlign:"justify",listStylePosition:""}}}]),u("justifyFull",new b({format:a.justifyFull,template:new l({template:c.buttonTemplate,title:"Justify Full"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/lists.min",["editor/plugins/formatblock.min"],e)}(function(){!function(e){var t=window.kendo,n=t.Class,i=e.extend,o=t.ui.editor,r=o.Dom,a=o.RangeUtils,s=o.EditorUtils,l=o.Command,d=o.ToolTemplate,c=o.FormatTool,u=o.BlockFormatFinder,f=a.textNodes,p=o.EditorUtils.registerTool,m=u.extend({init:function(e){this.tag=e;var t=this.tags=["ul"==e?"ol":"ul",e];u.fn.init.call(this,[{tags:t}])},isFormatted:function(e){var t,n,i=[];for(n=0;n<e.length;n++)t=this.findFormat(e[n]),t&&r.name(t)==this.tag&&i.push(t);if(i.length<1)return!1;if(i.length!=e.length)return!1;for(n=0;n<i.length&&i[n].parentNode==t.parentNode;n++)if(i[n]!=t)return!1;return!0},findSuitable:function(e){var t=this.findFormat(e[0]);return t&&r.name(t)==this.tag?t:null}}),h=n.extend({init:function(e,t){var n=this;n.finder=new m(e),n.tag=e,n.unwrapTag=t},isList:function(e){return r.list(e)},immutables:function(){return this.editor&&!!this.editor.options.immutables},wrap:function(t,n){var i,a,s=r.create(t.ownerDocument,"li"),l=this.immutables()?o.Immutables.immutable:e.noop;for(i=0;i<n.length;i++)if(a=n[i],r.is(a,"li"))t.appendChild(a);else if(this.isList(a))for(;a.firstChild;)t.appendChild(a.firstChild);else if(r.is(a,"td")){for(;a.firstChild;)s.appendChild(a.firstChild);t.appendChild(s),a.appendChild(t),t=t.cloneNode(!1),s=s.cloneNode(!1)}else s.appendChild(a),r.isBlock(a)&&(t.appendChild(s),l(a)||r.unwrap(a),s=s.cloneNode(!1));s.firstChild&&t.appendChild(s)},containsAny:function(e,t){for(var n=0;n<t.length;n++)if(r.isAncestorOrSelf(e,t[n]))return!0;return!1},suitable:function(e,t){if("k-marker"==e.className){var n=e.nextSibling;if(n&&r.isBlock(n))return!1;if(n=e.previousSibling,n&&r.isBlock(n))return!1}return this.containsAny(e,t)||r.isInline(e)||3==e.nodeType},_parentLists:function(t){var n=r.closestEditable(t);return e(t).parentsUntil(n,"ul,ol")},split:function(e){var t,n,i,o,s,l,d=f(e);if(d.length)for(t=r.parentOfType(d[0],["li"]),n=r.parentOfType(d[d.length-1],["li"]),e.setStartBefore(t),e.setEndAfter(n),o=0,s=d.length;o<s;o++)l=this.finder.findFormat(d[o]),l&&(i=this._parentLists(l),i.length?a.split(e,i.last()[0],!0):a.split(e,l,!0))},merge:function(e,t){for(var n,i=t.previousSibling;i&&("k-marker"==i.className||3==i.nodeType&&r.isWhitespace(i));)i=i.previousSibling;if(i&&r.name(i)==e){for(;t.firstChild;)i.appendChild(t.firstChild);r.remove(t),t=i}for(n=t.nextSibling;n&&("k-marker"==n.className||3==n.nodeType&&r.isWhitespace(n));)n=n.nextSibling;if(n&&r.name(n)==e){for(;t.lastChild;)n.insertBefore(t.lastChild,n.firstChild);r.remove(t)}},breakable:function(e){return e!=e.ownerDocument.body&&!/table|tbody|tr|td/.test(r.name(e))&&!e.attributes.contentEditable},applyOnSection:function(t,n){function i(){u.push(this)}var o,a,s,l,d=this.tag,c=r.closestSplittableParent(n),u=[],f=this.finder.findSuitable(n);for(f||(f=new m("ul"==d?"ol":"ul").findSuitable(n)),/table|tbody/.test(r.name(c))?o=e.map(n,function(e){return r.parentOfType(e,["td"])}):(o=r.significantChildNodes(c),e.grep(o,r.isBlock).length&&(o=e.grep(o,e.proxy(function(e){return this.containsAny(e,n)},this))),o.length||(o=n)),a=0;a<o.length;a++)s=o[a],l=(!f||!r.isAncestorOrSelf(f,s))&&this.suitable(s,n),l&&(f&&this.isList(s)?(e.each(s.children,i),r.remove(s)):u.push(s));for(u.length==o.length&&this.breakable(c)&&(u=[c]),f||(f=r.create(c.ownerDocument,d),r.isBlock(u[0])&&r.mergeAttributes(u[0],f),r.insertBefore(f,u[0])),this.wrap(f,u);r.isBom(f.nextSibling);)r.remove(f.nextSibling);r.is(f,d)||r.changeTag(f,d),this.merge(d,f)},apply:function(t){function n(){i&&c.push({section:i,nodes:a})}var i,a,s,l,d=0,c=[],u=t.length,f=this.immutables()?o.Immutables.immutableParent:e.noop;for(d=0;d<u;d++)l=f(t[d])||t[d],s=r.closestEditable(l,["td","body"]),i&&s==i?a.push(l):(n(),a=[l],i=s);for(n(),d=0;d<c.length;d++)this.applyOnSection(c[d].section,c[d].nodes)},unwrap:function(e){var t,n,i,o,a=e.ownerDocument.createDocumentFragment(),s=this.unwrapTag;for(n=e.firstChild;n;n=n.nextSibling){for(i=r.create(e.ownerDocument,s||"p");n.firstChild;)o=n.firstChild,r.isBlock(o)?(i.firstChild&&(a.appendChild(i),i=r.create(e.ownerDocument,s||"p")),a.appendChild(o)):i.appendChild(o);i.firstChild&&a.appendChild(i)}t=this._parentLists(e),t[0]?(r.insertAfter(a,t.last()[0]),t.last().remove()):r.insertAfter(a,e),r.remove(e)},remove:function(e){var t,n,i;for(n=0,i=e.length;n<i;n++)t=this.finder.findFormat(e[n]),t&&this.unwrap(t)},toggle:function(e){var t,n=this,i=f(e),o=e.commonAncestorContainer;i.length||(e.selectNodeContents(o),i=f(e),i.length||(t=o.ownerDocument.createTextNode(""),e.startContainer.appendChild(t),i=[t],e.selectNode(t.parentNode))),i=r.filterBy(i,r.htmlIndentSpace,!0),n.finder.isFormatted(i)?(n.split(e),n.remove(i)):n.apply(i)}}),g=l.extend({init:function(e){e.formatter=new h(e.tag),l.fn.init.call(this,e)}}),b=c.extend({init:function(e){this.options=e,c.fn.init.call(this,i(e,{finder:new m(e.tag)}))},command:function(e){return new g(i(e,{tag:this.options.tag}))}});i(o,{ListFormatFinder:m,ListFormatter:h,ListCommand:g,ListTool:b}),p("insertUnorderedList",new b({tag:"ul",template:new d({template:s.buttonTemplate,title:"Insert unordered list"})})),p("insertOrderedList",new b({tag:"ol",template:new d({template:s.buttonTemplate,title:"Insert ordered list"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/formatting.min",["editor/plugins/inlineformat.min"],e)}(function(){!function(e){function t(e){var t,o,r=l.closestEditableOfType(e,["li"]);r&&(t=new i.ListFormatter(l.name(r.parentNode)),o=n.ui.editor.W3CRange.fromNode(e),o.selectNode(r),t.toggle(o))}var n=window.kendo,i=n.ui.editor,o=i.Tool,r=i.ToolTemplate,a=i.DelayedExecutionTool,s=i.Command,l=i.Dom,d=i.EditorUtils,c=i.RangeUtils,u=d.registerTool,f=a.extend({init:function(e){var t=this;o.fn.init.call(t,n.deepExtend({},t.options,e)),t.type="kendoSelectBox",t.finder={getFormat:function(){return""}}},options:{items:[{text:"Paragraph",value:"p"},{text:"Quotation",value:"blockquote"},{text:"Heading 1",value:"h1"},{text:"Heading 2",value:"h2"},{text:"Heading 3",value:"h3"},{text:"Heading 4",value:"h4"},{text:"Heading 5",value:"h5"},{text:"Heading 6",value:"h6"}],width:110},toFormattingItem:function(e){var t,n=e.value;return n?e.tag||e.className?e:(t=n.indexOf("."),0===t?e.className=n.substring(1):t==-1?e.tag=n:(e.tag=n.substring(0,t),e.className=n.substring(t+1)),e):e},command:function(t){var n=this,o=t.value;return o=this.toFormattingItem(o),new i.FormatCommand({range:t.range,formatter:function(){var t,r=(o.tag||o.context||"span").split(","),a=[{tags:r,attr:{className:o.className||""}}];return t=e.inArray(r[0],l.inlineElements)>=0?new i.GreedyInlineFormatter(a):new i.GreedyBlockFormatter(a),t.editor=n.editor,t}})},initialize:function(e,t){var i=t.editor,r=this.options,a=r.name,s=this;s.editor=i,e.width(r.width),e.kendoSelectBox({dataTextField:"text",dataValueField:"value",dataSource:r.items||i.options[a],title:i.options.messages[a],autoSize:!0,change:function(){var e=this.dataItem();e&&o.exec(i,a,e.toJSON())},dataBound:function(){var e,t=this.dataSource.data();for(e=0;e<t.length;e++)t[e]=s.toFormattingItem(t[e])},highlightFirst:!1,template:n.template('<span unselectable="on" style="display:block;#=(data.style||"")#">#:data.text#</span>')}),e.addClass("k-decorated").closest(".k-widget").removeClass("k-"+a).find("*").addBack().attr("unselectable","on")},getFormattingValue:function(t,n){var i,o,r,a,s,l,d;for(i=0;i<t.length;i++)if(o=t[i],r=o.tag||o.context||"",a=o.className?"."+o.className:"",s=r+a,l=e(n[0]).closest(s)[0]){if(1==n.length)return o.value;for(d=1;d<n.length&&e(n[d]).closest(s)[0];d++)if(d==n.length-1)return o.value}return""},update:function(t,n){var i,o,r,s,d,c=e(t).data(this.type);if(c&&(i=c.dataSource,o=i.data(),d=l.commonAncestor.apply(null,n),d==l.closestEditable(d)||this._ancestor!=d)){for(this._ancestor=d,r=0;r<o.length;r++)s=o[r].context,o[r].visible=!s||!!e(d).closest(s).length;i.filter([{field:"visible",operator:"eq",value:!0}]),a.fn.update.call(this,t,n),c.value(this.getFormattingValue(i.view(),n)),c.wrapper.toggleClass("k-state-disabled",!i.view().length)}},destroy:function(){this._ancestor=null}}),p=s.extend({exec:function(){var e,t,n,i=this.lockRange(!0);for(this.tagsToClean=this.options.remove||"strong,em,span,sup,sub,del,b,i,u,font".split(","),c.wrapSelectedElements(i),e=c.mapAll(i,function(e){return e}),t=e.length-1;t>=0;t--)n=e[t],this.immutableParent(n)||this.clean(n);this.releaseRange(i)},clean:function(n){var o,r,a,s,d;if(n&&!l.isMarker(n)){if(o=l.name(n),"ul"==o||"ol"==o)for(r=new i.ListFormatter(o),a=n.previousSibling,s=n.nextSibling,r.unwrap(n);a&&a!=s;a=a.nextSibling)this.clean(a);else if("blockquote"==o)l.changeTag(n,"p");else if(1!=n.nodeType||l.insignificant(n))t(n);else{for(d=n.childNodes.length-1;d>=0;d--)this.clean(n.childNodes[d]);n.removeAttribute("style"),n.removeAttribute("class")}e.inArray(o,this.tagsToClean)>-1&&l.unwrap(n)}},immutableParent:function(e){return this.immutables()&&i.Immutables.immutableParent(e)}});e.extend(i,{FormattingTool:f,CleanFormatCommand:p}),u("formatting",new f({template:new r({template:d.dropDownListTemplate,title:"Format"})})),u("cleanFormatting",new o({command:p,template:new r({template:d.buttonTemplate,title:"Clean formatting"
})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/image.min",["kendo.imagebrowser.min","editor/command.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=n.ui.editor,r=o.EditorUtils,a=o.Dom,s=r.registerTool,l=o.ToolTemplate,d=o.RangeUtils,c=o.Command,u=n.keys,f="#k-editor-image-url",p="#k-editor-image-title",m="#k-editor-image-width",h="#k-editor-image-height",g=c.extend({init:function(e){var t=this;c.fn.init.call(t,e),t.async=!0,t.attributes={}},insertImage:function(e,t){var n,i=this.attributes,o=d.documentFromRange(t);if(i.src&&"http://"!=i.src){if(n=function(){setTimeout(function(){i.width||e.removeAttribute("width"),i.height||e.removeAttribute("height"),e.removeAttribute("complete")})},!e)return e=a.create(o,"img",i),e.onload=e.onerror=n,t.deleteContents(),t.insertNode(e),e.nextSibling||a.insertAfter(o.createTextNode("\ufeff"),e),n(),t.setStartAfter(e),t.setEndAfter(e),d.selectRange(t),!0;e.onload=e.onerror=n,a.attr(e,i),n()}return!1},_dialogTemplate:function(e){return n.template('<div class="k-editor-dialog k-popup-edit-form"><div class="k-edit-form-container"><div class="k-edit-form-content"># if (showBrowser) { #<div class="k-filebrowser k-imagebrowser"></div># } #<div class=\'k-edit-label\'><label for="k-editor-image-url">#: messages.imageWebAddress #</label></div><div class=\'k-edit-field\'><input type="text" class="k-textbox" id="k-editor-image-url"></div><div class=\'k-edit-label\'><label for="k-editor-image-title">#: messages.imageAltText #</label></div><div class=\'k-edit-field\'><input type="text" class="k-textbox" id="k-editor-image-title"></div><div class=\'k-edit-label\'><label for="k-editor-image-width">#: messages.imageWidth #</label></div><div class=\'k-edit-field\'><input type="text" class="k-textbox" id="k-editor-image-width"></div><div class=\'k-edit-label\'><label for="k-editor-image-height">#: messages.imageHeight #</label></div><div class=\'k-edit-field\'><input type="text" class="k-textbox" id="k-editor-image-height"></div></div><div class="k-edit-buttons k-state-default"><button class="k-dialog-insert k-button k-primary">#: messages.dialogInsert #</button><button class="k-dialog-close k-button">#: messages.dialogCancel #</button></div></div></div>')({messages:this.editor.options.messages,showBrowser:e})},redo:function(){var e=this,t=e.lockRange();e.insertImage(d.image(t),t)||e.releaseRange(t)},exec:function(){function e(e){var t=s.element,n=parseInt(t.find(m).val(),10),i=parseInt(t.find(h).val(),10);g.attributes={src:t.find(f).val().replace(/ /g,"%20"),alt:t.find(p).val()},g.attributes.width=null,g.attributes.height=null,!isNaN(n)&&n>0&&(g.attributes.width=n),!isNaN(i)&&i>0&&(g.attributes.height=i),v=g.insertImage(k,b),o(e),g.change&&g.change()}function o(e){e.preventDefault(),s.destroy(),a.windowFromDocument(d.documentFromRange(b)).focus(),v||g.releaseRange(b)}function r(t){t.keyCode==u.ENTER?e(t):t.keyCode==u.ESC&&o(t)}var s,l,c,g=this,b=g.lockRange(),v=!1,k=d.image(b),y=k&&k.getAttribute("width")||"",w=k&&k.getAttribute("height")||"",x=n.support.browser.msie,C=g.editor.options,T=C.messages,_=C.imageBrowser,N=!!(n.ui.ImageBrowser&&_&&_.transport&&_.transport.read!==t),R={title:T.insertImage,visible:!1,resizable:N};this.expandImmutablesIn(b),R.close=o,N&&(R.width=750),s=this.createDialog(g._dialogTemplate(N),R).toggleClass("k-filebrowser-dialog",N).find(".k-dialog-insert").click(e).end().find(".k-dialog-close").click(o).end().find(".k-edit-field input").keydown(r).end().find(f).val(k?k.getAttribute("src",2):"http://").end().find(p).val(k?k.alt:"").end().find(m).val(y).end().find(h).val(w).end().data("kendoWindow"),l=s.element,N&&(this._imageBrowser=new n.ui.ImageBrowser(l.find(".k-imagebrowser"),i({},_)),this._imageBrowser.bind("change",function(e){"f"===e.selected.get("type")&&l.find(f).val(this.value())}),this._imageBrowser.bind("apply",e)),x&&(c=l.closest(".k-window").height(),l.css("max-height",c)),s.center().open(),l.find(f).focus().select()}});n.ui.editor.ImageCommand=g,s("insertImage",new o.Tool({command:g,template:new l({template:r.buttonTemplate,title:"Insert Image"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/import.min",["editor/main.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=e.proxy,r=n.ui.editor,a=r.EditorUtils,s=r.Command,l=r.Tool,d=a.registerTool,c=r.ToolTemplate,u='<div contenteditable="false" class="k-loading-mask" style="width: 100%; height: 100%; position: absolute; top: 0px; left: 0px;"><div class="k-loading-image"></div><div class="k-loading-color"></div></div>',f=s.extend({exec:function(){(this.editor._uploadWidget||this._initializeUploadWidget()).element.click()},_initializeUploadWidget:function(){var t=this,n=t.editor,i=n.options["import"],r=e('<input id="editorImport" name="files" type="file" />').kendoUpload({success:o(t._onUploadSuccess,t),progress:o(t._onUploadProgress,t),select:o(t._onUploadSelect,t),error:o(t._onUploadError,t),complete:o(t._onUploadComplete,t),showFileList:!1,multiple:!1,async:{saveUrl:i.proxyUrl,autoUpload:!0,saveField:"file"},validation:{allowedExtensions:i.allowedExtensions,maxFileSize:i.maxFileSize}}).getKendoUpload();return n._uploadWidget=r,r},_onUploadComplete:function(e){this._trigger("complete",e),e.sender.clearAllFiles(),this._removeLoadingOverlay()},_onUploadSuccess:function(e){this.editor.value(e.response.html.replace(/<\/?body>/gi,"")),this._trigger("success",e)},_onUploadProgress:function(e){this._trigger("progress",e)},_onUploadSelect:function(e){this._trigger("select",e),e.files[0].validationErrors||this._initLoadingOverlay()},_onUploadError:function(e){this._trigger("error",e)},_trigger:function(e,t){var n=this.editor,i=n.options["import"];"function"==typeof i[e]&&i[e].call(n,t)},_initLoadingOverlay:function(){var t=this.editor.body;r.Dom.is(t,"body")?this._iframeWrapper=this._container=this.editor.wrapper.find("iframe").parent().css({position:"relative"}).append(u):this._container=e(t).append(u),n.ui.progress(this._container,!0)},_removeLoadingOverlay:function(){n.ui.progress(this._container,!1),e(this._iframeWrapper).css({position:""}),delete this._container,delete this._iframeWrapper}});i(r,{ImportCommand:f}),d("import",new l({command:f,template:new c({template:a.buttonTemplate,title:"Import"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/insert.min",["editor/command.min"],e)}(function(){!function(e){var t=window.kendo,n=t.ui.editor,i=n.Command,o=n.GenericCommand,r=n.EditorUtils,a=r.registerTool,s=n.Tool,l=n.ToolTemplate,d=n.RestorePoint,c=e.extend,u=i.extend({init:function(e){i.fn.init.call(this,e),this.managesUndoRedo=!0},exec:function(){var e,t=this.editor,n=this.options,i=n.range,r=t.body,a=new d(i,r),s=n.html||n.value||"";t.selectRange(i),t.clipboard.paste(s,n),n.postProcess&&n.postProcess(t,t.getRange()),e=new o(a,new d(t.getRange(),r)),e.editor=t,t.undoRedoStack.push(e),t.focus()}}),f=s.extend({initialize:function(e,t){var i=t.editor,o=this.options,r=o.items?o.items:i.options.insertHtml;this._selectBox=new n.SelectBox(e,{dataSource:r,dataTextField:"text",dataValueField:"value",change:function(){s.exec(i,"insertHtml",this.value())},title:i.options.messages.insertHtml,highlightFirst:!1})},command:function(e){return new u(e)},update:function(e){var t=e.data("kendoSelectBox")||e.find("select").data("kendoSelectBox");t.close(),t.value(t.options.title)}});c(n,{InsertHtmlCommand:u,InsertHtmlTool:f}),a("insertHtml",new f({template:new l({template:r.dropDownListTemplate,title:"Insert HTML",initialValue:"Insert HTML"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/export.min",["editor/main.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=e.proxy,r=n.ui.editor,a=r.EditorUtils,s=r.Command,l=r.Tool,d=a.registerTool,c=r.ToolTemplate,u=[{text:"Docx",value:"docx"},{text:"Rtf",value:"rtf"},{text:"Pdf",value:"pdf"},{text:"Html",value:"html"},{text:"Plain Text",value:"txt"}],f=s.extend({init:function(e){var t=this;t.options=e,s.fn.init.call(t,e),t.attributes=null,t.exportType=e.exportType},exec:function(){var e=this,t=this.lockRange(!0);e.postToProxy(),e.releaseRange(t)},postToProxy:function(){this.generateForm().appendTo("body").submit().remove()},generateForm:function(){var t=this,n=t.editor.options.exportAs,i=e("<form>").attr({action:n&&n.proxyURL||"",method:"POST"});return i.append([t.valueInput(),t.exportTypeInput(),t.fileNameInput()]),i},valueInput:function(){var t=this.editor;return e("<input>").attr({value:t.encodedValue(),name:"value",type:"hidden"})},exportTypeInput:function(){var t=this;return e("<input>").attr({value:t.exportType,name:"exportType",type:"hidden"})},fileNameInput:function(){var t=this.editor,n=t.options.exportAs,i=n&&n.fileName||t.element.attr("id")||"editor";return e("<input>").attr({value:i,name:"fileName",type:"hidden"})}}),p=l.extend({init:function(e){var t=this;l.fn.init.call(t,n.deepExtend({},t.options,e)),t.type="kendoSelectBox"},options:{items:u,width:115},command:function(e){var t=e.value;return new r.ExportAsCommand({range:e.range,exportType:t.exportType})},initialize:function(e,t){var i=this,r=t.editor,a=i.options,s=a.name,l=o(i.changeHandler,i),d=a.items||r.options[s];d.unshift({text:r.options.messages[s],value:""}),i.editor=r,e.width(a.width),e.kendoSelectBox({dataTextField:"text",dataValueField:"value",dataSource:d,autoSize:!0,change:l,open:function(e){var t=e.sender;t.items()[0].style.display="none",t.unbind("open")},highlightFirst:!1,template:n.template('<span unselectable="on" style="display:block;#=(data.style||"")#">#:data.text#</span>')}),e.addClass("k-decorated").closest(".k-widget").removeClass("k-"+s).find("*").addBack().attr("unselectable","on")},changeHandler:function(e){var t=e.sender,n=t.dataItem(),i=n&&n.value;this._exec(i),t.value("")},_exec:function(e){e&&l.exec(this.editor,this.options.name,{exportType:e})},destroy:function(){this._ancestor=null}});i(r,{ExportAsTool:p,ExportAsCommand:f}),d("exportAs",new p({template:new c({template:a.dropDownListTemplate,title:"Export As"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/indent.min",["editor/plugins/formatblock.min"],e)}(function(){!function(e,t){function n(n,i){var o="rtl"==e(n).css("direction"),r=o?"Right":"Left",a="td"!=s.name(n)?"margin"+r:"padding"+r;return i===t?n.style[a]||0:(i>0?n.style[a]=i+"px":(n.style[a]="",n.style.cssText||n.removeAttribute("style")),t)}var i=window.kendo,o=i.Class,r=e.extend,a=i.ui.editor,s=a.Dom,l=a.EditorUtils,d=l.registerTool,c=a.Command,u=a.Tool,f=a.ToolTemplate,p=a.RangeUtils,m=s.blockElements,h=a.BlockFormatFinder,g=a.BlockFormatter,b=o.extend({init:function(){this.finder=new h([{tags:s.blockElements}])},apply:function(t){var i,o,r,a,l,d,c,u,f,p,m,h;if(t=s.filterBy(t,s.htmlIndentSpace,!0),i=this.finder.findSuitable(t),o=[],i=this.mapImmutables(i),i.length){for(r=0,a=i.length;r<a;r++)s.is(i[r],"li")?e(i[r]).index()?e.inArray(i[r].parentNode,o)<0&&o.push(i[r]):o.push(i[r].parentNode):o.push(i[r]);for(;o.length;)if(l=o.shift(),s.is(l,"li"))if(d=l.parentNode,c=e(l).prev("li"),u=c.find("ul,ol").last(),f=e(l).children("ul,ol")[0],f&&c[0])u[0]?(u.append(l),u.append(e(f).children()),s.remove(f)):(c.append(f),f.insertBefore(l,f.firstChild));else for(f=c.children("ul,ol")[0],f||(f=s.create(l.ownerDocument,s.name(d)),c.append(f));l&&l.parentNode==d;)f.appendChild(l),l=o.shift();else for(p=parseInt(n(l),10)+30,n(l,p),m=0;m<o.length;m++)e.contains(l,o[m])&&o.splice(m,1)}else h=new g([{tags:["p"]}],{style:{marginLeft:30}}),h.apply(t)},mapImmutables:function(t){if(this.immutables){var n=[];return e.map(t,function(t){var i=a.Immutables.immutableParent(t);if(i){if(e.inArray(i,n)!==-1)return null;n.push(i)}return i||t})}return t},remove:function(t){t=s.filterBy(t,s.htmlIndentSpace,!0);var i,o,r,a,l,d,c,u,f=this.finder.findSuitable(t);for(f=this.mapImmutables(f),o=0,r=f.length;o<r;o++){if(c=e(f[o]),c.is("li")){if(a=c.parent(),l=a.parent(),l.is("li,ul,ol")&&!n(a[0])){if(i&&e.contains(i,l[0]))continue;d=c.nextAll("li"),d.length&&e(a[0].cloneNode(!1)).appendTo(c).append(d),l.is("li")?c.insertAfter(l):c.appendTo(l),a.children("li").length||a.remove();continue}if(i==a[0])continue;i=a[0]}else i=f[o];u=parseInt(n(i),10)-30,n(i,u)}}}),v=c.extend({init:function(t){var n=this;t.formatter={toggle:e.proxy(function(e){var t=new b;t.immutables=this.editor&&this.editor.options.immutables,t.apply(p.nodes(e))},n)},c.fn.init.call(this,t)}}),k=c.extend({init:function(t){var n=this;t.formatter={toggle:e.proxy(function(e){var t=new b;t.immutables=this.editor&&this.editor.options.immutables,t.remove(p.nodes(e))},n)},c.fn.init.call(this,t)}}),y=u.extend({init:function(e){u.fn.init.call(this,e),this.finder=new h([{tags:m}])},initialize:function(t,n){u.fn.initialize.call(this,t,n),e.extend(this.options,{immutables:n.editor&&n.editor.options.immutables}),t.addClass("k-state-disabled")},update:function(i,o){var r,l,d,c,u,f,p=this.finder.findSuitable(o);for(d=0,c=p.length;d<c;d++)if(u=p[d],this.options.immutables&&(f=a.Immutables.immutableParent(u),f&&(u=f)),r=n(u),r||(l=e(u).parents("ul,ol").length,r=s.is(u,"li")&&(l>1||n(u.parentNode))||s.ofType(u,["ul","ol"])&&l>0),r)return i.removeClass("k-state-disabled"),t;i.addClass("k-state-disabled").removeClass("k-state-hover")}});r(a,{IndentFormatter:b,IndentCommand:v,OutdentCommand:k,OutdentTool:y}),d("indent",new u({command:v,template:new f({template:l.buttonTemplate,title:"Indent"})})),d("outdent",new y({command:k,template:new f({template:l.buttonTemplate,title:"Outdent"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/linebreak.min",["editor/plugins/formatblock.min"],e)}(function(){!function(e){var t=window.kendo,n=e.extend,i=t.ui.editor,o=i.Dom,r=i.Command,a=i.Tool,s=i.BlockFormatter,l=o.normalize,d=i.RangeUtils,c=i.EditorUtils.registerTool,u=r.extend({init:function(e){this.options=e,r.fn.init.call(this,e)},_insertMarker:function(e,t){var n,i=o.create(e,"a");return i.className="k-marker",t.insertNode(i),i.parentNode||(n=t.commonAncestorContainer,n.innerHTML="",n.appendChild(i)),l(i.parentNode),i},_moveFocus:function(e,t){var n,i;if(o.isEmpty(t))e.setStartBefore(t);else{if(e.selectNodeContents(t),n=d.textNodes(e)[0],!n){for(;t.childNodes.length&&!o.is(t.firstChild,"br");)t=t.firstChild;n=t}o.isEmpty(n)?e.setStartBefore(n):(o.emptyNode(n)&&(n.innerHTML="\ufeff"),i=n.firstChild||n,o.isDataNode(i)?e.setStart(i,0):e.setStartBefore(i))}},shouldTrim:function(e){var t="p,h1,h2,h3,h4,h5,h6".split(","),n=o.parentOfType(e.startContainer,t),i=o.parentOfType(e.endContainer,t);return n&&!i||!n&&i},_blankAfter:function(e){for(;e&&(o.isMarker(e)||""===o.stripBom(e.nodeValue));)e=e.nextSibling;return!e},exec:function(){var t,n,r,a,c,u,f,p,m,h,g,b,v,k,y=this.getRange(),w=d.documentFromRange(y),x=i.emptyElementContent;this.expandImmutablesIn(y),h=this.shouldTrim(y),y.deleteContents(),c=this._insertMarker(w,y),o.stripBomNode(c.previousSibling),o.stripBomNode(c.nextSibling),u=o.closestEditableOfType(c,["li"]),f=o.closestEditableOfType(c,"h1,h2,h3,h4,h5,h6".split(",")),p=o.is(c.parentNode,"table")&&c.parentNode,u?o.emptyNode(u)&&(a=o.create(w,"p"),o.next(u)&&(m=y.cloneRange(),m.selectNode(u),d.split(m,u.parentNode)),g=e("br",u),1==g.length&&g.remove(),b=u.parentNode,v=u.parentNode.children.length,k=v>1&&1==u.childNodes.length&&u.children[0],o.insertAfter(a,b),o.remove(1==v?u.parentNode:u),k&&k!==c?(a.appendChild(k),a.appendChild(c)):a.innerHTML=x,r=a):f&&this._blankAfter(c)?(a=this._insertParagraphAfter(f),o.remove(c),r=a):p&&(a=this._insertParagraphAfter(p),o.remove(c),r=a),r||(u||f||new s([{tags:["p"]}]).apply([c]),y.selectNode(c),t=o.parentOfType(c,[u?"li":f?o.name(f):"p"]),d.split(y,t,h),n=t.previousSibling,o.is(n,"li")&&n.firstChild&&!o.is(n.firstChild,"br")&&(n=n.firstChild),r=t.nextSibling,this.clean(n,{links:!0}),this.clean(r,{links:!0}),o.is(r,"li")&&r.firstChild&&!o.is(r.firstChild,"br")&&(r=r.firstChild),o.remove(t),l(n)),l(r),this._moveFocus(y,r),y.collapse(!0),o.scrollTo(r,!0),d.selectRange(y)},_insertParagraphAfter:function(e){var t=this.getRange(),n=d.documentFromRange(t),r=i.emptyElementContent,a=o.create(n,"p");return o.insertAfter(a,e),a.innerHTML=r,a},clean:function(t,n){var r,a=t;if(t.firstChild&&o.is(t.firstChild,"br")&&o.remove(t.firstChild),o.isDataNode(t)&&!t.nodeValue&&(t=t.parentNode),t){for(r=!1;t.firstChild&&1==t.firstChild.nodeType;)r=r||o.significantNodes(t.childNodes).length>1,t=t.firstChild;if(o.isEmpty(t)||!/^\s*$/.test(t.innerHTML)||r||(e(a).find(".k-br").remove(),t.innerHTML=i.emptyElementContent),n&&n.links)for(;t!=a;){if(o.is(t,"a")&&o.emptyNode(t)){o.unwrap(t);break}t=t.parentNode}}}}),f=r.extend({init:function(e){this.options=e,r.fn.init.call(this,e)},exec:function(){var e,n,i,r,a,s,c=this.getRange();this.expandImmutablesIn(c),e=o.create(d.documentFromRange(c),"br"),n=c.startContainer,r=t.support.browser,a=r.msie&&r.version<11,s=o.is(n,"table")&&n,c.deleteContents(),s?o.insertAfter(e,s):c.insertNode(e),l(e.parentNode),a||e.nextSibling&&!o.isWhitespace(e.nextSibling)||(i=e.cloneNode(!0),i.className="k-br",o.insertAfter(i,e)),c.setStartAfter(e),c.collapse(!0),o.scrollTo(e.nextSibling||e,!0),d.selectRange(c)}});n(i,{ParagraphCommand:u,NewLineCommand:f}),c("insertLineBreak",new a({key:13,shift:!0,command:f})),c("insertParagraph",new a({key:13,command:u}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/file.min",["kendo.filebrowser.min","editor/plugins/link.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=n.ui.editor,r=o.EditorUtils,a=o.Dom,s=r.registerTool,l=o.ToolTemplate,d=o.RangeUtils,c=o.Command,u=o.LinkFormatter,f=d.textNodes,p=n.keys,m="#k-editor-file-url",h="#k-editor-file-text",g="#k-editor-file-title",b=c.extend({init:function(e){var t=this;c.fn.init.call(t,e),t.formatter=new u,t.async=!0,t.attributes={}},insertFile:function(e,t){var n=this.attributes,i=d.documentFromRange(t);if(n.href&&"http://"!=n.href){if(!e)return e=a.create(i,"a",{href:n.href}),e.innerHTML=n.innerHTML,e.title=n.title,t.deleteContents(),t.insertNode(e),e.nextSibling||a.insertAfter(i.createTextNode("\ufeff"),e),t.setStartAfter(e),t.setEndAfter(e),d.selectRange(t),!0;a.attr(e,n)}return!1},_dialogTemplate:function(e){return n.template('<div class="k-editor-dialog k-popup-edit-form"><div class="k-edit-form-container"><div class="k-edit-form-content"># if (showBrowser) { #<div class="k-filebrowser"></div># } #<div class=\'k-edit-label\'><label for="k-editor-file-url">#: messages.fileWebAddress #</label></div><div class=\'k-edit-field\'><input type="text" class="k-textbox" id="k-editor-file-url"></div><div class=\'k-edit-label\'><label for="k-editor-file-text">#: messages.fileText #</label></div><div class=\'k-edit-field\'><input type="text" class="k-textbox" id="k-editor-file-text"></div><div class=\'k-edit-label\'><label for="k-editor-file-title">#: messages.fileTitle #</label></div><div class=\'k-edit-field\'><input type="text" class="k-textbox" id="k-editor-file-title"></div></div><div class="k-edit-buttons k-state-default"><button class="k-dialog-insert k-button k-primary">#: messages.dialogInsert #</button><button class="k-dialog-close k-button">#: messages.dialogCancel #</button></div></div></div>')({messages:this.editor.options.messages,showBrowser:e})},redo:function(){var e=this,t=e.lockRange();this.formatter.apply(t,this.attributes),e.releaseRange(t)},exec:function(){function e(e){var t=s.element,n=t.find(m).val().replace(/ /g,"%20"),i=t.find(h).val(),r=t.find(g).val();u.attributes={href:n,innerHTML:""!==i?i:n,title:r},k=u.insertFile(y,b),o(e),u.change&&u.change()}function o(e){e.preventDefault(),s.destroy(),a.windowFromDocument(d.documentFromRange(b)).focus(),k||u.releaseRange(b)}function r(t){t.keyCode==p.ENTER?e(t):t.keyCode==p.ESC&&o(t)}var s,l,c,u=this,b=u.lockRange(),v=f(b),k=!1,y=v.length?this.formatter.finder.findSuitable(v[0]):null,w=n.support.browser.msie,x=u.editor.options,C=x.messages,T=x.fileBrowser,_=!!(n.ui.FileBrowser&&T&&T.transport&&T.transport.read!==t),N={title:C.insertFile,visible:!1,resizable:_};this.expandImmutablesIn(b),N.close=o,_&&(N.width=750),s=this.createDialog(u._dialogTemplate(_),N).toggleClass("k-filebrowser-dialog",_).find(".k-dialog-insert").click(e).end().find(".k-dialog-close").click(o).end().find(".k-edit-field input").keydown(r).end().find(m).val(y?y.getAttribute("href",2):"http://").end().find(h).val(y?y.innerText:"").end().find(g).val(y?y.title:"").end().data("kendoWindow"),l=s.element,_&&(u._fileBrowser=new n.ui.FileBrowser(l.find(".k-filebrowser"),i({},T)),u._fileBrowser.bind("change",function(e){"f"===e.selected.get("type")&&l.find(m).val(this.value())}),u._fileBrowser.bind("apply",e)),w&&(c=l.closest(".k-window").height(),l.css("max-height",c)),s.center().open(),l.find(m).focus().select()}});n.ui.editor.FileCommand=b,s("insertFile",new o.Tool({command:b,template:new l({template:r.buttonTemplate,title:"Insert File"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/tables.min",["editor/plugins/formatblock.min","editor/plugins/insert.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=e.proxy,r=n.ui.editor,a=r.Dom,s=r.EditorUtils,l=r.RangeUtils,d=r.Command,c="kendoEditor",u="k-state-active",f="k-state-selected",p=r.Tool,m=r.ToolTemplate,h=r.InsertHtmlCommand,g=r.BlockFormatFinder,b=r.EditorUtils.registerTool,v=n.getTouches,k=n.template,y="<td style='width:#=width#%;'>#=content#</td>",w=new g([{tags:["table"]}]),x=h.extend({init:function(t){var n=e.extend({postProcess:this.postProcess,skipCleaners:!0},t||{});h.fn.init.call(this,n)},_tableHtml:function(e,t){var n,i;return e=e||1,t=t||1,n=k(y)({width:100/t,content:r.emptyTableCellContent}),i=100/e,"<table class='k-table' data-last>"+Array(e+1).join("<tr style='height:"+i+"%;'>"+Array(t+1).join(n)+"</tr>")+"</table>"},postProcess:function(t,n){var i=e("table[data-last]",t.document).removeAttr("data-last");n.setStart(i.find("td")[0],0),n.collapse(!0),t.selectRange(n)},exec:function(){var e=this.options;e.html=this._tableHtml(e.rows,e.columns),h.fn.exec.call(this)}}),C=p.extend({initialize:function(t,n){var i,a,l,d;p.fn.initialize.call(this,t,n),i=e(this.options.popupTemplate).appendTo("body").kendoPopup({anchor:t,copyAnchorStyles:!1,open:o(this._open,this),activate:o(this._activate,this),close:o(this._close,this)}).data("kendoPopup"),t.click(o(this._toggle,this)).keydown(o(this._keydown,this)),a=this._editor=n.editor,this._popup=i,l=new r.TableWizardTool({template:new m({template:s.buttonTemplate,title:a.options.messages.tableWizard}),command:r.TableWizardCommand,insertNewTable:!0}),b("tableWizardInsert",l),d=e("<div class='k-editor-toolbar'>"+l.options.template.getHtml()+"</div>"),d.appendTo(i.element),a.toolbar&&a.toolbar.attachToolsEvents(d)},popup:function(){return this._popup},_activate:e.noop,_open:function(){this._popup.options.anchor.addClass(u)},_close:function(){this._popup.options.anchor.removeClass(u)},_keydown:function(e){var t=n.keys,i=e.keyCode;i==t.DOWN&&e.altKey?this._popup.open():i==t.ESC&&this._popup.close()},_toggle:function(t){var n=e(t.target).closest(".k-tool");n.hasClass("k-state-disabled")||this.popup().toggle()},update:function(e){var t=this.popup();t.wrapper&&"block"==t.wrapper.css("display")&&t.close(),e.removeClass("k-state-hover")},destroy:function(){this._popup.destroy()}}),T=C.extend({init:function(t){this.cols=8,this.rows=6,C.fn.init.call(this,e.extend(t,{command:x,popupTemplate:"<div class='k-ct-popup'>"+Array(this.cols*this.rows+1).join("<span class='k-ct-cell k-state-disabled' />")+"<div class='k-status'></div></div>"}))},_activate:function(){function t(t){var n=e(window);return{row:Math.floor((t.clientY+n.scrollTop()-u.top)/o)+1,col:Math.floor((t.clientX+n.scrollLeft()-u.left)/i)+1}}var i,o,r=this,a=r._popup.element,s=a.find(".k-ct-cell"),l=s.eq(0),d=s.eq(s.length-1),u=n.getOffset(l),f=n.getOffset(d),p=r.cols,m=r.rows;a.find("*").addBack().attr("unselectable","on"),f.left+=d[0].offsetWidth,f.top+=d[0].offsetHeight,i=(f.left-u.left)/p,o=(f.top-u.top)/m,a.autoApplyNS(c).on("mousemove",".k-ct-cell",function(e){r._setTableSize(t(e))}).on("mouseleave",".k-ct-cell",function(){r._setTableSize()}).on("down",".k-ct-cell",function(e){e.preventDefault();var n=v(e)[0];r._exec(t(n.location))})},_valid:function(e){return e&&e.row>0&&e.col>0&&e.row<=this.rows&&e.col<=this.cols},_exec:function(e){this._valid(e)&&(this._editor.exec("createTable",{rows:e.row,columns:e.col}),this._popup.close())},_setTableSize:function(t){var i=this._popup.element,o=i.find(".k-status"),r=i.find(".k-ct-cell"),a=this.cols,s=this._editor.options.messages;this._valid(t)?(o.text(n.format(s.createTableHint,t.row,t.col)),r.each(function(n){e(this).toggleClass(f,n%a<t.col&&n/a<t.row)})):(o.text(s.createTable),r.removeClass(f))},_keydown:function(e){var t,i,o,r,a,s,l,d;C.fn._keydown.call(this,e),this._popup.visible()&&(t=n.keys,i=e.keyCode,o=this._popup.element.find(".k-ct-cell"),r=Math.max(o.filter(".k-state-selected").last().index(),0),a=Math.floor(r/this.cols),s=r%this.cols,l=!1,i!=t.DOWN||e.altKey?i==t.UP?(l=!0,a--):i==t.RIGHT?(l=!0,s++):i==t.LEFT&&(l=!0,s--):(l=!0,a++),d={row:Math.max(1,Math.min(this.rows,a+1)),col:Math.max(1,Math.min(this.cols,s+1))},i==t.ENTER?this._exec(d):this._setTableSize(d),l&&(e.preventDefault(),e.stopImmediatePropagation()))},_open:function(){var e=this._editor.options.messages;C.fn._open.call(this),this.popup().element.find(".k-status").text(e.createTable).end().find(".k-ct-cell").removeClass(f)},_close:function(){C.fn._close.call(this),this.popup().element.off("."+c)}}),_=d.extend({exec:function(){for(var e,t,n,i,o=this.lockRange(!0),s=o.endContainer;"td"!=a.name(s);)s=s.parentNode;if(!this.immutables()||!r.Immutables.immutableParent(s)){for(t=s.parentNode,e=t.children.length,n=t.cloneNode(!0),i=0;i<t.cells.length;i++)n.cells[i].innerHTML=r.emptyTableCellContent;"before"==this.options.position?a.insertBefore(n,t):a.insertAfter(n,t),this.releaseRange(o)}}}),N=d.extend({exec:function(){var e,t,n,i,o=this.lockRange(!0),s=a.closest(o.endContainer,"td"),l=a.closest(s,"table"),d=l.rows,c=this.options.position;if(!this.immutables()||!r.Immutables.immutableParent(s)){for(e=a.findNodeIndex(s,!0),t=0;t<d.length;t++)n=d[t].cells[e],i=n.cloneNode(),i.innerHTML=r.emptyTableCellContent,"before"==c?a.insertBefore(i,n):a.insertAfter(i,n);this.releaseRange(o)}}}),R=d.extend({exec:function(){var t,n,i,o=this.lockRange(),s=l.mapAll(o,function(t){return e(t).closest("tr")[0]}),d=s[0];if(!this.immutables()||!r.Immutables.immutableParent(d)){if(t=a.closest(d,"table"),t.rows.length<=s.length)n=a.next(t),n&&!a.insignificant(n)||(n=a.prev(t)),a.remove(t);else for(i=0;i<s.length;i++)d=s[i],a.removeTextSiblings(d),n=a.next(d)||a.prev(d),n=n.cells[0],a.remove(d);n&&(o.setStart(n,0),o.collapse(!0),this.editor.selectRange(o))}}}),S=d.extend({exec:function(){var e,t,n=this.lockRange(),i=a.closest(n.endContainer,"td"),o=a.closest(i,"table"),s=o.rows,l=a.findNodeIndex(i,!0),d=s[0].cells.length;if(!this.immutables()||!r.Immutables.immutableParent(i)){if(1==d)e=a.next(o),e&&!a.insignificant(e)||(e=a.prev(o)),a.remove(o);else for(a.removeTextSiblings(i),e=a.next(i)||a.prev(i),t=0;t<s.length;t++)a.remove(s[t].cells[l]);e&&(n.setStart(e,0),n.collapse(!0),this.editor.selectRange(n))}}}),z=p.extend({command:function(e){return e=i(e,this.options),"delete"==e.action?"row"==e.type?new R(e):new S(e):"row"==e.type?new _(e):new N(e)},initialize:function(e,t){p.fn.initialize.call(this,e,t),e.addClass("k-state-disabled")},update:function(e,t){var n=!w.isFormatted(t);e.toggleClass("k-state-disabled",n)}});i(n.ui.editor,{PopupTool:C,TableCommand:x,InsertTableTool:T,TableModificationTool:z,InsertRowCommand:_,InsertColumnCommand:N,DeleteRowCommand:R,DeleteColumnCommand:S}),b("createTable",new T({template:new m({template:s.buttonTemplate,popup:!0,title:"Create table"})})),b("addColumnLeft",new z({type:"column",position:"before",template:new m({template:s.buttonTemplate,title:"Add column on the left"})})),b("addColumnRight",new z({type:"column",template:new m({template:s.buttonTemplate,title:"Add column on the right"})})),b("addRowAbove",new z({type:"row",position:"before",template:new m({template:s.buttonTemplate,title:"Add row above"})})),b("addRowBelow",new z({type:"row",template:new m({template:s.buttonTemplate,title:"Add row below"})})),b("deleteRow",new z({type:"row",action:"delete",template:new m({template:s.buttonTemplate,title:"Delete row"})})),b("deleteColumn",new z({type:"column",action:"delete",template:new m({template:s.buttonTemplate,title:"Delete column"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/clipboard.min",["editor/command.min"],e)}(function(){!function(e){var t=window.kendo,n=t.Class,i=t.ui.editor,o=i.RangeUtils,r=i.Dom,a=i.RestorePoint,s=i.Marker,l=t.support.browser,d=e.extend,c=n.extend({init:function(e){this.editor=e;var t=e.options.pasteCleanup;this.cleaners=[new f(t),new p(t),new m(t),new h(t),new b(t),new v(t),new x(t),new C(t)]},htmlToFragment:function(e){var t=this.editor,n=t.document,i=r.create(n,"div"),o=n.createDocumentFragment();for(i.innerHTML=e;i.firstChild;)o.appendChild(i.firstChild);return o},isBlock:function(e){return/<(div|p|ul|ol|table|h[1-6])/i.test(e)},_startModification:function(){var e,t,n=this.editor;if(!this._inProgress)return this._inProgress=!0,e=n.getRange(),t=new a(e,n.body),r.persistScrollTop(n.document),{range:e,restorePoint:t}},_endModification:function(e){i._finishUpdate(this.editor,e.restorePoint),this.editor._selectionChange(),this._inProgress=!1},_contentModification:function(e,t){var n=this,i=n.editor,o=n._startModification();o&&(e.call(n,i,o.range),setTimeout(function(){t.call(n,i,o.range),n._endModification(o)}))},_removeBomNodes:function(e){var t,n=o.textNodes(e);for(t=0;t<n.length;t++)n[t].nodeValue=r.stripBom(n[t].nodeValue)||n[t].nodeValue},_onBeforeCopy:function(e){var t=new s;t.add(e),this._removeBomNodes(e),t.remove(e),this.editor.selectRange(e)},oncopy:function(){this._onBeforeCopy(this.editor.getRange())},oncut:function(){this._onBeforeCopy(this.editor.getRange()),this._contentModification(e.noop,e.noop)},_fileToDataURL:function(t){var n=e.Deferred(),i=new FileReader;return t instanceof window.File||!t.getAsFile||(t=t.getAsFile()),i.onload=e.proxy(n.resolve,n),i.readAsDataURL(t),n.promise()},_triggerPaste:function(e,t){var n={html:e||""};n.html=n.html.replace(/\ufeff/g,""),this.editor.trigger("paste",n),this.paste(n.html,t||{})},_handleImagePaste:function(e){var t,n;if("FileReader"in window&&!(l.msie&&l.version>10))return t=e.clipboardData||e.originalEvent.clipboardData||window.clipboardData||{},n=t.items||t.files,this._insertImages(n)},_insertImages:function(t){var n,i,o;if(t&&(n=e.grep(t,function(e){return/^image\//i.test(e.type)}),i=e.grep(t,function(e){return/^text\/html/i.test(e.type)}),!i.length&&n.length&&(o=this._startModification())))return e.when.apply(e,e.map(n,this._fileToDataURL)).done(e.proxy(function(){var t=Array.prototype.slice.call(arguments),n=e.map(t,function(e){return'<img src="'+e.target.result+'" />'}).join("");this._triggerPaste(n),this._endModification(o)},this)),!0},onpaste:function(n){if("false"!==this.editor.body.contentEditable){if(this._handleImagePaste(n))return void n.preventDefault();this.expandImmutablesIn(),this._contentModification(function(i,o){var a,s,l,d=r.create(i.document,"div",{className:"k-paste-container",innerHTML:"\ufeff"}),c=t.support.browser,u=i.body;
this._decoreateClipboardNode(d,u),u.appendChild(d),c.webkit&&this._moveToCaretPosition(d,o),c.msie&&c.version<11?(n.preventDefault(),a=i.createRange(),a.selectNodeContents(d),i.selectRange(a),s=i.document.body.createTextRange(),s.moveToElementText(d),e(u).unbind("paste"),s.execCommand("Paste"),e(u).bind("paste",e.proxy(this.onpaste,this))):(l=i.createRange(),l.selectNodeContents(d),i.selectRange(l)),o.deleteContents()},function(t,n){var i,o="";t.selectRange(n),i=e(t.body).children(".k-paste-container"),i.each(function(){var e=this.lastChild;e&&r.is(e,"br")&&r.remove(e),o+=this.innerHTML}),i.remove(),this._triggerPaste(o,{clean:!0})})}},ondragover:function(e){(l.msie||l.edge)&&(e.stopPropagation(),e.preventDefault())},ondrop:function(e){var t,n;"FileReader"in window&&(t=(e.originalEvent||e).dataTransfer||{},n=t.items||t.files,this._insertImages(n)&&e.preventDefault())},_decoreateClipboardNode:function(t,n){var i,o,r,a;(l.msie||l.webkit)&&(t=e(t),t.css({borderWidth:"0px",width:"0px",height:"0px",overflow:"hidden",margin:"0",padding:"0"}),l.msie&&(i=e(n.ownerDocument.documentElement),t.css({fontVariant:"normal",fontWeight:"normal",lineSpacing:"normal",lineHeight:"normal",textDecoration:"none"}),o=i.css("color"),o&&t.css("color",o),r=i.css("fontFamily"),r&&t.css("fontFamily",r),a=i.css("fontSize"),a&&t.css("fontSize",a)))},_moveToCaretPosition:function(t,n){var i=this,o=i.editor.body,a=r.offset(t,o),s=i._caretOffset(n,o),l=s.left-a.left,d=s.top-a.top,c="translate("+l+"px,"+d+"px)";e(t).css({"-webkit-transform":c,transform:c})},_caretOffset:function(e,t){var n,i,o,a,s,l,d=this.editor,c=r.create(d.document,"span",{innerHTML:"\ufeff"}),u=e.startContainer;return e.collapsed?(i=r.isDataNode(u),i&&(r.isBom(u)||0===e.startOffset)?r.insertBefore(c,u):i&&e.startOffset===u.length?r.insertAfter(c,u):(e.insertNode(c),n=!0)):(u=u===t?u.childNodes[e.startOffset]:u,r.insertBefore(c,u)),o=r.offset(c,t),a=c.previousSibling,s=c.nextSibling,r.remove(c),n&&r.isDataNode(a)&&r.isDataNode(s)&&!r.isBom(a)&&!r.isBom(s)&&(l=a.length,s.data=a.data+s.data,e.setStart(s,l),r.remove(a),e.collapse(!0),d.selectRange(e)),o},expandImmutablesIn:function(e){var n,i,o,r=this.editor;r&&r.options.immutables&&(n=r.body,e=e||r.getRange(),t.ui.editor.Immutables.expandImmutablesIn(e),e.startContainer===n&&0===e.startOffset&&(i=n.ownerDocument,o=i.createTextNode("\ufeff"),n.insertBefore(o,n.childNodes[0]),e.setStartBefore(o)),r.selectRange(e))},splittableParent:function(e,t){var n,i;if(e)return r.closestEditableOfType(t,["p","ul","ol"])||t.parentNode;if(n=t.parentNode,i=t.ownerDocument.body,r.isInline(n))for(;n.parentNode!=i&&!r.isBlock(n.parentNode);)n=n.parentNode;return n},paste:function(t,n){var i,a,l,c,u,f,p,m,h,g,b,v,k,y,w,x,C,T=this.editor;if(this.expandImmutablesIn(),n=d({clean:!1,split:!0},n),!n.skipCleaners)for(i=0,a=this.cleaners.length;i<a;i++)this.cleaners[i].applicable(t)&&(t=this.cleaners[i].clean(t));if(n.clean&&(t=t.replace(/(<br>(\s|&nbsp;)*)+(<\/?(div|p|li|col|t))/gi,"$3"),t=t.replace(/<(a|span)[^>]*><\/\1>/gi,"")),t=t.replace(/<(a|span|font)([^>]*)> <\/\1>/gi,"<$1$2>&nbsp;</$1>"),t=t.replace(/^<li/i,"<ul><li").replace(/li>$/g,"li></ul>"),c=this.isBlock(t),T.focus(),u=T.getRange(),u.deleteContents(),u.startContainer==T.document&&u.selectNodeContents(T.body),f=new s,p=f.addCaret(u),m=this.splittableParent(c,p),h=!1,g=m!=T.body&&!r.is(m,"td"),n.split&&g&&(c||r.isInline(m))&&(u.selectNode(p),o.split(u,m,!0),h=!0),b=this.htmlToFragment(t),b.firstChild&&"k-paste-container"===b.firstChild.className){for(v=[],i=0,a=b.childNodes.length;i<a;i++)v.push(b.childNodes[i].innerHTML);b=this.htmlToFragment(v.join("<br />"))}if(l=b.childNodes,e(l).filter("table").addClass("k-table").end().find("table").addClass("k-table"),e(l).each(function(e,t){r.isBlock(t)&&!r.isSelfClosing(t)&&""===t.innerHTML&&t.appendChild(T.document.createTextNode("\ufeff"))}),u.insertNode(b),m=this.splittableParent(c,p),h){for(;p.parentNode!=m;)r.unwrap(p.parentNode);r.unwrap(p.parentNode)}if(r.normalize(u.commonAncestorContainer),p.style.display="inline",r.restoreScrollTop(T.document),r.scrollTo(p),f.removeCaret(u),k=u.commonAncestorContainer.parentNode,u.collapsed&&"tbody"==r.name(k)&&(u.setStartAfter(e(k).closest("table")[0]),u.collapse(!0)),y=e(u.commonAncestorContainer.parentNode).closest("table"),y.get(0)){for(w=y.parent().contents(),x=w.length-1,C=w.get(x);null!==C.nodeValue&&(" "===C.nodeValue||""===C.nodeValue);)x-=1,C=w.get(x);C===y.get(0)&&r.insertAfter(r.createEmptyNode(T.document,"p"),y[0])}T.selectRange(u)}}),u=n.extend({init:function(e){this.options=e||{},this.replacements=[]},clean:function(e,t){var n,i,o=this,r=t||o.replacements;for(n=0,i=r.length;n<i;n+=2)e=e.replace(r[n],r[n+1]);return e}}),f=u.extend({init:function(e){u.fn.init.call(this,e),this.replacements=[/<(\/?)script([^>]*)>/i,"<$1telerik:script$2>"]},applicable:function(e){return!this.options.none&&/<script[^>]*>/i.test(e)}}),p=u.extend({init:function(e){u.fn.init.call(this,e);var t=" ";this.replacements=[/<span\s+class="Apple-tab-span"[^>]*>\s*<\/span>/gi,t,/\t/gi,t,/&nbsp;&nbsp; &nbsp;/gi,t]},applicable:function(e){return/&nbsp;&nbsp; &nbsp;|class="?Apple-tab-span/i.test(e)}}),m=u.extend({init:function(e){u.fn.init.call(this,e),this.junkReplacements=[/<\?xml[^>]*>/gi,"",/<!--(.|\n)*?-->/g,"",/&quot;/g,"'",/<o:p>&nbsp;<\/o:p>/gi,"&nbsp;",/<\/?(meta|link|style|o:|v:|x:)[^>]*>((?:.|\n)*?<\/(meta|link|style|o:|v:|x:)[^>]*>)?/gi,"",/<\/o>/g,""],this.replacements=this.junkReplacements.concat([/(?:<br>&nbsp;[\s\r\n]+|<br>)*(<\/?(h[1-6]|hr|p|div|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|caption|address|pre|form|blockquote|dl|dt|dd|dir|fieldset)[^>]*>)(?:<br>&nbsp;[\s\r\n]+|<br>)*/g,"$1",/<br><br>/g,"<BR><BR>",/<br>(?!\n)/g," ",/<table([^>]*)>(\s|&nbsp;)+<t/gi,"<table$1><t",/<tr[^>]*>(\s|&nbsp;)*<\/tr>/gi,"",/<tbody[^>]*>(\s|&nbsp;)*<\/tbody>/gi,"",/<table[^>]*>(\s|&nbsp;)*<\/table>/gi,"",/<BR><BR>/g,"<br>",/^\s*(&nbsp;)+/gi,"",/(&nbsp;|<br[^>]*>)+\s*$/gi,"",/mso-[^;"]*;?/gi,"",/<(\/?)b(\s[^>]*)?>/gi,"<$1strong$2>",/<(\/?)font(\s[^>]*)?>/gi,this.convertFontMatch,/<(\/?)i(\s[^>]*)?>/gi,"<$1em$2>",/style=(["|'])\s*\1/g,"",/(<br[^>]*>)?\n/g,function(e,t){return t?e:" "}])},convertFontMatch:function(e,t,n){var i=/face=['"]([^'"]+)['"]/i,o=i.exec(n),r=n&&o&&o[1];return t?"</span>":r?'<span style="font-family:'+r+'">':"<span>"},applicable:function(e){return/class="?Mso/i.test(e)||/style="[^"]*mso-/i.test(e)||/urn:schemas-microsoft-com:office/.test(e)},stripEmptyAnchors:function(e){return e.replace(/<a([^>]*)>\s*<\/a>/gi,function(e,t){return!t||t.indexOf("href")<0?"":e})},listType:function(e,t){var n,i=e.innerHTML,o=r.innerText(e),a=i.match(/^(?:<span [^>]*texhtml[^>]*>)?<span [^>]*(?:Symbol|Wingdings)[^>]*>([^<]+)/i),s=a&&a[1],l=/^[a-z\d]/i.test(s),d=function(e){return e.replace(/^(?:&nbsp;|[\u00a0\n\r\s])+/,"")};return a&&(n=!0),i=i.replace(/<\/?\w+[^>]*>/g,"").replace(/&nbsp;/g," "),!n&&/^[\u2022\u00b7\u00a7\u00d8o]\u00a0+/.test(i)||n&&/^.\u00a0+/.test(i)||s&&!l&&t?{tag:"ul",style:this._guessUnorderedListStyle(d(o))}:/^\s*\w+[\.\)][\u00a0 ]{2,}/.test(i)?{tag:"ol",style:this._guessOrderedListStyle(d(o))}:void 0},_convertToLi:function(e){var t,n=r.name(e);return 1==e.childNodes.length?t=e.firstChild.nodeType===r.nodeTypes.TEXT_NODE?r.innerText(e):e.firstChild.innerHTML.replace(/^\w+[\.\)](&nbsp;)+ /,""):(r.remove(e.firstChild),3==e.firstChild.nodeType&&/^[ivxlcdm]+\.$/i.test(e.firstChild.nodeValue)&&r.remove(e.firstChild),/^(&nbsp;|\s)+$/i.test(e.firstChild.innerHTML)&&r.remove(e.firstChild),t="p"!=n?"<"+n+">"+e.innerHTML+"</"+n+">":e.innerHTML),r.remove(e),r.create(document,"li",{innerHTML:t})},_guessUnorderedListStyle:function(e){return/^[\u2022\u00b7\u00FC\u00D8\u002dv-]/.test(e)?null:/^o/.test(e)?"circle":"square"},_guessOrderedListStyle:function(e){var t=null;return/^\d/.test(e)||(t=(/^[a-z]/.test(e)?"lower-":"upper-")+(/^[ivxlcdm]/i.test(e)?"roman":"alpha")),t},extractListLevels:function(e){var n=/style=['"]?[^'"]*?mso-list:\s?[a-zA-Z]+(\d+)\s[a-zA-Z]+(\d+)\s(\w+)/gi;return e=e.replace(n,function(e,n,i){return t.format('data-list="{0}" data-level="{1}" {2}',n,i,e)})},_createList:function(e,t){return r.create(document,e,{style:{listStyleType:t}})},lists:function(t){var n,i,o,a,s,d,c,u,f,p,m,h,g,b,v,k,y=e(t).find(r.blockElements.join(",")),w=-1,x={},C=["p","h1","h2","h3","h4","h5","h6"];for(u=0;u<y.length;u++)f=y[u],g=e(f).data(),b=g.list,n=r.name(f),"td"!=n&&(v=this.listType(f,g),p=v&&v.tag,!p||C.indexOf(n)<0?f.innerHTML?i&&!d&&i.appendChild(f):r.remove(f):l.msie||(m=g.level||parseFloat(f.style.marginLeft||0),k=p+b,x[m]||(x[m]={}),(!o||o<0)&&(o=m,a=b,s=e(t).find("[data-list='"+a+"']:last")[0],c=this._createList(p,v.style),r.insertBefore(c,f),w=m,x[m][k]=c),d=s===f,h=x[m][k],(m>w||!h)&&(h=this._createList(p,v.style),x[m][k]=h,i.appendChild(h)),i=this._convertToLi(f),h.appendChild(i),d?o=w=-1:w=m))},removeAttributes:function(e){for(var t=e.attributes,n=t.length;n--;)"colspan"!=r.name(t[n])&&e.removeAttributeNode(t[n])},createColGroup:function(n){var i=n.cells,o=e(n).closest("table"),r=o.children("colgroup");i.length<2||(r.length&&(i=r.children(),r[0].parentNode.removeChild(r[0])),r=e(e.map(i,function(e){var n=e.width;return n&&0!==parseInt(n,10)?t.format('<col style="width:{0}px;"/>',n):"<col />"}).join("")),r.is("colgroup")||(r=e("<colgroup/>").append(r)),r.prependTo(o))},convertHeaders:function(t){var n,i=t.cells,o=e.map(i,function(t){var n=e(t).children("p").children("strong")[0];if(n&&"strong"==r.name(n))return n});if(o.length==i.length){for(n=0;n<o.length;n++)r.unwrap(o[n]);for(e(t).closest("table").find("colgroup").after("<thead></thead>").end().find("thead").append(t),n=0;n<i.length;n++)r.changeTag(i[n],"th")}},removeParagraphs:function(t){var n,i,o,a,s;for(n=0;n<t.length;n++)for(this.removeAttributes(t[n]),a=e(t[n]),s=a.children("p"),i=0,o=s.length;i<o;i++)i<o-1&&r.insertAfter(r.create(document,"br"),s[i]),r.unwrap(s[i])},removeDefaultColors:function(e){for(var t=0;t<e.length;t++)/^\s*color:\s*[^;]*;?$/i.test(e[t].style.cssText)&&r.unwrap(e[t])},tables:function(t){var n,i,o,r,a,s=e(t).find("table"),l=this;for(r=0;r<s.length;r++){for(n=s[r].rows,o=i=n[0],a=1;a<n.length;a++)n[a].cells.length>o.cells.length&&(o=n[a]);l.createColGroup(o),l.convertHeaders(i),l.removeAttributes(s[r]),l.removeParagraphs(s.eq(r).find("td,th")),l.removeDefaultColors(s.eq(r).find("span"))}},headers:function(t){var n,i=e(t).find("p.MsoTitle");for(n=0;n<i.length;n++)r.changeTag(i[n],"h1")},removeFormatting:function(t){e(t).find("*").each(function(){e(this).css({fontSize:"",fontFamily:""}),this.getAttribute("style")||this.style.cssText||this.removeAttribute("style")})},clean:function(e){var t,n=this,i=this.options;return i.none?(e=u.fn.clean.call(n,e,this.junkReplacements),e=n.stripEmptyAnchors(e)):(e=i.msConvertLists?this.extractListLevels(e):e,e=u.fn.clean.call(n,e),e=n.stripEmptyAnchors(e),t=r.create(document,"div",{innerHTML:e}),n.headers(t),i.msConvertLists&&n.lists(t),n.tables(t),i.msAllFormatting&&n.removeFormatting(t),e=t.innerHTML.replace(/(<[^>]*)\s+class="?[^"\s>]*"?/gi,"$1")),e}}),h=u.extend({init:function(e){u.fn.init.call(this,e),this.replacements=[/\s+class="Apple-style-span[^"]*"/gi,"",/<(div|p|h[1-6])\s+style="[^"]*"/gi,"<$1",/^<div>(.*)<\/div>$/,"$1"]},applicable:function(e){return/class="?Apple-style-span|style="[^"]*-webkit-nbsp-mode/i.test(e)}}),g=u.extend({clean:function(e){var t=r.create(document,"div",{innerHTML:e});return t=this.cleanDom(t),t.innerHTML},cleanDom:function(e){return e}}),b=g.extend({cleanDom:function(t){var n=this.collectTags();return e(t).find(n).each(function(){r.unwrap(this)}),t},collectTags:function(){if(this.options.span)return"span"},applicable:function(){return this.options.span}}),v=g.extend({cleanDom:function(t){var n=this.collectAttr(),i=e(t).find("["+n.join("],[")+"]");return i.removeAttr(n.join(" ")),t},collectAttr:function(){return this.options.css?["class","style"]:[]},applicable:function(){return this.options.css}}),k=function(){this.text="",this.add=function(e){this.text+=e}},y=n.extend({init:function(e){this.separators=e||{text:" ",line:"<br/>"},this.lines=[],this.inlineBlockText=[],this.resetLine()},appendText:function(e){3===e.nodeType&&(e=e.nodeValue),this.textContainer.add(e)},appendInlineBlockText:function(e){this.inlineBlockText.push(e)},flashInlineBlockText:function(){this.inlineBlockText.length&&(this.appendText(this.inlineBlockText.join(" ")),this.inlineBlockText=[])},endLine:function(){this.flashInlineBlockText(),this.resetLine()},html:function(){var e,t,n,i,o,r,a,s,l=this.separators,d="",c=this.lines;for(this.flashInlineBlockText(),e=0,t=c.length,n=t-1;e<t;e++){for(i=c[e],o=0,r=i.length,a=r-1;o<r;o++)s=i[o].text,d+=s,o!==a&&(d+=l.text);e!==n&&(d+=l.line)}return d},resetLine:function(){this.textContainer=new k,this.line=[],this.line.push(this.textContainer),this.lines.push(this.line)}}),w=n.extend({init:function(e){this.callback=e},enumerate:function(e){var t,n;e&&(t=this.callback(e),n=e.firstChild,!t&&n&&this.enumerate(n),this.enumerate(e.nextSibling))}}),x=u.extend({init:function(t){u.fn.init.call(this,t),this.hasText=!1,this.enumerator=new w(e.proxy(this.buildText,this))},clean:function(e){var t=r.create(document,"div",{innerHTML:e});return this.cleanDom(t)},cleanDom:function(e){return this.separators=this.getDefaultSeparators(),this.htmlLines=new y(this.separators),this.enumerator.enumerate(e.firstChild),this.hasText=!1,this.htmlLines.html()},buildText:function(e){if(r.isDataNode(e)){if(r.isEmptyspace(e))return;this.htmlLines.appendText(e.nodeValue.replace("\n",this.separators.line)),this.hasText=!0}else{if(r.isBlock(e)&&this.hasText){var t=this.actions[r.name(e)]||this.actions.block;return t(this,e)}r.isBr(e)&&this.htmlLines.appendText(this.separators.line)}},applicable:function(){var e=this.options;return e.all||e.keepNewLines},getDefaultSeparators:function(){return this.options.all?{text:" ",line:" "}:{text:" ",line:"<br/>"}},actions:{ul:e.noop,ol:e.noop,table:e.noop,thead:e.noop,tbody:e.noop,td:function(e,t){var n=new x({all:!0}),i=n.cleanDom(t);return e.htmlLines.appendInlineBlockText(i),!0},block:function(e){e.htmlLines.endLine()}}}),C=u.extend({clean:function(e){return this.options.custom(e)},applicable:function(){return"function"==typeof this.options.custom}});d(i,{Clipboard:c,Cleaner:u,ScriptCleaner:f,TabCleaner:p,MSWordFormatCleaner:m,WebkitFormatCleaner:h,HtmlTagsCleaner:b,HtmlAttrCleaner:v,HtmlContentCleaner:x,HtmlTextLines:y,CustomCleaner:C})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/keyboard.min",["editor/command.min"],e)}(function(){!function(e){function t(e,t){return t.startContainer===e&&t.endContainer===e&&0===t.startOffset&&t.endOffset==e.childNodes.length}function n(e,t,n){for(var i=e?e[t]:null;i&&!n(i);)i=i[t];return i}var i=window.kendo,o=i.Class,r=i.ui.editor,a=r.RangeUtils,s=r.Dom,l=r.RestorePoint,d=r.Marker,c=i.support.browser,u='<br class="k-br">',f=e.extend,p=s.nodeTypes,m="previousSibling",h="td,th,caption",g="table,tbody,thead,tfoot,tr",b=g+","+h,v=function(t){return!t.collapsed&&e(t.commonAncestorContainer).is(g)},k=o.extend({remove:function(t){var n,i,o,r,l,c,u,f=this,p=new d;p.add(t,!1),n=a.getAll(t,function(t){return e(t).is(b)}),i=a.documentFromRange(t),o=p.start,r=p.end,l=h.split(","),c=s.parentOfType(o,l),u=s.parentOfType(r,l),f._removeContent(o,c,!0),f._removeContent(r,u,!1),e(n).each(function(t,n){n=e(n),(n.is(h)?n:n.find(h)).each(function(e,t){t.innerHTML="&#65279;"})}),c&&!o.previousSibling&&s.insertBefore(i.createTextNode("\ufeff"),o),u&&!r.nextSibling&&s.insertAfter(i.createTextNode("\ufeff"),r),c?t.setStartBefore(o):n[0]&&(c=e(n[0]),c=c.is(h)?c:c.find(h).first(),c.length&&t.setStart(c.get(0),0)),t.collapse(!0),s.remove(o),s.remove(r)},_removeContent:function(t,n,i){if(n){var o,r=i?"nextSibling":"previousSibling",a=function(t){for(;t&&!t[r];)t=t.parentNode;return t&&e.contains(n,t)?t[r]:null};for(t=a(t);t;)o=a(t),s.remove(t),t=o}}}),y=o.extend({init:function(e){this.editor=e},keydown:function(n){var i,o,a,s,d=this,u=d.editor,p=u.keyboard,m=p.isTypingKey(n),h=f(e.Event(),n);return d.editor.trigger("keydown",h),h.isDefaultPrevented()?(n.preventDefault(),!0):!(h.isDefaultPrevented()||!m||p.isTypingInProgress())&&(i=u.getRange(),o=u.body,d.startRestorePoint=new l(i,o),v(i)&&(a=new k(u),a.remove(i),u.selectRange(i)),c.webkit&&!i.collapsed&&t(o,i)&&(o.innerHTML=""),u.immutables&&r.Immutables.immutablesContext(i)&&(s=new r.BackspaceHandler(u),s.deleteSelection(i)),p.startTyping(function(){d.endRestorePoint=r._finishUpdate(u,d.startRestorePoint)}),!0)},keyup:function(e){var t=this.editor.keyboard;return this.editor.trigger("keyup",e),!!t.isTypingInProgress()&&(t.endTyping(),!0)}}),w=o.extend({init:function(e){this.editor=e},_addCaret:function(e){var t=s.create(this.editor.document,"a");return!i.support.browser.chrome&&e.firstChild&&e.firstChild.nodeType===p.ELEMENT_NODE&&(e=e.firstChild),s.insertAt(e,t,0),s.stripBomNode(t.previousSibling),s.stripBomNode(t.nextSibling),t},_restoreCaret:function(e){var t=this.editor.createRange();!e.nextSibling&&s.isDataNode(e.previousSibling)?t.setStart(e.previousSibling,e.previousSibling.length):t.setStartAfter(e),t.collapse(!0),this.editor.selectRange(t),s.remove(e)},_handleDelete:function(e){var t,n,i=e.endContainer,o=s.closestEditableOfType(i,s.blockElements);return!(!o||!r.RangeUtils.isEndOf(e,o))&&(t=s.next(o),!(!t||"p"!=s.name(t))&&(n=this._addCaret(t),this._merge(o,t),this._restoreCaret(n),!0))},_cleanBomBefore:function(e){for(var t=e.startOffset,n=e.startContainer,i=n.nodeValue,o=0;t-o>=0&&"\ufeff"==i[t-o-1];)o++;o>0&&(n.deleteData(t-o,o),e.setStart(n,Math.max(0,t-o)),e.collapse(!0),this.editor.selectRange(e))},_handleBackspace:function(t){var i,o,a,l,d,c,u,f,p,h,g,b,v,k=t.startContainer,y=s.closestEditableOfType(k,["li"]),w=s.closestEditableOfType(k,"p,h1,h2,h3,h4,h5,h6".split(",")),x=this.editor;if(s.isDataNode(k)){if(t.collapsed&&/^\s[\ufeff]+$/.test(k.nodeValue))return t.setStart(k,0),t.setEnd(k,k.length),x.selectRange(t),!1;this._cleanBomBefore(t)}return i=n(w,m,function(e){return!s.htmlIndentSpace(e)}),t.collapsed&&t.startOffset!==t.endOffset&&t.startOffset<0&&(t.startOffset=0,t.endOffset=0,x.selectRange(t)),o=y&&r.RangeUtils.isStartOf(t,y),a=y&&e(y).index(),l=o&&a>0,l&&(w=y,i=s.prev(y)),w&&i&&s.is(i,"table")&&r.RangeUtils.isStartOf(t,w)?(""===w.innerText&&(w.innerHTML="\ufeff"),!0):r.RangeUtils.isStartOf(t,w)&&(parseInt(w.style.marginLeft,10)>0||parseInt(w.style.marginRight,10)>0)?(x.exec("outdent"),!0):w&&i&&r.RangeUtils.isStartOf(t,w)||l?(d=this._addCaret(w),this._merge(i,w),this._restoreCaret(d),!0):o&&0===a?(c=y.firstChild,c||(y.innerHTML=r.emptyElementContent,c=y.firstChild),u=new r.ListFormatter(s.name(y.parentNode),"p"),t.selectNodeContents(y),u.toggle(t),s.insignificant(c)?t.setStartBefore(c):t.setStart(c,0),x.selectRange(t),!0):(f=k.childNodes[t.startOffset-1],p=t,h=f&&s.closestEditableOfType(f,["a"]),g=n(f||k,m,function(e){return!s.isDataNode(e)||!s.isBom(e)&&e.length>0}),(h||(0===t.startOffset||f)&&s.is(g,"a"))&&(h=h||g,p=x.createRange(),p.setStart(h,h.childNodes.length),p.collapse(!0)),h=h||s.closestEditableOfType(f||p.startContainer,["a"]),b=h&&r.RangeUtils.isEndOf(p,h),b&&(v=new r.UnlinkCommand({range:p,body:x.body,immutables:!!x.immutables}),x.execCommand(v),x._selectionChange()),!1)},_handleSelection:function(t){var n,i,o,a,l=t.commonAncestorContainer,c=s.closest(l,"table"),u=r.emptyElementContent,f=this.editor;return v(t)?(n=new k(f),n.remove(t),f.selectRange(t),!0):(i=new d,i.add(t,!1),t.commonAncestorContainer===f.body&&this._surroundFullyContent(i,t),f.immutables&&this._handleImmutables(i),this._surroundFullySelectedAnchor(i,t),t.setStartAfter(i.start),t.setEndBefore(i.end),o=t.startContainer,a=t.endContainer,t.deleteContents(),"li"===a.tagName.toLocaleLowerCase()&&s.emptyNode(a)&&(t.selectNode(a),t.deleteContents()),c&&""===e(c).text()&&(t.selectNode(c),t.deleteContents()),l=t.commonAncestorContainer,"p"===s.name(l)&&""===l.innerHTML&&(l.innerHTML=u,t.setStart(l,0)),this._join(o,a),s.insertAfter(f.document.createTextNode("\ufeff"),i.start),i.remove(t),o=t.startContainer,"tr"==s.name(o)&&(o=o.childNodes[Math.max(0,t.startOffset-1)],t.setStart(o,s.getNodeLength(o))),t.collapse(!0),f.selectRange(t),!0)},_handleImmutables:function(e){var t=r.Immutables.immutableParent,n=t(e.start),i=t(e.start);n&&s.insertBefore(e.start,n),i&&s.insertAfter(e.end,i),n&&s.remove(n),i&&i.parentNode&&s.remove(i)},_surroundFullyContent:function(e,t){var n=t.commonAncestorContainer.children,i=n[0],o=n[n.length-1];this._moveMarker(e,t,i,o)},_surroundFullySelectedAnchor:function(t,n){var i=t.start,o=e(i).closest("a").get(0),r=t.end,a=e(r).closest("a").get(0);this._moveMarker(t,n,o,a)},_moveMarker:function(e,t,n,i){var o=e.start,r=e.end;n&&a.isStartOf(t,n)&&s.insertBefore(o,n),i&&a.isEndOf(t,i)&&s.insertAfter(r,i)},_root:function(e){for(;e&&"body"!=s.name(e)&&e.parentNode&&"body"!=s.name(e.parentNode);)e=e.parentNode;return e},_join:function(e,t){e=this._root(e),t=this._root(t),e!=t&&s.is(t,"p")&&this._merge(e,t)},_merge:function(e,t){for(s.removeTrailingBreak(e);e&&t.firstChild;)1==e.nodeType?(e=s.list(e)?e.children[e.children.length-1]:e,e&&e.appendChild(t.firstChild)):e.nodeType===p.TEXT_NODE?this._mergeWithTextNode(e,t.firstChild):e.parentNode.appendChild(t.firstChild);s.remove(t)},_mergeWithTextNode:function(e,t){e&&e.nodeType===p.TEXT_NODE&&(e.nextSibling&&this._isCaret(e.nextSibling)?s.insertAfter(t,e.nextSibling):s.insertAfter(t,e))},_isCaret:function(t){return e(t).is("a")},keydown:function(e){var t,n,o=this.editor,a=o.getRange(),s=e.keyCode,d=i.keys,c=s===d.BACKSPACE,u=s==d.DELETE;o.immutables&&o.immutables.keydown(e,a)||(!c&&!u||a.collapsed?c?t="_handleBackspace":u&&(t="_handleDelete"):t="_handleSelection",t&&(n=new l(a,o.body),this[t](a)&&(e.preventDefault(),r._finishUpdate(o,n))))},deleteSelection:function(e){this._handleSelection(e)},keyup:e.noop}),x=o.extend({init:function(e){this.editor=e,this.systemCommandIsInProgress=!1},createUndoCommand:function(){this.startRestorePoint=this.endRestorePoint=r._finishUpdate(this.editor,this.startRestorePoint)},changed:function(){return!!this.startRestorePoint&&this.startRestorePoint.html!=this.editor.body.innerHTML},keydown:function(e){var t=this,n=t.editor,i=n.keyboard;return i.isModifierKey(e)?(i.isTypingInProgress()&&i.endTyping(!0),t.startRestorePoint=new l(n.getRange(),n.body),!0):!!i.isSystem(e)&&(t.systemCommandIsInProgress=!0,t.changed()&&(t.systemCommandIsInProgress=!1,t.createUndoCommand()),!0)},keyup:function(){var e=this;return!(!e.systemCommandIsInProgress||!e.changed())&&(e.systemCommandIsInProgress=!1,e.createUndoCommand(),!0)}}),C=o.extend({init:function(e){this.editor=e},keydown:function(e){!c.webkit||e.isDefaultPrevented()||!e.ctrlKey||65!=e.keyCode||e.altKey||e.shiftKey||(this.editor.options.immutables&&this._toSelectableImmutables(),this._selectEditorBody())},_selectEditorBody:function(){var e=this.editor,t=e.getRange();t.selectNodeContents(e.body),e.selectRange(t)},_toSelectableImmutables:function(){for(var t=this.editor,n=t.body,i=r.Immutables.immutable,o=s.emptyTextNode,a=n.firstChild,l=n.lastChild;o(a);)a=a.nextSibling;for(;o(l);)l=l.previousSibling;a&&i(a)&&e(u).prependTo(n),l&&i(l)&&e(u).appendTo(n)},keyup:e.noop}),T=o.extend({init:function(e){this.handlers=e,this.typingInProgress=!1},isCharacter:function(e){return e>=48&&e<=90||e>=96&&e<=111||e>=186&&e<=192||e>=219&&e<=222||229==e},toolFromShortcut:function(t,n){var i,o,r=String.fromCharCode(n.keyCode),a=this._getShortcutModifier(n,navigator.platform);for(i in t)if(o=e.extend({ctrl:!1,alt:!1,shift:!1},t[i].options),(o.key==r||o.key==n.keyCode)&&o.ctrl==a&&o.alt==n.altKey&&o.shift==n.shiftKey)return i},_getShortcutModifier:function(e,t){var n=t.toUpperCase().indexOf("MAC")>=0;return n?e.metaKey:e.ctrlKey},toolsFromShortcut:function(t,n){var i,o,r,a=String.fromCharCode(n.keyCode),s=[],l=function(e){return e==a||e==n.keyCode||e==n.charCode};for(i in t)o=e.extend({ctrl:!1,alt:!1,shift:!1},t[i].options),r=e.isArray(o.key)?e.grep(o.key,l).length>0:l(o.key),r&&o.ctrl==n.ctrlKey&&o.alt==n.altKey&&o.shift==n.shiftKey&&s.push(t[i]);return s},isTypingKey:function(e){var t=e.keyCode;return this.isCharacter(t)&&!e.ctrlKey&&!e.altKey||32==t||13==t||8==t||46==t&&!e.shiftKey&&!e.ctrlKey&&!e.altKey},isModifierKey:function(e){var t=e.keyCode;return 17==t&&!e.shiftKey&&!e.altKey||16==t&&!e.ctrlKey&&!e.altKey||18==t&&!e.ctrlKey&&!e.shiftKey},isSystem:function(e){return 46==e.keyCode&&e.ctrlKey&&!e.altKey&&!e.shiftKey},startTyping:function(e){this.onEndTyping=e,this.typingInProgress=!0},stopTyping:function(){this.typingInProgress&&this.onEndTyping&&this.onEndTyping(),this.typingInProgress=!1},endTyping:function(t){var n=this;n.clearTimeout(),t?n.stopTyping():n.timeout=window.setTimeout(e.proxy(n.stopTyping,n),1e3)},isTypingInProgress:function(){return this.typingInProgress},clearTimeout:function(){window.clearTimeout(this.timeout)},notify:function(e,t){var n,i=this.handlers;for(n=0;n<i.length&&!i[n][t](e);n++);},keydown:function(e){this.notify(e,"keydown")},keyup:function(e){this.notify(e,"keyup")}});f(r,{TypingHandler:y,SystemHandler:x,BackspaceHandler:w,SelectAllHandler:C,Keyboard:T})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/exportpdf.min",["editor/command.min"],e)}(function(){!function(e){var t=window.kendo,n=t.ui.editor,i=n.Command,o=n.EditorUtils,r=o.registerTool,a=n.Tool,s=n.ToolTemplate,l=e.extend,d=i.extend({init:function(e){this.async=!0,i.fn.init.call(this,e)},exec:function(){var e=this,t=e.lockRange(!0),n=e.editor;n._destroyResizings(),n.saveAsPDF().then(function(){e.releaseRange(t),n._initializeColumnResizing(),n._initializeRowResizing(),n._initializeTableResizing()})}});l(n,{ExportPdfCommand:d}),r("pdf",new a({command:d,template:new s({template:o.buttonTemplate,title:"Export PDF"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/plugins/print.min",["editor/command.min"],e)}(function(){!function(e){var t=window.kendo,n=t.ui.editor,i=n.Command,o=n.EditorUtils,r=o.registerTool,a=n.Tool,s=n.ToolTemplate,l=e.extend,d=i.extend({init:function(e){i.fn.init.call(this,e),this.managesUndoRedo=!0},exec:function(){var e=this.editor;t.support.browser.msie?e.document.execCommand("print",!1,null):e.window.print&&e.window.print()}});l(n,{PrintCommand:d}),r("print",new a({command:d,template:new s({template:o.buttonTemplate,title:"Print"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/resizing/resizing-utils.min",["editor/main.min"],e)}(function(){!function(e,t){function n(e){var t=e.value,n=e.min,i=e.max;return f(u(p(t),p(i)),p(n))}function i(t){return t&&!m(t).is("body")&&t.scrollHeight>t.clientHeight?e.support.scrollbar():0}function o(e,t){return r(e)?p(e):p(e)/t*100}function r(e){return typeof e===w&&k.test(e)}function a(e){return typeof e===w&&y.test(e)}function s(e){return p(e)+b}function l(e){return p(e)+v}var d=window,c=d.Math,u=c.min,f=c.max,p=d.parseFloat,m=e.jQuery,h=m.extend,g=e.ui.editor,b="%",v="px",k=/(\d+)(\.?)(\d*)%/,y=/(\d+)(\.?)(\d*)px/,w="string",x={constrain:n,getScrollBarWidth:i,calculatePercentageRatio:o,inPercentages:r,inPixels:a,toPercentages:s,toPixels:l};h(g,{ResizingUtils:x})}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/resizing/table-element-resizing.min",["editor/main.min","kendo.resizable.min","editor/resizing/resizing-utils.min"],e)}(function(){!function(e,t){var n=e.jQuery,i=n.extend,o=n.noop,r=n.proxy,a=e.ui.editor,s=e.Class,l="keydown",d="mousedown",c="mouseenter",u="mouseleave",f="mousemove",p="mouseup",m=",",h=".",g=":last-child",b="table",v=s.extend({init:function(e,t){var o=this;o.options=i({},o.options,t),o.options.tags=n.isArray(o.options.tags)?o.options.tags:[o.options.tags],n(e).is(b)&&(o.element=e,o._attachEventHandlers())},destroy:function(){var e=this,t=e.options.eventNamespace;e.element&&(n(e.element).off(t),e.element=null),n(e.options.rootElement).off(l+t),e._destroyResizeHandle()},options:{tags:[],min:0,rootElement:null,eventNamespace:"",rtl:!1,handle:{dataAttribute:"",height:0,width:0,classNames:{},template:""}},_attachEventHandlers:function(){var e=this,t=e.options;n(e.element).on(f+t.eventNamespace,t.tags.join(m),r(e.detectElementBorderHovering,e))},resizingInProgress:function(){var e=this,t=e._resizable;return!!t&&!!t.resizing},resize:o,detectElementBorderHovering:function(e){var t=this,i=t.options,o=i.handle,r=n(e.currentTarget),a=t.resizeHandle,s=o.dataAttribute;t.resizingInProgress()||(!r.is(g)&&t.elementBorderHovered(r,e)?a?a.data(s)&&a.data(s)!==r[0]&&t.showResizeHandle(r,e):t.showResizeHandle(r,e):a&&t._destroyResizeHandle())},elementBorderHovered:o,showResizeHandle:function(e,t){var n=this;0===t.buttons&&(n._initResizeHandle(),n.setResizeHandlePosition(e),n.setResizeHandleDimensions(),n.setResizeHandleDataAttributes(e[0]),n._attachResizeHandleEventHandlers(),n._initResizable(e),n._hideResizeMarker(),n.resizeHandle.show())},_initResizeHandle:function(){var e=this,t=e.options;e._destroyResizeHandle(),e.resizeHandle=n(t.handle.template).appendTo(t.rootElement)},setResizeHandlePosition:o,setResizeHandleDimensions:o,setResizeHandleDataAttributes:function(e){var t=this;t.resizeHandle.data(t.options.handle.dataAttribute,e)},_attachResizeHandleEventHandlers:function(){var e=this,t=e.options,n=t.eventNamespace,i=t.handle.classNames.marker,o=e.resizeHandle;e.resizeHandle.on(d+n,function(){o.find(h+i).show()}).on(p+n,function(){o.find(h+i).hide()})},_hideResizeMarker:function(){var e=this;e.resizeHandle.find(h+e.options.handle.classNames.marker).hide()},_destroyResizeHandle:function(){var e=this;e.resizeHandle&&(e._destroyResizable(),e.resizeHandle.off(e.options.eventNamespace).remove(),e.resizeHandle=null)},_initResizable:function(t){var n=this;n.resizeHandle&&(n._destroyResizable(),n._resizable=new e.ui.Resizable(t,{draggableElement:n.resizeHandle[0],start:r(n.onResizeStart,n),resize:r(n.onResize,n),resizeend:r(n.onResizeEnd,n)}))},_destroyResizable:function(){var e=this;e._resizable&&(e._resizable.destroy(),e._resizable=null)},onResizeStart:function(){this._disableKeyboard()},onResize:function(e){this.setResizeHandleDragPosition(e)},setResizeHandleDragPosition:o,onResizeEnd:function(e){var t=this;t.resize(e),t._destroyResizeHandle(),t._enableKeyboard()},_enableKeyboard:function(){var e=this.options;n(e.rootElement).off(l+e.eventNamespace)},_disableKeyboard:function(){var e=this.options;n(e.rootElement).on(l+e.eventNamespace,function(e){e.preventDefault()})},_forceResizing:function(e){var t=this._resizable;t&&t.userEvents&&t.userEvents._end(e)}}),k=s.extend({create:function(e,t){var i=this,o=t.name,r=t.eventNamespace;n(e.body).on(c+r,b,function(n){var r=n.currentTarget,a=e[o];n.stopPropagation(),a?a.element===r||a.resizingInProgress()||(i._destroyResizing(e,t),i._initResizing(e,r,t)):i._initResizing(e,r,t)}).on(u+r,b,function(r){var a,s=e[o];r.stopPropagation(),!s||s.resizingInProgress()||s.resizeHandle||(a=n(s.element).parents(b)[0],a&&(i._destroyResizing(e,t),i._initResizing(e,a,t)))}).on(u+r,function(){var n=e[o];n&&!n.resizingInProgress()&&i._destroyResizing(e,t)}).on(p+r,function(r){var a,s=e[o];s&&s.resizingInProgress()&&(a=n(r.target).parents(b)[0],a&&(s._forceResizing(r),i._destroyResizing(e,t),i._initResizing(e,a,t)))})},dispose:function(e,t){n(e.body).off(t.eventNamespace)},_initResizing:function(t,n,i){var o=i.name,r=i.type;t[o]=new r(n,{rtl:e.support.isRtl(t.element),rootElement:t.body})},_destroyResizing:function(e,t){var n=t.name;e[n]&&(e[n].destroy(),e[n]=null)}});k.current=new k,v.create=function(e,t){k.current.create(e,t)},v.dispose=function(e,t){k.current.dispose(e,t)},i(a,{TableElementResizing:v})}(window.kendo);
},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/resizing/column-resizing.min",["editor/main.min","editor/resizing/resizing-utils.min","editor/resizing/table-element-resizing.min"],e)}(function(){!function(e,t){var n=window,i=n.Math,o=i.abs,r=e.jQuery,a=r.extend,s=e.ui.editor,l=s.TableElementResizing,d=s.ResizingUtils,c=d.constrain,u=d.calculatePercentageRatio,f=d.getScrollBarWidth,p=d.inPercentages,m=d.toPercentages,h=d.toPixels,g=e._outerWidth,b=".kendoEditorColumnResizing",v="k-column-resize-handle",k="k-column-resize-marker",y="body",w="tbody",x="td",C="th",T="tr",_=",",N="width",R=l.extend({options:{tags:[x,C],min:20,rootElement:null,eventNamespace:b,rtl:!1,handle:{dataAttribute:"column",width:10,height:0,classNames:{handle:v,marker:k},template:'<div class="k-column-resize-handle-wrapper" unselectable="on" contenteditable="false"><div class="'+v+'"><div class="'+k+'"></div></div></div>'}},elementBorderHovered:function(e,t){var n=this,i=n.options,o=i.handle.width,a=e.offset().left+(i.rtl?0:g(e)),s=t.clientX+r(e[0].ownerDocument).scrollLeft();return s>a-o&&s<a+o},setResizeHandlePosition:function(e){var t=this,n=r(t.element).children(w),i=t.options,o=i.rtl,a=i.handle.width,s=r(i.rootElement),l=s.is(y)?0:s.scrollTop(),d=s.is(y)?0:s.scrollLeft(),c=o?0:g(e),u=o?f(s[0]):0,p=e.offset().left-(s.offset().left+parseFloat(s.css("borderLeftWidth")))-parseFloat(e.css("marginLeft")),m=n.offset().top-(s.offset().top+parseFloat(s.css("borderTopWidth")))-parseFloat(n.css("marginTop"));t.resizeHandle.css({top:m+l,left:p+c+(d-u)-a/2,position:"absolute"})},setResizeHandleDimensions:function(){var e=this,t=r(e.element).children(w);e.resizeHandle.css({width:e.options.handle.width,height:t.height()})},setResizeHandleDragPosition:function(e){var t=this,n=r(r(e.currentTarget).data(t.options.handle.dataAttribute)),i=t.options,o=r(i.rootElement),a=i.handle?i.handle.width:0,s=i.min,l=i.rtl,d=g(n),u=n.offset().left-(o.offset().left+parseFloat(o.css("borderLeftWidth")))-parseFloat(n.css("marginLeft")),p=g(n.next()),m=r(t.resizeHandle),h=o.is(y)?0:o.scrollLeft(),b=l?f(o[0]):0,v=m.offset().left-(o.offset().left+parseFloat(o.css("borderLeftWidth")))-parseFloat(m.css("marginLeft")),k=c({value:v+(h-b)+e.x.delta,min:u+(h-b)-(l?p:0)+s,max:u+d+(h-b)+(l?0:p)-a-s});m.css({left:k})},resize:function(e){var t,n,i,o=this,a=r(r(e.currentTarget).data(o.options.handle.dataAttribute)),s=o.options,l=s.rtl?-1:1,d=s.min,u=l*e.x.initialDelta;o._setTableComputedWidth(),o._setColumnsComputedWidth(),i=g(a),n=g(a.next()),t=c({value:i+u,min:d,max:i+n-d}),o._resizeColumn(a[0],t),o._resizeTopAndBottomColumns(a[0],t),o._resizeAdjacentColumns(a.index(),n,i,i-t)},_setTableComputedWidth:function(){var e=this.element;""===e.style[N]&&(e.style[N]=h(g(r(e))))},_setColumnsComputedWidth:function(){var e,t=this,n=r(t.element).children(w),i=g(n),o=n.children(T).children(x),a=o.length,s=o.map(function(){return g(r(this))});for(e=0;e<a;e++)o[e].style[N]=p(o[e].style[N])?m(u(s[e],i)):h(s[e])},_resizeTopAndBottomColumns:function(e,t){var n,i=this,o=r(e).index(),a=r(i.element).children(w).children(T).children(i.options.tags.join(_)).filter(function(){var t=this;return r(t).index()===o&&t!==e}),s=a.length;for(n=0;n<s;n++)i._resizeColumn(a[n],t)},_resizeColumn:function(e,t){e.style[N]=p(e.style[N])?m(u(t,g(r(this.element).children(w)))):h(t)},_resizeAdjacentColumns:function(e,t,n,i){var o,a=this,s=r(a.element).children(w).children(T).children(a.options.tags.join(_)).filter(function(){return r(this).index()===e+1}),l=s.length;for(o=0;o<l;o++)a._resizeAdjacentColumn(s[o],t,n,i)},_resizeAdjacentColumn:function(e,t,n,i){var r=this,a=r.options.min,s=c({value:t+i,min:a,max:o(n+t-a)});r._resizeColumn(e,s)}});R.create=function(e){l.create(e,{name:"columnResizing",type:R,eventNamespace:b})},R.dispose=function(e){l.dispose(e,{eventNamespace:b})},a(s,{ColumnResizing:R})}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/resizing/row-resizing.min",["editor/main.min","editor/resizing/resizing-utils.min","editor/resizing/table-element-resizing.min"],e)}(function(){!function(e,t){var n=window.Math,i=n.abs,o=e.jQuery,r=o.extend,a=e.ui.editor,s=a.TableElementResizing,l=a.ResizingUtils,d=l.getScrollBarWidth,c=l.constrain,u=l.calculatePercentageRatio,f=l.inPercentages,p=l.toPercentages,m=l.toPixels,h=e._outerHeight,g=".kendoEditorRowResizing",b="k-row-resize-handle",v="k-row-resize-marker-wrapper",k="k-row-resize-marker",y="body",w="tr",x="tbody",C="height",T=s.extend({options:{tags:[w],min:20,rootElement:null,eventNamespace:g,rtl:!1,handle:{dataAttribute:"row",width:0,height:10,classNames:{handle:b,marker:k},template:'<div class="k-row-resize-handle-wrapper" unselectable="on" contenteditable="false"><div class="'+b+'"><div class="'+v+'"><div class="'+k+'"></div></div></div></div>'}},elementBorderHovered:function(e,t){var n=this,i=n.options.handle[C],r=e.offset().top+h(e),a=t.clientY+o(e[0].ownerDocument).scrollTop();return a>r-i&&a<r+i},setResizeHandlePosition:function(e){var t=this,n=t.options,i=n.handle[C],r=o(n.rootElement),a=r.is(y)?0:r.scrollTop(),s=r.is(y)?0:r.scrollLeft(),l=n.rtl?d(r[0]):0,c=e.offset().left-(r.offset().left+parseFloat(r.css("borderLeftWidth")))-parseFloat(e.css("marginLeft")),u=e.offset().top-(r.offset().top+parseFloat(r.css("borderTopWidth")))-parseFloat(e.css("marginTop"));t.resizeHandle.css({top:u+h(e)+a-i/2,left:c+(s-l),position:"absolute"})},setResizeHandleDimensions:function(){var e=this;e.resizeHandle.css({width:o(e.element).children(x).width(),height:e.options.handle[C]})},setResizeHandleDragPosition:function(e){var t=this,n=t.options,i=n.min,r=o(t.element).children(x),a=o(t.resizeHandle),s=o(e.currentTarget).data(n.handle.dataAttribute),l=o(s),d=o(n.rootElement),u=d.is(y)?0:d.scrollTop(),f=r.offset().top-(d.offset().top+parseFloat(d.css("borderTopWidth")))-parseFloat(r.css("marginTop")),p=l.offset().top-(d.offset().top+parseFloat(d.css("borderTopWidth")))-parseFloat(l.css("marginTop")),m=a.offset().top-(d.offset().top+parseFloat(d.css("borderTopWidth")))-parseFloat(a.css("marginTop")),g=c({value:m+u+e.y.delta,min:p+u+i,max:f+h(r)+u-n.handle[C]-i});a.css({top:g})},resize:function(e){var t=this,n=t.options,r=o(e.currentTarget).data(n.handle.dataAttribute),a=h(o(r)),s=o(t.element),l=h(s),d=s.children(x),u=d.height(),p=r.style[C],g=c({value:a+e.y.initialDelta,min:n.min,max:i(u-n.min)});t._setRowsHeightInPixels(),r.style[C]=m(g),t._setTableHeight(l+(g-a)),f(p)&&t._setRowsHeightInPercentages()},_setRowsHeightInPixels:function(){var e,t=this,n=o(t.element).children(x).children(w),i=n.length,r=n.map(function(){return h(o(this))});for(e=0;e<i;e++)n[e].style[C]=m(r[e])},_setRowsHeightInPercentages:function(){var e,t=this,n=o(t.element).children(x),i=n.height(),r=n.children(w),a=r.length,s=r.map(function(){return h(o(this))});for(e=0;e<a;e++)r[e].style[C]=p(u(s[e],i))},_setTableHeight:function(e){var t=this.element;t.style[C]=f(t.style[C])?p(u(e,o(t).parent().height())):m(e)}});T.create=function(e){s.create(e,{name:"rowResizing",type:T,eventNamespace:g})},T.dispose=function(e){s.dispose(e,{eventNamespace:g})},r(a,{RowResizing:T})}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/resizing/table-resize-handle.min",["editor/main.min","kendo.draganddrop.min","editor/resizing/resizing-utils.min"],e)}(function(){!function(e,t){var n,i,o,r,a,s,l,d,c,u,f,p,m,h,g,b,v,k,y,w,x,C,T=e.jQuery,_=T.extend,N=T.noop,R=T.proxy,S=e.ui.editor,z=e.Class,A=e.ui.Draggable,E=e.Observable,D=S.ResizingUtils.getScrollBarWidth,P=e._outerWidth,I=e._outerHeight,H=".kendoEditorTableResizeHandle",O="k-table-resize-handle",F="dragStart",B="drag",L="dragEnd",W="halfInside",U="mouseover",M="mouseout",V="body",j="table",K="east",$="north",G="northeast",q="northwest",Q="south",X="southeast",Y="southwest",J="west",Z=".",ee=E.extend({init:function(e){var t=this;E.fn.init.call(t),t.options=_({},t.options,e),t.element=T(t.options.template).appendTo(t.options.appendTo)[0],t._attachEventHandlers(),t._addStyles(),t._initDraggable(),t._initPositioningStrategy(),t._initDraggingStrategy(),T(t.element).data(j,t.options.resizableElement)},destroy:function(){var e=this;T(e.element).off(H).remove(),e.element=null,e._destroyDraggable(),e.unbind()},options:{appendTo:null,direction:X,resizableElement:null,rtl:!1,template:"<div class='k-table-resize-handle-wrapper' unselectable='on' contenteditable='false'><div class='"+O+"'></div></div>"},events:[F,B,L,U,M],show:function(){this._setPosition()},_setPosition:function(){var e=this,t=e._positioningStrategy.getPosition();T(e.element).css({top:t.top,left:t.left,position:"absolute"})},_attachEventHandlers:function(){var e=this;T(e.element).on(U+H,R(e._onMouseOver,e)).on(M+H,R(e._onMouseOut,e))},_onMouseOver:function(){this.trigger(U)},_onMouseOut:function(){this.trigger(M)},_addStyles:function(){var e=this;T(e.element).children(Z+O).addClass("k-resize-"+e.options.direction)},_initPositioningStrategy:function(){var e=this,t=e.options;e._positioningStrategy=n.create({name:t.direction,handle:e.element,resizableElement:t.resizableElement,rootElement:t.rootElement,rtl:t.rtl})},_initDraggable:function(){var e=this,t=e.element;!e._draggable&&t&&(e._draggable=new A(t,{dragstart:R(e._onDragStart,e),drag:R(e._onDrag,e),dragend:R(e._onDragEnd,e)}))},_onDragStart:function(){this.trigger(F)},_onDrag:function(e){var t=this;t.trigger(B,t._draggingStrategy.adjustDragDelta({deltaX:e.x.delta,deltaY:e.y.delta,initialDeltaX:e.x.initialDelta,initialDeltaY:e.y.initialDelta}))},_onDragEnd:function(){this.trigger(L)},_destroyDraggable:function(){var e=this;e._draggable&&(e._draggable.destroy(),e._draggable=null)},_initDraggingStrategy:function(){var e=this;e._draggingStrategy=f.create({name:e.options.direction})}}),te=z.extend({init:function(){this._items=[]},register:function(e,t){this._items.push({name:e,type:t})},create:function(e){var t,n,i,o=this._items,r=o.length,a=e.name?e.name.toLowerCase():"";for(i=0;i<r;i++)if(n=o[i],n.name.toLowerCase()===a){t=n;break}if(t)return new t.type(e)}}),ne=te.extend({});ne.current=new ne,n=z.extend({init:function(e){var t=this;t.options=_({},t.options,e)},options:{handle:null,offset:W,resizableElement:null,rootElement:null,rtl:!1},getPosition:function(){var e=this,t=e.calculatePosition(),n=e.applyHandleOffset(t),i=e.applyScrollOffset(n);return i},calculatePosition:N,applyHandleOffset:function(e){var t=this.options,n=T(t.handle);return t.offset===W?{top:e.top-I(n)/2,left:e.left-P(n)/2}:e},applyScrollOffset:function(e){var t=this.options,n=T(t.rootElement),i=t.rtl?D(n[0]):0;return n.is(V)?e:{top:e.top+(n.scrollTop()||0),left:e.left+(n.scrollLeft()||0)-i}}}),n.create=function(e){return ne.current.create(e)},i=n.extend({calculatePosition:function(){var e=T(this.options.resizableElement),t=e.position();return{top:t.top+I(e)/2,left:t.left+P(e)}}}),ne.current.register(K,i),o=n.extend({calculatePosition:function(){var e=T(this.options.resizableElement),t=e.position();return{top:t.top,left:t.left+P(e)/2}}}),ne.current.register($,o),r=n.extend({calculatePosition:function(){var e=T(this.options.resizableElement),t=e.position();return{top:t.top,left:t.left+P(e)}}}),ne.current.register(G,r),a=n.extend({calculatePosition:function(){var e=T(this.options.resizableElement),t=e.position();return{top:t.top,left:t.left}}}),ne.current.register(q,a),s=n.extend({calculatePosition:function(){var e=T(this.options.resizableElement),t=e.position();return{top:t.top+I(e),left:t.left+P(e)/2}}}),ne.current.register(Q,s),l=n.extend({calculatePosition:function(){var e=T(this.options.resizableElement),t=e.position();return{top:t.top+I(e),left:t.left+P(e)}}}),ne.current.register(X,l),d=n.extend({calculatePosition:function(){var e=T(this.options.resizableElement),t=e.position();return{top:t.top+I(e),left:t.left}}}),ne.current.register(Y,d),c=n.extend({calculatePosition:function(){var e=T(this.options.resizableElement),t=e.position();return{top:t.top+I(e)/2,left:t.left}}}),ne.current.register(J,c),u=te.extend({}),u.current=new u,f=z.extend({init:function(e){var t=this;t.options=_({},t.options,e)},options:{deltaX:{adjustment:null,modifier:null},deltaY:{adjustment:null,modifier:null}},adjustDragDelta:function(e){var t=this.options,n=t.deltaX.adjustment*t.deltaX.modifier,i=t.deltaY.adjustment*t.deltaY.modifier;return{deltaX:e.deltaX*n,deltaY:e.deltaY*i,initialDeltaX:e.initialDeltaX*n,initialDeltaY:e.initialDeltaY*i}}}),f.create=function(e){return u.current.create(e)},p=f.extend({options:{deltaX:{adjustment:1,modifier:1},deltaY:{adjustment:0,modifier:0}}}),m=p.extend({options:{deltaX:{modifier:1}}}),u.current.register(K,m),h=p.extend({options:{deltaX:{modifier:-1}}}),u.current.register(J,h),g=f.extend({options:{deltaX:{adjustment:0,modifier:0},deltaY:{adjustment:1,modifier:1}}}),b=g.extend({options:{deltaY:{modifier:-1}}}),u.current.register($,b),v=g.extend({options:{deltaY:{modifier:1}}}),u.current.register(Q,v),k=f.extend({options:{deltaX:{adjustment:1,modifier:1},deltaY:{adjustment:1,modifier:1}}}),y=k.extend({options:{deltaX:{modifier:1},deltaY:{modifier:-1}}}),u.current.register(G,y),w=k.extend({options:{deltaX:{modifier:-1},deltaY:{modifier:-1}}}),u.current.register(q,w),x=k.extend({options:{deltaX:{modifier:1},deltaY:{modifier:1}}}),u.current.register(X,x),C=k.extend({options:{deltaX:{modifier:-1},deltaY:{modifier:1}}}),u.current.register(Y,C),_(S,{TableResizeHandle:ee})}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/resizing/table-resizing.min",["editor/main.min","editor/resizing/table-resize-handle.min","editor/resizing/resizing-utils.min"],e)}(function(){!function(e,t){function n(e){return t===e}var i=window,o=i.Math,r=o.min,a=o.max,s=e.jQuery,l=s.contains,d=s.extend,c=s.proxy,u=e.support.browser,f=e.ui.editor,p=e.Class,m=f.TableResizeHandle,h=f.ResizingUtils,g=h.calculatePercentageRatio,b=h.constrain,v=h.inPercentages,k=h.inPixels,y=h.toPercentages,w=h.toPixels,x=e._outerWidth,C=e._outerHeight,T=".kendoEditorTableResizing",_="k-table-resize-handle-wrapper",N="k-table",R="k-table-resizing",S="dragStart",z="drag",A="dragEnd",E="keydown",D="mousedown",P="mouseover",I="mouseout",H="td",O="tr",F="tbody",B="table",L="width",W="height",U="east",M="north",V="northeast",j="northwest",K="south",$="southeast",G="southwest",q="west",Q=".",X=p.extend({init:function(e,t){var n=this;n.options=d({},n.options,t),n.handles=[],s(e).is(B)&&(n.element=e)},destroy:function(){var e=this;s(e.element).off(T),e.element=null,s(e.options.rootElement).off(E+T),e._destroyResizeHandles()},options:{appendHandlesTo:null,rtl:!1,rootElement:null,minWidth:10,minHeight:10,handles:[{direction:j},{direction:M},{direction:V},{direction:U},{direction:$},{direction:K},{direction:G},{direction:q}]},resize:function(e){var t=this,n=d({},{deltaX:0,deltaY:0,initialDeltaX:0,initialDeltaY:0},e);t._resizeWidth(n.deltaX,n.initialDeltaX),t._resizeHeight(n.deltaY,n.initialDeltaY),t.showResizeHandles()},_resizeWidth:function(e,t){var i,o,l,d,c=this,u=s(c.element),f=u[0].style[L],p=x(u),m=u.parent().width(),h=c._getMaxDimensionValue(L);0!==e&&(n(c._initialElementWidth)&&(c._initialElementWidth=p),d=b({value:c._initialElementWidth+t,min:c.options.minWidth,max:h}),v(f)?(p+e>m?(o=a(d,m),l=r(d,m)):(o=r(d,m),l=a(d,m)),i=y(g(o,l))):i=w(d),c._setColumnsWidth(),u[0].style[L]=i)},_resizeHeight:function(e,t){var i,o,l,d,c=this,u=s(c.element),f=u[0].style[W],p=C(u),m=u.parent(),h=m.height(),k=c._getMaxDimensionValue(W),x=c.options.minHeight,T=c._hasRowsInPixels();0!==e&&(n(c._initialElementHeight)&&(c._initialElementHeight=p),d=b({value:c._initialElementHeight+t,min:x,max:k}),T&&e<0&&c._setRowsHeightInPercentages(),v(f)?(p+e>h?(o=a(d,h),l=r(d,h)):(o=r(d,h),l=a(d,h)),i=y(g(o,l))):i=w(d),u[0].style[W]=i,T&&e<0&&c._setRowsHeightInPixels())},_getMaxDimensionValue:function(e){var t=this,n=s(t.element),i=e.toLowerCase(),o=t.options.rtl?-1:1,r=s(t.element).parent(),a=r[0],l=r[i](),d=o*(e===L?r.scrollLeft():r.scrollTop());return a===n.closest(H)[0]?""!==a.style[i]||v(t.element.style[i])?l+d:1/0:l+d},_setColumnsWidth:function(){function e(e){var t=e.style.width;return""!==t?!!v(t):!!s(e).hasClass(N)}var t,n=this,i=s(n.element),o=i.parent()[0],r=i.closest(H),a=r.closest(O).children(),l=a.length;if(e(i[0])&&o===r[0]&&""===o.style[L])for(t=0;t<l;t++)a[t].style[L]=w(s(a[t]).width())},_hasRowsInPixels:function(){var e,t=this,n=s(t.element).children(F).children(O);for(e=0;e<n.length;e++)if(""===n[e].style.height||k(n[e].style.height))return!0;return!1},_setRowsHeightInPercentages:function(){var e,t=this,n=s(t.element).children(F),i=n.height(),o=n.children(O),r=o.length,a=o.map(function(){return C(s(this))});for(e=0;e<r;e++)o[e].style[W]=y(g(a[e],i))},_setRowsHeightInPixels:function(){var e,t=this,n=s(t.element).children(F).children(O),i=n.length,o=n.map(function(){return C(s(this))});for(e=0;e<i;e++)n[e].style[W]=w(o[e])},showResizeHandles:function(){var e=this;e._initResizeHandles(),e._showResizeHandles()},_initResizeHandles:function(){var e,t=this,n=t.handles,i=t.options,o=t.options.handles,r=o.length;if(!(n&&n.length>0)){for(e=0;e<r;e++)t.handles.push(new m(d({appendTo:i.appendHandlesTo,resizableElement:t.element,rootElement:i.rootElement,rtl:i.rtl},o[e])));t._bindToResizeHandlesEvents()}},_destroyResizeHandles:function(){var e,t=this,n=t.handles?t.handles.length:0;for(e=0;e<n;e++)t.handles[e].destroy()},_showResizeHandles:function(){var e,t=this,n=t.handles||[],i=n.length;for(e=0;e<i;e++)t.handles[e].show()},_bindToResizeHandlesEvents:function(){var e,t,n=this,i=n.handles||[],o=i.length;for(e=0;e<o;e++)t=i[e],t.bind(S,c(n._onResizeHandleDragStart,n)),t.bind(z,c(n._onResizeHandleDrag,n)),t.bind(A,c(n._onResizeHandleDragEnd,n)),t.bind(P,c(n._onResizeHandleMouseOver,n)),t.bind(I,c(n._onResizeHandleMouseOut,n))},_onResizeHandleDragStart:function(){var e=this,t=s(e.element);t.addClass(R),e._initialElementHeight=C(t),e._initialElementWidth=x(t),e._disableKeyboard()},_onResizeHandleDrag:function(e){this.resize(e)},_onResizeHandleDragEnd:function(){var e=this;s(e.element).removeClass(R),e._enableKeyboard()},_enableKeyboard:function(){s(this.options.rootElement).off(E+T)},_disableKeyboard:function(){s(this.options.rootElement).on(E+T,function(e){e.preventDefault()})}}),Y=p.extend({create:function(e){var t=this;s(e.body).on(D+T,B,function(n){var i=n.target,o=n.currentTarget,r=e.tableResizing,a=r?r.element:null;if(r){if(a&&o!==a){if(l(o,a)&&a!==i&&l(a,i))return;a!==i&&(e._destroyTableResizing(),t._initResizing(e,o))}}else t._initResizing(e,o);e._showTableResizeHandles()}).on(D+T,function(t){var n=e.tableResizing,i=n?n.element:null,o=t.target,r=s(o).hasClass(_)||s(o).parents(Q+_).length>0;!n||i===o||l(i,o)||r||e._destroyTableResizing()})},dispose:function(e){s(e.body).off(T)},_initResizing:function(t,n){u.msie||u.mozilla||(t.tableResizing=new X(n,{appendHandlesTo:t.body,rtl:e.support.isRtl(t.element),rootElement:t.body}))}});Y.current=new Y,X.create=function(e){Y.current.create(e)},X.dispose=function(e){Y.current.dispose(e)},d(f,{TableResizing:X})}(window.kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/table-wizard/table-wizard-command.min",["editor/plugins/tables.min"],e)}(function(){!function(e,t){var n=window.kendo,i=n.ui.editor,o=i.EditorUtils,r=i.RangeUtils,a=i.Dom,s=o.registerTool,l=i.ToolTemplate,d=i.Command,c=new i.BlockFormatFinder([{tags:["table"]}]),u=new i.BlockFormatFinder([{tags:["td","th"]}]),f=/([a-z]+|%)$/i,p=d.extend({exec:function(){var o=this,r=o.editor,a=o.range=o.lockRange(),s=o._sourceTable=o.options.insertNewTable?t:o._selectedTable(a),l=o._selectedTableCells=s?o._selectedCells(a):t,d={visible:!1,messages:r.options.messages,closeCallback:e.proxy(o.onDialogClose,o),table:o.parseTable(s,l),dialogOptions:r.options.dialogOptions,isRtl:n.support.isRtl(r.wrapper)},c=new i.TableWizardDialog(d);c.open()},onDialogClose:function(e){var t=this;t.releaseRange(t.range),e&&(t.options.insertNewTable?t.insertTable(t.createNewTable(e)):t.updateTable(e,t._sourceTable,t._selectedTableCells))},releaseRange:function(e){var t=this,n=t.editor.document;a.windowFromDocument(n).focus(),d.fn.releaseRange.call(t,e)},insertTable:function(e){var t=this.range;t.insertNode(e),t.collapse(!0),this.editor.selectRange(t),this._ensureFocusableAfterTable(e)},_ensureFocusableAfterTable:function(t){for(var n=e(t).parent().contents(),i=n.length-1,o=n.get(i);null!==o.nodeValue&&(" "===o.nodeValue||""===o.nodeValue);)i-=1,o=n.get(i);o===t&&a.insertAfter(a.createEmptyNode(this.editor.document,"p"),t)},updateTable:function(t,n,i){for(var o,r,s,l,d,c,u,f,p=this,m=e(n.rows).toArray(),h=t.tableProperties,g=h.rows,b=h.columns,v=function(e){return e[e.length-1]};i.length>1;)i.pop();if(o=i.length?v(i).parentNode:v(m),p._deleteTableRows(m,m.length-g),m.length<g)for(l=e(o).index(),d=o.cells.length,c=g-m.length,s=o.parentNode;c;)r=s.insertRow(l+1),p._insertCells(d-r.cells.length,r),c--;m[0].cells.length>b&&e(m).each(function(e,t){for(;t.cells.length>b;)t.deleteCell(-1)}),m[0].cells.length<b&&(u=e(v(i)||v(o.cells)).index(),e(m).each(function(e,t){p._insertCells(b-t.cells.length,t,u+1)})),p._updateTableProperties(n,h),f=t.cellProperties,i[0]&&a.attr(i[0],{id:f.id||null}),(f.selectAllCells?e(m).children():e(i)).each(function(e,t){p._updateCellProperties(t,f)}),p._updateCaption(n,h),h.cellsWithHeaders=h.cellsWithHeaders||!1,p.cellsWithHeadersAssociated(n)!=h.cellsWithHeaders&&p.associateCellsWithHeader(n,h.cellsWithHeaders)},_isHeadingRow:function(e){return a.is(e.parentNode,"thead")||a.is(e.cells[0],"th")},associateCellsWithHeader:function(t,n){var i,o,r,a=(new Date).getTime(),s=[],l=t.rows[0].cells.length,d=function(){for(var e=0;e<l;e++)s[e]="table"+ ++a},c=function(t,i){e(i)[n?"attr":"removeAttr"]("id",s[t])},u=function(t,i){e(i)[n?"attr":"removeAttr"]("headers",s[t])},f=this._isHeadingRow;e(t.rows).each(function(n,a){if(f(a))for(i=n,o=t.rows[++i],r=o&&!f(o),r&&(d(),e(a.cells).each(c));r;)e(o.cells).each(u),o=t.rows[++i],r=o&&!f(o)})},cellsWithHeadersAssociated:function(t){var n,i=e(t.rows).children(),o=this._isHeadingRow,r=[];return i.each(function(e,t){t.id&&o(t.parentNode)&&r.push(t.id)}),n=i.filter(function(t,n){var i=n.getAttribute("headers");return i&&!o(n.parentNode)&&e.inArray(i,r)>-1}),!!n.length},_insertCells:function(e,t,n){n=isNaN(n)?-1:n;for(var i,o=0;o<e;o++)i=t.insertCell(n),i.innerHTML="&nbsp;"},_deleteTableRows:function(e,t){for(var n,i,o=0;o<t;o++)n=e.pop(),i=n.parentNode,i.removeChild(n),i.rows.length||a.remove(i)},createNewTable:function(e){var t,n,i,o,r,s=this,l=s.editor.document,d=e.tableProperties,c=e.cellProperties,u=c.selectAllCells,f=a.create(l,"table");for(s._updateTableProperties(f,d),s._updateCaption(f,d),t=f.createTBody(),n=0;n<d.rows;n++)for(i=t.insertRow(),o=0;o<d.columns;o++)r=i.insertCell(),r.innerHTML="&nbsp;",0===n&&0===o&&c.id&&(r.id=c.id),s._updateCellProperties(r,u||0===n&&0===o?c:{});return d.cellsWithHeaders&&s.associateCellsWithHeader(f,d.cellsWithHeaders),f},_updateTableProperties:function(t,n){var i=this._getStylesData(n);a.attr(t,{cellSpacing:n.cellSpacing||null,cellPadding:n.cellPadding||null,className:n.className||null,id:n.id||null,summary:n.summary||null,style:i||null}),e(t).addClass("k-table")},_updateCellProperties:function(e,t){var n=this._getStylesData(t);n.padding=t.cellPadding||null,n.margin=t.cellMargin||null,a.attr(e,{style:n||null,className:t.className||null})},_updateCaption:function(e,t){var n,i;e.caption&&!t.captionContent?e.deleteCaption():t.captionContent&&(n=e.createCaption(),n.innerHTML=t.captionContent,i=this._getAlignmentData(t.captionAlignment),a.attr(n,{style:{textAlign:i.textAlign,verticalAlign:i.verticalAlign}}))},_getStylesData:function(e){var t=this._getAlignmentData(e.alignment),n="wrapText"in e?e.wrapText?"":"nowrap":null;return{width:e.width?e.width+e.widthUnit:null,height:e.height?e.height+e.heightUnit:null,textAlign:t.textAlign,verticalAlign:t.verticalAlign,backgroundColor:e.bgColor||"",borderWidth:e.borderWidth,borderStyle:e.borderStyle,borderColor:e.borderColor||"",borderCollapse:e.collapseBorders?"collapse":null,whiteSpace:n}},_getAlignmentData:function(e){var t,n="",i=n;return e&&(e.indexOf(" ")!=-1?(t=e.split(" "),n=t[0],i=t[1]):n=e),{textAlign:n,verticalAlign:i}},parseTable:function(n,i){var o,r,a,s,l,d,c,u,f,p;return n?(o=this,r=n.style,a=n.rows,s=n.caption,l=e(s?s.cloneNode(!0):t),l.find(".k-marker").remove(),d=n.className,d=d.replace(/^k-table\s|\sk-table$/,""),d=d.replace(/\sk-table\s/," "),d=d.replace(/^k-table$/,""),c=o._getAlignment(n,!0),u=s?o._getAlignment(s):t,f=o.cellsWithHeadersAssociated(n),p={tableProperties:{width:r.width||n.width?parseFloat(r.width||n.width):null,height:r.height||n.height?parseFloat(r.height||n.height):null,columns:a[0]?a[0].children.length:0,rows:a.length,widthUnit:o._getUnit(r.width),heightUnit:o._getUnit(r.height),cellSpacing:n.cellSpacing,cellPadding:n.cellPadding,alignment:c.textAlign,bgColor:r.backgroundColor||n.bgColor,className:d,id:n.id,borderWidth:r.borderWidth||n.border,borderColor:r.borderColor,borderStyle:r.borderStyle||"",collapseBorders:!!r.borderCollapse,summary:n.summary,captionContent:s?l.html():"",captionAlignment:s&&u.textAlign?u.textAlign+" "+u.verticalAlign:"",cellsWithHeaders:f},selectedCells:[]},p.rows=o.parseTableRows(a,i,p),p):{tableProperties:{},selectedCells:[]}},parseTableRows:function(t,n,i){var o,r,a,s,l,d,c,u=this,f=[];for(d=0;d<t.length;d++)for(o=t[d],r={cells:[]},a=o.cells,f.push(r),c=0;c<a.length;c++)s=a[c],l=u.parseCell(s),e.inArray(s,n)!=-1&&i.selectedCells.push(l),r.cells.push(l);return f},parseCell:function(e){var t,n=this,i=e.style,o=n._getAlignment(e);return o=o.textAlign?o.textAlign+" "+o.verticalAlign:"",t={width:i.width||e.width?parseFloat(i.width||e.width):null,height:i.height||e.height?parseFloat(i.height||e.height):null,widthUnit:n._getUnit(i.width),heightUnit:n._getUnit(i.height),cellMargin:i.margin,cellPadding:i.padding,alignment:o,bgColor:i.backgroundColor||e.bgColor,className:e.className,id:e.id,borderWidth:i.borderWidth||e.border,borderColor:i.borderColor,borderStyle:i.borderStyle,wrapText:"nowrap"!=i.whiteSpace}},_getAlignment:function(e,t){var n,i=e.style,o=i.textAlign||e.align||"";return t?{textAlign:o}:(n=i.verticalAlign||e.vAlign||"",o&&n?{textAlign:o,verticalAlign:n}:!o&&n?{textAlign:"left",verticalAlign:n}:o&&!n?{textAlign:o,verticalAlign:"top"}:{textAlign:"",verticalAlign:""})},_getUnit:function(e){var t=(e||"").match(f);return t?t[0]:"px"},_selectedTable:function(e){var t=a.filterBy(r.nodes(e),a.htmlIndentSpace,!0);return c.findSuitable(t)[0]},_selectedCells:function(e){var t=a.filterBy(r.nodes(e),a.htmlIndentSpace,!0);return u.findSuitable(t)}}),m=i.Tool.extend({command:function(e){return e.insertNewTable=this.options.insertNewTable,new p(e)}}),h=m.extend({update:function(e,t){var n=!c.isFormatted(t);e.toggleClass("k-state-disabled",n)}});n.ui.editor.TableWizardTool=m,n.ui.editor.TableWizardCommand=p,s("tableWizard",new h({command:p,insertNewTable:!1,template:new l({template:o.buttonTemplate,title:"Table Wizard"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/table-wizard/table-wizard-dialog.min",["editor/table-wizard/table-wizard-command.min","kendo.tabstrip.min"],e)}(function(){!function(e,t){var n=window.kendo,i={format:"0",min:0},o=["px","em"],r=["solid","dotted","dashed","double","groove","ridge","inset","outset","initial","inherit","none","hidden"],a={dataSource:[{className:"k-icon k-i-table-align-middle-left",value:"left"},{className:"k-icon k-i-table-align-middle-center",value:"center"},{className:"k-icon k-i-table-align-middle-right",value:"right"},{className:"k-icon k-i-align-remove",value:""}],dataTextField:"className",dataValueField:"value",template:"<span class='#: className #' title='#: tooltip #'></span>",valueTemplate:"<span class='k-align-group #: className #' title='#: tooltip #'></span>"},s={dataSource:[{className:"k-icon k-i-table-align-top-left",value:"left top"},{className:"k-icon k-i-table-align-top-center",value:"center top"},{className:"k-icon k-i-table-align-top-right",value:"right top"},{className:"k-icon k-i-table-align-middle-left",value:"left middle"},{className:"k-icon k-i-table-align-middle-center",value:"center middle"},{className:"k-icon k-i-table-align-middle-right",value:"right middle"},{className:"k-icon k-i-table-align-bottom-left",value:"left bottom"},{className:"k-icon k-i-table-align-bottom-center",value:"center bottom"},{className:"k-icon k-i-table-align-bottom-right",value:"right bottom"},{className:"k-icon k-i-align-remove",value:""}],dataTextField:"className",dataValueField:"value",template:"<span class='#: className #' title='#: tooltip #'></span>",valueTemplate:"<span class='k-align-group #: className #' title='#: tooltip #'></span>"},l={dataSource:[{className:"k-icon k-i-table-align-top-left",value:"left top"},{className:"k-icon k-i-table-align-top-center",value:"center top"},{className:"k-icon k-i-table-align-top-right",value:"right top"},{className:"k-icon k-i-table-align-bottom-left",value:"left bottom"},{className:"k-icon k-i-table-align-bottom-center",value:"center bottom"},{className:"k-icon k-i-table-align-bottom-right",value:"right bottom"},{className:"k-icon k-i-align-remove",value:""}],dataTextField:"className",dataValueField:"value",template:"<span class='#: className #' title='#: tooltip #'></span>",valueTemplate:"<span class='k-align-group #: className #' title='#: tooltip #'></span>"},d='<div class="k-editor-dialog k-editor-table-wizard-dialog k-action-window k-popup-edit-form"><div class="k-edit-form-container"><div id="k-table-wizard-tabs" class="k-root-tabs"><ul><li class="k-state-active">#= messages.tableTab #</li><li>#= messages.cellTab #</li><li>#= messages.accessibilityTab #</li></ul><div id="k-table-properties"><div class="k-edit-label"><label for="k-editor-table-width">#= messages.width #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-table-width" /><input id="k-editor-table-width-type" aria-label="#= messages.units #" /></div><div class="k-edit-label"><label for="k-editor-table-height">#= messages.height #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-table-height" /><input id="k-editor-table-height-type" aria-label="#= messages.units #" /></div><div class="k-edit-label"><label for="k-editor-table-columns">#= messages.columns #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-table-columns" /></div><div class="k-edit-label"><label for="k-editor-table-rows">#= messages.rows #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-table-rows" /></div><div class="k-edit-label"><label for="k-editor-table-cell-spacing">#= messages.cellSpacing #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-table-cell-spacing" /></div><div class="k-edit-label"><label for="k-editor-table-cell-padding">#= messages.cellPadding #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-table-cell-padding" /></div><div class="k-edit-label"><label for="k-editor-table-alignment">#= messages.alignment #</label></div><div class="k-edit-field"><input id="k-editor-table-alignment" class="k-align" /></div><div class="k-edit-label"><label for="k-editor-table-bg">#= messages.background #</label></div><div class="k-edit-field"><input id="k-editor-table-bg" /></div><div class="k-edit-label"><label for="k-editor-css-class">#= messages.cssClass #</label></div><div class="k-edit-field"><input id="k-editor-css-class" class="k-textbox" type="text" /></div><div class="k-edit-label"><label for="k-editor-id">#= messages.id #</label></div><div class="k-edit-field"><input id="k-editor-id" class="k-textbox" type="text" /></div><div class="k-edit-label"><label for="k-editor-border-width">#= messages.border #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-border-width" /><input id="k-editor-border-color" /></div><div class="k-edit-label"><label for="k-editor-border-style">#= messages.borderStyle #</label></div><div class="k-edit-field"><input id="k-editor-border-style" /></div><div class="k-edit-label">&nbsp;</div><div class="k-edit-field"><input id="k-editor-collapse-borders" type="checkbox" class="k-checkbox" /><label for="k-editor-collapse-borders" class="k-checkbox-label">#= messages.collapseBorders #</label></div></div><div id="k-cell-properties"><div class="k-edit-field"><input id="k-editor-selectAllCells" type="checkbox" class="k-checkbox" /><label for="k-editor-selectAllCells" class="k-checkbox-label">#= messages.selectAllCells #</label></div><div class="k-edit-label"><label for="k-editor-cell-width">#= messages.width #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-cell-width" /><input id="k-editor-cell-width-type" aria-label="#= messages.units #" /></div><div class="k-edit-label"><label for="k-editor-cell-height">#= messages.height #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-cell-height" /><input id="k-editor-cell-height-type" aria-label="#= messages.units #" /></div><div class="k-edit-label"><label for="k-editor-table-cell-margin">#= messages.cellMargin #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-table-cell-margin" /></div><div class="k-edit-label"><label for="k-editor-table-cells-padding">#= messages.cellPadding #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-table-cells-padding" /></div><div class="k-edit-label"><label for="k-editor-cell-alignment">#= messages.alignment #</label></div><div class="k-edit-field"><input id="k-editor-cell-alignment" class="k-align" /></div><div class="k-edit-label"><label for="k-editor-cell-bg">#= messages.background #</label></div><div class="k-edit-field"><input id="k-editor-cell-bg" /></div><div class="k-edit-label"><label for="k-editor-cell-css-class">#= messages.cssClass #</label></div><div class="k-edit-field"><input id="k-editor-cell-css-class" class="k-textbox" type="text" /></div><div class="k-edit-label"><label for="k-editor-cell-id">#= messages.id #</label></div><div class="k-edit-field"><input id="k-editor-cell-id" class="k-textbox" type="text" /></div><div class="k-edit-label"><label for="k-editor-cell-border-width">#= messages.border #</label></div><div class="k-edit-field"><input type="numeric" id="k-editor-cell-border-width" /><input id="k-editor-cell-border-color" /></div><div class="k-edit-label"><label for="k-editor-cell-border-style">#= messages.borderStyle #</label></div><div class="k-edit-field"><input id="k-editor-cell-border-style" /></div><div class="k-edit-label">&nbsp;</div><div class="k-edit-field"><input id="k-editor-wrap-text" type="checkbox" class="k-checkbox" /><label for="k-editor-wrap-text" class="k-checkbox-label">#= messages.wrapText #</label></div></div><div id="k-accessibility-properties"><div class="k-edit-label"><label for="k-editor-table-caption">#= messages.caption #</label></div><div class="k-edit-field"><input id="k-editor-table-caption" class="k-textbox" type="text" /></div><div class="k-edit-label"><label for="k-editor-accessibility-alignment">#= messages.alignment #</label></div><div class="k-edit-field"><input id="k-editor-accessibility-alignment" class="k-align" /></div><div class="k-edit-label"><label for="k-editor-accessibility-summary">#= messages.summary #</label></div><div class="k-edit-field"><textarea id="k-editor-accessibility-summary" class="k-textbox"></textarea></div><div class="k-edit-label">&nbsp;</div><div class="k-edit-field"><input id="k-editor-cells-headers" type="checkbox" class="k-checkbox" /><label for="k-editor-cells-headers" class="k-checkbox-label">#= messages.associateCellsWithHeaders #</label></div></div></div><div class="k-edit-buttons k-state-default"><button class="k-button k-primary k-dialog-ok">#= messages.dialogOk #</button><button class="k-button k-dialog-close">#= messages.dialogCancel #</button></div></div></div>',c=n.Class.extend({
init:function(e){this.options=e},open:function(){function t(e){e.preventDefault(),l.destroy(),r.destroy()}function i(e){l.collectDialogValues(u),t(e),l.change&&l.change(),d.closeCallback(u)}function o(e){t(e),d.closeCallback()}var r,a,s,l=this,d=l.options,c=d.dialogOptions,u=d.table,f=d.messages,p=n.support.browser.msie;c.close=o,c.title=f.tableWizard,c.visible=d.visible,r=e(l._dialogTemplate(f)).appendTo(document.body).kendoWindow(c).closest(".k-window").toggleClass("k-rtl",d.isRtl).end().find(".k-dialog-ok").click(i).end().find(".k-dialog-close").click(o).end().data("kendoWindow"),a=r.element,l._initTabStripComponent(a),l._initTableViewComponents(a,u),l._initCellViewComponents(a,u),l._initAccessibilityViewComponents(a,u),r.center(),r.open(),p&&(s=a.closest(".k-window").height(),a.css("max-height",s))},_initTabStripComponent:function(e){var t=this.components={};t.tabStrip=e.find("#k-table-wizard-tabs").kendoTabStrip({animation:!1}).data("kendoTabStrip")},collectDialogValues:function(){var e=this,t=e.options.table;e._collectTableViewValues(t),e._collectCellViewValues(t),e._collectAccessibilityViewValues(t)},_collectTableViewValues:function(e){var t=this.components.tableView,n=e.tableProperties;n.width=t.width.value(),n.widthUnit=t.widthUnit.value(),n.height=t.height.value(),n.columns=t.columns.value(),n.rows=t.rows.value(),n.heightUnit=t.heightUnit.value(),n.cellSpacing=t.cellSpacing.value(),n.cellPadding=t.cellPadding.value(),n.alignment=t.alignment.value(),n.bgColor=t.bgColor.value(),n.className=t.className.value,n.id=t.id.value,n.borderWidth=t.borderWidth.value(),n.borderColor=t.borderColor.value(),n.borderStyle=t.borderStyle.value(),n.collapseBorders=t.collapseBorders.checked},_collectCellViewValues:function(e){var t=e.cellProperties={},n=this.components.cellView;t.selectAllCells=n.selectAllCells.checked,t.width=n.width.value(),t.widthUnit=n.widthUnit.value(),t.height=n.height.value(),t.heightUnit=n.heightUnit.value(),t.cellMargin=n.cellMargin.value(),t.cellPadding=n.cellPadding.value(),t.alignment=n.alignment.value(),t.bgColor=n.bgColor.value(),t.className=n.className.value,t.id=n.id.value,t.borderWidth=n.borderWidth.value(),t.borderColor=n.borderColor.value(),t.borderStyle=n.borderStyle.value(),t.wrapText=n.wrapText.checked,t.width||(t.selectAllCells=!0,t.width=100/e.tableProperties.columns,t.widthUnit="%")},_collectAccessibilityViewValues:function(e){var t=e.tableProperties,n=this.components.accessibilityView;t.captionContent=n.captionContent.value,t.captionAlignment=n.captionAlignment.value(),t.summary=n.summary.value,t.cellsWithHeaders=n.cellsWithHeaders.checked},_addUnit:function(t,n){n&&e.inArray(n,t)==-1&&t.push(n)},_initTableViewComponents:function(e,t){var n=this.components,i=n.tableView={},a=t.tableProperties=t.tableProperties||{};a.borderStyle=a.borderStyle||"",this._addUnit(o,a.widthUnit),this._addUnit(o,a.heightUnit),this._initNumericTextbox(e.find("#k-editor-table-width"),"width",a,i),this._initNumericTextbox(e.find("#k-editor-table-height"),"height",a,i),this._initNumericTextbox(e.find("#k-editor-table-columns"),"columns",a,i,{min:1,value:4}),this._initNumericTextbox(e.find("#k-editor-table-rows"),"rows",a,i,{min:1,value:4}),this._initDropDownList(e.find("#k-editor-table-width-type"),"widthUnit",a,i,o),this._initDropDownList(e.find("#k-editor-table-height-type"),"heightUnit",a,i,o),this._initNumericTextbox(e.find("#k-editor-table-cell-spacing"),"cellSpacing",a,i),this._initNumericTextbox(e.find("#k-editor-table-cell-padding"),"cellPadding",a,i),this._initTableAlignmentDropDown(e.find("#k-editor-table-alignment"),a),this._initColorPicker(e.find("#k-editor-table-bg"),"bgColor",a,i),this._initInput(e.find("#k-editor-css-class"),"className",a,i),this._initInput(e.find("#k-editor-id"),"id",a,i),this._initNumericTextbox(e.find("#k-editor-border-width"),"borderWidth",a,i),this._initColorPicker(e.find("#k-editor-border-color"),"borderColor",a,i),this._initDropDownList(e.find("#k-editor-border-style"),"borderStyle",a,i,r),this._initCheckbox(e.find("#k-editor-collapse-borders"),"collapseBorders",a,i)},_initCellViewComponents:function(e,t){var n,i=this.components,a=i.cellView={};t.selectedCells=t.selectedCells=t.selectedCells||[],n=t.selectedCells[0]||{borderStyle:"",wrapText:!0},this._addUnit(o,n.widthUnit),this._addUnit(o,n.heightUnit),this._initCheckbox(e.find("#k-editor-selectAllCells"),"selectAllCells",t.tableProperties,a),this._initNumericTextbox(e.find("#k-editor-cell-width"),"width",n,a),this._initNumericTextbox(e.find("#k-editor-cell-height"),"height",n,a),this._initDropDownList(e.find("#k-editor-cell-width-type"),"widthUnit",n,a,o),this._initDropDownList(e.find("#k-editor-cell-height-type"),"heightUnit",n,a,o),this._initNumericTextbox(e.find("#k-editor-table-cell-margin"),"cellMargin",n,a),this._initNumericTextbox(e.find("#k-editor-table-cells-padding"),"cellPadding",n,a),this._initCellAlignmentDropDown(e.find("#k-editor-cell-alignment"),n),this._initColorPicker(e.find("#k-editor-cell-bg"),"bgColor",n,a),this._initInput(e.find("#k-editor-cell-css-class"),"className",n,a),this._initInput(e.find("#k-editor-cell-id"),"id",n,a),this._initNumericTextbox(e.find("#k-editor-cell-border-width"),"borderWidth",n,a),this._initColorPicker(e.find("#k-editor-cell-border-color"),"borderColor",n,a),this._initDropDownList(e.find("#k-editor-cell-border-style"),"borderStyle",n,a,r),this._initCheckbox(e.find("#k-editor-wrap-text"),"wrapText",n,a)},_initAccessibilityViewComponents:function(e,t){var n=this.components,i=n.accessibilityView={},o=t.tableProperties;this._initInput(e.find("#k-editor-table-caption"),"captionContent",o,i),this._initAccessibilityAlignmentDropDown(e.find("#k-editor-accessibility-alignment"),o),this._initInput(e.find("#k-editor-accessibility-summary"),"summary",o,i),this._initCheckbox(e.find("#k-editor-cells-headers"),"cellsWithHeaders",o,i)},_initNumericTextbox:function(t,n,o,r,a){var s=r[n]=t.kendoNumericTextBox(a?e.extend({},i,a):i).data("kendoNumericTextBox");n in o&&s.value(parseInt(o[n],10))},_initDropDownList:function(e,t,n,i,o){var r=i[t]=e.kendoDropDownList({dataSource:o}).data("kendoDropDownList");this._setComponentValue(r,n,t)},_initTableAlignmentDropDown:function(e,t){var n=this.options.messages,i=this.components.tableView,o=a.dataSource;o[0].tooltip=n.alignLeft,o[1].tooltip=n.alignCenter,o[2].tooltip=n.alignRight,o[3].tooltip=n.alignRemove,this._initAlignmentDropDown(e,a,"alignment",t,i)},_initCellAlignmentDropDown:function(e,t){var n=this.options.messages,i=this.components.cellView,o=s.dataSource;o[0].tooltip=n.alignLeftTop,o[1].tooltip=n.alignCenterTop,o[2].tooltip=n.alignRightTop,o[3].tooltip=n.alignLeftMiddle,o[4].tooltip=n.alignCenterMiddle,o[5].tooltip=n.alignRightMiddle,o[6].tooltip=n.alignLeftBottom,o[7].tooltip=n.alignCenterBottom,o[8].tooltip=n.alignRightBottom,o[9].tooltip=n.alignRemove,this._initAlignmentDropDown(e,s,"alignment",t,i)},_initAccessibilityAlignmentDropDown:function(e,t){var n=this.options.messages,i=this.components.accessibilityView,o=l.dataSource;o[0].tooltip=n.alignLeftTop,o[1].tooltip=n.alignCenterTop,o[2].tooltip=n.alignRightTop,o[3].tooltip=n.alignLeftBottom,o[4].tooltip=n.alignCenterBottom,o[5].tooltip=n.alignRightBottom,o[6].tooltip=n.alignRemove,this._initAlignmentDropDown(e,l,"captionAlignment",t,i)},_initAlignmentDropDown:function(e,t,n,i,o){var r=o[n]=e.kendoDropDownList(t).data("kendoDropDownList");r.list.addClass("k-align").css("width","110px"),this._setComponentValue(r,i,n)},_setComponentValue:function(e,t,n){n in t&&e.value(t[n])},_initColorPicker:function(e,t,n,i){var o=i[t]=e.kendoColorPicker({buttons:!1,clearButton:!0}).data("kendoColorPicker");n[t]&&o.value(n[t])},_initInput:function(e,t,n,i){var o=i[t]=e.get(0);t in n&&(o.value=n[t])},_initCheckbox:function(e,t,n,i){var o=i[t]=e.get(0);t in n&&(o.checked=n[t])},destroy:function(){this._destroyComponents(this.components.tableView),this._destroyComponents(this.components.cellView),this._destroyComponents(this.components.accessibilityView),this._destroyComponents(this.components),delete this.components},_destroyComponents:function(e){for(var t in e)e[t].destroy&&e[t].destroy(),delete e[t]},_dialogTemplate:function(e){return n.template(d)({messages:e})}});n.ui.editor.TableWizardDialog=c}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.editor.min",["kendo.combobox.min","kendo.dropdownlist.min","kendo.resizable.min","kendo.window.min","kendo.colorpicker.min","kendo.imagebrowser.min","kendo.numerictextbox.min","util/undoredostack.min","editor/main.min","editor/dom.min","editor/serializer.min","editor/range.min","editor/command.min","editor/components.min","editor/toolbar.min","editor/immutables.min","editor/plugins/viewhtml.min","editor/plugins/link.min","editor/plugins/lists.min","editor/plugins/formatting.min","editor/plugins/image.min","editor/plugins/import.min","editor/plugins/insert.min","editor/plugins/export.min","editor/plugins/indent.min","editor/plugins/linebreak.min","editor/plugins/format.min","editor/plugins/inlineformat.min","editor/plugins/formatblock.min","editor/plugins/file.min","editor/plugins/tables.min","editor/plugins/clipboard.min","editor/plugins/keyboard.min","editor/plugins/exportpdf.min","editor/plugins/print.min","editor/resizing/column-resizing.min","editor/resizing/row-resizing.min","editor/resizing/table-resizing.min","editor/resizing/table-resize-handle.min","editor/table-wizard/table-wizard-command.min","editor/table-wizard/table-wizard-dialog.min"],e)}(function(){return window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.editor.min.js.map
