{"version": 3, "sources": ["kendo.mobile.switch.js"], "names": ["f", "define", "$", "undefined", "className", "name", "limitValue", "value", "minLimit", "maxLimit", "Math", "max", "min", "kendo", "window", "ui", "mobile", "outerWidth", "_outerWidth", "Widget", "support", "CHANGE", "SWITCHON", "SWITCHOFF", "MARGINLEFT", "ACTIVE_STATE", "DISABLED_STATE", "DISABLED", "RESOLVEDPREFIX", "transitions", "css", "TRANSFORMSTYLE", "proxy", "SWITCH_MARKUP", "Switch", "extend", "init", "element", "options", "checked", "that", "this", "fn", "call", "wrapper", "format", "onLabel", "offLabel", "handle", "find", "background", "insertBefore", "prepend", "_drag", "origin", "parseInt", "constrain", "snapPoint", "type", "_animateBackground", "check", "enable", "attr", "refresh", "notify", "handleWidth", "width", "data", "events", "_position", "toggleClass", "apply", "arguments", "destroy", "userEvents", "toggle", "removeAttr", "_resize", "_move", "e", "preventDefault", "position", "x", "delta", "_start", "capture", "addClass", "cancel", "_stop", "removeClass", "_toggle", "distance", "duration", "application", "os", "wp", "kendoStop", "kendoAnimate", "effects", "offset", "reset", "reverse", "axis", "complete", "trigger", "UserEvents", "fastTap", "tap", "start", "move", "end", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,uBACH,WACA,oBACDD,IACL,WA2KE,MAhKC,UAAUE,EAAGC,GAEV,QAASC,GAAUC,GACf,MAAO,MAAQA,EAEnB,QAASC,GAAWC,EAAOC,EAAUC,GACjC,MAAOC,MAAKC,IAAIH,EAAUE,KAAKE,IAAIH,EAAUF,IANpD,GACOM,GAAQC,OAAOD,MAAOE,EAAKF,EAAMG,OAAOD,GAAIE,EAAaJ,EAAMK,YAAaC,EAASJ,EAAGI,OAAQC,EAAUP,EAAMO,QAASC,EAAS,SAAUC,EAAW,YAAaC,EAAY,aAAcC,EAAa,cAAeC,EAAe,eAAgBC,EAAiB,iBAAkBC,EAAW,WAAYC,EAAiBR,EAAQS,YAAYC,MAAQ3B,EAAY,GAAKiB,EAAQS,YAAYC,IAAKC,EAAiBH,EAAiB,YAAaI,EAAQ9B,EAAE8B,MAOncC,EAAgB,gBAAkB7B,EAAU,UAAY,IAAMA,EAAU,UAAY,0BAA4BA,EAAU,kBAAoB,8BAAgCA,EAAU,qBAAuB,iDAAmDA,EAAU,oBAAsB,8BAAgCA,EAAU,iBAAmB,mCAAqCA,EAAU,mBAAqB,6CAA+CA,EAAU,oBAAsB,8DAClf8B,EAASf,EAAOgB,QAChBC,KAAM,SAAUC,EAASC,GACrB,GAAiBC,GAAbC,EAAOC,IACXtB,GAAOuB,GAAGN,KAAKO,KAAKH,EAAMH,EAASC,GACnCA,EAAUE,EAAKF,QACfE,EAAKI,QAAU1C,EAAEW,EAAMgC,OAAOZ,EAAeK,EAAQQ,QAASR,EAAQS,WACtEP,EAAKQ,OAASR,EAAKI,QAAQK,KAAK,qBAChCT,EAAKU,WAAaV,EAAKI,QAAQK,KAAK,yBACpCT,EAAKI,QAAQO,aAAaX,EAAKH,SAASe,QAAQZ,EAAKH,SACrDG,EAAKa,QACLb,EAAKc,OAASC,SAASf,EAAKU,WAAWpB,IAAIN,GAAa,IACxDgB,EAAKgB,UAAY,EACjBhB,EAAKiB,UAAY,EACjBpB,EAAUG,EAAKH,QAAQ,GACvBA,EAAQqB,KAAO,WACflB,EAAKmB,oBAAqB,EAC1BpB,EAAUC,EAAKF,QAAQC,QACP,OAAZA,IACAA,EAAUF,EAAQE,SAEtBC,EAAKoB,MAAMrB,GACXC,EAAKF,QAAQuB,OAASrB,EAAKF,QAAQuB,SAAWrB,EAAKH,QAAQyB,KAAKnC,GAChEa,EAAKqB,OAAOrB,EAAKF,QAAQuB,QACzBrB,EAAKuB,UACLlD,EAAMmD,OAAOxB,EAAM3B,EAAMG,OAAOD,KAEpCgD,QAAS,WACL,GAAIvB,GAAOC,KAAMwB,EAAchD,EAAWuB,EAAKQ,QAAQ,EACvDR,GAAK0B,MAAQ1B,EAAKI,QAAQsB,QAC1B1B,EAAKgB,UAAYhB,EAAK0B,MAAQD,EAC9BzB,EAAKiB,UAAYjB,EAAKgB,UAAY,EACR,gBAAfhB,GAAKc,SACZd,EAAKc,OAASC,SAASf,EAAKU,WAAWpB,IAAIN,GAAa,KAE5DgB,EAAKU,WAAWiB,KAAK,SAAU3B,EAAKc,QACpCd,EAAKoB,MAAMpB,EAAKH,QAAQ,GAAGE,UAE/B6B,QAAS/C,GACTiB,SACIjC,KAAM,SACNyC,QAAS,KACTC,SAAU,MACVR,QAAS,KACTsB,QAAQ,GAEZD,MAAO,SAAUA,GACb,GAAIpB,GAAOC,KAAMJ,EAAUG,EAAKH,QAAQ,EACxC,OAAIuB,KAAUzD,EACHkC,EAAQE,SAEnBC,EAAK6B,UAAUT,EAAQpB,EAAKgB,UAAY,GACxCnB,EAAQE,QAAUqB,EAClBpB,EAAKI,QAAQ0B,YAAYlE,EAAUkB,GAAWsC,GAAOU,YAAYlE,EAAUmB,IAAaqC,GAFxFpB,IAIJjC,MAAO,WACH,MAAOkC,MAAKmB,MAAMW,MAAM9B,KAAM+B,YAElCC,QAAS,WACLtD,EAAOuB,GAAG+B,QAAQ9B,KAAKF,MACvBA,KAAKiC,WAAWD,WAEpBE,OAAQ,WACJ,GAAInC,GAAOC,IACXD,GAAKoB,OAAOpB,EAAKH,QAAQ,GAAGE,UAEhCsB,OAAQ,SAAUA,GACd,GAAIxB,GAAUI,KAAKJ,QAASO,EAAUH,KAAKG,OACtB,KAAViB,IACPA,GAAS,GAEbpB,KAAKH,QAAQuB,OAASA,EAClBA,EACAxB,EAAQuC,WAAWjD,GAEnBU,EAAQyB,KAAKnC,EAAUA,GAE3BiB,EAAQ0B,YAAYlE,EAAUsB,IAAkBmC,IAEpDgB,QAAS,WACLpC,KAAKsB,WAETe,MAAO,SAAUC,GACb,GAAIvC,GAAOC,IACXsC,GAAEC,iBACFxC,EAAK6B,UAAU/D,EAAWkC,EAAKyC,SAAWF,EAAEG,EAAEC,MAAO,EAAG3C,EAAK0B,MAAQjD,EAAWuB,EAAKQ,QAAQ,MAEjGqB,UAAW,SAAUY,GACjB,GAAIzC,GAAOC,IACXD,GAAKyC,SAAWA,EAChBzC,EAAKQ,OAAOlB,IAAIC,EAAgB,cAAgBkD,EAAW,OACvDzC,EAAKmB,oBACLnB,EAAKU,WAAWpB,IAAIN,EAAYgB,EAAKc,OAAS2B,IAGtDG,OAAQ,WACC3C,KAAKH,QAAQuB,QAGdpB,KAAKiC,WAAWW,UAChB5C,KAAKO,OAAOsC,SAASlF,EAAUqB,KAH/BgB,KAAKiC,WAAWa,UAMxBC,MAAO,WACH,GAAIhD,GAAOC,IACXD,GAAKQ,OAAOyC,YAAYrF,EAAUqB,IAClCe,EAAKkD,QAAQlD,EAAKyC,SAAWzC,EAAKiB,YAEtCiC,QAAS,SAAUnD,GACf,GAA8KoD,GAA1KnD,EAAOC,KAAMO,EAASR,EAAKQ,OAAQX,EAAUG,EAAKH,QAAQ,GAAI9B,EAAQ8B,EAAQE,QAASqD,EAAW/E,EAAMG,OAAO6E,aAAehF,EAAMG,OAAO6E,YAAYC,GAAGC,GAAK,IAAM,GACzKvD,GAAKI,QAAQ0B,YAAYlE,EAAUkB,GAAWiB,GAAS+B,YAAYlE,EAAUmB,IAAagB,GAC1FC,EAAKyC,SAAWU,EAAWpD,EAAUC,EAAKgB,UACtChB,EAAKmB,oBACLnB,EAAKU,WAAW8C,WAAU,GAAM,GAAMC,cAClCC,QAAS,cACTC,OAAQR,EACRS,OAAO,EACPC,SAAU9D,EACV+D,KAAM,OACNV,SAAUA,IAGlB5C,EAAOgD,WAAU,GAAM,GAAMC,cACzBC,QAAS,UACTN,SAAUA,EACVO,OAAQR,EAAW,OACnBS,OAAO,EACPG,SAAU,WACFhG,IAAUgC,IACVF,EAAQE,QAAUA,EAClBC,EAAKgE,QAAQnF,GAAUkB,QAASA,SAKhDc,MAAO,WACH,GAAIb,GAAOC,IACXD,GAAKkC,WAAa,GAAI7D,GAAM4F,WAAWjE,EAAKI,SACxC8D,SAAS,EACTC,IAAK,WACGnE,EAAKF,QAAQuB,QACbrB,EAAKkD,SAASlD,EAAKH,QAAQ,GAAGE,UAGtCqE,MAAO5E,EAAMQ,EAAK4C,OAAQ5C,GAC1BqE,KAAM7E,EAAMQ,EAAKsC,MAAOtC,GACxBsE,IAAK9E,EAAMQ,EAAKgD,MAAOhD,OAInCzB,GAAGgG,OAAO7E,IACZpB,OAAOD,MAAMmG,QACRlG,OAAOD,OACE,kBAAVZ,SAAwBA,OAAOgH,IAAMhH,OAAS,SAAUiH,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.switch.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.switch', [\n        'kendo.fx',\n        'kendo.userevents'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.switch',\n        name: 'Switch',\n        category: 'mobile',\n        description: 'The mobile Switch widget is used to display two exclusive choices.',\n        depends: [\n            'fx',\n            'userevents'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.mobile.ui, outerWidth = kendo._outerWidth, Widget = ui.Widget, support = kendo.support, CHANGE = 'change', SWITCHON = 'switch-on', SWITCHOFF = 'switch-off', MARGINLEFT = 'margin-left', ACTIVE_STATE = 'state-active', DISABLED_STATE = 'state-disabled', DISABLED = 'disabled', RESOLVEDPREFIX = support.transitions.css === undefined ? '' : support.transitions.css, TRANSFORMSTYLE = RESOLVEDPREFIX + 'transform', proxy = $.proxy;\n        function className(name) {\n            return 'km-' + name;\n        }\n        function limitValue(value, minLimit, maxLimit) {\n            return Math.max(minLimit, Math.min(maxLimit, value));\n        }\n        var SWITCH_MARKUP = '<span class=\"' + className('switch') + ' ' + className('widget') + '\">        <span class=\"' + className('switch-wrapper') + '\">            <span class=\"' + className('switch-background') + '\"></span>        </span>         <span class=\"' + className('switch-container') + '\">            <span class=\"' + className('switch-handle') + '\">                 <span class=\"' + className('switch-label-on') + '\">{0}</span>                 <span class=\"' + className('switch-label-off') + '\">{1}</span>             </span>         </span>    </span>';\n        var Switch = Widget.extend({\n            init: function (element, options) {\n                var that = this, checked;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                that.wrapper = $(kendo.format(SWITCH_MARKUP, options.onLabel, options.offLabel));\n                that.handle = that.wrapper.find('.km-switch-handle');\n                that.background = that.wrapper.find('.km-switch-background');\n                that.wrapper.insertBefore(that.element).prepend(that.element);\n                that._drag();\n                that.origin = parseInt(that.background.css(MARGINLEFT), 10);\n                that.constrain = 0;\n                that.snapPoint = 0;\n                element = that.element[0];\n                element.type = 'checkbox';\n                that._animateBackground = true;\n                checked = that.options.checked;\n                if (checked === null) {\n                    checked = element.checked;\n                }\n                that.check(checked);\n                that.options.enable = that.options.enable && !that.element.attr(DISABLED);\n                that.enable(that.options.enable);\n                that.refresh();\n                kendo.notify(that, kendo.mobile.ui);\n            },\n            refresh: function () {\n                var that = this, handleWidth = outerWidth(that.handle, true);\n                that.width = that.wrapper.width();\n                that.constrain = that.width - handleWidth;\n                that.snapPoint = that.constrain / 2;\n                if (typeof that.origin != 'number') {\n                    that.origin = parseInt(that.background.css(MARGINLEFT), 10);\n                }\n                that.background.data('origin', that.origin);\n                that.check(that.element[0].checked);\n            },\n            events: [CHANGE],\n            options: {\n                name: 'Switch',\n                onLabel: 'on',\n                offLabel: 'off',\n                checked: null,\n                enable: true\n            },\n            check: function (check) {\n                var that = this, element = that.element[0];\n                if (check === undefined) {\n                    return element.checked;\n                }\n                that._position(check ? that.constrain : 0);\n                element.checked = check;\n                that.wrapper.toggleClass(className(SWITCHON), check).toggleClass(className(SWITCHOFF), !check);\n            },\n            value: function () {\n                return this.check.apply(this, arguments);\n            },\n            destroy: function () {\n                Widget.fn.destroy.call(this);\n                this.userEvents.destroy();\n            },\n            toggle: function () {\n                var that = this;\n                that.check(!that.element[0].checked);\n            },\n            enable: function (enable) {\n                var element = this.element, wrapper = this.wrapper;\n                if (typeof enable == 'undefined') {\n                    enable = true;\n                }\n                this.options.enable = enable;\n                if (enable) {\n                    element.removeAttr(DISABLED);\n                } else {\n                    element.attr(DISABLED, DISABLED);\n                }\n                wrapper.toggleClass(className(DISABLED_STATE), !enable);\n            },\n            _resize: function () {\n                this.refresh();\n            },\n            _move: function (e) {\n                var that = this;\n                e.preventDefault();\n                that._position(limitValue(that.position + e.x.delta, 0, that.width - outerWidth(that.handle, true)));\n            },\n            _position: function (position) {\n                var that = this;\n                that.position = position;\n                that.handle.css(TRANSFORMSTYLE, 'translatex(' + position + 'px)');\n                if (that._animateBackground) {\n                    that.background.css(MARGINLEFT, that.origin + position);\n                }\n            },\n            _start: function () {\n                if (!this.options.enable) {\n                    this.userEvents.cancel();\n                } else {\n                    this.userEvents.capture();\n                    this.handle.addClass(className(ACTIVE_STATE));\n                }\n            },\n            _stop: function () {\n                var that = this;\n                that.handle.removeClass(className(ACTIVE_STATE));\n                that._toggle(that.position > that.snapPoint);\n            },\n            _toggle: function (checked) {\n                var that = this, handle = that.handle, element = that.element[0], value = element.checked, duration = kendo.mobile.application && kendo.mobile.application.os.wp ? 100 : 200, distance;\n                that.wrapper.toggleClass(className(SWITCHON), checked).toggleClass(className(SWITCHOFF), !checked);\n                that.position = distance = checked * that.constrain;\n                if (that._animateBackground) {\n                    that.background.kendoStop(true, true).kendoAnimate({\n                        effects: 'slideMargin',\n                        offset: distance,\n                        reset: true,\n                        reverse: !checked,\n                        axis: 'left',\n                        duration: duration\n                    });\n                }\n                handle.kendoStop(true, true).kendoAnimate({\n                    effects: 'slideTo',\n                    duration: duration,\n                    offset: distance + 'px,0',\n                    reset: true,\n                    complete: function () {\n                        if (value !== checked) {\n                            element.checked = checked;\n                            that.trigger(CHANGE, { checked: checked });\n                        }\n                    }\n                });\n            },\n            _drag: function () {\n                var that = this;\n                that.userEvents = new kendo.UserEvents(that.wrapper, {\n                    fastTap: true,\n                    tap: function () {\n                        if (that.options.enable) {\n                            that._toggle(!that.element[0].checked);\n                        }\n                    },\n                    start: proxy(that._start, that),\n                    move: proxy(that._move, that),\n                    end: proxy(that._stop, that)\n                });\n            }\n        });\n        ui.plugin(Switch);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}