{"version": 3, "sources": ["kendo.multiviewcalendar.js"], "names": ["f", "define", "$", "undefined", "mousetoggle", "e", "disabled", "this", "hasClass", "toggleClass", "HOVER", "MOUSEENTER", "indexOf", "type", "FOCUS", "addDaysToArray", "array", "numberOfDays", "fromDate", "disableDates", "i", "nextDay", "Date", "getTime", "setDate", "getDate", "push", "daysBetweenTwoDates", "startDate", "endDate", "temp", "fromDateUTC", "endDateUTC", "calendar", "views", "UTC", "getFullYear", "getMonth", "Math", "ceil", "kendo", "date", "MS_PER_DAY", "shiftDate", "value", "dimension", "numberOfViews", "current", "DATE", "setFullYear", "abs", "setMonth", "last", "window", "support", "isInRange", "toDateObject", "createDate", "isEqualDate", "get<PERSON><PERSON>y", "keys", "ui", "Widget", "Selectable", "template", "mobileOS", "ns", "CLICK", "KEYDOWN", "ID", "MIN", "MONTH", "DOT", "CENTURY", "DECADE", "CHANGE", "NAVIGATE", "VALUE", "FOCUSED", "SELECTED", "MID", "SPLITEND", "SPLITSTART", "START", "END", "DISABLED", "TODAY", "OTHERMONTH", "OUTOFRANGE", "CELLSELECTOR", "CELLSELECTORVALID", "BLUR", "touch", "MOUSELEAVE_NS", "PREVARROW", "NEXTARROW", "ARIA_SELECTED", "INPUTSELECTOR", "ARIA_DISABLED", "ARIA_LABEL", "proxy", "month", "year", "decade", "century", "RangeSelectable", "extend", "init", "element", "options", "that", "fn", "call", "userEvents", "UserEvents", "global", "allowSelection", "filter", "tap", "_tap", "touchAction", "events", "name", "inputSelectors", "multiple", "dragToSelect", "relatedTarget", "noop", "destroy", "_lastActive", "_start", "_end", "_allowSelection", "target", "is", "cancel", "start", "addClass", "end", "mid", "elements", "tables", "find", "each", "lastCell", "firstCell", "clear", "clearVariables", "removeClass", "selectFrom", "items", "startIdx", "inArray", "index", "selectTo", "endIdx", "range", "change", "trigger", "_toggling", "MultiViewCalendar", "id", "culture", "wrapper", "getCulture", "format", "_extractFormat", "calendars", "standard", "patterns", "d", "_templates", "_header", "_wrapper", "weekNumber", "on", "_move", "_blur", "link", "currentTarget", "<PERSON><PERSON><PERSON><PERSON>", "href", "preventDefault", "_click", "_mouseEnter", "attr", "_cellID", "_calendarWidth", "width", "_range", "_initViews", "viewName", "_selectable", "_footer", "footer", "_selectDates", "selectable", "selectDates", "length", "_restoreSelection", "selectRange", "notify", "min", "max", "dates", "showViewHeader", "depth", "messages", "weekColumnHeader", "setOptions", "normalize", "_views", "off", "remove", "_cell", "_current<PERSON><PERSON>w", "_current", "header", "_title", "rangeSelectable", "_today", "focus", "table", "closest", "_dateInViews", "_cellByDate", "first", "_option", "view", "navigateToPast", "_navigate", "navigateToFuture", "navigateUp", "_index", "navigate", "navigateDown", "_value", "isNaN", "viewsEnum", "restrictValue", "_updateHeader", "lastDate", "prevDisabled", "nextDisabled", "title", "_firstViewValue", "visibleRange", "_visibleRange", "html", "compare", "_navContainer", "appendTo", "cell", "preventFocus", "prevent", "method", "lastActive", "cellIndex", "key", "keyCode", "focusedCell", "currentValue", "isRtl", "RIGHT", "LEFT", "UP", "DOWN", "SPACEBAR", "HOME", "eq", "prev", "_focusCell", "next", "ctrl<PERSON>ey", "metaKey", "ENTER", "_toggleSelection", "shift<PERSON>ey", "_nextNavigatable", "_select<PERSON>ange", "PAGEUP", "PAGEDOWN", "_visualizeSelectedDatesInView", "cells", "selectedDates", "toDateString", "_selectElement", "isDisabled", "navigatableDate", "event", "_unselect", "option", "isBigger", "parseDate", "_toggle", "toLowerCase", "_rangeSelection", "aria", "parseOptions", "_selection", "_onRelatedTarget", "unselect", "_unselecting", "_getFirstViewDate", "current<PERSON>iew", "ranges", "_canRenderNextView", "viewDate", "fullYear", "maxYear", "max<PERSON><PERSON><PERSON>", "viewOptions", "_table", "content", "url", "showHeader", "isWeekColumnVisible", "otherMonth", "tablesWrapper", "sender", "_preventChange", "selectElements", "domEvent", "isCell", "_validateValue", "_deselect", "_addSelectedCellsToArray", "item", "currentDateIndex", "map", "Number", "splice", "firstDateInView", "lastDateInView", "_fillRange", "daysDifference", "buttons", "prependTo", "insertAfter", "empty", "useWithBlock", "today", "show", "toString", "hide", "arrow", "modifier", "offset", "originaValue", "toggle", "isTodayDisabled", "_todayClick", "adjustDST", "_focus", "contains", "focusTable", "cellId", "removeAttribute", "clearSelection", "validSelectedDates", "datesUnique", "position", "time", "grep", "setHours", "startInRange", "endInRange", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,2BACH,aACA,mBACA,kBACDD,IACL,WA64CE,MAj4CC,UAAUE,EAAGC,GAg0CV,QAASC,GAAYC,GACjB,GAAIC,GAAWJ,EAAEK,MAAMC,SAAS,mBAC3BF,IACDJ,EAAEK,MAAME,YAAYC,EAAOC,EAAWC,QAAQP,EAAEQ,UAAcR,EAAEQ,MAAQC,GAGhF,QAASC,GAAeC,EAAOC,EAAcC,EAAUC,GAAvD,GACaC,GACDC,CADR,KAASD,EAAI,EAAGA,GAAKH,EAAcG,IAC3BC,EAAU,GAAIC,MAAKJ,EAASK,WAChCF,EAAU,GAAIC,MAAKD,EAAQG,QAAQH,EAAQI,UAAYL,IAClDD,EAAaE,IACdL,EAAMU,KAAKL,GAIvB,QAASM,GAAoBC,EAAWC,GAAxC,GAEYC,GAIJC,EACAC,CACJ,QAPKH,GAAWD,IACRE,GAAQF,EACZK,EAASC,MAAM,GAAGV,QAAQI,EAAWC,GACrCI,EAASC,MAAM,GAAGV,QAAQK,EAAS,GAAIP,MAAKQ,KAE5CC,EAAcT,KAAKa,IAAIP,EAAUQ,cAAeR,EAAUS,WAAYT,EAAUH,WAChFO,EAAaV,KAAKa,IAAIN,EAAQO,cAAeP,EAAQQ,WAAYR,EAAQJ,WACtEa,KAAKC,OAAOP,GAAcD,GAAeS,EAAMC,KAAKC,YAE/D,QAASC,GAAUC,EAAOC,EAAWC,GACjC,GAAIC,EACJ,OAAkB,UAAdF,GACAE,EAAU,GAAIC,IAAKJ,EAAMR,cAAeQ,EAAMP,WAAaS,EAAeF,EAAMnB,WAChFsB,EAAQE,YAAYL,EAAMR,gBACtBE,KAAKY,IAAIH,EAAQV,WAAaO,EAAMP,YAAcS,GAAiBA,EAAgB,MACnFC,EAAQI,SAASP,EAAMP,WAAaS,GACpCC,EAAUd,EAASC,MAAM,GAAGkB,KAAKL,IAE9BA,GACc,SAAdF,GACPE,EAAU,GAAIC,IAAK,EAAGJ,EAAMP,WAAYO,EAAMnB,WAC9CsB,EAAQE,YAAYL,EAAMR,cAAgBU,GACtCR,KAAKY,IAAIH,EAAQX,cAAgBQ,EAAMR,eAAiBU,IACxDC,EAAU,GAAIC,IAAK,EAAGJ,EAAMP,WAAY,GACxCU,EAAQE,YAAYL,EAAMR,cAAgBU,GAC1CC,EAAUd,EAASC,MAAM,GAAGkB,KAAKL,IAE9BA,GACc,WAAdF,GACPE,EAAU,GAAIC,IAAK,EAAGJ,EAAMP,WAAYO,EAAMnB,WAC9CsB,EAAQE,YAAYL,EAAMR,cAAgB,GAAKU,GAC3CR,KAAKY,IAAIH,EAAQX,cAAgBQ,EAAMR,eAAiB,GAAKU,IAC7DC,EAAU,GAAIC,IAAK,EAAGJ,EAAMP,WAAY,GACxCU,EAAQE,YAAYL,EAAMR,cAAgB,GAAKU,GAC/CC,EAAUd,EAASC,MAAM,GAAGkB,KAAKL,IAE9BA,GACc,YAAdF,GACPE,EAAU,GAAIC,IAAK,EAAGJ,EAAMP,WAAYO,EAAMnB,WAC9CsB,EAAQE,YAAYL,EAAMR,cAAgB,IAAMU,GAC5CR,KAAKY,IAAIH,EAAQX,cAAgBQ,EAAMR,eAAiB,IAAMU,IAC9DC,EAAU,GAAIC,IAAK,EAAGJ,EAAMP,WAAY,GACxCU,EAAQE,YAAYL,EAAMR,cAAgB,IAAMU,GAChDC,EAAUd,EAASC,MAAM,GAAGkB,KAAKL,IAE9BA,GARJ,EAr3Cd,GACOP,GAAQa,OAAOb,MAAOP,EAAWO,EAAMP,SAAUqB,EAAUd,EAAMc,QAASC,EAAYtB,EAASsB,UAAWC,EAAevB,EAASuB,aAAcC,EAAaxB,EAASwB,WAAYC,EAAczB,EAASyB,YAAaC,EAAW1B,EAAS0B,SAAUC,EAAOpB,EAAMoB,KAAMC,EAAKrB,EAAMqB,GAAIC,EAASD,EAAGC,OAAQC,EAAaF,EAAGE,WAAYC,EAAWxB,EAAMwB,SAAUC,EAAWX,EAAQW,SAAUC,EAAK,0BAA2BC,EAAQ,QAASC,EAAU,UAAWC,EAAK,KAAMC,EAAM,MAAOC,EAAQ,QAASC,EAAM,IAAKC,EAAU,UAAWC,EAAS,SAAUC,EAAS,SAAUC,EAAW,WAAYC,EAAQ,QAASC,EAAU,kBAAmBC,EAAW,mBAAoBC,EAAM,cAAeC,EAAW,oBAAqBC,EAAa,sBAAuBC,EAAQ,gBAAiBC,EAAM,cAAe1E,EAAQ,gBAAiB2E,EAAW,mBAAoBC,EAAQ,cAAeC,EAAa,gBAAiBC,EAAa,iBAAkBC,EAAe,wBAA0BD,EAAa,IAAKE,EAAoB,wBAA0BL,EAAW,UAAYG,EAAa,IAAKG,EAAO,OAAQ7E,EAAQ,QAASH,EAAa2C,EAAQsC,MAAQ,aAAe,aAAcC,EAAgBvC,EAAQsC,MAAQ,WAAa1B,EAAK,aAAeA,EAAK,aAAeA,EAAI4B,EAAY,aAAcC,EAAY,aAAcC,EAAgB,gBAAiBC,EAAgB,4IAA6IC,GAAgB,gBAAiBC,GAAa,aAAcC,GAAQlG,EAAEkG,MAAOpD,GAAO1B,KAAMY,IACvjDmE,MAAO,EACPC,KAAM,EACNC,OAAQ,EACRC,QAAS,GAEbC,GAAkB3C,EAAO4C,QACzBC,KAAM,SAAUC,EAASC,GACrB,GAAIC,GAAOvG,IACXuD,GAAOiD,GAAGJ,KAAKK,KAAKF,EAAMF,EAASC,GACnCC,EAAKG,WAAa,GAAIzE,GAAM0E,WAAWJ,EAAKF,SACxCO,QAAQ,EACRC,gBAAgB,EAChBC,OAAQP,EAAKD,QAAQQ,OACrBC,IAAKlB,GAAMU,EAAKS,KAAMT,GACtBU,YAAa,UAGrBC,QAAS9C,GACTkC,SACIa,KAAM,kBACNL,OAAQ,KACRM,eAAgB1B,EAChB2B,UAAU,EACVC,cAAc,EACdC,cAAe5H,EAAE6H,MAErBC,QAAS,WACL,GAAIlB,GAAOvG,IACXuD,GAAOiD,GAAGiB,QAAQhB,KAAKF,GACvBA,EAAKG,WAAWe,UAChBlB,EAAKmB,YAAcnB,EAAKF,QAAUE,EAAKG,WAAaH,EAAKoB,OAASpB,EAAKqB,KAAO,MAElFC,gBAAiB,SAAUC,GACvB,OAAInI,EAAEmI,GAAQC,GAAG/H,KAAKsG,QAAQc,kBAC1BpH,KAAK0G,WAAWsB,UACT,IAIfC,MAAO,SAAU5B,GACb,MAAIA,KAAYzG,EACLI,KAAK2H,QAEhBtB,EAAQ6B,SAAStD,EAAQ,IAAMJ,GAC/BxE,KAAK2H,OAAStB,EADdA,IAGJ8B,IAAK,SAAU9B,GACX,MAAIA,KAAYzG,EACLI,KAAK2H,QAEhBtB,EAAQ6B,SAASrD,EAAM,IAAML,GAC7BxE,KAAK4H,KAAOvB,EADZA,IAGJ+B,IAAK,SAAUC,GACX,GAAIC,GAAStI,KAAKqG,QAAQkC,KAAK,gBAC/BF,GAASH,SAASzD,GAClB6D,EAAOE,KAAK,WAAA,GACJjC,GAAO5G,EAAEK,MACTyI,EAAWlC,EAAKgC,KAAKpD,EAAoB,SACzCuD,EAAYnC,EAAKgC,KAAKpD,EAAoB,SAC1CsD,GAASxI,SAASwE,IAClBgE,EAASP,SAASxD,GAElBgE,EAAUzI,SAASwE,IACnBiE,EAAUR,SAASvD,MAI/BgE,MAAO,SAAUC,GACb5I,KAAKqG,QAAQkC,KAAKrD,GAAc2D,YAAYhE,EAAM,IAAML,EAAW,IAAMI,EAAQ,IAAMH,EAAM,IAAMC,EAAW,IAAMC,GAChHiE,IACA5I,KAAK2H,OAAS3H,KAAK4H,KAAO,OAGlCkB,WAAY,SAAUb,GAAV,GACJ1B,GAAOvG,KACP+I,EAEIxC,EAAKF,QAAQkC,KAAKrD,GADtB8D,EAEOrJ,EAAEsJ,QAAQtJ,EAAEsI,GAAO,GAAIc,EAClCxC,GAAKoC,QACLpC,EAAK0B,MAAMA,GACXc,EAAQA,EAAMjC,OAAO,SAAUoC,GAC3B,MAAOA,GAAQF,IAEnBzC,EAAK6B,IAAIW,IAEbI,SAAU,SAAUhB,GAAV,GACF5B,GAAOvG,KACP+I,EAEIxC,EAAKF,QAAQkC,KAAKrD,GADtBkE,EAEKzJ,EAAEsJ,QAAQtJ,EAAEwI,GAAK,GAAIY,EAC9BxC,GAAKoC,QACLI,EAAQA,EAAMjC,OAAO,SAAUoC,GAC3B,MAAOA,GAAQE,IAEnB7C,EAAK6B,IAAIW,GACTxC,EAAK4B,IAAIxI,EAAEwI,KAEfkB,MAAO,SAAUpB,EAAOE,GAAjB,GAECY,GACAC,EACAI,EACA7H,EAJAgF,EAAOvG,IAKX,OAAIiI,KAAUrI,GAENqI,MAAO1B,EAAKoB,OACZQ,IAAK5B,EAAKqB,OAGlBmB,EAAQxC,EAAKF,QAAQkC,KAAKrD,GAC1B8D,EAAWrJ,EAAEsJ,QAAQtJ,EAAEsI,GAAO,GAAIc,GAClCK,EAASzJ,EAAEsJ,QAAQtJ,EAAEwI,GAAK,GAAIY,GAC1BC,EAAWI,IACX7H,EAAO4G,EACPA,EAAMF,EACNA,EAAQ1G,EACRA,EAAOyH,EACPA,EAAWI,EACXA,EAAS7H,GAEbgF,EAAKoC,QACLV,EAAMC,SAAStD,EAAQ,IAAMJ,GAC7B+B,EAAKoB,OAASM,EACdc,EAAQA,EAAMjC,OAAO,SAAUoC,GAC3B,MAAOA,GAAQF,GAAYE,EAAQE,IAEvC7C,EAAK6B,IAAIW,GACTxC,EAAK4B,IAAIxI,EAAEwI,IAlBXY,IAoBJO,OAAQ,WACJtJ,KAAKuJ,QAAQnF,IAEjB4C,KAAM,SAAUlH,GACZ,GAAuCiJ,GAAOC,EAAUI,EAApDtB,EAASnI,EAAEG,EAAEgI,QAASvB,EAAOvG,IAEjC,IADAuG,EAAKmB,YAAcI,GACdvB,EAAKoB,OAGN,MAFApB,GAAK0B,MAAMH,GACXvB,EAAKgD,QAAQnF,GACb,CAEJ,IAAImC,EAAKoB,SAAWpB,EAAKqB,KAIrB,MAHAmB,GAAQxC,EAAKF,QAAQkC,KAAKrD,GAC1B8D,EAAWrJ,EAAEsJ,QAAQtJ,EAAE4G,EAAKoB,QAAQ,GAAIoB,GACxCK,EAASzJ,EAAEsJ,QAAQtJ,EAAEmI,GAAQ,GAAIiB,IAC5B9F,EAAatD,EAAE4G,EAAKoB,QAAQY,KAAK,OAAStF,EAAatD,EAAEmI,GAAQS,KAAK,OACvEhC,EAAKoC,QACLpC,EAAK0B,MAAMH,GACXvB,EAAKgD,QAAQnF,GACb,IAEJ2E,EAAQA,EAAMjC,OAAO,SAAUoC,GAC3B,MAAOA,GAAQF,GAAYE,EAAQE,IAEvC7C,EAAK6B,IAAIW,GACTxC,EAAK4B,IAAIxI,EAAEmI,IACXvB,EAAKgD,QAAQnF,GACb,EAEJ,IAAImC,EAAKoB,QAAUpB,EAAKqB,KAAM,CAC1B,GAAIE,EAAO7H,SAASwE,GAQhB,MAPK8B,GAAKiD,UAGNjD,EAAK8C,MAAM9C,EAAKoB,OAAQG,GAFxBvB,EAAK8C,MAAMvB,EAAQvB,EAAKqB,MAI5BrB,EAAKiD,WAAajD,EAAKiD,UACvBjD,EAAKgD,QAAQnF,GACb,CAEJmC,GAAKiD,WAAY,EACjBjD,EAAKqB,KAAO,KACZrB,EAAKoC,QACLpC,EAAK0B,MAAMH,GACXvB,EAAKgD,QAAQnF,OAIrBqF,GAAoBlG,EAAO4C,QAC3BC,KAAM,SAAUC,EAASC,GAAnB,GAEEoD,GACAC,EAFApD,EAAOvG,IAGXuD,GAAOiD,GAAGJ,KAAKK,KAAKF,EAAMF,EAASC,GACnCD,EAAUE,EAAKqD,QAAUrD,EAAKF,QAC9BC,EAAUC,EAAKD,QACfC,EAAKD,QAAQ1F,aAAec,EAAS3B,SAASwG,EAAKD,QAAQ1F,cAC3D+I,EAAU1H,EAAM4H,WAAWvD,EAAQqD,SACnCrD,EAAQwD,OAAS7H,EAAM8H,eAAezD,EAAQwD,QAAUH,EAAQK,UAAUC,SAASC,SAASC,GAC5F5D,EAAK6D,aACL7D,EAAK8D,UACL9D,EAAK+D,WACLZ,EAAKrD,EAAQ6B,SAAS,wCAA0C5B,EAAQiE,WAAa,iBAAmB,KAAKC,GAAG3G,EAAUF,EAAI,kBAAmBkC,GAAMU,EAAKkE,MAAOlE,IAAOiE,GAAGpF,EAAOzB,EAAI,QAASkC,GAAMU,EAAKmE,MAAOnE,IAAOiE,GAAG5G,EAAQD,EAAIwB,EAAmB,SAAUrF,GAClQ,GAAI6K,GAAO7K,EAAE8K,cAAcC,UACvBF,GAAKG,KAAKzK,QAAQ,UAClBP,EAAEiL,iBAENxE,EAAKyE,OAAOrL,EAAEgL,MACfH,GAAGpK,EAAauD,EAAIwB,EAAmBU,GAAMU,EAAK0E,YAAa1E,IAAOiE,GAAGlF,EAAeH,EAAmB,WAC1GxF,EAAEK,MAAM6I,YAAY1I,KACrB+K,KAAKpH,GACJ4F,IACAnD,EAAK4E,QAAUzB,EAAK,kBAExBnD,EAAK6E,eAAiB7E,EAAKF,QAAQgF,QACnC9E,EAAK+E,OAAShF,EAAQ+C,MACtB9C,EAAKgF,YACDC,SAAUlF,EAAQ2B,MAClB5F,MAAOiE,EAAQjE,QAEnBkE,EAAKkF,cACLlF,EAAKmF,QAAQnF,EAAKoF,QAClBpF,EAAKqF,gBACLrF,EAAKlE,MAAMiE,EAAQjE,OACO,YAAtBiE,EAAQuF,aACRtF,EAAKqF,aAAetF,EAAQwF,YAAYC,OAASzF,EAAQwF,YAAcvF,EAAKqF,aAC5ErF,EAAKyF,qBAEiB,SAAtB1F,EAAQuF,YACRtF,EAAK0F,YAAY1F,EAAK+E,QAE1BrJ,EAAMiK,OAAO3F,IAEjBD,SACIa,KAAM,oBACN9E,MAAO,KACP8J,IAAK,GAAI1J,IAAK,KAAM,EAAG,GACvB2J,IAAK,GAAI3J,IAAK,KAAM,GAAI,IACxB4J,SACAzL,aAAc,KACd+I,QAAS,GACTgC,OAAQ,GACR7B,OAAQ,GACRhE,SACAuD,OACIpB,MAAO,KACPE,IAAK,MAEToC,YAAY,EACZ5I,MAAO,EACP2K,gBAAgB,EAChBT,WAAY,SACZC,eACA7D,MAAOjE,EACPuI,MAAOvI,EACPwI,UAAYC,iBAAkB,KAElCvF,QACI9C,EACAC,GAEJqI,WAAY,SAAUpG,GAAV,GAQCzF,GAPL0F,EAAOvG,IAOX,KANA0B,EAASiL,UAAUrG,GACnBA,EAAQ1F,aAAec,EAAS3B,SAASuG,EAAQ1F,cACjD2C,EAAOiD,GAAGkG,WAAWjG,KAAKF,EAAMD,GAChCC,EAAKkF,cACLlF,EAAK6D,aACL7D,EAAKmF,QAAQnF,EAAKoF,QACT9K,EAAI,EAAGA,EAAI0F,EAAKqG,OAAOb,OAAQlL,IACpC0F,EAAKqG,OAAO/L,GAAGgM,IAAIlJ,GAAImJ,QAE3BvG,GAAKgF,YACDC,SAAUlF,EAAQ2B,MAClB5F,MAAOiE,EAAQjE,QAEnBkE,EAAK+E,OAAShF,EAAQ+C,QAClBpB,MAAO,KACPE,IAAK,MAET5B,EAAKyF,qBAETvE,QAAS,WAAA,GAMQ5G,GALT0F,EAAOvG,IAIX,IAHAuG,EAAKwG,MAAQ,KACbxG,EAAKyG,aAAe,KACpBzG,EAAK0G,SAAW,KACZ1G,EAAKqG,OACL,IAAS/L,EAAI,EAAGA,EAAI0F,EAAKqG,OAAOb,OAAQlL,IACpC0F,EAAKqG,OAAO/L,GAAGgM,IAAIlJ,GAAImJ,QAG/BvG,GAAKF,QAAQwG,IAAIlJ,GACb4C,EAAK2G,SACL3G,EAAK2G,OAAOL,IAAIlJ,GAChB4C,EAAK4G,OAAS,KACd5G,EAAK2G,OAAS,MAEd3G,EAAKsF,aACLtF,EAAKsF,WAAWpE,UAChBlB,EAAKsF,WAAa,MAElBtF,EAAK6G,kBACL7G,EAAK6G,gBAAgB3F,UACrBlB,EAAK6G,gBAAkB,MAEvB7G,EAAK8G,QACLpL,EAAMwF,QAAQlB,EAAK8G,OAAOR,IAAIlJ,IAElC4C,EAAKqG,OAAS,KACdrJ,EAAOiD,GAAGiB,QAAQhB,KAAKF,IAE3B/D,QAAS,WACL,MAAOxC,MAAKiN,UAEhBK,MAAO,WACH,GAAIC,EACAvN,MAAK+M,MACL/M,KAAK+M,MAAMS,QAAQ,SAASjE,QAAQ,SAC7BvJ,KAAKiN,UAAYjN,KAAKyN,aAAazN,KAAKiN,WAC/CjN,KAAK+M,MAAQ/M,KAAK0N,YAAY1N,KAAKiN,UACnCjN,KAAK+M,MAAMS,QAAQ,SAASjE,QAAQ,WAEpCgE,EAAQvN,KAAKqG,QAAQkC,KAAK,SAASoF,QAAQpE,QAAQ,SACnDvJ,KAAK+M,MAAQQ,EAAMhF,KAAKpD,EAAoB,UAC5CnF,KAAKiN,SAAWhK,EAAajD,KAAK+M,MAAMxE,KAAK,OAEjDvI,KAAK+M,MAAM7E,SAAS3D,IAExB4H,IAAK,SAAU9J,GACX,MAAOrC,MAAK4N,QAAQ7J,EAAK1B,IAE7B+J,IAAK,SAAU/J,GACX,MAAOrC,MAAK4N,QAAQ,MAAOvL,IAE/BwL,KAAM,WACF,MAAO7N,MAAKgN,cAEhBc,eAAgB,WACZ9N,KAAK+N,UAAUxI,OAEnByI,iBAAkB,WACdhO,KAAK+N,UAAUvI,EAAW,IAE9ByI,WAAY,WACR,GAAI1H,GAAOvG,KAAMkJ,EAAQ3C,EAAK2H,MAC1B3H,GAAK4G,OAAOlN,SAAS6E,IAGzByB,EAAK4H,SAAS5H,EAAK0G,WAAY/D,IAEnCkF,aAAc,SAAU/L,GACpB,GAAIkE,GAAOvG,KAAMkJ,EAAQ3C,EAAK2H,OAAQ3B,EAAQhG,EAAKD,QAAQiG,KAC3D,IAAKlK,EAGL,MAAI6G,KAAUvH,GAAM4K,IACXpJ,EAAYoD,EAAK8H,OAAQ9H,EAAK0G,WAAc9J,EAAYoD,EAAK8H,OAAQhM,KACtEkE,EAAKlE,MAAMA,GACXkE,EAAKgD,QAAQnF,IAEjB,IAEJmC,EAAK4H,SAAS9L,IAAS6G,GAAvB3C,IAEJ4H,SAAU,SAAU9L,EAAOwL,GAAjB,GAEFtH,GACAD,EACA6F,EACAC,EAUKvL,CAAT,KAdAgN,EAAOS,MAAMT,GAAQnM,EAASC,MAAMD,EAAS6M,UAAUV,IAASnM,EAASC,MAAMkM,GAC3EtH,EAAOvG,KACPsG,EAAUC,EAAKD,QACf6F,EAAM7F,EAAQ6F,IACdC,EAAM9F,EAAQ8F,IAId7F,EAAK0G,SAHJ5K,EAGeA,EAFAA,EAAQ,GAAII,MAAMf,EAAS8M,cAAcnM,EAAO8J,EAAKC,KAIrEyB,IAASjO,IACTiO,EAAOtH,EAAKyG,cAEhBzG,EAAKyG,aAAea,EACXhN,EAAI,EAAGA,EAAI0F,EAAKqG,OAAOb,OAAQlL,IACpC0F,EAAKqG,OAAO/L,GAAGgM,IAAIlJ,GAAImJ,QAE3BvG,GAAKgF,YACDC,SAAUqC,EAAK1G,KACf9E,MAAOA,IAEXkE,EAAKyF,qBAETyC,cAAe,WAAA,GAUPC,GACA3O,EACA4O,EACAC,EAZArI,EAAOvG,KACP6N,EAAOtH,EAAKyG,aACZ6B,EAAQtI,EAAK4G,OACb9K,EAAQkE,EAAKuI,gBACbxI,EAAUC,EAAKD,QACfyI,EAAexI,EAAKyI,gBACpBrF,EAAUrD,EAAQqD,QAClBwC,EAAM7F,EAAQ6F,IACdC,EAAM9F,EAAQ8F,GAKdyB,GAAK1G,OAAShD,GAAU0J,EAAK1G,OAASjD,GACtCwK,EAAWtM,EAAUC,EAAOwL,EAAK1G,KAAMb,EAAQ3E,MAAQ,GAClDqB,EAAU0L,EAAUvC,EAAKC,KAC1BsC,EAAWtC,GAEfyC,EAAMI,KAAKpB,EAAKF,MAAMtL,GAAOR,cAAgB,MAAQgM,EAAKhL,KAAK6L,GAAU7M,gBAEzEgN,EAAMI,KAAKpB,EAAKgB,MAAMxM,EAAO8J,EAAKC,EAAKzC,GAAW,MAAQkE,EAAKgB,MAAMzM,EAAUC,EAAOwL,EAAK1G,KAAMb,EAAQ3E,MAAQ,GAAIwK,EAAKC,EAAKzC,IAEnI5J,EAAW8N,EAAK1G,OAASjD,EACzB2K,EAAM3O,YAAY4E,EAAU/E,GAAUmL,KAAKvF,GAAe5F,GAC1D4O,EAAed,EAAKqB,QAAQH,EAAa9G,MAAO1B,EAAKD,QAAQ6F,KAAO,EACpEyC,EAAef,EAAKqB,QAAQH,EAAa5G,IAAK5B,EAAKD,QAAQ8F,QACvDuC,GAAgBC,EACZrI,EAAK4I,gBACL5I,EAAK4I,cAAcrC,SACnBvG,EAAK4I,cAAgB,OAGpB5I,EAAK4I,gBACN5I,EAAK4I,cAAgBxP,EAAE,qGAA4GiG,GAAa,sIAA6IA,GAAa,sEAA2EwJ,SAAS7I,EAAK2G,QACnY3G,EAAKhB,GAAagB,EAAK4I,cAAc5G,KAAK,gBAC1ChC,EAAKf,GAAae,EAAK4I,cAAc5G,KAAK,iBAE9ChC,EAAKhB,GAAWrF,YAAY4E,EAAU6J,GAAczD,KAAKvF,GAAegJ,GACpEpI,EAAKhB,GAAWtF,SAAS6E,IACzByB,EAAKhB,GAAWsD,YAAY1I,GAEhCoG,EAAKf,GAAWtF,YAAY4E,EAAU8J,GAAc1D,KAAKvF,GAAeiJ,GACpErI,EAAKf,GAAWvF,SAAS6E,IACzByB,EAAKf,GAAWqD,YAAY1I,KAIxC8K,YAAa,SAAUnL,GAAV,GAGLuJ,GACAN,EACAC,EACAI,EALA7C,EAAOvG,KACPqP,EAAO1P,EAAEG,EAAE8K,cAMf,IADAyE,EAAKnH,SAAS/H,GACVoG,EAAK6G,iBAA8C,UAA3B7G,EAAKyG,aAAa7F,OAC1CkC,EAAQ9C,EAAK0F,cACT5C,EAAMpB,QAAUoB,EAAMlB,KAAK,CAC3B,GAAI5B,EAAKkH,aAAalH,EAAK0F,cAAchE,OAAQ,CAI7C,GAHAc,EAAQxC,EAAKF,QAAQkC,KAAKhC,EAAK6G,gBAAgB9G,QAAQQ,QACvDkC,EAAWrJ,EAAEsJ,QAAQtJ,EAAE4G,EAAK6G,gBAAgBzF,QAAQ,GAAIoB,GACxDK,EAASzJ,EAAEsJ,QAAQtJ,EAAE0P,GAAM,GAAItG,GAC3BC,EAAWI,EACX,MAEJ7C,GAAK6G,gBAAgB/D,MAAM9C,EAAK6G,gBAAgBzF,OAAQ0H,QAChDpM,EAAasD,EAAKF,QAAQkC,KAAKrD,EAAe,UAAUqD,KAAK,OAASc,EAAMpB,OACpF1B,EAAK6G,gBAAgBjE,SAASkG,EAElC9I,GAAK6G,gBAAgBxF,KAAO,OAIxC6C,MAAO,SAAU3K,EAAGwP,GAAb,GAYCjN,GAAOkN,EAASC,EAAQH,EAAMI,EAAYC,EAX1CnJ,EAAOvG,KACPsG,EAAUC,EAAKD,QACfqJ,EAAM7P,EAAE8P,QACR1G,EAAQ3C,EAAK2H,OACb/B,EAAM7F,EAAQ6F,IACdC,EAAM9F,EAAQ8F,IACdyD,EAActJ,EAAKF,QAAQkC,KAAKtE,EAAMM,GACtCgJ,EAAQsC,EAAYrC,QAAQ,SAC5BsC,EAAe,GAAIrN,OAAO8D,EAAK0G,UAAYhK,EAAa4M,EAAYtH,KAAK,SACzEwH,EAAQ9N,EAAMc,QAAQgN,MAAMxJ,EAAKqD,SACjCuE,GAAW,CAgDf,IA9CIwB,GAAOtM,EAAK2M,QAAUD,GAASJ,GAAOtM,EAAK4M,MAAQF,GACnD1N,EAAQ,EACRkN,GAAU,GACHI,GAAOtM,EAAK4M,OAASF,GAASJ,GAAOtM,EAAK2M,OAASD,GAC1D1N,KACAkN,GAAU,GACHI,GAAOtM,EAAK6M,IACnB7N,EAAkB,IAAV6G,QACRqG,GAAU,GACHI,GAAOtM,EAAK8M,MACnB9N,EAAkB,IAAV6G,EAAc,EAAI,EAC1BqG,GAAU,GACHI,GAAOtM,EAAK+M,UACnB/N,EAAQ,EACRkN,GAAU,GACHI,GAAOtM,EAAKgN,MACnBd,GAAU,EACVF,EAAO9B,EAAMhF,KAAKpD,GAAmBmL,GAAG,GACpCjB,EAAKpP,SAASsE,IACdgJ,EAAQA,EAAMgD,OACVhD,EAAMxB,OACNxF,EAAKiK,WAAWjD,EAAMhF,KAAKpD,GAAmBmL,GAAG,KAEjDnC,EAAW5H,EAAKhB,KAAegB,EAAKhB,GAAWtF,SAAS6E,GACxDyB,EAAKwH,UAAUxI,KAAe+J,GAC9B/I,EAAKiK,WAAWjK,EAAKF,QAAQkC,KAAK,eAAiBpD,EAAoB,aAG3EoB,EAAKiK,WAAWnB,IAEbM,GAAOtM,EAAKwB,MACnB0K,GAAU,EACVF,EAAO9B,EAAMhF,KAAKpD,GAAmBtC,OACjCwM,EAAKpP,SAASsE,IACdgJ,EAAQA,EAAMkD,OACVlD,EAAMxB,OACNxF,EAAKiK,WAAWjD,EAAMhF,KAAKpD,GAAmBtC,SAE9CsL,EAAW5H,EAAKf,KAAee,EAAKf,GAAWvF,SAAS6E,GACxDyB,EAAKwH,UAAUvI,EAAW,EAAG8J,GAC7B/I,EAAKiK,WAAWjK,EAAKF,QAAQkC,KAAK,cAAgBpD,EAAoB,YAG1EoB,EAAKiK,WAAWnB,IAGpBvP,EAAE4Q,SAAW5Q,EAAE6Q,QACXhB,GAAOtM,EAAK2M,QAAUD,GAASJ,GAAOtM,EAAK4M,MAAQF,GACnD5B,EAAW5H,EAAKf,KAAee,EAAKf,GAAWvF,SAAS6E,GACxDyB,EAAKwH,UAAUvI,EAAW,EAAG8J,GAC7BC,GAAU,GACHI,GAAOtM,EAAK4M,OAASF,GAASJ,GAAOtM,EAAK2M,OAASD,GAC1D5B,EAAW5H,EAAKhB,KAAegB,EAAKhB,GAAWtF,SAAS6E,GACxDyB,EAAKwH,UAAUxI,KAAe+J,GAC9BC,GAAU,GACHI,GAAOtM,EAAK6M,IACnB/B,GAAY5H,EAAK4G,OAAOlN,SAAS6E,GACjCyB,EAAK0H,aACL1H,EAAKiK,WAAWjK,EAAKmH,YAAYnH,EAAK0G,WAAYqC,GAClDC,GAAU,GACHI,GAAOtM,EAAK8M,MACY,UAA3B5J,EAAKyG,aAAa7F,KAClBZ,EAAKlE,MAAMyN,IAEXvJ,EAAK6H,aAAa0B,GAClBvJ,EAAKiK,WAAWjK,EAAKmH,YAAYnH,EAAK0G,WAAYqC,GAClDnB,GAAW,GAEfoB,GAAU,GACHI,GAAOtM,EAAKuN,OAASjB,GAAOtM,EAAK+M,UACb,aAAvB9J,EAAQuF,YACRtF,EAAKsK,iBAAiB/Q,OAG3B,IAAIA,EAAEgR,UAAmC,WAAvBxK,EAAQuF,YAC7B,GAAIxJ,IAAUzC,GAAa4P,EAAQ,CAI/B,GAHKA,GACDjJ,EAAKyG,aAAa/L,QAAQ6O,EAAczN,GAEb,UAA3BkE,EAAKyG,aAAa7F,KAClB,MAEAb,GAAQ1F,aAAakP,KACrBA,EAAevJ,EAAKwK,iBAAiBjB,EAAczN,IAEvD8J,EAAMjJ,EAAWiJ,EAAItK,cAAesK,EAAIrK,WAAYqK,EAAIjL,WACpD8B,EAAU8M,EAAc3D,EAAKC,KACxB7F,EAAKkH,aAAaqC,KACfzN,EAAQ,GACR8L,EAAW5H,EAAKf,KAAee,EAAKf,GAAWvF,SAAS6E,GACxDyB,EAAKwH,UAAUvI,EAAW,EAAG8J,KAE7BnB,EAAW5H,EAAKhB,KAAegB,EAAKhB,GAAWtF,SAAS6E,GACxDyB,EAAKwH,UAAUxI,KAAe+J,KAGtCD,EAAO9I,EAAKmH,YAAYoC,GACxBvJ,EAAK0G,SAAW6C,EACZvJ,EAAKsF,aACLtF,EAAKyK,aAAa/N,GAAcsD,EAAKsF,WAAWnE,aAAemI,GAAatH,KAAK,MAAOuH,GACnFvJ,EAAKsF,WAAWnE,cACjBnB,EAAKsF,WAAWnE,YAAcmI,GAElCtJ,EAAKgD,QAAQnF,GACbmC,EAAKiK,WAAWnB,IAEhB9I,EAAK6G,kBACLqC,EAAaxM,GAAcsD,EAAK6G,gBAAgB1F,aAAemI,GAAatH,KAAK,MAC5EhC,EAAKkH,aAAagC,IASflJ,EAAK6G,gBAAgB1F,YADrBnB,EAAK6G,gBAAgBxF,MAAQrB,EAAK6G,gBAAgBxF,KAAKG,GAAG9D,EAAMM,GAC7BgC,EAAK6G,gBAAgBzF,OAErBpB,EAAKmH,YAAY+B,GAExDlJ,EAAK6G,gBAAgB/D,MAAM9C,EAAK6G,gBAAgB1F,YAAa2H,KAZxDI,GAAcK,GACfvJ,EAAK6G,gBAAgBxF,KAAOrB,EAAK6G,gBAAgB1F,YACjDnB,EAAK6G,gBAAgBtE,WAAWuG,IAEhC9I,EAAK6G,gBAAgBjE,SAASkG,GAUtC9I,EAAK6G,gBAAgB9D,SACrB/C,EAAKiK,WAAWnB,UAKxBM,IAAOtM,EAAKuN,OAASjB,GAAOtM,EAAK+M,UACF,UAA3B7J,EAAKyG,aAAa7F,MACdZ,EAAKsF,aACLtF,EAAKsF,WAAWnE,YAAcnB,EAAKmH,YAAYoC,IAEnDvJ,EAAKlE,MAAMyN,GACPvJ,EAAK6G,iBACL7G,EAAK6G,gBAAgB9D,UAGzB/C,EAAKyE,OAAOrL,EAAE4G,EAAKwG,MAAM,GAAGlC,YAAayE,GAE7CC,GAAU,GACHI,GAAOtM,EAAK4N,QAAUtB,GAAOtM,EAAK6N,WACzC3B,GAAU,EACVG,EAAYnC,EAAMhF,KAAKpD,GAAmB+D,MAAM2G,GAChDtC,EAAQoC,GAAOtM,EAAK4N,OAAS1D,EAAMgD,OAAShD,EAAMkD,OAC7ClD,EAAMxB,SACH4D,GAAOtM,EAAK4N,QACZ9C,EAAW5H,EAAKhB,KAAegB,EAAKhB,GAAWtF,SAAS6E,GACxDyB,EAAKuH,iBACLP,EAAQhH,EAAKF,QAAQkC,KAAK,iBAE1B4F,EAAW5H,EAAKf,KAAee,EAAKf,GAAWvF,SAAS6E,GACxDyB,EAAKyH,mBACLT,EAAQhH,EAAKF,QAAQkC,KAAK,gBAGlC8G,EAAO9B,EAAMhF,KAAKpD,GAAmBmL,GAAGZ,GAEpCnJ,EAAKiK,WADLnB,EAAKtD,OACWsD,EAEA9B,EAAMhF,KAAKpD,GAAmBtC,UAGlDR,GAASmN,KACJA,GACDjJ,EAAKyG,aAAa/L,QAAQ6O,EAAczN,GAE5C8J,EAAMjJ,EAAWiJ,EAAItK,cAAesK,EAAIrK,WAAYqK,EAAIjL,WACpD8B,EAAU8M,EAAc3D,EAAKC,KACzB7F,EAAKsF,YAAcvF,EAAQ1F,aAAakP,KACxCA,EAAevJ,EAAKwK,iBAAiBjB,EAAczN,IAElDkE,EAAKkH,aAAaqC,KACfzN,EAAQ,GACR8L,EAAW5H,EAAKf,KAAee,EAAKf,GAAWvF,SAAS6E,GACxDyB,EAAKwH,UAAUvI,EAAW,EAAG8J,KAE7BnB,EAAW5H,EAAKhB,KAAegB,EAAKhB,GAAWtF,SAAS6E,GACxDyB,EAAKwH,UAAUvI,KAAe8J,KAGtCD,EAAO9I,EAAKmH,YAAYoC,GACxBvJ,EAAK0G,SAAW6C,EAChBvJ,EAAKiK,WAAWnB,GAAOC,IAUnC,OANInB,IACA5H,EAAKgD,QAAQlF,GAEbkL,GACAzP,EAAEiL,iBAECxE,EAAK0G,UAEhBkE,8BAA+B,WAAA,GAGvBC,GAFA7K,EAAOvG,KACPqR,IAEJ1R,GAAE6I,KAAKjC,EAAKqF,aAAc,SAAU1C,EAAO7G,GACvCgP,EAAcpP,EAAMP,SAASC,MAAM,GAAG2P,aAAajP,IAAUA,IAEjEkE,EAAKsF,WAAWlD,QAChByI,EAAQ7K,EAAKF,QAAQkC,KAAK,SAASA,KAAKrD,GAAc4B,OAAO,SAAUoC,EAAO7C,GAC1E,MAAOgL,GAAc1R,EAAE0G,EAAQwE,YAAYK,KAAKjJ,EAAMiJ,KAAK5G,OAE3D8M,EAAMrF,OAAS,GACfxF,EAAKsF,WAAW0F,eAAeH,GAAO,IAG9CL,iBAAkB,SAAUjB,EAAczN,GAAxB,GACVkE,GAAOvG,KACPD,GAAW,EACX8N,EAAOtH,EAAKyG,aACZb,EAAM5F,EAAKD,QAAQ6F,IACnBC,EAAM7F,EAAKD,QAAQ8F,IACnBoF,EAAajL,EAAKD,QAAQ1F,aAC1B6Q,EAAkB,GAAI1Q,MAAK+O,EAAa9O,UAE5C,KADA6M,EAAK5M,QAAQwQ,GAAkBpP,GACxBtC,GAAU,CAEb,GADA8N,EAAK5M,QAAQ6O,EAAczN,IACtBW,EAAU8M,EAAc3D,EAAKC,GAAM,CACpC0D,EAAe2B,CACf,OAEJ1R,EAAWyR,EAAW1B,GAE1B,MAAOA,IAEXe,iBAAkB,SAAUa,GACxB,GAAInL,GAAOvG,IACXuG,GAAKsF,WAAWnE,YAAc/H,EAAE4G,EAAKwG,MAAM,IACvCpN,EAAE4G,EAAKwG,MAAM,IAAI9M,SAASuE,IAC1B+B,EAAKsF,WAAW8F,UAAUhS,EAAE4G,EAAKwG,MAAM,KACvCxG,EAAKsF,WAAWtC,QAAQnF,GAAUsN,MAAOA,KAEzCnL,EAAKsF,WAAWxJ,MAAM1C,EAAE4G,EAAKwG,MAAM,KAAO2E,MAAOA,KAGzD9D,QAAS,SAAUgE,EAAQvP,GAAlB,GAIDwP,GAHAtL,EAAOvG,KACPsG,EAAUC,EAAKD,QACfwJ,EAAevJ,EAAK8H,QAAU9H,EAAK0G,QAEvC,OAAI5K,KAAUzC,EACH0G,EAAQsL,IAEnBvP,EAAQJ,EAAM6P,UAAUzP,EAAOiE,EAAQwD,OAAQxD,EAAQqD,SAClDtH,IAGLiE,EAAQsL,GAAU,GAAInP,MAAMJ,IAExBwP,EADAD,IAAW7N,EACA1B,EAAQyN,EAERA,EAAezN,EAE1BwP,IACAtL,EAAK8H,OAAS,MAElB9H,EAAK4H,SAAS5H,EAAK8H,QACnB9H,EAAKwL,WAdL1P,IAgBJqL,YAAa,SAAUrL,GAInB,MAHIA,aAAiBtB,QACjBsB,EAAQrC,KAAKgN,aAAasE,aAAajP,IAEpCrC,KAAKqG,QAAQkC,KAAK,SAASA,KAAK,WAAavD,EAAa,KAAK8B,OAAO,WACzE,MAAOnH,GAAEK,KAAK6K,YAAYK,KAAKjJ,EAAMiJ,KAAK5G,MAAYjC,KAG9DoJ,YAAa,WAAA,GACLlF,GAAOvG,KACP6L,EAAatF,EAAKD,QAAQuF,UAC1BtF,GAAKsF,aACLtF,EAAKsF,WAAWpE,UAChBlB,EAAKsF,WAAa,MAElBtF,EAAK6G,kBACL7G,EAAK6G,gBAAgB3F,UACrBlB,EAAK6G,gBAAkB,MAEM,UAA7BvB,EAAWmG,cACXzL,EAAK6G,gBAAkB,GAAIlH,IAAgBK,EAAKqD,SAC5C9C,OAAQ,iBAAmB3B,EAC3BmE,OAAQzD,GAAMU,EAAK0L,gBAAiB1L,KAGxCA,EAAKsF,WAAa,GAAIrI,GAAW+C,EAAKqD,SAClCsI,MAAM,EACN5K,cAAc,EACdF,eAAgB,0IAChBC,SAAU7D,EAAW2O,aAAatG,GAAYxE,SAC9CP,OAAQ,mBAAqB3B,EAC7BmE,OAAQzD,GAAMU,EAAK6L,WAAY7L,GAC/BgB,cAAe1B,GAAMU,EAAK8L,iBAAkB9L,GAC5C+L,SAAUzM,GAAMU,EAAKgM,aAAchM,MAI/C8L,iBAAkB,SAAUvK,GACxB,GAAIvB,GAAOvG,IACPuG,GAAKsF,WAAWvF,QAAQe,UAAYS,EAAOC,GAAG5C,IAAsB2C,EAAOiE,OAAS,GACpFxF,EAAKiK,WAAW1I,EAAO6F,SAAS,IAGxC6E,kBAAmB,SAAUC,GAAV,GAIXxK,GACAE,EAEAtH,EANA0F,EAAOvG,KACPsG,EAAUC,EAAKD,QACfoM,KAGAlQ,EAAU,GAAIzB,QAAMwF,EAAK0G,UAE7B,KAAKpM,EAAI,EAAGA,EAAIyF,EAAQ3E,MAAOd,IAAK,CAGhC,GAFAoH,EAAQwK,EAAY9E,MAAMnL,GAC1B2F,EAAMsK,EAAY5P,KAAKL,IAClB2F,GAAO7B,EAAQ8F,IAAK,EAChBnE,IAAU3B,EAAQ8F,KACnBsG,EAAOvR,MACH8G,MAAOA,EACPE,IAAK,GAAIpH,QAAMuF,EAAQ8F,OAG/B,OAEJsG,EAAOvR,MACH8G,MAAOA,EACPE,IAAKA,IAET3F,EAAU,GAAIzB,QAAMqB,EAAU+F,EAAKsK,EAAYtL,KAAM,KAGzD,IADA3E,EAAU,GAAIzB,QAAMwF,EAAK0G,WACpBpM,EAAI,EAAGA,EAAIyF,EAAQ3E,MAAOd,IAAK,CAGhC,GAFAoH,EAAQwK,EAAY9E,MAAMnL,GAC1B2F,EAAMsK,EAAY5P,KAAKL,IAClByF,GAAS3B,EAAQ6F,IAAK,EAClBhE,IAAQ7B,EAAQ6F,KACjBuG,EAAOvR,MACH8G,MAAO,GAAIlH,QAAMuF,EAAQ6F,MACzBhE,IAAKA,GAGb,OAEJuK,EAAOvR,MACH8G,MAAOA,EACPE,IAAKA,IAET3F,EAAU,GAAIzB,QAAMqB,EAAU6F,EAAOwK,EAAYtL,WAGrD,IADAc,EAAQyK,EAAO,GAAGzK,MACbpH,EAAI,EAAGA,EAAIyF,EAAQ3E,MAAQ,GACvB+Q,EAAO7R,GADmBA,KAI1BoH,GAASyK,EAAO7R,GAAGoH,QACpBA,EAAQyK,EAAO7R,GAAGoH,MAG1B,OAAO,IAAIlH,QAAMkH,KAErB0K,mBAAoB,SAAUC,GAAV,GACZC,GAAWD,EAAS/Q,cACpBiE,EAAQ8M,EAAS9Q,WACjBI,EAAO0Q,EAAS1R,UAChBkL,EAAMpM,KAAKsG,QAAQ8F,IACnB0G,EAAU1G,EAAIvK,cACdkR,EAAW3G,EAAItK,UACnB,OAAI+Q,GAAWC,IAGXD,IAAaC,GAAWhN,EAAQiN,IAGhCF,IAAaC,GAAWhN,IAAUiN,GAAY7Q,EAAOkK,EAAIlL,WAGzD2R,IAAaC,GAAWhN,IAAUiN,GAAY7Q,IAASkK,EAAIlL,aAKnEqK,WAAY,SAAUyH,GAAV,GAKJJ,GAOK/R,EAXL0F,EAAOvG,KACPsG,EAAUC,EAAKD,QACf4C,EAAQxH,EAAS6M,UAAUyE,EAAYxH,UACvCiH,EAAc/Q,EAASC,MAAMuH,EAQjC,KANA3C,EAAK0G,SAAW,GAAIxK,MAAMf,EAAS8M,cAAcwE,EAAY3Q,MAAOiE,EAAQ6F,IAAK7F,EAAQ8F,OACzF7F,EAAKqG,UACLrG,EAAK2H,OAAShF,EACd0J,EAAWrM,EAAKiM,kBAAkBC,GAClCG,EAAS3R,QAAQ,GACjBsF,EAAKuI,gBAAkB,GAAI/N,QAAM6R,IACxB/R,EAAI,EAAGA,EAAIyF,EAAQ3E,QACxBiR,EAAW/R,EAAIuB,EAAUwQ,EAAUH,EAAYtL,KAAM,GAAKyL,EAC1DA,EAAS3R,QAAQ,GACZsF,EAAKoM,mBAAmBC,IAHE/R,IAM/B0F,EAAK0M,OAAStT,EAAE8S,EAAYS,QAAQvT,EAAEwG,QAClCgG,IAAK7F,EAAQ6F,IACbC,IAAK9F,EAAQ8F,IACblK,KAAM0Q,EACNO,IAAK7M,EAAQ6M,IACb9G,MAAO/F,EAAQ+F,MACfvC,OAAQxD,EAAQwD,OAChBH,QAASrD,EAAQqD,QACjB/I,aAAc0F,EAAQ1F,aACtBwS,WAAY9M,EAAQgG,eACpB+G,oBAAqB/M,EAAQiE,WAC7B+I,WAAYhN,EAAQgN,WACpB9G,SAAUlG,EAAQkG,UACnBjG,EAAKkM,EAAYtL,SACpBZ,EAAK0M,OAAO7D,SAAS7I,EAAKgN,eAAerL,SAAS,KAAOuK,EAAYtL,MACrEZ,EAAKqG,OAAOzL,KAAKoF,EAAK0M,OAE1B1M,GAAKyG,aAAeyF,EACpBlM,EAAKgN,cAAcrI,KAAK,QAAS,8BAAgCuH,EAAYtL,KAAO,QACpFZ,EAAKkI,iBAETwD,gBAAiB,SAAUnS,GAAV,GAGTmI,GACAE,EAHA5B,EAAOvG,KACPqJ,EAAQvJ,EAAE0T,OAAOnK,OAGjBA,GAAMpB,QACNA,EAAQhF,EAAaoG,EAAMpB,MAAMM,KAAK,OAEtCc,EAAMlB,MACNA,EAAMlF,EAAaoG,EAAMlB,IAAII,KAAK,OAEtChC,EAAK+E,QACDrD,MAAOA,EACPE,IAAKA,GAEJ5B,EAAKkN,gBACNlN,EAAKgD,QAAQnF,IAGrBgO,WAAY,SAAUtS,GAAV,GAMJgQ,GALAvJ,EAAOvG,KACP0T,EAAiB5T,EAAE0T,OAAOnR,QAC1BsR,EAAW7T,EAAE4R,MACb9G,EAAgBjL,EAAEgU,GAAYA,EAAS/I,eACvCgJ,EAAShJ,EAAc7C,GAAG,KAEE,YAA5BxB,EAAKD,QAAQuF,YACbtF,EAAKsN,eAAeH,EAAe,GAAKzQ,EAAayQ,EAAe/F,QAAQpF,KAAK,MAAQzI,EAAE0T,OAAO9L,YAAczE,EAAanD,EAAE0T,OAAO9L,YAAYa,KAAK,MAAQhC,EAAKlE,SAEzI,YAA3BkE,EAAKD,QAAQuF,aACT+H,IACA9D,EAAe7M,EAAa2H,EAAcrC,KAAK,OAE/CoL,GAAYA,EAASjD,QACjBkD,EACIhJ,EAAc3K,SAASuE,GACvB+B,EAAKqF,aAAazK,KAAK2O,GAEvBvJ,EAAKuN,UAAUhE,IAGnBvJ,EAAKF,QAAQkC,KAAK,SAAWpD,GAAmBqD,KAAK,SAAUU,EAAO7C,GAClE,GAAIhE,GAAQY,EAAatD,EAAE0G,GAASkC,KAAK,KACzChC,GAAKuN,UAAUzR,KAEnBkE,EAAKwN,4BAEFJ,GAAYA,EAAS7C,SAC5BvK,EAAKyK,aAAa/N,EAAanD,EAAE0T,OAAO9L,YAAc5H,EAAE0T,OAAO9L,YAAYa,KAAK,KAAOmL,EAAe/F,QAAQpF,KAAK,MAAOuH,GACnH8D,GACPrN,EAAKqF,gBACLrF,EAAKqF,aAAazK,KAAK2O,KAEvBvJ,EAAKqF,gBACLrF,EAAKwN,6BAGRxN,EAAKkN,gBACNlN,EAAKgD,QAAQnF,IAGrB2P,yBAA0B,WACtB,GAAIxN,GAAOvG,IACXuG,GAAKsF,WAAWxJ,QAAQmG,KAAK,SAAUU,EAAO8K,GAC1C,GAAI9R,GAAOe,EAAatD,EAAEqU,EAAKnJ,YAC1BtE,GAAKD,QAAQ1F,aAAasB,IAC3BqE,EAAKqF,aAAazK,KAAKe,MAInC4R,UAAW,SAAU5R,GAAV,GACHqE,GAAOvG,KACPiU,EAAmB1N,EAAKqF,aAAasI,IAAIC,QAAQ9T,SAAS6B,EAC1D+R,QACA1N,EAAKqF,aAAawI,OAAOH,EAAkB,IAGnD1B,aAAc,SAAUzS,GAAV,GACNyG,GAAOvG,KACPqG,EAAUvG,EAAEuG,OACgB,YAA5BE,EAAKD,QAAQuF,aAA4BnI,GAAY2C,EAAQpG,SAASsE,IACtEzE,EAAEiL,kBAGViE,cAAe,WAAA,GACP1G,GAAStI,KAAKqG,QAAQkC,KAAK,0BAC3B8L,EAAkBpR,EAAaqF,EAAOqF,QAAQpF,KAAKrD,EAAe,UAAUqD,KAAK,MACjF+L,EAAiBrR,EAAaqF,EAAOzF,OAAO0F,KAAKrD,EAAe,SAASqD,KAAK,KAClF,QACIN,MAAOoM,EACPlM,IAAKmM,IAGb7G,aAAc,SAAUvL,GAAV,GACNqE,GAAOvG,KACPsI,EAAS/B,EAAKF,QAAQkC,KAAK,0BAC3B8L,EAAkBpR,EAAaqF,EAAOqF,QAAQpF,KAAKrD,EAAe,UAAUqD,KAAK,MACjF+L,EAAiBrR,EAAaqF,EAAOzF,OAAO0F,KAAKrD,EAAe,SAASqD,KAAK,KAClF,QAAQrG,IAASoS,IAAmBpS,IAASmS,GAEjDE,WAAY,SAAUtM,EAAOE,GAAjB,GAEJqM,GADAjO,EAAOvG,IAEXuG,GAAKqF,gBACL4I,EAAiBpT,EAAoB6G,EAAOE,GAC5C3H,EAAe+F,EAAKqF,aAAc4I,EAAgBvM,EAAO1B,EAAKD,QAAQ1F,eAE1EoQ,aAAc,SAAU/I,EAAOE,GAAjB,GAEN3F,GADA+D,EAAOvG,MAENmI,GAAOF,IACRzF,EAAU2F,EACVA,EAAMF,EACNA,EAAQzF,GAEZ+D,EAAKgO,WAAWtM,EAAOE,GACvB5B,EAAK4K,iCAET9G,QAAS,WAAA,GAGDoK,GAFAlO,EAAOvG,KACPqG,EAAUE,EAAKF,QAEf6G,EAAS7G,EAAQkC,KAAK,qBACrB2E,GAAOnB,SACRmB,EAASvN,EAAE,oOAAqPiG,GAAa,sIAA6IA,GAAa,4EAAsF8O,UAAUrO,IAE3gBE,EAAK2G,OAASA,EACdA,EAAO1C,GAAGpK,EAAauD,EAAK,IAAM2B,EAAgB,IAAM/E,EAAQoD,EAAK,IAAMyB,EAAOzB,EAAI,YAAa9D,GAAa2K,GAAG,QAAS,WACxH,OAAO,IACRA,GAAG5G,EAAQD,EAAI,oBAAqB,WACnC4C,EAAK0H,aACL1H,EAAKiK,WAAWjK,EAAKmH,YAAYnH,EAAK0G,WAAW,GACjD1G,EAAKgD,QAAQlF,KACdmG,GAAG5G,EAAQD,EAAI,wBAAyB,SAAU7D,GACjDA,EAAEiL,iBACFxE,EAAKuH,iBACLvH,EAAKgD,QAAQlF,KACdmG,GAAG5G,EAAQD,EAAI,wBAAyB,SAAU7D,GACjDA,EAAEiL,iBACFxE,EAAKyH,mBACLzH,EAAKgD,QAAQlF,KAEjBoQ,EAAUvH,EAAO3E,KAAK,aACtBhC,EAAK4G,OAASsH,EAAQ3N,OAAO,YAC7BP,EAAK4I,cAAgBjC,EAAO3E,KAAK,mBACjChC,EAAKhB,GAAakP,EAAQ3N,OAAO,gBACjCP,EAAKf,GAAaiP,EAAQ3N,OAAO,iBAErCwD,SAAU,WACNtK,KAAKuT,cAAgB5T,EAAE,mCAAmCgV,YAAY3U,KAAKqG,QAAQ,GAAGwE,aAE1FT,WAAY,WAAA,GACJ7D,GAAOvG,KACPsG,EAAUC,EAAKD,QACfR,EAAQQ,EAAQR,MAChBoN,EAAUpN,EAAMoN,QAChB3I,EAAazE,EAAMyE,WACnBqK,EAAQ9O,EAAM8O,KAClBrO,GAAKT,OACDoN,QAASzP,EAAS,2GAA6GxB,EAAMiJ,KAAK5G,GAAS,gDAAkD4O,GAAW,iBAAmB,aAAe2B,eAAgB3B,IAClQ0B,MAAOnR,EAAS,uBAAyBmR,EAAQ,IAAM,6BAA+BA,GAAS,0BAA8B,SAAWC,eAAgBD,IACxJrK,WAAY9G,EAAS,sBAAwB8G,GAAc,wBAA0B,SAAWsK,eAAgBtK,MAGxHmB,QAAS,WAAA,GACDnF,GAAOvG,KACPsG,EAAUC,EAAKD,QACf7C,EAAW6C,EAAQqF,UAAW,EAAQ1J,EAAMwB,SAAS8C,EAAKD,QAAQqF,QAAU,+BAAiCrF,EAAQqD,QAAU,QAAUkL,cAAc,IAAW,KAClKC,EAAQ1R,IACRiD,EAAUE,EAAKF,QACfsF,EAAStF,EAAQkC,KAAK,YAC1B,OAAK9E,IAKAkI,EAAO,KACRA,EAAShM,EAAE,2EAA2EyP,SAAS/I,IAEnGE,EAAK8G,OAAS1B,EAAOoJ,OAAOxM,KAAK,WAAW0G,KAAKxL,EAASqR,IAAQ5J,KAAK,QAASjJ,EAAM+S,SAASF,EAAO,IAAKvO,EAAKD,QAAQqD,UACxHpD,EAAKwL,UAJL,IAJIxL,EAAKwL,SAAQ,GACbpG,EAAOsJ,OACP,IAQRlH,UAAW,SAAUmH,EAAOC,EAAU7F,GAA3B,GAKH8F,GAJA7O,EAAOvG,KACPkJ,EAAQ3C,EAAK2H,OAAS,EACtB4B,EAAe,GAAIrN,MAAM8D,EAAK0G,WAC9BoI,EAAe,GAAI5S,MAAM8D,EAAK0G,UAElCiI,GAAQ3O,EAAK2O,GACbE,EAAS7O,EAAKmH,YAAYoC,GAActC,QAAQ,SAAStE,QACrDiM,EAAW,EACXC,EAAS,EAAIA,EAEbA,GAAkB,EAEjBF,GAAUA,EAAMjV,SAAS6E,KACtBoE,EAAQ,EACR4G,EAAapN,YAAYoN,EAAajO,cAAgB,KAAOsT,EAAWC,IAExE1T,EAASC,MAAMuH,GAAOjI,QAAQ6O,EAAcqF,EAAWC,GAE3D7O,EAAK4H,SAAS2B,GACVvJ,EAAKkH,aAAa4H,IAClB9O,EAAKiK,WAAWjK,EAAKmH,YAAY2H,IAAgB/F,GACjD/I,EAAK0G,SAAWoI,IAEZnM,EAAQ,EACRmM,EAAa3S,YAAY2S,EAAaxT,cAAgB,IAAMsT,GAE5DzT,EAASC,MAAMuH,GAAOjI,QAAQoU,EAAcF,GAEhD5O,EAAKiK,WAAWjK,EAAKmH,YAAY2H,IAAgB/F,GACjD/I,EAAK0G,SAAWoI,KAI5BtD,QAAS,SAAUuD,GAAV,GACD/O,GAAOvG,KACPsG,EAAUC,EAAKD,QACfiP,EAAyC,UAAvBjP,EAAQuF,YAA0BtF,EAAKD,QAAQ1F,aAAawC,KAC9EuH,EAAOpE,EAAK8G,MACZiI,KAAW1V,IACX0V,EAAStS,EAAUI,IAAYkD,EAAQ6F,IAAK7F,EAAQ8F,MAEpDzB,IACAA,EAAKkC,IAAIjJ,EAAQD,GACb2R,IAAWC,EACX5K,EAAKzC,SAASnD,GAAO8D,YAAY/D,GAAU0F,GAAG5G,EAAQD,EAAIkC,GAAMU,EAAKiP,YAAajP,IAElFoE,EAAK9B,YAAY9D,GAAOmD,SAASpD,GAAU0F,GAAG5G,EAAQD,EAAI,SAAiB7D,GACvEA,EAAEiL,qBAKlBC,OAAQ,SAAUL,EAAM2E,GAAhB,GACA/I,GAAOvG,KACPsG,EAAUC,EAAKD,QACfwJ,EAAe,GAAI/O,QAAMwF,EAAK0G,WAC9B5K,EAAQY,EAAa0H,EACzB1I,GAAMC,KAAKuT,UAAUpT,EAAO,GAC5BkE,EAAKyG,aAAa/L,QAAQ6O,EAAczN,GACxCkE,EAAK0G,SAAW5K,EACZkE,EAAKyG,aAAa7F,OAASb,EAAQiG,OACnChG,EAAK6H,aAAa1M,EAAS8M,cAAcsB,EAAcxJ,EAAQ6F,IAAK7F,EAAQ8F,MAC5E7F,EAAKiK,WAAWjK,EAAKmH,YAAYnH,EAAK0G,WAAYqC,GAClD/I,EAAKgD,QAAQlF,IAEbkC,EAAKiK,WAAW7F,EAAK6C,QAAQ,OAAQ8B,IAG7C5E,MAAO,WACH,GAAInE,GAAOvG,IACPuG,GAAKwG,OACLxG,EAAKwG,MAAMlE,YAAYtE,IAG/BmR,OAAQ,SAAU5V,GAAV,GACAyG,GAAOvG,KACPuN,EAAQ5N,EAAEG,EAAE8K,eACZyE,EAAO9I,EAAKwG,KACXsC,IAAS1P,EAAEgW,SAASpI,EAAM,GAAI8B,EAAK,MACpCA,EAAO9B,EAAMhF,KAAKpD,EAAoB,WAE1CoB,EAAKiK,WAAWnB,IAEpBmB,WAAY,SAAUnB,EAAMuG,GAAhB,GACJrP,GAAOvG,KACP6V,EAAStP,EAAK4E,QACdoC,EAAQ8B,EAAK7B,QAAQ,QACrBjH,GAAKwG,OAASxG,EAAKwG,MAAMhB,SACzBxF,EAAKwG,MAAM,GAAG+I,gBAAgBrQ,GAC9Bc,EAAKwG,MAAM,GAAG+I,gBAAgBlQ,IAC9BW,EAAKwG,MAAMlE,YAAYtE,GACvBgC,EAAKwG,MAAM,GAAG+I,gBAAgBhS,GAC9ByC,EAAKwG,MAAMS,QAAQ,SAAS,GAAGsI,gBAAgB,0BAEnDvP,EAAKwG,MAAQsC,EACTuG,GACArI,EAAMhE,QAAQ,SAEdsM,IACAxG,EAAKnE,KAAKpH,EAAI+R,GACdtI,EAAMrC,KAAK,wBAAyB2K,IAExCxG,EAAKnE,KAAKzF,GAAe,GAAMyC,SAAS3D,GACpC8K,EAAKtD,QAAoC,SAA1BxF,EAAKyG,aAAa7F,OACjCZ,EAAK0G,SAAWhK,EAAaoM,EAAK9G,KAAK,QAG/CiN,YAAa,SAAU1V,GAAV,GACLyG,GAAOvG,KACPD,EAAWwG,EAAKD,QAAQ1F,aACxBkU,EAAQ1R,IACR+K,GAAW,CACfrO,GAAEiL,iBACEhL,EAAS+U,KAGbvO,EAAK8H,OAASyG,EACkB,aAA5BvO,EAAKD,QAAQuF,aACbtF,EAAKqF,cAAgBkJ,IAEO,UAA5BvO,EAAKD,QAAQuF,aACbtF,EAAK6G,gBAAgBzE,OAAM,GAC3BpC,EAAK+E,QACDrD,MAAO6M,EACP3M,IAAK,OAGiB,SAA1B5B,EAAKyG,aAAa7F,MAAoBZ,EAAKkH,aAAaqH,KACxD3G,GAAW,GAEf5H,EAAK4H,SAAS2G,EAAOvO,EAAKD,QAAQiG,OACF,WAA5BhG,EAAKD,QAAQuF,aACbtF,EAAKsF,WAAWnE,YAAc,MAE9ByG,GACA5H,EAAKgD,QAAQlF,GAEjBkC,EAAKgD,QAAQnF,KAEjByP,eAAgB,SAAUxR,GAAV,GACRkE,GAAOvG,KACPsG,EAAUC,EAAKD,QACf6F,EAAM7F,EAAQ6F,IACdC,EAAM9F,EAAQ8F,GAalB,OAZA/J,GAAQJ,EAAM6P,UAAUzP,EAAOiE,EAAQwD,OAAQxD,EAAQqD,SACzC,OAAVtH,IACAA,EAAQ,GAAII,MAAMJ,IACbW,EAAUX,EAAO8J,EAAKC,KACvB/J,EAAQ,OAGF,OAAVA,GAAmBkE,EAAKD,QAAQ1F,aAAa,GAAIG,QAAMsB,KAEhDkE,EAAK8H,SAAWzO,IACvB2G,EAAK8H,OAAS,MAFd9H,EAAK8H,OAAShM,EAIXkE,EAAK8H,QAEhB0H,eAAgB,WACZ,GAAIxP,GAAOvG,IACPuG,GAAKsF,YACLtF,EAAKF,QAAQkC,KAAKtE,EAAMO,GAAUqE,YAAYrE,GAE9C+B,EAAK6G,iBACL7G,EAAK6G,gBAAgBzE,OAAM,IAGnCqD,kBAAmB,WAAA,GAEX3C,GADA9C,EAAOvG,KAEP6L,EAAatF,EAAKD,QAAQuF,UAC9B,IAAItF,EAAKyG,aAAa7F,OAASZ,EAAKD,QAAQiG,MAA5C,CAIA,GADAhG,EAAKkN,gBAAiB,EACH,UAAf5H,EAAwB,CAExB,GADAxC,EAAQ9C,EAAK0F,eACR5C,IAAUA,EAAMpB,MAEjB,MADA1B,GAAKkN,gBAAiB,EACtB,CAEJlN,GAAK0F,YAAY5C,GAEF,WAAfwC,GAA2BtF,EAAKlE,SAChCkE,EAAKsF,WAAWxJ,MAAMkE,EAAKmH,YAAYnH,EAAKlE,UAE7B,aAAfwJ,GACAtF,EAAK4K,gCAET5K,EAAKkN,gBAAiB,IAE1BpR,MAAO,SAAUA,GAAV,GAECgN,GADA9I,EAAOvG,IAEX,OAAIqC,KAAUzC,EACH2G,EAAK8H,QAEhBhM,EAAQkE,EAAKsN,eAAexR,GAC5BkE,EAAKwP,iBACD1T,IAAUkE,EAAKkH,aAAapL,IAC5BkE,EAAK4H,SAAS9L,GAEJ,OAAVA,GAAkBkE,EAAKyG,aAAa7F,OAASnD,IAC7CqL,EAAO9I,EAAKmH,YAAYrL,GACpBkE,EAAKsF,YACLtF,EAAKsF,WAAWxJ,MAAMgN,GAEtB9I,EAAK6G,kBACL7G,EAAK6G,gBAAgBnF,MAAMoH,GAC3B9I,EAAK6G,gBAAgB1F,YAAc2H,IAZ3ChN,IAgBJyJ,YAAa,SAAUO,GAAV,GAEL2J,GACAC,EAFA1P,EAAOvG,IAGX,OAAIqM,KAAUzM,EACH2G,EAAKqF,cAEhBqK,EAAc5J,EAAM6H,IAAI,SAAUhS,GAC9B,MAAOA,GAAKlB,YACb8F,OAAO,SAAU5E,EAAMgU,EAAUzV,GAChC,MAAOA,GAAMJ,QAAQ6B,KAAUgU,IAChChC,IAAI,SAAUiC,GACb,MAAO,IAAIpV,MAAKoV,KAEpBH,EAAqBrW,EAAEyW,KAAKH,EAAa,SAAU5T,GAC/C,GAAIA,EACA,OAAQkE,EAAKsN,eAAe,GAAI9S,MAAKsB,EAAMgU,SAAS,EAAG,EAAG,EAAG,QAAUhU,IAG/EkE,EAAKqF,aAAeoK,EAAmBjK,OAAS,EAAIiK,EAA4C,IAAvBC,EAAYlK,OAAekK,EAAc1P,EAAKqF,aACvHrF,EAAK4K,gCAbL8E,IAeJhK,YAAa,SAAU5C,GAAV,GAELiN,GACAC,EACAxH,EAHAxI,EAAOvG,IAIX,OAAIqJ,KAAUzJ,EACH2G,EAAK+E,QAEhB/E,EAAK+E,OAASjC,EACTA,EAAMpB,QAGX8G,EAAexI,EAAKyI,gBACpBsH,EAAe/P,EAAKkH,aAAapE,EAAMpB,OACvCsO,EAAalN,EAAMlB,KAAO5B,EAAKkH,aAAapE,EAAMlB,MAC7CmO,GAAgBC,GACjBhQ,EAAK6G,gBAAgBjE,SAAS5C,EAAKmH,YAAYrE,EAAMlB,MAErDmO,GAAgBC,GAChBhQ,EAAK6G,gBAAgB/D,MAAM9C,EAAKmH,YAAYrE,EAAMpB,OAAQ1B,EAAKmH,YAAYrE,EAAMlB,MAEjFkB,EAAMlB,KAAOmO,IAAiBC,GAC9BhQ,EAAK6G,gBAAgBtE,WAAWvC,EAAKmH,YAAYrE,EAAMpB,SAEtDoB,EAAMlB,KAAOmO,GACd/P,EAAK6G,gBAAgBnF,MAAM1B,EAAKmH,YAAYrE,EAAMpB,SAEjD8G,EAAa9G,OAASoB,EAAMpB,QAAU8G,EAAa5G,KAAOkB,EAAMlB,KACjE5B,EAAK6G,gBAAgBhF,IAAI7B,EAAKF,QAAQkC,KAAKpD,KApB/CoB,KAwBRtE,GAAMqB,GAAGkT,OAAO/M,KAiElB3G,OAAOb,MAAMwU,QACR3T,OAAOb,OACE,kBAAVvC,SAAwBA,OAAOgX,IAAMhX,OAAS,SAAUiX,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.multiviewcalendar.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.multiviewcalendar', [\n        'kendo.core',\n        'kendo.selectable',\n        'kendo.calendar'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'multiviewcalendar',\n        name: 'MultiViewCalendar',\n        category: 'web',\n        description: 'Multi-view calendar.',\n        depends: [\n            'core',\n            'selectable',\n            'calendar'\n        ]\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, calendar = kendo.calendar, support = kendo.support, isInRange = calendar.isInRange, toDateObject = calendar.toDateObject, createDate = calendar.createDate, isEqualDate = calendar.isEqualDate, getToday = calendar.getToday, keys = kendo.keys, ui = kendo.ui, Widget = ui.Widget, Selectable = ui.Selectable, template = kendo.template, mobileOS = support.mobileOS, ns = '.kendoMultiViewCalendar', CLICK = 'click', KEYDOWN = 'keydown', ID = 'id', MIN = 'min', MONTH = 'month', DOT = '.', CENTURY = 'century', DECADE = 'decade', CHANGE = 'change', NAVIGATE = 'navigate', VALUE = 'value', FOCUSED = 'k-state-focused', SELECTED = 'k-state-selected', MID = 'k-range-mid', SPLITEND = 'k-range-split-end', SPLITSTART = 'k-range-split-start', START = 'k-range-start', END = 'k-range-end', HOVER = 'k-state-hover', DISABLED = 'k-state-disabled', TODAY = 'k-nav-today', OTHERMONTH = 'k-other-month', OUTOFRANGE = 'k-out-of-range', CELLSELECTOR = 'td:has(.k-link):not(.' + OUTOFRANGE + ')', CELLSELECTORVALID = 'td:has(.k-link):not(.' + DISABLED + '):not(.' + OUTOFRANGE + ')', BLUR = 'blur', FOCUS = 'focus', MOUSEENTER = support.touch ? 'touchstart' : 'mouseenter', MOUSELEAVE_NS = support.touch ? 'touchend' + ns + ' touchmove' + ns : 'mouseleave' + ns, PREVARROW = '_prevArrow', NEXTARROW = '_nextArrow', ARIA_SELECTED = 'aria-selected', INPUTSELECTOR = 'input,a,textarea,.k-multiselect-wrap,select,button,.k-button>span,.k-button>img,span.k-icon.k-i-arrow-60-down,span.k-icon.k-i-arrow-60-up', ARIA_DISABLED = 'aria-disabled', ARIA_LABEL = 'aria-label', proxy = $.proxy, DATE = Date, views = {\n                month: 0,\n                year: 1,\n                decade: 2,\n                century: 3\n            };\n        var RangeSelectable = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                that.userEvents = new kendo.UserEvents(that.element, {\n                    global: true,\n                    allowSelection: true,\n                    filter: that.options.filter,\n                    tap: proxy(that._tap, that),\n                    touchAction: 'none'\n                });\n            },\n            events: [CHANGE],\n            options: {\n                name: 'RangeSelectable',\n                filter: '>*',\n                inputSelectors: INPUTSELECTOR,\n                multiple: false,\n                dragToSelect: true,\n                relatedTarget: $.noop\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.userEvents.destroy();\n                that._lastActive = that.element = that.userEvents = that._start = that._end = null;\n            },\n            _allowSelection: function (target) {\n                if ($(target).is(this.options.inputSelectors)) {\n                    this.userEvents.cancel();\n                    return false;\n                }\n                return true;\n            },\n            start: function (element) {\n                if (element === undefined) {\n                    return this._start;\n                }\n                element.addClass(START + ' ' + SELECTED);\n                this._start = element;\n            },\n            end: function (element) {\n                if (element === undefined) {\n                    return this._start;\n                }\n                element.addClass(END + ' ' + SELECTED);\n                this._end = element;\n            },\n            mid: function (elements) {\n                var tables = this.element.find('table.k-month');\n                elements.addClass(MID);\n                tables.each(function () {\n                    var that = $(this);\n                    var lastCell = that.find(CELLSELECTORVALID + ':last');\n                    var firstCell = that.find(CELLSELECTORVALID + ':first');\n                    if (lastCell.hasClass(MID)) {\n                        lastCell.addClass(SPLITEND);\n                    }\n                    if (firstCell.hasClass(MID)) {\n                        firstCell.addClass(SPLITSTART);\n                    }\n                });\n            },\n            clear: function (clearVariables) {\n                this.element.find(CELLSELECTOR).removeClass(END + ' ' + SELECTED + ' ' + START + ' ' + MID + ' ' + SPLITEND + ' ' + SPLITSTART);\n                if (clearVariables) {\n                    this._start = this._end = null;\n                }\n            },\n            selectFrom: function (start) {\n                var that = this;\n                var items;\n                var startIdx;\n                items = that.element.find(CELLSELECTOR);\n                startIdx = $.inArray($(start)[0], items);\n                that.clear();\n                that.start(start);\n                items = items.filter(function (index) {\n                    return index > startIdx;\n                });\n                that.mid(items);\n            },\n            selectTo: function (end) {\n                var that = this;\n                var items;\n                var endIdx;\n                items = that.element.find(CELLSELECTOR);\n                endIdx = $.inArray($(end)[0], items);\n                that.clear();\n                items = items.filter(function (index) {\n                    return index < endIdx;\n                });\n                that.mid(items);\n                that.end($(end));\n            },\n            range: function (start, end) {\n                var that = this;\n                var items;\n                var startIdx;\n                var endIdx;\n                var temp;\n                if (start === undefined) {\n                    return {\n                        start: that._start,\n                        end: that._end\n                    };\n                }\n                items = that.element.find(CELLSELECTOR);\n                startIdx = $.inArray($(start)[0], items);\n                endIdx = $.inArray($(end)[0], items);\n                if (startIdx > endIdx) {\n                    temp = end;\n                    end = start;\n                    start = temp;\n                    temp = startIdx;\n                    startIdx = endIdx;\n                    endIdx = temp;\n                }\n                that.clear();\n                start.addClass(START + ' ' + SELECTED);\n                that._start = start;\n                items = items.filter(function (index) {\n                    return index > startIdx && index < endIdx;\n                });\n                that.mid(items);\n                that.end($(end));\n            },\n            change: function () {\n                this.trigger(CHANGE);\n            },\n            _tap: function (e) {\n                var target = $(e.target), that = this, items, startIdx, endIdx;\n                that._lastActive = target;\n                if (!that._start) {\n                    that.start(target);\n                    that.trigger(CHANGE);\n                    return;\n                }\n                if (that._start && !that._end) {\n                    items = that.element.find(CELLSELECTOR);\n                    startIdx = $.inArray($(that._start)[0], items);\n                    endIdx = $.inArray($(target)[0], items);\n                    if (+toDateObject($(that._start).find('a')) > +toDateObject($(target).find('a'))) {\n                        that.clear();\n                        that.start(target);\n                        that.trigger(CHANGE);\n                        return;\n                    }\n                    items = items.filter(function (index) {\n                        return index > startIdx && index < endIdx;\n                    });\n                    that.mid(items);\n                    that.end($(target));\n                    that.trigger(CHANGE);\n                    return;\n                }\n                if (that._start && that._end) {\n                    if (target.hasClass(MID)) {\n                        if (!that._toggling) {\n                            that.range(target, that._end);\n                        } else {\n                            that.range(that._start, target);\n                        }\n                        that._toggling = !that._toggling;\n                        that.trigger(CHANGE);\n                        return;\n                    }\n                    that._toggling = false;\n                    that._end = null;\n                    that.clear();\n                    that.start(target);\n                    that.trigger(CHANGE);\n                }\n            }\n        });\n        var MultiViewCalendar = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                var id;\n                var culture;\n                Widget.fn.init.call(that, element, options);\n                element = that.wrapper = that.element;\n                options = that.options;\n                that.options.disableDates = calendar.disabled(that.options.disableDates);\n                culture = kendo.getCulture(options.culture);\n                options.format = kendo._extractFormat(options.format || culture.calendars.standard.patterns.d);\n                that._templates();\n                that._header();\n                that._wrapper();\n                id = element.addClass('k-widget k-calendar k-calendar-range' + (options.weekNumber ? ' k-week-number' : '')).on(KEYDOWN + ns, 'table.k-content', proxy(that._move, that)).on(BLUR + ns, 'table', proxy(that._blur, that)).on(CLICK + ns, CELLSELECTORVALID, function (e) {\n                    var link = e.currentTarget.firstChild;\n                    if (link.href.indexOf('#') != -1) {\n                        e.preventDefault();\n                    }\n                    that._click($(link));\n                }).on(MOUSEENTER + ns, CELLSELECTORVALID, proxy(that._mouseEnter, that)).on(MOUSELEAVE_NS, CELLSELECTORVALID, function () {\n                    $(this).removeClass(HOVER);\n                }).attr(ID);\n                if (id) {\n                    that._cellID = id + '_cell_selected';\n                }\n                that._calendarWidth = that.element.width();\n                that._range = options.range;\n                that._initViews({\n                    viewName: options.start,\n                    value: options.value\n                });\n                that._selectable();\n                that._footer(that.footer);\n                that._selectDates = [];\n                that.value(options.value);\n                if (options.selectable == 'multiple') {\n                    that._selectDates = options.selectDates.length ? options.selectDates : that._selectDates;\n                    that._restoreSelection();\n                }\n                if (options.selectable == 'range') {\n                    that.selectRange(that._range);\n                }\n                kendo.notify(that);\n            },\n            options: {\n                name: 'MultiViewCalendar',\n                value: null,\n                min: new DATE(1900, 0, 1),\n                max: new DATE(2099, 11, 31),\n                dates: [],\n                disableDates: null,\n                culture: '',\n                footer: '',\n                format: '',\n                month: {},\n                range: {\n                    start: null,\n                    end: null\n                },\n                weekNumber: false,\n                views: 2,\n                showViewHeader: false,\n                selectable: 'single',\n                selectDates: [],\n                start: MONTH,\n                depth: MONTH,\n                messages: { weekColumnHeader: '' }\n            },\n            events: [\n                CHANGE,\n                NAVIGATE\n            ],\n            setOptions: function (options) {\n                var that = this;\n                calendar.normalize(options);\n                options.disableDates = calendar.disabled(options.disableDates);\n                Widget.fn.setOptions.call(that, options);\n                that._selectable();\n                that._templates();\n                that._footer(that.footer);\n                for (var i = 0; i < that._views.length; i++) {\n                    that._views[i].off(ns).remove();\n                }\n                that._initViews({\n                    viewName: options.start,\n                    value: options.value\n                });\n                that._range = options.range || {\n                    start: null,\n                    end: null\n                };\n                that._restoreSelection();\n            },\n            destroy: function () {\n                var that = this;\n                that._cell = null;\n                that._currentView = null;\n                that._current = null;\n                if (that._views) {\n                    for (var i = 0; i < that._views.length; i++) {\n                        that._views[i].off(ns).remove();\n                    }\n                }\n                that.element.off(ns);\n                if (that.header) {\n                    that.header.off(ns);\n                    that._title = null;\n                    that.header = null;\n                }\n                if (that.selectable) {\n                    that.selectable.destroy();\n                    that.selectable = null;\n                }\n                if (that.rangeSelectable) {\n                    that.rangeSelectable.destroy();\n                    that.rangeSelectable = null;\n                }\n                if (that._today) {\n                    kendo.destroy(that._today.off(ns));\n                }\n                that._views = null;\n                Widget.fn.destroy.call(that);\n            },\n            current: function () {\n                return this._current;\n            },\n            focus: function () {\n                var table;\n                if (this._cell) {\n                    this._cell.closest('table').trigger('focus');\n                } else if (this._current && this._dateInViews(this._current)) {\n                    this._cell = this._cellByDate(this._current);\n                    this._cell.closest('table').trigger('focus');\n                } else {\n                    table = this.element.find('table').first().trigger('focus');\n                    this._cell = table.find(CELLSELECTORVALID + ':first');\n                    this._current = toDateObject(this._cell.find('a'));\n                }\n                this._cell.addClass(FOCUSED);\n            },\n            min: function (value) {\n                return this._option(MIN, value);\n            },\n            max: function (value) {\n                return this._option('max', value);\n            },\n            view: function () {\n                return this._currentView;\n            },\n            navigateToPast: function () {\n                this._navigate(PREVARROW, -1);\n            },\n            navigateToFuture: function () {\n                this._navigate(NEXTARROW, 1);\n            },\n            navigateUp: function () {\n                var that = this, index = that._index;\n                if (that._title.hasClass(DISABLED)) {\n                    return;\n                }\n                that.navigate(that._current, ++index);\n            },\n            navigateDown: function (value) {\n                var that = this, index = that._index, depth = that.options.depth;\n                if (!value) {\n                    return;\n                }\n                if (index === views[depth]) {\n                    if (!isEqualDate(that._value, that._current) || !isEqualDate(that._value, value)) {\n                        that.value(value);\n                        that.trigger(CHANGE);\n                    }\n                    return;\n                }\n                that.navigate(value, --index);\n            },\n            navigate: function (value, view) {\n                view = isNaN(view) ? calendar.views[calendar.viewsEnum[view]] : calendar.views[view];\n                var that = this;\n                var options = that.options;\n                var min = options.min;\n                var max = options.max;\n                if (!value) {\n                    that._current = value = new DATE(+calendar.restrictValue(value, min, max));\n                } else {\n                    that._current = value;\n                }\n                if (view === undefined) {\n                    view = that._currentView;\n                }\n                that._currentView = view;\n                for (var i = 0; i < that._views.length; i++) {\n                    that._views[i].off(ns).remove();\n                }\n                that._initViews({\n                    viewName: view.name,\n                    value: value\n                });\n                that._restoreSelection();\n            },\n            _updateHeader: function () {\n                var that = this;\n                var view = that._currentView;\n                var title = that._title;\n                var value = that._firstViewValue;\n                var options = that.options;\n                var visibleRange = that._visibleRange();\n                var culture = options.culture;\n                var min = options.min;\n                var max = options.max;\n                var lastDate;\n                var disabled;\n                var prevDisabled;\n                var nextDisabled;\n                if (view.name === DECADE || view.name === CENTURY) {\n                    lastDate = shiftDate(value, view.name, options.views - 1);\n                    if (!isInRange(lastDate, min, max)) {\n                        lastDate = max;\n                    }\n                    title.html(view.first(value).getFullYear() + ' - ' + view.last(lastDate).getFullYear());\n                } else {\n                    title.html(view.title(value, min, max, culture) + ' - ' + view.title(shiftDate(value, view.name, options.views - 1), min, max, culture));\n                }\n                disabled = view.name === CENTURY;\n                title.toggleClass(DISABLED, disabled).attr(ARIA_DISABLED, disabled);\n                prevDisabled = view.compare(visibleRange.start, that.options.min) < 1;\n                nextDisabled = view.compare(visibleRange.end, that.options.max) > -1;\n                if (prevDisabled && nextDisabled) {\n                    if (that._navContainer) {\n                        that._navContainer.remove();\n                        that._navContainer = null;\n                    }\n                } else {\n                    if (!that._navContainer) {\n                        that._navContainer = $('<span class=\"k-calendar-nav\">' + '<a href=\"#\" role=\"button\" class=\"k-button k-button-icon k-prev-view\" ' + ARIA_LABEL + '=\"Previous\"><span class=\"k-icon k-i-arrow-60-left\"></span></a>' + '<a href=\"#\" role=\"button\" class=\"k-button k-button-icon k-next-view\" ' + ARIA_LABEL + '=\"Next\"><span class=\"k-icon k-i-arrow-60-right\"></span></a>' + '</span>').appendTo(that.header);\n                        that[PREVARROW] = that._navContainer.find('.k-prev-view');\n                        that[NEXTARROW] = that._navContainer.find('.k-next-view');\n                    }\n                    that[PREVARROW].toggleClass(DISABLED, prevDisabled).attr(ARIA_DISABLED, prevDisabled);\n                    if (that[PREVARROW].hasClass(DISABLED)) {\n                        that[PREVARROW].removeClass(HOVER);\n                    }\n                    that[NEXTARROW].toggleClass(DISABLED, nextDisabled).attr(ARIA_DISABLED, nextDisabled);\n                    if (that[NEXTARROW].hasClass(DISABLED)) {\n                        that[NEXTARROW].removeClass(HOVER);\n                    }\n                }\n            },\n            _mouseEnter: function (e) {\n                var that = this;\n                var cell = $(e.currentTarget);\n                var range;\n                var items;\n                var startIdx;\n                var endIdx;\n                cell.addClass(HOVER);\n                if (that.rangeSelectable && that._currentView.name === 'month') {\n                    range = that.selectRange();\n                    if (range.start && !range.end) {\n                        if (that._dateInViews(that.selectRange().start)) {\n                            items = that.element.find(that.rangeSelectable.options.filter);\n                            startIdx = $.inArray($(that.rangeSelectable._start)[0], items);\n                            endIdx = $.inArray($(cell)[0], items);\n                            if (startIdx > endIdx) {\n                                return;\n                            }\n                            that.rangeSelectable.range(that.rangeSelectable._start, cell);\n                        } else if (+toDateObject(that.element.find(CELLSELECTOR + ':first').find('a')) > +range.start) {\n                            that.rangeSelectable.selectTo(cell);\n                        }\n                        that.rangeSelectable._end = null;\n                    }\n                }\n            },\n            _move: function (e, preventFocus) {\n                var that = this;\n                var options = that.options;\n                var key = e.keyCode;\n                var index = that._index;\n                var min = options.min;\n                var max = options.max;\n                var focusedCell = that.element.find(DOT + FOCUSED);\n                var table = focusedCell.closest('table');\n                var currentValue = new DATE(+(that._current || toDateObject(focusedCell.find('a'))));\n                var isRtl = kendo.support.isRtl(that.wrapper);\n                var navigate = false;\n                var value, prevent, method, cell, lastActive, cellIndex;\n                if (key == keys.RIGHT && !isRtl || key == keys.LEFT && isRtl) {\n                    value = 1;\n                    prevent = true;\n                } else if (key == keys.LEFT && !isRtl || key == keys.RIGHT && isRtl) {\n                    value = -1;\n                    prevent = true;\n                } else if (key == keys.UP) {\n                    value = index === 0 ? -7 : -4;\n                    prevent = true;\n                } else if (key == keys.DOWN) {\n                    value = index === 0 ? 7 : 4;\n                    prevent = true;\n                } else if (key == keys.SPACEBAR) {\n                    value = 0;\n                    prevent = true;\n                } else if (key == keys.HOME) {\n                    prevent = true;\n                    cell = table.find(CELLSELECTORVALID).eq(0);\n                    if (cell.hasClass(FOCUSED)) {\n                        table = table.prev();\n                        if (table.length) {\n                            that._focusCell(table.find(CELLSELECTORVALID).eq(0));\n                        } else {\n                            navigate = that[PREVARROW] && !that[PREVARROW].hasClass(DISABLED);\n                            that._navigate(PREVARROW, -1, preventFocus);\n                            that._focusCell(that.element.find('table:first ' + CELLSELECTORVALID + ':first'));\n                        }\n                    } else {\n                        that._focusCell(cell);\n                    }\n                } else if (key == keys.END) {\n                    prevent = true;\n                    cell = table.find(CELLSELECTORVALID).last();\n                    if (cell.hasClass(FOCUSED)) {\n                        table = table.next();\n                        if (table.length) {\n                            that._focusCell(table.find(CELLSELECTORVALID).last());\n                        } else {\n                            navigate = that[NEXTARROW] && !that[NEXTARROW].hasClass(DISABLED);\n                            that._navigate(NEXTARROW, 1, preventFocus);\n                            that._focusCell(that.element.find('table:last ' + CELLSELECTORVALID + ':last'));\n                        }\n                    } else {\n                        that._focusCell(cell);\n                    }\n                }\n                if (e.ctrlKey || e.metaKey) {\n                    if (key == keys.RIGHT && !isRtl || key == keys.LEFT && isRtl) {\n                        navigate = that[NEXTARROW] && !that[NEXTARROW].hasClass(DISABLED);\n                        that._navigate(NEXTARROW, 1, preventFocus);\n                        prevent = true;\n                    } else if (key == keys.LEFT && !isRtl || key == keys.RIGHT && isRtl) {\n                        navigate = that[PREVARROW] && !that[PREVARROW].hasClass(DISABLED);\n                        that._navigate(PREVARROW, -1, preventFocus);\n                        prevent = true;\n                    } else if (key == keys.UP) {\n                        navigate = !that._title.hasClass(DISABLED);\n                        that.navigateUp();\n                        that._focusCell(that._cellByDate(that._current), !preventFocus);\n                        prevent = true;\n                    } else if (key == keys.DOWN) {\n                        if (that._currentView.name === 'month') {\n                            that.value(currentValue);\n                        } else {\n                            that.navigateDown(currentValue);\n                            that._focusCell(that._cellByDate(that._current), !preventFocus);\n                            navigate = true;\n                        }\n                        prevent = true;\n                    } else if (key == keys.ENTER || key == keys.SPACEBAR) {\n                        if (options.selectable === 'multiple') {\n                            that._toggleSelection(e);\n                        }\n                    }\n                } else if (e.shiftKey && options.selectable !== 'single') {\n                    if (value !== undefined || method) {\n                        if (!method) {\n                            that._currentView.setDate(currentValue, value);\n                        }\n                        if (that._currentView.name !== 'month') {\n                            return;\n                        }\n                        if (options.disableDates(currentValue)) {\n                            currentValue = that._nextNavigatable(currentValue, value);\n                        }\n                        min = createDate(min.getFullYear(), min.getMonth(), min.getDate());\n                        if (isInRange(currentValue, min, max)) {\n                            if (!that._dateInViews(currentValue)) {\n                                if (value > 0) {\n                                    navigate = that[NEXTARROW] && !that[NEXTARROW].hasClass(DISABLED);\n                                    that._navigate(NEXTARROW, 1, preventFocus);\n                                } else {\n                                    navigate = that[PREVARROW] && !that[PREVARROW].hasClass(DISABLED);\n                                    that._navigate(PREVARROW, -1, preventFocus);\n                                }\n                            }\n                            cell = that._cellByDate(currentValue);\n                            that._current = currentValue;\n                            if (that.selectable) {\n                                that._selectRange(toDateObject((that.selectable._lastActive || focusedCell).find('a')), currentValue);\n                                if (!that.selectable._lastActive) {\n                                    that.selectable._lastActive = focusedCell;\n                                }\n                                that.trigger(CHANGE);\n                                that._focusCell(cell);\n                            }\n                            if (that.rangeSelectable) {\n                                lastActive = toDateObject((that.rangeSelectable._lastActive || focusedCell).find('a'));\n                                if (!that._dateInViews(lastActive)) {\n                                    if (+lastActive > +currentValue) {\n                                        that.rangeSelectable._end = that.rangeSelectable._lastActive;\n                                        that.rangeSelectable.selectFrom(cell);\n                                    } else {\n                                        that.rangeSelectable.selectTo(cell);\n                                    }\n                                } else {\n                                    if (that.rangeSelectable._end && that.rangeSelectable._end.is(DOT + FOCUSED)) {\n                                        that.rangeSelectable._lastActive = that.rangeSelectable._start;\n                                    } else {\n                                        that.rangeSelectable._lastActive = that._cellByDate(lastActive);\n                                    }\n                                    that.rangeSelectable.range(that.rangeSelectable._lastActive, cell);\n                                }\n                                that.rangeSelectable.change();\n                                that._focusCell(cell);\n                            }\n                        }\n                    }\n                } else {\n                    if (key == keys.ENTER || key == keys.SPACEBAR) {\n                        if (that._currentView.name === 'month') {\n                            if (that.selectable) {\n                                that.selectable._lastActive = that._cellByDate(currentValue);\n                            }\n                            that.value(currentValue);\n                            if (that.rangeSelectable) {\n                                that.rangeSelectable.change();\n                            }\n                        } else {\n                            that._click($(that._cell[0].firstChild), preventFocus);\n                        }\n                        prevent = true;\n                    } else if (key == keys.PAGEUP || key == keys.PAGEDOWN) {\n                        prevent = true;\n                        cellIndex = table.find(CELLSELECTORVALID).index(focusedCell);\n                        table = key == keys.PAGEUP ? table.prev() : table.next();\n                        if (!table.length) {\n                            if (key == keys.PAGEUP) {\n                                navigate = that[PREVARROW] && !that[PREVARROW].hasClass(DISABLED);\n                                that.navigateToPast();\n                                table = that.element.find('table:first');\n                            } else {\n                                navigate = that[NEXTARROW] && !that[NEXTARROW].hasClass(DISABLED);\n                                that.navigateToFuture();\n                                table = that.element.find('table:last');\n                            }\n                        }\n                        cell = table.find(CELLSELECTORVALID).eq(cellIndex);\n                        if (cell.length) {\n                            that._focusCell(cell);\n                        } else {\n                            that._focusCell(table.find(CELLSELECTORVALID).last());\n                        }\n                    }\n                    if (value || method) {\n                        if (!method) {\n                            that._currentView.setDate(currentValue, value);\n                        }\n                        min = createDate(min.getFullYear(), min.getMonth(), min.getDate());\n                        if (isInRange(currentValue, min, max)) {\n                            if (that.selectable && options.disableDates(currentValue)) {\n                                currentValue = that._nextNavigatable(currentValue, value);\n                            }\n                            if (!that._dateInViews(currentValue)) {\n                                if (value > 0) {\n                                    navigate = that[NEXTARROW] && !that[NEXTARROW].hasClass(DISABLED);\n                                    that._navigate(NEXTARROW, 1, preventFocus);\n                                } else {\n                                    navigate = that[PREVARROW] && !that[PREVARROW].hasClass(DISABLED);\n                                    that._navigate(NEXTARROW, -1, preventFocus);\n                                }\n                            }\n                            cell = that._cellByDate(currentValue);\n                            that._current = currentValue;\n                            that._focusCell(cell, !preventFocus);\n                        }\n                    }\n                }\n                if (navigate) {\n                    that.trigger(NAVIGATE);\n                }\n                if (prevent) {\n                    e.preventDefault();\n                }\n                return that._current;\n            },\n            _visualizeSelectedDatesInView: function () {\n                var that = this;\n                var selectedDates = {};\n                var cells;\n                $.each(that._selectDates, function (index, value) {\n                    selectedDates[kendo.calendar.views[0].toDateString(value)] = value;\n                });\n                that.selectable.clear();\n                cells = that.element.find('table').find(CELLSELECTOR).filter(function (index, element) {\n                    return selectedDates[$(element.firstChild).attr(kendo.attr(VALUE))];\n                });\n                if (cells.length > 0) {\n                    that.selectable._selectElement(cells, true);\n                }\n            },\n            _nextNavigatable: function (currentValue, value) {\n                var that = this;\n                var disabled = true;\n                var view = that._currentView;\n                var min = that.options.min;\n                var max = that.options.max;\n                var isDisabled = that.options.disableDates;\n                var navigatableDate = new Date(currentValue.getTime());\n                view.setDate(navigatableDate, -value);\n                while (disabled) {\n                    view.setDate(currentValue, value);\n                    if (!isInRange(currentValue, min, max)) {\n                        currentValue = navigatableDate;\n                        break;\n                    }\n                    disabled = isDisabled(currentValue);\n                }\n                return currentValue;\n            },\n            _toggleSelection: function (event) {\n                var that = this;\n                that.selectable._lastActive = $(that._cell[0]);\n                if ($(that._cell[0]).hasClass(SELECTED)) {\n                    that.selectable._unselect($(that._cell[0]));\n                    that.selectable.trigger(CHANGE, { event: event });\n                } else {\n                    that.selectable.value($(that._cell[0]), { event: event });\n                }\n            },\n            _option: function (option, value) {\n                var that = this;\n                var options = that.options;\n                var currentValue = that._value || that._current;\n                var isBigger;\n                if (value === undefined) {\n                    return options[option];\n                }\n                value = kendo.parseDate(value, options.format, options.culture);\n                if (!value) {\n                    return;\n                }\n                options[option] = new DATE(+value);\n                if (option === MIN) {\n                    isBigger = value > currentValue;\n                } else {\n                    isBigger = currentValue > value;\n                }\n                if (isBigger) {\n                    that._value = null;\n                }\n                that.navigate(that._value);\n                that._toggle();\n            },\n            _cellByDate: function (value) {\n                if (value instanceof Date) {\n                    value = this._currentView.toDateString(value);\n                }\n                return this.element.find('table').find('td:not(.' + OTHERMONTH + ')').filter(function () {\n                    return $(this.firstChild).attr(kendo.attr(VALUE)) === value;\n                });\n            },\n            _selectable: function () {\n                var that = this;\n                var selectable = that.options.selectable;\n                if (that.selectable) {\n                    that.selectable.destroy();\n                    that.selectable = null;\n                }\n                if (that.rangeSelectable) {\n                    that.rangeSelectable.destroy();\n                    that.rangeSelectable = null;\n                }\n                if (selectable.toLowerCase() === 'range') {\n                    that.rangeSelectable = new RangeSelectable(that.wrapper, {\n                        filter: 'table.k-month ' + CELLSELECTORVALID,\n                        change: proxy(that._rangeSelection, that)\n                    });\n                } else {\n                    that.selectable = new Selectable(that.wrapper, {\n                        aria: true,\n                        dragToSelect: false,\n                        inputSelectors: 'input,textarea,.k-multiselect-wrap,select,button,.k-button>span,.k-button>img,span.k-icon.k-i-arrow-60-down,span.k-icon.k-i-arrow-60-up',\n                        multiple: Selectable.parseOptions(selectable).multiple,\n                        filter: 'table.k-content ' + CELLSELECTORVALID,\n                        change: proxy(that._selection, that),\n                        relatedTarget: proxy(that._onRelatedTarget, that),\n                        unselect: proxy(that._unselecting, that)\n                    });\n                }\n            },\n            _onRelatedTarget: function (target) {\n                var that = this;\n                if (that.selectable.options.multiple && target.is(CELLSELECTORVALID) && target.length > 1) {\n                    that._focusCell(target.first(), true);\n                }\n            },\n            _getFirstViewDate: function (currentView) {\n                var that = this;\n                var options = that.options;\n                var ranges = [];\n                var start;\n                var end;\n                var current = new Date(+that._current);\n                var i;\n                for (i = 0; i < options.views; i++) {\n                    start = currentView.first(current);\n                    end = currentView.last(current);\n                    if (+end > +options.max) {\n                        if (+start <= +options.max) {\n                            ranges.push({\n                                start: start,\n                                end: new Date(+options.max)\n                            });\n                        }\n                        break;\n                    }\n                    ranges.push({\n                        start: start,\n                        end: end\n                    });\n                    current = new Date(+shiftDate(end, currentView.name, 1));\n                }\n                current = new Date(+that._current);\n                for (i = 0; i < options.views; i++) {\n                    start = currentView.first(current);\n                    end = currentView.last(current);\n                    if (+start < +options.min) {\n                        if (+end >= +options.min) {\n                            ranges.push({\n                                start: new Date(+options.min),\n                                end: end\n                            });\n                        }\n                        break;\n                    }\n                    ranges.push({\n                        start: start,\n                        end: end\n                    });\n                    current = new Date(+shiftDate(start, currentView.name, -1));\n                }\n                start = ranges[0].start;\n                for (i = 0; i < options.views + 1; i++) {\n                    if (!ranges[i]) {\n                        break;\n                    }\n                    if (+start > +ranges[i].start) {\n                        start = ranges[i].start;\n                    }\n                }\n                return new Date(+start);\n            },\n            _canRenderNextView: function (viewDate) {\n                var fullYear = viewDate.getFullYear();\n                var month = viewDate.getMonth();\n                var date = viewDate.getDate();\n                var max = this.options.max;\n                var maxYear = max.getFullYear();\n                var maxMonth = max.getMonth();\n                if (fullYear < maxYear) {\n                    return true;\n                }\n                if (fullYear === maxYear && month < maxMonth) {\n                    return true;\n                }\n                if (fullYear === maxYear && month === maxMonth && date < max.getDate()) {\n                    return true;\n                }\n                if (fullYear === maxYear && month === maxMonth && date === max.getDate()) {\n                    return true;\n                }\n                return false;\n            },\n            _initViews: function (viewOptions) {\n                var that = this;\n                var options = that.options;\n                var index = calendar.viewsEnum[viewOptions.viewName];\n                var currentView = calendar.views[index];\n                var viewDate;\n                that._current = new DATE(+calendar.restrictValue(viewOptions.value, options.min, options.max));\n                that._views = [];\n                that._index = index;\n                viewDate = that._getFirstViewDate(currentView);\n                viewDate.setDate(1);\n                that._firstViewValue = new Date(+viewDate);\n                for (var i = 0; i < options.views; i++) {\n                    viewDate = i ? shiftDate(viewDate, currentView.name, 1) : viewDate;\n                    viewDate.setDate(1);\n                    if (!that._canRenderNextView(viewDate)) {\n                        break;\n                    }\n                    that._table = $(currentView.content($.extend({\n                        min: options.min,\n                        max: options.max,\n                        date: viewDate,\n                        url: options.url,\n                        dates: options.dates,\n                        format: options.format,\n                        culture: options.culture,\n                        disableDates: options.disableDates,\n                        showHeader: options.showViewHeader,\n                        isWeekColumnVisible: options.weekNumber,\n                        otherMonth: options.otherMonth,\n                        messages: options.messages\n                    }, that[currentView.name])));\n                    that._table.appendTo(that.tablesWrapper).addClass('k-' + currentView.name);\n                    that._views.push(that._table);\n                }\n                that._currentView = currentView;\n                that.tablesWrapper.attr('class', 'k-calendar-view k-calendar-' + currentView.name + 'view');\n                that._updateHeader();\n            },\n            _rangeSelection: function (e) {\n                var that = this;\n                var range = e.sender.range();\n                var start;\n                var end;\n                if (range.start) {\n                    start = toDateObject(range.start.find('a'));\n                }\n                if (range.end) {\n                    end = toDateObject(range.end.find('a'));\n                }\n                that._range = {\n                    start: start,\n                    end: end\n                };\n                if (!that._preventChange) {\n                    that.trigger(CHANGE);\n                }\n            },\n            _selection: function (e) {\n                var that = this;\n                var selectElements = e.sender.value();\n                var domEvent = e.event;\n                var currentTarget = $(domEvent && domEvent.currentTarget);\n                var isCell = currentTarget.is('td');\n                var currentValue;\n                if (that.options.selectable === 'single') {\n                    that._validateValue(selectElements[0] ? toDateObject(selectElements.first().find('a')) : e.sender._lastActive ? toDateObject(e.sender._lastActive.find('a')) : that.value());\n                }\n                if (that.options.selectable == 'multiple') {\n                    if (isCell) {\n                        currentValue = toDateObject(currentTarget.find('a'));\n                    }\n                    if (domEvent && domEvent.ctrlKey) {\n                        if (isCell) {\n                            if (currentTarget.hasClass(SELECTED)) {\n                                that._selectDates.push(currentValue);\n                            } else {\n                                that._deselect(currentValue);\n                            }\n                        } else {\n                            that.element.find('table ' + CELLSELECTORVALID).each(function (index, element) {\n                                var value = toDateObject($(element).find('a'));\n                                that._deselect(value);\n                            });\n                            that._addSelectedCellsToArray();\n                        }\n                    } else if (domEvent && domEvent.shiftKey) {\n                        that._selectRange(toDateObject(e.sender._lastActive ? e.sender._lastActive.find('a') : selectElements.first().find('a')), currentValue);\n                    } else if (isCell) {\n                        that._selectDates = [];\n                        that._selectDates.push(currentValue);\n                    } else {\n                        that._selectDates = [];\n                        that._addSelectedCellsToArray();\n                    }\n                }\n                if (!that._preventChange) {\n                    that.trigger(CHANGE);\n                }\n            },\n            _addSelectedCellsToArray: function () {\n                var that = this;\n                that.selectable.value().each(function (index, item) {\n                    var date = toDateObject($(item.firstChild));\n                    if (!that.options.disableDates(date)) {\n                        that._selectDates.push(date);\n                    }\n                });\n            },\n            _deselect: function (date) {\n                var that = this;\n                var currentDateIndex = that._selectDates.map(Number).indexOf(+date);\n                if (currentDateIndex != -1) {\n                    that._selectDates.splice(currentDateIndex, 1);\n                }\n            },\n            _unselecting: function (e) {\n                var that = this;\n                var element = e.element;\n                if (that.options.selectable === 'single' && !mobileOS && element.hasClass(FOCUSED)) {\n                    e.preventDefault();\n                }\n            },\n            _visibleRange: function () {\n                var tables = this.element.find('.k-calendar-view table');\n                var firstDateInView = toDateObject(tables.first().find(CELLSELECTOR + ':first').find('a'));\n                var lastDateInView = toDateObject(tables.last().find(CELLSELECTOR + ':last').find('a'));\n                return {\n                    start: firstDateInView,\n                    end: lastDateInView\n                };\n            },\n            _dateInViews: function (date) {\n                var that = this;\n                var tables = that.element.find('.k-calendar-view table');\n                var firstDateInView = toDateObject(tables.first().find(CELLSELECTOR + ':first').find('a'));\n                var lastDateInView = toDateObject(tables.last().find(CELLSELECTOR + ':last').find('a'));\n                return +date <= +lastDateInView && +date >= +firstDateInView;\n            },\n            _fillRange: function (start, end) {\n                var that = this;\n                var daysDifference;\n                that._selectDates = [];\n                daysDifference = daysBetweenTwoDates(start, end);\n                addDaysToArray(that._selectDates, daysDifference, start, that.options.disableDates);\n            },\n            _selectRange: function (start, end) {\n                var that = this;\n                var current;\n                if (+end < +start) {\n                    current = end;\n                    end = start;\n                    start = current;\n                }\n                that._fillRange(start, end);\n                that._visualizeSelectedDatesInView();\n            },\n            _header: function () {\n                var that = this;\n                var element = that.element;\n                var buttons;\n                var header = element.find('.k-calendar-header');\n                if (!header.length) {\n                    header = $('<div class=\"k-calendar-header\">' + '<a href=\"#\" role=\"button\" class=\"k-button k-title\" aria-live=\"assertive\" aria-atomic=\"true\"></a>' + '<span class=\"k-calendar-nav\">' + '<a href=\"#\" role=\"button\" class=\"k-button k-button-icon k-prev-view\" ' + ARIA_LABEL + '=\"Previous\"><span class=\"k-icon k-i-arrow-60-left\"></span></a>' + '<a href=\"#\" role=\"button\" class=\"k-button k-button-icon k-next-view\" ' + ARIA_LABEL + '=\"Next\"><span class=\"k-icon k-i-arrow-60-right\"></span></a>' + '</span>' + '</div>').prependTo(element);\n                }\n                that.header = header;\n                header.on(MOUSEENTER + ns + ' ' + MOUSELEAVE_NS + ' ' + FOCUS + ns + ' ' + BLUR + ns, '.k-button', mousetoggle).on('click', function () {\n                    return false;\n                }).on(CLICK + ns, '.k-button.k-title', function () {\n                    that.navigateUp();\n                    that._focusCell(that._cellByDate(that._current), true);\n                    that.trigger(NAVIGATE);\n                }).on(CLICK + ns, '.k-button.k-prev-view', function (e) {\n                    e.preventDefault();\n                    that.navigateToPast();\n                    that.trigger(NAVIGATE);\n                }).on(CLICK + ns, '.k-button.k-next-view', function (e) {\n                    e.preventDefault();\n                    that.navigateToFuture();\n                    that.trigger(NAVIGATE);\n                });\n                buttons = header.find('.k-button');\n                that._title = buttons.filter('.k-title');\n                that._navContainer = header.find('.k-calendar-nav');\n                that[PREVARROW] = buttons.filter('.k-prev-view');\n                that[NEXTARROW] = buttons.filter('.k-next-view');\n            },\n            _wrapper: function () {\n                this.tablesWrapper = $('<div class=\"k-calendar-view\" />').insertAfter(this.element[0].firstChild);\n            },\n            _templates: function () {\n                var that = this;\n                var options = that.options;\n                var month = options.month;\n                var content = month.content;\n                var weekNumber = month.weekNumber;\n                var empty = month.empty;\n                that.month = {\n                    content: template('<td#=data.cssClass# role=\"gridcell\"><a tabindex=\"-1\" class=\"k-link#=data.linkClass#\" href=\"#=data.url#\" ' + kendo.attr(VALUE) + '=\"#=data.dateString#\" title=\"#=data.title#\">' + (content || '#=data.value#') + '</a></td>', { useWithBlock: !!content }),\n                    empty: template('<td role=\"gridcell\"' + (empty ? '>' : ' class=\"k-out-of-range\">') + (empty || '<a class=\\'k-link\\'></a>') + '</td>', { useWithBlock: !!empty }),\n                    weekNumber: template('<td class=\"k-alt\">' + (weekNumber || '#= data.weekNumber #') + '</td>', { useWithBlock: !!weekNumber })\n                };\n            },\n            _footer: function () {\n                var that = this;\n                var options = that.options;\n                var template = options.footer !== false ? kendo.template(that.options.footer || '#= kendo.toString(data,\"D\",\"' + options.culture + '\") #', { useWithBlock: false }) : null;\n                var today = getToday();\n                var element = that.element;\n                var footer = element.find('.k-footer');\n                if (!template) {\n                    that._toggle(false);\n                    footer.hide();\n                    return;\n                }\n                if (!footer[0]) {\n                    footer = $('<div class=\"k-footer\"><a href=\"#\" class=\"k-link k-nav-today\"></a></div>').appendTo(element);\n                }\n                that._today = footer.show().find('.k-link').html(template(today)).attr('title', kendo.toString(today, 'D', that.options.culture));\n                that._toggle();\n            },\n            _navigate: function (arrow, modifier, preventFocus) {\n                var that = this;\n                var index = that._index + 1;\n                var currentValue = new DATE(+that._current);\n                var originaValue = new DATE(+that._current);\n                var offset;\n                arrow = that[arrow];\n                offset = that._cellByDate(currentValue).closest('table').index();\n                if (modifier > 0) {\n                    offset = 1 - offset;\n                } else {\n                    offset = offset + 1;\n                }\n                if (!arrow || !arrow.hasClass(DISABLED)) {\n                    if (index > 3) {\n                        currentValue.setFullYear(currentValue.getFullYear() + 100 * (modifier * offset));\n                    } else {\n                        calendar.views[index].setDate(currentValue, modifier * offset);\n                    }\n                    that.navigate(currentValue);\n                    if (that._dateInViews(originaValue)) {\n                        that._focusCell(that._cellByDate(originaValue), !preventFocus);\n                        that._current = originaValue;\n                    } else {\n                        if (index > 3) {\n                            originaValue.setFullYear(originaValue.getFullYear() + 100 * modifier);\n                        } else {\n                            calendar.views[index].setDate(originaValue, modifier);\n                        }\n                        that._focusCell(that._cellByDate(originaValue), !preventFocus);\n                        that._current = originaValue;\n                    }\n                }\n            },\n            _toggle: function (toggle) {\n                var that = this;\n                var options = that.options;\n                var isTodayDisabled = options.selectable !== 'range' && that.options.disableDates(getToday());\n                var link = that._today;\n                if (toggle === undefined) {\n                    toggle = isInRange(getToday(), options.min, options.max);\n                }\n                if (link) {\n                    link.off(CLICK + ns);\n                    if (toggle && !isTodayDisabled) {\n                        link.addClass(TODAY).removeClass(DISABLED).on(CLICK + ns, proxy(that._todayClick, that));\n                    } else {\n                        link.removeClass(TODAY).addClass(DISABLED).on(CLICK + ns, function prevent(e) {\n                            e.preventDefault();\n                        });\n                    }\n                }\n            },\n            _click: function (link, preventFocus) {\n                var that = this;\n                var options = that.options;\n                var currentValue = new Date(+that._current);\n                var value = toDateObject(link);\n                kendo.date.adjustDST(value, 0);\n                that._currentView.setDate(currentValue, value);\n                that._current = value;\n                if (that._currentView.name !== options.depth) {\n                    that.navigateDown(calendar.restrictValue(currentValue, options.min, options.max));\n                    that._focusCell(that._cellByDate(that._current), !preventFocus);\n                    that.trigger(NAVIGATE);\n                } else {\n                    that._focusCell(link.closest('td'), !preventFocus);\n                }\n            },\n            _blur: function () {\n                var that = this;\n                if (that._cell) {\n                    that._cell.removeClass(FOCUSED);\n                }\n            },\n            _focus: function (e) {\n                var that = this;\n                var table = $(e.currentTarget);\n                var cell = that._cell;\n                if (!cell || !$.contains(table[0], cell[0])) {\n                    cell = table.find(CELLSELECTORVALID + ':first');\n                }\n                that._focusCell(cell);\n            },\n            _focusCell: function (cell, focusTable) {\n                var that = this;\n                var cellId = that._cellID;\n                var table = cell.closest('table');\n                if (that._cell && that._cell.length) {\n                    that._cell[0].removeAttribute(ARIA_SELECTED);\n                    that._cell[0].removeAttribute(ARIA_LABEL);\n                    that._cell.removeClass(FOCUSED);\n                    that._cell[0].removeAttribute(ID);\n                    that._cell.closest('table')[0].removeAttribute('aria-activedescendant');\n                }\n                that._cell = cell;\n                if (focusTable) {\n                    table.trigger('focus');\n                }\n                if (cellId) {\n                    cell.attr(ID, cellId);\n                    table.attr('aria-activedescendant', cellId);\n                }\n                cell.attr(ARIA_SELECTED, true).addClass(FOCUSED);\n                if (cell.length && that._currentView.name == 'month') {\n                    that._current = toDateObject(cell.find('a'));\n                }\n            },\n            _todayClick: function (e) {\n                var that = this;\n                var disabled = that.options.disableDates;\n                var today = getToday();\n                var navigate = false;\n                e.preventDefault();\n                if (disabled(today)) {\n                    return;\n                }\n                that._value = today;\n                if (that.options.selectable === 'multiple') {\n                    that._selectDates = [today];\n                }\n                if (that.options.selectable === 'range') {\n                    that.rangeSelectable.clear(true);\n                    that._range = {\n                        start: today,\n                        end: null\n                    };\n                }\n                if (that._currentView.name != 'month' || !that._dateInViews(today)) {\n                    navigate = true;\n                }\n                that.navigate(today, that.options.depth);\n                if (that.options.selectable === 'single') {\n                    that.selectable._lastActive = null;\n                }\n                if (navigate) {\n                    that.trigger(NAVIGATE);\n                }\n                that.trigger(CHANGE);\n            },\n            _validateValue: function (value) {\n                var that = this;\n                var options = that.options;\n                var min = options.min;\n                var max = options.max;\n                value = kendo.parseDate(value, options.format, options.culture);\n                if (value !== null) {\n                    value = new DATE(+value);\n                    if (!isInRange(value, min, max)) {\n                        value = null;\n                    }\n                }\n                if (value === null || !that.options.disableDates(new Date(+value))) {\n                    that._value = value;\n                } else if (that._value === undefined) {\n                    that._value = null;\n                }\n                return that._value;\n            },\n            clearSelection: function () {\n                var that = this;\n                if (that.selectable) {\n                    that.element.find(DOT + SELECTED).removeClass(SELECTED);\n                }\n                if (that.rangeSelectable) {\n                    that.rangeSelectable.clear(true);\n                }\n            },\n            _restoreSelection: function () {\n                var that = this;\n                var range;\n                var selectable = that.options.selectable;\n                if (that._currentView.name !== that.options.depth) {\n                    return;\n                }\n                that._preventChange = true;\n                if (selectable === 'range') {\n                    range = that.selectRange();\n                    if (!range || !range.start) {\n                        that._preventChange = false;\n                        return;\n                    }\n                    that.selectRange(range);\n                }\n                if (selectable === 'single' && that.value()) {\n                    that.selectable.value(that._cellByDate(that.value()));\n                }\n                if (selectable === 'multiple') {\n                    that._visualizeSelectedDatesInView();\n                }\n                that._preventChange = false;\n            },\n            value: function (value) {\n                var that = this;\n                var cell;\n                if (value === undefined) {\n                    return that._value;\n                }\n                value = that._validateValue(value);\n                that.clearSelection();\n                if (value && !that._dateInViews(value)) {\n                    that.navigate(value);\n                }\n                if (value !== null && that._currentView.name === MONTH) {\n                    cell = that._cellByDate(value);\n                    if (that.selectable) {\n                        that.selectable.value(cell);\n                    }\n                    if (that.rangeSelectable) {\n                        that.rangeSelectable.start(cell);\n                        that.rangeSelectable._lastActive = cell;\n                    }\n                }\n            },\n            selectDates: function (dates) {\n                var that = this;\n                var validSelectedDates;\n                var datesUnique;\n                if (dates === undefined) {\n                    return that._selectDates;\n                }\n                datesUnique = dates.map(function (date) {\n                    return date.getTime();\n                }).filter(function (date, position, array) {\n                    return array.indexOf(date) === position;\n                }).map(function (time) {\n                    return new Date(time);\n                });\n                validSelectedDates = $.grep(datesUnique, function (value) {\n                    if (value) {\n                        return +that._validateValue(new Date(value.setHours(0, 0, 0, 0))) === +value;\n                    }\n                });\n                that._selectDates = validSelectedDates.length > 0 ? validSelectedDates : datesUnique.length === 0 ? datesUnique : that._selectDates;\n                that._visualizeSelectedDatesInView();\n            },\n            selectRange: function (range) {\n                var that = this;\n                var startInRange;\n                var endInRange;\n                var visibleRange;\n                if (range === undefined) {\n                    return that._range;\n                }\n                that._range = range;\n                if (!range.start) {\n                    return;\n                }\n                visibleRange = that._visibleRange();\n                startInRange = that._dateInViews(range.start);\n                endInRange = range.end && that._dateInViews(range.end);\n                if (!startInRange && endInRange) {\n                    that.rangeSelectable.selectTo(that._cellByDate(range.end));\n                }\n                if (startInRange && endInRange) {\n                    that.rangeSelectable.range(that._cellByDate(range.start), that._cellByDate(range.end));\n                }\n                if (range.end && startInRange && !endInRange) {\n                    that.rangeSelectable.selectFrom(that._cellByDate(range.start));\n                }\n                if (!range.end && startInRange) {\n                    that.rangeSelectable.start(that._cellByDate(range.start));\n                }\n                if (+visibleRange.start > +range.start && +visibleRange.end < +range.end) {\n                    that.rangeSelectable.mid(that.element.find(CELLSELECTORVALID));\n                }\n            }\n        });\n        kendo.ui.plugin(MultiViewCalendar);\n        function mousetoggle(e) {\n            var disabled = $(this).hasClass('k-state-disabled');\n            if (!disabled) {\n                $(this).toggleClass(HOVER, MOUSEENTER.indexOf(e.type) > -1 || e.type == FOCUS);\n            }\n        }\n        function addDaysToArray(array, numberOfDays, fromDate, disableDates) {\n            for (var i = 0; i <= numberOfDays; i++) {\n                var nextDay = new Date(fromDate.getTime());\n                nextDay = new Date(nextDay.setDate(nextDay.getDate() + i));\n                if (!disableDates(nextDay)) {\n                    array.push(nextDay);\n                }\n            }\n        }\n        function daysBetweenTwoDates(startDate, endDate) {\n            if (+endDate < +startDate) {\n                var temp = +startDate;\n                calendar.views[0].setDate(startDate, endDate);\n                calendar.views[0].setDate(endDate, new Date(temp));\n            }\n            var fromDateUTC = Date.UTC(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n            var endDateUTC = Date.UTC(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n            return Math.ceil((+endDateUTC - +fromDateUTC) / kendo.date.MS_PER_DAY);\n        }\n        function shiftDate(value, dimension, numberOfViews) {\n            var current;\n            if (dimension === 'month') {\n                current = new DATE(value.getFullYear(), value.getMonth() + numberOfViews, value.getDate());\n                current.setFullYear(value.getFullYear());\n                if (Math.abs(current.getMonth() - value.getMonth()) > numberOfViews || numberOfViews > 10) {\n                    current.setMonth(value.getMonth() + numberOfViews);\n                    current = calendar.views[0].last(current);\n                }\n                return current;\n            } else if (dimension === 'year') {\n                current = new DATE(1, value.getMonth(), value.getDate());\n                current.setFullYear(value.getFullYear() + numberOfViews);\n                if (Math.abs(current.getFullYear() - value.getFullYear()) > numberOfViews) {\n                    current = new DATE(1, value.getMonth(), 1);\n                    current.setFullYear(value.getFullYear() + numberOfViews);\n                    current = calendar.views[1].last(current);\n                }\n                return current;\n            } else if (dimension === 'decade') {\n                current = new DATE(1, value.getMonth(), value.getDate());\n                current.setFullYear(value.getFullYear() + 10 * numberOfViews);\n                if (Math.abs(current.getFullYear() - value.getFullYear()) > 10 * numberOfViews) {\n                    current = new DATE(1, value.getMonth(), 1);\n                    current.setFullYear(value.getFullYear() + 10 * numberOfViews);\n                    current = calendar.views[2].last(current);\n                }\n                return current;\n            } else if (dimension === 'century') {\n                current = new DATE(1, value.getMonth(), value.getDate());\n                current.setFullYear(value.getFullYear() + 100 * numberOfViews);\n                if (Math.abs(current.getFullYear() - value.getFullYear()) > 100 * numberOfViews) {\n                    current = new DATE(1, value.getMonth(), 1);\n                    current.setFullYear(value.getFullYear() + 100 * numberOfViews);\n                    current = calendar.views[3].last(current);\n                }\n                return current;\n            }\n        }\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}