/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("util/text-metrics.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return(t+"").replace(s,u)}function i(t){var e,i=[];for(e in t)i.push(e+t[e]);return i.sort().join("")}function n(t){var e,i=2166136261;for(e=0;e<t.length;++e)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=t.charCodeAt(e);return i>>>0}function o(){return{width:0,height:0,baseline:0}}function a(t,e,i){return c.current.measure(t,e,i)}var r,s,u,h,l,c;window.kendo.util=window.kendo.util||{},r=kendo.Class.extend({init:function(t){this._size=t,this._length=0,this._map={}},put:function(t,e){var i=this._map,n={key:t,value:e};i[t]=n,this._head?(this._tail.newer=n,n.older=this._tail,this._tail=n):this._head=this._tail=n,this._length>=this._size?(i[this._head.key]=null,this._head=this._head.newer,this._head.older=null):this._length++},get:function(t){var e=this._map[t];if(e)return e===this._head&&e!==this._tail&&(this._head=e.newer,this._head.older=null),e!==this._tail&&(e.older&&(e.older.newer=e.newer,e.newer.older=e.older),e.older=this._tail,e.newer=null,this._tail.newer=e,this._tail=e),e.value}}),s=/\r?\n|\r|\t/g,u=" ",h={baselineMarkerSize:1},"undefined"!=typeof document&&(l=document.createElement("div"),l.style.cssText="position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: pre!important;"),c=kendo.Class.extend({init:function(e){this._cache=new r(1e3),this.options=t.extend({},h,e)},measure:function(t,a,r){var s,u,h,c,d,p,f,m,_;if(void 0===r&&(r={}),!t)return o();if(s=i(a),u=n(t+s),h=this._cache.get(u))return h;c=o(),d=r.box||l,p=this._baselineMarker().cloneNode(!1);for(f in a)m=a[f],void 0!==m&&(d.style[f]=m);return _=r.normalizeText!==!1?e(t):t+"",d.textContent=_,d.appendChild(p),document.body.appendChild(d),_.length&&(c.width=d.offsetWidth-this.options.baselineMarkerSize,c.height=d.offsetHeight,c.baseline=p.offsetTop+this.options.baselineMarkerSize),c.width>0&&c.height>0&&this._cache.put(u,c),d.parentNode.removeChild(d),c},_baselineMarker:function(){var t=document.createElement("div");return t.style.cssText="display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;",t}}),c.current=new c,kendo.deepExtend(kendo.util,{LRUCache:r,TextMetrics:c,measureText:a,objectKey:i,hashKey:n,normalizeText:e})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("util/main.min",["kendo.core.min"],t)}(function(){return function(){function t(t){return t*t}function e(t){return"string"!=typeof t&&(t+="px"),t}function i(t){var e,i,n=[];if(t)for(e=h.toHyphens(t).split("-"),i=0;i<e.length;i++)n.push("k-pos-"+e[i]);return n.join(" ")}function n(t){for(var e={1:"i",10:"x",100:"c",2:"ii",20:"xx",200:"cc",3:"iii",30:"xxx",300:"ccc",4:"iv",40:"xl",400:"cd",5:"v",50:"l",500:"d",6:"vi",60:"lx",600:"dc",7:"vii",70:"lxx",700:"dcc",8:"viii",80:"lxxx",800:"dccc",9:"ix",90:"xc",900:"cm",1e3:"m"},i=[1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],n="";t>0;)t<i[0]?i.shift():(n+=e[i[0]],t-=i[0]);return n}function o(t){var e,i,n,o,a;for(t=t.toLowerCase(),e={i:1,v:5,x:10,l:50,c:100,d:500,m:1e3},i=0,n=0,o=0;o<t.length;++o){if(a=e[t.charAt(o)],!a)return null;i+=a,a>n&&(i-=2*n),n=a}return i}function a(t){var e=Object.create(null);return function(){var i,n="";for(i=arguments.length;--i>=0;)n+=":"+arguments[i];return n in e?e[n]:e[n]=t.apply(this,arguments)}}function r(t){return u.test(t)}function s(t,e){function i(t){this.value=t}try{return t.call(e,function(t){throw new i(t)})}catch(n){if(n instanceof i)return n.value;throw n}}var u,h=window.kendo,l=h.deepExtend,c=Date.now;c||(c=function(){return(new Date).getTime()}),l(h,{util:{now:c,renderPos:i,renderSize:e,sqr:t,romanToArabic:o,arabicToRoman:n,memoize:a,isUnicodeLetter:r,withExit:s}}),u=RegExp("[\\u0041-\\u005A\\u0061-\\u007A\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0-\\u08B2\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19C1-\\u19C7\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]")}(),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/location.min",["kendo.drawing.min","util/main.min"],t)}(function(){!function(t,e){var i,n=Math,o=n.abs,a=n.atan,r=n.atan2,s=n.cos,u=n.max,h=n.min,l=n.sin,c=n.tan,d=window.kendo,p=d.Class,f=d.dataviz,m=d.deepExtend,_=d.drawing.util,v=_.defined,g=_.deg,y=_.rad,A=_.round,w=_.valueOrDefault,C=d.util.sqr,E=p.extend({init:function(t,e){1===arguments.length?(this.lat=t[0],this.lng=t[1]):(this.lat=t,this.lng=e)},DISTANCE_ITERATIONS:100,DISTANCE_CONVERGENCE:1e-12,DISTANCE_PRECISION:2,FORMAT:"{0:N6},{1:N6}",toArray:function(){return[this.lat,this.lng]},equals:function(t){return t&&t.lat===this.lat&&t.lng===this.lng},clone:function(){return new E(this.lat,this.lng)},round:function(t){return this.lng=A(this.lng,t),this.lat=A(this.lat,t),this},wrap:function(){return this.lng=this.lng%180,this.lat=this.lat%90,this},distanceTo:function(t,e){return this.greatCircleTo(t,e).distance},destination:function(t,e,i){var o,a,u,h,c;return e=y(e),i=i||f.map.datums.WGS84,o=y(this.lat),a=y(this.lng),u=t/d.dataviz.map.datums.WGS84.a,h=n.asin(l(o)*s(u)+s(o)*l(u)*s(e)),c=a+r(l(e)*l(u)*s(o),s(u)-l(o)*l(h)),new E(g(h),g(c))},greatCircleTo:function(t,e){var i,u,h,d,p,m,_,v,w,b,x,k,F,z,D,B,T,S,L,M,O,P,I,N,j,R,V,Z,G;if(t=E.create(t),e=e||f.map.datums.WGS84,!t||this.clone().round(8).equals(t.clone().round(8)))return{distance:0,azimuthFrom:0,azimuthTo:0};for(i=e.a,u=e.b,h=e.f,d=y(t.lng-this.lng),p=a((1-h)*c(y(this.lat))),m=l(p),_=s(p),v=a((1-h)*c(y(t.lat))),w=l(v),b=s(v),x=d,F=this.DISTANCE_ITERATIONS,z=!1;!z&&F-- >0;)D=l(x),B=s(x),T=n.sqrt(C(b*D)+C(_*w-m*b*B)),L=m*w+_*b*B,O=r(T,L),P=_*b*D/T,S=1-C(P),M=0,0!==S&&(M=L-2*m*w/S),k=x,I=h/16*S*(4+h*(4-3*S)),x=d+(1-I)*h*P*(O+I*T*(M+I*L*(-1+2*C(M)))),z=o(x-k)<=this.DISTANCE_CONVERGENCE;return N=S*(C(i)-C(u))/C(u),j=1+N/16384*(4096+N*(-768+N*(320-175*N))),R=N/1024*(256+N*(-128+N*(74-47*N))),V=R*T*(M+R/4*(L*(-1+2*C(M))-R/6*M*(-3+4*C(T))*(-3+4*C(M)))),Z=r(b*D,_*w-m*b*B),G=r(_*D,-m*b+_*w*B),{distance:A(u*j*(O-V),this.DISTANCE_PRECISION),azimuthFrom:g(Z),azimuthTo:g(G)}}});E.fn.toString=function(){return d.format(this.FORMAT,this.lat,this.lng)},E.fromLngLat=function(t){return new E(t[1],t[0])},E.fromLatLng=function(t){return new E(t[0],t[1])},E.create=function(t,e){if(v(t))return t instanceof E?t.clone():1===arguments.length&&2===t.length?E.fromLatLng(t):new E(t,e)},i=p.extend({init:function(t,e){t=E.create(t),e=E.create(e),t.lng+180>e.lng+180&&t.lat+90<e.lat+90?(this.se=t,this.nw=e):(this.se=e,this.nw=t)},contains:function(t){var e=this.nw,i=this.se,n=w(t.lng,t[1]),o=w(t.lat,t[0]);return t&&n+180>=e.lng+180&&n+180<=i.lng+180&&o+90>=i.lat+90&&o+90<=e.lat+90},center:function(){var t=this.nw,e=this.se,i=t.lng+(e.lng-t.lng)/2,n=t.lat+(e.lat-t.lat)/2;return new E(n,i)},containsAny:function(t){var e,i=!1;for(e=0;e<t.length;e++)i=i||this.contains(t[e]);return i},include:function(t){var e=this.nw,i=this.se,n=w(t.lng,t[1]),o=w(t.lat,t[0]);e.lng=h(e.lng,n),e.lat=u(e.lat,o),i.lng=u(i.lng,n),i.lat=h(i.lat,o)},includeAll:function(t){for(var e=0;e<t.length;e++)this.include(t[e])},edges:function(){var t=this.nw,e=this.se;return{nw:this.nw,ne:new E(t.lat,e.lng),se:this.se,sw:new E(e.lat,t.lng)}},toArray:function(){var t=this.nw,e=this.se;return[t,new E(t.lat,e.lng),e,new E(e.lat,t.lng)]},overlaps:function(t){return this.containsAny(t.toArray())||t.containsAny(this.toArray())}}),i.World=new i([90,-180],[-90,180]),i.create=function(t,n){return t instanceof i?t:t&&n?new i(t,n):t&&4===t.length&&!n?new i([t[0],t[1]],[t[2],t[3]]):e},m(f,{map:{Extent:i,Location:E}})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/attribution.min",["kendo.drawing.min"],t)}(function(){!function(){var t=window.kendo,e=t.ui.Widget,i=t.template,n=t.drawing.util,o=n.valueOrDefault,a=n.defined,r=e.extend({init:function(t,i){e.fn.init.call(this,t,i),this._initOptions(i),this.items=[],this.element.addClass("k-widget k-attribution")},options:{name:"Attribution",separator:"&nbsp;|&nbsp;",itemTemplate:"#= text #"},filter:function(t,e){this._extent=t,this._zoom=e,this._render()},add:function(t){a(t)&&("string"==typeof t&&(t={text:t}),this.items.push(t),this._render())},remove:function(t){var e,i,n=[];for(e=0;e<this.items.length;e++)i=this.items[e],i.text!==t&&n.push(i);this.items=n,this._render()},clear:function(){this.items=[],this.element.empty()},_render:function(){var t,e,n,o=[],a=i(this.options.itemTemplate);for(t=0;t<this.items.length;t++)e=this.items[t],n=this._itemText(e),""!==n&&o.push(a({text:n}));o.length>0?this.element.empty().append(o.join(this.options.separator)).show():this.element.hide()},_itemText:function(t){var e="",i=this._inZoomLevel(t.minZoom,t.maxZoom),n=this._inArea(t.extent);return i&&n&&(e+=t.text),e},_inZoomLevel:function(t,e){var i=!0;return t=o(t,-Number.MAX_VALUE),e=o(e,Number.MAX_VALUE),i=this._zoom>t&&this._zoom<e},_inArea:function(t){var e=!0;return t&&(e=t.contains(this._extent)),e}});t.dataviz.ui.plugin(r)}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/navigator.min",["kendo.core.min"],t)}(function(){!function(t){function e(t){return i.format('<button class="k-button k-navigator-{0}" aria-label="move {0}"><span class="k-icon k-i-arrow-60-{0}"/></button>',t)}var i=window.kendo,n=i.ui.Widget,o=i.keys,a=t.proxy,r=".kendoNavigator",s=e("up")+e("right")+e("down")+e("left"),u=n.extend({init:function(t,e){n.fn.init.call(this,t,e),this._initOptions(e),this.element.addClass("k-widget k-header k-shadow k-navigator").append(s).on("click"+r,".k-button",a(this,"_click"));var o=this.element.parent().closest("["+i.attr("role")+"]");this._keyroot=o.length>0?o:this.element,this._tabindex(this._keyroot),this._keydown=a(this._keydown,this),this._keyroot.on("keydown",this._keydown)},options:{name:"Navigator",panStep:1},events:["pan"],dispose:function(){this._keyroot.off("keydown",this._keydown)},_pan:function(t,e){var i=this.options.panStep;this.trigger("pan",{x:t*i,y:e*i})},_click:function(e){var i=0,n=0,o=t(e.currentTarget);o.is(".k-navigator-up")?n=1:o.is(".k-navigator-down")?n=-1:o.is(".k-navigator-right")?i=1:o.is(".k-navigator-left")&&(i=-1),this._pan(i,n),e.preventDefault()},_keydown:function(t){switch(t.which){case o.UP:this._pan(0,1),t.preventDefault();break;case o.DOWN:this._pan(0,-1),t.preventDefault();break;case o.RIGHT:this._pan(1,0),t.preventDefault();break;case o.LEFT:this._pan(-1,0),t.preventDefault()}}});i.dataviz.ui.plugin(u)}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/zoom.min",["kendo.core.min"],t)}(function(){!function(t){function e(t,e){return i.format('<button class="k-button k-zoom-{0}" title="zoom-{0}" aria-label="zoom-{0}"><span class="k-icon {1}"></span></button>',t,e)}var i=window.kendo,n=i.ui.Widget,o=i.keys,a=t.proxy,r=".kendoZoomControl",s=e("in","k-i-plus")+e("out","k-i-minus"),u=187,h=189,l=61,c=173,d=n.extend({init:function(t,e){n.fn.init.call(this,t,e),this._initOptions(e),this.element.addClass("k-widget k-zoom-control k-button-wrap k-buttons-horizontal k-button-group k-group-horizontal").append(s).on("click"+r,".k-button",a(this,"_click"));var o=this.element.parent().closest("["+i.attr("role")+"]");this._keyroot=o.length>0?o:this.element,this._tabindex(this._keyroot),this._keydown=a(this._keydown,this),this._keyroot.on("keydown",this._keydown)},options:{name:"ZoomControl",zoomStep:1},events:["change"],_change:function(t){var e=this.options.zoomStep;this.trigger("change",{delta:t*e})},_click:function(e){var i=t(e.currentTarget),n=1;i.is(".k-zoom-out")&&(n=-1),this._change(n),e.preventDefault()},_keydown:function(t){switch(t.which){case o.NUMPAD_PLUS:case u:case l:this._change(1);break;case o.NUMPAD_MINUS:case h:case c:this._change(-1)}}});i.dataviz.ui.plugin(d)}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/crs.min",["dataviz/map/location.min","kendo.drawing.min"],t)}(function(){!function(t,e){var i=Math,n=i.atan,o=i.exp,a=i.pow,r=i.sin,s=i.log,u=i.tan,h=window.kendo,l=h.Class,c=h.dataviz,d=h.deepExtend,p=h.geometry,f=p.Point,m=c.map,_=m.Location,v=h.drawing.util,g=v.rad,y=v.deg,A=v.limitValue,w=i.PI,C=w/2,E=w/4,b=w/180,x={a:6378137,b:6356752.314245179,f:.0033528106647474805,e:.08181919084262149},k=l.extend({init:function(t){this._initOptions(t)},MAX_LNG:180,MAX_LAT:85.0840590501,INVERSE_ITERATIONS:15,INVERSE_CONVERGENCE:1e-12,options:{centralMeridian:0,datum:x},forward:function(t,e){var i=this,n=i.options,o=n.datum,a=o.a,r=n.centralMeridian,s=A(t.lat,-i.MAX_LAT,i.MAX_LAT),u=e?A(t.lng,-i.MAX_LNG,i.MAX_LNG):t.lng,h=g(u-r)*a,l=i._projectLat(s);return new f(h,l)},_projectLat:function(t){var e=this.options.datum,i=e.e,n=e.a,o=g(t),h=u(E+o/2),l=i*r(o),c=a((1-l)/(1+l),i/2);return n*s(h*c)},inverse:function(t,e){var i=this,n=i.options,o=n.datum,a=o.a,r=n.centralMeridian,s=t.x/(b*a)+r,u=A(i._inverseY(t.y),-i.MAX_LAT,i.MAX_LAT);return e&&(s=A(s,-i.MAX_LNG,i.MAX_LNG)),new _(u,s)},_inverseY:function(t){var e,s,u,h,l=this,c=l.options.datum,d=c.a,p=c.e,f=p/2,m=o(-t/d),_=C-2*n(m);for(e=0;e<=l.INVERSE_ITERATIONS&&(s=p*r(_),u=a((1-s)/(1+s),f),h=C-2*n(m*u)-_,_+=h,!(i.abs(h)<=l.INVERSE_CONVERGENCE));e++);return y(_)}}),F=k.extend({MAX_LAT:85.0511287798,_projectLat:function(t){var e=this.options.datum.a,i=g(t),n=u(E+i/2);return e*s(n)},_inverseY:function(t){var e=this.options.datum.a,i=o(-t/e);return y(C-2*n(i))}}),z=l.extend({forward:function(t){return new f(t.lng,t.lat)},inverse:function(t){return new _(t.y,t.x)}}),D=l.extend({init:function(){var t=this,e=t._proj=new F,i=this.c=2*w*e.options.datum.a;this._tm=p.transform().translate(.5,.5).scale(1/i,-1/i),this._itm=p.transform().scale(i,-i).translate(-.5,-.5)},toPoint:function(t,e,i){var n=this._proj.forward(t,i);return n.transform(this._tm).scale(e||1)},toLocation:function(t,e,i){return t=t.clone().scale(1/(e||1)).transform(this._itm),this._proj.inverse(t,i)}}),B=l.extend({init:function(){this._proj=new k},toPoint:function(t){return this._proj.forward(t)},toLocation:function(t){return this._proj.inverse(t)}}),T=l.extend({init:function(){this._proj=new z},toPoint:function(t){return this._proj.forward(t)},toLocation:function(t){return this._proj.inverse(t)}});d(c,{map:{crs:{EPSG3395:B,EPSG3857:D,EPSG4326:T},datums:{WGS84:x},projections:{Equirectangular:z,Mercator:k,SphericalMercator:F}}})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/layers/base.min",["kendo.core.min","dataviz/map/location.min"],t)}(function(){!function(t,e){var i=t.proxy,n=window.kendo,o=n.Class,a=n.dataviz,r=n.deepExtend,s=a.map.Extent,u=n.drawing.util,h=u.defined,l=o.extend({init:function(e,n){this._initOptions(n),this.map=e,this.element=t("<div class='k-layer'></div>").css({zIndex:this.options.zIndex,opacity:this.options.opacity}).appendTo(e.scrollElement),this._beforeReset=i(this._beforeReset,this),this._reset=i(this._reset,this),this._resize=i(this._resize,this),this._panEnd=i(this._panEnd,this),this._activate(),this._updateAttribution()},destroy:function(){this._deactivate()},show:function(){this.reset(),this._activate(),this._applyExtent(!0)},hide:function(){this._deactivate(),this._setVisibility(!1)},reset:function(){this._beforeReset(),this._reset()},_reset:function(){this._applyExtent()},_beforeReset:t.noop,_resize:t.noop,_panEnd:function(){this._applyExtent()},_applyExtent:function(){var t=this.options,e=this.map.zoom(),i=!h(t.minZoom)||e>=t.minZoom,n=!h(t.maxZoom)||e<=t.maxZoom,o=s.create(t.extent),a=!o||o.overlaps(this.map.extent());this._setVisibility(i&&n&&a)},_setVisibility:function(t){this.element.css("display",t?"":"none")},_activate:function(){var t=this.map;t.bind("beforeReset",this._beforeReset),t.bind("reset",this._reset),t.bind("resize",this._resize),t.bind("panEnd",this._panEnd)},_deactivate:function(){var t=this.map;t.unbind("beforeReset",this._beforeReset),t.unbind("reset",this._reset),t.unbind("resize",this._resize),t.unbind("panEnd",this._panEnd)},_updateAttribution:function(){var t=this.map.attribution;t&&t.add(this.options.attribution)}});r(a,{map:{layers:{Layer:l}}})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/layers/shape.min",["dataviz/map/layers/base.min","dataviz/map/location.min"],t)}(function(){!function(t,e){var i=t.proxy,n=window.kendo,o=n.Class,a=n.data.DataSource,r=n.dataviz,s=n.deepExtend,u=n.geometry,h=n.drawing,l=h.Group,c=h.util.last,d=h.util.defined,p=r.map,f=p.Location,m=p.layers.Layer,_=m.extend({init:function(t,e){this._pan=i(this._pan,this),m.fn.init.call(this,t,e),this.surface=h.Surface.create(this.element,{width:t.scrollElement.width(),height:t.scrollElement.height()}),this._initRoot(),this.movable=new n.ui.Movable(this.surface.element),this._markers=[],this._click=this._handler("shapeClick"),this.surface.bind("click",this._click),this._mouseenter=this._handler("shapeMouseEnter"),this.surface.bind("mouseenter",this._mouseenter),this._mouseleave=this._handler("shapeMouseLeave"),this.surface.bind("mouseleave",this._mouseleave),this._initDataSource()},options:{autoBind:!0},destroy:function(){m.fn.destroy.call(this),this.surface.destroy(),this.dataSource.unbind("change",this._dataChange)},setDataSource:function(t){this.dataSource&&this.dataSource.unbind("change",this._dataChange),this.dataSource=n.data.DataSource.create(t),this.dataSource.bind("change",this._dataChange),this.options.autoBind&&this.dataSource.fetch()},_reset:function(){m.fn._reset.call(this),this._translateSurface(),this._data&&this._load(this._data)},_initRoot:function(){this._root=new l,this.surface.draw(this._root)},_beforeReset:function(){this.surface.clear(),this._initRoot()},_resize:function(){this.surface.size(this.map.size())},_initDataSource:function(){var t=this.options.dataSource;this._dataChange=i(this._dataChange,this),this.dataSource=a.create(t).bind("change",this._dataChange),t&&this.options.autoBind&&this.dataSource.fetch()},_dataChange:function(t){this._data=t.sender.view(),this._load(this._data)},_load:function(t){var e,i,n;for(this._clearMarkers(),this._loader||(this._loader=new v(this.map,this.options.style,this)),e=new l,i=0;i<t.length;i++)n=this._loader.parse(t[i]),n&&e.append(n);this._root.clear(),this._root.append(e)},shapeCreated:function(t){var e,i=!1;return t instanceof h.Circle&&(i=d(this._createMarker(t))),i||(e={layer:this,shape:t},i=this.map.trigger("shapeCreated",e)),i},featureCreated:function(t){t.layer=this,this.map.trigger("shapeFeatureCreated",t)},_createMarker:function(t){var e=this.map.markers.bind({location:t.location},t.dataItem);return e&&this._markers.push(e),e},_clearMarkers:function(){for(var t=0;t<this._markers.length;t++)this.map.markers.remove(this._markers[t]);this._markers=[]},_pan:function(){this._panning||(this._panning=!0,this.surface.suspendTracking())},_panEnd:function(t){m.fn._panEnd.call(this,t),this._translateSurface(),this.surface.resumeTracking(),this._panning=!1},_translateSurface:function(){var t=this.map,e=t.locationToView(t.extent().nw);this.surface.translate&&(this.surface.translate(e),this.movable.moveTo({x:e.x,y:e.y}))},_handler:function(t){var e=this;return function(i){if(i.element){var n={layer:e,shape:i.element,originalEvent:i.originalEvent};e.map.trigger(t,n)}}},_activate:function(){m.fn._activate.call(this),this.map.bind("pan",this._pan)},_deactivate:function(){m.fn._deactivate.call(this),this.map.unbind("pan",this._pan)}}),v=o.extend({init:function(t,e,i){this.observer=i,this.locator=t,this.style=e},parse:function(t){var e=new l,i=!0;return"Feature"===t.type?(i=!1,this._loadGeometryTo(e,t.geometry,t),this._featureCreated(e,t)):this._loadGeometryTo(e,t,t),i&&e.children.length<2&&(e=e.children[0]),e},_shapeCreated:function(t){var e=!1;return this.observer&&this.observer.shapeCreated&&(e=this.observer.shapeCreated(t)),e},_featureCreated:function(t,e){this.observer&&this.observer.featureCreated&&this.observer.featureCreated({group:t,dataItem:e,properties:e.properties})},_loadGeometryTo:function(t,e,i){var n,o,a=e.coordinates;switch(e.type){case"LineString":o=this._loadPolygon(t,[a],i),this._setLineFill(o);break;case"MultiLineString":for(n=0;n<a.length;n++)o=this._loadPolygon(t,[a[n]],i),this._setLineFill(o);break;case"Polygon":this._loadPolygon(t,a,i);break;case"MultiPolygon":for(n=0;n<a.length;n++)this._loadPolygon(t,a[n],i);break;case"Point":this._loadPoint(t,a,i);break;case"MultiPoint":for(n=0;n<a.length;n++)this._loadPoint(t,a[n],i)}},_setLineFill:function(t){var e=t.segments;(e.length<4||!e[0].anchor().equals(c(e).anchor()))&&(t.options.fill=null)},_loadShape:function(t,e){return this._shapeCreated(e)||t.append(e),e},_loadPolygon:function(t,e,i){var n=this._buildPolygon(e);return n.dataItem=i,this._loadShape(t,n)},_buildPolygon:function(t){var e,i,n,o=t.length>1?h.MultiPath:h.Path,a=new o(this.style);for(e=0;e<t.length;e++)for(i=0;i<t[e].length;i++)n=this.locator.locationToView(f.fromLngLat(t[e][i])),0===i?a.moveTo(n.x,n.y):a.lineTo(n.x,n.y);return a},_loadPoint:function(t,e,i){var n=f.fromLngLat(e),o=this.locator.locationToView(n),a=new u.Circle(o,10),r=new h.Circle(a,this.style);return r.dataItem=i,r.location=n,this._loadShape(t,r)}});s(n.data,{schemas:{geojson:{type:"json",data:function(t){return"FeatureCollection"===t.type?t.features:"GeometryCollection"===t.type?t.geometries:t}}},transports:{geojson:{read:{dataType:"json"}}}}),s(r,{map:{layers:{shape:_,ShapeLayer:_},GeoJSONLoader:v}})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/layers/bubble.min",["dataviz/map/layers/shape.min"],t)}(function(){!function(t,e){var i=window.kendo,n=i.getter,o=i.dataviz,a=i.deepExtend,r=i.geometry,s=i.drawing,u=s.util,h=u.defined,l=o.map,c=l.Location,d=l.layers.ShapeLayer,p=d.extend({options:{autoBind:!0,locationField:"location",valueField:"value",minSize:0,maxSize:100,scale:"sqrt",symbol:"circle"},_load:function(t){var e,i,o,a,r,s,u,l,d,p,f;if(this.surface.clear(),0!==t.length)for(e=this.options,i=n(e.valueField),t=t.slice(0),t.sort(function(t,e){return i(e)-i(t)}),o=this._scaleType(),r=0;r<t.length;r++)s=t[r],u=n(e.locationField)(s),l=n(e.valueField)(s),h(u)&&h(l)&&(a||(a=new o([0,l],[e.minSize,e.maxSize])),u=c.create(u),d=this.map.locationToView(u),p=a.map(l),f=this._createSymbol({center:d,size:p,style:e.style,dataItem:s,location:u}),f.dataItem=s,f.location=u,f.value=l,this._drawSymbol(f))},_scaleType:function(){var t=this.options.scale;return i.isFunction(t)?t:o.map.scales[t]},_createSymbol:function(t){var e=this.options.symbol;return i.isFunction(e)||(e=o.map.symbols[e]),e(t)},_drawSymbol:function(t){var e={layer:this,shape:t},i=this.map.trigger("shapeCreated",e);i||this.surface.draw(t)}}),f=i.Class.extend({init:function(t,e){var i,n;this._domain=t,this._range=e,i=Math.sqrt(t[1])-Math.sqrt(t[0]),n=e[1]-e[0],this._ratio=n/i},map:function(t){var e=(Math.sqrt(t)-Math.sqrt(this._domain[0]))*this._ratio;return this._range[0]+e}}),m={circle:function(t){var e=new r.Circle(t.center,t.size/2);return new s.Circle(e,t.style)},square:function(t){var e=new s.Path(t.style),i=t.size/2,n=t.center;return e.moveTo(n.x-i,n.y-i).lineTo(n.x+i,n.y-i).lineTo(n.x+i,n.y+i).lineTo(n.x-i,n.y+i).close(),e}};a(o,{map:{layers:{bubble:p,BubbleLayer:p},scales:{sqrt:f},symbols:m}})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/layers/tile.min",["dataviz/map/layers/base.min","dataviz/map/location.min"],t)}(function(){!function(t,e){function i(t){return new c(_(t.x),_(t.y))}var n=Math,o=t.proxy,a=window.kendo,r=a.Class,s=a.template,u=a.dataviz,h=a.deepExtend,l=a.geometry,c=l.Point,d=u.map.layers.Layer,p=a.util,f=p.renderSize,m=a.drawing.util,_=m.round,v=m.limitValue,g=d.extend({init:function(t,e){d.fn.init.call(this,t,e),"string"==typeof this.options.subdomains&&(this.options.subdomains=this.options.subdomains.split(""));var i=this._viewType();this._view=new i(this.element,this.options)},destroy:function(){d.fn.destroy.call(this),this._view.destroy(),this._view=null},_beforeReset:function(){var t=this.map,e=t.locationToLayer(t.extent().nw).round();this._view.viewOrigin(e)},_reset:function(){d.fn._reset.call(this),this._updateView(),this._view.reset()},_viewType:function(){return y},_activate:function(){d.fn._activate.call(this),a.support.mobileOS||(this._pan||(this._pan=a.throttle(o(this._render,this),100)),this.map.bind("pan",this._pan))},_deactivate:function(){d.fn._deactivate.call(this),this._pan&&this.map.unbind("pan",this._pan)},_updateView:function(){var t=this._view,e=this.map,i=e.extent(),n={nw:e.locationToLayer(i.nw).round(),se:e.locationToLayer(i.se).round()};t.center(e.locationToLayer(e.center())),t.extent(n),t.zoom(e.zoom())},_resize:function(){this._render()},_panEnd:function(t){d.fn._panEnd.call(this,t),this._render()},_render:function(){this._updateView(),this._view.render()}}),y=r.extend({init:function(t,e){this.element=t,this._initOptions(e),this.pool=new w},options:{tileSize:256,subdomains:["a","b","c"],urlTemplate:""},center:function(t){this._center=t},extent:function(t){this._extent=t},viewOrigin:function(t){this._viewOrigin=t},zoom:function(t){this._zoom=t},pointToTileIndex:function(t){return new c(n.floor(t.x/this.options.tileSize),n.floor(t.y/this.options.tileSize))},tileCount:function(){var t=this.size(),e=this.pointToTileIndex(this._extent.nw),i=this._extent.nw,o=this.indexToPoint(e).translate(-i.x,-i.y);return{x:n.ceil((n.abs(o.x)+t.width)/this.options.tileSize),y:n.ceil((n.abs(o.y)+t.height)/this.options.tileSize)}},size:function(){var t=this._extent.nw,e=this._extent.se,i=e.clone().translate(-t.x,-t.y);return{width:i.x,height:i.y}},indexToPoint:function(t){var e=t.x,i=t.y;return new c(e*this.options.tileSize,i*this.options.tileSize)},subdomainText:function(){var t=this.options.subdomains;return t[this.subdomainIndex++%t.length]},destroy:function(){this.element.empty(),this.pool.empty()},reset:function(){this.pool.reset(),this.subdomainIndex=0,this.render()},render:function(){var t,e,i,n=this.tileCount(),o=this.pointToTileIndex(this._extent.nw);for(e=0;e<n.x;e++)for(i=0;i<n.y;i++)t=this.createTile({x:o.x+e,y:o.y+i}),t.visible||t.show()},createTile:function(t){var e=this.tileOptions(t),i=this.pool.get(this._center,e);return 0===i.element.parent().length&&this.element.append(i.element),i},tileOptions:function(t){var e=this.wrapIndex(t),n=this.indexToPoint(t),o=this._viewOrigin,a=n.clone().translate(-o.x,-o.y);return{index:e,currentIndex:t,point:n,offset:i(a),zoom:this._zoom,size:this.options.tileSize,subdomain:this.subdomainText(),urlTemplate:this.options.urlTemplate,errorUrlTemplate:this.options.errorUrlTemplate}},wrapIndex:function(t){var e=n.pow(2,this._zoom);
return{x:this.wrapValue(t.x,e),y:v(t.y,0,e-1)}},wrapValue:function(t,e){var i=n.abs(t)%e;return t=t>=0?i:e-(0===i?e:i)}}),A=r.extend({init:function(t,e){this.id=t,this.visible=!0,this._initOptions(e),this.createElement(),this.show()},options:{urlTemplate:"",errorUrlTemplate:""},createElement:function(){this.element=t("<img style='position: absolute; display: block;' alt='' />").css({width:this.options.size,height:this.options.size}).on("error",o(function(t){this.errorUrl()?t.target.setAttribute("src",this.errorUrl()):t.target.removeAttribute("src")},this))},show:function(){var t,e=this.element[0];e.style.top=f(this.options.offset.y),e.style.left=f(this.options.offset.x),t=this.url(),t&&e.setAttribute("src",t),e.style.visibility="visible",this.visible=!0},hide:function(){this.element[0].style.visibility="hidden",this.visible=!1},url:function(){var t=s(this.options.urlTemplate);return t(this.urlOptions())},errorUrl:function(){var t=s(this.options.errorUrlTemplate);return t(this.urlOptions())},urlOptions:function(){var t=this.options;return{zoom:t.zoom,subdomain:t.subdomain,z:t.zoom,x:t.index.x,y:t.index.y,s:t.subdomain,quadkey:t.quadkey,q:t.quadkey,culture:t.culture,c:t.culture}},destroy:function(){this.element&&(this.element.remove(),this.element=null)}}),w=r.extend({init:function(){this._items=[]},options:{maxSize:100},get:function(t,e){return this._items.length>=this.options.maxSize&&this._remove(t),this._create(e)},empty:function(){var t,e=this._items;for(t=0;t<e.length;t++)e[t].destroy();this._items=[]},reset:function(){var t,e=this._items;for(t=0;t<e.length;t++)e[t].hide()},_create:function(t){var e,i,n=this._items,o=p.hashKey(""+t.point+(""+t.offset)+t.zoom+t.urlTemplate);for(i=0;i<n.length;i++)if(n[i].id===o){e=n[i];break}return e?e.show():(e=new A(o,t),this._items.push(e)),e},_remove:function(t){var e,i,n=this._items,o=-1,a=-1;for(e=0;e<n.length;e++)i=n[e].options.point.distanceTo(t),i>o&&!n[e].visible&&(a=e,o=i);a!==-1&&(n[a].destroy(),n.splice(a,1))}});h(u,{map:{layers:{tile:g,TileLayer:g,ImageTile:A,TilePool:w,TileView:y}}})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/layers/bing.min",["dataviz/map/layers/tile.min"],t)}(function(){!function(t,e){var i=window.kendo,n=i.dataviz,o=i.deepExtend,a=i.drawing.util.defined,r=n.map.Extent,s=n.map.Location,u=n.map.layers.TileLayer,h=n.map.layers.TileView,l=u.extend({init:function(e,i){this.options.baseUrl=this._scheme()+"://dev.virtualearth.net/REST/v1/Imagery/Metadata/",u.fn.init.call(this,e,i),this._onMetadata=t.proxy(this._onMetadata,this),this._fetchMetadata()},options:{imagerySet:"road"},_fetchMetadata:function(){var e=this.options;if(!e.key)throw Error("Bing tile layer: API key is required");t.ajax({url:e.baseUrl+e.imagerySet,data:{output:"json",include:"ImageryProviders",key:e.key,uriScheme:this._scheme()},type:"get",dataType:"jsonp",jsonp:"jsonp",success:this._onMetadata})},_scheme:function(t){return t=t||window.location.protocol,"https"===t.replace(":","")?"https":"http"},_onMetadata:function(t){var e,i;t&&t.resourceSets.length&&(e=this.resource=t.resourceSets[0].resources[0],o(this._view.options,{urlTemplate:e.imageUrl.replace("{subdomain}","#= subdomain #").replace("{quadkey}","#= quadkey #").replace("{culture}","#= culture #"),subdomains:e.imageUrlSubdomains}),i=this.options,a(i.minZoom)||(i.minZoom=e.zoomMin),a(i.maxZoom)||(i.maxZoom=e.zoomMax),this._addAttribution(),"none"!==this.element.css("display")&&this._reset())},_viewType:function(){return c},_addAttribution:function(){var t,e,i,n,o,a=this.map.attribution;if(a&&(t=this.resource.imageryProviders))for(e=0;e<t.length;e++)for(i=t[e],n=0;n<i.coverageAreas.length;n++)o=i.coverageAreas[n],a.add({text:i.attribution,minZoom:o.zoomMin,maxZoom:o.zoomMax,extent:new r(new s(o.bbox[2],o.bbox[1]),new s(o.bbox[0],o.bbox[3]))})},imagerySet:function(t){return t?(this.options.imagerySet=t,this.map.attribution.clear(),this._fetchMetadata(),e):this.options.imagerySet}}),c=h.extend({options:{culture:"en-US"},tileOptions:function(t){var e=h.fn.tileOptions.call(this,t);return e.culture=this.options.culture,e.quadkey=this.tileQuadKey(this.wrapIndex(t)),e},tileQuadKey:function(t){var e,i,n,o="";for(n=this._zoom;n>0;n--)e=0,i=1<<n-1,0!==(t.x&i)&&e++,0!==(t.y&i)&&(e+=2),o+=e;return o}});o(n,{map:{layers:{bing:l,BingLayer:l,BingView:c}}})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/layers/marker.min",["dataviz/map/layers/base.min","dataviz/map/location.min","kendo.data.min","kendo.tooltip.min"],t)}(function(){!function(t,e){var i=document,n=Math,o=t.inArray,a=t.proxy,r=window.kendo,s=r.Class,u=r.data.DataSource,h=r.ui.Tooltip,l=r.dataviz,c=r.deepExtend,d=l.map,p=d.Location,f=d.layers.Layer,m=f.extend({init:function(t,e){f.fn.init.call(this,t,e),this._markerClick=a(this._markerClick,this),this.element.on("click",".k-marker",this._markerClick),this.items=[],this._initDataSource()},destroy:function(){f.fn.destroy.call(this),this.element.off("click",".k-marker",this._markerClick),this.dataSource.unbind("change",this._dataChange),this.clear()},options:{zIndex:1e3,autoBind:!0,dataSource:{},locationField:"location",titleField:"title"},add:function(e){if(!t.isArray(e))return this._addOne(e);for(var i=0;i<e.length;i++)this._addOne(e[i])},remove:function(t){t.destroy();var e=o(t,this.items);e>-1&&this.items.splice(e,1)},clear:function(){for(var t=0;t<this.items.length;t++)this.items[t].destroy();this.items=[]},update:function(t){var e,i=t.location();i&&(t.showAt(this.map.locationToView(i)),e={marker:t,layer:this},this.map.trigger("markerActivate",e))},_reset:function(){var t,e;for(f.fn._reset.call(this),t=this.items,e=0;e<t.length;e++)this.update(t[e])},bind:function(t,e){var i,n,o=d.Marker.create(t,this.options);if(o.dataItem=e,i={marker:o,layer:this},n=this.map.trigger("markerCreated",i),!n)return this.add(o),o},setDataSource:function(t){this.dataSource&&this.dataSource.unbind("change",this._dataChange),this.dataSource=r.data.DataSource.create(t),this.dataSource.bind("change",this._dataChange),this.options.autoBind&&this.dataSource.fetch()},_addOne:function(t){var e=_.create(t,this.options);return e.addTo(this),e},_initDataSource:function(){var t=this.options.dataSource;this._dataChange=a(this._dataChange,this),this.dataSource=u.create(t).bind("change",this._dataChange),t&&this.options.autoBind&&this.dataSource.fetch()},_dataChange:function(t){this._load(t.sender.view())},_load:function(t){var e,i,n,o;for(this._data=t,this.clear(),e=r.getter(this.options.locationField),i=r.getter(this.options.titleField),n=0;n<t.length;n++)o=t[n],this.bind({location:e(o),title:i(o)},o)},_markerClick:function(e){var i={marker:t(e.target).data("kendoMarker"),layer:this};this.map.trigger("markerClick",i)}}),_=s.extend({init:function(t){this.options=t||{}},addTo:function(t){this.layer=t.markers||t,this.layer.items.push(this),this.layer.update(this)},location:function(t){return t?(this.options.location=p.create(t).toArray(),this.layer&&this.layer.update(this),this):p.create(this.options.location)},showAt:function(t){this.render(),this.element.css({left:n.round(t.x),top:n.round(t.y)}),this.tooltip&&this.tooltip.popup&&this.tooltip.popup._position()},hide:function(){this.element&&(this.element.remove(),this.element=null),this.tooltip&&(this.tooltip.destroy(),this.tooltip=null)},destroy:function(){this.layer=null,this.hide()},render:function(){var e,n;this.element||(e=this.options,n=this.layer,this.element=t(i.createElement("span")).addClass("k-marker k-icon k-i-marker-"+r.toHyphens(e.shape||"pin")).attr("title",e.title).attr(e.attributes||{}).data("kendoMarker",this).css("zIndex",e.zIndex),n&&n.element.append(this.element),this.renderTooltip())},renderTooltip:function(){var t,e,i=this,n=i.options.title,o=i.options.tooltip||{};o&&h&&(t=o.template,t&&(e=r.template(t),o.content=function(t){return t.location=i.location(),t.marker=i,e(t)}),(n||o.content||o.contentUrl)&&(this.tooltip=new h(this.element,o),this.tooltip.marker=this))}});_.create=function(t,e){return t instanceof _?t:new _(c({},e,t))},c(l,{map:{layers:{marker:m,MarkerLayer:m},Marker:_}})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("dataviz/map/main.min",["dataviz/map/crs.min","dataviz/map/location.min"],t)}(function(){!function(t,e){var i=document,n=Math,o=n.min,a=n.pow,r=t.proxy,s=window.kendo,u=s.ui.Widget,h=s.deepExtend,l=s.dataviz,c=l.ui,d=s.geometry,p=d.Point,f=l.map,m=f.Extent,_=f.Location,v=f.crs.EPSG3857,g=s.util,y=g.renderPos,A=s.drawing.util,w=A.defined,C=A.limitValue,E=A.valueOrDefault,b="k-",x=.9,k=.93,F="DOMMouseScroll mousewheel",z=5,D=1,B=u.extend({init:function(t,e){s.destroy(t),u.fn.init.call(this,t),this._initOptions(e),this.bind(this.events,e),this.crs=new v,this.element.addClass(b+this.options.name.toLowerCase()).css("position","relative").empty().append(i.createElement("div")),this._viewOrigin=this._getOrigin(),this._initScroller(),this._initMarkers(),this._initControls(),this._initLayers(),this._reset(),this._mousewheel=r(this._mousewheel,this),this.element.bind(F,this._mousewheel)},options:{name:"Map",controls:{attribution:!0,navigator:{panStep:100},zoom:!0},layers:[],layerDefaults:{shape:{style:{fill:{color:"#fff"},stroke:{color:"#aaa",width:.5}}},bubble:{style:{fill:{color:"#fff",opacity:.5},stroke:{color:"#aaa",width:.5}}},marker:{shape:"pinTarget",tooltip:{position:"top"}}},center:[0,0],zoom:3,minSize:256,minZoom:1,maxZoom:19,markers:[],markerDefaults:{shape:"pinTarget",tooltip:{position:"top"}},wraparound:!0},events:["beforeReset","click","markerActivate","markerClick","markerCreated","pan","panEnd","reset","shapeClick","shapeCreated","shapeFeatureCreated","shapeMouseEnter","shapeMouseLeave","zoomEnd","zoomStart"],destroy:function(){this.scroller.destroy(),this.navigator&&this.navigator.destroy(),this.attribution&&this.attribution.destroy(),this.zoomControl&&this.zoomControl.destroy(),this.markers.destroy();for(var t=0;t<this.layers.length;t++)this.layers[t].destroy();u.fn.destroy.call(this)},zoom:function(t){var e=this.options;return w(t)?(t=n.round(C(t,e.minZoom,e.maxZoom)),e.zoom!==t&&(e.zoom=t,this._reset()),this):e.zoom},center:function(t){return t?(this.options.center=_.create(t).toArray(),this._reset(),this):_.create(this.options.center)},extent:function(t){return t?(this._setExtent(t),this):this._getExtent()},setOptions:function(t){u.fn.setOptions.call(this,t),this._reset()},locationToLayer:function(t,e){var i=!this.options.wraparound;return t=_.create(t),this.crs.toPoint(t,this._layerSize(e),i)},layerToLocation:function(t,e){var i=!this.options.wraparound;return t=p.create(t),this.crs.toLocation(t,this._layerSize(e),i)},locationToView:function(t){var e,i;return t=_.create(t),e=this.locationToLayer(this._viewOrigin),i=this.locationToLayer(t),i.translateWith(e.scale(-1))},viewToLocation:function(t,e){var i=this.locationToLayer(this._getOrigin(),e);return t=p.create(t),t=t.clone().translateWith(i),this.layerToLocation(t,e)},eventOffset:function(t){var e,i,n,o,a,r=this.element.offset();return t.x||t.y?(o="location",i=t.x[o]-r.left,n=t.y[o]-r.top,e=new d.Point(i,n)):(a=t.originalEvent||t,i=E(a.pageX,a.clientX)-r.left,n=E(a.pageY,a.clientY)-r.top,e=new d.Point(i,n)),e},eventToView:function(t){var e=this.eventOffset(t);return this.locationToView(this.viewToLocation(e))},eventToLayer:function(t){return this.locationToLayer(this.eventToLocation(t))},eventToLocation:function(t){var e=this.eventOffset(t);return this.viewToLocation(e)},viewSize:function(){var t=this.element,e=this._layerSize(),i=t.width();return this.options.wraparound||(i=o(e,i)),{width:i,height:o(e,t.height())}},exportVisual:function(){return this._reset(),!1},_setOrigin:function(t,e){var i,n=this.viewSize();return t=this._origin=_.create(t),i=this.locationToLayer(t,e),i.x+=n.width/2,i.y+=n.height/2,this.options.center=this.layerToLocation(i,e).toArray(),this},_getOrigin:function(t){var e,i=this.viewSize();return!t&&this._origin||(e=this.locationToLayer(this.center()),e.x-=i.width/2,e.y-=i.height/2,this._origin=this.layerToLocation(e)),this._origin},_setExtent:function(t){var e,i,o,a,r,s,u,h=m.create(t),l=h.se.clone();for(this.options.wraparound&&l.lng<0&&t.nw.lng>0&&(l.lng=180+(180+l.lng)),t=new m(h.nw,l),this.center(t.center()),e=this.element.width(),i=this.element.height(),o=this.options.maxZoom;o>=this.options.minZoom&&(a=this.locationToLayer(t.nw,o),r=this.locationToLayer(t.se,o),s=n.abs(r.x-a.x),u=n.abs(r.y-a.y),!(s<=e&&u<=i));o--);this.zoom(o)},_getExtent:function(){var t,e=this._getOrigin(),i=this.locationToLayer(e),n=this.viewSize();return i.x+=n.width,i.y+=n.height,t=this.layerToLocation(i),new m(e,t)},_zoomAround:function(t,e){this._setOrigin(this.layerToLocation(t,e),e),this.zoom(e)},_initControls:function(){var t=this.options.controls;c.Attribution&&t.attribution&&this._createAttribution(t.attribution),s.support.mobileOS||(c.Navigator&&t.navigator&&this._createNavigator(t.navigator),c.ZoomControl&&t.zoom&&this._createZoomControl(t.zoom))},_createControlElement:function(e,i){var n=e.position||i,o="."+y(n).replace(" ","."),a=t(".k-map-controls"+o,this.element);return 0===a.length&&(a=t("<div>").addClass("k-map-controls "+y(n)).appendTo(this.element)),t("<div>").appendTo(a)},_createAttribution:function(t){var e=this._createControlElement(t,"bottomRight");this.attribution=new c.Attribution(e,t)},_createNavigator:function(t){var e=this._createControlElement(t,"topLeft"),i=this.navigator=new c.Navigator(e,t);this._navigatorPan=r(this._navigatorPan,this),i.bind("pan",this._navigatorPan),this._navigatorCenter=r(this._navigatorCenter,this),i.bind("center",this._navigatorCenter)},_navigatorPan:function(t){var e=this,i=e.scroller,n=i.scrollLeft+t.x,o=i.scrollTop-t.y,a=this._virtualSize,r=this.element.height(),s=this.element.width();n=C(n,a.x.min,a.x.max-s),o=C(o,a.y.min,a.y.max-r),e.scroller.one("scroll",function(t){e._scrollEnd(t)}),e.scroller.scrollTo(-n,-o)},_navigatorCenter:function(){this.center(this.options.center)},_createZoomControl:function(t){var e=this._createControlElement(t,"topLeft"),i=this.zoomControl=new c.ZoomControl(e,t);this._zoomControlChange=r(this._zoomControlChange,this),i.bind("change",this._zoomControlChange)},_zoomControlChange:function(t){this.trigger("zoomStart",{originalEvent:t})||(this.zoom(this.zoom()+t.delta),this.trigger("zoomEnd",{originalEvent:t}))},_initScroller:function(){var t=s.support.mobileOS?k:x,e=this.options.zoomable!==!1,i=this.scroller=new s.mobile.ui.Scroller(this.element.children(0),{friction:t,velocityMultiplier:z,zoom:e,mousewheelScrolling:!1,supportDoubleTap:!0});i.bind("scroll",r(this._scroll,this)),i.bind("scrollEnd",r(this._scrollEnd,this)),i.userEvents.bind("gesturestart",r(this._scaleStart,this)),i.userEvents.bind("gestureend",r(this._scale,this)),i.userEvents.bind("doubleTap",r(this._doubleTap,this)),i.userEvents.bind("tap",r(this._tap,this)),this.scrollElement=i.scrollElement},_initLayers:function(){var t,e,i,n,o,a=this.options.layers,r=this.layers=[];for(t=0;t<a.length;t++)e=a[t],i=e.type||"shape",n=this.options.layerDefaults[i],o=l.map.layers[i],r.push(new o(this,h({},n,e)))},_initMarkers:function(){this.markers=new f.layers.MarkerLayer(this,this.options.markerDefaults),this.markers.add(this.options.markers)},_scroll:function(t){var e=this.locationToLayer(this._viewOrigin).round(),i=t.sender.movable,n=new d.Point(i.x,i.y).scale(-1).scale(1/i.scale);e.x+=n.x,e.y+=n.y,this._scrollOffset=n,this._setOrigin(this.layerToLocation(e)),this.trigger("pan",{originalEvent:t,origin:this._getOrigin(),center:this.center()})},_scrollEnd:function(t){this._scrollOffset&&this._panComplete()&&(this._scrollOffset=null,this._panEndTS=new Date,this.trigger("panEnd",{originalEvent:t,origin:this._getOrigin(),center:this.center()}))},_panComplete:function(){return new Date-(this._panEndTS||0)>50},_scaleStart:function(t){if(this.trigger("zoomStart",{originalEvent:t})){var e=t.touches[1];e&&e.cancel()}},_scale:function(t){var e=this.scroller.movable.scale,i=this._scaleToZoom(e),n=new d.Point(t.center.x,t.center.y),o=this.viewToLocation(n,i),a=this.locationToLayer(o,i),r=a.translate(-n.x,-n.y);this._zoomAround(r,i),this.trigger("zoomEnd",{originalEvent:t})},_scaleToZoom:function(t){var e=this._layerSize()*t,i=e/this.options.minSize,o=n.log(i)/n.log(2);return n.round(o)},_reset:function(){this.attribution&&this.attribution.filter(this.center(),this.zoom()),this._viewOrigin=this._getOrigin(!0),this._resetScroller(),this.trigger("beforeReset"),this.trigger("reset")},_resetScroller:function(){var t,e,i,n,o=this.scroller,r=o.dimensions.x,s=o.dimensions.y,u=this._layerSize(),h=this.extent().nw,l=this.locationToLayer(h).round();o.movable.round=!0,o.reset(),o.userEvents.cancel(),t=this.zoom(),o.dimensions.forcedMinScale=a(2,this.options.minZoom-t),o.dimensions.maxScale=a(2,this.options.maxZoom-t),e={min:-l.x,max:u-l.x},i={min:-l.y,max:u-l.y},this.options.wraparound&&(e.max=20*u,e.min=-e.max),this.options.pannable===!1&&(n=this.viewSize(),e.min=i.min=0,e.max=n.width,i.max=n.height),r.makeVirtual(),s.makeVirtual(),r.virtualSize(e.min,e.max),s.virtualSize(i.min,i.max),this._virtualSize={x:e,y:i}},_renderLayers:function(){var t,e,i,n,o,a=this.options.layers,r=this.layers=[],s=this.scrollWrap;for(s.empty(),t=0;t<a.length;t++)e=a[t],i=e.type||"shape",n=this.options.layerDefaults[i],o=l.map.layers[i],r.push(new o(this,h({},n,e)))},_layerSize:function(t){return t=E(t,this.options.zoom),this.options.minSize*a(2,t)},_tap:function(t){if(this._panComplete()){var e=this.eventOffset(t);this.trigger("click",{originalEvent:t,location:this.viewToLocation(e)})}},_doubleTap:function(t){var e,i,n,o,a,r=this.options;r.zoomable!==!1&&(this.trigger("zoomStart",{originalEvent:t})||(e=this.zoom()+D,i=this.eventOffset(t),n=this.viewToLocation(i),o=this.locationToLayer(n,e),a=o.translate(-i.x,-i.y),this._zoomAround(a,e),this.trigger("zoomEnd",{originalEvent:t})))},_mousewheel:function(t){var e,i,n,o,a,r,s,u;t.preventDefault(),e=l.mwDelta(t)>0?-1:1,i=this.options,n=this.zoom(),o=C(n+e,i.minZoom,i.maxZoom),i.zoomable!==!1&&o!==n&&(this.trigger("zoomStart",{originalEvent:t})||(a=this.eventOffset(t),r=this.viewToLocation(a),s=this.locationToLayer(r,o),u=s.translate(-a.x,-a.y),this._zoomAround(u,o),this.trigger("zoomEnd",{originalEvent:t})))}});l.ui.plugin(B)}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()}),function(t,define){define("kendo.dataviz.map.min",["kendo.data.min","kendo.userevents.min","kendo.tooltip.min","kendo.mobile.scroller.min","kendo.draganddrop.min","kendo.dataviz.core.min","dataviz/map/location.min","dataviz/map/attribution.min","dataviz/map/navigator.min","dataviz/map/zoom.min","dataviz/map/crs.min","dataviz/map/layers/base.min","dataviz/map/layers/shape.min","dataviz/map/layers/bubble.min","dataviz/map/layers/tile.min","dataviz/map/layers/bing.min","dataviz/map/layers/marker.min","dataviz/map/main.min"],t)}(function(){return window.kendo},"function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()});
//# sourceMappingURL=kendo.dataviz.map.min.js.map
