/** 
 * Kendo UI v2019.2.619 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2019 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(t,define){define("kendo.scheduler.view.min",["kendo.core.min"],t)}(function(){return kendo.ui.scheduler={},function(t){function e(t,e){function n(t,i){var s,r;if(i=i[e])for(s=o[t]=o[t]||[],r=0;r<i.length;r++)s.push(i[r]),n(t+1,i[r])}var o=[];return n(0,t),o}function n(){return b.support.cssBorderSpacing?"":'cellspacing="0"'}function o(e,o){return e.length?"<table "+n()+' class="'+t.trim("k-scheduler-table "+(o||""))+'"><tr>'+e.join("</tr><tr>")+"</tr></table>":""}function i(t,e){return t.length?"<div style='position:relative'>"+o(t,e)+"</div>":""}function s(e,n,i){var s,r=[];if(i>0)for(s=0;s<e;s++)r.push("<th>&#8203;</th>");return n&&r.push('<th class="k-scheduler-times-all-day">'+n.text+"</th>"),i<1?t():t('<div class="k-scheduler-times">'+o(r)+"</div>")}function r(e,n,s){var r,l,a,u,c,h,d,f,g,p,_=[];for(l=0;l<e.length;l++){for(a=e[l],u=[],c=n/a.length,r=0;r<a.length;r++)h=a[r],u.push('<th colspan="'+(h.colspan||c)+'" class="'+(h.className||"")+'">'+h.text+"</th>");_.push(u.join(""))}if(d=[],s){for(f=e[e.length-1],g=[],p=s.cellContent,r=0;r<f.length;r++)g.push('<td class="'+(f[r].className||"")+'">'+(p?p(r):"&nbsp;")+"</td>");d.push(g.join(""))}return t('<div class="k-scheduler-header k-state-default"><div class="k-scheduler-header-wrap">'+o(_)+i(d,"k-scheduler-header-all-day")+"</div></div>")}function l(e,n,i){var s,r,l,a,u,c,h=Array(n).join().split(","),d=[];for(r=0;r<e.length;r++)for(l=e[r],a=n/l.length,s=0;s<l.length;s++)u=l[s].className||"",c=l[s].text,l[s].allDay&&(u="k-scheduler-times-all-day"),i&&u.indexOf("k-scheduler-group-cell")!==-1&&(c='<span class="k-scheduler-group-text">'+c+"</span>"),h[a*s]+='<th class="'+u+'" rowspan="'+a+'">'+c+"</th>";for(s=0;s<n;s++)d.push(h[s]);return n<1?t():t('<div class="k-scheduler-times">'+o(d)+"</div>")}function a(){return t('<div class="k-scheduler-content"><table '+n()+' class="k-scheduler-table"/></div>')}function u(){return y=y?y:b.support.scrollbar()}function c(t,e,n){var o,i,s,r,l;for(o=t.length-1;o>=0;o--)i=h(t[o]),s=i.start,l=i.end,r=s<=e&&l>=e,(r||s>=e&&l<=n||e<=s&&n>=s)&&(s<e&&(e=s),l>n&&(n=l));return d(t,e,n)}function h(t){return{start:t.start,end:t.end}}function d(t,e,n){var o,i,s=[];for(o=0;o<t.length;o++)i=h(t[o]),(i.start<e&&i.end>e||i.start>=e&&i.end<=n)&&s.push(t[o]);return s}function f(t){return p(t)}function g(t){return p(t)}function p(t){var e,n,o,i,s,r,l,a=[];for(e=0;e<t.length;e++){for(n=t[e],o=h(n),i=null,s=0,r=a.length;s<r;s++)if(l=o.start>a[s].end,o.start<a[s].start||l){i=a[s],i.end<o.end&&(i.end=o.end);break}i||(i={start:o.start,end:o.end,events:[]},a.push(i)),i.events.push(n)}return a}function _(e,n,o,i){var s=[];return t.each(n,function(t,n){var r=n.className?"k-slot-cell "+n.className:"k-slot-cell",l={text:n.text,className:r};l[e]=i&&!n.minorTicks?_(e,n.columns,o,i):o,s.push(l)}),s}function v(e,n,o,i,s,r){var l,a,u,c=n[0],h=[];if(c){if(s&&o)t.each(s,function(t,o){o[e]=r&&!o.minorTicks?v(e,n,o.columns,i,o.columns,r):v(e,n,null,i)}),h=s;else for(l=c.dataSource.view(),a=0;a<l.length;a++)u={text:i({text:b.htmlEncode(b.getter(c.dataTextField)(l[a])),color:b.getter(c.dataColorField)(l[a]),field:c.field,title:c.title,name:c.name,value:b.getter(c.dataValueField)(l[a])}),className:"k-slot-cell k-scheduler-group-cell"},u[e]=v(e,n.slice(1),o,i),h.push(u);return h}return o}function m(e){return function(n){if(t.isArray(n)||n instanceof b.data.ObservableArray){for(var o=0;o<n.length;o++)if(n[o]==e)return!0;return!1}return n==e}}function S(t){t.className=t.className.replace(D,"")+" k-state-selected"}var y,C,D,b=window.kendo,x=b.ui,k=b.date.getDate,w=x.Widget,I=b._outerHeight,H=b.keys,T=".kendoSchedulerView",R="k-event-inverse",L=1024,z=Math,E='<div class="k-marquee k-scheduler-marquee"><div class="k-marquee-color"></div><div class="k-marquee-text"><div class="k-label-top"></div><div class="k-label-bottom"></div></div></div>',G=b.Class.extend({init:function(t,e){this._index=t,this._timeSlotCollections=[],this._daySlotCollections=[],this._isRtl=e},addTimeSlotCollection:function(t,e){return this._addCollection(t,e,this._timeSlotCollections)},addDaySlotCollection:function(t,e){return this._addCollection(t,e,this._daySlotCollections)},_addCollection:function(t,e,n){var o=new A(t,e,this._index,n.length);return n.push(o),o},timeSlotCollectionCount:function(){return this._timeSlotCollections.length},daySlotCollectionCount:function(){return this._daySlotCollections.length},daySlotByPosition:function(t,e,n){return this._slotByPosition(t,e,this._daySlotCollections,n)},timeSlotByPosition:function(t,e,n){return this._slotByPosition(t,e,this._timeSlotCollections,n)},_slotByPosition:function(t,e,n,o){var i,s,r,l,a,u,c,h,d;for(i=0;i<n.length;i++)for(s=n[i],r=0;r<s.count();r++)if(l=s.at(r),a=l.offsetWidth,u=l.offsetHeight,h=l.offsetLeft+a,d=l.offsetTop+u,o||(c=s.at(r+1)),c&&(c.offsetLeft!=l.offsetLeft?h=this._isRtl?l.offsetLeft+(l.offsetLeft-c.offsetLeft):c.offsetLeft:d=c.offsetTop),t>=l.offsetLeft&&t<h&&e>=l.offsetTop&&e<d)return l},refresh:function(){var t;for(t=0;t<this._daySlotCollections.length;t++)this._daySlotCollections[t].refresh();for(t=0;t<this._timeSlotCollections.length;t++)this._timeSlotCollections[t].refresh()},timeSlotRanges:function(t,e){var n,o=this._timeSlotCollections,i=this._startSlot(t,o);if(!i.inRange&&t>=i.slot.end&&(i=null),n=i,t<e&&(n=this._endSlot(e,o)),n&&!n.inRange&&e<=n.slot.start&&(n=null),null===i&&null===n)return[];if(null===i){if(n.slot.end<=t)return[];i={inRange:!0,slot:o[n.slot.collectionIndex].first()}}if(null===n){if(i.slot.start>=e)return[];n={inRange:!0,slot:o[i.slot.collectionIndex].last()}}return this._continuousRange(W,o,i,n)},daySlotRanges:function(t,e,n){var o,i=this._daySlotCollections,s=this._startSlot(t,i,n);if(!s.inRange&&t>=s.slot.end&&(s=null),o=s,t<e&&(o=this._endSlot(e,i,n)),o&&!o.inRange&&e<=o.slot.start&&(o=null),null===s&&null===o)return[];if(null===s){if(o.slot.end<=t)return[];do t+=b.date.MS_PER_DAY,s=this._startSlot(t,i,n);while(!s.inRange&&t>=s.slot.end)}if(null===o){if(s.slot.start>=e)return[];do e-=b.date.MS_PER_DAY,o=this._endSlot(e,i,n);while(!o.inRange&&e<=o.slot.start)}return this._continuousRange(M,i,s,o)},_continuousRange:function(t,e,n,o){var i,s,r,l,a,u,c=n.slot,h=o.slot,d=c.collectionIndex,f=h.collectionIndex,g=[];for(i=d;i<=f;i++)s=e[i],r=s.first(),l=s.last(),a=!1,u=!1,i==d&&(u=!n.inRange),i==f&&(a=!o.inRange),r.start<c.start&&(r=c),l.start>h.start&&(l=h),d<f&&(i==d?a=!0:i==f?u=!0:a=u=!0),g.push(new t({start:r,end:l,collection:s,head:a,tail:u}));return g},slotRanges:function(t,e){var n=t._startTime||b.date.toUtcTime(t.start),o=t._endTime||b.date.toUtcTime(t.end);return void 0===e&&(e=t.isMultiDay()),e?this.daySlotRanges(n,o,t.isAllDay):this.timeSlotRanges(n,o)},ranges:function(t,e,n,o){return"number"!=typeof t&&(t=b.date.toUtcTime(t)),"number"!=typeof e&&(e=b.date.toUtcTime(e)),n?this.daySlotRanges(t,e,o):this.timeSlotRanges(t,e)},_startCollection:function(t,e){var n,o;for(n=0;n<e.length;n++)if(o=e[n],o.startInRange(t))return o;return null},_endCollection:function(t,e,n){var o,i;for(o=0;o<e.length;o++)if(i=e[o],i.endInRange(t,n))return i;return null},_getCollections:function(t){return t?this._daySlotCollections:this._timeSlotCollections},continuousSlot:function(t,e){var n=e?-1:1,o=this._getCollections(t.isDaySlot),i=o[t.collectionIndex+n];return i?i[e?"last":"first"]():void 0},firstSlot:function(){var t=this._getCollections(this.daySlotCollectionCount());return t[0].first()},lastSlot:function(){var t=this._getCollections(this.daySlotCollectionCount());return t[t.length-1].last()},upSlot:function(t,e,n){var o=this,i=function(t,n,i){var s=0===i;if(!e&&!t&&s&&o.daySlotCollectionCount())return o._daySlotCollections[0].at(n)};return this.timeSlotCollectionCount()||(e=!0),this._verticalSlot(t,-1,i,n)},downSlot:function(t,e,n){var o=this,i=function(t,n,i){if(!e&&t&&o.timeSlotCollectionCount())return o._timeSlotCollections[i].at(0)};return this.timeSlotCollectionCount()||(e=!0),this._verticalSlot(t,1,i,n)},leftSlot:function(t,e){return this._horizontalSlot(t,-1,e)},rightSlot:function(t,e){return this._horizontalSlot(t,1,e)},_horizontalSlot:function(t,e,n){var o,i=t.index,s=t.isDaySlot,r=t.collectionIndex,l=this._getCollections(s);return s=!n&&s,s?i+=e:r+=e,o=l[r],o?o.at(i):void 0},_verticalSlot:function(t,e,n,o){var i,s=t.index,r=t.isDaySlot,l=t.collectionIndex,a=this._getCollections(r);return(t=n(r,l,s))?t:(r=!o&&r,r?l+=e:s+=e,i=a[l],i?i.at(s):void 0)},_collection:function(t,e){var n=e?this._daySlotCollections:this._timeSlotCollections;return n[t]},_startSlot:function(t,e,n){var o,i=this._startCollection(t,e),s=!0;return i||(i=e[0],s=!1),o=i.slotByStartDate(t,n),o||(o=i.first(),s=!1),{slot:o,inRange:s}},_endSlot:function(t,e,n){var o,i=this._endCollection(t,e,n),s=!0;return i||(i=e[e.length-1],s=!1),o=i.slotByEndDate(t,n),o||(o=i.last(),s=!1),{slot:o,inRange:s}},getSlotCollection:function(t,e){return this[e?"getDaySlotCollection":"getTimeSlotCollection"](t)},getTimeSlotCollection:function(t){return this._timeSlotCollections[t]},getDaySlotCollection:function(t){return this._daySlotCollections[t]}}),B=b.Class.extend({init:function(e){t.extend(this,e)},innerHeight:function(){var t,e=this.collection,n=this.start.index,o=this.end.index,i=0;for(t=n;t<=o;t++)i+=e.at(t).offsetHeight;return i},events:function(){return this.collection.events()},addEvent:function(t){this.events().push(t)},startSlot:function(){return this.start.offsetLeft>this.end.offsetLeft?this.end:this.start},endSlot:function(){return this.start.offsetLeft>this.end.offsetLeft?this.start:this.end}}),W=B.extend({innerHeight:function(){var t,e=this.collection,n=this.start.index,o=this.end.index,i=0;for(t=n;t<=o;t++)i+=e.at(t).offsetHeight;return i},outerRect:function(t,e,n){return this._rect("offset",t,e,n)},_rect:function(t,e,n,o){var i,s,r,l,a,u,c,h,d=this.start,f=this.end,g=b.support.isRtl(d.element);return"number"!=typeof e&&(e=b.date.toUtcTime(e)),"number"!=typeof n&&(n=b.date.toUtcTime(n)),o?(i=d.offsetTop,s=f.offsetTop+f[t+"Height"],g?(r=f.offsetLeft,l=d.offsetLeft+d[t+"Width"]):(r=d.offsetLeft,l=f.offsetLeft+f[t+"Width"])):(a=e-d.start,a<0&&(a=0),u=d.end-d.start,i=d.offsetTop+d[t+"Height"]*a/u,c=f.end-n,c<0&&(c=0),h=f.end-f.start,s=f.offsetTop+f[t+"Height"]-f[t+"Height"]*c/h,g?(r=Math.round(f.offsetLeft+f[t+"Width"]*c/h),l=Math.round(d.offsetLeft+d[t+"Width"]-d[t+"Width"]*a/u)):(r=Math.round(d.offsetLeft+d[t+"Width"]*a/u),l=Math.round(f.offsetLeft+f[t+"Width"]-f[t+"Width"]*c/h))),{top:i,bottom:s,left:0===r?r:r+1,right:l}},innerRect:function(t,e,n){return this._rect("client",t,e,n)}}),M=B.extend({innerWidth:function(){var t,e=this.collection,n=this.start.index,o=this.end.index,i=0,s=n!==o?"offsetWidth":"clientWidth";for(t=n;t<=o;t++)i+=e.at(t)[s];return i}}),A=b.Class.extend({init:function(t,e,n,o){this._slots=[],this._events=[],this._start=b.date.toUtcTime(t),this._end=b.date.toUtcTime(e),this._groupIndex=n,this._collectionIndex=o},refresh:function(){for(var t=0;t<this._slots.length;t++)this._slots[t].refresh()},startInRange:function(t){return this._start<=t&&t<this._end},endInRange:function(t,e){var n=e?t<this._end:t<=this._end;return this._start<=t&&n},slotByStartDate:function(t){var e,n,o=t;for("number"!=typeof o&&(o=b.date.toUtcTime(t)),e=0;e<this._slots.length;e++)if(n=this._slots[e],n.startInRange(o))return n;return null},slotByEndDate:function(t,e){var n,o,i=t;if("number"!=typeof i&&(i=b.date.toUtcTime(t)),e)return this.slotByStartDate(t,!1);for(n=0;n<this._slots.length;n++)if(o=this._slots[n],o.endInRange(i))return o;return null},count:function(){return this._slots.length},events:function(){return this._events},addTimeSlot:function(t,e,n,o){var i=new F(t,e,n,this._groupIndex,this._collectionIndex,this._slots.length,o);this._slots.push(i)},addDaySlot:function(t,e,n,o){var i=new N(t,e,n,this._groupIndex,this._collectionIndex,this._slots.length,o);this._slots.push(i)},first:function(){return this._slots[0]},last:function(){return this._slots[this._slots.length-1]},at:function(t){return this._slots[t]}}),V=b.Class.extend({init:function(t,e,n,o,i,s){this.element=t,this.clientWidth=t.clientWidth,this.clientHeight=t.clientHeight,this.offsetWidth=t.offsetWidth,this.offsetHeight=t.offsetHeight,this.offsetTop=t.offsetTop,this.offsetLeft=t.offsetLeft,this.start=e,this.end=n,this.element=t,this.groupIndex=o,this.collectionIndex=i,this.index=s,this.isDaySlot=!1},refresh:function(){var t=this.element;this.clientWidth=t.clientWidth,this.clientHeight=t.clientHeight,this.offsetWidth=t.offsetWidth,this.offsetHeight=t.offsetHeight,this.offsetTop=t.offsetTop,this.offsetLeft=t.offsetLeft},startDate:function(){return b.timezone.toLocalDate(this.start)},endDate:function(){return b.timezone.toLocalDate(this.end)},startInRange:function(t){return this.start<=t&&t<this.end},endInRange:function(t){return this.start<t&&t<=this.end},startOffset:function(){return this.start},endOffset:function(){return this.end}}),F=V.extend({init:function(t,e,n,o,i,s,r){V.fn.init.apply(this,arguments),this.isHorizontal=!!r},offsetX:function(t,e){return t?this.offsetLeft+e:this.offsetLeft+e},startInRange:function(t){return this.start<=t&&t<this.end},endInRange:function(t){return this.start<t&&t<=this.end},startOffset:function(e,n,o){var i,s,r,l,a;if(o)return this.start;if(i=t(this.element).offset(),s=this.end-this.start,this.isHorizontal){if(a=b.support.isRtl(this.element),r=e-i.left,l=Math.floor(s*(r/this.offsetWidth)),a)return this.start+s-l}else r=n-i.top,l=Math.floor(s*(r/this.offsetHeight));return this.start+l},endOffset:function(e,n,o){var i,s,r,l,a;if(o)return this.end;if(i=t(this.element).offset(),s=this.end-this.start,this.isHorizontal){if(a=b.support.isRtl(this.element),r=e-i.left,l=Math.floor(s*(r/this.offsetWidth)),a)return this.start+s-l}else r=n-i.top,l=Math.floor(s*(r/this.offsetHeight));return this.start+l}}),N=V.extend({init:function(t,e,n,o,i,s,r){if(V.fn.init.apply(this,arguments),this.eventCount=r,this.isDaySlot=!0,this.element.children.length){var l=this.element.children[0];this.firstChildHeight=l.offsetHeight,this.firstChildTop=l.offsetTop}else this.firstChildHeight=3,this.firstChildTop=0},startDate:function(){var t=new Date(this.start);return b.timezone.apply(t,"Etc/UTC")},endDate:function(){var t=new Date(this.end);return b.timezone.apply(t,"Etc/UTC")},startInRange:function(t){return this.start<=t&&t<this.end},endInRange:function(t){return this.start<t&&t<=this.end}});b.ui.SchedulerView=w.extend({init:function(e,n){w.fn.init.call(this,e,n),this._normalizeOptions(),this._scrollbar=u(),this._isRtl=b.support.isRtl(e),this._resizeHint=t(),this._moveHint=t(),this._cellId=b.guid(),this._resourcesForGroups(),this._selectedSlots=[]},visibleEndDate:function(){return this.endDate()},_normalizeOptions:function(){var t=this.options;t.startTime&&t.startTime.setMilliseconds(0),t.endTime&&t.endTime.setMilliseconds(0),t.workDayStart&&t.workDayStart.setMilliseconds(0),t.workDayEnd&&t.workDayEnd.setMilliseconds(0)},_isMobile:function(){var t=this.options;return t.mobile===!0&&b.support.mobileOS||"phone"===t.mobile||"tablet"===t.mobile},_addResourceView:function(){var t=new G(this.groups.length,this._isRtl);return this.groups.push(t),t},dateForTitle:function(){return b.format(this.options.selectedDateFormat,this.startDate(),this.endDate())},shortDateForTitle:function(){return b.format(this.options.selectedShortDateFormat,this.startDate(),this.endDate())},mobileDateForTitle:function(){return b.format(this.options.selectedMobileDateFormat||this.options.selectedShortDateFormat,this.startDate(),this.endDate())},_changeGroup:function(t,e){var n=e?"prevGroupSlot":"nextGroupSlot",o=this[n](t.start,t.groupIndex,t.isAllDay);return o&&(t.groupIndex+=e?-1:1),this._isGroupedByDate()&&!o&&(t.groupIndex=e?this.groups.length-1:0),o},_changeDate:function(t,e,n){var o,i,s,r=this.groups[t.groupIndex];if(n){if(o=r._getCollections(!1),i=r.daySlotCollectionCount()?e.index-1:e.collectionIndex-1,i>=0)return o[i]._slots[o[i]._slots.length-1]}else if(o=r._getCollections(r.daySlotCollectionCount()),i=r.daySlotCollectionCount()?0:e.collectionIndex+1,s=r.daySlotCollectionCount()?e.collectionIndex+1:0,o[i]&&o[i]._slots[s])return o[i]._slots[s]},_changeGroupContinuously:function(){return null},_changeViewPeriod:function(){return!1},_isInRange:function(t,e){return!!(t&&e&&this.options.min&&this.options.max)&&(k(t)<=k(this.options.min)||k(e)>=k(this.options.max))},_horizontalSlots:function(t,e,n,o){var i,s,r,l=o?"leftSlot":"rightSlot",a={startSlot:e[0].start,endSlot:e[e.length-1].end},u=this.groups[t.groupIndex],c=this._isVerticallyGrouped();return n||(i=this._normalizeHorizontalSelection(t,e,o),i&&(a.startSlot=a.endSlot=i)),this._isGroupedByDate()&&!n?(s=this._changeGroup(t,o),s?a.startSlot=a.endSlot=s:a=this._getNextHorizontalRange(u,l,a)):(a.startSlot=u[l](a.startSlot),a.endSlot=u[l](a.endSlot),n||c||a.startSlot&&a.endSlot||(a.startSlot=a.endSlot=this._changeGroup(t,o))),a.startSlot&&a.endSlot||this._isGroupedByDate()||(r=this._continuousSlot(t,e,o),r=this._changeGroupContinuously(t,r,n,o),r&&(a.startSlot=a.endSlot=r)),a},_getNextHorizontalRange:function(t,e,n){return this._isVerticallyGrouped()||(n.startSlot=t[e](n.startSlot),n.endSlot=t[e](n.endSlot)),n},_verticalSlots:function(t,e,n,o){var i,s,r=this.groups[t.groupIndex],l={startSlot:e[0].start,endSlot:e[e.length-1].end};return n||(i=this._normalizeVerticalSelection(t,e,o),i&&(l.startSlot=l.endSlot=i)),s=o?"upSlot":"downSlot",l=this._getNextVerticalRange(r,s,l,n),n||!this._isVerticallyGrouped()||l.startSlot&&l.endSlot||(l.startSlot=l.endSlot=this._isGroupedByDate()?this._changeDate(t,i,o):this._changeGroup(t,o)),l},_getNextVerticalRange:function(t,e,n,o){return n.startSlot=t[e](n.startSlot,o),n.endSlot=t[e](n.endSlot,o),n},_normalizeHorizontalSelection:function(){return null},_normalizeVerticalSelection:function(t,e,n){var o;return o=n?e[0].start:e[e.length-1].end},_continuousSlot:function(){return null},_footer:function(){var e,n=this,o=n.options;n._isMobile()&&(e='<div class="k-header k-scheduler-footer">',e+='<span class="k-state-default k-scheduler-today"><a href="#" class="k-link">',e+=o.messages.today+"</a></span>",e+="</div>",n.footer=t(e).appendTo(n.element)),n.footer&&n.footer.on("click"+T,".k-scheduler-today",function(t){var e,i,s,r,l;t.preventDefault(),e=n.options.timezone,i="today",s=new Date,e?(l=b.timezone.offset(s,e),r=b.timezone.convert(s,s.getTimezoneOffset(),l)):r=s,n.trigger("navigate",{view:n.name||o.name,action:i,date:r})})},constrainSelection:function(t){var e,n=this.groups[0];this.inRange(t)?n.daySlotCollectionCount()?n.timeSlotCollectionCount()||(t.isAllDay=!0):t.isAllDay=!1:(e=n.firstSlot(),t.isAllDay=e.isDaySlot,t.start=e.startDate(),t.end=e.endDate()),this.groups[t.groupIndex]||(t.groupIndex=0)},move:function(t,e,n){var o,i,s,r,l,a,u=!1,c=this.groups[t.groupIndex],h=this._isGroupedByDate()&&this._isVerticallyGrouped();if(c.timeSlotCollectionCount()||(t.isAllDay=!0),o=c.ranges(t.start,t.end,t.isAllDay,!1),e===H.DOWN||e===H.UP){if(u=!0,r=e===H.UP,this._updateDirection(t,o,n,r,!0),l=this._verticalSlots(t,o,n,r),!l.startSlot&&!n&&this._changeViewPeriod(t,r,!h))return u}else if((e===H.LEFT||e===H.RIGHT)&&(u=!0,r=e===H.LEFT,this._updateDirection(t,o,n,r,!1),l=this._horizontalSlots(t,o,n,r),!l.startSlot&&!n&&this._changeViewPeriod(t,r,h)))return u;return u&&(i=l.startSlot,s=l.endSlot,n?(a=t.backward,a&&i?t.start=i.startDate():!a&&s&&(t.end=s.endDate())):i&&s&&(t.isAllDay=i.isDaySlot,t.start=i.startDate(),t.end=s.endDate()),t.events=[]),u},moveToEventInGroup:function(e,n,o,i){var s,r,l,a,u=e._continuousEvents||[],c=i?-1:1,h=u.length,d=i?h-1:0;if(o.length)for(l=o[o.length-1],a=0;a<u.length;a++)u[a].uid===l&&(d=a+c);for(;d<h&&d>-1;){if(r=u[d],(!i&&r.start.startDate()>=n.startDate()||i&&r.start.startDate()<=n.startDate())&&r&&t.inArray(r.uid,o)===-1){s=!!r;break}d+=c}return r},moveToEvent:function(t,e){var n,o,i,s,r,l,a=t.groupIndex,u=this.groups[a],c=u.ranges(t.start,t.end,t.isAllDay,!1)[0].start,h=this.groups.length,d=e?-1:1,f=t.events;if(this._isGroupedByDate())if(o=this._getAllEvents(),i=this._getUniqueEvents(o),s=this._getSortedEvents(i),0===f.length)r=this._getNextEventIndexBySlot(c,s,a),e&&r--,n=s[r];else for(l=this._getStartIdx(f,s);l<s.length&&l>-1&&(f.length>0&&(c=this._getSelectedSlot(c,s,n,l,d,e)),c);){if((!e&&s[l].start.startDate()>=c.startDate()||e&&s[l].start.startDate()<=c.startDate())&&f[0]!=s[l].uid){n=s[l];break}l+=d}else for(;a<h&&a>-1&&(n=this.moveToEventInGroup(u,c,f,e),a+=d,u=this.groups[a],u&&!n);)f=[],c=e?u.lastSlot():u.firstSlot(!0);return n&&(t.events=[n.uid],t.start=n.start.startDate(),t.end=n.end.endDate(),t.isAllDay=n.start.isDaySlot,t.groupIndex=n.start.groupIndex),!!n},current:function(t){return void 0===t?this._current:(this._current=t,void(this.content.has(t)&&this._scrollTo(t,this.content[0])))},select:function(t){this.clearSelection(),this._selectEvents(t)||this._selectSlots(t)},_getNextEventIndexBySlot:function(t,e,n){var o,i,s=0,r=b.date.getDate(t.startDate());for(o=0;o<e.length;o++)if(i=b.date.getDate(e[o].start.startDate()),r>i)s++;else if(r.getTime()===i.getTime()&&n>e[o].start.groupIndex)s++;else{if(!(r.getTime()===i.getTime()&&n>=e[o].start.groupIndex&&t.startDate()>e[o].start.startDate()))break;s++}return s},_getSelectedSlot:function(t,e,n,o,i,s){var r,l;return e[o+i]&&e[o].start.groupIndex!==e[o+i].start.groupIndex&&(r=e[o+i].start.groupIndex,l=this.groups[r],l&&!n||(t=null),t=s?l.lastSlot():l.firstSlot(!0)),t},_getStartIdx:function(e,n){var o=0;return t.each(n,function(){return this.uid!==e[0]&&void o++}),o},_getAllEvents:function(){var t,e=[],n=this.groups;for(t=0;t<n.length;t++)n[t]._continuousEvents&&(e=e.concat(n[t]._continuousEvents));return e},_getUniqueEvents:function(t){var e,n,o,i=[];for(e=0;e<t.length;e++){for(n=!1,o=0;o<i.length;o++)if(t[e].uid===i[o].uid){n=!0;break}n||i.push(t[e])}return i},_getSortedEvents:function(e){return e.sort(function(e,n){var o=e.start.startDate(),i=n.start.startDate(),s=b.date.getDate(o)-b.date.getDate(i);return 0===s&&(s=e.start.groupIndex-n.start.groupIndex),0===s&&(s=o.getTime()-i.getTime()),0===s&&(e.start.isDaySlot&&!n.start.isDaySlot&&(s=-1),!e.start.isDaySlot&&n.start.isDaySlot&&(s=1)),0===s&&(s=t(e.element).index()-t(n.element).index()),s})},_selectSlots:function(t){var e,n,o,i,s,r,l,a=t.isAllDay,u=this.groups[t.groupIndex];for(u.timeSlotCollectionCount()||(a=!0),this._selectedSlots=[],e=u.ranges(t.start,t.end,a,!1),i=0;i<e.length;i++)for(s=e[i],r=s.collection,l=s.start.index;l<=s.end.index;l++)o=r.at(l),n=o.element,n.setAttribute("aria-selected",!0),S(n),this._selectedSlots.push({start:o.startDate(),end:o.endDate(),element:n});t.backward&&(n=e[0].start.element),this.current(n)},_selectEvents:function(e){var n,o,i,s=!1,r=e.events,l=this._getAllEvents(),a=l.length;if(!r[0]||!l[0])return s;for(i=t(),e.events=[],n=0;n<a;n++)t.inArray(l[n].uid,r)>-1&&(o=l[n],i=i.add(o.element),e.events.indexOf(o.uid)===-1&&e.events.push(o.uid));return i[0]&&(i.addClass("k-state-selected").attr("aria-selected",!0),this.current(i.last()[0]),this._selectedSlots=[],s=!0),s},inRange:function(t){var e=this.startDate(),n=b.date.addDays(this.endDate(),1),o=t.start,i=t.end;return e<=o&&o<n&&e<i&&i<=n},_resourceValue:function(t,e){return t.valuePrimitive&&(e=b.getter(t.dataValueField)(e)),e},_resourceBySlot:function(t){var e,n,o,i,s,r=this.groupedResources,l={};if(r.length)for(e=t.groupIndex,n=r.length-1;n>=0;n--)o=r[n],i=this._resourceValue(o,o.dataSource.view()[e%o.dataSource.total()]),o.multiple&&(i=[i]),s=b.setter(o.field),s(l,i),e=Math.floor(e/o.dataSource.total());return l},_createResizeHint:function(e,n,o,i){return t(E).css({left:e,top:n,width:o,height:i})},_removeResizeHint:function(){this._resizeHint.remove(),this._resizeHint=t()},_removeMoveHint:function(e){e?(this._moveHint.filter("[data-uid='"+e+"']").remove(),this._moveHint=this._moveHint.filter("[data-uid!='"+e+"']")):(this._moveHint.remove(),this._moveHint=t())},_scrollTo:function(t,e){var n=t.offsetTop,o=t.offsetHeight,i=e.scrollTop,s=e.clientHeight,r=n+o,l=0;l=i>n?n:r>i+s?o<=s?r-s:n:i,e.scrollTop=l},_inverseEventColor:function(t){var e=t.css("color"),n=new C(e).isDark(),o=t.css("background-color"),i=new C(o).isDark();n==i&&t.addClass(R)},_eventTmpl:function(e,n){var o,i=this.options,s=t.extend({},b.Template,i.templateSettings),r=s.paramName,l="",a=typeof e,u={storage:{},count:0};return"function"===a?(u.storage["tmpl"+u.count]=e,l+="#=this.tmpl"+u.count+"("+r+")#",u.count++):"string"===a&&(l+=e),o=b.template(b.format(n,l),s),u.count>0&&(o=t.proxy(o,u.storage)),o},eventResources:function(t){var e,n,o,i,s,r,l,a,u,c,h=[],d=this.options;if(!d.resources)return h;for(e=0;e<d.resources.length;e++)if(n=d.resources[e],o=n.field,i=b.getter(o)(t),null!=i)for(n.multiple||(i=[i]),s=n.dataSource.view(),r=0;r<i.length;r++){for(l=null,a=i[r],n.valuePrimitive||(a=b.getter(n.dataValueField)(a)),u=0;u<s.length;u++)if(s[u].get(n.dataValueField)==a){l=s[u];break}null!==l&&(c=b.getter(n.dataColorField)(l),h.push({field:n.field,title:n.title,name:n.name,text:b.getter(n.dataTextField)(l),value:a,color:c}))}return h},createLayout:function(o){var i,s,r,l,a,u=-1;for(o.rows||(o.rows=[]),i=0;i<o.rows.length;i++)if(o.rows[i].allDay){u=i;break}s=o.rows[u],u>=0&&o.rows.splice(u,1),r=this.columnLevels=e(o,"columns"),l=this.rowLevels=e(o,"rows"),this.table=t("<table "+n()+' class="k-scheduler-layout k-scheduler-'+this.name+'view"><tbody></tbody></table>'),a=l[l.length-1].length,this.table.find("tbody:first").append(this._topSection(r,s,a)),this.table.find("tbody:first").append(this._bottomSection(r,l,a)),this.element.append(this.table),this._isMobile()&&r.length>1&&"horizontal"===this._groupOrientation()&&b._outerWidth(t(window))<L&&(this.table.find(".k-scheduler-content .k-scheduler-table").width(100*r[r.length-2].length+"%"),this.table.find(".k-scheduler-header .k-scheduler-table").width(100*r[r.length-2].length+"%")),this._scroller()},refreshLayout:function(){var e,n,o,i,s,r,l=this,a=l.element.find(">.k-scheduler-toolbar"),u=l.element.innerHeight(),c=this._scrollbar,h=0,d=this._isRtl?"left":"right";for(e=0;e<a.length;e++)u-=I(a.eq(e));l.datesHeader&&(h=I(l.datesHeader)),l.timesHeader&&I(l.timesHeader)>h&&(h=I(l.timesHeader)),l.datesHeader&&l.timesHeader&&(n=l.datesHeader.find("table:first tr"),l.timesHeader.find("tr").height(function(e){t(this).height(n.eq(e).height())})),h&&(u-=h),l.footer&&(u-=I(l.footer)),o=function(t){var e,n;return!!t[0].style.height||(e=t.height(),t.height("auto"),n=t.height(),e!=n?(t.height(""),!0):(t.height(""),!1))},i=l.content[0],s=b.support.kineticScrollNeeded?0:c,o(l.element)&&(l.content.height(u>2*c?u:2*c+1),l.times.height(i.clientHeight),r=l.times.find("table"),r.length&&r.height(l.content.find("table")[0].clientHeight)),i.offsetWidth-i.clientWidth>0?(l.table.addClass("k-scrollbar-v"),l.datesHeader.css("padding-"+d,s-parseInt(l.datesHeader.children().css("border-"+d+"-width"),10))):l.datesHeader.css("padding-"+d,""),i.offsetHeight-i.clientHeight>0||i.clientHeight>l.content.children(".k-scheduler-table").height()?l.table.addClass("k-scrollbar-h"):l.table.removeClass("k-scrollbar-h")},_topSection:function(e,n,o){var i,l=e[e.length-1].length;return this.timesHeader=s(e.length,n,o),this.datesHeader=r(e,l,n),i="<tr "+(this._isMobile()?"class='k-mobile-header'":"")+">",t(i).append(this.timesHeader.add(this.datesHeader).wrap("<td>").parent())},_bottomSection:function(e,n,o){return this.times=l(n,o,this._isMobile()),this.content=a(e[e.length-1],n[n.length-1]),t("<tr>").append(this.times.add(this.content).wrap("<td>").parent())},_scroller:function(){var e,n=this;this.content.bind("scroll"+T,function(){n.datesHeader.find(">.k-scheduler-header-wrap").scrollLeft(this.scrollLeft),n.times.scrollTop(this.scrollTop)}),e=b.touchScroller(this.content,{avoidScrolling:function(e){return t(e.event.target).closest(".k-event.k-event-active").length>0}}),e&&e.movable&&(this._touchScroller=e,this.content=e.scrollElement,e.movable.bind("change",function(t){n.datesHeader.find(">.k-scheduler-header-wrap").scrollLeft(-t.sender.x),n.times.scrollTop(-t.sender.y)}))},_resourcesForGroups:function(){var t,e,n,o,i=[],s=this.options.group,r=this.options.resources;if(s=s&&s.resources?s.resources:[],r&&s.length)for(t=0,e=r.length;t<e;t++)for(n=0,o=s.length;n<o;n++)r[t].name===s[n]&&i.push(r[t]);this.groupedResources=i},_createDateLayout:function(t,e,n){return _("rows",t,e,n)},_createColumnsLayout:function(t,e,n,o,i){return v("columns",t,e,n,o,i)},_groupOrientation:function(){var t=this.options.group;return t&&t.resources?t.orientation:"horizontal"},_isGroupedByDate:function(){return this.options.group&&this.options.group.date},_isVerticallyGrouped:function(){return this.groupedResources.length&&"vertical"===this._groupOrientation()},_createRowsLayout:function(t,e,n,o){return v("rows",t,e,n,o)},selectionByElement:function(){return null},clearSelection:function(){this.content.find(".k-state-selected").removeAttr("id").attr("aria-selected",!1).removeClass("k-state-selected")},destroy:function(){var t=this;w.fn.destroy.call(this),t.table&&(b.destroy(t.table),t.table.remove()),t.footer&&(b.destroy(t.footer),t.footer.remove()),t.groups=null,t.table=null,t.content=null,t.times=null,t.datesHeader=null,t.timesHeader=null,t.footer=null,t._resizeHint=null,t._moveHint=null},calendarInfo:function(){return b.getCulture().calendars.standard},prevGroupSlot:function(t,e,n){var o,i=this.groups[e],s=i.ranges(t,t,n,!1)[0].start;if(!(e<=0))return this._isGroupedByDate()?s:this._isVerticallyGrouped()?i.timeSlotCollectionCount()?(o=i._collection(n?s.index:s.collectionIndex,!1),o.last()):(o=i._collection(i.daySlotCollectionCount()-1,!0),o.at(s.index)):i.timeSlotCollectionCount()?(o=i._collection(n?0:i.timeSlotCollectionCount()-1,n),n?o.last():o.at(s.index)):(o=i._collection(s.collectionIndex,!0),o.last())},nextGroupSlot:function(t,e,n){var o,i,s=this.groups[e],r=s.ranges(t,t,n,!1)[0].start;if(!(e>=this.groups.length-1))return this._isGroupedByDate()?r:this._isVerticallyGrouped()?s.timeSlotCollectionCount()?(i=s.daySlotCollectionCount(),o=s._collection(i?0:r.collectionIndex,i),n?o.first():o.at(r.collectionIndex)):(o=s._collection(0,!0),o.at(r.index)):s.timeSlotCollectionCount()?(o=s._collection(0,n),n?o.first():o.at(r.index)):(o=s._collection(r.collectionIndex,!0),o.first())},_eventOptionsForMove:function(){return{}},_updateEventForResize:function(){},_updateEventForSelection:function(t){return t}}),C=function(t){var e,n,o,i,s,r=this,l=C.formats;if(1===arguments.length)for(t=r.resolveColor(t),i=0;i<l.length;i++)e=l[i].re,n=l[i].process,o=e.exec(t),o&&(s=n(o),r.r=s[0],r.g=s[1],r.b=s[2]);else r.r=arguments[0],r.g=arguments[1],r.b=arguments[2];r.r=r.normalizeByte(r.r),r.g=r.normalizeByte(r.g),r.b=r.normalizeByte(r.b)},C.prototype={resolveColor:function(t){return t=t||"#000","#"==t.charAt(0)&&(t=t.substr(1,6)),t=t.replace(/ /g,""),t=t.toLowerCase(),t=C.namedColors[t]||t},normalizeByte:function(t){return t<0||isNaN(t)?0:t>255?255:t},percBrightness:function(){var t=this;return z.sqrt(.241*t.r*t.r+.691*t.g*t.g+.068*t.b*t.b)},isDark:function(){var t=this,e=t.percBrightness();return e<180}},C.formats=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,process:function(t){return[parseInt(t[1],10),parseInt(t[2],10),parseInt(t[3],10)]}},{re:/^(\w{2})(\w{2})(\w{2})$/,process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],C.namedColors={aqua:"00ffff",azure:"f0ffff",beige:"f5f5dc",black:"000000",blue:"0000ff",brown:"a52a2a",coral:"ff7f50",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgray:"a9a9a9",darkgreen:"006400",darkorange:"ff8c00",darkred:"8b0000",dimgray:"696969",fuchsia:"ff00ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lightblue:"add8e6",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightyellow:"ffffe0",
lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumblue:"0000cd",navy:"000080",olive:"808000",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",pink:"ffc0cb",plum:"dda0dd",purple:"800080",red:"ff0000",royalblue:"4169e1",salmon:"fa8072",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",steelblue:"4682b4",tan:"d2b48c",teal:"008080",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"},D=/\s*k-state-selected/,t.extend(x.SchedulerView,{createColumns:f,createRows:g,rangeIndex:h,collidingEvents:c,groupEqFilter:m})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(t,e,n){(n||e)()});
//# sourceMappingURL=kendo.scheduler.view.min.js.map
