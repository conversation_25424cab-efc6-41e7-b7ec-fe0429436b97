{"version": 3, "sources": ["kendo.selectable.js"], "names": ["f", "define", "$", "undefined", "collision", "element", "position", "is", "elementPosition", "kendo", "getOffset", "right", "left", "width", "bottom", "top", "height", "_outerWidth", "_outerHeight", "Selectable", "window", "Widget", "ui", "proxy", "abs", "Math", "ARIASELECTED", "SELECTED", "ACTIVE", "SELECTABLE", "CHANGE", "NS", "UNSELECT", "UNSELECTING", "INPUTSELECTOR", "msie", "support", "browser", "supportEventDelegation", "on", "find", "trigger", "end", "off", "extend", "init", "options", "multiple", "that", "this", "fn", "call", "_marquee", "_lastActive", "addClass", "relatedTarget", "aria", "attr", "userEvents", "UserEvents", "global", "allowSelection", "filter", "tap", "_tap", "touchAction", "bind", "_start", "_move", "_end", "_select", "events", "name", "inputSelectors", "noop", "_isElement", "target", "idx", "elements", "length", "result", "e", "selected", "ctrl<PERSON>ey", "event", "metaKey", "shift<PERSON>ey", "whichCode", "which", "buttonCode", "button", "closest", "_allowSelection", "hasClass", "clear", "add", "selectRange", "_firstSelectee", "_unselect", "_notify", "value", "_downTarget", "currentElement", "cancel", "useAllItems", "_items", "sender", "capture", "appendTo", "document", "body", "css", "x", "client", "y", "_selectElement", "startLocation", "location", "initialDelta", "_invalidateSelectables", "preventDefault", "remove", "removeClass", "related", "toSelect", "items", "eq", "val", "selectElement", "each", "preventNotify", "isPrevented", "args", "_activeElement", "resetTouchEvents", "start", "tmp", "continuousItems", "inArray", "destroy", "parseOptions", "selectable", "asLowerString", "toLowerCase", "indexOf", "cell", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,oBACH,aACA,oBACDD,IACL,WA2RE,MAhRC,UAAUE,EAAGC,GAqQV,QAASC,GAAUC,EAASC,GACxB,IAAKD,EAAQE,GAAG,YACZ,OAAO,CAEX,IAAIC,GAAkBC,EAAMC,UAAUL,GAAUM,EAAQL,EAASM,KAAON,EAASO,MAAOC,EAASR,EAASS,IAAMT,EAASU,MAGzH,OAFAR,GAAgBG,MAAQH,EAAgBI,KAAOH,EAAMQ,YAAYZ,GACjEG,EAAgBM,OAASN,EAAgBO,IAAMN,EAAMS,aAAab,KACzDG,EAAgBI,KAAOD,GAASH,EAAgBG,MAAQL,EAASM,MAAQJ,EAAgBO,IAAMD,GAAUN,EAAgBM,OAASR,EAASS,KA5Q3J,GASOI,GARAV,EAAQW,OAAOX,MAAOY,EAASZ,EAAMa,GAAGD,OAAQE,EAAQrB,EAAEqB,MAAOC,EAAMC,KAAKD,IAAKE,EAAe,gBAAiBC,EAAW,mBAAoBC,EAAS,oBAAqBC,EAAa,eAAgBC,EAAS,SAAUC,EAAK,mBAAoBC,EAAW,WAAYC,EAAc,sBAAuBC,EAAgB,qNAAsNC,EAAO1B,EAAM2B,QAAQC,QAAQF,KAAMG,GAAyB,GACxlB,SAAUpC,IACN,WACGA,EAAE,sCAAsCqC,GAAG,QAAS,KAAM,WACtDD,GAAyB,IAC1BE,KAAK,QAAQC,QAAQ,SAASC,MAAMC,UAE7CzC,GACEiB,EAAaE,EAAOuB,QACpBC,KAAM,SAAUxC,EAASyC,GACrB,GAAiBC,GAAbC,EAAOC,IACX5B,GAAO6B,GAAGL,KAAKM,KAAKH,EAAM3C,EAASyC,GACnCE,EAAKI,SAAWlD,EAAE,oEAClB8C,EAAKK,YAAc,KACnBL,EAAK3C,QAAQiD,SAASzB,GACtBmB,EAAKO,cAAgBP,EAAKF,QAAQS,cAClCR,EAAWC,EAAKF,QAAQC,SACpBE,KAAKH,QAAQU,MAAQT,GACrBC,EAAK3C,QAAQoD,KAAK,wBAAwB,GAE9CT,EAAKU,WAAa,GAAIjD,GAAMkD,WAAWX,EAAK3C,SACxCuD,QAAQ,EACRC,gBAAgB,EAChBC,QAAUxB,EAAkD,GAAzB,IAAMT,EAAa,KAAYmB,EAAKF,QAAQgB,OAC/EC,IAAKxC,EAAMyB,EAAKgB,KAAMhB,GACtBiB,YAAalB,EAAW,OAAS,gBAEjCA,GACAC,EAAKU,WAAWQ,KAAK,QAAS3C,EAAMyB,EAAKmB,OAAQnB,IAAOkB,KAAK,OAAQ3C,EAAMyB,EAAKoB,MAAOpB,IAAOkB,KAAK,MAAO3C,EAAMyB,EAAKqB,KAAMrB,IAAOkB,KAAK,SAAU3C,EAAMyB,EAAKsB,QAAStB,KAG7KuB,QACIzC,EACAE,GAEJc,SACI0B,KAAM,aACNV,OAAQ,KACRW,eAAgBvC,EAChBa,UAAU,EACVQ,cAAerD,EAAEwE,MAErBC,WAAY,SAAUC,GAAV,GAEJC,GADAC,EAAW7B,KAAK5C,QACX0E,EAASD,EAASC,OAAQC,GAAS,CAE5C,KADAJ,EAASA,EAAO,GACXC,EAAM,EAAGA,EAAME,EAAQF,IACxB,GAAIC,EAASD,KAASD,EAAQ,CAC1BI,GAAS,CACT,OAGR,MAAOA,IAEXhB,KAAM,SAAUiB,GACZ,GAAgKC,GAA5JN,EAAS1E,EAAE+E,EAAEL,QAAS5B,EAAOC,KAAMkC,EAAUF,EAAEG,MAAMD,SAAWF,EAAEG,MAAMC,QAAStC,EAAWC,EAAKF,QAAQC,SAAUuC,EAAWvC,GAAYkC,EAAEG,MAAME,SAAoBC,EAAYN,EAAEG,MAAMI,MAAOC,EAAaR,EAAEG,MAAMM,QACrN1C,EAAK2B,WAAWC,EAAOe,QAAQ,IAAM9D,KAAgB0D,GAA0B,GAAbA,GAAkBE,GAA4B,GAAdA,GAGlGxC,KAAK2C,gBAAgBX,EAAEG,MAAMR,UAGlCM,EAAWN,EAAOiB,SAASlE,GACtBoB,GAAaoC,GACdnC,EAAK8C,QAETlB,EAASA,EAAOmB,IAAI/C,EAAKO,cAAcqB,IACnCU,EACAtC,EAAKgD,YAAYhD,EAAKiD,iBAAkBrB,EAAQK,IAE5CC,GAAYC,GACZnC,EAAKkD,UAAUtB,GACf5B,EAAKmD,QAAQrE,EAAQmD,IAErBjC,EAAKoD,MAAMxB,EAAQK,GAEvBjC,EAAKK,YAAcL,EAAKqD,YAAczB,KAG9CT,OAAQ,SAAUc,GACd,GAA6EqB,GAAzEtD,EAAOC,KAAM2B,EAAS1E,EAAE+E,EAAEL,QAASM,EAAWN,EAAOiB,SAASlE,GAA2BwD,EAAUF,EAAEG,MAAMD,SAAWF,EAAEG,MAAMC,OAClI,IAAKpC,KAAK2C,gBAAgBX,EAAEG,MAAMR,QAAlC,CAIA,GADA5B,EAAKqD,YAAczB,GACd5B,EAAK2B,WAAWC,EAAOe,QAAQ,IAAM9D,IAEtC,MADAmB,GAAKU,WAAW6C,SAChB,CAEAvD,GAAKF,QAAQ0D,YACbxD,EAAKyD,OAASzD,EAAK3C,QAAQmC,KAAKQ,EAAKF,QAAQgB,SAE7CwC,EAAiB1B,EAAOe,QAAQ3C,EAAK3C,SACrC2C,EAAKyD,OAASH,EAAe9D,KAAKQ,EAAKF,QAAQgB,SAEnDmB,EAAEyB,OAAOC,UACT3D,EAAKI,SAASwD,SAASC,SAASC,MAAMC,KAClCnG,KAAMqE,EAAE+B,EAAEC,OAAS,EACnBlG,IAAKkE,EAAEiC,EAAED,OAAS,EAClBpG,MAAO,EACPG,OAAQ,IAEPmE,GACDnC,EAAK8C,QAETlB,EAASA,EAAOmB,IAAI/C,EAAKO,cAAcqB,IACnCM,IACAlC,EAAKmE,eAAevC,GAAQ,GACxBO,GACAP,EAAOtB,SAASrB,MAI5BmC,MAAO,SAAUa,GACb,GAAIjC,GAAOC,KAAM3C,GACTM,KAAMqE,EAAE+B,EAAEI,cAAgBnC,EAAE+B,EAAEK,SAAWpC,EAAE+B,EAAEK,SAAWpC,EAAE+B,EAAEI,cAC5DrG,IAAKkE,EAAEiC,EAAEE,cAAgBnC,EAAEiC,EAAEG,SAAWpC,EAAEiC,EAAEG,SAAWpC,EAAEiC,EAAEE,cAC3DvG,MAAOW,EAAIyD,EAAE+B,EAAEM,cACftG,OAAQQ,EAAIyD,EAAEiC,EAAEI,cAExBtE,GAAKI,SAAS2D,IAAIzG,GAClB0C,EAAKuE,uBAAuBjH,EAAU2E,EAAEG,MAAMD,SAAWF,EAAEG,MAAMC,SACjEJ,EAAEuC,kBAENnD,KAAM,SAAUY,GAAV,GAIEL,GAHA5B,EAAOC,IACXD,GAAKI,SAASqE,SACdzE,EAAKkD,UAAUlD,EAAK3C,QAAQmC,KAAKQ,EAAKF,QAAQgB,OAAS,IAAM7B,IAAcyF,YAAYzF,GACnF2C,EAAS5B,EAAK3C,QAAQmC,KAAKQ,EAAKF,QAAQgB,OAAS,IAAMlC,GAC3DgD,EAASA,EAAOmB,IAAI/C,EAAKO,cAAcqB,IACvC5B,EAAKoD,MAAMxB,EAAQK,GACnBjC,EAAKK,YAAcL,EAAKqD,YACxBrD,EAAKyD,OAAS,MAElBc,uBAAwB,SAAUjH,EAAU6E,GACxC,GAAIN,GAAKE,EAA2D4C,EAASC,EAA5DhD,EAAS3B,KAAKoD,YAAY,GAAIwB,EAAQ5E,KAAKwD,MAC5D,KAAK5B,EAAM,EAAGE,EAAS8C,EAAM9C,OAAQF,EAAME,EAAQF,IAC/C+C,EAAWC,EAAMC,GAAGjD,GACpB8C,EAAUC,EAAS7B,IAAI9C,KAAKM,cAAcqE,IACtCxH,EAAUwH,EAAUtH,GAChBsH,EAAS/B,SAASlE,GACdwD,GAAWP,IAAWgD,EAAS,IAC/BD,EAAQD,YAAY/F,GAAU2B,SAASrB,GAEnC2F,EAAS/B,SAASjE,IAAYgG,EAAS/B,SAAS5D,IACxD0F,EAAQrE,SAAS1B,GAGjBgG,EAAS/B,SAASjE,GAClB+F,EAAQD,YAAY9F,GACbuD,GAAWyC,EAAS/B,SAAS5D,IACpC0F,EAAQD,YAAYzF,GAAaqB,SAAS3B,IAK1DyE,MAAO,SAAU2B,EAAK9C,GAClB,GAAIjC,GAAOC,KAAM+E,EAAgBzG,EAAMyB,EAAKmE,eAAgBnE,EAC5D,OAAI+E,IACAA,EAAIE,KAAK,WACLD,EAAc/E,QAElBD,EAAKmD,QAAQrE,EAAQmD,GACrB,GAEGjC,EAAK3C,QAAQmC,KAAKQ,EAAKF,QAAQgB,OAAS,IAAMnC,IAEzDsE,eAAgB,WACZ,GAAiBf,GAAblC,EAAOC,IACX,OAAyB,QAArBD,EAAKK,YACEL,EAAKK,aAEhB6B,EAAWlC,EAAKoD,QACTlB,EAASH,OAAS,EAAIG,EAAS,GAAKlC,EAAK3C,QAAQmC,KAAKQ,EAAKF,QAAQgB,QAAQ,KAEtFqD,eAAgB,SAAU9G,EAAS6H,GAC/B,GAAIN,GAAW1H,EAAEG,GAAU8H,GAAeD,GAAiBjF,KAAKkD,QAAQ,UAAY9F,QAASA,GAC7FuH,GAASF,YAAY9F,GAChBuG,IACDP,EAAStE,SAAS3B,GACdsB,KAAKH,QAAQU,MACboE,EAASnE,KAAK/B,GAAc,KAIxCyE,QAAS,SAAU3B,EAAM4D,GAErB,MADAA,GAAOA,MACAnF,KAAKR,QAAQ+B,EAAM4D,IAE9BlC,UAAW,SAAU7F,GACjB,IAAI4C,KAAKR,QAAQT,GAAY3B,QAASA,IAOtC,MAJAA,GAAQqH,YAAY/F,GAChBsB,KAAKH,QAAQU,MACbnD,EAAQoD,KAAK/B,GAAc,GAExBrB,GAEXiE,QAAS,SAAUW,GACXhC,KAAK2C,gBAAgBX,EAAEG,MAAMR,WACxBzC,GAAQA,IAASjC,EAAEO,EAAM4H,kBAAkB9H,GAAG0C,KAAKH,QAAQ2B,kBAC5DQ,EAAEuC,kBAId5B,gBAAiB,SAAUhB,GACvB,OAAI1E,EAAE0E,GAAQrE,GAAG0C,KAAKH,QAAQ2B,kBAC1BxB,KAAKS,WAAW6C,SAChBtD,KAAKoD,YAAc,MACZ,IAIfiC,iBAAkB,WACdrF,KAAKS,WAAW6C,UAEpBT,MAAO,WACH,GAAI+B,GAAQ5E,KAAK5C,QAAQmC,KAAKS,KAAKH,QAAQgB,OAAS,IAAMnC,EAC1DsB,MAAKiD,UAAU2B,IAEnB7B,YAAa,SAAUuC,EAAO7F,EAAKuC,GAC/B,GAAiBJ,GAAK2D,EAAKX,EAAvB7E,EAAOC,IAkBX,KAjBAD,EAAK8C,QACD9C,EAAK3C,QAAQ0E,OAAS,IACtB8C,EAAQ7E,EAAKF,QAAQ2F,mBAEpBZ,GAAUA,EAAM9C,SACjB8C,EAAQ7E,EAAK3C,QAAQmC,KAAKQ,EAAKF,QAAQgB,SAE3CyE,EAAQrI,EAAEwI,QAAQxI,EAAEqI,GAAO,GAAIV,GAC/BnF,EAAMxC,EAAEwI,QAAQxI,EAAEwC,GAAK,GAAImF,GACvBU,EAAQ7F,IACR8F,EAAMD,EACNA,EAAQ7F,EACRA,EAAM8F,GAELxF,EAAKF,QAAQ0D,cACd9D,GAAOM,EAAK3C,QAAQ0E,OAAS,GAE5BF,EAAM0D,EAAO1D,GAAOnC,EAAKmC,IAC1B7B,EAAKmE,eAAeU,EAAMhD,GAE9B7B,GAAKmD,QAAQrE,EAAQmD,IAEzB0D,QAAS,WACL,GAAI3F,GAAOC,IACX5B,GAAO6B,GAAGyF,QAAQxF,KAAKH,GACvBA,EAAK3C,QAAQsC,IAAIZ,GACjBiB,EAAKU,WAAWiF,UAChB3F,EAAKI,SAAWJ,EAAKK,YAAcL,EAAK3C,QAAU2C,EAAKU,WAAa,QAG5EvC,EAAWyH,aAAe,SAAUC,GAChC,GAAIC,GAAsC,gBAAfD,IAA2BA,EAAWE,aACjE,QACIhG,SAAU+F,GAAiBA,EAAcE,QAAQ,eACjDC,KAAMH,GAAiBA,EAAcE,QAAQ,aAYrDvI,EAAMa,GAAG4H,OAAO/H,IAClBC,OAAOX,MAAM0I,QACR/H,OAAOX,OACE,kBAAVR,SAAwBA,OAAOmJ,IAAMnJ,OAAS,SAAUoJ,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.selectable.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.selectable', [\n        'kendo.core',\n        'kendo.userevents'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'selectable',\n        name: 'Selectable',\n        category: 'framework',\n        depends: [\n            'core',\n            'userevents'\n        ],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Widget = kendo.ui.Widget, proxy = $.proxy, abs = Math.abs, ARIASELECTED = 'aria-selected', SELECTED = 'k-state-selected', ACTIVE = 'k-state-selecting', SELECTABLE = 'k-selectable', CHANGE = 'change', NS = '.kendoSelectable', UNSELECT = 'unselect', UNSELECTING = 'k-state-unselecting', INPUTSELECTOR = 'input,a,textarea,.k-multiselect-wrap,select,button,.k-button>span,.k-button>img,span.k-icon.k-i-arrow-60-down,span.k-icon.k-i-arrow-60-up,label.k-checkbox-label.k-no-text,.k-icon.k-i-collapse,.k-icon.k-i-expand', msie = kendo.support.browser.msie, supportEventDelegation = false;\n        (function ($) {\n            (function () {\n                $('<div class=\"parent\"><span /></div>').on('click', '>*', function () {\n                    supportEventDelegation = true;\n                }).find('span').trigger('click').end().off();\n            }());\n        }($));\n        var Selectable = Widget.extend({\n            init: function (element, options) {\n                var that = this, multiple;\n                Widget.fn.init.call(that, element, options);\n                that._marquee = $('<div class=\\'k-marquee\\'><div class=\\'k-marquee-color\\'></div></div>');\n                that._lastActive = null;\n                that.element.addClass(SELECTABLE);\n                that.relatedTarget = that.options.relatedTarget;\n                multiple = that.options.multiple;\n                if (this.options.aria && multiple) {\n                    that.element.attr('aria-multiselectable', true);\n                }\n                that.userEvents = new kendo.UserEvents(that.element, {\n                    global: true,\n                    allowSelection: true,\n                    filter: (!supportEventDelegation ? '.' + SELECTABLE + ' ' : '') + that.options.filter,\n                    tap: proxy(that._tap, that),\n                    touchAction: multiple ? 'none' : 'pan-x pan-y'\n                });\n                if (multiple) {\n                    that.userEvents.bind('start', proxy(that._start, that)).bind('move', proxy(that._move, that)).bind('end', proxy(that._end, that)).bind('select', proxy(that._select, that));\n                }\n            },\n            events: [\n                CHANGE,\n                UNSELECT\n            ],\n            options: {\n                name: 'Selectable',\n                filter: '>*',\n                inputSelectors: INPUTSELECTOR,\n                multiple: false,\n                relatedTarget: $.noop\n            },\n            _isElement: function (target) {\n                var elements = this.element;\n                var idx, length = elements.length, result = false;\n                target = target[0];\n                for (idx = 0; idx < length; idx++) {\n                    if (elements[idx] === target) {\n                        result = true;\n                        break;\n                    }\n                }\n                return result;\n            },\n            _tap: function (e) {\n                var target = $(e.target), that = this, ctrlKey = e.event.ctrlKey || e.event.metaKey, multiple = that.options.multiple, shiftKey = multiple && e.event.shiftKey, selected, whichCode = e.event.which, buttonCode = e.event.button;\n                if (!that._isElement(target.closest('.' + SELECTABLE)) || whichCode && whichCode == 3 || buttonCode && buttonCode == 2) {\n                    return;\n                }\n                if (!this._allowSelection(e.event.target)) {\n                    return;\n                }\n                selected = target.hasClass(SELECTED);\n                if (!multiple || !ctrlKey) {\n                    that.clear();\n                }\n                target = target.add(that.relatedTarget(target));\n                if (shiftKey) {\n                    that.selectRange(that._firstSelectee(), target, e);\n                } else {\n                    if (selected && ctrlKey) {\n                        that._unselect(target);\n                        that._notify(CHANGE, e);\n                    } else {\n                        that.value(target, e);\n                    }\n                    that._lastActive = that._downTarget = target;\n                }\n            },\n            _start: function (e) {\n                var that = this, target = $(e.target), selected = target.hasClass(SELECTED), currentElement, ctrlKey = e.event.ctrlKey || e.event.metaKey;\n                if (!this._allowSelection(e.event.target)) {\n                    return;\n                }\n                that._downTarget = target;\n                if (!that._isElement(target.closest('.' + SELECTABLE))) {\n                    that.userEvents.cancel();\n                    return;\n                }\n                if (that.options.useAllItems) {\n                    that._items = that.element.find(that.options.filter);\n                } else {\n                    currentElement = target.closest(that.element);\n                    that._items = currentElement.find(that.options.filter);\n                }\n                e.sender.capture();\n                that._marquee.appendTo(document.body).css({\n                    left: e.x.client + 1,\n                    top: e.y.client + 1,\n                    width: 0,\n                    height: 0\n                });\n                if (!ctrlKey) {\n                    that.clear();\n                }\n                target = target.add(that.relatedTarget(target));\n                if (selected) {\n                    that._selectElement(target, true);\n                    if (ctrlKey) {\n                        target.addClass(UNSELECTING);\n                    }\n                }\n            },\n            _move: function (e) {\n                var that = this, position = {\n                        left: e.x.startLocation > e.x.location ? e.x.location : e.x.startLocation,\n                        top: e.y.startLocation > e.y.location ? e.y.location : e.y.startLocation,\n                        width: abs(e.x.initialDelta),\n                        height: abs(e.y.initialDelta)\n                    };\n                that._marquee.css(position);\n                that._invalidateSelectables(position, e.event.ctrlKey || e.event.metaKey);\n                e.preventDefault();\n            },\n            _end: function (e) {\n                var that = this;\n                that._marquee.remove();\n                that._unselect(that.element.find(that.options.filter + '.' + UNSELECTING)).removeClass(UNSELECTING);\n                var target = that.element.find(that.options.filter + '.' + ACTIVE);\n                target = target.add(that.relatedTarget(target));\n                that.value(target, e);\n                that._lastActive = that._downTarget;\n                that._items = null;\n            },\n            _invalidateSelectables: function (position, ctrlKey) {\n                var idx, length, target = this._downTarget[0], items = this._items, related, toSelect;\n                for (idx = 0, length = items.length; idx < length; idx++) {\n                    toSelect = items.eq(idx);\n                    related = toSelect.add(this.relatedTarget(toSelect));\n                    if (collision(toSelect, position)) {\n                        if (toSelect.hasClass(SELECTED)) {\n                            if (ctrlKey && target !== toSelect[0]) {\n                                related.removeClass(SELECTED).addClass(UNSELECTING);\n                            }\n                        } else if (!toSelect.hasClass(ACTIVE) && !toSelect.hasClass(UNSELECTING)) {\n                            related.addClass(ACTIVE);\n                        }\n                    } else {\n                        if (toSelect.hasClass(ACTIVE)) {\n                            related.removeClass(ACTIVE);\n                        } else if (ctrlKey && toSelect.hasClass(UNSELECTING)) {\n                            related.removeClass(UNSELECTING).addClass(SELECTED);\n                        }\n                    }\n                }\n            },\n            value: function (val, e) {\n                var that = this, selectElement = proxy(that._selectElement, that);\n                if (val) {\n                    val.each(function () {\n                        selectElement(this);\n                    });\n                    that._notify(CHANGE, e);\n                    return;\n                }\n                return that.element.find(that.options.filter + '.' + SELECTED);\n            },\n            _firstSelectee: function () {\n                var that = this, selected;\n                if (that._lastActive !== null) {\n                    return that._lastActive;\n                }\n                selected = that.value();\n                return selected.length > 0 ? selected[0] : that.element.find(that.options.filter)[0];\n            },\n            _selectElement: function (element, preventNotify) {\n                var toSelect = $(element), isPrevented = !preventNotify && this._notify('select', { element: element });\n                toSelect.removeClass(ACTIVE);\n                if (!isPrevented) {\n                    toSelect.addClass(SELECTED);\n                    if (this.options.aria) {\n                        toSelect.attr(ARIASELECTED, true);\n                    }\n                }\n            },\n            _notify: function (name, args) {\n                args = args || {};\n                return this.trigger(name, args);\n            },\n            _unselect: function (element) {\n                if (this.trigger(UNSELECT, { element: element })) {\n                    return;\n                }\n                element.removeClass(SELECTED);\n                if (this.options.aria) {\n                    element.attr(ARIASELECTED, false);\n                }\n                return element;\n            },\n            _select: function (e) {\n                if (this._allowSelection(e.event.target)) {\n                    if (!msie || msie && !$(kendo._activeElement()).is(this.options.inputSelectors)) {\n                        e.preventDefault();\n                    }\n                }\n            },\n            _allowSelection: function (target) {\n                if ($(target).is(this.options.inputSelectors)) {\n                    this.userEvents.cancel();\n                    this._downTarget = null;\n                    return false;\n                }\n                return true;\n            },\n            resetTouchEvents: function () {\n                this.userEvents.cancel();\n            },\n            clear: function () {\n                var items = this.element.find(this.options.filter + '.' + SELECTED);\n                this._unselect(items);\n            },\n            selectRange: function (start, end, e) {\n                var that = this, idx, tmp, items;\n                that.clear();\n                if (that.element.length > 1) {\n                    items = that.options.continuousItems();\n                }\n                if (!items || !items.length) {\n                    items = that.element.find(that.options.filter);\n                }\n                start = $.inArray($(start)[0], items);\n                end = $.inArray($(end)[0], items);\n                if (start > end) {\n                    tmp = start;\n                    start = end;\n                    end = tmp;\n                }\n                if (!that.options.useAllItems) {\n                    end += that.element.length - 1;\n                }\n                for (idx = start; idx <= end; idx++) {\n                    that._selectElement(items[idx]);\n                }\n                that._notify(CHANGE, e);\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                that.element.off(NS);\n                that.userEvents.destroy();\n                that._marquee = that._lastActive = that.element = that.userEvents = null;\n            }\n        });\n        Selectable.parseOptions = function (selectable) {\n            var asLowerString = typeof selectable === 'string' && selectable.toLowerCase();\n            return {\n                multiple: asLowerString && asLowerString.indexOf('multiple') > -1,\n                cell: asLowerString && asLowerString.indexOf('cell') > -1\n            };\n        };\n        function collision(element, position) {\n            if (!element.is(':visible')) {\n                return false;\n            }\n            var elementPosition = kendo.getOffset(element), right = position.left + position.width, bottom = position.top + position.height;\n            elementPosition.right = elementPosition.left + kendo._outerWidth(element);\n            elementPosition.bottom = elementPosition.top + kendo._outerHeight(element);\n            return !(elementPosition.left > right || elementPosition.right < position.left || elementPosition.top > bottom || elementPosition.bottom < position.top);\n        }\n        kendo.ui.plugin(Selectable);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}