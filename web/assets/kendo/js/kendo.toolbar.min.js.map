{"version": 3, "sources": ["kendo.toolbar.js"], "names": ["f", "define", "$", "undefined", "toggleActive", "e", "target", "is", "toggleClass", "STATE_ACTIVE", "type", "actionSheetWrap", "element", "hasClass", "closest", "addClass", "wrap", "parent", "preventClick", "length", "preventDefault", "findFocusableSibling", "dir", "getSibling", "fn", "next", "prev", "getter", "first", "last", "candidate", "call", "OVERFLOW_ANCHOR", "find", "components", "<PERSON><PERSON>", "<PERSON><PERSON>", "ToolBarButton", "OverflowButton", "ButtonGroup", "ToolBarButtonGroup", "OverflowButtonGroup", "ToolBarSplitButton", "OverflowSplitButton", "ToolBarSeparator", "OverflowSeparator", "ToolBarSpacer", "TemplateItem", "OverflowTemplateItem", "Group", "<PERSON><PERSON><PERSON><PERSON>", "kendo", "window", "Class", "Widget", "ui", "proxy", "isFunction", "keys", "outerWidth", "_outerWidth", "TOOLBAR", "BUTTON", "OVERFLOW_BUTTON", "TOGGLE_BUTTON", "BUTTON_GROUP", "SPLIT_BUTTON", "SEPARATOR", "SPACER_CLASS", "SPACER", "POPUP", "RESIZABLE_TOOLBAR", "STATE_DISABLED", "STATE_HIDDEN", "GROUP_START", "GROUP_END", "PRIMARY", "ICON", "ICON_PREFIX", "BUTTON_ICON", "BUTTON_ICON_TEXT", "LIST_CONTAINER", "SPLIT_BUTTON_ARROW", "OVERFLOW_CONTAINER", "FIRST_TOOLBAR_VISIBLE", "LAST_TOOLBAR_VISIBLE", "CLICK", "TOGGLE", "OPEN", "CLOSE", "OVERFLOW_OPEN", "OVERFLOW_CLOSE", "OVERFLOW_NEVER", "OVERFLOW_AUTO", "OVERFLOW_ALWAYS", "OVERFLOW_HIDDEN", "OPTION_LIST_SUFFIX", "KENDO_UID_ATTR", "attr", "toolbar", "overflowAnchor", "overflowContainer", "registerComponent", "name", "overflow", "extend", "addOverflowAttr", "this", "options", "addUidAttr", "uid", "addIdAttr", "id", "addOverflowIdAttr", "attributes", "show", "removeClass", "hidden", "hide", "overflowHidden", "remove", "enable", "isEnabled", "twin", "splitContainerId", "data", "resizable", "popup", "init", "useButtonTag", "primary", "togglable", "toggle", "selected", "url", "mobile", "group", "addToGroup", "click", "clickHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "propagate", "select", "getParentGroup", "<PERSON><PERSON><PERSON><PERSON>", "_addGraphics", "isEmpty", "span", "img", "icon", "spriteCssClass", "imageUrl", "contents", "filter", "each", "idx", "el", "nodeType", "trim", "nodeValue", "children", "prependTo", "align", "showText", "text", "html", "hasIcon", "showIcon", "button", "_wrap", "createButtons", "buttonConstructor", "item", "i", "items", "buttons", "guid", "appendTo", "refresh", "buttonGroup", "mainButton", "arrowButton", "popupElement", "removeAttr", "createMenuButtons", "createPopup", "_navigatable", "main", "splitButton", "kendoPopup", "that", "on", "li", "keyCode", "ESC", "TAB", "altKey", "UP", "focus", "DOWN", "SPACEBAR", "ENTER", "userEvents", "trigger", "HOME", "END", "menuButtons", "rootUid", "anchor", "isRtl", "_isRtl", "copyAnchorStyles", "animation", "open", "isDefaultPrevented", "adjustPopupWidth", "sender", "activate", "close", "width", "computedWidth", "css", "fontFamily", "min-width", "off", "destroy", "visible", "separator", "template", "j<PERSON><PERSON><PERSON>", "add", "index", "inArray", "splice", "tmp", "wrapper", "support", "_groups", "isMobile", "effects", "_renderOverflow", "overflowUserEvents", "UserEvents", "threshold", "allowSelection", "tap", "_toggleOverflow", "_resizeHandler", "onResize", "resize", "_shrink", "innerWidth", "document", "_buttonClick", "press", "release", "_toggleOverflowAnchor", "notify", "events", "groupName", "unbindResize", "tool", "overflowTool", "component", "itemClasses", "overflowTemplate", "container", "angular", "elements", "get", "_getItem", "toolbarItem", "overflowItem", "isResizable", "buttonGroupInstance", "getSelectedFromGroup", "checked", "horizontalDirection", "append", "Popup", "origin", "position", "parseFloat", "innerHeight", "isComplexTool", "hasVisibleChildren", "paddingEnd", "visibility", "splitContainer", "handler", "eventData", "url<PERSON>arget", "isSplitButtonArrow", "_toggle", "parents", "ev", "_keydown", "lastHasFocus", "firstHasFocus", "isOnlyOverflowAnchor", "lastItemNotOverflowAnchor", "isFirstTool", "prevFocusable", "isOverflowAnchor", "direction", "parentsUntil", "not", "shift<PERSON>ey", "_getPrevFocusable", "_preventNextFocus", "activeElement", "eq", "RIGHT", "_getNextElement", "LEFT", "itemIndex", "parentElement", "startIndex", "directionNumber", "searchIndex", "focusableItem", "elementToFocus", "prevElement", "prevElements", "prevAll", "_resize", "containerWidth", "_stretch", "_markVisibles", "_childrenWidth", "children<PERSON><PERSON>th", "Math", "ceil", "commandElement", "visibleCommands", "_hideItem", "hiddenCommands", "_showItem", "overflowItems", "toolbarItems", "visibleOverflowItems", "visibleToolbarItems", "plugin", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,iBACH,aACA,mBACA,eACDD,IACL,WA8yCE,MAtyCC,UAAUE,EAAGC,GAulBV,QAASC,GAAaC,GACbA,EAAEC,OAAOC,GAAG,qBACbF,EAAEC,OAAOE,YAAYC,EAAwB,SAAVJ,EAAEK,MAG7C,QAASC,GAAgBC,GAErB,MADAA,GAAUV,EAAEU,GACLA,EAAQC,SAAS,kBAAoBD,EAAQE,QAAQ,qBAAuBF,EAAQG,SAAS,4BAA4BC,KAAK,uFAAuFC,SAASD,KAAK,gDAAgDC,SAE9R,QAASC,GAAab,GACdH,EAAEG,EAAEC,QAAQQ,QAAQ,cAAcK,QAClCd,EAAEe,iBAGV,QAASC,GAAqBT,EAASU,GAAvC,GACQC,GAAqB,SAARD,EAAiBpB,EAAEsB,GAAGC,KAAOvB,EAAEsB,GAAGE,KAC/CC,EAAiB,SAARL,EAAiBpB,EAAEsB,GAAGI,MAAQ1B,EAAEsB,GAAGK,KAC5CC,EAAYP,EAAWQ,KAAKnB,EAChC,QAAKkB,EAAUX,QAAUP,EAAQL,GAAG,IAAMyB,GAC/BpB,EAEPkB,EAAUvB,GAAG,qBAAuBuB,EAAUX,OACvCW,EAEPA,EAAUG,KAAK,mBAAmBd,OAC3BQ,EAAOI,KAAKD,EAAUG,KAAK,oBAE/BZ,EAAqBS,EAAWR,GAlnB9C,GAGOY,GAUAC,EAuDAC,EAsFAC,EAwCAC,EAwDAC,EAuBAC,EAsBAC,EAwBAC,EAsJAC,EA+BAC,EAiBAC,EAqBAC,EAWAC,EAuBAC,EAwDAC,EAwBAC,EA3oBAC,EAAQC,OAAOD,MAAOE,EAAQF,EAAME,MAAOC,EAASH,EAAMI,GAAGD,OAAQE,EAAQtD,EAAEsD,MAAOC,EAAaN,EAAMM,WAAYC,EAAOP,EAAMO,KAAMC,EAAaR,EAAMS,YAAaC,EAAU,YAAaC,EAAS,WAAYC,EAAkB,oBAAqBC,EAAgB,kBAAmBC,EAAe,iBAAkBC,EAAe,iBAAkBC,EAAY,cAAeC,EAAe,WAAYC,EAAS,SAAUC,EAAQ,UAAWC,EAAoB,sBAAuB9D,EAAe,iBAAkB+D,EAAiB,mBAAoBC,EAAe,iBAAkBC,EAAc,gBAAiBC,EAAY,cAAeC,EAAU,YAAaC,EAAO,SAAUC,EAAc,OAAQC,EAAc,gBAAiBC,EAAmB,oBAAqBC,EAAiB,qCAAsCC,EAAqB,uBAAwBlD,EAAkB,oBAAqBmD,GAAqB,uBAAwBC,GAAwB,0BAA2BC,GAAuB,yBAA0BC,GAAQ,QAASC,GAAS,SAAUC,GAAO,OAAQC,GAAQ,QAASC,GAAgB,eAAgBC,GAAiB,gBAAiBC,GAAiB,QAASC,GAAgB,OAAQC,GAAkB,SAAUC,GAAkB,oBAAqBC,GAAqB,cAAeC,GAAiB9C,EAAM+C,KAAK,MACx2C/C,GAAMgD,WACFjE,GACAkE,eAAgB,8DAChBC,kBAAmB,2DAEvBlD,EAAMgD,QAAQG,kBAAoB,SAAUC,EAAMJ,EAASK,GACvDtE,EAAWqE,IACPJ,QAASA,EACTK,SAAUA,IAGdrE,EAAOgB,EAAME,MAAMoD,QACnBC,gBAAiB,WACbC,KAAK/F,QAAQsF,KAAK/C,EAAM+C,KAAK,YAAaS,KAAKC,QAAQJ,UAAYX,KAEvEgB,WAAY,WACRF,KAAK/F,QAAQsF,KAAKD,GAAgBU,KAAKC,QAAQE,MAEnDC,UAAW,WACHJ,KAAKC,QAAQI,IACbL,KAAK/F,QAAQsF,KAAK,KAAMS,KAAKC,QAAQI,KAG7CC,kBAAmB,WACXN,KAAKC,QAAQI,IACbL,KAAK/F,QAAQsF,KAAK,KAAMS,KAAKC,QAAQI,GAAK,cAGlDE,WAAY,WACJP,KAAKC,QAAQM,YACbP,KAAK/F,QAAQsF,KAAKS,KAAKC,QAAQM,aAGvCC,KAAM,WACFR,KAAK/F,QAAQwG,YAAY3C,GAAc0C,OACvCR,KAAKC,QAAQS,QAAS,GAE1BC,KAAM,WACFX,KAAK/F,QAAQG,SAAS0D,GAAc6C,OAChCX,KAAKH,UAAYG,KAAKY,gBACtBZ,KAAKY,iBAETZ,KAAKC,QAAQS,QAAS,GAE1BG,OAAQ,WACJb,KAAK/F,QAAQ4G,UAEjBC,OAAQ,SAAUC,GACVA,IAAcvH,IACduH,GAAY,GAEhBf,KAAK/F,QAAQJ,YAAYgE,GAAiBkD,GAC1Cf,KAAKC,QAAQa,OAASC,GAE1BC,KAAM,WACF,GAAIb,GAAMH,KAAK/F,QAAQsF,KAAKD,GAC5B,OAAIU,MAAKH,UAAYG,KAAKC,QAAQgB,iBACvB1H,EAAE,IAAMyG,KAAKC,QAAQgB,kBAAkB3F,KAAK,IAAMgE,GAAiB,KAAQa,EAAM,MAAOe,KAAKlB,KAAKC,QAAQlG,MAC1GiG,KAAKH,SACLG,KAAKR,QAAQvF,QAAQqB,KAAK,IAAMgE,GAAiB,KAAQa,EAAM,MAAOe,KAAKlB,KAAKC,QAAQlG,MACxFiG,KAAKR,QAAQS,QAAQkB,UACrBnB,KAAKR,QAAQ4B,MAAMnH,QAAQqB,KAAK,IAAMgE,GAAiB,KAAQa,EAAM,MAAOe,KAAKlB,KAAKC,QAAQlG,MADlG,KAKfyC,EAAMgD,QAAQhE,KAAOA,EACjBC,EAASD,EAAKsE,QACduB,KAAM,SAAUpB,EAAST,GACrB,GAAIvF,GAAiCV,EAAvB0G,EAAQqB,aAAiB,iCAAsC,4BAC7EtB,MAAK/F,QAAUA,EACf+F,KAAKC,QAAUA,EACfD,KAAKR,QAAUA,EACfQ,KAAKO,aACDN,EAAQsB,SACRtH,EAAQG,SAAS6D,GAEjBgC,EAAQuB,YACRvH,EAAQG,SAASiD,GACjB2C,KAAKyB,OAAOxB,EAAQyB,WAEpBzB,EAAQ0B,MAAQnI,GAAcyG,EAAQqB,eACtCrH,EAAQsF,KAAK,OAAQU,EAAQ0B,KACzB1B,EAAQ2B,QACR3H,EAAQsF,KAAK/C,EAAM+C,KAAK,QAAS,WAGrCU,EAAQ4B,QACR5H,EAAQsF,KAAK/C,EAAM+C,KAAK,SAAUU,EAAQ4B,OAC1C7B,KAAK6B,MAAQ7B,KAAKR,QAAQsC,WAAW9B,KAAMC,EAAQ4B,SAElD5B,EAAQuB,WAAavB,EAAQ8B,OAASjF,EAAWmD,EAAQ8B,SAC1D/B,KAAKgC,aAAe/B,EAAQ8B,OAE5B9B,EAAQuB,WAAavB,EAAQwB,QAAU3E,EAAWmD,EAAQwB,UAC1DzB,KAAKiC,cAAgBhC,EAAQwB,SAGrCA,OAAQ,SAAUS,EAAOC,GACrBD,IAAUA,EACNlC,KAAK6B,OAASK,EACdlC,KAAK6B,MAAMO,OAAOpC,MACVA,KAAK6B,OACb7B,KAAKoC,OAAOF,GAEZC,GAAanC,KAAKgB,QAClBhB,KAAKgB,OAAOS,OAAOS,IAG3BG,eAAgB,WACZ,GAAIrC,KAAKC,QAAQqC,QACb,MAAOtC,MAAK/F,QAAQE,QAAQ,IAAMmD,GAAc4D,KAAK,gBAG7DqB,aAAc,WACV,GAAsIC,GAASC,EAAMC,EAAjJzI,EAAU+F,KAAK/F,QAAS0I,EAAO3C,KAAKC,QAAQ0C,KAAMC,EAAiB5C,KAAKC,QAAQ2C,eAAgBC,EAAW7C,KAAKC,QAAQ4C,UACxHD,GAAkBC,GAAYF,KAC9BH,GAAU,EACVvI,EAAQ6I,WAAWC,OAAO,WACtB,OAAQxJ,EAAEyG,MAAM9F,SAAS,cAAgBX,EAAEyG,MAAM9F,SAASgE,KAAU3E,EAAEyG,MAAM9F,SAAS,aACtF8I,KAAK,SAAUC,EAAKC,IACA,GAAfA,EAAGC,UAAgC,GAAfD,EAAGC,UAAiB5J,EAAE6J,KAAKF,EAAGG,WAAW7I,OAAS,KACtEgI,GAAU,KAIdvI,EAAQG,SADRoI,EACiBpE,EAEAC,IAGrBsE,GACAF,EAAOxI,EAAQqJ,SAAS,QAAUpF,GAAMjD,QACnCwH,EAAK,KACNA,EAAOlJ,EAAE,gBAAkB2E,EAAO,aAAaqF,UAAUtJ,IAE7DwI,EAAKrI,SAAS+D,EAAcwE,IACrBC,GACPH,EAAOxI,EAAQqJ,SAAS,iBAAiBrI,QACpCwH,EAAK,KACNA,EAAOlJ,EAAE,yBAA2B2E,EAAO,aAAaqF,UAAUtJ,IAEtEwI,EAAKrI,SAASwI,IACPC,IACPH,EAAMzI,EAAQqJ,SAAS,eAAerI,QACjCyH,EAAI,KACLA,EAAMnJ,EAAE,sCAAsCgK,UAAUtJ,IAE5DyI,EAAInD,KAAK,MAAOsD,OAI5BrG,EAAMgD,QAAQ/D,OAASA,EACnBC,EAAgBD,EAAOqE,QACvBuB,KAAM,SAAUpB,EAAST,GACrB/D,EAAOZ,GAAGwG,KAAKjG,KAAK4E,KAAMC,EAAST,EACnC,IAAIvF,GAAU+F,KAAK/F,OACnBA,GAAQG,SAAS+C,GACjB6C,KAAKI,YACDH,EAAQuD,OACRvJ,EAAQG,SAAS,WAAa6F,EAAQuD,OAElB,YAApBvD,EAAQwD,UAA0BxD,EAAQyD,MAEtCzJ,EAAQ0J,KADR1D,EAAQ2B,OACK,yBAA2B3B,EAAQyD,KAAO,UAE1CzD,EAAQyD,MAG7BzD,EAAQ2D,QAA8B,YAApB3D,EAAQ4D,WAA2B5D,EAAQ0C,MAAQ1C,EAAQ2C,gBAAkB3C,EAAQ4C,UACnG5C,EAAQ2D,SACR5D,KAAKuC,eAETvC,KAAKE,aACLF,KAAKD,kBACLC,KAAKc,OAAOb,EAAQa,QAChBb,EAAQS,QACRV,KAAKW,OAETX,KAAK/F,QAAQiH,MACTnH,KAAM,SACN+J,OAAQ9D,QAGhBoC,OAAQ,SAAUV,GACVA,IAAalI,IACbkI,GAAW,GAEf1B,KAAK/F,QAAQJ,YAAYC,EAAc4H,GACvC1B,KAAKC,QAAQyB,SAAWA,KAGhClF,EAAMgD,QAAQ9D,cAAgBA,EAC1BC,EAAiBF,EAAOqE,QACxBuB,KAAM,SAAUpB,EAAST,GACrBQ,KAAKH,UAAW,EAChBpE,EAAOZ,GAAGwG,KAAKjG,KAAK4E,KAAMzG,EAAEuG,UAAWG,GAAUT,EACjD,IAAIvF,GAAU+F,KAAK/F,OACK,YAApBgG,EAAQwD,UAAyBxD,EAAQyD,MAErCzJ,EAAQ0J,KADR1D,EAAQ2B,OACK,yBAA2B3B,EAAQyD,KAAO,UAE1C,wBAA0BzD,EAAQyD,KAAO,WAG9DzD,EAAQ2D,QAA8B,WAApB3D,EAAQ4D,WAA0B5D,EAAQ0C,MAAQ1C,EAAQ2C,gBAAkB3C,EAAQ4C,UAClG5C,EAAQ2D,SACR5D,KAAKuC,eAEJtC,EAAQqC,SACTtC,KAAK+D,QAET/D,KAAKM,oBACLN,KAAKO,aACLP,KAAKE,aACLF,KAAKD,kBACLC,KAAKc,OAAOb,EAAQa,QACpB7G,EAAQG,SAASgD,EAAkB,IAAMD,GACrC8C,EAAQS,QACRV,KAAKW,OAELV,EAAQuB,WACRxB,KAAKyB,OAAOxB,EAAQyB,UAExB1B,KAAK/F,QAAQiH,MACTnH,KAAM,SACN+J,OAAQ9D,QAGhB+D,MAAO,WACH/D,KAAK/F,QAAU+F,KAAK/F,QAAQI,KAAK,aAAaC,UAElDsG,eAAgB,WACZZ,KAAK/F,QAAQG,SAASgF,KAE1BgD,OAAQ,SAAUV,GACVA,IAAalI,IACbkI,GAAW,GAEX1B,KAAKC,QAAQqC,QACbtC,KAAK/F,QAAQJ,YAAYC,EAAc4H,GAEvC1B,KAAK/F,QAAQqB,KAAK,aAAazB,YAAYC,EAAc4H,GAE7D1B,KAAKC,QAAQyB,SAAWA,KAGhClF,EAAMgD,QAAQ7D,eAAiBA,EAC/Ba,EAAMgD,QAAQG,kBAAkB,SAAUjE,EAAeC,GACrDC,EAAcJ,EAAKsE,QACnBkE,cAAe,SAAUC,GAAV,GAGPC,GACKC,EAHLlE,EAAUD,KAAKC,QACfmE,EAAQnE,EAAQoE,WAEpB,KAASF,EAAI,EAAGA,EAAIC,EAAM5J,OAAQ2J,IACzBC,EAAMD,GAAGhE,MACViE,EAAMD,GAAGhE,IAAM3D,EAAM8H,QAEzBJ,EAAO,GAAID,GAAkB1K,EAAEuG,QAC3B8B,OAAQ3B,EAAQ2B,OAChBU,SAAS,EACTvI,KAAM,UACPqK,EAAMD,IAAKnE,KAAKR,SACnB0E,EAAKjK,QAAQsK,SAASvE,KAAK/F,UAGnCuK,QAAS,WACLxE,KAAK/F,QAAQqJ,WAAWP,OAAO,UAAajF,EAAe,YAAa1D,SAAS2D,GACjFiC,KAAK/F,QAAQqJ,WAAWP,OAAO,UAAajF,EAAe,WAAY1D,SAAS4D,MAGxFxB,EAAMgD,QAAQ5D,YAAcA,EACxBC,EAAqBD,EAAYkE,QACjCuB,KAAM,SAAUpB,EAAST,GACrB,GAAIvF,GAAU+F,KAAK/F,QAAUV,EAAE,cAC/ByG,MAAKC,QAAUA,EACfD,KAAKR,QAAUA,EACfQ,KAAKI,YACDH,EAAQuD,OACRvJ,EAAQG,SAAS,WAAa6F,EAAQuD,OAE1CxD,KAAKgE,cAActI,GACnBsE,KAAKO,aACLP,KAAKE,aACLF,KAAKD,kBACLC,KAAKwE,UACLvK,EAAQG,SAASkD,GACjB0C,KAAK/F,QAAQiH,MACTnH,KAAM,cACN0K,YAAazE,UAIzBxD,EAAMgD,QAAQ3D,mBAAqBA,EAC/BC,EAAsBF,EAAYkE,QAClCuB,KAAM,SAAUpB,EAAST,GACrB,GAAIvF,GAAU+F,KAAK/F,QAAUV,EAAE,YAC/ByG,MAAKC,QAAUA,EACfD,KAAKR,QAAUA,EACfQ,KAAKH,UAAW,EAChBG,KAAKM,oBACLN,KAAKgE,cAAcrI,GACnBqE,KAAKO,aACLP,KAAKE,aACLF,KAAKD,kBACLC,KAAKwE,UACLvK,EAAQG,UAAU6F,EAAQ2B,OAAS,GAAKtE,GAAgB,qBACxD0C,KAAK/F,QAAQiH,MACTnH,KAAM,cACN0K,YAAazE,QAGrBY,eAAgB,WACZZ,KAAK/F,QAAQG,SAASgF,OAG9B5C,EAAMgD,QAAQ1D,oBAAsBA,EACpCU,EAAMgD,QAAQG,kBAAkB,cAAe9D,EAAoBC,GAC/DC,EAAqBP,EAAKsE,QAC1BuB,KAAM,SAAUpB,EAAST,GACrB,GAAIvF,GAAU+F,KAAK/F,QAAUV,EAAE,eAAiBgE,EAAe,wBAC/DyC,MAAKC,QAAUA,EACfD,KAAKR,QAAUA,EACfQ,KAAK0E,WAAa,GAAIhJ,GAAcnC,EAAEuG,UAAWG,GAAWS,QAAQ,IAAUlB,GAC9EQ,KAAK2E,YAAcpL,EAAE,aAAe4D,EAAS,IAAMoB,EAAqB,mBAAqB0B,EAAQ2B,OAAS,uBAAyB,4BAA8B,iBACrK5B,KAAK4E,aAAerL,EAAE,cAAgB+E,EAAiB,WACvD0B,KAAK0E,WAAWzK,QAAQ4K,WAAW,iBAAiBN,SAAStK,GAC7D+F,KAAK2E,YAAYJ,SAAStK,GAC1B+F,KAAK4E,aAAaL,SAAStK,GACvBgG,EAAQuD,OACRvJ,EAAQG,SAAS,WAAa6F,EAAQuD,OAErCvD,EAAQI,KACTJ,EAAQI,GAAKJ,EAAQE,KAEzBlG,EAAQsF,KAAK,KAAMU,EAAQI,GAAK,YAChCL,KAAKD,kBACLC,KAAKE,aACLF,KAAK8E,oBACL9E,KAAK+E,cACL/E,KAAKgF,eACLhF,KAAK0E,WAAWO,MAAO,EACvBjF,KAAKc,OAAOb,EAAQa,QAChBb,EAAQS,QACRV,KAAKW,OAET1G,EAAQiH,MACJnH,KAAM,cACNmL,YAAalF,KACbmF,WAAYnF,KAAKoB,SAGzB4D,aAAc,WACV,GAAII,GAAOpF,IACXoF,GAAKR,aAAaS,GAAG,UAAW,IAAMlI,EAAQ,SAAUzD,GACpD,GAAI4L,GAAK/L,EAAEG,EAAEC,QAAQW,QACrBZ,GAAEe,iBACEf,EAAE6L,UAAYxI,EAAKyI,KAAO9L,EAAE6L,UAAYxI,EAAK0I,KAAO/L,EAAEgM,QAAUhM,EAAE6L,UAAYxI,EAAK4I,IACnFP,EAAK3D,SACL2D,EAAKQ,SACElM,EAAE6L,UAAYxI,EAAK8I,KAC1BnL,EAAqB4K,EAAI,QAAQM,QAC1BlM,EAAE6L,UAAYxI,EAAK4I,GAC1BjL,EAAqB4K,EAAI,QAAQM,QAC1BlM,EAAE6L,UAAYxI,EAAK+I,UAAYpM,EAAE6L,UAAYxI,EAAKgJ,MACzDX,EAAK5F,QAAQwG,WAAWC,QAAQ,OAAStM,OAAQJ,EAAEG,EAAEC,UAC9CD,EAAE6L,UAAYxI,EAAKmJ,KAC1BZ,EAAGhL,SAASgB,KAAK,mBAAmBL,QAAQ2K,QACrClM,EAAE6L,UAAYxI,EAAKoJ,KAC1Bb,EAAGhL,SAASgB,KAAK,mBAAmBJ,OAAO0K,WAIvDd,kBAAmB,WAAA,GAGXZ,GACKC,EAHLlE,EAAUD,KAAKC,QACfmE,EAAQnE,EAAQmG,WAEpB,KAASjC,EAAI,EAAGA,EAAIC,EAAM5J,OAAQ2J,IAC9BD,EAAO,GAAIxI,GAAcnC,EAAEuG,QACvB8B,OAAQ3B,EAAQ2B,OAChB7H,KAAM,SACNgI,MAAO9B,EAAQ8B,OAChBqC,EAAMD,IAAKnE,KAAKR,SACnB0E,EAAKjK,QAAQI,KAAK,aAAaC,SAASiK,SAASvE,KAAK4E,eAG9DG,YAAa,WAAA,GACLK,GAAOpF,KACPC,EAAUD,KAAKC,QACfhG,EAAU+F,KAAK/F,OACnB+F,MAAK4E,aAAarF,KAAK,KAAMU,EAAQI,GAAKhB,IAAoBE,KAAKD,GAAgBW,EAAQoG,SACvFpG,EAAQ2B,SACR5B,KAAK4E,aAAe5K,EAAgBgG,KAAK4E,eAE7C5E,KAAKoB,MAAQpB,KAAK4E,aAAaO,YAC3BZ,SAAUtE,EAAQ2B,OAASrI,EAAE0G,EAAQ2B,QAAQ0B,SAAS,YAAc,KACpEgD,OAAQrM,EACRsM,MAAOvG,KAAKR,QAAQgH,OACpBC,kBAAkB,EAClBC,UAAWzG,EAAQyG,UACnBC,KAAM,SAAUjN,GACZ,GAAIkN,GAAqBxB,EAAK5F,QAAQyG,QAAQpH,IAAQlF,OAAQM,GAC9D,OAAI2M,IACAlN,EAAEe,iBACF,IAEJ2K,EAAKyB,iBAAiBnN,EAAEoN,QAAxB1B,IAEJ2B,SAAU,WACN/G,KAAK/F,QAAQqB,KAAK,mBAAmBL,QAAQ2K,SAEjDoB,MAAO,SAAUtN,GACb,GAAIkN,GAAqBxB,EAAK5F,QAAQyG,QAAQnH,IAASnF,OAAQM,GAC3D2M,IACAlN,EAAEe,iBAENR,EAAQ2L,WAEb1E,KAAK,cACRlB,KAAKoB,MAAMnH,QAAQoL,GAAG1G,GAAO,aAAcpE,IAE/CsM,iBAAkB,SAAUzF,GACxB,GAAuE6F,GAAnEX,EAASlF,EAAMnB,QAAQqG,OAAQY,EAAgBlK,EAAWsJ,EAC9D9J,GAAMnC,KAAK+G,EAAMnH,SAASG,SAAS,mBAE/B6M,EADoC,eAApC7F,EAAMnH,QAAQkN,IAAI,cACVD,GAAiBlK,EAAWoE,EAAMnH,SAAWmH,EAAMnH,QAAQgN,SAE3DC,EAEZ9F,EAAMnH,QAAQkN,KACVC,WAAYd,EAAOa,IAAI,eACvBE,YAAaJ,KAGrBpG,OAAQ,WACJb,KAAKoB,MAAMnH,QAAQqN,IAAI3I,GAAO,cAC9BqB,KAAKoB,MAAMmG,UACXvH,KAAK/F,QAAQ4G,UAEjBY,OAAQ,YACAzB,KAAKC,QAAQa,QAAUd,KAAKoB,MAAMoG,YAClCxH,KAAKoB,MAAMK,UAGnBX,OAAQ,SAAUC,GACVA,IAAcvH,IACduH,GAAY,GAEhBf,KAAK0E,WAAW5D,OAAOC,GACvBf,KAAK/F,QAAQJ,YAAYgE,GAAiBkD,GAC1Cf,KAAKC,QAAQa,OAASC,GAE1B6E,MAAO,WACH5F,KAAK/F,QAAQ2L,SAEjBjF,KAAM,WACEX,KAAKoB,OACLpB,KAAKoB,MAAM4F,QAEfhH,KAAK/F,QAAQG,SAAS0D,GAAc6C,OACpCX,KAAKC,QAAQS,QAAS,GAE1BF,KAAM,WACFR,KAAK/F,QAAQwG,YAAY3C,GAAc6C,OACvCX,KAAKC,QAAQS,QAAS,KAG9BlE,EAAMgD,QAAQzD,mBAAqBA,EAC/BC,EAAsBR,EAAKsE,QAC3BuB,KAAM,SAAUpB,EAAST,GAAnB,GACqG0E,GAAMjD,EAOpGkD,EAPLlK,EAAU+F,KAAK/F,QAAUV,EAAE,cAAgBgE,EAAe,WAAY6G,EAAQnE,EAAQmG,WAO1F,KANApG,KAAKC,QAAUA,EACfD,KAAKR,QAAUA,EACfQ,KAAKH,UAAW,EAChBoB,GAAoBhB,EAAQI,IAAMJ,EAAQE,KAAOd,GACjDW,KAAK0E,WAAa,GAAI/I,GAAepC,EAAEuG,UAAWG,IAClDD,KAAK0E,WAAWzK,QAAQsK,SAAStK,GACxBkK,EAAI,EAAGA,EAAIC,EAAM5J,OAAQ2J,IAC9BD,EAAO,GAAIvI,GAAepC,EAAEuG,QACxB8B,OAAQ3B,EAAQ2B,OAChB7H,KAAM,SACNkH,iBAAkBA,GACnBmD,EAAMD,IAAKnE,KAAKR,SACnB0E,EAAKjK,QAAQsK,SAAStK,EAE1B+F,MAAKE,aACLF,KAAKD,kBACLC,KAAK0E,WAAWO,MAAO,EACvBhL,EAAQiH,MACJnH,KAAM,cACNmL,YAAalF,QAGrBY,eAAgB,WACZZ,KAAK/F,QAAQG,SAASgF,OAG9B5C,EAAMgD,QAAQxD,oBAAsBA,EACpCQ,EAAMgD,QAAQG,kBAAkB,cAAe5D,EAAoBC,GAC/DC,EAAmBT,EAAKsE,QACxBuB,KAAM,SAAUpB,EAAST,GACrB,GAAIvF,GAAU+F,KAAK/F,QAAUV,EAAE,oBAC/ByG,MAAK/F,QAAUA,EACf+F,KAAKC,QAAUA,EACfD,KAAKR,QAAUA,EACfQ,KAAKO,aACLP,KAAKI,YACLJ,KAAKE,aACLF,KAAKD,kBACL9F,EAAQG,SAASoD,GACjBvD,EAAQiH,MACJnH,KAAM,YACN0N,UAAWzH,UAInB9D,EAAoBV,EAAKsE,QACzBuB,KAAM,SAAUpB,EAAST,GACrB,GAAIvF,GAAU+F,KAAK/F,QAAUV,EAAE,kBAC/ByG,MAAK/F,QAAUA,EACf+F,KAAKC,QAAUA,EACfD,KAAKR,QAAUA,EACfQ,KAAKH,UAAW,EAChBG,KAAKO,aACLP,KAAKE,aACLF,KAAKM,oBACLrG,EAAQG,SAASoD,GACjBvD,EAAQiH,MACJnH,KAAM,YACN0N,UAAWzH,QAGnBY,eAAgB,WACZZ,KAAK/F,QAAQG,SAASgF,OAG9B5C,EAAMgD,QAAQG,kBAAkB,YAAa1D,EAAkBC,GAC3DC,EAAgBX,EAAKsE,QACrBuB,KAAM,SAAUpB,EAAST,GACrB,GAAIvF,GAAU+F,KAAK/F,QAAUV,EAAE,oBAC/ByG,MAAK/F,QAAUA,EACf+F,KAAKC,QAAUA,EACfD,KAAKR,QAAUA,EACfvF,EAAQG,SAASqD,GACjBxD,EAAQiH,MAAOnH,KAAM2D,OAG7BlB,EAAMgD,QAAQG,kBAAkBjC,EAAQvB,GACpCC,EAAeZ,EAAKsE,QACpBuB,KAAM,SAAUqG,EAAUzH,EAAST,GAC/B,GAAIvF,GAAU6C,EAAW4K,GAAYA,EAASzH,GAAWyH,CAIrDzN,GAHEA,YAAmB0N,QAGX1N,EAAQI,KAAK,eAAeC,SAF5Bf,EAAE,eAAeoK,KAAK1J,GAIpC+F,KAAK/F,QAAUA,EACf+F,KAAKC,QAAUA,EACfD,KAAKC,QAAQlG,KAAO,WACpBiG,KAAKR,QAAUA,EACfQ,KAAKO,aACLP,KAAKE,aACLF,KAAKI,YACLJ,KAAKD,kBACL9F,EAAQiH,MACJnH,KAAM,WACN2N,SAAU1H,UAItBxD,EAAMgD,QAAQpD,aAAeA,EACzBC,EAAuBb,EAAKsE,QAC5BuB,KAAM,SAAUqG,EAAUzH,EAAST,GAC/B,GAAIvF,GAAiCV,EAAvBuD,EAAW4K,GAAcA,EAASzH,GAAcyH,EAI1DzN,GAHEA,YAAmB0N,QAGX1N,EAAQI,KAAK,aAAaC,SAF1Bf,EAAE,aAAaoK,KAAK1J,GAIlC+F,KAAK/F,QAAUA,EACf+F,KAAKC,QAAUA,EACfD,KAAKC,QAAQlG,KAAO,WACpBiG,KAAKR,QAAUA,EACfQ,KAAKH,UAAW,EAChBG,KAAKO,aACLP,KAAKE,aACLF,KAAKM,oBACLN,KAAKD,kBACL9F,EAAQiH,MACJnH,KAAM,WACN2N,SAAU1H,QAGlBY,eAAgB,WACZZ,KAAK/F,QAAQG,SAASgF,OAG9B5C,EAAMgD,QAAQnD,qBAAuBA,EA8BjCC,EAAQI,EAAMoD,QACduB,KAAM,SAAUzB,GACZI,KAAKJ,KAAOA,EACZI,KAAKqE,YAETuD,IAAK,SAAU9D,GACX9D,KAAKqE,QAAQrE,KAAKqE,QAAQ7J,QAAUsJ,GAExCjD,OAAQ,SAAUiD,GACd,GAAI+D,GAAQtO,EAAEuO,QAAQhE,EAAQ9D,KAAKqE,QACnCrE,MAAKqE,QAAQ0D,OAAOF,EAAO,IAE/BzF,OAAQ,SAAU0B,GAAV,GACAkE,GACK7D,CAAT,KAASA,EAAI,EAAGA,EAAInE,KAAKqE,QAAQ7J,OAAQ2J,IACrC6D,EAAMhI,KAAKqE,QAAQF,GACnB6D,EAAI5F,QAAO,EAEf0B,GAAO1B,QAAO,GACV0B,EAAO9C,QACP8C,EAAO9C,OAAOoB,QAAO,MAI7B7F,EAAUI,EAAOmD,QACjBuB,KAAM,SAAUpH,EAASgG,GAAnB,GAqCWkE,GApCTiB,EAAOpF,IAmCX,IAlCArD,EAAO9B,GAAGwG,KAAKjG,KAAKgK,EAAMnL,EAASgG,GACnCA,EAAUmF,EAAKnF,QACfhG,EAAUmL,EAAK6C,QAAU7C,EAAKnL,QAC9BA,EAAQG,SAAS8C,EAAU,aAC3B8C,KAAKG,IAAM3D,EAAM8H,OACjBtE,KAAKwG,OAAShK,EAAM0L,QAAQ3B,MAAMtM,GAClC+F,KAAKmI,WACLlO,EAAQsF,KAAKD,GAAgBU,KAAKG,KAClCiF,EAAKgD,SAAqC,iBAAnBnI,GAAQ2B,OAAuB3B,EAAQ2B,OAASwD,EAAKnL,QAAQE,QAAQ,YAAY,GACxGiL,EAAKsB,UAAYtB,EAAKgD,UAAazB,MAAQ0B,QAAS,YAChDjD,EAAKgD,WACLnO,EAAQG,SAAS,aACjB8D,EAAO,UACPC,EAAc,MACdhB,EAAS,YACTG,EAAe,iBACfxD,EAAe,kBACf+D,EAAiB,qBAEjBoC,EAAQkB,WACRiE,EAAKkD,kBACLrO,EAAQG,SAASwD,GACjBwH,EAAKmD,mBAAqB,GAAI/L,GAAMgM,WAAWpD,EAAKnL,SAChDwO,UAAW,EACXC,gBAAgB,EAChB3F,OAAQ,IAAM1H,EACdsN,IAAK9L,EAAMuI,EAAKwD,gBAAiBxD,KAErCA,EAAKyD,eAAiBrM,EAAMsM,SAAS,WACjC1D,EAAK2D,YAGT3D,EAAKhE,OAAUnH,QAASV,OAExB0G,EAAQmE,OAASnE,EAAQmE,MAAM5J,OAAQ,CACvC,IAAS2J,EAAI,EAAGA,EAAIlE,EAAQmE,MAAM5J,OAAQ2J,IACtCiB,EAAKwC,IAAI3H,EAAQmE,MAAMD,GAEvBlE,GAAQkB,WACRiE,EAAK4D,QAAQ5D,EAAKnL,QAAQgP,cAGlC7D,EAAKY,WAAa,GAAIxJ,GAAMgM,WAAWU,UACnCT,UAAW,EACXC,gBAAgB,EAChB3F,OAAQ,IAAMzD,GAAiB,IAAMU,KAAKG,IAAM,OAAShD,EAAS,MAAamC,GAAiB,IAAMU,KAAKG,IAAM,MAAQ/C,EACzHuL,IAAK9L,EAAMuI,EAAK+D,aAAc/D,GAC9BgE,MAAO3P,EACP4P,QAAS5P,IAEb2L,EAAKnL,QAAQoL,GAAG1G,GAAO,aAAcpE,GACrC6K,EAAKJ,eACD/E,EAAQkB,WACRiE,EAAKhE,MAAMnH,QAAQoL,GAAG1G,GAAO,IAAepE,GAE5C0F,EAAQkB,WACRnB,KAAKsJ,wBAET9M,EAAM+M,OAAOnE,IAEjBoE,QACI7K,GACAC,GACAC,GACAC,GACAC,GACAC,IAEJiB,SACIL,KAAM,UACNwE,SACAjD,WAAW,EACXS,OAAQ,MAEZE,WAAY,SAAUgC,EAAQ2F,GAC1B,GAAI5H,EAOJ,OAHIA,GAHC7B,KAAKmI,QAAQsB,GAGNzJ,KAAKmI,QAAQsB,GAFbzJ,KAAKmI,QAAQsB,GAAa,GAAInN,GAI1CuF,EAAM+F,IAAI9D,GACHjC,GAEX0F,QAAS,WACL,GAAInC,GAAOpF,IACXoF,GAAKnL,QAAQqB,KAAK,IAAMiC,GAAcyF,KAAK,SAAUC,EAAKhJ,GACtDV,EAAEU,GAASiH,KAAK,cAAcqG,YAElCnC,EAAKnL,QAAQqN,IAAI3I,GAAO,cACxByG,EAAKY,WAAWuB,UACZnC,EAAKnF,QAAQkB,YACb3E,EAAMkN,aAAatE,EAAKyD,gBACxBzD,EAAKmD,mBAAmBhB,UACxBnC,EAAKhE,MAAMnH,QAAQqN,IAAI3I,GAAO,cAC9ByG,EAAKhE,MAAMmG,WAEf5K,EAAO9B,GAAG0M,QAAQnM,KAAKgK,IAE3BwC,IAAK,SAAU3H,GAAV,GACsE0J,GAA6HC,EAQvLzF,EART0F,EAAYtO,EAAW0E,EAAQlG,MAAO2N,EAAWzH,EAAQyH,SAAgBtC,EAAOpF,KAAM8J,EAAc1E,EAAKgD,SAAW,GAAK,yBAA0B2B,EAAmB9J,EAAQ8J,gBAOlL,IANAxQ,EAAEuG,OAAOG,GACLE,IAAK3D,EAAM8H,OACXoC,UAAWtB,EAAKsB,UAChB9E,OAAQwD,EAAKgD,SACb/B,QAASjB,EAAKjF,MAEdF,EAAQmG,YACR,IAASjC,EAAI,EAAGA,EAAIlE,EAAQmG,YAAY5L,OAAQ2J,IAC5C5K,EAAEuG,OAAOG,EAAQmG,YAAYjC,IAAMhE,IAAK3D,EAAM8H,QAGlDoD,KAAaqC,GAAoB9J,EAAQlG,OAAS2D,EAClDuC,EAAQJ,SAAWZ,GACXgB,EAAQJ,WAChBI,EAAQJ,SAAWX,IAEnBe,EAAQJ,WAAaZ,IAAkBmG,EAAKnF,QAAQkB,YAChD4I,EACAH,EAAe,GAAIvN,GAAqB0N,EAAkB9J,EAASmF,GAC5DyE,IACPD,EAAe,GAAIC,GAAUhK,SAASI,EAASmF,GAC/CwE,EAAa3P,QAAQG,SAAS0P,IAE9BF,IACI3J,EAAQJ,WAAaX,IACrB0K,EAAahJ,iBAEjBgJ,EAAa3P,QAAQsK,SAASa,EAAKhE,MAAM4I,WACzC5E,EAAK6E,QAAQ,UAAW,WACpB,OAASC,SAAUN,EAAa3P,QAAQkQ,WAIhDlK,EAAQJ,WAAaV,KACjBuI,EACAiC,EAAO,GAAIvN,GAAasL,EAAUzH,EAASmF,GACpCyE,IACPF,EAAO,GAAIE,GAAUrK,QAAQS,EAASmF,IAEtCuE,IACAA,EAAK1P,QAAQsK,SAASa,EAAKnL,SAC3BmL,EAAK6E,QAAQ,UAAW,WACpB,OAASC,SAAUP,EAAK1P,QAAQkQ,YAKhDC,SAAU,SAAUjP,GAChB,GAAIlB,GAASoQ,EAAaC,EAAoDvQ,EAAtCwQ,EAAcvK,KAAKC,QAAQkB,SA0BnE,OAzBAlH,GAAU+F,KAAK/F,QAAQqB,KAAKH,GACvBlB,EAAQO,SACTP,EAAUV,EAAE,+BAAiCyG,KAAKG,IAAM,KAAK7E,KAAKH,IAEtEpB,EAAOE,EAAQO,OAASP,EAAQiH,KAAK,QAAU,GAC/CmJ,EAAcpQ,EAAQiH,KAAKnH,GACvBsQ,GACIA,EAAYpF,OACZhL,EAAUA,EAAQK,OAAO,IAAMiD,GAC/BxD,EAAO,cACPsQ,EAAcpQ,EAAQiH,KAAKnH,IAE3BwQ,IACAD,EAAeD,EAAYrJ,SAExBuJ,IACPtQ,EAAU+F,KAAKoB,MAAMnH,QAAQqB,KAAKH,GAClCpB,EAAOE,EAAQO,OAASP,EAAQiH,KAAK,QAAU,GAC/CoJ,EAAerQ,EAAQiH,KAAKnH,GACxBuQ,GAAgBA,EAAarF,OAC7BhL,EAAUA,EAAQK,OAAO,IAAMiD,GAC/BxD,EAAO,cACPuQ,EAAerQ,EAAQiH,KAAKnH,MAIhCA,KAAMA,EACNyF,QAAS6K,EACTxK,SAAUyK,IAGlBzJ,OAAQ,SAAU1F,GACd,GAAI+I,GAAOlE,KAAKoK,SAASjP,EACrB+I,GAAK1E,SACL0E,EAAK1E,QAAQqB,SAEbqD,EAAKrE,UACLqE,EAAKrE,SAASgB,SAElBb,KAAK+I,QAAO,IAEhBpI,KAAM,SAAUxF,GAAV,GAEEqP,GADAtG,EAAOlE,KAAKoK,SAASjP,EAErB+I,GAAK1E,UAC6B,WAA9B0E,EAAK1E,QAAQS,QAAQlG,MAAqBmK,EAAK1E,QAAQS,QAAQqC,SAC/DkI,EAAsBtG,EAAK1E,QAAQ6C,iBACnC6B,EAAK1E,QAAQmB,OACT6J,GACAA,EAAoBhG,WAEhBN,EAAK1E,QAAQS,QAAQS,QAC7BwD,EAAK1E,QAAQmB,QAGjBuD,EAAKrE,WAC8B,WAA/BqE,EAAKrE,SAASI,QAAQlG,MAAqBmK,EAAKrE,SAASI,QAAQqC,SACjEkI,EAAsBtG,EAAKrE,SAASwC,iBACpC6B,EAAKrE,SAASc,OACV6J,GACAA,EAAoBhG,WAEhBN,EAAKrE,SAASI,QAAQS,QAC9BwD,EAAKrE,SAASc,QAGtBX,KAAK+I,QAAO,IAEhBvI,KAAM,SAAUrF,GACZ,GAAI+I,GAAOlE,KAAKoK,SAASjP,EACrB+I,GAAK1E,UAC6B,WAA9B0E,EAAK1E,QAAQS,QAAQlG,MAAqBmK,EAAK1E,QAAQS,QAAQqC,SAC/D4B,EAAK1E,QAAQgB,OACb0D,EAAK1E,QAAQ6C,iBAAiBmC,WACvBN,EAAK1E,QAAQS,QAAQS,QAC5BwD,EAAK1E,QAAQgB,QAGjB0D,EAAKrE,WAC8B,WAA/BqE,EAAKrE,SAASI,QAAQlG,MAAqBmK,EAAKrE,SAASI,QAAQqC,SACjE4B,EAAK1E,QAAQgB,OACb0D,EAAKrE,SAASwC,iBAAiBmC,WACxBN,EAAKrE,SAASI,QAAQS,QAC7BwD,EAAKrE,SAASW,QAGtBR,KAAK+I,QAAO,IAEhBjI,OAAQ,SAAU7G,EAAS6G,GACvB,GAAIoD,GAAOlE,KAAKoK,SAASnQ,EACJ,KAAV6G,IACPA,GAAS,GAEToD,EAAK1E,SACL0E,EAAK1E,QAAQsB,OAAOA,GAEpBoD,EAAKrE,UACLqE,EAAKrE,SAASiB,OAAOA,IAG7B2J,qBAAsB,SAAUhB,GAC5B,MAAOzJ,MAAK/F,QAAQqB,KAAK,IAAM+B,EAAgB,gBAAmBoM,EAAY,MAAO1G,OAAO,IAAMjJ,IAEtG2H,OAAQ,SAAUqC,EAAQ4G,GACtB,GAAIzQ,GAAUV,EAAEuK,GAASI,EAAOjK,EAAQiH,KAAK,SACzCgD,GAAKjE,QAAQuB,YACTkJ,IAAYlR,IACZkR,GAAU,GAEdxG,EAAKzC,OAAOiJ,GAAS,KAG7BpC,gBAAiB,WACb,GAAIlD,GAAOpF,KAAMN,EAAoBnE,EAAWmE,kBAAmB6G,EAAQnB,EAAKoB,OAAQmE,EAAsBpE,EAAQ,OAAS,OAC/HnB,GAAK3F,eAAiBlG,EAAEgC,EAAWkE,gBAAgBrF,SAAS+C,GAC5DiI,EAAKnL,QAAQ2Q,OAAOxF,EAAK3F,gBACrB2F,EAAKgD,UACLhD,EAAK3F,eAAemL,OAAO,yCAC3BlL,EAAoB1F,EAAgB0F,IAEpC0F,EAAK3F,eAAemL,OAAO,kDAE/BxF,EAAKhE,MAAQ,GAAI5E,GAAMI,GAAGiO,MAAMnL,GAC5BoL,OAAQ,UAAYH,EACpBI,SAAU,OAASJ,EACnBrE,OAAQlB,EAAK3F,eACb8G,MAAOA,EACPG,UAAWtB,EAAKsB,UAChBnC,SAAUa,EAAKgD,SAAW7O,EAAE6L,EAAKgD,UAAU9E,SAAS,YAAc,KAClEmD,kBAAkB,EAClBE,KAAM,SAAUjN,GACZ,GAAIuO,GAAUzL,EAAMnC,KAAK+K,EAAKhE,MAAMnH,SAASG,SAAS,qBACjDgL,GAAKgD,SAGNhD,EAAKhE,MAAM4I,UAAU7C,IAAI,aAAc6D,WAAWzR,EAAE,uBAAuB0R,eAAiB,GAAK,MAFjGhD,EAAQd,IAAI,eAAgBZ,KAAa,KAAOvJ,EAAWiL,GAAWA,EAAQhB,SAAW,EAAI,IAI7F7B,EAAKa,QAAQlH,KACbrF,EAAEe,kBAGVsM,SAAU,WACN/G,KAAK/F,QAAQqB,KAAK,mBAAmBL,QAAQ2K,SAEjDoB,MAAO,SAAUtN,GACT0L,EAAKa,QAAQjH,KACbtF,EAAEe,iBAENuF,KAAK/F,QAAQ2L,WAGrBR,EAAKhE,MAAMnH,QAAQoL,GAAG,UAAW,IAAMlI,EAAQ,SAAUzD,GACrD,GAAwHO,GAApHN,EAASJ,EAAEG,EAAEC,QAAS2L,EAAK3L,EAAOW,SAAU4Q,EAAgB5F,EAAG1L,GAAG,IAAM0D,IAAiBgI,EAAG1L,GAAG,IAAM2D,EACzG7D,GAAEe,iBACEf,EAAE6L,UAAYxI,EAAKyI,KAAO9L,EAAE6L,UAAYxI,EAAK0I,KAAO/L,EAAEgM,QAAUhM,EAAE6L,UAAYxI,EAAK4I,IACnFP,EAAKwD,kBACLxD,EAAK3F,eAAemG,SACblM,EAAE6L,UAAYxI,EAAK8I,MAC1B5L,GAAWiR,GAAiBA,GAAiBvR,EAAOC,GAAG,eAAiB0L,EAAK3L,EAC7Ee,EAAqBT,EAAS,QAAQ2L,SAC/BlM,EAAE6L,UAAYxI,EAAK4I,IAC1B1L,GAAWiR,GAAiBA,GAAiBvR,EAAOC,GAAG,gBAAkB0L,EAAK3L,EAC9Ee,EAAqBT,EAAS,QAAQ2L,SAC/BlM,EAAE6L,UAAYxI,EAAK+I,UAAYpM,EAAE6L,UAAYxI,EAAKgJ,OACzDX,EAAKY,WAAWC,QAAQ,OAAStM,OAAQJ,EAAEG,EAAEC,UAC7CyL,EAAK3F,eAAemG,SACblM,EAAE6L,UAAYxI,EAAKmJ,KAC1BZ,EAAGhL,SAASgB,KAAK,mBAAmBL,QAAQ2K,QACrClM,EAAE6L,UAAYxI,EAAKoJ,KAC1Bb,EAAGhL,SAASgB,KAAK,mBAAmBJ,OAAO0K,UAI/CR,EAAKhE,MAAM4I,UADX5E,EAAKgD,SACkBhD,EAAKhE,MAAMnH,QAAQqB,KAAK,IAAMkD,IAE9B4G,EAAKhE,MAAMnH,QAEtCmL,EAAKhE,MAAM4I,UAAUzK,KAAKD,GAAgBU,KAAKG,MAEnDmJ,sBAAuB,WAAA,GACf6B,IAAqB,EACrBC,EAAapL,KAAKwG,OAAS,eAAiB,eAE5C2E,GADAnL,KAAKC,QAAQ2B,OACQ5B,KAAKoB,MAAMnH,QAAQqB,KAAK,IAAMkD,IAAoB8E,SAAS,SAAWlE,GAAkB,MAAQzB,EAAQ,KAAKnD,OAAS,EAEtHwF,KAAKoB,MAAMnH,QAAQqJ,SAAS,SAAWlE,GAAkB,MAAQzB,EAAQ,KAAKnD,OAAS,EAE5G2Q,GACAnL,KAAKP,eAAe0H,KAChBkE,WAAY,UACZpE,MAAO,KAEXjH,KAAKiI,QAAQd,IAAIiE,EAAYpL,KAAKP,eAAezC,YAAW,MAE5DgD,KAAKP,eAAe0H,KAChBkE,WAAY,SACZpE,MAAO,QAEXjH,KAAKiI,QAAQd,IAAIiE,EAAY,MAGrCjC,aAAc,SAAUzP,GACpB,GAAiB0H,GAAOzH,EAAQuK,EAAMoH,EAAwFC,EAASC,EAAWC,EAA9IrG,EAAOpF,KAA2C0L,EAAqBhS,EAAEC,OAAOQ,QAAQ,IAAMoE,GAAoB/D,MAEtH,OADAd,GAAEe,iBACEiR,GACAtG,EAAKuG,QAAQjS,GACb,IAEJC,EAASJ,EAAEG,EAAEC,QAAQQ,QAAQ,IAAMgD,EAAQiI,EAAKnL,SAC5CN,EAAOO,SAASmB,KAGpB6I,EAAOvK,EAAOuH,KAAK,WACdgD,GAAQkB,EAAKhE,QACdzH,EAASJ,EAAEG,EAAEC,QAAQQ,QAAQ,IAAMiD,EAAiBgI,EAAKhE,MAAM4I,WAC/D9F,EAAOvK,EAAOW,OAAO,MAAM4G,KAAK,WAE/BgD,GAASA,EAAKjE,QAAQa,SAGvBoD,EAAKjE,QAAQuB,WACb+J,EAAUzO,EAAWoH,EAAKjC,eAAiBiC,EAAKjC,cAAgB,KAChEiC,EAAKzC,QAAQyC,EAAKjE,QAAQyB,UAAU,GACpC8J,GACI7R,OAAQA,EACRkI,MAAOqC,EAAKjE,QAAQ4B,MACpB6I,QAASxG,EAAKjE,QAAQyB,SACtBrB,GAAI6D,EAAKjE,QAAQI,GACjB6D,KAAMA,GAENqH,GACAA,EAAQnQ,KAAKgK,EAAMoG,GAEvBpG,EAAKa,QAAQrH,GAAQ4M,KAErBD,EAAUzO,EAAWoH,EAAKlC,cAAgBkC,EAAKlC,aAAe,KAC9DwJ,GACI1E,OAAQ1B,EACRzL,OAAQA,EACR0G,GAAI6D,EAAKjE,QAAQI,GACjB6D,KAAMA,GAENqH,GACAA,EAAQnQ,KAAKgK,EAAMoG,GAEvBpG,EAAKa,QAAQtH,GAAO6M,IAEpBtH,EAAKjE,QAAQ0B,MACTuC,EAAKjE,QAAQM,YAAc2D,EAAKjE,QAAQM,WAAW5G,SACnD8R,EAAYvH,EAAKjE,QAAQM,WAAW5G,QAExC8C,OAAOkK,KAAKzC,EAAKjE,QAAQ0B,IAAK8J,GAAa,UAE3C9R,EAAOO,SAASkD,IAChBgI,EAAKhE,MAAM4F,QAEfsE,EAAiB3R,EAAOQ,QAAQ,sBAC5BmR,EAAe,KACflK,EAAQkK,EAAepK,KAAK,eAC3BE,EAAQA,EAAQkK,EAAeM,QAAQ,qBAAqB1K,KAAK,eAAe8F,WAnDrFrN,IAsDJqL,aAAc,WACV,GAAII,GAAOpF,IACXoF,GAAKnL,QAAQsF,KAAK,WAAY,GAAG8F,GAAG,UAAW,SAAUwG,GAAV,GACvClS,GAASJ,EAAEsS,EAAGlS,QACdM,EAAUV,EAAEyG,MAAM1E,KAAK,wBACtB3B,GAAOC,GAAG,IAAMsD,IAA+B,IAAnBjD,EAAQO,SAGrCP,EAAQL,GAAG,IAAMyB,KACjBpB,EAAUS,EAAqBT,EAAS,SAExCA,EAAQO,QACRP,EAAQ,GAAG2L,WAEhBP,GAAG,UAAWxI,EAAMuI,EAAK0G,SAAU1G,KAE1C0G,SAAU,SAAUpS,GAAV,GAGEO,GAAoD8R,EAAsBC,EAAuBC,EAQ7FC,EAiBJC,EAcIC,EAQJlH,EACAmH,EAlDJ1S,EAASJ,EAAEG,EAAEC,QAAS4L,EAAU7L,EAAE6L,QAASnB,EAAQpE,KAAK/F,QAAQqJ,SAAS,8BAA+BgJ,EAAYtM,KAAKwG,UAAc,CAgD3I,IA/CIjB,IAAYxI,EAAK0I,MACbxL,EAAUN,EAAO4S,aAAavM,KAAK/F,SAASiB,OAAQ6Q,GAAe,EAAOC,GAAgB,EAAOC,GAAuB,EACvH7H,EAAMoI,IAAI,IAAMnR,GAAiBb,SAClCyR,GAAuB,GAEtBhS,EAAQO,SACTP,EAAUN,GAEVM,EAAQL,GAAG,IAAMyB,KAAqB4Q,IAClCC,EAA4B9H,EAAMlJ,OAClCxB,EAAE+S,UACF/S,EAAEe,iBAEFyR,EAA0BtS,GAAG,mBAC7BwK,EAAMlJ,OAAO0K,QAEbxB,EAAMlJ,OAAOI,KAAK,mBAAmBJ,OAAO0K,SAG/ClM,EAAE+S,UAAYrI,EAAMyD,MAAM5N,KAAamK,EAAM5J,OAAS,IAEnDuR,GADA9R,EAAQL,GAAG,IAAM0D,IACF3D,EAAOC,GAAG,gBAK7BuS,EAAc/H,EAAMyD,MAAM5N,KAAamK,EAAMoI,IAAI,sBAAsBvR,QAAQ4M,QAC/EnO,EAAE+S,UAAYN,IAEVH,GADA/R,EAAQL,GAAG,IAAM0D,IACD3D,EAAOC,GAAG,iBAK9BmS,GAAgB/L,KAAKP,gBAA4D,WAA1CO,KAAKP,eAAe0H,IAAI,gBAA+B8E,IAC9FvS,EAAEe,iBACFuF,KAAKP,eAAemG,UAEpBoG,GAAiBC,GAAwBvS,EAAE+S,YAC3C/S,EAAEe,iBACE2R,EAAgBpM,KAAK0M,kBAAkB1M,KAAKiI,SAC5CmE,GACAA,EAAcxG,SAGtB5F,KAAK2M,mBAAoB,GAEzBjT,EAAEgM,QAAUH,IAAYxI,EAAK8I,KAQ7B,MAPIX,GAAc3L,EAAE2P,SAAS0D,eAAe1L,KAAK,eAC7CmL,EAAmB9S,EAAE2P,SAAS0D,eAAehT,GAAG,IAAMyB,GACtD6J,EACAA,EAAYzD,SACL4K,GACPrM,KAAK4I,kBAET,CAEJ,KAAKrD,IAAYxI,EAAK+I,UAAYP,IAAYxI,EAAKgJ,SAAWpM,EAAOC,GAAG,mBAUpE,MATI2L,KAAYxI,EAAK+I,UACjBpM,EAAEe,iBAEFd,EAAOC,GAAG,IAAM2D,IAChB5D,EAASA,EAAO2J,WAAWrI,QAC3B+E,KAAKgG,WAAWC,QAAQ,OAAStM,OAAQA,KAClC4L,IAAYxI,EAAK+I,UACxB9F,KAAKgG,WAAWC,QAAQ,OAAStM,OAAQA,IAE7C,CAEJ,IAAI4L,IAAYxI,EAAKmJ,KAAM,CACvB,GAAIvM,EAAOC,GAAG,gBAAkBD,EAAOC,GAAG,SACtC,MAEAoG,MAAKP,eACL2E,EAAMyI,GAAG,GAAGjH,QAEZxB,EAAMnJ,QAAQ2K,QAElBlM,EAAEe,qBACC,IAAI8K,IAAYxI,EAAKoJ,IAAK,CAC7B,GAAIxM,EAAOC,GAAG,gBAAkBD,EAAOC,GAAG,SACtC,MAEAoG,MAAKP,gBAA8D,UAA5ClG,EAAEyG,KAAKP,gBAAgB0H,IAAI,cAClDnH,KAAKP,eAAemG,QAEpBxB,EAAMlJ,OAAO0K,QAEjBlM,EAAEe,qBACK8K,KAAYxI,EAAK+P,OAAU9M,KAAK2M,mBAAsBhT,EAAOC,GAAG,gDAAiDoG,KAAK+M,gBAAgBrT,EAAEC,OAAQ,EAAI2S,GAGpJ/G,IAAYxI,EAAKiQ,MAAShN,KAAK2M,mBAAsBhT,EAAOC,GAAG,gDAAiDoG,KAAK+M,gBAAgBrT,EAAEC,UAAa2S,KAC3JtM,KAAK+M,gBAAgBrT,EAAEC,UAAa2S,GAAW1G,QAC/ClM,EAAEe,mBAJFuF,KAAK+M,gBAAgBrT,EAAEC,OAAQ,EAAI2S,GAAW1G,QAC9ClM,EAAEe,mBAMVsS,gBAAiB,SAAU7I,EAAMoI,GAAhB,GACTlI,GAAQpE,KAAK/F,QAAQqJ,SAAS,8BAC9B2J,EAAuC7I,EAAMyD,MAAjCzD,EAAMyD,MAAM3D,QAA2BA,EAAKgJ,cAA6BhJ,GACrFiJ,EAAanN,KAAKP,eAAiB,EAAI,EACvC2N,EAAkBd,EAClBe,EAA4B,IAAdf,EAAkBlI,EAAM5J,OAAS,EAAI2S,EACnDtF,EAAsB,IAAdyE,EAAkBa,EAAa/I,EAAM5J,OAAS,EACtD8S,EAAgBlJ,EAAM6I,EAAYX,EAEtC,IADAtM,KAAK2M,mBAAoB,EACrBpT,EAAE2K,GAAM/J,QAAQ,IAAMmD,GAAc9C,SAAWjB,EAAE2K,GAAMtK,GAAiB,IAAd0S,EAAkB,cAAgB,gBAC5F,MAAO/S,GAAE2K,GAAM/J,QAAQ,IAAMmD,GAAcgG,WAAW/J,EAAE2K,GAAM/J,QAAQ,IAAMmD,GAAcgG,WAAWuE,MAAM3D,GAAQoI,EAQvH,KANItM,KAAKP,gBAAkByE,IAASlE,KAAKP,eAAe,IAAM6M,SAC1DgB,EAAgBlJ,EAAMA,EAAM5J,OAAS,IAErCyS,IAAcI,IACdC,GAAiBtN,KAAKP,gBAAkBO,KAAKP,gBAA+D,WAA7ClG,EAAEyG,KAAKP,gBAAgB0H,IAAI,cAA6B/C,EAAMyD,GAAS7H,KAAKP,iBAEvIlG,EAAE+T,GAAe1T,GAAG,oBAAoB,CAM5C,GAJI0T,EADAhB,QAAoB/S,EAAE+T,GAAenT,QAAQ,IAAMmD,GAAc9C,OACjDjB,EAAE+T,GAAehK,SAAS,oBAAoBpI,OAE9C3B,EAAE+T,GAAehK,SAAS,oBAAoBrI,SAE7DqS,EAAc9S,SACf4S,GAAoCd,EACpCgB,EAAgBlJ,EAAM6I,EAAYG,IAC7BE,GACD,MAAOtN,MAAKP,cAGpBO,MAAK2M,mBAAoBpT,EAAE+T,GAAenT,QAAQ,IAAMmD,GAAc9C,OAE1E,MAAO8S,IAEXZ,kBAAmB,SAAUzS,GACzB,GAAIA,EAAQL,GAAG,QACX,MAAOK,EAEX,IAAIsT,GAAgBC,EAAaC,EAAexT,EAAQyT,SAWxD,OAVAD,GAAazK,KAAK,WAEd,MADAwK,GAAcjU,EAAEyG,MACZwN,EAAY5T,GAAG,oBACf2T,EAAiBC,GACV,GACAA,EAAYlS,KAAK,mBAAmBd,OAAS,GACpD+S,EAAiBC,EAAYlS,KAAK,mBAAmBJ,QAC9C,GAFJ,IAKPqS,EACOA,EAEAvN,KAAK0M,kBAAkBzS,EAAQK,WAG9CqR,QAAS,SAAUjS,GACf,GAAIwL,GAAc3L,EAAEG,EAAEC,QAAQQ,QAAQ,IAAMoD,GAAc2D,KAAK,cAC/DxH,GAAEe,iBACGyK,EAAYjF,QAAQa,QAGzBoE,EAAYzD,UAEhBmH,gBAAiB,WACb5I,KAAKoB,MAAMK,UAEfkM,QAAS,SAAUjU,GACf,GAAIkU,GAAiBlU,EAAEuN,KAClBjH,MAAKC,QAAQkB,YAGlBnB,KAAKoB,MAAM4F,QACXhH,KAAKgJ,QAAQ4E,GACb5N,KAAK6N,SAASD,GACd5N,KAAK8N,gBACL9N,KAAKsJ,0BAETyE,eAAgB,WACZ,GAAIC,GAAgB,CAIpB,OAHAhO,MAAK/F,QAAQqJ,SAAS,iBAAmBxF,EAAe,MAAQL,EAAe,KAAKuF,KAAK,WACrFgL,GAAiBhR,EAAWzD,EAAEyG,OAAO,KAElCiO,KAAKC,KAAKF,IAErBhF,QAAS,SAAU4E,GAAV,GACDO,GAAgBC,EAGPjK,CAFb,IAAIyJ,EAAiB5N,KAAK+N,iBAEtB,IADAK,EAAkBpO,KAAK/F,QAAQqJ,SAAS,0CAA8CjI,EAAkB,KAC/F8I,EAAIiK,EAAgB5T,OAAS,EAAG2J,GAAK,IAC1CgK,EAAiBC,EAAgBvB,GAAG1I,KAChCyJ,EAAiB5N,KAAK+N,mBAFmB5J,IAKzCnE,KAAKqO,UAAUF,IAK/BN,SAAU,SAAUD,GAAV,GACFO,GAAgBG,EAGPnK,CAFb,IAAIyJ,EAAiB5N,KAAK+N,iBAEtB,IADAO,EAAiBtO,KAAK/F,QAAQqJ,SAAS,iBAAoBxF,EAAe,MACjEqG,EAAI,EAAGA,EAAImK,EAAe9T,SAC/B2T,EAAiBG,EAAezB,GAAG1I,KAC/ByJ,EAAiB5N,KAAK+N,mBAAqB/N,KAAKuO,UAAUJ,EAAgBP,IAFvCzJ,OAQnDkK,UAAW,SAAUnK,GACjBA,EAAKvD,OACDX,KAAKoB,OACLpB,KAAKoB,MAAM4I,UAAU1O,KAAK,iBAAoB4I,EAAKhD,KAAK,OAAS,MAAOT,YAAYrB,KAG5FmP,UAAW,SAAUrK,EAAM0J,GACvB,SAAI1J,EAAK1J,QAAUoT,EAAiB5N,KAAK+N,iBAAmB/Q,EAAWkH,GAAM,MACzEA,EAAK1D,OACDR,KAAKoB,OACLpB,KAAKoB,MAAM4I,UAAU1O,KAAK,iBAAoB4I,EAAKhD,KAAK,OAAS,MAAO9G,SAASgF,KAE9E,IAIf0O,cAAe,WACX,GAAIU,GAAgBxO,KAAKoB,MAAM4I,UAAU1G,WAAYmL,EAAezO,KAAK/F,QAAQqJ,SAAS,4BAA6BoL,EAAuBF,EAAczL,OAAO,4BAA6B4L,EAAsBF,EAAa1L,OAAO,WAC1OyL,GAAc5G,IAAI6G,GAAchO,YAAYhC,GAAwB,IAAMC,IAC1EgQ,EAAqBzT,QAAQ2M,IAAI+G,EAAoB1T,SAASb,SAASqE,IACvEiQ,EAAqBxT,OAAO0M,IAAI+G,EAAoBzT,QAAQd,SAASsE,OAG7ElC,EAAMI,GAAGgS,OAAOrS,IAClBE,OAAOD,MAAMmL,QACRlL,OAAOD,OACE,kBAAVlD,SAAwBA,OAAOuV,IAAMvV,OAAS,SAAUwV,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.toolbar.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.toolbar', [\n        'kendo.core',\n        'kendo.userevents',\n        'kendo.popup'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'toolbar',\n        name: 'ToolBar',\n        category: 'web',\n        description: 'The ToolBar widget displays one or more command buttons divided into groups.',\n        depends: ['core']\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, Class = kendo.Class, Widget = kendo.ui.Widget, proxy = $.proxy, isFunction = kendo.isFunction, keys = kendo.keys, outerWidth = kendo._outerWidth, TOOLBAR = 'k-toolbar', BUTTON = 'k-button', OVERFLOW_BUTTON = 'k-overflow-button', TOGGLE_BUTTON = 'k-toggle-button', BUTTON_GROUP = 'k-button-group', SPLIT_BUTTON = 'k-split-button', SEPARATOR = 'k-separator', SPACER_CLASS = 'k-spacer', SPACER = 'spacer', POPUP = 'k-popup', RESIZABLE_TOOLBAR = 'k-toolbar-resizable', STATE_ACTIVE = 'k-state-active', STATE_DISABLED = 'k-state-disabled', STATE_HIDDEN = 'k-state-hidden', GROUP_START = 'k-group-start', GROUP_END = 'k-group-end', PRIMARY = 'k-primary', ICON = 'k-icon', ICON_PREFIX = 'k-i-', BUTTON_ICON = 'k-button-icon', BUTTON_ICON_TEXT = 'k-button-icontext', LIST_CONTAINER = 'k-list-container k-split-container', SPLIT_BUTTON_ARROW = 'k-split-button-arrow', OVERFLOW_ANCHOR = 'k-overflow-anchor', OVERFLOW_CONTAINER = 'k-overflow-container', FIRST_TOOLBAR_VISIBLE = 'k-toolbar-first-visible', LAST_TOOLBAR_VISIBLE = 'k-toolbar-last-visible', CLICK = 'click', TOGGLE = 'toggle', OPEN = 'open', CLOSE = 'close', OVERFLOW_OPEN = 'overflowOpen', OVERFLOW_CLOSE = 'overflowClose', OVERFLOW_NEVER = 'never', OVERFLOW_AUTO = 'auto', OVERFLOW_ALWAYS = 'always', OVERFLOW_HIDDEN = 'k-overflow-hidden', OPTION_LIST_SUFFIX = '_optionlist', KENDO_UID_ATTR = kendo.attr('uid');\n        kendo.toolbar = {};\n        var components = {\n            overflowAnchor: '<div tabindex=\"0\" class=\"k-overflow-anchor k-button\"></div>',\n            overflowContainer: '<ul class=\"k-overflow-container k-list-container\"></ul>'\n        };\n        kendo.toolbar.registerComponent = function (name, toolbar, overflow) {\n            components[name] = {\n                toolbar: toolbar,\n                overflow: overflow\n            };\n        };\n        var Item = kendo.Class.extend({\n            addOverflowAttr: function () {\n                this.element.attr(kendo.attr('overflow'), this.options.overflow || OVERFLOW_AUTO);\n            },\n            addUidAttr: function () {\n                this.element.attr(KENDO_UID_ATTR, this.options.uid);\n            },\n            addIdAttr: function () {\n                if (this.options.id) {\n                    this.element.attr('id', this.options.id);\n                }\n            },\n            addOverflowIdAttr: function () {\n                if (this.options.id) {\n                    this.element.attr('id', this.options.id + '_overflow');\n                }\n            },\n            attributes: function () {\n                if (this.options.attributes) {\n                    this.element.attr(this.options.attributes);\n                }\n            },\n            show: function () {\n                this.element.removeClass(STATE_HIDDEN).show();\n                this.options.hidden = false;\n            },\n            hide: function () {\n                this.element.addClass(STATE_HIDDEN).hide();\n                if (this.overflow && this.overflowHidden) {\n                    this.overflowHidden();\n                }\n                this.options.hidden = true;\n            },\n            remove: function () {\n                this.element.remove();\n            },\n            enable: function (isEnabled) {\n                if (isEnabled === undefined) {\n                    isEnabled = true;\n                }\n                this.element.toggleClass(STATE_DISABLED, !isEnabled);\n                this.options.enable = isEnabled;\n            },\n            twin: function () {\n                var uid = this.element.attr(KENDO_UID_ATTR);\n                if (this.overflow && this.options.splitContainerId) {\n                    return $('#' + this.options.splitContainerId).find('[' + KENDO_UID_ATTR + '=\\'' + uid + '\\']').data(this.options.type);\n                } else if (this.overflow) {\n                    return this.toolbar.element.find('[' + KENDO_UID_ATTR + '=\\'' + uid + '\\']').data(this.options.type);\n                } else if (this.toolbar.options.resizable) {\n                    return this.toolbar.popup.element.find('[' + KENDO_UID_ATTR + '=\\'' + uid + '\\']').data(this.options.type);\n                }\n            }\n        });\n        kendo.toolbar.Item = Item;\n        var Button = Item.extend({\n            init: function (options, toolbar) {\n                var element = options.useButtonTag ? $('<button tabindex=\"0\"></button>') : $('<a href tabindex=\"0\"></a>');\n                this.element = element;\n                this.options = options;\n                this.toolbar = toolbar;\n                this.attributes();\n                if (options.primary) {\n                    element.addClass(PRIMARY);\n                }\n                if (options.togglable) {\n                    element.addClass(TOGGLE_BUTTON);\n                    this.toggle(options.selected);\n                }\n                if (options.url !== undefined && !options.useButtonTag) {\n                    element.attr('href', options.url);\n                    if (options.mobile) {\n                        element.attr(kendo.attr('role'), 'button');\n                    }\n                }\n                if (options.group) {\n                    element.attr(kendo.attr('group'), options.group);\n                    this.group = this.toolbar.addToGroup(this, options.group);\n                }\n                if (!options.togglable && options.click && isFunction(options.click)) {\n                    this.clickHandler = options.click;\n                }\n                if (options.togglable && options.toggle && isFunction(options.toggle)) {\n                    this.toggleHandler = options.toggle;\n                }\n            },\n            toggle: function (state, propagate) {\n                state = !!state;\n                if (this.group && state) {\n                    this.group.select(this);\n                } else if (!this.group) {\n                    this.select(state);\n                }\n                if (propagate && this.twin()) {\n                    this.twin().toggle(state);\n                }\n            },\n            getParentGroup: function () {\n                if (this.options.isChild) {\n                    return this.element.closest('.' + BUTTON_GROUP).data('buttonGroup');\n                }\n            },\n            _addGraphics: function () {\n                var element = this.element, icon = this.options.icon, spriteCssClass = this.options.spriteCssClass, imageUrl = this.options.imageUrl, isEmpty, span, img;\n                if (spriteCssClass || imageUrl || icon) {\n                    isEmpty = true;\n                    element.contents().filter(function () {\n                        return !$(this).hasClass('k-sprite') && !$(this).hasClass(ICON) && !$(this).hasClass('k-image');\n                    }).each(function (idx, el) {\n                        if (el.nodeType == 1 || el.nodeType == 3 && $.trim(el.nodeValue).length > 0) {\n                            isEmpty = false;\n                        }\n                    });\n                    if (isEmpty) {\n                        element.addClass(BUTTON_ICON);\n                    } else {\n                        element.addClass(BUTTON_ICON_TEXT);\n                    }\n                }\n                if (icon) {\n                    span = element.children('span.' + ICON).first();\n                    if (!span[0]) {\n                        span = $('<span class=\"' + ICON + '\"></span>').prependTo(element);\n                    }\n                    span.addClass(ICON_PREFIX + icon);\n                } else if (spriteCssClass) {\n                    span = element.children('span.k-sprite').first();\n                    if (!span[0]) {\n                        span = $('<span class=\"k-sprite ' + ICON + '\"></span>').prependTo(element);\n                    }\n                    span.addClass(spriteCssClass);\n                } else if (imageUrl) {\n                    img = element.children('img.k-image').first();\n                    if (!img[0]) {\n                        img = $('<img alt=\"icon\" class=\"k-image\" />').prependTo(element);\n                    }\n                    img.attr('src', imageUrl);\n                }\n            }\n        });\n        kendo.toolbar.Button = Button;\n        var ToolBarButton = Button.extend({\n            init: function (options, toolbar) {\n                Button.fn.init.call(this, options, toolbar);\n                var element = this.element;\n                element.addClass(BUTTON);\n                this.addIdAttr();\n                if (options.align) {\n                    element.addClass('k-align-' + options.align);\n                }\n                if (options.showText != 'overflow' && options.text) {\n                    if (options.mobile) {\n                        element.html('<span class=\"km-text\">' + options.text + '</span>');\n                    } else {\n                        element.html(options.text);\n                    }\n                }\n                options.hasIcon = options.showIcon != 'overflow' && (options.icon || options.spriteCssClass || options.imageUrl);\n                if (options.hasIcon) {\n                    this._addGraphics();\n                }\n                this.addUidAttr();\n                this.addOverflowAttr();\n                this.enable(options.enable);\n                if (options.hidden) {\n                    this.hide();\n                }\n                this.element.data({\n                    type: 'button',\n                    button: this\n                });\n            },\n            select: function (selected) {\n                if (selected === undefined) {\n                    selected = false;\n                }\n                this.element.toggleClass(STATE_ACTIVE, selected);\n                this.options.selected = selected;\n            }\n        });\n        kendo.toolbar.ToolBarButton = ToolBarButton;\n        var OverflowButton = Button.extend({\n            init: function (options, toolbar) {\n                this.overflow = true;\n                Button.fn.init.call(this, $.extend({}, options), toolbar);\n                var element = this.element;\n                if (options.showText != 'toolbar' && options.text) {\n                    if (options.mobile) {\n                        element.html('<span class=\"km-text\">' + options.text + '</span>');\n                    } else {\n                        element.html('<span class=\"k-text\">' + options.text + '</span>');\n                    }\n                }\n                options.hasIcon = options.showIcon != 'toolbar' && (options.icon || options.spriteCssClass || options.imageUrl);\n                if (options.hasIcon) {\n                    this._addGraphics();\n                }\n                if (!options.isChild) {\n                    this._wrap();\n                }\n                this.addOverflowIdAttr();\n                this.attributes();\n                this.addUidAttr();\n                this.addOverflowAttr();\n                this.enable(options.enable);\n                element.addClass(OVERFLOW_BUTTON + ' ' + BUTTON);\n                if (options.hidden) {\n                    this.hide();\n                }\n                if (options.togglable) {\n                    this.toggle(options.selected);\n                }\n                this.element.data({\n                    type: 'button',\n                    button: this\n                });\n            },\n            _wrap: function () {\n                this.element = this.element.wrap('<li></li>').parent();\n            },\n            overflowHidden: function () {\n                this.element.addClass(OVERFLOW_HIDDEN);\n            },\n            select: function (selected) {\n                if (selected === undefined) {\n                    selected = false;\n                }\n                if (this.options.isChild) {\n                    this.element.toggleClass(STATE_ACTIVE, selected);\n                } else {\n                    this.element.find('.k-button').toggleClass(STATE_ACTIVE, selected);\n                }\n                this.options.selected = selected;\n            }\n        });\n        kendo.toolbar.OverflowButton = OverflowButton;\n        kendo.toolbar.registerComponent('button', ToolBarButton, OverflowButton);\n        var ButtonGroup = Item.extend({\n            createButtons: function (buttonConstructor) {\n                var options = this.options;\n                var items = options.buttons || [];\n                var item;\n                for (var i = 0; i < items.length; i++) {\n                    if (!items[i].uid) {\n                        items[i].uid = kendo.guid();\n                    }\n                    item = new buttonConstructor($.extend({\n                        mobile: options.mobile,\n                        isChild: true,\n                        type: 'button'\n                    }, items[i]), this.toolbar);\n                    item.element.appendTo(this.element);\n                }\n            },\n            refresh: function () {\n                this.element.children().filter(':not(\\'.' + STATE_HIDDEN + '\\'):first').addClass(GROUP_START);\n                this.element.children().filter(':not(\\'.' + STATE_HIDDEN + '\\'):last').addClass(GROUP_END);\n            }\n        });\n        kendo.toolbar.ButtonGroup = ButtonGroup;\n        var ToolBarButtonGroup = ButtonGroup.extend({\n            init: function (options, toolbar) {\n                var element = this.element = $('<div></div>');\n                this.options = options;\n                this.toolbar = toolbar;\n                this.addIdAttr();\n                if (options.align) {\n                    element.addClass('k-align-' + options.align);\n                }\n                this.createButtons(ToolBarButton);\n                this.attributes();\n                this.addUidAttr();\n                this.addOverflowAttr();\n                this.refresh();\n                element.addClass(BUTTON_GROUP);\n                this.element.data({\n                    type: 'buttonGroup',\n                    buttonGroup: this\n                });\n            }\n        });\n        kendo.toolbar.ToolBarButtonGroup = ToolBarButtonGroup;\n        var OverflowButtonGroup = ButtonGroup.extend({\n            init: function (options, toolbar) {\n                var element = this.element = $('<li></li>');\n                this.options = options;\n                this.toolbar = toolbar;\n                this.overflow = true;\n                this.addOverflowIdAttr();\n                this.createButtons(OverflowButton);\n                this.attributes();\n                this.addUidAttr();\n                this.addOverflowAttr();\n                this.refresh();\n                element.addClass((options.mobile ? '' : BUTTON_GROUP) + ' k-overflow-group');\n                this.element.data({\n                    type: 'buttonGroup',\n                    buttonGroup: this\n                });\n            },\n            overflowHidden: function () {\n                this.element.addClass(OVERFLOW_HIDDEN);\n            }\n        });\n        kendo.toolbar.OverflowButtonGroup = OverflowButtonGroup;\n        kendo.toolbar.registerComponent('buttonGroup', ToolBarButtonGroup, OverflowButtonGroup);\n        var ToolBarSplitButton = Item.extend({\n            init: function (options, toolbar) {\n                var element = this.element = $('<div class=\"' + SPLIT_BUTTON + '\" tabindex=\"0\"></div>');\n                this.options = options;\n                this.toolbar = toolbar;\n                this.mainButton = new ToolBarButton($.extend({}, options, { hidden: false }), toolbar);\n                this.arrowButton = $('<a class=\"' + BUTTON + ' ' + SPLIT_BUTTON_ARROW + '\"><span class=\"' + (options.mobile ? 'km-icon km-arrowdown' : 'k-icon k-i-arrow-60-down') + '\"></span></a>');\n                this.popupElement = $('<ul class=\"' + LIST_CONTAINER + '\"></ul>');\n                this.mainButton.element.removeAttr('href tabindex').appendTo(element);\n                this.arrowButton.appendTo(element);\n                this.popupElement.appendTo(element);\n                if (options.align) {\n                    element.addClass('k-align-' + options.align);\n                }\n                if (!options.id) {\n                    options.id = options.uid;\n                }\n                element.attr('id', options.id + '_wrapper');\n                this.addOverflowAttr();\n                this.addUidAttr();\n                this.createMenuButtons();\n                this.createPopup();\n                this._navigatable();\n                this.mainButton.main = true;\n                this.enable(options.enable);\n                if (options.hidden) {\n                    this.hide();\n                }\n                element.data({\n                    type: 'splitButton',\n                    splitButton: this,\n                    kendoPopup: this.popup\n                });\n            },\n            _navigatable: function () {\n                var that = this;\n                that.popupElement.on('keydown', '.' + BUTTON, function (e) {\n                    var li = $(e.target).parent();\n                    e.preventDefault();\n                    if (e.keyCode === keys.ESC || e.keyCode === keys.TAB || e.altKey && e.keyCode === keys.UP) {\n                        that.toggle();\n                        that.focus();\n                    } else if (e.keyCode === keys.DOWN) {\n                        findFocusableSibling(li, 'next').focus();\n                    } else if (e.keyCode === keys.UP) {\n                        findFocusableSibling(li, 'prev').focus();\n                    } else if (e.keyCode === keys.SPACEBAR || e.keyCode === keys.ENTER) {\n                        that.toolbar.userEvents.trigger('tap', { target: $(e.target) });\n                    } else if (e.keyCode === keys.HOME) {\n                        li.parent().find(':kendoFocusable').first().focus();\n                    } else if (e.keyCode === keys.END) {\n                        li.parent().find(':kendoFocusable').last().focus();\n                    }\n                });\n            },\n            createMenuButtons: function () {\n                var options = this.options;\n                var items = options.menuButtons;\n                var item;\n                for (var i = 0; i < items.length; i++) {\n                    item = new ToolBarButton($.extend({\n                        mobile: options.mobile,\n                        type: 'button',\n                        click: options.click\n                    }, items[i]), this.toolbar);\n                    item.element.wrap('<li></li>').parent().appendTo(this.popupElement);\n                }\n            },\n            createPopup: function () {\n                var that = this;\n                var options = this.options;\n                var element = this.element;\n                this.popupElement.attr('id', options.id + OPTION_LIST_SUFFIX).attr(KENDO_UID_ATTR, options.rootUid);\n                if (options.mobile) {\n                    this.popupElement = actionSheetWrap(this.popupElement);\n                }\n                this.popup = this.popupElement.kendoPopup({\n                    appendTo: options.mobile ? $(options.mobile).children('.km-pane') : null,\n                    anchor: element,\n                    isRtl: this.toolbar._isRtl,\n                    copyAnchorStyles: false,\n                    animation: options.animation,\n                    open: function (e) {\n                        var isDefaultPrevented = that.toolbar.trigger(OPEN, { target: element });\n                        if (isDefaultPrevented) {\n                            e.preventDefault();\n                            return;\n                        }\n                        that.adjustPopupWidth(e.sender);\n                    },\n                    activate: function () {\n                        this.element.find(':kendoFocusable').first().focus();\n                    },\n                    close: function (e) {\n                        var isDefaultPrevented = that.toolbar.trigger(CLOSE, { target: element });\n                        if (isDefaultPrevented) {\n                            e.preventDefault();\n                        }\n                        element.focus();\n                    }\n                }).data('kendoPopup');\n                this.popup.element.on(CLICK, 'a.k-button', preventClick);\n            },\n            adjustPopupWidth: function (popup) {\n                var anchor = popup.options.anchor, computedWidth = outerWidth(anchor), width;\n                kendo.wrap(popup.element).addClass('k-split-wrapper');\n                if (popup.element.css('box-sizing') !== 'border-box') {\n                    width = computedWidth - (outerWidth(popup.element) - popup.element.width());\n                } else {\n                    width = computedWidth;\n                }\n                popup.element.css({\n                    fontFamily: anchor.css('font-family'),\n                    'min-width': width\n                });\n            },\n            remove: function () {\n                this.popup.element.off(CLICK, 'a.k-button');\n                this.popup.destroy();\n                this.element.remove();\n            },\n            toggle: function () {\n                if (this.options.enable || this.popup.visible()) {\n                    this.popup.toggle();\n                }\n            },\n            enable: function (isEnabled) {\n                if (isEnabled === undefined) {\n                    isEnabled = true;\n                }\n                this.mainButton.enable(isEnabled);\n                this.element.toggleClass(STATE_DISABLED, !isEnabled);\n                this.options.enable = isEnabled;\n            },\n            focus: function () {\n                this.element.focus();\n            },\n            hide: function () {\n                if (this.popup) {\n                    this.popup.close();\n                }\n                this.element.addClass(STATE_HIDDEN).hide();\n                this.options.hidden = true;\n            },\n            show: function () {\n                this.element.removeClass(STATE_HIDDEN).hide();\n                this.options.hidden = false;\n            }\n        });\n        kendo.toolbar.ToolBarSplitButton = ToolBarSplitButton;\n        var OverflowSplitButton = Item.extend({\n            init: function (options, toolbar) {\n                var element = this.element = $('<li class=\"' + SPLIT_BUTTON + '\"></li>'), items = options.menuButtons, item, splitContainerId;\n                this.options = options;\n                this.toolbar = toolbar;\n                this.overflow = true;\n                splitContainerId = (options.id || options.uid) + OPTION_LIST_SUFFIX;\n                this.mainButton = new OverflowButton($.extend({}, options));\n                this.mainButton.element.appendTo(element);\n                for (var i = 0; i < items.length; i++) {\n                    item = new OverflowButton($.extend({\n                        mobile: options.mobile,\n                        type: 'button',\n                        splitContainerId: splitContainerId\n                    }, items[i]), this.toolbar);\n                    item.element.appendTo(element);\n                }\n                this.addUidAttr();\n                this.addOverflowAttr();\n                this.mainButton.main = true;\n                element.data({\n                    type: 'splitButton',\n                    splitButton: this\n                });\n            },\n            overflowHidden: function () {\n                this.element.addClass(OVERFLOW_HIDDEN);\n            }\n        });\n        kendo.toolbar.OverflowSplitButton = OverflowSplitButton;\n        kendo.toolbar.registerComponent('splitButton', ToolBarSplitButton, OverflowSplitButton);\n        var ToolBarSeparator = Item.extend({\n            init: function (options, toolbar) {\n                var element = this.element = $('<div>&nbsp;</div>');\n                this.element = element;\n                this.options = options;\n                this.toolbar = toolbar;\n                this.attributes();\n                this.addIdAttr();\n                this.addUidAttr();\n                this.addOverflowAttr();\n                element.addClass(SEPARATOR);\n                element.data({\n                    type: 'separator',\n                    separator: this\n                });\n            }\n        });\n        var OverflowSeparator = Item.extend({\n            init: function (options, toolbar) {\n                var element = this.element = $('<li>&nbsp;</li>');\n                this.element = element;\n                this.options = options;\n                this.toolbar = toolbar;\n                this.overflow = true;\n                this.attributes();\n                this.addUidAttr();\n                this.addOverflowIdAttr();\n                element.addClass(SEPARATOR);\n                element.data({\n                    type: 'separator',\n                    separator: this\n                });\n            },\n            overflowHidden: function () {\n                this.element.addClass(OVERFLOW_HIDDEN);\n            }\n        });\n        kendo.toolbar.registerComponent('separator', ToolBarSeparator, OverflowSeparator);\n        var ToolBarSpacer = Item.extend({\n            init: function (options, toolbar) {\n                var element = this.element = $('<div>&nbsp;</div>');\n                this.element = element;\n                this.options = options;\n                this.toolbar = toolbar;\n                element.addClass(SPACER_CLASS);\n                element.data({ type: SPACER });\n            }\n        });\n        kendo.toolbar.registerComponent(SPACER, ToolBarSpacer);\n        var TemplateItem = Item.extend({\n            init: function (template, options, toolbar) {\n                var element = isFunction(template) ? template(options) : template;\n                if (!(element instanceof jQuery)) {\n                    element = $('<div></div>').html(element);\n                } else {\n                    element = element.wrap('<div></div>').parent();\n                }\n                this.element = element;\n                this.options = options;\n                this.options.type = 'template';\n                this.toolbar = toolbar;\n                this.attributes();\n                this.addUidAttr();\n                this.addIdAttr();\n                this.addOverflowAttr();\n                element.data({\n                    type: 'template',\n                    template: this\n                });\n            }\n        });\n        kendo.toolbar.TemplateItem = TemplateItem;\n        var OverflowTemplateItem = Item.extend({\n            init: function (template, options, toolbar) {\n                var element = isFunction(template) ? $(template(options)) : $(template);\n                if (!(element instanceof jQuery)) {\n                    element = $('<li></li>').html(element);\n                } else {\n                    element = element.wrap('<li></li>').parent();\n                }\n                this.element = element;\n                this.options = options;\n                this.options.type = 'template';\n                this.toolbar = toolbar;\n                this.overflow = true;\n                this.attributes();\n                this.addUidAttr();\n                this.addOverflowIdAttr();\n                this.addOverflowAttr();\n                element.data({\n                    type: 'template',\n                    template: this\n                });\n            },\n            overflowHidden: function () {\n                this.element.addClass(OVERFLOW_HIDDEN);\n            }\n        });\n        kendo.toolbar.OverflowTemplateItem = OverflowTemplateItem;\n        function toggleActive(e) {\n            if (!e.target.is('.k-toggle-button')) {\n                e.target.toggleClass(STATE_ACTIVE, e.type == 'press');\n            }\n        }\n        function actionSheetWrap(element) {\n            element = $(element);\n            return element.hasClass('km-actionsheet') ? element.closest('.km-popup-wrapper') : element.addClass('km-widget km-actionsheet').wrap('<div class=\"km-actionsheet-wrapper km-actionsheet-tablet km-widget km-popup\"></div>').parent().wrap('<div class=\"km-popup-wrapper k-popup\"></div>').parent();\n        }\n        function preventClick(e) {\n            if ($(e.target).closest('a.k-button').length) {\n                e.preventDefault();\n            }\n        }\n        function findFocusableSibling(element, dir) {\n            var getSibling = dir === 'next' ? $.fn.next : $.fn.prev;\n            var getter = dir === 'next' ? $.fn.first : $.fn.last;\n            var candidate = getSibling.call(element);\n            if (!candidate.length && element.is('.' + OVERFLOW_ANCHOR)) {\n                return element;\n            }\n            if (candidate.is(':kendoFocusable') || !candidate.length) {\n                return candidate;\n            }\n            if (candidate.find(':kendoFocusable').length) {\n                return getter.call(candidate.find(':kendoFocusable'));\n            }\n            return findFocusableSibling(candidate, dir);\n        }\n        var Group = Class.extend({\n            init: function (name) {\n                this.name = name;\n                this.buttons = [];\n            },\n            add: function (button) {\n                this.buttons[this.buttons.length] = button;\n            },\n            remove: function (button) {\n                var index = $.inArray(button, this.buttons);\n                this.buttons.splice(index, 1);\n            },\n            select: function (button) {\n                var tmp;\n                for (var i = 0; i < this.buttons.length; i++) {\n                    tmp = this.buttons[i];\n                    tmp.select(false);\n                }\n                button.select(true);\n                if (button.twin()) {\n                    button.twin().select(true);\n                }\n            }\n        });\n        var ToolBar = Widget.extend({\n            init: function (element, options) {\n                var that = this;\n                Widget.fn.init.call(that, element, options);\n                options = that.options;\n                element = that.wrapper = that.element;\n                element.addClass(TOOLBAR + ' k-widget');\n                this.uid = kendo.guid();\n                this._isRtl = kendo.support.isRtl(element);\n                this._groups = {};\n                element.attr(KENDO_UID_ATTR, this.uid);\n                that.isMobile = typeof options.mobile === 'boolean' ? options.mobile : that.element.closest('.km-root')[0];\n                that.animation = that.isMobile ? { open: { effects: 'fade' } } : {};\n                if (that.isMobile) {\n                    element.addClass('km-widget');\n                    ICON = 'km-icon';\n                    ICON_PREFIX = 'km-';\n                    BUTTON = 'km-button';\n                    BUTTON_GROUP = 'km-buttongroup';\n                    STATE_ACTIVE = 'km-state-active';\n                    STATE_DISABLED = 'km-state-disabled';\n                }\n                if (options.resizable) {\n                    that._renderOverflow();\n                    element.addClass(RESIZABLE_TOOLBAR);\n                    that.overflowUserEvents = new kendo.UserEvents(that.element, {\n                        threshold: 5,\n                        allowSelection: true,\n                        filter: '.' + OVERFLOW_ANCHOR,\n                        tap: proxy(that._toggleOverflow, that)\n                    });\n                    that._resizeHandler = kendo.onResize(function () {\n                        that.resize();\n                    });\n                } else {\n                    that.popup = { element: $([]) };\n                }\n                if (options.items && options.items.length) {\n                    for (var i = 0; i < options.items.length; i++) {\n                        that.add(options.items[i]);\n                    }\n                    if (options.resizable) {\n                        that._shrink(that.element.innerWidth());\n                    }\n                }\n                that.userEvents = new kendo.UserEvents(document, {\n                    threshold: 5,\n                    allowSelection: true,\n                    filter: '[' + KENDO_UID_ATTR + '=' + this.uid + '] a.' + BUTTON + ', ' + '[' + KENDO_UID_ATTR + '=' + this.uid + '] .' + OVERFLOW_BUTTON,\n                    tap: proxy(that._buttonClick, that),\n                    press: toggleActive,\n                    release: toggleActive\n                });\n                that.element.on(CLICK, 'a.k-button', preventClick);\n                that._navigatable();\n                if (options.resizable) {\n                    that.popup.element.on(CLICK, +'a.k-button', preventClick);\n                }\n                if (options.resizable) {\n                    this._toggleOverflowAnchor();\n                }\n                kendo.notify(that);\n            },\n            events: [\n                CLICK,\n                TOGGLE,\n                OPEN,\n                CLOSE,\n                OVERFLOW_OPEN,\n                OVERFLOW_CLOSE\n            ],\n            options: {\n                name: 'ToolBar',\n                items: [],\n                resizable: true,\n                mobile: null\n            },\n            addToGroup: function (button, groupName) {\n                var group;\n                if (!this._groups[groupName]) {\n                    group = this._groups[groupName] = new Group();\n                } else {\n                    group = this._groups[groupName];\n                }\n                group.add(button);\n                return group;\n            },\n            destroy: function () {\n                var that = this;\n                that.element.find('.' + SPLIT_BUTTON).each(function (idx, element) {\n                    $(element).data('kendoPopup').destroy();\n                });\n                that.element.off(CLICK, 'a.k-button');\n                that.userEvents.destroy();\n                if (that.options.resizable) {\n                    kendo.unbindResize(that._resizeHandler);\n                    that.overflowUserEvents.destroy();\n                    that.popup.element.off(CLICK, 'a.k-button');\n                    that.popup.destroy();\n                }\n                Widget.fn.destroy.call(that);\n            },\n            add: function (options) {\n                var component = components[options.type], template = options.template, tool, that = this, itemClasses = that.isMobile ? '' : 'k-item k-state-default', overflowTemplate = options.overflowTemplate, overflowTool;\n                $.extend(options, {\n                    uid: kendo.guid(),\n                    animation: that.animation,\n                    mobile: that.isMobile,\n                    rootUid: that.uid\n                });\n                if (options.menuButtons) {\n                    for (var i = 0; i < options.menuButtons.length; i++) {\n                        $.extend(options.menuButtons[i], { uid: kendo.guid() });\n                    }\n                }\n                if (template && !overflowTemplate || options.type === SPACER) {\n                    options.overflow = OVERFLOW_NEVER;\n                } else if (!options.overflow) {\n                    options.overflow = OVERFLOW_AUTO;\n                }\n                if (options.overflow !== OVERFLOW_NEVER && that.options.resizable) {\n                    if (overflowTemplate) {\n                        overflowTool = new OverflowTemplateItem(overflowTemplate, options, that);\n                    } else if (component) {\n                        overflowTool = new component.overflow(options, that);\n                        overflowTool.element.addClass(itemClasses);\n                    }\n                    if (overflowTool) {\n                        if (options.overflow === OVERFLOW_AUTO) {\n                            overflowTool.overflowHidden();\n                        }\n                        overflowTool.element.appendTo(that.popup.container);\n                        that.angular('compile', function () {\n                            return { elements: overflowTool.element.get() };\n                        });\n                    }\n                }\n                if (options.overflow !== OVERFLOW_ALWAYS) {\n                    if (template) {\n                        tool = new TemplateItem(template, options, that);\n                    } else if (component) {\n                        tool = new component.toolbar(options, that);\n                    }\n                    if (tool) {\n                        tool.element.appendTo(that.element);\n                        that.angular('compile', function () {\n                            return { elements: tool.element.get() };\n                        });\n                    }\n                }\n            },\n            _getItem: function (candidate) {\n                var element, toolbarItem, overflowItem, isResizable = this.options.resizable, type;\n                element = this.element.find(candidate);\n                if (!element.length) {\n                    element = $('.k-split-container[data-uid=' + this.uid + ']').find(candidate);\n                }\n                type = element.length ? element.data('type') : '';\n                toolbarItem = element.data(type);\n                if (toolbarItem) {\n                    if (toolbarItem.main) {\n                        element = element.parent('.' + SPLIT_BUTTON);\n                        type = 'splitButton';\n                        toolbarItem = element.data(type);\n                    }\n                    if (isResizable) {\n                        overflowItem = toolbarItem.twin();\n                    }\n                } else if (isResizable) {\n                    element = this.popup.element.find(candidate);\n                    type = element.length ? element.data('type') : '';\n                    overflowItem = element.data(type);\n                    if (overflowItem && overflowItem.main) {\n                        element = element.parent('.' + SPLIT_BUTTON);\n                        type = 'splitButton';\n                        overflowItem = element.data(type);\n                    }\n                }\n                return {\n                    type: type,\n                    toolbar: toolbarItem,\n                    overflow: overflowItem\n                };\n            },\n            remove: function (candidate) {\n                var item = this._getItem(candidate);\n                if (item.toolbar) {\n                    item.toolbar.remove();\n                }\n                if (item.overflow) {\n                    item.overflow.remove();\n                }\n                this.resize(true);\n            },\n            hide: function (candidate) {\n                var item = this._getItem(candidate);\n                var buttonGroupInstance;\n                if (item.toolbar) {\n                    if (item.toolbar.options.type === 'button' && item.toolbar.options.isChild) {\n                        buttonGroupInstance = item.toolbar.getParentGroup();\n                        item.toolbar.hide();\n                        if (buttonGroupInstance) {\n                            buttonGroupInstance.refresh();\n                        }\n                    } else if (!item.toolbar.options.hidden) {\n                        item.toolbar.hide();\n                    }\n                }\n                if (item.overflow) {\n                    if (item.overflow.options.type === 'button' && item.overflow.options.isChild) {\n                        buttonGroupInstance = item.overflow.getParentGroup();\n                        item.overflow.hide();\n                        if (buttonGroupInstance) {\n                            buttonGroupInstance.refresh();\n                        }\n                    } else if (!item.overflow.options.hidden) {\n                        item.overflow.hide();\n                    }\n                }\n                this.resize(true);\n            },\n            show: function (candidate) {\n                var item = this._getItem(candidate);\n                if (item.toolbar) {\n                    if (item.toolbar.options.type === 'button' && item.toolbar.options.isChild) {\n                        item.toolbar.show();\n                        item.toolbar.getParentGroup().refresh();\n                    } else if (item.toolbar.options.hidden) {\n                        item.toolbar.show();\n                    }\n                }\n                if (item.overflow) {\n                    if (item.overflow.options.type === 'button' && item.overflow.options.isChild) {\n                        item.toolbar.show();\n                        item.overflow.getParentGroup().refresh();\n                    } else if (item.overflow.options.hidden) {\n                        item.overflow.show();\n                    }\n                }\n                this.resize(true);\n            },\n            enable: function (element, enable) {\n                var item = this._getItem(element);\n                if (typeof enable == 'undefined') {\n                    enable = true;\n                }\n                if (item.toolbar) {\n                    item.toolbar.enable(enable);\n                }\n                if (item.overflow) {\n                    item.overflow.enable(enable);\n                }\n            },\n            getSelectedFromGroup: function (groupName) {\n                return this.element.find('.' + TOGGLE_BUTTON + '[data-group=\\'' + groupName + '\\']').filter('.' + STATE_ACTIVE);\n            },\n            toggle: function (button, checked) {\n                var element = $(button), item = element.data('button');\n                if (item.options.togglable) {\n                    if (checked === undefined) {\n                        checked = true;\n                    }\n                    item.toggle(checked, true);\n                }\n            },\n            _renderOverflow: function () {\n                var that = this, overflowContainer = components.overflowContainer, isRtl = that._isRtl, horizontalDirection = isRtl ? 'left' : 'right';\n                that.overflowAnchor = $(components.overflowAnchor).addClass(BUTTON);\n                that.element.append(that.overflowAnchor);\n                if (that.isMobile) {\n                    that.overflowAnchor.append('<span class=\"km-icon km-more\"></span>');\n                    overflowContainer = actionSheetWrap(overflowContainer);\n                } else {\n                    that.overflowAnchor.append('<span class=\"k-icon k-i-more-vertical\"></span>');\n                }\n                that.popup = new kendo.ui.Popup(overflowContainer, {\n                    origin: 'bottom ' + horizontalDirection,\n                    position: 'top ' + horizontalDirection,\n                    anchor: that.overflowAnchor,\n                    isRtl: isRtl,\n                    animation: that.animation,\n                    appendTo: that.isMobile ? $(that.isMobile).children('.km-pane') : null,\n                    copyAnchorStyles: false,\n                    open: function (e) {\n                        var wrapper = kendo.wrap(that.popup.element).addClass('k-overflow-wrapper');\n                        if (!that.isMobile) {\n                            wrapper.css('margin-left', (isRtl ? -1 : 1) * ((outerWidth(wrapper) - wrapper.width()) / 2 + 1));\n                        } else {\n                            that.popup.container.css('max-height', parseFloat($('.km-content:visible').innerHeight()) - 15 + 'px');\n                        }\n                        if (that.trigger(OVERFLOW_OPEN)) {\n                            e.preventDefault();\n                        }\n                    },\n                    activate: function () {\n                        this.element.find(':kendoFocusable').first().focus();\n                    },\n                    close: function (e) {\n                        if (that.trigger(OVERFLOW_CLOSE)) {\n                            e.preventDefault();\n                        }\n                        this.element.focus();\n                    }\n                });\n                that.popup.element.on('keydown', '.' + BUTTON, function (e) {\n                    var target = $(e.target), li = target.parent(), isComplexTool = li.is('.' + BUTTON_GROUP) || li.is('.' + SPLIT_BUTTON), element;\n                    e.preventDefault();\n                    if (e.keyCode === keys.ESC || e.keyCode === keys.TAB || e.altKey && e.keyCode === keys.UP) {\n                        that._toggleOverflow();\n                        that.overflowAnchor.focus();\n                    } else if (e.keyCode === keys.DOWN) {\n                        element = !isComplexTool || isComplexTool && target.is(':last-child') ? li : target;\n                        findFocusableSibling(element, 'next').focus();\n                    } else if (e.keyCode === keys.UP) {\n                        element = !isComplexTool || isComplexTool && target.is(':first-child') ? li : target;\n                        findFocusableSibling(element, 'prev').focus();\n                    } else if (e.keyCode === keys.SPACEBAR || e.keyCode === keys.ENTER) {\n                        that.userEvents.trigger('tap', { target: $(e.target) });\n                        that.overflowAnchor.focus();\n                    } else if (e.keyCode === keys.HOME) {\n                        li.parent().find(':kendoFocusable').first().focus();\n                    } else if (e.keyCode === keys.END) {\n                        li.parent().find(':kendoFocusable').last().focus();\n                    }\n                });\n                if (that.isMobile) {\n                    that.popup.container = that.popup.element.find('.' + OVERFLOW_CONTAINER);\n                } else {\n                    that.popup.container = that.popup.element;\n                }\n                that.popup.container.attr(KENDO_UID_ATTR, this.uid);\n            },\n            _toggleOverflowAnchor: function () {\n                var hasVisibleChildren = false;\n                var paddingEnd = this._isRtl ? 'padding-left' : 'padding-right';\n                if (this.options.mobile) {\n                    hasVisibleChildren = this.popup.element.find('.' + OVERFLOW_CONTAINER).children(':not(.' + OVERFLOW_HIDDEN + ', .' + POPUP + ')').length > 0;\n                } else {\n                    hasVisibleChildren = this.popup.element.children(':not(.' + OVERFLOW_HIDDEN + ', .' + POPUP + ')').length > 0;\n                }\n                if (hasVisibleChildren) {\n                    this.overflowAnchor.css({\n                        visibility: 'visible',\n                        width: ''\n                    });\n                    this.wrapper.css(paddingEnd, this.overflowAnchor.outerWidth(true));\n                } else {\n                    this.overflowAnchor.css({\n                        visibility: 'hidden',\n                        width: '1px'\n                    });\n                    this.wrapper.css(paddingEnd, '');\n                }\n            },\n            _buttonClick: function (e) {\n                var that = this, popup, target, item, splitContainer, isSplitButtonArrow = e.target.closest('.' + SPLIT_BUTTON_ARROW).length, handler, eventData, urlTarget;\n                e.preventDefault();\n                if (isSplitButtonArrow) {\n                    that._toggle(e);\n                    return;\n                }\n                target = $(e.target).closest('.' + BUTTON, that.element);\n                if (target.hasClass(OVERFLOW_ANCHOR)) {\n                    return;\n                }\n                item = target.data('button');\n                if (!item && that.popup) {\n                    target = $(e.target).closest('.' + OVERFLOW_BUTTON, that.popup.container);\n                    item = target.parent('li').data('button');\n                }\n                if (!item || !item.options.enable) {\n                    return;\n                }\n                if (item.options.togglable) {\n                    handler = isFunction(item.toggleHandler) ? item.toggleHandler : null;\n                    item.toggle(!item.options.selected, true);\n                    eventData = {\n                        target: target,\n                        group: item.options.group,\n                        checked: item.options.selected,\n                        id: item.options.id,\n                        item: item\n                    };\n                    if (handler) {\n                        handler.call(that, eventData);\n                    }\n                    that.trigger(TOGGLE, eventData);\n                } else {\n                    handler = isFunction(item.clickHandler) ? item.clickHandler : null;\n                    eventData = {\n                        sender: that,\n                        target: target,\n                        id: item.options.id,\n                        item: item\n                    };\n                    if (handler) {\n                        handler.call(that, eventData);\n                    }\n                    that.trigger(CLICK, eventData);\n                }\n                if (item.options.url) {\n                    if (item.options.attributes && item.options.attributes.target) {\n                        urlTarget = item.options.attributes.target;\n                    }\n                    window.open(item.options.url, urlTarget || '_self');\n                }\n                if (target.hasClass(OVERFLOW_BUTTON)) {\n                    that.popup.close();\n                }\n                splitContainer = target.closest('.k-split-container');\n                if (splitContainer[0]) {\n                    popup = splitContainer.data('kendoPopup');\n                    (popup ? popup : splitContainer.parents('.km-popup-wrapper').data('kendoPopup')).close();\n                }\n            },\n            _navigatable: function () {\n                var that = this;\n                that.element.attr('tabindex', 0).on('focusin', function (ev) {\n                    var target = $(ev.target);\n                    var element = $(this).find(':kendoFocusable:first');\n                    if (!target.is('.' + TOOLBAR) || element.length === 0) {\n                        return;\n                    }\n                    if (element.is('.' + OVERFLOW_ANCHOR)) {\n                        element = findFocusableSibling(element, 'next');\n                    }\n                    if (element.length) {\n                        element[0].focus();\n                    }\n                }).on('keydown', proxy(that._keydown, that));\n            },\n            _keydown: function (e) {\n                var target = $(e.target), keyCode = e.keyCode, items = this.element.children(':not(.k-separator):visible'), direction = this._isRtl ? -1 : 1;\n                if (keyCode === keys.TAB) {\n                    var element = target.parentsUntil(this.element).last(), lastHasFocus = false, firstHasFocus = false, isOnlyOverflowAnchor = false;\n                    if (!items.not('.' + OVERFLOW_ANCHOR).length) {\n                        isOnlyOverflowAnchor = true;\n                    }\n                    if (!element.length) {\n                        element = target;\n                    }\n                    if (element.is('.' + OVERFLOW_ANCHOR) && !isOnlyOverflowAnchor) {\n                        var lastItemNotOverflowAnchor = items.last();\n                        if (e.shiftKey) {\n                            e.preventDefault();\n                        }\n                        if (lastItemNotOverflowAnchor.is(':kendoFocusable')) {\n                            items.last().focus();\n                        } else {\n                            items.last().find(':kendoFocusable').last().focus();\n                        }\n                    }\n                    if (!e.shiftKey && items.index(element) === items.length - 1) {\n                        if (element.is('.' + BUTTON_GROUP)) {\n                            lastHasFocus = target.is(':last-child');\n                        } else {\n                            lastHasFocus = true;\n                        }\n                    }\n                    var isFirstTool = items.index(element) === items.not('.k-overflow-anchor').first().index();\n                    if (e.shiftKey && isFirstTool) {\n                        if (element.is('.' + BUTTON_GROUP)) {\n                            firstHasFocus = target.is(':first-child');\n                        } else {\n                            firstHasFocus = true;\n                        }\n                    }\n                    if (lastHasFocus && this.overflowAnchor && this.overflowAnchor.css('visibility') !== 'hidden' && !isOnlyOverflowAnchor) {\n                        e.preventDefault();\n                        this.overflowAnchor.focus();\n                    }\n                    if (firstHasFocus || isOnlyOverflowAnchor && e.shiftKey) {\n                        e.preventDefault();\n                        var prevFocusable = this._getPrevFocusable(this.wrapper);\n                        if (prevFocusable) {\n                            prevFocusable.focus();\n                        }\n                    }\n                    this._preventNextFocus = false;\n                }\n                if (e.altKey && keyCode === keys.DOWN) {\n                    var splitButton = $(document.activeElement).data('splitButton');\n                    var isOverflowAnchor = $(document.activeElement).is('.' + OVERFLOW_ANCHOR);\n                    if (splitButton) {\n                        splitButton.toggle();\n                    } else if (isOverflowAnchor) {\n                        this._toggleOverflow();\n                    }\n                    return;\n                }\n                if ((keyCode === keys.SPACEBAR || keyCode === keys.ENTER) && !target.is('input, checkbox')) {\n                    if (keyCode === keys.SPACEBAR) {\n                        e.preventDefault();\n                    }\n                    if (target.is('.' + SPLIT_BUTTON)) {\n                        target = target.children().first();\n                        this.userEvents.trigger('tap', { target: target });\n                    } else if (keyCode === keys.SPACEBAR) {\n                        this.userEvents.trigger('tap', { target: target });\n                    }\n                    return;\n                }\n                if (keyCode === keys.HOME) {\n                    if (target.is('.k-dropdown') || target.is('input')) {\n                        return;\n                    }\n                    if (this.overflowAnchor) {\n                        items.eq(1).focus();\n                    } else {\n                        items.first().focus();\n                    }\n                    e.preventDefault();\n                } else if (keyCode === keys.END) {\n                    if (target.is('.k-dropdown') || target.is('input')) {\n                        return;\n                    }\n                    if (this.overflowAnchor && $(this.overflowAnchor).css('visibility') != 'hidden') {\n                        this.overflowAnchor.focus();\n                    } else {\n                        items.last().focus();\n                    }\n                    e.preventDefault();\n                } else if (keyCode === keys.RIGHT && !this._preventNextFocus && !target.is('input, select, .k-dropdown, .k-colorpicker') && this._getNextElement(e.target, 1 * direction)) {\n                    this._getNextElement(e.target, 1 * direction).focus();\n                    e.preventDefault();\n                } else if (keyCode === keys.LEFT && !this._preventNextFocus && !target.is('input, select, .k-dropdown, .k-colorpicker') && this._getNextElement(e.target, -1 * direction)) {\n                    this._getNextElement(e.target, -1 * direction).focus();\n                    e.preventDefault();\n                }\n            },\n            _getNextElement: function (item, direction) {\n                var items = this.element.children(':not(.k-separator):visible');\n                var itemIndex = items.index(item) === -1 ? items.index(item.parentElement) : items.index(item);\n                var startIndex = this.overflowAnchor ? 1 : 0;\n                var directionNumber = direction;\n                var searchIndex = direction === 1 ? items.length - 1 : startIndex;\n                var index = direction === 1 ? startIndex : items.length - 1;\n                var focusableItem = items[itemIndex + direction];\n                this._preventNextFocus = false;\n                if ($(item).closest('.' + BUTTON_GROUP).length && !$(item).is(direction === 1 ? ':last-child' : ':first-child')) {\n                    return $(item).closest('.' + BUTTON_GROUP).children()[$(item).closest('.' + BUTTON_GROUP).children().index(item) + direction];\n                }\n                if (this.overflowAnchor && item === this.overflowAnchor[0] && direction === -1) {\n                    focusableItem = items[items.length - 1];\n                }\n                if (itemIndex === searchIndex) {\n                    focusableItem = !this.overflowAnchor || this.overflowAnchor && $(this.overflowAnchor).css('visibility') === 'hidden' ? items[index] : this.overflowAnchor;\n                }\n                while (!$(focusableItem).is(':kendoFocusable')) {\n                    if (direction === -1 && $(focusableItem).closest('.' + BUTTON_GROUP).length) {\n                        focusableItem = $(focusableItem).children(':not(label, div)').last();\n                    } else {\n                        focusableItem = $(focusableItem).children(':not(label, div)').first();\n                    }\n                    if (!focusableItem.length) {\n                        directionNumber = directionNumber + direction;\n                        focusableItem = items[itemIndex + directionNumber];\n                        if (!focusableItem) {\n                            return this.overflowAnchor;\n                        }\n                    }\n                    this._preventNextFocus = $(focusableItem).closest('.' + BUTTON_GROUP).length ? false : true;\n                }\n                return focusableItem;\n            },\n            _getPrevFocusable: function (element) {\n                if (element.is('html')) {\n                    return element;\n                }\n                var elementToFocus, prevElement, prevElements = element.prevAll();\n                prevElements.each(function () {\n                    prevElement = $(this);\n                    if (prevElement.is(':kendoFocusable')) {\n                        elementToFocus = prevElement;\n                        return false;\n                    } else if (prevElement.find(':kendoFocusable').length > 0) {\n                        elementToFocus = prevElement.find(':kendoFocusable').last();\n                        return false;\n                    }\n                });\n                if (elementToFocus) {\n                    return elementToFocus;\n                } else {\n                    return this._getPrevFocusable(element.parent());\n                }\n            },\n            _toggle: function (e) {\n                var splitButton = $(e.target).closest('.' + SPLIT_BUTTON).data('splitButton');\n                e.preventDefault();\n                if (!splitButton.options.enable) {\n                    return;\n                }\n                splitButton.toggle();\n            },\n            _toggleOverflow: function () {\n                this.popup.toggle();\n            },\n            _resize: function (e) {\n                var containerWidth = e.width;\n                if (!this.options.resizable) {\n                    return;\n                }\n                this.popup.close();\n                this._shrink(containerWidth);\n                this._stretch(containerWidth);\n                this._markVisibles();\n                this._toggleOverflowAnchor();\n            },\n            _childrenWidth: function () {\n                var childrenWidth = 0;\n                this.element.children(':visible:not(.' + STATE_HIDDEN + ', .' + SPACER_CLASS + ')').each(function () {\n                    childrenWidth += outerWidth($(this), true);\n                });\n                return Math.ceil(childrenWidth);\n            },\n            _shrink: function (containerWidth) {\n                var commandElement, visibleCommands;\n                if (containerWidth < this._childrenWidth()) {\n                    visibleCommands = this.element.children(':visible:not([data-overflow=\\'never\\'], .' + OVERFLOW_ANCHOR + ')');\n                    for (var i = visibleCommands.length - 1; i >= 0; i--) {\n                        commandElement = visibleCommands.eq(i);\n                        if (containerWidth > this._childrenWidth()) {\n                            break;\n                        } else {\n                            this._hideItem(commandElement);\n                        }\n                    }\n                }\n            },\n            _stretch: function (containerWidth) {\n                var commandElement, hiddenCommands;\n                if (containerWidth > this._childrenWidth()) {\n                    hiddenCommands = this.element.children(':hidden:not(\\'.' + STATE_HIDDEN + '\\')');\n                    for (var i = 0; i < hiddenCommands.length; i++) {\n                        commandElement = hiddenCommands.eq(i);\n                        if (containerWidth < this._childrenWidth() || !this._showItem(commandElement, containerWidth)) {\n                            break;\n                        }\n                    }\n                }\n            },\n            _hideItem: function (item) {\n                item.hide();\n                if (this.popup) {\n                    this.popup.container.find('>li[data-uid=\\'' + item.data('uid') + '\\']').removeClass(OVERFLOW_HIDDEN);\n                }\n            },\n            _showItem: function (item, containerWidth) {\n                if (item.length && containerWidth > this._childrenWidth() + outerWidth(item, true)) {\n                    item.show();\n                    if (this.popup) {\n                        this.popup.container.find('>li[data-uid=\\'' + item.data('uid') + '\\']').addClass(OVERFLOW_HIDDEN);\n                    }\n                    return true;\n                }\n                return false;\n            },\n            _markVisibles: function () {\n                var overflowItems = this.popup.container.children(), toolbarItems = this.element.children(':not(.k-overflow-anchor)'), visibleOverflowItems = overflowItems.filter(':not(.k-overflow-hidden)'), visibleToolbarItems = toolbarItems.filter(':visible');\n                overflowItems.add(toolbarItems).removeClass(FIRST_TOOLBAR_VISIBLE + ' ' + LAST_TOOLBAR_VISIBLE);\n                visibleOverflowItems.first().add(visibleToolbarItems.first()).addClass(FIRST_TOOLBAR_VISIBLE);\n                visibleOverflowItems.last().add(visibleToolbarItems.last()).addClass(LAST_TOOLBAR_VISIBLE);\n            }\n        });\n        kendo.ui.plugin(ToolBar);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}