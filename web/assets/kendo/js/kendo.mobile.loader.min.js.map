{"version": 3, "sources": ["kendo.mobile.loader.js"], "names": ["f", "define", "$", "undefined", "kendo", "window", "ui", "mobile", "Widget", "CAPTURE_EVENTS", "map", "eventMap", "value", "join", "split", "Loader", "extend", "init", "container", "options", "that", "this", "element", "fn", "call", "captureEvents", "_attachCapture", "append", "loading", "hide", "appendTo", "name", "timeout", "show", "clearTimeout", "_loading", "setTimeout", "changeMessage", "message", "find", "html", "transition", "css", "transitionDone", "capture", "e", "preventDefault", "i", "length", "addEventListener", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,uBAAwB,cAAeD,IAChD,WAsEE,MA7DC,UAAUE,EAAGC,GAAb,GACOC,GAAQC,OAAOD,MAAOE,EAAKF,EAAMG,OAAOD,GAAIE,EAASF,EAAGE,OAAQC,EAAiBP,EAAEQ,IAAIN,EAAMO,SAAU,SAAUC,GAC7G,MAAOA,KACRC,KAAK,KAAKC,MAAM,KACnBC,EAASP,EAAOQ,QAChBC,KAAM,SAAUC,EAAWC,GACvB,GAAIC,GAAOC,KAAMC,EAAUpB,EAAE,mJAC7BM,GAAOe,GAAGN,KAAKO,KAAKJ,EAAME,EAASH,GACnCC,EAAKF,UAAYA,EACjBE,EAAKK,eAAgB,EACrBL,EAAKM,iBACLJ,EAAQK,OAAOP,EAAKD,QAAQS,SAASC,OAAOC,SAASZ,IAEzDC,SACIY,KAAM,SACNH,QAAS,sBACTI,QAAS,KAEbC,KAAM,WACF,GAAIb,GAAOC,IACXa,cAAad,EAAKe,UACdf,EAAKD,QAAQS,WAAY,IAG7BR,EAAKK,eAAgB,EACrBL,EAAKe,SAAWC,WAAW,WACvBhB,EAAKE,QAAQW,QACdb,EAAKD,QAAQa,WAEpBH,KAAM,WACFR,KAAKI,eAAgB,EACrBS,aAAab,KAAKc,UAClBd,KAAKC,QAAQO,QAEjBQ,cAAe,SAAUC,GACrBjB,KAAKF,QAAQS,QAAUU,EACvBjB,KAAKC,QAAQiB,KAAK,OAAOC,KAAKF,IAElCG,WAAY,WACRpB,KAAKI,eAAgB,EACrBJ,KAAKH,UAAUwB,IAAI,iBAAkB,SAEzCC,eAAgB,WACZtB,KAAKI,eAAgB,EACrBJ,KAAKH,UAAUwB,IAAI,iBAAkB,KAEzChB,eAAgB,WAGZ,QAASkB,GAAQC,GACTzB,EAAKK,eACLoB,EAAEC,iBALE,GAQHC,GAPL3B,EAAOC,IAOX,KANAD,EAAKK,eAAgB,EAMZsB,EAAI,EAAGA,EAAItC,EAAeuC,OAAQD,IACvC3B,EAAKF,UAAU,GAAG+B,iBAAiBxC,EAAesC,GAAIH,GAAS,KAI3EtC,GAAG4C,OAAOnC,IACZV,OAAOD,MAAM+C,QACR9C,OAAOD,OACE,kBAAVH,SAAwBA,OAAOmD,IAAMnD,OAAS,SAAUoD,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.mobile.loader.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.mobile.loader', ['kendo.core'], f);\n}(function () {\n    var __meta__ = {\n        id: 'mobile.loader',\n        name: 'Loader',\n        category: 'mobile',\n        description: 'Mobile Loader',\n        depends: ['core'],\n        hidden: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.mobile.ui, Widget = ui.Widget, CAPTURE_EVENTS = $.map(kendo.eventMap, function (value) {\n                return value;\n            }).join(' ').split(' ');\n        var Loader = Widget.extend({\n            init: function (container, options) {\n                var that = this, element = $('<div class=\"km-loader\"><span class=\"km-loading km-spin\"></span><span class=\"km-loading-left\"></span><span class=\"km-loading-right\"></span></div>');\n                Widget.fn.init.call(that, element, options);\n                that.container = container;\n                that.captureEvents = false;\n                that._attachCapture();\n                element.append(that.options.loading).hide().appendTo(container);\n            },\n            options: {\n                name: 'Loader',\n                loading: '<h1>Loading...</h1>',\n                timeout: 100\n            },\n            show: function () {\n                var that = this;\n                clearTimeout(that._loading);\n                if (that.options.loading === false) {\n                    return;\n                }\n                that.captureEvents = true;\n                that._loading = setTimeout(function () {\n                    that.element.show();\n                }, that.options.timeout);\n            },\n            hide: function () {\n                this.captureEvents = false;\n                clearTimeout(this._loading);\n                this.element.hide();\n            },\n            changeMessage: function (message) {\n                this.options.loading = message;\n                this.element.find('>h1').html(message);\n            },\n            transition: function () {\n                this.captureEvents = true;\n                this.container.css('pointer-events', 'none');\n            },\n            transitionDone: function () {\n                this.captureEvents = false;\n                this.container.css('pointer-events', '');\n            },\n            _attachCapture: function () {\n                var that = this;\n                that.captureEvents = false;\n                function capture(e) {\n                    if (that.captureEvents) {\n                        e.preventDefault();\n                    }\n                }\n                for (var i = 0; i < CAPTURE_EVENTS.length; i++) {\n                    that.container[0].addEventListener(CAPTURE_EVENTS[i], capture, true);\n                }\n            }\n        });\n        ui.plugin(Loader);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}