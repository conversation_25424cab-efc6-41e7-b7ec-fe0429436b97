{"version": 3, "sources": ["kendo.filtermenu.js"], "names": ["f", "define", "$", "undefined", "removeFiltersForField", "expression", "field", "filters", "grep", "filter", "length", "convertItems", "items", "idx", "item", "value", "text", "result", "clearFilter", "expr", "nested", "filterValuesForField", "operator", "flatFilterValues", "logic", "map", "distinct", "getter", "kendo", "index", "seen", "hasOwnProperty", "push", "removeDuplicates", "dataSelector", "dataTextField", "e", "window", "ui", "proxy", "support", "AUTOCOMPLETEVALUE", "browser", "chrome", "POPUP", "INIT", "OPEN", "REFRESH", "CHANGE", "NS", "EQ", "NEQ", "roles", "number", "date", "mobileRoles", "string", "isFunction", "Widget", "booleanTemplate", "customBooleanTemplate", "defaultTemplate", "defaultMobileTemplate", "booleanMobileTemplate", "FilterMenu", "extend", "init", "element", "options", "operators", "initial", "link", "that", "this", "type", "fn", "call", "appendToElement", "addClass", "find", "prepend", "messages", "attr", "on", "_click", "dataSource", "DataSource", "create", "model", "reader", "_parse", "fields", "parse", "values", "_defaultFilter", "_refresh<PERSON><PERSON><PERSON>", "refresh", "bind", "_init", "role", "setUI", "pane", "_isMobile", "_createMobileForm", "_createForm", "form", "_submit", "_reset", "removeClass", "each", "end", "trigger", "container", "cycleForm", "hasCustomTemplate", "title", "html", "template", "format", "ns", "extra", "append", "popup", "closest", "data", "anchor", "open", "_open", "activate", "_activate", "close", "closeCallback", "_keydown", "filterMenuGuid", "guid", "inputType", "view", "submit", "preventDefault", "_closeForm", "_mobileClear", "defaultFilters", "defaultOperator", "filterModel", "observable", "children", "first", "_bind", "destroy", "unbind", "purge", "currentFilter", "found", "current", "set", "_stripFilters", "_merge", "clear", "_removeFilter", "booleanRadioButton", "booleanRadioButtonValue", "operatorSelects", "valueInputs", "andLogicRadio", "viewElement", "val", "prop", "i", "input", "containsFilters", "currentExpression", "stopPropagation", "toJSON", "_checkFor<PERSON>ull<PERSON>r<PERSON>mptyFilter", "firstNullOrEmpty", "second<PERSON>ull<PERSON>r<PERSON>mpty", "search", "parent", "show", "navigate", "animations", "right", "left", "toggle", "not", "focus", "keyCode", "keys", "ESC", "events", "name", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "gte", "gt", "lte", "lt", "enums", "info", "isTrue", "isFalse", "and", "or", "selectValue", "additionalValue", "additionalOperator", "cancel", "done", "into", "multiCheckNS", "multiCkeckMobileTemplate", "FilterMultiCheck", "checkSource", "_foreignKeyV<PERSON>ues", "fetch", "forceUnique", "pageSize", "toLowerCase", "parseFloat", "_createLink", "_link", "serverPaging", "_attachProgress", "checkChangeHandler", "empty", "_filter", "_progress<PERSON><PERSON><PERSON>", "progress", "_progressHideHandler", "_input", "_clearTypingTimeout", "_typingTimeout", "setTimeout", "clearTimeout", "label", "labelText", "ignoreCase", "searchString", "searchTextBox", "labels", "checkAll", "style", "visibility", "parentNode", "display", "textContent", "innerText", "indexOf", "selectedItemsFormat", "_mobileCheckAll", "kendoPopup", "createCheckAllItem", "itemTemplate", "mobile", "checkAllContainer", "all", "checkBoxAll", "checkAll<PERSON><PERSON><PERSON>", "updateCheckAllState", "state", "getFilterArray", "toggleClass", "sender", "action", "autoSync", "is", "createCheckBoxes", "checkValues", "flatValues", "itemsHtml", "templateOptions", "valueField", "render", "ele", "checkBoxVal", "getTime", "checkboxes", "checkbox", "valueFormat", "clearAll", "plugin", "j<PERSON><PERSON><PERSON>", "amd", "a1", "a2", "a3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAwBC,SAAUA,EAAGC,QACVA,OAAO,oBACH,mBACA,uBACA,qBACA,gBACDD,IACL,WAoiCE,MAvhCC,UAAUE,EAAGC,GAcV,QAASC,GAAsBC,EAAYC,GACnCD,EAAWE,UACXF,EAAWE,QAAUL,EAAEM,KAAKH,EAAWE,QAAS,SAAUE,GAEtD,MADAL,GAAsBK,EAAQH,GAC1BG,EAAOF,QACAE,EAAOF,QAAQG,OAEfD,EAAOH,OAASA,KAKvC,QAASK,GAAaC,GAClB,GAAIC,GAAKH,EAAQI,EAAMC,EAAOC,EAAMC,CACpC,IAAIL,GAASA,EAAMF,OAEf,IADAO,KACKJ,EAAM,EAAGH,EAASE,EAAMF,OAAQG,EAAMH,EAAQG,IAC/CC,EAAOF,EAAMC,GACbG,EAAqB,KAAdF,EAAKE,KAAcF,EAAKE,MAAQF,EAAKC,OAASD,EAAOA,EAAKE,KACjED,EAAsB,MAAdD,EAAKC,MAAgBD,EAAKE,MAAQF,EAAOA,EAAKC,MACtDE,EAAOJ,IACHG,KAAMA,EACND,MAAOA,EAInB,OAAOE,GAEX,QAASC,GAAYX,EAASD,GAC1B,MAAOJ,GAAEM,KAAKD,EAAS,SAAUY,GAC7B,MAAIA,GAAKZ,SACLY,EAAKZ,QAAUL,EAAEM,KAAKW,EAAKZ,QAAS,SAAUa,GAC1C,MAAOA,GAAOd,OAASA,IAEpBa,EAAKZ,QAAQG,QAEjBS,EAAKb,OAASA,IAse7B,QAASe,GAAqBhB,EAAYC,GAClCD,EAAWE,UACXF,EAAWE,QAAUL,EAAEM,KAAKH,EAAWE,QAAS,SAAUE,GAEtD,MADAY,GAAqBZ,EAAQH,GACzBG,EAAOF,QACAE,EAAOF,QAAQG,OAEfD,EAAOH,OAASA,GAA4B,MAAnBG,EAAOa,YAKvD,QAASC,GAAiBlB,GACtB,MAAwB,OAApBA,EAAWmB,OAAkBnB,EAAWE,QAAQG,OAAS,KAGzDL,EAAWE,QACJL,EAAEuB,IAAIpB,EAAWE,QAAS,SAAUE,GACvC,MAAOc,GAAiBd,KAErBJ,EAAWU,QAAUZ,GACpBE,EAAWU,UAK3B,QAASW,GAASd,EAAON,GAErB,IAFJ,GAGYQ,GAAuBE,EAF3BW,EAASC,EAAMD,OAAOrB,GAAO,GAAOW,KAAaY,EAAQ,EAAGC,KACzDD,EAAQjB,EAAMF,QACbI,EAAOF,EAAMiB,KAAUb,EAAOW,EAAOb,GACrCE,IAASb,GAAc2B,EAAKC,eAAef,KAC3CC,EAAOe,KAAKlB,GACZgB,EAAKd,IAAQ,EAGrB,OAAOC,GAEX,QAASgB,GAAiBC,EAAcC,GACpC,MAAO,UAAUC,GACb,GAAIxB,GAAQsB,EAAaE,EACzB,OAAOV,GAASd,EAAOuB,IAhkBlC,GACOP,GAAQS,OAAOT,MAAOU,EAAKV,EAAMU,GAAIC,EAAQrC,EAAEqC,MAAOC,EAAUZ,EAAMY,QAASC,EAAoBD,EAAQE,QAAQC,OAAS,WAAa,MAAOC,EAAQ,aAAcC,EAAO,OAAQC,EAAO,OAAQC,EAAU,UAAWC,EAAS,SAAUC,EAAK,mBAAoBC,EAAK,cAAeC,EAAM,kBAAmBC,GAC9SC,OAAU,iBACVC,KAAQ,cACTC,GACCC,OAAU,OACVH,OAAU,SACVC,KAAQ,QACTG,EAAa7B,EAAM6B,WAAYC,EAASpB,EAAGoB,OAC9CC,EAAkB,ulBAClBC,EAAwB,mbACxBC,EAAkB,48DAClBC,EAAwB,+0BAAi6BrB,EAAoB,+UAAwYA,EAAoB,mUAAwWA,EAAoB,gTAAoWA,EAAoB,sTAA2VA,EAAoB,kWAAiZA,EAAoB,6WAAsaA,EAAoB,uVAA4XA,EAAoB,0MAC3rHsB,EAAwB,80BAAs5BtB,EAAoB,gTAAsUA,EAAoB,qRAwC5xCuB,EAAaN,EAAOO,QACpBC,KAAM,SAAUC,EAASC,GACrB,GAAkCC,GAAWC,EAASC,EAAMjE,EAAxDkE,EAAOC,KAAMC,EAAO,QACxBhB,GAAOiB,GAAGT,KAAKU,KAAKJ,EAAML,EAASC,GACnCC,EAAYG,EAAKH,UAAYD,EAAQC,cACrCF,EAAUK,EAAKL,QACfC,EAAUI,EAAKJ,QACVA,EAAQS,kBACTN,EAAOJ,EAAQW,SAAS,4BAA4BC,KAAK,kBACpDR,EAAK,KACNA,EAAOJ,EAAQa,QAAQ,4CAA8CZ,EAAQa,SAASxE,OAAS,iBAAmB2D,EAAQa,SAASxE,OAAS,iDAAiDsE,KAAK,mBAEtMR,EAAKW,KAAK,eAAgBC,GAAG,QAAUlC,EAAIV,EAAMiC,EAAKY,OAAQZ,KAElEA,EAAKD,KAAOA,GAAQrE,IACpBsE,EAAKa,WAAaC,EAAWC,OAAOnB,EAAQiB,YAC5Cb,EAAKlE,MAAQ8D,EAAQ9D,OAAS6D,EAAQe,KAAKtD,EAAMsD,KAAK,UACtDV,EAAKgB,MAAQhB,EAAKa,WAAWI,OAAOD,MACpChB,EAAKkB,OAAS,SAAU3E,GACpB,MAAgB,OAATA,EAAgBA,EAAQ,GAAKA,GAEpCyD,EAAKgB,OAAShB,EAAKgB,MAAMG,SACzBrF,EAAQkE,EAAKgB,MAAMG,OAAOnB,EAAKlE,OAC3BA,IACAoE,EAAOpE,EAAMoE,MAAQ,SACjBpE,EAAMsF,QACNpB,EAAKkB,OAASnD,EAAMjC,EAAMsF,MAAOtF,MAIzC8D,EAAQyB,SACRnB,EAAO,SAEXF,EAAKE,KAAOA,EACZL,EAAYA,EAAUK,IAASN,EAAQC,UAAUK,EACjD,KAAKJ,IAAWD,GACZ,KAEJG,GAAKsB,eAAiB,WAClB,OACIxF,MAAOkE,EAAKlE,MACZgB,SAAUgD,GAAW,KACrBvD,MAAO,KAGfyD,EAAKuB,gBAAkBxD,EAAMiC,EAAKwB,QAASxB,GAC3CA,EAAKa,WAAWY,KAAKjD,EAAQwB,EAAKuB,iBAC9B3B,EAAQS,gBACRL,EAAK0B,QAEL1B,EAAKwB,WAGbE,MAAO,WACH,GAA+DC,GAA3D3B,EAAOC,KAAMnC,EAAKkC,EAAKJ,QAAQ9B,GAAI8D,EAAQ3C,EAAWnB,EAC1DkC,GAAK6B,KAAO7B,EAAKJ,QAAQiC,KACrB7B,EAAK6B,OACL7B,EAAK8B,WAAY,GAEhBF,IACDD,EAAO7D,GAAMc,EAAMoB,EAAKE,OAExBF,EAAK8B,UACL9B,EAAK+B,kBAAkBJ,GAEvB3B,EAAKgC,YAAYL,GAErB3B,EAAKiC,KAAKtB,GAAG,SAAWlC,EAAIV,EAAMiC,EAAKkC,QAASlC,IAAOW,GAAG,QAAUlC,EAAIV,EAAMiC,EAAKmC,OAAQnC,IACvF4B,GACA5B,EAAKiC,KAAK1B,KAAK,cAAc6B,YAAY,aAAaC,KAAK,WACvDvE,EAAGpC,EAAEuE,SAGbD,EAAKiC,KAAK1B,KAAK,IAAMnD,EAAMsD,KAAK,QAAU,oBAAoB0B,YAAY,aAAaE,MAAM/B,KAAK,IAAMnD,EAAMsD,KAAK,QAAU,oBAAoB0B,YAAY,aAAaE,MAAM/B,KAAK,IAAMnD,EAAMsD,KAAK,QAAU,gBAAgB0B,YAAY,aAAaE,MAAM/B,KAAK,IAAMnD,EAAMsD,KAAK,QAAU,gBAAgB0B,YAAY,aAC3TpC,EAAKwB,UACLxB,EAAKuC,QAAQlE,GACTvC,MAAOkE,EAAKlE,MACZ0G,UAAWxC,EAAKiC,OAEpB7E,EAAMqF,UAAUzC,EAAKiC,OAEzBD,YAAa,SAAUL,GACnB,GAAI3B,GAAOC,KAAML,EAAUI,EAAKJ,QAASC,EAAYG,EAAKH,cAAiBK,EAAOF,EAAKE,KAAMwC,EAAoBzD,EAAWe,EAAKJ,QAAQ9B,GACzI+B,GAAYA,EAAUK,IAASN,EAAQC,UAAUK,GACjDF,EAAKiC,KAAOvG,EAAE,gBAAkBsE,EAAKJ,QAAQa,SAASkC,MAAQ,6BAA6BC,KAAKxF,EAAMyF,SAAkB,YAAT3C,EAAqBwC,EAAoBtD,EAAwBD,EAAkBE,IAC9LvD,MAAOkE,EAAKlE,MACZgH,OAAQlD,EAAQkD,OAChBC,GAAI3F,EAAM2F,GACVtC,SAAUb,EAAQa,SAClBuC,MAAOpD,EAAQoD,MACfnD,UAAWA,EACXK,KAAMA,EACNyB,KAAMA,EACNN,OAAQlF,EAAayD,EAAQyB,WAE5BzB,EAAQS,iBAYTL,EAAKL,QAAQsD,OAAOjD,EAAKiC,MACzBjC,EAAKkD,MAAQlD,EAAKL,QAAQwD,QAAQ,YAAYC,KAAKhF,IAZnD4B,EAAKkD,MAAQlD,EAAKiC,KAAK7D,IACnBiF,OAAQrD,EAAKD,KACbuD,KAAMvF,EAAMiC,EAAKuD,MAAOvD,GACxBwD,SAAUzF,EAAMiC,EAAKyD,UAAWzD,GAChC0D,MAAO,WACC1D,EAAKJ,QAAQ+D,eACb3D,EAAKJ,QAAQ+D,cAAc3D,EAAKL,YAGzCyD,KAAKhF,GAKZ4B,EAAKiC,KAAKtB,GAAG,UAAYlC,EAAIV,EAAMiC,EAAK4D,SAAU5D,KAEtD+B,kBAAmB,SAAUJ,GACzB,GAAI3B,GAAOC,KAAML,EAAUI,EAAKJ,QAASC,EAAYG,EAAKH,cAAiBgE,EAAiBzG,EAAM0G,OAAQ5D,EAAOF,EAAKE,IACtHL,GAAYA,EAAUK,IAASN,EAAQC,UAAUK,GACjDF,EAAKiC,KAAOvG,EAAE,WAAWkH,KAAKxF,EAAMyF,SAAkB,YAAT3C,EAAqBX,EAAwBD,IACtFxD,MAAOkE,EAAKlE,MACZ6G,MAAO/C,EAAQ+C,OAAS3C,EAAKlE,MAC7BgH,OAAQlD,EAAQkD,OAChBC,GAAI3F,EAAM2F,GACVtC,SAAUb,EAAQa,SAClBuC,MAAOpD,EAAQoD,MACfnD,UAAWA,EACXgE,eAAgBA,EAChB3D,KAAMA,EACNyB,KAAMA,EACNoC,UAAWhF,EAAYmB,GACvBmB,OAAQlF,EAAayD,EAAQyB,WAEjCrB,EAAKgE,KAAOhE,EAAK6B,KAAKoB,OAAOjD,EAAKiC,KAAKW,QACvC5C,EAAKiC,KAAOjC,EAAKgE,KAAKrE,QAAQY,KAAK,QACnCP,EAAKgE,KAAKrE,QAAQgB,GAAG,QAAS,iBAAkB,SAAU/C,GACtDoC,EAAKiC,KAAKgC,SACVrG,EAAEsG,mBACHvD,GAAG,QAAS,mBAAoB,SAAU/C,GACzCoC,EAAKmE,aACLvG,EAAEsG,mBACHvD,GAAG,QAAS,WAAY,SAAU/C,GACjCoC,EAAKoE,eACLxG,EAAEsG,mBAENlE,EAAKgE,KAAKvC,KAAK,OAAQ,WACnBzB,EAAKwB,aAGbA,QAAS,WAAA,GACDxB,GAAOC,KAAMpE,EAAamE,EAAKa,WAAW5E,WACtCF,WACAiB,MAAO,OAEXqH,GAAkBrE,EAAKsB,kBACvBgD,EAAkBtE,EAAKsB,iBAAiBxE,UACxCkD,EAAKJ,QAAQoD,OAA6B,WAApBsB,GAAoD,kBAApBA,GAA2D,qBAApBA,GAA8D,cAApBA,GAAuD,YAApBA,GAAqD,eAApBA,IAC3MD,EAAe7G,KAAKwC,EAAKsB,kBAE7BtB,EAAKuE,YAAcnH,EAAMoH,YACrBxH,MAAO,MACPjB,QAASsI,IAETrE,EAAKiC,MACL7E,EAAMqE,KAAKzB,EAAKiC,KAAKwC,WAAWC,QAAS1E,EAAKuE,aAE9CvE,EAAK2E,MAAM9I,GACXmE,EAAKD,KAAKO,SAAS,kBAEnBN,EAAKD,KAAKqC,YAAY,mBAG9BwC,QAAS,WACL,GAAI5E,GAAOC,IACXf,GAAOiB,GAAGyE,QAAQxE,KAAKJ,GACnBA,EAAKiC,OACL7E,EAAMyH,OAAO7E,EAAKiC,MAClB7E,EAAMwH,QAAQ5E,EAAKiC,MACnBjC,EAAKiC,KAAK4C,OAAOpG,GACbuB,EAAKkD,QACLlD,EAAKkD,MAAM0B,UACX5E,EAAKkD,MAAQ,MAEjBlD,EAAKiC,KAAO,MAEZjC,EAAKgE,OACLhE,EAAKgE,KAAKc,QACV9E,EAAKgE,KAAO,MAEhBhE,EAAKD,KAAK8E,OAAOpG,GACbuB,EAAKuB,kBACLvB,EAAKa,WAAWgE,OAAOrG,EAAQwB,EAAKuB,iBACpCvB,EAAKa,WAAa,MAEtBb,EAAKL,QAAUK,EAAKD,KAAOC,EAAKuB,gBAAkBvB,EAAKuE,YAAc,MAEzEI,MAAO,SAAU9I,GACb,GAA+CQ,GAAKH,EAAoE6I,EAAe9I,EAAnI+D,EAAOC,KAAMlE,EAAUF,EAAWE,QAAsBiJ,GAAQ,EAAOC,EAAU,EAAGV,EAAcvE,EAAKuE,WAC3G,KAAKlI,EAAM,EAAGH,EAASH,EAAQG,OAAQG,EAAMH,EAAQG,IACjDJ,EAASF,EAAQM,GACbJ,EAAOH,OAASkE,EAAKlE,OACrByI,EAAYW,IAAI,QAASrJ,EAAWmB,OACpC+H,EAAgBR,EAAYxI,QAAQkJ,GAC/BF,IACDR,EAAYxI,QAAQyB,MAAO1B,MAAOkE,EAAKlE,QACvCiJ,EAAgBR,EAAYxI,QAAQkJ,IAExCF,EAAcG,IAAI,QAASlF,EAAKkB,OAAOjF,EAAOM,QAC9CwI,EAAcG,IAAI,WAAYjJ,EAAOa,UACrCmI,IACAD,GAAQ,GACD/I,EAAOF,UACdiJ,EAAQA,GAAShF,EAAK2E,MAAM1I,GAGpC,OAAO+I,IAEXG,cAAe,SAAUpJ,GACrB,MAAOL,GAAEM,KAAKD,EAAS,SAAUE,GAC7B,MAAwB,KAAjBA,EAAOM,OAAgC,MAAhBN,EAAOM,OAAsC,WAApBN,EAAOa,UAA6C,cAApBb,EAAOa,UAAgD,YAApBb,EAAOa,UAA8C,eAApBb,EAAOa,UAAgD,iBAAnBb,EAAOa,UAAkD,oBAAnBb,EAAOa,YAGpPsI,OAAQ,SAAUvJ,GACd,GAAsGI,GAG/FI,EAAKH,EAHR8D,EAAOC,KAAMjD,EAAQnB,EAAWmB,OAAS,MAAOjB,EAAUkE,KAAKkF,cAActJ,EAAWE,SAAkBU,EAASuD,EAAKa,WAAW5E,WAC/HF,WACAiB,MAAO,MAGf,KADApB,EAAsBa,EAAQuD,EAAKlE,OAC9BO,EAAM,EAAGH,EAASH,EAAQG,OAAQG,EAAMH,EAAQG,IACjDJ,EAASF,EAAQM,GACjBJ,EAAOM,MAAQyD,EAAKkB,OAAOjF,EAAOM,MAsBtC,OApBIR,GAAQG,SACJO,EAAOV,QAAQG,QACfL,EAAWE,QAAUA,EACA,QAAjBU,EAAOO,QACPP,EAAOV,UACCiB,MAAOP,EAAOO,MACdjB,QAASU,EAAOV,UAExBU,EAAOO,MAAQ,OAGfP,EAAOV,QAAQyB,KADfzB,EAAQG,OAAS,EACGL,EAEAE,EAAQ,MAGhCU,EAAOV,QAAUA,EACjBU,EAAOO,MAAQA,IAGhBP,GAEXR,OAAQ,SAAUJ,GACd,GAAIE,GAAUkE,KAAKkF,cAActJ,EAAWE,QACxCA,GAAQG,QAAU+D,KAAKsC,QAAQ,UAC3BtG,QACIe,MAAOnB,EAAWmB,MAClBjB,QAASA,GAEbD,MAAOmE,KAAKnE,UAIpBD,EAAaoE,KAAKmF,OAAOvJ,GACrBA,EAAWE,QAAQG,QACnB+D,KAAKY,WAAW5E,OAAOJ,KAG/BwJ,MAAO,WACH,GAAIrF,GAAOC,KAAMpE,EAAamE,EAAKa,WAAW5E,WAAcF,WACxDkE,MAAKsC,QAAQ,UACTtG,OAAQ,KACRH,MAAOkE,EAAKlE,SAIpBkE,EAAKsF,cAAczJ,IAEvBuI,aAAc,WAAA,GAIFmB,GACAC,EAMAC,EAOIC,EAQAC,EAzBR3F,EAAOC,KACP2F,EAAc5F,EAAKgE,KAAKrE,OACV,aAAdK,EAAKE,MACDqF,EAAqBK,EAAYrF,KAAK,oBACtCiF,EAA0BD,EAAmBM,MACjDN,EAAmBM,IAAI,IACvBN,EAAmBhD,QAAQ,UAC3BgD,EAAmBM,IAAIL,GACvBD,EAAmBO,KAAK,WAAW,KAE/BL,EAAkBG,EAAYrF,KAAK,UACvCkF,EAAgBpD,KAAK,SAAU0D,EAAGnI,GAC9B,GAAIoI,GAAQtK,EAAEkC,EACdoI,GAAMH,IAAIG,EAAMzF,KAAK,gBAAgBsF,OACrCG,EAAMzD,QAAQ,YAEA,WAAdvC,EAAKE,MAAmC,SAAdF,EAAKE,MAAiC,WAAdF,EAAKE,OACnDwF,EAAcE,EAAYrF,KAAK,kBACnCmF,EAAYrD,KAAK,SAAU0D,EAAGnI,GAC1B,GAAIoI,GAAQtK,EAAEkC,EACdoI,GAAMH,IAAI,IACVG,EAAMzD,QAAQ,aAGlBvC,EAAKJ,QAAQoD,QACT2C,EAAgBC,EAAYrF,KAAK,gBAAgBmE,QACrDiB,EAAcG,KAAK,WAAW,GAC9BH,EAAcpD,QAAQ,aAIlC+C,cAAe,SAAUzJ,GACrB,GAAImE,GAAOC,IACXpE,GAAWE,QAAUL,EAAEM,KAAKH,EAAWE,QAAS,SAAUE,GACtD,MAAIA,GAAOF,SACPE,EAAOF,QAAUW,EAAYT,EAAOF,QAASiE,EAAKlE,OAC3CG,EAAOF,QAAQG,QAEnBD,EAAOH,OAASkE,EAAKlE,QAE3BD,EAAWE,QAAQG,SACpBL,EAAa,MAEjBmE,EAAKa,WAAW5E,OAAOJ,IAE3BqG,QAAS,SAAUtE,GAAV,GAGD/B,GACAoK,EAMIC,CATRtI,GAAEsG,iBACFtG,EAAEuI,kBACEtK,EAAaoE,KAAKsE,YAAY6B,SAC9BH,EAAkBvK,EAAEM,KAAKH,EAAWE,QAAS,SAAUE,GACvD,MAAwB,KAAjBA,EAAOM,OAAiC,OAAjBN,EAAOM,QAErC0D,KAAKoG,2BAA2BxK,IAAeoK,GAAmBA,EAAgB/J,OAClF+D,KAAKhE,OAAOJ,IAERqK,EAAoBjG,KAAKY,WAAW5E,SACpCiK,IACAA,EAAkBnK,QAAQyB,KAAK3B,GAC/BA,EAAaqK,GAEjBjG,KAAKqF,cAAczJ,IAEvBoE,KAAKkE,cAETkC,2BAA4B,SAAUxK,GAAV,GAIpByK,GACAC,EACAzJ,CALJ,UAAKjB,GAAeA,EAAWE,SAAYF,EAAWE,QAAQG,UAG1DoK,GAAmB,EACnBC,GAAoB,EAEpB1K,EAAWE,QAAQ,KACnBe,EAAWjB,EAAWE,QAAQ,GAAGe,SACjCwJ,EAA+B,UAAZxJ,GAAoC,aAAZA,GAAuC,cAAZA,GAAwC,WAAZA,GAAqC,iBAAZA,GAA2C,oBAAZA,GAE1JjB,EAAWE,QAAQ,KACnBe,EAAWjB,EAAWE,QAAQ,GAAGe,SACjCyJ,EAAgC,UAAZzJ,GAAoC,aAAZA,GAAuC,cAAZA,GAAwC,WAAZA,GAAqC,iBAAZA,GAA2C,oBAAZA,IAEvJmD,KAAKL,QAAQoD,OAASsD,GAAoBrG,KAAKL,QAAQoD,QAAUsD,GAAoBC,KAEjGpE,OAAQ,WACJlC,KAAKoF,QACDpF,KAAKL,QAAQ4G,QAAUvG,KAAKuC,WAC5BvC,KAAKuC,UAAUjC,KAAK,SAASkG,SAASC,OAE1CzG,KAAKkE,cAETA,WAAY,WACJlE,KAAK6B,UACL7B,KAAK4B,KAAK8E,SAAS,GAAI1G,KAAKL,QAAQgH,WAAWC,OAE/C5G,KAAKiD,MAAMQ,SAGnB9C,OAAQ,SAAUhD,GACdA,EAAEsG,iBACFtG,EAAEuI,kBACGlG,KAAKiD,OAAUjD,KAAK4B,MACrB5B,KAAKyB,QAELzB,KAAK6B,UACL7B,KAAK4B,KAAK8E,SAAS1G,KAAK+D,KAAM/D,KAAKL,QAAQgH,WAAWE,MAEtD7G,KAAKiD,MAAM6D,UAGnBxD,MAAO,WACH,GAAIL,EACJxH,GAAE,kBAAkBsL,IAAI/G,KAAKgC,MAAMI,KAAK,WACpCa,EAAQxH,EAAEuE,MAAMmD,KAAKhF,GACjB8E,GACAA,EAAMQ,WAIlBD,UAAW,WACPxD,KAAKgC,KAAK1B,KAAK,yBAAyB0G,QACxChH,KAAKsC,QAAQjE,GACTxC,MAAOmE,KAAKnE,MACZ0G,UAAWvC,KAAKgC,QAGxB2B,SAAU,SAAUhG,GACZA,EAAEsJ,SAAW9J,EAAM+J,KAAKC,KACxBnH,KAAKiD,MAAMQ,SAGnB2D,QACIhJ,EACA,SACAC,GAEJsB,SACI0H,KAAM,aACNtE,OAAO,EACP3C,iBAAiB,EACjBH,KAAM,SACNL,WACIb,QACIuI,GAAI7I,EACJ8I,IAAK7I,EACL8I,WAAY,cACZC,SAAU,WACVC,eAAgB,mBAChBC,SAAU,YACVC,OAAQ,UACRC,UAAW,cACXC,QAAS,WACTC,WAAY,eACZC,cAAe,eACfC,iBAAkB,aAEtBrJ,QACI0I,GAAI7I,EACJ8I,IAAK7I,EACLwJ,IAAK,8BACLC,GAAI,kBACJC,IAAK,2BACLC,GAAI,eACJT,OAAQ,UACRC,UAAW,eAEfhJ,MACIyI,GAAI7I,EACJ8I,IAAK7I,EACLwJ,IAAK,uBACLC,GAAI,WACJC,IAAK,wBACLC,GAAI,YACJT,OAAQ,UACRC,UAAW,eAEfS,OACIhB,GAAI7I,EACJ8I,IAAK7I,EACLkJ,OAAQ,UACRC,UAAW,gBAGnBrH,UACI+H,KAAM,8BACN7F,MAAO,8BACP8F,OAAQ,UACRC,QAAS,WACTzM,OAAQ,SACRoJ,MAAO,QACPsD,IAAK,MACLC,GAAI,KACJC,YAAa,iBACb/L,SAAU,WACVP,MAAO,QACPuM,gBAAiB,mBACjBC,mBAAoB,sBACpB/L,MAAO,gBACPgM,OAAQ,SACRC,KAAM,OACNC,KAAM,MAEVtC,YACIE,KAAM,QACND,MAAO,kBAIfsC,EAAe,yBA4CfrI,EAAa1D,EAAMgG,KAAKtC,WACxBsI,EAA2B,onBAAkrBnL,EAAoB,mhBACjuBoL,EAAmBnK,EAAOO,QAC1BC,KAAM,SAAUC,EAASC,GAAnB,GAIE9D,GACAwN,CAJJpK,GAAOiB,GAAGT,KAAKU,KAAKH,KAAMN,EAASC,GACnCA,EAAUK,KAAKL,QACfK,KAAKN,QAAUjE,EAAEiE,GACb7D,EAAQmE,KAAKnE,MAAQmE,KAAKL,QAAQ9D,OAASmE,KAAKN,QAAQe,KAAKtD,EAAMsD,KAAK,UACxE4I,EAAc1J,EAAQ0J,YACtBrJ,KAAKsJ,qBACLtJ,KAAKqJ,YAAcxI,EAAWC,OAAOnB,EAAQyB,QAC7CpB,KAAKqJ,YAAYE,SACV5J,EAAQ6J,aACfH,EAAc5N,EAAE+D,QAAO,KAAUG,EAAQiB,WAAWjB,eAC7C0J,GAAYI,SACnBzJ,KAAKqJ,YAAcxI,EAAWC,OAAOuI,GACrCrJ,KAAKqJ,YAAYrI,OAAOmC,KAAO3F,EAAiBwC,KAAKqJ,YAAYrI,OAAOmC,KAAMnD,KAAKnE,QAEnFmE,KAAKqJ,YAAcxI,EAAWC,OAAOuI,GAEzCrJ,KAAKY,WAAajB,EAAQiB,WAC1BZ,KAAKe,MAAQf,KAAKY,WAAWI,OAAOD,MACpCf,KAAKiB,OAAS,SAAU3E,GACpB,MAAOA,GAAQ,IAEf0D,KAAKe,OAASf,KAAKe,MAAMG,SACzBrF,EAAQmE,KAAKe,MAAMG,OAAOlB,KAAKnE,OAC3BA,IACkB,UAAdA,EAAMoE,KACND,KAAKiB,OAAS,SAAU3E,GACpB,MAAqB,gBAAVA,IAA8C,SAAxBA,EAAMoN,cAC5B,KAEJC,WAAWrN,IAEfT,EAAMsF,QACbnB,KAAKiB,OAASnD,EAAMjC,EAAMsF,MAAOtF,IAErCmE,KAAKC,KAAOpE,EAAMoE,MAAQ,WAG7BN,EAAQS,gBAGTJ,KAAKyB,QAFLzB,KAAK4J,cAIT5J,KAAKsB,gBAAkBxD,EAAMkC,KAAKuB,QAASvB,MAC3CA,KAAKY,WAAWY,KAAKjD,EAAQyB,KAAKsB,kBAEtCsI,YAAa,WAAA,GACLlK,GAAUM,KAAKN,QACfI,EAAOJ,EAAQW,SAAS,4BAA4BC,KAAK,iBACxDR,GAAK,KACNA,EAAOJ,EAAQa,QAAQ,4CAA8CP,KAAKL,QAAQa,SAASxE,OAAS,iBAAmBgE,KAAKL,QAAQa,SAASxE,OAAS,2CAA2CsE,KAAK,mBAE1MN,KAAK6J,MAAQ/J,EAAKW,KAAK,eAAgBC,GAAG,QAAUlC,EAAIV,EAAMkC,KAAKW,OAAQX,QAE/EyB,MAAO,WAAA,GACC1B,GAAOC,KACPwJ,EAAcxJ,KAAKL,QAAQ6J,YAC3B7J,EAAUK,KAAKL,OACnBK,MAAK4B,KAAOjC,EAAQiC,KAChB5B,KAAK4B,OACL5B,KAAK6B,WAAY,GAErB7B,KAAK+B,cACD/B,KAAKsJ,oBACLtJ,KAAKuB,UACEiI,IAAgBxJ,KAAKqJ,YAAY1J,QAAQmK,cAAgB9J,KAAKY,WAAWuC,OAAOlH,QACvF+D,KAAKqJ,YAAYlG,KAAKlG,EAAS+C,KAAKY,WAAWuC,OAAQnD,KAAKnE,QAC5DmE,KAAKuB,YAELvB,KAAK+J,kBACL/J,KAAKqJ,YAAYE,MAAM,WACnBxJ,EAAKwB,QAAQpB,KAAKJ,MAGrBC,KAAKL,QAAQ6J,cACdxJ,KAAKgK,mBAAqB,WACtBjK,EAAKwC,UAAU0H,QACflK,EAAKwB,WAETvB,KAAKqJ,YAAY7H,KAAKjD,EAAQyB,KAAKgK,qBAEvChK,KAAKgC,KAAKtB,GAAG,UAAYwI,EAAcpL,EAAMkC,KAAK2D,SAAU3D,OAAOU,GAAG,SAAWwI,EAAcpL,EAAMkC,KAAKkK,QAASlK,OAAOU,GAAG,QAAUwI,EAAcpL,EAAMkC,KAAKkC,OAAQlC,OACxKA,KAAKsC,QAAQlE,GACTvC,MAAOmE,KAAKnE,MACZ0G,UAAWvC,KAAKgC,QAGxB+H,gBAAiB,WACb,GAAIhK,GAAOC,IACXA,MAAKmK,iBAAmB,WACpBtM,EAAGuM,SAASrK,EAAKwC,WAAW,IAEhCvC,KAAKqK,qBAAuB,WACxBxM,EAAGuM,SAASrK,EAAKwC,WAAW,IAEhCvC,KAAKqJ,YAAY7H,KAAK,WAAYxB,KAAKmK,kBAAkB3I,KAAK,SAAUxB,KAAKqK,uBAEjFC,OAAQ,WACJ,GAAIvK,GAAOC,IACXD,GAAKwK,sBACLxK,EAAKyK,eAAiBC,WAAW,WAC7B1K,EAAKwG,UACN,MAEPgE,oBAAqB,WACbvK,KAAKwK,iBACLE,aAAa1K,KAAKwK,gBAClBxK,KAAKwK,eAAiB,OAG9BjE,OAAQ,WAAA,GAOAT,GAUI6E,EACAC,EAjBJC,EAAa7K,KAAKL,QAAQkL,WAC1BC,EAAe9K,KAAK+K,cAAc,GAAGzO,MACrC0O,EAAShL,KAAKuC,UAAUjC,KAAK,QAajC,KAZIuK,IACAC,EAAeA,EAAapB,eAE5B5D,EAAI,EACJ9F,KAAKL,QAAQsL,UAAYD,EAAO/O,SAC3B+D,KAAK6B,UAIN7B,KAAK+D,KAAKrE,QAAQY,KAAK,iBAAiB,GAAG4K,MAAMC,WAAaL,EAAe,SAAW,IAHxFE,EAAO,GAAGI,WAAWF,MAAMG,QAAUP,EAAe,OAAS,GAC7DhF,MAKDA,EAAIkF,EAAO/O,QACV0O,EAAQK,EAAOlF,GACf8E,EAAYD,EAAMW,aAAeX,EAAMY,UACvCV,IACAD,EAAYA,EAAUlB,eAE1BiB,EAAMS,WAAWF,MAAMG,QAAUT,EAAUY,QAAQV,IAAiB,EAAI,GAAK,OAC7EhF,KAGRtC,UAAW,WACPxD,KAAKgC,KAAK1B,KAAK,yBAAyB0G,QACxChH,KAAKsC,QAAQjE,GACTxC,MAAOmE,KAAKnE,MACZ0G,UAAWvC,KAAKgC,QAGxBD,YAAa,WAAA,GAgCDrC,GA/BJC,EAAUK,KAAKL,QACfgD,EAAO,GACP5C,EAAOC,IACNA,MAAK6B,YACNc,GAAQ,wCACJhD,EAAQ4G,SACR5D,GAAQ,4DAAsEhD,EAAQa,SAAS+F,OAAS,6CAE5G5D,GAAQ,8CACJhD,EAAQa,SAASiL,sBACjB9I,GAAQ,wCAA4CxF,EAAM0F,OAAOlD,EAAQa,SAASiL,oBAAqB,GAAK,UAEhH9I,GAAQ,iCACRA,GAAQ,oDAA0DhD,EAAQa,SAASxE,OAAS,YAC5F2G,GAAQ,yCAA+ChD,EAAQa,SAAS4E,MAAQ,YAChFzC,GAAQ,SACRA,GAAQ,SACR3C,KAAKgC,KAAOvG,EAAE,iCAAiCkH,KAAKA,GACpD3C,KAAKuC,UAAYvC,KAAKgC,KAAK1B,KAAK,uBAEhCN,KAAK6B,WACL9B,EAAKiC,KAAOvG,EAAE,WAAWkH,KAAKxF,EAAMyF,SAASuG,IACzCtN,MAAOkE,EAAKlE,MACZ6G,MAAO/C,EAAQ+C,OAAS3C,EAAKlE,MAC7BiH,GAAI3F,EAAM2F,GACVtC,SAAUb,EAAQa,SAClB+F,OAAQ5G,EAAQ4G,OAChB0E,SAAUtL,EAAQsL,YAEtBlL,EAAKgE,KAAOhE,EAAK6B,KAAKoB,OAAOjD,EAAKiC,KAAKW,QACvC5C,EAAKiC,KAAOjC,EAAKgE,KAAKrE,QAAQY,KAAK,QAC/BZ,EAAUM,KAAK+D,KAAKrE,QACxBM,KAAKuC,UAAY7C,EAAQY,KAAK,sBAC9BZ,EAAQgB,GAAG,QAAS,iBAAkB,SAAU/C,GAC5CoC,EAAKiC,KAAKgC,SACVrG,EAAEsG,mBACHvD,GAAG,QAAS,mBAAoB,SAAU/C,GACzCoC,EAAKmE,aACLvG,EAAEsG,mBACHvD,GAAG,QAAS,eAAgB,SAAU/C,GACrCoC,EAAK2L,iBAAgB,GACrB/N,EAAEsG,mBACHvD,GAAG,QAAS,gBAAiB,SAAU/C,GACtCoC,EAAK2L,iBAAgB,GACrB/N,EAAEsG,mBAENlE,EAAKgE,KAAKvC,KAAK,OAAQ,WACnBzB,EAAKwB,aAGJ5B,EAAQS,iBAYTJ,KAAKiD,MAAQjD,KAAKN,QAAQwD,QAAQ,YAAYC,KAAKhF,GACnD6B,KAAKN,QAAQsD,OAAOhD,KAAKgC,OAZzBjC,EAAKkD,MAAQlD,EAAKiC,KAAK2J,YACnBvI,OAAQrD,EAAK8J,MACbxG,KAAMvF,EAAMiC,EAAKuD,MAAOvD,GACxBwD,SAAUzF,EAAMiC,EAAKyD,UAAWzD,GAChC0D,MAAO,WACC1D,EAAKJ,QAAQ+D,eACb3D,EAAKJ,QAAQ+D,cAAc3D,EAAKL,YAGzCyD,KAAKhF,GAMZwB,EAAQ4G,SACRvG,KAAK+K,cAAgB/K,KAAKgC,KAAK1B,KAAK,sBACpCN,KAAK+K,cAAcrK,GAAG,QAAS5C,EAAMkC,KAAKsK,OAAQtK,SAG1D4L,mBAAoB,WAAA,GACZjM,GAAUK,KAAKL,QACfiD,EAAWzF,EAAMyF,SAASjD,EAAQkM,cAClChQ,MAAO,MACPiQ,OAAQ9L,KAAK6B,aAEbkK,EAAoBtQ,EAAEmH,GAAWoJ,IAAKrM,EAAQa,SAASyK,WAC3DjL,MAAKuC,UAAUhC,QAAQwL,GACvB/L,KAAKiM,YAAcF,EAAkBzL,KAAK,aAAagH,GAAG,GAAGjH,SAAS,eACtEL,KAAKkM,gBAAkBpO,EAAMkC,KAAKiL,SAAUjL,MAC5CA,KAAKiM,YAAYvL,GAAGnC,EAAS2K,EAAclJ,KAAKkM,kBAEpDC,oBAAqB,WAIjB,GAHInM,KAAKL,QAAQa,SAASiL,qBACtBzL,KAAKgC,KAAK1B,KAAK,4BAA4B/D,KAAKY,EAAM0F,OAAO7C,KAAKL,QAAQa,SAASiL,oBAAqBzL,KAAKuC,UAAUjC,KAAK,8BAA8BrE,SAE1J+D,KAAKiM,YAAa,CAClB,GAAIG,GAAQpM,KAAKuC,UAAUjC,KAAK,+BAA+BrE,QAAU+D,KAAKuC,UAAUjC,KAAK,8BAA8BrE,MAC3H+D,MAAKiM,YAAYpG,KAAK,UAAWuG,KAGzC7K,QAAS,SAAU5D,GAAV,GACD6L,GAAcxJ,KAAKL,QAAQ6J,YAC3B5I,EAAaZ,KAAKY,WAClB9E,EAAUkE,KAAKqM,gBACfrM,MAAK6J,OACL7J,KAAK6J,MAAMyC,YAAY,iBAAqC,IAAnBxQ,EAAQG,QAEjD+D,KAAKgC,OACDrE,GAAK6L,GAAe7L,EAAE4O,SAAW3L,IAAeA,EAAWjB,QAAQmK,eAA6B,cAAZnM,EAAE6O,QAAsC,OAAZ7O,EAAE6O,QAA+B,UAAZ7O,EAAE6O,QAAsB5L,EAAWjB,QAAQ8M,UAAyB,SAAb9O,EAAE6O,UAAuBxM,KAAKsJ,sBAC1NtJ,KAAKqJ,YAAYlG,KAAKlG,EAAS+C,KAAKY,WAAWuC,OAAQnD,KAAKnE,QAC5DmE,KAAKuC,UAAU0H,SAEfjK,KAAKuC,UAAUmK,GAAG,WAClB1M,KAAK2M,mBAET3M,KAAK4M,YAAY9Q,GACjBkE,KAAKsC,QAAQhE,KAGrB+N,eAAgB,WAAA,GAMRQ,GALAjR,EAAaH,EAAE+D,QAAO,MACtB1D,WACAiB,MAAO,OACRiD,KAAKY,WAAW5E,SAGnB,OAFAY,GAAqBhB,EAAYoE,KAAKnE,OAClCgR,EAAa/P,EAAiBlB,IAGtC+Q,iBAAkB,WAAA,GAEVxJ,GAgBAP,EACAkK,EAlBAnN,EAAUK,KAAKL,QAEfoN,GACAlR,MAAOmE,KAAKnE,MACZgH,OAAQlD,EAAQkD,OAChBiJ,OAAQ9L,KAAK6B,UACb5B,KAAMD,KAAKC,KAEVD,MAAKL,QAAQ6J,YAEPxJ,KAAKsJ,qBACZnG,EAAOnD,KAAKqJ,YAAYlG,OACxB4J,EAAgBC,WAAa,QAC7BD,EAAgBlR,MAAQ,QAExBsH,EAAOnD,KAAKqJ,YAAYlG,OANxBA,EAAOnD,KAAKqJ,YAAYtF,OAQxBnB,EAAWzF,EAAMyF,SAASjD,EAAQkM,aAAakB,IAC/CD,EAAY3P,EAAM8P,OAAOrK,EAAUO,GACnCxD,EAAQsL,WAAajL,KAAK6B,WAC1B7B,KAAK4L,qBAET5L,KAAKuC,UAAU7B,GAAGnC,EAAS2K,EAAc,YAAapL,EAAMkC,KAAKmM,oBAAqBnM,OACtFA,KAAKuC,UAAUS,OAAO8J,IAE1B7B,SAAU,WACN,GAAImB,GAAQpM,KAAKiM,YAAYS,GAAG,WAChC1M,MAAKuC,UAAUjC,KAAK,aAAauF,KAAK,UAAWuG,IAErDQ,YAAa,SAAUxL,GACnB,GAAIrB,GAAOC,IACXvE,GAAEA,EAAEM,KAAKiE,KAAKuC,UAAUjC,KAAK,aAAauF,KAAK,WAAW,GAAQ,SAAUqH,GAAV,GAK1DC,GACKrH,EALLf,GAAQ,CACZ,KAAItJ,EAAEyR,GAAKR,GAAG,gBAId,IADIS,EAAcpN,EAAKkB,OAAOxF,EAAEyR,GAAKtH,OAC5BE,EAAI,EAAGA,EAAI1E,EAAOnF,OAAQ6J,IAY/B,GATQf,EAFS,QAAbhF,EAAKE,KACDmB,EAAO0E,IAAMqH,EACL/L,EAAO0E,GAAGsH,WAAaD,EAAYC,UACtB,OAAdhM,EAAO0E,IAA+B,OAAhBqH,EAMzB/L,EAAO0E,IAAMqH,EAGrB,MAAOpI,MAGfc,KAAK,WAAW,GACpB7F,KAAKmM,uBAETT,gBAAiB,SAAUU,GAAV,GACTrM,GAAOC,KACPqN,EAAatN,EAAKwC,UAAUjC,KAAK,YACrC+M,GAAWjL,KAAK,SAAU0D,EAAGnI,GACzB,GAAI2P,GAAW7R,EAAEkC,EACjB2P,GAASzH,KAAK,UAAWuG,GACzBkB,EAAShL,QAAQ,aAGzB4H,QAAS,SAAUvM,GAAV,GAGD/B,GACAmE,CAHJpC,GAAEsG,iBACFtG,EAAEuI,kBACEtK,GAAemB,MAAO,MACtBgD,EAAOC,KACXpE,EAAWE,QAAUL,EAAEuB,IAAIgD,KAAKgC,KAAK1B,KAAK,uCAAwC,SAAUjE,GACxF,OACIC,MAAOb,EAAEY,GAAMuJ,MACf/I,SAAU,KACVhB,MAAOkE,EAAKlE,SAGhBD,EAAWE,QAAQG,QAAU+D,KAAKsC,QAAQ,UACtCtG,OAAQJ,EACRC,MAAOkE,EAAKlE,UAIpBD,EAAaoE,KAAKmF,OAAOvJ,GACrBA,EAAWE,QAAQG,OACnB+D,KAAKY,WAAW5E,OAAOJ,GAEvBoE,KAAKoF,QAETpF,KAAKkE,eAETgB,cAAe,SAAUpJ,GACrB,MAAOL,GAAEM,KAAKD,EAAS,SAAUE,GAC7B,MAAuB,OAAhBA,EAAOM,SAGtBgN,kBAAmB,WACf,GAAI3J,GAAUK,KAAKL,OACnB,OAAOA,GAAQyB,SAAWzB,EAAQ0J,aAEtC1E,QAAS,WACL,GAAI5E,GAAOC,IACXf,GAAOiB,GAAGyE,QAAQxE,KAAKJ,GACnBA,EAAKiC,OACL7E,EAAMyH,OAAO7E,EAAKiC,MAClB7E,EAAMwH,QAAQ5E,EAAKiC,MACnBjC,EAAKiC,KAAK4C,OAAOsE,GACbnJ,EAAKkD,QACLlD,EAAKkD,MAAM0B,UACX5E,EAAKkD,MAAQ,MAEjBlD,EAAKiC,KAAO,KACRjC,EAAKwC,YACLxC,EAAKwC,UAAUqC,OAAOsE,GACtBnJ,EAAKwC,UAAY,MAEjBxC,EAAKkM,aACLlM,EAAKkM,YAAYrH,OAAOsE,IAG5BnJ,EAAKgE,OACLhE,EAAKgE,KAAKc,QACV9E,EAAKgE,KAAO,MAEZhE,EAAK8J,OACL9J,EAAK8J,MAAMjF,OAAOpG,GAElBuB,EAAKuB,kBACLvB,EAAKa,WAAWgE,OAAOrG,EAAQwB,EAAKuB,iBACpCvB,EAAKa,WAAa,MAElBb,EAAKiK,oBACLjK,EAAKsJ,YAAYzE,OAAOrG,EAAQwB,EAAKiK,oBAErCjK,EAAKoK,kBACLpK,EAAKsJ,YAAYzE,OAAO,WAAY7E,EAAKoK,kBAEzCpK,EAAKsK,sBACLtK,EAAKsJ,YAAYzE,OAAO,SAAU7E,EAAKsK,sBAE3CrK,KAAKuK,sBACLvK,KAAK+K,cAAgB,KACrBhL,EAAKL,QAAUK,EAAKsJ,YAActJ,EAAKwC,UAAYxC,EAAKkM,YAAclM,EAAK8J,MAAQ9J,EAAKuB,gBAAkBvB,EAAKmM,gBAAkB,MAErIvM,SACI0H,KAAM,mBACNwE,aAAc,SAAUlM,GAAV,GACN9D,GAAQ8D,EAAQ9D,MAChBgH,EAASlD,EAAQkD,OACjBmK,EAAarN,EAAQqN,WACrBlB,EAASnM,EAAQmM,OACjByB,EAAc,EAOlB,OANIP,KAAetR,IACfsR,EAAanR,GAEG,QAAhB8D,EAAQM,OACRsN,EAAc,2BAEX,4EAA+FzB,EAAS,UAAY,IAAM,+BAAoCyB,EAAc,MAASP,EAAa,oDAA+DnK,EAASA,EAAS,OAAS,MAAShH,EAAQ,0BAExToP,UAAU,EACV1E,QAAQ,EACRsE,YAAY,EACZzK,iBAAiB,EACjBI,UACIyK,SAAU,aACVuC,SAAU,YACVpI,MAAO,QACPpJ,OAAQ,SACRuK,OAAQ,SACRwC,OAAQ,SACR0C,oBAAqB,qBACrBzC,KAAM,OACNC,KAAM,MAEVO,aAAa,EACb7C,YACIE,KAAM,QACND,MAAO,gBAGfQ,QACIhJ,EACAE,EACA,SACAD,IAGR5C,GAAE+D,OAAO4J,EAAiBlJ,IACtBS,OAAQpB,EAAWW,GAAGS,OACtBgD,SAAUpE,EAAWW,GAAGyD,SACxBzB,OAAQ3C,EAAWW,GAAGgC,OACtBgC,WAAY3E,EAAWW,GAAGgE,WAC1BmB,cAAe9F,EAAWW,GAAGmF,cAC7BD,MAAO7F,EAAWW,GAAGkF,MACrBD,OAAQ5F,EAAWW,GAAGiF,SAE1BtH,EAAG4P,OAAOlO,GACV1B,EAAG4P,OAAOrE,IACZxL,OAAOT,MAAMuQ,QACR9P,OAAOT,OACE,kBAAV3B,SAAwBA,OAAOmS,IAAMnS,OAAS,SAAUoS,EAAIC,EAAIC,IACrEA,GAAMD", "file": "kendo.filtermenu.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n(function (f, define) {\n    define('kendo.filtermenu', [\n        'kendo.datepicker',\n        'kendo.numerictextbox',\n        'kendo.dropdownlist',\n        'kendo.binder'\n    ], f);\n}(function () {\n    var __meta__ = {\n        id: 'filtermenu',\n        name: 'Filtering Menu',\n        category: 'framework',\n        depends: [\n            'datepicker',\n            'numerictextbox',\n            'dropdownlist',\n            'binder'\n        ],\n        advanced: true\n    };\n    (function ($, undefined) {\n        var kendo = window.kendo, ui = kendo.ui, proxy = $.proxy, support = kendo.support, AUTOCOMPLETEVALUE = support.browser.chrome ? 'disabled' : 'off', POPUP = 'kendoPopup', INIT = 'init', OPEN = 'open', REFRESH = 'refresh', CHANGE = 'change', NS = '.kendoFilterMenu', EQ = 'Is equal to', NEQ = 'Is not equal to', roles = {\n                'number': 'numerictextbox',\n                'date': 'datepicker'\n            }, mobileRoles = {\n                'string': 'text',\n                'number': 'number',\n                'date': 'date'\n            }, isFunction = kendo.isFunction, Widget = ui.Widget;\n        var booleanTemplate = '<div class=\"k-filter-menu-container\">' + '<div class=\"k-filter-help-text\">#=messages.info#</div>' + '<label>' + '<input type=\"radio\" data-#=ns#bind=\"checked: filters[0].value\" value=\"true\" name=\"filters[0].value\"/>' + '#=messages.isTrue#' + '</label>' + '<label>' + '<input type=\"radio\" data-#=ns#bind=\"checked: filters[0].value\" value=\"false\" name=\"filters[0].value\"/>' + '#=messages.isFalse#' + '</label>' + '<div class=\"k-action-buttons\">' + '<button type=\"submit\" title=\"#=messages.filter#\" class=\"k-button k-primary\">#=messages.filter#</button>' + '<button type=\"reset\" title=\"#=messages.clear#\" class=\"k-button\">#=messages.clear#</button>' + '</div>' + '</div>';\n        var customBooleanTemplate = '<div class=\"k-filter-menu-container\">' + '<div class=\"k-filter-help-text\">#=messages.info#</div>' + '<label>' + '<input class=\"k-textbox\" data-#=ns#bind=\"value: filters[0].value\" name=\"filters[0].value\"/>' + '</label>' + '<div class=\"k-action-buttons\">' + '<button type=\"submit\" title=\"#=messages.filter#\" class=\"k-button k-primary\">#=messages.filter#</button>' + '<button type=\"reset\" title=\"#=messages.clear#\" class=\"k-button\">#=messages.clear#</button>' + '</div>' + '</div>';\n        var defaultTemplate = '<div class=\"k-filter-menu-container\">' + '<div class=\"k-filter-help-text\">#=messages.info#</div>' + '<select title=\"#=messages.operator#\" data-#=ns#bind=\"value: filters[0].operator\" data-#=ns#role=\"dropdownlist\">' + '#for(var op in operators){#' + '<option value=\"#=op#\">#=operators[op]#</option>' + '#}#' + '</select>' + '#if(values){#' + '<select title=\"#=messages.value#\" data-#=ns#bind=\"value:filters[0].value\" data-#=ns#text-field=\"text\" data-#=ns#value-field=\"value\" data-#=ns#source=\\'#=kendo.stringify(values).replace(/\\'/g,\"&\\\\#39;\")#\\' data-#=ns#role=\"dropdownlist\" data-#=ns#option-label=\"#=messages.selectValue#\" data-#=ns#value-primitive=\"true\">' + '</select>' + '#}else{#' + '<input title=\"#=messages.value#\" data-#=ns#bind=\"value:filters[0].value\" class=\"k-textbox\" type=\"text\" #=role ? \"data-\" + ns + \"role=\\'\" + role + \"\\'\" : \"\"# />' + '#}#' + '#if(extra){#' + '<select title=\"#=messages.logic#\" class=\"k-filter-and\" data-#=ns#bind=\"value: logic\" data-#=ns#role=\"dropdownlist\">' + '<option value=\"and\">#=messages.and#</option>' + '<option value=\"or\">#=messages.or#</option>' + '</select>' + '<select title=\"#=messages.additionalOperator#\" data-#=ns#bind=\"value: filters[1].operator\" data-#=ns#role=\"dropdownlist\">' + '#for(var op in operators){#' + '<option value=\"#=op#\">#=operators[op]#</option>' + '#}#' + '</select>' + '#if(values){#' + '<select title=\"#=messages.additionalValue#\" data-#=ns#bind=\"value:filters[1].value\" data-#=ns#text-field=\"text\" data-#=ns#value-field=\"value\" data-#=ns#source=\\'#=kendo.stringify(values).replace(/\\'/g,\"&\\\\#39;\")#\\' data-#=ns#role=\"dropdownlist\" data-#=ns#option-label=\"#=messages.selectValue#\" data-#=ns#value-primitive=\"true\">' + '</select>' + '#}else{#' + '<input title=\"#=messages.additionalValue#\" data-#=ns#bind=\"value: filters[1].value\" class=\"k-textbox\" type=\"text\" #=role ? \"data-\" + ns + \"role=\\'\" + role + \"\\'\" : \"\"#/>' + '#}#' + '#}#' + '<div class=\"k-action-buttons\">' + '<button type=\"submit\" title=\"#=messages.filter#\" class=\"k-button k-primary\">#=messages.filter#</button>' + '<button type=\"reset\" title=\"#=messages.clear#\" class=\"k-button\">#=messages.clear#</button>' + '</div>' + '</div>';\n        var defaultMobileTemplate = '<div data-#=ns#role=\"view\" class=\"k-grid-filter-menu\">' + '<div data-#=ns#role=\"header\" class=\"k-header\">' + '<a href=\"\\\\#\" class=\"k-header-cancel k-link\" title=\"#=messages.cancel#\" ' + 'aria-label=\"#=messages.cancel#\"><span class=\"k-icon k-i-arrow-chevron-left\"></span></a>' + '#=messages.filter# #=messages.into# #=title#' + '<a href=\"\\\\#\" class=\"k-header-done k-link\" title=\"#=messages.done#\" ' + 'aria-label=\"#=messages.done#\"><span class=\"k-icon k-i-check\"></span></a>' + '</div>' + '<form title=\"#=messages.title#\" class=\"k-filter-menu k-mobile-list\">' + '<ul>' + '<li>' + '<span class=\"k-filter-help-text\">#=messages.info#</span>' + '<ul>' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-filter-operator-text\">#=messages.operator#</span>' + '<select id=\"operator_#=filterMenuGuid#\" title=\"#=messages.operator#\" class=\"k-filter-operator\" data-#=ns#bind=\"value: filters[0].operator\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\" >' + '#for(var op in operators){#' + '<option value=\"#=op#\">#=operators[op]#</option>' + '#}#' + '</select>' + '</label>' + '</li>' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-filter-input-text\">#=messages.value#</span>' + '#if(values){#' + '<select id=\"value_#=filterMenuGuid#\" title=\"#=messages.value#\" data-#=ns#bind=\"value:filters[0].value\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\" >' + '<option value=\"\">#=messages.selectValue#</option>' + '#for(var val in values){#' + '<option value=\"#=values[val].value#\">#=values[val].text#</option>' + '#}#' + '</select>' + '#}else{#' + '<input id=\"value_#=filterMenuGuid#\" title=\"#=messages.value#\" data-#=ns#bind=\"value:filters[0].value\" class=\"k-value-input\" type=\"#=inputType#\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\" />' + '#}#' + '</label>' + '</li>' + '</ul>' + '#if(extra){#' + '<ul>' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-filter-logic-and-text\">#=messages.and#</span>' + '<input id=\"and_#=filterMenuGuid#\" title=\"#=messages.and#\" type=\"radio\" name=\"logic\" class=\"k-radio\" data-#=ns#bind=\"checked: logic\" value=\"and\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\" />' + '<span class=\"k-radio-label\"></span>' + '</label>' + '</li>' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-filter-logic-or-text\">#=messages.or#</span>' + '<input id=\"or_#=filterMenuGuid#\" title=\"#=messages.or#\" type=\"radio\" name=\"logic\" class=\"k-radio\" data-#=ns#bind=\"checked: logic\" value=\"or\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\" />' + '<span class=\"k-radio-label\"></label>' + '</label>' + '</li>' + '</ul>' + '<ul>' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-filter-operator-text\">#=messages.additionalOperator#</span>' + '<select id=\"additionalOperator_#=filterMenuGuid#\" title=\"#=messages.additionalOperator#\" class=\"k-filter-operator\" data-#=ns#bind=\"value: filters[1].operator\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\" >' + '#for(var op in operators){#' + '<option value=\"#=op#\">#=operators[op]#</option>' + '#}#' + '</select>' + '</label>' + '</li>' + '<li class=\"k-item\">' + '<label class=\"k-label\">' + '<span class=\"k-filter-input-text\">#=messages.additionalValue#</span>' + '#if(values){#' + '<select id=\"additionalValue_#=filterMenuGuid#\" title=\"#=messages.additionalValue#\" data-#=ns#bind=\"value:filters[1].value\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\" >' + '<option value=\"\">#=messages.selectValue#</option>' + '#for(var val in values){#' + '<option value=\"#=values[val].value#\">#=values[val].text#</option>' + '#}#' + '</select>' + '#}else{#' + '<input id=\"additionalValue_#=filterMenuGuid#\" title=\"#=messages.additionalValue#\" data-#=ns#bind=\"value:filters[1].value\" class=\"k-value-input\" type=\"#=inputType#\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\" />' + '#}#' + '</label>' + '</li>' + '</ul>' + '#}#' + '</li>' + '<li class=\"k-item k-clear-wrap\">' + '<span class=\"k-label k-clear\" title=\"#=messages.clear#\" ' + 'aria-label=\"#=messages.clear#\">#=messages.clear#</span>' + '</li>' + '</ul>' + '</form>' + '</div>';\n        var booleanMobileTemplate = '<div data-#=ns#role=\"view\" class=\"k-grid-filter-menu\">' + '<div data-#=ns#role=\"header\" class=\"k-header\">' + '<a href=\"\\\\#\" class=\"k-header-cancel k-link\" title=\"#=messages.cancel#\" ' + 'aria-label=\"#=messages.cancel#\"><span class=\"k-icon k-i-arrow-chevron-left\"></span></a>' + '#=messages.filter# #=messages.into# #=title#' + '<a href=\"\\\\#\" class=\"k-header-done k-link\" title=\"#=messages.done#\" ' + 'aria-label=\"#=messages.done#\"><span class=\"k-icon k-i-check\"></span></a>' + '</div>' + '<form title=\"#=messages.title#\" class=\"k-filter-menu k-mobile-list\">' + '<ul>' + '<li>' + '<span class=\"k-filter-help-text\">#=messages.info#</span>' + '<ul class=\"k-multicheck-bool-wrap\">' + '<li class=\"k-item\"><label class=\"k-label\">' + '<input id=\"true_#=filterMenuGuid#\" title=\"#=messages.isTrue#\" class=\"k-check\" type=\"radio\" data-#=ns#bind=\"checked: filters[0].value\" value=\"true\" name=\"filters[0].value\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\" />' + '<span class=\"k-item-title\">#=messages.isTrue#</span>' + '</label></li>' + '<li class=\"k-item\"><label class=\"k-label\">' + '<input id=\"false_#=filterMenuGuid#\" title=\"#=messages.isFalse#\" class=\"k-check\" type=\"radio\" data-#=ns#bind=\"checked: filters[0].value\" value=\"false\" name=\"filters[0].value\"/> autocomplete=\"' + AUTOCOMPLETEVALUE + '\" ' + '<span for=\"false_#=filterMenuGuid#\" class=\"k-item-title\">#=messages.isFalse#</span>' + '</label></li>' + '</ul>' + '</li>' + '<li class=\"k-item k-clear-wrap\">' + '<span class=\"k-label k-clear\" title=\"#=messages.clear#\" ' + 'aria-label=\"#=messages.clear#\">#=messages.clear#</span>' + '</li>' + '</ul>' + '</form>' + '</div>';\n        function removeFiltersForField(expression, field) {\n            if (expression.filters) {\n                expression.filters = $.grep(expression.filters, function (filter) {\n                    removeFiltersForField(filter, field);\n                    if (filter.filters) {\n                        return filter.filters.length;\n                    } else {\n                        return filter.field != field;\n                    }\n                });\n            }\n        }\n        function convertItems(items) {\n            var idx, length, item, value, text, result;\n            if (items && items.length) {\n                result = [];\n                for (idx = 0, length = items.length; idx < length; idx++) {\n                    item = items[idx];\n                    text = item.text !== '' ? item.text || item.value || item : item.text;\n                    value = item.value == null ? item.text || item : item.value;\n                    result[idx] = {\n                        text: text,\n                        value: value\n                    };\n                }\n            }\n            return result;\n        }\n        function clearFilter(filters, field) {\n            return $.grep(filters, function (expr) {\n                if (expr.filters) {\n                    expr.filters = $.grep(expr.filters, function (nested) {\n                        return nested.field != field;\n                    });\n                    return expr.filters.length;\n                }\n                return expr.field != field;\n            });\n        }\n        var FilterMenu = Widget.extend({\n            init: function (element, options) {\n                var that = this, type = 'string', operators, initial, link, field;\n                Widget.fn.init.call(that, element, options);\n                operators = that.operators = options.operators || {};\n                element = that.element;\n                options = that.options;\n                if (!options.appendToElement) {\n                    link = element.addClass('k-with-icon k-filterable').find('.k-grid-filter');\n                    if (!link[0]) {\n                        link = element.prepend('<a class=\"k-grid-filter\" href=\"#\" title=\"' + options.messages.filter + '\" aria-label=\"' + options.messages.filter + '\"><span class=\"k-icon k-i-filter\"></span></a>').find('.k-grid-filter');\n                    }\n                    link.attr('tabindex', -1).on('click' + NS, proxy(that._click, that));\n                }\n                that.link = link || $();\n                that.dataSource = DataSource.create(options.dataSource);\n                that.field = options.field || element.attr(kendo.attr('field'));\n                that.model = that.dataSource.reader.model;\n                that._parse = function (value) {\n                    return value != null ? value + '' : value;\n                };\n                if (that.model && that.model.fields) {\n                    field = that.model.fields[that.field];\n                    if (field) {\n                        type = field.type || 'string';\n                        if (field.parse) {\n                            that._parse = proxy(field.parse, field);\n                        }\n                    }\n                }\n                if (options.values) {\n                    type = 'enums';\n                }\n                that.type = type;\n                operators = operators[type] || options.operators[type];\n                for (initial in operators) {\n                    break;\n                }\n                that._defaultFilter = function () {\n                    return {\n                        field: that.field,\n                        operator: initial || 'eq',\n                        value: ''\n                    };\n                };\n                that._refreshHandler = proxy(that.refresh, that);\n                that.dataSource.bind(CHANGE, that._refreshHandler);\n                if (options.appendToElement) {\n                    that._init();\n                } else {\n                    that.refresh();\n                }\n            },\n            _init: function () {\n                var that = this, ui = that.options.ui, setUI = isFunction(ui), role;\n                that.pane = that.options.pane;\n                if (that.pane) {\n                    that._isMobile = true;\n                }\n                if (!setUI) {\n                    role = ui || roles[that.type];\n                }\n                if (that._isMobile) {\n                    that._createMobileForm(role);\n                } else {\n                    that._createForm(role);\n                }\n                that.form.on('submit' + NS, proxy(that._submit, that)).on('reset' + NS, proxy(that._reset, that));\n                if (setUI) {\n                    that.form.find('.k-textbox').removeClass('k-textbox').each(function () {\n                        ui($(this));\n                    });\n                }\n                that.form.find('[' + kendo.attr('role') + '=numerictextbox]').removeClass('k-textbox').end().find('[' + kendo.attr('role') + '=datetimepicker]').removeClass('k-textbox').end().find('[' + kendo.attr('role') + '=timepicker]').removeClass('k-textbox').end().find('[' + kendo.attr('role') + '=datepicker]').removeClass('k-textbox');\n                that.refresh();\n                that.trigger(INIT, {\n                    field: that.field,\n                    container: that.form\n                });\n                kendo.cycleForm(that.form);\n            },\n            _createForm: function (role) {\n                var that = this, options = that.options, operators = that.operators || {}, type = that.type, hasCustomTemplate = isFunction(that.options.ui);\n                operators = operators[type] || options.operators[type];\n                that.form = $('<form title=\"' + that.options.messages.title + '\" class=\"k-filter-menu\"/>').html(kendo.template(type === 'boolean' ? hasCustomTemplate ? customBooleanTemplate : booleanTemplate : defaultTemplate)({\n                    field: that.field,\n                    format: options.format,\n                    ns: kendo.ns,\n                    messages: options.messages,\n                    extra: options.extra,\n                    operators: operators,\n                    type: type,\n                    role: role,\n                    values: convertItems(options.values)\n                }));\n                if (!options.appendToElement) {\n                    that.popup = that.form[POPUP]({\n                        anchor: that.link,\n                        open: proxy(that._open, that),\n                        activate: proxy(that._activate, that),\n                        close: function () {\n                            if (that.options.closeCallback) {\n                                that.options.closeCallback(that.element);\n                            }\n                        }\n                    }).data(POPUP);\n                } else {\n                    that.element.append(that.form);\n                    that.popup = that.element.closest('.k-popup').data(POPUP);\n                }\n                that.form.on('keydown' + NS, proxy(that._keydown, that));\n            },\n            _createMobileForm: function (role) {\n                var that = this, options = that.options, operators = that.operators || {}, filterMenuGuid = kendo.guid(), type = that.type;\n                operators = operators[type] || options.operators[type];\n                that.form = $('<div />').html(kendo.template(type === 'boolean' ? booleanMobileTemplate : defaultMobileTemplate)({\n                    field: that.field,\n                    title: options.title || that.field,\n                    format: options.format,\n                    ns: kendo.ns,\n                    messages: options.messages,\n                    extra: options.extra,\n                    operators: operators,\n                    filterMenuGuid: filterMenuGuid,\n                    type: type,\n                    role: role,\n                    inputType: mobileRoles[type],\n                    values: convertItems(options.values)\n                }));\n                that.view = that.pane.append(that.form.html());\n                that.form = that.view.element.find('form');\n                that.view.element.on('click', '.k-header-done', function (e) {\n                    that.form.submit();\n                    e.preventDefault();\n                }).on('click', '.k-header-cancel', function (e) {\n                    that._closeForm();\n                    e.preventDefault();\n                }).on('click', '.k-clear', function (e) {\n                    that._mobileClear();\n                    e.preventDefault();\n                });\n                that.view.bind('show', function () {\n                    that.refresh();\n                });\n            },\n            refresh: function () {\n                var that = this, expression = that.dataSource.filter() || {\n                        filters: [],\n                        logic: 'and'\n                    };\n                var defaultFilters = [that._defaultFilter()];\n                var defaultOperator = that._defaultFilter().operator;\n                if (that.options.extra || defaultOperator !== 'isnull' && defaultOperator !== 'isnullorempty' && defaultOperator !== 'isnotnullorempty' && defaultOperator !== 'isnotnull' && defaultOperator !== 'isempty' && defaultOperator !== 'isnotempty') {\n                    defaultFilters.push(that._defaultFilter());\n                }\n                that.filterModel = kendo.observable({\n                    logic: 'and',\n                    filters: defaultFilters\n                });\n                if (that.form) {\n                    kendo.bind(that.form.children().first(), that.filterModel);\n                }\n                if (that._bind(expression)) {\n                    that.link.addClass('k-state-active');\n                } else {\n                    that.link.removeClass('k-state-active');\n                }\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                if (that.form) {\n                    kendo.unbind(that.form);\n                    kendo.destroy(that.form);\n                    that.form.unbind(NS);\n                    if (that.popup) {\n                        that.popup.destroy();\n                        that.popup = null;\n                    }\n                    that.form = null;\n                }\n                if (that.view) {\n                    that.view.purge();\n                    that.view = null;\n                }\n                that.link.unbind(NS);\n                if (that._refreshHandler) {\n                    that.dataSource.unbind(CHANGE, that._refreshHandler);\n                    that.dataSource = null;\n                }\n                that.element = that.link = that._refreshHandler = that.filterModel = null;\n            },\n            _bind: function (expression) {\n                var that = this, filters = expression.filters, idx, length, found = false, current = 0, filterModel = that.filterModel, currentFilter, filter;\n                for (idx = 0, length = filters.length; idx < length; idx++) {\n                    filter = filters[idx];\n                    if (filter.field == that.field) {\n                        filterModel.set('logic', expression.logic);\n                        currentFilter = filterModel.filters[current];\n                        if (!currentFilter) {\n                            filterModel.filters.push({ field: that.field });\n                            currentFilter = filterModel.filters[current];\n                        }\n                        currentFilter.set('value', that._parse(filter.value));\n                        currentFilter.set('operator', filter.operator);\n                        current++;\n                        found = true;\n                    } else if (filter.filters) {\n                        found = found || that._bind(filter);\n                    }\n                }\n                return found;\n            },\n            _stripFilters: function (filters) {\n                return $.grep(filters, function (filter) {\n                    return filter.value !== '' && filter.value != null || (filter.operator === 'isnull' || filter.operator === 'isnotnull' || filter.operator === 'isempty' || filter.operator === 'isnotempty' || filter.operator == 'isnullorempty' || filter.operator == 'isnotnullorempty');\n                });\n            },\n            _merge: function (expression) {\n                var that = this, logic = expression.logic || 'and', filters = this._stripFilters(expression.filters), filter, result = that.dataSource.filter() || {\n                        filters: [],\n                        logic: 'and'\n                    }, idx, length;\n                removeFiltersForField(result, that.field);\n                for (idx = 0, length = filters.length; idx < length; idx++) {\n                    filter = filters[idx];\n                    filter.value = that._parse(filter.value);\n                }\n                if (filters.length) {\n                    if (result.filters.length) {\n                        expression.filters = filters;\n                        if (result.logic !== 'and') {\n                            result.filters = [{\n                                    logic: result.logic,\n                                    filters: result.filters\n                                }];\n                            result.logic = 'and';\n                        }\n                        if (filters.length > 1) {\n                            result.filters.push(expression);\n                        } else {\n                            result.filters.push(filters[0]);\n                        }\n                    } else {\n                        result.filters = filters;\n                        result.logic = logic;\n                    }\n                }\n                return result;\n            },\n            filter: function (expression) {\n                var filters = this._stripFilters(expression.filters);\n                if (filters.length && this.trigger('change', {\n                        filter: {\n                            logic: expression.logic,\n                            filters: filters\n                        },\n                        field: this.field\n                    })) {\n                    return;\n                }\n                expression = this._merge(expression);\n                if (expression.filters.length) {\n                    this.dataSource.filter(expression);\n                }\n            },\n            clear: function () {\n                var that = this, expression = that.dataSource.filter() || { filters: [] };\n                if (this.trigger('change', {\n                        filter: null,\n                        field: that.field\n                    })) {\n                    return;\n                }\n                that._removeFilter(expression);\n            },\n            _mobileClear: function () {\n                var that = this;\n                var viewElement = that.view.element;\n                if (that.type === 'boolean') {\n                    var booleanRadioButton = viewElement.find('.k-check:checked');\n                    var booleanRadioButtonValue = booleanRadioButton.val();\n                    booleanRadioButton.val('');\n                    booleanRadioButton.trigger('change');\n                    booleanRadioButton.val(booleanRadioButtonValue);\n                    booleanRadioButton.prop('checked', false);\n                } else {\n                    var operatorSelects = viewElement.find('select');\n                    operatorSelects.each(function (i, e) {\n                        var input = $(e);\n                        input.val(input.find('option:first').val());\n                        input.trigger('change');\n                    });\n                    if (that.type === 'string' || that.type === 'date' || that.type === 'number') {\n                        var valueInputs = viewElement.find('.k-value-input');\n                        valueInputs.each(function (i, e) {\n                            var input = $(e);\n                            input.val('');\n                            input.trigger('change');\n                        });\n                    }\n                    if (that.options.extra) {\n                        var andLogicRadio = viewElement.find('[name=logic]').first();\n                        andLogicRadio.prop('checked', true);\n                        andLogicRadio.trigger('change');\n                    }\n                }\n            },\n            _removeFilter: function (expression) {\n                var that = this;\n                expression.filters = $.grep(expression.filters, function (filter) {\n                    if (filter.filters) {\n                        filter.filters = clearFilter(filter.filters, that.field);\n                        return filter.filters.length;\n                    }\n                    return filter.field != that.field;\n                });\n                if (!expression.filters.length) {\n                    expression = null;\n                }\n                that.dataSource.filter(expression);\n            },\n            _submit: function (e) {\n                e.preventDefault();\n                e.stopPropagation();\n                var expression = this.filterModel.toJSON();\n                var containsFilters = $.grep(expression.filters, function (filter) {\n                    return filter.value !== '' && filter.value !== null;\n                });\n                if (this._checkForNullOrEmptyFilter(expression) || containsFilters && containsFilters.length) {\n                    this.filter(expression);\n                } else {\n                    var currentExpression = this.dataSource.filter();\n                    if (currentExpression) {\n                        currentExpression.filters.push(expression);\n                        expression = currentExpression;\n                    }\n                    this._removeFilter(expression);\n                }\n                this._closeForm();\n            },\n            _checkForNullOrEmptyFilter: function (expression) {\n                if (!expression || !expression.filters || !expression.filters.length) {\n                    return false;\n                }\n                var firstNullOrEmpty = false;\n                var secondNullOrEmpty = false;\n                var operator;\n                if (expression.filters[0]) {\n                    operator = expression.filters[0].operator;\n                    firstNullOrEmpty = operator == 'isnull' || operator == 'isnotnull' || operator == 'isnotempty' || operator == 'isempty' || operator == 'isnullorempty' || operator == 'isnotnullorempty';\n                }\n                if (expression.filters[1]) {\n                    operator = expression.filters[1].operator;\n                    secondNullOrEmpty = operator == 'isnull' || operator == 'isnotnull' || operator == 'isnotempty' || operator == 'isempty' || operator == 'isnullorempty' || operator == 'isnotnullorempty';\n                }\n                return !this.options.extra && firstNullOrEmpty || this.options.extra && (firstNullOrEmpty || secondNullOrEmpty);\n            },\n            _reset: function () {\n                this.clear();\n                if (this.options.search && this.container) {\n                    this.container.find('label').parent().show();\n                }\n                this._closeForm();\n            },\n            _closeForm: function () {\n                if (this._isMobile) {\n                    this.pane.navigate('', this.options.animations.right);\n                } else {\n                    this.popup.close();\n                }\n            },\n            _click: function (e) {\n                e.preventDefault();\n                e.stopPropagation();\n                if (!this.popup && !this.pane) {\n                    this._init();\n                }\n                if (this._isMobile) {\n                    this.pane.navigate(this.view, this.options.animations.left);\n                } else {\n                    this.popup.toggle();\n                }\n            },\n            _open: function () {\n                var popup;\n                $('.k-filter-menu').not(this.form).each(function () {\n                    popup = $(this).data(POPUP);\n                    if (popup) {\n                        popup.close();\n                    }\n                });\n            },\n            _activate: function () {\n                this.form.find(':kendoFocusable:first').focus();\n                this.trigger(OPEN, {\n                    field: this.field,\n                    container: this.form\n                });\n            },\n            _keydown: function (e) {\n                if (e.keyCode == kendo.keys.ESC) {\n                    this.popup.close();\n                }\n            },\n            events: [\n                INIT,\n                'change',\n                OPEN\n            ],\n            options: {\n                name: 'FilterMenu',\n                extra: true,\n                appendToElement: false,\n                type: 'string',\n                operators: {\n                    string: {\n                        eq: EQ,\n                        neq: NEQ,\n                        startswith: 'Starts with',\n                        contains: 'Contains',\n                        doesnotcontain: 'Does not contain',\n                        endswith: 'Ends with',\n                        isnull: 'Is null',\n                        isnotnull: 'Is not null',\n                        isempty: 'Is empty',\n                        isnotempty: 'Is not empty',\n                        isnullorempty: 'Has no value',\n                        isnotnullorempty: 'Has value'\n                    },\n                    number: {\n                        eq: EQ,\n                        neq: NEQ,\n                        gte: 'Is greater than or equal to',\n                        gt: 'Is greater than',\n                        lte: 'Is less than or equal to',\n                        lt: 'Is less than',\n                        isnull: 'Is null',\n                        isnotnull: 'Is not null'\n                    },\n                    date: {\n                        eq: EQ,\n                        neq: NEQ,\n                        gte: 'Is after or equal to',\n                        gt: 'Is after',\n                        lte: 'Is before or equal to',\n                        lt: 'Is before',\n                        isnull: 'Is null',\n                        isnotnull: 'Is not null'\n                    },\n                    enums: {\n                        eq: EQ,\n                        neq: NEQ,\n                        isnull: 'Is null',\n                        isnotnull: 'Is not null'\n                    }\n                },\n                messages: {\n                    info: 'Show items with value that:',\n                    title: 'Show items with value that:',\n                    isTrue: 'is true',\n                    isFalse: 'is false',\n                    filter: 'Filter',\n                    clear: 'Clear',\n                    and: 'And',\n                    or: 'Or',\n                    selectValue: '-Select value-',\n                    operator: 'Operator',\n                    value: 'Value',\n                    additionalValue: 'Additional value',\n                    additionalOperator: 'Additional operator',\n                    logic: 'Filters logic',\n                    cancel: 'Cancel',\n                    done: 'Done',\n                    into: 'in'\n                },\n                animations: {\n                    left: 'slide',\n                    right: 'slide:right'\n                }\n            }\n        });\n        var multiCheckNS = '.kendoFilterMultiCheck';\n        function filterValuesForField(expression, field) {\n            if (expression.filters) {\n                expression.filters = $.grep(expression.filters, function (filter) {\n                    filterValuesForField(filter, field);\n                    if (filter.filters) {\n                        return filter.filters.length;\n                    } else {\n                        return filter.field == field && filter.operator == 'eq';\n                    }\n                });\n            }\n        }\n        function flatFilterValues(expression) {\n            if (expression.logic == 'and' && expression.filters.length > 1) {\n                return [];\n            }\n            if (expression.filters) {\n                return $.map(expression.filters, function (filter) {\n                    return flatFilterValues(filter);\n                });\n            } else if (expression.value !== undefined) {\n                return [expression.value];\n            } else {\n                return [];\n            }\n        }\n        function distinct(items, field) {\n            var getter = kendo.getter(field, true), result = [], index = 0, seen = {};\n            while (index < items.length) {\n                var item = items[index++], text = getter(item);\n                if (text !== undefined && !seen.hasOwnProperty(text)) {\n                    result.push(item);\n                    seen[text] = true;\n                }\n            }\n            return result;\n        }\n        function removeDuplicates(dataSelector, dataTextField) {\n            return function (e) {\n                var items = dataSelector(e);\n                return distinct(items, dataTextField);\n            };\n        }\n        var DataSource = kendo.data.DataSource;\n        var multiCkeckMobileTemplate = '<div data-#=ns#role=\"view\" class=\"k-grid-filter-menu\">' + '<div data-#=ns#role=\"header\" class=\"k-header\">' + '<a href=\"\\\\#\" class=\"k-header-cancel k-link\" title=\"#=messages.cancel#\" ' + 'aria-label=\"#=messages.cancel#\"><span class=\"k-icon k-i-arrow-chevron-left\"></span></a>' + '#=messages.filter# #=messages.into# #=title#' + '<a href=\"\\\\#\" class=\"k-header-done k-link\" title=\"#=messages.done#\" ' + 'aria-label=\"#=messages.done#\"><span class=\"k-icon k-i-check\"></span></a>' + '</div>' + '<form class=\"k-filter-menu k-mobile-list\">' + '<ul>' + '#if(search){#' + '<li class=\"k-textbox k-space-right\">' + '<input placeholder=\"#=messages.search#\" title=\"#=messages.search#\" autocomplete=\"' + AUTOCOMPLETEVALUE + '\"  />' + '<span class=\"k-icon k-i-zoom\" />' + '</li>' + '#}#' + '<li class=\"k-filter-tools\">' + '<span style=\"#=checkAll ? \"\" : \"visibility: hidden;\" #\" class=\"k-label k-select-all\" title=\"#=messages.checkAll#\" ' + 'aria-label=\"#=messages.checkAll#\">#=messages.checkAll#</span>' + '<span class=\"k-label k-clear-all\" title=\"#=messages.clearAll#\" ' + 'aria-label=\"#=messages.clearAll#\">#=messages.clearAll#</span>' + '</li>' + '#if(messages.selectedItemsFormat){#' + '<li>' + '<div class=\"k-filter-selected-items\"></div>' + '</li>' + '#}#' + '<li>' + '<ul class=\"k-multicheck-wrap\"></ul>' + '</li>' + '</ul>' + '</form>' + '</div>';\n        var FilterMultiCheck = Widget.extend({\n            init: function (element, options) {\n                Widget.fn.init.call(this, element, options);\n                options = this.options;\n                this.element = $(element);\n                var field = this.field = this.options.field || this.element.attr(kendo.attr('field'));\n                var checkSource = options.checkSource;\n                if (this._foreignKeyValues()) {\n                    this.checkSource = DataSource.create(options.values);\n                    this.checkSource.fetch();\n                } else if (options.forceUnique) {\n                    checkSource = $.extend(true, {}, options.dataSource.options);\n                    delete checkSource.pageSize;\n                    this.checkSource = DataSource.create(checkSource);\n                    this.checkSource.reader.data = removeDuplicates(this.checkSource.reader.data, this.field);\n                } else {\n                    this.checkSource = DataSource.create(checkSource);\n                }\n                this.dataSource = options.dataSource;\n                this.model = this.dataSource.reader.model;\n                this._parse = function (value) {\n                    return value + '';\n                };\n                if (this.model && this.model.fields) {\n                    field = this.model.fields[this.field];\n                    if (field) {\n                        if (field.type == 'number') {\n                            this._parse = function (value) {\n                                if (typeof value === 'string' && value.toLowerCase() === 'null') {\n                                    return null;\n                                }\n                                return parseFloat(value);\n                            };\n                        } else if (field.parse) {\n                            this._parse = proxy(field.parse, field);\n                        }\n                        this.type = field.type || 'string';\n                    }\n                }\n                if (!options.appendToElement) {\n                    this._createLink();\n                } else {\n                    this._init();\n                }\n                this._refreshHandler = proxy(this.refresh, this);\n                this.dataSource.bind(CHANGE, this._refreshHandler);\n            },\n            _createLink: function () {\n                var element = this.element;\n                var link = element.addClass('k-with-icon k-filterable').find('.k-grid-filter');\n                if (!link[0]) {\n                    link = element.prepend('<a class=\"k-grid-filter\" href=\"#\" title=\"' + this.options.messages.filter + '\" aria-label=\"' + this.options.messages.filter + '\"><span class=\"k-icon k-i-filter\"/></a>').find('.k-grid-filter');\n                }\n                this._link = link.attr('tabindex', -1).on('click' + NS, proxy(this._click, this));\n            },\n            _init: function () {\n                var that = this;\n                var forceUnique = this.options.forceUnique;\n                var options = this.options;\n                this.pane = options.pane;\n                if (this.pane) {\n                    this._isMobile = true;\n                }\n                this._createForm();\n                if (this._foreignKeyValues()) {\n                    this.refresh();\n                } else if (forceUnique && !this.checkSource.options.serverPaging && this.dataSource.data().length) {\n                    this.checkSource.data(distinct(this.dataSource.data(), this.field));\n                    this.refresh();\n                } else {\n                    this._attachProgress();\n                    this.checkSource.fetch(function () {\n                        that.refresh.call(that);\n                    });\n                }\n                if (!this.options.forceUnique) {\n                    this.checkChangeHandler = function () {\n                        that.container.empty();\n                        that.refresh();\n                    };\n                    this.checkSource.bind(CHANGE, this.checkChangeHandler);\n                }\n                this.form.on('keydown' + multiCheckNS, proxy(this._keydown, this)).on('submit' + multiCheckNS, proxy(this._filter, this)).on('reset' + multiCheckNS, proxy(this._reset, this));\n                this.trigger(INIT, {\n                    field: this.field,\n                    container: this.form\n                });\n            },\n            _attachProgress: function () {\n                var that = this;\n                this._progressHandler = function () {\n                    ui.progress(that.container, true);\n                };\n                this._progressHideHandler = function () {\n                    ui.progress(that.container, false);\n                };\n                this.checkSource.bind('progress', this._progressHandler).bind('change', this._progressHideHandler);\n            },\n            _input: function () {\n                var that = this;\n                that._clearTypingTimeout();\n                that._typingTimeout = setTimeout(function () {\n                    that.search();\n                }, 100);\n            },\n            _clearTypingTimeout: function () {\n                if (this._typingTimeout) {\n                    clearTimeout(this._typingTimeout);\n                    this._typingTimeout = null;\n                }\n            },\n            search: function () {\n                var ignoreCase = this.options.ignoreCase;\n                var searchString = this.searchTextBox[0].value;\n                var labels = this.container.find('label');\n                if (ignoreCase) {\n                    searchString = searchString.toLowerCase();\n                }\n                var i = 0;\n                if (this.options.checkAll && labels.length) {\n                    if (!this._isMobile) {\n                        labels[0].parentNode.style.display = searchString ? 'none' : '';\n                        i++;\n                    } else {\n                        this.view.element.find('.k-select-all')[0].style.visibility = searchString ? 'hidden' : '';\n                    }\n                }\n                while (i < labels.length) {\n                    var label = labels[i];\n                    var labelText = label.textContent || label.innerText;\n                    if (ignoreCase) {\n                        labelText = labelText.toLowerCase();\n                    }\n                    label.parentNode.style.display = labelText.indexOf(searchString) >= 0 ? '' : 'none';\n                    i++;\n                }\n            },\n            _activate: function () {\n                this.form.find(':kendoFocusable:first').focus();\n                this.trigger(OPEN, {\n                    field: this.field,\n                    container: this.form\n                });\n            },\n            _createForm: function () {\n                var options = this.options;\n                var html = '';\n                var that = this;\n                if (!this._isMobile) {\n                    html += '<div class=\\'k-filter-menu-container\\'>';\n                    if (options.search) {\n                        html += '<div class=\\'k-textbox k-space-right\\'>' + '<input placeholder=\\'' + options.messages.search + '\\'/>' + '<span class=\\'k-icon k-i-zoom\\' />' + '</div>';\n                    }\n                    html += '<ul class=\\'k-reset k-multicheck-wrap\\'></ul>';\n                    if (options.messages.selectedItemsFormat) {\n                        html += '<div class=\\'k-filter-selected-items\\'>' + kendo.format(options.messages.selectedItemsFormat, 0) + '</div>';\n                    }\n                    html += '<div class=\\'k-action-buttons\\'>';\n                    html += '<button type=\\'submit\\' class=\\'k-button k-primary\\'>' + options.messages.filter + '</button>';\n                    html += '<button type=\\'reset\\' class=\\'k-button\\'>' + options.messages.clear + '</button>';\n                    html += '</div>';\n                    html += '</div>';\n                    this.form = $('<form class=\"k-filter-menu\"/>').html(html);\n                    this.container = this.form.find('.k-multicheck-wrap');\n                }\n                if (this._isMobile) {\n                    that.form = $('<div />').html(kendo.template(multiCkeckMobileTemplate)({\n                        field: that.field,\n                        title: options.title || that.field,\n                        ns: kendo.ns,\n                        messages: options.messages,\n                        search: options.search,\n                        checkAll: options.checkAll\n                    }));\n                    that.view = that.pane.append(that.form.html());\n                    that.form = that.view.element.find('form');\n                    var element = this.view.element;\n                    this.container = element.find('.k-multicheck-wrap');\n                    element.on('click', '.k-header-done', function (e) {\n                        that.form.submit();\n                        e.preventDefault();\n                    }).on('click', '.k-header-cancel', function (e) {\n                        that._closeForm();\n                        e.preventDefault();\n                    }).on('click', '.k-clear-all', function (e) {\n                        that._mobileCheckAll(false);\n                        e.preventDefault();\n                    }).on('click', '.k-select-all', function (e) {\n                        that._mobileCheckAll(true);\n                        e.preventDefault();\n                    });\n                    that.view.bind('show', function () {\n                        that.refresh();\n                    });\n                } else {\n                    if (!options.appendToElement) {\n                        that.popup = that.form.kendoPopup({\n                            anchor: that._link,\n                            open: proxy(that._open, that),\n                            activate: proxy(that._activate, that),\n                            close: function () {\n                                if (that.options.closeCallback) {\n                                    that.options.closeCallback(that.element);\n                                }\n                            }\n                        }).data(POPUP);\n                    } else {\n                        this.popup = this.element.closest('.k-popup').data(POPUP);\n                        this.element.append(this.form);\n                    }\n                }\n                if (options.search) {\n                    this.searchTextBox = this.form.find('.k-textbox > input');\n                    this.searchTextBox.on('input', proxy(this._input, this));\n                }\n            },\n            createCheckAllItem: function () {\n                var options = this.options;\n                var template = kendo.template(options.itemTemplate({\n                    field: 'all',\n                    mobile: this._isMobile\n                }));\n                var checkAllContainer = $(template({ all: options.messages.checkAll }));\n                this.container.prepend(checkAllContainer);\n                this.checkBoxAll = checkAllContainer.find(':checkbox').eq(0).addClass('k-check-all');\n                this.checkAllHandler = proxy(this.checkAll, this);\n                this.checkBoxAll.on(CHANGE + multiCheckNS, this.checkAllHandler);\n            },\n            updateCheckAllState: function () {\n                if (this.options.messages.selectedItemsFormat) {\n                    this.form.find('.k-filter-selected-items').text(kendo.format(this.options.messages.selectedItemsFormat, this.container.find(':checked:not(.k-check-all)').length));\n                }\n                if (this.checkBoxAll) {\n                    var state = this.container.find(':checkbox:not(.k-check-all)').length == this.container.find(':checked:not(.k-check-all)').length;\n                    this.checkBoxAll.prop('checked', state);\n                }\n            },\n            refresh: function (e) {\n                var forceUnique = this.options.forceUnique;\n                var dataSource = this.dataSource;\n                var filters = this.getFilterArray();\n                if (this._link) {\n                    this._link.toggleClass('k-state-active', filters.length !== 0);\n                }\n                if (this.form) {\n                    if (e && forceUnique && e.sender === dataSource && !dataSource.options.serverPaging && (e.action == 'itemchange' || e.action == 'add' || e.action == 'remove' || dataSource.options.autoSync && e.action === 'sync') && !this._foreignKeyValues()) {\n                        this.checkSource.data(distinct(this.dataSource.data(), this.field));\n                        this.container.empty();\n                    }\n                    if (this.container.is(':empty')) {\n                        this.createCheckBoxes();\n                    }\n                    this.checkValues(filters);\n                    this.trigger(REFRESH);\n                }\n            },\n            getFilterArray: function () {\n                var expression = $.extend(true, {}, {\n                    filters: [],\n                    logic: 'and'\n                }, this.dataSource.filter());\n                filterValuesForField(expression, this.field);\n                var flatValues = flatFilterValues(expression);\n                return flatValues;\n            },\n            createCheckBoxes: function () {\n                var options = this.options;\n                var data;\n                var templateOptions = {\n                    field: this.field,\n                    format: options.format,\n                    mobile: this._isMobile,\n                    type: this.type\n                };\n                if (!this.options.forceUnique) {\n                    data = this.checkSource.view();\n                } else if (this._foreignKeyValues()) {\n                    data = this.checkSource.data();\n                    templateOptions.valueField = 'value';\n                    templateOptions.field = 'text';\n                } else {\n                    data = this.checkSource.data();\n                }\n                var template = kendo.template(options.itemTemplate(templateOptions));\n                var itemsHtml = kendo.render(template, data);\n                if (options.checkAll && !this._isMobile) {\n                    this.createCheckAllItem();\n                }\n                this.container.on(CHANGE + multiCheckNS, ':checkbox', proxy(this.updateCheckAllState, this));\n                this.container.append(itemsHtml);\n            },\n            checkAll: function () {\n                var state = this.checkBoxAll.is(':checked');\n                this.container.find(':checkbox').prop('checked', state);\n            },\n            checkValues: function (values) {\n                var that = this;\n                $($.grep(this.container.find(':checkbox').prop('checked', false), function (ele) {\n                    var found = false;\n                    if ($(ele).is('.k-check-all')) {\n                        return;\n                    }\n                    var checkBoxVal = that._parse($(ele).val());\n                    for (var i = 0; i < values.length; i++) {\n                        if (that.type == 'date') {\n                            if (values[i] && checkBoxVal) {\n                                found = values[i].getTime() == checkBoxVal.getTime();\n                            } else if (values[i] === null && checkBoxVal === null) {\n                                found = true;\n                            } else {\n                                found = false;\n                            }\n                        } else {\n                            found = values[i] == checkBoxVal;\n                        }\n                        if (found) {\n                            return found;\n                        }\n                    }\n                })).prop('checked', true);\n                this.updateCheckAllState();\n            },\n            _mobileCheckAll: function (state) {\n                var that = this;\n                var checkboxes = that.container.find(':checkbox');\n                checkboxes.each(function (i, e) {\n                    var checkbox = $(e);\n                    checkbox.prop('checked', state);\n                    checkbox.trigger('change');\n                });\n            },\n            _filter: function (e) {\n                e.preventDefault();\n                e.stopPropagation();\n                var expression = { logic: 'or' };\n                var that = this;\n                expression.filters = $.map(this.form.find(':checkbox:checked:not(.k-check-all)'), function (item) {\n                    return {\n                        value: $(item).val(),\n                        operator: 'eq',\n                        field: that.field\n                    };\n                });\n                if (expression.filters.length && this.trigger('change', {\n                        filter: expression,\n                        field: that.field\n                    })) {\n                    return;\n                }\n                expression = this._merge(expression);\n                if (expression.filters.length) {\n                    this.dataSource.filter(expression);\n                } else {\n                    this.clear();\n                }\n                this._closeForm();\n            },\n            _stripFilters: function (filters) {\n                return $.grep(filters, function (filter) {\n                    return filter.value != null;\n                });\n            },\n            _foreignKeyValues: function () {\n                var options = this.options;\n                return options.values && !options.checkSource;\n            },\n            destroy: function () {\n                var that = this;\n                Widget.fn.destroy.call(that);\n                if (that.form) {\n                    kendo.unbind(that.form);\n                    kendo.destroy(that.form);\n                    that.form.unbind(multiCheckNS);\n                    if (that.popup) {\n                        that.popup.destroy();\n                        that.popup = null;\n                    }\n                    that.form = null;\n                    if (that.container) {\n                        that.container.unbind(multiCheckNS);\n                        that.container = null;\n                    }\n                    if (that.checkBoxAll) {\n                        that.checkBoxAll.unbind(multiCheckNS);\n                    }\n                }\n                if (that.view) {\n                    that.view.purge();\n                    that.view = null;\n                }\n                if (that._link) {\n                    that._link.unbind(NS);\n                }\n                if (that._refreshHandler) {\n                    that.dataSource.unbind(CHANGE, that._refreshHandler);\n                    that.dataSource = null;\n                }\n                if (that.checkChangeHandler) {\n                    that.checkSource.unbind(CHANGE, that.checkChangeHandler);\n                }\n                if (that._progressHandler) {\n                    that.checkSource.unbind('progress', that._progressHandler);\n                }\n                if (that._progressHideHandler) {\n                    that.checkSource.unbind('change', that._progressHideHandler);\n                }\n                this._clearTypingTimeout();\n                this.searchTextBox = null;\n                that.element = that.checkSource = that.container = that.checkBoxAll = that._link = that._refreshHandler = that.checkAllHandler = null;\n            },\n            options: {\n                name: 'FilterMultiCheck',\n                itemTemplate: function (options) {\n                    var field = options.field;\n                    var format = options.format;\n                    var valueField = options.valueField;\n                    var mobile = options.mobile;\n                    var valueFormat = '';\n                    if (valueField === undefined) {\n                        valueField = field;\n                    }\n                    if (options.type == 'date') {\n                        valueFormat = ':yyyy-MM-ddTHH:mm:sszzz';\n                    }\n                    return '<li class=\\'k-item\\'>' + '<label class=\\'k-label\\'>' + '<input type=\\'checkbox\\' class=\\'' + (mobile ? 'k-check' : '') + '\\'  value=\\'#:kendo.format(\\'{0' + valueFormat + '}\\',' + valueField + ')#\\'/>' + '<span class=\\'k-item-title\\'>#:kendo.format(\\'' + (format ? format : '{0}') + '\\', ' + field + ')#</span>' + '</label>' + '</li>';\n                },\n                checkAll: true,\n                search: false,\n                ignoreCase: true,\n                appendToElement: false,\n                messages: {\n                    checkAll: 'Select All',\n                    clearAll: 'Clear All',\n                    clear: 'Clear',\n                    filter: 'Filter',\n                    search: 'Search',\n                    cancel: 'Cancel',\n                    selectedItemsFormat: '{0} items selected',\n                    done: 'Done',\n                    into: 'in'\n                },\n                forceUnique: true,\n                animations: {\n                    left: 'slide',\n                    right: 'slide:right'\n                }\n            },\n            events: [\n                INIT,\n                REFRESH,\n                'change',\n                OPEN\n            ]\n        });\n        $.extend(FilterMultiCheck.fn, {\n            _click: FilterMenu.fn._click,\n            _keydown: FilterMenu.fn._keydown,\n            _reset: FilterMenu.fn._reset,\n            _closeForm: FilterMenu.fn._closeForm,\n            _removeFilter: FilterMenu.fn._removeFilter,\n            clear: FilterMenu.fn.clear,\n            _merge: FilterMenu.fn._merge\n        });\n        ui.plugin(FilterMenu);\n        ui.plugin(FilterMultiCheck);\n    }(window.kendo.jQuery));\n    return window.kendo;\n}, typeof define == 'function' && define.amd ? define : function (a1, a2, a3) {\n    (a3 || a2)();\n}));"]}