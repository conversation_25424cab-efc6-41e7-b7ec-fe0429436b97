{"version": 3, "sources": ["messages/kendo.messages.el-GR.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "cleanFormatting", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeView", "Upload", "localization", "clearSelectedFiles", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "progress", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGRC,MAAMC,GAAGC,kBACXF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,WACTC,OAAU,QACVC,QAAW,cACXC,WAAc,kBAMhBV,MAAMC,GAAGU,cACXX,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,UACpDE,MAAS,WACTC,OAAU,QACVC,QAAW,cACXC,WAAc,kBAMhBV,MAAMC,GAAGW,aACXZ,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,UACnDQ,cAAiB,qBACjBC,eAAkB,sBAClBC,OAAU,SACVC,QAAW,SACXC,KAAQ,WACRC,SAAY,kBACZC,KAAQ,WACRC,OAAU,gBAMZpB,MAAMC,GAAGoB,SACXrB,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,UAC/CiB,KAAQ,SACRC,OAAU,SACVC,UAAa,cACbC,cAAiB,oBACjBC,YAAe,UACfC,UAAa,UACbC,cAAiB,kBACjBC,YAAe,oBACfC,aAAgB,iBAChBC,YAAe,UACfC,oBAAuB,oCACvBC,kBAAqB,iCACrBC,OAAU,QACVC,QAAW,WACXC,WAAc,qBACdC,OAAU,qBACVC,YAAe,mBACfC,WAAc,mBACdC,WAAc,gBACdC,SAAY,kBACZC,SAAY,qCACZC,gBAAmB,gCACnBC,SAAY,kCACZC,gBAAmB,0BACnBC,YAAe,cACfC,WAAc,cACdC,UAAa,QACbC,UAAa,eACbC,MAAS,OACTC,YAAe,gBACfC,WAAc,cACdC,QAAW,iBACXC,YAAe,UACfC,YAAe,QACfC,gBAAmB,mFACnBC,WAAc,oDACdC,cAAiB,+FACjBC,kBAAqB,oCACrBC,gBAAmB,wBACnBC,aAAgB,sBAChBC,WAAc,cACdC,YAAe,YACfC,eAAkB,wBAClBC,UAAa,SACbC,eAAkB,wBAClBC,SAAY,UACZC,YAAe,UACfC,oBAAuB,oCACvBC,aAAgB,WAChBC,aAAgB,WAChBC,sBAAyB,IACzBC,aAAgB,QAChBC,gBAAmB,2BACnBC,YAAe,oBACfC,cAAiB,2BACjBC,eAAkB,wBAClBC,YAAe,wBACfC,YAAe,wBACfC,UAAa,mBACbC,aAAgB,kBAChBC,SAAY,UACZC,YAAe,gBACfC,SAAY,UACZC,QAAW,OACXC,iBAAoB,iBACpBC,QAAW,UACXC,QAAW,WACXC,MAAS,SACTC,OAAU,OACVC,YAAe,cACfC,YAAe,kBACfC,WAAc,mBACdC,UAAa,eACbC,WAAc,QACdC,SAAY,YACZC,GAAM,KACNC,OAAU,YACVC,YAAe,kBACfC,gBAAmB,mBACnBC,SAAY,sBACZC,0BAA6B,iCAC7BC,UAAa,wBACbC,YAAe,sBACfC,WAAc,qBACdC,aAAgB,6BAChBC,eAAkB,2BAClBC,cAAiB,0BACjBC,gBAAmB,6BACnBC,kBAAqB,2BACrBC,iBAAoB,0BACpBC,gBAAmB,6BACnBC,kBAAqB,2BACrBC,iBAAoB,0BACpBC,YAAe,yBACfnG,QAAW,SACXoG,KAAQ,UACRC,eAAkB,6BAMpBrH,MAAMC,GAAGqH,cACXtH,MAAMC,GAAGqH,YAAYnH,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqH,YAAYnH,UAAUC,QAAQC,UACpD+C,WAAc,cACdC,QAAW,gBACXE,YAAe,QACfD,YAAe,UACfK,kBAAqB,wCACrBR,YAAe,gBACfM,WAAc,oDACdD,gBAAmB,wFACnBE,cAAiB,wFACjB6D,cAAiB,kCACjBC,OAAU,eAMZxH,MAAMC,GAAGwH,aACXzH,MAAMC,GAAGwH,WAAWtH,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwH,WAAWtH,UAAUC,QAAQC,UACnDqH,OAAU,eACVC,QAAW,eACX5G,OAAU,SACV6G,MAAS,aACTC,SAAY,eAMd7H,MAAMC,GAAGwH,aACXzH,MAAMC,GAAGwH,WAAWtH,UAAUC,QAAQ0H,UACpChI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwH,WAAWtH,UAAUC,QAAQ0H,WACnDC,QACEC,GAAM,eACNC,IAAO,mBACPC,WAAc,YACdC,SAAY,WACZC,eAAkB,eAClBC,SAAY,eACZC,OAAU,aACVC,UAAa,iBACbC,QAAW,aACXC,WAAc,kBAEhBC,QACEV,GAAM,eACNC,IAAO,mBACPU,IAAO,4BACPC,GAAM,uBACNC,IAAO,2BACPC,GAAM,sBACNR,OAAU,aACVC,UAAa,kBAEfQ,MACEf,GAAM,eACNC,IAAO,mBACPU,IAAO,sBACPC,GAAM,aACNC,IAAO,sBACPC,GAAM,aACNR,OAAU,aACVC,UAAa,kBAEfS,OACEhB,GAAM,eACNC,IAAO,mBACPK,OAAU,aACVC,UAAa,qBAOjBvI,MAAMC,GAAGgJ,aACXjJ,MAAMC,GAAGgJ,WAAW9I,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgJ,WAAW9I,UAAUC,QAAQC,UACnD6I,KAAQ,iCACRC,MAAS,gCACTzB,OAAU,eACVC,QAAW,eACX5G,OAAU,SACV6G,MAAS,aACTwB,IAAO,MACPC,GAAM,IACNC,YAAe,kBACfzB,SAAY,UACZ0B,MAAS,OACT/I,OAAU,WAMZR,MAAMC,GAAGgJ,aACXjJ,MAAMC,GAAGgJ,WAAW9I,UAAUC,QAAQ0H,UACpChI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgJ,WAAW9I,UAAUC,QAAQ0H,WACnDC,QACEC,GAAM,eACNC,IAAO,mBACPC,WAAc,YACdC,SAAY,WACZC,eAAkB,eAClBC,SAAY,eACZC,OAAU,aACVC,UAAa,iBACbC,QAAW,aACXC,WAAc,kBAEhBC,QACEV,GAAM,eACNC,IAAO,mBACPU,IAAO,4BACPC,GAAM,uBACNC,IAAO,2BACPC,GAAM,sBACNR,OAAU,aACVC,UAAa,kBAEfQ,MACEf,GAAM,eACNC,IAAO,mBACPU,IAAO,sBACPC,GAAM,aACNC,IAAO,sBACPC,GAAM,aACNR,OAAU,aACVC,UAAa,kBAEfS,OACEhB,GAAM,eACNC,IAAO,mBACPK,OAAU,aACVC,UAAa,qBAOjBvI,MAAMC,GAAGuJ,mBACXxJ,MAAMC,GAAGuJ,iBAAiBrJ,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuJ,iBAAiBrJ,UAAUC,QAAQC,UACzDoJ,SAAY,eACZ7B,MAAS,aACT7G,OAAU,SACVyG,OAAU,eAMZxH,MAAMC,GAAGyJ,QACX1J,MAAMC,GAAGyJ,MAAMvJ,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,MAAMvJ,UAAUC,QAAQC,UAC9CsJ,SACEC,SAAY,mBACZC,OAAU,oBACVC,YAAe,gBACfC,aAAgB,gBAChBC,IAAO,kBAETxJ,OAAU,QACVyJ,4BAA+B,qBAC/BC,sBAAyB,oBACzBC,QAAW,WACXC,QACEC,aAAgB,UAChBC,YAAe,UACfC,IAAO,QACPC,gBAAmB,aACnBC,UAAa,QACbC,qBAAwB,QACxBC,gBAAmB,QACnBC,MAAS,OACTzB,MAAS,SACT0B,YAAe,WAEjBC,KAAQ,aACRC,OACEC,IAAO,QACPT,IAAO,QACPU,MAAS,QACTL,MAAS,OACTM,KAAQ,WACRC,KAAQ,aAOZnL,MAAMC,GAAGmL,OACXpL,MAAMC,GAAGmL,KAAKjL,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmL,KAAKjL,UAAUC,QAAQC,UAC7CgL,UACE7K,OAAU,kBACV8K,WAAc,UACdC,OAAU,oBACVpB,QAAW,WACXqB,KAAQ,cACRC,MAAS,mBACTzB,IAAO,iBACPc,KAAQ,qBACRY,OAAU,UACVC,OAAU,YAEZC,UACEC,aAAgB,QAChBC,aAAgB,4DAChBC,cAAiB,YAEnBC,UAAa,4BAMfhM,MAAMC,GAAGgM,WACXjM,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQC,UACjD6L,OAAU,wBACVC,QAAW,aACXC,cAAiB,oBACjBC,MAAS,gBACThB,UACEG,KAAQ,cACRG,OAAU,WACVL,WAAc,QACdC,OAAU,oBACVe,YAAe,4BACfnC,QAAW,WACXsB,MAAS,mBACTzB,IAAO,qBAOXhK,MAAMC,GAAGsM,YACXvM,MAAMC,GAAGsM,UAAUpM,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,UAAUpM,UAAUC,QAAQC,UAClDmM,MAAS,iEAMXxM,MAAMC,GAAGwM,iBACXzM,MAAMC,GAAGwM,eAAetM,UAAUC,QAChCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,eAAetM,UAAUC,SAC/CsM,YAAe,eACfC,cAAiB,kBAMnB3M,MAAMC,GAAG2M,cACX5M,MAAMC,GAAG2M,YAAYzM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,YAAYzM,UAAUC,QAAQC,UACpDwM,MAAS,QACTC,KAAQ,WACRC,KAAQ,SACRC,OAAU,oBACVC,QAAW,WACXC,WAAc,kBAMhBlN,MAAMC,GAAGkN,QACXnN,MAAMC,GAAGkN,MAAMhN,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkN,MAAMhN,UAAUC,QAAQC,UAC9C+M,SAAY,MACZC,QAAW,gCACXb,MAAS,2BACTc,KAAQ,SACRC,GAAM,UACNC,aAAgB,yBAChBC,MAAS,6BACTC,SAAY,mCACZC,KAAQ,+BACRC,KAAQ,iCACRC,QAAW,WACXC,UAAa,0BAMf9N,MAAMC,GAAG8N,gBACX/N,MAAMC,GAAG8N,cAAc5N,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8N,cAAc5N,UAAUC,QAAQC,UACtD+M,SAAY,MACZE,KAAQ,SACRD,QAAW,gCACXb,MAAS,2BACTe,GAAM,UACNC,aAAgB,yBAChBC,MAAS,6BACTC,SAAY,mCACZC,KAAQ,+BACRC,KAAQ,iCACRC,QAAW,WACXC,UAAa,0BAMf9N,MAAMC,GAAG+N,YACXhO,MAAMC,GAAG+N,UAAU7N,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+N,UAAU7N,UAAUC,QAAQC,UAClD4N,cAAiB,mBACjBC,aAAgB,mBAChBC,UAAa,wBAMfnO,MAAMC,GAAGmO,iBACXpO,MAAMC,GAAGmO,eAAejO,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmO,eAAejO,UAAUC,QAAQC,UACvD6I,KAAQ,iCACRmF,aAAgB,gBAChBtN,OAAU,SACVuN,QAAW,wBACXnF,MAAS,8BACTvB,MAAS,aACT2G,GAAM,UACN/N,OAAU,QACVsH,WACEK,SAAY,WACZC,eAAkB,eAClBF,WAAc,YACdG,SAAY,eACZL,GAAM,eACNC,IAAO,uBAOXjI,MAAMC,GAAGuO,mBACXxO,MAAMC,GAAGuO,iBAAiBrO,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuO,iBAAiBrO,UAAUC,QAAQC,UACzDoO,aACEC,MAAS,OACTC,OAAU,SACVC,MAAS,WACTC,OAAU,cACVC,QAAW,UACXC,OAAU,UAEZJ,QACEK,YAAe,mBACfC,SAAY,YAEdL,OACEI,YAAe,mBACfC,SAAY,aAEdJ,QACEI,SAAY,gBACZD,YAAe,mBACfE,SAAY,mBAEdJ,SACEE,YAAe,mBACfE,SAAY,kBACZD,SAAY,YACZjE,IAAO,UAET+D,QACEC,YAAe,mBACfE,SAAY,iBACZD,SAAY,aACZ1B,GAAM,SAERhD,KACE4E,MAAS,SACTC,YAAe,YACfV,MAAS,OACTW,MAAS,QACTC,WAAc,iBACdC,GAAM,QAERC,iBACE/B,MAAS,QACTgC,OAAU,UACVC,MAAS,QACTC,OAAU,SACV/B,KAAQ,aAEVgC,UACE5E,IAAO,QACP6E,QAAW,aACXC,QAAW,qBAOf9P,MAAMC,GAAG8P,YACX/P,MAAMC,GAAG8P,UAAU5P,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8P,UAAU5P,UAAUC,QAAQC,UAClD2P,OAAU,WACVjH,KAAQ,aACRkH,MAAS,UACTC,KAAQ,MACRC,YAAe,uBACfC,YAAe,uBACfC,MAAS,SACTvF,KAAQ,aACRtK,OAAU,QACV2J,QAAW,WACXmG,kBAAqB,qBACrBC,cAAiB,8BACjBC,eAAkB,0BAClB5E,UACEE,aAAgB,uDAElBf,OACEC,IAAO,QACPE,KAAQ,WACRuF,SAAY,oBACZC,OAAU,UACVzF,MAAS,SAEX0F,oBACEL,kBAAqB,0CACrBM,uBAA0B,+BAC1BC,mBAAsB,sBACtBC,gBAAmB,6CACnBC,qBAAwB,kCACxBC,iBAAoB,yBACpBC,gBAAmB,iEACnBC,cAAiB,uEAEnB9G,QACEjB,MAAS,SACTyB,MAAS,OACTL,IAAO,QACP4G,YAAe,mBACfC,YAAe,YACfC,OAAU,YACVC,SAAY,IACZC,cAAiB,qBACjBC,YAAe,kBACfC,kBAAqB,0CACrBC,oBAAuB,aACvBC,qBAAwB,YACxBC,cAAiB,aACjBC,WAAc,kBACdvH,YAAe,cAOnBtK,MAAM8R,aAAe9R,MAAM8R,YAAYzR,SAAS0R,gBAClD/R,MAAM8R,YAAYzR,SAAS0R,cACzBjS,EAAEQ,QAAO,EAAMN,MAAM8R,YAAYzR,SAAS0R,eACxCC,WAAc,gBACdC,cAAiB,mBACjBC,wBAA2B,6BAC3BC,sBAAyB,0BACzBC,eAAkB,mBAClBC,WAAc,kBACdC,UAAa,cACbC,YAAe,eACfC,aAAgB,cAChBC,UAAa,eACbC,MAAS,qBACTC,YAAe,yBACfpS,MAAS,WACTC,OAAU,WAIZR,MAAM8R,aAAe9R,MAAM8R,YAAYzR,SAASuS,UAClD5S,MAAM8R,YAAYzR,SAASuS,QACzB9S,EAAEQ,QAAO,EAAMN,MAAM8R,YAAYzR,SAASuS,SACxCrS,MAAS,WACTuK,KAAQ,aACRtK,OAAU,QACVqS,OAAU,WACVxG,MAAS,kBACTyG,OAAU,cACVC,OAAU,UACVC,mBACE7J,MAAS,QACT8J,YACEvK,OAAU,QACVwK,SAAY,UACZnK,KAAQ,eAGZoK,kBACEhK,MAAS,iBAEXiK,gBACEjK,MAAS,0BAEXkK,eACElK,MAAS,UAEXmK,iBACEnK,MAAS,WACToK,SACEC,aAAgB,oBAChB5R,cAAiB,SACjBE,aAAgB,iBAChBC,YAAe,UACf0R,SAAY,gBACZC,YAAe,gBACfC,YAAe,kBAGnBC,aACEzK,MAAS,kBACToK,SACEM,WAAc,gBACdC,kBAAqB,qBACrBC,gBAAmB,kBACnBC,QAAW,0BAGfC,cACE9K,MAAS,mBACToK,SACEW,YAAe,mBACfC,WAAc,gBACdC,cAAiB,gBACjBC,SAAY,uBAGhBC,oBACEC,KAAQ,yDACRpL,MAAS,mBAEXqL,kBACErL,MAAS,sBACTsL,YAAe,8CACfC,UAAa,gBACbC,UACEC,IAAO,mBACPlM,OAAU,UACV6L,KAAQ,UACRxL,KAAQ,aACR8L,OAAU,sBACVC,KAAQ,SAEVC,WACEC,YAAe,iBACfC,SAAY,gBACZC,QAAW,SACXC,WAAc,aACdC,QAAW,SACXC,WAAc,aACdC,qBAAwB,sBACxBC,kBAAqB,sBAEvBC,kBACER,YAAe,qBACfC,SAAY,oBACZC,QAAW,qBACXC,WAAc,yBACdC,QAAW,aACXC,WAAc,iBACdC,qBAAwB,0BACxBC,kBAAqB,yBACrBV,OAAU,gCAEZY,QACEd,SAAY,WACZe,SAAY,YACZC,IAAO,WACPC,IAAO,UACPrM,MAAS,OACTqB,MAAS,OACTL,IAAO,QACPsL,cAAiB,wBACjBC,YAAe,qBACfC,YAAe,sBACfC,SAAY,gBACZtB,UAAa,oBACbD,YAAe,mBACfwB,YAAe,gBAEjBC,cACEC,UAAa,eACbC,YAAe,oBAGnBC,gBACElN,MAAS,aACTsM,QACEa,SAAY,gBACZC,WAAc,sBACdC,WAAc,UACdC,UAAa,kBACbC,QAAW,YACXC,YAAe,cACfC,MAAS,WACTC,WAAc,qBACdC,OAAU,SACVC,aAAgB,YAChBC,WAAc,WAGlBC,oBACEC,aAAgB,2DAElBC,mBACEhO,MAAS,2BACT+N,aAAgB,gHAChBzB,QACE2B,QAAW,gBACXC,OAAU,cACVC,SAAY,mBAGhBC,4BACEL,aAAgB,4DAKpBlX,MAAM8R,aAAe9R,MAAM8R,YAAYzR,SAASmX,aAClDxX,MAAM8R,YAAYzR,SAASmX,WACzB1X,EAAEQ,QAAO,EAAMN,MAAM8R,YAAYzR,SAASmX,YACxC3W,cAAiB,0BACjBC,eAAkB,0BAClB2W,cAAiB,iBACjBC,kBAAqB,oBACrBnX,MAAS,WACTiH,OAAU,YACVmQ,aAAgB,+BAChB/P,MAAS,aACTgQ,OAAU,SACVC,aAAgB,SAChBzO,IAAO,MACPC,GAAM,IACNvB,WACEC,QACEI,SAAY,mBACZC,eAAkB,uBAClBF,WAAc,qBACdG,SAAY,wBAEdU,MACEf,GAAM,mBACNC,IAAO,uBACPa,GAAM,4BACNF,GAAM,6BAERF,QACEV,GAAM,eACNC,IAAO,mBACPU,IAAO,6BACPC,GAAM,uBACNC,IAAO,4BACPC,GAAM,2BAMZ9I,MAAM8R,aAAe9R,MAAM8R,YAAYzR,SAASyX,UAClD9X,MAAM8R,YAAYzR,SAASyX,QACzBhY,EAAEQ,QAAO,EAAMN,MAAM8R,YAAYzR,SAASyX,SACxClT,cAAiB,4BACjBC,eAAkB,yBAClBC,YAAe,wBACfC,YAAe,wBACfe,UAAa,eACbiS,kBACEvE,aAAgB,wBAChB5R,cAAiB,SACjBE,aAAgB,qBAChBC,YAAe,UACf0R,SAAY,oBACZC,YAAe,oBACfC,YAAe,qBAEjBqE,gBAAmB,QACnB1W,KAAQ,SACR2W,QAAW,SACXC,aACExF,MAAS,qBACTC,YAAe,2BAEjBwF,KAAQ,YACRC,IAAO,UACPnT,aAAgB,kBAChBD,UAAa,mBACbqT,YAAe,wBACftX,OAAU,SACVuX,WAAc,gBACd1V,SAAY,yBACZ2V,OAAU,2BACVC,aACEC,UAAa,WACb/P,OAAU,UACVgQ,QAAW,UACXC,UAAa,aACbzF,SAAY,SACZnK,KAAQ,aACRmH,KAAQ,MACR0I,SAAY,iBACZC,SAAY,WACZC,YAAe,0BAEjBC,sBAAyB,mBACzBC,sBAAyB,mBACzBC,OAAU,mBACVC,eACEhF,YAAe,mBACfC,WAAc,iBACdC,cAAiB,gBACjBC,SAAY,4BAEd9S,OAAU,SACV4X,MAAS,kBACTC,cACEvF,WAAc,gBACdC,kBAAqB,qBACrBC,gBAAmB,kBACnBC,QAAW,qBAEbqF,KAAQ,YACRC,MAAS,aACTC,aACEC,KAAQ,oBACRC,KAAQ,YAEVC,OAAU,mBACVC,QAAW,qBACXC,SAAY,sBACZC,aACEC,aAAgB,2BAChBC,cAAiB,2BACjBC,aAAgB,2BAChBC,cAAiB,4BAEnBC,UAAa,iBACbC,SAAY,sBACZ3Y,UAAa,cACb4Y,WAAc,4BAIhBpa,MAAM8R,aAAe9R,MAAM8R,YAAYzR,SAASga,OAClDra,MAAM8R,YAAYzR,SAASga,KACzBva,EAAEQ,QAAO,EAAMN,MAAM8R,YAAYzR,SAASga,MACxCC,QACEC,sBAAyB,kJACzBC,4BAA+B,0DAC/BC,gBAAmB,sEAErBC,MACEC,KAAQ,SACRC,OAAU,WACVC,KAAQ,eAOZ7a,MAAMC,GAAG6a,SACX9a,MAAMC,GAAG6a,OAAO3a,UAAUC,QACxBN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6a,OAAO3a,UAAUC,SACvC2a,oBAAuB,SACvBC,oBAAuB,YAMzBhb,MAAMC,GAAGgM,WACXjM,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQC,UACjD6L,OAAU,wBACVC,QAAW,cACXC,cAAiB,kBACjBC,MAAS,YACThB,UACEG,KAAQ,cACRG,OAAU,WACVL,WAAc,QACdC,OAAU,oBACVe,YAAe,4BACfnC,QAAW,WACXsB,MAAS,mBACTzB,IAAO,qBAOXhK,MAAMC,GAAGgb,WACXjb,MAAMC,GAAGgb,SAAS9a,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgb,SAAS9a,UAAUC,QAAQC,UACjD8L,QAAW,cACXC,cAAiB,kBACjBC,MAAS,eAMXrM,MAAMC,GAAGib,SACXlb,MAAMC,GAAGib,OAAO/a,UAAUC,QAAQ+a,aAChCrb,EAAEQ,QAAO,EAAMN,MAAMC,GAAGib,OAAO/a,UAAUC,QAAQ+a,cAC/CzP,OAAU,qBACVlL,OAAU,QACV6L,MAAS,YACTwG,OAAU,WACVuI,mBAAsB,aACtBC,oBAAuB,sBACvB9T,cAAiB,mCACjB+T,gBAAmB,cACnBC,eAAkB,UAClBC,cAAiB,gBACjBC,aAAgB,UAChBC,sBAAyB,iBACzBC,qBAAwB,aACxBC,mBAAsB,4CACtBC,mBAAsB,2CACtBC,qBAAwB,+BAM1B9b,MAAMC,GAAG8b,YACX/b,MAAMC,GAAG8b,UAAU5b,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8b,UAAU5b,UAAUC,QAAQC,UAClD2b,SAAY,wBACZC,QAAW,uBACXtG,IAAO,kDACPC,IAAO,kDACPsG,KAAQ,uBACRC,MAAS,6BACTC,IAAO,2BACPrT,KAAQ,kCACRsT,YAAe,6EAKjBrc,MAAMC,GAAGqc,WACXtc,MAAMC,GAAGqc,SAASjc,SAChBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqc,SAASjc,UAC/B8L,QAAS,gBAMXnM,MAAMC,GAAGsc,SACXvc,MAAMC,GAAGsc,OAAOpc,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsc,OAAOpc,UAAUC,QAAQ+a,cAC/CqB,MAAS,cAMXxc,MAAMC,GAAGwc,QACXzc,MAAMC,GAAGwc,MAAMtc,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwc,MAAMtc,UAAUC,QAAQ+a,cAC9CpI,OAAU,aAMZ/S,MAAMC,GAAGyc,UACX1c,MAAMC,GAAGyc,QAAQvc,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyc,QAAQvc,UAAUC,QAAQ+a,cAChDpI,OAAU,UACVvS,OAAU,WAKZR,MAAMC,GAAG0c,SACX3c,MAAMC,GAAG0c,OAAOxc,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0c,OAAOxc,UAAUC,QAAQ+a,cAC/CpI,OAAU,UACVvS,OAAU,YAIfoc,OAAO5c,MAAM6c", "file": "kendo.messages.el-GR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n  /* FlatColorPicker messages */\n\n  if (kendo.ui.FlatColorPicker) {\n    kendo.ui.FlatColorPicker.prototype.options.messages =\n      $.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages, {\n        \"apply\": \"Εφαρμογή\",\n        \"cancel\": \"Άκυρο\",\n        \"noColor\": \"Χωρίς Χρώμα\",\n        \"clearColor\": \"Καθαρό Χρώμα\"\n      });\n  }\n\n  /* ColorPicker messages */\n\n  if (kendo.ui.ColorPicker) {\n    kendo.ui.ColorPicker.prototype.options.messages =\n      $.extend(true, kendo.ui.ColorPicker.prototype.options.messages, {\n        \"apply\": \"Εφαρμογή\",\n        \"cancel\": \"Άκυρο\",\n        \"noColor\": \"Χω<PERSON><PERSON>ς Χρώμα\",\n        \"clearColor\": \"Καθαρό Χρώμα\"\n      });\n  }\n\n  /* ColumnMenu messages */\n\n  if (kendo.ui.ColumnMenu) {\n    kendo.ui.ColumnMenu.prototype.options.messages =\n      $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages, {\n        \"sortAscending\": \"Αύξουσα Ταξινόμηση\",\n        \"sortDescending\": \"Φθίνουσα Ταξινόμηση\",\n        \"filter\": \"Φίλτρο\",\n        \"columns\": \"Στήλες\",\n        \"done\": \"Εφαρμογή\",\n        \"settings\": \"Στήλη Ρυθμίσεων\",\n        \"lock\": \"Κλείδωμα\",\n        \"unlock\": \"Ξεκλείδωμα\"\n      });\n  }\n\n  /* Editor messages */\n\n  if (kendo.ui.Editor) {\n    kendo.ui.Editor.prototype.options.messages =\n      $.extend(true, kendo.ui.Editor.prototype.options.messages, {\n        \"bold\": \"Έντονα\",\n        \"italic\": \"Πλάγια\",\n        \"underline\": \"Υπογράμμιση\",\n        \"strikethrough\": \"Διακριτή Διαγραφή\",\n        \"superscript\": \"Εκθέτης\",\n        \"subscript\": \"Δείκτης\",\n        \"justifyCenter\": \"Στοίχιση Κέντρο\",\n        \"justifyLeft\": \"Στοίχιση Αριστερά\",\n        \"justifyRight\": \"Στοίχιση Δεξιά\",\n        \"justifyFull\": \"Justify\",\n        \"insertUnorderedList\": \"Τοποθετήστε μη διατεταγμένη λίστα\",\n        \"insertOrderedList\": \"Τοποθετήστε διατεταγμένη λίστα\",\n        \"indent\": \"Εσοχή\",\n        \"outdent\": \"Προεξοχή\",\n        \"createLink\": \"Εισαγωγή Συνδέσμου\",\n        \"unlink\": \"Αφαίρεση Συνδέσμου\",\n        \"insertImage\": \"Εισαγωγή Εικόνας\",\n        \"insertFile\": \"Εισαγωγή Αρχείου\",\n        \"insertHtml\": \"Εισαγωγή HTML\",\n        \"viewHtml\": \"Επισκόπηση HTML\",\n        \"fontName\": \"Επιλογή Οικογένειας Γραμματοσειράς\",\n        \"fontNameInherit\": \"(Κληρονομημένη Γραμματοσειρά)\",\n        \"fontSize\": \"Επιλογή Μεγέθους Γραμματοσειράς\",\n        \"fontSizeInherit\": \"(Κληρονομημένη Μέγεθος)\",\n        \"formatBlock\": \"Μορφοποίηση\",\n        \"formatting\": \"Μορφοποίηση\",\n        \"foreColor\": \"Χρώμα\",\n        \"backColor\": \"Χρώμα Φόντου\",\n        \"style\": \"Στυλ\",\n        \"emptyFolder\": \"Κενός Φάκελος\",\n        \"uploadFile\": \"Μεταφόρτωση\",\n        \"orderBy\": \"Διευθέτηση με:\",\n        \"orderBySize\": \"Μέγεθος\",\n        \"orderByName\": \"Όνομα\",\n        \"invalidFileType\": \"Το επιλεγμένο αρχείο \\\"{0}\\\" δεν είναι έγκυρο. Υποστηριζόμενοι τύποι είναι οι {1}.\",\n        \"deleteFile\": 'Είστε σίγουροι ότι θέλετε να διαγράψετε το \"{0}\"?',\n        \"overwriteFile\": 'Το αρχείο με όνομα \"{0}\" υπάρχει ήδη στον συγκεκριμένο φάκελο. Θέλετε να το αντικαταστήσετε?',\n        \"directoryNotFound\": \"Το όνομα του φακέλου δεν βρέθηκε.\",\n        \"imageWebAddress\": \"Ηλεκτρονική Διεύθυνση\",\n        \"imageAltText\": \"Εναλλακτικό Κείμενο\",\n        \"imageWidth\": \"Πλάτος (px)\",\n        \"imageHeight\": \"Ύψος (px)\",\n        \"fileWebAddress\": \"Ηλεκτρονική Διεύθυνση\",\n        \"fileTitle\": \"Τίτλος\",\n        \"linkWebAddress\": \"Ηλεκτρονική Διεύθυνση\",\n        \"linkText\": \"Κείμενο\",\n        \"linkToolTip\": \"ToolTip\",\n        \"linkOpenInNewWindow\": \"Άνοιγμα συνδέσμου σε νέα καρτέλα.\",\n        \"dialogUpdate\": \"Ανανέωση\",\n        \"dialogInsert\": \"Εισαγωγή\",\n        \"dialogButtonSeparator\": \"ή\",\n        \"dialogCancel\": \"Άκυρο\",\n        \"cleanFormatting\": \"Καθαρίστε τη Μορφοποίηση\",\n        \"createTable\": \"Δημιουργία πίνακα\",\n        \"addColumnLeft\": \"Εισαγωγή στήλης αριστερά\",\n        \"addColumnRight\": \"Εισαγωγή στήλης δεξιά\",\n        \"addRowAbove\": \"Εισαγωγή γραμμής πάνω\",\n        \"addRowBelow\": \"Εισαγωγή γραμμής κάτω\",\n        \"deleteRow\": \"Διαγραφή γραμμής\",\n        \"deleteColumn\": \"Διαγραφή στήλης\",\n        \"dialogOk\": \"Εντάξει\",\n        \"tableWizard\": \"Οδηγός πίνακα\",\n        \"tableTab\": \"Πίνακας\",\n        \"cellTab\": \"Κελί\",\n        \"accessibilityTab\": \"Προσβασιμότητα\",\n        \"caption\": \"Λεζάντα\",\n        \"summary\": \"Περίληψη\",\n        \"width\": \"Πλάτος\",\n        \"height\": \"Ύψος\",\n        \"cellSpacing\": \"Κενό Κελιών\",\n        \"cellPadding\": \"Επένδυση Κελιών\",\n        \"cellMargin\": \"Περιθώριο Κελιών\",\n        \"alignment\": \"Ευθυγράμμιση\",\n        \"background\": \"Φόντο\",\n        \"cssClass\": \"Κλάση CSS\",\n        \"id\": \"ID\",\n        \"border\": \"Περιθώριο\",\n        \"borderStyle\": \"Στυλ Περιθώριου\",\n        \"collapseBorders\": \"Ένωση Περιθώριου\",\n        \"wrapText\": \"Αναδίπλωση Κειμένου\",\n        \"associateCellsWithHeaders\": \"Σύνδεση Κελιών με Επικεφαλίδες\",\n        \"alignLeft\": \"Ευθυγράμμιση Αριστερά\",\n        \"alignCenter\": \"Ευθυγράμμιση Κέντρο\",\n        \"alignRight\": \"Ευθυγράμμιση Δεξιά\",\n        \"alignLeftTop\": \"Ευθυγράμμιση Αριστερά Πάνω\",\n        \"alignCenterTop\": \"Ευθυγράμμιση Κέντρο Πάνω\",\n        \"alignRightTop\": \"Ευθυγράμμιση Δεξιά Πάνω\",\n        \"alignLeftMiddle\": \"Ευθυγράμμιση Αριστερά Μέση\",\n        \"alignCenterMiddle\": \"Ευθυγράμμιση Κέντρο Μέση\",\n        \"alignRightMiddle\": \"Ευθυγράμμιση Δεξιά Μέση\",\n        \"alignLeftBottom\": \"Ευθυγράμμιση Αριστερά Κάτω\",\n        \"alignCenterBottom\": \"Ευθυγράμμιση Κέντρο Κάτω\",\n        \"alignRightBottom\": \"Ευθυγράμμιση Δεξιά Κάτω\",\n        \"alignRemove\": \"Αφαίρεση Ευθυγράμμισης\",\n        \"columns\": \"Στήλες\",\n        \"rows\": \"Γραμμές\",\n        \"selectAllCells\": \"Επιλογή όλων των κελιών\"\n      });\n  }\n\n  /* FileBrowser messages */\n\n  if (kendo.ui.FileBrowser) {\n    kendo.ui.FileBrowser.prototype.options.messages =\n      $.extend(true, kendo.ui.FileBrowser.prototype.options.messages, {\n        \"uploadFile\": \"Μεταφόρτωση\",\n        \"orderBy\": \"Διευθέτηση με\",\n        \"orderByName\": \"Όνομα\",\n        \"orderBySize\": \"Μέγεθος\",\n        \"directoryNotFound\": \"Δεν βρέθηκε Φάκελος με αυτό το όνομα.\",\n        \"emptyFolder\": \"Κενός Φάκελος\",\n        \"deleteFile\": 'Είστε σίγουροι ότι θέλετε να διαγράψετε το \"{0}\"?',\n        \"invalidFileType\": \"Το επιλεγμένο αρχείο \\\"{0}\\\" δνε είναι έγκυρο. Υποστηριζόμενοι τύποι αρχείων είναι {1}.\",\n        \"overwriteFile\": \"Το αρχείο με όνομα \\\"{0}\\\" υπάρχει ήδη σε αυτό το φάκελο. Θέλετε να το αντικαταστήσετε?\",\n        \"dropFilesHere\": \"Σύρτε το αρχείο για μεταφόρτωση\",\n        \"search\": \"Αναζήτηση\"\n      });\n  }\n\n  /* FilterCell messages */\n\n  if (kendo.ui.FilterCell) {\n    kendo.ui.FilterCell.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterCell.prototype.options.messages, {\n        \"isTrue\": \"είναι αληθές\",\n        \"isFalse\": \"είναι ψευδές\",\n        \"filter\": \"Φίλτρο\",\n        \"clear\": \"Εκκαθάριση\",\n        \"operator\": \"Χειριστής\"\n      });\n  }\n\n  /* FilterCell operators */\n\n  if (kendo.ui.FilterCell) {\n    kendo.ui.FilterCell.prototype.options.operators =\n      $.extend(true, kendo.ui.FilterCell.prototype.options.operators, {\n        \"string\": {\n          \"eq\": \"Είναι ίσο με\",\n          \"neq\": \"Δεν είναι ίσο με\",\n          \"startswith\": \"Ξεκινά με\",\n          \"contains\": \"Περιέχει\",\n          \"doesnotcontain\": \"Δεν περιέχει\",\n          \"endswith\": \"Τελειώνει με\",\n          \"isnull\": \"Είναι null\",\n          \"isnotnull\": \"Δεν είναι null\",\n          \"isempty\": \"Είναι κενό\",\n          \"isnotempty\": \"Δεν είναι κενό\"\n        },\n        \"number\": {\n          \"eq\": \"Είναι ίσο με\",\n          \"neq\": \"Δεν είναι ίσο με\",\n          \"gte\": \"Είναι μεγαλύτερο ή ίσο με\",\n          \"gt\": \"Είναι μεγαλύτερο από\",\n          \"lte\": \"Είναι μικρότερο ή ίσο με\",\n          \"lt\": \"Είναι μικρότερο από\",\n          \"isnull\": \"Είναι null\",\n          \"isnotnull\": \"Δεν είναι null\"\n        },\n        \"date\": {\n          \"eq\": \"Είναι ίσο με\",\n          \"neq\": \"Δεν είναι ίσο με\",\n          \"gte\": \"Είναι μετά ή ίσο με\",\n          \"gt\": \"Είναι μετά\",\n          \"lte\": \"Είναι πριν ή ίσο με\",\n          \"lt\": \"Είναι πριν\",\n          \"isnull\": \"Είναι null\",\n          \"isnotnull\": \"Δεν είναι null\"\n        },\n        \"enums\": {\n          \"eq\": \"Είναι ίσο με\",\n          \"neq\": \"Δεν είναι ίσο με\",\n          \"isnull\": \"Είναι null\",\n          \"isnotnull\": \"Δεν είναι null\"\n        }\n      });\n  }\n\n  /* FilterMenu messages */\n\n  if (kendo.ui.FilterMenu) {\n    kendo.ui.FilterMenu.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterMenu.prototype.options.messages, {\n        \"info\": \"Δείξε αντικείμενα με τιμή που:\",\n        \"title\": \"Δείξε αντικείμενα με τιμή που\",\n        \"isTrue\": \"είναι αληθές\",\n        \"isFalse\": \"είναι ψευδές\",\n        \"filter\": \"Φίλτρο\",\n        \"clear\": \"Εκκαθάριση\",\n        \"and\": \"Και\",\n        \"or\": \"Ή\",\n        \"selectValue\": \"-Επιλογή Τιμής-\",\n        \"operator\": \"Σύμβολο\",\n        \"value\": \"Τιμή\",\n        \"cancel\": \"Άκυρο\"\n      });\n  }\n\n  /* FilterMenu operator messages */\n\n  if (kendo.ui.FilterMenu) {\n    kendo.ui.FilterMenu.prototype.options.operators =\n      $.extend(true, kendo.ui.FilterMenu.prototype.options.operators, {\n        \"string\": {\n          \"eq\": \"Είναι ίσο με\",\n          \"neq\": \"Δεν είναι ίσο με\",\n          \"startswith\": \"Ξεκινά με\",\n          \"contains\": \"Περιέχει\",\n          \"doesnotcontain\": \"Δεν περιέχει\",\n          \"endswith\": \"Τελειώνει με\",\n          \"isnull\": \"Είναι null\",\n          \"isnotnull\": \"Δεν είναι null\",\n          \"isempty\": \"Είναι κενό\",\n          \"isnotempty\": \"Δεν είναι κενό\"\n        },\n        \"number\": {\n          \"eq\": \"Είναι ίσο με\",\n          \"neq\": \"Δεν είναι ίσο με\",\n          \"gte\": \"Είναι μεγαλύτερο ή ίσο με\",\n          \"gt\": \"Είναι μεγαλύτερο από\",\n          \"lte\": \"Είναι μικρότερο ή ίσο με\",\n          \"lt\": \"Είναι μικρότερο από\",\n          \"isnull\": \"Είναι null\",\n          \"isnotnull\": \"Δεν είναι null\"\n        },\n        \"date\": {\n          \"eq\": \"Είναι ίσο με\",\n          \"neq\": \"Δεν είναι ίσο με\",\n          \"gte\": \"Είναι μετά ή ίσο με\",\n          \"gt\": \"Είναι μετά\",\n          \"lte\": \"Είναι πριν ή ίσο με\",\n          \"lt\": \"Είναι πριν\",\n          \"isnull\": \"Είναι null\",\n          \"isnotnull\": \"Δεν έιναι null\"\n        },\n        \"enums\": {\n          \"eq\": \"Είναι ίσο με\",\n          \"neq\": \"Δεν είναι ίσο με\",\n          \"isnull\": \"Είναι null\",\n          \"isnotnull\": \"Δεν είναι null\"\n        }\n      });\n  }\n\n  /* FilterMultiCheck messages */\n\n  if (kendo.ui.FilterMultiCheck) {\n    kendo.ui.FilterMultiCheck.prototype.options.messages =\n      $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages, {\n        \"checkAll\": \"Επιλογή όλων\",\n        \"clear\": \"Εκκαθάριση\",\n        \"filter\": \"Φίλτρο\",\n        \"search\": \"Αναζήτηση\"\n      });\n  }\n\n  /* Gantt messages */\n\n  if (kendo.ui.Gantt) {\n    kendo.ui.Gantt.prototype.options.messages =\n      $.extend(true, kendo.ui.Gantt.prototype.options.messages, {\n        \"actions\": {\n          \"addChild\": \"Προσθήκη Παιδιού\",\n          \"append\": \"Προσθήκη Εργασίας\",\n          \"insertAfter\": \"Προσθήκη κάτω\",\n          \"insertBefore\": \"Προσθήκη πάνω\",\n          \"pdf\": \"Εξαγωγή σε PDF\"\n        },\n        \"cancel\": \"Άκυρο\",\n        \"deleteDependencyWindowTitle\": \"Διαγραφή Εξάρτησης\",\n        \"deleteTaskWindowTitle\": \"Διαγραφή Εργασίας\",\n        \"destroy\": \"Διαγραφή\",\n        \"editor\": {\n          \"assingButton\": \"Ανάθεση\",\n          \"editorTitle\": \"Εργασία\",\n          \"end\": \"Τέλος\",\n          \"percentComplete\": \"Ολοκλήρωση\",\n          \"resources\": \"Πόροι\",\n          \"resourcesEditorTitle\": \"Πόροι\",\n          \"resourcesHeader\": \"Πόροι\",\n          \"start\": \"Αρχή\",\n          \"title\": \"Τίτλος\",\n          \"unitsHeader\": \"Μονάδες\"\n        },\n        \"save\": \"Αποθήκευση\",\n        \"views\": {\n          \"day\": \"Ημέρα\",\n          \"end\": \"Τέλος\",\n          \"month\": \"Μήνας\",\n          \"start\": \"Αρχή\",\n          \"week\": \"Εβδομάδα\",\n          \"year\": \"Χρόνος\"\n        }\n      });\n  }\n\n  /* Grid messages */\n\n  if (kendo.ui.Grid) {\n    kendo.ui.Grid.prototype.options.messages =\n      $.extend(true, kendo.ui.Grid.prototype.options.messages, {\n        \"commands\": {\n          \"cancel\": \"Ακύρωση αλλαγών\",\n          \"canceledit\": \"Ακύρωση\",\n          \"create\": \"Προσθήκη Εγγραφής\",\n          \"destroy\": \"Διαγραφή\",\n          \"edit\": \"Επεξεργασία\",\n          \"excel\": \"Εξαγωγή σε Excel\",\n          \"pdf\": \"Εξαγωγή σε PDF\",\n          \"save\": \"Αποθήκευση Αλλαγών\",\n          \"select\": \"Επιλογή\",\n          \"update\": \"Ανανέωση\"\n        },\n        \"editable\": {\n          \"cancelDelete\": \"Άκυρο\",\n          \"confirmation\": \"Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την εγγραφή;\",\n          \"confirmDelete\": \"Διαγραφή\"\n        },\n        \"noRecords\": \"Δεν υπάρχουν εγγραφές.\"\n      });\n  }\n\n  /* TreeList messages */\n\n  if (kendo.ui.TreeList) {\n    kendo.ui.TreeList.prototype.options.messages =\n      $.extend(true, kendo.ui.TreeList.prototype.options.messages, {\n        \"noRows\": \"Δεν υπάρχουν εγγραφές\",\n        \"loading\": \"Φόρτωση...\",\n        \"requestFailed\": \"Αποτυχία Αίτησης.\",\n        \"retry\": \"Ξαναδοκιμάστε\",\n        \"commands\": {\n          \"edit\": \"Επεξεργασία\",\n          \"update\": \"Ανανέωση\",\n          \"canceledit\": \"Άκυρο\",\n          \"create\": \"Προσθήκη Εγγραφής\",\n          \"createchild\": \"Προσθήκη Εγγραφής Παιδιού\",\n          \"destroy\": \"Διαγραφή\",\n          \"excel\": \"Εξαγωγή σε Excel\",\n          \"pdf\": \"Εξαγωγή σε PDF\"\n        }\n      });\n  }\n\n  /* Groupable messages */\n\n  if (kendo.ui.Groupable) {\n    kendo.ui.Groupable.prototype.options.messages =\n      $.extend(true, kendo.ui.Groupable.prototype.options.messages, {\n        \"empty\": \"Τραβήξτε μια στήλη εδώ για να φιλτραριστεί με αυτή τη στήλη\"\n      });\n  }\n\n  /* NumericTextBox messages */\n\n  if (kendo.ui.NumericTextBox) {\n    kendo.ui.NumericTextBox.prototype.options =\n      $.extend(true, kendo.ui.NumericTextBox.prototype.options, {\n        \"upArrowText\": \"Αύξηση Τιμής\",\n        \"downArrowText\": \"Μείωση Τιμής\"\n      });\n  }\n\n  /* MediaPlayer messages */\n\n  if (kendo.ui.MediaPlayer) {\n    kendo.ui.MediaPlayer.prototype.options.messages =\n      $.extend(true, kendo.ui.MediaPlayer.prototype.options.messages, {\n        \"pause\": \"Παύση\",\n        \"play\": \"Εκκίνηση\",\n        \"mute\": \"Σίγαση\",\n        \"unmute\": \"Κατάργηση Σίγασης\",\n        \"quality\": \"Ποιότητα\",\n        \"fullscreen\": \"Πλήρης Οθόνη\"\n      });\n  }\n\n  /* Pager messages */\n\n  if (kendo.ui.Pager) {\n    kendo.ui.Pager.prototype.options.messages =\n      $.extend(true, kendo.ui.Pager.prototype.options.messages, {\n        \"allPages\": \"Όλα\",\n        \"display\": \"{0} - {1} από {2} αντικείμενα\",\n        \"empty\": \"Δεν υπάρχουν αντικείμενα\",\n        \"page\": \"Σελίδα\",\n        \"of\": \"από {0}\",\n        \"itemsPerPage\": \"αντικείμενα ανα σελίδα\",\n        \"first\": \"Πηγαίντε στην πρώτη σελίδα\",\n        \"previous\": \"Πηγαίντε στην προηγούμενη σελίδα\",\n        \"next\": \"Πηγαίντε στην επόμενη σελίδα\",\n        \"last\": \"Πηγαίντε στην τελευταία σελίδα\",\n        \"refresh\": \"Ανανέωση\",\n        \"morePages\": \"Περισσότερες Σελίδες\"\n      });\n  }\n\n  /* TreeListPager messages */\n\n  if (kendo.ui.TreeListPager) {\n    kendo.ui.TreeListPager.prototype.options.messages =\n      $.extend(true, kendo.ui.TreeListPager.prototype.options.messages, {\n        \"allPages\": \"Όλα\",\n        \"page\": \"Σελίδα\",\n        \"display\": \"{0} - {1} από {2} αντικείμενα\",\n        \"empty\": \"Δεν υπάρχουν αντικείμενα\",\n        \"of\": \"από {0}\",\n        \"itemsPerPage\": \"αντικείμενα ανα σελίδα\",\n        \"first\": \"Πηγαίντε στην πρώτη σελίδα\",\n        \"previous\": \"Πηγαίντε στην προηγούμενη σελίδα\",\n        \"next\": \"Πηγαίντε στην επόμενη σελίδα\",\n        \"last\": \"Πηγαίντε στην τελευταία σελίδα\",\n        \"refresh\": \"Ανανέωση\",\n        \"morePages\": \"Περισσότερες Σελίδες\"\n      });\n  }\n\n  /* PivotGrid messages */\n\n  if (kendo.ui.PivotGrid) {\n    kendo.ui.PivotGrid.prototype.options.messages =\n      $.extend(true, kendo.ui.PivotGrid.prototype.options.messages, {\n        \"measureFields\": \"Σύρετε πεδία εδώ\",\n        \"columnFields\": \"Σύρετε στήλη εδώ\",\n        \"rowFields\": \"Σύρετε γραμμές εδώ\"\n      });\n  }\n\n  /* PivotFieldMenu messages */\n\n  if (kendo.ui.PivotFieldMenu) {\n    kendo.ui.PivotFieldMenu.prototype.options.messages =\n      $.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages, {\n        \"info\": \"Δείξε αντικείμενα με τιμή που:\",\n        \"filterFields\": \"Φίλτρο Πεδίων\",\n        \"filter\": \"Φίλτρο\",\n        \"include\": \"Συμπεριέλαβε πεδία...\",\n        \"title\": \"Πεδία που θα συμπεριληφθούν\",\n        \"clear\": \"Εκκαθάριση\",\n        \"ok\": \"Εντάξει\",\n        \"cancel\": \"Άκυρο\",\n        \"operators\": {\n          \"contains\": \"Περιέχει\",\n          \"doesnotcontain\": \"Δεν περιέχει\",\n          \"startswith\": \"Ξεκινά με\",\n          \"endswith\": \"Τελειώνει με\",\n          \"eq\": \"Είναι ίσο με\",\n          \"neq\": \"Δεν είναι ίσο με\"\n        }\n      });\n  }\n\n  /* RecurrenceEditor messages */\n\n  if (kendo.ui.RecurrenceEditor) {\n    kendo.ui.RecurrenceEditor.prototype.options.messages =\n      $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages, {\n        \"frequencies\": {\n          \"never\": \"Ποτέ\",\n          \"hourly\": \"Ωριαία\",\n          \"daily\": \"Ημερήσια\",\n          \"weekly\": \"Εβδομαδιαία\",\n          \"monthly\": \"Μηνιαία\",\n          \"yearly\": \"Ετήσια\"\n        },\n        \"hourly\": {\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\n          \"interval\": \" ώρα(ες)\"\n        },\n        \"daily\": {\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\n          \"interval\": \" μέρα(ες)\"\n        },\n        \"weekly\": {\n          \"interval\": \" εβδομάδα(ες)\",\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\n          \"repeatOn\": \"Επανάληψη την: \"\n        },\n        \"monthly\": {\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\n          \"repeatOn\": \"Επανάληψη τον: \",\n          \"interval\": \" μήνα(ες)\",\n          \"day\": \"Ημέρα \"\n        },\n        \"yearly\": {\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\n          \"repeatOn\": \"Επανάληψη το: \",\n          \"interval\": \" χρόνο(ια)\",\n          \"of\": \" από \"\n        },\n        \"end\": {\n          \"label\": \"Τέλος:\",\n          \"mobileLabel\": \"Τελειώνει\",\n          \"never\": \"Ποτέ\",\n          \"after\": \"Μετά \",\n          \"occurrence\": \" εμφάνιση(εις)\",\n          \"on\": \"Τον \"\n        },\n        \"offsetPositions\": {\n          \"first\": \"πρώτο\",\n          \"second\": \"δεύτερο\",\n          \"third\": \"τρίτο\",\n          \"fourth\": \"τέταρο\",\n          \"last\": \"τελευταίο\"\n        },\n        \"weekdays\": {\n          \"day\": \"ημέρα\",\n          \"weekday\": \"καθημερινή\",\n          \"weekend\": \"σαββατοκύριακο\"\n        }\n      });\n  }\n\n  /* Scheduler messages */\n\n  if (kendo.ui.Scheduler) {\n    kendo.ui.Scheduler.prototype.options.messages =\n      $.extend(true, kendo.ui.Scheduler.prototype.options.messages, {\n        \"allDay\": \"όλη μέρα\",\n        \"date\": \"Ημερομηνία\",\n        \"event\": \"Γεγονός\",\n        \"time\": \"Ώρα\",\n        \"showFullDay\": \"Δείξε ολόκληρη ημέρα\",\n        \"showWorkDay\": \"Δείξε εργάσιμες ώρες\",\n        \"today\": \"Σήμερα\",\n        \"save\": \"Αποθήκευση\",\n        \"cancel\": \"Άκυρο\",\n        \"destroy\": \"Διαγραφή\",\n        \"deleteWindowTitle\": \"Διαγραφή γεγονότος\",\n        \"ariaSlotLabel\": \"Επιλογή από {0:t} έως {1:t}\",\n        \"ariaEventLabel\": \"{0} από {1:D} την {2:t}\",\n        \"editable\": {\n          \"confirmation\": \"Είστε σίγουροι ότι θέλετε να διαγράψετε το γεγονός;\"\n        },\n        \"views\": {\n          \"day\": \"Ημέρα\",\n          \"week\": \"Εβδομάδα\",\n          \"workWeek\": \"Εργάσιμη Εβδομάδα\",\n          \"agenda\": \"Ατζέντα\",\n          \"month\": \"Μήνας\"\n        },\n        \"recurrenceMessages\": {\n          \"deleteWindowTitle\": \"Διαγραφή επαναλαμβανόμενων αντικειμένων\",\n          \"deleteWindowOccurrence\": \"Διαγραφή τρέχουσας εμφάνισης\",\n          \"deleteWindowSeries\": \"Διαγραφή της σειράς\",\n          \"editWindowTitle\": \"Επεξεργασία επαναλαμβανόμενου αντικειμένου\",\n          \"editWindowOccurrence\": \"Επεξεργασία τρέχουσας εμφάνισης\",\n          \"editWindowSeries\": \"Επεξεργασία της σειράς\",\n          \"deleteRecurring\": \"Θέλετε να διαγράψετε μόνο αυτό το γεγονός ή ολόκληρη τη σειρά;\",\n          \"editRecurring\": \"Θέλετε να επεξεργαστείτε μονο αυτό το γεγονός ή ολόκληρη της σειρά;\"\n        },\n        \"editor\": {\n          \"title\": \"Τίτλος\",\n          \"start\": \"Αρχή\",\n          \"end\": \"Τέλος\",\n          \"allDayEvent\": \"Ολοήμερο γεγονός\",\n          \"description\": \"Περιγραφή\",\n          \"repeat\": \"Επανάληψη\",\n          \"timezone\": \" \",\n          \"startTimezone\": \"Εκκίνηση ζώνη ώρας\",\n          \"endTimezone\": \"Τέλος ζώνη ώρας\",\n          \"separateTimezones\": \"Χρήση διαφορετικών αρχή-τέλος ζώνη ώρας\",\n          \"timezoneEditorTitle\": \"Ζώνες Ώρας\",\n          \"timezoneEditorButton\": \"Ζώνη Ώρας\",\n          \"timezoneTitle\": \"Ζώνες Ώρας\",\n          \"noTimezone\": \"Καμία ζώνη ώρας\",\n          \"editorTitle\": \"Γεγονός\"\n        }\n      });\n  }\n\n  /* Spreadsheet messages */\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\n    kendo.spreadsheet.messages.borderPalette =\n      $.extend(true, kendo.spreadsheet.messages.borderPalette, {\n        \"allBorders\": \"Όλα τα σύνορα\",\n        \"insideBorders\": \"Εσωτερικά σύνορα\",\n        \"insideHorizontalBorders\": \"Εσωτερικά οριζότνια σύνορα\",\n        \"insideVerticalBorders\": \"Εσωτερικά κάθετα σύνορα\",\n        \"outsideBorders\": \"Εξωτερικά σύνορα\",\n        \"leftBorder\": \"Αριστερό Σύνορο\",\n        \"topBorder\": \"Πάνω Σύνορο\",\n        \"rightBorder\": \"Δεξιά Σύνορο\",\n        \"bottomBorder\": \"Κάτω Σύνορο\",\n        \"noBorders\": \"Χωρίς Σύνορα\",\n        \"reset\": \"Επαναφορά Χρώματος\",\n        \"customColor\": \"Προσαρμοσμένο Χρώμα...\",\n        \"apply\": \"Εφαρμογή\",\n        \"cancel\": \"Άκυρο\"\n      });\n  }\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\n    kendo.spreadsheet.messages.dialogs =\n      $.extend(true, kendo.spreadsheet.messages.dialogs, {\n        \"apply\": \"Εφαρμογή\",\n        \"save\": \"Αποθήκευση\",\n        \"cancel\": \"Άκυρο\",\n        \"remove\": \"Αφαίρεση\",\n        \"retry\": \"Ξαναπροσπαθήστε\",\n        \"revert\": \"Επαναστροφή\",\n        \"okText\": \"Εντάξει\",\n        \"formatCellsDialog\": {\n          \"title\": \"Μορφή\",\n          \"categories\": {\n            \"number\": \"Χρώμα\",\n            \"currency\": \"Νόμισμα\",\n            \"date\": \"Ημερομηνία\"\n          }\n        },\n        \"fontFamilyDialog\": {\n          \"title\": \"Γραμματοσειρά\"\n        },\n        \"fontSizeDialog\": {\n          \"title\": \"Μέγεθος Γραμματοσειράς\"\n        },\n        \"bordersDialog\": {\n          \"title\": \"Σύνορα\"\n        },\n        \"alignmentDialog\": {\n          \"title\": \"Στοίχιση\",\n          \"buttons\": {\n            \"justtifyLeft\": \"Στοίχιση Αριστερά\",\n            \"justifyCenter\": \"Κέντρο\",\n            \"justifyRight\": \"Στοίχιση Δεξιά\",\n            \"justifyFull\": \"Justify\",\n            \"alignTop\": \"Στοίχιση Πάνω\",\n            \"alignMiddle\": \"Στοίχιση Μέση\",\n            \"alignBottom\": \"Στοίχιση Κάτω\"\n          }\n        },\n        \"mergeDialog\": {\n          \"title\": \"Συνένωση Κελιών\",\n          \"buttons\": {\n            \"mergeCells\": \"Συνένωση όλων\",\n            \"mergeHorizontally\": \"Συνένωση οριζόντια\",\n            \"mergeVertically\": \"Συνένωση κάθετα\",\n            \"unmerge\": \"Κατάργηση Συγχώνευσης\"\n          }\n        },\n        \"freezeDialog\": {\n          \"title\": \"Πάγωμα Παραθύρων\",\n          \"buttons\": {\n            \"freezePanes\": \"Πάγωμα Παραθύρων\",\n            \"freezeRows\": \"Πάγωμα Σειρών\",\n            \"freezeColumns\": \"Πάγωμα Στηλών\",\n            \"unfreeze\": \"Ξεπάγωμα Παραθύρων\"\n          }\n        },\n        \"confirmationDialog\": {\n          \"text\": \"Είστε σίγουροι ότι θέλετε να διαγράψετε αυτό το φύλλο;\",\n          \"title\": \"Αφαίρεση Φύλλου\"\n        },\n        \"validationDialog\": {\n          \"title\": \"Επικύρωση Δεδομένων\",\n          \"hintMessage\": \"Παρακαλώ εισάγεται μια έγκυρη {0} τιμή {1}.\",\n          \"hintTitle\": \"Επικύρωση {0}\",\n          \"criteria\": {\n            \"any\": \"Οποιαδήποτε Τιμή\",\n            \"number\": \"Αριθμός\",\n            \"text\": \"Κείμενο\",\n            \"date\": \"Ημερομηνία\",\n            \"custom\": \"Διαμορφωμένος τύπος\",\n            \"list\": \"Λίστα\"\n          },\n          \"comparers\": {\n            \"greaterThan\": \"μεγαλύτερο από\",\n            \"lessThan\": \"μικρότερο από\",\n            \"between\": \"μεταξύ\",\n            \"notBetween\": \"όχι μεταξύ\",\n            \"equalTo\": \"ίσο με\",\n            \"notEqualTo\": \"όχι ίσο με\",\n            \"greaterThanOrEqualTo\": \"μεγαλύτερο ή ίσο με\",\n            \"lessThanOrEqualTo\": \"μικρότερο ή ίσο με\"\n          },\n          \"comparerMessages\": {\n            \"greaterThan\": \"μεγαλύτερο από {0}\",\n            \"lessThan\": \"μικρότερο από {0}\",\n            \"between\": \"μεταξύ {0} και {1}\",\n            \"notBetween\": \"όχι μεταξύ {0} και {1}\",\n            \"equalTo\": \"ίσο με {0}\",\n            \"notEqualTo\": \"όχι ίσο με {0}\",\n            \"greaterThanOrEqualTo\": \"μεγαλύτερο ή ίσο με {0}\",\n            \"lessThanOrEqualTo\": \"μικρότερο ή ίσο με {0}\",\n            \"custom\": \"που ικανοποιεί τον τύπο: {0}\"\n          },\n          \"labels\": {\n            \"criteria\": \"Κριτήρια\",\n            \"comparer\": \"Συγκριτής\",\n            \"min\": \"Ελάχιστο\",\n            \"max\": \"Μέγιστο\",\n            \"value\": \"Τιμή\",\n            \"start\": \"Αρχή\",\n            \"end\": \"Τέλος\",\n            \"onInvalidData\": \"Σε μη-έγκυρα δεδομένα\",\n            \"rejectInput\": \"Απόρριψη Εισαγωγής\",\n            \"showWarning\": \"Δείξε προειδοποίηση\",\n            \"showHint\": \"Δείξε βοήθεια\",\n            \"hintTitle\": \"Βοηθητικός΄τίτλος\",\n            \"hintMessage\": \"Βοηθητικό μήνυμα\",\n            \"ignoreBlank\": \"Αγνόησε κενά\"\n          },\n          \"placeholders\": {\n            \"typeTitle\": \"Τύπος τίτλου\",\n            \"typeMessage\": \"Τυπος μηνύματος\"\n          }\n        },\n        \"exportAsDialog\": {\n          \"title\": \"Εξαγωγή...\",\n          \"labels\": {\n            \"fileName\": \"Όνομα Αρχείου\",\n            \"saveAsType\": \"Αποθήκευση ως τύπος\",\n            \"exportArea\": \"Εξαγωγή\",\n            \"paperSize\": \"Μέγεθος Χαρτιού\",\n            \"margins\": \"Περιθώρια\",\n            \"orientation\": \"Orientation\",\n            \"print\": \"Εκτύπωση\",\n            \"guidelines\": \"Βοηθητικές Γραμμες\",\n            \"center\": \"Κέντρο\",\n            \"horizontally\": \"οριζόντια\",\n            \"vertically\": \"Κάθετα\"\n          }\n        },\n        \"modifyMergedDialog\": {\n          \"errorMessage\": \"Δεν μπορεί να γίνει αλλαγή μέρους συγχωνευμένου κελιού.\"\n        },\n        \"useKeyboardDialog\": {\n          \"title\": \"Αντιγραφή και Επικόλληση\",\n          \"errorMessage\": \"Αυτές οι πράξεις δεν μπορούν να γίνουν από αυτό το μενού. Παρακαλώ χρησιμοποιήστε συντομεύσεις πληκτρολογίου:\",\n          \"labels\": {\n            \"forCopy\": \"για Αντιγραφή\",\n            \"forCut\": \"για Αποκοπή\",\n            \"forPaste\": \"για Επικόλληση\"\n          }\n        },\n        \"unsupportedSelectionDialog\": {\n          \"errorMessage\": \"Αυτή η πράξη δεν μπορεί να γίνει σε πολλαπλή επιλογή.\"\n        }\n      });\n  }\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\n    kendo.spreadsheet.messages.filterMenu =\n      $.extend(true, kendo.spreadsheet.messages.filterMenu, {\n        \"sortAscending\": \"Ταξινόμιση εύρος A σε Ω\",\n        \"sortDescending\": \"Ταξινόμιση εύρος Ω σε A\",\n        \"filterByValue\": \"Φίλτρο με τιμή\",\n        \"filterByCondition\": \"Φίλτρο με συνθήκη\",\n        \"apply\": \"Εφαρμογή\",\n        \"search\": \"Αναζήτηση\",\n        \"addToCurrent\": \"Προσθήκη στην τωρινή επιλογή\",\n        \"clear\": \"Εκκαθάριση\",\n        \"blanks\": \"(Κενά)\",\n        \"operatorNone\": \"Κανένα\",\n        \"and\": \"ΚΑΙ\",\n        \"or\": \"Ή\",\n        \"operators\": {\n          \"string\": {\n            \"contains\": \"Κείμενο περιέχει\",\n            \"doesnotcontain\": \"Κείμενο δεν περιέχει\",\n            \"startswith\": \"Κείμενο αρχίζει με\",\n            \"endswith\": \"Κείμενο τελειώνει με\"\n          },\n          \"date\": {\n            \"eq\": \"Ημερομηνία είναι\",\n            \"neq\": \"Ημερομηνία δεν είναι\",\n            \"lt\": \"Ημερομηνία είναι πριν από\",\n            \"gt\": \"Ημερομηνία είναι μετά από\"\n          },\n          \"number\": {\n            \"eq\": \"Είναι ίσο με\",\n            \"neq\": \"Δεν είναι ίσο με\",\n            \"gte\": \"Είναι μεγαλύτερο ή ίσό από\",\n            \"gt\": \"Είναι μεγαλύτερο από\",\n            \"lte\": \"Είναι μικρότερο ή ίσο από\",\n            \"lt\": \"Είναι μικρότερο από\"\n          }\n        }\n      });\n  }\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\n    kendo.spreadsheet.messages.toolbar =\n      $.extend(true, kendo.spreadsheet.messages.toolbar, {\n        \"addColumnLeft\": \"Προσθήκη γραμμής αριστερά\",\n        \"addColumnRight\": \"Προσθήκη γραμμής δεξιά\",\n        \"addRowAbove\": \"Προσθήκη γραμμής πάνω\",\n        \"addRowBelow\": \"Προσθήκη γραμμής κάτω\",\n        \"alignment\": \"Ευθυγράμμιση\",\n        \"alignmentButtons\": {\n          \"justtifyLeft\": \"Ευθυγράμμιση Αριστερά\",\n          \"justifyCenter\": \"Κέντρο\",\n          \"justifyRight\": \"Ευθυγράμμιση Δεξιά\",\n          \"justifyFull\": \"Justify\",\n          \"alignTop\": \"Ευθυγράμμιση Πάνω\",\n          \"alignMiddle\": \"Ευθυγράμμιση Μέση\",\n          \"alignBottom\": \"Ευθυγράμμιση Κάτω\"\n        },\n        \"backgroundColor\": \"Φόντο\",\n        \"bold\": \"Έντονα\",\n        \"borders\": \"Σύνορα\",\n        \"colorPicker\": {\n          \"reset\": \"Επαναφορά Χρώματος\",\n          \"customColor\": \"Προσχεδιασμένο Χρώμα...\"\n        },\n        \"copy\": \"Αντιγραφή\",\n        \"cut\": \"Αποκοπή\",\n        \"deleteColumn\": \"Διαγραφή στήλης\",\n        \"deleteRow\": \"Διαγραφή γραμμής\",\n        \"excelImport\": \"Εισαγωγή από Excel...\",\n        \"filter\": \"φίλτρο\",\n        \"fontFamily\": \"Γραμματοσειρά\",\n        \"fontSize\": \"Μέγεθος Γραμματοσειράς\",\n        \"format\": \"Προσχεδιασμένος Τύπος...\",\n        \"formatTypes\": {\n          \"automatic\": \"Αυτόματο\",\n          \"number\": \"Αριθμός\",\n          \"percent\": \"Ποσοστό\",\n          \"financial\": \"Οικονομικά\",\n          \"currency\": \"Νόμισα\",\n          \"date\": \"Ημερομηνία\",\n          \"time\": \"Ώρα\",\n          \"dateTime\": \"Ημερομηνία-Ώρα\",\n          \"duration\": \"Διάρκεια\",\n          \"moreFormats\": \"Περισσότερα formats...\"\n        },\n        \"formatDecreaseDecimal\": \"Μείωση δεκαδικών\",\n        \"formatIncreaseDecimal\": \"Αύξηση δεκαδικών\",\n        \"freeze\": \"Πάγωμα παραθύρων\",\n        \"freezeButtons\": {\n          \"freezePanes\": \"Πάγωμα παραθύρων\",\n          \"freezeRows\": \"Πάγωμα γραμμών\",\n          \"freezeColumns\": \"Πάγωμα στηλών\",\n          \"unfreeze\": \"Ξεπάγωμα παραθύρων panes\"\n        },\n        \"italic\": \"Πλάγια\",\n        \"merge\": \"Συνένωση κελιών\",\n        \"mergeButtons\": {\n          \"mergeCells\": \"Συνένωση όλων\",\n          \"mergeHorizontally\": \"Σενένωση οριζόντια\",\n          \"mergeVertically\": \"Συνένωση κάθετα\",\n          \"unmerge\": \"Ακύρωση Συνένωσης\"\n        },\n        \"open\": \"Άνογμα...\",\n        \"paste\": \"Επικόλληση\",\n        \"quickAccess\": {\n          \"redo\": \"Ακύρωση Αναίρεσης\",\n          \"undo\": \"Αναίρεση\"\n        },\n        \"saveAs\": \"Αποθήκευση ως...\",\n        \"sortAsc\": \"Αύξουσα Ταξινόμιση\",\n        \"sortDesc\": \"Φθήνουσα Ταξινόμιση\",\n        \"sortButtons\": {\n          \"sortSheetAsc\": \"Ταξινόμιση φύλλου A σε Ω\",\n          \"sortSheetDesc\": \"Ταξινόμιση φύλλου Ω σε A\",\n          \"sortRangeAsc\": \"Ταξινόμιση εύρους A σε Ω\",\n          \"sortRangeDesc\": \"Ταξινόμιση εύρους Ω σε A\"\n        },\n        \"textColor\": \"Χρώμα Κειμένου\",\n        \"textWrap\": \"Αναδίπλωση κειμένου\",\n        \"underline\": \"Υπογράμμιση\",\n        \"validation\": \"Επικύρωση Δεδομένων...\"\n      });\n  }\n\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\n    kendo.spreadsheet.messages.view =\n      $.extend(true, kendo.spreadsheet.messages.view, {\n        \"errors\": {\n          \"shiftingNonblankCells\": \"Δεν μπορεί να γίνει εισαγωγή κελιών λόγω πιθανότητας απώλειας δεδομένων. Επιλέξτε άλλη τοποθεσία ή διαγράψτε τα δεδομένα από το φύλλο εργασίας.\",\n          \"filterRangeContainingMerges\": \"Δεν μπορεί να δημιουργηθεί φίλτρο μέσα στο εύρος ένωσης\",\n          \"validationError\": \"Η τιμή που εισάγετε παραβιάζει την επικύρωση δεδομένων του κελιού.\"\n        },\n        \"tabs\": {\n          \"home\": \"Αρχική\",\n          \"insert\": \"Εισαγωγή\",\n          \"data\": \"Δεδομένα\"\n        }\n      });\n  }\n\n  /* Slider messages */\n\n  if (kendo.ui.Slider) {\n    kendo.ui.Slider.prototype.options =\n      $.extend(true, kendo.ui.Slider.prototype.options, {\n        \"increaseButtonTitle\": \"Αύξηση\",\n        \"decreaseButtonTitle\": \"Μείωση\"\n      });\n  }\n\n  /* TreeList messages */\n\n  if (kendo.ui.TreeList) {\n    kendo.ui.TreeList.prototype.options.messages =\n      $.extend(true, kendo.ui.TreeList.prototype.options.messages, {\n        \"noRows\": \"Δεν υπάρχουν εγγραφές\",\n        \"loading\": \"Φορτώνει...\",\n        \"requestFailed\": \"Αίτημα απέτυχε.\",\n        \"retry\": \"Επανάληψη\",\n        \"commands\": {\n          \"edit\": \"Επεξεργασία\",\n          \"update\": \"Ανανέωση\",\n          \"canceledit\": \"Άκυρο\",\n          \"create\": \"Προσθήκη Εγγραφής\",\n          \"createchild\": \"Προσθήκη Εγγραφής παιδιού\",\n          \"destroy\": \"Διαγραφή\",\n          \"excel\": \"Εξαγωγή σε Excel\",\n          \"pdf\": \"Εξαγωγή σε PDF\"\n        }\n      });\n  }\n\n  /* TreeView messages */\n\n  if (kendo.ui.TreeView) {\n    kendo.ui.TreeView.prototype.options.messages =\n      $.extend(true, kendo.ui.TreeView.prototype.options.messages, {\n        \"loading\": \"Φορτώνει...\",\n        \"requestFailed\": \"Αίτημα απέτυχε.\",\n        \"retry\": \"Επανάληψη\"\n      });\n  }\n\n  /* Upload messages */\n\n  if (kendo.ui.Upload) {\n    kendo.ui.Upload.prototype.options.localization =\n      $.extend(true, kendo.ui.Upload.prototype.options.localization, {\n        \"select\": \"Επιλογή αρχέιων...\",\n        \"cancel\": \"Άκυρο\",\n        \"retry\": \"Επανάληψη\",\n        \"remove\": \"Αφαίρεση\",\n        \"clearSelectedFiles\": \"Εκκαθάριση\",\n        \"uploadSelectedFiles\": \"Μεταφόρτωση αρχείων\",\n        \"dropFilesHere\": \"Σύρτε αρχεία εδώ για μεταφόρτωση\",\n        \"statusUploading\": \"μεταφόρτωση\",\n        \"statusUploaded\": \"ανέβηκε\",\n        \"statusWarning\": \"προειδοποίηση\",\n        \"statusFailed\": \"απέτυχε\",\n        \"headerStatusUploading\": \"Μεταφόρτωση...\",\n        \"headerStatusUploaded\": \"Ολοκλήρωση\",\n        \"invalidMaxFileSize\": \"Το μέγεθος του αρχείου είναι πολύ μεγάλο.\",\n        \"invalidMinFileSize\": \"Το μέγεθος του αρχείου είναι πολύ μικρό.\",\n        \"invalidFileExtension\": \"Μη έγκυρος τύπος αρχείου.\"\n      });\n  }\n\n  /* Validator messages */\n\n  if (kendo.ui.Validator) {\n    kendo.ui.Validator.prototype.options.messages =\n      $.extend(true, kendo.ui.Validator.prototype.options.messages, {\n        \"required\": \"{0} είναι υποχρεωτικό\",\n        \"pattern\": \"{0} δεν είναι έγκυρο\",\n        \"min\": \"{0} πρέπει να είναι μεγαλύτερο από ή ίσο με {1}\",\n        \"max\": \"{0} πρέπει να είναι μικτρότερο από ή ίσο με {1}\",\n        \"step\": \"{0} δεν είναι έγκυρο\",\n        \"email\": \"{0} δεν είναι έγκυρο email\",\n        \"url\": \"{0} δεν είναι έγκυρο URL\",\n        \"date\": \"{0} δεν είναι έγκυρη ημερομηνία\",\n        \"dateCompare\": \"Η ημερομηνία λήξης πρέπει να είναι μεγαλύερη από την ημερομηνία έναρξης\"\n      });\n  }\n\n  /* kendo.ui.progress method */\n  if (kendo.ui.progress) {\n    kendo.ui.progress.messages =\n      $.extend(true, kendo.ui.progress.messages, {\n        loading: \"Φόρτωση...\"\n      });\n  }\n\n  /* Dialog */\n\n  if (kendo.ui.Dialog) {\n    kendo.ui.Dialog.prototype.options.messages =\n      $.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n        \"close\": \"Κλείσιμο\"\n      });\n  }\n\n  /* Alert */\n\n  if (kendo.ui.Alert) {\n    kendo.ui.Alert.prototype.options.messages =\n      $.extend(true, kendo.ui.Alert.prototype.options.localization, {\n        \"okText\": \"Εντάξει\"\n      });\n  }\n\n  /* Confirm */\n\n  if (kendo.ui.Confirm) {\n    kendo.ui.Confirm.prototype.options.messages =\n      $.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n        \"okText\": \"Εντάξει\",\n        \"cancel\": \"Άκυρο\"\n      });\n  }\n\n  /* Prompt */\n  if (kendo.ui.Prompt) {\n    kendo.ui.Prompt.prototype.options.messages =\n      $.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n        \"okText\": \"Εντάξει\",\n        \"cancel\": \"Άκυρο\"\n      });\n  }\n\n})(window.kendo.jQuery);\n}));"]}