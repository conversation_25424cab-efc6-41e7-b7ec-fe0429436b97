{"version": 3, "sources": ["messages/kendo.messages.pt-BR.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "retry", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeList", "noRows", "loading", "requestFailed", "createchild", "TreeView", "Upload", "localization", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "progress", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,UACTC,OAAU,cAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,UACTC,OAAU,cAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,qBACjBC,eAAkB,sBAClBC,OAAU,SACVC,QAAW,UACXC,KAAQ,QACRC,SAAY,2BACZC,KAAQ,UACRC,OAAU,gBAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,UACRC,OAAU,UACVC,UAAa,aACbC,cAAiB,UACjBC,YAAe,cACfC,UAAa,YACbC,cAAiB,cACjBC,YAAe,qBACfC,aAAgB,oBAChBC,YAAe,aACfC,oBAAuB,0BACvBC,kBAAqB,yBACrBC,OAAU,iBACVC,QAAW,iBACXC,WAAc,iBACdC,OAAU,mBACVC,YAAe,iBACfC,WAAc,kBACdC,WAAc,eACdC,SAAY,qBACZC,SAAY,QACZC,gBAAmB,kBACnBC,SAAY,UACZC,gBAAmB,oBACnBC,YAAe,iBACfC,WAAc,UACdC,UAAa,MACbC,UAAa,eACbC,MAAS,SACTC,YAAe,cACfC,WAAc,iBACdC,QAAW,eACXC,YAAe,UACfC,YAAe,OACfC,gBAAmB,oFACnBC,WAAc,2CACdC,cAAiB,8EACjBC,kBAAqB,iDACrBC,gBAAmB,eACnBC,aAAgB,oBAChBC,WAAc,eACdC,YAAe,cACfC,eAAkB,eAClBC,UAAa,oBACbC,eAAkB,eAClBC,SAAY,QACZC,YAAe,UACfC,oBAAuB,4BACvBC,aAAgB,YAChBC,aAAgB,UAChBC,sBAAyB,KACzBC,aAAgB,WAChBC,YAAe,iBACfC,cAAiB,yBACjBC,eAAkB,wBAClBC,YAAe,oBACfC,YAAe,qBACfC,UAAa,gBACbC,aAAgB,iBAChBC,SAAY,KACZC,YAAe,uBACfC,SAAY,SACZC,QAAW,SACXC,iBAAoB,iBACpBC,QAAW,SACXC,QAAW,SACXC,MAAS,UACTC,OAAU,SACVC,YAAe,wBACfC,YAAe,0BACfC,WAAc,mBACdC,UAAa,cACbC,WAAc,QACdC,SAAY,gBACZC,GAAM,KACNC,OAAU,QACVC,YAAe,kBACfC,gBAAmB,kBACnBC,SAAY,gBACZC,0BAA6B,mCAC7BC,UAAa,qBACbC,YAAe,oBACfC,WAAc,oBACdC,aAAgB,2BAChBC,eAAkB,2BAClBC,cAAiB,2BACjBC,gBAAmB,4BACnBC,kBAAqB,2BACrBC,iBAAoB,2BACpBC,gBAAmB,8BACnBC,kBAAqB,6BACrBC,iBAAoB,6BACpBC,YAAe,sBACflG,QAAW,UACXmG,KAAQ,SACRC,eAAkB,iCAMhBlH,MAAMC,GAAGkH,cACbnH,MAAMC,GAAGkH,YAAYhH,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkH,YAAYhH,UAAUC,QAAQC,UACpD6C,WAAc,SACdC,QAAW,eACXE,YAAe,OACfD,YAAe,UACfK,kBAAqB,gDACrBR,YAAe,cACfM,WAAc,wCACdD,gBAAmB,mFACnBE,cAAiB,sFACjB4D,cAAiB,6CACjBC,OAAU,cAMRrH,MAAMC,GAAGqH,aACbtH,MAAMC,GAAGqH,WAAWnH,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqH,WAAWnH,UAAUC,QAAQC,UACnDkH,OAAU,YACVC,QAAW,UACX3G,OAAU,UACV4G,MAAS,SACTC,SAAY,cAMV1H,MAAMC,GAAGqH,aACbtH,MAAMC,GAAGqH,WAAWnH,UAAUC,QAAQuH,UACtC7H,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqH,WAAWnH,UAAUC,QAAQuH,WACnDC,QACEC,GAAM,YACNC,IAAO,gBACPC,WAAc,aACdC,SAAY,SACZC,eAAkB,aAClBC,SAAY,cACZC,OAAU,SACVC,UAAa,aACbC,QAAW,UACXC,WAAc,eAEhBC,QACEV,GAAM,YACNC,IAAO,gBACPU,IAAO,yBACPC,GAAM,cACNC,IAAO,yBACPC,GAAM,cACNR,OAAU,SACVC,UAAa,cAEfQ,MACEf,GAAM,YACNC,IAAO,gBACPU,IAAO,yBACPC,GAAM,gBACNC,IAAO,wBACPC,GAAM,eACNR,OAAU,SACVC,UAAa,cAEfS,OACEhB,GAAM,YACNC,IAAO,gBACPK,OAAU,SACVC,UAAa,iBAObpI,MAAMC,GAAG6I,aACb9I,MAAMC,GAAG6I,WAAW3I,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6I,WAAW3I,UAAUC,QAAQC,UACnD0I,KAAQ,gCACRC,MAAS,gCACTzB,OAAU,YACVC,QAAW,UACX3G,OAAU,UACV4G,MAAS,SACTwB,IAAO,IACPC,GAAM,KACNC,YAAe,wBACfzB,SAAY,WACZ0B,MAAS,QACT5I,OAAU,cAMRR,MAAMC,GAAG6I,aACb9I,MAAMC,GAAG6I,WAAW3I,UAAUC,QAAQuH,UACtC7H,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6I,WAAW3I,UAAUC,QAAQuH,WACnDC,QACEC,GAAM,YACNC,IAAO,gBACPC,WAAc,aACdC,SAAY,SACZC,eAAkB,aAClBC,SAAY,cACZC,OAAU,SACVC,UAAa,aACbC,QAAW,UACXC,WAAc,eAEhBC,QACEV,GAAM,YACNC,IAAO,gBACPU,IAAO,yBACPC,GAAM,cACNC,IAAO,yBACPC,GAAM,cACNR,OAAU,SACVC,UAAa,cAEfQ,MACEf,GAAM,YACNC,IAAO,gBACPU,IAAO,yBACPC,GAAM,gBACNC,IAAO,wBACPC,GAAM,eACNR,OAAU,SACVC,UAAa,cAEfS,OACEhB,GAAM,YACNC,IAAO,gBACPK,OAAU,SACVC,UAAa,iBAObpI,MAAMC,GAAGoJ,mBACbrJ,MAAMC,GAAGoJ,iBAAiBlJ,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoJ,iBAAiBlJ,UAAUC,QAAQC,UACzDiJ,SAAY,mBACZ7B,MAAS,SACT5G,OAAU,UACVwG,OAAU,cAMRrH,MAAMC,GAAGsJ,QACbvJ,MAAMC,GAAGsJ,MAAMpJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsJ,MAAMpJ,UAAUC,QAAQC,UAC9CmJ,SACEC,SAAY,kBACZC,OAAU,mBACVC,YAAe,mBACfC,aAAgB,kBAChBC,IAAO,qBAETrJ,OAAU,WACVsJ,4BAA+B,sBAC/BC,sBAAyB,iBACzBC,QAAW,UACXC,QACEC,aAAgB,WAChBC,YAAe,SACfC,IAAO,QACPC,gBAAmB,WACnBC,UAAa,WACbC,qBAAwB,WACxBC,gBAAmB,WACnBC,MAAS,SACTzB,MAAS,SACT0B,YAAe,YAEjBC,KAAQ,SACRC,OACEC,IAAO,MACPT,IAAO,QACPU,MAAS,MACTL,MAAS,SACTM,KAAQ,SACRC,KAAQ,UAORhL,MAAMC,GAAGgL,OACbjL,MAAMC,GAAGgL,KAAK9K,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgL,KAAK9K,UAAUC,QAAQC,UAC7C6K,UACE1K,OAAU,sBACV2K,WAAc,WACdC,OAAU,YACVpB,QAAW,UACXqB,KAAQ,SACRC,MAAS,sBACTzB,IAAO,oBACPc,KAAQ,oBACRY,OAAU,aACVC,OAAU,aAEZC,UACEC,aAAgB,WAChBC,aAAgB,qDAChBC,cAAiB,WAEnBC,UAAa,iCAMX7L,MAAMC,GAAG6L,YACb9L,MAAMC,GAAG6L,UAAU3L,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6L,UAAU3L,UAAUC,QAAQC,UAClD0L,MAAS,yEAMP/L,MAAMC,GAAG+L,iBACbhM,MAAMC,GAAG+L,eAAe7L,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+L,eAAe7L,UAAUC,SAC/C6L,YAAe,iBACfC,cAAiB,oBAMflM,MAAMC,GAAGkM,cACbnM,MAAMC,GAAGkM,YAAYhM,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkM,YAAYhM,UAAUC,QAAQC,UACpD+L,MAAS,SACTC,KAAQ,UACRC,KAAQ,OACRC,OAAU,aACVC,QAAW,YACXC,WAAc,gBAMZzM,MAAMC,GAAGyM,QACb1M,MAAMC,GAAGyM,MAAMvM,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,MAAMvM,UAAUC,QAAQC,UAC9CsM,SAAY,QACZC,QAAW,kCACXb,MAAS,8BACTc,KAAQ,SACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,4BACTC,SAAY,4BACZC,KAAQ,2BACRC,KAAQ,0BACRC,QAAW,YACXC,UAAa,kBAMXrN,MAAMC,GAAGqN,gBACbtN,MAAMC,GAAGqN,cAAcnN,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqN,cAAcnN,UAAUC,QAAQC,UACtDsM,SAAY,QACZC,QAAW,kCACXb,MAAS,8BACTc,KAAQ,SACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,4BACTC,SAAY,4BACZC,KAAQ,2BACRC,KAAQ,0BACRC,QAAW,YACXC,UAAa,kBAMXrN,MAAMC,GAAGsN,YACbvN,MAAMC,GAAGsN,UAAUpN,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsN,UAAUpN,UAAUC,QAAQC,UAClDmN,cAAiB,8BACjBC,aAAgB,+BAChBC,UAAa,iCAMX1N,MAAMC,GAAG0N,iBACb3N,MAAMC,GAAG0N,eAAexN,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0N,eAAexN,UAAUC,QAAQC,UACvD0I,KAAQ,+BACR6E,aAAgB,mBAChB/M,OAAU,SACVgN,QAAW,oBACX7E,MAAS,mBACTvB,MAAS,SACTqG,GAAM,KACNtN,OAAU,WACVmH,WACEK,SAAY,SACZC,eAAkB,aAClBF,WAAc,aACdG,SAAY,cACZL,GAAM,YACNC,IAAO,oBAOP9H,MAAMC,GAAG8N,mBACb/N,MAAMC,GAAG8N,iBAAiB5N,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8N,iBAAiB5N,UAAUC,QAAQC,UACzD2N,aACEC,MAAS,QACTC,OAAU,WACVC,MAAS,cACTC,OAAU,eACVC,QAAW,cACXC,OAAU,cAEZJ,QACEK,YAAe,iBACfC,SAAY,YAEdL,OACEI,YAAe,iBACfC,SAAY,WAEdJ,QACEI,SAAY,YACZD,YAAe,iBACfE,SAAY,gBAEdJ,SACEE,YAAe,iBACfE,SAAY,eACZD,SAAY,WACZ3D,IAAO,QAETyD,QACEC,YAAe,iBACfE,SAAY,eACZD,SAAY,WACZ1B,GAAM,QAER1C,KACEsE,MAAS,OACTC,YAAe,QACfV,MAAS,QACTW,MAAS,QACTC,WAAc,iBACdC,GAAM,OAERC,iBACE/B,MAAS,WACTgC,OAAU,UACVC,MAAS,WACTC,OAAU,SACV/B,KAAQ,UAEVgC,UACEtE,IAAO,MACPuE,QAAW,gBACXC,QAAW,sBAOXrP,MAAMC,GAAGqP,YACbtP,MAAMC,GAAGqP,UAAUnP,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqP,UAAUnP,UAAUC,QAAQC,UAClDkP,OAAU,cACV3G,KAAQ,OACR4G,MAAS,SACTC,KAAQ,OACRC,YAAe,cACfC,YAAe,oBACfC,MAAS,OACTjF,KAAQ,SACRnK,OAAU,WACVwJ,QAAW,UACX6F,kBAAqB,iBACrBC,cAAiB,8BACjBC,eAAkB,yBAClBtE,UACEE,aAAgB,+CAElBf,OACEC,IAAO,MACPE,KAAQ,SACRiF,SAAY,qBACZC,OAAU,SACVnF,MAAS,OAEXoF,oBACEL,kBAAqB,0BACrBM,uBAA0B,2BAC1BC,mBAAsB,gBACtBC,gBAAmB,yBACnBC,qBAAwB,0BACxBC,iBAAoB,eACpBC,gBAAmB,kEACnBC,cAAiB,2DAEnBxG,QACEjB,MAAS,SACTyB,MAAS,SACTL,IAAO,MACPsG,YAAe,wBACfC,YAAe,YACfC,OAAU,UACVC,SAAY,GACZC,cAAiB,uBACjBC,YAAe,qBACfC,kBAAqB,kDACrBC,oBAAuB,iBACvBC,qBAAwB,eACxBC,cAAiB,gBACjBC,WAAc,mBACdjH,YAAe,aAOfnK,MAAMqR,aAAerR,MAAMqR,YAAYhR,SAASiR,gBACpDtR,MAAMqR,YAAYhR,SAASiR,cAC3BxR,EAAEQ,QAAO,EAAMN,MAAMqR,YAAYhR,SAASiR,eACxCC,WAAc,kBACdC,cAAiB,oBACjBC,wBAA2B,gCAC3BC,sBAAyB,8BACzBC,eAAkB,kBAClBC,WAAc,iBACdC,UAAa,iBACbC,YAAe,eACfC,aAAgB,iBAChBC,UAAa,aACbC,MAAS,gBACTC,YAAe,uBACf3R,MAAS,UACTC,OAAU,cAIRR,MAAMqR,aAAerR,MAAMqR,YAAYhR,SAAS8R,UACpDnS,MAAMqR,YAAYhR,SAAS8R,QAC3BrS,EAAEQ,QAAO,EAAMN,MAAMqR,YAAYhR,SAAS8R,SACxC5R,MAAS,UACToK,KAAQ,SACRnK,OAAU,WACV4R,OAAU,UACVC,MAAS,mBACTC,OAAU,WACVC,OAAU,KACVC,mBACExJ,MAAS,WACTyJ,YACElK,OAAU,SACVmK,SAAY,QACZ9J,KAAQ,SAGZ+J,kBACE3J,MAAS,SAEX4J,gBACE5J,MAAS,oBAEX6J,eACE7J,MAAS,UAEX8J,iBACE9J,MAAS,cACT+J,SACCC,aAAgB,qBAChBtR,cAAiB,cACjBE,aAAgB,oBAChBC,YAAe,aACfoR,SAAY,kBACZC,YAAe,kBACfC,YAAe,mBAGlBC,aACEpK,MAAS,kBACT+J,SACEM,WAAc,eACdC,kBAAqB,0BACrBC,gBAAmB,wBACnBC,QAAW,eAGfC,cACEzK,MAAS,iBACT+J,SACEW,YAAe,iBACfC,WAAc,gBACdC,cAAiB,iBACjBC,SAAY,sBAGhBC,kBACE9K,MAAS,mBACT+K,YAAe,+CACfC,UAAa,gBACbC,UACEC,IAAO,iBACP3L,OAAU,SACV4L,KAAQ,QACRvL,KAAQ,OACRwL,OAAU,wBACVC,KAAQ,SAEVC,WACEC,YAAe,YACfC,SAAY,YACZC,QAAW,QACXC,WAAc,iBACdC,QAAW,QACXC,WAAc,cACdC,qBAAwB,qBACxBC,kBAAqB,sBAEvBC,kBACER,YAAe,gBACfC,SAAY,gBACZC,QAAW,kBACXC,WAAc,2BACdC,QAAW,cACXC,WAAc,oBACdC,qBAAwB,uBACxBC,kBAAqB,uBACrBV,OAAU,gCAEZY,QACEf,SAAY,WACZgB,SAAY,WACZC,IAAO,MACPC,IAAO,MACP/L,MAAS,QACTqB,MAAS,SACTL,IAAO,MACPgL,cAAiB,qBACjBC,YAAe,mBACfC,YAAe,gBACfC,SAAY,eACZvB,UAAa,iBACbD,YAAe,mBACfyB,YAAe,qBAEjBC,cACEC,UAAa,kBACbC,YAAe,uBAGnBC,gBACE5M,MAAS,cACTgM,QACEa,SAAY,kBACZC,WAAc,mBACdC,WAAc,WACdC,UAAa,mBACbC,QAAW,UACXC,YAAe,aACfC,MAAS,WACTC,WAAc,aACdC,OAAU,cACVC,aAAgB,kBAChBC,WAAc,kBAGlBC,oBACEC,aAAgB,wDAElBC,mBACE1N,MAAS,iBACTyN,aAAgB,iGAChBzB,QACE2B,QAAW,aACXC,OAAU,gBACVC,SAAY,eAGhBC,4BACEL,aAAgB,4DAKhBzW,MAAMqR,aAAerR,MAAMqR,YAAYhR,SAAS0W,aACpD/W,MAAMqR,YAAYhR,SAAS0W,WAC3BjX,EAAEQ,QAAO,EAAMN,MAAMqR,YAAYhR,SAAS0W,YACxCpW,cAAiB,uBACjBC,eAAkB,uBAClBoW,cAAiB,oBACjBC,kBAAqB,wBACrB1W,MAAS,UACT8G,OAAU,WACV6P,aAAgB,oBAChBzP,MAAS,SACT0P,OAAU,UACVC,aAAgB,SAChBnO,IAAO,IACPC,GAAM,KACNvB,WACEC,QACEI,SAAY,eACZC,eAAkB,mBAClBF,WAAc,mBACdG,SAAY,qBAEdU,MACEf,GAAO,YACPC,IAAO,gBACPa,GAAO,eACPF,GAAO,iBAETF,QACEV,GAAM,YACNC,IAAO,gBACPU,IAAO,qBACPC,GAAM,cACNC,IAAO,qBACPC,GAAM,mBAMR3I,MAAMqR,aAAerR,MAAMqR,YAAYhR,SAASgX,UACpDrX,MAAMqR,YAAYhR,SAASgX,QAC3BvX,EAAEQ,QAAO,EAAMN,MAAMqR,YAAYhR,SAASgX,SACxC5S,cAAiB,8BACjBC,eAAkB,6BAClBC,YAAe,wBACfC,YAAe,yBACfe,UAAa,cACb2R,kBACEtE,aAAgB,qBAChBtR,cAAiB,cACjBE,aAAgB,mBAChBC,YAAe,aACfoR,SAAY,gBACZC,YAAe,kBACfC,YAAe,kBAEjBoE,gBAAmB,QACnBnW,KAAQ,UACRoW,QAAW,SACXC,aACExF,MAAS,gBACTC,YAAe,wBAEjBwF,KAAQ,SACRC,IAAO,WACP7S,aAAgB,iBAChBD,UAAa,gBACb+S,YAAe,uBACf/W,OAAU,SACVgX,WAAc,QACdnV,SAAY,mBACZoV,OAAU,2BACVC,aACEC,UAAa,aACbzP,OAAU,SACV0P,QAAW,cACXC,UAAa,aACbxF,SAAY,QACZ9J,KAAQ,OACR6G,KAAQ,OACR0I,SAAY,YACZC,SAAY,UACZC,YAAe,oBAEjBC,sBAAyB,mBACzBC,sBAAyB,mBACzBC,OAAU,iBACVC,eACE/E,YAAe,iBACfC,WAAc,gBACdC,cAAiB,iBACjBC,SAAY,qBAEdxS,OAAU,UACVqX,MAAS,kBACTC,cACEtF,WAAc,eACdC,kBAAqB,0BACrBC,gBAAmB,wBACnBC,QAAW,cAEboF,KAAQ,WACRC,MAAS,QACTC,aACEC,KAAQ,UACRC,KAAQ,YAEVC,OAAU,iBACVC,QAAW,qBACXC,SAAY,0BACZC,aACEC,aAAgB,6BAChBC,cAAiB,6BACjBC,aAAgB,6BAChBC,cAAiB,8BAEnBC,UAAa,eACbC,SAAY,iBACZpY,UAAa,aACbqY,WAAc,yBAIZ3Z,MAAMqR,aAAerR,MAAMqR,YAAYhR,SAASuZ,OACpD5Z,MAAMqR,YAAYhR,SAASuZ,KAC3B9Z,EAAEQ,QAAO,EAAMN,MAAMqR,YAAYhR,SAASuZ,MACxCC,QACEC,sBAAyB,0JACzBC,4BAA+B,+EAC/BC,gBAAmB,sEAErBC,MACEC,KAAQ,YACRC,OAAU,UACVC,KAAQ,WAORpa,MAAMC,GAAGoa,SACbra,MAAMC,GAAGoa,OAAOla,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoa,OAAOla,UAAUC,SACvCka,oBAAuB,WACvBC,oBAAuB,cAMrBva,MAAMC,GAAGua,WACbxa,MAAMC,GAAGua,SAASra,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGua,SAASra,UAAUC,QAAQC,UACjDoa,OAAU,iBACVC,QAAW,gBACXC,cAAiB,wBACjBtI,MAAS,mBACTnH,UACIG,KAAQ,SACRG,OAAU,YACVL,WAAc,WACdC,OAAU,yBACVwP,YAAe,2BACf5Q,QAAW,UACXsB,MAAS,sBACTzB,IAAO,wBAOT7J,MAAMC,GAAG4a,WACb7a,MAAMC,GAAG4a,SAAS1a,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4a,SAAS1a,UAAUC,QAAQC,UACjDqa,QAAW,gBACXC,cAAiB,qBACjBtI,MAAS,sBAMPrS,MAAMC,GAAG6a,SACb9a,MAAMC,GAAG6a,OAAO3a,UAAUC,QAAQ2a,aAClCjb,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6a,OAAO3a,UAAUC,QAAQ2a,cAC/CxP,OAAU,gBACV/K,OAAU,WACV6R,MAAS,mBACTD,OAAU,UACV4I,oBAAuB,kBACvB5T,cAAiB,oCACjB6T,gBAAmB,WACnBC,eAAkB,UAClBC,cAAiB,UACjBC,aAAgB,SAChBC,sBAAyB,gBACzBC,qBAAwB,YAMtBtb,MAAMC,GAAGsb,YACbvb,MAAMC,GAAGsb,UAAUpb,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsb,UAAUpb,UAAUC,QAAQC,UAClDmb,SAAY,oBACZC,QAAW,mBACXvG,IAAO,oCACPC,IAAO,oCACPuG,KAAQ,mBACRC,MAAS,4BACTC,IAAO,mCACPhT,KAAQ,4BACRiT,YAAe,oDAKb7b,MAAMC,GAAG6b,WACb9b,MAAMC,GAAG6b,SAASzb,SAClBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6b,SAASzb,UAC7Bqa,QAAS,mBAMT1a,MAAMC,GAAG8b,SACb/b,MAAMC,GAAG8b,OAAO5b,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8b,OAAO5b,UAAUC,QAAQ2a,cAC/CiB,MAAS,YAMPhc,MAAMC,GAAGgc,QACbjc,MAAMC,GAAGgc,MAAM9b,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgc,MAAM9b,UAAUC,QAAQ2a,cAC9CxI,OAAU,QAMRvS,MAAMC,GAAGic,UACblc,MAAMC,GAAGic,QAAQ/b,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGic,QAAQ/b,UAAUC,QAAQ2a,cAChDxI,OAAU,KACV/R,OAAU,cAKRR,MAAMC,GAAGkc,SACbnc,MAAMC,GAAGkc,OAAOhc,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkc,OAAOhc,UAAUC,QAAQ2a,cAC/CxI,OAAU,KACV/R,OAAU,eAIT4b,OAAOpc,MAAMqc", "file": "kendo.messages.pt-BR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Aplicar\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Aplicar\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Ordenar Ascendente\",\n  \"sortDescending\": \"Ordenar Descendente\",\n  \"filter\": \"Filtro\",\n  \"columns\": \"Colunas\",\n  \"done\": \"Feito\",\n  \"settings\": \"Configurações de Colunas\",\n  \"lock\": \"Trancar\",\n  \"unlock\": \"Destrancar\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Negrito\",\n  \"italic\": \"Itálico\",\n  \"underline\": \"Sublinhado\",\n  \"strikethrough\": \"Tachado\",\n  \"superscript\": \"Sobrescrito\",\n  \"subscript\": \"Subscrito\",\n  \"justifyCenter\": \"Centralizar\",\n  \"justifyLeft\": \"Alinhar à Esquerda\",\n  \"justifyRight\": \"Alinhar à Direita\",\n  \"justifyFull\": \"Justificar\",\n  \"insertUnorderedList\": \"Inserir Lista Aleatória\",\n  \"insertOrderedList\": \"Inserir Lista Ordenada\",\n  \"indent\": \"Aumentar Recuo\",\n  \"outdent\": \"Diminuir Recuo\",\n  \"createLink\": \"Adicionar Link\",\n  \"unlink\": \"Remove Hyperlink\",\n  \"insertImage\": \"Inserir Imagem\",\n  \"insertFile\": \"Inserir arquivo\",\n  \"insertHtml\": \"Inserir HTML\",\n  \"viewHtml\": \"Exibir código HTML\",\n  \"fontName\": \"Fonte\",\n  \"fontNameInherit\": \"(fonte herdada)\",\n  \"fontSize\": \"Tamanho\",\n  \"fontSizeInherit\": \"(tamanho herdado)\",\n  \"formatBlock\": \"Formatar Bloco\",\n  \"formatting\": \"Formato\",\n  \"foreColor\": \"Cor\",\n  \"backColor\": \"Cor de Fundo\",\n  \"style\": \"Estilo\",\n  \"emptyFolder\": \"Pasta vazia\",\n  \"uploadFile\": \"Enviar arquivo\",\n  \"orderBy\": \"Ordenar por:\",\n  \"orderBySize\": \"Tamanho\",\n  \"orderByName\": \"Nome\",\n  \"invalidFileType\": \"O arquivo selecionado \\\"{0}\\\" não é válido. Os tipos de arquivo suportados são {1}.\",\n  \"deleteFile\": \"Tem certeza de que deseja remover \\\"{0}\\\"?\",\n  \"overwriteFile\": \"Um arquivo de nome \\\"{0}\\\" já existe no diretório atual. Deseja substituí-lo?\",\n  \"directoryNotFound\": \"Um diretório com este nome não foi encontrado.\",\n  \"imageWebAddress\": \"Endereço web\",\n  \"imageAltText\": \"Texto alternativo\",\n  \"imageWidth\": \"Largura (px)\",\n  \"imageHeight\": \"Altura (px)\",\n  \"fileWebAddress\": \"Endereço Web\",\n  \"fileTitle\": \"Título do arquivo\",\n  \"linkWebAddress\": \"Endereço Web\",\n  \"linkText\": \"Texto\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkOpenInNewWindow\": \"Abrir link em nova janela\",\n  \"dialogUpdate\": \"Atualizar\",\n  \"dialogInsert\": \"Inserir\",\n  \"dialogButtonSeparator\": \"ou\",\n  \"dialogCancel\": \"Cancelar\",\n  \"createTable\": \"Criar a tabela\",\n  \"addColumnLeft\": \"Nova coluna à esquerda\",\n  \"addColumnRight\": \"Nova coluna à direita\",\n  \"addRowAbove\": \"Nova coluna acima\",\n  \"addRowBelow\": \"Nova coluna abaixo\",\n  \"deleteRow\": \"Excluir linha\",\n  \"deleteColumn\": \"Excluir coluna\",\n  \"dialogOk\": \"Ok\",\n  \"tableWizard\": \"Assistente de tabela\",\n  \"tableTab\": \"Tabela\",\n  \"cellTab\": \"Célula\",\n  \"accessibilityTab\": \"Acessibilidade\",\n  \"caption\": \"Rubica\",\n  \"summary\": \"Resumo\",\n  \"width\": \"Largura\",\n  \"height\": \"Altura\",\n  \"cellSpacing\": \"Espaçamento da célula\",\n  \"cellPadding\": \"Preenchimento da célula\",\n  \"cellMargin\": \"Margem da célula\",\n  \"alignment\": \"Alinhamento\",\n  \"background\": \"Fundo\",\n  \"cssClass\": \"Classe do CSS\",\n  \"id\": \"ID\",\n  \"border\": \"Borda\",\n  \"borderStyle\": \"Estilo da borda\",\n  \"collapseBorders\": \"Colapsar bordas\",\n  \"wrapText\": \"Quebrar textp\",\n  \"associateCellsWithHeaders\": \"Células associadas com cabeçalho\",\n  \"alignLeft\": \"Alinhar à esquerda\",\n  \"alignCenter\": \"Alinhar ao centro\",\n  \"alignRight\": \"Alinhar à direita\",\n  \"alignLeftTop\": \"Alinhar à equerda e topo\",\n  \"alignCenterTop\": \"Alinhar ao centro e topo\",\n  \"alignRightTop\": \"Alinhar à direita e topo\",\n  \"alignLeftMiddle\": \"Alinhar à esquerda e meio\",\n  \"alignCenterMiddle\": \"Alinhar ao centro e meio\",\n  \"alignRightMiddle\": \"Alinhar à direita e meio\",\n  \"alignLeftBottom\": \"Alinhar à esquerda e abaixo\",\n  \"alignCenterBottom\": \"Alinhar ao centro e abaixo\",\n  \"alignRightBottom\": \"Alinhar à direita e abaixo\",\n  \"alignRemove\": \"Remover alinhamento\",\n  \"columns\": \"Colunas\",\n  \"rows\": \"Linhas\",\n  \"selectAllCells\": \"Selecionar todas as células\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Upload\",\n  \"orderBy\": \"Organize por\",\n  \"orderByName\": \"Nome\",\n  \"orderBySize\": \"Tamanho\",\n  \"directoryNotFound\": \"O diretório com este nome não foi encontrado.\",\n  \"emptyFolder\": \"Pasta vazia\",\n  \"deleteFile\": 'Tem certeza que deseja excluir \"{0}\"?',\n  \"invalidFileType\": \"O arquivo selecionado \\\"{0}\\\" é inválido. Os tipos de arquivos suportados são {1}.\",\n  \"overwriteFile\": \"O arquivo com o nome \\\"{0}\\\" já existe no diretório selecionado. Deseja sobrescrever?\",\n  \"dropFilesHere\": \"Solte os arquivos aqui para fazer o Upload\",\n  \"search\": \"Procurar\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"É verdade\",\n  \"isFalse\": \"É falso\",\n  \"filter\": \"Filtrar\",\n  \"clear\": \"Limpar\",\n  \"operator\": \"Operador\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"startswith\": \"Começa com\",\n    \"contains\": \"Contém\",\n    \"doesnotcontain\": \"Não contém\",\n    \"endswith\": \"Termina com\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\",\n    \"isempty\": \"É vazio\",\n    \"isnotempty\": \"É não vazio\"\n  },\n  \"number\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"gte\": \"É maior que ou igual a\",\n    \"gt\": \"É maior que\",\n    \"lte\": \"É menor que ou igual a\",\n    \"lt\": \"É menor que\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  },\n  \"date\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"gte\": \"É posterior ou igual a\",\n    \"gt\": \"É posterior a\",\n    \"lte\": \"É anterior ou igual a\",\n    \"lt\": \"É anterior a\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  },\n  \"enums\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Exibir linhas com valores que\",\n  \"title\": \"Exibir linhas com valores que\",\n  \"isTrue\": \"É verdade\",\n  \"isFalse\": \"É falso\",\n  \"filter\": \"Filtrar\",\n  \"clear\": \"Limpar\",\n  \"and\": \"E\",\n  \"or\": \"Ou\",\n  \"selectValue\": \"-Selecione uma opção-\",\n  \"operator\": \"Operador\",\n  \"value\": \"Valor\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"startswith\": \"Começa com\",\n    \"contains\": \"Contém\",\n    \"doesnotcontain\": \"Não contém\",\n    \"endswith\": \"Termina com\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\",\n    \"isempty\": \"É vazio\",\n    \"isnotempty\": \"É não vazio\"\n  },\n  \"number\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"gte\": \"É maior que ou igual a\",\n    \"gt\": \"É maior que\",\n    \"lte\": \"É menor que ou igual a\",\n    \"lt\": \"É menor que\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  },\n  \"date\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"gte\": \"É posterior ou igual a\",\n    \"gt\": \"É posterior a\",\n    \"lte\": \"É anterior ou igual a\",\n    \"lt\": \"É anterior a\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  },\n  \"enums\": {\n    \"eq\": \"É igual a\",\n    \"neq\": \"Não é igual a\",\n    \"isnull\": \"É nulo\",\n    \"isnotnull\": \"É não nulo\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Selecionar todos\",\n  \"clear\": \"Limpar\",\n  \"filter\": \"Filtrar\",\n  \"search\": \"Procurar\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Adicionar filho\",\n    \"append\": \"Adicionar tarefa\",\n    \"insertAfter\": \"Adicionar abaixo\",\n    \"insertBefore\": \"Adicionar acima\",\n    \"pdf\": \"Exportar para PDF\"\n  },\n  \"cancel\": \"Cancelar\",\n  \"deleteDependencyWindowTitle\": \"Excluir dependência\",\n  \"deleteTaskWindowTitle\": \"Excluir tarefa\",\n  \"destroy\": \"Excluir\",\n  \"editor\": {\n    \"assingButton\": \"Atribuir\",\n    \"editorTitle\": \"Tarefa\",\n    \"end\": \"Final\",\n    \"percentComplete\": \"Completo\",\n    \"resources\": \"Recursos\",\n    \"resourcesEditorTitle\": \"Recursos\",\n    \"resourcesHeader\": \"Recursos\",\n    \"start\": \"Inicio\",\n    \"title\": \"Título\",\n    \"unitsHeader\": \"Unidades\"\n  },\n  \"save\": \"Salvar\",\n  \"views\": {\n    \"day\": \"Dia\",\n    \"end\": \"Final\",\n    \"month\": \"Mês\",\n    \"start\": \"Inicio\",\n    \"week\": \"Semana\",\n    \"year\": \"Ano\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Cancelar alterações\",\n    \"canceledit\": \"Cancelar\",\n    \"create\": \"Adicionar\",\n    \"destroy\": \"Excluir\",\n    \"edit\": \"Editar\",\n    \"excel\": \"Exportar para Excel\",\n    \"pdf\": \"Exportar para PDF\",\n    \"save\": \"Salvar alterações\",\n    \"select\": \"Selecionar\",\n    \"update\": \"Atualizar\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Cancelar\",\n    \"confirmation\": \"Você tem certeza que deseja excluir este registro?\",\n    \"confirmDelete\": \"Excluir\"\n  },\n  \"noRecords\": \"Nenhum registro encontrado.\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Arraste aqui o cabeçalho de uma coluna para agrupar por esta coluna\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Aumentar valor\",\n  \"downArrowText\": \"Diminuir valor\"\n});\n}\n\n/* MediaPlayer messages */\n\nif (kendo.ui.MediaPlayer) {\nkendo.ui.MediaPlayer.prototype.options.messages =\n$.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n  \"pause\": \"Pausar\",\n  \"play\": \"Iniciar\",\n  \"mute\": \"Mudo\",\n  \"unmute\": \"Ativar som\",\n  \"quality\": \"Qualidade\",\n  \"fullscreen\": \"Tela cheia\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Todos\",\n  \"display\": \"Exibindo itens {0} - {1} de {2}\",\n  \"empty\": \"Nenhum registro encontrado.\",\n  \"page\": \"Página\",\n  \"of\": \"de {0}\",\n  \"itemsPerPage\": \"itens por página\",\n  \"first\": \"Ir para a primeira página\",\n  \"previous\": \"Ir para a página anterior\",\n  \"next\": \"Ir para a próxima página\",\n  \"last\": \"Ir para a última página\",\n  \"refresh\": \"Atualizar\",\n  \"morePages\": \"Mais páginas\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"Todos\",\n  \"display\": \"Exibindo itens {0} - {1} de {2}\",\n  \"empty\": \"Nenhum registro encontrado.\",\n  \"page\": \"Página\",\n  \"of\": \"de {0}\",\n  \"itemsPerPage\": \"itens por página\",\n  \"first\": \"Ir para a primeira página\",\n  \"previous\": \"Ir para a página anterior\",\n  \"next\": \"Ir para a próxima página\",\n  \"last\": \"Ir para a última página\",\n  \"refresh\": \"Atualizar\",\n  \"morePages\": \"Mais páginas\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Soltar campos de dados aqui\",\n  \"columnFields\": \"Soltar campos de coluna aqui\",\n  \"rowFields\": \"Soltar campos de linha aqui\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Mostrar itens com valor que:\",\n  \"filterFields\": \"Filtro de campos\",\n  \"filter\": \"Filtro\",\n  \"include\": \"Incluir campos...\",\n  \"title\": \"Campos a incluir\",\n  \"clear\": \"Limpar\",\n  \"ok\": \"Ok\",\n  \"cancel\": \"Cancelar\",\n  \"operators\": {\n    \"contains\": \"Contém\",\n    \"doesnotcontain\": \"Não contém\",\n    \"startswith\": \"Começa com\",\n    \"endswith\": \"Termina com\",\n    \"eq\": \"É igual à\",\n    \"neq\": \"Não é igual a\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Nunca\",\n    \"hourly\": \"Por hora\",\n    \"daily\": \"Diariamente\",\n    \"weekly\": \"Semanalmente\",\n    \"monthly\": \"Mensalmente\",\n    \"yearly\": \"Anualmente\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Repetir toda: \",\n    \"interval\": \" hora(s)\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Repetir todo: \",\n    \"interval\": \" dia(s)\"\n  },\n  \"weekly\": {\n    \"interval\": \"semana(s)\",\n    \"repeatEvery\": \"Repetir todo: \",\n    \"repeatOn\": \"Repetir em: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Repetir todo: \",\n    \"repeatOn\": \"Repetir em: \",\n    \"interval\": \" mês(es)\",\n    \"day\": \"Dia \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Repetir todo: \",\n    \"repeatOn\": \"Repetir em: \",\n    \"interval\": \" ano(s) \",\n    \"of\": \" de \"\n  },\n  \"end\": {\n    \"label\": \"Fim:\",\n    \"mobileLabel\": \"Final\",\n    \"never\": \"Nunca\",\n    \"after\": \"Após \",\n    \"occurrence\": \" ocorrência(s)\",\n    \"on\": \"Em \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"primeiro\",\n    \"second\": \"segundo\",\n    \"third\": \"terceiro\",\n    \"fourth\": \"quarto\",\n    \"last\": \"último\"\n  },\n  \"weekdays\": {\n    \"day\": \"dia\",\n    \"weekday\": \"dia da semana\",\n    \"weekend\": \"final de semana\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"dia inteiro\",\n  \"date\": \"Data\",\n  \"event\": \"Evento\",\n  \"time\": \"Hora\",\n  \"showFullDay\": \"Dia inteiro\",\n  \"showWorkDay\": \"Horário comercial\",\n  \"today\": \"Hoje\",\n  \"save\": \"Salvar\",\n  \"cancel\": \"Cancelar\",\n  \"destroy\": \"Excluir\",\n  \"deleteWindowTitle\": \"Excluir evento\",\n  \"ariaSlotLabel\": \"Selecionar de {0:t} à {1:t}\",\n  \"ariaEventLabel\": \"{0} em {1:D} até {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Tem certeza que deseja excluir este evento?\"\n  },\n  \"views\": {\n    \"day\": \"Dia\",\n    \"week\": \"Semana\",\n    \"workWeek\": \"Semana de trabalho\",\n    \"agenda\": \"Agenda\",\n    \"month\": \"Mês\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Excluir Item Recorrente\",\n    \"deleteWindowOccurrence\": \"Excluir ocorrência atual\",\n    \"deleteWindowSeries\": \"Excluir série\",\n    \"editWindowTitle\": \"Editar item recorrente\",\n    \"editWindowOccurrence\": \"Editar ocorrência atual\",\n    \"editWindowSeries\": \"Editar série\",\n    \"deleteRecurring\": \"Você deseja excluir apenas este evento ou todas as ocorrências?\",\n    \"editRecurring\": \"Você quer editar apenas este evento ou a série inteira?\"\n  },\n  \"editor\": {\n    \"title\": \"Título\",\n    \"start\": \"Início\",\n    \"end\": \"Fim\",\n    \"allDayEvent\": \"Evento de dia inteiro\",\n    \"description\": \"Descrição\",\n    \"repeat\": \"Repetir\",\n    \"timezone\": \"\",\n    \"startTimezone\": \"Fuso-horário inicial\",\n    \"endTimezone\": \"Fuso-horário final\",\n    \"separateTimezones\": \"Usar fuso-horário diferente para o início e fim\",\n    \"timezoneEditorTitle\": \"Fusos-horários\",\n    \"timezoneEditorButton\": \"Fuso horário\",\n    \"timezoneTitle\": \"Fuso-horários\",\n    \"noTimezone\": \"Sem fuso-horário\",\n    \"editorTitle\": \"Evento\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Todas as bordas\",\n  \"insideBorders\": \"Dentro das bordas\",\n  \"insideHorizontalBorders\": \"Dentro das bordas horizontais\",\n  \"insideVerticalBorders\": \"Dentro das bordas verticais\",\n  \"outsideBorders\": \"Fora das bordas\",\n  \"leftBorder\": \"Borda esquerda\",\n  \"topBorder\": \"Borda superior\",\n  \"rightBorder\": \"Borda direta\",\n  \"bottomBorder\": \"Borda inferior\",\n  \"noBorders\": \"Sem bordas\",\n  \"reset\": \"Redefinir cor\",\n  \"customColor\": \"Cor personalizada...\",\n  \"apply\": \"Aplicar\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Aplicar\",\n  \"save\": \"Salvar\",\n  \"cancel\": \"Cancelar\",\n  \"remove\": \"Excluir\",\n  \"retry\": \"Tentar novamente\",\n  \"revert\": \"Reverter\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Formatar\",\n    \"categories\": {\n      \"number\": \"Número\",\n      \"currency\": \"Moeda\",\n      \"date\": \"Data\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Fonte\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Tamanho da fonte\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Bordas\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Alinhamento\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Alinhar à esquerda\",\n     \"justifyCenter\": \"Centralizar\",\n     \"justifyRight\": \"Alinhar à direita\",\n     \"justifyFull\": \"Justificar\",\n     \"alignTop\": \"Alinhar no topo\",\n     \"alignMiddle\": \"Alinhar no meio\",\n     \"alignBottom\": \"Alinhar abaixo\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Mesclar céludas\",\n    \"buttons\": {\n      \"mergeCells\": \"Mesclar tudo\",\n      \"mergeHorizontally\": \"Mesclar horizontalmente\",\n      \"mergeVertically\": \"Mesclar verticalmente\",\n      \"unmerge\": \"Desmesclar\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Travar painéis\",\n    \"buttons\": {\n      \"freezePanes\": \"Travar painéis\",\n      \"freezeRows\": \"Travar linhas\",\n      \"freezeColumns\": \"Travar colunas\",\n      \"unfreeze\": \"Destravar painéis\"\n    }\n  },\n  \"validationDialog\": {\n    \"title\": \"Data de validade\",\n    \"hintMessage\": \"Por favor entre com um {0} valor válido {1}.\",\n    \"hintTitle\": \"Validação {0}\",\n    \"criteria\": {\n      \"any\": \"Qualquer valor\",\n      \"number\": \"Número\",\n      \"text\": \"Texto\",\n      \"date\": \"Data\",\n      \"custom\": \"Fórmula personalizada\",\n      \"list\": \"Lista\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"maior que\",\n      \"lessThan\": \"menor que\",\n      \"between\": \"entre\",\n      \"notBetween\": \"não está entre\",\n      \"equalTo\": \"igual\",\n      \"notEqualTo\": \"não é igual\",\n      \"greaterThanOrEqualTo\": \"maior ou igual que\",\n      \"lessThanOrEqualTo\": \"menor ou igual que\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"maior que {0}\",\n      \"lessThan\": \"menor que {0}\",\n      \"between\": \"entre {0} e {1}\",\n      \"notBetween\": \"não está entre {0} e {1}\",\n      \"equalTo\": \"igual à {0}\",\n      \"notEqualTo\": \"não é igual a {0}\",\n      \"greaterThanOrEqualTo\": \"maior ou igual à {0}\",\n      \"lessThanOrEqualTo\": \"menor ou igual à {0}\",\n      \"custom\": \"Isso satisfaz a fórmula: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Critério\",\n      \"comparer\": \"Comparar\",\n      \"min\": \"Min\",\n      \"max\": \"Max\",\n      \"value\": \"Valor\",\n      \"start\": \"Inicio\",\n      \"end\": \"Fim\",\n      \"onInvalidData\": \"Em dados inválidos\",\n      \"rejectInput\": \"Rejeitar entrada\",\n      \"showWarning\": \"Mostrar aviso\",\n      \"showHint\": \"Mostrar dica\",\n      \"hintTitle\": \"Título da dica\",\n      \"hintMessage\": \"Mensagem de dica\",\n      \"ignoreBlank\": \"Ignorar em branco\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Escrea o título\",\n      \"typeMessage\": \"Escreva a mensagem\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Exportar...\",\n    \"labels\": {\n      \"fileName\": \"Nome do arquivo\",\n      \"saveAsType\": \"Salvar como tipo\",\n      \"exportArea\": \"Exportar\",\n      \"paperSize\": \"Tamanho do papel\",\n      \"margins\": \"Margens\",\n      \"orientation\": \"Orientação\",\n      \"print\": \"Imprimir\",\n      \"guidelines\": \"Diretrizes\",\n      \"center\": \"Centralizar\",\n      \"horizontally\": \"Horizontalmente\",\n      \"vertically\": \"Verticalmente\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Não é possível alterar parte de uma célula mesclada.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Copiar e colar\",\n    \"errorMessage\": \"Estas ações não podem ser invocadas através do menu. Use os atalhos do teclado ao invés disso:\",\n    \"labels\": {\n      \"forCopy\": \"para copia\",\n      \"forCut\": \"para recortar\",\n      \"forPaste\": \"para colar\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"Essa ação não pode ser realizada na seleção múltipla.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Classificar de A a Z\",\n  \"sortDescending\": \"Classificar de Z a A\",\n  \"filterByValue\": \"Filtrar por valor\",\n  \"filterByCondition\": \"Filtrar pela condição\",\n  \"apply\": \"Aplicar\",\n  \"search\": \"Procurar\",\n  \"addToCurrent\": \"Adicionar à lista\",\n  \"clear\": \"Limpar\",\n  \"blanks\": \"(Vazio)\",\n  \"operatorNone\": \"Nenhum\",\n  \"and\": \"E\",\n  \"or\": \"OU\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Texto contém\",\n      \"doesnotcontain\": \"Texto não contém\",\n      \"startswith\": \"Texto começa com\",\n      \"endswith\": \"Texto termina com\"\n    },\n    \"date\": {\n      \"eq\":  \"É igual a\",\n      \"neq\": \"Não é igual a\",\n      \"lt\":  \"É anterior a\",\n      \"gt\":  \"É posterior a\"\n    },\n    \"number\": {\n      \"eq\": \"É igual à\",\n      \"neq\": \"Não é igual a\",\n      \"gte\": \"É maior ou igual à\",\n      \"gt\": \"É maior que\",\n      \"lte\": \"É menor ou igual à\",\n      \"lt\": \"É menor que\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Adicionar coluna à esquerda\",\n  \"addColumnRight\": \"Adicionar coluna à direita\",\n  \"addRowAbove\": \"Adicionar linha acima\",\n  \"addRowBelow\": \"Adicionar linha abaixo\",\n  \"alignment\": \"Alinhamento\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Alinhar à esquerda\",\n    \"justifyCenter\": \"Centralizar\",\n    \"justifyRight\": \"Alinha à direita\",\n    \"justifyFull\": \"Justificar\",\n    \"alignTop\": \"Alinhar acima\",\n    \"alignMiddle\": \"Alinhar no meio\",\n    \"alignBottom\": \"Alinhar abaixo\"\n  },\n  \"backgroundColor\": \"Fundo\",\n  \"bold\": \"Negrito\",\n  \"borders\": \"Bordas\",\n  \"colorPicker\": {\n    \"reset\": \"Redefinir cor\",\n    \"customColor\": \"Cor personalizada...\"\n  },\n  \"copy\": \"Copiar\",\n  \"cut\": \"Recortar\",\n  \"deleteColumn\": \"Excluir coluna\",\n  \"deleteRow\": \"Excluir linha\",\n  \"excelImport\": \"Importar do Excel...\",\n  \"filter\": \"Filtro\",\n  \"fontFamily\": \"Fonte\",\n  \"fontSize\": \"Tamanho da fonte\",\n  \"format\": \"Formato personalizado...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automático\",\n    \"number\": \"Número\",\n    \"percent\": \"Porcentagem\",\n    \"financial\": \"Financeiro\",\n    \"currency\": \"Moeda\",\n    \"date\": \"Data\",\n    \"time\": \"Hora\",\n    \"dateTime\": \"Data hora\",\n    \"duration\": \"Duração\",\n    \"moreFormats\": \"Mais formatos...\"\n  },\n  \"formatDecreaseDecimal\": \"Diminuir decimal\",\n  \"formatIncreaseDecimal\": \"Aumentar decimal\",\n  \"freeze\": \"Travar painéis\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Travar painéis\",\n    \"freezeRows\": \"Travar linhas\",\n    \"freezeColumns\": \"Travar colunas\",\n    \"unfreeze\": \"Destravar painéis\"\n  },\n  \"italic\": \"Itálico\",\n  \"merge\": \"Mesclar células\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Mesclar tudo\",\n    \"mergeHorizontally\": \"Mesclar horizontalmente\",\n    \"mergeVertically\": \"Mesclar verticalmente\",\n    \"unmerge\": \"Desmesclar\"\n  },\n  \"open\": \"Abrir...\",\n  \"paste\": \"Colar\",\n  \"quickAccess\": {\n    \"redo\": \"Refazer\",\n    \"undo\": \"Desfazer\"\n  },\n  \"saveAs\": \"Salvar como...\",\n  \"sortAsc\": \"Ordenar ascendente\",\n  \"sortDesc\": \"Classificar decrescente\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Classificar folha de A à Z\",\n    \"sortSheetDesc\": \"Classificar folha de Z à A\",\n    \"sortRangeAsc\": \"Ordenar intervalo de A a Z\",\n    \"sortRangeDesc\": \"Ordenar intervalo de Z a A\"\n  },\n  \"textColor\": \"Cor do texto\",\n  \"textWrap\": \"Envolver texto\",\n  \"underline\": \"Sublinhado\",\n  \"validation\": \"Data de validade...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Não é possível inserir células devido à possibilidade de perda de dados. Selecione outro local de inserção ou exclua os dados do final de sua planilha.\",\n    \"filterRangeContainingMerges\": \"Não é possível criar um filtro dentro de um intervalo que contém mesclagens.\",\n    \"validationError\": \"O valor inserido viola as regras de validação definidas na célula.\"\n  },\n  \"tabs\": {\n    \"home\": \"Principal\",\n    \"insert\": \"Inserir\",\n    \"data\": \"Dado\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Aumentar\",\n  \"decreaseButtonTitle\": \"Diminuir\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"Sem resultados\",\n  \"loading\": \"Carregando...\",\n  \"requestFailed\": \"Falha na solicitação.\",\n  \"retry\": \"Tentar novamente\",\n  \"commands\": {\n      \"edit\": \"Editar\",\n      \"update\": \"Atualizar\",\n      \"canceledit\": \"Cancelar\",\n      \"create\": \"Adicionar novo registo\",\n      \"createchild\": \"Adicionar registro filho\",\n      \"destroy\": \"Excluir\",\n      \"excel\": \"Exportar para Excel\",\n      \"pdf\": \"Exportar para PDF\"\n  }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Carregando...\",\n  \"requestFailed\": \"Requisição falhou.\",\n  \"retry\": \"Tentar novamente\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Selecionar...\",\n  \"cancel\": \"Cancelar\",\n  \"retry\": \"Tentar novamente\",\n  \"remove\": \"Remover\",\n  \"uploadSelectedFiles\": \"Enviar arquivos\",\n  \"dropFilesHere\": \"arraste arquivos aqui para enviar\",\n  \"statusUploading\": \"enviando\",\n  \"statusUploaded\": \"enviado\",\n  \"statusWarning\": \"warning\",\n  \"statusFailed\": \"falhou\",\n  \"headerStatusUploading\": \"Carregando...\",\n  \"headerStatusUploaded\": \"Pronto\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} é obrigatório\",\n  \"pattern\": \"{0} não é válido\",\n  \"min\": \"{0} deve ser maior ou igual a {1}\",\n  \"max\": \"{0} deve ser menor ou igual a {1}\",\n  \"step\": \"{0} não é válido\",\n  \"email\": \"{0} não é um email válido\",\n  \"url\": \"{0} não é um endereço web válido\",\n  \"date\": \"{0} não é uma data válida\",\n  \"dateCompare\": \"A data final deve ser posterior à data inicial\"\n});\n}\n\n/* kendo.ui.progress method */\nif (kendo.ui.progress) {\nkendo.ui.progress.messages =\n$.extend(true, kendo.ui.progress.messages, {\n    loading: \"Carregando...\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Fechar\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}